<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="60 44 520 628"><!--oldViewBox="0 0 620 752"--><style>.B{fill:#1e1d1e}.C{fill:#b0afb0}.D{fill:#242324}.E{fill:#aaa9a9}.F{fill:#b6b4b5}.G{fill:#a4a3a4}.H{fill:#282828}.I{fill:#171616}.J{fill:#2f2f2f}.K{fill:#3a3a3a}.L{fill:#161516}.M{fill:#c1c0c1}.N{fill:#9e9d9d}.O{fill:#434243}.P{fill:#343334}.Q{fill:#494949}.R{fill:#e1e0e1}.S{fill:#cac9c9}.T{fill:#969596}.U{fill:#848384}.V{fill:#5d5d5d}.W{fill:#747374}.X{fill:#bdbcbd}.Y{fill:#535253}.Z{fill:#d0cecf}.a{fill:#8b8a8b}.b{fill:#686768}.c{fill:#c6c5c6}.d{fill:#929192}.e{fill:#7c7b7c}.f{fill:#0b0b0b}.g{fill:#191919}.h{fill:#202020}.i{fill:#121212}.j{fill:#4e4d4d}.k{fill:#3f3d3f}.l{fill:#6e6e6e}.m{fill:#626262}.n{fill:#010101}.o{fill:#595859}</style><path d="M219 335h0l1 7h-2 0-1v-2c0-1 0-2 1-3s1-1 1-2z" class="i"></path><path d="M550 520l2 1v3c0 3 0 5-1 7v1h0l-1-12zm-5-17h2l4 11c-1 1-1 2-2 3l-4-14z" class="T"></path><path d="M180 211h2c1 2 2 2 2 4l-1 1c0 2 0 3-1 4h0l-1 1h0 0c-1 1-1 1-2 1h0s1-1 1-2h0c2-2 1-7 0-9z" class="M"></path><path d="M358 601h2v1c2 1 4-1 4 1v1c1 1 2 2 3 2h2l-1 1c0 1 1 1 2 2v2l-12-10z" class="V"></path><path d="M369 601h0l1 1c1 1 1 2 1 3h1c0 1-1 1-2 2h0v1h1l-1 1c-1-1-2-1-2-2l1-1h-2c-1 0-2-1-3-2v-1c2-1 3-1 4-1l-1-1h2z" class="P"></path><path d="M369 601h0v1l-1 1h0v1h2v1h-2l-1 1c-1 0-2-1-3-2v-1c2-1 3-1 4-1l-1-1h2z" class="O"></path><path d="M208 296v4h0 0l-2 2c0 1-1 1-1 2-1 0-1 0-1 1h-1c-1 0-2 2-3 2h0c1-2 1-3 2-4-1 0-2 1-3 1h0-1c1 0 1-1 2-2 0 0 1 0 1-1h-1c2-1 3-2 4-4 1-1 2-1 4-1z" class="i"></path><path d="M192 275l1 1c0 2 1 3 2 5l1 2c1 0 1 1 1 1-1 2 1 5 2 6 0 1-1 1 0 2v3h0l1 1v2h0c1 1 0 1 0 2v1h1c0 1-1 1-1 1l-1-1c0-1 1-1 0-2h-1l-1-1 1-1 1-3c-1-1-1-1-2-3 0 0-1-1-1-2l-1-1h0c0-2-1-3-1-4l-1 1v1h-1l-1-1c1-1 2-2 2-3v-1-2c-1-1-2-2-2-3h0l1-1z" class="e"></path><path d="M350 596c3-1 4-1 7 0 1 0 3 1 5 2h4l1 1v1l2 1h-2l1 1c-1 0-2 0-4 1 0-2-2 0-4-1v-1h-2l-5-3-3-2z" class="K"></path><path d="M353 598s1-1 2 0l12 3 1 1c-1 0-2 0-4 1 0-2-2 0-4-1v-1h-2l-5-3z" class="Y"></path><path d="M346 600l2-1c2 3 6 6 9 8l14 10h-1c-1-1-2-1-2-2h-1 0s-1-1-2-1h0-2c1 1 3 2 4 3 3 1 6 2 8 4l2 2c-9-4-17-10-24-16v1c-1-1-2-2-3-4l-1-1-3-3z" class="Z"></path><path d="M168 257h0c0 1 0 2 1 3h2-2c0 2 0 2 1 3v1l1 1c2 1 3 3 4 5v1c-1 1-2 1-2 1v1c-4-1-5 5-8 6l6-7v-1c-3-1-6 4-8 3 2-1 4-3 6-4 1 0 2-1 2-2l-1-1c-2 0-6 2-8 3-3 1-6 3-9 5h0c3-3 7-4 11-6 2-1 3-2 5-3h1v-1c-2 0-3 1-5 1-3 1-7 2-10 3l-6 1s1 0 1-1h1c3-1 18-4 19-6h-1c-3-1-6-1-9-1h1l7-1h0l-1-2c0-1 1-1 1-2z" class="d"></path><path d="M218 284l3 3c1 2 2 2 2 3v6 12 70 19 9-7-14-54c0-9 0-19-1-28 0-2 1-6 0-8-1 1 0 4-1 6 0 1 0 4-1 6v1-7l1-7v-4c-1-3-2-4-4-6h1z" class="Z"></path><path d="M213 400h0c2-1 3-3 4-5 1 0 2 0 3 1 0 3 0 16-1 19l-1-2-1-1-1 1h0v-1-3-1l-1 1-2-2h0c1 0 1 0 1 1h1c-1-1-1-2-1-2h-1-1l2-4c-1 0-1 1-2 0v-1c0-1 1-1 1-1z" class="i"></path><path d="M214 402v-1c0-1 2-2 2-3h1 0v1 2c-1 1-2 3-3 4v1h-1-1l2-4z" class="D"></path><path d="M220 301v7 7 2c0 2-1 2-1 3h0c-2 2-3 4-4 4h-1l-3 4h-1v-2h0c-1 0 3-7 3-8h1v-1l1-2v-1l3-6c0-1 1-3 0-4 1-2 1-2 2-3z" class="f"></path><path d="M204 329h1c2-1 6-8 8-11h1-1c0 1-4 8-3 8h0v2c-1 0-1 0-1 1s0 1-1 2c-2 1-3 2-3 4l-1 2-1 1c-1 1-1 3-3 4 1 2 0 1 0 2s1 2 1 2c-1 1-2 2-3 2-1-1-1 0-2-1l-1 1h0c0-2 1-3 2-5h0-1c1-1 2-4 1-5h-1 0l3-4v-1c2-1 4-3 5-4z" class="k"></path><path d="M200 342c1 2 0 1 0 2s1 2 1 2c-1 1-2 2-3 2-1-1-1 0-2-1l4-5z" class="a"></path><path d="M209 393h1s0-1 1-1l4-7c0-1 2-2 2-3 1-2 0-2 0-4h1c1 1 1 2 2 3v10 5c-1-1-2-1-3-1-1 2-2 4-4 5h0s-1 0-1 1l-1 1h-1c1-1 1-2 1-3s1-2 1-3h-1l-1 1c-1 0 0-1 0-2l-1-1v-1z" class="n"></path><path d="M220 391v5c-1-1-2-1-3-1-1 2-2 4-4 5h0c1-2 2-3 3-5 1-1 2-3 4-4zm-41-122h3c3 0 4-1 6 0v1c1 1 3 3 4 5l-1 1h0c0 1 1 2 2 3v2c-1 0-2 0-2-1l-2 1c-2 0-3-1-5-2-1 0-3-1-4-1-1-1-2-2-3-2l-2 2h0c0-1 1-2 1-3h-1c-1 0-2 2-3 3 1-2 2-3 2-4h1c0-1 2-1 2-2 2 0 2 0 2-2 1 0 2 1 4 0l-1-1h-3z" class="B"></path><path d="M181 277l2-1c0 1 1 1 1 2v1c-1 0-3-1-4-1l1-1z" class="l"></path><path d="M187 276c1 0 2 1 2 2v1 1h2l-2 1c-2 0-3-1-5-2v-1h3v-2h0z" class="V"></path><path d="M187 274l3 1 1 1h0c0 1 1 2 2 3v2c-1 0-2 0-2-1h-2v-1-1c0-1-1-2-2-2v-2z" class="I"></path><path d="M185 271v-1h3c1 1 3 3 4 5l-1 1-1-1-3-1v-1c-1 0-1-1-2-1l-1-1h0 1z" class="U"></path><path d="M185 272l-1-1h0 1c2 0 3 1 4 3h1v1l-3-1v-1c-1 0-1-1-2-1z" class="H"></path><path d="M179 269h3c3 0 4-1 6 0v1h-3v1h-1 0l1 1s1 1 1 2h-1c0-1-1-1-2-1l1 1v1c-1-1-2-2-2-3-1 0-1-1-2-1 0 0-1 1-1 2l-1 1 1 1h0c1-1 1-1 2-1h0c-1 2 0 2 0 3l-1 1c-1-1-2-2-3-2l-2 2h0c0-1 1-2 1-3h-1c-1 0-2 2-3 3 1-2 2-3 2-4h1c0-1 2-1 2-2 2 0 2 0 2-2 1 0 2 1 4 0l-1-1h-3z" class="W"></path><path d="M206 348c1-1 3-4 3-4 1-2 3-5 4-5-1 2-2 3-1 5h0l-1 1-1 1c-1 1-1 2-2 2-1 2-3 4-4 5l1 1c1-1 4-3 4-5 1-1 2-1 2-2s1-1 1-1c1-1 1-1 1-2h0l1 1c0 1 0 1-1 2-2 3-5 7-6 10-1 1-1 1-1 2v1l-2 2v1c-1 0-2 1-2 1-2 1-3 3-5 4l2-4c-1 0-1 0-2 1h0 0c0-3 2-5 4-8-1 0-2 2-3 2l-3 3c3-6 6-10 11-14z" class="H"></path><path d="M193 276v-2h0 2l1 1h0c2 1 3 3 4 5l1 1c4 5 5 10 7 15-2 0-3 0-4 1-1 2-2 3-4 4v-1c0-1 1-1 0-2h0v-2l-1-1h0v-3c-1-1 0-1 0-2-1-1-3-4-2-6 0 0 0-1-1-1l-1-2c-1-2-2-3-2-5z" class="n"></path><path d="M195 281c1 0 2 0 3 1 2 0 2 0 3 2h0v2 1c0 2 0 4-1 6 0 0 0 1-1 2v-3c-1-1 0-1 0-2-1-1-3-4-2-6 0 0 0-1-1-1l-1-2z" class="L"></path><path d="M326 391v1 11 20 10c1 7 0 15 0 22 0 2 0 6 1 8v5 16l-1 1h1l-1 1v4 9 39l1 1c0 1-1 9 0 10v4 3c0 1 1 1 1 2-1 0 0 1 0 2v1 1s1 2 1 3v1 2h0c1 1 1 2 1 3v1l1 1v2c2 3 3 8 6 11 0 1 1 1 2 2 0 1 0 2 1 2h0v-1l1 1 2 2 1 1c1 3 2 5 4 6l-2 1c-1-1-2-2-2-3-7-8-13-18-15-29-4-16-4-35-4-52l1-56-1-45c0-8 0-16 1-24z" class="R"></path><path d="M218 304c1 1 0 3 0 4l-3 6v1l-1 2v1h-1c-2 3-6 10-8 11h-1c-1 1-3 3-5 4v1h-1c-1 1-2 1-3 2l4-5h-1-2c1-2 3-4 4-5h0c-1 0-3 0-4 1 1-1 2-3 3-3s1-1 1-1c1 0 1 0 2-1h0c2-1 3-3 4-4v-2c2-3 4-5 5-7 0 2 0 3-1 5 1-1 1-2 2-2 2-2 4-6 6-8z" class="Q"></path><path d="M200 326c1 0 2-1 3-1 1-1 2-4 4-4v1c-2 1-3 4-5 5l-3 3-1 1h-2c1-2 3-4 4-5z" class="H"></path><path d="M218 304c1 1 0 3 0 4l-3 6v1l-1 2v1h-1c-2 3-6 10-8 11h-1c1-1 1-2 2-3s1-1 1-2l3-3c1-1 4-6 4-8h-1c-1 2-2 5-4 8l-1-1c1-1 3-3 3-5l-2 1v-1c-3 3-4 7-8 9 0 1-1 1-1 2-1 0-3 0-4 1 1-1 2-3 3-3s1-1 1-1c1 0 1 0 2-1h0c2-1 3-3 4-4v-2c2-3 4-5 5-7 0 2 0 3-1 5 1-1 1-2 2-2 2-2 4-6 6-8z" class="h"></path><path d="M187 255h1c0 1-1 2 0 3h1l1 2-2 1v1l-1 1h2v1h4c-2 0-5 0-7 1h-3l1 1h4c1 2 2 3 3 3h1c9 7 16 17 17 28 0 1 1 4 1 5-1 1-2 3-3 4l-4 5-2-1s-2 1-3 1h0l3-3c1-1 2-2 2-3h1c0-1 0-1 1-1 0-1 1-1 1-2l2-2h0 0v-4c-2-5-3-10-7-15l-1-1c-1-2-2-4-4-5h0l-1-1h-2 0v2l-1-1c-1-2-3-4-4-5v-1c-2-1-3 0-6 0h-3c-2 0-2-1-4-2v-1l4-4s1-1 1-2c1-1 2-2 3-4 2 0 2 0 4-1z" class="U"></path><path d="M188 269c2 1 6 3 7 5h-2 0v2l-1-1c-1-2-3-4-4-5v-1z" class="L"></path><path d="M203 305h1c0-1 0-1 1-1 0-1 1-1 1-2l2-2h0c0 3-2 7-5 9 0 1-1 1-2 1h-1l1-2c1-1 2-2 2-3z" class="n"></path><path d="M187 255h1c0 1-1 2 0 3h1l1 2-2 1v1l-1 1h2v1h-3-2-1v-1-2c-1 1-1 1-1 2l-1 1v1h1v1c-1 1-2 1-3 1-1-1-1-1-2-1 0 0-1 1-2 1v-1l4-4s1-1 1-2c1-1 2-2 3-4 2 0 2 0 4-1z" class="T"></path><path d="M184 260v-1h1c1 1 1 0 2 0 1 1 1 1 1 2v1l-1 1h2v1h-3-2c1 0 2 0 2-1 0-2 0-2-1-3h-1z" class="a"></path><path d="M187 255h1c0 1-1 2 0 3h1l1 2-2 1c0-1 0-1-1-2-1 0-1 1-2 0h-1v1h-2c0 1 0 1-1 1-1 1-1 2-1 3h-1v-1-1s1-1 1-2c1-1 2-2 3-4 2 0 2 0 4-1z" class="e"></path><path d="M184 215l2-1h1c0 1-1 1 0 2v1l2 1-1 2-1 2c-2 2-5 4-6 7-1 1-2 2-2 3h0l-2 3-1 3c-1 1-1 3-1 4v1l-1 2-2 4c-1 3-3 6-4 8h0l1-6v-3h0c0-1 0-1 1-2 0-1-1-7-1-8-1 0-2 0-2-1l1-1h1v-2c0-1 1-1 1-1 0-2 1-3 3-5l3-3v-1h1l2-2h0c1 0 1 0 2-1h0 0l1-1h0c1-1 1-2 1-4l1-1z" class="Q"></path><path d="M171 244v-5h0c1 0 2 0 2 1 1 2 1 3 1 4v1h0c-1 0-1 1-2 1 0-1 0-1-1-2z" class="X"></path><path d="M169 251c2-2 1-3 2-5h-1v-3c1 0 1 1 1 1 1 1 1 1 1 2 1 0 1-1 2-1h0l-2 4c-1 3-3 6-4 8h0l1-6z" class="n"></path><path d="M187 217l2 1-1 2-1 2c-2 2-5 4-6 7-1 1-2 2-2 3h0l-2 3-1 3-2-1-1-1 1-4 5-5c1-1 1-2 2-2h1c0-2 1-3 3-4v-1c0-2 1-2 2-3z" class="I"></path><path d="M174 237v-2c1-2 3-3 5-4v1l-2 3-1 3-2-1z" class="f"></path><path d="M476 472c3-5 7-10 12-12 13-7 28-6 41-2h-3c-1-1-2 0-4 0l-1 1h0v1l-2 1h-7c-1 0-2-1-3-1-4 0-7 0-11 1-1 0-3 1-4 1l-3 1v1c-4 2-7 4-9 7-1 0-2 1-3 2h-1l-2 3h0c0 1-1 1-1 2h-2l3-6z" class="U"></path><path d="M484 465c0-1 1-1 2-2s3-2 5-2l1 1c-1 0-1 1-2 1-2 0-4 0-6 2h0z" class="l"></path><path d="M491 461c5-2 10-3 15-4h7 6l3 1-1 1c-9-2-20-1-29 3l-1-1z" class="V"></path><path d="M484 465h0c2-2 4-2 6-2h1v1c-4 2-7 4-9 7-1 0-2 1-3 2h-1l-2 3h0c0 1-1 1-1 2h-2l3-6c1 0 2-2 3-2 1-1 1-1 2-1 1-1 2-3 3-4z" class="e"></path><path d="M476 472c1 0 2-2 3-2 1-1 1-1 2-1-1 1-5 5-5 7 0 1-1 1-1 2h-2l3-6z" class="d"></path><path d="M492 462c9-4 20-5 29-3h0v1l-2 1h-7c-1 0-2-1-3-1-4 0-7 0-11 1-1 0-3 1-4 1l-3 1h-1c1 0 1-1 2-1z" class="E"></path><path d="M219 320h0c0-1 1-1 1-3l-1 18h0c0 1 0 1-1 2s-1 2-1 3c-2 1-2 3-4 4h0c0 1 0 1-1 2 0 0-1 0-1 1s-1 1-2 2c0 2-3 4-4 5l-1-1c1-1 3-3 4-5 1 0 1-1 2-2l1-1 1-1h0c-1-2 0-3 1-5-1 0-3 3-4 5 0 0-2 3-3 4-2 0-2 1-3 2-2 2-6 6-8 7 0-2 1-3 2-4l-1-2-2 1h0l4-4c1 0 2-1 3-2 0 0-1-1-1-2s1 0 0-2c2-1 2-3 3-4l1-1 1-2c0-2 1-3 3-4 1-1 1-1 1-2s0-1 1-1h1l3-4h1c1 0 2-2 4-4z" class="K"></path><path d="M196 351c2-2 5-4 8-6 2-3 4-6 7-8-3 5-7 10-11 14-1 1-2 2-3 2l-1-2z" class="C"></path><path d="M219 320h0c0-1 1-1 1-3l-1 18h0-1l-1 1h-1c0-1 0-2 1-3v-1h-1l-1 3h-1l2-3c1-2 2-4 2-6l1-2v-1-3z" class="g"></path><path d="M219 320v3 1c-3 0-4 1-5 3 0 1 0 2-1 3v1l-1 1h0c-1 1-1 2-1 2-2 4-6 9-10 12 0 0-1-1-1-2s1 0 0-2c2-1 2-3 3-4l1-1 1-2c0-2 1-3 3-4 1-1 1-1 1-2s0-1 1-1h1l3-4h1c1 0 2-2 4-4z" class="B"></path><path d="M205 335c2-2 4-4 5-6h1c0 2-1 3-2 4s-1 3-2 4c0 1-2 2-1 3h-1v-1l-2-1 1-1 1-2z" class="V"></path><path d="M206 340c3-4 4-8 7-12l-1 4c-1 1-1 2-1 2-2 4-6 9-10 12 0 0-1-1-1-2s1 0 0-2c2-1 2-3 3-4l2 1v1h1zm11 0v2h1 0 2v8 17c-1-5 0-10-1-15v1 1c-1 2-1 4-2 6-1 1-2 1-2 2-2 4-5 8-8 11-1 1-2 2-3 4h1c0 2-2 2-2 3v-3l-2 2c0-2 2-5 2-6l-3 3h0c0-1 1-2 0-2h-1v-1l-1-1c1-1 1-3 1-4 1-2 2-3 3-4 0 0 1-1 2-1v-1l2-2v-1c0-1 0-1 1-2 1-3 4-7 6-10 1-1 1-1 1-2l-1-1c2-1 2-3 4-4z" class="W"></path><path d="M217 340v2h1 0v3c-1 2-2 6-4 7 0-1 1-2 1-3 1-1 1-2 2-3-2 2-3 4-5 6-1 2-1 4-3 5 0-1 2-4 3-6-2 3-5 5-5 9-1 2-3 4-5 5-1 1-1 2-2 3h-1c1-2 2-3 3-4 0 0 1-1 2-1v-1l2-2v-1c0-1 0-1 1-2 1-3 4-7 6-10 1-1 1-1 1-2l-1-1c2-1 2-3 4-4z" class="B"></path><path d="M216 351c2-2 2-5 3-8 1 3 0 6 0 9v1h-1c0 1 0 3-1 4-1 3-3 5-5 7-1 2-2 3-3 5v-1c1-1 2-3 2-5-2 3-3 5-6 7h0 0v-1c2-1 3-4 4-6 2-2 3-5 5-8 1-1 2-2 2-4z" class="g"></path><path d="M214 355c1-1 2-2 2-4 1 1 1 2 1 3-1 1-2 1-3 1z" class="h"></path><path d="M525 471c1 0 3 1 4 2 3 1 7 4 10 7 1 1 3 2 4 3v2c1 0 1 0 2 1l3 3-1 2c1 1 2 2 2 3h-1c0 2 0 2 1 3h0c-1-1-1-1-1-2l-1-1v2l3 8-1-1v1c-1 0-2-3-3-4h0l1 3h-2l-3-6c-6-10-16-17-27-20h3 0c1-1 1-2 1-3h0c0-1 0-1 1-2h1v-1c2 0 3 1 4 1v-1z" class="O"></path><path d="M544 490l-3-3-1 1c-1-1-2-3-4-4-2-2-5-4-7-5l-1-1h2v-1l4 1 5 4 1 1-1 1c1 3 4 4 5 6z" class="Q"></path><path d="M534 478c-3-2-5-3-7-4-1 0-1-1-1-2 1 1 1 1 2 1h1c3 1 7 4 10 7 1 1 3 2 4 3v2c1 0 1 0 2 1l3 3-1 2c1 1 2 2 2 3h-1c0 2 0 2 1 3h0c-1-1-1-1-1-2l-1-1v2c-1-1-1-2-2-3v-1c0-1-1-1-1-2-1-2-4-3-5-6l1-1-1-1-5-4z" class="m"></path><path d="M539 482l1 1v2h0c1 0 2 1 3 1 2 1 3 4 4 5s2 2 2 3h-1c0 2 0 2 1 3h0c-1-1-1-1-1-2l-1-1v2c-1-1-1-2-2-3v-1c0-1-1-1-1-2-1-2-4-3-5-6l1-1-1-1z" class="V"></path><path d="M219 353v-1c1 5 0 10 1 15v14c-1-1-1-2-2-3h-1c0 2 1 2 0 4 0 1-2 2-2 3l-4 7c-1 0-1 1-1 1h-1 0l-2 2v-1c0-1 0-2 1-3-1-1-1-1-2-1h-2c-1-1-1-3-2-4 1-2 1-3 2-5h0 0-1-1l1-1c0-1 2-1 2-3h-1c1-2 2-3 3-4 3-3 6-7 8-11 0-1 1-1 2-2 1-2 1-4 2-6v-1z" class="B"></path><path d="M214 376l1-1h0c0 2-4 6-3 7 2-1 2-2 3-4h1c0 1-1 1-1 2-2 2-4 5-5 8-1 0-1 0-1-1h-1c0-3 4-7 5-10l1-1z" class="I"></path><path d="M204 381c2-3 4-5 5-8 1-1 2-2 2-3 1-1 2-3 3-4 0 0 0-1 1-2v1c0 1 0 2-1 2v1c-1 1-1 2-2 3 0 1-1 1-1 2v1l-3 7c-1 1-2 2-3 4v-1l-1-3h0 0z" class="K"></path><path d="M211 374c1-1 2-3 3-4s1-3 2-4c0 3-2 5-3 8l1 2-1 1c-1 3-5 7-5 10h1c0 1 0 1 1 1l-1 1h-1v2c-1-1-1-1-2-1h-2c-1-1-1-3-2-4 1-2 1-3 2-5l1 3v1c1-2 2-3 3-4l3-7z" class="e"></path><path d="M209 381l4-7 1 2-1 1c-2 1-2 3-4 4z" class="H"></path><path d="M209 381c2-1 2-3 4-4-1 3-5 7-5 10h1l-1 1h-1v-2l-1 1v-1c1-2 2-4 3-5z" class="P"></path><path d="M118 631l1 1v14l289 1h0c-3-4-5-8-5-12h1c1 5 2 9 5 13h-9-17-50-80l-89-1h-46c-1-3 0-13 0-16z" class="U"></path><path d="M326 294v70 19c0 3-1 6 0 8-1 8-1 16-1 24l1 45-1 56c0 17 0 36 4 52 2 11 8 21 15 29 0 1 1 2 2 3l3 3 1 1-1 1c-2-3-5-6-7-9-8-9-13-20-16-31-3-17-2-35-2-51v-63-107c1-14 0-28 1-42 1-1 0-6 1-8z" class="N"></path><path d="M522 458c2 0 3-1 4 0h3c6 2 13 5 18 10l5 4h-3l-2-2h0-1 0v1h-1c0-1-1-1-2-2h0-1s0-1-1-1c0 1 1 2 2 2h0v1h0v1c3 1 4 2 6 3l3 3c-1 0 0 1-1 1h0l1 1h-2l-1 1 1 1h0l1 2c2 2 4 4 6 7v1h-1c-2 0-2-1-3-2h0v1 3l-2-5-1 2h0c0 1 1 2 0 3h0-1c0-1-1-2-2-3l1-2-3-3c-1-1-1-1-2-1v-2c-1-1-3-2-4-3-3-3-7-6-10-7-1-1-3-2-4-2l1-2c-1-1-3-2-4-3-2 0-4-1-7-1v-1h3v-1c-2 0-4-2-6-2h7l2-1v-1h0l1-1z" class="U"></path><path d="M543 479c-2 0-2-2-4-3-1-1-6-4-7-5 1 0 3 1 4 2 2 2 5 3 7 5v1z" class="e"></path><path d="M521 459c2 0 4 1 6 1-2 2-5 2-7 2 0 0-1 0-1-1l2-1v-1z" class="T"></path><path d="M541 471l2 1c3 1 4 2 6 3l3 3c-1 0 0 1-1 1h0l1 1h-2l-1 1-2-1-4-4h0l5 2c-2-2-4-3-5-5l-2-2z" class="V"></path><path d="M528 460c1 1 1 1 2 1h1c0 1 0 1 1 2s2 2 3 2l1 1c-1 0-4-1-5-2l10 7 2 2c-4 0-7-4-10-5l-8-5c1 0 3 1 5 1v-1c-1-1-2-1-2-3z" class="W"></path><path d="M543 478c1 1 2 2 4 2h0l2 1 1 1h0l1 2c2 2 4 4 6 7v1h-1c-2 0-2-1-3-2h0v1 3l-2-5-3-3c0-1-2-2-2-3h-1v-1h1v-1c-1-1-2-2-3-2v-1z" class="b"></path><path d="M548 486h1c1 1 4 3 4 4h0v1 3l-2-5-3-3z" class="m"></path><path d="M526 469c5 3 11 6 15 11l1 1 1-1c1 1 2 1 2 2v1h1c0 1 2 2 2 3l3 3-1 2h0c0 1 1 2 0 3h0-1c0-1-1-2-2-3l1-2-3-3c-1-1-1-1-2-1v-2c-1-1-3-2-4-3-3-3-7-6-10-7-1-1-3-2-4-2l1-2z" class="W"></path><path d="M522 458c2 0 3-1 4 0h3c6 2 13 5 18 10l5 4h-3l-2-2h0-1 0v1h-1c0-1-1-1-2-2h0-1s0-1-1-1c0 1 1 2 2 2h0v1h0v1l-2-1-10-7c1 1 4 2 5 2l-1-1c-1 0-2-1-3-2s-1-1-1-2h-1c-1 0-1 0-2-1h-1c-2 0-4-1-6-1h0l1-1z" class="b"></path><path d="M535 465v-1l9 5c0-2-4-3-5-4s-3-1-4-2l1-1c3 2 7 6 10 6h1 0l5 4h-3l-2-2h0-1 0v1h-1c0-1-1-1-2-2h0-1s0-1-1-1c0 1 1 2 2 2h0v1h0v1l-2-1-10-7c1 1 4 2 5 2l-1-1z" class="Q"></path><path d="M177 235l2 2v2c1 1 2 2 2 3 1 0 2 1 2 2v4c1-1 1-3 2-5l-1-1 2-1 1 1 1-1v1c1-1 1-1 1-2v-1c1 0 1-1 2-1 2-1 5-1 8-1 1 0 3 1 4 1l4 1v2h1 0c-2 3-4 6-7 7-1 0-1 1-2 1l-1 1h-1v1c0 1-2 1-3 1h-2v1h-2c-1 1-2 1-3 2-2 1-2 1-4 1-1 2-2 3-3 4 0 1-1 2-1 2l-4 4v1c2 1 2 2 4 2h3l1 1c-2 1-3 0-4 0 0 2 0 2-2 2 0 1-2 1-2 2h-1l-1-1v-1s1 0 2-1v-1c-1-2-2-4-4-5l-1-1v-1c-1-1-1-1-1-3h2-2c-1-1-1-2-1-3 1-2 3-5 4-8l2-4 1-2v-1c0-1 0-3 1-4l1-3z" class="I"></path><path d="M186 252h2c0 1 1 1 2 1-1 1-2 1-3 2-2 1-2 1-4 1 1-2 2-3 3-4zm-3-4c0 1 1 1 1 2-1 2-3 4-4 7-1 1-1 2-2 3v-2c1-2 1-4 2-5h1c0-1 0-2 1-3l1-2z" class="Q"></path><path d="M186 241l1 1 1-1v1c0 1-1 2-1 3l-3 5c0-1-1-1-1-2h0c1-1 1-3 2-5l-1-1 2-1z" class="O"></path><path d="M193 241c0-1 1-1 1-1 1-1 2-1 3 0v2c0 2 0 3-1 4h-1-1c-1-1-1-4-1-5z" class="G"></path><path d="M197 242l1 2c1-1 0-5 3-5 1 0 1 1 2 1 0 1 0 2-1 3-1 2-3 3-5 4 0 0-1 0-2-1h1c1-1 1-2 1-4z" class="d"></path><path d="M207 241h1 0c-2 3-4 6-7 7-1 0-1 1-2 1l-1 1h-1v1c0 1-2 1-3 1h-2v1h-2c-1 0-2 0-2-1h-2 0v-1c1-1 1-1 3-1h1 1s-1-2-2-2c0-1 1-2 1-3 1-1 2-3 3-4 0 1 0 4 1 5h1c1 1 2 1 2 1 2-1 4-2 5-4v1h1 1l3-3z" class="i"></path><path d="M189 250c1 1 2 2 3 2v-1c0-1-1-1-1-2v-2h0 1v2c1 0 1 1 2 1s1 1 1 0h2v1c0 1-2 1-3 1h-2v1h-2c-1 0-2 0-2-1h-2 0v-1c1-1 1-1 3-1h0z" class="K"></path><path d="M186 252v-1c1-1 1-1 3-1h0c0 1 0 2-1 2h0c-1 0-1-1-2 0z" class="h"></path><path d="M207 241h1 0c-2 3-4 6-7 7-1 0-1 1-2 1l-1 1h-1-2c0 1 0 0-1 0s-1-1-2-1v-2c1 1 3 1 4 1 4 0 6-2 8-4l3-3z" class="G"></path><path d="M177 235l2 2v2c1 1 2 2 2 3 1 0 2 1 2 2v4h0l-1 2c-1 1-1 2-1 3h-1c-1 1-1 3-2 5v2h-1 0-4 0 2v1c0 2-1 3-2 5-1-1-3-3-3-5l1-1h0-2c-1-1-1-2-1-3 1-2 3-5 4-8l2-4 1-2v-1c0-1 0-3 1-4l1-3z" class="F"></path><path d="M172 249l3-1v1c1 2 1 3 2 5l-2 5c-2 0-3 0-4 1h-2c-1-1-1-2-1-3 1-2 3-5 4-8z" class="R"></path><path d="M177 235l2 2v2c1 1 2 2 2 3 1 0 2 1 2 2v4h0l-1 2c-1 1-1 2-1 3h-1c-1 1-1 3-2 5v2h-1 0c0-2 0-4 2-6v-1c0-2-1-4-1-6 0 0-1 0 0-1v-3c-2-1-2 0-3 0v-1c0-1 0-3 1-4l1-3z" class="N"></path><path d="M177 235l2 2v2 1c-1 0-2 0-3 1l-1 1c0-1 0-3 1-4l1-3z" class="a"></path><path d="M179 239c1 1 2 2 2 3 1 0 2 1 2 2v4h0l-1 2c-1 1-1 2-1 3h-1v-2c1-2 1-3 2-4-1-1-1-2-2-3v1h-1v-5-1z" class="W"></path><defs><linearGradient id="A" x1="392.823" y1="356.031" x2="384.705" y2="367.166" xlink:href="#B"><stop offset="0" stop-color="#2b2a29"></stop><stop offset="1" stop-color="#4c4b4d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M369 336c-2-4-7-7-9-10l1-1c3 3 7 6 10 9 3 4 6 7 9 11 0 0 2 2 2 3l19 23h-1l-1-1c0 2 0 2 1 3l-1 1v2l1 2c2 4 3 8 4 12v1c-1 0-1 0-2-1h-2 0l-2-1v1c-1-2-2-3-4-4l-3-5s-1-1-1-2l-2-3-1-1c-1-1-1-2-2-3s-1-2-2-3l2-1v-1c0-1-1-2-2-3h0c-1 0-2 0-2 1l-1-2-1-3-1-1c-2-2-3-5-5-7h0c-1-1-1-2-1-3h-1l-1-3v-1l1 1h1c0-1 0-2-1-2v-1c-1-1-1-2-2-3 1 0 2 1 2 2l1-1-4-4 1-1z"></path><path d="M389 365h1c2 3 4 5 4 8v1 1h-1c0 2 0 3-1 4h-1-1l-2-3v-3c0-1 1-1 0-2v-2c1-2 0-3 0-5 1 0 1 1 1 1z" class="f"></path><path d="M390 369h0c1 2 1 3 1 5h-1l2 2h1c0-2 0-2 1-3v1 1h-1c0 2 0 3-1 4h-1c0-1-1-3-2-4h0c0-2 0-2 1-4v-2z" class="D"></path><path d="M388 364c1 0 1 1 1 1 1 1 1 2 1 4v2c-1 2-1 2-1 4h0c1 1 2 3 2 4h-1l-2-3v-3c0-1 1-1 0-2v-2c1-2 0-3 0-5z" class="O"></path><path d="M391 379h1c1-1 1-2 1-4h1v-1l1 1c1 0 1 1 1 1h0l1 3v1h1c0-1-1-1-1-2v-2h2l1 2c2 4 3 8 4 12v1c-1 0-1 0-2-1h-2 0l-2-1v1c-1-2-2-3-4-4l-3-5s-1-1-1-2h1z" class="Y"></path><path d="M396 386c-1-1-2-3-2-3v-2c0-1-1-1-1-2 1-1 1-2 3-3l1 3v2c1 0 1 0 1 1v1 2c0 1 1 1 0 2l-2-1z" class="V"></path><path d="M398 382c-1 1 0 1-1 1l-2-1v-2l2 1c1 0 1 0 1 1z" class="W"></path><path d="M391 379h1c1-1 1-2 1-4h1v-1l1 1c1 0 1 1 1 1h0c-2 1-2 2-3 3 0 1 1 1 1 2v2s1 2 2 3c0 0 1 1 2 1h1l1-1h0c1 2 1 3 2 4h0-2 0l-2-1v1c-1-2-2-3-4-4l-3-5s-1-1-1-2h1z" class="Q"></path><path d="M369 336c-2-4-7-7-9-10l1-1c3 3 7 6 10 9 3 4 6 7 9 11 0 0 2 2 2 3h0-1s0-1-1-1c1 1 1 2 1 3 2 5 7 9 7 14 0 2 1 3 0 5v2c1 1 0 1 0 2v3l-1-1c-1-1-1-2-2-3s-1-2-2-3l2-1v-1c0-1-1-2-2-3h0c-1 0-2 0-2 1l-1-2-1-3-1-1c-2-2-3-5-5-7h0c-1-1-1-2-1-3h-1l-1-3v-1l1 1h1c0-1 0-2-1-2v-1c-1-1-1-2-2-3 1 0 2 1 2 2l1-1-4-4 1-1z" class="O"></path><g class="j"><path d="M380 356c1 1 1 1 1 2 0 2 1 4 2 6-1 0-2 0-2 1l-1-2-1-3v-3h0 1v-1h0z"></path><path d="M383 364c1-1 1-1 1-2s-2-7-3-8c0 1 0 1-1 1v-4c1 1 1 2 2 3 0-1-1-1-1-2 0 0 0-1-1-1l-1-2v-1l2 2c2 5 7 9 7 14 0 2 1 3 0 5v2c1 1 0 1 0 2v3l-1-1c-1-1-1-2-2-3s-1-2-2-3l2-1v-1c0-1-1-2-2-3z"></path></g><path d="M386 366c1-1 1-1 2 0v2l-1-1c-1 0-1 0-1-1z" class="o"></path><defs><linearGradient id="C" x1="374.783" y1="355.228" x2="375.531" y2="342.061" xlink:href="#B"><stop offset="0" stop-color="#4f4f50"></stop><stop offset="1" stop-color="#6b696a"></stop></linearGradient></defs><path fill="url(#C)" d="M369 340c1 0 2 1 2 2l1-1-4-4 1-1c4 5 10 11 11 18v2h0v1h-1 0v3l-1-1c-2-2-3-5-5-7h0c-1-1-1-2-1-3h-1l-1-3v-1l1 1h1c0-1 0-2-1-2v-1c-1-1-1-2-2-3z"></path><path d="M370 345l1 1c1 0 1 1 2 1 1 1 2 2 2 3l-1 1-2-2h0-1l-1-3v-1z" class="Q"></path><path d="M372 349h0l2 2 1-1c1 3 3 6 3 9-2-2-3-5-5-7h0c-1-1-1-2-1-3z" class="P"></path><path d="M209 239h3 3 6c0 2 0 7 1 8v7 7h3 0v2c-2 0-2 1-4 1v3c0 1 0 2-1 2l-8-1c-3 0-8-2-11-3-3 0-5-1-8-1h-4v-1h-2l1-1v-1l2-1-1-2h-1c-1-1 0-2 0-3h-1c1-1 2-1 3-2h2v-1h2c1 0 3 0 3-1v-1h1l1-1c1 0 1-1 2-1 3-1 5-4 7-7h0-1v-2h2z" class="d"></path><path d="M213 260l1-2h1l1 1h1c1 1 2 1 4 1h-8z" class="I"></path><path d="M189 263c2 0 3 0 4-1 1 0 2 1 2 1 2-1 3-1 5-1-1 1-1 1 0 2l1 1c-3 0-5-1-8-1h-4v-1z" class="h"></path><path d="M217 257h0 1l-1 2h-1l-1-1h-1l-1 2-9 1v-1c0-1 1-2 2-2s1 1 2 1v-1c1-1 1 0 2 0l1-1 2 1h0s1 0 1-1c1 1 2 0 3 0z" class="O"></path><path d="M190 260l1-1c1 0 1 1 2 1l1-1c1 0 1 0 1-1h1c1-1 2-1 3 0 1 0 1 1 2 1h1c0-1 1-1 1-1 1-1 2-1 3 0-1 0-2 1-2 2v1h-6-6c-1 1-2 0-4 1v-1l2-1z" class="V"></path><path d="M200 262c7 0 15-2 23-1-2 1-2 2-2 3h0v3c0 1 0 2-1 2l-8-1c-3 0-8-2-11-3l-1-1c-1-1-1-1 0-2z" class="f"></path><path d="M197 250h1c0 1 0 1 1 2l1-1c1 0 1 1 2 1 1-1 2 0 4 0 1-1 1 0 3-1v1 1h-1l1 1v1 1h2l1 1h-1l-1 1c-1 0-1-1-2 0v1c-1 0-1-1-2-1-1-1-2-1-3 0 0 0-1 0-1 1h-1c-1 0-1-1-2-1-1-1-2-1-3 0h-1c0 1 0 1-1 1l-1 1c-1 0-1-1-2-1l-1 1-1-2h-1c-1-1 0-2 0-3h-1c1-1 2-1 3-2h2v-1h2c1 0 3 0 3-1v-1z" class="b"></path><path d="M207 254c0 1 1 1 2 1v1h-7l-1-1h5l1-1z" class="Y"></path><path d="M206 252c1-1 1 0 3-1v1 1h-1l1 1v1c-1 0-2 0-2-1h-3-1l1-1h1l1-1zm-9-2h1c0 1 0 1 1 2l1 1h1 1l-1 1h-1c-1 1 0 1-1 2h-3l2-2h-1c-2 0-4 3-7 3l-1-1h0c-1 0-1 0-1-1h-1c1-1 2-1 3-2h2v-1h2c1 0 3 0 3-1v-1z" class="V"></path><path d="M209 239h3 3 6c0 2 0 7 1 8v7h-1c0 1 1 4 0 6-2 0-3 0-4-1l1-2h-1 0c-1 0-2 1-3 0 0 1-1 1-1 1h0l-2-1h1l-1-1h-2v-1-1l-1-1h1v-1-1c-2 1-2 0-3 1-2 0-3-1-4 0-1 0-1-1-2-1l-1 1c-1-1-1-1-1-2l1-1c1 0 1-1 2-1 3-1 5-4 7-7h0-1v-2h2z" class="j"></path><path d="M209 252l3 1c-1 1-2 1-3 1l-1-1h1v-1zm-11-2l1-1 1 1v1l-1 1c-1-1-1-1-1-2z" class="Q"></path><path d="M214 257s-1-1-1-2c1-1 1-1 2-1h1l-1 1c0 1 1 1 2 2-1 0-2 1-3 0z" class="Y"></path><path d="M207 246l1 1-1 1h1 2s1 0 2 1v1h0v1h0l-1-1c-1-1 0 1-1 0 0-1-1-1-2-1s-1-1-2-1v1h-1l-1-1h1v-2h2z" class="Q"></path><path d="M213 251l1-1-1-1h1 0c1 0 2 0 3 1v2h1s1 0 1 1h1v1c-1 1-1 1-1 3h-1s0-1-1-1v-3c-1 1-1 1-2 1v-1l2-1-1-1h-3z" class="O"></path><path d="M207 246c1 0 1-1 2-1v2h2c3-1 6-1 8 0h1c0 1-1 2-1 3l1 1c-1 1-1 1-1 2 0-1-1-1-1-1h-1v-2c-1-1-2-1-3-1h0-1l1 1-1 1h-1v-1h0v-1c-1-1-2-1-2-1h-2-1l1-1-1-1z" class="K"></path><defs><linearGradient id="D" x1="214.259" y1="238.882" x2="213.033" y2="246.618" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#252526"></stop></linearGradient></defs><path fill="url(#D)" d="M209 239h3 3 6c0 2 0 7 1 8v7h-1c0 1 1 4 0 6-2 0-3 0-4-1l1-2h1c0-2 0-2 1-3v-1h-1c0-1 0-1 1-2l-1-1c0-1 1-2 1-3h-1c-2-1-5-1-8 0h-2v-2c-1 0-1 1-2 1h-2v2h-1 0l-1 2h-1l-1-2c3-1 5-4 7-7h0-1v-2h2z"></path><path d="M220 251l1-5v8c0 1 1 4 0 6-2 0-3 0-4-1l1-2h1c0-2 0-2 1-3v-1h-1c0-1 0-1 1-2z" class="g"></path><defs><linearGradient id="E" x1="466.931" y1="612.715" x2="489.592" y2="604.544" xlink:href="#B"><stop offset="0" stop-color="#444245"></stop><stop offset="1" stop-color="#70706e"></stop></linearGradient></defs><path fill="url(#E)" d="M551 535v3l1 1c1 2 0 5 0 7h0l1 1h1c0 1 0 2-1 3v1c0 2 0 2-1 4 0 1 0 2-1 3l-2 8c0 1-1 2-1 3l1 1v1h-1c-1 6-5 12-9 17-2 3-5 7-8 10-2 2-4 4-6 5-1 0-3 1-3 2-1 2-4 3-6 4-3 1-7 4-10 4-1 0-2 0-3-1h0-3c-1 0-1 0-1-1h0 0l-1-1c-1 1-1 1-2 1h-4c-1 1 0 1-2 1l1 1c-1 1-4 1-6 1h-4l-1 1v1h2c-1 1-3 1-4 1v1c-4-1-6-2-9-4h0l-1-1c-1 0-1-1-1-1-1-1-1 0-1-1v-1s-1-1-1-2l-1-1 1-2c5 3 11 4 16 5 16 1 33-5 45-16 3-4 7-7 10-11 7-10 11-22 13-34l2-14z"></path><path d="M498 610h1c1 0 1-1 2-1h1l8-3v1c-3 2-5 3-8 4h-3 0l-1-1z" class="W"></path><path d="M542 577v1l3-6h1c0 1-1 2-1 3-1 1-2 3-2 4-1 2-2 3-3 4-2 3-4 7-7 10-4 4-9 8-14 10-3 2-6 4-9 4v-1c4-2 9-4 13-7 8-6 13-14 19-22z" class="Q"></path><defs><linearGradient id="F" x1="478.77" y1="614.464" x2="480.226" y2="609.711" xlink:href="#B"><stop offset="0" stop-color="#302e2f"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#F)" d="M465 608c1 1 2 2 4 3v-1-1h0c3 0 5 1 8 2 2 0 5 0 7 1h3 0c1 1 2 0 3 0l1 1c-1 1-4 1-6 1h-4l-1 1v1h2c-1 1-3 1-4 1v1c-4-1-6-2-9-4h0l-1-1c-1 0-1-1-1-1-1-1-1 0-1-1v-1s-1-1-1-2z"></path><path d="M469 614h0c0-1 0-1-1-2h1s1 1 2 1c1-1 1-1 3-1 1 1 3 1 4 2h0l2 2h0 2c-1 1-3 1-4 1v1c-4-1-6-2-9-4z" class="H"></path><path d="M541 583c2-2 3-4 4-7 1-2 2-3 3-5-1 6-5 12-9 17-2 3-5 7-8 10-2 2-4 4-6 5-1 0-3 1-3 2-1 2-4 3-6 4-3 1-7 4-10 4-1 0-2 0-3-1h0-3c-1 0-1 0-1-1h0 3c3-1 5-2 8-4 3 0 6-2 9-4 5-2 10-6 14-10 3-3 5-7 7-10h1z" class="o"></path><path d="M540 583h1c-3 5-7 11-12 15-4 4-9 7-14 9-2 1-3 2-6 3-2 0-5 2-7 1 3-1 5-2 8-4 3 0 6-2 9-4 5-2 10-6 14-10 3-3 5-7 7-10z" class="J"></path><path d="M551 535v3l1 1c1 2 0 5 0 7h0l1 1h1c0 1 0 2-1 3v1c0 2 0 2-1 4 0 1 0 2-1 3l-2 8c0 1-1 2-1 3l1 1v1h-1c-1 2-2 3-3 5-1 3-2 5-4 7h-1c1-1 2-2 3-4 0-1 1-3 2-4 0-1 1-2 1-3h-1l-3 6v-1l1-4h0 0c-2 4-4 7-6 10-1 2-2 3-3 4l-1 1c-1 1-3 2-3 4-1 0-2 1-3 2h-1c3-4 7-7 10-11 7-10 11-22 13-34l2-14z" class="T"></path><defs><linearGradient id="G" x1="551.802" y1="559.346" x2="547.024" y2="556.979" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#575559"></stop></linearGradient></defs><path fill="url(#G)" d="M552 546h0l1 1h1c0 1 0 2-1 3v1c0 2 0 2-1 4 0 1 0 2-1 3l-2 8c0 1-1 2-1 3l1 1v1h-1c-1 2-2 3-3 5-1 3-2 5-4 7h-1c1-1 2-2 3-4 0-1 1-3 2-4 0-1 1-2 1-3h-1l-3 6v-1l1-4h1c4-9 6-18 8-27z"></path><path d="M547 503l-1-3h0c1 1 2 4 3 4v-1l1 1 3 6c0 2 0 3 1 5l1-1c1 1 1 1 1 0h1c1 0 1 1 2 2 0-1 1-2 1-3h0l1 4c0 1 0 2-1 3 1 1 1 2 1 2v1c0 2 1 3 1 5h0c2 3 1 10 1 13h1v5 3l-3 15c-1 2-1 3-2 5 0-1 0-1-1-1 0 1-1 3-1 5-1 0-2 1-3 2-2 0-3 3-4 5l-10 15h-1 0v-1-1 1-2c1-1 3-3 3-4-2 0-3 2-4 4-2 2-4 4-6 7-3 2-6 4-10 6 0-1 2-2 3-2 2-1 4-3 6-5 3-3 6-7 8-10 4-5 8-11 9-17h1v-1l-1-1c0-1 1-2 1-3l2-8c1-1 1-2 1-3 1-2 1-2 1-4v-1c1-1 1-2 1-3h-1l-1-1h0c0-2 1-5 0-7l-1-1v-3-3h0v-1c1-2 1-4 1-7v-3l-2-1-1-3c1-1 1-2 2-3l-4-11z" class="H"></path><path d="M551 514l1 7-2-1-1-3c1-1 1-2 2-3z" class="a"></path><path d="M555 514c1 1 1 1 1 0l1 7-1-1c-1-2-1-4-2-5l1-1z" class="K"></path><path d="M552 524c1 4 1 7 1 10 1 2 1 4 2 6v1c0 1-1 5-1 6h-1l-1-1h0c0-2 1-5 0-7l-1-1v-3-3h0v-1c1-2 1-4 1-7z" class="Y"></path><path d="M562 528c2 3 1 10 1 13h1v5 3l-3 15c-1 2-1 3-2 5 0-1 0-1-1-1 4-10 5-21 5-32h-1c-1-1-1-1-1-3 1-1 1-4 1-5z" class="o"></path><path d="M560 513h0l1 4c0 1 0 2-1 3 1 1 1 2 1 2v1c0 2 1 3 1 5h0c0 1 0 4-1 5 0-2 0-4-2-6 0-2-1-4-2-6l-1-7h1c1 0 1 1 2 2 0-1 1-2 1-3z" class="W"></path><path d="M560 513h0l1 4c0 1 0 2-1 3l-1-4c0-1 1-2 1-3z" class="P"></path><path d="M193 264c3 0 5 1 8 1 3 1 8 3 11 3l8 1c1 0 1-1 1-2 0 1 1 2 0 3l1 1c1 0 2 1 3 1v1h0v16c-1 0-1 1-1 2l-1-1c0-1-1-1-2-3l-3-3h-1c2 2 3 3 4 6v4l-1 7c-1 1-1 1-2 3-2 2-4 6-6 8-1 0-1 1-2 2 1-2 1-3 1-5-1 2-3 4-5 7v2c-1 1-2 3-4 4h0c-1 1-1 1-2 1h-1 0l3-4h-1c0 1-1 1-1 1l4-6-5 3 4-6 4-5c1-1 2-3 3-4 0-1-1-4-1-5-1-11-8-21-17-28h-1c-1 0-2-1-3-3h-4l-1-1h3c2-1 5-1 7-1z" class="n"></path><path d="M221 290v4c-1-2-2-1-2-2s1-1 2-2z" class="f"></path><path d="M212 302c0-2 1-4 2-5 0-1 0-1 1-2v1c0 1 0 2-1 4v2h-1-1z" class="H"></path><path d="M212 302h1 1l-3 7c-1 2-3 4-5 7v2c-1 1-2 3-4 4h0c-1 1-1 1-2 1h-1 0l3-4h-1l1-1c0-1 1-2 2-2l3-3s1-1 1-2v-2c0-1 1-1 1-1l3-6z" class="J"></path><path d="M188 266h3c3 0 6 0 9 1h0-1-2v1 1h3 0 0c0 1 1 1 1 1l1 1c3 2 6 4 8 6 2 1 3 3 5 4 1 0 2 2 3 3h-1l-5-4c-2-2-5-3-7-5-4-2-9-6-13-6h-1c-1 0-2-1-3-3z" class="Y"></path><path d="M191 266c3 0 6 0 9 1h0-1-2v1 1h3 0 0c0 1 1 1 1 1l1 1c-1 0-2-1-3-1h0c0-1-1-1-1-1-1 0-3-1-4-1s-2 0-3-2z" class="k"></path><path d="M193 264c3 0 5 1 8 1 3 1 8 3 11 3l8 1c1 0 1-1 1-2 0 1 1 2 0 3l1 1c1 0 2 1 3 1v1h0v16c-1 0-1 1-1 2l-1-1c0-1-1-1-2-3l-3-3c-1-1-2-3-3-3-2-1-3-3-5-4-2-2-5-4-8-6l-1-1s-1 0-1-1h0 0-3v-1-1h2 1 0c-3-1-6-1-9-1h-3-4l-1-1h3c2-1 5-1 7-1z" class="f"></path><path d="M221 267c0 1 1 2 0 3l1 1c1 0 2 1 3 1v1c-1 0-3 0-3 1h0c0 2 0 2-1 4 0-1 0-2-1-2v-2h1v-3h-2-1c-1-1-3-1-4-1-1-1-2-1-3-1h2 0c0-1 0-1-1-1l8 1c1 0 1-1 1-2z" class="W"></path><path d="M193 264c3 0 5 1 8 1 3 1 8 3 11 3 1 0 1 0 1 1h0-2l-11-2h0c-3-1-6-1-9-1h-3-4l-1-1h3c2-1 5-1 7-1z" class="G"></path><path d="M200 269c8 2 15 5 21 10v-1c1-2 1-2 1-4h0c0-1 2-1 3-1h0v16c-1 0-1 1-1 2l-1-1c0-1-1-1-2-3l-3-3c-1-1-2-3-3-3-2-1-3-3-5-4-2-2-5-4-8-6l-1-1s-1 0-1-1h0z" class="T"></path><path d="M221 278c1-2 1-2 1-4h0c0-1 2-1 3-1h0c0 1 0 1-1 2h-1 0c0 1 0 1 1 1-1 2-1 2-1 4h-1s-1 0-1-1v-1z" class="U"></path><path d="M201 270c5 1 9 4 14 6 1 1 6 4 7 6v1 2h0-1s0 1 1 2h-1l-3-3c-1-1-2-3-3-3-2-1-3-3-5-4-2-2-5-4-8-6l-1-1z" class="f"></path><path d="M191 129c-18 2-38 7-54 16-7 4-13 8-18 13l-15 15c-12 17-20 38-23 58-1 7-1 13-2 20H61v-43-69-33c0-6-1-12 0-17h445c0 7-1 14-1 20l-2 29-8 88-2 23c-1 5-1 11-2 16h-14-7l1-1h2 17l8-82 7-92H62v160h16c1-10 2-19 4-29 2-12 7-23 12-33 2-4 5-8 7-12 5-6 10-12 16-17 5-5 12-10 18-14 4-2 7-3 11-5 14-6 30-10 46-12l-1 1z" class="N"></path><path d="M494 462c1 0 3-1 4-1 4-1 7-1 11-1 1 0 2 1 3 1 2 0 4 2 6 2v1h-3v1c3 0 5 1 7 1 1 1 3 2 4 3l-1 2v1c-1 0-2-1-4-1v1h-1c-1 1-1 1-1 2h0c0 1 0 2-1 3h0-3c-7-2-16-2-23 2-3 3-6 6-7 11-1 3 0 6 2 8 2 3 4 3 7 4-2 2-3 4-6 4-3 1-7 0-9-2 0-1 0-1-1-1h0c-1-1-2-3-3-4-4-6-3-14-2-21h2c0-1 1-1 1-2h0l2-3h1c1-1 2-2 3-2 2-3 5-5 9-7v-1l3-1z" class="B"></path><path d="M482 477l3-5v4c0 1-1 2-2 3l-1-2z" class="D"></path><path d="M500 466c1 0 2 0 2 1h3 4l-11 1c-2-1-3-1-5-1l3-1h4z" class="Q"></path><path d="M480 481l2-4 1 2c-2 3-2 5-3 8l-1 1c0 1 0 1-1 2 1 1 1 2 1 3v2-1h-1c-1-1 0-3 0-4-1-3 0-5 1-7l1-2z" class="P"></path><path d="M488 467h2 1 2c2 0 3 0 5 1l-4 1c-1 0-2 1-3 0l-6 3h-1v-1c1-1 3-2 4-4z" class="H"></path><path d="M488 467h2 1 2c2 0 3 0 5 1l-4 1h-4c-1 0-1-1-2-2z" class="J"></path><path d="M515 465c3 0 5 1 7 1 1 1 3 2 4 3l-1 2c-4-3-8-3-13-3 1-1 2 0 3-1v-2h-1 1z" class="m"></path><path d="M478 478l2-4c1-1 2-3 4-4v1 1h1 0l-3 5-2 4-1-2-1 1-1 2v-2c0-1 1-2 1-2z" class="b"></path><path d="M491 464h0v1c3 1 6 0 9 1h-4l-3 1h-2-1-2c-1 2-3 3-4 4v-1c-2 1-3 3-4 4l-2 4v-1c0-2 1-3 1-4 1-1 2-2 3-2 2-3 5-5 9-7z" class="V"></path><defs><linearGradient id="H" x1="476.862" y1="497.903" x2="475.184" y2="485.031" xlink:href="#B"><stop offset="0" stop-color="#2d2c2d"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#H)" d="M476 476h0l2-3h1c0 1-1 2-1 4v1s-1 1-1 2v2l1-2 1-1 1 2-1 2c-1 2-2 4-1 7 0 1-1 3 0 4h1v1 2c0-1-1-1-1-1 0 1-1 2-1 3 1 0 1 1 1 2l1 1-1 1h0c-1-1-2-3-3-4-4-6-3-14-2-21h2c0-1 1-1 1-2z"></path><path d="M477 482l1-2 1-1 1 2-1 2c-1 0 0 0-1-1h0l-2 2v-1-1h1 0z" class="o"></path><defs><linearGradient id="I" x1="477.003" y1="489.648" x2="470.066" y2="483.399" xlink:href="#B"><stop offset="0" stop-color="#666667"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#I)" d="M476 476h0l2-3h1c0 1-1 2-1 4-3 5-4 10-4 16 1 2 2 4 1 6-4-6-3-14-2-21h2c0-1 1-1 1-2z"></path><path d="M494 462c1 0 3-1 4-1 4-1 7-1 11-1 1 0 2 1 3 1 2 0 4 2 6 2v1h-3v1h-1 1v2c-1 1-2 0-3 1l-3-1h-4-3c0-1-1-1-2-1-3-1-6 0-9-1v-1h0v-1l3-1z" class="W"></path><path d="M504 465c-2 0-4-1-7-1 3-1 6-1 9-1h0s-1 0-1 1h0 2l-3 1z" class="U"></path><path d="M509 460c1 0 2 1 3 1 2 0 4 2 6 2v1h-3v1h-1 1l-2 1-1-1h1l-1-1c1-1 1-1 1-2h-1-3c-1 0-1 0-1-1l1-1zm-18 3l3-1 1 1h-1 1c-1 1-2 1-3 1v1h0 3c2 1 6 0 9 0l3-1v1h0l-2 2h-3c0-1-1-1-2-1-3-1-6 0-9-1v-1h0v-1z" class="b"></path><path d="M180 211c0-3-2-5-3-8l-5-10c-1-3-3-5-2-8 0-2 0-2 1-2 6-1 11 5 16 8 3 2 6 5 9 7 5 3 11 6 17 7h1c2-1 4-2 5-4v2 2 8c0 1 1 4 1 6v1 1h0c-1 0-2 1-2 1v1l-3 2h1 1l1 1c1 0 2-1 3-1 1 5 0 10 0 14h-6-3-3-2l-4-1c-1 0-3-1-4-1-3 0-6 0-8 1-1 0-1 1-2 1v1c0 1 0 1-1 2v-1l-1 1-1-1-2 1 1 1c-1 2-1 4-2 5v-4c0-1-1-2-2-2 0-1-1-2-2-3v-2l-2-2 2-3h0c0-1 1-2 2-3 1-3 4-5 6-7l1-2 1-2-2-1v-1c-1-1 0-1 0-2h-1l-2 1c0-2-1-2-2-4h-2z" class="m"></path><path d="M185 232v-2c1 0 2-1 2-2h1v2 1h-1c-1 0-1 1-2 1z" class="Q"></path><path d="M183 239h0l-1-2c1-2 2-2 3-3v2c-1 1-2 2-2 3z" class="Y"></path><path d="M183 239c0-1 1-2 2-3 0 1 0 3 1 4 0 0-1 1 0 1l-2 1c-1-1-1-2-1-3z" class="K"></path><path d="M179 232c0-1 1-2 2-3 1 1 1 1 1 2s0 1-1 2c0 1-1 2-1 3h-1c0-2 2-2 0-4zm11-12v2c0 1-2 3-2 4h-1-1c1-1 1-2 1-2v-2l1-2h2z" class="W"></path><path d="M195 213v3c-1 2-4 2-5 4h-2l1-2c2-1 4-2 5-4l1-1z" class="Q"></path><path d="M202 212v1c-2 2-4 4-6 7h0l-1 1 1-3c-1 0-1 1-2 1 1-2 2-2 3-3 2-2 3-3 5-4z" class="a"></path><path d="M194 219c1 0 1-1 2-1l-1 3c-1 2-4 5-4 7l-1 3-2-2c2-3 3-7 6-10z" class="N"></path><defs><linearGradient id="J" x1="185.609" y1="240.328" x2="187.43" y2="231.525" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#2f2f2e"></stop></linearGradient></defs><path fill="url(#J)" d="M185 232c1 0 1-1 2-1h1c-1 2-1 7 1 9 0 1 0 1-1 2v-1l-1 1-1-1c-1 0 0-1 0-1-1-1-1-3-1-4v-2-2z"></path><path d="M191 228c1 2 0 2 2 3-2 3-3 4-3 7h1c-1 0-1 1-2 1v1c-2-2-2-7-1-9v-1-1l2 2 1-3z" class="a"></path><path d="M191 228c1 2 0 2 2 3-2 3-3 4-3 7h1c-1 0-1 1-2 1l1-8 1-3z" class="j"></path><path d="M196 220c1 1 2 1 2 2-2 3-4 6-5 9h0c-2-1-1-1-2-3 0-2 3-5 4-7l1-1z" class="b"></path><path d="M178 192l-1-1h0c-1-2-2-3-3-5 0 0-1 0-1-1 7 3 11 10 15 17v1l-1-1h-1l-1-1v-1c-1-1-2-2-3-4h-1c-1 0-1 0-2-1 1 0 2 0 2-1-1 0-2-1-2-2h-1z" class="I"></path><path d="M188 202l5 8c1 1 1 2 2 3l-1 1c-1 2-3 3-5 4l-2-1v-1c-1-1 0-1 0-2h1v-1l-1-1v-2c1 0 1-1 2-2h-2 0c1-1 1-1 2-1 0-1-1-1-1-2s-1-2-2-3h1l1 1v-1z" class="i"></path><path d="M193 210c1 1 1 2 2 3l-1 1v-1c-2-1-4 0-6 1v-1l-1-1v-2 2h3c1 0 1 0 2-1h1v-1z" class="I"></path><path d="M188 214c2-1 4-2 6-1v1c-1 2-3 3-5 4l-2-1v-1c-1-1 0-1 0-2h1z" class="L"></path><path d="M180 211c0-3-2-5-3-8l-5-10c-1-3-3-5-2-8 0-2 0-2 1-2 1 1 3 1 4 2h-1-1c-1 1 0 1 0 2v2h1v1h1c0 1 0 2 1 2h1l1 1v-1h1c0 1 1 2 2 2 0 1-1 1-2 1 1 1 1 1 2 1h1c1 2 2 3 3 4v1l1 1c1 1 2 2 2 3s1 1 1 2c-1 0-1 0-2 1h0 2c-1 1-1 2-2 2v2l1 1v1h-1-1l-2 1c0-2-1-2-2-4h-2z" class="F"></path><path d="M178 192h1c0 1 1 2 2 2 0 1-1 1-2 1 1 1 1 1 2 1h1c1 2 2 3 3 4v1l1 1c1 1 2 2 2 3s1 1 1 2c-1 0-1 0-2 1h0 2c-1 1-1 2-2 2v2l1 1v1h-1-1v-1-3c-1-1-1 0-2-1l1-1v-2h0l-1-2c0-1-1-1-1-1v-2c-1-1-1-1-3-1v-1h1v-1l-1-1-1-1-1 1c-1-1 1-1 1-3h-3l1-1h1v-1z" class="Y"></path><path d="M171 183c6-1 11 5 16 8 3 2 6 5 9 7 5 3 11 6 17 7l-2 2c-1 1-3 1-4 1s-2 0-2 1c-3 1-6 1-8 4h-1c-3-7-8-14-13-20-2-3-5-7-8-8-1-1-3-1-4-2z" class="n"></path><path d="M219 201v2 2 8c0 1 1 4 1 6v1 1h0c-1 0-2 1-2 1v1l-3 2h1 1l1 1c1 0 2-1 3-1 1 5 0 10 0 14h-6-3-3-2l-4-1c-1 0-3-1-4-1-3 0-6 0-8 1h-1c0-3 1-4 3-7h0c1-3 3-6 5-9 0-1-1-1-2-2h0c2-3 4-5 6-7v-1c0-1 1-1 2-1 2-1 3-1 4-2h-3c0-1 1-1 2-1s3 0 4-1l2-2h1c2-1 4-2 5-4z" class="W"></path><path d="M202 218c0-1 2-2 3-3l1 1v1l-3 3h0c-1-1-1-1-1-2z" class="a"></path><path d="M202 218c0 1 0 1 1 2h0v2l-3 2c-2 1-3 3-4 4-1 2-2 3-2 4l-1-1c1-3 3-6 5-9l4-4z" class="N"></path><path d="M203 220h0v2l-3 2c0-2 1-3 3-4z" class="U"></path><path d="M219 201v2 2h0c-1 1-2 2-2 3l-1 2c0 1-1 2-1 3v1h0 0c0-1-1-1-1-1h-1 0l-1 1c0-1-1-1-1-1-1 0-1 0-1-1l1-1c-3-1-4 2-6 3h0c0-2 4-4 5-5v-1l-2 1h-3c0-1 1-1 2-1s3 0 4-1l2-2h1c2-1 4-2 5-4z" class="o"></path><path d="M219 203v2h0c-1 1-2 2-2 3l-1 2c-1 0-2 0-2 1-1-1-1-1-1-2h0l2-2c2-1 3-2 4-4z" class="K"></path><path d="M213 209c0 1 0 1 1 2 0-1 1-1 2-1 0 1-1 2-1 3v1h0 0c0-1-1-1-1-1h-1 0l-1 1c0-1-1-1-1-1-1 0-1 0-1-1l1-1c0-1 1-1 1 0h1l-1-1 1-1z" class="Q"></path><path d="M216 210l1-2c0-1 1-2 2-3h0v8c0 1 1 4 1 6v1 1h0c-1 0-2 1-2 1-1 1-2 1-3 2s-2 1-3 1c1-2 6-3 6-5-1 0-1 0-2 1s-2 1-3 2c-2 1-4 2-5 2-1 1-2 1-3 2v-1l-1 1h-1c-1 1-1 1-1 2h-1-1l-3 2h0c0-1 0-2-1-3 1-1 2-3 4-4l3-2c1-1 3-2 5-3h1c1 0 2-1 3-2v-1c-1 0-1 1-2 1 1-1 1-1 1-2h-1-2c1-1 1-1 3-1 0 1 1 1 1 1 1 0 1 0 1-1h1 0l-1-1h0 1s1 0 1 1h0 0v-1c0-1 1-2 1-3z" class="V"></path><path d="M216 210l1-2c0-1 1-2 2-3h0v8c0 1 1 4 1 6v1h0c-1-1-2-2-3-2h0v-2h0l-1-1c-1-1 1-2 2-3v-1c-1 1-2 2-3 2 0-1 1-2 1-3z" class="e"></path><path d="M216 210l1-2c0-1 1-2 2-3v5l-1 1c-1 1-2 2-3 2 0-1 1-2 1-3z" class="a"></path><defs><linearGradient id="K" x1="220.432" y1="238.562" x2="200.907" y2="224.578" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#K)" d="M197 231h0l3-2h1 1c0-1 0-1 1-2h1l1-1v1c1-1 2-1 3-2 1 0 3-1 5-2 1-1 2-1 3-2s1-1 2-1c0 2-5 3-6 5 1 0 2 0 3-1s2-1 3-2v1l-3 2h1 1l1 1c1 0 2-1 3-1 1 5 0 10 0 14h-6-3-3-2l-4-1c-1 0-3-1-4-1-3 0-6 0-8 1h-1c0-3 1-4 3-7h0l1 1c0-1 1-2 2-4 1 1 1 2 1 3z"></path><path d="M196 228c1 1 1 2 1 3-1 1-2 1-3 3h-1l1-2c0-1 1-2 2-4z" class="Q"></path><path d="M193 231h0l1 1-1 2h0v1c3 0 6 0 8-1 3 0 10 1 12 3 1 0 2 1 2 2h-3-3-2l-4-1c-1 0-3-1-4-1-3 0-6 0-8 1h-1c0-3 1-4 3-7z" class="d"></path><path d="M199 237c1-1 2-1 3-1h1c3 1 7 1 9 3h0-3-2l-4-1c-1 0-3-1-4-1z" class="G"></path><path d="M203 238c1-1 2-1 4-1 1 1 1 1 2 1v1h0-2l-4-1z" class="C"></path><defs><linearGradient id="L" x1="309.566" y1="406.945" x2="370.034" y2="394.586" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363636"></stop></linearGradient></defs><path fill="url(#L)" d="M329 502V300l8 6c5 4 10 7 15 11 3 2 6 5 9 8l-1 1c2 3 7 6 9 10l-1 1 4 4-1 1c0-1-1-2-2-2 1 1 1 2 2 3v1c1 0 1 1 1 2h-1l-1-1v1l1 3h1c0 1 0 2 1 3h0c2 2 3 5 5 7l1 1 1 3 1 2h0c-2 2-3 3-3 6h-1l-1-1h0c1 3 5 7 5 10 0 1 1 1 1 2 1 1 1 2 2 3 2 4 3 8 4 13 1 1 1 4 1 6 1 0 1 1 1 2-1 1-1 2-2 3h0c-1 3-4 7-3 9 0 1 0 2 1 3v2h0l-1-3-1 2h0c1 1 1 2 1 4 1 1 1 2 1 4h1l-1 1c-1-1-2-4-2-6 0-1 0-2-1-3h-1 0c0-1 0-1-1-2h0v-1l-1 1v-1c-1 1-1 1-1 2h-1v2h-1v-2-1c-3 4-3 8-5 12v3l-1 4v4 2 1h1c1 1 1 5 1 6s0 1-1 2v-2c0 1 0 2-1 3v1 1c1 0 1-1 2-1h0l1-1c0 1 0 2-1 3l-1 2 1 2-2 2-1 1-2-1-1 2c-1 2-2 3-3 4-1 2-3 3-4 4l-1 1-1 1c-1 2-4 3-5 5 1 0 2-1 3-1h0c1-1 1-1 2-1h1c3-1 5-3 8-4l1 1c-1 0-1 0-1 1h-1 0c-1 1-2 1-3 1s0 0-1 1c0 0-1 0-2 1h0v1l-2 1-11 6c-1 1-2 1-3 3l-1 1-2 1v-2c-3 3-5 5-7 8-1 2-2 4-3 5h-2v-1-1z"></path><path d="M341 491l6-3c-1 1-2 1-3 3l-1 1-2 1v-2z" class="Q"></path><path d="M356 474h0l1-1c0 1 1 1 2 2l-1 1c-1 1-2 2-4 2 1-2 2-3 2-4z" class="K"></path><path d="M336 372h1l1 3c-1 2-1 4-1 7 0 0-1 1-1 2v5 5l-2 5h0c0-3 1-5 1-7-1-2-1-3 0-4v-1-1-3-3c1-1 1-1 1-2v-1c1-1 1-2 1-2v-1l-1-1v-1z" class="P"></path><path d="M355 469c1 1 0 1 0 2-1 1-1 2-2 2l-4 5c-1 2-3 4-5 6 1-4 3-7 4-11h0c2-1 3-3 5-4l-1 2v1l3-3z" class="I"></path><path d="M355 469c1 1 0 1 0 2-1 1-1 2-2 2l-4 5c0-3 2-4 3-6l3-3z" class="O"></path><path d="M350 459v4c-1 1-1 1 0 2h0c-1 2-3 3-3 5l1 1c0 1 0 2-1 3-1 2-2 5-3 7l-1-1c1-1 1-2 1-3-1 2-2 3-3 5-1-3 3-10 5-13 0-1 0-3 1-4h1l2-6z" class="J"></path><path d="M342 462v-1c1-1 1-2 1-3h1c0 2 0 3-1 5h0c0 2-2 5-1 6 0 2-1 3-2 5s-1 4-2 6h-2l-1-1c0-1 0-2 1-3v-2l1-1c0-1 0-1 1-1v-2c0-1 1-2 2-3 0-2 1-4 2-5z" class="H"></path><path d="M340 331h2c2 2 3 5 5 7l1 1v-2c0-1 1-1 1-1v-2l1 1v1l-1 1c-1 1-1 2 0 3 0 1 1 2 2 3l4 10h-1c-1 0-2-3-3-3 0 1 0 2 1 4h0 0c-2-2-2-3-3-5 0-1-1-2-2-3h1c-1-2-1-3-2-4l-3-6h1 1c0-1-3-4-4-5h-1z" class="K"></path><path d="M346 342l-3-6h1 1c2 4 3 7 5 11 0 1 1 2 1 3s0 2 1 4h0 0c-2-2-2-3-3-5 0-1-1-2-2-3h1c-1-2-1-3-2-4z" class="W"></path><defs><linearGradient id="M" x1="336.257" y1="334.931" x2="343.998" y2="340.469" xlink:href="#B"><stop offset="0" stop-color="#434343"></stop><stop offset="1" stop-color="#616161"></stop></linearGradient></defs><path fill="url(#M)" d="M336 338c1-1 1-3 1-5 0 0 0-1 1-1 0-1 1-1 1-2l1 1h1c1 1 4 4 4 5h-1-1l3 6s-1-1-2-1v1c-1 0-1 0-1-1h0-1c-1 0-1 1-1 2l1 1v1h1l-2 1v-2l-1-1-1-1c0-2-1-3 0-5v-1l-2 2h-1z"></path><path d="M341 331c1 1 4 4 4 5h-1l-1-1-1 1c0-1 0-1-1-1l-1-3 1-1z" class="Y"></path><path d="M335 383c0 1 0 3-1 4h0c0-2 1-4 1-5 0-2-1-3-1-4v-11c-1-3-2-7-2-10v-5c1-1 0-3 0-4s2-3 2-4c0 0-1-2-1-3v-7c1 3 2 5 2 7 1 4-1 6-2 10 0 1 1 2 1 4v3c1 2 0 3 0 5 1 1 1 2 1 3 1 1 1 1 1 2l-1 1c1 1 1 2 1 3v1l1 1v1s0 1-1 2v1c0 1 0 1-1 2v3z" class="J"></path><path d="M337 338l2-2v1c-1 2 0 3 0 5l1 1-1 1c0 1-1 1-1 1v1 1l-2 2c0 1 1 2 0 3v5l1 1c0 2-1 2-1 4 1 0 1 0 2-1 0 2-1 3-1 4-1 1-1 2-1 3h1v4h-1c0-1 0-2-1-3l1-1c0-1 0-1-1-2 0-1 0-2-1-3 0-2 1-3 0-5v-3c0-2-1-3-1-4 1-4 3-6 2-10v-1-1h1v-1h1z" class="O"></path><path d="M337 338l2-2v1c-1 2 0 3 0 5l1 1-1 1c0 1-1 1-1 1v1 1l-2 2c0 1 1 2 0 3v5h-1c0-2-1-6 0-7 0-1 1-2 1-4v-1h1v-7z" class="Q"></path><path d="M358 452h1v2c1 2 1 5 0 7l-1 2-1 1s-2 4-2 5l-3 3v-1l1-2c-2 1-3 3-5 4v-2l-1-1c0-2 2-3 3-5h0l1-3c0-1 0-2 1-4v-3h1l1-1v3h1v-1c0 1 0 1 1 1v-2-1h1l1-2h0z" class="L"></path><path d="M357 464v-1c0-3 1-6 2-9 1 2 1 5 0 7l-1 2-1 1z" class="I"></path><path d="M358 452h0c0 2 0 4-1 5 0 2-1 7-3 7 0-1 1-3 1-4-1 2-1 3-2 4h-1c0-1 0-1-1-2 0-1 0-2 1-4v-3h1l1-1v3h1v-1c0 1 0 1 1 1v-2-1h1l1-2z" class="j"></path><path d="M352 458v-3h1v5c0 1 0 2-1 4 0-1 0-1-1-2 0-1 0-2 1-4z" class="D"></path><defs><linearGradient id="N" x1="349.247" y1="322.952" x2="347.109" y2="335.138" xlink:href="#B"><stop offset="0" stop-color="#404041"></stop><stop offset="1" stop-color="#616161"></stop></linearGradient></defs><path fill="url(#N)" d="M340 324h-1v-1c1 0 1-1 2-1h1c1-1 1-1 2-1 3 2 7 7 8 10l1 1c3 2 4 6 7 8h-1v1s-3-3-4-3c0 1-1 1-1 1 0 2 1 3 2 4h-1 0c-1-2-2-3-3-4l-1-1h-1l-1 2c-1-1-1-2 0-3l1-1v-1l-1-1h-1c0-1-1-1-1-2-2-2-4-3-6-6l-1-2z"></path><path d="M348 331c1 1 2 1 2 2 1 0 1 0 1 1l1-1c2 1 5 6 7 7v1s-3-3-4-3c0 1-1 1-1 1 0 2 1 3 2 4h-1 0c-1-2-2-3-3-4h0c0-1-1-2-1-2 0-1 1-1 1-2-2-1-3-2-4-4z" class="Y"></path><path d="M340 324c1 1 2 1 2 2 1 0 5 5 6 5 1 2 2 3 4 4 0 1-1 1-1 2 0 0 1 1 1 2h0l-1-1h-1l-1 2c-1-1-1-2 0-3l1-1v-1l-1-1h-1c0-1-1-1-1-2-2-2-4-3-6-6l-1-2z" class="Q"></path><path d="M338 375v1 1 2h-1l1 1h0l1-1v3c-1 0-1 0-1 1v2h0c1 0 1-1 2-1v1c0 1 0 1 1 1 0-1 0-1 1-2 0 4-3 8-4 11 0 3-1 6-1 9-1 1-1 6 0 7v1h0c-2 3 0 7-1 10l-1 2c0 1-1 2-2 3 1-1 0-4 0-6 0-4 1-8 0-12v-1c1 0 1-2 1-3 1-1 1-3 1-4h0v-2c1-1 0-2 1-3v-1-1h0v-5-5c0-1 1-2 1-2 0-3 0-5 1-7z" class="H"></path><path d="M335 405v2c0 2 1 5 0 6h-1c0-3 0-5 1-8z" class="P"></path><path d="M338 375v1 1 2h-1l1 1h0l1-1v3c-1 0-1 0-1 1v2h0c1 0 1-1 2-1v1c0 1 0 1 1 1l-3 7v1 2l-1 5c-1 2-1 3-2 4 0-2 0-4 1-7v-3-1h0v-5-5c0-1 1-2 1-2 0-3 0-5 1-7z" class="O"></path><defs><linearGradient id="O" x1="343.108" y1="304.951" x2="363.443" y2="340.129" xlink:href="#B"><stop offset="0" stop-color="#2f2d2d"></stop><stop offset="1" stop-color="#626163"></stop></linearGradient></defs><path fill="url(#O)" d="M337 306c5 4 10 7 15 11 3 2 6 5 9 8l-1 1c2 3 7 6 9 10l-1 1 4 4-1 1c0-1-1-2-2-2-2 0-3-4-4-5s-2 0-3-1c0 0-1-1-2-1h-2c-1-2-4-3-6-6l-2-2 1-2c-1 0-1-1-2-1 0-2-1-3-3-4l1-1c-2-3-5-5-8-7-2-1-2-1-2-4z"></path><path d="M351 323c2 2 3 4 5 6 1 1 3 2 4 4h-2c-1-2-4-3-6-6l-2-2 1-2z" class="O"></path><defs><linearGradient id="P" x1="333.533" y1="445.795" x2="340.381" y2="445.709" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#484848"></stop></linearGradient></defs><path fill="url(#P)" d="M337 426h0v2 5h1v-7c1 2 0 6 0 8h1l-1-4h1v4h0c0-1 0-5 1-7 1 2 0 3 1 5v-1c1 1 1 1 1 2 0 2-1 3-1 4v6h0c0 1 0 1-1 2v5l1 2v1 4l1 1v3-2c-1 1-1 3-2 4h0-1v-1l-1 3c-1 0-1-1-1-1h-1c-1 0-1 0-2-1l1-1-1-2c-1 0-1-1-1-2v-1c-1-1-1-4-1-5 1-1 1-4 1-5l1-8c0-3-1-5 0-8 1-2 2-4 3-5z"></path><path d="M335 462h0c0 1 0 1 1 1v1-1c0-1 1-1 1-1l1 1v-1-4 3h1c0-2 1-5 2-7v-1 4l1 1v3-2c-1 1-1 3-2 4h0-1v-1l-1 3c-1 0-1-1-1-1h-1c-1 0-1 0-2-1l1-1z" class="D"></path><path d="M341 431v-3c0-1 1-2 1-2 2 1 0 3 1 5l1 1v-6h1c0 1 0 1 1 2 0-1 0-1 1-1v2h1v3c-1 3 0 7 0 11 1 1 1 3 1 4h-1v1l1 5c0 1 0 1-1 2v2 1c1 1-1 5-1 7-1 1-1 3-1 4-2 3-6 10-5 13 0 1-1 2-2 3v-1s0-2 1-2l-2-2c0 1-1 1-2 1h-1v-1-1l1 1h2c1-2 1-4 2-6s2-3 2-5c-1-1 1-4 1-6h0c1-2 1-3 1-5h-1c0 1 0 2-1 3v1-1-3l-1-1v-4-1l-1-2v-5c1-1 1-1 1-2h0v-6c0-1 1-2 1-4 0-1 0-1-1-2z" class="Y"></path><path d="M345 433h1c0 1 0 3 1 4h0c-1 2-1 5-1 7-1-3 1-6-1-9h0v-2z" class="W"></path><path d="M343 449h0v-1h1 0v3c1 1 0 5-1 7v-9z" class="m"></path><path d="M346 444c0-2 0-5 1-7 1 4 0 8 1 11l1 5c-2-1-2-2-2-4l-1-5z" class="a"></path><path d="M345 433c0-1 0-1 1-2 1 1 1 1 2 1-1 3 0 7 0 11 1 1 1 3 1 4h-1v1c-1-3 0-7-1-11h0c-1-1-1-3-1-4h-1z" class="b"></path><path d="M344 447h0c-1-3-2-9 0-12 1 4 1 7 1 10 0 2 1 5 1 7l-2-5z" class="e"></path><defs><linearGradient id="Q" x1="347.7" y1="431.166" x2="337.23" y2="444.174" xlink:href="#B"><stop offset="0" stop-color="#494746"></stop><stop offset="1" stop-color="#676769"></stop></linearGradient></defs><path fill="url(#Q)" d="M341 431v-3c0-1 1-2 1-2 2 1 0 3 1 5l1 1v2 1c-2 3-1 9 0 12h0v1h-1v1h0l-2-6v-6c0-1 1-2 1-4 0-1 0-1-1-2z"></path><path d="M348 458c1 1-1 5-1 7-1 1-1 3-1 4-2 3-6 10-5 13 0 1-1 2-2 3v-1s0-2 1-2l-2-2c0 1-1 1-2 1h-1v-1-1l1 1h2c1-2 1-4 2-6s2-3 2-5c1-3 2-7 3-11h1c0 2 0 4-1 6l-3 8-2 7c1-1 1-2 2-4l6-17z" class="L"></path><path d="M353 332h1c0 1 1 1 2 2v-1h2 2c1 0 2 1 2 1 1 1 2 0 3 1s2 5 4 5c1 1 1 2 2 3v1c1 0 1 1 1 2h-1l-1-1v1l1 3h1c0 1 0 2 1 3h0c2 2 3 5 5 7l1 1 1 3 1 2h0c-2 2-3 3-3 6h-1l-1-1h0c1 3 5 7 5 10-1-2-2-3-3-5s-3-4-5-6c0 2 0 2 1 4h0l-3-3c-1-1-1-2-2-2l-1-1v2h-1l-1-1s-1 0-1 1h0-1l-1-2-1-1c0-1-1-2-1-2h0c0-1 0-2 1-3h1 1c-1 0-2-2-3-3l-1 1v1h-2c0-1-1-3-1-4l-2-3-4-10c-1-1-2-2-2-3l1-2h1l1 1c1 1 2 2 3 4h0 1c-1-1-2-2-2-4 0 0 1 0 1-1 1 0 4 3 4 3v-1h1c-3-2-4-6-7-8z" class="U"></path><path d="M367 362l-1-1c-1-1-1-1-1-2s1-2 2-2h1v-1h1v1 1l2 2c0 1 1 3 1 4h0c0 1 0 1 1 2v1h-1v-1h-1v1h-1v-2l-3-3v-1 1z" class="e"></path><path d="M369 358l2 2c0 1 1 3 1 4h0c0 1 0 1 1 2v1h-1v-1h-1v1h-1v-2l-3-3h1v-3c0 1 1 1 1 2s1 1 1 2h1c0-2-2-3-2-5h0z" class="Y"></path><path d="M367 362v-1 1l3 3v2h1v-1c1 1 1 2 2 3h0c0 2 0 2 1 4h0l-3-3c-1-1-1-2-2-2l-1-1v2h-1l-1-1s-1 0-1 1h0-1l-1-2-1-1c0-1-1-2-1-2h0c0-1 0-2 1-3h1 1l2 2h1v-1z" class="Q"></path><path d="M362 366c0-1-1-2-1-2h0c0-1 0-2 1-3h1c0 1 1 3 2 3 0 0 1 0 1 1 1 0 1 1 1 2h-1-1l-1 1 1 1h0-1l-1-2-1-1z" class="P"></path><path d="M355 343h1c-1-1-2-2-2-4 0 0 1 0 1-1 1 0 4 3 4 3v-1h1s1 1 1 2l3 3c0 1 1 1 1 1v1c0 1 1 2 1 3-1 0-1-3-3-3v1s1 0 1 1h-1v1c-2 0-2-1-3-2l-1 1-4-6z" class="W"></path><path d="M360 348l-1-1c0-2-2-3-3-5 2 1 3 3 5 3l2 4v1c-2 0-2-1-3-2z" class="T"></path><path d="M349 340l1-2h1l1 1c1 1 2 2 3 4h0l4 6c1 2 2 3 2 6v1 1 1l-1 1v1h-2c0-1-1-3-1-4l-2-3-4-10c-1-1-2-2-2-3z" class="W"></path><path d="M349 340l1-2h1l1 1h-1v4c-1-1-2-2-2-3z" class="V"></path><path d="M357 356l1 1v-1h1 0c1 0 1 1 2 1v1l-1 1v1h-2c0-1-1-3-1-4z" class="b"></path><path d="M361 356l-4-5c-2-2-5-7-4-9 1 0 1 0 2 1h0l4 6c1 2 2 3 2 6v1z" class="d"></path><defs><linearGradient id="R" x1="355.056" y1="335.049" x2="372.94" y2="344.1" xlink:href="#B"><stop offset="0" stop-color="#3f4040"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#R)" d="M353 332h1c0 1 1 1 2 2v-1h2 2c1 0 2 1 2 1 1 1 2 0 3 1s2 5 4 5c1 1 1 2 2 3v1c1 0 1 1 1 2h-1l-1-1v1l1 3v2l-2-2c0 2 1 2 0 3l-1 1c0-2-1-3-1-4-1 0-1-1-1-1 0-1-1-1-1-2 0 0-1 0-1-1l-3-3c0-1-1-2-1-2-3-2-4-6-7-8z"></path><path d="M366 341c1 0 2 1 3 2l1 2v1c-2-1-4-3-4-5z" class="o"></path><path d="M364 345c1 0 1 0 1-1h1v-1l-1-1c-1-1-2-1-2-2l1-1 2 2c0 2 2 4 4 5l1 3v2l-2-2c0 2 1 2 0 3l-1 1c0-2-1-3-1-4-1 0-1-1-1-1 0-1-1-1-1-2 0 0-1 0-1-1z" class="m"></path><path d="M371 349h1c0 1 0 2 1 3h0c2 2 3 5 5 7l1 1 1 3 1 2h0c-2 2-3 3-3 6h-1l-1-1h0c1 3 5 7 5 10-1-2-2-3-3-5s-3-4-5-6h0c-1-1-1-2-2-3h1v1h1v-1c-1-1-1-1-1-2h0c0-1-1-3-1-4v-1c-1-2-1-3-2-4h0c0-1 0-2-1-2l1-1c1-1 0-1 0-3l2 2v-2z" class="W"></path><path d="M371 349h1c0 1 0 2 1 3 2 4 3 7 4 11 0 1-1 1-1 2l-1 1c0 1 0 1 1 2h0l-2-1 1-1c-1 0-1-1-1-1 0-1 0-2-1-2v-1h0l1 1h1c0-1-1-2-1-3 0-2-1-3-1-5-1-1-2-2-2-4v-2z" class="Y"></path><path d="M373 352h0c2 2 3 5 5 7l1 1 1 3c-1 2-2 3-2 5h-1-1c1-2 1-2 0-3 0-1 1-1 1-2-1-4-2-7-4-11z" class="h"></path><path d="M380 363l1 2h0c-2 2-3 3-3 6h-1l-1-1h0c1 3 5 7 5 10-1-2-2-3-3-5s-3-4-5-6h0c-1-1-1-2-2-3h1v1h1v-1c-1-1-1-1-1-2h0l2 3 2 1h0c-1-1-1-1-1-2l1-1c1 1 1 1 0 3h1 1c0-2 1-3 2-5z" class="K"></path><path d="M360 416c1-2 1-4 1-6l1-1c0 1 1 2 1 2h2l1 1v-1c1 0 1 1 3 0l1 2s0 1 1 1 1 0 2 1c0 1 1 1 2 2 0 0 0 1 1 1l1-1v-1-1l1 1v1h1l1-1v1h1v-2h1c1 2 2 3 3 5l-1 2h0c1 1 1 2 1 4 1 1 1 2 1 4h1l-1 1c-1-1-2-4-2-6 0-1 0-2-1-3h-1 0c0-1 0-1-1-2h0v-1l-1 1v-1c-1 1-1 1-1 2h-1v2h-1v-2-1c-3 4-3 8-5 12v3l-1 4v4 2 1h1c1 1 1 5 1 6s0 1-1 2v-2c0 1 0 2-1 3v1 1c1 0 1-1 2-1h0l1-1c0 1 0 2-1 3l-1 2 1 2-2 2-1 1-2-1-1 2c-1 2-2 3-3 4-1 2-3 3-4 4l-1 1c-1-1-2-1-2-2l-1 1h0v-1c-2 2-3 4-5 5 0-1 1-3 2-4v-1c1 0 1-1 2-2 0-1 1-1 0-2 0-1 2-5 2-5l1-1 1-2c1-2 1-5 0-7v-2h-1 0l-1 2h-1v1 2c-1 0-1 0-1-1v1h-1v-3l-1 1h-1v3c-1 2-1 3-1 4l-1 3c-1-1-1-1 0-2v-4l-2 6h-1c0-2 2-6 1-7v-1-2c1-1 1-1 1-2l-1-5v-1h1c0-1 0-3-1-4 0-4-1-8 0-11v-3c0-1 0-1 1-2v6h1v-4h0v-1c1-3 4-5 6-7l4-5h0z" class="l"></path><path d="M359 428c-1-1-1-1-1-3h1c0 1 0 1 1 2l1-1h0c1-1 1-3 1-4v-1c-1 0-1-1 0-1v1l3-3h0c0 1-1 3-2 4v4l-1 1v-1l-1 1c-1 1-1 0-1 1l-1-1v1z" class="U"></path><path d="M370 413s0 1 1 1 1 0 2 1c0 1 1 1 2 2 0 0 0 1 1 1v1c-1 0-1 1-2 1 0-2 0-2-2-3h0c0 1 1 1 1 3h-1v-1l-1-1v1h-2c1-1 1-1 1-2 0 0-1 0-1-1v-2l1-1zm-10 3c2-1 2-3 3-3s1 1 2 1v-1l1 1v1l-2-1-1 2c-1 0-1 1-1 2s-1 1-1 2v1h0l-1-1c0 1 0 1-1 2 0 1-1 2-1 4-1 0-1 0-1-1l1-1v-1l-1-1v-1h1 1c1-2 1-3 1-5h0z" class="V"></path><path d="M360 416c1-2 1-4 1-6l1-1c0 1 1 2 1 2h2l1 1v-1c1 0 1 1 3 0l1 2-1 1-2-2h0c0 2 1 4 2 5v1h-1 0c0-1-1-2-1-3h0-1 0v-1l-1-1v1c-1 0-1-1-2-1s-1 2-3 3z" class="Y"></path><path d="M356 454c0-3-1-14 0-16 1-1 1-2 1-3h0c1-1-1-4 1-5v1 4 1 1c-1 2-1 4-1 6 0 3 0 7 1 9l-1 2h-1z" class="e"></path><path d="M368 425v-2c1-1 1-3 2-3h1v2l-1 1c0 2-1 3-2 5l-1 5v1h1v-3c1-2 2-7 4-8h0l-2 8v1c-1 3-2 6-2 10 0 1 0 3-1 5v-3-3s-1 0-1-1l-1 1v2l-1-2v-3c2-5 3-9 4-13z" class="d"></path><path d="M377 416v-1l1 1v1h1l1-1v1h1v-2h1c1 2 2 3 3 5l-1 2h0c1 1 1 2 1 4 1 1 1 2 1 4h1l-1 1c-1-1-2-4-2-6 0-1 0-2-1-3h-1 0c0-1 0-1-1-2h0v-1l-1 1v-1c-1 1-1 1-1 2h-1v2h-1v-2-1c-3 4-3 8-5 12v3l-1 4v4 2 1h1c1 1 1 5 1 6s0 1-1 2v-2-2l-1-1v-2-1-1c0 2-1 4 0 6 0 2 0 3-1 5 0 1 0 1-1 1h0c0-1 1-3 1-4h-1 0c0 1 0 2-1 3v-4h0l-1 3h-1c0-2 1-5 1-7v-4 3c1-2 1-4 1-5 0-4 1-7 2-10v-1l2-8h1c0-1 0-2 1-2h0c-1 3-3 6-3 9 0 0 0 1-1 1v4l-1 3v5h0c0 1 0 1 1 2 1-1 0-3 0-4 1-1 0-2 1-3v-2-3l3-8c0-2 2-4 2-6v-1l1-1v-1z" class="j"></path><path d="M368 456c1-1 1-2 1-3h0 1c0 1-1 3-1 4h0c1 0 1 0 1-1 1-2 1-3 1-5-1-2 0-4 0-6v1 1 2l1 1v2c0 1 0 2-1 3v1 1c1 0 1-1 2-1h0l1-1c0 1 0 2-1 3l-1 2 1 2-2 2-1 1-2-1-1 2c-1 2-2 3-3 4-1 2-3 3-4 4l-1 1c-1-1-2-1-2-2l-1 1h0v-1c-2 2-3 4-5 5 0-1 1-3 2-4v-1c1 0 1-1 2-2 0-1 1-1 0-2 0-1 2-5 2-5l1-1 1 1h0l2 1c1-1 1-2 2-2l3-8h1l1-3h0v4z" class="J"></path><path d="M359 464l2 1v1c-1 1-2 1-3 2v-1c0-1 0-2 1-3z" class="B"></path><path d="M358 472c2-2 3-4 5-5 1 0 1 0 2 1-2 2-4 3-7 4z" class="K"></path><path d="M373 456l1-1c0 1 0 2-1 3l-1 2 1 2-2 2-1 1-2-1c2-2 4-5 5-8z" class="O"></path><path d="M372 460l1 2-2 2c0-1 1-2 1-4z" class="K"></path><path d="M367 466c-1 2-2 3-3 4-1 2-3 3-4 4l-1 1c-1-1-2-1-2-2l1-1c3-1 5-2 7-4l2-2z" class="Q"></path><path d="M366 455h1l1-3h0v4c0 1 0 2-1 2h-1v1c0 1-2 3-3 4h0l3-8z" class="O"></path><path d="M360 416c0 2 0 3-1 5h-1-1v1 1c-1 1-1 2-2 3v-2l-1 1v2h0 1v1c1 2 1 6 1 9-1 2-1 4-1 6h1c0 3 0 8-1 11v2 1h-1v-3l-1 1h-1v3c-1 2-1 3-1 4l-1 3c-1-1-1-1 0-2v-4l-2 6h-1c0-2 2-6 1-7v-1-2c1-1 1-1 1-2l-1-5v-1h1c0-1 0-3-1-4 0-4-1-8 0-11v-3c0-1 0-1 1-2v6h1v-4h0v-1c1-3 4-5 6-7l4-5z" class="Q"></path><path d="M350 459c0-1 0-3 1-5v3c0 1 1 1 1 1-1 2-1 3-1 4l-1 3c-1-1-1-1 0-2v-4z" class="k"></path><path d="M348 429c0-1 0-1 1-2v6h1v-4h0 1c0 3 0 6 1 8v2c1 2-1 6 0 8 1 1 0 3 0 4h0c-1-1-1-3-1-5l-1-2c0 3 0 5 1 7l-1 1c-1-2-1-3-1-5 0-1 0-3-1-4 0-4-1-8 0-11v-3z" class="U"></path><path d="M350 429h1c0 3 0 6 1 8v2c1 2-1 6 0 8 1 1 0 3 0 4h0c-1-1-1-3-1-5v-4c-1-2 0-4-1-6v-2-5h0z" class="T"></path><path d="M360 416c0 2 0 3-1 5h-1-1v1 1c-1 1-1 2-2 3v-2l-1 1v2h0 1v1c1 2 1 6 1 9-1 2-1 4-1 6v11c-1-4-2-8-2-12l-1 5c-1-2 1-6 0-8v-2c-1-2-1-5-1-8h-1v-1c1-3 4-5 6-7l4-5z" class="a"></path><path d="M355 428c1 2 1 6 1 9-1-1-1-2-1-3 0-2-1-5 0-6z" class="G"></path><path d="M360 416c0 2 0 3-1 5h-1-1v1 1c-1 1-1 2-2 3v-2l-1 1v2h0v6c0-1-1-5-1-6h-1c0 3 1 8 0 10-1-2-1-5-1-8h-1v-1c1-3 4-5 6-7l4-5z" class="j"></path><path d="M362 427v1h1v-2c1-2 1-6 3-7h1c-2 3-2 7-3 10v1c1-1 1-2 1-3 1-1 1-3 2-5v1 2h1c-1 4-2 8-4 13v3l1 2v-2l1-1c0 1 1 1 1 1v3 4c0 2-1 5-1 7l-3 8c-1 0-1 1-2 2l-2-1h0l-1-1 1-2c1-2 1-5 0-7v-2h-1 0c-1-2-1-6-1-9l1 1v-8c1-2 1-5 1-8v-1l1 1c0-1 0 0 1-1l1-1v1z" class="b"></path><path d="M364 430c1-1 1-2 1-3 1-1 1-3 2-5v1 2c0 2-1 4-2 5 0 2 0 4-1 6l-1-1v-3c1-1 1-1 1-2h0z" class="E"></path><path d="M359 428v-1l1 1c0 3 1 7-1 10v8 3h-1v-5-8c1-2 1-5 1-8z" class="d"></path><path d="M362 427v1h1v-2c1-2 1-6 3-7h1c-2 3-2 7-3 10v1h0c0 1 0 1-1 2v3l1 1h0c-1 3-1 6-1 9-1-2 0-6-1-8-2-2-1-7-1-10l1-1v1z" class="T"></path><path d="M357 443l1 1v5h1v-3h1c0 3 0 7 2 9 1-2-1-4 1-6h0c0 2 0 3 1 5v1l-1 3c-1 2-3 4-4 6l-1-1 1-2c1-2 1-5 0-7v-2h-1 0c-1-2-1-6-1-9z" class="Q"></path><path d="M364 441l1 2v-2l1-1c0 1 1 1 1 1v3 4c0 2-1 5-1 7l-3 8c-1 0-1 1-2 2l-2-1h0c1-2 3-4 4-6l1-3v-1c-1-2-1-3-1-5 1 0 1-1 1-2v-6z" class="D"></path><defs><linearGradient id="S" x1="363.878" y1="453.083" x2="364.773" y2="443.919" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#S)" d="M364 441l1 2v-2l1-1c0 1 1 1 1 1-1 1-1 3-1 4 0 4 0 7-2 10v-1c-1-2-1-3-1-5 1 0 1-1 1-2v-6z"></path><path d="M351 350c1 0 2 3 3 3h1l2 3c0 1 1 3 1 4h2v-1l1-1c1 1 2 3 3 3h-1-1c-1 1-1 2-1 3h0s1 1 1 2l1 1 1 2h1 0c0-1 1-1 1-1l1 1h1v-2l1 1c1 0 1 1 2 2l3 3h0c-1-2-1-2-1-4 2 2 4 4 5 6s2 3 3 5c0 1 1 1 1 2 1 1 1 2 2 3 2 4 3 8 4 13 1 1 1 4 1 6 1 0 1 1 1 2-1 1-1 2-2 3h0c-1 3-4 7-3 9 0 1 0 2 1 3v2h0l-1-3c-1-2-2-3-3-5h-1v2h-1v-1l-1 1h-1v-1l-1-1v1 1l-1 1c-1 0-1-1-1-1-1-1-2-1-2-2-1-1-1-1-2-1s-1-1-1-1l-1-2-1-1h0c0-1 0-2-1-3l-1-1-1 1h0l-3-3v-1c-1-1-1-1-1-2s0-2-1-3h-1v-2l-6-14c-2-2-3-5-4-8 0 0 1 0 1-1v-1h0c-1-2-1-2-1-4h1c0 1 0 2 1 3v-2l1-1v-2-3c0-1 0-2-1-2v-1l1-1s-1-1-1-2v-2h0c1 1 2 2 2 3h1c0-1-1-3-2-4h0c-1-2-1-3-1-4z" class="F"></path><path d="M365 377l2 2v2c0 1 1 2 2 3 1 3 4 6 5 10l-1 1c-2-3-3-7-5-10-1-3-4-5-5-8h2z" class="d"></path><path d="M373 395l1-1c1 2 2 5 3 8s2 8 4 10c1 1 1 2 1 3h-1v2h-1v-1l-1 1h-1v-1l-1-1v1c-1-1-1-3-1-4 0-3-1-5-1-8 0-1 0-1-1-1v-1h1c0 1 0 2 1 3 0 2 1 5 2 6h0c0-1-1-3-1-5-1-4-2-8-4-11z" class="J"></path><path d="M364 374l1-1 1 1h0v-1-1l5 5-1 1c0 1 1 2 1 4v1c1 1 1 2 2 3v1c1 1 1 1 1 2 0 2 2 5 3 7 1 6 2 11 4 16-2-2-3-7-4-10s-2-6-3-8c-1-4-4-7-5-10-1-1-2-2-2-3v-2l-2-2c0-1-1-2-1-3z" class="C"></path><path d="M361 376l6 15c1 2 3 5 3 7s1 4 2 5l-1-4v-1c0-1 0-2-1-3l-3-9h0c2 2 3 6 4 8 1 3 3 5 4 8h-1v1c1 0 1 0 1 1 0 3 1 5 1 8l-1-2c-1-2-3-7-4-7-1-1-1-2-2-3h0c0-2 0-3-1-5 0 1 0 1-1 1-1-2-1-3-2-5h0v1 1c0 1 1 1 0 2h-1c0-2-1-5-2-7h-1l-1-1c0-1-1-2-2-3v1l-1-1v-1h1 1v-3c1 0 1-1 1-1v-1c1 0 1-1 1-2z" class="e"></path><path d="M362 388c-1-2-2-4-1-7h1c3 5 4 9 6 14 0 1 0 1-1 1-1-2-1-3-2-5h0v1 1c0 1 1 1 0 2h-1c0-2-1-5-2-7z" class="E"></path><path d="M362 388c1 2 2 5 2 7h1c1-1 0-1 0-2v-1-1h0c1 2 1 3 2 5 1 0 1 0 1-1 1 2 1 3 1 5h0c1 1 1 2 2 3 1 0 3 5 4 7l1 2c0 1 0 3 1 4v1l-1 1c-1 0-1-1-1-1-1-1-2-1-2-2-1-1-1-1-2-1s-1-1-1-1l-1-2-1-1h0c0-1 0-2-1-3l-1-1-1 1h0l-3-3v-1c-1-1-1-1-1-2s0-2-1-3l-1-4v-2-1c0-1 0-2 2-3h1z" class="N"></path><path d="M371 403c1 0 3 5 4 7l1 2c0 1 0 3 1 4v1l-1 1c-1 0-1-1-1-1-1-1-2-1-2-2-1-1-1-1-2-1s-1-1-1-1l-1-2-1-1h0c1-1 1-1 1-2 1 1 1 2 2 2h1 0c-1-1-1-2-1-3l3 5h0c0-3-2-6-3-9z" class="B"></path><path d="M359 392c2 3 3 6 6 8l-2-4h1c1 2 1 4 3 6 0 0 0 1 1 1 0 1 0 1 1 2v-1s0-1-1-1v-2l-1-1v-1l2 2c0 1 1 3 1 4 1 1 1 2 1 2 0 1 0 2 1 3h0-1c-1 0-1-1-2-2 0 1 0 1-1 2 0-1 0-2-1-3l-1-1-1 1h0l-3-3v-1c-1-1-1-1-1-2s0-2-1-3l-1-4v-2z" class="e"></path><path d="M361 401c1 0 2 1 2 1l3 3h1v1c1 0 1 0 2-1v3c0 1 0 1-1 2 0-1 0-2-1-3l-1-1-1 1h0l-3-3v-1c-1-1-1-1-1-2z" class="b"></path><path d="M351 350c1 0 2 3 3 3h1l2 3c0 1 1 3 1 4h2v-1l1-1c1 1 2 3 3 3h-1-1c-1 1-1 2-1 3h0s1 1 1 2l1 1 1 2h1c0 1 1 2 1 3v1 1h0l-1-1-1 1c0 1 1 2 1 3h-2c0-1-1-1-1-2l-1 1h0c0 1 0 2-1 2v1s0 1-1 1v3h-1-1v1l1 1v-1c1 1 2 2 2 3l1 1c-2 1-2 2-2 3v1 2l1 4h-1v-2l-6-14c-2-2-3-5-4-8 0 0 1 0 1-1v-1h0c-1-2-1-2-1-4h1c0 1 0 2 1 3v-2l1-1v-2-3c0-1 0-2-1-2v-1l1-1s-1-1-1-2v-2h0c1 1 2 2 2 3h1c0-1-1-3-2-4h0c-1-2-1-3-1-4z" class="k"></path><path d="M350 373c1 1 1 3 2 4 1 2 1 3 1 5-2-2-3-5-4-8 0 0 1 0 1-1z" class="V"></path><path d="M359 394c-1-3-2-6-4-9v-1c1 0 1 1 2 1v-1l1 1v-1c1 1 2 2 2 3l1 1c-2 1-2 2-2 3v1 2z" class="W"></path><path d="M358 385v-1c1 1 2 2 2 3l1 1c-2 1-2 2-2 3-1-2-1-4-1-6z" class="d"></path><path d="M352 366l1 2 2 6h-1c-1-1-1-1-1-2h0v4s1 0 1 1h-1l-2-5h-1 0c-1-2-1-2-1-4h1c0 1 0 2 1 3v-2l1-1v-2z" class="V"></path><path d="M360 367l1-1h1l1 1-1 1h0c0 1-1 3-1 4 0 0-1 0-1 1 0 0 0 2 1 2v1h0c0 1 0 2-1 2v1s0 1-1 1v3h-1-1v-1c1-1 1-2 1-4h0l1 1 1-1-2-3v-4l1-1v-3h1z" class="b"></path><path d="M361 376v-1c-1 0-1-2-1-2 0-1 1-1 1-1 0-1 1-3 1-4h0l1-1 1 2h1c0 1 1 2 1 3v1 1h0l-1-1-1 1c0 1 1 2 1 3h-2c0-1-1-1-1-2l-1 1z" class="U"></path><path d="M364 374c-1-1-2-2-2-3v-2h2 1c0 1 1 2 1 3v1 1h0l-1-1-1 1z" class="T"></path><path d="M353 368v-1c1-1 1-1 1-3h-1c0-1 0-1 1-2l1 1c0 2 1 3 2 5 0 1 0 2 1 3v4c0 1-1 1-1 1v1l-1 1v-2c-1-1-1-1-1-2l-2-6z" class="P"></path><path d="M351 350c1 0 2 3 3 3h1l2 3c0 1 1 3 1 4h2v-1l1-1c1 1 2 3 3 3h-1-1c-1 1-1 2-1 3h0s1 1 1 2h-1l-1 1h-1v3l-1 1c-1-1-1-2-1-3-1-2-2-3-2-5l-1-1c-1 1-1 1-1 2h1c0 2 0 2-1 3v1l-1-2v-3c0-1 0-2-1-2v-1l1-1s-1-1-1-2v-2h0c1 1 2 2 2 3h1c0-1-1-3-2-4h0c-1-2-1-3-1-4z" class="j"></path><path d="M357 368s1-1 1-2h0c1 0 1 1 2 1h-1v3l-1 1c-1-1-1-2-1-3z" class="V"></path><path d="M351 350c1 0 2 3 3 3h1l2 3c0 1 1 3 1 4 0 2-1 4-1 6-1-3-2-5-3-7v-1c0-1-1-3-2-4h0c-1-2-1-3-1-4z" class="O"></path><path d="M365 369c0-1 1-1 1-1l1 1h1v-2l1 1c1 0 1 1 2 2l3 3h0c-1-2-1-2-1-4 2 2 4 4 5 6s2 3 3 5c0 1 1 1 1 2 1 1 1 2 2 3 2 4 3 8 4 13 1 1 1 4 1 6 1 0 1 1 1 2-1 1-1 2-2 3h0c-1 3-4 7-3 9 0 1 0 2 1 3v2h0l-1-3c-1-2-2-3-3-5 0-1 0-2-1-3-2-5-3-10-4-16-1-2-3-5-3-7 0-1 0-1-1-2v-1c-1-1-1-2-2-3v-1c0-2-1-3-1-4l1-1-5-5c0-1-1-2-1-3h0z" class="B"></path><path d="M374 389c0-1 0-1-1-2v-1c-1-1-1-2-2-3v-1c0-2-1-3-1-4l1-1c2 3 3 7 5 10 1 3 2 6 3 8 0 2 1 5 1 7 1 2 2 5 2 8-1-2-1-4-2-5l-3-9c-1-2-2-5-3-7z" class="M"></path><path d="M365 369c0-1 1-1 1-1l1 1h1v-2l1 1c0 1 2 3 1 4l-1 1c2 2 4 3 5 5h0c4 6 8 12 10 19 0 2 1 5 1 7l-1 1c0-4-2-8-3-11 0-2-1-3-2-4 0 2 2 6 3 9 0 3 1 6 1 8-2-3-2-8-4-12h0c-1-2-2-5-3-8-2-3-3-7-5-10l-5-5c0-1-1-2-1-3h0z" class="F"></path><path d="M365 369c0-1 1-1 1-1l1 1h1v-2l1 1c0 1 2 3 1 4l-1 1-1-2h-1c0 2 4 5 5 7 2 3 4 6 5 9s1 5 2 8h0c-1-2-2-5-3-8-2-3-3-7-5-10l-5-5c0-1-1-2-1-3h0z" class="V"></path><path d="M369 368c1 0 1 1 2 2l3 3h0c-1-2-1-2-1-4 2 2 4 4 5 6s2 3 3 5c0 1 1 1 1 2 1 1 1 2 2 3 2 4 3 8 4 13 1 1 1 4 1 6l-2-2c0 2 0 3-1 4v2h-1c0-1-1-2-1-3l1-1c0-2-1-5-1-7-2-7-6-13-10-19h0c-1-2-3-3-5-5l1-1c1-1-1-3-1-4z" class="G"></path><path d="M369 368c1 0 1 1 2 2l3 3h0c1 0 1 1 2 2 1 2 2 3 3 5l-1 1-3-5h-1l1 2h-1 0c-1-2-3-3-5-5l1-1c1-1-1-3-1-4z" class="U"></path><path d="M369 368c1 0 1 1 2 2v1c0 2 2 3 2 4l1 1 1 2h-1 0c-1-2-3-3-5-5l1-1c1-1-1-3-1-4z" class="e"></path><path d="M375 378c3 4 6 9 8 14 1 2 2 5 3 7h0c0-4-2-7-3-11 1 1 2 3 3 4 0 2 0 5 2 6 1 1 1 4 1 6l-2-2c0 2 0 3-1 4v2h-1c0-1-1-2-1-3l1-1c0-2-1-5-1-7-2-7-6-13-10-19h1z" class="O"></path><path d="M343 345h-1v-1l-1-1c0-1 0-2 1-2h1 0c0 1 0 1 1 1v-1c1 0 2 1 2 1 1 1 1 2 2 4h-1c1 1 2 2 2 3 1 2 1 3 3 5h0c1 1 2 3 2 4h-1c0-1-1-2-2-3h0v2c0 1 1 2 1 2l-1 1v1c1 0 1 1 1 2v3 2l-1 1v2c-1-1-1-2-1-3h-1c0 2 0 2 1 4h0v1c0 1-1 1-1 1 1 3 2 6 4 8l6 14v2h1c1 1 1 2 1 3s0 1 1 2v1l3 3h0l1-1 1 1c1 1 1 2 1 3h0l1 1c-2 1-2 0-3 0v1l-1-1h-2s-1-1-1-2l-1 1c0 2 0 4-1 6h0l-4 5c-2 2-5 4-6 7v1h0v4h-1v-6c-1 1-1 1-1 2h-1v-2c-1 0-1 0-1 1-1-1-1-1-1-2h-1v6l-1-1c-1-2 1-4-1-5 0 0-1 1-1 2v3 1c-1-2 0-3-1-5-1 2-1 6-1 7h0v-4h-1l1 4h-1c0-2 1-6 0-8v7h-1v-5-2h0v-1c1-1 1-2 2-3l1 1h1c0-1 1-2 2-3v-2h-2v-1-3l-1 1c0 1 0 2-1 3 0 2-1 3-2 4v-5-5h0v-1c-1-1-1-6 0-7 0-3 1-6 1-9 1-3 4-7 4-11-1 1-1 1-1 2-1 0-1 0-1-1v-1c-1 0-1 1-2 1h0v-2c0-1 0-1 1-1v-3l-1 1h0l-1-1h1v-2-1-1l-1-3v-4h-1c0-1 0-2 1-3 0-1 1-2 1-4-1 1-1 1-2 1 0-2 1-2 1-4l-1-1v-5c1-1 0-2 0-3l2-2v-1-1s1 0 1-1l1-1 1 1v2l2-1z" class="b"></path><path d="M352 387c-1-1-2-2-2-3-1-1-1-2-2-4v-4c-1-1-2-3-2-4h1c1 2 2 5 3 7v4c1 1 2 2 2 4z" class="o"></path><path d="M351 355l-1-2h-2v-1h-1c-1-1-1-2-1-4h1v2h1v-1c0-1-1-2-1-3 1 1 2 2 2 3 1 2 1 3 3 5h0c1 1 2 3 2 4h-1c0-1-1-2-2-3h0z" class="V"></path><path d="M337 404v4c2-2 1-7 2-9 1-5 2-10 5-15-1 5-3 9-4 13l-1 9c0 1 0 2-1 3v2 3 1c0 1 0 1-1 2v-5h0v-1c-1-1-1-6 0-7z" class="K"></path><g class="V"><path d="M341 373h0c1 0 1-1 1-1l1-1v1 1c-1 1-1 2-1 3h0c1-1 1-1 1-2h1c0 1 0 2 1 3v2h0v2h-1v1h-1s0-1-1-2h-1l-2-3c1-1 1-3 2-4z"></path><path d="M342 364c2-1 2-2 3-4v-2l1 1c0 1-3 6-3 6l1 1c0-1 1-2 1-3h1c0 1-2 5-3 6l-1 1-1 1h0l-1 1v2l-1-1-1-2 1-1c0 1 0 1 1 1-2-3 0-3 0-5 0-1 1-2 2-2z"></path></g><path d="M341 408s0 1 1 2c0-3 0-6 1-8v-4c1-4 2-9 4-12h1c0 1-1 3-2 4-1 2-1 4-1 6-1 3-1 6-2 9v2l1 1v6h-2v1 2h-1v-3s-1-1-1-2 1-3 1-4z" class="O"></path><path d="M340 406c0-2 1-3 1-5 1-5 2-11 4-16l1-1v-1h1c0 1-1 2-1 3-1 2-2 3-2 5-1 2-1 4-2 6l-1 11c0 1-1 3-1 4s1 2 1 2l-1 1c0 1 0 2-1 3 0 2-1 3-2 4v-5c1-1 1-1 1-2v-1-3-2c1-1 1-2 1-3h1z" class="J"></path><path d="M339 406h1c0 2-1 4-1 6 0 1 0 2-1 3v-1-3-2c1-1 1-2 1-3z" class="O"></path><path d="M337 365c1-1 4-4 4-6v-1h0c1-1 1-3 2-3v1c-1 2-1 3-2 4h0c0 1 0 1 1 2v-1l1-3 1-1h0c0-1 0-2 1-2l-3 9c-1 0-2 1-2 2 0 2-2 2 0 5-1 0-1 0-1-1l-1 1 1 2 1 1v-2l1-1v2c-1 1-1 3-2 4l2 3c0 1 1 3 1 4-1 1-1 1-1 2-1 0-1 0-1-1v-1c-1 0-1 1-2 1h0v-2c0-1 0-1 1-1v-3l-1 1h0l-1-1h1v-2-1-1l-1-3v-4h-1c0-1 0-2 1-3z" class="j"></path><path d="M340 343l1 1v2l2-1c0 1 1 2 1 3 0 0 0 1-1 1v1h1 1c0 1 1 2 1 3 0 0 0 1-1 2-1 0-1 1-1 2h0l-1 1-1 3v1c-1-1-1-1-1-2h0c1-1 1-2 2-4v-1c-1 0-1 2-2 3h0v1c0 2-3 5-4 6 0-1 1-2 1-4-1 1-1 1-2 1 0-2 1-2 1-4l-1-1v-5c1-1 0-2 0-3l2-2v-1-1s1 0 1-1l1-1z" class="V"></path><path d="M341 344v2l1 1v1c-1 0-1-1-2-1l-1 1h0c0-1 1-3 2-4z" class="j"></path><path d="M340 343l1 1c-1 1-2 3-2 4h0c0 1 0 1-1 2-1 0-1 1-1 2v1 2 1c1 0 1 0 2-1l1-2h0c-1 2-1 4-2 5h-1l-1-1v-5c1-1 0-2 0-3l2-2v-1-1s1 0 1-1l1-1z" class="Y"></path><path d="M346 399h0c1-2 1-4 2-6l1-4s0-1 1-2c0 1 0 3-1 4-1 3-1 5-1 8l1 1c-1 1-1 2-1 4-2 3 0 7 0 10h-1v6c0 1-1 3 0 4h1c-1 1-1 1-2 1h0l1 1h0 1s0 1 1 1h0c-1 1-1 1-1 2h-1v-2c-1 0-1 0-1 1-1-1-1-1-1-2h-1v6l-1-1c-1-2 1-4-1-5 0 0-1 1-1 2v3 1c-1-2 0-3-1-5-1 2-1 6-1 7h0v-4h-1l1 4h-1c0-2 1-6 0-8v7h-1v-5-2h0v-1c1-1 1-2 2-3l1 1h1c0-1 1-2 2-3v-2h-2v-1h1v-2-1h2v-6-2c1-2 1-2 1-4 1-1 1-2 1-3h0 0z" class="H"></path><path d="M348 399l1 1c-1 1-1 2-1 4-2 3 0 7 0 10h-1-1v6c-1-2-1-5-1-8h1c1-1 0-5 1-7 0-2 1-4 1-6z" class="O"></path><path d="M346 399h0c1-2 1-4 2-6l1-4s0-1 1-2c0 1 0 3-1 4-1 3-1 5-1 8 0 2-1 4-1 6-1 2 0 6-1 7h-1v-7c1-2 1-4 1-6z" class="d"></path><path d="M347 372l-1-1 1-1c1 2 1 3 2 4 1 3 2 6 4 8l6 14v2h1c1 1 1 2 1 3s0 1 1 2v1l3 3h0l1-1 1 1c1 1 1 2 1 3h0l1 1c-2 1-2 0-3 0v1l-1-1h-2s-1-1-1-2l-1 1c0 2 0 4-1 6h0l-4 5c-2 2-5 4-6 7v1h0v4h-1v-6h0c-1 0-1-1-1-1h-1 0l-1-1h0c1 0 1 0 2-1h-1c-1-1 0-3 0-4v-6h1c0-3-2-7 0-10 0-2 0-3 1-4 0-2 0-4 1-6 0-2 1-4 1-6l1-1c0-2-1-3-2-4v-4c-1-2-2-5-3-7z" class="U"></path><path d="M354 405c1 1 0 4 1 5h1c0 1 0 2 1 3h0 0v2h-1l-3-3v4c-1-4 1-7 1-11z" class="l"></path><path d="M348 414h1c0-3-1-6-1-9h1v5c1 2 1 4 0 6l1 1c1-3 1-6 0-9v-3h1v2c0 1 1 1 1 2 1 3-2 7 0 10h0l1-3v-4l3 3h1c-1 1-2 4-3 4 0 1-1 1-1 1l-1 1h0-1c0 1 0 1-1 2h0c-1 0-1 0-1 1h-1-1c-1-1 0-3 0-4v-6h1z" class="V"></path><path d="M347 372l-1-1 1-1c1 2 1 3 2 4 1 3 2 6 4 8l6 14h-2v1h0v1c0 2 1 5 1 8 0 2 0 4-1 7h0 0c-1-1-1-2-1-3h-1c-1-1 0-4-1-5l1-2 1-2v-3h-1v-2h-1c-1 2-2 5-2 8h-1c0-2 1-4 1-5 1-1 1-2 1-4h0c-2 1-3 7-3 9h0-1v-1 1h-1c0-2 0-3 1-4 0-2 0-4 1-6 0-2 1-4 1-6l1-1c0-2-1-3-2-4v-4c-1-2-2-5-3-7z" class="W"></path><path d="M351 388c1 1 1 2 0 3v3c1 1-2 7-2 9v1h-1c0-2 0-3 1-4 0-2 0-4 1-6 0-2 1-4 1-6z" class="d"></path><path d="M357 397h0v-1h2v2h1c1 1 1 2 1 3s0 1 1 2v1l3 3h0l1-1 1 1c1 1 1 2 1 3h0l1 1c-2 1-2 0-3 0v1l-1-1h-2s-1-1-1-2l-1 1c0 2 0 4-1 6h0l-4 5c-2 2-5 4-6 7v1h0v4h-1v-6h0c-1 0-1-1-1-1h-1 0l-1-1h0c1 0 1 0 2-1h1c0-1 0-1 1-1h0c1-1 1-1 1-2h1 0l1-1s1 0 1-1c1 0 2-3 3-4v-2c1-3 1-5 1-7 0-3-1-6-1-8v-1z" class="B"></path><path d="M357 397h0v-1h2v2h1c1 1 1 2 1 3s0 1 1 2v1l3 3h0l1-1 1 1c1 1 1 2 1 3h0l1 1c-2 1-2 0-3 0v1l-1-1h0l-2-3c-1-2-3-4-4-7 0-1-1-3-2-4z" class="m"></path><path d="M363 408c2 0 2 0 4 1h0v-1-1c1 1 1 2 1 3h0l1 1c-2 1-2 0-3 0v1l-1-1h0l-2-3z" class="J"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3v1l-2 1c1 1 1 2 2 3s1 2 2 3l1 1 2 3c0 1 1 2 1 2l3 5c2 1 3 2 4 4v-1l2 1h0 2c1 1 1 1 2 1v-1c-1-4-2-8-4-12l-1-2v-2l1-1c-1-1-1-1-1-3l1 1h1c8 9 16 17 23 27l7 10c10 15 19 30 23 48 1 5 2 11 3 17 0 6 1 11 2 17 2 7 4 13 5 20 1 8 1 16 0 24-1 6 0 12-1 18-1 4-2 7-3 11l-6 20v4c1 4 2 6 3 10 2 1 3 3 4 5l4 3-1 2 1 1c0 1 1 2 1 2v1c0 1 0 0 1 1 0 0 0 1 1 1l1 1h0c3 2 5 3 9 4v-1c1 0 3 0 4-1h-2v-1l1-1h4c2 0 5 0 6-1l-1-1c2 0 1 0 2-1h4c1 0 1 0 2-1l1 1h0 0c0 1 0 1 1 1h3 0c1 1 2 1 3 1 3 0 7-3 10-4 2-1 5-2 6-4 4-2 7-4 10-6 2-3 4-5 6-7 1-2 2-4 4-4 0 1-2 3-3 4v2-1 1 1h0 1l10-15c1-2 2-5 4-5 1-1 2-2 3-2 0-2 1-4 1-5 1 0 1 0 1 1 1-2 1-3 2-5l3-15v-3-5h-1c0-3 1-10-1-13h0c0-2-1-3-1-5v-1s0-1-1-2c1-1 1-2 1-3l-1-4h0c0 1-1 2-1 3-1-1-1-2-2-2h-1c0 1 0 1-1 0l-1 1c-1-2-1-3-1-5l-3-6-3-8v-2l1 1c0 1 0 1 1 2h0c-1-1-1-1-1-3h1 1 0c1-1 0-2 0-3h0l1-2 2 5v-3-1h0c1 1 1 2 3 2h1v-1c-2-3-4-5-6-7l-1-2h0l-1-1 1-1h2l-1-1h0c1 0 0-1 1-1l-3-3c-2-1-3-2-6-3v-1h0v-1h0c-1 0-2-1-2-2 1 0 1 1 1 1h1 0c1 1 2 1 2 2h1v-1h0 1 0l2 2h3c15 12 23 31 24 50v1c1 3 1 6 1 9 0 15-1 28-5 42-2 8-5 16-9 23-3 7-7 15-13 20-5 4-10 9-16 13-15 10-33 14-50 14-16-1-33-5-44-17l-5-7c-8 6-16 11-25 13-1 1-3 2-5 1h-1v-6h-3c-18 1-35-9-48-20v-1c7 6 15 12 24 16 5 1 10 3 16 4 2 0 10 1 11 0v-3c-1-1-2 0-3-1l-1-1c-2 1-4 0-6 0-7-1-12-3-18-6-2-1-4-4-6-5v-2l1-1h-1v-1h0c1-1 2-1 2-2h-1c0-1 0-2-1-3l-1-1h0l-2-1v-1l-1-1h-4c-2-1-4-2-5-2-3-1-4-1-7 0l-4-5-1-1c-15-15-16-37-16-58v-30 1 1h2c1-1 2-3 3-5 2-3 4-5 7-8v2l2-1 1-1c1-2 2-2 3-3l11-6 2-1v-1h0c1-1 2-1 2-1 1-1 0-1 1-1s2 0 3-1h0 1c0-1 0-1 1-1l-1-1c-3 1-5 3-8 4h-1c-1 0-1 0-2 1h0c-1 0-2 1-3 1 1-2 4-3 5-5l1-1 1-1c1-1 3-2 4-4 1-1 2-2 3-4l1-2 2 1 1-1 2-2-1-2 1-2c1-1 1-2 1-3l-1 1h0c-1 0-1 1-2 1v-1-1c1-1 1-2 1-3v2c1-1 1-1 1-2s0-5-1-6h-1v-1-2-4l1-4v-3c2-4 2-8 5-12v1 2h1v-2h1c0-1 0-1 1-2v1l1-1v1h0c1 1 1 1 1 2h0 1c1 1 1 2 1 3 0 2 1 5 2 6l1-1h-1c0-2 0-3-1-4 0-2 0-3-1-4h0l1-2 1 3h0v-2c-1-1-1-2-1-3-1-2 2-6 3-9h0c1-1 1-2 2-3 0-1 0-2-1-2 0-2 0-5-1-6-1-5-2-9-4-13-1-1-1-2-2-3 0-1-1-1-1-2 0-3-4-7-5-10h0l1 1h1c0-3 1-4 3-6h0z" class="f"></path><path d="M410 585h1c0-1 1-1 1-2l2 1-1 1h-3z" class="L"></path><path d="M409 579l1-1c1 2 0 2 0 3s-1 1-1 1l-1-1c0-1 0-2 1-2h0z" class="I"></path><path d="M369 599c1 1 2 1 3 1 1 1 2 1 4 1h0-2v1h0l-2-1s-1 0-1 1h-1l-1-1v-2z" class="L"></path><path d="M385 559l1 2c0 1-1 1-2 2h-2l2-2c0-1 1-1 1-2z" class="H"></path><path d="M557 578h1c0-1 1-2 2-2l-2 4-2 2c0-2 1-3 1-4z" class="J"></path><path d="M461 602l4 3-1 2-3-3v-2z" class="O"></path><path d="M471 634h1 2 1c1 1 2 1 3 2h-1l-4-1h-3 1v-1z" class="h"></path><path d="M402 583c1-1 1 0 3 0-1 1-1 2-2 3l-1 1-1 1v-1h-1l-1 1h0v-1c2-1 2-2 3-4z" class="J"></path><path d="M403 595h-1 0-1 0v-1h-1l1-1 1-1h1l-1-2v-1h3c0 1-1 2-1 2l-1 1v2 1z" class="P"></path><path d="M378 555v1 1h0c0 1 0 2-1 2l-3 5v-2c0-1 1-2 2-4 0-1 1-2 2-3z" class="B"></path><path d="M474 623h1c1 0 1-1 1-1 1-1 1 0 1-1h0v-1c2 2 5 2 8 2v1c-3 1-6 1-9 0h-2z" class="I"></path><path d="M395 565c1 0 1 1 2 2 1 0 1 1 1 1l-2 3c-1-1-2-1-2-1v-1c-1 1-1 1-2 1 0-1 1-1 1-2h0v-2l2-1z" class="L"></path><path d="M400 579c2-1 6 0 9 0h0c-1 0-1 1-1 2l1 1-4 1c-2 0-2-1-3 0v-1h2c0-1-2-1-3-2 1 0 2 0 3-1h-4z" class="k"></path><path d="M346 560v2c1 0 1-3 1-4 1 3 0 4 0 7 0 2 1 4 1 6v-1c0-1 0-1-1-1v-1 2h-1 0-1v-3h0v-1h0l-1-1h1v-2c0 1 0 2 1 3h0v-6z" class="B"></path><path d="M440 546l1-2v3c0 4 0 8-1 12h-1c-1-1 0-3-1-5 1 0 1-1 1-2s0-1 1-2v-4z" class="J"></path><path d="M347 558v-1l1 1v5c0 1 1 2 1 4 0 3 1 7 3 10v1h-1l-1-2c0-1-1-1-1-2v-1s-1-1-1-2c0-2-1-4-1-6 0-3 1-4 0-7z" class="D"></path><path d="M563 506h1l2 1v1c0 1 1 2 1 3h0c0 1 1 2 1 3v4c0 1 0 4-1 6v-5l-1-3c0-2 0-5-1-7h-1l-1-3z" class="P"></path><path d="M405 564l3-3v-1c0 1 0 1 1 2 0-1 1-2 1-3s0-2 1-3c0 1-1 3 0 4l-4 6c0 1-1 3-3 3h-2l1-1c1-1 2-2 2-4zm12-21l1 1v-1c0-1 1-1 1-2h0l1-1h0c0-1 1-1 1-2 0 0 0-1-1-1 0-1-1 0-2 0v-1h2 0 1c1-1 1 0 2 0s1 1 1 2h-1c-2 1-3 4-4 6h-1v1c0 1 0 1-1 1v1c-1 0-2-1-2-2h1 1v-1-1z" class="D"></path><path d="M362 568h1c1 2 2 3 3 5l1 1h1v-1h0c1 1 1 1 2 1-1 1-2 2-4 2v1h0c0 1 0 1-1 2v-1h0v-1h0c0-1-1-1-1-1v-1s0-1-1-2l-1-4v-1h0z" class="L"></path><path d="M418 608h1c-4 3-8 7-13 9h-1-1c2-1 4-4 5-4 2-1 4-2 5-2v-1h1c1-1 2-1 3-2z" class="g"></path><path d="M523 620h1v1h0l1-1h1l-1 1c0 1 0 0-1 1h-1c0 1 0 1 1 0v1h2v-1c1 0 2-2 3-2-1 2-4 3-6 4h0c-1 1-4 2-5 1 1 0 3-1 3-2h-1c-1 1-2 1-3 1h-1 0c0-1 2-2 3-2l2-1c1 0 1 0 2-1z" class="L"></path><path d="M402 589c1-1 2-1 3-1h0c1 0 1 0 2 1v-1c1 1 1 1 2 1h0c0-1 1-1 1-1h1 1v2l-1-1s-1 0-2 1l-1 1c1 0 2 1 3 0h0c0 1 1 1 2 1h-1-2c-1 0-2 1-3 0h-1-1-1-1l1-1s1-1 1-2h-3z" class="I"></path><path d="M435 569c-1 1-1 2-2 3h-1c-1 0 2-4 2-5 3-6 4-13 4-20h1c0 3-1 7-1 9-1 1 0 2-1 2v2 1l1-1v1h0 1v-2h1c-1 2-1 6-2 7l-1-1c1-1 1-2 1-3h0c-1 1-3 5-3 7z" class="D"></path><path d="M370 602h1l3 3c0 1 0 1-1 2v2h-2c1 1 3 4 4 5h1 0v1 1c-2-1-4-4-6-5v-2l1-1h-1v-1h0c1-1 2-1 2-2h-1c0-1 0-2-1-3z" class="H"></path><path d="M338 549l1-1h0v8l1 1h0v1c0 3-1 5 1 7v3 1 1h0c-1 0-1-3-1-4-1-1-1-3-1-4-1-1-2-2-2-3h0c1-1 1-3 1-4v-6z" class="B"></path><path d="M338 549l1-1h0v8l1 1h0v1l-1 3h0v-7l-1-1v2h0v-6z" class="J"></path><path d="M554 595h2c-2 2-3 4-4 6-1 1-2 2-2 3h0l-3 3-1-1c-1 1-1 1-2 0h1c1-1 1-1 2-1v-2c1-2 2-2 3-4l2-3h0 0l2-1z" class="H"></path><path d="M448 539v-1h1v4 5 1c1 3 0 8-1 12-1-2 0-3-1-5v-2-1l1-1c0-1 0-4-1-6v-2-4h1z" class="B"></path><path d="M448 539v-1h1v4 5c-1-1-1-5-1-8z" class="K"></path><path d="M403 595l1-1c1 0 1-1 2-1v1h1l-3 3h1c1 1 1 1 1 2l1-1h1l-1 1c-1 0 0 0-1 1l-3 2h0c0-1 1-1 1-2h0c1-1 1-1 1-2h-2v1c-1-1-1-1-2-1l-1 1c-1 0-2 1-2 2-1 0-1 0-2 1v-1h0v-1l1-2h1v1c1 0 1-1 1-1 1-1 2-1 2-1 1 0 1-1 1-1l1-1z" class="H"></path><path d="M409 540c1 1 1 2 1 3l-1 1c1 1 1 0 2 0h1 1 1 1 0 1c1-1 0-2 1-3v-1s0-1 1-1l-1-1v-1c1 0 1 1 1 1 1 1 0 2 0 3s0 1-1 2v1 1h-1-1l-1 1v-1h-3c-2 0-3 0-4 1v1h-1c0-1-1-1-1-2h0l-1-1s1 0 1-1h2c1-1 2-2 2-3zm-57 26c-1 1-1 3 0 3v1 1 1h0-1l-1-3v-2-2c-1-2-2-6-1-9 0-1 0-1 1-1s0 4 1 6c0-2-1-3 0-4 0 3 1 7 1 9z" class="B"></path><path d="M400 579h4c-1 1-2 1-3 1 1 1 3 1 3 2h-2-1c-1 0-2-1-3 0h0c0 1 0 1-1 1s-3 0-4 1-1 1-2 1v-1-2l9-3z" class="o"></path><path d="M449 522v-6h1c2 1 2 2 3 4h-1c-2 3 0 6-1 9v-2c0 2 0 4-1 7v1-1l-1 1v-4c0-3 1-6 0-9z" class="K"></path><path d="M450 516c2 1 2 2 3 4h-1c-2 3 0 6-1 9v-2c0 2 0 4-1 7v1-1c0-6 1-12 0-18z" class="h"></path><path d="M396 563h1 0 2c0 1 1 1 1 2h0l-1 2c1 0 1 0 1-1l3-3s0 1 1 1c0 1 0 2-1 3v1l-1 1c0 1 0 1-1 1l2-4c-3 2-5 3-7 5h0l2-3s0-1-1-1c-1-1-1-2-2-2v-1l1-1h0z" class="I"></path><path d="M396 563c2 1 2 2 3 3l-1 2s0-1-1-1c-1-1-1-2-2-2v-1l1-1z" class="h"></path><path d="M552 480c1-1 2-1 3-1 0-1 0-1-1-2s-2-1-4-1v-1l1 1h2c1 0 1 1 2 1l1 1h0v1c1 1 0 1 0 1h1l1 1c-1 0-1 0-1 1h0l1 1c1 0 1 0 2 2h1 0 0c-1 0-2-1-3-1l-1 1c-1-1-2-1-3-1-2-1-2-2-4-2h0l-1-1 1-1h2z" class="L"></path><path d="M441 489v-1c1 1 1 3 1 4v4h1l2 1c0 1 1 3 0 4-1 0-1 1-2 0h0-1v-3h0v4h-1v-2c0-2 0-4-2-6v-2h1v-3h1z" class="n"></path><path d="M440 489h1v8 3c0-2 0-4-2-6v-2h1v-3z" class="L"></path><path d="M346 591h2c6 3 13 3 17 7 1 0 3 1 4 1v2h0l-2-1v-1l-1-1h-4c-2-1-4-2-5-2-3-1-4-1-7 0l-4-5z" class="D"></path><path d="M561 508l1-2h1l1 3h1c1 2 1 5 1 7l1 3c0 1 0 2-1 3 0-2-1-4-2-5l-1-3-1-1h0v-2c0-1-1-2-1-3z" class="l"></path><path d="M564 517v-2-3h1l1 4 1 3c0 1 0 2-1 3 0-2-1-4-2-5z" class="e"></path><path d="M451 529c1-3-1-6 1-9h1v3h0v2c0 2 0 4 1 5 0 2 0 5-1 6v2-1h-1v2c1 1 1 2 1 3h-1c0-1 0-4-1-5v-8z" class="J"></path><path d="M395 542l1-1h1c1 1 1 1 2 1 2 0 4 2 5 2l1 1h0c0 1 1 1 1 2h1v-1l1 1h1 0l1-1c1 0 1 0 2 1h4v1h0v1 1c-1 0 0 0-1 1h0c-1 1-1 2-2 3 0 1 0 3-1 4v-1c0-1 0-2 1-3v-1c0-1 1-3 2-4l1-1h-1-1-3c0 2 0 2 1 3v1c-1 0-1-1-2-2 0-1-1-1-1-2h0-2-2 0l-2-2-2 2c0-2 0-2 1-3l-1-1h-2 0-1c-1-1-2-1-3-2z" class="P"></path><path d="M558 580c-1 6-5 12-8 17-1 1-2 2-3 4l-1 1h0l-1-1-6 6h0c1-2 2-3 3-4 2-2 4-4 5-6 4-4 7-10 9-15l2-2z" class="D"></path><path d="M497 632h3c1-1 2-1 4-1v1c-3 0-5 1-8 1-2 1-6 2-9 1h-4 0 0c2 0 3 0 4 1h-1c-3 0-6 0-9-1h-3c0-1 1-1 1-1v-1c1 0 2 1 2 1h1v-1h-3-2c1-1 3-1 4 0h0 2v1h0 1 0 0 3c0-1 0 0-1-1 4 1 7 1 10 0h5z" class="H"></path><path d="M423 555h1v-1-1 1-1l1-1v-2h0c1 0 0 5 0 5v1l1-1v2h1v4c-1 0-1 0-1 1s-1 3-2 5l-1 2c-1 3-3 6-5 8 1-2 2-4 3-7 1-1 1-3 1-4l1-1v-2h0c1-1 1-1 1-2v-1-1 2l-1-1v-5z" class="k"></path><path d="M417 582l2-1h1s1 0 1-1v1 1h1c1 0 1 0 1-1v1h1l1 1h3 0v1 1h0c-1 1-2 2-2 4h-1c0 1-1 2-2 3l-1 1v2l-1 1v-2s-1 0 0-1 2-2 2-3c1-1 2-2 2-3h1c0-1 1-2 1-3h-1c-1 0-1 0-2-1h0v-1l-1 1h0c-1 0-2 0-3-1-1 1-1 1-1 2l-1 1v2l1-1v1c0 1-1 1-2 2v-1h1l-1-1v-1h-1c-1 0 1 0-1 0h-1-1-1l1 1v1c-1 0-1-1-2-2h0-2c-1-1-1-1-2-1l1-1 2 1h0 3l1-1c1-2 1-1 3-2z" class="D"></path><path d="M414 584c1-2 1-1 3-2-1 1-1 2-2 3h-2l1-1z" class="B"></path><path d="M441 460c0 1 0 2 1 4 0 1 1 3 1 4v1 2 1 1c1 0 1 1 1 1l1 5v4c1 4 1 8 1 12 0 3 2 6 1 9h-1c0-1-1-2-1-3 1-1 0-3 0-4-1-3-1-7-1-10v-1c0-4-1-8-1-12l-1-1v-1-3l-1-1v-4-1c-1 0-1-1-1-1v-2h1z" class="H"></path><path d="M447 514c0-1 0-1 1-2 0 3 0 8 1 10 1 3 0 6 0 9v4l1-1v1 3h-1v4h0v-4h-1v1h-1v-1h-1c0-1 1-3 1-4v-20h0z" class="J"></path><path d="M447 514s1 0 1 1v4 10 4l-1 1h0v-20z" class="j"></path><path d="M567 544c1 1 1 2 2 2 0 1-1 2-1 2v7c-1 5-3 10-5 15l-3 6c-1 0-2 1-2 2h-1v-1l2-1v-1-1s1 0 1-1c0-3 2-5 3-8l1-3 1-5 2-13z" class="K"></path><path d="M433 441c1 1 2 3 2 4 1 1 1 2 1 3l1 5h1v3l1 1v2c1 0 1 1 1 1v2s0 1 1 1v1 4l1 1v3 1l1 1c0 4 1 8 1 12v1c0 3 0 7 1 10l-2-1h-1v-4l1-1v-8c-1-1 0-3-1-4v-3-1l-1-4v-2c-1-1-1-1-1-2h0v-2h0c-1-4-2-8-3-11-1-2-1-4-1-6l-3-7z" class="L"></path><path d="M443 496c2-2-1-7 1-9 0 3 0 7 1 10l-2-1z" class="f"></path><path d="M550 617c1-5 4-8 6-12 3-5 6-11 8-17l1 1h0c-1 2-1 4-2 6v1 1h0c-3 7-7 15-13 20z" class="B"></path><path d="M552 472c15 12 23 31 24 50v1c-2-2-1-4-2-6v-2h0c-2-1-1-2-2-4v-1-1-1h-1v-3h0c0-1-1-3-1-3v-1-1h1l-1-1h0c0-2-1-3-1-4-1 0-1 0 0-1-1 0-1-1-2-2l-1-1v-1h0v-1h-1v-1l-1-1-1-1c0-1-1-1-1-2 0 0 0-1-1-1 0-1-1-1-2-2l-1-2c-1-1-1-1-1-2h-1c0-1-2-3-3-4-1 0-1 0-2-1h-2 3z" class="I"></path><path d="M485 622c2 1 5 1 8 2 0 0 0 1 1 2 3 0 6 1 9 1h1c-2 2-5 1-7 1v2h-1v-1-1h-1c1-1 3-1 4 0h0v-1h-3-1 0c-2-1-4 0-5-1h-5c-4-1-8-1-12-1v2 1c-1 0-1 1-1 2-1 0-1 1-2 0h-1-1c1-1 3 0 4-1h0v-3c-1 0-1 0-1 1l-2-1h-1l1-1v1h3v-1l-1-1v-1h-1 0 3 1 2c3 1 6 1 9 0v-1z" class="B"></path><path d="M358 534c1 1 1 2 2 3h0c0 1 1 1 2 1v3l-1 2c0-1-1-2-1-2-1-1-2-2-2-3h0c0 1-1 1-1 2 0 2-2 3-3 5h-1c-1 2-2 4-3 7 0 0-1 0-1-1l-1 1v1h-1-1c-1 1 0 2 0 3v3 1 6h0c-1-1-1-2-1-3l-1-10h0 0 1v-5h0c1 0 1 2 2 3v-2-4h1v3h1v-2h2v-2-1l1 1h1v-2h0 0l1-1 2-2c2-1 2-3 2-5z" class="I"></path><path d="M348 548h1v-2h2v2h-1s-1 1-1 2l-1-1v-1z" class="K"></path><path d="M339 529c1 2 0 3 0 4 0 2-1 5-1 7 0 3 1 6 0 9v6c0 1 0 3-1 4h0c0-2 0-4-2-6h0c-1-4-1-9 0-13 0-3 1-5 1-8l2-1 1-2z" class="I"></path><path d="M336 532l2-1c0 1 0 8-1 8v1c-1 1-1 4-1 5h-1v-5c0-3 1-5 1-8z" class="K"></path><path d="M339 529c1 2 0 3 0 4 0 2-1 5-1 7 0 3 1 6 0 9v6h-1c-1-5 0-11 0-16 1 0 1-7 1-8l1-2z" class="m"></path><path d="M476 629l2-1h-1 2 3 2l1-1h2 1 1l1 1c2 0 5-1 5 0-2 1-5 0-8 0v1h5 3 0c-1 0-1 1-2 0l1 1h1 1 0c0 1 1 1 1 1h1 1-3-1 0-2 0 2c1 0 1 1 2 1h-5c-3 1-6 1-10 0 0-1-1 0-2-1-2 0-4-1-5-1h-1v-2h1l1 1z" class="B"></path><path d="M480 631c-2 0-4-1-5-1h-1v-2h1l1 1c5 1 9 1 14 1h3c-1 1-2 1-3 1-3 0-7-1-10 0z" class="J"></path><defs><linearGradient id="T" x1="558.204" y1="498.796" x2="562.837" y2="486.964" xlink:href="#B"><stop offset="0" stop-color="#1b1a1e"></stop><stop offset="1" stop-color="#31322f"></stop></linearGradient></defs><path fill="url(#T)" d="M550 482c2 0 2 1 4 2 1 0 2 0 3 1l1-1c1 0 2 1 3 1h0c0 1 1 2 2 3h-1 0 0c-1-1-2-3-3-3h-1c1 0 1 0 1 1v1h1c0 1 0 1 1 2 0 1 0 1 1 2v2c1 0 1 1 2 1 0 1 1 3 2 4v1 1c1 1 0 2 1 2v1c-1 1 0 3-1 5v-1l-2-1h-1l-1-2h0c-1-2-2-4-2-6-1-2-2-5-3-7-2-3-4-5-6-7l-1-2z"></path><path d="M564 506v-2c-1 0-1 0-1-1h0v-3-1c1 1 3 4 3 5v3l-2-1z" class="f"></path><path d="M551 484c6 2 8 7 10 12 1 3 2 5 1 8h0c-1-2-2-4-2-6-1-2-2-5-3-7-2-3-4-5-6-7z" class="K"></path><defs><linearGradient id="U" x1="331.736" y1="507.855" x2="340.205" y2="508.096" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#484949"></stop></linearGradient></defs><path fill="url(#U)" d="M341 491v2l2-1v1l2-1h1c-2 1-4 3-5 4h1c1-1 2-1 3-2-1 2-3 4-4 5h-1c-1 0-2 1-2 2h0-1l-1 1c1 1 1 1 1 2-1 1-1 1-1 2h0l1 1h0c-1 1-1 3-2 4l-1 3v1h0c1 2-1 2 1 4v4h0c-1 0-1-2-1-3v4c-1-1 0-3-1-3v4l1 2h0v2h-1v-2h0v-2s-1-2-1-3c-1 1-1 1-1 2v3h0v-18-5c1-1 2-3 3-5 2-3 4-5 7-8z"></path><path d="M335 511l-1-1v-1c1-1 1-2 2-3l1 1h0c-1 1-1 3-2 4z" class="k"></path><path d="M336 506h-2v-1l2-3c1 1 1 1 1 2-1 1-1 1-1 2z" class="Q"></path><path d="M343 492v1l2-1h1c-2 1-4 3-5 4h1c1-1 2-1 3-2-1 2-3 4-4 5h-1c-1 0-2 1-2 2h0-1c0-1 1-1 1-2-1 0-2 1-2 1v1-1c0-2 4-5 5-7l2-1z" class="b"></path><path d="M443 512v-1c1-2-1-6 1-8 1 1 0 9 0 11 1-1 1-8 2-8v3c1 1 2 2 2 3h0c-1 1-1 1-1 2h0v20c0 1-1 3-1 4h1l-1 1c-1-1-1-1-1-3h1v-2-5c0 2 0 3-1 4v3h0v2 3h0c0 2 1 4 0 6h1v1 2c1 2 0 4 0 6v1l-1 1-1-1c1-3 0-8-1-12v-2c0-4-1-7-1-11v-13-6l1-1z" class="D"></path><path d="M446 509c1 1 2 2 2 3h0c-1 1-1 1-1 2v2h-1v-7z" class="i"></path><path d="M444 519l1-2h0c1 4 1 11 0 15-1 0-1-1-1-2 0-4 1-8 0-11z" class="V"></path><path d="M443 512v2c0 1 0 5 1 6v-1c1 3 0 7 0 11 0 1 0 2 1 2 0 2-1 3-1 5v6h-1c0-4-1-7-1-11v-13-6l1-1z" class="k"></path><path d="M443 514c0 1 0 5 1 6v-1c1 3 0 7 0 11v3c-1-1-1-4-1-6v-13z" class="J"></path><path d="M341 529c0-2 0-5 1-7 0-2-1-3 0-4 0 1 0 2 1 3v1c0 3-2 6-1 8v1c1-1 1-2 2-2v2c1-1 1-2 1-3l2 1v1c-1 2-1 5-2 7l-1 7c0 1 0 2 1 3v-1 2 5h-1 0 0l-1-1h0l-1 1-1 6v-6-4c-1 3-1 6-1 8h0l-1-1v-8h0l-1 1c1-3 0-6 0-9 0-2 1-5 1-7 0-1 1-2 0-4l1-1v1h1z" class="K"></path><path d="M344 544h0c0 1 0 2 1 3v-1 2 5h-1 0 0l-1-1h0l-1 1c1-2 1-8 2-9z" class="B"></path><path d="M344 544h0c0 1 0 2 1 3v-1 2 5h-1 0v-9z" class="Y"></path><path d="M341 529c0-2 0-5 1-7 0-2-1-3 0-4 0 1 0 2 1 3v1c0 3-2 6-1 8v1c1-1 1-2 2-2v2c1-1 1-2 1-3l2 1v1c-1 2-1 5-2 7h-1v3c-1 0-1-2-1-3l-1-1v6h1l-1 1c-1-2-1-4-2-6h0v-2-1c0-2 1-3 1-4v-1z" class="Q"></path><defs><linearGradient id="V" x1="390.945" y1="595.024" x2="389.508" y2="583.134" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#V)" d="M397 583c1 0 1 0 1-1h0c1-1 2 0 3 0h1v1c-1 2-1 3-3 4v1h0l1 1-2 2v-1h-2-2-1c0 1 0 1-1 1l-1 1h-1v1h1c1 0 1 0 1 1h0 1c0 1 0 1 1 2h0-1c0-1 0-1-1-1 0-1 0-1-1-1h-1c-1 0-2 0-2 1-1 0-1 1-3 1 0 0-1 0-1-1h0c-1 1-2 1-2 2-1 0-1 1-2 1v2l-1-1v1h0c1-4 2-9 5-12 2-3 4-4 7-6v2 1c1 0 1 0 2-1s3-1 4-1z"></path><path d="M392 587h1v2 1l-2-1v-1l1-1z" class="O"></path><path d="M397 583c1 0 1 0 1-1h0c1-1 2 0 3 0v1c-1 1-2 1-3 1 0-1 0-1-1-1z" class="j"></path><path d="M562 513h0l1 1 1 3c1 1 2 3 2 5 1-1 1-2 1-3v5l1 11c0 3 0 7-1 9l-2 13-1 5-1 3c-1 0-1-1-1-1s0-2 1-2c0-3 3-11 2-13v-1l-1 1v-3-5h-1c0-3 1-10-1-13h0c0-2-1-3-1-5v-1s0-1-1-2c1-1 1-2 1-3 0-2 0-3 1-4z" class="Q"></path><path d="M563 514l1 3c1 1 2 3 2 5v2l-1-1c-1-2-2-6-2-9z" class="b"></path><path d="M567 519v5l1 11h-1c0-4-1-7-1-11v-2c1-1 1-2 1-3z" class="l"></path><path d="M562 513h0v5c1 2 1 5 2 8v15h-1c0-3 1-10-1-13h0c0-2-1-3-1-5v-1s0-1-1-2c1-1 1-2 1-3 0-2 0-3 1-4z" class="b"></path><path d="M383 537l2-1 1 1v2h0l2-1v1c0 1-1 1-2 2l2 2c2 0 3 0 4-1 0 1-1 2-1 3-1 1-2 3-3 4-2 4-4 8-5 11l1 1-2 2-2 1v-2l-4 4 4-8c0-1 1-2 1-3v-1h-1c0 1-1 2-2 3h0v-1-1c-1 1-2 2-2 3l-1-1 2-5 3-6h1l-1-1v1l-1-1 1-1h1 1 1c1-1 1-1 1-2h-1s0 1-1 1v-1h-1v1h-1c0-1 0-1-1-1 0-1 0-1-1-2l-1 1h0c1-1 1-2 1-3l1-1 1 1s-1 0-1 1c1 0 1-1 1-1h1c1-1 1-1 2-1z" class="O"></path><path d="M383 537l2-1 1 1v2h0l2-1v1c0 1-1 1-2 2l-1 1h0v-2c-2 1-4 0-6 0l4-3z" class="g"></path><path d="M383 547l1 1v1c-1 1-1 1-1 2h1v-1l1-2c1-1 1-2 2-2 0 1 0 2-1 3s-1 2-1 3h-1c-1 3-2 4-4 6 0-1 1-2 1-3v-1h-1c0 1-1 2-2 3h0v-1-1-1c0-1 1-3 3-4-1 1-1 2-1 3h0c2-2 3-4 3-6z" class="J"></path><path d="M383 544c-1 2-2 3-2 5 2-1 2-4 4-6v1c0 1-1 2-2 3 0 2-1 4-3 6h0c0-1 0-2 1-3-2 1-3 3-3 4v1c-1 1-2 2-2 3l-1-1 2-5 3-6h1l-1-1v1l-1-1 1-1h1 1 1z" class="V"></path><path d="M369 543s0-2 1-2c1-2 2-4 3-4h1l-1 1v1l1-1c1 0 1 1 2 1v1l1-1 1-1c0 1 0 2-1 3h0l1-1c1 1 1 1 1 2 1 0 1 0 1 1h1v-1h1v1c1 0 1-1 1-1h1c0 1 0 1-1 2h-1-1-1l-1 1 1 1v-1l1 1h-1l-3 6-2 5 1 1c-1 2-2 3-2 4l-4 6c-1-3-1-7-1-11 1 0 1-1 1-2l1-2h0-1c-1 0-1-1-2-1l-1 1v-3h-1c1-2 2-4 3-7z" class="Y"></path><path d="M373 538v1 1h0c1 0 1 1 2 2h0-1-1c0 1 0 2 1 2 1 1 2 2 2 3s0 1 1 1l-1 1v-1l-1 2-1 1-3-3c1-1 2-2 2-3s0-1-1-2v-2l-1 1v-1c1-1 1-2 2-3z" class="O"></path><path d="M369 543s0-2 1-2c1-2 2-4 3-4h1l-1 1c-1 1-1 2-2 3v1l1-1v2c1 1 1 1 1 2s-1 2-2 3v-1h-1v1l-1 1v-2c-1 1-1 2-2 3h-1c1-2 2-4 3-7z" class="j"></path><path d="M370 548v-1h1v1l3 3 1 2s0 1-1 1l-1 1c0 1-1 3-2 4v-3l-1-1 1-2h0-1c-1 0-1-1-2-1l-1 1v-3c1-1 1-2 2-3v2l1-1z" class="K"></path><path d="M367 550c1-1 1-2 2-3v2l1-1c0 1 1 1 1 2l1 1h-1l-1 1h-1-1l-1 1v-3z" class="k"></path><path d="M374 554c0 2-2 4-2 6h1c1-2 3-6 3-8 0 0-1 0-1-1 1 0 2 1 2 1l-2 5 1 1c-1 2-2 3-2 4l-4 6c-1-3-1-7-1-11 1 0 1-1 1-2l1 1v3c1-1 2-3 2-4l1-1z" class="J"></path><path d="M551 489l2 5v-3-1h0c1 1 1 2 3 2h1v-1c1 2 2 5 3 7 0 2 1 4 2 6h0l1 2h-1l-1 2c0 1 1 2 1 3v2c-1 1-1 2-1 4l-1-4h0c0 1-1 2-1 3-1-1-1-2-2-2h-1c0 1 0 1-1 0l-1 1c-1-2-1-3-1-5l-3-6-3-8v-2l1 1c0 1 0 1 1 2h0c-1-1-1-1-1-3h1 1 0c1-1 0-2 0-3h0l1-2z" class="Q"></path><path d="M560 513c0-1 0-2 1-3v1h1v2c-1 1-1 2-1 4l-1-4z" class="Y"></path><path d="M556 504h1c2 2 2 6 3 9 0 1-1 2-1 3-1-1-1-2-2-2h-1c0 1 0 1-1 0v-1l1-1 1-1v-6l-1-1z" class="D"></path><path d="M555 513l1-1c0 1 1 1 1 2h-1c0 1 0 1-1 0v-1z" class="I"></path><path d="M553 494v-3-1h0c1 1 1 2 3 2h1v-1c1 2 2 5 3 7 0 2 1 4 2 6h0l1 2h-1l-1 2h0c-1-1-1-3-1-4s-1-7-3-8c-1 1-1 1-2 1h-1c0-1-1-2-1-3z" class="o"></path><path d="M560 504v-2l2 2h0 0l1 2h-1l-1 2h0c-1-1-1-3-1-4z" class="m"></path><defs><linearGradient id="W" x1="548.374" y1="507.89" x2="554.182" y2="497.383" xlink:href="#B"><stop offset="0" stop-color="#39393b"></stop><stop offset="1" stop-color="#6b6968"></stop></linearGradient></defs><path fill="url(#W)" d="M551 489l2 5c0 1 1 2 1 3 1 2 1 6 2 7l1 1v6l-1 1-1 1v1l-1 1c-1-2-1-3-1-5l-3-6-3-8v-2l1 1c0 1 0 1 1 2h0c-1-1-1-1-1-3h1 1 0c1-1 0-2 0-3h0l1-2z"></path><path d="M553 510h0l1 1v-1l1 3v1l-1 1c-1-2-1-3-1-5z" class="k"></path><path d="M551 489l2 5c0 1 1 2 1 3 1 2 1 6 2 7l1 1c-1 1-1 1-1 2-1-1-1-3-2-4-1-3-3-6-4-9h0c1-1 0-2 0-3h0l1-2z" class="U"></path><path d="M355 516l1-1c1 1 1 1 1 2s0 2-1 3v4c1 1 1 2 1 3v1c0 1 1 3 1 4l-1 1c-1 1-1 3-1 4v2l-2 2-1 1h0 0v2h-1l-1-1v1 2h-2v2h-1v-3h-1v4 2c-1-1-1-3-2-3h0v-2 1c-1-1-1-2-1-3l1-7c1-2 1-5 2-7v-1-2h1v-1c1-1 1-1 3-1v-3c1-1 1-1 1-2h0c2-1 3-2 3-4z" class="m"></path><path d="M347 527h1v-1c1-1 1-1 3-1l-1 3-2 2c0-1 0-2-1-3h0z" class="O"></path><path d="M351 522c1 1 1 2 1 2 0 1-1 1-1 2v9h0s-1 0-1-1v-1c0-2-1-3 0-5l1-3v-3z" class="W"></path><path d="M346 541v-4c1 0 1 2 1 3h1v-3c0-1-1-1-1-2s0-3 1-4h0v4l1 1c-1 1-1 3-1 5 0 1-1 2-1 4h0v4 2c-1-1-1-3-2-3h0v-2c0-2 0-3 1-5z" class="Y"></path><path d="M346 541v1c1 1 0 7 1 7v2c-1-1-1-3-2-3h0v-2c0-2 0-3 1-5z" class="K"></path><path d="M347 545h0c0-2 1-3 1-4 0-2 0-4 1-5v5h1c1-1 0-3 0-5 2 2 1 5 3 6v2h-1l-1-1v1 2h-2v2h-1v-3h-1z" class="Q"></path><path d="M352 526l1-1v1 4l1 1h0l-1 2 1 1h1c0 2 0 2 1 3v2l-2 2-1 1h0l-2-7v-9h1z" class="V"></path><path d="M353 535v-2l1 1c0 2 0 3-1 5v-4z" class="b"></path><path d="M352 526l1-1v1 4l1 1h0l-1 2v2c-1-1-1-2-1-3h1c-1-2-1-4-1-6z" class="l"></path><path d="M355 516l1-1c1 1 1 1 1 2s0 2-1 3v4c1 1 1 2 1 3v1c0 1 1 3 1 4l-1 1c-1 1-1 3-1 4-1-1-1-1-1-3h-1l-1-1 1-2h0l-1-1v-4-1l-1 1h-1c0-1 1-1 1-2 0 0 0-1-1-2 1-1 1-1 1-2h0c2-1 3-2 3-4z" class="m"></path><path d="M357 528h0-1c0-1 0-3-1-4h0v-4h1v4h0c1 1 1 2 1 3v1z" class="W"></path><path d="M352 524l1 1v-5h1c1 1 0 3 0 4s1 1 1 1c0 2 0 4 1 6v1h-1v-1c0-1-1-1-1-1v-3l-1-1v-1l-1 1h-1c0-1 1-1 1-2z" class="b"></path><path d="M433 486v-1h1c1 2 0 6 0 8h1v-8h1c1 3 1 10 1 13l-1 18v4l-2 7c0 2 0 5-1 6h-1v-2h0v-2h1v-2h0l-1 2h-1c0-1 0-1-1-2h1v-1s0-1 1-1v-4c-1 1-1 2-1 3h0c-1 1-1 1-1 2v1s0 1-1 1l-1 1h0c0 1 0 1-1 1v1c-1 0-2 0-3 1h-1 0l1-1c-1 0-1 0 0-1h-1l1-1 1-1c1-1 2-2 3-4v-1-2c3-5 3-12 3-17l1-18h1z" class="D"></path><path d="M434 527v-1c0-3 0-5 1-7v-6l1 4v-1h0v4l-2 7z" class="O"></path><path d="M433 486v-1h1c1 2 0 6 0 8-1 4-1 8-1 13s-1 11-3 16c0-2 0-4 1-7l1-9v-10c0-3 1-6 1-10z" class="e"></path><path d="M435 493v-8h1c1 3 1 10 1 13l-1 18h0v1l-1-4c1-3 2-8 1-11l-2 10c0 3 0 9-1 11-1-2 1-13 1-16v-7c0-2 0-5 1-7z" class="b"></path><path d="M427 488v-6h1c0 2 0 5 1 7 0-2-1-4 0-6h1v2h1v-1l1 1v1l-1 18c0 5 0 12-3 17v2 1c-1 2-2 3-3 4l-1 1-1 1h-1c0 1-1 1-2 1 0-1-1-1-1-1h-1c-2 0-3-1-4-1h-2l4-1s2-1 2-2h0c-1 0-1 0-2-1h-3c2 0 3-1 4-1 1-1 3-2 4-3s1-1 2-1c2-3 2-8 2-11 1-1 1-2 1-3 0-3-1-6-1-9l1-4v1l1-1v-5z" class="J"></path><path d="M414 529h4c1 0 1-1 2-1s2 0 3-1l1 1h0l-2 1 1 1h-1c0 1-1 1-2 1 0-1-1-1-1-1h-1c-2 0-3-1-4-1z" class="D"></path><path d="M430 501v3h1c0 5 0 12-3 17 0-2 1-5 1-7 0-5 0-9 1-13z" class="e"></path><path d="M417 524c1-1 3-2 4-3s1-1 2-1l1 1c0 1-2 3-4 4l-2 1h0c-1 0-1 0-2-1h-3c2 0 3-1 4-1z" class="K"></path><path d="M431 485v-1l1 1v1l-1 18h-1v-3c-1-5 1-10 1-16z" class="a"></path><path d="M427 488v-6h1c0 2 0 5 1 7 0-2-1-4 0-6h1v8c-1 2-1 5-1 7 0 6 0 13-2 18v-8c-1 5-1 8-3 13l-1-1c2-3 2-8 2-11 1-1 1-2 1-3 0-3-1-6-1-9l1-4v1l1-1v-5z" class="e"></path><path d="M437 498c1 1 0 5 0 7h1v-3h0v5l1 1v-3-2l2-1c1 2 1 6 1 8v1 3-1 6 13c0 4 1 7 1 11v2c1 4 2 9 1 12l1 1 1-1c-1 4-3 9-6 12-1 0-1 1-2 1l-1 1-1-1v-1h-1 0 0c0-2 2-6 3-7h0c0 1 0 2-1 3l1 1c1-1 1-5 2-7 1-4 1-8 1-12v-3l-1 2c0-3 0-6-1-9v-5c-1-1-1-5-1-7v-5h-2v-4l1-18z" class="B"></path><path d="M442 547h1v12h-1v-12z" class="J"></path><defs><linearGradient id="X" x1="437.442" y1="538.585" x2="443.552" y2="535.071" xlink:href="#B"><stop offset="0" stop-color="#262628"></stop><stop offset="1" stop-color="#4c4c4c"></stop></linearGradient></defs><path fill="url(#X)" d="M440 516c0-1 0-4 1-4 1 1 0 6 0 8v14c0 5 1 9 0 13v-3l-1 2c0-3 0-6-1-9l1-21z"></path><path d="M437 498c1 1 0 5 0 7h1v-3h0v5l1 1v-3c0 3 0 9 1 11l-1 21v-5c-1-1-1-5-1-7v-5h-2v-4l1-18z" class="I"></path><path d="M438 520c0-1 0-3 1-4 1 2 0 9 0 11h-1v-2-5z" class="P"></path><path d="M417 568l-1-1c-2 0-3 4-4 5-1 2-2 3-4 4h0c1-1 2-3 3-4 2-1 2-2 2-4 1-1 2-2 2-4 1-4 1-9 3-13v-3l1-1c0-1 1-2 1-3s0-1 1-1v-1l1-1-1-1h2v1h1v-2h2 1c1-1 2-1 2-2l2 1c0-1 1-1 2-2h0l1 1c0 3 0 8-1 11 0 1 1 2 0 3v2 1c0 1-1 1-1 2h0v2l-1-1c-1-2-1-4-1-6-1 1-1 1-1 2h0v1l-1 3h0c0 2 0 2-1 3v-1-2c1-1 1-2 1-3v-1h0v-3h1c0-2-1-3-1-5v-2h-1c0 1 1 4 0 5v-1c-1 0-1 1-1 2h0c-1-1-1-1 0-2v-2h-1l1 1c-1 0-1 1-1 1v1h0l-1-5c0 2 1 3 0 5v2c-1 1-1 3-1 5v5l1 1c-3 6-4 12-9 18 1-3 2-5 2-7l1-1h-1 0c-1 2-2 5-4 6 1-3 3-6 4-9z" class="J"></path><path d="M430 544h0c1 0 1 1 1 1v7c-1-2-2-6-1-8z" class="k"></path><path d="M432 539h1c1 2 0 7 0 8l-1 1v-9z" class="O"></path><path d="M417 568c1-5 1-10 2-14 0-3 2-5 2-8v-1h1c0 2-1 4-1 6-1 2 0 5-1 7v1l-3 10h0v2c-1 2-2 5-4 6 1-3 3-6 4-9z" class="V"></path><path d="M421 551v3h0c1-2 1-4 2-6 0 3 0 5-1 8 0 2 0 4-1 7h1l1-3 1 1c-3 6-4 12-9 18 1-3 2-5 2-7l1-1h-1 0v-2h0l3-10v-1c1-2 0-5 1-7z" class="Y"></path><path d="M393 542h1 0 1c1 1 2 1 3 2h1 0 2l1 1c-1 1-1 1-1 3l2-2 2 2h0 2 2 0c0 1 1 1 1 2 1 1 1 2 2 2v1 2h-1l1 1h-1c-1 1-1 2-1 3s-1 2-1 3c-1-1-1-1-1-2v1l-3 3c0 2-1 3-2 4v-1c1-1 1-2 1-3-1 0-1-1-1-1l-3 3c0 1 0 1-1 1l1-2h0c0-1-1-1-1-2h-2 0-1 0l-1 1v1l-2 1v-2-1l-2 1h0c1-2 2-3 1-6h-1c-1 2-3 4-5 6 1-1 2-2 2-4 1 0 1-1 0-2h0l-2 3-1-2c0 1-1 1-1 2l-1-1c1-3 3-7 5-11 1-1 2-3 3-4 0-1 1-2 1-3h1z" class="O"></path><path d="M405 557c1 0 2 1 2 1v1c-1 0-2-1-2-1v-1zm-11 5c0-1 0-2 1-2 0-1 0-2 1-3-1 1-1 2 0 3v1h2l-1 1h0l-1 1h0l-1 1c-1-1-1-1-1-2z" class="k"></path><path d="M399 553c1 0 1 0 2 1v2h-1v1l-1 1-1-1c0-2 1-2 1-4z" class="Y"></path><path d="M393 555c0-1 0-1 1-2v1l2 2v1c-1 1-1 2-1 3-1 0-1 1-1 2l-1-1c1-1 1-1 1-3 0 0-1-1-1-2v-1z" class="P"></path><path d="M397 562h2l1-1v1c1-1 1-1 2-1h0v1h1 0c0-1 0-1 1-2h0v-1h1c0 1 0 1-1 2v1c1 0 1-1 2-1 0 1 0 1-1 2v1c0 2-1 3-2 4v-1c1-1 1-2 1-3-1 0-1-1-1-1l-3 3c0 1 0 1-1 1l1-2h0c0-1-1-1-1-2h-2 0-1l1-1h0z" class="K"></path><path d="M388 558c1-3 3-7 5-8 0 2-2 4-2 6v1l2-2v1c0 1 1 2 1 2 0 2 0 2-1 3l1 1c0 1 0 1 1 2v1l-2 1v-2-1l-2 1h0c1-2 2-3 1-6h-1c-1 2-3 4-5 6 1-1 2-2 2-4 1 0 1-1 0-2h0z" class="H"></path><path d="M393 542l1 1-1 1h1c0 2-2 6-3 7 0 0-1 0-1 1 0 2-2 4-3 6h-1 0l-1 1c0 1-1 1-1 2l-1-1c1-3 3-7 5-11 1-1 2-3 3-4 0-1 1-2 1-3h1z" class="j"></path><path d="M419 503l1 1v3l1 1v4l-1 2 1 1 1-3h0c0 3-2 5-2 7-1 2-3 3-3 5-1 0-2 1-4 1h3c1 1 1 1 2 1h0c0 1-2 2-2 2l-4 1c-3 1-4 2-5 4v1 3l1 2 1 1h0c0 1-1 2-2 3h-2c0 1-1 1-1 1-1 0-3-2-5-2-1 0-1 0-2-1h-1l-1 1h-1 0-1-1c-1 1-2 1-4 1l1-2v-1h2v-2-3c1 0 2-1 2-1 0-1 0-2-1-2l1-2c1-1 1-2 2-3v-1l-1-2 1-2 1 1c0-1 2-2 4-2h1c1 0 1-1 2-2 0 0 1 0 1-1l3-2s1 0 2-1c1 0 2-2 3-2h1c0-1 1-2 1-3h0c1-2 1-2 2-3 0 0 0 1 1 1v-1h2v-1-3z" class="O"></path><path d="M404 522c1-2 3-3 6-4v1l-1 1h2l-1 1h-2c-1 1-2 1-4 1h0z" class="l"></path><path d="M404 522h0c2 0 3 0 4-1h2l-2 2c-1 1-3 2-4 3v1l-1-1c-2 1-2 2-3 3 0-3 1-5 4-7z" class="a"></path><path d="M421 515l1-3h0c0 3-2 5-2 7-1 2-3 3-3 5-1 0-2 1-4 1v-2l3-2 1-1c2-1 3-3 4-5z" class="J"></path><path d="M409 515c1 0 2-2 3-2l-1 2h2c1 2 1 2 3 2l2 1c-1 1-2 2-4 3h-1v1l-1-1c0-1 2-1 2-3h-2-1v-1c-1 0-1 1-2 1l-1-1 1-1v-1z" class="I"></path><path d="M407 516v1l-3 3c-2 2-5 3-6 5-1 1-1 3-2 5 0 1 0 1-1 2l-2-2c1-1 1-2 2-3v-1l-1-2 1-2 1 1c0-1 2-2 4-2h1c1 0 1-1 2-2 0 0 1 0 1-1l3-2z" class="V"></path><path d="M395 526v-1l3-3h1v1h0c-1 0-1 1-1 1l-3 4v1l1 1c0 1 0 1-1 2l-2-2c1-1 1-2 2-3v-1z" class="Y"></path><path d="M419 503l1 1v3l1 1v4l-1 2c-1 1-1 3-2 4l-2-1c-2 0-2 0-3-2h-2l1-2h1c0-1 1-2 1-3h0c1-2 1-2 2-3 0 0 0 1 1 1v-1h2v-1-3z" class="B"></path><path d="M419 503l1 1v3l1 1v4c-1-1-1-3-2-4h0l-1 1v-1l-1 1c-1 0-1 0-2-1l-1 2c1-2 1-2 2-3 0 0 0 1 1 1v-1h2v-1-3z" class="D"></path><path d="M404 526c1-1 3-2 4-3 1 1 2 0 3 0v1c-2 1-3 2-4 3 3 1 6-1 9 1l-4 1c-3 1-4 2-5 4v1 3l1 2 1 1h0c0 1-1 2-2 3h-2c0 1-1 1-1 1-1 0-3-2-5-2-1 0-1 0-2-1h-1l-1 1h-1 0-1-1c-1 1-2 1-4 1l1-2v-1h2v-2l2-2c2-1 2-2 3-3s2-3 3-3l1-1c1-1 1-2 3-3l1 1v-1z" class="m"></path><path d="M405 536h0v1l-1 1c-1 1-1 1-2 1l-1-1 1-2h1v1l1 1v-2h1z" class="j"></path><path d="M405 536c0-2 0-3 1-5v3h1v3 1l-2-2h0z" class="Y"></path><path d="M405 536l2 2v-1l1 2-1 1v1h-2c-1 0-2-1-3-2 1 0 1 0 2-1l1-1v-1z" class="O"></path><path d="M400 529c1-1 1-2 3-3l1 1-4 8c0-1-1-1-1-2v-3l1-1z" class="W"></path><path d="M399 536h2v2l1 1c1 1 2 2 3 2h2v-1l1-1 1 1h0c0 1-1 2-2 3h-2s-1-1-2-1l-1-1c-2-1-2-3-3-4v-1z" class="K"></path><path d="M409 540h0c0 1-1 2-2 3h-2s-1-1-2-1c1-1 1-1 2 0 1 0 2-1 2-1 1-1 1-1 2-1z" class="I"></path><path d="M404 526c1-1 3-2 4-3 1 1 2 0 3 0v1c-2 1-3 2-4 3-2 1-3 6-4 8h-1c0-2 1-5 2-7v-2z" class="W"></path><path d="M391 538l2-2c2-1 2-2 3-3s2-3 3-3v3 3 1c1 1 1 3 3 4l1 1c1 0 2 1 2 1 0 1-1 1-1 1-1 0-3-2-5-2-1 0-1 0-2-1h-1l-1 1h-1 0-1-1c-1 1-2 1-4 1l1-2v-1h2v-2z" class="L"></path><path d="M391 538l2-2c2-1 2-2 3-3s2-3 3-3v3 3 1 1c-1 1 1 1 0 2-1-1-2-3-3-4-1 1 0 3-2 3v-1c-2 0-2 2-3 2l-2 1v-1h2v-2zm-31-8h0v2c1-1 1-1 2-1l1 1c1 1 3 3 5 4h1l2 1v-1 1h0v1c-1 1-1 1-1 2-1 1-2 2-2 3h1c-1 3-2 5-3 7h1v3l1-1c1 0 1 1 2 1h1 0l-1 2c0 1 0 2-1 2 0 4 0 8 1 11l4-6v2 3l-1 1v2 1l-1 2-1 1h-1 0c-1 0-1 0-2-1h0v1h-1l-1-1c-1-2-2-3-3-5h-1 0v1l1 4c1 1 1 2 1 2-1 0-1 0-2-1 0 0 0-1-1-1s-1-1-2-1h0s1 0 1 1h0v2h-1c-1-1-1-2-2-4 0-2-1-4-1-7-1 2-1 2-1 3l3 9c-2-2-2-4-3-6h-1c0 2 2 4 2 7h0c-3-2-3-8-4-11 0-2-1-6-1-9 1-1 1-3 1-4 1-1 1-3 2-4 0-1 1-2 2-3 2-1 3-2 5-3l1-2v-3c-1 0-2 0-2-1h0c-1-1-1-2-2-3h0c1-1 2-2 2-4z" class="J"></path><path d="M360 557h1c0 4 1 7 2 11h-1 0 0c-2-1-2-3-3-5h0v-6h1z" class="I"></path><path d="M359 563v-6h1c0 2 0 5 1 7 0 1 0 2 1 4h0 0c-2-1-2-3-3-5h0z" class="O"></path><path d="M364 541c-1-2-2-3-2-5l1-1c0 1 0 2 1 2 0 0 1-1 2-1 1 2 0 1 0 3 1-1 2-1 2-2h1v1h1l1-1v-1 1h0v1c-1 1-1 1-1 2-1 1-2 2-2 3l-1 1v1s0 1-1 1c0-1 0-2 1-2l-1-1s1-1 1-2v-1h0-1c-1 0-1 0-2 1z" class="K"></path><path d="M356 564h0c-1-4-1-9-1-13h1v3h1c0 5 0 8 1 12 0 1 1 3 1 4h1v-3h0c-1-1-1-2-1-3v-1h0c1 2 1 4 3 5h0v1l1 4c1 1 1 2 1 2-1 0-1 0-2-1 0 0 0-1-1-1s-1-1-2-1h0s1 0 1 1h0v2h-1c-1-1-1-2-2-4 0-2-1-4-1-7z" class="H"></path><path d="M363 573h0 0c-2 0-2 0-3-2h0l2-2 1 4z" class="P"></path><path d="M356 564h0c-1-4-1-9-1-13h1v3c0 5 0 9 1 14 0 1 1 2 0 3 0-2-1-4-1-7z" class="k"></path><defs><linearGradient id="Y" x1="373.98" y1="570.318" x2="359.95" y2="549.901" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient></defs><path fill="url(#Y)" d="M364 541c1-1 1-1 2-1h1 0v1c0 1-1 2-1 2l1 1c-1 0-1 1-1 2 1 0 1-1 1-1v-1l1-1h1c-1 3-2 5-3 7h1v3l1-1c1 0 1 1 2 1h1 0l-1 2c0 1 0 2-1 2 0 4 0 8 1 11l4-6v2 3l-1 1v2 1l-1 2-1 1h-1 0c-1 0-1 0-2-1h0v1h-1l-1-1h0c0-2-1-3-1-4-1-2-2-5-3-7v-7-1c1-1 1-2 1-2l-1-1v-2l2-6v-2h-1 1z"></path><path d="M364 541c1-1 1-1 2-1 0 1-1 2-2 3v-2h-1 1z" class="H"></path><path d="M367 553l-2 4c0-2 0-4 1-7h1v3z" class="Q"></path><path d="M372 573h-1l-1-1v-1h0c-2-4-2-10-1-14h0c0 4 0 8 1 11l4-6v2 3l-1 1v2 1l-1 2z" class="L"></path><path d="M564 549l1-1v1c1 2-2 10-2 13-1 0-1 2-1 2s0 1 1 1c-1 3-3 5-3 8 0 1-1 1-1 1v1 1l-2 1v1c0 1-1 2-1 4-2 5-5 11-9 15-1 2-3 4-5 6-1 1-2 2-3 4h0l6-6 1 1c-1 1-3 2-3 4-1 1-3 2-4 3-1 0 0-1-1-1-2 2-3 3-5 3 0 0-1 0-1-1h0c-1 1-1 2-2 2-1 1-2 1-3 2h0c0 1 0 0-1 1-1 0-2 1-3 3-1 1-4 2-6 3-1 1-3 1-5 2-3 1-7 2-11 1v-1c3 0 5 0 7-1h2c1 0 1-1 2-1h1v-1h-1 0c-1-1-1 0-2 0h-2c-7 1-14 2-21 0l-9-2v-1c1 0 3 0 4-1h-2v-1l1-1h4c2 0 5 0 6-1l-1-1c2 0 1 0 2-1h4c1 0 1 0 2-1l1 1h0 0c0 1 0 1 1 1h3 0c1 1 2 1 3 1 3 0 7-3 10-4 2-1 5-2 6-4 4-2 7-4 10-6 2-3 4-5 6-7 1-2 2-4 4-4 0 1-2 3-3 4v2-1 1 1h0 1l10-15c1-2 2-5 4-5 1-1 2-2 3-2 0-2 1-4 1-5 1 0 1 0 1 1 1-2 1-3 2-5l3-15z" class="J"></path><path d="M544 595c0-1 0-2 1-2 1-1 1-3 2-3s1 1 0 2c-2 2-3 6-5 8s-3 4-5 6l-1-1 5-5c1-1 3-3 3-5z" class="k"></path><path d="M521 608h1c4-2 8-6 12-7-3 3-8 5-12 8-2 1-3 3-5 4h-2c1-2 2-2 3-4l3-1z" class="O"></path><path d="M513 620c1 0 2-1 3-2l6-3 1-1c1 0 0 0 1-1 0 0 1 0 1-1-2 0-3 2-4 2s-1 0-1 1h-1 0v-1h1c2-2 3-3 5-4 1-1 3-1 5-2 0 0-1 1-2 1-1 1-1 1-1 2h0 1 0c0 1-1 1-1 2h0c1 0 2 0 3-1-1 1-2 1-3 2h0c0 1 0 0-1 1-1 0-2 1-3 3-1 1-4 2-6 3-1 1-3 1-5 2-3 1-7 2-11 1v-1c3 0 5 0 7-1h2c1 0 1-1 2-1h1v-1z" class="D"></path><path d="M507 616h3c2-1 2-1 3-1-3 2-6 3-9 3v1c3 0 11-4 13-2-2 1-7 2-9 3-7 1-14 2-21 0v-2c2 0 5 0 7-1h3 5c2-1 4-2 5-1z" class="Q"></path><path d="M522 605c4-2 7-4 10-6 2-3 4-5 6-7 1-2 2-4 4-4 0 1-2 3-3 4-2 2-4 5-7 8s-7 5-11 8l-3 1c-1 2-2 2-3 4h2c0 1 0 1-1 2-1 0-2 1-3 0-1 0-1 0-3 1h-3c-1-1-3 0-5 1h-5-3c-2 1-5 1-7 1v2l-9-2v-1c1 0 3 0 4-1h-2v-1l1-1h4c2 0 5 0 6-1l-1-1c2 0 1 0 2-1h4c1 0 1 0 2-1l1 1h0 0c0 1 0 1 1 1h3 0c1 1 2 1 3 1 3 0 7-3 10-4 2-1 5-2 6-4z" class="Y"></path><path d="M515 613h2c0 1 0 1-1 2-1 0-2 1-3 0-1 0-1 0-3 1h-3l8-3z" class="K"></path><path d="M480 616v-1l1-1v1s1 0 1 1h3c-1 1-2 1-2 2h4v2l-9-2v-1c1 0 3 0 4-1h-2z" class="O"></path><path d="M498 610l1 1h0 0c0 1 0 1 1 1 0 1 1 1 1 1v1l-6-1v1c1 0 2 0 3 1h-1l1 1c-1 1-3 1-4 0-2 0-5-1-8 0l-1-2c2 0 5 0 6-1l-1-1c2 0 1 0 2-1h4c1 0 1 0 2-1z" class="b"></path><path d="M522 605c4-2 7-4 10-6 2-3 4-5 6-7 1-2 2-4 4-4 0 1-2 3-3 4-2 2-4 5-7 8s-7 5-11 8l-3 1c-3 1-5 3-8 4-2 0-4 0-6 1-1 0-2 1-3 1h-3c-1-1-2-1-3-1v-1l6 1v-1s-1 0-1-1h3 0c1 1 2 1 3 1 3 0 7-3 10-4 2-1 5-2 6-4z" class="W"></path><path d="M564 549l1-1v1c1 2-2 10-2 13-1 0-1 2-1 2s0 1 1 1c-1 3-3 5-3 8 0 1-1 1-1 1v1 1l-2 1v1c0 1-1 2-1 4-2 5-5 11-9 15-1 2-3 4-5 6 0-1 0-1 1-2l-1-1c2-2 3-6 5-8 1-1 1-2 0-2s-1 2-2 3c-1 0-1 1-1 2-2 3-5 7-9 10v-1c2-2 3-3 5-6 1-1 2-3 3-4 1-2 2-3 3-5l2-3h0c-2 3-5 7-7 9h-1l10-15c1-2 2-5 4-5 1-1 2-2 3-2 0-2 1-4 1-5 1 0 1 0 1 1 1-2 1-3 2-5l3-15z" class="O"></path><path d="M557 573c0 1-1 2-1 3l-1 1c-1 0-2 1-2 2 0 0-1 1-2 1h-1c1-2 2-5 4-5 1-1 2-2 3-2zm-10 19c2-2 4-3 5-6h0c0 2-2 5-3 6v2h-1v-1l-1 1 1 1c-1 1-1 1-1 2-1 2-3 4-5 6 0-1 0-1 1-2l-1-1c2-2 3-6 5-8z" class="P"></path><path d="M365 480l2-1c0 1 1 1 1 2h0c1 0 2 0 3 1h2 1l1 2c1 0 1 0 2 1l3-1v1h2 1v1l2-1v1l-3 1-3 3c2 0 4 0 5 1 0 1 0 1-1 1s-1 1-2 1-2 1-3 1h-1c-1 0-1 0-1 1l-5 1-5 2h-1c-2 1-3 1-4 2h-2 0c0 1-1 1-2 2-1 0-1 1-2 1v1h0l-1 1v1l-2 2v1 2c1-1 2-3 3-3-1 1-2 2-2 4h1-1v1c-1 2-1 2-1 4 1 0 2-1 3-1 0 2-1 3-3 4h0c0 1 0 1-1 2v3c-2 0-2 0-3 1v1h-1v2l-2-1c0 1 0 2-1 3v-2c-1 0-1 1-2 2v-1c-1-2 1-5 1-8v-1c-1-1-1-2-1-3-1 1 0 2 0 4-1 2-1 5-1 7h-1v-1l-1 1-1 2-2 1v-1 1h0v2h-1v-4h-1v-1h0v-2h0l-1-2v-4c1 0 0 2 1 3v-4c0 1 0 3 1 3h0v-4c-2-2 0-2-1-4h0v-1l1-3c1-1 1-3 2-4h0l-1-1h0c0-1 0-1 1-2 0-1 0-1-1-2l1-1h1 0c0-1 1-2 2-2h1c1-1 3-3 4-5-1 1-2 1-3 2h-1c1-1 3-3 5-4h-1l-2 1v-1l1-1c1-2 2-2 3-3l11-6c3 0 3 0 5-1l2-1z" class="b"></path><path d="M375 484c1 0 1 0 2 1l3-1v1h2 1c-2 0-3 1-4 1-1 1-1 1-3 1v-1c-1 0-1-1-2-1h0-1c0-1 1-1 2-1z" class="o"></path><path d="M353 500c5-3 10-5 15-7 3 0 6-1 9-1-4 2-9 3-13 4-4 2-7 3-10 6h0c0-1 0-1-1-2z" class="F"></path><path d="M369 489c2-1 3-1 5-1v1c-8 2-17 4-24 10l-1-1v-1c2-2 4-3 6-4 1 0 3-1 4-1 1-1 3-1 5-2h2l3-1z" class="E"></path><path d="M348 505l5-5c1 1 1 1 1 2h0c-3 2-5 6-7 9-1 4-2 6-2 10h-1c0 1-1 4 0 5v3c-1 0-1 1-2 2v-1c-1-2 1-5 1-8v-1c-1-1-1-2-1-3 1-5 3-9 6-13z" class="C"></path><path d="M343 521c0-2 0-4 1-6 0-2 2-6 4-7-1 1-2 3-2 4-2 3-2 6-2 9 0 1-1 4 0 5v3c-1 0-1 1-2 2v-1c-1-2 1-5 1-8v-1z" class="P"></path><path d="M347 511c2 0 2-1 3-2s2-3 2-4l3-3c1-1 2-2 3-2h1c0 1-1 1-2 2-1 0-1 1-2 1v1h0l-1 1v1l-2 2v1 2c1-1 2-3 3-3-1 1-2 2-2 4h1-1v1c-1 2-1 2-1 4 1 0 2-1 3-1 0 2-1 3-3 4h0c0 1 0 1-1 2v3c-2 0-2 0-3 1v1h-1v2l-2-1c0 1 0 2-1 3v-2-3c-1-1 0-4 0-5h1c0-4 1-6 2-10z" class="J"></path><path d="M345 523h2v3l-2 2c0 1 0 2-1 3v-2-3l1-3z" class="i"></path><path d="M347 511c2 0 2-1 3-2s2-3 2-4l3-3c1-1 2-2 3-2h1c0 1-1 1-2 2-1 0-1 1-2 1v1h0l-1 1v1l-2 2-1 1c0 1 0 1-1 2-2 2-3 7-3 10-1 0-1 1-2 1v1l-1 3c-1-1 0-4 0-5h1c0-4 1-6 2-10z" class="l"></path><path d="M347 503c2-2 4-4 7-6s15-7 19-7c0 0 1 0 1 1-2 1-5 1-7 2l-8 3c-2 1-3 1-4 2-3 2-6 4-8 6l1 1c-3 4-5 8-6 13-1 1 0 2 0 4-1 2-1 5-1 7h-1v-1l-1 1-1 2-2 1v-1 1h0v2h-1v-4h-1v-1h0v-2h0l-1-2v-4c1 0 0 2 1 3v-4c0 1 0 3 1 3h0v-4l2-4c1 0 1-2 2-3h1v-1l1-1c1-3 3-5 5-7h1z" class="R"></path><path d="M347 504l1 1c-3 4-5 8-6 13-1 1 0 2 0 4-1 2-1 5-1 7h-1v-1l-1 1-1 2-2 1v-1 1h0v2h-1v-4h-1v-1h1c0-1 0-3 1-4v-1l1-1c1 0 2 0 2 1v1l1-1c1-1 1-3 1-4v-2c1-5 3-10 6-14z" class="g"></path><path d="M346 503h1c-2 3-4 5-5 8-1 2-3 7-2 9h1c0 1 0 3-1 4l-1 1v-1c0-1-1-1-2-1l-1 1v1c-1 1-1 3-1 4h-1 0v-2h0l-1-2v-4c1 0 0 2 1 3v-4c0 1 0 3 1 3h0v-4l2-4c1 0 1-2 2-3h1v-1l1-1c1-3 3-5 5-7z" class="K"></path><path d="M339 512h1v-1l1-1c0 2-1 2-1 4l-2 7c-1 0-1-1-1-2h-1c0 1 0 3-1 4v-4l2-4c1 0 1-2 2-3z" class="b"></path><path d="M365 480l2-1c0 1 1 1 1 2h0c1 0 2 0 3 1h2 1l1 2c-1 0-2 0-2 1h1l-2 1h1c-1 1-3 2-4 3h0l-3 1h-2c-2 1-4 1-5 2-1 0-3 1-4 1-2 1-4 2-6 4v1l1 1-4 4c-2 2-4 4-5 7l-1 1v1h-1c-1 1-1 3-2 3l-2 4c-2-2 0-2-1-4h0v-1l1-3c1-1 1-3 2-4h0l-1-1h0c0-1 0-1 1-2 0-1 0-1-1-2l1-1h1 0c0-1 1-2 2-2h1c1-1 3-3 4-5-1 1-2 1-3 2h-1c1-1 3-3 5-4h-1l-2 1v-1l1-1c1-2 2-2 3-3l11-6c3 0 3 0 5-1l2-1z" class="m"></path><path d="M334 514h1c1-1 1-4 3-5 0 2-3 4-1 6l-2 4c-2-2 0-2-1-4h0v-1z" class="k"></path><path d="M353 491v1c-1 0-1 1-2 1h0c-2 0-4 2-6 3s-3 3-5 5h-1c0-1 1-1 1-2h1c1-1 3-3 4-5h0c1-1 3-2 4-3l1 1c1 0 2 0 3-1z" class="U"></path><path d="M349 497v1l1 1-4 4c-2 2-4 4-5 7l-1 1v1h-1c1-2 1-3 2-5 1 0 0-1 0-2l8-8z" class="N"></path><path d="M353 491h0l1-1c0-1 1-1 2-2v-1h2v1l1 1 1-1h1c1-1 1-1 2-1l1 1c-1 1-3 1-4 2h3 1c-2 1-4 1-5 2-1 0-3 1-4 1h0c-6 1-12 8-15 13h-1v-1c1-1 2-3 3-4 2-3 6-6 9-8h0c1 0 1-1 2-1v-1z" class="d"></path><path d="M365 480l2-1c0 1 1 1 1 2h0c1 0 2 0 3 1h2 1l1 2c-1 0-2 0-2 1h1l-2 1h1c-1 1-3 2-4 3h0l-3 1h-2-1-3c1-1 3-1 4-2l-1-1c-1 0-1 0-2 1h-1l-1 1-1-1v-1h-2v1c-1 1-2 1-2 2l-1 1h0c-1 1-2 1-3 1l-1-1c-1 1-3 2-4 3h0c-1 1-2 1-3 2h-1c1-1 3-3 5-4h-1l-2 1v-1l1-1c1-2 2-2 3-3l11-6c3 0 3 0 5-1l2-1z" class="U"></path><path d="M365 480l2-1c0 1 1 1 1 2h0c1 0 2 0 3 1h2 1l1 2c-1 0-2 0-2 1h1l-2 1h-2c0 1-1 1-1 1-1 1-1 1-3 0 1-1 3 0 4-2h0-2-1c-1 0-1 0-2-1h-2l-1-1s0-1 1-1h0v-1l2-1z" class="W"></path><path d="M365 480l2-1c0 1 1 1 1 2l-1 1h1l-3 1-1-1 1-1v-1z" class="b"></path><path d="M368 481h0c1 0 2 0 3 1h2 1l1 2c-1 0-2 0-2 1h1l-2 1h-2 0c1 0 2-1 3-2h-3l-2-2h-1l1-1z" class="V"></path><path d="M344 491c1-1 2 0 2-1 1 0 2-1 3-2 1 0 2-1 3-1l9-5c-1 1-1 2-2 2v1l1 1-1 1v1h1l1-1c0-1 1-1 2-1s2 1 2 2l-1 1 1 1h1-2-1-3c1-1 3-1 4-2l-1-1c-1 0-1 0-2 1h-1l-1 1-1-1v-1h-2v1c-1 1-2 1-2 2l-1 1h0c-1 1-2 1-3 1l-1-1c-1 1-3 2-4 3h0c-1 1-2 1-3 2h-1c1-1 3-3 5-4h-1l-2 1v-1l1-1z" class="T"></path><path d="M413 417l-1-2v-2-3-1l2 3 2 2h0v1 1 3 5c0 2 2 5 0 8 0 1-2 2 0 4l1 5h2c-1-2-1-4-1-5 0-2 2-3 3-5-1-2-2-4-2-5 1-1 1-2 1-3 1-1 2-1 2-2h1v-1c1 0 1 0 2 1h0c0 1 0 2 1 3v2h0 0c1 3 1 6 3 8 1 3 2 6 4 7l3 7c0 2 0 4 1 6 1 3 2 7 3 11h0v2h0c0 1 0 1 1 2v2l1 4v1 3c1 1 0 3 1 4v8l-1 1c0-1 0-3-1-4v1h-1v3h-1v2c2 2 2 4 2 6v2h0l-2 1v2 3l-1-1v-5h0v3h-1c0-2 1-6 0-7 0-3 0-10-1-13h-1v8h-1c0-2 1-6 0-8h-1v1h-1v-1l-1-1v1h-1v-2h-1c-1 2 0 4 0 6-1-2-1-5-1-7h-1v6 5l-1 1v-1l-1 4c0 3 1 6 1 9 0 1 0 2-1 3 0 3 0 8-2 11-1 0-1 0-2 1s-3 2-4 3c0-2 2-3 3-5 0-2 2-4 2-7h0l-1 3-1-1 1-2v-4l-1-1v-3-11c1-4 1-8 1-12v-7l-1-6c0-3-1-5-2-8h2l-4-12c0-1 0-2 1-3v-2c0-1-1-1-1-2 0-2-1-5-2-7 1-1 1-1 1-2l-1-1v-3-1c-1-1-1-3-2-4 1 0 1 0 1-1s0-1-1-2v-1c1-1 1-1 1-2z" class="m"></path><path d="M419 448v-1l2-3h1v4l-1 1v3h-1v-2-1l-1-1z" class="e"></path><path d="M438 478c0-2-1-6-1-8h1v5c1 2 1 6 1 9 0 2 1 3 1 5v3h-1c-1-5-1-9-1-14z" class="f"></path><path d="M418 436h1l1 1c0 1 0 2 1 2v-1h1c0 2-1 2-1 4v1l-1 1-1 1v1l-1 1c0 1 0 1 1 1l1 1v1 2 3l-1 1c0-2-1-5-1-7h-1c0-1 0-2 1-3v-3-1l1-1h0c-1-2-1-4-1-5z" class="b"></path><path d="M430 444c1 2 2 3 3 5 0 1 1 1 1 2 2 4 4 10 5 14v2h1 0c0 1 0 1 1 2v2l1 4v1 3c1 1 0 3 1 4v8l-1 1c0-1 0-3-1-4v1h-1c0-2-1-3-1-5h1c1-2 0-7 0-9-2-7-3-14-5-21-1-1-1-3-2-4-2-2-2-4-3-6z" class="B"></path><path d="M429 477c0-2 0-5-1-7v-4c0-1-1-3-1-4l1-1c0 3 1 6 2 9 0 2 0 4 1 6 1-3-1-12-2-15-1 0 0 0-1-1v-1l-1-1c0 1 0 2-1 2 0-1 0-2 1-3v-2c1 2 2 4 2 6 1 4 3 9 3 13 0 2-1 6 0 8 0 0 1 0 1 1 1-1 0-3 0-4 0-5 0-10-1-15l-3-10c-1-1-2-2-2-4 1 1 2 3 2 4 2 6 4 12 5 18 1 1 1 2 1 3l2 7h0 0l1-4c0 5 0 9 1 14v2c2 2 2 4 2 6v2h0l-2 1v2 3l-1-1v-5h0v3h-1c0-2 1-6 0-7 0-3 0-10-1-13h-1v8h-1c0-2 1-6 0-8h-1v1h-1v-1l-1-1v1h-1v-2h-1c-1 2 0 4 0 6-1-2-1-5-1-7v-1l1-1v-3z" class="n"></path><path d="M438 478c0 5 0 9 1 14v2c2 2 2 4 2 6v2h0l-2 1v2 3l-1-1v-5h0l-1-13v-7h0 0l1-4z" class="k"></path><path d="M439 494c2 2 2 4 2 6v2h0l-2 1v-9z" class="I"></path><path d="M423 420c1 0 1 0 2 1h0c0 1 0 2 1 3v2h0 0c1 3 1 6 3 8 1 3 2 6 4 7l3 7c0 2 0 4 1 6 1 3 2 7 3 11h0v2h-1v-2c-1-4-3-10-5-14 0-1-1-1-1-2-1-2-2-3-3-5l-1-1c0-1 0-1-1-2 0 1-1 2-1 3h-1l1-2v-1l-2 1v-1h-1c0 1 0 1-1 1v1h-1l-1-1c0-2 1-2 1-4h-1v1c-1 0-1-1-1-2l-1-1h-1c0-2 2-3 3-5-1-2-2-4-2-5 1-1 1-2 1-3 1-1 2-1 2-2h1v-1z" class="o"></path><path d="M423 429h1c1 1 1 1 0 2h0c-1 0-1 0-2 1v-1c0-1 1-2 1-2z" class="Y"></path><path d="M423 437h1c0 1 1 1 1 3h0l-1-1h0c-1-1-1 0-2 0 0-1 0-1 1-2z" class="j"></path><path d="M413 417l-1-2v-2-3-1l2 3 2 2h0v1 1 3 5c0 2 2 5 0 8 0 1-2 2 0 4l1 5h2 0l-1 1v1 3c-1 1-1 2-1 3h1c0 2 1 5 1 7l3 9c1 4 1 8 2 11v-2c1-4 0-9-1-12 0-2-2-4-2-5 1 0 2 4 3 5l2 10c0 1 0 3 1 4v-6c1 1 1 2 1 3v6l1 1v-3 3l-1 1v1h-1v6 5l-1 1v-1l-1 4c0 3 1 6 1 9 0 1 0 2-1 3 0 3 0 8-2 11-1 0-1 0-2 1s-3 2-4 3c0-2 2-3 3-5 0-2 2-4 2-7h0l-1 3-1-1 1-2v-4l-1-1v-3-11c1-4 1-8 1-12v-7l-1-6c0-3-1-5-2-8h2l-4-12c0-1 0-2 1-3v-2c0-1-1-1-1-2 0-2-1-5-2-7 1-1 1-1 1-2l-1-1v-3-1c-1-1-1-3-2-4 1 0 1 0 1-1s0-1-1-2v-1c1-1 1-1 1-2z" class="f"></path><path d="M413 417c1 1 1 3 2 5h0c-1 2 0 3-1 5-1-1-1-3-2-4 1 0 1 0 1-1s0-1-1-2v-1c1-1 1-1 1-2z" class="D"></path><path d="M423 492v-7-5h1v2 14l-1-4h0z" class="e"></path><path d="M425 480h1c0 1 0 8 1 8h0v5l-1 1v-1l-1 4c0 3 1 6 1 9 0 1 0 2-1 3h-1v-5-1-6-1-14l1 3c0-1-1-3 0-4v-1z" class="J"></path><path d="M424 497v1c1 1 1 4 0 6l1-1v-6h0c0 3 1 6 1 9 0 1 0 2-1 3h-1v-5-1-6z" class="P"></path><path d="M425 480h1c0 1 0 8 1 8h0v5l-1 1v-1l-1 4h0v-17z" class="W"></path><path d="M420 460c1 6 2 12 2 18v15l1-1h0l1 4v1 6 1 5h1c0 3 0 8-2 11-1 0-1 0-2 1s-3 2-4 3c0-2 2-3 3-5 0-2 2-4 2-7h0l-1 3-1-1 1-2v-4l-1-1v-3-11c1-4 1-8 1-12v-7l-1-6c0-3-1-5-2-8h2z" class="m"></path><path d="M424 503v1l-1 4h0-1v-4l2-1z" class="W"></path><path d="M423 492h0l1 4v1 6l-2 1 1-12z" class="U"></path><path d="M424 509h1c0 3 0 8-2 11-1 0-1 0-2 1s-3 2-4 3c0-2 2-3 3-5 3-3 3-6 4-10z" class="H"></path><path d="M400 373c-1-1-1-1-1-3l1 1h1c8 9 16 17 23 27l7 10c10 15 19 30 23 48 1 5 2 11 3 17 0 6 1 11 2 17h-1v1l1 1v2 1l1 1v1 1c1 1 1 1 1 2v2h0c1 1 1 1 1 2h-3 0-1l-1-1c0-1-1-2-2-3h0v-2c1-1 1-3 0-4h0c0 3 0 6-1 9 0 4 1 8 1 12-2-2-1-7-1-9 0-1-1-2-1-3l-1 1v-2 3 1c1 2-1 5 0 7 0 2 4 6 5 7 1 2 1 3 2 4l-7-8c0-1-1-1-1-2v-1l-3-7v-1-6c0-3 0-6 1-8s2-3 2-4c1-1 1-6 0-7v-6c-1-2-1-5-2-6s-1-1-2 0v2c1 3 2 6 1 9 0 3 1 6 0 9 0-1-1-3-1-4v-4c-1-2-2-4-2-6 0 0 0-1-1-2h0v-1-2c0-1 0-1-1-1v-1-4h0c-1 0-1-1-1-1v-2c-1-2-1-3-2-5 0-1 0-2-1-3v-1-1s0-1-1-1v1h0v1c1 1 1 2 1 3 1 2 1 4 2 6h-1s0-1-1-1v-2l-1-1v-3h-1l-1-5c0-1 0-2-1-3 0-1-1-3-2-4-2-1-3-4-4-7-2-2-2-5-3-8h0 0v-2c-1-1-1-2-1-3h0c-1-1-1-1-2-1v1h-1c0 1-1 1-2 2 0 1 0 2-1 3 0 1 1 3 2 5-1 2-3 3-3 5 0 1 0 3 1 5h-2l-1-5c-2-2 0-3 0-4 2-3 0-6 0-8v-5-3-1-1h0l-2-2-2-3v1 3 2l1 2c0 1 0 1-1 2v1c0-1 0-2-1-2 0-2-1-2-1-4h1c0-2 0-3-1-4l-1-3-1-2c-1 0-2 2-3 2l-1-2h1v-4-3c-1-1-2-3-3-4h0c0-2 0-2-2-4h2c1 1 1 1 2 1v-1c-1-4-2-8-4-12l-1-2v-2l1-1z" class="B"></path><path d="M400 373c1 2 2 5 4 7 1 1 1 2 2 3h-1l-2-3c-1-1-1-2-2-2h-1l-1-2v-2l1-1z" class="K"></path><path d="M450 462v1 1l1-1h0 0c1 0 2 1 2 1 0 1-1 2-1 2s1 1 1 2c1 1 1 2 0 4v2c0-4-3-7-3-12z" class="I"></path><path d="M415 395c3 5 6 11 7 16 0-1-1-2-1-2-1-2-1-3-2-4-1 0-1 1-2 2 0 1 1 2 1 3s-1 1-1 2c-1 0-1-1-2-1h0l-1 1-2-3v1 3 2l1 2c0 1 0 1-1 2v1c0-1 0-2-1-2 0-2-1-2-1-4h1c0-2 0-3-1-4l-1-3h1c0-1 1-2 1-2 2 1 3 3 4 4h1v-1l1-1s0-1-1-1h1v-2-2c-1-2-2-5-2-7z" class="J"></path><defs><linearGradient id="Z" x1="406.637" y1="390.782" x2="401.724" y2="391.972" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#303032"></stop></linearGradient></defs><path fill="url(#Z)" d="M400 378h1c1 0 1 1 2 2h0c0 1 0 1 1 2l1 1c0 1 1 2 1 3v1c1 2 0 6 1 9h0v2 4c0 1-1 2-2 3v-4-3c-1-1-2-3-3-4h0c0-2 0-2-2-4h2c1 1 1 1 2 1v-1c-1-4-2-8-4-12z"></path><path d="M404 390l2 8h-1c-1-1-2-3-3-4h0c0-2 0-2-2-4h2c1 1 1 1 2 1v-1z" class="K"></path><path d="M416 419h0c0-1 1-1 1-2 1-1 1 0 3 0h2 2l2 1h1 0l1 1v2c1 0 1 1 1 1 0 1 1 1 1 2l-1-1h-1c0-1 0-1-1-1l-1-2c-1-2-2-1-4-2-1 1-2 1-3 0h0c-1 1-1 1-1 2 1-1 2-1 3 0h1 0 1v1h-1c0 1-1 1-2 2 0 1 0 2-1 3 0 1 1 3 2 5-1 2-3 3-3 5 0 1 0 3 1 5h-2l-1-5c-2-2 0-3 0-4 2-3 0-6 0-8v-5z" class="P"></path><path d="M418 420c1-1 2-1 3 0h1 0 1v1h-1c0 1-1 1-2 2 0 1 0 2-1 3 0 1 1 3 2 5-1 2-3 3-3 5 0 1 0 3 1 5h-2l-1-5h0l1 1c1-1 0-1 0-2s1-1 1-2 1-1 1-2c-1-1-1-2-1-4v-2-5z" class="j"></path><path d="M418 420c0-1 0-1 1-2h0c1 1 2 1 3 0 2 1 3 0 4 2 0 2 1 3 1 5l1 1c0 1 1 1 1 3l2 4h1v-1 1h0c0 2 0 3 1 4v1c1 0 1 0 1 1s1 2 2 3v-1h0c-1-2-2-3-2-5v-1c1 1 3 7 4 9 1 0 1 1 1 2s1 3 1 4 1 2 1 3v1l2 8v1h0c-1 0-1-1-1-1v-2c-1-2-1-3-2-5 0-1 0-2-1-3v-1-1s0-1-1-1v1h0v1c1 1 1 2 1 3 1 2 1 4 2 6h-1s0-1-1-1v-2l-1-1v-3h-1l-1-5c0-1 0-2-1-3 0-1-1-3-2-4-2-1-3-4-4-7-2-2-2-5-3-8h0 0v-2c-1-1-1-2-1-3h0c-1-1-1-1-2-1h-1 0-1c-1-1-2-1-3 0zm-11-24c0-1 0-3 1-4l1-3 2 2 4 4c0 2 1 5 2 7v2 2h-1c1 0 1 1 1 1l-1 1v1h-1c-1-1-2-3-4-4 0 0-1 1-1 2h-1l-1-2c-1 0-2 2-3 2l-1-2h1c1-1 2-2 2-3v-4-2z" class="O"></path><path d="M407 398c1 1 0 2 1 3 0 1 1 2 1 3l1 1-1 1 1 1h-1l-1-2c-1 0-2 2-3 2l-1-2h1c1-1 2-2 2-3v-4z" class="K"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3v1l-2 1c1 1 1 2 2 3s1 2 2 3l1 1 2 3c0 1 1 2 1 2l3 5c2 1 3 2 4 4v-1l2 1h0c2 2 2 2 2 4h0c1 1 2 3 3 4v3 4h-1l1 2c1 0 2-2 3-2l1 2 1 3c1 1 1 2 1 4h-1c0 2 1 2 1 4 1 0 1 1 1 2 1 1 1 1 1 2s0 1-1 1c1 1 1 3 2 4v1 3l1 1c0 1 0 1-1 2 1 2 2 5 2 7 0 1 1 1 1 2v2c-1 1-1 2-1 3l4 12h-2c1 3 2 5 2 8l1 6v7c0 4 0 8-1 12v11l-1-1v3 1h-2v1c-1 0-1-1-1-1-1 1-1 1-2 3h0c0 1-1 2-1 3h-1c-1 0-2 2-3 2-1 1-2 1-2 1l-3 2c0 1-1 1-1 1-1 1-1 2-2 2h-1c-2 0-4 1-4 2l-1-1-1 2 1 2v1c-1 1-1 2-2 3l-1 2c1 0 1 1 1 2 0 0-1 1-2 1v3 2h-2v1l-1 2-2-2c1-1 2-1 2-2v-1l-2 1h0v-2l-1-1-2 1c-1 0-1 0-2 1h-1s0 1-1 1c0-1 1-1 1-1l-1-1-1 1-1 1-1 1v-1c-1 0-1-1-2-1l-1 1v-1l1-1h-1c-1 0-2 2-3 4-1 0-1 2-1 2h-1c0-1 1-2 2-3 0-1 0-1 1-2v-1h0v-1 1l-2-1h-1c-2-1-4-3-5-4l-1-1c-1 0-1 0-2 1v-2h0c0 2-1 3-2 4h0c0 2 0 4-2 5v-2c0-1 0-3 1-4l1-1c0-1-1-3-1-4v-1c0-1 0-2-1-3v-4c1-1 1-2 1-3s0-1-1-2l-1 1c-1 0-2 1-3 1 0-2 0-2 1-4v-1h1-1c0-2 1-3 2-4-1 0-2 2-3 3v-2-1l2-2v-1l1-1h0v-1c1 0 1-1 2-1 1-1 2-1 2-2h0 2c1-1 2-1 4-2h1l5-2 5-1c0-1 0-1 1-1h1c1 0 2-1 3-1s1-1 2-1 1 0 1-1c-1-1-3-1-5-1l3-3 3-1v-1l-2 1v-1h-1-2v-1l-3 1c-1-1-1-1-2-1l-1-2h-1-2c-1-1-2-1-3-1h0c0-1-1-1-1-2l-2 1-2 1c-2 1-2 1-5 1l2-1v-1h0c1-1 2-1 2-1 1-1 0-1 1-1s2 0 3-1h0 1c0-1 0-1 1-1l-1-1c-3 1-5 3-8 4h-1c-1 0-1 0-2 1h0c-1 0-2 1-3 1 1-2 4-3 5-5l1-1 1-1c1-1 3-2 4-4 1-1 2-2 3-4l1-2 2 1 1-1 2-2-1-2 1-2c1-1 1-2 1-3l-1 1h0c-1 0-1 1-2 1v-1-1c1-1 1-2 1-3v2c1-1 1-1 1-2s0-5-1-6h-1v-1-2-4l1-4v-3c2-4 2-8 5-12v1 2h1v-2h1c0-1 0-1 1-2v1l1-1v1h0c1 1 1 1 1 2h0 1c1 1 1 2 1 3 0 2 1 5 2 6l1-1h-1c0-2 0-3-1-4 0-2 0-3-1-4h0l1-2 1 3h0v-2c-1-1-1-2-1-3-1-2 2-6 3-9h0c1-1 1-2 2-3 0-1 0-2-1-2 0-2 0-5-1-6-1-5-2-9-4-13-1-1-1-2-2-3 0-1-1-1-1-2 0-3-4-7-5-10h0l1 1h1c0-3 1-4 3-6h0z" class="j"></path><path d="M404 490v-3h0l1 3v1c0 2 1 5 2 8 0 0 0 1 1 2v1c-2-1-2-3-3-4 0-1 0-3-1-4v-4z" class="U"></path><path d="M397 518v1c-1 1-3 1-4 2-1 0-1 0-1 1l-1 1c-1 1-1 3-2 4l-1 1c0-2 0-4 1-5h1l-1-1c-1 2-2 4-2 6v1h-1c0-2 0-4 1-5 1-2 1-3 3-3h1c0-1 0-1 1-1s1 0 2-1l3-1z" class="e"></path><path d="M397 518c1-1 2-1 3-1v1h1c-2 2-4 3-6 4h0l-1 2 1 2v1c-2 0-3-1-4-1v-1s0-1 1-1l-1-1 1-1c0-1 0-1 1-1 1-1 3-1 4-2v-1z" class="l"></path><path d="M414 498c0 2-1 4-1 5l-1 1-1-1c0 1 0 2-1 3-1 2-4 5-6 7l-1 1h2 2l1 1h-1l-3 1v-1c-1 0-2 0-3 1l-1-1h1v-1l-7 3c1-2 3-2 5-3 4-1 6-4 8-7 1-1 2-2 2-3s1-2 1-2v-1h1v1l1-1c0-1 1-2 2-3z" class="H"></path><defs><linearGradient id="a" x1="390.06" y1="537.196" x2="389.989" y2="526.546" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#535352"></stop></linearGradient></defs><path fill="url(#a)" d="M391 523l1 1c-1 0-1 1-1 1v1c1 0 2 1 4 1-1 1-1 2-2 3l-1 2c1 0 1 1 1 2 0 0-1 1-2 1v3 2h-2v1l-1 2-2-2c1-1 2-1 2-2v-1h0c1-1 1-1 2-1v-1l-2-1h0c1-1 0-3 0-4v-1-2l1-1c1-1 1-3 2-4z"></path><path d="M388 528l1-1c1 2 0 5 0 8h-1 0c1-1 0-3 0-4v-1-2z" class="l"></path><path d="M391 526c1 0 2 1 4 1-1 1-1 2-2 3l-1 2c1 0 1 1 1 2 0 0-1 1-2 1v-1c1-3 0-5 0-8z" class="V"></path><path d="M401 478h1c1 1 1 4 0 5 0 3 0 5 1 7h0v3c1 2 1 3 1 5 1 1 1 1 1 2v1c-1 0-1 1-1 2l-1 1v-3h1v-3h-1v2c0 2-2 4-3 5v1c0-3 2-5 2-7h0l-2 4c0 1-1 1-1 1l-2-2 1-1v-3c1 0 1-1 0-2v-4c1-2 2-6 2-9l1-3v-2z" class="U"></path><path d="M401 478h1c1 1 1 4 0 5v1h-1v1c0 1 1 3 0 4v-9-2z" class="a"></path><path d="M402 483c0 3 0 5 1 7h0v3 1h-1l-1-5c1-1 0-3 0-4v-1h1v-1z" class="d"></path><path d="M401 430v-1l2 3 3 6c0 2 1 3 1 5 2 5 4 11 5 17v4c1 2 0 5 1 7v3l2 2h0v2c1 2 0 11-1 13v7c-1 1-2 2-2 3 0 0 0-1-1-1 0-1 0-1 1-2v-5-1l-1-1v-5h1c0 1-1 4 0 5l1-1c0-3 0-7-1-11v-5c-1-3-1-7-1-11-1-6-2-12-4-18-2-5-3-11-6-15z" class="J"></path><path d="M380 523c1-2 3-3 4-4s2-1 3-2c1 0 1-1 2-1 2-2 5-3 8-4h0l-1 1c-2 1-3 2-5 3v2c-1 0-1 1-2 1-2 1-4 4-4 6h-1v1 4c1 1 2 1 3 2l1-1c0 1 1 3 0 4h0l2 1v1c-1 0-1 0-2 1h0l-2 1h0v-2l-1-1-2 1c-1 0-1 0-2 1h-1s0 1-1 1c0-1 1-1 1-1l-1-1-1 1-1 1c0-1-1-2-2-2 0-1 1-1 1-2v-1l1-1v3h1v-1c0-1 0-2 1-2v-1-2l-1-2c0-1 1-1 1-2v-1-1l1-1h0 0z" class="k"></path><path d="M378 528c0-1 1-1 1-2v-1-1l1-1h0 0c0 3-1 5 0 7-1 2-1 3-1 5h1v-2h1v2h1v-2-1c1 1 1 2 2 3v-1c0-1 1-1 1-2h1v5l-1-1-2 1c-1 0-1 0-2 1h-1s0 1-1 1c0-1 1-1 1-1l-1-1-1 1-1 1c0-1-1-2-2-2 0-1 1-1 1-2v-1l1-1v3h1v-1c0-1 0-2 1-2v-1-2l-1-2z" class="B"></path><path d="M380 523c1-2 3-3 4-4s2-1 3-2c1 0 1-1 2-1 2-2 5-3 8-4h0l-1 1c-2 1-3 2-5 3-1 1-3 2-4 4-1 0-1 1-2 2-1 2-2 3-2 5 0 1 0 2-1 3-1-1 0-4 0-6 1-1 2-3 3-4h-1c-3 3-3 6-4 11v-1c-1-2 0-4 0-7z" class="T"></path><path d="M395 434h0c1 1 2 2 2 3l1 2v-1c0-2-1-3-2-5s-1-3-2-5c2 2 3 5 5 7 1 4 2 7 3 11 3 9 5 19 5 28 1 6 0 11 0 16 0 3 1 5 1 8h-1c0-3-1-6-1-8h-1l-1-3h0v3h-1 0c-1-2-1-4-1-7 1-1 1-4 0-5h-1 0l1-2v-1c1-1 2-3 2-5h0c0-2 1-4 0-5v-2-1h1c0-1 0-3-1-3v-3-1c-1-1-1-2-1-4-1-2-2-5-3-8h-1l-1-1c0-2-1-2-2-3 0-1 0-1-1-2v-3z" class="N"></path><path d="M404 470h0c0-2 1-4 0-5v-2-1h1c1 8 1 16 1 24v4h-1l-1-3h0v3h-1 0c-1-2-1-4-1-7 1-1 1-4 0-5h-1 0l1-2v-1c1-1 2-3 2-5z" class="W"></path><path d="M389 421c1-1 2 0 3-1 0 2-1 2 0 4v1c0 3 3 6 3 9v3c1 1 1 1 1 2 1 1 2 1 2 3l1 1h1l3 8c0 2 0 3 1 4v1 3c1 0 1 2 1 3h-1v1 2c1 1 0 3 0 5h0c0 2-1 4-2 5v1l-1 2h0v2l-1 3c0 3-1 7-2 9v-1h-1v2h-1v-1l-1 1h0l-1 1-1-1c1-2 2-3 2-6h0c0-1 1-1 1-2h-1c1-3 2-5 2-8h0c1-1 2-4 1-5l-1-1v-3h1v3c2-2 0-3 2-4v-2c0-2-1-5-1-7-2-6-4-11-5-17-1-2-2-3-2-5 0-1 0-2-1-3-1-3-1-6-2-9h0c2 1 2 5 3 7 0 2 1 3 1 4v1h1v-2c-1-1-2-4-2-6-1-3-2-5-3-7z" class="B"></path><path d="M403 465v4l1 1c0 2-1 4-2 5v1l-1 2c0-2 0-8 1-9s1-3 1-4z" class="b"></path><path d="M396 492c1-2 1-5 3-7l1-2c0 3-1 7-2 9v-1h-1v2h-1v-1z" class="I"></path><path d="M400 467c1 6-1 13-4 18h-1c1-3 2-5 2-8h0c1-1 2-4 1-5l-1-1v-3h1v3c2-2 0-3 2-4z" class="e"></path><path d="M389 421c1-1 2 0 3-1 0 2-1 2 0 4v1c0 3 3 6 3 9v3c1 1 1 1 1 2 1 1 2 1 2 3l1 1h1l3 8c0 2 0 3 1 4v1 3c1 0 1 2 1 3h-1v1 2c1 1 0 3 0 5h0l-1-1v-4-3c-2-9-4-17-9-24v1c3 4 4 9 5 14 2 4 3 8 3 13h-1c-1-6-2-13-5-19-1-3-1-5-2-8-1-1-1-2-2-3 0-1 0-2-1-3-1-3-1-6-2-9h0c2 1 2 5 3 7 0 2 1 3 1 4v1h1v-2c-1-1-2-4-2-6-1-3-2-5-3-7z" class="G"></path><g class="e"><path d="M399 443h1l3 8c0 2 0 3 1 4v1 3c1 0 1 2 1 3h-1v1 2c1 1 0 3 0 5h0l-1-1v-4-3h0v-3-2c-1-2-1-4-2-6l-1-5s0-1-1-1v-2z"></path><path d="M390 406l2 2h1c1 0 1 0 2 1h1l1 1h-2-1v1c-1 0-1-1-2-1v1c0 1 1 2 2 4 0 2 2 4 3 6v1l4 6v1 1c3 4 4 10 6 15 2 6 3 12 4 18v4c-2-2-1-6-1-8-2-5-3-10-5-15-1-4-2-7-4-10h0c2 6 4 13 6 19 0 2 1 4 1 6 1 5 1 10 2 14v11c-1 1 0 2-1 3v4h0-1c0-3 0-6 1-9 0-3 0-7-1-11 0-3 0-7-1-10-1-5-2-10-4-15-1-4-2-8-4-11h0c-2-2-3-5-5-7 1 2 1 3 2 5s2 3 2 5v1l-1-2c0-1-1-2-2-3h0c0-3-3-6-3-9v-1c-1-2 0-2 0-4-1 1-2 0-3 1 1 2 2 4 3 7 0 2 1 5 2 6v2h-1v-1c0-1-1-2-1-4-1-2-1-6-3-7h0c-1-1-2-3-2-4v-3c-1 1-1 3-1 4-1-1-1-2-1-3-1-2 2-6 3-9h0c1-1 1-2 2-3z"></path></g><path d="M389 421h0c-1-2 0-4 0-6l3 5c-1 1-2 0-3 1z" class="N"></path><path d="M390 406l2 2h1c1 0 1 0 2 1h1l1 1h-2-1v1c-1 0-1-1-2-1v1c0 1 1 2 2 4 0 2 2 4 3 6v1l4 6v1 1l-1-2c-1-1-1-1-1-2-2-5-5-10-8-14 0 1-1 2-1 3h-1c0-1 0-1-1-2l-1 4c-1 1-1 3-1 4-1-1-1-2-1-3-1-2 2-6 3-9h0c1-1 1-2 2-3z" class="Y"></path><path d="M390 406l2 2c-1 0-1 0-2 1h-1-1c1-1 1-2 2-3z" class="Q"></path><path d="M388 413c1-1 1-2 1-3h0l1-1c1 1 1 2 1 3s-1 2-1 3h-1c0-1 0-1-1-2z" class="W"></path><path d="M396 493h1v-2h1v1 4c1 1 1 2 0 2v3l-1 1 2 2s1 0 1-1l2-4h0c0 2-2 4-2 7-3 2-5 5-8 6-1 0-2-1-2-1l-2 2v1c-3 2-6 3-8 6h0c-2 3-3 4-2 7v1l1 2v2 1c-1 0-1 1-1 2v1h-1v-3l-1 1v1c0 1-1 1-1 2 1 0 2 1 2 2l-1 1v-1c-1 0-1-1-2-1l-1 1v-1l1-1h-1c-1 0-2 2-3 4-1 0-1 2-1 2h-1c0-1 1-2 2-3 0-1 0-1 1-2v-1h0v-1 1l-2-1h-1c-2-1-4-3-5-4l-1-1c-1 0-1 0-2 1v-2c0-1 0-3 1-4h0c0-1 0-2 1-2 0-2 1-3 1-4v-1c1-1 1-2 2-2 0 1-1 2 0 3h0c0-1 1-1 1-2s0-2 1-3v-1c1 0 1-1 2-1l1-1c1-1 5-3 7-4h1c2-1 4-2 7-3l2-1s1-1 2-1c1-1 3-1 4-2h1c0-1 1-1 2-2 0-1-1-1 0-2v-1h-1-1v-1h0c1 0 2-1 2-2z" class="J"></path><path d="M379 516h1v1l-2 2v1c-1 0-2 2-2 3v1c-1 0 0 1-1 1-1 3 1 6 0 9 0 1-1 2-1 2h-1 0v-2c1-1 1-2 1-3v-7c1-1 1-2 2-3v-2h1c1-1 1-2 2-3z" class="D"></path><path d="M361 526h0c1 1 1 1 1 2l1 1 1-2v3l1 1v-1c0-1 1-2 1-3v-2c0-1 0-2 1-3v-2l1-1c0-1 1-3 2-4v-1c1 0 1-1 2-1h1c-1 1-2 1-3 2v1c-1 1-1 2-2 3v1h0v2c-1 0-1 1-1 1v2h0c1-1 1-2 2-3v-2h0v-1c1 0 1-1 1-1h1c0 1-1 1-1 2v1c-1 0-1 1-1 1v2c-1 2-1 7 0 9v1h1l1-1c0 1 1 1 1 3h-1v1l-2-1h-1c-2-1-4-3-5-4l-1-1c-1 0-1 0-2 1v-2c0-1 0-3 1-4z" class="I"></path><path d="M396 493h1v-2h1v1 4c1 1 1 2 0 2v3l-1 1 2 2s1 0 1-1l2-4h0c0 2-2 4-2 7-3 2-5 5-8 6v-1h0v-1-1c1 0 2-1 2-2h0c-2 1-2 2-4 2 0-1 1-1 2-1v-1h1c0-1 1-1 1-2v-1h0v-1-1h0c-1 0-3 1-4 2-2 0-4 1-6 2s-4 1-5 2-2 0-2 0h1c2-1 4-2 7-3l2-1s1-1 2-1c1-1 3-1 4-2h1c0-1 1-1 2-2 0-1-1-1 0-2v-1h-1-1v-1h0c1 0 2-1 2-2z" class="B"></path><path d="M401 429v-1l-4-6v-1c-1-2-3-4-3-6-1-2-2-3-2-4v-1c1 0 1 1 2 1v-1h1 2v1h1 0 1v1h1c1 1 1 3 3 4 1 1 2 3 2 5l1 1h-1c0-1-1-2-2-4h0c-1 2 1 3 0 4 0-1 0-1-1-2l-1-1v1c0 1 1 2 1 3 1 1 1 2 2 3s2 2 2 3c4 7 6 15 8 23l1-1c0-2-1-3-1-5 0 0-1 0-1-1l-3-12h0c2 2 2 6 3 9l2 4c0 1 0 2 1 3 0 2 0 4 1 5l1 6c1 3 2 5 2 8l1 6v7c0 4 0 8-1 12v11l-1-1v3 1h-2v1c-1 0-1-1-1-1-1 1-1 1-2 3h0c0 1-1 2-1 3h-1c-1 0-2 2-3 2-1 1-2 1-2 1l-3 2c0-1 0-1 1-1l2-2h1l-1-1h-2-2l1-1c2-2 5-5 6-7 1-1 1-2 1-3l1 1 1-1c0-1 1-3 1-5v-7c1-2 2-11 1-13v-2h0l-2-2v-3c-1-2 0-5-1-7v-4c-1-6-3-12-5-17 0-2-1-3-1-5l-3-6-2-3z" class="V"></path><path d="M415 476c0-3-1-5-1-8-1-2 0-5-1-7-1-8-4-15-6-23-1-3-2-7-4-10h0c3 2 4 8 6 12 1 3 2 7 3 10l1 4c0 3 1 5 2 8v-1l1 1v3 2c0 1 0 0 1 1v5 7c1 3 1 6 1 9v1c0 1-2 2-3 3v3c0 2 0 5-1 7 0 1 1 1 1 2s1 2 1 2c-1 1-1 1-2 3h0c0 1-1 2-1 3h-1c-1 0-2 2-3 2-1 1-2 1-2 1l-3 2c0-1 0-1 1-1l2-2h1l-1-1h-2-2l1-1c2-2 5-5 6-7 1-1 1-2 1-3l1 1 1-1c0-1 1-3 1-5v-7c1-2 2-11 1-13v-2h0z" class="g"></path><path d="M412 509l2-6c0 1 1 1 1 2s1 2 1 2c-1 1-1 1-2 3h0c0 1-1 2-1 3h-1c-1 0-2 2-3 2-1 1-2 1-2 1l-3 2c0-1 0-1 1-1l2-2h1l-1-1h0c2-1 3-2 4-3v-2h1z" class="D"></path><path d="M412 509l2-6c0 1 1 1 1 2s1 2 1 2c-1 1-1 1-2 3h0l-1 1c0-2 0-2 1-3h0l-1 1h-1zm3-48l1 1v3 2c0 1 0 0 1 1v5 7 10h0-1v-5c1-2 0-3 0-5 0-4 0-8-1-12v-3c-1-1 0-2 0-3v-1z" class="K"></path><path d="M415 461c0-2-1-3-1-5-2-7-3-13-5-20-1-2-3-5-3-7h0c4 7 6 15 8 23l1-1c0-2-1-3-1-5 0 0-1 0-1-1l-3-12h0c2 2 2 6 3 9l2 4c0 1 0 2 1 3 0 2 0 4 1 5l1 6c1 3 2 5 2 8l1 6v7c0 4 0 8-1 12v11l-1-1v3 1h-2v1c-1 0-1-1-1-1s-1-1-1-2-1-1-1-2c1-2 1-5 1-7v-3c1-1 3-2 3-3v-1c0-3 0-6-1-9v-7-5c-1-1-1 0-1-1v-2-3l-1-1z" class="j"></path><path d="M418 499v-4h1v4h-1z" class="m"></path><path d="M419 499v4 3l-1-1c-1-1-1-5 0-6h1z" class="Y"></path><path d="M415 461c0-2-1-3-1-5-2-7-3-13-5-20-1-2-3-5-3-7h0c4 7 6 15 8 23l3 9c1 7 2 15 2 22 0 2 0 5-1 7v-1c0-3 0-6-1-9v-7-5c-1-1-1 0-1-1v-2-3l-1-1z" class="I"></path><path d="M390 487c1 0 3-1 4-1l1 1h0c0 3-1 4-2 6l1 1 1-1h0l1-1v1c0 1-1 2-2 2h0v1h1 1v1c-1 1 0 1 0 2-1 1-2 1-2 2h-1c-1 1-3 1-4 2-1 0-2 1-2 1l-2 1c-3 1-5 2-7 3h-1c-2 1-6 3-7 4l-1 1c-1 0-1 1-2 1v1c-1 1-1 2-1 3s-1 1-1 2h0c-1-1 0-2 0-3-1 0-1 1-2 2v1c0 1-1 2-1 4-1 0-1 1-1 2h0c-1 1-1 3-1 4h0c0 2-1 3-2 4h0c0 2 0 4-2 5v-2c0-1 0-3 1-4l1-1c0-1-1-3-1-4v-1c0-1 0-2-1-3v-4c1-1 1-2 1-3s0-1-1-2l-1 1c-1 0-2 1-3 1 0-2 0-2 1-4v-1h1-1c0-2 1-3 2-4-1 0-2 2-3 3v-2c2-3 4-6 7-8 5-3 12-4 18-6 2 0 3-1 4-1h3c1 0 1-1 2-2h-1v-1h2v-1c0-1 1 0 2-1 0-1 0-1 1-2z" class="G"></path><path d="M366 507c1-1 3-1 4-2 0 0 0-1 1-1v1h0l-2 2h0l6-3h2 0c-1 1-5 2-6 3l1 1c-4 3-7 5-10 9 0 1-1 2-1 3l-1 1h0s-1-1 0-1v-3h0c-2 1-1 3-2 4-1 0-1-1-2-1 1-1 1-2 1-3l1-1c0-1 0-1 1-2v-1 1c1 0 1 0 1-1h1c1-3 3-5 5-6z" class="d"></path><path d="M359 513v1c1 0 1 0 1-1h1c1-3 3-5 5-6l-1 2c-2 1-4 4-4 6l2-3h1c-1 2-2 4-2 5s-1 2-1 3l-1 1h0s-1-1 0-1v-3h0c-2 1-1 3-2 4-1 0-1-1-2-1 1-1 1-2 1-3l1-1c0-1 0-1 1-2v-1z" class="O"></path><path d="M352 509c2-3 4-6 7-8 5-3 12-4 18-6 2 0 3-1 4-1-2 2-4 2-6 3-3 1-6 1-8 3h0c2-1 4-2 7-2l2-1v3c-2 1-4 2-6 2h0c0-1 1-1 2-2h-1 0-1l-2 1c-1 1-1 1-2 1 0 1-1 1-1 1v1h-1c-1 3-4 6-5 9v1c-1 1-1 1-1 2l-1 1c0-1 0-1-1-2l-1 1c-1 0-2 1-3 1 0-2 0-2 1-4v-1h1-1c0-2 1-3 2-4-1 0-2 2-3 3v-2z" class="C"></path><path d="M361 504c1 0 2-1 3-1-3 2-4 4-5 7 1 0 1-1 2-3l3-3c-1 3-4 6-5 9v1c-1 1-1 1-1 2l-1 1c0-1 0-1-1-2l-1 1c-1 0-2 1-3 1 0-2 0-2 1-4v-1h1-1c0-2 1-3 2-4l1-1c1 0 0 0 1-1h0c1-1 3-2 4-2z" class="B"></path><path d="M355 508l1-1c1 0 0 0 1-1h0c1-1 3-2 4-2-1 2-4 3-4 5h0l-1 1c-1 0-1 0-2 1v1h-1c0-2 1-3 2-4z" class="F"></path><path d="M390 487c1 0 3-1 4-1l1 1h0c0 3-1 4-2 6l1 1 1-1h0l1-1v1c0 1-1 2-2 2h0v1h1 1v1c-1 1 0 1 0 2-1 1-2 1-2 2h-1c-1 1-3 1-4 2-1 0-2 1-2 1l-2 1c-3 1-5 2-7 3h-1c-2 1-6 3-7 4l-1 1c-1 0-1 1-2 1v1c-1 1-1 2-1 3s-1 1-1 2h0c-1-1 0-2 0-3-1 0-1 1-2 2v1c0 1-1 2-1 4-1 0-1 1-1 2h0c-1 1-1 3-1 4h0c0 2-1 3-2 4h0c0 2 0 4-2 5v-2c0-1 0-3 1-4l1-1c0-1-1-3-1-4v-1c0-1 0-2-1-3v-4c1 0 1 1 2 1 1-1 0-3 2-4h0v3c-1 0 0 1 0 1h0l1-1c0-1 1-2 1-3 3-4 6-6 10-9 3-1 5-3 8-4 5-1 9-3 13-6h0c-1 1-2 1-3 1h-1c-1 1-3 2-4 2 1-1 2-2 4-3h-3c-3 1-6 3-9 3 2-1 5-2 7-4 2 0 3 0 4-2h0l-3 1h0l1-2h0-1c-1 1-2 1-3 1-1 1-2 2-3 2h-3l-2 1c-3 0-5 1-7 2h0c2-2 5-2 8-3 2-1 4-1 6-3h3c1 0 1-1 2-2h-1v-1h2v-1c0-1 1 0 2-1 0-1 0-1 1-2z" class="K"></path><path d="M394 495h0c-1 0-1 0-1 1h-2l1-1v-2-1h0c-1 0-1 1-3 1v-1c1 0 1 0 2-1l4-4c0 3-1 4-2 6l1 1 1-1h0l1-1v1c0 1-1 2-2 2h0z" class="P"></path><path d="M357 527h0c1-1 1-1 1-2s0-1 1-1v1s0 1 1 1v4c0 2-1 3-2 4h0c0 2 0 4-2 5v-2c0-1 0-3 1-4l1-1c0-1-1-3-1-4v-1z" class="Y"></path><path d="M394 451c1 2 3 5 2 6l3 8h0 1v2c-2 1 0 2-2 4v-3h-1v3l1 1c1 1 0 4-1 5h0c0 3-1 5-2 8h1c0 1-1 1-1 2l-1-1c-1 0-3 1-4 1-1 1-1 1-1 2-1 1-2 0-2 1v1h-2v1h1c-1 1-1 2-2 2h-3c-1 0-2 1-4 1-6 2-13 3-18 6-3 2-5 5-7 8v-1l2-2v-1l1-1h0v-1c1 0 1-1 2-1 1-1 2-1 2-2h0 2c1-1 2-1 4-2h1l5-2 5-1c0-1 0-1 1-1h1c1 0 2-1 3-1s1-1 2-1 1 0 1-1c-1-1-3-1-5-1l3-3 3-1v-1l-2 1v-1h-1-2v-1l-3 1c-1-1-1-1-2-1l-1-2h-1-2c-1-1-2-1-3-1h0c0-1-1-1-1-2l-2 1-2 1c-2 1-2 1-5 1l2-1v-1h0c1-1 2-1 2-1 1-1 0-1 1-1s2 0 3-1h0 1c0-1 0-1 1-1l-1-1c-3 1-5 3-8 4h-1c-1 0-1 0-2 1h0l1-1h0v-1c2 0 4-1 5-2h0c2 0 3-1 4-2 3 0 3-3 5-4s2-2 3-3c2-2 4-3 5-5 0-1 0-1 1-1 0 1 1 1 2 2h-1v1h1c0-1 1-2 2-3l1-2h0l2-1s1 0 1 1h0 1v-2h0c4-1 2-4 5-6z" class="o"></path><path d="M394 467c-1-1-1-4-1-5l1-1 1 3c0 1-1 2-1 3z" class="m"></path><path d="M395 464c0 1 0 1 1 2 0 1 0 1-1 2v3l-1 1h-1l1-1c0-2-1-2-1-3l1-1c0-1 1-2 1-3z" class="Q"></path><path d="M387 464h1v2h1c-1 1-2 3-2 5h-2c0-1 0-2 1-4l1-3z" class="V"></path><path d="M385 486c1 0 1 0 1 1 1 0 2-1 3-1v1c-1 1-2 2-3 2h-2c0-1 0-1-1-1l-1-1 3-1z" class="j"></path><path d="M389 466v4c-1 1-2 2-2 3h-1v-1l-3 2h0l2-3h2c0-2 1-4 2-5zm3 17c0-1-1-1-1-2l1-1v-1c1-1 2-4 3-6 0 0 0-1 1-1h0v3h-1l-1 3c0 2 0 4-2 5z" class="b"></path><path d="M394 478l1 1h1l1-2h0c0 3-1 5-2 8h1c0 1-1 1-1 2l-1-1c-1 0-3 1-4 1 0-1 0-1 1-2l1-2c2-1 2-3 2-5z" class="j"></path><path d="M396 457l3 8h0 1v2c-2 1 0 2-2 4v-3h-1 0c-2 1 0 3-2 3v-3c1-1 1-1 1-2v-6c-1-1 0-1 0-3z" class="V"></path><path d="M387 458s1 0 1 1h0 1v-2h0v1l1 1v2c1 1 1 2 1 4l-1-1h0c0-1 0-3-1-3l-1 1-1 2-1 3v-2l-2 2c0-2 0-3-1-4l2-4 2-1z" class="K"></path><path d="M386 465c-1-2 0-2 0-4h1l1 1-1 2-1 3v-2z" class="O"></path><path d="M383 463c1 1 1 2 1 4-1 2-2 3-3 5-2 0-2 0-4 1h1 0c0 1-1 1-1 1-2 1-2 2-4 2h0c0-1 1-2 2-3 1 0 1-1 1-2 2-1 3-2 4-4s2-3 3-4z" class="Q"></path><path d="M394 451c1 2 3 5 2 6 0 2-1 2 0 3v6c-1-1-1-1-1-2l-1-3v-5c-1 1-2 3-2 4v1c1 1 1 4 1 5h-1v-4c-1-1-1-2-1-3-1-1-1-1-2-1v-1c4-1 2-4 5-6z" class="O"></path><path d="M379 474l1-1c1 0 1 1 2 1 0 1-1 1 0 2v-1c1 0 1 1 1 1h1c0-1 0-1 1-1l1 1 1-1c0 1 1 1 2 2-1 0-2 1-2 2h1c0-1 1-2 1-3s1-2 1-3c1 1 1 1 2 1l-1 1h-1v1l1 1-3 3c-1 0-1 1-1 1-1 0-1-1-1-1 0-1 0-1-1-2l1-2h0c-1 0-1 1-1 1l-2 2h0l-1 1h-1c0-1-2-1-2-2h-1c0-1 0-1 1-2l-2-1 2-1z" class="j"></path><path d="M379 474c1 0 1 0 1 1v1h0s-1 0-1 1c2 0 1 1 2 1 1 1 1 0 2 1l-1 1h-1c0-1-2-1-2-2h-1c0-1 0-1 1-2l-2-1 2-1z" class="O"></path><path d="M373 477c2 0 3-1 4-2l2 1c-1 1-1 1-1 2h1c0 1 2 1 2 2h1l1-1h0l2-2s0-1 1-1h0l-1 2c1 1 1 1 1 2 0 0 0 1 1 1l-1 1h0c2-1 3-2 5-4v1c-1 1-1 2-2 2-2 1-3 3-4 4l-2 1v-1h-1-2v-1l-3 1c-1-1-1-1-2-1l-1-2h-1-2c-1-1-2-1-3-1v-1h3l-1-1 2-2h1z" class="k"></path><path d="M381 480h1v1h1c0 1-1 2-1 2h-2v-1c-1 0-2 0-3 1v-1c1-1 1-1 3-1l1-1z" class="I"></path><path d="M368 481v-1h3l-1-1 2-2 1 1c-1 1-1 1-2 1v1h1c1 0 1 0 2 1h1v1h-1-1-2c-1-1-2-1-3-1z" class="Q"></path><path d="M373 477c2 0 3-1 4-2l2 1c-1 1-1 1-1 2h1c0 1 2 1 2 2l-1 1c-1-2-2-2-3-2s-1 1-2 1l1-2h0-2l-1-1z" class="K"></path><path d="M382 463h-1v1h1c0-1 1-2 2-3l1-2h0l-2 4c-1 1-2 2-3 4s-2 3-4 4c0 1 0 2-1 2-1 1-2 2-2 3-3 1-5 2-7 2l-6 3v-1h0c1-1 2-1 2-1 1-1 0-1 1-1s2 0 3-1h0 1c0-1 0-1 1-1l-1-1c-3 1-5 3-8 4h-1c-1 0-1 0-2 1h0l1-1h0v-1c2 0 4-1 5-2h0c2 0 3-1 4-2 3 0 3-3 5-4s2-2 3-3c2-2 4-3 5-5 0-1 0-1 1-1 0 1 1 1 2 2z" class="B"></path><path d="M366 478c1-1 4-2 5-4l3-2c1-2 4-7 7-8l-1 3c-1 2-2 3-4 4 0 1 0 2-1 2-1 1-2 2-2 3-3 1-5 2-7 2z" class="P"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3v1l-2 1c1 1 1 2 2 3s1 2 2 3l1 1 2 3c0 1 1 2 1 2l3 5c2 1 3 2 4 4v-1l2 1h0c2 2 2 2 2 4h0c1 1 2 3 3 4v3 4h-1l1 2c1 0 2-2 3-2l1 2 1 3c1 1 1 2 1 4h-1c0 2 1 2 1 4 1 0 1 1 1 2 1 1 1 1 1 2s0 1-1 1c1 1 1 3 2 4v1 3l1 1c0 1 0 1-1 2 1 2 2 5 2 7 0 1 1 1 1 2v2c-1 1-1 2-1 3l4 12h-2l-1-6c-1-1-1-3-1-5-1-1-1-2-1-3l-2-4c-1-3-1-7-3-9h0l3 12c0 1 1 1 1 1 0 2 1 3 1 5l-1 1c-2-8-4-16-8-23 0-1-1-2-2-3s-1-2-2-3c0-1-1-2-1-3v-1l1 1c1 1 1 1 1 2 1-1-1-2 0-4h0c1 2 2 3 2 4h1l-1-1c0-2-1-4-2-5-2-1-2-3-3-4h-1v-1h-1 0-1v-1l-1-1h-1c-1-1-1-1-2-1h-1l-2-2c0-1 0-2-1-2 0-2 0-5-1-6-1-5-2-9-4-13-1-1-1-2-2-3 0-1-1-1-1-2 0-3-4-7-5-10h0l1 1h1c0-3 1-4 3-6h0z" class="b"></path><path d="M414 434c-1-2-2-3-2-5-1 0-1-1-1-2-3-2-4-7-6-10 3 3 3 7 6 9 0-2-3-6-3-8h1v2l3 5c0 1 0 2 1 3h1v3l1 1c0 1 0 1-1 2z" class="h"></path><path d="M408 413c0-1 1-1 1-2s0-1 1-1c1 1 1 2 1 4h-1c0 2 1 2 1 4 1 0 1 1 1 2 1 1 1 1 1 2s0 1-1 1c1 1 1 3 2 4v1h-1c-1-1-1-2-1-3v-2c-2-2-1-4-3-5v-2h-1v-3z" class="Y"></path><path d="M394 386c2 1 3 2 4 4v-1l2 1h0c2 2 2 2 2 4h0c1 1 2 3 3 4v3 4h-1l1 2c1 0 2-2 3-2l1 2 1 3c-1 0-1 0-1 1s-1 1-1 2c-1 1-1 2-2 2s-1 0-2-1h0l1-1v1c1 0 2-1 2-2l1-1-1-1c0 1-1 1-2 2 0-1 1-1 1-1v-1h-1s0 1-1 1v-1h-1c0 1-1 1-2 2 0-1 0-1-1-1v1h-1v-1h-1 0-1v-1l-1-1h2l-1-2v-3s0-1-1-2v-2h-1v2h-1v-3h-1l-1-2v-2h1l1-3h0c0-1 0-1 1-2-1-2-1-2-1-4z" class="Q"></path><path d="M401 400c1 2 1 3 2 4h1l1-3v4h-1c0 1-1 2-1 3h-1v-3c-1-2-1-3-1-5zm-4 7c1-1 1-2 2-2v-1-1c1 0 1 1 0 1v3h1s0-1 1-1l1 4h-1c-1 0-1-1-2-1l-1 1-1 1v-1l-1-1h2l-1-2z" class="H"></path><path d="M401 399c0-2 1-3 0-5h0 1c1 1 2 3 3 4v3l-1 3h-1c-1-1-1-2-2-4v-1z" class="L"></path><path d="M394 386c2 1 3 2 4 4v-1l2 1h0c2 2 2 2 2 4h0-1 0c1 2 0 3 0 5l-1-1v1c-1-1-1-1-1-2h-1v2c-1 1-1 3-2 3v-2h-1v2h-1v-3h-1l-1-2v-2h1l1-3h0c0-1 0-1 1-2-1-2-1-2-1-4z" class="Y"></path><path d="M398 390v-1l2 1h0c2 2 2 2 2 4h0-1 0c1 2 0 3 0 5l-1-1-2-8z" class="J"></path><path d="M394 392h0c0-1 0-1 1-2 1 1 2 2 2 4v1c-1-1-1-2-2-3 0 2 2 6 1 7 0 0-1 0-1 1h0v2h-1v-3h-1l-1-2v-2h1l1-3z" class="V"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3v1l-2 1c1 1 1 2 2 3s1 2 2 3l1 1 2 3c0 1 1 2 1 2l3 5c0 2 0 2 1 4-1 1-1 1-1 2h0l-1 3h-1v2l1 2h1v3h1v-2h1v2c1 1 1 2 1 2v3l1 2h-2-1c-1-1-1-1-2-1h-1l-2-2c0-1 0-2-1-2 0-2 0-5-1-6-1-5-2-9-4-13-1-1-1-2-2-3 0-1-1-1-1-2 0-3-4-7-5-10h0l1 1h1c0-3 1-4 3-6h0z" class="U"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3v1l-2 1c1 1 1 2 2 3s1 2 2 3v2l-1-1-2-2c-1-1-2-2-2-3h-1c0 1 1 1 1 2h-1v-1l-1 1v1l-3-3h1c0-3 1-4 3-6h0z" class="b"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3v1l-2 1c-1 0-1-1-2-1s-1 1-2 1v3l1 1v1l-3-3h1c0-3 1-4 3-6h0z" class="Y"></path><path d="M381 365c0-1 1-1 2-1h0c1 1 2 2 2 3l-1 1c-1 0-2-2-3-3h0 0z" class="b"></path><path d="M391 395c0-1 0-2-1-3 0-3-1-4-2-7-1-1-1-2-2-3 0-2 0-2-1-3h-1v-2c-1 0-2-2-2-3h1c0 1 1 2 1 3h1l1-1 1 1v-2l1 1 2 3c0 1 1 2 1 2l3 5c0 2 0 2 1 4-1 1-1 1-1 2h0l-1 3h-1v2c0 1-1 1-1 1v-3z" class="l"></path><path d="M387 375l1 1 2 3c0 1 1 2 1 2l3 5c0 2 0 2 1 4-1 1-1 1-1 2h0c-1 0-1-1-1-2-4-3-4-9-6-13v-2z" class="o"></path><g class="b"><path d="M391 381l3 5c0 2 0 2 1 4-1 1-1 1-1 2h0c-1 0-1-1-1-2v-1c-1-1-1-1-1-3 0-1 0-1-1-3v-1h-1l1-1z"></path><path d="M388 394c1-1 1-1 1-2-1-5-5-10-7-15l2 3c1 1 2 3 2 4 1 2 2 3 3 5 0 2 1 4 2 6v3s1 0 1-1l1 2h1v3h1v-2h1v2c1 1 1 2 1 2v3l1 2h-2-1c-1-1-1-1-2-1h-1l-2-2c0-1 0-2-1-2 0-2 0-5-1-6-1-5-2-9-4-13 1 0 3 7 4 9h0z"></path></g><path d="M393 399h1v3h1v-2h1v2c1 1 1 2 1 2-1 0-1 1-1 1 0 1-1 1-1 1-1 0-1 0-2-1h1c-1-2-1-4-1-6z" class="K"></path><path d="M384 385c1 0 3 7 4 9h0c0 2 1 3 2 5 0 2 2 4 3 6 1 1 1 1 2 1 0 0 1 0 1-1 0 0 0-1 1-1v3l1 2h-2-1c-1-1-1-1-2-1h-1l-2-2c0-1 0-2-1-2 0-2 0-5-1-6-1-5-2-9-4-13z" class="L"></path><path d="M386 421c0-1 0-3 1-4v3c0 1 1 3 2 4 1 3 1 6 2 9 1 1 1 2 1 3 0 2 1 3 2 5 1 6 3 11 5 17 0 2 1 5 1 7h-1 0l-3-8c1-1-1-4-2-6-3 2-1 5-5 6h0v2h-1 0c0-1-1-1-1-1l-2 1h0l-1 2c-1 1-2 2-2 3h-1v-1h1c-1-1-2-1-2-2-1 0-1 0-1 1-1 2-3 3-5 5-1 1-1 2-3 3s-2 4-5 4c-1 1-2 2-4 2h0c-1 1-3 2-5 2v1h0l-1 1c-1 0-2 1-3 1 1-2 4-3 5-5l1-1 1-1c1-1 3-2 4-4 1-1 2-2 3-4l1-2 2 1 1-1 2-2-1-2 1-2c1-1 1-2 1-3l-1 1h0c-1 0-1 1-2 1v-1-1c1-1 1-2 1-3v2c1-1 1-1 1-2s0-5-1-6h-1v-1-2-4l1-4v-3c2-4 2-8 5-12v1 2h1v-2h1c0-1 0-1 1-2v1l1-1v1h0c1 1 1 1 1 2h0 1c1 1 1 2 1 3 0 2 1 5 2 6l1-1h-1c0-2 0-3-1-4 0-2 0-3-1-4h0l1-2 1 3h0v-2z" class="W"></path><path d="M380 454v-4c-1-5-1-10 0-14 0-2 0-4 1-5 0 1-1 4-1 5v1 10 1c0 1 1 3 1 4 1 2 0 5-1 7l-1-1c1-1 1-2 1-4z" class="h"></path><path d="M380 448c1-1 1-3 1-5l1-12h1v5c-1 2 0 5 0 7v3c-1 1-1 1 0 2v4l1 1c0 1 0 1-1 1-1 2-1 5-3 5h0c1-2 2-5 1-7 0-1-1-3-1-4z" class="Q"></path><path d="M380 420l1-1v1h0c1 1 1 1 1 2h0c1 2 1 3 1 5l3 9c0 3-1 6-1 8l1 5c1 0 1 0 1 1h1l-2 4c0 1 0 2-1 3v-2c-1 1-1 3-2 4l-1-1 1-4c1 0 1 0 1-1l-1-1v-4c-1-1-1-1 0-2h0l1-2c1-3 1-7 1-10-1-3-2-5-2-7-1-3-1-5-3-7z" class="J"></path><path d="M385 444l1 5c1 0 1 0 1 1h1l-2 4v-1c-1-1-1-6-1-9z" class="Q"></path><path d="M383 446h0l1-2v7c0 1 1 2 1 4-1 1-1 3-2 4l-1-1 1-4c1 0 1 0 1-1l-1-1v-4c-1-1-1-1 0-2z" class="Y"></path><defs><linearGradient id="b" x1="388.178" y1="441.53" x2="385.418" y2="441.243" xlink:href="#B"><stop offset="0" stop-color="#484948"></stop><stop offset="1" stop-color="#6a686a"></stop></linearGradient></defs><path fill="url(#b)" d="M385 420l1 3c1 4 2 8 2 11v3c1 4 1 9 0 12v1h-1c0-1 0-1-1-1l-1-5c0-2 1-5 1-8l-3-9c0-2 0-3-1-5h1c1 1 1 2 1 3 0 2 1 5 2 6l1-1h-1c0-2 0-3-1-4 0-2 0-3-1-4h0l1-2z"></path><path d="M372 446c0-1 0-3 1-5v-3c0-1 0-3 1-4 0-4 1-7 2-11l1 1h0c-1 3-2 7-3 11 0 1 0 3-1 5v7c1 2 0 4 2 6 0-4-1-9 0-12 1-2 0-4 1-5v-3-1h1 0 0c0 1 0 2-1 3v2 2c-1 1-1 4-1 6 1 1 1 4 1 6 1-1 1 0 1-1 1 1 1 1 1 2v5h0c-1 0 0 0-1 1v1c-1 1-1 2-2 2h0l1-2h-1c-1 0-1 1-2 3l-1-2 1-2c1-1 1-2 1-3l-1 1h0c-1 0-1 1-2 1v-1-1c1-1 1-2 1-3v2c1-1 1-1 1-2s0-5-1-6z" class="P"></path><path d="M377 450v-10c0-1 1-2 1-4h0v-1l1-8h1c0 3-1 6-2 9v3 1c0 3-1 11 2 14 0 2 0 3-1 4l1 1h0c2 0 2-3 3-5l-1 4 1 1c-1 1-1 2-1 2v2c-1-1-2-1-2-2-1 0-1 0-1 1-1 2-3 3-5 5-1 1-1 2-3 3s-2 4-5 4c-1 1-2 2-4 2h0c-1 1-3 2-5 2v1h0l-1 1c-1 0-2 1-3 1 1-2 4-3 5-5l1-1 1-1c1-1 3-2 4-4 1-1 2-2 3-4l1-2 2 1 1-1 2-2c1-2 1-3 2-3h1l-1 2h0c1 0 1-1 2-2v-1c1-1 0-1 1-1h0v-5c0-1 0-1-1-2z" class="H"></path><path d="M367 466l1-2 2 1c-1 5-5 7-8 10-1 0-2 0-2-1 1-1 3-2 4-4 1-1 2-2 3-4z" class="K"></path><path d="M386 421c0-1 0-3 1-4v3c0 1 1 3 2 4 1 3 1 6 2 9 1 1 1 2 1 3 0 2 1 3 2 5 1 6 3 11 5 17 0 2 1 5 1 7h-1 0l-3-8c1-1-1-4-2-6-3 2-1 5-5 6h0v2h-1 0c0-1-1-1-1-1l-2 1h0l-1 2c-1 1-2 2-2 3h-1v-1h1v-2s0-1 1-2 1-3 2-4v2c1-1 1-2 1-3l2-4v-1c1-3 1-8 0-12v-3c0-3-1-7-2-11h0v-2z" class="f"></path><path d="M391 448v-1-3h0 1c0 1 1 1 2 2v1c-1 1-1 2-1 3-1-1-1-2-2-2z" class="L"></path><path d="M391 448c1 0 1 1 2 2l1 1c-3 2-1 5-5 6h0v2h-1 0c0-1-1-1-1-1s0-1 1-1c2-3 2-6 3-9z" class="P"></path><path d="M386 421c0-1 0-3 1-4v3c0 1 1 3 2 4 1 3 1 6 2 9 1 1 1 2 1 3 0 2 1 3 2 5 1 6 3 11 5 17 0 2 1 5 1 7h-1 0l-3-8c1-1-1-4-2-6l-1-1c0-1 0-2 1-3v-1c-1-3-2-5-3-7l-5-16v-2z" class="l"></path><path d="M394 447c2 4 3 9 4 14 0 1 1 2 1 4h0l-3-8c1-1-1-4-2-6l-1-1c0-1 0-2 1-3z" class="f"></path><path d="M192 128c-16 2-32 6-46 12-4 2-7 3-11 5-6 4-13 9-18 14-6 5-11 11-16 17-2 4-5 8-7 12-5 10-10 21-12 33-2 10-3 19-4 29H62V90h443l-7 92-8 82h-17-2l-1 1v-1c-1-2-1-6-1-8l-3-18c0-4-1-7-2-11-2-9-4-18-7-26-10-25-27-46-53-56-8-3-16-5-25-6-12-2-25-3-38-3h-15v158c-1 2 0 7-1 8-1 14 0 28-1 42v107 63c0 16-1 34 2 51 3 11 8 22 16 31 2 3 5 6 7 9l1-1c1 2 2 3 3 4 13 11 30 21 48 20h3v6 1h-1c0 4 2 8 5 12h0l-289-1v-14l-1-1v-1c1-1 0-2 1-3h13c11 0 22-3 33-8 11-4 23-11 31-21 3-4 6-9 9-13 2-3 3-6 4-9 3-8 6-17 7-26 1-4 1-8 2-12v-4c1-4 1-9 1-13l2-25 1-8v-25l1-15v-42-9-19-70-12-6l1 1c0-1 0-2 1-2v-16h0v-1c-1 0-2-1-3-1l-1-1c1-1 0-2 0-3v-3c2 0 2-1 4-1v-2h0-3v-7-7c-1-1-1-6-1-8 0-4 1-9 0-14-1 0-2 1-3 1l-1-1h-1-1l3-2v-1s1-1 2-1h0v-1-1c0-2-1-5-1-6v-8-2-2l2-3 1 1v3h1l-1-3h1v2c1-1 0-3 0-4v-8-38c0-7-1-16 0-24h0-12c-7 1-14 1-20 2l1-1z" class="R"></path><path d="M67 140c0 1 0 2 1 3h1v3c0 1-1 3-2 3 0 2 1 3 0 4v-13z" class="H"></path><path d="M229 343v6l1 2c1 2 2 3 1 5-1 0-1-1-2 0v4h0c-1-1 0-3 0-5v-12z" class="M"></path><path d="M104 163h2l-7 10-1-2c2-3 4-5 6-8z" class="j"></path><path d="M319 158l1 11v6l-1 1c0-4 0-7-3-10h1c1-1 1-1 2-1h0v-7h0z" class="I"></path><path d="M318 191v-3-1h1c0-2 0-3 1-4v26c-1-3 0-6-1-9h0v-1h-1v-4-1-2-1z" class="H"></path><path d="M136 139v1l-16 10v-1l1-1c2-2 4-2 5-4 0-1 1-1 1-2h1c3 0 5-1 8-3z" class="D"></path><path d="M225 263c0 2-1 6 1 8l-1 1c-1 0-2-1-3-1l-1-1c1-1 0-2 0-3v-3c2 0 2-1 4-1z" class="N"></path><path d="M313 254h3 0c1 1 2 1 3 1v-1h-1v-5h1v-1-2-1h1v20c0 3 0 7-1 10v-5h-1v-2c0-1 0-2-1-3v-1l-1-1c0-2 0-3 2-4h-1 1v-3h-1l-4-2z" class="D"></path><path d="M318 259v1c0 1 0 3-1 4l-1-1c0-2 0-3 2-4z" class="f"></path><path d="M494 129c1-1 2-1 3-1l-1 17v-3h-1l-1-1c-1-1-1-3 0-5l-1-1h-1v1h-2c1-1 1-1 1-2l-2-2c1 0 2-1 2-2l2-1h1z" class="B"></path><path d="M494 129h2v1c-2 0-2 0-3 1v2c-1 0-1 0-2 1l-2-2c1 0 2-1 2-2l2-1h1z" class="O"></path><path d="M115 147h3c1 0 2 0 3 1l-1 1v1l-11 10v-1c0-1 1-2 2-3 2-3 2-5 1-9l1 1c1-1 1-1 2-1z" class="J"></path><path d="M115 147h3c1 0 1 1 2 1h0c-1 2-3 2-4 3-1 0-1 0-1-1h0l-2-2c1-1 1-1 2-1z" class="i"></path><path d="M100 162h4 0v1c-2 3-4 5-6 8l1 2c-2 2-4 4-5 6-1-1-1-2-2-3h1c1-4 3-8 5-11v-2h1l1-1z" class="f"></path><path d="M319 176l1-1v8c-1 1-1 2-1 4h-1v1 3 1 2-1c-2-2-3-2-6-3h-1c1-2 1-4 1-5s0-1 1-1c1-1 2-1 3-1v-2l1-1c1-1 1-2 2-4z" class="i"></path><path d="M317 180s1 0 1 1 0 1-1 2h-1v-2l1-1z" class="I"></path><path d="M312 190c0-1 0-1 1-2 1 0 2 0 3 1l2 2v1 2-1c-2-2-3-2-6-3z" class="h"></path><path d="M318 270h1v5c0 7 1 14 0 21 0-2 0-4-1-5v-3c0-3-1-5-2-7-3-4-5-4-10-5h0l-1-1c3-1 6 0 9-1 2 0 3-3 4-4z" class="B"></path><path d="M69 146l2-3h1c0 1 1 2 1 2-1 1-2 5-3 6-1 0-1 0-2 1h1s0 1 1 1l1-1 1-1v2c-3 6-4 13-4 20 0 2 0 5-1 7v3 9c-1-3-1-6-1-9l1-30c1-1 0-2 0-4 1 0 2-2 2-3z" class="f"></path><path d="M230 183v-5h0 1c0 2 1 3 3 5 0 0 1 0 2 1h0v1s-1 1-1 2v1c1 1 1 1 2 1v3h0l-2-1c-1 0-2-1-3 0s-2 2-2 4c0 1-1 4 0 6h2l3 3v1l-2 12v4-1c0-1-1-3 0-4h0-1c-1 0-2 1-2 2l-1-1 1-34z" class="B"></path><path d="M494 95h6l-1 15c-1 4-1 7-1 10v-4l-1 1v-1c-1-5-4-6-7-9l-4-4c1-1 2-3 3-3l3-3h1l-2-1h1 2 0 0c1 1 1 0 2 1h0 0v-1h1l-7-1h4 0z" class="L"></path><path d="M493 97c1 0 2 0 3 1 1 2 1 2 1 3-1 2-2 4-3 4-1 1-2 1-2 0-2 0-3-1-3-2v-1h1c1 1 1 1 3 1 1 0 1-1 2-2v-2c-1-1-2-1-3-2h1z" class="X"></path><path d="M230 140v9h0c1 4 0 8 4 11 2 2 5 3 8 3 1 0 2-1 4-2v-1l3-3h0c1 0 2 0 2-1h0v-1h0c0-1 1-2 1-3 2 2 5 6 5 8-3 0-5 1-7 1 0 1 1 1 2 2-1 0-1 1-2 1h-4c-6 2-11 3-14 8 0 2-1 3-1 5v1h-1 0v5-43z" class="D"></path><path d="M246 164c1-1 2-1 2-1l1-1 1-1c0 1 1 1 2 2-1 0-1 1-2 1h-4z" class="J"></path><path d="M235 198l1 4c0 1 0 3 1 4v4c-2 5-2 9-2 14l1 1c0 1 0 2 1 3l-1 1c0 1 0 3 1 4h0c1 0 1-1 1-2 0 4 0 6 1 9l2 2c-1 1-2 1-2 1h-1 0v-1c-2-2-6-2-8-4-1 0-1-1-1-2v-6-13l1 1c0-1 1-2 2-2h1 0c-1 1 0 3 0 4v1-4l2-12v-1l-3-3c1 0 1-1 2-2l1-1z" class="D"></path><path d="M235 232h0c1 2 1 4 1 6-1-1-2-1-3-1h0v-1c1 0 1-1 1-1l1-1v-2z" class="K"></path><path d="M236 229c0 1 0 3 1 4h0c1 0 1-1 1-2 0 4 0 6 1 9-2 0-2-1-3-2 0-2 0-4-1-6h1v-3z" class="Q"></path><path d="M235 198l1 4c0 1 0 3 1 4v4c-2 5-2 9-2 14l1 1c0 1 0 2 1 3l-1 1v3h-1 0l-1-4c-1-4 0-7-1-11l2-12v-1l-3-3c1 0 1-1 2-2l1-1z" class="a"></path><path d="M235 198l1 4-1 4v-1-1l-3-3c1 0 1-1 2-2l1-1z" class="C"></path><path d="M331 590c5 8 11 16 19 23h-2v1h0c-2-3-6-4-9-4-2 0-3 1-5 2s-4 1-6 3l-1-1 1-2c0-1 1-2 1-2 0-1-1-1-1-2h2v-1l-1-2c-1-1-1-2-1-2v-1c-1-1-1-2-2-4h1c1 0 3 1 4 0-1-1-1-3-2-4h-1 1l1-1c0-2 0-2 1-3z" class="i"></path><path d="M327 598c1 0 3 1 4 0 1 1 2 1 3 2l-4 2h0c0-1 0-1-1-1l-1 1c-1-1-1-2-2-4h1z" class="h"></path><path d="M334 600c1 1 3 2 3 3 1 1 1 2 0 3v1c-1-1-2-1-2-1-2-2-4-1-5-4l4-2z" class="U"></path><path d="M223 290l1 1c0-1 0-2 1-2l-1 125v29c0 4 0 9-1 13 0 3 1 5 0 8v14l-1 20c0 4 0 9-1 13-1 8 0 18-2 26-1 4-1 9-2 13l-3 15c-1 2-1 4-2 6-3 7-6 14-10 20-2 3-4 5-6 8-2 2-4 5-7 6-4 4-8 7-13 10l-6 3c-2 1-5 3-8 3v1l-9 3c-7 1-12 2-19 3h0-7c-2 1-4 1-6 0-1 0-1 0-2-1h13c11 0 22-3 33-8 11-4 23-11 31-21 3-4 6-9 9-13 2-3 3-6 4-9 3-8 6-17 7-26 1-4 1-8 2-12v-4c1-4 1-9 1-13l2-25 1-8v-25l1-15v-42-9-19-70-12-6z" class="G"></path><path d="M325 302V135c6 1 13 0 20 0l23 2c7 0 14 1 21 2 16 4 31 12 43 23 11 10 20 24 26 39 3 9 6 19 8 29 2 11 4 23 5 34l-1 1v-1c-1-2-1-6-1-8l-3-18c0-4-1-7-2-11-2-9-4-18-7-26-10-25-27-46-53-56-8-3-16-5-25-6-12-2-25-3-38-3h-15v158c-1 2 0 7-1 8z" class="U"></path><path d="M192 128c2-1 5-1 8-1 8-1 16-1 25-1v134c1 1 3 1 4 0 0-1-1-16 0-19v19 1h0 0c-2 1-3 0-4 0h0-3v-7-7c-1-1-1-6-1-8 0-4 1-9 0-14-1 0-2 1-3 1l-1-1h-1-1l3-2v-1s1-1 2-1h0v-1-1c0-2-1-5-1-6v-8-2-2l2-3 1 1v3h1l-1-3h1v2c1-1 0-3 0-4v-8-38c0-7-1-16 0-24h0-12c-7 1-14 1-20 2l1-1z" class="X"></path><path d="M222 216v31c-1-1-1-6-1-8 0-4 1-9 0-14-1 0-2 1-3 1l-1-1h-1-1l3-2v-1s1-1 2-1h0v-1-1c1-1 1-2 2-3z" class="Y"></path><path d="M219 201l2-3 1 1v17c-1 1-1 2-2 3 0-2-1-5-1-6v-8-2-2z" class="I"></path><path d="M229 360v1h2 0v-2c0-1 1-1 1-2h0v1c1 0 2-1 2-1-2 4-5 12-4 17h0c1 1 1 2 1 4h-2v18 5c1 0 1 1 1 1v1c0-1 1-2 1-3h1c1 3 2 6 4 8l2 2c-1 1-1 1-2 1h0 2 2l1 2v1c1 4 3 8 6 11h-2c-1 0-2 0-3-1h-1c-1 1 0 1-1 2h-1l-1-2h-1v3l-2 1c2 1 3 3 5 3l-1 2 3 3-1 1c-2-1-6-2-8-1l-1 1c0 1 1 2 1 3-1 1 0 2 0 3-2-1-2-1-3-2h-1c0 2 1 11 0 12v-93h0z" class="H"></path><path d="M235 410l1 1h0c-2 1-3 3-4 5s-1 6-1 8v-2c-1 0-1-1-1-2h1v-2l-1 1h0c0 2 0 2-1 3v1-4c0-1 1-1 1-1 0-2 1-4 2-5 0-1 0-2 1-3h2z" class="L"></path><path d="M230 403c0-1 1-2 1-3h1c1 3 2 6 4 8l2 2c-1 1-1 1-2 1l-1-1c-1-2-2-3-4-3h0v-1c1 0 1 0 2-1h0c-1-1-2-1-2-2h-1zm-1 20v-1c1-1 1-1 1-3h0l1-1v2h-1c0 1 0 2 1 2v2c1 3 3 6 5 7 1 1 2 1 3 2l3 3-1 1c-2-1-6-2-8-1l-1 1c0 1 1 2 1 3-1 1 0 2 0 3-2-1-2-1-3-2h-1v-9-2-7z" class="I"></path><path d="M229 430l3 2-1 2c-1-1-1-2-2-2v-2z" class="L"></path><path d="M232 437c0 1 1 2 1 3-1 1 0 2 0 3-2-1-2-1-3-2l2-4z" class="a"></path><path d="M232 432l2 2c-1 1-3 2-4 3h0c0-2 0-2 1-3l1-2z" class="H"></path><path d="M236 411h2 2l1 2v1c1 4 3 8 6 11h-2c-1 0-2 0-3-1h-1c-1 1 0 1-1 2h-1l-1-2h-1v3l-2 1c2 1 3 3 5 3l-1 2c-1-1-2-1-3-2-2-1-4-4-5-7 0-2 0-6 1-8s2-4 4-5z" class="L"></path><path d="M235 418l2-3h1c1 1 2 3 2 4l-1 1c-1 0-2 0-3 1h-1v-1-2z" class="e"></path><path d="M235 415v3 2 1h1c1-1 2-1 3-1 1 1 1 1 0 2v1l-1 1h-1v3l-2 1c0-1-1-2-1-3-1-3-1-7 1-10z" class="B"></path><path d="M239 420c1 1 1 1 0 2l-3 1-1-1v-1h1c1-1 2-1 3-1z" class="U"></path><path d="M236 411h2c-1 1-2 2-3 4-2 3-2 7-1 10 0 1 1 2 1 3 2 1 3 3 5 3l-1 2c-1-1-2-1-3-2-2-1-4-4-5-7 0-2 0-6 1-8s2-4 4-5z" class="E"></path><defs><linearGradient id="c" x1="76.658" y1="223.645" x2="67.114" y2="221.816" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252426"></stop></linearGradient></defs><path fill="url(#c)" d="M67 180l2 7c1 0 1 0 1 2l1 1c1 3 5 12 8 13h2c0-1 1-2 1-3h1l-3 9c-4 12-6 23-6 35-2 0-5 0-7 1v-45-8-9-3z"></path><path d="M72 195c2 3 5 7 5 11v1-1c-3-2-4-6-5-9v-2z" class="G"></path><path d="M69 187c1 0 1 0 1 2l1 1c0 2 0 3 1 5v2c0-1-1-1-1-2h-1v3l-1-3c1-2 0-6 0-8z" class="S"></path><path d="M67 180l2 7c0 2 1 6 0 8-1 3-1 5-1 7v1l-1 1v-4-8-9-3z" class="I"></path><path d="M70 198v-3h1c0 1 1 1 1 2 1 3 2 7 5 9v1h-1c-1 0-2-1-2-2-1 1 0 3 0 4-1 4-2 9-4 12-1-4 0-8 1-13 0-3-1-7-1-10z" class="Z"></path><path d="M235 428l2-1v-3h1l1 2h1c1-1 0-1 1-2h1c1 1 2 1 3 1h2l5 4 1-1c1 1 3 2 3 2 1 0 3-1 3-1l2 2s2 1 2 2v3 1h0c1 1 1 1 1 2 1 2 1 3 1 5l-1 1-2-2h-1c-1 1-1 2-2 4v1h0l-1-1c-1-1-1-1-3-1v1h0c0 1 1 1 1 2l1 2c-1 0-3-1-5-1l-3-2c-1 0-2-1-3-1-1-1-2-1-3-2h-1-1c-1 0-1 0-2-1h-3c-1 0-2-1-3-1 0-1-1-2 0-3 0-1-1-2-1-3l1-1c2-1 6 0 8 1l1-1-3-3 1-2c-2 0-3-2-5-3z" class="B"></path><path d="M253 428c1 1 3 2 3 2 1 0 3-1 3-1l2 2h-4c0 1 0 1 1 2 0 1 1 2 1 3v1c-1 1 0 1-1 2h0c-1-3-2-1-4-2h0l1-1v-2c1 0 1-1 1-1l-4-4 1-1z" class="a"></path><path d="M256 433c1 1 2 2 2 3l-1 1h-3 0l1-1v-2c1 0 1-1 1-1z" class="I"></path><path d="M250 435l1 1 3 1h0c2 1 3-1 4 2v1 7c-1-1-1-1-3-1v1l-1-1c0-3-1-5-3-7l-2-3s1 0 1-1z" class="F"></path><path d="M254 440v-1c1-1 2 0 3 0l1 1h-1c0 2 0 1 1 2v2l-1 1c-1-1-2-1-3-2 0-1 1-2 0-3h0z" class="Z"></path><path d="M250 435l1 1 3 1h0c2 1 3-1 4 2v1h0l-1-1c-1 0-2-1-3 0v1c-1 0-2-1-3-1l-2-3s1 0 1-1z" class="M"></path><path d="M247 425l5 4 4 4s0 1-1 1v2l-1 1-3-1-1-1c0 1-1 1-1 1l-3-3v-4c1 0 1-1 1-2s-1-1-2-2h2z" class="B"></path><path d="M251 433v-1c1 1 1 2 1 3 0 0-1 0-1 1l-1-1c0-1-1-1-1-2h2z" class="i"></path><path d="M246 429h1 2c1 1 1 3 2 4h-2c0 1 1 1 1 2s-1 1-1 1l-3-3v-4z" class="e"></path><path d="M235 428l2-1v-3h1l1 2h1c1-1 0-1 1-2h1c1 1 2 1 3 1 1 1 2 1 2 2s0 2-1 2v4l3 3 2 3c2 2 3 4 3 7l1 1h0c-1 0-2-1-2-2 0 0-1-1-1-2l-1 1-1-1c-1 0-1-1-2-2v-1c-1 0-2 0-3-1v-1-1c-1-1-2-2-3-2v1l-3-3 1-2c-2 0-3-2-5-3z" class="I"></path><path d="M240 431c2 1 4 2 6 4l-1 1c0 1 1 1 1 1l-1 1v-1c-1-1-2-2-3-2v1l-3-3 1-2z" class="C"></path><path d="M246 435c2 2 4 3 4 5 1 1 2 2 2 3l-1 1-1-1c-1 0-1-1-2-2v-1c-1 0-2 0-3-1v-1l1-1s-1 0-1-1l1-1z" class="X"></path><path d="M239 426h1c1-1 0-1 1-2h1c1 1 2 1 3 1 1 1 2 1 2 2s0 2-1 2v4c-1 0-2-1-2-1 0-1-1-1-1-1-3-1-3-3-4-5z" class="N"></path><path d="M239 426h1c1-1 0-1 1-2h1v1c0 1 1 1 1 1v5c-3-1-3-3-4-5z" class="F"></path><defs><linearGradient id="d" x1="242.471" y1="445.815" x2="244.814" y2="436.097" xlink:href="#B"><stop offset="0" stop-color="#a5a3a5"></stop><stop offset="1" stop-color="#c6c4c4"></stop></linearGradient></defs><path fill="url(#d)" d="M242 436v-1c1 0 2 1 3 2v1 1c1 1 2 1 3 1v1c1 1 1 2 2 2l1 1 1-1c0 1 1 2 1 2 0 1 1 2 2 2 0 1 1 1 1 2l1 2c-1 0-3-1-5-1l-3-2c-1 0-2-1-3-1-1-1-2-1-3-2h-1-1c-1 0-1 0-2-1h-3c-1 0-2-1-3-1 0-1-1-2 0-3 0-1-1-2-1-3l1-1c2-1 6 0 8 1l1-1z"></path><path d="M233 443c0-1-1-2 0-3 1 0 2 0 4 1 1 0 1 0 2 1 1 0 1-1 2-1h2l1 1c1 0 2 2 2 2v1h0-3-1-1c-1 0-1 0-2-1h-3c-1 0-2-1-3-1z" class="E"></path><path d="M318 199h1v1h0c1 3 0 6 1 9v4 13 19h-1v1 2 1h-1v5h1v1c-1 0-2 0-3-1h0-3 0c-1 0-1 0-2-1l1-1v-1h-1c-1-1-1-2-1-3h0c-1 0-1 1-1 1 0 2-1 3-1 4l-1 3c-1 0-1 1-1 1h-1v-2l1-1h-1l-1 1h-3l2-3c0-2 2-3 3-4 3-4 5-8 6-13-1-2 0-4-1-5v-6c-1-1-1-1-2-1-1-1-1-2-1-4 1-1 0-5 0-7l1 2h1v-3h0v-2c1-4 3-6 5-8l3-2z" class="f"></path><path d="M317 207c2 1 2 2 2 4v1h-1c-1-1-2-2-2-3l1-2z" class="T"></path><path d="M315 201c0 2 0 2-1 3-1 2-2 5-2 8v-2h-1v2l-1-3c1-4 3-6 5-8z" class="P"></path><path d="M320 226h0c-1-1-1-2-2-3 0-2 1-6 0-7v-2c0-1 1-1 2-1v13z" class="B"></path><path d="M313 217l1-5v-1-2c1-1 1-2 2-3 0 0 0 1 1 1h0l-1 2c-1 1-1 1-1 2v4c0 2 0 4 1 6 0 1 1 3 1 5h-1c-1 1 0 3-1 4v-6-1c-1-2 0-5-1-7h0l-1 1h0z" class="m"></path><path d="M310 211v-2l1 3v-2h1v2 3h-1v4 5c-1-1-1-1-2-1-1-1-1-2-1-4 1-1 0-5 0-7l1 2h1v-3h0z" class="C"></path><path d="M310 211v-2l1 3v-2h1v2 3h-1v4c-1-3-1-6-1-8z" class="K"></path><path d="M312 215v2h1 0l1-1h0c1 2 0 5 1 7v1 6c1-1 0-3 1-4h1c1 7 1 13 0 20-1 3-2 5-4 7v1c-1 0-1 0-2-1l1-1v-1h-1c-1-1-1-2-1-3h0c-1 0-1 1-1 1 0 2-1 3-1 4l-1 3c-1 0-1 1-1 1h-1v-2l1-1h-1l-1 1h-3l2-3c0-2 2-3 3-4 3-4 5-8 6-13-1-2 0-4-1-5v-6-5-4h1z" class="c"></path><path d="M312 215v2h1 0c-1 4-1 9-1 13v5c-1-2 0-4-1-5v-6-5-4h1z" class="D"></path><defs><linearGradient id="e" x1="314.644" y1="230.684" x2="317.833" y2="240.538" xlink:href="#B"><stop offset="0" stop-color="#878787"></stop><stop offset="1" stop-color="#a8a8a7"></stop></linearGradient></defs><path fill="url(#e)" d="M315 223v1 6c1-1 0-3 1-4h1c1 7 1 13 0 20l-1-4v-1c-1-1 0-1-1-2v6c0 1 0 2-1 2h-1c1-3 1-5 1-8l1-16z"></path><path d="M80 146l1 1h-1v1 1s-1 1-1 2v4c1 3 2 6 2 8v-3c2 1 3 3 4 4h0c1 0 2 2 3 2h2 5c1-1 1-1 3-1-2 3-4 7-5 11h-1c1 1 1 2 2 3 0 1-1 2-2 3-4 5-6 12-9 18h-1c0 1-1 2-1 3h-2c-3-1-7-10-8-13l-1-1c0-2 0-2-1-2l-2-7c1-2 1-5 1-7 0-7 1-14 4-20 1 0 2-3 2-4l1 1 4-3c0-1 1-1 1-1z" class="Z"></path><path d="M80 148v1s-1 1-1 2v4c1 3 2 6 2 8-1 2-2 4-5 5v1h0l1 1h0v3l1 2-1 1h1c0 1 1 2 1 3h1-4 0c-1 0-1 0-1-1-1-1-1 0-1-1v-1h0-1l-1-1-1 1v-1c-2-9 4-19 9-27z" class="I"></path><path d="M74 168h2v1h0l1 1h0v3c-1-1-3-2-4-3 0-1 1-2 1-2z" class="F"></path><path d="M74 168h2v1h0l1 1h-4c0-1 1-2 1-2z" class="c"></path><path d="M79 155c1 3 2 6 2 8-1 2-2 4-5 5h-2v-3h1c-1-1-1-2-1-4 0-1 1-2 2-3v-1h2 0c1-1 1-1 1-2z" class="B"></path><path d="M76 158c1 0 1 2 2 3l-2 4h-1c-1-1-1-2-1-4 0-1 1-2 2-3z" class="U"></path><path d="M73 176h1 0v1c0 1 0 0 1 1 0 1 0 1 1 1h0 4 0c2 2 3 5 6 5h1c1-2 2-3 1-4h1c1 0 3 1 3 2-4 5-6 12-9 18h-1-1l-1 1h0c-1-1-2-2-2-3s-1-2-1-3v-1l1 1h1l-2-2c1-1 2-1 3-2v-1c-2 1-3 1-5 0-1-1-2-2-3-4h0l-2-2v-2c1-1 1-1 1-2v-1h0v-2c1 0 1 1 2 1v-2z" class="n"></path><path d="M72 186h0l-2-2v-2c1-1 1-1 1-2v-1c0 3 1 4 3 5v1 1h2v1h-1c-1 0-2-1-3-1z" class="S"></path><path d="M74 184h1c1 1 2 1 3 1l3-2h1l-1 1c2 2 5 2 5 5-1 1-1 2-2 2-2 0-2 0-3-1l-1-3h-2-3 1v-1h-2v-1-1z" class="X"></path><path d="M81 163v-3c2 1 3 3 4 4h0c1 0 2 2 3 2h2 5c1-1 1-1 3-1-2 3-4 7-5 11h-1c1 1 1 2 2 3 0 1-1 2-2 3 0-1-2-2-3-2h-1c1 1 0 2-1 4h-1c-3 0-4-3-6-5h0-1c0-1-1-2-1-3h-1l1-1-1-2v-3h0l-1-1h0v-1c3-1 4-3 5-5z" class="Z"></path><path d="M77 170l1 1c1-1 1-1 1-2h1v1c0 1-1 2 0 3l1 1s0 1 1 1c1 1 3 2 4 3v-2-1 1c1 1 2 3 2 4 1 1 0 2-1 4h-1c-3 0-4-3-6-5h0-1c0-1-1-2-1-3h-1l1-1-1-2v-3z" class="M"></path><path d="M85 164h0c1 0 2 2 3 2h2 5c1-1 1-1 3-1-2 3-4 7-5 11h-1c1 1 1 2 2 3 0 1-1 2-2 3 0-1-2-2-3-2h-1c0-1-1-3-2-4v-1c-1-2-2-2-2-5h4 0c-1-3-2-4-3-6z" class="d"></path><path d="M86 176c1-2-1-3-1-5h1c1 1 2 1 2 3 1 1 3 2 4 2 1 1 1 2 2 3 0 1-1 2-2 3 0-1-2-2-3-2h-1c0-1-1-3-2-4z" class="C"></path><path d="M104 134c1 0 1 0 2-1h1 1c1-1 1-1 2 0h4c0 1 1 2 1 3l9 3c-1 0-1 0-2 1 1 0 2 1 3 2h2c0 1-1 1-1 2-1 2-3 2-5 4-1-1-2-1-3-1h-3c-1 0-1 0-2 1l-1-1c1 4 1 6-1 9-1 1-2 2-2 3v1l-3 3h-2v-1h0-4l-1 1h-1v2c-2 0-2 0-3 1h-5-2c-1 0-2-2-3-2h0c-1-1-2-3-4-4v3c0-2-1-5-2-8v-4c0-1 1-2 1-2v-1-1h1l-1-1s-1 0-1 1l-4 3-1-1c3-4 6-8 11-10l-1-1 3-2c2-1 5-1 7-2h2v1c3-1 6-1 8-1z" class="c"></path><path d="M105 161c1 0 2-2 4-2v1l-3 3h-2v-1h0l1-1z" class="H"></path><path d="M97 156c1 0 2-1 3-2l1-1h0l1 2c1 0 1 1 2 1h-2-4 0 1c1 0 1 1 2 1h1s0 1 1 1h2c0 1 0 2-1 2l1 1-1 1h-4l-1 1h-1l-3-1c0-1-1-2-1-2v-1l1 1c1-1 1-2 2-2l-1-2h1z" class="E"></path><path d="M94 159l1 1c2 0 4 1 5 2l-1 1h-1l-3-1c0-1-1-2-1-2v-1z" class="H"></path><path d="M88 144c4-2 9-3 14-1 2 0 3 1 4 2 2 2 4 4 4 7 0 1-1 3-2 4h-3 0c0-1 0-1 1-1l2-2c1-1 0-1 0-2s0-2-1-2c-2-2-3-3-5-3-1 0-2-2-3-3-1 2-2 3-2 4v1h0l-2-1v1c0 1 0 1 1 2v1l-1-1s-1 0-1-1c-1 0-2 0-2-1h-2-1c-1 1-1 1-2 0 0-1 1-2 1-4z" class="i"></path><path d="M95 150l1 1v-1c-1-1-1-1-1-2v-1l2 1h0v-1c0-1 1-2 2-4 1 1 2 3 3 3 2 0 3 1 5 3 1 0 1 1 1 2s1 1 0 2l-2 2c-1 0-1 0-1 1h-1c-1 0-1-1-2-1l-1-2h0l-1 1c-1 1-2 2-3 2-1-1-1-2-2-3h1c-1 0-2-1-2-1-1 0-1 0-1-1h0 0c1-1 1-1 2-1z" class="R"></path><path d="M96 153c1-1 2-2 3-2 1 1 0 1 1 2 1-1 2-1 3 0-1 1-1 1-1 2l-1-2h0l-1 1c-1 1-2 2-3 2-1-1-1-2-2-3h1z" class="H"></path><path d="M103 153v-1h-1v-1l1-1v-2c2-1 3 2 5 3h0c0 1 1 1 0 2l-2 2c-1 0-1 0-1 1h-1c-1 0-1-1-2-1 0-1 0-1 1-2zm-15-9c0 2-1 3-1 4 1 1 1 1 2 0h1 2c0 1 1 1 2 1 0 1 1 1 1 1-1 0-1 0-2 1h0 0c0 1 0 1 1 1 0 0 1 1 2 1h-1c1 1 1 2 2 3h-1l1 2c-1 0-1 1-2 2l-1-1v1s1 1 1 2c-1 0-2 1-3 1h-2c-2 0-5-2-7-4-1-2-2-5-1-7 1-4 3-6 6-8z" class="B"></path><path d="M95 153c1 1 1 2 2 3h-1 0c-1 0-1 0-2-1h-2l1-1 2-1z" class="L"></path><path d="M96 156h0l1 2c-1 0-1 1-2 2l-1-1-2-1c2-1 3-1 4-2z" class="T"></path><path d="M94 149c0 1 1 1 1 1-1 0-1 0-2 1h0 0c0 1 0 1 1 1 0 0 1 1 2 1h-1l-2 1-2-1c0-1 0-2 1-3 0 0 1-1 2-1z" class="K"></path><path d="M90 163c0-1 1-1 0-2h-1 0c-1-1-1 0-1-1-1 0-1-1-1-2h4c0 1 1 0 1 0l2 1v1s1 1 1 2c-1 0-2 1-3 1h-2z" class="L"></path><path d="M94 160s1 1 1 2c-1 0-2 1-3 1v-2c1 0 1-1 2-1z" class="K"></path><path d="M104 134c1 0 1 0 2-1h1 1c1-1 1-1 2 0h4c0 1 1 2 1 3l9 3c-1 0-1 0-2 1 1 0 2 1 3 2h2c0 1-1 1-1 2-1 2-3 2-5 4-1-1-2-1-3-1h-3c-1 0-1 0-2 1l-1-1c-1-2-2-4-4-5l-5-3h-3l-9 1h-2c-1 1-3 2-4 2s-2 1-2 1l-1 1-2 2s-1 0-1 1l-4 3-1-1c3-4 6-8 11-10l-1-1 3-2c2-1 5-1 7-2h2v1c3-1 6-1 8-1z" class="R"></path><path d="M104 137c3-1 5-1 8 0l7 2h-4l-2-1c-2 0-4-1-6-1h-3z" class="C"></path><path d="M94 134h2v1c-2 1-5 1-7 2-1 0-3 2-4 2l-1-1 3-2c2-1 5-1 7-2z" class="b"></path><path d="M104 134c1 0 1 0 2-1h1 1c1-1 1-1 2 0h4c0 1 1 2 1 3-3-1-7-2-11-2z" class="I"></path><path d="M91 140c2-3 10-3 13-3h3c-2 1-3 2-4 2h-3l-9 1z" class="g"></path><path d="M107 137c2 0 4 1 6 1l2 1h4c1 0 2 1 3 1s2 1 3 2h2c0 1-1 1-1 2-1 2-3 2-5 4-1-1-2-1-3-1h-3c-1 0-1 0-2 1l-1-1c-1-2-2-4-4-5l-5-3c1 0 2-1 4-2z" class="E"></path><path d="M123 142h2 2c0 1-1 1-1 2-2 0-3 1-4 2h0c-2 0-3 0-4-1v-2l-1-1c3 0 2 2 4 3 0-1 0-1 1-2 0 0 0-1 1-1z" class="L"></path><path d="M113 138l2 1h4c1 0 2 1 3 1s2 1 3 2h-2c-1 0-1 1-1 1-1 1-1 1-1 2-2-1-1-3-4-3h-1c0 1 0 1-1 2 0-2 0-2-1-3h0l-2-2 1-1z" class="I"></path><path d="M119 139c1 0 2 1 3 1s2 1 3 2h-2l-8-3h4z" class="G"></path><path d="M107 137c2 0 4 1 6 1l-1 1 2 2-1 1 1 1h0c-1 0-1-1-2-1 0 1 0 2 1 3l2 2c-1 0-1 0-2 1l-1-1c-1-2-2-4-4-5l-5-3c1 0 2-1 4-2z" class="f"></path><path d="M108 142v-1h-1l1-1 2 2h2v-1l-1-1h0v-1h1l2 2-1 1 1 1h0c-1 0-1-1-2-1 0 1 0 2 1 3l2 2c-1 0-1 0-2 1l-1-1c-1-2-2-4-4-5z" class="B"></path><defs><linearGradient id="f" x1="443.617" y1="95.105" x2="443.622" y2="104.292" xlink:href="#B"><stop offset="0" stop-color="#1e1c1c"></stop><stop offset="1" stop-color="#393a3b"></stop></linearGradient></defs><path fill="url(#f)" d="M387 95h107 0-4l7 1h-1v1h0 0c-1-1-1 0-2-1h0 0-2-1l2 1h-1l-3 3c-1 0-2 2-3 3-2-2-3-2-5-3-4-1-7-1-11 1-3 3-4 4-5 8v1c1 0 1 0 1-1 0 2 0 3 1 5l1 8h1l-2 2v1h0-1l-1-2c-1-1-1-2-2-3h0v3h-1l-1-2-1 1h0c-3-2-1-2-2-4l1-2 1-1c1-1 1-2 1-4 0-1-2-2-3-3s-2-2-3-2h0l-2-1v-1c-2 0-4-1-5-3 1 0 1 0 1-1l-1-1v-1h1-3c-2 0-3-1-5 0-3-1-6-1-9-1-3 1-4 1-7 4h-1c-2 0-3-1-5-2-2 0-4 0-6-1l-1 1c0 1 1 2 2 3-1 0-1 0-2 1v-1c-1 1-2 1-2 2l-1 1c0-1 0-1-1-2l-3 2v2 2h0l-1 2v2h-1v-3c0-1-1-1-1-1v-1s-1 0-1-1v3h-1 0c-1-1-3-1-4 0h0-1c-1 1-3 2-4 4h0c0 1 0 1-1 1 0 1 0 2-1 2 0 2-2 4-3 5h0l-1-1c0-1 1-2 1-2v-1-2-6c0-1-1-1-1-2s-1-1-1-2h0c0-2-3-5-4-7 1-2 5-3 7-4h0z"></path><path d="M473 97h3 1c1 1 3 0 4 1s0 1 0 2c-4-1-7-1-11 1l-1-1c1-1 4-2 4-3z" class="K"></path><path d="M451 97c3 0 4 0 7 2 1 1 3 2 4 2 0 1 0 2-1 2v1c1 1 1 2 1 3s0 3-1 4h0c0-1-2-2-3-3s-2-2-3-2h0l-2-1v-1c-2 0-4-1-5-3 1 0 1 0 1-1l-1-1v-1h1l2-1z" class="c"></path><path d="M456 102h0c2 1 3 3 4 4l1 1h0v-3c1 1 1 2 1 3s0 3-1 4h0c0-1-2-2-3-3l1-1c-1-2-2-3-3-4v-1z" class="G"></path><path d="M449 98l2-1c-1 2-1 2-1 3 2 1 4 1 6 2v1c1 1 2 2 3 4l-1 1c-1-1-2-2-3-2h0l-2-1v-1c-2 0-4-1-5-3 1 0 1 0 1-1l-1-1v-1h1z" class="E"></path><path d="M398 101c1 0 2-2 4-3 3-2 9-1 13-1s7 1 9 4c-2 0-3-1-5-2-2 0-4 0-6-1l-1 1c0 1 1 2 2 3-1 0-1 0-2 1v-1c-1 1-2 1-2 2l-1 1c0-1 0-1-1-2l-3 2v2 2h0l-1 2v2h-1v-3c0-1-1-1-1-1v-1s-1 0-1-1v-1c-1-2-3-2-4-3l-1-2h1 0 1z" class="C"></path><path d="M408 99l5-1-1 1c0 1 1 2 2 3-1 0-1 0-2 1v-1c-1-1-3-2-4-3z" class="N"></path><path d="M397 103l-1-2h1 0 1v1c1 1 3 0 4 1 0 1 0 2-1 3h0c-1-2-3-2-4-3z" class="I"></path><path d="M408 103h-2c-1-1-3-2-3-3s0-1 1-1c1-1 2-1 4 0 1 1 3 2 4 3-1 1-2 1-2 2l-1 1c0-1 0-1-1-2z" class="f"></path><path d="M473 97c0 1-3 2-4 3l1 1c-3 3-4 4-5 8v1c1 0 1 0 1-1 0 2 0 3 1 5l1 8h1l-2 2v1h0-1l-1-2c-1-1-1-2-2-3h0v3h-1l-1-2-1 1h0c-3-2-1-2-2-4l1-2 1-1c1-1 1-2 1-4h0c1-1 1-3 1-4 1-2 2-4 3-5 3-3 4-4 8-5z" class="l"></path><path d="M465 120l1-1 2 3h1l-2 2v1h0l-2-5z" class="N"></path><path d="M459 116l1-1c0 2 0 4 1 6l-1 1h0c-3-2-1-2-2-4l1-2z" class="J"></path><path d="M465 120v-5c0-2-1-4 0-6v1c1 0 1 0 1-1 0 2 0 3 1 5l1 8-2-3-1 1z" class="a"></path><path d="M473 97c0 1-3 2-4 3-3 3-6 8-6 13 0 1 0 3-1 5 0-2-1-4-1-7 1-1 1-3 1-4 1-2 2-4 3-5 3-3 4-4 8-5z" class="M"></path><path d="M387 95c1 0 2 1 4 2 1 1 2 2 2 3-1 2-1 4-2 5v1c2-2 3-3 6-3 1 1 3 1 4 3v1 3h-1 0c-1-1-3-1-4 0h0-1c-1 1-3 2-4 4h0c0 1 0 1-1 1 0 1 0 2-1 2 0 2-2 4-3 5h0l-1-1c0-1 1-2 1-2v-1-2-6c0-1-1-1-1-2s-1-1-1-2h0c0-2-3-5-4-7 1-2 5-3 7-4z" class="X"></path><path d="M391 97c1 1 2 2 2 3-1 2-1 4-2 5v1c0 1-1 2-1 3-1 2-1 5-3 6h0l1-4v-1c1-1 0-1 1-2s0-2 0-3l2-8z" class="E"></path><path d="M425 101c3-3 4-3 7-4 3 0 6 0 9 1 2-1 3 0 5 0h3-1v1l1 1c0 1 0 1-1 1 1 2 3 3 5 3v1l2 1h0c1 0 2 1 3 2s3 2 3 3c0 2 0 3-1 4l-1 1-1 2c1 2-1 2 2 4h0l1-1 1 2h1v-3h0c1 1 1 2 2 3l1 2h1 0c1 1 3 2 3 4 2 1 4 4 6 5h1v1l3 3-6-3c0-1-1-3-2-4-1 0-4 0-5-1v1h-8l-2 1h0-1c-2 1-5 0-8-1s-5-2-7-4l-1-1-3 1c-1 0-1-1-2-1s-3-1-4-2h-1 0c-3-2-4-5-5-8l-2-6v-4-1-3l2-1z" class="R"></path><path d="M444 103c1 1 2 1 3 1v1h1l-2 1c-1 0-1 0-2 1l-1-1c1-1 1-2 1-3z" class="X"></path><path d="M458 129c3 0 6 1 9 1v1h-8v-1l-1-1z" class="N"></path><path d="M449 127c3 1 6 2 9 2l1 1v1l-2 1c0-1 0-2-1-2s-3-1-5 0h-2v-3z" class="K"></path><path d="M456 124l3 1c3 0 5 1 8 2h-1l-1 1c0-1-1-1-2-1-2 0-5-1-7-3z" class="C"></path><path d="M449 130h2c2-1 4 0 5 0s1 1 1 2h0-1c-2 1-5 0-8-1 1 0 1-1 1-1z" class="D"></path><path d="M441 98c2-1 3 0 5 0h3-1v1l1 1c0 1 0 1-1 1 1 2 3 3 5 3v1c-5-1-8-5-12-7z" class="l"></path><path d="M462 123h1v-3h0c1 1 1 2 2 3l1 2h1 0c1 1 3 2 3 4l-3-2c-3-1-5-2-8-2l1-2h2z" class="P"></path><path d="M440 126h0c2-1 3-3 4-4 1 1 3 4 5 5v3s0 1-1 1c-3-1-5-2-7-4l-1-1z" class="L"></path><path d="M449 117c1 1 3 2 5 2s3-1 4-1c1 2-1 2 2 4h0l1-1 1 2h-2l-1 2-3-1h-5l-4-1c-1-1-2-2-2-3l1-2c1 1 1 1 2 1l1-2z" class="g"></path><path d="M447 123c0-1 1-2 2-2s2 1 4 1l-2 2-4-1z" class="I"></path><path d="M461 121l1 2h-2l-1 2-3-1h-5l2-2h1 1 2c1 1 2 1 3 0l1-1z" class="O"></path><path d="M435 107c2 0 3 0 4-1 0 3 0 5 2 7 0 0 1 0 1 1 1 1 1 2 1 4l-1 1s-1 2-1 3c-1 0-2 1-3 2h-3c-2-1-4-3-4-4v-1c2-1 2-1 4 0 0 0 0-1 1-1l-2-2-4-3c1 0 2 0 2-1 1 0 1 0 2-1h0 1 2v-1c0-1-1-2-2-3z" class="h"></path><path d="M435 107c2 0 3 0 4-1 0 3 0 5 2 7 0 0 1 0 1 1 1 1 1 2 1 4l-1 1v-1c-2-2-5-4-7-7h2v-1c0-1-1-2-2-3z" class="G"></path><path d="M435 111h2c1 0 2 0 3 1v1h0c1 1 2 2 2 3v2c-2-2-5-4-7-7z" class="C"></path><path d="M426 109c0-1-1-2-2-4 1-2 3-4 4-5 2-2 5-2 8-2 4 1 6 2 8 5 0 1 0 2-1 3s-1 1-2 1c0 0 0-1-1-1h-1 0c-1 1-2 1-4 1 1 1 2 2 2 3v1h-2-1 0c-1 1-1 1-2 1 0 1-1 1-2 1h-1l-1-1c-1-1-2-1-2-3z" class="H"></path><path d="M426 109c1 1 3 1 4 1l2-2v1c0 1 0 2 1 3h-1c0 1-1 1-2 1h-1l-1-1c-1-1-2-1-2-3zm3-2l-1-2-1-1 2-1c2-1 4-3 7-3v1l-2 3v1c-1 0-2-1-3-1-1 1-1 1-1 2 0 0 0 1-1 1z" class="F"></path><path d="M429 107c1 0 1-1 1-1 0-1 0-1 1-2 1 0 2 1 3 1v-1c1 0 1 1 2 1s2 0 3 1h0c-1 1-2 1-4 1 1 1 2 2 2 3v1h-2-1 0c-1 1-1 1-2 1h1c-1-1-1-2-1-3v-1h0c-1 0-2-1-3-1z" class="E"></path><path d="M432 109c1-1 1-2 2-2h1c1 1 2 2 2 3v1h-2-1 0c-1 1-1 1-2 1h1c-1-1-1-2-1-3z" class="H"></path><path d="M446 106l2-1c0 1 0 2 1 3l1-1h1c0 1 0 1-1 2h-1v1c1 0 1 1 2 1 1-1 3-2 4-2 1-1 1 0 2 0 0-1-1-2-2-3 1 0 2 1 3 2s3 2 3 3c0 2 0 3-1 4l-1 1-1 2c-1 0-2 1-4 1s-4-1-5-2l-1 2c-1 0-1 0-2-1 1-1 1-2 0-3s-1-1-2-1l-1-1v1l-1-1v1c0-1-1-1-1-1-2-2-2-4-2-7h0 1c1 0 1 1 1 1 1 0 1 0 2-1l1 1c1-1 1-1 2-1z" class="C"></path><path d="M455 106c1 0 2 1 3 2s3 2 3 3c0 2 0 3-1 4l-1 1c0-1 0-3-1-3 0-1-1-3-2-3-2-1-3 0-4 1h-1c1-1 3-2 4-2 1-1 1 0 2 0 0-1-1-2-2-3z" class="Q"></path><path d="M444 107c1-1 1-1 2-1 1 2 1 4 2 5h0c0 2-1 5 1 6l-1 2c-1 0-1 0-2-1 1-1 1-2 0-3 0-3-2-4-3-7l1-1z" class="P"></path><path d="M439 106h1c1 0 1 1 1 1 1 0 1 0 2-1l1 1-1 1c1 3 3 4 3 7-1-1-1-1-2-1l-1-1v1l-1-1v1c0-1-1-1-1-1-2-2-2-4-2-7h0z" class="C"></path><path d="M67 115V94h22c1 1 2 1 2 1h-1l1 1h-1l4 1 1 1-2 1h1v1h1c1 0 3 1 4 2-2 0-2-1-3-1-1-1-2-1-2-1 3 2 6 4 7 8l2 4-1 4 1-1 3-6c1 0 2 1 3 1v1 2h0c0 3-2 8-4 10 0 1 0 1-1 1h1c2-1 5-1 7-3l2-1v2c0 1 0 1-1 2h-1v1c-1 1-2 1-4 1l-5 2h-1c-2 1-3 2-5 3v1h0 0l-3 1v1c-2 1-5 1-7 2l-3 2 1 1c-5 2-8 6-11 10 0 1-1 4-2 4v-2l-1 1-1 1c-1 0-1-1-1-1h-1c1-1 1-1 2-1 1-1 2-5 3-6 0 0-1-1-1-2h-1l-2 3v-3h-1c-1-1-1-2-1-3v-7-18z" class="M"></path><path d="M73 129l-1-1c-1-1-1-2-1-4 0-1 1-2 2-3 1 0 2 1 3 1l-2 2c0 1-1 2 0 3h1c-1 1-1 2-2 2z" class="H"></path><path d="M75 127c0 1 1 1 2 2h1 1 4c1 0 1-1 1-2 1-1 2-1 3-2 0 0 1 0 1-1v-2l4-1c-2 4-6 8-10 9-2 1-6 1-8 0-1 0-1 0-1-1 1 0 1-1 2-2z" class="I"></path><path d="M94 105v-1s1 0 1 1c2 3 2 9 2 13l1 1c0 1-1 1-1 2-1 1-2 3-3 4 0 2-3 5-5 5h0v1-1h-1l6-6c0-1 0-1 1-1v-1c0-1 1-2 1-3 1-1 0-5 0-6l-1-1-1 1v-8z" class="N"></path><path d="M70 136h4c0 1-2 1-2 2h0c3-1 5-1 7-3-1 0-1-1-1-1h-1c2 0 3 0 5 1l-3 2c-2 1-3 3-4 5 0 1-1 2-2 3 0 0-1-1-1-2h-1l-2 3v-3h0c0-1 1-1 1-2h-1l-1-2c1-1 1 0 3-1-1-1-1-1-1-2z" class="B"></path><path d="M69 143l3-1h1l-1 1h0-1l-2 3v-3h0z" class="J"></path><path d="M78 110l1 1c2 1 2 1 3 1l3 2v1l-1 1-1 1c1 1 1 2 2 3h1v1h-1l1 1c-2 0-2 1-4 1v1h0v-1h-1l-1-1c0-2 0-3-1-4h0c-1-1-1-1-1-2-1-2 0-4 0-6z" class="B"></path><path d="M83 117l-2-2v-1c0-1 1-1 1-2l3 2v1l-1 1-1 1z" class="e"></path><path d="M79 118l6 3 1 1c-2 0-2 1-4 1v1h0v-1h-1l-1-1c0-2 0-3-1-4z" class="F"></path><path d="M86 122h0c2-1 1-2 2-3l1 1c0-1 1-1 2-1l1 1v1l-4 1v2c0 1-1 1-1 1-1 1-2 1-3 2 0 1 0 2-1 2h-4-1-1c-1-1-2-1-2-2h-1c-1-1 0-2 0-3l2-2v1s1 3 2 3h1 1v-2h2 0v-1c2 0 2-1 4-1z" class="L"></path><path d="M74 124l2-2v1s1 3 2 3h1l-1 1c-1 0-1-1-2-1l-2-2h0z" class="B"></path><path d="M94 105v8l-1 5-1 2-1-1c-1 0-2 0-2 1l-1-1c-1 1 0 2-2 3h0l-1-1h1v-1h-1c-1-1-1-2-2-3l1-1 1-1v-1l2-2v-1c1 1 2 1 3 1h1 1c1 0 1 0 1-1l1-6z" class="I"></path><path d="M87 111c1 1 2 1 3 1 1 1 1 1 1 2-1 1-1 2-2 3l-3-2c0 1 1 2 0 3-1-1-1-2-1-3v-1l2-2v-1z" class="N"></path><path d="M94 105v8l-1 5h-4v-1c1-1 1-2 2-3 0-1 0-1-1-2h1 1c1 0 1 0 1-1l1-6z" class="g"></path><path d="M93 103h1c2 1 4 3 5 6s0 6-1 10l-1-1c0-4 0-10-2-13 0-1-1-1-1-1v1l-1 6c0 1 0 1-1 1h-1-1c-1 0-2 0-3-1v-2c1-2 3-4 6-5v-1z" class="I"></path><path d="M78 106v-1c1-2 4-4 7-5h0 5c1 1 1 2 2 3h1v1c-3 1-5 3-6 5v2 1l-2 2-3-2c-1 0-1 0-3-1l-1-1 2-4v-1c1-1 1-2 2-2h0c-1 1-3 2-4 3z" class="C"></path><path d="M80 106c2-2 4-4 7-4 1-1 1 0 2 0h0v1c-2 1-5 3-7 6l1 1c2 0 2 1 4 2l-2 2-3-2c-1 0-1 0-3-1l-1-1 2-4z" class="g"></path><path d="M67 115h0c1-1 1-10 1-12 0 1 0 2 1 2 0 1 1 2 2 3h0-2v1c0 1 0 1 1 2 2 0 3-3 5-4l1 1h-1l-2 3c0 1 0 1-1 1h-3l-1 1v2-1l1-1v4c2 1 5 0 7 0-3 2-6 4-6 8 0 2 0 4 1 6 1 1 3 2 4 2h1l-1 1h-3c-1 0-2 1-2 1v1c0 1 0 1 1 2-2 1-2 0-3 1l1 2h1c0 1-1 1-1 2h0-1c-1-1-1-2-1-3v-7-18z" class="f"></path><defs><linearGradient id="g" x1="86.022" y1="106.555" x2="99.388" y2="125.074" xlink:href="#B"><stop offset="0" stop-color="#969797"></stop><stop offset="1" stop-color="#c8c7c8"></stop></linearGradient></defs><path fill="url(#g)" d="M90 100h4c3 2 6 4 7 8l2 4-1 4v1 1l-1 2h0c0 2-1 3-2 5h0c-1 2-5 6-7 6h-1c-4 1-7 5-11 7l-1-1 3-2v-1 1c1-1 1-1 2-1l1-1s1-1 2-1 1-1 2-2v1-1h0c2 0 5-3 5-5 1-1 2-3 3-4 0-1 1-1 1-2 1-4 2-7 1-10s-3-5-5-6h-1-1c-1-1-1-2-2-3z"></path><path d="M106 109c1 0 2 1 3 1v1 2h0c0 3-2 8-4 10 0 1 0 1-1 1h1c2-1 5-1 7-3l2-1v2c0 1 0 1-1 2h-1v1c-1 1-2 1-4 1l-5 2h-1c-2 1-3 2-5 3v1h0 0l-3 1v1c-2 1-5 1-7 2l-3 2 1 1c-5 2-8 6-11 10 0 1-1 4-2 4v-2l-1 1-1 1c-1 0-1-1-1-1h-1c1-1 1-1 2-1 1-1 2-5 3-6s2-2 2-3c1-2 2-4 4-5l1 1c4-2 7-6 11-7h1c2 0 6-4 7-6h0c1-2 2-3 2-5h0l1-2v-1-1l1-1 3-6z" class="E"></path><path d="M97 132h0l-3 1v1c-2 1-5 1-7 2 3-2 7-4 10-4z" class="l"></path><path d="M84 138l1 1c-5 2-8 6-11 10 0 1-1 4-2 4v-2l-1 1v-1c1-1 2-3 3-4 2-4 6-7 10-9z" class="k"></path><path d="M104 124v-1l1-2-1-1h0c0-1 0-2 1-3 0-1 0-3 1-5l2-2h0c1 1 1 2 1 3 0 3-2 8-4 10 0 1 0 1-1 1z" class="c"></path><path d="M67 115V94h22c1 1 2 1 2 1h-1l1 1h-1l4 1 1 1-2 1h1v1h1c1 0 3 1 4 2-2 0-2-1-3-1-1-1-2-1-2-1h-4-5 0c-3 1-6 3-7 5v1c-2 2-4 6-6 7l-2-1-1 1-1 1v1-2l1-1h3c1 0 1 0 1-1l2-3h1l-1-1c-2 1-3 4-5 4-1-1-1-1-1-2v-1h2 0c-1-1-2-2-2-3-1 0-1-1-1-2 0 2 0 11-1 12h0z" class="n"></path><path d="M75 100h0c2 0 2 0 2 1s-1 2-1 3c-1 0-2-1-2-2s0-1 1-2z" class="E"></path><path d="M70 102c2 2 2 3 5 3h1v1c-1 1-2 1-3 2-2-1-3-2-4-4l1-1v-1z" class="S"></path><path d="M69 104l-1-2c1-2 1-4 2-5 2-1 4-1 6-1h5c-1 0-3 0-4 1h-1c-2 0-4 1-5 3-1 0-1 1-1 2v1l-1 1z" class="Z"></path><path d="M76 96c5-1 10-1 14 0l4 1 1 1-2 1c-6-2-12-2-17-2h1c1-1 3-1 4-1h-5z" class="c"></path><path d="M317 404l1 1c0 2 0 4 1 6v108c-1-1 0-2-1-4h0c-2 3-3 3-6 4h-1 0-2c-1 1-1 1-2 1h-1 0c0-1-1-2-1-3-1 0-1 0-1-1s-1-1-2-2c-1 0-1 0-2-1 1-1 0-2 0-3h0c0-1-1-2-1-3h-1v-4-2 1l-1-3h0v-1h1l1-1c-1 0-1-1-1-1v-2h0c-1 0-2-1-3-2v-1s1-1 2-1c0 1 0 1 1 1 0-1 1-1 1-3v-1h-2-1l-1 1h-1c0-1-1-1-1-1-1-3-3-7-4-10v-2h0-1c-1-1-1-3-1-5v1l-1 1-1-1h-3c0-2 1-3 1-5v-3c1 0 1 0 1-1s0-1-1-2v-1c0-3-1-4-3-6l-1-1c2-1 3 0 5-1h1 2v1c-1 0-1 1-2 1v1c1 1 0 2 0 3s-1 2-1 3h2l3-4v1c0 1-1 2-1 2l1 1h7 2c1 0 1 0 1-1v1c2-1 2-1 3-2s1-3 2-4c0 0 0-1 1-1v-1l1-1c1 0 1-1 1-2 1 0 1 0 1-1 0 0 0-1 1-1v-1s1-1 1-2h0l1-1h0c1-1 1-3 2-5 2-9 4-16 4-25 0-3 0-5-1-8l1-1z" class="M"></path><path d="M296 479c1 0 2 0 3 1v-1c2 1 4 2 6 2 0 0 1 0 1-1l-1 2c1 0 1 0 1 1h0c-2 0-5 0-7-1l-3-3z" class="P"></path><defs><linearGradient id="h" x1="310.14" y1="498.622" x2="313.734" y2="484.383" xlink:href="#B"><stop offset="0" stop-color="#0b0c0c"></stop><stop offset="1" stop-color="#29292a"></stop></linearGradient></defs><path fill="url(#h)" d="M311 491h0c2-4 2-7 1-11h1c1 2 2 6 2 8v13h-1l-3-2-2-1h0c-2 0-3-1-4-1 1-1 2-2 3-2 2-1 2-3 3-4z"></path><path d="M296 479v-6c1-1 2-2 3-2 2 0 5 1 6 2 2 2 4 4 5 6-1 2-2 3-4 4 0-1 0-1-1-1l1-2c0 1-1 1-1 1-2 0-4-1-6-2v1c-1-1-2-1-3-1z" class="g"></path><path d="M299 479v-1h1c0-1 1-1 2-2 1 1 3 2 4 3v1c0 1-1 1-1 1-2 0-4-1-6-2z" class="W"></path><defs><linearGradient id="i" x1="313.75" y1="460.623" x2="304.279" y2="471.065" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272727"></stop></linearGradient></defs><path fill="url(#i)" d="M312 460h1 1c-1 5-1 11-3 15v-1c-2-2-6-5-9-5-1-1-2 0-3 0-1-1 0-1 0-2s1-1 2-1v-1h0c1 0 2-1 3-1 0 0 1 0 1 1h1l1-1v-1h1 1v-2s1 0 1-1h2z"></path><path d="M301 465h0c1 0 2-1 3-1 0 0 1 0 1 1h1l1-1v-1h1v1 2l1 1 1-1v4c-1-1-2-1-3-2-2-1-4-2-6-2v-1z" class="C"></path><path d="M279 452c2-1 3 0 5-1h1 2v1c-1 0-1 1-2 1v1c1 1 0 2 0 3s-1 2-1 3h2l3-4v1c0 1-1 2-1 2l1 1h7 2c1 0 1 0 1-1v1c3 0 8-2 10-3h1v1 2c0 1-1 1-1 1v2h-1-1v1l-1 1h-1c0-1-1-1-1-1-1 0-2 1-3 1h0-1c-1 1-1 1-2 1h-1c-1-1-2 0-3 0h-1c-1 0-1 0-1-1s1-1 2 0c1 0 2-1 3-1h-1 0-5v2l-2-2v1c0 2-1 4-2 5v1l-1 1-1-1h-3c0-2 1-3 1-5v-3c1 0 1 0 1-1s0-1-1-2v-1c0-3-1-4-3-6l-1-1z" class="R"></path><path d="M285 467h1c0 1 0 2-1 2 0 1 0 1-1 0v-1c0-1 0 0 1-1z" class="Z"></path><path d="M279 452c2-1 3 0 5-1h1 2v1c-1 0-1 1-2 1v1c1 1 0 2 0 3s-1 2-1 3h2l3-4v1c0 1-1 2-1 2l1 1h7 2c1 0 1 0 1-1v1c3 0 8-2 10-3h1v1 2c0 1-1 1-1 1l-1-1h-1c-2 1-4 1-6 1-2-1-3 0-5 0-1 0-2 0-3 1h-5l-1-1c0 1 0 1-1 2l-3 3v-3c1 0 1 0 1-1s0-1-1-2v-1c0-3-1-4-3-6l-1-1z" class="G"></path><path d="M287 470c1-1 2-3 2-5v-1l2 2-1 1h1c2 0 4 1 6 2 0 1-1 1-1 2-2 1-3 3-3 5 1 2 1 4 2 6 2 2 3 2 6 3 2 0 4 0 6-1l3-3c1 3 1 5 0 7 0 2 0 2 1 3-1 1-1 3-3 4-1 0-2 1-3 2l-1 1h0v-2l-2-2c-1-1-3 0-4 0s-2-1-3-2v-1s1-1 2-1c0 1 0 1 1 1 0-1 1-1 1-3v-1h-2-1l-1 1h-1c0-1-1-1-1-1-1-3-3-7-4-10v-2h0-1c-1-1-1-3-1-5z" class="L"></path><path d="M302 493v-1c0-2 2-5 3-6h1v1l-1 3-3 3z" class="G"></path><path d="M305 490c1 1 1 2 2 4-1 1-2 2-3 2l-2-2v-1l3-3z" class="f"></path><path d="M307 494l3-6c0 2 0 2 1 3-1 1-1 3-3 4-1 0-2 1-3 2l-1 1h0v-2c1 0 2-1 3-2z" class="F"></path><path d="M295 482c0-1-1-1-2-1 0 0 0-1-1-1h0c0-1 0-1 1-2 0 0-1-1-1-2h-1 0l1-1c-1-1-1-2-1-3l1-1h2 0 0 2c-2 1-3 3-3 5 1 2 1 4 2 6z" class="D"></path><path d="M317 404l1 1c0 2 0 4 1 6v108c-1-1 0-2-1-4h0c-2 3-3 3-6 4h-1 0-2c-1 1-1 1-2 1h-1 0c0-1-1-2-1-3-1 0-1 0-1-1s-1-1-2-2c-1 0-1 0-2-1 1-1 0-2 0-3h0c0-1-1-2-1-3h-1v-4-2 1l-1-3h0v-1h1l1-1c-1 0-1-1-1-1v-2h0c1 0 3-1 4 0l2 2v2h0l1-1c1 0 2 1 4 1h0l2 1 3 2h1v-13 1c1 2 0 4 1 6l1-2v-7c0-3-2-5-2-8 1-1 1-2 1-3-1 1-1 2-2 2 0-2 2-5 2-8 1-6 0-14-1-19l-1 10h0-1-1-2v-2-1h-1c-2 1-7 3-10 3 2-1 2-1 3-2s1-3 2-4c0 0 0-1 1-1v-1l1-1c1 0 1-1 1-2 1 0 1 0 1-1 0 0 0-1 1-1v-1s1-1 1-2h0l1-1h0c1-1 1-3 2-5 2-9 4-16 4-25 0-3 0-5-1-8l1-1z" class="I"></path><path d="M309 457c1-1 2-2 3-2 0 1 1 1 1 3l-1 2h-2v-2-1h-1z" class="N"></path><path d="M309 448c1 0 2-1 2-1 1 1 1 2 2 2l-1 3h-1s-1-1-1-2h0-1v-1-1z" class="M"></path><path d="M309 447h0v1 1 1h1 0v2c-2 2-6 4-8 6 1-1 1-3 2-4 0 0 0-1 1-1v-1l1-1c1 0 1-1 1-2 1 0 1 0 1-1 0 0 0-1 1-1z" class="E"></path><path d="M311 443v1c1-1 2-1 4-2l2 2c-1 2-3 4-4 5-1 0-1-1-2-2 0 0-1 1-2 1v-1h0v-1s1-1 1-2h0l1-1zm4 45v1c1 2 0 4 1 6l1-2c0 5 0 10-5 14 0 0-1 1-2 1 0-2 0-3-1-4h1l-1-2c1 0 1 0 2-1v-2l3 2h1v-13z" class="C"></path><path d="M311 499l3 2h0c-1 1-1 3-1 3-1 0-1-1-2-1h0c0 1-1 1-1 1l-1-2c1 0 1 0 2-1v-2z" class="Z"></path><path d="M310 510c2 0 3-1 5-1 1 1 2 2 2 3l1 3c-2 3-3 3-6 4h-1v-1c-1-1-1-2-2-3v-2c0-1-1-1-2-2l1-1h1 1z" class="C"></path><path d="M309 510h1 4l-1 1h0c0 1 0 2 1 3v1 1c-1 0-1 0-2 1v1 1h-1v-1c-1-1-1-2-2-3v-2c0-1-1-1-2-2l1-1h1z" class="S"></path><path d="M298 494c1 0 3-1 4 0l2 2v2h0l1-1c1 0 2 1 4 1h0l2 1v2c-1 1-1 1-2 1l1 2h-1c1 1 1 2 1 4h0-1c-2-1-4-2-7-2 3 1 5 2 7 4h-1l-1 1c1 1 2 1 2 2v2c1 1 1 2 2 3v1h0-2c-1 1-1 1-2 1h-1 0c0-1-1-2-1-3-1 0-1 0-1-1s-1-1-2-2c-1 0-1 0-2-1 1-1 0-2 0-3h0c0-1-1-2-1-3h-1v-4-2 1l-1-3h0v-1h1l1-1c-1 0-1-1-1-1v-2h0z" class="M"></path><path d="M309 498h0v4l-2-1c0 1-1 1-2 1h0l4-4zm0 10c-1-1-1-2-1-3v-1h1c1 1 1 2 1 4h0-1z" class="R"></path><path d="M300 510h2c1 1 2 2 3 2 1 1 2 2 2 3v1l2 3c-1 1-1 1-2 1h-1 0c0-1-1-2-1-3-1 0-1 0-1-1s-1-1-2-2c-1 0-1 0-2-1 1-1 0-2 0-3h0z" class="C"></path><path d="M306 520c1-2 1-3 1-4l2 3c-1 1-1 1-2 1h-1z" class="i"></path><path d="M216 95h171 0c-2 1-6 2-7 4 1 2 4 5 4 7-1-1-1-2-3-2v1h0-2c0-1-1-1-2-2h0c-1 0-2-1-3-1h0v3l-1 1v1h0-2-2s-1-1-1-2c-1 1-1 4-2 5h0l-1-1h0l-2 1h-13-3-1c-1-1-3 0-4 0-1-1-2-1-3-1h-5c1 0 2 0 2-1 1 0 1-1 2-2-2 1-4 1-5 2h-6 0c-1 1-2 1-3 2-2 0-3 1-4 2l-2 2c-1 0-1 0-1-1l1-2c-1 0-1-1-1-2l-1 1c0-3 0-5-1-8h0c-2-2-2-3-4-4-1-1-3-1-4-1-4 0-6 0-9 2-1 2-2 3-3 5h0c0 2 0 3 1 5v2c-1 0-1 0-1 1-1-2-2-4-3-5-2-4-6-6-10-8-1 1-5-1-6-1-6 0-11 1-16 5-6 7-8 16-8 24 0 4 1 8 2 12l1 1h-2c-1-1-1-2-2-3 0-1-1-2-1-3h0c0-1-2-2-2-3-2-2-4-6-5-8v-2c-1 0-1-1-1-2-2-2-3-3-3-6v-3-1l-4-1v1l1 1-1 1v1 1c1 0 2 2 2 3l1 3c-1 0-1 0-1-1h0c-2-1-3-3-5-4l-1 1 1 1h-1s-2-1-2-2l-5-5-3-5-2-4h0c0-1-2-2-3-3v-1h-1 0c0-1 2-1 3-1h-2z" class="D"></path><path d="M241 97h7v1c-1 0-3 1-4 2l-2-2s-1 0-1-1z" class="Q"></path><path d="M234 102c2 1 5 2 6 3h-1c-2 1-2 1-4 1-1-1-1-2-1-4z" class="J"></path><path d="M225 108c1-1 1-1 3-2 0 2 0 3 1 5l-1 1c-1-1-2-3-3-4z" class="Z"></path><path d="M366 100c3-1 7-3 9-2-2 2-6 3-8 4l-1-1v-1z" class="S"></path><path d="M230 104l5 8v1 1c-2-2-3-4-5-6h0v-4z" class="F"></path><path d="M231 100c2 0 5 1 7 1l1 1h-1v1h0c2 0 2 0 3 1v3c0-1 0-1-1-2s-4-2-6-3c-1 0-2-1-3-2zm137 5l6-3h0v3l-1 1v1h0-2-2s-1-1-1-2z" class="C"></path><path d="M231 100l-2-2h1c1 0 1 1 2 1s2 0 3 1h3 0c0-1 1 0 2-1 1 0 1 0 2 1h0v-1c-1 0-1 0-2-1h0-2l3-1c0 1 1 1 1 1l2 2c-2 1-3 2-5 2l-1-1c-2 0-5-1-7-1z" class="O"></path><path d="M318 106c1 0 2 0 4-1-1 1-2 3-2 4l2-1 2 2c-2 0-3 1-4 2l-2 2c-1 0-1 0-1-1l1-2v-5z" class="C"></path><path d="M321 101c2-2 4-2 7-3-3 2-4 4-6 7-2 1-3 1-4 1 0-2 0-3 2-5h1z" class="c"></path><path d="M260 103v-2c1 0 0-1 0-1 1-1 1-2 1-3h1c1 0 1 1 1 1l1-1v2h0 1 1c1 0 1 0 2-1h0-3v-1h2l1 1c1 0 1 0 2-1 2 0 4 0 6 1-6 0-11 1-16 5z" class="K"></path><path d="M230 108c2 2 3 4 5 6v-1c1 0 2 2 2 3l1 3c-1 0-1 0-1-1h0c-2-1-3-3-5-4l-1 1 1 1h-1s-2-1-2-2 0-2-1-2l1-1c1 1 1 1 2 1h0v-1c0-1 0-2-1-3z" class="E"></path><path d="M221 99c2 0 3 0 5 1 0 1 1 2 2 4v2c-2 1-2 1-3 2-2-3-3-6-4-9z" class="c"></path><path d="M221 99l-1-2h3c3 1 6 5 7 7h0v4h0c1 1 1 2 1 3v1h0c-1 0-1 0-2-1-1-2-1-3-1-5v-2c-1-2-2-3-2-4-2-1-3-1-5-1z" class="T"></path><path d="M311 98c1-1 2-1 3 0 2 0 3-1 4 0h2l1 3h-1c-2 2-2 3-2 5v5c-1 0-1-1-1-2l-1 1c0-3 0-5-1-8h0c-2-2-2-3-4-4z" class="K"></path><path d="M315 102s1-1 1-2h1c1 1 0 1 1 2 0-1 1 0 2-1-2 2-2 3-2 5v5c-1 0-1-1-1-2l-1 1c0-3 0-5-1-8z" class="D"></path><path d="M346 98l9-1h3 1l1 1 1 1h0l-2 1h4 3v1l1 1h-4-10l-1-1c-1-2-4-2-6-3z" class="F"></path><path d="M358 97h1l1 1 1 1h0l-2 1h0-3c0-1 1-2 2-3z" class="g"></path><path d="M282 99h2c0 1 2 2 3 1h1l1-1c1 1 2 1 2 1l1-2h-5c4 0 7-1 11 1-1 2-2 3-3 5h0c0 2 0 3 1 5v2c-1 0-1 0-1 1-1-2-2-4-3-5-2-4-6-6-10-8z" class="K"></path><path d="M293 100h1l1 1c0 1-1 2-2 3v-1c-1 0-1-1-2-2 1 0 1 0 2-1h0z" class="V"></path><path d="M352 101l1 1c-1 0-1 0-2 1-2 2-4 4-6 5h-1v1c2 1 3 0 5 0l2-2c1-1 2-1 3-2 2 0 6 1 8 1 1 1 2 1 3 2h0v1l-2 1h-13-3-1c-1-1-3 0-4 0-1-1-2-1-3-1h-5c1 0 2 0 2-1 1 0 1-1 2-2s3-3 5-3l4-2h2v1c1 0 3-1 3-1z" class="T"></path><path d="M354 105v1 1 1h1s-1 0-1 1c-1-1-2-1-3-2 1-1 2-1 3-2z" class="N"></path><path d="M354 106h2 1v1s-1 1-2 1h-1v-1-1zm8 0c1 1 2 1 3 2h0-1c-1 0-1 0-2 1h0-2l-1-1c1-1 2-1 3-2z" class="C"></path><path d="M343 103l4-2h2v1h0c-2 1-4 6-6 6h-4l4-5z" class="L"></path><path d="M333 100c2-1 3-3 5-3l2 2h1v-1c2 0 4-1 5 0 2 1 5 1 6 3 0 0-2 1-3 1v-1h-2l-4 2c-2 0-4 2-5 3-2 1-4 1-5 2h-6 0c-1 1-2 1-3 2l-2-2v-1l3-5c1-1 1-2 2-2 1 1 1 3 1 4l1-1c1-1 1-2 1-4h2 0l1 1z" class="M"></path><path d="M322 108v-1c1 1 2 1 3 0l2 1c-1 1-2 1-3 2l-2-2z" class="N"></path><path d="M334 103l3-1h0v1l-1 1h-1c-1 1-3 2-5 2 1-1 3-2 4-3z" class="Z"></path><path d="M328 104l1-1c1-1 1-2 1-4h2 0l1 1c-1 1-2 2-2 3v1c1 0 1-1 2-1l1-1v1c-1 1-3 2-4 3h-1l-1-1v-1z" class="R"></path><path d="M341 99v-1c2 0 4-1 5 0 2 1 5 1 6 3 0 0-2 1-3 1v-1h-2v-1h-2 0c-1 0-1 1-2 1l-2-2h0z" class="I"></path><path d="M248 97c2 0 4 0 6 1 2 0 3 2 3 3s0 1-1 1c-1 2-2 4-2 6-1 1-1 2-2 3 0 1 0 2-1 3v4h-1c0-1 0-2-1-3 0 1 0 5-1 7 0 2 1 7 2 10 1 0 1 1 1 2h0-1 0c0-1-2-2-2-3-2-2-4-6-5-8v-2c-1 0-1-1-1-2-2-2-3-3-3-6v-3h0l2-3v-3c-1-1-1-1-3-1h0v-1h1c2 0 3-1 5-2 1-1 3-2 4-2v-1z" class="M"></path><path d="M242 119v-1-2l1-1h0 0c1 4 3 8 4 11l3 6c1 0 1 1 1 2h0-1 0c0-1-2-2-2-3-2-2-4-6-5-8v-2c-1 0-1-1-1-2z" class="P"></path><path d="M248 97c2 0 4 0 6 1 2 0 3 2 3 3s0 1-1 1c-1 2-2 4-2 6-1 1-1 2-2 3 0 1 0 2-1 3 0-2 1-5 1-8-1 0-1-1-2-2h1c1-1 2-2 2-4v-1h-2c-1 0-6 3-7 4h0-1l1-1c1-1 2-2 3-2 0-1 1-1 2-2h-1v-1z" class="E"></path><path d="M307 97c1 0 3 0 4 1 2 1 2 2 4 4h0c1 3 1 5 1 8l1-1c0 1 0 2 1 2l-1 2c0 1 0 1 1 1l2-2v1c-1 1-4 3-5 5-3 3-7 6-10 10l-3 6c-1 0-2 2-3 2v3c-1 2-3 3-4 5l-2 2h-1l-4 4c-1 2-4 5-5 7v-1-3s-1 0-1-1c-1 0-2 1-3 1h0l-2-2h0-1v2h0c-1 1 0 2-1 2-1 1-1 1-2 1h-1c-1 0-1-1-1-1 0 1-1 2-1 4l-2-1c-1 1-1 2-2 2l-1-2-1-1c-2-2-3-3-4-5l-2-2-1-1v-3h-2v-2-4l-1-1c-1-4-2-8-2-12 0-8 2-17 8-24 5-4 10-5 16-5 1 0 5 2 6 1 4 2 8 4 10 8 1 1 2 3 3 5 0-1 0-1 1-1v-2c-1-2-1-3-1-5h0c1-2 2-3 3-5 3-2 5-2 9-2z" class="X"></path><path d="M255 133h0l1 2c0 1 1 3 2 4-1 0-2 1-3 1h0c1 2 1 2 0 4v-4l-1-1 1-1c1-1 0-3 0-5zm11-26l1-3c1 2 1 3 1 4s-1 2 0 2v1h1c1 1 1 2 2 3-2 0-3-1-4-2l-1-1c-1-1 0-3 0-4z" class="C"></path><path d="M259 110c1-2 2-3 3-4 2-3 4-4 7-4h1c-1 1-2 1-2 2h-1l-1 3c-1 1-1 2-1 3h-1v-2h0c-1 1-1 1-1 2l-1 1c-1-1-1-1-2-1h-1z" class="i"></path><path d="M262 111l1-1c0-1 0-1 1-2h0v2h1c0-1 0-2 1-3 0 1-1 3 0 4l1 1c0 1-1 2-1 2-1 1-2 1-3 1h-2c0-1-1-2-2-2 0-1 0-2 1-3 1 0 1 0 2 1z" class="Q"></path><path d="M260 110c1 0 1 0 2 1h0c1 0 1 1 1 1 1 0 1 1 1 1v1l-3 1c0-1-1-2-2-2 0-1 0-2 1-3z" class="T"></path><path d="M255 133v-6c0-3 0-6 1-9h2c1 1 3 2 4 3h0c0 1-1 1-1 2l-2-1v1h-1c-1 0-1 1-2 1v1 6h0l-1 2h0z" class="D"></path><path d="M258 118c1 1 3 2 4 3h0c0 1-1 1-1 2l-2-1v1h-1l-1-2c0-1 0-2 1-3z" class="E"></path><path d="M259 110h1c-1 1-1 2-1 3 1 0 2 1 2 2h2c1 0 2 0 3-1 0 0 1-1 1-2 1 1 2 2 4 2l2 2h0-1l-2 1-2 2-4 3s-1 0-1-1h-1c-1-1-3-2-4-3h-2l3-8z" class="B"></path><path d="M263 121h1v-1c0-1-1-2-2-3-1 0-1 0-2-1v-1c1 0 2 1 3 2 1 0 1 0 2-1h2c1 1 2 1 2 1h1l-2 2-4 3s-1 0-1-1z" class="H"></path><path d="M262 121h1c0 1 1 1 1 1l-1 1s0 1 1 1v2c0 2-1 6-3 8h-1c0 2 0 3 1 5h0c0 2 1 2 1 4l1 1c-2 0-4-3-5-5-1-1-2-3-2-4l-1-2 1-2h0v-6-1c1 0 1-1 2-1h1v-1l2 1c0-1 1-1 1-2h0z" class="M"></path><path d="M256 135h2c1-1 1-1 1-2s-1 0-2-1c1-1 2-2 3-2 0 3-1 8 1 11 0 1 1 1 1 2l1 1c-2 0-4-3-5-5-1-1-2-3-2-4z" class="D"></path><path d="M256 125v-1c1 0 1-1 2-1h1l2 3-1 4c-1 0-2 1-3 2 1 1 2 0 2 1s0 1-1 2h-2l-1-2 1-2h0v-6z" class="B"></path><path d="M256 125l2 1c1 1 1 1 0 2 0 1-1 2-2 3v-6z" class="U"></path><path d="M283 107v-1c-1-2-2-3-4-4h0c6 1 9 3 12 8l3 9c1 1-1 5 2 7 0-1 0-1 1-2-1 3-1 5-2 8l-3-1v-1l-3 6c-1-1-1-2-1-2v-3c-1-4 0-5 2-8l-1-1c0-1 0-2 1-2l-1-1c-2 1-3 2-4 3h-1l-2-2c-2-1-3-2-5-3l6-6h0v-4z" class="D"></path><path d="M289 119h2l1 1c0 1-1 2-2 3l-1-1c0-1 0-2 1-2l-1-1z" class="N"></path><path d="M283 107c0 1 0 2 1 3s2 0 3 0h2c0 1 1 2 2 3-1 1-1 1-2 1h-4c-1-1-1-2-2-3h0v-4z" class="U"></path><path d="M294 119c1 1-1 5 2 7 0-1 0-1 1-2-1 3-1 5-2 8l-3-1v-1c1-4 2-7 2-11z" class="G"></path><path d="M270 117l2-1h1 0l1 1s1 0 1 1c1 0 1 0 2-1 2 1 3 2 5 3l2 2c0 1 0 1-1 2l-1 1c-1 2-2 4-3 5l-3 3c-2 1-3-1-4 2 0 2 0 3 1 4h-1-1c0-2 0-3-2-4h-1c-1-1-1-2-1-3-1 1-1 1-1 2l-1 1h-1c0 1 0 3 1 4 0 1 0 1 1 2v2h-2v1h-1l-1-1c0-2-1-2-1-4h0c-1-2-1-3-1-5h1c2-2 3-6 3-8v-2c-1 0-1-1-1-1l1-1 4-3 2-2z" class="S"></path><path d="M270 117l2-1h1 0l1 1s1 0 1 1c1 0 1 0 2-1 2 1 3 2 5 3-2 0-3 0-4-1h-2-2-2c-1 0-1 1-2 0h-2l2-2z" class="C"></path><path d="M289 119l1 1c-1 0-1 1-1 2l1 1c-2 3-3 4-2 8v3 3h0c-1 3-5 6-8 8h-1c0-1 0-1-1-1l-2 1v2 1 1c2-1 3-3 5-3h0l-1 2c-2 1-3 1-4 3v2h0c-1 1 0 2-1 2-1 1-1 1-2 1h-1c-1 0-1-1-1-1 0 1-1 2-1 4l-2-1c-1 1-1 2-2 2l-1-2-1-1c-2-2-3-3-4-5l-2-2-1-1v-3h-2v-2c1-2 1-2 0-4h0c1 0 2-1 3-1 1 2 3 5 5 5h1v-1h2v-2c-1-1-1-1-1-2-1-1-1-3-1-4h1l1-1c0-1 0-1 1-2 0 1 0 2 1 3h1c2 1 2 2 2 4h1 1c-1-1-1-2-1-4 1-3 2-1 4-2l3-3c1-1 2-3 3-5l1-1c1-1 1-1 1-2h1c1-1 2-2 4-3z" class="X"></path><path d="M261 149v2l-1 1-2-2h1c1 0 1 0 2-1z" class="E"></path><path d="M264 146l6 3v1c-1 0-1 1-2 1h-4 2v-2l-1-1s-1-1-1-2z" class="H"></path><path d="M266 141c1 1 3 1 4 3h1v2h-1-2c-2-1-3-1-4-2v-1h2v-2z" class="Z"></path><path d="M266 141c1 1 3 1 4 3-1 0-1 1-2 1l-2-2v-2z" class="C"></path><path d="M265 135c3 1 5 3 6 6v3h-1c-1-2-3-2-4-3s-1-1-1-2c-1-1-1-3-1-4h1z" class="g"></path><path d="M261 149l-4-7c2 1 4 3 7 4h0c0 1 1 2 1 2l1 1v2h-2l-1-1v2c1 1 3 2 4 2 1-1 1-1 2-1l2 2c0 1-1 2-1 4l-2-1c-1 1-1 2-2 2l-1-2-1-1c-2-2-3-3-4-5l1-1v-2z" class="h"></path><path d="M261 151c1 2 2 3 3 6-2-2-3-3-4-5l1-1z" class="F"></path><path d="M263 150h0l-2-2h1c1-1 2 0 3 0l1 1v2h-2l-1-1z" class="L"></path><path d="M289 122l1 1c-2 3-3 4-2 8v3 3h0c-1 3-5 6-8 8h-1c0-1 0-1-1-1l-2 1h0c-1-1 0-4 0-5 1-3 3-4 5-6 1 0 1-1 2-1 1 1 2 1 4 0h0c-1-1-1-1-1-2l1-1v-1-2c-1 0-1-1-2-1h0v-1c1-1 2-2 4-3z" class="C"></path><path d="M281 134c2 1 2 1 3 2 0 2-1 3-2 4s-3 2-4 4l-2 1h0c-1-1 0-4 0-5 1-3 3-4 5-6z" class="f"></path><defs><linearGradient id="j" x1="315.938" y1="130.87" x2="280.06" y2="140.151" xlink:href="#B"><stop offset="0" stop-color="#151b1b"></stop><stop offset="1" stop-color="#3a3536"></stop></linearGradient></defs><path fill="url(#j)" d="M307 97c1 0 3 0 4 1 2 1 2 2 4 4h0c1 3 1 5 1 8l1-1c0 1 0 2 1 2l-1 2c0 1 0 1 1 1l2-2v1c-1 1-4 3-5 5-3 3-7 6-10 10l-3 6c-1 0-2 2-3 2v3c-1 2-3 3-4 5l-2 2h-1l-4 4c-1 2-4 5-5 7v-1-3s-1 0-1-1c-1 0-2 1-3 1h0l-2-2h0-1c1-2 2-2 4-3l1-2h0c-2 0-3 2-5 3v-1-1-2l2-1c1 0 1 0 1 1h1c3-2 7-5 8-8h0v-3s0 1 1 2l3-6v1l3 1c1-3 1-5 2-8h0l-1-4c0-3 0-6-1-8 0-1 0-1 1-1v-2c-1-2-1-3-1-5h0c1-2 2-3 3-5 3-2 5-2 9-2z"></path><path d="M277 151h7v1c0 1-1 1-1 2 1 1 0 2 0 2v-3s-1 0-1-1c-1 0-2 1-3 1h0l-2-2z" class="d"></path><path d="M280 148h2l1 1-1 2h-5-1c1-2 2-2 4-3z" class="B"></path><path d="M317 113c0 1 0 1 1 1l2-2v1c-1 1-4 3-5 5-3 3-7 6-10 10l-3 6c-1 0-2 2-3 2v3c-1 2-3 3-4 5l-2 2h-1c2-3 5-7 7-10 2-4 4-9 8-12 1-2 3-3 5-5s3-3 5-6z" class="T"></path><path d="M305 110l1 4-2 1h4s0 1 1 1h2c1 0 1 0 1 1h-1c-1 0-2 1-4 1 0 0-1 0-2 1 0 0-1 2-2 3-1 2-2 5-3 8-4 6-8 12-12 17-1 0-2 2-2 2h-3c7-4 12-13 14-20 2-5 3-9 5-14 1 0 0-1 1-2 0-1 1-2 2-3z" class="c"></path><path d="M296 111l1 1c1 0 2-1 3-1v2l-2 1s1 1 2 1c0 1-2 2-1 3 1-1 2-3 3-3h0c-2 5-3 9-5 14-2 7-7 16-14 20h0l-1-1h-2l1-2h0c-2 0-3 2-5 3v-1-1-2l2-1c1 0 1 0 1 1h1c3-2 7-5 8-8h0v-3s0 1 1 2l3-6v1l3 1c1-3 1-5 2-8h0l-1-4c0-3 0-6-1-8 0-1 0-1 1-1z" class="D"></path><path d="M296 120h2l1 1c-1 1-2 2-2 3l-1-4z" class="I"></path><path d="M292 130v1l3 1c-4 5-8 11-14 14h0c-2 0-3 2-5 3v-1-1-2l2-1c1 0 1 0 1 1h1c3-2 7-5 8-8h0v-3s0 1 1 2l3-6z" class="d"></path><path d="M278 144c1 0 1 0 1 1l-3 3v-1-2l2-1z" class="Z"></path><path d="M307 97c1 0 3 0 4 1 2 1 2 2 4 4h0c1 3 1 5 1 8 0 2-1 5-4 6v1c0-1 0-1-1-1h-2c-1 0-1-1-1-1h-4l2-1-1-4c-1 1-2 2-2 3-1 1 0 2-1 2h0c-1 0-2 2-3 3-1-1 1-2 1-3-1 0-2-1-2-1l2-1v-2c-1 0-2 1-3 1l-1-1v-2c-1-2-1-3-1-5h0c1-2 2-3 3-5 3-2 5-2 9-2z" class="B"></path><path d="M298 99c3-2 5-2 9-2 1 2 1 3 3 4 1 2 1 4 2 6 0 1-1 2-1 3h-3c-1-1-1-2-2-3 1-2 1-3 2-4 1 0 1 0 2 1-1-2-2-3-3-3-2-1-5-2-6-1-3 1-3 3-4 5l-2-1h0c1-2 2-3 3-5z" class="S"></path><path d="M302 103h1 1v1c0 2 0 4 1 6-1 1-2 2-2 3-1 1 0 2-1 2h0c-1 0-2 2-3 3-1-1 1-2 1-3-1 0-2-1-2-1l2-1v-2c-1 0-2 1-3 1l-1-1v-2c-1-2-1-3-1-5l2 1c-1 1-1 2 0 3h1v-2c1-2 3-3 4-3z" class="C"></path><path d="M298 108v-2c1-2 3-3 4-3l1 1-1 1v2c-1 1-3 1-4 1z" class="E"></path><path d="M307 97c1 0 3 0 4 1 2 1 2 2 4 4h0c1 3 1 5 1 8 0 2-1 5-4 6v1c0-1 0-1-1-1h-2c-1 0-1-1-1-1h-4l2-1h1c1 0 2 0 3-1 2-1 3-3 3-4 0-3-1-6-3-8h0c-2-1-2-2-3-4z" class="F"></path><path d="M413 98c2 1 4 1 6 1 2 1 3 2 5 2h1l-2 1v3 1 4l2 6c1 3 2 6 5 8h0 1c1 1 3 2 4 2s1 1 2 1h0l1 1c1 0 4 1 5 3h0c0 1 0 4 1 5v1h0c1-1 2-1 3-1v1c0 1 1 2 2 2h0c0 2-1 2-2 3s-1 3-1 4l1 1c0 1 1 1 1 3s-1 4-3 5c-1 1-3 2-5 2-2 1-2 1-3 2-6-4-11-9-17-13-23-14-51-15-77-16h-18l-6-1v18c-1-2 0-3-1-5l-2 1h-1c-1-1-2-1-4-2-1 1-3 1-4 1s-4 1-5 0l-2 2h-2l8-9c-1-1-1-1-2-1l-1 1h0 0-1v-1l3-6c3-4 7-7 10-10 1-2 4-4 5-5v-1c1-1 2-2 4-2 1-1 2-1 3-2h0 6c1-1 3-1 5-2-1 1-1 2-2 2 0 1-1 1-2 1h5c1 0 2 0 3 1 1 0 3-1 4 0h1 3 13l2-1h0l1 1h0c1-1 1-4 2-5 0 1 1 2 1 2h2 2 0v-1l1-1v-3h0c1 0 2 1 3 1h0c1 1 2 1 2 2h2 0v-1c2 0 2 1 3 2h0c0 1 1 1 1 2s1 1 1 2v6 2 1s-1 1-1 2l1 1h0c1-1 3-3 3-5 1 0 1-1 1-2 1 0 1 0 1-1h0c1-2 3-3 4-4h1 0c1-1 3-1 4 0h0 1v-3c0 1 1 1 1 1v1s1 0 1 1v3h1v-2l1-2h0v-2-2l3-2c1 1 1 1 1 2l1-1c0-1 1-1 2-2v1c1-1 1-1 2-1-1-1-2-2-2-3l1-1z" class="g"></path><path d="M424 137h1l1 1-2 1c0 1 0 2-1 3 0 0-1 0-1-1s1-3 2-4z" class="H"></path><path d="M328 124c0-1 0-1 1-2l3 3h0c0 1 1 2 2 3h0c-1 0-3 0-4-1 0-1-1-3-2-3h0zm10-2c2 1 3 1 4 2h0c2 2 4 4 6 5h-4c-2 0-4-3-6-4 0 0-1-1-2-1 1 0 1-1 1-1h1v-1z" class="X"></path><path d="M403 128l2-1c1 0 0 0 1 1h0c-3 3-7 4-11 4-2-1-5-2-7-3h0c1-1 2-1 3-1l4 1c3 1 6 0 8-1z" class="C"></path><path d="M322 121c3-1 9 0 12 1 0 0 1 0 2 1v1c1 0 2 1 2 1 1 1 2 2 2 3v1c-3 0-5-2-8-4h0l-3-3c-1 1-1 1-1 2v4c-2 0-4-4-6-4h0c-1-1 0-1-1-1h-1c0-1 1-1 2-2z" class="S"></path><path d="M311 132c1-1 3-2 5-3 1 0 1-1 2-1h2 1c2 0 1 0 2 1 1 0 1 0 2 1l-6-1v18c-1-2 0-3-1-5l-2 1h-1c-1-1-2-1-4-2-1 1-3 1-4 1s-4 1-5 0l4-2c0-1 1-2 1-2h1c0-1 0-2 1-3s1-2 2-3z" class="H"></path><path d="M317 135h1l1 2-1 1h-2c0-2 0-2 1-3z" class="B"></path><path d="M311 132c1 1 2 1 2 1 0 2 1 2 0 4h-3l-1-2c1-1 1-2 2-3zm-5 8c4-1 8-1 12-2v4l-2 1h-1c-1-1-2-1-4-2-1 1-3 1-4 1s-4 1-5 0l4-2z" class="E"></path><path d="M401 107c0 1 1 1 1 1v1s1 0 1 1v3h1l-1 2 1 1c0 1-1 2-2 2 0 1 0 1 1 1l-1 1 2 2c-1 1-2 1-4 1 2 1 3 1 4 1h1v2h-1l-1 2c-2 1-5 2-8 1l-4-1c-1 0-2 0-3 1v-2c-2 1-5 2-7 2-5 1-12 1-17 0l1-1c3-1 6 0 9 0v-1c2 0 3-1 5-1l2-1h1l2-2c1 0 2-1 2-1 1-1 3-3 3-5 1 0 1-1 1-2 1 0 1 0 1-1h0c1-2 3-3 4-4h1 0c1-1 3-1 4 0h0 1v-3z" class="G"></path><path d="M401 107c0 1 1 1 1 1v1s1 0 1 1v3h1l-1 2h-1-5v-1c-1 0-2 1-2 1h0-1c0-1 1-1 1-2h3l1-1 1 1 1-1v-2-3z" class="T"></path><path d="M403 115l1 1c0 1-1 2-2 2 0 1 0 1 1 1l-1 1h-1-2s-1 0-1 1l-4-1c1-2 2-4 3-5h5 1z" class="H"></path><path d="M398 118c2-1 3 0 4 0 0 1 0 1 1 1l-1 1h-1-2l-1-2z" class="o"></path><path d="M397 115h5c-2 2-3 0-5 3l1 1v-1l1 2s-1 0-1 1l-4-1c1-2 2-4 3-5z" class="I"></path><path d="M401 120h1l2 2c-1 1-2 1-4 1 2 1 3 1 4 1h1v2h-1l-1 2c-2 1-5 2-8 1l-4-1c-1 0-2 0-3 1v-2h0c4-2 5-4 6-7l4 1c0-1 1-1 1-1h2z" class="B"></path><path d="M397 124h1v-1l1 1 1-1c2 1 3 1 4 1h1v2h-1l-1-1c-1-1-3 1-5 1-1 0-1 0-3-1h0l2-1z" class="O"></path><path d="M401 120h1l2 2c-1 1-2 1-4 1l-1 1-1-1v1h-1c0-1 0-2 1-3 0-1 1-1 1-1h2z" class="G"></path><path d="M399 120h2c-1 1-2 2-2 4l-1-1v1h-1c0-1 0-2 1-3 0-1 1-1 1-1z" class="m"></path><path d="M403 125l1 1-1 2c-2 1-5 2-8 1l-4-1c1 0 1 0 2-1h1c1-1 1 0 2 0s3 0 4-1h2c1 0 1 0 1-1z" class="H"></path><path d="M413 98c2 1 4 1 6 1 2 1 3 2 5 2h1l-2 1v3 1 4l2 6h-1l-1-1c-1 1 0 2 0 3v1h-1-1v2c1 2-1 5-2 7v1c-1 2-3 5-5 7v-1c-5 3-11 2-16 1 0-1 2-1 2-1 3 0 5 0 7-1 3 0 4-2 6-5v-2c0-1 0-2-1-3-1 1-1 1-2 1-2 1-3 2-4 3h0c-1-1 0-1-1-1l-2 1 1-2h1v-2h-1c-1 0-2 0-4-1 2 0 3 0 4-1l-2-2 1-1c-1 0-1 0-1-1 1 0 2-1 2-2l-1-1 1-2v-2l1-2h0v-2-2l3-2c1 1 1 1 1 2l1-1c0-1 1-1 2-2v1c1-1 1-1 2-1-1-1-2-2-2-3l1-1z" class="c"></path><path d="M416 104c1 2 0 5 1 7s-1 5-1 7v7c-1 2-2 3-3 4v-2c0-2 1-3 1-4 1-2 1-4 1-6 0-4 1-8 1-13z" class="F"></path><path d="M413 98c2 1 4 1 6 1 2 1 3 2 5 2h1l-2 1h-1c-1 0-1-1-2-1-1-1-5-1-7-2v1c2 1 2 2 3 4h0c0 5-1 9-1 13 0 2 0 4-1 6 0-2 0-4-1-6 0-1 1-2 1-2 1-2 1-3 1-5 0-3 0-6-1-8-1-1-2-2-2-3l1-1z" class="V"></path><path d="M422 102h1v3 1 4l2 6h-1l-1-1c-1 1 0 2 0 3v1h-1-1v2c1 2-1 5-2 7v1c-1 2-3 5-5 7v-1c1-1 3-3 4-5v-3c1-2 1-4 1-6s0-4 1-6c0-2-1-4 0-6 0-1 1-1 1-2s0-3 1-5z" class="a"></path><path d="M423 110l2 6h-1l-1-1c-1 1 0 2 0 3h-1c0-2 0-6 1-8z" class="d"></path><path d="M412 103c1-1 1-1 2-1 1 2 1 5 1 8 0 2 0 3-1 5 0 0-1 1-1 2 1 2 1 4 1 6 0 1-1 2-1 4 0-1 0-2-1-3-1 1-1 1-2 1-2 1-3 2-4 3h0c-1-1 0-1-1-1l-2 1 1-2h1v-2h-1c-1 0-2 0-4-1 2 0 3 0 4-1l-2-2 1-1c-1 0-1 0-1-1 1 0 2-1 2-2l-1-1 1-2v-2l1-2h0v-2-2l3-2c1 1 1 1 1 2l1-1c0-1 1-1 2-2v1z" class="M"></path><path d="M406 115l1 1c-1 1-3 2-4 3-1 0-1 0-1-1 1 0 2-1 2-2l2-1z" class="Y"></path><path d="M409 110l3-2-1 4c-1 1-2 3-4 4l-1-1c1-1 2-2 3-4v-1z" class="K"></path><path d="M413 117c1 2 1 4 1 6 0 1-1 2-1 4 0-1 0-2-1-3-1 1-1 1-2 1l3-8z" class="P"></path><path d="M412 102v1 5l-3 2c0-1-1-1 0-2 0-1 1-1 1-2l-1-1h0l1-1c0-1 1-1 2-2z" class="g"></path><path d="M408 121l1 1c-1 2-2 3-4 5l-2 1 1-2h1v-2h-1c-1 0-2 0-4-1 2 0 3 0 4-1 0 0 1 0 2-1l1 1 1-1z" class="P"></path><path d="M404 122s1 0 2-1v2c-1 0-1 1-2 1s-2 0-4-1c2 0 3 0 4-1z" class="H"></path><path d="M404 113v-2l1-2c0 1 1 1 2 2 0-1 0-1 2-1v1c-1 2-2 3-3 4l-2 1-1-1 1-2z" class="i"></path><path d="M407 122l-1-1c0-2 4-5 5-6 0-1 0-2 1-2h1c0 1-1 3-2 4 0 1-1 2-2 3 0 0 0 1-1 1l-1 1z" class="Z"></path><path d="M408 103c1 1 1 1 1 2h0l1 1c0 1-1 1-1 2-1 1 0 1 0 2v1-1c-2 0-2 0-2 1-1-1-2-1-2-2h0v-2-2l3-2z" class="D"></path><path d="M408 103c1 1 1 1 1 2h0v1c-1 1-3 0-4 1v-2l3-2z" class="k"></path><path d="M423 118c0-1-1-2 0-3l1 1h1c1 3 2 6 5 8h0 1c1 1 3 2 4 2s1 1 2 1h0l1 1c1 0 4 1 5 3h0c0 1 0 4 1 5v1h0c1-1 2-1 3-1v1c0 1 1 2 2 2h0c0 2-1 2-2 3s-1 3-1 4l1 1c0 1 1 1 1 3s-1 4-3 5c-1 1-3 2-5 2-5 0-7-2-10-6-2-3-1-9 0-13l-3 4c0-3 0-5-1-7-1-1-2-1-3-1 0 2-1 5-3 6-1 1-2 1-2 1h-3v-2c-1 1-1 1-1 2-1-1-2-1-3-1h0-1l4-4c2-2 4-5 5-7v-1c1-2 3-5 2-7v-2h1 1v-1z" class="S"></path><path d="M438 133c1 1 1 2 1 3s-1 1-2 2l-1-1 1-1c0-1 1-1 1-2v-1z" class="F"></path><path d="M423 134v-3l1-1c1 1 1 2 2 3v1 1c-1-1-2-1-3-1z" class="H"></path><path d="M415 139c0-1 4-3 5-4h0l1 1c-2 1-3 1-4 4v1h1-3v-2z" class="M"></path><path d="M433 148c-1 0-1 0-1-1-1-2 1-5 2-7 0-1 1-1 1-2v-1h1l1 1h-1v1l-3 9z" class="E"></path><path d="M423 118c0-1-1-2 0-3l1 1h1c1 3 2 6 5 8h0 1v2c0 1 5 3 4 5l-6-5c-1 0-1 1-1 1v1c1 0 1 1 1 2h-1l-3-6c-1 0-1-2-1-2v-1-2h0c-2 1-2 1-3 2v-2h1 1v-1z" class="C"></path><path d="M424 116h1c1 3 2 6 5 8h0 1v2c0 1 5 3 4 5l-6-5c-2-3-4-6-5-10z" class="j"></path><path d="M431 124c1 1 3 2 4 2s1 1 2 1h0l1 1c1 0 4 1 5 3h0c0 1 0 4 1 5v1h-2 0c0-1-1-2-2-3l-1 2c0-1 0-2-1-3 0-1-2-2-3-2 1-2-4-4-4-5v-2z" class="G"></path><path d="M438 128c1 0 4 1 5 3h0v2c-2 0-3 0-5-1-1-2-1-2-1-4h1z" class="X"></path><path d="M439 136l1-2c1 1 2 2 2 3h0 2 0c1-1 2-1 3-1v1c0 1 1 2 2 2h0c0 2-1 2-2 3s-1 3-1 4l1 1c0 1 1 1 1 3s-1 4-3 5c-1 1-3 2-5 2-5 0-7-2-10-6 1-1 2-2 3-2v-1l3-9v-1h1c1-1 2-1 2-2z" class="Z"></path><path d="M441 148c2 0 2 0 3 2 0 1-1 2-1 2l-1 1-1-1-1-1 1-1v-2z" class="i"></path><path d="M440 144c1 0 3 0 4 1l2 1 1 1h-1c-1 0-2 0-3-1h-1-1 0l-1 1 1 1v2l-1 1c-1-1-2-1-3-3 0-1 2-3 3-4z" class="c"></path><path d="M436 139h1c0 1-1 2 0 3v1c1 1 2 0 3 1-1 1-3 3-3 4 1 2 2 2 3 3l1 1 1 1h-3c-2 0-4-2-6-4v-1l3-9z" class="n"></path><path d="M439 136l1-2c1 1 2 2 2 3h0 2 0c1-1 2-1 3-1v1c0 1 1 2 2 2h0c0 2-1 2-2 3s-1 3-1 4l-2-1c-1-1-3-1-4-1-1-1-2 0-3-1v-1c-1-1 0-2 0-3h-1v-1h1c1-1 2-1 2-2z" class="F"></path><path d="M439 136l1-2c1 1 2 2 2 3h0c0 1-1 2-2 3-1-1-2 0-3-1v3c-1-1 0-2 0-3h-1v-1h1c1-1 2-1 2-2z" class="g"></path><path d="M444 137c1-1 2-1 3-1v1c0 1 1 2 2 2h0c0 2-1 2-2 3s-1 3-1 4l-2-1c-1-1-3-1-4-1-1-1-2 0-3-1h0 1c2 0 4 0 6-1v-5z" class="B"></path><path d="M374 102c1 0 2 1 3 1h0c1 1 2 1 2 2h2 0v-1c2 0 2 1 3 2h0c0 1 1 1 1 2s1 1 1 2v6 2 1s-1 1-1 2l1 1h0s-1 1-2 1l-2 2h-1l-2 1c-2 0-3 1-5 1v1c-3 0-6-1-9 0l-1 1h0-3-1l-1 1c-4 0-7 0-11-2-2-1-4-3-6-4-1-1-2-1-4-2v1h-1s0 1-1 1v-1c-1-1-2-1-2-1-3-1-9-2-12-1-1 1-2 1-2 2h1c1 0 0 0 1 1-4 0-8 2-11 5l-2 1-3 5c-1-1-1-1-2-1l-1 1h0 0-1v-1l3-6c3-4 7-7 10-10 1-2 4-4 5-5v-1c1-1 2-2 4-2 1-1 2-1 3-2h0 6c1-1 3-1 5-2-1 1-1 2-2 2 0 1-1 1-2 1h5c1 0 2 0 3 1 1 0 3-1 4 0h1 3 13l2-1h0l1 1h0c1-1 1-4 2-5 0 1 1 2 1 2h2 2 0v-1l1-1v-3h0z" class="c"></path><path d="M355 116l1 1v2 1h-1c-1-1-3-2-4-3h1c1-1 2-1 3-1z" class="L"></path><path d="M371 122h0c1 0 3-1 4-1s1 0 2-1c-2 3-3 4-7 5h0c0-1-1-2-2-2h0v-1c1-1 1-1 1-2l2 2z" class="B"></path><path d="M303 135c0-1 1-2 2-3 1-2 3-5 5-6 1 0 2-2 3-2 0 1-3 3-4 5v1h0l-3 5c-1-1-1-1-2-1l-1 1h0 0z" class="E"></path><path d="M313 124c1-2 3-3 5-4 1 0 2 1 4 1h0c-1 1-2 1-2 2h1c1 0 0 0 1 1-4 0-8 2-11 5l-2 1h0v-1c1-2 4-4 4-5z" class="F"></path><path d="M318 120c1 0 1 0 2-1 1 0 2 0 2-1h2 0c1-1 2-1 2-1 1 0 2-1 3 0h1c-1 1-2 1-3 2l1 1h3c2 1 4 2 6 2h1v1h-1s0 1-1 1v-1c-1-1-2-1-2-1-3-1-9-2-12-1h0c-2 0-3-1-4-1z" class="C"></path><path d="M363 122c2 0 3-1 4-2h0 1v2 1h0c1 0 2 1 2 2h0 0c-5 1-10 0-14-3h0 0l1-1h0 2 3l1 1z" class="L"></path><path d="M357 121h0 2 3l1 1c-2 0-5 1-6 0h0-1 0 0l1-1z" class="G"></path><path d="M384 106c0 1 1 1 1 2s1 1 1 2v6 2 1s-1 1-1 2l1 1h0s-1 1-2 1l-2 2h-1l-2 1c-1-1-1 0-3 0h-1c0-1 0-1 1-1 1-1 2-1 3-2v-1c1 0 1-1 2-2v-1h0c1-1 0-5 0-6v-1h0 1v5 2c0 1 0 2-1 3v1c1 0 1 0 1-1h0c1-1 2-1 2-2v-1h1v-1-8h0v-1c-1-1-1-1-1-2v-1z" class="F"></path><path d="M329 117c10-2 16 3 24 7 4 2 7 3 11 5h-3-1c-7-1-11-5-17-8-4-3-8-5-13-4h-1z" class="n"></path><path d="M346 110h1c0 1 2 1 3 2v1h4 4s0 1-1 0c0 1-1 1-1 1-1 1 0 1-1 2-1 0-2 0-3 1h-1c-1-1-3-1-4-2h-2c-2-1-3-2-6-2-3-1-6-1-9 0l-9 2h0v-1c2 0 5-1 7-1 4-1 9-2 14-1 1 0 3 1 5 2 0-1 1-1 1-1v-1h-1c-1 0-1-1-1-2z" class="C"></path><path d="M354 113h4s0 1-1 0c0 1-1 1-1 1-1 1 0 1-1 2-1 0-2 0-3 1h-1c-1-1-3-1-4-2 1 0 3 1 4 1h1c-1-1-1-1-1-2 1 0 2 0 3-1z" class="k"></path><path d="M330 117c5-1 9 1 13 4 6 3 10 7 17 8l-1 1c-4 0-7 0-11-2-2-1-4-3-6-4-1-1-2-1-4-2h-1c-2 0-4-1-6-2h-3l-1-1c1-1 2-1 3-2z" class="S"></path><defs><linearGradient id="k" x1="330.788" y1="118.099" x2="331.675" y2="108.777" xlink:href="#B"><stop offset="0" stop-color="#aeabac"></stop><stop offset="1" stop-color="#d0cfd0"></stop></linearGradient></defs><path fill="url(#k)" d="M338 106c-1 1-1 2-2 2 0 1-1 1-2 1h5c1 0 2 0 3 1 1 0 3-1 4 0 0 1 0 2 1 2h1v1s-1 0-1 1c-2-1-4-2-5-2-5-1-10 0-14 1-2 0-5 1-7 1v1h0c-2 1-3 2-6 3 1-2 4-4 5-5v-1c1-1 2-2 4-2 1-1 2-1 3-2h0 6c1-1 3-1 5-2z"></path><path d="M338 106c-1 1-1 2-2 2 0 1-1 1-2 1h0c-3 1-7 1-10 2-2 1-3 1-4 2v-1c1-1 2-2 4-2 1-1 2-1 3-2h0 6c1-1 3-1 5-2z" class="H"></path><path d="M365 109h0l1 1h0v2c1 1 0 2 0 3v3c-1 1-2 2-3 2s-3 0-4 1h-2 0v-1h-1v-1-2l-1-1c1-1 0-1 1-2 0 0 1 0 1-1 1 1 1 0 1 0h-4-4v-1c-1-1-3-1-3-2h3 13l2-1z" class="J"></path><path d="M365 109h0l1 1c-1 1-1 2-2 2h-1v-2l2-1z" class="B"></path><path d="M357 113l1 1c0 1-1 1-1 2h-1l1 1h2l-3 2v-2l-1-1c1-1 0-1 1-2 0 0 1 0 1-1z" class="D"></path><path d="M353 110c2 0 7 0 8 1v1h-4c-1 0-1 0-1-1-2 0-2 0-3-1z" class="f"></path><path d="M359 117h0c2-1 3-2 5-3-1 2-2 4-4 6 0 0-2 0-3 1h0v-1h-1v-1l3-2z" class="E"></path><path d="M347 110h3 3c1 1 1 1 3 1 0 1 0 1 1 1h4c-1 1-2 1-3 1h-4-4v-1c-1-1-3-1-3-2z" class="L"></path><path d="M366 110c1-1 1-4 2-5 0 1 1 2 1 2h2 2 0l2 1c1 0 2 1 3 2s1 2 2 4c0 1-1 1-1 1v3l-2 2c-1 1-1 1-2 1s-3 1-4 1h0l-2-2c0 1 0 1-1 2v-2h-1 0c-1 1-2 2-4 2l-1-1h-3c1-1 3-1 4-1s2-1 3-2v-3c0-1 1-2 0-3v-2z" class="i"></path><path d="M369 114v-1c1 1 1 1 3 1 1 1 0 4 0 4l-3-4h0z" class="D"></path><path d="M378 110c1 1 1 2 2 4 0 1-1 1-1 1v3c0-2 0-3-2-5-1-2-3-2-5-2h1 2c1 0 2-1 3-1zm-9 4l3 4c0 2 0 2-1 4l-2-2c0 1 0 1-1 2v-2s0-1-1-1v-2c0-1 1-2 2-3z" class="G"></path><path d="M367 117h1l2 2c0 1 0 1-1 1 0 1 0 1-1 2v-2s0-1-1-1v-2z" class="C"></path><path d="M366 110c1-1 1-4 2-5 0 1 1 2 1 2h2 2 0l2 1c1 0 2 1 3 2-1 0-2 1-3 1h-2-1c-1 0-2 1-3 2v1h0c-1 1-2 2-2 3v2c1 0 1 1 1 1h-1 0c-1 1-2 2-4 2l-1-1h-3c1-1 3-1 4-1s2-1 3-2v-3c0-1 1-2 0-3v-2z" class="N"></path><path d="M375 108c1 0 2 1 3 2-1 0-2 1-3 1h-2-1c-1 0-2 1-3 2v1l-1-1v-1l1-1v-2c1-1 3 0 5 0 0-1 1-1 1-1z" class="C"></path><path d="M89 94l86 1h31 7 3 2c-1 0-3 0-3 1h0 1v1c1 1 3 2 3 3h0l2 4 3 5 5 5c0 1 2 2 2 2h1l2 2v2l-2-2s-1 0-1-1c-1 0-2-1-3-1a30.44 30.44 0 0 1 8 8c1 2 3 4 3 6h-1l-1-1v2l-3 2c0-2 0-4-1-6 0-1-1-1-1-1-1 0-1 0-1-1h-2c0-1 0-2-1-3-4-1-8 0-11 0-16 0-31 1-46 5l-20 6c-5 2-10 4-15 7v-1c-3 2-5 3-8 3h-1-2c-1-1-2-2-3-2 1-1 1-1 2-1l-9-3c0-1-1-2-1-3h-4c-1-1-1-1-2 0h-1-1c-1 1-1 1-2 1-2 0-5 0-8 1v-1h-2v-1l3-1h0 0v-1c2-1 3-2 5-3h1l5-2c2 0 3 0 4-1v-1h1c1-1 1-1 1-2v-2l-2 1c-2 2-5 2-7 3h-1c1 0 1 0 1-1 2-2 4-7 4-10h0v-2-1c-1 0-2-1-3-1l-3 6-1 1 1-4-2-4c-1-4-4-6-7-8 0 0 1 0 2 1 1 0 1 1 3 1-1-1-3-2-4-2h-1v-1h-1l2-1-1-1-4-1h1l-1-1h1s-1 0-2-1z" class="L"></path><path d="M143 110l1-1c0-1 1-2 2-2l2 2h1l-6 1z" class="K"></path><path d="M109 111c1 0 1 0 2 1s0 2 0 3h0 0-1-1v-2-2z" class="I"></path><path d="M106 109l1-4c1 0 1 0 1 1 1 1 1 2 1 4-1 0-2-1-3-1z" class="G"></path><path d="M94 97c4 1 7 2 9 6 1 1 1 2 1 3v3-1c-1 0-1-1-1-1 0-3-3-6-5-7-1 0-3-1-4-1h-1l2-1-1-1z" class="N"></path><path d="M112 121h-1v-1h1v-1h-3 0l1-1h2l1-1c0-1 0-3-1-4v-1-2h1c1 2 3 4 4 6s1 3 1 5h0c-1 1-1 2-2 2l-4 2v-1h1c1-1 1-1 1-2v-2l-2 1z" class="I"></path><path d="M118 121l-1-1-2-1c-1 0-1 0-1-1h0v-2-1-1c1 1 2 2 3 2 1 2 1 3 1 5h0z" class="g"></path><path d="M162 98l-1-1c1-1 2 0 3 0 2 1 3 2 6 3l1 1v-1h-1l1-1c0-1-2-2-3-2 3 0 6 0 8 2l1 1c1 0 2-1 2 0h0c0 2-1 4-1 6v3h-4c0-1-1-2-2-3v-1c-1-1-3-3-4-3h-1c-1-1-1-1-2-1l-3-3z" class="M"></path><path d="M210 104c-1 1-2 1-4 1-1 0-2-1-3-2 0-1-1-2-1-3s1-2 2-3c2-1 4-1 6-1h4c1 0 2 2 3 2 0 1 0 1 1 1l1 1 2 4c-1 0-2-1-4-2h0-3l-1 1c1 1 1 1 1 2h-1c-1-1-1-2-2-2l-2-1v1l1 1z" class="S"></path><path d="M210 96h4c1 0 2 2 3 2 0 1 0 1 1 1l-1 1h-3c-2-1-3-2-4-4z" class="R"></path><path d="M209 103h-3c-1-1-1-1-2-3 1 0 2-1 3-1 1-1 1 0 2 0v3 1z" class="D"></path><path d="M209 102l2 1c1 0 1 1 2 2h1c0-1 0-1-1-2l1-1h3 0c2 1 3 2 4 2l3 5 5 5c0 1 2 2 2 2h1l2 2v2l-2-2s-1 0-1-1c-1 0-2-1-3-1h0l-2-1c-1 0-2 0-2-1v-1c-2 0-3-1-5-2-1-1-2-1-3-2s-1-1-2-1c-2-1-2-3-4-4l-1-1v-1z" class="M"></path><path d="M224 113h1 0c0-1-1-3-3-4-1 0-1 0-2-1h0c1 0 2 0 3 1h1l5 5c0 1 2 2 2 2h1l2 2v2l-2-2s-1 0-1-1c-1 0-2-1-3-1h0l-2-1c-1 0-2 0-2-1v-1z" class="F"></path><path d="M131 100v-1h2 0c0-1 1-2 2-2h3v1c0 2-2 4-2 7 1-2 3-5 4-5l1-1c1 0 2-1 3-1l-1 1c-1 1-2 2-2 3h0c4-3 7-5 12-5 1 0 2 0 3 1h1c1 0 2 1 2 2 1 1 0 2 0 3-1 2-3 3-4 3h-2l-1-1 1-2h-1v-1c0-1 3-1 3-2h1c-1-1-2-2-4-2-5 1-8 7-11 10-2 2-4 3-5 4-2 0-3 2-5 3 0 1-1 2-2 2v-1l1-1c0-1 0-1 1-1l-1-1c-1 1-1 2-2 2v-2h-1l2-5c0-2-1-5-1-7 1 1 1 1 1 2 1-1 1-2 2-3z" class="c"></path><path d="M156 100c1 0 1 0 1 2l-2 2h-1v-1h-1 0-1v-1c0-1 3-1 3-2h1z" class="K"></path><path d="M128 101c1 1 1 1 1 2 1-1 1-2 2-3 0 2-1 5 1 6-1 2-3 6-4 7h-1l2-5c0-2-1-5-1-7z" class="D"></path><path d="M210 117h-2 0c-1-1-2-1-3-1h-4c-2 0-3-2-4-3v-2c1-2 2-3 3-5h0c0 2 0 3 1 4h0v1c0 1 1 2 1 3l2-2c-1 0-1-1-2-1 1-1 1-1 2-1h3c1 0 2-1 2-1 1-1 2 0 3 0l1 1 1-2c1 0 1 0 2 1s2 1 3 2c2 1 3 2 5 2v1c0 1 1 1 2 1l2 1h0a30.44 30.44 0 0 1 8 8c1 2 3 4 3 6h-1l-1-1c-1-3-3-6-5-8-3-1-7-2-10-1-3 0-9 1-10 0l2-2h-2c0 1-1 1-1 1-1 0-1 0-1-1 0 0 1 0 1-1h-1z" class="S"></path><path d="M214 108c1 0 1 0 2 1s2 1 3 2c2 1 3 2 5 2v1c-2-1-3-1-5-2h-4-2c-1 1-2 1-4 1-1 1-2 2-3 2-2 0-3 0-4-1l2-2c-1 0-1-1-2-1 1-1 1-1 2-1h3c1 0 2-1 2-1 1-1 2 0 3 0l1 1 1-2z" class="I"></path><path d="M209 113h-3-1c3-2 7-2 10-2 2 0 3 1 4 1h-4-2c-1 1-2 1-4 1z" class="M"></path><path d="M182 118l1-1c4-2 7-2 12-1h0c1 0 1 1 2 1-1 1-2 1-2 2h1c3 0 7 0 10 1 1-2 1-1 2-2 1 0 1 0 2-1h1c0 1-1 1-1 1 0 1 0 1 1 1 0 0 1 0 1-1h2l-2 2c1 1 7 0 10 0 3-1 7 0 10 1 2 2 4 5 5 8v2l-3 2c0-2 0-4-1-6 0-1-1-1-1-1-1 0-1 0-1-1h-2c0-1 0-2-1-3-4-1-8 0-11 0-16 0-31 1-46 5-1 0 0 0-1-1l8-2h-1c2-3 3-5 5-6z" class="n"></path><path d="M195 116h0c1 0 1 1 2 1-1 1-2 1-2 2h1c3 0 7 0 10 1h0 3 2l-1 1h-7-5c-2 0-6 1-8 0 1 0 1 0 1-1 1-2 3-3 4-4z" class="e"></path><path d="M182 118l1-1c4-2 7-2 12-1-1 1-3 2-4 4 0 1 0 1-1 1-2 1-3 1-5 1-1 0 0-1-2-1-1 1-2 1-2 2-1 1-2 1-3 1h-1c2-3 3-5 5-6z" class="c"></path><path d="M113 110c0-1 1-1 1 0h1v-1h-1l-2-2c-1-1-1-3-1-5l-8-2 4-3h2l1 1c-1 1-1 2-1 2h1c1-1 4-3 5-3h1c2 1 3 1 5 2 1 0 2 1 3 2l1-1h0 2c1 0 1 1 1 1 0 2 1 5 1 7l-2 5h0v1l-2 4-1 2h-1v-6-2c-1 2 0 6-1 7v1l-2 1s-1 0-1 1l-1-1c0-2 0-3-1-5s-3-4-4-6z" class="Z"></path><path d="M120 121c0-2 1-3 0-5v-1-1h-1c0-1-1-2-1-2v-1c0 1 1 1 1 1h1c0-2-2-3-3-5 0-2-2-3-3-4v-1l4 4c1 1 1 2 2 2h1v-2h0l1 3v1c1 1 1 1 1 2-1 2 0 6-1 7v1l-2 1zm4-20l1-1h0 2c1 0 1 1 1 1 0 2 1 5 1 7l-2 5h0v1l-2 4-1 2h-1v-6c1 2 1 2 1 3v1c0-1 1-1 1-2 0 0 0-1 1-1v-2-1c0-1 0-2-1-3 0-2 0-4 1-6 1 0 1 0 1-1v-1c-1 0-2 0-3 1v-1z" class="M"></path><path d="M127 113v-1c-1-1-1-1-1-2 1-1 2-2 3-2l-2 5h0z" class="X"></path><path d="M180 98c2-1 5 0 8-1h0 2c3 0 7-1 10 0l1 1-1 1h-2c1 0 1 1 1 1v2c-1 1-2 4-3 4h-1c-1 1-2 1-2 1 0 1 1 1 1 1l1 1c0 1-1 2-2 3-4 2-8 3-11 5v1c-2 1-3 3-5 6h1l-8 2c1 1 0 1 1 1l-20 6c2-3 5-2 8-5h-1l3-1 1-2 3-1h2 3c0-1 1-1 1-1l2-2c1-1 2-3 3-5h1c1-1 1-2 1-3v-2-2-3c0-2 1-4 1-6h0c0-1-1 0-2 0l-1-1c1-1 1-1 2-1h2z" class="c"></path><path d="M197 98c1 0 2 0 3 1h-2c1 0 1 1 1 1v2l-1-1c-2 0-2 0-3 1l-1-1 1-1v-2h2z" class="B"></path><path d="M181 106l1-1 2 1c-2 3-4 5-6 7v-2-2-3l1 1h1 1v-1z" class="o"></path><path d="M178 106l1 1h1 1l-3 4v-2-3z" class="J"></path><path d="M194 108s-1 0-1 1c-1 0-3 1-4 1h-3c-2 1-5 4-6 6l-1-1h1v-1c1-2 6-6 8-6h4l1-1c0 1 1 1 1 1z" class="F"></path><path d="M176 99c1-1 1-1 2-1h2 0l1 1c0 1 1 1 2 2v1h1 2l-2 4-2-1-1 1v1h-1-1l-1-1c0-2 1-4 1-6h0c0-1-1 0-2 0l-1-1z" class="H"></path><path d="M181 104v2 1h-1v-3h1z" class="B"></path><path d="M180 98l1 1c0 1 1 1 2 2v1l-1 1h-1c0-1 0-2-1-2v-1-2z" class="i"></path><path d="M183 102h1 2l-2 4-2-1-1 1v-2l1-1 1-1z" class="K"></path><path d="M180 98c2-1 5 0 8-1h0 2c3 0 7-1 10 0l1 1-1 1c-1-1-2-1-3-1h-2v2l-1-1c-1 0-2 1-2 1-1 1-2 3-3 3v-2c-1 0-2 1-3 1h-2-1v-1c-1-1-2-1-2-2l-1-1h0z" class="I"></path><path d="M200 97l1 1-1 1c-1-1-2-1-3-1v-1h3z" class="O"></path><path d="M181 99h0 2l1 1v-1c1 1 2 1 2 2v1h-2-1v-1c-1-1-2-1-2-2z" class="g"></path><path d="M171 123l1 1 2-2 3-3v-1c1-1 1-1 2-1h0l-2 3v1h0c1 0 3-3 5-4v1c-2 1-3 3-5 6h1l-8 2c1 1 0 1 1 1l-20 6c2-3 5-2 8-5h-1l3-1 1-2 3-1h2 3c0-1 1-1 1-1z" class="G"></path><path d="M161 127c2-1 5-1 7-1-2 1-6 1-7 2 2 1 7-1 9-2 1 1 0 1 1 1l-20 6c2-3 5-2 8-5h-1l3-1z" class="Y"></path><defs><linearGradient id="l" x1="136.628" y1="106.879" x2="171.16" y2="116.371" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#dbdada"></stop></linearGradient></defs><path fill="url(#l)" d="M162 98l3 3c1 0 1 0 2 1h1c1 0 3 2 4 3v1c1 1 2 2 2 3h4v2 2c0 1 0 2-1 3h-1c-1 2-2 4-3 5l-2 2s-1 0-1 1h-3-2l1-2c-2-1-3-2-5-4v2c-1 0-1 0-2-1v1l-1-1-3-3-2-1h-1l-3-1h0c-1-1-4-1-5 0h-1l-4 2-4 3c-2 1-3 1-5 1h-1v-1c-1 1-2 1-2 1h0c-1 1-3 1-4 1s-1 0-2 1l-1-1 2-1v-1c1-1 0-5 1-7v2 6h1l1-2 2-4v-1h0 1v2c1 0 1-1 2-2l1 1c-1 0-1 0-1 1l-1 1v1c1 0 2-1 2-2 2-1 3-3 5-3s5-2 7-2l6-1h1c1-1 2 0 3-1 0 0 2 0 3-1h2c1-1 2-2 3-4 0 0 0-1 1-1 0-1 0-1 1-1s1 1 2 2h0l-1-1v-2c-1 0-1-1-2-2h0z"></path><path d="M132 117c1 0 2 0 2-1 2 0 3-1 4-1l1 1-4 3c-2 1-3 1-5 1h-1l3-3z" class="M"></path><path d="M149 114l1-1c1 1 2 0 3 2l1-1c1 0 1 0 2 1 1 0 1 0 1 1h1c1 0 2 0 2 1 1 0 1 1 1 1v2c-1 0-1 0-2-1v1l-1-1-3-3-2-1h-1l-3-1h0z" class="S"></path><path d="M173 113h0c0 2 0 3-2 5h0-3v-2h0c-1-1-2-1-2-2h3v-1h2 2z" class="Z"></path><path d="M123 112v2 6h1l1-2 2-4h0v3h0l1-1v1 1h1c1 0 2-1 2-1h1l-3 3v-1c-1 1-2 1-2 1h0c-1 1-3 1-4 1s-1 0-2 1l-1-1 2-1v-1c1-1 0-5 1-7z" class="N"></path><path d="M173 113v-1c1 1 1 2 1 3s0 2-1 3v3l-2 2s-1 0-1 1h-3-2l1-2c1-1 1-3 2-4h3 0c2-2 2-3 2-5z" class="B"></path><path d="M167 124l6-6v3l-2 2s-1 0-1 1h-3z" class="N"></path><path d="M172 106c1 1 2 2 2 3h4v2 2c0 1 0 2-1 3h-1c-1 2-2 4-3 5v-3c1-1 1-2 1-3s0-2-1-3v1h0c0-1-1-2-2-3l1-1c0 1 0 1 1 0l-1-3z" class="G"></path><path d="M178 109v2 2c0 1 0 2-1 3h-1c0-2 0-5-2-7h4z" class="C"></path><path d="M162 98l3 3c1 0 1 0 2 1h1c1 0 3 2 4 3v1l1 3c-1 1-1 1-1 0l-1 1c-2-1-5 0-6-1v-2h0c-1-1-2 0-3 0h-1 0c0-1-1-1-1-1-3 3-6 3-10 3 1-1 2 0 3-1 0 0 2 0 3-1h2c1-1 2-2 3-4 0 0 0-1 1-1 0-1 0-1 1-1s1 1 2 2h0l-1-1v-2c-1 0-1-1-2-2h0z" class="H"></path><path d="M160 106c1-1 2-2 2-4h1c1 1 2 2 2 3v2h0 0c-1-1-2 0-3 0h-1 0c0-1-1-1-1-1z" class="M"></path><path d="M165 101c1 0 1 0 2 1h1c1 0 3 2 4 3v1l1 3c-1 1-1 1-1 0l-7-8z" class="F"></path><defs><linearGradient id="m" x1="143.723" y1="113.878" x2="111.399" y2="143.153" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#cdccce"></stop></linearGradient></defs><path fill="url(#m)" d="M143 114h1c1-1 4-1 5 0h0l3 1h1l2 1 3 3 1 1v-1c1 1 1 1 2 1v-2c2 2 3 3 5 4l-1 2-3 1-1 2-3 1h1c-3 3-6 2-8 5-5 2-10 4-15 7v-1c-3 2-5 3-8 3h-1-2c-1-1-2-2-3-2 1-1 1-1 2-1l-9-3c0-1-1-2-1-3h-4c-1-1-1-1-2 0h-1-1c-1 1-1 1-2 1-2 0-5 0-8 1v-1h-2v-1l3-1h0 0v-1c2-1 3-2 5-3h1l5-2c2 0 3 0 4-1l4-2c1 0 1-1 2-2h0l1 1c0-1 1-1 1-1l1 1c1-1 1-1 2-1s3 0 4-1h0s1 0 2-1v1h1c2 0 3 0 5-1l4-3 4-2z"></path><path d="M113 128v1c0 1-2 1-3 2v-1h-3c2-1 4-1 6-2z" class="a"></path><path d="M113 128l4-2c1-1 1-1 3 0l-7 3v-1z" class="T"></path><path d="M129 120h1c2 0 3 0 5-1 1 0 1 1 2 1v1h5 1s0 1 1 1 1-1 1-1h0l1 1v2c-2 0-4-1-6-2-4-1-8-1-13-1v-1h0s1 0 2-1v1z" class="B"></path><path d="M145 116l3 3v2c-1 0-1 1-2 1l-1-1h0s0 1-1 1-1-1-1-1h-1-5v-1c1 0 3-1 4-1l3-1c1-1 1-1 1-2z" class="V"></path><path d="M145 116l3 3c-1 0-2 1-2 1-1 0-1 0-1-1h-4l3-1c1-1 1-1 1-2z" class="D"></path><path d="M143 114c1 1 2 1 2 2s0 1-1 2l-3 1c-1 0-3 1-4 1s-1-1-2-1l4-3 4-2z" class="L"></path><path d="M161 118c2 2 3 3 5 4l-1 2-3 1c-1 1-3 2-5 1-1 1-1 1-3 1h0-1-2l1-1 3-1c1-1 2-2 3-4l1-1v-1c1 1 1 1 2 1v-2z" class="M"></path><path d="M159 120v-1c1 1 1 1 2 1v3c-1 1-2 3-4 3h-2v-1c1-1 2-2 3-4l1-1z" class="Z"></path><path d="M120 126c1-1 1-1 2-1 1 1 1 2 1 3-3 3-10 4-13 5-1-1-1-1-2 0h-1-1c-1 1-1 1-2 1-2 0-5 0-8 1v-1h-2v-1l3-1h0 0l10-2h3v1c1-1 3-1 3-2l7-3z" class="h"></path><path d="M97 132l10-2h3v1c-4 1-9 2-14 3h-2v-1l3-1h0 0z" class="d"></path><path d="M143 114h1c1-1 4-1 5 0h0l3 1h1l2 1 3 3 1 1-1 1c-1 2-2 3-3 4l-3 1c-2 0-4-1-6-2v-2c1 0 1-1 2-1v-2l-3-3c0-1-1-1-2-2z" class="H"></path><path d="M153 115l2 1 3 3 1 1-1 1-3-1v2h-1c-1-1 0-1 0-2h0c0-1-1-2-2-3h0c0-1 0-1 1-2z" class="B"></path><path d="M155 116l3 3h-3v-3z" class="j"></path><path d="M143 114h1c1-1 4-1 5 0h0v1c1 2 2 3 2 6l-1 1h-1l-1-1v-2l-3-3c0-1-1-1-2-2z" class="F"></path><path d="M126 125l-1-1 1-1c5 0 10 1 15 2 3 1 7 3 10 4 2 0 5-1 7-1h1c-3 3-6 2-8 5-5 2-10 4-15 7v-1c-3 2-5 3-8 3h-1-2c-1-1-2-2-3-2 1-1 1-1 2-1l-9-3c0-1-1-2-1-3 3 0 6 0 9-1h0 2v-1l2-2v-1l-1-1-2 1v-2c1 0 1 0 1-1h1z" class="h"></path><path d="M139 127c2 0 4 1 5 2-2 2-3 3-5 4-1-2 0-3 0-5v-1z" class="d"></path><path d="M134 126c1-1 3-1 4 0h0 1v1 1c0 2-1 3 0 5h-2l1 1-1 1h-2 0c-1 0-1 0-2-1v-2l-1-1c1-1 1-1 3-1l2-3v-1h-3z" class="H"></path><path d="M135 135c-1 0-1 0-2-1v-2l-1-1c1-1 1-1 3-1v1h3l-1 1c0 1-1 1-2 1v1h1v1h-1 0z" class="B"></path><path d="M126 125c1 1 1 2 3 2 1 0 1 0 2-1 0 0 2-1 3 0h0 3v1l-2 3c-2 0-2 0-3 1l1 1v2c1 1 1 1 2 1 0 1-1 1-1 2-2-2-3-3-5-4h1c0-1 0-1-1-2l-1 1s-1 1-2 0c0 1 0 0-1 0v-1l2-2v-1l-1-1-2 1v-2c1 0 1 0 1-1h1z" class="G"></path><path d="M126 132v-1h3l-1 1s-1 1-2 0z" class="M"></path><path d="M125 131v1c1 0 1 1 1 0 1 1 2 0 2 0l1-1c1 1 1 1 1 2h-1c2 1 3 2 5 4 0-1 1-1 1-2h0 2c0 1 1 1 0 2l-1 2c-3 2-5 3-8 3h-1-2c-1-1-2-2-3-2 1-1 1-1 2-1l-9-3c0-1-1-2-1-3 3 0 6 0 9-1h0 2v-1z" class="B"></path><path d="M125 131v1c1 0 1 1 1 0 1 1 2 0 2 0l1-1c1 1 1 1 1 2h-1c-1 1-3 2-4 4h0c-1 0-1 0-2-1s1-2 1-4h-1 0 2v-1z" class="F"></path><path d="M135 135h2c0 1 1 1 0 2l-1 2c-3 2-5 3-8 3h-1-2c-1-1-2-2-3-2 1-1 1-1 2-1h2c3 1 5-1 8-2 0-1 1-1 1-2h0zm335-34c4-2 7-2 11-1 2 1 3 1 5 3l4 4c3 3 6 4 7 9v1l1-1v4l-1 8c-1 0-2 0-3 1h-1l-2 1c0 1-1 2-2 2l2 2c0 1 0 1-1 2h2v-1h1l1 1c-1 2-1 4 0 5l1 1h1v3l-1 9-6 69c0 6 0 11-1 17l-4 19c-2 0-5 1-7 0l-2-11-1-6-3-17-8-26-3-7c-3-7-7-14-11-20-4-4-7-8-11-12l-1-1c1-1 1-1 3-2 2 0 4-1 5-2 2-1 3-3 3-5s-1-2-1-3l-1-1c0-1 0-3 1-4s2-1 2-3h0c-1 0-2-1-2-2v-1c-1 0-2 0-3 1h0v-1c-1-1-1-4-1-5h0c-1-2-4-3-5-3l-1-1h0l3-1 1 1c2 2 4 3 7 4s6 2 8 1h1 0l2-1h8v-1c1 1 4 1 5 1 1 1 2 3 2 4l6 3-3-3v-1h-1c-2-1-4-4-6-5 0-2-2-3-3-4v-1l2-2h-1l-1-8c-1-2-1-3-1-5 0 1 0 1-1 1v-1c1-4 2-5 5-8z" class="S"></path><path d="M483 207h1s0-1 1-2c0 0 0-1 1-1v2c0 2-1 5-2 7v-2c1-1 1-2 0-3 0 1 0 1-1 1h0c-1-1 0-1 0-2z" class="R"></path><path d="M478 215h1l3-4c-1 2-2 3-2 5l-3 3c-1 2-2 3-3 4h0c-1-1-1-3-2-4v-1c2 0 5-2 6-3h0z" class="D"></path><path d="M478 215h1c-1 2-1 3-3 4h-4v-1c2 0 5-2 6-3z" class="S"></path><path d="M483 143v-1c5 8 7 17 7 25-1 2 0 4-1 5 0 0 0-1-1-1v-6c0-6-1-15-4-21l-1-1z" class="V"></path><path d="M483 207l1-2c0-1 0-3 1-4 1 0 0-1 0-1v1l-1-1 3-3c0-1 0-1 1-2h0c0 1 0 0 1 1v7h-1c-1 1-1 3-1 3h-1v-2c-1 0-1 1-1 1-1 1-1 2-1 2h-1z" class="N"></path><path d="M468 110c0 1 1 2 2 3 0 0-1 1-1 2v3c0 3 1 5 3 8 1 2 4 5 5 5 4 4 10 9 12 14h0c-1 1-1 2 0 3-2-4-3-7-7-10 0-1-1-1-2-2s-2-2-3-2h-1c-2-1-4-4-6-5 0-2-2-3-3-4v-1l2-2h-1l-1-8 1-4z" class="Q"></path><path d="M469 122c2 4 4 6 6 9 3 2 5 4 7 7 0-1-1-1-2-2s-2-2-3-2h-1c-2-1-4-4-6-5 0-2-2-3-3-4v-1l2-2z" class="E"></path><path d="M469 115h0c1-1 2-2 2-3v-1h1l1 1c0 1 1 1 2 2-1 2-2 5-1 7 1 3 4 4 5 6l-1 1h0c1 2 2 3 3 4h0 0-1c-1 0-2-1-3-1s-4-3-5-5c-2-3-3-5-3-8v-3z" class="S"></path><path d="M469 118c1 2 2 3 3 5v1h0v2c-2-3-3-5-3-8zm4-6c0 1 1 1 2 2-1 2-2 5-1 7 1 3 4 4 5 6l-1 1-1-1c-2-1-5-5-5-7v-2-1-2c0-1 1-1 1-2v-1z" class="F"></path><path d="M479 127l1 1c2 2 5 3 9 4l2 2c0 1 0 1-1 2h2v-1h1l1 1c-1 2-1 4 0 5l1 1h1v3l-1 9v-2 2c0 1 0 2-1 3v-2l-1-3v3c-1-1-1-1-1-2 0-3-2-5-3-8h0c-2-5-8-10-12-14 1 0 2 1 3 1h1 0 0c-1-1-2-2-3-4h0l1-1z" class="L"></path><path d="M479 127l1 1c2 2 5 3 9 4l2 2c0 1 0 1-1 2l-7-2c6 5 9 11 9 19h0c0-3-2-5-3-8h0c-2-5-8-10-12-14 1 0 2 1 3 1h1 0 0c-1-1-2-2-3-4h0l1-1z" class="M"></path><path d="M487 206s0-2 1-3h1c0 6 1 11 0 17-1-2-1-4-1-5-1 2-1 4-2 6-2 1-2 3-2 6-1 1-2 3-3 4-1 3-1 7-3 9-1 0-1-1-1-2l-1-1c-1 1-2 1-2 2v3l-3-17h1l2-2h0c0 1 1 1 2 2 0 1-2 2-1 4v1c1 0 1 0 1-1 1 0 1 0 1-1h1c2-5 5-10 6-15 1-2 2-5 2-7h1z" class="X"></path><path d="M486 206h1l-6 18c-2 5-5 9-6 14l3-6-1 6-1-1c-1 1-2 1-2 2v3l-3-17h1l2-2h0c0 1 1 1 2 2 0 1-2 2-1 4v1c1 0 1 0 1-1 1 0 1 0 1-1h1c2-5 5-10 6-15 1-2 2-5 2-7z" class="J"></path><path d="M474 223c0 1 1 1 2 2 0 1-2 2-1 4v1c1 0 1 0 1-1 1 0 1 0 1-1l-2 6c-1-2-1-3-1-5-1-1 0-1-1-1 0-1 0-1-1-2v-1l2-2h0z" class="X"></path><path d="M489 148c-1-1-1-2 0-3 1 3 3 5 3 8 0 1 0 1 1 2v-3l1 3v2c1-1 1-2 1-3v-2 2l-6 69c0 6 0 11-1 17-1 0-2 0-2 1h-1 0c1-2 1-3 2-5v-7h-1 0l-1 1-1-3c0-3 0-5 2-6 1-2 1-4 2-6 0 1 0 3 1 5 1-6 0-11 0-17v-7c-1-1-1 0-1-1h0v-1c1-2 1-4 2-6 1-5 2-11 2-16s0-10-1-14c0-4-1-7-2-10z" class="n"></path><path d="M484 227c0-3 0-5 2-6 1 4 2 11 1 15v-7h-1 0l-1 1-1-3z" class="d"></path><path d="M488 171c1 0 1 1 1 1-1 10-3 23-11 29-2 2-4 3-7 3-1 0-2-1-3-1-1-1-2-3-2-4 0-3 0-6 2-8l2 2c0-1 1-1 1-1h1 2v1c1-1 2-2 3-2h0l1-2c-1-2-1-2-3-3 3-1 5-2 7-4 4-3 6-7 6-11z" class="g"></path><path d="M477 191c3-2 4-7 7-8 0 5-1 9-5 13-2 2-5 2-8 2-2 0-2 0-3-1v-2l2-2c0-1 1-1 1-1h1 2v1c1-1 2-2 3-2h0z" class="F"></path><path d="M484 227l1 3 1-1h0 1v7c-1 2-1 3-2 5h0 1c0-1 1-1 2-1l-4 19c-2 0-5 1-7 0l-2-11-1-6v-3c0-1 1-1 2-2l1 1c0 1 0 2 1 2 2-2 2-6 3-9 1-1 2-3 3-4z" class="i"></path><path d="M481 245v4h0c-1 1-1 2-1 3v1h-1v-1-1-1c-1-1-1-2-1-4l1 1 2-2z" class="h"></path><path d="M474 242v-3c0-1 1-1 2-2l1 1c0 1 0 2 1 2l-1 1h-1v2c0 2 0 4-1 5l-1-6z" class="H"></path><path d="M485 241h0 1 1c-1 2-1 4-3 5 0 1-1 2-2 4l-2 2c0-1 0-2 1-3h0v-4c1-2 2-3 4-4z" class="P"></path><path d="M485 230l1-1h0 1v7c-1 2-1 3-2 5-2 1-3 2-4 4l-2 2-1-1c1-3 3-5 4-8 1-1 2-2 2-3v-2-1h1v3c1-1 1-3 0-5z" class="G"></path><path d="M449 139c2-1 3-1 5-1-3 3-5 6-5 10 0 3 0 6 3 8 0 0 1 1 2 1s2 0 2 1c-1 2-3 4-3 6v3c0 1 0 3 1 4l-1 1c0 1 1 2 2 2l5 3-1 1h-2 0l2 3c2 2 4 3 7 4h5c1 0 2 0 3 1h1c2 1 2 1 3 3l-1 2h0c-1 0-2 1-3 2v-1h-2-1s-1 0-1 1l-2-2c-2 2-2 5-2 8 0 1 1 3 2 4 1 0 2 1 3 1h-3 0c2 1 3 1 3 2h1c3 1 5 0 8-1 2-1 3-3 5-5-3 5-4 9-8 12-2 2-4 3-6 5 2 0 4-1 6-2h1 0 0c-1 1-4 3-6 3v1c1 1 1 3 2 4l-2 2h-1l-8-26-3-7c-3-7-7-14-11-20-4-4-7-8-11-12l-1-1c1-1 1-1 3-2 2 0 4-1 5-2 2-1 3-3 3-5s-1-2-1-3l-1-1c0-1 0-3 1-4s2-1 2-3z" class="f"></path><path d="M453 167c0 1 0 3 1 4l-1 1c-1-1-2-3-2-5h2z" class="J"></path><path d="M447 156l1-1c1 2 2 3 3 5l-1 4h0 0c-1-2-2-3-3-5-1-1-1-2 0-3z" class="T"></path><path d="M460 192h2v-1c-1-2-2-4-3-5l1-1 1 1h2c0-1 1-1 2-1v1c0 1-1 1-2 1v2 1c1 1 1 3 0 4v1h0v4l-3-7zm-22-32c2 0 5 0 6 1l4 6c1 2 1 3 1 5-4-4-7-8-11-12z" class="H"></path><path d="M471 185c1 0 2 0 3 1h1c2 1 2 1 3 3l-1 2h0c-1 0-2 1-3 2v-1h-2-1s-1 0-1 1l-2-2c-2 2-2 5-2 8 0 1 1 3 2 4 1 0 2 1 3 1h-3 0c2 1 3 1 3 2l-2-1c-2 0-3-2-4-4s-1-6 0-9c2-3 3-5 6-7z" class="M"></path><path d="M468 191c1-1 2-1 3-2 1 1 2 1 3 2v1h-2-1s-1 0-1 1l-2-2z" class="I"></path><path d="M470 101c4-2 7-2 11-1 2 1 3 1 5 3l4 4c3 3 6 4 7 9v1l1-1v4l-1 8c-1 0-2 0-3 1h-1l-2 1c0 1-1 2-2 2-4-1-7-2-9-4l-1-1c-1-2-4-3-5-6-1-2 0-5 1-7-1-1-2-1-2-2l-1-1h-1v1c0 1-1 2-2 3h0c0-1 1-2 1-2-1-1-2-2-2-3l-1 4c-1-2-1-3-1-5 0 1 0 1-1 1v-1c1-4 2-5 5-8z" class="F"></path><path d="M488 119v2 2h-1l-4-2h0 1v-1l1 1c1-1 2-1 3-2z" class="P"></path><path d="M466 109c1-2 2-3 3-5h3c-2 1-3 2-3 4l-1 2-1 4c-1-2-1-3-1-5z" class="U"></path><path d="M492 111c1 0 1 0 2 1 0 0 1 2 0 3 0 2-1 5-3 7-1 0-1 1-1 1v-1h0c1-2 3-5 2-7v-4z" class="g"></path><path d="M487 110v-2l2-1c0 1 1 1 1 2l1 1 1 1c-1 1-1 3 0 4v1s0 1-1 1c0 0-1 0-1-1-1 1-2 1-2 3-1 1-2 1-3 2l-1-1c1-3 2-6 3-10z" class="M"></path><path d="M472 104c3-1 6-2 9-1 2 1 4 3 5 5 0 1 1 2 1 2-1 4-2 7-3 10v1h-1-2 0c2-2 0-3 1-5v-2s-1 0-2-1l-1-1c-1-1-1 0-2-1-1 1-1 1-1 2l-1-1v2c-1-1-2-1-2-2l-1-1h-1v1c0 1-1 2-2 3h0c0-1 1-2 1-2-1-1-2-2-2-3l1-2c0-2 1-3 3-4z" class="L"></path><path d="M478 110c0-2 1-3 3-5 1 1 2 4 2 5 0 2 0 3-1 4 0 0-1 0-2-1l-1-1c-1-1-1 0-2-1l1-1z" class="m"></path><path d="M478 110h3c0 1 0 1 1 2 0-1 0-2 1-2h0c0 2 0 3-1 4 0 0-1 0-2-1l-1-1c-1-1-1 0-2-1l1-1z" class="G"></path><path d="M470 113l1-2c1-2 4-6 7-7v1c0 2-1 5-3 7v2c-1-1-2-1-2-2l-1-1h-1v1c0 1-1 2-2 3h0c0-1 1-2 1-2z" class="N"></path><path d="M476 113c0-1 0-1 1-2 1 1 1 0 2 1l1 1c1 1 2 1 2 1v2c-1 2 1 3-1 5h0c0 1-1 1-1 1h-1v1l4 4c2 0 5-2 7-2l1-1c3-1 5-5 6-7l1-1v4l-1 8c-1 0-2 0-3 1h-1l-2 1c0 1-1 2-2 2-4-1-7-2-9-4l-1-1c-1-2-4-3-5-6-1-2 0-5 1-7v-2l1 1z" class="f"></path><path d="M487 130c0-1 1-2 1-3h1c0 1 0 1 1 1v-2h2 0c0 1-1 1-1 2h0c1 1 1 1 2 1l-2 1h-1v-1h-1c-1 0-1 0-2 1h0z" class="D"></path><path d="M480 128c1 0 2 1 3 0h1 0c1 1 1 1 1 2h1 1 0c1-1 1-1 2-1h1v1h1c0 1-1 2-2 2-4-1-7-2-9-4z" class="B"></path><path d="M476 113c0-1 0-1 1-2 1 1 1 0 2 1l1 1c1 1 2 1 2 1v2c-1 2 1 3-1 5-1-2-2-3-3-4s-1-3-2-4z" class="T"></path><path d="M437 127l3-1 1 1c2 2 4 3 7 4s6 2 8 1h1 0l2-1h8v-1c1 1 4 1 5 1 1 1 2 3 2 4l6 3c1 2 3 3 3 4v1l1 1c3 6 4 15 4 21v6c0 4-2 8-6 11-2 2-4 3-7 4h-1c-1-1-2-1-3-1h-5c-3-1-5-2-7-4l-2-3h0 2l1-1-5-3c-1 0-2-1-2-2l1-1c-1-1-1-3-1-4v-3c0-2 2-4 3-6 0-1-1-1-2-1s-2-1-2-1c-3-2-3-5-3-8 0-4 2-7 5-10-2 0-3 0-5 1h0c-1 0-2-1-2-2v-1c-1 0-2 0-3 1h0v-1c-1-1-1-4-1-5h0c-1-2-4-3-5-3l-1-1h0z" class="S"></path><path d="M460 160c1-2 3-3 5-4h1l-3 3v1c-1 1-3 1-4 2h0c-2 0-2 0-3 1h-1l-1 1v-1l-1 1c0-2 2-4 3-6l1-2 2 2h-1l2 2z" class="X"></path><path d="M458 158l2 2-2 1c-1 0-1 0-2-1l2-2z" class="Z"></path><path d="M468 177c0-1-1-2 0-4 0-3 3-7 6-9l-3 5c1 3 0 3-1 6h1l2 2 1-1v-1c1 1 1 2 2 2-1 1-2 1-3 1h-1v1h2 1-4-1c0-1-1-2-2-2z" class="j"></path><path d="M471 169c1 3 0 3-1 6h1l2 2 1-1v-1c1 1 1 2 2 2-1 1-2 1-3 1h-1c-1-1-2-1-2-2-1-2 0-5 1-7z" class="G"></path><path d="M479 162h0l2-1 2 1v1 3h1c0 2-1 4-3 4 0 1-1 1-1 1 0-1 0-2 1-3 1 0 0-1 0-2 0 1-1 2-2 2h0c-1 1-2 1-3 1v-1l1-1h-1c1-1 1-2 2-3 0-1 0-2 1-2z" class="X"></path><path d="M477 167h0c1 0 1-1 2-1h0 1l-1 2h0c-1 1-2 1-3 1v-1l1-1z" class="Z"></path><path d="M481 166h0v-2l1-1 1 1v2h0 1c0 2-1 4-3 4 0 1-1 1-1 1 0-1 0-2 1-3 1 0 0-1 0-2z" class="G"></path><path d="M454 171c2 2 3 2 6 2h0c1-1 1-1 2-1v1c1 1 4 3 5 4h1c1 0 2 1 2 2h1c0 1 0 1 1 2-1 0-1 0-2-1h-1c-3 0-5 0-8-1-1 0-1-1-2-1l1-1-5-3c-1 0-2-1-2-2l1-1z" class="l"></path><path d="M455 174h1c2 0 3 1 6 2 2 2 5 2 8 3h1c0 1 0 1 1 2-1 0-1 0-2-1h-1c-3 0-5 0-8-1-1 0-1-1-2-1l1-1-5-3z" class="C"></path><path d="M477 156c2-1 4 0 5 1l2 1 1 1c2 4 2 9 0 14h0c-2 3-4 5-7 6h-3-1-2v-1h1c1 0 2 0 3-1-1 0-1-1-2-2v-1c1-1 2-2 3-2l3-1s1 0 1-1c2 0 3-2 3-4h-1v-3-1l-2-1-2 1h0v-2l-1-1c-1 0-1-1-2-1 1-1 1-1 2-1h-1v-1z" class="L"></path><path d="M478 157c2 1 5 3 6 5v4h-1v-3-1l-2-1-2 1h0v-2l-1-1c-1 0-1-1-2-1 1-1 1-1 2-1z" class="C"></path><path d="M474 175v-1c1-1 2-2 3-2h0c0 2 0 4-1 6 1-1 3-1 4-2 2-1 2-3 5-3-2 3-4 5-7 6h-3-1-2v-1h1c1 0 2 0 3-1-1 0-1-1-2-2z" class="P"></path><path d="M484 144c3 6 4 15 4 21v6c0 4-2 8-6 11-2 2-4 3-7 4h-1c-1-1-2-1-3-1h-5c-3-1-5-2-7-4l-2-3h0 2c1 0 1 1 2 1 3 1 5 1 8 1h1c1 1 1 1 2 1-1-1-1-1-1-2h4 3c3-1 5-3 7-6h0c2-5 2-10 0-14 0-3-1-8-3-11h-1 0c1-1 1-2 1-3v-1l1 1 1-1z" class="X"></path><path d="M471 181h3v1h-2l-1-1z" class="S"></path><path d="M471 179h4 3c-2 1-4 2-6 2-1-1-1-1-1-2z" class="E"></path><path d="M459 181l-2-3h0 2c1 0 1 1 2 1 3 1 5 1 8 1 0 1-1 2-1 3h-2c-2-1-5-1-7-2zm-22-54l3-1 1 1c2 2 4 3 7 4s6 2 8 1h1 0l2-1h8v-1c1 1 4 1 5 1 1 1 2 3 2 4l6 3c1 2 3 3 3 4v1l1 1-1 1-1-1v1c0 1 0 2-1 3h0 1c2 3 3 8 3 11l-1-1-2-1c-1-1-3-2-5-1v1h1c-1 0-1 0-2 1l-4 2c-2-2-4-3-6-4h-1c-2 1-4 2-5 4l-2-2h1l-2-2-1 2c0-1-1-1-2-1s-2-1-2-1c-3-2-3-5-3-8 0-4 2-7 5-10-2 0-3 0-5 1h0c-1 0-2-1-2-2v-1c-1 0-2 0-3 1h0v-1c-1-1-1-4-1-5h0c-1-2-4-3-5-3l-1-1h0z" class="c"></path><path d="M454 144c2-2 4-4 6-4h1c-1 2-2 3-2 5l-1-1h-4z" class="J"></path><path d="M454 147c0-1 0-1-1-2l1-1h4l1 1c0 1 1 2 2 3-1 1-1 1-2 1l-1-1c0-1 0-1-1-2 0 0-1 0-1 1h-2z" class="B"></path><path d="M437 127l3-1 1 1c2 2 4 3 7 4s6 2 8 1h1 0l2-1h8v-1c1 1 4 1 5 1 1 1 2 3 2 4l6 3c1 2 3 3 3 4v1c-1-2-3-3-5-5-3-2-8-5-11-5l-3 1c2 1 6-1 9 2h-1 0-1c-5-1-10 0-14 1-1 1-2 1-3 1h0c-2 0-3 0-5 1h0c-1 0-2-1-2-2v-1c-1 0-2 0-3 1h0v-1c-1-1-1-4-1-5h0c-1-2-4-3-5-3l-1-1h0z" class="F"></path><path d="M437 127l3-1 1 1v1c0 1 4 3 5 4 1 0 1 1 2 1 0 1 0 1-1 2h0 2c0 1-1 1-2 2v-1c-1 0-2 0-3 1h0v-1c-1-1-1-4-1-5h0c-1-2-4-3-5-3l-1-1h0z" class="J"></path><path d="M444 136v-1c1-1 1-1 2-1l1 2c-1 0-2 0-3 1h0v-1z" class="g"></path><path d="M467 130c1 1 4 1 5 1 1 1 2 3 2 4l6 3c1 2 3 3 3 4v1c-1-2-3-3-5-5-3-2-8-5-11-5h-4c-1 0-5 1-6 1l-1-1c2 0 5 0 6-1h4l1-1v-1z" class="G"></path><path d="M470 138c3 0 7 2 9 3s3 2 3 3v1c0 1 0 2-1 3h0 1c2 3 3 8 3 11l-1-1-2-1c-1-1-3-2-5-1v1h1c-1 0-1 0-2 1l-4 2c-2-2-4-3-6-4h-1c-2 1-4 2-5 4l-2-2h1l-2-2-1 2c0-1-1-1-2-1l2-1c0-2-3-3-4-4s-1-2-2-3c2 1 4 2 6 2h0c-1-1 0-2-1-3 0 0 0-1-1-1h2c0-1 1-1 1-1 1 1 1 1 1 2l1 1c1 0 1 0 2-1h0c2-2 3-4 6-5v-1-2c1-1 2-1 3-2z" class="D"></path><path d="M471 156l1-1v1c1 1 1 1 1 2h-1-1v-2z" class="g"></path><path d="M467 143v-1-2c1-1 2-1 3-2v1 2h-1c0 1-1 1-2 2z" class="J"></path><path d="M477 144c1 0 4 1 5 1 0 1 0 2-1 3-1-1-2-1-3-2h0 0l-1-2z" class="C"></path><path d="M474 147l-1-1c-1-1-2-1-4-2h0c1-1 2-3 4-3l1 1 1-1c1 1 1 2 2 2v1l1 2-1-1-1 1h0v1h-1v-1c-1 0-1 1-1 1z" class="X"></path><path d="M474 147s0-1 1-1v1h1v-1h0l1-1 1 1h0 0c1 1 2 1 3 2h0c-2 1-5 1-6 2s-2 2-3 1h-3l1-1c1-1 3-2 4-3zm-17 9c1-1 2-2 2-4h0c3-1 5-2 8-4-1 3-2 5-2 8-2 1-4 2-5 4l-2-2h1l-2-2z" class="c"></path><path d="M481 148h1c2 3 3 8 3 11l-1-1-2-1c-1-1-3-2-5-1-1 0-1 0-1-1l1-2v-1c-1 0-2-1-2-2 1-1 4-1 6-2z" class="K"></path><path d="M477 153l1-1v1c1 1 1 1 2 1l1-1v-1-1l1 1v1l1 1c0 2 1 3 1 4l-2-1c-1-1-3-2-5-1-1 0-1 0-1-1l1-2z" class="N"></path><path d="M272 434v1l1 2h0l1-1v5h1v2h0v3l4 7h1c2 2 3 3 3 6v1c1 1 1 1 1 2s0 1-1 1v3c0 2-1 3-1 5h3l1 1 1-1v-1c0 2 0 4 1 5h1 0v2c1 3 3 7 4 10 0 0 1 0 1 1h1l1-1h1 2v1c0 2-1 2-1 3-1 0-1 0-1-1-1 0-2 1-2 1v1c1 1 2 2 3 2h0v2s0 1 1 1l-1 1h-1v1h0l1 3v-1 2 4h1c0 1 1 2 1 3h0c0 1 1 2 0 3 1 1 1 1 2 1 1 1 2 1 2 2s0 1 1 1c0 1 1 2 1 3h0 1c1 0 1 0 2-1h2l-1 1v1h0c-3 5-7 7-12 10h1l-1 1c-2 1-3 3-5 4-1 1-2 1-3 2l-1 1-1 2-2 3c-2 2-4 5-5 8-1 2-1 3-2 5v1c-1 1-2 1-2 2-1 1-1 2-1 2v2c-1-1-1-2-1-3h0c0-2 0-3-1-4h-1 0-1v3c0 1-1 2 0 3l-2-5h-1c0-1-1-2-2-2l-3-3c1 0 1-1 1-1l-2-3h0 0c1-1 0-3 0-4s-1-1-2-2l-1-2h0l-5-3c-1-1-3-2-5-3h-4l-1-1 1-1c-1-2-5-4-6-5s-5-2-7-3c-1 0-1-1-2-1-1-1-2-4-2-6v-4c-1 0-1 1-1 1v1 4h-1v-6h0c-1-4 0-9 0-12l2-49c1-1 0-10 0-12h1c1 1 1 1 3 2 1 0 2 1 3 1h3c1 1 1 1 2 1h1 1c1 1 2 1 3 2 1 0 2 1 3 1l3 2c2 0 4 1 5 1l-1-2c0-1-1-1-1-2h0v-1c2 0 2 0 3 1l1 1h0v-1c1-2 1-3 2-4h1l2 2 1-1c0-2 0-3-1-5 0 0 0-1 1-1h2 1c1 1 1 1 2 1l1-1-1-2 1-1h0l1-1z" class="R"></path><path d="M273 491l1-1v-2l2 2v1h1l1 1h-4-1c-1-1-2-1-4-2h-1 1 2c1 0 2 0 2 1z" class="W"></path><path d="M280 496l1 1-1 1 1 1h-3c-1 0-3 0-4-1 1 0 1 0 1-1 2-1 3-1 5-1z" class="J"></path><path d="M252 490h1c2 1 4 3 5 4 2 1 5 2 7 3l-1 1h0c-1 0-2-1-3-1-3-1-8-4-9-7z" class="c"></path><path d="M285 500l2-2c2-1 4-2 7-1 1 0 2 1 3 2h0l-1 1c-3-1-4 0-7 0h-3-1z" class="J"></path><path d="M282 493v1 1c-1 1-2 1-2 1-2 0-3 0-5 1h-2c-1 0-5-1-6-3h1 7c2 0 4 0 7-1z" class="B"></path><path d="M268 494h0-1c-3-1-5-1-7-3h-1l-1-1-2-2h1-1c3 0 5 2 8 3s6 1 9 1h1c2 0 4 1 5 1h0-1c-1 0-1 0-2 1h-1 0-7z" class="c"></path><path d="M246 492l-1-3h1c2 3 4 5 7 7s8 3 11 7l-1 1-9-3c-2-1-3-3-4-4-1-2-2-4-4-5z" class="f"></path><path d="M290 489c1-1 1-1 2-1 1 1 1 1 1 2-2 3-5 5-8 7v-1l-2 1-2 2-1-1 1-1-1-1s1 0 2-1v-1-1c1-1 2-2 4-3 1 0 1 0 2 1 0-1 1-2 2-2z" class="L"></path><path d="M286 490c1 0 1 0 2 1h0l-2 2c-2 1-3 2-4 3 0 1 1 1 1 1l-2 2-1-1 1-1-1-1s1 0 2-1v-1-1c1-1 2-2 4-3z" class="I"></path><path d="M248 479l2 1c5 2 10 4 15 4 1 0 2 0 2 1 1 1 3 1 4 1v4h-2-1 0-1c-3-1-5-3-8-4-4-2-8-3-11-5l-1-1 1-1z" class="L"></path><path d="M233 465c1-2 1-3 2-4 1-2 2-3 4-3-1 2-2 5-2 8v3h1c0 3 2 4 2 6 2 2 5 5 7 5l1 1h-1-2c1 2 2 4 4 5 1 0 2 1 3 2l-1 1-4-4c-1 0-2 0-4-1-1-2-2-4-3-5-2-4-5-6-7-10h0v-4h0z" class="T"></path><path d="M236 473l1-1 3 3c2 2 5 5 7 5l1 1h-1-2c-4-2-6-5-9-8z" class="S"></path><path d="M233 465c1-2 1-3 2-4 1-2 2-3 4-3-1 2-2 5-2 8v3h1c0 3 2 4 2 6l-3-3-1 1c-1-1-2-3-2-4h0c0-2-1-3-1-4z" class="F"></path><path d="M287 471v-1c0 2 0 4 1 5h1 0v2c1 3 3 7 4 10 0 0 1 0 1 1h1l1-1h1l-1 1-1 1h-1l-1 1c0-1 0-1-1-2-1 0-1 0-2 1-1 0-2 1-2 2-1-1-1-1-2-1-2 1-3 2-4 3-3 1-5 1-7 1h0 1c1-1 1-1 2-1h1 0c-1 0-3-1-5-1h4l-1-1h-1v-1l-2-2v-4c1-1 1-3 1-4 1-1 1-1 2-1v-1c1-1 1-1 2-1l1-1h-2l1-1 2-1 1-3h3l1 1 1-1z" class="G"></path><path d="M288 487c0 1 1 1 2 2-1 0-2 1-2 2-1-1-1-1-2-1 0-1 1-2 2-3z" class="D"></path><path d="M289 475h0v2 1h-2-1l3-3z" class="X"></path><path d="M288 487c0-1 0-2 1-3l4 3h0s1 0 1 1h1l1-1h1l-1 1-1 1h-1l-1 1c0-1 0-1-1-2-1 0-1 0-2 1-1-1-2-1-2-2z" class="P"></path><path d="M282 471h3l1 1 1-1v5h-1c-2 0-3 0-4 1 0 0-1 0-1 1 0 0-1-1-2-1l1-1h-2l1-1 2-1 1-3z" class="F"></path><path d="M282 471h3l1 1c-2 1-3 2-5 2h0l1-3z" class="S"></path><path d="M279 477c1 0 2 1 2 1 3 1 5 2 7 4 0 2-1 4-2 6h-1v-1-2c0 1 0 3-1 4-2 2-4 3-6 3l-1-1h-1v-1l-2-2v-4c1-1 1-3 1-4 1-1 1-1 2-1v-1c1-1 1-1 2-1z" class="M"></path><path d="M274 484c1-1 1-3 1-4 1-1 1-1 2-1-1 2-1 4-1 6v5l-2-2v-4z" class="G"></path><path d="M279 480c2 0 2 0 3 1s2 2 2 4h1c0 1 0 3-1 4-2 2-4 3-6 3l-1-1 2-6v-5z" class="B"></path><path d="M279 485v-5c0 1 0 2 1 2 0 2 1 1 1 3-1 1-1 0-2 0z" class="J"></path><path d="M240 487l1-1c1 2 2 6 3 7v-2c-1-1-2-3-2-5 1 2 2 5 4 6s3 3 4 5c1 1 2 3 4 4l9 3 1-1v-2h3 1c2 1 3 1 5 1h1-1l2 1v-1h2v2l2 7c-1 0-1 1-2 1s-2 1-4 0c-1 0-4 1-6 1-3 0-6 0-10-1-1 0-3-1-4-1l-2-2-6-9c0-1-1-3-2-4l-3-9z" class="S"></path><path d="M251 509h2l1 1h0 0c1 0 1 1 2 1h1 0 1c1 1 2 1 4 1 0 0 1 0 1-1h-1c-2-1-4-1-6-2v-1c1 1 2 1 3 2 2 0 3 0 4 1 1 0 2 1 3 0h1l-1-1c1-1 1-1 2-1h0c1 0 1 0 2 1h1v1h-1c-1 0-1 1-2 2h-1 0c-3 0-6 0-10-1-1 0-3-1-4-1l-2-2z" class="R"></path><path d="M250 497c1 1 2 3 4 4l9 3 2 2-1 1c-1 0-1 0-1 1l-6-3c-1-1-1-2-2-2-1-1-2-1-3-1-1-1-2-3-2-5z" class="X"></path><path d="M264 503v-2h3 1c2 1 3 1 5 1h1-1l2 1v-1h2v2l2 7c-1 0-1 1-2 1s-2 1-4 0c-1 0-4 1-6 1h0 1c1-1 1-2 2-2h1v-1h-1c-1-1-1-1-2-1h0c-1 0-1 0-2 1-1-1-2-1-3-2 0-1 0-1 1-1l1-1-2-2 1-1z" class="G"></path><path d="M268 501c2 1 3 1 5 1h1-1c-1 1-1 2-1 3v3h-1l-1-1c-1-2 0-4-2-6z" class="B"></path><path d="M275 502h2v2 2l-1 1-1-2v1h0v2h-3 0v-3c0-1 0-2 1-3l2 1v-1z" class="C"></path><path d="M264 503v-2h3c-1 2-1 4-1 6 1 1 2 1 3 2h-1 0c-1 0-1 0-2 1-1-1-2-1-3-2 0-1 0-1 1-1l1-1-2-2 1-1z" class="H"></path><path d="M265 506l3 3c-1 0-1 0-2 1-1-1-2-1-3-2 0-1 0-1 1-1l1-1z" class="E"></path><path d="M249 452l1 1 1-1c2 1 6 5 8 7s4 4 4 8c2 2 3 4 4 7h-2c0 1 0 1 1 2v1l-2 1v2c1 1 1 1 1 2v2c-5 0-10-2-15-4l-2-1-1 1c-2 0-5-3-7-5 0-2-2-3-2-6h-1v-3c0-3 1-6 2-8l1-1c0-1 0-1-1-2h-2 0c1-1 1-2 2-2s2 0 3 1v1h1c2 0 3 1 5 1l-1-1 1-2 1-1z" class="f"></path><path d="M244 466c1 0 2 0 2 1l1 1 1 1h2l-2 1h-1l-1 1c-1-1-1-3-2-5z" class="E"></path><path d="M252 458h1v1h1v1h0c-3 1-4 1-6 4v1c-1 1-1 2 0 4l-1-1-1-1c0-1-1-1-2-1 0-1 1-3 2-4s2-3 4-3h1l1-1z" class="F"></path><path d="M237 466c0-3 1-5 3-7s4-2 5-2h1c-2 2-5 4-5 7s1 5 2 7v3s1 1 1 2v1h0c1 1 2 2 4 2l-1 1c-2 0-5-3-7-5 0-2-2-3-2-6h-1v-3h0z" class="M"></path><path d="M240 471h0l1 1v-3 1c0 1 1 2 2 4 0 0 1 1 1 2v1h0 0c-1-1-2-1-3-2 0-2-1-3-1-4z" class="S"></path><path d="M237 466h0c1 2 1 4 3 5 0 1 1 2 1 4 1 1 2 1 3 2h0c1 1 2 2 4 2l-1 1c-2 0-5-3-7-5 0-2-2-3-2-6h-1v-3z" class="G"></path><path d="M257 476l2-1c1 0 1-1 1-2 0-2 0-4-2-5h0 1c3 1 5 3 6 6 0 1 0 1 1 2v1l-2 1v2c1 1 1 1 1 2v2c-5 0-10-2-15-4l-2-1c-2 0-3-1-4-2h0v-1c0-1-1-2-1-2v-3c1 2 2 3 3 4 3 1 7 2 10 1h1z" class="F"></path><path d="M246 475c3 1 7 2 10 1h1c0 1 0 1 1 2-1 0-2 0-3-1-1 1-5 1-6 1l-3-3zm18 5c1 1 1 1 1 2v2c-5 0-10-2-15-4h1 0 3 3c2 0 4 1 6 1l1-1z" class="G"></path><path d="M249 452l1 1 1-1c2 1 6 5 8 7s4 4 4 8c2 2 3 4 4 7h-2c-1-3-3-5-6-6h-1 0c2 1 2 3 2 5 0 1 0 2-1 2l-2 1h-1v-1c-1-1-2-1-4-2 0-2 1-2 1-3v-1c-2 1-2 2-4 1h-1l2-1h-2c-1-2-1-3 0-4v-1c2-3 3-3 6-4h0v-1h-1v-1h-1l-4-2-1-1 1-2 1-1z" class="h"></path><path d="M250 469l1-1c1-1 3-1 4-1l3 1h-1c-1 1-1 1-2 1h-2c-2 1-2 2-4 1h-1l2-1z" class="T"></path><path d="M253 469h2c1 0 1 0 2-1v1l-1 1v1c0 1 1 1 1 1 0 1-1 2-1 3-1-1-2-1-4-2 0-2 1-2 1-3v-1z" class="a"></path><path d="M254 460c2 1 3 2 5 2v1l-1 1-1-1-1 1v2l-1 1-1-3c-1 1-2 1-4 1h-1-1v-1c2-3 3-3 6-4z" class="J"></path><path d="M254 464h-3 0l-1-1h1c1-1 2-1 3-1s2 0 3 1l-1 1v2l-1 1-1-3z" class="g"></path><path d="M249 452l1 1 1-1c2 1 6 5 8 7s4 4 4 8c-1-2-2-3-4-4v-1c-2 0-3-1-5-2h0v-1h-1v-1h-1l-4-2-1-1 1-2 1-1z" class="e"></path><path d="M253 455l6 6v1c-2 0-3-1-5-2h0v-1h-1v-1h0v-1l-1-1s1 0 1-1h0z" class="G"></path><path d="M249 452l1 1 3 2h0 0c0 1-1 1-1 1l1 1v1h0-1l-4-2-1-1 1-2 1-1z" class="c"></path><path d="M249 452l1 1 3 2h-2c-1 0-2 0-3-2l1-1z" class="C"></path><path d="M272 434v1l1 2h0l1-1v5h1v2h0v3l4 7h1c2 2 3 3 3 6v1c1 1 1 1 1 2s0 1-1 1v3c0 2-1 3-1 5l-1 3-2 1-1 1h2l-1 1c-1 0-1 0-2 1v1c-1 0-1 0-2 1 0 1 0 3-1 4v4 2l-1 1c0-1-1-1-2-1v-4c-1 0-3 0-4-1 0-1-1-1-2-1v-2c0-1 0-1-1-2v-2l2-1v-1c-1-1-1-1-1-2h2c-1-3-2-5-4-7 0-4-2-6-4-8v-1c2 0 3 2 4 3l-1-2c-3-4-6-7-10-9 2 0 4 1 5 1l-1-2c0-1-1-1-1-2h0v-1c2 0 2 0 3 1l1 1h0v-1c1-2 1-3 2-4h1l2 2 1-1c0-2 0-3-1-5 0 0 0-1 1-1h2 1c1 1 1 1 2 1l1-1-1-2 1-1h0l1-1z" class="T"></path><path d="M272 465c1 0 2 0 2 1l1 3h-1c-1 0-1 0-2 1v-5z" class="N"></path><path d="M276 462h1c1 0 1 1 2 1v1 1h-1c-1 1-1 4-1 5h-1c-1-2 0-5 0-8zm3 13h0l-1 1h2l-1 1c-1 0-1 0-2 1v1c-1 0-1 0-2 1 0 1 0 3-1 4v-2c0-2 0-3 1-4s0-1 0-2c1 0 1-1 2-1h2z" class="E"></path><path d="M280 464v3h1c0-2 1-3 2-4v3c0 2-1 3-1 5l-1 3-2 1h0l-1-1s1-1 0-1c0-1 1-3 1-5 1-1 1-3 1-4h0z" class="V"></path><path d="M269 459c1-2 3-5 6-6h0 4 1c2 2 3 3 3 6v1c1 1 1 1 1 2s0 1-1 1c-1 1-2 2-2 4h-1v-3h0v-1h-1c-1 0-1-1-2-1h-1-1l-1-1h-1c-1 1-1 2-1 4v5c0 1 0 0-1 1v-2h0c-2-3-2-6-2-9v-1z" class="g"></path><path d="M275 462l1-1c1-1 2-4 3-4 1 2 1 4 1 7h0v-1h-1c-1 0-1-1-2-1h-1-1z" class="a"></path><defs><linearGradient id="n" x1="268.202" y1="464.227" x2="271.953" y2="489.578" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#c1c0c1"></stop></linearGradient></defs><path fill="url(#n)" d="M265 452h1 0v2c0 2 0 4 1 6v2h1l1-2c0 3 0 6 2 9h0v2c1-1 1 0 1-1 1-1 1-1 2-1 0 3-1 5 0 8-1 1-1 0-2 2 0 2 0 4 1 7v4 1c0-1-1-1-2-1v-4c-1 0-3 0-4-1 0-1-1-1-2-1v-2c0-1 0-1-1-2v-2l2-1v-1c-1-1-1-1-1-2h2c-1-3-2-5-4-7 0-4-2-6-4-8v-1c2 0 3 2 4 3l-1-2 1-1v-3s0-1 1-1c0-1 0-1 1-2z"></path><path d="M266 477c1 0 2-1 3-1 0 3 1 7 2 10-1 0-3 0-4-1 0-1-1-1-2-1v-2c0-1 0-1-1-2v-2l2-1z" class="h"></path><path d="M265 452h1 0v2c0 2 0 4 1 6v2h1l1-2c0 3 0 6 2 9h0-1v4h0l-1-2-6-10-1-2 1-1v-3s0-1 1-1c0-1 0-1 1-2z" class="W"></path><path d="M263 458v-3s0-1 1-1c0-1 0-1 1-2 1 3 0 6-1 8 0-1-1-1-1-2z" class="U"></path><path d="M263 458c0 1 1 1 1 2 1 3 4 3 4 6 1 2 2 3 1 5l-6-10-1-2 1-1z" class="o"></path><path d="M272 434v1l1 2h0l1-1v5h1v2h0v3l4 7h-4 0c-3 1-5 4-6 6v1l-1 2h-1v-2c-1-2-1-4-1-6v-2h0-1c-1 1-1 1-1 2-1 0-1 1-1 1v3l-1 1c-3-4-6-7-10-9 2 0 4 1 5 1l-1-2c0-1-1-1-1-2h0v-1c2 0 2 0 3 1l1 1h0v-1c1-2 1-3 2-4h1l2 2 1-1c0-2 0-3-1-5 0 0 0-1 1-1h2 1c1 1 1 1 2 1l1-1-1-2 1-1h0l1-1z" class="M"></path><path d="M259 447c1-1 2-2 2-3h1c1 1 1 1 1 3l-1 2c0 1 0 2-1 2-1 1-1 1-2 1l-2-1-1-2c0-1-1-1-1-2h0v-1c2 0 2 0 3 1l1 1h0v-1z" class="S"></path><path d="M272 434v1l1 2h0l1-1v5c-3 3-5 7-7 9-1 0-2 1-3 1l-4 1h-1c1 0 1 0 2-1 1 0 1-1 1-2 2 0 2 1 3 0l3-3c-1-3 1-2 2-4l-1-1h0c0-1-1-2-2-3h1c1 1 1 1 2 1l1-1-1-2 1-1h0l1-1z" class="G"></path><path d="M272 434v1c0 1 1 3 1 4h-1c-1 1-2 2-3 2 0-1-1-2-2-3h1c1 1 1 1 2 1l1-1-1-2 1-1h0l1-1z" class="a"></path><path d="M274 441h1v2h0l-1 1c-2 2-4 5-5 6l-2 2c0 1 0 1-1 2v-2h0-1c-1 1-1 1-1 2-1 0-1 1-1 1v3l-1 1c-3-4-6-7-10-9 2 0 4 1 5 1l2 1h1l4-1c1 0 2-1 3-1 2-2 4-6 7-9z" class="O"></path><path d="M260 452l4-1v1h0c-1 1-2 3-3 3-1-1-1-2-1-3z" class="P"></path><path d="M274 444l1-1v3l4 7h-4 0c-3 1-5 4-6 6v1l-1 2h-1v-2c-1-2-1-4-1-6 1-1 1-1 1-2l2-2c1-1 3-4 5-6z" class="M"></path><path d="M274 444v3c-1 0-1 1-2 1l2 2v1h-1c-1 0-2 0-3 1-1 2-2 5-2 7h1v1l-1 2h-1v-2c-1-2-1-4-1-6 1-1 1-1 1-2l2-2c1-1 3-4 5-6z" class="U"></path><defs><linearGradient id="o" x1="226.945" y1="471.162" x2="240.11" y2="470.707" xlink:href="#B"><stop offset="0" stop-color="#030304"></stop><stop offset="1" stop-color="#2a292a"></stop></linearGradient></defs><path fill="url(#o)" d="M229 441h1c1 1 1 1 3 2 1 0 2 1 3 1h3c1 1 1 1 2 1h1 1c1 1 2 1 3 2 1 0 2 1 3 1l3 2c4 2 7 5 10 9l1 2c-1-1-2-3-4-3v1c-2-2-6-6-8-7l-1 1-1-1-1 1-1 2 1 1c-2 0-3-1-5-1h-1v-1c-1-1-2-1-3-1s-1 1-2 2h0 2c1 1 1 1 1 2l-1 1c-2 0-3 1-4 3-1 1-1 2-2 4h0v4h0l-1 1 4 6h0c0 3 2 8 3 11h1l3 9c1 1 2 3 2 4l6 9 2 2v1c-1 1-2 1-3 1h1-1c-1 0-4-2-5-3h-2c-1-1-1-2-3-3l-2-2c0 1-1 1-2 2v3 3l-1-1c0-2-1-3-2-4-1 0-1 0-2 1v3c0 1 0 0-1 1v1c0 1 0 2-1 2v2h0v-4c-1 0-1 1-1 1v1 4h-1v-6h0c-1-4 0-9 0-12l2-49c1-1 0-10 0-12z"></path><path d="M236 448h6c-1 1-1 2-3 2h0-1-1c1 0 2-1 3-1v-1c-2 0-3 1-4 0z" class="B"></path><path d="M238 450h1c-3 2-5 7-6 10v5 4h0l-1 1c-1 0-1-1-1-2 0-3 0-6 1-9 1-4 3-7 6-9z" class="C"></path><path d="M242 448l9 4-1 1-1-1-1 1-1 2 1 1c-2 0-3-1-5-1h-1v-1c-1-1-2-1-3-1s-1 1-2 2h0 2c1 1 1 1 1 2l-1 1c-2 0-3 1-4 3-1 1-1 2-2 4h0v-5c1-3 3-8 6-10h0c2 0 2-1 3-2z" class="L"></path><path d="M242 454l2-4c1 0 3 1 4 2h1l-1 1-1 2 1 1c-2 0-3-1-5-1h-1v-1z" class="R"></path><path d="M248 452h1l-1 1-1 2-2-2 3-1z" class="Z"></path><defs><linearGradient id="p" x1="245.235" y1="455.643" x2="246.175" y2="445.654" xlink:href="#B"><stop offset="0" stop-color="#969597"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#p)" d="M236 448h0c-2 0-3 1-5 3 0-2 1-4 1-5 3-2 4-2 7-2 1 1 1 1 2 1h1 1c1 1 2 1 3 2 1 0 2 1 3 1l3 2c4 2 7 5 10 9l1 2c-1-1-2-3-4-3v1c-2-2-6-6-8-7l-9-4h-6z"></path><defs><linearGradient id="q" x1="241.426" y1="481.024" x2="233.031" y2="489.637" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#b5b5b5"></stop></linearGradient></defs><path fill="url(#q)" d="M231 468c0 1 0 2 1 2l4 6h0c0 3 2 8 3 11h1l3 9v2 1h0l-1-1c0 1 0 1 1 2v2l-1 1-2-2-2-4c-1-2-2-4-3-7-3-7-4-14-4-22z"></path><path d="M238 497h0c0-3-2-6-2-9l1-1c2 2 1 5 2 7 1 1 3 4 3 6h0v1h0 0-2l-2-4z" class="c"></path><path d="M232 496h-2 0c-1-2 0-6 0-9l1-1 1 1v2c-1 1-1 1-1 2h1 2l1-1c1 3 2 5 3 7l2 4 2 2 1-1v-2c-1-1-1-1-1-2l1 1h0v-1-2c1 1 2 3 2 4l6 9 2 2v1c-1 1-2 1-3 1h1-1c-1 0-4-2-5-3h-2c-1-1-1-2-3-3l-2-2c0 1-1 1-2 2v3 3l-1-1c0-2-1-3-2-4-1 0-1 0-2 1v3c0 1 0 0-1 1v1c0 1 0 2-1 2v2h0v-4c-1 0-1 1-1 1v1 4h-1v-6h0c-1-4 0-9 0-12h1c1 0 1 0 1 1h1s0-1-1-2c1-1 2-3 3-5z" class="L"></path><path d="M248 505c1 1 2 3 2 4l-1 1c-1 0-1-1-2-1-1-1-2-2-2-3s2 0 3-1z" class="C"></path><path d="M243 496c1 1 2 3 2 4 0 2 2 4 3 5-1 1-3 0-3 1l-3-3 1-1v-2c-1-1-1-1-1-2l1 1h0v-1-2z" class="G"></path><path d="M232 496c2 1 3 1 3 3 1 1 1 2 1 3-1 2-3 3-6 4-1-1-2-2-2-3v-1c1 0 1 0 1 1h1s0-1-1-2c1-1 2-3 3-5z" class="d"></path><defs><linearGradient id="r" x1="259.615" y1="531.245" x2="279.446" y2="523.34" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#r)" d="M275 502h0l1-1c4 0 5-2 8-4l1 1-1 1 1 1h0 1 3c3 0 4-1 7 0l1-1 1 3v-1 2 4h1c0 1 1 2 1 3h0c0 1 1 2 0 3 1 1 1 1 2 1 1 1 2 1 2 2s0 1 1 1c0 1 1 2 1 3h0 1c1 0 1 0 2-1h2l-1 1v1h0c-3 5-7 7-12 10h1l-1 1c-2 1-3 3-5 4-1 1-2 1-3 2l-1 1-1 2-2 3c-2 2-4 5-5 8-1 2-1 3-2 5v1c-1 1-2 1-2 2-1 1-1 2-1 2v2c-1-1-1-2-1-3h0c0-2 0-3-1-4h-1 0-1v3c0 1-1 2 0 3l-2-5h-1c0-1-1-2-2-2l-3-3c1 0 1-1 1-1l-2-3h0 0c1-1 0-3 0-4s-1-1-2-2l-1-2h0l-5-3c-1-1-3-2-5-3h-4l-1-1 1-1c-1-2-5-4-6-5s-5-2-7-3c-1 0-1-1-2-1-1-1-2-4-2-6h0v-2c1 0 1-1 1-2v-1c1-1 1 0 1-1v-3c1-1 1-1 2-1 1 1 2 2 2 4l1 1v-3-3c1-1 2-1 2-2l2 2c2 1 2 2 3 3h2c1 1 4 3 5 3h1-1c1 0 2 0 3-1v-1c1 0 3 1 4 1 4 1 7 1 10 1 2 0 5-1 6-1 2 1 3 0 4 0s1-1 2-1l-2-7v-2h-2z"></path><path d="M273 548l1 3v6h-1 0l-1-7h1v-2z" class="G"></path><path d="M268 535h0 1c1 2 2 3 2 5h0l-1 2c-1-2-2-4-3-5 0-1 0-2 1-2z" class="T"></path><path d="M264 530l1-1c1-1 2 0 4 0l-4 1h0c0 1 1 1 1 1 1 2 1 3 2 4-1 0-1 1-1 2l-1-1c-2-2-2-4-2-6z" class="U"></path><path d="M270 542l1-2v3c1 0 1 1 2 1v4 2h-1l-2-8zm-1-13c1 1 2 2 2 3v1h0l-1 1h-1v1h-1 0c-1-1-1-2-2-4 0 0-1 0-1-1h0l4-1z" class="F"></path><path d="M266 531c2 1 2 1 4 1v1l-1 1v-1l-1 2h0c-1-1-1-2-2-4z" class="E"></path><path d="M251 513c3 1 5 3 7 4l-2 1c-1 0-1-1-2-1l-1 1v1h-1l-2-2-1 1c0-2 0-3 1-5h1z" class="T"></path><path d="M266 522l-2-2h0 2 2 0l-1-1 1-1c1 1 2 1 2 2h1c1 1 1 2 1 4h0l-2 2c-1-1-2-2-2-3l-2-1z" class="O"></path><path d="M266 522c1-1 2 0 4 1 0 0 1 1 2 1h0l-2 2c-1-1-2-2-2-3l-2-1zm-6 19h0c1 0 2 0 3 1v-1h0 2c2 3 4 6 5 10h-1c-1-1-1-3-3-5h-1c-1 0-1 0-2-1 0-1-1-1-2-2l-1-2h0z" class="E"></path><path d="M260 541h0c1 0 2 0 3 1v-1c1 1 1 2 1 3h0-1 0c-1-2-2-2-3-3h0z" class="F"></path><path d="M268 523c0 1 1 2 2 3h1l1 6h-1c0-1-1-2-2-3-2 0-3-1-4 0l-1 1v-2l2-2c-1-1-2-1-2-2l1-1 2 1 1-1z" class="K"></path><path d="M271 520v-3c1-1 2-2 4-2v3c0 1-1 1-1 2 1 0 1 1 1 1v1c-1 2 0 4 0 5s-2 2-2 2c1 1 1 0 2 0l-2 4c-1 0-1-1-1-1l-1-6h-1l2-2h0c0-2 0-3-1-4z" class="F"></path><path d="M272 524l1-1h1v3l-2 1-1-1h-1l2-2z" class="M"></path><defs><linearGradient id="s" x1="265.536" y1="547.06" x2="270.58" y2="561.077" xlink:href="#B"><stop offset="0" stop-color="#878787"></stop><stop offset="1" stop-color="#a0a0a1"></stop></linearGradient></defs><path fill="url(#s)" d="M263 545c1 1 1 1 2 1h1c2 2 2 4 3 5h1c1 1 2 4 2 6v3c0 1-1 2 0 3l-2-5h-1c0-1-1-2-2-2l-3-3c1 0 1-1 1-1l-2-3h0 0c1-1 0-3 0-4z"></path><path d="M263 545c1 1 1 1 2 1h1l-1 1c0 1 1 3 1 4h-1v1l-2-3h0 0c1-1 0-3 0-4zm9-13s0 1 1 1l2-4c-1 0-1 1-2 0 0 0 2-1 2-2s-1-3 0-5h0c0 1 1 1 1 1h1 2 0c-1 2-3 3-2 6h0v1c-1 1 0 3 0 5 0 1-1 1-1 2l1 1h0c-2 3-2 9-2 13h-1l-1-3v-4c-1 0-1-1-2-1v-3h0c0-2-1-3-2-5v-1h1l1-1h0v-1h1z" class="N"></path><path d="M269 535v-1h1l1-1h0c2 3 2 8 2 11-1 0-1-1-2-1v-3h0c0-2-1-3-2-5zm-33-25v-3c1-1 2-1 2-2l2 2c2 1 2 2 3 3h2c1 1 4 3 5 3-1 2-1 3-1 5l1-1 2 2h1v-1l1-1c1 0 1 1 2 1l2-1c2 1 3 3 4 5v3s0 1-1 2c0 1 1 2 0 3 0 2 1 4 1 6 1 2 2 4 3 5h-2 0v1c-1-1-2-1-3-1h0l-5-3c-1-1-3-2-5-3h-4l-1-1 1-1c-1-2-5-4-6-5s-5-2-7-3c-1 0-1-1-2-1-1-1-2-4-2-6h0v-2c1 0 1-1 1-2v-1c1-1 1 0 1-1v-3c1-1 1-1 2-1 1 1 2 2 2 4l1 1v-3z" class="M"></path><path d="M229 518h0v-2c1 0 1-1 1-2v-1c1-1 1 0 1-1v-3c1-1 1-1 2-1 1 1 2 2 2 4l1 1v1l1 1v1h-2v1 1h0c-1-1-2-1-2-2l-1-1h0c0 1-1 2-1 3 0 2 4 4 4 4v1c-1 0-2 1-2 2-1 0-1-1-2-1-1-1-2-4-2-6z" class="C"></path><path d="M236 514h0-3v-1c1-1 1-1 2-1l1 1v1z" class="M"></path><path d="M237 515c1 0 2 0 2 1l4 5c2 4 6 8 10 11 0 1 1 1 2 2l5 3h1l-1-1v-1-2-2l1-1c0 2 1 4 1 6 1 2 2 4 3 5h-2c-1-2-4-4-6-5-4-2-7-5-10-7-1 0-1 0-2-1-1 0-3-2-4-4l-6-6v-1-1h2v-1z" class="d"></path><path d="M235 517v-1h2l2 2 1 1c0 1 0 1 1 2l2 3c2 1 3 2 4 5-1 0-1 0-2-1-1 0-3-2-4-4l-6-6v-1z" class="N"></path><path d="M235 517v-1h2l2 2 1 1c0 1 0 1 1 2l2 3c-2-1-6-5-8-7z" class="X"></path><path d="M236 510v-3c1-1 2-1 2-2l2 2c2 1 2 2 3 3h2c1 1 4 3 5 3-1 2-1 3-1 5 0 0-1 0-1 1l-2-2-1 1h-1c1 3 3 4 4 6 1 1 1 2 3 3 1 1 2 3 3 4-1 0-1 1-1 1-4-3-8-7-10-11l-4-5c0-1-1-1-2-1l-1-1v-1-3z" class="F"></path><path d="M236 510c1 0 1 0 2 1 0 0 1 1 2 1h0l-1 4c0-1-1-1-2-1l-1-1v-1-3z" class="N"></path><path d="M241 511c-1 0-1 0-1-1s0-1 1-1 1 0 2 1h0 2c1 1 4 3 5 3-1 2-1 3-1 5 0 0-1 0-1 1l-2-2-1 1h-1c-1-1-1-2-2-3-1-2-1-2-1-4z" class="D"></path><path d="M241 511c3 2 4 3 5 6l-1 1h-1c-1-1-1-2-2-3-1-2-1-2-1-4z" class="E"></path><path d="M258 517c2 1 3 3 4 5v3s0 1-1 2c0 1 1 2 0 3l-1 1v2 2 1l1 1h-1l-5-3c-1-1-2-1-2-2 0 0 0-1 1-1-1-1-2-3-3-4-2-1-2-2-3-3-1-2-3-3-4-6h1l1-1 2 2c0-1 1-1 1-1l1-1 2 2h1v-1l1-1c1 0 1 1 2 1l2-1z" class="G"></path><path d="M256 527c2 1 3 2 3 4h0c-1 0-2 0-3-1h0c1-1 1-2 0-3zm-1-5l2 1h0c2 2 3 3 3 5h-1l-5-5 1-1z" class="C"></path><path d="M252 519h1v-1l1-1c1 0 1 1 2 1 0 1 0 3 1 4v1l-2-1-3-3z" class="E"></path><path d="M249 518l1-1 2 2 3 3-1 1-1-1h-1l4 5c1 1 1 2 0 3h0c0-1-1-1-1-1-2-3-5-5-7-7-1-1-2-1-2-2-1 0-1-1-1-2l1-1 2 2c0-1 1-1 1-1z" class="R"></path><path d="M275 502h0l1-1c4 0 5-2 8-4l1 1-1 1 1 1h0 1 3c3 0 4-1 7 0l1-1 1 3v-1 2 4h1c0 1 1 2 1 3h0c0 1 1 2 0 3 1 1 1 1 2 1 1 1 2 1 2 2s0 1 1 1c0 1 1 2 1 3h0 1c1 0 1 0 2-1h2l-1 1v1h0c-3 5-7 7-12 10h1l-1 1c-2 1-3 3-5 4-1 1-2 1-3 2l-1 1-1 2-2 3c-2 2-4 5-5 8-1 2-1 3-2 5v1c-1 1-2 1-2 2-1 1-1 2-1 2v2c-1-1-1-2-1-3h0c0-2 0-3-1-4v-6h1c0-4 0-10 2-13h0l-1-1c0-1 1-1 1-2 0-2-1-4 0-5v-1h0c-1-3 1-4 2-6h0-2-1s-1 0-1-1h0v-1s0-1-1-1c0-1 1-1 1-2v-3-1l1 1h1v1c1 0 1 0 2-1-1 0-1 0-1-1-1-1-1-1-1-2 1 0 1-1 2-1l-2-7v-2h-2z" class="B"></path><path d="M282 525s1 0 1-1c1 0 1 0 1-1l2 2-2 2-1 2-2-2 1-2z" class="J"></path><path d="M283 514h2v4h-2c0 1 1 1 1 2h1v1c-2 0-3-2-5-2l-1 1v-1c1-2 3-3 4-4v-1z" class="I"></path><path d="M275 515v-1l1 1v3c1 0 1 0 2-1v1c-1 1-2 1-2 2v1c0 1 0 1 1 1 2 0 3 0 4 1v1c0 1 0 1 1 1l-1 2 2 2 1-2c1 0 1 1 2 2l-1 1c-1 1-3 2-3 3-2 1-4 3-5 5h0l-1-1c0-1 1-1 1-2 0-2-1-4 0-5v-1h0c-1-3 1-4 2-6h0-2-1s-1 0-1-1h0v-1s0-1-1-1c0-1 1-1 1-2v-3z" class="d"></path><path d="M281 524c0 1 0 1 1 1l-1 2 2 2c-2 1-4 3-5 5-1-4 0-7 3-10z" class="D"></path><path d="M284 527c1 0 1 1 2 2l-1 1c-1 1-3 2-3 3-2 1-4 3-5 5h0c0-2 1-3 1-4 1-2 3-4 5-5l1-2z" class="E"></path><path d="M284 512c-2 0-3 0-4-1v-2c0-2 2-8 4-10l1 1h0 1 3c3 0 4-1 7 0l1-1 1 3h-1c-3 1-5 2-7 5 0 1-1 2-1 3v1h1v1l-5 2h-2s0-1 1-2z" class="G"></path><path d="M285 505c0-1 1-1 2-1 0 2-2 4-3 6h0-1c0-2 1-3 2-5z" class="d"></path><path d="M293 502c2 0 3-1 4 0-3 1-5 2-7 5 0 1-1 2-1 3v1h1v1l-5 2h-2s0-1 1-2v-1-1h0 1c1 1 1 1 2 1 1-2 1-3 2-5s2-3 4-4z" class="H"></path><path d="M284 512c-2 0-3 0-4-1v-2c0-2 2-8 4-10l1 1h0 1 3c3 0 4-1 7 0l1-1 1 3h-1c-1-1-2 0-4 0-1-1-3 0-4-1-1 1-2 1-2 2v1c-1 0-2 0-2 1-1 2-2 3-2 5h1v1 1z" class="C"></path><path d="M297 499l1 3h-1c-1-1-2 0-4 0-1-1-3 0-4-1-1 1-2 1-2 2v1c-1 0-2 0-2 1v-2h-1c0 1 0 1-1 1l-1 2v-1c1-1 2-3 2-4l1-1h0 1 3c3 0 4-1 7 0l1-1z" class="T"></path><path d="M298 502v-1 2 4h1c0 1 1 2 1 3h0c0 1 1 2 0 3 1 1 1 1 2 1 1 1 2 1 2 2s0 1 1 1c0 1 1 2 1 3-2 3-7 4-10 6h-2c-1 0-2-1-3 0h0c-2 1-3 2-4 2l-1 1c-1-1-1-2-2-2l2-2 1-2 1-1c-1-1-2-1-3-1v-1h-1c0-1-1-1-1-2h2v-4l5-2v-1h-1v-1c0-1 1-2 1-3 2-3 4-4 7-5h1z" class="Z"></path><path d="M290 507c1 0 2 0 2 1 1 0 1-1 2-1 0 0 0-1 1-1v1l-1 1h0c-1 1-4 1-4 2l1 1h0v2l-1 2h1v1c1-1 2-2 2-3v-1h1v1h0c-1 2 1 3-1 4-1 0-2 0-4-1l-1 2s-1 0-2 1l-1-1v-4l5-2v-1h-1v-1c0-1 1-2 1-3z" class="c"></path><path d="M294 520h2c1-2 0-4 0-6 1-1 1-1 1-2v-1h1v3c-1 1 0 3 0 4h1v-1c1 0 2 0 3 1h2v-2c0 1 0 1 1 1 0 1 1 2 1 3-2 3-7 4-10 6v-1-1c0-2 0-2-1-4h-1z" class="M"></path><path d="M285 521v-1h-1c0-1-1-1-1-2h2l1 1c1-1 2-1 2-1 1 1 1 1 1 2 2 0 3-1 5 0h1c1 2 1 2 1 4v1 1h-2c-1 0-2-1-3 0h0c-2 1-3 2-4 2l-1 1c-1-1-1-2-2-2l2-2 1-2 1-1c-1-1-2-1-3-1z" class="D"></path><path d="M287 523h2v1c0 2-1 2-2 3v1l-1 1c-1-1-1-2-2-2l2-2 1-2z" class="T"></path><path d="M286 519c1-1 2-1 2-1 1 1 1 1 1 2 2 0 3-1 5 0h1c1 2 1 2 1 4v1 1h-2l-3-4s-1 0-2-1h-1c-1 0-1-1-2-2z" class="E"></path><path d="M296 525c-1 0-1 0-2-1 0-1 0-2 1-4 1 2 1 2 1 4v1z" class="U"></path><path d="M309 519h2l-1 1v1h0c-3 5-7 7-12 10h1l-1 1c-2 1-3 3-5 4-1 1-2 1-3 2l-1 1-1 2-2 3c-2 2-4 5-5 8-1 2-1 3-2 5v1c-1 1-2 1-2 2-1 1-1 2-1 2v2c-1-1-1-2-1-3h0c0-2 0-3-1-4v-6h1c0-4 0-10 2-13 1-2 3-4 5-5 0-1 2-2 3-3l1-1 1-1c1 0 2-1 4-2h0c1-1 2 0 3 0h2c3-2 8-3 10-6h0 1c1 0 1 0 2-1z" class="g"></path><path d="M274 551h1v4h0c0-1 0-1 1-1h1l-2 7c0-2 0-3-1-4v-6z" class="W"></path><path d="M282 533c0-1 2-2 3-3v1c2 0 5-1 7-1l-5 4c-1-1 0-1 0-2l-2 2-1-2h0l-2 2v-1z" class="N"></path><path d="M287 538l2 1-1 2-2 3c-2 0-3 2-4 4l-1-1 6-9z" class="E"></path><path d="M281 547l1 1c1-2 2-4 4-4-2 2-4 5-5 8-1 2-1 3-2 5v1c-1 1-2 1-2 2 0-5 2-9 4-13z" class="d"></path><path d="M285 534l2-2c0 1-1 1 0 2-2 3-4 5-6 8l-1 3c-2-1-1-2-1-3h-1v-3c1-2 2-3 4-5l2-2h0l1 2z" class="T"></path><path d="M282 534l2-2h0l1 2c-1 0-3 1-4 3h0c0 2-1 2-2 3v1c1 1 1 0 2 1l-1 3c-2-1-1-2-1-3h-1v-3c1-2 2-3 4-5z" class="E"></path><path d="M277 538c1-2 3-4 5-5v1c-2 2-3 3-4 5v3h1c0 1-1 2 1 3l-3 9h-1c-1 0-1 0-1 1h0v-4c0-4 0-10 2-13z" class="G"></path><path d="M277 538c1-2 3-4 5-5v1c-2 2-3 3-4 5-2 4-2 10-2 15-1 0-1 0-1 1h0v-4c0-4 0-10 2-13z" class="l"></path><path d="M287 538c2-2 5-3 7-6 1-1 1-2 2-3 2-1 5-2 7-4 2-1 4-2 6-4h1c-3 5-7 7-12 10h1l-1 1c-2 1-3 3-5 4-1 1-2 1-3 2l-1 1-2-1z" class="N"></path><path d="M235 112v-1l1-1-1-1v-1l4 1v1 3c0 3 1 4 3 6 0 1 0 2 1 2v2c1 2 3 6 5 8 0 1 2 2 2 3h0c0 1 1 2 1 3 1 1 1 2 2 3h2v4 2h2v3l1 1 2 2c1 2 2 3 4 5l1 1 1 2c1 0 1-1 2-2l2 1c0-2 1-3 1-4 0 0 0 1 1 1h1c1 0 1 0 2-1 1 0 0-1 1-2h0v-2h1 0l2 2h0c1 0 2-1 3-1 0 1 1 1 1 1v3 1c1-2 4-5 5-7l4-4h1l2-2c1-2 3-3 4-5v-3c1 0 2-2 3-2v1h1 0 0l1-1c1 0 1 0 2 1l-8 9h2l2-2c1 1 4 0 5 0s3 0 4-1c2 1 3 1 4 2h1l2-1c1 2 0 3 1 5 0 2 1 4 0 5 0 2 1 4 0 6h0v7h0c-1 0-1 0-2 1h-1c3 3 3 6 3 10-1 2-1 3-2 4l-1 1v2c-1 0-2 0-3 1-1 0-1 0-1 1s0 3-1 5h1c3 1 4 1 6 3v1 1 4l-3 2c-2 2-4 4-5 8v2h0v3h-1l-1-2c0 2 1 6 0 7 0 2 0 3 1 4 1 0 1 0 2 1v6c1 1 0 3 1 5-1 5-3 9-6 13-1 1-3 2-3 4l-2-3-1-1-2-3h-2-1v1c-1-2-2-2-3-2l-2-2-4-4c-2 0-4 1-5 1-1 1-2 1-3 1-1 2-2 4-2 6 0 0 0 1-1 1v1h-1v-4 1l-2 2v3c0 1 0 1-1 2h0v-1h0c0-2-1-2-1-3l-1-1v-2l-1-3h-1 0-1c-2 0-5 2-7 3-5 2-9 3-13 1l-4-2-1-2-2-2c-1-3-1-5-1-9 0 1 0 2-1 2h0c-1-1-1-3-1-4l1-1c-1-1-1-2-1-3l-1-1c0-5 0-9 2-14v-4c-1-1-1-3-1-4l-1-4-1 1c-1 1-1 2-2 2h-2c-1-2 0-5 0-6 0-2 1-3 2-4s2 0 3 0l2 1h0v-3c-1 0-1 0-2-1v-1c0-1 1-2 1-2v-1h0c-1-1-2-1-2-1-2-2-3-3-3-5v-1c0-2 1-3 1-5 3-5 8-6 14-8h4c1 0 1-1 2-1-1-1-2-1-2-2 2 0 4-1 7-1 0-2-3-6-5-8 0 1-1 2-1 3h0v1h0c0 1-1 1-2 1h0l-3 3v1c-2 1-3 2-4 2-3 0-6-1-8-3-4-3-3-7-4-11h0v-9l-1-15h2c0 1 0 1 1 1 0 0 1 0 1 1 1 2 1 4 1 6l3-2v-2l1 1h1c0-2-2-4-3-6a30.44 30.44 0 0 0-8-8c1 0 2 1 3 1 0 1 1 1 1 1l2 2v-2l-2-2-1-1 1-1c2 1 3 3 5 4h0c0 1 0 1 1 1l-1-3c0-1-1-3-2-3v-1z" class="c"></path><path d="M258 205l2 2c-1 1-1 1-2 1v1-4z" class="l"></path><path d="M252 205l1 1c0 1 0 1-1 2h-1v-1c0-1 0-1 1-2z" class="Z"></path><path d="M251 168c-2-1-3-1-5-1v-1h3c1 1 2 1 3 1l-1 1z" class="R"></path><path d="M247 169c1 0 2 1 2 2l-4 3v-4c1 0 1-1 2-1z" class="K"></path><path d="M239 179c1 0 2 1 2 2-1 1-1 1-2 1-2 0-3 0-4-1v-1h1c1 0 1-1 2-1h1z" class="P"></path><path d="M235 181l-1-1c-1-1-1-3 0-4v1l2-1 1 1v1h1l1 1h-1c-1 0-1 1-2 1h-1v1z" class="V"></path><path d="M302 179c0-2 1-4-1-6-1-1-3-2-5-2h0c1-1 3-2 5-2 0 1 0 2 1 3h0c1 1 1 2 0 3 1 2 1 3 0 4z" class="P"></path><path d="M238 170c3-1 6-2 9-1-1 0-1 1-2 1h-1l-3 3-1-1c-1 0-1-1-2-2z" class="J"></path><path d="M253 193c1 0 1-1 2-1h1v1l-2 8h-1c-1 1-2 1-3 2h0l2-3c0-2 1-3 2-4 0-2 0-2-1-3z" class="X"></path><path d="M278 185v3c0-1 0-1-1-1-1 2-1 3-1 5 0 3 1 8-1 10-1-1-1 0-2-1v-1c0-1 1-2 1-2 1-3 1-6 2-9v-2c1-1 1-2 2-2z" class="C"></path><path d="M245 200c1-1 2-3 3-4 0 0-1-2-1-3h1 2l1 1-1 1 1 2c-1 0-2 1-2 2-1 1-2 3-4 3v-2z" class="R"></path><path d="M237 172l1-2c1 1 1 2 2 2l1 1-1 1c0 1-1 2-2 3h0v1h-1v-1l-1-1-2 1v-1c0-2 1-3 3-4z" class="O"></path><path d="M237 172l1-2c1 1 1 2 2 2l-2 2v-1l-1-1z" class="D"></path><path d="M237 172l1 1-2 1v2h0l-2 1v-1c0-2 1-3 3-4z" class="K"></path><path d="M274 207c0 1 1 3 2 4 1 2 2 3 5 3h1v1c-2 0-4 0-6-1l-2-1-1-1v1c-1 1-2 2-3 2s-3 1-3 0h0l1-1c3-1 4-3 6-7z" class="L"></path><path d="M246 179h0c-1-1 0-3 0-4 1-1 2-2 3-2 3-1 5 1 7 2-1 1-1 2-2 3h0v-2h0-1c-1 1-1 2-1 4l-2-2h-1v1h-1-2z" class="J"></path><path d="M248 179l1-2c1-1 2-1 4-1-1 1-1 2-1 4l-2-2h-1v1h-1z" class="Q"></path><path d="M301 169c4 0 7-1 10 2 1 1 2 2 3 4l-1 1h-1c0-1-1-2-2-2h-1-1-1v-2h0l-2 1c-1-1-1-1-3-1h0c-1-1-1-2-1-3z" class="L"></path><path d="M307 172l1-1 2 2-1 1h-1-1v-2z" class="f"></path><path d="M256 175c1 2 3 3 3 5v1 1c-1 1-1 2-3 2-1 0-1 0-2-1s-2-2-2-3c0-2 0-3 1-4h1 0v2h0c1-1 1-2 2-3z" class="b"></path><path d="M254 183l2-5c1 1 2 3 3 4-1 1-1 2-3 2-1 0-1 0-2-1z" class="E"></path><path d="M301 164h3c3 0 9 1 12 4 2 2 2 5 2 8-1 1-1 2-2 3s-2 1-3 1l3-3c1-1 1-1 1-2-1-1-1-3-2-4-1-2-3-3-5-4-5-3-11-2-16 0 2-2 5-3 7-3z" class="K"></path><path d="M302 172c2 0 2 0 3 1l2-1h0v2h1 1v3c0 1 3 2 2 4l-1 1c2 0 4 0 6-1v2c-1 0-2 0-3 1-1 0-1 0-1 1v-1h-4-1l-2-2-1-1c0-1 0-1 1-1v-1-2h1l-1-1h-1-1 1l-2-1c1-1 1-2 0-3z" class="B"></path><path d="M302 172c2 0 2 0 3 1l2-1c0 1-1 2-2 3h-1v1l-2-1c1-1 1-2 0-3z" class="H"></path><path d="M309 174v3c0 1 3 2 2 4l-1 1s-1 0-2-1-1-1-1-2c0-2 0-3 1-5h1z" class="R"></path><path d="M250 161c2 0 4-1 7-1l3 6c0 1 0 3 1 4h-1c-1 1-1 1-2 1h0 0c-3 0-4-1-6-2l-1-1 1-1c2 0 2 1 4 0l-6-3c1 0 1-1 2-1-1-1-2-1-2-2z" class="S"></path><path d="M252 163c2 1 5 3 7 6-1-1-2-1-3-2l-6-3c1 0 1-1 2-1z" class="K"></path><path d="M249 179v-1h1l2 2c0 1 1 2 2 3s1 1 2 1c2 0 2-1 3-2v-1l5 11c-5-3-10-4-14-7-2-2-4-3-4-6h2 1z" class="I"></path><path d="M246 179h2 1l-1 2c1 0 1 1 1 1 1 0 2 0 2 1s0 1-1 2c-2-2-4-3-4-6z" class="P"></path><path d="M287 193h1c1 0 2-1 4-2 0 3 0 4-1 5-1 0-2 0-3 1h2l1 2c-1 1-1 1-2 1v1c0 1 1 3 0 4v3l-2 2c-2-2-4-6-4-9s2-6 4-8z" class="i"></path><path d="M289 201h0c0 1 1 3 0 4-1 0-2 0-2-1-1 0-1-1-1-2 1 0 2 0 2-1h1z" class="C"></path><path d="M288 197h2l1 2c-1 1-1 1-2 1v1h0c-1-1-1-1-2-1h0c0-2 0-2 1-3z" class="M"></path><path d="M302 175l2 1h-1 1 1l1 1h-1v2 1c-1 0-1 0-1 1l1 1c-1 1-2 1-2 3-1 0-1 0-1 1h0v1h0v1c-1 0-2 1-2 2l-1 1 3 3v2h-3c0-1-5-4-6-5v5l-3 1h-2c1-1 2-1 3-1 1-1 1-2 1-5-2 1-3 2-4 2h-1c2-2 5-2 7-4 5-2 7-5 8-10 1-1 1-2 0-4z" class="D"></path><path d="M235 112v-1l1-1-1-1v-1l4 1v1 3c0 3 1 4 3 6 0 1 0 2 1 2v2c1 2 3 6 5 8 0 1 2 2 2 3h0c0 1 1 2 1 3 1 1 1 2 2 3h2v4 2c-5-5-8-11-12-17l-5-10-1-3c0-1-1-3-2-3v-1z" class="g"></path><path d="M235 112v-1l1-1-1-1v-1l4 1v1 3c0 1-1 2 0 2h-1v-3h-1c0 2 1 3 0 4 0-1-1-3-2-3v-1z" class="L"></path><path d="M291 214l1 1v1c-1 3-2 5-4 8h2c-3 2-7 3-10 4l2 1-5 5-2-1-4-1-2 1h0c-1-2-2-3-4-4l1-1-9-5 1-1c-1-2-1-4-1-6l3 6c3 3 6 4 9 5 2 0 5 1 7 1 1 0 3-2 4-2s1-1 3-1h2v-1h1c2-1 3-3 4-4v-1c1-2 1-3 1-5z" class="C"></path><path d="M266 228c3 2 9 2 12 1l2-1 2 1-5 5-2-1-4-1-2 1h0c-1-2-2-3-4-4l1-1z" class="I"></path><path d="M241 173l3-3h1v4c-1 2-2 3-1 6 0 2 2 4 4 6l2 2 7 3-1 1h-1c-1 0-1 1-2 1 0 0-1 1-2 1l-1-1h-2-1c0 1 1 3 1 3-1 1-2 3-3 4l-1 2c-1-1-1-4-1-4 1-2 3-2 3-4-1-2-2-2-3-2v-1h2l1-1-1-1h0 0l1-1v-1c-1-1-2-3-3-3-2 0-5 0-7 1v-1c1-1 2-1 3-1s1-1 2-2c0-1-1-2-2-2l-1-1v-1h0c1-1 2-2 2-3l1-1z" class="H"></path><path d="M248 186l2 2c0 1 0 1 1 1 0 1 0 1 1 1v3h-1l-1-1-1-1h-2c-1-1-1-3-1-4l2-1z" class="L"></path><path d="M237 228h0c1-4 3-7 7-8h4 1c1 0 3-1 4-2v-1c1 2 2 4 4 6h0l9 5-1 1h0 0-1-1-2-5c-1 0-1 0-1-1l-4-1-4-1v-1c1-1 1-2 2-2-3 0-6 1-8 3s-2 3-3 5c0 1 0 2-1 2h0c-1-1-1-3-1-4l1-1z" class="B"></path><path d="M249 223c2 0 4 0 7 1h0s-1 0-1 1h-3-1v1 1l-4-1v-1c1-1 1-2 2-2z" class="X"></path><path d="M256 224c3 1 7 3 9 5h0-1-1-2-5c-1 0-1 0-1-1l-4-1v-1-1h1 3c0-1 1-1 1-1z" class="S"></path><path d="M251 227v-1-1h1l3 1c1 0 0 1 0 2h0l-4-1z" class="R"></path><path d="M257 191l1 1c3 1 5 2 6 4 1 3 1 5-1 8 0 1-1 3-3 5v4c-1 0-1-1-2-1-2 0-2 1-4 2v1h-1c-2 0-3 0-4-1 0-1 2-1 2-2-1-2-2-2-4-3-1 0-2 1-3 1h0c2-1 3-2 5-1 1 0 1 0 2 1l4 1c1-1 2-2 3-2v-1c1 0 1 0 2-1l-2-2c-1-2-1-2-2-3 0 0-1-1-2-1l2-8v-1l1-1z" class="H"></path><defs><linearGradient id="t" x1="259.918" y1="199.137" x2="259.089" y2="205.248" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#t)" d="M262 198l1 1-3 8-2-2c-1-2-1-2-2-3h1l3-3 2-1z"></path><path d="M257 191l1 1c0 1 1 3 1 4 1 1 2 1 3 2l-2 1-3 3h-1s-1-1-2-1l2-8v-1l1-1z" class="h"></path><path d="M257 191l1 1c0 1 1 3 1 4 1 1 2 1 3 2l-2 1c-1-1-2-1-3-2 0-1 0-1-1-1v-3-1l1-1z" class="L"></path><path d="M294 186c-6 3-9 6-13 11 0-5 3-12 6-17 2-2 6-6 9-6 1 0 2 0 3 1s1 2 1 3c0 2-1 3-2 5-1 1-3 2-4 3z" class="h"></path><path d="M292 185l-1 1c0-1 0-2-1-2h-2c0-2 0-2 1-3h1c0-1 1-2 1-2h3c1 0 1-1 2 0v3c1 0 1 1 2 1-1 1-3 2-4 3v-1h-2z" class="K"></path><path d="M292 185c-1-1-1-2-1-3 1 0 2-1 3-1v1 3h-2z" class="L"></path><path d="M296 182c1 0 1 1 2 1-1 1-3 2-4 3v-1-3 2l2-2z" class="I"></path><path d="M228 116c1 0 2 1 3 1 0 1 1 1 1 1l2 2c2 2 4 4 5 6l4 8c2 2 3 4 4 6v2l2 2-5-2c-1 0-3 0-4-1h-2l1 1h-2c-2 1-4 2-5 4-1 1-1 2-2 3h0v-9l-1-15h2c0 1 0 1 1 1 0 0 1 0 1 1 1 2 1 4 1 6l3-2v-2l1 1h1c0-2-2-4-3-6a30.44 30.44 0 0 0-8-8z" class="a"></path><path d="M242 140v-3-1c1 2 3 5 5 6l2 2-5-2-2-2z" class="n"></path><path d="M237 129l1 1c1 2 3 4 4 6v1 3h-1c-1-2-3-5-3-8 0 0-1 0-1-1v-2z" class="L"></path><path d="M237 131c0 1 1 1 1 1 0 3 2 6 3 8-1 0-2 0-2-1h-1-1c0-1-1-1-1-1 0-1-1-1-1-2h0l-1-1c-1 0-2 0-2-1 1-1 1-1 2-1l3-2z" class="C"></path><path d="M234 135l1 1h0c0 1 1 1 1 2 0 0 1 0 1 1h1 1c0 1 1 1 2 1h1l2 2c-1 0-3 0-4-1h-2l1 1h-2 0-3c0-2-1-3-1-4l-1-1v-1l2-1z" class="a"></path><path d="M234 135l1 1h0v2h-2l-1-1v-1l2-1z" class="N"></path><path d="M229 125h2c0 1 0 1 1 1 0 0 1 0 1 1 1 2 1 4 1 6-1 0-1 0-2 1 0 1 1 1 2 1l-2 1v1l1 1c0 1 1 2 1 4h3 0c-2 1-4 2-5 4-1 1-1 2-2 3h0v-9l-1-15z" class="L"></path><path d="M236 185c2-1 5-1 7-1 1 0 2 2 3 3v1l-1 1h0 0l1 1-1 1h-2v1c1 0 2 0 3 2 0 2-2 2-3 4 0 0 0 3 1 4h-1c-1 2-2 3-3 4s-2 3-3 4v-4c-1-1-1-3-1-4l-1-4-1 1c-1 1-1 2-2 2h-2c-1-2 0-5 0-6 0-2 1-3 2-4s2 0 3 0l2 1h0v-3c-1 0-1 0-2-1v-1c0-1 1-2 1-2z" class="R"></path><path d="M236 195h0c1 0 1-1 1-1h1v1c-1 0-1 0-1 1l2 1h-1c0 1 0 2-1 3l1 1c1-1 1-1 2-1h1v3h0c1 0 1-1 2-1-1 2-2 3-3 4s-2 3-3 4v-4c-1-1-1-3-1-4l-1-4-1-1-1-1h0c1-1 1-1 3-1z" class="G"></path><path d="M235 198l-1-1-1-1h0c1-1 1-1 3-1l1 1v10c-1-1-1-3-1-4l-1-4z" class="J"></path><path d="M276 153h0v-2h1 0l2 2h0c1 0 2-1 3-1 0 1 1 1 1 1v3 1c0 1 0 2-1 3s-1 4-1 6l-2 6 3-3 5-6h0c0 1 0 1 1 1-3 4-5 7-7 10v1c0 1-1 2-2 4 0 2-1 4-1 6-1 0-1 1-2 2v2c-1 3-1 6-2 9 0 0-1 1-1 2 0-3-1-8 0-10l-2-11-6-21 1 2c1 0 1-1 2-2l2 1c0-2 1-3 1-4 0 0 0 1 1 1h1c1 0 1 0 2-1 1 0 0-1 1-2z" class="G"></path><path d="M276 164l1-2 1 1 1-1-1 4-2-2z" class="I"></path><path d="M276 153h0v-2h1 0l2 2c-1 0-2 1-3 0z" class="F"></path><path d="M273 190v-3c0-3 0-5 1-8v3 5 3 8s-1 1-1 2c0-3-1-8 0-10z" class="N"></path><path d="M276 164l2 2-4 10v1c0-1-1-1-1-1v-4h0c0-1 0-2-1-3h1l1-1 1 1 1-5z" class="P"></path><path d="M272 169h1l1-1 1 1c0 1-1 2-2 3h0c0-1 0-2-1-3z" class="O"></path><path d="M276 155v-1c2-1 5 0 6 0l-3 8-1 1-1-1h0c1-1 1-3 1-4s-1-2-2-3z" class="D"></path><path d="M279 172l3-3 5-6h0c0 1 0 1 1 1-3 4-5 7-7 10l-1 1-3 6v3l-1-1v-3c1-1 1-2 1-3l1-2v-1c0-1 0-1 1-2z" class="C"></path><path d="M276 155c1 1 2 2 2 3s0 3-1 4h0l-1 2-1 5-1-1-1 1h-1c-1-4-2-8 1-13 1 0 1 0 2-1l1 1v-1z" class="K"></path><path d="M275 161c1 0 2 1 2 1h0l-1 2-1 5-1-1 1-7z" class="E"></path><path d="M276 155c1 1 2 2 2 3s0 3-1 4c0 0-1-1-2-1l1-5v-1z" class="C"></path><path d="M271 155s0 1 1 1h1c-3 5-2 9-1 13 1 1 1 2 1 3h0v4s1 0 1 1v2c-1 3-1 5-1 8v3l-2-11-6-21 1 2c1 0 1-1 2-2l2 1c0-2 1-3 1-4z" class="I"></path><path d="M271 155s0 1 1 1h1c-3 5-2 9-1 13 1 1 1 2 1 3h0v4l-1-1c-1-5-4-12-2-16 0-2 1-3 1-4z" class="G"></path><path d="M237 142h2l-1-1h2c1 1 3 1 4 1l5 2c1 1 4 3 5 5h0c1 1 1 1 1 2 1 0 1 1 2 2 0 1 1 1 2 2 1 2 2 4 3 5 1 2 2 4 2 6l1 3c1 1 2 3 2 4h0l-2-3-1 1h0v2l-1-1c0 1 1 3 1 4s1 3 1 5h0 0l-1-1v-2c0-1-1-2-1-2v-2c-1-1-1-3-2-4s-1-3-1-4l-3-6c0-2-3-6-5-8 0 1-1 2-1 3h0v1h0c0 1-1 1-2 1h0l-3 3v1c-2 1-3 2-4 2-3 0-6-1-8-3-4-3-3-7-4-11 1-1 1-2 2-3 1-2 3-3 5-4z" class="R"></path><path d="M244 146c3 1 6 3 8 6h-2c-1-1-1 0-1 0-1-1-3-2-4-3h0 1l-2-3z" class="B"></path><path d="M244 149h1 0c1 1 3 2 4 3 0 0 0-1 1 0h2c0 1-1 2-1 3h0v1h0c0 1-1 1-2 1h0c-1 0-2-1-4-2h0c-1-2-1-4-1-6z" class="Y"></path><path d="M250 152h2c0 1-1 2-1 3l-2-2 1-1zm-6-3h1 0c1 1 1 2 1 3l3 2h0c-2 1-2 0-4 1-1-2-1-4-1-6z" class="L"></path><path d="M240 144l1 1c1 0 2 0 3 1l2 3h-1-1c0 2 0 4 1 6h0c2 1 3 2 4 2l-3 3v1c-2 1-3 2-4 2-3 0-6-1-8-3-4-3-3-7-4-11 1-1 1-2 2-3h0v1c-1 1-1 3-1 5v-1c1-1 1-1 1-2h0c0-1 0-1 1-2v-1h1 1c2-1 3-2 5-2z" class="S"></path><path d="M245 155c2 1 3 2 4 2l-3 3v1l-1-3h-2l-2 2h0v-1h0v-1c1-1 2-1 4-1v-2z" class="E"></path><path d="M234 155l1 1h3c2-1 4-3 4-5 0-1 0-1 1-2h1c0 2 0 4 1 6h0v2c-2 0-3 0-4 1v1h0-1-1c-1-1-1-1-2-1s-2 0-3-1v-2z" class="F"></path><path d="M240 144l1 1c1 0 2 0 3 1l2 3h-1-1-1c-1 1-1 1-1 2 0 2-2 4-4 5h-3l-1-1c-1 0-1-1-2-2 0-3 1-5 3-7 2-1 3-2 5-2z" class="n"></path><path d="M240 144l1 1c0 2 1 5-1 7v1l-1 1c-1 1-1 1-2 1 1-1 1-2 1-4 0-1-1-2-1-3s1-2 2-3l1-1z" class="C"></path><path d="M238 231c1-2 1-3 3-5s5-3 8-3c-1 0-1 1-2 2v1l4 1 4 1c0 1 0 1 1 1h5 2 1 1 0 0c2 1 3 2 4 4h0l2-1c0 1 1 3 1 4 0 2 0 4-1 5 0 2 1 4 1 6v3c0 1 0 1-1 2h0v-1h0c0-2-1-2-1-3l-1-1v-2l-1-3h-1 0-1c-2 0-5 2-7 3-5 2-9 3-13 1l-4-2-1-2-2-2c-1-3-1-5-1-9z" class="G"></path><path d="M249 235l-1 1c-1 0-1 0-2-1v-1h3v1z" class="M"></path><path d="M250 233l1-1h0c0 1 1 1 1 1v1c-1 0-1 0-1 1v1h1 1 0c1 1 1 1 2 3h0c0 1-1 1-2 2h0 0l-1-1h0c-2-1-3-3-3-5v-1l1-1z" class="D"></path><path d="M244 236h2c0 1 1 2 1 2 1 2 4 3 6 3h0 2v1l-1 1h-1s0 1-1 1h-4c-2 0-5-2-6-4l1-1h1v-3z" class="B"></path><path d="M247 226l4 1 4 1c0 1 0 1 1 1-2 0-4 1-5 3h0l-1 1h-3v-1c-1 1-1 1-1 2h-2v-1 1h0c0 1 0 1-1 1v1h1v3h-1l-1 1c-1-1-1-2-2-3 0-2 0-5 1-7 2-3 4-3 6-4z" class="H"></path><path d="M251 227l4 1c0 1 0 1 1 1-2 0-4 1-5 3h0l-1 1h-3v-1c-1 1-1 1-1 2h-2v-1c0-1 1-3 2-4h2c1-1 2-1 3-2z" class="M"></path><path d="M247 232l-1-1h0c1-1 2 0 3 0v1c-1 0-1 0-2 1v-1z" class="Z"></path><path d="M238 231c1-2 1-3 3-5s5-3 8-3c-1 0-1 1-2 2v1c-2 1-4 1-6 4-1 2-1 5-1 7 1 1 1 2 2 3 1 2 4 4 6 4h4c1 0 1-1 1-1h1l1-1v-1h-2 0 0 0c1-1 2-1 2-2l1 1v-1l2-2h0c1 0 2-1 3-1h1s1 0 1 1h0 1v1l1 1c1 0 1 1 2 1v2h0-1c-2 0-5 2-7 3-5 2-9 3-13 1l-4-2-1-2-2-2c-1-3-1-5-1-9z" class="E"></path><path d="M261 236h1s1 0 1 1h0 1v1l1 1c1 0 1 1 2 1-1 1-3 1-4 1h0c-3 3-8 4-12 3h-3 4c1 0 1-1 1-1h1l1-1v-1h-2 0 0 0c1-1 2-1 2-2l1 1v-1l2-2h0c1 0 2-1 3-1z" class="J"></path><path d="M261 236h1s1 0 1 1c-2 0-3 1-4 3l-1-1v-2h0c1 0 2-1 3-1z" class="d"></path><path d="M258 237v2l1 1c-1 0-2 1-2 2v1h-1-2l1-1v-1h-2 0 0 0c1-1 2-1 2-2l1 1v-1l2-2z" class="U"></path><path d="M265 229h0c2 1 3 2 4 4h0l2-1c0 1 1 3 1 4 0 2 0 4-1 5 0 2 1 4 1 6v3c0 1 0 1-1 2h0v-1h0c0-2-1-2-1-3l-1-1v-2l-1-3h-1v-2c-1 0-1-1-2-1l-1-1v-1h-1 0c0-1-1-1-1-1h-1c-1 0-2 1-3 1h0l-2 2v1l-1-1h0c-1-2-1-2-2-3h0-1-1v-1c0-1 0-1 1-1v-1s-1 0-1-1h0 0c1-2 3-3 5-3h5 2 1 1 0z" class="h"></path><path d="M255 232h5l2 1c1 2 1 2 1 4h0c0-1-1-1-1-1h-1-1c-1-1-1-1-1-2l-1-2h-3z" class="Q"></path><path d="M253 236v-1-1l2-2h3l1 2c0 1 0 1 1 2h1c-1 0-2 1-3 1h0l-2 2v1l-1-1h0c-1-2-1-2-2-3h0z" class="b"></path><path d="M255 239v-2s1-1 2-1l1 1h0l-2 2v1l-1-1h0z" class="N"></path><path d="M253 236v-1-1l2-2h3l1 2h-1c-1 1-1 1-2 1v-1l-1 1v1h-2 0z" class="o"></path><path d="M265 229h0c2 1 3 2 4 4h0l2-1c0 1 1 3 1 4 0 2 0 4-1 5 0 2 1 4 1 6v3c0 1 0 1-1 2h0v-1h0c0-2-1-2-1-3l-1-1v-2l-1-3c0-3-1-6-2-8-1-3-3-4-5-5h2 1 1 0z" class="X"></path><path d="M269 233l2-1c0 1 1 3 1 4 0 2 0 4-1 5 0-3-1-5-2-8z" class="D"></path><path d="M305 182l2 2h1 4v1c0 1 0 3-1 5h1c3 1 4 1 6 3v1 1 4l-3 2c-2 2-4 4-5 8v2h0v3h-1l-1-2c0 2 1 6 0 7 0 2 0 3 1 4 0 1-1 1-1 1-2-1-3-2-5-3-1-1-3-2-4-4-1 0-2-1-3-2v-1c3-2 4 0 6 0v-1c-1-2-2-2-3-3-2 0-4 1-6 2h-2c-1-1-1-3-2-4v-3c1-1 0-3 0-4v-1c1 0 1 0 2-1l-1-2 3-1v-5c1 1 6 4 6 5h3v-2l-3-3 1-1c0-1 1-2 2-2v-1h0v-1h0c0-1 0-1 1-1 0-2 1-2 2-3z" class="S"></path><path d="M293 196v-5c1 1 6 4 6 5v2h-2c-1 1 0 1-1 2v-4l-1-1h-2v1h0z" class="M"></path><path d="M310 193v1c0 1-2 2-2 3v2c-2 4-1 10 0 13 0 2 1 6 0 7-1-4-3-11-7-14-1 0-1 0-1-1 1 0 1 1 2 1h0v-3c-1-2-2-3-3-4v-2h3v1c0 3 1 5 2 8 0 2 2 4 2 6h1 0c-1-3-1-7-1-10h0c0-2 1-4 2-5l2-3z" class="a"></path><path d="M299 196h3v1 5c-1-2-2-3-3-4v-2z" class="L"></path><path d="M310 193c0-1 0-2 1-3h1c3 1 4 1 6 3v1 1 4l-3 2c-2 2-4 4-5 8v2h0v3h-1l-1-2c-1-3-2-9 0-13v-2c0-1 2-2 2-3v-1z" class="Z"></path><path d="M310 193c0-1 0-2 1-3h1c3 1 4 1 6 3v1 1c-1 0-1 1-2 1l-1 1v-1h-1l1-1h-1s-1 0-1 1l-1 1v-1c-2 1-3 2-4 3v-2c0-1 2-2 2-3v-1z" class="F"></path><path d="M305 182l2 2h1 4v1c0 1 0 3-1 5-1 1-1 2-1 3l-2 3c-1 1-2 3-2 5h0c0 3 0 7 1 10h0-1c0-2-2-4-2-6-1-3-2-5-2-8v-1-2l-3-3 1-1c0-1 1-2 2-2v-1h0v-1h0c0-1 0-1 1-1 0-2 1-2 2-3z" class="X"></path><path d="M302 194l-3-3 1-1h3c1 1 1 1 1 3l-1 1h-1z" class="Z"></path><path d="M305 182l2 2h-1v2l-1 1h0c-1-1-2-1-3-1h0c0-1 0-1 1-1 0-2 1-2 2-3z" class="C"></path><path d="M308 184h4v1c0 1 0 3-1 5-1 1-1 2-1 3l-2 3c-1 1-2 3-2 5h0l-1 2v1h-1l-1-1c0-1 1-2 1-3v-2c1-2 1-3 1-4v-4c1-1 2-1 3-3-1-1-1-1-1-2h1 0v-1z" class="Z"></path><path d="M292 146h1l2-2c1-2 3-3 4-5v-3c1 0 2-2 3-2v1h1 0 0l1-1c1 0 1 0 2 1l-8 9h2l2-2c1 1 4 0 5 0s3 0 4-1c2 1 3 1 4 2h1l2-1c1 2 0 3 1 5 0 2 1 4 0 5 0 2 1 4 0 6h0v7h0c-1 0-1 0-2 1h-1c-2-2-5-2-8-3-3 0-6-1-9-1-7 2-14 7-18 13h0v-1c2-3 4-6 7-10-1 0-1 0-1-1h0l-5 6-3 3 2-6c0-2 0-5 1-6s1-2 1-3c1-2 4-5 5-7l4-4z" class="B"></path><path d="M316 143l2-1c1 2 0 3 1 5 0 2 1 4 0 5h0v-2c0-1-1-1-1-2v-1 1 1c1 2 0 3 0 4 0-3-1-6-2-8v-2z" class="D"></path><path d="M295 156c1 1 1 1 1 3h-1c-1 0-2 1-3 2l-4 3c-1 0-1 0-1-1h0c1-2 2-4 3-5 2 0 4-1 5-2z" class="a"></path><path d="M287 163c1 0 1-1 1-1 1-2 2-3 3-3s1 1 1 2l-4 3c-1 0-1 0-1-1z" class="S"></path><path d="M295 156h0l1-1 3-3c1 0 2 1 2 1v2c1 1 1 2 1 3v1l1 1c-1 0-2 0-3-1-1 0-1 0-2 1 0-1-1-1-1-1h-1c0-2 0-2-1-3z" class="D"></path><path d="M302 159s-1 0-1-1h-1-1-1 0v-2c1 0 2 0 3-1 1 1 1 2 1 3v1z" class="B"></path><path d="M307 142c1 0 3 0 4-1 2 1 3 1 4 2h1v2c1 2 2 5 2 8-1 1-1 2-2 3-1 3-3 4-6 5-2 0-5 0-7-1l-1-1v-1c0-1 0-2-1-3v-2h1v-1-1l2-2 1-1c-1 0-2-1-3-1l2-2c1 0 2-1 2-1 2 0 3 0 4 1v-1l1-1h0c-1-1-2-1-4-1z" class="N"></path><path d="M304 149c1 1 2 2 3 2l1 1 3 2h-1-2-3c-1-1-1-2-3-2v-1l2-2zm-3 4h1c1 1 3 1 4 2h6v1l-1 1h0c-1 1-2 1-4 1-2-1-3-2-5-2v2c0-1 0-2-1-3v-2z" class="F"></path><path d="M310 145v-1l1-1c2 2 4 3 4 5 1 2 0 3-1 4s-2 1-3 2l-3-2 1-1c1 0 1-1 1-1l1-1-1-4z" class="B"></path><path d="M306 144c2 0 3 0 4 1l1 4-1 1s0 1-1 1l-1 1-1-1c-1 0-2-1-3-2l1-1c-1 0-2-1-3-1l2-2c1 0 2-1 2-1z" class="C"></path><path d="M304 145c1 0 2-1 2-1l1 6v1c-1 0-2-1-3-2l1-1c-1 0-2-1-3-1l2-2z" class="D"></path><path d="M311 157c2 0 3-1 4-2l1 1c-1 3-3 4-6 5-2 0-5 0-7-1l-1-1v-1-2c2 0 3 1 5 2 2 0 3 0 4-1z" class="Z"></path><path d="M292 146h1l2-2c1-2 3-3 4-5v-3c1 0 2-2 3-2v1h1 0 0l1-1c1 0 1 0 2 1l-8 9h2l2-2c1 1 4 0 5 0 2 0 3 0 4 1h0l-1 1v1c-1-1-2-1-4-1 0 0-1 1-2 1l-2 2c1 0 2 1 3 1l-1 1-2 2v1 1h-1s-1-1-2-1l-3 3-1 1h0c-1 1-3 2-5 2-1 1-2 3-3 5l-5 6-3 3 2-6c0-2 0-5 1-6s1-2 1-3c1-2 4-5 5-7l4-4z" class="M"></path><path d="M283 157c1-2 4-5 5-7 0 3-1 4-2 6s-2 4-3 5c0 2-1 3-2 5 0-2 0-5 1-6s1-2 1-3z" class="F"></path><path d="M302 142c1 1 4 0 5 0 2 0 3 0 4 1h0l-1 1v1c-1-1-2-1-4-1 0 0-1 1-2 1h-2c-1 1-2 1-3 1s-3 1-4 2c1-2 2-3 3-4h2l2-2z" class="V"></path><path d="M290 158c3-5 8-8 12-11 1 0 2 1 3 1l-1 1-2 2v1 1h-1s-1-1-2-1l-3 3-1 1h0c-1 1-3 2-5 2z" class="B"></path><defs><linearGradient id="u" x1="294.454" y1="137.653" x2="297.648" y2="151.691" xlink:href="#B"><stop offset="0" stop-color="#b4b4b1"></stop><stop offset="1" stop-color="#d9d5db"></stop></linearGradient></defs><path fill="url(#u)" d="M292 146h1l2-2c1-2 3-3 4-5v-3c1 0 2-2 3-2v1h1 0 0l1-1c1 0 1 0 2 1l-8 9c-1 1-2 2-3 4-1 0-3 2-4 3v1l-4 4c0 1 0 1-1 0 1-2 2-3 2-6l4-4z"></path><path d="M289 208c1 1 1 3 2 4h2c2-1 4-2 6-2 1 1 2 1 3 3v1c-2 0-3-2-6 0v1c1 1 2 2 3 2 1 2 3 3 4 4 2 1 3 2 5 3 0 0 1 0 1-1 1 0 1 0 2 1v6c1 1 0 3 1 5-1 5-3 9-6 13-1 1-3 2-3 4l-2-3-1-1-2-3h-2-1v1c-1-2-2-2-3-2l-2-2-4-4c-2 0-4 1-5 1-1 1-2 1-3 1-1 2-2 4-2 6 0 0 0 1-1 1v1h-1v-4 1l-2 2c0-2-1-4-1-6 1-1 1-3 1-5 0-1-1-3-1-4l4 1 2 1 5-5-2-1c3-1 7-2 10-4h-2c2-3 3-5 4-8v-1l-1-1c-1-2-3-3-4-4l2-2z" class="c"></path><path d="M275 233l2 1c-1 2-2 4-2 6h-1v-1c0-2 0-4 1-6z" class="g"></path><path d="M271 232l4 1c-1 2-1 4-1 6l-1 1h0l-1-4c0-1-1-3-1-4z" class="G"></path><path d="M272 236l1 4h0l1-1v1h1c-1 1-1 3-1 4v1l-2 2c0-2-1-4-1-6 1-1 1-3 1-5z" class="V"></path><path d="M301 240c1 1 0 1 0 2-1 1-1 1-3 2h1l1 1 1-1c1 0 1 0 1-1v2h-1-3-2-1v1c-1-2-2-2-3-2l2-2v-1l1 1c2 1 2 1 4 1 1-1 2-2 2-3z" class="R"></path><path d="M300 226c3 2 5 6 5 9 1 2 1 3 0 5 0 0 0 1 1 1 0 1 0 1-1 2h-1 0c-1 1-1 2-2 2h0v-2h0c1-1 2-2 2-4 0-4-2-8-5-11 1-1 1-1 1-2z" class="I"></path><path d="M280 235c2-3 5-5 8-6s6 0 8 1c3 2 4 4 5 7v3c0 1-1 2-2 3-2 0-2 0-4-1l-1-1v1l-2 2-2-2-4-4c-2 0-4 1-5 1-1 1-2 1-3 1l1-2 1-3z" class="L"></path><path d="M282 236l3-3h1c1 0 1 0 1-1h1 3 3c1 0 2 2 2 2 1 1 1 2 1 3-1-2-2-2-4-3l-2 1v-2h-2-1c-1 1-2 1-3 2h0c-1 1-2 1-2 2l-1-1z" class="I"></path><path d="M291 233c1 0 2 0 3 1h0-1l-2 1v-2z" class="D"></path><path d="M283 237c0-1 1-1 2-2h0c1-1 2-1 3-2h1 2v2c-2 1-3 3-5 3s-4 1-5 1c-1 1-2 1-3 1l1-2 1-3c1 1 1 1 2 1l1 1z" class="g"></path><path d="M280 235c1 1 1 1 2 1l1 1c-2 1-3 1-4 1l1-3z" class="D"></path><path d="M293 234c2 1 3 1 4 3v3l-2 2-1-1v1l-2 2-2-2-4-4c2 0 3-2 5-3l2-1z" class="C"></path><path d="M291 237h4 0c1 1 1 1 1 2l-2 2v1c-1-1-2-1-3-2v-2-1z" class="K"></path><path d="M291 237h4 0c-1 1-1 2-2 3-1-1-1-1-1-2l-1-1z" class="a"></path><path d="M289 208c1 1 1 3 2 4h2c2-1 4-2 6-2 1 1 2 1 3 3v1c-2 0-3-2-6 0v1c1 1 2 2 3 2 1 2 3 3 4 4 2 1 3 2 5 3 0 0 1 0 1-1 1 0 1 0 2 1v6c1 1 0 3 1 5-1 5-3 9-6 13-1 1-3 2-3 4l-2-3-1-1-2-3h3 1 0c1 0 1-1 2-2h0 1c1-1 1-1 1-2-1 0-1-1-1-1 1-2 1-3 0-5 0-3-2-7-5-9 0 1 0 1-1 2-3-2-5-3-9-2-3 0-6 2-8 3l-2-1c3-1 7-2 10-4h-2c2-3 3-5 4-8v-1l-1-1c-1-2-3-3-4-4l2-2z" class="B"></path><path d="M295 221h1 0 1l1 1c-2 1-4 2-6 1 1 0 2-1 3-2z" class="G"></path><path d="M298 218l3 2v1c-1 0-2 1-3 1l-1-1h-1 0l2-3z" class="a"></path><path d="M292 215c2 0 4 2 6 3l-2 3h-1 0l-1-1v-3h0l-2-1v-1z" class="c"></path><path d="M302 245h0c1 0 1-1 2-2h0 1c0 1-1 2-2 3s-2 2-2 3l-1-1-2-3h3 1z" class="P"></path><path d="M292 216l2 1h0v3l1 1h0c-1 1-2 2-3 2l-2 1h-2c2-3 3-5 4-8z" class="Z"></path><path d="M309 223c1 0 1 0 2 1v6c0 3-1 7-2 10 0-6 1-10-1-15v-1s1 0 1-1z" class="M"></path><path d="M300 226v-1c1-1 2-1 3-1s2 1 3 2c2 2 2 7 1 11 0 1-1 3-1 4-1 0-1-1-1-1 1-2 1-3 0-5 0-3-2-7-5-9z" class="X"></path><path d="M227 514h0v6h1v-4-1s0-1 1-1v4c0 2 1 5 2 6 1 0 1 1 2 1 2 1 6 2 7 3s5 3 6 5l-1 1 1 1h4c2 1 4 2 5 3l5 3h0l1 2c1 1 2 1 2 2s1 3 0 4h0 0l2 3s0 1-1 1l3 3c1 0 2 1 2 2h1l2 5c-1-1 0-2 0-3v-3h1 0 1c1 1 1 2 1 4h0c0 1 0 2 1 3v-2s0-1 1-2c0-1 1-1 2-2v-1c1-2 1-3 2-5 1-3 3-6 5-8l2-3 1-2 1-1c1-1 2-1 3-2 2-1 3-3 5-4l1-1h-1c5-3 9-5 12-10h0v-1l1-1h0 1c3-1 4-1 6-4h0c1 2 0 3 1 4l2 43v4l5 14 5 10c-1 1-1 1-1 3l-1 1h-1 1c1 1 1 3 2 4-1 1-3 0-4 0h-1c1 2 1 3 2 4v1s0 1 1 2l1 2v1h-2c0 1 1 1 1 2 0 0-1 1-1 2l-1 2 1 1c2-2 4-2 6-3s3-2 5-2c3 0 7 1 9 4h0v-1h2c9 8 22 14 34 17 5 1 10 3 15 3h0c1 1 1 3 1 4 0 2 0 4 1 6h-53l-24-1h-41-37-89c-11 0-23-1-33 0v-10h14c16-2 32-7 46-16 4-3 8-6 12-10 0 0 3-3 3-4 4-4 7-9 10-13 0-2 3-6 4-7 4-10 7-19 9-29l5-39z" class="n"></path><path d="M220 638h-1-3c0-1 0-1 1-1 1-1 2-2 3-1v2z" class="G"></path><path d="M272 636l1-2h1c1 1 2 2 2 4v1h-1 0c0-2-2-3-3-3z" class="W"></path><path d="M347 618h1c0-1 0-1 1-1h0 2c1 1 1 1 1 2l-1 2v-1c-1 0-1 0-2-1 0 0-1 0-2-1z" class="a"></path><path d="M363 627l7 2c0 1-1 1-2 1-1-1-3 0-5-1v-2z" class="N"></path><path d="M377 632c1-1 1-1 2-1 1 1 3 2 5 2-1 0-2 0-2 1h-2c-1 0-1 1-1 1h-1c-1-1 0-2-1-3z" class="c"></path><path d="M275 639h-1c-1 1-3 0-4 0 0-2 1-2 2-3 1 0 3 1 3 3h0z" class="U"></path><path d="M370 629c2 0 3 1 4 2v1h1 0v-1h1l1 1c-1 0-1 1-2 1h-1c-1 0-2-2-3-2h-2l-1-1c1 0 2 0 2-1z" class="T"></path><path d="M368 632c-2-1-5-1-7-2v-3h2v2c2 1 4 0 5 1l1 1s-1 0-1 1z" class="E"></path><path d="M220 636l1-1h1 0c2 1 4 1 5 3-2 0-5 1-7 0v-2z" class="e"></path><path d="M188 629c1 0 2 0 3 1h1v2c-2 0-2 0-3 1h-2c-1 0-1-1-2-2 1-1 2-1 3-2z" class="C"></path><path d="M351 623h1c0-1 0-1 1-1s3 1 4 3c1 1 2 3 3 5h0c-2-1-2-3-4-4h0c1 2 2 3 2 5l-3-4 1-1c-1-1-2-2-4-2 0 0 0-1-1-1z" class="G"></path><path d="M202 634c-2-1-3-2-4-3 2-1 4-2 6-2v1h1v1c-1 0-1 0-1 1v1l-2 1z" class="C"></path><path d="M192 630c1 1 2 2 3 2h1v3c-1 1-1 1-2 1-2 0-3 0-4-1 1-1 2-1 2-2v-1-2z" class="M"></path><path d="M196 608c1 0 1-1 2-1h1v1h-1v1c1 1 1 2 1 3h-7c0-1 3-3 4-4z" class="l"></path><path d="M131 635h1c1 1 1 1 1 2l-2 2h-1s-3 1-3 0v-2-2h4z" class="B"></path><path d="M131 635h1c1 1 1 1 1 2l-2 2h-2v-1h1c0-1 0-2 1-3z" class="j"></path><path d="M199 608c1 0 2 1 3 1 0 0 1 0 2 1h0c1 0 2 0 3-1h1 0c-1 1-1 2-2 2l-1 1 1 1-7-1c0-1 0-2-1-3v-1h1z" class="U"></path><path d="M155 638l-1 2c-4 0-6 0-10-2 3 0 6 0 9-1 1-1 1-1 2-1h3v1c-1 0-1 1-2 1h-1z" class="c"></path><path d="M332 636c1 1 3 1 4 1 2 0 3-2 5-2 0 1-3 2-3 3h2-2l1 1-2 1c-5-2-11-1-16-3h0l1-1c3 1 7 2 10 0z" class="G"></path><path d="M155 635c2-2 3-3 5-3s4 0 5 1c2 1 3 4 4 5-1 0-2-1-3-2-2 0-3 0-4-1-1 0-2 0-4 1v-1h-2-1 0z" class="Z"></path><path d="M384 633l2 1h1v1h-1v1h2c2 1 4-1 6-2-2 2-4 4-7 5-3 0-7-1-10-3 1 0 2 0 3-1h-1s0-1 1-1h2c0-1 1-1 2-1z" class="C"></path><path d="M384 633l2 1h-1v1 1c-1 0-4-1-5-1h-1s0-1 1-1h2c0-1 1-1 2-1z" class="S"></path><path d="M158 636h1c1 1 3 1 4 1 2 0 3 0 4 2 1 0 2 0 2-1h3c2 0 4-1 5-4h1l1 1c-2 2-4 4-7 4-3 1-8 1-12 1-1-1-3-1-4-1l-1-1h1c1 0 1-1 2-1v-1h-3v-1h1 2v1z" class="C"></path><path d="M155 635h0c0 1-1 1-1 1-2 1-10 2-12 1-1-1-3-2-3-3h1c3 1 6-2 10-2 2 0 4 1 5 3h0z" class="R"></path><path d="M358 631c0-2-1-3-2-5h0c2 1 2 3 4 4h0c1 2 1 5 0 7s-3 4-5 4c-1 1-3 1-5 1s-5 0-7-1c-2 0-4-1-6-1l2-1-1-1h2c2-1 5-1 6-2s4 0 5 0l1 1v1h-1c1 1 2 1 3 1 2 0 3-1 3-2 2-2 1-4 1-6z" class="M"></path><path d="M340 638c2-1 5-1 6-2s4 0 5 0c-2 1-3 2-5 2-2 1-5 1-7 1l-1-1h2z" class="I"></path><defs><linearGradient id="v" x1="292.766" y1="623.636" x2="290.422" y2="639.866" xlink:href="#B"><stop offset="0" stop-color="#9d9c9e"></stop><stop offset="1" stop-color="#b8b8b9"></stop></linearGradient></defs><path fill="url(#v)" d="M281 621l4 1c-1 1-2 1-3 1-2 1-3 1-4 4-1 1 0 4 0 5 2 3 5 5 9 6 2 1 4 1 6 0 2 1 5 0 6 0h1 3c0-1 2-1 3-1v1h-1c1 1 2 0 3 0l-1 1h0c-2 1-4 2-6 2-6 0-16 1-21-4-3-2-5-5-5-8s2-6 4-8h2z"></path><path d="M336 625l1-1 1 1c1 0 2 0 3 1 1 2 1 3 1 5-1 1-3 1-5 3-2 0-3 1-5 2-3 2-7 1-10 0 4 0 1-1 3-3 1-1 3-2 4-3h0 1 1c2 0 4-2 5-4v-1z" class="F"></path><path d="M330 633h1v1 1c-1 0-2-1-2-1l1-1z" class="M"></path><path d="M325 633c2 1 3 2 4 3 3 0 4-2 6-3l2 1c-2 0-3 1-5 2-3 2-7 1-10 0 4 0 1-1 3-3z" class="g"></path><path d="M338 625c1 0 2 0 3 1 1 2 1 3 1 5-1 1-3 1-5 3l-2-1 3-5v-3z" class="I"></path><path d="M368 632c0-1 1-1 1-1h2c1 0 2 2 3 2h1c1 0 1-1 2-1h0c1 1 0 2 1 3h1 1c-1 1-2 1-3 1v1c1 1 2 1 4 1 1 0 2 1 4 1v1h-4l-1 1c-1 0-2 0-3-1l-2-1c-1 1-2 2-3 2l-2-1-1-1c-1 1-1 1-2 1s-4 0-5-1c0-1 0-2 1-2 1-2 4-3 5-5h0z" class="R"></path><path d="M370 640l-1-1c-1 1-1 1-2 1s-4 0-5-1c0-1 0-2 1-2h0c2 1 11-1 12 0l-1 1c-1 0-2 1-3 1l-1 1z" class="G"></path><defs><linearGradient id="w" x1="354.152" y1="630.713" x2="334.623" y2="639.318" xlink:href="#B"><stop offset="0" stop-color="#a5a3a4"></stop><stop offset="1" stop-color="#c3c1c4"></stop></linearGradient></defs><path fill="url(#w)" d="M345 627l1-2c4-1 6 0 9 2l3 4c0 2 1 4-1 6 0 1-1 2-3 2-1 0-2 0-3-1h1v-1l-1-1c-1 0-4-1-5 0s-4 1-6 2h-2c0-1 3-2 3-3-2 0-3 2-5 2-1 0-3 0-4-1 2-1 3-2 5-2 2-2 4-2 5-3h1l1-1c1-1 1-2 1-3z"></path><path d="M345 627l1-2c4-1 6 0 9 2l3 4c0 2 1 4-1 6 0 1-1 2-3 2-1 0-2 0-3-1h1c1-1 2-1 2-2 1 0 3-1 3-3-1-1-3 0-5 0v-1l2-2c0-1 0-1-1-2-1 0-2 1-3 1-1 1-2 1-4 1l-2 1h-1l1-1c1-1 1-2 1-3z" class="I"></path><path d="M190 618c2-1 6-3 8-3 3 0 5 0 8 1 1 1 1 1 1 2l1 1c-1 1-2 3-1 4l2 5c-1 1-2 2-4 3v-1h-1v-1-1c1-1 1-3 0-4l-1-1c-2 0-3 1-4 2s-2 2-2 4c0 1-1 2-1 3h-1c-1 0-2-1-3-2h-1c-1-1-2-1-3-1h0c-1 0-2 0-3-1v-3h1 1c-1 0-1 0-1-1h-1l1-1c1-2 2-3 4-5z" class="R"></path><path d="M190 618c2-1 6-3 8-3 3 0 5 0 8 1 1 1 1 1 1 2l1 1c-1 1-2 3-1 4l2 5c-1 1-2 2-4 3v-1c1-1 2-1 2-2v-3l-1-1v-1l-1-2c1-1 2-1 2-3h-1c-2-1-2-1-4 0h-2c-1 0-2-1-3-1v1h0-1-3-3z" class="N"></path><path d="M339 610c3 0 7 1 9 4h0 0c0 1-1 2-1 3v1c1 1 2 1 2 1 1 1 1 1 2 1v1 1 1c1 0 1 1 1 1 2 0 3 1 4 2l-1 1c-3-2-5-3-9-2l-1 2c0 1 0 2-1 3l-1 1h-1c0-2 0-3-1-5-1-1-2-1-3-1l-1-1-1 1c-1 1-1 2-3 2 0 0 0-1-1-2 0-3 0-5-1-7h-1c-2 1-2 3-3 5h-1 0-1v-2c1-1 1-2 1-2v-1c1-1 1-2 2-3 2-2 4-2 6-3s3-2 5-2z" class="R"></path><path d="M343 615h3v1l-1 1v1h-1c-1-1-1-2-1-3z" class="S"></path><path d="M334 612c2-1 3-2 5-2v2 1c-1 0-1-1-2 0h-1v1s0 1-1 2v1h0l-1-1h0c1-1 1-2 1-4h-1z" class="C"></path><path d="M341 626h1v-1-1c1 1 2 1 2 3h1c0 1 0 2-1 3l-1 1h-1c0-2 0-3-1-5z" class="F"></path><defs><linearGradient id="x" x1="326.029" y1="624.733" x2="293.636" y2="637.733" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#c7c5c6"></stop></linearGradient></defs><path fill="url(#x)" d="M319 605l2 2c0-1 1-1 1-2 3 0 4-1 7 0l1 2v1h-2c0 1 1 1 1 2 0 0-1 1-1 2l-1 2 1 1c-1 1-1 2-2 3v1s0 1-1 2v2h1 0 1v3c-1 4-7 7-10 9-3 1-6 3-10 4h0l1-1c-1 0-2 1-3 0h1v-1c-1 0-3 0-3 1h-3-1c-1 0-4 1-6 0l-2-1 1-2h2c1 1 3 1 4 1 5 0 9-1 13-4 4-2 7-6 8-10h1c0-1-1-1-1-2l1-1h0v-1c-1-1-1-1-2-1v-1h1v-2c-1 0-2-1-3-1v-2h0c1-1 1-1 2-1h-1c0-2 0-4 1-6l1 1z"></path><path d="M316 611c2 1 3 0 4 0l1 1c0 1-1 2 0 4l1 1c0 1 1 2 1 3v3l1 1c-1 2-2 2-2 3-1 1-1 2-2 2h0v-1h1c0-1 0-2 1-3v-4h-1 0-1v-2h0v-1c-1-1-1-1-2-1v-1h1v-2c-1 0-2-1-3-1v-2z" class="N"></path><path d="M323 613h1v-1h1c1-1 2-1 3 0l-1 2 1 1c-1 1-1 2-2 3v-1c-1 1-2 1-2 3 0 1 0 2-1 3v-3c0-1-1-2-1-3l-1-1c-1-2 0-3 0-4l2 1z" class="G"></path><path d="M323 613h1v-1h1c1-1 2-1 3 0l-1 2c0 1-1 1-1 1l-1 1h-1c0-1 0-2-1-3z" class="C"></path><path d="M319 605l2 2c0-1 1-1 1-2 3 0 4-1 7 0l1 2v1h-2c0 1 1 1 1 2 0 0-1 1-1 2-1-1-2-1-3 0h-1v1h-1l-2-1-1-1c-1 0-2 1-4 0h0c1-1 1-1 2-1h-1c0-2 0-4 1-6l1 1z" class="F"></path><path d="M318 604l1 1v1c0 1 0 2 1 4h0-2-1c0-2 0-4 1-6z" class="c"></path><path d="M288 608l2-2c2 0 2 0 4 1l3 3v-1c2 1 3 2 4 4l1-1 4 5c1 2 3 4 6 5 2 0 3 0 4-1 2 0 2 0 3 1-1 4-4 8-8 10-4 3-8 4-13 4-1 0-3 0-4-1h-2l-1 2 2 1c-2 1-4 1-6 0-4-1-7-3-9-6 0-1-1-4 0-5 1-3 2-3 4-4 1 0 2 0 3-1l-4-1 1-1v-1l-1-1h-4c-2 0-4 0-6-1h-3 0c3-1 6 1 10-1h3c1-1 1-1 1-2h0c1 0 1 1 1 2l1-1h-1v-1c0-2 1-3 2-4h1-1 0v-1h1l2-1z" class="I"></path><g class="T"><path d="M291 637c0-1 0-1-1-1-1-1-4-3-4-5h0c-1-2 0-5 0-7 1 2 2 4 2 6l-1 1h0s2 0 2 1c1 0 2 2 3 3h0l-1 2z"></path><path d="M285 622h2c5 3 6 7 8 11v1c-2 0-2 0-3-2-2-3-3-6-6-8h0c-2-1-3-1-4-1 1 0 2 0 3-1z"></path></g><path d="M288 608l2-2c2 0 2 0 4 1l3 3v-1c2 1 3 2 4 4l1-1 4 5-2 1c0 1 1 4 0 5h0-2l1 1v2l1 1h0-2l-1 1v-1h-1v2c0 1 0 2-1 3h-3c0-1-1-2-1-4h0c-1-1-2-3-3-4l-2-3c-3-2-4-2-8-2l-1-1h-4c-2 0-4 0-6-1h-3 0c3-1 6 1 10-1h3c1-1 1-1 1-2h0c1 0 1 1 1 2l1-1h-1v-1c0-2 1-3 2-4h1-1 0v-1h1l2-1z" class="C"></path><path d="M296 617h0c2 1 2 2 3 3 0 3 1 5-1 7v1l-1-1c0-2 0-4 1-6h1c-1-1-3-2-3-3v-1zm-25 0h5 6c1 0 3 0 4 1s3 0 4 2c2 1 2 2 2 4l-2-3c-3-2-4-2-8-2l-1-1h-4c-2 0-4 0-6-1z" class="N"></path><path d="M288 608l2-2c2 0 2 0 4 1l3 3h1c-1 1-1 1-2 1l1 1-1 1s-1 0-1 1h-3l-1-1c1 0 1-1 2-1h1v-1c-1 0-2 0-2-1h0 2-1l2 1v-1-1h-1c-2-1-4-1-6-1z" class="G"></path><path d="M297 610v-1c2 1 3 2 4 4l1-1 4 5-2 1c0 1 1 4 0 5h0-2 0v-2l-3-3h0l-2-1c1-1 1-1 1-2l-3-1c0-1 1-1 1-1l1-1-1-1c1 0 1 0 2-1h-1z" class="T"></path><path d="M297 610v-1c2 1 3 2 4 4 0 1 1 2 2 4-2-2-4-4-6-5l-1-1c1 0 1 0 2-1h-1z" class="W"></path><path d="M302 612l4 5-2 1c0 1 1 4 0 5h0c0-2 0-5-1-6-1-2-2-3-2-4l1-1z" class="J"></path><path d="M211 590c3-3 6-4 11-3 2 0 3 1 4 3 2 2 3 5 4 7v3l3-2h2c4 0 9 0 12 3 1 1 1 2 2 3-2 1-2 2-4 2h-1c1-1 1-1 1-2-1-1-2-2-3-2-4 1-8 5-10 8-1 1-2 1-2 2v-4h0c0 1-1 4-2 5-1 2-1 5-2 7s-3 5-3 7c0 0 1 0 1 1-1 0-2 2-3 3l-3 3c-4 4-11 7-17 7-7 0-15-1-19-7-2-2-3-4-4-6-2-1-3-1-4-1s-3 1-3 2c-1 1-1 2 0 3 0 1 1 2 1 2l-1 1c-1-1-2-1-2-2-1-2-1-3-1-4 1-2 2-3 4-4s3-1 6-1h0c1-2 3-2 4-4 0 0 1-1 2-1h2c1-1 1-1 2-1 0 1-1 2-2 2-1 2-4 7-4 9 0 1 1 3 1 3 2 4 6 5 10 6 5 2 11 0 16-2-2 0-4-1-6-2h-1l2-1v-1c0-1 0-1 1-1 2-1 3-2 4-3l-2-5c-1-1 0-3 1-4h0c1-2 4-2 5-3l-4-4h0c-1 0-2 1-3 1l-1-1 1-1c1 0 1-1 2-2h0-1c-1 1-2 1-3 1h0c-1-1-2-1-2-1-1 0-2-1-3-1v-1h-1c-1 0-1 1-2 1v-2s3-3 3-4c4-4 7-9 10-13v2c1-1 1-1 2-1z" class="M"></path><path d="M178 624h0c1-2 3-2 4-4 0 0 1-1 2-1h2 0c-3 3-4 6-5 9-1-1-2-3-3-4z" class="L"></path><path d="M218 620v2l2-2h0v-3l2-1c-1 5-2 9-5 13v-2h0v-1c-1 1-1 1-2 1 1-1 2-2 2-4h0l1-3z" class="B"></path><path d="M211 624c1-2 4-4 6-4h1l-1 3h0c0 2-1 3-2 4 1 0 1 0 2-1v1h0v2c-3 2-6 4-10 4-1 1-3 1-4 1h-1l2-1c2 0 6-1 8-3v-3c0-1 0-2-1-3z" class="i"></path><path d="M211 624c1-2 4-4 6-4h1l-1 3h0c0 2-1 3-2 4h-1-1v-1c1 0 1-1 2-1h-2-1v2c0-1 0-2-1-3z" class="a"></path><path d="M208 619c1 0 2 0 3 1l-2 1v1l2 2c1 1 1 2 1 3v3c-2 2-6 3-8 3v-1c0-1 0-1 1-1 2-1 3-2 4-3l-2-5c-1-1 0-3 1-4h0z" class="R"></path><path d="M220 598c1 1 3 1 4 2v3 1c1 1 0 5 0 6v1l-2 5-2 1v3h0l-2 2v-2h-1c-2 0-5 2-6 4l-2-2v-1l2-1c-1-1-2-1-3-1 1-2 4-2 5-3l-4-4h0l1-1 5 4 2-1c2-1 4-3 5-5s1-3 0-5c0-2-2-2-3-3v-1l-1-1h1 1v-1z" class="E"></path><path d="M211 620h0c2-1 6-2 7-4 1-1 4-3 5-4l1-2v1l-2 5-2 1v3h0l-2 2v-2h-1c-2 0-5 2-6 4l-2-2v-1l2-1z" class="i"></path><path d="M214 601c2 0 3-1 5 0 1 1 3 1 3 3 1 2 1 3 0 5s-3 4-5 5l-2 1-5-4-1-1c1-1 2-3 2-4 1-2 2-4 3-5z" class="B"></path><path d="M213 607l1-2c1-1 2-1 3-2 1 1 2 2 1 4 0 2-1 4-2 5l-1 1h0c-1-1-2-2-2-3-1-1 0-2 0-3z" class="G"></path><path d="M215 613h0c-1-1-2-2-2-3-1-1 0-2 0-3 1 0 1 1 2 0h1c1 0 1 0 1 1-1 1-1 2-1 3v1l-1 1z" class="N"></path><path d="M217 603h3s0 1 1 1h1c1 2 1 3 0 5s-3 4-5 5h-3c1 0 1 0 1-1l1-1c1-1 2-3 2-5 1-2 0-3-1-4z" class="g"></path><path d="M235 598c4 0 9 0 12 3 1 1 1 2 2 3-2 1-2 2-4 2h-1c1-1 1-1 1-2-1-1-2-2-3-2-4 1-8 5-10 8-1 1-2 1-2 2v-4h0c0 1-1 4-2 5-1 2-1 5-2 7s-3 5-3 7c0 0 1 0 1 1-1 0-2 2-3 3l-3 3c0-1 0-3 1-4 2-3 5-6 6-10 2-5 3-9 5-14 0-1 0-2 1-4h0-1v-1-1l3-2h2z" class="F"></path><path d="M230 606c2-2 5-5 8-6h1c2 0 3 0 5 1h1 0v1h0c-3-1-5-1-7-1l-1 1h0c-3 1-6 4-7 6 0 1-1 4-2 5-1 2-1 5-2 7s-3 5-3 7c0 0 1 0 1 1-1 0-2 2-3 3l-3 3c0-1 0-3 1-4 2-3 5-6 6-10 2-5 3-9 5-14z" class="U"></path><path d="M211 590c3-3 6-4 11-3 2 0 3 1 4 3 2 2 3 5 4 7v3 1c-1 1-1 2-2 2h-1c-1 2-3 6-3 8v-1c0-1 1-5 0-6v-1-3c-1-1-3-1-4-2v1h-1-1l1 1v1c-2-1-3 0-5 0-1 1-2 3-3 5 0 1-1 3-2 4l1 1-1 1c-1 0-2 1-3 1l-1-1 1-1c1 0 1-1 2-2h0-1c-1 1-2 1-3 1h0c-1-1-2-1-2-1-1 0-2-1-3-1v-1h-1c-1 0-1 1-2 1v-2s3-3 3-4c4-4 7-9 10-13v2c1-1 1-1 2-1z" class="B"></path><path d="M199 607h1c1-1 3-1 4-1 0 2-1 3-2 3s-2-1-3-1v-1z" class="f"></path><path d="M214 600c0-1 1-1 1-2h5v1h-1-1l1 1v1c-2-1-3 0-5 0v-1z" class="F"></path><path d="M204 606s1-1 0-2c-1 0-1 0-2-1v-1c1-1 2-1 3-1 0 1 1 1 1 2 1 1 0 1 0 3h3c-2 1-3 3-5 4-1-1-2-1-2-1 1 0 2-1 2-3z" class="D"></path><path d="M209 606c2-2 4-4 5-6v1c-1 1-2 3-3 5 0 1-1 3-2 4l1 1-1 1c-1 0-2 1-3 1l-1-1 1-1c1 0 1-1 2-2h0-1c-1 1-2 1-3 1h0c2-1 3-3 5-4z" class="N"></path><path d="M211 590l1 1c0 1-1 2-2 3 0 1-1 3-1 3 0 1 1 3 1 4 1 0 0 0 0 1h-1c-1-1-3-2-4-4 2-3 3-5 6-8z" class="F"></path><path d="M225 590v1l1-1c2 2 3 5 4 7v3 1c-1 1-1 2-2 2h-1c0-1 1-2 1-3-1-1-1-1-1-2-2 0-4-1-6-2 1-1 1-1 1-2h1c0-2 1-2 2-4z" class="G"></path><path d="M225 590v1c2 3 3 6 3 9-1-1-1-1-1-2-2 0-4-1-6-2 1-1 1-1 1-2h1c0-2 1-2 2-4z" class="D"></path><path d="M223 594c1 0 2 2 3 3 0 1 1 1 1 1-2 0-4-1-6-2 1-1 1-1 1-2h1z" class="T"></path><path d="M211 590c3-3 6-4 11-3 2 0 3 1 4 3l-1 1v-1c-1 2-2 2-2 4h-1c0 1 0 1-1 2-1 0-1 0-1-1l-1 1c-1 0-1-1-1-1h-1c-1 0-1 0-2 1h-2v-1c-1 0-2 0-3-1 1-1 2-2 2-3l-1-1h0z" class="N"></path><path d="M219 591c0-1 0-1 1-2h0c2-1 3 0 5 1-1 2-2 2-2 4h-1-1c-1-1-1-2-2-3z" class="L"></path><path d="M215 596v-3c1-2 1-1 2-2v-1c1-1 1-1 2-1v2h0c1 1 1 2 2 3h1c0 1 0 1-1 2-1 0-1 0-1-1l-1 1c-1 0-1-1-1-1h-1c-1 0-1 0-2 1z" class="G"></path><path d="M271 583h0c2 1 3 3 4 5h-1l1 1c0 2-1 5 0 7v1 3l1 1v2c1 0 2 1 3 1s2 0 3 1 2 2 3 4h0v1h0 1-1c-1 1-2 2-2 4v1h1l-1 1c0-1 0-2-1-2h0c0 1 0 1-1 2h-3c-4 2-7 0-10 1h0 3c2 1 4 1 6 1h4l1 1v1l-1 1h-2c-1 0-1 0-2 1h0c-1-1-3-1-4-1 1 3 1 7 1 10 0 0-1 1-1 2h0c-1 0-2 1-3 1-2 2-4 4-7 5-8 2-19 3-28 0-4-2-8-5-10-9-1-1-1-1-1-2s-1-1-1-1c0-2 2-5 3-7s1-5 2-7c1-1 2-4 2-5h0v4c0-1 1-1 2-2 2-3 6-7 10-8 1 0 2 1 3 2 0 1 0 1-1 2h1c2 0 2-1 4-2h0 0c0 1 1 1 1 1l-3 2 1 1 3-1h2l1 1c1 1 7 1 8 1l2-2s1-1 2-1c0-1 1-2 1-3h2c0-1 1-1 1-2-1-1-2-2-2-3 1-1 1-3 1-4h3v-2c1-2 1-4 1-6-1-1-2-1-3-2h1l-1-1h1z" class="E"></path><path d="M252 637c1-1 2-1 3-1 1 1 1 1 2 1v1h-1c-2 1-2 0-4-1z" class="M"></path><path d="M255 624h2c0 1 0 1 1 2l-1-2c1-1 3-2 4-3 2 0 3 0 5 1h-1-2l-1 1 1 1h0c0 3 1 5-1 7-1 1-2 2-4 2 0 1-1 1-2 2h0v-1h-1c2-1 2-2 3-3v-3l-3-3v-1z" class="e"></path><path d="M258 633h0l1-1c1-2 2-2 2-4 0-1 0-3-1-3h-1 0v-1l4-2-1 1 1 1h0c0 3 1 5-1 7-1 1-2 2-4 2z" class="H"></path><path d="M266 622c2 1 3 2 4 5 0 2 0 5-2 6v1h2c-2 2-4 4-7 5 1-1 1-1 1-2h-3l-4 1v-1c2 0 5-1 6-3 2-2 3-5 2-7 0-2-1-2-2-3l-1-1 1-1h2 1z" class="f"></path><path d="M266 622c2 1 3 2 4 5 0 2 0 5-2 6v1h2c-2 2-4 4-7 5 1-1 1-1 1-2h-3c2-1 4-1 6-3s2-5 1-7c0-3-1-3-3-5h1z" class="E"></path><path d="M225 630v-2c1 1 1 2 2 3s3 2 4 3h0l3 2h1c0 1 0 1 1 1h2v1h1c1 0 2 0 3 1h0c3 0 7 0 10-2 2 1 2 2 4 1h1l4-1h3c0 1 0 1-1 2-8 2-19 3-28 0-4-2-8-5-10-9z" class="c"></path><path d="M268 617h3c2 1 4 1 6 1h4l1 1v1l-1 1h-2c-1 0-1 0-2 1h0c-1-1-3-1-4-1 1 3 1 7 1 10 0 0-1 1-1 2h0c-1 0-2 1-3 1h-2v-1c2-1 2-4 2-6-1-3-2-4-4-5s-3-1-5-1c-1 1-3 2-4 3l1 2c-1-1-1-1-1-2h-2l-1 1 4-4 1-1c0-1 0-1 1-2h0 4c1-1 3-1 4-1z" class="d"></path><path d="M273 621c-1 0-1-1-1-1h0 1c1-1 2-1 3-1h1c2-1 2-1 3-1 1 1 0 1 0 2h2l-1 1h-2c-1 0-1 0-2 1h0c-1-1-3-1-4-1z" class="b"></path><path d="M259 620c3-1 6-2 10 0 1 0 2 1 2 3h1c0 2 0 5-1 7 0 1-1 2-2 4l-1-1c2-1 2-4 2-6-1-3-2-4-4-5s-3-1-5-1c-1 1-3 2-4 3l1 2c-1-1-1-1-1-2h-2l-1 1 4-4 1-1z" class="L"></path><path d="M229 615l3 2v1c-1 0-2 1-2 3v1c1-1 1-2 2-2 1-1 5-1 6 0 3 2 5 4 6 7 1 1 2 1 3 2v-1h0c-1 0-1-1-1-1 1-1 2-1 2-2 1-1 1-1 2-1v-1c1-2 2-2 3-2 1 1 2 1 3 1v-2c1 0 2 0 2 1l-4 4 1-1v1l3 3v3c-1 1-1 2-3 3-6 2-12 2-18-1-4-1-7-3-8-7-2-4-1-7 0-11z" class="I"></path><path d="M250 624v-1c1-2 2-2 3-2 1 1 2 1 3 1v-2c1 0 2 0 2 1l-4 4v1l1 1h0-1-2c-1 0-1 0-1 1s1 2 0 4h0l-1 1c-1 0-2 0-3-1v-3-1h0c-1 0-1-1-1-1 1-1 2-1 2-2 1-1 1-1 2-1z" class="E"></path><path d="M246 627c1-1 2-1 2-2 1-1 1-1 2-1-1 1-1 2 0 4h-1l1 1v1c0 1 0 1 1 2l-1 1c-1 0-2 0-3-1v-3-1h0c-1 0-1-1-1-1z" class="S"></path><path d="M271 583h0c2 1 3 3 4 5h-1l1 1c0 2-1 5 0 7v1 3l1 1v2c1 0 2 1 3 1s2 0 3 1 2 2 3 4h0v1h0 1-1c-1 1-2 2-2 4v1h1l-1 1c0-1 0-2-1-2h0c0 1 0 1-1 2h-3c-4 2-7 0-10 1h0c-1 0-3 0-4 1h-4 0c-1 1-1 1-1 2l-1 1c0-1-1-1-2-1v2c-1 0-2 0-3-1-1 0-2 0-3 2v1c-1 0-1 0-2 1 0 1-1 1-2 2 0 0 0 1 1 1h0v1c-1-1-2-1-3-2-1-3-3-5-6-7-1-1-5-1-6 0-1 0-1 1-2 2v-1c0-2 1-3 2-3v-1l-3-2c1-2 2-3 3-5 2-3 6-7 10-8 1 0 2 1 3 2 0 1 0 1-1 2h1c2 0 2-1 4-2h0 0c0 1 1 1 1 1l-3 2 1 1 3-1h2l1 1c1 1 7 1 8 1l2-2s1-1 2-1c0-1 1-2 1-3h2c0-1 1-1 1-2-1-1-2-2-2-3 1-1 1-3 1-4h3v-2c1-2 1-4 1-6-1-1-2-1-3-2h1l-1-1h1z" class="X"></path><path d="M247 614c1-1 3-1 4-1l1 1h-4l-3 3-1-1c1-1 2-2 3-2z" class="c"></path><path d="M270 601c-1-1-2-2-2-3 1-1 1-3 1-4h3l-1 1c1 0 1 1 1 1 1 0 1-1 2-1v1c0 1 0 2-1 3 1 1 1 3 1 4-1 0-2 0-3 1h-2c-1 0-2 1-3 2 0-1 1-2 1-3h2c0-1 1-1 1-2z" class="C"></path><path d="M270 601l1-1v-4h1c1 2 1 4 1 6l-4 2c-1 0-2 1-3 2 0-1 1-2 1-3h2c0-1 1-1 1-2z" class="K"></path><path d="M274 605h2 2l2 2v5c-1 1-2 3-4 3h-4l-1-1c0-1 0-1 1-2h0v-1-1h0l1 1v2h-1c1 1 2 1 2 0v-1c-1-1 0-4-1-6h0s1 0 1-1z" class="S"></path><path d="M232 610c2-3 6-7 10-8 1 0 2 1 3 2 0 1 0 1-1 2h1c2 0 2-1 4-2h0 0c0 1 1 1 1 1l-3 2 1 1 3-1h2l1 1c-1 1-3 1-4 1s-3 3-4 4h-2v1c1 0 2-1 3 0-1 0-2 1-3 2l1 1 3-3 1 1c-2 2-4 2-5 6v3l1-1 1-2c0-1 1-2 2-3h1v1h-1c-1 1-2 2-2 4v3h0v1s0 1 1 1h0v1c-1-1-2-1-3-2-1-3-3-5-6-7-1-1-5-1-6 0-1 0-1 1-2 2v-1c0-2 1-3 2-3v-1l-3-2c1-2 2-3 3-5z" class="N"></path><path d="M235 615h2l1 2h-1 0-2v-2z" class="e"></path><path d="M249 604h0 0c0 1 1 1 1 1l-3 2c-3 4-4 7-4 12l-3-6c1-2 1-3 1-4l2-1-1-1c0-1 0-1 1-2h0c0-1 1-1 2-1 0 1 0 1-1 2h1c2 0 2-1 4-2z" class="B"></path><path d="M232 610c2-3 6-7 10-8 1 0 2 1 3 2-1 0-2 0-2 1h0c-1 1-1 1-1 2l1 1-2 1c0 1 0 2-1 4l-1-1c-1-1-2 0-4 0l-1 2c0 1 0 1 1 1v2l-3 1v-1l-3-2c1-2 2-3 3-5z" class="i"></path><path d="M243 605c-1 1-1 1-1 2l1 1-2 1c-1-1-1-1-2-1 0 1-1 1-2 1 2-2 4-3 6-4z" class="b"></path><path d="M235 612l1-1c0-1 0-2 1-2s2 0 2-1c1 0 1 0 2 1 0 1 0 2-1 4l-1-1c-1-1-2 0-4 0z" class="H"></path><path d="M227 514h0v6h1v-4-1s0-1 1-1v4c0 2 1 5 2 6 1 0 1 1 2 1 2 1 6 2 7 3s5 3 6 5l-1 1 1 1h4c2 1 4 2 5 3l5 3h0l1 2c1 1 2 1 2 2s1 3 0 4h0 0l2 3s0 1-1 1l3 3c1 0 2 1 2 2h1l2 5c-1-1 0-2 0-3v-3h1 0 1c1 1 1 2 1 4h0c0 1 0 2 1 3v-2 5c1 2 0 4 1 7l1 2c0 2-2 2 0 4v3l-1 2c-1 0-1 1-1 2-1 0 0 0-1 1-1-2-2-4-4-5h0-1l1 1h-1c1 1 2 1 3 2 0 2 0 4-1 6v2h-3c0 1 0 3-1 4 0 1 1 2 2 3 0 1-1 1-1 2h-2c0 1-1 2-1 3-1 0-2 1-2 1l-2 2c-1 0-7 0-8-1l-1-1h-2l-3 1-1-1 3-2s-1 0-1-1h0 0c-1-1-1-2-2-3-3-3-8-3-12-3h-2l-3 2v-3c-1-2-2-5-4-7-1-2-2-3-4-3-5-1-8 0-11 3-1 0-1 0-2 1v-2c0-2 3-6 4-7 4-10 7-19 9-29l5-39z" class="S"></path><path d="M254 563c1 1 2 1 3 2l1 2v2c-1-2-3-3-4-4v-2z" class="C"></path><path d="M243 571c2 1 4 4 4 6l1 2h0v1h0c-3-3-4-5-5-9z" class="B"></path><path d="M245 597c1-1 1-2 1-2v-1c-1-1-2-2-3-4l1 1c1 1 2 2 3 2h0v-1c-1-1-1-4-1-5l4 10c0 1 1 1 2 2v1c-2 0-5-2-7-3z" class="e"></path><path d="M254 576l-1-2v-1c-1-2-6-4-8-5s-3-1-4-3h1l6 3c2 1 3 2 4 3 2 1 4 1 5 3h0c1 2 3 3 3 6h0c-1 0-1 0-2-1 0-1-1-1-2-2v1l-2-2z" class="R"></path><path d="M248 580c1 2 2 4 4 6 0 1 1 2 1 3s2 2 2 2h1v-1l1 2h1v2 1l-2-1h1c0 1 0 1-1 2l-1 2h-1c0 1 0 1-1 2h-1v-1c-1-1-2-1-2-2 1 0 2 1 3 1 0-1 0-1-1-2h0l-1-1v-1l-1-1v-1h0c0-1-1-2 0-3 1 0 1 0 2 1v-1c-1-1-1-2-1-3l-3-6h0z" class="N"></path><path d="M251 586c2 3 3 7 4 11-1-2-3-3-4-5v-1l1 5-1-1v-1l-1-1v-1h0c0-1-1-2 0-3 1 0 1 0 2 1v-1c-1-1-1-2-1-3z" class="R"></path><path d="M233 592l1-1c0-2-1-4 0-6v-1l1-3 1-1h1l-1 1c0 1 0 3 1 4 0 0 0-1 1-1h0c0 2 1 4 1 6 0 3 2 4 2 6-1 0-2-1-2-1h-2c-1 0-1-2-2-1l-1 2s0 1 1 2h-2v-1c0-2-2-1-3-2 0-1 0-1 1-1v-1-2l1 1h1z" class="X"></path><path d="M233 592h1 1l-1-1v-2h1 0c1 2 2 4 4 6h-2c-1 0-1-2-2-1l-1 2s0 1 1 2h-2v-1c0-2-2-1-3-2 0-1 0-1 1-1v-1-2l1 1h1z" class="h"></path><path d="M243 571c0-1-1-1-1-2h0c2 1 2 0 3 0 2 1 2 4 4 5s5 5 6 6l1 1c0-1 0-2-1-2 0-1-1-2-1-3l2 2 2 3c-1 1-1 1-2 1h-1 0c0 1 1 3 1 4v1h0 0v2 1 1h-1s-2-1-2-2-1-2-1-3c-2-2-3-4-4-6v-1h0l-1-2c0-2-2-5-4-6z" class="M"></path><path d="M253 589c1 0 2 0 2 1v-1-1c0-2-2-4-2-7v-2c1 0 2 2 2 3h0c0 1 1 3 1 4v1h0 0v2 1 1h-1s-2-1-2-2z" class="E"></path><path d="M257 574l2 1h1l-1-3c-1-2-6-5-8-5l-1-1c2 0 5 3 7 3h1v-2c1 0 1 2 2 2 0 1 1 2 1 4h0c0 2 1 3 1 4s1 1 2 2c1 0 1 0 1 1h1v1 2c1-1 1-1 3-1 1 0 1 0 2 1h-1l1 1h-1l-2 1-3 3v2h-1v1l-3 1c1-1 1-1 1-2h-1v-1h-1 0v2c-1 1-1 0-2 0h0v1h-1l-1-2v-1-2h0 0v-1c0-1-1-3-1-4h0 1c1 0 1 0 2-1l-2-3v-1c1 1 2 1 2 2 1 1 1 1 2 1h0c0-3-2-4-3-6h0z" class="N"></path><path d="M256 589v-1c1 0 2 1 2 2v1h0v1h-1l-1-2v-1zm4 0h-1c-1-1-2-2-2-3v-1h1 2v-2l-1-1 1-1 1 2v3c1 1 1 2 1 4h-1v-1h-1 0z" class="d"></path><path d="M262 577c0 1 1 1 2 2 1 0 1 0 1 1h1v1 2c1-1 1-1 3-1 1 0 1 0 2 1h-1l1 1h-1l-2 1-3 3v2h-1v1l-3 1c1-1 1-1 1-2 0-2 0-3-1-4v-3h1v-6z" class="B"></path><path d="M264 579c1 0 1 0 1 1h1v1c-1 1-2 1-2 1h-1c-1-1-1-2-1-3 1 0 1 0 1 1h1v-1z" class="L"></path><path d="M266 583c1-1 1-1 3-1 1 0 1 0 2 1h-1l1 1h-1l-2 1-3 3v2h-1c0-3 0-5 2-7z" class="C"></path><path d="M270 584c1 1 2 1 3 2 0 2 0 4-1 6v2h-3c0 1 0 3-1 4 0 1 1 2 2 3 0 1-1 1-1 2h-2c0 1-1 2-1 3-1 0-2 1-2 1l-2 2c-1 0-7 0-8-1l-1-1h-2l-3 1-1-1 3-2s-1 0-1-1h0 0c-1-1-1-2-2-3-3-3-8-3-12-3-1-1-1-2-1-2l1-2c1-1 1 1 2 1h2s1 1 2 1c0-2-2-3-2-6 1 3 4 5 6 7 2 1 5 3 7 3h1c1-1 1-1 1-2h1l1-2c1-1 1-1 1-2h-1l2 1v-1-2-1h0c1 0 1 1 2 0v-2h0 1v1h1c0 1 0 1-1 2l3-1v-1h1v-2l3-3 2-1z" class="i"></path><path d="M265 588c1 1 2 1 3 2v2h-1-1c0-1-1-1-2-1v-1h1v-2z" class="K"></path><path d="M260 589h0 1v1h1c0 1 0 1-1 2h0v3c0 1-1 1-1 1v1l-2 2-3 2c-1 0-2 0-3-1h1c1-1 1-1 1-2h1l1-2c1-1 1-1 1-2h-1l2 1v-1-2-1h0c1 0 1 1 2 0v-2z" class="U"></path><path d="M258 603c3 0 3 1 5 2v2h1l-2 2c-1 0-7 0-8-1l-1-1h-2l-3 1-1-1 3-2 2-1c1 0 2-1 4-1 1 1 2 1 3 3h0c1 1 2 2 3 2v-1l-2-1c-1-1-1-2-2-3z" class="E"></path><path d="M252 604c1 0 2-1 4-1l1 2h-2v1h2v1h-3c-1-1-2-2-2-3z" class="F"></path><path d="M270 584c1 1 2 1 3 2 0 2 0 4-1 6v2h-3c0 1 0 3-1 4 0 1 1 2 2 3 0 1-1 1-1 2h-2c0 1-1 2-1 3-1 0-2 1-2 1h-1v-2c-2-1-2-2-5-2l1-2c0-1 0-1 1-1 3-2 5-6 8-7h1c1-1 2-1 2-2v-4c-1-1-2-2-3-2l2-1z" class="G"></path><path d="M227 514h0v6h1v-4-1s0-1 1-1v4c0 2 1 5 2 6 1 0 1 1 2 1 2 1 6 2 7 3v1 1 1l-3 3-1-1c-1-1-2-1-4-1l-1-1c-1 1-1 2-1 4l2 1v1h-1c-1 1-1 2-1 4h1l-1 1 1 1v1 2c1 0 1 1 2 2l1 1h0l3 3h0v-2c0-3 0-3 1-5l1 1v1c0 1-1 2-1 3l1 1v1c1-1 1-1 2-1v1c1-2 2-3 2-4 2 1 2 1 3 1l1-1h1c1 1 2 1 3 2l-2 2c-1 1-2 2-2 3v1c1 1 2 1 3 2 1 0 1 0 2-1 0 0 3 1 3 2v1h2v1l2 1v1c-1 1-1 2-2 2-1-1-2-1-3-2-6-4-14-5-21-8-1-1-3-2-3-3-1-4-2-7-2-10 0-4 0-7-1-11-1 8-1 15 1 22l-1 1v2l-1-1h-1c-2 0-2-1-3-2l5-39z" class="i"></path><path d="M231 524c1 0 1 1 2 1 2 1 6 2 7 3v1 1 1l-3 3-1-1c-1-1-2-1-4-1 2-1 5-1 6-2-2-1-4-1-6-3-1-1-1-2-1-3z" class="G"></path><path d="M238 545l1 1v1c0 1-1 2-1 3l1 1v1c1-1 1-1 2-1v1l1 1c0 1 0 1 1 2h-1s-1-1-2-1c-2 0-5-1-6-3-2-1-3-3-3-5 1 0 1 1 2 2l1 1h0l3 3h0v-2c0-3 0-3 1-5z" class="W"></path><path d="M239 552c1-1 1-1 2-1v1l1 1h-1-1l-1-1z" class="G"></path><path d="M240 528c1 1 5 3 6 5l-1 1 1 1h4c2 1 4 2 5 3l5 3h0l1 2c1 1 2 1 2 2s1 3 0 4h0 0l-2-1-1 1c-2-1-6 0-8-1l-1 1v1c-1-1-2-1-3-2h-1l-1 1c-1 0-1 0-3-1 0 1-1 2-2 4v-1c-1 0-1 0-2 1v-1l-1-1c0-1 1-2 1-3v-1l-1-1c-1 2-1 2-1 5v2h0l-3-3h0l-1-1c-1-1-1-2-2-2v-2-1l-1-1 1-1h-1c0-2 0-3 1-4h1v-1l-2-1c0-2 0-3 1-4l1 1c2 0 3 0 4 1l1 1 3-3v-1-1-1z" class="E"></path><path d="M231 544h3v1 2h0s1 0 1 1l-1 1-1-1c-1-1-1-2-2-2v-2z" class="M"></path><path d="M240 528c1 1 5 3 6 5-3 1-6 1-9 1l3-3v-1-1-1z" class="L"></path><path d="M253 539h-3s0-1-1-1c0 0-1 0-1-1 1 0 3 0 4 1h3 0l5 3h0l1 2v1c-1-1-2-1-2-1h-1 0c-2-1-3-1-6-1-1 0-1 0-2-1h3 1 3v-1l-2-1h-2zm-7-4h4c-1 1-2 1-3 2-2 0-3 1-5 2l-1 1h-1l2-2h-1l-2 1h-1s-1 0-1 1h-1c0 1-1 1-1 2l-1-1c1 0 1 0 1-1h0l-1-1h0v-1h-1v-1l1-1h5l1 1c1 0 3-1 4-1 1-1 1-1 2-1z" class="N"></path><path d="M253 539h2l2 1v1h-3-1-3c1 1 1 1 2 1-4 1-6 3-9 6 0 1-1 2-2 4v-1c-1 0-1 0-2 1v-1l-1-1c0-1 1-2 1-3v-1l-1-1c1-1 2-1 3-2 2-1 4-3 7-3h0l2-1h3z" class="X"></path><path d="M250 541c1 1 1 1 2 1-4 1-6 3-9 6 0 1-1 2-2 4v-1c-1 0-1 0-2 1v-1l-1-1c0-1 1-2 1-3v-1l-1-1c1-1 2-1 3-2-1 1-2 3-2 4s1 1 1 1h1l1-1c2-3 5-4 8-6z" class="T"></path><path d="M252 542c3 0 4 0 6 1h0 1s1 0 2 1v-1c1 1 2 1 2 2s1 3 0 4h0 0l-2-1-1 1c-2-1-6 0-8-1l-1 1v1c-1-1-2-1-3-2h-1l-1 1c-1 0-1 0-3-1 3-3 5-5 9-6z" class="B"></path><path d="M258 543h1s1 0 2 1v-1c1 1 2 1 2 2s1 3 0 4h0c0-2-3-5-5-6z" class="T"></path><path d="M248 548v-1c1-1 3-1 4-2v3l-1 1v1c-1-1-2-1-3-2z" class="G"></path><path d="M252 545l9 3-1 1c-2-1-6 0-8-1v-3z" class="E"></path><path d="M222 553c1 1 1 2 3 2h1l1 1v-2l1-1 2 6c0 1 0 1 1 2 0 1 1 2 1 3 1 3 1 8 0 11 0 1-1 2-1 3v5c0 1 0 3 1 4v3 2l-1-1v2 1c-1 0-1 0-1 1 1 1 3 0 3 2v1l-3 2v-3c-1-2-2-5-4-7-1-2-2-3-4-3-5-1-8 0-11 3-1 0-1 0-2 1v-2c0-2 3-6 4-7 4-10 7-19 9-29z" class="n"></path><path d="M225 555h1l1 1c0 1 1 2 1 3-1 1-2 2-2 3-2 4-4 8-4 12-1 1-1 2-1 2h-1l-1-1c0-1 1-2 2-4 1-3 2-7 4-9l1-1c-1-1-2-1-3-2h1 2v-1c-1-1-1-1-2-1h0l1-2z" class="E"></path><path d="M220 576h1s0-1 1-2c0 4 0 8 2 11v1c-1 0-2-1-3-1h-1c-1-1-1-1-2-1l-1 1v-1l1-1v-1-1-1l-1 1c-1 0-1 0-1-1h0c1-1 1-1 2-1h0v-1h-1v1h-1v-1c1 0 1 0 1-1h1s1 0 1-1h1z" class="T"></path><path d="M227 556v-2l1-1 2 6c0 2-2 4-3 6s-2 4-3 7l1 1c-1 0-1 0-1 1-1 3 0 6 1 9-1 1-1 1-1 2h0c-2-3-2-7-2-11s2-8 4-12c0-1 1-2 2-3 0-1-1-2-1-3z" class="n"></path><path d="M217 581l1-1v1 1 1l-1 1v1l1-1c1 0 1 0 2 1h1c1 0 2 1 3 1v-1h0c0-1 0-1 1-2 1 3 2 5 4 7l2 1v2 1c-1 0-1 0-1 1 1 1 3 0 3 2v1l-3 2v-3c-1-2-2-5-4-7-1-2-2-3-4-3-5-1-8 0-11 3-1 0-1 0-2 1v-2c0-2 3-6 4-7 1 1 1 1 2 1l2-2z" class="f"></path><path d="M230 559c0 1 0 1 1 2 0 1 1 2 1 3 1 3 1 8 0 11 0 1-1 2-1 3v5c0 1 0 3 1 4v3 2l-1-1-2-1c-2-2-3-4-4-7s-2-6-1-9c0-1 0-1 1-1l-1-1c1-3 2-5 3-7s3-4 3-6z" class="S"></path><path d="M230 559c0 1 0 1 1 2 0 1 1 2 1 3 1 3 1 8 0 11 0 1-1 2-1 3v5c0 1 0 3 1 4v3 2l-1-1-2-1 1-1v-9c0-4 3-10 1-14 0-1 0-1-1-1 0 1 0 3-1 4v1c1 3-1 12-3 13h0v-1c1-2 1-4 2-7 0-1 1-2 0-3v-1-2c-1 0-1 0 0-1l1-1c0-1-1-1-2-2 1-2 3-4 3-6z" class="C"></path><path d="M261 548l2 1 2 3s0 1-1 1l3 3c1 0 2 1 2 2h1l2 5c-1-1 0-2 0-3v-3h1 0 1c1 1 1 2 1 4h0c0 1 0 2 1 3v-2 5c1 2 0 4 1 7l1 2c0 2-2 2 0 4v3l-1 2c-1 0-1 1-1 2-1 0 0 0-1 1-1-2-2-4-4-5h0c-1-1-1-1-2-1-2 0-2 0-3 1v-2-1h-1c0-1 0-1-1-1-1-1-2-1-2-2s-1-2-1-4h0c0-2-1-3-1-4-1 0-1-2-2-2l-1-2c1 0 1-1 2-2v-1l-2-1v-1h-2v-1c0-1-3-2-3-2-1 1-1 1-2 1-1-1-2-1-3-2v-1c0-1 1-2 2-3l2-2v-1l1-1c2 1 6 0 8 1l1-1z" class="Q"></path><path d="M265 565c1-1 0-3 0-4h1l1 1v4l-2-1z" class="W"></path><path d="M257 565c1 0 1-1 2-2 0 2 0 2 2 2h2v1l-1 1h-2v1 1c-1 0-1-2-2-2l-1-2z" class="D"></path><path d="M258 557l2-1v1 1h-1v1h0c1 0 2 0 2 1l1 4h1c-1 1-2 1-2 1-2 0-2 0-2-2v-1l-2-1v-1h-2v-1h1c1 0 1 0 2-1v-1z" class="e"></path><path d="M258 557c-1 0-2-1-3-3 1-1 2-2 3-2 2-1 4 0 6 1l3 3c1 3 1 7 1 10h-1v-4h0l-1-1v-1-2c-1 0-2-2-3-2h0c0 1 1 2 1 3h-2l-2-2v-1l-2 1z" class="D"></path><defs><linearGradient id="y" x1="261.323" y1="575.055" x2="269.043" y2="570.153" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#373738"></stop></linearGradient></defs><path fill="url(#y)" d="M267 562h0v4h1v4l-2 4c-1 2-1 4-1 6 0-1 0-1-1-1-1-1-2-1-2-2s-1-2-1-4h0c0-2-1-3-1-4v-1-1h2v1h2v1l1-4 2 1v-4z"></path><path d="M260 569v-1-1h2v1h2v1h0-3-1z" class="L"></path><path d="M265 565l2 1c0 2-2 6-3 7h-1c0-1 0-3 1-4h0l1-4z" class="a"></path><path d="M261 548l2 1 2 3s0 1-1 1c-2-1-4-2-6-1-1 0-2 1-3 2 1 2 2 3 3 3v1c-1 1-1 1-2 1h-1c0-1-3-2-3-2-1 1-1 1-2 1-1-1-2-1-3-2v-1c0-1 1-2 2-3l2-2v-1l1-1c2 1 6 0 8 1l1-1z" class="C"></path><path d="M247 555c0-1 1-2 2-3 0 1 0 2 1 3l2-1c0-1 0-1 1-1h0c0 2 0 3-1 4s-1 1-2 1c-1-1-2-1-3-2v-1z" class="a"></path><path d="M247 555c0-1 1-2 2-3 0 1 0 2 1 3h-1v1c-1 0-1 0-2-1z" class="d"></path><path d="M252 548c2 1 6 0 8 1v1l-1 1c-2 0-4 1-6 2h0c-1-1-2-2-2-4l1-1z" class="M"></path><path d="M267 556c1 0 2 1 2 2h1l2 5c-1-1 0-2 0-3v-3h1 0 1c1 1 1 2 1 4h0c0 1 0 2 1 3v-2 5c1 2 0 4 1 7l1 2c0 2-2 2 0 4v3l-1 2c-1 0-1 1-1 2-1 0 0 0-1 1-1-2-2-4-4-5h0c-1-1-1-1-2-1-2 0-2 0-3 1v-2-1h-1c0-2 0-4 1-6l2-4v-4c0-3 0-7-1-10z" class="E"></path><path d="M267 556c1 0 2 1 2 2h1l2 5-1 12v1-5l-1-1c0 1 0 1-1 1l-1-1v-4c0-3 0-7-1-10z" class="T"></path><path d="M265 580c0-2 0-4 1-6l2-4 1 1c1 0 1 0 1-1l1 1v5c0 1 0 5 1 6h0l-1 1h0c-1-1-1-1-2-1-2 0-2 0-3 1v-2-1h-1z" class="j"></path><path d="M267 578h2v1 1h-2v-1-1z" class="W"></path><path d="M273 557h0 1c1 1 1 2 1 4h0c0 1 0 2 1 3v-2 5c1 2 0 4 1 7l1 2c0 2-2 2 0 4v3l-1 2c-1 0-1 1-1 2-1 0 0 0-1 1-1-2-2-4-4-5l1-1c1 1 1 1 2 1v-1c0-3 0-5-1-8v-6c0-3 0-6-1-8v-3h1z" class="a"></path><path d="M273 557h0 1c1 1 1 2 1 4h0c0 1 0 2 1 3v3h-1v-1h-1v1h-1v1c0-3 0-6-1-8v-3h1z" class="W"></path><path d="M273 557h1c1 1 1 2 1 4h0 0l-1 1c0-1-1-3-1-5z" class="a"></path><defs><linearGradient id="z" x1="272.33" y1="579.909" x2="278.756" y2="576.507" xlink:href="#B"><stop offset="0" stop-color="#929190"></stop><stop offset="1" stop-color="#aba9ac"></stop></linearGradient></defs><path fill="url(#z)" d="M276 567c1 2 0 4 1 7l1 2c0 2-2 2 0 4v3l-1 2c-1 0-1 1-1 2-1 0 0 0-1 1-1-2-2-4-4-5l1-1c1 1 1 1 2 1v-1l1 1h0l1-16z"></path><path d="M318 515h0c1 2 0 3 1 4l2 43v4l5 14 5 10c-1 1-1 1-1 3l-1 1h-1 1c1 1 1 3 2 4-1 1-3 0-4 0h-1c1 2 1 3 2 4v1s0 1 1 2c-3-1-4 0-7 0 0 1-1 1-1 2l-2-2-1-1c-1 2-1 4-1 6h1c-1 0-1 0-2 1h0v2c1 0 2 1 3 1v2h-1v1c1 0 1 0 2 1v1h0l-1 1c0 1 1 1 1 2h-1c-1-1-1-1-3-1-1 1-2 1-4 1-3-1-5-3-6-5l-4-5-1 1c-1-2-2-3-4-4v1l-3-3c-2-1-2-1-4-1l-2 2-2 1h-1 0c-1-2-2-3-3-4s-2-1-3-1-2-1-3-1v-2l-1-1v-3-1c-1-2 0-5 0-7l-1-1h1c1-1 0-1 1-1 0-1 0-2 1-2l1-2v-3c-2-2 0-2 0-4l-1-2c-1-3 0-5-1-7v-5s0-1 1-2c0-1 1-1 2-2v-1c1-2 1-3 2-5 1-3 3-6 5-8l2-3 1-2 1-1c1-1 2-1 3-2 2-1 3-3 5-4l1-1h-1c5-3 9-5 12-10h0v-1l1-1h0 1c3-1 4-1 6-4z" class="M"></path><path d="M293 536c1 0 1 1 1 2l-1 1v1l-1 1h0s-1 0-2 1l-2 2v-1-1-1l1-2 1-1c1-1 2-1 3-2z" class="F"></path><path d="M291 546l1-1c1-1 2-1 3-1 0 1 1 1 2 1h1 1l1 1h1l-1 2v1h-1-1c-1-1-1-2-3-2h-4v-1h0z" class="Z"></path><path d="M285 551l2-3 2-2h2 0v1h4c2 0 2 1 3 2h1-1-2l-1 1 1 1c1 1 1 2 2 2l1 1h-1c-1-1-2-1-3-2l-2-1c-2 0-4-1-7 0h-1z" class="P"></path><path d="M298 549c-3 0-6 0-9-1v-1l2-1v1h4c2 0 2 1 3 2z" class="X"></path><path d="M305 538c0 1 1 1 2 1h0v1h-2c0 1 0 1 1 2 1 0 3 1 4 1h2l1 2c1 2 1 5 1 7l-5-6c-3-3-4-5-9-5-3-1-5 0-8 0h0l1-1c2 0 5-1 7-2h5z" class="D"></path><path d="M289 554c1 0 1 1 2 2l-1 1c-1 3-2 5 0 8v-1l2 2v1h2v1h0 2v-1c1 1 1 2 2 2h2c-1 1-1 1-2 1h0l1 1h4c-2 1-4 1-6 1v-1h-2l-1-1c-1 0-2-1-2-1-1-1-1-1-3-1 2 2 5 4 8 4l2 1c1 0 2 0 4 1h-2c-5 0-9-2-12-6-2-2-2-5-3-8h1c1-1 1-2 1-3l-1-1h0 2v-2z" class="G"></path><path d="M290 565v-1l2 2v1h2v1h0 2v-1c1 1 1 2 2 2h2c-1 1-1 1-2 1h0l1 1h-3c-2-1-5-4-6-6z" class="I"></path><path d="M295 552c1 1 2 1 3 2h1l1 1c0 1 1 2 0 3 0 0 1 1 1 2v2h1c0 1 0 1-1 2h0l-1 1c-2 0-1-1-2-1h-2v3 1h-2 0v-1h-2v-1l-2-2v1c-2-3-1-5 0-8l1-1c-1-1-1-2-2-2v-1h4c0-1 1-1 2-1z" class="D"></path><path d="M290 557c0 2 0 3 1 4 0 2 1 3 1 4 1 1 1 2 2 2h-2v-1l-2-2v1c-2-3-1-5 0-8z" class="Q"></path><path d="M295 552c1 1 2 1 3 2s1 2 0 3l-1 1c-1 0-1 0-2-1s-1-2-2-4h0c0-1 1-1 2-1z" class="E"></path><path d="M300 558s1 1 1 2v2h1c0 1 0 1-1 2h0l-1 1c-2 0-1-1-2-1h-2v-1-1c-1 1-1 1-2 1v-1h1v-1h-1c-1-1-1-1-1-2 1 0 3 2 4 2l3-3z" class="C"></path><path d="M301 560v2h0c-1 1-1 1-3 2v-1l3-3z" class="S"></path><path d="M300 549h0c2-1 4-1 5-3 2 2 3 4 5 6 2 3 3 5 3 9 0 3-1 5-4 7-2 2-4 3-6 3h-4l-1-1h0c1 0 1 0 2-1h-2c-1 0-1-1-2-2v-3h2c1 0 0 1 2 1l1-1h0c1-1 1-1 1-2h-1v-2c0-1-1-2-1-2 1-1 0-2 0-3l-1-1-1-1c-1 0-1-1-2-2l-1-1 1-1h2 1 1z" class="D"></path><path d="M296 551h9l4 4c1 0 1 1 1 1v1c-1-1-2-2-3-2l-3-3h-1v1 1l-1 1h0l-1-1-1 1-1-1-1-1c-1 0-1-1-2-2z" class="O"></path><path d="M300 555l1-1 1 1h0l1-1h1v2c1 0 2 0 2 1l3 3s1 1 1 2c0 2 0 3-1 4s-2 1-3 2c-1-1-2-1-3-2l-3 3h-2c-1 0-1-1-2-2v-3h2c1 0 0 1 2 1l1-1h0c1-1 1-1 1-2h-1v-2c0-1-1-2-1-2 1-1 0-2 0-3z" class="R"></path><path d="M306 557l3 3s1 1 1 2h-2-2v1h0 2c0 1 0 1 1 1l-1 1-1-1h0-2v-1-2-1h-1v-1h2v-2z" class="G"></path><path d="M300 555l1-1 1 1h0l1-1h1v2c1 0 2 0 2 1v2h-2v1h1l-3 1v1h-1v-2c0-1-1-2-1-2 1-1 0-2 0-3z" class="F"></path><defs><linearGradient id="AA" x1="317.471" y1="515.825" x2="309.165" y2="563.699" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292929"></stop></linearGradient></defs><path fill="url(#AA)" d="M318 515h0c1 2 0 3 1 4l2 43v4l-1 1v2c0-1-1-1-2-1v-3-1h-2c1-4 0-8 0-13 0-2 1-5 0-6v-7h0c-1 1-1 2-2 3-1 0-2-1-2-2l-1 1h0l1 3h-2c-1 0-3-1-4-1-1-1-1-1-1-2h2v-1h0c-1 0-2 0-2-1h-5c-2 1-5 2-7 2v-1l1-1c0-1 0-2-1-2 2-1 3-3 5-4l1-1h-1c5-3 9-5 12-10h0v-1l1-1h0 1c3-1 4-1 6-4z"></path><path d="M321 562v4l-1 1v2c0-1-1-1-2-1v-3l1 1c1-1 1-1 1-2v-2h1z" class="J"></path><path d="M314 521h2c0 1 1 2 1 3l-1 1c-1-1-1 0-3-1h0v1c1 0 1 0 2 1 0-1 0-1 1-1v2h-2c-2 0-3-1-4-2 1-2 1-2 2-3 1 0 2 1 3 1 0-1-1-1-1-2z" class="V"></path><path d="M307 529c3 0 6 1 9 0h1v3c-1 2-3 2-5 3v-1-2c-1-1-3-1-5-3z" class="R"></path><path d="M299 531c3-1 5-2 8-2 2 2 4 2 5 3v2 1c-1 0-2 1-3 1h-1c0 2 0 3-1 3h0c-1 0-2 0-2-1h-5c-2 1-5 2-7 2v-1l1-1c0-1 0-2-1-2 2-1 3-3 5-4l1-1z" class="c"></path><path d="M303 531h1c1 1 2 1 2 2l-2 1-1 1-1-1c0-1 0-2 1-3z" class="O"></path><path d="M300 538s0-1-1-2h1c1-1 1 0 2 0h4 3-1c0 2 0 3-1 3h0c-1 0-2 0-2-1h-5z" class="X"></path><path d="M308 536c0 2 0 3-1 3h0c-1 0-2 0-2-1l3-2z" class="B"></path><defs><linearGradient id="AB" x1="279.276" y1="567.821" x2="292.636" y2="574.495" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#353533"></stop></linearGradient></defs><path fill="url(#AB)" d="M280 557l3-6h2 1c3-1 5 0 7 0l2 1c-1 0-2 0-2 1h-4v1 2h-2 0l1 1c0 1 0 2-1 3h-1c1 3 1 6 3 8 3 4 7 6 12 6h2l1-1 1 1v1c-4 3-5 6-7 11h-2c-1 2-1 4-2 7h0 0c0 1 0 4-1 5h0l-1-1h0l2 6h-1l1 3v1c-2-1-2-1-4-1l-2 2-2 1h-1 0c-1-2-2-3-3-4s-2-1-3-1-2-1-3-1v-2l-1-1v-3-1c-1-2 0-5 0-7l-1-1h1c1-1 0-1 1-1 0-1 0-2 1-2l1-2v-3c-2-2 0-2 0-4l-1-2c-1-3 0-5-1-7v-5s0-1 1-2c0-1 1-1 2-2v-1h1z"></path><path d="M278 583c1-1 3-2 4-2 2 1 4 1 5 2v1h0-1 0l1 1v1 1h-1c0-2-1-2-2-3-1 0-1-1-2-1s-1 1-2 2l-1-2-2 2 1-2z" class="G"></path><path d="M285 563v-2l1-1c1 3 1 6 3 8l-2 2h-1c1 1 2 3 3 4h0c1 1 3 2 3 3-1 0-1 1-1 1h0-1l-1 1-1 1c-1 0-1-1-2-1v-1h0-2l-1-2v-4c0-1-1-2-1-4s1-3 3-5z" class="T"></path><path d="M285 570h1c1 1 2 3 3 4-1 0-1 0-1 1-1-1-1-1-2-1v-2l-1-1h0v-1h0z" class="E"></path><path d="M284 578c0-1 0-2 1-2h2c1 0 1 1 1 2h2l-1 1-1 1c-1 0-1-1-2-1v-1h0-2z" class="F"></path><path d="M285 563v-2l1-1c1 3 1 6 3 8l-2 2h-1-1l1-1-1-1h-1l1-1v-1-3z" class="H"></path><path d="M280 557l3-6h2 1c3-1 5 0 7 0l2 1c-1 0-2 0-2 1h-4v1 2h-2 0l-5 5c-3 6-3 12-4 19-2-2 0-2 0-4l-1-2c-1-3 0-5-1-7v-5s0-1 1-2c0-1 1-1 2-2v-1h1z" class="T"></path><path d="M277 560c0-1 1-1 2-2v-1h1c0 1 0 1-1 2v1c-1 2-1 4-1 5l-1 2h0v7c-1-3 0-5-1-7v-5s0-1 1-2z" class="N"></path><path d="M286 551c3-1 5 0 7 0l2 1c-1 0-2 0-2 1h-4v1 2h-2 0l-5 5c0-2 0-2-1-3 0 0 0-1 1-1l1-3c1-2 2-2 3-3h0z" class="F"></path><path d="M286 551c3-1 5 0 7 0l2 1c-1 0-2 0-2 1h-4c-2 1-3 2-5 3h0c1-1 2-3 3-3s2-1 2-1v-1h-3 0z" class="N"></path><path d="M289 568c3 4 7 6 12 6h2l1-1 1 1v1c-4 3-5 6-7 11h-2c-1 2-1 4-2 7v-1h0l-2-5c0-1 0-1-1-1l-3-6 1-1 1-1h1 0s0-1 1-1c0-1-2-2-3-3h0c-1-1-2-3-3-4h1l2-2z" class="D"></path><path d="M289 574c0-1 0-1 1-1h3c1 1 1 1 1 2v1l-1 1v1l-1-1c0-1-2-2-3-3z" class="B"></path><path d="M291 578s0-1 1-1l1 1h0c2 1 3 3 4 5 0 1 0 2-1 3-1 2-1 4-2 7v-1h0l-2-5c0-1 0-1-1-1l-3-6 1-1 1-1h1 0z" class="X"></path><path d="M291 578s0-1 1-1l1 1h0v2l-1 1c0-1-1-2-1-3z" class="c"></path><path d="M280 585c1-1 1-2 2-2s1 1 2 1c1 1 2 1 2 3h1v-1-1l-1-1h0 1 0c2 0 2 1 3 2h1c1 0 1 0 1 1l2 5h0v1h0 0c0 1 0 4-1 5h0l-1-1h0l2 6h-1l1 3v1c-2-1-2-1-4-1l-2 2-2 1h-1 0c-1-2-2-3-3-4s-2-1-3-1-2-1-3-1v-2l-1-1v-3-1c-1-2 0-5 0-7l-1-1h1c1-1 0-1 1-1 0-1 0-2 1-2l2-2 1 2z" class="G"></path><path d="M285 608c0-2 1-4 3-5h1 1v1h-1c-1 0-2 2-3 3v1h-1z" class="E"></path><path d="M285 594l3 3c0 1-1 1-1 1-1 0-2-1-2 0-1 0-1 1-2 2 0-2 0-2 1-3v-1c1 0 1 0 1-2z" class="h"></path><path d="M288 597c2 2 4 4 5 6l1 3v1c-2-1-2-1-4-1l-2 2-2 1h-1 0v-1h1l6-3c-1-2-2-4-4-5 0-1-1-1-1-2h0s1 0 1-1z" class="J"></path><path d="M285 591v1l3 1c1 1 0 2 1 3h1l1-1c0 1 0 2 1 2l2 6h-1c-1-2-3-4-5-6l-3-3-2-2s1-1 2-1z" class="E"></path><path d="M280 585c1-1 1-2 2-2s1 1 2 1c1 1 2 1 2 3h1c0 3 2 5 4 8l-1 1h-1c-1-1 0-2-1-3l-3-1v-1-1c0-2-2-3-4-4l-1-1z" class="X"></path><path d="M283 592l2 2c0 2 0 2-1 2v1c-1 1-1 1-1 3s-2 1-1 4v1c-1-1-2-1-3-1s-2-1-3-1v-2h0s1-1 1-2c1 0 1 1 2 0 1 0 2-3 2-4l1-2 1-1z" class="L"></path><path d="M284 596v1c-1 1-1 1-1 3s-2 1-1 4v1c-1-1-2-1-3-1 1-1 1-1 2-1-1-1-1-2-1-2 1-2 2-3 3-4v-1h1z" class="e"></path><defs><linearGradient id="AC" x1="293.827" y1="594.426" x2="284.799" y2="584.516" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#b4b2b2"></stop></linearGradient></defs><path fill="url(#AC)" d="M287 587v-1-1l-1-1h0 1 0c2 0 2 1 3 2h1c1 0 1 0 1 1l2 5h0v1h0 0c0 1 0 4-1 5h0l-1-1h0c-1 0-1-1-1-2-2-3-4-5-4-8z"></path><path d="M277 585l2-2 1 2 1 1c2 1 4 2 4 4v1c-1 0-2 1-2 1l-1 1-1 2c0 1-1 4-2 4-1 1-1 0-2 0 0 1-1 2-1 2h0l-1-1v-3-1c-1-2 0-5 0-7l-1-1h1c1-1 0-1 1-1 0-1 0-2 1-2z" class="C"></path><path d="M281 586c2 1 4 2 4 4v1c-1 0-2 1-2 1l-1 1-1 2c0-1-1-2-2-2h-1c-1-2-1-3 0-5s2-2 3-2z" class="B"></path><defs><linearGradient id="AD" x1="310.171" y1="595.77" x2="303.537" y2="576.64" xlink:href="#B"><stop offset="0" stop-color="#aaa9ab"></stop><stop offset="1" stop-color="#cbc9ca"></stop></linearGradient></defs><path fill="url(#AD)" d="M316 564h2v1 3c1 0 2 0 2 1v-2l1-1 5 14 5 10c-1 1-1 1-1 3l-1 1h-1 1c1 1 1 3 2 4-1 1-3 0-4 0h-1c1 2 1 3 2 4v1s0 1 1 2c-3-1-4 0-7 0 0 1-1 1-1 2l-2-2-1-1c-1 2-1 4-1 6h1c-1 0-1 0-2 1h0v2c1 0 2 1 3 1v2h-1v1c1 0 1 0 2 1v1h0l-1 1c0 1 1 1 1 2h-1c-1-1-1-1-3-1-1 1-2 1-4 1-3-1-5-3-6-5l-4-5-1 1c-1-2-2-3-4-4v1l-3-3v-1l-1-3h1l-2-6h0l1 1h0c1-1 1-4 1-5h0 0c1-3 1-5 2-7h2c2-5 3-8 7-11v-1l-1-1c2 0 4-1 6-2 3-2 4-4 6-6v-1z"></path><path d="M296 586h2c-1 3-2 6-2 9h0-1 0l-1-2h0c1-3 1-5 2-7z" class="V"></path><path d="M310 571c0 1 0 1-1 2h0v3c0 1-1 1-2 2h-1c0-1 0-2-1-3v-1l-1-1c2 0 4-1 6-2z" class="I"></path><path d="M311 582l1 1c-3 1-6 3-8 5-2 1-2 3-3 5-1 1-1 2-2 3v1l-1-1 1-2c1-4 3-7 7-8 1-1 3-2 4-3l1-1z" class="W"></path><path d="M294 593l1 2h0 1 0c0 3 0 6 1 9 1 1 1 2 1 3h-1v2 1l-3-3v-1l-1-3h1l-2-6h0l1 1h0c1-1 1-4 1-5h0z" class="l"></path><path d="M298 596l1 1v-1c1 3 0 6 1 9l1 1v-3l1 2h0c1 0 1-1 2-2l1 1c-1 1-2 2-2 3-1 1 1 3 1 4v1l-2-1v1h0l-1 1c-1-2-2-3-4-4v-2h1 1c1-1-1-6-1-8v-3z" class="U"></path><path d="M297 609v-2h1c1 2 2 4 4 5h0l-1 1c-1-2-2-3-4-4z" class="j"></path><path d="M316 564h2v1 3c1 0 2 0 2 1v-2l1-1 5 14 5 10c-1 1-1 1-1 3l-2-4c0-1-1-3-1-4-2-1-4-2-5-4h-2c-2 0-3-1-5 1-1 0-1 0-2 1h-1l-1-1c1-1 3-3 5-4 1-1 3-2 4-3-3-1-6 0-8 0-1 0-2-2-3-2 1-1 1-1 1-2 3-2 4-4 6-6v-1z" class="B"></path><path d="M315 570c1 0 3 0 4 1 1 0 1 0 1 1h-1l-1 1c-1 0-2-1-4-2l1-1z" class="E"></path><path d="M316 564h2v1 3c-1 0-1 1-1 2h-1v-1-4h0v-1z" class="L"></path><path d="M321 566l5 14c-1-1-2-2-3-2h-1v-1h1l1-1c-2-2-2-5-4-7v-2l1-1z" class="O"></path><path d="M316 578h0 2 0v1l1 1c1 0 1-1 2-1s1 1 1 2h-2c-2 0-3-1-5 1-1 0-1 0-2 1h-1l-1-1c1-1 3-3 5-4z" class="J"></path><path d="M313 583c1-1 1-1 2-1 2-2 3-1 5-1h2c1 2 3 3 5 4 0 1 1 3 1 4l2 4-1 1h-1 1c1 1 1 3 2 4-1 1-3 0-4 0h0c0-1-1-1-1-1-1 0-2-1-3-1 0-2-1-3-3-5h0c-2-1-4-1-6-1h-2v1c0 1 1 1 2 2h0l-1 1c-2-2-2-3-4-3s-3 0-5 1c-2 3-3 6-3 9v2 3l-1-1c-1-3 0-6-1-9 1-1 1-2 2-3 1-2 1-4 3-5 2-2 5-4 8-5h1z" class="C"></path><path d="M304 588c2 0 2 0 4-1h3c1-1 3 0 5 0 1 0 1 0 2 1l2 1h-3c-1-1-2-1-2-1h-6 0c-1 0-1 1-2 1l-3 2c-1 0-1 0-1 1h-1l-1 1c1-2 1-4 3-5z" class="T"></path><path d="M312 583h1v2c1 1 3 1 4 0h1v3c-1-1-1-1-2-1-2 0-4-1-5 0h-3c-2 1-2 1-4 1 2-2 5-4 8-5z" class="C"></path><path d="M313 583c1-1 1-1 2-1 2-2 3-1 5-1h2c1 2 3 3 5 4 0 1 1 3 1 4l2 4-1 1h-1 1c1 1 1 3 2 4-1 1-3 0-4 0h0c0-1-1-1-1-1-1 0-2-1-3-1 0-2-1-3-3-5h1 1c1 0 2 0 3 1h1l1 1h0v-1c-1-1-2-1-3-2-2-1-2-1-4-1l-2-1v-3h-1c-1 1-3 1-4 0v-2z" class="M"></path><path d="M320 581h2c1 2 3 3 5 4 0 1 1 3 1 4-1 0-1 0-2-1s-1-1-3-2h0v-1c1 0 1 0 1-1s-2-1-3-2l-1-1z" class="d"></path><path d="M314 593h0c-1-1-2-1-2-2v-1h2c2 0 4 0 6 1h0c2 2 3 3 3 5 1 0 2 1 3 1 0 0 1 0 1 1h0-1c1 2 1 3 2 4v1s0 1 1 2c-3-1-4 0-7 0 0 1-1 1-1 2l-2-2-1-1c-1 2-1 4-1 6h1c-1 0-1 0-2 1h0v2c1 0 2 1 3 1v2h-1v1c1 0 1 0 2 1v1h0l-1 1c0 1 1 1 1 2h-1c-1-1-1-1-3-1-1 1-2 1-4 1-3-1-5-3-6-5l-4-5h0v-1l2 1v-1c0-1-2-3-1-4 0-1 1-2 2-3l-1-1c-1 1-1 2-2 2h0l-1-2v-2c0-3 1-6 3-9 2-1 3-1 5-1s2 1 4 3l1-1z" class="R"></path><path d="M317 598l1 1 1 1c-1 1-1 1-2 1h0-2c1 1 1 1 1 2-1-1-2 0-3-1v-1c2-2 2-2 4-2v-1z" class="C"></path><path d="M310 603c1 1 4 2 4 3s-1 2-2 2h-1-1c-1 0-2 0-3-1h2c1 0 1-1 1-1v-3h0z" class="L"></path><path d="M306 607h1c1 1 2 1 3 1v4c0 1 0 1 1 1 1-1 1-2 1-3l-1-1h1 1v3c1 1 2 4 4 5 1-1 1-1 1-2 0 0-1-1-1-2h-1c1 0 2 1 3 1v2h-1v1c1 0 1 0 2 1v1h0l-1 1c0 1 1 1 1 2h-1c-1-1-1-1-3-1 0-2 0-2-2-3-1-1-4-3-5-5 0-1-1-2-2-3s-1-1-1-2v-1z" class="E"></path><path d="M306 607h1c1 1 2 1 3 1v4l-2-2v-1c-1-1-1-1-2-1v-1z" class="F"></path><path d="M302 612v-1l2 1v-1c0-1-2-3-1-4 0-1 1-2 2-3l1 3v1c0 1 0 1 1 2s2 2 2 3c1 2 4 4 5 5 2 1 2 1 2 3-1 1-2 1-4 1-3-1-5-3-6-5l-4-5h0z" class="R"></path><path d="M301 603v-2c0-3 1-6 3-9 2-1 3-1 5-1s2 1 4 3h-1c-1 1-3 2-4 3v2l1 3c1-1 1-1 2-1l-1 2h0v3s0 1-1 1h-2-1l-1-3-1-1c-1 1-1 2-2 2h0l-1-2z" class="B"></path><path d="M304 603h-1v-2c1-1 1-1 2-1 0 1 0 1 1 1v-1h0c1 0 1 0 2 1-1 0-1 0-1 1s0 1 1 2h1v-1h1 0v3s0 1-1 1h-2-1l-1-3-1-1z" class="f"></path><path d="M314 593h0c-1-1-2-1-2-2v-1h2c2 0 4 0 6 1h0c2 2 3 3 3 5 1 0 2 1 3 1 0 0 1 0 1 1h0-1c1 2 1 3 2 4v1s0 1 1 2c-3-1-4 0-7 0 0 1-1 1-1 2l-2-2-1-1-2-1c0-1 0-1-1-2h2 0c1 0 1 0 2-1l-1-1-1-1c-1-1-1-2-1-4h0c-1 0-1 0-2-1h0z" class="B"></path><path d="M323 596c1 0 2 1 3 1 0 0 1 0 1 1h0-1c1 2 1 3 2 4v1h-1c-2-1-3-5-4-7z" class="N"></path><path d="M314 593h0c-1-1-2-1-2-2v-1h2c0 1 1 2 2 2 1 1 3 2 4 4 0 0 0 1 1 2h-1c-1 0-1 1-2 0v1l-1-1c-1-1-1-2-1-4h0c-1 0-1 0-2-1h0z" class="U"></path><path d="M229 236c0 1 0 2 1 2 2 2 6 2 8 4v1h0 1s1 0 2-1l1 2 4 2c4 2 8 1 13-1 2-1 5-3 7-3h1 0 1l1 3v2l1 1c0 1 1 1 1 3h0v1h0c1-1 1-1 1-2v-3l2-2v-1 4h1v-1c1 0 1-1 1-1 0-2 1-4 2-6 1 0 2 0 3-1 1 0 3-1 5-1l4 4 2 2c1 0 2 0 3 2v-1h1 2l2 3 1 1 2 3-2 3h3l1-1h1l-1 1v2h1s0-1 1-1l1-3c0-1 1-2 1-4 0 0 0-1 1-1h0c0 1 0 2 1 3h1v1l-1 1c1 1 1 1 2 1h0l4 2h1v3h-1 1c-2 1-2 2-2 4l1 1v1c1 1 1 2 1 3v2c-1 1-2 4-4 4-3 1-6 0-9 1l1 1h0c5 1 7 1 10 5 1 2 2 4 2 7v3c1 1 1 3 1 5v4 39 72c-1-2-1-4-1-6l-1-1-1 1c1 3 1 5 1 8 0 9-2 16-4 25-1 2-1 4-2 5h0l-1 1h0c0 1-1 2-1 2v1c-1 0-1 1-1 1 0 1 0 1-1 1 0 1 0 2-1 2l-1 1v1c-1 0-1 1-1 1-1 1-1 3-2 4s-1 1-3 2v-1c0 1 0 1-1 1h-2-7l-1-1s1-1 1-2v-1l-3 4h-2c0-1 1-2 1-3s1-2 0-3v-1c1 0 1-1 2-1v-1h-2-1c-2 1-3 0-5 1l1 1h-1l-4-7v-3h0v-2h-1v-5l-1 1h0l-1-2v-1l-1 1h0l-1 1 1 2-1 1c-1 0-1 0-2-1h-1-2c-1 0-1 1-1 1 0-1 0-1-1-2h0v-1-3c0-1-2-2-2-2l-2-2s-2 1-3 1c0 0-2-1-3-2l-1 1-5-4c-3-3-5-7-6-11v-1l-1-2h-2-2 0c1 0 1 0 2-1l-2-2c-2-2-3-5-4-8h-1c0 1-1 2-1 3v-1s0-1-1-1v-5-18h2c0-2 0-3-1-4h0c-1-5 2-13 4-17 0 0-1 1-2 1v-1h0c0 1-1 1-1 2v2h0-2v-1-4c1-1 1 0 2 0 1-2 0-3-1-5l-1-2v-6-30-10-3-9h0c-1-1 0-3 0-5v-9c0-1 0-3-1-5h0 1c0-3-1-6 0-9v-1-1h0 0v-1-19-5z" class="M"></path><path d="M268 325l1-1c0 1 0 1 1 1 0 1 0 2-1 3-1-1-1-2-1-3z" class="U"></path><path d="M258 320h1c-1 1-2 2-2 4h-1 0v-2l2-2z" class="S"></path><path d="M278 295c0-1 0-2 1-3s1 0 2 0c0 1-1 2-3 3z" class="E"></path><path d="M252 280c1 0 1 0 2 1-2 1 0 5-2 6-1-1 0-4 0-5s-1-1 0-2z" class="C"></path><path d="M247 294c1 1 1 2 2 3 0 1 0 1-1 2h0c-1-2-2-3-3-4 1 0 1-1 1-1h1z" class="d"></path><path d="M273 259c1 1 1 2 1 3 0 2 0 3-1 4h-1v-2c0-1 0-1 1-1-1-2-1-3 0-4z" class="Z"></path><path d="M253 390l1-1v1 2h-1v3h-2l-1 1-1 1 1-1-1-1 2-1c0-1 0-1 1-2v-1h1v-1z" class="G"></path><path d="M252 299h1c0-2-1-2 0-3 1 0 1 0 2 1 0 1-1 2-2 3 0 2-1 3-2 4 0-2 1-3 1-5z" class="a"></path><path d="M244 377h-1c1-3 5-6 7-9v1l-2 2 1 1-1 1c-1 1-2 1-2 2 1 0 1 0 2 1h-2c-2 0-2 0-2 1z" class="P"></path><path d="M248 309h1c0 2 0 4-1 6v1c-1-2-2-3-3-4l3-3z" class="H"></path><path d="M282 291v1l3 3-2 1c-2 0-4-1-6 1l1-2c2-1 3-2 3-3l1-1z" class="B"></path><path d="M270 319c1 0 1 2 1 4v2 5h-1s-1-1-1-2c1-1 1-2 1-3s-1-2-1-4h1v-2z" class="W"></path><path d="M245 384h-1c-1 0-2 1-3 2v-1c0-2 0-6 2-7l1 2 2 3c0 1-1 1-1 1z" class="B"></path><path d="M268 266h1v2h1 2c0 2-1 3 1 5v2h-2v-1s0-1-1-1c0-1 1-1 1-2v-1-1c-1 0-2 0-2 1-1-1-1-2-2-2h-1l1-1s1 0 1-1h0z" class="F"></path><path d="M274 304v-3c1-2 1-3 3-4h2v1h-1c-1 1-1 2-2 3 0 1 0 1 1 2l-1 1-1 3v-2l-1-1z" class="C"></path><path d="M263 314c2 3 5 5 6 7 0 2 1 3 1 4-1 0-1 0-1-1l-1 1v-1c-1-2-1-3-3-5h1 0c0-1-2-2-2-3-1 0-1-1-1-2z" class="N"></path><path d="M247 276c1 0 2 1 3 1l2 1c2 0 2 1 3 1l1 1h-1c0 1 1 1 1 1l1 1v1h0c-1 0-1-1-2-1l-1-1c-1-1-1-1-2-1-1-1-1-1-1-2l-2 2c-1-1-2-3-3-4h0 1z" class="d"></path><path d="M246 383c1 2 2 3 2 5 1 1 1 3 1 4-1 1-2 1-3 1h0v-3c0-2 0-3-1-5v-1s1 0 1-1z" class="J"></path><path d="M254 309c0 2-1 4 0 5l-1 1v1 10c-1-1-1-3-1-4l-1-1c-1-3 0-5 1-7v-4h1l1-1z" class="C"></path><path d="M274 304l1 1v2 8h1c0 3-1 6-2 9v1-2h-1v-5c1-2 1-3 1-4v-10z" class="N"></path><path d="M255 309h1v-2h0c2 0 3 2 4 4 1 1 1 2 2 3 0 1 1 1 1 1-1 1-1 1-1 2 0 0 0 1-1 1 0 1-1 1-2 1l2-1c0-3-5-7-6-9z" class="E"></path><path d="M241 283c0-2 1-4 2-5l1 1h1c1 1 3 1 4 2v1c-1 0-2 0-2 1l-1 1h1v1h-2c-1 1-1 2-1 3v-1c0-1 0-2-1-3l1-1h0-3z" class="h"></path><path d="M248 300h2c1-1 2-1 2-1 0 2-1 3-1 5 0 1-1 2-2 2h-1l-1 1v1l-3-3v-1c1 0 2 0 3-1s1-2 1-3z" class="F"></path><path d="M252 339c2-7 5-13 11-19h0c1 1 1 2 0 2-1 2-2 3-3 5-2 4-6 8-7 13l-1-1z" class="h"></path><path d="M241 283h3 0l-1 1c1 1 1 2 1 3v1l2 2c0 1 1 1 1 2v2h-1s0 1-1 1l-3-2-2-2c1 0 1 0 2-1-1-2-2-4-1-7z" class="H"></path><path d="M243 284c1 1 1 2 1 3v1h0l-1 1c0-2-1-2-1-4l1-1z" class="B"></path><path d="M242 290l4 4s0 1-1 1l-3-2-2-2c1 0 1 0 2-1z" class="G"></path><path d="M243 307c0-1 0-1 1-2l3 3 1 1-3 3c1 1 2 2 3 4v-1l1 6c0 1-1 2-1 3-2-5-4-10-5-16v-1z" class="E"></path><path d="M243 307c0-1 0-1 1-2l3 3 1 1-3 3c0-2-1-3-2-5z" class="J"></path><path d="M263 306l2 1c2 3 4 8 5 12v2h-1c-1-2-4-4-6-7 1-1 0-2 0-3v-5z" class="I"></path><path d="M254 346c1-6 6-10 8-16 0-1 1-3 1-4 2 2 3 2 4 5l-2 1h0c-1 2-3 3-4 5l-2 1-3 5c-1 1-1 2-2 3z" class="i"></path><path d="M284 279l1-1v3h2l-1 1v3l-3 6h0l-1 1v-1h0c0-1-1-1-1-2-1 0-1 1-2 1h0-1c1-1 1-2 2-2 1-1 3-2 3-3h-2 0v-2l-1-1c1-1 3-2 4-3z" class="R"></path><path d="M284 279l1-1v3h2l-1 1v3l-3 6h0l-1 1v-1h0c1-3 3-9 2-12z" class="H"></path><path d="M242 293l3 2c1 1 2 2 3 4h0v1c0 1 0 2-1 3s-2 1-3 1-1-1-2-2v-1c0-1-1-1-1-1l1-2c-1 0-1-1-1-2 1-1 0-1 0-1 0-1 1-2 1-2z" class="D"></path><path d="M242 301h1c1-1 0-2 0-3h1v1 2c1 1 1 1 2 1 0 0 0-1 1-1h1v-2h0v1c0 1 0 2-1 3s-2 1-3 1-1-1-2-2v-1z" class="W"></path><path d="M254 309h0 1c1 2 6 6 6 9l-2 1-1 1-2 2h-2l-1 6h0v-2-10-1l1-1c-1-1 0-3 0-5z" class="D"></path><path d="M254 322c1-2 2-3 3-5h0 0c1 0 2 1 3 1h1l-2 1-1 1-2 2h-2z" class="d"></path><path d="M254 346c1-1 1-2 2-3l1 1h2c0 1 1 1 1 1l-2 1 1 3c-2 2-3 4-5 7-1 2-1 5-3 6h0v-8c0-2 1-4 2-6 0-1 0-1 1-2z" class="B"></path><path d="M254 346c1-1 1-2 2-3l1 1h2c0 1 1 1 1 1l-2 1c-1 1-3 2-4 2h-1c0-1 0-1 1-2z" class="F"></path><path d="M276 304c1 1 2 1 2 2 2 1 3 3 4 4s0 3 0 4c-1 2-3 4-4 6-2 2-3 4-4 7h0v-2-1c1-3 2-6 2-9h-1v-8l1-3z" class="D"></path><path d="M276 304c1 1 2 1 2 2l-2 9h-1v-8l1-3z" class="M"></path><path d="M248 266h2c1 2 3 3 4 4s2 3 3 4l2 2h0l2 3c3 1 4 1 6 3h-2c0 1 0 2-1 3l-1-2v-1l-3 2c1 0 1 1 1 2 1 1 1 2 2 3h-2v-1c-1 0-1-1-1-2l-3-3h-1l1 4 1 1s1 0 1 1v1h-1l-3-3v-5c1 0 1 1 2 1h0v-1l-1-1s-1 0-1-1h1c1 1 3 3 4 3v-1c-2-4-5-8-9-12l-3-4z" class="C"></path><path d="M243 308c1 6 3 11 5 16 1 4 1 8 1 13h-1v-1c-1 0-2-1-3-2l-1-5c1-1 1-2 2-3-2-2-4-3-5-6h-1v-1c1-1 3 0 4 0h0c-1-4-1-6-2-10l1-1z" class="I"></path><path d="M246 326c1 4 1 7 2 10-1 0-2-1-3-2l-1-5c1-1 1-2 2-3z" class="E"></path><path d="M244 319c2 2 3 5 3 7l-6-6h0-1v-1c1-1 3 0 4 0h0z" class="C"></path><path d="M246 266h2l3 4c4 4 7 8 9 12v1c-1 0-3-2-4-3l-1-1c-1 0-1-1-3-1l-2-1c-1 0-2-1-3-1h-1c-1-1-2 0-3-1h1v-2h1 1v-1c1 0 1-1 1-1h1l-2-2-1-1 1-2z" class="J"></path><path d="M252 274c0 1 1 2 1 3l-1 1-2-1c1-1 1-2 2-3z" class="B"></path><path d="M250 274l1-1h0l1 1c-1 1-1 2-2 3-1 0-2-1-3-1v-1c1 0 2-1 3-1z" class="D"></path><path d="M243 275h1v-2h1 1l1 1h0c1-1 1-1 2-1l1 1c-1 0-2 1-3 1v1h-1c-1-1-2 0-3-1z" class="O"></path><path d="M246 266h2l3 4s0 1-1 1-1-1-2 0c1 1 1 1 2 1l-1 1c-1 0-1 0-2 1h0l-1-1v-1c1 0 1-1 1-1h1l-2-2-1-1 1-2z" class="h"></path><path d="M279 259l2 3c2 1 4 3 6 4h2v1 1c-3 2-4 5-7 7 0 1 0 1-1 1v-1c-1 0-1 0-1-1l1-3v-1h-1l1-1v-1c-1 0-1-1-2-2-1 1-1 1-1 2-1-1-1-2-2-3 1-2 0-4 1-5h1l1-1z" class="B"></path><path d="M279 259l2 3c2 1 4 3 6 4h2v1h-5c-2-1-5-4-6-7l1-1zm-30 136l1 1-1 1-3 6s0 1 1 2c0 0-1 1-1 2 0 2-1 5 1 7h0l-1 3h-1c0 1 1 1 1 2h0v2l1-1v-3-1l1-2c1-1 2-3 3-3h1c-1 2-3 3-3 5 0 3-1 7 2 10 0 1 1 1 2 2h0 0l-1 1-5-4c-3-3-5-7-6-11 2 1 2 2 2 4l1 2h0v-3l-1-1v-3c0-4 0-6 1-10 1-1 1-1 1-2 1-1 2-3 3-4h0c1-1 1-1 1-2z" class="N"></path><path d="M246 403s0 1 1 2c0 0-1 1-1 2 0 2-1 5 1 7h0l-1 3h-1v-2c-1-4-1-8 1-12z" class="X"></path><path d="M263 264h1c1 1 0 2 1 3 0 1 0 0 1 1-1 0-2 1-2 2 1 1 2 2 2 3h-2c-1 1-1 1-1 2v1h-4 0l-2-2c-1-1-2-3-3-4s-3-2-4-4c0-1 0-1-1-1l1-1 4 1c1 0 3 0 5 1h0c1 0 3-1 4-2h0z" class="B"></path><path d="M259 270l-1-1v-1l4-1v1l1 2h-1c-1 0-1 0-2-1l-1 1z" class="I"></path><path d="M254 265c1 0 3 0 5 1h0c1 0 3-1 4-2 0 1 0 2-1 3l-4 1v1l1 1c1 1 2 2 2 3l-2 2v1l-2-2h1c1-1 1-1 2-1v-1c-2-2-3-4-6-5v-2z" class="k"></path><path d="M250 264l4 1v2c3 1 4 3 6 5v1c-1 0-1 0-2 1h-1c-1-1-2-3-3-4s-3-2-4-4c0-1 0-1-1-1l1-1z" class="H"></path><path d="M250 374c2 0 4-1 6-1v2l-1 1s-1 0-1 1c-1 0-1 1-1 2h0c-1 4 1 7 1 10l-1 1c-1 0-1 0-2-1-1 0-1 0-1-1h-1 0-1c0-2-1-3-2-5l-2-3 1-1v-2h-1c0-1 0-1 2-1h2c-1-1-1-1-2-1 0-1 1-1 2-2 0 1 1 1 2 1z" class="C"></path><path d="M246 376h2l1 1v2l-2-1-1-2z" class="O"></path><path d="M247 378l2 1 1 2c1 0 1 2 1 3s1 2 2 3h0v1h-1c-1 0-2-1-2-1v-1c-1-1-1-2-1-3s-2-3-3-4l1-1z" class="S"></path><path d="M250 374c2 0 4-1 6-1v2l-1 1s-1 0-1 1c-1 0-1 1-1 2h0 0l-3 2-1-2v-2l-1-1c-1-1-1-1-2-1 0-1 1-1 2-2 0 1 1 1 2 1z" class="B"></path><path d="M249 377h1c1 0 2-1 4-2h2l-1 1s-1 0-1 1c-1 0-1 1-1 2h0 0l-3 2-1-2v-2z" class="J"></path><path d="M241 300s1 0 1 1v1c1 1 1 2 2 2v1c-1 1-1 1-1 2v1l-1 1c1 4 1 6 2 10h0c-1 0-3-1-4 0v1 1h0l-2-1v-2s0-1 1-1h0 0c0-1-1-2-1-3 0 0 0-1-1-2 0 1 0 1-1 1 0-1 0-1-1-2v-1c-1-1-1-2-2-3l-2-1 1-1 1 1c0-1 1-2 1-2v-2c1 1 1 2 2 2l1-1h0l1-1c1-1 2-2 3-2z" class="S"></path><path d="M242 302c1 1 1 2 2 2v1c-1 1-1 1-1 2v1l-1 1v-1-3-3z" class="P"></path><path d="M234 302c1 1 1 2 2 2l1-1h0v4l-2-1v1c-1 0-2-1-2-1 0-1 1-2 1-2v-2z" class="i"></path><path d="M253 395v-3h1c1 1 2 1 2 2v1h1l1 1c-1 0-1 1-1 2h0 1l-1-1h2c2 0 3-1 4-2h0 2c0 1 0 1-1 1v1h-1s-1 1-2 1l-2 2-4 3-6 6c-1 2-2 3-2 5-2-2-1-5-1-7 0-1 1-2 1-2-1-1-1-2-1-2l3-6 1-1 1-1h2z" class="S"></path><path d="M253 395v-3h1c1 1 2 1 2 2v1h0c-1 1-2 1-3 1v-1z" class="K"></path><defs><linearGradient id="AE" x1="230.716" y1="401.844" x2="241.717" y2="390.914" xlink:href="#B"><stop offset="0" stop-color="#a3a0a5"></stop><stop offset="1" stop-color="#b7b7b4"></stop></linearGradient></defs><path fill="url(#AE)" d="M233 389c0-3 0-6-1-8v-1c2 1 2 3 4 3 1 1 1 4 1 5 0 4 1 7 1 11 1 3 1 6 2 9 0 1 0 3 1 4v1l-1-2h-2-2 0c1 0 1 0 2-1l-2-2c-2-2-3-5-4-8s-1-6 0-9c1-1 1-1 1-2h0z"></path><path d="M233 389c1 2 1 5 1 8 1 3 3 6 3 10l-1 1c-2-2-3-5-4-8s-1-6 0-9c1-1 1-1 1-2h0z" class="N"></path><path d="M242 366l1-1v3 1h0v2l-4 4-2-1-1 4v5c-2 0-2-2-4-3v1c1 2 1 5 1 8h0c0 1 0 1-1 2-1 3-1 6 0 9h-1c0 1-1 2-1 3v-1s0-1-1-1v-5-18h2c0-2 0-3-1-4l1-3c1-1 1-2 2-2h1c2 2 2 2 4 1s3-2 4-4z" class="D"></path><path d="M242 366l1-1v3 1h0v2l-4 4-2-1-1 4c-1 1-2 1-3 1v-2c1-3 1-5 1-8 2 2 2 2 4 1s3-2 4-4z" class="L"></path><path d="M298 266c1-1 2-2 3-2 2-1 4-1 6-1h3 0l4 4-1 1h-2c-4-1-11 1-14 2l-2 1c-4 3-6 5-8 10h-2v-3l-1 1c-1 1-3 2-4 3-2 2-4 5-5 7v2-1-1c0-3 1-6 3-7 1-1 3-2 5-3-2-2-2 0-4-1v-1h2v-1c1 0 1 0 1-1 3-2 4-5 7-7l1 1 2-2c1 0 2 0 3 1l3-2z" class="g"></path><path d="M298 266c1-1 2-2 3-2 2-1 4-1 6-1h3 0v2c-1 1-4 0-6 1-1 0-3 1-5 1l-1-1z" class="N"></path><path d="M289 268l1 1 2-2c1 0 2 0 3 1-3 1-5 2-6 4-2 2-4 5-6 7-2-2-2 0-4-1v-1h2v-1c1 0 1 0 1-1 3-2 4-5 7-7z" class="C"></path><path d="M307 256l1-3c0-1 1-2 1-4 0 0 0-1 1-1h0c0 1 0 2 1 3h1v1l-1 1c1 1 1 1 2 1h0l4 2h1v3h-1 1c-2 1-2 2-2 4l1 1v1c0 1 0 1-1 1s-1 1-2 1h0l-4-4h0-3c-2 0-4 0-6 1-1 0-2 1-3 2l-3 2c-1-1-2-1-3-1l-2 2-1-1v-1-1h-2l3-1 2-1 3-3h0c1 0 2-1 2-2 1-1 0-3 1-4h1v1h1c0-1 1-1 1-1h3l1-1h1l-1 1v2h1s0-1 1-1z" class="M"></path><path d="M307 263l2-2h1 2l1 1v1l-1-1h-2 0v1h-3z" class="R"></path><path d="M292 264h2l-5 4v-1-1h-2l3-1 2-1z" class="a"></path><path d="M304 255l1-1h1l-1 1v2h1s0-1 1-1h0s0 1-1 1c0 1 0 1-1 2 0 1 0 0-1 1h-3v-1c1-1 2-2 3-4h0z" class="C"></path><path d="M297 259c1-1 0-3 1-4h1v1 5l-5 3h-2l3-3h0c1 0 2-1 2-2z" class="J"></path><path d="M308 258c3-1 7 0 9 1h1c-2 1-2 2-2 4-2-3-5-3-8-4v-1z" class="g"></path><path d="M308 258c0-2 1-2 1-4 0 0 1 0 1-1v1l1-1c1 1 1 1 2 1h0l4 2h1v3h-1c-2-1-6-2-9-1z" class="F"></path><path d="M311 253c1 1 1 1 2 1h0l4 2h-1c-1 0-4 0-5-1l-1-1 1-1z" class="Z"></path><path d="M229 260c4-1 10 0 13 1s6 1 9 2h3c2 1 6 2 9 0v1h0c-1 1-3 2-4 2h0c-2-1-4-1-5-1l-4-1-1 1c1 0 1 0 1 1h-2-2l-1 2 1 1 2 2h-1s0 1-1 1v1h-1-1v2h-1l-2-1h-2l-6-2c-2 0-3 0-4-1v-8-1-1h0 0v-1z" class="f"></path><path d="M233 272h0c0-1 0-6-1-7 1-1 1-1 2 0s2 3 3 5c0 1 0 1 1 2v1l1 1-6-2z" class="D"></path><path d="M240 271h0c-1 0-1-1-2-2h0c0-1-1-2 0-3v1c2 0 4 1 6 1 1 1 1 2 2 3v1 1h-1-1v2h-1l-2-1-1-3z" class="B"></path><path d="M240 271c1 0 1 0 2 1h0v-1c1-1 3 0 4 0v1 1h-1-1v2h-1l-2-1-1-3z" class="J"></path><path d="M229 260c4-1 10 0 13 1s6 1 9 2h3c2 1 6 2 9 0v1h0c-1 1-3 2-4 2h0c-2-1-4-1-5-1l-4-1-1 1c1 0 1 0 1 1h-2-2l-9-3h-1c-1 0-2-1-3-2-1 0-3 0-4 1v-1h0 0v-1z" class="d"></path><path d="M233 261l6 1h-1c0 1-1 1-1 1h-1c-1 0-2-1-3-2z" class="B"></path><path d="M239 262l11 2-1 1c1 0 1 0 1 1h-2-2l-9-3s1 0 1-1h1z" class="I"></path><path d="M295 246v-1h1 2l2 3 1 1 2 3-2 3s-1 0-1 1h-1v-1h-1c-1 1 0 3-1 4 0 1-1 2-2 2h0l-3 3-2 1-3 1c-2-1-4-3-6-4l1-3v-1h3-2v-2-1c0-1 1-1 1-2l1-2h0c1 0 2-1 3-2 0-1 1-1 1-1 2 1 5-1 6-2z" class="S"></path><path d="M292 249h1 1c0 1-1 1-2 2h-1v-1l1-1z" class="Z"></path><path d="M288 249v2h1 2v1s-1 1-2 1-2-1-3 0h0-2l1-2h0c1 0 2-1 3-2z" class="X"></path><path d="M283 256h1l1-2h1v1h0c-1 1-1 1-1 2 1 0 2-1 3-1s2 0 3-1h0l1 1c-1 0-1 1-2 1s-1 0-2 1h0c-1 0-2 1-3 0h-2v-2z" class="F"></path><path d="M300 248l1 1 2 3-2 3s-1 0-1 1h-1v-1h-1c-1 1 0 3-1 4 0-2 1-4 0-5h-1v-4h2v-1h-1v-1h3z" class="C"></path><path d="M288 258c1 1 1 3 2 3h1c1-1 2-3 3-3s1 2 1 3l-3 3-2 1-3 1c-2-1-4-3-6-4l1-3v-1h3c1 1 2 0 3 0z" class="B"></path><path d="M282 259c2 2 3 4 5 5 1 0 1 0 1 1h2l-3 1c-2-1-4-3-6-4l1-3z" class="Q"></path><path d="M281 239c1 0 3-1 5-1l4 4 2 2c1 0 2 0 3 2-1 1-4 3-6 2 0 0-1 0-1 1-1 1-2 2-3 2h0l-1 2c0 1-1 1-1 2v1 2h2-3v1l-1 3-2-3-1 1h-1c-1 1 0 3-1 5v-2c-1 0-1 0-2-1 0-1 0-2-1-3v-3c-1-1-1-4-1-6v-3l2-2v-1 4h1v-1c1 0 1-1 1-1 0-2 1-4 2-6 1 0 2 0 3-1z" class="D"></path><path d="M287 245s1 0 2-1v2 2l-2-3z" class="J"></path><path d="M287 245l2 3h0s-1 0-1 1c-1 1-2 2-3 2h0c-2 0-3 2-4 1v-1c1-3 4-4 6-6z" class="U"></path><path d="M281 239c1 0 3-1 5-1l4 4c-1 1-1 1-2 1-2-1-4-1-5 0h-1v-2h-1v-2z" class="Q"></path><path d="M282 241c1 0 4-1 5-1l2 2h0l-1 1c-2-1-4-1-5 0h-1v-2z" class="M"></path><path d="M276 246c0-2 1-4 2-6 1 0 2 0 3-1v2h1v2h1c-3 1-5 3-5 6-1 2-1 3-1 5 1 2 1 3 2 5l-1 1h-1c-1 1 0 3-1 5v-2c-1 0-1 0-2-1 0-1 0-2-1-3v-3c-1-1-1-4-1-6v-3l2-2v-1 4h1v-1c1 0 1-1 1-1z" class="F"></path><path d="M274 245l-1 11c-1-1-1-4-1-6v-3l2-2z" class="b"></path><path d="M276 246c0-2 1-4 2-6 1 0 2 0 3-1v2c-1 1-3 2-3 3l-2 2z" class="K"></path><path d="M277 260c0-2-1-2-1-4l-1-1c0-2 1-4 1-6h1v5h0c1 2 1 3 2 5l-1 1h-1z" class="R"></path><path d="M246 341c1 1 1 1 1 2 1 0 1 1 2 1v1h1c0-1 1-2 1-3 1-1 1-2 1-3l1 1c0 1-1 2-1 2 0 1 0 1-1 2-1 6-3 11-3 18 0 1 1 2 1 3l-1 2c-2 1-3 3-5 4v-2h0v-1-3l-1 1c-1 2-2 3-4 4s-2 1-4-1h-1c-1 0-1 1-2 2l-1 3h0c-1-5 2-13 4-17l8-13 2 1h2l-2-1c1-1 1 0 2 0v-2-1z" class="C"></path><path d="M243 350v-3l1-1 1 1c0 1 0 1-1 2h0v2h0-1v-1z" class="R"></path><path d="M235 362l1 1-1 2h0c2 0 2 0 4 1v2l-1 1h-1c-2 0-2-1-3-2l-1-1c0-1 1-2 2-4z" class="H"></path><path d="M234 367v-1h2c0 1 1 1 2 2v1h-1c-2 0-2-1-3-2zm1-5l4-10c1-2 2-3 3-4 0 0 0 1 1 2v1 3 2c0 1 1 2 1 3 2 3 3 5 4 8-2 1-3 3-5 4v-2h0v-1-3l-1 1v-1h-1 0-1c-1-1-2-2-3-2h-1l-1-1z" class="B"></path><path d="M243 354v2h-4v-1h3v-1h1z" class="L"></path><path d="M237 363c1-1 3-1 5-2 1 2 1 3 2 4v1c0 1 0 2-1 3h0v-1-3l-1 1v-1h-1 0-1c-1-1-2-2-3-2z" class="O"></path><path d="M263 275c0-1 0-1 1-2h2l2 1h0c-1 0-1 1-1 1l-1 1c1 1 3 1 3 2v2 1s0 1 1 2c2 3 0 8 1 11v1h0c1 1 0 3 1 4v2c-1 1 0 1 0 2h0c-1 2 1 4 0 6-1-1 0-2 0-3-2 2 0 7 0 10v1h-1 0v1c1 0 1 1 1 2h0c0 2 0 4-1 5v-2c0-2 0-4-1-4-1-4-3-9-5-12l-2-1h0l-1-1c-2 0-3-1-5-2-2 0-1 2-3 3 0-1 0-2 1-3l-1-2c1-2 3-3 4-5v-1c1-1 1-1 1-3v-1c1 1 1 1 1 2v1c-1 1-1 1 0 2l2-2c0-2 0-3-1-5h2c-1-1-1-2-2-3 0-1 0-2-1-2l3-2v1l1 2c1-1 1-2 1-3h2c-2-2-3-2-6-3l-2-3h4v-1z" class="C"></path><path d="M255 303c1-2 1-3 3-4v2h1c1 0 2 1 4 2v2h-1c-2 0-3-1-5-2-2 0-1 2-3 3 0-1 0-2 1-3z" class="U"></path><path d="M263 275v2c2 2 4 3 5 5v1l1 1h0v3h-1v1h0c-1-1-2-1-3-2-1 0-1-1-1-1 1-1 1-2 1-3h2c-2-2-3-2-6-3l-2-3h4v-1z" class="I"></path><path d="M264 285c1-1 1-2 1-3h2c1 1 1 3 1 5v1h0c-1-1-2-1-3-2-1 0-1-1-1-1z" class="R"></path><path d="M263 289c-1-1-1-2-2-3 0-1 0-2-1-2l3-2v1l1 2s0 1 1 1c1 1 2 1 3 2h0c1 2 0 4 1 5v2 1c1 0 1 1 2 2v6 12h0c-1 0-1 0-1-1 0 0 0-1-1-2v-5l-1 1h-1c-2-2-1-6-2-7 0-1-1-1-1-2h1v-1s-1 0-2-1c0 1 0 1-1 1l-1-1s-1 1-2 1h0 0c0-1 0-1 1-2v-1l2-2c0-2 0-3-1-5h2z" class="S"></path><path d="M263 289v2 1s0 1 1 1h0c2 0 3 1 4 1l1 1h-1-3v1 1h0-1l-1 1c0 1 0 1-1 1l-1-1s-1 1-2 1h0 0c0-1 0-1 1-2v-1l2-2c0-2 0-3-1-5h2z" class="G"></path><path d="M229 291h0c-1-1 0-3 0-5v-9c0-1 0-3-1-5h0 1c0-3-1-6 0-9v8c1 1 2 1 4 1l6 2h2l2 1c1 1 2 0 3 1h0l-1 1h0c0 1 0 1-1 2l-1-1c-1 1-2 3-2 5-1 3 0 5 1 7-1 1-1 1-2 1l2 2s-1 1-1 2c0 0 1 0 0 1 0 1 0 2 1 2l-1 2c-1 0-2 1-3 2l-1 1h0l-1 1c-1 0-1-1-2-2v2s-1 1-1 2l-1-1-1 1 2 1c-1 1-1 1-2 1v-1c0-1 0-1-1-2 0 2 0 6-1 8v-10-3-9z" class="C"></path><path d="M230 289c1 1 3 3 5 3v3l-3-2-2-2v-2z" class="F"></path><path d="M235 280c2-2 4-3 6-3h1c-2 2-3 4-3 7h0l-1-1h-1s0-1-1-1c0-1 0-1-1-2z" class="P"></path><path d="M231 273h1v1h2 2c1 1 2 1 3 1v1l-1 1-4 2h-2-1-1l1-2c-1-1 0-2-1-3l1-1z" class="L"></path><path d="M232 273v1h2 2c1 1 2 1 3 1v1l-1 1c-1-1-1-1-2-1l-1 1h-1v-1h-1c-1-1-1-2-1-3z" class="k"></path><path d="M229 291c0-3-1-16 1-18h1l-1 1c1 1 0 2 1 3l-1 2h1 1 2c-1 2-2 3-3 5 0 1-1 2-1 4l1 1h-1v2c-1 2 0 11-1 12v-3-9z" class="I"></path><path d="M230 291l2 2c-1 2-1 3-1 5h0v2h1l2 2h0v2s-1 1-1 2l-1-1-1 1 2 1c-1 1-1 1-2 1v-1c0-1 0-1-1-2 0 2 0 6-1 8v-10c1-1 0-10 1-12z" class="g"></path><path d="M232 293l3 2h2v1c1 1 1 2 2 3-1 2-2 3-2 4l-1 1c-1 0-1-1-2-2h0l-2-2h-1v-2h0c0-2 0-3 1-5z" class="X"></path><path d="M232 293l3 2h2v1c-1 0-2 0-3 1h-2s0 1-1 1h0c0-2 0-3 1-5z" class="D"></path><path d="M235 280c1 1 1 1 1 2 1 0 1 1 1 1h1l1 1c-1 3 0 5 1 7h0l2 2s-1 1-1 2c0 0 1 0 0 1 0 1 0 2 1 2l-1 2c-1 0-2 1-3 2l-1 1h0c0-1 1-2 2-4-1-1-1-2-2-3v-1h-2v-3-1h0l1-1h0c-1 0-2-1-3-1-1-1-1-1-1-2 0-2 0-3 1-4l2-3z" class="B"></path><path d="M236 290c2 1 2 1 3 2v1l-2 2h-2v-3-1h0l1-1z" class="Z"></path><path d="M236 290c-1 0-2-1-3-1-1-1-1-1-1-2 0-2 0-3 1-4l1 1h2v-1 1c-1 1-1 2-3 3h1c0 1 1 1 2 1v1 1z" class="J"></path><defs><linearGradient id="AF" x1="220.713" y1="311.371" x2="240.314" y2="339.916" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#AF)" d="M229 313c1-2 1-6 1-8 1 1 1 1 1 2v1c1 0 1 0 2-1 1 1 1 2 2 3v1c1 1 1 1 1 2 1 0 1 0 1-1 1 1 1 2 1 2 0 1 1 2 1 3h0 0c-1 0-1 1-1 1v2l2 1h0v-1h1c1 3 3 4 5 6-1 1-1 2-2 3l1 5v1-1l-1 1v-2l-1 3 3 5v1 2c-1 0-1-1-2 0l2 1h-2l-2-1-8 13s-1 1-2 1v-1h0c0 1-1 1-1 2v2h0-2v-1-4c1-1 1 0 2 0 1-2 0-3-1-5l-1-2v-6-30z"></path><path d="M232 313l1-1c0-1 0-1-1-1v-1-1c1 1 2 1 3 1v1 2 1l-1-1h-2z" class="H"></path><path d="M233 337c1-2-1-3-2-4v-3l1-1c2 2 4 3 6 5h0c-1 1-2 1-4 1v2h-1z" class="C"></path><path d="M240 326h2c0 1 1 2 2 3l1 5v1-1l-1 1v-2l-1 3c-1-2-1-4-3-4l-1-1c-1-1-1-2 0-4l1-1z" class="F"></path><path d="M240 326h2c0 1 1 2 2 3l1 5v1-1l-1 1v-2l-1-2c-1-2-1-3-3-5z" class="S"></path><path d="M233 337h1v-2c2 0 3 0 4-1h0l8 8v2c-1 0-1-1-2 0l2 1h-2l-2-1h-2c-1 1-1 2-3 3h-1v-1s-1-1-1-2h0l-1-1-2-1c-1-1-1-2-1-4v-1h2z" class="M"></path><path d="M231 338l2 2c2 1 3 0 5 1 0 0 1 1 1 2h0l-1 1c0-1 0-1-1-2-1 2 0 2-1 3v1s-1-1-1-2h0l-1-1-2-1c-1-1-1-2-1-4z" class="F"></path><path d="M230 345c1-2 1-2 2-3l2 1 1 1h0c0 1 1 2 1 2v1h1c2-1 2-2 3-3h2l-8 13s-1 1-2 1v-1h0c0 1-1 1-1 2v2h0-2v-1-4c1-1 1 0 2 0 1-2 0-3-1-5l-1-2c0-2 0-3 1-4z" class="B"></path><path d="M230 345c1-2 1-2 2-3l2 1 1 1h0c0 1 1 2 1 2v1h0l-2-1h-1l1 1h0 0-2c-1 1-1 1-1 2 1 1 1 1 3 1l-1 1h-3l-1-2c0-2 0-3 1-4z" class="L"></path><path d="M230 345c1-2 1-2 2-3l2 1c-1 1-2 1-3 2h-1z" class="I"></path><path d="M235 311c1 1 1 1 1 2 1 0 1 0 1-1 1 1 1 2 1 2 0 1 1 2 1 3h0 0c-1 0-1 1-1 1v2l2 1h0v-1h1c1 3 3 4 5 6-1 1-1 2-2 3-1-1-2-2-2-3h-2l-1 1h-1c-1 0-1 1-2 1h-2c-1-1-2-2-3-4v-3h1v-2l-1-1h1c1 1 1 1 2 1-2-2-2-3-2-6h2l1 1v-1-2z" class="R"></path><path d="M231 324v-3h1v-2l-1-1h1c1 1 1 1 2 1 0 1 1 1 1 2s1 1 1 2c-1 1-1 1-2 0l-1-1h-1l-1 2z" class="b"></path><path d="M229 236c0 1 0 2 1 2 2 2 6 2 8 4v1h0 1s1 0 2-1l1 2 4 2c4 2 8 1 13-1 2-1 5-3 7-3h1 0 1l1 3v2l1 1c0 1 1 1 1 3h0v1h0c1-1 1-1 1-2 0 2 0 5 1 6v3c-1 1-1 2 0 4-1 0-1 0-1 1-1 0-2 1-3 1v1h-1 0c0 1-1 1-1 1l-1 1h0c-1-1-1 0-1-1-1-1 0-2-1-3h-1v-1c-3 2-7 1-9 0h-3c-3-1-6-1-9-2s-9-2-13-1v-19-5z" class="H"></path><path d="M266 246h0l1 2v1h0l-2-2 1-1z" class="B"></path><path d="M267 242h0 1l1 3c-1 1-2 1-3 1h0l-1-1c1-1 1-2 2-3zm-21 13c1-1 1-1 1-2v1c1 0 1 1 2 1v1 1h2s0 1 1 1l-2 2c-2-2-3-3-4-5z" class="L"></path><path d="M265 250c1 1 1 2 2 4-2 2-4 6-7 7h0 0-2l-2-2h-4l-1-3h2v2h3 0l2-1c1 0 2 0 2-1 2-1 2-1 2-3h1l1-1 1-1v-1z" class="N"></path><path d="M256 258l2-1c1 0 2 0 2-1 2-1 2-1 2-3h1 1v1c-1 1-1 2-1 3-1 0-1 1-2 1h-1c-1 0-1 1-2 1s-1 0-2-1z" class="F"></path><path d="M269 247l1 1c0 1 1 1 1 3h0v1h0c1-1 1-1 1-2 0 2 0 5 1 6v3c-1 1-1 2 0 4-1 0-1 0-1 1-1 0-2 1-3 1v1h-1 0c0 1-1 1-1 1l-1 1h0c-1-1-1 0-1-1-1-1 0-2-1-3h-1v-1c-3 2-7 1-9 0h-3l1-1 1-1h1 1 1c0 1 1 2 2 2h0v-2h2l-1 1 1 1h0 1 1c0-1 0-1 1-1l1-1h0c1-1 2-1 2-2h1v1 1h-1c-1 1-1 1-1 2l1 1c1-2 1-3 2-4v-2c0-1 1-1 1-1v-2-8z" class="K"></path><path d="M268 266h-1c-1 0-1 0-1-1v-1h2v2h0z" class="H"></path><path d="M269 247l1 1c0 1 1 1 1 3h0v1h0c1-1 1-1 1-2 0 2 0 5 1 6v3c-1 1-1 2 0 4-1 0-1 0-1 1-1 0-2 1-3 1v-1c1-3 1-6 0-9v-8zm-18 9h0v-4c1-3 3-3 6-4 1-1 2-2 3-2 2 1 4 2 5 4v1l-1 1-1 1h-1c0 2 0 2-2 3 0 1-1 1-2 1l-2 1h0-3v-2h-2z" class="E"></path><path d="M255 250h1c1 0 1 1 1 1h0 3v-1l1 1v1 2c-1 0-1-1-2 0-1 0-3-1-4-1v1c-1-1 0-1 0-2v-2z" class="F"></path><defs><linearGradient id="AG" x1="244.514" y1="257.892" x2="226.782" y2="245.027" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#AG)" d="M229 236c0 1 0 2 1 2 2 2 6 2 8 4v1h0 1s1 0 2-1l1 2c1 3 4 5 7 6v3h-2c0 1 0 1-1 2h-1v1l-1 1-1-2-1 1 1 1v2c1 1 1 1 1 2h-2c-3-1-9-2-13-1v-19-5z"></path><path d="M317 265c1 1 1 2 1 3v2c-1 1-2 4-4 4-3 1-6 0-9 1l1 1h0c5 1 7 1 10 5 1 2 2 4 2 7v3c1 1 1 3 1 5v4h0c-1 2 0 7-1 8l-1-1-1-2-2-1-1 2h0c-1 0-1 0-1 1h-3-1-1-2-1c0-1 2-2 3-2l-1-1c1 0 2 0 2-1-1-1-1-1-2-1s-1 0-1-1h-1c-2 0-5-2-6-2-2-1-4-2-5-3l-2 1s-1 0-1 1c-2-1-6-1-7-2l2-1-3-3 1-1h0l3-6v-3l1-1c2-5 4-7 8-10l2-1c3-1 10-3 14-2h2l1-1h0c1 0 1-1 2-1s1 0 1-1z" class="F"></path><path d="M286 282c2 0 3-1 4-1h1c-2 2-1 2-2 4-1 1-2 2-2 3v1l-1 1c-1 0-2 0-3 1l3-6v-3z" class="X"></path><path d="M303 276l2-1 1 1h0l-1 1c1 0 2 1 3 1l-1 1 1 1c1 0 1 1 2 2 1 0 1 1 1 1 0 1 0 1 1 1v2l-1 1c-1 0-2 2-3 2v-5c0-4-2-6-5-8z" class="J"></path><path d="M297 279c2-1 3-1 5 0 2 0 3 2 4 4v2h0 0c-1 0-1 0-3 1h-1c-1-1-1-1-2-1h-1l1-1v-2h-2v-1c-1 0-1-1-1-2z" class="D"></path><path d="M298 281h0c2 1 4 0 5 3v2h-1c-1-1-1-1-2-1h-1l1-1v-2h-2v-1z" class="e"></path><path d="M287 289l1-1v-1h1 0l1 1h0l1-1h0l1-2c0-1 1-2 2-3h0l1-2 2-1c0 1 0 2 1 2v1 2c-2 1-4 2-5 3s-1 1-2 1v1h-1l-1 1c0 1 1 3 1 4h-3c-1 1-2 1-2 1l-3-3 1-1h0c1-1 2-1 3-1l1-1z" class="S"></path><path d="M283 291v1c2 0 3-1 4-1v1 2c-1 1-2 1-2 1l-3-3 1-1z" class="E"></path><path d="M317 265c1 1 1 2 1 3v2c-1 1-2 4-4 4-3 1-6 0-9 1l-2 1h-2c0-1-1-1-1-1-1 1-2 1-2 1-3 0-5 2-6 3l-1 1v-1c0-1 1-1 1-2-1 0-1 1-2 1v-1l1-1c2-2 4-2 6-3h0l1-2c-1 1-2 0-3 0l2-1c3-1 10-3 14-2h2l1-1h0c1 0 1-1 2-1s1 0 1-1z" class="G"></path><path d="M311 269h2c-1 1-1 2-1 3h-4c1 0 2-1 2-1 1-1 1-1 1-2z" class="F"></path><path d="M298 271c1 0 3-1 4-1 3 0 6 0 9-1 0 1 0 1-1 2 0 0-1 1-2 1h-2c-1 1-2 1-2 1h-2s0-1-1-1-2 0-4 1l1-2z" class="S"></path><path d="M306 276c5 1 7 1 10 5 1 2 2 4 2 7v3c0 2-1 4-3 6h-3c-1 0-4-2-5-2h0v-1h-1v1c0 1-1 1-1 1h-1c0-1 0-1-1-2h0c3-1 4-2 5-5 1 0 2-2 3-2l1-1v-2c-1 0-1 0-1-1 0 0 0-1-1-1-1-1-1-2-2-2l-1-1 1-1c-1 0-2-1-3-1l1-1z" class="X"></path><path d="M308 289c1 0 2-2 3-2v1l1 1-1 1c-1 1-2 3-4 5v-1h-1v1c0 1-1 1-1 1h-1c0-1 0-1-1-2h0c3-1 4-2 5-5z" class="h"></path><path d="M308 278c2 1 3 2 5 3h0c2 2 2 4 2 6v2h-3l-1-1v-1l1-1v-2c-1 0-1 0-1-1 0 0 0-1-1-1-1-1-1-2-2-2l-1-1 1-1z" class="L"></path><path d="M298 282h2v2l-1 1h1c1 0 1 0 2 1h1c2-1 2-1 3-1 0 3-1 6-3 7 0 1 0 1-1 1l1 1c-2 1-4 2-6 4l1 1c-2-1-4-2-5-3l-2 1s-1 0-1 1c-2-1-6-1-7-2l2-1s1 0 2-1h3c0-1-1-3-1-4l1-1h1v-1c1 0 1 0 2-1s3-2 5-3v-2z" class="X"></path><path d="M293 296c-1-1 0-2 1-3l1-1c1 1 1 1 1 2 1 0 1-1 2-1h0c-1 2-2 2-2 3l1 2 1 1c-2-1-4-2-5-3z" class="V"></path><path d="M299 285h1c1 0 1 0 2 1h1c2-1 2-1 3-1 0 3-1 6-3 7 0 1 0 1-1 1 0-1-1-2-1-3v-2h-1c-1-1-1-1-1-3z" class="g"></path><path d="M318 291c1 1 1 3 1 5v4h0c-1 2 0 7-1 8l-1-1-1-2-2-1-1 2h0c-1 0-1 0-1 1h-3-1-1-2-1c0-1 2-2 3-2l-1-1c1 0 2 0 2-1-1-1-1-1-2-1s-1 0-1-1h-1c-2 0-5-2-6-2l-1-1c2-2 4-3 6-4h0 0c1 1 1 1 1 2h1s1 0 1-1v-1h1v1h0c1 0 4 2 5 2h3c2-2 3-4 3-6z" class="f"></path><path d="M307 305c2-1 5-1 7-1l-1 2h0c-1 0-1 0-1 1h-3-1-1-2-1c0-1 2-2 3-2z" class="c"></path><path d="M304 301c0-1 1-2 2-2v-1c1 0 1 1 2 2h0v-2h1 0 2c1 0 3 1 4 2v1c0 1 0 1-1 2h-2c-1-1-2 0-4 0-1-1-1-1-2-1s-1 0-1-1h-1z" class="F"></path><path d="M267 331c1 3 3 5 4 8v6c0 3 0 7 2 9h0c2 2 5 3 8 4-3-3-6-4-7-8-2-5-1-11 1-15 1 1 1 1 1 2l1 1v1c1-2 1-2 2-3 3 0 4 1 7 1h0v1h2l-1 2-1 1c0 1-1 2-2 3v1c-1 1-1 2-1 3 1 1 1 1 0 2 1 2 1 1 2 2v1l1 1 1-1c2 1 2 2 4 3l-1 1c-1 1-2 1-4 1l-1 1c1 0 1 1 1 1-2 0-5-1-7 0l-1 1h-1v2c-1 0-1 0-1-1v1l-1 1 1 1v1 2c0 1 0 1-1 1l-1 1v1c-3 1-5-1-8-2h-1c0-1-1 0-2 0 0 1 1 2 2 3h0c0 1 0 1 1 2-1 0-1 0-1 1l1 1h0c-1 0-2 1-2 2l-2-1c0 1-2 2-3 2s-2-1-3-1v-2h-1l1-1v-2c-2 0-4 1-6 1-1 0-2 0-2-1l1-1-1-1 2-2v-1h0v-1c0-2 0-4 1-5h0c2-1 2-4 3-6 2-3 3-5 5-7l-1-3 2-1s-1 0-1-1h-2l-1-1 3-5 2-1c1-2 3-3 4-5h0l2-1z" class="D"></path><path d="M254 362l2 1v1l-2 1-1-1 1-2z" class="B"></path><path d="M271 339v6c-1 0-3 0-3-1s1-1 2-2c1 0 0-2 1-3z" class="P"></path><path d="M256 363v-1c2 0 4-1 5-2-1 3-4 4-6 6h-1v-1l2-1v-1z" class="O"></path><path d="M270 359v-2h0l4 2c1 1 1 1 1 2l1 1c-2 0-4-1-5-3h0-1z" class="G"></path><path d="M263 353c0 1 0 2 1 3-1 0-2 2-4 2-1 0-1-1-2-2 0 0 0-1 1-1s2 1 4 0v-2z" class="U"></path><path d="M279 350l1-1h1v1l-1 1h1c1-1 1-1 2-1h0c1 2 1 1 2 2v1l1 1c-1 1-1 1-1 2h-1c-2-1-3-2-5-3-1-1-1-1-1-2 1 0 1 0 2-1h-1z" class="a"></path><path d="M279 350l1-1h1v1l-1 1h1c1-1 1-1 2-1h0c1 2 1 1 2 2v1l-1 1-2-2v-1c-1 0-2 1-3 2-1-1-1-1-1-2 1 0 1 0 2-1h-1z" class="C"></path><path d="M265 332l1 2c-1 1-2 1-2 2 1 1 2 1 2 2h0c0 1-1 1-1 2l1-1c0 2 0 2-1 3s-3 2-5 3c0 0-1 0-1-1h-2l-1-1 3-5 2-1c1-2 3-3 4-5z" class="M"></path><path d="M259 338l2-1 2 1v1c-1 0-1 1-2 1h0l-2-2z" class="N"></path><path d="M262 368l1 1c0 1 1 2 2 3h0c0 1 0 1 1 2-1 0-1 0-1 1l1 1h0c-1 0-2 1-2 2l-2-1c0 1-2 2-3 2s-2-1-3-1v-2h-1l1-1v-2c-2 0-4 1-6 1v-1c2-2 5-3 8-4 1-1 1-1 3 0 1 0 1-1 1-1z" class="F"></path><path d="M259 375c1-1 1-2 1-2h2v1 2c-1 0-2-1-3-1z" class="M"></path><path d="M262 368l1 1c0 1 1 2 2 3l-1 1h-2c0-2-1-3-1-4 1 0 1-1 1-1z" class="J"></path><path d="M265 372h0c0 1 0 1 1 2-1 0-1 0-1 1l1 1h0c-1 0-2 1-2 2l-2-1h1l-1-1h0v-2-1h2l1-1z" class="O"></path><path d="M259 375c1 0 2 1 3 1h0l1 1h-1c0 1-2 2-3 2s-2-1-3-1l1-1c1-1 2-1 2-2z" class="c"></path><path d="M259 375c1 0 2 1 3 1h0v1h-4-1c1-1 2-1 2-2z" class="Z"></path><path d="M258 369h1c1 1 0 2 0 3-1 2 0 3-2 4h-1-1l1-1v-2c-2 0-4 1-6 1v-1c2-2 5-3 8-4z" class="S"></path><path d="M277 339c1-2 1-2 2-3 3 0 4 1 7 1h0v1h2l-1 2-1 1c0 1-1 2-2 3v1c-1 1-1 2-1 3 1 1 1 1 0 2h0c-1 0-1 0-2 1h-1l1-1v-1h-1l-1 1c-1 0-2-1-3-1 0-2 2-2 3-4h-2-1c1-1 0-2 0-3h2l-2-2c1 0 1-1 1-1h0z" class="X"></path><path d="M279 338h1c1 1 0 1 0 2h-2c0-1 0-1 1-2z" class="S"></path><path d="M286 337h0v1h2l-1 2-1 1c0 1-1 2-2 3v1-5c0-1 0-1 1-1h1v-2z" class="F"></path><defs><linearGradient id="AH" x1="262.74" y1="362.291" x2="275.439" y2="366.388" xlink:href="#B"><stop offset="0" stop-color="#a3a1a1"></stop><stop offset="1" stop-color="#c5c3c4"></stop></linearGradient></defs><path fill="url(#AH)" d="M262 368l-1-2 1-1v-1-1l1-1c1 0 2 0 3 1v-1l-1-1 1-1c0 1 1 1 2 1 0-1-1-1 0-2h1 1 1 0c1 2 3 3 5 3v1l-1 1 1 1v1 2c0 1 0 1-1 1l-1 1v1c-3 1-5-1-8-2h-1c0-1-1 0-2 0l-1-1z"></path><path d="M259 349l1-1c1-1 4-3 6-3 1 0 2 1 2 2l2 2h0v2 1l-2 2c-2 1-3 1-4 2-1-1-1-2-1-3v2c-2 1-3 0-4 0s-1 1-1 1c-2 2-3 4-4 6l-1 2-3 4v-1c0-2 0-4 1-5h0c2-1 2-4 3-6 2-3 3-5 5-7z" class="G"></path><path d="M268 347l2 2h0v2c-1-1-2-1-4-2-1 0-2 1-3 2l1 1h-1v1h0v2c-2 1-3 0-4 0 1-4 2-5 6-6 1-1 2-1 3-2z" class="K"></path><path d="M263 352h1l-1-1c1-1 2-2 3-2 2 1 3 1 4 2v1l-2 2c-2 1-3 1-4 2-1-1-1-2-1-3h0v-1z" class="E"></path><path d="M263 352c2 0 3 1 4 1l1 1c-2 1-3 1-4 2-1-1-1-2-1-3h0v-1z" class="R"></path><path d="M302 359l2-4 3-3h1l-3 4-3 4c-1 2-2 3-3 4h0l-1 1v1l8-5c-2 2-3 4-5 6-1 1-1 2-2 3l-3 3c-1 3-2 4-4 6h-1l-3 3-1 1-1 1v1c-1 0-1 1-1 2-1 1-1 4-1 5-1 1-1 1-1 2s-2 3-3 4h-2c0 2 0 2-1 3v1c0 1 0 1-1 1v-1l-1-1-1 1c-1 0-1 0-2 1-2 0-3-1-4-1-2-1-4-2-6-4h-1c1 0 2-1 2-1h1v-1c1 0 1 0 1-1h-2 0c-1 1-2 2-4 2h-2l1 1h-1 0c0-1 0-2 1-2l-1-1h-1v-1c0-1-1-1-2-2v-2-1c0-3-2-6-1-10h0c0-1 0-2 1-2 0-1 1-1 1-1h1v2c1 0 2 1 3 1s3-1 3-2l2 1c0-1 1-2 2-2h0l-1-1c0-1 0-1 1-1-1-1-1-1-1-2h0c-1-1-2-2-2-3 1 0 2-1 2 0h1c3 1 5 3 8 2v-1l1-1c1 0 1 0 1-1v-2-1l-1-1 1-1v-1c0 1 0 1 1 1v-2h1l1-1c2-1 5 0 7 0 4 0 8 0 12-1 1-1 3-1 3-1l1 1z" class="F"></path><path d="M274 384h1c1 1 0 1 1 1h2 0c-1 1-1 2-3 2l-3 3c-1 1-1 2-1 3l-1-2c1 0 1-1 1-2s1-1 2-1l-1-1h0v-1h1c1 0 2-1 2-2h-1zm22-11c-1 3-2 4-4 6h-1l-3 3-1 1-1 1v1c-1 0-1 1-1 2l-2-1c1-1 2-2 3-4 1-1 3-2 4-4l6-5z" class="N"></path><path d="M275 378l7-1c-5 2-10 4-15 4 0 0 0-1-1-2 3 0 6 0 9-1z" class="J"></path><path d="M264 378c0-1 1-2 2-2 0 0 1 0 1 1 2 0 4-2 6-1l2 2c-3 1-6 1-9 1 0 0-1-1-2-1z" class="c"></path><path d="M277 398c3-3 4-8 6-12l2 1c-1 1-1 4-1 5-1 1-1 1-1 2s-2 3-3 4h-2-1z" class="G"></path><path d="M263 369c1 0 2-1 2 0h1c3 1 5 3 8 2v-1c0 1 1 1 1 2h7c1 0 3 0 4 1v1l-2 1v-1h0 1v-1h0-3c-3 0-7-1-9 0h-1c-2 1-2 1-4 0-1-1-2-1-3-1h0c-1-1-2-2-2-3z" class="E"></path><path d="M274 384h1c0 1-1 2-2 2h-1v1h0l1 1c-1 0-2 0-2 1s0 2-1 2l1 2v1 2h0 1c1-1 1-1 2-1s1-1 1-1h2c-2 1-3 2-4 3v1h3 1 1c0 2 0 2-1 3v1c0 1 0 1-1 1v-1l-1-1-1 1c-1 0-1 0-2 1-2 0-3-1-4-1-2-1-4-2-6-4 1 0 2-1 3-1 1-1 1-2 1-2v-3-1c-1 0-2 1-3 1l-1-1c1-1 2-2 4-3 1 0 3-2 5-3 1 0 2-1 3-1z" class="T"></path><path d="M266 392l1-1h1v4 3c-1-1-2-1-3-1 1-1 1-2 1-2v-3z" class="P"></path><path d="M277 398h1c0 2 0 2-1 3v1c0 1 0 1-1 1v-1l-1-1-1 1c-1 0-1 0-2 1v-1-1c-1 0-1-1-2-2 2 0 5 0 6-1h1z" class="H"></path><path d="M265 397c1 0 2 0 3 1 0 0 1 0 2 1s1 2 2 2v1 1c-2 0-3-1-4-1-2-1-4-2-6-4 1 0 2-1 3-1z" class="h"></path><path d="M255 376h1v2c1 0 2 1 3 1s3-1 3-2l2 1c1 0 2 1 2 1 1 1 1 2 1 2-2 1-4 1-5 2 1 1 2 1 2 3 0-1 0-2 1-3h4c1 0 1 1 2 2-2 1-4 3-5 3-2 1-3 2-4 3l1 1c1 0 2-1 3-1v1 3s0 1-1 2c-1 0-2 1-3 1h-1c1 0 2-1 2-1h1v-1c1 0 1 0 1-1h-2 0c-1 1-2 2-4 2h-2l1 1h-1 0c0-1 0-2 1-2l-1-1h-1v-1c0-1-1-1-2-2v-2-1c0-3-2-6-1-10h0c0-1 0-2 1-2 0-1 1-1 1-1z" class="M"></path><path d="M266 395h-2-1c0-1-1-1-1-2v-2l1 1c1 0 2-1 3-1v1 3z" class="C"></path><path d="M254 390h1v-1c1 0 2-1 3-2-1 2-1 3-1 5h0 1c0 2 0 2-1 3h-1v-1c0-1-1-1-2-2v-2z" class="Q"></path><path d="M255 376h1v2c1 0 2 1 3 1s3-1 3-2l2 1c1 0 2 1 2 1 1 1 1 2 1 2-2 1-4 1-5 2 1 1 2 1 2 3l-2 1c0 1 1 2 0 2-1-1-3-2-4-3v1c-1 1-2 2-3 2v1h-1v-1c0-3-2-6-1-10h0c0-1 0-2 1-2 0-1 1-1 1-1z" class="D"></path><path d="M302 359l2-4 3-3h1l-3 4-3 4c-1 2-2 3-3 4h0l-1 1v1c0 1-1 2-2 3-1 2-4 4-6 6-1 2-4 2-6 3h0l9-6c-1 0-3 1-4 1-1 1-2 1-3 1v-1c-1-1-3-1-4-1h-7c0-1-1-1-1-2l1-1c1 0 1 0 1-1v-2-1l-1-1 1-1v-1c0 1 0 1 1 1v-2h1l1-1c2-1 5 0 7 0 4 0 8 0 12-1 1-1 3-1 3-1l1 1z" class="N"></path><path d="M281 366h6c-2 1-4 2-6 2v-2z" class="E"></path><path d="M281 369c1 0 2 1 3 1 3-1 7-2 10-2-1 1-2 1-3 1-1 1-1 1-2 1h0v1c-2 1-5 1-7 1h-7c0-1-1-1-1-2l1-1c1 1 2 2 4 2v-1l2-1z" class="M"></path><path d="M274 370l1-1c1 1 2 2 4 2 3 0 7 0 10-1v1c-2 1-5 1-7 1h-7c0-1-1-1-1-2z" class="K"></path><path d="M298 359c1-1 3-1 3-1l1 1-2 2c-3 3-9 5-13 5h-6-1v2l1 1-2 1v1c-2 0-3-1-4-2 1 0 1 0 1-1v-2-1l-1-1 1-1v-1c0 1 0 1 1 1v-2h1l1-1c2-1 5 0 7 0 4 0 8 0 12-1z" class="c"></path><path d="M279 362h1l1 1-1 1h-2c0-1 0-1 1-2z" class="R"></path><path d="M276 362c0 1 0 1 1 1v-2c0 1 1 2 1 2-1 1-2 1 0 2 1 1 1 1 2 1v2h-2v-1l-2-2-1-1 1-1v-1z" class="T"></path><path d="M276 365l2 2v1h2l1 1-2 1v1c-2 0-3-1-4-2 1 0 1 0 1-1v-2-1z" class="E"></path><path d="M298 359c1-1 3-1 3-1l1 1-2 2v-1h-1s-2 1-3 1c-1 1-3 1-4 1-2-1-6-1-8-1-1-1-3-1-5-1 2-1 5 0 7 0 4 0 8 0 12-1z" class="C"></path><path d="M283 296c1 1 5 1 7 2 0-1 1-1 1-1l2-1c1 1 3 2 5 3 1 0 4 2 6 2h1c0 1 0 1 1 1s1 0 2 1c0 1-1 1-2 1 0 1-1 1-2 2h-2v1c0 1 0 1-1 2 1 1 2 2 2 3h0c2 3 4 6 5 10 3 7 4 15 3 22v4s-1 2-2 2c0 1-1 2-1 2h-1l-3 3-2 4-1-1s-2 0-3 1c-4 1-8 1-12 1 0 0 0-1-1-1l1-1c2 0 3 0 4-1l1-1c-2-1-2-2-4-3l-1 1-1-1v-1c-1-1-1 0-2-2 1-1 1-1 0-2 0-1 0-2 1-3v-1c1-1 2-2 2-3l1-1 1-2h-2v-1h0c-3 0-4-1-7-1-1 1-1 1-2 3v-1l-1-1c0-1 0-1-1-2 1-1 1-1 1-2h0c0-4 3-8 5-11 1-2 2-4 3-5v-1h-1-2c-1 2-2 4-3 4 1-2 3-4 4-6 0-1 1-3 0-4s-2-3-4-4c0-1-1-1-2-2l1-1c-1-1-1-1-1-2 1-1 1-2 2-3h1v-1h-2c2-2 4-1 6-1z" class="F"></path><path d="M281 300l2-1-1 1c0 1-1 2-1 3h0l-1 1-1 1h-1c1-3 1-4 3-5z" class="K"></path><path d="M284 317c1-1 3-4 4-5v-1l1-1-1-1c1-2 2-4 3-5 1 2 2 3 4 5v5l1 1c-2 0-2 0-3-1 0 0-1 1 0 1v1c0 1 1 3 0 4 0 0 1 0 1 1-1 0-3 0-4 1h-1l-4 2-1 1h-2c-1-1-1-2 0-3h-1c1-2 2-4 3-5z" class="h"></path><path d="M293 314v-1h-1 0v-3h1l1 1v2l1 1 1 1c-2 0-2 0-3-1z" class="B"></path><path d="M282 322c1-2 3-4 5-5 1 0 2 0 3 1v1c0 1-1 2-1 3l-4 2-1 1h-2c-1-1-1-2 0-3z" class="c"></path><path d="M294 321c2 1 3 2 4 5v1l-2 2h0c-1 1-5 1-6 2v2c-1 1-2 3-2 4h-2 0 0c-3 0-4-1-7-1-1 1-1 1-2 3v-1l-1-1c0-1 0-1-1-2 1-1 1-1 1-2h0c0-4 3-8 5-11h1c-1 1-1 2 0 3h2l1-1 4-2h1c1-1 3-1 4-1z" class="Z"></path><path d="M283 330c0-1 1-1 1-2v-2l2-2v1c0 1-1 2-1 3l1 3-1 1-1-1-1-1z" class="R"></path><path d="M294 321c2 1 3 2 4 5v1l-2 2v-3h-1c-1 1-1 1-2 1-1 1 0 1-1 0 1 0 3-1 3-2v-1c-2-2-3-2-5-2 1-1 3-1 4-1z" class="C"></path><path d="M281 322h1c-1 1-1 2 0 3l-4 3v1c2 0 3 0 5 1l1 1v4c1 1 1 2 2 2h0 0c-3 0-4-1-7-1-1 1-1 1-2 3v-1l-1-1c0-1 0-1-1-2 1-1 1-1 1-2h0c0-4 3-8 5-11z" class="i"></path><path d="M283 296c1 1 5 1 7 2 0-1 1-1 1-1l2-1c1 1 3 2 5 3 1 0 4 2 6 2h1c0 1 0 1 1 1s1 0 2 1c0 1-1 1-2 1 0 1-1 1-2 2h-2v1c0 1 0 1-1 2 1 1 2 2 2 3h0v1l-1-1c-1-2-2-4-4-5-1-2-3-3-5-5 1 1 2 3 3 4 4 5 8 13 8 20h0-1c0 1 0 2-1 2h-2c0 1-1 3-1 4l-1-2c-1 1-1 2-1 3l-1-1 1-2h0l1-3v-1c-1-3-2-4-4-5 0-1-1-1-1-1 1-1 0-3 0-4v-1c-1 0 0-1 0-1 1 1 1 1 3 1l-1-1v-5s0-1-1-2h0v-1h0c1 1 2 2 3 2l-2-2-1-1c-2-2-4-4-6-4 0 1 0 1-1 2 0 1-1 0-1 0-1 0-1-1-1-1h-1v2l-1-1-1 1h-2l1-1h0c0-1 1-2 1-3l1-1-2 1v-3h0-1l-3 6c-1-1-1-1-1-2 1-1 1-2 2-3h1v-1h-2c2-2 4-1 6-1z" class="N"></path><path d="M293 296c1 1 3 2 5 3 1 0 4 2 6 2h1c0 1 0 1 1 1s1 0 2 1c0 1-1 1-2 1 0 1-1 1-2 2h-2v1c0 1 0 1-1 2-3-5-6-8-11-11 0-1 1-1 1-1l2-1z" class="Z"></path><path d="M298 299c1 0 4 2 6 2h1c0 1 0 1 1 1s1 0 2 1c0 1-1 1-2 1 0 1-1 1-2 2v-3c-1-1-2 0-4 0l-1-1c-1-1-1-2-1-3z" class="M"></path><path d="M295 309s0-1-1-2h0v-1h0c1 1 2 2 3 2 2 4 4 8 5 11v1c1 2 1 4 1 6 0 1 0 2-1 2h-2c0 1-1 3-1 4l-1-2c-1 1-1 2-1 3l-1-1 1-2h0l1-3v-1c-1-3-2-4-4-5 0-1-1-1-1-1 1-1 0-3 0-4v-1c-1 0 0-1 0-1 1 1 1 1 3 1l-1-1v-5z" class="c"></path><path d="M294 321c0-1-1-1-1-1 1-1 0-3 0-4v-1c-1 0 0-1 0-1 1 1 1 1 3 1 2 4 3 7 4 11v2c0 1-1 3-1 4l-1-2c-1 1-1 2-1 3l-1-1 1-2h0l1-3v-1c-1-3-2-4-4-5z" class="L"></path><path d="M300 326v2c0 1-1 3-1 4l-1-2c0-1 0-2 1-3h1v-1z" class="W"></path><path d="M304 326h0c0-7-4-15-8-20-1-1-2-3-3-4 2 2 4 3 5 5 2 1 3 3 4 5l1 1v-1c2 3 4 6 5 10 3 7 4 15 3 22v4s-1 2-2 2c0 1-1 2-1 2h-1l-3 3-2 4-1-1s-2 0-3 1c-4 1-8 1-12 1 0 0 0-1-1-1l1-1c2 0 3 0 4-1l1-1c-2-1-2-2-4-3l-1 1-1-1v-1c-1-1-1 0-2-2 1-1 1-1 0-2 0-1 0-2 1-3v-1c1-1 2-2 2-3l1-1 1-2h-2v-1h0 2c0-1 1-3 2-4v-2c1-1 5-1 6-2h0l2-2-1 3h0l-1 2 1 1c0-1 0-2 1-3l1 2c0-1 1-3 1-4h2c1 0 1-1 1-2h1z" class="L"></path><path d="M309 347c1-1 1-2 2-3v4s-1 2-2 2c0 1-1 2-1 2h-1l-3 3-2 4-1-1s-2 0-3 1c3-2 5-5 8-8 1-1 2-3 3-4z" class="E"></path><path d="M294 353v-1c1-1 1-1 1-2h1 1c1 1 1 1 3 1h1c1 1 2 0 3 1h0l-3 1-2 2h-3l-1-1v1c0-1-1-2-1-2z" class="b"></path><path d="M289 350c1 0 1-1 2-1h1c0 1 1 3 2 4 0 0 1 1 1 2v1s-1-1-2-1v1h-2c-2-1-2-2-4-3 1 0 1-1 1-1 0-1 1-2 1-2z" class="e"></path><path d="M287 353c1 0 1-1 1-1 0-1 1-2 1-2 0 1 0 2 1 2v1c0 1 1 2 2 2h0l1 1h-2c-2-1-2-2-4-3z" class="U"></path><path d="M295 343h0v-4c1 1 2 3 3 5 1 1 2 1 2 2 0-1 1-2 2-3l2-1h1c0 1 0 1 1 2h2c-1 2-2 3-4 4-1 1-4 1-5 0-2-1-3-2-4-4v-1z" class="G"></path><path d="M304 342l1 1c-1 2-1 2-2 3h-3c0-1 1-2 2-3l2-1z" class="D"></path><path d="M304 326v6c-1 2-2 3-2 5-1 0-3 2-2 3v1c1 1 1 2 2 2-1 1-2 2-2 3 0-1-1-1-2-2-1-2-2-4-3-5v4h0c0 1 0 2-1 3v-3-2c0-3 1-7 2-9l1 1c0-1 0-2 1-3l1 2c0-1 1-3 1-4h2c1 0 1-1 1-2h1z" class="f"></path><path d="M298 341l1 2 1-1v-1c1 1 1 2 2 2-1 1-2 2-2 3 0-1-1-1-2-2v-3z" class="P"></path><path d="M298 330l1 2-2 6c0 1 1 2 1 3v3c-1-2-2-4-3-5v4h0c0 1 0 2-1 3v-3-2c0-3 1-7 2-9l1 1c0-1 0-2 1-3z" class="e"></path><path d="M296 329l2-2-1 3h0l-1 2c-1 2-2 6-2 9v2 3c0 1 0 2-1 3h-1-1c-1 0-1 1-2 1 0 0-1 1-1 2 0 0 0 1-1 1l-1 1-1-1v-1c-1-1-1 0-2-2 1-1 1-1 0-2 0-1 0-2 1-3v-1c1-1 2-2 2-3l1-1 1-2h-2v-1h0 2c0-1 1-3 2-4v-2c1-1 5-1 6-2h0z" class="T"></path><path d="M286 337h2v1 1 1c1 0 0 2 0 2v1c0 1 0 2-1 2v-3h0v-2l1-2h-2v-1h0z" class="G"></path><path d="M289 348c0-2 0-4 1-5v-1c0-3 2-5 3-8 1-2 2-3 3-4h1l-1 2c-1 2-2 6-2 9v2h0c-1 0-1-1-2-1v1s0 1 1 1l-1 1-1 1 1 1c-1 0-1 1-1 1h-1-1z" class="E"></path><path d="M287 340v2h0v3l-1 1v3h1v-2l2 1h1 1s0-1 1-1l-1-1 1-1 1-1c-1 0-1-1-1-1v-1c1 0 1 1 2 1h0v3c0 1 0 2-1 3h-1-1c-1 0-1 1-2 1 0 0-1 1-1 2 0 0 0 1-1 1l-1 1-1-1v-1c-1-1-1 0-2-2 1-1 1-1 0-2 0-1 0-2 1-3v-1c1-1 2-2 2-3l1-1z" class="N"></path><path d="M287 342v3l-1 1h-1c0-1 1-3 2-4z" class="E"></path><path d="M304 326h0c0-7-4-15-8-20-1-1-2-3-3-4 2 2 4 3 5 5 2 1 3 3 4 5l1 1v-1c2 3 4 6 5 10 3 7 4 15 3 22-1 1-1 2-2 3v-2c1 0 1-1 1-1v-1l1-2h-1s0 1-1 2v-1l-1 2h-2c-1-1-1-1-1-2h-1l-2 1c-1 0-1-1-2-2v-1c-1-1 1-3 2-3 0-2 1-3 2-5v-6z" class="S"></path><path d="M303 313v-1c2 3 4 6 5 10 3 7 4 15 3 22-1 1-1 2-2 3v-2c1 0 1-1 1-1v-1l1-2h-1s0 1-1 2v-1-2c1-8-1-21-6-27z" class="i"></path><defs><linearGradient id="AI" x1="321.181" y1="351.791" x2="307.506" y2="354.19" xlink:href="#B"><stop offset="0" stop-color="#050403"></stop><stop offset="1" stop-color="#262627"></stop></linearGradient></defs><path fill="url(#AI)" d="M317 307l1 1c1-1 0-6 1-8h0v39 72c-1-2-1-4-1-6l-1-1-1 1c-1-3-3-6-5-9h0v-1h-1-1c-1 0-2 0-2 1-1 0-1 0-1 1h-1v3h-1l-1 1c-2 1-3 3-5 3h-1v-1c0-1 0-1-1-2h-3 0l-1 1c0-1-1-1-1-2h-2v1l-1 1c-1 0-2 1-3 1 0 1 0 2 1 2l-1 1h-1c-1-2-3-1-4-2s-2-1-3-2v-1c1-1 1-1 1-3h2c1-1 3-3 3-4s0-1 1-2c0-1 0-4 1-5 0-1 0-2 1-2v-1l1-1 1-1 3-3h1c2-2 3-3 4-6l3-3c1-1 1-2 2-3 2-2 3-4 5-6l-8 5v-1l1-1h0c1-1 2-2 3-4l3-4 3-4s1-1 1-2c1 0 2-2 2-2v-4c1-7 0-15-3-22-1-4-3-7-5-10h0c0-1-1-2-2-3 1-1 1-1 1-2v-1h2c1-1 2-1 2-2l1 1c-1 0-3 1-3 2h1 2 1 1 3c0-1 0-1 1-1h0l1-2 2 1 1 2z"></path><path d="M302 307l3 4-1 1h-1c0-1-1-2-2-3 1-1 1-1 1-2z" class="X"></path><path d="M315 376c0-1 0-2 1-3h0c1 2 1 4 1 6h0-1-1c1-1 1-2 0-3z" class="P"></path><path d="M305 311c2 3 4 6 5 9l-2-2v-1l-1-1c0 2 1 4 1 6-1-4-3-7-5-10h0 1l1-1z" class="C"></path><path d="M305 307h2c1 2 2 3 3 4l3 3c0 2 1 3 2 5l-1 2-9-14z" class="G"></path><path d="M313 372s1 1 2 1v3c1 1 1 2 0 3 0 1-1 2-1 3l1 1c0 1-1 2-1 3-1 0-1 1-1 1-1 0-1 0-2-1v-2l2-12zm-5 17l2 2c1 1 2 2 2 3h1l1 1 1-1 1-1h0c0 2 0 4-1 6l1 2 1-1h1c0 1-2 2-2 4h1l-1 1c-1-3-3-6-5-9h0v-1h-1c-1-2-1-4-2-6z" class="g"></path><path d="M313 396c1 0 1 0 2 1v1h-1s0-1-1-2z" class="H"></path><path d="M315 383c0-1 0-1 1-1 2 3 1 8 0 11h0l-1 1-1 1-1-1h-1c0-1-1-2-2-3 1 0 1-1 1-1 0-1 0-2-1-3v-1h1c1 1 1 1 2 1 0 0 0-1 1-1 0-1 1-2 1-3z" class="C"></path><path d="M311 386c1 1 1 1 2 1 0 0 0-1 1-1v3c0 1-1 2-1 3v1h1l1 1-1 1-1-1h-1c0-1-1-2-2-3 1 0 1-1 1-1 0-1 0-2-1-3v-1h1z" class="P"></path><path d="M316 339v4c1 5 2 12 0 17h0l-1-1v1l-3 9-1-1c0-2 2-4 2-6 1-2 2-5 2-7v-4h0l-1-1-1 2c0-3 1-6 1-8s1-4 2-5z" class="a"></path><path d="M316 339v4c0 3 0 8-1 12v-4h0l-1-1-1 2c0-3 1-6 1-8s1-4 2-5z" class="E"></path><path d="M313 352l1-2 1 1h0v4c0 2-1 5-2 7 0 2-2 4-2 6-1 4-2 7-3 10l-1-1v-1h-1v-4c1-2 2-3 2-5 1-1 2-3 2-5 0-1 0-1 1-2l2-8z" class="Z"></path><path d="M312 369l3-9v-1l1 1-3 12-2 12v1 1h-1c-1-2-2-2-2-4l-1 3h-2v-3h1v-1l2-3c1-3 2-6 3-10l1 1z" class="M"></path><path d="M311 368l1 1-4 13-1 3h-2v-3h1v-1l2-3c1-3 2-6 3-10z" class="B"></path><path d="M308 322c0-2-1-4-1-6l1 1v1l2 2c3 5 6 12 6 19-1 1-2 3-2 5-1 3-2 5-3 7h-1 0c1-1 2-2 2-3l-2 2h-1c1 0 2-2 2-2v-4c1-7 0-15-3-22z" class="R"></path><path d="M317 307l1 1c1-1 0-6 1-8h0v39c-1-2 0-6 0-9-1-1 0-2-1-3v4l-1-1c0-3-2-6-3-9l1-2c-1-2-2-3-2-5l-3-3c-1-1-2-2-3-4h1 1 3c0-1 0-1 1-1h0l1-2 2 1 1 2z" class="B"></path><path d="M314 304l2 1 1 2v2 2l-1-1c-1-1-2-2-3-2-1 1-3 0-4-1h3c0-1 0-1 1-1h0l1-2z" class="E"></path><path d="M317 315l1 11c0 1 0 3-1 4 0-3-2-6-3-9l1-2h1v-2h0c0-1 0-1 1-1v-1z" class="X"></path><path d="M307 307h1 1c1 1 3 2 4 1 1 1 4 3 4 4v3 1c-1 0-1 0-1 1h0v2h-1c-1-2-2-3-2-5l-3-3c-1-1-2-2-3-4z" class="Z"></path><path d="M314 344c0 2-1 5-1 8l-2 8c-1 1-1 1-1 2 0 2-1 4-2 5 0 2-1 3-2 5v4h1v1l1 1-2 3v1h-1v3h2l1-3c0 2 1 2 2 4h1v-1-1 2h-1v1c1 1 1 2 1 3 0 0 0 1-1 1l-2-2c1 2 1 4 2 6h-1c-1 0-2 0-2 1-1 0-1 0-1 1h-1v3h-1l-1 1c-2 1-3 3-5 3h-1v-1c0-1 0-1-1-2h-3 0l-1 1c0-1-1-1-1-2h-2v1l-1 1c-1 0-2 1-3 1 0 1 0 2 1 2l-1 1h-1c-1-2-3-1-4-2s-2-1-3-2v-1c1-1 1-1 1-3h2c1-1 3-3 3-4s0-1 1-2c0-1 0-4 1-5 0-1 0-2 1-2v-1l1-1 1-1 3-3h1c2-2 3-3 4-6l3-3c1-1 1-2 2-3 2-2 3-4 5-6l-8 5v-1l1-1h0c1-1 2-2 3-4l3-4 3-4s1-1 1-2h1l2-2c0 1-1 2-2 3h0 1c1-2 2-4 3-7z" class="M"></path><path d="M303 381l2-6c0 2 1 4 1 6h0v1h-1v3c-1-1-1 0-1-1s0-2-1-3z" class="K"></path><path d="M303 381c1 1 1 2 1 3s0 0 1 1h2l-1 1h-1-2c-1 1-1 3-2 4 0-3 2-7 2-9z" class="Y"></path><path d="M304 366h1v2l-3 6c0 2-1 7-2 8v-3c1-2 1-5 2-7v-1l-1-1s2-3 3-4zm-13 19v-1c0-1 1-3 2-4 2-3 5-6 7-8 0 2-2 4-3 6v2h-1-1c-1 2-1 3-1 6l-3-1z" class="E"></path><path d="M307 385l1-3c0 2 1 2 2 4h1v-1-1 2h-1v1c1 1 1 2 1 3 0 0 0 1-1 1l-2-2c1 2 1 4 2 6h-1c-1 0-2 0-2 1-1 0-1 0-1 1h-1v3h-1l-1 1c-2 1-3 3-5 3h-1c0-3 1-7 0-9h1c0 1 1 2 1 4v1h1c1-2 1-5 0-7 0-1 0-2 1-3s1-3 2-4h2 1l1-1z" class="R"></path><path d="M307 385l1-3c0 2 1 2 2 4h1v-1-1 2h-1v1c1 1 1 2 1 3 0 0 0 1-1 1l-2-2c0-1 0-2-1-2l-1 1c-1-1-1-1-1-2h1l1-1z" class="N"></path><path d="M314 344c0 2-1 5-1 8l-2 8-3 4-3 4v-2h-1c-1 1-3 4-3 4l-1 2c-2 2-5 5-7 8-1 1-2 3-2 4v1c0-1-1-1-1-2l2-4c2-2 3-3 4-6l3-3c1-1 1-2 2-3 2-2 3-4 5-6l-8 5v-1l1-1h0c1-1 2-2 3-4l3-4 3-4s1-1 1-2h1l2-2c0 1-1 2-2 3h0 1c1-2 2-4 3-7z" class="U"></path><path d="M304 366c1-1 2-4 4-5 0 1-1 1 0 3l-3 4v-2h-1z" class="N"></path><path d="M309 350h1l2-2c0 1-1 2-2 3h0 1c-3 6-7 11-12 13h0c1-1 2-2 3-4l3-4 3-4s1-1 1-2z" class="F"></path><path d="M285 387c0-1 0-2 1-2v-1l1-1 1-1 3-3h1l-2 4c0 1 1 1 1 2l3 1c0 1-1 3-2 4 0 2 1 2 2 3 0 1 0 3 1 3h0c1-1 1-1 2-1 1 2 0 6 0 9v-1c0-1 0-1-1-2h-3 0l-1 1c0-1-1-1-1-2h-2v1l-1 1c-1 0-2 1-3 1 0 1 0 2 1 2l-1 1h-1c-1-2-3-1-4-2s-2-1-3-2v-1c1-1 1-1 1-3h2c1-1 3-3 3-4s0-1 1-2c0-1 0-4 1-5z" class="R"></path><path d="M288 392l1 2c1 3 3 5 4 7l-1 1c0-1-1-1-1-2h-2c-1-1 0-2-1-4v-4z" class="E"></path><path d="M290 383c0 1 1 1 1 2l3 1c0 1-1 3-2 4l-1 1c0 1 1 1 1 2v1h0l-1-1c-1 0-1 0-2 1l-1-2c0-1-1-2-1-4 1-2 2-3 3-5z" class="H"></path><path d="M289 394c1-1 1-1 2-1l1 1h0v-1c0-1-1-1-1-2l1-1c0 2 1 2 2 3 0 1 0 3 1 3h0c1-1 1-1 2-1 1 2 0 6 0 9v-1c0-1 0-1-1-2h-3 0c-1-2-3-4-4-7zm-5-2l1 5h-1l-2 2h0c0 1 1 1 1 2 1 0 1 1 1 2h1c0 1 0 2 1 2l-1 1h-1c-1-2-3-1-4-2s-2-1-3-2v-1c1-1 1-1 1-3h2c1-1 3-3 3-4s0-1 1-2z" class="J"></path><path d="M282 399h0c0 1 1 1 1 2 1 0 1 1 1 2h-1c-1-1-2-2-2-3l1-1z" class="E"></path><path d="M310 395h1v1h0c2 3 4 6 5 9s1 5 1 8c0 9-2 16-4 25-1 2-1 4-2 5h0l-1 1h0c0 1-1 2-1 2v1c-1 0-1 1-1 1 0 1 0 1-1 1 0 1 0 2-1 2l-1 1v1c-1 0-1 1-1 1-1 1-1 3-2 4s-1 1-3 2v-1c0 1 0 1-1 1h-2-7l-1-1s1-1 1-2v-1l-3 4h-2c0-1 1-2 1-3s1-2 0-3v-1c1 0 1-1 2-1v-1h-2-1c-2 1-3 0-5 1l1 1h-1l-4-7v-3h0v-2h-1v-5l-1 1h0l-1-2v-1l-1 1h0l-1 1 1 2-1 1c-1 0-1 0-2-1h-1-2c-1 0-1 1-1 1 0-1 0-1-1-2h0v-1-3c0-1-2-2-2-2l-2-2s-2 1-3 1c0 0-2-1-3-2h0 0c-1-1-2-1-2-2-3-3-2-7-2-10 0-2 2-3 3-5h-1c-1 0-2 2-3 3l-1 2v1 3l-1 1v-2h0c0-1-1-1-1-2h1l1-3h0c0-2 1-3 2-5l6-6 4-3 2-2h1c2 2 4 3 6 4 1 0 2 1 4 1 1-1 1-1 2-1l1-1 1 1v1c1 0 1 0 1-1 1 1 2 1 3 2s3 0 4 2h1l1-1c-1 0-1-1-1-2 1 0 2-1 3-1l1-1v-1h2c0 1 1 1 1 2l1-1h0 3c1 1 1 1 1 2v1h1c2 0 3-2 5-3l1-1h1v-3h1c0-1 0-1 1-1 0-1 1-1 2-1h1z" class="F"></path><path d="M290 416h1v1l-1 2c-1 0-1 0-2-1 1 0 1-1 2-2z" class="M"></path><path d="M280 419h0 2l1 1-1 1-1 1c-1-1-1-2-1-3z" class="d"></path><path d="M288 413v3l-2 3h-1l1-1-1-2 2-1 1-2z" class="Y"></path><path d="M259 423l-1-1v-2h0c0-1-1-1-2-2l1-1 1 1c1 0 1 1 2 3v1l-1 1z" class="E"></path><path d="M256 410h0 1c1 0 2 1 3 1h0c-2 0-4 0-5 1l1 1h1c0 1 0 2-1 3h-2c1-2 1-3 0-4 0-1 2-2 2-2z" class="Y"></path><path d="M263 421c0-2-1-3-2-4v-1h2c1 0 2 0 3 1 0 1-1 2 0 3h-3v1z" class="D"></path><path d="M252 419h2v1s0 1-1 1c1 1 2 2 2 3 1-1 1-2 1-2 1 1 1 2 1 4h-2c-1-1-2-3-3-5v-1-1z" class="E"></path><path d="M266 420c1 1 2 1 4 3h0c-1 0-2 1-3 1s-2 1-4 0v-1-2-1h3z" class="B"></path><path d="M254 409l2 1s-2 1-2 2c1 1 1 2 0 4-1 0-2 0-3 1-1-1-1-1-2-1 0-2 2-3 3-5h0l2-2z" class="J"></path><path d="M254 409l2 1s-2 1-2 2c0 0-1 1-2 1v-1-1l2-2z" class="V"></path><path d="M280 418c1 0 4-1 5-2l1 2-1 1h1c1 1 0 2 0 4h2c0 1-1 1-1 2 2 1 2 1 3 2h-1l-1-1h-2l-2-2 1-1v-1h-1c0-1 0-2 1-3h-2v1l-1-1h-2 0v-1z" class="Q"></path><path d="M280 418c-1 0-1 0-1-1s2-3 3-4 2-2 3-1c2 0 2 0 3 1l-1 2-2 1c-1 1-4 2-5 2z" class="H"></path><path d="M266 417l2 2c0-1 0-1 1-1l1 1c1-1 2-1 2-1h3 1v3 1c-1 0-1 1-1 2l-2-1c1-1 1-1 1-2h-1c-2 1-2 2-2 4 0-1-1-1-1-2h0c-2-2-3-2-4-3s0-2 0-3z" class="G"></path><path d="M249 416c1 0 1 0 2 1l1 2v1 1c1 2 2 4 3 5h2 0 3l1 1c-1 1-1 2-2 2 0 0-2 1-3 1 0 0-2-1-3-2h0 0c-1-1-2-1-2-2-3-3-2-7-2-10z" class="D"></path><path d="M272 403c1-1 1-1 2-1l1-1 1 1v1h-1-1l1 1-2 2v1c-1 1-1 2-2 3h0v4c-1 0-2 0-2-1-3-2-5-2-9-2h0c-1 0-2-1-3-1h-1c1-1 2-1 3-3l-1-1c2-1 3-2 5-2h1c1 2 1 2 3 2l1-1-1-1h1v-2c1 0 2 1 4 1z" class="J"></path><path d="M269 406c1 1 1 1 1 2l-1 1-1-1c1-1 1-1 1-2z" class="H"></path><path d="M269 413v-2c0-1-1-1-1-2h1l1 1h1 0 0v4c-1 0-2 0-2-1z" class="O"></path><path d="M264 404c1 2 1 2 3 2 0 1-1 2-1 2-2 1-2 1-3 0 0-1 1-3 1-4z" class="E"></path><path d="M272 403c1-1 1-1 2-1l1-1 1 1v1h-1-1l1 1-2 2c-1-3-2-1-4-3l-1 1v-2c1 0 2 1 4 1z" class="O"></path><path d="M259 407c1-1 2-1 4-2v3c-2 1-2 1-2 2l-1 1h0c-1 0-2-1-3-1h-1c1-1 2-1 3-3z" class="B"></path><path d="M261 398h1c2 2 4 3 6 4v2h-1l1 1-1 1c-2 0-2 0-3-2h-1c-2 0-3 1-5 2l1 1c-1 2-2 2-3 3h0l-2-1-2 2h0-1c-1 0-2 2-3 3l-1 2v1 3l-1 1v-2h0c0-1-1-1-1-2h1l1-3h0c0-2 1-3 2-5l6-6 4-3 2-2z" class="C"></path><path d="M263 403c-1 0-1 0-2-1l1-1c1 0 1 1 2 1l-1 1z" class="M"></path><path d="M264 402l1 1c1 1 1 1 2 1l1 1-1 1c-2 0-2 0-3-2h-1v-1l1-1z" class="R"></path><path d="M254 409l4-3 1 1c-1 2-2 2-3 3h0l-2-1z" class="b"></path><path d="M260 422s1 0 1 1h1 1v1c2 1 3 0 4 0s2-1 3-1c0 1 1 1 1 2l-1 2 1 2v1c0 2 1 3 1 4l-1 1h0l-1 1 1 2-1 1c-1 0-1 0-2-1h-1-2c-1 0-1 1-1 1 0-1 0-1-1-2h0v-1-3c0-1-2-2-2-2l-2-2c1 0 1-1 2-2h0s0-1 1-1h0c0-1 0-1-1-2h-2v-1l1-1z" class="M"></path><path d="M270 423c0 1 1 1 1 2l-1 2 1 2v1c0 2 1 3 1 4l-1 1h0l-1 1 1 2-1 1c-1 0-1 0-2-1h-1-2c-1 0-1 1-1 1 0-1 0-1-1-2h0v-1h1v1l2-1c0-2 1-3 1-4v-1h0v-1c0-1 0-2 1-3v-1-1l1-1h-2c1 0 2-1 3-1z" class="C"></path><path d="M269 432l2 3-1 1c-1 0-2-1-2-2l1-2z" class="T"></path><path d="M270 423c0 1 1 1 1 2l-1 2 1 2v1c0 2 1 3 1 4l-1 1h0l-2-3v-1h-1 0v-3-2-1l1-1h-2c1 0 2-1 3-1z" class="e"></path><path d="M268 425c1 1 1 2 2 2l1 2v1c-1 0-2-1-3-2v-2-1z" class="Y"></path><path d="M270 423c0 1 1 1 1 2l-1 2c-1 0-1-1-2-2l1-1h-2c1 0 2-1 3-1z" class="a"></path><path d="M304 415c1 2 0 5-1 7-3 4-6 6-11 7-2 1-3 1-5 0-2 0-4-1-6-2h-2c-1 0-2 0-2 1h-1c0-2 1-3 3-5 0 0 1-1 2-1l1-1 1-1v-1h2c-1 1-1 2-1 3h1v1l-1 1 2 2h2l1 1h1c0-1 1-1 2-1s1-1 2-1h1l1-1v-3-1h0l2 2h1c0-2 0-2-1-3v-1h1 1c-1 0-1-1-1-2h0 3s1-1 2-1h0 0z" class="H"></path><path d="M281 422l1-1c1 1 1 2 1 3-1 0-1 0-2 1h-1l1 2h-2c-1 0-2 0-2 1h-1c0-2 1-3 3-5 0 0 1-1 2-1z" class="K"></path><path d="M271 425c0-2 0-3 2-4h1c0 1 0 1-1 2l2 1c0-1 0-2 1-2s2 0 3 1c-2 2-3 3-3 5h1c0-1 1-1 2-1h1v2c-1 0-1 1-1 2 1 0 2-1 2-2v1l1 1 1 1c-1 1-2 3-2 4-2 1-3 4-5 5 1 1 1 1 1 2-1 1 0 2-1 3h-1v-3h0v-2h-1v-5l-1 1h0l-1-2v-1c0-1-1-2-1-4v-1l-1-2 1-2z" class="U"></path><path d="M276 422c1 0 2 0 3 1-2 2-3 3-3 5l-1 1v-4h0v-1c0-1 0-2 1-2z" class="M"></path><path d="M277 428c0-1 1-1 2-1h1v2c-1 0-1 1-1 2-1 1-1 2-2 4v3h-2v-4c1-2 1-4 2-6z" class="F"></path><path d="M271 425c0-2 0-3 2-4h1c0 1 0 1-1 2 1 2 0 5 1 8v5l-1 1h0l-1-2v-1c0-1-1-2-1-4v-1l-1-2 1-2z" class="E"></path><path d="M281 429v1l1 1 1 1c-1 1-2 3-2 4-2 1-3 4-5 5 1 1 1 1 1 2-1 1 0 2-1 3h-1v-3h0v-2-3h2v-3c1-2 1-3 2-4 1 0 2-1 2-2z" class="C"></path><path d="M276 441v-1c1-2 3-4 4-5v-3c1-1 1-1 2-1l1 1c-1 1-2 3-2 4-2 1-3 4-5 5z" class="T"></path><path d="M304 413v-2l1-1 2 3v3c0 1 0 3 1 3v3h1c-1 2-4 6-4 8 0-1-2-2-3-3-1 2-2 2-4 3-3 1-6 2-10 2v3 2h-1s-1 1-2 1h-1c0-1 0-1-1-2h0-2c0-1 1-3 2-4l-1-1-1-1v-1c0 1-1 2-2 2 0-1 0-2 1-2v-2h-1 2c2 1 4 2 6 2 2 1 3 1 5 0 5-1 8-3 11-7 1-2 2-5 1-7v-2h0z" class="R"></path><path d="M287 437c0-1-1-1-1-2l-2-1v-1h1l2 1 1-1-1-1h1v3 2h-1z" class="C"></path><path d="M279 427h2c2 1 4 2 6 2-1 1-2 2-3 2-1-1-1-2-2-3l-1 1c0 1-1 2-2 2 0-1 0-2 1-2v-2h-1z" class="N"></path><path d="M307 416c0 1 0 3 1 3v3h1c-1 2-4 6-4 8 0-1-2-2-3-3 3-3 4-6 5-11zm-18-16h2c0 1 1 1 1 2l1-1h0 3c1 1 1 1 1 2v1h1c1 0 2 1 3 1v4h-3-3 0c-2 0-3 0-5-1-4 0-9 2-12 5-1 0-2 2-3 2v-2c-1-2-1-4-1-5s-1-1-1-1v-1l2-2-1-1h1 1c1 0 1 0 1-1 1 1 2 1 3 2s3 0 4 2h1l1-1c-1 0-1-1-1-2 1 0 2-1 3-1l1-1v-1z" class="H"></path><path d="M276 406c0 1 1 1 1 2l1 1h1l-1 1c-1 0-2 0-3 1h0v2c-1-2-1-4-1-5s1-2 2-2z" class="K"></path><path d="M289 400h2c0 1 1 1 1 2s0 2-2 3h-2v-2-1l1-1v-1z" class="C"></path><path d="M276 406c1-1 1-2 3-2v1c1 0 1 1 3 1h0l-1 1v1c-1 1-2 1-2 1h-1l-1-1c0-1-1-1-1-2z" class="j"></path><path d="M277 402c1 1 2 1 3 2s3 0 4 2h-2 0c-2 0-2-1-3-1v-1c-2 0-2 1-3 2-1 0-2 1-2 2 0-1-1-1-1-1v-1l2-2-1-1h1 1c1 0 1 0 1-1z" class="Y"></path><path d="M288 432c4 0 7-1 10-2 2-1 3-1 4-3 1 1 3 2 3 3l-1 3 1 1-1 2c-1 0-1 1-2 1h-1c-1 0-1 0-2 1-2 3-2 6-5 8-1 1-2 2-3 2l1 1c0 1-1 1-1 1-1 1-1 1-2 1l-1-1-1 1h-2-1c-2 1-3 0-5 1l1 1h-1l-4-7h1c1-1 0-2 1-3 0-1 0-1-1-2 2-1 3-4 5-5h2 0c1 1 1 1 1 2h1c1 0 2-1 2-1h1v-2-3z" class="B"></path><path d="M288 435h1 4l-2 2h-3v-2z" class="J"></path><path d="M302 437c1-2 1-3 0-4v-2h0l2 2 1 1-1 2c-1 0-1 1-2 1z" class="F"></path><path d="M281 436h2 0c1 1 1 1 1 2h1v1 1h-1v2c-2 1-5 5-7 5l-1-1c1-1 0-2 1-3 0-1 0-1-1-2 2-1 3-4 5-5z" class="X"></path><path d="M279 441h1 0c0 1 0 1-1 1 0 1 0 2-1 2h0c0-1-1-1-1-2l2-1zm5 5c1-2 2-4 4-5h1c2 0 5-1 7-2h1c0 1-1 4-2 5l-2 2h1c-1 1-2 2-3 2l1 1c0 1-1 1-1 1-1 1-1 1-2 1l-1-1-1 1h-2-1c-2 1-3 0-5 1h0c1-2 2-3 3-4 1 0 2-1 2-2z" class="Z"></path><path d="M279 452h0c1-2 2-3 3-4 1 0 2-1 2-2 1 1 1 1 1 3h-2c-1 1-2 1-2 2h1 1c1 0 1 0 2-1h2 0c1-2 3-2 5-3l1-1h1c-1 1-2 2-3 2l1 1c0 1-1 1-1 1-1 1-1 1-2 1l-1-1-1 1h-2-1c-2 1-3 0-5 1z" class="X"></path><path d="M310 395h1v1h0c2 3 4 6 5 9s1 5 1 8c0 9-2 16-4 25-1 2-1 4-2 5h0l-1 1h0c0 1-1 2-1 2v1c-1 0-1 1-1 1 0 1 0 1-1 1 0 1 0 2-1 2l-1 1v1c-1 0-1 1-1 1-1 1-1 3-2 4s-1 1-3 2v-1c0 1 0 1-1 1h-2-7l-1-1s1-1 1-2v-1l-3 4h-2c0-1 1-2 1-3s1-2 0-3v-1c1 0 1-1 2-1v-1l1-1 1 1c1 0 1 0 2-1 0 0 1 0 1-1l-1-1c1 0 2-1 3-2 3-2 3-5 5-8 1-1 1-1 2-1h1c1 0 1-1 2-1l1-2-1-1 1-3c0-2 3-6 4-8h-1v-3c-1 0-1-2-1-3v-3l-2-3-1 1v2h0v2h0 0c-1 0-2 1-2 1h-3v-2-1l-4-4h3 3v-4c-1 0-2-1-3-1 2 0 3-2 5-3l1-1h1v-3h1c0-1 0-1 1-1 0-1 1-1 2-1h1z" class="M"></path><path d="M313 430l-3 8c0 1-1 2-1 3l-1-1c1-1 1-3 1-4 1-1 1-2 1-3h0 1c0-1 1-1 1-1-1-1-1-1-2-1l1-1c1-1 1-1 2 0z" class="B"></path><path d="M308 440l1 1c-2 5-4 11-10 14-2 1-5 0-7 1v-1c1-1 4-2 6-3 5-1 7-7 10-12z" class="H"></path><path d="M310 433c0 1 0 2-1 3 0 1 0 3-1 4-3 5-5 11-10 12-2 1-5 2-6 3v1c-1 0-2 1-3 1v-1h0v-1c1-1 4-2 5-3h1c2 0 7-6 7-7 1-2 3-3 4-5 1-1 1-2 2-3l1-2h-1l2-2z" class="F"></path><path d="M310 395h1v1h0v1c0 1 1 3 1 4 0 3 1 6 0 9-1 1-2 1-3 1-1-1-2-1-2-2v-1h0-1c0-1 0-1 1-2v1l1-1v-2c1 0 1-1 1-1l-1-1c0 1-1 1-1 1-1 0-1-1-2-2v-1-3h1c0-1 0-1 1-1 0-1 1-1 2-1h1z" class="h"></path><path d="M308 402l-1-1h1c1 1 2 2 2 4l-1 1c1 1 1 1 1 2h-3 0-1c0-1 0-1 1-2v1l1-1v-2c1 0 1-1 1-1l-1-1z" class="b"></path><path d="M305 400v1c1 1 1 2 2 2 0 0 1 0 1-1l1 1s0 1-1 1v2l-1 1v-1c-1 1-1 1-1 2h1 0v1 1l1 3h-1l-2-3-1 1v2h0v2h0 0c-1 0-2 1-2 1h-3v-2-1l-4-4h3 3v-4c-1 0-2-1-3-1 2 0 3-2 5-3l1-1h1z" class="J"></path><path d="M303 401l1-1 1 5c-1 0-2 0-3-1l1-3z" class="B"></path><path d="M305 400v1c1 1 1 2 2 2 0 0 1 0 1-1l1 1s0 1-1 1-2 1-3 1l-1-5h1z" class="H"></path><path d="M302 407v-1h0c2 1 2 3 3 4l-1 1v2l-1-3h0c-1-1-1-2-1-3z" class="C"></path><path d="M302 407c0 1 0 2 1 3h0l1 3h0v2h0 0c-1 0-2 1-2 1h-3v-2-1l-4-4h3 3c1-1 0-1 1-2z" class="P"></path><path d="M302 407c0 1 0 2 1 3h0c-1 1-1 2-2 2s-2-2-3-3h3c1-1 0-1 1-2z" class="a"></path><path d="M299 414l2-1h1v1l2-1v2h0 0c-1 0-2 1-2 1h-3v-2z" class="B"></path><path d="M308 413h0c2 1 3 1 5 2v2c1 4 1 9 0 13-1-1-1-1-2 0l-1 1c1 0 1 0 2 1 0 0-1 0-1 1h-1 0l-2 2-2 2h-1l-1-1 1-2-1-1 1-3c0-2 3-6 4-8h-1v-3c-1 0-1-2-1-3v-3h1z" class="L"></path><path d="M308 413h0c2 1 3 1 5 2v2c0-1 0-1-1-2-1 2-1 3-2 4h0-2c-1 0-1-2-1-3v-3h1z" class="B"></path><path d="M309 422l1-1v5c0 2-2 4-3 6-1 1-2 1-2 2l-1-1 1-3c0-2 3-6 4-8z" class="T"></path><path d="M308 435h1l-1 2c-1 1-1 2-2 3-1 2-3 3-4 5 0 1-5 7-7 7h-1c-1 1-4 2-5 3v1h0l-3 4h-2c0-1 1-2 1-3s1-2 0-3v-1c1 0 1-1 2-1v-1l1-1 1 1c1 0 1 0 2-1 0 0 1 0 1-1l-1-1c1 0 2-1 3-2 3-2 3-5 5-8 1-1 1-1 2-1h1c1 0 1-1 2-1l1 1h1l2-2z" class="c"></path><path d="M287 451l1-1 1 1v2l-2 2c0 1-1 1-2 2 0-1 1-2 0-3v-1c1 0 1-1 2-1v-1z" class="F"></path><path d="M302 437c1 0 1-1 2-1l1 1h1c-4 3-7 7-10 11 0-4 5-7 5-10v-1h1z" class="J"></path></svg>