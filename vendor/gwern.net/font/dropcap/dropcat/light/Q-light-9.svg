<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="45 38 572 676"><!--oldViewBox="0 0 652 752"--><style>.B{fill:#2e2d2e}.C{fill:#1e1d1e}.D{fill:#242323}.E{fill:#171718}.F{fill:#121212}.G{fill:#373737}.H{fill:#494848}.I{fill:#292929}.J{fill:#010101}.K{fill:#0c0c0c}.L{fill:#3d3d3d}.M{fill:#6d6d6d}.N{fill:#504f50}.O{fill:#5e5d5e}.P{fill:#575657}.Q{fill:#424142}.R{fill:#656465}.S{fill:#888788}.T{fill:#040404}.U{fill:#787778}.V{fill:#949393}.W{fill:#a2a1a2}.X{fill:#f0eef0}.Y{fill:#d1d0d1}</style><path d="M556 391h2 1l2 2-2 2c-1-2-1-3-3-4z" class="D"></path><path d="M521 533c1 2 1 7 0 9 0-2-1-3-1-5 1-2 1-3 1-4z" class="Q"></path><path d="M561 393l1 2v-1 6l-3-5 2-2z" class="B"></path><path d="M520 533c0-3 0-5 1-8v8c0 1 0 2-1 4v-4z" class="G"></path><path d="M528 552l1-1 1 1c-1 2-1 2 0 3 1 2 0 2 1 4h-1c0-1 0-1-1-1-1-1 0-3-1-5v-1z" class="X"></path><path d="M613 626c1 4 1 9 1 14l-3-9 2 2h0v-1-3-1-2z" class="J"></path><path d="M512 538c1-2 2-4 2-6 1 3 1 7 0 10l-2 1v-1-4z" class="D"></path><path d="M535 431c2 1 2 1 3 2v1c0 3 4 6 3 9v-1h-2v-1c-1-1-1-2-1-3v-2c-2-1-2-2-3-5z" class="K"></path><path d="M566 631c2 2 3 3 3 5v4c0 2-1 4-2 5l-1 1c2-6 1-9 0-15z" class="O"></path><path d="M567 168c3 1 5 2 7 5 1 2 1 4 1 6v2 1l-3-8-1-1c-1-2-3-3-4-5z" class="Y"></path><path d="M510 532h0c2-1 4-3 6-4-1 2-3 4-4 6v1c0 3-3 6-4 7s-2 2-2 3c-1-1-1-1-2-1h-1c1 0 1-1 1-1h1l2-2 3-3 1-1v-2-1-1-1h-1 0z" class="K"></path><path d="M519 579c2-3 5-5 9-6 1-1 4-2 5-2-1 1-3 2-4 3-3 2-6 6-9 7v-1c0-1-1-1-1-1z" class="H"></path><path d="M565 665c-6 1-11 0-16-3 7 2 13 2 20-1v2h0-1l-3 2z" class="E"></path><path d="M547 132c3-3 7-5 11-8 2-1 3-3 6-3l-4 5c-3 0-3 1-5 2-3 1-5 4-8 4z" class="W"></path><defs><linearGradient id="A" x1="515.257" y1="152.782" x2="513.529" y2="148.955" xlink:href="#B"><stop offset="0" stop-color="#777577"></stop><stop offset="1" stop-color="#989796"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M506 152c4-2 8-3 13-3l2 1c1-1 2-2 3-2 0 1 0 2-1 3h-1l-1 1h-1c-2-1-4-1-5-1s-2 0-3 1c-2 0-3 0-4 1-1 0-1-1-2-1z"></path><path d="M564 666v1h3c2-1 4-2 5-3h1l-1 1c-2 2-4 3-5 5-1 0-1 0-1 1-2-1-6-2-8-2s-4 0-6-1h8c1-1 2-1 4-2z" class="C"></path><path d="M526 596c3-1 6-1 8-2h0c0 1-1 1-1 1-2 1-4 1-6 2s-5 2-8 3h1 1s0-1 1 0l3-1 1-1h1 1l1-1h1 1 1 0 3c-1 0-4 0-5 1-8 2-15 5-22 9v-1c5-4 12-7 18-10z" class="X"></path><path d="M547 703c5-1 9-3 12-5h2c-8 7-17 11-27 11-5 0-8-2-12-5-1 0-1 0-2-1 1 0 3 1 4 2h0c1 0 3 1 4 2h2s1 1 2 0h0l1 1h5l1-1c1 1 1 0 2 0h1 0 1 1l1-1h0 1 1 0c1 0 1 0 1-1h1 1c0-1 0 0 1-1h-3-1v-1z" class="D"></path><path d="M562 358c0-1-1-1-1-2-1-3-4-5-6-7h0 3l2 1c1 1 3 2 4 2h1c3 4 6 7 8 12-4-3-7-5-11-6z" class="J"></path><defs><linearGradient id="C" x1="171.012" y1="102.242" x2="177.671" y2="102.296" xlink:href="#B"><stop offset="0" stop-color="#343335"></stop><stop offset="1" stop-color="#4c4b4a"></stop></linearGradient></defs><path fill="url(#C)" d="M172 106v-1c-2-4-2-9-2-13 3 3 6 7 9 8h0l2 2h0c-1 0-1 0-2-1-1 1-1 2-1 3h0v1h0v2c1 1 1 1 1 2v3 3l-4-4c-1-2-1-4-3-5z"></path><defs><linearGradient id="D" x1="534.021" y1="667.32" x2="532.8" y2="650.228" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#716f6e"></stop></linearGradient></defs><path fill="url(#D)" d="M532 671c-1-2-2-3-2-5-3-6-4-12-4-18 2 2 3 5 4 7 4 7 11 12 18 15-1 0-2 0-3-1-2 0-4-2-5-2v1l1 1h0l-6-3h0c-1 0-1 1-2 1s-1 0-2 1h0l1 3z"></path><path d="M558 164s1 1 2 1c3-1 11-11 14-13h0l-13 13c2 1 4 3 6 3 1 2 3 3 4 5l1 1c-1 1-1 3 0 4h-1c-1 0-1-1-1-2h-2c-1 1-1 1-2 1-3-3-7-6-11-8h2c0-1-1-1-1-2h2l-2-2c1 0 1-1 2-1z" class="N"></path><path d="M562 168l6 3c0 1 1 1 1 1l2 1 1 1c-1 1-1 3 0 4h-1c-1 0-1-1-1-2h0c-1-3-7-6-8-8z" class="R"></path><path d="M562 168c-1-1-2-1-2-2l1-1c2 1 4 3 6 3 1 2 3 3 4 5l-2-1s-1 0-1-1l-6-3z" class="U"></path><path d="M558 167l3 3c2 1 4 4 7 4l2 2h0-2c-1 1-1 1-2 1-3-3-7-6-11-8h2c0-1-1-1-1-2h2z" class="L"></path><path d="M558 167l3 3h-1c-1-1-2-1-3-1h0c0-1-1-1-1-2h2z" class="B"></path><defs><linearGradient id="E" x1="399.035" y1="659.592" x2="413.795" y2="681.201" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#515050"></stop></linearGradient></defs><path fill="url(#E)" d="M404 657c1-1 1-2 2-3v1h0 2c1 0 2 0 3-1-4 9-11 19-7 29 2 3 3 6 6 9-5-3-9-7-11-12 0-2-1-4-2-5v-1-5h0v-1c-1-1 0-2-1-4 0-1 0-2 1-3v-1c0-1 0-2 1-3v3h0v1c1 1 2 0 2 0 1 0 1 1 1 1 1 0 1 0 1-1 1-1 2-3 2-4z"></path><path d="M490 626h0c1-1 1-1 2-1 1-1 2-1 2-1 2-1 3-3 5-4 1 0 2-1 3-2 1 0 1-1 2-1 2-2 5-3 8-4 2-2 6-3 9-3h0c2 1 3 1 4 1 4 0 9 1 12 3-4 0-8 0-11 1-8 2-15 5-21 8-3 2-5 3-7 5l-6 2h0l2 1c-1 1-2 1-3 1h0c-2 1-4 1-5 1v-1c-2 0-5 1-7 1h0l12-6-1-1z" class="G"></path><path d="M486 632l6-2h0l2 1c-1 1-2 1-3 1h0c-2 1-4 1-5 1v-1z" class="O"></path><defs><linearGradient id="F" x1="535.274" y1="622.522" x2="512.864" y2="643.475" xlink:href="#B"><stop offset="0" stop-color="#181617"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#F)" d="M519 623c3 0 6-1 9 0 7 1 14 4 18 10 2 2 3 4 4 7-5-3-9-5-15-6 0 0-1-1-2-1l-1 1h1-1-1-1-1 0c-2-2-7-2-10-3h1v-1c-3-2-7 0-10 0h0c-4 0-7 2-10 3h0c1-1 1-1 2-1l1-1c-1 1-3 1-4 1-1 1-2 1-4 1h-1-1-1 0-1v-1h0c1 0 2 0 3-1l-2-1h0l6-2h1c7-2 13-5 20-5z"></path><path d="M528 630c-1-1-2-1-3-2 1 0 2 0 4 1l-1 1z" class="C"></path><path d="M522 624h1v1h-1-4l-1 1h6 0c-3 0-6 1-9 0 2-1 4-1 6-1 1 0 2 0 2-1z" class="D"></path><path d="M529 629c2 0 4 1 7 2-1 0-2-1-2 0h0c1 0 1 0 2 1-1 0-6-2-8-2l1-1z" class="E"></path><path d="M519 623c3 0 6-1 9 0-1 1-2 0-3 1h-1-2c0 1-1 1-2 1 0-1 0-2-1-2z" class="I"></path><path d="M492 630l6-2h1c-2 1-3 2-5 3l-2-1h0z" class="M"></path><path d="M495 633l2-1c3-1 6-2 9-2 0-1 1-1 2-1 0 0 1 1 2 1-4 0-7 2-10 3h0c1-1 1-1 2-1l1-1c-1 1-3 1-4 1-1 1-2 1-4 1z" class="E"></path><defs><linearGradient id="G" x1="619.026" y1="625.056" x2="577.167" y2="590.344" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#343335"></stop></linearGradient></defs><path fill="url(#G)" d="M595 599c1 0 2 0 2-1v-2c0-1-1-2-2-3s-3-3-5-4l-1-2h-1c-1-1-1-1-2-1v-1h0c1 0 2 1 2 1 2 1 2 2 3 2 3 2 5 4 7 7l3 4c1 0 1 1 2 1h1l1 1h1c4 7 7 17 7 25v2 1 3 1h0l-2-2c-2-4-4-8-7-12-8-10-18-17-29-22-1 0-3-2-4-2l1-2 4 2c2 0 5 1 6 2l7 4c1 1 3 1 5 0l1 1h0c1 0 1-1 1-1l4 4h0l-5-6z"></path><path d="M572 593l4 2h-2c0 1 0 1 1 1h1l-1 1c-1 0-3-2-4-2l1-2z" class="E"></path><defs><linearGradient id="H" x1="576.569" y1="665.278" x2="568.249" y2="637.715" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#H)" d="M584 637v6c1 5 0 10-2 15l-2 4c-1 1-1 0-1 1s-3 4-3 5l-2 2h0 0v-1c-1 1-2 1-3 2 0-1 0-1 1-1l3-3c1-1 2-2 2-3h0l1-1h-1c-1 1-1 1-2 3-1 0-2 1-2 2-1 0-1 1-2 2 0-1 0-1-1-1h0c0-1 2-2 2-3v-1l1-1h-1c-1 1-3 2-5 3h-3v-1l1-1 3-2h1 0v-2c3-2 5-4 7-8-1 1-3 2-5 3-4 2-9 2-14 2 0-1 2-2 3-2 2-2 4-5 5-7l1-3 1-1c1-1 2-3 2-5l1-1h0v-1h1 1v-2 3 2h1v-1h1 1c0 1 0 2 1 3l1-1h1 0c2-1 3-2 5-1h1c0-1 0-2-1-2v-2h1z"></path><path d="M569 640l1-1h0v-1h1 1v2h-1c-1 1-2 4-3 5 0 2-1 3-3 4l1-3 1-1c1-1 2-3 2-5z" class="P"></path><defs><linearGradient id="I" x1="566.81" y1="635.628" x2="594.104" y2="618.804" xlink:href="#B"><stop offset="0" stop-color="#0a090a"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#I)" d="M557 596c15 2 29 13 38 24 3 4 5 7 7 11s2 8 3 12c0 6-1 12-3 18 0-3 0-7-2-10-1 7-1 14-5 21h-1c0 1 0 2-1 3h-2l-3 4c1-2 2-6 2-8v-1c0-1 0-4 1-5v-5-1l1-1v-1-1l1-1v-1-1c0-1 0-3 1-5v-1h-1l1-1c0-3 0-8-2-11l-1-6c-1-2-2-5-2-6 1 0 1 1 2 1l4 7v-1c-1-4-5-10-8-14-1-2-3-3-5-5-2-3-6-4-8-6-1-1-2-1-3-2-2 0-3-2-6-2-4-2-9-2-13-3h1c-2-1-4 0-6-1h0-1v-1h11z"></path><path d="M591 675l5-11c-1 3-1 6-2 8 0 1 0 2-1 3h-2z" class="X"></path><path d="M602 631c2 4 2 8 3 12-3-2-3-6-3-9v-3zm-10 4l-1-6c-1-2-2-5-2-6 1 0 1 1 2 1l4 7h0c0 2 1 3 1 4 1 4 0 8-1 12v-6c1-1 0-1 0-2l-1 1v2c1 1 0 3 0 5v-1c0-3 0-8-2-11z" class="H"></path><path d="M522 548v1c1-2 2-3 2-5s0-3 1-5c0 3-1 7 0 10 0 1-1 5-1 7 1-2 3-4 4-7h0 0v2 1 1c1 2 0 4 1 5 1 0 1 0 1 1s0 2-1 3c0 2-1 5-3 7s-4 3-7 5l-4 4v1c-2 3-5 5-6 8 2-1 3-2 4-4 0-1 1-1 1-1l1-1s1-1 2-1l2-1s1 0 1 1v1h0l-2 2v1l-1 1c-1 0-1 1-2 1-2 1-3 4-4 5l-4 3c-1 2-2 3-4 3l-1 1c-1 2-3 5-5 7h-1c-1 0-1 1-1 1l-1 1-3 4-2 2c-1 2-3 4-5 4 3-3 5-6 6-10l4-7c2-3 4-7 5-10l6-12c5-9 11-19 15-29 0-1 0-1 1-2v-1h1v2z" class="J"></path><path d="M516 563v1 1c-1 3-3 5-5 7 1-2 3-6 5-9z" class="C"></path><path d="M529 562h0l-1 1c-1 3-2 4-5 6 1-2 2-3 3-5 1-1 1-4 1-6 1 0 1-2 1-3l-1-1c0-1 1-2 1-3v1 1c1 2 0 4 1 5 1 0 1 0 1 1s0 2-1 3z" class="Y"></path><path d="M522 548v1c1-2 2-3 2-5s0-3 1-5c0 3-1 7 0 10 0 1-1 5-1 7 0 1 0 2-1 2v1l-1-1c1-1 1-2 1-3h0 0c-1 2-3 3-4 4-1 2-2 4-3 5v-1l5-13c0-1 0-1 1-2z" class="I"></path><path d="M515 578v1c-2 3-5 5-6 8 2-1 3-2 4-4 0-1 1-1 1-1l1-1s1-1 2-1l2-1s1 0 1 1v1h0l-2 2v1l-1 1c-1 0-1 1-2 1-2 1-3 4-4 5l-4 3c-1 2-2 3-4 3l-1 1c-1 2-3 5-5 7h-1c6-9 11-19 19-27z" class="Y"></path><path d="M519 579s1 0 1 1v1h0l-2 2c-1 0-1 0-1 1h-1l-3 2c-1 1-2 2-2 3h-1l7-9 2-1z" class="L"></path><path d="M510 589h1c0-1 1-2 2-3l3-2h1c0-1 0-1 1-1v1l-1 1c-1 0-1 1-2 1-2 1-3 4-4 5l-4 3c-1 2-2 3-4 3 2-3 4-6 7-8z" class="H"></path><path d="M520 533v4c0 2 1 3 1 5v3h1v-3c1 1 1 2 1 3s-1 1-1 1h-1v1c-1 1-1 1-1 2-4 10-10 20-15 29l-6 12c-1 3-3 7-5 10l-4 7h0l-4 4c-1 0-1-1-1-1-1 0-2 1-3 2v-1c-2 1-3 3-5 3l-3 3-2 1h0c0-1 1-1 1-1 1 0 1-1 2-1-1-1-2 0-3 1-2 1-7 4-9 3l3-1h-1c1-2 2-2 4-3 1-1 1-1 1-2 1-1 3-2 4-3 4-3 8-8 11-12v-2c1-3 6-6 6-10 1-4 4-6 6-10l2-7c0-2 2-4 3-6s1-4 2-6l1 1c1-3 3-7 5-11 1-1 2-3 2-5l2-1c0 2 0 4-1 7 0 1-2 5-2 7 3-5 5-10 6-15l3-8z" class="T"></path><path d="M499 570c0-2 2-4 3-6s1-4 2-6l1 1c-2 5-3 10-5 15-1 1-1 3-2 4l-1-1 2-7z" class="E"></path><path d="M497 577l1 1-3 9c-1 1-1 2-2 2h-1c-2 3-5 6-7 10v-2c1-3 6-6 6-10 1-4 4-6 6-10z" class="D"></path><path d="M491 600c1-3 3-5 4-8s3-6 4-9c0 3-2 6-3 8-1 3-3 7-2 9l-4 7h0l-4 4c-1 0-1-1-1-1 2-3 4-7 6-10z" class="P"></path><path d="M520 533v4c0 2 1 3 1 5v3h1v-3c1 1 1 2 1 3s-1 1-1 1h-1v1c-1 1-1 1-1 2-4 10-10 20-15 29l-6 12c-1 3-3 7-5 10-1-2 1-6 2-9 1 1 0 1 1 1s20-39 22-43v-1c0-1 1-1 0-3v-3-1-2l1-1s0-1-1-1c0 2 0 3-2 4l3-8z" class="I"></path><defs><linearGradient id="J" x1="478.687" y1="601.04" x2="485.486" y2="604.305" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#666566"></stop></linearGradient></defs><path fill="url(#J)" d="M485 599c2-4 5-7 7-10h1l-4 10v2c1-1 1-1 2-1-2 3-4 7-6 10-1 0-2 1-3 2v-1c-2 1-3 3-5 3l-3 3-2 1h0c0-1 1-1 1-1 1 0 1-1 2-1-1-1-2 0-3 1-2 1-7 4-9 3l3-1h-1c1-2 2-2 4-3 1-1 1-1 1-2 1-1 3-2 4-3 4-3 8-8 11-12z"></path><path d="M466 619c5-3 10-5 15-9-1 1-2 3-4 4l-3 3-2 1h0c0-1 1-1 1-1 1 0 1-1 2-1-1-1-2 0-3 1-2 1-7 4-9 3l3-1z" class="J"></path><path d="M489 599v2c1-1 1-1 2-1-2 3-4 7-6 10-1 0-2 1-3 2v-1c2-4 5-7 7-12z" class="D"></path><path d="M546 384h4c0-1 2-1 3-1s1 1 2 2v-1h1c1 1 2 2 2 4 1 1 0 2 1 3h-1-2l-2-2h-1s0 1 1 1c1 3 1 5 1 8-1-2-3-6-4-7h-1c1 1 2 3 2 5 0 1 1 2 2 3h0 0c-2 0-2-1-3-2 0 2 2 3 3 4 2 2 4 5 6 8 0 1 1 2 1 3-4-4-8-6-12-8 1 3 3 5 4 9l-5-6v-1c-2-2-8-4-11-4-6-3-15-3-21-2s-11 2-16 3c-4 1-8 2-11 3h-1c2-2 6-2 9-4 4-1 8-4 12-7 11-6 24-14 37-11z" class="J"></path><path d="M546 384h4c0-1 2-1 3-1s1 1 2 2v-1h1c1 1 2 2 2 4 1 1 0 2 1 3h-1-2l-2-2h2c1 0 1-1 1-1 0-1-1-2-1-2h-2c-2-1-4-1-5-1-1-1-2-1-3-1z" class="E"></path><path d="M537 402c3 0 9 2 11 4v1 1c1 2 3 3 3 6l-3-4v1l1 1c0 1 0 1 1 2 2 3 5 6 7 9 4 5 7 12 8 18l3 19c-2-2-4-6-6-9-3-4-7-9-11-13v1c-2-3-4-6-7-8h0c-1-1-1-2-2-3-2-2-4-5-6-7-3-2-6-5-10-6l-4-2h-1c-1-1-1-1-2-1v-1c-1 0-1 0-2-1h2c1 0 2-1 2-2-1 0-1 0-2-1 3 0 6 0 9 1h5 2 0-1c1-2 0-2 0-4 1 0 1-1 2-1h1l-1 2 1-1h1 1 2c0-1-1-1-2-1s-1-1-2-1h0z" class="T"></path><path d="M542 420c1 1 3 1 4 2l3 3c-3-1-5-3-7-5z" class="C"></path><path d="M548 421c1 2 2 4 4 5l3 3c3 3 7 9 7 13h0c-1-1-2-3-3-4-1-2-1-4-3-5-1-2-2-2-3-4-2-1-3-3-4-4l-3-3 2-1z" class="D"></path><defs><linearGradient id="K" x1="556.046" y1="422.14" x2="547.847" y2="427.038" xlink:href="#B"><stop offset="0" stop-color="#1f1e1e"></stop><stop offset="1" stop-color="#3a3a3a"></stop></linearGradient></defs><path fill="url(#K)" d="M540 414v-1h-1c2 0 3 0 5 1 1 0 3 1 5 1l1-1c2 3 5 6 7 9 4 5 7 12 8 18-2-1-2-3-3-5s-3-5-4-7c-5-6-11-10-17-14l-1-1z"></path><defs><linearGradient id="L" x1="536.262" y1="422.769" x2="530.861" y2="404.718" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#L)" d="M537 402c3 0 9 2 11 4v1 1c1 2 3 3 3 6l-3-4v1l1 1c0 1 0 1 1 2l-1 1c-2 0-4-1-5-1-2-1-3-1-5-1h1v1l1 1h0c2 2 5 4 7 6l-2 1c-1-1-3-1-4-2-3-2-5-4-8-5 5 3 9 7 13 12 0 2 1 3 2 4l2 2v1c-1 0-1-1-2-1 0-1-1-2-1-2l-1-1h-1c2 3 3 6 5 8h0v1c-2-3-4-6-7-8h0c-1-1-1-2-2-3-2-2-4-5-6-7-3-2-6-5-10-6l-4-2h-1c-1-1-1-1-2-1v-1c-1 0-1 0-2-1h2c1 0 2-1 2-2-1 0-1 0-2-1 3 0 6 0 9 1h5 2 0-1c1-2 0-2 0-4 1 0 1-1 2-1h1l-1 2 1-1h1 1 2c0-1-1-1-2-1s-1-1-2-1h0z"></path><path d="M541 415h-1c-2 0-3-1-5-3 2 0 3 1 5 2l1 1h0z" class="G"></path><path d="M522 413c1 0 2 0 3 1 1 0 3 1 5 2 4 2 10 6 14 9v2h1 2c0 2 1 3 2 4l2 2v1c-1 0-1-1-2-1 0-1-1-2-1-2l-1-1h-1c2 3 3 6 5 8h0v1c-2-3-4-6-7-8h0c-1-1-1-2-2-3-2-2-4-5-6-7-3-2-6-5-10-6l-4-2z" class="F"></path><path d="M537 402c3 0 9 2 11 4v1 1c1 2 3 3 3 6l-3-4v1l1 1-1 1c-2-1-3-2-5-3l-7-4c-1-1-1 0-1-1h1l1-1h1 1 2c0-1-1-1-2-1s-1-1-2-1h0z" class="J"></path><path d="M537 402c3 0 9 2 11 4v1 1l-1-2c-2-1-5-2-8-1-1 0-2 0-3 1-1-1-1 0-1-1h1l1-1h1 1 2c0-1-1-1-2-1s-1-1-2-1h0z" class="N"></path><path d="M510 630h0c3 0 7-2 10 0v1h-1c3 1 8 1 10 3h0 1 1 1 1-1l1-1c1 0 2 1 2 1 1 2 3 3 4 4 2 2 4 4 5 6l7 12c-2-2-4-3-6-5-3-2-6-4-9-5-4-1-8-3-12-3-8 0-17 3-25 5-6 2-12 3-18 3-3 0-7 0-10-1-2 1-6-1-8-1s-5-1-7-2c1 0 2 0 4-1-1 0-2 0-3-1h-1v-1c2 0 4 1 6 0 5 1 12 1 17-1-1-1-1 0-2-1h3c5-2 9-4 14-6l6-3c3-1 6-3 10-3z" class="B"></path><path d="M522 633c2 0 4 1 7 1h0 1 1 1 1-1l1-1c1 0 2 1 2 1 1 2 3 3 4 4 2 2 4 4 5 6l7 12c-2-2-4-3-6-5-3-2-6-4-9-5-1-3-3-4-5-5-10-5-20-3-31 0-6 3-12 6-19 7h-2 0v-1c2 0 4-1 6-1 9-3 18-8 28-10h3c2-1 3-2 5-2l1-1z" class="T"></path><path d="M516 636h6c1 0 2-1 3-1 2 1 3 1 4 2l-16-1h3z" class="C"></path><path d="M522 633c2 0 4 1 7 1h0 1 1 1 1-1l1-1c1 0 2 1 2 1 1 2 3 3 4 4 2 2 4 4 5 6-3-1-5-4-8-6-2-1-5-1-7-1-1-1-2-1-4-2-1 0-2 1-3 1h-6c2-1 3-2 5-2l1-1z" class="I"></path><path d="M510 630h0c3 0 7-2 10 0v1h-1c3 1 8 1 10 3-3 0-5-1-7-1l-1 1c-2 0-3 1-5 2h-3c-10 2-19 7-28 10-2 0-4 1-6 1v1h0 2c-1 1-3 1-4 1v1h1-7c-2 1-6-1-8-1s-5-1-7-2c1 0 2 0 4-1-1 0-2 0-3-1h-1v-1c2 0 4 1 6 0 5 1 12 1 17-1-1-1-1 0-2-1h3c5-2 9-4 14-6l6-3c3-1 6-3 10-3z" class="F"></path><path d="M477 642h3 2l-3 1c-1-1-1 0-2-1z" class="N"></path><path d="M460 646c3 1 7 0 11 1h-8v1 1c-2 0-5-1-7-2 1 0 2 0 4-1z" class="B"></path><path d="M471 647c4 0 8-1 12-3 0 1 1 1 2 2-2 0-4 1-6 1-6 1-11 1-16 1v-1h8z" class="P"></path><path d="M463 648c5 0 10 0 16-1v1h0 2c-1 1-3 1-4 1v1h1-7c-2 1-6-1-8-1v-1z" class="K"></path><path d="M510 630h0c3 0 7-2 10 0v1h-1c-10-1-18 4-27 8-3 1-6 3-10 3h-2c5-2 9-4 14-6l6-3c3-1 6-3 10-3z" class="P"></path><path d="M483 644l4-2c4-1 7-3 10-4 5-3 12-6 18-6 2 0 4 0 7 1l-1 1c-2 0-3 1-5 2h-3c-10 2-19 7-28 10-1-1-2-1-2-2z" class="N"></path><defs><linearGradient id="M" x1="576.971" y1="682.834" x2="554.83" y2="637.62" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#M)" d="M587 629h0 0c-1-2-1-4-2-6 2 2 3 4 4 6 1 1 2 5 3 6 2 3 2 8 2 11l-1 1h1v1c-1 2-1 4-1 5v1 1l-1 1v1 1l-1 1v1 5c-1 1-1 4-1 5-2 5-5 10-8 13l3-7c-3 3-6 8-8 11l3-9c-3 2-6 4-9 5-9 3-19 3-27 0-5-3-10-7-12-12l-1-3h0c1-1 1-1 2-1s1-1 2-1h0l6 3h0l-1-1v-1c1 0 3 2 5 2 1 1 2 1 3 1s2 0 3 1h0c2-2 5-1 7-2 2 0 6 1 8 2 0-1 0-1 1-1 1-2 3-3 5-5v1c0 1-2 2-2 3h0c1 0 1 0 1 1 1-1 1-2 2-2 0-1 1-2 2-2 1-2 1-2 2-3h1l-1 1h0c0 1-1 2-2 3l-3 3c-1 0-1 0-1 1 1-1 2-1 3-2v1h0 0l2-2c0-1 3-4 3-5h1v1 1 2l4-5c1 0 1 0 2-1 0 0 0-2 1-3 0-3 1-7 1-11 1-6 1-12-1-18z"></path><path d="M587 658v1c0 1-1 2-1 3l1-2h1l-2 4v-1-2s0-2 1-3z" class="E"></path><path d="M583 668s0 1 1 1h0c-1 2-1 3-2 4h-1c0-2 2-3 2-5h0zm-11-3v1c0 1-2 2-2 3h0c1 0 1 0 1 1-1 0-2 1-2 1l-2 1-1-1-1 1c-2 0-3 1-5 0h0c2 0 4-1 6-1 0-1 0-1 1-1 1-2 3-3 5-5z" class="K"></path><path d="M558 669c2 0 6 1 8 2-2 0-4 1-6 1h-5c-1 0-3-1-4-1h0c2-2 5-1 7-2z" class="B"></path><defs><linearGradient id="N" x1="524.3" y1="395.695" x2="514.556" y2="374.988" xlink:href="#B"><stop offset="0" stop-color="#232324"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#N)" d="M532 367c2-2 6-3 9-3h1c1 1 2 1 3 1l1 1c-1 0-2 0-3 1 1 1 5-1 6 1 2 0 8 0 9 1s2 1 3 1c7 5 11 12 14 19-5-4-10-8-16-11v3c3 4 3 8 3 13v1l-1-2-2-2c-1-1 0-2-1-3 0-2-1-3-2-4h-1v1c-1-1-1-2-2-2s-3 0-3 1h-4c-13-3-26 5-37 11-4 3-8 6-12 7h0-3c-1 1-2 1-3 1 0 1-1 1-2 1-1 1 0 1-1 0 4-2 7-5 10-7 1-2 2-2 3-4h0l9-9c6-7 14-13 22-17z"></path><path d="M532 367c2-2 6-3 9-3h1c1 1 2 1 3 1l1 1c-1 0-2 0-3 1 1 1 5-1 6 1-2 1-3 1-5 1l1-1c-4-1-9 0-14 2 2-2 4-3 6-4-1 0-3 0-4 1h-1z" class="H"></path><path d="M532 367h1c-3 1-5 3-8 4l1 2c2 0 3-1 4 0-1 3-1 3-3 4l-1 1h-1c-2 1-4 0-6 1l-6 3-3 3v-1c6-7 14-13 22-17z" class="U"></path><path d="M546 377h5c2-1 5 1 7 0 0 0 0 1 1 1h0v3c3 4 3 8 3 13v1l-1-2-2-2c-1-1 0-2-1-3 0-2-1-3-2-4l-1-1c-4-4-9-5-15-5h0c2-1 4-1 6-1z" class="K"></path><path d="M546 377h5c2-1 5 1 7 0 0 0 0 1 1 1h0v3c-4 0-7-4-12-3-1 0-1 0-1-1z" class="C"></path><path d="M549 368c2 0 8 0 9 1s2 1 3 1c7 5 11 12 14 19-5-4-10-8-16-11h0c-1 0-1-1-1-1-4-3-11-3-16-3l-2 1h-1c-3 1-6 3-10 4-1 1-3 3-5 3 0 0 3-2 3-3h0c1-1 2-2 4-3h1 1c-1-1-1-1 0-1 0-1 1-1 2-1 0-1 1-2 2-2 2-1 5-3 7-3s3 0 5-1z" class="F"></path><path d="M549 370h4c1 0 1 1 2 1 2 2 6 2 8 5h0 0c-7-4-13-2-20-3h10v-1h-1c-2 0-2-1-3-2z" class="B"></path><defs><linearGradient id="O" x1="550.346" y1="368.766" x2="535.699" y2="377.673" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#656566"></stop></linearGradient></defs><path fill="url(#O)" d="M527 379h0c1-1 2-2 4-3h1 1c-1-1-1-1 0-1 0-1 1-1 2-1 0-1 1-2 2-2 2-1 5-3 7-3v1h5c1 1 1 2 3 2h1v1h-10c-4 1-7 2-11 3-1 1-3 2-5 3z"></path><path d="M555 348c1 0 2 1 3 1h-3 0c2 2 5 4 6 7 0 1 1 1 1 2l-2-1v1c2 2 4 4 5 7-2-1-3-3-5-4l2 3v1h-1 0 1c1 2 2 4 2 6-1 0-1 0-2-1v-2c-1 0-1-1-1-1l-1 1 1 1v1c-1 0-2 0-3-1s-7-1-9-1c-1-2-5 0-6-1 1-1 2-1 3-1l-1-1c-1 0-2 0-3-1h-1c-3 0-7 1-9 3-8 4-16 10-22 17l-9 9h0c-1 2-2 2-3 4-3 2-6 5-10 7 1 1 0 1 1 0 1 0 2 0 2-1 1 0 2 0 3-1h3 0c-3 2-7 2-9 4h1c-2 1-5 3-7 4v1l-43 18-1-1c-3 1-6 3-9 5-1 0-3 2-4 1l2-1 19-11c7-1 13-4 19-8l13-9c1 0 3-1 4-2 3-2 5-5 8-7 1-1 3-2 4-4 12-11 23-23 33-36 4-4 9-5 15-6h2 3 3 3 1c0-1 0-2 1-2z" class="J"></path><path d="M542 351h2v1c-1 0-1 1-2 1s-1 1-2 1c-1 1-3 3-4 3 0-2 2-3 3-4l-5 1h0c2-2 5-2 8-3z" class="D"></path><path d="M560 368l-1-1c-1-1-3-2-5-3-3-1-7-1-10-1l5-2c2 0 3 1 5 2l3 1 2 2c1 0 1 0 2 1l-1 1z" class="C"></path><path d="M541 364c1-1 2-1 4-1h0c2 1 5 2 7 2 3 1 6 2 9 4h0v1c-1 0-2 0-3-1s-7-1-9-1c-1-2-5 0-6-1 1-1 2-1 3-1l-1-1c-1 0-2 0-3-1h-1z" class="B"></path><path d="M541 355c1-1 1-1 2-1 2 0 3-2 5-2l2 1c3 0 5 0 8 2l2 2v1c-2-1-3-2-5-2-3-1-7-1-10 0-4 1-8 4-10 7 0 1 0 1-1 1h0c2-3 4-6 7-9z" class="I"></path><path d="M549 361h2v-1c-2 0-5 1-7 2l-1-1c3-1 4-2 7-2 2 0 4-1 6 1 2 1 3 3 4 3l1 2h0 1c1 2 2 4 2 6-1 0-1 0-2-1v-2c-1 0-1-1-1-1-1-1-1-1-2-1l-2-2-3-1c-2-1-3-2-5-2z" class="E"></path><path d="M555 348c1 0 2 1 3 1h-3 0c2 2 5 4 6 7 0 1 1 1 1 2l-2-1-2-2c-3-2-5-2-8-2l-2-1c-2 0-3 2-5 2-1 0-1 0-2 1l-1-1c1 0 1-1 2-1s1-1 2-1v-1h-2v-1h2 3 3 3 1c0-1 0-2 1-2z" class="F"></path><path d="M547 350h3 3 1l2 1v1h-1c-2-1-5-1-8-2z" class="D"></path><path d="M501 393h0c-1 2-2 2-3 4-3 2-6 5-10 7 1 1 0 1 1 0 1 0 2 0 2-1 1 0 2 0 3-1h3 0c-3 2-7 2-9 4h1c-2 1-5 3-7 4v1l-43 18-1-1c3-1 6-3 9-4 3-2 6-2 9-4l18-9c5-2 9-4 13-7 5-3 10-7 14-11z" class="B"></path><defs><linearGradient id="P" x1="377.277" y1="649.001" x2="415.915" y2="682.591" xlink:href="#B"><stop offset="0" stop-color="#0f0e0f"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#P)" d="M393 623l7 5 2 2h1 0v1 1c1 2 3 4 4 6 1 1 2 3 3 5s1 5 2 7l-1 4c-1 1-2 1-3 1h-2 0v-1c-1 1-1 2-2 3 0 1-1 3-2 4 0 1 0 1-1 1 0 0 0-1-1-1 0 0-1 1-2 0v-1h0v-3c-1 1-1 2-1 3v1c-1 1-1 2-1 3 1 2 0 3 1 4v1h0v5 1c0 3 0 6 1 9 2 7 5 11 10 16-3-1-7-3-10-5s-5-4-7-7c1 4 4 7 6 9-3-2-7-4-10-8-2-1-3-4-4-6 0 3 1 5 2 8h0c-1-2-3-4-4-7-1-1-1-3-1-5 0-1 0-2-1-3-1 2 0 5-1 7-3-9 0-17 4-25l5-16c1-2 2-5 2-8h0-1 0v-2l1 1h1s0 1 1 1c0-2 1-3 1-5 1-2 1-4 1-6z"></path><path d="M393 623l7 5h0v3 3l-3 6c0 3 0 5-1 7-1 3-2 7-3 10-1 5-2 10-4 14 1-10 6-20 7-30l-1-1v1l-1-1-8 20c0-3 1-5 2-8 1-5 4-12 3-18h0c0-2 1-3 1-5 1-2 1-4 1-6z" class="J"></path><path d="M394 640c2-4 3-8 3-13h0c2 3-1 10-1 14l-1-1v1l-1-1z" class="D"></path><path d="M397 661v-9c1-6 3-12 4-18v-4l2 2c1 2 3 4 4 6 1 1 2 3 3 5s1 5 2 7l-1 4c-1 1-2 1-3 1h-2 0v-1c-1 1-1 2-2 3 0 1-1 3-2 4 0 1 0 1-1 1 0 0 0-1-1-1 0 0-1 1-2 0v-1h0v-3c-1 1-1 2-1 3v1z" class="J"></path><path d="M404 647c0-1 0-2 1-3v4l-1-1z" class="F"></path><path d="M410 643c1 2 1 5 2 7l-1 4c-1 1-2 1-3 1h-2 0v-1l3-8h0c0-2 1-2 1-3z" class="C"></path><path d="M398 657c-1-1 0-2 0-3 1-3 2-7 3-10 2-3 3-5 3-8v-1l1 1h0v4c0 1-1 2-1 3v1h0v-1l-1 1c0 2 0 3-1 4 0 2-1 5-1 7v-1c1-3 2-5 3-7l1 1-2 11c0-1 0-2 1-2 0 1-1 3-2 4 0 1 0 1-1 1 0 0 0-1-1-1 0 0-1 1-2 0v-1h0v-3z" class="E"></path><defs><linearGradient id="Q" x1="552.688" y1="601.382" x2="552.878" y2="569.158" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#Q)" d="M520 581v1c4-3 8-6 13-8 6-4 15-6 23-6h0c-4 1-8 3-10 5 12-4 25-2 36 4 10 6 19 14 24 24h0-1l-1-1h-1c-1 0-1-1-2-1l-3-4c-2-3-4-5-7-7-1 0-1-1-3-2 0 0-1-1-2-1h0v1c1 0 1 0 2 1h1l1 2c2 1 4 3 5 4s2 2 2 3v2c0 1-1 1-2 1l5 6h0l-4-4s0 1-1 1h0l-1-1c-2 1-4 1-5 0l-7-4c-1-1-4-2-6-2l-4-2-1 2c-11-4-25-3-37-1-2 1-5 1-8 2h-2-1l-1 1h0c-1 1-1 1-2 1v-1c1-1 3-1 4-2h0c-2 0-3 0-4 1-5 2-9 5-14 8v-1l-2 2h-1 0v-1c0-2 2-4 4-5h-2 0c-1 1-1 2-2 2h-1-1c1-1 1-2 2-2l-1-1 1-1c2 0 3-1 4-3l4-3c1-1 2-4 4-5 1 0 1-1 2-1l1-1v-1l2-2z"></path><path d="M511 591c0 1 0 1-1 1 0 1 0 1-1 2l-1 1-1-1 4-3z" class="D"></path><path d="M507 599c4-4 9-8 13-11 12-6 28-8 42-6 13 2 23 8 33 17l5 6h0l-4-4s0 1-1 1h0l-1-1c-2 1-4 1-5 0l-7-4h0 1c1-2 1-3 1-4l-4-3c-2-1-3-1-4-2l-2-1c-13-6-30-7-43-1-5 1-8 3-12 6-6 4-11 7-16 13h0v-1c0-2 2-4 4-5z" class="T"></path><path d="M574 587c6 1 12 6 17 10 1 2 3 3 4 5h0l-1-1c-2 1-4 1-5 0l-7-4h0 1c1-2 1-3 1-4l-4-3c-2-1-3-1-4-2l-2-1z" class="B"></path><defs><linearGradient id="R" x1="543.919" y1="601.064" x2="542.243" y2="582.964" xlink:href="#B"><stop offset="0" stop-color="#333334"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#R)" d="M503 605c5-6 10-9 16-13 4-3 7-5 12-6 13-6 30-5 43 1l2 1c1 1 2 1 4 2l4 3c0 1 0 2-1 4h-1 0c-1-1-4-2-6-2l-4-2-1 2c-11-4-25-3-37-1-2 1-5 1-8 2h-2-1l-1 1h0c-1 1-1 1-2 1v-1c1-1 3-1 4-2h0c-2 0-3 0-4 1-5 2-9 5-14 8v-1l-2 2h-1z"></path><path d="M524 595c11-5 26-7 38-4 4 0 7 1 10 2l-1 2c-11-4-25-3-37-1-2 1-5 1-8 2h-2-1l-1 1h0c-1 1-1 1-2 1v-1c1-1 3-1 4-2h0z" class="T"></path><path d="M491 587c0 4-5 7-6 10v2c-3 4-7 9-11 12-1 1-3 2-4 3 0 1 0 1-1 2-2 1-3 1-4 3h1l-3 1c-3 2-7 3-11 3h-3-1 0-1l1-1c-3 0-9 1-12-1-3-1-9-1-13-2h-3-1 0c1 2 3 3 5 3s2 1 4 1c5 3 11 6 17 8h0c1 1 3 2 4 2 5 2 10 2 15 2s10-1 15-2h0c2 0 5-1 7-1v1c1 0 3 0 5-1v1h1 0 1 1 1c2 0 3 0 4-1 1 0 3 0 4-1l-1 1c-1 0-1 0-2 1h0l-6 3c-5 2-9 4-14 6h-3c1 1 1 0 2 1-5 2-12 2-17 1-2 1-4 0-6 0v1h1c1 1 2 1 3 1-2 1-3 1-4 1l-15-6c-1-2-2-2-3-3s-3-2-4-4c-6-3-9-8-13-12-2-2-4-3-6-5-1 0-2-1-3-1 2 3 6 5 9 7v1l9 10 1 1v1l-1-1h0c-1 0-1-1-2-1v1c-1-2-2-3-3-4h-1 0l-1 1 8 12 4 6c2 3 4 6 5 9h0l-11-15-11-13c-2-2-3-4-5-6s-5-4-8-5l-9-8c-3-2-5-4-8-7s-5-6-8-9v-2-1h0l13 7h-1c2 3 7 6 10 7h1l8 2h1c1 1 2 0 2 1 2 0 4-1 5 0v1c9 0 19-2 27-5 2-1 5-2 7-3 0 1 0 1-1 1 1 1 2 2 3 2h0 2 1l-1 1c1 0 1 1 2 1 0 0 1 0 1-1h1c2 0 3-2 4-2l7-6 1-1c1-1 2-2 3-2l1 1h0 2c4-1 7-4 9-7l3-3z" class="J"></path><path d="M462 611c1-1 3-1 4-2-1 2-2 2-3 3-1 0 0 0 0-1h-1z" class="K"></path><path d="M449 640c2 1 3 1 5 2h2s2-1 3 0h1c2 1 4 0 6 1h-9-1c2 1 4 0 6 1-2 1-4 0-6 0h0l-7-2s0-1 1-1h-1v-1z" class="G"></path><path d="M469 611c-3 2-6 3-8 5-3 1-5 2-7 3-3 2-6 2-9 2h-3-2v-1c5 0 12 0 17-3 3-2 7-5 11-7l1 1z" class="E"></path><path d="M466 643l11-1c1 1 1 0 2 1-5 2-12 2-17 1-2-1-4 0-6-1h1 9z" class="Q"></path><path d="M448 622c4 0 8-1 12-2 1-1 2-1 3-1l1-1c1 0 2-1 2-1v-1c1-1 3-2 3-2h1c0 1 0 1-1 2-2 1-3 1-4 3h1l-3 1c-3 2-7 3-11 3h-3-1 0-1l1-1z" class="I"></path><path d="M480 599c1 0 1-1 2-2l1 1-1 1 1 1c-2 3-4 5-6 6-3 2-6 3-8 5l-1-1c5-2 8-6 12-10v-1z" class="G"></path><path d="M423 632c-1-2-5-6-5-7 1 1 1 1 2 1v-1s-2-1-2-2c-3-2-7-4-9-7 2 1 4 3 7 4 1 2 3 4 5 4l9 10 1 1v1l-1-1h0c-1 0-1-1-2-1v1c-1-2-2-3-3-4h-1 0l-1 1z" class="E"></path><path d="M425 631l1-1c1 1 2 3 3 4h1l1 1v1l-1-1h0c-1 0-1-1-2-1v1c-1-2-2-3-3-4z" class="B"></path><path d="M491 587c0 4-5 7-6 10l-2 3-1-1 1-1-1-1c-1 1-1 2-2 2l-11 9-1-1 11-10c4-1 7-4 9-7l3-3z" class="H"></path><defs><linearGradient id="S" x1="471.298" y1="596.007" x2="470.397" y2="609.255" xlink:href="#B"><stop offset="0" stop-color="#1d1b1c"></stop><stop offset="1" stop-color="#333434"></stop></linearGradient></defs><path fill="url(#S)" d="M472 599l1-1c1-1 2-2 3-2l1 1h0 2l-11 10 1 1c-1 0-2 1-3 1-1 1-3 1-4 2-2 0-3 1-5 1l6-3 3-3c2-2 6-5 6-7z"></path><defs><linearGradient id="T" x1="382.886" y1="603.943" x2="405.818" y2="599.701" xlink:href="#B"><stop offset="0" stop-color="#383937"></stop><stop offset="1" stop-color="#676567"></stop></linearGradient></defs><path fill="url(#T)" d="M380 594v-1h0l13 7h-1c2 3 7 6 10 7h1l8 2c-1 1-3 1-4 1h0l-2 1h1c-9-4-19-9-26-17z"></path><path d="M434 634h0c-1-2-2-4-4-5-2-2-5-3-7-6h0c3 1 5 3 7 4 1 2 3 4 4 5 2 1 5 2 7 3 2 2 4 3 7 4 0 1 1 1 1 1v1h1c-1 0-1 1-1 1l7 2h0v1h1c1 1 2 1 3 1-2 1-3 1-4 1l-15-6c-1-2-2-2-3-3s-3-2-4-4z" class="D"></path><path d="M424 622c2 0 2 1 4 1 5 3 11 6 17 8h0c1 1 3 2 4 2 5 2 10 2 15 2s10-1 15-2h0c2 0 5-1 7-1v1c1 0 3 0 5-1v1h1 0 1 1l-1 1c-1 1-4 1-6 2-10 4-20 5-31 3-3-1-19-7-21-9 1-1 1 0 2 0h1c-1-1-1-1-2-1-4-1-8-5-12-7z" class="Q"></path><defs><linearGradient id="U" x1="438.643" y1="598.297" x2="428.285" y2="620.841" xlink:href="#B"><stop offset="0" stop-color="#585756"></stop><stop offset="1" stop-color="#848485"></stop></linearGradient></defs><path fill="url(#U)" d="M419 611c9 0 19-2 27-5 2-1 5-2 7-3 0 1 0 1-1 1 1 1 2 2 3 2h0 2 1l-1 1c1 0 1 1 2 1 0 0 1 0 1-1h1c-12 8-28 12-43 9l-8-3-4-2h-1l2-1h0c1 0 3 0 4-1h1c1 1 2 0 2 1 2 0 4-1 5 0v1z"></path><path d="M411 609h1c1 1 2 0 2 1 2 0 4-1 5 0v1h0c-3 0-8-2-10 0 0 1 1 1 1 2l-4-2h-1l2-1h0c1 0 3 0 4-1z" class="M"></path><path d="M421 624v-1c-3-2-7-4-9-7 1 0 2 1 3 1 2 2 4 3 6 5 4 4 7 9 13 12 1 2 3 3 4 4s2 1 3 3c1 1 2 1 3 2l9 6c6 2 11 3 17 5 9 2 17 3 26 8 6 3 11 8 17 13s12 9 19 12c3 1 6 2 9 2 2 1 3 1 5 1 6 0 11 1 17-1 3 0 6-2 8-3l-10 12h-2c-3 2-7 4-12 5v1h-3c-5 2-10 0-14-2-2 0-3 0-4-1-1 0-2-1-3-1s-1 0-1-1h1c0-1-1-1-1-1h1 1l1 1s1-1 1 0h4c1 1 2 0 4 1h4l-3-1h-4l-4-1c-3-1-5-2-7-3-1 0-2-1-3-1l-7-3c-1-1-5-2-6-4-1-1-2-1-3-1 0-1 0-1-1-1-2-2-6-3-9-4-4-2-8-5-13-6-4-1-7-3-10-5-6-3-12-7-17-11-9-7-17-15-23-24v-1c1 0 1 1 2 1h0l1 1v-1l-1-1-9-10z" class="G"></path><path d="M515 692c1-1 3 0 4 1 1 0 2 1 3 1h2c2 1 4 1 6 1 0 1 1 1 1 1h1 0 15-2c-2 0-3 0-5-1-2 0-4-1-7-1-1-1-3-2-5-2l-7-3c-1-1-2-2-3-2-1-1-2-1-2-1v-1h1 0l1 1c1 0 2 0 3 1 8 3 16 5 24 5 1-1 2-1 3-1h-3v-1h1c6 0 11 1 17-1 3 0 6-2 8-3l-10 12h-2c-3 2-7 4-12 5v1h-3c-5 2-10 0-14-2-2 0-3 0-4-1-1 0-2-1-3-1s-1 0-1-1h1c0-1-1-1-1-1h1 1l1 1s1-1 1 0h4c1 1 2 0 4 1h4l-3-1h-4l-4-1c-3-1-5-2-7-3-1 0-2-1-3-1l-7-3c-1-1-5-2-6-4l11 5z" class="T"></path><path d="M515 692c1-1 3 0 4 1 1 0 2 1 3 1h2c2 1 4 1 6 1 0 1 1 1 1 1h1 0 15-2c-2 0-3 0-5-1-2 0-4-1-7-1-1-1-3-2-5-2l-7-3c-1-1-2-2-3-2-1-1-2-1-2-1v-1h1 0l1 1c1 0 2 0 3 1h-1c2 2 5 3 7 3 3 2 5 3 8 3 6 2 14 3 20 1 2 0 3-1 5-1-4 4-19 4-24 4-6-1-15-2-21-5h0z" class="I"></path><path d="M517 694c2 0 5 1 7 2 3 1 7 2 11 3 4 0 8 1 13 0h5c-3 1-5 1-7 1-4 1-7 1-10 0-2 0-8-1-10 0l2 1c7 3 12 3 19 2v1h-3c-5 2-10 0-14-2-2 0-3 0-4-1-1 0-2-1-3-1s-1 0-1-1h1c0-1-1-1-1-1h1 1l1 1s1-1 1 0h4c1 1 2 0 4 1h4l-3-1h-4l-4-1c-3-1-5-2-7-3-1 0-2-1-3-1z" class="C"></path><path d="M564 121l5-4c1 0 2-1 2-1l1 1c0 2-3 3-4 5-2 1-3 2-4 3l6-1c-2 2-4 3-6 5v1l2-1h1c-2 2-5 4-6 6 2-1 4-2 6-4-2 3-4 5-7 8l6-2c-2 2-5 3-7 6h0c3-1 5-1 7-2-3 2-6 4-8 7l6-3c-2 3-5 4-8 6 2 0 4-1 6-1l-5 3c-1 1-2 1-2 2 1 1 3 1 5 1h0c-3 1-5 1-7 2 1 1 4 0 5 1-1 1-2 1-3 1l2 1-1 2 2 1c-1 0-1 1-2 1l2 2h-2c0 1 1 1 1 2h-2c4 2 8 5 11 8l1 1-1-1c-1 1 0 1-1 2-1-1-1-1-2-1l1 1-1 2v-1h-1v2c-1 1-1 1-1 2l-2-1v2c1 0 0 0 1-1h1c-1 1-3 2-3 3v3h0l-2 1h-1l-1-2c1 0 2 0 2-1v-1c-2-1-4-1-5-1-2-1-4-1-6-2-4-1-8-2-11-3v1c-7-2-13-6-20-6h-2-10c-2-1-4 0-6 0l3 1v1c-1 0-2 0-3 1l-7 2c-1 0-2 0-3 1-1 0-2 1-2 1-1 1-1 1-1 2l-2 2c0-2 1-3 1-5h-1c4-6 8-12 11-19 0-1 0-1-1-1 0-1 6-5 8-6v-1c2 0 5-2 7-3 1 0 1 1 2 1 1-1 2-1 4-1 1-1 2-1 3-1s3 0 5 1h1l1-1h1c1-1 1-2 1-3 1-2 6-6 8-7 1 0 2 0 2-1h2c2-1 2-2 4-3 2-2 4-3 7-5h0c3 0 5-3 8-4 2-1 2-2 5-2l4-5z" class="J"></path><path d="M555 160l2 1-1 2-3-2h0l2-1z" class="Y"></path><path d="M553 167h0v-1c1 0 2 1 3 1 0 1 1 1 1 2h-2l-2-2z" class="D"></path><path d="M545 147h0c0 1-1 2-1 3v5 2l-1-1v-6c0-1 1-3 2-3z" class="I"></path><path d="M548 182c3 1 7 2 9 4v1c-3-2-6-2-9-3v-2z" class="B"></path><path d="M553 179c-5-3-10-5-14-8l6 1v1l1 1 2 1c2 1 4 2 5 3 0 0 1 0 0 1z" class="E"></path><path d="M563 177c-2-1-3-3-4-3-3-2-7-2-9-5h0l3 1h0c1 1 1 1 2 1 3 0 8 3 9 5 1 1 1 1 0 1h-1z" class="D"></path><path d="M545 136c0 1 0 2-1 2 0 1 0 1-1 1h0c-2 2-3 6-4 9 0 1 0 1-1 2 0-2 1-7 2-9 0-1 1-1 1-2v-1c0-1 3-2 4-2z" class="P"></path><path d="M558 176l-11-5v-1c2 0 4 2 6 3 3 1 6 2 9 4 0 1 1 1 1 2-1 0-3-2-5-3h0z" class="C"></path><path d="M545 155c1-1 1-2 1-3 0-3 1-6 2-9l1 1v3l-4 13v-5z" class="R"></path><path d="M529 146v5c1 1 2 3 2 3h-1-1-1l-2-2h0l-1-1c0-1 1-2 1-3 1 0 1-1 2-1l1-1z" class="D"></path><path d="M526 152v-1h2l2 3h-1-1l-2-2h0z" class="F"></path><path d="M547 132v1c-1 1-1 2-2 2v1c-1 0-4 1-4 2v1c-2 2-3 4-4 7 0 0 0 2-1 2 0-3 1-5 1-7h0l-1-1c2-1 2-2 4-3 2-2 4-3 7-5z" class="R"></path><path d="M526 152l2 2 4 3c2 2 6 4 8 6-1 0-4-2-5-3-1 0-2-1-4-1h-2-1 0c-2-1-3-1-5-2l1-2 3 1h0l-1-1v-3z" class="D"></path><path d="M526 152l2 2 4 3h-2c-1-1-2 0-3-1h0l-1-1v-3z" class="H"></path><path d="M507 170c10 0 21 4 30 8 2 0 4 1 5 2 2 1 4 1 6 2h0v2c-2-1-4-2-7-3-8-4-17-8-26-9-2-1-5-1-8-1h1 2c0-1 1 0 1 0-1-1-2-1-3-1h-1z" class="I"></path><path d="M547 139c0-1 1-2 1-2v2l2-1h0v1l-2 4c-1 3-2 6-2 9 0 1 0 2-1 3 0-1 1-1 0-2-1 1-1 1-1 2v-5c0-1 1-2 1-3h0l-1-1v1h-1 0v-1h-1c0 1 0 4-1 5h-1v-3l1-2 1-1c2-1 2-5 4-7l1 1z" class="F"></path><path d="M547 139c0-1 1-2 1-2v2l2-1h0v1h-1c-2 2-3 4-4 6l2-6z" class="M"></path><path d="M513 156c1 0 3 1 4 1h1l2 1c2 0 4 0 5 2h0c-1-1-3-1-4-1h0c1 1 2 2 2 3l1 1h0-1c-3-1-8-1-11-1v1h0c-2 0-2 0-4-1h1 5c-1-1-1-1-1 0l1-1h0l1-1h-3 0v-1h1 0l1-1h0c-1 0-2-1-2-1h-1l2-1z" class="G"></path><path d="M517 157h1l2 1h-1v1l2 1h-1-1v1h-2l1-1v-1l-3-1 2-1z" class="B"></path><path d="M518 157l2 1h-1v1c-1 0-1 0-2-1l1-1z" class="C"></path><path d="M520 158c2 0 4 0 5 2h0c-1-1-3-1-4-1h0c1 1 2 2 2 3l-4-1v-1h1 1l-2-1v-1h1z" class="E"></path><defs><linearGradient id="V" x1="520.307" y1="156.469" x2="518.539" y2="150.16" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#V)" d="M524 148c1-2 6-6 8-7 1 0 2 0 2-1h2l1 1h-1c-1 2-1 4-1 7h-1v-6h0c-2 2-1 5-1 7-2-1 0-4-2-6v7h-1v-5l-1 1-1 1c-1 0-1 1-2 1 0 1-1 2-1 3l1 1h0v3l1 1h0l-3-1h-3s0-1-1-1c-2-1-7-1-8-2 1-1 2-1 3-1s3 0 5 1h1l1-1h1c1-1 1-2 1-3z"></path><path d="M526 155l-2-2h0v-3l2-2c0 1-1 2-1 3l1 1h0v3z" class="P"></path><defs><linearGradient id="W" x1="519.463" y1="161.03" x2="508.12" y2="152.404" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#W)" d="M512 152c1 1 6 1 8 2 1 0 1 1 1 1h3l-1 2c2 1 3 1 5 2h0c2 1 3 1 5 2 0 1 1 1 2 2 1 0 1 0 2 1l-1 1c0-1 0-1-1-1h0l-10-4c-1-2-3-2-5-2l-2-1h-1c-1 0-3-1-4-1-3 0-6 1-9 2h-1l1-1 3-3 1-1c1-1 2-1 4-1z"></path><path d="M521 155h3l-1 2-2-1v-1z" class="Q"></path><defs><linearGradient id="X" x1="545.085" y1="136.973" x2="558.219" y2="135.442" xlink:href="#B"><stop offset="0" stop-color="#5f5d5e"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#X)" d="M547 132c3 0 5-3 8-4 2-1 2-2 5-2-2 3-3 6-5 8l-6 13v-3l-1-1 2-4v-1h0l-2 1v-2s-1 1-1 2l-1-1c-2 2-2 6-4 7 0-2 1-4 1-6 1 0 1 0 1-1 1 0 1-1 1-2v-1c1 0 1-1 2-2v-1h0z"></path><defs><linearGradient id="Y" x1="513.163" y1="173.192" x2="501.349" y2="163.252" xlink:href="#B"><stop offset="0" stop-color="#1e1c1e"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#Y)" d="M495 171c1-3 4-5 7-7h1c2-2 3-2 5-2 2 1 2 1 4 1h0c2 0 3 1 5 1 1 1 2 1 3 1v1l-1 1h1s1 0 1 1c2 0 3 1 5 2h-1-1l-2-1c-6-1-11-1-16 0-2 0-4 0-7 1-1 0-3 1-4 1z"></path><path d="M517 164c1 1 2 1 3 1v1l-1 1c-1 0-2-1-4-1v-1s1 0 1-1h1z" class="C"></path><path d="M553 167l2 2c4 2 8 5 11 8l1 1-1-1c-1 1 0 1-1 2-1-1-1-1-2-1l1 1-1 2v-1h-1v2c-1 1-1 1-1 2l-2-1v2c1 0 0 0 1-1h1c-1 1-3 2-3 3v3h0l-2 1h-1l-1-2c1 0 2 0 2-1v-1h1v-1s0-1 1-1c-1 0-1-1-2-1 0 0-1 0-1-1h0-3l-1-1-2-1c-3-1-5-2-8-3-1-1-1-1-2-1l1-1 1 1h2v1h2c1 1 2 1 3 2l2 1c1 0 2 0 3 1h0 1v-1h1l2 2h1v-1c-2 0-3-2-5-3 1-1 0-1 0-1-1-1-3-2-5-3l-2-1-1-1v-1c1 0 2 0 3 1l10 4v-1h0c2 1 4 3 5 3 0-1-1-1-1-2h1 1c1 0 1 0 0-1-1-2-6-5-9-5-1 0-1 0-2-1h0 0c-1-1-1-1-1-2 1 0 1 0 1-1z" class="F"></path><path d="M556 188h1v2h1 0l-2 1h-1l-1-2c1 0 2 0 2-1z" class="E"></path><path d="M558 177l2 2c1 0 1 1 1 2h-1c-1-1-3-2-4-3-2-2-6-3-9-5h1l10 4z" class="C"></path><path d="M507 171c3 0 6 0 8 1 9 1 18 5 26 9 3 1 5 2 7 3 3 1 6 1 9 3h-1c-2-1-4-1-5-1-2-1-4-1-6-2-4-1-8-2-11-3v1c-7-2-13-6-20-6-2-1-3-1-4-1h0-1 3l-2-1h1c-1-1-5-1-6-2v-1h2z" class="J"></path><path d="M514 176c-2-1-3-1-4-1h0-1 3l-2-1h1c7 1 13 3 20 6l3 1v1c-7-2-13-6-20-6z" class="C"></path><defs><linearGradient id="Z" x1="507.418" y1="182.314" x2="489.885" y2="172.671" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#Z)" d="M499 170c3-1 5-1 7-1h0l1 1h1c1 0 2 0 3 1 0 0-1-1-1 0h-2-1-2v1c1 1 5 1 6 2h-1l2 1h-3 1 0c1 0 2 0 4 1h-2-10c-2-1-4 0-6 0l3 1v1c-1 0-2 0-3 1l-7 2c-1 0-2 0-3 1-1 0-2 1-2 1 1-2 3-6 5-8 1-1 3-2 4-3l2-1c1 0 3-1 4-1z"></path><path d="M499 170c3-1 5-1 7-1h0l1 1h1c1 0 2 0 3 1 0 0-1-1-1 0h-2-1-2 0-1-2l1-1h1-4-1z" class="G"></path><defs><linearGradient id="a" x1="496.082" y1="168.624" x2="489.777" y2="164.016" xlink:href="#B"><stop offset="0" stop-color="#777577"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#a)" d="M506 152c1 0 1 1 2 1l-1 1-3 3-1 1h1c3-1 6-2 9-2l-2 1h1s1 1 2 1h0l-1 1h0-1v1h0 3l-1 1h0l-1 1c0-1 0-1 1 0h-5-1c-2 0-3 0-5 2h-1c-3 2-6 4-7 7l-2 1c-1 1-3 2-4 3-2 2-4 6-5 8-1 1-1 1-1 2l-2 2c0-2 1-3 1-5h-1c4-6 8-12 11-19 0-1 0-1-1-1 0-1 6-5 8-6v-1c2 0 5-2 7-3z"></path><path d="M503 158h1v1c2 0 3-1 5 0-1 1-2 1-3 1h0c-1-1-3-1-5-1h0 0l2-1z" class="O"></path><defs><linearGradient id="b" x1="512.196" y1="165.397" x2="493.272" y2="162.84" xlink:href="#B"><stop offset="0" stop-color="#414142"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#b)" d="M504 158c3-1 6-2 9-2l-2 1h1s1 1 2 1h0l-1 1h0-1v1h0 3l-1 1h0l-1 1c0-1 0-1 1 0h-5-1c-2 0-3 0-5 2h-1c-3 2-6 4-7 7l-2 1c-1-1-1-1-1-2 0-2 3-3 4-4 2-3 7-6 10-6h0c1 0 2 0 3-1-2-1-3 0-5 0v-1z"></path><path d="M506 604c5-3 9-6 14-8 1-1 2-1 4-1h0c-1 1-3 1-4 2v1c1 0 1 0 2-1h0l1-1h1 2c-6 3-13 6-18 10v1c7-4 14-7 22-9 1-1 4-1 5-1 7-2 15-2 22-1h-11v1h1 0c2 1 4 0 6 1h-1c4 1 9 1 13 3 3 0 4 2 6 2 1 1 2 1 3 2 2 2 6 3 8 6 2 2 4 3 5 5 3 4 7 10 8 14v1l-4-7c-1 0-1-1-2-1 0 1 1 4 2 6l1 6c-1-1-2-5-3-6-1-2-2-4-4-6 1 2 1 4 2 6h0 0c2 6 2 12 1 18 0 4-1 8-1 11-1 1-1 3-1 3-1 1-1 1-2 1l-4 5v-2-1-1h-1c0-1 0 0 1-1l2-4c2-5 3-10 2-15v-6h-1v2c1 0 1 1 1 2h-1c-2-1-3 0-5 1h0-1l-1 1c-1-1-1-2-1-3h-1-1v1h-1v-2-3 2h-1-1v1h0l-1 1v-4c0-2-1-3-3-5-1-7-6-13-12-18l-1-1c-11-5-21-5-32-2-3 0-7 1-9 3-3 1-6 2-8 4-1 0-1 1-2 1-1 1-2 2-3 2-2 1-3 3-5 4 0 0-1 0-2 1-1 0-1 0-2 1h0l1 1-12 6c-5 1-10 2-15 2s-10 0-15-2c-1 0-3-1-4-2h0c-6-2-12-5-17-8-2 0-2-1-4-1s-4-1-5-3h0 1 3c4 1 10 1 13 2 3 2 9 1 12 1l-1 1h1 0 1 3c4 0 8-1 11-3 2 1 7-2 9-3 1-1 2-2 3-1-1 0-1 1-2 1 0 0-1 0-1 1h0l2-1 3-3c2 0 3-2 5-3v1c1-1 2-2 3-2 0 0 0 1 1 1l4-4h0c-1 4-3 7-6 10 2 0 4-2 5-4l2-2 3-4 1-1s0-1 1-1h1c2-2 4-5 5-7l1 1c-1 0-1 1-2 2h1 1c1 0 1-1 2-2h0 2c-2 1-4 3-4 5v1h0 1l2-2v1z" class="K"></path><path d="M490 616c1-1 3-2 4-4 1 0 2-2 3-2 1-1 1-1 3-2l-2 2v1c-2 3-5 5-8 7-1 2-3 4-5 6v-1l6-6s0-1-1-1z" class="B"></path><path d="M423 619c4 1 10 1 13 2 3 2 9 1 12 1l-1 1h1 0 1 3-4c-6 0-11 0-17-1-2-1-6-1-8-3z" class="D"></path><path d="M508 607c7-4 14-7 22-9v1c-1 0-2 1-4 1-2 1-4 2-5 3l-1 1c-3 3-7 4-10 6-2 1-4 2-5 3v-1l6-3c0-1 0-1 1-1s2 0 2-1v-1c1 0 1 0 2-1 1 0 4-2 4-2l-1-1c-3 2-5 3-8 4-1 1-4 3-6 3 1-1 2-1 3-2zm46-2c-6-2-14-2-20-1h-1v-1h4c8-1 17 1 24 4h0c3 1 7 4 9 6-1-1-3-1-4-2h-1-1 0-1-1 0c-1-1-3-2-4-3s-3-2-4-3z" class="C"></path><path d="M587 629c-1-2-2-5-3-7-2-3-4-6-7-9-1 0-3-2-3-3 1 0 2 1 3 2 5 4 8 8 11 13v-1h0v-1c0-1-1-2-2-3h0v-2c2 1 4 5 5 6-1 0-1-1-2-1 0 1 1 4 2 6l1 6c-1-1-2-5-3-6-1-2-2-4-4-6 1 2 1 4 2 6h0 0z" class="I"></path><path d="M490 626l1 1-12 6c-5 1-10 2-15 2s-10 0-15-2c-1 0-3-1-4-2 8 2 16 4 24 3 7-1 15-5 21-8z" class="X"></path><path d="M535 597c7-2 15-2 22-1h-11v1h1 0c2 1 4 0 6 1h-1c-7-1-14 1-20 2-4 1-8 2-12 4l1-1c1-1 3-2 5-3 2 0 3-1 4-1v-1c1-1 4-1 5-1z" class="L"></path><path d="M580 665l3-8c2-6 4-14 2-21 0-2 0-3-1-5 0-3-1-5-2-8v-3c1 3 3 6 4 9 1 5 2 12 1 17 0 5-1 11-3 16l-4 5v-2z" class="D"></path><path d="M502 598l1 1c-1 0-1 1-2 2h1 1c1 0 1-1 2-2h0 2c-2 1-4 3-4 5v1l-3 3c-2 1-2 1-3 2-1 0-2 2-3 2-1 2-3 3-4 4s-1 2-2 2h-1-1-1l-2 2h-1c1-1 2-2 2-3 2 0 4-2 5-4l2-2 3-4 1-1s0-1 1-1h1c2-2 4-5 5-7z" class="C"></path><path d="M506 604c5-3 9-6 14-8 1-1 2-1 4-1h0c-1 1-3 1-4 2v1c1 0 1 0 2-1h0l1-1h1 2c-6 3-13 6-18 10v1c-1 1-2 1-3 2-3 3-7 5-10 7h-1l1-1h0c1-1 2-2 3-2 1-2 3-2 4-4l-4 2v-1l2-2 3-3h0 1l2-2v1z" class="B"></path><path d="M503 605h1l2-2v1l-3 3s-1 1-1 2l-4 2v-1l2-2 3-3h0z" class="Q"></path><defs><linearGradient id="c" x1="472.762" y1="622.693" x2="489.334" y2="609.803" xlink:href="#B"><stop offset="0" stop-color="#2d2c2d"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#c)" d="M485 610s0 1 1 1l4-4h0c-1 4-3 7-6 10 0 1-1 2-2 3h0c-2 3-7 6-10 7h-1c1-1 2-1 3-2v-1l-1-3c-2 2-7 6-10 6 1-1 3-2 5-3 6-3 10-7 14-12 1-1 2-2 3-2z"></path><defs><linearGradient id="d" x1="561.393" y1="612.633" x2="583.078" y2="640.95" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#676767"></stop></linearGradient></defs><path fill="url(#d)" d="M557 609h1c-1-2-3-2-4-3v-1c1 1 3 2 4 3s3 2 4 3h0 1 1 0 1 1c1 1 3 1 4 2 7 6 13 15 14 24h-1v2c1 0 1 1 1 2h-1c-2-1-3 0-5 1h0-1l-1 1c-1-1-1-2-1-3h-1-1v1h-1v-2-3 2h-1-1v1h0l-1 1v-4c0-2-1-3-3-5-1-7-6-13-12-18 2 0 2 0 3 1h0v-3-1h-1 0l1-1z"></path><path d="M570 627v-1l1 1 1 1c0 2 0 4 1 5v1 1h1 0l1 5h-1-1v1h-1v-2-3h0c-1-3-1-6-2-9z" class="N"></path><defs><linearGradient id="e" x1="567.286" y1="614.278" x2="558.195" y2="634.849" xlink:href="#B"><stop offset="0" stop-color="#0f0e0f"></stop><stop offset="1" stop-color="#5e5e5f"></stop></linearGradient></defs><path fill="url(#e)" d="M557 609h1c-1-2-3-2-4-3v-1c1 1 3 2 4 3s3 2 4 3c3 4 8 9 10 14v1 2l-1-1-1-1v1c1 3 1 6 2 9h0v2h-1-1v1h0l-1 1v-4c0-2-1-3-3-5-1-7-6-13-12-18 2 0 2 0 3 1h0v-3-1h-1 0l1-1z"></path><path d="M565 616c2 3 6 7 6 11l-1-1v1h0c-1 0-1-1-1-1-2-3-3-6-4-8l-1-1 1-1z" class="L"></path><path d="M557 609h1c-1-2-3-2-4-3v-1c1 1 3 2 4 3s3 2 4 3c3 4 8 9 10 14v1 2l-1-1c0-4-4-8-6-11l-4-4c-2-1-3-2-4-3z" class="J"></path><path d="M537 312c1 0 2-1 3-1 3 2 9 0 11 3l1 2h-1c0 1 0 1 1 2h1l1 1v1c0 1 1 2 2 3h-1-1v1c3 2 6 5 8 9-1-2-3-3-4-3h-1c4 3 6 6 8 10 0 2 1 3 2 5l-4-5c1 3 2 6 2 9h0c-1-1-1-1-1-2s-1-3-2-4h-1c1 1 1 1 1 2v1l-1 1c1 1 3 3 4 5h-1c-1 0-3-1-4-2l-2-1c-1 0-2-1-3-1s-1 1-1 2h-1-3-3-3-2c-6 1-11 2-15 6-10 13-21 25-33 36-1 2-3 3-4 4-3 2-5 5-8 7-1 1-3 2-4 2l-13 9-1-1h1v-1l1-1 1-1c3-2 6-6 8-9l2 1 4-4c2-2 3-4 4-6l-1 1h0v-2c-1 2-2 4-4 7 0-3 5-11 7-13l4-9h-1c3-6 4-11 5-17 1-1 1-2 1-3 0 2-1 4 0 6v-1c0-1 0-1 1-2v1h0 1l1-1h1l8-17 11-20 6-6c2 0 3-1 5-1h1c1 0 2-1 3-1 1-1 1-1 3-2z" class="T"></path><path d="M549 335c2 1 5 3 8 4h0l-1 1h0c-2-1-4-2-7-2-1-1-1-1-3-1v-1l3-1z" class="C"></path><path d="M537 333h1l-2 1h1 2c-6 2-11 5-17 8h0c2-3 11-8 15-9z" class="B"></path><defs><linearGradient id="f" x1="546.917" y1="335.023" x2="538.053" y2="338.564" xlink:href="#B"><stop offset="0" stop-color="#212020"></stop><stop offset="1" stop-color="#414041"></stop></linearGradient></defs><path fill="url(#f)" d="M528 340h1c6-4 13-7 20-5l-3 1v1c2 0 2 0 3 1-2 0-4 0-6 1-1 0-1-1-1 0h-2c-3 1-6 3-8 4v-2h3l1-1c1-1 2-1 2-1v-1h0c-1 0-1-1-2-1s-6 3-7 3h-1z"></path><path d="M484 396h1c1 0 1-1 2-1l-6 6c-1 1-3 2-3 4l-13 9-1-1h1v-1l1-1 1-1c3-2 6-6 8-9l2 1-3 4h0c4-4 7-6 10-10z" class="G"></path><defs><linearGradient id="g" x1="547.575" y1="339.468" x2="546.27" y2="344.231" xlink:href="#B"><stop offset="0" stop-color="#242424"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#g)" d="M532 343c2-1 5-3 8-4h2c0-1 0 0 1 0h1c3 0 6 0 9 1 2 1 6 6 9 5v1l-1 1c-2-1-5-3-6-3-3-2-7-3-10-3-3 1-5 2-8 3l1-1h1l-1-1-6 2v-1z"></path><path d="M550 330c1-1 2-1 3-1h1c1 1 1 1 2 1h1c4 3 6 6 8 10-1 0-2 0-3-1-1-2-1-3-3-4-2 0-4-2-6-2-3-1-10 0-14 1h-2-1l2-1h-1c2-1 6-2 9-2 1 0 3 0 4-1h0z" class="E"></path><defs><linearGradient id="h" x1="500.439" y1="384.731" x2="492.743" y2="373.993" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#5d5d5c"></stop></linearGradient></defs><path fill="url(#h)" d="M512 356h0c-1 3-3 5-4 7 0 1 1 1 2 1h1c0 1-1 2-2 2 0 1-1 2-1 2-1 2-2 2-2 3l-5 7c-1 2-3 4-4 6-2 2-4 5-6 7l-3 3-1 1h0c-1 0-1 1-2 1h-1l28-40z"></path><defs><linearGradient id="i" x1="530.876" y1="355.264" x2="516.976" y2="347.073" xlink:href="#B"><stop offset="0" stop-color="#4a4a4b"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#i)" d="M528 340h1c1 0 6-3 7-3s1 1 2 1h0v1s-1 0-2 1l-1 1h-3v2 1l6-2 1 1h-1l-1 1c-2 1-5 2-7 4-3 1-7 5-10 7h0c-1 1-2 2-3 2-1 1-3 3-3 4l-3 3h-1c-1 0-2 0-2-1 1-2 3-4 4-7h0c3-6 10-13 16-16z"></path><defs><linearGradient id="j" x1="556.765" y1="345.587" x2="514.904" y2="355.73" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#j)" d="M537 344l8-3c3 0 7 1 10 3 1 0 4 2 6 3 1 1 3 3 4 5h-1c-1 0-3-1-4-2l-2-1c-1 0-2-1-3-1s-1 1-1 2h-1-3-3-3-2c-6 1-11 2-15 6l-1-1c-2 2-3 3-5 4-1 1-1 1-2 1l-3 1 3-3v-1c1-1 1-1 1-2h0c3-2 7-6 10-7 2-2 5-3 7-4z"></path><path d="M549 348h1c1 0 1 0 2-1l3 1c-1 0-1 1-1 2h-1c-1 0-3-1-4-1v-1z" class="C"></path><defs><linearGradient id="k" x1="540.538" y1="351.115" x2="526.966" y2="352.356" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#4d4e4e"></stop></linearGradient></defs><path fill="url(#k)" d="M526 355c2-2 5-4 8-5 2 0 4-1 6-2h9v1c1 0 3 1 4 1h-3-3-3-2c-6 1-11 2-15 6l-1-1z"></path><path d="M544 350c-1 0-1 0-1-1h-1c2-1 5 0 7 0 1 0 3 1 4 1h-3-3-3z" class="B"></path><path d="M537 344l8-3c3 0 7 1 10 3 1 0 4 2 6 3 1 1 3 3 4 5h-1c-1 0-3-1-4-2v-1c-5-4-11-8-18-6-3 1-7 3-10 4-1 1-1 1-2 1 2-2 5-3 7-4z" class="K"></path><defs><linearGradient id="l" x1="507.197" y1="392.089" x2="497.235" y2="367.622" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#504e4f"></stop></linearGradient></defs><path fill="url(#l)" d="M511 364l3-3c0-1 2-3 3-4 1 0 2-1 3-2 0 1 0 1-1 2v1l-3 3 3-1c1 0 1 0 2-1 2-1 3-2 5-4l1 1c-10 13-21 25-33 36-1 2-3 3-4 4-3 2-5 5-8 7-1 1-3 2-4 2 0-2 2-3 3-4l6-6h0l1-1 3-3c2-2 4-5 6-7 1-2 3-4 4-6l5-7c0-1 1-1 2-3 0 0 1-1 1-2 1 0 2-1 2-2z"></path><path d="M537 312c1 0 2-1 3-1 3 2 9 0 11 3l1 2h-1c0 1 0 1 1 2h1l1 1v1c0 1 1 2 2 3h-1-1v1c3 2 6 5 8 9-1-2-3-3-4-3h-1-1c-1 0-1 0-2-1h-1c-1 0-2 0-3 1-9-1-19 2-27 8-3 3-6 6-9 10l-2 4-5 8c-2 5-6 10-9 15-4 6-7 12-13 17l-1 1h0v-2c-1 2-2 4-4 7 0-3 5-11 7-13l4-9h-1c3-6 4-11 5-17 1-1 1-2 1-3 0 2-1 4 0 6v-1c0-1 0-1 1-2v1h0 1l1-1h1l8-17 11-20 6-6c2 0 3-1 5-1h1c1 0 2-1 3-1 1-1 1-1 3-2z" class="J"></path><path d="M499 359h1c-1 3-3 6-5 8l1-2c0-2 1-4 1-5h0 1l1-1z" class="F"></path><path d="M541 321c2-1 3-1 5-1 2 1 3 1 5 2s2 0 3 1v1c-2 0-3-1-4-1l-9-2z" class="E"></path><path d="M495 359c1-1 1-2 1-3 0 2-1 4 0 6v-1c0-1 0-1 1-2v1c0 1-1 3-1 5l-1 2-4 9h-1c3-6 4-11 5-17z" class="L"></path><path d="M522 323c2-2 3-4 6-5 7-3 16-1 23 2 1 0 1 0 2 1h-1s-1 0-2-1c-1 0-3 0-4-1-2 0-3 0-5-1h-2c-2-1-4 1-7 0-2 1-4 2-6 4v-1l-1 1c-1 0-1 1-2 2l-1-1z" class="B"></path><defs><linearGradient id="m" x1="536.099" y1="326.801" x2="528.8" y2="318.152" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#2e2e2f"></stop></linearGradient></defs><path fill="url(#m)" d="M522 323l1 1c1-1 1-2 2-2l1-1v1c2-2 4-3 6-4 3 1 5-1 7 0h2c2 1 3 1 5 1v1c-2 0-3 0-5 1-6-2-13 2-19 6-1 0-3 2-4 3 1-2 2-5 4-7z"></path><defs><linearGradient id="n" x1="546.797" y1="317.771" x2="538.049" y2="309.737" xlink:href="#B"><stop offset="0" stop-color="#18181a"></stop><stop offset="1" stop-color="#383736"></stop></linearGradient></defs><path fill="url(#n)" d="M537 312c1 0 2-1 3-1 3 2 9 0 11 3l1 2h-1c0 1 0 1 1 2h1l1 1v1c-2-1-3-2-4-3h-1c-2 0-3-1-5-1-4-1-8-1-13-1 1 0 2-1 3-1 1-1 1-1 3-2z"></path><path d="M551 314l1 2h-1c0 1 0 1 1 2h1l1 1v1c-2-1-3-2-4-3 0 0 0-1-1-1s-1 0-1-1h-2c2 0 3-1 4 0h1v-1z" class="E"></path><defs><linearGradient id="o" x1="491.065" y1="393.763" x2="496.806" y2="360.382" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#5e5c5c"></stop></linearGradient></defs><path fill="url(#o)" d="M484 391l21-36h0c0 2-1 3-1 4l-2 2c0 1 1 2 2 2v1l1-1c0-1 1-2 2-3-2 5-6 10-9 15-4 6-7 12-13 17l-1 1h0v-2z"></path><defs><linearGradient id="p" x1="530.859" y1="348.373" x2="508.211" y2="341.64" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#p)" d="M515 338c2-3 5-6 8-9 4-3 13-5 18-5 4 1 7 2 10 3 2 0 3 1 5 2 1 0 1 0 2 1h-1-1c-1 0-1 0-2-1h-1c-1 0-2 0-3 1-9-1-19 2-27 8-3 3-6 6-9 10l-2 4-5 8c-1 1-2 2-2 3l-1 1v-1c-1 0-2-1-2-2l2-2c0-1 1-2 1-4h0c0-1 1-3 2-4 2-4 4-9 8-13z"></path><defs><linearGradient id="q" x1="544.487" y1="323.025" x2="538.07" y2="333.191" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#q)" d="M515 338c2-3 5-6 8-9 4-3 13-5 18-5 4 1 7 2 10 3 2 0 3 1 5 2 1 0 1 0 2 1h-1-1c-1 0-1 0-2-1h-1c-1 0-2 0-3 1-9-1-19 2-27 8-3 3-6 6-9 10l-2 4h-1c1-2 3-4 4-6 0 0 1-1 1-2h1c0-1 0-1 1-2v-1c1-1 2-1 3-2h0l2-2h0l2-1h0l1-1h0 1v-1h1c1 0 2-2 4-2l2-1s1 0 2-1l1-1h0l-1-1h-1l-4 2c-1 0-3 1-4 1 1-1 2-1 3-2h1v-1c1 0 1-1 1-2-6 1-9 4-13 9-2 1-2 2-4 3h0z"></path><path d="M379 596h1c3 3 5 6 8 9s5 5 8 7l9 8c3 1 6 3 8 5s3 4 5 6l11 13 11 15h0c-1-3-3-6-5-9l-4-6-8-12 1-1h0 1c1 1 2 2 3 4 6 9 14 17 23 24 5 4 11 8 17 11 3 2 6 4 10 5 5 1 9 4 13 6 3 1 7 2 9 4 1 0 1 0 1 1 1 0 2 0 3 1 1 2 5 3 6 4l7 3c1 0 2 1 3 1 2 1 4 2 7 3l4 1h4l3 1h-4c-2-1-3 0-4-1h-4c0-1-1 0-1 0l-1-1h-1-1s1 0 1 1h-1c0 1 0 1 1 1s2 1 3 1c1 1 2 1 4 1 4 2 9 4 14 2h3 1 3c-1 1-1 0-1 1h-1-1c0 1 0 1-1 1h0-1-1 0l-1 1h-1-1 0-1c-1 0-1 1-2 0l-1 1h-5l-1-1h0c-1 1-2 0-2 0h-2c-1-1-3-2-4-2h0c-1-1-3-2-4-2 1 1 1 1 2 1 0 1 1 2 2 3l9 6h0c-3 0-6 0-9-1-10-1-20-3-28-8-2-1-4-3-5-4 3 5 8 10 13 14-3 0-6-1-9-2-4-2-8-4-11-7-5-6-8-13-13-19-4-6-11-11-17-16 2 3 4 5 7 8 5 6 12 13 14 21 2 3 2 7 2 11-6-8-17-14-26-18l-12-6c-3-2-6-5-9-7 2 4 5 8 8 12-1 0-1-1-2-1-13-5-17-16-20-29l-3-9-1-1v-1c-1-2-1-5-2-7s-2-4-3-5c-1-2-3-4-4-6v-1-1h0-1l-2-2-7-5 1-2v-1c-1-1-3-4-3-5l-1-1v1h0-1l-1-2-3-7c-1-4-3-7-6-10z" class="B"></path><path d="M391 613h-1l-1-1-1-2c0-1 0-2-1-3v-1h0l2 1c0 2 2 4 4 5l-1 1-1-1v1z" class="D"></path><path d="M421 655h0c1 1 2 3 2 4h1v-1h0c2 3 3 6 4 9 0 1 0 0-1 1v-1c-2-2-3-5-4-7v-1c-1-2-2-2-2-4z" class="C"></path><path d="M403 631v-1h0c7 7 13 14 16 23l-6-9c-2-5-6-9-10-13z" class="T"></path><path d="M421 647c1 1 3 5 4 5h0l1 1c1 3 1 6 3 9 5 7 12 12 19 17h0l-6-4a53.56 53.56 0 0 1-15-15c-1-1-2-3-2-4-1-3-2-5-3-7 0-1-1-2-1-2z" class="E"></path><g class="J"><path d="M424 631h0 1c1 1 2 2 3 4 6 9 14 17 23 24 5 4 11 8 17 11 3 2 6 4 10 5h-2c-11-5-24-12-34-21-1-2-3-4-5-6-5-5-9-11-13-17zm-35-24c3 3 5 5 8 7l8 6c3 2 6 4 8 6s4 4 5 6c2 3 4 4 6 7l6 12c2 4 4 7 6 10 1 1 2 3 3 4l-3-3c-2-2-3-4-4-7-4-6-7-12-12-18-4-5-9-10-15-14-1-2-4-3-6-4-2-2-4-5-6-7-2-1-4-3-4-5z"></path><path d="M391 613v-1l1 1c3 2 5 5 8 7 2 1 3 2 5 3 4 4 11 9 14 14l6 15h0c-1 0-3-4-4-5-2-5-5-10-9-14h0l1 1c1 2 3 4 4 6 3 6 4 13 7 18v1h-1c0-1-1-3-2-4h0c-3-10-10-20-18-26l-1 1-2-2-7-5 1-2v-1c-1-1-3-4-3-5 2 2 4 5 6 6l2 1-4-4c-1-2-2-4-4-5z"></path></g><path d="M394 621c2 3 5 5 9 8h0l-1 1-2-2-7-5 1-2z" class="G"></path><path d="M516 400c6-1 15-1 21 2h0c1 0 1 1 2 1s2 0 2 1h-2-1-1l-1 1 1-2h-1c-1 0-1 1-2 1 0 2 1 2 0 4h1 0-2-5c-3-1-6-1-9-1 1 1 1 1 2 1 0 1-1 2-2 2h-2c1 1 1 1 2 1v1c1 0 1 0 2 1h1l4 2c4 1 7 4 10 6 2 2 4 5 6 7 1 1 1 2 2 3h0c3 2 5 5 7 8l2 15c-1-2-2-5-3-7-3-5-8-10-12-13v-1c-1-1-1-1-3-2 1 3 1 4 3 5v2c0 1 0 2 1 3v1h1c0 1 0 1 1 1 1 1 2 3 2 5-1-2-2-3-4-5 2 3 3 6 4 9 4 8 3 17 2 25v1c-2-7-5-12-9-18h0c0 2 1 4 2 6l2 9c-2-2-3-5-5-8 0 2 2 5 2 8-1-2-1-5-3-6h0v2c1 3 1 5 1 7s1 5 0 6c-1-2-1-4-2-6 1 4 1 8 1 11l3 6c-1-1-5-7-6-7h0v9c-2-5-4-9-6-13 0 3 1 7 1 11 0 0-1 1-1 2 0 2-1 5 0 7 0 3 2 5 3 8-4-3-7-5-10-8l1 1c0 2 1 5 0 7h-1v-1h-1c1 2 1 4 2 6h-1-1l-2 2v1c-2 1-3 3-4 5 2-1 2-1 3-3l2-2c-1 4-3 8-6 12h0 1v1 1 1 2l-1 1-3 3-2 2h-1s0 1-1 1h1c1 0 1 0 2 1 0-1 1-2 2-3l1 1 3-5v4 1c0 2-1 4-2 5-2 4-4 8-5 11l-1-1c-1 2-1 4-2 6s-3 4-3 6l-2 7c-2 4-5 6-6 10l-3 3c-2 3-5 6-9 7h-2 0l-1-1c-1 0-2 1-3 2l-1 1-7 6c-1 0-2 2-4 2h-1c0 1-1 1-1 1-1 0-1-1-2-1l1-1h-1-2 0c-1 0-2-1-3-2 1 0 1 0 1-1-2 1-5 2-7 3-8 3-18 5-27 5v-1c-1-1-3 0-5 0 0-1-1 0-2-1h-1l-8-2h-1c-3-1-8-4-10-7h1l-13-7h0v1 2h-1c3 3 5 6 6 10l3 7 1 2h1 0v-1l1 1c0 1 2 4 3 5v1l-1 2c0 2 0 4-1 6 0 2-1 3-1 5-1 0-1-1-1-1h-1l-1-1v2h0 1l-5 1c-1 6-3 13-5 19-5 11-13 20-23 27-4 3-8 4-12 6 7-9 12-19 16-31-7 6-14 8-22 10 7-5 14-8 16-17 1-2 1-5 1-8h-6 2c0-2-1-5-1-8l-2-13h-7c2-1 5-1 7-1l-1-20c0-3 0-6-1-9l1-2c0-6 1-11 3-17 0-2 0-4 1-6s4-5 6-5c1 0 0 0 1-1 1 0 2-1 2-1 2-1 3-2 5-2v-1h-1 0c1-1 2-2 3-2 1 1 3 4 4 4 0-1-2-3-2-4v-1c-1-1-1-2-1-4s-1-3-1-4c-1-2-1-3-1-4-1-1-1-3-1-4 0-2-1-4 0-6v-1c0-1 1-2 2-4l2-2v1h0c-1 3 0 7 1 10h1v-1c0-3-1-6 0-9h0c0-1-1-1-1-2h2c1-1 1 0 0-1h-1l2-1c-7 1-13 4-19 7-2 1-3 3-6 4v-1l-2 2-1 1-3 3c1-2 1-3 3-4v-1c-4 4-6 8-7 13l-1 2h-1l3-9-2-1h0 0l-1-2v2-2l2-12c0-1 0-3 1-4 2-3 4-7 7-10 2-4 6-7 10-10 13-10 29-15 46-13 2 0 5 0 7 1 8 2 15 7 21 12 3 3 5 6 7 8-4-9-12-16-21-21l-7-3c-3 0-5-1-8-1-7-1-14 0-21 1v-1l-4-2 4-1 1-1c-2 1-3 1-4 1-2 1-4 2-6 2l-1 1h-1c-1 1-2 2-3 2 0 1 0 2-1 2h0 0c-1 0-1-1-2 0-2 0-4 2-7 3-1 2-3 3-4 5l-4 4h0c-1 0-2 1-2 1v-1c0-2 2-5 3-8 1-2 1-5 2-7 0-2 0-4 1-6h1l1-1v1c1-1 2-3 2-4 2-4 4-9 7-12 7-11 15-20 25-29 5-5 12-10 16-16 1 1 1 1 1 2 1 2 1 4 1 6 1 0 2-1 3-1h1 0v-1c0 1 0 5 1 6-1 8-5 14-11 19h0c-1 2-2 3-4 4h1l4-4 10-8h1c0 1 3-2 4-2v-1c1 0 3-2 4-3v1h0l-1 3 1-1 3-3c1 0 2-1 3-2 0 1 0 1 1 1h0v2l-2 1c1 1 3-1 4-1 3-2 6-4 9-5l1 1 43-18v-1c2-1 5-3 7-4 3-1 7-2 11-3l16-3z" class="J"></path><path d="M438 571h1l1 2-4-1v-1h2z" class="L"></path><path d="M429 523h2 0l2 2c0 2 0 2-1 3l-3-5zm-63 63c0-2 0-4-1-5l4 4v2l-1-1h-1-1z" class="E"></path><path d="M466 483c2 3 4 8 5 12h0c-1 0-1-1-1-1h0c-1-1-1-1-1-2v-1h0l-1-3-2-4v-1h0z" class="F"></path><path d="M379 596c-1-2-3-3-4-5h1c1 1 1 1 2 1s1 0 2 1h0v1 2h-1z" class="C"></path><path d="M441 560h5c1 0 2 1 4 1h-5-5 0l1-1z" class="U"></path><path d="M407 456c3-2 8-4 12-5-3 2-6 3-9 4h1l-4 1h0z" class="N"></path><path d="M455 428c4-1 6-2 10-2l-2 2h-3-5z" class="C"></path><path d="M465 426h7 1l-2 1c-3 1-5 1-8 1l2-2z" class="B"></path><path d="M465 521v-3c1 4 2 9 1 13-1-1 0-2-1-4v-6z" class="F"></path><path d="M446 560c3-1 5-1 8-2h1l-1 1h0c-1 1-3 1-4 2-2 0-3-1-4-1z" class="S"></path><path d="M439 520l1-1c-1-1-1-3-1-4l1-1c1 1 1 3 1 5v-1h-1v-1-1 1c0 2 0 2 1 3v6c-1-2-2-5-2-6z" class="F"></path><path d="M448 577l1-1h3 0l-3 3h-3c-1 1-2 1-3 1 1-1 2-1 2-2 1 0 2 0 3-1zm13-55v-1-1l1 1c0 4 1 8 0 12v-3h-1v-3-5z" class="E"></path><path d="M436 570h3c2 1 4 1 6 2 0 1 2 0 3 0-2 1-5 1-8 1l-1-2h-1l-2-1z" class="P"></path><path d="M429 568c4 0 7 1 10 1v1h-3c-2 0-6 0-7-2h0z" class="R"></path><path d="M433 560h8l-1 1h0 5c-4 1-10 1-13 0h0l1-1z" class="O"></path><path d="M503 468c1 1 1 1 0 3h0v1c-1 0-2 1-3 1 0 1-1 2-1 3h-2l-1-1v-1h0l1 1h1c1-1 2-3 3-4 0 0 1-1 2-1v-2z" class="B"></path><path d="M366 586h1l3 9-1 5-3-14z" class="D"></path><path d="M408 457c3-1 7-2 10-2 0 0-1 0-2 1 2 0 5-1 7 0-5 0-10 1-15 2v-1z" class="M"></path><path d="M430 564l10 1h-5v1h6c-3 1-10 1-13 0v-1h-2l4-1z" class="H"></path><path d="M369 585l3 8c0 1 1 3 1 5v3c-1-5-3-10-4-14v-2z" class="D"></path><path d="M466 539c1 0 1-1 2-1 0 1-1 1-1 2-1 1-3 4-5 5v-1l-1 1-1-1c2-2 4-3 6-5z" class="L"></path><path d="M510 498v1 4h1v6c0 2-1 5-1 7h-1l1-18z" class="K"></path><path d="M435 524v11c0-1-3-6-3-7 1-1 1-1 1-3v2l1-1s0-1 1-2z" class="F"></path><path d="M428 566c-3-1-7-3-10-5-1-1-3-2-3-4 1 0 1 1 2 2 2 1 4 3 5 4 3 1 6 1 8 1l-4 1h2v1z" class="Q"></path><path d="M381 465l1-2c3-2 6-3 9-4 1-1 3-1 3-1 0 1 0 1-1 1-3 2-6 3-8 5h0c-2 1-3 1-4 1z" class="I"></path><path d="M402 458c2 0 4-1 6-1v1l-12 3s-1 0-1-1c2 0 4-1 7-2z" class="N"></path><path d="M462 433h5c1 1 2 1 4 2l-1 1c1 1 3 2 4 3h-1l-11-6z" class="C"></path><path d="M432 561c-6 0-14-4-18-8v-1c6 6 12 7 19 8l-1 1h0z" class="N"></path><path d="M393 452h0l3-2h1l-1 2h1s-11 6-12 7l8-7z" class="M"></path><path d="M395 524l1-1 4 3 7 11h0c-1 0-2-2-3-3l-5-6c0-1-1-1-1-1-1-1-2-3-3-3z" class="P"></path><path d="M462 559h2c-3 2-7 5-11 5h0 1c0-1 1-1 1-1 1 0 1 0 2-1-1 0-6 2-8 2h0c5-2 9-3 13-5zm-64-115c1 0 2 0 2 1v1h0c-1 2-2 3-4 4l-3 2h0l5-8z" class="S"></path><path d="M497 419h-11c-3 0-7 2-11 1 2-1 14-3 17-3l-2 1c2 1 5 0 7 1z" class="N"></path><path d="M466 539c4-4 8-9 11-15h0v2c-2 4-4 7-6 10-1 1-2 3-4 4 0-1 1-1 1-2-1 0-1 1-2 1z" class="I"></path><path d="M463 428c3 0 5 0 8-1l-1 1c2 0 5-1 6 0h-4v1h-2v1h-3c-3-1-5-1-7-1v-1h3z" class="N"></path><path d="M511 535v2l-1 1-3 3-2 2h-1s0 1-1 1h1c1 0 1 0 2 1-1 0-1 1-2 2l-1-1c-2 0-3 1-4 2h-1l3-3c2-1 3-3 5-4l3-3c1-1 1-2 2-3z" class="C"></path><path d="M411 455h2 0l1-1h1 1c1-1 2 0 3-1h4-1l1 1c-1 0-4 0-5 1-3 0-7 1-10 2-2 0-4 1-6 1 1-1 3-2 5-2h0l4-1z" class="H"></path><path d="M443 428h3 0 0l-5 2c-4 1-7 2-11 3h-1c3-2 6-4 9-5l1 1c-1 0-2 0-2 1h0l6-2z" class="I"></path><path d="M364 515s-1 0-2 1c-2 1-5 2-7 3l-4 4c2-5 10-9 15-12v1c-1 0-1 1-1 1 0 1-1 1-1 2z" class="B"></path><path d="M449 564h0c2 0 7-2 8-2-1 1-1 1-2 1 0 0-1 0-1 1h-1 0c-3 2-8 2-12 2h-6v-1h5c3 0 6-1 9-1z" class="U"></path><path d="M536 421c2 2 4 5 6 7 1 1 1 2 2 3 2 3 5 8 6 11l1 3-1 1c-1-2 0-3-1-5s-3-5-5-7c0-1 0-2-1-3-1-2-3-3-4-5l-3-3h0l-2-1h1 1v-1z" class="B"></path><path d="M486 424c-5 0-10-1-15-1-2 1-4 1-6 2 2-2 5-3 7-3 3 0 7-1 10-1h0c3 1 6 1 9 2h-7v1h2z" class="I"></path><path d="M495 499h1v2c0 1-1 2 0 3 0 1 0 3-1 4 0 1 0 2-1 4 0 2-1 4-2 5-1 2-2 3-3 5-1 3-2 6-4 8h-1s2-2 2-3c2-3 3-6 4-8 0-1 0-1 1-2v-2c1-2 1-3 2-4v-1l1-2s0-1 1-1v-1c1-1 0-5 0-7z" class="F"></path><path d="M432 572h0l-6-3c-3-1-7-3-9-5v-1c2 1 3 2 5 3s5 1 7 2h0c1 2 5 2 7 2l2 1h-2v1h-4z" class="B"></path><path d="M432 572c0-1 0-1 1-1h3v1h-4z" class="G"></path><path d="M483 542v1c-5 6-12 13-19 16h0-2c8-5 15-10 21-17z" class="U"></path><path d="M389 615h1 0v-1l1 1c0 1 2 4 3 5v1l-1 2c0 2 0 4-1 6 0 2-1 3-1 5-1 0-1-1-1-1v-5c1-5 0-8-1-13z" class="D"></path><path d="M443 580c1 0 2 0 3-1h3l-8 7c0 1-1 2-2 2h0s0-1 1-1h0v-1c-1 1-2 1-3 2v-1c1 0 2-1 2-1v-1c1-1 2-1 2-2h0c-1 0-4 2-6 3v-1c0-1 3-2 5-3 1-1 2-2 3-2z" class="C"></path><path d="M410 547c0 1 1 2 1 3h0c0 1 1 2 1 3 1 5 2 10 6 15h-1l-3-3-1-2c0-1-1-1-2-1l-1-4c1-2 0-7-1-9v-1l1-1z" class="S"></path><path d="M483 542v-1c1-1 3-2 4-4l1-2 3-6 5-17v-1h0l1 1-5 18c-1 2-2 4-3 5-1 3-3 6-6 8v-1z" class="F"></path><path d="M455 428h5v1h-9l1 1c5 1 10 1 15 2h0l-1 1h1-5c-3-1-5-1-8-2-3 0-9 1-11-1 4 0 8-2 12-2z" class="L"></path><path d="M522 435c0-1 0-1 1-1l3 2c1 1 4 3 4 4-1 2 0 3 1 4l5 9c1 1 1 2 2 3l2 4v2h0c-1 0-1-1-1-2-1-1-2-3-2-4-5-7-8-16-15-21h0z" class="E"></path><defs><linearGradient id="r" x1="467.246" y1="539.135" x2="456.407" y2="553.514" xlink:href="#B"><stop offset="0" stop-color="#3a3b38"></stop><stop offset="1" stop-color="#656366"></stop></linearGradient></defs><path fill="url(#r)" d="M454 551c2-2 5-3 7-4 3-2 6-5 8-7l2 2h0c-4 4-9 7-14 10-1 0-3 1-4 1v-1c0-1 1-1 1-1z"></path><defs><linearGradient id="s" x1="479.599" y1="533.98" x2="468.786" y2="535.676" xlink:href="#B"><stop offset="0" stop-color="#1d1d1c"></stop><stop offset="1" stop-color="#363637"></stop></linearGradient></defs><path fill="url(#s)" d="M469 540c6-6 10-13 14-21v2-1h1v-2l1 1c-4 7-7 15-12 21-1 0-2 1-2 2h0l-2-2z"></path><path d="M354 475v1c1-1 2-3 2-4 2-4 4-9 7-12h-1l-3 8s1-1 1-2c2-1 3-3 5-4-2 2-4 5-5 7-3 4-6 9-7 14-1 1-2 3-2 5l-2 1c1-2 1-5 2-7 0-2 0-4 1-6h1l1-1z" class="C"></path><defs><linearGradient id="t" x1="489.348" y1="533.94" x2="474.224" y2="540.663" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#404141"></stop></linearGradient></defs><path fill="url(#t)" d="M475 544c3-3 5-7 8-10l6-6c0-1 0-2 1-2-2 8-7 14-13 19h0s-1 0-1-1h-1z"></path><defs><linearGradient id="u" x1="463.642" y1="549.642" x2="465.487" y2="554.736" xlink:href="#B"><stop offset="0" stop-color="#4e504f"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#u)" d="M475 544h1c0 1 1 1 1 1-1 1-2 3-3 4-6 4-12 9-20 10h0l1-1h-1l6-3c6-3 11-6 15-11z"></path><path d="M476 544c0 1 1 1 1 1-1 1-2 3-3 4 1-2 1-3 2-5zm30 1c0-1 1-2 2-3l1 1-3 3c-1 1-2 2-2 4-1 1-3 2-4 3-3 3-7 5-11 7-5 3-10 7-16 8h0c0-1 0-1-1-1 1-1 7-3 9-3 5-3 10-7 15-10 1-1 4-2 5-4l3-3c1-1 1-2 2-2z" class="H"></path><path d="M526 436c1 0 1 0 2 1h1l-1-1h0v-2h0l1 1h0c2 3 4 6 7 8v1c-1 0-1-1-2-1h0c3 6 6 10 9 16 1 2 1 4 1 6v1c-1-1-1-2-1-3s-1-2-2-4c-3-6-7-13-11-19 0-1-3-3-4-4z" class="D"></path><path d="M385 464c3-1 7-4 10-4 0 1 1 1 1 1-3 2-6 2-9 3-8 3-16 7-22 12-3 2-6 4-8 6l-2 3h-1c4-5 9-10 14-14 4-2 9-4 13-6 1 0 2 0 4-1z" class="H"></path><path d="M442 469c3 0 6 4 8 6 1 1 5 5 5 7v1c0 1 2 4 3 5 1 3 3 6 3 9-2-2-3-6-5-8l-14-20zm24-21c1 0 2 1 3 2 1 0 2 1 3 2v-1l-1-1h1 0 1c3 4 5 9 7 14h0c-1-2-2-3-3-5-1 0-1 0-1-1h0l-3-2v1l2 3c3 4 5 9 7 14 1 2 2 4 1 6-2-6-5-12-9-18 0-1-1-3-2-4v-1h0c0-1-1-2-1-3l-2-2c0-1-1-1-1-2l-2-2h0 0z" class="C"></path><path d="M423 432c1 0 2-1 3-2 0 1 0 1 1 1h0v2l-2 1c1 1 3-1 4-1h1c1 1 2 1 3 1l-10 5c-3 1-6 3-8 5l-6 4c0-1 1-2 2-2l2-2h0l1-1c1-1 2-2 4-2v-1h-2 0c-1 1-1 2-2 2h-1c-1 2-3 3-4 4h-1c2-3 5-4 7-6l4-4 1-1 3-3z" class="F"></path><path d="M423 432c1 0 2-1 3-2 0 1 0 1 1 1h0v2l-2 1v1c-4 2-5 4-10 5l4-4 1-1 3-3z" class="C"></path><path d="M423 432c1 0 2-1 3-2 0 1 0 1 1 1h0l-6 5-1-1 3-3z" class="F"></path><defs><linearGradient id="v" x1="466.376" y1="463.264" x2="464.361" y2="449.129" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#v)" d="M454 450h5c2 0 3 0 5-1 1 1 1 1 2 1l2 2h1l2 2c0 1 1 2 1 3h0v1c1 1 2 3 2 4v2l-1-1c-4-6-12-11-19-13z"></path><defs><linearGradient id="w" x1="411.031" y1="445.34" x2="404.245" y2="440.513" xlink:href="#B"><stop offset="0" stop-color="#69696c"></stop><stop offset="1" stop-color="#858584"></stop></linearGradient></defs><path fill="url(#w)" d="M420 432v1h0l-1 3-4 4c-2 2-5 3-7 6-4 2-7 4-11 6h-1l1-2 4-4 10-8h1c0 1 3-2 4-2v-1c1 0 3-2 4-3z"></path><path d="M415 444c2 0 3-1 5-2v1h1c3 0 5-1 7-1v1c-1 0-2 0-2 1-2 0-4 1-5 2 1 1 1 1 2 1h1c-3 1-6 2-10 3-2 0-5 2-7 2-1 1-2 1-3 1-2 1-4 2-7 3v-1c1 0 2-1 3-1 1-1 3-1 4-2s3-3 4-3l1-1 6-4z" class="L"></path><path d="M415 444c2 0 3-1 5-2v1h1l-9 5c-1 0-3 1-4 1h0l1-1 6-4z" class="P"></path><defs><linearGradient id="x" x1="412.54" y1="426.311" x2="396.456" y2="440.1" xlink:href="#B"><stop offset="0" stop-color="#4e4e4f"></stop><stop offset="1" stop-color="#868485"></stop></linearGradient></defs><path fill="url(#x)" d="M406 423c1 0 2-1 3-1h1 0v-1c0 1 0 5 1 6-1 8-5 14-11 19h0 0v-1c0-1-1-1-2-1 4-7 8-13 8-21z"></path><path d="M419 513c6 4 11 12 13 19 0 3 1 8 0 11h-1c0-2 0-4-1-6h0l-1-1c1-1 0-1 0-2-3-7-7-11-12-16h1 1 1c0-1 0-1-1-2 0-1-1-2 0-3z" class="D"></path><defs><linearGradient id="y" x1="491.125" y1="431.832" x2="491.914" y2="427.338" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#y)" d="M473 426c3-1 7 0 11 0 10 0 19 3 28 7v1 1c-1 0-2 1-3 1-10-5-21-7-33-8-1-1-4 0-6 0l1-1 2-1z"></path><defs><linearGradient id="z" x1="434.871" y1="465.034" x2="432.519" y2="452.153" xlink:href="#B"><stop offset="0" stop-color="#494a4b"></stop><stop offset="1" stop-color="#787676"></stop></linearGradient></defs><path fill="url(#z)" d="M423 454c10-2 21 0 29 6-1 2-2 1-4 3h-1c-3-1-5-2-8-3-5-3-10-4-16-4-2-1-5 0-7 0 1-1 2-1 2-1 1-1 4-1 5-1z"></path><path d="M362 585c3 14 7 28 7 42 1 5 1 10 0 15v-3c0 1 0 1-1 1v-1-6c0 1 0 5-1 6v-3-7l-6-41c1-1 1-2 1-3z" class="D"></path><path d="M512 538v4 1c0 2-1 4-2 5-2 4-4 8-5 11l-1-1c-1 2-1 4-2 6s-3 4-3 6l-1-1h0c2-3 3-8 4-12l2-4c-4 4-8 9-13 12l-1-1c0 1-1 1-1 1l-3 2v-1s1 0 1-1l10-8c3-2 5-5 7-7l3-3-1-1 3-3 3-5z" class="B"></path><path d="M490 564l12-10 3-3c1-2 3-4 5-6l-4 10c-1 1-1 2-2 3-1 2-1 4-2 6s-3 4-3 6l-1-1h0c2-3 3-8 4-12l2-4c-4 4-8 9-13 12l-1-1z" class="J"></path><path d="M457 546c1-1 2-1 2-2l1-1v1h0 0l1 1 1-1v1c-1 2-4 3-7 4 0 1-1 1-1 2 0 0-1 0-1 1v1c-2 1-5 2-8 2l-9 1c-7 0-16-2-22-8 9 3 20 6 29 3h0 1c3 1 3-1 6-1 1 0 2 0 2-1l2-2c2 0 2 0 3-1z" class="H"></path><path d="M453 552v1c-2 1-5 2-8 2h-3 0c4-1 7-2 11-3z" class="M"></path><defs><linearGradient id="AA" x1="512.103" y1="432.249" x2="512.552" y2="417.097" xlink:href="#B"><stop offset="0" stop-color="#222223"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#AA)" d="M492 417c4-1 8-1 13 0 8 0 16 4 23 9 2 1 5 5 7 5 1 3 1 4 3 5v2c-3-2-6-5-9-8-6-4-14-9-22-10-3-1-7-1-10-1-2-1-5 0-7-1l2-1z"></path><path d="M463 566h0 2c0-1 0-1 1-1h2c1-1 1-1 2-1l6-3c0 1-1 2-2 2-2 1-2 2-3 3l-5 2 6-1c1 0 1 0 1 1-8 2-16 5-25 4-1 0-3 1-3 0-2-1-4-1-6-2v-1c8-1 14-2 21-4h1-1v1h3z" class="U"></path><defs><linearGradient id="AB" x1="446.328" y1="582.469" x2="435.676" y2="609.507" xlink:href="#B"><stop offset="0" stop-color="#262625"></stop><stop offset="1" stop-color="#6a696b"></stop></linearGradient></defs><path fill="url(#AB)" d="M413 605c21 0 37-12 53-25v1c-3 3-7 7-11 10v1c-1 2-3 3-5 4 0 2-5 4-6 5-1 0-2 1-3 1-3 3-11 4-15 4l-2-2h-1l-1 1c-3 1-9 0-13 0h4z"></path><path d="M450 596h-1c1-1 3-4 5-4l1-1h0v1c-1 2-3 3-5 4z" class="B"></path><defs><linearGradient id="AC" x1="469.649" y1="473.338" x2="448.781" y2="474.914" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#494a4a"></stop></linearGradient></defs><path fill="url(#AC)" d="M452 460c9 5 16 15 19 25l1 7v-2c-2-4-5-8-7-11-3-5-6-8-10-11l6 6 5 9h0c-1 0-1-1-1-2h-1 0c0-1 0-1-1-1v-1 1s0 1 1 2h0v2h0v1c-1-3-2-6-4-9h0c-1-3-5-8-9-9l-4-4h1c2-2 3-1 4-3z"></path><path d="M439 467c-1-1-3-2-5-3-9-3-18-5-27-3-4 1-7 2-10 2 1-1 6-2 7-3 9-1 19-3 28-2 6 2 13 5 18 8v1c-1 0-5 2-6 1-2 0-2-1-3-1h-2z" class="N"></path><defs><linearGradient id="AD" x1="435.128" y1="461.74" x2="435.729" y2="450.42" xlink:href="#B"><stop offset="0" stop-color="#383839"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#AD)" d="M419 451c12-3 27-1 38 4 2 2 3 2 5 3-1 1-1 2-1 3s-1 2-2 2l-5-3-1-1c-1-1-1-1-2-1h-1c-2-2-4-3-7-4h-1c-1 0-2-1-3-1h-2 0 0c-4 0-7-1-11 0h-3-4c-1 1-2 0-3 1h-1-1l-1 1h0-2-1c3-1 6-2 9-4z"></path><defs><linearGradient id="AE" x1="478.643" y1="469.878" x2="460.882" y2="472.358" xlink:href="#B"><stop offset="0" stop-color="#121112"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#AE)" d="M437 453h0 0 2c1 0 2 1 3 1h1c3 1 5 2 7 4h1c1 0 1 0 2 1l1 1 5 3c1 0 2-1 2-2s0-2 1-3c-2-1-3-1-5-3 12 4 18 14 23 24 1 2 1 5 2 7-3-2-5-5-7-8h0c-1 0-2-1-3-2-3-2-6-6-9-8-4-5-10-9-16-11-3-2-7-3-10-4z"></path><path d="M472 476h0c0-2-2-4-3-5h1c2 1 5 4 5 7-1 0-2-1-3-2z" class="F"></path><defs><linearGradient id="AF" x1="517.082" y1="451.332" x2="491.079" y2="447.891" xlink:href="#B"><stop offset="0" stop-color="#262525"></stop><stop offset="1" stop-color="#6f6f71"></stop></linearGradient></defs><path fill="url(#AF)" d="M490 433c4 2 8 4 12 7s8 7 12 10c1 1 1 3 2 4 0 1 2 3 2 4-1 0-1 0-1-1 0 1-1 2 0 3 0 0 1 1 1 2h0l-5-8h-1c0 1 1 2 2 3 1 2 2 3 3 5 3 6 5 11 7 17l-1 1-1-2v-1-1c0-1 0-2-1-3v-1l-1-1v-1l-1-1v-1c0-1-1-3-2-4s-2-5-3-6c0 1-1 1-1 2s1 2-1 3l1 2v2h0v1h-1 0v-2c0-1-1-2-1-3v-1c0-1 0-1-1-2l-1-2h0c0-1-1-2-1-3l-1-2s-1-1-1-2c-1-1-2-3-3-4-1-2-3-3-4-5-3-2-6-5-9-7l-1-1 1-1z"></path><defs><linearGradient id="AG" x1="448.59" y1="448.6" x2="448.666" y2="432.925" xlink:href="#B"><stop offset="0" stop-color="#2f2f30"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#AG)" d="M433 434c8-3 16-2 24 2 6 3 11 9 16 14h-1 0-1l1 1v1c-1-1-2-2-3-2-1-1-2-2-3-2l-4-4c-5-3-13-6-18-7h-12c-3 1-6 2-9 2h0l10-5z"></path><defs><linearGradient id="AH" x1="419.794" y1="600.278" x2="419.818" y2="609.171" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#AH)" d="M393 600c4 1 8 3 12 4 3 0 5 0 8 1h-4c4 0 10 1 13 0l1-1h1l2 2c4 0 12-1 15-4 3 0 5 1 7 1l1 1c-8 3-16 5-24 6h-6c-1-1-3 0-5 0 0-1-1 0-2-1h-1l-8-2h-1c-3-1-8-4-10-7h1z"></path><defs><linearGradient id="AI" x1="466.246" y1="487.077" x2="444.205" y2="487.069" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#424142"></stop></linearGradient></defs><path fill="url(#AI)" d="M450 466c1 0 1 1 1 1 4 1 8 6 9 9h0v1 1c1 0 1 1 1 2h0c1 0 1 0 1 1 2 8 5 14 5 23v4c-1 0-1 0-1 1-1-2 0-4-1-6-1-5-2-9-5-14-1-2-3-5-5-7 0-2-4-6-5-7-2-2-5-6-8-6l-3-2h2c1 0 1 1 3 1 1 1 5-1 6-1v-1z"></path><defs><linearGradient id="AJ" x1="390.75" y1="515.857" x2="390.92" y2="505.859" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#727172"></stop></linearGradient></defs><path fill="url(#AJ)" d="M366 511l6-2c14-5 28-5 41 1l6 3c-1 1 0 2 0 3 1 1 1 1 1 2h-1-1-1l-5-3c-2 0-5-2-7-3s-5-1-7-2l-7-1c-10 0-18 2-27 6 0-1 1-1 1-2 0 0 0-1 1-1v-1z"></path><path d="M391 509c1-1 3-1 5-1v1h1 1 2v1h0-2l-7-1z" class="O"></path><path d="M413 510l6 3c-1 1 0 2 0 3 1 1 1 1 1 2h-1-1-1l-5-3c1 0 2 0 3 2l1-2c1-1 0-2 0-3-1 0-2-1-3-2h0z" class="G"></path><defs><linearGradient id="AK" x1="482.909" y1="480.154" x2="488.138" y2="442.887" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#AK)" d="M474 439c2 1 3 2 5 3s5 4 6 6c4 5 8 11 10 17v3 2h0c1 2 0 4 0 6h0c0 1 0 1-1 2h0l-1 1c-1 0-2 1-3 1 0 1 0 0-1 1h-2-1v2c-1 2-2 3-2 4v1c-1 2-2 5-4 7 0 1-1 2-2 3h0 0l3-6c5-9 8-19 7-29s-7-18-15-24h1z"></path><defs><linearGradient id="AL" x1="489.956" y1="531.279" x2="496.299" y2="546.929" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient></defs><path fill="url(#AL)" d="M489 543c3-4 5-8 8-13 1-1 2-3 2-4l1-1h0l1-1v1h1l-1 1c0 1-1 2-2 4v4h1c2-1 5-5 7-6 0 2-2 3-3 4-2 2-4 5-6 8l-10 11c-2 2-3 4-5 6-3 3-7 6-11 9h-1c1-1 1-2 3-3 1 0 2-1 2-2l-6 3c-1 0-1 0-2 1h-2c-1 0-1 0-1 1h-2 0-3v-1h1-1c8-4 16-8 22-14 2-2 5-5 7-8z"></path><defs><linearGradient id="AM" x1="474.73" y1="548.166" x2="477.512" y2="560.834" xlink:href="#B"><stop offset="0" stop-color="#454440"></stop><stop offset="1" stop-color="#747478"></stop></linearGradient></defs><path fill="url(#AM)" d="M489 543c0 1 0 2-1 2l1 1-4 5c-1 2-3 4-5 5h0c0 1 0 0-1 1 0 0-1 0-1 1-1 0-1 0-2 1l-1 1-7 3v1h-2 0c-1 1-1 1-2 1 0 0-1 0-1 1h0-3v-1h1-1c8-4 16-8 22-14 2-2 5-5 7-8z"></path><path d="M442 492c5 6 10 12 14 18 2 4 4 8 5 12v5 3h1v3c0 1 0 3-1 4 0 2-3 4-3 6 0 1 0 1-1 2v1h0c-1 1-1 1-3 1l-2 2c0 1-1 1-2 1-3 0-3 2-6 1h-1l2-1c3-2 6-5 7-8s2-5 2-8c1-3 1-6 1-8 0-3 0-5-1-7v-1c1-1 1-2 1-3 0-6-5-13-9-18-1-1-3-3-4-5z" class="J"></path><path d="M461 530h1v3c0 1 0 3-1 4 0 2-3 4-3 6 0 1 0 1-1 2v1h0c-1 1-1 1-3 1 1-1 3-2 3-4 2-5 4-8 4-13z" class="D"></path><path d="M453 542c0 2-1 3-2 4s-1 2-2 3c4-3 6-6 7-10 1-2 1-3 2-4-1 5-3 9-6 13v1c0 1-1 1-2 1-3 0-3 2-6 1h-1l2-1c3-2 6-5 7-8h1z" class="G"></path><path d="M455 515c1 4 2 8 2 12v4c-2 4-2 8-4 11h-1c1-3 2-5 2-8 1-3 1-6 1-8 0-3 0-5-1-7v-1c1-1 1-2 1-3z" class="E"></path><defs><linearGradient id="AN" x1="507.055" y1="436.768" x2="506.917" y2="420.328" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#AN)" d="M482 421c6 0 13-1 19-1 9 1 18 4 25 10l7 7 3 3c1 1 2 1 2 2h0l-1 1c-1-2-2-3-4-4-1-1-2-3-4-4h0l-1-1h0v2h0l1 1h-1c-1-1-1-1-2-1l-3-2c-1 0-1 0-1 1-5-4-10-6-16-7-6-2-13-3-20-4h-2v-1h7c-3-1-6-1-9-2h0z"></path><defs><linearGradient id="AO" x1="486.033" y1="570.073" x2="444.065" y2="604.682" xlink:href="#B"><stop offset="0" stop-color="#121112"></stop><stop offset="1" stop-color="#565657"></stop></linearGradient></defs><path fill="url(#AO)" d="M491 565c5-3 9-8 13-12l-2 4-2 2c-2 1-3 3-4 4h0c-5 2-8 6-12 10a30.44 30.44 0 0 0-8 8c-2 3-5 5-7 8-6 6-13 12-20 15l-1-1c-2 0-4-1-7-1 1 0 2-1 3-1 1-1 6-3 6-5 2-1 4-2 5-4l13-10c4-4 8-8 13-12 1-1 3-3 5-4v1l3-2s1 0 1-1l1 1z"></path><path d="M491 565c5-3 9-8 13-12l-2 4-2 2c-2 1-3 3-4 4h0c-5 2-8 6-12 10a30.44 30.44 0 0 0-8 8h0l1-2h0c1-1 3-3 4-5h0l-5 5h0c2-3 4-5 7-7 1-1 3-3 5-4l3-3h0z" class="I"></path><defs><linearGradient id="AP" x1="480.303" y1="470.353" x2="488.789" y2="438.14" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#AP)" d="M467 432c11 2 22 8 29 18l4 7c0 1 0 3 1 4v2c0 1 1 3 2 4v1 2c-1 0-2 1-2 1-1 1-2 3-3 4h-1l-1-1h0l-1 2c0-2 1-4 0-6h0v-2-3c-2-6-6-12-10-17-1-2-4-5-6-6s-3-2-5-3c-1-1-3-2-4-3l1-1c-2-1-3-1-4-2h-1l1-1z"></path><path d="M474 439c-1-1-3-2-4-3l1-1c11 6 19 12 23 24 1 2 2 7 2 9h-1v-3c-2-6-6-12-10-17-1-2-4-5-6-6s-3-2-5-3z" class="J"></path><defs><linearGradient id="AQ" x1="408.904" y1="576.418" x2="402.105" y2="597.477" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#AQ)" d="M358 571c-1-2-2-5-5-6 0-1 1-1 1-2 1 0 1 1 1 1 2 4 5 7 8 10 5 5 12 11 19 13 1 0 1 1 2 1h1 0c1 0 1 1 2 1h1l1 1 3 1h1 0c1 0 1 1 1 1h2 1c1 1 1 1 2 0l1 1h6 5 5c1-1 2 0 3-1h1 1s1 0 1-1h3l4-2h1c3-1 6-3 9-4v1s-1 1-2 1v1c1-1 2-1 3-2v1h0c-1 0-1 1-1 1h0 0c-7 7-17 10-26 10-14 1-27-4-38-12-6-4-11-9-16-14l-1-1z"></path><path d="M396 519c1 0 2 1 3 1l8 5c2 2 4 4 5 6l1-1 1-1c1-1 1-2 1-2l3 3c0 1 0 0 1 1 0 1 1 1 2 2v-2-1c0-1-1-2-2-3h1l3 4v-2l1 2 1-1c1 2 3 4 4 6v-2c0 1 1 1 0 2l1 1h0c1 2 1 4 1 6h1c0 1 0 1-1 2-2 1-5 0-7 0-2-1-3-1-5-2s-4-2-5-3c-6-4-10-9-14-14h0l-4-3-1 1-1-1h0l-1-2v-1h0 1l1 1h0c0-1 1-1 1-1v-1z" class="T"></path><path d="M430 537h0c1 2 1 4 1 6h1c0 1 0 1-1 2-2 1-5 0-7 0l1-1h3 1v-2c1-2 1-3 1-5z" class="F"></path><path d="M416 539c3 2 6 4 9 5l-1 1c-2-1-3-1-5-2s-4-2-5-3l2-1z" class="G"></path><path d="M423 531v-2l1 2 1-1c1 2 3 4 4 6v-2c0 1 1 1 0 2 0 1 0 2-1 3-1-3-4-5-5-8z" class="F"></path><path d="M412 531l1-1 1-1c1-1 1-2 1-2l3 3c0 1 0 0 1 1 0 1 1 1 2 2v-2-1c1 2 2 5 3 7l-4-4-1-1c-1 0-1-1-2-2h0c-1 2 2 5 3 7 0 1 1 3 1 4-3-3-6-7-9-10z" class="E"></path><defs><linearGradient id="AR" x1="416.011" y1="536.79" x2="395.578" y2="524.039" xlink:href="#B"><stop offset="0" stop-color="#373737"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#AR)" d="M393 520h1l1 1h0c0-1 1-1 1-1 2 1 4 2 5 4 6 4 10 10 15 15l-2 1c-6-4-10-9-14-14h0l-4-3-1 1-1-1h0l-1-2v-1h0z"></path><path d="M393 520h1l1 1h0c2 1 4 3 5 5h0l-4-3-1 1-1-1h0l-1-2v-1h0z" class="H"></path><path d="M393 520h1l1 1h0v1l-1 1-1-2v-1h0z" class="P"></path><defs><linearGradient id="AS" x1="495.571" y1="434.05" x2="494.779" y2="409.05" xlink:href="#B"><stop offset="0" stop-color="#0a0a0b"></stop><stop offset="1" stop-color="#6c6b6c"></stop></linearGradient></defs><path fill="url(#AS)" d="M443 428c6-2 12-4 18-7 11-4 23-11 35-13l1 1c2 1 7 0 9 0 3-1 8 0 10 1h1c1 1 1 1 2 1v1c1 0 1 0 2 1h1l4 2c4 1 7 4 10 6v1h-1-1l2 1h0l3 3c1 2 3 3 4 5 1 1 1 2 1 3 0 0-1 0-1-1-1 0-2-1-2-2l-6-6c-4-2-7-5-11-7-12-5-29-5-42-2-4 2-9 4-14 5l-22 7h0 0-3z"></path><path d="M506 409h2-2z" class="M"></path><path d="M526 415c4 1 7 4 10 6v1h-1-1 0c-1-1-1-1-2-1l-1-1h2c-1-1-2-2-4-3-1 0-2-1-3-2z" class="L"></path><defs><linearGradient id="AT" x1="443.392" y1="451.865" x2="441.559" y2="437.737" xlink:href="#B"><stop offset="0" stop-color="#3d3d3e"></stop><stop offset="1" stop-color="#737272"></stop></linearGradient></defs><path fill="url(#AT)" d="M423 439h0c3 0 6-1 9-2h12c5 1 13 4 18 7l4 4h0 0l2 2c0 1 1 1 1 2h-1l-2-2c-1 0-1 0-2-1-2 1-3 1-5 1h-5c-10-3-20-5-30-3h-1c-1 0-1 0-2-1 1-1 3-2 5-2 0-1 1-1 2-1v-1c-2 0-4 1-7 1h-1v-1c-2 1-3 2-5 2 2-2 5-4 8-5z"></path><path d="M421 443c5-2 11-2 16-2l-11 3c0-1 1-1 2-1v-1c-2 0-4 1-7 1z" class="H"></path><path d="M423 439h0c3 0 6-1 9-2h12c5 1 13 4 18 7l4 4h0 0l2 2c0 1 1 1 1 2h-1l-2-2c-1 0-1 0-2-1 0 0 1 0 1-1-1-1-3-3-5-4-1-1-3-2-5-3-11-5-23-3-35 1-2 1-3 2-5 2 2-2 5-4 8-5z" class="G"></path><path d="M516 400c6-1 15-1 21 2h0c1 0 1 1 2 1s2 0 2 1h-2-1-1l-1 1 1-2h-1c-1 0-1 1-2 1 0 2 1 2 0 4h1 0-2-5c-3-1-6-1-9-1 1 1 1 1 2 1 0 1-1 2-2 2h-2-1c-2-1-7-2-10-1-2 0-7 1-9 0l-1-1c-12 2-24 9-35 13-6 3-12 5-18 7l-6 2h0c0-1 1-1 2-1l43-18v-1c2-1 5-3 7-4 3-1 7-2 11-3l16-3z" class="J"></path><path d="M496 408c7-2 16-2 23-1 1 1 1 1 2 1 0 1-1 2-2 2h-2-1c-2-1-7-2-10-1-2 0-7 1-9 0l-1-1z" class="M"></path><defs><linearGradient id="AU" x1="486.531" y1="409.893" x2="513.718" y2="401.838" xlink:href="#B"><stop offset="0" stop-color="#414243"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#AU)" d="M489 406c3-1 7-2 11-3l16-3-1 1c-1 0-3 0-4 1 1 0 2 1 3 1l2 2h0c-11 0-23 2-34 6v-1c2-1 5-3 7-4z"></path><defs><linearGradient id="AV" x1="539.103" y1="408.473" x2="512.889" y2="397.697" xlink:href="#B"><stop offset="0" stop-color="#646465"></stop><stop offset="1" stop-color="#8b8989"></stop></linearGradient></defs><path fill="url(#AV)" d="M516 400c6-1 15-1 21 2h0c1 0 1 1 2 1s2 0 2 1h-2-1-1l-1 1 1-2h-1c-1 0-1 1-2 1 0 2 1 2 0 4h1 0-2c-6-2-11-2-17-3h0l-2-2c-1 0-2-1-3-1 1-1 3-1 4-1l1-1z"></path><defs><linearGradient id="AW" x1="409.451" y1="425.176" x2="353.419" y2="458.277" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#AW)" d="M404 415c1 1 1 1 1 2v1c-1 2 0 3-1 6l-3 6c-9 14-26 19-36 32-2 1-3 3-5 4 0 1-1 2-1 2l3-8h1c7-11 15-20 25-29 5-5 12-10 16-16z"></path><defs><linearGradient id="AX" x1="484.774" y1="455.187" x2="493.479" y2="435.503" xlink:href="#B"><stop offset="0" stop-color="#312f30"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#AX)" d="M460 429c2 0 4 0 7 1h3 3 0c2-1 4 0 6 0h0c4 0 8 2 11 3h0l-1 1 1 1c3 2 6 5 9 7 1 2 3 3 4 5 1 1 2 3 3 4 0 1 1 2 1 2l1 2c0 1 1 2 1 3h0l1 2c1 1 1 1 1 2v1c0 1 1 2 1 3-1 0-1-1-1-2l-1-1h0l-1 1h0v1l-1 1h0c0 1 1 2 0 3v1h-1s-1 0-1 1h-1c0-1-1-1-1-2v-2c-1 0-1-1-1-1v-1 1 1c-1-1-2-3-2-4v-2c-1-1-1-3-1-4l-4-7c-7-10-18-16-29-18h0c-5-1-10-1-15-2l-1-1h9z"></path><path d="M490 435c3 2 6 5 9 7 1 2 3 3 4 5 1 1 2 3 3 4 0 1 1 2 1 2l1 2c0 1 1 2 1 3h0l1 2c1 1 1 1 1 2v1c0 1 1 2 1 3-1 0-1-1-1-2l-1-1h0l-1 1h0v1l-1 1h0c0 1 1 2 0 3v1h-1s-1 0-1 1h-1c0-1-1-1-1-2v-2c-1 0-1-1-1-1v-1 1 1c-1-1-2-3-2-4v-2c-1-1-1-3-1-4 0 1 0 2 1 2h0c1 1 1 2 1 3h0v1h0l1-1v1h1c1 0 2-1 2-2 0-3 0-6-1-8-1 0-1-1-1-1-1-4-6-9-9-12-2-2-5-3-5-5z" class="B"></path><path d="M460 429c2 0 4 0 7 1h3 3c10 3 19 9 24 18 2 4 3 7 4 11h0c-1 0-1-1-1-2l-4-7c-7-10-18-16-29-18h0c-5-1-10-1-15-2l-1-1h9z" class="J"></path><defs><linearGradient id="AY" x1="469.837" y1="506.104" x2="397.763" y2="478.214" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#5b5b5c"></stop></linearGradient></defs><path fill="url(#AY)" d="M400 466c16-1 29 4 42 15 7 6 14 15 19 24 2 4 3 8 4 13v3c-2-6-5-11-9-16 0-1-1-2-2-4 3 6 8 13 8 20l-1-1v1 1c-1-4-3-8-5-12-4-6-9-12-14-18 0-1-5-5-5-6h-1c-1-1-3-3-4-3v-1c-2-1-3-2-5-3h0c-1-1-2-1-2-2h-1 0-1l-1-1c-7-4-15-6-24-6 1-1 2-1 3 0h2v-1c0-1 0-1-1-2-1 0-1-1-2-1z"></path><defs><linearGradient id="AZ" x1="382.558" y1="534.682" x2="382.379" y2="511.499" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#AZ)" d="M345 531h0c3-4 7-7 12-10 13-9 29-13 45-9 10 2 17 9 23 18l-1 1-1-2v2l-3-4h-1c1 1 2 2 2 3v1 2c-1-1-2-1-2-2-1-1-1 0-1-1l-3-3s0 1-1 2l-1 1-1 1c-1-2-3-4-5-6-3-2-5-3-8-5-1 0-2-1-3-1v1s-1 0-1 1h0l-1-1h-1l-1-1-3-1c-4 0-10-1-14 1l-2 1c-7 1-13 4-19 7-2 1-3 3-6 4v-1l-2 2-1 1-3 3c1-2 1-3 3-4v-1z"></path><path d="M408 519l1 1 2 2h-1l-3-2h0l1-1z" class="L"></path><path d="M389 518h3c1 0 3 1 4 1v1s-1 0-1 1h0l-1-1h-1l-1-1-3-1z" class="M"></path><path d="M415 527l1-2h0c-1-2-2-3-3-5h0c1 0 2 0 2 1 1 1 1 1 2 1v1c0 1 1 1 1 2l1 1h-1l1 1c1 1 2 2 2 3v1 2c-1-1-2-1-2-2-1-1-1 0-1-1l-3-3z" class="I"></path><defs><linearGradient id="Aa" x1="396.299" y1="482.421" x2="394.858" y2="471.682" xlink:href="#B"><stop offset="0" stop-color="#575757"></stop><stop offset="1" stop-color="#6e6d6e"></stop></linearGradient></defs><path fill="url(#Aa)" d="M381 470c6-3 12-4 19-4 1 0 1 1 2 1 1 1 1 1 1 2v1h-2c-1-1-2-1-3 0 9 0 17 2 24 6l-6-2v1l-1 1c-1 0-2 1-3 2l-5 3h1l1 1h0c-3 0-5-1-8-1-7-1-14 0-21 1v-1l-4-2 4-1 1-1c-2 1-3 1-4 1-2 1-4 2-6 2l-1 1h-1c-1 1-2 2-3 2 0 1 0 2-1 2h0 0c-1 0-1-1-2 0-2 0-4 2-7 3-1 2-3 3-4 5l-4 4h0c-1 0-2 1-2 1v-1c0-2 2-5 3-8l2-1c1-1 1-2 3-3h1l2-3c2-2 5-4 8-6h1c5-3 10-5 15-6z"></path><path d="M366 480c2 1 5-1 7-2l3-1h0c3-1 6-1 8-1 2-1 3 0 4-1h7c0 1-1 1-1 1h-2l-11 1h0c-2 1-3 1-4 1-2 1-4 2-6 2l-1 1h-1c-1 1-2 2-3 2 0 1 0 2-1 2h0 0c-1 0-1-1-2 0-2 0-4 2-7 3h0s1-1 1-2h0l9-6z" class="L"></path><path d="M381 470c6-3 12-4 19-4 1 0 1 1 2 1 1 1 1 1 1 2v1h-2c-1-1-2-1-3 0 9 0 17 2 24 6l-6-2c-17-4-33-3-49 6h-1l-9 6h0c0 1-1 2-1 2h0c-1 2-3 3-4 5l-4 4h0c-1 0-2 1-2 1v-1c0-2 2-5 3-8l2-1c1-1 1-2 3-3h1l2-3c2-2 5-4 8-6h1c5-3 10-5 15-6z" class="J"></path><path d="M357 482c2-2 5-4 8-6h1l1 1c-3 2-6 5-9 5h-1z" class="C"></path><path d="M366 476c5-3 10-5 15-6l-2 1h3c-5 2-11 3-15 6l-1-1z" class="N"></path><path d="M351 488c1-1 1-2 3-3h1c-2 2-7 7-7 10 0 1-1 2-2 2 0-2 2-5 3-8l2-1z" class="D"></path><path d="M357 486c0 1-1 2-1 2h0c-1 2-3 3-4 5l-4 4h0c-1 0-2 1-2 1v-1c1 0 2-1 2-2l9-9z" class="G"></path><path d="M381 470c6-3 12-4 19-4 1 0 1 1 2 1 1 1 1 1 1 2v1h-2c-1-1-2-1-3 0-5 0-11 0-16 1h-3l2-1z" class="O"></path><defs><linearGradient id="Ab" x1="503.973" y1="578.89" x2="448.622" y2="591.19" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#Ab)" d="M500 559l2-2c-1 4-2 9-4 12h0l1 1-2 7c-2 4-5 6-6 10l-3 3c-2 3-5 6-9 7h-2 0l-1-1c-1 0-2 1-3 2l-1 1-7 6c-1 0-2 2-4 2h-1c0 1-1 1-1 1-1 0-1-1-2-1l1-1h-1-2 0c-1 0-2-1-3-2 1 0 1 0 1-1-2 1-5 2-7 3-8 3-18 5-27 5v-1h6c8-1 16-3 24-6 7-3 14-9 20-15 2-3 5-5 7-8a30.44 30.44 0 0 1 8-8c4-4 7-8 12-10h0c1-1 2-3 4-4z"></path><path d="M500 559l2-2c-1 4-2 9-4 12l-2 4h0c0-2 2-5 3-7v-1-1c1 0 1-1 1-1v-2c-1 2-2 4-4 6h0c1-2 4-5 4-8z" class="D"></path><path d="M498 569h0l1 1-2 7c-2 4-5 6-6 10l-3 3c-2 3-5 6-9 7h-2 0l-1-1 3-2c7-6 13-14 17-21l2-4z" class="K"></path><path d="M479 594h1c3-2 6-4 8-7h1c-1 1-1 2-1 3h0c-2 3-5 6-9 7h-2 0l-1-1 3-2z" class="E"></path><path d="M500 559c0 3-3 6-4 8l-21 26c1-3 3-5 5-7l12-16c2-2 4-4 4-6h0 0c-2 1-3 2-5 4l-12 12c-5 4-8 10-13 14-2 1-4 3-6 5l-5 3c-1 0-2 0-2 1-2 1-5 2-7 3-8 3-18 5-27 5v-1h6c8-1 16-3 24-6 7-3 14-9 20-15 2-3 5-5 7-8a30.44 30.44 0 0 1 8-8c4-4 7-8 12-10h0c1-1 2-3 4-4z" class="F"></path><path d="M476 428c12 1 23 3 33 8 1 0 2-1 3-1v-1-1c9 6 15 12 21 21 1 1 3 4 3 5v1h0c0 2 1 4 2 6l2 9c-2-2-3-5-5-8 0 2 2 5 2 8-1-2-1-5-3-6h0v2c1 3 1 5 1 7s1 5 0 6c-1-2-1-4-2-6 1 4 1 8 1 11l3 6c-1-1-5-7-6-7h0 0l-1-1c0-3-1-5-2-8s-3-6-5-9l-5-8h0c0-1-1-2-1-2-1-1 0-2 0-3 0 1 0 1 1 1 0-1-2-3-2-4-1-1-1-3-2-4-4-3-8-7-12-10s-8-5-12-7h0c-3-1-7-3-11-3h0c-2 0-4-1-6 0h0-3v-1h2v-1h4z" class="J"></path><path d="M472 429c2 0 5 0 7 1h0c-2 0-4-1-6 0h0-3v-1h2z" class="O"></path><path d="M531 463l1 4c1 0 1 1 1 1l1 1v2l-1 2v3h-1v-2h0v-1-2l-1-1v-1c-1-2-1-5 0-6z" class="F"></path><path d="M529 468l-1-1c0-2-2-4-3-6l-3-6h0c2 2 4 5 6 8 1 2 1 4 2 5s1 1 1 2v2l-1 1v2h0l-1-3c-1-1 0-3 0-4z" class="E"></path><path d="M514 450c5 5 11 13 14 19v-1h1c0 1-1 3 0 4l1 3h0v-2l1-1 1 2v2h1v-3l1-2c1 3 1 5 1 7s1 5 0 6c-1-2-1-4-2-6 1 4 1 8 1 11l3 6c-1-1-5-7-6-7h0 0l-1-1c0-3-1-5-2-8s-3-6-5-9l-5-8h0c0-1-1-2-1-2-1-1 0-2 0-3 0 1 0 1 1 1 0-1-2-3-2-4-1-1-1-3-2-4z" class="C"></path><defs><linearGradient id="Ac" x1="534.872" y1="449.745" x2="512.136" y2="452.724" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#444345"></stop></linearGradient></defs><path fill="url(#Ac)" d="M512 433c9 6 15 12 21 21 1 1 3 4 3 5v1h0c0 2 1 4 2 6l2 9c-2-2-3-5-5-8 0 2 2 5 2 8-1-2-1-5-3-6h0l-1-1s0-1-1-1l-1-4-2-2c-1-2-2-4-2-6l1-1c-6-7-11-12-19-18 1 0 2-1 3-1v-1-1z"></path><path d="M528 454l4 7h0 0c0-1-1-1-1-1h-1 0l-1 1c-1-2-2-4-2-6l1-1z" class="K"></path><defs><linearGradient id="Ad" x1="454.13" y1="524.914" x2="405.014" y2="483.532" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#Ad)" d="M416 474l6 2 1 1h1 0 1c0 1 1 1 2 2h0c2 1 3 2 5 3v1c1 0 3 2 4 3h1c0 1 5 5 5 6 1 2 3 4 4 5 4 5 9 12 9 18 0 1 0 2-1 3v1-2c0 1 0 4-1 5v6c-1-1-1-3-1-5-1-4-2-7-4-10l1 8c1 6-3 14-6 19h-1c1-1 1-2 1-3l-1-1c1-6 1-12-1-17h0c0-2 0-4-1-5l-1 1c0 1 0 3 1 4l-1 1c-1-3-2-6-4-8 0-1-1-1-1-2s-1-2-2-3-2-2-2-3c-1 0-1-1-2-1v1l2 2h0 0l-3-1h1l-1 1h-2l-2-2c-1 0-2-1-2-1v-1c-2-1-4-3-6-4h-1c-1-1-1-1-2-1s-1 0-1-1h-1 2s1 0 1 1h0c2 0 4 0 6-1 1-1 2-1 2-3-1-2-3-2-5-3-1-1-3-2-4-2l-2-1c-1 0-2 0-3-1-1 0-3-1-5-1 2 0 5 0 7 1 8 2 15 7 21 12 3 3 5 6 7 8-4-9-12-16-21-21l-7-3h0l-1-1h-1l5-3c1-1 2-2 3-2l1-1v-1z"></path><path d="M441 505c1 2 3 5 3 7 1 1 1 3 1 5 1 4 1 7 0 11v1c-1-1-1-4-1-6 0-6-1-12-3-18z" class="K"></path><defs><linearGradient id="Ae" x1="390.33" y1="540.121" x2="376.572" y2="496.656" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#Ae)" d="M346 508c2-4 6-7 10-10 13-10 29-15 46-13 2 0 4 1 5 1 1 1 2 1 3 1l2 1c1 0 3 1 4 2 2 1 4 1 5 3 0 2-1 2-2 3-2 1-4 1-6 1h0c0-1-1-1-1-1h-2 1c0 1 0 1 1 1s1 0 2 1h1c2 1 4 3 6 4v1h0l1 2c2 2 5 4 7 7 0 1 1 1 1 2 1 1 2 3 3 4v1c0 1 1 1 1 2l1 3c-1 1-1 2-1 2l-1 1v-2l-2-2h0-2v-1c-1 0-1-1-2-2-3-6-10-14-17-16-4-1-9-1-13-1-13-1-25 3-37 10-7 4-12 9-17 15-1 3-3 6-4 9l-2-1h0 0l-1-2v2-2l2-12c0-1 0-3 1-4 2-3 4-7 7-10z"></path><path d="M417 504c1 0 2 1 3 2h0l-2-1v1c-1-1-1-1-1-2z" class="C"></path><path d="M366 501h0c-3 2-6 3-8 5-6 5-11 11-15 18-2 2-3 4-4 7v-1l-1-1c1-3 2-6 4-9 3-7 9-11 15-15l9-4z" class="J"></path><path d="M346 508c1 1 0-1 1-1h1 0v1h0c2-1 5-4 7-4 1 0 2 0 2 1-6 4-12 8-15 15-2 3-3 6-4 9l1 1v1c0 2-1 3-2 5h0 0l-1-2v2-2l2-12c0-1 0-3 1-4 2-3 4-7 7-10z" class="B"></path><path d="M337 536c0-2 0-5 1-7l1 1v1c0 2-1 3-2 5h0z" class="E"></path><defs><linearGradient id="Af" x1="384.733" y1="501.791" x2="382.122" y2="485.667" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#858585"></stop></linearGradient></defs><path fill="url(#Af)" d="M346 508c2-4 6-7 10-10 13-10 29-15 46-13 2 0 4 1 5 1 1 1 2 1 3 1l2 1c1 0 3 1 4 2 2 1 4 1 5 3 0 2-1 2-2 3-2 1-4 1-6 1h0c0-1-1-1-1-1h-2 0-3l-1-1h-3l-2-1c-4 0-7-1-11 0-5 0-10 2-15 3l-9 3v1l-9 4c0-1-1-1-2-1-2 0-5 3-7 4h0v-1h0-1c-1 0 0 2-1 1z"></path><defs><linearGradient id="Ag" x1="372.388" y1="582.12" x2="353.542" y2="685.709" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#Ag)" d="M348 587c0-4 1-8 2-11 0-2 0-3 1-4v5h0c1 3 0 7-1 10l1 1c1-1 1-2 1-3v-2h0c0-1 1-1 1-2h1l-1 13 1 4v-1c0-2 0-4 1-6v-1-12c1 3 1 8 1 12l4 28 2 14v-1l-3-35c1-1 1-2 1-3-1-1-1-2 0-3v2 3h1v-1l5 37 1-2v7 3c1-1 1-5 1-6v6 1c1 0 1 0 1-1v3l-1 8v2c0-1 1-2 1-3v-1c3-16 2-32 0-48l1-5-3-9h1l1 1c1 4 3 9 4 14v-3c2 5 4 11 6 16l3 13v-1h1 0c0-5-1-11-3-16-1-5-4-9-5-14h0c3 4 4 9 6 13s3 7 4 10c1 5 2 9 3 13v2h0 1l-5 1c-1 6-3 13-5 19-5 11-13 20-23 27-4 3-8 4-12 6 7-9 12-19 16-31-7 6-14 8-22 10 7-5 14-8 16-17 1-2 1-5 1-8h-6 2c0-2-1-5-1-8l-2-13h-7c2-1 5-1 7-1l-1-20c0-3 0-6-1-9l1-2v1l1-2z"></path><path d="M376 645c0 2-1 5-2 7v-2c1-2 1-4 1-5h1z" class="D"></path><path d="M367 629v7h0-1v-5l1-2zm-5 2c1 3 1 9 0 11 0-3-1-7 0-10v-1z" class="E"></path><path d="M376 630c1 1 1 4 1 6v-2h0c1 3-1 8-1 11h-1c1-5 1-10 1-15zm-21-40l-1 18h0l-1-1v3c-1-5 0-11 0-16l1 4v-1c0-2 0-4 1-6v-1z" class="K"></path><path d="M348 587c0-4 1-8 2-11 0-2 0-3 1-4v5l-2 18v-9c1-1 0-2 1-2v-6l-1 7c-1 2-1 5-1 7h0v-5z" class="C"></path><path d="M367 586h1l1 1c1 4 3 9 4 14 0 4 1 7 2 11 2 8 3 14 2 22h0v2c0-2 0-5-1-6 0-12-3-23-6-35l-3-9z" class="J"></path><path d="M375 519c4-2 10-1 14-1l3 1 1 1h0v1l1 2h0l1 1c1 0 2 2 3 3 0 0 1 0 1 1h0l9 16c0 1 2 3 2 3l-1 1v1c1 2 2 7 1 9l1 4c1 0 2 0 2 1l1 2 3 3h1c4 5 12 10 19 10h3 2c1 0 4-1 6-1-1 1-2 1-3 1 0 1-1 1-2 2-1 0-2 1-3 2-2 1-5 2-5 3v1c2-1 5-3 6-3h0c0 1-1 1-2 2-3 1-6 3-9 4h-1l-4 2h-3c0 1-1 1-1 1h-1-1c-1 1-2 0-3 1h-5-5-6l-1-1c-1 1-1 1-2 0h-1-2s0-1-1-1h0-1l-3-1-1-1h-1c-1 0-1-1-2-1h0-1c-1 0-1-1-2-1-7-2-14-8-19-13-3-3-6-6-8-10 0 0 0-1-1-1 0 1-1 1-1 2 3 1 4 4 5 6l1 1c0 1 1 1 1 2 1 3 2 8 2 11 0 1 0 2-1 3l6 41-1 2-5-37v1h-1v-3-2c-1 1-1 2 0 3 0 1 0 2-1 3l3 35v1l-2-14-4-28c0-4 0-9-1-12v12 1c-1 2-1 4-1 6v1l-1-4 1-13h-1c0 1-1 1-1 2h0v2c0 1 0 2-1 3l-1-1c1-3 2-7 1-10h0v-5c-1 1-1 2-1 4-1 3-2 7-2 11l-1 2v-1c0-6 1-11 3-17 0-2 0-4 1-6s4-5 6-5c1 0 0 0 1-1 1 0 2-1 2-1 2-1 3-2 5-2v-1h-1 0c1-1 2-2 3-2 1 1 3 4 4 4 0-1-2-3-2-4v-1c-1-1-1-2-1-4s-1-3-1-4c-1-2-1-3-1-4-1-1-1-3-1-4 0-2-1-4 0-6v-1c0-1 1-2 2-4l2-2v1h0c-1 3 0 7 1 10h1v-1c0-3-1-6 0-9h0c0-1-1-1-1-2h2c1-1 1 0 0-1h-1l2-1 2-1z" class="T"></path><path d="M359 596c0-4-1-9-1-13 2 4 2 7 3 11v1h-1v-3-2c-1 1-1 2 0 3 0 1 0 2-1 3z" class="E"></path><path d="M353 581c0-5-1-10 0-15h1c1 1 0 3 0 5 0 3-1 7 0 10h-1z" class="L"></path><path d="M416 575h2c-1-1-1-1-1-2 0 0-1-1-1-2 1 1 3 2 5 4h-1c0 2 2 4 3 5-3-1-5-2-7-4v-1z" class="F"></path><path d="M358 571l1 1c0 1 1 1 1 2 1 3 2 8 2 11 0 1 0 2-1 3-1-6-3-11-3-17z" class="B"></path><path d="M408 558h0c1 0 1 1 1 1l3 16h1-1c-3-5-4-10-4-17z" class="P"></path><path d="M398 527s1 0 1 1h0l9 16c0 1 2 3 2 3l-1 1v1c1 2 2 7 1 9l-1 1s0-1-1-1h0 0v-7c0-1 1-2 1-3s-1-2-2-3c-3-6-8-12-9-18z" class="L"></path><path d="M409 559l1-1 1 4c1 0 2 0 2 1-1 2 1 4 0 7 0 1 2 4 3 5v1c0 1 1 2 2 3 0 1 1 2 1 3h-1c0-1-1-1-1-1h-1c-1-2-3-3-4-6h1-1l-3-16z" class="E"></path><path d="M411 562c1 0 2 0 2 1-1 2 1 4 0 7-1-3-2-5-2-8z" class="M"></path><path d="M398 538c1 2 2 5 3 7 2 4 4 8 5 12v2l-6-7c-1-1-1-2-2-3h0c-1-3-1-5-1-8 0-1 0-2 1-3z" class="C"></path><defs><linearGradient id="Ah" x1="435.374" y1="588.845" x2="416.185" y2="579.49" xlink:href="#B"><stop offset="0" stop-color="#2a282b"></stop><stop offset="1" stop-color="#535351"></stop></linearGradient></defs><path fill="url(#Ah)" d="M440 578h2c1 0 4-1 6-1-1 1-2 1-3 1 0 1-1 1-2 2-1 0-2 1-3 2-2 1-5 2-5 3v1c-2 1-4 0-6 1-5 1-9 1-14 2h-2l-5-2-1-1c12 1 24-1 35-7v-1h-2z"></path><path d="M408 587c3 0 5 0 7 2h-2l-5-2z" class="O"></path><defs><linearGradient id="Ai" x1="425.446" y1="576.7" x2="427.072" y2="569.498" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#49494a"></stop></linearGradient></defs><path fill="url(#Ai)" d="M413 563l1 2 3 3h1c4 5 12 10 19 10h3 2l-1 1c-7 1-15-1-20-4-2-2-4-3-5-4 0 1 1 2 1 2 0 1 0 1 1 2h-2c-1-1-3-4-3-5 1-3-1-5 0-7z"></path><path d="M393 534c1-2 2-4 3-7v4c0 3 0 5 2 7-1 1-1 2-1 3 0 3 0 5 1 8h0c1 1 1 2 1 2v1l4 10c-1-1-3-3-4-5-2-2-4-5-5-7v-1c-2-5-1-10-1-15z" class="D"></path><path d="M396 531c0 3 0 5 2 7-1 1-1 2-1 3 0 3 0 5 1 8h0c1 1 1 2 1 2v1l4 10c-1-1-3-3-4-5l1 1c0-2-1-4-1-6-1-2-2-5-3-7-1-4 0-9 0-14z" class="F"></path><defs><linearGradient id="Aj" x1="382.528" y1="575.805" x2="363.903" y2="554.422" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#Aj)" d="M365 556v-1h-1 0c1-1 2-2 3-2 1 1 3 4 4 4 1 2 2 4 3 5 3 6 6 11 11 16-3-1-5-2-7-3-7-4-11-12-13-19z"></path><defs><linearGradient id="Ak" x1="398.86" y1="579.901" x2="397.098" y2="590.092" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#Ak)" d="M384 588c-5-3-10-6-14-9-3-2-5-4-7-7-2-2-5-5-6-8h0l3 3c1 1 2 2 3 4 5 4 10 7 16 9l5 2 8 2 15 2 1 1 5 2h2c5-1 9-1 14-2 2-1 4 0 6-1s5-3 6-3h0c0 1-1 1-2 2-3 1-6 3-9 4h-1l-4 2h-3c0 1-1 1-1 1h-1-1c-1 1-2 0-3 1h-5-5-6l-1-1c-1 1-1 1-2 0h-1-2s0-1-1-1h0-1l-3-1-1-1h-1c-1 0-1-1-2-1h0-1z"></path><path d="M379 580l5 2h-5c-2-1-3-1-5-2h0c1 0 1 0 2 1h3v-1z" class="N"></path><path d="M413 589c-2 0-3 0-5-1h-1c-1-1-2-2-3-2h-3c-2 0-3 0-5-1h-1c-1 0-2 0-3-1h0l15 2 1 1 5 2z" class="R"></path><path d="M385 588c1 0 1 0 2 1 1-1 2-1 3-2s2-1 4-1c0 0 1 0 2 1h1c3-1 4 1 6 2 3 1 5 1 7 3h1c1 0 3 0 5 1h-5-5-6l-1-1c-1 1-1 1-2 0h-1-2s0-1-1-1h0-1l-3-1-1-1h-1c-1 0-1-1-2-1h0z" class="U"></path><defs><linearGradient id="Al" x1="387.587" y1="546.502" x2="372.488" y2="558.346" xlink:href="#B"><stop offset="0" stop-color="#212121"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#Al)" d="M371 533c0-3-1-6 0-9h0c0-1-1-1-1-2h2c-1 3 0 6 1 9 3 8 8 16 13 23 4 7 8 14 14 20h-1c-1-1-2-2-3-2-1-1-2-2-3-2 1 2 4 4 5 6-5-3-10-6-14-11l-11-13c1 2 4 5 5 7l4 6h0v-1c-2 0-3-1-5-1v-1l-1 1s1 1 1 2c-2-2-4-5-5-7l-1-1c0-1-2-3-2-4v-1c-1-1-1-2-1-4s-1-3-1-4c-1-2-1-3-1-4-1-1-1-3-1-4 0-2-1-4 0-6v-1c0-1 1-2 2-4l2-2v1h0c-1 3 0 7 1 10h1v-1z"></path><path d="M371 533c1 2 1 3 1 5h0c-1-1-2-2-2-4h1v-1z" class="K"></path><path d="M384 565c1 0 2 1 3 2s2 3 3 3l5 4c-2-3-4-5-5-7 0-1 0-1 1-2 2 2 2 4 4 5s3 2 4 4c-1-1-2-2-3-2-1-1-2-2-3-2 1 2 4 4 5 6-5-3-10-6-14-11z" class="F"></path><defs><linearGradient id="Am" x1="402.634" y1="548.064" x2="386.773" y2="550.344" xlink:href="#B"><stop offset="0" stop-color="#0c0b0c"></stop><stop offset="1" stop-color="#2e2d2d"></stop></linearGradient></defs><path fill="url(#Am)" d="M375 519c4-2 10-1 14-1l3 1 1 1h0v1l1 2h0c-1 3-1 7-1 11 0 5-1 10 1 15v1l-1 1 7 10 3 4c1 1 2 2 3 4 0 1 1 3 1 4-3-2-5-3-7-5-5-4-10-9-13-15h0l-1 1h0c-5-7-10-15-13-23-1-3-2-6-1-9 1-1 1 0 0-1h-1l2-1 2-1z"></path><g class="E"><path d="M391 541c0 1 0 2-1 3h0v-7c0-1 0-4 1-6l1 1c-1 3-1 6-1 9zm-7 7s1 0 1-1v-10l1 3h0c0-1 0-1 1-2 0 5-1 10 0 15h0l-2-3c-1-1-1-1-1-2z"></path><path d="M392 532v1l1 1c0 5-1 10 1 15v1l-1 1c-1-2-1-5-1-7-1-1-1-2-1-3 0-3 0-6 1-9z"></path></g><path d="M392 532v1 11c-1-1-1-2-1-3 0-3 0-6 1-9z" class="D"></path><path d="M385 537c1-2 1-6 2-8l1-1v-1c1-2 3-6 5-7v1c-1 1-1 2-1 3-3 4-4 9-5 14-1 1-1 1-1 2h0l-1-3z" class="K"></path><path d="M392 519l1 1h0c-2 1-4 5-5 7v1l-1 1c-1 2-1 6-2 8v10c0 1-1 1-1 1 0-2-1-6-1-8v-3c1-6 3-14 9-18z" class="D"></path><defs><linearGradient id="An" x1="374.043" y1="537.69" x2="388.412" y2="535.134" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#An)" d="M375 519c4-2 10-1 14-1l3 1c-6 4-8 12-9 18v3c0 2 1 6 1 8 0 1 0 1 1 2l2 3-1 1h0c-5-7-10-15-13-23-1-3-2-6-1-9 1-1 1 0 0-1h-1l2-1 2-1z"></path><path d="M375 519c4-2 10-1 14-1l3 1c-6 4-8 12-9 18v3c-1-1-1-2-1-2 1-1 1-1 1-2 0-5 2-11 4-16-1-1-6-1-8-1 0 2-1 4-2 6v3-3-2c1-2 1-3 2-4h0-3-1z" class="T"></path><path d="M179 100c17-8 35-16 53-21s37-8 56-9c25-2 49-2 73 1 15 2 30 5 44 9 11 4 22 8 32 13 20 10 38 25 51 44 3 5 7 10 9 15l2 3v1c-2 1-8 5-8 6 1 0 1 0 1 1-3 7-7 13-11 19h1c0 2-1 3-1 5l2-2c0-1 0-1 1-2 0 0 1-1 2-1 1-1 2-1 3-1l7-2c1-1 2-1 3-1v-1l-3-1c2 0 4-1 6 0h10 2c7 0 13 4 20 6v-1c3 1 7 2 11 3 2 1 4 1 6 2 1 0 3 0 5 1v1c0 1-1 1-2 1l1 2h1l2-1h0v-3c0-1 2-2 3-3h-1c-1 1 0 1-1 1v-2l2 1c0-1 0-1 1-2v-2h1v1l1-2-1-1c1 0 1 0 2 1 1-1 0-1 1-2l1 1-1-1c1 0 1 0 2-1h2c0 1 0 2 1 2h1c-1-1-1-3 0-4 1 3 2 5 3 8 0 3 0 8 1 10l4 4c3 3 7 5 11 7 2 2 4 3 6 4 1 1 2 1 2 2 0 2-1 4-2 6-2 5-3 11-8 15l-4 3c-2 2-5 3-8 4h0-1c-1 1-3 1-4 1-2 0-6 1-7 2 0 1-1 2-1 4-1-2 0-4 1-5-5 3-8 5-9 10-1 2-1 3-1 5h0v-2l-1-1v2c0 1-1 3-1 5v4l-2-7c-1 4-2 7-2 11v1l3 9c-2-2-3-4-5-6 1 3 2 5 3 7-1-1-2-1-3-3h-1l1 1c0 2 2 3 2 4l4 9c-1-1-2-2-2-4h-1v2l-1 1 1 2h0-1-1c1 1 2 3 3 4l-1 1h-1c2 1 4 3 6 6-2-1-3-1-5-2v1c3 2 7 6 9 9l-4-1h-1c2 2 3 4 4 7s1 6 2 8v3h0l-1-1c-1-2-3-3-5-5l-1-1h-1c-1-1-1-1-1-2h1l-1-2c-2-3-8-1-11-3-1 0-2 1-3 1-2 1-2 1-3 2-1 0-2 1-3 1h-1c-2 0-3 1-5 1l-6 6-11 20-8 17h-1l-1 1h-1 0v-1c-1 1-1 1-1 2v1c-1-2 0-4 0-6 0 1 0 2-1 3v-16l-1-9-1-3-3-9c-1-1-2-3-3-4-8-11-22-17-34-21-3-1-7-3-10-3-1 0-1 0-2-1h-5l1-1h0v-1-1c-2-29-6-59-14-88-5-19-12-36-21-52-6-11-13-21-22-29-19-18-44-29-70-28-28 1-56 14-75 35-18 18-28 39-37 63-18 51-21 105-21 159l1 53c2 44 8 89 28 130 10 21 23 41 40 57 16 14 34 23 54 27 6 2 12 3 17 3l3-3c1-1 2-3 3-4 2-4 3-9 4-13 0-2 1-5 2-7-3 1-5 4-7 6l-6 3c-6 3-11 5-18 6 3-2 5-4 7-6 6-6 11-13 14-22 0-1 1-3 1-5 11-12 14-26 16-41v2-2l1 2h0 0l2 1-3 9h1l1-2c1-5 3-9 7-13v1c-2 1-2 2-3 4l3-3 1-1 2-2v1c3-1 4-3 6-4 6-3 12-6 19-7l-2 1h1c1 1 1 0 0 1h-2c0 1 1 1 1 2h0c-1 3 0 6 0 9v1h-1c-1-3-2-7-1-10h0v-1l-2 2c-1 2-2 3-2 4v1c-1 2 0 4 0 6 0 1 0 3 1 4 0 1 0 2 1 4 0 1 1 2 1 4s0 3 1 4v1c0 1 2 3 2 4-1 0-3-3-4-4-1 0-2 1-3 2h0 1v1c-2 0-3 1-5 2 0 0-1 1-2 1-1 1 0 1-1 1-2 0-5 3-6 5s-1 4-1 6c-2 6-3 11-3 17l-1 2c1 3 1 6 1 9l1 20c-2 0-5 0-7 1h7l2 13c0 3 1 6 1 8h-2c-42 2-83 3-124-7-60-15-104-45-136-99-12-20-21-42-27-65-15-59-15-122-4-181l6-28c3-11 7-21 11-32 16-40 40-76 72-104 8-7 16-14 25-19 2 1 2 3 3 5l4 4v-3-3c0-1 0-1-1-2v-2h0v-1h0c0-1 0-2 1-3 1 1 1 1 2 1h0l-2-2z" class="J"></path><path d="M108 350h3v1h-1 0c-1 0-2 0-2-1z" class="I"></path><path d="M166 193l1-1h1v2l-2-1z" class="P"></path><path d="M160 322c1 1 2 1 2 3h0c-1 0-1-1-2-1v-2h0z" class="K"></path><path d="M143 168c1 0 2 1 3 1-1 0-1 1-2 1l-2-1c1 0 1 0 1-1z" class="U"></path><path d="M141 168h2c0 1 0 1-1 1h-6 4l1-1z" class="S"></path><path d="M146 169c1 0 1 1 2 1 0 1 0 1-1 1l-3-1c1 0 1-1 2-1z" class="M"></path><path d="M69 271v-7l1 6h0-1v1zm70 94c1 2 2 4 2 5h-1c-1-1-1-2-2-4l1-1z" class="F"></path><path d="M122 188l1 4c-1 1-1 1-2 1h-1v-1l1-1 1-3z" class="R"></path><path d="M454 112h6c-1 1-3 1-4 2h0l-1-1-1-1z" class="H"></path><path d="M473 265c2 0 4 0 5 1h1c-2 1-2 1-3 1-1-1-2-2-3-2z" class="C"></path><path d="M153 247l1 1c-1 1-1 0-1 1h-1 1s1 0 2 1c-2 0-5 0-7-1 2 0 4 0 5-2z" class="K"></path><path d="M542 189h-1-3c-1-1-2-1-2-2h2c0 1 1 1 2 1 1-1 1 0 2 0v1z" class="F"></path><path d="M228 111l5-1c1 0 1 0 2 1l-5 1s1-1 2-1h0-3-1z" class="B"></path><path d="M478 208l2-1c1 1 1 1 3 2h0 2c-2 1-3 1-4 1l-1-1h1v-1h-2-1z" class="E"></path><path d="M481 205c1 0 3 0 4-1l2 1v1l-5 1v-1l-1-1z" class="G"></path><path d="M141 404l1-1h1l3 2-3 1c-1-1-1-2-2-2z" class="O"></path><path d="M595 212c-2 0-3-1-4-2v-1h3c0 1 1 2 1 3z" class="R"></path><path d="M136 216h-1c-1 0-2 0-3-1v-1l2 1h3 0s1 0 2 1h0-3z" class="K"></path><path d="M594 209c2 0 2 0 3 1v2h-2c0-1-1-2-1-3z" class="S"></path><path d="M156 321l4 3c1 0 1 1 2 1l-1 1h-2v-1l-1-1v-1c-1 0-1-1-2-2z" class="P"></path><path d="M143 403l3-1c1 0 2 1 2 2l-2 1-3-2z" class="N"></path><path d="M455 280h-8l3-1c2-1 3 0 5 0v1z" class="G"></path><path d="M481 198c1 1 2 1 3 2l-5 1v-1c0-1 1-1 2-2z" class="H"></path><path d="M119 286c0 3 1 6 2 9h-2l-1-4c0-2 0-3 1-5z" class="M"></path><path d="M97 353c1 0 5-1 5-1 1 0 1 0 1 1l-7 2h1c0-1 1-1 2-1l2-1c-1 0-2 0-3 1l-1-1z" class="L"></path><path d="M111 350c3-1 6 0 9-1 0 1-1 1-1 1-3 0-6 1-9 1h1v-1z" class="C"></path><path d="M405 113c2 0 5 1 6 3h0-2 0-1l-3-3z" class="F"></path><path d="M148 254c3-1 7-2 9-1h0v1h-6v1h2c-2 0-3 1-5-1z" class="E"></path><path d="M498 268c1-3 3-4 4-7l-3 9h0l-1-1v1h0v-2z" class="H"></path><path d="M102 352l6-2c0 1 1 1 2 1l-7 2c0-1 0-1-1-1zm374-143l2-1h1 2v1h-1l-6 2 2-2z" class="B"></path><path d="M176 450c1 1 2 1 4 2l-2 2h-1c-1-1-1-2-1-4z" class="R"></path><path d="M517 212l7 3v1c-2 0-4-1-5-2l-2-1v-1z" class="E"></path><path d="M165 292c2 0 5-1 7 0l-5 3v-2h0v-1h-2z" class="B"></path><path d="M144 145c-1 0-2-1-2-1 0-1 0-2 1-3 0-2 1-3 3-3-1 1-2 3-2 5v2h0z" class="F"></path><path d="M559 194h0c0 2 1 7 0 8 0-1-1-1-1-2s0-5 1-6z" class="R"></path><path d="M126 360c2 1 4 2 6 4h-4v-1l-2-3z" class="B"></path><path d="M411 116c2 1 4 1 6 1v2h-2l-4-3h0z" class="L"></path><path d="M133 168c2-1 5-1 8 0l-1 1h-4l-2 1c-1-1-1-1-1-2z" class="V"></path><path d="M139 416c1-1 1-1 2-1 2 0 4 1 6 2l-3 3s0-1 1-2v-1c-1 0-2-1-3 0-1 0-2-1-3-1z" class="S"></path><path d="M145 249l3-1c2-1 3-1 5-1-1 2-3 2-5 2l-1 1h0-1l-1-1z" class="C"></path><path d="M495 304c1 1 1 2 1 3s0 2-1 3l-1-5v-1h1z" class="L"></path><path d="M197 534l1-2h1c1 2 2 4 1 7 0-1 0-1-1-2h0v-1h-1 0l-1-2h0z" class="P"></path><path d="M495 231v1c1 0 5 1 6 1v1h-2c-1-1-3 0-5 0v-1l1-1h-5 0l5-1z" class="K"></path><path d="M73 271h2v-2c1 0 2 1 3 2l-1 1c-1 1-1 1-2 1l-2-2z" class="E"></path><path d="M496 269c0-3 1-6 3-8 0 2-2 5-1 7v2h-2v-1z" class="G"></path><path d="M154 275l4-5-2 9v-2c0-1-1-1-2-2z" class="H"></path><path d="M121 207v-1h2l4 4h-2 0l-4-3z" class="B"></path><path d="M148 170c2 1 3 3 5 5-1 0-1 0-1 1l-5-5c1 0 1 0 1-1z" class="R"></path><path d="M497 307c1 2 1 3 1 5-1 1-1 2-2 3l-1-5c1-1 1-2 1-3h1z" class="H"></path><path d="M481 205l1 1v1h-2l-2 1-2 1h-2c1-1 1-2 2-3h1c1 0 3 0 4-1z" class="M"></path><path d="M146 138h2-1c-2 2-2 5-1 7h0-2 0v-2c0-2 1-4 2-5zm376 141c1-2 1-5 2-6 0-2 1-3 1-4h1v2c-1 1-1 1-1 2-1 0-1 1-1 1v5h0c-1 0-1 1-1 1h-1 0l1-1h-1z" class="C"></path><path d="M481 198c2 0 4 0 6-1 1 1 3 1 4 1h1-1c-1 0-1 0-2 1-2 0-3 0-5 1-1-1-2-1-3-2z" class="G"></path><path d="M57 406v-1c1 1 1 3 2 4l1 6h-1c-1-1-2-7-2-9z" class="C"></path><path d="M111 338h0c1-2 1-4 1-5v-3c1 1 2 2 1 4 0 2-1 7-3 9h0c0-2 1-3 1-5z" class="H"></path><path d="M149 136c-2 0-3-1-5-2l7-2c-1 1-2 3-2 4z" class="C"></path><path d="M488 272c-2-3-4-6-8-7-1 0 0 0-1-1h1c4 1 8 5 10 7-1 0-2-1-2-1v2z" class="D"></path><path d="M136 211c3 1 6 0 9 0 0 1 0 1 1 2h-10v-1-1z" class="H"></path><path d="M148 195c1 0 2 1 2 2-1 0-2 1-3 1h-1l-1-1h-4c3-1 5-1 7-2z" class="P"></path><path d="M106 551c1 2 2 4 4 4l1 1c-1 0-2 1-3 1s-1 0-2-1 0-3 0-5z" class="E"></path><path d="M176 504l4-6-1 7h0c-1-1-2-1-3-1z" class="P"></path><path d="M457 156h3c1 0 1 1 2 1h0c1 1 1 1 2 1h0v1l-1-1h-1c0 1 1 1 1 1 1 1 1 1 2 1 1 1 2 1 3 2h0 0l-11-6z" class="G"></path><path d="M77 355c3-3 9-6 13-7l-8 6c-1 0-1-1-1-1-2 1-3 2-4 2z" class="N"></path><path d="M496 324l1 11-1 3h0c0-3 0-6-1-10 1 1 1 1 1 2v1-4l-1-1c1-1 1-1 1-2z" class="Q"></path><path d="M201 585l1 1c1 2 2 5 2 7-2-2-3-4-4-6 1-1 1-1 1-2z" class="F"></path><path d="M176 504c1 0 2 0 3 1h0v2h-1c-2 0-3 0-5 2 1-2 2-4 3-5z" class="R"></path><path d="M485 204c4 0 8 1 12 2 1 1 3 1 5 2-3 0-6-1-8-2h-7v-1l-2-1z" class="I"></path><path d="M233 110l13-3s1-1 2-1c-3 1-11 5-13 5-1-1-1-1-2-1z" class="D"></path><path d="M392 103h6v1 1h0v1l2 1h-1c-3-1-5-3-7-4z" class="E"></path><path d="M524 215h8c2 0 3-1 5 0v-1c0 1 0 1-1 1-3 2-9 2-12 1v-1z" class="F"></path><path d="M470 278c-1 0-3-1-4-1-2-1-6-1-7-2 1 0 2-1 3-1 2 0 7 2 8 3v1z" class="E"></path><path d="M436 146c3 2 6 5 9 7-1 0-2 0-2 1-2-2-3-4-5-5-1-1-1-2-2-3z" class="C"></path><path d="M466 245h2v1l-8 5s0-1 1-1c0-1 0-2 1-3 2 0 3-1 4-2z" class="H"></path><path d="M340 92l-8-1c-1 0-2 0-2-1 6 0 13 0 20 2h-3c-1 0-2 0-4-1h-3v1z" class="D"></path><path d="M182 507c1-3 4-5 6-7 0 2-1 4-1 6-2-1-4 1-5 1z" class="N"></path><path d="M90 312c4-1 9-1 13-1l3 1h0c-2 1-5 1-8 1h0c1 0 1 0 2-1H90z" class="P"></path><path d="M136 569c1 1 1 3 2 4 0 1 0 2-1 3-2-1-3-3-5-4 2 0 3-1 4 0h0v-3z" class="O"></path><path d="M116 221c1 1 2 1 2 3s-1 6-2 7l-1 1v-2c1 0 1-1 1-1-1-1 0-7 0-8z" class="D"></path><path d="M478 245v1l-8 4v-1c-1 0-2 1-3 1h-1c4-2 8-4 12-5z" class="I"></path><path d="M121 470c2 2 3 5 4 8l1 5v1c-2-4-3-8-5-12 1 1 1 2 2 4 0-1 0-2-1-3v-1c0-1 0-1-1-2z" class="U"></path><path d="M447 275l1 1v1h1v1l-1 1c-3 1-5 4-8 5 3-3 5-6 7-9z" class="S"></path><path d="M157 314c0 3 2 5 3 8h0c-2-1-4-2-5-4 0-1 0-2-1-3l1-1 1 1 1-1z" class="C"></path><path d="M132 162c4-1 7-1 10-1 1 0 1 1 1 1 0 1 1 1 1 1l1 1h-1c-3-1-5-1-8-1h4 0c-2-1-5-1-8-1z" class="H"></path><path d="M520 278c0-2 0-4 1-5 0-1 1-3 1-4h1 0c-1 3-1 6-2 9l1 1h1l-1 1h-4v-1l2-1z" class="D"></path><path d="M146 402c4-1 7-1 11-1l1 2c-3-1-6 0-10 1 0-1-1-2-2-2z" class="L"></path><path d="M483 193h1c-1 1-2 1-2 2-1 0-1 0-1 1h1v1c-2 0-3 1-5 3v1h0-1c0-2 1-3 3-4l-1-1h1-1c-1 1-2 2-3 2h0c2-3 4-4 8-5zm-316-13v1c1 1 1 1 1 2v-1h1c0 4 0 7-1 10h-1c0-4 1-8 0-12z" class="R"></path><path d="M199 581c1-2-1-4 0-6h1c0 1 0 3 1 4v-3c1 3 0 7 1 10l-1-1-2-4z" class="I"></path><path d="M162 325h0l8 6c1 1 3 2 3 3l-12-8 1-1z" class="Q"></path><path d="M141 468c-4 4-5 7-8 11 1-5 3-8 5-11h3z" class="B"></path><path d="M460 112c2 1 4 1 5 2v1l-3-1v2h-2-2l-1-1h2c-1-1-2-1-3-1 1-1 3-1 4-2z" class="G"></path><path d="M459 115h1 0l-1-1h3v2h-2-2l-1-1h2z" class="F"></path><path d="M113 413c-1 0-3-1-4-1-4 0-8 2-10 4l-1 1v-1c0-1 3-2 4-3l2-1c3-2 8-1 10 0l-1 1z" class="B"></path><path d="M442 286h0l-1-2c4-2 7-1 11-1-2 1-6 0-7 3-1 1-1 1-2 1l-1-1z" class="U"></path><path d="M94 421v1c1 0 2-1 2-1 1 0 1 0 0 1 0 2-1 3-2 5h0l3-1 1 1c-2 1-5 1-7 2 1-1 2-2 2-3s1-4 1-5z" class="G"></path><path d="M509 256c6 1 13 3 19 7h0c-4-1-8-3-12-4-3-1-6-2-9-2l2-1z" class="L"></path><path d="M187 538l-1-8h0v-1l1 2h0c0 1 1 4 3 4 1 0 1-1 1-2 0 2 0 3-1 5h-1l-1-1v1h-1 0z" class="D"></path><path d="M242 625h0c4 3 9 5 15 7-5 0-9-2-14-4-1-1-1-2-2-3h1z" class="C"></path><path d="M151 132h7c-2 1-4 1-5 2s0 2 0 3h2l-6-1c0-1 1-3 2-4z" class="Q"></path><path d="M526 271h0c0 2-1 4-2 5l1 1c2-1 2-4 3-6v-1h0c1 2 0 3 0 5 0 0-1 1-1 2s-1 2-2 2h-1v-5s0-1 1-1c0-1 0-1 1-2zm-387-55c1-1 2-1 3-1h2 1l-6 3h0c-2-1-3 0-4 0-2 0-4 0-4-1v-1h1v1h4v-1h3z" class="E"></path><path d="M519 219c2-1 2 0 3 0l1 1c4 2 9 1 13 0h0l-2 2c-3 0-7 1-10-1-2 0-3-1-4-1s-1 0-2-1h0c1 0 1 0 2 1v-1h-1z" class="K"></path><path d="M486 182c1-1 2-1 3-1h0c2 1 4 0 6 0s5 0 7 1l3 1h-13v-1h-6 0z" class="Q"></path><path d="M148 523c1 0 2 0 3 1 1 0 2 0 4 1 1 0 2 1 3 1h-8-5 5v-1h-12c4-1 8 0 12 0v-1h-3l-1-1h2z" class="D"></path><path d="M326 634h1s1-1 2-1c-1 2-3 5-2 6 0 1-3 1-4 1 1-2 1-4 3-6z" class="E"></path><path d="M532 201c1 0 3 0 5-1 2 0 4-1 7-2h0v1c-3 2-10 4-14 3h-1-1v-1h4z" class="C"></path><path d="M117 360c3-1 5-1 8 0h1l2 3v1h-1-3c1 0 1 0 2-1-3-2-6-3-9-3z" class="Q"></path><path d="M566 177c1 0 1 0 2-1h2c0 1 0 2 1 2h1v3c0 1-1 2-1 3h0c-1-1-1-2-1-4l-1 1c-1-1-1-2-2-3l-1-1z" class="B"></path><path d="M503 276l1-1c1-2 3-4 4-6 2-2 6-5 9-5-1 1-2 1-4 2l-6 6-1 1 1 1v1h-1-1c-1 1-1 1-2 1z" class="N"></path><path d="M158 132l3 1c-2 0-3 0-4 1v1c0 1 0 2 1 3l-3-1h-2c0-1-1-2 0-3s3-1 5-2z" class="O"></path><path d="M496 179c1-1 2-1 3-1v-1l-3-1c2 0 4-1 6 0 0 1 2 2 3 2 2 1 5 1 7 2h2v1c-2 0-4-1-5-1-4-2-9-1-13-1z" class="B"></path><path d="M465 243l1 1c-1 1-2 2-4 3-1 1-1 2-1 3-1 0-1 1-1 1l-1 2c0-2 1-4 1-7h0 0l1-2v1c2 0 3-2 4-2z" class="P"></path><path d="M102 248c-2 3 2 6 2 8-1 0-1 0-1-1-2-2-4-8-3-12v-3h1c0-1 0-1 1-2v1s0 1-1 1v1h0v2c0 2 0 4 1 5h0z" class="E"></path><path d="M141 504c-1-1-2-9-2-11l5 7c-1 1-2 1-2 3l-1 1z" class="I"></path><path d="M97 426h0c2 0 5 0 7-1 4 0 8-1 12 0v2c-5-2-13-1-18 0l-1-1zm423-205l6 2c3 2 6 1 10 0 1 0 2 0 3-1 1 0 2-1 3 0-1 0-2 0-3 1-2 0-4 1-6 2-4 1-9 0-13-2v-1-1z" class="E"></path><path d="M97 335l3 3v-1l1 1v-1l1 1h2v1c-2 1-3 3-4 5-1 0-1-2-1-3-1-2-1-4-2-6z" class="L"></path><path d="M131 224c0 3 0 6 1 8h0 1v3s1 0 1 1l-1 1c1 1 2 1 2 3-2-2-4-4-5-7s0-6 1-9z" class="C"></path><path d="M112 490l1-1c3 5 7 9 11 14-1-1-2-1-3-2-1-2-7-9-9-11z" class="I"></path><path d="M365 97c5 2 11 4 15 7-1 0-2-1-3-1h0-3c-1 0-3-2-4-2-1-1-2-2-4-2l-1-1v-1z" class="Q"></path><path d="M150 553c2 2 4 4 5 7-2-1-3-1-6-1v1-1-1c1-2 1-3 1-5z" class="G"></path><path d="M122 204c2 1 3 3 5 4s5 2 7 3h2v1 1c-3 0-6-2-9-3l-4-4-1-1c0-1 1-1 0-1h0z" class="L"></path><path d="M109 262c0 1 0 1 1 2l1-2h1c0 1 1 1 1 1l2 1-3 6v-4c-1 0-2-1-3-1l-1-2 1-1z" class="G"></path><path d="M146 452c1 0 2 1 3 1l-9 9c2-4 3-7 6-10z" class="R"></path><path d="M479 202h5c5-1 9 0 13 2 1 0 3 0 3 1h1-3l-1-1c-6-2-10-2-16 0l-2-2z" class="L"></path><path d="M417 117l14 1c-1 1-3 1-4 1-1 1-1 1-3 1v1c-2 0-4-1-7-2v-2z" class="P"></path><path d="M170 525c1 3 3 5 4 8v-2c0-1 0-1 1-2l1 12c-3-5-6-10-6-16z" class="H"></path><defs><linearGradient id="Ao" x1="121.544" y1="312.982" x2="125.743" y2="310.397" xlink:href="#B"><stop offset="0" stop-color="#565758"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#Ao)" d="M120 309l8 2h-2c1 1 1 1 2 1 0 1 0 1 1 2l-9-2s1 0 1-1h1c-1-1-1-1-2-1v-1z"></path><path d="M450 113l4-1 1 1 1 1h0c1 0 2 0 3 1h-2l1 1h0v1c0 1-1 1-2 1h-2 0l1-1c-1-2-3-3-5-4h-1-1 2z" class="O"></path><path d="M450 113l4-1 1 1 1 1h0c1 0 2 0 3 1h-2l1 1h0c-2 0-2-1-3-1-1-1-2-2-3-2h-2z" class="P"></path><path d="M450 113l4-1 1 1 1 1c-2 0-3-1-4-1h-2z" class="N"></path><path d="M519 197h0c1 0 1 0 1-1 2 0 4 1 6 2h0c2 1 4 1 6 3h-4c-3-1-6-2-9-4z" class="F"></path><path d="M398 103c3 0 5-1 8-2v1c1 1 2 1 4 1v2l-12-1v-1z" class="B"></path><defs><linearGradient id="Ap" x1="493.192" y1="264.362" x2="493.259" y2="270.99" xlink:href="#B"><stop offset="0" stop-color="#1e1e1f"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#Ap)" d="M492 270l1-1c-1-3-3-6-4-9 3 2 5 5 6 8v1h1v1c0 1-1 1-2 2-1 0-1 0-2-1v-1z"></path><path d="M147 375c1 2 2 4 5 5h0c-1 1-2 2-2 3l-1 3c-1-3-2-7-3-10 0-1 0-1 1-1z" class="P"></path><path d="M516 182c3-1 9 3 12 4 2 0 5 1 7 2 1 1 2 1 3 2h0c-1 1-3-1-4-1-2-1-5-1-7-2-3-2-6-4-9-4-1-1-1-1-2-1z" class="D"></path><path d="M144 500c1 1 2 2 3 4h-1c-2 2-2 4-1 6-2-2-3-4-4-6l1-1c0-2 1-2 2-3z" class="L"></path><path d="M114 220l2 1c0 1-1 7 0 8 0 0 0 1-1 1v2 1c-1-1-2-1-2-2v-4-2h1l-1-1c1-1 1-3 1-4zm34-82h2c-1 2-2 4-1 6v2h-1l-2-1h0c-1-2-1-5 1-7h1z" class="B"></path><path d="M148 138h2c-1 2-2 4-1 6v2h-1s-1-1-1-2c0-2 0-4 1-6z" class="L"></path><path d="M490 256c6-1 13-2 19 0l-2 1c-5-1-9 0-14 0h-3v-1zm-384 23v-2c0 7 1 13 4 19v1c-3-2-4-4-5-6v-1-4c1-2 1-5 1-7zM59 415h1c1 4 3 8 6 12l1 1c-1 1-1 1-2 3-3-5-5-11-6-16z" class="H"></path><path d="M521 193h3 1 0c0 1 1 1 1 1h0 3 3c1 0 1 1 2 2 2 0 5 0 7 1h-10c-2-1-4-1-7-2l1-1-4-1z" class="E"></path><path d="M525 194c1 0 2 1 3 1s2 0 4 1h0l-1 1c-2-1-4-1-7-2l1-1z" class="D"></path><path d="M466 250h1c1 0 2-1 3-1v1c-2 1-4 2-5 3-2 2-3 3-5 4v-2l1-1h-1v-1c2-1 4-3 6-3z" class="G"></path><path d="M191 157l-2 5c1-5 2-8 5-13 0 1 1 1 2 2v1 1c-1 2-2 5-3 6v-1c0-1 1-3 1-5h0l-2 1h0c0 1-1 2-1 3z" class="O"></path><path d="M89 529c-3-2-3-6-4-9h0c2 0 4 0 6 1-2 2-2 5-2 8zm426-247c0-2 0-6 1-7 0-1 3-3 4-4l-1 4v2l1 1-2 1-1 1-2 2z" class="C"></path><path d="M517 280h0c0-2 1-3 2-5v2l1 1-2 1-1 1z" class="F"></path><defs><linearGradient id="Aq" x1="63.508" y1="283.514" x2="69.754" y2="288.403" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#3c3c3d"></stop></linearGradient></defs><path fill="url(#Aq)" d="M68 291c-3-3-4-9-4-13l3 6c1 2 3 4 4 7-1-1-2-1-3 0z"></path><path d="M484 183s1-1 2-1h0 6v1c-2 0-5 1-7 1 0 1-1 1-1 1 2 1 4-1 7 1h0c-4 0-6 1-10 2v-1l2-2c0-1 0-1 1-2z" class="O"></path><path d="M98 470h1c-1 1-3 2-5 2-4 2-8 4-12 5-2 0-4 0-6 1h0-1c3-2 8-3 11-4 2 0 4-1 5-2h1l3-1h0 0 0 1c1-1 1-1 2-1z" class="D"></path><path d="M213 569c0-1 0-2 1-3 2-1 5-1 8-2 0 2 1 4 1 6-3-1-6-3-9-2l-1 1z" class="B"></path><path d="M150 138l2 1h2l-1 1c0 1 0 2-1 3 0 1 0 3 1 4h-2l-2-1v-2c-1-2 0-4 1-6z" class="N"></path><path d="M150 138l2 1c-2 0-2 1-2 2l-1 2 1 1h0c0 1 0 2 1 3l-2-1v-2c-1-2 0-4 1-6z" class="Q"></path><path d="M172 204h-2c-1-1 0-1 0-2 3-6 2-12 2-18 1 2 1 4 3 6-2 1-2 2-2 3 0 4 0 7-1 11z" class="M"></path><path d="M186 150h0c1-3 4-5 6-8v1c-1 1-2 3-2 5 0 3-2 5-4 7l-5 7c0 1-1 1-2 2h0c0-1 1-2 1-2v-1c3-4 6-7 8-12h0l-1 1v1l-1-1z" class="F"></path><path d="M132 445v8 3c0 1 1 1 1 2 0 2-1 4-1 6 0 1 0 3-1 5 0-2-1-6-1-7l1-1c-1-4 0-9 0-13v3c1 0 1-5 1-6z" class="K"></path><path d="M492 223c3 1 6 2 10 3 3 1 7 1 10 3 0 1 2 2 3 2 0 1 1 1 1 1l2 1h0c-5-1-8-3-12-5s-10-3-15-4l1-1z" class="E"></path><path d="M208 112l20-1h1 3 0c-1 0-2 1-2 1l-14 5c1-1 2-2 4-3 1 0 2 0 3-1h0c-2-1-4-1-6 0h-1c-1-1-2 0-3-1h-5 0z" class="Q"></path><path d="M319 633c-8 4-20 3-28 3 1-1 4-1 6-1s4 0 7-1c4 0 9-2 13-4h0c-1 2-2 2-3 2-3 1-5 2-8 3h-2 0 4 3c1-1 2 0 3-1h0c1 0 1 0 2-1h3z" class="I"></path><path d="M170 464l2 1-1 1c1 1 1 2 2 4v1c-1-1-1-1-2-1-1 1-1 5-2 5s-3-1-3-2h-1c1-1 2-2 2-3 1-1 3-2 3-4v-2z" class="F"></path><defs><linearGradient id="Ar" x1="124.095" y1="422.125" x2="130.685" y2="427.547" xlink:href="#B"><stop offset="0" stop-color="#6a6a6a"></stop><stop offset="1" stop-color="#808181"></stop></linearGradient></defs><path fill="url(#Ar)" d="M125 420c3 2 5 5 6 8v2c1 2 1 4 1 6l-1 1c-1-6-3-9-6-14l-1-3h1z"></path><defs><linearGradient id="As" x1="201.092" y1="576.237" x2="198.384" y2="569.172" xlink:href="#B"><stop offset="0" stop-color="#403f40"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#As)" d="M196 564h1v3 2l1 1 2-1v-2h-1v-1s0-1 1-1l1-1v12 3c-1-1-1-3-1-4h0l-1-1c-2-2-3-7-3-10z"></path><path d="M216 604h0l2 2c2 2 3 5 5 7h1c-1-2-2-4-3-5-1-2-1-2-1-4 4 7 8 13 15 18v1h-1c-7-6-14-11-18-19z" class="O"></path><path d="M492 313c2 3 3 7 4 11 0 1 0 1-1 2l1 1v4-1c0-1 0-1-1-2l-3-9c1-1 0-4 0-6z" class="H"></path><path d="M489 238c10-1 19 0 28 2 1 1 3 1 4 2h1c-4-1-8-2-11-2-7-1-13-1-20-1 0-1-1-1-2-1z" class="B"></path><path d="M466 245c2-1 5-2 7-3 6-2 11-3 16-4 1 0 2 0 2 1-8 1-16 3-23 7v-1h-2z" class="G"></path><defs><linearGradient id="At" x1="115.479" y1="414.084" x2="124.051" y2="419.384" xlink:href="#B"><stop offset="0" stop-color="#323231"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#At)" d="M114 412c4 2 9 4 11 8h-1l1 3c-4-4-7-7-12-10l1-1z"></path><defs><linearGradient id="Au" x1="109.038" y1="204.595" x2="104.616" y2="209.234" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#545355"></stop></linearGradient></defs><path fill="url(#Au)" d="M96 210c4-2 10-5 15-4h1c-2 2-4 3-7 4h0v-1c-1 0-5 1-6 1v-1l-2 1h-1z"></path><path d="M138 450c0 2 1 2 1 4h1c1 0 1-1 2-2l1 1-3 3 1 1c-1 2-2 3-3 4 0-1 0 0 1-1l-1-1 1-1h-1s-1 0-1 1h0l-1-1c0 1 0 1-1 2h0c0-2 2-8 3-10z" class="L"></path><path d="M157 426c3-2 4-1 7-1v1h1c1 0 1 0 2 1h1v1h0c0 1 1 1 1 2l1 1s1 0 1 1c-2 0-4-2-5-3l-9-3zm305-234c1 1 1 2 1 4l1 1c2-1 2-3 5-2l-9 13c1-2 2-4 2-6 1-3 0-7 0-10zm-26 15h0v3c-1 0-1 1-1 2l1-1 1 1v-1c0 1 0 3-1 4 0 0 0 1-1 1 0 2-1 4-1 6 1-1 2-2 2-3l2-2c-1 3-3 5-5 7v-6s0-1 1-1l-1 1-1-1v1-1c1-3 3-7 4-10z" class="D"></path><path d="M481 182h1c0 2-1 3-1 5v1c-2 1-3 3-4 4-2 1-3 2-5 3 3-5 6-9 9-13z" class="S"></path><path d="M521 230c6 2 11 5 17 2 3-2 6-4 9-5l6-3h2 1c-1 1-2 1-2 1-1 0-2 1-2 1h-1l-4 2c-6 3-12 7-19 5l-7-2v-1z" class="E"></path><defs><linearGradient id="Av" x1="488.331" y1="186.635" x2="485.251" y2="193.432" xlink:href="#B"><stop offset="0" stop-color="#2e2f2d"></stop><stop offset="1" stop-color="#59575c"></stop></linearGradient></defs><path fill="url(#Av)" d="M493 190c-7-1-12 2-17 5 2-3 3-5 7-6s8-1 12 0c-1 0-1 1-2 1z"></path><path d="M108 254v2c1 2 4 3 6 4h3l-2 4-2-1s-1 0-1-1h-1l-1 2c-1-1-1-1-1-2-1-2-1-5-1-8z" class="H"></path><path d="M491 186h0c-3-2-5 0-7-1 0 0 1 0 1-1l1 1h11c5 1 10 2 14 4 4 1 7 3 10 4l4 1-1 1c-6-2-12-6-19-7-2-1-5-1-7-2-2 0-5 1-7 0z" class="L"></path><defs><linearGradient id="Aw" x1="405.319" y1="110.188" x2="410.048" y2="104.617" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#Aw)" d="M398 105l18 3h0v1c0 1 1 1 2 1-4 1-7-1-10-2h-1l-1 1c-2-1-4-2-6-2l-2-1v-1z"></path><path d="M91 521l3 1c-1 3-1 7-1 10-1-1-3-2-4-3 0-3 0-6 2-8z" class="P"></path><path d="M79 245c-1-7 0-13 3-19 1 1 1 4 1 5-2 5-3 8-4 13v1z" class="N"></path><defs><linearGradient id="Ax" x1="121.809" y1="281.083" x2="117.237" y2="278.999" xlink:href="#B"><stop offset="0" stop-color="#676667"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#Ax)" d="M124 271c-1 1-1 1-1 2-3 4-4 8-4 13-1 2-1 3-1 5-1-4-1-10 0-14v-3c0 1 1 1 1 2l4-5h1z"></path><path d="M104 518c0-1-1-2-1-4 7 0 13 0 20 2 0 1-1 1-2 1v-1h-3-8c-1 1-1 0-2 1h0-2-1l-1 1z" class="H"></path><defs><linearGradient id="Ay" x1="160.688" y1="253.652" x2="146.699" y2="258.667" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#2b2b2c"></stop></linearGradient></defs><path fill="url(#Ay)" d="M145 255c1 0 2 0 3-1 2 2 3 1 5 1l11 2c-3 1-6 1-10 1h-3-4c-1 0-1-2-2-3z"></path><defs><linearGradient id="Az" x1="510.731" y1="268.762" x2="507.929" y2="279.425" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#585759"></stop></linearGradient></defs><path fill="url(#Az)" d="M507 272c2 1 9-5 12-6 1-1 3-1 5-1-2 1-3 1-5 2-6 3-11 8-14 14v-1c0-2 0-3 1-5h1v-1l-1-1 1-1z"></path><path d="M111 329l1 1v3c0 1 0 3-1 5h0v-1c-2 3-3 5-5 7 0-2-1-3-2-5h0v-1c1 0 2 0 4-1 2-2 3-5 3-8z" class="D"></path><path d="M138 525h12v1h-5c-6 1-12 3-19 5 1-1 2-2 3-2h2v-1h1c0-1-1-1-1-1h-4 0c3-2 7-2 11-2z" class="H"></path><path d="M495 189c8 2 16 5 24 8 3 2 6 3 9 4v1c-12-4-23-10-35-12 1 0 1-1 2-1z" class="B"></path><path d="M259 90c-4-2-9-5-14-7l13-3c1-1 3-1 4-1h0c-2 2-4 1-6 2 0 1-1 2-2 2 0 1-1 2-1 3h0c2 1 5 2 6 4z" class="I"></path><path d="M518 215l5 2c4 2 7 2 11 1l-1 1c-4 1-7 1-10-1h-1c-2-1-4-1-5-2-2-2-4-4-6-5l-6-3v-1c2 0 5 3 7 3 0 1 0 1 1 1h0c1 0 1 1 2 1v-1l2 1v1l2 1-1 1z" class="E"></path><path d="M513 211c1 0 1 1 2 1v-1l2 1v1l2 1-1 1c-1 0-2-1-2-1l-1-1c-1 0-1-1-2-2z" class="F"></path><path d="M165 161c1 0 3 0 4 2 1 1 2 3 2 5v1c-1 1-1 1-2 1-2-1-3-2-4-4s-1-3 0-5z" class="S"></path><path d="M466 267l3-1c2-1 3-1 4-1s2 1 3 2h-2 0-2v1c1 1 5 1 6 1l1 1h0c-4 0-7-1-11 0h-3v1c1 0 1 0 2 1h-3c0-1-1-1-1-1-1 0 0 0-1-1 1 0 3-1 4-1v-2z" class="D"></path><path d="M161 469l7-6 2 1v2c0 2-2 3-3 4 0 1-1 2-2 3l-4-4z" class="B"></path><defs><linearGradient id="BA" x1="116.778" y1="204.302" x2="119.909" y2="196.201" xlink:href="#B"><stop offset="0" stop-color="#2d2c2e"></stop><stop offset="1" stop-color="#636361"></stop></linearGradient></defs><path fill="url(#BA)" d="M114 193h0c1 1 1 1 1 2h2 0c0 1 1 1 1 2 1 2 2 4 4 7h0c1 0 0 0 0 1l1 1h-2v1c-4-5-6-9-7-14z"></path><path d="M473 229h1c0-1 1-2 1-2 1 0 1 1 2 0 2 0 5 0 7 1 1 0 3-1 4 1h-2l-10 2-1-1c-4 1-6 4-9 5h-1l2-2c2-2 4-3 6-4z" class="K"></path><path d="M475 230c3 0 8-2 11-1l-10 2-1-1z" class="D"></path><defs><linearGradient id="BB" x1="132.058" y1="455.776" x2="126.313" y2="450.79" xlink:href="#B"><stop offset="0" stop-color="#272728"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#BB)" d="M130 443l1 5c0 4-1 9 0 13l-1 1c-1-4-2-7-4-10 1-1 1-3 2-4 2-2 2-3 2-5z"></path><defs><linearGradient id="BC" x1="165.055" y1="527.816" x2="168.546" y2="521.506" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#666665"></stop></linearGradient></defs><path fill="url(#BC)" d="M165 517l1 1h1 0v2h1v1c0 3 0 7 1 10l1 3c-1-2-2-3-4-5 0-1-2-2-2-3l1-2c-2-2-1-5 0-7z"></path><path d="M483 209c6-1 10-1 16 1h0c8 2 14 8 21 11v1 1c-3-1-5-3-8-5-9-6-16-10-27-9h-2z" class="C"></path><defs><linearGradient id="BD" x1="70.528" y1="334.574" x2="77.069" y2="330.429" xlink:href="#B"><stop offset="0" stop-color="#302e2f"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#BD)" d="M70 334v-1s0-1 1-1c1-1 2-1 3-2h0v-1c1 0 2 1 2 1h1 0 1c1 0 1 0 1-1h1l2-2c1 0 1 0 1-1h1 0v1l-17 13h0v-1l2-2h0l1-1-1-1 1-1z"></path><defs><linearGradient id="BE" x1="154.537" y1="279.668" x2="151.607" y2="282.156" xlink:href="#B"><stop offset="0" stop-color="#6b6a6a"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#BE)" d="M154 275c1 1 2 1 2 2v2c0 1-1 2-1 3 2-1 3-3 4-5 1 1 2 1 2 2l1 1h-1l-2-1c-2 1-3 6-6 6l-3-2c0 1-1 3-2 4 1-4 3-7 5-10l1-2z"></path><path d="M154 275c1 1 2 1 2 2v2c0 1-1 2-1 3h0v-3l-2-2 1-2z" class="R"></path><path d="M205 583c-1-3-1-6-1-9l7-1v4 1c-1 0-2 0-3 1-1 2-2 3-2 5l-1-1z" class="O"></path><path d="M205 583v-3h0c1-1 1-2 2-3 1 0 3-1 4 0v1c-1 0-2 0-3 1-1 2-2 3-2 5l-1-1z" class="M"></path><defs><linearGradient id="BF" x1="126.32" y1="449.155" x2="124.021" y2="446.443" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#BF)" d="M129 439l1 4c0 2 0 3-2 5-1 1-1 3-2 4l-4-6h1v1l1-1h0c1-1 1-1 1-3 1-1 2-1 3-2s1-1 1-2z"></path><path d="M165 517v-2c1-3 3-5 6-7l1 1v2c0 1-2 3-2 4 0 3-1 4 0 7h0-1v-4h0l-1 2h-1v-2h0-1l-1-1z" class="M"></path><path d="M170 193l1 1c0 2-1 5-3 7-3 4-6 10-11 13-1 2-3 2-5 4h0c-1 0-1-1-1-1 1-1 1-1 1-2 1-1 2-2 4-3s4-2 6-4c4-4 6-10 8-15z" class="D"></path><defs><linearGradient id="BG" x1="465.129" y1="257.314" x2="465.878" y2="266.633" xlink:href="#B"><stop offset="0" stop-color="#282826"></stop><stop offset="1" stop-color="#403f43"></stop></linearGradient></defs><path fill="url(#BG)" d="M457 264c3 1 9-4 11-5s4-2 5-3l1 1h0v1l-17 11-1-1h0c1-1 1-1 1-2l-1-1v-1h1z"></path><path d="M128 484h0c1 1 2 5 4 5h0c1 2 1 3 2 5 1 3 2 6 4 9v1c-1-1-3-3-4-5-3-5-6-10-8-15v-1l1 1h1z" class="L"></path><path d="M499 299h0v6h0c1-1 1-2 1-3l-1 31c-1-1-1-5-1-6 0-4-1-8-2-12 1-1 1-2 2-3h0c1-2 1-4 1-6v-7z" class="G"></path><defs><linearGradient id="BH" x1="139.75" y1="401.012" x2="134.336" y2="410.595" xlink:href="#B"><stop offset="0" stop-color="#787676"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#BH)" d="M137 403h2c1-1 2-1 3-2v1 1l-1 1c1 0 1 1 2 2l-4 1-8 6h-1l-1-1c1-1 2-1 3-2 1-2 2-3 3-5l2-2z"></path><path d="M139 407c1-1 1-2 2-3 1 0 1 1 2 2l-4 1z" class="U"></path><defs><linearGradient id="BI" x1="70.202" y1="290.43" x2="75.245" y2="298.398" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#747474"></stop></linearGradient></defs><path fill="url(#BI)" d="M68 291c1-1 2-1 3 0h1c1 0 2 2 3 2l1 1h0c1 1 2 1 2 3v1h0c-1 1-1 2-1 3h0c-4-3-7-6-9-10z"></path><path d="M475 250c3 0 5-1 8-2 12-3 25-2 37 3h-1c-4-1-9-2-13-3-7-1-15 0-22 1-3 1-6 2-8 3-1-1-1-1-1-2z" class="Q"></path><path d="M98 470c-1-1-2-1-2-2l-1-1c0-1-1-1-1-2s0-1-1-1v-1h-2v1h-1-2c4-2 10-2 14-3l1 1h-1l-3 8h-1z" class="G"></path><path d="M148 501v-8c-1-1-2-2-2-3-1-4-1-6 0-10l6-3c-1 2-2 3-3 5-1 3-1 5 0 8 0 3 0 8-1 11z" class="C"></path><defs><linearGradient id="BJ" x1="92.919" y1="424.621" x2="84.419" y2="424.716" xlink:href="#B"><stop offset="0" stop-color="#454446"></stop><stop offset="1" stop-color="#656564"></stop></linearGradient></defs><path fill="url(#BJ)" d="M91 429h-3c-2-1-3-2-5-3 3-1 6-3 9-5 0 0 0-1 1-1l1 1c0 1-1 4-1 5s-1 2-2 3z"></path><path d="M478 245c9-3 17-3 26-3 5 0 12 1 16 4h1-2c-3-1-6-2-10-2-10-1-21-1-31 2v-1z" class="G"></path><defs><linearGradient id="BK" x1="174.729" y1="588.681" x2="182.014" y2="588.981" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#BK)" d="M186 595l-8-4c-2-1-3-1-4-3h1c4-3 9-2 14 0h-2-1 0c-1-1-2-1-3-1-1 1-1 3-1 5 1-1 0-1 1-1v-1l1 1c0 1 1 3 2 4z"></path><path d="M473 256c12-5 28-8 40-4l2 1h0c-3 0-5-1-7-1-12-1-23 0-34 6v-1h0l-1-1z" class="G"></path><defs><linearGradient id="BL" x1="156.064" y1="266.059" x2="146.86" y2="261.2" xlink:href="#B"><stop offset="0" stop-color="#111112"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#BL)" d="M151 259c2 0 5 1 6 2h0-1-6c1 1 3 1 4 1 2 1 3 2 5 4-6-1-11 0-16 0l2-1h1l-1-1h1c1-1 2-3 2-4h0l3-1z"></path><path d="M476 267c1 0 1 0 3-1l3 3 6 5 1-1c1 2 1 3 1 4l-1 1c-1-1-1-2-3-2l-1-1c-2-2-4-3-6-5h0l-1-1c-1 0-5 0-6-1v-1h2 0 2z" class="C"></path><path d="M476 267c1 0 1 0 3-1l3 3h0-1c-2-1-4-1-7-2h2z" class="I"></path><path d="M482 269l6 5 1-1c1 2 1 3 1 4l-1 1c-1-1-1-2-3-2l-1-1 1-1 1 1v-1l-5-5h0z" class="B"></path><path d="M487 197c9-1 19 6 28 9 3 2 7 3 11 4 3 1 6 0 9-1h3 0 0c-4 1-7 2-11 2-3 0-6-1-9-2-7-3-13-7-20-9-3-1-6-1-9-1 1-1 1-1 2-1h1-1c-1 0-3 0-4-1z" class="I"></path><path d="M153 175c2 3 3 7 3 10 0 4-1 8-4 10l-2 2c0-1-1-2-2-2 2-2 4-4 5-6 2-5 0-9-1-13 0-1 0-1 1-1z" class="L"></path><path d="M479 139c0-1-1-1-1-2l2-1c4 2 11 7 12 11v1c-1 0-2-2-3-2-3-2-6-4-9-5h1c-1 0-2-2-2-2z" class="C"></path><path d="M213 569l1-1c3-1 6 1 9 2v6c-1-1-2-1-2-1-1-1-2-1-3-1-1-1-3-2-4-2l-1 5v-8z" class="R"></path><path d="M483 193c2-1 3-1 5 0 4 1 8 1 12 2 8 2 15 7 22 9 2 1 4 1 6 1h2c-2 1-6 1-8 0h-1c-3-1-6-2-9-4-7-3-14-7-22-7-2 1-4 1-5 1-1 1-2 1-3 2v-1h-1c0-1 0-1 1-1 0-1 1-1 2-2h-1zM152 430c1-2 3-3 5-4l9 3c1 1 3 3 5 3l1 1h0c0 1 0 1 1 1v1h0 1l1-1-1-1h-1 0v-1h-1v-1c2 0 2 0 3 1l1 1c-1 2-3 3-4 5v-2c0-1-1-2-2-2l-1-1h-2c0-1 0-1-1-1h0l-2-1h-2l-2-1c-1 0-1-1-2-1h-2c-1-1-1-1-2 0h0c-1 1-2 1-2 1z" class="B"></path><path d="M542 188c2 0 6 2 7 1 1 0 1 0 2 1h1c1-1 1-1 2-1l1 2h1c-2 1-3 1-4 2-2 1-4 4-6 4l-1-1-1 1h-4l1-1h3c1-1 2-1 3-2h0c-2-2-4-3-6-4h0c2 0 3 1 5 2h3l-1-1c-2 0-4-2-6-2v-1z" class="I"></path><defs><linearGradient id="BM" x1="148.109" y1="582.238" x2="145.638" y2="572.842" xlink:href="#B"><stop offset="0" stop-color="#2a2929"></stop><stop offset="1" stop-color="#47484a"></stop></linearGradient></defs><path fill="url(#BM)" d="M138 573c6 5 12 6 20 7h9c-2 0-4 0-6 1h-13c-4-1-8-2-11-5 1-1 1-2 1-3z"></path><defs><linearGradient id="BN" x1="175.874" y1="161.694" x2="171.381" y2="159.234" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#5c5c5d"></stop></linearGradient></defs><path fill="url(#BN)" d="M186 150l1 1c-3 4-8 8-11 12-2 2-4 4-5 6v-1c0-2-1-4-2-5-1-2-3-2-4-2 0 0 0-1 1-1h2c2 0 6 0 9-1h0c3-2 7-6 9-9z"></path><path d="M220 604l-2-7 4 8c3 6 7 11 12 15 2 2 4 3 7 5 1 1 1 2 2 3h0c-3-1-7-3-9-5h1v-1c-7-5-11-11-15-18z" class="E"></path><path d="M106 551c0-1 1-3 2-4 0 2 2 4 3 5 2 1 4 2 5 4l1 2c0 2-3 4-4 6v-2c0-1 0-3-1-4l-1 1v-3l-1-1c-2 0-3-2-4-4z" class="D"></path><defs><linearGradient id="BO" x1="158.228" y1="206.477" x2="155.036" y2="201.771" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#515152"></stop></linearGradient></defs><path fill="url(#BO)" d="M145 211c2-1 6-1 8-2 6-4 11-9 13-16l2 1c-2 7-7 12-13 15-3 2-6 3-9 4-1-1-1-1-1-2z"></path><path d="M455 279c2 0 6 0 7 1 3 0 6 1 9 2 1 1 2 2 3 2h0-2-1c1 0 1 0 2 1s2 2 3 4l-21-9v-1z" class="I"></path><path d="M462 280c3 0 6 1 9 2 1 1 2 2 3 2h0-2-1c1 0 1 0 2 1-2-1-3-2-5-2-1-1-2-1-2-1l-1-1c-1 0-2 0-3-1z" class="G"></path><path d="M178 213c2 1 4 3 5 5 0 1 0 2-1 3v4c-1 4 0 8-1 12v2c-1 0-1-1-2 0s-1 3-1 5c1 2 2 8 0 10 0-2 1-5 0-7h-2v-1c1-1 1-4 1-6l3-6c1-4 1-8 0-11 0-2 1-5 0-6 0-2-1-3-2-4z" class="K"></path><path d="M157 401c5 1 10 4 14 7 0 1 0 2 1 3h0c1 2 0 5-1 7 0 1-1 2-2 3v-1c0-3-1-9-2-12l-2-2c-2-2-5-3-7-3l-1-2z" class="I"></path><defs><linearGradient id="BP" x1="52.425" y1="401.384" x2="60.852" y2="391.923" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#323233"></stop></linearGradient></defs><path fill="url(#BP)" d="M57 406c-1-1-1-2-2-4-1-6-1-11 1-17 0 1 1 3 1 4 0 2-1 6 0 7h1v1c1 4 1 8 1 11v1c-1-1-1-3-2-4v1z"></path><path d="M534 181c3 1 7 2 11 3 2 1 4 1 6 2 1 0 3 0 5 1v1c0 1-1 1-2 1s-1 0-2 1h-1c-1-1-1-1-2-1-2 0-5-2-7-2-1-1-3 0-4-1-3-1-6-1-9-2-2-1-3-1-4-2 3 0 7 3 10 3 5 0 10 2 15 3-4-3-7-3-11-4-1-1-3-1-5-2v-1z" class="G"></path><path d="M340 92v-1h3c2 1 3 1 4 1h3c5 1 10 3 15 5h0v1l1 1v1c-1-1-3-1-4-2-1 0-1 0-2 1-7-3-13-6-20-7z" class="I"></path><defs><linearGradient id="BQ" x1="167.864" y1="433.615" x2="164.716" y2="437.758" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#BQ)" d="M164 431l2 1h0c1 0 1 0 1 1h2l1 1c1 0 2 1 2 2v2c-2 1-5 4-7 4h0l1-1v-2c-2-2-3-3-5-4-1 0-2 1-2 1 1-1 1-3 2-4 1 0 1 0 2 1h1v-2z"></path><path d="M170 435l1 1-1-1z" class="L"></path><path d="M208 139l1-1c2-1 11-10 12-10 1-1 1 0 1 0-8 8-16 18-19 30 0-1-1-1-1-2h1v-1c0-1 0-2 1-3h0v-1l2-3c-1-1-1-2 0-4h0c0-2 2-3 2-5z" class="V"></path><defs><linearGradient id="BR" x1="473.336" y1="281.879" x2="485.359" y2="296.686" xlink:href="#B"><stop offset="0" stop-color="#333232"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#BR)" d="M473 285c-1-1-1-1-2-1h1 2 0c2 0 3 2 5 3 3 2 5 4 8 6 3 3 4 8 7 11v1c-5-8-11-12-18-16-1-2-2-3-3-4z"></path><defs><linearGradient id="BS" x1="177.69" y1="550.88" x2="179.535" y2="533.235" xlink:href="#B"><stop offset="0" stop-color="#171618"></stop><stop offset="1" stop-color="#4a4949"></stop></linearGradient></defs><path fill="url(#BS)" d="M176 523c0 2 0 4 1 6v4h2v-1h1l1 16c0 3 1 5 0 8h0c-1-2-1-5-2-7l-3-18v-8z"></path><defs><linearGradient id="BT" x1="167.682" y1="598.047" x2="174.473" y2="596.091" xlink:href="#B"><stop offset="0" stop-color="#0d0c0d"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#BT)" d="M178 603s-1 0-1 1c-1 0-2 1-2 2h-1 0l1-1c0-1 0-2-1-3s-2-2-3-2c-1 1-2 3-2 4-1-1-1-7-1-8l2-2h-1l-2 1h-2-1c1-1 2-1 3-1 1-1 2-2 4-1 1 0 1 1 2 0v1c0 1 1 2 1 3h1c1 0 1 2 1 3s1 2 2 3z"></path><defs><linearGradient id="BU" x1="112.017" y1="399.284" x2="118.47" y2="397.287" xlink:href="#B"><stop offset="0" stop-color="#434343"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#BU)" d="M123 397c-1 2-3 3-3 5v1h-1c-2 0-4-1-6-2-1-1-2-1-2-2 1-3 4-6 7-7 0 2 0 4 1 6v-3l1 3 1 1v-1s1-1 2-1z"></path><defs><linearGradient id="BV" x1="232.902" y1="620.657" x2="227.83" y2="608.32" xlink:href="#B"><stop offset="0" stop-color="#383839"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#BV)" d="M222 605c1 1 1 1 2 1v1s1 0 1 1h0 2 1v-1c-1 0 0-1-1-1v-1-1l-1-3-1-1h0 0v2 1c-1-3-1-4 1-6 2 9 5 17 11 24 1 1 3 3 5 4h-1c-3-2-5-3-7-5-5-4-9-9-12-15z"></path><defs><linearGradient id="BW" x1="129.743" y1="185.584" x2="122.624" y2="173.661" xlink:href="#B"><stop offset="0" stop-color="#676769"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#BW)" d="M120 192l-1-2v-1c-1-5 1-10 4-14 2-3 6-6 10-7 0 1 0 1 1 2-4 1-8 3-10 7-2 3-3 7-2 11l-1 3-1 1z"></path><defs><linearGradient id="BX" x1="55.876" y1="390.355" x2="63.253" y2="371.783" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#BX)" d="M63 369v2l1 1c0 1-1 2-1 3-3 7-5 14-5 21h-1c-1-1 0-5 0-7 0-1-1-3-1-4l1-3 1-3c1-4 3-7 5-10z"></path><path d="M58 379h0c0 1 0 2 1 3 0 0 0 1-1 1l-1 1v-2l1-3z" class="O"></path><path d="M57 382v2c1 2 1 3 0 5 0-1-1-3-1-4l1-3z" class="Q"></path><path d="M164 526c0-1-1-2-1-2v-1h0v-1c-1-1-1-3-1-5 0-7 7-12 11-17l-1 9-1-1c-3 2-5 4-6 7v2c-1 2-2 5 0 7l-1 2z" class="H"></path><path d="M406 109l1-1h1c3 1 6 3 10 2 1 1 2 1 4 1h0c5 0 10 0 14-1-2 2-5 2-8 2 0 1-1 2-1 3l-8-1c-5-1-9-3-13-5z" class="B"></path><path d="M419 114v-1c3-1 7-1 9-1 0 1-1 2-1 3l-8-1z" class="N"></path><path d="M104 483h0c-1 0-2-1-3-2v-1l1 1h0c-1-1-4-3-6-4l-2-1h1c4 0 6 1 9 3 4 3 7 6 9 10l-1 1c2 2 8 9 9 11-5-4-8-9-12-14-2-1-3-3-5-4z" class="K"></path><path d="M112 490l-6-6c-2-3-4-5-7-7h-1c2 0 4 2 6 2h0c4 3 7 6 9 10l-1 1z" class="C"></path><defs><linearGradient id="BY" x1="174.025" y1="595.309" x2="181.633" y2="594.43" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#BY)" d="M171 593h8v1c3 0 5 1 7 3-2 0-2 1-4 2l1 1h3c-2 0-4 0-6 1l-2 2c-1-1-2-2-2-3s0-3-1-3h-1c0-1-1-2-1-3v-1c-1 1-1 0-2 0z"></path><defs><linearGradient id="BZ" x1="198.966" y1="124.789" x2="183.762" y2="136.044" xlink:href="#B"><stop offset="0" stop-color="#2a2929"></stop><stop offset="1" stop-color="#646365"></stop></linearGradient></defs><path fill="url(#BZ)" d="M179 133c9-1 16-4 24-7l9-3 1 1-27 12-1-1c-1 0-2 0-3-1s-1-1-3-1z"></path><defs><linearGradient id="Ba" x1="413.144" y1="172.081" x2="416.934" y2="147.938" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#333"></stop></linearGradient></defs><path fill="url(#Ba)" d="M415 147c2 7 3 13 2 20v8c-3-8-3-18-4-27 1 0 2 0 2-1z"></path><defs><linearGradient id="Bb" x1="473.355" y1="129.441" x2="458.388" y2="130.527" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#414142"></stop></linearGradient></defs><path fill="url(#Bb)" d="M456 127l1-1c1 1 10 3 12 4 4 1 8 4 11 6l-2 1c0 1 1 1 1 2-1-1-2-1-3-2 0-1 1-1 2-1h0c-7-6-20-3-28-4l-1-1c2 0 3 1 5 0h5c0-1 1-1 0-2 0-1-2-2-3-2z"></path><defs><linearGradient id="Bc" x1="473.832" y1="253.558" x2="457.403" y2="259.646" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#Bc)" d="M460 257v1c5-2 10-6 15-8 0 1 0 1 1 2-4 2-8 4-12 7-2 1-3 3-5 4l-2 1h-1l1-1-2-2c-1 2-3 4-4 5h-1c1-1 4-5 6-6 0-1 1-1 2-2s0-2 2-3v2z"></path><path d="M455 261c1 0 1-1 2-1 1 1 1 2 2 3l-2 1h-1l1-1-2-2z" class="O"></path><defs><linearGradient id="Bd" x1="475.958" y1="273.901" x2="475.98" y2="282.008" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#3f3e3f"></stop></linearGradient></defs><path fill="url(#Bd)" d="M481 285l-6-6c-3-2-6-4-10-6h6c6 1 12 6 16 10 1 0 1 1 2 2-2 0-3-2-4-3-1 0-1 0-2 1-1-1-2-1-3-1 0 1 1 1 1 2v1z"></path><path d="M458 270h0c3-3 7-5 11-7 3-1 7-4 10-5 4-1 7-2 11-2v1h3c-9 1-19 5-27 9-1 1-3 1-5 3h1l4-2v2c-1 0-3 1-4 1 1 1 0 1 1 1 0 0 1 0 1 1l-3 1c-1-1-1-1-1-2-1 0-2-1-2-1z" class="G"></path><defs><linearGradient id="Be" x1="75.217" y1="364.209" x2="67.867" y2="360.362" xlink:href="#B"><stop offset="0" stop-color="#5a585b"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#Be)" d="M77 355c1 0 2-1 4-2 0 0 0 1 1 1-8 6-14 12-19 21 0-1 1-2 1-3l-1-1v-2c0-1 1-2 2-3 3-4 8-8 12-11z"></path><defs><linearGradient id="Bf" x1="185.865" y1="541.734" x2="191.019" y2="544.333" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#Bf)" d="M187 538h0 1v-1l1 1v1l1 1h1 0l-1 2v3 2 1c-1 2-2 4-2 6-1 1-1 3-2 4h0l-1 3c-1-3-2-9 0-12 0-2 1-3 2-4v-7z"></path><defs><linearGradient id="Bg" x1="145.139" y1="191.33" x2="131.273" y2="188.877" xlink:href="#B"><stop offset="0" stop-color="#706e6f"></stop><stop offset="1" stop-color="#99999a"></stop></linearGradient></defs><path fill="url(#Bg)" d="M147 198l-2 1c-3 1-6 0-9-1-3-2-4-3-5-6 0-3 0-6 1-8 1-1 3-2 4-3 2 0 3 0 5 1v1h-4c-2 1-3 3-3 4-1 2-1 4 0 6s5 3 7 4h4l1 1h1z"></path><defs><linearGradient id="Bh" x1="408.095" y1="105.515" x2="421.911" y2="99.351" xlink:href="#B"><stop offset="0" stop-color="#404041"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#Bh)" d="M406 101l13-2h1c1 1 1 1 2 0 0 2 0 2 1 3 0 1-1 2-2 3h-1 2 0-12v-2c-2 0-3 0-4-1v-1z"></path><path d="M419 99l17-3c-4 5-6 9-13 9h-1 0-2 1c1-1 2-2 2-3-1-1-1-1-1-3-1 1-1 1-2 0h-1z" class="M"></path><defs><linearGradient id="Bi" x1="135.896" y1="453.676" x2="130.005" y2="438.043" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#535153"></stop></linearGradient></defs><path fill="url(#Bi)" d="M131 428h0c2 2 2 7 3 9l4-1-5 22c0-1-1-1-1-2v-3-8l-1-8 1-1c0-2 0-4-1-6v-2h0z"></path><path d="M558 190h5l3 3c1 3 2 6 2 9-1 1-1 2-2 2-3 2-8 2-11 1-2-1-6-4-7-5v-2h1c3 3 7 6 11 6 2 0 4 0 5-1 1-2 1-5 0-8 0-1-1-3-2-3-2-1-4-1-5 0-4 1-7 4-11 5l-3 2v-1l2-1c2 0 4-3 6-4 1-1 2-1 4-2l2-1z" class="W"></path><path d="M116 425c3 1 7 2 8 5h0v1h-1l-10 5c-2-1-4-5-6-6h0v-1h0l1 1c2 0 7-2 9-3h0-1v-2z" class="I"></path><defs><linearGradient id="Bj" x1="170.727" y1="273.706" x2="167.703" y2="281.235" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#Bj)" d="M162 276l11-3 2 3c-1 3-3 5-5 7-1 0-1 1-1 0l-3-1s-1 0-1-1c-1 0-2 0-3-1l-1-1c0-1-1-1-2-2 1 0 2 0 3-1z"></path><path d="M162 276c2 1 4 3 5 4 0 1 0 1 1 1 0 0 0 1 1 1v1l-3-1s-1 0-1-1c-1 0-2 0-3-1l-1-1c0-1-1-1-2-2 1 0 2 0 3-1z" class="H"></path><path d="M119 308l1 1v1c1 0 1 0 2 1h-1c0 1-1 1-1 1h0c-3 1-6 1-9 1s-5 2-8 2v-1c-1 0-2 1-3 1h-3l-1 1c-5 1-10 3-15 6l-1-1c6-3 12-6 18-8 3 0 6 0 8-1h0l-3-1c3 0 7 1 9-1h-1 1s0-1-1-1h1v-1h1c2 1 4 1 6 0z" class="B"></path><path d="M119 308l1 1v1c1 0 1 0 2 1h-1c0 1-1 1-1 1h0c-2-1-5-2-8-2 0 0 0-1-1-1h1v-1h1c2 1 4 1 6 0z" class="N"></path><defs><linearGradient id="Bk" x1="162.55" y1="165.414" x2="156.201" y2="171.898" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#6a6b6c"></stop></linearGradient></defs><path fill="url(#Bk)" d="M146 154c9 2 15 8 20 15 2 4 3 9 3 13h-1v1c0-1 0-1-1-2v-1c0-3-1-6-3-9-4-8-11-11-19-14h2 1l1-1c-1 0-1 0-2-1 0 0-1 0-1-1z"></path><path d="M100 219c3-1 6-1 9-1h0 1c-8 0-15 3-21 9l-1 1c-1 1-1 2-1 3l1-1h0 1l-3 3c-3 4-4 9-4 14h-1v-2c0 2 0 3-1 4-1-1-1-3-1-4v-1c1-5 2-8 4-13 4-6 10-9 17-12z" class="D"></path><path d="M84 233c1-2 2-3 4-5-1 1-1 2-1 3l1-1h0 1l-3 3s-1 1-2 1v-1z" class="M"></path><path d="M84 233v1c1 0 2-1 2-1-3 4-4 9-4 14h-1v-2c0-5 1-8 3-12z" class="P"></path><defs><linearGradient id="Bl" x1="142.373" y1="320.249" x2="143.634" y2="314.514" xlink:href="#B"><stop offset="0" stop-color="#5f5f5e"></stop><stop offset="1" stop-color="#99999a"></stop></linearGradient></defs><path fill="url(#Bl)" d="M128 311l3 1h3 1c7 2 15 5 21 9 1 1 1 2 2 2v1l1 1v1c-3-2-6-4-9-5-7-3-14-6-21-7h0c-1-1-1-1-1-2-1 0-1 0-2-1h2z"></path><path d="M128 311l3 1h3 1-4-1-1c0-1-1 0-1 0l2 1-1 1h0c-1-1-1-1-1-2-1 0-1 0-2-1h2z" class="S"></path><path d="M490 232h5l-1 1v1c-7 1-13 2-19 4l-9 6-1-1c-1 0-2 2-4 2 2-1 4-3 5-4 7-6 15-9 24-9z" class="J"></path><path d="M474 237l1 1-9 6-1-1 9-6z" class="L"></path><path d="M494 233v1c-7 1-13 2-19 4l-1-1c1 0 2 0 2-1 6-2 12-1 18-3z" class="B"></path><path d="M496 218h4c1 1 1 1 2 1s1 1 2 1l1 1c1 0 1 1 2 1 0 1 0 0 1 1h0 1v-1l-1-1h0 0c1 1 2 1 3 2 2 1 3 1 5 2 1 1 3 2 4 2l1 1h1v1h-2 0l1 1v1l-1 1h2c0 1 1 1 1 1l-1 1s0-1-1-1h-1-1c0-1-2-1-3-1 0 0-1 0-1-1-1 0-3-1-3-2v-1c-2-2-5-4-7-6-3-2-7-2-10-3l1-1z" class="K"></path><path d="M470 227v1h0c-2 1-4 2-5 4h-1l1 1 1-1 1 1-2 2-1 1c0 1 0 1-1 2-1 0-1 0-1 1l1 2h-1c0 1-1 2-1 3h0l-1 2c-2 0-3 1-4 2-3 2-5 5-8 7v-1l7-7c2-2 3-5 5-7l-6 2h0c3-4 8-9 10-14h0l1 1 1-1h0c1 0 2-1 4-1z" class="M"></path><path d="M93 327h1l1-1 3 5c1 2 3 3 5 4h1v-2c2-1 4-4 6-5l1 1c0 3-1 6-3 8-2 1-3 1-4 1h-2l-1-1v1l-1-1v1l-3-3c-1-2-2-4-3-7l-2 3h-1c1-1 1-3 2-4z" class="H"></path><path d="M470 227c1-1 2-1 4-2 6-2 12-2 18-2l-1 1c-3 0-6 0-9 1 6 0 10 0 16 2 2 1 4 3 7 4l9 3c3 2 7 2 10 4-1 0-2 0-3-1-8-1-14-4-21-8-6-3-12-3-18-3-2-1-5 0-7 1 0 0-1 1-1 2h-1c-2 1-4 2-6 4l-1-1-1 1-1-1h1c1-2 3-3 5-4h0v-1z" class="B"></path><path d="M465 232h1c2-2 4-3 7-3-2 1-4 2-6 4l-1-1-1 1-1-1h1z" class="R"></path><path d="M218 126c5-4 11-7 17-10s13-6 20-8c-5 4-12 5-18 9-4 2-7 4-10 7-1 1-3 3-5 4 0 0 0-1-1 0-1 0-10 9-12 10l-1 1c1-3 4-6 7-8 1-1 4-3 5-5h-2z" class="B"></path><defs><linearGradient id="Bm" x1="528.499" y1="275.807" x2="527.441" y2="285.687" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#Bm)" d="M527 277c5-2 8-1 12 0 3 1 5 2 7 3h0c2 1 4 2 5 4h-1c-3-3-5-3-9-5-2 0-7-1-10 0h-1c-1 0-1 0-2 1-2 0-3 1-5 2 0 0-1 0-1 1h-1l-3 2-1-1h-1 0c-2 1-3 2-4 2l-4 5c0 1-1 1-2 1 2-4 6-7 9-10l2-2 1-1v1h4 0 1s0-1 1-1h0 1c1 0 2-1 2-2z"></path><path d="M99 284c-2-4-3-7-3-11 2 4 3 8 5 11l4 7c1 2 2 4 5 6v-1c3 2 5 5 8 8 2 1 4 2 6 4-6-2-10-5-15-9h0a57.31 57.31 0 0 1-11-11c1-1 1-3 1-4z" class="B"></path><defs><linearGradient id="Bn" x1="103.463" y1="291.504" x2="98.241" y2="287.002" xlink:href="#B"><stop offset="0" stop-color="#595958"></stop><stop offset="1" stop-color="#79797a"></stop></linearGradient></defs><path fill="url(#Bn)" d="M99 284l6 10 3 3 1 1v1a57.31 57.31 0 0 1-11-11c1-1 1-3 1-4z"></path><defs><linearGradient id="Bo" x1="479.541" y1="227.773" x2="473.258" y2="242.548" xlink:href="#B"><stop offset="0" stop-color="#242523"></stop><stop offset="1" stop-color="#4f4c51"></stop></linearGradient></defs><path fill="url(#Bo)" d="M475 230l1 1c-3 1-7 3-10 5 2 0 5-2 7-2 7-3 14-4 22-3l-5 1h0c-9 0-17 3-24 9-1 1-3 3-5 4v-1h0c0-1 1-2 1-3h1l-1-2c0-1 0-1 1-1 1-1 1-1 1-2l1-1h1c3-1 5-4 9-5z"></path><path d="M462 239h1l1-1h1v1h0v1l1 1c-1 1-3 3-5 4v-1h0c0-1 1-2 1-3h1l-1-2z" class="O"></path><defs><linearGradient id="Bp" x1="107.697" y1="468.323" x2="116.716" y2="462.944" xlink:href="#B"><stop offset="0" stop-color="#3d3f3e"></stop><stop offset="1" stop-color="#878787"></stop></linearGradient></defs><path fill="url(#Bp)" d="M102 459v-1l1-3c1 0 3 1 4 1h0c7 2 11 7 14 14 1 1 1 1 1 2v1c1 1 1 2 1 3-1-2-1-3-2-4h-1c-1-2-2-4-4-5-3-4-7-5-13-5l-1-1h8l-2-1c-2-1-4-1-6-1z"></path><defs><linearGradient id="Bq" x1="189.5" y1="538.097" x2="195.108" y2="541.435" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#Bq)" d="M191 533v-1h1c1 1 1 2 1 3l2-5 1 1 1 2v1h0l1 2h0l1 2h-1v1 2c-1 0-1 1-1 2s-1 1-1 2h-1c0 1-1 2-2 3v-1l1-2c1-1 1-1 0-2-1 2-2 3-3 5-1 1-1 3-2 4l1-4v-1-2-3l1-2h0-1l-1-1v-1h1c1-2 1-3 1-5z"></path><path d="M168 368c2 2 5 4 5 7v7h-1c-1 4-3 7-7 8-2 1-4 2-7 2 3-3 5-4 7-7 4-5 5-11 3-17z" class="R"></path><defs><linearGradient id="Br" x1="93.435" y1="436.36" x2="102.109" y2="451.342" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#Br)" d="M101 445h-1-1c-5-2-11-1-15-3v-1c1 0 2-1 3-1h1c7-1 16 0 22 4v1c-1 2-1 3-2 4v-1h-1c-1 0-1 0-1-1h-2s-1 0-1-1h-2v-1z"></path><path d="M101 442h1c1 0 1 0 2 1-1 1-3 0-4 0h-1 3l-1-1z" class="G"></path><path d="M450 269c-2 0-6 3-7 4 3-5 6-9 8-14v-1h-1c4-4 5-9 10-12h0c0 3-1 5-1 7l-2 2c1 0 2-1 3-1h1l-1 1c-2 1-1 2-2 3s-2 1-2 2c-2 1-5 5-6 6h1c0 2-1 2-1 3h0z" class="M"></path><defs><linearGradient id="Bs" x1="218.725" y1="606.94" x2="202.966" y2="606.208" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#585859"></stop></linearGradient></defs><path fill="url(#Bs)" d="M203 597c6 5 10 11 16 16 1 1 5 3 5 4-8-5-18-8-27-12h2 0c1 0 3 1 4 1v-1c0-2 0-2 1-4v-1l-1 1c-1 0-1 1-2 1v-1s1-1 1-2c1 0 1-1 1-2z"></path><defs><linearGradient id="Bt" x1="165.754" y1="413.991" x2="160.578" y2="415.906" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#747474"></stop></linearGradient></defs><path fill="url(#Bt)" d="M165 406l2 2c1 3 2 9 2 12v1c-1 1-3 1-4 1h-1c0-1 0-1-1-1-1-1-2-3-3-5 0-4 1-6 3-9l2-1z"></path><path d="M165 406l2 2c1 3 2 9 2 12v1c-1 1-3 1-4 1h-1l2-1c1-1 0-2 0-4v-6c-1-2-1-3-3-4l2-1z" class="H"></path><defs><linearGradient id="Bu" x1="487.172" y1="271.333" x2="495.813" y2="281.096" xlink:href="#B"><stop offset="0" stop-color="#2e2d2d"></stop><stop offset="1" stop-color="#636363"></stop></linearGradient></defs><path fill="url(#Bu)" d="M491 271c-2-5-3-6-7-9l-1-1h0 1c2 1 4 3 6 5l2 4v1c1 1 1 1 2 1 1-1 2-1 2-2h2 0v-1l1 1v1c0 1-1 3-1 3-2 1-4 4-5 6v1 5h0l-7-10c2 0 2 1 3 2l1-1c0-1 0-2-1-4h0l-1-1v-2s1 1 2 1h1z"></path><path d="M488 272v-2s1 1 2 1h1v2c-1 0-1-1-2 0h0l-1-1z" class="B"></path><path d="M498 270v-1l1 1v1c-2 1-5 5-6 6v-1-1c1-2 4-3 5-5z" class="N"></path><path d="M493 277c1-1 4-5 6-6 0 1-1 3-1 3-2 1-4 4-5 6v1c0-1-1-1 0-3h-1l1-1z" class="O"></path><defs><linearGradient id="Bv" x1="182.417" y1="555.275" x2="166.727" y2="548.743" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#666667"></stop></linearGradient></defs><path fill="url(#Bv)" d="M175 542c4 7 3 15 5 23 1 2 2 4 2 6l-1-1h0c-1-1-2-2-2-3v-1c-3-8-5-16-11-23 2 0 4-1 5-2l1 1h1z"></path><defs><linearGradient id="Bw" x1="110.622" y1="203.642" x2="109.281" y2="199.163" xlink:href="#B"><stop offset="0" stop-color="#111011"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#Bw)" d="M113 194v2c1 2 1 4 3 6l1 2-1 1h0c0-1-1-1-1-1h-5v-1c-2 0-3-1-5-2-1 0-2-1-2-2-2-2-2-6-1-9v5c1 0 1-1 2-1 1-1 7 1 8 1h1v-1z"></path><path d="M167 568v-1c-1-1-2-2-3-2l-1-1c3 0 5-1 8-1s5 2 8 4c0 1 1 2 2 3h0l1 1h0c2 5 8 9 12 12 1 2 3 3 4 4-2-1-4-3-7-5l-7-5-3-3c-2-1-5-4-8-5l-1-1h0-3c-1 0-1 1-1 1h-3l2-1z" class="O"></path><path d="M167 568v-1-1c1-1 2-1 4-1 0 0 1 1 2 1h0c2 1 4 3 6 4 2 0 2 2 3 3l-1 1c-2-1-5-4-8-5l-1-1h0-3c-1 0-1 1-1 1h-3l2-1z" class="S"></path><defs><linearGradient id="Bx" x1="195.115" y1="181.51" x2="196.167" y2="167.033" xlink:href="#B"><stop offset="0" stop-color="#0d0b0c"></stop><stop offset="1" stop-color="#3f3e3e"></stop></linearGradient></defs><path fill="url(#Bx)" d="M191 164c0 2 2 3 3 4 2 2 3 1 5 2h2v-2c0-1 1-1 1-2 0 3 0 10-2 12l-1-1h-1c-2 2-3 4-5 7l-1-10v-2-2h0c-1-1-1-2-2-2 0 1-1 4 0 5v3c-1-4-1-9 0-12h1z"></path><defs><linearGradient id="By" x1="135.796" y1="482.395" x2="122.983" y2="470.789" xlink:href="#B"><stop offset="0" stop-color="#444443"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#By)" d="M127 461c4 9 4 19 5 28h0c-2 0-3-4-4-5h0-1l-1-1-1-5h0l1-1-3-8 2 2h0c1-1 1-3 2-4 0-1 0-1 1-2h-1v-2-2z"></path><path d="M126 477l2 7h-1l-1-1-1-5h0l1-1z" class="G"></path><defs><linearGradient id="Bz" x1="234.861" y1="83.626" x2="230.274" y2="88.219" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#Bz)" d="M224 89c1-3 2-5 4-6s4-1 6-1l4 4v3c-1 2-4 2-4 4l2 2c-1 2-1 4-2 6l-1-1 1-1v-2h-1c1-1 1-1 1-2l-2-2h-2c-1-2-1-3-3-4h0v-1l-3 1z"></path><defs><linearGradient id="CA" x1="147.817" y1="469.319" x2="150.271" y2="455.321" xlink:href="#B"><stop offset="0" stop-color="#2f3031"></stop><stop offset="1" stop-color="#757373"></stop></linearGradient></defs><path fill="url(#CA)" d="M150 456l2-1v1c2 2 6 0 9 1 2 1 4 1 6 2h-2c-3-1-5 0-8 1h-2l-2 1c-2 0-4 2-6 3h5c-5 0-7 1-11 4h-3c3-4 6-7 9-10l3-2z"></path><path d="M147 458l3-2 2 1c-2 1-2 2-4 2 0-1 0-1-1-1z" class="M"></path><defs><linearGradient id="CB" x1="455.148" y1="269.137" x2="448.808" y2="267.862" xlink:href="#B"><stop offset="0" stop-color="#605f60"></stop><stop offset="1" stop-color="#828081"></stop></linearGradient></defs><path fill="url(#CB)" d="M455 261l2 2-1 1v1l1 1c0 1 0 1-1 2h0l1 1-2 1h3s1 1 2 1c0 1 0 1 1 2h-2l-4 2-3 2-3 1v-1h-1v-1l-1-1 3-6h0c0-1 1-1 1-3 1-1 3-3 4-5z"></path><path d="M458 270s1 1 2 1c0 1 0 1 1 2h-2c0-1-1-1-2-1l-1-1c-1 0-1 1-2 1 0-1 1-1 1-2h3z" class="Q"></path><path d="M452 277l-2-1v-1c2-1 2-2 3-3h0 0c1 1 2 1 3 1v1l-1 1-3 2z" class="R"></path><path d="M65 431c1-2 1-2 2-3 3 3 8 5 14 5l5-1v1l-3 6c-5 1-9 0-14-4l-4-4z" class="M"></path><path d="M115 458l1-1 1-1c1-1 2-2 2-3v-1l1-1c2 3 6 6 7 10v2 2h1c-1 1-1 1-1 2-1 1-1 3-2 4h0l-2-2c-2-4-4-8-8-11z" class="S"></path><defs><linearGradient id="CC" x1="130.969" y1="386.217" x2="136.415" y2="408.89" xlink:href="#B"><stop offset="0" stop-color="#4c4b4c"></stop><stop offset="1" stop-color="#767676"></stop></linearGradient></defs><path fill="url(#CC)" d="M133 399h0s1-1 2-1c0-1 1-3 1-4 2-4 4-8 4-13 0-2-1-4 0-6 2 6 1 11 0 17-1 3-1 5-2 7s-3 5-3 6c-1 2-2 3-3 5-1 1-2 1-3 2v1l-2-2v1s0 1-1 1v-4h1c3-3 4-6 6-10z"></path><defs><linearGradient id="CD" x1="472.523" y1="121.129" x2="458.786" y2="122.052" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient></defs><path fill="url(#CD)" d="M458 116h2c7 3 12 8 17 13-3-2-6-3-10-3-2-1-4-1-6 0h-4l-1 1h0-2c-1 0-2-1-3-2h3 2c1 0 1-1 2-2h1l1-1c0-1-1-2-1-2h-1-1-1-8c1 0 1-1 2-1l4-1h2c1 0 2 0 2-1v-1h0z"></path><path d="M454 125c2 1 5 0 7 1h-4l-1 1h0-2c-1 0-2-1-3-2h3z" class="H"></path><defs><linearGradient id="CE" x1="109.439" y1="405.792" x2="123.829" y2="410.83" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#515050"></stop></linearGradient></defs><path fill="url(#CE)" d="M127 420c-1-1-2-3-3-4-5-4-12-5-18-6 2-2 4-3 7-4h1c2-1 5-1 7 0 2 0 4 2 6 3h-1v4c-1 1-1 1-1 2 0 0 1 0 1 1 1 1 1 3 1 4z"></path><path d="M475 198h0c1 0 2-1 3-2h1-1l1 1c-2 1-3 2-3 4h1 0l2-1v1c-1 1-3 1-3 2 1 0 2 0 3-1l2 2-5 2c-1 1-1 2-2 3h2l-2 2-3 2-5 5 3-7-8 5h0c3-6 9-13 14-18z" class="U"></path><path d="M474 209h2l-2 2-3 2c1-2 2-3 3-4z" class="R"></path><defs><linearGradient id="CF" x1="179.697" y1="521.482" x2="169.574" y2="516.221" xlink:href="#B"><stop offset="0" stop-color="#6c6d6c"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#CF)" d="M173 509c2-2 3-2 5-2h1c-2 8-4 15-4 22-1 1-1 1-1 2v2c-1-3-3-5-4-8v-3h0c-1-3 0-4 0-7 0-1 2-3 2-4l1-2z"></path><defs><linearGradient id="CG" x1="353.189" y1="82.125" x2="370.425" y2="102.233" xlink:href="#B"><stop offset="0" stop-color="#201e1f"></stop><stop offset="1" stop-color="#4f504f"></stop></linearGradient></defs><path fill="url(#CG)" d="M351 88c11 2 22 5 32 10l6 3h0c-3 0-5-2-8-3-1 0-2-1-3-1s-2 1-3 1v1c1 0 3 1 4 2v1s-2-1-2-2c-2 0-4-1-5-2-9-3-19-8-28-10h0c0-1 5 0 7 0z"></path><defs><linearGradient id="CH" x1="101.943" y1="444.269" x2="116.902" y2="455.802" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#CH)" d="M110 444c4 2 7 4 10 7l-1 1v1c0 1-1 2-2 3l-1 1-1 1c-2-2-4-3-7-4l-3-1-1-3s-2 1-3 0h-2l1-1v-2-2h1v1h2c0 1 1 1 1 1h2c0 1 0 1 1 1h1v1c1-1 1-2 2-4v-1z"></path><path d="M104 450c2 0 3 1 4 2v2l-3-1-1-3z" class="P"></path><path d="M152 477c5 0 9-1 13-1l1 1c3 0 5 2 8 4-1 1-1 1-2 1-2-3-7-5-11-5-1 0-3 0-4 1-1 0-1 0-1 1 0 0-1 0-1 1-2 2-3 5-3 7-1 3 0 5 0 7 0 3 0 8-1 11-1 1-2 3-2 4 1 1 1 1 1 2v1c-1-2-2-4-2-5 1-1 0-4 0-6 1-3 1-8 1-11-1-3-1-5 0-8 1-2 2-3 3-5z" class="H"></path><path d="M132 222c2-1 4-3 6-4-1 1-1 2-1 3l-1 2c0 1 0 2-1 2h0v-2c-1 3-1 7 0 10l2 2c0 1 0 1 1 2s2 1 3 2 2 1 3 2h1c0 1 1 1 2 2h3c1 1 3 2 4 2l8 4 13 6v1c-4-1-8-3-12-5-3-1-6-3-10-5l-18-6c0-2-1-2-2-3l1-1c0-1-1-1-1-1v-3h-1 0c-1-2-1-5-1-8l1-2z" class="B"></path><path d="M131 224l1-2v3c0 2 1 5 0 7-1-2-1-5-1-8z" class="D"></path><defs><linearGradient id="CI" x1="166.852" y1="346.498" x2="152.716" y2="359.766" xlink:href="#B"><stop offset="0" stop-color="#858585"></stop><stop offset="1" stop-color="#a5a4a4"></stop></linearGradient></defs><path fill="url(#CI)" d="M158 352c1-3 2-6 4-8h3c1 1 2 1 2 3 1 2 1 6-1 8-3 4-8 4-12 7h-1c0-2 4-5 4-8h0c1-1 1-1 1-2z"></path><path d="M171 213c-2-1-3-3-5-3 5 0 8 1 12 3h0c1 1 2 2 2 4 1 1 0 4 0 6 1 3 1 7 0 11l-3 6c0 2 0 5-1 6-3 2-5 2-8 2 1 0 1-1 1-1h4c1-1 1-3 1-4v-2l-1 1h0c1-5 4-8 4-13 1-3 1-11-1-14-1-1-3-2-5-2z" class="Q"></path><path d="M114 361c1-1 2-1 3-1 3 0 6 1 9 3-1 1-1 1-2 1v1c2 1 4 2 5 3 0 1-1 1-2 1-1 1-1 2-2 3-1 6-4 11-7 16h0c-1-1-1-2-1-3s1-2 0-3h0 1 0c1-1 2-3 3-4v-1l1-2c0-3 0-5-2-8s-5-4-8-4l2-2z" class="R"></path><path d="M114 361c1-1 2-1 3-1 3 0 6 1 9 3-1 1-1 1-2 1v1c2 1 4 2 5 3 0 1-1 1-2 1-1 1-1 2-2 3v-2c1-1 1 0 1-1h-1c-1-1-3-3-3-4v-1h0l-1-1c-1-2-4-2-7-2z" class="P"></path><defs><linearGradient id="CJ" x1="185.651" y1="522.821" x2="175.883" y2="515.731" xlink:href="#B"><stop offset="0" stop-color="#676767"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#CJ)" d="M182 507c1 0 3-2 5-1-3 8-6 17-7 26h-1v1h-2v-4c-1-2-1-4-1-6 0-5 3-12 6-16z"></path><defs><linearGradient id="CK" x1="440.878" y1="156.47" x2="466.977" y2="173.857" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#CK)" d="M445 153c3 3 7 6 10 9l15 12h-4l-2-1h-1c-1 0-1 0-1-1l-1 1v2h-1c0 1 1 1 0 2l-1-2h0c0-4-9-12-12-15-1-2-3-4-4-6 0-1 1-1 2-1z"></path><path d="M572 174l3 8c0 3 0 8 1 10l4 4c3 3 7 5 11 7 2 2 4 3 6 4l-2 1c-1-1-4-1-6-1h-1l-1-1c-2 0-4-2-6-4-2-1-6-4-7-7v-4c0-2-1-5-1-7s1-3 0-5c-1 1-1 2-1 2v-3c-1-1-1-3 0-4z" class="D"></path><defs><linearGradient id="CL" x1="140.392" y1="512.647" x2="157.696" y2="514.61" xlink:href="#B"><stop offset="0" stop-color="#5d5d5e"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#CL)" d="M145 510c-1-2-1-4 1-6h1l1 3c0 1 1 3 2 5 3 6 8 12 13 16 1 0 1 1 2 1v1l-1-1c-4-4-10-7-16-9-3-1-6-2-9-4h1l1 1v-1-1h0 0v-1h1 1l-1-1-1-2v-1c1 0 2 0 3 1h0l5 4c-1-2-3-3-4-5z"></path><defs><linearGradient id="CM" x1="162.297" y1="435.395" x2="151.827" y2="454.709" xlink:href="#B"><stop offset="0" stop-color="#676666"></stop><stop offset="1" stop-color="#9a999a"></stop></linearGradient></defs><path fill="url(#CM)" d="M159 436s1-1 2-1c2 1 3 2 5 4v2l-1 1h0l-16 11c-1 0-2-1-3-1l12-16h1z"></path><defs><linearGradient id="CN" x1="119.186" y1="210.099" x2="99.873" y2="216.614" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#CN)" d="M97 210l2-1v1c1 0 5-1 6-1v1l-1 1c3 0 5-1 8-1 2 0 5 4 8 5 1 1 3 2 5 3-6 0-11-1-16 0-3 0-6 0-9 1 0-1 1-1 1-1h1c0-2-1-3-2-5h0l-3-3z"></path><defs><linearGradient id="CO" x1="120.421" y1="445.853" x2="123.798" y2="432.471" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#CO)" d="M124 430c3 3 4 5 5 9 0 1 0 1-1 2s-2 1-3 2c0 2 0 2-1 3h0l-1 1v-1h-1c-3-3-6-7-9-10l10-5h1v-1h0z"></path><path d="M152 455c8-5 15-5 24-5 0 2 0 3 1 4l-2 1c-1 1-1 1-1 2-1 0-2 1-3 1 0 0 0 1-1 1 0-1-1 0-2 0h-1c-2-1-4-1-6-2-3-1-7 1-9-1v-1z" class="V"></path><path d="M440 160h1l1 1h0 1l3 3c0 1 0 3 1 4 2 3 4 7 5 11 0 0-1 1-1 2 1 8 2 15 2 23v-1l-1 3v-5h0l-1 2v2c-1 0 0-9 0-10-1-1 0-2-1-3 0 2 0 4-1 7h0c0-3 1-6 1-9s-1-6-2-9l-6-16h-1c0-1 0-4-1-5z" class="F"></path><path d="M452 192c1 4 1 8 1 11l-1 3v-5h0l-1 2c0-3 0-8 1-11z" class="Q"></path><path d="M442 165h1l3 6c1 3 3 6 3 8l-1 2-6-16zm0-4h1l3 3c0 1 0 3 1 4 2 3 4 7 5 11 0 0-1 1-1 2 0-3-2-7-3-9s-2-3-2-5c-1-2-2-4-4-6z" class="B"></path><path d="M448 181l1-2c1 4 3 10 3 13-1 3-1 8-1 11v2c-1 0 0-9 0-10-1-1 0-2-1-3 0 2 0 4-1 7h0c0-3 1-6 1-9s-1-6-2-9z" class="G"></path><defs><linearGradient id="CP" x1="128.475" y1="181.823" x2="117.567" y2="171.812" xlink:href="#B"><stop offset="0" stop-color="#5e5e5f"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#CP)" d="M132 162c3 0 6 0 8 1h0-4c-6 2-12 5-16 10-4 6-4 14-2 21v3c0-1-1-1-1-2h0-2c0-1 0-1-1-2h0c-1-3-1-10-1-13h0c1-2 1-3 2-4 3-8 10-12 17-14z"></path><path d="M464 228c-3 0-4 1-6 3 1-3 3-5 5-8 2-2 3-4 5-6 4-3 12-5 17-4h-1-1c-4 1-11 3-14 6 2 0 3-1 5-2 1 0 3-1 4-1 2-1 6-1 8-1 1 0 1 1 2 1-2 1-5 1-7 2h0c-4 2-8 3-12 6 2-1 5-2 7-1h0c-3 1-8 3-10 5l-1 1-1-1z" class="U"></path><path d="M478 216c2-1 6-1 8-1 1 0 1 1 2 1-2 1-5 1-7 2-4 2-9 3-13 4 3-2 7-4 11-5l-1-1z" class="E"></path><defs><linearGradient id="CQ" x1="84.782" y1="327.503" x2="82.078" y2="321.134" xlink:href="#B"><stop offset="0" stop-color="#504f50"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#CQ)" d="M81 322c5-3 10-5 15-6l1-1h3c1 0 2-1 3-1v1c-7 3-12 8-19 12v-1h0-1c0 1 0 1-1 1l-2 2h-1c0 1 0 1-1 1h-1 0-1s-1-1-2-1v1h0c-1 1-2 1-3 2-1 0-1 1-1 1v1c-1 0-1 0-2 1 0-2 2-3 3-5h0c2-3 6-6 10-8z"></path><defs><linearGradient id="CR" x1="421.752" y1="133.557" x2="434.582" y2="126.858" xlink:href="#B"><stop offset="0" stop-color="#0c0b0c"></stop><stop offset="1" stop-color="#292928"></stop></linearGradient></defs><path fill="url(#CR)" d="M416 125c2 0 5 1 7 2l8 4c3 1 7 2 9 4h0l5 2h0v1l1 1c-1 0-1 1-1 1-1 0-1 0-2 1-1-1-2-1-3-2v1h-1c-1 0-2-1-2-1-1 0-1 0-2 1l4 4c-3-2-6-5-8-7-1 0-5-4-6-4h-1 0 0c-1-1-2-2-4-2l-4-6z"></path><path d="M440 135l5 2h0v1l1 1c-1 0-1 1-1 1-1 0-1 0-2 1-1-1-2-1-3-2l-3-3c2 1 3 1 5 2h0c0-1-1-2-2-2v-1z" class="Q"></path><path d="M431 137h1l4 2-4-4h0c2 0 4 0 5 1l3 3v1h-1c-1 0-2-1-2-1-1 0-1 0-2 1l4 4c-3-2-6-5-8-7zM77 289v-1c6 5 13 10 19 13 1 1 2 1 3 1h1v-4c4 3 8 6 12 8l7 2c-2 1-4 1-6 0h-1v1h-1c1 0 1 1 1 1h-1c-5 0-10-1-15-2 1 0 2-1 3-2h0c-1 0-1-1-1-1 0-1-1-1-1-1v-1c-4-2-7-4-11-6l-7-6-2-2z" class="B"></path><defs><linearGradient id="CS" x1="107.042" y1="301.605" x2="100.059" y2="302.914" xlink:href="#B"><stop offset="0" stop-color="#414241"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#CS)" d="M100 298c4 3 8 6 12 8v1c-1 0-1 0-2-1-2-1-4-1-6-2l-4-1-1-1h1v-4z"></path><defs><linearGradient id="CT" x1="104.932" y1="309.673" x2="104.441" y2="303.856" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#CT)" d="M97 303l8 3h1c2 1 5 1 6 2v1h-1c1 0 1 1 1 1h-1c-5 0-10-1-15-2 1 0 2-1 3-2h0c-1 0-1-1-1-1 0-1-1-1-1-1v-1z"></path><defs><linearGradient id="CU" x1="141.143" y1="374.562" x2="126.016" y2="386.027" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#3f3e3f"></stop></linearGradient></defs><path fill="url(#CU)" d="M132 364c2 0 3 1 4 2 1 2 4 6 4 9-1 2 0 4 0 6 0 5-2 9-4 13 0 1-1 3-1 4-1 0-2 1-2 1h0l3-20c0-3 0-6-2-8s-5-2-7-2c1 0 2 0 2-1-1-1-3-2-5-3v-1h3 1 4z"></path><path d="M132 364c2 0 3 1 4 2l-1 1h-2c-1-1-2-1-3-1-2 0-3 0-4-2h1 1 4z" class="K"></path><defs><linearGradient id="CV" x1="133.335" y1="259.605" x2="115.018" y2="265.06" xlink:href="#B"><stop offset="0" stop-color="#5a5a5b"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#CV)" d="M127 253l3 3c2 1 3 2 4 3h0c-3 1-4 3-6 3h0 0v1l-3 3c0 2 0 3-1 4v1h-1l-4 5c0-1-1-1-1-2 1-1 1-1 1-2v-1c1 0 1-1 1-2h-1c-1 1-1 2-1 3h-1c1-2 2-5 0-7 1-1 1-2 1-3h0c2-4 6-7 9-9z"></path><path d="M128 262v1l-3 3c0 2 0 3-1 4v1h-1l-4 5c0-1-1-1-1-2 1-1 1-1 1-2v-1c1 0 1-1 1-2h1v-1c2-2 4-4 7-6z" class="V"></path><defs><linearGradient id="CW" x1="132.933" y1="368.61" x2="151.164" y2="393.738" xlink:href="#B"><stop offset="0" stop-color="#393838"></stop><stop offset="1" stop-color="#6c6d6d"></stop></linearGradient></defs><path fill="url(#CW)" d="M137 403c5-8 7-18 6-27 0-4-1-9-1-13 2-2 3-4 6-6 0 1 0 1-1 2-1 3-1 6-1 9v1-1l-1 1 2 6c-1 0-1 0-1 1-1 3 1 7 1 10 0 5-2 10-3 14-1 0-1 2-2 2v-1c-1 1-2 1-3 2h-2z"></path><path d="M145 369v-1-1c-1-2-1-3 0-5 1 2 1 4 1 6l-1 1zM81 245v2h1v1c0 1 0 2 1 4 1 3 2 5 5 8-1-4-2-7-2-10l6 7 1 1 4 5c-3 3-5 7-8 9 0 1-1 1-1 1s-1-4-2-5l-6-19c1-1 1-2 1-4z" class="G"></path><path d="M93 258l4 5c-3 3-5 7-8 9 0 1-1 1-1 1s-1-4-2-5l1-1c0-1 0-2 1-2 1-2 2-3 4-4 1 0 1 0 1-1v-2z" class="N"></path><path d="M145 241c5 1 10 1 16-1h0c1 2 3 5 5 6 0 1 2 1 2 2 3 0 5 0 8-2v1h2c1 2 0 5 0 7v2h-3 0v-1l-13-6-8-4c-1 0-3-1-4-2h-3c-1-1-2-1-2-2z" class="J"></path><path d="M163 249l-9-6 5-2c2 3 4 5 6 7h1l-1 1c1 0 1 1 1 1l-3-1z" class="P"></path><path d="M176 247h2c1 2 0 5 0 7v2h-3 0v-1l-13-6h1l3 1s0-1-1-1l1-1c2 1 4 1 6 1l2-1h1l1-1z" class="C"></path><path d="M175 248h1c0 2 0 4-1 5h-4l2-1c0-1 0-2 1-4h1z" class="L"></path><defs><linearGradient id="CX" x1="172.341" y1="251.272" x2="166.522" y2="247.488" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#CX)" d="M166 248c2 1 4 1 6 1l2-1c-1 2-1 3-1 4l-2 1-5-3s0-1-1-1l1-1z"></path><defs><linearGradient id="CY" x1="143.679" y1="245.317" x2="139.894" y2="259.619" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#CY)" d="M127 253s1-1 2-1c3-2 6-3 9-4 3-2 5-3 9-5-1 1-1 2-2 2-1 2-3 4-5 6 2-1 3-1 5-2l1 1h1 0l1-1c2 1 5 1 7 1h0c1 0 3 1 4 2-7 0-14 1-20 4-2 1-4 2-5 3-1-1-2-2-4-3l-3-3z"></path><defs><linearGradient id="CZ" x1="124.468" y1="381.17" x2="124.01" y2="398.859" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#CZ)" d="M119 395c0-2 0-3-1-5 2-4 4-8 7-12 2 0 6 1 7 3 0 2 0 5-1 7-2 3-5 6-7 9h-1c-1 0-2 1-2 1v1l-1-1-1-3z"></path><defs><linearGradient id="Ca" x1="150.026" y1="569.855" x2="144.472" y2="567.324" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#Ca)" d="M139 552c4-1 8-1 11 1 0 2 0 3-1 5v1 1c2 2 4 5 3 8-1 2-3 4-4 5-2 1-3 1-5 1l-2-1h0l-1-1-1-2h1c0-1 1-2 1-3 2-3 4-5 4-9 0-1 0-2-1-3h0 0 0v-1c1-1 0-1 0-2-1-1-4 0-5 0z"></path><path d="M140 572c1 0 1-1 2-1v1l-1 1-1-1z" class="P"></path><path d="M145 558l1-2c1 2 1 5 0 6l-3 6s-1 1-1 2v1c-1 0-1 1-2 1l-1-2h1c0-1 1-2 1-3 2-3 4-5 4-9z" class="R"></path><path d="M96 210h1l3 3h0c1 2 2 3 2 5h-1s-1 0-1 1c-7 3-13 6-17 12 0-1 0-4-1-5 4-7 7-12 14-16z" class="S"></path><defs><linearGradient id="Cb" x1="185.841" y1="573.771" x2="163.141" y2="576.724" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#Cb)" d="M165 569h3s0-1 1-1h3 0l1 1c6 6 12 11 19 15l-12-3c-2-1-4-1-6-2-5-1-9-3-12-6 1-2 2-3 3-4z"></path><defs><linearGradient id="Cc" x1="58.653" y1="400.688" x2="69.868" y2="398.908" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#6d6d6d"></stop></linearGradient></defs><path fill="url(#Cc)" d="M63 381c1-1 2-2 4-2l1 2c-4 8-6 16-5 25 0 2 1 5 1 7 1 1 1 2 2 2v1h0-1c0 3 1 6 1 9-4-5-5-11-7-17 0-3 0-7-1-11l2-7h0c1-1 3-7 3-9z"></path><defs><linearGradient id="Cd" x1="84.999" y1="372.771" x2="73.561" y2="364.018" xlink:href="#B"><stop offset="0" stop-color="#2a292c"></stop><stop offset="1" stop-color="#91908f"></stop></linearGradient></defs><path fill="url(#Cd)" d="M97 353l1 1c1-1 2-1 3-1l-2 1c-1 0-2 0-2 1h-1c-4 2-8 4-11 6-8 5-13 12-17 20l-1-2c-2 0-3 1-4 2 0 2-2 8-3 9h0c0-4 1-7 2-10l3-6c6-8 14-13 23-17 3-2 6-3 9-4z"></path><path d="M62 380h0v3c-1 1-1 2-1 3v-1c1-1 1-2 1-3l1-1c0 2-2 8-3 9h0c0-4 1-7 2-10z" class="O"></path><defs><linearGradient id="Ce" x1="112.079" y1="502.698" x2="142.129" y2="509.968" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#Ce)" d="M104 483c2 1 3 3 5 4 4 5 7 10 12 14 1 1 2 1 3 2l15 9c1 1 2 1 3 2h-1v1h0 0v1 1l-1-1h-1c-3-1-6-2-9-4-5-2-10-5-15-9-7-4-13-9-19-15v-1c2 3 5 5 7 7 2 1 3 2 4 3l5 4 1-1 3-3c-2-2-3-3-4-5l-3-3c-1-2-3-4-5-6z"></path><path d="M141 516c-1-1-1-1-2-1l-1-1 1-1v-1c1 1 2 1 3 2h-1v1h0 0v1z" class="O"></path><defs><linearGradient id="Cf" x1="87.112" y1="295.761" x2="82.654" y2="303.311" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#949394"></stop></linearGradient></defs><path fill="url(#Cf)" d="M76 294v-2 1h0l-1-1c0-1 0-1 1-2h-1l1-1 2 3 1-1 7 6c4 2 7 4 11 6v1s1 0 1 1c0 0 0 1 1 1h0c-1 1-2 2-3 2l-5-1c-5-1-9-3-14-6h0c0-1 0-2 1-3h0v-1c0-2-1-2-2-3z"></path><path d="M91 307l2-1c1 0 2 0 3-1 0-1 1 0 2 0 0 0 0 1 1 1h0c-1 1-2 2-3 2l-5-1z" class="O"></path><defs><linearGradient id="Cg" x1="83.33" y1="280.094" x2="90.574" y2="303.179" xlink:href="#B"><stop offset="0" stop-color="#6d6c6d"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#Cg)" d="M79 283l2-2 1 1 2-1c3 6 8 11 13 15l3 2v4h-1c-1 0-2 0-3-1-6-3-13-8-19-13v-1-1-1l2-2z"></path><path d="M97 296l3 2v4h-1c-1 0-2 0-3-1h1 0 2c-2-1-2-3-2-5z" class="O"></path><defs><linearGradient id="Ch" x1="114.127" y1="289.528" x2="125.753" y2="284.419" xlink:href="#B"><stop offset="0" stop-color="#4c4b4c"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#Ch)" d="M118 262h0c0 1 0 2-1 3 2 2 1 5 0 7h1c0-1 0-2 1-3h1c0 1 0 2-1 2v1c0 1 0 1-1 2v3c-1 4-1 10 0 14l1 4h2c2 7 7 14 13 17h-3c0-2-3-3-4-4-7-4-11-11-13-19s-1-20 4-27z"></path><defs><linearGradient id="Ci" x1="106.41" y1="233.132" x2="94.313" y2="221.748" xlink:href="#B"><stop offset="0" stop-color="#474748"></stop><stop offset="1" stop-color="#8d8d8d"></stop></linearGradient></defs><path fill="url(#Ci)" d="M88 228l1-1c6-6 13-9 21-9h1c1 1 2 1 3 2 0 1 0 3-1 4l1 1h-1v2 4l-2-1c-5-3-11-4-17-2-1 0-2 1-4 1l-1 1h-1 0l-1 1c0-1 0-2 1-3z"></path><path d="M111 218c1 1 2 1 3 2 0 1 0 3-1 4l1 1h-1v2 4l-2-1c1-1 1-2 0-2v-1c1-1 0-2 1-3h-1c-1-1 0-4 0-6z" class="L"></path><defs><linearGradient id="Cj" x1="427.413" y1="103.444" x2="436.073" y2="116.619" xlink:href="#B"><stop offset="0" stop-color="#656565"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#Cj)" d="M424 108c7 0 14-2 21-4 1 1 3 3 2 5-1 3-4 4-7 5-4 2-9 1-13 1 0-1 1-2 1-3 3 0 6 0 8-2-4 1-9 1-14 1h0c-2 0-3 0-4-1-1 0-2 0-2-1v-1h8z"></path><path d="M416 108h8-1c-1 1-2 1-4 1h0c1 1 2 1 3 2-2 0-3 0-4-1-1 0-2 0-2-1v-1z" class="O"></path><defs><linearGradient id="Ck" x1="119.869" y1="325.369" x2="96.021" y2="321.27" xlink:href="#B"><stop offset="0" stop-color="#575658"></stop><stop offset="1" stop-color="#939292"></stop></linearGradient></defs><path fill="url(#Ck)" d="M93 327c4-5 8-9 14-11 7-1 16-2 22 0-7 0-17 1-22 7-2 3-3 7-3 10v2h-1c-2-1-4-2-5-4l-3-5-1 1h-1z"></path><defs><linearGradient id="Cl" x1="452.604" y1="144.621" x2="448.619" y2="150.673" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#Cl)" d="M439 144l-4-4c1-1 1-1 2-1 0 0 1 1 2 1h1c3 1 5 3 8 4 9 5 18 8 26 14h-1 0c-1-1-1-1-2-1v-1c-1-1-3-1-4-2 0 1 0 1-1 2 0 1-1 2-1 2h-1 0c-1 0-1 0-2-1h0c-1 0-1-1-2-1h-3c-6-3-12-7-18-12z"></path><defs><linearGradient id="Cm" x1="478.6" y1="283" x2="498.814" y2="300.28" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#7a7a7a"></stop></linearGradient></defs><path fill="url(#Cm)" d="M471 282h0c-3-3-8-3-11-5 3 0 7 1 10 2 1 1 2 2 3 2h0l-3-3v-1c5 3 8 6 12 9 0-1 0-1-1-1v-1c0-1-1-1-1-2 1 0 2 0 3 1 1-1 1-1 2-1 1 1 2 3 4 3 4 4 5 8 7 13 0 3 0 6 1 9h-1c0-1 0-2-1-3h-1c-3-3-4-8-7-11-3-2-5-4-8-6-2-1-3-3-5-3-1 0-2-1-3-2z"></path><path d="M471 282h0c-3-3-8-3-11-5 3 0 7 1 10 2 1 1 2 2 3 2h0c2 1 4 3 5 4 2 2 5 3 7 5 4 4 7 9 10 14h-1c-3-3-4-8-7-11-3-2-5-4-8-6-2-1-3-3-5-3-1 0-2-1-3-2z" class="D"></path><path d="M185 561l1-3h0c1-1 1-3 2-4 0-2 1-4 2-6l-1 4h1c-1 3-2 5-2 8 1 1 1 2 1 3v1c0 2 1 3 2 5l2 1v-1-2-8-1c1 1 1 2 1 2 0 2 1 3 1 4h0v1h1 0l-1-1h1 0c0 3 1 8 3 10l1 1h0-1c-1 2 1 4 0 6l2 4c0 1 0 1-1 2 1 2 2 4 4 6l2 4h0c-1-1-6-7-6-8s0-1-1-2c-3-5-7-9-10-15-2-3-3-7-4-11z" class="P"></path><path d="M200 587c-2-3-3-7-4-10-1-4-3-8-2-12 1 6 3 10 5 16l2 4c0 1 0 1-1 2z" class="T"></path><defs><linearGradient id="Cn" x1="194.306" y1="573.452" x2="198.663" y2="568.32" xlink:href="#B"><stop offset="0" stop-color="#3c3b3e"></stop><stop offset="1" stop-color="#595a57"></stop></linearGradient></defs><path fill="url(#Cn)" d="M194 560c0 2 1 3 1 4h0v1h1 0l-1-1h1 0c0 3 1 8 3 10l1 1h0-1c-1 2 1 4 0 6-2-6-4-10-5-16v-5z"></path><defs><linearGradient id="Co" x1="379.504" y1="117.592" x2="383.48" y2="113.655" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#Co)" d="M360 99c1-1 1-1 2-1 1 1 3 1 4 2h2c7 4 14 11 19 17 4 4 8 9 11 14 1 2 1 4 2 6l4 7c-3-1-4-6-6-8l-12-14c-8-9-16-18-26-23z"></path><path d="M488 216l8 2-1 1c3 1 7 1 10 3 2 2 5 4 7 6v1c-3-2-7-2-10-3-4-1-7-2-10-3-6 0-12 0-18 2-2 1-3 1-4 2-2 0-3 1-4 1h0c2-2 7-4 10-5h0c-2-1-5 0-7 1 4-3 8-4 12-6h0c2-1 5-1 7-2z" class="J"></path><path d="M488 216l8 2-1 1c-4-2-10-1-14-1h0c2-1 5-1 7-2z" class="D"></path><defs><linearGradient id="Cp" x1="133.098" y1="353.866" x2="142.132" y2="353.216" xlink:href="#B"><stop offset="0" stop-color="#111112"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#Cp)" d="M146 340c-1 1-1 2-2 3-2 2-2 6-1 9l1 1c0 1 1 2 1 3l1 1c-1 1-1 2-2 2-1 1-1 2-2 3l-1 1h-2v2l-1 1-2-3h0s-1 0-1-1c-1-1-2-1-2-2 0-4-1-9 1-12 2-4 4-5 8-7 1 0 3-1 4-1z"></path><path d="M139 363c-1-1-1-2-1-3h0 0v-1c1 1 2 3 3 4h0-2z" class="E"></path><path d="M146 340c-1 1-1 2-2 3-2 2-2 6-1 9l1 1c0 1 1 2 1 3l1 1c-1 1-1 2-2 2-1 1-1 2-2 3l-1-1c1-2 0-4 0-6v-1c-1-2-1-8 1-10h0v-3c1 0 3-1 4-1z" class="Q"></path><path d="M143 352l1 1c0 1 1 2 1 3l1 1c-1 1-1 2-2 2h-1v-7z" class="N"></path><defs><linearGradient id="Cq" x1="84.974" y1="320.215" x2="80.95" y2="312.641" xlink:href="#B"><stop offset="0" stop-color="#696869"></stop><stop offset="1" stop-color="#8d8d8d"></stop></linearGradient></defs><path fill="url(#Cq)" d="M66 322v-1c6-6 16-8 24-9h10c-1 1-1 1-2 1h0c-6 2-12 5-18 8l-6 4h-1 0c-1 1-1 2-2 2l-3-3v2c-2 0-4 1-5 1v-1c1-2 2-3 3-4z"></path><path d="M63 326c1-2 2-3 3-4 2 1 3 0 5 2h-1-2 0v2c-2 0-4 1-5 1v-1z" class="P"></path><path d="M424 133h0l7 7c2 2 4 4 5 6 1 1 1 2 2 3 2 1 3 3 5 5 1 2 3 4 4 6 3 3 12 11 12 15l-1-2-1 1v1h-1c-1 0-1 1-1 1-3-3-6-6-8-9v1c-1-1-1-3-1-4l-3-3h-1 0l-1-1h-1c-1 0-2-5-3-7l-3-6-2-2h-1c-1-2-3-4-4-7-1-2-3-3-3-5z" class="D"></path><path d="M438 149c2 1 3 3 5 5 1 2 3 4 4 6 3 3 12 11 12 15l-1-2c-1-1-2-2-2-3l-7-6c-4-5-8-10-11-15z" class="K"></path><path d="M434 147h1c0 1 1 3 2 4l7 9 3 3v2 2 1c-1-1-1-3-1-4l-3-3h-1 0l-1-1h-1c-1 0-2-5-3-7l-3-6z" class="E"></path><defs><linearGradient id="Cr" x1="147.787" y1="261.24" x2="125.146" y2="264.092" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#Cr)" d="M134 259h1c3-2 7-3 10-4 1 1 1 3 2 3h4c-2 0-4 0-6 1h6l-3 1h0c0 1-1 3-2 4h-1l1 1h-1l-2 1c-4 1-8 2-12 4-3 1-5 2-8 3 0-1 0-1 1-2v-1c1-1 1-2 1-4l3-3v-1h0 0c2 0 3-2 6-3z"></path><path d="M128 262h0 0l2 1c0 3-3 6-5 7h-1c1-1 1-2 1-4l3-3v-1z" class="M"></path><defs><linearGradient id="Cs" x1="447.404" y1="119.033" x2="435.959" y2="133.229" xlink:href="#B"><stop offset="0" stop-color="#514f4f"></stop><stop offset="1" stop-color="#808081"></stop></linearGradient></defs><path fill="url(#Cs)" d="M417 119c3 1 5 2 7 2l23 3c1 0 3 1 4 1 1 1 2 2 3 2h2 0c1 0 3 1 3 2 1 1 0 1 0 2h-5c-2 1-3 0-5 0-8 0-15-3-23-6-4-1-8-4-11-6h2z"></path><path d="M417 119c3 1 5 2 7 2l23 3c-2 1-6 0-9 0-4-1-9-1-13-1h-2l2 1 1 1c-4-1-8-4-11-6h2z" class="G"></path><path d="M80 321l1 1c-4 2-8 5-10 8h0c-1 2-3 3-3 5 1-1 1-1 2-1l-1 1 1 1-1 1h0l-2 2v1h0c-1 1-2 2-3 2h0c-3 3-6 6-9 8h0c1-3 0-8 2-11 1-3 1-6 3-8l1-2c1-1 1-2 2-3v1c1 0 3-1 5-1v-2l3 3c1 0 1-1 2-2h0 1l6-4z" class="K"></path><path d="M57 339c1-3 1-6 3-8h0 1 1 0l1-1 1 1c0 1 0 1-1 2h0-1c-2 1-2 4-5 6z" class="D"></path><path d="M65 339h-1 0c1-3 4-7 7-9h0c-1 2-3 3-3 5 1-1 1-1 2-1l-1 1 1 1-1 1h0l-2 2v1h0c-1 1-2 2-3 2l2-2v-1h0-1z" class="B"></path><path d="M69 335l1 1-1 1h-2l2-2z" class="C"></path><path d="M67 337h2 0l-2 2v1h0c-1 1-2 2-3 2l2-2v-1h0-1l2-2z" class="F"></path><defs><linearGradient id="Ct" x1="59.775" y1="333.219" x2="70.795" y2="327.79" xlink:href="#B"><stop offset="0" stop-color="#252424"></stop><stop offset="1" stop-color="#595758"></stop></linearGradient></defs><path fill="url(#Ct)" d="M68 324l3 3c1 0 1-1 2-2h0 1c-4 4-8 8-11 12-1 2-3 4-5 6l5-10h0c1-1 1-1 1-2l-1-1-1 1h0-1-1 0l1-2c1-1 1-2 2-3v1c1 0 3-1 5-1v-2z"></path><path d="M63 326v1c0 1 0 1 1 2-1 0-1 0-2 1h-1v-1c1-1 1-2 2-3z" class="L"></path><defs><linearGradient id="Cu" x1="142.264" y1="564.541" x2="132.373" y2="556.173" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#Cu)" d="M132 572c-1-2-1-4-2-6-1-3 0-7 2-10 1-2 4-4 7-4 1 0 4-1 5 0 0 1 1 1 0 2v1h0 0 0c1 1 1 2 1 3 0 4-2 6-4 9 0 1-1 2-1 3h-1c-1-2-1-4 0-6l-1-1c0 1 0 1-1 1-1 2-1 3-1 5v3h0c-1-1-2 0-4 0z"></path><defs><linearGradient id="Cv" x1="165.664" y1="467.678" x2="164.503" y2="458.728" xlink:href="#B"><stop offset="0" stop-color="#616162"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#Cv)" d="M168 459c1 0 2-1 2 0 1 0 1-1 1-1 1 0 2-1 3-1 0-1 0-1 1-2v1h1c4 2 5 5 7 9v2l-11-2-2-1-2-1-7 6c-3-2-5-3-9-5h-5c2-1 4-3 6-3l2-1h2c3-1 5-2 8-1h2 1z"></path><path d="M168 459c1 0 2-1 2 0 1 0 1-1 1-1 1 0 2-1 3-1 0-1 0-1 1-2v1h1l-1 2-2 1h-1c-1 1-3 0-3 0h-1z" class="S"></path><defs><linearGradient id="Cw" x1="161.765" y1="180.721" x2="119.828" y2="189.106" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#Cw)" d="M142 161c7 1 13 5 17 10 3 6 5 13 3 19v2h0c-2 6-5 11-10 14-5 2-12 3-18 1-5-2-10-5-13-10l-1-4h1c1 0 1 0 2-1 1 4 3 7 7 10s10 5 16 4c4-1 8-4 11-8 3-5 4-14 3-20-1-4-4-8-7-10h-1c-2-2-5-3-8-4h1l-1-1s-1 0-1-1c0 0 0-1-1-1z"></path><defs><linearGradient id="Cx" x1="444.359" y1="128.446" x2="439.804" y2="114.708" xlink:href="#B"><stop offset="0" stop-color="#5f5f60"></stop><stop offset="1" stop-color="#868586"></stop></linearGradient></defs><path fill="url(#Cx)" d="M448 113h1 1c2 1 4 2 5 4l-1 1h0l-4 1c-1 0-1 1-2 1h8 1 1 1s1 1 1 2l-1 1h-1c-1 1-1 2-2 2h-2-3c-1 0-3-1-4-1l-23-3v-1c2 0 2 0 3-1 1 0 3 0 4-1 6 0 10-2 15-5h2z"></path><path d="M448 113h1 1c2 1 4 2 5 4l-1 1v-1c0-1 0-1-1-1-2 0-5-2-7-3h2z" class="M"></path><defs><linearGradient id="Cy" x1="122.794" y1="418.984" x2="141.113" y2="426.723" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#949494"></stop></linearGradient></defs><path fill="url(#Cy)" d="M126 413c1 0 1-1 1-1v-1l2 2v-1l1 1h1 0c2 2 3 6 5 9l3-6c1 0 2 1 3 1 1-1 2 0 3 0v1c-1 1-1 2-1 2-2 4-4 8-5 12-1 1-1 3-1 4l-4 1c-1-2-1-7-3-9h0l-4-8c0-1 0-3-1-4 0-1-1-1-1-1 0-1 0-1 1-2z"></path><defs><linearGradient id="Cz" x1="67.856" y1="270.297" x2="76.899" y2="282.982" xlink:href="#B"><stop offset="0" stop-color="#0c0b0b"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#Cz)" d="M73 272c-1-1-1-2-1-3h0c1 0 1 1 1 2l2 2c1 0 1 0 2-1l1-1c2 2 3 5 4 7l1 1 1 2-2 1-1-1-2 2-2 2v1 1 1 1l2 2-1 1-2-3-1 1h1c-1 1-1 1-1 2l1 1h0v-1 2h0l-1-1c-1 0-2-2-3-2l-4-7h0c-1-3-2-5-2-8-1-2-1-3-1-5h1l3 6v-5-1-1h1 0c1 1 1 2 1 3 1 0 1 0 2-1z"></path><path d="M73 272h0c0 2 0 2-1 4 0-1-1-1-1-2v-1c1 0 1 0 2-1z" class="E"></path><path d="M75 287v-1-1h0c0-1-1-1-1-2l2-2c1-1 4-3 5-3h1l1 1 1 2-2 1-1-1-2 2-2 2v1 1 1 1l-2-2z" class="H"></path><path d="M83 279l1 2-2 1-1-1-2 2c-1-1 0-1 0-2 1-1 2-1 4-2z" class="P"></path><defs><linearGradient id="DA" x1="70.286" y1="286.308" x2="75.742" y2="289.394" xlink:href="#B"><stop offset="0" stop-color="#434343"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#DA)" d="M68 284h0c1 1 2 1 2 2h0 1c0-1 0-1 1-2l3 3 2 2 2 2-1 1-2-3-1 1h1c-1 1-1 1-1 2l1 1h0v-1 2h0l-1-1c-1 0-2-2-3-2l-4-7z"></path><path d="M186 595c-1-1-2-3-2-4l-1-1v1c-1 0 0 0-1 1 0-2 0-4 1-5 1 0 2 0 3 1h0 1 2c6 1 10 5 14 9 0 1 0 2-1 2 0 1-1 2-1 2v1c1 0 1-1 2-1l1-1v1c-1 2-1 2-1 4v1c-1 0-3-1-4-1h0-2l-11-5h-3l-1-1c2-1 2-2 4-2l5 2c-2-2-4-3-5-4z" class="S"></path><defs><linearGradient id="DB" x1="442.204" y1="212.965" x2="451.674" y2="215.163" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#DB)" d="M449 199h0c1-3 1-5 1-7 1 1 0 2 1 3 0 1-1 10 0 10v-2l1-2h0v5 9c0 1 0 3-1 5 0 3-2 7-3 9s-2 3-2 5l-1 3c-1-1-2-1-3-1s-2-1-2-1l-3-1v1c-1 1-1 1-1 2h0 0c1-4 3-7 4-10 3-5 5-9 7-14 1-5 2-10 2-14z"></path><path d="M175 190c1 1 2 3 3 3h1c0-1-4-6-5-7s-1-3-1-4c-1-8 2-12 6-18h0c-3 4-4 9-4 15 1 1 1 2 2 3 2 1 5 2 6 3s2 3 2 4c1 4 0 8-2 11-2 2-4 4-6 4h-5c1-4 1-7 1-11 0-1 0-2 2-3z" class="V"></path><defs><linearGradient id="DC" x1="469.87" y1="193.866" x2="461.53" y2="173.806" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#DC)" d="M459 175h0l1 2c1-1 0-1 0-2h1v-2l1-1c0 1 0 1 1 1h1l2 1h4c2 4 3 7 3 11-1 4-2 7-4 10-3-1-3 1-5 2l-1-1c0-2 0-3-1-4v-3c-2-5-4-9-7-13 0 0 0-1 1-1h1v-1l1-1 1 2z"></path><path d="M459 175h0l1 2c1-1 0-1 0-2h1v-2l1-1c0 1 0 1 1 1h1l2 1h0-3-1v2c0 1 0 4 1 5v1 1c0 1 1 1 1 2h1 0v-1h1c1 1 0 1 0 2-1 1-3 2-4 3-2-5-4-9-7-13 0 0 0-1 1-1h1v-1l1-1 1 2z" class="H"></path><defs><linearGradient id="DD" x1="106.363" y1="483.783" x2="144.147" y2="503.656" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#DD)" d="M142 513l-18-13c-7-6-11-12-14-20 0-2-1-4-1-5 8 6 13 14 20 21l15 15c-1-1-2-1-3-1v1l1 2z"></path><defs><linearGradient id="DE" x1="112.194" y1="550.577" x2="132.249" y2="535.469" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient></defs><path fill="url(#DE)" d="M108 547c4-5 9-8 15-10 4-2 8-4 12-5l-1 1s-1 1-2 1c0 2-1 4-1 5l1 1c-6 4-12 11-15 18l-1-2c-1-2-3-3-5-4-1-1-3-3-3-5z"></path><defs><linearGradient id="DF" x1="152.402" y1="543.524" x2="150.475" y2="529.663" xlink:href="#B"><stop offset="0" stop-color="#6d6d6d"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#DF)" d="M135 532c9-3 22-4 32 1 1 1 3 2 5 4 1 1 2 3 3 5h-1l-1-1c-1 1-3 2-5 2l-1-1c-2-3-6-5-10-6-9-2-17 0-25 4l-1-1c0-1 1-3 1-5 1 0 2-1 2-1l1-1z"></path><path d="M172 537c1 1 2 3 3 5h-1l-1-1c-1 1-3 2-5 2l-1-1c2 0 4-1 6-2-1-1-1-2-1-3zm328-266c0-2 1-4 3-6 1 0 1-1 2-1h-1c-2 2-2 6-3 8 1-1 2-2 3-4 2-3 6-6 11-7h0-1c-4 2-8 5-9 9-2 2-2 4-2 7h0v-1c1 0 1 0 2-1h1c-1 2-1 3-1 5v1c-3 7-4 14-5 21 0 1 0 2-1 3h0v-6h0v7c0 2 0 4-1 6h0c0-2 0-3-1-5-1-3-1-6-1-9v-2h0l-2-8-1-2h0v-5-1c1-2 3-5 5-6 0 0 1-2 1-3v-1h0l1 1z" class="M"></path><path d="M499 270h0l1 1-2 9c0 6 0 13 1 19v7c0 2 0 4-1 6h0c0-2 0-3-1-5-1-3-1-6-1-9v-2 1h1v-16l1-7s1-2 1-3v-1z" class="D"></path><path d="M493 281v-1c1-2 3-5 5-6l-1 7v16h-1v-1h0l-2-8-1-2h0v-5z" class="U"></path><path d="M493 281v-1c1-2 3-5 5-6l-1 7v-5 1c0 1-1 2-2 4-1 0-2 1-2 2 0 2 1 3 1 5l-1-2h0v-5z" class="R"></path><defs><linearGradient id="DG" x1="439.631" y1="178.944" x2="415.384" y2="135.488" xlink:href="#B"><stop offset="0" stop-color="#1b1c1c"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#DG)" d="M415 124l1 1 4 6c2 0 3 1 4 2h0c0 2 2 3 3 5h-2c2 3 3 6 5 9 4 9 8 17 7 27 0 8-5 14-10 19h-1c4-4 7-8 8-13 3-10 0-18-4-27l-6-12 5 13h0c-1-1-1-1-1-2s0 0-1-1v-1c0-1 0-1-1-1h-1-1 0c-1 1-1 1-2 1v1c1 1 1 1 1 2v2h0c0 1 1 3 0 4v-1-2c-1-1-1-2-1-2v-1c-1 1-2 1-3 1 0 1 0 0 1 1 0 1-1 3 0 4v1 5c-1-9-2-17-5-26v-1h0l6-5-6-9z"></path><path d="M420 131c2 0 3 1 4 2h0c0 2 2 3 3 5h-2s-1-1-1-2l-4-5z" class="C"></path><defs><linearGradient id="DH" x1="156.338" y1="134.87" x2="178.516" y2="145.547" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#9f9f9f"></stop></linearGradient></defs><path fill="url(#DH)" d="M166 133h13c2 0 2 0 3 1s2 1 3 1l1 1-6 3c-7 3-14 6-22 7l-5 1c-1-1-1-3-1-4 1-1 1-2 1-3l1-1h2c3 1 7 1 10 0l-8-1c-1-1-1-2-1-3v-1c1-1 2-1 4-1h5z"></path><path d="M154 139h2c-1 1-1 2-1 4h0v1 1h0l1 1h2l-5 1c-1-1-1-3-1-4 1-1 1-2 1-3l1-1z" class="O"></path><path d="M166 133h13c2 0 2 0 3 1s2 1 3 1l1 1-6 3 1-1c1 0 1-1 2-1h-3c-2-1-3-3-5-4h-2c-1 0-6 1-7 0z" class="R"></path><defs><linearGradient id="DI" x1="188.139" y1="552.977" x2="203.556" y2="552.438" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#DI)" d="M198 536h1v1h0c1 1 1 1 1 2 1 3 1 6 2 9l3-3c-1 6-3 12-4 19l-1 1c-1 0-1 1-1 1v1h1v2l-2 1-1-1v-2-3h-1 0-1l1 1h0-1v-1h0c0-1-1-2-1-4 0 0 0-1-1-2v1 8 2 1l-2-1c-1-2-2-3-2-5v-1c0-1 0-2-1-3 0-3 1-5 2-8h-1c1-1 1-3 2-4 1-2 2-3 3-5 1 1 1 1 0 2l-1 2v1c1-1 2-2 2-3h1c0-1 1-1 1-2s0-2 1-2v-2-1h1l-1-2z"></path><defs><linearGradient id="DJ" x1="141.781" y1="433.977" x2="151.199" y2="443.848" xlink:href="#B"><stop offset="0" stop-color="#5f5f5f"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#DJ)" d="M140 445c0-2 2-4 3-7 2-5 4-9 7-14l-1 9 3-3s1 0 2-1h0c1-1 1-1 2 0h2c1 0 1 1 2 1l2 1h2v2h-1c-1-1-1-1-2-1-1 1-1 3-2 4h-1v-1l-2 3c-1 1-2 3-3 4s-1 2-2 2c-2 3-5 7-8 10-1 1-1 2-2 3l-1-1 3-3-1-1c-1 1-1 2-2 2h-1c0-2-1-2-1-4l2-5z"></path><path d="M151 444v-2h1v-1l1 1c-1 1-1 2-2 2z" class="S"></path><path d="M154 431c1 1 3 2 4 3v1l-2 3c0-1 0-2-1-2l-1-1c0-1 0-1-1-2 0-1 0-1 1-2z" class="M"></path><path d="M156 429h2c1 0 1 1 2 1l2 1h2v2h-1c-1-1-1-1-2-1-1 1-1 3-2 4h-1v-1-1c-1-1-3-2-4-3v-1h1s0-1 1-1z" class="Q"></path><path d="M140 445v4c0 1 0 2 1 3 0-1 0-2 1-3l4-4c-1 1-1 3-1 4 0 2-3 4-2 5-1 1-1 2-2 3l-1-1 3-3-1-1c-1 1-1 2-2 2h-1c0-2-1-2-1-4l2-5z" class="N"></path><defs><linearGradient id="DK" x1="149.789" y1="158.638" x2="98.093" y2="184.465" xlink:href="#B"><stop offset="0" stop-color="#575758"></stop><stop offset="1" stop-color="#999897"></stop></linearGradient></defs><path fill="url(#DK)" d="M102 190h0c0-4 3-8 4-11 4-7 8-14 14-19 8-6 16-7 26-6 0 1 1 1 1 1 1 1 1 1 2 1l-1 1h-1-2c-9-1-17 1-24 6-5 4-8 10-9 16-1 5 0 11 1 15v1h-1c-1 0-7-2-8-1-1 0-1 1-2 1v-5z"></path><defs><linearGradient id="DL" x1="163.261" y1="386.51" x2="153.92" y2="372.185" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#DL)" d="M162 367h4l2 1c2 6 1 12-3 17-2 3-4 4-7 7l-2-1c-3-1-5-3-7-5l1-3c0-1 1-2 2-3 2 1 4 1 6 1v-1h-3c0-1-1-2-1-2v-2h1l1-5c2-2 3-3 6-4z"></path><path d="M162 367h4c1 3 1 7 1 10l-1-2h-1c-1-1 0-2 0-3h0c-1-1 0-1 0-2-1 0-1-1-1-1v-1l-2-1z" class="S"></path><path d="M166 367l2 1c2 6 1 12-3 17-2 3-4 4-7 7l-2-1 3-2h0c1 0 1 0 1-1h1c0-1 1-1 1-2h1 1c1-1 1-1 1-2h-1l2-5h0c0-1 0-1 1-2 0-3 0-7-1-10z" class="M"></path><defs><linearGradient id="DM" x1="250.245" y1="600.903" x2="235.667" y2="605.422" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#DM)" d="M242 617c-5-6-7-11-8-19 0-2-1-4 0-6l6 2c1 0 2 1 3 0v-2l1-1c3 3 5 8 7 12 1 2 2 5 4 7l-2-1h0v1c-1 1-1 2-1 2-1 1-1 1-2 1-1-1-3-5-5-5v1l1 2c0 2-1 4-3 5 0 1-1 1-1 1z"></path><defs><linearGradient id="DN" x1="391.971" y1="127.974" x2="400.175" y2="118.954" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#6b6b6b"></stop></linearGradient></defs><path fill="url(#DN)" d="M374 103h3 0c1 0 2 1 3 1 9 4 18 11 24 18 2 4 4 7 6 11 1 2 3 5 4 8l1 6c0 1-1 1-2 1-2-8-5-16-11-21l4 19c-1-2-1-4-2-6-4-8-8-16-14-23-5-5-10-9-16-14z"></path><defs><linearGradient id="DO" x1="104.831" y1="241.666" x2="111.586" y2="248.082" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#DO)" d="M101 243l1 1v-1h1 1s1-1 2-1v-1c1 0 2 0 2-1h1 1l1-1h4s0 1 1 1v1c2 0 3-2 4-2s5 2 5 3h1c-1 3-3 6-4 9l-5 9h-3c-2-1-5-2-6-4v-2c-1-1-3-2-4-3s-1-2-2-3h0c-1-1-1-3-1-5z"></path><defs><linearGradient id="DP" x1="108.326" y1="253.94" x2="122.96" y2="250.865" xlink:href="#B"><stop offset="0" stop-color="#616060"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#DP)" d="M108 256c1-1 1-2 1-3 1-2 2-5 4-7 1-1 1-1 2-1h0l1 1h2v1c2 1 3 0 4 1h0v3l-5 9h-3c-2-1-5-2-6-4z"></path><defs><linearGradient id="DQ" x1="447.919" y1="211.826" x2="457.148" y2="212.498" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#DQ)" d="M452 179c6 15 8 33 4 49-1 6-3 11-5 16 0 1-1 2-2 4v-1h0c1-1 1-2 1-2 0-1 0-1-1-2 0 0 0-1-1-1 0-1 0-2-1-2 0-1 0-1-1-2 0 1-1 1-1 2h-1 0 0c0 1-1 1-1 1 0-1 1-3 2-4l1-3c0-2 1-3 2-5s3-6 3-9c1-2 1-4 1-5v-9l1-3v1c0-8-1-15-2-23 0-1 1-2 1-2z"></path><path d="M453 203v1c0 3 1 8-1 11v-9l1-3z" class="B"></path><defs><linearGradient id="DR" x1="394.324" y1="119.416" x2="401.494" y2="110.848" xlink:href="#B"><stop offset="0" stop-color="#4d4d4e"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#DR)" d="M379 102v-1c-1-1-3-2-4-2v-1c1 0 2-1 3-1s2 1 3 1c3 1 5 3 8 3h0l3 2c2 1 4 3 7 4l6 6 3 3h1c2 3 4 5 6 8l6 9-6 5h0v1c-4-11-11-19-20-26-2-2-4-4-6-5-3-3-7-4-10-6z"></path><defs><linearGradient id="DS" x1="206.894" y1="168.931" x2="202.664" y2="128.283" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#DS)" d="M218 126h2c-1 2-4 4-5 5-3 2-6 5-7 8 0 2-2 3-2 5h0c-1 2-1 3 0 4l-2 3v1h0c-1 1-1 2-1 3v1h-1c0 1 1 1 1 2 0 2-1 5-1 8 0 1-1 1-1 2v2h-2c-2-1-3 0-5-2-1-1-3-2-3-4h-1l2-5c0-1 0-1-1-2 0-1 1-2 1-3h0l2-1h0c0 2-1 4-1 5v1c1-1 2-4 3-6v-1-1c-1-1-2-1-2-2 1-2 2-3 3-5 7-7 13-13 21-18z"></path><path d="M199 147c1 2-1 5-2 7s-3 6-3 8h0s-1-1 0-2v-1h0c-1 1-1 1-1 2l-1-1-1 4h-1l2-5c0-1 0-1-1-2 0-1 1-2 1-3h0l2-1h0c0 2-1 4-1 5v1c1-1 2-4 3-6l3-6z" class="H"></path><path d="M218 126h2c-1 2-4 4-5 5h0c1-2 2-2 3-3 0-1 0-1 1-1-2 0-3 2-5 3-3 2-6 4-9 7 0 2 0 3-1 4-1 2-4 4-5 6l-3 6v-1-1c-1-1-2-1-2-2 1-2 2-3 3-5 7-7 13-13 21-18z" class="V"></path><path d="M194 149c1-2 2-3 3-5 1 2 0 5-1 6v1c-1-1-2-1-2-2z" class="M"></path><defs><linearGradient id="DT" x1="110.136" y1="282.73" x2="94.897" y2="267.497" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#DT)" d="M97 263l2 3 2-3c1-1 2-3 3-3 2 0 3 2 4 3l1 2-3 12v2c0 2 0 5-1 7v4 1l-4-7c-2-3-3-7-5-11 0 4 1 7 3 11 0 1 0 3-1 4-4-5-7-9-10-15 0 0 1 0 1-1 3-2 5-6 8-9z"></path><path d="M101 284c2 1 2 3 3 5 0-1 1-2 0-3h0v-4c0-2 1-2 2-3 0 2 0 5-1 7v4 1l-4-7z" class="R"></path><defs><linearGradient id="DU" x1="113.972" y1="395.557" x2="112.185" y2="370.262" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#DU)" d="M112 363c3 0 6 1 8 4s2 5 2 8l-1 2v1c-1 1-2 3-3 4h0-1 0c1 1 0 2 0 3s0 2 1 3h0a34.47 34.47 0 0 1-12 12l-1-1c-1-3-4-9-4-11 3-2 8-1 12-2 1 0 2 0 2-1 0-2-4-3-6-5-2-3-2-6-2-10 1-2 2-3 3-5l2-2z"></path><path d="M110 365h3 0l1 1c1 0 1 0 2 1h1 0c0 2 1 3 1 4 1 1 0 3 0 4-1 1-1 2-3 2s-5-2-7-4c0-1 0-2-1-3 1-2 2-3 3-5z" class="V"></path><defs><linearGradient id="DV" x1="200.986" y1="105.435" x2="198.108" y2="120.801" xlink:href="#B"><stop offset="0" stop-color="#686768"></stop><stop offset="1" stop-color="#979697"></stop></linearGradient></defs><path fill="url(#DV)" d="M179 112v-3c0-1 0-1-1-2v-2h0v-1h0c0-1 0-2 1-3 1 1 1 1 2 1h0l1 1c1 0 3 2 5 3 6 3 14 5 21 6h0 5c1 1 2 0 3 1h1c2-1 4-1 6 0h0c-1 1-2 1-3 1-2 1-3 2-4 3-9 2-19 5-28 3-2-1-3-2-5-3l-2-1-2-1v-3z"></path><path d="M182 113c1 1 1 2 1 2l1 1h1s0 1 1 1c0 1 1 2 2 3-2-1-3-2-5-3l-1-4z" class="M"></path><path d="M179 112h1c1 1 1 1 2 1l1 4-2-1-2-1v-3z" class="R"></path><path d="M179 112h1c1 2 1 3 1 4l-2-1v-3z" class="P"></path><defs><linearGradient id="DW" x1="484.477" y1="140.348" x2="451.065" y2="155.442" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#626263"></stop></linearGradient></defs><path fill="url(#DW)" d="M445 137c6 2 11 3 17 4l12 1c3 1 7 1 10 2 4 3 6 6 8 10 0 1 1 2 1 3l-1 1c-4 0-9-3-13-5 1 2 6 4 6 5-1 0-3-1-4-2-2-1-5-2-7-3-6-3-12-5-18-7-5-1-9-3-13-5 1-1 1-1 2-1 0 0 0-1 1-1l-1-1v-1z"></path><defs><linearGradient id="DX" x1="140.623" y1="352.294" x2="156.427" y2="357.398" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#909090"></stop></linearGradient></defs><path fill="url(#DX)" d="M146 340c2-2 4-3 7-5-1 4-1 8-1 11 1-1 2-3 3-5l1-2s2-1 3-1h2c1 0 1 1 1 2v2s-1 1-1 2c-2 2-3 5-3 8 0 1 0 1-1 2h0c0 3-4 6-4 8h1c-2 3-3 5-3 9 1 2 1 3 3 4h0l1 1h-1v2s1 1 1 2h3v1c-2 0-4 0-6-1h0c-3-1-4-3-5-5l-2-6 1-1v1-1c0-3 0-6 1-9 1-1 1-1 1-2 0 0 1-2 2-2 2-2 4-4 5-6-3 2-6 5-9 8l-1-1c0-1-1-2-1-3l-1-1c-1-3-1-7 1-9 1-1 1-2 2-3z"></path><path d="M151 371c1 2 1 3 3 4h0l1 1h-1v2s1 1 1 2c-2-1-5-3-6-5h1s0 1 1 1v-2h1c-1-1-1-2-1-3h0z" class="S"></path><path d="M145 369l1-1v1l3 6c1 2 4 4 6 5h3v1c-2 0-4 0-6-1h0c-3-1-4-3-5-5l-2-6z" class="J"></path><defs><linearGradient id="DY" x1="149.196" y1="496.61" x2="166.387" y2="501.483" xlink:href="#B"><stop offset="0" stop-color="#626262"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#DY)" d="M150 511c0-1 0-1-1-2 0-1 1-3 2-4 1-3 1-8 1-11 0-2-1-4 0-7 0-2 1-5 3-7 0-1 1-1 1-1 0-1 0-1 1-1 1-1 3-1 4-1 4 0 9 2 11 5-4 1-7 2-9 6-4 7-4 16-3 25 0 3 0 6 1 8 0 2 1 4 2 5-5-5-9-9-13-15z"></path><path d="M154 488h1v4 1c-1 1 0 2-1 4v1c-1-2 0-6 0-8v-2z" class="M"></path><path d="M213 577l1-5c1 0 3 1 4 2 1 0 2 0 3 1 0 0 1 0 2 1l3 21c-2 2-2 3-1 6v-1-2h0 0l1 1 1 3v1 1c1 0 0 1 1 1v1h-1-2 0c0-1-1-1-1-1v-1c-1 0-1 0-2-1l-4-8 2 7c0 2 0 2 1 4 1 1 2 3 3 5h-1c-2-2-3-5-5-7l-2-2h0c-4-5-7-11-9-17l-1-3c0-2 1-3 2-5 1-1 2-1 3-1l1 2 1 1v-1-3z" class="S"></path><path d="M211 578l1 2c-1 0-2 0-3 1-1 2-1 4-2 6l-1-3c0-2 1-3 2-5 1-1 2-1 3-1z" class="U"></path><defs><linearGradient id="DZ" x1="162.051" y1="298.896" x2="146.011" y2="291.358" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#DZ)" d="M148 287c1-1 2-3 2-4l3 2c3 0 4-5 6-6l2 1h1c1 1 2 1 3 1 0 1 1 1 1 1l3 1c0 1 0 0 1 0l-6 9h0 1 2v1h0v2h0l-2 2-2 1-2 2c-4 5-5 8-5 14h1l-1 1-1-1-1 1c1 1 1 2 1 3-3-3-6-7-7-11-3-6-3-14 0-20z"></path><path d="M163 298s0-1 1-1c0-1 0-2 1-2v2l-2 1z" class="H"></path><path d="M165 295l1-1c1 0 1 1 1 1l-2 2v-2z" class="L"></path><path d="M160 287h0c0 2-1 3-1 5h-1v-1c0-2 1-3 2-4z" class="U"></path><path d="M154 315c-2-5-1-13 0-18l2-5h1l-3 8c0 5 0 9 1 14l-1 1z" class="F"></path><defs><linearGradient id="Da" x1="159.073" y1="309.818" x2="154.793" y2="303.725" xlink:href="#B"><stop offset="0" stop-color="#2d2e2d"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#Da)" d="M154 300h1v3 1c1-1 1-1 1-2l2-2v2-1c1 0 2-1 3-1-4 5-5 8-5 14h1l-1 1-1-1c-1-5-1-9-1-14z"></path><defs><linearGradient id="Db" x1="70.852" y1="457.621" x2="101.129" y2="449.521" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#Db)" d="M102 459c-5 1-10 2-16 4 0 0-2 1-3 1h-1v-1h-2c-3 0-5 1-7 3h0c-1 0-1 0-1-1 1-6 5-9 10-12l-9 2h-1c-1 0-3 0-3 1h-1v-1c1 0 1 0 2-1h1s1-1 2-1c3-1 6-2 9-4-2 0-5 1-8 0v-1h0c2-1 4-2 6-2 7-1 13-2 20-1v2 2l-1 1h2c1 1 3 0 3 0l1 3 3 1c3 1 5 2 7 4 4 3 6 7 8 11l3 8-1 1h0c-1-3-2-6-4-8-3-7-7-12-14-14h0c-1 0-3-1-4-1l-1 3v1z"></path><path d="M107 456c-1-1-4-2-5-2-5 0-8 1-12 4l-3 3c1-2 3-4 5-6 4-3 8-4 13-2l3 1c3 1 5 2 7 4 4 3 6 7 8 11l3 8-1 1h0c-1-3-2-6-4-8-3-7-7-12-14-14z" class="J"></path><path d="M337 603c1-1 2-3 3-4 3-6 5-12 7-19 0 1 0 1-1 1v3h0v5 1c1 3 1 6 1 9l1 20c-2 0-5 0-7 1h-1c-2 8-4 15-12 19h-1c-1-1 1-4 2-6-1 0-2 1-2 1h-1l8-9-15 8h-3c-1 1-1 1-2 1h0c-1 1-2 0-3 1h-3-4 0 2c3-1 5-2 8-3 1 0 2 0 3-2h0 1c2-1 3-2 5-3 3-3 6-7 9-11l-1-1c1-3 2-6 4-9 0 1 0 1 1 1l1-4z" class="G"></path><path d="M341 598v1l-4 10h-1c0-1 1-1 1-2v-1h0c1-1 1-1 1-2v-1h0l1 2c0-1 0-2 1-3v-2s1-1 1-2z" class="I"></path><path d="M335 606c0 1 0 1 1 1-1 3-2 6-4 9l-1-1c1-3 2-6 4-9z" class="K"></path><path fill="#fff" d="M340 620c0-5 2-10 3-15 1-3 2-7 3-11l1 5 1 20c-2 0-5 0-7 1h-1z"></path><path d="M431 145h1l2 2 3 6c1 2 2 7 3 7 1 1 1 4 1 5 1 3 3 7 4 10s1 7 2 10 1 7 0 10h0c-1 8-4 16-9 22l-2 2c0 1-1 2-2 3 0-2 1-4 1-6 1 0 1-1 1-1 1-1 1-3 1-4v1l-1-1-1 1c0-1 0-2 1-2v-3h0c2-4 4-7 5-11 1-2 1-5 1-7 0-1 0-2-1-2 0 9-4 16-8 25l-3-3c-1-1-1-1-2-1v-1 2h0c0-6 4-11 7-16 2-5 3-11 4-17 0-8-2-15-4-22-1-3-2-6-4-9z" class="L"></path><path d="M441 187v-11c1 2 1 3 1 4h0c0 3 1 6 0 9 0-1 0-2-1-2z" class="D"></path><path d="M431 145h1l2 2 3 6c1 2 2 7 3 7 1 1 1 4 1 5 1 3 3 7 4 10s1 7 2 10 1 7 0 10h0v-1 1l-1-10v-1c-1-1 0-3-1-4 0-1 0-2-1-3l-3-10v-1-1-1h-1v-3c-1-1-1-2-1-3-1-1-2-2-2-4-1-1-2-3-3-5l3 9v1c1 2 1 5 2 7 1 3 1 7 0 10h0c0-8-2-15-4-22-1-3-2-6-4-9z" class="B"></path><defs><linearGradient id="Dc" x1="116.691" y1="516.553" x2="109.03" y2="537.201" xlink:href="#B"><stop offset="0" stop-color="#5c5c5c"></stop><stop offset="1" stop-color="#989898"></stop></linearGradient></defs><path fill="url(#Dc)" d="M106 517h2 0c1-1 1 0 2-1h8 3v1c1 0 2 0 2-1l25 7h-2l1 1h3v1c-4 0-8-1-12 0-4 0-8 0-11 2h0 4s1 0 1 1h-1v1h-2c-1 0-2 1-3 2-5 1-11 3-17 4-4 0-7 0-11-1l-5-2c0-3 0-7 1-10l5 1 9 1-2-4c-1 0-1-1-2-2l1-1h1z"></path><path d="M104 518l1-1h1l1 1h1l1 1c-1 1-2 1-3 1s-1-1-2-2z" class="R"></path><path d="M94 522l5 1h-2l-1 1h-1v2 1 2 1h1v1s0 1 1 1v1l1 1-5-2c0-3 0-7 1-10z" class="O"></path><path d="M125 526c7-1 14-3 21-3l1 1h3v1c-4 0-8-1-12 0-4 0-8 0-11 2l-13 1c1-1 9-2 11-2z" class="T"></path><defs><linearGradient id="Dd" x1="134.593" y1="517.131" x2="130.473" y2="524.273" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#505051"></stop></linearGradient></defs><path fill="url(#Dd)" d="M106 517h2 0c1-1 1 0 2-1h8 3v1c1 0 2 0 2-1l25 7h-2c-7 0-14 2-21 3 1-1 1-1 3-1 1 0 1-1 2-1-1 0-1-1-1-1l-1-1-1-1c-3-2-7-3-10-3h-1-1 0-8l-1-1z"></path><path d="M67 412v-1c3 1 9 3 12 1 0-1 1-1 1-2h0v-1c-3 0-6 0-8-2-1-2-2-4-1-5 0-3 1-5 3-6l1-1h0 5c1 0 2 1 3 2 3 2 7 6 9 10l3 9c-4 5-13 8-19 10 3 1 7 1 8 4h0c-1 1-4 1-5 1-2 0-3 0-5-1-3 0-6-2-8-5 0-3-1-6-1-9h1c1-1 1-2 1-4z" class="S"></path><path d="M67 412c1 1 1 2 1 3s1 3 1 4c0 3 0 8 3 10h1l1 1c-3 0-6-2-8-5 0-3-1-6-1-9h1c1-1 1-2 1-4z" class="P"></path><defs><linearGradient id="De" x1="455.79" y1="316.362" x2="480.168" y2="302.568" xlink:href="#B"><stop offset="0" stop-color="#161617"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#De)" d="M452 283c1 0 3 0 5 1 7 1 12 5 18 8 4 3 7 5 9 8 4 4 6 8 8 13 0 2 1 5 0 6l3 9c1 4 1 7 1 10-1 2-1 4-1 5l-1-9-1-3-3-9c-1-1-2-3-3-4-8-11-22-17-34-21-3-1-7-3-10-3-1 0-1 0-2-1h-5l1-1h0v-1-1c0 1 0 1 1 1l3-3 1-2 1 1c1 0 1 0 2-1 1-3 5-2 7-3z"></path><path d="M491 321c1 2 2 4 2 7 1 1 1 1 1 2l-1 1-3-9c0 1 1 1 1 2l1 4v-1-2h0v-1c-1-1-1-1-1-3h0z" class="G"></path><path d="M471 299c1 1 2 1 3 2 7 5 14 10 18 18l3 9c1 4 1 7 1 10-1 2-1 4-1 5l-1-9-1-3 1-1c0-1 0-1-1-2 0-3-1-5-2-7l-2-3c-3-8-11-13-18-18v-1z" class="D"></path><path d="M493 331l1-1v4h0l-1-3z" class="B"></path><defs><linearGradient id="Df" x1="478.132" y1="309.141" x2="486.267" y2="303.51" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#Df)" d="M474 301v-1c-1 0-2-1-2-1l-2-1h1 2c1 0 2 1 3 1 2 0 4 2 6 2l2-1c4 4 6 8 8 13 0 2 1 5 0 6-4-8-11-13-18-18z"></path><defs><linearGradient id="Dg" x1="461.033" y1="279.628" x2="471.906" y2="304.784" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#515152"></stop></linearGradient></defs><path fill="url(#Dg)" d="M452 283c1 0 3 0 5 1 7 1 12 5 18 8 4 3 7 5 9 8l-2 1c-2 0-4-2-6-2-1 0-2-1-3-1h-2-1l2 1s1 1 2 1v1c-1-1-2-1-3-2 0 0-1 0-1-1h-1c-1-1-2-1-3-1-4-1-8-3-12-5-2 0-6 0-7-1 0-1 0-1-1-1v1h-2c-1 1-1 2-3 2h-5l1-1h0v-1-1c0 1 0 1 1 1l3-3 1-2 1 1c1 0 1 0 2-1 1-3 5-2 7-3z"></path><path d="M445 286l5-1c-2 1-4 2-6 4h0c0 1 1 1 2 1v1h-2c-1 1-1 2-3 2h-5l1-1h0v-1-1c0 1 0 1 1 1l3-3 1-2 1 1c1 0 1 0 2-1z" class="S"></path><path d="M437 292c2 0 5-1 7-2h-1l1-1c0 1 1 1 2 1v1h-2c-1 1-1 2-3 2h-5l1-1z" class="Q"></path><path d="M444 289c2 0 2 0 4-1 1 0 3-2 3-2 2 0 6 1 8 2 3 1 7 3 9 5-3-1-6-3-9-4l-1 1c3 1 5 2 7 4h0l-2-1-1 1 4 3c-4-1-8-3-12-5-2 0-6 0-7-1 0-1 0-1-1-1s-2 0-2-1h0z" class="J"></path><path d="M456 292c-2-1-4-2-7-2h0-1l3-2c3 0 5 0 8 1l-1 1c3 1 5 2 7 4h0l-2-1-1 1c-1 0-5-2-6-2z" class="F"></path><path d="M456 292l1-1c2 0 4 1 6 2h0l-1 1c-1 0-5-2-6-2z" class="B"></path><defs><linearGradient id="Dh" x1="146.231" y1="243.934" x2="171.249" y2="213.465" xlink:href="#B"><stop offset="0" stop-color="#575757"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#Dh)" d="M171 213c2 0 4 1 5 2 2 3 2 11 1 14 0 5-3 8-4 13h0l1-1v2c0 1 0 3-1 4h-4s0 1-1 1c0-1-2-1-2-2-2-1-4-4-5-6h0c-6 2-11 2-16 1h-1c-1-1-2-1-3-2s-2-1-3-2-1-1-1-2l-2-2c-1-3-1-7 0-10v2h0c1 0 1-1 1-2l1-2c0-1 0-2 1-3h1 0l6-3 5-1v1c-1 0-1 1-1 2l1 1c3 0 5 3 8 4 1 1 2 1 3 2s2 3 3 3 3 0 4-1c2-2 3-3 4-6s0-5-1-7z"></path><path d="M171 213c2 0 4 1 5 2 2 3 2 11 1 14 0 5-3 8-4 13l-1 1v-1c0-1 0-1-1-2h1v-1c0-1 0-2 1-3h0v-2-1h0v-3h1 0v-1h0v-3h1l-1-1h1v-1h0v-7 2 1l-2-2v-1 3h-1c1-3 0-5-1-7z" class="O"></path><path d="M150 218c3 0 5 3 8 4 1 1 2 1 3 2s2 3 3 3 3 0 4-1v1l-3 3c-2 3-3 3-6 4-1 0-2-1-3-2-3-2-5-7-6-10v-1-2h0l-1-1h1z" class="V"></path><defs><linearGradient id="Di" x1="372.482" y1="66.665" x2="385.198" y2="107.512" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#6b6b6b"></stop></linearGradient></defs><path fill="url(#Di)" d="M304 79c3-1 7 0 11 0 14-1 28 0 42 2l18 2c15 1 30-1 44 6-3 1-6 3-9 4l21 1c-3 1-6 2-10 3s-8 1-12 1h-12c-6 0-12-2-17-4-2-1-5-2-7-3l-19-4c-1 0-2 0-3 1-2 0-7-1-7 0h0c-1 1-3 0-4-1h-2c-1 0-1 0-2-1h-16l-8 1c-1 0-3 1-4 1v-1c1 0 1 0 2-1v-1h2l-3-1c-15 1-29 1-44-1 3-1 7 0 10 0l12 1c10 0 20-1 31-2h0 2c1 0 1-1 2-1s1 1 2 0h1 2 4l1-1-13-1h-3-12 0z"></path><path d="M318 82c3 1 7 0 10 0h13c10 1 20 2 30 4l14 3c4 1 9 1 13 3-9 0-17-3-25-5h-2c-3-1-5-1-8-2-6-1-13 0-19-1-12 0-24-1-35 0-15 1-29 1-44-1 3-1 7 0 10 0l12 1c10 0 20-1 31-2z" class="J"></path><defs><linearGradient id="Dj" x1="351.508" y1="84.67" x2="350.772" y2="92.477" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#747475"></stop></linearGradient></defs><path fill="url(#Dj)" d="M309 84c11-1 23 0 35 0 6 1 13 0 19 1 3 1 5 1 8 2h2v1h0c1 0 2 0 3 1 3 1 7 1 10 2 2 0 4 1 7 1l-1 1v1c0 1-1 2-1 2h-2-1 0c-1-1-2-1-3-1l-1-1c-1 0-2 0-3-1-1 0-3-1-4-1-1-1-2-1-4-1l-19-4c-1 0-2 0-3 1-2 0-7-1-7 0h0c-1 1-3 0-4-1h-2c-1 0-1 0-2-1h-16l-8 1c-1 0-3 1-4 1v-1c1 0 1 0 2-1v-1h2l-3-1z"></path><path d="M336 86l18 1c-1 0-2 0-3 1-2 0-7-1-7 0h0c-1 1-3 0-4-1h-2c-1 0-1 0-2-1z" class="K"></path><defs><linearGradient id="Dk" x1="236.994" y1="112.841" x2="321.595" y2="62.611" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#767576"></stop></linearGradient></defs><path fill="url(#Dk)" d="M262 79l4-1-11-3h13c12 1 23 3 36 4h0 12 3l13 1-1 1h-4-2-1c-1 1-1 0-2 0s-1 1-2 1h-2 0c-11 1-21 2-31 2l-12-1c-3 0-7-1-10 0 15 2 29 2 44 1l3 1h-2v1c-1 1-1 1-2 1v1c-17 3-33 8-50 12-5 2-11 4-16 4-4 1-8 0-10-2h-1l-3-3c-3-3-4-6-4-10l3-1v1h0c2 1 2 2 3 4h2l2 2c0 1 0 1-1 2h1v2l-1 1 1 1c1-2 1-4 2-6l1 1h1c6 3 12 1 18-1l9-3-6-2c-1-2-4-3-6-4h0c0-1 1-2 1-3 1 0 2-1 2-2 2-1 4 0 6-2h0z"></path><path d="M224 89l3-1v1h0c2 1 2 2 3 4v1c-1 1-1 2-1 4v2h1v1l1-2h0c1 1 1 1 1 2h-1 1v1h-1l-3-3c-3-3-4-6-4-10z" class="S"></path><path d="M523 282c2-1 3-2 5-2 1-1 1-1 2-1h1c3-1 8 0 10 0 4 2 6 2 9 5v2l-1 1 1 2h0-1-1c1 1 2 3 3 4l-1 1h-1c2 1 4 3 6 6-2-1-3-1-5-2v1c3 2 7 6 9 9l-4-1h-1c2 2 3 4 4 7s1 6 2 8v3h0l-1-1c-1-2-3-3-5-5l-1-1h-1c-1-1-1-1-1-2h1l-1-2c-2-3-8-1-11-3-1 0-2 1-3 1-2 1-2 1-3 2-1 0-2 1-3 1h-1c-2 0-3 1-5 1l-6 6-11 20-8 17h-1l-1 1h-1 0v-1c-1 1-1 1-1 2v1c-1-2 0-4 0-6 0 1 0 2-1 3v-16c0-1 0-3 1-5h0l1-3v3h1v-11c0 1 0 5 1 6 0 0 1 2 1 3 1-10-1-20 2-30 0-4 1-10 4-14 1 0 2 0 2-1l4-5c1 0 2-1 4-2h0 1l1 1 3-2h1c0-1 1-1 1-1z" class="J"></path><path d="M503 338l1 1c2 0 3 0 4 1v1c-1 0-1 0 0 1l-8 17h-1l4-21z" class="I"></path><defs><linearGradient id="Dl" x1="539.108" y1="292.082" x2="531.039" y2="301.66" xlink:href="#B"><stop offset="0" stop-color="#262726"></stop><stop offset="1" stop-color="#403e41"></stop></linearGradient></defs><path fill="url(#Dl)" d="M519 301c1-1 1-2 2-2 7-4 16-5 23-3h0l-1 1h1 0-3c-1 0-2 1-3 1-1 1-3 2-4 2-2 1-4 3-6 4h0-1c0-1-1-1-2-1h0v-2l-2 2h0-1v-1c1-1 1-2 2-2h0c1-1 2-2 3-2-3-1-5 2-7 3h-1z"></path><path d="M529 299c4-2 8-3 12-2-1 0-2 1-3 1-1 1-3 2-4 2v-1c-1 0-3 1-5 0h0z" class="C"></path><path d="M525 301s0-1 1-1c1-1 1-2 2-2l1 1h0c2 1 4 0 5 0v1c-2 1-4 3-6 4h0-1c0-1-1-1-2-1h0v-2z" class="L"></path><defs><linearGradient id="Dm" x1="526.606" y1="285.574" x2="508.489" y2="296.337" xlink:href="#B"><stop offset="0" stop-color="#303031"></stop><stop offset="1" stop-color="#686868"></stop></linearGradient></defs><path fill="url(#Dm)" d="M521 283h1c0-1 1-1 1-1 1 0 2 0 3 1 0 0 0 1 1 1 1 1 1 2 1 4h1l-1 1h0c-3 0-5 1-7 3-4 2-7 7-10 11h0v-1-1-1c0-1 0-2 1-3 0-2 3-7 4-7 0-2 1-2 0-3h0l2-2 3-2z"></path><path d="M521 283h1c0-1 1-1 1-1 1 0 2 0 3 1 0 0 0 1 1 1 1 1 1 2 1 4h1l-1 1h0v-1c-1-1-1-2-2-3s-1-2-3-2c-1 0-1 1-2 1v-1z" class="B"></path><defs><linearGradient id="Dn" x1="502.582" y1="337.192" x2="518.148" y2="320.576" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#Dn)" d="M517 317v4c1 1 1 1 2 1l-11 20c-1-1-1-1 0-1v-1c-1-1-2-1-4-1l-1-1c1-6 2-14 5-19h0v2 2 1h-1v2c1 1 1 1 3 1h0c0-1 0-2 1-3l1-2 1 1c1-3 3-4 4-6z"></path><path d="M511 324l1-2 1 1-1 2c-1 2-1 4-1 6h0c0-1 0 0-1-1 0-1 1-4 1-6z" class="L"></path><defs><linearGradient id="Do" x1="511.777" y1="299.297" x2="504.556" y2="296.281" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#Do)" d="M516 284h0 1l1 1-2 2h0c1 1 0 1 0 3-1 0-4 5-4 7-1 1-1 2-1 3v1 1 1h0l-4 9-1 1h-3c-1-3-1-4-1-7 0-4 1-10 4-14 1 0 2 0 2-1l4-5c1 0 2-1 4-2z"></path><path d="M516 284h0 1l1 1-2 2h0c1 1 0 1 0 3 0-1-1-2-1-3l2-2s-1 0-1-1z" class="N"></path><defs><linearGradient id="Dp" x1="519.487" y1="313.691" x2="512.602" y2="308.991" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#7f7f7f"></stop></linearGradient></defs><path fill="url(#Dp)" d="M519 301h1c2-1 4-4 7-3-1 0-2 1-3 2h0c-1 0-1 1-2 2v1h1 0l2-2v2h0c1 0 2 0 2 1h1 0c-7 4-12 11-16 18l-1 2c-1 1-1 2-1 3h0c-2 0-2 0-3-1v-2h1v-1-2-2h0c1-7 6-14 11-18z"></path><defs><linearGradient id="Dq" x1="485.871" y1="343.734" x2="514.279" y2="327.993" xlink:href="#B"><stop offset="0" stop-color="#222124"></stop><stop offset="1" stop-color="#454643"></stop></linearGradient></defs><path fill="url(#Dq)" d="M499 333s1 2 1 3c1-10-1-20 2-30 0 3 0 4 1 7h3l1-1c-1 3-2 6-3 10-2 12-2 25-7 38h0v-1c-1 1-1 1-1 2v1c-1-2 0-4 0-6 0 1 0 2-1 3v-16c0-1 0-3 1-5h0l1-3v3h1v-11c0 1 0 5 1 6z"></path><defs><linearGradient id="Dr" x1="547.894" y1="282.929" x2="523.462" y2="292.529" xlink:href="#B"><stop offset="0" stop-color="#080708"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#Dr)" d="M523 282c2-1 3-2 5-2 1-1 1-1 2-1h1c3-1 8 0 10 0 4 2 6 2 9 5v2l-1 1 1 2h0-1-1c1 1 2 3 3 4l-1 1h-1c-1 0-1 0-2-1h-1c-1 0-1 0-2-1h-3c-7-1-13 0-20 2l7-5h0l1-1h-1c0-2 0-3-1-4-1 0-1-1-1-1-1-1-2-1-3-1z"></path><path d="M548 289l-1-1c-1-1-2-1-3-1l-9-1h0 1v-1c3 0 6 1 9 1h0c2 1 3 1 4 3h-1z" class="I"></path><path d="M545 286v-1c-2-2-6-2-10-2 1-1 3-1 4-1s2 0 3-1c2 1 4 2 6 4 0 1 0 1 1 2l1 2h0-1c-1-2-2-2-4-3z" class="J"></path><defs><linearGradient id="Ds" x1="541.313" y1="300.483" x2="530.281" y2="319.136" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#Ds)" d="M544 296c1 0 5 1 6 2v1c3 2 7 6 9 9l-4-1h-1c2 2 3 4 4 7s1 6 2 8v3h0l-1-1c-1-2-3-3-5-5l-1-1h-1c-1-1-1-1-1-2h1l-1-2c-2-3-8-1-11-3-1 0-2 1-3 1-2 1-2 1-3 2-1 0-2 1-3 1h-1c-2 0-3 1-5 1l-6 6c-1 0-1 0-2-1v-4c-1 2-3 3-4 6l-1-1c4-7 9-14 16-18 2-1 4-3 6-4 1 0 3-1 4-2 1 0 2-1 3-1h3 0-1l1-1h0z"></path><path d="M530 315h0c0-1 0-1 1-1 1-2 4-2 6-2-2 1-2 1-3 2-1 0-2 1-3 1h-1z" class="H"></path><path d="M538 304h2v2h-1c-2 1-3 2-5 2-2 1-3 2-5 3h-1c2-2 5-4 7-4 0-1-1-1-1-2 1 0 3-1 4-1z" class="G"></path><path d="M534 305c1 0 3-1 4-1v1c0 1-1 1-1 1h-1s-1 0-1 1c0-1-1-1-1-2z" class="L"></path><path d="M534 305c-1 1-1 1-2 1 2-2 7-4 10-4 2 0 5 1 6 2 1 0 2 1 3 1 1 1 2 1 3 2 2 2 3 4 4 7-1 0-2-1-2-2v-1l-3-3c-2 0-2-2-4-3-3-1-5-1-9-1h-2c-1 0-3 1-4 1z" class="F"></path><path d="M520 312h1s1 0 2-1l1 2v2l1 1-6 6c-1 0-1 0-2-1v-4l3-5z" class="M"></path><path d="M540 311c3-1 6-1 9-1l1 1v-1c-1 0-2-1-3-1-2 0-4 0-6-1 2-1 5 0 7 0 3 1 5 2 8 4 0 1 1 2 2 2 1 3 1 6 2 8v3h0l-1-1c-1-2-3-3-5-5l-1-1h-1c-1-1-1-1-1-2h1l-1-2c-2-3-8-1-11-3z" class="K"></path><path d="M544 296c1 0 5 1 6 2v1c3 2 7 6 9 9l-4-1c-1-2-2-2-4-3 0 0 0-1-1-1-1-1-3-1-5-2h-1c-4-1-9 1-13 2-4 2-8 6-11 9l-3 5c-1 2-3 3-4 6l-1-1c4-7 9-14 16-18 2-1 4-3 6-4 1 0 3-1 4-2 1 0 2-1 3-1h3 0-1l1-1h0z" class="T"></path><path d="M544 296c1 0 5 1 6 2v1c-2-1-4-2-6-2h0-1l1-1h0z" class="B"></path><path d="M348 531c3-1 4-3 6-4 6-3 12-6 19-7l-2 1h1c1 1 1 0 0 1h-2c0 1 1 1 1 2h0c-1 3 0 6 0 9v1h-1c-1-3-2-7-1-10h0v-1l-2 2c-1 2-2 3-2 4v1c-1 2 0 4 0 6 0 1 0 3 1 4 0 1 0 2 1 4 0 1 1 2 1 4s0 3 1 4v1c0 1 2 3 2 4-1 0-3-3-4-4-1 0-2 1-3 2h0 1v1c-2 0-3 1-5 2 0 0-1 1-2 1-1 1 0 1-1 1-2 0-5 3-6 5s-1 4-1 6c-2 6-3 11-3 17l-1 2v-1-5h0v-3c1 0 1 0 1-1-2 7-4 13-7 19-1 1-2 3-3 4l-1 4c-1 0-1 0-1-1-2 3-3 6-4 9-4 8-12 14-21 16-3 1-6 1-9 2-13 2-24-1-37-4-6-1-14-5-19-9l-3-3s1 0 1-1c2-1 3-3 3-5l-1-2v-1c2 0 4 4 5 5 1 0 1 0 2-1 0 0 0-1 1-2v-1h0l2 1c3 6 8 11 14 15 3 2 6 3 10 4 8 3 19 1 27-2h1c2 0 4-1 5-2l6-5h-1l3-3c1-1 2-3 3-4 2-4 3-9 4-13 0-2 1-5 2-7-3 1-5 4-7 6l-6 3c-6 3-11 5-18 6 3-2 5-4 7-6 6-6 11-13 14-22 0-1 1-3 1-5 11-12 14-26 16-41v2-2l1 2h0 0l2 1-3 9h1l1-2c1-5 3-9 7-13v1c-2 1-2 2-3 4l3-3 1-1 2-2v1z" class="J"></path><path d="M352 544h1c1 2 3 4 3 7 0 0-1 1-1 2l-1-1c0-3-1-5-2-8z" class="G"></path><path d="M335 587c2-1 3-6 3-8l1 3c0 3-2 6-4 9v-4z" class="D"></path><path d="M345 531v1c-2 1-2 2-3 4l3-3 1-1 2-2v1a30.44 30.44 0 0 0-8 8v1c-1 1-1 3-1 5l-1-1c1-5 3-9 7-13z" class="C"></path><path d="M338 544l1 1-5 13h0v-2c0-3 1-7 2-10h1l1-2z" class="E"></path><path d="M312 625l2 1c-11 6-26 6-37 3h-1 3c8 3 19 1 27-2h1c2 0 4-1 5-2z" class="I"></path><path d="M355 553c-2 1-3 2-4 4 0-3-1-6-1-8v-5c0-2 0-3-1-5v-4l4 9h-1c1 3 2 5 2 8l1 1z" class="B"></path><path d="M354 552v1h-1c-1-3-2-6-2-9h0 1c1 3 2 5 2 8z" class="T"></path><path d="M365 547h-1c-3-3-5-10-6-13 0-1-1-3-1-4 1-2 2-3 4-5 0 2-1 3-1 5s2 7 4 10c0 1 0 3 1 4h1c0-1-1-5-2-6v-7c0-2 1-4 3-6-1 2-2 3-2 4v1c-1 2 0 4 0 6 0 1 0 3 1 4 0 1 0 2 1 4 0 2 1 4 1 6-1 0-1-1-2-1 0-1 0-2-1-3v1z" class="E"></path><path d="M342 566v-1l5-29c1 3 2 5 2 8-1 4-1 8-2 13-2 8-6 16-8 25l-1-3c2-4 4-9 4-13z" class="I"></path><defs><linearGradient id="Dt" x1="259.842" y1="625.942" x2="260.049" y2="608.883" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#5f6060"></stop></linearGradient></defs><path fill="url(#Dt)" d="M246 611l-1-2v-1c2 0 4 4 5 5 1 0 1 0 2-1 0 0 0-1 1-2v-1h0l2 1c3 6 8 11 14 15 3 2 6 3 10 4h-3 0c-9-2-17-6-23-12l-6-6h-1 0z"></path><defs><linearGradient id="Du" x1="258.95" y1="631.545" x2="252.565" y2="613.366" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#Du)" d="M246 611h0l1 1c4 6 9 10 15 14 2 1 4 1 5 2v1h0-1-2c-6-1-14-5-19-9l-3-3s1 0 1-1c2-1 3-3 3-5z"></path><path d="M246 611h0l1 1c1 2 1 3 0 5l-1 1c0 1-1 1-1 2l-3-3s1 0 1-1c2-1 3-3 3-5z" class="O"></path><defs><linearGradient id="Dv" x1="322.774" y1="605.309" x2="329.207" y2="610.011" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#393938"></stop></linearGradient></defs><path fill="url(#Dv)" d="M327 600v3 1l-3 9 1-1v-1c1 0 1 0 1-1 0 0 0-1 1-1v-2h0l1-2h0l1-1v-2c2-2 2-6 3-9l3-6v4c0 4-2 7-3 10-1 2-1 5-2 7-2 6-5 9-9 13-1 1-6 5-7 5l-2-1 6-5h-1l3-3c1-1 2-3 3-4 2-4 3-9 4-13z"></path><path d="M341 563h0l2-4v3c-1 0 0 1-1 1v2 1h0c0 4-2 9-4 13 0 2-1 7-3 8l-3 6c-1 3-1 7-3 9v2l-1 1h0l-1 2h0v2c-1 0-1 1-1 1 0 1 0 1-1 1v1l-1 1 3-9v-1-3c0-2 1-5 2-7 0 0 2-4 2-5 2-4 4-9 6-13 1-4 2-8 4-12z" class="K"></path><path d="M349 562h0c-1 1-1 2-1 3v-1l2-1c-4 7-6 15-8 23-2 7-5 14-7 20-2 3-3 6-4 9-4 8-12 14-21 16-3 1-6 1-9 2-13 2-24-1-37-4h2 1 0v-1c12 4 24 6 36 3 5-1 10-3 15-6 4-3 8-7 11-12 2-2 2-5 3-8l6-14c1-4 3-8 4-12 2-6 3-12 6-17h1z" class="I"></path><path d="M349 562c2-5 7-8 11-12l-3-4-4-5s-1-1-1-2v-1c1 2 3 4 4 5 2 2 5 5 7 5h2v-1-1c1 1 1 2 1 3 1 0 1 1 2 1 0-2-1-4-1-6 0 1 1 2 1 4s0 3 1 4v1c0 1 2 3 2 4-1 0-3-3-4-4-1 0-2 1-3 2h0 1v1c-2 0-3 1-5 2 0 0-1 1-2 1-1 1 0 1-1 1-2 0-5 3-6 5s-1 4-1 6c-2 6-3 11-3 17l-1 2v-1-5h0v-3c1 0 1 0 1-1-2 7-4 13-7 19-1 1-2 3-3 4l-1 4c-1 0-1 0-1-1 2-6 5-13 7-20 2-8 4-16 8-23l-2 1v1c0-1 0-2 1-3h0z" class="B"></path><path d="M350 563c1-1 3-2 4-4 4-2 7-5 11-7h1 1c-2 2-4 2-5 3-3 2-7 5-10 7l-3 6c-2 6-3 14-5 20-2 5-5 10-7 15l-1 4c-1 0-1 0-1-1 2-6 5-13 7-20 2-8 4-16 8-23z" class="J"></path><defs><linearGradient id="Dw" x1="315.692" y1="563.577" x2="330.139" y2="573.599" xlink:href="#B"><stop offset="0" stop-color="#19191a"></stop><stop offset="1" stop-color="#464645"></stop></linearGradient></defs><path fill="url(#Dw)" d="M336 534v2-2l1 2h0 0l2 1-3 9c-1 3-2 7-2 10v2c-1 5-2 10-4 15s-5 10-9 15c-2 4-5 8-9 11h0c-2 1-3 2-5 3h-2c6-6 11-13 14-22 0-1 1-3 1-5 11-12 14-26 16-41z"></path><defs><linearGradient id="Dx" x1="319.304" y1="574.68" x2="333.54" y2="582.596" xlink:href="#B"><stop offset="0" stop-color="#070708"></stop><stop offset="1" stop-color="#595958"></stop></linearGradient></defs><path fill="url(#Dx)" d="M341 543c0-2 1-2 2-3 1 1 1 1 1 3 1 7-1 13-3 20-2 4-3 8-4 12-2 4-4 9-6 13 0 1-2 5-2 5-3 1-5 4-7 6l-6 3c-6 3-11 5-18 6 3-2 5-4 7-6h2c2-1 3-2 5-3h0 1l4-4c1-1 4-6 5-7-1 3-3 5-4 7l-1 1c-1 1-2 3-4 4l-1 1h0 1c6-4 10-13 14-20 5-12 11-25 14-38z"></path><path d="M341 543c0-2 1-2 2-3 1 1 1 1 1 3h0c-1 1-1 3-1 4 0 2-1 3-1 4-1-3 0-5-1-8z" class="K"></path><path fill="#fff" d="M317 620c-5 0-11-1-17-3-20-4-38-13-54-27-17-16-30-36-40-57-20-41-26-86-28-130l-1-53c0-54 3-108 21-159 9-24 19-45 37-63 19-21 47-34 75-35 26-1 51 10 70 28 9 8 16 18 22 29 9 16 16 33 21 52 8 29 12 59 14 88v1 1h0l-1 1h5c1 1 1 1 2 1 3 0 7 2 10 3 12 4 26 10 34 21 1 1 2 3 3 4l3 9 1 3 1 9v16c-1 6-2 11-5 17h1l-4 9c-2 2-7 10-7 13 2-3 3-5 4-7v2h0l1-1c-1 2-2 4-4 6l-4 4-2-1c-2 3-5 7-8 9l-1 1-1 1v1h-1l1 1c-6 4-12 7-19 8l-19 11v-2h0c-1 0-1 0-1-1-1 1-2 2-3 2l-3 3-1 1 1-3h0v-1c-1 1-3 3-4 3v1c-1 0-4 3-4 2h-1l-10 8-4 4h-1c2-1 3-2 4-4h0c6-5 10-11 11-19-1-1-1-5-1-6v1h0-1c-1 0-2 1-3 1 0-2 0-4-1-6 0-1 0-1-1-2-4 6-11 11-16 16-10 9-18 18-25 29-3 3-5 8-7 12 0 1-1 3-2 4v-1l-1 1h-1c-1 2-1 4-1 6-1 2-1 5-2 7-1 3-3 6-3 8v1s1-1 2-1h0l4-4c1-2 3-3 4-5 3-1 5-3 7-3 1-1 1 0 2 0h0 0c1 0 1-1 1-2 1 0 2-1 3-2h1l1-1c2 0 4-1 6-2 1 0 2 0 4-1l-1 1-4 1 4 2v1c7-1 14-2 21-1 3 0 5 1 8 1l7 3c9 5 17 12 21 21-2-2-4-5-7-8-6-5-13-10-21-12-2-1-5-1-7-1-17-2-33 3-46 13-4 3-8 6-10 10-3 3-5 7-7 10-1 1-1 3-1 4l-2 12c-2 15-5 29-16 41 0 2-1 4-1 5-3 9-8 16-14 22-2 2-4 4-7 6 7-1 12-3 18-6l6-3c2-2 4-5 7-6-1 2-2 5-2 7-1 4-2 9-4 13-1 1-2 3-3 4l-3 3z"></path><path d="M312 494c0 1 1 1 1 2 1 1 1 2 2 3h0c0 2 0 3-1 4l-2-9z" class="R"></path><path d="M435 296c2 0 4 0 6 1h-2l-1 1c0 1 0 2 1 3 0 2 0 2-1 3h-2c1-2 0-5 0-7l-1-1z" class="L"></path><path d="M297 536c1-1 2-1 3-1l10-2s-2 1-2 2c-1 1-2 1-2 3l-1-2h0v-1c-1 1-1 2-2 3l-1-1 1-1h-1c-1 1-1 1-1 2l-1-1v-1c-1 1-2 2-3 2v-1l1-1h-1z" class="W"></path><path d="M393 353h0 1v-1c1-3 6-6 8-7h2c0 1 0 1-1 1h1c-1 1-2 2-3 2l-3 3c-1 1-2 2-4 3h-1v-1z" class="S"></path><path d="M284 555l1 1h2c1 2 2 5 3 7l3 6c-3-2-6-5-8-9-1-2-1-3-2-4l1-1h0z" class="D"></path><path d="M414 338h2v1c-1 0-1 0-2 1h1l-1 1-1 1 9 3-1 1c-1-1-3-1-4-1h0c-2-1-4-2-6-1 1 0 2 0 2 1-1 1-2 0-4 0l-5 1h-1c1 0 1 0 1-1h-2l12-7z" class="M"></path><path d="M409 345h3l-1-1h-4 0c2-1 4-1 6-1v-1h-2 0c1-1 2-1 3-1l-1 1 9 3-1 1c-1-1-3-1-4-1h0c-2-1-4-2-6-1 1 0 2 0 2 1-1 1-2 0-4 0z" class="L"></path><path d="M361 387c1-3 3-5 5-7l14-16c4-3 6-7 10-10 0 3-3 5-4 7-3 1-5 4-6 6l-13 13c-1 2-2 4-4 6-1 0-1 1-2 1z" class="S"></path><path d="M416 338v-1c1 0 2 1 4 1h0c0 1 1 1 1 1h1l2 2c1 1 3 2 3 3 1 1 1 2 2 3-2 0-2 0-3-1s-2-1-4-1l-9-3 1-1 1-1h-1c1-1 1-1 2-1v-1z" class="T"></path><path d="M424 341c1 1 3 2 3 3 1 1 1 2 2 3-2 0-2 0-3-1 0-2-2-3-4-4h0 1l1-1z" class="F"></path><path d="M307 560l1 1c2 3 2 5 2 7-1 4-4 6-7 7l-3 2h0c-2 1-6 1-9 1-2 0-5 0-7-1v-2c1-1 3-2 5-3 3 0 7 0 9 1 2 0 3-1 5-2h0 0 1v-2 1 1h1c2-3 2-6 1-10l1-1zm-34-21c6-1 11-2 17-2-1 1-2 1-3 1h-1v1h-1c-2 4-1 9-1 14v2h0l-1 1c-10-3-19-8-27-13l-1-1h0c4 0 8-1 13-2l5-1z" class="J"></path><path d="M256 543l-1-1h0c4 0 8-1 13-2v2h-1 0l1 1 6 3c-2 0-3-1-5-1-2-1-4-2-6-2 3 2 6 3 9 5l-11-5c-2 0-4-1-5 0z" class="W"></path><path d="M273 539c6-1 11-2 17-2-1 1-2 1-3 1h-1v1h-1l-1 1c-1 1-4 1-6 1v-1l-1 1h1c1 0 1 1 2 2h0c-1 0-3-1-4-1v1l3 2h-1 0c-1 0-2-1-4 0l-1-1c-2-1-3-2-5-1l-1-1h0 1v-2l5-1z" class="R"></path><path d="M268 540l5-1c0 1 0 1-1 2h-2v1c1 0 2 1 3 1v1c-2-1-3-2-5-1l-1-1h0 1v-2z" class="V"></path><defs><linearGradient id="Dy" x1="333.372" y1="453.005" x2="302.219" y2="463.46" xlink:href="#B"><stop offset="0" stop-color="#1c1d1c"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#Dy)" d="M312 494l-3-13c-3-14-2-29 4-42 3-7 7-13 12-19l-1 8c0 1 0 2-1 3h-1c-2 4-3 9-5 13l-3 15c-2 9-1 18-1 27l2 13c-1-1-1-2-2-3 0-1-1-1-1-2z"></path><path d="M303 538c1-1 1-2 2-3v1h0c-1 3-2 5-2 8v1 5 2h0s0 1-1 1c0 1-1 1-2 2l2 1c0 1 1 1 2 1l2 2v2h0c1 4 1 7-1 10h-1v-1-1 2h-1 0c-1-1-3-2-5-2-3-2-5-4-7-7l-1 1c-1-2-2-5-3-7h-2l-1-1v-2c0-5-1-10 1-14h1v-1h1c1 0 2 0 3-1h2 3v1h1c0-1 0-1 1-2h0 1l-1 1v1c1 0 2-1 3-2v1l1 1c0-1 0-1 1-2h1l-1 1 1 1z" class="K"></path><path d="M289 541h0v-2c1-1 1-1 2-1v2 3 7 1 2 1c-2-3-2-8-2-12v-1h0z" class="L"></path><path d="M291 554v-1-2c2 3 2 5 5 7s6 6 8 9c-2-1-5-2-7-3s-4-5-5-8l-1-2z" class="P"></path><defs><linearGradient id="Dz" x1="297.594" y1="555.339" x2="307.373" y2="564.978" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#919191"></stop></linearGradient></defs><path fill="url(#Dz)" d="M297 553h1c1 0 2 1 2 2l2 1c0 1 1 1 2 1l2 2v2h0c1 4 1 7-1 10h-1v-1c2-6-4-11-7-16v-1z"></path><path d="M290 537h2l1 1-2 2v-2c-1 0-1 0-2 1v2h0c-2 4-2 8-2 13 1 1 1 3 2 4 0 1 1 3 2 4l-1 1c-1-2-2-5-3-7h-2l-1-1v-2c0-5-1-10 1-14h1v-1h1c1 0 2 0 3-1z" class="N"></path><path d="M285 545c2 3 1 7 2 11h-2l-1-1v-2h1v-8z" class="B"></path><path d="M285 539h1v-1h1c0 2-2 5-2 7v8h-1c0-5-1-10 1-14z" class="M"></path><path d="M303 538c1-1 1-2 2-3v1h0c-1 3-2 5-2 8v1 5 2h0s0 1-1 1c0 1-1 1-2 2 0-1-1-2-2-2h-1l-1-2-1-1c-1-3-1-5 0-9h-1 0c0 1-1 1-1 2v-1c-1 0 0 0 0-1v-3l-1-1h3v1h1c0-1 0-1 1-2h0 1l-1 1v1c1 0 2-1 3-2v1l1 1c0-1 0-1 1-2h1l-1 1 1 1z" class="B"></path><path d="M293 541l1-1h0s0-1 1-1c0-1 0-1 1 0h0c-1 1-1 1-1 2h0-1 0c0 1-1 1-1 2v-1c-1 0 0 0 0-1z" class="E"></path><path d="M297 538c1 0 2-1 3-2v1c-1 1-2 3-2 5v4 4h-1v-1c0-2-1-3-1-5 0-1 0-2 1-3v-2-1z" class="C"></path><path d="M298 546c1 1 1 1 1 2l1 2 1 1c0-1 0-1 1-2 0 1 0 1 1 1v2h0s0 1-1 1c0 1-1 1-2 2 0-1-1-2-2-2h-1l-1-2v-3l1 1v1h1v-4z" class="N"></path><path d="M303 538c1-1 1-2 2-3v1h0c-1 3-2 5-2 8v1 5c-1 0-1 0-1-1-1 1-1 1-1 2l-1-1-1-2c0-1 0-1-1-2v-4c0-2 1-4 2-5l1 1c0-1 0-1 1-2h1l-1 1 1 1z" class="Q"></path><path d="M303 538c1-1 1-2 2-3v1h0c-1 3-2 5-2 8v1 2h-1c0-3 0-7 1-9z" class="K"></path><path d="M301 538c0-1 0-1 1-2h1l-1 1c-1 2-1 5-1 7s-1 3-1 5h0l1 1h-1l-1-2c0-1 0-1-1-2v-4c0-2 1-4 2-5l1 1z" class="F"></path><path d="M298 546v-4c0-2 1-4 2-5l1 1c-2 3-2 6-2 10 0-1 0-1-1-2z" class="Q"></path><path d="M398 351h1v1h1s1 0 1 1c-3 1-6 2-7 4 0 1-1 2-1 3-2 11-6 21-11 30-3 6-7 13-10 18-11 18-23 35-27 56l-2 10c1-1 1-2 2-3 0 2-1 4-1 6v7 3h1c0 1 0 3-1 4 0 3-3 11-2 13 1-2 2-5 4-6 0 0 1-1 2-1h0l4-4c1-2 3-3 4-5 3-1 5-3 7-3 1-1 1 0 2 0h0 0c1 0 1-1 1-2 1 0 2-1 3-2h1l1-1c2 0 4-1 6-2 1 0 2 0 4-1l-1 1-4 1 4 2v1c7-1 14-2 21-1 3 0 5 1 8 1l7 3c9 5 17 12 21 21-2-2-4-5-7-8-6-5-13-10-21-12-2-1-5-1-7-1-17-2-33 3-46 13-4 3-8 6-10 10-3 3-5 7-7 10-1 1-1 3-1 4l-2 12c-2 15-5 29-16 41-5 4-10 6-16 8l-10 2c-5 2-8 3-11 7h0 0c0-3 0-6 2-8 3-5 10-5 15-6 1-1 0-1 0-1h0l3-2c3-1 6-3 7-7 0-2 0-4-2-7l-1-1-1 1h0v-2l-2-2c-1 0-2 0-2-1l-2-1c1-1 2-1 2-2 1 0 1-1 1-1h0v-2-5-1c0-3 1-5 2-8l1 2c0-2 1-2 2-3 0-1 2-2 2-2 2-1 5-1 7-3 1-1 2-1 3-3h0l1 2h1v-4-1l-6-17-2-4c1-1 1-2 1-4h0l-2-13c0-9-1-18 1-27l3-15c2-4 3-9 5-13h1c1-1 1-2 1-3l3 23 1 5c1 3 1 7 1 10 0 0 0 1 1 1v-4c0-3 1-7 1-10 2-18 8-35 18-50l12-16c1 0 1-1 2-1 2-2 3-4 4-6l13-13c1-2 3-5 6-6 1-2 4-4 4-7 0 0 2-1 2-2h1 0v1 1h1c2-1 3-2 4-3z" class="T"></path><path d="M315 499l2 6c-1 1-1 1-1 2l-2-4c1-1 1-2 1-4z" class="O"></path><path d="M309 553c0 2 1 4 2 6-1-1-1-1-3-2l-1-4h2z" class="R"></path><path d="M341 509h0v2c-1 0-1 0 0 1 0-1 1-3 2-3-1 3-3 6-4 9h0c-1-2 0-7 2-9z" class="I"></path><path d="M336 504h1v9 5c-1 2-1 5-1 7v-21z" class="F"></path><path d="M311 534h0v2c1-1 1-1 2-1 0 4 0 7 1 11h0l-1-1v-1c-2-2-2-5-2-7v-3z" class="M"></path><path d="M333 488c1 3 1 7 1 10v7c-1-3-2-7-2-11 1 2 1 4 1 5v-2-4-5z" class="F"></path><path d="M303 544c0-3 1-5 2-8l1 2c-1 3 0 6 0 10-1-2-1-3-3-4z" class="G"></path><path d="M311 534v3c-1 1-1 1-1 2v1c1 1 0 2 1 3v3c1 2 2 4 2 7-1-3-2-6-3-10-1-1 0-5-1-6l2-3z" class="H"></path><path d="M332 483c1-2 0-5 1-7v3 9 5 4 2c0-1 0-3-1-5v-5-6zm-5 54c3 6 3 9 2 15-1-2-1-3-1-5s-1-3-2-5l1-1v-4z" class="D"></path><path d="M319 505c1 1 1 3 2 4 0 1 0 2 1 3 0 1 1 2 1 3l1 2 1 1v-1l3 13-6-16c-1-3-3-6-3-9zm-2 0c2 4 3 9 4 14l1 1v1 3l-6-17c0-1 0-1 1-2z" class="B"></path><path d="M330 477h0c1-3 1-5 1-8h0v3h1v-3h0v14 6l-1-5v-2-2 5c-1 2-1 4-1 6v12h0v-6-17-3z" class="E"></path><path d="M310 533c2-1 5-1 7-3 1-1 2-1 3-3v5l-1 1v-1h-1 0c-1 1-2 2-2 4h-1v-1-2h-1v1l-1 1c-1 0-1 0-2 1v-2h0l-2 3-2 2h0c0-1 1-3 1-4h0c0-1 2-2 2-2z" class="V"></path><path d="M294 585c2-3 7-4 10-5 2-1 4-2 5-3 2-1 3-3 5-5h0v2l-3 3c-1 1-2 1-3 2s-3 2-4 3v1l-10 2z" class="I"></path><path d="M336 464c0 3-1 18 0 20l1-2h0v7 15h-1c0-4-1-7-1-11v-12-8l1-3c-1-1 0-4 0-6z" class="C"></path><path d="M336 464c0 3-1 18 0 20l1-2h0v7c-1 2 0 4-1 5-1-8 0-16 0-24-1-1 0-4 0-6z" class="D"></path><path d="M314 534v-1h1v2 1h1c0-2 1-3 2-4h0 1l-1 2c-2 4 4 10 4 15l-2-5c-1-1-1-3-2-4h-1c0 1 3 9 2 12l-1-4c-1-5-3-9-4-14h0z" class="G"></path><path d="M308 557c2 1 2 1 3 2 1 4 1 8-1 12-2 3-5 6-8 7h-2c1-1 0-1 0-1h0l3-2c3-1 6-3 7-7 0-2 0-4-2-7h1v-3l-1-1z" class="U"></path><path d="M313 553c0-3-1-5-2-7v-3c-1-1 0-2-1-3v-1c0-1 0-1 1-2 0 2 0 5 2 7v1l1 1h0c1 2 1 4 2 6 1 6 1 13-2 18-1 2-4 4-6 6v-1c4-4 7-7 7-13 0-3 0-7-2-9z" class="B"></path><path d="M303 544c2 1 2 2 3 4l3 5h-2l1 4 1 1v3h-1l-1-1-1 1h0v-2l-2-2c-1 0-2 0-2-1l-2-1c1-1 2-1 2-2 1 0 1-1 1-1h0v-2-5-1z" class="U"></path><path d="M306 558c-1-2-1-4-1-6l1-1 1 2 1 4 1 1v3h-1l-1-1c0-1 0-2-1-2z" class="S"></path><path d="M306 558c1 0 1 0 2 1v-1s1 1 1 0v3h-1l-1-1c0-1 0-2-1-2z" class="V"></path><defs><linearGradient id="EA" x1="304.152" y1="544.065" x2="305.58" y2="551.338" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#5e5e5f"></stop></linearGradient></defs><path fill="url(#EA)" d="M303 544c2 1 2 2 3 4l3 5h-2l-1-2c-1-1-1-1-1-2h-1v3c0 1 0 1-1 2l-1 2-2-1c1-1 2-1 2-2 1 0 1-1 1-1h0v-2-5-1z"></path><path d="M343 474c1-1 1-2 2-3 0 2-1 4-1 6v7 3h1c0 1 0 3-1 4 0 3-3 11-2 13 0 2-1 3-1 4-1-2 0-4 0-6 0-7-1-14 0-21l1 5c1-2 1-9 1-12h0z" class="K"></path><path d="M344 491h0-1l1-7v3h1c0 1 0 3-1 4z" class="E"></path><path d="M322 525c3 4 4 8 5 12v4l-1 1c-1-3-3-9-5-12 1 6 4 11 5 16-1-1-1-2-1-2l-4-7c0 4 4 7 3 11h0c-2-5-4-9-6-14l1-2v1l1-1v-5h0l1 2h1v-4z" class="Q"></path><defs><linearGradient id="EB" x1="335.986" y1="533.263" x2="325.241" y2="507.692" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#EB)" d="M321 489c1 1 1 2 1 3 1 1 1 2 1 2v3l2 6c0 2 0 4 1 5 1 0 2-1 3-2s0-2 1-3h0c0 5 1 11 1 17 1 6 3 15 1 21-1-8-3-15-5-23l-3-15-3-14z"></path><defs><linearGradient id="EC" x1="352.447" y1="490.064" x2="363.835" y2="499.684" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#5d5e5e"></stop></linearGradient></defs><path fill="url(#EC)" d="M356 488c3-1 5-3 7-3 1-1 1 0 2 0h0 0c1 0 1-1 1-2 1 0 2-1 3-2h1l1-1c2 0 4-1 6-2 1 0 2 0 4-1l-1 1-4 1 4 2v1l-12 3c-9 4-18 12-23 20l-2 3v1c-1 0-2 2-2 3-1-1-1-1 0-1v-2h0v-1c0-1 1-2 1-4 1-2 2-5 4-6 0 0 1-1 2-1h0l4-4c1-2 3-3 4-5z"></path><defs><linearGradient id="ED" x1="352.82" y1="457.794" x2="327.081" y2="419.05" xlink:href="#B"><stop offset="0" stop-color="#333335"></stop><stop offset="1" stop-color="#4f4f4e"></stop></linearGradient></defs><path fill="url(#ED)" d="M330 463c0-3 1-7 1-10 2-18 8-35 18-50v2h1 0c-1 2-1 4-1 6h0c-1 1-1 3-2 4v1l-1 1h0c0 1 0 1-1 2s-1 3-1 4c-4 9-7 18-8 28l-3 28v-3c-1 2 0 5-1 7v-14h0v3h-1v-3h0c0 3 0 5-1 8h0c0-4 1-10 0-14z"></path><defs><linearGradient id="EE" x1="326.665" y1="474.743" x2="314.852" y2="474.151" xlink:href="#B"><stop offset="0" stop-color="#282727"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#EE)" d="M324 428l3 23v1h-1v-1c0-2 0-6-2-7 0 2 0 4-1 6h0v-3c-1 2-1 6-2 9-1 14-2 27 0 41l4 20v1l-1-1-1-2c0-1-1-2-1-3-1-1-1-2-1-3-1-1-1-3-2-4-2-8-4-16-4-24-2-17 2-34 8-50 1-1 1-2 1-3z"></path><defs><linearGradient id="EF" x1="333.766" y1="477.271" x2="319.276" y2="474.99" xlink:href="#B"><stop offset="0" stop-color="#454545"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#EF)" d="M323 450h0c1-2 1-4 1-6 2 1 2 5 2 7v1h1v-1l1 5c1 3 1 7 1 10 0 0 0 1 1 1v-4c1 4 0 10 0 14v3 17 6c-1 1 0 2-1 3s-2 2-3 2c-1-1-1-3-1-5l-2-6v-3s0-1-1-2c0-1 0-2-1-3h0c-1-12 0-27 2-39z"></path><path d="M327 451l1 5v1l-1-1c-1-1-1-3-1-4h1v-1z" class="M"></path><path d="M329 466s0 1 1 1v-4c1 4 0 10 0 14v3h0c0-1-1-1-1-2h1v-4c0-1-1-1-1-2h0v12h-1l1-10c-1-3-1-6 0-9v1z" class="N"></path><defs><linearGradient id="EG" x1="384.942" y1="460.527" x2="351.699" y2="369.976" xlink:href="#B"><stop offset="0" stop-color="#313333"></stop><stop offset="1" stop-color="#7a7778"></stop></linearGradient></defs><path fill="url(#EG)" d="M398 351h1v1h1s1 0 1 1c-3 1-6 2-7 4 0 1-1 2-1 3-2 11-6 21-11 30-3 6-7 13-10 18-11 18-23 35-27 56l-2 10h0c0 3 0 10-1 12l-1-5c0-6 0-12 2-18 0-10 2-20 5-29 4-11 11-21 18-31l16-26 6-12c1-2 2-4 2-6 1-1 2-2 2-4 1-1 0-2 0-3h1 0v1 1h1c2-1 3-2 4-3z"></path><path d="M398 351h1v1h1s1 0 1 1c-3 1-6 2-7 4 0 1-1 2-1 3v-1-1c0-1 1-2 2-3l-1-1c0 1 0 2-1 2v-1l1-1c2-1 3-2 4-3z" class="G"></path><path d="M341 481c0-6 0-12 2-18v1 3l-1 9c1-1 0-2 0-3l1-3v-2h0c0-2 1-2 1-3l1-1-2 10h0c0 3 0 10-1 12l-1-5z" class="C"></path><defs><linearGradient id="EH" x1="323.265" y1="472.219" x2="389.061" y2="370.592" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#EH)" d="M386 361c-4 6-7 12-11 17l-10 13c-1 2-3 3-3 5 2-2 4-5 6-8l17-22c1-2 2-4 4-6-3 9-9 18-14 26l-11 18c-6 10-13 20-17 30-2 4-3 8-4 12-4 12-6 24-6 36h0l-1 2c-1-2 0-17 0-20 0-4 1-8 1-12l3-18 5-15c1-1 1-1 1-2h0l1-1v-1c1-1 1-3 2-4h0c0-2 0-4 1-6h0-1v-2l12-16c1 0 1-1 2-1 2-2 3-4 4-6l13-13c1-2 3-5 6-6z"></path><path d="M435 296h-6l7-3h5c1 1 1 1 2 1 3 0 7 2 10 3 12 4 26 10 34 21 1 1 2 3 3 4l3 9 1 3 1 9v16c-1 6-2 11-5 17h1l-4 9c-2 2-7 10-7 13 2-3 3-5 4-7v2h0l1-1c-1 2-2 4-4 6l-4 4-2-1c-2 3-5 7-8 9l-1 1-1 1v1h-1l1 1c-6 4-12 7-19 8l-19 11v-2h0c-1 0-1 0-1-1-1 1-2 2-3 2l-3 3-1 1 1-3h0v-1c-1 1-3 3-4 3v1c-1 0-4 3-4 2h-1l-10 8-4 4h-1c2-1 3-2 4-4h0c6-5 10-11 11-19-1-1-1-5-1-6v1h0-1c-1 0-2 1-3 1 0-2 0-4-1-6 0-1 0-1-1-2-4 6-11 11-16 16-10 9-18 18-25 29-3 3-5 8-7 12 0 1-1 3-2 4v-1l-1 1h-1c-1 2-1 4-1 6-1 2-1 5-2 7-1 3-3 6-3 8v1c-2 1-3 4-4 6-1-2 2-10 2-13 1-1 1-3 1-4h-1v-3-7c0-2 1-4 1-6-1 1-1 2-2 3l2-10c4-21 16-38 27-56 3-5 7-12 10-18 5-9 9-19 11-30 0-1 1-2 1-3 1-2 4-3 7-4 0-1-1-1-1-1h-1v-1h-1l3-3c1 0 2-1 3-2l5-1c2 0 3 1 4 0 0-1-1-1-2-1 2-1 4 0 6 1h0c1 0 3 0 4 1l1-1c2 0 3 0 4 1s1 1 3 1c-1-1-1-2-2-3 0-1-2-2-3-3l-2-2h-1s-1 0-1-1h0c-2 0-3-1-4-1v1h-2c-1-1-2-1-3-2 1 0 2-1 3-1 1-1 1-2 2-3 0-2 2-4 4-5 1 0 1-1 2-2s2-1 3-2v-1c0-1 1-1 2-2-1 0-2 0-3 1h0l-1-1c1 0 1-1 2-1 1-2 2-3 4-4l2-2 1-1c-1 0-3 0-4-1-4 0-7-1-11-2h0l5-2c3-1 6-2 9-2h1l4-1h2c1-1 1-1 1-3-1-1-1-2-1-3l1-1h2c-2-1-4-1-6-1z" class="T"></path><path d="M455 405l-1 1h-1c0-1 1-2 1-2l1 1zm-19-45c1 2 3 3 3 5-1 0-2-1-3-2v-1l-1-1 1-1z" class="F"></path><path d="M454 325l2-1c2 2 5 4 6 6l-1 1h1l-1 1v-1l-1-1c-2-2-4-4-6-5z" class="N"></path><path d="M439 395c1 4 1 8 0 11-1-1-2-3-2-4 1-1 2-5 2-7z" class="B"></path><path d="M413 345c0-1-1-1-2-1 2-1 4 0 6 1h0 0c0 1 1 1 1 1l6 3c-1 0-3 0-4-1h0c-2 0-3-1-4-1s-1-1-2-1h0 1c-1-1-1-1-2-1z" class="K"></path><path d="M422 345c2 0 3 0 4 1s1 1 3 1c1 0 2 2 3 3v2c-4-3-7-5-11-6l1-1z" class="C"></path><path d="M429 315h5v1h-3l-1 1h4c-1 1-3 1-4 1l-5 1c1-2 2-3 4-4z" class="N"></path><path d="M429 315l2-2h2c1 1 1 1 2 1 4 0 8 0 11 3h1l2 1c-5-1-10-2-15-2v-1h-5z" class="C"></path><path d="M449 396c0 3-1 6-2 9h1c1-3 2-5 3-7s1-3 2-5l1 1-1 1c0 4-3 7-5 10l-2 5c-1 2-2 4-4 6 2-4 4-8 5-12 1-2 1-5 2-8h0z" class="F"></path><path d="M478 375l1-1h0c0 5 0 9-1 14-1-1-1-1-1-2-1-1-1-2-2-3l-2 1 1-3 1-2c0-1 1-3 2-4h1z" class="M"></path><path d="M475 379l2-1c0 1-1 3-2 4 0-1 0-1-1-1l1-2z" class="H"></path><path d="M478 375c0 1 0 2-1 3l-2 1c0-1 1-3 2-4h1z" class="L"></path><path d="M431 390l6 12c0 1 1 3 2 4 1 3-1 6-2 9 0-2-1-5-2-7v-3-1c0-5-2-9-5-13l1-1z" class="C"></path><defs><linearGradient id="EI" x1="445.825" y1="339.065" x2="445.535" y2="355.648" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#4c4b4c"></stop></linearGradient></defs><path fill="url(#EI)" d="M446 349c-1 0-8-8-9-9-2-2-5-3-7-4 6 1 12 4 17 8l-1 1 5 5c1 1 2 3 3 4 0 0 0 1 1 2-1-1-2-2-3-2h0c-2-2-4-5-6-5z"></path><path d="M440 388l1-1c0 2 0 4 1 6 0 0 0 1 1 2 1-1 2-2 2-3v7c0 6-3 13-6 18h-1l3-6v-9c0-1-1-3 0-4 0-3 0-7-1-10z" class="D"></path><path d="M445 392v7l-1 1-1-1c-1-2-1-4-1-6 0 0 0 1 1 2 1-1 2-2 2-3z" class="Q"></path><path d="M414 335c1-1 1-2 2-3 0-2 2-4 4-5 0 1 1 1 2 1v1h-1 0c2 2 6 3 9 4-2 0-5-1-6 0-2 0-4 1-6 1 0 1 1 1 1 2h0-1l1 1s0 1 1 1c-2 0-3-1-4-1v1h-2c-1-1-2-1-3-2 1 0 2-1 3-1z" class="N"></path><path d="M414 335c1-1 1-2 2-3 0-2 2-4 4-5 0 1 1 1 2 1v1h-1-1l-1 1h2c-1 1-1 2-2 2v2c-2 0-3 0-5 1z" class="W"></path><path d="M434 317c2 0 4 1 6 1 6 2 11 3 16 6h0l-2 1c2 1 4 3 6 5h0 0c-5-5-12-8-18-8l-10-2c-1 0-1-1-2-2 1 0 3 0 4-1z" class="C"></path><path d="M432 320v-1c3-1 6 1 9 1 1 0 1 1 2 1 2 1 5 1 8 2l3 2c2 1 4 3 6 5h0 0c-5-5-12-8-18-8l-10-2z" class="F"></path><defs><linearGradient id="EJ" x1="433.491" y1="337.276" x2="442.286" y2="352.657" xlink:href="#B"><stop offset="0" stop-color="#201f1f"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#EJ)" d="M445 354l-10-10c-3-3-5-6-10-7l1-1c8 2 15 8 20 14 1 2 2 3 3 5h0c1 0 1 1 2 1h-1 0c-2-2-3-3-4-5l-2 1 1 2z"></path><defs><linearGradient id="EK" x1="478.051" y1="385.853" x2="465.504" y2="398.888" xlink:href="#B"><stop offset="0" stop-color="#747475"></stop><stop offset="1" stop-color="#8d8b8b"></stop></linearGradient></defs><path fill="url(#EK)" d="M473 384l2-1c1 1 1 2 2 3 0 1 0 1 1 2-1 2-2 5-3 7-1 3-2 6-4 8-1 0-2 0-3-1l1-1c0-2 0-4 1-6 0-1 0-1 1-2l2-9z"></path><path d="M469 401h0v1l3-3h0c1 0 1-1 1-1 0-1 1-2 1-3h1c-1 3-2 6-4 8-1 0-2 0-3-1l1-1z" class="S"></path><path d="M438 382c0-2 0-4-1-6h0 1c0-2 0-2-1-3v-1c5 5 8 10 8 17v3c0 1-1 2-2 3-1-1-1-2-1-2-1-2-1-4-1-6-1-2-2-4-3-5z" class="O"></path><defs><linearGradient id="EL" x1="428.582" y1="390.911" x2="436.189" y2="388.643" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#EL)" d="M428 384c0-2-2-3-3-5 2 1 4 4 6 5h1l-1-2 1-1 3 5c2 2 3 6 4 9 0 2-1 6-2 7l-6-12-3-6z"></path><defs><linearGradient id="EM" x1="454.513" y1="359.374" x2="459.603" y2="357.085" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#EM)" d="M447 344c8 6 16 13 17 24h1v1 2h-1-1-2c-1-7-5-12-9-17h0c1 0 2 1 3 2-1-1-1-2-1-2-1-1-2-3-3-4l-5-5 1-1z"></path><path d="M454 354c1 1 2 3 3 5l3 5c1 1 2 3 2 5l1 2h-2c-1-7-5-12-9-17h0c1 0 2 1 3 2-1-1-1-2-1-2z" class="N"></path><defs><linearGradient id="EN" x1="423.993" y1="410.397" x2="433.706" y2="408.783" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#383838"></stop></linearGradient></defs><path fill="url(#EN)" d="M427 404v-1c0-2-5-6-7-8-1-1-4-4-5-4 1 0 2 0 3 1l8 4-3-3h0c4 1 6 4 8 8 3 3 3 7 4 10 0 5-1 10-4 13l-2 3c-1-1 1-4 2-6v-3-1c1-1 1-3 0-5h-1c0-3-1-4-2-6 0-1-1-1-1-2z"></path><path d="M402 383c7-1 13 0 19 2 1 1 3 2 4 3-1-1-1-2-2-3l3 1c1 2 3 3 4 5 3 4 5 8 5 13v1c-1-2-3-4-4-6-3-4-6-7-9-10-3-2-7-4-10-4-6-1-12 2-18 3l-1-1c1 0 1 0 2-1 3-1 6-2 9-2v-1h-2z" class="Q"></path><defs><linearGradient id="EO" x1="465.035" y1="392.36" x2="452.88" y2="379.022" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#565556"></stop></linearGradient></defs><path fill="url(#EO)" d="M465 368c2 5 0 14-2 19l-8 18-1-1c1-3 2-5 2-7 2-7 2-14 1-21 1 1 1 1 2 1h1c1 0 1 0 1-1v-5h2 1 1v-2-1z"></path><defs><linearGradient id="EP" x1="449.472" y1="365.044" x2="457.249" y2="361.875" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#EP)" d="M446 349c2 0 4 3 6 5 4 5 8 10 9 17v5c0 1 0 1-1 1h-1c-1 0-1 0-2-1-1-2-1-5-3-8-2-5-6-9-9-14l-1-2 2-1c1 2 2 3 4 5h0 1c-1 0-1-1-2-1l-3-6z"></path><defs><linearGradient id="EQ" x1="459.754" y1="409.146" x2="467.169" y2="385.179" xlink:href="#B"><stop offset="0" stop-color="#5f5e5f"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#EQ)" d="M457 405c1-2 2-4 3-5 4-6 8-11 11-17 0 3 0 6-1 9l1 1c-1 1-1 1-1 2-1 2-1 4-1 6l-1 1c1 1 2 1 3 1l-1 2c-1 1-2 3-3 4h-1c0-1 0-1 1-1v-1l-3-2-2 2c-1 1-2 1-3 2h0-1c-1 0-1 0-1-1-1-1 0-2 0-3z"></path><defs><linearGradient id="ER" x1="464.963" y1="405.622" x2="470.064" y2="398.895" xlink:href="#B"><stop offset="0" stop-color="#585758"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#ER)" d="M459 409h0l2-3 2-3 2-2c1-1 1-2 2-3 0-2 0-1 1-1 1-1 1-3 2-4v-1l1 1c-1 1-1 1-1 2-1 2-1 4-1 6l-1 1c1 1 2 1 3 1l-1 2c-1 1-2 3-3 4h-1c0-1 0-1 1-1v-1l-3-2-2 2c-1 1-2 1-3 2h0z"></path><path d="M468 402c1 1 2 1 3 1l-1 2c-1-1-1 0-2-1v-2z" class="M"></path><defs><linearGradient id="ES" x1="477.805" y1="371.623" x2="457.479" y2="350.868" xlink:href="#B"><stop offset="0" stop-color="#070606"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#ES)" d="M467 336c6 10 8 23 5 34-1 4-3 10-5 14 0-2 0-3 1-5v-4c1 0 1-1 1-1v-7c-1-7-3-15-7-22h3c0-2 3-3 3-5-1-1-1-2-1-3v-1z"></path><defs><linearGradient id="ET" x1="422.022" y1="398.675" x2="405.648" y2="421.441" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#ET)" d="M396 388c8 2 17 3 23 8a30.44 30.44 0 0 1 8 8c0 1 1 1 1 2 1 2 2 3 2 6h1c1 2 1 4 0 5v1 3c-1 2-3 5-2 6l-3 3c-1 1-2 2-3 2l-3 3-1 1 1-3h0v-1c4-6 8-13 7-21-2-5-5-7-9-10h1c0-1 0-1-1-1 0-1-2-2-3-3h0c-1-2-4-3-6-4s-4-2-7-3c-3 0-6-1-9-1 1-1 2-1 3-1z"></path><path d="M431 412c1 2 1 4 0 5v1 3c-1 2-3 5-2 6l-3 3c-1 1-2 2-3 2 2-2 3-4 5-7 2-4 3-8 3-13z" class="E"></path><defs><linearGradient id="EU" x1="450.086" y1="427.14" x2="448.46" y2="406.188" xlink:href="#B"><stop offset="0" stop-color="#242426"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#EU)" d="M457 405c0 1-1 2 0 3 0 1 0 1 1 1h1 0c1-1 2-1 3-2l2-2 3 2v1c-1 0-1 0-1 1h1v1l-1 1-1 1v1h-1l1 1c-6 4-12 7-19 8l-19 11v-2h0c1-1 3-2 4-3 9-5 17-11 23-19l3-4z"></path><path d="M457 405c0 1-1 2 0 3 0 1 0 1 1 1-1 2-2 3-4 3h0 0v-3l3-4z" class="O"></path><path d="M466 409h1v1l-1 1-1 1v1h-1l1 1c-6 4-12 7-19 8 2-1 3-2 5-3l6-4c3-1 7-3 9-6z" class="B"></path><defs><linearGradient id="EV" x1="430.537" y1="331.555" x2="468.253" y2="336.894" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#EV)" d="M425 319l5-1c1 1 1 2 2 2l10 2c6 0 13 3 18 8h0 0l1 1v1l1-1h-1l1-1c2 2 4 4 5 6v1c0 1 0 2 1 3 0 2-3 3-3 5h-3c-5-6-10-13-18-16-3-2-5-3-8-4h-1l-1-1c-1 0-1 0-2-1h0 2-3-3v-1h1l1-1-1-1h-2c-1 0-2 0-3 1h0l-1-1c1 0 1-1 2-1z"></path><path d="M425 319l5-1c1 1 1 2 2 2l10 2v1c5 2 9 4 13 9-1-1-3-2-4-3-3-2-6-3-9-5-3 0-5-1-8-1h-3-3v-1h1l1-1-1-1h-2c-1 0-2 0-3 1h0l-1-1c1 0 1-1 2-1z" class="K"></path><path d="M425 319l5-1c1 1 1 2 2 2l10 2v1h-1 0c-4-2-8-1-12-1l1-1-1-1h-2c-1 0-2 0-3 1h0l-1-1c1 0 1-1 2-1z" class="G"></path><defs><linearGradient id="EW" x1="447.607" y1="319.863" x2="454.273" y2="334.63" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#EW)" d="M442 322c6 0 13 3 18 8h0v1c1 1 1 1 1 2-1-1-1-1-2 0-1 0-1 0-1 1v1h0c0-1-2-3-3-3-4-5-8-7-13-9v-1z"></path><defs><linearGradient id="EX" x1="432.254" y1="300.267" x2="456.373" y2="326.139" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#EX)" d="M431 305h1 4c2 1 4 1 5 1l6 2 6 3 11 7h0c0 1 1 1 2 2h0c-2-1-3-1-4-2-2 2-2 3-1 5v1c1 0 1 1 2 2l-14-8-2-1h-1c-3-3-7-3-11-3-1 0-1 0-2-1h-2l1-1c-1 0-3 0-4-1-4 0-7-1-11-2h0l5-2c3-1 6-2 9-2z"></path><path d="M431 305h1 4c2 1 4 1 5 1l6 2 6 3 11 7h0 0c-3-1-5-3-8-5-5-3-11-4-17-5-2-1-7-1-8-3z" class="T"></path><path d="M432 312c-1 0-3 0-4-1-4 0-7-1-11-2h0l5-2h1c2 1 4 0 7 1 5 1 10 3 15 5-4-1-9-1-13-1z" class="J"></path><defs><linearGradient id="EY" x1="431.124" y1="364.83" x2="447.398" y2="357.413" xlink:href="#B"><stop offset="0" stop-color="#030203"></stop><stop offset="1" stop-color="#646464"></stop></linearGradient></defs><path fill="url(#EY)" d="M420 338c-1 0-1-1-1-1l-1-1h1c1 0 1 0 2 1 2 1 5 1 7 3 2 1 4 3 6 5 7 7 16 16 20 25 2 5 3 12 3 18-1 0-2 1-4 2l-2 1c-1 1-1 4-2 5h0c1-5 2-11 1-16-2-11-9-22-18-28v-2c-1-1-2-3-3-3-1-1-1-2-2-3 0-1-2-2-3-3l-2-2h-1s-1 0-1-1h0z"></path><defs><linearGradient id="EZ" x1="433.006" y1="344.322" x2="458.722" y2="336.811" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#EZ)" d="M427 320h2l1 1-1 1h-1v1h3 3-2 0c1 1 1 1 2 1l1 1h1c3 1 5 2 8 4 8 3 13 10 18 16 4 7 6 15 7 22v7s0 1-1 1c0-1 1-4 0-6s-2-5-3-7l-4-10-5-5c-6-7-14-11-21-15-4-2-8-3-12-3h-1v-1c-1 0-2 0-2-1 1 0 1-1 2-2s2-1 3-2v-1c0-1 1-1 2-2z"></path><path d="M427 320h2l1 1-1 1h-1v1h3 3-2 0c1 1 1 1 2 1l1 1h1c0 1 1 2 1 2 3 0 6 2 8 3-2 0-4-1-6-2h0v1h0 0c-1 0-2 0-2-1-4 0-8-1-12-1l-2 2h-1v-1c-1 0-2 0-2-1 1 0 1-1 2-2s2-1 3-2v-1c0-1 1-1 2-2z" class="C"></path><path d="M426 326h0 8c1 0 2 1 3 2h0c-4 0-8-1-12-1h-4c1 0 3-1 5-1z" class="J"></path><path d="M427 320h2l1 1-1 1h-1v1h3c-2 1-3 1-5 2v1c-2 0-4 1-5 1h4l-2 2h-1v-1c-1 0-2 0-2-1 1 0 1-1 2-2s2-1 3-2v-1c0-1 1-1 2-2z" class="H"></path><path d="M436 325c3 1 5 2 8 4 8 3 13 10 18 16 4 7 6 15 7 22v7s0 1-1 1c0-1 1-4 0-6s-2-5-3-7l-4-10 2 1 1-1-3-6c-4-7-10-12-16-16-2-1-5-3-8-3 0 0-1-1-1-2z" class="F"></path><path d="M464 352c1 3 3 8 2 11l-1-1-4-10 2 1 1-1z" class="D"></path><defs><linearGradient id="Ea" x1="492.71" y1="367.46" x2="472.45" y2="357.982" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#424143"></stop></linearGradient></defs><path fill="url(#Ea)" d="M435 296h-6l7-3h5c1 1 1 1 2 1 3 0 7 2 10 3 12 4 26 10 34 21 1 1 2 3 3 4l3 9 1 3 1 9v16c-1 6-2 11-5 17h1l-4 9c-2 2-7 10-7 13 2-3 3-5 4-7v2h0l1-1c-1 2-2 4-4 6l-4 4-2-1 7-12c0-3 1-6 2-10 1-10 2-20-1-29-1-3-3-6-5-9-4-7-9-11-15-15-1-1-1-2-2-2v-1c-1-2-1-3 1-5 1 1 2 1 4 2h0c-1-1-2-1-2-2h0l-11-7-6-3-6-2c-1 0-3 0-5-1h-4l4-1h2c1-1 1-1 1-3-1-1-1-2-1-3l1-1h2c-2-1-4-1-6-1z"></path><path d="M470 320c1 0 3 2 4 3 6 4 10 9 12 16 1 2 2 5 2 7 2 13 1 24-2 36-1-1 0-3 0-5l1-10c2-10 1-24-5-33-2-3-4-6-7-9-2-2-4-3-5-5z" class="J"></path><path d="M490 342c3 7 3 18 1 26 0 3-1 6-2 8v1l1-1h1l-4 9c-2 2-7 10-7 13 2-3 3-5 4-7v2h0l1-1c-1 2-2 4-4 6l-4 4-2-1 7-12c2-2 3-4 4-7 3-12 4-23 2-36l1 1c0-1 1-2 1-2h1c-1-1-1-2-1-3z" class="I"></path><defs><linearGradient id="Eb" x1="468.646" y1="336.685" x2="474.643" y2="323.709" xlink:href="#B"><stop offset="0" stop-color="#49494a"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#Eb)" d="M463 326c-1-1-1-2-2-2v-1c-1-2-1-3 1-5 1 1 2 1 4 2h0c1 0 2 1 3 1h0l-1-1v-1l2 1c1 2 3 3 5 5 3 3 5 6 7 9h0c0 1 0 2 1 3v1l1 1v1c-2 1-3 1-4 2-1 0-1-1-2-1-4-7-9-11-15-15z"></path><defs><linearGradient id="Ec" x1="474.12" y1="330.046" x2="482.434" y2="322.998" xlink:href="#B"><stop offset="0" stop-color="#454545"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#Ec)" d="M464 315c0-2 1-4 2-5s1-1 1-2l4 3c2 1 3 3 5 4 5 5 9 10 11 16 2 4 2 7 3 11 0 1 0 2 1 3h-1s-1 1-1 2l-1-1c0-2-1-5-2-7-2-7-6-12-12-16-1-1-3-3-4-3l-2-1v1l1 1h0c-1 0-2-1-3-1-1-1-2-1-2-2h0l-11-7 1-1h0c2 1 5 3 7 4l1-1 2 2z"></path><path d="M461 314l1-1 2 2h1-1c-1 0-2-1-3-1z" class="H"></path><path d="M435 296h-6l7-3h5c1 1 1 1 2 1 3 0 7 2 10 3 12 4 26 10 34 21 1 1 2 3 3 4l3 9 1 3 1 9v16c-1 6-2 11-5 17l-1 1v-1c1-2 2-5 2-8 2-8 2-19-1-26-1-4-1-7-3-11-2-6-6-11-11-16-2-1-3-3-5-4l-4-3c0 1 0 1-1 2s-2 3-2 5l-2-2-1 1c-2-1-5-3-7-4h0l-1 1-6-3-6-2c-1 0-3 0-5-1h-4l4-1h2c1-1 1-1 1-3-1-1-1-2-1-3l1-1h2c-2-1-4-1-6-1z" class="J"></path><defs><linearGradient id="Ed" x1="439.625" y1="299.173" x2="465.599" y2="310.401" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#Ed)" d="M441 297c1-1 3 0 5 0 7 2 15 6 22 11 3 2 6 4 8 7-2-1-3-3-5-4l-4-3c0 1 0 1-1 2s-2 3-2 5l-2-2-1 1c-2-1-5-3-7-4h0l-1 1-6-3-6-2c-1 0-3 0-5-1h-4l4-1h2c1-1 1-1 1-3-1-1-1-2-1-3l1-1h2z"></path><path d="M438 304h0 2c2 0 4 0 6 1 2 0 3 2 4 2 1 1 3 1 4 2-3-1-4-1-6-2l-1 1-6-2c-1 0-3 0-5-1h-4l4-1h2z" class="F"></path><path d="M441 306l1-1c2 0 4 1 6 2h0l-1 1-6-2z" class="C"></path><path d="M439 301c-1-1-1-2-1-3l1-1v1c2 1 4 1 5 2l8 3c2 1 4 2 5 4-3-2-7-4-10-4-2-1-5-1-7-2h-1z" class="T"></path><path d="M413 345c1 0 1 0 2 1h-1-2c0 1 3 1 4 2 3 1 6 2 9 4l3 3 8 5-1 1 1 1v1c1 1 2 2 3 2l2 2h0c-5-2-10-4-16-5h0c1 3 6 4 8 6 1 1 4 3 4 4v1c1 1 1 1 1 3h-1 0c1 2 1 4 1 6 1 1 2 3 3 5l-1 1c1 3 1 7 1 10-1-4-3-10-6-13v1l-3-5-1 1 1 2h-1c-2-1-4-4-6-5 1 2 3 3 3 5l3 6-1 1c-1-2-3-3-4-5l-3-1c1 1 1 2 2 3-1-1-3-2-4-3-6-2-12-3-19-2h2v1c-3 0-6 1-9 2-1 1-1 1-2 1l1 1h2c-1 0-2 0-3 1 3 0 6 1 9 1 3 1 5 2 7 3s5 2 6 4h0c1 1 3 2 3 3 1 0 1 0 1 1h-1c4 3 7 5 9 10 1 8-3 15-7 21-1 1-3 3-4 3v1c-1 0-4 3-4 2h-1l-10 8-4 4h-1c2-1 3-2 4-4h0c6-5 10-11 11-19-1-1-1-5-1-6v1h0-1c-1 0-2 1-3 1 0-2 0-4-1-6 0-1 0-1-1-2-4 6-11 11-16 16-10 9-18 18-25 29-3 3-5 8-7 12 0 1-1 3-2 4v-1l-1 1h-1c-1 2-1 4-1 6-1 2-1 5-2 7-1 3-3 6-3 8v1c-2 1-3 4-4 6-1-2 2-10 2-13 1-1 1-3 1-4h-1v-3-7c0-2 1-4 1-6-1 1-1 2-2 3l2-10c4-21 16-38 27-56 3-5 7-12 10-18 5-9 9-19 11-30 0-1 1-2 1-3 1-2 4-3 7-4 0-1-1-1-1-1h-1v-1h-1l3-3c1 0 2-1 3-2l5-1c2 0 3 1 4 0z" class="J"></path><path d="M401 374h1l2 1h0-1v1h1 0l-6-1h2l1-1z" class="H"></path><path d="M397 373c1 1 2 1 4 1l-1 1h-2-3s2-1 2-2z" class="N"></path><path d="M420 371l-1-1h0v-1-1-1c1 0 2 1 3 2l1 1-3 1z" class="E"></path><path d="M398 373l4-4h9c-1 1-2 1-3 1h-1c-2 1-4 1-6 2l-1 1h-2z" class="O"></path><path d="M411 369c2 1 5 1 6 2h-1-1c-3 0-4 1-7 1-1 0-2 1-4 1-1 1-3 0-4 0l1-1c2-1 4-1 6-2h1c1 0 2 0 3-1z" class="N"></path><path d="M404 375c6 0 9 2 13 5l-1 1-12-5h0-1v-1h1 0z" class="G"></path><path d="M400 373c1 0 3 1 4 0 2 0 3-1 4-1 3 0 4-1 7-1v2c-2 1-3 1-5 1s-6-1-8 0h-1c-2 0-3 0-4-1h1 2z" class="I"></path><path d="M389 389h-2 0c3-2 5-3 8-4 2-1 4-2 7-2h2v1c-3 0-6 1-9 2-1 1-1 1-2 1l1 1h2c-1 0-2 0-3 1h-4z" class="N"></path><path d="M393 387l1 1h2c-1 0-2 0-3 1h-4c2-1 3-1 4-2z" class="I"></path><path d="M412 351l6 1c1 0 2-1 3 0h0 3 1l3 3 8 5-1 1c-2-1-5-3-7-5-4-2-10-3-15-4l-1-1z" class="C"></path><path d="M421 352h3 1l3 3-10-3c1 0 2-1 3 0h0z" class="F"></path><path d="M393 407c-1 1-3 1-4 1-3 1-5 2-7 3h0c2-3 4-5 8-7 4-1 6-1 10 1h0c-1 1-2 1-4 1-1 1-2 1-3 1z" class="Q"></path><path d="M413 360l-11-1c2-1 3-2 4-3h1c4-1 9 0 13 1h2c0 1-1 2-1 2h-1c-1 1-2 1-3 2l-4-1z" class="M"></path><path d="M420 357h2c0 1-1 2-1 2h-1c-1 1-2 1-3 2l-4-1c2-1 5-2 7-2v-1z" class="O"></path><defs><linearGradient id="Ee" x1="432.178" y1="367.033" x2="427.325" y2="357.607" xlink:href="#B"><stop offset="0" stop-color="#212122"></stop><stop offset="1" stop-color="#414040"></stop></linearGradient></defs><path fill="url(#Ee)" d="M422 357l3 1c4 1 7 2 11 5 1 1 2 2 3 2l2 2h0c-5-2-10-4-16-5-1 0-2 0-3-1v1c-1 0-1 0-1 1l-3-2h-1c1-1 2-1 3-2h1s1-1 1-2z"></path><path d="M422 357l3 1c-1 1-3 2-4 2-1 1-2 1-3 1h-1c1-1 2-1 3-2h1s1-1 1-2z" class="P"></path><defs><linearGradient id="Ef" x1="410.77" y1="391.735" x2="395.419" y2="405.062" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#Ef)" d="M388 398h-2 0c1-1 2-2 3-2 3-1 6-1 9-2h12c2 1 3 2 5 3h0c1 1 3 2 3 3 1 0 1 0 1 1h-1c-4-2-7-4-12-5-1-1-5 0-7 0s-3 0-5 1v1h-1-4v-1l-1 1z"></path><path d="M421 363c0-1 0-1 1-1v-1c1 1 2 1 3 1h0c1 3 6 4 8 6 1 1 4 3 4 4v1c1 1 1 1 1 3h-1 0c1 2 1 4 1 6-2-3-4-6-7-9l-4-3v-1c1 1 2 1 2 1 1 1 2 2 3 2v-1s0-1-1-1h0v-1c-1-3-7-4-10-6h0z" class="C"></path><path d="M433 368c1 1 4 3 4 4v1c1 1 1 1 1 3h-1 0c1 2 1 4 1 6-2-3-4-6-7-9l1-1 2 2h2c1-2-3-3-3-6z" class="G"></path><path d="M417 371c6 1 11 6 15 10l-1 1 1 2h-1c-2-1-4-4-6-5 1 2 3 3 3 5l-2-2c-2-2-4-3-6-5-3-2-6-2-10-3 2 0 3 0 5-1v-2h1 1z" class="C"></path><path d="M417 371c6 1 11 6 15 10l-1 1 1 2h-1c-2-1-4-4-6-5 1 2 3 3 3 5l-2-2c-1-1-1-2-2-3h0l1-1h0s2 2 3 2v-1c-1 0-1-1-2-2-1 0-2-1-3-2-2-2-4-2-6-4h-1 1z" class="I"></path><path d="M402 374c2-1 6 0 8 0 4 1 7 1 10 3 2 2 4 3 6 5l2 2 3 6-1 1c-1-2-3-3-4-5l-3-1c1 1 1 2 2 3-1-1-3-2-4-3s-3-3-5-4l1-1c-4-3-7-5-13-5l-2-1z" class="T"></path><path d="M417 380c3 2 7 4 9 6l-3-1c1 1 1 2 2 3-1-1-3-2-4-3s-3-3-5-4l1-1z" class="B"></path><path d="M400 405c4 2 7 7 9 11 2 3 2 7 2 10v1c-1-1-1-5-1-6v1h0-1c-1 0-2 1-3 1 0-2 0-4-1-6 0-1 0-1-1-2-1-2-2-3-3-5h-3-1c-4 2-6 8-10 9 3-3 6-7 9-9 1-1 2 0 4 0l-1-1c-2-2-4-2-6-2 1 0 2 0 3-1 2 0 3 0 4-1z" class="G"></path><path d="M413 345c1 0 1 0 2 1h-1-2c0 1 3 1 4 2 3 1 6 2 9 4h-1-3 0c-1-1-2 0-3 0l-6-1 1 1c-5-1-7 0-12 1 0-1-1-1-1-1h-1v-1h-1l3-3c1 0 2-1 3-2l5-1c2 0 3 1 4 0z" class="I"></path><path d="M406 347h2 0c0 1-1 1-2 2h-1c1-1 1-1 1-2z" class="L"></path><path d="M401 348h1c2-1 3-1 4-1 0 1 0 1-1 2l-4 2h-2-1l3-3z" class="H"></path><path d="M410 349c4 1 8 1 11 3h0c-1-1-2 0-3 0l-6-1-4-1c-1 0 0 0-1-1h3z" class="T"></path><path d="M406 349h4-3c1 1 0 1 1 1l4 1 1 1c-5-1-7 0-12 1 0-1-1-1-1-1h-1v-1h2l4-2h1z" class="B"></path><path d="M406 349h4-3c1 1 0 1 1 1-2 1-5 1-7 1l4-2h1z" class="E"></path><defs><linearGradient id="Eg" x1="427.153" y1="369.618" x2="428.271" y2="380.579" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#Eg)" d="M422 369c2-1 4-1 5 0v1l4 3c3 3 5 6 7 9 1 1 2 3 3 5l-1 1c1 3 1 7 1 10-1-4-3-10-6-13h0v-1c-3-5-7-10-12-12-1-1-2-1-3-1l3-1-1-1z"></path><path d="M422 369c2-1 4-1 5 0v1l4 3c3 3 5 6 7 9 1 1 2 3 3 5l-1 1c-1-3-2-6-4-8-4-5-7-8-13-10l-1-1z" class="K"></path><path d="M436 380c2 2 3 5 4 8s1 7 1 10c-1-4-3-10-6-13h0v-2h0c0-1 0-2 1-3z" class="P"></path><path d="M399 396c2 0 6-1 7 0 5 1 8 3 12 5 4 3 7 5 9 10 1 8-3 15-7 21-1 1-3 3-4 3v1c-1 0-4 3-4 2h-1l-10 8-4 4h-1c2-1 3-2 4-4h0c6-5 10-11 11-19v-1c0-3 0-7-2-10-2-4-5-9-9-11h0 1c-2-2-8-5-11-5s-6 2-7 4c0-2 1-3 3-4l1-1s1 0 1-1l1-1v1h4 1v-1c2-1 3-1 5-1z" class="K"></path><path d="M413 406c1 1 2 3 2 4h0c1 2 0 3 0 4l-1-3c1-1 0-3-1-5z" class="F"></path><path d="M407 402c2 0 4 1 5 3l1 1c1 2 2 4 1 5-2-3-4-6-7-9z" class="E"></path><defs><linearGradient id="Eh" x1="417.002" y1="429.529" x2="413.909" y2="437.449" xlink:href="#B"><stop offset="0" stop-color="#626262"></stop><stop offset="1" stop-color="#818182"></stop></linearGradient></defs><path fill="url(#Eh)" d="M416 430h2c1 0 1 1 1 1l-3 4v1c-1 0-4 3-4 2h-1c2-2 3-5 5-8z"></path><path d="M403 400l4 2c3 3 5 6 7 9l1 3c1 3 1 8 0 11v-3-1h0v-3-2-1c-1 1-1 1-1 2h-1v1-1l-4-9h1c-1-3-3-5-6-6 0-1-1-1-2-2h1z" class="I"></path><path d="M410 408c2 2 4 4 4 7l-1 2-4-9h1z" class="C"></path><path d="M394 398v-1c2-1 3-1 5-1 1 1 3 1 4 1v1c1 0 3 1 4 1h-3c-1 1-1 0-1 1h-1c1 1 2 1 2 2 3 1 5 3 6 6h-1l4 9v1-1h1c0-1 0-1 1-2v1 2 3h0v1 3h0c0 6-4 11-8 15 1-2 2-3 3-5 2-5 3-14 1-20h-1c-1-5-5-8-9-10-2-2-8-5-11-5s-6 2-7 4c0-2 1-3 3-4l1-1s1 0 1-1l1-1v1h4 1z" class="B"></path><path d="M394 398v-1c2-1 3-1 5-1 1 1 3 1 4 1v1c1 0 3 1 4 1h-3c-1 1-1 0-1 1h-1c1 1 2 1 2 2 3 1 5 3 6 6h-1c-3-4-6-6-10-8-2-1-3-1-5-2h0z" class="F"></path><path d="M394 398v-1c2-1 3-1 5-1 1 1 3 1 4 1v1h-9 0z" class="H"></path><defs><linearGradient id="Ei" x1="420.988" y1="404.61" x2="414.89" y2="429.165" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#Ei)" d="M399 396c2 0 6-1 7 0 5 1 8 3 12 5 4 3 7 5 9 10 1 8-3 15-7 21-1 1-3 3-4 3l3-4s0-1-1-1h-2l1-3c2-6 3-13 0-19-3-4-5-8-10-9-1 0-3-1-4-1v-1c-1 0-3 0-4-1z"></path><path d="M399 396c2 0 6-1 7 0 5 1 8 3 12 5 4 3 7 5 9 10 1 8-3 15-7 21-1 1-3 3-4 3l3-4c4-5 5-12 5-18 0-4-2-7-5-10l-1 2-1-2-1 1h0l1 4c-3-4-5-8-10-9-1 0-3-1-4-1v-1c-1 0-3 0-4-1z" class="K"></path><path d="M399 396c2 0 6-1 7 0l1 2 12 5-1 2-1-2-1 1h0l1 4c-3-4-5-8-10-9-1 0-3-1-4-1v-1c-1 0-3 0-4-1z" class="C"></path><defs><linearGradient id="Ej" x1="375.988" y1="450.267" x2="359.09" y2="434.039" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#Ej)" d="M345 471v-3c2-10 7-19 12-27 6-11 12-20 23-27 1 0 1 0 2-1 3-2 8-4 12-4-2 1-3 3-4 5l-7 8c0 1-1 2-2 3l6-6c4-1 6-7 10-9h1 3c1 2 2 3 3 5-4 6-11 11-16 16-10 9-18 18-25 29-3 3-5 8-7 12 0 1-1 3-2 4v-1l-1 1h-1c-1 2-1 4-1 6-1 2-1 5-2 7-1 3-3 6-3 8v1c-2 1-3 4-4 6-1-2 2-10 2-13 1-1 1-3 1-4h-1v-3-7c0-2 1-4 1-6z"></path><path d="M354 474c2-4 4-9 6-13 6-10 15-18 23-27l12-13c1-1 4-3 4-4v-1c-1 2-3 3-5 4-1 0-1 0-2-1l1-1c2-2 5-7 8-8 1 2 2 3 3 5-4 6-11 11-16 16-10 9-18 18-25 29-3 3-5 8-7 12 0 1-1 3-2 4v-1-1z" class="K"></path><path d="M349 476c2-7 5-14 10-20 2-4 4-7 6-10h0c-2 3-4 6-5 10-3 5-5 10-7 16l1 2v1l-1 1h-1c-1 2-1 4-1 6-1 2-1 5-2 7-1 3-3 6-3 8v1c-2 1-3 4-4 6-1-2 2-10 2-13 1-1 1-3 1-4 1-9 3-18 7-26 1-2 1-5 3-7 0 3-2 5-3 8-1 4-2 8-3 13v1z" class="J"></path><path d="M352 476l1-4 1 2v1l-1 1h-1z" class="B"></path><path d="M348 482l-1 6c-1-1-1-3 0-4v-1h0l1-1z" class="K"></path><path d="M349 475v1l-1 6-1 1 2-8z" class="C"></path></svg>