<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="50 38 568 636"><!--oldViewBox="0 0 658 752"--><style>.B{fill:#2b2a2a}.C{fill:#d5d3d4}.D{fill:#302f30}.E{fill:#666566}.F{fill:#dfdedf}.G{fill:#b2b0b1}.H{fill:#c3c2c3}.I{fill:#202020}.J{fill:#434243}.K{fill:#7b7b7c}.L{fill:#cdcccd}.M{fill:#252425}.N{fill:#e8e6e8}.O{fill:#bcbbbc}.P{fill:#dad9da}.Q{fill:#4e4d4e}.R{fill:#555456}.S{fill:#343434}.T{fill:#a7a6a6}.U{fill:#3a393b}.V{fill:#edeced}.W{fill:#111011}.X{fill:#e3e2e3}.Y{fill:#5e5d5e}.Z{fill:#f3f2f2}.a{fill:#1c1b1c}.b{fill:#c8c6c7}.c{fill:#484849}.d{fill:#949394}.e{fill:#9a9999}.f{fill:#191919}.g{fill:#9e9c9e}.h{fill:#717071}.i{fill:#8f8e8e}.j{fill:#c0c1c0}.k{fill:#3e3d3e}.l{fill:#acabac}.m{fill:#8a8989}.n{fill:#848484}.o{fill:#6c6b6c}.p{fill:#767576}.q{fill:#b8b7b8}.r{fill:#5a595a}.s{fill:#f7f6f6}.t{fill:#fefefe}.u{fill:#a2a1a1}.v{fill:#0d0d0d}.w{fill:#817f81}.x{fill:#131313}.y{fill:#030303}.z{fill:#0a0a0a}</style><path d="M468 646l1-2 1 1-2 8c0-2 0-4 1-5v-1l-1-1z" class="I"></path><path d="M546 176l4-3c0 2-1 2-2 3l-2 2v-2z" class="G"></path><path d="M236 376l1 3h0c1 1 1 2 1 3l-1 2-1 1v-9z" class="k"></path><path d="M508 564c2 1 4 2 5 4l-2 2c0-1-1-1-2-2 0-2 0-2-1-4z" class="U"></path><path d="M544 184c1 1 3 2 4 3h0-1c-2 1-3 2-5 2 0-2 1-3 2-5z" class="i"></path><path d="M536 373h1l2 2 2 4c-1-1-2-1-3-1-1-1-2-2-2-3v-2z" class="R"></path><path d="M602 608c1 0 1 0 2 1-2 2-3 5-6 8v-2l4-7z" class="W"></path><path d="M529 468h2c1 1 2 2 2 3 1 3 2 5 3 7h-1c-1-1-2-3-3-5h0c0-1-2-4-3-5z" class="q"></path><path d="M546 540s1-1 1-2c1-3 5-6 9-8-1 1-1 2-2 3-2 2-5 4-6 6l-2 1z" class="o"></path><path d="M590 272c3 2 7 5 10 8-3-1-6-3-8-4l-3-2v-1h0l1-1z" class="X"></path><path d="M589 273c1 1 2 1 3 2v1l-3-2v-1z" class="Z"></path><path d="M238 357h0c2-1 3-1 4-2h0l-2 3-1 1-1 1-1 1c0 1-1 2-2 2 0 2-2 4-3 6 1-4 4-9 6-12z" class="S"></path><path d="M228 533h1c1 0 2 1 2 2v1l2 1c0 2 2 3 2 5v2c-3-3-5-7-7-11z" class="B"></path><path d="M581 319l3 6h0c1 5 2 11 1 16h0c-1-1 0-3 0-4 0-2 0-3-1-5 0-3-1-6-2-8 0-2-1-3-2-5h1z" class="P"></path><path d="M541 386l9 13-2-1c-2 0-3-4-4-5s-3-4-3-5v-2z" class="k"></path><path d="M550 173l5-5c0 1 1 1 2 1l-8 8-1-1c1-1 2-1 2-3z" class="e"></path><path d="M567 526c-6 0-10 1-15 4l2-2c6-4 17-4 23-3h-4v1h-6z" class="B"></path><path d="M241 343l1-1v1c-1 1-1 1-2 3h1v1h1c-4 2-7 5-10 8 1-5 6-8 9-12z" class="S"></path><path d="M555 168c2-2 4-5 5-9h1c1-4 1-8 0-12h0c1 0 1 1 2 2 1 7-3 14-6 20-1 0-2 0-2-1z" class="i"></path><path d="M498 541v-2c1-1 1-1 1-2h0 0l1 1h1c0 2 0 4-1 6h0v-2l-1 1c0 3 0 6-1 9v-4l-1-1v7c-1-2-1-8 0-10 1-1 1-1 1-3z" class="n"></path><path d="M243 331c-2 2-6 4-7 7-1 1-2 2-3 4v-1l1-2c2-4 5-9 9-10v2z" class="p"></path><path d="M553 196c4 1 8 3 11 4 2 1 3 1 5 3-3-1-7-2-10-3h-3c0-1-4-1-5-1h0l-1-1c1 0 1 0 1-1 1 0 2 1 4 1-1 0-1-1-2-2z" class="Q"></path><path d="M551 197c1 0 2 1 4 1h1l-1 1h-4l-1-1c1 0 1 0 1-1z" class="I"></path><path d="M238 357c1-3 3-5 5-7 1 1 1 3 1 5h0l-3 3c-1 1-2 2-2 3l-1 1-1 1-1 1-1-1c1 0 2-1 2-2l1-1 1-1 1-1 2-3h0c-1 1-2 1-4 2h0z" class="R"></path><path d="M567 263c1 1 2 2 4 3 1 0 2 1 3 2l-1 1c-1 0-2-1-3 0h0c2 3 2 6 1 9-1-1 0-4-1-5-1-2-1-4-2-5h0v-1c0-1-1-1-2-1v-3h1z" class="e"></path><path d="M505 645v-1c1-1 2 0 4 0 1 0 2 0 3 1h3 3s1 0 1 1v2l-14-3z" class="v"></path><path d="M376 93l-1-2v-1h2c1 1 5 0 7 0h18 8c-1 1-26 1-29 1 0 0 0 1 1 1h-4c1 1 2 2 3 4h-2l-2-2s0-1-1-1z" class="B"></path><path d="M518 484c0 2 0 4 1 6-1 1-1 2-1 3h1c0-1 1-1 1-2v-1c0 5-3 9-5 13v-1c2-5 1-10 1-15v2h1c0-1 0-3 1-5z" class="G"></path><path d="M517 492h1v2h-1v-2z" class="q"></path><path d="M536 427c0 3 0 5 1 8s4 7 6 10l-1 1c-2-3-3-6-6-8l1-1c-1-2-3-4-4-5v-1-1h1v-1l2-2z" class="J"></path><path d="M548 176l1 1c-2 2-4 3-7 5l-1 1 3 1c-1 2-2 3-2 5-1-1-1 0-2 0v-1l2-2c-1 0-2 0-2-1-1 0-1 0-2-1h-1v-2c1 0 1 0 2-1 2-1 5-2 7-3l2-2z" class="K"></path><path d="M550 550c1 0 2-1 4-2h2c0-1 1-1 1-1h1c1-1 3-1 5-1 1 0 2 1 3 1-7 0-13 2-18 7-2 2-3 4-4 5 1-4 3-7 6-9h0z" class="v"></path><path d="M544 352v-11h0l1 2v1 1 8l3 19h0c-2-4-3-10-4-14v-6zm-1-277h1c1-1 1-3 3-5l-4 8c0 1-1 3 0 4 0 1 1 3 1 4l3 8-1 1c0-3-1-5-3-7 0 0 0 1-1 1s-1-1-2-1l1-1c0-1-1-2-1-4l3-8z" class="a"></path><path d="M540 83c2 2 3 3 3 5 0 0 0 1-1 1s-1-1-2-1l1-1c0-1-1-2-1-4z" class="C"></path><path d="M502 109c3-3 6-5 10-8l7-6 1 1c-2 2-4 3-6 5l-10 8-9 8c1-3 5-4 6-6-2 1-4 3-7 3v1l1-2 5-3h1v-1h1z" class="I"></path><path d="M570 242h0l1-1c1 0 1 0 2 1h-2 0v1c1 1 3 3 4 5l6 6c2 1 4 3 5 5 1 1 2 2 1 2l-2-2c-2-3-7-8-11-9v1h0l-4-4 1-1v-1c-1-1-1-2-1-3z" class="l"></path><path d="M581 254l-6-6c-1-2-3-4-4-5v-1h0 2c2 0 5 2 6 3 0 1 1 2 1 3s0 3 1 4h0l1 1-1 1z" class="s"></path><path d="M565 287c1 2 1 4 1 6v4c0 1 1 2 2 2l4 6c4 4 6 9 9 14h-1c-4-8-9-14-14-21 0 2 0 5-1 7v1-4c0-3 0-4-2-6h0 2v-1c0-2-1-2-2-3v-1h1 1v-1h-1l-1 1v-1-1h0l2-2z" class="w"></path><path d="M538 378c1 0 2 0 3 1h0c2 3 5 5 8 8h0c3 3 6 6 8 10 1 1 1 2 2 3h-1 0c-1-3-3-4-5-6-4-5-8-8-13-12h0-1c-1-1-1-3-1-4z" class="c"></path><path d="M577 525c7 1 11 3 16 6l-1 1 1 1h0c1 2 2 3 3 5h0c-5-6-11-9-18-11-2-1-4-1-5-1v-1h4z" class="M"></path><path d="M525 475l1-1 2 2c0 1 2 2 3 3 0 0-1 0-1 1s2 5 2 7c1 2 1 5 2 7v3c-1-2-2-4-2-6l-6-9c0-1 1-1 2-1h0c0-1-1-4-2-5l-1-1z" class="l"></path><path d="M542 331c2 2 2 5 3 6h1 0 1c0 2 0 3 1 5 2 5 4 7 8 11-5-2-8-4-10-9l-1-1v2-1-1l-1-2h0v11c-1-4-1-8-1-12 1 0 1-2 0-2v-3c-1-1-1-2-1-4z" class="J"></path><path d="M374 91h0c-1-2-2-3-3-4-2-1-5-3-7-5-1-1-1-2-2-4l-4-6h0c2 1 3 3 5 5 3 3 6 5 9 7 2 2 3 4 5 6h-2v1l1 2h-1l-1-2z" class="c"></path><path d="M501 510c0-2 0-4 1-5 0-2 0-2 1-3 1 0 1 0 1 1l1-1-1-1v-2h1c1 1 1 2 1 3l-1 3v-2c0 1-1 1-1 1-1 3-1 6-1 8-1 3-1 6-1 8 1 3 2 5 2 7l3 6h0c-1-1-2-3-3-4-1-2-2-7-3-7 0-1 0-1-1-1h0c-1-4 0-7 1-11z" class="m"></path><path d="M243 405h1v5-1c0-1 0-2 1-4v15 7c-1 2-1 4-1 6 0 5 1 10 0 15-1-2 0-5 0-7l-1-13c0-2 1-5 0-7-3 6-7 12-13 16 3-3 5-6 8-9 5-7 5-15 5-23z" class="k"></path><path d="M574 268l15 6 3 2c2 1 5 3 8 4 7 5 14 10 20 16 2 2 4 4 6 7h0c-10-12-25-22-40-29-5-2-9-3-13-5l1-1z" class="j"></path><path d="M562 283v-1c-1 0-1-1-1-2h0 1c0 1 1 1 1 2h1l1-1 1 2 1-1v-1h1c1 1 2 3 4 3h0c-1 1 0 2 0 3 1 3 0 7-1 10v-1c0-3-1-5-2-7 0-1-2-3-3-4h-1v2l-2 2v-1c0-1 0-3-1-3v-2z" class="G"></path><path d="M567 284v-1h1c1 1 2 2 3 4 0 1 0 1 1 2v1h-1l-1-1c-1-2-1-4-3-5z" class="O"></path><path d="M562 283v-1c-1 0-1-1-1-2h0 1c0 1 1 1 1 2h1l1-1 1 2 1-1v-1h1c1 1 2 3 4 3h0c-1 1 0 2 0 3h-1c-1-2-2-3-3-4h-1v1h0l-2-1h0-2 0c0 1 1 2 2 2v2l-2 2v-1c0-1 0-3-1-3v-2z" class="i"></path><path d="M530 371c1 0 3 1 4 1l2 3c0 1 1 2 2 3 0 1 0 3 1 4h1l1 4v2c0 1 2 4 3 5-3 0-5-3-6-4-1-2-1-4-2-6s-1-4-3-6v1h0l-3-7z" class="x"></path><path d="M518 476h1s1 1 1 2c0-2-1-4-1-5l3 3 2-1c1 1 1 2 2 1 1 1 2 4 2 5h0c-1 0-2 0-2 1-2-3-3-5-6-7 1 3 3 5 4 8 3 7 4 15 1 22v1c-1-1 1-5 1-7 0-6-1-14-6-19 0 1 1 3 1 4 0 2 0 4-1 6v1c0 1-1 1-1 2h-1c0-1 0-2 1-3h0c1-3 0-4 1-6 0-2-1-6-2-8z" class="m"></path><path d="M556 530c3-1 6-3 9-3v1c1 1 2 0 3 1h1l-2 1-3 1c-2 1-3 1-4 2-3 1-6 3-8 5l-3 2h-1v-1c1-2 4-4 6-6 1-1 1-2 2-3z" class="y"></path><path d="M556 530c3-1 6-3 9-3v1c1 1 2 0 3 1-5 0-9 1-14 4 1-1 1-2 2-3z" class="E"></path><path d="M593 531c8 7 12 14 15 24-1-1-1-1-1-3-1 2 0 4-1 6-1-2-1-3-1-4-1-1-1-2-1-4l-5-7-1-2c0-1-1-2-2-3-1-2-2-3-3-5h0l-1-1 1-1z" class="B"></path><path d="M593 533c2 0 4 2 5 4 3 3 7 9 6 13l-5-7-1-2c0-1-1-2-2-3-1-2-2-3-3-5z" class="G"></path><path d="M559 207c2 0 4 0 6 1 0 0 2 1 3 0 5 2 8 6 12 9 2 3 5 6 7 10 1 1 1 3 2 5h-1c-1-1-2-5-4-7-4-6-10-11-17-14-1-1-4-2-6-2l-1 1h-1c-2-1-3-1-5-1 0 0-1 0-1-1 2-1 3 0 5 0l1-1z" class="Q"></path><path d="M521 462c1 1 4 3 4 5h1c1 0 3 3 4 4l2 2c1 2 2 4 3 5h1c1 3 2 10 2 13h0c-1-1-3-5-3-6-1-2-3-4-4-6-1-1-3-2-3-3v-1c1-1 0-1-1-2 0-2-1-2-2-4-1-1-1-2-2-3h0v-1c-1-1-1-2-2-2v-1z" class="P"></path><path d="M521 462c1 1 4 3 4 5h1c2 4 9 15 9 18-1-2-3-4-4-6-1-1-3-2-3-3v-1c1-1 0-1-1-2 0-2-1-2-2-4-1-1-1-2-2-3h0v-1c-1-1-1-2-2-2v-1z" class="D"></path><path d="M145 640h1c2 4 4 9 5 14h26 20c2 0 5-1 7 0h0 0 11 6c-2 1-6 1-9 1h-15-47l-5-15z" class="B"></path><path d="M564 278c1 1 1 1 2 1l1 1h1c9 6 17 15 24 23 0 1 1 2 2 2 0 1 0 1 1 1 4 7 8 15 11 22 3 12 5 26-1 36h0c4-8 4-18 2-27-4-18-14-33-27-46-3-2-5-5-8-7h0c-2 0-3-2-4-3h-1v1l-1 1-1-2-1 1h-1c0-1-1-1-1-2h-1v-1c1 1 2 1 3 0v-1z" class="Q"></path><path d="M532 431l1 1c1 1 3 3 4 5l-1 1h0c0 1-1 1 0 2s1 2 2 3c1 2 3 4 3 7l1 1c0 2 0 3 1 4v1l-1 1c-3-3-6-9-9-13 0-1 0-1 1-2h1l-1-1c-1-2-3-3-4-4l-2-2c0-1-1-1-1-2l2-1 1 1 1-1 1-1z" class="G"></path><path d="M532 431l1 1c1 1 3 3 4 5l-1 1h0c-1-1-2-1-2-1-2-1-3-2-4-4l1-1 1-1z" class="v"></path><path d="M533 444c0-1 0-1 1-2l2 2v-1h0l2 2v1c2 2 2 7 5 9v1l-1 1c-3-3-6-9-9-13z" class="s"></path><path d="M144 638c-1-2-1-40 0-41h0c1 1 1 2 2 2h1 4l-2 1v1h-1v1h0 0c1 3 1 6 1 8-1 1-1 2-1 3v-1-2 6l-1 1c0 1 0 2-1 4-1 0-1 1-2 2v15z" class="O"></path><path d="M147 599h4l-2 1v1h-1v1h0 0c1 3 1 6 1 8-1 1-1 2-1 3v-1-2c-1-3-1-7-1-11zm-1 22l-1-1c0-6-1-13 1-19 0 4 0 7 1 11 0 1-1 3 0 4v1c0 1 0 2-1 4z" class="V"></path><path d="M569 257l2 2v-1l6 5c0 1 7 6 9 7l4 2-1 1h0v1l-15-6c-1-1-2-2-3-2 0-3-2-4-4-6h1l3 2 1-1c-1-1-2-1-4-2h0l1-2z" class="t"></path><path d="M569 257l2 2v-1l6 5c0 1 7 6 9 7l4 2-1 1c-1-1-3-2-4-3l-9-6-5-2 1-1c-1-1-2-1-4-2h0l1-2z" class="q"></path><path d="M569 257l2 2c1 0 1 1 2 1l1 2c1 0 1 1 2 2l-5-2 1-1c-1-1-2-1-4-2h0l1-2z" class="H"></path><defs><linearGradient id="A" x1="539.697" y1="390.001" x2="554.065" y2="411.629" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M538 389c1 1 3 4 6 4 1 1 2 5 4 5l2 1c1 1 1 2 2 3 2 4 5 9 6 14 0 1 1 2 0 2v-1c-3-7-8-11-13-16l-3-3h-1 0l-4-9h1z"></path><defs><linearGradient id="C" x1="558.213" y1="209.629" x2="570.073" y2="223.72" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#484847"></stop></linearGradient></defs><path fill="url(#C)" d="M552 208c1 1 1 1 2 1 2 0 3 0 5 1h1c6 2 9 5 13 9 1 2 3 4 4 7l2 4h-1c-1-1-1-3-2-5-4-4-11-8-16-10h-2-1-2 0c-1-1-2-1-3-1 0-1-1-1-2-1v-1h1 1c2 1 2 0 4 0l-2-1c-1 0-2-1-3-1l1-2z"></path><path d="M554 211c1-1 3 0 5 0l1 1h0v1c-1 0-3-1-4-1l-2-1z" class="U"></path><path d="M552 208c1 1 1 1 2 1 2 0 3 0 5 1v1c-2 0-4-1-5 0-1 0-2-1-3-1l1-2z" class="M"></path><path d="M244 355h1l-1 5v6-3c-1 0-1 1-1 2v-2l-3 16h0c-1 2-1 3-2 5h0v-2c0-1 0-2-1-3h0l-1-3c0-2 1-4 1-6v-1c0-2 2-6 3-8-2 2-4 6-5 8 0 2-1 4-1 6-1 1 0 3-1 4v-1-1c0-3 1-5 2-7 0-3 0-4 1-6l1-1 1-1 1-1c0-1 1-2 2-3l3-3z" class="W"></path><path d="M237 379c0-2 0-3 1-5 0-2 1-4 2-5 0-2 1-5 3-6l-3 16h0c-1 2-1 3-2 5h0v-2c0-1 0-2-1-3z" class="L"></path><path d="M542 76l1-1-3 8c0 2 1 3 1 4l-1 1-2-1c-2-1-5 1-7 2l-27 22v-2l10-8c2-2 4-3 6-5l-1-1c2-1 4-3 6-4 5-4 8-8 10-14h0c0 2-1 5-2 7 4-2 6-5 9-8z" class="E"></path><path d="M542 76l1-1-3 8c0 2 1 3 1 4l-1 1-2-1c-2-1-5 1-7 2l-1-1c0-1 6-5 7-5 2-2 4-5 5-7z" class="Z"></path><path d="M238 382v2h0c1-2 1-3 2-5h0 0 1c1 1 1 7 0 9l1 9c0 2 0 4 1 5-1 1-1 2-2 2l-2 10c-1 4-3 6-7 8l3-3c2-2 4-7 3-10v-10c0-5-1-9-2-14l1-1 1-2z" class="Q"></path><path d="M241 404c-1 1-1 4-2 5v-8c0-7 0-14 2-21v7 1l1 9c0 2 0 4 1 5-1 1-1 2-2 2z" class="N"></path><path d="M520 430c1 0 2 0 3 1s2 3 2 4h3l2 2c1 1 3 2 4 4l1 1h-1c-1 1-1 1-1 2 3 4 6 10 9 13l1 1h-1l-4-5h0v3c-1-1-2-2-2-3-1-2-3-3-5-5-1-1-4-4-5-6h0l-1-3-3-3c-1-2-2-3-2-4v-1-1z" class="B"></path><path d="M520 430c1 0 2 0 3 1s2 3 2 4v1h1v1c-2-1-4-4-6-6v-1z" class="E"></path><path d="M525 435h3l2 2h-2c0 1 2 3 3 4v1l-5-5v-1h-1v-1z" class="p"></path><path d="M530 437c1 1 3 2 4 4l1 1h-1c-1 1-1 1-1 2l-2-2v-1c-1-1-3-3-3-4h2z" class="T"></path><path d="M525 439c4 3 9 7 11 11 1 1 2 2 2 3h0v3c-1-1-2-2-2-3-1-2-3-3-5-5-1-1-4-4-5-6h0l-1-3z" class="K"></path><path d="M125 92c5-1 10-1 15-1l30 1 88-1h0c-2 1-5 1-7 1h3c3 0 6 0 9 1h-67l2-1h0-73z" class="B"></path><path d="M504 459l-1-1v-1c1 0 4 2 4 3l3 3h0c2 2 3 5 4 7 0 1 2 2 2 4l2 2c1 2 2 6 2 8-1 2 0 3-1 6h0c-1-2-1-4-1-6-1 2-1 4-1 5h-1v-2c0-1-1-3-2-4h-1v-3l-1-2c0-3-1-5-3-7v-1l2-1-1-1-6-9z" class="W"></path><path d="M511 469c1 3 7 13 7 15-1 2-1 4-1 5h-1v-2c0-1-1-3-2-4h-1v-3l-1-2c0-3-1-5-3-7v-1l2-1z" class="c"></path><path d="M504 459l6 9 1 1-2 1v1c2 2 3 4 3 7l1 2v3c-1 7-1 14-4 21-1-1-1-2-1-4 1-1 0-7 0-9 1 0 2 1 3 0v-2l-1-1v-1h1v-3l-1-1v-1c-1-1-2-5-2-7-1-1-2-2-2-3v-1c-1-1-2-2-2-4h1c1-1-1-3-1-5v-3z" class="B"></path><path d="M513 480h-1 0c-2-1-2-3-3-5l1-1v1l2 3 1 2z" class="f"></path><path d="M504 459l6 9h0c-1 0-2-1-2-2h-1-1c0 1 1 2 1 3 0 0 0 1-1 1v1c-1-1-2-2-2-4h1c1-1-1-3-1-5v-3z" class="r"></path><path d="M557 215h1v1h0l2 2c1 3 6 5 8 7l8 6c4 2 7 4 11 7 6 5 13 11 18 17h0c-6-6-13-12-20-18-3-2-7-4-10-6l-2-1-1 1h-1 0v1c-2 0-3-1-5-2l-4-2c-2-2-4-3-6-4h1l1-1c-1 0-3-1-5-1 3 0 6-1 9 0h1-1c-1-1-2-1-2-1-1-1-1 0-1-1l-3-1c1 0 1-1 2-1l-1-3z" class="S"></path><path d="M558 223c5 0 11 4 16 7l1 1-2-1-1 1h-1 0v1c-2 0-3-1-5-2l-4-2c-2-2-4-3-6-4h1l1-1z" class="g"></path><path d="M564 226c2 1 6 3 7 5v1c-2 0-3-1-5-2 1 0 1 0 1-1l-3-3z" class="j"></path><path d="M558 223c2 1 4 1 6 3l3 3c0 1 0 1-1 1l-4-2c-2-2-4-3-6-4h1l1-1z" class="C"></path><path d="M563 230l-1-2h0l4 2c2 1 3 2 5 2v-1h0 1c2 2 4 4 5 6 2 3 3 7 5 9-1 0-1-1-1 0v6h0c-1-1-1-3-1-4s-1-2-1-3c-1-1-4-3-6-3-1-1-1-1-2-1l-1 1h0-1c1-1 1 0 1-1-1-1-2-3-4-4s-3-1-4-3l1-1 2 1h0l-3-3 1-1z" class="K"></path><path d="M562 231l1-1c3 2 6 3 9 6-2 0-3-1-4 0-1-1-2-1-3-2l-3-3z" class="H"></path><path d="M568 236c1-1 2 0 4 0 3 2 5 5 7 8-3-2-6-3-8-5l-3-3z" class="X"></path><path d="M442 648c1 1 2 1 4 1h0c-1 2-1 2-1 3v-2h-1c0 2 1 2-1 3v1h1-4v1c3 0 7-1 10 0l-77-1h17c3 1 7 0 10 0 0 1 0 0 1 0-1 0-2 0-2-1h2l14 1 1-1 1-1 3-1v1c2 0 3 0 5-1v1c2-1 3-1 5-1 0 1 1 1 2 1s2-1 3-2c1 0 2-1 3 0h1 0v1h1c0-1 1-2 2-3z" class="J"></path><path d="M420 652c2 0 3 0 5-1v1l-2 1c2 1 6 0 9 1h-6-11l1-1 1-1 3-1v1z" class="G"></path><path d="M442 648c1 1 2 1 4 1h0c-1 2-1 2-1 3v-2h-1c0 2 1 2-1 3v1h-10c1-1 2-1 3-1 1-1 2-1 3-3h0v1h1c0-1 1-2 2-3z" class="H"></path><defs><linearGradient id="D" x1="430.055" y1="654.429" x2="431.5" y2="648.948" xlink:href="#B"><stop offset="0" stop-color="#bfbebf"></stop><stop offset="1" stop-color="#e2e0e2"></stop></linearGradient></defs><path fill="url(#D)" d="M435 650c1 0 2-1 3 0h1c-1 2-2 2-3 3-1 0-2 0-3 1h-1c-3-1-7 0-9-1l2-1c2-1 3-1 5-1 0 1 1 1 2 1s2-1 3-2z"></path><path d="M258 91h11 21 66c0 1 0 1-1 1l-68 1h-17-7c-3-1-6-1-9-1h-3c2 0 5 0 7-1h0z" class="y"></path><path d="M548 187h2c6-2 12-6 17-10 3-2 5-4 7-7 1-2 2-4 2-6 1-1 0-2 1-3h0v3c-1 4-3 7-5 11l-2 2s0 1-1 1c-4 6-9 10-15 13 3 1 8 3 11 5h0l-12-5v1c-2 1-4 1-6 2l6 2c1 1 1 2 2 2-2 0-3-1-4-1-2 0-3-2-5-2-1 0-3 1-4 1v-1c1-1 1-2 2-3h-1c0-1 2-1 2-1 1-1 2-1 3-2l-1-1v-1h1z" class="U"></path><path d="M549 189h1l2 2c-1 1-3 1-5 2h-2v-1c1-1 3-1 4-2v-1z" class="N"></path><path d="M569 178c-1 0-6 6-7 7-3 2-5 4-9 5-1 0-2-1-3-2 2-1 3-1 4-2 3-1 5-2 7-3 1-1 3-2 5-4 1-1 3-4 6-4l-2 2s0 1-1 1z" class="s"></path><path d="M410 90h55c10 0 21 1 31 0l3 10-1 1-3-9c-1-1-104 0-113 0-1 0-1-1-1-1 3 0 28 0 29-1z" class="y"></path><defs><linearGradient id="E" x1="531.082" y1="396.116" x2="550.285" y2="408.392" xlink:href="#B"><stop offset="0" stop-color="#0f1112"></stop><stop offset="1" stop-color="#332f2f"></stop></linearGradient></defs><path fill="url(#E)" d="M539 406v-3l-1-1v-2c0-1-1-3-1-4v-1c-1-2-1-3-1-4v-2h0 1l4 9h0c3 8 5 17 8 25 1 1 2 3 2 5-1 0-1 1-1 0-2 0-3-2-5-3h0l-1-1c-2-4-3-8-4-13l-1-4h0v-1z"></path><path d="M539 407c1 1 2 3 3 5l7 11h0c1 1 2 3 2 5-1 0-1 1-1 0-2 0-3-2-5-3h0l-1-1c-2-4-3-8-4-13l-1-4z" class="t"></path><path d="M540 411v1c4 5 4 11 9 16h1l1-1-2-4h0c1 1 2 3 2 5-1 0-1 1-1 0-2 0-3-2-5-3h0l-1-1c-2-4-3-8-4-13z" class="h"></path><path d="M513 438l1-1 1 3c3 2 6 5 10 7 3 3 6 6 9 7 2 1 3 3 4 4 1 0 3 3 4 4 2 2 3 4 5 5 3 3 6 4 8 6-5-1-10-5-13-9-4-3-8-7-12-9l-1-1s-1-1-2-1v2l-1-1-4-2h0c0-1-1-1-1-1h-2c-1 0-2-2-3-2l-3-3c-1-1-2-2-3-2l1-1c1-1 1-2 1-4h0c1 0 1 0 1-1z" class="U"></path><path d="M520 448c1 1 3 2 4 3h1c0 1 1 1 1 2v1l-4-2h0c0-1-1-1-1-1h-2l1-1v-2z" class="a"></path><path d="M513 438l1-1 1 3c-1 1 0 1 0 3l5 5v2l-1 1c-1 0-2-2-3-2l-3-3c-1-1-2-2-3-2l1-1c1-1 1-2 1-4h0c1 0 1 0 1-1z" class="z"></path><path d="M513 438l1-1 1 3c-1 1 0 1 0 3-1-1-1-2-2-3l-1-1h0c1 0 1 0 1-1z" class="Q"></path><path d="M567 526h6c1 0 3 0 5 1 7 2 13 5 18 11h0c1 1 2 2 2 3l1 2h-2c-1-2-3-2-5-3 1 2 2 3 3 4l-1 1c-2-2-3-3-6-5l-3-3c-2-1-3-3-5-4-1-1-3-1-4-1-4-1-7-1-11 0l-1-1 3-1 2-1h-1c-1-1-2 0-3-1v-1h1l1-1h0z" class="W"></path><path d="M567 530c5 0 10-1 14 3h-1c-1-1-3-1-4-1-4-1-7-1-11 0l-1-1 3-1z" class="n"></path><path d="M567 526h6c1 0 3 0 5 1l-1 1 1 2-1-1h-3c-1 0-1-1-2-1s-2 0-3 1h-1c-1-1-2 0-3-1v-1h1l1-1h0z" class="Q"></path><path d="M565 527h1 3c2 1 3 1 4 0 2 0 3 0 4 1l1 2-1-1h-3c-1 0-1-1-2-1s-2 0-3 1h-1c-1-1-2 0-3-1v-1z" class="J"></path><path d="M581 533h1l1 1 4 3c1 1 3 4 5 4-1-1-1-2-2-3l-5-5c4 1 5 3 8 5l3 3h2l1 2h-2c-1-2-3-2-5-3 1 2 2 3 3 4l-1 1c-2-2-3-3-6-5l-3-3c-2-1-3-3-5-4h1z" class="S"></path><defs><linearGradient id="F" x1="579.668" y1="534.327" x2="593.506" y2="532.044" xlink:href="#B"><stop offset="0" stop-color="#797676"></stop><stop offset="1" stop-color="#969696"></stop></linearGradient></defs><path fill="url(#F)" d="M578 527c7 2 13 5 18 11h0c1 1 2 2 2 3h-2l-3-3c-3-2-4-4-8-5l-7-3-1-2 1-1z"></path><path d="M531 404h1 0c0 1 0 1 1 2h1l1 2s1 1 1 2l1 4c2 4 3 7 5 11 2 3 3 6 5 8h0c2 1 2 2 2 4 1 1 2 3 3 4-7-5-12-13-17-19l1 5-2 2v1h-1c-1-2-1-4-1-6s-1-3-1-4c-2-4-2-7-3-10l1-1c1 0 2 1 3 1-1-1-1-2-1-4h0v-2z" class="M"></path><path d="M533 406h1l1 2s1 1 1 2c-1 0-1 0-2 1v1c-1-2-1-4-1-6z" class="h"></path><path d="M534 412v-1c1-1 1-1 2-1l1 4-2 1-1-3z" class="e"></path><path d="M528 410l1-1c1 0 2 1 3 1 0 2 1 4 1 6-1 2 0 4 1 5s0 1 0 2l-1 2c0-1-1-1-1-1 0-2-1-3-1-4-2-4-2-7-3-10z" class="v"></path><path d="M535 415l2-1c2 4 3 7 5 11 2 3 3 6 5 8h0c2 1 2 2 2 4-2-1-4-4-6-6-3-5-6-10-8-16z" class="G"></path><path d="M491 505l1-1h4 0l1 2h1c0 1 0 2 1 3l2 1c-1 4-2 7-1 11h0l1 3c0 1-1 3-1 3l1 11h-1l-1-1h0 0c0 1 0 1-1 2v2c0-3-2-6-3-10l-2-10c0-5-1-9-2-14l1-1-1-1z" class="Q"></path><path d="M497 528c1 2 1 4 1 6v2h0c-1-1-1-2-2-4h1v-3-1z" class="x"></path><path d="M494 512c1 2 2 4 2 6 1 0 0 0 0 1v1h-2v-8z" class="a"></path><path d="M494 520h2l1 8v1 3h-1c-1-4-2-8-2-12z" class="z"></path><path d="M496 519c1 1 1 4 2 5h1l1 1s0-1 1-1c0 1-1 3-1 3-1 1-1 2-1 4 0 1 0 2-1 3 0-2 0-4-1-6l-1-8v-1z" class="k"></path><path d="M491 505l1-1h4 0l1 2h1c0 1 0 2 1 3l2 1c-1 4-2 7-1 11h0l1 3c-1 0-1 1-1 1l-1-1h-1c-1-1-1-4-2-5 0-1 1-1 0-1 0-2-1-4-2-6s-1-4-2-6l-1-1z" class="y"></path><path d="M502 493c1-1 3-2 3-3 1-1 1-2 2-3v1l1 1v2c0 2 1 8 0 9 0 2 0 3 1 4-2 6-3 13 0 18 1 2 3 3 4 5-3-2-6-5-8-9-1-4 0-8 0-13l1-3c0-1 0-2-1-3h-1v2l1 1-1 1c0-1 0-1-1-1-1 1-1 1-1 3-1 1-1 3-1 5l-2-1c-1-1-1-2-1-3h-1l-1-2-1-4h0 1l-1-3 1-1c0-1 0-3-1-4h2v1l2-1h0v2l1 1c1-1 1-2 1-3 0 1 0 2 1 3v-2z" class="a"></path><path d="M498 506l2-1v1c0 1 0 2-1 3-1-1-1-2-1-3z" class="E"></path><path d="M499 492v2l1 1c1-1 1-2 1-3 0 1 0 2 1 3 0 0-1 1-1 2s1 1 0 2h-1 0v1h1v3c0 1-1 1-1 2v1-1l-2 1h-1l1-1c0-2 1-3 1-5 0-1-1-2-1-3s1-3 1-5h0z" class="J"></path><path d="M497 492v1l2-1c0 2-1 4-1 5s1 2 1 3c0 2-1 3-1 5l-1 1-1-2-1-4h0 1l-1-3 1-1c0-1 0-3-1-4h2z" class="o"></path><path d="M495 497l1-1c1 1 1 2 1 4v1l-1-1-1-3z" class="Q"></path><defs><linearGradient id="G" x1="186.567" y1="591.526" x2="175.248" y2="600.174" xlink:href="#B"><stop offset="0" stop-color="#b8b5b7"></stop><stop offset="1" stop-color="#d8d9d9"></stop></linearGradient></defs><path fill="url(#G)" d="M170 598c0-1 1-1 1-2h-7v1c-2 0-3 0-4-1h1 3c2 0 4-1 6-1 8-1 16-2 24-4s15-5 23-9v1l-5 3 1 1-8 4c-4 0-8 2-12 3l-2 2c-1 0-4 0-5 1h0c2 1 5-1 8 0l-2 2c-4 1-9 2-13 2h-10c1 0 1 0 2-1h0l-1-1c-1 1-2 1-3 1 0-1 0-1-1-2h4z"></path><path d="M212 586l1 1-8 4c-4 0-8 2-12 3-3 0-6 1-9 1v-1c3-1 6-1 10-2 6-1 12-3 18-6z" class="P"></path><defs><linearGradient id="H" x1="183.444" y1="591.956" x2="177.134" y2="603.974" xlink:href="#B"><stop offset="0" stop-color="#929193"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#H)" d="M170 598c3 0 6 0 9-1h7 0c2 1 5-1 8 0l-2 2c-4 1-9 2-13 2h-10c1 0 1 0 2-1h0l-1-1c-1 1-2 1-3 1 0-1 0-1-1-2h4z"></path><path d="M551 199c1 0 5 0 5 1h3v1l1 2c2 1 3 1 4 3h0c2 1 3 1 4 2-1 1-3 0-3 0-2-1-4-1-6-1l-1 1c-2 0-3-1-5 0 0 1 1 1 1 1-1 0-1 0-2-1l-1 2-6-1-3 1h-4v1c-1-1-2-1-3-1h1v-1h-2v-1h-1c1-1 1-1 2-1 0 0 1 0 2 1h0c-1-1-2-2-2-3h2v-1h1-1c0-1 1-1 1-1 2 0 4-1 5-2h2c2-1 4-1 6-2z" class="v"></path><path d="M558 202l2 1c2 1 3 1 4 3h0l-6-2v-2z" class="T"></path><path d="M549 207c4-1 7-1 10 0l-1 1c-2 0-3-1-5 0 0 1 1 1 1 1-1 0-1 0-2-1-1 0-2 0-3-1z" class="J"></path><path d="M543 201h2l1 1c1 0 1 0 2-1 1 1 1 1 2 1h1v-1c1 1 1 1 1 2h3c-4 0-6 0-9 1h-5c-1 0-3 1-4 1v-1h1-1c0-1 1-1 1-1 2 0 4-1 5-2z" class="R"></path><path d="M533 208c1-1 1-1 2-1 0 0 1 0 2 1h0c1 0 2-1 3 0h0 5l4-1c1 1 2 1 3 1l-1 2-6-1-3 1h-4v1c-1-1-2-1-3-1h1v-1h-2v-1h-1z" class="J"></path><path d="M538 210v-1h1c1 0 2 0 3 1h-4z" class="R"></path><path d="M545 208l4-1c1 1 2 1 3 1l-1 2-6-1c1 0 2 0 3-1h0-3z" class="D"></path><path d="M551 199c1 0 5 0 5 1h3v1l1 2-2-1v2c-1 0-2-1-3-1h-3c0-1 0-1-1-2v1h-1c-1 0-1 0-2-1-1 1-1 1-2 1l-1-1c2-1 4-1 6-2z" class="e"></path><path d="M556 200h3v1l1 2-2-1-2-1h0v-1z" class="L"></path><path d="M160 200l1 1 2-2v1l-7 8c-7 7-12 16-16 25-1 5-3 9-4 14l-4 14v-1h0c-1 2-1 5-2 7v5h-1v-1-1-4c2-4 1-9 2-13v-2c1-1 1-2 1-3v-2c1-1 1-1 1-2s0-1 1-2v-5l1-3 2-5c1-1 1-3 2-5s2-3 3-5l1 1-2 4h1c1-1 2-3 3-5 1-3 3-6 6-8v-1l1-1 1-1c0-1-1 0 0-1 1 0 1 0 1-1h1l4-4v-1l1-1z" class="C"></path><path d="M142 219l1 1-2 4v1l-4 8-1 1h-1l2-5c1-1 1-3 2-5s2-3 3-5z" class="N"></path><path d="M557 288l5 6h0c0 1 0 1 1 2 2 2 2 3 2 6v4c0 1-1 1-2 2v1 3c-1 1 0 2-1 3v2c0 1-1 1-1 2 6 8 11 16 14 26 2 6 2 13 2 20v5c0-15-3-28-10-41l-6-9c-1 1-1 2-2 3 0-1 1-3 1-4-1-1-1-2-1-2l-1-1h1l-1-1v-1c-1-1-2-3-3-5 0-1 0-1-1-2 0-1 0-5 1-6h1v5c1 0 1 0 1-1 0-2 0-5 1-7v-1-1h0v-1c0-2-1-3-2-4 0-2 0-2 1-3z" class="o"></path><path d="M560 306c0-2 1-4 0-7 2 2 2 5 2 6v2l-1-1h-1 0z" class="F"></path><path d="M560 306h1l1 1c0 1 0 1-1 2-1 2 0 4-3 6v-1l1-1c1-2 1-5 1-7zm3 3v3c-1 1 0 2-1 3v2c0 1-1 1-1 2-1-1-1-2-2-3l1-1c1-2 1-5 3-6z" class="V"></path><path d="M557 305c0-2 0-5 1-7v-1l1 3c1 2 0 5 1 6h0c0 2 0 5-1 7l-1-1h0c1-2 1-6 0-7h-1z" class="H"></path><path d="M563 308c0-3 0-6-1-9 0-2-1-3-1-5h1c0 1 0 1 1 2 2 2 2 3 2 6v4c0 1-1 1-2 2z" class="N"></path><path d="M554 307c0-1 0-5 1-6h1v5c1 0 1 0 1-1h1c1 1 1 5 0 7h0l1 1-1 1c-1-1-2-3-3-5 0-1 0-1-1-2z" class="F"></path><defs><linearGradient id="I" x1="531.235" y1="385.331" x2="541.066" y2="414.731" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#292828"></stop></linearGradient></defs><path fill="url(#I)" d="M528 375c1 1 2 1 3 3v1c1 1 2 2 2 3h0c1-2 0-3 0-4v-1c2 2 2 4 3 6s1 4 2 6h-1-1 0v2c0 1 0 2 1 4v1c0 1 1 3 1 4v2l1 1v3 1h0l1 4c1 5 2 9 4 13l1 1c2 3 4 8 6 11 4 4 9 8 14 11h-1c-5-2-10-6-13-10-2-2-4-8-6-9v1h0c1 1 2 3 2 4h0c-2-2-3-5-5-8-2-4-3-7-5-11l-1-4c0-1-1-2-1-2l-1-2v-1-2h0v-2l-1-1v-1-1c-1-1-1-3-1-4-1 0 0-3-1-4v-1-1s-1 0-1-1h0l-1-5v-1c1-1-1-5-1-6z"></path><path d="M528 375c1 1 2 1 3 3v1c1 1 2 2 2 3h0c1-2 0-3 0-4v-1c2 2 2 4 3 6s1 4 2 6h-1-1 0v2c0 1 0 2 1 4v1c0 1 1 3 1 4v2l1 1v3l-2-7-1-1v-1c-1-2-1-3-1-5-1-2-1-3 0-5v-1c-1 0-1 0-1 1-1-1-1-2-2-2l-2 2-1-5v-1c1-1-1-5-1-6z" class="I"></path><path d="M540 316v4l1 1v-1h1l1 3c0 2-1 3 1 5l-2 2v-1l-1 1 1 1c0 2 0 3 1 4v3c1 0 1 2 0 2-1 1-1 2-1 4s-1 4-1 6c0 9 2 22 8 29l2 3h0c-4-2-6-6-7-10l-4-14c0-1 0-2-1-2v1h-1v1h-1v-1h0l-2-7c0-1 0-3-1-4 0-3-1-5-2-8l1-2c-1 0-1 0-1-1s0-2 1-3c1 2 1 5 2 7l1-2h1c0-2 0-3 1-4v-1c1 0 1-1 2-2v-2-3c-1 0-1-1-2-1v-3h1v-1h1v-4z" class="R"></path><path d="M540 320l1 1v-1c0 3 1 6 1 8l-2 2v-10z" class="D"></path><path d="M538 333v-1c1 0 1-1 2-2v4c1 1 1 3 1 4l-2 3c0-1-1-1-1-2v-3-3z" class="i"></path><path d="M533 332c1 2 1 5 2 7l1-2h1c0-2 0-3 1-4v3 3c0 1 1 1 1 2l2-3-1 17v3c0-1 0-2-1-2v1h-1v1h-1v-1h0l-2-7c0-1 0-3-1-4 0-3-1-5-2-8l1-2c-1 0-1 0-1-1s0-2 1-3z" class="g"></path><path d="M539 349h0c1 2 0 4 1 6v3c0-1 0-2-1-2v1h-1v-7l1-1z" class="T"></path><path d="M538 348v-2h0v4 7 1h-1v-1-2-2l1-5z" class="y"></path><path d="M538 348l-1-12h1v3c0 1 1 1 1 2v8l-1 1v-4h0v2z" class="S"></path><path d="M538 333v3h-1l1 12-1 5h0c-1-2-1-4-1-7 0-2-1-5-1-7l1-2h1c0-2 0-3 1-4z" class="K"></path><path d="M533 332c1 2 1 5 2 7 0 2 1 5 1 7 0 3 0 5 1 7h0v2 2h0l-2-7c0-1 0-3-1-4 0-3-1-5-2-8l1-2c-1 0-1 0-1-1s0-2 1-3z" class="W"></path><path d="M546 266l3-1v-1h0c3 1 5 3 8 5 1 0 2 0 3-1l-2-2v-2c1 1 2 1 3 2v-1c1-1 1-1 2-1 1 1 2 1 3 2h0c1 0 2 0 2 1v1h0c1 1 1 3 2 5 1 1 0 4 1 5v1c9 7 19 16 24 27-1 0-1 0-1-1-1 0-2-1-2-2-7-8-15-17-24-23h-1l-1-1c-1 0-1 0-2-1l-3-2c-2 0-3-1-4-2v-1h0c0-2-9-6-11-7z" class="E"></path><path d="M560 268c2 2 2 3 3 5h0c-2-1-4-3-6-4 1 0 2 0 3-1z" class="B"></path><path d="M568 280h1c7 4 14 9 19 16 1 2 3 4 4 6v1c-7-8-15-17-24-23z" class="s"></path><path d="M561 266v-1c1-1 1-1 2-1 1 1 2 1 3 2h0c1 0 2 0 2 1v1h0c1 1 1 3 2 5 1 1 0 4 1 5v1h-2c-2-2-3-3-5-6v1l-1-1h0c-1-2-1-3-3-5l-2-2v-2c1 1 2 1 3 2z" class="C"></path><path d="M568 268c1 1 1 3 2 5 1 1 0 4 1 5v1h-2v-3c0-2-1-4-2-6 0-1 0-1 1-2z" class="N"></path><path d="M561 266v-1c1-1 1-1 2-1 1 1 2 1 3 2h0c1 0 2 0 2 1v1c-2-1-3-2-4-2v1 2 4h0v1l-1-1h0c-1-2-1-3-3-5l-2-2v-2c1 1 2 1 3 2z" class="i"></path><path d="M558 264c1 1 2 1 3 2h0c1 0 1 1 2 1 0 2 0 4 1 6h0v1l-1-1h0c-1-2-1-3-3-5l-2-2v-2z" class="R"></path><path d="M510 419c0 2 1 3 2 4h1v2l2-1-1-1c0-1 0-1 1-2 1 3 3 6 5 9v1 1c0 1 1 2 2 4l3 3 1 3h0c1 2 4 5 5 6 2 2 4 3 5 5 0 1 1 2 2 3v2c-1-1-2-3-4-4-3-1-6-4-9-7-4-2-7-5-10-7l-1-3-1 1h-1l-1-3-4-4c0-1-1-1-1-1 0-1 0-1 1-1l-1-2v-4h1 1 0c1-1 1-2 1-2l1-2z" class="g"></path><path d="M507 423h1c2 2 5 5 5 7v2c0 1 0 1-1 1v1h0c-2-2-4-3-5-5l-1-2v-4h1z" class="S"></path><path d="M507 423h1c2 2 5 5 5 7v2c0 1 0 1-1 1v-2c0-1-2-2-2-2-2-2-3-4-3-6z" class="T"></path><path d="M513 432c2 1 3 3 5 5s5 5 8 7c1 2 3 5 5 6v1c-2-1-3-3-5-4-4-2-8-6-11-10-1-1-2-3-2-5z" class="M"></path><path d="M513 430l4 4c0 1 1 1 1 2l1-1-1-1h0 1c1 1 1 1 3 2l3 3 1 3h0c1 2 4 5 5 6v1 1c-2-1-4-4-5-6-3-2-6-5-8-7s-3-4-5-5v-2z" class="m"></path><path d="M510 419c0 2 1 3 2 4h1v2l2-1-1-1c0-1 0-1 1-2 1 3 3 6 5 9v1 1c0 1 1 2 2 4-2-1-2-1-3-2h-1 0l1 1-1 1c0-1-1-1-1-2l-4-4c0-2-3-5-5-7h0c1-1 1-2 1-2l1-2z" class="k"></path><path d="M513 425l2-1c1 3 3 6 4 9v1h-1 0l1 1-1 1c0-1-1-1-1-2 0-2-1-5-2-7l-2-2z" class="o"></path><path d="M510 419c0 2 1 3 2 4h1v2l2 2c1 2 2 5 2 7l-4-4c0-2-3-5-5-7h0c1-1 1-2 1-2l1-2z" class="c"></path><path d="M510 419c0 2 1 3 2 4h1v1c-1 1-1 1-2 1v-2l-2-2 1-2z" class="U"></path><path d="M243 329v-6c-3 3-8 5-12 6 4-3 8-5 13-7l-1-49c1-4 1-8 1-11v-22c0-5-1-9 0-14 0 5-1 11 0 16h0l1-1 1-1c1 4 0 9 0 14s1 10 0 16h0c0 1-1 1-1 2h1c1 5 0 11 0 15v26 7 1c-1 4-1 10-1 15v-3c0-3 1-8 0-11-1 3 0 5 0 8v16l-1 2v-1l-1-1-1 1h-1v-1h-1c1-2 1-2 2-3v-1l-1 1v-2h-1l-6 4c1-2 4-4 6-5 1-1 2-1 3-2h0v-7-2z" class="D"></path><path d="M240 341c1-1 2-2 3-2v5l-2 2h-1c1-2 1-2 2-3v-1l-1 1v-2h-1z" class="Z"></path><path d="M245 241l1-1c1 4 0 9 0 14s1 10 0 16h0c0 1-1 1-1 2-1-1 0-5 0-7l-1-23 1-1z" class="P"></path><path d="M245 272h1c1 5 0 11 0 15v26 7 1c-1 4-1 10-1 15v-3c0-3 1-8 0-11-1-9 0-18 0-27 0-8-1-16 0-23z" class="V"></path><path d="M245 322c1 3 0 8 0 11v3c0-5 0-11 1-15v-1 122h0v-6c-1 1-1 3-1 5h0v-4-10-7-15c-1 2-1 3-1 4v1-5h-1v-3c-1-1-1-3-1-5l-1-9c1-2 1-8 0-9h-1 0l3-16v2c0-1 0-2 1-2v3-6l1-5h-1 0c0-2 0-4-1-5 0-1 0-2 1-3v1l1-2v-16c0-3-1-5 0-8z" class="L"></path><path d="M245 346v9h-1 0c0-2 0-4-1-5 0-1 0-2 1-3v1l1-2z" class="S"></path><path d="M243 363v2c0-1 0-2 1-2v3-6l1 32v13c-1 2-1 3-1 4v1-5h-1v-3c-1-1-1-3-1-5l-1-9c1-2 1-8 0-9h-1 0l3-16z" class="I"></path><defs><linearGradient id="J" x1="512.05" y1="448.411" x2="528.121" y2="459.203" xlink:href="#B"><stop offset="0" stop-color="#151516"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#J)" d="M506 430s1 0 1 1l4 4 1 3h1c0 1 0 1-1 1h0c0 2 0 3-1 4l-1 1c1 0 2 1 3 2l3 3c1 0 2 2 3 2h2s1 0 1 1h0l4 2 1 1v-2c1 0 2 1 2 1v1c0 1 1 2 1 3 0 2 1 4 2 5v3 1h0c-1 0-1 1-1 1h-2c1 1 3 4 3 5h0l-2-2c-1-1-3-4-4-4h-1c0-2-3-4-4-5-3-2-5-5-8-8h0l-3-3c0-1 0-1-1-2h-2v-1c-1-1-2-3-4-4l-2-3 1-1c0 1 1 1 2 2v-1-4c-1-1 0-3 0-5h1l1-2z"></path><path d="M527 465c0-1 1-1 1-2 1 1 2 2 4 3h0v1h0c-1 0-1 1-1 1h-2l-2-3z" class="C"></path><path d="M522 458c2 1 5 3 6 5 0 1-1 1-1 2-2-2-5-5-5-7z" class="H"></path><path d="M522 452l4 2 1 1c1 1 0 1 1 2v1 1c-2-2-5-5-6-7z" class="F"></path><path d="M527 455v-2c1 0 2 1 2 1v1c0 1 1 2 1 3 0 2 1 4 2 5-2-1-3-2-4-4v-1-1c-1-1 0-1-1-2z" class="j"></path><path d="M515 453c2 0 5 3 7 5h0c0 2 3 5 5 7l2 3c1 1 3 4 3 5h0l-2-2c-1-1-3-4-4-4h-1c0-2-3-4-4-5-3-2-5-5-8-8h0c2 2 4 3 7 4-2-2-4-3-5-5z" class="R"></path><path d="M506 430s1 0 1 1l4 4 1 3h1c0 1 0 1-1 1h0c0 2 0 3-1 4l-1 1c1 0 2 1 3 2-1 0-1 0-1 1 0 0-1 0 0 1 0 0 1 1 1 2h-1l1 1 1 1 1 1c1 2 3 3 5 5-3-1-5-2-7-4l-3-3c0-1 0-1-1-2h-2v-1c-1-1-2-3-4-4l-2-3 1-1c0 1 1 1 2 2v-1-4c-1-1 0-3 0-5h1l1-2z" class="W"></path><path d="M502 440c0 1 1 1 2 2 2 2 4 5 5 7h-2v-1c-1-1-2-3-4-4l-2-3 1-1z" class="m"></path><path d="M506 430s1 0 1 1l4 4 1 3h1c0 1 0 1-1 1h0c0 2 0 3-1 4l-1 1h0c-1-1-1-2-1-3l-3-9-2 5c-1-1 0-3 0-5h1l1-2z" class="R"></path><path d="M508 435c-1-1-1-2-1-2v-2l4 4-1 2v-2c-1 0-1 0-2-1h0v1z" class="B"></path><path d="M508 435v-1h0c1 1 1 1 2 1v2h0c1 2 1 3 1 5h0l-3-7z" class="I"></path><path d="M468 637c0 1 0 1-1 2h3l2 1c-1 2-1 4-2 5l-1-1-1 2 1 1v1c-1 1-1 3-1 5 0 1 0 2-1 2h-12-5c-3-1-7 0-10 0v-1h4-1v-1c2-1 1-1 1-3h1v2c0-1 0-1 1-3h0c0-1 1-3 2-4-1-1-1-1-1-2h0l1-2v-1h2l-2-1h18v-1h1l1-1z" class="C"></path><path d="M455 644h1c0 2 0 2-1 3h0c-1-1-1-1-1-2l1-1z" class="F"></path><path d="M458 641l4 5c0 1 0 2-2 3 0-1 0-2-1-3 0-2-1-3-2-4l1-1z" class="y"></path><path d="M462 646l1 2s0 1 1 1h2c-1 0-1 1-1 1h-1v1c-1 1-3 1-5 1v-1l1-2c2-1 2-2 2-3z" class="W"></path><path d="M450 640h8v1l-1 1-1-1c-1 1 0 2-1 3 0-1 0-3-1-3h-1l1 2h-1-2v1l-3-3v-1h2z" class="d"></path><path d="M448 641l3 3h0c1 2 2 3 4 3h2 1 0c-1 2-2 2-4 2l-1-1c-1 1-1 2-2 2-2 2-3 2-5 2h-1c0-1 0-1 1-3h0c0-1 1-3 2-4-1-1-1-1-1-2h0l1-2z" class="I"></path><path d="M451 648v-1c1 0 1 1 2 1-1 1-1 2-2 2v-2z" class="C"></path><path d="M446 649c1 1 2 1 3 1s1-1 2-2v2c-2 2-3 2-5 2h-1c0-1 0-1 1-3z" class="L"></path><path d="M468 646l1 1v1c-1 1-1 3-1 5 0 1 0 2-1 2h-12-5c-3-1-7 0-10 0v-1h4-1v-1c2-1 1-1 1-3h1v2h1c2 0 3 0 5-2 1 0 1-1 2-2l1 1-2 1 2 2h0c2 0 3 1 5 1 1 1 3 0 4 1h3c1-2 2-6 2-8z" class="W"></path><path d="M453 648l1 1-2 1 2 2h0c2 0 3 1 5 1 1 1 3 0 4 1h-19-1v-1c2-1 1-1 1-3h1v2h1c2 0 3 0 5-2 1 0 1-1 2-2z" class="T"></path><path d="M468 637c0 1 0 1-1 2h3l2 1c-1 2-1 4-2 5l-1-1 1-3-1-1v1c-1 2-2 5-3 8h-2c-1 0-1-1-1-1l-1-2-4-5v-1h-8l-2-1h18v-1h1l1-1z" class="B"></path><path d="M458 640h9v2c0 3-2 4-4 6l-1-2-4-5v-1z" class="H"></path><path d="M595 544c-1-1-2-2-3-4 2 1 4 1 5 3h2l5 7c0 2 0 3 1 4 0 1 0 2 1 4 1-2 0-4 1-6 0 2 0 2 1 3 6 17 4 37-4 53v1c-1-1-1-1-2-1 1-2 2-4 2-6h-2v-2c0 2-1 3-2 5h-1c0-1 1-3 1-5 1-4 2-8 4-11 0-3 1-5 2-8-1-1-1-3-1-5v-4c0-1-1-2-1-3 0-4-1-8-2-12-1-1-1-2-2-2 0 0-1-1-1-2v-1c-1-2-2-5-4-8z" class="a"></path><path d="M595 544c-1-1-2-2-3-4 2 1 4 1 5 3h1c1 5 4 9 4 14-1-1-1-2-2-2 0 0-1-1-1-2v-1c-1-2-2-5-4-8zM220 167c7 0 16-1 23 1h2v73l-1 1h0c-1-5 0-11 0-16-1-1 0-3 0-4v-9-40l-14-1c-18 0-35 5-50 15-6 3-12 8-17 13v-1l-2 2-1-1 5-5c2-1 3-3 4-3l1-1 2-2h0l3-2 1-1c1 0 1 0 1-1l2-2c2 0 3-2 5-3l1-1c1 0 2-1 3-1 1-1 2-2 3-2s1 0 2-1l12-4 4-1h1 2c1 0 1-1 2-1h2c0-1 1-1 1-1l3-1z" class="v"></path><path d="M177 185c1 0 2 0 2-1l6-3 7-4c1 0 1 0 2-1h2c2-1 5-2 8-3 0 0 2 0 2-1h2 0c1 0 1 0 2-1h1 0 1 2c1-1 1 0 2 0 3-1 7-1 10-1h0l-2 1c-2 0-5 1-7 1s-4 1-6 1h-1c-3 1-7 2-11 3-14 5-26 13-36 23l-2 2-1-1 5-5c2-1 3-3 4-3l1-1 2-2h0l3-2 1-1c1 0 1 0 1-1z" class="V"></path><path d="M210 173h-1l-1-1h3 0v1h-1z" class="Z"></path><path d="M177 185l2-2c2 0 3-2 5-3l1-1c1 0 2-1 3-1 1-1 2-2 3-2s1 0 2-1l12-4 4-1h1 2c1 0 1-1 2-1h2c0-1 1-1 1-1h13c5 0 9-1 13 1l1 1h-1 0c-3-1-6-1-9-1h0c3 1 6 1 9 2h0l1 1h-1c-6-1-13-1-19-1l2-1h0c-3 0-7 0-10 1-1 0-1-1-2 0h-2-1 0-1c-1 1-1 1-2 1h0-2c0 1-2 1-2 1-3 1-6 2-8 3h-2c-1 1-1 1-2 1l-7 4-6 3c0 1-1 1-2 1z" class="O"></path><path d="M491 420v-3c2 1 2 2 3 4 0 3 0 7 3 10v-1c2 2 3 7 4 9l1 1-1 1 2 3c2 1 3 3 4 4v1h2c1 1 1 1 1 2l3 3h0c3 3 5 6 8 8v1c1 0 1 1 2 2v1h0c1 1 1 2 2 3 1 2 2 2 2 4 1 1 2 1 1 2v1l-2-2-1 1 1 1c-1 1-1 0-2-1l-2 1-3-3c0 1 1 3 1 5 0-1-1-2-1-2h-1l-2-2c0-2-2-3-2-4-1-2-2-5-4-7h0l-3-3h1c-2-3-5-6-7-8-5-8-8-16-9-25-1-1-1-2-1-3v-1-3z" class="x"></path><path d="M508 460c2 0 2 2 4 2 0 1 1 1 2 2v1l-2-1c0 2 2 5 3 6s2 4 3 5c1-1 0-2 1-3 1 0 1-1 2-1l1-1c1 1 1 2 2 3 0 1 1 2 1 2l1 1c-1 1-1 0-2-1l-2 1-3-3c0 1 1 3 1 5 0-1-1-2-1-2h-1l-2-2c0-2-2-3-2-4-1-2-2-5-4-7h0l-3-3h1z" class="I"></path><path d="M491 420v-3c2 1 2 2 3 4 0 3 0 7 3 10v-1c2 2 3 7 4 9l1 1-1 1c-3-4-7-10-8-14l-1-1v1c-1-1-1-2-1-3v-1-3z" class="T"></path><path d="M491 423v-3c1 1 2 1 2 2s1 4 0 5l-1-1v1c-1-1-1-2-1-3v-1z" class="w"></path><path d="M503 444c2 1 3 3 4 4v1h0c0 3 3 5 5 7s13 17 14 18l-1 1s-1-1-1-2c-1-1-1-2-2-3l-1 1c-4-2-5-7-8-10 0 0-1-1-1-2l-1-2-1-1c-1-2-3-4-4-6l-3-6z" class="U"></path><path d="M511 457c1 1 2 3 3 4 2 3 6 6 8 9l-1 1c-4-2-5-7-8-10 0 0-1-1-1-2l-1-2z" class="M"></path><path d="M507 449h2c1 1 1 1 1 2l3 3h0c3 3 5 6 8 8v1c1 0 1 1 2 2v1h0c1 1 1 2 2 3 1 2 2 2 2 4 1 1 2 1 1 2v1l-2-2c-1-1-12-16-14-18s-5-4-5-7h0z" class="a"></path><path d="M218 163h3v-1c1 0 3 1 3 0 2 0 5 1 6 0 2 0 4-1 5 0h5c0 1 0 1 1 1s1 0 2 1 2 1 2 2v2h-2c-7-2-16-1-23-1l-3 1s-1 0-1 1h-2c-1 0-1 1-2 1h-2-1l-4 1-12 4c-1 1-1 1-2 1s-2 1-3 2c-1 0-2 1-3 1l-1 1c-2 1-3 3-5 3l-2 2c0 1 0 1-1 1l-1 1-3 2h0l-2 2-1 1c-1 0-2 2-4 3l-5 5-1 1v1l-4 4h-1c0 1 0 1-1 1-1 1 0 0 0 1l-1 1-1 1v1c-3 2-5 5-6 8-1 2-2 4-3 5h-1l2-4-1-1c0-1 1-2 1-2 0-1 1-2 1-2 1-1 2-3 3-4v-1h0c1-1 0-1 1-1 0-1 0-1 1-1 0-2 1-2 2-3 0-1 1-2 1-2 1-1 2-1 3-2 1-2 2-2 3-4s3-3 5-5l1-1c2-1 2-2 3-3 1 0 0 1 1 0 0-1 1-1 2-2l3-2 3-3h1l3-2 3-3h1l1-1h1l2-1h0 2l4-2 1-1h1v-1h2v-1c1 0 1 1 2 0l2-1s1 0 2-1h1l3-1h1c1-1 1-1 2-1h2c1-1 2 0 3-1 1 0 1 0 2-1z" class="L"></path><path d="M203 170l5-1h1s1 0 1-1h2c1 0 2 0 2-1h2c1-1 3-1 4-1v1l-3 1s-1 0-1 1h-2c-1 0-1 1-2 1h-2-1l-4 1-12 4c-1 1-1 1-2 1s-2 1-3 2c-1 0-2 1-3 1l-1 1c-2 1-3 3-5 3l-2 2c0 1 0 1-1 1l-1 1-3 2h0l-2 2-1 1c-1 0-2 2-4 3l-5 5-1 1c-1 0-2 2-3 2 0-2 2-2 3-4s4-4 5-5 1-2 2-2l3-3 2-2h1l10-7c1-1 2-2 3-2l1-1h1c1-1 2-1 3-2h1 1l6-3h1c1 0 1 0 1-1h1 2v-1z" class="j"></path><path d="M218 163h3v-1c1 0 3 1 3 0 2 0 5 1 6 0 2 0 4-1 5 0h5c0 1 0 1 1 1s1 0 2 1 2 1 2 2v2h-2c-7-2-16-1-23-1v-1c-1 0-3 0-4 1h-2c0 1-1 1-2 1h-2c0 1-1 1-1 1h-1l-5 1v-1h2c1-1 2-1 3-1h0c1 0 1 0 2-1h1 1l2-1h0c1 0 2 0 3-1l-1-1c1 0 1 0 2-1z" class="H"></path><path d="M427 638h0 39v1h-18l2 1h-2v1l-1 2h0c0 1 0 1 1 2-1 1-2 3-2 4-2 0-3 0-4-1-1 1-2 2-2 3h-1v-1h0-1c-1-1-2 0-3 0-1 1-2 2-3 2s-2 0-2-1c-2 0-3 0-5 1v-1c-2 1-3 1-5 1v-1l-3 1-1 1-1 1-14-1h6c0-1 0-1-1-2 0-1-1-2-1-3l1-1v-1h0 0-1c-1 0-1 0-2-1v1c-1-2-1-3-2-4v-1s0-1 1-1v-1h-3 0l10-1h7 11z" class="Q"></path><path d="M436 641h0v-1h5c-1 1-1 1-1 2v1 1c-1-1-2-2-4-3z" class="e"></path><path d="M427 638h0 2v1h-13 0v-1h11z" class="Y"></path><path d="M441 640h7v1l-1 2v-2h0l-1 1c-2 0-4 0-6 1v1h0v-1-1c0-1 0-1 1-2z" class="T"></path><path d="M423 640h9l-2 2h-3-1c1 1 2 1 2 3h-1l-1-1c-1-2-2-2-3-3v-1z" class="q"></path><path d="M413 641h-1v-1h11v1h-3-2c-1 1-2 1-3 1 1 1 2 3 3 3v1h0v1 1h-2c0-1 0-1 1-2-2-1-2-4-4-5z" class="u"></path><path d="M429 644c1-2 3-3 4-4 2 1 2 1 3 2v1c-1 2-3 2-4 4-2-1-2-2-3-3z" class="C"></path><path d="M446 642l1-1h0v2h0c0 1 0 1 1 2-1 1-2 3-2 4-2 0-3 0-4-1v-1l-2-3v-1c2-1 4-1 6-1z" class="P"></path><path d="M446 642l1-1h0v2h0c0 1 0 1 1 2-1 1-2 3-2 4-2 0-3 0-4-1v-1l1 1 3-3-1-2c1 0 1 0 1-1z" class="q"></path><path d="M436 641c2 1 3 2 4 3h0l2 3v1c-1 1-2 2-2 3h-1v-1h0-1c-1-1-2 0-3 0s-1-1-2-2h-2s1 0 1-1h0c1-2 3-2 4-4v-1-1z" class="B"></path><path d="M437 646c1 1 2 2 2 3h0v1h0-1c-1-1-2 0-3 0s-1-1-2-2c2 0 2 0 4-2z" class="o"></path><path d="M436 643l2 2h0 1-1c0 1-1 1-1 1-2 2-2 2-4 2h-2s1 0 1-1h0c1-2 3-2 4-4z" class="l"></path><path d="M402 641h3s0-1 1-1 4 1 5 1 1 1 2 1v2c0 1 0 1-2 2 0 0-1 1-2 1h-1s-1 0-1-1h-1 0 0-1c-1 0-1 0-2-1v1c-1-2-1-3-2-4v-1h1z" class="L"></path><path d="M401 641h1c1 1 2 3 4 3l2-1v2l-1 1h-1 0 0-1c-1 0-1 0-2-1v1c-1-2-1-3-2-4v-1z" class="W"></path><path d="M418 645c-1 0-2-2-3-3 1 0 2 0 3-1h2 3c1 1 2 1 3 3l1 1h1l1-1h0c1 1 1 2 3 3h0c0 1-1 1-1 1h2c1 1 1 2 2 2-1 1-2 2-3 2s-2 0-2-1c-2 0-3 0-5 1v-1l1-1 1-2-1-1-3 1c-1 0-2 0-2-1l-3-1v-1z" class="S"></path><path d="M432 652v-1c-1-1-2-1-2-2v-1h1 2c1 1 1 2 2 2-1 1-2 2-3 2z" class="q"></path><path d="M418 645l-1-2c1-1 1-1 3-1 1 0 2 0 3 1 1 0 2 1 3 2-1 1-1 2-2 2h-2-1l-3-1v-1z" class="H"></path><path d="M413 642v-1c2 1 2 4 4 5-1 1-1 1-1 2h2v-1-1h0l3 1c0 1 1 1 2 1l3-1 1 1-1 2-1 1c-2 1-3 1-5 1v-1l-3 1-1 1-1 1-14-1h6c0-1 0-1-1-2 0-1-1-2-1-3l1-1v-1h1c0 1 1 1 1 1h1c1 0 2-1 2-1 2-1 2-1 2-2v-2z" class="L"></path><path d="M420 652l1-1c1 0 0 0 1-1h4l-1 1c-2 1-3 1-5 1z" class="N"></path><path d="M413 642v-1c2 1 2 4 4 5-1 1-1 1-1 2h2v-1-1h0l3 1c0 1 1 1 2 1l-1 1h0-1c-2 0-3 1-5 2h-2c0-2 0-2 1-4h0c-1 0-2 1-2 2h-4c-1-1-2-1-3-2v-1h1c0 1 1 1 1 1h1c1 0 2-1 2-1 2-1 2-1 2-2v-2z" class="M"></path><path d="M413 644c1 1 1 2 0 3s-1 1-2 1-2 0-3-1h1c1 0 2-1 2-1 2-1 2-1 2-2z" class="G"></path><path d="M543 88c2 2 3 4 3 7l1-1c1 6 2 12 1 18 0 4-1 9-1 13 1 0 2 0 3-1 2 0 5-1 7-1h4c-1 1-2 1-4 1s-5 1-7 1l-3 1-7 32h-1c0 1 0 1-1 2 0 0 0 1-1 1v-2l-1-2v1h-1c0-1-1-2-1-3h-1c0-1 0-2-1-3 0-1-1-1 0-2 0-1 0-1 1-2h0l-4-2c0 1 1 1 0 2 0 0-1 0-1 1l-1 3c0 1-1 2-2 2h0c-1 0-1-1-1-2h-2v-1h-1-1l-2 1h0-1c-1 1-1 2-1 3l-1 1-2 2-1-1h-1l1-1-1-1h-2 0v-1h0l-1-1 2-1v-1-1h-2c-2 1-3 1-4 0h0l-1-1 1-1c1 0 3-2 4-3l2-2 4-2 1-1 2-2c1 0 1 0 2-1 2-2 5-3 8-5l2-1 1 1c1-1 2-1 3-2l2-1 3-2h1 1v2h1v-1c0-1 1-2 1-4s0-3-1-5h2c1 0 1-1 1-2 0-2 0-4-1-6v-1h0v-1-3h0c0-2 0-4-1-5 0-2-1-3-1-4-1-1-1-2-2-3l-1-1v-1-1l1-1h3v-1c1 0 1-1 1-1z" class="J"></path><path d="M540 146s1 0 1 1l-1 4-1-1-1-1 2-3z" class="g"></path><path d="M541 139v3h1l-1 5c0-1-1-1-1-1l1-7z" class="G"></path><path d="M541 93v-1h1s0 1 1 1c0 1 1 2 1 2 0 1-1 1-1 2v1h0l-1-1v-2c0-1-1-1-1-2z" class="i"></path><path d="M538 91l1-1h3s1 1 1 2v1c-1 0-1-1-1-1h-1v1c-1-1-2-1-3-1v-1z" class="K"></path><path d="M536 157l2-8 1 1 1 1c-1 2-1 4-2 5s-1 1-1 2h0l1-1v-1c1 1 1 1 1 2s0 1-1 2c0 0 0 1-1 1v-2l-1-2z" class="d"></path><path d="M544 129v-2l1-1 1 1c0 1-1 3-1 4l-3 11h-1v-3l2-8 1-2z" class="C"></path><path d="M543 88c2 2 3 4 3 7 1 5 2 9 2 14 0 3 0 6-1 9s0 6-2 8c-1-1 0-7 1-9 0-8 1-17-3-25 0-1-1-2-1-2v-1c1 0 1-1 1-1z" class="H"></path><defs><linearGradient id="K" x1="535.538" y1="157.613" x2="538.163" y2="130.715" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#373738"></stop></linearGradient></defs><path fill="url(#K)" d="M538 127h1 1v2h1v-1c1 0 2 1 3 1l-1 2-2 8-1 7-2 3-2 8v1h-1c0-1-1-2-1-3h-1c0-1 0-2-1-3 0-1-1-1 0-2 0-1 0-1 1-2h0l-4-2-3-1 2-2v-1h-1v-1h4s0-1 1-1v-1h-1l1-1h-1c-1 0-1-1-2-2v1h-1l-2-2h-1l4-3h1c1-1 2-1 3-2l2-1 3-2z"></path><path d="M541 128c1 0 2 1 3 1l-1 2c-1-1-1 0-2-2h0v-1z" class="D"></path><path d="M532 139h-1l1-1 3 1 1 1 1-1 1 1-1 2h0c-1 0-1 0-1 1 0 0 0 1 1 2-1 0-1 0-2-1h0c-2-1-3-1-4-3 0 0 0-1 1-1v-1z" class="O"></path><path d="M536 143l-3-3 1-1h1l1 1 1-1 1 1-1 2h0c-1 0-1 0-1 1z" class="G"></path><path d="M533 135l1-1c1 1 2 1 3 1 1 1 1 2 2 2 0 1 0 2-1 3l-1-1-1 1-1-1-3-1h-1c-1 0-1-1-2-2 2-1 2-1 4-1z" class="L"></path><path d="M533 135l1-1c1 1 2 1 3 1 1 1 1 2 2 2 0 1 0 2-1 3l-1-1v-2c-1-1-2-1-4-2z" class="T"></path><path d="M538 127h1 1v2h1 0c0 1-1 2-1 3v1 3l-1 1c-1 0-1-1-2-2v-1-1-3l-2 2c0-1 1-2 0-3l3-2z" class="b"></path><path d="M537 134v-2l2 2 1 2-1 1c-1 0-1-1-2-2v-1z" class="C"></path><path d="M538 127h1 1v2h1 0c0 1-1 2-1 3v1 3l-1-2v-1h1v-2l-2-2-1 1-2 2c0-1 1-2 0-3l3-2z" class="e"></path><path d="M527 141h4c1 2 2 2 4 3h0v4 1l-2-1h0l-4-2-3-1 2-2v-1h-1v-1z" class="G"></path><path d="M528 142h1 0v2c1 0 2 1 3 1h1v2 1l-4-2-3-1 2-2v-1z" class="C"></path><path d="M535 129c1 1 0 2 0 3l2-2v3 1 1c-1 0-2 0-3-1l-1 1c-2 0-2 0-4 1v1h-1l-2-2h-1l4-3h1c1-1 2-1 3-2l2-1z" class="j"></path><path d="M535 129c1 1 0 2 0 3h0l-2-2 2-1z" class="i"></path><path d="M529 132l1 1-1 1c-1 1-2 1-3 1h-1l4-3z" class="d"></path><path d="M527 132l2-1 1 1h-1l-4 3h1l2 2h1v-1c1 1 1 2 2 2h1l-1 1h1v1c-1 0-1 1-1 1h-4v1h1v1l-2 2 3 1c0 1 1 1 0 2 0 0-1 0-1 1l-1 3c0 1-1 2-2 2h0c-1 0-1-1-1-2h-2v-1h-1-1l-2 1h0-1c-1 1-1 2-1 3l-1 1-2 2-1-1h-1l1-1-1-1h-2 0v-1h0l-1-1 2-1v-1-1h-2c-2 1-3 1-4 0h0l-1-1 1-1c1 0 3-2 4-3l2-2 4-2 1-1 2-2c1 0 1 0 2-1 2-2 5-3 8-5z" class="K"></path><path d="M514 152c1 0 1-1 1-1 1-1 3-1 3-1 1 1 1 1 2 1l-2 1h0-1-3z" class="d"></path><path d="M515 148c2 0 4-1 7 0-1 1-2 1-3 2l2 1h-1c-1 0-1 0-2-1h1c-1-1-3-1-5-1l1-1z" class="g"></path><path d="M517 152c-1 1-1 2-1 3l-1 1-2 2-1-1h-1l1-1-1-1h-2 0v-1h0l4-2h1 3z" class="D"></path><path d="M517 152c-1 1-1 2-1 3l-1 1-2 2-1-1h-1l1-1h1c1-1 1-2 1-3s0-1-1-1h1 3z" class="c"></path><defs><linearGradient id="L" x1="511.009" y1="145.348" x2="513.141" y2="148.985" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#464546"></stop></linearGradient></defs><path fill="url(#L)" d="M510 145c2 0 3 1 5 0 3 1 6 2 10 2v1c-1 0-1 1-2 0h-1c-3-1-5 0-7 0h-1 0-2c0 1 2 1 2 2l-3 2h-1v-1-1h-2v-1h-1l2-3 1-1z"></path><path d="M511 152v-1-1l1-1c-1 0-2 0-2 1-1 0-1 0-2-1h1c1-2 3-1 5-1h0-2c0 1 2 1 2 2l-3 2z" class="r"></path><path d="M516 144l2-1v1s1 1 2 1l1-1v1c2 0 3 0 5 1v-1h0l3 1c0 1 1 1 0 2 0 0-1 0-1 1l-1 3c0 1-1 2-2 2h0c-1 0-1-1-1-2h-2v-1h-1l-2-1c1-1 2-1 3-2h1c1 1 1 0 2 0v-1c-4 0-7-1-10-2h-2c1-1 2-1 3-1z" class="l"></path><path d="M525 149h2v2l-1 1s-1 0-1-1c-1 0-1 0-2-1l2-1z" class="O"></path><path d="M527 132l2-1 1 1h-1l-4 3h1l2 2h1v-1c1 1 1 2 2 2h1l-1 1h1v1c-1 0-1 1-1 1h-4v1h1v1l-2 2h0v1c-2-1-3-1-5-1v-1l-1 1c-1 0-2-1-2-1v-1l-2 1c-1 0-2 0-3 1h2c-2 1-3 0-5 0l-1 1-2 3h1v1c-2 1-3 1-4 0h0l-1-1 1-1c1 0 3-2 4-3l2-2 4-2 1-1 2-2c1 0 1 0 2-1 2-2 5-3 8-5z" class="H"></path><path d="M522 142c0-1 0-1 1-2 1 0 1 0 1 1h1c0 1 0 1-1 1v2h-3l1-2z" class="C"></path><path d="M527 141v-1c2-1 3-1 5-1v1c-1 0-1 1-1 1h-4z" class="L"></path><path d="M517 140c2-2 6-4 8-5h1l2 2c-1 0-3 0-4 1h-1v2h-1v2l-1 2h3l2 1v1c-2-1-3-1-5-1v-1l-1 1c-1 0-2-1-2-1v-1l-2 1v-2c1 0 1-1 1-2z" class="K"></path><path d="M520 140v3l-2-1v-1l1-1h1z" class="G"></path><path d="M523 138v2h-1v2l-1 2-1-1v-3c1 0 1 0 2-1h1v-1z" class="O"></path><path d="M527 132l2-1 1 1h-1l-4 3c-2 1-6 3-8 5 0 1 0 2-1 2v2c-1 0-2 0-3 1h2c-2 1-3 0-5 0l-1 1-2 3h1v1c-2 1-3 1-4 0h0l-1-1 1-1c1 0 3-2 4-3l2-2 4-2 1-1 2-2c1 0 1 0 2-1 2-2 5-3 8-5z" class="b"></path><path d="M504 150l5-4-2 3h1v1c-2 1-3 1-4 0h0z" class="D"></path><path d="M517 140c0 1 0 2-1 2v2c-1 0-2 0-3 1h2c-2 1-3 0-5 0l7-5z" class="J"></path><path d="M496 372l1-2c1 0 2 1 3 2l3 1 1 1 1 1c2 2 4 3 6 5l1 2c1 2 4 4 4 6l2 3 1 1c1 1 2 2 3 4l2 4 1 1c1 1 1 2 2 2l1 1h1 0 0l2 2c0 2 0 3 1 4-1 0-2-1-3-1l-1 1c1 3 1 6 3 10 0 1 1 2 1 4s0 4 1 6v1 1l-1-1-1 1-1 1-1-1-2 1c0 1 1 1 1 2h-3c0-1-1-3-2-4s-2-1-3-1c-2-3-4-6-5-9-1 1-1 1-1 2l1 1-2 1v-2h-1c-1-1-2-2-2-4v-5h-1l-1-1v-5h-1c0-2 0-3-1-4v-1l-1-1v4h-1v-3l-1-2h1v-4c-1-3-3-6-4-9l1-1s1 0 1-1l-1-2v-4-1l-2-1c0-1 0-1-1-2 0 0 0-1-1-1h-1v-4 1z" class="a"></path><path d="M531 432l-1-1h-1 0l1-1c-1-1-2-1-2-2l1 1h3v2l-1 1z" class="f"></path><path d="M531 420c0 1 1 2 1 4s0 4 1 6v1 1l-1-1v-2l-3-9h0 1 1z" class="Q"></path><path d="M516 415h1c0 2 1 3 2 5 3 3 6 6 8 10-2-1-2-2-4-3 0-1-1-3-2-4l-1 1h0c-1-2-4-6-4-9h0z" class="E"></path><path d="M520 424l1-1c1 1 2 3 2 4 2 1 2 2 4 3l2 2-2 1c0 1 1 1 1 2h-3c0-1-1-3-2-4v-1s-1 0-1-1c1-1-1-4-2-5z" class="n"></path><path d="M520 424l1-1c1 1 2 3 2 4v1c1 2 2 3 4 5v1c-1-1-1 0-1-1l-3-3s-1 0-1-1c1-1-1-4-2-5z" class="o"></path><path d="M515 415h1c0 3 3 7 4 9h0c1 1 3 4 2 5 0 1 1 1 1 1v1c-1-1-2-1-3-1-2-3-4-6-5-9l-1-1v-4l1 3v-1-3z" class="K"></path><path d="M515 415h1c0 3 3 7 4 9h0c1 1 3 4 2 5-1-1-2-2-3-4s-3-4-4-6v-1-3z" class="U"></path><path d="M511 405c0-1 0-1 1-2l2 6 1 2h1 0v4h0-1v3 1l-1-3v4l1 1c-1 1-1 1-1 2l1 1-2 1v-2h-1c-1-1-2-2-2-4v-5h-1c1-3 1-6 1-9h1z" class="K"></path><path d="M510 414v3h1 0c1 0 2-1 3-1h0v4l1 1c-1 1-1 1-1 2l1 1-2 1v-2h-1c-1-1-2-2-2-4v-5z" class="E"></path><path d="M511 405c0-1 0-1 1-2l2 6 1 2h1 0v4h0-1v3 1l-1-3h0c-2-4-3-8-3-11z" class="D"></path><path d="M514 409l1 2h1 0v4h0-1v-2c-1-1-1-2-1-4z" class="h"></path><path d="M519 402c1 0 1 1 2 2h0l4 8c0 3 2 6 2 9l-1 1 1 2h0c-3-2-5-4-7-7-2-2-2-4-4-6h0c0-1 0-2-1-3l1-1c0-1 1-1 1-2s0-2 1-2h1v-1z" class="E"></path><path d="M517 407c1 1 2 1 3 1l1 1h0c-1 1-2 1-2 2v1 1l-2-3c-1-1 0-2 0-3z" class="f"></path><path d="M519 402c1 0 1 1 2 2-1 1-1 3-1 4h0c-1 0-2 0-3-1 1-1 1-3 2-4v-1z" class="M"></path><path d="M519 413v-1-1c0-1 1-1 2-2 0 2 0 4 1 5 0 1 1 2 1 3l-2-1c0-1-1-2-2-3z" class="k"></path><path d="M521 404h0l4 8c0 3 2 6 2 9l-1 1c-1-2-2-3-3-5 0-1-1-2-1-3-1-1-1-3-1-5h0l-1-1h0c0-1 0-3 1-4z" class="z"></path><path d="M518 391l1 1c1 1 2 2 3 4l2 4 1 1c1 1 1 2 2 2l1 1h1 0 0l2 2c0 2 0 3 1 4-1 0-2-1-3-1l-1 1c1 3 1 6 3 10h-1-1 0c-1-3-2-6-4-8l-4-8h0c-1-1-1-2-2-2v1h-1c1-4 0-6-1-10 1 0 1-1 1-2z" class="J"></path><path d="M528 404h1 0 0l2 2c0 2 0 3 1 4-1 0-2-1-3-1l-1 1c-1-1-1-1-1-2h0c-1 0-2-1-3-2l1-2h2 1z" class="f"></path><path d="M528 404h1 0 0l-3 1 1 1v2h0c-1 0-2-1-3-2l1-2h2 1z" class="Y"></path><path d="M518 391l1 1c1 1 2 2 3 4l2 4 1 1c1 1 1 2 2 2l1 1h-1-2l-1 2-1-2c-1-1-1-2-2-2v2h0c-1-1-1-2-2-2v1h-1c1-4 0-6-1-10 1 0 1-1 1-2z" class="H"></path><path d="M519 402l1-5h0l2 2c-1 1-1 2-1 3v2h0c-1-1-1-2-2-2z" class="S"></path><path d="M522 399l1 2c1 0 1-1 1-1l1 1c1 1 1 2 2 2l1 1h-1-2l-1 2-1-2c-1-1-1-2-2-2 0-1 0-2 1-3z" class="h"></path><path d="M522 399l1 2 1 2c0 1-1 1-1 1-1-1-1-2-2-2 0-1 0-2 1-3z" class="r"></path><path d="M511 391h0c-1-1-1-3-1-5 1-1 0 0 1 0h1l2 2 1 1 1-1 2 3c0 1 0 2-1 2 1 4 2 6 1 10-1 0-1 1-1 2s-1 1-1 2l-1 1c1 1 1 2 1 3h-1l-1-2-2-6-2-8c0-1 0-1 1-2v-2z" class="I"></path><path d="M516 401c1 1 1 2 1 4 0 1-1 1-1 2l-1 1-1-3c2-2 2-2 2-4z" class="S"></path><path d="M511 391h0c-1-1-1-3-1-5 1-1 0 0 1 0h1l2 2v2h-1v3 1l-2-3h0 0z" class="J"></path><path d="M510 395c0-1 0-1 1-2l3 12 1 3c1 1 1 2 1 3h-1l-1-2-2-6-2-8z" class="d"></path><path d="M516 388l2 3c0 1 0 2-1 2 1 4 2 6 1 10-1 0-1 1-1 2 0-2 0-3-1-4v-1l1-1c-1-1-1-3-2-4 0-2-1-3-1-5v-2l1 1 1-1z" class="Q"></path><path d="M516 388l2 3c0 1 0 2-1 2l-2-4 1-1z" class="C"></path><defs><linearGradient id="M" x1="501.98" y1="394.779" x2="511.633" y2="396.224" xlink:href="#B"><stop offset="0" stop-color="#191b1d"></stop><stop offset="1" stop-color="#3a3837"></stop></linearGradient></defs><path fill="url(#M)" d="M496 372l1-2c1 0 2 1 3 2l3 1 1 1 1 1c2 2 4 3 6 5l1 2c1 2 4 4 4 6l-1 1-1-1-2-2h-1c-1 0 0-1-1 0 0 2 0 4 1 5h0v2c-1 1-1 1-1 2l2 8c-1 1-1 1-1 2h-1c0 3 0 6-1 9l-1-1v-5h-1c0-2 0-3-1-4v-1l-1-1v4h-1v-3l-1-2h1v-4c-1-3-3-6-4-9l1-1s1 0 1-1l-1-2v-4-1l-2-1c0-1 0-1-1-2 0 0 0-1-1-1h-1v-4 1z"></path><path d="M507 380c2 1 4 4 5 6h0-1c-1 0 0-1-1 0 0 2 0 4 1 5h0v2c-1 1-1 1-1 2-1-5-1-10-3-15z" class="m"></path><path d="M502 375h2v1c0 4 2 10 3 14-1 1 0 1-1 1v-1l-1 2c0-1 0-2-1-3l-1-8-1-1v-5z" class="O"></path><path d="M504 374l1 1c2 2 4 3 6 5l1 2c1 2 4 4 4 6l-1 1-1-1-2-2h0c-1-2-3-5-5-6-1-2-2-3-3-4v-1-1z" class="H"></path><path d="M496 372l1-2c1 0 2 1 3 2l3 1 1 1v1h-2v5h-1v-1l-2-1c0-1 0-1-1-2 0 0 0-1-1-1h-1v-4 1z" class="T"></path><path d="M500 374l2 1v5h-1v-1c0-2-1-3-1-5z" class="d"></path><path d="M496 372l1-2c1 0 2 1 3 2l3 1 1 1v1h-2l-2-1-4-2z" class="C"></path><path d="M501 380h1l1 1 1 8c1 1 1 2 1 3-1 1 0 4 0 5h-1c-1-3-3-6-4-9l1-1s1 0 1-1l-1-2v-4z" class="g"></path><path d="M503 381l1 8h0c-1 0-1-1-2-2 0-2 0-5 1-6z" class="d"></path><path d="M507 390c1 6 2 12 1 18h-1c0-2 0-3-1-4v-1l-1-1v4h-1v-3l-1-2h1v-4h1c0-1-1-4 0-5l1-2v1c1 0 0 0 1-1z" class="G"></path><path d="M504 397h1v5 4h-1v-3l-1-2h1v-4z" class="c"></path><path d="M244 448c1-5 0-10 0-15 0-2 0-4 1-6v10 4h0c0-2 0-4 1-5v6h0v62 16 1h1v-7 7c0 4 2 8 1 11-1 2 0 3 0 5s-1 5-1 8v1c0 2 0 3-1 5-1 3-1 7-2 11-2 6-6 10-10 15-1 1-2 3-4 5-1 1-3 2-4 3-11 6-22 11-34 14l2-2c-3-1-6 1-8 0h0c1-1 4-1 5-1l2-2c4-1 8-3 12-3l8-4-1-1 5-3v-1l3-1c10-8 17-18 20-31-2-1-3-4-5-6v-2c0-2-2-3-2-5l-2-1v-1c0-1-1-2-2-2h-1v-3-7c0-1 0-3 1-3 0-1 0-1 1-1-1-2-1-4-1-6 0 1 1 2 2 4v-1-3h0c-1-1-2-2-2-3 0-2 1-5 2-6s1-2 2-3h0c1-3 7-7 10-9l-2-2h0l2 1h1v-43z" class="z"></path><path d="M236 522h1v1c-1 1-2 1-4 1v-1l2-1h1z" class="J"></path><path d="M232 510c1-1 2-1 4-1v1c-1 1-2 2-4 2h0c0-1-1-1-1-2h1z" class="Y"></path><path d="M231 513l1 2h1 1-1c1 3 3 2 3 5h-1-2c-1-1-2-2-2-3v-1-3z" class="h"></path><path d="M231 524l1-1v1 1 2 1l1-1c1 1 0 2 0 3l-1 1c0 1 1 1 1 3-1 0-1 0-2 1 0-1-1-2-2-2l2-1-1-1v-2c1 0 1 0 1-1l-1-2 1-1v-1z" class="c"></path><path d="M229 533l2-1 2 2h0c-1 0-1 0-2 1 0-1-1-2-2-2z" class="I"></path><path d="M230 519c-1-2-1-4-1-6 0 1 1 2 2 4 0 1 1 2 2 3h2l-2 1-1-1c0 1 0 1 1 1 1 1 2 1 3 1h-1l-2 1v1h0l-1 1v-1-1l-1 1c0 1 0 1-1 1 0-1 0-1-1-2h0l1-1s1 0 1-1v-1l-1-1zm3-18c1 0 3-1 4-2h1c0 1 0 1 1 1h0c-1 1-2 1-2 1v1l2 1h0c-1 1-2 1-4 1l-1 2v-1c-1 0-2-1-3-1 1-1 1-2 2-3z" class="a"></path><path d="M233 501h0c1-3 7-7 10-9 0 3-1 4-2 5s-3 2-4 2c-1 1-3 2-4 2z" class="Y"></path><path d="M230 519l1 1v1c0 1-1 1-1 1l-1 1h0c1 1 1 1 1 2 1 0 1 0 1-1v1l-1 1 1 2c0 1 0 1-1 1v2l1 1-2 1h-1v-3-7c0-1 0-3 1-3 0-1 0-1 1-1z" class="U"></path><path d="M244 520l1-29v13h1v16h-2z" class="b"></path><path d="M231 504c1 0 2 1 3 1v1l2 1v1c-2 0-3 0-5 1l1 1h-1c0 1 1 1 1 2v2c2 1 4-1 4 1v1l-3-1h1-1-1l-1-2h0c-1-1-2-2-2-3 0-2 1-5 2-6z" class="J"></path><path d="M245 441h0c0-2 0-4 1-5v6h0v62h-1v-13-4-9-37z" class="P"></path><path d="M246 521h1v-7 7c0 4 2 8 1 11-1 2 0 3 0 5s-1 5-1 8v1c0 2 0 3-1 5-1 3-1 7-2 11-2 6-6 10-10 15-1 1-2 3-4 5-1 1-3 2-4 3-11 6-22 11-34 14l2-2c-3-1-6 1-8 0h0c1-1 4-1 5-1l2-2c4-1 8-3 12-3l8-4-1-1 5-3c8-5 14-11 19-19 3-6 5-12 6-19 1-5 2-11 2-17v-8h2v1z" class="r"></path><path d="M244 520h2v1l-1 13c0-2 0-4-1-6v-8z" class="O"></path><path d="M244 528c1 2 1 4 1 6-1 11-3 22-8 31l-1-1c3-6 5-12 6-19 1-5 2-11 2-17z" class="P"></path><path d="M217 583c8-5 14-11 19-19l1 1c-6 11-13 17-24 22l-1-1 5-3z" class="N"></path><path d="M205 591l1 1 1-1c1 0 2-1 3-1 3-1 5-2 8-4l4-2c1-1 2-1 3-2s4-3 5-4c0-1 1-2 2-3v-1h0c1-2 3-3 4-5 2-3 3-6 4-9 1-1 1-2 2-3h0c-1 4-2 8-4 11l-6 9h0 0l5-6c4-5 5-10 7-16 0-1 0-3 1-4v-4c1 0 1 0 2-1 0 2 0 3-1 5-1 3-1 7-2 11-2 6-6 10-10 15-1 1-2 3-4 5-1 1-3 2-4 3-11 6-22 11-34 14l2-2c-3-1-6 1-8 0h0c1-1 4-1 5-1l2-2c4-1 8-3 12-3z" class="K"></path><path d="M556 238l9 2 4 2h1c0 1 0 2 1 3v1l-1 1 4 4h0c1 2 2 5 3 7v4 1l-6-5v1l-2-2-1 2h0c2 1 3 1 4 2l-1 1-3-2h-1c2 2 4 3 4 6-2-1-3-2-4-3h-1v3h0c-1-1-2-1-3-2-1 0-1 0-2 1v1c-1-1-2-1-3-2v2l2 2c-1 1-2 1-3 1-3-2-5-4-8-5h0v1l-3 1c-2-1-5-2-7-3-6-2-11-4-17-5-5-1-11-2-17-3h-2-1-6 0c-1-1-2-1-4 0v-3-1h0c0-1 1-1 1-1-1 0-1-1-1-1h8v-1c-2 0-4 0-6-1 1-1 0-1 0-2h-2l-1-1 4-1 4-1 11-2-2 2c5-1 10-2 15-2 11-2 21-3 32-1 0-1 1-1 1-1z" class="z"></path><path d="M522 242c2 1 3 2 5 3 0 0 0 1 1 1v1h-1c-1-1-2-1-3-2l-2-2v-1z" class="D"></path><path d="M548 245l1-1h1c4 1 7 4 10 6v1c0 2 2 3 3 4l-10-3h3 0c1 0 1 0 2 1h0c0-1 0-1 1-1l-1-1h-1-1c-3-2-6-4-8-6z" class="B"></path><path d="M503 248l50 4 10 3c-1-1-3-2-3-4l6 4h0l-1 1 1 1-1 1c-2-1-3-2-5-2l-2-1c-7-2-14-3-21-4-5 0-10-1-16-1h-10l-11-1v-1h3z" class="u"></path><path d="M508 242c5-1 10-2 15-2l-3 1h0l2 1v1c-6-2-13 0-19 2h1 0 3v1h-2s0 1-1 1h0c-1 1 0 1-1 1h-3c-2 0-4 0-6-1 1-1 0-1 0-2h-2l-1-1 4-1 4-1 11-2-2 2z" class="R"></path><path d="M499 242l11-2-2 2c-3 0-6 2-9 2-2 0-3 1-4 1h-1-2l-1-1 4-1 4-1z" class="H"></path><path d="M495 243l1 1h3c-2 0-3 1-4 1h-1-2l-1-1 4-1z" class="O"></path><path d="M495 245h4l1 1c1-1 2-1 3-1h1 0 3v1h-2s0 1-1 1h0c-1 1 0 1-1 1h-3c-2 0-4 0-6-1 1-1 0-1 0-2h1zm61-7l9 2 4 2h1c0 1 0 2 1 3v1l-1 1 4 4h0c1 2 2 5 3 7v4 1l-6-5v1l-2-2-1 2c0-1-2-2-2-2l-1-1 1-1h0l-6-4v-1c-3-2-6-5-10-6h-1l-1 1h-1s-1 0-1-1c-1-1-5-3-7-3h-1-1c-2-1-6 0-8 1-1 0-3-1-5 0 1 1 1 1 2 1 0 1 1 1 1 2 1 0 1 0 1 1-1 0-1-1-1-1-2-1-3-2-5-3l-2-1h0l3-1c11-2 21-3 32-1 0-1 1-1 1-1z" class="f"></path><path d="M566 257l-1-1 1-1 3 2-1 2c0-1-2-2-2-2z" class="O"></path><path d="M556 238l9 2 4 2h1c0 1 0 2 1 3v1l-1 1c-4-4-9-6-15-8 0-1 1-1 1-1z" class="X"></path><path d="M571 258c-1-3-5-6-6-9 1 0 1-1 1-1 2 0 7 6 8 7 0 1 1 2 2 3h1v4 1l-6-5z" class="P"></path><defs><linearGradient id="N" x1="535.643" y1="257.231" x2="538.014" y2="249.423" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272628"></stop></linearGradient></defs><path fill="url(#N)" d="M492 249h8l11 1h10c6 0 11 1 16 1 7 1 14 2 21 4l2 1c2 0 3 1 5 2l1-1s2 1 2 2h0c2 1 3 1 4 2l-1 1-3-2h-1c2 2 4 3 4 6-2-1-3-2-4-3h-1v3h0c-1-1-2-1-3-2-1 0-1 0-2 1v1c-1-1-2-1-3-2v2l2 2c-1 1-2 1-3 1-3-2-5-4-8-5h0v1l-3 1c-2-1-5-2-7-3-6-2-11-4-17-5-5-1-11-2-17-3h-2-1-6 0c-1-1-2-1-4 0v-3-1h0c0-1 1-1 1-1-1 0-1-1-1-1z"></path><path d="M542 257l7 2-1 1c-2 0-4-1-6-1v-2z" class="O"></path><path d="M537 256l5 1v2l-4-1-3-1s1-1 2-1z" class="H"></path><path d="M557 258h1l1 1c1 2 3 4 5 4h2v3h0c-1-1-2-1-3-2-1 0-1 0-2 1v1c-1-1-2-1-3-2-3-2-6-3-10-4l1-1c1 0 3 1 4 1l7 3c-1-2-1-3-3-5z" class="u"></path><path d="M557 258h-1l-2-2c-1 0-2 0-3-1h0c1 0 2 0 2 1h2 2l1-1 2 1c2 0 3 1 5 2l1-1s2 1 2 2h0c2 1 3 1 4 2l-1 1-3-2h-1c2 2 4 3 4 6-2-1-3-2-4-3h-1-2c-2 0-4-2-5-4l-1-1h-1z" class="B"></path><path d="M566 257s2 1 2 2h0c2 1 3 1 4 2l-1 1-3-2h-1c2 2 4 3 4 6-2-1-3-2-4-3-3-2-5-4-7-6h0c2 0 3 1 5 2v-1l1-1z" class="j"></path><path d="M538 258l4 1c2 0 4 1 6 1 4 1 7 2 10 4v2l2 2c-1 1-2 1-3 1-3-2-5-4-8-5h0v1l-3 1c-2-1-5-2-7-3h1c1 1 1 1 2 1h0 2 1c1-1 0-1 0-2h1 0c-1-1-2-1-3-1-1-1-2-1-3-2h0l-2-1z" class="I"></path><path d="M538 258l4 1c2 0 4 1 6 1 4 1 7 2 10 4v2c-4-2-8-3-11-5-3-1-5-2-7-2l-2-1z" class="E"></path><path d="M492 249h8l11 1h10l-5 1h0l4 1c1 0 1 0 1 1l16 3c-1 0-2 1-2 1l3 1 2 1h0c1 1 2 1 3 2 1 0 2 0 3 1h0-1c0 1 1 1 0 2h-1-2 0c-1 0-1 0-2-1h-1c-6-2-11-4-17-5-5-1-11-2-17-3h-2-1-6 0c-1-1-2-1-4 0v-3-1h0c0-1 1-1 1-1-1 0-1-1-1-1z" class="v"></path><path d="M492 252c3 1 5 2 7 2 2 1 4 1 6 1h-2-1-6 0c-1-1-2-1-4 0v-3z" class="q"></path><path d="M504 251l17 2 16 3c-1 0-2 1-2 1-11-3-21-4-32-5h0v-1h1z" class="L"></path><path d="M492 249h8l11 1h10l-5 1h0l4 1c1 0 1 0 1 1l-17-2h-1v1h0l-11-1c0-1 1-1 1-1-1 0-1-1-1-1z" class="z"></path><path d="M493 250h10s1 0 1 1h-1v1h0l-11-1c0-1 1-1 1-1z" class="H"></path><path d="M441 335c-2-1-3-2-4-3v-2c-1 0-1 0 0-1h1l2 2c1 1 2 2 4 2h2l2 1-1-3 4 2 1 1c1 0 4 1 5 1 1 1 4 2 5 3l4 2c1-1 1-1 1-2 1 0 2 1 3 1h1c1 1 2 1 2 2v1l1 1 2 2c1 1 2 1 2 2v2c3 2 6 4 8 7h3c3 3 4 7 5 11h1l1 4v4h1c1 0 1 1 1 1 1 1 1 1 1 2l2 1v1 4l1 2c0 1-1 1-1 1l-1 1c1 3 3 6 4 9v4h-1l1 2v3h1v-4l1 1v1c1 1 1 2 1 4h1v5l1 1h1v5l-1 2s0 1-1 2h0-1-1v4l1 2c-1 0-1 0-1 1l-1 2h-1c0 2-1 4 0 5v4 1c-1-1-2-1-2-2l-1-1c-1-2-2-7-4-9v1c-3-3-3-7-3-10 0-2-1-5-1-7 0-1-1-2-1-3l1-1c0-2-1-3-1-5 0-4-1-9-3-12h0c-1 0-2-4-2-4-2-2-3-6-3-9-2-3-4-6-6-8s-4-4-6-5-2-2-3-3l-13-14v-1l1 1v-2l-7-4-3-2c-2-1-5-3-7-5v-1h0l1-1z" class="F"></path><path d="M484 380c1 1 1 2 2 3 0 1 0 1 1 1v5c-2-2-3-6-3-9z" class="V"></path><path d="M476 367c3 1 6 5 8 7l6 8c4 4 6 7 8 11l-1 1h0c-1-2-2-3-3-5-3-3-5-7-7-10l-12-12h1z" class="G"></path><path d="M494 389c1 2 2 3 3 5h0c1 2 1 4 1 5v4 1l-1 2h0c-1 0-1 0-2 1 0 1 0 2-1 2l-1 1c0-2-1-3-1-5 1 1 1 3 2 4v-1-1c0-1 1-2 1-3-1-1-1-2-2-3v-1c0-1 0-1 1-1 0 0 1 1 1 2l1-1c-1-2 0-5-1-7-1-1-1-3-1-4z" class="j"></path><defs><linearGradient id="O" x1="504.69" y1="389.042" x2="480.119" y2="384.558" xlink:href="#B"><stop offset="0" stop-color="#bfbfc0"></stop><stop offset="1" stop-color="#f3f2f1"></stop></linearGradient></defs><path fill="url(#O)" d="M484 374c0-1 0-1-1-2h0 0l2 1c2 2 4 3 5 5 2 2 4 6 7 7h0c1 1 1 3 2 3h1c1 3 3 6 4 9v4h-1l1 2-1 1h0l-3-5-2-6c-2-4-4-7-8-11l-6-8z"></path><path d="M499 388h1c1 3 3 6 4 9v4h-1c-1-4-3-8-4-13z" class="D"></path><path d="M457 348l10 8h1c1 3 3 4 5 6h0l4 4c2 1 4 3 6 5l2 2-2-1h0 0c1 1 1 1 1 2-2-2-5-6-8-7h-1l-6-3-13-14v-1l1 1v-2z" class="X"></path><path d="M469 364l-13-14v-1l1 1c5 3 8 7 12 11 2 2 4 4 7 6h-1l-6-3z" class="Q"></path><path d="M494 367h1l1 4v4h1c1 0 1 1 1 1 1 1 1 1 1 2l2 1v1 4l1 2c0 1-1 1-1 1l-1 1h-1c-1 0-1-2-2-3h0c-3-1-5-5-7-7-1-2-3-3-5-5l-2-2h2l1 1 2 1v-1l1-1c-1 0-1 0-2-1h0c1-1 0-1 0-2 0 1 0 1 1 1h0c2 0 3 1 4 1h1 1 1l-1-2v-1z" class="b"></path><path d="M494 367h1l1 4v4c1 3 2 7 3 10l1 3h-1c-1 0-1-2-2-3 0-2-1-4-2-7 0-2 0-5-1-6l-2-2h1 1 1l-1-2v-1z" class="U"></path><path d="M496 375h1c1 0 1 1 1 1 1 1 1 1 1 2l2 1v1 4l1 2c0 1-1 1-1 1l-1 1-1-3c-1-3-2-7-3-10z" class="q"></path><path d="M499 378l2 1v1 4l1 2c0 1-1 1-1 1l-1 1-1-3c1 0 1 1 1 1v1h1c0-2-1-2-1-3v-1-1c0-2 0-3-1-4z" class="G"></path><path d="M498 393l2 6 3 5h0l1-1v3h1v-4l1 1v1c1 1 1 2 1 4h1v5l1 1h1v5l-1 2s0 1-1 2h0-1-1v4l1 2c-1 0-1 0-1 1l-1 2h-1c0 2-1 4 0 5v4 1c-1-1-2-1-2-2l-1-1c-1-2-2-7-4-9v1c-3-3-3-7-3-10 0-2-1-5-1-7 0-1-1-2-1-3l1-1 1-1c1 0 1-1 1-2 1-1 1-1 2-1h0l1-2v-1-4c0-1 0-3-1-5l1-1z" class="d"></path><path d="M500 423h0v-1c-1-3-2-6-2-9v-3h1v1l1 1c0 1 1 2 1 3h-1c0 2 2 4 2 5l-1 1s1 2 0 2h-1z" class="K"></path><path d="M499 411l1 1c0 1 1 2 1 3h-1c-1-1-2-1-2-4h1z" class="o"></path><path d="M498 399c1 4 1 7 2 11l1 1-1 1-1-1v-1l-1-1-2-3c0 3 0 6-1 9-1 1 1 5 1 6v2c1 2 1 5 1 7v1c-3-3-3-7-3-10 0-2-1-5-1-7 0-1-1-2-1-3l1-1 1-1c1 0 1-1 1-2 1-1 1-1 2-1h0l1-2v-1-4z" class="b"></path><path d="M496 421v-2h1c1 2 2 5 3 7s2 3 3 4c0 1 0 1 1 2h0c0 2-1 4 0 5v4 1c-1-1-2-1-2-2l-1-1c-1-2-2-7-4-9 0-2 0-5-1-7v-2z" class="S"></path><path d="M501 439c1 0 1-1 2-1l1 3v1c-1-1-2-1-2-2l-1-1z" class="w"></path><path d="M496 423c1 2 2 4 3 7 1 2 3 5 4 8-1 0-1 1-2 1-1-2-2-7-4-9 0-2 0-5-1-7z" class="h"></path><path d="M498 393l2 6 3 5h0l1-1v3h1v-4l1 1v1c1 1 1 2 1 4h1v5l1 1h1v5l-1 2s0 1-1 2h0-1-1v4l1 2c-1 0-1 0-1 1l-1 2h-1 0c-1-1-1-1-1-2-1-1-2-2-3-4 1-1 1-1 0-3h1c1 0 0-2 0-2l1-1c0-1-2-3-2-5h1c0-1-1-2-1-3l1-1-1-1c-1-4-1-7-2-11 0-1 0-3-1-5l1-1z" class="m"></path><path d="M503 415v1l2 1c0 1 0 2-1 3-1-1-1-3-2-4l1-1z" class="d"></path><path d="M505 402l1 1v1c0 3 0 6-1 8v2h-1v-6-2h1v-4z" class="Y"></path><path d="M502 406l1-1c0 1 0 2 1 3v6h1c-1 1-1 2-2 2v-1l-1-2v-7z" class="k"></path><path d="M506 403v1c1 1 1 2 1 4h1v5 1c-1 0-2-1-2-1v-2-7-1z" class="g"></path><path d="M501 408v-4h1v2h0v7l1 2-1 1-1-1c0-1-1-2-1-3l1-1-1-1 1-2z" class="T"></path><path d="M501 411h1v2l1 2-1 1-1-1c0-1-1-2-1-3l1-1z" class="O"></path><path d="M508 413l1 1h1v5l-1 2s0 1-1 2c0-1-1-2-1-2 0-2 0-5 1-7v-1zm-8 2h1l1 1c1 1 1 3 2 4s1 3 1 4v1l-1 1-2-6c0-1-2-3-2-5z" class="D"></path><path d="M501 423c1 0 0-2 0-2l1-1 2 6c1 0 1 0 2 1l1 2c-1 0-1 0-1 1l-1 2h-1 0c-1-1-1-1-1-2-1-1-2-2-3-4 1-1 1-1 0-3h1z" class="u"></path><path d="M500 423h1l2 4c1 2 1 4 1 5-1-1-1-1-1-2-1-1-2-2-3-4 1-1 1-1 0-3z" class="k"></path><path d="M498 393l2 6 3 5h0l1-1v3 2c-1-1-1-2-1-3l-1 1h0v-2h-1v4l-1 2c-1-4-1-7-2-11 0-1 0-3-1-5l1-1z" class="d"></path><path d="M500 399l3 5h0l1-1v3 2c-1-1-1-2-1-3l-1 1h0v-2h-1v4c-1-1 0-2-1-4v-1-4z" class="g"></path><path d="M441 335c-2-1-3-2-4-3v-2c-1 0-1 0 0-1h1l2 2c1 1 2 2 4 2h2l2 1-1-3 4 2 1 1c1 0 4 1 5 1 1 1 4 2 5 3l4 2c1-1 1-1 1-2 1 0 2 1 3 1h1c1 1 2 1 2 2v1l1 1 2 2c1 1 2 1 2 2v2c3 2 6 4 8 7h3c3 3 4 7 5 11v1l1 2h-1-1-1c-1 0-2-1-4-1h0c-1 0-1 0-1-1 0 1 1 1 0 2h0c1 1 1 1 2 1l-1 1v1l-2-1-1-1h-2c-2-2-4-4-6-5l-4-4h0c-2-2-4-3-5-6h-1l-10-8-7-4-3-2c-2-1-5-3-7-5v-1h0l1-1z" class="L"></path><path d="M477 358h0s0-1 1-1v-1c2 3 3 4 5 6-1 0-2-1-3-1l-1-1c-1 0-2-1-2-2z" class="q"></path><path d="M484 363c3 1 7 3 9 5v1h1l-1-2h0l1 1 1 2h-1-1c-1-2-3-2-5-3l-4-4z" class="H"></path><path d="M486 356h3c3 3 4 7 5 11v1l-1-1c-3-3-5-8-7-11z" class="l"></path><path d="M462 338l4 2c1-1 1-1 1-2 1 0 2 1 3 1h1c1 1 2 1 2 2v1l1 1 2 2c1 1 2 1 2 2v2l-8-6-6-3c-1-1-2-1-2-2z" class="g"></path><path d="M467 338c1 0 2 1 3 1 1 2 1 2 1 3l-5-2c1-1 1-1 1-2z" class="C"></path><path d="M471 339c1 1 2 1 2 2v1l1 1 2 2-1 1c-2-1-3-2-4-4 0-1 0-1-1-3h1z" class="G"></path><path d="M472 353l5 5c0 1 1 2 2 2l1 1c1 0 2 1 3 1l1 1 4 4c2 1 4 1 5 3h-1c-1 0-2-1-4-1-3-2-6-5-9-7l-3-3s-1 1-2 1c-2-2-4-3-6-4h-1l1-1h1l2 1h1l-1-1 1-2z" class="T"></path><path d="M468 356c2 1 4 2 6 4 1 0 2-1 2-1l3 3c3 2 6 5 9 7h0c-1 0-1 0-1-1 0 1 1 1 0 2h0c1 1 1 1 2 1l-1 1v1l-2-1-1-1h-2c-2-2-4-4-6-5l-4-4h0c-2-2-4-3-5-6z" class="C"></path><path d="M473 362c2 0 2 0 3 1l2 2-1 1-4-4z" class="H"></path><path d="M478 365c3 2 6 3 9 5h0c1 1 1 1 2 1l-1 1v1l-2-1-1-1h-2c-2-2-4-4-6-5l1-1z" class="O"></path><path d="M441 335c-2-1-3-2-4-3v-2c-1 0-1 0 0-1h1l2 2c1 1 2 2 4 2h2l2 1-1-3 4 2 1 1c2 1 4 3 7 5 1 1 1 1 1 2l3 3c2 1 3 2 4 4 1 1 4 3 5 5h0l-1 2 1 1h-1l-2-1h-1l-1 1-10-8-7-4-3-2c-2-1-5-3-7-5v-1h0l1-1z" class="j"></path><path d="M455 343c-1 0-2-1-3-2h0c1-1 1-1 1 0l6 3-2 1c-1-1-2-1-2-2z" class="F"></path><path d="M447 331l4 2 3 5c2 1 4 2 5 3l1 1c-3-1-6-4-9-4l-7-5h2l2 1-1-3z" class="K"></path><path d="M447 331l4 2 3 5c-2-1-4-3-6-4l-1-3z" class="l"></path><path d="M452 344l4 2c-1-1-1-1-1-3 0 1 1 1 2 2l2-1 12 11 1 1h-1l-2-1h-1l-1 1-10-8-7-4h2z" class="C"></path><path d="M455 343c0 1 1 1 2 2 0 1 1 2 1 3l-2-2c-1-1-1-1-1-3z" class="j"></path><path d="M452 344l4 2 2 2c4 2 7 5 10 7l-1 1-10-8-7-4h2z" class="K"></path><path d="M441 335c-2-1-3-2-4-3v-2c-1 0-1 0 0-1h1l2 2c1 1 2 2 4 2l7 5c1 1 2 1 2 3 0-1 0-1-1 0h0c1 1 2 2 3 2 0 2 0 2 1 3l-4-2h-2l-3-2c-2-1-5-3-7-5v-1h0l1-1z" class="H"></path><defs><linearGradient id="P" x1="443.775" y1="334.315" x2="447.022" y2="344.982" xlink:href="#B"><stop offset="0" stop-color="#626060"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#P)" d="M441 335c4 3 8 5 11 9h-2l-3-2c-2-1-5-3-7-5v-1h0l1-1z"></path><path d="M545 209l6 1c1 0 2 1 3 1l2 1c-2 0-2 1-4 0h-1-1v1c1 0 2 0 2 1 1 0 2 0 3 1h0 2l1 3c-1 0-1 1-2 1l3 1c0 1 0 0 1 1 0 0 1 0 2 1h1-1c-3-1-6 0-9 0 2 0 4 1 5 1l-1 1h-1c2 1 4 2 6 4h0l1 2-1 1 3 3h0l-2-1-1 1c1 2 2 2 4 3s3 3 4 4c0 1 0 0-1 1l-4-2-9-2s-1 0-1 1c-11-2-21-1-32 1-5 0-10 1-15 2l2-2-11 2-4 1-4 1h-2v1h-4v-1l3-1h0c-2 0-3 0-4-1h-1l-1-1h0c0-2-1-2-2-3-1 0-2-1-2-2h1c-1-1-4-4-5-4s-2 0-2 1h-1c-1 0-1-1-2-1h-1 0v-1l1-1c0 1 0 1 1 1l-3-6h1l-1-1 1-1c4 2 8 5 14 4l1 1h4c1 0 2-1 4-1v1h3 0c3-1 5-2 7-4 0-1 1-1 1-1 1 0 1 0 2-1l13-6 4-1h-1l1-2 8-2h4l2-1c1 0 2 0 3 1v-1h4l3-1z" class="v"></path><path d="M532 223c1 1 2 1 2 2s-1 1-2 1v-3z" class="a"></path><path d="M540 233s1 0 1-1h0c1 0 2 0 3 1l-1 1h-2l-1-1z" class="f"></path><path d="M520 232c2 0 6 0 8 1h0-1c-2 0-5 1-7 0v-1z" class="M"></path><path d="M554 230s-1 0-2-1c0 0-1-1-2-1-2 0-2 0-4-1l1-1h4l1 1-1 1 3 2z" class="x"></path><path d="M554 230l-3-2 1-1c2 1 4 1 5 2-1 1-1 1-1 2 1 1 2 1 3 2l1 2-6-5z" class="D"></path><path d="M527 215c2-1 3-1 5-1v1c2 0 4 0 6 1 2 0 4 1 6 2l-16-3h-1z" class="R"></path><path d="M530 223h2v3l-1 1c-2 0-3-1-5-1l-1 1c1 1 2 1 2 1v1s-1 1-2 1c0-1-1-2-1-3l-1-1h1 2c2 0 0 0 1-1h2l1-1v-1z" class="B"></path><path d="M519 218c6-1 12-1 17 1h-15c-1 0-1 0-2-1z" class="T"></path><path d="M487 231h5l6-2-1 1c0 1 1 1 1 1l1 1 1-1c1 1 1 1 2 1-2 1-5 1-7 1 1 0 1 0 1-1-2 0-5 1-7 0-1 0-2-1-2-1z" class="K"></path><path d="M484 231h3s1 1 2 1c2 1 5 0 7 0 0 1 0 1-1 1l-1 1v1l-1 1-4-1-3-1h-1c0-1-1-2-2-2l-1-1h2z" class="d"></path><path d="M486 234l2-1c1 0 1 0 2 1 0 0-1 0-1 1l-3-1z" class="e"></path><path d="M521 213l8-2c2 1 3 1 5 1v1 1h-2c-2 0-3 0-5 1h-6-1l1-2z" class="w"></path><path d="M535 210c1 0 2 0 3 1 3 1 7 2 10 4h-1c-4-1-8-1-12-1h-1v-1-1c-2 0-3 0-5-1h4l2-1z" class="h"></path><path d="M529 211h4 2 2v1c1 1 1 1 2 1v1c-1 0-3-1-4 0h-1v-1-1c-2 0-3 0-5-1z" class="p"></path><path d="M562 231c-3-2-5-4-7-6l-8-3h6c2 0 4 1 5 1l-1 1h-1c2 1 4 2 6 4h0l1 2-1 1z" class="e"></path><path d="M517 231h3v1 1c2 1 5 0 7 0v1h0c1 0 2 1 2 1v1h-7-6 0 0v-1h0c1 0 3-1 4-1l-7-1v-1l4-1z" class="I"></path><path d="M520 234c3 0 6 0 9 1-2 0-5 0-7 1h-6 0 0v-1h0c1 0 3-1 4-1z" class="c"></path><path d="M509 222c3-2 7-3 10-4 1 1 1 1 2 1v1h-1l-4 1c1 1 1 0 2 0h1 2 2l1 1h-7c-2 1-4 1-6 1l-4 1h-1l1-1h0c0-1 1-1 2-1z" class="I"></path><path d="M509 222c3-2 7-3 10-4 1 1 1 1 2 1v1c-3 0-5 0-7 1-2 0-3 1-5 1z" class="e"></path><path d="M557 229c2 1 4 3 6 4h0l-1 1c1 2 2 2 4 3s3 3 4 4c0 1 0 0-1 1l-4-2v-1h-1c-1-1-3-1-5-2-1 0-3-2-4-3-1 0-2 0-3-1h0c3 0 5 2 8 3v-1h0l-1-2c-1-1-2-1-3-2 0-1 0-1 1-2z" class="J"></path><path d="M544 233c3 1 7 2 10 2l1-1c1 1 3 3 4 3 2 1 4 1 5 2h1v1l-9-2c-6-1-11-2-17-1l2-1v-1h-1c-1-1-1-1-2-1v-1h2l1 1h2l1-1z" class="a"></path><path d="M539 237c6-1 11 0 17 1 0 0-1 0-1 1-11-2-21-1-32 1-5 0-10 1-15 2l2-2 29-3z" class="G"></path><path d="M545 209l6 1c1 0 2 1 3 1l2 1c-2 0-2 1-4 0h-1-1v1c1 0 2 0 2 1 1 0 2 0 3 1h0 2l1 3c-1 0-1 1-2 1l-5-2h-1l-1-1c-1 0-1 0-2-1h1c-3-2-7-3-10-4v-1h4l3-1z" class="f"></path><defs><linearGradient id="Q" x1="510.397" y1="231.151" x2="514.423" y2="221.958" xlink:href="#B"><stop offset="0" stop-color="#1a1a18"></stop><stop offset="1" stop-color="#333034"></stop></linearGradient></defs><path fill="url(#Q)" d="M517 222h7l5 1h1v1l-1 1h-2c-1 1 1 1-1 1h-2-1l1 1c-2 0-4-1-6 0h-11c-2 0-5 1-7 2v-1h0c1-2 4-3 6-4h1l4-1c2 0 4 0 6-1z"></path><path d="M517 222h7l5 1v1h-4c-1 0-1 1-2 1s-2-1-4-1c-3 0-7 0-11 1l-1-1 4-1c2 0 4 0 6-1z" class="T"></path><path d="M511 223c2 0 4 0 6-1 1 1 2 1 3 1v1c-2 0-7 1-9-1z" class="O"></path><path d="M518 227c2-1 4 0 6 0 0 1 1 2 1 3-2 1-3 1-5 1h-3l-4 1h-3-8c-1 0-1 0-2-1l-1 1-1-1s-1 0-1-1l1-1h2c2-1 5-2 7-2h11z" class="k"></path><path d="M513 230h7v1h-3l-4 1h-3l3-1v-1z" class="E"></path><path d="M500 231c4-1 9-1 13-1v1l-3 1h-8c-1 0-1 0-2-1z" class="m"></path><path d="M518 227c2 0 3 1 5 1l-14 1c-4 1-7 2-11 2 0 0-1 0-1-1l1-1h2c2-1 5-2 7-2h11z" class="d"></path><path d="M468 231l1-1c0 1 0 1 1 1l-3-6h1l6 4c3 1 6 1 10 2h-2l1 1c1 0 2 1 2 2h1l3 1 4 1 1-1v-1l1-1c2 0 5 0 7-1h8 3v1l7 1c-1 0-3 1-4 1h0v1h0 0l-8 1c-4 1-8 2-11 4h-1c1 1 2 1 3 1l-4 1-4 1h-2v1h-4v-1l3-1h0c-2 0-3 0-4-1h-1l-1-1h0c0-2-1-2-2-3-1 0-2-1-2-2h1c-1-1-4-4-5-4s-2 0-2 1h-1c-1 0-1-1-2-1h-1 0v-1z" class="E"></path><path d="M490 236l3 1c-1 2-3 2-4 2l-3-2 1 1c1 0 2-1 3-2z" class="p"></path><defs><linearGradient id="R" x1="481.756" y1="229.899" x2="481.369" y2="236.197" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#908f90"></stop></linearGradient></defs><path fill="url(#R)" d="M474 229c3 1 6 1 10 2h-2l1 1c1 0 2 1 2 2h0l5 2c-1 1-2 2-3 2l-1-1c-4-1-7-3-10-5-1-1-2-1-2-3h0 0 0z"></path><path d="M482 241h0c0-2-1-2-2-3-1 0-2-1-2-2h1c1 1 3 2 5 2 2 1 5 2 7 2 1-1 2-1 4-1l2 2h-1c1 1 2 1 3 1l-4 1-4 1h-2v1h-4v-1l3-1h0c-2 0-3 0-4-1h-1l-1-1z" class="w"></path><path d="M489 244h-1c1-1 1-1 1-2h0c3 0 5 0 7-1 1 1 2 1 3 1l-4 1-4 1h-2z" class="B"></path><path d="M510 232h3v1l7 1c-1 0-3 1-4 1h0-7c-1 0-1 1-2 1-2 0-4 1-6 1l-1 1c-2 0-5 1-7-1l-3-1-5-2h0 1l3 1 4 1 1-1v-1l1-1c2 0 5 0 7-1h8z" class="k"></path><path d="M485 234h1l3 1 4 1c1 0 5 0 7-1h2v1l-1 1-1 1c-2 0-5 1-7-1l-3-1-5-2h0z" class="B"></path><path d="M128 285l-57-2v-1c0-6-7-11-11-15l-10-9c0-2 1-4 2-6l2-9 11-42 18-75 8-35 34 1h73 0l-2 1H93c-1 2-2 5-2 7l-4 15-5 22-10 41-6 24-6 25-4 15c0 2 0 5-1 6l-1 1c0 1 0 2-1 3v4l14 2h7c3 0 6 1 9 0 0-1-1-1 0-3h3c2-1 4 0 5 0l2 1c1 0 1 0 2 1l3 1c6 1 12 0 18 1l1 1v2c-4 2-10-1-13 0l12 1 2 1h3c1 0 1-1 1-1 1-2 0-4 1-6v2c1 1 0 1 1 2l1 1s1 1 1 2v1c0 1 0 1 1 2v3c-1 2-1 4-2 6h0 0l1 1c0-1 1-1 1-2h0v-2h0 1s0-1 1-2v1h1v-5c1-2 1-5 2-7h0v1c0 3-3 23-4 24z" class="y"></path><path d="M85 265h0c-2 0-8 1-9-1h0c2-1 4 0 6 0 1 0 2 1 3 1z" class="O"></path><path d="M104 262l12 1 2 1h-10c-2 0-3 0-4-1v-1z" class="j"></path><path d="M90 262l12 1h1c-1 1-4 1-6 1l-9-1c-2-1-4-1-6-1h0 8z" class="P"></path><path d="M58 263h1c2 1 3 1 4 1h4 5c0 1-1 2-1 2-2 1-6 1-8 1h-1c-1-1-3-2-4-4zm24 1h12c1 0 3 1 5 1h10 2c3 0 6 0 8 1-1 0-1 0-1 1h-1c-3-2-7-1-11-1-3 0-7 0-11-1H85c-1 0-2-1-3-1z" class="j"></path><path d="M58 263l-4-3h8l16 1c3 0 8 0 12 1h-8 0c2 0 4 0 6 1-4 0-9-1-13 0-1 1-2 1-3 0H62h-3-1z" class="g"></path><path d="M58 263l-4-3h8 1v1h-5l1 1c2 0 5-1 6 1h-3-3-1z" class="L"></path><path d="M130 272v-5c1-2 1-5 2-7h0v1c0 3-3 23-4 24 0-1 1-1 0-1h-3c-5 0-11-1-16-1-12 0-24 0-36-1l-1-1h1l54 2v-1c-5 0-10-1-15-1-6 0-14 1-21 0h-2c-6-1-11-1-17-1l-2-4h2v-1h-1l1-1h3c1 1 4 0 5 0 3 1 5 1 8 2 1 0 1-1 2 0h2c1 1 3 0 4 0h10s1 1 2 1h15l1-1h0 1 0l1 1c0-1 1-1 1-2h0v-2h0 1s0-1 1-2v1h1z" class="j"></path><path d="M129 271v1h1c-1 2-1 5-1 7-1-1-1-4-1-6 0 0 0-1 1-2z" class="L"></path><path d="M90 280c2-1 2 0 4 0 4 0 10-1 14 0 1 1 3 0 4 1H98h-7-2c0-1 0 0 1-1z" class="P"></path><path d="M72 275c2 0 3 0 4 1 1 0 3-1 4 0h1 3 1c1 0 1 0 2 1-1 1-2 0-3 1l-2-1h-3c-1 1-1 0-2 0h-4v1l-1-1v-1-1z" class="N"></path><path d="M70 276h2v1l1 1c2 2 7-1 9 1h4 1c1 0 2 0 3 1-1 1-1 0-1 1-6-1-11-1-17-1l-2-4z" class="X"></path><path d="M87 277h3 0 1 3c3 0 7 0 10 1h7l8 1h0-16c-1 0-2 0-2-1-2 0-3 1-4 1h-4c-1 0-3 0-4-1h0-2-2-1c1-1 2 0 3-1z" class="C"></path><path d="M122 263c1-2 0-4 1-6v2c1 1 0 1 1 2l1 1s1 1 1 2v1c0 1 0 1 1 2v3c-1 2-1 4-2 6h0-1 0l-1 1h-15c-1 0-2-1-2-1H96c-1 0-3 1-4 0h-2c-1-1-1 0-2 0-3-1-5-1-8-2-1 0-4 1-5 0h-3l-1 1h1v1h-2c-2-4-5-6-8-9h1c2 0 6 0 8-1 0 0 1-1 1-2h2l2 2h4 16l-1-1c4 1 8 1 11 1 4 0 8-1 11 1h1c0-1 0-1 1-1h2v-2c1 0 1-1 1-1z" class="R"></path><path d="M112 272h9 1 1v1h-1-11c1-1 0-1 1-1z" class="j"></path><path d="M111 273l-17-1-1-1h1 10c3 0 5 0 8 1-1 0 0 0-1 1z" class="L"></path><path d="M72 264h2l2 2h4 16c1 0 3 1 5 1h10 4v1c-8 1-16-1-24-1-1 0-2 0-4 1h-5c-3 0-7-1-10 0v-1l-1-1s1-1 1-2z" class="G"></path><g class="L"><path d="M122 263c1-2 0-4 1-6v2c1 1 0 1 1 2l1 1s1 1 1 2v1c0 1 0 1 1 2v3c-1 2-1 4-2 6-2-1-1-1-2-2l-1-1h1v-1h-1-1 1c-1-1-4-1-6-1l-13-1h-9-1c-1-1-1 0-2 0 1 1 1 1 1 2h0c-1 0-2-1-3-1l1-1c-1 0-1-1-1-1 1-1 4 0 5 0h18l7 1h3l1-1v-2h-5 0c0-1 0-1 1-1h2v-2c1 0 1-1 1-1z"></path><path d="M71 266l1 1v1h-6-1c3 2 23-1 23 2-2 1-7 0-10 0H67v1c2 1 8 0 11 0s7 0 10 1c1 0 3 1 4 1 3 1 8 0 12 0 3 0 7 1 10 1h6l1 1c-4 1-10 0-14-1h-7-6-1l-5-1-9-1c-3 0-6 1-9 0v1h0 1 6c4 0 9 1 13 2h9l18 1c2 0 4-1 7 0l-1 1h-15c-1 0-2-1-2-1H96c-1 0-3 1-4 0h-2c-1-1-1 0-2 0-3-1-5-1-8-2-1 0-4 1-5 0h-3l-1 1h1v1h-2c-2-4-5-6-8-9h1c2 0 6 0 8-1z"></path></g><path d="M533 170h1 0c0 2 1 4-1 5h-1c-1 0-1 1-2 1h0l-1 1v1c2 0 3-1 4-2h2c0-1 0-2 1-3 2 2 5 2 7 4 1 0 2-1 3-1v2c-2 1-5 2-7 3-1 1-1 1-2 1v2h1c1 1 1 1 2 1 0 1 1 1 2 1l-2 2v1c1 0 1-1 2 0 2 0 3-1 5-2v1l1 1c-1 1-2 1-3 2 0 0-2 0-2 1h1c-1 1-1 2-2 3v1c1 0 3-1 4-1 2 0 3 2 5 2 0 1 0 1-1 1l1 1h0c-2 1-4 1-6 2h-2c-1 1-3 2-5 2 0 0-1 0-1 1h1-1v1h-2c0 1 1 2 2 3h0c-1-1-2-1-2-1-1 0-1 0-2 1h1v1h2v1h-1l-2 1h-4l-8 2-1 2h1l-4 1-13 6c-1 1-1 1-2 1 0 0-1 0-1 1-2 2-4 3-7 4h0-3v-1c-2 0-3 1-4 1h-4l-1-1c-6 1-10-2-14-4-1-2-2-3-4-4h-1c1-1 1-2 1-2l2-3v-3l1-2c1 0 2-1 2-2 1 0 2 0 3-1h4 0v-2c2 0 4-1 6-2l2 1c1 0 2 0 3-1h-1v-1h0-1-1c1-1 1-1 2-1 1-1 2-1 3-2l2-2 6-3 1-1c5-2 11-5 16-8 1-1 4-3 6-4 2-2 4-3 7-5 2-1 5-1 6-4v-1z" class="f"></path><path d="M517 215c1-1 3-1 4-2l-1 2h1l-4 1h-1c1 0 0 0 1-1h0z" class="h"></path><path d="M504 222c4-4 9-5 13-7h0c-1 1 0 1-1 1h1l-13 6z" class="E"></path><path d="M533 208h1v1c-11 1-23 4-32 13v-1l7-8 2-2 1 1-1 1h1c1-1 3-2 4-2l11-2c2-1 4-1 6-1z" class="w"></path><path d="M487 211c3 1 15 0 17 1-2 4-5 9-9 12l-9 3h0v-10l1-6z" class="t"></path><path d="M538 201h0 5 0c-1 1-3 2-5 2 0 0-1 0-1 1h1-1v1h-2c0 1 1 2 2 3h0c-1-1-2-1-2-1-1 0-1 0-2 1-2 0-4 0-6 1l-11 2c-1 0-3 1-4 2h-1l1-1-1-1-2 2c-1 0-1 0-1-1 0-2 1-3 2-4 0-1 0-1-1-2l1-1 2-2h0 3 0c2-1 3-1 5-1h0l1 2c5-2 11-2 16-4l1 1z" class="B"></path><path d="M510 205l2-2c1 1 1 1 2 1-1 1-2 1-3 2-1 0-1 0-1-1z" class="I"></path><path d="M515 203h0c2-1 3-1 5-1-1 1-3 2-5 3h-1 0c0-1 1-1 1-2h0z" class="J"></path><path d="M538 201h0 5 0c-1 1-3 2-5 2 0 0-1 0-1 1h-4s-1 0-2-1c-1 0-3 1-4 1l-12 3c-2 0-4 2-6 2 4-3 7-4 12-5 5-2 11-2 16-4l1 1z" class="m"></path><path d="M538 201h5 0c-1 1-3 2-5 2 0 0-1 0-1 1h-4s-1 0-2-1c2 0 5-1 7-2z" class="a"></path><path d="M537 204h1-1v1h-2c0 1 1 2 2 3h0c-1-1-2-1-2-1-1 0-1 0-2 1-2 0-4 0-6 1l-11 2c-1 0-3 1-4 2h-1l1-1-1-1c6-5 15-6 22-7h4z" class="G"></path><path d="M512 212c7-4 15-5 23-7 0 1 1 2 2 3h0c-1-1-2-1-2-1-1 0-1 0-2 1-2 0-4 0-6 1l-11 2c-1 0-3 1-4 2h-1l1-1z" class="M"></path><path d="M494 199c1 0 2 0 4-1v2h0v1c1 0 3 0 4-1 0 0 1-1 2-1v1c1 1 1 1 2 1 0 0 0-1 1-1v1h0c-1 1-1 1-2 1l-2 1v1l2 1c1 1 1 3 1 4v1c-1 1-1 1-1 3h-1v-2 1c-2-1-14 0-17-1l-1 6h0l-1-2h0c-1-1-1-4-1-5v2 2l-2-9c-2 0-4 1-6 1v-2c2 0 4-1 6-2l2 1c1 0 2 0 3-1 1 0 2-1 4-1l1-1s2 0 2-1z" class="I"></path><path d="M484 207v-2c1-1 1 0 2 0v4-2h-1-1z" class="H"></path><path d="M484 207h1 1v2l-1 6h0c-1-1-1-4-1-5v-3z" class="L"></path><path d="M505 205v1c-2-1-4-2-6-2s-3 0-4-1h0l2-1c1 1 2 1 3 1h1c1 0 1 1 2 1l2 1z" class="D"></path><path d="M492 200v2h1 0-2c-1 1-2 1-3 2h-4 0v-1c1 0 2 0 3-1 1 0 2-1 4-1l1-1z" class="z"></path><path d="M494 199c1 0 2 0 4-1v2h0v1c1 0 3 0 4-1 0 0 1-1 2-1v1c1 1 1 1 2 1 0 0 0-1 1-1v1h0c-1 1-1 1-2 1l-2 1v1c-1 0-1-1-2-1h-1c-1 0-2 0-3-1h-6 2 0-1v-2s2 0 2-1z" class="a"></path><path d="M487 211v-3c0-1 1-2 1-3 4 0 8 0 12 1 1 1 2 1 3 2s1 2 1 3v1c-2-1-14 0-17-1z" class="N"></path><path d="M476 206c2 0 4-1 6-1l2 9v-2-2c0 1 0 4 1 5h0l1 2h0v10h0l1 1h-4l-1-1c-6 1-10-2-14-4-1-2-2-3-4-4h-1c1-1 1-2 1-2l2-3v-3l1-2c1 0 2-1 2-2 1 0 2 0 3-1h4 0z" class="s"></path><path d="M467 209c1 0 2-1 2-2 1 0 2 0 3-1h4c-5 2-7 4-10 8v-3l1-2zm17 1c0 1 0 4 1 5h0l1 2h0v10h0l1 1h-4c1-1 1 0 2-1l-1-13h0v-2-2z" class="U"></path><path d="M464 219h-1c1-1 1-2 1-2l1 1c0-1 0-1 1-1v1c2 3 8 7 11 8l5 1c-6 1-10-2-14-4-1-2-2-3-4-4z" class="R"></path><path d="M537 182v2h1c1 1 1 1 2 1 0 1 1 1 2 1l-2 2v1c1 0 1-1 2 0 2 0 3-1 5-2v1l1 1c-1 1-2 1-3 2 0 0-2 0-2 1h1c-1 1-1 2-2 3v1c1 0 3-1 4-1 2 0 3 2 5 2 0 1 0 1-1 1l1 1h0c-2 1-4 1-6 2h-2 0-5 0l-1-1c-5 2-11 2-16 4l-1-2h0c-2 0-3 0-5 1h0-3l2-1h-2v-1h0 2-1l1 1c0-1 1-1 1-1 2-1 3-1 4-2-1 0-6 1-7 2h-1-4v-1c-1 0-1 1-1 1-1 0-1 0-2-1v-1c3-2 5-2 8-3s5-2 8-3l1-3c5-2 11-4 16-8z" class="W"></path><path d="M507 200l4-1s1 0 1-1l7-2h0 2l2-1 4-1c1 0 2 0 3-1 1 1 1 1 2 1l-4 2c-1 0-1 1-2 1-2 1-4 2-6 2-2 2-4 3-6 3h-2v-1h0 2-1l1 1c0-1 1-1 1-1 2-1 3-1 4-2-1 0-6 1-7 2h-1-4v-1z" class="B"></path><path d="M537 182v2h1c1 1 1 1 2 1 0 1 1 1 2 1l-2 2v1c1 0 1-1 2 0 2 0 3-1 5-2v1c-4 2-9 4-13 6h-2c-1 0-1 0-2-1 1 0 3-1 4-1v-1-1l-1 1h-2c-1 1 0 0-1 0-1 1-3 1-4 1-2 1-4 1-6 1l1-3c5-2 11-4 16-8z" class="I"></path><path d="M547 188l1 1c-1 1-2 1-3 2 0 0-2 0-2 1h1c-1 1-1 2-2 3v1c1 0 3-1 4-1 2 0 3 2 5 2 0 1 0 1-1 1l1 1h0c-2 1-4 1-6 2h-2 0-5 0l-1-1c-5 2-11 2-16 4l-1-2h0c-2 0-3 0-5 1h0-3l2-1c2 0 4-1 6-3 2 0 4-1 6-2 1 0 1-1 2-1l4-2h2c4-2 9-4 13-6z" class="h"></path><path d="M537 198c-1 2-3 2-4 2s-2 1-2 1h1 0 1c1 0 1 0 1-1h2 0 0 1c-5 2-11 2-16 4l-1-2 17-4z" class="f"></path><path d="M543 192h1c-1 1-1 2-2 3-2 1-4 2-7 3-4 1-8 1-12 2h0c1-1 1-1 2-1 3-1 6-2 9-4l9-3z" class="a"></path><path d="M542 196c1 0 3-1 4-1 2 0 3 2 5 2 0 1 0 1-1 1l1 1h0c-2 1-4 1-6 2h-2 0-5 0l-1-1h-1 0 0-2c0 1 0 1-1 1h-1 0-1s1-1 2-1 3 0 4-2c0 0 4-2 5-2z" class="W"></path><path d="M538 201v-1c1 0 3 0 4-1 3 0 6-1 8-1l1 1h0c-2 1-4 1-6 2h-2 0-5 0z" class="D"></path><path d="M533 170h1 0c0 2 1 4-1 5h-1c-1 0-1 1-2 1h0l-1 1v1c2 0 3-1 4-2h2c0-1 0-2 1-3 2 2 5 2 7 4 1 0 2-1 3-1v2c-2 1-5 2-7 3-1 1-1 1-2 1-5 4-11 6-16 8l-1 3c-3 1-5 2-8 3s-5 1-8 3c-1 0-2 1-2 1-1 1-3 1-4 1v-1h0v-2c-2 1-3 1-4 1 0 1-2 1-2 1l-1 1c-2 0-3 1-4 1h-1v-1h0-1-1c1-1 1-1 2-1 1-1 2-1 3-2l2-2 6-3 1-1c5-2 11-5 16-8 1-1 4-3 6-4 2-2 4-3 7-5 2-1 5-1 6-4v-1z" class="a"></path><path d="M531 184s0-1 1-1l1-1h-1-1c-1 1-2 1-3 1v-1l3-2 1 1h0 2 2 1l-6 3z" class="D"></path><path d="M535 176h1c0 1-1 2-1 3 1 0 2 1 3 1h0l-1 1h-1-2-2 0l-1-1h0 0l-2-2h0c2 0 3-1 4-2h2z" class="o"></path><path d="M533 176c1 1 1 1 0 2 0 1-1 1-2 2h0l-2-2h0c2 0 3-1 4-2z" class="D"></path><path d="M535 176c0-1 0-2 1-3 2 2 5 2 7 4 1 0 2-1 3-1v2c-2 1-5 2-7 3-1 1-1 1-2 1-5 4-11 6-16 8l-1 3c-3 1-5 2-8 3s-5 1-8 3c-1 0-2 1-2 1-1 1-3 1-4 1v-1h0v-2c1 0 3-1 5-1v-1h1l1-1 26-11 6-3 1-1h0c-1 0-2-1-3-1 0-1 1-2 1-3h-1z" class="g"></path><path d="M536 176v-1c2 0 3 0 4 1l1 1 1 1h-1c-1 1-3 1-3 2-1 0-2-1-3-1 0-1 1-2 1-3z" class="N"></path><path d="M503 196c6-1 12-3 18-6l-1 3c-3 1-5 2-8 3s-5 1-8 3c-1 0-2 1-2 1-1 1-3 1-4 1v-1h0v-2c1 0 3-1 5-1v-1z" class="U"></path><path d="M503 197h2c-1 1-5 3-7 3v-2c1 0 3-1 5-1z" class="I"></path><path d="M533 170h1 0c0 2 1 4-1 5h-1c-1 0-1 1-2 1h0l-1 1v1h0c-6 3-11 8-17 11l-5 2-3 3v1h0 1l-1 1h-1v1c-2 0-4 1-5 1-2 1-3 1-4 1 0 1-2 1-2 1l-1 1c-2 0-3 1-4 1h-1v-1h0-1-1c1-1 1-1 2-1 1-1 2-1 3-2l2-2 6-3 1-1c5-2 11-5 16-8 1-1 4-3 6-4 2-2 4-3 7-5 2-1 5-1 6-4v-1z" class="E"></path><path d="M489 200l1-1h1c1-1 2 0 3 0 0 1-2 1-2 1l-1 1-2-1z" class="k"></path><path d="M489 200l2 1c-2 0-3 1-4 1h-1v-1h0 0l3-1z" class="D"></path><path d="M491 196l6-3h0c0 1-1 1-1 2h0l-4 2-3 1 2-2z" class="R"></path><path d="M520 180h0l6-3h1 0 0c-2 2-4 3-6 4v1c-4 2-7 5-11 6l-13 7h-1 0c0-1 1-1 1-2h0l1-1c5-2 11-5 16-8 1-1 4-3 6-4z" class="Y"></path><path d="M531 89c2-1 5-3 7-2l2 1c1 0 1 1 2 1v1h-3l-1 1v1 1l1 1c1 1 1 2 2 3 0 1 1 2 1 4 1 1 1 3 1 5h0v3 1h0v1c1 2 1 4 1 6 0 1 0 2-1 2h-2c1 2 1 3 1 5s-1 3-1 4v1h-1v-2h-1-1l-3 2-2 1c-1 1-2 1-3 2l-1-1-2 1c-3 2-6 3-8 5-1 1-1 1-2 1l-2 2-1 1-4 2-2 2c-1 1-3 3-4 3l-1 1-8 7-2 3-4 3-3 3c0-1 0-1-1-2l-1 2-1-1 2-5-1-1-3 4v-3l1-1v-3-1h-1s-1-1-1-2l2-4c-1 1-2 1-2 1l-1 1h-1c0 2 0 2-1 3l-1 1h-1l-2 2-1 2-1 3-1-1v2c0-1 0-2-1-3h0c0-2 0-3 1-5h-1l-1 1v-2c1-1 1-2 1-3l3-6v-1h-1 0v-1h-1c-1 1-1 2-1 3 0-1 0-2-1-3h-1l3-3c-1-1-1-1-3-2 1-1 2-2 2-3l1-1 3-3c0-1 1-2 1-2l1-1c0-1 2-1 2-2 1 0 1-1 1-2h1c0-1 1-1 1-1l1-1v1c3-2 5-3 7-5l2 1c1-1 2-3 4-3v-1c3 0 5-2 7-3-1 2-5 3-6 6l9-8v2l27-22z" class="x"></path><path d="M477 133h3l-3 3c0-1 0-2-1-3h1z" class="D"></path><path d="M536 92c-2-1-2 0-4-1h0 2 1 3v1 1l-2-1z" class="W"></path><path d="M479 150c1-2 2-5 5-6v1 1 1c0 1-1 1-2 1-1 1-2 1-2 1l-1 1z" class="r"></path><path d="M501 140c1 1 1 1 2 1h0v2 1h-1 0l-1 2h-1c0-1-1-1-2-2l1-1 2-2v-1z" class="I"></path><path d="M501 141v2c1 0 1 0 2 1h-1-2 0l-1-1 2-2z" class="B"></path><path d="M501 140c1 1 1 1 2 1h0v2 1c-1-1-1-1-2-1v-2-1z" class="k"></path><path d="M484 147c1-1 2-1 3-1l-3 3-1 1-1 1c1 2 1 2 0 3h-1s-1-1-1-2l2-4c1 0 2 0 2-1z" class="i"></path><path d="M538 87l2 1c1 0 1 1 2 1v1h-3l-1 1h-3-1c1-2 3-2 4-4z" class="T"></path><path d="M477 133c3-3 7-5 10-7h0c0 1-1 1-1 1 0 2-4 5-6 6h-3z" class="U"></path><path d="M502 144l1 1c2-1 3-1 4 0h1c-1 1-3 3-4 3v-2h0-2c-1 1-2 1-3 1h0l1-1h1l1-2z" class="B"></path><path d="M500 130c-1 1-2 1-3 2l-2-1h0-1c3-3 5-5 9-6-1 1-1 2-1 3-1 0-2 1-3 2h1z" class="I"></path><path d="M502 140l2-2v-1h-1c-1 0-2 0-3 1l-1-1c2-2 7-3 9-3h0l1 1 1 1-2 1c-1 1-3 1-4 3h-1-1z" class="E"></path><path d="M484 149v1h3c-2 3-3 4-3 7v1h0l-3 4v-3l1-1v-3-1c1-1 1-1 0-3l1-1 1-1z" class="c"></path><path d="M482 154c1-1 1-1 0-3l1-1c1 0 1 1 1 2 0 2 0 2-2 3v-1z" class="o"></path><path d="M516 111h1 0 2c-1 1-1 1-1 2h1l1-1 2 1h0l-1 1-5 1h0l-1-1h-4c-1 1-2 1-3 2 1-1 2-3 4-4h1c1-1 2-1 3-1z" class="a"></path><path d="M516 111h1 0 2c-1 1-1 1-1 2h1c-1 1-4 1-5 1h-1l3-3z" class="U"></path><defs><linearGradient id="S" x1="521.819" y1="105.535" x2="528.883" y2="110.095" xlink:href="#B"><stop offset="0" stop-color="#434244"></stop><stop offset="1" stop-color="#6d6d6d"></stop></linearGradient></defs><path fill="url(#S)" d="M517 111c5-3 10-6 15-8v1h0c0 1 1 2 1 3h-1c-2 0-4 0-6 1h-1l-3 3s-1 0-2 1l-1 1h-1c0-1 0-1 1-2h-2 0z"></path><path d="M516 115h0 1c1 1 2 0 3 1l-1 1h0-3c2 1 3 1 5 1-1 1-1 2-3 3l-6 1 8 1v1h-3 0c-6 0-10 2-15 4 0-1 0-2 1-3 3 0 5-2 8-3 1 0 2 0 2-1h1v-1h4v-1c-1-1-3 0-4-1v-1h1-3-1c1-1 3-2 5-2z" class="S"></path><defs><linearGradient id="T" x1="503.979" y1="127.714" x2="512.447" y2="133.65" xlink:href="#B"><stop offset="0" stop-color="#565655"></stop><stop offset="1" stop-color="#807e80"></stop></linearGradient></defs><path fill="url(#T)" d="M509 129c3-1 5-1 8 0h4-2l-1 1h2-1l1 1c0 1 0 1-1 1h-5c-1 1-2 1-4 1l2-1c1 0 1 0 2-1l-5 1-12 3c2-2 4-4 6-5 2 0 5-1 6-1z"></path><path d="M509 132c3-2 7-2 10-2l1 1c0 1 0 1-1 1h-5c-1 1-2 1-4 1l2-1c1 0 1 0 2-1l-5 1z" class="J"></path><path d="M520 130h0c2 1 5 1 7 2-3 2-6 3-8 5-1 1-1 1-2 1h-2c-1 0-1-1-2-1h-2v-2l-1 1-1-1-1-1h0l2-1c2 0 3 0 4-1h5c1 0 1 0 1-1l-1-1h1z" class="m"></path><path d="M516 135v-1h4 1c-1 1-1 1-2 1h-3z" class="T"></path><path d="M514 136c1 0 3 0 4 1h1c-1 1-1 1-2 1h-2c-1 0-1-1-2-1l1-1z" class="l"></path><defs><linearGradient id="U" x1="509.146" y1="133.63" x2="513.597" y2="134.516" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#898789"></stop></linearGradient></defs><path fill="url(#U)" d="M510 133c2 0 3 0 4-1 2 1 4 1 6 2h-4v1h-1 0v1h0-1l-1 1h-2v-2l-1 1-1-1-1-1h0l2-1z"></path><path d="M515 135v1h0-1l-1 1h-2v-2h4z" class="h"></path><path d="M511 135v2h2c1 0 1 1 2 1h2l-2 2-1 1-4 2-2 2h-1c-1-1-2-1-4 0l-1-1h0 1v-1-2h0c-1 0-1 0-2-1h1 1 1c1-2 3-2 4-3l2-1 1-1z" class="r"></path><path d="M510 139h4l1 1-1 1c-1-1-2-1-3 0h-1l-1-1 1-1z" class="w"></path><path d="M511 137h2c1 0 1 1 2 1h2l-2 2-1-1h-4v-1l1-1z" class="K"></path><path d="M511 135v2l-1 1h-1c-1 0-2 2-3 2h-2c1-2 3-2 4-3l2-1 1-1z" class="c"></path><path d="M503 141c1 1 2 1 4 1 1 1 2 0 3 1l-2 2h-1c-1-1-2-1-4 0l-1-1h0 1v-1-2h0z" class="Q"></path><path d="M525 110h2c0 2 1 2 2 3 1 0 2 0 3 1h0l-1 1c-1 0-1-1-2 0v1l2 1-1 1h-3v2l-2-1-3-1h-1c-2 0-3 0-5-1h3 0l1-1c-1-1-2 0-3-1h-1l5-1 1-1h0l-2-1c1-1 2-1 2-1l3-1z" class="m"></path><path d="M520 112h5c1 1 1 1 2 1-2 1-4 1-6 1l1-1h0l-2-1z" class="c"></path><path d="M529 113c1 0 2 0 3 1h0l-1 1c-1 0-1-1-2 0v1l2 1-1 1h-3v2l-2-1-3-1v-1h5 0l-2-2h0v-1c2 0 3 0 4-1h0z" class="T"></path><path d="M525 119v-1h2v2l-2-1z" class="G"></path><defs><linearGradient id="V" x1="510.808" y1="125.708" x2="521.749" y2="124.6" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#989697"></stop></linearGradient></defs><path fill="url(#V)" d="M502 128c5-2 9-4 15-4 2 0 4 0 6 1h0 0v1c-1 0-2 0-3 1l1 1c1 0 2 0 2 1h-2-4c-3-1-5-1-8 0-1 0-4 1-6 1h0l-1-1-2 1h-1c1-1 2-2 3-2z"></path><defs><linearGradient id="W" x1="512.22" y1="127.189" x2="518.466" y2="127.483" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#555454"></stop></linearGradient></defs><path fill="url(#W)" d="M507 127c3-1 8-1 12 0 1 0 1 1 2 1s2 0 2 1h-2-4c-3-1-5-1-8 0-1 0-4 1-6 1h0l-1-1c1-1 3-1 5-2z"></path><path d="M507 127c1 1 3 1 4 0-1 1-1 0-2 1v1c-1 0-4 1-6 1h0l-1-1c1-1 3-1 5-2z" class="M"></path><defs><linearGradient id="X" x1="531.269" y1="109.688" x2="536.308" y2="114.33" xlink:href="#B"><stop offset="0" stop-color="#aaa8a9"></stop><stop offset="1" stop-color="#cccbcc"></stop></linearGradient></defs><path fill="url(#X)" d="M532 107h3l4 2v2l-2 2v1h0v1h-3-3l1-1h0c-1-1-2-1-3-1-1-1-2-1-2-3h-2l-3 1 3-3h1c2-1 4-1 6-1z"></path><path d="M534 110c0-1-1-1-1-2 1-1 1-1 2-1l4 2v2l-2 2v-2c-1-2-1-1-3-1z" class="G"></path><path d="M532 107h3c-1 0-1 0-2 1 0 1 1 1 1 2-1 1-3 1-5 1h0l-2-1h-2l-3 1 3-3h1c2-1 4-1 6-1z" class="e"></path><path d="M525 108h1v1c2 0 4-1 5 1h0l-2 1-2-1h-2l-3 1 3-3z" class="o"></path><path d="M522 111l3-3c1 1 2 1 2 2h-2l-3 1z" class="Y"></path><path d="M520 100l1 1 4-3c1 0 1 0 2 1h3 0 2 1v1 1h0 1v1h-1c-1-1-4 0-5 1h-1c-2 1-6 2-8 3-4 2-8 5-11 8-1 0-2 1-3 1l1-1c2-1 4-3 6-4 0-1 1-1 1-2l1-2c1-1 4-5 6-6z" class="B"></path><path d="M527 103h-1c2-2 4-3 7-4v1 1h0 1v1h-1c-1-1-4 0-5 1h-1z" class="E"></path><path d="M520 100l1 1 4-3c1 0 1 0 2 1-5 2-9 7-14 9l1-2c1-1 4-5 6-6z" class="a"></path><path d="M495 117l9-8v2l-17 15c-3 2-7 4-10 7h-1c1 1 1 2 1 3s-1 2-2 3v1l-2 3-1 1v-1h-1 0v-1h-1c-1 1-1 2-1 3 0-1 0-2-1-3h-1l3-3c4-6 10-10 16-14 3-3 6-5 9-8z" class="R"></path><path d="M470 142c1-2 2-4 3-5l2-2s1-1 1-2c1 1 1 2 1 3s-1 2-2 3v1l-2 3-1 1v-1h-1 0v-1h-1z" class="B"></path><path d="M471 142l3-3h1v1l-2 3-1 1v-1h-1 0v-1z" class="Y"></path><path d="M491 148c0-1 2-2 3-2v1l1 1c0 1-1 2-2 2-1 1-2 2-2 3l1 2 1-1c1 2 1 2 2 2l-2 3-4 3-3 3c0-1 0-1-1-2l-1 2-1-1 2-5-1-1h0v-1c0-3 1-4 3-7l2-1s1-1 2-1z" class="J"></path><path d="M494 147l1 1c0 1-1 2-2 2h-1c-1 1-1 1-2 1v-1l1-1v-1h0v1h0 2s1-1 1-2z" class="B"></path><path d="M491 148c0-1 2-2 3-2v1c0 1-1 2-1 2h-2 0v-1h0z" class="I"></path><path d="M489 162s0-1-1-1c1-2 3-3 4-4 0 0 1 1 1 2l-4 3z" class="Y"></path><path d="M489 149v1l-1 2c-1 3-2 4-4 6v-1c0-3 1-4 3-7l2-1z" class="e"></path><path d="M485 159c1-1 2-4 4-5l1-1c-1 2-1 4-2 5-1 2-2 3-3 5l-1 2-1-1 2-5z" class="d"></path><path d="M477 140c2-2 5-5 7-6-1 2-3 3-3 6h1c0 2-2 4-3 5s-1 3-1 3l-1 3c-1 1-2 2-2 3l-2 2-1 2-1 3-1-1v2c0-1 0-2-1-3h0c0-2 0-3 1-5h-1l-1 1v-2c1-1 1-2 1-3l3-6 1-1 2-3v1h1l1-1z" class="B"></path><path d="M472 148h0v-1c0-2 2-3 3-4h1l-1 1v1h3v3l-1 3c-1-2-1-2-2-2v-2c-1 0-2 1-3 1z" class="o"></path><path d="M472 144l1-1 2-3v1h1l1-1 1 1h0v1l-2 1h-1c-1 1-3 2-3 4v1h0c-1 3-2 5-1 8 0 1-1 2-1 4v2c0-1 0-2-1-3h0c0-2 0-3 1-5h-1l-1 1v-2c1-1 1-2 1-3l3-6z" class="S"></path><path d="M472 148c1 0 2-1 3-1v2c1 0 1 0 2 2-1 1-2 2-2 3l-2 2-1 2-1 3-1-1c0-2 1-3 1-4-1-3 0-5 1-8z" class="g"></path><path d="M473 151h1c0 1-1 2-2 3 1 1 1 1 1 2h0-2c0-1 1-2 1-3h-1c1-1 1-2 2-2z" class="G"></path><path d="M494 115v-1c3 0 5-2 7-3-1 2-5 3-6 6-3 3-6 5-9 8-6 4-12 8-16 14-1-1-1-1-3-2 1-1 2-2 2-3l1-1 3-3c0-1 1-2 1-2l1-1c0-1 2-1 2-2 1 0 1-1 1-2h1c0-1 1-1 1-1l1-1v1c3-2 5-3 7-5l2 1c1-1 2-3 4-3z" class="W"></path><path d="M488 117l2 1c-3 3-7 6-10 8-2 1-3 2-4 4h-1v2l-1 1-1-1v-1-1h0c0-1 1-2 1-2l1-1c0-1 2-1 2-2 1 0 1-1 1-2h1c0-1 1-1 1-1l1-1v1c3-2 5-3 7-5z" class="J"></path><path d="M479 123h2 1l-4 4c-1 0-1 0-2 1h-2 0l1-1c0-1 2-1 2-2 1 0 1-1 1-2h1z" class="f"></path><path d="M536 92l2 1 1 1c1 1 1 2 2 3 0 1 1 2 1 4 1 1 1 3 1 5h0v3l-1-1h-1l-1 1h-1l-4-2h-3 1c0-1-1-2-1-3h0v-1h1v-1h1v-1h-1 0v-1-1h-1-2 0-3c-1-1-1-1-2-1l-4 3-1-1c1-1 3-2 5-3 1-1 4-2 5-3h1l5-2z" class="r"></path><path d="M539 94c1 1 1 2 2 3h-1c-1 1-1 2-3 2v1c-1 0-2 0-3-1h0c2-1 2-1 3-2h1 0v-1-1l1-1z" class="w"></path><path d="M534 99c1 1 2 1 3 1h2v1l-2 1c-1 1-2 1-4 1v-1h1v-1h-1 0v-1l1-1z" class="K"></path><path d="M530 94h1c1 0 1 1 2 1-1 1-2 1-2 1v1l5-1h0l-6 3h-3c-1-1-1-1-2-1l-4 3-1-1c1-1 3-2 5-3 1-1 4-2 5-3z" class="M"></path><path d="M530 94h1c1 0 1 1 2 1-1 1-2 1-2 1v1c-1 1-1 1-2 1v-1c0-1 1-2 1-3z" class="B"></path><path d="M537 100v-1c2 0 2-1 3-2h1c0 1 1 2 1 4 1 1 1 3 1 5h0v3l-1-1h-1l-1 1h-1l-4-2h-3 1c0-1-1-2-1-3h0v-1h1c2 0 3 0 4-1l2-1v-1h-2z" class="T"></path><path d="M543 106l-1 1h-1c-1 0 0-1-1-2h-1l1-1 2 2h1 0z" class="O"></path><path d="M537 100v-1c2 0 2-1 3-2h1c0 1 1 2 1 4 1 1 1 3 1 5h-1l-2-2h1v-1h-2l1-1-1-2h-2z" class="G"></path><path d="M533 103c2 0 3 0 4-1v1c0 1 0 2-1 3h-1 0c1 1 2 1 3 2h1c0-1 0-1 1-1l1 1-1 1h-1l-4-2h-3 1c0-1-1-2-1-3h0v-1h1z" class="i"></path><path d="M533 103c2 0 3 0 4-1v1c-1 2-3 2-4 4 0-1-1-2-1-3h0v-1h1z" class="m"></path><path d="M541 108h1l1 1v1h0v1c1 2 1 4 1 6 0 1 0 2-1 2h-2c1 2 1 3 1 5s-1 3-1 4v1h-1v-2h-1-1l-3 2-2 1c-1 1-2 1-3 2l-1-1-2 1c-2-1-5-1-7-2h0-2l1-1h2 2c0-1-1-1-2-1l-1-1c1-1 2-1 3-1v-1h0 0c-2-1-4-1-6-1h0 3v-1l-8-1 6-1c2-1 2-2 3-3h1l3 1 2 1v-2h3l1-1-2-1v-1c1-1 1 0 2 0h3 3v-1h0v-1l2-2v-2h1l1-1z" class="q"></path><path d="M541 108h1v4c-1 0-1 0-1-1l-1-2 1-1z" class="O"></path><path d="M536 124c1 0 2 1 2 2h0l-2 1c0-1 0-1-1-2l1-1z" class="e"></path><path d="M532 123h2l2 1-1 1h-1c-1-1-2-1-2-2z" class="T"></path><path d="M525 123s1 1 2 1v1h0-4 0 0 0 1c0-1 0-1 1-2z" class="m"></path><path d="M540 109l1 2v2l-2 1v-2-1-2h1z" class="e"></path><path d="M539 111v1 2h0v1c-1 0-2 0-2-1h0v-1l2-2z" class="T"></path><path d="M533 129c-1-1-2-1-3-2l1-1c2 0 4 1 5 1-1 1-2 1-3 2z" class="L"></path><path d="M541 119h0c1 2 1 3 1 5-1 0-1 0-2-1v-3s0-1 1-1z" class="H"></path><path d="M520 123c1-1 1 0 2 0h3c-1 1-1 1-1 2h-1 0c-2-1-4-1-6-1h0 3v-1z" class="K"></path><path d="M536 127h2l-3 2-2 1c-1 1-2 1-3 2l-1-1c1-1 2-2 4-2 1-1 2-1 3-2z" class="F"></path><path d="M521 118h1l3 1 2 1 1 1h-1c-3-1-6-1-9 0 2-1 2-2 3-3z" class="R"></path><path d="M541 119c-1 0-1-1-2-1 1-1 2 0 3-1v-3-3s0-1 1-1h0v1c1 2 1 4 1 6 0 1 0 2-1 2h-2 0z" class="g"></path><path d="M531 117l-2-1v-1c1-1 1 0 2 0h3c2 1 4 2 5 3l-1 1h-1-2c-1 0-1 0-2-1h0c-1-1-2-1-2-1z" class="L"></path><path d="M531 117s1 0 2 1h0c1 1 1 1 2 1v1h-1v1l1 1-1 1h-2c-1-1-1-1-2-1l-1-1h-1l-1-1v-2h3l1-1z" class="H"></path><path d="M148 616v21h2 2l1-3h2 0c6-1 13 0 20 0 3 0 6-1 9-1 1 0 2 1 4 1 9 0 20 1 29 0 3-1 6-2 9-2h0l-2 1v1h3c6 1 13 0 20 0 4 0 9 1 13 1h14c1 0 3 0 3-1 1 0 1-2 2-2l1-1v1-1l1 1h2 0c2 0 5 1 8 1l7 2h25 17c6 0 12 1 19 1h10c3 0 5-2 8-1l1-1v-1-1c2 1 3 3 5 3 4 1 10 1 14 1h5 4 12v1c4 0 9-1 13 0h0c-2 1-3 0-5 0h-3 0c-1 1-3 0-4 0 2 1 5 0 8 1h-11-7l-10 1h0 3v1c-1 0-1 1-1 1v1c1 1 1 2 2 4v-1c1 1 1 1 2 1h1 0 0v1l-1 1c0 1 1 2 1 3 1 1 1 1 1 2h-6-2c0 1 1 1 2 1-1 0-1 1-1 0-3 0-7 1-10 0h-17c-1 1-3 0-5 0h-12-45-90-6-11 0 0c-2-1-5 0-7 0h-20-26c-1-5-3-10-5-14h-1l-1-2v-15c1-1 1-2 2-2 1-2 1-3 1-4l1-1z" class="R"></path><path d="M153 637h4l4 1 1 1h-7v-1s-1-1-2-1z" class="j"></path><path d="M245 652c3-1 7-1 10-1-1 2-3 1-5 2h-6c1 0 2-1 3-1h-2z" class="C"></path><path d="M228 649l14 1c4 0 8-1 12 0h-3c-9 1-18 1-27 0 1-1 3-1 4-1z" class="L"></path><path d="M149 642h1l1 1h0 4-3c1 1 3 0 3 1h-3c0 2 1 4 2 6h1c2 1 6 0 9 0h13v1h-24c-2-3-3-6-4-9z" class="V"></path><path d="M157 640h10c3 0 7-1 10 0h6 10 6c1 1 3 1 4 1h13c2 0 5-1 7 1h-1-4-18c-2-2-6 0-9-1h-11c1 1 3 1 4 1h7v1h-12-3c-2 0-3 0-5-1h-3-1v1h-5-11 0l-1-1h-1v-2c3 1 6 0 8 0z" class="j"></path><path d="M149 642v-2c3 1 6 0 8 0h1c2 0 6 0 7 1h0c-3 2-12-1-14 2h0l-1-1h-1z" class="P"></path><path d="M148 616v21h2 2 1c1 0 2 1 2 1v1h-7c0 3 2 11 5 13 3 1 9 0 13 0h33 46 2c-1 0-2 1-3 1l-40 1c-2-1-5 0-7 0h-20-26c-1-5-3-10-5-14h-1l-1-2v-15c1-1 1-2 2-2 1-2 1-3 1-4l1-1z" class="Z"></path><path d="M148 616v21l-1-1c0-2 1-14 0-15-3 3-1 11-1 16-1 1 0 2 0 3h-1l-1-2v-15c1-1 1-2 2-2 1-2 1-3 1-4l1-1z" class="E"></path><path d="M167 643v-1h1 3c2 1 3 1 5 1h3s1 1 2 1c2 0 6-1 7 0 2 0 4 0 5 1h4 19c2 0 6-1 7 0s1 2 3 2h7-5v1h2c-1 0-3 0-4 1h2c-1 0-3 0-4 1-2 0-3 0-4 1-6-1-12 0-18 0h-18-7v-1h-13c-3 0-7 1-9 0h-1c-1-2-2-4-2-6h3c0-1-2 0-3-1h3-4 0 0 11 5z" class="n"></path><path d="M158 649c-1 0-3 0-4-1h0 4 3c-1 1-2 1-3 1z" class="t"></path><path d="M167 643c2 0 4 0 5 1v1h-3c-3-1-6 0-9-1 2 0 5 1 7 0v-1z" class="j"></path><path d="M157 647h-1c-1 0-2 0-3-1v-1c1 1 2 0 3 0 3 1 5 1 8 1h0c-1 1-7 0-7 1z" class="N"></path><path d="M151 643h11 5 0v1c-2 1-5 0-7 0h-8 3c0-1-2 0-3-1h3-4 0 0z" class="F"></path><path d="M167 648l29-1 1 2c-1 0-3-1-4 0h-4-12 8c-1-1-4-1-5-1h-13z" class="T"></path><path d="M158 648h9 13c1 0 4 0 5 1h-8-19c1 0 2 0 3-1h-3z" class="Z"></path><path d="M164 646h21c1 1 4 0 5 0v1h-33c0-1 6 0 7-1h0z" class="P"></path><path d="M223 645c1 1 1 2 3 2h7-5v1h-28l-3 1-1-2h7 12 4v-1c2 0 3 0 4-1z" class="e"></path><path d="M203 647h11l1 1h-10-5l-3 1-1-2h7z" class="l"></path><path d="M155 650h23 14 4c5-1 9-1 14-1 1 0 4 0 5 1h0l-13 1h-18-7v-1h-13c-3 0-7 1-9 0z" class="G"></path><path d="M167 643v-1h1 3c2 1 3 1 5 1h3s1 1 2 1c2 0 6-1 7 0 2 0 4 0 5 1h4 19c2 0 6-1 7 0-1 1-2 1-4 1h-9-12-1c-1 1-3 0-4 0-1 1-1 1-3 1v-1c-1 0-4 1-5 0h5c-2-1-5-1-7-1h-14 3v-1c-1-1-3-1-5-1h0z" class="l"></path><path d="M259 639l27 1c8-1 17 0 25 0h12v1h0c2 0 3 0 5 1h-6 0c1 0 2 0 4 1h0 0c-1 0-2 0-3 1l-9-1c-1 1-1 1-2 1l41 1-1 1h-34c-4 0-7 0-10 1h-3-13c-3 0-7-1-10 0h-1v-1h-1v1h-2-6-23-16-7c-2 0-2-1-3-2s-5 0-7 0h-19-4c-1-1-3-1-5-1-1-1-5 0-7 0-1 0-2-1-2-1h12v-1h-7c-1 0-3 0-4-1h11c3 1 7-1 9 1h18 4 1 0v-1h6 18c3 0 13 1 15-1h0-3v-1z" class="E"></path><path d="M294 643c7 0 13-1 20 0-1 1-1 1-2 1h-20-2v-1h4z" class="j"></path><path d="M235 645c7-1 13 0 20 0h26 0v1h-1-25c-1-1-16-1-20-1z" class="l"></path><path d="M266 643c4 0 9-1 13-1 3 0 5 1 7 1 3 0 6-1 8 0h-4v1h2-9-57v1h-1c-1 0-1-1-2-2 2-1 13 0 16 0h27z" class="u"></path><path d="M286 640c8-1 17 0 25 0h12v1h0c2 0 3 0 5 1h-6 0c1 0 2 0 4 1h0 0c-1 0-2 0-3 1l-9-1c-7-1-13 0-20 0-2-1-5 0-8 0-2 0-4-1-7-1-4 0-9 1-13 1 1-1 3-1 4-1l-1-1c2-1 17 1 20 0v-1h-3z" class="o"></path><path d="M259 639l27 1h3v1c-3 1-18-1-20 0l1 1c-1 0-3 0-4 1h-27c-3 0-14-1-16 0 1 1 1 2 2 2h1 9c4 0 19 0 20 1h25v1h-2-6-23-16-7c-2 0-2-1-3-2s-5 0-7 0h-19-4c-1-1-3-1-5-1-1-1-5 0-7 0-1 0-2-1-2-1h12v-1h-7c-1 0-3 0-4-1h11c3 1 7-1 9 1h18 4 1 0v-1h6 18c3 0 13 1 15-1h0-3v-1z" class="y"></path><path d="M226 645h9c4 0 19 0 20 1-4 0-28 1-30-1h1z" class="P"></path><path d="M179 643h12v-1h-7c-1 0-3 0-4-1h11c3 1 7-1 9 1h1c4 2 11 1 16 1h4v1c-8 0-18 1-26 0h-1c1 1 2 1 3 1h-4c-1-1-3-1-5-1-1-1-5 0-7 0-1 0-2-1-2-1z" class="u"></path><path d="M259 639l27 1h3v1c-3 1-18-1-20 0l-12 1h-23c-3 0-8 1-11 0v-1h6 18c3 0 13 1 15-1h0-3v-1z" class="l"></path><path d="M399 641c1 0 1 0 2 1s1 2 2 4v-1c1 1 1 1 2 1h1 0 0v1l-1 1c0 1 1 2 1 3 1 1 1 1 1 2h-6-2c0 1 1 1 2 1-1 0-1 1-1 0-3 0-7 1-10 0h-17c-1 1-3 0-5 0h-12-45-90-6-11 0 0l40-1h6c2-1 4 0 5-2h0 21 0c-1-1-6-1-8-1h-17 3c-4-1-8 0-12 0l-14-1h-2c1-1 3-1 4-1h-2v-1h5 16 23 6 2v-1h1v1h1c3-1 7 0 10 0h13 3c3-1 6-1 10-1h34l1-1h9 17 7c2-1 4 0 6-1h0v-1h-5l5-1 1 1 1 1h1 0c1 1 1 2 1 2v-2l3-3h0z" class="y"></path><path d="M276 651h10v-1c2-1 8 0 10 0v1h3 8 0 13v1c-1 1-15 0-16 1h-40-4-10c2-1 4 0 5-2h0 21 0z" class="l"></path><path d="M276 651h2l-3 1h-12 5 9c0 1-2 0-2 1-4 0-7-1-11 0h-4-10c2-1 4 0 5-2h0 21z" class="L"></path><path d="M304 649h1 10l18-1c-1 0-2 0-2 1h5 12c3 0 5 0 8 1 1 0 3-1 4 0h9 6c1 1 3 0 5 1h3 5 1 4 0 1c1 0 2-1 3-1h3 2c1 0 3 0 4 1s1 1 1 2h-6-2c0 1 1 1 2 1-1 0-1 1-1 0-3 0-7 1-10 0h0l-5-1h-12-6-8-55c1-1 15 0 16-1v-1h-13s-2 0-3-1v-1z" class="d"></path><path d="M359 653c-2-1-4-1-6-1v-1h5c5 1 12 1 17 1 1 0 3-1 5 0h-19 0c2 0 5 0 6 1h-8z" class="q"></path><path d="M380 652h7 0c2 1 4-1 6 1h-1-13c-2 0-4-1-6 0h-6c-1-1-4-1-6-1h0 19z" class="N"></path><path d="M393 651h1c1 0 2-1 3-1h3 2c1 0 3 0 4 1s1 1 1 2h-6-2c0 1 1 1 2 1-1 0-1 1-1 0-3 0-7 1-10 0h0l-5-1h-12c2-1 4 0 6 0h13 1c-2-2-4 0-6-1h0c2 0 4 0 6-1h0z" class="F"></path><path d="M385 653h14c0 1 1 1 2 1-1 0-1 1-1 0-3 0-7 1-10 0h0l-5-1z" class="U"></path><path d="M399 641c1 0 1 0 2 1s1 2 2 4v-1c1 1 1 1 2 1h1 0 0v1l-1 1c0 1 1 2 1 3-1-1-3-1-4-1h-2-3c-1 0-2 1-3 1h-1 0-4-1-5-3c-2-1-4 0-5-1h-6-9c-1-1-3 0-4 0-3-1-5-1-8-1h-12-5c0-1 1-1 2-1l-18 1h-10-1v1c1 1 3 1 3 1h0-8-3v-1c-2 0-8-1-10 0v1h-10c-1-1-6-1-8-1h-17 3c-4-1-8 0-12 0l-14-1h-2c1-1 3-1 4-1h-2v-1h5 16 23 6 2v-1h1v1h1c3-1 7 0 10 0h13 3c3-1 6-1 10-1h34l1-1h9 17 7c2-1 4 0 6-1h0v-1h-5l5-1 1 1 1 1h1 0c1 1 1 2 1 2v-2l3-3h0z" class="y"></path><path d="M393 643l1 1h1v4h0c-1 0-1-1-1-1-1-1-1-2-1-4z" class="L"></path><path d="M398 649l1-2h4c-1 1-2 2-3 2h-2z" class="O"></path><path d="M348 649v-1h10l13 1c-1 1-10 0-12 0h1v1c-1-1-3 0-4 0-3-1-5-1-8-1z" class="b"></path><path d="M333 648h17 8-10v1h-12-5c0-1 1-1 2-1z" class="g"></path><path d="M399 641c1 0 1 0 2 1s1 2 2 4v1h-4l-1 2h-1c-1-1-1-1-1-3v-2l3-3h0z" class="C"></path><path d="M371 649c3 0 8 0 10-1 3-1 9 0 12 0 0 1 1 2 0 3h0-4-1-5-3c-2-1-4 0-5-1h-6-9v-1h-1c2 0 11 1 12 0z" class="L"></path><path d="M251 649h11 24 14 4v1c1 1 3 1 3 1h0-8-3v-1c-2 0-8-1-10 0v1h-10c-1-1-6-1-8-1h-17 3c-4-1-8 0-12 0 3-1 6-1 9-1z" class="g"></path><path d="M242 650c3-1 6-1 9-1h23v1h-6-17 3c-4-1-8 0-12 0z" class="b"></path><path d="M352 646h27c4 0 7-1 10-1 1 0 2 0 2 1h1v1c-2 1-6 0-8 0h-50l-24 1h-52c-9 0-19 1-28 0h-2v-1h5 16 23 6 2v-1h1v1h1c3-1 7 0 10 0h13 3c3-1 6-1 10-1h34z" class="u"></path><path d="M280 631l1 1h2 0c2 0 5 1 8 1l7 2h25 17c6 0 12 1 19 1h10c3 0 5-2 8-1l1-1v-1-1c2 1 3 3 5 3 4 1 10 1 14 1h5 4 12v1c4 0 9-1 13 0h0c-2 1-3 0-5 0h-3 0c-1 1-3 0-4 0 2 1 5 0 8 1h-11-7l-10 1h0 3v1c-1 0-1 1-1 1v1c-1-1-1-1-2-1h0l-3 3v2s0-1-1-2h0-1l-1-1-1-1-5 1h5v1h0c-2 1-4 0-6 1h-7-17-9l-41-1c1 0 1 0 2-1l9 1c1-1 2-1 3-1h0 0c-2-1-3-1-4-1h0 6c-2-1-3-1-5-1h0v-1h-12c-8 0-17-1-25 0l-27-1v1l-69-1c-6 0-11-1-17-1-3 0-7 1-11 1l-1-1-4-1h-4-1l1-3h2 0c6-1 13 0 20 0 3 0 6-1 9-1 1 0 2 1 4 1 9 0 20 1 29 0 3-1 6-2 9-2h0l-2 1v1h3c6 1 13 0 20 0 4 0 9 1 13 1h14c1 0 3 0 3-1 1 0 1-2 2-2l1-1v1-1z" class="v"></path><path d="M280 631l1 1h2 0c2 0 5 1 8 1l7 2h-11c-2-1-4 0-6-1-1 0-1-1-1-2v-1z" class="T"></path><path d="M155 634c6-1 13 0 20 0h-1c-4 1-10-1-14 1h0l1 1c1 1 6 1 8 1h-12-4-1l1-3h2 0z" class="R"></path><path d="M169 637l18 1h11-8v1c-6 0-11-1-17-1-3 0-7 1-11 1l-1-1-4-1h12z" class="q"></path><path d="M198 638l61 1v1l-69-1v-1h8z" class="L"></path><defs><linearGradient id="Y" x1="334.851" y1="646.188" x2="339.38" y2="636.993" xlink:href="#B"><stop offset="0" stop-color="#707172"></stop><stop offset="1" stop-color="#8d898a"></stop></linearGradient></defs><path fill="url(#Y)" d="M399 639h3v1c-1 0-1 1-1 1v1c-1-1-1-1-2-1h0l-3 3v2s0-1-1-2h0-1l-1-1-1-1-5 1h5v1h0c-2 1-4 0-6 1h-7-17-9l-41-1c1 0 1 0 2-1l9 1c1-1 2-1 3-1h0 0c-2-1-3-1-4-1h0 6c-2-1-3-1-5-1h0v-1h42 3c4-1 8-1 12-1h19z"></path><path d="M362 642l27-1h3v1l-5 1c-1-1-3-1-4 0h-33v-1h12 0z" class="P"></path><path d="M383 643c1-1 3-1 4 0h5v1h0c-2 1-4 0-6 1h-7-17-9l-41-1c1 0 1 0 2-1l9 1h35c6 0 13 1 18 0h6l1-1z" class="G"></path><path d="M399 639h3v1c-1 0-1 1-1 1v1c-1-1-1-1-2-1h0l-3 3v2s0-1-1-2h0-1l-1-1-1-1v-1h-3l-27 1h-12v-1h12c1-1 2 0 3 0v-1h3c4-1 8-1 12-1h19z" class="E"></path><path d="M389 641h5 5 0l-3 3v2s0-1-1-2h0-1l-1-1-1-1v-1h-3z" class="H"></path><defs><linearGradient id="Z" x1="487.718" y1="275.679" x2="536.163" y2="309.264" xlink:href="#B"><stop offset="0" stop-color="#0c0b0b"></stop><stop offset="1" stop-color="#323132"></stop></linearGradient></defs><path fill="url(#Z)" d="M466 234c1-1 1-2 2-3v1h0 1c1 0 1 1 2 1h1c0-1 1-1 2-1s4 3 5 4h-1c0 1 1 2 2 2 1 1 2 1 2 3h0l1 1h1c1 1 2 1 4 1h0l-3 1v1h4v-1h2l1 1h2c0 1 1 1 0 2 2 1 4 1 6 1v1h-8s0 1 1 1c0 0-1 0-1 1h0v1 3c2-1 3-1 4 0h0 6 1 2l17 3c6 1 11 3 17 5 2 1 5 2 7 3s11 5 11 7h0v1c1 1 2 2 4 2l3 2v1c-1 1-2 1-3 0v1h0c0 1 0 2 1 2v1 2c1 0 1 2 1 3v1h0v1 1l1-1h1v1h-1-1v1c1 1 2 1 2 3v1h-2 0c-1-1-1-1-1-2h0l-5-6c-1 1-1 1-1 3 1 1 2 2 2 4v1h0v1 1c-1 2-1 5-1 7 0 1 0 1-1 1v-5h-1c-1 1-1 5-1 6 1 1 1 1 1 2 1 2 2 4 3 5v1l1 1h-1c0 1-1 3-1 4v1 1c0 1 0 1-1 2 0-3 2-6 1-8 0-2-1-4-2-5l-1 1 1 6c0 2-1 3-1 5 0 0 0 1-1 1v6l-2 8c-1 1-2 3-3 4-1-2-1-3-1-5h-1 0-1c-1-1-1-4-3-6l-1-1 1-1v1l2-2c-2-2-1-3-1-5l-1-3h-1v1l-1-1v-4 4h-1v1h-1v3c1 0 1 1 2 1v3 2c-1 1-1 2-2 2v1c-1 1-1 2-1 4h-1l-1 2c-1-2-1-5-2-7v-3l-1-1c0 2 1 3 0 4-1-1-1-1-1-2-1-1-1-2-2-3 0-2-1-3-2-5 0 1 0 1-1 1l-7-12s-2-2-3-2c0 1 0 1 1 2l1 2v1c-1-1-1-2-1-2-2-2-3-4-5-6l-1 1c-1-1-2-1-2-1-1-1-1-1-2-1v1l-2-1v1l1 1h-2l-1 1c-1 0-1 1-2 2l-3-2v-1l1-1-1-1h0 1c-1-2-3-3-3-5h-1v-1-2s0-1-1-1c-1-1-2-1-3-1 0-1-1-1-1-1h-1-1v-1h-1l-2-2h0c-1 0-2-1-2-1h1 1 0c-2-2-2-2-4-3h-1c1-1 2-1 3-1h1l-2-2-1 1c-1 0-2-1-2-1-1-1-1-1-2-1-1 1-2 1-3 1 0 0-1 0-1-1 1-1 1-2 2-2h2v-1c0-1 0-1-1-2-1 0-1-1-2-1l1-1c-2-1-3-1-5-1h-1c0-1 0-1-1-1-1-1-2-1-3-1l-1-1-4-1h0v-1h0c0-1 0-1-1-1s-2-1-3-1h0c-4-1-7-4-9-7v-1h2l1-1c0-1 0-1 1-1 0-1 0-2 1-3l-2 1c0-1 0-2 1-3l1-1v-1s1-1 1-2h-1l-1-2-1-1 1-1c2 0 3-1 4 0 1 0 2 0 3-1 1 0 1-2 1-2 0-1 0 0 1-1v-2 1h2v-1c1 0 1 0 1-1v-3h2s0-1 1-1h0z"></path><path d="M499 279h1c1 1 1 1 1 2-1 0-1-1-2-1v-1h0z" class="f"></path><path d="M494 293c-1 0-1-1-2-2h1c1 0 2 0 3 1l-2 1z" class="B"></path><path d="M489 294l-1-1-2-1v-1c1 0 1 0 2 1 1 0 1 1 2 1 1 1 2 1 3 2 1-1 1 0 1-1h1l1 2h-1 0-1c-1-1-2-1-3-1 0-1-1-1-1-1h-1z" class="I"></path><path d="M515 278c1-1 1-1 2-1 2 1 5 2 7 4-1 0-1 1-2 1l-7-4z" class="C"></path><path d="M502 285h1v-1c1 0 1-1 1-2l1-1c1 3 0 4 3 6h1 0c0 1 1 1 1 2h-1c-1 0-2-1-3-1l-3-3h-1zm-11-13l3 1c0-1 0-1 1-1h1c1 0 2 0 3 1h0-1c1 1 1 1 2 1l-1 1h-1 0c-1-1-1-1-2-1h-1l-1 2c-1-2-3-2-5-4h2z" class="f"></path><path d="M505 271c2 0 12 5 12 6-1 0-1 0-2 1-4-2-7-5-11-6l1-1z" class="j"></path><path d="M496 292c2 0 4 2 6 3h0v1 1l-4-1v1l-2-1-1-2h-1v-1l2-1z" class="S"></path><path d="M495 294h1c1 0 1 1 1 1h3 2 0v1 1l-4-1v1l-2-1-1-2z" class="J"></path><path d="M521 298h1c-1-2-2-3-2-5l5 6 3 3 1-1c1 1 2 2 2 3l3 4h-1l-6-6c-2-1-4-2-6-4z" class="k"></path><path d="M524 281c2 2 6 4 8 6s14 12 14 13v1c-7-7-16-13-24-19 1 0 1-1 2-1z" class="j"></path><defs><linearGradient id="a" x1="509.901" y1="299.318" x2="512.045" y2="308.274" xlink:href="#B"><stop offset="0" stop-color="#626060"></stop><stop offset="1" stop-color="#797779"></stop></linearGradient></defs><path fill="url(#a)" d="M505 297c4 3 8 6 11 10l3 4s-2-2-3-2c0 1 0 1 1 2l1 2v1c-1-1-1-2-1-2-2-2-3-4-5-6s-4-3-5-5l-3-3 1-1z"></path><path d="M501 292c-1 0-2-1-2-1-1 0-1-1-2-1s-4-1-4-2l-2-2v-1-1c2 1 2 1 3 2l1 1c1-1 3 0 4-1s0-1 0-2h1c1 0 1 0 2 1h-1c1 2 0 4 1 6l-1 1z" class="a"></path><path d="M544 306c1 0 0-1 0-1v-1c-1-1-1-2-2-3h0v-1 1l1-1c1 1 2 3 3 4h0c1 1 2 2 2 3 0 2 0 3 1 5v1l-1 1v3h-1v1-1c-1 1-1 1-1 2-1-2-1-5-2-8l-1-4c1 0 1 0 1-1z" class="D"></path><path d="M543 307c1 0 1 0 1-1 1 1 2 6 3 7h0l1 1v3h-1v1-1c-1 1-1 1-1 2-1-2-1-5-2-8l-1-4z" class="E"></path><path d="M547 313h0l1 1v3h-1v-4zm-16-19c1 0 1 0 2 1v-1l-1-1h1c0 1 0 1 1 2l3 3c1 1 2 1 3 2s2 3 3 5v2h0l1 4v3c-1-1-2-3-3-4v2h0v-2h-1l-1-1-1-2v-2-1c0-1 0-1-1-2l-6-8z" class="M"></path><path d="M496 296l2 1v-1l4 1v-1-1l3 2-1 1 3 3c1 2 3 3 5 5l-1 1c-1-1-2-1-2-1-1-1-1-1-2-1v1l-2-1v1l1 1h-2l-1 1c-1 0-1 1-2 2l-3-2v-1l1-1-1-1h0 1c-1-2-3-3-3-5h-1v-1-2s0-1-1-1h1 0 1z" class="R"></path><path d="M502 295l3 2-1 1v1c-1 0-2-1-2-2v-1-1z" class="c"></path><defs><linearGradient id="b" x1="495.679" y1="294.961" x2="503.673" y2="304.039" xlink:href="#B"><stop offset="0" stop-color="#262726"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#b)" d="M496 296l2 1c3 3 6 4 9 8-3-1-5-2-7-4h0c-1 0-2-1-2-1 0-1-1-1-2-2h0l-1 2v-1-2s0-1-1-1h1 0 1z"></path><path d="M495 300l1-2h0c1 1 2 1 2 2 0 0 1 1 2 1h0v1c1 1 3 4 5 4l1 1h-2l-1 1c-1 0-1 1-2 2l-3-2v-1l1-1-1-1h0 1c-1-2-3-3-3-5h-1z" class="M"></path><path d="M498 305c2 0 3 1 4 2l-1 1v-1c-1 0-1 0-2-1l-1-1z" class="D"></path><path d="M495 300l1-2h0c1 1 2 1 2 2 0 0 1 1 2 1h0v1c1 1 3 4 5 4l1 1h-2l-1-1c-2-1-4-3-6-4 0-1-1-2-1-2h-1zm28-6c-1-1-3-2-4-4h0l-2-2c0-1-1-2-2-3v-1h0v-1c1 1 2 2 4 3 1 1 3 2 4 4h1c0-1-1-1-1-2 1 1 2 3 4 3 2 1 3 1 4 3l6 8c1 1 1 1 1 2v1 2h0l-1 1h-2c0-1 0-1-1-2v-1c-1-1-1-1-2-1h-1c0-1-1-2-2-3l-1-1c-1-3-3-4-5-6z" class="U"></path><path d="M523 294h1 2 0l-1-2 1-1 2 2-1 1c0 2 1 2 2 4l-1 2c-1-3-3-4-5-6z" class="Q"></path><path d="M528 293c2 2 3 4 5 6v1l1 2c1 1 2 3 4 4v1l-1 1h-2c0-1 0-1-1-2v-1c-1-1-1-1-2-1h-1c0-1-1-2-2-3l-1-1 1-2c-1-2-2-2-2-4l1-1z" class="R"></path><path d="M533 300l1 2-1 1c-1-1-2-1-3-2l2-1h1z" class="E"></path><path d="M530 301c-1 0-1-1-1-2v-1h1c0 1 1 2 2 2l-2 1z" class="Y"></path><path d="M532 282c2 3 5 5 7 8l1 1v-1-1-1c2 2 3 4 5 6l1 1v-3l-1-2c1 0 1 0 1 1 1 0 1 1 1 1l1 2c0 1 0 1 1 2h0c0 1 1 3 2 5v-2-2c1 0 1 0 2 1s1 2 2 2h1v1h-1c-1 1-1 5-1 6 1 1 1 1 1 2-1 0-2-1-3-2-2 0-5-4-6-6v-1c0-1-12-11-14-13h2v-1l-3-4h1z" class="r"></path><path d="M545 290c1 0 1 0 1 1 1 0 1 1 1 1l1 2c0 1 0 1 1 2h0-1v1 1c1 1 1 2 1 4 0-1-1-1-1-1 0-1-2-3-2-4-1-1-1-2-1-3l1 1v-3l-1-2z" class="M"></path><path d="M540 288c2 2 3 4 5 6 0 1 0 2 1 3-2-1-6-4-7-7l1 1v-1-1-1z" class="S"></path><path d="M551 297c1 0 1 0 2 1s1 2 2 2h1v1h-1c-1 1-1 5-1 6-1-1-1-2-2-3v-2s-1 0-1-1v-2-2z" class="d"></path><path d="M539 309l1 1h1v2h0v-2c1 1 2 3 3 4v-3c1 3 1 6 2 8 0-1 0-1 1-2v1-1h1l1 4c0 5 0 10-2 16h-1 0-1c-1-1-1-4-3-6l-1-1 1-1v1l2-2c-2-2-1-3-1-5l-1-3h-1v1l-1-1v-4c0-1-1-4-2-5 1-1 1-1 1-2z" class="l"></path><path d="M548 317l1 4c0 5 0 10-2 16h-1 0v-1c3-5 2-12 1-18v-1h1z" class="D"></path><path d="M541 312v-2c1 1 2 3 3 4v-3c1 3 1 6 2 8v6c1 1 1 5 0 7v-5c0-3-1-7-2-10h-1s-2-4-2-5z" class="c"></path><path d="M539 309l1 1h1v2h0c0 1 2 5 2 5 1 4 2 8 1 11-2-2-1-3-1-5l-1-3h-1v1l-1-1v-4c0-1-1-4-2-5 1-1 1-1 1-2z" class="S"></path><path d="M546 304c3 0 3 1 5 3l4 4-1 1 1 6c0 2-1 3-1 5 0 0 0 1-1 1v6l-2 8c-1 1-2 3-3 4-1-2-1-3-1-5 2-6 2-11 2-16l-1-4v-3l1-1v-1c-1-2-1-3-1-5 0-1-1-2-2-3z" class="K"></path><path d="M551 307l4 4-1 1 1 6c0 2-1 3-1 5 0 0 0 1-1 1v6c0-2 0-5-1-7 0-2-1-5-1-7 1-2 0-3 0-4-1-1-1-1 0-1h1c-1-1-1-2-2-3h0l1-1z" class="L"></path><path d="M549 321v-3h1c2 4 2 11 1 15 0 1-1 3 0 5-1 1-2 3-3 4-1-2-1-3-1-5 2-6 2-11 2-16z" class="V"></path><path d="M487 271l2 1h0c2 2 4 2 5 4l1 1c0 1 2 1 3 2-3 0-5-1-8-1h-1l1 1c1 1 3 2 4 2l1 1h-1 0l-6-1c-1 0-1 0-1 1l-1 1c-1 0-2 0-2-1h0-3c0 1 0 2 1 2l-1 1c-1 0-2-1-2-1-1-1-1-1-2-1-1 1-2 1-3 1 0 0-1 0-1-1 1-1 1-2 2-2h2v-1c0-1 0-1-1-2-1 0-1-1-2-1l1-1 2-1h0c2 0 3-2 5-2h0c2-1 2-1 4 0 0-1 1-1 1-2z" class="r"></path><path d="M487 271l2 1v3l2 2h0c-2 0-3 0-4-1v1h0c1 1 1 1 1 2l-1 1c-1-1-1-1-2-1 0 1 0 1 1 2h-1l-1 1h0-3v-1h2v-1l1-1c-1-1-1-1-1-2h0c1 0 1 0 2-1h0c-1-1-2-3-3-3h0c2-1 2-1 4 0 0-1 1-1 1-2z" class="u"></path><path d="M487 271l2 1v3c-1-1-2-1-3-2 0-1 1-1 1-2z" class="d"></path><path d="M482 273c1 0 2 2 3 3h0c-1 1-1 1-2 1h0c0 1 0 1 1 2l-1 1v1h-2v1c0 1 0 2 1 2l-1 1c-1 0-2-1-2-1-1-1-1-1-2-1-1 1-2 1-3 1 0 0-1 0-1-1 1-1 1-2 2-2h2v-1c0-1 0-1-1-2-1 0-1-1-2-1l1-1 2-1h0c2 0 3-2 5-2z" class="b"></path><path d="M477 275c1 0 1 1 2 1h1l1 1h0c-1 2-1 3-2 5h-4c0 1 0 1-1 2 0 0-1 0-1-1 1-1 1-2 2-2h2v-1c0-1 0-1-1-2-1 0-1-1-2-1l1-1 2-1z" class="F"></path><path d="M496 260h1c2 2 4 2 6 3 1-1 0-1 0-1l1-1 9 3 15 6v2c1 2 4 4 5 5 3 3 7 6 9 10l1 1c0 1 1 2 2 2h0l1 2v3l-1-1c-2-2-3-4-5-6v1 1 1l-1-1c-2-3-5-5-7-8h-1l3 4v1h-2c-2-2-6-4-8-6s-5-3-7-4c0-1-10-6-12-6 0-1-1-1-2-1l-7-4c-1 0-3-1-4-2l-2-2c0-2 4-1 6-2h0z" class="x"></path><path d="M526 275l2 2h0c1 1 1 3 2 3l2 2h-1c-2 0-4-4-5-5v-2z" class="S"></path><path d="M516 270c4 1 8 3 10 5v2c-3-3-7-4-10-6v-1z" class="k"></path><path d="M501 264c2 0 4 0 6 2h1l1-1c2 0 2 2 3 3 2 1 3 1 4 2v1c-5-2-12-4-15-7z" class="D"></path><path d="M509 265c2 0 2 2 3 3-2 0-4-1-6-2h1 1l1-1z" class="I"></path><path d="M496 260h1c2 2 4 2 6 3 2 0 4 1 6 2l-1 1h-1c-2-2-4-2-6-2-1-1-3-2-4-2h0c0 1 1 2 1 3h-1-1v1c-1 0-3-1-4-2l-2-2c0-2 4-1 6-2h0z" class="Q"></path><path d="M504 261l9 3 15 6v2c1 2 4 4 5 5 3 3 7 6 9 10l1 1c0 1 1 2 2 2h0l1 2v3l-1-1c-2-2-3-4-5-6v1 1 1l-1-1c-2-3-5-5-7-8l-2-2c-1 0-1-2-2-3h0l-2-2c-2-2-6-4-10-5-1-1-2-1-4-2-1-1-1-3-3-3-2-1-4-2-6-2 1-1 0-1 0-1l1-1z" class="z"></path><path d="M528 277c4 3 9 7 12 11v1 1 1l-1-1c-2-3-5-5-7-8l-2-2c-1 0-1-2-2-3z" class="D"></path><path d="M501 292l1-1c-1-2 0-4-1-6h1 0 1l3 3c1 0 2 1 3 1l6 6c2 0 3 1 4 2l2 1c2 2 4 3 6 4l6 6h1l-3-4h1c1 0 1 0 2 1v1c1 1 1 1 1 2h2l1-1h0l1 2c0 1 0 1-1 2 1 1 2 4 2 5v4h-1v1h-1v3c1 0 1 1 2 1v3 2c-1 1-1 2-2 2v1c-1 1-1 2-1 4h-1l-1 2c-1-2-1-5-2-7v-3l-1-1c0 2 1 3 0 4-1-1-1-1-1-2-1-1-1-2-2-3 0-2-1-3-2-5 0 1 0 1-1 1l-7-12-3-4h2l-1-1h0c-3-4-6-7-10-10-1 0-2-1-3-2-1 0-2-1-3-2z" class="Q"></path><path d="M533 329v-1s0-1-1-1h1c1 1 1 4 2 5l1-2v7l-1 2c-1-2-1-5-2-7v-3z" class="R"></path><path d="M518 307l1 2 1 1c0 1 1 1 1 2 1 1 1 2 2 2 0 1 1 2 1 2 1 2 2 5 3 6 0 1 0 1-1 1l-7-12-3-4h2z" class="U"></path><path d="M501 292l1-1c-1-2 0-4-1-6h1 0 1l-1 1 2 2c0 1 0 1-1 1l3 3 12 11h-2c-2-2-5-6-9-8v1c-1 0-2-1-3-2-1 0-2-1-3-2z" class="D"></path><defs><linearGradient id="c" x1="516.747" y1="298.411" x2="521.843" y2="303.596" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#a2a0a0"></stop></linearGradient></defs><path fill="url(#c)" d="M525 308c-5-3-8-8-12-12l-5-5c-1 0-1-1-2-2h-1v-1h1c1 0 2 1 3 1l6 6c1 2 4 4 4 5 1 1 2 2 3 2h0c1 0 3 1 3 2l1 1-3-2v2h1c1 1 1 1 1 3z"></path><path d="M515 295c2 0 3 1 4 2l2 1c2 2 4 3 6 4l6 6h1l-3-4h1c1 0 1 0 2 1v1c1 1 1 1 1 2h2l1-1h0l1 2c0 1 0 1-1 2 1 1 2 4 2 5v4h-1v1h-1v3c1 0 1 1 2 1v3 2c-1 1-1 2-2 2v1c-1 1-1 2-1 4h-1v-7l-1-2v-1c0-3-1-5-2-7-2-4-5-8-8-12 0-2 0-2-1-3h-1v-2l3 2-1-1c0-1-2-2-3-2h0c-1 0-2-1-3-2 0-1-3-3-4-5z" class="m"></path><path d="M538 315c0 1 0 2 1 3v2 1h-1l-1-4 1-2z" class="Q"></path><path d="M535 328s0-2 1-2v-1-2h1c2 1 1 4 3 5v2c-1 1-1 2-2 2v1c-1 1-1 2-1 4h-1v-7l-1-2z" class="T"></path><path d="M525 308c0-2 0-2-1-3h-1v-2l3 2c3 4 6 8 7 12v1c1 1 0 1 0 2-2-4-5-8-8-12z" class="G"></path><g class="E"><path d="M515 295c2 0 3 1 4 2l2 1c2 2 4 3 6 4l6 6c1 2 2 3 3 5v2c-3-4-6-9-10-11 0-1-1-1-1-1-2-2-3-3-6-3 0-1-3-3-4-5z"></path><path d="M531 304h1c1 0 1 0 2 1v1c1 1 1 1 1 2h2l1-1h0l1 2c0 1 0 1-1 2 1 1 2 4 2 5v4h-1v-2c-1-1-1-2-1-3l-1 2-1-2v-2c-1-2-2-3-3-5h1l-3-4z"></path></g><path d="M538 307h0l1 2c0 1 0 1-1 2-1-2-1-2-3-3h2l1-1z" class="Y"></path><path d="M534 308h0c2 3 3 4 4 7l-1 2-1-2v-2c-1-2-2-3-3-5h1z" class="R"></path><path d="M496 255h6 1 2l17 3c6 1 11 3 17 5 2 1 5 2 7 3s11 5 11 7h0v1c1 1 2 2 4 2l3 2v1c-1 1-2 1-3 0v1h0c0 1 0 2 1 2v1 2c1 0 1 2 1 3v1h0v1 1l1-1h1v1h-1-1v1c1 1 2 1 2 3v1h-2 0c-1-1-1-1-1-2h0l-5-6c-1 1-1 1-1 3 1 1 2 2 2 4v1h0v1 1c-1 2-1 5-1 7 0 1 0 1-1 1v-5-1h-1c-1 0-1-1-2-2s-1-1-2-1v2 2c-1-2-2-4-2-5h0c-1-1-1-1-1-2l-1-2s0-1-1-1c0-1 0-1-1-1h0c-1 0-2-1-2-2l-1-1c-2-4-6-7-9-10-1-1-4-3-5-5v-2l-15-6-9-3-1 1s1 0 0 1c-2-1-4-1-6-3h-1l-2-2-2-1h3c-1 0-1 0-1-1 1 0 1 0 2-1h0z" class="z"></path><path d="M514 262c7 3 14 6 20 9 4 1 7 3 11 5l-1 1c-6-3-11-5-16-7l-15-6v-1c1 1 1 1 2 0h-1v-1z" class="C"></path><path d="M496 255h6 1l2 1h-1v3c3 1 7 2 10 3v1h1c-1 1-1 1-2 0v1l-9-3-1 1s1 0 0 1c-2-1-4-1-6-3h-1l-2-2-2-1h3c-1 0-1 0-1-1 1 0 1 0 2-1h0z" class="b"></path><path d="M494 258c1 0 2 1 3 1s7 2 7 2l-1 1s1 0 0 1c-2-1-4-1-6-3h-1l-2-2zm2-3h6 1l2 1h-1v3l-9-2c-1 0-1 0-1-1 1 0 1 0 2-1h0z" class="a"></path><path d="M505 255l17 3c6 1 11 3 17 5 2 1 5 2 7 3s11 5 11 7h0v1l-1 1c-1 0-4-2-6-3 0 1-1 1-2 2l-12-9c-1 0-1-1-2-1h-2l-1-1c-1 0-1 0-2-1-7-3-16-4-24-6l-2-1h2z" class="b"></path><path d="M543 267l14 6v1l-1 1c-1 0-4-2-6-3l-7-5z" class="r"></path><path d="M529 262c5 1 10 3 14 5l7 5c0 1-1 1-2 2l-12-9c-1 0-1-1-2-1h-2l-1-1c-1 0-1 0-2-1z" class="B"></path><path d="M550 272c2 1 5 3 6 3l1-1c1 1 2 2 4 2l3 2v1c-1 1-2 1-3 0v1h0c0 1 0 2 1 2v1 2c1 0 1 2 1 3v1h0v1 1l1-1h1v1h-1-1v1c1 1 2 1 2 3v1h-2 0c-1-1-1-1-1-2h0l-5-6-5-5s-1-1-2-1c-2-2-4-3-6-5l1-1h0c3 2 6 4 8 6s3 4 5 4c-2-3-4-6-6-8-1 0-2-1-2-2-1 0-1-1-1-2h-1c1-1 2-1 2-2z" class="b"></path><path d="M550 276h1s1 1 2 1c0 0 1-1 2 0l2 2v1c2 1 2 3 4 4h0l1-1v2c1 0 1 2 1 3v1h0v1c-1-1-1-3-2-4s-3-2-4-3c-1-2-2-4-4-6l-1 1c-1 0-2-1-2-2z" class="o"></path><path d="M550 272c2 1 5 3 6 3l1-1c1 1 2 2 4 2l3 2v1c-1 1-2 1-3 0v1h0c0 1 0 2 1 2v1l-1 1h0c-2-1-2-3-4-4v-1l-2-2c-1-1-2 0-2 0-1 0-2-1-2-1h-1c-1 0-1-1-1-2h-1c1-1 2-1 2-2z" class="R"></path><path d="M550 272c2 1 5 3 6 3l1-1c1 1 2 2 4 2l3 2v1c-1 1-2 1-3 0s-2-1-4-1c0 0 0-1-1-1 0-1-1-1-1-1-1-1-1-1-2-1-1-1-2-1-4-1h0-1c1-1 2-1 2-2z" class="J"></path><path d="M528 270c5 2 10 4 16 7 2 2 4 3 6 5 1 0 2 1 2 1l5 5c-1 1-1 1-1 3 1 1 2 2 2 4v1h0v1 1c-1 2-1 5-1 7 0 1 0 1-1 1v-5-1h-1c-1 0-1-1-2-2s-1-1-2-1v2 2c-1-2-2-4-2-5h0c-1-1-1-1-1-2l-1-2s0-1-1-1c0-1 0-1-1-1h0c-1 0-2-1-2-2l-1-1c-2-4-6-7-9-10-1-1-4-3-5-5v-2z" class="a"></path><path d="M551 297c-1-3-2-6-2-9 1 2 2 4 3 5v1c1 1 1 3 1 4-1-1-1-1-2-1z" class="K"></path><path d="M552 283l5 5c-1 1-1 1-1 3 1 1 2 2 2 4v1h0l-1-1-1-1-1-2c-1-1-1-1-2-1l-1-1v-1h2c-1-1-1-2-2-3h0 0 1 0 1c-1-1-2-1-2-3h0z" class="D"></path><path d="M553 291c1 0 1 0 2 1l1 2 1 1 1 1v1 1c-1 2-1 5-1 7 0 1 0 1-1 1v-5-1h-1c-1 0-1-1-2-2 0-1 0-3-1-4l1 1h1c0-2-1-3-1-4z" class="h"></path><path d="M553 291c1 0 1 0 2 1l1 2 1 1h0v2h0 0c-1 0-1-1-1-1l-1-1c-1 1 0 2-1 1-1 0-1 0-1-1h1c0-2-1-3-1-4z" class="o"></path><path d="M466 234c1-1 1-2 2-3v1h0 1c1 0 1 1 2 1h1c0-1 1-1 2-1s4 3 5 4h-1c0 1 1 2 2 2 1 1 2 1 2 3h0l1 1h1c1 1 2 1 4 1h0l-3 1v1h4v-1h2l1 1h2c0 1 1 1 0 2 2 1 4 1 6 1v1h-8s0 1 1 1c0 0-1 0-1 1h0v1 3c2-1 3-1 4 0h0 0c-1 1-1 1-2 1 0 1 0 1 1 1h-3l2 1 2 2h0c-2 1-6 0-6 2l2 2c1 1 3 2 4 2l7 4c1 0 2 0 2 1l-1 1-1-1-1 1v1l-1 1-2-1h0c-1-1-2-1-3-1h-1c-1 0-1 0-1 1l-3-1h-2 0l-2-1c0 1-1 1-1 2-2-1-2-1-4 0h0c-2 0-3 2-5 2h0l-2 1c-2-1-3-1-5-1h-1c0-1 0-1-1-1-1-1-2-1-3-1l-1-1-4-1h0v-1h0c0-1 0-1-1-1s-2-1-3-1h0c-4-1-7-4-9-7v-1h2l1-1c0-1 0-1 1-1 0-1 0-2 1-3l-2 1c0-1 0-2 1-3l1-1v-1s1-1 1-2h-1l-1-2-1-1 1-1c2 0 3-1 4 0 1 0 2 0 3-1 1 0 1-2 1-2 0-1 0 0 1-1v-2 1h2v-1c1 0 1 0 1-1v-3h2s0-1 1-1h0z" class="t"></path><path d="M479 263l3 2c-2 2-4 1-6 2h-2l1-1h1c0-1 2-2 3-3z" class="V"></path><path d="M488 250l2 1h2v1 3h-4l1-1c0-1-1-2-2-3l1-1z" class="H"></path><path d="M471 253h0 1c-1 1-1 2-2 2s-3-1-3-2c0 0-1-1-1-2h-1c0 1-1 1-2 1v-1h0-1v1h-1l-2-2h1c1 0 2 0 3-1h0c1 1 3 1 4 2s2 2 4 2z" class="C"></path><path d="M492 255c2-1 3-1 4 0h0 0c-1 1-1 1-2 1 0 1 0 1 1 1h-3c-3 0-7-1-10-1v1l-1 1c-1-1-1-1-1-2 1-1 6-1 7-1h1 4z" class="E"></path><path d="M482 257v-1c3 0 7 1 10 1l2 1 2 2h0c-2 1-6 0-6 2l2 2c0 1 0 1-1 1h-3c-1-1-1-1-2-1s-2 0-3-1h0-1-2l-2-2h4v-4h0z" class="V"></path><path d="M489 244h2l1 1h2c0 1 1 1 0 2 2 1 4 1 6 1v1h-8s0 1 1 1c0 0-1 0-1 1h0-2l-2-1c-6-2-13-1-20 0l3 3c-2 0-3-1-4-2s-3-1-4-2h4 1c2 0 4-1 6-1 3-1 7-2 10-3l1-1v1h4v-1z" class="d"></path><path d="M487 246c1 0 2 0 3 1l1 2c-3 0-5 0-7-1v-1h3v-1z" class="b"></path><path d="M487 246c2 0 4 0 6 1h1 0c2 1 4 1 6 1v1h-8-1l-1-2c-1-1-2-1-3-1z" class="T"></path><path d="M479 263l-4-2c-2-1-6-2-8-1h0c-1 1-3 0-4 0v-1h0 4c4 0 7 0 11 2l2 2h2 1 0c1 1 2 1 3 1s1 0 2 1h3c1 0 1 0 1-1 1 1 3 2 4 2l7 4c1 0 2 0 2 1l-1 1-1-1-1 1v1l-1 1-2-1h0c-1-1-2-1-3-1h-1c-1 0-1 0-1 1l-3-1-2-1v-2h0l-1-1h-1c-1-1-1-1-2-1l-3-2-3-2z" class="p"></path><path d="M480 263h2 1 0c1 1 2 1 3 1s1 0 2 1c0 0 1 0 2 1h2c1 0 1 0 2 1h-1c0 1 2 2 3 2l-1 1h-2v1c-1-1-1-2-2-3h-1v-1h-2c-1-1-2-1-3-2h-2c-1-1-2-1-3-2z" class="i"></path><path d="M492 264c1 1 3 2 4 2l7 4c1 0 2 0 2 1l-1 1-1-1-1 1v1l-1 1-2-1h0l1-1c-1-1-2-1-3-2-1 0-1 0-1-1-1 0-3-1-3-2h1c-1-1-1-1-2-1h-2c-1-1-2-1-2-1h3c1 0 1 0 1-1z" class="O"></path><path d="M494 267c2 1 6 2 9 4l-1 1v1l-1 1-2-1h0l1-1c-1-1-2-1-3-2-1 0-1 0-1-1-1 0-3-1-3-2h1z" class="M"></path><path d="M466 234c1-1 1-2 2-3v1h0 1c1 0 1 1 2 1h1c0-1 1-1 2-1s4 3 5 4h-1c0 1 1 2 2 2 1 1 2 1 2 3h0l1 1h1c1 1 2 1 4 1h0l-3 1-1 1-4-2c-1 0-1-1-3-1s-4-1-6-2c-2 0-4-1-6-1l-1 3c-1 2-3 3-4 4s-1 2-2 2h0c-3 1-5 5-6 7l-2 1c0-1 0-2 1-3l1-1v-1s1-1 1-2h-1l-1-2-1-1 1-1c2 0 3-1 4 0 1 0 2 0 3-1 1 0 1-2 1-2 0-1 0 0 1-1v-2 1h2v-1c1 0 1 0 1-1v-3h2s0-1 1-1h0z" class="l"></path><path d="M475 240c-1-2-1-3-3-4h0l1-1 1 2c2 1 4 3 5 5l-3-2h-1z" class="Y"></path><path d="M460 239v1h2c0 2-2 5-4 6h0l-1-1h-2c1 0 2 0 3-1 1 0 1-2 1-2 0-1 0 0 1-1v-2z" class="B"></path><path d="M451 245c2 0 3-1 4 0h2l1 1c-2 1-4 2-5 3h-1l-1-2-1-1 1-1z" class="W"></path><path d="M466 234c1-1 1-2 2-3v1h0 1c1 0 1 1 2 1h1c0-1 1-1 2-1s4 3 5 4h-1c0 1 1 2 2 2 1 1 2 1 2 3h0c-2-1-4-2-6-4 0-1-1-1-2 0l-1-2-1 1h0c2 1 2 2 3 4-1 0-2-1-3-1h-1-1l-1-1v1c-1-1-2-1-3-1l-1-1h-1s-1 0-1 1v-3h2s0-1 1-1h0z" class="h"></path><path d="M466 234c1-1 1-2 2-3v1h0 1c0 1 0 2 1 2 0 2 2 2 1 3s-1 1-2 1v-1-2c-1 0-1-1-3-1z" class="U"></path><path d="M466 234c2 0 2 1 3 1-1 1-1 1-1 2h-3-1s-1 0-1 1v-3h2s0-1 1-1h0z" class="Q"></path><path d="M450 259c0-1 0-1 1-1 0 1 1 1 2 1 1 2 3 3 5 4 1 1 3 2 4 2h0c4 2 8 2 12 2h2c2-1 4 0 6-2l3 2c1 0 1 0 2 1h1l1 1h0v2l2 1h-2 0l-2-1c0 1-1 1-1 2-2-1-2-1-4 0h0c-2 0-3 2-5 2h0l-2 1c-2-1-3-1-5-1h-1c0-1 0-1-1-1-1-1-2-1-3-1l-1-1-4-1h0v-1h0c0-1 0-1-1-1s-2-1-3-1h0c-4-1-7-4-9-7v-1h2l1-1z" class="C"></path><path d="M450 259c0-1 0-1 1-1 0 1 1 1 2 1 1 2 3 3 5 4 1 1 3 2 4 2h0c-4 0-6-3-9-3h1c1 1 1 1 2 1h-1-1c-2-1-3-2-4-4z" class="V"></path><path d="M482 265l3 2c-1 1-1 0-2 1-1 0-1 1-1 2h-3l1-1c1 0 1 0 1-1-1 0-2 0-3-1v2c-2-2-3 0-5-1l-1-1h4 0c2-1 4 0 6-2z" class="b"></path><path d="M450 259c1 2 2 3 4 4l1 1v1l-2-1h0c0 1 0 1 1 1 1 1 1 2 2 2l1 1h-1 0c-4-1-7-4-9-7v-1h2l1-1z" class="L"></path><path d="M485 267c1 0 1 0 2 1h1l1 1h0v2l2 1h-2 0l-2-1c0 1-1 1-1 2-2-1-2-1-4 0h0c-2 0-3 2-5 2h0l-2 1c-2-1-3-1-5-1h-1c0-1 0-1-1-1-1-1-2-1-3-1l-1-1-4-1h0v-1h0c0-1 0-1-1-1 7 2 13 3 20 1h3c0-1 0-2 1-2 1-1 1 0 2-1z" class="D"></path><path d="M478 273c2-1 4 0 5-2 2 0 3-1 4 0 0 1-1 1-1 2-2-1-2-1-4 0h-4z" class="m"></path><path d="M485 267c1 0 1 0 2 1h1l1 1h0v2l-2-1c-1-1-3 0-5 0 0-1 0-2 1-2 1-1 1 0 2-1z" class="K"></path><path d="M465 273h13 0 4 0c-2 0-3 2-5 2h0l-2 1c-2-1-3-1-5-1h-1c0-1 0-1-1-1-1-1-2-1-3-1z" class="O"></path><path d="M167 169c4-5 9-8 15-11 12-7 23-11 36-11 9-1 17-1 25 0 4 0 8 0 12 1v50 15c0 4-1 8 0 12v7 26 8 2c-1 2 0 4-1 6h2 0c2-2 3-3 5-4v4l1 4c0 1-1 2-2 3-1 2-2 3-3 5-2 4-2 7-2 12 0 1-1 3 0 5v1c-1 2 0 4 0 6v12 25c0 3 0 6 1 9h0c-1 3-1 6-1 8v28 25 21l1 1c1 0 1 0 2-1h0 1l-1 2c1 2 1 5 1 7-1 1-2 1-4 1v8 11 50c1 4 2 7 3 11 0 2 0 4-1 6 0 2-2 4-1 6 0 1-1 2-1 3-3 8-3 18-7 25-3 1-3 3-5 6-1 2-3 3-5 5l-1 2c-1 0-2 1-3 2l-2 2v-1c0-1 0-2 1-3h0l-1-1c0 1-1 1-1 2s-1 2-2 3h-1c0 1-1 1-1 2h-1l-1 1c0-1 1-1 1-2v-1c1-1 3-2 4-3 2-2 3-4 4-5 4-5 8-9 10-15 1-4 1-8 2-11 1-2 1-3 1-5v-1c0-3 1-6 1-8s-1-3 0-5c1-3-1-7-1-11v-7 7h-1v-1-16-62-122-7-26c0-4 1-10 0-15h-1c0-1 1-1 1-2h0c1-6 0-11 0-16s1-10 0-14l-1 1v-73-2c0-1-1-1-2-2s-1-1-2-1-1 0-1-1h-5c-1-1-3 0-5 0-1 1-4 0-6 0 0 1-2 0-3 0v1h-3c-1 1-1 1-2 1-1 1-2 0-3 1h-2c-1 0-1 0-2 1h-1l-3 1h-1c-1 1-2 1-2 1l-2 1c-1 1-1 0-2 0v1h-2v1h-1l-1 1-4 2h-2 0l-2 1h-1l-1 1h-1l-3 3-3 2h-1l-3 3-3 2c-1 1-2 1-2 2-1 1 0 0-1 0-1 1-1 2-3 3l-1 1c-2 2-4 3-5 5s-2 2-3 4c-1 1-2 1-3 2 0 0-1 1-1 2-1 1-2 1-2 3-1 0-1 0-1 1-1 0 0 0-1 1h0v1c-1 1-2 3-3 4 0 0-1 1-1 2 0 0-1 1-1 2-1 2-2 3-3 5s-1 4-2 5l-2 5-1 3v5c-1 1-1 1-1 2s0 1-1 2v2c0 1 0 2-1 3v2c-1 4 0 9-2 13v4 1c-1 1-1 2-1 2h-1 0v2h0c0 1-1 1-1 2l-1-1h0 0c1-2 1-4 2-6v-3c-1-1-1-1-1-2v-1c0-1-1-2-1-2l-1-1c-1-1 0-1-1-2v-2c-1 2 0 4-1 6 0 0 0 1-1 1h-3l-2-1-12-1c3-1 9 2 13 0v-2l-1-1 1-1v-6l3-11c3-12 7-23 13-34 1-3 3-6 4-8 3-4 5-7 8-10-2 0-5 0-6-1l1-1c1 1 2 1 4 0 0-1 0-2-1-3l3-2c1-1 2-2 3-2s2 0 4-1v-1c2-2 3-4 4-7l3-3c2 0 1 1 2 1 2 1 4 1 5 0z" class="W"></path><path d="M254 274h2 0v5h-1 0l-1-5z" class="V"></path><path d="M255 438l1 1c1 0 1 0 2-1h0 1l-1 2-2 4-1-6z" class="F"></path><path d="M146 182c1-1 2-2 3-2v2c0 1-1 2-1 2 0 1 0 2-1 3v-1l-1-1s1-1 1-2l-1-1z" class="e"></path><path d="M120 257l1 1v4h1v1s0 1-1 1h-3l-2-1c1 0 2 0 3-1l1-5z" class="H"></path><path d="M143 184l3-2 1 1c0 1-1 2-1 2l1 1v1c-1 1-2 0-3 0 0-1 0-2-1-3z" class="F"></path><path d="M248 221v2c0-1 0-2 1-3v-1 12l-1 5h0-1l1-15z" class="H"></path><path d="M256 274c2-2 3-3 5-4v4c-1 1-1 0-1 1s-3 3-4 4v-5z" class="D"></path><path d="M141 201h0c-1 4-5 7-6 11h1l-3 6c0 1-1 3-2 5v-2h-1c3-8 6-14 11-20z" class="i"></path><path d="M248 258l1 2v-29h1v9 16c0 4 0 8 1 13h-1c-1 0-2 1-4 1h0l1-1v-6l1-1v-4zm-102-64h2l-1 1c1 0 2 1 2 2-1 1-2 1-2 3 0 1-1 1-1 2-1 0-1 1-1 1-1 1-2 5-4 6h0-1-1v-1l-1-1-2 5h-1c1-4 5-7 6-11h0c0-1 0-1 1-2 1-2 2-3 4-5z" class="G"></path><path d="M146 194h2l-1 1v3h-1v-1c-3 3-5 6-7 9l-1 1-2 5h-1c1-4 5-7 6-11h0c0-1 0-1 1-2 1-2 2-3 4-5z" class="Y"></path><path d="M252 358c1 6 0 12 0 18v15c0 3 1 7-1 9v7c0 7 1 15 0 22l-1-44v-8l1-15h0c1-1 1-2 1-4z" class="J"></path><path d="M252 358c1 6 0 12 0 18l-1 1c0 2 1 12 0 13-1-2 0-3-1-5v-8l1-15h0c1-1 1-2 1-4z" class="U"></path><path d="M251 464v13 17l1 27c0 5-1 10-2 15v-7l-1-1v5-3-4c-1-1-1-3-1-4h1v-1c0-6 0-12 1-18v-19c0 3-1 15 0 17h1v-22c0-5-1-10 0-15z" class="h"></path><path d="M252 159v9 15 52 30 1c0 1 1 1 1 2h-1l-1-1c1-1 0-3 0-4v-15-68-15-5l1-1z" class="q"></path><path d="M249 530v3c1 12-1 23-6 34-1 3-3 5-5 7l-5 7-1-1c0 1-1 1-1 2s-1 2-2 3h-1c0 1-1 1-1 2h-1l-1 1c0-1 1-1 1-2v-1c1-1 3-2 4-3 2-2 3-4 4-5 4-5 8-9 10-15 1-4 1-8 2-11 1-2 1-3 1-5v-1c0-3 1-6 1-8s-1-3 0-5v1c0-2 0-2 1-3z" class="g"></path><path d="M247 546v-1c0-3 1-6 1-8s-1-3 0-5v1 10c0 4-1 8-2 13-1 3-1 6-3 9l-1 1c-2 5-4 8-8 11 0 1-1 3-2 3 0 1-1 1-1 2s-1 2-2 3h-1c0 1-1 1-1 2h-1l-1 1c0-1 1-1 1-2v-1c1-1 3-2 4-3 2-2 3-4 4-5 4-5 8-9 10-15 1-4 1-8 2-11 1-2 1-3 1-5z" class="W"></path><path d="M174 168c12-11 27-16 43-18 7 0 29-1 33 2 1 2 1 5 2 7l-1 1c0-2-1-4-2-5s-1-2-3-2c-6-1-12-1-18-1-12 0-23 1-34 6-4 1-8 3-11 5l-8 5h-1z" class="V"></path><path d="M249 366c2 2 1 8 1 11v8l1 44v35c-1 5 0 10 0 15v22h-1c-1-2 0-14 0-17l-1-118z" class="l"></path><defs><linearGradient id="d" x1="251.996" y1="205.043" x2="243.34" y2="207.675" xlink:href="#B"><stop offset="0" stop-color="#a5a9a8"></stop><stop offset="1" stop-color="#ccc6c9"></stop></linearGradient></defs><path fill="url(#d)" d="M246 154c2 1 3 1 3 3 1 1 1 2 1 4 1 10 0 21 0 31l-1 20v7 1c-1 1-1 2-1 3v-2-12h-1v-31 5h1c-1-5 0-11 0-17v-5c-1-1-1-1 0-2h0c-1-1-1-1-1-2l-1-3z"></path><path d="M138 207l1 1v1h1c-1 3-3 6-4 9 0 1 1 0 1 1-1 2-2 4-1 6l1-1c1 1 1 0 1 1l-1 1v3l-2 5-1 3v5c-1 1-1 1-1 2s0 1-1 2v2c0 1 0 2-1 3v2c-1 4 0 9-2 13v4 1c-1 1-1 2-1 2h-1 0v2h0c0 1-1 1-1 2l-1-1h0 0c1-2 1-4 2-6v-3c-1-1-1-1-1-2v-1c0-1-1-2-1-2l-1-1c-1-1 0-1-1-2v-2c-1 2 0 4-1 6v-1h-1v-4l-1-1c0-1 1-3 1-5 1-9 4-18 7-27l2-4h1v2c1-2 2-4 2-5l3-6 2-5z" class="O"></path><path d="M133 228c1 0 2 0 3 1l-1 1h-1c0 1-1 2-1 2-1 0-1 0-1-1l1-3z" class="p"></path><path d="M136 229c0-1 1-2 1-3v3l-2 5-1 3c0-2 1-3 1-5v-2l1-1z" class="j"></path><path d="M130 221h1v2c0 2-1 5-3 7v-2c0-1 1-2 1-3h-1l2-4z" class="g"></path><path d="M132 231h0c-1 2-2 4-2 5-1 1-1 1-1 2v1l-1 1v-2-1c1-1 1-2 1-3 1-1 1-3 2-5v-1c1-2 1-3 3-4l-1 4-1 3z" class="C"></path><path d="M139 209h1c-1 3-3 6-4 9 0 1 1 0 1 1-1 2-2 4-1 6l1-1c1 1 1 0 1 1l-1 1c0 1-1 2-1 3-1-1-2-1-3-1l1-4c1-1 1-4 1-6 0-1 3-7 4-9z" class="i"></path><path d="M128 225h1c0 1-1 2-1 3v2 1c0 1 0 2-1 3h0v2c-1 0-1 1-1 2v1 1 1c-1 0-1 0-1 1v2l-1 1v4c0 1 0 1 1 1l1 1v2c1 1 1 1 1 2v1l1 2v1l1 1v4l-1 1v-1-1h-1v3c1 0 1 0 1 1s0 2 1 3v1c-1 1-1 2-1 2h-1 0v2h0c0 1-1 1-1 2l-1-1h0 0c1-2 1-4 2-6v-3c-1-1-1-1-1-2v-1c0-1-1-2-1-2l-1-1c-1-1 0-1-1-2v-2c-1 2 0 4-1 6v-1h-1v-4l-1-1c0-1 1-3 1-5 1-9 4-18 7-27z" class="G"></path><path d="M125 262v-2l-1-1v-2-2l-1-1v-4l1 1 1 3v1 1 2s1 0 1 1h1l-1-5h-1v-4l1 1v2c1 1 1 1 1 2v1l1 2v1l1 1v4l-1 1v-1-1h-1v3c1 0 1 0 1 1s0 2 1 3v1c-1 1-1 2-1 2h-1 0v2h0c0 1-1 1-1 2l-1-1h0 0c1-2 1-4 2-6v-3c-1-1-1-1-1-2v-1c0-1-1-2-1-2z" class="b"></path><path d="M250 269h1c1 2 0 10 0 13v25l1 51c0 2 0 3-1 4h0l-1 15c0-3 1-9-1-11l1 118v19c-1 6-1 12-1 18v1h-1c0 1 0 3 1 4v4c-1 1-1 1-1 3v-1c1-3-1-7-1-11v-7 7h-1v-1-16-62-122-7-26c0-4 1-10 0-15h-1c0-1 1-1 1-2 2 0 3-1 4-1z" class="B"></path><path d="M249 351c0 1 0 3 1 4 0 2 1 5 1 7l-1 15c0-3 1-9-1-11v-15z" class="w"></path><path d="M248 295l-1-24 1-1 1 1v7 13l-1 4h0z" class="V"></path><path d="M249 494l-1 28c0 1 0 3 1 4v4c-1 1-1 1-1 3v-1c1-3-1-7-1-11v-7-18h1 0c0-1 1-1 1-2z" class="i"></path><path d="M250 269h1c1 2 0 10 0 13v25l-1 9h0v8h-1v-2c0-5 1-10 1-16v-23c0-5-1-9 0-14z" class="g"></path><path d="M251 307l1 51c0 2 0 3-1 4h0c0-2-1-5-1-7-1-1-1-3-1-4v-16-13 2h1v-8h0l1-9z" class="E"></path><path d="M250 316c0 3 0 6 1 9 1 1 0 3 0 5v6l-1 11c0 2 1 4 0 6v2c-1-1-1-3-1-4v-16-13 2h1v-8h0z" class="K"></path><path d="M249 335l1-1c1 1 0 16 0 19v2c-1-1-1-3-1-4v-16z" class="d"></path><path d="M249 291v203c0 1-1 1-1 2h0-1l1-201h0l1-4zm-55-133c12-4 24-5 37-5 5-1 11-1 15 1l1 3c0 1 0 1 1 2h0c-1 1-1 1 0 2v5c0 6-1 12 0 17h-1v-5 31h1v12l-1 15h1 0v15c0 2 1 5 0 7v4l-1 1v6l-1 1c1-6 0-11 0-16s1-10 0-14l-1 1v-73-2c0-1-1-1-2-2s-1-1-2-1-1 0-1-1h-5c-1-1-3 0-5 0-1 1-4 0-6 0 0 1-2 0-3 0v1h-3c-1 1-1 1-2 1-1 1-2 0-3 1h-2c-1 0-1 0-2 1h-1l-3 1h-1c-1 1-2 1-2 1l-2 1c-1 1-1 0-2 0v1h-2v1h-1l-1 1-4 2h-2 0l-2 1h-1l-1 1h-1l-3 3-3 2h-1l-3 3-3 2c-1 1-2 1-2 2-1 1 0 0-1 0-1 1-1 2-3 3l-1 1c-2 2-4 3-5 5s-2 2-3 4c-1 1-2 1-3 2 0 0-1 1-1 2-1 1-2 1-2 3-1 0-1 0-1 1-1 0 0 0-1 1h0v1c-1 1-2 3-3 4 0 0-1 1-1 2 0 0-1 1-1 2-1 2-2 3-3 5s-1 4-2 5v-3l1-1c0-1 0 0-1-1l-1 1c-1-2 0-4 1-6 0-1-1 0-1-1 1-3 3-6 4-9h1 0c2-1 3-5 4-6 0 0 0-1 1-1 0-1 1-1 1-2 0-2 1-2 2-3 0-1-1-2-2-2l1-1h-2l2-2 2-4 7-8c1-2 3-5 6-6l3-1c2-2 5-4 8-5h1l8-5c3-2 7-4 11-5h0z" class="q"></path><path d="M137 224c0-1 1-1 1-2h1c0 1 0 2-1 3h0c0-1 0 0-1-1z" class="l"></path><path d="M171 179c1 0 3-1 4-2s3-2 5-3c3-3 6-6 10-6h0 4-1-3l-1 1c-1 1-1 1-2 1l-1 1-4 3-4 2c-1 1-3 2-4 3-2 1-2 2-3 3h-1l-1 1-1 1-1 1c-1 0-1 1-2 2-2 1-16 15-16 16l-1 1s0 1-1 1c0 1-1 1-1 2-1 0 0 0-1 1v1l-3 4h0l-1 1 1 1v-1h1c0 1-1 1-1 2h0c-1 1-1 2-1 3l-3 2c-1-1-1-1-1-2s-1 0-1-1c1-3 3-6 4-9h1 0c2-1 3-5 4-6 0 0 0-1 1-1 0-1 1-1 1-2 3-3 6-8 11-10-2 2-5 4-7 6-3 4-6 8-8 13l6-8c4-5 8-8 12-12l10-10z" class="H"></path><path d="M190 168v-1c4-1 8-3 12-4 6-2 11-3 17-4 7-1 15-2 21-1 2 1 4 1 5 2 1 2 1 4 1 6v15 28 20 11h0l-1 1v-73-2c0-1-1-1-2-2s-1-1-2-1-1 0-1-1h-5c-1-1-3 0-5 0-1 1-4 0-6 0 0 1-2 0-3 0v1h-3-2c-1 0-1 1-1 1h-3-1-1s-1 0-1 1c-2 1-4 1-6 2h0-2l-3 1c-1 1-1 1-2 1s-1 1-2 1l-1 1-2 1-1-1h1c1 0 1-2 3-2l2-1c1-1 2-1 3-2h2c1-1 1-1 2-1h0 1c1-1 3-1 5-2h4l3-1h1c1-1 2 0 3-1h8c1-1 5 0 6 0s3-1 4 0h2c1 0 1 0 2 1l2 1v-1c0-1-1-1-1-1l-1-1h-1 0-1-1c-1-1-12-1-14 0-2 0-5 0-8 1h-5c0 1-1 0-2 1-1 0-2 0-3 1h-2l-8 3s-1 0-1 1h-1c-1 0-1 1-1 1h-4 0z" class="N"></path><path d="M174 168h1l8-5c3-2 7-4 11-5h0l-11 6c-1 0-1 0-2 1s-3 2-4 3c-2 1-4 3-6 4-1 1-3 1-4 3 3-1 5-2 7-4 1-1 3-2 4-3l2-1h0c-1 2-2 2-3 3-2 1-3 3-5 4-1 1-3 2-4 3-2 1-3 2-4 3h0l-2 2s-1 1-1 2h1v1c-1 1-1 2-2 2h-1v1l1 1c1-1 2-3 4-4l3-3c2-1 3-2 4-3h0l-10 10c-4 4-8 7-12 12l-6 8c2-5 5-9 8-13 2-2 5-4 7-6-5 2-8 7-11 10 0-2 1-2 2-3 0-1-1-2-2-2l1-1h-2l2-2 2-4 7-8c1-2 3-5 6-6l3-1c2-2 5-4 8-5z" class="Q"></path><path d="M163 174l3-1c-1 2-1 3-3 4v-1-2z" class="D"></path><path d="M159 184c-1 0-1 1-1 1-1 2-3 3-4 4-1 2-2 5-4 6h0c0 1-1 1-1 2 0-1-1-2-2-2l1-1h-2l2-2 2-4h2c1-1 2-1 4-1l3-3z" class="K"></path><path d="M148 192l2-1c-1 1-1 2-2 3h-2l2-2z" class="R"></path><path d="M150 188h2c1-1 2-1 4-1-1 1-2 2-4 3v1l-1 1v-1h-1l-2 1 2-4z" class="Q"></path><path d="M150 188l7-8c1-2 3-5 6-6v2 1c-1 2-2 3-3 4s-1 2-1 3l-3 3c-2 0-3 0-4 1h-2z" class="U"></path><path d="M163 176v1c-1 2-2 3-3 4-1 0-1 0-2-1 1-1 3-3 5-4z" class="M"></path><path d="M194 158c12-4 24-5 37-5 5-1 11-1 15 1l1 3c0 1 0 1 1 2h0c-1 1-1 1 0 2v5c0 6-1 12 0 17h-1v-5 31h1v12l-1 15h1 0v15c0 2 1 5 0 7v4l-1 1v6l-1 1c1-6 0-11 0-16s1-10 0-14h0v-11-20-28-15c0-2 0-4-1-6-1-1-3-1-5-2-6-1-14 0-21 1-6 1-11 2-17 4-4 1-8 3-12 4v1c-4 0-7 3-10 6-2 1-4 2-5 3s-3 2-4 2h0c-1 1-2 2-4 3l-3 3c-2 1-3 3-4 4l-1-1v-1h1c1 0 1-1 2-2v-1h-1c0-1 1-2 1-2l2-2h0c1-1 2-2 4-3 1-1 3-2 4-3 2-1 3-3 5-4 1-1 2-1 3-3h0l-2 1c-1 1-3 2-4 3-2 2-4 3-7 4 1-2 3-2 4-3 2-1 4-3 6-4 1-1 3-2 4-3s1-1 2-1l11-6z" class="y"></path><path d="M247 209h1v12l-1 15h1 0v15c0 2 1 5 0 7v4l-1 1v-54z" class="q"></path><path d="M172 176h0l10-7c0-1 0 0 1-1 2-2 5-3 8-3l6-3h1l3-1c1-1 2-1 3-1 1-1 2 0 3-1 2 0 4-1 5-1h2c1 0 2 0 3-1h6v1h0-4c0 1-1 0-1 1h-6c-1 1-1 1-2 1l-5 1-3 1-11 4h-1l-1 1c-1 0-1 0-2 1-2 0-4 2-5 3l-8 5-3 3c-1 1-2 2-4 3l-3 3c-2 1-3 3-4 4l-1-1v-1h1c1 0 1-1 2-2v-1h-1c0-1 1-2 1-2l2-2h0l1 1 3-3c1-1 2-1 4-2z" class="Y"></path><path d="M194 158c12-4 24-5 37-5 5-1 11-1 15 1l1 3c0 1 0 1 1 2h0c-1 1-1 1 0 2v5c0 6-1 12 0 17h-1v-5l-1-18c-1-1-2-2-4-3-4-1-9-1-14-1h-9c-10 1-19 4-28 8-2 0-4 1-7 2-4 3-8 6-12 10-2 1-3 1-4 2l-3 3-1-1c1-1 2-2 4-3 1-1 3-2 4-3 2-1 3-3 5-4 1-1 2-1 3-3h0l-2 1c-1 1-3 2-4 3-2 2-4 3-7 4 1-2 3-2 4-3 2-1 4-3 6-4 1-1 3-2 4-3s1-1 2-1l11-6z" class="L"></path><path d="M183 164h3v-1l7-3c1 0 2-1 3-1h1c5-2 10-3 15-4 2-1 4 0 6-1h8c4 0 8-1 11 0 3 0 7-1 9 2 0 0 0 1 1 2v1h-1l-1-2s0-1-1-1h0l-1-1h-6-8-9-3c-1 1-2 1-3 1h-5c-1 1-3 2-5 2h-1c-2 1-4 1-6 2-3 1-6 3-8 3l-1 1h-1c-1 0-2 1-3 1v1c-4 3-8 6-12 10-2 1-3 1-4 2l-3 3-1-1c1-1 2-2 4-3 1-1 3-2 4-3 2-1 3-3 5-4 1-1 2-1 3-3h0l-2 1c-1 1-3 2-4 3-2 2-4 3-7 4 1-2 3-2 4-3 2-1 4-3 6-4 1-1 3-2 4-3s1-1 2-1z" class="m"></path><path d="M445 269h6c2 0 4 1 6 1 1 0 2 1 3 1l4 1 1 1c1 0 2 0 3 1 1 0 1 0 1 1h1c2 0 3 0 5 1l-1 1c1 0 1 1 2 1 1 1 1 1 1 2v1h-2c-1 0-1 1-2 2 0 1 1 1 1 1 1 0 2 0 3-1 1 0 1 0 2 1 0 0 1 1 2 1l1-1 2 2h-1c-1 0-2 0-3 1h1c2 1 2 1 4 3h0-1-1s1 1 2 1h0l2 2h1v1h1 1s1 0 1 1c1 0 2 0 3 1 1 0 1 1 1 1v2 1h1c0 2 2 3 3 5h-1 0l1 1-1 1v1l3 2c1-1 1-2 2-2l1-1h2l-1-1v-1l2 1v-1c1 0 1 0 2 1 0 0 1 0 2 1l1-1c2 2 3 4 5 6 0 0 0 1 1 2v-1l-1-2c-1-1-1-1-1-2 1 0 3 2 3 2l7 12c1 0 1 0 1-1 1 2 2 3 2 5 1 1 1 2 2 3 0 1 0 1 1 2 1-1 0-2 0-4l1 1v3c-1 1-1 2-1 3s0 1 1 1l-1 2c1 3 2 5 2 8 1 1 1 3 1 4l2 7h0v1h1v-1h1v-1c1 0 1 1 1 2l4 14h-1c-1 1 0 3-1 4h0l-2-3c-1 1-1 1-1 2l-2-2h-1v2l-2-3c-1 0-3-1-4-1l3 7h0c0 1 1 2 0 4h0c0-1-1-2-2-3v-1c-1-2-2-2-3-3 0 1 2 5 1 6v1l1 5h0c0 1 1 1 1 1v1 1c1 1 0 4 1 4 0 1 0 3 1 4v1 1l1 1v2h0v2 1h-1c-1-1-1-1-1-2h0-1v2h0l-2-2h0 0-1l-1-1c-1 0-1-1-2-2l-1-1-2-4c-1-2-2-3-3-4l-1-1-2-3c0-2-3-4-4-6l-1-2c-2-2-4-3-6-5l-1-1-1-1-3-1c-1-1-2-2-3-2l-1 2v-1l-1-4h-1c-1-4-2-8-5-11h-3c-2-3-5-5-8-7v-2c0-1-1-1-2-2l-2-2-1-1v-1c0-1-1-1-2-2h-1c-1 0-2-1-3-1 0 1 0 1-1 2l-4-2c-1-1-4-2-5-3-1 0-4-1-5-1l-1-1-4-2-3-2c-2-2-4-3-6-6 0-1-1-3-2-4 0-1-1-2-2-3l1-2-1-1c-2-3-4-8-5-12l-2-6c0-1 0-2-1-2l1-4h-1v-2l-1-1-1 4-1-2h-1-1v-2c0-1 0-2 1-3h0-1l-1-1 1-1h0l1-1h0v1h0c1-2 2-3 2-4h-1c0-2 0-2 1-2l3-1 2-1c1 0 3-1 4-1 1-1 3-1 5-1 2-1 4-1 6-2h1z" class="x"></path><path d="M435 303c0 1 0 2 1 3l-1 1h0c-1-1-2-2-2-4h2z" class="S"></path><path d="M439 308v-3h0c1 1 2 3 3 5-1 0-1 0-1 1l-2-3zm51-14s1 0 1 1c1 0 2 0 3 1 1 0 1 1 1 1v2l-5-5zm-54 12c0 1 1 2 1 4l2 4h-1c-1-2-3-4-3-7h0l1-1z" class="B"></path><path d="M460 302v-1l1-1h0c1 0 1 0 1 1h1c1 1 1 2 1 4 0-1-1-1-1-1-1-1-1-1-2-1h-1v-1zm-27 1c0-2-1-6 0-8h0c0 2 1 5 2 8h-2z" class="U"></path><path d="M427 285l1-1c1 1 0 2 2 2l-1 1c0 1 0 1-1 2v1 1c-1 2-1 3-1 4 0-1 0-2-1-2l1-4h-1c1-1 1-3 1-4z" class="f"></path><path d="M456 312v-1c2 1 4 3 5 4 0 1 2 3 2 5l-1 1-1-2-5-7z" class="d"></path><path d="M456 312c-2-2-3-4-4-7l1 2c2 1 3 3 5 3v-1l1 1 3 4-1 1c-1-1-3-3-5-4v1z" class="E"></path><path d="M476 310c-3-2-5-5-6-8 4 2 6 6 9 9l3 3h0c-1 0-3-2-3-3l-1 1c-1-1-2-1-2-2z" class="Q"></path><path d="M469 285c1-1 1-1 1-2l1-1 2 1c0 1 1 1 1 1 1 0 2 0 3-1 1 0 1 0 2 1l-4 2h-1l1 2h0c-2 0-4-2-6-3z" class="O"></path><path d="M478 312l1-1c0 1 2 3 3 3l8 7-1 1c-1-1-3-1-4-2-2-1-3-3-5-5h0l-2-3z" class="K"></path><path d="M495 318l1 1h0l2 1 4 2-1 1v3-1h-2c0-1-6-4-7-5h1l2-2z" class="Y"></path><path d="M501 325l-1-1c-1-2-3-3-5-5h1l2 1 4 2-1 1v3-1z" class="m"></path><path d="M468 286h1v1c-2 1-2 1-3 0h-1c0 2 2 1 2 3h-1v2l2 2h-1c-1 0-2-1-3-2h-1c0-1-1-1-1-2s-1-2-1-4h0c1 0 1 1 2 1v-1-1c1 1 2 1 3 2 1-1 1-1 2-1z" class="B"></path><defs><linearGradient id="e" x1="455.74" y1="301.853" x2="457.426" y2="307.579" xlink:href="#B"><stop offset="0" stop-color="#747373"></stop><stop offset="1" stop-color="#8c8b8c"></stop></linearGradient></defs><path fill="url(#e)" d="M458 309c0-1-2-3-3-4-2-3-4-7-5-10l7 8c1 1 1 2 2 2 2 1 3 2 4 3h-2v1c-1 0-1 0-2 1l-1-1z"></path><path d="M490 321l8 5 1 1 2 1h-2v2h-1l-2-1-5-3-2-1v-1h1v-1l-1-1 1-1z" class="h"></path><path d="M496 329v-3h2l1 1 2 1h-2v2h-1l-2-1z" class="w"></path><path d="M499 325h2v1c3 1 6 3 8 5-1 1 0 2-1 2l3 3c-2-1-3-2-4-3h-1s-1 0-1-1c-2-1-3-2-5-2-1 1-1 0-2 0h1v-2h2l-2-1c2 0 3 0 4 1h0s-1-1-2-1c0-1-1-1-1-2h-1z" class="D"></path><path d="M501 328l7 5 3 3c-2-1-3-2-4-3h-1s-1 0-1-1c-2-1-3-2-5-2-1 1-1 0-2 0h1v-2h2z" class="m"></path><path d="M432 285c1 1 1 2 2 3l2 2c1-1 1-1 1-2 1 0 1 0 2 1v-2c2 0 2 2 3 2l1-1h1c1 1 1 1 1 2h-1v1l-1-1c-1 0-1 0-2 1l-2-1v1l-1 1h0c-2 0-1 1-2 2-1-1 0-2-1-4-1 0-2 0-2 1h-1v-3l-1-1s0-1 1-2z" class="D"></path><path d="M477 313l2 2h1 0c2 2 3 4 5 5 1 1 3 1 4 2l1 1v1h-1v1h-3-1-1c-1-1-2-2-3-2l-2-2v-4l-3-3 1-1z" class="E"></path><path d="M477 313l2 2c2 2 3 4 5 6-2-1-4-2-5-4l-3-3 1-1z" class="w"></path><path d="M481 323l1-1h1c0 1 1 1 1 1l1-1c2 0 2 1 4 2v1h-3-1-1c-1-1-2-2-3-2z" class="n"></path><path d="M443 312h3c0 1 1 1 1 2l2 2c0 1 2 3 3 4v-1c-1-1-2-2-2-3l-1-2-1-1c-1-1-2-2-2-3-2-3-4-7-4-9v-3c0-1-1-2 0-3h0 1v1h-1l2 2c0-1 0-1 1-2 0 1 0 2 1 3-1 2 0 2 0 4s1 5 2 7v1c0 2 3 6 5 8h0v1c-3 0-4-4-6-5h-2c0-1-1-2-2-3z" class="f"></path><path d="M478 304l-2-2c-1-1-2-2-3-4 1 1 2 2 4 3v-1l-1-1 1-1c3 3 6 6 9 7 1 1 2 2 3 2l1-1-4-3 1-1c1 1 3 3 4 3s1 0 1-1c1 1 3 2 4 4h-1-3v2l-2-1h0c-2-1-3-2-5-2h-3v-1l-1 1c-1-1-2-3-3-3z" class="M"></path><path d="M478 304c3 0 3 2 5 2 1 1 3 0 4 1h0c1 0 2 1 3 2-2-1-3-2-5-2h-3v-1l-1 1c-1-1-2-3-3-3z" class="B"></path><path d="M489 301c-2-1-4-3-6-5 2 1 3 2 5 3v-1l-3-3c2 0 3 1 4 2s2 2 3 2l2 2h-1c1 1 2 2 3 2 1 1 1 1 2 1v1h0l1 1-1 1v1h-1l2 2c-1 1-1 1-2 1-1 1-1 1-1 2l-1-1c-1 0-2-1-3-1v-1-2h3 1c-1-2-3-3-4-4l-3-3z" class="f"></path><path d="M489 301c3 0 6 5 9 6v1h-1l2 2c-1 1-1 1-2 1-1 1-1 1-1 2l-1-1c-1 0-2-1-3-1v-1-2h3 1c-1-2-3-3-4-4l-3-3z" class="S"></path><path d="M463 301h1l4 4c2 3 3 6 7 6 0 0 1 0 1-1 0 1 1 1 2 2l2 3h-1l-2-2-1 1 3 3v4l-4-4h0c-1-1-1-2-2-3h-2 0v1l-4-5h0v-1h2l-1-1v-1c-1-1-3-2-4-2 0-2 0-3-1-4z" class="J"></path><path d="M467 310v-1h2l-1-1v-1c2 2 3 4 4 7l-5-4z" class="M"></path><path d="M475 317c-1-1-1-3-2-4v-1c-1 0 0 0-1-1l1 1c1 1 2 1 3 0l1 1-1 1 3 3v4l-4-4z" class="R"></path><path d="M453 319c-2-2-5-6-5-8v-1c-1-2-2-5-2-7s-1-2 0-4l1 5c2 5 6 11 10 15 1 2 3 3 4 5l3 3v1h0c-2-1-3-2-5-3s-5-4-6-6h0z" class="J"></path><path d="M453 319h1c2 1 3 4 5 5v1c-2-1-5-4-6-6h0z" class="Q"></path><defs><linearGradient id="f" x1="464.041" y1="321.214" x2="451.651" y2="327.461" xlink:href="#B"><stop offset="0" stop-color="#171817"></stop><stop offset="1" stop-color="#373738"></stop></linearGradient></defs><path fill="url(#f)" d="M445 315h2c2 1 3 5 6 5v-1c1 2 4 5 6 6s3 2 5 3 4 3 6 3v3l-11-6c-2-1-4-3-6-3h0c-2-2-5-3-6-6 0-1-2-3-2-4z"></path><path d="M481 307l1-1v1h3c2 0 3 1 5 2h0l2 1v1c1 0 2 1 3 1l1 1c0-1 0-1 1-2l3 3 1 1v1 1c1 0 2 1 3 1h0c-2 1-3-1-6 0h0l-1 1h0c1 1 1 0 1 1l-2-1h0l-1-1-2 2h-1c-1 0-1-1-2-2s-2-1-3-3c1 0 3 2 4 2h1c-4-3-7-7-11-10z" class="U"></path><path d="M490 318c1-1 1 0 2 0s1-1 2-1l1 1-2 2h-1c-1 0-1-1-2-2z" class="Q"></path><path d="M490 309l2 1v1h-1 0c1 1 3 4 4 5 1 0 2 1 2 2-3-2-7-5-10-8h0c1 0 2-1 3-1z" class="c"></path><path d="M497 318c0-1-1-2-2-2-1-1-3-4-4-5h0 1c1 0 2 1 3 1l1 1c0-1 0-1 1-2l3 3 1 1v1 1c1 0 2 1 3 1h0c-2 1-3-1-6 0h-1z" class="E"></path><path d="M496 313c0-1 0-1 1-2l3 3h-2c-1 0-2-1-2-1zm1 5c0-1-1-2-2-2-1-1-3-4-4-5h0 1c1 0 2 1 3 1h-2c0 1 2 3 3 4 1 0 2 1 4 1h1c1 0 2 1 3 1h0c-2 1-3-1-6 0h-1z" class="R"></path><path d="M506 307s2 3 3 3c2 2 3 3 4 5l1 2c-1 1-2 2-2 3l-1-1h-1-1s-1-1-2-1h-1l-1-1h-1v1c-1 0-2-1-3-1v-1-1l-1-1-3-3c1 0 1 0 2-1l-2-2h1l3 2c1-1 1-2 2-2l1-1h2z" class="Y"></path><path d="M503 308l3 2c-1 1-2 1-2 1l-3-1c1-1 1-2 2-2z" class="Q"></path><path d="M502 312s1-1 2 0c2 1 5 3 7 6v1h-1-1s-1-1-2-1l-2-3-3-3z" class="g"></path><path d="M499 310s2 2 3 2l3 3 2 3h-1l-1-1h-1v1c-1 0-2-1-3-1v-1-1l-1-1-3-3c1 0 1 0 2-1z" class="J"></path><path d="M505 315l2 3h-1l-1-1h-1v1c-1 0-2-1-3-1v-1-1l3 1 1-1z" class="h"></path><defs><linearGradient id="g" x1="504.255" y1="312.563" x2="512.527" y2="312.381" xlink:href="#B"><stop offset="0" stop-color="#2e2f2f"></stop><stop offset="1" stop-color="#4f4d4e"></stop></linearGradient></defs><path fill="url(#g)" d="M506 307s2 3 3 3c2 2 3 3 4 5l1 2c-1 1-2 2-2 3l-1-1v-1l-1-1v-3c0-1-3-3-4-4l-3-2 1-1h2z"></path><defs><linearGradient id="h" x1="458.138" y1="306.452" x2="484.205" y2="322.541" xlink:href="#B"><stop offset="0" stop-color="#181819"></stop><stop offset="1" stop-color="#4c4b4b"></stop></linearGradient></defs><path fill="url(#h)" d="M457 303h1 0 1l1-1v1h1c1 0 1 0 2 1 0 0 1 0 1 1 1 0 3 1 4 2v1l1 1h-2v1h0l4 5v-1h0 2c1 1 1 2 2 3h0l4 4 2 2c1 0 2 1 3 2 0 1 0 2-2 3l-12-11h-1c-1 0-1 1-1 1-1 0-2-2-3-3v-2-1c0-1-1-3-2-4s-2-2-4-3c-1 0-1-1-2-2z"></path><path d="M465 312l5 5h-1c-1 0-1 1-1 1-1 0-2-2-3-3v-2-1zm-41-35l1-1 1 1-1 2c1 1 2 0 3 0v1s1 1 1 2v1h1c2 0 2 1 3 2 1 0 1 1 1 1l1-1 1-1c0 1 0 2 1 2h1v-2h0c2 1 2 2 4 2 1 0 2 1 3 1v1c1-1 1-2 1-2h0 0c1 0 1 1 2 1 0 0 1 1 2 1v1c1 2 3 5 5 6-2-1-4-2-5-4-1 0-2-2-3-3l-1 1v1h-1c0-1 0-1-1-2h-1l-1 1c-1 0-1-2-3-2v2c-1-1-1-1-2-1 0 1 0 1-1 2l-2-2c-1-1-1-2-2-3-1 1-1 2-1 2l-1-1c-2 0-1-1-2-2l-1 1c0 1 0 3-1 4v-2l-1-1-1 4-1-2h-1-1v-2c0-1 0-2 1-3h0-1l-1-1 1-1h0l1-1h0v1h0c1-2 2-3 2-4z" class="T"></path><path d="M421 286c0-1 0-2 1-3h0-1l-1-1 1-1v1l1-1 1 1c-1 1-1 1-1 2h1s1-1 1-2c1-1 2-1 3-1v2c1 0 1 0 1-1 1 1 0 2 1 3h3c-1 1-1 2-1 2l-1-1c-2 0-1-1-2-2l-1 1c0 1 0 3-1 4v-2l-1-1-1 4-1-2h-1-1v-2z" class="U"></path><path d="M421 286h1c1 0 1-1 1-1 1 0 1 0 1 1h0 1v-2h1v1h1c0 1 0 3-1 4v-2l-1-1-1 4-1-2h-1-1v-2z" class="M"></path><defs><linearGradient id="i" x1="491.953" y1="336.84" x2="495.582" y2="333.49" xlink:href="#B"><stop offset="0" stop-color="#908e8f"></stop><stop offset="1" stop-color="#b5b3b3"></stop></linearGradient></defs><path fill="url(#i)" d="M484 325h1 1 3l2 1 5 3 2 1c1 0 1 1 2 0 2 0 3 1 5 2l-1 2 1 1h0c0 2 0 3 2 5-1 1-1 1-1 2l-2-1v1 1h-2 0c-4-5-9-9-15-12h-1 0c-1-1-2-1-2-2l-2-1c2-1 2-2 2-3z"></path><path d="M484 325h1 1 3l2 1-1 1c1 1 1 1 2 1h0c1 1 2 2 3 2 1 1 1 1 1 2v1c1 0 2 2 3 3h0c-2-1-4-2-6-4l-3-2c-2-1-4-2-6-1l-2-1c2-1 2-2 2-3z" class="i"></path><path d="M484 325h1c3 2 6 4 9 7h-1l-3-2c-2-1-4-2-6-1l-2-1c2-1 2-2 2-3z" class="o"></path><path d="M491 326l5 3 2 1c1 0 1 1 2 0 2 0 3 1 5 2l-1 2 1 1h0c0 2 0 3 2 5-1 1-1 1-1 2l-2-1v1 1h-2 0l1-1c0-2-3-5-4-6h0c-1-1-2-3-3-3v-1c0-1 0-1-1-2-1 0-2-1-3-2h0c-1 0-1 0-2-1l1-1z" class="u"></path><path d="M501 334l1-1-1-1c1 1 2 1 3 2l1 1h0c0 2 0 3 2 5-1 1-1 1-1 2l-2-1v1-1h1c0-2-3-4-4-5v-1h0 0 0v-1z" class="m"></path><path d="M491 326l5 3 2 1c1 0 1 1 2 0 2 0 3 1 5 2l-1 2c-1-1-2-1-3-2l1 1-1 1v1h0 0 0l-5-3c0-1 0-1-1-2-1 0-2-1-3-2h0c-1 0-1 0-2-1l1-1z" class="e"></path><path d="M495 330c2 1 3 2 5 3 0 0 1 0 1 1v1h0 0 0l-5-3c0-1 0-1-1-2z" class="w"></path><defs><linearGradient id="j" x1="443.479" y1="326.803" x2="464.173" y2="319.711" xlink:href="#B"><stop offset="0" stop-color="#111112"></stop><stop offset="1" stop-color="#444342"></stop></linearGradient></defs><path fill="url(#j)" d="M439 308l2 3c0-1 0-1 1-1l1 2c1 1 2 2 2 3s2 3 2 4c1 3 4 4 6 6h0c4 3 8 6 12 8 2 1 3 2 5 3 0 0 1 1 1 2-1 0-2-1-3-1l3 2h-1c-1 0-2-1-3-1 0 1 0 1-1 2l-4-2c-1-1-4-2-5-3-1 0-4-1-5-1l-1-1-4-2-3-2c-2-2-4-3-6-6 0-1-1-3-2-4 0-1-1-2-2-3l1-2-1-1c1 0 2 2 3 1h1 1l-2-4c1-1 2-1 2-2z"></path><path d="M441 311c0-1 0-1 1-1l1 2c1 1 2 2 2 3s2 3 2 4c-3-2-5-5-6-8z" class="J"></path><path d="M434 313c1 0 2 2 3 1h1 1c1 1 2 2 2 4 1 1 2 2 3 4l8 6c-2 0-3 0-5-1s-4-3-5-5c0-1-1-2-1-2h-1c-1 0-2-2-3-2l-1 1c0-1-1-2-2-3l1-2-1-1z" class="K"></path><path d="M434 313c1 0 2 2 3 1h1 1c1 1 2 2 2 4 1 1 2 2 3 4-3-2-4-5-6-6v1c-1 0-2-2-3-3l-1-1z" class="I"></path><path d="M447 327c2 1 3 1 5 1 4 3 7 5 11 7 1 0 3 2 5 2l3 2h-1c-1 0-2-1-3-1 0 1 0 1-1 2l-4-2c-1-1-4-2-5-3h2 0c-4-3-9-4-12-8z" class="i"></path><path d="M459 335l8 3c0 1 0 1-1 2l-4-2c-1-1-4-2-5-3h2 0z" class="X"></path><defs><linearGradient id="k" x1="449.831" y1="323.899" x2="447.575" y2="332.265" xlink:href="#B"><stop offset="0" stop-color="#c5c0c0"></stop><stop offset="1" stop-color="#e8e8e8"></stop></linearGradient></defs><path fill="url(#k)" d="M436 319l1-1c1 0 2 2 3 2h1s1 1 1 2c1 2 3 4 5 5 3 4 8 5 12 8h0-2c-1 0-4-1-5-1l-1-1-4-2-3-2c-2-2-4-3-6-6 0-1-1-3-2-4z"></path><defs><linearGradient id="l" x1="466.622" y1="327.336" x2="482.095" y2="323.765" xlink:href="#B"><stop offset="0" stop-color="#777"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#l)" d="M463 308c1 1 2 3 2 4v1 2c1 1 2 3 3 3 0 0 0-1 1-1h1l12 11 2 1c0 1 1 1 2 2h0l2 2h0c-1 0-2 0-3-1v1h0l-1 1 5 4 4 4h-2s-1-1-2-1c-1-1-1-2-2-3h-1l-2-2v2l-1 1c-3-2-6-3-9-5l-2-1c-1-1-2-1-2-2-2 0-4-2-6-3h0v-1-1l-2-2c-1-2-1-3-1-5l1 2 1-1c0-2-2-4-2-5l1-1-3-4c1-1 1-1 2-1v-1h2z"></path><path d="M463 308c1 1 2 3 2 4v1 2h0c0 1 0 2 1 3l-1 1c-1-1-2-2-2-3l-1-2-3-4c1-1 1-1 2-1v-1h2z" class="d"></path><path d="M459 310c1-1 1-1 2-1 1 2 4 6 4 9l-2-2-1-2-3-4z" class="u"></path><path d="M468 318s0-1 1-1h1l12 11 2 1c0 1 1 1 2 2l-1 1c-6-2-13-9-17-14z" class="O"></path><defs><linearGradient id="m" x1="475.399" y1="324.966" x2="474.834" y2="338.189" xlink:href="#B"><stop offset="0" stop-color="#a7a7a5"></stop><stop offset="1" stop-color="#c8c5c7"></stop></linearGradient></defs><path fill="url(#m)" d="M461 319l1 2 1-1c3 4 9 9 14 12 2 1 5 3 7 4v2l-1 1c-3-2-6-3-9-5l-2-1c-1-1-2-1-2-2-2 0-4-2-6-3h0v-1-1l-2-2c-1-2-1-3-1-5z"></path><path d="M461 319l1 2c1 2 3 4 5 5 1 2 4 4 5 6v1c-1-1-2-1-2-2-2 0-4-2-6-3h0v-1-1l-2-2c-1-2-1-3-1-5z" class="U"></path><path d="M445 269h6c2 0 4 1 6 1 1 0 2 1 3 1l4 1 1 1c1 0 2 0 3 1 1 0 1 0 1 1h1c2 0 3 0 5 1l-1 1c1 0 1 1 2 1 1 1 1 1 1 2v1h-2c-1 0-1 1-2 2l-2-1-1 1c0 1 0 1-1 2l-2-2h0c0 1 1 2 1 3-1 0-1 0-2 1-1-1-2-1-3-2v1 1c-1 0-1-1-2-1h0c0 2 1 3 1 4l-7-5c1 3 1 4 3 7l-8-6v2c-1 0-2-1-2-1-1 0-1-1-2-1h0 0s0 1-1 2v-1c-1 0-2-1-3-1-2 0-2-1-4-2h0v2h-1c-1 0-1-1-1-2l-1 1-1 1s0-1-1-1c-1-1-1-2-3-2h-1v-1c0-1-1-2-1-2v-1c-1 0-2 1-3 0l1-2-1-1-1 1h-1c0-2 0-2 1-2l3-1 2-1c1 0 3-1 4-1 1-1 3-1 5-1 2-1 4-1 6-2h1z" class="t"></path><path d="M446 286c1-1 2-1 3-2 0 1 1 1 1 2v2c-1 0-2-1-2-1-1 0-1-1-2-1h0z" class="Z"></path><path d="M465 275h5c2 0 3 0 5 1l-1 1c1 0 1 1 2 1 1 1 1 1 1 2l-1-1c-2-2-4-2-6-2-2-1-4-1-5-2z" class="N"></path><path d="M464 272l1 1c1 0 2 0 3 1 1 0 1 0 1 1h1-5c-2 0-4-1-6-2 2 0 3-1 5-1z" class="F"></path><path d="M445 269h6c2 0 4 1 6 1 1 0 2 1 3 1l4 1c-2 0-3 1-5 1-3-1-7-3-10-3h0-2 0c-1-1-2-1-3-1h1z" class="j"></path><path d="M429 273c1 0 3-1 4-1 1-1 3-1 5-1-2 1-3 2-4 2s-2 1-3 1c-1 1-1 0-2 1h-1c-2 1-3 0-4 1v1-1c1-1 3 0 4 0h1v2c2 1 3 0 4 0l-1 2c1 1 1 0 2 0 0 0 1 1 1 2h2s1 1 1 2h0v2h-1c-1 0-1-1-1-2l-1 1-1 1s0-1-1-1c-1-1-1-2-3-2h-1v-1c0-1-1-2-1-2v-1c-1 0-2 1-3 0l1-2-1-1-1 1h-1c0-2 0-2 1-2l3-1 2-1z" class="X"></path><path d="M428 280c1 0 1 0 3-1v1h0l-1 1c2 1 2 1 3 1s2 1 3 1v1h0l-1 1-1 1s0-1-1-1c-1-1-1-2-3-2h-1v-1c0-1-1-2-1-2z" class="C"></path><path d="M453 325c2 0 4 2 6 3l11 6v-3c0 1 1 1 2 2l2 1c3 2 6 3 9 5l1-1v-2l2 2h1c1 1 1 2 2 3 1 0 2 1 2 1h2l-4-4-5-4 1-1h0v-1c1 1 2 1 3 1h0l-2-2h1c6 3 11 7 15 12h0c1 1 3 3 3 4 0 0 0 1 1 1-1 1-1 0-2-1h-2l-1-1 2 2v3c2 2 3 4 5 7l-1 1h0l4 6c1 2 2 3 2 5 1 2 1 3 2 5h-2-1 0c0 2 2 5 0 7l-1-2c-2-2-4-3-6-5l-1-1-1-1-3-1c-1-1-2-2-3-2l-1 2v-1l-1-4h-1c-1-4-2-8-5-11h-3c-2-3-5-5-8-7v-2c0-1-1-1-2-2l-2-2-1-1v-1c0-1-1-1-2-2l-3-2c1 0 2 1 3 1 0-1-1-2-1-2-2-1-3-2-5-3-4-2-8-5-12-8z" class="E"></path><path d="M485 345c1 0 1 1 2 1l3 3h-1-1-1 0s-1-1-1-2l-1-2z" class="p"></path><path d="M470 331c0 1 1 1 2 2l2 1 1 2v1l-5-3v-3z" class="D"></path><path d="M470 336c1 0 2 1 3 2s2 2 3 2c-1 1-1 2-2 3l-1-1v-1c0-1-1-1-2-2l-3-2c1 0 2 1 3 1 0-1-1-2-1-2z" class="r"></path><path d="M477 341c3 1 7 4 8 7l1 2c-3-2-8-5-9-9zm-3-7c3 2 6 3 9 5 4 3 7 7 11 11l-1 1c-1-2-4-4-5-6l-1 1c-1 0-1-1-2-1l-2-2v-1h0c-2-2-5-3-8-5v-1l-1-2z" class="J"></path><path d="M483 342c1 0 5 2 5 3l-1 1c-1 0-1-1-2-1l-2-2v-1z" class="K"></path><path d="M488 345c1 2 4 4 5 6l8 9c1 1 3 3 3 5-1 0-2-1-3-1-1-2-3-3-4-4l-6-6c0-2-2-4-4-5h1 1 1l-3-3 1-1z" class="T"></path><path d="M487 349h1 1 1c1 1 2 1 3 3s3 3 4 6v2l-6-6c0-2-2-4-4-5z" class="i"></path><path d="M476 340l1 1c1 4 6 7 9 9l5 3c-2 1-2 1-3 1h-1l2 2h-3c-2-3-5-5-8-7v-2c0-1-1-1-2-2l-2-2c1-1 1-2 2-3z" class="B"></path><path d="M478 347l9 7 2 2h-3c-2-3-5-5-8-7v-2z" class="d"></path><path d="M491 353v1l6 6c1 1 3 2 4 4l1 2c1 2 2 6 4 8-1 0-1 1-1 1l-1-1-1-1-3-1c-1-1-2-2-3-2l-1 2v-1l-1-4h-1c-1-4-2-8-5-11l-2-2h1c1 0 1 0 3-1z" class="D"></path><path d="M502 370l1 3-3-1 1-1 1-1z" class="H"></path><path d="M495 367c-1-4-2-7-4-10 3 2 5 4 6 6 2 2 4 4 5 7l-1 1-1 1c-1-1-2-2-3-2l-1 2v-1l-1-4z" class="q"></path><path d="M501 371c-2-1-3-3-4-5 0-1 0-2-1-2-1-1-1-2-1-3 0 1 1 1 2 2h0c2 2 4 4 5 7l-1 1z" class="b"></path><defs><linearGradient id="n" x1="511.753" y1="370.154" x2="505.171" y2="370.812" xlink:href="#B"><stop offset="0" stop-color="#1a1814"></stop><stop offset="1" stop-color="#48494e"></stop></linearGradient></defs><path fill="url(#n)" d="M499 353c0-1-1-3-2-4h0l3 2 2 3c2 1 4 3 5 5h0l4 6c1 2 2 3 2 5 1 2 1 3 2 5h-2-1 0c0 2 2 5 0 7l-1-2c-2-2-4-3-6-5 0 0 0-1 1-1-2-2-3-6-4-8l-1-2c1 0 2 1 3 1 0-2-2-4-3-5l-8-9 1-1c1 0 1 1 1 1l2 2 1 1s1 1 1 2h0 1c-1-1-1-2-1-3h0z"></path><path d="M502 354c2 1 4 3 5 5h0v1l2 6h0s-1 0-1 1c-2-5-4-9-6-13z" class="g"></path><path d="M508 367c0-1 1-1 1-1h0l-2-6v-1l4 6c1 2 2 3 2 5 1 2 1 3 2 5h-2-1c-1-3-2-6-4-8zm-14-17c1 0 1 1 1 1l2 2 1 1s1 1 1 2h0 1c-1-1-1-2-1-3h0l3 5 3 6 3 6c1 3 2 7 3 10-2-2-4-3-6-5 0 0 0-1 1-1-2-2-3-6-4-8l-1-2c1 0 2 1 3 1 0-2-2-4-3-5l-8-9 1-1z" class="d"></path><path d="M499 353l3 5h-2c0-1-1-1-1-2-1-1-1-1-1-2 0 0 1 1 1 2h0 1c-1-1-1-2-1-3h0z" class="u"></path><path d="M502 366c3 2 3 3 5 5v2l1 1v1l-2-2h-1l1 1c-2-2-3-6-4-8z" class="K"></path><path d="M486 331h1c6 3 11 7 15 12h0c1 1 3 3 3 4 0 0 0 1 1 1-1 1-1 0-2-1h-2l-1-1 2 2v3c2 2 3 4 5 7l-1 1c-1-2-3-4-5-5l-2-3-3-2h0c1 1 2 3 2 4h0c0 1 0 2 1 3h-1 0c0-1-1-2-1-2l-1-1-2-2s0-1-1-1c-4-4-7-8-11-11l1-1v-2l2 2h1c1 1 1 2 2 3 1 0 2 1 2 1h2l-4-4-5-4 1-1h0v-1c1 1 2 1 3 1h0l-2-2z" class="G"></path><path d="M500 351l-1-1v-1c-2-2-4-4-5-7h0c4 1 7 6 9 9 2 2 3 4 5 7l-1 1c-1-2-3-4-5-5l-2-3z" class="L"></path><defs><linearGradient id="o" x1="506.598" y1="343.72" x2="489.242" y2="338.785" xlink:href="#B"><stop offset="0" stop-color="#2f2d2c"></stop><stop offset="1" stop-color="#4f5052"></stop></linearGradient></defs><path fill="url(#o)" d="M486 331h1c6 3 11 7 15 12h0c1 1 3 3 3 4 0 0 0 1 1 1-1 1-1 0-2-1h-2l-1-1c-4-5-10-9-16-13h0v-1c1 1 2 1 3 1h0l-2-2z"></path><defs><linearGradient id="p" x1="527.547" y1="378.825" x2="518.813" y2="382.778" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#2c2c2d"></stop></linearGradient></defs><path fill="url(#p)" d="M505 332c0 1 1 1 1 1h1c1 1 2 2 4 3h1l-1 1h0c1 2 1 3 1 5 3 2 5 5 8 8l6 9v3l4 9 3 7h0c0 1 1 2 0 4h0c0-1-1-2-2-3v-1c-1-2-2-2-3-3 0 1 2 5 1 6v1l1 5h0c0 1 1 1 1 1v1 1c1 1 0 4 1 4 0 1 0 3 1 4v1 1l1 1v2h0v2 1h-1c-1-1-1-1-1-2h0-1v2h0l-2-2h0 0-1l-1-1c-1 0-1-1-2-2l-1-1-2-4c-1-2-2-3-3-4l-1-1-2-3c0-2-3-4-4-6 2-2 0-5 0-7h0 1 2c-1-2-1-3-2-5 0-2-1-3-2-5l-4-6h0l1-1c-2-3-3-5-5-7v-3l-2-2 1 1h2c1 1 1 2 2 1-1 0-1-1-1-1 0-1-2-3-3-4h2v-1-1l2 1c0-1 0-1 1-2-2-2-2-3-2-5h0l-1-1 1-2z"></path><path d="M528 403l1-2h1v-1c-1 0-1-1-1-2s0-2 1-3v1 2c2 2 3 5 4 7v1h-1c-1-1-1-1-1-2h0-1v2h0l-2-2h0 0-1l-1-1h1z" class="S"></path><path d="M528 403c0-1 1-1 1-1l2 2v2h0l-2-2h0 0-1l-1-1h1z" class="Q"></path><path d="M508 353l1-1 1 1h1c1 0 1 1 2 2l2 4v1c2 3 4 5 5 8-1-1-1-1-2-3s-3-4-4-6l-1 1c2 2 4 5 4 8h1l-10-15z" class="B"></path><path d="M510 353h1c1 0 1 1 2 2l2 4v1l-5-7z" class="p"></path><path d="M513 353l4 4h0c0-1 0-1 1-1l3 6c2 3 3 6 4 9l-1-1c-1 1 0 3-1 5 0-1 0-2-1-3l-2-4c-1-3-3-5-5-8v-1l-2-4v-2z" class="R"></path><path d="M517 357h0c0-1 0-1 1-1l3 6c2 3 3 6 4 9l-1-1c-1 1 0 3-1 5 0-1 0-2-1-3l1-1c-1-5-4-10-6-14z" class="D"></path><path d="M522 382c1 1 1 1 1 3l4 7-1 1h0v1c0 2 1 3 1 5h0v1 1 1-1h-2l-1-1-2-4c-1-2-2-3-3-4h1v-5l2 1v-6z" class="M"></path><path d="M527 400c-1-1-1-1-1-2s0-2-1-3h0l1-1c0 2 1 3 1 5h0v1z" class="I"></path><path d="M527 392l-1 1c-2-2-3-6-3-8h0l4 7z" class="J"></path><path d="M520 387l2 1v8c-1-2-2-3-3-4h1v-5z" class="K"></path><path d="M523 362l1 1h0c0-1 1-1 2-1l4 9 3 7h0c0 1 1 2 0 4h0c0-1-1-2-2-3v-1c-1-2-2-2-3-3 0 1 2 5 1 6v1l-4-11c-1-3-2-6-4-9h2z" class="y"></path><path d="M523 362l1 1c1 4 3 8 4 12 0 1 2 5 1 6v1l-4-11c-1-3-2-6-4-9h2z" class="E"></path><path d="M501 346l1 1h2c1 1 1 2 2 1-1 0-1-1-1-1l4 5-1 1 10 15c1 5 2 9 4 14v6l-2-1v-5l-1-4c-1-5-3-10-6-14h-1 0l-1 1-4-6h0l1-1c-2-3-3-5-5-7v-3l-2-2z" class="m"></path><path d="M508 358c1 1 2 2 3 4l1 2-1 1-4-6h0l1-1z" class="O"></path><path d="M502 347h2c1 1 1 2 2 1-1 0-1-1-1-1l4 5-1 1-6-6z" class="S"></path><path d="M512 364h0 1c3 4 5 9 6 14l1 4v5 5h-1l-1-1-2-3c0-2-3-4-4-6 2-2 0-5 0-7h0 1 2c-1-2-1-3-2-5 0-2-1-3-2-5l1-1z" class="l"></path><path d="M518 379v-1h1l1 4h-1c-1 1-1 3-1 5v-8z" class="c"></path><path d="M518 387c0-2 0-4 1-5h1v5 5h-1l-1-5z" class="U"></path><path d="M512 364h1c3 4 5 9 6 14h-1v1c-1 0-1-4-2-5-1-3-2-6-4-10z" class="p"></path><path d="M513 375h2c0 2 1 5 1 7v3c0 1 1 2 1 3h-1c0-2-3-4-4-6 2-2 0-5 0-7h0 1z" class="U"></path><path d="M513 375h2c0 2 1 5 1 7v3l-3-10z" class="n"></path><defs><linearGradient id="q" x1="512.477" y1="353.701" x2="518.091" y2="345.966" xlink:href="#B"><stop offset="0" stop-color="#0a0c0c"></stop><stop offset="1" stop-color="#323131"></stop></linearGradient></defs><path fill="url(#q)" d="M505 332c0 1 1 1 1 1h1c1 1 2 2 4 3h1l-1 1h0c1 2 1 3 1 5 3 2 5 5 8 8l6 9v3c-1 0-2 0-2 1h0l-1-1h-2l-3-6c-1 0-1 0-1 1h0l-4-4v2c-1-1-1-2-2-2h-1l-1-1-4-5c0-1-2-3-3-4h2v-1-1l2 1c0-1 0-1 1-2-2-2-2-3-2-5h0l-1-1 1-2z"></path><path d="M505 332c0 1 1 1 1 1h1c1 1 2 2 4 3h1l-1 1h0c1 2 1 3 1 5-2-2-4-5-7-7h0l-1-1 1-2z" class="d"></path><path d="M507 340c6 7 12 13 16 22h-2l-3-6c-1 0-1 0-1 1h0l-4-4v2c-1-1-1-2-2-2h-1l-1-1-4-5c0-1-2-3-3-4h2v-1-1l2 1c0-1 0-1 1-2z" class="n"></path><path d="M512 350s-1-1-1-2c1 1 1 2 2 2h0c2 2 4 4 5 6-1 0-1 0-1 1h0l-4-4c0-1-1-2-1-3z" class="U"></path><path d="M504 341l2 1 6 6c0 1 1 2 1 2h0c-1 0-1-1-2-2 0 1 1 2 1 2-2-2-7-6-8-9z" class="J"></path><defs><linearGradient id="r" x1="510.854" y1="347.926" x2="505.491" y2="348.817" xlink:href="#B"><stop offset="0" stop-color="#717072"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#r)" d="M504 342v-1c1 3 6 7 8 9 0 1 1 2 1 3v2c-1-1-1-2-2-2h-1l-1-1-4-5c0-1-2-3-3-4h2v-1z"></path><path d="M505 306v-1l2 1v-1c1 0 1 0 2 1 0 0 1 0 2 1l1-1c2 2 3 4 5 6 0 0 0 1 1 2v-1l-1-2c-1-1-1-1-1-2 1 0 3 2 3 2l7 12c1 0 1 0 1-1 1 2 2 3 2 5 1 1 1 2 2 3 0 1 0 1 1 2 1-1 0-2 0-4l1 1v3c-1 1-1 2-1 3s0 1 1 1l-1 2c1 3 2 5 2 8 1 1 1 3 1 4l2 7h0v1h1v-1h1v-1c1 0 1 1 1 2l4 14h-1c-1 1 0 3-1 4h0l-2-3c-1 1-1 1-1 2l-2-2h-1v2l-2-3c-1 0-3-1-4-1l-4-9v-3l-6-9c-3-3-5-6-8-8 0-2 0-3-1-5h0l1-1h-1l-3-3c1 0 0-1 1-2-2-2-5-4-8-5v-3l1-1-4-2c0-1 0 0-1-1h0l1-1h0c3-1 4 1 6 0h0v-1h1l1 1h1c1 0 2 1 2 1h1 1l1 1c0-1 1-2 2-3l-1-2c-1-2-2-3-4-5-1 0-3-3-3-3l-1-1z" class="D"></path><path d="M524 339c1 2 3 5 4 8h-1-1l-3-6h0l1 1v-3z" class="l"></path><path d="M535 367c0-1 0-1 1-1 1 1 4 6 4 7-1 1-1 1-1 2l-2-2-2-6z" class="F"></path><path d="M519 326l2-2 3 6c0 2 1 3 1 5l-6-9z" class="c"></path><path d="M511 319l1 1 2 3c2 1 3 4 4 6-2 0-2-1-3-2h0c-2-2-2-3-3-4s-2-2-3-4h1 1z" class="K"></path><path d="M511 319l1 1 2 3h0c-2 0-3-2-4-4h1z" class="d"></path><path d="M534 354h0c0-2 0-3 1-4l2 7c0 2 0 4 1 7 0 1 1 3 1 4h-1c-2-5-4-9-4-14z" class="l"></path><path d="M539 357v-1c1 0 1 1 1 2l4 14h-1c-1 1 0 3-1 4 0-2-1-3-1-5s-1-4-2-7v-7z" class="F"></path><path d="M504 318v-1h1l1 1h1c1 0 2 1 2 1 1 2 2 3 3 4s1 2 3 4h-2v2l-4-4c0-1-1-3-1-4-1-1-2-2-4-3h0z" class="i"></path><path d="M508 321c1 2 4 4 5 6v2l-4-4c0-1-1-3-1-4z" class="o"></path><path d="M498 318c3-1 4 1 6 0 2 1 3 2 4 3 0 1 1 3 1 4h0c-2-1-3-2-5-3l-1 1-1-1-4-2c0-1 0 0-1-1h0l1-1h0z" class="Q"></path><path d="M498 318c2 1 4 2 5 3l1 1-1 1-1-1-4-2c0-1 0 0-1-1h0l1-1z" class="D"></path><path d="M528 347c3 4 5 10 7 16 0 0 1 2 1 3-1 0-1 0-1 1 0 0-1-3-2-4l-7-16h1 1z" class="e"></path><defs><linearGradient id="s" x1="515.404" y1="326.564" x2="522.394" y2="342.192" xlink:href="#B"><stop offset="0" stop-color="#858485"></stop><stop offset="1" stop-color="#b5b5b4"></stop></linearGradient></defs><path fill="url(#s)" d="M515 327h0c1 1 1 2 3 2l5 8c0 1 1 2 1 2v3l-1-1h0l-10-12v-2h2z"></path><path d="M525 347h1l7 16c1 1 2 4 2 4l2 6h-1c-5-8-7-18-11-26z" class="f"></path><path d="M509 325h0l4 4 10 12 3 6h-1c-5-7-10-14-17-20l1-2z" class="S"></path><defs><linearGradient id="t" x1="499.65" y1="324.855" x2="535.85" y2="365.214" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#c8c8c8"></stop></linearGradient></defs><path fill="url(#t)" d="M503 323l1-1c2 1 3 2 5 3l-1 2c7 6 12 13 17 20 4 8 6 18 11 26v2l-2-3c-1 0-3-1-4-1l-4-9v-3l-6-9c-3-3-5-6-8-8 0-2 0-3-1-5h0l1-1h-1l-3-3c1 0 0-1 1-2-2-2-5-4-8-5v-3l1-1 1 1z"></path><path d="M503 323l1-1c2 1 3 2 5 3l-1 2c-1-1-3-3-5-4z" class="B"></path><path d="M509 331l4 4v1h-1-1l-3-3c1 0 0-1 1-2z" class="U"></path><path d="M513 335c1 1 2 2 3 4 0 0 0 1 1 2-1 0-1-1-2-1 0 0-3-3-3-4h1v-1z" class="R"></path><path d="M516 339c3 3 5 7 7 11h0l-8-10c1 0 1 1 2 1-1-1-1-2-1-2z" class="Y"></path><path d="M526 359l8 13c-1 0-3-1-4-1l-4-9v-3z" class="M"></path><path d="M505 306v-1l2 1v-1c1 0 1 0 2 1 0 0 1 0 2 1l1-1c2 2 3 4 5 6 0 0 0 1 1 2v-1l-1-2c-1-1-1-1-1-2 1 0 3 2 3 2l7 12c1 0 1 0 1-1 1 2 2 3 2 5 1 1 1 2 2 3 0 1 0 1 1 2 1-1 0-2 0-4l1 1v3c-1 1-1 2-1 3s0 1 1 1l-1 2c1 3 2 5 2 8 1 1 1 3 1 4-1 1-1 2-1 4h0c-1-1-1-2-1-3-1-2-2-3-2-5-2-4-5-7-6-11 0-2-1-3-1-5l-3-6-2 2-1-3-4-6-1-2c-1-2-2-3-4-5-1 0-3-3-3-3l-1-1z" class="e"></path><path d="M531 344c1 2 2 3 2 5v2c-1-2-2-3-2-5v-2z" class="o"></path><path d="M533 349v-2h0l1-1c1 1 1 3 1 4-1 1-1 2-1 4h0c-1-1-1-2-1-3v-2z" class="T"></path><path d="M526 323c1 0 1 0 1-1 1 2 2 3 2 5 1 1 1 2 2 3-1 0-1 0-1 1l-4-8z" class="S"></path><path d="M531 330c0 1 0 1 1 2 1-1 0-2 0-4l1 1v3c-1 1-1 2-1 3s0 1 1 1l-1 2-2-7c0-1 0-1 1-1z" class="M"></path><path d="M516 312h1s0 1 1 2c3 5 6 10 8 15 1 2 1 4 2 6l3 9v2c-2-4-5-7-6-11 0-2-1-3-1-5l1-1c-2-5-4-9-7-13l-1-2-1-2z" class="Y"></path><path d="M505 306v-1l2 1v-1c1 0 1 0 2 1 0 0 1 0 2 1l1-1c2 2 3 4 5 6h-1l1 2 1 2c3 4 5 8 7 13l-1 1-3-6-2 2-1-3-4-6-1-2c-1-2-2-3-4-5-1 0-3-3-3-3l-1-1z" class="u"></path><path d="M515 314h2l1 2-2 1-1-3z" class="i"></path><path d="M520 322l1 2-2 2-1-3 2-1z" class="R"></path><path d="M513 315c3 2 5 4 7 7l-2 1-4-6-1-2z" class="E"></path><path d="M505 306v-1l2 1v-1c1 0 1 0 2 1 0 0 1 0 2 1l1-1c2 2 3 4 5 6h-1l1 2h-2l-5-5-1 1c-1 0-3-3-3-3l-1-1z" class="K"></path><path d="M509 306s1 0 2 1l1-1c2 2 3 4 5 6h-1c-2-1-4-4-5-5-1 0-2 0-2-1z" class="E"></path><path d="M255 268c3-2 5-3 8-4 0 1 1 1 1 2s1 2 2 3l-2 2c1 1 1 2 1 3-1 3 1 5-1 9l-1 1 1 1c0 1-1 3-1 4h0c0 1 0 1 1 2v3l3-1h0c-3 2-8 6-8 9-1 3 0 6 0 9 0 2 1 5 0 6l1 2v4 2h1l1 1h-1-1v1c0 1 1 1 1 2l-1 1h-1v2c0 1 0 1 1 2 0 0 1 0 1 1v3l1 1h0 0c0 2 1 4 1 6 0 3 1 4 0 7-1 2-1 2-1 4-1-1-1-1-1-2l-1 4v1c0 1-1 2 0 3v4l1 1h0c-2 4-2 12-1 16v6h0v2c2 5 4 9 8 13-1-1-3-2-4-3 0 0 0 1-1 1l-1-1h-1c-1 1 1 2-2 2v13 3c1 0 1 1 2 1l-2 2s1 1 0 2c0 2 2 6 2 8s2 7 1 9c0 1-1 2 0 3h0 1s1 0 1 1v1h0l-1 2c-1 0-1 1-1 1-1 1-1 2-1 3v1l1-1h1l3-1h1 1c-1 1-2 1-3 1l-3 1-1 1c-1 2-1 4-1 6v1c1-1 1-1 3-1l16-3 15-3c3-1 6-2 9-2 5-1 9-1 14-2 2 0 5 0 7-1 2 1 3 1 6 1l1 1h0c1 0 3 1 4 1l-4 3v1c-2 0-2 1-3 2v2c-5 3-10 6-15 7h-1c-1 1-3 1-4 1-1 1-1 1-2 1h-1c-1 1-2 0-4 1h-1c-1 0-1 0-2 1h-1c-1 0-3 1-4 1l-2 1s0 1-1 1-2 1-2 1h-1c-1 0-1 1-1 1l-6 3h0l-1 1h2l-3 1-2 2-1 1c-1 0-4 3-4 3-1 0-2 1-2 1-3 2-7 7-8 10v1c0 2-1 4-1 6 0 3 1 6 1 9 1 5 1 10 0 15 0 2 0 4-1 6s0 4-1 6c0 2-2 6-1 9l4 4 1 1h1v-1-3c0-1 1-2 1-3h0l-1-1c-2-2-3-4-3-8 0-1 0-1-1-2h0c2-2 2-8 2-11h0c1 1 3 2 3 2 2 3 1 7 4 9 6 5 13 3 19 4 3 1 6 3 9 4h1l3 1c2 1 3 2 5 3h3c1 0 3 1 4 1h-2v1h-1l-1 1c2 2 4 3 5 6h1 2v1c-1 0-2 1-3 1 1 1 1 1 1 2h0c0 1 0 2 1 2v3c1 1 0 5 0 6s0 2-1 3c0 1 0 3-1 4h1v2h0v1l-2 3c-1 1-1 2-1 3 1 0 1 0 2 1h0c1 1 1 2 3 3l-1 1 6 2c2 0 4 1 5 2s2 3 2 4v1c-1 2-2 3-4 4h-5 0l-5-2h-1c-3-1-7-1-11 0-2 1-4 1-6 1-4 1-8 2-11 4-1 1-2 2-3 2-1 1-1 1-1 2-2 0-4-1-5 0h-1c-1 1-1 1-2 1l-2 1h-2c-1 1-2 0-3 1h-2c-2 0-4 1-5 1l-1 1c1 1 2 1 3 1s2 0 3-1h1l2 1h1c1 1 2 1 3 2h0l2 1v2c-1 1-3 1-5 2 0 0 0 1-1 1h0 3c2 1 3 1 5 1 1 0 2 0 3 1l1 1-1 1c-1 0-1 2-2 2 0 1-2 1-3 1h-14c-4 0-9-1-13-1-7 0-14 1-20 0h-3v-1l2-1h0c-3 0-6 1-9 2-9 1-20 0-29 0-2 0-3-1-4-1-3 0-6 1-9 1-7 0-14-1-20 0h0-2l-1 3h-2-2v-21-6 2 1c0-1 0-2 1-3 0-2 0-5-1-8h0 0v-1h1v-1l2-1s1 1 2 0h4l9-1c1 1 1 1 1 2 1 0 2 0 3-1l1 1h0c-1 1-1 1-2 1h10c4 0 9-1 13-2 12-3 23-8 34-14v1c0 1-1 1-1 2l1-1h1c0-1 1-1 1-2h1c1-1 2-2 2-3s1-1 1-2l1 1h0c-1 1-1 2-1 3v1l2-2c1-1 2-2 3-2l1-2c2-2 4-3 5-5 2-3 2-5 5-6 4-7 4-17 7-25 0-1 1-2 1-3-1-2 1-4 1-6 1-2 1-4 1-6-1-4-2-7-3-11v-50-11-8c2 0 3 0 4-1 0-2 0-5-1-7l1-2h-1 0c-1 1-1 1-2 1l-1-1v-21-25-28c0-2 0-5 1-8h0c-1-3-1-6-1-9v-25-12c0-2-1-4 0-6v-1c-1-2 0-4 0-5 0-5 0-8 2-12 1-2 2-3 3-5 1-1 2-2 2-3l-1-4v-4c-2 1-3 2-5 4h0-2c1-2 0-4 1-6z" class="Z"></path><path d="M285 472c0-1-1-3 0-4h1v1c0 1-1 2 0 3h-1z" class="V"></path><path d="M295 465h0v1c-1 1-1 1-2 1h0-1l1-1v-1h2z" class="N"></path><path d="M237 581l1-2 1 2-2 2v-2z" class="U"></path><path d="M324 454h1l1 1c0 2 0 3-1 4h-1 0v-5h0z" class="L"></path><path d="M264 271v-1c0-2-1-3-2-4l1-1 1 1c0 1 1 2 2 3l-2 2z" class="H"></path><path d="M254 571c1 0 1 0 2 1v1l-1 2h-2v-1h0v-1c0-1 1-1 1-1v-1z" class="t"></path><path d="M166 621h1 2 0l-1 2c-1 1-2 1-3 2v-1c0-1 1-2 1-3h0z" class="b"></path><path d="M237 581v2l-4 3 1-3c1-1 2-2 3-2z" class="B"></path><path d="M253 610c1 0 1 0 2 1 2 0 4 0 6 1l1 1c-3 0-7-1-9-3h0z" class="N"></path><path d="M268 628h3c2 1 3 1 5 1l-7 1h-3l-1-1h0l1-1h2z" class="E"></path><path d="M187 609h1c0 2 1 2 2 3v1l-1-1h-2-1c-1-1-1-1-1-2v-1h2z" class="K"></path><path d="M152 633h1v-3h0c0 1 0 3 1 4h1 0-2l-1 3h-2v-2-2h2z" class="E"></path><path d="M262 278c0 1 1 2 0 3 0 1-1 2-2 3 0 1-1 1-1 1l-2 1c1-2 2-3 3-5 1-1 2-2 2-3z" class="C"></path><path d="M226 623h0l1 1-1 2h-1-2c0 1 1 1 1 2h0l-2-1c-1-1-1-1 0-2s2-1 4-2z" class="n"></path><path d="M239 629h3 1v1h4c-6 0-13 1-19 0h10v-1h1z" class="Y"></path><path d="M180 611c1 1 1 3 1 5 0 1-1 1-1 1-1 0-1 0-1-1-1-1 0-3-1-5h0 2z" class="C"></path><path d="M224 628c2 0 4 1 6 1 1-1 3 0 5 0h3v1h-10c-2 0-3-1-4-2h0z" class="Q"></path><path d="M259 317l1 2v4 2h1l1 1h-1-1v1c0 1 1 1 1 2l-1 1h-1l-1-8h1v-5z" class="Y"></path><path d="M238 579c2-2 4-3 5-5 2-3 2-5 5-6-2 5-5 9-9 13l-1-2z" class="c"></path><path d="M252 629l14-1-1 1h0l1 1h3c-5 1-10 1-16 1-1 0-3 0-5-1h4 0l-1-1h1z" class="h"></path><path d="M262 481c1 2 1 6 2 9h0l-3 3-1 1-1-1c0-1 0-1 1-2v-2h0l-1-1 1-1v-2l1-1v-2l1-1z" class="N"></path><path d="M298 545l3 1c2 1 3 2 5 3h3c1 0 3 1 4 1h-2v1h-1l-1 1c2 2 4 3 5 6h1 2v1c-1 0-2 1-3 1h0c-3-7-11-12-17-15h1z" class="H"></path><path d="M297 456h1c0 2 1 3 0 4-2 2-5 3-7 4h-1c2-2 6-2 8-5l-1-1-5 1-6 2c-2 1-4 0-5 1h-1v-1c2-4 14-1 17-5z" class="O"></path><path d="M163 611c4-1 8-1 12-1 1 0 3 0 5 1h-2 0c-1 1-2 1-3 1h0-3l-1 1h0 0 0c2-1 2-1 4-1 1 1 1 2 1 3v1l-2-2h-1v1h-1l-1-1h0c-1 0-1-1-1-1h-1c0 1 0 1-1 1-1 1-1 1-1 2-1-1-1-1-1-2v-2h-1c1 0 2 0 3-1h0-5z" class="F"></path><path d="M260 358v1c0 1-1 2 0 3v4l1 1h0c-2 4-2 12-1 16v6h0v2-1h-1c-1-6-1-13-1-19 0-4 0-8 1-12l1-1z" class="R"></path><path d="M163 611h5 0c-1 1-2 1-3 1 0 1 0 2-1 3s-3 1-5 1v2 1 4c-1 0-1 0-1 1-1-2 0-3 0-4v-9h2 3z" class="L"></path><path d="M226 623h4c1 0 1 0 2 1h0v2l1-1h1l2 1h1v1h0c1 0 2 1 2 2h-1-3c-2 0-4-1-5 0-2 0-4-1-6-1 0-1-1-1-1-2h2 1l1-2-1-1z" class="H"></path><path d="M230 623c1 0 1 0 2 1h0v2h-4l2-3z" class="l"></path><path d="M226 623h4l-2 3-1 1-1-1 1-2-1-1zm-31 4v1h1c1 1 2 0 3 0 1 1 3 0 4 2h-2c-1-1-3 0-5 0-1-1-2-1-3-1h-2c-1 0-1-1-2-1h0-2c-2-1-4-1-6-1 0-1-1-3 0-4 1 1 1 1 3 0h0l1 1c0 1 1 1 2 0h2l1 1c1 1 2 1 2 2h1c1 0 1 0 1-1h1v1z" class="C"></path><path d="M284 566h0c1 0 0 1 1 2v-1c2 1 3 3 4 5 1 0 1 0 1 1v1h0c0 1 0 1-1 2 0 0-1 1-2 1s-1-1-2-1l-3-3c0-1 0-1 1-2v-2-1c0-1 0-2 1-2z" class="t"></path><path d="M225 588l1-1h1c0-1 1-1 1-2h1c1-1 2-2 2-3s1-1 1-2l1 1h0c-1 1-1 2-1 3v1l2-2-1 3c-6 5-14 9-22 11l1-1c1-1 1 0 1-1 1-1 3-1 5-2l3-3c1-1 3-1 4-2z" class="I"></path><path d="M259 332c0 1 0 1 1 2 0 0 1 0 1 1v3l1 1h0 0c0 2 1 4 1 6 0 3 1 4 0 7-1 2-1 2-1 4-1-1-1-1-1-2l-1 4-1 1c1-9 1-18 0-27z" class="K"></path><path d="M262 339h0c0 2 1 4 1 6 0 3 1 4 0 7-1 2-1 2-1 4-1-1-1-1-1-2s-1-3 0-4v-5-4c0-1 1-2 1-2h0z" class="F"></path><path d="M215 600h2c3-1 6-2 8-4 3-2 5-4 9-5 2-1 4-2 6-1l1 1h0c-2 2-3 3-5 3-1 2-1 3-2 5 0-1-1-1-1-1v-1c-1-1-1-2-1-3-1 0-1 0-2 1-2 1-3 2-5 3l-6 2c-1 1-2 1-3 1l-1-1z" class="g"></path><path d="M240 590l1 1h0c-2 2-3 3-5 3 0-1 2-2 2-3h2v-1z" class="j"></path><defs><linearGradient id="u" x1="157.329" y1="620.301" x2="147.586" y2="611.414" xlink:href="#B"><stop offset="0" stop-color="#5e5858"></stop><stop offset="1" stop-color="#777b7c"></stop></linearGradient></defs><path fill="url(#u)" d="M151 613v-6l3-2c1-2 2-1 4-1h0 3c0 1 0 1 1 2h-1-1-6l-1 24h0v3h-1l-1-20z"></path><path d="M158 604h0 3c0 1 0 1 1 2h-1-1c-1-1-3 0-4-1-1 0-1 0-1-1h2 1z" class="Y"></path><path d="M215 600l1 1c1 0 2 0 3-1l6-2c2-1 3-2 5-3 1-1 1-1 2-1 0 1 0 2 1 3v1l-1 1v-2c-1 0-3 2-4 3s-3 1-4 2c-2 1-9 3-11 3h-1v-1c1 0 1-1 2-1h0v-1h-1 0c-1 0-1 0-2 1h0-1v-1-1c1 0 2 0 3-1h2z" class="F"></path><path d="M241 591h0c3 2 6 5 8 7v1c-2-1-4-5-6-5-2 2-3 4-3 6s1 4 2 6c0 1 2 2 3 2 2 2 5 4 7 5v1 1l-1-1c-1-1-5-5-7-5h-1c0 2 1 3 3 4 1 1 2 1 3 2l1 1c1 1 4 1 5 1l2 2h1v1h0c-3-1-7-2-9-4-2-1-4-2-6-4 0 0-1 0-1-1l-1-1v-1h-2v-1h-1c-1-1-1-1-1-2 1 0 2-1 2-1 1 0 1-1 1-2h-1-1l1-1v-2c0-1 0-2 1-4l2-3-1-2z" class="T"></path><path d="M237 606c1 0 2-1 2-1v1l1-1 2 2v1h-2-1-1c-1-1-1-1-1-2z" class="C"></path><path d="M241 591l1 2-2 3c-1 2-1 3-1 4v2l-1 1h1 1c0 1 0 2-1 2 0 0-1 1-2 1v-1l-1-1h0c-1 0-1 1-1 1-1 1-1 2-1 3l-1-1h0v-3-3h-1l-1-1 1-1 1-1s1 0 1 1c1-2 1-3 2-5 2 0 3-1 5-3z" class="s"></path><path d="M241 591l1 2-2 3v-1h-3l-1 1v1c1 0 2 0 2 1-1 1-2 1-4 1h0c1-2 1-3 2-5 2 0 3-1 5-3z" class="N"></path><path d="M233 598s1 0 1 1h0v4l5-1-1 1h1 1c0 1 0 2-1 2 0 0-1 1-2 1v-1l-1-1h0c-1 0-1 1-1 1-1 1-1 2-1 3l-1-1h0v-3-3h-1l-1-1 1-1 1-1z" class="G"></path><path d="M239 603h1c0 1 0 2-1 2 0 0-1 1-2 1v-1l-1-1h0l3-1z" class="F"></path><path d="M169 601h10l-8 2h0 0c-1 0-2 1-3 2h0 4 7v1c-1 1-3 1-4 1-4 1-7 1-11 0h-1c0-1 0-1-1-1h-1 1c-1-1-1-1-1-2h-3c2-1 4-2 7-2l4-1z" class="C"></path><path d="M161 604c3 0 7-1 10-1h0 0c-1 0-2 1-3 2h0 4c-3 1-7 1-10 1-1-1-1-1-1-2z" class="c"></path><path d="M169 601h10l-8 2c-3 0-7 1-10 1h-3c2-1 4-2 7-2l4-1z" class="h"></path><path d="M263 284l1 1c0 1-1 3-1 4h0c0 1 0 1 1 2v3l3-1h0c-3 2-8 6-8 9-1 3 0 6 0 9 0 2 1 5 0 6v5h-1l-1-20h0c1-2 0-5 1-7s2-3 3-5 2-4 2-6z" class="R"></path><path d="M264 294c-2 2-3 5-5 5 0-1 0-2 1-2l-1-1v-1l1-1v-1c1-2 2-3 3-4h0c0 1 0 1 1 2v3z" class="X"></path><path d="M149 610v-2c2 2 0 4 2 5h0l1 20h-2v2 2h-2v-21-6 2 1c0-1 0-2 1-3h0z" class="P"></path><path d="M149 610v-2c2 2 0 4 2 5 0 3 0 7-1 10l-1-13z" class="u"></path><path d="M151 613l1 20h-2v2-12c1-3 1-7 1-10h0z" class="w"></path><path d="M315 582v1l-2 3c-1 1-1 2-1 3 1 0 1 0 2 1h0c1 1 1 2 3 3l-1 1 6 2c2 0 4 1 5 2s2 3 2 4v1c-1 2-2 3-4 4h-5 0l-5-2h-1c-3-1-7-1-11 0-2 1-4 1-6 1-4 1-8 2-11 4-1 1-2 2-3 2v-2c2-1 5-3 7-3 4-2 8-2 11-2 4-1 9-2 13-2 4 1 7 3 11 3 1 0 1-1 2-1 1-1 1-2 1-3-1-7-11-6-15-9-1-1-2-2-2-4s2-6 3-7h1z" class="u"></path><path d="M312 589c1 0 1 0 2 1h0c1 1 1 2 3 3l-1 1-1-1c-2 0-2-2-3-4z" class="O"></path><path d="M259 390h1v1c2 5 4 9 8 13-1-1-3-2-4-3 0 0 0 1-1 1l-1-1h-1c-1 1 1 2-2 2v13 3c1 0 1 1 2 1l-2 2s1 1 0 2c0 2 2 6 2 8s2 7 1 9c-7-17-4-34-3-51z" class="J"></path><path d="M259 403c0-1 0-3 1-4v-5 1c1 2 2 4 4 5v1s0 1-1 1l-1-1h-1c-1 1 1 2-2 2z" class="N"></path><defs><linearGradient id="v" x1="168.569" y1="596.797" x2="156.025" y2="603.906" xlink:href="#B"><stop offset="0" stop-color="#b2b2b2"></stop><stop offset="1" stop-color="#dfdcdc"></stop></linearGradient></defs><path fill="url(#v)" d="M151 599s1 1 2 0h4l9-1c1 1 1 1 1 2 1 0 2 0 3-1l1 1h0c-1 1-1 1-2 1l-4 1c-3 0-5 1-7 2h0c-2 0-3-1-4 1l-3 2v6h0c-2-1 0-3-2-5v2h0c0-2 0-5-1-8h0 0v-1h1v-1l2-1z"></path><path d="M148 602h0 0v-1h1 5c-1 0-1 1-2 1h-4z" class="Z"></path><path d="M152 602h13c-3 0-5 1-7 2h0c-2 0-3-1-4 1l-3 2v6h0c-2-1 0-3-2-5v2h0c0-2 0-5-1-8h4z" class="T"></path><path d="M275 468c1 0 1-1 2-1-1-1-1-1-2-1h-1v-1-1c2 0 4 0 5 1v3 1l2-1v-3c1 1 1 1 1 2v1s-1 0-1 1v1h-2v1c0 1 0 1 1 2 0-1 1-1 1-1h1 3 1l4-1h0c-1 1-2 2-4 2-1 1-4 0-6 0 0 1-1 1-2 2v-1-2h-1v3l1 1c-1 1-2 2-1 3h1c-1 1-3 2-4 3l-3 1v-1l1-1c0-1 0-4-1-5 1-1 1-2 1-3v-3c0-1 0-2 1-3l2 1z" class="C"></path><path d="M272 481c0-1 0-4-1-5 1-1 1-2 1-3v-3c0-1 0-2 1-3l2 1c1 2 1 3 0 4 0 1-1 2-1 3h0 2 0c0 1 0 2-1 3h0c-1 0-2 1-2 2v1h-1z" class="Z"></path><path d="M212 604v1h1c2 0 9-2 11-3 1-1 3-1 4-2s3-3 4-3v2l-1 1 1 1h1v3l-2 1h-1-1v2l-12 1h-3l-11-3c3-1 6 0 9-1z" class="t"></path><path d="M232 601h1v3l-2 1h-1c0-1 1-2 1-2 1-1 1-1 1-2h0z" class="L"></path><path d="M185 617c1-1 1-1 2-1h1l1 1 7 1c1 0 2 0 3 1v1c-1 1-1 1-1 2l1 1c1 0 4 1 5 0h1 1c0 1 0 1 1 1 2 0 12 0 13 1-3 1-14 0-17 0-1 0-3 0-4 1-2 1-2 1-4 1v-1h-1c0 1 0 1-1 1h-1c0-1-1-1-2-2l-1-1h-2c-1 1-2 1-2 0l-1-1h0c0-1 0-2-1-3v-1-1h0v-1h2z" class="X"></path><path d="M185 617c1-1 1-1 2-1h1l1 1 7 1c1 0 2 0 3 1v1c-1 1-1 1-1 2h-2-1c-2-1-4-1-6-1h0 0c0-2 1 0 2-1 0-1 0-1-1-2h-1-2c-1 0-2-1-2-1z" class="L"></path><defs><linearGradient id="w" x1="200.37" y1="600.951" x2="194.426" y2="589.913" xlink:href="#B"><stop offset="0" stop-color="#040405"></stop><stop offset="1" stop-color="#4a484a"></stop></linearGradient></defs><path fill="url(#w)" d="M226 585v1c0 1-1 1-1 2-1 1-3 1-4 2l-3 3c-2 1-4 1-5 2 0 1 0 0-1 1l-1 1c-9 4-21 6-32 8h-7-4 0c1-1 2-2 3-2h0 0l8-2c4 0 9-1 13-2 12-3 23-8 34-14z"></path><path d="M260 494c0 1 0 2-1 2h0 0c-1-2 0-5-1-7v-8-10c1-1 0-3 0-4 0-3 1-6 1-8v-5c1 0 0-1 1-2v-1-2c1-1 1-2 2-3h2l-1 2c-1 0-1 1-1 1-1 1-1 2-1 3v1l1-1h1l3-1h1 1c-1 1-2 1-3 1l-3 1-1 1c-1 2-1 4-1 6v1c1-1 1-1 3-1l16-3 15-3c3-1 6-2 9-2 5-1 9-1 14-2 2 0 5 0 7-1 2 1 3 1 6 1l1 1h0c-3 0-6 1-9 1-1-1-3-1-4-1l-10 1-15 3-15 3c-4 1-9 2-13 2-1 1-2 1-3 1 0 1 0 1 1 1l1 1c1 0 1 0 1-1h3c-1 1-1 1-2 1-2 1-1 2-3 2v-1l-1 1c0 1 0 2-1 3v3c-1 1-1 1-1 2l1 1v1c0 1 0 1 1 2l-1 1s0 1 1 2v1l-1 1v2l-1 1v2l-1 1 1 1h0v2c-1 1-1 1-1 2l1 1z" class="l"></path><path d="M262 461c0 1 0 1 1 1l1 1c1 0 1 0 1-1h3c-1 1-1 1-2 1-2 1-1 2-3 2v-1l-1 1c0 1 0 2-1 3v3c-1 1-1 1-1 2l1 1v1c0 1 0 1 1 2l-1 1s0 1 1 2v1l-1 1v2l-1 1v2c-1-3-1-5-1-8v-8h1c0-2-1-3 0-4 0-1 0-3 1-4 0 0 1-1 1-2z" class="F"></path><path d="M262 461c1 0 2 0 3-1 4 0 9-1 13-2l1 1v3 3h0c-1-1-3-1-5-1v1 1h1c1 0 1 0 2 1-1 0-1 1-2 1l-2-1c-1 1-1 2-1 3v3c0 1 0 2-1 3 1 1 1 4 1 5l-1 1v1 1h0c-2 1-3 2-5 4l-1-21c0-1 0-1-1-2h-2l1-1v1c2 0 1-1 3-2 1 0 1 0 2-1h-3c0 1 0 1-1 1l-1-1c-1 0-1 0-1-1z" class="N"></path><path d="M267 475c0-1 0-1 1-2h1c0 1-1 2 0 2v1 1h-1c-1 0-1 1-1 1 0-1 0-2 1-3h-1z" class="F"></path><path d="M279 462c-1 0-2 1-2 1-1 0-2 0-3-1v-2h1c0 1 0 1 1 1v-1c1-1 2-1 3-1v3z" class="b"></path><path d="M265 467l1-1h1v3h0c-1 2 0 3-1 5l1 1h1c-1 1-1 2-1 3v7h1 0c1-1 2-1 2-2l1 1c-2 1-3 2-5 4l-1-21z" class="n"></path><path d="M195 609l-1-1-1-1c1 0 1-1 2-1 0 0 1 0 1-1h1c1 0 1 0 2-1h1c2-1 5-1 7-1 1-1 2-1 3-2v1 1h1 0c1-1 1-1 2-1h0 1v1h0c-1 0-1 1-2 1-3 1-6 0-9 1l11 3h3c1 0 1 1 1 2 1 0 2 0 2 1l1 1h2c1 0 2-1 2-1 1-1 1-1 1-2v-1c1 0 1 0 2 1 0 1-1 2-1 3-1 0-2 1-2 2v1c0 1 1 2 1 3 1 0 3 1 4 0h2 0v1c-2 1-3 1-4 1h-1c0 1-1 1-2 1h-1c-2-1-2-2-3-2-1 1-1 1-2 1h-1c1-1 2-1 2-2v-1h-2-3c-5 1-10 1-15 1 0-2-1-3 0-5 2-1 6 0 8 0h0c-1-1-2-1-2-1-2-1-3-1-5-1-2-1-4-2-6-2z" class="H"></path><path d="M214 608h3c1 0 1 1 1 2 1 0 2 0 2 1-1 0-2-1-3-1s0 0-1-1c-1 0-1-1-2-1z" class="F"></path><path d="M201 607c4 0 7 0 11 1v1h-3c-1 0-7-1-8-2h0z" class="N"></path><path d="M195 609c2 0 3-1 5 0h3c0 1 0 1 1 1 0 1 1 0 1 0 1 0 1 1 1 1 1 1 2 1 3 1v1c-1 1-7 0-9 0 2-1 6 0 8 0h0c-1-1-2-1-2-1-2-1-3-1-5-1-2-1-4-2-6-2z" class="C"></path><path d="M221 612h2c1 0 2-1 2-1 1-1 1-1 1-2v-1c1 0 1 0 2 1 0 1-1 2-1 3-1 0-2 1-2 2v1c0 1 1 2 1 3 1 0 3 1 4 0h2 0v1c-2 1-3 1-4 1h-1c0 1-1 1-2 1h-1c-2-1-2-2-3-2-1 1-1 1-2 1h-1c1-1 2-1 2-2v-1c0 1 1 1 1 1h0v-3c-2 0-3 1-4 1h-1-3c-3 1-7 1-10 1l-2-2c1 0 1 0 1-1h4 8c2 0 3 0 4-1s2-1 3-1z" class="s"></path><path d="M221 615s1-1 2-1h1l-1 2c0 1 1 1 1 2l1 1 2 1c0 1-1 1-2 1h-1c-2-1-2-2-3-2-1 1-1 1-2 1h-1c1-1 2-1 2-2v-1c0 1 1 1 1 1h0v-3z" class="P"></path><path d="M235 605s0-1 1-1h0l1 1v1c0 1 0 1 1 2h1v1h2v1l1 1c0 1 1 1 1 1 2 2 4 3 6 4 2 2 6 3 9 4h0v-1h-1l-2-2 5 1-1 1c1 1 2 1 3 1s2 0 3-1h1l2 1h1c1 1 2 1 3 2h0l2 1v2c-1 1-3 1-5 2 0 0 0 1-1 1h0-2l-14 1h-1l1 1h0-4-1-4v-1h-1-3c0-1-1-2-2-2h0v-1h-1l-2-1h-1l-1 1v-2h0c-1-1-1-1-2-1h-4 0l-2-2h1c1 0 2 0 2-1h1c1 0 2 0 4-1v-1h0-2c-1 1-3 0-4 0 0-1-1-2-1-3v-1c0-1 1-2 2-2 0-1 1-2 1-3-1-1-1-1-2-1v1c0 1 0 1-1 2 0 0-1 1-2 1h-2l-1-1c0-1-1-1-2-1 0-1 0-2-1-2l12-1v-2h1 1l2-1v3h0l1 1c0-1 0-2 1-3z" class="N"></path><path d="M237 622h1l-2 2-2 1h-1l-1 1v-2h0l1-1c1 0 2-1 4-1z" class="b"></path><path d="M232 624h4l-2 1h-1l-1 1v-2z" class="O"></path><path d="M230 605h1l2-1v3h-1l-1 3h-1l1-1c-1-1-2-1-2-2v-2h1z" class="X"></path><path d="M229 605l2 1v3c-1-1-2-1-2-2v-2z" class="Z"></path><path d="M247 625h1 0v-2c1 0 2 0 3 1h0l-1 2h0c-1 0-1 1-2 1-1 1-2 0-3 1h-1l1-1v-2h2z" class="C"></path><path d="M244 628h1c1-1 2 0 3-1 1 0 1-1 2-1 1 1 2 2 2 3h-1l1 1h0-4-1-4v-1h0l1-1z" class="G"></path><path d="M243 629h8l1 1h0-4-1-4v-1h0z" class="c"></path><path d="M255 617l5 1-1 1c1 1 2 1 3 1s2 0 3-1h1l2 1h1c1 1 2 1 3 2h0-3v-1h-1-5v1l-2-1c-1 1-1 1-2 1h-1v-1-1h0v-1h-1l-2-2z" class="b"></path><path d="M227 620h1c1 0 2 0 4-1v-1h0-2c-1 1-3 0-4 0 0-1-1-2-1-3v-1c0-1 1-2 2-2 0 1 0 1-1 2l1 1v1l2-1h0 1l1 1h2c1 1 1 1 1 2-1 2-2 2-3 3h-6c1 0 2 0 2-1z" class="F"></path><path d="M237 622c0-1 0-1 1-1s1 1 2 1h1v-1c2 0 2 1 2 2h0c2 0 1-1 3 0l1 1v1h-2v2l-1 1-1 1h0-1-3c0-1-1-2-2-2h0v-1h-1l-2-1 2-1 2-2h-1z" class="P"></path><path d="M238 622l1 2c0 1-1 1-2 2h-1l-2-1 2-1 2-2z" class="G"></path><path d="M235 605s0-1 1-1h0l1 1v1c0 1 0 1 1 2h1v1h2v1l1 1c0 1 1 1 1 1l-1 1v1 2c2 1 3 1 5 2 1 0 3 0 4 1v1h-1l-1-1c-2 0-4-1-6-1-3-1-4-3-6-4l-2-2c0-1 0-1-1-1 0-1-1-2-1-4l1 1c0-1 0-2 1-3z" class="O"></path><path d="M235 605l1 1c0 2 0 2-1 2h-1c0-1 0-2 1-3z" class="F"></path><path d="M242 611c0 1 1 1 1 1l-1 1v1h0l-1 1c-2 0-2-1-3-2 1-1 1-1 2-1s1-1 2-1z" class="Z"></path><path d="M242 611c0 1 1 1 1 1l-1 1v1h0c-1-1-1-2-2-2 1 0 1-1 2-1z" class="X"></path><path d="M238 613l-1-1s-1-1-1-2h-1 1c1-1 2-1 3-1h2v1l1 1c-1 0-1 1-2 1s-1 0-2 1z" class="C"></path><path d="M361 87c2-1 2-2 4-2 3 0 7 4 9 6l1 2h1c1 0 1 1 1 1l2 2h2c-1-2-2-3-3-4h4 113l3 9 1-1 1 2 1 4 1 3h-1v1h-1l-5 3-1 2c-2 0-3 2-4 3l-2-1c-2 2-4 3-7 5v-1l-1 1s-1 0-1 1h-1c0 1 0 2-1 2 0 1-2 1-2 2l-1 1s-1 1-1 2l-3 3-1 1c0 1-1 2-2 3 2 1 2 1 3 2l-3 3h1c1 1 1 2 1 3 0-1 0-2 1-3h1v1h0 1v1l-3 6c0 1 0 2-1 3v2l1-1h1c-1 2-1 3-1 5h0c1 1 1 2 1 3v-2l1 1 1-3 1-2 2-2h1l1-1c1-1 1-1 1-3h1l1-1s1 0 2-1l-2 4c0 1 1 2 1 2h1v1 3l-1 1v3l3-4 1 1-2 5 1 1 1-2c1 1 1 1 1 2l3-3 4-3 2-3 8-7 1 1h0c1 1 2 1 4 0h2v1 1l-2 1 1 1h0v1h0 2l1 1-1 1h1l1 1 2-2 1-1c0-1 0-2 1-3h1 0l2-1h1 1v1h2c0 1 0 2 1 2h0c1 0 2-1 2-2l1-3c0-1 1-1 1-1 1-1 0-1 0-2l4 2h0c-1 1-1 1-1 2-1 1 0 1 0 2 1 1 1 2 1 3h1c0 1 1 2 1 3h1v-1l1 2v2c1 0 1-1 1-1 1-1 1-1 1-2h1l-3 6c0 2-1 3-1 4h1c2 0 5-3 6-4-2 3-5 6-7 9-1 1-1 2-1 3h-2c-1 1-2 2-4 2v-1l1-1h0c1 0 1-1 2-1h1c2-1 1-3 1-5h0-1v1c-1 3-4 3-6 4-3 2-5 3-7 5-2 1-5 3-6 4-5 3-11 6-16 8l-1 1-6 3-2 2c-1 1-2 1-3 2-1 0-1 0-2 1h1 1 0v1h1c-1 1-2 1-3 1l-2-1c-2 1-4 2-6 2v2h0-4c-1 1-2 1-3 1 0 1-1 2-2 2l-1 2v3l-2 3s0 1-1 2h1c2 1 3 2 4 4l-1 1 1 1h-1l3 6c-1 0-1 0-1-1l-1 1c-1 1-1 2-2 3h0c-1 0-1 1-1 1h-2v3c0 1 0 1-1 1v1h-2v-1 2c-1 1-1 0-1 1 0 0 0 2-1 2-1 1-2 1-3 1-1-1-2 0-4 0l-1 1h0c-2 0-2 0-3-1v-1c-2 1-2 3-2 4-1 0-2-1-3-2h0c0-1-1-1-1-2-2-1-4 0-6 0v2c-2-1-3-3-3-4l-1-1-1-2-1 2c-1-1-1-1-1-2-3 0-4-1-7 1h0l-1-1c-1 1-3 1-4 1l-1 1-1-1v-1c0-1 2-1 3-3v-1s1-1 2-1 2-2 4-2c1 0 2-2 2-2v-1l1-2h-1c0-1 1-2 1-3-2 1-4 2-6 4-2 0-3 1-5 2h-4l-3 1-2-1c-2-1-3-2-5-3l-1 1c-1 0-2-1-4-1-1 0-2-1-3-1h1v-1c-2-3-5-5-7-8h0l1 1 1-1h0l-1-1c-1-2-2-3-4-4h0c-3-2-6-2-8-4h-4c-1-1-3-1-4-1h-3-4l1-1 2-1c3-1 5-1 7-1-1-1-3 0-4-1h-3c-1 0-1 1-2 1-2 0-5 1-7 1v-1h0l-1-1h-3l1-1v-2c-1 0-3 1-4 1l-1-1h0c0-1 0-1-1-2l-6 2-2 1-1-3 1-2h0c-1 1-2 1-3 1 1-1 2-2 4-3h-1c0-1-1-1-1-2h2c1-1 2-1 2-1h2c0-1 1-1 1-1 1-1 2-1 2-2 1 1 1 0 1 1h0c1-1 1-1 1-2l-5-1c-1 0-3-1-4-2h1v-4l1-1c1 0 2 1 3 1h2l2 1v-1l-1-1h0c-1-1-1-1-2-1l1-1 1 1h0v-1c1-2 0-4 2-6l1 1v-2h3v1-2l2 1v-16h0c0-6 1-12 0-18v-3l2 8h1v-1h1v-1c0-1-1-3-1-4v-3l-1-5-1-3h1c0-1 1 0 2 0 0-1-1-2-1-3s1-4 1-6c0-1 0-2 1-3 0-1 1-2 1-4v-1-3c0-1 1-2 1-3h2v-4l-1-2c1-1 1-1 1-2s0-1 1-1c0-1 0-1-1-2h0l-2 1c0-1-1-1-2-1 1-1 1-2 1-3z" class="z"></path><path d="M406 201c1 0 2-1 3 0h-1l1 1c-2 0-3-1-4-1h-1 2z" class="y"></path><path d="M435 200h1c1 1 2 2 2 3h0c-2-1-2-1-4-1v-1l1-1z" class="W"></path><path d="M425 176v-1c1 1 2 2 3 4v1l-1 1-2-5z" class="c"></path><path d="M448 213c1-1 2-1 3-1v2l1 1h-2c0-1-1-1-1-2h-1z" class="f"></path><path d="M460 209h1c0 3 0 5-1 7-2-1 0-3 0-5 0-1 0-1-1-1l1-1z" class="x"></path><path d="M377 171c2 1 3 3 4 5h0c-1-1-2-1-3-1v-1c-1-1-1-1-3-1l2-1v-1z" class="w"></path><path d="M373 168l4 3v1l-2 1-3-4 1-1z" class="n"></path><path d="M395 169l-1-2h0l1-1c0 1 0 1 1 2l1 1v2h1c0 1 1 2 1 2-2-1-3-2-4-4z" class="B"></path><path d="M398 150v-1c0 2 1 3 2 4h1l1 3h0c-1-1-2-1-3-2 0-1-2-2-3-2l1-1s0-1 1-1z" class="n"></path><path d="M354 203c3-1 7 0 10 0-1 0-1 1-2 1-2 0-5 1-7 1v-1h0l-1-1z" class="B"></path><path d="M425 176c-1-1-1-1-1-2 1 0 2-1 2-1l3 3v1l-1 2c-1-2-2-3-3-4v1z" class="a"></path><path d="M428 179l1-2c1 1 1 1 3 2l-1 1h-2v2h0l-1 1c-1 0-1 0-1-1v-1l1-1v-1z" class="k"></path><path d="M456 229l1 1h0c1 0 1-1 2-1 0 2 0 4 1 7-1-1-2-1-2-1-1-2-1-3-2-5v-1z" class="B"></path><path d="M411 159h1c1 0 2 2 2 3 1 1 3 2 3 4-2-1-2-3-4-4l-1 1 1 1v1c1 1 2 2 2 3v1h-1c0-2-2-3-2-5l-1-1c0-1 0-1 1-2l-1-2z" class="x"></path><path d="M411 205c1 0 1 0 2 1l-2 9-1 1h0l1-11z" class="H"></path><path d="M410 167c2 1 2 4 3 5 1 2 3 3 4 4l-1 1-1-1c-3-3-5-5-7-8 1 0 1 0 2-1h0z" class="I"></path><path d="M457 203h0c0-2 1-3 2-4v-3h1c0 1 0 2 1 3v-3c0 2 0 3 1 4l-1 1h0-1v2h-1l-1-1v1 1h-1v-1z" class="M"></path><path d="M432 230v-2l1 1h0 1v2c0 2 0 4-1 6h1c1-1 1-2 2-2-1 2-1 2-2 3h-1v-5c-1 0-1 0-2 1h0v-2c0-1 0-1 1-2h0z" class="a"></path><path d="M373 178c1 0 1 0 2-1 4 2 9 4 14 6h-3c-1 0-1 0-1 1l-3-1c-3-2-6-3-9-5z" class="j"></path><path d="M400 162c1 1 2 1 2 2l3 3c2 3 5 6 7 10-2-1-4-3-6-4 1-1 1-1 1-2-1-1-2-2-3-4l-3-3s0-1-1-1v-1zm39 57v-4c0 1 0 1 1 2 0 1 0 2 1 4 0 1 1 1 0 2h0c0 1 0 2-1 3 1 1 1 1 1 2h-2 0 0c-1-2 0-2 0-4v-5z" class="M"></path><path d="M439 219c0 2 0 5 1 6 0 1 0 2-1 3h0 0c-1-2 0-2 0-4v-5z" class="f"></path><path d="M462 190h0c0 1 1 2 1 3h0v-1c1 1 1 1 1 2v1c1 1 1 2 1 3v1h0c-1-1-1-2-1-3h-1v3c-1 1-1 1-1 3h-1v-1h0l1-1c-1-1-1-2-1-4s0-4 1-6z" class="a"></path><path d="M372 169l3 4c2 0 2 0 3 1v1 2h-2l-2-1-1-1v-3c-1-1-1-2-2-3h1z" class="Y"></path><path d="M374 176v-3l2 2h0v2l-2-1z" class="h"></path><path d="M425 182h-2c-1 0-5-4-5-4v-3c0-2-1-3-1-4 1 1 3 3 4 5 0 2 3 4 4 6z" class="J"></path><path d="M477 203l-2-2h1 2c1 0 2-1 4-2l9-3-2 2c-1 1-2 1-3 2-1 0-1 0-2 1h-4 0c-1 1-1 1-2 1l-1 1z" class="B"></path><path d="M449 187v-1c1 1 1 1 1 2v4 5 1l1 1v3c-1-1-2-5-2-6-1 0-1 2-2 2v-5l2-6z" class="b"></path><path d="M452 216c1 1 1 3 2 4h1c0-2-1-1 0-2h1c0 1-1 2 0 3 1 0 1 0 1 1h0c0 1 0 2 1 2v2c1 1 1 2 1 3-1 0-1 1-2 1h0l-1-1c0-2-1-5-1-7v-1l-1-1c-1 0-1-1-2-2v-2z" class="x"></path><path d="M428 217h1v2h1v-1 1s0 1-1 1l-1 1c0 1-1 1-2 1-1 1-3 2-5 3h-4c4-2 8-5 11-8z" class="J"></path><path d="M354 198h1c1 0 1 1 2 1 2 0 3 1 5 1l-5 1c-1 1-3 1-5 1v-2c-1 0-3 1-4 1l-1-1h2c1-1 3-1 4-2h1z" class="M"></path><path d="M354 198h1c1 0 1 1 2 1 2 0 3 1 5 1l-5 1c-1-1-2-1-3-1h-1v-1l1-1z" class="W"></path><path d="M458 189c2-2 2-6 5-8v1c-1 2-1 5-1 7v1c-1 2-1 4-1 6v3c-1-1-1-2-1-3l-1-5c0-1-1-1-1-2z" class="T"></path><path d="M389 170c1 1 2 2 4 3 1 1 4 2 6 4 0 2 4 2 4 5-2-1-5-3-7-4-1-1-2-3-3-3-1-1-2-1-3-1-1-1-1-3-1-4z" class="k"></path><path d="M456 189h1v2h1 0v-2c0 1 1 1 1 2l1 5h-1v3c-1 1-2 2-2 4h0l-1-2h-1l1-1v-1c1-2 1-4 1-6l-1-1v-3z" class="J"></path><path d="M477 203l1-1c1 0 1 0 2-1h0 4 1 1 0v1h1c-1 1-2 1-3 1l-2-1c-2 1-4 2-6 2v2h0-4c-1 1-2 1-3 1 0 1-1 2-2 2 2-4 6-4 10-6z" class="D"></path><path d="M486 201h0v1h1c-1 1-2 1-3 1l-2-1 3-1h1z" class="B"></path><path d="M417 182l10 8c2 2 5 5 6 7-5-3-9-7-14-11l-3-3 1-1z" class="u"></path><path d="M442 213v-1l1 1s0 1 1 1h1 0 1v3h1v-2h1 0c1 1 1 1 2 1 0 1 1 1 1 2h-1-1v-1h-1c-1 1-1 2-2 3v-2-2h-1l-1 1c0 1 0 1-1 2l-1-1-1 3c-1-2-1-3-1-4l1-1c1-1 1-2 1-3z" class="k"></path><path d="M442 213v-1l1 1s0 1 1 1h1 0l-1 1c-1 1-1 0-2 0v-2z" class="B"></path><path d="M440 217l1-1c1-1 1-2 1-3v2 3l-1 3c-1-2-1-3-1-4z" class="a"></path><path d="M436 235l2-3v2l-2 5v1h1c-1 1-2 2-2 3v1 2c-2-1-3-3-3-4l-1-1c1-1 1-2 1-3h1 1c1-1 1-1 2-3z" class="M"></path><path d="M461 216c1-2 2-5 4-7h0v1l1 1v3l-2 3s0 1-1 2h1c-1 1-2 2-2 4h-1c-1-2-1-3-1-5 0-1 1-1 1-2z" class="I"></path><path d="M461 216l1 1c0 1 0 1-1 1h-1c0-1 1-1 1-2z" class="B"></path><path d="M392 213l-1-6c1-1 3-1 5-2 3 0 10-1 13 0l-4 1h0-1c-3 0-8 1-10 3-1 1 0 2 0 3l-2 1z" class="L"></path><path d="M500 188h2 0c2-1 3-1 4-2 1 0 2 0 3-1l1-1h1 3c-5 3-11 6-16 8v-1h1 0 1l2-2-1 1c-4 1-7 1-11 2 3-2 6-3 10-4z" class="v"></path><path d="M386 174c4 2 7 5 10 7 4 3 9 6 13 9-5-2-9-5-14-8-1-1-3-1-5-2-1-2-3-3-5-5h0l1-1z" class="h"></path><path d="M402 149s1 0 1 1v-2l1-1 1-1c1 1 2 2 3 4h0-1l-1-1h0l1 2 1 2h0l-2-1-1 1v2h0c-1-1-2-2-4-2h0-1c-1-1-2-2-2-4h4z" class="p"></path><path d="M469 177l1 1c-1 2-1 4-2 6-3 4-1 8-1 13-1-3-2-5-3-8h0c-1 1-1 2-1 3v1h0c0-1-1-2-1-3h0v-1s0-2 1-3 2-4 4-5v1h0c0-2 1-3 2-5z" class="e"></path><path d="M442 218l1 1c1-1 1-1 1-2l1-1h1v2c-1 0-1 0-2 1l-1 1v2 2 3 2h0c-1 2-1 4-3 5v1c-1 0-1-1-2-1h0v-2l1-4h0 0 2c0-1 0-1-1-2 1-1 1-2 1-3h0c1-1 0-1 0-2l1-3z" class="Q"></path><path d="M439 228h2c0-1 0-1-1-2 1-1 1-2 1-3 0 2 1 4 1 6-1 1-2 1-2 2h0c0-1-1-2-1-3h0z" class="U"></path><path d="M439 228h0c0 1 1 2 1 3h0v3 1c-1 0-1-1-2-1h0v-2l1-4z" class="S"></path><path d="M425 152l1 2 1 1s2 1 2 2v3h1c0 2 1 3 1 5l1 1v2h-1-1 0v1c-1-2-2-4-3-7 0-1-1-2-1-3v-1c0-1 0-2-1-3h0l-1 2h-1l-2-3h2c0-1 1-2 2-2z" class="x"></path><path d="M367 162c2 2 4 3 6 5v1h0l-1 1h-1c1 1 1 2 2 3v3l-2-1h-1c-1 0 0-2-1-3v-1h-2v-2l-1-4v-1-1h1z" class="p"></path><path d="M371 174v-1c1-1 0-1 2-1v3l-2-1z" class="w"></path><path d="M367 162c2 2 4 3 6 5v1c-1-1-2-2-4-3h0l1 2c0 1-1 1-1 2l1 1v1h-1v-1h-2v-2l-1-4v-1-1h1z" class="a"></path><path d="M400 140c2 0 3 2 4 3l1 1h0l1 1c1 0 1 1 2 1 0 1 0 1 1 1l2 2h-3 0v1c1 0 1 1 1 1v1l-1-1h-1l-1-2h0l1 1h1 0c-1-2-2-3-3-4-2 0-3-1-6-1h-1v-1c-1 1-2 0-3 1h-1l-1-1h-2v-2h7 0c1 1 3 1 4 1v-1h-1c0-1 0-2-1-2z" class="M"></path><path d="M366 158c0 2 1 3 3 4v-1h2v1c1 1 3 2 4 3v1h2 0c0 1 1 1 1 2h2v1 1l6 4-1 1-12-8c-2-2-4-3-6-5l-3-3s1 0 2-1z" class="e"></path><path d="M492 168l1-1c1 0 1 1 1 1l1 1h0c1 0 1 0 1 1l-4 4v1c-2 1-3 3-4 4s-1 1-2 1c-1 1-1 2-2 3-2 1-3 3-6 4l8-10 2-2c2-2 3-4 4-7z" class="c"></path><path d="M495 169h0c1 0 1 0 1 1l-4 4h-1c0-1 0-1 1-2v-1h0c1-1 2-2 3-2z" class="R"></path><path d="M366 164l1 4v2 2 1 2h1c1 0 1 0 2 1 1 0 4 1 5 1-1 1-1 1-2 1l-7-2c0-1-1 0-1 0v1 1 1h-1v-2-1h-1s-1 0-1-1l-3-1h1 1l1-1v-1-2c1-1 0 0 0-1 1-1 2-1 3-2h1v-3z" class="h"></path><path d="M367 173l-1 1c-1-2-1-3-1-5v-1h2v2 2 1z" class="O"></path><path d="M445 214c0-1 0-2 1-3v1c0 1 0 2 1 3h0l1-2h1c0 1 1 1 1 2h2v1 2c1 1 1 2 2 2l1 1v1c0 2 1 5 1 7v1l-1 1h0c-1 0-1 0-1-1h0l-1-1c-1 0-1 0-1-1l-1-2h0l1-1v-5l-1-2h0c0-1-1-1-1-2-1 0-1 0-2-1h0-1v2h-1v-3h-1 0z" class="D"></path><path d="M452 220c1 2 1 3 1 5v2 1 1c-1 0-1 0-1-1l-1-2h0l1-1v-5z" class="R"></path><path d="M443 224h1 2l-1 3c0 1 0 1 1 2v1c0 1 1 2 0 3l1 1h-1-1s0-1-1-1h0l-1 4c-1 1-2 1-4 2h-1-2l2-5h0c1 0 1 1 2 1v-1c2-1 2-3 3-5h0v-2-3z" class="i"></path><path d="M444 224h2l-1 3h-1v-3z" class="T"></path><path d="M445 227c0 1 0 1 1 2v1h-2v-3h1z" class="g"></path><path d="M446 230c0 1 1 2 0 3l1 1h-1-1s0-1-1-1h0v-3h2z" class="H"></path><path d="M429 223h3l1-1 1 1c-1 1-1 3-2 5v2h0c-1 1-1 1-1 2v2h0c1-1 1-1 2-1v5h-1v-2h-1v1h-1v-4s-1 0-1-1h0l1-3-1-1-2 2h0l1-2h-1l-2 2v-1l1-2h-1c0-1 1-2 1-3l3-1z" class="D"></path><path d="M429 223h3l1-1 1 1c-1 1-1 3-2 5v2l-1-2c1 0 1-1 1-1h-1 0l1-2h0c-1 1-2 1-2 2h-1-2 0v-1c1 0 2-1 3-2l-1-1z" class="M"></path><path d="M429 223l1 1c-1 1-2 2-3 2v1h0 2 1 1c-1 1-1 1-1 2l-1-1-2 2h0l1-2h-1l-2 2v-1l1-2h-1c0-1 1-2 1-3l3-1z" class="k"></path><path d="M385 184c0-1 0-1 1-1h3c9 4 17 8 26 13 2 1 5 2 7 4-5-2-11-5-16-7l-21-9z" class="C"></path><path d="M362 206l2-1c3-1 5-1 7-1 4 0 7 1 10 2 3 2 5 2 7 5-2 0-2-1-4-2h0c-3-1-6-1-8-1h-4c-1-1-3-1-4-1h-3-4l1-1z" class="E"></path><path d="M376 208c2 0 5 0 8 1h0c2 1 2 2 4 2 1 1 1 3 2 4 2 5 6 10 11 12l-1 1c-1 0-2-1-4-1-1 0-2-1-3-1h1v-1c-2-3-5-5-7-8h0l1 1 1-1h0l-1-1c-1-2-2-3-4-4h0c-3-2-6-2-8-4z" class="J"></path><path d="M376 208c2 0 5 0 8 1h0c2 1 2 2 4 2 1 1 1 3 2 4l-2-1s-1-1-1-2h-1c0-1-1-2-1-2h-1v-1c-1 0-1 0-2 1l1 1h1v1h0c-3-2-6-2-8-4z" class="U"></path><path d="M433 178v-1c1 0 1-1 2-1v1l1 2h0v2l1 3-1 2h0-1c0-1 0-1-1-2h-1c-1 1-1 2-1 3l1 1-1 1c-1-1-1-1-1-2s0 0-1-1v1c0 1 0 2 1 2v1c-2-2-5-5-6-8-1-2-4-4-4-6l6 6c0 1 0 1 1 1l1-1h0v-2h2l1-1 1-1z" class="Y"></path><path d="M430 181l1-1h0l1 2v1h-1c0-1 0-1-1-2h0z" class="R"></path><path d="M434 184l1-4h0l1 1 1 3-1 2h0-1c0-1 0-1-1-2z" class="g"></path><path d="M433 178v-1c1 0 1-1 2-1v1l1 2h0c-2 0-2 2-4 3l-1-2h0l-1 1-1-1h2l1-1 1-1z" class="J"></path><path d="M437 184c0 1 0 3 1 3v-2c0 2 0 8 1 10 1 0 2 1 3 1 0-1 0-2 1-3h1c1 2 0 5 1 7h1v1c0 1-1 2-2 2 0-1 0-2-1-3v-1l-1-1c-1 1-1 1-2 1v-2h-1v-2c-1 0-1-1-2-1h0c-1 0-1 0-2-1h0c-1-1-1-1-1-2-1 0-1-1-2-1v1h-1v-1-1c-1 0-1-1-1-2v-1c1 1 1 0 1 1s0 1 1 2l1-1-1-1c0-1 0-2 1-3h1c1 1 1 1 1 2h1 0l1-2z" class="c"></path><path d="M434 191h0c0-1 0-2-1-2l1-1h1c1 1 0 3 1 4 1-2 0-3 1-4l1 1 1 6c-1 0-1-1-2-1h0c-1 0-1 0-2-1h0c-1-1-1-1-1-2z" class="S"></path><path d="M413 212c0-2 1-4 2-6 4 1 11 4 14 8 0 1 0 2 1 3v1 1h-1v-2h-1c-1-3-9-2-12-4 0-1-3-1-3-1z" class="F"></path><path d="M439 181l1 1v3h1v-2h1v5 1h0c1 0 1-2 2-3l1-3 1-2c1 1 1 3 1 5v1 7l-1 2v4h-1c-1-2 0-5-1-7h-1c-1 1-1 2-1 3-1 0-2-1-3-1-1-2-1-8-1-10 1-2 1-3 1-4z" class="q"></path><path d="M442 189v3h-1l-1-1c-1-1-1-2-1-3 1-1 1-1 2-1l1 1v1z" class="F"></path><path d="M446 196h-1v-6-1c-1-1-1-1 0-2l1-1 1 1v7l-1 2z" class="b"></path><path d="M409 205v5 3h-5c-3-1-7-1-10-1 0-1-1-2 0-3 2-2 7-3 10-3h1 0l4-1z" class="N"></path><path d="M409 205v5c-1 0-2-1-3-2h0c0-1-1-1-1-1l-1-1h1 0l4-1z" class="P"></path><path d="M365 177v-1s1-1 1 0l7 2c3 2 6 3 9 5h0c-1 0-2 0-2-1-2 0-3-1-4-1 1 1 2 1 3 2v2c1 0 2 1 3 2 0 0 1 0 1 1h0 0-1c-2-1-3-1-5-2-1-1-3-2-5-3-2 0-3-1-5-1h-1s-1 0-1-1c-1-1-1-1-3-1l2-1h1v-1-1z" class="I"></path><path d="M365 177h3v1c2 0 4 2 5 3h1c1 0 1 1 1 1v1c-1-1-2-1-2-1-1-1-2-1-2-1h-1c1 1 2 1 2 2-2 0-3-1-5-1h-1s-1 0-1-1c-1-1-1-1-3-1l2-1h1v-1-1z" class="B"></path><path d="M365 178h0c1 1 1 1 2 1l1 1c0 1-1 1-1 2h-1s-1 0-1-1c-1-1-1-1-3-1l2-1h1v-1z" class="r"></path><path d="M464 219c2 1 3 2 4 4l-1 1 1 1h-1l3 6c-1 0-1 0-1-1l-1 1c-1 1-1 2-2 3h0c-1 0-1 1-1 1h-2v3c0 1 0 1-1 1v-2c1-2 0-4 0-6-1-2-1-2-1-4v-4h1c0-2 1-3 2-4z" class="M"></path><path d="M463 235v-3h1c1 1 1 2 2 2-1 0-1 1-1 1h-2z" class="J"></path><path d="M464 219c2 1 3 2 4 4l-1 1s-1-1-2-1v1c-1 1-1 2-1 2 0 2 0 4-1 6v-1-2c-1-2-1-4-1-6s1-3 2-4z" class="W"></path><path d="M472 158c1 1 2 2 2 3l-1 1 1 2v3c-1 2-1 3-1 4 1 1 1 1 0 2s-2 3-3 5h0l-1-1c-1 2-2 3-2 5h0v-1c-2 1-3 4-4 5s-1 3-1 3c0-2 0-5 1-7 2-1 4-6 5-8 2-4 2-9 3-13l1-3z" class="x"></path><path d="M469 177h0s0-1 1-1c0 0 1-1 1-2l-1-1s1-1 1-2c1-2 1-3 2-5v-4h0l1 2v3c-1 2-1 3-1 4 1 1 1 1 0 2s-2 3-3 5h0l-1-1z" class="D"></path><path d="M446 218v2c1-1 1-2 2-3h1v1h1 1 0l1 2v5l-1 1h0l1 2c-1 0-1 0-2 1 1 1 1 1 1 2 1 0 1 0 0 1v1c-1-1-2-1-2-1l-1 1c0 1 0 1-1 2v-1l-1-1c1-1 0-2 0-3v-1c-1-1-1-1-1-2l1-3h-2-1v-2-2l1-1c1-1 1-1 2-1z" class="i"></path><path d="M450 218h1 0v5h0-1v-5z" class="g"></path><path d="M443 222l1-1c2 0 2 0 3 1v1l-1 1h-2-1v-2z" class="H"></path><path d="M449 225l1-1v3h1v-1l1 2c-1 0-1 0-2 1 1 1 1 1 1 2 1 0 1 0 0 1v1c-1-1-2-1-2-1l-1 1c0 1 0 1-1 2v-1l-1-1c1-1 0-2 0-3v-1-2-1h1c1 0 1-1 2-1z" class="n"></path><path d="M451 232c0-1-1-1-2-1l-1-1v-3h0c0 1 0 2 1 2h1c1 1 1 1 1 2 1 0 1 0 0 1z" class="p"></path><path d="M449 225l1-1v3h1v-1l1 2c-1 0-1 0-2 1h-1c-1 0-1-1-1-2l1-2z" class="r"></path><path d="M384 154h3l3 2 1-1c2 1 3 3 4 4v1c-1 0-2 0-2-1l-1 1v1l14 12c2 1 4 3 6 4l5 5-1 1-17-15c-5-5-12-8-17-12v-1c1 0 1 0 2-1z" class="G"></path><path d="M392 161c-2 0-5-3-6-4 0-1-1-2-1-3h2l3 2 1-1c2 1 3 3 4 4v1c-1 0-2 0-2-1l-1 1v1z" class="R"></path><path d="M383 146h-1l1-1 3 3h3l4 4c1 1 1 3 2 4 2 2 3 4 5 6v1c1 0 1 1 1 1l3 3c1 2 2 3 3 4 0 1 0 1-1 2l-14-12v-1l1-1c0 1 1 1 2 1v-1c-1-1-2-3-4-4l-4-4-1-2-3-3z" class="a"></path><path d="M389 148l4 4c1 1 1 3 2 4 2 2 3 4 5 6v1l-14-15h3z" class="H"></path><path d="M387 142c1 1 3 0 4 0v2h2l1 1h1c1-1 2 0 3-1v1h1c3 0 4 1 6 1l-1 1-1 1v2c0-1-1-1-1-1h-4v1c-1 0-1 1-1 1l-1 1c1 0 3 1 3 2v2c-2-1-2-2-4-2v2c-1-1-1-3-2-4l-4-4c-1-2-2-3-4-4 1-1 0-1 2-2z" class="R"></path><path d="M396 146c1 1 2 1 3 1h0c1 0 2 1 3 2h-4v1c-1-1-1-2-2-2h0v-2z" class="K"></path><path d="M393 146h3v2h0c1 0 1 1 2 2-1 0-1 1-1 1l-1 1-3-3c1 0 1-1 1-2h1l-2-1z" class="d"></path><path d="M393 149c-2-1-4-4-6-6l6 3 2 1h-1c0 1 0 2-1 2z" class="T"></path><path d="M425 230l2-2h1l-1 2h0l2-2 1 1-1 3h0c0 1 1 1 1 1v4h1v-1h1v2c0 1 0 2-1 3l-1-2-1 2c-1-1-1-1-1-2-3 0-4-1-7 1h0l-1-1c-1 1-3 1-4 1l-1 1-1-1v-1c0-1 2-1 3-3v-1s1-1 2-1 2-2 4-2c1 0 2-2 2-2z" class="K"></path><path d="M419 234c1 0 2-2 4-2h0c-1 1-1 1-1 2l-3 3v-3zm8-4h0l2-2 1 1-1 3-1 1h-1c-1 0-2 0-2 1l-1-1c1-1 2-1 2-2s0 0 1-1z" class="o"></path><path d="M417 236v-1s1-1 2-1v3l-3 3-1 1-1-1v-1c0-1 2-1 3-3z" class="R"></path><path d="M428 233l1-1h0c0 1 1 1 1 1v4h1v-1h1v2c0 1 0 2-1 3l-1-2h0c-1 0-1-1-1-2-1 0-2 0-3 1v-1l2-4z" class="J"></path><path d="M470 186c0-2 1-5 3-7 1-2 2-3 4-5 2-3 3-7 6-10l1 1 1-2c1 1 1 1 1 2s-1 2-1 2c0 1 0 1 1 2l-2 2-1 2-4 4c-1 2-2 4-4 6l-3 6-2 3c-1-2-1-4 0-6z" class="E"></path><path d="M474 182h1 0v1l-3 6-1-1c0-2 1-4 3-6z" class="q"></path><path d="M485 163c1 1 1 1 1 2s-1 2-1 2c0 1 0 1 1 2l-2 2-1 2-4 4c-1 2-2 4-4 6v-1h0-1v-1h0c0-1 1-3 2-4l7-10 1-2 1-2z" class="D"></path><path d="M484 168l1-1c0 1 0 1 1 2l-2 2-1 2-1-2 2-3z" class="L"></path><path d="M485 163c1 1 1 1 1 2s-1 2-1 2l-1 1v-1l-1 1v-1l1-2 1-2z" class="S"></path><path d="M482 171l1 2-4 4c-1 2-2 4-4 6v-1h0-1v-1l8-10z" class="H"></path><path d="M389 163c2 2 4 5 6 6 1 2 2 3 4 4v1c2 1 4 4 6 6 3 3 7 7 11 10 2 2 6 4 9 6 1 1 3 2 4 3h-1c-7-4-14-9-21-15h-1c-1 0-2-1-3-2 0-3-4-3-4-5-2-2-5-3-6-4-2-1-3-2-4-3-3 0-5-1-7-1 0-1 1-2 2-3-1-1-1-2-2-3l2 1c2 0 3 0 5-1z" class="m"></path><path d="M399 177c3 2 6 4 8 7h-1c-1 0-2-1-3-2 0-3-4-3-4-5z" class="W"></path><path d="M389 163c2 2 4 5 6 6 1 2 2 3 4 4v1 1c-1 0-1 0-1-1-3 0-3-2-5-3h-1c-2-1-3-2-4-3v-1h-3c0-1-1-1-1-1-1-1-1-2-2-3l2 1c2 0 3 0 5-1z" class="e"></path><path d="M382 163l2 1c2 1 3 1 4 2l1 1c2 1 2 0 3 2 2 2 4 3 6 5-3 0-3-2-5-3h-1c-2-1-3-2-4-3v-1h-3c0-1-1-1-1-1-1-1-1-2-2-3z" class="E"></path><defs><linearGradient id="x" x1="388.439" y1="195.517" x2="377.5" y2="206.171" xlink:href="#B"><stop offset="0" stop-color="#0d0c0d"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#x)" d="M362 197c3 0 5 1 7 1l2-1 19 2c2 1 3 0 5 0v1h0v1h0 3-1v2c-2 0-3 0-4 1h0-5c-4 0-8-1-12-2l-14-2c-2 0-3-1-5-1l-1-1 1-1s1 1 2 1l1-1h2z"></path><path d="M362 197c3 0 5 1 7 1l5 1-1 1c-2 0-5-1-7-1h-2c-1 0-2 0-3-1h-2l1-1h2z" class="I"></path><path d="M371 197l19 2c2 1 3 0 5 0v1h0c-7 2-14-1-21-1h0l-5-1 2-1z" class="b"></path><path d="M394 212c3 0 7 0 10 1h5c0 4 0 9-1 13l-5-1c-6-2-8-7-11-12l2-1zm19 0s3 0 3 1c3 2 11 1 12 4-3 3-7 6-11 8l-3 1h-5c1-5 2-9 4-14z" class="t"></path><path d="M452 174h1v1h1l1 1 1 2h1v1l-1 2c0 2-1 3-1 4h1v4 3l1 1c0 2 0 4-1 6v1l-1 1h1c-1 0-2 0-3 1v1c-1-2-1-3-2-5v1l-1-1v-1-5-4c0-1 0-1-1-2v1l1-6c0-1 1-4 1-5 1-1 1-1 1-2z" class="o"></path><path d="M453 198v-3c1 1 2 4 2 6h1c-1 0-2 0-3 1v-4z" class="I"></path><path d="M455 185h1v4 3 3-1c-1-2-1-4-1-6v-3z" class="l"></path><path d="M450 188v3c1 0 1 0 1-1 0 0 0-1 1-1h0c1 2 0 6 1 9v4 1c-1-2-1-3-2-5v1l-1-1v-1-5-4z" class="T"></path><path d="M452 174h1v1h1l1 1 1 2h1v1l-1 2c0 2-1 3-1 4v3l-1 1h-1c-1-2-1-4-1-6h0c0-1 0-1-1-1v-1h-1c0-1 1-4 1-5 1-1 1-1 1-2z" class="J"></path><path d="M454 175l1 1 1 2c-1 0-1 0-1 1 0 2-1 4-2 6h0l1-10z" class="f"></path><path d="M468 155l1-1h1c-1 2-1 3-1 5h0c1 1 1 2 1 3v-2l1 1c-1 4-1 9-3 13-1 2-3 7-5 8v-1c-3 2-3 6-5 8v2h0-1v-2h-1v-4h-1c0-1 1-2 1-4l1-2v-1l1-1c1-1 2-1 2-2h1l3-4c0-1 1-1 1-2v-1c0-1 0-2 1-3v-3l2-7z" class="W"></path><path d="M457 179h1 1l-1 5v-1l-1-1-1-1 1-2z" class="n"></path><path d="M458 177c1-1 2-1 2-2h1l-2 4h-1-1v-1l1-1z" class="p"></path><path d="M470 160l1 1c-1 4-1 9-3 13-1 2-3 7-5 8v-1c-3 2-3 6-5 8v2h0-1v-2h-1v-4h-1c0-1 1-2 1-4l1 1 1 1v1h1c1-4 5-8 7-12 0-1 1-2 1-2 1-1 1-1 1-2 1-2 1-4 2-6v-2z" class="U"></path><path d="M456 181l1 1 1 1c-1 2-1 4-1 6h-1v-4h-1c0-1 1-2 1-4z" class="g"></path><path d="M479 150l1-1s1 0 2-1l-2 4c0 1 1 2 1 2h1v1 3l-1 1v3l3-4 1 1-2 5c-3 3-4 7-6 10-2 2-3 3-4 5-2 2-3 5-3 7v-2-2l1-1h0v-1-1c-1 2-2 3-3 4v1c1-2 1-4 2-6h0c1-2 2-4 3-5s1-1 0-2c0-1 0-2 1-4v-3l-1-2 1-1c0-1-1-2-2-3l1-2 2-2h1l1-1c1-1 1-1 1-3h1z" class="a"></path><path d="M478 157h0c0-1 0-1 1-2 0-1 0-2 1-3 0 1 1 2 1 2h1v1 3l-1 1v3c-1 1-1 2-2 2v-1l-1-1h0v-1-4z" class="w"></path><path d="M481 159c0-1-1-1-1-1 0-1 0-1 1-2 1 0 0 1 1 2l-1 1z" class="d"></path><path d="M479 150l1-1s1 0 2-1l-2 4c-1 1-1 2-1 3-1 1-1 1-1 2h0v-1c-1 0-1 2-2 3l-2 5-1-2 1-1c0-1-1-2-2-3l1-2 2-2h1l1-1c1-1 1-1 1-3h1z" class="Y"></path><path d="M475 154h1c-1 1-1 2 0 3h-1l-1 1c0 1 1 1 0 2v1c0-1-1-2-2-3l1-2 2-2z" class="I"></path><path d="M355 136v-3l2 8 4 14v1h1c0 1 1 3 2 3l3 3h-1v1 1 3h-1c-1 1-2 1-3 2 0 1 1 0 0 1v2 1l-1 1h-1l-2-2c1 0 2 1 3 0l-1-1c-1 1-2 0-4 0-1 0-1 0-1-1v-16h0c0-6 1-12 0-18z" class="f"></path><path d="M359 165c0 2 1 4 1 6-1 1-2 0-4 0h1v-1c1 0 1-1 1-2l1-1v-2z" class="I"></path><path d="M359 159c1 1 1 2 1 3 0 2 1 4 1 6 1 0 1 0 1 1s1 0 0 1v2 1l-1 1h-1l-2-2c1 0 2 1 3 0l-1-1c0-2-1-4-1-6v-6z" class="l"></path><path d="M359 159c-1 0 0-2-1-3 0 0 0-1-1-1h1v-1c1 1 2 2 3 2h0 1c0 1 1 3 2 3l3 3h-1l-2-1c-1 0-1-1-3-1v1 1h-1c0-1 0-2-1-3z" class="M"></path><path d="M361 161v-1c2 0 2 1 3 1l2 1v1 1 3h-1c-1 1-2 1-3 2 0-1 0-1-1-1 0-2-1-4-1-6h1v-1z" class="K"></path><path d="M364 166v-1c0-1 1-1 1-2h0 1v1 3h-1l-1-1z" class="G"></path><path d="M361 161v-1c2 0 2 1 3 1l-1 1c0 1 1 1-1 2v2h0v-1c0-2-1-3-1-4z" class="h"></path><path d="M361 161c0 1 1 2 1 4v1h0 2l1 1c-1 1-2 1-3 2 0-1 0-1-1-1 0-2-1-4-1-6h1v-1z" class="E"></path><path d="M450 229c1-1 1-1 2-1 0 1 0 1 1 1l1 1h0c0 1 0 1 1 1h0l1-1c1 2 1 3 2 5 0 0 1 0 2 1-1 1-1 2 0 3v2c-1 1-1 0-1 1 0 0 0 2-1 2-1 1-2 1-3 1-1-1-2 0-4 0l-1 1h0c-2 0-2 0-3-1v-1c-2 1-2 3-2 4-1 0-2-1-3-2h0c0-1-1-1-1-2-2-1-4 0-6 0v-1c0-1 1-2 2-3h-1v-1h2 1c2-1 3-1 4-2l1-4h0c1 0 1 1 1 1h1 1v1c1-1 1-1 1-2l1-1s1 0 2 1v-1c1-1 1-1 0-1 0-1 0-1-1-2z" class="h"></path><path d="M451 233v3h1c0-2 0-2 1-3h0c1 1 1 2 0 3v2c1 0 1 0 1-1l2 2v1h-3c0-1 0-2-1-3-1 0-1 0-1-1v-3z" class="d"></path><path d="M454 230h0c0 1 0 1 1 1h0l1-1c1 2 1 3 2 5-1 0-1 1-1 1h-2c0-1 0-3-1-3l-1-1s1-1 1-2z" class="c"></path><path d="M458 235s1 0 2 1c-1 1-1 2 0 3v2c-1 1-1 0-1 1 0 0 0 2-1 2 0-2 0-3-1-5h0v-3s0-1 1-1z" class="S"></path><path d="M448 233l1-1s1 0 2 1h0v3c0 1 0 1 1 1 1 1 1 2 1 3h-8c-2 0-4-1-6-1 2-1 3-1 4-2l1-4h0c1 0 1 1 1 1h1 1v1c1-1 1-1 1-2z" class="C"></path><path d="M444 237h1v1c-1 1-1 1-1 0v-1z" class="F"></path><path d="M452 237c1 1 1 2 1 3h-8c2 0 5 0 7-1-1 0-1-1-2-2h2z" class="G"></path><path d="M448 233l1-1s1 0 2 1h0v3c0 1 0 1 1 1h-2l-1 1v-1-4h-1z" class="u"></path><path d="M435 243c1-1 2-2 3-2 1-1 3-1 4-1l1 1h4 6 3v2h-1l-1-1v1h1v1c-1 0-3 0-4 1l-1 1h0c-2 0-2 0-3-1v-1c-2 1-2 3-2 4-1 0-2-1-3-2h0c0-1-1-1-1-2-2-1-4 0-6 0v-1z" class="G"></path><path d="M442 246l2-1v-1s1-1 2-1l1 1c-2 1-2 3-2 4-1 0-2-1-3-2h0z" class="g"></path><path d="M495 156l8-7 1 1h0c1 1 2 1 4 0h2v1 1l-2 1 1 1h0v1h0 2l1 1-1 1h1l1 1-2 1-1 1h-1l-2 2c-2 0-4 1-6 3l-2 1-4 3h0l-1-1s0-1-1-1l-1 1c-1 3-2 5-4 7 0-3 1-5 2-7 0-1 1-2 1-3h0c-2 2-4 5-7 6h0l2-2c-1-1-1-1-1-2 0 0 1-1 1-2l3-3 4-3 2-3z" class="M"></path><path d="M492 164l2 2c1 0 1-1 2-2l1 1 2 1-4 3h0l-1-1s0-1-1-1l-1 1v-1h-1 0c1-1 1-1 1-3h0z" class="k"></path><path d="M495 156l8-7 1 1h0c-1 2-7 6-7 8l-11 11c-1-1-1-1-1-2 0 0 1-1 1-2l3-3 4-3 2-3z" class="C"></path><path d="M508 150h2v1 1l-2 1-10 6h0c-1 0-2 1-3 1l2-2h0c0-2 6-6 7-8 1 1 2 1 4 0z" class="f"></path><path d="M498 159l10-6 1 1h0v1h0 2l1 1-1 1h1l1 1-2 1-1 1h-1l-2 2c-2 0-4 1-6 3l-2 1-2-1-1-1c-1 1-1 2-2 2l-2-2c1-1 2-2 3-2l3-3z" class="I"></path><path d="M497 165c1-1 3-2 4-2h2l6-3-2 2c-2 0-4 1-6 3l-2 1-2-1z" class="S"></path><path d="M509 155h2l1 1-1 1h1l1 1-2 1-1 1v-1h-1-3c-1 1-3 3-5 2 0-1 3-2 3-2 2-1 3-2 5-4z" class="U"></path><path d="M512 157l1 1-2 1-1 1v-1-1l2-1z" class="B"></path><path d="M498 159l10-6 1 1h0c-2 1-4 1-5 2a30.44 30.44 0 0 0-8 8c-1 1-1 2-2 2l-2-2c1-1 2-2 3-2l3-3z" class="c"></path><path d="M359 139c1 1 1 2 2 3l2 1 1-2 1 1h1v1 2 1h2c2 0 2 1 3 2v1c1 0 3-1 4-2 2 0 4 0 6-1 1 0 1-1 1-1l-2-2v-1l3 3h0l-1 1h1l3 3 1 2 4 4-1 1-3-2h-3c-1 1-1 1-2 1v1c5 4 12 7 17 12-2 0-3-1-4-2l-1 1h0l1 2c-2-1-4-4-6-6-2 1-3 1-5 1l-2-1c1 1 1 2 2 3-1 1-2 2-2 3h-2v-1h-2c0-1-1-1-1-2h0-2v-1c-1-1-3-2-4-3v-1h-2v1c-2-1-3-2-3-4-1 1-2 1-2 1-1 0-2-2-2-3h-1v-1l-4-14h1v-1h1v-1z" class="S"></path><path d="M362 145c1 1 1 2 2 3l1 2c0 1-1 1-1 2-1-2-2-4-2-7z" class="K"></path><path d="M367 157l1-2c0 1 0 1 1 2 0 0 0 1 1 1l1 1c-1 0-2 1-3 1 0-1-1-2-1-3z" class="i"></path><path d="M364 152h2 0l2 2v1l-1 2c-1-1-1-2-2-3 0-1 0-1-1-2z" class="K"></path><path d="M362 156c0-1 1-1 2-2l2 4c-1 1-2 1-2 1-1 0-2-2-2-3z" class="F"></path><path d="M369 161c2-1 5-3 7-3 0 0 0 1 1 1-1 1-2 1-3 1v1l-3 1v-1h-2z" class="u"></path><path d="M365 146l1-1v1c0 1 0 3 1 4 0 1 0 1-1 2h0-2c0-1 1-1 1-2l-1-2c1-1 1-1 1-2z" class="m"></path><path d="M364 141l1 1h1v1 2l-1 1c0 1 0 1-1 2-1-1-1-2-2-3s-1-2-1-3l2 1 1-2z" class="i"></path><path d="M364 141l1 1h1v1 2l-1 1h-1l-1-3 1-2z" class="G"></path><path d="M358 140h1c0 3 1 5 2 8 1 1 2 3 2 4 0 0 1 1 1 2-1 1-2 1-2 2h-1v-1l-4-14h1v-1z" class="O"></path><path d="M361 155v-3h2s1 1 1 2c-1 1-2 1-2 2h-1v-1z" class="L"></path><path d="M376 158c2-1 3-1 5-1 3 1 5 2 6 5 1 0 1 1 2 1-2 1-3 1-5 1l-2-1v-1h-2l-1-2h-1c0 1-1 1-2 1s-1 0-2-1c1 0 2 0 3-1-1 0-1-1-1-1z" class="O"></path><path d="M382 159c1 0 2 0 3 1v1c1 1 1 1 2 1s1 1 2 1c-2 1-3 1-5 1l-2-1v-1h2v-1l-2-2z" class="H"></path><path d="M377 159c1 1 2 0 2 0v-1c1 1 2 1 3 1h0l2 2v1h-2-2l-1-2h-1c0 1-1 1-2 1s-1 0-2-1c1 0 2 0 3-1z" class="L"></path><path d="M374 160c1 1 1 1 2 1s2 0 2-1h1l1 2h2v1c1 1 1 2 2 3-1 1-2 2-2 3h-2v-1h-2c0-1-1-1-1-2h0-2v-1c-1-1-3-2-4-3l3-1v-1z" class="O"></path><path d="M374 161c0 1 0 3 1 4-1-1-3-2-4-3l3-1z" class="l"></path><path d="M380 162h2v1c1 1 1 2 2 3-1 1-2 2-2 3h-2v-1h1l-1-1c1-2 0-4 0-5z" class="u"></path><path d="M380 142l3 3h0l-1 1h1l3 3 1 2 4 4-1 1-3-2h-3c-1 1-1 1-2 1v1h-2l-3 1-4 1-2 1-1-1c-1 0-1-1-1-1-1-1-1-1-1-2v-1l-2-2c1-1 1-1 1-2-1-1-1-3-1-4h2c2 0 2 1 3 2v1c1 0 3-1 4-2 2 0 4 0 6-1 1 0 1-1 1-1l-2-2v-1z" class="g"></path><path d="M376 156c1-1 1-1 2-1h2l1 1h-1l-3 1-1-1z" class="O"></path><path d="M376 156l1 1-4 1c0-1-1-1 0-2h2 0 1z" class="G"></path><path d="M380 142l3 3h0l-1 1h1v1h-3s-1 0-1 1c-2 0-3 0-4 1v1h1l1 1h0c0 1 1 1 1 2l-2 1h0 0c-2 0-3 1-4 1h0c-1 0-1-1-1-1l1-1-1-2h1v-1c-2 0-2 1-3 1l-1-1h-1c-1-1-1-3-1-4h2c2 0 2 1 3 2v1c1 0 3-1 4-2 2 0 4 0 6-1 1 0 1-1 1-1l-2-2v-1z" class="O"></path><path d="M383 146l3 3 1 2 4 4-1 1-3-2h-3c-1 1-1 1-2 1-2-2-3-3-5-4h0l-1-1h-1v-1c1-1 2-1 4-1 0-1 1-1 1-1h3v-1z" class="L"></path><path d="M384 154h-1c-1-1-1-1-1-3h3 2l4 4-1 1-3-2h-3z" class="F"></path><path d="M383 146l3 3c-2 1-3 1-4 0l-2 2c-2-1-2-1-4-1h-1v-1c1-1 2-1 4-1 0-1 1-1 1-1h3v-1z" class="X"></path><path d="M436 133l1-2h0c0 1 1 1 1 1 1 2 0 2 1 4v1l-1 1v1c-1 2-1 4-1 7h1l1-2h0c1 2 0 5 0 7v1c0 1 0 2 1 2v3h-1v1c0 1 0 1 1 2h0v-4h1c0 2 1 3 1 4l1 1v1h1v7c0 2 0 2 1 3v1 1h0v3h1c0 1 0 2-1 3l1 1-1 2-1 3c-1 1-1 3-2 3h0v-1-5h-1v2h-1v-3l-1-1c0 1 0 2-1 4v2c-1 0-1-2-1-3l-1-3v-2h0l-1-2v-1c-1 0-1 1-2 1v1l-1-3c-1-1-1-4-2-6v-1h0 1 1v-2l-1-1c0-2-1-3-1-5h-1v-3c0-1-2-2-2-2l-1-1-1-2c0-2-1-4-2-5v-1c1-1 2-2 2-3l1-2c0 1 1 1 1 2h1l1 3h2c0-1 0-3-1-4h1 0c1 0 2-1 3-1h0v-3l1-1 1 1c-1-2-1-3-1-5h1z" class="f"></path><path d="M435 156v4c-1 0-1 1-1 1l-2-4h1c1 0 1 0 2-1z" class="E"></path><path d="M436 156l1 1v3h-1c-1 1-1 2-1 3l-1-1v-1s0-1 1-1v-3l1-1z" class="D"></path><path d="M440 173c1 3 0 6 0 9h0l-1-1v-1c-1-1 0-5 1-7z" class="J"></path><path d="M435 171c0 2 0 4 1 5l-1 1v-1c-1 0-1 1-2 1v1l-1-3c1-1 1-1 2-1 1-2 0-2 1-3z" class="S"></path><path d="M435 163c0 2 0 4 2 6 1 1 0 4 0 5v1c-1-1-1-3-1-4s-1-2-1-3l-1-6 1 1z" class="a"></path><path d="M443 162h1v7c0 2 0 2 1 3v1 1h0l-1 1-1-1v-3-9z" class="U"></path><path d="M443 171v3l1 1 1-1v3h1c0 1 0 2-1 3l1 1-1 2c-1-1-1-4-2-6h-1l1-6z" class="S"></path><path d="M430 168c1 0 1 1 2 1h3v2c-1 1 0 1-1 3-1 0-1 0-2 1-1-1-1-4-2-6v-1z" class="M"></path><path d="M436 181l1 1h1l1-13v-1c1 1 1 1 2 1-1 2-1 2-1 4h0c-1 2-2 6-1 7v1c0 1 0 2-1 4v2c-1 0-1-2-1-3l-1-3z" class="S"></path><path d="M430 152c0-1-1-2-1-3h1l1 1 2-1v2c0 2 1 4 2 5-1 1-1 1-2 1h-1l-2-5z" class="d"></path><path d="M442 177h1c1 2 1 5 2 6l-1 3c-1 1-1 3-2 3h0v-1-5h-1v-2c1-1 1-2 1-4h0z" class="U"></path><path d="M442 177h1c1 2 1 5 2 6l-1 3c-1-1-1-3-1-4s-1-1-1-1v-4h0z" class="c"></path><path d="M437 154c1 0 1 1 1 1 1-1 1-2 1-3 0 1 0 2 1 2v3h-1v1c0 1 0 1 1 2h0v-4h1c0 2 0 3-1 5v-1h1c1 2 0 4 0 5h-1 0-1c-1 0-2-2-2-3v-1-1-3h0v-3z" class="I"></path><path d="M437 154c1 0 1 1 1 1 1-1 1-2 1-3 0 1 0 2 1 2v3h-1v-1h0c-1 1-1 4-2 5v-1-3h0v-3z" class="Y"></path><path d="M426 141c0 1 1 1 1 2h1l1 3h2v1l1-1c1 1 1 2 1 3l-2 1-1-1h-1c0 1 1 2 1 3s0 0-1 1c0 1-1 1-1 1-1 0-1 1-1 1l-1-1-1-2c0-2-1-4-2-5v-1c1-1 2-2 2-3l1-2z" class="J"></path><path d="M431 146v1 1h-1l-1-2h2z" class="f"></path><path d="M425 143c2 1 2 3 3 4 0 2 0 4-1 5v2h-1l-1-2c0-2-1-4-2-5v-1c1-1 2-2 2-3z" class="o"></path><path d="M436 133l1-2h0c0 1 1 1 1 1 1 2 0 2 1 4v1l-1 1v1c-1 2-1 4-1 7h1l1-2h0c1 2 0 5 0 7v1c0 1 0 2-1 3 0 0 0-1-1-1v3h0l-1-1-1 1v3-4c-1-1-2-3-2-5v-2c0-1 0-2-1-3l-1 1v-1c0-1 0-3-1-4h1 0c1 0 2-1 3-1h0v-3l1-1 1 1c-1-2-1-3-1-5h1z" class="J"></path><path d="M435 133c1 1 2 2 3 2v1h-1c0 1 0 1-1 2h0c-1-2-1-3-1-5zm-2 18h0c1 0 1 0 2-1v4c1 1 1 0 1 2l-1 1v3-4c-1-1-2-3-2-5z" class="Y"></path><path d="M437 154v-1c-1-2-1-4-1-5h2l1-1v1 3 1c0 1 0 2-1 3 0 0 0-1-1-1z" class="h"></path><path d="M434 138l1-1 1 1c0 2-1 6 0 7v5l-1-1v-3h-1v4h0c-1 0-1-1-1-1 0-1 0-2-1-3l-1 1v-1c0-1 0-3-1-4h1 0c1 0 2-1 3-1h0v-3z" class="I"></path><path d="M430 142h1 0c1 0 2-1 3-1v2c-1 2-2 2-1 4l1-1v4h0c-1 0-1-1-1-1 0-1 0-2-1-3l-1 1v-1c0-1 0-3-1-4z" class="D"></path><path d="M353 169l2 1c0 1 0 1 1 1 2 0 3 1 4 0l1 1c-1 1-2 0-3 0l2 2h-1l3 1c0 1 1 1 1 1h1v1 2l-2 1-1 1s1 0 1 1h0 1l2 1c1 0 2 1 4 2 0 0 1 1 1 2 2 1 15 6 16 6v-1c1 0 3 1 4 1l15 4c1 0 4 1 5 2l-5-1c-6-1-12-3-18-5v1c4 2 10 4 14 4l1 1s3 0 3 1l-1-1h-2-4l-5-2v1c0 1 2 1 2 1-2 0-3 1-5 0l-19-2-2 1c-2 0-4-1-7-1h-2l-1 1c-1 0-2-1-2-1l-1 1 1 1c-1 0-1-1-2-1h-1-1c-1 1-3 1-4 2h-2 0c0-1 0-1-1-2l-6 2-2 1-1-3 1-2h0c-1 1-2 1-3 1 1-1 2-2 4-3h-1c0-1-1-1-1-2h2c1-1 2-1 2-1h2c0-1 1-1 1-1 1-1 2-1 2-2 1 1 1 0 1 1h0c1-1 1-1 1-2l-5-1c-1 0-3-1-4-2h1v-4l1-1c1 0 2 1 3 1h2l2 1v-1l-1-1h0c-1-1-1-1-2-1l1-1 1 1h0v-1c1-2 0-4 2-6l1 1v-2h3v1-2z" class="j"></path><path d="M363 182l2 1c1 0 2 1 4 2 0 0 1 1 1 2l-9-3c1-1 2-1 2-2z" class="I"></path><path d="M347 177c1-2 0-4 2-6l1 1h0l1 7c-1 0-2-1-4-1v-1z" class="t"></path><path d="M353 169l2 1c0 1 0 1 1 1 2 0 3 1 4 0l1 1c-1 1-2 0-3 0l2 2h-1-1l-3 1c-1 2 1 4-1 6-1-1-2-1-2-2h0c1-1 0-2 0-3v-3-1-1h-1 0l-1 1h0v-2h3v1-2z" class="R"></path><path d="M352 176v-1h1s1 0 2 1c0 1 0 3-1 4l-2-1c1-1 0-2 0-3z" class="V"></path><path d="M350 170h3v1h1 1v5c-1-1-2-1-2-1h-1v1-3-1-1h-1 0l-1 1h0v-2z" class="F"></path><path d="M359 174l3 1c0 1 1 1 1 1h1v1 2l-2 1-1 1s1 0 1 1h0 1c0 1-1 1-2 2l-7-3c2-2 0-4 1-6l3-1h1z" class="B"></path><path d="M357 178h1v1l-1 1h-1c0-1 0-1 1-2z" class="I"></path><path d="M359 177l1 1c2 0 3 0 4-1v2l-2 1-1 1s1 0 1 1h0c0 1 0 0-1 1 0-1-1-1-1-1-1-2-1-4-1-5z" class="G"></path><path d="M359 174l3 1c0 1 1 1 1 1h1v1c-1 1-2 1-4 1l-1-1-1-3h1z" class="b"></path><path d="M360 184l33 13h0v1c0 1 2 1 2 1-2 0-3 1-5 0h0c-1-1 0-1-1-1h-1-3c-1-1-2-1-2-1l-5-2v-1h-2c-1-1-1-1-2-1h-2l-1-1h-1-1c-1-1-2-1-3-1l-1-1v-1c-1-1-2 0-4-1-1-1-2-1-2-3l1-1z" class="B"></path><path d="M340 180l1-1c1 0 2 1 3 1h2l2 1v-1l12 4-1 1c0 2 1 2 2 3 2 1 3 0 4 1v1l1 1h-2v-1h-2 0-1c0-1-1-1-1-1h-1c-1 0-2-1-3-1v1c-3 0-5-1-8-2l-5-1c-1 0-3-1-4-2h1v-4z" class="U"></path><path d="M346 180l2 1c1 0 3 0 4 1 0 2 0 3-1 4-1 0-2 0-3-1h0-2c0-1 1-1 2-2h0v-1l-2-2z" class="F"></path><path d="M340 184c2 0 4 0 6 1h2 0c1 2 6 3 8 3v1c-3 0-5-1-8-2l-5-1c-1 0-3-1-4-2h1z" class="L"></path><path d="M340 180l1-1c1 0 2 1 3 1h2l2 2v1h0c-1 1-2 1-2 2-2-1-4-1-6-1v-4z" class="s"></path><path d="M348 187c3 1 5 2 8 2v-1c1 0 2 1 3 1h1s1 0 1 1h1 0 2v1h2c1 0 2 0 3 1h1 1l1 1h2c1 0 1 0 2 1h2v1l5 2s1 0 2 1h3 1c1 0 0 0 1 1h0l-19-2c-6-1-11-2-16-4v-2l1-1h2-2c-2-1-4 0-6 1v-1h-6c1-1 2-1 2-2 1 1 1 0 1 1h0c1-1 1-1 1-2z" class="W"></path><path d="M348 187c3 1 5 2 8 2v-1c1 0 2 1 3 1h1s1 0 1 1h1 0 2v1h2c1 0 2 0 3 1h1 1l1 1h2c1 0 1 0 2 1h2v1h-2 0c-1 0-3-1-4-1h-1c-1 0-1-1-1-1h-3l-1-1h-2l-3-1-3-1h-2c-2-1-4 0-6 1v-1h-6c1-1 2-1 2-2 1 1 1 0 1 1h0c1-1 1-1 1-2z" class="J"></path><path d="M350 191c2-1 4-2 6-1h2-2l-1 1v2c5 2 10 3 16 4l-2 1c-2 0-4-1-7-1h-2l-1 1c-1 0-2-1-2-1l-1 1 1 1c-1 0-1-1-2-1h-1-1c-1 1-3 1-4 2h-2 0c0-1 0-1-1-2l-6 2-2 1-1-3 1-2h0c-1 1-2 1-3 1 1-1 2-2 4-3h-1c0-1-1-1-1-2h2c1-1 2-1 2-1h2c0-1 1-1 1-1h6v1z" class="H"></path><path d="M351 194c3 0 8 2 11 3h-2l-1 1c-1 0-2-1-2-1l-1 1 1 1c-1 0-1-1-2-1h-1-1 2v-1c-1-1-2-2-4-2v-1z" class="B"></path><path d="M341 191v2h1 2c-1 1-3 2-5 3 0 0-1 1-1 0h0c-1 1-2 1-3 1 1-1 2-2 4-3h-1c0-1-1-1-1-2h2c1-1 2-1 2-1z" class="C"></path><path d="M339 192h1 1c-1 1-1 2-2 2h0-1c0-1-1-1-1-2h2z" class="F"></path><path d="M350 191c2-1 4-2 6-1h2-2l-1 1v2c-2 0-5-1-7-1l-2 1h0v-1s1 0 1-1h3z" class="M"></path><path d="M339 199c1-3 2-3 4-5h3 0c1 0 2-1 3 0h2c-3 2-6 3-10 4 0 1-1 1-2 1z" class="N"></path><path d="M351 194h0v1c2 0 3 1 4 2v1h-2c-1 1-3 1-4 2h-2 0c0-1 0-1-1-2l-6 2-1-1c1 0 2 0 2-1 4-1 7-2 10-4z" class="K"></path><path d="M529 146l4 2h0c-1 1-1 1-1 2-1 1 0 1 0 2 1 1 1 2 1 3h1c0 1 1 2 1 3h1v-1l1 2v2c1 0 1-1 1-1 1-1 1-1 1-2h1l-3 6c0 2-1 3-1 4h1c2 0 5-3 6-4-2 3-5 6-7 9-1 1-1 2-1 3h-2c-1 1-2 2-4 2v-1l1-1h0c1 0 1-1 2-1h1c2-1 1-3 1-5h0-1v1c-1 3-4 3-6 4-3 2-5 3-7 5-2 1-5 3-6 4h-3-1l-1 1c-1 1-2 1-3 1-1 1-2 1-4 2h0-2c-4 1-7 2-10 4-3 1-7 3-10 5l-6 3 4-3-1-2-5 3c1-2 4-3 6-5l16-13v-3-2h-2v-1l4-4c0-1 0-1-1-1l4-3 2-1c2-2 4-3 6-3l2-2h1l1-1 2-1 2-2 1-1c0-1 0-2 1-3h1 0l2-1h1 1v1h2c0 1 0 2 1 2h0c1 0 2-1 2-2l1-3c0-1 1-1 1-1 1-1 0-1 0-2z" class="W"></path><path d="M530 167l2 2c-2 2-5 4-8 5 1-1 2-1 3-3h1v-1c-1 1-2 2-4 2h-1l1-1c1 0 4-1 5-2l1-2z" class="D"></path><path d="M514 174h0l7-4 1 1s1-1 2-1v1l-1 1s-1 1-2 1c-4 2-8 3-12 5v-1l5-3z" class="K"></path><path d="M512 173h1c0 1 1 1 1 1l-5 3v1c-1 1-2 1-3 2l-3 1c-1 0-2 1-3 1h0-3 1l3-3s0-1 1-1v-1l-1-1 2-1c2 1 2 0 4 0h1c1 0 3-1 4-2z" class="a"></path><path d="M503 175c2 1 2 0 4 0l-6 4s0-1 1-1v-1l-1-1 2-1z" class="Q"></path><path d="M515 179l1 1-16 8c-4 1-7 2-10 4-3 1-7 3-10 5l-6 3 4-3 37-18zm-21 1c6-4 11-9 17-13l1 1-1 1-8 6-2 1 1 1v1c-1 0-1 1-1 1l-3 3h-1l-6 4-1 1h-1-2l-10 8-5 3c1-2 4-3 6-5l16-13z" class="H"></path><path d="M501 176l1 1v1c-1 0-1 1-1 1l-3 3h-1l-6 4-1 1h-1-2c4-4 10-7 14-11z" class="B"></path><path d="M528 164l2 1c0 1-1 2-2 3h0c0 1 1 1 1 1-1 1-4 2-5 2v-1c-1 0-2 1-2 1l-1-1-7 4h0s-1 0-1-1h-1c-1 1-3 2-4 2h-1c-2 0-2 1-4 0l8-6 1-1h0l1 1c2 0 3-1 4-2l2-1 2 2 1-2c2-1 4-2 6-2h0z" class="Q"></path><path d="M517 169h3c-1 1-2 2-4 2-1 1-2 1-3 2h-1-1v-1l6-3z" class="g"></path><path d="M528 164c1 2 0 3-1 4s-2 0-2 0c-1 1-2 1-3 1h0l-1-1 1-2c2-1 4-2 6-2z" class="I"></path><path d="M503 175l8-6h1 5l-6 3v1h1c-1 1-3 2-4 2h-1c-2 0-2 1-4 0z" class="E"></path><path d="M508 175l-1-1c1-1 2-2 4-2v1h1c-1 1-3 2-4 2z" class="K"></path><path d="M510 160l1-1 1 1-2 2v1c2 1 3 0 5 0l-4 4c-6 4-11 9-17 13v-3-2h-2v-1l4-4c0-1 0-1-1-1l4-3 2-1c2-2 4-3 6-3l2-2h1z" class="c"></path><path d="M504 167c1-1 2-2 4-3 0 2-2 3-4 5v-2z" class="E"></path><path d="M499 173l1-1h2c-2 2-5 5-7 5h0c1-2 2-3 4-4z" class="w"></path><path d="M501 170l3-3v2l-2 3h0-2l-1 1c-1-1-1 0-1-1h-1 0c0-1 1-2 1-2v-1c1 1 1 1 1 2l2-1z" class="p"></path><path d="M496 170l2-1v1s-1 1-1 2h0 1c0 1 0 0 1 1-2 1-3 2-4 4h-1v-2h-2v-1l4-4z" class="M"></path><path d="M507 162h2c0 1-1 1-1 2-2 1-3 2-4 3l-3 3-2 1c0-1 0-1-1-2l-2 1c0-1 0-1-1-1l4-3 2-1c2-2 4-3 6-3z" class="e"></path><path d="M499 166l2-1v2h-1c0 1 0 2 1 3l-2 1c0-1 0-1-1-2l-2 1c0-1 0-1-1-1l4-3z" class="n"></path><path d="M533 155h1c0 1 1 2 1 3h1v-1l1 2v2c1 0 1-1 1-1 1-1 1-1 1-2h1l-3 6c0 2-1 3-1 4h1c2 0 5-3 6-4-2 3-5 6-7 9-1 1-1 2-1 3h-2c-1 1-2 2-4 2v-1l1-1h0c1 0 1-1 2-1h1c2-1 1-3 1-5h0-1v1c-1 3-4 3-6 4-3 2-5 3-7 5-2 1-5 3-6 4h-3-1l-1 1c-1 1-2 1-3 1-1 1-2 1-4 2h0-2l16-8-1-1c2-2 6-4 9-5s6-3 8-5l2-2-2-2c1-1 1-1 1-2s1-1 1-2c1-2 0-4-1-6z" class="h"></path><path d="M533 155h1c0 1 1 2 1 3h1v-1l1 2c0 1-1 1-1 1 1 2-1 5-2 7l-2-2c1-1 1-1 1-2s1-1 1-2c1-2 0-4-1-6z" class="I"></path><path d="M533 170v1c-1 3-4 3-6 4-3 2-5 3-7 5-2 1-5 3-6 4h-3-1l-1 1c-1 1-2 1-3 1-1 1-2 1-4 2h0-2l16-8h0c6-2 12-6 17-10z" class="a"></path><path d="M529 146l4 2h0c-1 1-1 1-1 2-1 1 0 1 0 2 1 1 1 2 1 3 1 2 2 4 1 6 0 1-1 1-1 2s0 1-1 2l2 2-2 2-2-2-1 2s-1 0-1-1h0c1-1 2-2 2-3l-2-1h0c-2 0-4 1-6 2l-1 2-2-2-2 1c-1 1-2 2-4 2l-1-1h0l-1-1 4-4c-2 0-3 1-5 0v-1l2-2-1-1 2-1 2-2 1-1c0-1 0-2 1-3h1 0l2-1h1 1v1h2c0 1 0 2 1 2h0c1 0 2-1 2-2l1-3c0-1 1-1 1-1 1-1 0-1 0-2z" class="c"></path><path d="M520 161c1-1 3-3 4-3l2 1c-2 2-3 3-6 3v-1z" class="D"></path><path d="M519 164c2-1 4-2 6-1h0c0 2-4 1-3 3l-1 2-2-2-2 1v-2l2-1z" class="M"></path><path d="M524 152c0 1 0 2 1 2h0l-1 2h1c1 0 1 0 2-1l1 1v1c-2 0-3 0-4 1-1 0-3 2-4 3h0l-1-1 1-1 3-2h0-3 0l1-2h0c2-1 2-1 3-3z" class="T"></path><path d="M515 156c0 1 0 1 1 1 0 0 1 0 1-1h1s1 0 2-1h0 1l-1 2h0 3 0l-3 2v-1c-3 0-5 2-8 2l-1-1 2-1 2-2z" class="Y"></path><path d="M512 160c3 0 5-2 8-2v1l-1 1-4 3c-2 0-3 1-5 0v-1l2-2z" class="B"></path><path d="M521 151h1v1h2c-1 2-1 2-3 3h0-1 0c-1 1-2 1-2 1h-1c0 1-1 1-1 1-1 0-1 0-1-1l1-1c0-1 0-2 1-3h1 0l2-1h1z" class="h"></path><path d="M518 152h2l1 1v1c-1 0-2 0-3-1v-1h0z" class="c"></path><path d="M515 163l4-3 1 1-3 3h2l-2 1v2c-1 1-2 2-4 2l-1-1h0l-1-1 4-4z" class="O"></path><path d="M517 164h2l-2 1v2c-1 1-2 2-4 2l-1-1 5-4z" class="D"></path><path d="M529 146l4 2h0c-1 1-1 1-1 2-1 1 0 1 0 2 1 1 1 2 1 3 1 2 2 4 1 6 0 1-1 1-1 2s0 1-1 2l2 2-2 2-2-2-1 2s-1 0-1-1h0c1-1 2-2 2-3l-2-1v-1c0-1 0-1-1-2h0c0-1 0-1 1-2h-1v-1l1-1h0v-1l-1-1c-1 1-1 1-2 1h-1l1-2c1 0 2-1 2-2l1-3c0-1 1-1 1-1 1-1 0-1 0-2z" class="e"></path><path d="M527 155c1 0 2-1 3 0h2c0 1 0 1-1 1h-3l-1-1z" class="G"></path><path d="M528 149h1c1 1 1 2 1 3v1c-1-1-2-1-3-1l1-3z" class="r"></path><path d="M528 156h3v2l-3 1h0-1v-1l1-1h0v-1z" class="Y"></path><path d="M528 159l3-1v3h3c0 1-1 1-1 2s0 1-1 2l2 2-2 2-2-2-1 2s-1 0-1-1h0c1-1 2-2 2-3l-2-1v-1c0-1 0-1-1-2h0c0-1 0-1 1-2h0z" class="E"></path><path d="M528 159l3-1v3c-2 0-2-1-3-2z" class="i"></path><path d="M532 165l2 2-2 2-2-2 2-2z" class="k"></path><path d="M528 163c2-1 2-1 3-1l1 1-2 2-2-1v-1z" class="e"></path><path d="M441 122l1-2h1c1 1 1 2 1 3h1 1c1 0 2 1 2 1 1 1 1 1 2 1v1h1c0 1 0 2-1 3h1v-1c1 0 1-1 2-1l1 1s1 1 2 1h0c0 1-1 3-1 4h1c0 1 0 2 1 2 0 1 0 1 1 2v2 1l-1 2h1v2h1c1 0 1 1 2 2h1v1h1l1-3c1-2 2-4 3-5l-1-1h0c0-1 0-2-1-3h0v-1 1h2 0v1 1h0 0c2 1 2 1 3 2l-3 3h1c1 1 1 2 1 3 0-1 0-2 1-3h1v1h0 1v1l-3 6c0 1 0 2-1 3v2l-2 7v3c-1 1-1 2-1 3v1c0 1-1 1-1 2l-3 4h-1c0 1-1 1-2 2l-1 1h-1l-1-2-1-1h-1v-1h-1c0 1 0 1-1 2 0 1-1 4-1 5l-1 6-2 6v1-7-1c0-2 0-4-1-5l-1-1c1-1 1-2 1-3h-1v-3h0v-1-1c-1-1-1-1-1-3v-7h-1v-1l-1-1c0-1-1-2-1-4h-1v4h0c-1-1-1-1-1-2v-1h1v-3c-1 0-1-1-1-2v-1c0-2 1-5 0-7h0l-1 2h-1c0-3 0-5 1-7v-1h1l1 1h0v-1-1-2-1h0v-3c1-1 1-2 2-4v-1l-5-1c2 0 3 0 4-1v-2z" class="R"></path><path d="M454 159v-3l1 1 1 2v3l-1-3h-1z" class="J"></path><path d="M454 150l-1-2c0-1 0-2 1-3h1v3 2h-1z" class="K"></path><path d="M455 148l1 1c0 1 0 2 1 3-2 1-1 1-1 3-1-2-2-3-2-5h1v-2z" class="E"></path><path d="M448 135h1v1h0v1 1h1v-1c1 0 1 0 2 1-1 0-1 1-2 1h0c-2 0 0 3-1 4h0c-1-1-1-3-1-4v-4z" class="S"></path><path d="M456 141v4 4l-1-1v-3c0-1-1-2-2-3 2 0 2 0 3-1z" class="M"></path><path d="M454 159h1l1 3c0 1 0 2 1 3v1h-1 0v-3l-2-1c-1-1 0-2 0-3z" class="D"></path><path d="M456 155c0-2-1-2 1-3v5 2 2 4c-1-1-1-2-1-3v-3-4z" class="d"></path><path d="M450 139c1 0 1-1 2-1h0l1 4-1 1c1 1 1 3 0 5l-1-1v-1-1-2c-1-2-1-3-1-4z" class="J"></path><path d="M445 136v2c1 1 1 2 1 3h1v-4 6c-1 1-1 2-1 3v1h0-1 0c0-4-1-7-1-10l1-1z" class="S"></path><path d="M449 164h-1c-1-2-1-4-1-6 0-5 0-10 1-15l1 2v2c1 1 1 2 1 2 1 0 1-1 1-1 1 1 0 4 0 6l-2 10z" class="q"></path><defs><linearGradient id="y" x1="439.991" y1="161.246" x2="450.369" y2="153.851" xlink:href="#B"><stop offset="0" stop-color="#0c0e10"></stop><stop offset="1" stop-color="#3f3d3b"></stop></linearGradient></defs><path fill="url(#y)" d="M446 147c1 1 1 3 1 4-1 5-1 11 1 15v2h-2c-1 1 0 1-2 1v-7h-1v-1-3-9h2v-2h0 1 0z"></path><path d="M443 149h2c0 4 0 9-1 13h-1v-1-3-9z" class="T"></path><path d="M450 126h1c0 1 0 2-1 3h1v-1c1 0 1-1 2-1l1 1s1 1 2 1h0c0 1-1 3-1 4h1c0 1 0 2 1 2 0 1 0 1 1 2v2-1h-1c0 1 0 2-1 3s-1 1-3 1h0l-1-4h0c-1-1-1-1-2-1v1h-1v-1-1h0v-1h-1c0-1 0-3-1-4 0-1 1-1 1-1 0-2 0-2-1-3l3-1z" class="B"></path><path d="M453 127l1 1s1 1 2 1h0c0 1-1 3-1 4l-1 4c-1-2 0-5-1-7v-3z" class="E"></path><path d="M455 133h1c0 1 0 2 1 2 0 1 0 1 1 2v2-1h-1c0 1 0 2-1 3s-1 1-3 1h0l-1-4h0 1v-1l1 1v-1h0l1-4z" class="I"></path><path d="M452 138h0 1v-1l1 1c0 1 0 1 1 2 0 0 1 0 1 1-1 1-1 1-3 1h0l-1-4z" class="f"></path><path d="M450 126h1c0 1 0 2-1 3h1v-1c1 0 1-1 2-1v3l-1 1c0 1 0 1-1 2 0 1-1 2-2 3h0v-1h-1c0-1 0-3-1-4 0-1 1-1 1-1 0-2 0-2-1-3l3-1z" class="M"></path><path d="M449 135l1-5h0l1 3c0 1-1 2-2 3h0v-1z" class="k"></path><path d="M440 139h0v-1-1-2-1h0l1 6v-2l1-1v2c1-1 1-2 1-3h0l1 1c0 3 1 6 1 10v2h-2v9 3l-1-1c0-1-1-2-1-4h-1v4h0c-1-1-1-1-1-2v-1h1v-3c-1 0-1-1-1-2v-1c0-2 1-5 0-7h0l-1 2h-1c0-3 0-5 1-7v-1h1l1 1z" class="B"></path><path d="M438 138h1l1 1c-1 1-1 2-1 3h-1 0v-3-1z" class="W"></path><defs><linearGradient id="z" x1="444.429" y1="148.793" x2="440.054" y2="150.493" xlink:href="#B"><stop offset="0" stop-color="#3c3e3c"></stop><stop offset="1" stop-color="#585658"></stop></linearGradient></defs><path fill="url(#z)" d="M441 145v3h1c0-1 0-3 1-4h0c1 2 1 4 0 5v9c-3-3-3-8-2-13z"></path><path d="M441 140v-2l1-1v2c1-1 1-2 1-3h0l1 1c0 3 1 6 1 10v2h-2c1-1 1-3 0-5h0c-1 1-1 3-1 4h-1v-3-5z" class="Y"></path><path d="M441 122l1-2h1c1 1 1 2 1 3h1 1c1 0 2 1 2 1 1 1 1 1 2 1v1l-3 1c1 1 1 1 1 3 0 0-1 0-1 1v6 4h-1c0-1 0-2-1-3v-2l-1 1-1-1h0c0 1 0 2-1 3v-2l-1 1v2l-1-6v-3c1-1 1-2 2-4v-1l-5-1c2 0 3 0 4-1v-2z" class="f"></path><path d="M446 123c1 0 2 1 2 1 1 1 1 1 2 1v1l-3 1v1h-1c1-1 1-1 1-2l-3-3h0 1 1z" class="n"></path><path d="M442 126v2c0 1 0 0 1 1-2 2-1 6-1 9l1-5h1c0 1 1 2 1 3l-1 1-1-1h0c0 1 0 2-1 3v-2l-1 1v2l-1-6v-3c1-1 1-2 2-4v-1z" class="J"></path><path d="M441 122l1-2h1c1 1 1 2 1 3h0v1c0 1 1 2 0 3 0 1 0 1-1 1v1c-1-1-1 0-1-1v-2l-5-1c2 0 3 0 4-1v-2z" class="K"></path><path d="M444 124c0 1 1 2 0 3 0 1 0 1-1 1v-1c-1-1 0-2 1-3z" class="n"></path><path d="M451 154c2 2 1 6 1 9l1 1 1 4h-2v1h-1v1 3l-1 1h1v-1l1 1c0 1 0 1-1 2 0 1-1 4-1 5l-1 6-2 6v1-7-1c0-2 0-4-1-5l-1-1c1-1 1-2 1-3h-1v-3h0v-1-1c-1-1-1-1-1-3 2 0 1 0 2-1h2v-2l1-1v-1l2-10z" class="f"></path><path d="M452 168v-5l1 1 1 4h-2z" class="B"></path><path d="M446 177h1l1 1-1 8c0-2 0-4-1-5l-1-1c1-1 1-2 1-3z" class="k"></path><path d="M448 166l1-1v6 2c0 1 0 2-1 3v2l-1-1h-1-1v-3h0v-1-1c-1-1-1-1-1-3 2 0 1 0 2-1h2v-2z" class="D"></path><path d="M445 174h1c0 1 0 2-1 3h0v-3h0z" class="M"></path><path d="M449 173h-1c-1 0-1-1-1-2h0 2v2z" class="k"></path><path d="M466 138h0c0-1 0-2-1-3h0v-1 1h2 0v1 1h0 0c2 1 2 1 3 2l-3 3h1c1 1 1 2 1 3 0-1 0-2 1-3h1v1h0 1v1l-3 6c0 1 0 2-1 3v2l-2 7v3c-1 1-1 2-1 3v1c0 1-1 1-1 2l-3 4h-1c0 1-1 1-2 2l-1 1h-1l-1-2-1-1h-1v-1h-1l-1-1v1h-1l1-1v-3-1h1v-1h2l-1-4 1-2 2 1v3h0 1v-1-4-2-2-5c-1-1-1-2-1-3v-4-4c1-1 1-2 1-3h1v1 1l-1 2h1v2h1c1 0 1 1 2 2h1v1h1l1-3c1-2 2-4 3-5l-1-1z" class="f"></path><path d="M458 175c1-2 1-4 3-5h1v1h1 1l-3 4h-1c0 1-1 1-2 2v-2z" class="D"></path><path d="M464 152h1v4h0l1-1c1 0 1-1 2-2v2l-2 7v3c-1-1-1-3-1-4h0c1-1 0-1 1-1v-1h-1v1h-1v-8z" class="c"></path><path d="M454 168c0 1 0 1 1 2h0v-1l1-1c1 0 1 0 1 1h1v3c-1 0-1 1-1 2v1h1v2l-1 1h-1l-1-2-1-1h-1v-1h-1l-1-1v1h-1l1-1v-3-1h1v-1h2z" class="I"></path><path d="M451 170h1c0 2 0 2-1 3h0v-3z" class="S"></path><path d="M455 176v-1c1-1 1-1 1-2h0 1v1 1h1v2l-1 1h-1l-1-2z" class="B"></path><path d="M466 138h0c0-1 0-2-1-3h0v-1 1h2 0v1 1h0 0c2 1 2 1 3 2l-3 3h1c1 1 1 2 1 3 0-1 0-2 1-3h1v1h0 1v1l-3 6c0 1 0 2-1 3s-1 2-2 2l-1 1h0v-4h-1l1-3v-1-1c0-1 1-2 1-3h0 0-2c1-2 2-4 3-5l-1-1z" class="n"></path><path d="M470 142h1v1h0l-2 3h0v-1c0-1 0-2 1-3z" class="S"></path><path d="M467 142h1c1 1 1 2 1 3v1h0l1 1-2 4v-6h-1c0-1 0-1-1-2l1-1z" class="o"></path><path d="M466 148h1c1 3 0 3-1 5v2l-1 1h0v-4l1-4z" class="G"></path><path d="M466 138h0c0-1 0-2-1-3h0v-1 1h2 0v1 1h0 0c2 1 2 1 3 2l-3 3-1 1c1 1 1 1 1 2s-1 1-1 3l-1 4h-1l1-3v-1-1c0-1 1-2 1-3h0 0-2c1-2 2-4 3-5l-1-1z" class="D"></path><path d="M456 141c1-1 1-2 1-3h1v1 1l-1 2h1v2h1c1 0 1 1 2 2h1v1h1l-1 5-1 6v3h-1v-3l-1-1c0-1 0-2-1-3h0l-1 3v-5c-1-1-1-2-1-3v-4-4z" class="B"></path><path d="M456 141c1-1 1-2 1-3h1v1 1l-1 2h1v2h0c-1 1 0 3-1 4-1-1-1-2-1-3v-4z" class="Y"></path><path d="M459 144c1 0 1 1 2 2h1v1l-1 1c-2 2-1 6-1 9v1h1v3h-1v-3l-1-8v-6z" class="r"></path><path d="M462 147h1l-1 5-1 6h-1v-1c0-3-1-7 1-9l1-1z" class="i"></path><path d="M462 147h1l-1 5h0-1v-1-3h0l1-1z" class="g"></path><path d="M361 87c2-1 2-2 4-2 3 0 7 4 9 6l1 2h1c1 0 1 1 1 1l2 2h2l2 3 1 1 1 1c-1 0-1 1-1 2l2 2 9 11c1 2 3 5 5 7l7 8 6 8s1 1 1 2l-1-1 1 2h0 0l2 2c1 1 2 3 2 4-2 0-2-1-3-2h-1v2l1 1s0 2 1 2c0 1 0 1-1 2l-4-4-2-2c-1 0-1 0-1-1-1 0-1-1-2-1l-1-1h0l-1-1c-1-1-2-3-4-3 1 0 1 1 1 2h1v1c-1 0-3 0-4-1h0-7c-1 0-3 1-4 0-2 1-1 1-2 2 2 1 3 2 4 4h-3l-3-3h0l-3-3v1l2 2s0 1-1 1c-2 1-4 1-6 1-1 1-3 2-4 2v-1c-1-1-1-2-3-2h-2v-1-2-1h-1l-1-1-1 2-2-1c-1-1-1-2-2-3 0-1-1-3-1-4v-3l-1-5-1-3h1c0-1 1 0 2 0 0-1-1-2-1-3s1-4 1-6c0-1 0-2 1-3 0-1 1-2 1-4v-1-3c0-1 1-2 1-3h2v-4l-1-2c1-1 1-1 1-2s0-1 1-1c0-1 0-1-1-2h0l-2 1c0-1-1-1-2-1 1-1 1-2 1-3z" class="Z"></path><path d="M390 128l2 2v1h-1-2-1c-1-1-1-1-2-1h-2c-1-1-6-1-8 0h-1c2-2 8-2 10-1 2 0 4 0 5-1z" class="c"></path><path d="M379 118c5 1 11 6 15 9-1 0-5-1-7-2v2c-4-2-8-2-13-3h0 1 1l2-2h0v-1h-6 0-1l1-1c2-1 3 0 5 0 1 0 2 0 3-1l-1-1z" class="h"></path><path d="M376 124l2-2h0v-1h-6 0-1l1-1c2-1 3 0 5 0h-2c1 1 3 1 5 1 1 0 2 0 3 1h0c1 0 2 1 3 2-3 0-6-1-10 0z" class="C"></path><path d="M368 101c1 1 3 1 4 1l1 2 1 1v1c1 1 1 1 2 1h1l1 1h0 2v1c1 2 3 4 4 5s1 2 2 3h0c-1 1-2 0-3 1 0-1-2-1-2-2h-6 0l1 1h1-9l6-2c1 0 1 0 1-1v-1h-2-3c0-1 1-2 1-2v-1l-1-1v-1l1-2-1-1c-1 0-2 1-3 0 0-1 1-2 1-2h0v-2z" class="K"></path><path d="M378 108h0 2v1c1 2 3 4 4 5s1 2 2 3h0c-1 1-2 0-3 1 0-1-2-1-2-2 1 1 1 1 2 0v-1c0-1-1-1-1-2h-1c-1-2-2-2-3-3h-1 0l1-2z" class="c"></path><path d="M368 101c1 1 3 1 4 1l1 2h0c0 1-1 1-1 1l-1 1c2 0 2 0 3 1s1 2 2 4c0 1 1 2 3 3v1h0-5c1 0 1 0 1-1v-1h-2-3c0-1 1-2 1-2v-1l-1-1v-1l1-2-1-1c-1 0-2 1-3 0 0-1 1-2 1-2h0v-2z" class="G"></path><path d="M371 111v-1l-1-1v-1l1 1c1 0 2 0 2 1v1c-1 0-1-1-2 0z" class="b"></path><path d="M373 110l1 1h-1v2h-3c0-1 1-2 1-2 1-1 1 0 2 0v-1z" class="O"></path><path d="M362 101h2 1 0 3 0v2h0s-1 1-1 2c1 1 2 0 3 0l1 1-1 2v1l1 1v1s-1 1-1 2h-3l-1 1v1c-1 1-1 2-1 3h0c-1-1-2-1-3-1h-1v2c0 1 0 1 1 1v1 4h1c1-1 1-1 1-2-1-1-1-1-1-3h2c1 1 1 2 1 3v1c-1 0-1 1-1 1 0 1 0 1-1 1 3 3 6 5 9 7h-1l-1-1v2c-1-1-1-2-2-2l-2 2v1c-1 0-1 0-2-1s-1-2-3-3c-1 0-2-1-3-2 0-1-1-2-2-2l-1-3h1c0-1 1 0 2 0 0-1-1-2-1-3s1-4 1-6c0-1 0-2 1-3 0-1 1-2 1-4v-1-3c0-1 1-2 1-3z" class="X"></path><path d="M362 112l1 1-2 4h0c-1-1 0-1 0-2s1-2 1-3z" class="b"></path><path d="M362 112v-2-1h1l1 1c0 1-1 2-1 3l-1-1z" class="C"></path><path d="M370 105l1 1-1 2v1l1 1v1s-1 1-1 2h-3c0-1 1-1 1-3h-1-2-1l1-1h2 1c0-1 0-1 1-1v-1l-1-1c1 0 2 0 2-1z" class="P"></path><path d="M362 101h2 1 0 3 0v2h0s-1 1-1 2c1 1 2 0 3 0 0 1-1 1-2 1-2 0-2 1-3 1l-1 1h-1c-1-1-1-1-2-1v-3c0-1 1-2 1-3z" class="L"></path><path d="M357 127c1 0 2 1 2 2 1 1 2 2 3 2 2 1 2 2 3 3s1 1 2 1v-1l2-2c1 0 1 1 2 2v-2l1 1h1c1-1 4-2 5-2h3 2c1 0 2 0 3-1 1 0 1 0 2 1h1l2 2c0 1 1 2 2 3 1 0 2 1 3 2 0 1 0 1-1 1h1l-1 1h0c1 0 1 1 3 0l1-1 1 1c1 0 1 1 1 2h1v1c-1 0-3 0-4-1h0-7c-1 0-3 1-4 0-2 1-1 1-2 2 2 1 3 2 4 4h-3l-3-3h0l-3-3v1l2 2s0 1-1 1c-2 1-4 1-6 1-1 1-3 2-4 2v-1c-1-1-1-2-3-2h-2v-1-2-1h-1l-1-1-1 2-2-1c-1-1-1-2-2-3 0-1-1-3-1-4v-3l-1-5z" class="b"></path><path d="M367 134l2-2c1 0 1 1 2 2 0 0-1 0-1 1-1 0-1 0-1 1l-2-2z" class="Z"></path><path d="M375 137c-1-1-2-2-2-3 1 0 2-1 2-1h1 1l1-1h2c-1 1-1 1-1 2 0 0-3 2-4 3z" class="C"></path><path d="M381 132h3c-1 0-3 0-5 1 1 1 2 2 3 2h2c-2 0-3 0-4 1l-1 1-1-1c0 1 0 2-2 2l-1-1c1-1 4-3 4-3 0-1 0-1 1-2h1z" class="P"></path><path d="M384 132c1 0 2 0 3 1 0 1 0 1-2 2h-1-2c-1 0-2-1-3-2 2-1 4-1 5-1z" class="X"></path><path d="M358 132c2 2 3 3 4 6l3 1s1 0 0 1c-1 0-1-1-3 0l2 1-1 2-2-1c-1-1-1-2-2-3 0-1-1-3-1-4v-3z" class="O"></path><path d="M358 135c1 1 1 2 2 3h1 0c0 1 1 1 1 2l2 1-1 2-2-1c-1-1-1-2-2-3 0-1-1-3-1-4z" class="d"></path><path d="M357 127c1 0 2 1 2 2 1 1 2 2 3 2 2 1 2 2 3 3s1 1 2 1l-1 1-1-1c-1 0-1 1-1 1v1h1l1 1h-4c-1-3-2-4-4-6l-1-5z" class="C"></path><path d="M386 130c1 0 1 0 2 1h1l2 2c0 1 1 2 2 3 1 0 1 1 1 2h-5 0l-2-1h-2-1-1c-1 0-2-1-3 0v-1c1-1 2-1 4-1h1c2-1 2-1 2-2-1-1-2-1-3-1h-3v-1h2c1 0 2 0 3-1z" class="G"></path><path d="M387 133h1c0 1 0 1-1 2 1 1 1 1 1 2h-3-1-1c-1 0-2-1-3 0v-1c1-1 2-1 4-1h1c2-1 2-1 2-2z" class="b"></path><path d="M386 130c1 0 1 0 2 1h1l2 2c0 1 1 2 2 3 1 0 1 1 1 2h-5 0 3v-1l-1-1v-1-1c-1 0-2-1-3-1h-1c-1-1-2-1-3-1h-3v-1h2c1 0 2 0 3-1z" class="Q"></path><path d="M376 138c2 0 2-1 2-2l1 1 1-1v1c1-1 2 0 3 0h1c0 1 1 1 2 2v3h1 0c-2 1-1 1-2 2 2 1 3 2 4 4h-3l-3-3h0l-3-3c-1-1-2-2-3-4h-1z" class="L"></path><path d="M380 137c1-1 2 0 3 0h1c0 1 1 1 2 2v3h1 0c-2 1-1 1-2 2 0 0-1 0-1-1-1-1-1-2-2-3-1 0-1 0-1-1 1 0 2 0 2-1h0c-1 0-2 0-3-1z" class="G"></path><path d="M382 140c1 0 2 0 3 1v1l-1 1h0c-1-1-1-2-2-3z" class="H"></path><path d="M393 136c1 0 2 1 3 2 0 1 0 1-1 1h1l-1 1h0c1 0 1 1 3 0l1-1 1 1c1 0 1 1 1 2h1v1c-1 0-3 0-4-1h0-7c-1 0-3 1-4 0h0-1v-3c-1-1-2-1-2-2h1 2l2 1h0 5c0-1 0-2-1-2z" class="S"></path><path d="M384 137h1 2l-1 1c1 1 1 1 2 1 2 1 4 2 6 2h4v1h-7c-1 0-3 1-4 0h0-1v-3c-1-1-2-1-2-2z" class="T"></path><path d="M368 140l-1-1v-1h2l3-3 5 5 3 3 2 2s0 1-1 1c-2 1-4 1-6 1-1 1-3 2-4 2v-1c-1-1-1-2-3-2h-2v-1-2-1h-1v-1-1l2 1 1-1z" class="X"></path><path d="M368 140l1 1c0 1 0 2 1 3h-2v-3h-1l1-1z" class="j"></path><path d="M365 142v-1-1l2 1h1v3h-1l-1-1v-1h-1z" class="P"></path><path d="M372 144c0-2 0-3-1-4v-1h1c1 1 1 1 1 2s0 3 1 4h-1l-1-1z" class="j"></path><path d="M366 143l1 1h1 2l1 1 1-1 1 1h1 1v2c-1 1-3 2-4 2v-1c-1-1-1-2-3-2h-2v-1-2z" class="H"></path><path d="M372 144l1 1c-1 1 0 1-1 1l-1-1 1-1z" class="F"></path><defs><linearGradient id="AA" x1="397.743" y1="109.491" x2="371.521" y2="118.518" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#242425"></stop></linearGradient></defs><path fill="url(#AA)" d="M361 87c2-1 2-2 4-2 3 0 7 4 9 6l1 2h1c1 0 1 1 1 1l2 2h2l2 3 1 1 1 1c-1 0-1 1-1 2l2 2 9 11c1 2 3 5 5 7l7 8 6 8s1 1 1 2l-1-1 1 2h0 0l2 2c1 1 2 3 2 4-2 0-2-1-3-2h-1v2l1 1s0 2 1 2c0 1 0 1-1 2l-4-4-2-2c-1 0-1 0-1-1-1 0-1-1-2-1l-1-1h0l-1-1c-1-1-2-3-4-3l-1-1-1 1c-2 1-2 0-3 0h0l1-1h-1c1 0 1 0 1-1-1-1-2-2-3-2-1-1-2-2-2-3l-2-2h2 1v-1l-2-2c-1 0-2-1-3-1v-2c2 1 6 2 7 2-4-3-10-8-15-9l-2-1h-1l-1-1h0 6c0 1 2 1 2 2 1-1 2 0 3-1h0c-1-1-1-2-2-3s-3-3-4-5v-1h-2 0l-1-1h-1c-1 0-1 0-2-1v-1l-1-1-1-2c-1 0-3 0-4-1h0-3 0-1v-4l-1-2c1-1 1-1 1-2s0-1 1-1c0-1 0-1-1-2h0l-2 1c0-1-1-1-2-1 1-1 1-2 1-3z"></path><path d="M376 93c1 0 1 1 1 1l2 2h2l2 3 1 1 1 1c-1 0-1 1-1 2l-3-3v1l-4-5h0c-1-1-1-2-2-3h1z" class="S"></path><path d="M381 96l2 3h-2l-1-2-1-1h2z" class="D"></path><path d="M385 112c2 2 4 5 5 7 0 1 1 2 1 3h0c-1-1-2-1-3-2-1 0-1-1-2-1-1-1-2-1-3-1 1-1 2 0 3-1h0c-1-1-1-2-2-3h1v-2h0z" class="U"></path><path d="M387 127v-2c2 1 6 2 7 2l1 1c1 2 2 3 3 5s2 4 4 7c0 1 2 2 3 2h0v-1-1c-1 0 0 0-1-1-1-3-4-6-6-10h1c1 1 1 1 2 3 2 3 4 5 6 7h1c0-1-1-2-1-3 1 1 3 4 3 6-1 0-2-1-3-2h-1l1 1-1 2-1 1-1-1c-1-1-2-3-4-3l-1-1-1 1c-2 1-2 0-3 0h0l1-1h-1c1 0 1 0 1-1-1-1-2-2-3-2-1-1-2-2-2-3l-2-2h2 1v-1l-2-2c-1 0-2-1-3-1z" class="W"></path><path d="M387 127v-2c2 1 6 2 7 2l1 1-2 1c0 1 0 3 1 4 1 2 3 3 4 5l1 1-1 1c-2 1-2 0-3 0h0l1-1h-1c1 0 1 0 1-1-1-1-2-2-3-2-1-1-2-2-2-3l-2-2h2 1v-1l-2-2c-1 0-2-1-3-1z" class="B"></path><path d="M398 138l1 1-1 1c-1 0-1 0-1-1s0-1 1-1z" class="I"></path><path d="M366 93v-1c1 0 1 0 2 1h1l1 1h1l-2-2h1c3 3 6 7 8 11 1 1 2 3 4 5l3 4h0v2h-1c-1-1-3-3-4-5v-1h-2 0l-1-1h-1c-1 0-1 0-2-1v-1l-1-1-1-2c-1 0-3 0-4-1h0-3 0-1v-4l-1-2c1-1 1-1 1-2s0-1 1-1l1 1z" class="M"></path><path d="M364 93c0-1 0-1 1-1l1 1 2 2h1s0 1 1 2h0-2s-1 0-2-1c-1 0-1 0-2 1l-1-2c1-1 1-1 1-2z" class="c"></path><path d="M364 97c1-1 1-1 2-1 1 1 2 1 2 1h2c1 0 1 1 2 2h1l-1 1v2c-1 0-3 0-4-1h0-3 0-1v-4z" class="J"></path><path d="M368 97h2c1 0 1 1 2 2h-1c-1 0-2-1-3-1v-1z" class="h"></path><path d="M364 97c1-1 1-1 2-1 1 1 2 1 2 1v1c-1 1-1 2 0 3h-3 0-1v-4z" class="p"></path><path d="M365 101c0-1-1-2 0-3h1v1h0v2h-1 0z" class="Q"></path><path d="M381 101v-1l3 3 2 2 9 11c1 2 3 5 5 7l7 8 6 8s1 1 1 2l-1-1 1 2h0 0l2 2c1 1 2 3 2 4-2 0-2-1-3-2h-1v2l1 1s0 2 1 2c0 1 0 1-1 2l-4-4-2-2c-1 0-1 0-1-1-1 0-1-1-2-1l-1-1h0l1-1 1-2-1-1h1c1 1 2 2 3 2 0-2-2-5-3-6 0-1-1-3-2-3l-6-8-18-24z" class="p"></path><path d="M407 131l6 8s1 1 1 2l-1-1h-2c-1-1-2-4-3-5-1-2-1-3-1-4z" class="r"></path><path d="M411 140h2l1 2h0 0l2 2c1 1 2 3 2 4-2 0-2-1-3-2h-1v2-1c-1-3-1-4-3-7z" class="c"></path><path d="M407 141c2 3 4 6 7 8h1s0 2 1 2c0 1 0 1-1 2l-4-4-2-2c-1 0-1 0-1-1-1 0-1-1-2-1l-1-1h0l1-1 1-2z" class="D"></path><path d="M382 92h113l3 9 1-1 1 2 1 4 1 3h-1v1h-1l-5 3-1 2c-2 0-3 2-4 3l-2-1c-2 2-4 3-7 5v-1l-1 1s-1 0-1 1h-1c0 1 0 2-1 2 0 1-2 1-2 2l-1 1s-1 1-1 2l-3 3-1 1c0 1-1 2-2 3h0 0v-1-1h0-2v-1 1h0c1 1 1 2 1 3h0l1 1c-1 1-2 3-3 5l-1 3h-1v-1h-1c-1-1-1-2-2-2h-1v-2h-1l1-2v-1-2c-1-1-1-1-1-2-1 0-1-1-1-2h-1c0-1 1-3 1-4h0c-1 0-2-1-2-1l-1-1c-1 0-1 1-2 1v1h-1c1-1 1-2 1-3h-1v-1c-1 0-1 0-2-1 0 0-1-1-2-1h-1-1c0-1 0-2-1-3h-1l-1 2v2c-1 1-2 1-4 1l5 1v1c-1 2-1 3-2 4v3h0v1 2 1 1h0l-1-1h-1l1-1v-1c-1-2 0-2-1-4 0 0-1 0-1-1h0l-1 2h-1c0 2 0 3 1 5l-1-1-1 1v3h0c-1 0-2 1-3 1h0-1c1 1 1 3 1 4h-2l-1-3h-1c0-1-1-1-1-2l-1 2c0 1-1 2-2 3v1c1 1 2 3 2 5-1 0-2 1-2 2h-2v-1l-2-3-1-2c0-1-1-3-2-4l-2-2h0 0l-1-2 1 1c0-1-1-2-1-2l-6-8-7-8c-2-2-4-5-5-7l-9-11-2-2c0-1 0-2 1-2l-1-1-1-1-2-3c-1-2-2-3-3-4h4z" class="t"></path><path d="M499 100l1 2c0 1-1 1-1 2l-1-3 1-1z" class="v"></path><path d="M501 106l1 3h-1v1h-1v-3l1-1z" class="M"></path><path d="M500 102l1 4-1 1-1-3c0-1 1-1 1-2z" class="I"></path><path d="M385 99l1-1h6 12-1-1c-1 1-3 1-5 1h0-11-1z" class="o"></path><path d="M477 106h1c0 2 0 4-1 6h0c-1 3-4 6-6 8-1 0-1 0-2 1v-1c3-2 5-4 6-7v-1l1-1c0-2 1-3 1-4v-1z" class="g"></path><path d="M468 98l-1-1c2 0 8 1 9 1 1 2 2 5 2 8h-1l-1-4c-2-2-5-3-8-4z" class="K"></path><path d="M421 115c5-1 8-6 13-7 3 0 7 2 10 4l-3 1c0-1-1-1-1-2-1-1-3-2-4-2s-3 0-4 1c-3 1-6 4-8 5-1 0-2 1-2 1l-1-1z" class="L"></path><path d="M452 97h11v1h0c0 1 0 1-1 2-1 0-4-1-5 0-2-1-3-1-4-1s-1 0-2-1c1 0 1 0 1-1z" class="G"></path><path d="M458 119h0c1-1 1-2 2-3h0c-1 4-4 6-6 8h0 3l-2 2h1l-2 2-1-1c-1 0-1 1-2 1v1h-1c1-1 1-2 1-3h-1v-1c1 0 2-1 2-2 2-1 4-2 6-4z" class="R"></path><path d="M468 98c3 1 6 2 8 4l1 4v1l-1-2h0c-4-2-6-3-8-7z" class="C"></path><path d="M448 105c1 0 1 1 2 1l1 1h0v3c0 1 1 2 2 3 1 0 1 0 2 1v2h-1c-1 0-1 0-1-1h0c-1-1-2-1-2-2-1-2-3-3-4-3 0-1 0-2 1-3h1c0-1-1-1-1-2z" class="Z"></path><path d="M414 98l38-1c0 1 0 1-1 1h-3c-1 1-2 1-3 1h-10v1l-1-1v-1h-11-9z" class="Y"></path><path d="M403 117l1-1c1 2 2 3 4 4l-3-6h1c1 1 1 3 2 4 1 2 2 3 4 4 0-2 0-3 2-5l1 1c-1 1-1 2-1 3s0 2 1 2c-4 1-6-1-9-3-1 0-2-2-3-3z" class="J"></path><path d="M446 116l1 1v1c-1 1-1 2-1 3 1 1 2 2 3 2s1 0 1-1v-1c-1-1-1-1-1-2-1-1-1-2-1-3v-1l1-1c1 1 1 2 1 3s1 1 2 2v2h0 2 1c0-1 0-1 1-1v-1h2c-2 2-4 3-6 4 0 1-1 2-2 2s-1 0-2-1c0 0-1-1-2-1 0-1-1-1-1-2v-2c1-1 1-1 1-3z" class="V"></path><path d="M385 99h1 11 0l-1 1v1c-2 0-4 0-6 1v1c-1 0-2 1-2 1h-1-1v1l-2-2c0-1 0-2 1-2l-1-1 1-1z" class="F"></path><path d="M385 99h1l3 3h1v1c-1 0-2 1-2 1h-1-1v1l-2-2c0-1 0-2 1-2l-1-1 1-1z" class="Z"></path><path d="M385 101c1 1 2 2 2 3h-1v1l-2-2c0-1 0-2 1-2z" class="B"></path><path d="M444 112l1 1c0 2 1 2 1 3 0 2 0 2-1 3v2c0 1 1 1 1 2h-1-1c0-1 0-2-1-3h-1l-1 2h-1l1-1c-1-1-2-1-3-3h1c1-2 3-2 4-3-1-1-1-2-2-2l3-1z" class="F"></path><path d="M445 113c0 2 1 2 1 3 0 2 0 2-1 3h-1c-1 0-1-2-1-2 0-2 1-3 2-4z" class="X"></path><path d="M441 121c0-2 0-3 1-4h1s0 2 1 2h1v2c0 1 1 1 1 2h-1-1c0-1 0-2-1-3h-1l-1 2h-1l1-1z" class="g"></path><path d="M434 99l1 1v-1h10c1 0 2 0 3-1h3c1 1 1 1 2 1s2 0 4 1h-2c-2 0-3 1-4 2-1 0-1 0-2 1v-2c1-1 1-1 3-1v-1c-3 0-5 1-8 1h-4-2-2s1 1 1 2h2 0 2v1l2 1h0c1 0 2 0 3 1h1 1c0 1 1 1 1 2h-1c-1 1-1 2-1 3l-3-3h0c-1-1-3-1-4-2-1 0-1-1-2-1l-1-1-2-2s0 1-1 1h0-1c-1 0-1 0-1-1l1-1v-1h1z" class="X"></path><path d="M488 117l3-3c0-1 0-4-1-5l-1 1h0c-1-1-1-5-1-6 0-2 0-3-1-4v-1c-2 0-3 0-5-1h0c-1 0-1 0-2-1 1-1 6 1 7 0 1 0 2 0 2-1 2 1 3 7 4 9 0 1 1 2 1 4l1 4-1 2c-2 0-3 2-4 3l-2-1z" class="H"></path><path d="M492 109h1l1 2v2l-2 1h0-1l1-1v-4z" class="N"></path><path d="M402 98c1 1 2 2 2 3v1h1l1 1v1h0l-1 1c-1 1-1 1-2 1-1 1-2 1-3 1l-1 1c-2 1-3 2-4 3h-1c-2-3-5-5-6-7 0 0 1-1 2-1v-1c2-1 4-1 6-1v-1l1-1c2 0 4 0 5-1z" class="t"></path><path d="M402 98c1 1 2 2 2 3v1h1l1 1v1h0l-1 1c-1 1-1 1-2 1v-1c0-1 1-2 0-4-1-1-5 0-7 0h0v-1l1-1c2 0 4 0 5-1z" class="V"></path><path d="M390 103c1 2 4 6 5 6h1c1-1 3-2 5-3l2-1v1c-1 1-2 1-3 1l-1 1c-2 1-3 2-4 3h-1c-2-3-5-5-6-7 0 0 1-1 2-1z" class="X"></path><path d="M404 98c3 0 7-1 10 0h9 11v1h-1v1l-1 1c0 1 0 1 1 1h1 0c1 0 1-1 1-1l2 2c-2 0-3 1-4 0h-5-8c-3 0-5 0-7 1h-3-2v1h-1-2l1-1h0v-1l-1-1h-1v-1c0-1-1-2-2-3h1 1z" class="F"></path><path d="M423 98h11v1h-1-2c-2 0-5 1-7 0h-9c3-1 6 1 8-1z" class="Q"></path><path d="M404 98c3 0 7-1 10 0h9c-2 2-5 0-8 1h-2v2c-1 0-1 0-2-1v-1h-3-1-2c-1 1 0 2-1 2 0-1-1-2-2-3h1 1z" class="J"></path><path d="M422 116s1-1 2-1c2-1 5-4 8-5 1-1 3-1 4-1s3 1 4 2c0 1 1 1 1 2 1 0 1 1 2 2-1 1-3 1-4 3h-1c1 2 2 2 3 3l-1 1h1v2c-1 1-2 1-4 1 0 0-1-1-2-1-1-1-2-2-2-3v-1h-1c0 2 1 3 1 4v1l-1-1c-2 0-3 0-5-1v-1c-2-1-7-3-7-5l2-1z" class="s"></path><path d="M427 122h0 1l-1-1c0-2 1-4 1-5 0 2 1 4 2 5v1c0 1 1 2 2 2-2 0-3 0-5-1v-1z" class="n"></path><path d="M440 111c0 1 1 1 1 2 1 0 1 1 2 2-1 1-3 1-4 3h-1c1 2 2 2 3 3l-1 1h1v2c-1 1-2 1-4 1 0 0-1-1-2-1-1-1-2-2-2-3v-1h-1c0 2 1 3 1 4v1l-1-1c-1 0-2-1-2-2s1-1 1-2h0l1-1c0-1 1-3 2-4l1-1 5-3z" class="F"></path><path d="M440 122c-1 0-2 0-3-1 0-2 0-2 1-4v1c1 2 2 2 3 3l-1 1z" class="L"></path><path d="M440 111c0 1 1 1 1 2 1 0 1 1 2 2-1 1-3 1-4 3h-1v-1l2-2h1v-1h-1c-1-1-1-1-2-1 0 1-1 1-2 1h-1l5-3z" class="q"></path><path d="M421 115l1 1-2 1c0 2 5 4 7 5v1c2 1 3 1 5 1l1 1v-1c0-1-1-2-1-4h1v1c0 1 1 2 2 3 1 0 2 1 2 1l5 1v1c-1 2-1 3-2 4v3h0v1 2 1 1h0l-1-1h-1l1-1v-1c-1-2 0-2-1-4 0 0-1 0-1-1h0l-1 2h-1c0 2 0 3 1 5l-1-1-1 1v3h0c-1 0-2 1-3 1h0-1c1 1 1 3 1 4h-2l-1-3h-1v-2-1c-1-1 0-2 0-3h-1l-1-2s-1-1-1-2h-1c-1-1-3-3-3-4-1-1-1-1-1-2v-1 1l-4-3v-1c-1 0-1-1-1-2s0-2 1-3h0c2 2 4 6 7 6l1-1c-1-1-3-3-3-5l-2-2h0 1c1 0 1 0 2-1z" class="I"></path><path d="M426 131c1 2 1 3 1 4v2h0-1l-1-2c1-1 1-2 1-4z" class="r"></path><path d="M419 127c2 1 4 3 5 4v-1h0l2 1c0 2 0 3-1 4 0 0-1-1-1-2h-1c-1-1-3-3-3-4-1-1-1-1-1-2z" class="E"></path><path d="M415 118h0c2 2 4 6 7 6l1-1c1 0 1 0 1 1 1 1 2 1 2 1l1 1v1c-1-1-2-1-3-1-1-1-2-1-3-1h-1c-1-1 0-1-1-1h-2v-1c-1 0-1 1-2 1v-1c-1 0-1-1-1-2s0-2 1-3z" class="h"></path><path d="M421 115l1 1-2 1c0 2 5 4 7 5v1c2 1 3 1 5 1l1 1v-1c0-1-1-2-1-4h1v1c0 1 1 2 2 3 1 0 2 1 2 1l5 1v1h-1c-1 0-1 1-2 1h-1c-1 0-1 1-2 1-1-1-2-1-4-1-2-1-3-1-5-1v-1l-1-1s-1 0-2-1c0-1 0-1-1-1-1-1-3-3-3-5l-2-2h0 1c1 0 1 0 2-1z" class="k"></path><path d="M427 123c2 1 3 1 5 1l1 1h0l-1 1c-1 1-3 0-4-1h0 1l-2-2z" class="S"></path><path d="M429 134c0-1-1-2-1-3-1-1-1-1-1-2 1 0 1 1 2 1s3 1 4 1c1 1 2 1 3 2h-1c0 2 0 3 1 5l-1-1-1 1v3h0c-1 0-2 1-3 1h0-1c1 1 1 3 1 4h-2l-1-3h-1v-2-1c-1-1 0-2 0-3h0v-2h1v-2h0 0c0 1 0 1 1 1z" class="Q"></path><path d="M431 140l1-2c1 1 1 2 2 3h0c-1 0-2 1-3 1h0-1v-2h1z" class="U"></path><path d="M429 134h1 0 2c0 2-1 4-1 6h-1l-1-6z" class="i"></path><path d="M429 130c1 0 3 1 4 1 1 1 2 1 3 2h-1c0 2 0 3 1 5l-1-1-1 1v-3c-2-1-4-3-5-5h0z" class="U"></path><path d="M428 135v-2h0 0c0 1 0 1 1 1h0l1 6v2c1 1 1 3 1 4h-2l-1-3h-1v-2-1c-1-1 0-2 0-3h0v-2h1z" class="W"></path><path d="M427 135h1v8h-1v-2-1c-1-1 0-2 0-3h0v-2z" class="S"></path><path d="M457 124l8-5c-1 1-2 3-3 4v1c1-1 1-1 2-1l1 1h2c2-1 4-1 6-2 1-1 2-1 3-2s3-2 4-3v1l-3 3c-1 0-2 1-2 1h0c1 0 2 0 2-1 1 0 2 0 3-1 3-1 4-4 6-6v1l-1 2c-1 2-3 3-4 4l-1 1s-1 0-1 1h-1c0 1 0 2-1 2 0 1-2 1-2 2l-1 1s-1 1-1 2l-3 3-1 1c0 1-1 2-2 3h0 0v-1-1h0-2v-1 1h0c1 1 1 2 1 3h0l1 1c-1 1-2 3-3 5l-1 3h-1v-1h-1c-1-1-1-2-2-2h-1v-2h-1l1-2v-1-2c-1-1-1-1-1-2-1 0-1-1-1-2h-1c0-1 1-3 1-4h0c-1 0-2-1-2-1l2-2h-1l2-2z" class="U"></path><path d="M456 126l1 1-1 2h0 0c-1 0-2-1-2-1l2-2z" class="K"></path><path d="M466 134c1-1 1-1 1-3h-1v-1c1 0 2-1 2-1l1 1c0 1-1 2-1 3h1v-1l1 1-1 1c0 1-1 2-2 3h0 0v-1-1h0-2v-1h1z" class="R"></path><path d="M462 126h3 1c0 1 0 1-1 1h-1c-1 0-1 0-1 1-1 0-1 1-2 2v1 2 3h1c0 2 0 4-1 5-1-2 0-3-1-6 0-1 1-2 0-4h0c-1-1 1-3 2-5z" class="D"></path><path d="M460 127h0c1 0 1 0 2-1-1 2-3 4-2 5l-1 3c-1 1-1 2-1 3-1-1-1-1-1-2-1 0-1-1-1-2h-1c0-1 1-3 1-4h0c1 0 1 0 1 1h1v-1l1-2h1z" class="B"></path><path d="M460 127h0c1 0 1 0 2-1-1 2-3 4-2 5l-1 3c-1 1-1 2-1 3-1-1-1-1-1-2l1-1v-3l2-4z" class="E"></path><path d="M478 123c0 1 0 2-1 2 0 1-2 1-2 2l-1 1s-1 1-1 2l-3 3-1-1v1h-1c0-1 1-2 1-3s0-1 1-2h0c0-1 3-2 4-2 0-1 0-1 1-1 1-1 2-1 3-2z" class="D"></path><path d="M469 132l1-1c1-2 2-4 4-5l1 1-1 1s-1 1-1 2l-3 3-1-1z" class="E"></path><path d="M460 131h0c1 2 0 3 0 4 1 3 0 4 1 6 1-1 1-3 1-5 1-1 1-3 3-5 0 1 0 2 1 2v1h-1v1h0c1 1 1 2 1 3h0l1 1c-1 1-2 3-3 5l-1 3h-1v-1h-1c-1-1-1-2-2-2h-1v-2h-1l1-2v-1-2c0-1 0-2 1-3l1-3z" class="x"></path><path d="M458 144v-2h-1l1-2c0 1 1 2 2 2l1 2h0 2l-1 2h-1c-1-1-1-2-2-2h-1z" class="M"></path><path d="M466 138l1 1c-1 1-2 3-3 5l-1 3h-1v-1l1-2 3-6z" class="w"></path><path d="M386 105v-1h1 1c1 2 4 4 6 7h1c1 2 3 3 4 5l2 1h0l-2-5c2 1 3 3 4 5 1 1 2 3 3 3 3 2 5 4 9 3v1l4 3v-1 1c0 1 0 1 1 2 0 1 2 3 3 4h1c0 1 1 2 1 2l1 2h1c0 1-1 2 0 3v1 2c0-1-1-1-1-2l-1 2c0 1-1 2-2 3v1c1 1 2 3 2 5-1 0-2 1-2 2h-2v-1l-2-3-1-2c0-1-1-3-2-4l-2-2h0 0l-1-2 1 1c0-1-1-2-1-2l-6-8-7-8c-2-2-4-5-5-7l-9-11z" class="y"></path><path d="M416 133c-1-2-1-4-1-6h1v1 1c1 1 1 1 1 2h0-1v2z" class="a"></path><path d="M386 105v-1h1 1c1 2 4 4 6 7h1c1 2 3 3 4 5h0c-2-1-3-2-5-4l-1-1-4-4c1 2 5 7 6 8l1 1h1c1 2 3 5 3 7-2-2-4-5-5-7l-9-11z" class="I"></path><path d="M419 127v-1 1c0 1 0 1 1 2 0 1 2 3 3 4h1c0 1 1 2 1 2l1 2h1c0 1-1 2 0 3v1 2c0-1-1-1-1-2l-3-2h-2c-1 0-2-1-2-2l-1 1v1h-1c-1 1 1 2 0 2l-2-1v1h-1c0-1-1-2-1-2h3v-1h1v-1-1h-1c-1 0-1 0-1-1h0l1 1 1-1c0-1-1-2-1-2v-2h1 0c0-1 0-1-1-2v-1h1c1 0 1 0 2-1z" class="D"></path><path d="M422 136l1 2h-2c-1-1-1-1-1-2h2z" class="M"></path><path d="M422 136v-2c1 0 1 1 2 1 1 1 1 2 2 2h1c0 1-1 2 0 3v1 2c0-1-1-1-1-2l-3-2v-1l-1-2z" class="f"></path><path d="M414 141h1v-1l2 1c1 0-1-1 0-2h1v-1l1-1c0 1 1 2 2 2h2l3 2-1 2c0 1-1 2-2 3v1c1 1 2 3 2 5-1 0-2 1-2 2h-2v-1l-2-3-1-2c0-1-1-3-2-4l-2-2h0 0l-1-2 1 1z" class="E"></path><path d="M421 143v-1-1l2 2 1-1 1 1c0 1-1 2-2 3v1l-2-4z" class="n"></path><path d="M414 142h1c1 0 2 1 2 1 1 1 1 2 2 2 0 1 1 3 1 4l-1 1-1-2c0-1-1-3-2-4l-2-2h0z" class="e"></path><path d="M421 143l2 4c1 1 2 3 2 5-1 0-2 1-2 2h-2v-1l1-1c1-1 0-2 0-3s-2-4-2-5l1-1z" class="M"></path><path d="M356 91l1-1c1-2 2-5 2-7 0-3-2-4-4-6 2 1 4 2 5 3s1 2 1 4v3c0 1 0 2-1 3 1 0 2 0 2 1l2-1h0c1 1 1 1 1 2-1 0-1 0-1 1s0 1-1 2l1 2v4h-2c0 1-1 2-1 3v3 1c0 2-1 3-1 4-1 1-1 2-1 3 0 2-1 5-1 6s1 2 1 3c-1 0-2-1-2 0h-1l1 3 1 5v3c0 1 1 3 1 4v1h-1v1h-1l-2-8v3c1 6 0 12 0 18h0v16l-2-1v2-1h-3v2l-1-1c-2 2-1 4-2 6v1h0l-1-1-1 1c1 0 1 0 2 1h0l1 1v1l-2-1h-2c-1 0-2-1-3-1l-1 1v4h-1c1 1 3 2 4 2l5 1c0 1 0 1-1 2h0c0-1 0 0-1-1 0 1-1 1-2 2 0 0-1 0-1 1h-2s-1 0-2 1h-2c0 1 1 1 1 2h1c-2 1-3 2-4 3 1 0 2 0 3-1h0l-1 2 1 3 2-1 6-2c1 1 1 1 1 2h0l1 1c1 0 3-1 4-1v2l-1 1h3l1 1h0v1h-7v1h-1c-2 0-5 1-7 1-7 2-18 5-21 11-1 1-2 3-3 4l-7 3c-5 4-7 7-10 12v1h-36c-2 1-3 3-4 5v1c2 1 2 1 3 2l-3 3v3l1 4v1l2 6h3 0l-2 1c-3 1-5 2-8 4v-2-8-26-7c-1-4 0-8 0-12v-15-50c-4-1-8-1-12-1-8-1-16-1-25 0-13 0-24 4-36 11-6 3-11 6-15 11-1 1-3 1-5 0-1 0 0-1-2-1l-3 3c-1 3-2 5-4 7v1c-2 1-3 1-4 1s-2 1-3 2l-3 2c1 1 1 2 1 3-2 1-3 1-4 0l-1 1c1 1 4 1 6 1-3 3-5 6-8 10-1 2-3 5-4 8-6 11-10 22-13 34l-3 11v6l-1 1c-6-1-12 0-18-1l-3-1c-1-1-1-1-2-1l-2-1c-1 0-3-1-5 0h-3c-1 2 0 2 0 3-3 1-6 0-9 0h-7l-14-2v-4c1-1 1-2 1-3l1-1c1-1 1-4 1-6l4-15 6-25 6-24 10-41 5-22 4-15c0-2 1-5 2-7h103 67 7 17l68-1c1 0 1 0 1-1z" class="s"></path><path d="M108 182c1 2 1 2 1 4-1 0-1-1-1-1v-3z" class="N"></path><path d="M158 118l2-1-1 3h-2l1-2z" class="C"></path><path d="M129 170h0c0 2 0 2-1 3 0 0-1-1-2-1 1-1 2-1 3-2z" class="q"></path><path d="M185 122v-1-1l2 1h0c-1 1-1 2-1 2v1l-1-2z" class="F"></path><path d="M237 115l1 1c0 1 0 2-1 2h-1c0-2 0-2 1-3z" class="C"></path><path d="M254 119v1c1 0 1 0 1-1 1 0 1-1 1-1l1-1c0 3 0 3-1 5l-2-1h0v-2z" class="V"></path><path d="M157 163l1-1 1 1v2h-1v3h0l-1-5z" class="J"></path><path d="M102 102c0 1 1 1 1 2s-1 2-2 3v-1-1c-1-1-1-1-1-2l1-1h1z" class="H"></path><path d="M127 121l-1 1h-1v-2c0-1-1-1 0-2h1l1 3z" class="L"></path><path d="M98 102h0c0-1 1-2 2-3 1 1 2 2 2 3h-1-1l-2 1v-1z" class="K"></path><path d="M124 177l1-1 2-1c0 1 1 2 1 3v1 1l-4-3z" class="F"></path><path d="M98 103l2-1h1l-1 1c0 1 0 1 1 2v1 1c-2-1-2-2-3-3v-1z" class="G"></path><path d="M170 117h0c1 0 1 0 2 1l1 1c1 1 2 1 2 2h0-1c-2-1-1-1-3 0 0-1-1-2-1-4z" class="P"></path><path d="M140 187h1c0-2 1-3 2-3 1 1 1 2 1 3-2 1-3 1-4 0z" class="N"></path><path d="M136 112c2 0 6 1 8 0v1h-2 0-1v2h-1v-1h-1v1c-1-1-1-2-2-2l-1-1z" class="F"></path><path d="M185 127h-2l-1-1v-2l1-1 1 1c1 1 1 2 2 3h-1z" class="b"></path><path d="M157 115v-2h3c0 1-1 2-1 3v1h1l-2 1c-1-1-1-2-1-3z" class="V"></path><path d="M128 118h1c1 1 0 3 0 5h-1v-2h-1l-1-3h2z" class="P"></path><path d="M128 118h0v1 2h-1l-1-3h2z" class="F"></path><path d="M107 161h1c1 3 0 5 0 8v2c-1-1-1-2-1-4h0v-2-2-2z" class="P"></path><path d="M121 200h3c0 2 1 3 1 5h-3l-1-2c1-1 1-2 1-2l-1-1z" class="H"></path><path d="M152 167l1 2c0 1-1 1-1 2s0 1-1 2c-1-1-1-1-1-2v-1c1-2 1-2 2-3z" class="G"></path><path d="M131 113c1 0 1 0 2-1h3l1 1c1 0 1 1 2 2h-2c-1-1-1-1-2-1v1h-1l-1-1v-1h-2z" class="j"></path><path d="M176 100l2 1h-1l-2 2c1 1 1 1 2 1h0c0 1-1 1-1 1h-2l-1-1c1-2 0-2 1-3l2-1z" class="N"></path><path d="M141 133h1c0 1 1 2 1 4 0 1 0 1-1 2l-2-5c0-1 0-1 1-1zm-24 92c1 0 2-1 3-1l-2 7h-1v-3h-1c0-1 1-2 1-3z" class="H"></path><path d="M328 183h0c2 1 5 1 6 2l-1 1h4l-1 1c-3-2-6 1-9 1 1-1 1-1 3-2v-1c0-1-1-1-1-1l-1-1z" class="X"></path><path d="M90 167c2 3 2 9 2 12l-1-1c0-2 0-4-1-6 0-1-1-2-1-3v-1h1v-1z" class="F"></path><path d="M97 135h0 0c0 2-1 3-1 4l1 1c-1 0-3 1-4 2h0 0c0-3 2-6 4-7z" class="X"></path><path d="M121 218c0 2-1 4-1 6-1 0-2 1-3 1 0-1 1-2 1-3v-3l1-1c0 1 1 1 1 1l1-1z" class="L"></path><path d="M159 165h1c1-1 1-2 1-3l1-1 1 1c-1 1-1 2-1 2 0 2-2 3-3 4h1l-3 3 1-3h0v-3h1z" class="B"></path><path d="M131 138l2-2 4 4-2 2v-2h0l-1-1-1 2h-1c0-1 0-1-1-3z" class="T"></path><path d="M311 113c2 1 5 1 7 3l-1 1c-2-1-2-3-5-2h0l1 7v2h-1v-5l-1-1c0-1 0-2-1-3l1-1v-1z" class="q"></path><path d="M98 102v1 1c1 1 1 2 3 3-2 0-2 0-3-1-1 1-1 1-1 2l1 1c0 2-1 3-2 4 0-1 0-1-1-2l3-9z" class="i"></path><path d="M157 125c-1 0-2-1-3-2v-1c1-1 2-2 3-2h2c0 1 1 1 0 2v1c-1 0-1 1-1 1v1h-1z" class="H"></path><path d="M255 126c-1-1-2-1-3-2 0-2 1-4 2-6 0-1 0-2 1-3h1v1c-1 0-1 1-1 1l-1 2v2h0l2 1c-1 1-1 3-1 4zm-146 50l1 1v2c1 1 2 1 2 3h1 0l1-1c1 0 2 0 3 1-2 1-4 1-6 1v-1-1h-2-1v-1h0c1-1 1-2 1-4z" class="X"></path><path d="M163 162c0 1 1 2 1 3v3h0c1 0 2 1 3 1-1 1-3 1-5 0-1 0 0-1-2-1h-1c1-1 3-2 3-4 0 0 0-1 1-2z" class="S"></path><path d="M147 161c-1-2-2-4-4-6-1-1-2-2-4-3h-1v-2h1c2 1 4 3 6 5 0 1 0 1 1 1 1 1 2 3 3 4l-2 1z" class="T"></path><path d="M166 131h1l6 2-1 1c1 0 2 1 2 2h0 0-2-1c-1-1-1-1-3-1 0 0-1-1-2-1v-1l-1 1v-1l1-2z" class="P"></path><path d="M167 131l6 2-1 1h-2c-1-1-1-1-2-1h-1v-1-1z" class="C"></path><path d="M300 162l3 3c1 1 1 2 2 4 0-3 0-7-1-9l-1-5v-1c1 1 1 2 2 4h0v3c0 3 1 7 0 9l-1 1c-1 0-1-1-1-1l-1-2c0-2-1-4-2-6z" class="N"></path><path d="M193 121h1c0-1 1-2 2-2s1 1 2 1h0 0l1 1h-1v1c0 2 2 2 2 4h-1c0-1-1-2-2-2s-2-1-2 0h-1l-1 1c-1-2 0-2 0-4h0z" class="Z"></path><path d="M257 117h2c1 0 1 0 2 1-1 1-2 2-3 2-1 2-2 4-2 5-1 3-1 7-1 10h0c-1-3-1-6 0-9 0-1 0-3 1-4 1-2 1-2 1-5z" class="O"></path><path d="M165 123c0-1-1-2 0-3h1v1c0 1 0 1 1 2h1c0-1 0-2-1-3 1-1 1-1 1-2 1 2 1 3 1 4s-1 1-1 2h1s1 0 1-1c0 0 1 0 1-1 1 1 1 1 1 2-2 1-3 2-5 2v-1l-1 1 2 1c-1 0-2 0-3-1l1-2-1-1z" class="N"></path><path d="M252 110h1c1-1 2-1 3 0h-1c-1 1-1 1-1 2l-2 2h0c0 1 0 2-1 2 0 1 0 1-1 2s-1 1-1 2h-1l-1-1 1-1s1-2 1-3h0l1-1 2-4z" class="Z"></path><path d="M116 203h0 1c-1-1-2-1-3-2h0c2-1 5-1 7-1l1 1s0 1-1 2l1 2c-2 0-4-1-6-2z" class="F"></path><path d="M154 153h2c1 1 3 1 5 1l1-1h0l1 2h2 1c0 1 0 1 1 1 0 1 1 1 1 2h-1-1c-2-1-9-4-12-4h-3 1c1-1 1-1 2-1z" class="b"></path><path d="M102 140c1-2 2-3 2-4s1-2 1-2h1c0 1-1 2-1 3l1 2h-1c-1 3 0 4-2 6h-2-1l1-2c1-1 1-2 1-3h0z" class="P"></path><path d="M149 160c1 2 2 4 3 7h0c-1 1-1 1-2 3v1c-2-3-2-7-3-10l2-1zm41-16l3 3c-2 2-4 3-7 4l-1-1c-1-1-3-2-4-3h0 1c1 1 3 2 4 1 1 0 1-1 2-1l-1-1h0 2 0l1-2z" class="e"></path><path d="M188 147l-1-1h0 2 0l2 1v1h-3v-1h0 0z" class="X"></path><path d="M157 163c0-1 0-2-1-3 0-1-1-1-1-1l-2-2h0 2c1 0 1 0 1 1 1 0 1 0 1-1 1 1 3 1 4 2 0 1 0 2 1 2l-1 1c0 1 0 2-1 3h-1v-2l-1-1-1 1z" class="Q"></path><path d="M149 149s1 1 1 0c1 1 1 1 2 1 1 1 1 2 3 2h1c2 0 4 1 6 1h0 0l-1 1c-2 0-4 0-5-1h-2c-1 0-1 0-2 1h-1c-2-1-3-2-5-3v-1c1 0 1 1 2 1h1c0-1-1-1 0-2z" class="F"></path><path d="M149 149s1 1 1 0c1 3 2 3 4 4-1 0-1 0-2 1h-1c-2-1-3-2-5-3v-1c1 0 1 1 2 1h1c0-1-1-1 0-2z" class="Y"></path><path d="M123 167c2-1 3-1 4-1 1 1 2 1 3 2-1 1-1 2-1 2h0c-1 1-2 1-3 2l-2-1c1 0 1-1 1-1v-1c-1 1-1 1-3 1v-2c1 0 0 0 0-1h1z" class="F"></path><path d="M125 169h0 1c1 0 2 0 3 1-1 1-2 1-3 2l-2-1c1 0 1-1 1-1v-1z" class="b"></path><path d="M122 167h1 2 3v1 1h-2-1 0c-1 1-1 1-3 1v-2c1 0 0 0 0-1z" class="N"></path><path d="M78 247c3 0 4 1 7 0h1c2 0 5 1 7 1h1v1c-5 0-13 1-18 0l-3-1h4v-1h1z" class="E"></path><path d="M122 184h1c1 1 2 1 3 2 0 0 1 1 2 1h1c1 0 2 1 2 1v1c-2-1-7-1-9 0h-1l-1-1v-1c1-2 2-1 1-3h1z" class="X"></path><path d="M260 103l-1 1c2 1 3 1 4 1l1-1h1 1l1-1v1c-1 1-3 2-4 4-1 0 0-1-1 0l-4 4v-1h0v-2-1c0-1 0-1 1-2l-2-1v-1c0-1 2-1 3-1z" class="V"></path><path d="M107 184l1 1s0 1 1 1 3 1 4 1c0 0 0-1 1-1s1 0 2 1c0 1 1 1 0 2-2 0-3 1-5 0v1 3 2h0l-4-11z" class="C"></path><path d="M123 195l1-2 1 1c-1 2-1 4-1 6h-3c-2 0-5 0-7 1h0c1 1 2 1 3 2h-1 0c-3-2-6-3-9-7h1c1 2 2 3 4 3h1c-1-1-2-3-2-4h0l3 3c1 1 1 1 3 2v-1h5v-1c0-1 1-2 1-3h0z" class="j"></path><path d="M138 160c0 1 0 2 2 3h1v3h-1c-1 0-1-1-2-1v1h0l-1 1c-1 0-2 0-4-1h0c-2 0-2-1-3-3l1-1v1l1 1 1 1 1-2c0-1 0-1 1-2 0 0 0 1 1 1l2-1v-1z" class="V"></path><path d="M102 140h0c0 1 0 2-1 3l-1 2h1 2 3c2 0 4 2 5 3v1h0c-1 0-2-2-2-2h-2l-2-1c-1 2-2 9-2 12v3s1 0 1 1v4 1c-1-1-1-2-1-4v-1-1c-2-4 0-10 0-14h0l-2 1h-1s-1 1-1 2l1-3c0-1 0-1-1-2 1-1 2-4 3-5z" class="L"></path><path d="M127 158v-1c3-2 5-4 8-3 3 0 5 2 6 4 2 3 2 5 1 8h-1v-3h-1c-2-1-2-2-2-3v-1c0-1 1 0 2-1-2-1-3-3-5-3-3 0-4 0-6 2l-1 3-1-2z" class="u"></path><path d="M140 158c0 1 1 1 1 2v3h-1c-2-1-2-2-2-3v-1c0-1 1 0 2-1zm171-40l1 1v5h1v-2c0 2 1 6 0 8v2l3 8 5 17v2h0l-1-1c-2-6-4-12-5-18-1-3-3-6-3-9-1-4-1-9-1-13z" class="H"></path><path d="M184 141h1c1 1 4 3 4 5h-2 0l1 1c-1 0-1 1-2 1-1 1-3 0-4-1h-1 0c-1-1-3-1-4-2v-1h1l1 1s1 0 1 1c1-1 1-2 1-2l3-3z" class="Z"></path><path d="M158 124c1 0 2 1 3 0v-4l1-1c0 2 0 4 1 5v2h1c0-1 0-2 1-3l1 1-1 2c1 1 2 1 3 1 3 1 6 3 9 4 1 0 1 0 2 1h2c0 1 1 1 1 1h1v1c-1 0-2 0-3-1-3-1-7-3-10-4l-6-2c-2 0-3 0-4-1-1 0-2-1-2-1h-1 1v-1z" class="C"></path><path d="M194 132h1l1 1h1c1 1 1 1 2 1h1c0 1 1 1 2 1h1c1 0 2 0 3 1h3v-1h2 0l1 1h1l1 1h-2c-1-1-1-1-2-1 0 1-1 2-1 2h0s0-1-1-1c-2 0-3 0-5 1-1 0-2-1-4 0-1 0-3-1-5-1h-1l-1-1h-1l-2-2v-1c2 1 3 2 5 3h0 1 1l1 1c1 0 2 0 3-1-1 0-1-1-2-1-2-1-3-2-4-3h0z" class="P"></path><path d="M173 133c1 1 3 1 4 2 1 0 3 0 4 1 3 1 6 6 9 8l-1 2h0c0-2-3-4-4-5h-1l-3 3-1-4v-1l-1-1c0-1-2-2-3-3l-2 1h0c0-1-1-2-2-2l1-1z" class="O"></path><path d="M179 138l1-1c1 0 2 0 2 1h1v1c1 0 1 1 2 2h-1l-3 3-1-4v-1l-1-1z" class="P"></path><path d="M180 139h3l-2 2-1-1v-1z" class="V"></path><path d="M183 139c1 0 1 1 2 2h-1-3l2-2h0z" class="X"></path><path d="M180 140l1 1h3l-3 3-1-4z" class="P"></path><path d="M147 139c0-3 1-5 3-7 3 3 6 5 9 8-2 0-3 1-4 0-2 0-3 1-4 1v-1l-1-1v-1l-3 1z" class="O"></path><path d="M152 137h1s1 0 1 1h-1l-1 1-1-1 1-1zm-4-15c1 0 1 0 2 1-1 1-2 1-3 1l-10 2c-8 1-14 5-19 12l-5 9-1 1h0c0-1 0-1 1-2 1-4 4-8 6-11l3-3c3-3 7-5 11-7 2 0 4-1 7-1l8-2z" class="F"></path><path d="M335 197c1 0 2 0 3-1h0l-1 2c-1 1-2 1-3 2h0l-2 2h2 0c0 1-1 2-1 2 1 1 2 1 3 1h0v1h-1c-1 0-2 0-2 1l-1-1h-4 0l-4 1h0c-2 1-3 2-5 2 0 0-1 0-1 1h0-1v-1s1 0 1-1l4-1c1 0 3-1 5-1 0-1 1-1 2-1l1-1c1-1 1-2 2-3 0-1 0-2 1-2 0-1 1-2 2-2z" class="N"></path><path d="M334 200c1-1 2-1 3-2l1 3 2-1 6-2c1 1 1 1 1 2l-11 5c-1 0-2 0-3-1 0 0 1-1 1-2h0-2l2-2h0z" class="V"></path><path d="M334 200c1-1 2-1 3-2l1 3c-2 0-2 0-4-1z" class="C"></path><path d="M253 107c1-1 2-2 2-3 1-1 3-2 4-4h0c1-1 3 0 4-1 1 0 3 0 4 1h7-3c-1 1-3 0-5 1h-2c-2 0-2 1-4 2-1 0-3 0-3 1v1l2 1c-1 1-1 1-1 2l-2 2c-1-1-2-1-3 0h-1v-1c1-1 1-1 1-2z" class="P"></path><path d="M253 107c1 0 2 1 2 2h0 1c1-2 0-2 0-3l1-1 2 1c-1 1-1 1-1 2l-2 2c-1-1-2-1-3 0h-1v-1c1-1 1-1 1-2z" class="H"></path><path d="M106 134c1-1 1-3 2-4s2-2 2-4h0c1-2 1-5 1-7v-4c0-1 2-1 3-1l1 1c0 1 0 1 1 1h0l-1-2 1-1 1 1v1h1v3 2h-1v-1h-1v3h-1v-4l-1-1c-1 2-1 5-1 7h-1c0-1 1-2 0-3v-2-1h-1c0 1 0 2 1 4 0 1-1 2-1 4h0c0 1 0 1-1 2s-2 3-3 4c0 2-1 3-2 5 0-1 1-2 1-3z" class="X"></path><path d="M96 113c1-1 2-2 2-4l-1-1c0-1 0-1 1-2 1 1 1 1 3 1v-1 1c-2 7-2 14-5 20-1-2 1-7 1-9l-1-1v-4z" class="e"></path><path d="M128 179c1 0 3 1 4 1h0c2 1 3 0 4 0l1-1h0l1-1h0l1-1 1-1v-1-3c0-1 1-1 2-1h1c1 2 1 4 0 6s-4 4-6 5c-3 1-6 0-8-2h-1 0v-1z" class="L"></path><path d="M99 145c1 1 1 1 1 2l-1 3h0c0 2-1 4-1 6-1 6 0 13 1 19 0 2 1 6 3 7l1 1s1 0 1 1h-1c0 1 0 2-1 3-6-14-8-27-3-42z" class="G"></path><path d="M122 167c0 1 1 1 0 1v2c2 0 2 0 3-1v1s0 1-1 1l2 1c1 0 2 1 2 1l-1 2-2 1-1 1s-1-1-2-1l-1-1c-2-2-2-3-3-5 2-1 2-2 4-3z" class="C"></path><path d="M125 169v1s0 1-1 1l-2 2-1-1c0-1 0-1 1-3v1c2 0 2 0 3-1z" class="q"></path><path d="M131 113h2v1c-1 0-1 0-2 1h-3c-2 0-3 1-5 0-1 0-1-1-2-1h0c0 1 0 2-1 3 2 1 2 1 3 3-1 1-2 1-3 1h0l-1 1c0-1 0-1-1-2v1h-1l-1 1v-3h1v1h1v-2-3-2h1c0 1 0 2 1 3v-1-2h11z" class="V"></path><path d="M120 121h-1c0-2 0-2 1-4 0 0 0 1 1 1 1 1 1 1 1 2 0 0-1 0-1 1h-1 0z" class="P"></path><path d="M128 160s0 1-1 2-5 0-6 0-1 1-2 1h-1c-1-3 0-5 1-7 0-1 0-2 1-3h2c2 0 2 1 3 1 1 1 1 1 1 2h0l-2 1h-1l-3 2v1c2 1 5 1 6 1 1-1 1-2 1-2v-1l1 2z" class="H"></path><path d="M122 153c2 0 2 1 3 1 1 1 1 1 1 2h0l-2 1h-1-2l-2 1h0c1-1 1-2 2-3 0 0 1 0 1-1v-1z" class="N"></path><path d="M259 194l2 2c0 1-1 2 0 3 0 1 0 2 1 3s1 1 2 3l1 1v2c-1 0-3-1-4 0v12 3c0 1 0 1-1 2 0-2 1-4 0-5-1-2 0-5 0-6v-18c-1-1-1-1-1-2z" class="X"></path><path d="M189 117v-7c-1-1-2-1-2-1h-1-3v-1h2 2 2l2 2h1l3 3c0 2-2 2-1 3 1 2 3 2 4 4h0 0c-1 0-1-1-2-1s-2 1-2 2h-1v-2l-2-1-2-1z" class="P"></path><path d="M189 117c1 0 1-1 2-1 0-1 1-1 2-1v2h-1l-1 1-2-1z" class="N"></path><path d="M193 115c0 2 0 2 1 3h1s1 0 1 1c-1 0-2 1-2 2h-1v-2l-2-1 1-1h1v-2z" class="F"></path><path d="M347 200l1 1c1 0 3-1 4-1v2l-1 1h3l1 1h0v1h-7v1h-1v-1c-3-1-10 2-13 3v-1c2 0 3-1 5-2 1 0 3 0 4-1h-1c-2 0-4 0-6 1h0l11-5h0z" class="k"></path><path d="M165 134v1c-1 0-3-1-4 0-1 0-1 0-1 1l3 3v1h0-1c0-1-1-1-1-2-2-1-4-3-5-5-1 0-2-1-1-2l1-1v-2h-1-1-1 0v-1h2 1l2 2 4 1h3l1 1-1 2v1z" class="H"></path><path d="M165 130l1 1-1 2v1h-3v-1h1c1-1 1-1 2-3z" class="j"></path><path d="M158 129l4 1c-1 0-1 1-2 1v1c-1 0-2-1-3-1 0-1 1-1 1-2z" class="X"></path><path d="M162 130h3c-1 2-1 2-2 3-1 0-2-1-3-1v-1c1 0 1-1 2-1z" class="C"></path><path d="M147 139l3-1v1l1 1v1c1 0 2-1 4-1 1 1 2 0 4 0 1 1 3 3 5 4 1 0 3 1 4 1-1 1-2 0-3 1l-8 3h0l7-4-5-4h0c-4 1-7 2-11 4v1c0 1 0 2 1 3-1 1 0 1 0 2h-1c-1 0-1-1-2-1v1h-1l1-1h1v-2c-1-2-1-4 0-7v-2z" class="u"></path><path d="M147 139l3-1v1l1 1v1c1 0 2-1 4-1-2 1-3 2-5 2l-1-1v1 1h-1c-1 0-1-1-1-2v-2z" class="P"></path><path d="M238 98l7 1c1 0 2-1 3 0l1 1h0 0-8c-1 0-1 1-1 2-1 0-1 1-1 1 0 1 0 2-1 3s-3 1-4 2v1c-1-1-2-1-2-1-8-2-18-2-25-6v-1l2 1v-2c3 1 4 3 6 3 1 0 1 1 2 1 0-1 1-2 2-2v-1c1 1 1 2 2 3l11 3h1c2-1 4-1 5-3 1-1 1-3 1-5h-1v-1z" class="e"></path><path d="M219 101c1 1 1 2 2 3h-4c0-1 1-2 2-2v-1z" class="C"></path><path d="M104 166c0 2 1 6 2 7v2h7v-1c-1-1-1-2-2-4v-1-1h-1v-2-2h0l1 1c1 6 4 10 7 15l3 3h1v1h-1 0l-4-4-3-4c-1 0-3 0-4 1l-1-1c0 2 0 3-1 4h0v1 1 3l-1-1h-1c-1-4-1-8-1-12-1-1-1-3-1-5v-1z" class="F"></path><path d="M108 180l-2-2v-1c1 0 2-1 3-1 0 2 0 3-1 4h0z" class="Z"></path><path d="M255 266h0l3-1 3-2c-1-3-2-7-3-11 0-2-1-4-1-6s2-5 2-6c0 0-1-1-1-2 1-1 2-1 3-2 2-1 4-1 6-1v1h-2l-2 2c-2 1-3 3-4 5v1c2 1 2 1 3 2l-3 3v3l1 4v1l2 6h3 0l-2 1c-3 1-5 2-8 4v-2z" class="u"></path><path d="M259 249c-1-2 0-4 0-6v1c2 1 2 1 3 2l-3 3z" class="V"></path><path d="M118 231c-3 6-3 14-6 20v-18c0-2 0-4-1-6v-6c0-5 4-8 8-11 1 3 1 5 2 8l-1 1s-1 0-1-1c0-2 0-3-1-5h0c-2 0-4 3-5 5-1 1-1 2-1 3 0 2 0 5 1 7v14l4-11h1zm89-129h-1c-4-2-9-2-14-3l1-1h26l1 1h-1l1 1h3l-3 1h-1v1c-1 0-2 1-2 2-1 0-1-1-2-1-2 0-3-2-6-3v2l-2-1v1z" class="h"></path><path d="M207 101s-2-1-3-2h4l1 1h0v2l-2-1z" class="j"></path><path d="M208 99c3 0 5 0 9 1h3 3l-3 1h-1v1c-1 0-2 1-2 2-1 0-1-1-2-1-2 0-3-2-6-3h0l-1-1z" class="H"></path><path d="M185 122l1 2v-1c1 1 2 1 2 1h2v1h0v2l1 1h0l3 4h0c1 1 2 2 4 3 1 0 1 1 2 1-1 1-2 1-3 1l-1-1h-1-1 0c-2-1-3-2-5-3v1c-1-1-2-2-2-3-1 0-1 0-2-1h0v-3h1c-1-1-1-2-2-3 1-1 1-1 1-2z" class="V"></path><path d="M127 141c1-2 2-2 3-3 0 1 0 1 1 1v-1c1 2 1 2 1 3h1l1-2 1 1h0v2h-1c0 1-1 3-2 4h0c1 1 1 1 1 2h-2c0-1-1-1-1-2h0l-1 1h0c-2 1-3 1-5 2-1 0-2 1-3 1 1 1 2 1 2 2h-1c-1 0-2 0-3-1h0c1-1 3-2 3-3 1 0 1-1 2-1v-1c-1-1-1-1 0-2v-1c1-1 2-2 3-2z" class="H"></path><path d="M127 141l1 3h-3-1v-1c1-1 2-2 3-2z" class="N"></path><path d="M111 190l1 2c1 0 2 0 2 1h1v1c1 0 1-1 2-1 3-2 8-1 11-2h1l1-1 2 2-1 1c-1-1-1-1-2-1-1 1-2 1-2 2l-1-1h-1v1l-1-1-1 2h0c0 1-1 2-1 3v1h-5v1c-2-1-2-1-3-2l-3-3v-2-3z" class="P"></path><path d="M123 195h-3c-1 0-1 0-2-1l1-1h2 1l1 1v1h0z" class="V"></path><path d="M115 196c0 1 1 1 1 1 1 0 2 1 3 1 0 0 1-1 2-1 0 0 0 1 1 1v1h-5v1c-2-1-2-1-3-2l1-2z" class="Z"></path><path d="M111 190l1 2c1 0 2 0 2 1h1l-1 1c0 1 0 1-1 2h2l-1 2-3-3v-2-3z" class="N"></path><path d="M95 111c1 1 1 1 1 2v4l1 1c0 2-2 7-1 9l-6 9-4 12c0 1 0 2-1 2-1-1 0-3 1-4 0-3 1-6 2-8 1-6 4-11 6-17 1-3 0-7 1-10z" class="K"></path><path d="M96 117l1 1c0 2-2 7-1 9l-6 9v-1c1-1 1-1 1-2 0-3 2-7 3-10 1-2 1-3 1-4v-1s0-1 1-1z" class="O"></path><path d="M148 146v-1c4-2 7-3 11-4h0l5 4-7 4h0c-2 1-4 0-5 1-1 0-1 0-2-1 0 1-1 0-1 0-1-1-1-2-1-3z" class="s"></path><path d="M148 146l3 3c1 0 1-1 2-1 1 1 2 1 4 1h0c-2 1-4 0-5 1-1 0-1 0-2-1 0 1-1 0-1 0-1-1-1-2-1-3z" class="V"></path><path d="M121 98l3 1h0c3 1 6 3 9 4s7 1 10 2l1 1c1 0 3 1 4 1s2-1 3-1l1-1h2c2 2 4 3 7 2h1 0l-1-1c1 0 1 0 1 1 2 0 3 1 4 0v-1h0l1 1c1 0 2 0 3-1h1c-1 2-3 2-5 2-3 1-7 1-9 0l-1 1 1 2c1 0 2 0 4 1v-1h6l5-3c-1 2-2 3-3 3h-1c-1 0-4 1-5 2h-2l-1-1v1h-3v2l-1-1c-1-1-2-1-3-1v-1h2l1-1h0c0-1-1-3-1-3-1-1-2-2-3-2-1 1-3 2-4 2l-2-1c-3-1-5-1-7-1-1 0-2-1-3 0l-1 1h0-1l-1-1c-2-4-8-6-12-7v-1zm113 10c1-1 3-1 4-2s1-2 1-3c0 0 0-1 1-1 0-1 0-2 1-2h8 0 0 3l-1 1c-3 2-7 4-9 8h0c-1 1-1 2-1 3 0 2 0 5-1 6l-1 1v-1c1-2 1-4 1-6h-1v-1c-2-1-4-1-5-2v-1z" class="G"></path><path d="M243 103v-1h3v1l-5 5c0-3 2-3 2-5h0z" class="N"></path><path d="M234 108c1-1 3-1 4-2s1-2 1-3c0 0 0-1 1-1 0-1 0-2 1-2h8 0 1l-1 1-3 2v-1h-3v1h-1c-2 2-2 4-5 5-1 0-1 0-1 1l-2-1z" class="P"></path><path d="M124 99h1c1-1 1-1 2-1 3 3 13 3 17 3h9c1 1 1 2 1 3v1h-2l-1 1c-1 0-2 1-3 1s-3-1-4-1l-1-1c-3-1-7-1-10-2s-6-3-9-4h0z" class="Z"></path><path d="M117 231l-4 11v-14c-1-2-1-5-1-7 0-1 0-2 1-3 1-2 3-5 5-5h0c1 2 1 3 1 5l-1 1v3c0 1-1 2-1 3s-1 2-1 3h1v3z" class="N"></path><path d="M260 225c1-1 1-1 1-2v-3-12c1-1 3 0 4 0h3 1 0s-1 1-2 1c0 1-1 0-1 0-1 1 0 1 0 1v8 2c-1 1-1 1-1 2h0 1 0c1 0 1 0 1-1l4-1h0c1 0 1-1 2-1 2 0 3-2 4-3 0 2-1 3-3 4-3 1-9 3-10 5-1 1-1 1-1 2v1 1 1c0 1-1 1-1 2l-1-1v-1c-1-2 0-4-1-5z" class="V"></path><path d="M161 159l5 2c3 0 5-3 8-4-3-2-5-4-8-6 1-1 3-3 5-3v-1c2 0 2 0 4 1 1 0 3 1 4 2l2 1s1 0 1 1-5 3-7 4c-2 2-4 4-7 6-1 1-3 1-4 3 0-1-1-2-1-3l-1-1c-1 0-1-1-1-2z" class="n"></path><path d="M171 151l-2-1v-1c1-1 3-1 5-1l2 1v1h0-2-2l-1 1z" class="N"></path><path d="M175 148c1 0 3 1 4 2l2 1s1 0 1 1c-1 1-3 1-5 2 0 0-1 1-2 1h0-1c-1 0-1 0-2-1-1 0-3-2-3-3h2l1-1h2 2 0v-1l-2-1h1z" class="P"></path><path d="M179 150l2 1s1 0 1 1c-1 1-3 1-5 2 0 0-1 1-2 1h0v-2-2h1 0l3-1z" class="N"></path><path d="M315 181l10 1h5v1c2 0 4 0 6 1h3c1 1 3 2 4 2l5 1c0 1 0 1-1 2h0c0-1 0 0-1-1 0 1-1 1-2 2 0 0-1 0-1 1h-2s-1 0-2 1h-2l-2 1c-4 1-6 5-10 7h0c3-2 5-5 8-7-3 0-6 0-9 1h0 0 0c2-2 6-2 9-2l2-2 1-3 1-1h-4l1-1c-1-1-4-1-6-2h0c-1 0-1 1-2 0-3-1-8-1-11-1v-1z" class="G"></path><path d="M335 190h0l3-3c2 0 3 1 5 1v1s-1 1-2 1c-2 1-5 1-8 1v1l2-2z" class="Z"></path><path d="M295 178h1l2-2 1-1v1c0 2-3 5-4 6h0v2h2l-1-1c0-1 3-4 3-5 1 0 1-1 1-2 1-1 1-1 1-2 1-1 1-2 1-2 0-1 0-1 1-1v1h2c0 1-1 2-1 3l-6 7 1 1-1 1h1 0v-1c3-1 6-1 9-2h7v1h0-7c-2 0-2 0-4 1h-1c-2 1-5 2-6 4v1h0c-1 2-2 5-3 6 0-2 1-5 2-6s1-2 1-3h0-2c0 1-1 2-1 2l-3-1c0-2 1-3 0-4v-2c-1 1-1 3-1 4v1l1 1c-1 1-1 1-2 1-1-1 0-2 0-3 0-2 0-3 1-5 0-2 1-3 1-5v-1l1-1 1 1c-1 1-1 2-1 4 1 0 2 0 3 1z" class="X"></path><path d="M351 97c1 0 2 0 4 1l-2 6v-2l-1 1c-1 2-2 4-2 6 0 1 0 2-1 3v-1c0-2 0-3-1-5-1-1-3-4-4-5s-4-1-5-1h0l-2-1h-3v1h0l-1-1h-14-8v-1h1l38-1h1z" class="o"></path><path d="M351 97c1 0 2 0 4 1l-2 6v-2l-1 1c-1 2-2 4-2 6-1-1-1-2-2-4s-2-3-3-4c2-1 2-2 4-3-1 1-1 1 0 2 0-1 0-1 1-1v-2h1z" class="C"></path><path d="M350 97h1c0 1 1 1 1 2s-1 2-1 3l-2-1v-1c0-1 0-1 1-1v-2z" class="F"></path><path d="M105 134c1-4 3-5 4-8 1-2 1-6 1-8-1-3-1-6-1-9-1-3-2-7-3-10 5-1 11-1 15-1v1l-1 1c-2 0-5 0-7-1-2 0-4 0-6 1l2 3c0 1 0 3 1 3l2 2c-1 1-1 1-2 1h0v3c2 1 22-1 26-1h20l-1 1h-2v1h0v1h0-1c-1-1-1-2-2-1 0 0 0 1-1 1l-1-1c-1-1-2-1-4 0v-1c-2 1-6 0-8 0h-3c-1 1-1 1-2 1h-11v2 1c-1-1-1-2-1-3h-1v2h-1v-1l-1-1-1 1 1 2h0c-1 0-1 0-1-1l-1-1c-1 0-3 0-3 1v4c0 2 0 5-1 7h0c0 2-1 3-2 4s-1 3-2 4h-1z" class="O"></path><path d="M219 98c4 0 16-1 19 0v1h1c0 2 0 4-1 5-1 2-3 2-5 3h-1l-11-3c-1-1-1-2-2-3h1l3-1h-3l-1-1h1l-1-1z" class="Z"></path><path d="M219 98c4 0 16-1 19 0v1c-2 1-4 2-6 4h0v-1l1-1c-3-1-7-2-10-1h-3l-1-1h1l-1-1z" class="i"></path><path d="M110 109h0c1 0 1 0 2-1l-2-2c-1 0-1-2-1-3l-2-3c2-1 4-1 6-1 2 1 5 1 7 1l1-1c4 1 10 3 12 7l1 1h-7-3-2c-2 0-4 1-6 1 0 1-2 2-3 1h0-3z" class="Z"></path><path d="M124 107c-1 0-2-1-2-2h0 2v-1c1 1 1 1 2 1l4 1c-1 1-2 1-3 1h-3z" class="L"></path><path d="M116 108v-1c0-1 0-2 1-4-1 0-1-1-1-1h0c2 1 3 3 4 3l2 2c-2 0-4 1-6 1z" class="X"></path><path d="M332 175h0l2-5 3-5-3-2c-1 0-2 0-3-1l1 1 6 1c1 1 2 1 3 1l5 2c2 0 4 1 5 1l2 1h0v2-1h-3v2l-1-1c-2 2-1 4-2 6v1h0l-1-1-1 1c1 0 1 0 2 1h0c-2 0-5-1-6-1-1-1-2-1-4-1l-5-2z" class="C"></path><path d="M347 177v-2c0-1-1-1-1-2 0 0 1 0 0-1 0-1 0-2 1-3h0c2 0 2 0 3 1v2l-1-1c-2 2-1 4-2 6z" class="Z"></path><path d="M338 166h0 1v9h-3l-2-1h0c0-2 2-6 4-8zm3 1l4 1v5 4c-1 0-3-1-4-1-1-2-1-8 0-9z" class="t"></path><path d="M339 163l3-8s1-1 1-2c0 0 0-1 1-1v2h1c2 0 4 1 5 0h1c1 0 1-1 1-1 1 0 1 0 2 1h1v16l-2-1h0l-2-1c-1 0-3-1-5-1l-5-2c-1 0-2 0-3-1l1-1z" class="b"></path><path d="M347 158h1v-2h2v1c-1 1-1 2-1 3h1v1 1h-2c0-1 1-1 1-2l-2-1v-1z" class="X"></path><path d="M345 159h1l1-1v1l-1 2c1 1 1 0 1 1 1 1 0 1 1 2v1h-2-1l-1-1c0-1 1-2 0-4h0l1-1z" class="P"></path><path d="M339 163s1 0 1-1v-1h1c0-1 0-1 1-2 0 0 1 0 2-1h1v1l-1 1h0c1 2 0 3 0 4h-1c-1-1-2-1-3 0 0 0-1 0-1-1z" class="X"></path><path d="M338 164l1-1h0c0 1 1 1 1 1 1-1 2-1 3 0h1l1 1h1 2l3 3h0c-1 0-3-1-5-1l-5-2c-1 0-2 0-3-1z" class="N"></path><path d="M354 154h1v16l-2-1h0l-2-1h0v-8-3c1 0 1-1 1-1v-1c0-1 1-1 2-1z" class="Z"></path><path d="M248 99c4-1 9-1 13-1h51-1v1h8-16-2v1c-1 1-1 1-2 1v1h-1c3 1 5 3 8 5 0 1 1 1 2 2 4 1 7 0 11 1 3 1 5 2 7 3h1c1-1 2 0 3 0h2c1-1 0-4 1-6h0 0v-1c1-3 2-5 4-7l2 1h0 0c-4 2-4 5-6 8h1c0 1-1 1-1 2 1 1 1 3 1 4 2 0 7 1 8 2-2 1-5-1-7-2h0c-3 0-5 1-8 0-2 0-4-1-6-2-3-1-8-2-12-1h0c0 1 1 2 2 2v1l-1 1-2-3c-1-2-2-4-4-5-1-2-4-3-5-4-4-1-7-3-10-3-5-1-10-1-15 0h-7c-1-1-3-1-4-1-1 1-3 0-4 1h0c-1 2-3 3-4 4 0 1-1 2-2 3 0 1 0 1-1 2v1l-2 4-1 1h0c1-3 2-6 3-8 2-3 5-5 7-8-3 0-5 0-7 1h-3l-1-1z" class="i"></path><path d="M298 102c-1 0-3-1-4-2-1 0-3 0-4-1h6 7-2v1c-1 1-1 1-2 1v1h-1z" class="P"></path><path d="M134 107h1 0l1-1c1-1 2 0 3 0 2 0 4 0 7 1l2 1c1 0 3-1 4-2 1 0 2 1 3 2 0 0 1 2 1 3h0-20c-4 0-24 2-26 1v-3h3 0c1 1 3 0 3-1 2 0 4-1 6-1h2 3 7z" class="s"></path><path d="M99 150c0-1 1-2 1-2h1l2-1h0c0 4-2 10 0 14v1 1c0 2 0 3 1 4 0 2 0 4 1 5 0 4 0 8 1 12h1l4 11c0 1 1 3 2 4h-1c-2 0-3-1-4-3h-1c-2-3-4-6-5-9 1-1 1-2 1-3h1c0-1-1-1-1-1l-1-1c-2-1-3-5-3-7-1-6-2-13-1-19 0-2 1-4 1-6h0z" class="V"></path><path d="M102 187c1-1 1-2 1-3h1l1 2c-1 0-1 1-1 2 1 2 2 4 3 5 0 1 1 2 1 3h-1c-2-3-4-6-5-9z" class="l"></path><path d="M99 175l1 1h0c1-3 0-9-1-12v-2h1v1c1 1 0 3 0 4l3 16-1-1c-2-1-3-5-3-7z" class="b"></path><path d="M127 98c13-1 27 0 40 1 4 0 10 0 15 1v-1c1-1 4-1 5 0 2 1 5 6 6 8h0c-2-3-3-6-6-7h-1c2 2 3 4 5 7-4-2-6-4-9-6h-1-3l-2-1-2 1c-1 1 0 1-1 3l-1 1c-1 0-1 0-1 1h-1c-1 1-2 1-3 1l-1-1h0v1c-1 1-2 0-4 0 0-1 0-1-1-1l1 1h0-1c-3 1-5 0-7-2v-1c0-1 0-2-1-3h-9c-4 0-14 0-17-3z" class="e"></path><path d="M133 99c6-1 12 0 18 0v1h-2-4-8-3s0-1-1-1z" class="j"></path><path d="M153 101l1-1h0c1 1 1 1 2 1 0 0 1 0 2-1v1h1l1-1h1 1 2c0 1-1 1 0 2l-1 1h-1c-1 0-1 0-2-1h0v2 1c0 1 0 1 1 2h1 0-1c-3 1-5 0-7-2v-1c0-1 0-2-1-3z" class="X"></path><path d="M164 102c0-1 0-1 1-2h1 10l-2 1c-1 1 0 1-1 3l-1 1c-1 0-1 0-1 1h-1c-1 1-2 1-3 1l-1-1h0v1c-1 1-2 0-4 0 0-1 0-1-1-1l1 1h-1c-1-1-1-1-1-2v-1-2h0c1 1 1 1 2 1h1l1-1h0z" class="Z"></path><path d="M170 106l-2-2h1c1 1 2 0 3 1-1 0-1 0-1 1h-1z" class="V"></path><path d="M164 102c0-1 0-1 1-2h1 10l-2 1c-1 1 0 1-1 3l-1 1c-1-1-2 0-3-1v-2h-3 0v1 1c-1-1-1-1-1-2h-1z" class="P"></path><path d="M259 194c0-1 1-2 0-3v-3c0-2-1-4-1-6 0-1 1-3 0-4v-4-10-9c0-2 1-5 0-8v-2l-1-1c0-3 0-7 2-9 3-5 8-7 13-8 4-1 9-1 12 0l1 1c5 1 9 3 13 7 7 5 10 14 11 22 0 3 1 10-2 12 0-2 1-4 1-6 0-6 0-13-3-18-4-7-9-12-16-15-2-1-3-1-5-2v7l1 1c1 0 1 1 2 1h0c-1 0-2 0-3-1v1 1c1 2 2 3 3 4h-1-1l-3-3c0-1 0-2-1-3-1-3-6-5-9-5h0-1-2c-1 1-2 0-4 1 0 0-2 2-3 2v2 1c0 1 0 1 1 2v1c1 0 1 0 1 1s-1 1-1 2h-1-1l-2 2h0l2 51-2-2z" class="T"></path><path d="M259 138v1l1 1c0 1 0 2 1 3l-2 2h0 0c-1-2 0-5 0-7z" class="P"></path><path d="M259 138v-2c1-2 3-4 5-5l1-1c4-1 8-3 11-2 1 0 1 0 1-1 2 1 4 1 6 1 1 1 1 5 1 7-3-2-7-4-10-5 0 0-1 0-2 1h0-1-2c-1 1-2 0-4 1 0 0-2 2-3 2v2 1c0 1 0 1 1 2v1c1 0 1 0 1 1s-1 1-1 2h-1-1c-1-1-1-2-1-3l-1-1v-1z" class="Z"></path><path d="M265 236c2 1 4 1 6 1 1 0 3 0 4-1 2 0 3-1 4-1 2-1 4-2 7-3l6-6 10-10v-1h1v1c0 1 0 2 1 2h1c0 2 0 3 1 5h1l2 2c-5 4-7 7-10 12v1h-36l2-2z" class="t"></path><path d="M265 236c2 1 4 1 6 1 1 0 3 0 4-1 2 0 3-1 4-1 2-1 4-2 7-3l6-6 10-10v-1h1v1l-14 14c5-2 8-4 12-6v1c-3 2-7 4-11 6-3 1-5 3-8 3-1 1-2 1-3 1l-1 1v1h21v1h-36l2-2z" class="q"></path><path d="M305 161c1 1 1 3 1 4 0 3 0 4 1 6 8 1 17 1 25 4l5 2c2 0 3 0 4 1 1 0 4 1 6 1l1 1v1l-2-1h-2c-1 0-2-1-3-1l-1 1v4h-1-3c-2-1-4-1-6-1v-1h-5l-10-1h-7c-3 1-6 1-9 2v1h0-1l1-1-1-1 6-7c0-1 1-2 1-3l-1-1 1-1c1-2 0-6 0-9z" class="t"></path><path d="M305 161c1 1 1 3 1 4 0 3 0 4 1 6l-1 1c-1 2-3 4-4 6s-2 4-3 5l-1-1 6-7c0-1 1-2 1-3l-1-1 1-1c1-2 0-6 0-9z" class="T"></path><path d="M306 172l1-1c8 1 17 1 25 4l5 2c2 0 3 0 4 1 1 0 4 1 6 1l1 1v1l-2-1h-2c-1 0-2-1-3-1l-1 1v4h-1-3c-2-1-4-1-6-1v-1c1-2 1-4 2-6-4-1-7-1-11-2-5-1-10-2-15-2z" class="O"></path><path d="M330 183c1-2 1-4 3-6 1 1 2 1 3 1h1c1 0 1 0 2 1 1 0 0 0 1 1h0v4h-1-3c-2-1-4-1-6-1z" class="t"></path><path d="M336 184c0-2 0-3 1-4h3 0v4h-1-3z" class="V"></path><path d="M319 99h14l1 1h0v-1h3c-2 2-3 4-4 7v1h0 0c-1 2 0 5-1 6h-2c-1 0-2-1-3 0h-1c-2-1-4-2-7-3-4-1-7 0-11-1-1-1-2-1-2-2-3-2-5-4-8-5h1v-1c1 0 1 0 2-1v-1h2 16z" class="t"></path><path d="M306 107v-1c1 0 1 0 2-1 0 1-1 1 1 2h1l1 1 1-1h0c1-2 3-2 5-2l1 1h-3c-1 0-1 0-1 1s1 1 2 1h3c1-1 1-1 1-2 1 0 5 2 6 2 1 1 1 2 1 3l1 1h0c0-1 2-2 3-2h0c1-1 1-1 1-2l1-1c-1 2 0 5-1 6h-2c-1 0-2-1-3 0h-1c-2-1-4-2-7-3-4-1-7 0-11-1-1-1-2-1-2-2z" class="N"></path><path d="M76 221h0c1 0 2 0 3-1h0c1 0 2 0 3 1h3 2 1 2s2 1 3 1c3 1 6-1 9-1 1 5 2 23 0 25-1 2-4 2-5 3h-3v-1h-1c-2 0-5-1-7-1h-1c-3 1-4 0-7 0 0-1-1-1-2-2h-1c-1 0-2-2-3-3v-1c-2-2-2-6-2-8h0v-1-4c1-1 1-1 1-2 0-2 3-3 5-5z" class="Z"></path><path d="M72 242c2 0 3 1 4 2v1h-1c-1 0-2-2-3-3z" class="C"></path><path d="M86 247l1-1h1 5s1 1 2 1l-2 1c-2 0-5-1-7-1zm-10-26h0c1 0 2 0 3-1h0c1 0 2 0 3 1h3l-1 1h-1-4l-1 1-2-2z" class="P"></path><path d="M98 244l-1-1v-1c-1-2 1-1 1-2v-2l1 1h1l1 1-2 1c-1 1 0 2-1 3z" class="F"></path><path d="M76 244l2 1 3-2h0v2 1c1 1 2 1 4 1-3 1-4 0-7 0 0-1-1-1-2-2v-1z" class="X"></path><path d="M92 223v1h5l3-1c1 1 1 2 1 3v-1c-1 0-3 0-4 1-2 0-5 3-6 5v1h0-1-1c1-2 5-5 6-6-1 0-5 1-6 0v-1l1-1h1l1-1z" class="F"></path><path d="M88 221h2s2 1 3 1c3 1 6-1 9-1 1 5 2 23 0 25-1 2-4 2-5 3h-3v-1h-1l2-1h0l3-3c1-1 0-2 1-3l2-1-1-1h-1l-1-1h2l2 1v-1l-1-12c0-1 0-2-1-3l-3 1h-5v-1l-4-2z" class="w"></path><path d="M98 244c1-1 0-2 1-3l1 1 1-1c1 1 1 2 1 3-1 0-2 1-2 1-1 1-1 1-1 2h-4l3-3z" class="q"></path><path d="M63 246v-7c1-5 1-9 2-13l2-9c1-2 1-3 2-5l3-3s1-1 1-2v-3-11-2c1-1 1-2 1-2v-3l1-2c0-1 1-3 2-4l2-4 1-3h0v-2-1c1-1 1-2 1-3v-5c-1-1-1-5 0-6h2l1 1h0c1 1 0 4 1 6l1 1c1 4 1 7 2 11 0 3 1 7 2 10 1 2 3 4 4 5v1c2 4 3 8 4 12 1 3 1 5 1 7 0 5-2 7-6 9-2 1-3 1-3 2h-2-1-2-3c-1-1-2-1-3-1h0c-1 1-2 1-3 1h0c-2 2-5 3-5 5 0 1 0 1-1 2v4 1h0c0 2 0 6 2 8v1c1 1 2 3 3 3h1c1 1 2 1 2 2h-1v1h-4l3 1h-9c-1 0-3 0-4-1v-2z" class="l"></path><path d="M78 180v4 2 1h0l-1-1h0-1l2-6z" class="F"></path><path d="M65 231v1 2 7c1 1 1 2 1 3v1c-1 0-1 0-2 1v-5c1-3 1-7 1-10z" class="G"></path><path d="M76 186h1 0l1 1h0l-1 2c0 2-1 6-1 7l-2 1c0-2 0-3 1-5 0-1-1-2 0-3 0-1 0-3 1-3z" class="C"></path><path d="M71 211v6h0-1v1l1 1s1 1 0 1v1c-1 0-1 1-1 2h-1v2l-1 5h0-1s0-1-1-1v-2l2-9h0c0-1 0-1 1-2v-2c1-1 2-2 2-3z" class="L"></path><path d="M66 227v2c1 0 1 1 1 1h1 0l1 2v3c0 1-1 1-1 2l-1 1c0 1 1 1 1 2v4h1v1s0 1 1 1l-1 1h-1c-1 0-1-1-2-2v-1c0-1 0-2-1-3v-7-2-1-2c0-1 1-1 1-2z" class="H"></path><path d="M65 232c1 0 1 0 1 1s0 1-1 1v-2z" class="L"></path><path d="M66 244c0-1 0-3 1-4 0 2 0 4 1 5v-1h1v1s0 1 1 1l-1 1h-1c-1 0-1-1-2-2v-1z" class="P"></path><path d="M69 225l2 1c0 1 0 1-1 2v4 1h0c0 2 0 6 2 8v1c1 1 2 3 3 3h1c1 1 2 1 2 2h-1v1h-4l3 1h-9c-1 0-3 0-4-1v-2h1c1-1 1-1 2-1 1 1 1 2 2 2h1l1-1c-1 0-1-1-1-1v-1h-1v-4c0-1-1-1-1-2l1-1c0-1 1-1 1-2v-3l-1-2 1-5z" class="u"></path><path d="M66 245c1 1 1 2 2 2h0-4v1c1 0 2 0 3 1-1 0-3 0-4-1v-2h1c1-1 1-1 2-1z" class="H"></path><path d="M69 244c1 1 0 2 2 3v-1-1h2 0c1 1 3 2 4 3-3 0-6 0-9-1h0 1l1-1c-1 0-1-1-1-1v-1h0z" class="L"></path><path d="M68 237c2 2 2 4 4 7 0 0-1 0-1 1v1 1c-2-1-1-2-2-3h0-1v-4c0-1-1-1-1-2l1-1z" class="P"></path><path d="M68 240c1 2 1 2 1 4h0-1v-4z" class="N"></path><path d="M80 176v-2h1v-2l1-2c0-1-1-2 0-3v-10h1v3l1 1h0v2h0v1l2 5v2c1 5 1 11 4 16 1 1 3 3 3 4 2 6 4 11 5 16 1 3 0 6-1 8-2 3-7 4-10 4h-1-3c-3 0-5 0-8 2h-1c-1 0-2 2-3 2h-1c0-1 0-2 1-2v-1c1 0 0-1 0-1l-1-1v-1h1 0v-6l1-1 1-1s1-1 1-2v-3-7l2-1c0-1 1-5 1-7l1-2v-1-2-4c0-1 1-2 2-4z" class="s"></path><path d="M82 196h1v2c0 1 1 1 1 2h-2l-1-2c0-1 0-1 1-2z" class="V"></path><path d="M80 176v1 3c1 1 0 3 1 3l-1 1h0l-1-1c0 1 0 2-1 3v-2-4c0-1 1-2 2-4z" class="N"></path><path d="M71 217c1-1 3 0 4-1l1 1v-1h1 1v2h1 2l-1-1h1l2 2c-3 0-5 0-8 2h-1c-1 0-2 2-3 2h-1c0-1 0-2 1-2v-1c1 0 0-1 0-1l-1-1v-1h1 0z" class="F"></path><path d="M356 91l1-1c1-2 2-5 2-7 0-3-2-4-4-6 2 1 4 2 5 3s1 2 1 4v3c0 1 0 2-1 3 1 0 2 0 2 1l2-1h0c1 1 1 1 1 2-1 0-1 0-1 1s0 1-1 2l1 2v4h-2c0 1-1 2-1 3v3 1c0 2-1 3-1 4-1 1-1 2-1 3 0 2-1 5-1 6s1 2 1 3c-1 0-2-1-2 0h-1l1 3 1 5v3c0 1 1 3 1 4v1h-1v1h-1l-2-8v3c1 6 0 12 0 18h0-1c-1-1-1-1-2-1-1-1-1-1-2-1l-4-3c-1-1-3-1-4-2h-1l-1-1c-1 0-1-1-3-1h0c-1-1-2-1-2-3l-1-1v-1c-1-3-1-6-1-8v-5l-3-6-5-1v-1h-2 0-1l-1-1c0 1-1 0-2 0v-2h-1c-2-2-5-2-7-3-1 0-2-1-2-2h0c4-1 9 0 12 1 2 1 4 2 6 2 3 1 5 0 8 0h0c2 1 5 3 7 2-1-1-6-2-8-2 0-1 0-3-1-4 0-1 1-1 1-2h-1c2-3 2-6 6-8h0c1 0 4 0 5 1s3 4 4 5c1 2 1 3 1 5v1c1-1 1-2 1-3 0-2 1-4 2-6l1-1v2l2-6c0-2 1-4 1-6h-1c1 0 1 0 1-1z" class="t"></path><path d="M325 119l5 1c1 2 3 5 4 7 1 4-1 11 2 14l-1 1-1-1v-1c-1-3-1-6-1-8v-5l-3-6-5-1v-1z" class="P"></path><path d="M356 91l1-1c1-2 2-5 2-7 0-3-2-4-4-6 2 1 4 2 5 3s1 2 1 4v3c0 1 0 2-1 3 1 0 2 0 2 1l2-1h0c1 1 1 1 1 2-1 0-1 0-1 1s0 1-1 2l1 2v4h-2c0 1-1 2-1 3v3 1c0 2-1 3-1 4-1 1-1 2-1 3 0 2-1 5-1 6s1 2 1 3c-1 0-2-1-2 0h-1l1 3 1 5v3c0 1 1 3 1 4v1h-1v1h-1l-2-8v3c-2-6-2-11-3-17v-7l1-8 2-6c0-2 1-4 1-6h-1c1 0 1 0 1-1z" class="H"></path><path d="M356 91l1-1c1-2 2-5 2-7 0-3-2-4-4-6 2 1 4 2 5 3s1 2 1 4c0 3-2 5-3 7l-3 8c-1 4-1 7-1 11-1 1-1 6-1 6l-1-4 1-8 2-6c0-2 1-4 1-6h-1c1 0 1 0 1-1z" class="U"></path><path d="M360 90c1 0 2 0 2 1l2-1h0c1 1 1 1 1 2-1 0-1 0-1 1s0 1-1 2l1 2v4h-2c0 1-1 2-1 3v3 1c-1 0-1 1-2 0h-1v-4h0v-2l-2 1c0-5 2-9 4-13z" class="r"></path><path d="M363 95l1 2v4h-2v-1c1-2 0-3 1-5z" class="m"></path><path d="M360 96h1c0 2-1 5-1 7l1 1v3 1c-1 0-1 1-2 0h-1v-4h0v-2l1-3 1-3z" class="T"></path><path d="M359 99c1 2 1 3 1 4-1 0-1 0-2 1h0v-2l1-3z" class="O"></path><path d="M358 104c1-1 1-1 2-1l-1 5h-1v-4z" class="L"></path><defs><linearGradient id="AB" x1="357.247" y1="92.133" x2="360.391" y2="99.755" xlink:href="#B"><stop offset="0" stop-color="#373337"></stop><stop offset="1" stop-color="#454946"></stop></linearGradient></defs><path fill="url(#AB)" d="M360 90c1 0 2 0 2 1l2-1h0c1 1 1 1 1 2-1 0-1 0-1 1l-1-1c-2 0-2 2-3 4h0l-1 3-1 3-2 1c0-5 2-9 4-13z"></path><path d="M356 103l2-1v2h0v4h1c1 1 1 0 2 0 0 2-1 3-1 4-1 1-1 2-1 3 0 2-1 5-1 6s1 2 1 3c-1 0-2-1-2 0h-1l1 3 1 5v3c0 1 1 3 1 4v1h-1v1h-1l-2-8v-2-2c-1-1-1-2-1-3l1-1c0-3-1-6-1-10s1-8 2-12z" class="Y"></path><path d="M355 133v-2-2c-1-1-1-2-1-3l1-1 3 15v1h-1l-2-8z" class="F"></path><path d="M358 104h0v4h1c1 1 1 0 2 0 0 2-1 3-1 4-1 1-1 2-1 3 0 2-1 5-1 6s1 2 1 3c-1 0-2-1-2 0h-1c-1-6 0-12 1-19l1-1z" class="H"></path><path d="M358 104h0v4h1c1 1 1 0 2 0 0 2-1 3-1 4-1 1-1 2-1 3-1-1-1-2-2-3v-7l1-1z" class="j"></path><path d="M357 112l1-2 1-1v1c0 1 0 2 1 2-1 1-1 2-1 3-1-1-1-2-2-3z" class="C"></path><path d="M272 131c3 0 8 2 9 5 1 1 1 2 1 3l3 3h1 1c4 2 9 10 10 14 1 1 2 3 2 4l1 2c1 2 2 4 2 6l1 2s0 1 1 1l1 1h-2v-1c-1 0-1 0-1 1 0 0 0 1-1 2 0 1 0 1-1 2 0 1 0 2-1 2 0 1-3 4-3 5l1 1h-2v-2h0c1-1 4-4 4-6v-1l-1 1-2 2h-1c-1-1-2-1-3-1 0-2 0-3 1-4l-1-1-1 1v1c0 2-1 3-1 5-1 2-1 3-1 5 0 1-1 2 0 3h1 1 1v1c0 1 0 2-1 3h-1c0 1 0 1-1 1h-1l-1 1-1 1h-1-1-2c-1 1-2 0-3 0-1 1-1 1-2 1-1 1-1 2-1 4h0l2 2v1c0 1 0 2 1 2v3h-1c0 2 1 6 0 7v1l-1 1c-1 1-2 3-4 3-1 0-1 1-2 1h0l-4 1c0 1 0 1-1 1h0-1 0c0-1 0-1 1-2v-2-8s-1 0 0-1c0 0 1 1 1 0 1 0 2-1 2-1h0-1-3v-2l-1-1c-1-2-1-2-2-3s-1-2-1-3c-1-1 0-2 0-3l-2-51h0l2-2h1 1c0-1 1-1 1-2s0-1-1-1v-1c-1-1-1-1-1-2v-1-2c1 0 3-2 3-2 2-1 3 0 4-1h2 1 0z" class="t"></path><path d="M291 174c-1 0-3 0-4 1h-3-1 0c1-1 2-1 4-1v-1h4v1z" class="s"></path><path d="M265 175s1 1 2 1h-2c-1 3 0 13 0 16 1 1 1 1 3 2h0v1c-1 0-2-1-3-2-2-4-1-13 0-18z" class="j"></path><path d="M287 142c4 2 9 10 10 14 1 1 2 3 2 4l-1 1c-3-6-6-12-12-16h0v3h-2-1c1-2 1-3 0-4 0-1 0-1 1-1l1-1h1 1z" class="F"></path><path d="M284 148v-1-2h2 0v3h-2z" class="d"></path><path d="M295 178l2-3c1-1 1-2 1-3 0-3-1-8-3-10l-3-3c-2-1-7-3-7-6-1 0 0-1 0-1v-2h1v1l-1 1c2 5 8 5 11 9 1 2 2 4 2 6 0 1 0 3 1 4h2c1-4-1-8-3-10l1-1 1 2c1 2 2 4 2 6l1 2s0 1 1 1l1 1h-2v-1c-1 0-1 0-1 1 0 0 0 1-1 2 0 1 0 1-1 2 0 1 0 2-1 2 0 1-3 4-3 5l1 1h-2v-2h0c1-1 4-4 4-6v-1l-1 1-2 2h-1z" class="G"></path><path d="M364 203h3c1 1 3 0 4 1-2 0-4 0-7 1l-2 1-1 1h4 3c1 0 3 0 4 1h4c2 2 5 2 8 4h0c2 1 3 2 4 4l1 1h0l-1 1-1-1h0c2 3 5 5 7 8v1h-1c1 0 2 1 3 1 2 0 3 1 4 1l1-1c2 1 3 2 5 3l2 1 3-1h4c2-1 3-2 5-2 2-2 4-3 6-4 0 1-1 2-1 3h1l-1 2v1s-1 2-2 2c-2 0-3 2-4 2s-2 1-2 1v1c-1 2-3 2-3 3v1l1 1c-2 1-5 2-7 2h0l-6 3c0 1 1 1 1 1h-2v1h-3v2s1 0 2 1h-1l-1 1v1l-1 1v2c3-1 5-2 8-2l-2 2-1 1h0c-2 0-3 0-4 2-1 0-1 0-1 1 1 0 2-1 3-1l-2 2h0c1 0 2 0 3-1l1 2h-1l-4 2c1 0 1 0 2 1l-10 3c-7 3-14 6-20 11h-1c-3 2-6 4-8 6v1s-1 1-1 2c-1 1 0 3 0 4-1 4-1 8 0 12v1 2 5 4l-1 5v7c-1-1 0-2-1-4v3l-2 133-1 10-1 1 1 2h-2v3l1 2h0 1 0c3 8 2 19 2 26l1 10v5 7c0 5-1 9 0 14h0v2c0 3 0 7-2 9-1 0-1 0-1-1h-1 0v-1h-1c0 1-1 2-1 3h0v1c-1 0-1 1-1 1h-1l-1-1 1-1c0-2-1-2-2-3l-4-3-2-1c1-2 2-4 4-6l1-1c-1 0-2-1-3-1v-3c-1 0-1 0-1-1-1 1-1 1-1 2l-1 2-1 1c0 1-1 2-2 3s-3 2-4 3h-1-2c-1 0-1 1-2 1-1 1-2 1-3 1-2 1-4 1-6 2-1 0-2 1-3 1h-2l-1 1c-1 0-3-1-4-1h-3c-2-1-3-2-5-3l-3-1h-1c-3-1-6-3-9-4-6-1-13 1-19-4-3-2-2-6-4-9 0 0-2-1-3-2h0c0 3 0 9-2 11h0c1 1 1 1 1 2 0 4 1 6 3 8l1 1h0c0 1-1 2-1 3v3 1h-1l-1-1-4-4c-1-3 1-7 1-9 1-2 0-4 1-6s1-4 1-6c1-5 1-10 0-15 0-3-1-6-1-9 0-2 1-4 1-6v-1c1-3 5-8 8-10 0 0 1-1 2-1 0 0 3-3 4-3l1-1 2-2 3-1h-2l1-1h0l6-3s0-1 1-1h1s1-1 2-1 1-1 1-1l2-1c1 0 3-1 4-1h1c1-1 1-1 2-1h1c2-1 3 0 4-1h1c1 0 1 0 2-1 1 0 3 0 4-1h1c5-1 10-4 15-7v-2c1-1 1-2 3-2v-1l4-3c-1 0-3-1-4-1h0l-1-1c-3 0-4 0-6-1-2 1-5 1-7 1-5 1-9 1-14 2-3 0-6 1-9 2l-15 3c-6 1-11 2-16 3-2 0-2 0-3 1v-1c0-2 0-4 1-6l1-1 3-1c1 0 2 0 3-1h-1-1l-3 1h-1l-1 1v-1c0-1 0-2 1-3 0 0 0-1 1-1l1-2h0v-1c0-1-1-1-1-1h-1 0c-1-1 0-2 0-3 1-2-1-7-1-9s-2-6-2-8c1-1 0-2 0-2l2-2c-1 0-1-1-2-1v-3-13c3 0 1-1 2-2h1l1 1c1 0 1-1 1-1 1 1 3 2 4 3-4-4-6-8-8-13v-2h0v-6c-1-4-1-12 1-16h0l-1-1v-4c-1-1 0-2 0-3v-1l1-4c0 1 0 1 1 2 0-2 0-2 1-4 1-3 0-4 0-7 0-2-1-4-1-6h0 0l-1-1v-3c0-1-1-1-1-1-1-1-1-1-1-2v-2h1l1-1c0-1-1-1-1-2v-1h1 1l-1-1h-1v-2-4l-1-2c1-1 0-4 0-6 0-3-1-6 0-9 0-3 5-7 8-9h0l-3 1v-3c-1-1-1-1-1-2h0c0-1 1-3 1-4l-1-1 1-1c2-4 0-6 1-9 0-1 0-2-1-3l2-2c-1-1-2-2-2-3s-1-1-1-2l2-1h0-3l-2-6v-1l-1-4v-3l3-3c-1-1-1-1-3-2v-1c1-2 2-4 4-5h36v-1c3-5 5-8 10-12l7-3c1-1 2-3 3-4 3-6 14-9 21-11 2 0 5-1 7-1h1v-1h7c2 0 5-1 7-1 1 0 1-1 2-1z" class="t"></path><path d="M268 282v1l1 7-1-1v-7z" class="X"></path><path d="M349 441c1-1 1-2 2-3l1 1c0 1 0 1-1 2h-2z" class="Q"></path><path d="M329 316h1c2 0 6 0 7 1h0c-1 0-7 0-8-1z" class="V"></path><path d="M267 333c0 1 0 1-1 2l-1 1v-1c-1 0-1 0-1 1v1c-1-1-1-1-1-2 1 0 1-1 2-1s1-1 1-1h1z" class="Z"></path><path d="M295 360l1 1v8l-1 1v-1-9z" class="s"></path><path d="M349 441h2v4h-2v-3-1z" class="E"></path><path d="M322 471c2-1 4-1 5-2v2c-1 1 0 1-1 2h-1c-1-1-2-2-3-2z" class="e"></path><path d="M259 252c2 0 2 0 3 1 0 1-1 1-1 2h1 0 1v2l-1 1-1-1v-2h0c0 1-1 1 0 1l-1 1v-1l-1-4z" class="s"></path><path d="M319 298l-1-1c-2-1-6 1-8 1l-1-1h1 1l10-2-2 3z" class="Z"></path><path d="M334 507c1 0 2-1 3 0v1c-1 0-1 1-2 2-1 0-2 2-3 2v-3h0s1 0 1-1l1-1z" class="p"></path><path d="M318 361v-2h1c0-1-1-1 0-2h2c1 1 2 2 2 3-1 0-1 0-2 1h-2l-1-1v1z" class="s"></path><path d="M309 447c-4-1-7-1-11-1 2-1 4-1 6-1s4 0 6-1c0 1 0 2-1 3z" class="g"></path><path d="M310 444l6-1v1c0 1-1 2-2 2-2 0-3 1-5 1 1-1 1-2 1-3z" class="m"></path><path d="M346 277v1c-1 3-1 8-4 11 1-2 3-7 2-8s-2-1-3 0c2-2 4-3 5-4z" class="Z"></path><path d="M268 322c2 0 5 1 7 2-1 2-2 4-3 5h0-1c0-1 0-1 1-1v-3c0-1-2-2-4-2h0v-1z" class="b"></path><path d="M352 476l1 2h0 1 0-1v12c0-2-1-4-2-6h1 0c1-2 0-6 0-8z" class="S"></path><path d="M270 429h-1c-4-2-5-4-7-7l1-2c2 3 4 6 7 9z" class="P"></path><path d="M305 321c-1-4 1-6 3-9 1-2 1-2 3-3-1 3-4 7-5 10-1 1-1 1-1 2zm10-19c-4-2-11 1-15-2l1-1c4 1 11 2 15 1h1c-1 1-1 2-2 2z" class="V"></path><path d="M293 337l1-1c1 0 2 0 2-1 1 0 1-1 1-1l1 1c-2 2-7 4-10 5-1 1-2 2-4 2v-1c0-1 1-1 2-2l1 1h1v-1h2c1-1 2-2 3-2z" class="H"></path><path d="M320 341h4c1 2 0 4-1 5l4 3c-2 0-3-2-4-2-1 1 0 2 0 3v1c-1 1-1 2-2 2l-1-2c1 0 1-1 2-2v-2h0v-1s1 0 1-1l1-3-4-1z" class="X"></path><path d="M271 291l1 1c-1 0-1 1-1 1v1l-1 1-1 5c-1 4 0 9-1 12h0c-1-2-1-8 0-10l1-4v-2c1-2 1-3 2-5z" class="Z"></path><path d="M306 286c3-2 6-5 9-7v1c-1 1-2 1-3 2h1 0l3-2h0l1-1h1c0-1 2-1 2-1l-12 8h-2z" class="P"></path><path d="M336 289h1l1 1h1v2c-1 3-3 5-5 6 0-1 1-2 1-4 1-1 1-2 1-4v-1z" class="F"></path><path d="M336 289h1l1 1h1v2-1l-1 1-2-2v-1z" class="V"></path><path d="M261 325c0-1 0-1 1-2h3c-1 0-1 1-1 1v2 2h-2c0 1 0 1 1 1h0l1 1 1 1h-2c-1-1-1-2-2-2 0-1-1-1-1-2v-1h1 1l-1-1zm88 61l1 1c-1 0-1 0-1 1v1c-1 1-2 3-2 5h1c-1 1-1 2-2 3-2 1-2 2-5 2h0c3-3 5-5 7-9v-2s0-1 1-1v-1z" class="N"></path><path d="M337 263c2-1 5-2 7-2l-1 1v1h2 0v1c-1 1-3 2-5 3 0-1 1-2 1-2h1c1 0 1-1 2-1h0c-1 0-1 0-2-1-1 0-1 1-2 2 0 0-1 1-2 1s-2 1-3 0l1-1h2c1-1 2-1 2-2h-3z" class="Z"></path><path d="M348 493c-1-2-1-5-1-8h1c1 1 1 2 2 4 0 1 1 3 1 5-1-1-1-1-2 0h0-1v-1z" class="K"></path><path d="M350 489c0 1 1 3 1 5-1-1-1-1-2 0h0-1v-1h1v-1c0-1-1-2-1-3h2z" class="i"></path><path d="M266 269v2c1 4 0 10-2 14h0l-1-1 1-1c2-4 0-6 1-9 0-1 0-2-1-3l2-2z" class="L"></path><path d="M329 299h0 3l-2 1c-1 2-4 3-7 4h-3v-1c2 0 3 0 4-1v-1c2-1 3-2 5-2zm16 121h0c1 1 1 1 1 2h2c1 2 1 5 0 7 0 1 0 2-1 2h0c-1-1 0-3-1-4v-1-1-1-1c-1 0-1 0-1-1v-2z" class="N"></path><path d="M269 488s1-1 2-1c0 1 0 2-1 3 1 0 1 0 1 1l-1 1v1c1 1 0 3 0 4h0-1v-4h-1c-1 0-2 1-2 3-1-1-1-2-1-2l1-2c1-1 2-2 3-4z" class="V"></path><path d="M262 246l2-3 1 1h-1c0 1-1 2-2 3l1 1v1c-1 0-1 2-1 3v1c1 0 1 1 1 2h-1 0-1c0-1 1-1 1-2-1-1-1-1-3-1v-3l3-3z" class="N"></path><path d="M299 329v2c0 1-1 1-1 2h0 0c1 0 0 0 1-1v2l-1 1-1-1s0 1-1 1c0 1-1 1-2 1l-1 1v-6h1 0 2c0 1 0 1 1 2l1-1c0-1 0-1 1-1v-2z" class="V"></path><path d="M324 339h1c1 1 2 1 3 2 2 2 3 4 4 6 2 4 2 11 0 15h-1v-1c1 0 0-1 0-2 1-2 1-6 1-9-1-1-1-3-2-5h0c0-1-2-4-4-4 0-1-1-1-2-2zm-54 168h2c1-1 0-2 1-3h1v1 2 6h-1c-1 0-1 1-2 2h-1c0-2 0-4 1-6 0 0 1 0 1-1l-2-1z" class="Z"></path><path d="M350 460c0 2 0 13-2 14 0 1 0 1-1 1 0-1 0-2-1-3h0v-2c1-1 1-4 1-5v-2h1c0 1 1 2 0 3l1-1v-2c1-1 1-2 1-3z" class="X"></path><path d="M261 329c1 0 1 1 2 2-1 1-1 2-1 3 1 0 2-1 3-1h1c0-1 0-1 1-1v1h-1s0 1-1 1-1 1-2 1c0 1 0 1 1 2v1c-1 0-1 0-2 1h0l-1-1v-3c0-1-1-1-1-1-1-1-1-1-1-2v-2h1l1-1z" class="b"></path><path d="M259 330h1v4c-1-1-1-1-1-2v-2z" class="o"></path><path d="M260 319c2 3 5 3 8 3v1h0v1c1 2 1 3 1 5l-1 1-1-1-1-2c0-1 1-3 1-4h-2-3c-1 1-1 1-1 2h-1v-2-4z" class="P"></path><path d="M281 480l2-1 4-1v-1h2c1 0 1 0 1-1l8-2h2l1 1h-3l-1 1h0-1-1c-2 1-5 1-7 2-1 2 0 4-2 6v-1h0v-4h-1c-3 1-5 2-7 3 0 0-1 1-2 1l2-2 3-1z" class="F"></path><path d="M352 326v-20h0l1 25v12c-1-1-1-2-1-3h0-1 0v-1l1-8v-5z" class="c"></path><path d="M302 440c2 0 3 0 5-1l10-2c1 0 3-1 4-1h0c0 1 0 2-1 3l-1 1v-1h-1l-3 1c-4 0-9 2-13 0z" class="H"></path><path d="M298 302c2 0 12 2 12 2-1 1-4 0-6 0h-4c3 1 7 1 9 3 1 0 1 0 0 1l-1 2-1-1c-1 0-2 0-3-1h0-1c-1 0-2-1-2-1-1-1-1-2-1-2 0-1-1-2-2-3z" class="N"></path><path d="M292 542v-2l1-1c0-1 0-1 1-2 1 1 2 1 3 2h1c0 1 1 1 2 1 1 1 1 0 1 1h2 0l1 1h0-4-1v-1h-2 0v1c1 1 1 1 1 2 0 0 1 0 2 1 0 0 1 0 1 1l-3-1-6-3z" class="s"></path><path d="M295 371c1 0 1 1 2 1 1 2 2 3 4 5 0 1 1 1 2 2h1 2l1 1h0-3-3l-1-1h-2-1c-1 0-2-1-3-2h1 1 1l-1-1-2-2c0-1 0-2 1-3z" class="F"></path><path d="M262 367h0v4c1 2 0 4 0 6v6c1 1 1 2 0 3-1-1 0-2-1-3l-1-1v-3 4c-1-4-1-12 1-16h1z" class="X"></path><path d="M301 477l-2 1h0c-7 3-14 10-18 16 0 1 0 2-1 3 0 1-1 1-1 2 0 2 0 3-1 5h-1c0-2 1-6 2-8h0c0-1 1-2 1-3 1-2 0-3 1-4 4 0 7-5 10-7s6-4 9-5h1z" class="j"></path><path d="M339 513h0v1l-1 2c0 1 0 1-1 2h0c-1 1-2 2-4 2h-1v1h-1-2-1l-1-1s-1-1-1-2h0v-1h2c1 0 3-1 4-1h2v3h1c1-1 2-2 2-3l1-1 1-2z" class="X"></path><path d="M343 278c5-3 11-5 17-8-1 2-2 4-2 6h0-2 0l-1-1v-1c-2 0-6 3-9 3-1 1-3 2-5 4h0c-3 2-8 4-11 7v-1l13-9z" class="a"></path><path d="M278 333c0 1 0 2-1 3v2-1h-1c-3 2-6 6-8 9-1 0-2-4-2-5v-1c1 1 1 1 2 1 1-3 3-5 6-6 0-1 2 0 3 0h0l1-2z" class="L"></path><path d="M261 432l1 3c1 2 4 3 6 5h1c0 1 1 1 1 2 0 0 1 0 1 1h0c-1 1-1 0-2 0 0-1 0-2-1-3l-1 1v1 2h1l-1 1-1-1c-1 0-1 0-2 1 0-1-1-1-1-1h-1 0c-1-1 0-2 0-3 1-2-1-7-1-9z" class="N"></path><path d="M341 431v1c-3 2-9 2-12 2-1-1-2-1-2-2 1-2 3-3 4-5 2-2 3-4 4-7 2 2 4 4 5 7h-2v-1c-1 0-2-1-4-1l-5 7c3 1 8 2 11-1h1z" class="E"></path><path d="M334 425l2-2h0c1 1 2 2 2 3-1 0-2-1-4-1z" class="b"></path><path d="M335 468c-1 0-2 0-3-1h0 0l2-2 3-2c3-2 6-6 8-9l2 5h0c0 1 0 3 1 4h-1c0-1-1-1-1-2h-1c-1 1-2 2-3 2h0l-2 3h0c-1-2 0-2 1-3h-1c-1 0-2 0-2 1-2 1-3 2-4 3l1 1z" class="O"></path><path d="M339 269v3 1l1 1c0 1 0 2-1 3h0c-1 1-2 1-2 2l-1 1v-1c0-1-1-1-1-1-1-2-2-3-3-5h0c0-1 5-4 7-4z" class="Z"></path><path d="M338 464c0-1 1-1 2-1h1c-1 1-2 1-1 3h0l2-3h0c1 0 2-1 3-2h1c0 1 1 1 1 2v2h-2c0 1-1 1-1 1-1 0-2 2-2 3h0l-1 1h-1l-1-1h-2c-1 0-2-1-2-1l-1-1c1-1 2-2 4-3z" class="V"></path><path d="M338 464v2l-1 1 1 1 1 1h-2c-1 0-2-1-2-1l-1-1c1-1 2-2 4-3z" class="F"></path><path d="M308 371c1 0 2 0 2 1h1l1-1h1c1 0 1 0 2-1v1c0 1 1 0 1 1s-2 3-3 4h1l3 1v1c-1 1-2 1-3 1s-1 0-1 1h-4 0v1 1c-1-3-1-7-1-11z" class="s"></path><path d="M263 416l1-1c0 5 1 7 4 11 3 3 8 4 12 5 4 0 8-1 12-1h1c-2 2-7 4-7 8v1l-1 1v1-3c0-1 1-2 0-3 0 1-1 1-2 1 1-1 1-1 2-1 2-1 3-2 4-3v-1l-1 1c-3 1-10 0-13-1l-5-2c-3-3-5-6-7-9v-4z" class="g"></path><path d="M334 425c2 0 3 1 4 1v1h2c1 1 2 3 1 4h-1c-3 3-8 2-11 1l5-7z" class="P"></path><path d="M340 427c1 1 2 3 1 4h-1 1c-2-2-2-3-3-4h2z" class="i"></path><path d="M341 281l-2 9h-1l-1-1h-1v-1h0c-3 1-6 4-8 6-2 0-2 1-4 2 0 0-1 0-1 1 2-3 5-6 7-9 3-3 8-5 11-7z" class="F"></path><path d="M338 285h1v2h-1v-2z" class="V"></path><path d="M355 282v-7l1 1h0 2 0v1c1 1 1 2 1 3v2h0l3-1 6-4-1 2h1c-3 2-6 4-8 6-2 1-5 4-6 4s-1 0-1-1l3-1v-1c1 0 2-1 3-2h-1v1h-1l-1 1h0-1v1h-2v-1l2-1v-3z" class="P"></path><path d="M355 282v-7l1 1h0 2 0v1c0 2 0 5-1 7l-2 1v-3z" class="D"></path><path d="M263 352c1 1 1 1 2 1l1-1c1 1 1 1 1 2v3 1c-1 2-2 4-4 6h0c0 1-1 2-1 3h-1 0l-1-1v-4c-1-1 0-2 0-3v-1l1-4c0 1 0 1 1 2 0-2 0-2 1-4z" class="H"></path><path d="M263 352c1 1 1 1 2 1l-2 2v1h-1c0-2 0-2 1-4z" class="N"></path><path d="M260 358l1-4c0 1 0 1 1 2h1c0 1-1 1 0 2v2c-1 1-1 1-2 1v-2h-1v-1z" class="C"></path><path d="M271 251c1-1 1-1 2-1l2-2h0-2-4c-1-1-2-2-2-3v-1c-1-1-1-1-1-2l2-2v1c0 1-1 2-1 3l3 3c2 0 3 0 4-1 2-1 3-2 4-3l1-1 1-1h0c-1 2-1 3-2 5s-1 3-2 5l-3 9v7c-1 0-1 0-2-1 1-2 1-4 1-7 0-1 1-2 1-3h-1c-1 1-1 1-1 2v1h0l-1-1c1-2 1-3 2-5l1-1-2-1z" class="F"></path><path d="M273 252c0 1 1 1 1 1 0 1 0 1-1 2-1-1-1-1-1-2l1-1z" class="V"></path><path d="M323 360h0v2 1l-2-2c1 2 2 4 3 5v2h1c1 0 1 0 2-1 0-1 0-2 1-3 0-1 0-2 1-3v-1c1 3-1 6 0 8v-1l1 1c-2 2-4 4-6 5v1c-1-1 0-3-2-3-1 1 0 3-1 4v-1c0-2 0-5-1-6l-1-3v-1l-1-3v-1l1 1h2c1-1 1-1 2-1z" class="F"></path><path d="M338 412v1c3 0 5-1 8-1 1 0 2-1 4-1-1 3-1 5-1 8v2c-1 0-1 1-1 1h-2c0-1 0-1-1-2h0-1c0-1 0-1-1-2h0c-1-1-1-2-2-2l-1-1h-2s-1 0-1-1h-1 0c-1-1-2 0-3-1 0-1 2 0 3-1h1 1z" class="s"></path><path d="M280 324c1 1 1 1 1 2l-2 8v2l-1 3 1 1 1 1v1l-1 2h1c1 0 1-1 2-1l1 1c-2 1-4 3-6 4v1h0c0 7 0 16 3 22v1h0c-2-3-3-7-4-11v-13c0-4 1-7 1-10v-2c1-1 1-2 1-3 0-3 1-6 2-9z" class="l"></path><path d="M277 349v-2c0-3 1-5 1-8l1 1 1 1v1l-1 2h1c1 0 1-1 2-1l1 1c-2 1-4 3-6 4v1h0z" class="N"></path><path d="M337 449h1c0 2 0 2 1 3h1c-5 6-11 11-18 13-2 1-5 2-7 3h-1 0l1 1c-2 1-4 1-6 1l-1-1h1c2 0 3 0 4-1l-1-1h1c5-1 10-4 15-7v-2c1-1 1-2 3-2v-1l4-3s1 0 2-1h0c1-1 1-1 0-2z" class="T"></path><path d="M337 449h1c0 2 0 2 1 3l-2 2h-1l1-3h0c1-1 1-1 0-2z" class="C"></path><path d="M331 456c0 1 0 2-1 3l-2 1v-2c1-1 1-2 3-2z" class="b"></path><path d="M335 452s1 0 2-1l-1 3h1c-1 1-1 2-2 2-2 0-3-1-4-1l4-3z" class="H"></path><path d="M277 409c1 0 2 1 3 1l1 1c3 1 7 2 10 2l8 2c2 0 4 0 5 1 0 2 0 2-1 3-1 2-3 2-5 2-3 1-8 0-12 1-1 0-3-2-4 0h-1 0c0-4 0-6-2-8-1-1-1-1-2-1l-1-1h2c2 2 3 3 4 6 0 1 0 1 1 2h1 1 4 1c2 1 4 0 5 0h0c1 0 2-1 3-2l-1-1c-1 0-1 0-2-1v-1l-3-1h-1-2c-2-1-3-1-4-1h-1c-1-1-2-1-3-1v-1c-2 0-3-1-4-2z" class="L"></path><path d="M295 415h2l2 1h1l1 1h-1c0 1-1 1-1 2-1 1-2 1-4 1 1 0 2-1 3-2l-1-1c-1 0-1 0-2-1v-1z" class="F"></path><path d="M351 301h0c1-1 0-4 1-5 0 1 1 1 1 2 0 0-1 0-1 1 0 2 0 4 1 6 0 0-1 0-1 1h0v20c-1 0 0-6-1-7h-5 0-1-2-2l-1 1-1-1h0l1-1h1 3s1-1 2-1h0c1 0 1-1 1-2v-1c1-1 0-1 1-2 0-3 0-6 1-9v-1c1-1 1 0 2-1z" class="P"></path><path d="M349 303v-1c1-1 1 0 2-1v4h-1c-1-1-1-1-1-2z" class="N"></path><path d="M337 263c-3 0-8 1-11 3 0 1-1 0-2 0-1 2-4 2-6 3-1 0-2 0-3 1h-1c-1 0-2 1-4 1 1-2 12-4 14-5v-1c2 0 5-1 7-1 4-1 7-2 10-3 2-1 4-3 6-2h0c0 1-1 2-1 2v2l1 1h1l-2 2c0 2 1 4 1 7h0c-2-2-3-4-3-6-2 0-4 1-5 2-2 0-7 3-7 4-6 2-11 5-16 9-3 2-7 5-10 8v-1l2-3 12-8 20-11c2-1 4-2 5-3v-1h0-2v-1l1-1c-2 0-5 1-7 2z" class="p"></path><path d="M321 295c3-3 6-6 9-8v1c-2 3-5 6-7 9-9 10-17 21-21 34v1c-2 5-3 12-2 17 0 2 1 4 1 6h0c-1-1-1-2-1-3-1-1-1-1-1-2 0-3-1-5 0-9v-3c1-5 2-9 4-14l1-1s0-1 1-2c0-1 0-1 1-2 1-3 4-7 5-10l4-7c1 0 1-1 2-2l2-2 2-3z" class="G"></path><path d="M356 247v-1l1-1v1l-1 2 1 1h1v1h1 1v-1h2s0 1 1 0h0c1 1 2 1 3 1v-1c2 1 3 2 4 2-2 0-4 1-6 1h-2c-1 0-1 0-2 1l-1-1v3h-2-5 0a126.62 126.62 0 0 0-28 6l-11 4c-2 0-4 1-6 2 2-2 6-3 8-4 9-3 18-6 27-7l8-1v-3h-2c1 0 1-1 2-2 1 1 1 1 1 2h1c1-2 2-4 4-5z" class="M"></path><path d="M356 247c-1 2-2 5-2 7l2-3h1c-1 1-1 2-2 3h0-2c-1 0-1 0-2 1h-1v-3h-2c1 0 1-1 2-2 1 1 1 1 1 2h1c1-2 2-4 4-5z" class="P"></path><path d="M343 240c1 0 2 0 2 1h3 4c-1 2-3 1-5 1 5 1 10 0 14 1h-1-6v2c-2 0-3 0-4 1 0 1-1 3-1 4h1c-1 1-1 2-2 2h2v3l-8 1v-1h0v-1h1c1-1 1-2 1-2 0-1 1-1 1-3l-1 1v1l-1-1c1-2 1-4 2-7-1 0-4 0-5 1 0 1-1 1-1 2 0 3-1 5-1 8h-1c0-3 1-6 1-8h-1c0 1 0 1-1 2 0-1 0-3 1-4l1 1v-1h-1v-1-2c2 0 4 0 6-1z" class="D"></path><path d="M343 240c1 0 2 0 2 1h3 4c-1 2-3 1-5 1s-8 0-10 1v-2c2 0 4 0 6-1z" class="H"></path><path d="M343 254l1 1c1-1 1-3 2-4 0-2 1-5 2-6 1 0-1 2-1 3v2 1 1h1 2v3l-8 1v-1h0v-1h1z" class="N"></path><path d="M349 442v3h2l1 7c0 2 1 4 1 7 1 4 0 8-1 13h1v-1l1 2h-2v3c0 2 1 6 0 8h0-1c-1-1-2-4-3-5 0-1-1-2-1-2 0-1-1-1-2-1l-5-3v-1c2 1 5 3 7 3h0c1 0 1 0 1-1 2-1 2-12 2-14-1-6-2-12-1-18z" class="h"></path><path d="M352 484c-1-1-3-3-3-4v-2h-1v-2h1v1h1l1-1 1 1c0 2-1 4 0 7h0z" class="V"></path><path d="M352 452c0 2 1 4 1 7 1 4 0 8-1 13h1v-1l1 2h-2v3c0 2 1 6 0 8-1-3 0-5 0-7l-1-1-1 1c-1-3 0-8 1-11v-9c0-1-1-3 0-4 0 0 0-1 1-1z" class="i"></path><path d="M351 466v-6c1 1 1 1 1 2 0 3-1 6 0 9v1 1 3c0 2 1 6 0 8-1-3 0-5 0-7l-1-1-1 1c-1-3 0-8 1-11z" class="P"></path><path d="M271 251l2 1-1 1c-1 2-1 3-2 5l1 1h0v-1c0-1 0-1 1-2h1c0 1-1 2-1 3 0 3 0 5-1 7 1 1 1 1 2 1v1l2 11c0 1 0 3 1 4v1l3-2h1c-1 1-1 1-1 2l1 1s-1 0-1 1c-1 0-2 1-3 1-1 1-3 3-4 3h-1l1-1h1v-1c-1 0-1-1-1-2l-1-1h0v-1c-1-5-1-10-1-14v-2-1c1-1 1-2 1-4h-1v-1c0-1-1-1-1-2 0 0-1 0-1-1v-1-3c0-1 1-1 1-2l2-2z" class="s"></path><path d="M275 279s0 2-1 2v-2-3h0l-1 4c0-1 1-3 0-4v-1c-1-1-1-5-1-6l1-1 2 11z" class="X"></path><path d="M283 332l12-3h4v2c-1 0-1 0-1 1l-1 1c-1-1-1-1-1-2h-2 0-1v6c-1 0-2 1-3 2h-2v1h-1l-1-1c-1 1-2 1-2 2v1l-1 2-1-1c-1 0-1 1-2 1h-1l1-2v-1l-1-1-1-1 1-3v-2h1c1-1 1-1 3-2z" class="j"></path><path d="M283 333l2 1c0 1 0 2-1 2 0 1-1 1-2 1v-1l1-1v-2zm4 7c0-3-1-5 0-7l1-1v1c0 2-1 5 0 6v1h-1z" class="Z"></path><path d="M284 341c-1 0-1 0-1-1-1 1-1 2-2 2 0-1-1-1-1-2 1 0 1-1 2-1 1-1 3-2 4-1v1c-1 1-2 1-2 2z" class="N"></path><path d="M279 334h1c1-1 1-1 3-2v1 2l-1 1v1l-1 1-2 2-1-1 1-3v-2z" class="F"></path><path d="M279 336h2v2l-2 2-1-1 1-3z" class="Z"></path><path d="M291 332l2-1v6c-1 0-2 1-3 2h-2c-1-1 0-4 0-6 2 0 1-1 3-1z" class="V"></path><path d="M291 332l2-1v6c-1 0-2 1-3 2 0-2 0-2 2-4 0-1 0-2-1-3z" class="F"></path><path d="M379 251c6-2 12-1 19-1 0 0 1 0 2 1h-1l-1 1v1l-1 1c-2-1-6-1-8-1-7 1-13 1-20 2-3 1-8 2-11 2 1-1 2-2 3-1h1v-1h2 2-2-4-1-2c-2 0-3 1-5 0h0 0 5 2v-3l1 1c1-1 1-1 2-1h2c2 0 4-1 6-1h1l2 1c2-1 4-1 6-1z" class="L"></path><path d="M373 252c2-1 4-1 6-1 2 1 4 1 6 1l-12 1h-3v-1h3z" class="I"></path><path d="M370 251h1l2 1h-3v1h3c-5 1-10 0-14 2v-3l1 1c1-1 1-1 2-1h2c2 0 4-1 6-1z" class="h"></path><path d="M379 251c6-2 12-1 19-1 0 0 1 0 2 1h-1l-10 1h-4c-2 0-4 0-6-1z" class="z"></path><path d="M341 439l4-1c0 5-1 10-5 14h-1c-1-1-1-1-1-3h-1c1 1 1 1 0 2h0c-1 1-2 1-2 1-1 0-3-1-4-1h0l-1-1c-3 0-4 0-6-1 1-1 2-1 3-1 2-1 6-2 8-3 0-1 0-1 1-2h0c1-1 0-1 0-2h-2c1-1 1-1 2-1h1c1 0 1-1 2-1h0 2z" class="m"></path><path d="M331 451l6-2c1 1 1 1 0 2h0c-1 1-2 1-2 1-1 0-3-1-4-1h0zm5-8c1-1 0-1 0-2h-2c1-1 1-1 2-1h1c1 0 1-1 2-1h0 2v2h-1l1 1c1-1 1-2 2-2h1v3c-1 1-1 1-2 1h1c-3 1-5 0-7-1z" class="N"></path><path d="M324 449c1-1 2-1 3-1 2-1 6-2 8-3 0-1 0-1 1-2h0c2 1 4 2 7 1 0 2-1 3-1 4l-1 1c0-1-1-1-2-1-1-1-8 2-9 2-3 0-4 0-6-1z" class="Z"></path><path d="M306 286h2l-2 3v1h0c1 1 2 2 3 2v1h1 2c2-1 3 0 5-1l4-1 1 1c-3 1-7 1-10 2-2 0-4 0-5-1s-1-2-2-3c-1 1-5 4-5 5v2c-1 0-2-1-2 1 1 0 1 0 1 1h-2v1l1 2c1 1 2 2 2 3 0 0 0 1 1 2 0 0 1 1 2 1h1 0c1 1 2 1 3 1l1 1h-1c-4 0-6-2-8-5l-3-5c-6 8-12 16-15 26 0-1 0-1-1-2h0c0-3 1-6 2-8 4-12 16-21 24-30z" class="L"></path><path d="M309 225l7-3h1c-3 2-6 3-9 5-4 2-6 6-8 10l15 1h5c0-1 1-1 1-2s2-2 3-4c1 0 3-1 4-2h1a30.44 30.44 0 0 0-8 8c5 1 10 1 14 2v1c-5-1-9-1-14-2-1 3-3 6-3 9-1 3-1 6-1 9l-2-1s-1-1-1-2c-2-3-2-6-2-9 1-3 2-4 3-6l1-1h-16c0 1-1 2 0 3v1c-1-1-1-3-1-4v-1c3-5 5-8 10-12z" class="m"></path><path d="M316 239h4c-2 6-3 10-4 16v1c-2-2-3-4-3-6-1-4 1-8 3-11z" class="t"></path><path d="M334 487c1-1 0-1 0-2h2l2 2h0c1 1 1 1 2 1v1l1 1v2h1v2h0c1 3 0 7 0 10v3l-1 1v1c0 1 0 1-1 2v-2h0v-2c-1 0-2 0-3 1v-1c-1-1-2 0-3 0v-1c1-1 2-2 3-2h1l1-1c-2-2-3-5-5-6h-1-1c-1-1-2 0-3 1h-1 0v-1-1h0v-2s0-1 1-2h0v-1l3-3c1 0 2 0 2-1z" class="V"></path><path d="M339 498l2 2v4h-4 1l1-1s1 0 1-1h0l-2-2 1-2z" class="Z"></path><path d="M334 487l2 1h0c-1 1-1 1-2 1 0 1 0 2-1 2l-1 1v1c2 0 3 0 4-1l1 1h-2c-1 1-1 1-1 2 3 0 3 1 5 3h0l-1 2 2 2h0c0 1-1 1-1 1-2-2-3-5-5-6h-1-1c-1-1-2 0-3 1h-1 0v-1-1h0v-2s0-1 1-2h0v-1l3-3c1 0 2 0 2-1z" class="C"></path><path d="M349 494h0c1-1 1-1 2 0 2 5 2 11 2 16v17 23h0v-1c0-1-1-2-1-3l-1-15c1-6 0-13 0-19 0-2 1-5 0-7h-1c0 1 0 1 1 2v1c-1 1-1 3-2 5 0 3-3 6-5 9h0c1 1 2 2 2 4v1 1c0 1 0 1 1 2v2l1 1h-1v2c-1 1-1 1-1 2v1c-1 0-2-1-3-1v-3c-1 0-1 0-1-1-1 1-1 1-1 2l-1 2-1 1c0 1-1 2-2 3s-3 2-4 3c2-3 5-5 6-8 1-2 1-4 0-6 0-1 0-1-1-1l-2 1h0l3-4c5-4 10-12 10-19v-3c-1-1-1-2-1-3v-7h1z" class="K"></path><path d="M348 501l1-1h0v2h1l1-2v3l-2 2v2-3c-1-1-1-2-1-3z" class="L"></path><path d="M348 494h1l2 1c0 1-1 2-1 3 1 0 1 1 1 2l-1 2h-1v-2h0l-1 1v-7z" class="C"></path><path d="M343 527l-1 1h-2c0-2 3-5 4-6 1 1 2 2 2 4v1 1c0 1 0 1 1 2v2l1 1h-1v2c-1 1-1 1-1 2v1c-1 0-2-1-3-1v-3c-1 0-1 0-1-1-1-1 0-2-1-3v-1c1-1 2-1 2-2z" class="N"></path><path d="M343 527c1 2 0 7 1 7h2l1 1c-1 1-1 1-1 2v1c-1 0-2-1-3-1v-3c-1 0-1 0-1-1-1-1 0-2-1-3v-1c1-1 2-1 2-2z" class="P"></path><path d="M344 522h0c2-3 5-6 5-9 1-2 1-4 2-5v-1c-1-1-1-1-1-2h1c1 2 0 5 0 7 0 6 1 13 0 19l1 15c0 1 1 2 1 3h-1c0 1-1 2-1 3h0v1c-1 0-1 1-1 1h-1l-1-1 1-1c0-2-1-2-2-3l-4-3-2-1c1-2 2-4 4-6l1-1v-1c0-1 0-1 1-2v-2h1l-1-1v-2c-1-1-1-1-1-2v-1-1c0-2-1-3-2-4z" class="s"></path><path d="M343 546c1 0 2 0 3 1 1 0 1 1 1 2l-4-3z" class="X"></path><path d="M351 531l1 15c0 1 1 2 1 3h-1c0 1-1 2-1 3h0v1c-1 0-1 1-1 1h-1l-1-1 1-1h1v-2-4c0-1-1-1-1-2 1-1 1-3 2-4v-2c1-2 0-5 0-7z" class="P"></path><path d="M259 403c3 0 1-1 2-2h1l1 1c1 0 1-1 1-1 1 1 3 2 4 3 1 0 1 0 2 1 0 1 0 0 1 0v1h1l2 1 1 1c1 1 1 1 2 1 1 1 2 2 4 2v1c1 0 2 0 3 1h1c1 0 2 0 4 1h2 1l3 1v1c1 1 1 1 2 1l1 1c-1 1-2 2-3 2h0c-1 0-3 1-5 0h-1-4-1-1c-1-1-1-1-1-2-1-3-2-4-4-6h-2l-4-2c-2-1-3-1-5-1l-2 2v1 2l-1 1-1 1c-1 1-1 1-2 1v1 2h0c-1 0-1-1-2-1v-3-13z" class="s"></path><path d="M360 285v1s-1 1-1 2c-1 1 0 3 0 4-1 4-1 8 0 12v1 2 5 4l-1 5v7c-1-1 0-2-1-4v3-39c-2 3-4 5-5 7-4 5-8 11-10 17-1 3-2 5-3 7h0c0 2-1 5-1 6l-1 1c0 11 0 21 4 31 0 3 1 5 3 7v1c-6-7-8-25-8-33 1-16 8-29 17-41-22 16-39 38-44 66-1 4-1 9-1 14 0 4 0 8 1 11 1 5 3 12 7 16 1 1 2 0 3 0s1 0 2-1v-2h0 1c0 1-1 2-1 3-1 1-2 1-3 1-2 0-3-1-3-2-5-5-7-15-8-22v-11l1-5c1-7 3-14 5-20 5-11 11-22 20-32 6-6 13-12 21-18 1 0 4-3 6-4z" class="M"></path><path d="M357 324v-4c1 0 1 0 1 1v7c-1-1 0-2-1-4z" class="f"></path><path d="M352 241l39 2c1 1 3 1 5 2 2 0 4 1 6 1 0 1 1 1 1 1h-2v1h-3v2c-7 0-13-1-19 1-2 0-4 0-6 1l-2-1h-1c-1 0-2-1-4-2v1c-1 0-2 0-3-1h0c-1 1-1 0-1 0h-2v1h-1-1v-1h-1l-1-1 1-2v-1l-1 1v1c-2 1-3 3-4 5h-1c0-1 0-1-1-2h-1c0-1 1-3 1-4 1-1 2-1 4-1v-2h6 1c-4-1-9 0-14-1 2 0 4 1 5-1z" class="W"></path><path d="M354 245v-2h6l-1 2v1h-1v-1h-4z" class="B"></path><path d="M371 251c3-1 6-1 9-2 6 0 12-1 18-1v2c-7 0-13-1-19 1-2 0-4 0-6 1l-2-1z" class="L"></path><path d="M352 241l39 2c1 1 3 1 5 2 2 0 4 1 6 1 0 1 1 1 1 1h-2c-4 0-7-2-11-2-9-2-19-2-29-2-4-1-9 0-14-1 2 0 4 1 5-1z" class="l"></path><path d="M319 440c-3 1-7 1-10 2-4 0-9 0-13 1-1 0-2 0-3 1h0c1 1 2 1 3 1s0 0 1 1h-4-1c-1-3 0-8 0-10 1-4 4-6 7-7 1-1 3-2 4-3h0c4-3 7-7 11-10h0v1c0 1-2 1-2 2l-3 3h2l12-2 1-1c2 0 3 1 4 3 0 1 0 2-1 3-2 1-3 2-4 4-1 1-2 4-3 5h-5c-6 0-11 2-17 2-1 1-4 1-4 1-1 1-1 4-1 5 3 0 6-1 9-2 4 2 9 0 13 0l3-1h1v1z" class="q"></path><path d="M321 421c2 0 4 0 6 1v1c-1 1-1 1-2 1l-17 4h-6l6-5h3c3-1 7-1 10-2zm2 5h0 1c-2 2-3 4-4 6l-25 4h-1c0-1 0-3 1-3 1-1 3-3 5-3 3 0 6-1 9-2l14-2z" class="t"></path><path d="M343 319h2 1 0 5c1 1 0 7 1 7v5l-1 8v1h0 1 0c0 1 0 2 1 3l-1 12c1 5 1 9 0 13 0 3-1 6-1 10v6c0 3 2 6 1 10v12c-1 6-3 11-1 17v2c1 3 2 6 1 10h-1v-5-6l-1-1c-1 2 0 5-1 7v1l-2 1v-1h0c1 0 1-1 1-2 1-2 1-5 0-7 0 0 0-1 1-1v-2c0-3 0-5 1-8-2 0-3 1-4 1-3 0-5 1-8 1v-1c2 0 7 0 10-1 0-1 0-1 1-1h1c1-1 1-4 1-6l-24 6-1 1c-1 0-2-1-2-3h0l6-3c3-3 8-3 11-6h0c3 0 3-1 5-2 1-1 1-2 2-3h-1c0-2 1-4 2-5v-1c0-1 0-1 1-1l-1-1c0-1 0-2 1-3 1-9 2-19 2-28v-9c-1-2-2-4-2-6-1-3-2-5-4-7-2-3-5-5-8-7h-1l1-1c0-1 1-4 1-6l1 1 1-1h2z" class="J"></path><path d="M330 406h4c-1 0-2 1-3 1h0c1 1 2 1 2 1l-8 1v-1s1-1 2-1 2-1 3-1z" class="N"></path><path d="M349 386c0-1 0-2 1-3 1 2 1 4 1 6 0 1-2 4-3 5h-1c0-2 1-4 2-5v-1c0-1 0-1 1-1l-1-1z" class="V"></path><path d="M346 397c2-2 4-4 5-7 0 2 1 4 0 6l-6 3 1-2z" class="Z"></path><path d="M343 319h2 1 0v9 3h0c-1 0-3-3-5-4-1 0-2-1-3-2 0-1 1-4 1-6l1 1 1-1h2z" class="V"></path><path d="M340 320l1-1h2-1v3l-1 1c-1-1-1-1-1-2h0v-1z" class="Z"></path><path d="M346 319h5c1 1 0 7 1 7v5l-1 8v1c-1-2-2-5-3-7-1 0-1-1-1-1-1-2 0-3-1-4v-9z" class="X"></path><path d="M348 333v-4c0-1 0-1 1-2l1 1 1 1v1 1h1l-1 8v1c-1-2-2-5-3-7z" class="C"></path><path d="M341 399h0c3 0 3-1 5-2h0l-1 2 6-3v2h0c0 2 0 4-1 5s-3 2-5 2l-12 3s-1 0-2-1h0c1 0 2-1 3-1h-4v-1c3-3 8-3 11-6z" class="t"></path><path d="M341 399h0c3 0 3-1 5-2h0l-1 2 6-3v2h0v-1l-6 3c-2 1-3 2-4 3-3 1-5 2-7 3h-4v-1c3-3 8-3 11-6z" class="b"></path><path d="M316 222c1-1 2-3 3-4 3-6 14-9 21-11l1 1-6 2h-1v1h1c1 0 1 0 2 1h4v1l1 1c0 1 0 1-1 1-2 1-4 0-6 1v2h0l-2 2-5 6-3 4c-1 0-1 1-1 2-1 2-3 3-3 4s-1 1-1 2h-5l-15-1c2-4 4-8 8-10 3-2 6-3 9-5h-1z" class="t"></path><path d="M316 222c1-1 2-3 3-4 3-6 14-9 21-11l1 1-6 2h-1v1h1c1 0 1 0 2 1h4v1c-5 1-9 2-14 5h0l-3 1 2-2c3-2 6-4 9-5h-2c-4 0-9 2-12 4-2 2-2 5-4 6h-1z" class="J"></path><path d="M341 213l1 1c0 1 0 1-1 1-2 1-4 0-6 1v2h0l-2 2-5 6v-2c-1 0-2 0-3 1l-2 2c-2 1-3 3-3 4v1l-1 1v-1h0v-2c0-1 1-1 1-2 0 0 0-1 1-1v-1c0-1 0 0 1-1 1-3 3-4 5-6v-1h0c5-3 9-4 14-5z" class="O"></path><path d="M322 225c1 0 1 0 1-1 1-1 3-2 3-3h1c1-1 1-1 2-1 1-1 1-1 2-1 0 2-5 3-6 6l-2 2c-2 1-3 3-3 4v1l-1 1v-1h0v-2c0-1 1-1 1-2 0 0 0-1 1-1v-1c0-1 0 0 1-1z" class="P"></path><path d="M300 477c7-3 15-4 22-6 1 0 2 1 3 2h-1c1 1 2 2 3 2s1 0 2 1l2 1h-6c-3 1-7 3-10 2 2-1 10-1 11-3-1-1-1-1-2-1h-1c-1 0-3 0-4 1-2 1-5 0-8 1-12 2-20 8-27 18-1 1-2 3-3 4v2c0 2-1 4-1 7 0 7 2 14 6 20 1 1 2 1 3 2h0-1v1c1 2 5 4 7 5h-1c-1 0-2-1-2-1h-1 0c-1 2-1 4-1 5s1 2 2 2h0l6 3h-1c-3-1-6-3-9-4-6-1-13 1-19-4-3-2-2-6-4-9 0 0-2-1-3-2h0c0 3 0 9-2 11h0c1 1 1 1 1 2 0 4 1 6 3 8l1 1h0c0 1-1 2-1 3v3 1h-1l-1-1-4-4c-1-3 1-7 1-9 1-2 0-4 1-6s1-4 1-6c1-5 1-10 0-15 0-3-1-6-1-9 0-2 1-4 1-6v-1c1-3 5-8 8-10-1 2-2 3-3 4l-1 2s0 1 1 2c-2 1-1 4-1 6-1 1 0 3-1 4v2h1c1 0 1 0 1 1-1 2-1 6 0 8v1l1-1c2-1 2-1 2-4v-1-9-5h1c1 1 0 3 0 5v4l2 1c0 1-1 1-1 1-1 2-1 4-1 6h1c1-1 1-2 2-2h1l5-3v-3h-1l-1 1-1-1s0-1 1-2c1 1 1 1 2 1 1-1 1-5 2-6v-3c1-2 3-4 4-6 2-2 3-4 5-5 5-5 11-8 17-10h0-1l-5 1h-1z" class="l"></path><path d="M321 473h1 1l-1 2h-3-1l-1-1h2c0-1 1-1 2-1z" class="O"></path><path d="M272 532c1 1 1 2 1 3l-1 3h-1l-2-2c2 0 2 0 3-1v-1c-1-1 0-1 0-1v-1z" class="s"></path><path d="M285 530v-1l1 1-2 2-7 3c-1 1-1 1-2 0h1c3-2 5-4 9-5z" class="N"></path><path d="M262 554c0-1 0-1-1-2h0v-1c1 0 1-1 0-1v-6s0 1 1 1l2 3c-1 1-1 2 0 3h0v3 1h-1l-1-1z" class="X"></path><g class="V"><path d="M269 536c0-1-1-1-1-2s0-1 1-2h0-2v-4h1c0 1 1 1 2 2 1 0 1 1 2 2v1s-1 0 0 1v1c-1 1-1 1-3 1z"></path><path d="M262 513c2 1 2 1 3 2v1 2l-1 1v1l1 1c1 1 1 1 2 1 1 1 1 1 2 1s2 0 3 1v1c1 0 1 0 1 1h-2-1v1c-1 0-1 0-1 1-3-1-4-1-6-3 0-1 0-3 1-5h-1v-2c0-2 0-4-1-5z"></path></g><path d="M265 494s0 1 1 2c-2 1-1 4-1 6-1 1 0 3-1 4v2h1c1 0 1 0 1 1-1 2-1 6 0 8v1h-1v-2-1c-1-1-1-1-3-2v-1-2c0-2 0-4-1-5v-1c1 0 1-2 1-3v-2c1-1 1 0 1-1 0-2 1-3 2-4z" class="C"></path><path d="M265 502c-1 1 0 3-1 4v-2h-1 0-1v-2h3z" class="N"></path><path d="M264 519l15-8 1 7c-1 1-3 2-4 2-2 0-5 2-7 3-1 0-1 0-2-1-1 0-1 0-2-1l-1-1v-1z" class="t"></path><path d="M289 537c0 1 0 2-1 3 0 0-1 1-1 0h-3c-3-1-8 0-11-2 1-1 1-1 1-2h1v2c1 0 2-2 3-2 3-2 6-3 9-5v1c2 1 3 1 4 2v1h0-1s0 1-1 2z" class="s"></path><path d="M287 532c2 1 3 1 4 2v1h0-1s0 1-1 2v-2c-1 0-1 2-1 3h-1v-1c0-1 1-2 1-3-1-1-1-1-1-2z" class="V"></path><path d="M276 520c2 1 3-1 5 0v2h0c1 2 2 4 4 5v1h0c-3 1-5 3-7 4-1 1-3 1-4 2-2-3-2-5-5-6 0-1 0-1 1-1v-1h1 2c0-1 0-1-1-1v-1c-1-1-2-1-3-1 2-1 5-3 7-3z" class="t"></path><path d="M276 520c2 1 3-1 5 0v2h-1v6c0-2-1-5-1-6h-3v2c1 2 1 5 1 7h0-1c0-2 0-4-1-7-1 0-1 1-2 2 0-1 0-1-1-1v-1c-1-1-2-1-3-1 2-1 5-3 7-3z" class="X"></path><path d="M358 257c3 0 8-1 11-2 7-1 13-1 20-2 2 0 6 0 8 1v2c3-1 5-2 8-2l-2 2-1 1h0c-2 0-3 0-4 2-1 0-1 0-1 1 1 0 2-1 3-1l-2 2h0c1 0 2 0 3-1l1 2h-1l-4 2c1 0 1 0 2 1l-10 3c-7 3-14 6-20 11h-1-1l1-2-6 4-3 1h0v-2c0-1 0-2-1-3v-1c0-2 1-4 2-6-6 3-12 5-17 8v-1l3-3 1-1h0c0-3-1-5-1-7l2-2h-1l-1-1v-2s1-1 1-2h0l1 1h1c1-1 2-2 3-2 1-1 4-1 6-1z" class="y"></path><path d="M349 260h1c3 0 6-2 10-3-4 3-8 5-12 6-1 0-1 0-1 1l-1-1v-2s1-1 1-2h0l1 1h1z" class="N"></path><path d="M397 256c3-1 5-2 8-2l-2 2-1 1h0c-1-1-3 0-4 0-3 1-7 1-10 3-7 1-13 4-19 6-3 2-6 3-9 4h0c-6 3-12 5-17 8v-1l3-3 1-1h0c0-3-1-5-1-7l2-2c1-1 3-1 4-2v5h0l1-5h0l4-2c1-1 2-1 3-1 0 2-4 7-2 9l2 1c2-2 5-3 7-4 5-2 10-4 15-5l15-4z" class="V"></path><path d="M357 260c1-1 2-1 3-1 0 2-4 7-2 9l2 1-8 3 2-1c1-1 1-1 1-2 1-1 0-1 0-2 1-1 2-5 2-7h0z" class="Q"></path><path d="M388 260c3-2 7-2 10-3 1 0 3-1 4 0-2 0-3 0-4 2-1 0-1 0-1 1 1 0 2-1 3-1l-2 2h0c1 0 2 0 3-1l1 2h-1l-4 2c1 0 1 0 2 1l-10 3c-7 3-14 6-20 11h-1-1l1-2-6 4-3 1h0v-2c0-1 0-2-1-3v-1c0-2 1-4 2-6h0c3-1 6-2 9-4 6-2 12-5 19-6z" class="v"></path><path d="M360 270h0c0 1 1 1 1 2-1 2-1 5-2 8 0-1 0-2-1-3v-1c0-2 1-4 2-6z" class="b"></path><path d="M362 281v-2c1-2 2-1 4-2 0-1 1-1 2-2s3-2 5-2l5-1-10 5-6 4z" class="I"></path><path d="M388 260c3-2 7-2 10-3 1 0 3-1 4 0-2 0-3 0-4 2-1 0-1 0-1 1s-1 1-1 2l-1-1v-1h0l-10 5c1-2 3-3 4-5h0c1 0 1 0 2 1h1 1c1-1 1-2 2-2h1v1l1-2h-2c-2 1-5 2-7 2z" class="D"></path><path d="M378 272l19-8c1 0 1 0 2 1l-10 3c-7 3-14 6-20 11h-1-1l1-2 10-5z" class="F"></path><path d="M364 203h3c1 1 3 0 4 1-2 0-4 0-7 1l-2 1-1 1h4 3c1 0 3 0 4 1h4c2 2 5 2 8 4h0c2 1 3 2 4 4l1 1h0l-1 1-1-1h0c2 3 5 5 7 8v1h-1c1 0 2 1 3 1 2 0 3 1 4 1l1-1c2 1 3 2 5 3l2 1 3-1h4c2-1 3-2 5-2 2-2 4-3 6-4 0 1-1 2-1 3h1l-1 2v1s-1 2-2 2c-2 0-3 2-4 2s-2 1-2 1v1c-1 2-3 2-3 3v1l1 1c-2 1-5 2-7 2h0l-6 3c-2 0-4-1-6-1-2-1-4-1-5-2l-39-2h-4-3c0-1-1-1-2-1-2 1-4 1-6 1h-2v-1c-4-1-9-1-14-2a30.44 30.44 0 0 1 8-8h-1c-1 1-3 2-4 2 0-1 0-2 1-2l3-4 5-6 2-2h0v-2c2-1 4 0 6-1 1 0 1 0 1-1l-1-1v-1h-4c-1-1-1-1-2-1h-1v-1h1l6-2-1-1c2 0 5-1 7-1h1v-1h7c2 0 5-1 7-1 1 0 1-1 2-1z" class="v"></path><path d="M372 236c2-1 2-1 4-1 1 1 2 1 3 1h-7z" class="D"></path><path d="M371 225l1-1h1c0 1 0 1 1 2 1 0 2 0 4 1-3 0-6 0-9-1v-1h2z" class="W"></path><path d="M371 225l1-1h1c0 1 0 1 1 2l-3-1z" class="S"></path><path d="M376 235h7c2 1 5 1 5 1h0l-1 1h-2-1c-2-1-3-1-5-1-1 0-2 0-3-1z" class="J"></path><path d="M374 233v-1c-2-1-5 0-6 0h-1c3-2 9-3 12-3l-4 1v2c0 1-1 1-1 1z" class="I"></path><path d="M384 229l3 1c1 1 2 2 4 2l3 2v1l-1 1v-1c-1 0-1 0-2-1h0l-7-3h2s1 0 2 1l-2-1-2-2h0z" class="M"></path><path d="M379 229c0 1 3 1 3 1 0 1-1 0-1 1-1 0-1 1-1 1-2 0-2 0-3 1h0 1 0l-6 1v-1h2s1 0 1-1v-2l4-1z" class="D"></path><path d="M378 233h4l3 1c0 1-1 1-2 1h-7c-2 0-2 0-4 1-2 0-4 2-6 2 1-1 0-1 1-1 1-1 2-1 3-2l2-2v1l6-1h0z" class="I"></path><path d="M378 233h-1 0c1-1 1-1 3-1 0 0 0-1 1-1 0-1 1 0 1-1 1 1 2 1 2 1l7 3c-1 2-1 1-3 0h-3l-3-1h-4z" class="c"></path><path d="M394 234h0c2 1 4 1 6 2h0l-1 1h-1 0l-1 1h1l-1 1-5-2-4-1s-3 0-5-1c1 0 2 0 2-1h3c2 1 2 2 3 0h0c1 1 1 1 2 1v1l1-1v-1z" class="D"></path><path d="M392 237l1-1c2 0 3 0 5 1h0l-1 1h1l-1 1-5-2z" class="a"></path><path d="M353 210h5l2 1 1 1h-4c-2 0-4 1-6 1h-1c-2 1-6-1-7 1-1-1-1-2-1-3h1 4 6v-1z" class="h"></path><path d="M353 210h5l2 1 1 1h-4-2c-3-1-5 0-8-1h6v-1z" class="w"></path><path d="M347 215c1 1 2 1 2 2h3 0c-1 2-2 1-3 2h-2-1l-1 1c-1 0-2 1-3 2l-2-2 1-1c-2-1-2 0-3-2 1 0 3-1 4-1 2 0 4 0 5-1z" class="a"></path><path d="M341 219c2 0 3-1 5 0h0l-1 1c-1 0-2 1-3 2l-2-2 1-1z" class="d"></path><path d="M368 207c1 0 3 0 4 1-1 0-1 1-2 1h2v1h-1-1l-9 2-1-1-2-1h1v-2c3-1 6 0 9-1z" class="B"></path><path d="M359 210c4 0 7-1 11-1h2v1h-1-1l-9 2-1-1-2-1h1z" class="E"></path><path d="M350 234c2-1 3-2 5-3l-3 4c1 1 1 1 0 2 0 0-1 1-2 1h0l1 1v-1h4 0v1h-1c-1 0-2 0-3 1h-1c-1 1-1 0-2 1h-3c0-1-1-1-2-1v-1c2 0 3-2 4-3l3-2z" class="M"></path><path d="M350 234v1c-1 3-2 4-5 6 0-1-1-1-2-1v-1c2 0 3-2 4-3l3-2z" class="C"></path><path d="M372 208h4c2 2 5 2 8 4h0c2 1 3 2 4 4l1 1h0l-1 1-1-1h0l-4-1-1-1c-1 0-2 0-3-1h1l-1-2s0-1-1-1c-2-1-5-1-8-1h1 1v-1h-2c1 0 1-1 2-1z" class="Q"></path><path d="M378 211c4 1 7 3 9 6h0l-4-1-1-1c-1 0-2 0-3-1h1l-1-2s0-1-1-1z" class="B"></path><path d="M341 213v-1h-4c-1-1-1-1-2-1h-1v-1h1 1 2l5 1h0-1c0 1 0 2 1 3 1 0 3 1 4 1-1 1-3 1-5 1-1 0-3 1-4 1 1 2 1 1 3 2l-1 1-2 1c-1 0-3 0-4-1h-1l2-2h0v-2c2-1 4 0 6-1 1 0 1 0 1-1l-1-1z" class="K"></path><path d="M334 220c1-1 3-2 4-3 1 2 1 1 3 2l-1 1-2 1c-1 0-3 0-4-1z" class="S"></path><path d="M377 219c4 0 9 3 12 5 1 1 3 1 4 2 1 0 2 1 3 1l-1 2-6-3-7-4c-2 0-3-1-5 0l1 1h1c-2 1-4 1-6 1h-1l-1 1h-2l-1-1 1-1c1-1 2-1 3-1h0l2-2c2 0 3-1 5 1h2 2c-2-1-5-1-6-2z" class="M"></path><path d="M378 223c-1 0-3 0-4-1h3l1 1z" class="k"></path><path d="M388 236l4 1 5 2h2l5 1h0l-2 2c-1 1-2 1-4 1h-3v-1h1v1c2 0 2 0 3-1-2-1-4 0-6 0-1 0-3-1-3-1v-1-1h-3c-2 1-4 0-6 0h0c1-1 1-1 2-1l1-1h1 2l1-1h0z" class="I"></path><path d="M387 237c1 0 2 0 3 1 2 1 5 0 6 2h0c-1 0-1 0-1 1h3c-2 0-3 0-4 1h-1c-1-1-1-1-2-1l1-1c-1-2-3-1-4-1s-2-1-2-2h-1 2z" class="U"></path><path d="M388 236l4 1 5 2h2l5 1h0l-2 2-4-1h-3c0-1 0-1 1-1h0c-1-2-4-1-6-2-1-1-2-1-3-1l1-1h0z" class="h"></path><defs><linearGradient id="AC" x1="373.67" y1="223.444" x2="383.228" y2="225.973" xlink:href="#B"><stop offset="0" stop-color="#3e3c3d"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#AC)" d="M377 222c2-1 3 0 5 0l7 4c0 2 2 3 5 5-2 0-2 0-3 1-2 0-3-1-4-2l-3-1c-2-1-4-2-6-2-2-1-3-1-4-1-1-1-1-1-1-2 2 0 4 0 6-1h-1l-1-1z"></path><path d="M382 222l7 4c0 2 2 3 5 5-2 0-2 0-3 1-2 0-3-1-4-2l-1-2c-1-1-1-1-1-2h0v-1h0c-1 0-4-1-4-2l1-1z" class="m"></path><defs><linearGradient id="AD" x1="368.785" y1="214.512" x2="383.656" y2="217.978" xlink:href="#B"><stop offset="0" stop-color="#201e20"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#AD)" d="M376 218h-3c-2-1-5-1-8-1 1 0 3-1 5-1 1-1 4-2 6-2h3c1 1 2 1 3 1l1 1 4 1c2 3 5 5 7 8v1h-1c-1-1-3-1-4-2-3-2-8-5-12-5l-1-1z"></path><path d="M376 218h0c1-1 3-1 4 0 1 0 3 0 5 1 0 1 2 2 3 2v-1l-5-4h0l4 1c2 3 5 5 7 8v1h-1c-1-1-3-1-4-2-3-2-8-5-12-5l-1-1z" class="E"></path><path d="M364 203h3c1 1 3 0 4 1-2 0-4 0-7 1l-2 1-1 1h4 3c-3 1-6 0-9 1v2h-1-5v1h-6-4 0l-5-1h-2-1l6-2-1-1c2 0 5-1 7-1h1v-1h7c2 0 5-1 7-1 1 0 1-1 2-1z" class="W"></path><path d="M345 208h8c3 0 6-1 9-2l-1 1h4 3c-3 1-6 0-9 1v2h-1-5 0c-2 0-3 0-4-2h-4z" class="r"></path><path d="M353 210v-1c1-1 5-1 6-1v2h-1-5 0z" class="M"></path><path d="M343 208h2 4c1 2 2 2 4 2h0v1h-6-4 0l-5-1h-2-1l6-2h2z" class="l"></path><path d="M341 208h2c1 0 1 0 1 1v1h-6-2-1l6-2z" class="H"></path><path d="M389 226l6 3 1-2c2 0 3 1 4 1l1-1c2 1 3 2 5 3l2 1 3-1h4c1 0 2 0 2 2-1 0-2 1-3 1h-1c-3 2-9 3-13 3-2-1-4-1-6-2h0l-3-2c1-1 1-1 3-1-3-2-5-3-5-5z" class="Y"></path><path d="M394 231l4 2h-4v1h0l-3-2c1-1 1-1 3-1z" class="E"></path><path d="M396 227c2 0 3 1 4 1l1-1c2 1 3 2 5 3l2 1c-1 0-1 1-2 1h-4c-1 0-2 0-4-1 0 0-1 0-1-1h1 0l-3-1 1-2z" class="S"></path><path d="M402 232c-1 0-2 0-4-1 0 0-1 0-1-1h1c2 0 5 1 8 0l2 1c-1 0-1 1-2 1h-4z" class="d"></path><path d="M415 230c1 0 2 0 2 2-1 0-2 1-3 1h-1c-3 2-9 3-13 3-2-1-4-1-6-2v-1h4c1 1 3 0 4 1h3 0v-1h1l-4-1h4c1 0 1-1 2-1l3-1h4z" class="K"></path><path d="M415 230c1 0 2 0 2 2-1 0-2 1-3 1h-1-4c0 1 0 1-1 1s-1-1-1-1h-2 1l-4-1h4c1 0 1-1 2-1l3-1h4z" class="E"></path><path d="M415 230c1 0 2 0 2 2-1 0-2 1-3 1v-1c-1 1-2 1-3 1l2-2h0 0l-2-1h4z" class="J"></path><path d="M415 230c2-1 3-2 5-2 2-2 4-3 6-4 0 1-1 2-1 3h1l-1 2v1s-1 2-2 2c-2 0-3 2-4 2s-2 1-2 1v1c-1 2-3 2-3 3v1l1 1c-2 1-5 2-7 2h0l-6 3c-2 0-4-1-6-1-2-1-4-1-5-2h4 3c2 0 3 0 4-1l2-2h0l-5-1h-2l1-1h-1l1-1h0 1l1-1h0c4 0 10-1 13-3h1c1 0 2-1 3-1 0-2-1-2-2-2z" class="B"></path><path d="M425 227h1l-1 2v1s-1 2-2 2c-2 0-3 2-4 2s-2 1-2 1v1c-2 0-4 2-5 2 1-1 2-3 3-3l10-8z" class="Y"></path><path d="M417 236c-1 2-3 2-3 3v1l1 1c-2 1-5 2-7 2h0-1c-1-1-2-1-2-2l4-3h2c1 0 0 1 1 0h0c1 0 3-2 5-2z" class="I"></path><path d="M417 236c-1 2-3 2-3 3v1l1 1c-2 1-5 2-7 2h0c0-1 2-2 3-3 1 0 1 0 2-1l-1-1h0c1 0 3-2 5-2z" class="D"></path><path d="M399 239h4c2 0 4-1 6-1l-4 3c0 1 1 1 2 2h1l-6 3c-2 0-4-1-6-1-2-1-4-1-5-2h4 3c2 0 3 0 4-1l2-2h0l-5-1z" class="B"></path><path d="M399 239h4c2 0 4-1 6-1l-4 3c0 1 1 1 2 2h-3c-1 0-1 0-2 1s-3 0-4 0h0v-1c2 0 3 0 4-1l2-2h0l-5-1z" class="E"></path><path d="M342 222c1-1 2-2 3-2v1c0 1 1 1 2 1l1-1h0c0 1 1 1 0 1l-1 1c0 2 0 2-1 3s-2 1-3 1v1c1 0 1 1 2 1l1-1h3c0 1 0 2-1 3 0 1-1 1-1 1-1 1-1 2-1 3l1 1c-1 1-2 3-4 3v1c-2 1-4 1-6 1h-2v-1c-4-1-9-1-14-2a30.44 30.44 0 0 1 8-8h-1c-1 1-3 2-4 2 0-1 0-2 1-2l3-4 5-6h1c1 1 3 1 4 1l2-1 2 2z" class="s"></path><path d="M342 229h0 1v1l1 1-4 4c-1 1-1 3-3 4h0c1-3 2-6 4-9l1 1 1-1v-1h-1z" class="Q"></path><path d="M342 235c1 1 1 1 1 3v1 1c-2 1-4 1-6 1h-2v-1h3c1 0 3-4 4-5z" class="k"></path><path d="M341 230h-1c-4 2-8 4-10 8h-1 0-1c3-6 7-9 13-12-1 1-1 2-1 3h2 1v1l-1 1-1-1z" class="R"></path><path d="M342 222c1-1 2-2 3-2v1c0 1 1 1 2 1l1-1h0c0 1 1 1 0 1l-1 1c0 2 0 2-1 3s-2 1-3 1v1c1 0 1 1 2 1l1-1h3c0 1 0 2-1 3 0 1-1 1-1 1-1 1-1 2-1 3l1 1c-1 1-2 3-4 3v-1c0-2 0-2-1-3l6-6v-1l-1 1c-1 0-2 1-3 2l-1-1v-1h-1 0-2c0-1 0-2 1-3l5-3c-6 1-12 3-17 7h-1c-1 1-3 2-4 2 0-1 0-2 1-2l3-4 5-6h1c1 1 3 1 4 1l2-1 2 2z" class="M"></path><path d="M340 220l2 2c-1 0-3 2-4 2v-1h-3l3-2 2-1z" class="L"></path><path d="M333 220h1c1 1 3 1 4 1l-3 2h3v1c-4 1-7 3-10 6-1 1-3 2-4 2 0-1 0-2 1-2l3-4 5-6z" class="X"></path><path d="M333 220h1c1 1 3 1 4 1l-3 2c-2 1-3 1-4 2-2 1-4 5-6 5l3-4 5-6z" class="R"></path><path d="M421 240c3-2 4-1 7-1 0 1 0 1 1 2l1-2 1 2 1 1c0 1 1 3 3 4v-2c2 0 4-1 6 0 0 1 1 1 1 2h0c1 1 2 2 3 2 0-1 0-3 2-4v1c1 1 1 1 3 1h0l1 1 1 2h1c0 1-1 2-1 2v1l-1 1c-1 1-1 2-1 3l2-1c-1 1-1 2-1 3-1 0-1 0-1 1l-1 1h-2v1c2 3 5 6 9 7h0c1 0 2 1 3 1s1 0 1 1h0v1h0c-1 0-2-1-3-1-2 0-4-1-6-1h-6-1c-2 1-4 1-6 2-2 0-4 0-5 1-1 0-3 1-4 1l-2 1-3 1c-1 0-1 0-1 2h1c0 1-1 2-2 4h0v-1h0l-1 1h0l-1 1 1 1h1 0c-1 1-1 2-1 3v2h1 1l1 2 1-4 1 1v2h1l-1 4c1 0 1 1 1 2l2 6c1 4 3 9 5 12l1 1-1 2c1 1 2 2 2 3 1 1 2 3 2 4 2 3 4 4 6 6l3 2 1 3-2-1h-2c-2 0-3-1-4-2l-2-2h-1c-1 1-1 1 0 1v2c1 1 2 2 4 3l-1 1h0v1c2 2 5 4 7 5l3 2 7 4v2l-1-1v1l13 14c1 1 1 2 3 3s4 3 6 5 4 5 6 8c0 3 1 7 3 9 0 0 1 4 2 4h0c2 3 3 8 3 12 0 2 1 3 1 5l-1 1c0 1 1 2 1 3 0 2 1 5 1 7-1-2-1-3-3-4v3 3 1c0 1 0 2 1 3 1 9 4 17 9 25 2 2 5 5 7 8h-1c0-1-3-3-4-3v1l1 1v3c0 2 2 4 1 5h-1c0 2 1 3 2 4v1c0 1 1 2 2 3 0 2 1 6 2 7v1l1 1v3h-1v1l1 1v2c-1 1-2 0-3 0v-2l-1-1v-1c-1 1-1 2-2 3 0 1-2 2-3 3v2c-1-1-1-2-1-3 0 1 0 2-1 3l-1-1v-2h0l-2 1v-1h-2c1 1 1 3 1 4l-1 1 1 3h-1 0l1 4h0-4l-1 1 1 1-1 1c1 5 2 9 2 14l2 10c1 4 3 7 3 10 0 2 0 2-1 3-1 2-1 8 0 10 0 0 1 1 0 2 0-1 0-1-1-2 0 1 0 2 1 3 0 1 1 2 3 2v1c3 0 6 2 8 4 1 2 1 2 1 4 1 1 2 1 2 2l2-2c1 2 2 3 4 4l1 1h0-1v1c1 1 3 2 4 3h0l-2-1v1c1 4 3 5 3 10l-1 2c1 4 1 9-1 12-1 2-2 3-4 3v1h0c7 3 15 5 23 5 11 0 24-4 32-13 5-5 10-12 11-20v-6c-1-7-3-14-8-20h0-1c-1-2-3-2-5-3l-2-1c-1 0-2-1-3-1-2 0-4 0-5 1h-1s-1 0-1 1h-2c-2 1-3 2-4 2l1-2h0c-5 1-7 5-10 9h0c1-3 2-6 4-8h-1l-1-1c1-2 2-5 3-8l2-1v1h1l3-2c2-2 5-4 8-5 1-1 2-1 4-2l1 1c4-1 7-1 11 0 1 0 3 0 4 1 2 1 3 3 5 4l3 3c3 2 4 3 6 5l1-1c2 3 3 6 4 8v1c0 1 1 2 1 2 1 0 1 1 2 2 1 4 2 8 2 12 0 1 1 2 1 3v4c0 2 0 4 1 5-1 3-2 5-2 8-2 3-3 7-4 11 0 2-1 4-1 5h1c1-2 2-3 2-5v2h2c0 2-1 4-2 6l-4 7v2l-7 8c-4 4-9 8-14 11h0c-8 4-17 8-25 10-4 1-8 2-12 2-7 1-14 0-21 0v-2c0-1-1-1-1-1h-3-3c-1-1-2-1-3-1-2 0-3-1-4 0v1c-13-3-24-13-33-23-1 2 0 3 0 5 0 4 1 10 0 13l-2-1h-3c1-1 1-1 1-2l-1 1h-1-39 0c-3-1-6 0-8-1 1 0 3 1 4 0h0 3c2 0 3 1 5 0h0c-4-1-9 0-13 0v-1h-12-4-5c-4 0-10 0-14-1-2 0-3-2-5-3v1 1l-1 1c-3-1-5 1-8 1h-10c-7 0-13-1-19-1h-17-25l-7-2c-3 0-6-1-8-1h0-2l-1-1v1-1l-1-1c-1-1-2-1-3-1-2 0-3 0-5-1h-3 0c1 0 1-1 1-1 2-1 4-1 5-2v-2l-2-1h0c-1-1-2-1-3-2h-1l-2-1h-1c-1 1-2 1-3 1s-2 0-3-1l1-1c1 0 3-1 5-1h2c1-1 2 0 3-1h2l2-1c1 0 1 0 2-1h1c1-1 3 0 5 0 0-1 0-1 1-2 1 0 2-1 3-2 3-2 7-3 11-4 2 0 4 0 6-1 4-1 8-1 11 0h1l5 2h0 5c2-1 3-2 4-4v-1c0-1-1-3-2-4s-3-2-5-2l-6-2 1-1c-2-1-2-2-3-3h0c-1-1-1-1-2-1 0-1 0-2 1-3l2-3v-1h0v-2h-1c1-1 1-3 1-4 1-1 1-2 1-3s1-5 0-6v-3c-1 0-1-1-1-2h0c0-1 0-1-1-2 1 0 2-1 3-1v-1h-2-1c-1-3-3-4-5-6l1-1h1v-1h2l1-1h2c1 0 2-1 3-1 2-1 4-1 6-2 1 0 2 0 3-1 1 0 1-1 2-1h2 1c1-1 3-2 4-3s2-2 2-3l1-1 1-2c0-1 0-1 1-2 0 1 0 1 1 1v3c1 0 2 1 3 1l-1 1c-2 2-3 4-4 6l2 1 4 3c1 1 2 1 2 3l-1 1 1 1h1s0-1 1-1v-1h0c0-1 1-2 1-3h1v1h0 1c0 1 0 1 1 1 2-2 2-6 2-9v-2h0c-1-5 0-9 0-14v-7-5l-1-10c0-7 1-18-2-26h0-1 0l-1-2v-3h2l-1-2 1-1 1-10 2-133v-3c1 2 0 3 1 4v-7l1-5v-4-5-2-1c-1-4-1-8 0-12 0-1-1-3 0-4 0-1 1-2 1-2v-1c2-2 5-4 8-6h1c6-5 13-8 20-11l10-3c-1-1-1-1-2-1l4-2h1l-1-2c-1 1-2 1-3 1h0l2-2c-1 0-2 1-3 1 0-1 0-1 1-1 1-2 2-2 4-2h0l1-1 2-2c-3 0-5 1-8 2v-2l1-1v-1l1-1h1c-1-1-2-1-2-1v-2h3v-1h2s-1 0-1-1l6-3h0c2 0 5-1 7-2l1-1c1 0 3 0 4-1l1 1h0z" class="y"></path><path d="M437 565c1-1 1-1 2 0v1h-1l-1-1z" class="v"></path><path d="M408 588l1 1-1 1v1l-3-1c1-1 2 0 3-2z" class="z"></path><path d="M411 462c1 1 1 1 2 1l1 1-2 2v-1l-1-3z" class="D"></path><path d="M449 548c-1 2-3 3-5 5 2-3 3-5 5-7l-1 2h1z" class="v"></path><path d="M415 514h1l1 3v1h-1l-1-4z" class="U"></path><path d="M435 559h1s0 1 1 1l2 1-1 1c-1 0-3-1-4-2l1-1zm-50-203h1c0 2 1 4 1 6h0c-1-1-2-4-2-6z" class="v"></path><path d="M379 465c0-1 0-1 1-2v3c0 2 0 3-1 5v-4 1h-1c0-1 1-3 1-3z" class="f"></path><path d="M515 590l-1-1c1-2 1-4 1-5h1v2c0 1 0 3-1 4z" class="J"></path><path d="M402 476h1c0 2 0 5-1 7h0c-1 0-1-1-1-1v-2-1c1-1 1-2 1-3z" class="c"></path><path d="M410 455h1v3c0 1 0 2 1 3l1-1c0 1 0 2 1 3h-1c-1 0-1 0-2-1v-2c-1-2-1-3-1-5z" class="k"></path><path d="M369 493v1h0 0v1c-1 1-1 1-2 1v-1-3c0-1 0 1 0-1h0 1 0v1l1 1z" class="B"></path><path d="M458 580l3 6-3-3v1c-1 0-1-1-2-1 1-1 1-2 2-3z" class="h"></path><path d="M441 583v-2h2 0c1 1 1 2 0 3v1-1l-2 1h-1c1-1 1-1 1-2z" class="B"></path><path d="M518 580l1-1 1 1v5l-1 1v-1l-1-1v-4z" class="m"></path><path d="M423 441s0-1 1-1c1 2 2 4 4 4v1h0l-1 1v-1l-1 2v-2c-1-1-3-2-3-4z" class="B"></path><path d="M387 483l-3 8 1-10 2 2z" class="r"></path><path d="M358 540v-2c0-2-1-3 1-4 0 3 1 5 0 8-1-1-1-1-1-2z" class="C"></path><path d="M399 374h0c1 4 4 9 3 12h0c-1-3-2-7-3-9v-3z" class="f"></path><path d="M385 570h2v1c1 1 1 1 1 3-1 0-2 0-3-1l-1-1c0-1 0-1 1-2z" class="k"></path><path d="M387 571c1 1 1 1 1 3-1 0-2 0-3-1h1l1-2zm37-180h-3 0-1c0-2 0-3 1-5 1 1 2 3 3 5zm-2 131c0-1 0-1 1-2h0v-2l-1-2h1c0 1 1 1 1 2v1c0 1 1 2 2 3h-1-2v-1l-1 1z" class="W"></path><path d="M523 617c1 0 2 1 4 2h0l-1 1c-2-1-4-1-7-2 1-1 3-1 4-1z" class="D"></path><path d="M369 400h1v8l1 7h-1v-2c-1-2-1-4-1-5s0-1-1-2h1c1 0 0-5 0-6z" class="a"></path><path d="M410 563c0-1 0-1 1-1v-5l1-1h1-1v1c1 0 1 0 1 1h0l1 2-1 1h-1v2h-1c-1-1-1 0-1 0z" class="f"></path><path d="M371 498c0 2-1 5-1 7l-2-1v-2c1-1 1-2 1-2l-1-1c0-1 0-1 1-1h1 1z" class="B"></path><path d="M434 547h0c-2 2-5 6-7 6h0l5-6c0-2 1-2 2-3 0-1 1-1 1-2h0c0 2-1 3-2 4v1h1z" class="W"></path><path d="M515 614h0c2 1 5 2 8 3-1 0-3 0-4 1-2 0-5-3-7-4h3z" class="U"></path><path d="M492 541h0c1 2 1 4 0 6v2h-1 0v-2s-1 0-1-1h0l-1-2 2-2v1h1v-2z" class="I"></path><path d="M424 518c1 1 2 2 3 2 0 1 0 1 1 2h1 0v-2h1v1 1s1 1 1 2v1c-2-1-3-2-5-3-1-1-2-2-2-3v-1z" class="Q"></path><path d="M413 474c1 0 1-1 2-1 1 2 1 5 1 8v-1l-1 1h0c0-1 0-1-1-2h0v-1-1c0-1-1-1-1-2-1 0 0 0 0-1v1-1z" class="M"></path><path d="M397 407h2l1 10h-1 0l-2-10z" class="E"></path><path d="M391 393c1 3 1 5 1 8v1h0-1c0-1 0-2-1-3v-2c-1-1 0-3 0-3l1-1z" class="p"></path><path d="M469 551c1 0 1 0 2 1h0-1 0l3 6h0c1 0 1 0 1-1h0 1v2l1 3h1c0 1 0 1-1 1h-1c-1-3-3-6-4-9-1 0-2-2-2-2v-1z" class="W"></path><path d="M423 441c-3-3-3-6-2-10l3 8v1c-1 0-1 1-1 1z" class="a"></path><path d="M417 388h1c1 3 1 5 1 7s0 3 1 5c-1-1-2-3-2-4h-1v-8z" class="B"></path><path d="M423 455l4 6c-1 1-1 1-2 1 0 1 0 1-1 1v-1c-1-2-2-5-1-7z" class="I"></path><path d="M440 585h1l2-1v1h-1c1 1 1 1 1 2v3h-2v-3h0l-2 1-1-1 2-1v-1h0z" class="S"></path><path d="M444 539h2l1-1v1c-1 2-2 3-4 5h1 0 0c-1 2-3 4-5 5h0c0-1 1-3 2-4l1-1 3-3 1-1h0c-1 0-1 0-1-1h-1z" class="W"></path><path d="M456 583c1 0 1 1 2 1v-1 3c1 1 1 2 1 3-1-1-1-1-2 0h0 0v-1h-1l-1-1h-1 0 1c0-1 1-2 1-3v-1z" class="E"></path><path d="M397 485s1 0 1 1-1 2-2 4v4c-1 2-1 3-2 4v1l-1-1c1-4 2-9 4-13z" class="x"></path><path d="M391 577h2c0 1 1 1 1 1 1 1 2 1 2 2h0 1c2 1 3 2 3 4-1-1-1-2-2-2h-1c-1 0-2-1-3-2l-3-3zm31-55l1-1v1h2 1c2 1 3 2 5 3v1h-1c-1 0-4-3-5-3 0 1 0 1-1 2 0-1 0-1-1-1h-1v-2z" class="I"></path><path d="M377 389c0-2-1-5 0-6 1 0 1 0 2 1h1c-1 0-2 0-2 1v1 4h0c0 2 0 3 1 4l1 6h0l-3-11z" class="M"></path><path d="M406 512l3 9v3h0-1l-1-3v-2c-1-2-2-5-1-7z" class="d"></path><path d="M418 543v-5h1l2 9h-1v5c-1-1-1-3-2-5 1-1 1-3 0-4zm171 82c1-1 2-2 3-2 2-1 4-7 5-8v-1l1 1v2l-7 8h-2z" class="M"></path><path d="M412 500c0-1 0-3 1-4 0 0 0-1 1-1 1 1 1 7 1 8l-2-1v1h-1v-3z" class="W"></path><path d="M427 461c0 2 3 5 2 7h0l-2-2-1 1 1 2c-1 0-1-1-1-1-1-2-1-3-2-5 1 0 1 0 1-1 1 0 1 0 2-1z" class="D"></path><path d="M414 464c0 2 0 4-1 7v3 1-1c0 1-1 1 0 1 0 1 1 1 1 2h-1-1c-1-4 0-8 0-12v1l2-2z" class="I"></path><path d="M365 519c0-1 0-3 1-4v1l1 15h-1c-1-4-1-8-1-12z" class="P"></path><path d="M426 564h3c1 1 1 3 2 3 1 1 2 0 3 0l2 1v2h-1c-1-1-2-1-3-1s-1-1-1-1h-2l-1-1-2-3h0z" class="E"></path><path d="M378 446h1v2 1c0 1-1 1-1 1v1c0 1 0 5-1 6h-2v2c0-3 0-6 1-8 1 0 1 0 2-1v-4h0z" class="a"></path><path d="M425 392h1v1c0 1 2 3 2 4h-1-1v3 1h-1c0-1 0 0-1-1v-7h1v-1z" class="c"></path><path d="M418 367s0-1 1-1c1 3 2 7 3 10-1 1-1 2-1 3l-3-12z" class="P"></path><path d="M410 500c0-1 0-1 1-2v2h1v3h1v5l-1 1v-3c-1-1-1-1-2 0-1-1-1-1-1-3 1 0 1-1 1-1v-2z" class="z"></path><path d="M410 500c1 1 2 4 2 6-1-1-1-1-2 0-1-1-1-1-1-3 1 0 1-1 1-1v-2z" class="B"></path><path d="M490 568l-1 1h0l3 4c-2-1-4-2-5-3v-1c-1 1-2 1-2 0h0c0-1 1-1 2-1v-1c0-1-1-1-1-2h-2v-1h2s0 1 1 1 1-1 2 0v1l-1 1s0 1 1 1h1z" class="a"></path><path d="M357 519l2 15c-2 1-1 2-1 4v2h-1 0c-1-5 0-9 0-14v-7z" class="j"></path><path d="M568 548h0c3 0 6-1 8 1 1 1 2 2 2 4-1-1-3-1-4-2h0-1c-1-2-3-2-5-3z" class="W"></path><path d="M504 584h1l1 1v3c1 0 1 0 1 1l-1 1h-2l-3-3v-1s1 0 1-1c1 0 1 1 2 0v-1z" class="J"></path><path d="M413 354c2 3 5 8 6 12-1 0-1 1-1 1-2-4-3-8-5-12v-1z" class="L"></path><path d="M513 568c1 2 2 3 4 4l1 1h0-1-2l-1-1h0c0 1 0 1 1 1 0 1 0 3-1 4-1-1-1-4-2-5h-1v-2l2-2z" class="v"></path><path d="M424 410l-4-6c2 1 5 5 7 7l3 5-2 1-4-7z" class="K"></path><path d="M375 419v4c1 4 1 6-1 9h0l-1 1c0-3-1-5 0-8 0-1 1-2 1-3s0-2 1-3z" class="f"></path><path d="M374 480c0 1 0 3-1 3-1 1 0 1-1 1 0 1 0 2-1 2l-1 1h1 1 0c1 0 0 1 0 2 0 2-1 2 0 4h-1 0c-1 0-2-1-2 0l-1-1h2v-6h0c0-2 1-4 2-5v1h1l1-2z" class="M"></path><path d="M480 502c0 1 0 2 1 3 0 2 1 6 0 8l-1 1h-1v-8-3h1v-1z" class="W"></path><path d="M409 451l1 3v2 2 2 1h0v3 9l-1-1c-1-1-1-2-1-3v-2-2h1v-2-7-5z" class="D"></path><path d="M408 465h1v-2 6c0 1 0 0-1 0v-2-2z" class="Q"></path><path d="M355 460l1 12v6h0c-1-2-1-3-1-4l-1-1-1-2 1-1 1-10z" class="a"></path><path d="M354 470h1c0 1 0 2 1 3l-1 1-1-1-1-2 1-1z" class="f"></path><path d="M370 308c1 0 1 1 2 2 0 4 1 9-1 13-1-4-1-10-1-15h0z" class="E"></path><path d="M424 509c-2-2-2-6-3-9l-2-5c0-1-1-2-1-3 1 0 1 0 1 1h0l1-1 2 7c1 1 1 3 2 4 0 1 1 2 1 4h0l-1 2z" class="W"></path><path d="M384 369c-2-4-3-7-4-11 0-3-1-5-1-8h0 1c0 3 1 4 1 6 1 3 1 6 3 9 0 1 0 2 1 2l-1 2z" class="v"></path><path d="M366 324v-7h1v1c0 2 0 4 1 6 0 1 0 3-2 4 0 5 1 9 0 14h0v-5-13z" class="I"></path><path d="M399 467l1 1 1 1c1 1 1 5 1 7 0 1 0 2-1 3v1l-1-1c0-4-1-9-1-12z" class="J"></path><path d="M376 578l2 2c1 4 7 9 10 11 1 1 3 2 4 2v3l-2-2h0c0-1-1-2-2-2s-1-1-2-2c0 0-1-1-2-1-2-1-3-4-5-5s-2-3-2-4c-1-1-1-1-1-2z" class="W"></path><path d="M493 593l13 9h-1c-1 0-2-1-3-2l-1 1c1 0 1 1 2 1 1 1 1 1 1 2-1-1-2-2-3-2-3-3-6-6-9-8l1-1z" class="j"></path><path d="M506 573v-1l-2-2h0l1-1c1 2 2 3 3 4-1 1-2 2-2 3l-2 1c-1-1-3-2-4-4h1s0 1 1 1h0v-1c2-1 2-1 4 0z" class="Y"></path><path d="M502 574v-1c2-1 2-1 4 0v1h-4z" class="E"></path><path d="M427 469l-1-2 1-1 2 2c1 2 2 3 3 4h0c-1 2 0 2-1 3 0 1-2 1-2 1l-2-5v-2z" class="u"></path><path d="M521 589c1 4 1 9-1 12-1 2-2 3-4 3v-1c1 0 1-2 2-2v-1c1 0 1-1 1-2s0-1 1-2v-3h0l1-1v-3z" class="I"></path><path d="M431 443c-2-4-5-9-7-14 4 3 7 8 9 12-1 0-1 0-2 2z" class="e"></path><path d="M498 563c1 0 2 0 3-1-1-2-5-2-6-4h-2l-1-1h0l1-1c0-1 1-2 3-3v1c0 1 0 2 1 3 0 1 1 2 3 2v1c1 0 2 1 2 3h0-3-1z" class="W"></path><defs><linearGradient id="AE" x1="481.764" y1="583.472" x2="474.759" y2="583.45" xlink:href="#B"><stop offset="0" stop-color="#171514"></stop><stop offset="1" stop-color="#2f3134"></stop></linearGradient></defs><path fill="url(#AE)" d="M474 577l1 2c1-1 1 0 1-1h1v1c2 4 4 7 7 10h-2c-3-3-6-7-9-11l1-1z"></path><path d="M394 482v4 1c0 1-1 3-1 4s-1 3-1 4l-1 1h-1-1c0-3 2-6 2-9h1c1-1 1-3 2-5z" class="x"></path><path d="M413 503v-1l2 1 1 11h-1c-1-2-1-4-2-6v-5z" class="B"></path><path d="M484 583l9 10-1 1c-3-3-7-6-9-10l1-1z" class="h"></path><path d="M421 379c0-1 0-2 1-3l3 13v3 1h-1v-2c-1-2-2-4-3-5 1-3 0-5 0-7z" class="N"></path><path d="M459 531h1 0c0 1 0 0 1 1h0l1-1c0 1 0 1 1 2h-1 0c-1 2-2 3-3 4h1c-1 1-1 1-1 2h-1v1c-2 0-2 0-2-1v-1c0 1 0 1 1 1l2-1v-2c-1 1-1 0-1 1l-1-1h1v-1l-1 1c0 1-1 1-1 1 0 2 0 2-1 3l-2 1v-2h0v-1c2-1 2-1 3-2 1 0 1-1 2-2v-1l1-2z" class="a"></path><path d="M459 531h1 0c0 1 0 0 1 1h0c0 1-1 2-1 3h-1v-1s0-1-1-1l1-2z" class="B"></path><path d="M423 455l-1-1 1-1c2 2 5 7 6 10h0c0 2 1 3 1 4l2 3v2c-1-1-2-2-3-4h0c1-2-2-5-2-7l-4-6z" class="E"></path><path d="M488 506l1-1c1 0 1 1 2 1v1c1 5 2 9 2 14v-2c-2-1-2-2-3-4v-3c0-3-1-4-2-6z" class="W"></path><path d="M376 485v2h1c0-2 1-3 2-5-1 4-1 11-4 14v-1c0-1 0-2 1-3v-3h-1c-1 3-3 5-4 8v1h-1l1-1c0-1 0-2 1-3l1-2c-1 0-1 0-1-1 1-1 3-4 4-6z" class="a"></path><path d="M378 464h0l1 1s-1 2-1 3l-4 12-1 2h-1v-1c1-2 1-5 2-7 1-4 3-7 4-10z" class="R"></path><path d="M399 480l1-1 1 1v2s0 1 1 1h0v2c-2 4-2 9-4 13 0-6 0-12 1-18z" class="M"></path><path d="M399 480l1-1 1 1v2l-1 1c-1-1-1-2-1-3z" class="D"></path><path d="M413 529v-2h2 0c1 3 1 5 0 8v4h0c-1-2-1-4-3-5v-1-4h1z" class="W"></path><path d="M413 529v-2h2 0c0 1 0 2-1 3 0 1 0 2-1 2v-3z" class="J"></path><path d="M374 347c1-1 1-2 1-3v4c0 2 1 4 2 5l3 12 3 6h-1c-2-3-3-5-4-9l-4-15z" class="v"></path><path d="M374 347c-1-7 0-15 3-22v1c1 0 0 0 1-1h0 0c-1 2-1 3-1 5-1 5-2 9-2 14 0 1 0 2-1 3z" class="c"></path><path d="M420 556h1c0-1 0-2-1-3h1l2 1 3 4 3 6h-3c0-1-1-2-1-3-1-1-1-2-1-3l-1 1c-1 0-1-1-2-1-1-1-1-1-1-2z" class="Y"></path><path d="M420 556h1c0-1 0-2-1-3h1l2 1 3 4h-3v-2h-1c-1 0-1 1-1 2-1-1-1-1-1-2z" class="Q"></path><path d="M388 375h0c2 2 2 5 3 8h2v-1c0 2 0 2 2 3h0v3l-1 1s1 0 1 1h-1c0 1 0 2-1 2-2-5-4-12-5-17z" class="E"></path><path d="M391 383h2v-1c0 2 0 2 2 3h0v3l-1 1s1 0 1 1h-1c-1-1-2-5-3-7z" class="D"></path><path d="M491 549v1c1-1 1-3 2-3l1-1h0c0-1-1-3 0-4v4 1h0c1 1 1 0 1 1l-1 1v1l-1 1v1c-1 2-2 4-4 6h-1v-2c1 0 1 0 1-1-1 0-1 1-2 1h-1 0l2-2h0v-1h0l1 1v-1l1-1c1 0 1-2 1-3h0zm-44-9c2-2 4-5 5-7 0-1 0-1 1-1h1c-1 3-3 7-5 9l4-2h0v2l2-1c-2 3-4 6-6 8h0-1l1-2c1-1 2-2 2-4 1-1 1-1 1-2-1 1-3 3-5 4v-1l3-3 1-2c0-1 0-2 1-3h0v-1h1v-1h0l-1 1s0 1-1 1v1 1c-1 0-1 0-1 1-1 1-1 2-2 3h-1v-1z" class="W"></path><path d="M414 433v-1c-1-2-1-5-1-8 0-1-1-2-1-3h1c3 10 3 21 3 31v1l-1-1-1-19z" class="E"></path><path d="M377 389c-2-3 0-6-1-9-1-2-1-4-1-5 1 1 2 2 2 3v1h1v-1h0c-1-3-1-6-2-9l4 8h-1c0 1 0 4 1 5v2h-1c-1-1-1-1-2-1-1 1 0 4 0 6z" class="a"></path><path d="M407 535l-3-9c-1-2-1-5-2-6-2-3-3-8-3-12h0c1 2 1 3 2 5v2l1 1c2 2 3 8 3 10 1 2 3 7 2 8v1z" class="U"></path><path d="M516 612c3 1 5 2 7 3 1 0 4 1 4 1v1h0l6 3c2 0 4 1 5 1-3 0-8 0-11-2h0c-2-1-3-2-4-2-3-1-6-2-8-3l1-2z" class="R"></path><path d="M455 528h0 2c0 1 0 2-1 4h1c0 1 0 1 1 2-1 1-1 2-2 2-1 1-1 1-3 2v1l-4 2c2-2 4-6 5-9h0l1-4z" class="B"></path><path d="M455 528h0 2c0 1 0 2-1 4l-1 1s-1 0-1-1l1-4z" class="J"></path><path d="M516 586l1 1c-1 1 0 4-1 5h-1c-1 1-2 3-4 4-1 0 0 1-1 2h-1v-2h0l-1 1h-1v-1h0-1 0-1c1-1 1-2 2-2 0-1 1-1 1-1l1 1h0 1v-1-1h2v-1h0l1-1 2 1h0v-1c1-1 1-3 1-4z" class="W"></path><path d="M372 310c1-1 1-1 2-1v1c1 7-1 12-3 18 0 1 0 2-1 3v-1c0-2 0-5 1-7 2-4 1-9 1-13z" class="d"></path><path d="M417 517v1c1 1 2 1 2 3 0 1 0 2-1 2v-3h0c-1 1-1 2-1 3h1v1 10c0 1 1 3 1 4h-1v5c-1-2 0-5-1-7 0-4-1-9-1-13v-1-4h1v-1z" class="W"></path><path d="M417 517v1 2 3h-1 0v-1-4h1v-1z" class="B"></path><path d="M374 432c1 2 1 3 1 4v4l1 4v2 2c-1 0-2-1-2-1l-2 1 1-11v-4l1-1z" class="D"></path><path d="M375 445l1-1v2 2c-1 0-2-1-2-1 0-1 1-1 1-2h0z" class="J"></path><path d="M374 441l1-1 1 4-1 1-1-1c0-1-1-2 0-3z" class="Q"></path><path d="M374 432c1 2 1 3 1 4v4l-1 1c0-1-1-3-1-4v-4l1-1z" class="J"></path><path d="M576 532c1 0 3 0 4 1 2 1 3 3 5 4l3 3c3 2 4 3 6 5l1-1c2 3 3 6 4 8v1c-1-2-3-4-4-5-1-2-3-3-4-4s-2-2-4-3c-2-2-5-3-7-4v-1c1 1 1 1 2 1h1v1h1s1 0 1 1c0-1-1-2-2-2-2-1-2-3-5-4-1 0-1-1-2-1z" class="a"></path><path d="M428 452c2 3 3 6 5 9 0 1-1 3 0 5 0 1 1 3 1 4s0 1-1 1l-1-1-2-3c0-1-1-2-1-4l1 2h0l1 1v-1h0c0-1 0-1-1-2h0v-1c-1-1 0-2 0-3l-2-3v-1-1c-1-1 0-1 0-2z" class="S"></path><path d="M434 442h1l1 2v-2c-1 0-1-1-1-1l-2-4c0-1-1-2-2-3h0c-1-1 0-1-1-1 0-1 0-1-1-1 0-1-1-1-1-2h0l-3-3c-1-1-2-3-3-5 0-2-1-3-2-4 2 1 4 3 5 4l1 1-1 1c3 5 7 9 9 14 1 2 3 4 4 7l-2 1c0-2-1-3-2-4zm47 111c0 1 0 1-1 2h0v1h-2 0c0-1 0-1-1-1v-1-1c-1-1-1-3-2-4 0-1 0-1-1-1-1 1-1 1-1 2v-1-1c1-1 1-2 2-4 0-1 1-1 2-1v-2l1 2c-1 1-1 1-2 1 0 3-1 4 1 6 1 0 2-1 4-1 0 1 0 2 1 3 1-1 1-1 2 0v1h-1c-1-1-1 0-2 0z" class="I"></path><path d="M477 550c1 0 2-1 4-1 0 1 0 2 1 3 1-1 1-1 2 0v1h-1c-1-1-1 0-2 0h0c-1 1-1 1-2 1l-1-1s-1 0-1-1v-2z" class="B"></path><path d="M479 554c0-1 0-1 1-2l1 1c-1 1-1 1-2 1z" class="D"></path><path d="M599 605h1c1-2 2-3 2-5v2h2c0 2-1 4-2 6l-4 7-1-1c1-1 2-3 2-5l-5 6 5-10z" class="U"></path><path d="M378 444v-2h1v1c0 1 0 3 1 5h0l1 1v5 2 1l-1 9v-3c-1 1-1 1-1 2l-1-1h0c0-5 2-9 2-15-1-1-1-2-1-3h-1v-2z" class="B"></path><path d="M380 448l1 1v5 2-3s-1 0-1-1v-4z" class="S"></path><path d="M384 324c1 0 2 2 3 3l-1 1c1 1 2 2 2 3 1 1 1 3 2 4s2 2 2 3v1 2h0l-1-2c-1-1-1-2-2-3h0c-1-1-1-1-1-2l-1 1c0 1 1 2 0 3h-1c0-3-1-5-1-7v-3l-1-1v-1-2z" class="M"></path><path d="M567 607h0c-3 4-7 5-11 7v1h0c1-1 2-1 3-1-4 3-8 4-13 4 1 0 0 0 1-1h0l-1-1h0c8-2 15-5 21-9z" class="K"></path><path d="M446 571s0 1 1 1c0 1 1 1 2 2h-2v2h2v1c0 1 1 1 1 2v1 1c1 0 1-1 1-1h1c0 1 0 1-1 2v-1l-1 1c-1-1-1-2-2-3v-1-1h-2c-1-1-2-1-3-1v3c-1-1-1-1-1-2l-2-3c1-1 1-1 1-3l2 1 2 1h1v-2z" class="D"></path><path d="M446 571s0 1 1 1c0 1 1 1 2 2h-2v2c-2-1-2-2-4-2v-2l2 1h1v-2z" class="d"></path><path d="M574 551c1 1 3 1 4 2 2 2 3 5 4 7 1 3 2 7 1 10l-1 1c-1-7-3-14-8-20z" class="U"></path><path d="M427 504c-1-1-1-2-1-2-2-3-2-6-2-10l5 6 1 1 2 2 1 1-1 1-3-1 2 4h-1c-1 1-2-1-2-2h-1z" class="o"></path><path d="M429 502c0-1-1-3-1-3l1-1 1 1 2 2 1 1-1 1-3-1z" class="G"></path><path d="M517 587h1l1 5v3c-1 1-1 2-2 3h-1s0 1 1 2h-1c-1-1-1-2-2-3h0v2h-1l-1-2h0l-1-1c2-1 3-3 4-4h1c1-1 0-4 1-5z" class="Y"></path><path d="M519 595c-1 0-2 0-3 1h0-1l1-1c0-1 1-4 2-4l1 1v3z" class="G"></path><path d="M508 573c1 1 1 2 2 2 0 1 1 2 1 2 0 1 0 1 1 2l-1 1v1 1c0 1 0 1-1 2v1h-1c-1-1-2-1-2-3l1-1c0-1-3-3-4-4l2-1c0-1 1-2 2-3z" class="c"></path><path d="M509 577h1v2l1 1v1 1c0 1 0 1-1 2 0-2-1-3-2-5 0-1 0-1 1-2z" class="W"></path><path d="M508 573c1 1 1 2 2 2 0 1 1 2 1 2 0 1 0 1 1 2l-1 1-1-1v-2h-1v-1c-1 1-1 1-2 1l-1-1c0-1 1-2 2-3z" class="a"></path><path d="M490 546h0c0 1 1 1 1 1v2c0 1 0 3-1 3l-1 1v-1-1-1l-2 2v-1h-1 0v2h0l-2 2h-1l-1 1c-1-1-1-1-1-2h1 0 1l1-1v-1c-1-1-1-1-2 0-1-1-1-2-1-3 2-1 2-1 3-1h0 5c0-1 0-1 1-2h0z" class="D"></path><path d="M437 568c2-1 5-1 7 0 1 0 2 1 4 1 0 0 1 1 1 2h-2v1h3 1c0 1 1 1 1 2 1 0 1 1 1 1h-2l-2-1c-1-1-2-1-2-2-1 0-1-1-1-1v2h-1l-2-1-2-1-1-1h-1l-2-2z" class="c"></path><path d="M440 570s1-1 2-1c0 1 1 1 1 1 1 1 2 0 3 1v2h-1l-2-1-2-1-1-1z" class="g"></path><defs><linearGradient id="AF" x1="403.29" y1="512.733" x2="406.436" y2="518.846" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#656465"></stop></linearGradient></defs><path fill="url(#AF)" d="M407 525l-4-14v-4c-1-2-2-7-1-9 1 1 1 2 1 3 1 3 1 5 2 7 0 2 1 3 1 4-1 2 0 5 1 7v2h0-1c0 1 0 2 1 3v1z"></path><path d="M589 625h2c-4 4-9 8-14 11h0c0-1-1-1-1-1-3 1-5 2-8 3l21-13z" class="U"></path><path d="M366 479v24 9 4-1c-1 1-1 3-1 4-1-4-1-8-1-12l1-28h1zm65-36c1-2 1-2 2-2l1 1c1 1 2 2 2 4l2 3 1 8c0 1-1 1-1 1 0-1 0-2-1-2l-6-13z" class="C"></path><path d="M414 433l1 19 1 1v-1 7 5l-1 1v-4-1l-1-1h0c1-1 0-2 0-3v3 1 3c-1-1-1-2-1-3 1-2 1-6 1-8l-1-9v-8h1v-2z" class="D"></path><path d="M415 452l1 1v-1 7h0c-1-1-1-5-1-7z" class="c"></path><path d="M379 419c1 2 1 8 2 9s2 2 1 3h0c1-1 1-1 1-2v-2h0c0 1 1 2 1 2v1h2 1v2h0l-1 2 1 3c-1 0-1 0-2 1l-1 1v-1c0 1 0 1-1 1l-1-4v-1l-1-5c-1 0-1-1-1-2h0l-1 1h0c0-2-1-7 0-9z" class="Q"></path><path d="M384 430h2 1v2h0l-1 2 1 3c-1 0-1 0-2 1l-1 1v-1-2-6z" class="h"></path><path d="M384 436l1-3h1v1l1 3c-1 0-1 0-2 1l-1 1v-1-2z" class="K"></path><path d="M414 372c2 13 0 25-5 37-1 1-1 3-2 4l-1 3h0l-1-1c2-4 3-8 5-12 3-10 4-17 3-27h0l1-1v-3zm35 202l2 1h2c2 2 4 3 5 5-1 1-1 2-2 3v1c0 1-1 2-1 3h-1 0v-1c0-1 0-2-1-3v-1l-2 1v-1c1-1 1-1 1-2h-1s0 1-1 1v-1-1c0-1-1-1-1-2v-1h-2v-2h2z" class="e"></path><path d="M449 574l2 1-1 1 1 1h-2v-1h-2v-2h2z" class="u"></path><path d="M449 577h2c1 0 1 0 2 1 1 0 1 0 1 1 1 2 1 3 2 5 0 1-1 2-1 3h-1 0v-1c0-1 0-2-1-3v-1l-2 1v-1c1-1 1-1 1-2h-1s0 1-1 1v-1-1c0-1-1-1-1-2z" class="G"></path><path d="M453 583c1 1 1 2 1 3v1h0 0 1l1 1h1v1h0l1 1c0 1 0 1 1 2-1 1-1 1-2 1 0 1-1 2-1 2-1 1-1 0-2 0h0l2-1-1-1c-1 0-1 1-2 2 0 0-1 0-1 1l-1-1 2-1-1-1c0 1-1 1-1 1h-1c0-1 1-1 2-2h-1l-1-1c1 0 2-1 3-2h-3c-1 0-1-1-1-2v-1c1 0 2-1 3-1 1-1 1-1 1-2z" class="Y"></path><path d="M456 588h1v1h0l1 1c0 1-1 1-1 1l-1 1c0-1-1-1-1-1 0-1-1-1-1-1 0-1 1-1 2-2z" class="K"></path><path d="M449 586c1 1 4 0 4 0-1 2-1 2-3 3-1 0-1-1-1-2v-1z" class="f"></path><defs><linearGradient id="AG" x1="393.722" y1="410.084" x2="388.02" y2="409.845" xlink:href="#B"><stop offset="0" stop-color="#3c3c3b"></stop><stop offset="1" stop-color="#5d5b5e"></stop></linearGradient></defs><path fill="url(#AG)" d="M388 400v-1c1-1 1-2 2-2v2c1 1 1 2 1 3h1 0v-1l1 6c0 2 1 6 1 9v3c-1 0 0 1 0 2h-1 0 0c-1-1-1-4-1-5-1-2-1-4-1-5l-3-11z"></path><path d="M390 399c1 1 1 2 1 3h1 0v-1l1 6v2h-1c-1-3-2-7-2-10z" class="o"></path><path d="M488 526h0c0-3-1-5-1-7v-1l1 2h1l1-1h0c1 1 1 3 1 4s0 2 1 3c0 0 1 1 1 2 1 1 0 1 1 2v1 1 1c0 1 0 1 1 2v5h0v-1h-1 0v-1h-1c0-1 0-3-1-4v-2h-1l1-1-1 3h1 0v2c0 1 0 2 1 3h0l-1 2h0c0-2 0-4-1-7-1-2-1-5-1-7 0-1-1-2-1-2h-1v1z" class="a"></path><path d="M365 298l1-1v3c1 1 1 1 2 3v1c0 1 1 3 1 3v12c0 2 0 3-1 5-1-2-1-4-1-6v-1h-1v7c-1-3-1-6-1-9v-17z" class="U"></path><path d="M367 318v-2h1v1c1 0 0 2 1 2 0 2 0 3-1 5-1-2-1-4-1-6z" class="k"></path><path d="M377 446v1c1 0 1 0 1-1v4c-1 1-1 1-2 1-1 2-1 5-1 8 0 1 0 2-1 3v-1h-2c-1-4-1-8 0-12v-1l2-1s1 1 2 1v-2h1z" class="B"></path><path d="M372 448l2-1s1 1 2 1c-1 1-2 2-2 4l-2 1v-1-3-1z" class="U"></path><path d="M428 452c-2-2-3-3-4-5 4 2 9 9 10 13 1 1 1 2 2 4 1 3 1 6 2 9v6h0c-1-1-1-2-2-4 0-1-1-3-2-5h0c0-1-1-3-1-4-1-2 0-4 0-5-2-3-3-6-5-9z" class="G"></path><defs><linearGradient id="AH" x1="397.832" y1="464.714" x2="402.767" y2="460.437" xlink:href="#B"><stop offset="0" stop-color="#5b5a5b"></stop><stop offset="1" stop-color="#767675"></stop></linearGradient></defs><path fill="url(#AH)" d="M396 452h0c1 1 1 3 1 4s0 1 1 1h0l1 1 1-2h2c0 1-1 2-1 2 0 1 0 2 1 2 0-1 0-4 1-5h0v8 3 1 8 1h-1c0-2 0-6-1-7l-1-1-1-1c0-2 0-6-1-7l-1 1s0-1-1-1c1-2 1-2 0-4v-4z"></path><path d="M403 466h-1l-1-2c1-1 1-1 2-1v3z" class="n"></path><path d="M401 469c0-1 1-2 1-2 1 2 1 6 1 8v1h-1c0-2 0-6-1-7z" class="K"></path><path d="M395 385h0c0-2 0-3 1-4 0 1 1 6 1 6l2 20h-2l-4-15c1 0 1-1 1-2h1c0-1-1-1-1-1l1-1v-3z" class="r"></path><path d="M436 459c1 1 3 10 3 13 1 1 2 2 3 2h0c-1 2-1 4-3 5 0 1 1 1 0 2 0 1-1 2-1 2h-1 0c1-4-2-7-4-9 0-1-1-2-1-2h0v-2l1 1c1 0 1 0 1-1h0c1 2 2 4 2 5 1 2 1 3 2 4h0v-6c-1-3-1-6-2-9-1-2-1-3-2-4h1l1 1c0-1-1-1 0-2h0z" class="Y"></path><path d="M439 472c1 1 2 2 3 2h0c-1 2-1 4-3 5v-7z" class="L"></path><path d="M441 535h0 0c1 0 2 1 3 1l-1 2c2-1 3-2 5-3l1 1c0 1-2 3-2 4v1l-3 3h0 0-1c2-2 3-3 4-5v-1l-1 1h-2-1v1c0 1-2 2-3 4h-1v-2c-2 1-4 3-5 5h-1v-1c1-1 2-2 2-4h0l2-3 1-1 1-1h0l2-2z" class="a"></path><path d="M439 540h0 1 1 1c0 1-1 2-2 3h-1c1-1 1-1 1-2l-1-1z" class="M"></path><path d="M441 535h0 0c1 0 2 1 3 1l-1 2-1 1v1h-1-1-1 0l-2 1c0-1 1-2 2-3v-1h0l2-2z" class="D"></path><path d="M441 535c1 0 2 1 3 1l-1 2-1 1-1-1c-1-1 0-1 0-3h0z" class="R"></path><path d="M407 413c1-1 1-3 2-4 2 2 0 4 1 7 1 1 0 3 1 5v1 1 1 1 2h0c0 2 0 6 1 8l-2-5c-1-2-1-3-2-4h0v3h-1v-6c-1-3-1-5-1-7l1-3z" class="a"></path><path d="M407 413l2 2v2c0 1 0 3 1 5v3h-1v-1c-1-1-1-1-2-1-1-3-1-5-1-7l1-3z" class="v"></path><path d="M567 607v-1c2-2 5-3 7-5 3-3 6-8 9-12h0c0 2-1 3-2 4-2 3-4 8-6 11-4 4-10 8-15 11l-1-1c-1 0-2 0-3 1h0v-1c4-2 8-3 11-7h0zm-182-37l1-1h6c2 1 3 1 4 1 2 0 3 1 4 1v2 2l-1 1c-1-1-1-1-2-1v1l1 1 3 3c-3-1-5-4-8-5h-4 0l-1-1c0-2 0-2-1-3v-1h-2z" class="B"></path><path d="M392 569c2 1 3 1 4 1 2 0 3 1 4 1v2h0-2c-1 0-3-2-4-2-2-1-3 0-4-1l2-1z" class="Q"></path><path d="M427 504h1c0 1 1 3 2 2h1 0l1 2c1 2 2 2 3 3v2 3 1 1 1h-2c-1-1-2-2-4-3h0l-1-2h0c-2-2-3-4-4-5l1-2c1 2 2 3 3 5l1-1c0-1-1-3-1-4-1-1-1-2-1-3z" class="c"></path><path d="M432 512h2l1 1v3l-3-4z" class="Y"></path><path d="M428 514l1-1c2 0 4 3 5 5l-1 1c-1-1-2-2-4-3h0l-1-2z" class="n"></path><path d="M427 504h1c0 1 1 3 2 2h1 0l1 2c1 2 2 2 3 3v2l-1-1h-2c-1 0-2-2-3-3 0-1-1-1-1-2-1-1-1-2-1-3z" class="E"></path><path d="M431 506l1 2v1 1l-2-1v-2l1-1z" class="r"></path><path d="M379 428l1-1h0c0 1 0 2 1 2l1 5v1l1 4c1 0 1 0 1-1v1 7 2 1 3c-1 1-1 1-1 2-1 1-1 1-1 2h-1v1-1-2-5l-1-1h0c-1-2-1-4-1-5v-3h1v-5h0c-1-2-1-5-1-7z" class="p"></path><path d="M379 428l1-1h0c0 1 0 2 1 2l1 5s-1-1-1-2c0 0 0-1-1-1v4c-1-2-1-5-1-7z" class="E"></path><path d="M380 435c1 4 1 9 2 13v-3h0l2 1v2 1 3c-1 1-1 1-1 2-1 1-1 1-1 2h-1v1-1-2-5l-1-1h0c-1-2-1-4-1-5v-3h1v-5z" class="Q"></path><path d="M382 448v-3h0l2 1v2 1 3c-1 1-1 1-1 2-1 1-1 1-1 2h-1v1-1-2c1-2 1-4 1-6z" class="K"></path><path d="M432 472s1 1 1 2c2 2 5 5 4 9h0 1s1-1 1-2c1 2 4 5 5 7l2 3-3-3-1 1c-1-1-2-2-2-3l-1-1h-2-1v1h-1c-1-1-2-3-3-5h0c-1-1-1 0-1-1s-1-1-1-2l-1-2s2 0 2-1c1-1 0-1 1-3z" class="C"></path><path d="M440 486v-1l1 1c1 1 2 1 2 2l-1 1c-1-1-2-2-2-3z" class="F"></path><path d="M432 472s1 1 1 2c-1 0-1 0-1 1s0 2-1 3c0 1 1 2 1 3-1-1-1 0-1-1s-1-1-1-2l-1-2s2 0 2-1c1-1 0-1 1-3z" class="b"></path><path d="M407 525v-1c-1-1-1-2-1-3h1 0l1 3h1 0c1 1 1 2 1 3h1v-1l1 1v1 1 4 1 2c-1 2-1 3-1 4h1c0 1 0 1-1 2h0c-2-1-4-5-4-7v-1c1-1-1-6-2-8l2-1h0z" class="K"></path><path d="M411 526l1 1v1 1 4 1 2c-1 2-1 3-1 4-1-1-1-1-1-2 1-1 0-2 0-4-1-2-1-5 0-7h1v-1z" class="R"></path><path d="M411 526l1 1v1h-1v-1-1z" class="Y"></path><path d="M410 534v-4c1 1 1 1 1 2l1 1v1 2c-1 2-1 3-1 4-1-1-1-1-1-2 1-1 0-2 0-4z" class="I"></path><path d="M407 525l3 13c0 1 0 1 1 2h1c0 1 0 1-1 2h0c-2-1-4-5-4-7v-1c1-1-1-6-2-8l2-1z" class="f"></path><path d="M406 458c0-2-1-5 0-7l1 3v9l1 2v2 2c0 1 0 2 1 3l1 1v2c0 2 1 3 0 5h0l-2-2c-1-1-1-2-1-3v-2-1h0v-2h-1v2 5h-1v-4c0-1 1-2-1-3v-1-6-2 1 4l-1 1v-1-3-8-1h1v4h0 1 1z" class="W"></path><path d="M406 458c0-2-1-5 0-7l1 3v9l1 2v2h-2v3h-1v-8c-1-1-1-2-1-4h1 1z" class="Q"></path><path d="M406 467v-6h1v2l1 2v2h-2z" class="J"></path><path d="M405 458h1v8h-1v-4c-1-1-1-2-1-4h1z" class="n"></path><path d="M548 539v1h1l3-2c2-2 5-4 8-5 1-1 2-1 4-2l1 1v1h-2-1c-1 1-2 1-2 1l-3 2c-1 0-1 0-2 1-2 2-4 3-6 5l1 1 3-2h4 0l-1 1c-1 0-1 1-2 1l-2 2c-2 1-5 4-7 4h-1l-1-1c1-2 2-5 3-8l2-1z" class="W"></path><path d="M420 552v-5h1l2 7-2-1h-1c1 1 1 2 1 3h-1c0 1 0 1 1 2 1 0 1 1 2 1l1-1c0 1 0 2 1 3-2-1-3-1-5-1-1 2 0 2-1 3h-3v-1h-1 0v-1l-1-1-1-2h0c0-1 0-1-1-1v-1h1c0 1 0 1 1 1 1-2-1-3 0-5v1h1c0-1 0-1 1-2 1 0 2 2 3 3l1-1v-1z" class="D"></path><path d="M413 558h1l1 2h1c1-2 0-2-1-3v-2h1c0 1 0 2 1 2h1c-1-1-1-1-1-2h1l2 1c0 1 0 1 1 2 1 0 1 1 2 1l1-1c0 1 0 2 1 3-2-1-3-1-5-1-1 2 0 2-1 3h-3v-1h-1 0v-1l-1-1-1-2z" class="K"></path><path d="M379 316h1l6 6 11 12c2 2 5 4 6 7-1 0-1 0-2-1h-4c-3-5-6-9-10-13-1-1-2-3-3-3 0-1-1-2-2-3s-2-3-3-5z" class="X"></path><path d="M376 425v-3l1 3c1 2 1 4 1 6h1s0-1-1-2l1-1h0c0 2 0 5 1 7h0v5h-1v3-1h-1v2 2h0c0 1 0 1-1 1v-1h-1v-2l-1-4v-4c0-1 0-2-1-4h0c2-3 2-5 1-9l1 2z" class="U"></path><path d="M377 431l1 1v1 1h-1v-3z" class="J"></path><path d="M375 423l1 2c0 1 0 3 1 5v1 3c-1 1-1 3-1 4v1c-1 0-1-2-1-3s0-2-1-4h0c2-3 2-5 1-9z" class="B"></path><path d="M375 436c0 1 0 3 1 3v-1h1 1v1h0c1 0 1 0 1 1v3-1h-1v2 2h0c0 1 0 1-1 1v-1h-1v-2l-1-4v-4z" class="J"></path><path d="M378 439h0c1 0 1 0 1 1v3-1h-1v2-5zm-3-3c0 1 0 3 1 3 1 2 1 4 1 6v1h-1v-2l-1-4v-4z" class="D"></path><path d="M375 305h1c2 6 6 11 10 15l1 1-1 1-6-6h-1s0-1-1-1l-4 12c0 1 0 2-1 3v1l-1-1c0-1 0-1 1-2h-1-1c2-6 4-11 3-18h1l1-1c-1-1-1-3-1-4z" class="M"></path><path d="M374 327c-1-1-1-3 0-5 1-4 2-8 2-12h0l1-1c1 3 2 5 3 7h-1s0-1-1-1l-4 12z" class="p"></path><path d="M387 456c2 1 2 1 3 1 0 0 0-1 1-1h0v2 1h1 0v2s-1 3-1 4c-1 4-3 9-3 13l-1 1c1 1 0 2 0 4l-2-2 1-16h1v-4h0v-5z" class="E"></path><path d="M387 479h0v-3h0l1 2-1 1z" class="o"></path><path d="M387 465v-4l1 1h2l-2 4c0 1 0 1-1 2h0v-3z" class="h"></path><path d="M390 462c0-2 0-3 1-4v1h1 0v2s-1 3-1 4l-1 1h-1-1l2-4z" class="o"></path><path d="M387 456c2 1 2 1 3 1 0 0 0-1 1-1h0v2c-1 1-1 2-1 4h-2l-1-1h0v-5z" class="i"></path><path d="M500 560c3 0 6 2 8 4 1 2 1 2 1 4-3-2-5-4-9-3h0v2h-2c-1-1-3-1-4-1 1 2 3 1 2 3h-2-1c-1 1-2 0-3-1h-1c-1 0-1-1-1-1l1-1v-1c-1-1-1 0-2 0s-1-1-1-1h1c4-1 7-1 11-1h1 3 0c0-2-1-3-2-3z" class="B"></path><path d="M487 564c4-1 7-1 11-1h1l-1 1c-1 0-1 0-1 1h0l-2-1v1l-1 1c-2-1-1-2-3-2-1 1-2 0-3 0h-1z" class="m"></path><path d="M450 519c1 1 1 1 2 1v1 1h1 0c1 1 1 2 2 3 0 0 0 1 1 1l-1 2h0l-1 4h0-1c-1 0-1 0-1 1-1 2-3 5-5 7 0-1 2-3 2-4l-1-1c-2 1-3 2-5 3l1-2 1-1c1-2 2-3 2-5l1-1c0-1 1-1 1-2v-1c0-1 0-3 1-4v-3z" class="I"></path><path d="M453 522c1 1 1 2 2 3 0 0 0 1 1 1l-1 2h0-1l-1 1c0 1 0 2-1 2h0-1c1-1 1-2 1-2 0-1 0-1 1-2v-1c1-1 1-2 0-3v-1z" class="S"></path><path d="M451 531h1 0c1 0 1-1 1-2l1-1h1l-1 4h0-1c-1 0-1 0-1 1-1 2-3 5-5 7 0-1 2-3 2-4v-1c2-2 1-3 2-4z" class="D"></path><path d="M450 519c1 1 1 1 2 1v1 1h1 0v1l-3 8v-4h-1v-1c0-1 0-3 1-4v-3z" class="E"></path><path d="M449 527h1v4c0 1-2 2-2 3 0 0 1 0 0 1h0c-2 1-3 2-5 3l1-2 1-1c1-2 2-3 2-5l1-1c0-1 1-1 1-2z" class="S"></path><path d="M397 340h4c1 1 1 1 2 1h0c4 4 8 8 10 13v1c-3-4-5-7-9-10 4 6 7 13 8 20 1 2 2 5 2 7v3l-1 1h0c-2-10-4-19-9-27-2-3-4-6-7-9z" class="O"></path><path d="M395 452h1v4c1 2 1 2 0 4 1 0 1 1 1 1l-3 21c-1 2-1 4-2 5l1-8h0c-1-2 0-4 0-7v-11h-1v-2-3l1-2h0l1 1v-1h1 0v-1-1z" class="h"></path><path d="M395 452h1v4 5l-1 1h-1c0-1 0-2-1-3v-3h-1l1-2h0l1 1v-1h1 0v-1-1z" class="i"></path><path d="M392 456h1v3 8h2c0 2 0 4-1 7 0 2 0 4-1 5h0c-1-2 0-4 0-7v-11h-1v-2-3z" class="Y"></path><defs><linearGradient id="AI" x1="380.22" y1="379.598" x2="392.365" y2="390.414" xlink:href="#B"><stop offset="0" stop-color="#313032"></stop><stop offset="1" stop-color="#585857"></stop></linearGradient></defs><path fill="url(#AI)" d="M382 371h1c2 3 4 8 5 12l1 3 2 7-1 1s-1 2 0 3c-1 0-1 1-2 2v1c-1-3-2-7-4-11l-1 1h0v-2h-1c0-1 0-2 1-3v1h1 0v-3l1 1c0-1 0-2-1-3v-2l-2-8z"></path><path d="M388 386l-1-1v-2h1l1 3h-1z" class="Q"></path><path d="M388 386h1l2 7-1 1h0c-1-3-1-5-2-8z" class="E"></path><path d="M384 379c1 1 2 4 2 6 0 0 0 1-1 2 1 1 1 1 1 2-1 0-1-1-2-1v1l-1 1h0v-2h-1c0-1 0-2 1-3v1h1 0v-3l1 1c0-1 0-2-1-3v-2z" class="I"></path><path d="M504 604c0-1 0-1-1-2-1 0-1-1-2-1l1-1c1 1 2 2 3 2h1c8 6 16 10 26 13 5 1 9 2 14 1l1 1h0c-1 1 0 1-1 1-7 0-12 0-19-2 0 0-3-1-4-1-2-1-4-2-7-3-2-1-5-3-7-5l-5-3z" class="H"></path><path d="M463 517h0v-2h1c0 1 1 2 1 3l-1 4h1c-1 2-1 2 0 3l-2 2c0 1-1 2-1 3v1h0l-1 1h0c-1-1-1 0-1-1h0-1l-1 2v1c-1-1-1-1-1-2h-1c1-2 1-3 1-4h-2l1-2c-1 0-1-1-1-1-1-1-1-2-2-3h0l1-2 1-1 1 2h0 1l1-3 1 2c1 0 1 1 2 2v-1l1-1 1-3z" class="D"></path><path d="M462 527h1c0 1-1 2-1 3v1l-1-1c0-2 0-2 1-3z" class="c"></path><path d="M461 521l1-1c0 2-1 5-2 7l-1-1c0-1 1-2 1-3l1-1v-1z" class="R"></path><path d="M464 522h1c-1 2-1 2 0 3l-2 2h-1c0-2 1-4 2-5z" class="Q"></path><path d="M459 520c1 0 1 1 2 2l-1 1c0 1-1 2-1 3l1 1c-1 1-1 2-1 4l-1 2v1c-1-1-1-1-1-2h-1c1-2 1-3 1-4s0-2 1-2c0-2 1-4 1-6z" class="S"></path><path d="M457 532l1-1v1l1-1-1 2v1c-1-1-1-1-1-2z" class="D"></path><path d="M458 518l1 2c0 2-1 4-1 6-1 0-1 1-1 2h-2l1-2c-1 0-1-1-1-1-1-1-1-2-2-3h0l1-2 1-1 1 2h0 1l1-3z" class="p"></path><path d="M454 520l1-1 1 2h0 1c0 1-1 3-1 4v1c-1 0-1-1-1-1-1-1-1-2-2-3h0l1-2z" class="k"></path><path d="M429 516c2 1 3 2 4 3h2v-1-1c1 1 2 2 3 2l2 1h1 3l1 1-1 2c1 0 1 1 2 1-1 1-1 1-3 1h-4l-1-1-1-1-1 3h1 0l1 1-1 1h-4c0-1-1-1-2-1l-1-1h1v-1-1c0-1-1-2-1-2v-1-1h-1v2h0-1c-1-1-1-1-1-2l1-1h-1c0-1-1-1-1-2h1s1 1 2 1c0 1 1 0 1 1h1v-1c-1 0-1-1-2-2h0z" class="U"></path><path d="M438 519l2 1v1 2h-1v-2c-1 1-1 2-1 3l-1-1c-1 0-2-1-2-1v-1h1l1 1c1-1 1-1 1-3zm-8 2l4 2c0 1 1 1 1 1v2h-1-3v-1-1c0-1-1-2-1-2v-1z" class="R"></path><path d="M440 520h1 3l1 1-1 2c1 0 1 1 2 1-1 1-1 1-3 1h-4l-1-1c0-1 0-2 1-3v2h1v-2-1z" class="K"></path><path d="M440 520h1 3l1 1-1 2c-1-1-2-2-4-2v-1z" class="Y"></path><path d="M427 471l2 5 1 2c0 1 1 1 1 2s0 0 1 1h0c1 2 2 4 3 5h1v-1h1v2l1 1 1 2 1 1c0 2 1 2 1 4 0 1 1 1 1 2 1 0 1 1 1 1v1l-2-1h0-2c-1 0-2-1-2-1l-1-1-4-4-2-4v-2l-4-11h1 0c1 1 1 1 1 2h0v1l1 1v-2l-1-1v-1s0-1-1-1v-3z" class="D"></path><path d="M430 486c4 4 6 8 10 11h1v1h0-2c-1 0-2-1-2-1l-1-1-4-4-2-4v-2z" class="n"></path><path d="M430 478c0 1 1 1 1 2s0 0 1 1h0c1 2 2 4 3 5h1v-1h1v2l1 1 1 2 1 1c0 2 1 2 1 4 0 1 1 1 1 2 1 0 1 1 1 1v1l-2-1v-1c-5-6-9-12-11-19z" class="O"></path><path d="M388 433l1-1v4 2h0c-1 2-1 3 0 5 0 2-1 3 0 4v1 2 2h-1l1 1c1 0 1 1 2 1h0v-2h1l1-2c1-1 1-1 2-1v3 1 1h0-1v1l-1-1h0l-1 2v3h0-1v-1-2h0c-1 0-1 1-1 1-1 0-1 0-3-1v-2h0c-2-1-2-1-3-2v-3-1-2-7l1-1c1-1 1-1 2-1l-1-3 1-2 1 1z" class="E"></path><path d="M388 433l1-1v4 2h0c0-1-1-1-1-2v-3z" class="K"></path><path d="M384 439l1-1c1-1 1-1 2-1v8c-1 1-1 1-1 2v2c-1 0-1 0-2-1v-2-7z" class="d"></path><path d="M384 448c1 1 1 1 2 1v-2c0-1 0-1 1-2 1 2 1 3 1 5v2l1 1c1 0 1 1 2 1h0v-2h1l1-2c1-1 1-1 2-1v3 1 1h0-1v1l-1-1h0l-1 2v3h0-1v-1-2h0c-1 0-1 1-1 1-1 0-1 0-3-1v-2h0c-2-1-2-1-3-2v-3-1z" class="g"></path><path d="M393 450c1-1 1-1 2-1v3 1h0c-2-1 0-2-2-3z" class="O"></path><path d="M384 448c1 1 1 1 2 1v-2c0-1 0-1 1-2 1 2 1 3 1 5h-1c0 2-1 1-2 1l-1 1v-3-1z" class="u"></path><defs><linearGradient id="AJ" x1="386.621" y1="477.572" x2="374.474" y2="458.347" xlink:href="#B"><stop offset="0" stop-color="#605f60"></stop><stop offset="1" stop-color="#828184"></stop></linearGradient></defs><path fill="url(#AJ)" d="M381 457v-1h1c0-1 0-1 1-2 0-1 0-1 1-2 1 1 1 1 3 2h0v2 5h0v4h-1v-3h0s0 1-1 2l-1 1-1 4-4 13c-1 2-2 3-2 5h-1v-2c1-4 2-9 3-14 1-2 1-3 1-5l1-9z"></path><path d="M383 469c-1-1-1-3-1-5 1-2 1-7 3-8h1c0-1 0-1 1-2v2 5h0v4h-1v-3h0s0 1-1 2l-1 1-1 4z" class="o"></path><path d="M384 465l2-6v-1h1v3h0v4h-1v-3h0s0 1-1 2l-1 1z" class="M"></path><path d="M367 531c2 7 2 14 4 21 4 13 12 24 23 32 3 2 7 4 11 6l3 1 12 3 1 1c-14-2-25-7-35-16 0-1-4-4-5-5-3-4-6-8-8-13-4-9-6-20-7-30h1z" class="j"></path><path d="M477 579h1l2 4 2 2v-2l1 1c2 4 6 7 9 10 3 2 6 5 9 8 1 0 2 1 3 2l5 3c2 2 5 4 7 5l-1 2h0l-21-15 4 4c2 2 3 4 5 5 2 2 4 3 6 5h-1c-4-2-7-5-10-8l-14-16c-3-3-5-6-7-10z" class="J"></path><path d="M482 583l1 1c2 4 6 7 9 10 3 2 6 5 9 8 1 0 2 1 3 2l5 3-1 1-9-7c-2-1-4-4-7-5-4-2-7-8-10-11v-2z" class="v"></path><defs><linearGradient id="AK" x1="435.598" y1="463.837" x2="443.448" y2="471.023" xlink:href="#B"><stop offset="0" stop-color="#b3b2b2"></stop><stop offset="1" stop-color="#d7d6d8"></stop></linearGradient></defs><path fill="url(#AK)" d="M436 459c-2-6-6-11-8-17-1-2-2-3-2-5h1c2 1 6 12 7 14s1 4 2 5h1c1 0 1 1 1 2v1h2 1 1v1c0 2 0 4 1 6v-1c1 0 1 1 1 1v2 3h0v3c-1 0-1 0-1 1h0v1l-1 1c0 1 0 1-1 2 1 2 2 3 2 5 1 1 1 2 1 3v1c-1-2-4-5-5-7 1-1 0-1 0-2 2-1 2-3 3-5h0c-1 0-2-1-3-2 0-3-2-12-3-13z"></path><path d="M438 459h2 1 1v1c0 2 0 4 1 6v-1c1 0 1 1 1 1v2 3h0v3c-1 0-1 0-1 1h0v1l-1 1c0 1 0 1-1 2 1 2 2 3 2 5 1 1 1 2 1 3v1c-1-2-4-5-5-7 1-1 0-1 0-2 2-1 2-3 3-5h0v-2h0c0-2-1-4-2-6v-2l-1-1c0-1-1-2-1-4z" class="G"></path><path d="M405 422v-7l1 1h0c0 2 0 4 1 7v6 1c1 2 1 4 1 7h0c0 1 0 2-1 3 0-1 0-1-1-1l1 7v6 2l-1-3c-1 2 0 5 0 7h-1-1 0v-4h-1c0-2 0-5 1-7v-6-3c0-1-1-2-1-3l-2-12c1 1 1 2 1 2v1c1 1 1 1 1 2v-1c1 0 1-1 1-1v-1c0-1 1-2 1-3z" class="K"></path><path d="M406 439c-1-4 0-7 0-11h1v2c1 2 1 4 1 7h0c0 1 0 2-1 3 0-1 0-1-1-1z" class="C"></path><path d="M405 446l1-1c1 0 1 5 0 5v1c-1 2 0 5 0 7h-1-1 0c1-2 0-3 0-5v-7h1z" class="i"></path><path d="M405 446l1-1c1 0 1 5 0 5v1c-1 2 0 5 0 7h-1v-12z" class="E"></path><path d="M405 422c0 3 1 5 1 7l-2-1c-1 3 2 8 0 10 0-1-1-2-1-3l-2-12c1 1 1 2 1 2v1c1 1 1 1 1 2v-1c1 0 1-1 1-1v-1c0-1 1-2 1-3z" class="D"></path><path d="M374 419c-2-7-1-16-2-23v-8-1l1-1 2 12c1 6 4 12 4 18v3c-1 2 0 7 0 9l-1 1c1 1 1 2 1 2h-1c0-2 0-4-1-6l-1-3v3l-1-2v-4h-1z" class="x"></path><path d="M374 419c1-1 0-2 1-3h1c1 3 1 6 1 9l-1-3v3l-1-2v-4h-1z" class="D"></path><path d="M361 383c0-2-1-6 0-7 1 1 1 3 1 5v12l-1 12v5 27c-1 2-1 4-1 7v-1-1h-1v5-12-16-31c1-2 1-6 1-9v1c1 1 1 2 1 3z" class="E"></path><path d="M359 388c1-2 1-6 1-9v1c1 1 1 2 1 3l-1 25v14 6l-1-9v-31z" class="g"></path><path d="M429 502l3 1 1-1c1 1 2 1 3 2l3 2 1-1 2 2v2 1l2 4v2 4h0-3-1l-2-1c-1 0-2-1-3-2v-1-3-2c-1-1-2-1-3-3l-1-2h0l-2-4z" class="T"></path><path d="M436 504l3 2c1 2 2 3 2 6h0c-1 0-2-2-2-2 0-1-1-2-1-2-1-2-1-3-2-4z" class="O"></path><path d="M442 517h1v-3c-1-1-1-2-1-4h0l2 4v2 4h0-3v-1l-1-2h2z" class="m"></path><path d="M435 513c1 0 2 1 3 1h2l1 1c0 1 0 1 1 2h-2l1 2v1h-1l-2-1c-1 0-2-1-3-2v-1-3z" class="d"></path><path d="M438 517l1-1 1 1 1 2v1h-1l-2-1v-2z" class="O"></path><path d="M435 513c1 0 2 1 3 1 0 1 0 1-1 1 0 1 0 1 1 2h0v2c-1 0-2-1-3-2v-1-3z" class="p"></path><path d="M429 502l3 1 1-1c1 1 2 1 3 2s1 2 2 4c0 0 1 1 1 2 0 0 1 2 2 2v3h0l-1-1h-2c-1 0-2-1-3-1v-2c-1-1-2-1-3-3l-1-2h0l-2-4z" class="e"></path><path d="M439 510s1 2 2 2v3h0l-1-1c-1 0-1-1-1-1 0-1-1-1-1-2l1-1z" class="H"></path><path d="M432 503l1-1c1 1 2 1 3 2s1 2 2 4c0 0 1 1 1 2l-1 1-6-8z" class="L"></path><path d="M385 367l1 3h1 0c0-1 0-1 1-2l-1-1c0-1-1-2-1-3v-1h1c1 1 1 3 2 4 0 1 1 3 1 4 1 2 2 5 3 7 0-2 0-4-1-5-1-2-1-4-1-5l-1-1 1-1c0 1 0 1 1 2l1 3c0 1 0 1 1 2v-3l-1-1v-1-1h0c1 0 1 1 1 1l2 6v1s1 1 1 2v1 1l1 2v1 1l1 1v3 1l1 1v1 2c1 0 1 1 1 1v1c1 3 1 6 2 8 0 2 1 5 1 6v1l-1-2v2l-3-15v-1l-1-2h0v-2l-1-1v-1h-1l-1-6c-1 1-1 2-1 4h0 0c-2-1-2-1-2-3v1h-2c-1-3-1-6-3-8h0c-1-1-1-1-1-2-1-1-2-3-3-4l1-2z" class="x"></path><path d="M393 382c0-1-1-3-1-3 0-1 1-1 1-1v-3c2 1 2 5 3 6-1 1-1 2-1 4h0 0c-2-1-2-1-2-3z" class="U"></path><path d="M440 443v-3s0 1 1 1l1 1c2 3 3 8 4 13v1s1 1 2 1c-1 2-1 2-1 4v3c-1 2-1 3-1 4l-1 1h0l-1-1v-2s0-1-1-1v1c-1-2-1-4-1-6v-1h-1-1-2v-1s1 0 1-1l-1-8-2-3 2-1v1l1-1c0 1 0 1 1 2v-3-1z" class="N"></path><path d="M440 444c2 2 2 5 3 8 0 2 0 3 1 5 1 1 1 1 1 2v9h1l-1 1h0l-1-1v-2s0-1-1-1c0-4-1-16-3-18v-3z" class="L"></path><path d="M438 445v1l1-1c0 1 0 1 1 2 2 2 3 14 3 18v1c-1-2-1-4-1-6v-1h-1-1-2v-1s1 0 1-1l-1-8-2-3 2-1z" class="R"></path><path d="M438 449c2 4 3 6 4 10h-1-1-2v-1s1 0 1-1l-1-8z" class="O"></path><defs><linearGradient id="AL" x1="481.267" y1="503.79" x2="485.683" y2="496.532" xlink:href="#B"><stop offset="0" stop-color="#0d0c0d"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#AL)" d="M477 486h1s1 0 1 1h3c0 2 1 3 1 4l1 2 7 13c-1 0-1-1-2-1l-1 1v-1c-1-1-2-1-3-1l-1 1-1-1-1 2c0-1 0-1-1-1v-1 1c-1-1-1-2-1-3v1h-1v-1c-1 0-1-1-1-1v-4c0-1-1-1-1-2v7l-1-3v-8l1-3-1-1 1-1h0z"></path><path d="M481 494h1v-2l1-1 1 2-1 1v6c-1-2-1-4-2-6z" class="r"></path><path d="M477 486h1s1 0 1 1h3c0 2 1 3 1 4l-1 1v2h-1l-1-3c0-2 0-3-2-4l-1 1-1-1 1-1h0z" class="E"></path><path d="M477 488l1-1c2 1 2 2 2 4v5h0 0v-1c-1 2-1 3 0 4v3 1h-1v-1c-1 0-1-1-1-1v-4c0-1-1-1-1-2v7l-1-3v-8l1-3z" class="I"></path><defs><linearGradient id="AM" x1="472.549" y1="621.023" x2="465.258" y2="620.574" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#AM)" d="M468 637l-1-11c-1-6-2-13-1-19 0-2 1-5 1-7 2 6 2 12 2 19h2 1-1v1 2h1c-1 2 0 3 0 5 0 4 1 10 0 13l-2-1h-3c1-1 1-1 1-2z"></path><path d="M469 619h2 1-1v1 2c0 1 0 1-1 1h-1v-4z" class="v"></path><path d="M428 392c0 1 1 3 1 3l4 8 2 2v1c1 1 3 3 3 4s0 2 1 4v1c0 1 1 1 1 3h-1l-2-3c0-1 0-1-1-2 0 1-1 1-1 2v2c-1 0-1 0-2 1 0-1-1-2-2-2v1h-1v-1l-3-5c1 0 2 2 3 2-1-4-4-8-6-13 1 1 1 0 1 1h1v-1-3h1 1c0-1-2-3-2-4v-1l1 1 1-1z" class="D"></path><path d="M433 411c1 1 1 2 1 3h0c-1-1-2-1-2-2 1 0 1 0 1-1z" class="I"></path><path d="M426 400v-3h1c1 3 2 6 4 8 0 2 1 5 2 6h0c0 1 0 1-1 1-3-3-5-8-6-12z" class="W"></path><path d="M428 392c0 1 1 3 1 3l4 8 2 2v1c1 1 3 3 3 4s0 2 1 4v1c0 1 1 1 1 3h-1l-2-3c0-1 0-1-1-2l-1-1c0-1-1-1-2-1-1-1-2-4-2-6-2-2-3-5-4-8h1c0-1-2-3-2-4v-1l1 1 1-1z" class="I"></path><path d="M428 392c0 1 1 3 1 3l4 8-4-4c1 2 2 5 2 6-2-2-3-5-4-8h1c0-1-2-3-2-4v-1l1 1 1-1z" class="k"></path><path d="M471 622v-2-1h1c2 2 4 5 7 7 1 0 2 0 3 1 5 2 9 5 12 9 1 1 3 2 5 3 6 3 12 4 19 6h-3-3c-1-1-2-1-3-1-2 0-3-1-4 0v1c-13-3-24-13-33-23h-1z" class="B"></path><path d="M463 552l1-1c0-1 0-1-1-2v-1l1-1 15 27c1 1 1 2 1 3l4 6-1 1-1-1v2l-2-2-2-4h-1v-1h-1c0 1 0 0-1 1l-1-2-3-6c-1-1-2-3-3-5l-1-2v-1-1l-1-1c-1-1-1-2-1-3h0l2 1c-1-1-1-2-2-3l-1-3c-1 0-1 0-1-1z" class="M"></path><path d="M467 563v-1l-1-1c-1-1-1-2-1-3h0l2 1 4 7c0 1 1 2 1 3-2-2-3-4-5-6z" class="o"></path><path d="M482 583c-1-1-3-3-4-5l-3-6 5 5 4 6-1 1-1-1z" class="e"></path><path d="M468 566l-1-2v-1c2 2 3 4 5 6 1 1 1 1 1 2l1 1c-1 1-1 2 0 3s1 2 2 3c0 1 0 0-1 1l-1-2-3-6c-1-1-2-3-3-5z" class="f"></path><path d="M422 524h1c1 0 1 0 1 1 1-1 1-1 1-2 1 0 4 3 5 3l1 1c0 2-1 3-1 4-1 1-1 2-2 2l-2 2h2 0v1l1-1v1 2l1 1h1 1v1l1-1v1 1h-1 1v1h1c1-1 1-2 2-4l2-3h0v3l-3 4c-1 1-1 1-2 1-1 1-1 2-2 2h0v1h-1c-1 1-2 3-3 4h-1c0-1 0-1 1-1 1-1 1-3 2-4v-1h0v-1c-2 0-2 2-3 3l-1 1c-2-2-1-3-2-6 0-1 0-3-1-4v-3h0c0-1 0-1-1-1 0-2 0-4 1-5 1 1 2 2 2 4h0c1 1 0 1 1 2h0 0v-2h0c0-1 0-1-1-2h0c0-1-1-1-1-2 0 0 0-1-1-1h0v-2-1z" class="W"></path><path d="M422 534h2v1c0 1-1 1-2 2v-3z" class="M"></path><path d="M422 534v-5c1 1 2 3 2 5h-2 0z" class="S"></path><path d="M426 531c1 0 0-1 2-1v3l-2 2h2 0v1l1-1v1 2l1 1c0 1-2 3-3 4 0-2 1-3 2-5 0-1 0-1-1-1h0c-1 0-1 1-2 1h0c-1-2-1-3 0-5v-2z" class="M"></path><path d="M422 524h1c1 0 1 0 1 1 1-1 1-1 1-2 1 0 4 3 5 3l1 1c0 2-1 3-1 4-1 1-1 2-2 2v-3c-2 0-1 1-2 1l-4-6v-1z" class="h"></path><path d="M405 506v-2-1c-1-4-1-10 0-14 1 2 1 3 1 5 1 2 1 4 1 5 1 1 1 2 2 3h0 1 0s0 1-1 1c0 2 0 2 1 3 1-1 1-1 2 0v3l1-1c1 2 1 4 2 6l1 4v4l-1-2-1 1c1 2 1 4 1 6h-2v2h-1v-1-1l-1-1v1h-1c0-1 0-2-1-3v-3c1 0 1 0 1-1-2-2-2-5-3-7 0-3-1-5-2-7z" class="c"></path><path d="M411 526v-1-1c1-1 1-2 1-2v-1l1 1c0 2 0 3-1 5l-1-1z" class="R"></path><path d="M414 519v2h0c1 2 1 4 1 6h-2v2h-1v-1-1c1-2 1-3 1-5 1-1 1-2 1-3z" class="S"></path><path d="M413 508c1 2 1 4 2 6l1 4v4l-1-2-1 1h0v-2c0-4-1-7-2-10l1-1z" class="x"></path><path d="M407 504h0v-1-1-3c1 1 1 2 2 3h0 1 0s0 1-1 1c0 2 0 2 1 3 0 3 2 7 2 10-1-1-1-2-1-3-1-2-2-7-3-8l-1-1z" class="M"></path><path d="M405 506v-2-1c-1-4-1-10 0-14 1 2 1 3 1 5 1 2 1 4 1 5v3 1 1h0c1 4 2 8 3 11v2c1 0 1 1 1 1h-1 0v-2-1l-1-3-3-6h-1z" class="f"></path><path d="M407 429h1v-3h0c1 1 1 2 2 4l2 5c-1-2-1-6-1-8h0l2 8v8l1 9c0 2 0 6-1 8l-1 1c-1-1-1-2-1-3v-3h-1v-1l-1-3v5 7 2h-1l-1-2v-9-2-6l-1-7c1 0 1 0 1 1 1-1 1-2 1-3h0c0-3 0-5-1-7v-1z" class="Y"></path><defs><linearGradient id="AN" x1="406.528" y1="448.971" x2="409.217" y2="440.797" xlink:href="#B"><stop offset="0" stop-color="#a5a4a5"></stop><stop offset="1" stop-color="#c5c4c5"></stop></linearGradient></defs><path fill="url(#AN)" d="M406 439c1 0 1 0 1 1 1-1 1-2 1-3h0l1 13h-1c0-2-1-3-1-4h0l-1-7z"></path><defs><linearGradient id="AO" x1="408.856" y1="462.221" x2="407.435" y2="450.878" xlink:href="#B"><stop offset="0" stop-color="#676766"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#AO)" d="M407 446h0c0 1 1 2 1 4h1v6 7 2h-1l-1-2v-9-2-6z"></path><path d="M409 451c0-2 1-5 1-7-1-1-1-2-1-3h1v2 2l1 2v2h2v-6l1 9c0 2 0 6-1 8l-1 1c-1-1-1-2-1-3v-3h-1v-1l-1-3z" class="Q"></path><path d="M413 443l1 9c-1 1-1 2-1 2l-2 2v-7h2v-6z" class="K"></path><defs><linearGradient id="AP" x1="409.502" y1="439.597" x2="412.098" y2="439.438" xlink:href="#B"><stop offset="0" stop-color="#80807f"></stop><stop offset="1" stop-color="#989798"></stop></linearGradient></defs><path fill="url(#AP)" d="M411 427l2 8v8 6h-2v-2c0-1 0-4-1-6 0-1 0-3-1-5v-6h1l2 5c-1-2-1-6-1-8h0z"></path><path d="M382 304c0 2 0 4 1 6h1 0l1 1c1 2 3 3 3 4h0l2 3s0-1 1-1l1 1 1 1 2 2v-1h0l3 6h1c2 5 5 7 9 11h-2l-3-2-1 1c1 0 1 2 1 2l2 2-2 1h0c-1-3-4-5-6-7l-11-12 1-1-1-1c1-1-4-8-4-9v-7z" class="k"></path><path d="M390 318s0-1 1-1l1 1 1 1 2 2v-1h0l3 6h1c2 5 5 7 9 11h-2l-3-2-1 1-6-6c-2-3-4-8-6-12z" class="T"></path><path d="M393 319l2 2v-1h0l3 6h1c2 5 5 7 9 11h-2l-3-2c-2-2-4-5-6-8-1-2-3-5-4-8z" class="Y"></path><path d="M361 410h1v-1 11s0 3 1 3v21h0c0 2 0 6-1 7 0 3-1 5-1 8v2 17 14c0 6 1 13 0 19h-1v-3-23l-1-11v-1h-1l1-26v-5h1v1 1c0-3 0-5 1-7v-27z" class="H"></path><path d="M359 447v-5h1v1 1c0-3 0-5 1-7v13c0 3-1 6-1 10v17 8l-1-11v-1h-1l1-26z" class="h"></path><path d="M380 384v-2c-1-1-1-4-1-5h1c0 1 1 2 2 3v1h0l1 1 1 1v3h0-1v-1c-1 1-1 2-1 3h1v2h0l1-1c2 4 3 8 4 11l3 11c0 1 0 3 1 5 0 1 0 4 1 5v2l-2-2h1l-1-1c-1 1 0 1-1 2l-1 1h-1v-2c-1-2-1-3-1-5l-1-2c0-2-1-5-1-8l-3-11v-1c0-2-1-5-3-7h0v2h0v5c-1-1-1-2-1-4h0v-4-1c0-1 1-1 2-1z" class="z"></path><path d="M392 416c0 1 0 4 1 5v2l-2-2h1l-1-1c-1 1 0 1-1 2l-1 1h-1v-2c-1-2-1-3-1-5v2c1 2 1 3 2 5 1-2 0-4 1-6 0-1 1-1 2-1zm-12-32v-2c-1-1-1-4-1-5h1c0 1 1 2 2 3v1h0l1 1 1 1v3h0-1v-1c-1 1-1 2-1 3h1v2h0l2 6c1 3 2 5 2 8v1c-1-2-1-4-2-5 0-2-1-3-2-5v-1h-1c0-2-1-5-3-7h0v2h0v5c-1-1-1-2-1-4h0v-4-1c0-1 1-1 2-1zm57 139l1 1 1 1h4c2 0 2 0 3-1l1 1v-1c0 1 1 2 1 2h1v1c0 1-1 1-1 2l-1 1c0 2-1 3-2 5l-1 1c-1 0-2-1-3-1h0 0c-1 0-1-1-2-1l-1 1h0l-2 3c-1 2-1 3-2 4h-1v-1h-1 1v-1-1l-1 1v-1h-1-1l-1-1v-2-1l-1 1v-1h0-2l2-2c1 0 1-1 2-2 0-1 1-2 1-4 1 0 2 0 2 1h4l1-1-1-1h0-1l1-3z" class="B"></path><path d="M436 535l4-5c0 2 0 2-1 3v1c-1 0-1 1-1 1l-2 3-1-1c-1 0-1 1-2 1h0c0-1 1-1 1-2h1l1-1zm-5-8c1 0 2 0 2 1h0c1 1 1 1 1 2-1 1-1 3-2 4h0c0 2-1 3-2 4 0-2 0-4 1-6l-1-1c0-1 1-2 1-4z" class="c"></path><path d="M433 528c1 1 1 1 1 2-1 1-1 3-2 4h0-1 0c0-2 1-4 2-6z" class="p"></path><path d="M437 526h3v1s0 1-1 1l1 2h0l-4 5-1 1h-1c0-1 0-1-1-2h-1 0c1-1 1-3 2-4 0-1 0-1-1-2h0 4l1-1-1-1z" class="R"></path><path d="M436 535h-1c1-1 1-2 0-3 1-2 2-3 4-4l1 2h0l-4 5z" class="n"></path><path d="M437 523l1 1 1 1h4c2 0 2 0 3-1l1 1v-1c0 1 1 2 1 2h1v1c0 1-1 1-1 2l-1 1c0 2-1 3-2 5l-1 1c-1 0-2-1-3-1h0 0c-1 0-1-1-2-1l-1 1h0s0-1 1-1v-1c1-1 1-1 1-3h0l-1-2c1 0 1-1 1-1v-1h-3 0-1l1-3z" class="U"></path><path d="M443 529v1c0 1 0 2-1 2 0 1-1 1-1 1v-2l2-2z" class="Y"></path><path d="M443 528l2 1c0 2-1 2-2 3v1l-1-1c1 0 1-1 1-2v-1-1zm-6-5l1 1 1 1h4v2-1l-2-1c-1 1-1 2 0 4h0l-1 1-1-2c1 0 1-1 1-1v-1h-3 0-1l1-3z" class="Q"></path><path d="M446 524l1 1v-1c0 1 1 2 1 2h1v1c0 1-1 1-1 2l-1 1c0 2-1 3-2 5l-1 1c-1 0-2-1-3-1l2-2v-1c1-1 2-1 2-3l-2-1h0v-1-2c2 0 2 0 3-1z" class="p"></path><path d="M447 530c0-1-1 0-1-1v-1h1l1 1-1 1zm0-6c0 1 1 2 1 2 0 1-1 1-2 1v-1l1-1v-1z" class="n"></path><path d="M443 532c1 1 1 2 2 3l-1 1c-1 0-2-1-3-1l2-2v-1zm3-8l1 1-1 1c-1 1-1 2-1 3l-2-1h0v-1-2c2 0 2 0 3-1z" class="o"></path><path d="M379 394v-5h0v-2h0c2 2 3 5 3 7v1l3 11c0 3 1 6 1 8l1 2c0 2 0 3 1 5v2h1l1-1c1-1 0-1 1-2l1 1h-1v4 1 1 3l-1 4s0 1-1 2v-4l-1 1-1-1h0v-2h-1-2v-1s-1-1-1-2h0l-3-27h0l-1-6z" class="Y"></path><path d="M389 428c1-1 2-1 2-1v3h-1-1v-2z" class="Q"></path><path d="M386 414l1 2c0 2 0 3 1 5v4h1c-1 1-1 2-2 2l-1-1 1-1c-1-3-1-8-1-11z" class="J"></path><path d="M388 421v2h1l1-1c1-1 0-1 1-2l1 1h-1v4 1 1s-1 0-2 1v-3h-1v-4z" class="S"></path><path d="M379 394v-5h0v-2h0c2 2 3 5 3 7v1l-1 1c0 3 1 7 2 10v3h0l-3-9-1-6z" class="Q"></path><defs><linearGradient id="AQ" x1="385.54" y1="416.712" x2="380.413" y2="420.475" xlink:href="#B"><stop offset="0" stop-color="#333533"></stop><stop offset="1" stop-color="#504c4f"></stop></linearGradient></defs><path fill="url(#AQ)" d="M380 400l3 9h0c1 3 1 7 2 11v4c0 1 0 1-1 1 0 1-1 1-1 2h0l-3-27h0z"></path><path d="M386 579c10 9 21 14 35 16l-1-1 5 1 9 1h3l29 1 1 1h-3 1c0 1 0 1 1 1v1l-1 1c-1 0-3-1-5-1h-13-11-6-1c-1-1-2 0-3-1 0 0-1-1-2-1v1h0-3c-1 0-3-1-4-1l-11-3h0 2 2l8 2h1c1 1 1 1 1 0h1c-1 0-1-1-2-1h0-1-2c-1-1 0-1-1-1-2 0-3 0-5-1-1 0-2 0-3-1h-2l-2-1c0-1-2-2-3-2-4-1-7-4-10-6-1-1-4-3-4-5z" class="U"></path><path d="M420 594l5 1 9 1-3 1h0c-3 1-7-1-10-2l-1-1z" class="O"></path><path d="M434 596h3l29 1 1 1h-3-6c-9 0-18 1-27-1h0l3-1z" class="H"></path><path d="M430 499l1 1h1c-1-1-2-2-2-3h-1l-1-3-1-1-1-1v-2c-1-1-1-1-1-2-1-1-1-1-1-2s-1-1-2-2l-2-6s-1-1-1-2v-3c0-3 0-6-1-9h0 1c1 2 0 5 1 6h0l1-1v-1h-1v-1l2-2v-1l-1-1v-1c-1-1-1-1 0-2 0 1 1 4 1 5 1 1 2 1 2 2s1 1 1 2c1 2 1 4 2 6h-1l4 11v2l2 4 4 4 1 1c0 2 1 3 0 5h-1l-2-2-1 1h-1l-2-2z" class="f"></path><path d="M422 477c-1-2-1-3-1-5l-1-1c1-1 1-2 1-4l1-1v1c0 1 0 3 1 5-2 2-1 3-1 5z" class="B"></path><path d="M422 477c0-2-1-3 1-5 0 3 1 7 2 10l2 7h-1c-2-4-3-8-4-12z" class="J"></path><path d="M429 489v-1h1l2 4h-1c0 2 1 3 0 5l-1-2c-2-1-3-4-4-6h1c1 0 1-1 2-1v1z" class="S"></path><path d="M429 489v-1h1l2 4h-1c0 2 1 3 0 5l-1-2 1-1c0-1-1-4-2-5z" class="b"></path><path d="M426 480v-5h0l4 11v2h-1v1-1c-1 0-1 1-2 1l-2-7c1 0 1 1 1 2v-4z" class="T"></path><path d="M426 480l3 8c-1 0-1 1-2 1l-2-7c1 0 1 1 1 2v-4z" class="M"></path><path d="M431 497c1-2 0-3 0-5h1l4 4 1 1c0 2 1 3 0 5h-1l-2-2c-2-1-2-2-3-3z" class="C"></path><path d="M488 526v-1h1s1 1 1 2c0 2 0 5 1 7 1 3 1 5 1 7v2h-1v-1l-2 2 1 2h0c-1 1-1 1-1 2h-5 0c-1 0-1 0-3 1-2 0-3 1-4 1-2-2-1-3-1-6 1 0 1 0 2-1l-1-2c1 0 1-1 1-2v-2l1-1 1-1h0c0-2 1-3 2-4 1 0 1-1 1-2l1-1v-1h1 0 1c0 1 1 1 2 2l1-1-1-2z" class="S"></path><path d="M481 540c0-3 1-5 2-7l2 3h1v-1h1s0 1 1 1l1 1h-1v4 2c1 0 1 0 1 1l-1 1c0-1 0-3-1-4h-1c-1-1-1-1-1-2h0c-1-1-1-1-2-1h0l-2 2z" class="c"></path><path d="M488 526v-1h1s1 1 1 2c-1 2-1 6-1 8-1-2-2-4-4-5l1 5v1h-1l-2-3c-1 2-2 4-2 7h-1c-1 0-1 0-2-1v-2l1-1 1-1h0c0-2 1-3 2-4 1 0 1-1 1-2l1-1v-1h1 0 1c0 1 1 1 2 2l1-1-1-2z" class="M"></path><path d="M483 538h0c1 0 1 0 2 1h0c0 1 0 1 1 2h1c1 1 1 3 1 4v2h-1c-1 1-2 0-3 1-1 0-1 0-3 1-2 0-3 1-4 1-2-2-1-3-1-6 1 0 1 0 2-1l-1-2c1 0 1-1 1-2 1 1 1 1 2 1h1l2-2z" class="m"></path><path d="M483 538h0c1 0 1 0 2 1h0c0 1 0 1 1 2h1v4 1c-1 1-1-1-3 0v1c-1 0-1 0-2 1h0c-1 0-2 0-2-1-1-2-1-2-1-3l1-1v-2l2 2c1-2 1-3 1-5z" class="T"></path><path d="M484 542h1l1 1v1l-2 1v-3z" class="H"></path><path d="M479 544c1 0 1-1 2 0h2v-1l1-1v3 1 1c-1 0-1 0-2 1h0c-1 0-2 0-2-1-1-2-1-2-1-3z" class="G"></path><path d="M426 421c-1-2-4-4-5-6v-2c0-2-1-3-1-5 1 1 2 3 3 4 1 0 1 1 2 2 0-1 0-1-1-2v-2l4 7 2-1v1l1 4 1-1h0 0 1v1h0l4 6h0c2 2 4 5 5 7v1h0v3 1 3l-1-1c-1 0-1-1-1-1v3 1 3c-1-1-1-1-1-2l-1 1v-1c-1-3-3-5-4-7-2-5-6-9-9-14l1-1-1-1 1-1z" class="M"></path><path d="M426 421c2 2 3 4 3 6v1l-3-5-1-1 1-1z" class="W"></path><path d="M431 425c-1-1-2-3-3-5-1-1-3-3-4-5h1l4 4c0-1 0-1-1-2l2-1v1l1 4 1-1h0 0 1v1h0l4 6h0c2 2 4 5 5 7v1h0c-1-1-2-3-4-5v1c-1 0-1-1-2-1h-1c-1-1-2-3-4-5z" class="p"></path><path d="M430 416v1l1 4 1 2c-1-1-3-2-3-4 0-1 0-1-1-2l2-1z" class="r"></path><path d="M431 421l1-1h0 0 1v1h0l4 6v3c-2-2-4-5-5-7l-1-2z" class="S"></path><path d="M426 423l3 5c1 1 1 2 2 3l1-1c0-2-1-3-1-5 2 2 3 4 4 5h1c1 0 1 1 2 1v-1c2 2 3 4 4 5v3 1 3l-1-1c-1 0-1-1-1-1v3 1 3c-1-1-1-1-1-2l-1 1v-1c-1-3-3-5-4-7-2-5-6-9-9-14l1-1z" class="r"></path><path d="M432 430c1 1 1 2 2 3v1c-1 0-2-2-3-3l1-1z" class="D"></path><path d="M440 443c-2-4-3-7-4-11 2 2 3 4 4 7l1 2c-1 0-1-1-1-1v3z" class="L"></path><path d="M435 430h1c1 0 1 1 2 1v-1c2 2 3 4 4 5v3 1 3l-1-1-1-2c0-1-1-2-1-3-1-2-1-3-2-4l-2-2z" class="O"></path><path d="M414 560l1 1v1h0 1v1h3c1-1 0-1 1-3 2 0 3 0 5 1 0 1 1 2 1 3h0l2 3 1 1h2s0 1 1 1 2 0 3 1h1v-2l1 1v-1l2 2h1l1 1c0 2 0 2-1 3h0c-1 0-1 1-1 1 0 1 0 1-1 2h-3v-1c-1 0-2 0-3 1h0c-1-1-2-1-2-1h-1-1l2-2v-1h-1-2c0-1 1-1 2-2-1-1-3 0-4-1h-1c0-1-1-1-2 0h0c-2-1-2 0-3 1-2 1-4 2-5 4v-1-1h-3v1h-1c-1-2-1-4-1-6l1-1c-1-2-1-3 0-4h0s0-1 1 0h1v-2h1l1-1z" class="p"></path><path d="M410 574c-1-2-1-4-1-6l1-1c-1-2-1-3 0-4v2h2c0 1-1 1 0 2h0 1c1 1 0 1 0 2h2v1l2-1 1 1v1h1c-2 1-4 2-5 4v-1-1h-3v1h-1z" class="B"></path><path d="M412 567h1c1 1 0 1 0 2h2v1l2-1 1 1v1h1c-2 1-4 2-5 4v-1c0-1 1-2 1-3h0c-1 0-2 1-2 1v-1c-1-1-1-1-2-1 0-2 1-2 1-3z" class="r"></path><path d="M416 563h3c1-1 0-1 1-3 2 0 3 0 5 1 0 1 1 2 1 3h0l2 3c-1 0-3 1-4 1 0 0-1-1-2-1-1 1 1 1-1 1h-3l-1-2v-1l-1-2z" class="C"></path><path d="M421 568l-1-2 2-2v-2h0 0c1 0 1 0 2 1 0 1 0 1 1 2l1-1 2 3c-1 0-3 1-4 1 0 0-1-1-2-1-1 1 1 1-1 1z" class="X"></path><path d="M437 568l2 2h1l1 1c0 2 0 2-1 3h0c-1 0-1 1-1 1 0 1 0 1-1 2h-3v-1c-1 0-2 0-3 1h0c-1-1-2-1-2-1h-1-1l2-2h0c1 0 1-1 1-2l-1-1c-1-1-1-1-2-1h-1 0v-1c3 0 5 0 8 1h1v-2l1 1v-1z" class="G"></path><path d="M430 571v-1c2 0 3 1 5 1v2c0 1-1 1-2 1-1-1-1-2-3-3z" class="N"></path><path d="M430 576c1-1 1-1 3-1v1c1 0 2-1 3-1v-2h1l2 2c0 1 0 1-1 2h-3v-1c-1 0-2 0-3 1h0c-1-1-2-1-2-1z" class="Y"></path><path d="M437 568l2 2h1l1 1c0 2 0 2-1 3h0l-4-4v-2l1 1v-1z" class="S"></path><path d="M364 338c1-3 0-13 1-15v8 4s1 1 1 2v5 137h-1l-1-36v-2c0 1-1 2-1 3v-21-13h0c0-2 0-3 1-4v-46c1-2 0-4 0-7v-15z" class="b"></path><path d="M363 410h0c0-2 0-3 1-4v35c0 1-1 2-1 3v-21-13z" class="D"></path><path d="M364 441v2l1 36-1 28c0 4 0 8 1 12 0 4 0 8 1 12 1 10 3 21 7 30 2 5 5 9 8 13h-1c-2-2-4-4-5-7-1-1-2-3-3-4v-1c0-1-1-2-1-2v2h0s0-1-1-1v-1-1c0-1 0-1-1-1 0-2 0-3-1-5v-2c-1 0-1 0-1-1v-2-2c-1-1-1-1-1-2h0v-3c-1-1 0-1 0-2-1-1-1-1-1-2v-2c-1-1-1-2-1-3v-4c-1-1 0-2-1-3v-7c-1-7-1-15-1-22v-18c0-5 1-13-1-17v-2c0-3 1-5 1-8 1-1 1-5 1-7h0c0-1 1-2 1-3z" class="R"></path><path d="M364 441v2 5h-1v-4h0c0-1 1-2 1-3z" class="J"></path><path d="M361 461v-2c0-3 1-5 1-8l1 3v8c1 7 2 14 1 21-1-4 0-9-1-13v-4l-1 11v1c0-5 1-13-1-17z" class="l"></path><path d="M362 478v-1l1-11v4c1 4 0 9 1 13-1 10-1 19-1 29 1 6 1 12 2 19 1 9 2 20 6 29v2h0s0-1-1-1v-1-1c0-1 0-1-1-1 0-2 0-3-1-5v-2c-1 0-1 0-1-1v-2-2c-1-1-1-1-1-2h0v-3c-1-1 0-1 0-2-1-1-1-1-1-2v-2c-1-1-1-2-1-3v-4c-1-1 0-2-1-3v-7c-1-7-1-15-1-22v-18z" class="L"></path><path d="M464 536l1-1v1h1 0c-1 1-1 2-1 2h1l1 1-2 2 1 1-3 3v1l1 1-1 1v1c1 1 1 1 1 2l-1 1c0 1 0 1 1 1l1 3c1 1 1 2 2 3l-2-1h0c0 1 0 2 1 3l1 1v1 1l1 2c1 2 2 4 3 5l3 6-1 1c-2-3-4-6-7-8-2-3-5-5-7-7l-3-4-2-3-1-1 1-1c0-1 0-2-1-3s-1-1-2 0c0 0 0 1-1 1l-1-1c1 0 1-1 1-1l1-2c1-1 2-3 3-3h1c1 0 2-1 2-2 3-2 5-4 7-7z" class="B"></path><path d="M461 557c0-1-1-1 0-3h0 1 0l2-1 1 3c-1-1-2-1-3-1l-1 2z" class="R"></path><path d="M461 557l1-2c1 0 2 0 3 1s1 2 2 3l-2-1h0c0 1 0 2 1 3l1 1v1 1l1 2c0 1 0 1-1 1l-6-10z" class="Y"></path><path d="M454 556v-1c1 1 1 1 2 1 0-1 1-2 1-2 0-1-1-2-1-4 1 1 3 4 3 6h-1c1 1 1 1 1 2l2 3 2 2c0 1 0 1 1 2h0c-1 1-1 1-1 0-1 0-2-1-2-2-1-1-1-1-1-2h-1v-1c-1 0-1-1-2-2l-1 1-2-3z" class="I"></path><path d="M464 536l1-1v1h1 0c-1 1-1 2-1 2h1l1 1-2 2 1 1-3 3v1l1 1-1 1v1c1 1 1 1 1 2l-1 1-2-2c-1 0-1 0-1-1h-1c0 2 1 5 1 7h-1c0-2-2-5-3-6 0 2 1 3 1 4 0 0-1 1-1 2-1 0-1 0-2-1v1l-1-1 1-1c0-1 0-2-1-3s-1-1-2 0c0 0 0 1-1 1l-1-1c1 0 1-1 1-1l1-2c1-1 2-3 3-3h1c1 0 2-1 2-2 3-2 5-4 7-7z" class="W"></path><path d="M464 536l1-1v1h1 0c-1 1-1 2-1 2h1l1 1-2 2 1 1-3 3v1c-2 0-2-1-3-2 0-1 1-1 2-2s1-3 2-4h1v-1l-1-1z" class="I"></path><defs><linearGradient id="AR" x1="359.439" y1="314.955" x2="364.163" y2="316.309" xlink:href="#B"><stop offset="0" stop-color="#4a4b4d"></stop><stop offset="1" stop-color="#706e6c"></stop></linearGradient></defs><path fill="url(#AR)" d="M369 279v1h0c-1 1-4 2-4 4 0 1 0 1-1 2v13l1-1v17c0 3 0 6 1 9v13c0-1-1-2-1-2v-4-8c-1 2 0 12-1 15v15c0 3 1 5 0 7v-8-22h-1v-3-3c0-1 1-2 0-3v-4 1c0 1-1 2-1 2-1 4 0 8-1 12v-4 1h-1l-1-1-1 1h0v-1-7l1-5v-4-5-2-1c-1-4-1-8 0-12 0-1-1-3 0-4 0-1 1-2 1-2v-1c2-2 5-4 8-6h1z"></path><path d="M359 312h1s1 0 1 1l-1 6h-1v-3-4z" class="C"></path><path d="M359 316v3h1v3c1 2 0 5 0 7l-1-1-1 1h0v-1-7l1-5z" class="p"></path><path d="M364 299l1-1v17c0 3 0 6 1 9v13c0-1-1-2-1-2v-4-8c-1 2 0 12-1 15v-39z" class="F"></path><path d="M369 279v1h0c-1 1-4 2-4 4 0 1 0 1-1 2-1 4-1 9-1 13-1 2 0 4-1 6 0 3-1 6-1 8 0-1-1-1-1-1h-1v-5-2-1c-1-4-1-8 0-12 0-1-1-3 0-4 0-1 1-2 1-2v-1c2-2 5-4 8-6h1z" class="l"></path><path d="M359 307v-4c1 1 1 3 1 5h1v-5l1-1v3c0 3-1 6-1 8 0-1-1-1-1-1h-1v-5z" class="q"></path><path d="M369 279v1h0c-1 1-4 2-4 4-2 1-3 1-4 4 0 2 0 3-1 5h0c0-2 0-5 1-7h-1v-1c2-2 5-4 8-6h1z" class="B"></path><path d="M419 571c1-1 1-2 3-1h0c1-1 2-1 2 0h1c1 1 3 0 4 1-1 1-2 1-2 2h2 1v1l-2 2c-1 1-2 1-3 2h-3c-1 0-2-1-3-1h0c-1 0-1 1 0 1-1 1-2 1-2 2 1 2 3 3 3 6h0c1 1 1 1 1 2l1 2v1 1c1 1 3 1 3 2h1l-1 1-5-1-12-3v-1l1-1-1-1-1-1c-1 0-1-1-2-2l1-1c0-1 0-1 1-2v-2h-1c-1-1-2-1-2-1v-1-1c-1-1-1-1-2-1l-2-1v-2-2h1l4 4h0c1 0 2 1 2 1 1-1 1-1 2-1 0-1 1-1 1-1h1v-1h3v1 1c1-2 3-3 5-4z" class="W"></path><path d="M411 574v-1h3v1 1l-1 1c-1 0-1-1-2-2z" class="M"></path><path d="M404 578c1 0 1-1 2-1h2c2 0 2 1 3 2v1 1c-1 0-2 1-3 1h-1v-2h-1c-1-1-2-1-2-1v-1z" class="n"></path><path d="M411 579l2 1h0 0c1 1 1 1 2 1 1 1 1 2 1 2l2 1v2c-1 1-1 1-2 1v-1c-1 0-1 1-3 1h-1l-1 2-1-1v1h-1l-1-1-1-1c-1 0-1-1-2-2l1-1c0-1 0-1 1-2h1c1 0 2-1 3-1v-1-1z" class="h"></path><path d="M411 584c1-1 0-1 1-1 0 1 0 1-1 1v1c1 0 2 1 3 1v-1 1h-1 0v1h-1l-2-1c0-1 1-1 1-2z" class="r"></path><path d="M413 580h0c1 1 1 1 2 1 1 1 1 2 1 2l2 1v2c-1 1-1 1-2 1v-1c-1 0-1 1-3 1v-1h0 1v-1c0-1 0-1 1-2h0l-2-2v-1z" class="Q"></path><path d="M411 581h1l-1 1h0c-1 0-2 1-2 2v1h1l1-1c0 1-1 1-1 2l2 1-1 2-1-1v1h-1l-1-1-1-1c-1 0-1-1-2-2l1-1c0-1 0-1 1-2h1c1 0 2-1 3-1z" class="J"></path><path d="M406 584h0 2v2l-1 1c-1 0-1-1-2-2l1-1z" class="a"></path><path d="M413 587c2 0 2-1 3-1v1c1 0 1 0 2-1 1 1 1 1 2 1l1 1 1 2v1 1c1 1 3 1 3 2h1l-1 1-5-1-12-3v-1l1-1h1v-1l1 1 1-2h1z" class="f"></path><path d="M413 587c2 0 2-1 3-1v1c1 0 1 0 2-1 1 1 1 1 2 1l1 1 1 2v1c-2 0-2 1-4 0l-8-2v-1l1 1 1-2h1z" class="M"></path><path d="M411 589h5c0-1 0-1 1-1h1 0v1c1 0 3 1 4 1v1c-2 0-2 1-4 0l-8-2v-1l1 1z" class="Q"></path><path d="M361 332c1-4 0-8 1-12 0 0 1-1 1-2v-1 4c1 1 0 2 0 3v3 3h1v22 8 46c-1 1-1 2-1 4h0v13c-1 0-1-3-1-3v-11 1h-1v-5l1-12v-12c0-2 0-4-1-5-1 1 0 5 0 7 0-1 0-2-1-3v-1c0 3 0 7-1 9v-11-8-21l-1-19h0l1-1 1 1h1v-1 4z" class="m"></path><path d="M362 393v-15h1v16c0 2-1 5 0 8v8 13c-1 0-1-3-1-3v-11 1h-1v-5l1-12z" class="T"></path><path d="M361 405l1-1v1 4 1h-1v-5z" class="q"></path><path d="M363 330h1v22 8 46c-1 1-1 2-1 4h0v-8c1-4 0-10 0-14v-32-26z" class="B"></path><path d="M361 329v-1 4 7c1 6 1 13 0 19 0 2-1 4-1 7h0v-2c0 5 0 10-1 14v-8-21l-1-19h0l1-1 1 1h1z" class="K"></path><path d="M361 329v-1 4 7 12h0c-1-1-1-2-1-3v-10c0-1 0-1-1-2v1c0 3 1 8 0 11l-1-19h0l1-1 1 1h1z" class="E"></path><path d="M358 329l1-1 1 1h1v1c-1 1-2 0-2 0l-1-1z" class="n"></path><path d="M475 491h1 0v8l1 3v3c1 1 0 10 0 12s-1 4-1 6c-1 2-1 6-3 8 1 1 1 1 0 2h-1c-2 1-2 3-3 5s-2 3-3 4l-1-1 2-2-1-1h-1s0-1 1-2h0-1v-1l-1 1c-2 3-4 5-7 7 0 1-1 2-2 2h-1c3-2 5-4 6-7 2-2 2-3 3-5-1-1-1-1-1-2h0v-1c0-1 1-2 1-3l2-2c-1-1-1-1 0-3l2-3v-1l1-1v1h0c0-1 1-2 1-3 1 1 1 2 2 2v-5l-1-6c0-3 0-6-1-9v-3h1l1 2v-1 1l1-1v-1h1v1h2v-4z" class="B"></path><path d="M472 526l1 1v1c-1 2-2 4-3 5h-1v-1c1-2 2-3 3-5v-1z" class="I"></path><path d="M470 524v1 1c1 1 1 1 2 1-1 2-2 3-3 5 0-1-1-2-1-3 0-2 1-3 2-5z" class="U"></path><path d="M469 519v4h1v1c-1 2-2 3-2 5l-2 4-1-1 1-3c1-2 3-6 2-8 0-1 1-1 1-2z" class="E"></path><path d="M467 518l1-1v1h0l1 1c0 1-1 1-1 2 1 2-1 6-2 8h0v-2c1-2 1-2 1-4v-1c-1 1-2 2-2 3-1-1-1-1 0-3l2-3v-1z" class="U"></path><path d="M467 518l1-1v1 2h-1v-1-1z" class="S"></path><path d="M471 512l1 2v-1h0v1 6h1l1-1v2h0l-2 5v1c-1 0-1 0-2-1v-1-1-1h-1v-4l-1-1c0-1 1-2 1-3 1 1 1 2 2 2v-5z" class="c"></path><path d="M472 514v6h1l1-1v2h0l-3 3c1-4 1-7 1-10z" class="o"></path><path d="M471 524l3-3-2 5v1c-1 0-1 0-2-1v-1l1-1z" class="Y"></path><path d="M469 515c1 1 1 2 2 2l-1 6h-1v-4l-1-1c0-1 1-2 1-3z" class="h"></path><defs><linearGradient id="AS" x1="468.528" y1="516.743" x2="476.323" y2="501.685" xlink:href="#B"><stop offset="0" stop-color="#797a78"></stop><stop offset="1" stop-color="#aaa7ab"></stop></linearGradient></defs><path fill="url(#AS)" d="M475 491h1 0v8l1 3v3 6c-1 2-2 4-2 6-1 1-1 3-1 4v-2l-1 1h-1v-6-1h0v1l-1-2-1-6c0-3 0-6-1-9v-3h1l1 2v-1 1l1-1v-1h1v1h2v-4z"></path><path d="M469 497v-3h1l1 2v2c1 5 1 10 1 15h0v1l-1-2-1-6c0-3 0-6-1-9z" class="Q"></path><path d="M476 499l1 3v3 6c-1 2-2 4-2 6-1 1-1 3-1 4v-2-5c0-3 1-5 1-8 1-2 1-4 1-6v-1z" class="r"></path><path d="M475 491h1 0v8 1c0 1-1 1-1 2v1h-1v-3c-1-1-2-1-3-2v-2-1 1l1-1v-1h1v1h2v-4z" class="G"></path><path d="M440 574h0l2 3c0 1 0 1 1 2v2h0-2v2c0 1 0 1-1 2h0v1l-2 1 1 1 2-1h0v3h2l-1 2c-2 2-3 3-5 3v1h-3l-9-1 1-1h-1c0-1-2-1-3-2v-1-1l-1-2c0-1 0-1-1-2h0c0-3-2-4-3-6 0-1 1-1 2-2-1 0-1-1 0-1h0c1 0 2 1 3 1h3c1-1 2-1 3-2h1 1s1 0 2 1h0c1-1 2-1 3-1v1h3c1-1 1-1 1-2 0 0 0-1 1-1z" class="z"></path><path d="M419 578c-1 0-1-1 0-1h0c1 0 2 1 3 1h3 1 0c1 0 2-1 3-1v1 1h2l-1 1-1 1h0c-1 0-1-1-2-1s-2-1-3 0c0 0 0 1-1 1 0 0-1 0-1-1-2 0-2-1-3-2z" class="I"></path><path d="M440 574h0l2 3c0 1 0 1 1 2v2h0-2v2h-1 0v-1h-2-1l-1 1v1l1 1h-2l-2 1h-1-1l-1-1 1-2c-1 0-1-1-2-2h0l1-1 1-1h-2v-1-1c-1 0-2 1-3 1h0-1c1-1 2-1 3-2h1 1s1 0 2 1h0c1-1 2-1 3-1v1h3c1-1 1-1 1-2 0 0 0-1 1-1z" class="S"></path><path d="M442 577c0 1 0 1 1 2v2h0-2v2h-1 0v-1h-2 0c1 0 2-1 3-1-1-1-1-1-1-3 1 0 1 0 2-1z" class="J"></path><path d="M432 586v-1h0l-1-1v-1c2-1 4-1 6-1l-1 1v1l1 1h-2l-2 1h-1z" class="r"></path><path d="M436 583v1l1 1h-2l-2 1c-1-1-1-1 0-2h1c1 0 2-1 2-1z" class="E"></path><path d="M440 574h0l2 3c-1 1-1 1-2 1l-1-1c-1 0-2 1-4 1-1 1-2 1-3 1v1c1 0 2 0 3-1 1 0 3-1 4 0h0c-3 1-5 2-7 3-1 0-1 0-2-1h0v-1l1-1h-2v-1-1c-1 0-2 1-3 1h0-1c1-1 2-1 3-2h1 1s1 0 2 1h0c1-1 2-1 3-1v1h3c1-1 1-1 1-2 0 0 0-1 1-1z" class="n"></path><path d="M425 578c1-1 2-1 3-2h1 1s1 0 2 1h0c1-1 2-1 3-1v1c-1 0-2 1-4 1h-2v-1c-1 0-2 1-3 1h0-1z" class="R"></path><path d="M437 582h1 2v1h0 1c0 1 0 1-1 2h0v1l-2 1 1 1 2-1h0v3h2l-1 2c-2 2-3 3-5 3v1h-3l-9-1 1-1c1 0 2 1 3 0 0-1 0-1-1-2h0c0-1 1-2 2-3 0-1 0-1 1-1v-2h1 1l2-1h2l-1-1v-1l1-1z" class="B"></path><path d="M439 588l2-1h0v3c-2 0-2-1-4 0h-3-2v-1h2c2 0 3 0 5-1z" class="Y"></path><path d="M437 582h1 2v1h0 1c0 1 0 1-1 2h0v1l-2 1-4 1h-2v-1c1 0 2-1 2-1h3v-1h-2 2l-1-1v-1l1-1z" class="U"></path><path d="M437 582h1 2v1l-3 2-1-1v-1l1-1z" class="K"></path><path d="M434 590h3c2-1 2 0 4 0h2l-1 2c-2 2-3 3-5 3s-7 0-8-1v-1l1-2h1c1 0 2 0 3-1z" class="k"></path><path d="M429 593h1c1 0 3 0 4 1v1c0-1 0-1 1-1 0-1 0-1 1-1 1 1 1 1 2 1v-1c1 0 2 0 3-1h0 1c-2 2-3 3-5 3s-7 0-8-1v-1z" class="p"></path><path d="M369 279c6-5 13-8 20-11v2c-1 1-3 2-3 2-1 1-7 6-6 7h0l-1 2c1 1 1 2 0 4 0 0 0 1-1 2h0v4c-1-1-1-2-1-3h-1l-1 3v2 4c1 2 1 5 1 8h-1c0 1 0 3 1 4l-1 1h-1v-1c-1 0-1 0-2 1-1-1-1-2-2-2h0 0l-1-1s-1-2-1-3v-1c-1-2-1-2-2-3v-3l-1 1-1 1v-13c1-1 1-1 1-2 0-2 3-3 4-4h0v-1z" class="I"></path><path d="M379 281c1 1 1 2 0 4 0 0 0 1-1 2h0v4c-1-1-1-2-1-3h-1l-1 3v-3c-1 0-1-1-2-1 0-1 2-3 3-4h0l1 1h0c1-1 2-2 2-3z" class="h"></path><path d="M376 288c0-1 1-2 1-3h1v2 4c-1-1-1-2-1-3h-1z" class="w"></path><defs><linearGradient id="AT" x1="365.617" y1="283.089" x2="363.896" y2="297.335" xlink:href="#B"><stop offset="0" stop-color="#a8a7a9"></stop><stop offset="1" stop-color="#dcdbdc"></stop></linearGradient></defs><path fill="url(#AT)" d="M365 284c0-2 3-3 4-4-3 4-4 12-3 17l-1 1-1 1v-13c1-1 1-1 1-2z"></path><path d="M369 304c0-5-1-9 0-13 0-1 1-4 2-4 1 2-1 4 0 7v4c0 1 0 1 1 1 0 4 1 7 2 10-1 0-1 0-2 1-1-1-1-2-2-2h0 0l-1-1s-1-2-1-3h1z" class="K"></path><path d="M368 304h1l1 4h0 0l-1-1s-1-2-1-3z" class="f"></path><path d="M373 287c1 0 1 1 2 1v3 2 4c1 2 1 5 1 8h-1c0 1 0 3 1 4l-1 1h-1v-1c-1-3-2-6-2-10-1 0-1 0-1-1v-4c0-1 0-2 1-3 0-2 1-3 1-4z" class="d"></path><path d="M375 288v3 2 4h0c-1-2-1-3-1-5h0l1-4z" class="n"></path><path d="M373 287c1 0 1 1 2 1l-1 4h0v-1h-2c0-2 1-3 1-4z" class="i"></path><path d="M372 299l1-3h0v1c0 2 2 5 2 8 0 1 0 3 1 4l-1 1h-1v-1c-1-3-2-6-2-10z" class="a"></path><path d="M436 413c1 1 1 1 1 2l2 3 3 3 1-1h0v-1h1 1 1v1 5 1c1 1 1 2 2 4v3 1c1 5 3 9 5 13v1 1c0 1 1 3 2 5v7c0 1 0 3-1 4v1c-2-3-4-6-6-8v3 4l-1-1v-3c0-2 0-2 1-4-1 0-2-1-2-1v-1c-1-5-2-10-4-13v-3-1-3h0v-1c-1-2-3-5-5-7h0l-4-6h0v-1h-1 0 0l-1 1-1-4h1v-1c1 0 2 1 2 2 1-1 1-1 2-1v-2c0-1 1-1 1-2z" class="S"></path><path d="M443 431l2-2c1 2 2 5 2 7h0l-1-1h0l-3-4z" class="C"></path><path d="M448 433s0-1-1-1c0-2-2-3-3-5-1-1-1-3-2-4-2-2-4-5-6-7l1-1 2 3 3 3 1-1h0v-1h1 1 1v1 5 1c1 1 1 2 2 4v3z" class="i"></path><path d="M443 420v-1h1 1 1v1 5 1l-3-6z" class="Z"></path><path d="M431 417v-1c1 0 2 1 2 2h0l1 1v-1c1 0 1 1 2 1l4 4c2 2 4 4 5 6l-2 2 3 4h0-1v1h0l-1-1-5-8h0l-1-1-1 1h0l-4-6h0v-1h-1 0 0l-1 1-1-4h1z" class="i"></path><path d="M431 417v-1c1 0 2 1 2 2h0l1 1v-1c1 0 1 1 2 1v3c-1 0-1-1-3-1h0v-1h-1 0 0l-1 1-1-4h1z" class="k"></path><path d="M431 417c1 1 1 2 2 3h-1 0 0l-1 1-1-4h1z" class="B"></path><path d="M440 423c2 2 4 4 5 6l-2 2c-1-2-2-3-3-5h-1c-1 0-1-1-1-2l2-1z" class="G"></path><path d="M448 458c1-4 2-10 1-14v-3c1 2 2 5 4 7v1c0 1 1 3 2 5v7c0 1 0 3-1 4v1c-2-3-4-6-6-8z" class="V"></path><path d="M437 427l1-1 1 1h0l5 8 1 1h0c1 3 3 5 3 8 1 4 0 9 0 13-1 0-2-1-2-1v-1c-1-5-2-10-4-13v-3-1-3h0v-1c-1-2-3-5-5-7z" class="C"></path><path d="M437 427l1-1 1 1h0l5 8 1 1-1-1c-1 1-1 1-1 2h0c0 2 1 6 2 8 0 2 0 4 1 6 0 1 1 2 0 4-1-5-2-10-4-13v-3-1-3h0v-1c-1-2-3-5-5-7z" class="e"></path><path d="M361 461c2 4 1 12 1 17v18c0 7 0 15 1 22v7c1 1 0 2 1 3v4c0 1 0 2 1 3v2c0 1 0 1 1 2 0 1-1 1 0 2v3h0c0 1 0 1 1 2v2 2c0 1 0 1 1 1v2c1 2 1 3 1 5 1 0 1 0 1 1v1 1c1 0 1 1 1 1h0v-2s1 1 1 2v1c1 1 2 3 3 4 1 3 3 5 5 7h1c1 1 5 4 5 5 0 2 3 4 4 5 3 2 6 5 10 6 1 0 3 1 3 2l2 1h2c1 1 2 1 3 1 2 1 3 1 5 1 1 0 0 0 1 1h2 1 0c1 0 1 1 2 1h-1c0 1 0 1-1 0h-1l-8-2h-2-2 0l-1 1v1h-1 0c-1 0-3-1-4-1-3-1-7-3-9-5h0 0 1c1 1 3 1 4 2l-8-6v1l-3-2c-2-2-4-4-6-5l-1-1-2-2-3-6c-2-3-3-6-4-9-3-8-5-17-6-25-2-10-3-20-3-30v3h1c1-6 0-13 0-19v-14-17z" class="i"></path><path d="M379 576l-2-2c-2-1-4-4-5-6l-1-3c0-1-1-2-2-3v-2c0-1 0-1-1-2v-1c0-1-1-2-1-4h1c1 2 1 3 1 5 1 0 1 0 1 1v1 1c1 0 1 1 1 1h0v-2s1 1 1 2v1c1 1 2 3 3 4 1 3 3 5 5 7h1c1 1 5 4 5 5 0 2 3 4 4 5 3 2 6 5 10 6 1 0 3 1 3 2l2 1h2c1 1 2 1 3 1 2 1 3 1 5 1 1 0 0 0 1 1h2 1 0c1 0 1 1 2 1h-1c0 1 0 1-1 0h-1l-8-2h-2-2 0l-1 1v1h-1 0c-1 0-3-1-4-1-3-1-7-3-9-5h0 0 1c1 1 3 1 4 2l-8-6c-2-1-4-3-6-5v-1c6 6 13 10 20 13h1c-3-3-7-4-11-6-2-2-5-4-7-6l-3-3-3-3z" class="R"></path><path d="M379 576l-2-2c-2-1-4-4-5-6l-1-3c0-1-1-2-2-3v-2c0-1 0-1-1-2v-1c0-1-1-2-1-4h1c1 2 1 3 1 5 1 0 1 0 1 1v1 1c1 0 1 1 1 1h0v-2s1 1 1 2v1c1 1 2 3 3 4 1 3 3 5 5 7h1c1 1 5 4 5 5 0 2 3 4 4 5 3 2 6 5 10 6 1 0 3 1 3 2h0c-1 0-2 0-3-1h-1c-4-1-7-5-10-7-1-1-2-1-3-2l-4-4c-1-1-2-2-3-2h0z" class="h"></path><path d="M437 485h2l1 1c0 1 1 2 2 3l1-1 3 3 1 1v-1h1c1 0 1 1 1 2 1 0 1 0 1 1 1 1 2 3 2 5l1 2v2c1 1 1 2 2 4l1 2v4c0 1 0 3 1 4l-1 4h0l-1-2-1 1-1 2h-1v-1-1c-1 0-1 0-2-1v3c-1 1-1 3-1 4h-1s-1-1-1-2v1l-1-1c-1 0-1-1-2-1l1-2-1-1h0v-4-2l-2-4v-1-2l-2-2-1 1-3-2c-1-1-2-1-3-2l-1-1h1l1-1 2 2h1c1-2 0-3 0-5 0 0 1 1 2 1h2 0l2 1v-1s0-1-1-1c0-1-1-1-1-2 0-2-1-2-1-4l-1-1-1-2-1-1v-2z" class="b"></path><path d="M442 507v-1c1 0 2 1 3 2h0v1h-3v-2z" class="P"></path><path d="M434 500l2 2h1s2 1 3 1 1 0 1 1h0l-1 1-1 1-3-2c-1-1-2-1-3-2l-1-1h1l1-1z" class="E"></path><path d="M444 514h0l2-2c1 1 0 2 2 3h0 1v1h0v1h-1c-1 0-1 0-1 1s0 0-1 1l-2 1v-4-2z" class="G"></path><path d="M444 516h1 2 2 0v1h-1c-1 0-1 0-1 1s0 0-1 1l-2 1v-4z" class="T"></path><path d="M449 516c1 2 1 4 1 6-1 1-1 3-1 4h-1s-1-1-1-2v1l-1-1c-1 0-1-1-2-1l1-2-1-1h0l2-1c1-1 1 0 1-1s0-1 1-1h1v-1z" class="d"></path><path d="M446 519h2v1c0 1 0 2-1 3v1 1l-1-1c-1 0-1-1-2-1l1-2-1-1h0l2-1z" class="p"></path><path d="M445 521h1v3c-1 0-1-1-2-1l1-2z" class="e"></path><path d="M441 498l2 1v-1s0-1-1-1c0-1-1-1-1-2 0-2-1-2-1-4l3 6s1 1 2 1l2 1h0l1 4v2h0-1l-1 1v-2h-3c0-1 0-2-1-3h-1v1c-1-1-1-1-1-2 1 0 1 0 1-1v-1h0z" class="d"></path><path d="M445 498l2 1h0l1 4v2c-1-1-1-2-2-3s-1-3-1-4z" class="T"></path><path d="M447 499c2 2 3 6 4 8l2 4h0c1-1 1-2 0-2h1c0-2 0-2 1-2l1 2v4c0 1 0 3 1 4l-1 4h0l-1-2-1 1-1 2h-1v-1-1c-1 0-1 0-2-1v3c0-2 0-4-1-6h0v-1c-1-4-1-6-3-9l1-1h1 0v-2l-1-4z" class="K"></path><path d="M450 519c0-1 0-1 1-2v-1h1v4c-1 0-1 0-2-1z" class="R"></path><path d="M447 499c2 2 3 6 4 8h0v6 1c-1-1-1-4-2-5 0-1-1-1-1-2v-2h0v-2l-1-4z" class="G"></path><path d="M451 513v-6h0l2 4h0c1-1 1-2 0-2h1c0-2 0-2 1-2l1 2v4c0 1 0 3 1 4l-1 4h0l-1-2-1 1 1-2-2-1c-1-1 0-2 0-4 0-1 0-1-1-2l-1 2z" class="g"></path><path d="M454 509c0-2 0-2 1-2l1 2v4h0c-1-1 0-1-1-2v1h0v2l-1-5z" class="E"></path><path d="M455 514v-2h0v-1c1 1 0 1 1 2h0c0 1 0 3 1 4l-1 4h0l-1-2-1 1 1-2v-4z" class="r"></path><defs><linearGradient id="AU" x1="445.75" y1="500.523" x2="450.242" y2="497.084" xlink:href="#B"><stop offset="0" stop-color="#c0bebe"></stop><stop offset="1" stop-color="#e3e3e4"></stop></linearGradient></defs><path fill="url(#AU)" d="M437 485h2l1 1c0 1 1 2 2 3l1-1 3 3 1 1v-1h1c1 0 1 1 1 2 1 0 1 0 1 1 1 1 2 3 2 5l1 2v2c1 1 1 2 2 4-1 0-1 0-1 2h-1c1 0 1 1 0 2h0l-2-4c-1-2-2-6-4-8h0l-2-1c-1 0-2-1-2-1l-3-6-1-1-1-2-1-1v-2z"></path><path d="M452 504l1-1c1 1 1 2 2 4-1 0-1 0-1 2h-1l-1-5z" class="o"></path><path d="M445 495c1 1 2 2 2 3v1l-2-1c-1 0-2-1-2-1l1-1 1-1z" class="G"></path><path d="M450 498v-1l2 2 1 2v2l-1 1h0l-2-6z" class="i"></path><path d="M447 492v-1h1c1 0 1 1 1 2 1 0 1 0 1 1 1 1 2 3 2 5l-2-2v1h0l-3-6z" class="T"></path><path d="M437 485h2l1 1c0 1 1 2 2 3 1 2 3 4 3 6l-1 1-1 1-3-6-1-1-1-2-1-1v-2z" class="H"></path><path d="M394 416h0c1 2 1 3 2 4s2 2 2 3h1v-1l-1-1h0c1 0 1-2 1-3v-1h1c0 2 1 4 1 6h0l2 12c0 1 1 2 1 3v3 6c-1 2-1 5-1 7v1h0c-1 1-1 4-1 5-1 0-1-1-1-2 0 0 1-1 1-2h-2l-1 2-1-1h0c-1 0-1 0-1-1s0-3-1-4h0-1v-3c-1 0-1 0-2 1l-1 2h-1v2h0c-1 0-1-1-2-1l-1-1h1v-2-2-1c-1-1 0-2 0-4-1-2-1-3 0-5h0v-2c1-1 1-2 1-2l1-4v-3-1-1-4l2 2v-2h0 0 1c0-1-1-2 0-2v-3z" class="m"></path><path d="M399 444v-4h1l1 1h0c0 1 0 2 1 3l-2 1-1-1z" class="u"></path><path d="M399 444l1 1c0 1 0 2 1 4 0 1 0 3-1 5h0l-1-2v-2c-1-1-1 0-2 0v-1c-1 0-1 0 0-1h2v-3-1z" class="T"></path><defs><linearGradient id="AV" x1="398.525" y1="427.36" x2="402.753" y2="435.679" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#919191"></stop></linearGradient></defs><path fill="url(#AV)" d="M399 417h1c0 2 1 4 1 6h0l2 12c-1 1-2 2-2 4v1h0c0-1 0-1-1-2h0c-1-2-1-3-1-5h1c0-5 0-10-1-15v-1z"></path><path d="M399 417h1c0 2 1 4 1 6v3h-1v12h0c-1-2-1-3-1-5h1c0-5 0-10-1-15v-1z" class="K"></path><path d="M394 416h0c1 2 1 3 2 4s2 2 2 3h1v-1l-1-1h0c1 0 1-2 1-3 1 5 1 10 1 15h-1c0-1 0-3-1-4v3l-1-1c0-1-1-3-2-4 0-3-1-6-1-8v-3z" class="M"></path><path d="M394 441c2-2 0-4 1-6h1c1 1 1 2 1 2 1 1 1 1 1 2v6h1v3h-2c-1 1-1 1 0 1v1c1 0 1-1 2 0v2l-1 1v3h-1c0-1 0-3-1-4h0-1v-3-5l-1-3z" class="d"></path><path d="M398 439v6c0 1 0 2-1 2h-1v-1c0-2 1-5 2-7z" class="T"></path><path d="M393 421h0 0 1c0-1-1-2 0-2 0 2 1 5 1 8 1 1 2 3 2 4l1 1v3h0c0 2 0 2-1 2 0 0 0-1-1-2h-1c-1 2 1 4-1 6l1 3v5c-1 0-1 0-2 1l-1 2h-1v2h0c-1 0-1-1-2-1l-1-1h1v-2-2-1c-1-1 0-2 0-4-1-2-1-3 0-5h0v-2c1-1 1-2 1-2l1-4v-3-1-1-4l2 2v-2z" class="n"></path><path d="M391 426l1-1c0 2 0 4 1 6v1h1 0-2v1l-1 1v5l-2-1v-2c1-1 1-2 1-2l1-4v-3-1z" class="p"></path><defs><linearGradient id="AW" x1="393.988" y1="422.62" x2="394.188" y2="428.514" xlink:href="#B"><stop offset="0" stop-color="#4e4e50"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#AW)" d="M393 421h0 0 1c0-1-1-2 0-2 0 2 1 5 1 8 1 1 2 3 2 4h-1l-1-1h0v2c0 1-1 1-1 1-1 0-1 0 0-1h0-1v-1c-1-2-1-4-1-6l-1 1v-1-4l2 2v-2z"></path><path d="M391 426v-1-4l2 2c1 2 0 5 1 7l-1 1c-1-2-1-4-1-6l-1 1z" class="U"></path><path d="M391 449c-1-4 0-7 1-11v-5h1c0 2 1 4 1 6v2l1 3v5c-1 0-1 0-2 1l-1 2h-1v-3z" class="g"></path><path d="M392 444c1-1 1-4 2-5v2l1 3h-3z" class="O"></path><path d="M392 444h3v5c-1 0-1 0-2 1l-1 2h-1v-3l1-5z" class="L"></path><path d="M378 580l1 1c2 1 4 3 6 5l3 2v-1l8 6c-1-1-3-1-4-2h-1 0 0c2 2 6 4 9 5 1 0 3 1 4 1h0 1v-1l1-1 11 3c1 0 3 1 4 1h3 0v-1c1 0 2 1 2 1 1 1 2 0 3 1h1v1c2 1 6-1 8 1-2 1-7 0-9 0v1h9 5 4c3 0 15-1 16 1h1c1 1 1 1 0 1v1c-1-1-1-1-2-1v1h-9c2 1 8 1 11 1v1h0c-2-1-6 0-8 0h-5c-3 1-7 1-10 0h-5-4c-1 0-4 0-5-1-4-1-8-1-11-2-5 0-9-2-13-4-2-1-4-1-6-2-1-1-3-2-5-3v-3c-1 0-3-1-4-2-3-2-9-7-10-11z" class="a"></path><path d="M378 580l1 1c2 1 4 3 6 5l3 2v-1l8 6c-1-1-3-1-4-2h-1 0 0c2 2 6 4 9 5 1 0 3 1 4 1l9 3c2 1 4 2 7 3 2 0 4 0 6 1 2 0 4 1 5 1 7 1 15 0 22 0 3 0 7-1 9 1h-9c-7 0-14 1-21 0-8-1-15-3-22-5-6-2-12-4-18-8-1 0-3-1-4-2-3-2-9-7-10-11z" class="K"></path><path d="M405 597v-1l1-1 11 3c1 0 3 1 4 1h3 0v-1c1 0 2 1 2 1 1 1 2 0 3 1h1v1c2 1 6-1 8 1-2 1-7 0-9 0v1h9 5 4c3 0 15-1 16 1h1c1 1 1 1 0 1v1c-1-1-1-1-2-1v1c-2-2-6-1-9-1-7 0-15 1-22 0-1 0-3-1-5-1-2-1-4-1-6-1-3-1-5-2-7-3l-9-3h0 1z" class="v"></path><path d="M405 597v-1l1-1 11 3v2 1c-4-2-8-3-12-4z" class="T"></path><path d="M417 598c1 0 3 1 4 1h3 0c1 1 2 1 2 3-1 1-1 0-2 0-3 0-5 0-7-1v-1-2z" class="e"></path><path d="M424 599c1 1 2 1 2 3-1 1-1 0-2 0-1-1-1-2-2-2h0v-1h2 0 0z" class="G"></path><defs><linearGradient id="AX" x1="462.487" y1="501.981" x2="454.248" y2="469.717" xlink:href="#B"><stop offset="0" stop-color="#c9c9c9"></stop><stop offset="1" stop-color="#f1f0f0"></stop></linearGradient></defs><path fill="url(#AX)" d="M448 461v-3c2 2 4 5 6 8l1 1c1 2 2 3 3 4 1 2 4 6 4 8l-1 1 3 6 5 11c1 3 1 6 1 9l1 6v5c-1 0-1-1-2-2 0 1-1 2-1 3h0v-1l-1 1v1l-2 3h-1l1-4c0-1-1-2-1-3h-1v2h0l-1 3-1 1v1c-1-1-1-2-2-2l-1-2-1 3h-1l1-4c-1-1-1-3-1-4v-4l-1-2c-1-2-1-3-2-4v-2l-1-2c0-2-1-4-2-5 0-1 0-1-1-1 0-1 0-2-1-2h-1v1l-1-1-2-3v-1c0-1 0-2-1-3 0-2-1-3-2-5 1-1 1-1 1-2l1-1v-1h0c0-1 0-1 1-1v-3h0v-3l1 1h0l1-1c0-1 0-2 1-4l1 1v-4z"></path><path d="M459 483l2 3 3 4h-3v1c-1-3-2-5-2-8z" class="b"></path><path d="M461 491v-1h3v1c-1 1-2 2-2 3-1 1 0 4 0 6h0c0 1 1 3 1 5h-1c-1-1-2-4-2-5v-2c0 1 1 3 1 4h0l1-1h-1l1-1c-1-2-1-6-1-9z" class="G"></path><path d="M462 500h0c0-2-1-5 0-6 0-1 1-2 2-3l1 2 3 8c-1-1-2-2-2-3h0v-1 1l-2 2-1 1v4h0c0-2-1-4-1-5z" class="m"></path><path d="M462 500h0c0-2-1-5 0-6 0-1 1-2 2-3l1 2h-1c1 2 1 3 1 4-1 0-1-1-2-1-1 2 0 2-1 4z" class="g"></path><path d="M459 483v-1c-1-3-2-5-2-9h1c1 2 2 4 3 7l3 6v3l-3-3-2-3z" class="Z"></path><path d="M448 461v-3c2 2 4 5 6 8l1 1c1 2 2 3 3 4 1 2 4 6 4 8l-1 1c-1-3-2-5-3-7s-3-4-4-5c0 3-1 7 0 11h-1v-4l1-8-5-7-1 1z" class="j"></path><defs><linearGradient id="AY" x1="470.494" y1="504.374" x2="463.673" y2="491.948" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#c8c7c9"></stop></linearGradient></defs><path fill="url(#AY)" d="M464 486l5 11c1 3 1 6 1 9v1c-1 0-1-1-1-1h0l-1-5-3-8-1-2v-1l-3-4 3 3v-3z"></path><path d="M448 461l1-1 5 7-1 8c-1 1-1 0-1 1-1-1-2-2-2-3v4l-1-1v1h0c-2-2-3-3-4-5h-1v2h0v-3h0v-3l1 1h0l1-1c0-1 0-2 1-4l1 1v-4z" class="V"></path><path d="M447 464l1 1c-1 2-1 4 1 7l1 1v4l-1-1v1h0c-2-2-3-3-4-5h-1v2h0v-3h0v-3l1 1h0l1-1c0-1 0-2 1-4z" class="H"></path><path d="M450 473c0 1 1 2 2 3 0-1 0 0 1-1v4h1c2 4 3 9 5 14 0 2 1 4 1 5l-1 1c0 2 2 4 1 6h-1v-2h-1v2l-1 1v-1-3l-1-1v-1l-1-2c0-1-1-3-2-4v-1h1l1-1c-2-5-3-10-6-15v-1l1 1v-4z" class="l"></path><path d="M450 473c0 1 1 2 2 3 0-1 0 0 1-1v4 2h0c-2-2-2-3-3-4v-4z" class="L"></path><path d="M455 492c2 1 3 5 3 7-1 1-1 1-2 1l-1-2c0-1-1-3-2-4v-1h1l1-1z" class="H"></path><path d="M464 500l2-2v-1 1h0c0 1 1 2 2 3l1 5h0s0 1 1 1v-1l1 6v5c-1 0-1-1-2-2 0 1-1 2-1 3h0v-1l-1 1v1l-2 3h-1l1-4c0-1-1-2-1-3h-1v2h0v-3-9-4l1-1z" class="R"></path><path d="M463 514c0-1 1-2 2-2h0v6c0-1-1-2-1-3h-1v2h0v-3z" class="c"></path><path d="M464 500l2-2v-1 1h0c0 1 1 2 2 3l1 5-1 2c-1 2-1 5-1 7-1 0-1-1-1-1v-6c0-1 0-2-1-3 0-2 0-3-1-5z" class="K"></path><path d="M470 506l1 6v5c-1 0-1-1-2-2 0 1-1 2-1 3h0v-1l-1 1c-1-1 0-2 0-3 0-2 0-5 1-7l1-2h0s0 1 1 1v-1z" class="n"></path><path d="M468 508c1 3 2 5 1 7 0 1-1 2-1 3h0v-1l-1 1c-1-1 0-2 0-3 0-2 0-5 1-7z" class="Q"></path><path d="M450 494l1-2 2 2h0c1 1 2 3 2 4l1 2v1l1 1v3 1l1-1v-2h1v2h1c1-2-1-4-1-6l1-1v2c0 1 1 4 2 5h1 0v9 3l-1 3-1 1v1c-1-1-1-2-2-2l-1-2-1 3h-1l1-4c-1-1-1-3-1-4v-4l-1-2c-1-2-1-3-2-4v-2l-1-2c0-2-1-4-2-5z" class="g"></path><path d="M457 507h0c2 1 2 4 2 5h-1v1h0c0-2-1-4-1-6z" class="K"></path><path d="M456 507h1c0 2 1 4 1 6l-1 4c-1-1-1-3-1-4v-4-2z" class="n"></path><path d="M453 501l2 1h0c1 1 1 2 1 4v1 2l-1-2c-1-2-1-3-2-4v-2z" class="G"></path><path d="M450 494l1-2 2 2v2c1 2 2 4 2 6h0l-2-1-1-2c0-2-1-4-2-5z" class="b"></path><path d="M459 512v2l1-1 1-4c1 3 0 5-1 7v1h1 2l-1 3-1 1v1c-1-1-1-2-2-2l-1-2-1 3h-1l1-4 1-4h0v-1h1z" class="o"></path><path d="M460 516v1h1 2l-1 3-1 1c-1-1-1-2-1-3-1-1 0-1 0-2z" class="i"></path><path d="M458 513c1 1 0 3 0 5h0l-1 3h-1l1-4 1-4h0z" class="J"></path><path d="M444 474v-2h1c1 2 2 3 4 5h0c3 5 4 10 6 15l-1 1h-1v1h0l-2-2-1 2c0-1 0-1-1-1 0-1 0-2-1-2h-1v1l-1-1-2-3v-1c0-1 0-2-1-3 0-2-1-3-2-5 1-1 1-1 1-2l1-1v-1h0c0-1 0-1 1-1h0z" class="j"></path><path d="M443 475c1 1 1 2 1 3l-1 1 3 6h-1c-1 0-1 0-2-1 0-2-1-3-2-5 1-1 1-1 1-2l1-1v-1z" class="L"></path><path d="M444 474v-2h1c1 2 2 3 4 5h0c3 5 4 10 6 15l-1 1h-1c0-2-1-4-2-5-2-5-5-9-7-14z" class="F"></path><path d="M406 262l2-4 1 1h0c-1 1-1 1-1 2 1 1 4 1 5 0l9-1c2-1 4-1 6-1v1l-15 2c-3 1-10 2-12 4v1 1h3l1 1h1v1c1 0 2-1 3-1 0 1-2 3-3 4s-3 2-4 4c-2 2-1 4-3 6v2s0 1-1 1v2l1 1c0 1 0 3-1 4v1 1 1l1 1c0 3 0 6 1 9v1h0v-1c2 2 3 6 4 9 1 2 3 4 4 6l-1 1v3l-1-1c0 2 2 3 2 5h0v1l1-1h0 0l1 1h0c0 1 0 1 1 2 0 1 2 3 3 4v1l4 3v1c-1 1-3 0-4-1l1 3h-1 0-2l-2-1c0-2-2-3-4-5h2c-4-4-7-6-9-11h-1l-3-6h0v1l-2-2-1-1-1-1c-1 0-1 1-1 1l-2-3h0c0-1-2-2-3-4l-1-1h0-1c-1-2-1-4-1-6v7c0 1 5 8 4 9-4-4-8-9-10-15 0-3 0-6-1-8v-4-2l1-3h1c0 1 0 2 1 3v-4h0c1-1 1-2 1-2 1-2 1-3 0-4l1-2h0c-1-1 5-6 6-7 0 0 2-1 3-2v-2l10-3c2-1 5-1 7-3z" class="W"></path><path d="M387 282l1-1h2c0 1-1 2-1 2l-1 1-1-2z" class="M"></path><path d="M393 275c1 0 1-1 1-1 1-1 1-2 1-3h2s0 1-1 2h1v1c-1 1-2 1-4 1z" class="f"></path><path d="M393 275c2 0 3 0 4-1l-2 4c-1 0-2 0-3-1 0 0 1-1 1-2z" class="B"></path><path d="M392 277c1 1 2 1 3 1-1 1-1 2-1 4h-1 0-1l1 1c-1 1-1 3-2 4v-3l-1 1-1-2s1-1 1-2h0c1-1 1-2 2-4z" class="S"></path><path d="M390 281l1 1v2l-1 1-1-2s1-1 1-2z" class="c"></path><path d="M384 279c0-1 1-2 2-3h1l1 1c-1 3-4 5-5 8h0c0 2 0 4-1 6v10c1 1 1 2 1 4v3l1-1v3h0-1c-1-2-1-4-1-6v7c0 1 5 8 4 9-4-4-8-9-10-15 0-3 0-6-1-8v-4-2l1-3h1c0 1 0 2 1 3v-4h0c1-1 1-2 1-2 1-2 1-3 0-4l1-2c0 1 1 1 1 1h1c1 0 1-1 2-1z" class="f"></path><path d="M381 286c0-2 0-2 1-3h1c-1 3-1 5-1 7 0-1 0-1-1-2h-1l1-2z" class="Q"></path><path d="M380 288h1c1 1 1 1 1 2l-1 10v-8h-1v-4z" class="Y"></path><path d="M384 279v-1l1 1-1 3-1 1h-1c-1 1-1 1-1 3h-1c0-1 0-1-1-1 1-2 1-3 0-4l1-2c0 1 1 1 1 1h1c1 0 1-1 2-1z" class="B"></path><path d="M378 287c1-1 1-2 1-2 1 0 1 0 1 1h1l-1 2v4h1v8l1 1v3 7l-5-17 1-3v-4h0z" class="Q"></path><path d="M380 292h1v8l1 1-1 2h0c-1-3-1-6-1-9v-2z" class="p"></path><path d="M378 287c1-1 1-2 1-2 1 0 1 0 1 1h1l-1 2v4 2 2h-1v-9h-1z" class="S"></path><defs><linearGradient id="AZ" x1="382.371" y1="301.989" x2="376.601" y2="304.99" xlink:href="#B"><stop offset="0" stop-color="#9a9a9a"></stop><stop offset="1" stop-color="#c7c5c7"></stop></linearGradient></defs><path fill="url(#AZ)" d="M376 288h1c0 1 0 2 1 3l-1 3 5 17c0 1 5 8 4 9-4-4-8-9-10-15 0-3 0-6-1-8v-4-2l1-3z"></path><path d="M376 288h1c0 1 0 2 1 3l-1 3v-2-1h-1v2h-1v-2l1-3z" class="d"></path><path d="M387 282l1 2 1-1 1 2c-1 1-1 2-1 3 1 1 1 1 1 2l-1 4c1 1 1 1 1 2h1 0c0 4 0 8 1 12l3 12v1l-2-2-1-1-1-1c-1 0-1 1-1 1l-2-3h0c0-1-2-2-3-4l-1-1v-3c0-5-1-10 0-14 0-2 1-5 1-7 1-1 1-2 2-4z" class="D"></path><path d="M389 288c1 1 1 1 1 2l-1 4v7l1 5-1 3h0c-1-3-2-7-2-10 0-2 1-5 1-7 0-1 1-3 1-4z" class="l"></path><path d="M389 288c1 1 1 1 1 2l-1 4v7c-1-2-1-5-1-7v4l-1 1c0-2 1-5 1-7 0-1 1-3 1-4z" class="g"></path><path d="M384 307c0-5-1-10 0-14 0 1 0 6 1 6v-3c0 4 0 12 2 16 1-1 1-1 2-1 0 1 1 2 0 3 0 1-1 1-1 1h0c0-1-2-2-3-4l-1-1v-3z" class="Q"></path><path d="M387 282l1 2 1-1 1 2c-1 1-1 2-1 3s-1 3-1 4l-1-1h0-1l-1 5v3c-1 0-1-5-1-6 0-2 1-5 1-7 1-1 1-2 2-4z" class="E"></path><path d="M387 282l1 2 1-1 1 2c-1 1-1 2-1 3s-1 3-1 4l-1-1h0-1l2-6h0c-1 0-1 0-2 1h-1c1-1 1-2 2-4z" class="U"></path><defs><linearGradient id="Aa" x1="390.354" y1="307.541" x2="391.936" y2="307.282" xlink:href="#B"><stop offset="0" stop-color="#2c2a2c"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#Aa)" d="M389 294c1 1 1 1 1 2h1 0c0 4 0 8 1 12l3 12v1l-2-2-1-1-1-1c-1 0-1 1-1 1l-2-3s1 0 1-1c1-1 0-2 0-3v-2h0l1-3-1-5v-7z"></path><path d="M390 306c0 4 1 8 2 12l-1-1c-1 0-1 1-1 1l-2-3s1 0 1-1c1-1 0-2 0-3v-2h0l1-3z" class="d"></path><path d="M404 268l1 1h1v1c1 0 2-1 3-1 0 1-2 3-3 4s-3 2-4 4c-2 2-1 4-3 6v2s0 1-1 1v2l1 1c0 1 0 3-1 4v1 1 1c-1 3-1 7-1 10v3 2c-1 0-1-1-1-1v4c-2-2-3-4-4-6-1-4-1-8-1-12h0-1c0-1 0-1-1-2l1-4c0-1 0-1-1-2 0-1 0-2 1-3l1-1v3c1-1 1-3 2-4l-1-1h1 0 1c0-2 0-3 1-4l2-4v-1h1c1-1 2-2 3-2l3-3z" class="i"></path><path d="M396 310l-1-2c-1-3-3-11-1-14h1v3c1 3 1 7 2 9v3 2c-1 0-1-1-1-1z" class="O"></path><path d="M395 287h0l1-2h0v6 1c1-1 2-3 2-4l1 1c0 1 0 3-1 4v1 1 1c-1 3-1 7-1 10-1-2-1-6-2-9v-5c1-2 1-3 0-5z" class="B"></path><path d="M393 283h1v1c0 2-1 4-1 6h0c1-1 1-2 2-3 1 2 1 3 0 5l-1-1c-1 0-2 1-2 2l-1 1v2h0-1c0-1 0-1-1-2l1-4c0-1 0-1-1-2 0-1 0-2 1-3l1-1v3c1-1 1-3 2-4z" class="Q"></path><path d="M390 290c1 1 1 2 1 4v2h0-1c0-1 0-1-1-2l1-4z" class="J"></path><path d="M398 273h1l1 1v1c-2 3-3 7-4 10h0 0l-1 2h0c-1 1-1 2-2 3h0c0-2 1-4 1-6v-1h-1l-1-1h1 0 1c0-2 0-3 1-4l2-4v-1h1z" class="f"></path><path d="M398 273h1c-2 4-3 7-5 11v-1h-1l-1-1h1 0 1c0-2 0-3 1-4l2-4v-1h1z" class="J"></path><path d="M404 268l1 1h1v1c1 0 2-1 3-1 0 1-2 3-3 4s-3 2-4 4c-2 2-1 4-3 6v2s0 1-1 1v2c0 1-1 3-2 4v-1-6h0c1-3 2-7 4-10v-1l-1-1h-1c1-1 2-2 3-2l3-3z" class="v"></path><path d="M404 268l1 1h1v1c0 1 0 1-1 1-2 1-3 3-5 4v-1l-1-1h-1c1-1 2-2 3-2l3-3z" class="I"></path><path d="M398 296l1 1c0 3 0 6 1 9v1h0v-1c2 2 3 6 4 9 1 2 3 4 4 6l-1 1v3l-1-1c0 2 2 3 2 5h0v1l1-1h0 0l1 1h0c0 1 0 1 1 2 0 1 2 3 3 4v1l4 3v1c-1 1-3 0-4-1l1 3h-1 0-2l-2-1c0-2-2-3-4-5h2c-4-4-7-6-9-11h-1l-3-6h0l-3-12c1 2 2 4 4 6v-4s0 1 1 1v-2-3c0-3 0-7 1-10z" class="f"></path><path d="M397 309c1 5 3 10 5 14h0c-1 0-2-1-2-1-1-2-1-2-2-3l-1-2-1-3v-4s0 1 1 1v-2z" class="e"></path><path d="M400 307v-1c2 2 3 6 4 9 1 2 3 4 4 6l-1 1v3l-1-1c-3-6-4-11-6-17z" class="l"></path><path d="M392 308c1 2 2 4 4 6l1 3 1 2c1 1 1 1 2 3 0 0 1 1 2 1h0l2 3c1 1 1 3 1 4-1-1-2-2-3-4 0 1-1 1-1 2-1-1-2-1-2-2h-1l-3-6h0l-3-12z" class="K"></path><path d="M395 320v-2l1-1c1 1 2 3 3 5 0 0 1 1 1 2-1 1-1 0-2 2l-3-6z" class="T"></path><path d="M400 322s1 1 2 1h0l2 3c1 1 1 3 1 4-1-1-2-2-3-4 0 1-1 1-1 2-1-1-2-1-2-2h-1c1-2 1-1 2-2 0-1-1-2-1-2h1z" class="w"></path><path d="M398 326c1-2 1-1 2-2 1 0 2 2 2 2 0 1-1 1-1 2-1-1-2-1-2-2h-1z" class="b"></path><path d="M399 326c0 1 1 1 2 2 0-1 1-1 1-2 1 2 2 3 3 4 0-1 0-3-1-4l4 3v1l1-1h0 0l1 1h0c0 1 0 1 1 2 0 1 2 3 3 4v1l4 3v1c-1 1-3 0-4-1l1 3h-1 0-2l-2-1c0-2-2-3-4-5h2c-4-4-7-6-9-11z" class="C"></path><path d="M404 326l4 3v1l1-1h0 0l1 1h0c0 1 0 1 1 2 0 1 2 3 3 4v1c-4-2-6-5-9-7 0-1 0-3-1-4z" class="E"></path><path d="M399 326c0 1 1 1 2 2 3 4 6 7 10 10 1 1 2 2 3 2l1 3h-1 0-2l-2-1c0-2-2-3-4-5h2c-4-4-7-6-9-11z" class="T"></path><path d="M406 337h2l4 4 2 2h0-2l-2-1c0-2-2-3-4-5z" class="R"></path><path d="M452 414c4 5 9 11 12 17v1c1 2 2 4 4 5h1c1 3 1 6 3 8 0 1 3 4 3 6 1 2 3 4 3 7h-1v2l2 7c0 1 0 1 1 2h0v3h-1c0 2 1 5 1 6l2 9h-3c0-1-1-1-1-1h-1 0l-1 1 1 1-1 3h0-1v4h-2v-1h-1v1l-1 1v-1 1l-1-2h-1v3l-5-11-3-6 1-1c0-2-3-6-4-8-1-1-2-2-3-4l-1-1v-1c1-1 1-3 1-4v-7c-1-2-2-4-2-5v-1-1c-2-4-4-8-5-13v-1-3c-1-2-1-3-2-4v-1-5c1 1 1 0 1 0v-2-1c1 0 2-1 3-1l2-2z" class="s"></path><path d="M455 467c1 0 2-1 2-2 0 2 1 4 1 6-1-1-2-2-3-4z" class="X"></path><path d="M469 451c0 2 1 5 0 7l-1 4v-7-3h1v-1z" class="F"></path><path d="M468 476l-2-9c2 1 3 3 4 5-1 1-1 3-2 4z" class="V"></path><path d="M474 451h1c1 2 3 4 3 7h-1v2c0-3-1-6-3-9zm-17 6v8c0 1-1 2-2 2l-1-1v-1c1-1 1-3 1-4 2-2 1-2 2-4z" class="C"></path><path d="M446 420c1 1 1 0 1 0v-2-1l1 3h0l2 2v1l-1-1-1 1v5 2c-1-2-1-3-2-4v-1-5z" class="G"></path><path d="M469 479c0-1 0-1 1-2h1l1 4v-1s0-1 1-2l1 3c-1 0-1 1-2 2v2h-1v2l-1-4-1-4z" class="j"></path><path d="M470 483l1-2h0l1 2v2h-1v2l-1-4z" class="G"></path><path d="M470 472c0 1 1 2 1 2 1 1 2 3 2 4-1 1-1 2-1 2v1l-1-4h-1c-1 1-1 1-1 2l-1-3c1-1 1-3 2-4z" class="P"></path><path d="M453 447c1 1 2 3 2 4 1 2 1 4 2 6-1 2 0 2-2 4v-7c-1-2-2-4-2-5v-1-1z" class="H"></path><path d="M472 483c1-1 1-2 2-2v2l1 3v1h1l1 1-1 3h0-1v-1c-1-2-2-3-3-5v-2z" class="O"></path><path d="M475 486v1h1l1 1-1 3h0-1v-1-4z" class="b"></path><path d="M462 433h1l3 3c1 3 1 6 2 9l1 6v1h-1c-1-6-3-13-6-19z" class="j"></path><path d="M463 481c1 1 2 3 3 3 0-2-3-9-3-9h1v2c2 3 4 7 5 10 0 1 0 2 1 2v1h-2 0c-1-1-2-1-2-2l-3-7z" class="q"></path><path d="M466 488h1 0c1-1 1-1 1-2v-1c0 1 0 1 1 2 0 1 0 2 1 2v1h-2 0c-1-1-2-1-2-2z" class="G"></path><path d="M462 479c1 0 1 1 1 2l3 7c0 1 1 1 2 2h0 2l1 5v1l-1-2h-1v3l-5-11-3-6 1-1z" class="e"></path><path d="M450 416c1 2 4 3 5 5l8 11v1h-1c-1-1-12-14-12-15v-1c-1 0-1 0-2 1v2h0 0l-1-3c1 0 2-1 3-1z" class="C"></path><path d="M471 487v-2h1c1 2 2 3 3 5v1 4h-2v-1h-1v1l-1 1v-1l-1-5v-1s0 1 1 1v1-1h0v-2-1z" class="u"></path><path d="M471 488l1 6v1l-1 1v-1l-1-5v-1s0 1 1 1v1-1h0v-2z" class="C"></path><path d="M474 483l2 2v1-2-2c0-5 0-12-2-18v-2l4 6h0l1-1c0 1 0 1 1 2h0v3h-1c-1-2-1-3-2-5 0 3 1 5 1 7s-1 3-1 4v6 2l-1 1h-1v-1l-1-3z" class="F"></path><path d="M464 431v1c1 2 2 4 4 5h1c1 3 1 6 3 8 0 1 3 4 3 6h-1c-2-2-3-5-5-7l-1 1c-1-3-1-6-2-9l-3-3v-1l1-1z" class="X"></path><path d="M452 414c4 5 9 11 12 17l-1 1-8-11c-1-2-4-3-5-5l2-2z" class="Z"></path><path d="M448 428h2c6 8 12 15 13 25 0 2 1 4 1 5h-1 0c-1-3-1-7-2-10-1-5-4-8-6-12-2-3-3-5-6-7h0v1c0 2-1 3-1 4v-1-3-2z" class="P"></path><path d="M477 484v-6c0-1 1-2 1-4s-1-4-1-7c1 2 1 3 2 5 0 2 1 5 1 6l2 9h-3c0-1-1-1-1-1h-1 0v-2z" class="O"></path><path d="M477 484c0-1 0-2 1-3l1 1c-1 1-1 2-1 3l-1 1h0v-2z" class="G"></path><path d="M479 482v-4h1l2 9h-3c0-1-1-1-1-1h-1l1-1c0-1 0-2 1-3z" class="T"></path><path d="M403 335l3 2c2 2 4 3 4 5l2 1h2 0 1l-1-3c1 1 3 2 4 1v-1l1 1v1h-1 0v1l7 7c0 1 1 1 1 1l8 9c1 2 2 3 3 5 2 2 4 4 5 7h2c1 1 2 3 2 4h0l3 4 1 3c-1 0-1 0-1 1l-1-1v3c1 2 0 4 1 6v4l-1 1v1 1h0l3 4-1 1 1 1h-1v2l3 2h-1v1l1 1 1 1-2 2-2 2c-1 0-2 1-3 1v1 2s0 1-1 0v-1h-1-1-1v1h0l-1 1-3-3h1c0-2-1-2-1-3v-1c-1-2-1-3-1-4s-2-3-3-4v-1l-2-2-4-8s-1-2-1-3l-1 1-1-1h-1v-3l-3-13c-1-3-2-7-3-10-1-4-4-9-6-12-2-5-6-9-10-13l2-1-2-2s0-2-1-2l1-1z" class="W"></path><path d="M403 335l3 2c2 2 4 3 4 5l2 1c7 8 14 15 19 24 0 0 0 1-1 1-3-5-6-10-10-14 3 5 5 10 8 16 1 3 1 7 2 10l-1 1c0-4-2-7-3-11-5-12-11-21-21-30l-2-2s0-2-1-2l1-1z" class="T"></path><path d="M403 335l3 2c2 2 4 3 4 5-2-1-4-4-7-4 0 0 0-2-1-2l1-1z" class="w"></path><path d="M430 380c-1-3-1-7-2-10-3-6-5-11-8-16 4 4 7 9 10 14l10 20-1 1h-1l-3-3c0-1-1-1-1-2h-1c1 1 1 2 1 2l-1 1c-2-1-1-2-2-4l-1-3z" class="v"></path><path d="M430 380l1 3c1 2 0 3 2 4l1-1s0-1-1-2h1c0 1 1 1 1 2l3 3h0c1 2 2 3 4 4h0s-1 1-2 0v2h-1 0-2-1l-1-1-1 1c1 1 1 3 2 5v3 1h0-1v1l-2-2-4-8s-1-2-1-3l-1 1-1-1h-1v-3c1 1 1 1 2 0v-1c1-1 1-2 0-4h0v-1c1 1 2 3 3 4 1-1-1-4-1-6h0l1-1z" class="D"></path><path d="M435 386l3 3h0c1 2 2 3 4 4h0s-1 1-2 0v2h-1 0-2-1l-1-1-1 1c1 1 1 3 2 5-1-1-2-3-2-5l-3-6h0c1 0 2 2 3 3s1 2 3 2v-1c-2-1-3-3-3-5h1l-1-2h1z" class="r"></path><path d="M438 389c1 2 2 3 4 4h0s-1 1-2 0v2h-1c-1-2-1-4-2-5l1-1z" class="e"></path><path d="M428 391c0-1 1-2 1-3l2 1 3 6c0 2 1 4 2 5v3 1h0-1v1l-2-2-4-8s-1-2-1-3v-1z" class="M"></path><path d="M428 391c1 1 2 2 2 3h-1v1s-1-2-1-3v-1z" class="R"></path><path d="M429 395v-1h1l5 10v1l-2-2-4-8z" class="p"></path><path d="M442 393h1c1 1 3 2 4 4h0 1v1 1h0l3 4-1 1 1 1h-1v2l3 2h-1v1l1 1 1 1-2 2-2 2c-1 0-2 1-3 1v1 2s0 1-1 0v-1h-1-1-1v1h0l-1 1-3-3h1c0-2-1-2-1-3v-1c-1-2-1-3-1-4s-2-3-3-4v-1-1h1 0v-1-3c-1-2-1-4-2-5l1-1 1 1h1 2 0 1v-2c1 1 2 0 2 0h0z" class="g"></path><path d="M448 411l1-1h3l1 1-3 3h0l-2-3z" class="H"></path><path d="M439 395h1c1 1 1 2 2 3h0c-1 0-1-1-2-1s-2 1-2 1c-1-1-1-2-2-3h1 2 0z" class="w"></path><path d="M436 403l7 17-1 1-3-3h1c0-2-1-2-1-3v-1c-1-2-1-3-1-4s-2-3-3-4v-1-1h1 0v-1z" class="D"></path><path d="M442 393h1c1 1 3 2 4 4l-1 2c0 1 0 2-1 2l-3-3c-1-1-1-2-2-3v-2c1 1 2 0 2 0h0z" class="l"></path><path d="M443 393c1 1 3 2 4 4l-1 2-2-1c-1-1-2-3-2-4l1-1z" class="H"></path><path d="M438 402v-1c2 1 6 10 7 13h0c0 1 1 2 0 4h-1c-2-1-3-3-3-5-1-1-1-2-1-3s-1-1-1-2c0-2-1-4-1-6z" class="V"></path><path d="M447 397h0 1v1 1h0l3 4-1 1 1 1h-1v2l3 2h-1v1h-3l-1 1 2 3-2 2h-1v-1l-2-2v1h0c-1-3-5-12-7-13v1c-1-1-1-2-1-2l1-1v1c1-1 1-1 1-2h1c2 1 3 3 4 4l1-1c1 0 1-1 1-2l1-2z" class="T"></path><path d="M448 399l3 4-1 1 1 1h-1c-1-1-2-2-2-4v-2z" class="C"></path><path d="M447 397h0 1v1c-1 2 0 4-2 5-1 0-1 0-2-1l1-1c1 0 1-1 1-2l1-2z" class="m"></path><path d="M447 415c-1-2-2-6-3-7s-1-1-1-2l1-1h1l3 3c1 0 2-1 2-1l3 2h-1v1h-3l-1 1 2 3-2 2h-1v-1z" class="q"></path><path d="M448 416l-1-1v-5l1 1 2 3-2 2z" class="N"></path><path d="M418 340l1 1v1h-1 0v1l7 7c0 1 1 1 1 1l8 9c1 2 2 3 3 5 2 2 4 4 5 7h2c1 1 2 3 2 4h0l3 4 1 3c-1 0-1 0-1 1l-1-1v3c1 2 0 4 1 6v4l-1 1h-1 0c-1-2-3-3-4-4h-1c-2-1-3-2-4-4h0 1l1-1-10-20c1 0 1-1 1-1-5-9-12-16-19-24h2 0 1l-1-3c1 1 3 2 4 1v-1z" class="g"></path><path d="M442 372h2c1 1 2 3 2 4h0l3 4 1 3c-1 0-1 0-1 1l-1-1v3c-1 1-1 2-1 4 0-2 0-4-1-6 0-5-2-9-4-12z" class="Y"></path><path d="M446 376l3 4 1 3c-1 0-1 0-1 1l-1-1c-1-2-1-4-2-6v-1z" class="b"></path><path d="M432 363c1 2 3 3 4 4h0c5 6 7 11 9 18 0 1 0 1-1 1l-3-2c-1-2-1-2-3-2l-1-4h1c0-1 0-2-1-3-1-4-3-7-5-11v-1z" class="B"></path><path d="M431 367c2 3 4 7 6 11l1 4c2 0 2 0 3 2l3 2c1 0 1 0 1-1l1 6h1v-1c0-2 0-3 1-4 1 2 0 4 1 6v4l-1 1h-1 0c-1-2-3-3-4-4h-1c-2-1-3-2-4-4h0 1l1-1-10-20c1 0 1-1 1-1z" class="K"></path><path d="M438 382c2 0 2 0 3 2 1 1 2 2 2 4 0-1-1-1-2-2v1l-1-1c-1-1-2-3-2-4z" class="r"></path><path d="M441 384l3 2c1 0 1 0 1-1l1 6h0l-3-3c0-2-1-3-2-4z" class="J"></path><path d="M448 386c1 2 0 4 1 6v4l-1 1h-1 0c-1-2-3-3-4-4h-1c-2-1-3-2-4-4h0 1l1-1c2 3 4 5 6 7 1-1 1-3 1-4v-1c0-2 0-3 1-4z" class="Q"></path><defs><linearGradient id="Ab" x1="429.612" y1="359.556" x2="423.379" y2="362.692" xlink:href="#B"><stop offset="0" stop-color="#070706"></stop><stop offset="1" stop-color="#2a2a2d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M418 340l1 1v1h-1 0v1l7 7c0 1 1 1 1 1l8 9c1 2 2 3 3 5l-1 1h0v1h0c-1-1-3-2-4-4v1c2 4 4 7 5 11 1 1 1 2 1 3h-1c-2-4-4-8-6-11-5-9-12-16-19-24h2 0 1l-1-3c1 1 3 2 4 1v-1z"></path><defs><linearGradient id="Ac" x1="422.134" y1="356.42" x2="426.683" y2="346.636" xlink:href="#B"><stop offset="0" stop-color="#838082"></stop><stop offset="1" stop-color="#a8a9a8"></stop></linearGradient></defs><path fill="url(#Ac)" d="M418 340l1 1v1h-1 0v1l7 7c0 1 1 1 1 1l8 9c1 2 2 3 3 5l-1 1h0v1h0c-1-1-3-2-4-4-2-2-4-5-6-8-3-4-8-8-12-12h0 1l-1-3c1 1 3 2 4 1v-1z"></path><path d="M434 360c1 2 2 3 3 5l-1 1h0v1h0c0-1-1-2-2-3l-1-2 1-2z" class="e"></path><path d="M421 240c3-2 4-1 7-1 0 1 0 1 1 2l1-2 1 2 1 1c0 1 1 3 3 4v-2c2 0 4-1 6 0 0 1 1 1 1 2h0c1 1 2 2 3 2 0-1 0-3 2-4v1c1 1 1 1 3 1h0l1 1 1 2h1c0 1-1 2-1 2v1l-1 1c-1 1-1 2-1 3l2-1c-1 1-1 2-1 3-1 0-1 0-1 1l-1 1h-2v1c2 3 5 6 9 7h0c1 0 2 1 3 1s1 0 1 1h0v1h0c-1 0-2-1-3-1-2 0-4-1-6-1h-6-1c-2 1-4 1-6 2-2 0-4 0-5 1-1 0-3 1-4 1l-2 1-3 1c-1 0-1 0-1 2h1c0 1-1 2-2 4h0v-1h0l-1 1c0-1 0-1-1-2-1 0-1 1-1 1-2 0-2-1-2-2l-1-1-1 1c0-1 0-2 1-3h-1l-3 4h0v-1-1c0-1-1-1-2-1s-1 1-2 1l-2 2h0c0-1 0-2 1-3h-1l-1-1 2-2h-1c1-1 3-3 3-4-1 0-2 1-3 1v-1h-1l-1-1h-3v-1-1c2-2 9-3 12-4l15-2v-1c-2 0-4 0-6 1l-9 1c-1 1-4 1-5 0 0-1 0-1 1-2h0l-1-1-2 4c-2 2-5 2-7 3-1-1-1-1-2-1l4-2h1l-1-2c-1 1-2 1-3 1h0l2-2c-1 0-2 1-3 1 0-1 0-1 1-1 1-2 2-2 4-2h0l1-1 2-2c-3 0-5 1-8 2v-2l1-1v-1l1-1h1c-1-1-2-1-2-1v-2h3v-1h2s-1 0-1-1l6-3h0c2 0 5-1 7-2l1-1c1 0 3 0 4-1l1 1h0z" class="t"></path><path d="M440 252l1 1h0l1 1v1c-1 0-1 0-1 1h-1v-4z" class="T"></path><path d="M401 260h0c1 0 1-1 2-1h0v2h0c1 0 2 1 3 1-2 2-5 2-7 3-1-1-1-1-2-1l4-2h1l-1-2z" class="q"></path><path d="M421 240c3-2 4-1 7-1 0 1 0 1 1 2l1-2 1 2 1 1c-1 0-2 1-2 1-2 0-4-3-6-3h-3z" class="O"></path><path d="M435 244c2 0 4-1 6 0 0 1 1 1 1 2h0v5h0c-1 1-1 1-1 2l-1-1-3-3-1-2-1-1v-2z" class="I"></path><path d="M436 247c1-1 2-1 3 0s2 3 3 4c-1 1-1 1-1 2l-1-1-3-3-1-2z" class="g"></path><path d="M401 247h2c4 1 8 3 12 4 3 2 7 3 10 5h0c-2 1-6-3-8-3s-4 1-6 2c0-1-1-1-2-1h-4 0c-3 0-5 1-8 2v-2l1-1v-1l1-1h1c-1-1-2-1-2-1v-2h3v-1z" class="E"></path><path d="M398 248h3l1 1 12 3c1 0 2 1 2 1-3 0-7-1-11 0h-7v-1l1-1h1c-1-1-2-1-2-1v-2z" class="V"></path><path d="M447 244v1c1 1 1 1 3 1h0l1 1 1 2h1c0 1-1 2-1 2v1l-1 1c-1 1-1 2-1 3l2-1c-1 1-1 2-1 3-1 0-1 0-1 1l-1 1h-2v1l-3 2c1-1 1-3 1-4v-1l-2-1-2-1c0-1 0-1 1-1v-1l-1-1h0c0-1 0-1 1-2h0v-5c1 1 2 2 3 2 0-1 0-3 2-4z" class="B"></path><path d="M447 245c1 1 1 1 3 1h0l1 1-1 2s-1 0-1-1h-2v-3z" class="e"></path><path d="M450 256l2-1c-1 1-1 2-1 3-1 0-1 0-1 1l-1 1h-2v-1c0-1 2-2 3-3z" class="P"></path><path d="M441 253l1-1c0 1 1 1 1 1 2-1 2-2 2-3v8l-2-1-2-1c0-1 0-1 1-1v-1l-1-1z" class="K"></path><path d="M442 246c1 1 2 2 3 2v2c0 1 0 2-2 3 0 0-1 0-1-1l-1 1h0c0-1 0-1 1-2h0v-5z" class="i"></path><path d="M447 248h2c0 1 1 1 1 1v1h0v1l-3 5v-7-1z" class="g"></path><path d="M447 248h2c0 1 1 1 1 1v1c-1 0-2 1-3 0v-1-1z" class="G"></path><path d="M443 257l2 1v1c0 1 0 3-1 4s-3 2-4 3c-3 1-7 2-10 3-3 2-6 2-8 2-1 0-1 0-1 1-1 0-1 0-1-1-2 0-6 1-7 2l-1-1c-2 1-3 3-5 4h-1l-1-1 2-2h-1c1-1 3-3 3-4-1 0-2 1-3 1v-1h-1l-1-1h-3v-1-1c2-2 9-3 12-4l-1 2h0l-2 2s-1 1-1 2h0 1c0-1 4-2 5-2h1v1c-1-1-2-1-3 0l-2 1h0 1l1 1h5c2 1 5 0 7-1l1-1c2-1 5-2 6-3s2-1 3-2c1 0 1-1 2-1s2-1 4-2h0c1-1 2-1 2-2z" class="C"></path><path d="M407 273c1-1 2-2 4-2 1-1 1 0 1 0v1c-2 1-3 3-5 4h-1l-1-1 2-2z" class="f"></path><path d="M443 257l2 1v1c-1 1-2 2-4 3l-8 3c0 1-1 1-1 1-1 0-1 0-1 1h-1c-1 1-2 0-4 1h-1l1-1c2-1 5-2 6-3s2-1 3-2c1 0 1-1 2-1s2-1 4-2h0c1-1 2-1 2-2z" class="Z"></path><path d="M447 261c2 3 5 6 9 7h0c1 0 2 1 3 1s1 0 1 1h0v1h0c-1 0-2-1-3-1-2 0-4-1-6-1h-6-1c-2 1-4 1-6 2-2 0-4 0-5 1-1 0-3 1-4 1l-2 1-3 1c-1 0-1 0-1 2h1c0 1-1 2-2 4h0v-1h0l-1 1c0-1 0-1-1-2-1 0-1 1-1 1-2 0-2-1-2-2l-1-1-1 1c0-1 0-2 1-3h-1l-3 4h0v-1-1c0-1-1-1-2-1s-1 1-2 1l-2 2h0c0-1 0-2 1-3 2-1 3-3 5-4l1 1c1-1 5-2 7-2 0 1 0 1 1 1 0-1 0-1 1-1 2 0 5 0 8-2 3-1 7-2 10-3 1-1 3-2 4-3l3-2z" class="D"></path><path d="M412 278l2-3h-1v-1c2 0 4-1 5 0 3-1 7 0 11-1l-2 1c-2 0-5 0-7 1l-1 2h-1v1h-1l-1-1-1 1c0-1 0-2 1-3h-1l-3 4h0v-1z" class="d"></path><path d="M419 277l1-2c2-1 5-1 7-1l-3 1c-1 0-1 0-1 2h1c0 1-1 2-2 4h0v-1h0l-1 1c0-1 0-1-1-2-1 0-1 1-1 1-2 0-2-1-2-2h1v-1h1z" class="b"></path><path d="M417 278h1v-1h1l1 1 1-1v1h0c0 1 0 1 1 2l-1 1c0-1 0-1-1-2-1 0-1 1-1 1-2 0-2-1-2-2z" class="m"></path><path d="M447 261c2 3 5 6 9 7h0c1 0 2 1 3 1s1 0 1 1h0v1h0c-1 0-2-1-3-1-2 0-4-1-6-1h-6c0-1-1-1-2-1-1 1-3 0-4 1 0 0-1 0-2 1h0-2l-1 1h-4v-1-1c3-1 7-2 10-3 1-1 3-2 4-3l3-2z" class="W"></path><defs><linearGradient id="Ad" x1="407.201" y1="288.847" x2="423.736" y2="302.954" xlink:href="#B"><stop offset="0" stop-color="#040403"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#Ad)" d="M402 277c1-2 3-3 4-4h1l-2 2 1 1h1c-1 1-1 2-1 3h0l2-2c1 0 1-1 2-1s2 0 2 1v1 1h0l3-4h1c-1 1-1 2-1 3l1-1 1 1c0 1 0 2 2 2 0 0 0-1 1-1 1 1 1 1 1 2h0l-1 1 1 1h1 0c-1 1-1 2-1 3v2h1 1l1 2 1-4 1 1v2h1l-1 4c1 0 1 1 1 2l2 6c1 4 3 9 5 12l1 1-1 2c1 1 2 2 2 3 1 1 2 3 2 4 2 3 4 4 6 6l3 2 1 3-2-1h-2c-2 0-3-1-4-2l-2-2h-1c-1 1-1 1 0 1v2c1 1 2 2 4 3l-1 1h0v1h0-2v1h-2 0c-1 0-6-4-6-5l-5-4v1c1 1 2 2 3 2-1 1-2 0-3 0-1-1-3-2-5-4-2-1-3-2-5-3h-1 0l-2-2v-1l-2-2c-1 1-1 1-2 1h0c-1-2-3-4-4-6-1-3-2-7-4-9v1h0v-1c-1-3-1-6-1-9l-1-1v-1-1-1c1-1 1-3 1-4l-1-1v-2c1 0 1-1 1-1v-2c2-2 1-4 3-6z"></path><path d="M417 303l2-2v4l-2-1h0v-1z" class="J"></path><path d="M418 311l2-2 1 2v2c-1 0-1 1-2 2l-1-4z" class="m"></path><path d="M417 304l2 1 1 4-2 2h0c0-2-1-4-1-7z" class="E"></path><path d="M416 293c1-2 0-6 1-8l1 1v2c1 2 0 3 0 5h-1-1z" class="W"></path><path d="M416 293h1 1v3c1 1 1 1 0 2 1 1 1 2 1 3l-2 2v1c-1-2 0-4 0-6h0 0c-1-2-1-3-1-5z" class="I"></path><path d="M417 303c0-1 1-3 1-5h0c1 1 1 2 1 3l-2 2z" class="k"></path><path d="M421 288h1 1l1 2c-1 0-2 1-2 1v3c0 2-1 4-1 6v-1c-1 1-1 1-1 2h-1c0-1 1-1 1-1 0-1-1-3 0-4v-1-1c1-1 1-2 1-3v-3z" class="f"></path><path d="M408 299c0-1 0-3 1-4h1v1c0 4 0 7 1 11 0 1 1 2 1 3l-1 1-3-12z" class="K"></path><path d="M412 279h0l3-4h1c-1 1-1 2-1 3l1-1 1 1c0 1 0 2 2 2l-1 1h-1c-1 0-1-1-2-2v1c-1 1-2 1-2 2-1 1-2 3-2 4-1-1-1-1-1-3 0-1 1-2 1-3l1-1z" class="B"></path><path d="M411 280c0 1-1 2-1 3 0 2 0 2 1 3 0 1-1 2-1 2 0 1 1 2 0 3v5-1h-1c-1 1-1 3-1 4v-1c-1-2-1-5-1-6 0-4 2-9 4-12z" class="a"></path><path d="M408 298v-5l1-1c0-1 0-1 1-1v5-1h-1c-1 1-1 3-1 4v-1z" class="Q"></path><defs><linearGradient id="Ae" x1="421.525" y1="325.481" x2="425.651" y2="323.318" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#Ae)" d="M416 313c-2-4-3-8-3-12h0c1 1 1 4 2 5 1 3 5 16 8 16 2 1 3 3 3 4h0v1c1 0 1 0 1 1l2 2h0v-2c1 1 3 3 3 4h0 0-1-1 0v1l-5-4-1-1c-3-3-5-5-6-9l-2-6z"></path><path d="M423 322c2 1 3 3 3 4h0v1c1 0 1 0 1 1h0c-1-1-2-2-2-3-1-1 0-1-1-1 0-1 0-1-1-2z" class="e"></path><path d="M419 315c1-1 1-2 2-2l4 7 2 3 1 1 1 1c0 1 1 2 2 3s2 3 3 4c2 2 4 3 6 5h-2v1h-2 0c-1 0-6-4-6-5v-1h0 1 1 0 0c0-1-2-3-3-4v2h0l-2-2c0-1 0-1-1-1v-1h0c0-1-1-3-3-4l-4-7z" class="g"></path><path d="M424 290l1-4 1 1v2h1l-1 4c1 0 1 1 1 2l2 6c1 4 3 9 5 12l1 1-1 2c1 1 2 2 2 3 1 1 2 3 2 4 2 3 4 4 6 6l3 2 1 3-2-1h-2c-2 0-3-1-4-2l-2-2h-1c-1 1-1 1 0 1v2c1 1 2 2 4 3l-1 1h0v1h0c-2-2-4-3-6-5-1-1-2-3-3-4s-2-2-2-3l-1-1-1-1-2-3-4-7v-2h1c-1-2-1-4-1-6-1-1-1-3 0-4v-1h0c0-2 1-4 1-6v-3s1-1 2-1z" class="k"></path><path d="M421 311h1c1 2 2 6 4 7v-2-2h1c-1 3 3 5 2 8h-1c-1-1-1-2-2-3h0l-1 1-4-7v-2z" class="I"></path><path d="M426 310c2 5 5 9 8 13l1 1 3 3h-2l-1-1c-2-2-3-4-5-5-1-2-2-5-3-7h-1v2c-1-1-1-2-1-3h1v-3z" class="a"></path><path d="M426 310l-1-5 3 4v-1c1 1 1 2 2 3l1 5c1 2 2 3 3 5v2c-3-4-6-8-8-13z" class="Y"></path><path d="M434 332h2c-1-2-4-4-4-6h2 1l1 1h2l3 3-1 1-2-2h-1c-1 1-1 1 0 1v2c1 1 2 2 4 3l-1 1h0v1h0c-2-2-4-3-6-5z" class="E"></path><defs><linearGradient id="Af" x1="431.944" y1="306.75" x2="428.743" y2="309.023" xlink:href="#B"><stop offset="0" stop-color="#5f5e5f"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#Af)" d="M426 301c1-1 1-1 1-2l2 2c1 4 3 9 5 12l1 1-1 2v-1l-1 1c1 1 3 4 3 6h-1l-1-1c-1-2-2-3-3-5l-1-5c-1-1-1-2-2-3-1-2-1-5-2-7z"></path><path d="M434 321l1 1h1c0-2-2-5-3-6l1-1v1c1 1 2 2 2 3 1 1 2 3 2 4 2 3 4 4 6 6l3 2 1 3-2-1h-2c-2 0-3-1-4-2l1-1-3-3-3-3-1-1v-2z" class="h"></path><path d="M435 324l1-1v1c2 2 5 5 7 6l1-1 3 2 1 3-2-1h-2c-2 0-3-1-4-2l1-1-3-3-3-3z" class="d"></path><path d="M441 330l5 3h-2c-2 0-3-1-4-2l1-1z" class="p"></path><path d="M424 290l1-4 1 1v2h1l-1 4c1 0 1 1 1 2l2 6-2-2c0 1 0 1-1 2 1 2 1 5 2 7v1l-3-4 1 5v3h-1c0 1 0 2 1 3v2c-2-1-3-5-4-7s-1-4-1-6c-1-1-1-3 0-4v-1h0c0-2 1-4 1-6v-3s1-1 2-1z" class="v"></path><path d="M426 293c1 0 1 1 1 2l2 6-2-2c0 1 0 1-1 2v-8z" class="J"></path><path d="M422 311c-1-2-1-4-1-6-1-1-1-3 0-4v-1c1 5 2 9 4 13 0 1 0 2 1 3v2c-2-1-3-5-4-7z" class="R"></path><defs><linearGradient id="Ag" x1="404.702" y1="304.71" x2="427.146" y2="304.5" xlink:href="#B"><stop offset="0" stop-color="#080908"></stop><stop offset="1" stop-color="#292829"></stop></linearGradient></defs><path fill="url(#Ag)" d="M402 277c1-2 3-3 4-4h1l-2 2 1 1h1c-1 1-1 2-1 3h0l2-2c1 0 1-1 2-1s2 0 2 1v1 1l-1 1c-2 3-4 8-4 12 0 1 0 4 1 6v1l3 12 1-1c1 1 1 2 2 3h2l2 6c1 4 3 6 6 9l1 1v1c1 1 2 2 3 2-1 1-2 0-3 0-1-1-3-2-5-4-2-1-3-2-5-3h-1 0l-2-2v-1l-2-2c-1 1-1 1-2 1h0c-1-2-3-4-4-6-1-3-2-7-4-9v1h0v-1c-1-3-1-6-1-9l-1-1v-1-1-1c1-1 1-3 1-4l-1-1v-2c1 0 1-1 1-1v-2c2-2 1-4 3-6z"></path><path d="M408 277v1l-3 5c1 1 1 2 0 3-2-3 0-4 1-6v-1l2-2z" class="W"></path><path d="M412 310c1 1 1 2 2 3h2l2 6c-3-1-6-6-7-8l1-1z" class="U"></path><path d="M401 304c1-1 2-6 3-6v1c1 2 1 4 2 6l4 8 10 15c-2-1-3-2-5-3h-1 0l-2-2v-1l-2-2c-1 1-1 1-2 1h0c-1-2-3-4-4-6h1v-1c-1-2-2-5-3-8 0 0 0-1-1-2z" class="i"></path><path d="M405 314l5 6c-1 1-1 1-2 1h0c-1-2-3-4-4-6h1v-1z" class="E"></path><path d="M406 305l4 8-1 1c1 2 2 4 3 5h0l3 3-1 1c0-1-1-1-2-2l-1-1-2-2c0-1-1-2-1-2 0-1-1-2-1-3h0c-1-2-2-5-2-6s1-1 1-2h0z" class="n"></path><defs><linearGradient id="Ah" x1="398.214" y1="288.52" x2="404.99" y2="291.546" xlink:href="#B"><stop offset="0" stop-color="#09090a"></stop><stop offset="1" stop-color="#2f302e"></stop></linearGradient></defs><path fill="url(#Ah)" d="M402 277c1-2 3-3 4-4h1l-2 2 1 1h1c-1 1-1 2-1 3h0v1c-1 2-3 3-1 6l-1 2v2c0 3 1 6 0 9v-1c-1 0-2 5-3 6 1 1 1 2 1 2l3 8v1h-1c-1-3-2-7-4-9v1h0v-1c-1-3-1-6-1-9l-1-1v-1-1-1c1-1 1-3 1-4l-1-1v-2c1 0 1-1 1-1v-2c2-2 1-4 3-6z"></path><path d="M402 277h1l-2 6c-1 2-2 4-2 6l-1-1v-2c1 0 1-1 1-1v-2c2-2 1-4 3-6z" class="a"></path><path d="M402 291c1-1 1-1 2-1 0 3 1 6 0 9v-1c-1 0-2 5-3 6v-3c0-3 0-6 1-10z" class="U"></path><path d="M402 277c1-2 3-3 4-4h1l-2 2 1 1h1c-1 1-1 2-1 3h0v1c-1 2-3 3-1 6l-1 2v2c-1 0-1 0-2 1v-1-1c0-1 0-2 1-3 0-2 1-4 1-5v-4h-1-1z" class="I"></path><path d="M402 290l2-2h0v2c-1 0-1 0-2 1v-1z" class="B"></path><path d="M410 320l2 2v1l2 2h0 1c2 1 3 2 5 3 2 2 4 3 5 4 1 0 2 1 3 0-1 0-2-1-3-2v-1l5 4c0 1 5 5 6 5h0 2v-1h2 0c2 2 5 4 7 5l3 2 7 4v2l-1-1v1l13 14c1 1 1 2 3 3s4 3 6 5 4 5 6 8c0 3 1 7 3 9 0 0 1 4 2 4h0c2 3 3 8 3 12 0 2 1 3 1 5l-1 1c0 1 1 2 1 3 0 2 1 5 1 7-1-2-1-3-3-4v3 3 1c0 1 0 2 1 3 1 9 4 17 9 25 2 2 5 5 7 8h-1c0-1-3-3-4-3v1l1 1v3c0 2 2 4 1 5h-1c0 2 1 3 2 4v1c0 1 1 2 2 3 0 2 1 6 2 7v1l1 1v3h-1v1l1 1v2c-1 1-2 0-3 0v-2l-1-1v-1c-1 1-1 2-2 3 0 1-2 2-3 3v2c-1-1-1-2-1-3 0 1 0 2-1 3l-1-1v-2h0l-2 1v-1h-2c1 1 1 3 1 4l-1 1 1 3h-1 0l1 4h0-4l-1 1 1 1-1 1v-1l-7-13-1-2c0-1-1-2-1-4l-2-9c0-1-1-4-1-6h1v-3h0c-1-1-1-1-1-2l-2-7v-2h1c0-3-2-5-3-7 0-2-3-5-3-6-2-2-2-5-3-8h-1c-2-1-3-3-4-5v-1c-3-6-8-12-12-17l2-2-1-1-1-1v-1h1l-3-2v-2h1l-1-1 1-1-3-4h0v-1-1l1-1v-4c-1-2 0-4-1-6v-3l1 1c0-1 0-1 1-1l-1-3-3-4h0c0-1-1-3-2-4h-2c-1-3-3-5-5-7-1-2-2-3-3-5l-8-9s-1 0-1-1l-7-7v-1h0 1v-1l-1-1-4-3v-1c-1-1-3-3-3-4-1-1-1-1-1-2h0l-1-1h0 0l-1 1v-1h0c0-2-2-3-2-5l1 1v-3l1-1h0c1 0 1 0 2-1z" class="V"></path><path d="M484 416v1-1c2 3 2 6 2 8h-1v-2-1h0v4c-1-3-1-6-1-9z" class="P"></path><path d="M469 370l-1-1 1-1c4 4 10 9 11 15v1-1c-1-1-1-1-1-2-1-1-1-2-2-3l-1-1c0-1-1-1-1-2-1 0 0 1 0 2-2-3-4-5-6-7zm9 52h0c1 4 3 9 3 13v9l1 4-1-1v-1l-1-1h0l-1-2-1 2v-2c1 0 1-1 1-2 0-6 0-13-1-19z" class="C"></path><path d="M458 365h0c3 5 7 10 10 15 1 2 2 4 2 7h0c-2-4-5-9-7-13h0c-1-1-2-2-2-3l-1 1v1l-1-1-1-3v-4z" class="L"></path><path d="M491 424c0 1 0 2 1 3 1 9 4 17 9 25-1 0-1 1-1 1 1 1 1 2 1 3v1c-1-2-2-3-3-5v-1c0-1-1-3-2-5-2-5-6-11-5-17v-5z" class="u"></path><path d="M489 393h0c2 3 3 8 3 12 0 2 1 3 1 5l-1 1c0 1 1 2 1 3 0 2 1 5 1 7-1-2-1-3-3-4v3-3c-1-3-2-7-1-10v-4-3c0-2-1-5-1-7z" class="q"></path><path d="M490 403c0 2 0 3 1 4v2 1 4c-1 1-1 2 0 3-1-3-2-7-1-10v-4z" class="b"></path><path d="M485 430c0 1 1 2 1 3v3c0 2 1 3 1 4h1c0-1 0-2-1-2v-1-2-1h0v-3h-1 0c1-1 0-2 0-2 0-2 0-4 1-6 0-2 0-5-1-8h0v-2l-1-3v-2h0l1 1c0 2 1 4 2 5v2 1c0 1 0 1 1 1v1 1 1l1 1h-1-1v-4h-1c0 2 0 4 1 5h0 0c0 3 1 6 0 8 0 5 3 10 5 14-1 0-2 0-3 1v-1h0v3h0c-3-2-4-8-4-11-1-2-1-5-1-7z" class="F"></path><path d="M490 445c-2-5-3-10-3-15h1v1c0 5 3 10 5 14-1 0-2 0-3 1v-1h0z" class="r"></path><defs><linearGradient id="Ai" x1="477.883" y1="437.329" x2="486.718" y2="424.827" xlink:href="#B"><stop offset="0" stop-color="#b8b9b9"></stop><stop offset="1" stop-color="#dedcdb"></stop></linearGradient></defs><path fill="url(#Ai)" d="M484 446h0l-2-16c0-3-1-6-1-9v-1l1 1v1c2 2 2 4 3 6v2c0 2 0 5 1 7 0 3 1 9 4 11h0 1l2 5h-1l1 3c-3-2-5-5-7-7 0-1-1-3-2-4v1z"></path><path d="M492 453c-1-1-2-2-3-4-1-1-4-4-4-7h0v-1c0-1 0-3 1-4 0 3 1 9 4 11h0 1l2 5h-1z" class="H"></path><path d="M460 373v-1l1-1c0 1 1 2 2 3h0c2 4 5 9 7 13v1h2v1c1 1 1 1 1 3 0 0-1 1-1 2h-2c-1-1-1-1-1-2v2 1h-1c-1-3-3-7-4-10l-6-12 1-1 1 1z" class="s"></path><path d="M460 373v-1l1-1c0 1 1 2 2 3h0l-1 1 1 3c2 4 5 10 6 14v2 1h-1c-1-3-3-7-4-10l-6-12 1-1 1 1z" class="G"></path><path d="M460 373v-1l1-1c0 1 1 2 2 3h0l-1 1 1 3c-2-1-2-3-3-5z" class="C"></path><path d="M455 374c4 4 6 8 8 12l1-1c1 3 3 7 4 10h1v-1h0c1 2 1 4 2 6 0 1 1 2 2 4 0 1 1 1 2 2 0 0 3 12 3 13-1-1-1-5-3-6v1c1 2 1 4 2 6 0 0 0 2 1 2 1 6 1 13 1 19v1c-2-4-2-8-3-12-2-4-5-7-6-11l-1-2h-1c-1-2-2-5-3-8 0-2-1-5-2-7-1-1-1-1-1-2v-1-1h0v-1c-1-1-1-2-1-3-1-2-1-3-2-4v-2l-7-11 1-1 2 2v-4z" class="s"></path><path d="M469 397l1 3v1h0v2 5c-1-1-1-3-1-4v-1c1-2 0-4-1-5l1-1z" class="F"></path><path d="M461 394l2 2c3 7 4 14 6 21h-1c-1-2-2-5-3-8 0-2-1-5-2-7-1-1-1-1-1-2v-1-1h0v-1c-1-1-1-2-1-3z" class="O"></path><defs><linearGradient id="Aj" x1="452.604" y1="379.641" x2="465.374" y2="385.945" xlink:href="#B"><stop offset="0" stop-color="#818380"></stop><stop offset="1" stop-color="#aeabad"></stop></linearGradient></defs><path fill="url(#Aj)" d="M455 374c4 4 6 8 8 12l1-1c1 3 3 7 4 10h1v-1h0v3l-1 1c1 1 2 3 1 5v1l-4-11c-1 0-2 0-2 1v2l-2-2c-1-2-1-3-2-4v-2l-7-11 1-1 2 2v-4z"></path><path d="M464 385c1 3 3 7 4 10h1v-1h0v3l-1 1c0-1-1-3-1-4-1-3-3-5-4-8l1-1z" class="C"></path><path d="M452 377l1-1 2 2c2 2 3 4 4 7 1 2 2 5 4 7h0v-1h1c0 1 1 1 1 2-1 0-2 0-2 1v2l-2-2c-1-2-1-3-2-4v-2l-7-11z" class="F"></path><defs><linearGradient id="Ak" x1="460.087" y1="376.367" x2="476.976" y2="367.922" xlink:href="#B"><stop offset="0" stop-color="#cac9ca"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ak)" d="M436 338h0 2v-1h2 0c2 2 5 4 7 5l3 2 7 4v2l-1-1v1l13 14c1 1 1 2 3 3s4 3 6 5 4 5 6 8c0 3 1 7 3 9 0 0 1 4 2 4 0 2 1 5 1 7-1 2 0 4-1 5h0v1l-1-1v-1l-1-1v-1h0v-1h0v-1h-1 0v-1c-1-2-2-5-3-8 0-2-1-5-3-8-1-6-7-11-11-15l-1 1 1 1h-1v2 1h0v1l-2-4c-1 0-1-1-1-1l-2-3h0c-1-1-3-3-4-5h0c-1 0-2-1-2-1l-2-1h0l2 2v2c1 0 1 1 1 2h0l-1-2-1-2c-2-3-5-6-8-9 0 0-1-2-2-2-1-1-2-1-2-1-1-1-3-2-4-3 1 0 2 0 3 1h1 2l-6-5c-1 0-2-1-3-2 0-1 0-1-1-2z"></path><path d="M436 338h0 2v-1h2 0c2 2 5 4 7 5-1 0-2 0-4-1-1-1-3-1-5-2 1 2 4 4 6 6 4 2 8 5 11 9-1-1-4-4-5-4 0 1 1 2 1 2-1-2-3-4-5-5l-6-5c-1 0-2-1-3-2 0-1 0-1-1-2z" class="G"></path><path d="M440 346c1 0 2 0 3 1h1 2c2 1 4 3 5 5 0 1 0 1 1 2h1c0 1 1 2 1 2l1-1c0 1 1 1 1 2 1 1 2 1 2 2 1 2 3 3 5 5 0 1 1 1 1 2h-1 0c-1-1-3-3-4-5h0c-1 0-2-1-2-1l-2-1h0l2 2v2c1 0 1 1 1 2h0l-1-2-1-2c-2-3-5-6-8-9 0 0-1-2-2-2-1-1-2-1-2-1-1-1-3-2-4-3z" class="C"></path><defs><linearGradient id="Al" x1="507.967" y1="468.378" x2="497.232" y2="470.348" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#343436"></stop></linearGradient></defs><path fill="url(#Al)" d="M493 445c2 2 4 4 5 7 1 2 2 3 3 5v-1c0-1 0-2-1-3 0 0 0-1 1-1 2 2 5 5 7 8h-1c0-1-3-3-4-3v1l1 1v3c0 2 2 4 1 5h-1c0 2 1 3 2 4v1c0 1 1 2 2 3 0 2 1 6 2 7v1l1 1v3h-1v1l1 1v2c-1 1-2 0-3 0v-2l-1-1v-1c-1 1-1 2-2 3 0 1-2 2-3 3l1-3v-3h0-1v-2l-3-12v-2c-2-1-2-2-3-4l-1-1c-1 1-1 2-1 3-1-1-1-2-2-2 0-1 0-2 1-2-1-2-3-5-4-6l-2-4h0c-1-1-1-2-2-3 0-2-1-4-1-6v-1c1 1 2 3 2 4 2 2 4 5 7 7l-1-3h1l-2-5h-1v-3h0v1c1-1 2-1 3-1z"></path><path d="M497 459c2 2 3 6 4 9h0c-1-1-2-2-2-3-2-2-2-3-3-5 1 1 1 2 2 2h0c-1-1-1-2-1-3z" class="l"></path><path d="M493 453c1 1 3 4 4 6 0 1 0 2 1 3h0c-1 0-1-1-2-2s-2-3-3-4l-1-3h1z" class="q"></path><path d="M495 452h3c1 2 2 3 3 5v4l-6-9z" class="p"></path><path d="M493 445c2 2 4 4 5 7h-3c-1-2-2-4-4-5v1h-1v-3h0v1c1-1 2-1 3-1z" class="i"></path><path d="M501 452c2 2 5 5 7 8h-1c0-1-3-3-4-3v1l1 1v3c0 2 2 4 1 5h-1l-3-6v-4-1c0-1 0-2-1-3 0 0 0-1 1-1z" class="n"></path><path d="M503 487c1-2 0-4 1-6 0-1 1-1 1-1l2 5c0 1 0 1 1 2v-1h0c1 1 0 1 1 1v-3l1 1v2 1l1 1v2c-1 1-2 0-3 0v-2l-1-1v-1c-1 1-1 2-2 3 0 1-2 2-3 3l1-3v-3z" class="M"></path><path d="M484 446v-1c1 1 2 3 2 4 2 2 4 5 7 7 1 1 2 3 3 4 1 2 1 3 3 5 0 1 1 2 2 3h0c3 4 4 9 5 13 1 1 1 3 1 4l-2-5s-1 0-1 1c-1 2 0 4-1 6h0-1v-2l-3-12v-2c-2-1-2-2-3-4l-1-1c-1 1-1 2-1 3-1-1-1-2-2-2 0-1 0-2 1-2-1-2-3-5-4-6l-2-4h0c-1-1-1-2-2-3 0-2-1-4-1-6z" class="d"></path><path d="M484 446v-1c1 1 2 3 2 4 1 3 4 5 6 9h-1c-1 0-1-1-2-2l-2-2v1c-1-1-1-2-2-3 0-2-1-4-1-6z" class="m"></path><path d="M487 455v-1l2 2c1 1 1 2 2 2h1l3 5h0l1 2v2l-1-1c-1 1-1 2-1 3-1-1-1-2-2-2 0-1 0-2 1-2-1-2-3-5-4-6l-2-4h0z" class="c"></path><defs><linearGradient id="Am" x1="502.454" y1="486.133" x2="499.776" y2="469.509" xlink:href="#B"><stop offset="0" stop-color="#797779"></stop><stop offset="1" stop-color="#9f9fa0"></stop></linearGradient></defs><path fill="url(#Am)" d="M495 463h1c1 1 2 4 4 6 0 0 1 1 1 2h0c2 1 3 5 3 6 1 1 1 2 1 3 0 0-1 0-1 1-1 2 0 4-1 6h0-1v-2l-3-12v-2c-2-1-2-2-3-4v-2l-1-2z"></path><path d="M496 465l3 6c-2-1-2-2-3-4v-2z" class="R"></path><path d="M501 471c2 1 3 5 3 6 1 1 1 2 1 3 0 0-1 0-1 1-1 2 0 4-1 6h0c1-5 0-11-2-16z" class="D"></path><path d="M448 383l1 1c0-1 0-1 1-1s2 2 2 2v1c1 0 1 0 2 1 1 0 1 1 1 2h1c1 0 2 0 3-1v2c1 1 1 2 2 4 0 1 0 2 1 3v1h0v1 1c0 1 0 1 1 2 1 2 2 5 2 7l3 8h1l1 2c1 4 4 7 6 11 1 4 1 8 3 12v-1c0 1 0 2-1 2v2l1 8c0 2 1 4 0 5v1l-1-1c0-3-2-5-3-7 0-2-3-5-3-6-2-2-2-5-3-8h-1c-2-1-3-3-4-5v-1c-3-6-8-12-12-17l2-2-1-1-1-1v-1h1l-3-2v-2h1l-1-1 1-1-3-4h0v-1-1l1-1v-4c-1-2 0-4-1-6v-3z" class="s"></path><path d="M450 405h1l-1-1 1-1 1 1 4 5c-1 0-1 1-1 1 0 1-1 1-1 2l-1-1-1-1v-1h1l-3-2v-2z" class="T"></path><path d="M450 405h1l-1-1 1-1 1 1h-1l1 1c0 1 1 1 1 2v2l-3-2v-2z" class="b"></path><path d="M469 417l1 2c1 4 4 7 6 11 1 4 1 8 3 12v-1c0 1 0 2-1 2-1-3-1-6-2-9v-1c-3-6-7-10-8-16h1z" class="X"></path><path d="M459 418c-2-2-3-4-3-7l3 5h1c-1-1-1-1-1-2 1 0 1 1 2 2v-1c5 3 8 9 10 14 1 3 3 6 3 9h0c-2-5-6-10-9-14-2-3-4-6-7-8h0c1 0 1 1 1 2z" class="F"></path><path d="M456 409c1 2 3 4 5 6v1c-1-1-1-2-2-2 0 1 0 1 1 2h-1l-3-5c0 3 1 5 3 7h0l10 19h-1c-2-1-3-3-4-5v-1c-3-6-8-12-12-17l2-2c0-1 1-1 1-2 0 0 0-1 1-1z" class="b"></path><path d="M452 385v1c1 0 1 0 2 1 1 0 1 1 1 2h1c1 0 2 0 3-1v2c1 1 1 2 2 4 0 1 0 2 1 3v1h0v1 1c0 1 0 1 1 2 1 2 2 5 2 7h0c0-1-1-2-1-3l-1-1c0 1 0 3 1 5l-1 1h-1l-1-1c-1-1-1-1-1-3s2-3 1-5v-4c-1 3-1 7-2 9h-1c1-1 1-5 2-7 0-1-1-2-2-3-3-3-4-7-7-10v-1l1-1z" class="V"></path><path d="M459 388v2l-1 3-3-4h1c1 0 2 0 3-1z" class="g"></path><path d="M461 410c0-2 0-5 1-7 1 2 1 5 1 7l-1 1-1-1z" class="j"></path><path d="M459 390c1 1 1 2 2 4 0 1 0 2 1 3v1h0v1 1l-4-7 1-3z" class="l"></path><path d="M448 383l1 1c0-1 0-1 1-1s2 2 2 2l-1 1v1c3 3 4 7 7 10 1 1 2 2 2 3-1 2-1 6-2 7-2-1-4-4-6-6-1-1-1-2-2-3l-2-1 1-1v-4c-1-2 0-4-1-6v-3z" class="O"></path><path d="M449 384c0-1 0-1 1-1s2 2 2 2l-1 1v1c-1 0-2-2-2-2v-1z" class="L"></path><path d="M450 392c1 2 3 6 2 9h0c-1-1-1-2-2-3v-6z" class="T"></path><path d="M448 383l1 1v1c0 2 1 4 1 7v6l-2-1 1-1v-4c-1-2 0-4-1-6v-3z" class="e"></path><path d="M478 445l1-2 1 2h0l1 1v1l1 1 1 3v1h0c0 1 1 1 2 2v-2c1 1 1 2 2 3h0l2 4c1 1 3 4 4 6-1 0-1 1-1 2 1 0 1 1 2 2 0-1 0-2 1-3l1 1c1 2 1 3 3 4v2l3 12v2h1 0v3l-1 3v2c-1-1-1-2-1-3 0 1 0 2-1 3l-1-1v-2h0l-2 1v-1h-2c1 1 1 3 1 4l-1 1 1 3h-1 0l1 4h0-4l-1 1 1 1-1 1v-1l-7-13-1-2c0-1-1-2-1-4l-2-9c0-1-1-4-1-6h1v-3h0c-1-1-1-1-1-2l-2-7v-2h1l1 1v-1c1-1 0-3 0-5l-1-8z" class="Y"></path><path d="M495 485l2 7h-2c-1-2-1-4-2-6l1 1v1c0-1 0-2 1-3z" class="e"></path><path d="M485 466l1-1 2 2 1-1c1 3 1 5 2 9 0 2 1 4 2 6 1 1 1 3 2 4-1 1-1 2-1 3v-1l-1-1c0-1-1-2-2-3v1c-1 0-2-3-3-4l-3-14z" class="G"></path><path d="M493 481l-1 1c-2-3-2-5-3-7h0 1 0v-2l1 2c0 2 1 4 2 6z" class="H"></path><defs><linearGradient id="An" x1="492.968" y1="484.508" x2="502.111" y2="477.89" xlink:href="#B"><stop offset="0" stop-color="#050405"></stop><stop offset="1" stop-color="#252525"></stop></linearGradient></defs><path fill="url(#An)" d="M494 469c0-1 0-2 1-3l1 1c1 2 1 3 3 4v2l3 12v2h1 0v3l-1 3v2c-1-1-1-2-1-3 0 1 0 2-1 3l-1-1v-2l-5-12c-1-3-2-5-2-9-1-1-1-2-1-3 2 1 3 4 4 6 1 1 1 2 2 2 0-1-1-2-1-3-1-1-2-3-2-4z"></path><path d="M502 485v2h1 0v3h-1l-1-1 1-4z" class="h"></path><path d="M501 492v-3l1 1h1l-1 3v2c-1-1-1-2-1-3z" class="R"></path><path d="M494 469c0-1 0-2 1-3l1 1c1 2 1 3 3 4v2s-1 0-1-1c-1-1-1-1-2-1v-2c-1 0-1 0-1 1 1 1 1 1 1 2v1c-1-1-2-3-2-4z" class="k"></path><path d="M478 445l1-2 1 2h0l1 1v1l1 1 1 3v1h0c0 1 1 1 2 2v-2c1 1 1 2 2 3h0l2 4s-1 1 0 2l-2 1 2 4-1 1-2-2-1 1 3 14-1 1-2-2c1 0 0-2 0-3l-1-2h-1l-2 2c0-2 0-5-1-7h0c-1-1-1-1-1-2l-2-7v-2h1l1 1v-1c1-1 0-3 0-5l-1-8z" class="L"></path><path d="M483 451v1h0c0 1 1 1 2 2v-2c1 1 1 2 2 3h0l-1 1s-1 0-1-1c-1 1-1 1 0 1l-1 1c-1-2-1-4-1-6z" class="C"></path><path d="M485 456c-1 0-1 0 0-1 0 1 1 1 1 1l1-1 2 4s-1 1 0 2l-2 1s0-1-1-2c0-1-1-2-1-4z" class="O"></path><path d="M481 460v-3h1v1l3 8 3 14-1 1-2-2c1 0 0-2 0-3l-1-2h-1l-2 2c0-2 0-5-1-7h0c1-4 0-8 0-11l1-1v3z" class="N"></path><path d="M480 469c1-4 0-8 0-11l1-1v3l4 16-1-2h-1l-2 2c0-2 0-5-1-7h0z" class="u"></path><path d="M480 469c1 2 1 5 1 7l2-2h1l1 2c0 1 1 3 0 3l2 2 1-1c1 1 2 4 3 4v-1c1 1 2 2 2 3 1 2 1 4 2 6 1 1 1 3 1 4l-1 1 1 3h-1 0l1 4h0-4l-1 1 1 1-1 1v-1l-7-13-1-2c0-1-1-2-1-4l-2-9c0-1-1-4-1-6h1v-3z" class="x"></path><path d="M491 497l3 1 1 2h0c-1 1-1 1-2 1-1-1-1-2-2-4z" class="I"></path><path d="M481 476l2-2h1l1 2c0 1 1 3 0 3l3 6c3 4 4 9 6 13l-3-1-7-16v-1h-1v3l-2-7z" class="J"></path><path d="M480 469c1 2 1 5 1 7l2 7c2 8 5 15 8 22l1 1-1 1v-1l-7-13-1-2c0-1-1-2-1-4l-2-9c0-1-1-4-1-6h1v-3z" class="C"></path><defs><linearGradient id="Ao" x1="488.49" y1="493.798" x2="493.624" y2="485.057" xlink:href="#B"><stop offset="0" stop-color="#a6a6a9"></stop><stop offset="1" stop-color="#c6c4c3"></stop></linearGradient></defs><path fill="url(#Ao)" d="M485 479l2 2 1-1c1 1 2 4 3 4v-1c1 1 2 2 2 3 1 2 1 4 2 6 1 1 1 3 1 4l-1 1 1 3h-1l-1-2c-2-4-3-9-6-13l-3-6z"></path><path d="M491 484v-1c1 1 2 2 2 3 1 2 1 4 2 6 1 1 1 3 1 4l-1 1c-1-1-1-5-2-7l-2-6z" class="E"></path><path d="M410 320l2 2v1l2 2h0 1c2 1 3 2 5 3 2 2 4 3 5 4 1 0 2 1 3 0-1 0-2-1-3-2v-1l5 4c0 1 5 5 6 5 1 1 1 1 1 2 1 1 2 2 3 2l6 5h-2-1c-1-1-2-1-3-1 1 1 3 2 4 3 0 0 1 0 2 1 1 0 2 2 2 2 3 3 6 6 8 9l1 2 1 2v4l1 3-1 1 6 12-1 1c-2-4-4-8-8-12v4l-2-2-1 1 7 11c-1 1-2 1-3 1h-1c0-1 0-2-1-2-1-1-1-1-2-1v-1s-1-2-2-2l-1-3-3-4h0c0-1-1-3-2-4h-2c-1-3-3-5-5-7-1-2-2-3-3-5l-8-9s-1 0-1-1l-7-7v-1h0 1v-1l-1-1-4-3v-1c-1-1-3-3-3-4-1-1-1-1-1-2h0l-1-1h0 0l-1 1v-1h0c0-2-2-3-2-5l1 1v-3l1-1h0c1 0 1 0 2-1z" class="z"></path><path d="M451 377h1l7 11c-1 1-2 1-3 1h-1c0-1 0-2-1-2-1-1-1-1-2-1v-1s-1-2-2-2l-1-3h1 1c1 1 2 3 3 4-1-2-3-4-3-7h0z" class="o"></path><path d="M440 359c2 1 3 1 4 3 2 3 6 6 8 9 1 1 3 2 3 3v4l-2-2-1 1h-1c-1-3-2-5-4-7 1-1 1-1 1-2l-6-6h0l-2-3z" class="i"></path><path d="M414 336l8 5 1-1 5 3 2 2c3 2 6 5 8 7l6 8h0l1 1-1 1c-1-2-2-2-4-3l2 3h0c-4-1-7-6-11-9-2-1-2-2-5-2 0 0-1 0-1-1l-7-7v-1h0 1v-1l-1-1-4-3v-1z" class="d"></path><path d="M419 341c2 1 3 1 4 3-1 0-2 0-3-1h-2v-1h0 1v-1z" class="T"></path><path d="M418 343h2c1 1 1 2 3 2v-1c2 1 5 4 7 5 0 2 1 2 2 3s1 1 1 2l3 3h2l2 1v1l2 3h0c-4-1-7-6-11-9-2-1-2-2-5-2 0 0-1 0-1-1l-7-7z" class="K"></path><path d="M423 340l5 3 2 2c3 2 6 5 8 7l6 8h0l1 1-1 1c-1-2-2-2-4-3v-1l-4-4-7-8-7-5 1-1z" class="Z"></path><path d="M423 340l5 3 2 2h0c-1 0-2-1-2-1l-3-2h0l2 2c1 0 1 1 2 2l-7-5 1-1z" class="X"></path><defs><linearGradient id="Ap" x1="440.506" y1="357.326" x2="445.027" y2="353.808" xlink:href="#B"><stop offset="0" stop-color="#a5a4a5"></stop><stop offset="1" stop-color="#c8c6c7"></stop></linearGradient></defs><path fill="url(#Ap)" d="M428 343c1-1 1-1 3 0 1 0 3 1 5 2v-1c1 0 3 2 4 2 1 1 3 2 4 3 0 0 1 0 2 1 1 0 2 2 2 2 3 3 6 6 8 9l1 2 1 2v4l1 3-1 1 6 12-1 1c-2-4-4-8-8-12 0-1-2-2-3-3-2-3-6-6-8-9l1-1-1-1h0l-6-8c-2-2-5-5-8-7l-2-2z"></path><path d="M446 354c3 4 6 6 8 10-1-1-3-1-4-3-2-2-3-3-4-7z" class="G"></path><path d="M438 352c2 0 2 2 4 3 2 0 6 6 7 8v1l-5-4-6-8z" class="T"></path><path d="M436 345v-1c1 0 3 2 4 2 1 1 3 2 4 3l2 3c-1 0-2-1-3 0 0 0-1-2-2-2l-5-5z" class="c"></path><path d="M443 352c1-1 2 0 3 0 4 5 8 9 11 15 0 1 0 1 1 2l1 3-1 1v-1l-4-8c-2-4-5-6-8-10l-3-2z" class="k"></path><path d="M444 349s1 0 2 1c1 0 2 2 2 2 3 3 6 6 8 9l1 2 1 2v4c-1-1-1-1-1-2-3-6-7-10-11-15l-2-3z" class="e"></path><path d="M444 360l5 4 5 5c1 1 2 2 4 3v1l6 12-1 1c-2-4-4-8-8-12 0-1-2-2-3-3-2-3-6-6-8-9l1-1-1-1h0z" class="N"></path><path d="M410 320l2 2v1l2 2h0 1c2 1 3 2 5 3 2 2 4 3 5 4 1 0 2 1 3 0-1 0-2-1-3-2v-1l5 4c0 1 5 5 6 5 1 1 1 1 1 2 1 1 2 2 3 2l6 5h-2-1c-1-1-2-1-3-1s-3-2-4-2v1c-2-1-4-2-5-2-2-1-2-1-3 0l-5-3-1 1-8-5c-1-1-3-3-3-4-1-1-1-1-1-2h0l-1-1h0 0l-1 1v-1h0c0-2-2-3-2-5l1 1v-3l1-1h0c1 0 1 0 2-1z" class="X"></path><path d="M425 329l5 4c0 1 5 5 6 5 1 1 1 1 1 2-4-2-8-5-12-8 1 0 2 1 3 0-1 0-2-1-3-2v-1z" class="D"></path><path d="M417 331h2l2 2 10 7-2-1h-1c-3-2-7-3-10-5h0v-2l-1-1z" class="e"></path><path d="M417 331h2l2 2h-1l-2-1-1-1z" class="d"></path><path d="M410 320l2 2v1l2 2h0c2 2 3 4 5 6h-2l1 1v2h0l-2-2-1 2c-2-1-3-2-5-4h0l-1-1h0 0l-1 1v-1h0c0-2-2-3-2-5l1 1v-3l1-1h0c1 0 1 0 2-1z" class="O"></path><path d="M415 330l2 1 1 1v2h0l-2-2c-1 0-2-1-3-1h3l-1-1z" class="l"></path><path d="M406 324l1 1 6 6c1 0 2 1 3 1l-1 2c-2-1-3-2-5-4h0l-1-1h0 0l-1 1v-1h0c0-2-2-3-2-5z" class="r"></path><defs><linearGradient id="Aq" x1="408.617" y1="323.869" x2="418.19" y2="328.004" xlink:href="#B"><stop offset="0" stop-color="#797a7c"></stop><stop offset="1" stop-color="#939293"></stop></linearGradient></defs><path fill="url(#Aq)" d="M410 320l2 2v1l2 2h0c2 2 3 4 5 6h-2l-2-1-7-9c1 0 1 0 2-1z"></path><path d="M410 330c2 2 3 3 5 4l1-2 2 2c3 2 7 3 10 5h1l2 1c2 1 4 2 5 4h0v1c-2-1-4-2-5-2-2-1-2-1-3 0l-5-3-1 1-8-5c-1-1-3-3-3-4-1-1-1-1-1-2z" class="m"></path><defs><linearGradient id="Ar" x1="414.801" y1="332.62" x2="420.55" y2="341.169" xlink:href="#B"><stop offset="0" stop-color="#c1bfc1"></stop><stop offset="1" stop-color="#e5e5e2"></stop></linearGradient></defs><path fill="url(#Ar)" d="M410 330c2 2 3 3 5 4l8 6-1 1-8-5c-1-1-3-3-3-4-1-1-1-1-1-2z"></path><path d="M342 533c0 1 0 1 1 1v3c1 0 2 1 3 1l-1 1c-2 2-3 4-4 6l2 1 4 3c1 1 2 1 2 3l-1 1 1 1h1s0-1 1-1v-1h0c0-1 1-2 1-3h1v1h0 1c0 1 0 1 1 1 2-2 2-6 2-9l2 8c1 1 3 2 4 4l2 9c2 5 4 11 7 15 2 3 3 5 5 7 2 3 4 5 6 8 9 9 20 12 32 16l16 3h0l13 1 10-1h10l1 18v6h-47-12-4-5c-4 0-10 0-14-1-2 0-3-2-5-3v1 1l-1 1c-3-1-5 1-8 1h-10c-7 0-13-1-19-1h-17-25l-7-2c-3 0-6-1-8-1h0-2l-1-1v1-1l-1-1c-1-1-2-1-3-1-2 0-3 0-5-1h-3 0c1 0 1-1 1-1 2-1 4-1 5-2v-2l-2-1h0c-1-1-2-1-3-2h-1l-2-1h-1c-1 1-2 1-3 1s-2 0-3-1l1-1c1 0 3-1 5-1h2c1-1 2 0 3-1h2l2-1c1 0 1 0 2-1h1c1-1 3 0 5 0 0-1 0-1 1-2 1 0 2-1 3-2 3-2 7-3 11-4 2 0 4 0 6-1 4-1 8-1 11 0h1l5 2h0 5c2-1 3-2 4-4v-1c0-1-1-3-2-4s-3-2-5-2l-6-2 1-1c-2-1-2-2-3-3h0c-1-1-1-1-2-1 0-1 0-2 1-3l2-3v-1h0v-2h-1c1-1 1-3 1-4 1-1 1-2 1-3s1-5 0-6v-3c-1 0-1-1-1-2h0c0-1 0-1-1-2 1 0 2-1 3-1v-1h-2-1c-1-3-3-4-5-6l1-1h1v-1h2l1-1h2c1 0 2-1 3-1 2-1 4-1 6-2 1 0 2 0 3-1 1 0 1-1 2-1h2 1c1-1 3-2 4-3s2-2 2-3l1-1 1-2c0-1 0-1 1-2z" class="t"></path><path d="M459 615c1 1 1 1 2 3l-1 3h0v-2-2l-1-1v-1z" class="Z"></path><path d="M322 553h0 1c-1 1-1 2-1 3h-1 0v1l-1 1-1-1c0-2 2-3 3-4z" class="C"></path><path d="M357 602c0-1 1-1 1-1v3l2 2c-1 0-3 1-4 0h1v-4z" class="P"></path><path d="M393 610l1-1c0-1 0-1 1-1 2-2 4-1 6-1l-8 3z" class="V"></path><path d="M336 557h1c1 0 2 2 3 3l-1 1h-1c-1 0-2-1-3-2 0-1 0-1 1-2z" class="e"></path><path d="M367 595v-1-4h2v2h1l-2 1c0 1-1 2 0 3s1 1 2 1c-1 1-1 1-2 1s-1-1-1-2c-1 0 0-1 0-1z" class="N"></path><path d="M405 610l1 1h1 2v2h0l4 6h-1c-1-2-3-4-5-6-1-1-1-2-3-3h1z" class="E"></path><path d="M375 593c1 0 2 2 3 3-1 0-1 0-2-1h0c-1 1-2 1-3 0h0c-2 1-3 2-4 2 1-1 2-3 4-3 1-1 1-1 2-1z" class="Z"></path><path d="M355 555c1-1 2-1 3-1v1c1 1 2 3 2 5l-6-1h0l1-1h3v-1-2h-1-1-1z" class="E"></path><path d="M328 629h5c2-1 4-1 6-1h7c-1 0-4 0-5 1l-1 1h-3l-9-1z" class="F"></path><path d="M425 629h2v1c0 1 1 1 1 2h0-2l-2 1h0v-1-1h-2l-1-1h0 1 2v-1h1z" class="Z"></path><path d="M351 605l5 1c1 1 3 0 4 0h0 2v2c-1 1-3 0-4 0s-1 0-2-1h0-2l-1-1h-2v-1z" class="H"></path><path d="M357 602v-1c0-1 3-3 5-4 1 1 2 3 2 4l-1 1v1h-1c-1-1-2-1-3-2h0 1 1v-1-1c-2 0-2 1-3 2 0 0-1 0-1 1z" class="e"></path><path d="M278 623h2c3-1 5-3 7-4l-3 4 1 1h-2v2h-2 0l-1-2h0l-2-1zm166-10l10-1-2 1 4 1c1 0 2 1 3 1v1h-1c-1 0-2 0-3-1h-2c-2 0-5 0-6-1s-2-1-3-1zm-79-30l2 1c0 1-1 2-1 2 0 2 0 4-1 6v-3-2l-1-1h0c-1 2-2 4-2 6 0 0 1 0 0 1l-1-1c1-1 1-3 1-4 0-2 1-2 1-4-2 0-3 1-4 2 1-1 1-2 2-2 1-1 2 0 3 0 0 0 1 0 1-1z" class="N"></path><path d="M401 607h1l3 3h-1c-1-1-2-1-4-1-3 1-5 1-8 3h0c-1 0-1 1-2 1l-1 1v-1l4-3 8-3z" class="Q"></path><path d="M288 626c1 1 1 1 2 1l1-1h-1v-1l2-2h1 1v2c0 1-1 2-2 3h5c-2 1-5 1-7 1l-2-2h0v-1z" class="N"></path><path d="M297 606c2 0 4 0 6-1 4-1 8-1 11 0h1c-1 1-2 1-3 1-1 1-5 1-7 0h-8z" class="P"></path><path d="M314 605h1c-1 1-2 1-3 1h-1c0-1-1-1-1-1h4z" class="X"></path><path d="M357 542l2 8h-2c0 1 0 2 1 3v1c-1 0-2 0-3 1-1-2-1-3-2-5h0 1c0 1 0 1 1 1 2-2 2-6 2-9z" class="V"></path><path d="M357 620c3 0 7-1 10 0h0c-2 2-4 3-6 3h-1c1 0 1 0 2-1 0-1 0-1-1-1l-1 1h-3c0 1-1 0-2 0h0 1c0-1 0-1 1-2z" class="O"></path><path d="M285 624h0 3v1h0v1 1h0l2 2c-2 0-5 0-7-1-1 0-1 0-2-1h-1l1-1h0 2v-2h2z" class="C"></path><path d="M285 624h0 3v1h0-1s-1 0-1 1h0-2c0-1 1-1 1-2h0z" class="N"></path><path d="M349 603l2 2v1h2l1 1h2 0c1 1 1 1 2 1-1 1-1 2-1 3h0v1c-2-1-4-1-5-3l-2-1v-2l-1-2v-1z" class="P"></path><path d="M349 603l2 2v1h2l1 1h2 0-1 0v1 1c-1 0-2 0-3-1 0-1 0-2-1-2h-1l-1-2v-1z" class="L"></path><path d="M364 629c2-1 4-1 6-2 2 0 3-2 5-2-1 2-3 5-5 6l-2 1h-4 0c1-1 1-1 1-2l-1-1z" class="d"></path><path d="M358 590l1 1c-1 1-1 2-2 3s-1 2-2 3h-1l-2 2c-1 1-3 1-5 3 1 0 1 0 2 1h0v1l-2-1c-1 0-1 0-1-1l2-2c2-1 4-2 6-4v-1c1 0 1-1 1-1h-1-1 0 0c0-1 0-2 1-3h0l1-1c1 1 2 1 3 1v-1z" class="V"></path><path d="M306 629c2 0 4 0 6 1h4-1c-1 1-3 0-4 1h3c-3 1-8 1-11 1-2 0-3 0-4-1h0v-1c2-1 4 0 6-1h1z" class="F"></path><path d="M368 582v1h-2-1c0 1-1 1-1 1-1 0-2-1-3 0-1 0-1 1-2 2l-2 2 1 2v1c-1 0-2 0-3-1l-1 1h0v-2-1h0c1-2 2-3 4-4h0c3-2 6-2 9-2h1z" class="L"></path><path d="M281 627c1 1 1 1 2 1 2 1 5 1 7 1s5 0 7-1c3 0 6 0 9 1h-1c-2 1-4 0-6 1v1h0c-1 0-3-1-4-1h-11s-2-1-3 0v-3z" class="q"></path><path d="M324 621h2c-1 1 0 1-1 2-2 2-11-1-14-1-3-1-8 0-10 2l-3 1c-1 0-1 0-2 1h0-1c1-1 2-2 3-2l4-2c3-1 8-2 11-1 2 0 5 1 8 1 2 0 2 0 3-1z" class="C"></path><path d="M345 569v-3c2-2 4-3 6-3 1-1 2-1 3-1 1 1 1 1 2 1v2h-3c-1 1-3 0-4 1v1l-1 1h0l-1 1h1c0 1 0 1-1 1h-2v-1z" class="X"></path><path d="M365 583c2 0 2 1 3 1l1 1c0 1 1 2 1 4h1l1-1c1 1 1 2 2 3v1l1 1c-1 0-1 0-2 1v-3c-2 0-2 1-3 1h-1v-2h-2v4 1c-1-1-1-2-2-3 1-2 1-4 1-6 0 0 1-1 1-2l-2-1z" class="C"></path><path d="M355 622c1 0 2 1 2 0h3l1-1c1 0 1 0 1 1-1 1-1 1-2 1h-2c0 1-1 1-2 1-3 1-7 0-10 1-6 0-12 0-17-2 0-1 0-1 1-1 4 1 9 2 13 1s8 0 12-1z" class="e"></path><path d="M415 609l16 3h0-1-2s-1 1-2 1h-1c-1 0-1 1-1 1h-3l-2 1c1-1 0-1 0-2-1 1-1 0-3 0l1-2h-1c-2-1-4 0-6 0v-1h4l1-1z" class="F"></path><path d="M417 611c2 1 4 1 6 2v1h-2l-2 1c1-1 0-1 0-2-1 1-1 0-3 0l1-2z" class="R"></path><path d="M410 611c2 0 4-1 6 0h1l-1 2c2 0 2 1 3 0 0 1 1 1 0 2s-4 0-4 1v1h-1c-1 1 0 1 0 2l-1 1v-1l-4-6h0v-2h-2-1 4z" class="Z"></path><path d="M410 611c2 0 4-1 6 0h1l-1 2h0c0 1-1 1-1 2h-1v-2-1c-1 1-1 1-2 1v-1c-1 1-2 1-3 1h0v-2h-2-1 4z" class="h"></path><path d="M287 613h1c4 1 8 0 11 0 9 1 17 3 26 4h0-5c-4 0-9-2-12-2-3 1-5 2-8 2l5-2c-3-2-8-1-12 0h-4c-1 0-1-1-2-2z" class="b"></path><path d="M356 563c2 0 3 0 5 1l-2-1v1h0v1h2c0 2 0 3 1 4h-1-1c-3-1-5-1-8 0h0-1-3-1l1-1h0l1-1v-1c1-1 3 0 4-1h3v-2z" class="L"></path><path d="M349 567h2 1v-1h5l1 1h-5l-2 1h-1-1-1l1-1z" class="F"></path><path d="M272 622l3 1h1 2l2 1h0l1 2-1 1h1v3l-1 1v1-1l-1-1c-1-1-2-1-3-1-2 0-3 0-5-1h-3 0c1 0 1-1 1-1 2-1 4-1 5-2v-2l-2-1h0z" class="M"></path><path d="M275 623h1 2l2 1h0l1 2-1 1-1-1-4-3z" class="j"></path><path d="M279 626c0-1 0-1 1-2l1 2-1 1-1-1zm-5-3c1 1 3 2 4 4-1 1-5 1-7 1h-3 0c1 0 1-1 1-1 2-1 4-1 5-2v-2z" class="O"></path><path d="M343 569c0 2-1 2-2 4-2 2-4 5-4 8 1-1 2-2 4-2 1-1 2-1 3-2v1c-2 1-6 4-7 7v1c0 1-1 1-1 2 0 5 3 11 7 15h0v1c-2-1-3-3-4-5-4-6-5-14-3-20 1-4 4-8 7-10z" class="d"></path><path d="M413 619v1l1-1c0-1-1-1 0-2h1v1c2 0 3 0 4-1h5c4 0 6 1 10 1h-3c-2 0-4-1-5 0 2 1 5 4 4 7 0 2-1 4-3 5v-1c1-1 2-4 2-6 0-1-2-3-3-4s-2-1-3-1-2 0-2 1c-1 1-1 3-1 4h1c0 1 0 2 1 3l3 3h-1 0c-2-2-3-3-4-6h0c0-1 0-3-1-4h-3c-1 1-2 3-2 5-1 2 1 3 2 5h0c-1-1-3-3-3-4-1-1-1-4-1-6h1z" class="O"></path><path d="M365 563c2 5 4 11 7 15 2 3 3 5 5 7 2 3 4 5 6 8l-2-1c-4-4-7-8-10-12l-3-6c-1-1-1-3-2-4h-3c0-2-1-4-1-5h2s1-1 1-2z" class="X"></path><path d="M400 609c2 0 3 0 4 1 2 1 2 2 3 3-5 0-12 0-17 2 0 0-1 0-1 1h-1c-1 0-3 2-4 3 0 1 0 1-1 2h0v-2c1-3 4-5 6-6v1l1-1c1 0 1-1 2-1h0c3-2 5-2 8-3z" class="O"></path><path d="M390 613c1 0 1-1 2-1h0c3-2 5-2 8-3v1c-1 2-3 2-5 2-1 1-3 1-4 1 0 1-1 0-1 0z" class="C"></path><path d="M324 621c-6-2-12-3-18-3h0c3 0 6-1 8 0 3 1 5 1 7 1 3 0 6 0 9 1 4 1 7 1 11 2h5c1-1 3-1 4-1l7-1c-1 1-1 1-1 2h-1 0c-4 1-8 0-12 1s-9 0-13-1c-1 0-1 0-1 1-1 0-2-1-3-2h-2z" class="L"></path><path d="M425 629l-3-3c-1-1-1-2-1-3h-1c0-1 0-3 1-4 0-1 1-1 2-1s2 0 3 1 3 3 3 4c0 2-1 5-2 6h-2z" class="t"></path><path d="M337 630h4c2 1 4 0 6 1 1 0 1 0 2-1h1 2l4-1c1 1 2 2 2 3h-3v-1 2c-1 0-1 0-2-1h-9c-10 0-20-3-30-1h-3c1-1 3 0 4-1h1c1 0 2-1 3-2 3 1 7 1 9 1l9 1z" class="m"></path><path d="M344 632h7c1-1 3-2 4-2v1 2c-1 0-1 0-2-1h-9z" class="F"></path><path d="M315 582c0-1 1-2 2-3h0v1h0v3c1 0 1 1 1 2h0 0v1 2h1v-2c1-1 1-1 1-2h1l1 1h1v2s-1 0 0 1v-1c1 1 1 2 1 3h1v2h-1-1l1-1-1-1-1 1h-2c-1 0-2 1-3 2-2-1-2-2-3-3h0c-1-1-1-1-2-1 0-1 0-2 1-3l2-3v-1h0z" class="N"></path><path d="M315 583v1l1-1h1c-1 1-1 2-2 3v1c-1-1-1-1-2-1l2-3z" class="P"></path><path d="M313 586c1 0 1 0 2 1 1 0 1 1 2 2h1v1c-2 0-3-1-4 0h0c-1-1-1-1-2-1 0-1 0-2 1-3z" class="C"></path><path d="M362 569c-1-1-1-2-1-4h-2v-1h0v-1l2 1 1 1c0 1 1 3 1 5 3 9 8 16 14 22 2 3 5 5 7 7h0-2s-1-1-2-1c0 0-1 2-2 2-2 3-5 5-9 7h0-1c0-1 0-1 1-1 3-1 7-4 9-7 1-1 1-2 0-3h0c-1-1-2-3-3-3l-1-1v-1c-1-1-1-2-2-3l-1 1h-1c0-2-1-3-1-4l-1-1c-1 0-1-1-3-1h1 2v-1l-1-1c-1-1-4-1-5-1-3 0-7 1-10 1h0l9-2c1-1 2-1 3-1h0 1c0-1-1-2-2-3h-1c1 0 1 0 1-1v-2c-2 0-5 1-6 1 1-1 3-1 5-2v-2z" class="n"></path><path d="M342 533c0 1 0 1 1 1v3c1 0 2 1 3 1l-1 1c-2 2-3 4-4 6l2 1 4 3c1 1 2 1 2 3l-1 1c-1-2-1-3-3-5-4-3-10-3-15-1-2 1-4 3-7 4h-12v-1h2l1-1h2c1 0 2-1 3-1 2-1 4-1 6-2 1 0 2 0 3-1 1 0 1-1 2-1h2 1c1-1 3-2 4-3s2-2 2-3l1-1 1-2c0-1 0-1 1-2z" class="O"></path><path d="M341 535c1 1 1 2 2 3h1 0v1h-1c-1-1-2-1-3-2l1-2z" class="Z"></path><path d="M342 533c0 1 0 1 1 1v3l1 1h-1c-1-1-1-2-2-3 0-1 0-1 1-2z" class="L"></path><path d="M339 538l2 1c1 0 1 1 1 2h0s0 1-1 1c0 1 0 1-1 1l-1 1h-1c-2 1-4 1-6 1h0c-3 1-5 4-8 4-2 1-4 1-6 1v-1c1 0 3 0 4-1s3-1 3-2c1 0 2 0 3-1 1 0 1-1 2-1h2 1c1-1 3-2 4-3s2-2 2-3z" class="V"></path><path d="M346 628c7 0 12-2 18-3l12-6c2 0 3-1 4-1v1l-2 2c0 1-3 4-3 4-2 0-3 2-5 2-2 1-4 1-6 2l1 1c0 1 0 1-1 2h0-2c-2 1-5 1-7 1v-2 1h3c0-1-1-2-2-3l-4 1h-2-1c-1 1-1 1-2 1-2-1-4 0-6-1h-4 3l1-1c1-1 4-1 5-1z" class="H"></path><path d="M378 621c0 1-3 4-3 4-2 0-3 2-5 2-2 1-4 1-6 2-2-1-3 0-4 0-2 0-2 0-3-1h0l18-6 1-1h2z" class="V"></path><path d="M343 569h1 1v1h2c1 0 1 0 1-1h3 1 0c3-1 5-1 8 0h1 1v2c-2 1-4 1-5 2 1 0 4-1 6-1v2c0 1 0 1-1 1h1c1 1 2 2 2 3h-1 0c-1 0-2 0-3 1l-9 2v-1c0-1 0-1-1-2h-1c0 1 0 1-1 2v-1h0v-2h-3l-2 1v-1c-1 1-2 1-3 2-2 0-3 1-4 2 0-3 2-6 4-8 1-2 2-2 2-4zm-20 18c1-1 1-1 2-1h1v-1-1c1 1 0 2 0 3l1 1s1 0 2-1c0 0 0-1 1-1v-1-3-1-3h1c0-1-1-1 0-2v-1h1v2c-1 1 0 5-1 7 0 2-1 6 0 8v1c1 1 1 1 1 2s0 2 1 3v1l1 2c1 2 4 5 6 7 1 0 1 0 2 1l1 1h1l2 2 3 1c1 1 3 1 4 1v1h0-1-2 0c-1-1 0-1-1-1h-3v-1c-1 0-2-1-4-1h1l-1 1-1-2c-1 1-1 0-2 1v1h1l1 1h3c0 1 0 1 1 1s2 0 3 1h0c1 0 1 0 2 1v1h0l-2 2h0c-1 1-8 1-10 0h-1-1c-1 0-2-1-3-1h1v-1h1 1c0-1 0 0 1-1-1 0-2 1-3 1s-3 0-5-1v-1-1c0-1 1-2 2-3s1-1 1-2l-2-1c0-1-1-1-2-1v1h-2-2 0-1v-1h3c-3-1-4 0-6-1h5c2-1 3-2 4-4v-1c0-1-1-3-2-4s-3-2-5-2l-6-2 1-1c1-1 2-2 3-2h2l1-1 1 1-1 1h1 1v-2h-1c0-1 0-2-1-3z" class="Z"></path><path d="M327 598c1 1 2 1 3 2 0 0 1 1 1 2s1 1 2 1v1 1c-1 0-2-1-3-1l-1-1v-1c0-1-1-3-2-4z" class="X"></path><path d="M331 612c1 1 1 2 0 3 1 1 1 1 2 1s2 0 2-1c1 0 1 0 1-1l1 1c-1 1-1 1-1 2h-5l-1-1h0-1v-1c0-1 1-2 2-3z" class="O"></path><path d="M350 617v1h0l-2 2h-9c-1 0-4-1-4-1v-1c1 0 2 0 3-1h0 1c0 1 0 1 1 2 1 0 2 0 3-1h2 3l2-1z" class="P"></path><path d="M341 614h3c0 1 0 1 1 1s2 0 3 1h0c1 0 1 0 2 1l-2 1h-3-2c-1 1-2 1-3 1-1-1-1-1-1-2h-1 0l3-1c1-1 0-1 0-2z" class="X"></path></svg>