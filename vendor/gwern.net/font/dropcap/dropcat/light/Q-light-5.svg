<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="50 38 432 632"><!--oldViewBox="0 0 536 752"--><style>.B{fill:#010101}.C{fill:#333}.D{fill:#464646}.E{fill:#585858}.F{fill:#2a292a}.G{fill:#626162}.H{fill:#3d3c3d}.I{fill:#181718}.J{fill:#0f0f0f}.K{fill:#535252}.L{fill:#4d4c4d}.M{fill:#222}.N{fill:#1c1b1c}.O{fill:#2f2f2f}.P{fill:#656565}.Q{fill:#717071}.R{fill:#7b7a7b}.S{fill:#898788}.T{fill:#eeecef}.U{fill:#c2c3c3}</style><path d="M258 502h-1c-1-1-1-1-1-2 0-2 0-3 2-5v7z" class="E"></path><path d="M79 578c1 1 1 1 1 2 1 0 1 1 1 1 0 1-1 3-2 4v-2l-1-1h0l1-1v-3z" class="H"></path><path d="M208 531l1-6 2 2c-2 1 1 4-3 4z" class="I"></path><path d="M250 611l-4-4 4 1c1 0 2 1 2 1 1 0 1 1 1 1h-3v1z" class="L"></path><path d="M229 292l4 8c-1-1-3-3-5-4h-1 1c0-2 0-3 1-4z" class="C"></path><path d="M261 427h-8l6-3 2 2h1c-1 0-1 0-1 1z" class="G"></path><path d="M81 624v3h1v-1h1l-1 9-2-4c1-2 1-5 1-7z" class="L"></path><path d="M261 616h0c1-1 1-1 1-2 1 0 1 0 1 1 1 3 1 5 1 8 0-1 0-2-1-3 0-1-1-2-2-2v-2z" class="K"></path><path d="M185 450c2 3 3 5 4 8l-4-2h1c-1-2-2-3-3-4h0c0-1 1 0 1-1l1-1z" class="D"></path><path d="M259 424c3 0 6 0 10 2-1 0-2 1-2 1-2 0-4-1-5-1h-1l-2-2z" class="Q"></path><path d="M67 560l1 3v3 2c-1 0-3-4-3-6v-1h1l1-1z" class="M"></path><path d="M254 574c4 1 7 3 9 7h0c-1 0-1 0-2-1-2-2-5-3-7-6z" class="K"></path><path d="M226 557c2-1 4-5 5-6 1 2 2 3 2 4l-2 1c-1 1-3 1-5 1z" class="Q"></path><path d="M214 273h5c1 0 3 1 3 2v1l-10-1h0c1 0 2 0 3-1l-1-1z" class="C"></path><path d="M150 634c2 1 4 1 5 2 2 1 3 1 4 1 2 1 3 2 4 3l-7-1 1-1-1-1h-2c-2-1-3-2-4-3z" class="D"></path><path d="M278 398c-2-2-4-5-4-7-1-2-1-3 0-4h1v1c0 4 2 6 3 10z" class="G"></path><path d="M299 507l6-3c-2 3-4 5-6 6-1-1-2-1-3-1 0-1 0-1 1-1h0l2-1z" class="F"></path><path d="M218 647v-4l2-2h3c2 1 3 2 4 4l-2-1c-1 0-1 0-2-1h0c-1 0-2 0-3 1h-1l-1 3zm42-157c0-1-1-2-1-3v-1c4-1 6-1 9 0-2 1-3 1-4 1-1 1-3 2-4 3z" class="P"></path><path d="M199 547v-1c1-3 2-5 4-7 2 0 2 0 3 1l-3 3c-1 1-2 3-4 4z" class="K"></path><path d="M305 661c1 0 3-1 5-2-2 2-3 5-5 7-1 0-2-1-2-1 0-1 1-1 1-2h-1 0c0-1 1-2 2-2z" class="P"></path><path d="M48 471h0c2 2 1 6 1 9 0 1-1 2-2 3v-8c1-2 1-3 1-4z" class="S"></path><path d="M269 426c4 2 6 4 9 7l-4-2c-2-1-5-3-7-4 0 0 1-1 2-1z" class="K"></path><path d="M238 665h0c2-2 4-2 6-2h0c2 0 4 0 5 1h1c-3 2-9 1-12 1z" class="D"></path><path d="M244 524l1 1c1 3 3 8 3 10-2-3-4-7-6-10 1-1 1-1 2-1zm-79 82c5 1 9 3 12 7h-1c-2-1-3-1-4-2l-6-3c-1-1-1-2-1-2z" class="L"></path><path d="M48 497c-4-3-5-5-6-9 1 2 3 3 5 4 0 1 2 2 2 2l-1 1v2z" class="D"></path><path d="M47 452c1 1 3 2 4 3 0 1 0 2 1 3h1c1 0 1 0 1 1h0 0c0 1-1 1-1 1v1l-2-1c-2-1-3-6-4-8z" class="R"></path><path d="M240 558c2-4 4-6 9-8h3s-2 0-2 1c-1 0-1 1-2 1-2 2-4 3-6 5-1 1-1 1-2 1z" class="C"></path><path d="M250 608c4 0 7 1 9 4 1 1 2 2 2 3h-2l-3-4c-1-1-2-1-3-1 0 0 0-1-1-1 0 0-1-1-2-1z" class="E"></path><path d="M244 451l1 1v-1l1 2c1 2 1 4 3 5 1 2 3 3 3 4-1 0-3-1-4-2-3-2-4-5-4-9z" class="L"></path><defs><linearGradient id="A" x1="78.24" y1="612.769" x2="73.749" y2="615.474" xlink:href="#B"><stop offset="0" stop-color="#616061"></stop><stop offset="1" stop-color="#7a797b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M75 611h0c1-1 1-1 1-2h0c0 2 1 3 1 4l1-2v1c0 3-1 6 0 10l-1 1c-1-4-2-8-2-12z"></path><path d="M259 548c2-3 5-4 8-5 0 1-1 2-1 3v1h0c-1 1-4 2-6 2 0-1-1-1-1-1z" class="M"></path><path d="M266 440h2c2 1 6-1 8 1-2 0-5 0-7 1l1 1c3 1 5 1 7 1h-7c-2-1-3-1-5-1h0l-1-1 2-2zm10-70c-4-2-7-5-10-7h-1l8 1c1 1 2 1 3 2h-1l-1 1c0 1 0 1 1 1 0 0 0 1 1 2z" class="E"></path><path d="M273 364c5 0 11 3 15 7h0-2c-1 0-2-1-3-2l-7-3c-1-1-2-1-3-2z" class="R"></path><path d="M212 276c5 1 10 2 14 6-2 0-5-2-7-1l-2-2c-1-1-2-2-4-2l-1-1z" class="D"></path><path d="M46 471c-1 1-3 1-5 1 3-3 7-6 10-8v2s-1 0-1 1h2 1v1c-2 0-3 0-5 1l-2 2z" class="S"></path><path d="M303 466c3-2 6-3 10-3 2 0 4 1 6 2v1h-2c-1-1-2 0-4-1h-5c-1 0-1 1-2 1h-3z" class="L"></path><path d="M243 439c1 0 1-1 2-1 2-2 3-4 6-5l-6 9h0-1l-3 2c0-2 1-3 2-5z" class="E"></path><path d="M182 652c3 0 6 1 8 0h7 0c0 1-1 2-2 3h0l-1-1c-2 1-4 1-6 1l-3-1-1-1-2-1z" class="G"></path><path d="M146 630c5 2 9 4 13 7-1 0-2 0-4-1-1-1-3-1-5-2l-7-3 3-1z" class="E"></path><path d="M275 387c5 1 11 5 14 9h-1c-1 0-2-1-3-2-2-2-6-3-8-5 0 0-1 0-2-1v-1z" class="P"></path><path d="M172 611c1 1 2 1 4 2h1c3 3 4 6 5 10l-3-3h-1c-2-4-4-6-6-9z" class="D"></path><path d="M290 656c0-3 2-6 3-7s1-2 2-2c2 2 3 3 3 6-2-1-3-1-5 0l-1 1-2 2z" class="K"></path><defs><linearGradient id="C" x1="270.285" y1="427.99" x2="264.194" y2="430.627" xlink:href="#B"><stop offset="0" stop-color="#161716"></stop><stop offset="1" stop-color="#2f2c2f"></stop></linearGradient></defs><path fill="url(#C)" d="M261 427c0-1 0-1 1-1s3 1 5 1c2 1 5 3 7 4-2 1-4 1-6 0h0 0l-1-1h-4v-1h1v-1h0l-3-1h0z"></path><path d="M234 402c0-1 0-3-1-5 0-4 1-9 4-12 0 0 1 1 0 2 0 2-1 5-2 7v6c0 1-1 1-1 2z" class="R"></path><path d="M303 658h1l1-2v4 1c-1 0-2 1-2 2h0 1c0 1-1 1-1 2 0 0 1 1 2 1l-2 2c-2-1-2 0-4-1l1-3c1-1 2-2 2-4v-1l1-1z" class="C"></path><defs><linearGradient id="D" x1="133.042" y1="628.475" x2="144.452" y2="627.677" xlink:href="#B"><stop offset="0" stop-color="#4c4a4c"></stop><stop offset="1" stop-color="#686866"></stop></linearGradient></defs><path fill="url(#D)" d="M132 627v-1-1h-3 7c1 2 2 2 3 3l7 2-3 1-11-4z"></path><path d="M265 493c1 1 1 2 1 3v1c-1-1-2-1-4-1-1 1-2 2-2 3v3c-1 1-1 1-2 0v-7c1 0 2-1 3-1s3 0 4-1z" class="F"></path><defs><linearGradient id="E" x1="245.205" y1="518.305" x2="240.388" y2="515.13" xlink:href="#B"><stop offset="0" stop-color="#504e4f"></stop><stop offset="1" stop-color="#6a686a"></stop></linearGradient></defs><path fill="url(#E)" d="M244 524c-1-1-2-2-2-3-1-2-1-8 0-10 1 0 2 1 3 2 0 1-1 2-1 3 1 3 2 7 1 9l-1-1z"></path><path d="M294 378c0-1-1-3-1-3-1-2-2-4-2-6 4 3 7 8 9 13-1 0-2-1-2-1l-2-2-1-1h0v2h0 0c-1 0-1-1-1-2z" class="E"></path><path d="M280 439c1 0 2 0 2 1h0-3c0 1 1 1 1 2 1 0 1 1 1 2h-4c-2 0-4 0-7-1l-1-1c2-1 5-1 7-1 0 0 1 0 1-1 1 0 1 0 1-1h2 0z" class="M"></path><path d="M261 427l3 1c-1 1-2 0-3 1s-4 1-5 2c-2 0-4 1-5 2-3 1-4 3-6 5-1 0-1 1-2 1v-1c4-7 11-8 18-10v-1z" class="G"></path><path d="M44 449c-4-3-6-6-8-10l3 3c2 2 4 2 6 3h3 3c-1 1-5 3-6 3l-1 1z" class="R"></path><defs><linearGradient id="F" x1="281.504" y1="395.309" x2="276.89" y2="394.879" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#555355"></stop></linearGradient></defs><path fill="url(#F)" d="M275 388c1 1 2 1 2 1 1 3 3 5 5 7 0 1 1 2 1 2v2 1c-2-1-4-2-5-3-1-4-3-6-3-10z"></path><defs><linearGradient id="G" x1="306.281" y1="413.65" x2="309.24" y2="406.625" xlink:href="#B"><stop offset="0" stop-color="#232324"></stop><stop offset="1" stop-color="#3e3c3e"></stop></linearGradient></defs><path fill="url(#G)" d="M306 409h0c2-2 1-5 2-7 1 5 2 11 0 17v-2h0v-1-1l-1 1-1-1-1 1v-5-1s1 0 1-1z"></path><path d="M83 618s0 1 1 2v-1c1 1 0 5 0 6s1 1 1 2v2h1c0 1 0 1 1 1v2 1 1c1 2 3 6 2 8-2-3-3-6-4-9-1-2-1-5-2-7h0-1l1-8z" class="G"></path><defs><linearGradient id="H" x1="201.907" y1="175.714" x2="205.53" y2="167.614" xlink:href="#B"><stop offset="0" stop-color="#1d1d1c"></stop><stop offset="1" stop-color="#4b4b4c"></stop></linearGradient></defs><path fill="url(#H)" d="M206 165l4-4-6 14c-1 2-2 5-3 7v-2c0-1 1-1 1-2h0v-1h0v-1c-2 1-2 3-3 4h-1l2-4 6-9v-2z"></path><path d="M247 573c3 0 5 0 7 1 2 3 5 4 7 6 1 1 1 1 2 1 2 4 3 7 2 11h0s-1 0-1-1c0-2-2-4-3-6h1v-1c0-3-4-5-6-7s-6-3-8-4h-1z" class="C"></path><path d="M290 493l1 1c1 0 1-1 2-1v-1h0c0 4-2 6-3 9-2 3-4 7-7 10 0-3 2-5 3-7 0-2 2-6 3-8v-2l1-1z" class="J"></path><path d="M312 471l1 1c0 1 0 1-1 2-3 1-7 1-9 4-2 1-3 3-4 4h0v-1-1c1-2 4-7 6-8h3c1-1 3-1 4-1z" class="F"></path><path d="M94 667v1c-2 1-5 2-7 1-2 0-3-2-4-3-1-2-1-4 0-6 0-2 1-2 2-3h5c1 1 1 2 2 3l-1 1-2-2s-1-1-2-1l-3 3v3c1 2 2 3 4 3 2 1 3 0 5 0h1z" class="B"></path><path d="M271 435l4 1c2 0 4 2 5 3h0-2c0 1 0 1-1 1 0 1-1 1-1 1-2-2-6 0-8-1h-2c1-2 3-3 5-4v-1z" class="F"></path><path d="M271 435l4 1h0 0c-3 1-5 3-7 4h-2c1-2 3-3 5-4v-1z" class="G"></path><defs><linearGradient id="I" x1="301.475" y1="404.103" x2="307.012" y2="399.119" xlink:href="#B"><stop offset="0" stop-color="#1e2121"></stop><stop offset="1" stop-color="#3a3436"></stop></linearGradient></defs><path fill="url(#I)" d="M303 396v4l2-10c1 4 1 7 1 10 1 3 0 6 0 9 0 1-1 1-1 1v-1l-1-1c-3-3-2-9-2-12h1z"></path><path d="M261 618c1 0 2 1 2 2 1 1 1 2 1 3l-1 6 3-2h0c1 4-1 7-3 9l-1 1c0-1 0-2 1-3v-2l-1 1h-1v-2-1c-1-2 1-5 1-7v-2c-1-1-1-2-1-3z" class="D"></path><path d="M227 648c4-7 14-8 17-16 0-1 0-3 1-5 1 2 0 5-1 7 0 1-1 2-2 3l-1 1h0 1c-1 3-6 5-9 7-1 1-3 2-4 4h0c0-1-1-1-2-1z" class="Q"></path><defs><linearGradient id="J" x1="286.23" y1="511.199" x2="297.622" y2="504.977" xlink:href="#B"><stop offset="0" stop-color="#181719"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#J)" d="M284 512l3-3c3-3 6-5 11-5 0 1 0 2 1 3l-2 1h0c-1 0-1 0-1 1-4 1-8 3-12 3z"></path><path d="M303 466h3c1 0 1-1 2-1h5c2 1 3 0 4 1-1 1-2 1-3 2s-2 1-3 3h1c-1 0-3 0-4 1h-3c1-1 1-2 2-3 2-1 5-1 7-3h0c-2 0-8 1-10 3-1 1-2 2-4 3h0l3-6z" class="H"></path><path d="M287 436c-2-2-2-5-3-7 0-3 0-8 2-10 1-1 1-1 2-1 3 0 4 2 6 4-2 0-4 0-5 1s-2 3-2 5 1 4 1 5-1 2-1 3z" class="G"></path><path d="M239 616c1 0 3 0 4 1 3 1 4 5 5 8 1 6 0 10-4 16h0c2-4 3-8 3-12 0-3-1-6-2-8 0-1-1-1-2-2h-1-2c-2 1-3 2-3 3-1 1-1 2-1 3 1 2 1 2 3 3h0 0-1c-1 0-2-1-3-2-1-2-1-4 0-6 0-2 2-3 4-4z" class="J"></path><path d="M300 624c4 1 6 3 8 7 4 6 3 15 1 22-1 2-2 5-4 7v-4c1-1 2-3 3-5h0l1-2c1-6 1-16-4-20-1-2-3-2-4-4l-1-1z" class="E"></path><path d="M277 389c2 2 6 3 8 5h-1c-1 0-1 0-2-1s-2-1-3-2c0 1 2 4 3 5s2 2 3 2v1c1 2 2 1 2 4h0c1 3 5 5 5 9v2h-1 0-1v-1c-2-2-7-7-7-10v-1-1-1-2s-1-1-1-2c-2-2-4-4-5-7z" class="N"></path><path d="M279 471l-2-3c-2-4-3-12-1-16 0-2 2-4 4-4 3-1 5-1 8 0-3 1-5 1-7 2h-1c-1 1-2 3-3 5 1 3 1 5 2 8v4 4z" class="E"></path><defs><linearGradient id="K" x1="179.816" y1="690.623" x2="184.003" y2="684.861" xlink:href="#B"><stop offset="0" stop-color="#1a181a"></stop><stop offset="1" stop-color="#3c3c3b"></stop></linearGradient></defs><path fill="url(#K)" d="M168 681c5 2 10 3 14 5l11 2c-6 1-13 1-19 1h0c0-1 0-1-1-2h0c-2-2-5-4-5-6z"></path><path d="M287 436c0-1 1-2 1-3s-1-3-1-5 1-4 2-5 3-1 5-1c1 1 2 2 2 3-1 2-1 2-1 4-2-1-4-3-5-5-1 3-1 5-1 8v2c0 1 0 2 1 2 0 2 1 4 2 5 1 3 2 4 2 7-3-3-5-8-7-12z" class="O"></path><path d="M290 656l2-2 1-1c2-1 3-1 5 0-2 6-6 10-11 13-1 1-2 2-3 2 1-1 2-3 3-4 1-2 2-6 3-8z" class="B"></path><path d="M190 246h2c1 1 2 2 4 3 3 3 7 6 11 8l4 1c-2 1-4 1-6 1h-4l3-1v-1l-5-1c-3-1-5-3-7-5l-6-4h-2c2-1 3-1 5 0l1-1z" class="H"></path><path d="M186 247c2 0 3 0 5 1l4 4c2 1 3 2 4 4-3-1-5-3-7-5l-6-4z" class="C"></path><path d="M264 428h0v1h-1v1h4l1 1h0 0c-6 0-12 1-16 6-4 4-7 9-6 15v1l-1-2v1l-1-1c-1-3 0-6 1-9h0l6-9c1-1 3-2 5-2 1-1 4-1 5-2s2 0 3-1z" class="H"></path><path d="M263 661h5c1 1 2 2 2 3s0 3-1 5c0 1-3 3-4 4 0 0-1-1-2-1-2-1-4-2-6-2l-9 3-12 3c1-1 2-1 4-2 1 0 2-1 3-2 1 0 2 0 3-1l8-2c4-1 7 0 11 1h2c1-1 2-3 2-5-1-1-1-2-2-3h-4c-7 1-12 7-18 9 6-4 11-8 18-10z" class="B"></path><path d="M303 451h0 0c1-1 2-3 3-4h0c1-1 2-1 2-1h1 1l2 2h0l-1 1v-1c-1-1-1-1-2-1-2 0-3 1-3 2-2 3-3 6-4 9v2 1c-1 5-3 9-5 13 0 1-1 2-2 3 1-8 2-13 0-20v-2l2 5c1 1 1 2 1 3h1c1-4 2-8 4-12z" class="U"></path><defs><linearGradient id="L" x1="88.254" y1="646.644" x2="101.31" y2="651.932" xlink:href="#B"><stop offset="0" stop-color="#090a0c"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#L)" d="M93 632h1v1l1 1h1 0c0 1 0 2 1 3h0c1 8 5 21 0 28-1 1-2 2-3 2h-1v-1c2-1 2-2 3-4 0-2-1-5 0-7h0v-2 1 1l1-2s0-1-1-1v-1c1 0 1-1 1-2v-1l-3-11c0-2-1-3-1-5z"></path><path d="M250 611v-1h3c1 0 2 0 3 1l3 4h2v1 2c0 1 0 2 1 3v2c-1-1-1-2-2-3h-1c-1-1-3-2-3-3v-1c-1 1-1 2-1 3-1 2 0 5 0 8 0 1 0 2-1 3 0-1 1-3 0-3 0-2-2-3-2-4-1 0-1-2-1-2 0-2 1-6 0-8 0-1-1-2-1-2z" class="O"></path><path d="M253 610c1 0 2 0 3 1v1c0 1 0 2 1 3h-1l-3-5z" class="D"></path><path d="M191 453c1 0 2 0 3 1 2 5 4 10 6 16 1 4 3 9 4 13 2 5 2 10 4 15-1 2-1 3-1 4l-4-18c-2-4-3-9-5-13-1-4-2-8-4-12l-3-6z" class="F"></path><path d="M251 368c6-1 12-2 17 0h-2-1-2 0c-1 1-2 1-3 1h-5v1h4l-1 1-2 1c-3 1-6 1-9 2h-1c-1 0-5 1-6 2l-1-1 1-1c4-3 6-5 11-6z" class="P"></path><path d="M240 374c4-3 6-5 11-6v1c-1 1-3 1-4 2s-3 2-5 3h-2z" class="S"></path><path d="M233 311c4 5 4 18 4 24-1 4-2 8-3 13 0-3-1-6-2-8h1v-1l1-12c-1-2-1-4-1-7v-3c0-2-1-4 0-6zm12 202v-4c2-1 1 0 3 0 1 0 3-2 4-2l1 1v1h2 0 0c-2 2-2 4-3 5-1 4-1 7 0 10-1 0-1 0-1-1-1-1 0-1-1-2l-1-1v2 2 1h0l-1-1-3-7-1-1c0-1 1-2 1-3z" class="L"></path><path d="M248 524c1-3-1-6 0-8 0-2 0-2 1-3l2 2v-1h1c-1 4-1 7 0 10-1 0-1 0-1-1-1-1 0-1-1-2l-1-1v2 2 1h0l-1-1z" class="N"></path><path d="M245 513v-4c2-1 1 0 3 0 1 0 3-2 4-2l1 1v1l-1 1h0l-1-2c-2 1-3 3-3 6 0 0 0 1-1 1v4l-1-1-1-1-1-1c0-1 1-2 1-3z" class="E"></path><path d="M279 497c0-3 0-7 1-9 1-1 2-3 3-3 1 2 3 4 3 7 0 1 0 2 1 4 0-1 1-1 1-2h1 0v2c-1 2-3 6-3 8-1 2-3 4-3 7l-2 2c0-3 2-5 3-8l1-2c-1-1-1-3-1-5-1-2-1-3-1-5v-1-2h0c0 1-1 3-1 4-1 2-1 3-3 4h-1s0-1 1-1z" class="C"></path><path d="M279 497c0-1 0-2 1-3 1-2 2-4 2-6h1c1 1 1 2 1 3v2c1 1 1 1 2 0v7s-1 0-1 1v1 1c-1-1-1-3-1-5-1-2-1-3-1-5v-1-2h0c0 1-1 3-1 4-1 2-1 3-3 4h-1s0-1 1-1z" class="J"></path><path d="M161 610h4c2 1 6 3 7 5 1 1 1 1 1 2 1 1 2 2 2 3l1 1h-1c1 1 1 1 1 2l1 1c1 2 1 3 0 5-1 1-3 3-5 3-1 1-3 1-4 0-1 0-2-1-2-2h0l1 1c1 0 1 1 2 1s2-1 3-2c1-2 0-4 0-7-2-5-6-10-11-13z" class="M"></path><path d="M288 448l5 5c-4-1-7-3-11-1-1 1-2 2-2 3-1 2 0 5 0 7 1 2 0 4 1 6 1 5 4 10 6 14 1 1 1 3 1 5v4c-1-2-1-3-1-5-2-5-5-10-8-15v-4-4c-1-3-1-5-2-8 1-2 2-4 3-5h1c2-1 4-1 7-2z" class="F"></path><path d="M244 516l1 1 3 7 1 1h0v-1-2-2l1 1c1 1 0 1 1 2 0 1 0 1 1 1 1 8 1 15-2 22 0-4-1-7-2-11 0-2-2-7-3-10 1-2 0-6-1-9z" class="B"></path><defs><linearGradient id="M" x1="275.532" y1="671.758" x2="272.889" y2="664.264" xlink:href="#B"><stop offset="0" stop-color="#09090a"></stop><stop offset="1" stop-color="#444242"></stop></linearGradient></defs><path fill="url(#M)" d="M284 660h0 1c-1 3-2 5-4 7-5 6-16 11-24 11h-1 1c6-1 11-5 15-9s6-7 12-9z"></path><path d="M300 472h0c2-1 3-2 4-3 2-2 8-3 10-3h0c-2 2-5 2-7 3-1 1-1 2-2 3-2 1-5 6-6 8v1 1h0c-1 2-4 10-6 10h0v1c-1 0-1 1-2 1l-1-1c1-4 3-7 4-10 2-4 3-7 6-11z" class="N"></path><defs><linearGradient id="N" x1="162.285" y1="604.307" x2="167.244" y2="625.227" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242222"></stop></linearGradient></defs><path fill="url(#N)" d="M162 605l3 1s0 1 1 2l6 3c2 3 4 5 6 9 1 3 2 5 2 8-1-2-2-4-4-5 0-1 0-1-1-2h1l-1-1c0-1-1-2-2-3 0-1 0-1-1-2-1-2-5-4-7-5h-4c-5-2-9-2-15-2v-1c5-1 10-2 16-2z"></path><path d="M195 258h-1c1-1 6 0 7 1h4v1c3 1 6 3 8 5l4 4-6-2c3 2 5 4 8 6h-5-2c-2-2-5-3-7-4l-1-2v-1c-2-2-5-3-8-5h1c1 0 2 1 3 1l1-2c-1-1-4-1-5-2h-1z" class="D"></path><path d="M201 260l2 2c2 2 4 3 7 5 1 0 1 0 2 1l-1 1c-2 0-5-2-7-3-2-2-5-3-8-5h1c1 0 2 1 3 1l1-2z" class="M"></path><path d="M195 258h-1c1-1 6 0 7 1h4v1c3 1 6 3 8 5h-2l1 1c-1 0-2 0-2 1-3-2-5-3-7-5l-2-2c-1-1-4-1-5-2h-1z" class="G"></path><path d="M203 262c3 0 5 1 8 3l1 1c-1 0-2 0-2 1-3-2-5-3-7-5z" class="H"></path><defs><linearGradient id="O" x1="299.851" y1="640.558" x2="308.29" y2="639.755" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#3d3c3c"></stop></linearGradient></defs><path fill="url(#O)" d="M293 624c2-1 5-1 7 0l1 1c1 2 3 2 4 4 5 4 5 14 4 20l-1 2h0c-1 2-2 4-3 5l-1 2h-1c1-1 1-1 1-2 1-1 1-3 1-5 1-1 1-2 1-3s1-3 0-5v-2c-1 1-1 1-2 1v-4h0c-1-2-2-6-4-8 0-1-2-2-2-3v-1l-2-2h-3v1-1z"></path><path d="M214 593h5c-2 1-4 3-6 4-5 3-12 3-18 3 3 1 5 2 7 3 4 3 10 8 15 7 1 0 3 0 4-2 1-1 2-2 1-4 0-1-1-3-2-3-2-2-4-1-6 0v-1c1 0 2-1 3-1 2 0 4 1 5 2h0c1 1 2 3 3 4l-1 2c-1 2-3 4-5 5-7 2-16-7-22-9-4-2-9-2-13-4 3-1 5 0 8 0s8 0 12-1c3-1 7-3 10-5z" class="B"></path><path d="M78 388l1 1c1-1 2-1 3-1h0c0 2-1 2-1 3h-1l6-2v2c-4 1-7 4-9 8l-1 1v-3-1h-1c-1 1-3 3-4 5-1 1-2 4-4 5h0c-2 2-3 6-6 8 1-5 3-10 5-14 3-4 6-7 9-10l3-2z" class="S"></path><path d="M80 391l6-2v2c-4 1-7 4-9 8l-1 1v-3-1h-1c-1 1-3 3-4 5-1 1-2 4-4 5h0c2-4 4-9 8-11 1-2 4-3 5-4z" class="G"></path><defs><linearGradient id="P" x1="78.005" y1="560.823" x2="69.115" y2="563.326" xlink:href="#B"><stop offset="0" stop-color="#222323"></stop><stop offset="1" stop-color="#565456"></stop></linearGradient></defs><path fill="url(#P)" d="M70 558h0c0-1 0-2 1-3 1 0 1 1 1 1 0 1 1 1 1 1l1-1c0-2 1-3 3-4v4h1c-1 3-3 5-4 8 1 0 1 1 1 1v1c-1 1-1 3-1 4h1 0l1-1v1h0c-1 2-1 4-1 7 0 1 0 2 1 4l2 1 1 1v2c0 1 0 1-1 1-4-4-7-12-8-18h-2v-2c1-3 1-5 2-8z"></path><path d="M70 558v10h-2v-2c1-3 1-5 2-8z" class="T"></path><path d="M73 568c0-1 0-2 1-4h0c1 0 1 1 1 1v1c-1 1-1 3-1 4h1 0l1-1v1h0c-1 2-1 4-1 7l-1-1c-1-3-1-5-1-8z" class="D"></path><path d="M73 568c0-1 0-2 1-4h0c1 0 1 1 1 1v1c-1 1-1 3-1 4v1h-1v-3z" class="C"></path><path d="M290 414h1 0 1v-2c0-4-4-6-5-9h0c0-3-1-2-2-4v-1c-1 0-2-1-3-2s-3-4-3-5c1 1 2 1 3 2s1 1 2 1h1c1 1 2 2 3 2h1c5 8 9 17 9 26 0 2 1 4 0 6 0-2-1-4-1-6-2-3-5-5-7-8z" class="B"></path><path d="M67 406h0c2-1 3-4 4-5 1-2 3-4 4-5h1v1 3h0c-4 2-7 6-8 10v1l1-1h0c0 1-2 3-2 5-1 2-3 5-4 8-1 4-3 7-5 10v1c-1 1-3 2-4 2l-1-1c6-5 7-14 8-21 3-2 4-6 6-8z" class="R"></path><defs><linearGradient id="Q" x1="91.797" y1="597.039" x2="75.616" y2="597.056" xlink:href="#B"><stop offset="0" stop-color="#2b2b2c"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#Q)" d="M91 582h0l1-1v1c-1 1-1 1-1 2-2 3-5 6-7 9s-3 7-3 10l-3 8v1-1l-1 2c0-1-1-2-1-4h0c0 1 0 1-1 2h0c0-8 1-17 6-24l1 1s1-1 1-2h0 1s1-1 2-1v-1l1 1c1 0 2-2 4-3z"></path><path d="M128 163h3c1 1 2 3 3 4 3 4 7 8 11 12 2 1 3 2 5 3-2 0-5-1-7-1 2 1 4 3 7 4l-1 1h-3c-2-1-4-1-6-2-4-2-6-5-9-9 1-2 0-3-1-4-1-2-1-3-2-5h0v-3z" class="H"></path><defs><linearGradient id="R" x1="141.473" y1="177.321" x2="143.875" y2="186.169" xlink:href="#B"><stop offset="0" stop-color="#272629"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#R)" d="M140 184l-1-2c-1-2-3-4-4-6h0c2 2 4 2 6 4 1 0 1 0 2 1h0c2 1 4 3 7 4l-1 1h-3c-2-1-4-1-6-2z"></path><defs><linearGradient id="S" x1="65.819" y1="524.863" x2="53.218" y2="524.944" xlink:href="#B"><stop offset="0" stop-color="#4d4d4e"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#S)" d="M64 508l2-1h1l-1 1h0c0 1 0 1 1 1l-6 6-2 4h-1c0 1-1 3-1 4-2 4 0 8 0 12 1 1 1 2 1 4 0 1-1 2-1 3 0 0 0 1-1 2l-3-12v-6c0-2 0-5-1-7 0 0 0-1-1-1v-4c0-1 1-1 1-2 1-3 4-4 7-5h1l-4 4c2-1 5-3 8-3z"></path><path d="M58 519l-1-1c1-2 2-3 3-4 1 0 1 0 1 1l-2 4h-1z" class="E"></path><path d="M59 507h1l-4 4c2-1 5-3 8-3h1-1c-4 2-10 5-12 9h0l-1-3c0-1 1-1 1-2 1-3 4-4 7-5z" class="C"></path><path d="M96 634c-1-4-2-7-2-11 0-2-1-4-1-6 2 2 2 5 3 7 1 4 2 7 4 10 0 2 2 5 3 7 1 3 2 5 3 8 0 0 1 2 1 3 1 1 2 2 2 3 2 2 3 4 3 6l2 5c0 2 1 3 1 4h2v-2h1c0 2-1 5-1 8-3-7-8-12-11-18-4-6-7-13-8-20l-1-1h0 0c-1-1-1-2-1-3h0z" class="O"></path><path d="M107 652c1 1 2 2 2 3 2 2 3 4 3 6l2 5c-1 0-2-1-3-2 0-1-1-2-1-3-1-1-1-3-1-4-1-2-1-3-2-5z" class="M"></path><defs><linearGradient id="T" x1="215.476" y1="686.731" x2="222.895" y2="677.081" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#3c3b3c"></stop></linearGradient></defs><path fill="url(#T)" d="M202 675c1 1 3 1 4 2h3c2 0 4 1 6 1v-1c3 1 18 6 20 8-1 0 0 0 0 0l5 3c-7 0-16 1-22-2-3-1-8-4-12-5h0c-1-1-2-1-3-2 2 0 3 1 5 1-1-1-2-1-3-1l-1-1c-1-1-1-2-2-3z"></path><path d="M110 624v3c1 1 1 2 1 3h0v1 1 1c3 8 10 15 16 19l12 9h-2v-1c-3-2-5-1-8-1l1 1h0c0 1 2 2 3 3h0c-4-2-8-4-11-7 0 1 0 1-1 1-2-2-3-4-5-6l-4-8c0-1-1-3-2-4 0-1-1-2-1-3-1-1-3-5-2-6h0l1-1c0 1 0 2 1 3v-2h0c1-1 0-3 0-4l1-2z" class="O"></path><path d="M122 656c-2-1-3-3-4-5h1v-1h0 0c3 4 7 8 11 10 0 1 2 2 3 3h0c-4-2-8-4-11-7z" class="I"></path><defs><linearGradient id="U" x1="84.437" y1="584.958" x2="86.521" y2="629.422" xlink:href="#B"><stop offset="0" stop-color="#2a2929"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#U)" d="M93 583l1-2h1c0 2-1 3-1 4-2 4-5 8-7 12s-3 8-4 13v1h1c-1 2-1 5-1 7l-1 8v1h-1v-3c0 2 0 5-1 7-1-2-2-5-3-8l1-1c-1-4 0-7 0-10v-1l3-8c0-3 1-7 3-10s5-6 7-9l2-3v2z"></path><path d="M78 612v-1l1 1v4c-1 2-1 4-1 6h0 0c-1-4 0-7 0-10z" class="H"></path><path d="M83 610v1h1c-1 2-1 5-1 7l-1 8v1h-1v-3l2-14z" class="F"></path><path d="M93 581v2c-1 3-3 5-5 8-2 2-4 4-5 6s-1 5-2 7c0 2-2 5-2 8l-1-1 3-8c0-3 1-7 3-10s5-6 7-9l2-3z" class="J"></path><defs><linearGradient id="V" x1="299.288" y1="402.91" x2="279.989" y2="363.109" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#V)" d="M276 366l7 3c1 1 2 2 3 2h2c2 2 4 5 6 7 0 1 0 2 1 2h0 0v-2h0l1 1 2 2s1 1 2 1c1 5 3 9 3 14h-1c0 3-1 9 2 12l1 1v1 1c0-1 0-1-1-2h0c-1-1-2-2-2-3v-1-1c0-1-1-1-1-1l-1-1c0-5-2-9-4-14-1-1-2-2-2-3l-3-3c0-1-1-2-2-3h-1c0-1 0-1-1-1l1 1h-1c-1 0-1 0-1-1h0c-1-4-4-4-7-6 0-1-1-1-2-1 0-1-1-1-1-1-1-1-1-2-1-2-1 0-1 0-1-1l1-1h1z"></path><path d="M296 379l2 2s1 1 2 1c1 5 3 9 3 14h-1c-1-3-1-6-2-8 0-2-1-3-2-4 0-1 0-2-1-3s-1-1-1-2z" class="C"></path><defs><linearGradient id="W" x1="226.113" y1="576.303" x2="223.976" y2="557.531" xlink:href="#B"><stop offset="0" stop-color="#070608"></stop><stop offset="1" stop-color="#535252"></stop></linearGradient></defs><path fill="url(#W)" d="M226 557c2 0 4 0 5-1l2-1v2c0 1-1 2 0 4l1-2h0c1 2 0 6-1 8s-1 3-2 4h0c-1 0-1 2-2 2v-1l-6 3c-1 0-2 2-4 2 0 1-1 1-1 2h0l-1-1c1-1 2-4 1-5 0-2 0-4-1-6 2-4 6-7 9-10z"></path><path d="M233 561l1-2h0c1 2 0 6-1 8s-1 3-2 4h0l-1-1c0 1 0 1-1 2 0-1 1-2 1-2v-1c-2 1-4 3-6 3h1l-1-1h1l-1-1c1-1 1 0 2-1 0 0 1-1 2-1 2-2 3-4 4-6 1 0 1-1 1-1z" class="F"></path><path d="M57 535v-1c1-2 1-4 2-6 0 1 0 3 1 4v1c0 1 1 2 2 2h0 0c4 2 6 4 10 3 1 0 2 0 4-1-2 2-4 5-6 7-3 4-4 9-4 14l1 2-1 1h-1v1l-2-2c-3-5-5-10-7-16 1-1 1-2 1-2 0-1 1-2 1-3 0-2 0-3-1-4z" class="B"></path><path d="M57 535v-1c1-2 1-4 2-6 0 1 0 3 1 4v1c0 1 1 2 2 2h0v2c-1 1-1 3 0 4 0 7 2 13 3 20v1l-2-2c-3-5-5-10-7-16 1-1 1-2 1-2 0-1 1-2 1-3 0-2 0-3-1-4z" class="D"></path><path d="M58 539c1 3 4 8 3 12l1 1c0 2 1 2 1 4h0v2 2c-3-5-5-10-7-16 1-1 1-2 1-2 0-1 1-2 1-3z" class="E"></path><defs><linearGradient id="X" x1="233.153" y1="578.035" x2="240.79" y2="594.034" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#292829"></stop></linearGradient></defs><path fill="url(#X)" d="M209 589l21-8c6-3 10-7 17-8h1c2 1 6 2 8 4s6 4 6 7v1h-1c-1-1-3-3-5-3-1-1-3-1-4-1h0l-1 1c-2 2-7 2-9 3h-4l-4 1-25 4v-1z"></path><path d="M234 586c1-1 1-2 1-2 1-1 4-1 5-1l-2 2-4 1z" class="I"></path><path d="M240 583c4 0 8-3 12-2l-1 1c-2 2-7 2-9 3h-4l2-2z" class="C"></path><path d="M217 567c1 2 1 4 1 6 1 1 0 4-1 5l1 1h0c0-1 1-1 1-2 2 0 3-2 4-2l6-3v1c-4 5-10 10-16 13-2 1-5 2-7 3h3v1l-32 8 2-2h1c1-1 1-1 2-1 9-5 18-8 26-16 3-3 6-8 9-12z" class="B"></path><defs><linearGradient id="Y" x1="251.153" y1="534.85" x2="264.205" y2="535.965" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242324"></stop></linearGradient></defs><path fill="url(#Y)" d="M257 522h0c1-2 1-6 3-7 0 1 0 1 1 2h0c1 0 3-6 6-7h0c1 1 2 3 2 5l-1 3h1v-1h1v2c0 4-2 8-4 11-1 3-3 5-5 7-3 4-6 11-12 13v-1l3-6c2-7 4-14 5-21z"></path><path d="M268 516l1-1-1 3h1v-1h1v2c-3 2-4 4-6 7v1h-1c1-4 2-9 5-11z" class="L"></path><path d="M270 519c0 4-2 8-4 11-1-1-1-2-1-3l-1-1c2-3 3-5 6-7z" class="F"></path><path d="M257 522h0c1-2 1-6 3-7 0 1 0 1 1 2h0c1 0 3-6 6-7h0c1 1 2 3 2 5l-1 1-1-1c-2 2-3 9-6 10-1-2 3-5 1-7-2 1-2 4-3 5h0c-1-1-1-1-2-1z" class="D"></path><path d="M288 491v-4c0-2 0-4-1-5-2-4-5-9-6-14-1-2 0-4-1-6 0-2-1-5 0-7 0-1 1-2 2-3 4-2 7 0 11 1 0 1 1 2 2 4 2 7 1 12 0 20l-6 17h-1v-3zm-90-295h1c6 2 9 7 14 11 2 2 5 1 8 3 1 1 1 3 2 5l1-1c3-1 6-1 9-1 5 0 11 0 16 1 3 0 5 2 8 3h0c-3-1-7-3-11-3-1 0-3 0-5-1-6-1-13 1-19 3v5c2 0 4 0 7 1 3 0 6 2 9 3h1c3 1 7 3 10 5 1 1 2 1 2 2-1 0-2-1-3-2l-6-3c-6-3-13-6-20-5-1 1-1 3-2 5-2 4-6 5-10 7l-6 3c2-2 6-5 7-8v-6c-2-4-5-7-8-10-2-2-5-4-6-8-1-3 0-6 1-9zm-30 405c4-1 8-2 12-2 2 0 3 1 4 1 7 1 13 3 17 9 1 3 3 6 2 10h0v-1l-6-6c1 1 2 3 2 4 2 6 1 13-1 19l-1-6c0 2-1 6-3 8-1 0-2 1-3 0l2-9-3 5v-13c0 1-1 2-1 3-1-7-3-12-9-16-4-2-10-3-15-3-2-1-4-1-6-1l9-2h0z" class="B"></path><defs><linearGradient id="Z" x1="92.163" y1="547.478" x2="70.782" y2="540.504" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#Z)" d="M105 521h1l-1 1c-4 5-7 12-10 18-2 2-3 4-5 6-1 2-2 3-3 5l-12 14s0-1-1-1c1-3 3-5 4-8h-1v-4c-2 1-3 2-3 4l-1 1s-1 0-1-1c0 0 0-1-1-1-1 1-1 2-1 3h0c-1 3-1 5-2 8v-3l-1-3-1-2c0-5 1-10 4-14 2-2 4-5 6-7h1c-1-1-1-1-2-1h1v-1c4 0 6-2 9-3 2-1 4-1 6-1v-1l6-3c2-1 3-1 4-2s2-3 4-4z"></path><path d="M97 527c2-1 3-1 4-2l-1 3c-3 0-6 2-9 3v-1l6-3z" class="I"></path><path d="M105 521h1l-1 1c-4 5-7 12-10 18-2 2-3 4-5 6-1 2-2 3-3 5l-12 14s0-1-1-1c1-3 3-5 4-8h-1v-4-1c1 0 1-1 2-1h0c0-1-1-1-1-1l2-2 1-1c1 0 1-1 2-2h-2 0c1-1 2-1 3-1l6-3c1-1 4-2 5-3-2 0-3 1-5 2s-5 1-7 1c6-4 13-6 17-12l1-3c1-1 2-3 4-4z" class="B"></path><path d="M77 552v-1c1 0 1-1 2-1h0c0-1-1-1-1-1l2-2 1-1c1 0 1-1 2-2h-2 0c1-1 2-1 3-1v1c-1 2-2 3-5 4l1 1 1-1h0 1 0c-1 1-4 2-4 4 1 0 1 1 2 1 0 1-1 2-2 3h-1v-4z" class="N"></path><path d="M280 541h0c2-4 7-7 11-8v1c-5 3-8 10-11 14l-2 2c-2 0-3 1-5 2-1 1-2 1-4 2-3 1-6 1-9 2l-1-3h0c-2 2-1 4-2 6 0 1-1 2-2 3-4 6-11 9-17 13-7 3-14 7-21 9 2-2 5-3 8-5 7-5 11-13 15-21 1 0 1 0 2-1 2-2 4-3 6-5 1 0 1-1 2-1 0-1 2-1 2-1 1 2-3 5-2 7l2-1c1-4 4-6 7-8 0 0 1 0 1 1 2 0 5-1 6-2h0v-1c0-1 1-2 1-3 4-2 8-2 12-3h0l1 1z" class="B"></path><defs><linearGradient id="a" x1="267.476" y1="542.152" x2="276.233" y2="543.989" xlink:href="#B"><stop offset="0" stop-color="#353635"></stop><stop offset="1" stop-color="#575557"></stop></linearGradient></defs><path fill="url(#a)" d="M267 543c4-2 8-2 12-3h0l1 1c-1 2-2 4-4 6 1-2 2-3 2-5h-1c-1 1-3 2-5 2l-6 3h0v-1c0-1 1-2 1-3z"></path><defs><linearGradient id="b" x1="140.962" y1="686.046" x2="143.19" y2="645.029" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333132"></stop></linearGradient></defs><path fill="url(#b)" d="M127 670h0c-2-3-4-5-7-8-1-2-2-6-4-7-1 0-1-1-1-1s-1 0-1-1c0 0-1 0-1-1s-1-2-1-4c0 1 1 2 1 2 0-1 0-2-1-3v-4l2 5c1 1 1 2 2 2v1c2 2 3 4 5 6 1 0 1 0 1-1 3 3 7 5 11 7h0c-1-1-3-2-3-3h0l-1-1c3 0 5-1 8 1v1h2c4 2 7 5 10 8 6 5 12 9 19 12 0 2 3 4 5 6h0c1 1 1 1 1 2-4-1-8-1-12-2-3-1-7-4-10-5 2 2 5 4 8 6-2 0-4-1-6-1-4-2-7-4-11-6-5-4-11-7-16-11z"></path><defs><linearGradient id="c" x1="154.313" y1="536.743" x2="209.256" y2="501.403" xlink:href="#B"><stop offset="0" stop-color="#0f1010"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#c)" d="M179 474v-2h0l7 2s1 0 1 1h1c1 2 5 3 7 5 7 6 10 17 11 26 2 10 2 21-4 29-2 3-4 6-5 9-2 4-3 9-6 13l-1-1v1h-1-1c-1-2 0-6 0-8 0-1 0-2 1-2l-1-1-9 23h-1c-1 1-1 2-1 3h-1v-1c1 0 1 0 1-1-1 0-1 0-1 1l-1-1c1-5 3-9 5-13 1-4 2-7 3-10s3-6 4-9h1c0 2-1 2 0 4 0-1 1-2 1-3l7-13c1 1 1 1 1 3l1-2c3-5 3-10 4-16 0-3 0-6-1-9 0-2-1-6-3-8-4-6-7-12-12-16-2-1-5-3-7-4z"></path><path d="M206 506c2 10 2 21-4 29-1-2 1-5 1-8 1-6 1-12 2-19v1c0-1 1-2 1-3z" class="E"></path><path d="M268 486c4 3 8 6 10 10v2h1c2-1 2-2 3-4 0-1 1-3 1-4h0v2 1c0 2 0 3 1 5 0 2 0 4 1 5l-1 2c-1 3-3 5-3 8-3 4-5 6-6 10l9-11c4 0 8-2 12-3 1 0 2 0 3 1l-1 1h2c-3 3-6 6-9 7-2 1-5 1-6 2-5 3-8 8-12 12-1 1-3 3-3 4 4-3 7-8 11-10 2 0 4 0 6 1h0c-2 3-5 5-9 7l9-1c-2 2-5 2-7 3-5 0-10 1-14 4-4 2-7 5-10 7 4-6 9-11 13-17 3-6 5-14 4-21 0-5-3-9-7-12v-1c0-1 0-2-1-3h-1c-1-1-2-2-3-2l-1-1c1-1 3-2 4-3 1 0 2 0 4-1z" class="B"></path><path d="M261 491l3-3h1c1 0 2 1 2 1l-1 1c2 1 3 3 4 4l-6-3v2c-1-1-2-2-3-2z" class="K"></path><path d="M268 486c4 3 8 6 10 10h-2c-1 0-2-2-2-2-1-1-2-3-4-4 1 2 2 5 3 7-3-2-3-5-6-7v-1s-1-1-2-1h-1l-3 3-1-1c1-1 3-2 4-3 1 0 2 0 4-1z" class="E"></path><defs><linearGradient id="d" x1="72.25" y1="497.437" x2="57.29" y2="480.594" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#d)" d="M53 468c1 0 3 0 4-1-1 1-2 1-3 2-1 0-1 1-2 1 0 2-1 3 0 5 1 1 1 2 1 3 1 1 1 2 2 4h1l1 1c0 1 3 4 4 4h0c2 1 3 2 5 2h0c2-1 4 0 6-1 3 0 6 0 9-1 1 0 2-1 3-2s3-2 4-4h0 0c1 0 2-1 2-2 1-1 1-2 2-3 1 3-1 7-2 9v1l-1 1c-1 2-3 3-4 5l-1 2h0 1c-1 1-1 2-2 3l-6 5h0-1c-2 2-4 5-6 5-1 1-3 2-3 2-1 0-1 0-1-1h0l1-1h-1l-2 1c-3 0-6 2-8 3l4-4h-1v-1h-3c-4-1-6-2-8-5-1-1-2-3-3-4 1 0 2 1 4 1v-1h-1v-2l1-1s-2-1-2-2c1-2-2-4-1-6h0l2 1h1l-2-4c1-1 2-2 2-3 0-3 1-7-1-9h0-2l2-2c2-1 3-1 5-1z"></path><path d="M64 497c3 0 6-1 9-1-2 2-5 3-7 3-1-1-2-1-2-1v-1z" class="H"></path><path d="M49 480l1 3c0 2 0 4 1 5l1 1v1c-2-1-3-2-4-3h1l-2-4c1-1 2-2 2-3z" class="R"></path><path d="M48 487c1 1 2 2 4 3 1 1 2 2 3 2s2 1 3 1h0 1 1v1h0 0c-3 0-7-2-10-2-1-2-2-3-3-4l1-1z" class="K"></path><path d="M49 494c5 2 10 3 15 3v1h-9c-2 0-4 0-6-1h-1v-2l1-1z" class="I"></path><path d="M92 476c1 3-1 7-2 9v1h-2 0c-1 0-1 1-2 1-2 0-3 2-4 3v-1h-1l1-1h0c1-1 6-5 6-7 1 0 2-1 2-2 1-1 1-2 2-3z" class="K"></path><path d="M53 468c1 0 3 0 4-1-1 1-2 1-3 2-1 0-1 1-2 1 0 2-1 3 0 5 1 1 1 2 1 3 1 1 1 2 2 4h1l1 1c0 1 3 4 4 4h0c2 1 3 2 5 2-2 1-2 1-4 1h0c1 0 1 0 1 1h1 0c-1 0-3 1-4 0-1 0-2-1-3-1s-1-1-2-1c-1-1-2-1-2-2h-1c0-1-1-2-1-3l-1-1-1-3c0-3 1-7-1-9h0-2l2-2c2-1 3-1 5-1z" class="E"></path><path d="M46 471l2-2c1 1 2 1 3 2h0c-1 4-1 8 1 11 1 2 2 3 3 4v1h0l-3-3h-1l-1-1-1-3c0-3 1-7-1-9h0-2z" class="P"></path><path d="M73 496c1 0 3-1 5-2l3-3c-4 5-9 9-15 12-1 1-3 2-5 2-2 1-3 1-5 1-4-1-6-2-8-5-1-1-2-3-3-4 1 0 2 1 4 1v-1c2 1 4 1 6 1h9s1 0 2 1c2 0 5-1 7-3z" class="B"></path><path d="M48 501c-1-1-2-3-3-4 1 0 2 1 4 1v-1c2 1 4 1 6 1h9s1 0 2 1c-6 1-12 2-17 1l-1 1z" class="G"></path><defs><linearGradient id="e" x1="75.046" y1="502.313" x2="70.618" y2="497.576" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#e)" d="M86 487c1 0 1-1 2-1h0 2l-1 1c-1 2-3 3-4 5l-1 2h0 1c-1 1-1 2-2 3l-6 5h0-1c-2 2-4 5-6 5-1 1-3 2-3 2-1 0-1 0-1-1h0l1-1h-1l-2 1c-3 0-6 2-8 3l4-4h-1v-1h-3c2 0 3 0 5-1 2 0 4-1 5-2 6-3 11-7 15-12 2 0 4-3 5-4z"></path><path d="M110 624h0v-5h0l3 6c5 7 14 13 23 16 4 2 9 3 13 5 7 4 13 8 20 11 1 0 6 2 8 1 7 2 16 4 23 8 6 2 10 6 16 8 1 0 2 1 3 1 4 1 7 3 11 3 6 2 11 1 17 1 5 0 9 1 14 1 3-1 7-2 10-3 11-5 24-11 29-23 2-4 4-12 2-16v-1c-1-2-3-3-4-5-1-1-2-3-3-4-2 0-5-1-7 1v3c0 1 1 2 2 2h1v1c-1 0-2 0-2-1-1 0-2-1-2-2-1-2 0-4 1-5 1-2 3-3 5-3v1-1h3l2 2v1c0 1 2 2 2 3 2 2 3 6 4 8h0v4c1 0 1 0 2-1v2c1 2 0 4 0 5s0 2-1 3c0 2 0 4-1 5 0 1 0 1-1 2l-1 1v1c0 2-1 3-2 4l-1 3c2 1 2 0 4 1l-2 1-6 2c3 0 5 0 7-1-5 6-11 8-18 10-1 0-8 1-8 1v1l2 1h-1c-4 1-8-1-13-1 3 1 5 2 7 4h0c-3-1-5-1-7-1 1 1 3 1 4 2-1 1-7 1-9 1l-2-1c-4-1-8-4-13-5 3 2 8 4 11 6-4 0-8 1-11 0l-9-3c-2-2-17-7-20-8-5-1-10-4-15-6-10-5-20-10-31-13-1 0-1 1-2 1h-1c-8-3-14-9-21-12-7-2-15-2-22-3-2-1-4-4-6-4v1h0l-6-9v-1-1h0c0-1 0-2-1-3v-3z" class="B"></path><path d="M110 627c2 2 3 4 4 6 2 3 6 8 10 10l-1 1c-2-1-4-4-6-4v1h0l-6-9v-1-1h0c0-1 0-2-1-3z" class="M"></path><path d="M301 669l-1-1c-1 0-3 1-5 1 2-2 2-5 3-8 1-1 1-1 1-2 1-1 2-2 2-3 1-1 1-1 1-2v-1l1-1v-1l1-1h0 0c0-2 0-2 1-3h0c0 3-1 7-2 9-1 1-1 2-1 3h0v1c0 2-1 3-2 4l-1 3c2 1 2 0 4 1l-2 1zm-177-26h0c5 1 10 1 14 1 9 2 16 7 23 11 2 1 6 2 8 3-1 0-1 1-2 1h-1c-8-3-14-9-21-12-7-2-15-2-22-3l1-1z" class="N"></path><path d="M254 630c1-1 1-2 1-3 0-3-1-6 0-8 0-1 0-2 1-3v1c0 1 2 2 3 3h1c1 1 1 2 2 3 0 2-2 5-1 7v1 2h1l1-1v2c-1 1-1 2-1 3-6 6-13 7-20 11-2 1-5 3-7 4v1l5-1c3-1 6-3 9-3 4-4 8-5 13-6-1 1-1 3-2 5-1 1-3 3-4 5l3-1h0c-1 3-5 8-8 9-2 0-5 1-7 2-2 0-4 0-6 2h0c-3 0-7 1-10 0 1 1 3 2 3 4 0 1-1 3-3 4 0 1-1 1-2 1-1 1-3 0-5 0-1 0-1 0-2 1-1 0-2-1-3-1-6-2-10-6-16-8-7-4-16-6-23-8 1 0 3 1 5 1 5 1 9 3 14 4l-4-2c-2-1-6-1-7-2h2v-1h-2l-7-2h-1v-1h1c1-1 4 1 5 1l17 5h0c1 0 2 0 2 1h1l-1-1c3-3 7 0 9-1 1-1 2 0 3-1h1c2 0 3-1 4-2v-1c-1 1-4 2-5 2h-3-3c-1 1-1 1-2 0h-1c2-1 3-2 5-2 1 0 2-1 2-1h1 0c0-1 1-1 1-1l2-2c-3 1-5 3-7 4-2 0-3 1-5 0-1 0-2 0-3 1h-5-6l-1-1h1c-2-1-5 0-7-1h0c-2 0-3-1-5-1h0 3c2 0 4 1 7 1 2 0 4 0 6-1l1 1-4 1c5 1 9 0 13-1 3-1 6-2 8-4 2-1 3-2 4-3h2 1l-1-1 1-3h1c1-1 2-1 3-1h0c1 1 1 1 2 1l2 1v3c1 0 2 0 2 1l-1 1c-1 1-2 1-2 2 3-1 5-3 7-4 3-1 5-2 8-3 5-3 11-8 13-14v-1h0z" class="B"></path><path d="M226 674c0-2 3-4 3-6l-2-1c0-1-1-1-1-2h2c1 1 3 2 3 4 0 1-1 3-3 4 0 1-1 1-2 1z" class="C"></path><path d="M221 650h0c0 1 1 1 1 1v1c-1 0-1 1-2 2-1 0-4 1-4 2v1c-1 0-1 1-2 1-2-1-3 0-5-1 1 0 1-1 2-1s2 0 3-1h1l1-1c2-1 3-3 5-4z" class="J"></path><path d="M219 648c0 1 1 1 2 2-2 1-3 3-5 4l-1 1h-1l3-3-1-1c-1 1-3 2-4 2-2 1-3 2-4 2h-4c3-1 6-2 8-4 2-1 3-2 4-3h2 1z" class="F"></path><path d="M232 661c-9 1-19 1-28 0h0c4 0 9 0 12-2 3 0 4-2 7-2h0l1 1h-1l-4 1c3 0 5 0 8 1 1 1 3 0 5 1z" class="C"></path><path d="M207 666c-2-2-5-2-8-4-1 0-2 0-2-1h1c5 2 10 4 16 4h10c0 1-1 1-2 2-1 0-2 1-2 2h-5-3c-1 0-2 0-3-1l-2-2z" class="U"></path><path d="M207 666c5 1 10 1 15 1-1 0-2 1-2 2h-5-3c-1 0-2 0-3-1l-2-2z" class="O"></path><path d="M223 643h0c1 1 1 1 2 1l2 1v3c1 0 2 0 2 1l-1 1c-1 1-2 1-2 2s-1 2-2 2v-1c1 0 1 0 1-1h0c-3 0-5 3-7 4-1 0-1 1-2 1v-1c0-1 3-2 4-2 1-1 1-2 2-2v-1s-1 0-1-1h0c-1-1-2-1-2-2l-1-1 1-3h1c1-1 2-1 3-1z" class="N"></path><path d="M223 643h0c1 1 1 1 2 1l2 1v3c1 0 2 0 2 1l-1 1c-1-1-2-1-2-2h-1l-1 1c-1-2 0-4-1-6z" class="L"></path><path d="M177 658c1 0 3 1 5 1 5 1 9 3 14 4l-4-2c-2-1-6-1-7-2h2v-1h-2l-7-2h-1v-1h1c7 2 14 3 20 6h-1c0 1 1 1 2 1 3 2 6 2 8 4l2 2c1 1 2 1 3 1 3 1 6 3 9 5-1 0-1 0-2 1-1 0-2-1-3-1-6-2-10-6-16-8-7-4-16-6-23-8z" class="K"></path><defs><linearGradient id="f" x1="240.68" y1="659.692" x2="241.38" y2="645.082" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302e2e"></stop></linearGradient></defs><path fill="url(#f)" d="M249 649c4-4 8-5 13-6-1 1-1 3-2 5-1 1-3 3-4 5l-1 1-3 3h-1 0c-3 1-6 3-8 4h-11 0c-2-1-4 0-5-1-3-1-5-1-8-1l4-1h1l-1-1h0l12-5v1l5-1c3-1 6-3 9-3z"></path><path d="M240 652c3-1 6-3 9-3-3 4-10 6-15 7h-2c1-2 6-2 8-3v-1z" class="U"></path><path fill="#b4b3b5" d="M223 657l12-5v1l5-1v1c-2 1-7 1-8 3h2c-3 1-7 1-10 2l-1-1h0z"></path><path d="M227 660v-1l1 1c6 1 15 0 20-3 1 0 1-1 2-1l2-2h1 2l-3 3h-1 0c-3 1-6 3-8 4h-11 0c-2-1-4 0-5-1z" class="J"></path><path d="M117 641v-1c2 0 4 3 6 4 7 1 15 1 22 3 7 3 13 9 21 12h1c1 0 1-1 2-1 11 3 21 8 31 13 5 2 10 5 15 6v1c-2 0-4-1-6-1h-3c-1-1-3-1-4-2 1 1 1 2 2 3l1 1c1 0 2 0 3 1-2 0-3-1-5-1 1 1 2 1 3 2h0l5 3 7 3c-8 0-17 1-25-1-14-3-27-7-39-15l-15-12c-8-6-16-10-22-18z" class="B"></path><path d="M158 662c0-1 0-1 1-1-1-1-1-2-2-3-1 0-3-1-4-1-3-2-7-2-11-3 3-1 8 1 11 2 2 1 5 2 7 3 2 2 5 2 7 4h-3v-1l-2-1-2 1h-2z" class="J"></path><path d="M211 684l7 3c-8 0-17 1-25-1l2-1 1 1h2 5c2 1 5 0 7 0-1 0-3-1-4-1h-1c-1 0-2 0-3-1h-4c3 0 7 0 11 1 1 0 1-1 2-1z" class="M"></path><defs><linearGradient id="g" x1="164.3" y1="668.967" x2="165.933" y2="662.846" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#272627"></stop></linearGradient></defs><path fill="url(#g)" d="M183 674h-2-1v-1l-1 1c0-1 0-1-1-1-1-1-3-2-5-3-2 0-5-1-7-2l-14-5c-4-2-8-5-12-6v-1h0c3 0 6 3 9 4s6 1 9 2h2l2-1 2 1v1h0c2 2 4 3 6 4 3 1 6 3 9 4 1 0 3 1 4 1l3 1v1h-1-2z"></path><defs><linearGradient id="h" x1="187.925" y1="681.173" x2="186.805" y2="661.68" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2b2c"></stop></linearGradient></defs><path fill="url(#h)" d="M160 659l1-1-2-1h0c6 4 12 7 19 10h0l-1 1c3 1 5 2 8 3 3 0 5 1 7 2 3 1 7 1 10 2 1 1 1 2 2 3l1 1c1 0 2 0 3 1-2 0-3-1-5-1h-1-1l-2-1-3-1h-3 0c-1-1-2-1-3 0-1 0-2-1-3-1l-1-1c-1 0-2 0-3-1h2 1v-1l-3-1c-1 0-3-1-4-1-3-1-6-3-9-4-2-1-4-2-6-4h0 3c-2-2-5-2-7-4z"></path><path d="M167 659c1 0 1-1 2-1 11 3 21 8 31 13 5 2 10 5 15 6v1c-2 0-4-1-6-1h-3c-1-1-3-1-4-2-3-1-7-1-10-2-2-1-4-2-7-2-3-1-5-2-8-3l1-1c5 2 11 4 16 4-2-1-4-2-7-3-6-3-13-6-20-9z" class="C"></path><path d="M208 531c4 0 1-3 3-4 1 0 2 0 3 2s1 4 2 5c0 6 0 12-2 18l-3 6c3-4 4-8 6-12 2 3 3 5 2 8 0 4-2 8-5 10-1 2-3 3-4 4s0 2 0 2c0 1-1 2-1 2-2 3-4 6-6 8-6 5-11 9-18 12 0 1-3 2-3 3-1 0-1 0-2 1h-1l-2 2-8 1-15 5c3-3 6-5 9-7 5-4 9-9 12-14h0c1-1 1-3 1-5h0c1-1 1-3 2-5 0-1 1-2 1-4l9-23 1 1c-1 0-1 1-1 2 0 2-1 6 0 8h1 1v-1l1 1-7 10c4-2 6-6 10-8 1 5 0 9-3 14 3-3 6-8 7-11v-1c1-4 1-10 1-14 2-1 3-3 4-4l3-3c1 1 2 1 2 3h0c1 2 0 4 0 6h1l-1-18z" class="B"></path><path d="M169 599v-1c2-1 3-3 5-4 3-1 7-2 10-3 2-1 4-3 6-4 3-2 5-3 8-5 1-1 2-2 3-2h2c-6 5-11 9-18 12 0 1-3 2-3 3-1 0-1 0-2 1h-1l-2 2-8 1z" class="N"></path><path d="M98 417h1 0v1h0 1v1c-3 3-6 7-8 12-1 3-5 8-4 11 2-1 2-1 3-3 0-1 0-2 1-3v1c-2 5-2 8-2 13 1-1 1-2 1-3s1-2 1-3 0-2 1-4l1 1c-1 2-1 5-1 7l1 16c0 5-1 8-2 12-1 1-1 2-2 3 0 1-1 2-2 2h0 0c-1 2-3 3-4 4s-2 2-3 2c-3 1-6 1-9 1-2 1-4 0-6 1h0c-2 0-3-1-5-2h0c-1 0-4-3-4-4l-1-1h-1c-1-2-1-3-2-4 0-1 0-2-1-3-1-2 0-3 0-5 1 0 1-1 2-1 1-1 2-1 3-2-1 1-3 1-4 1v-1h-1-2c0-1 1-1 1-1v-2c2-1 3-1 5-1h0v-1c-1 0-2 0-3-1h0v-1s1 0 1-1h0 0c0-1 0-1-1-1h-1c-1-1-1-2-1-3 2 1 4 2 6 2 4 0 7-2 10-4 6-4 9-11 13-16 2-4 4-8 7-10v-1c1 0 3-1 3-2h1 0c0-1 1-2 1-2 2-2 4-4 6-5z" class="B"></path><path d="M76 479c-1 0-1 1-2 1-2 2-5 2-8 1l4-3 1 1h0c1 0 2 0 3 1 1-1 1-1 2-1z" class="C"></path><path d="M82 456l1 1c-2 4-3 7-6 10l2-1c0 2-1 2-2 4l-1 1c-3 2-7 3-9 5l-2 1h-1-1l-1-1 5-3c-2 0-5 2-6 1l1-1v-1c-1 1-2 1-3 1l13-10c3-2 4-5 7-6l1 1-2 1c1 0 1 0 2-1h1c0-1 1-1 1-2z" class="L"></path><path d="M77 467l2-1c0 2-1 2-2 4l-1 1c-3 2-7 3-9 5l-2 1h-1c1-1 1-2 2-2 2-1 7-3 8-4v-2c1-1 2-2 3-2z" class="D"></path><path d="M72 463c3-2 4-5 7-6l1 1-2 1c-1 1-3 3-3 4h0l1 1h-1l-2 2c-1 2-4 5-6 5l-1 1h-1c0 1-1 1-1 1h0-1v-1h0-1 0c-1 1-2 1-3 1l13-10z" class="E"></path><defs><linearGradient id="i" x1="96.421" y1="460.764" x2="60.516" y2="487.138" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#i)" d="M93 448l1 16c0 5-1 8-2 12-1 1-1 2-2 3 0 1-1 2-2 2h0 0c-1 2-3 3-4 4s-2 2-3 2c-3 1-6 1-9 1-2 1-4 0-6 1h0c-2 0-3-1-5-2h0c-1 0-4-3-4-4l-1-1c0-1-1-2-1-3-1-2-2-3-1-5 2 5 7 9 12 11 4 1 10 1 13-1 7-3 9-8 11-14v-1c0-2 1-5 1-7v-2c0-2 1-4 1-6v-5l1-1z"></path><path d="M91 460c1 2 1 4 1 6-1 1-1 2-1 3l-1 1v-1c0-2 1-5 1-7v-2z" class="H"></path><defs><linearGradient id="j" x1="72.001" y1="459.157" x2="88.814" y2="460.451" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#j)" d="M92 436v1c-2 5-2 8-2 13 1-1 1-2 1-3s1-2 1-3 0-2 1-4l1 1c-1 2-1 5-1 7l-1 1v5c0 2-1 4-1 6v2c0 2-1 5-1 7-2 5-8 10-13 12l-3 1c2-1 3-3 4-5l-2 2c-1 0-1 0-2 1-1-1-2-1-3-1h0l-1-1 1-1-2 1c-1 0-4 1-6 1l2-2h0l2-1c2-2 6-3 9-5l1-1c1-2 2-2 2-4l-2 1c3-3 4-6 6-10l-1-1h-1c2-2 4-3 5-6v4c1-4 2-8 2-12 2-1 2-1 3-3 0-1 0-2 1-3z"></path><path d="M76 471h0c2 0 3-2 5-3-1 2-3 5-4 6h-1l-1-1-4 4-2 1c-1 0-4 1-6 1l2-2h0l2-1c2-2 6-3 9-5z" class="L"></path><path d="M86 450v4c-1 5-2 11-7 15-1 0-1 0-2 1 1-2 2-2 2-4l-2 1c3-3 4-6 6-10l-1-1h-1c2-2 4-3 5-6z" class="B"></path><defs><linearGradient id="k" x1="78.895" y1="462.773" x2="90.122" y2="464.162" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#k)" d="M92 444c0-1 0-2 1-4l1 1c-1 2-1 5-1 7l-1 1v5c0 2-1 4-1 6v2c0 2-1 5-1 7-2 5-8 10-13 12l-3 1c2-1 3-3 4-5l6-6c2-4 4-9 5-14 1-2 1-5 1-7 1-1 1-2 1-3s1-2 1-3z"></path><path d="M92 444c0-1 0-2 1-4l1 1c-1 2-1 5-1 7l-1 1v5c0 2-1 4-1 6v2c-1-4 0-7 1-10v-8z" class="K"></path><path d="M98 417h1 0v1h0 1v1c-3 3-6 7-8 12-1 3-5 8-4 11 0 4-1 8-2 12v-4c-1 3-3 4-5 6h1c0 1-1 1-1 2h-1c-1 1-1 1-2 1l2-1-1-1c-3 1-4 4-7 6l-1-1-1 1c-3 3-7 5-11 6-1 0-5 1-5 2-1 0-1 1-1 1l1 2c-1 2 0 3 1 5 0 1 1 2 1 3h-1c-1-2-1-3-2-4 0-1 0-2-1-3-1-2 0-3 0-5 1 0 1-1 2-1 1-1 2-1 3-2-1 1-3 1-4 1v-1h-1-2c0-1 1-1 1-1v-2c2-1 3-1 5-1h0v-1c-1 0-2 0-3-1h0v-1s1 0 1-1h0 0c0-1 0-1-1-1h-1c-1-1-1-2-1-3 2 1 4 2 6 2 4 0 7-2 10-4 6-4 9-11 13-16 2-4 4-8 7-10v-1c1 0 3-1 3-2h1 0c0-1 1-2 1-2 2-2 4-4 6-5z" class="P"></path><path d="M53 461v-1h8v1h2c-1 1-2 1-3 1l-1 1h0-3v-1c-1 0-2 0-3-1z" class="M"></path><path d="M84 446c4-5 5-12 8-18 2-3 5-6 7-9h1c-3 3-6 7-8 12-1 3-5 8-4 11 0 4-1 8-2 12v-4h-1c1-1 1-3 1-4h0l-1 1-1-1z" class="H"></path><path d="M84 446l1 1 1-1h0c0 1 0 3-1 4h1c-1 3-3 4-5 6h1c0 1-1 1-1 2h-1c-1 1-1 1-2 1l2-1-1-1c-3 1-4 4-7 6l-1-1c2-2 5-4 7-7l6-9z" class="D"></path><path d="M61 460h1c9-2 16-13 20-20 1-2 2-4 4-6l-2 5v3c-1 1-1 1-1 2v1l-2 5h-1-1c-1 2-3 4-4 6l-4 4h0l-2-1c-1 1-2 2-4 2h-1c0 1 0 1-1 1l1-1v-1l-1 1h-2v-1z" class="J"></path><path d="M69 459c6-6 12-13 15-20v3c-1 1-1 1-1 2v1l-2 5h-1-1c-1 2-3 4-4 6l-4 4h0l-2-1z" class="E"></path><path d="M71 460h0l4-4c1-2 3-4 4-6h1v2c-1 0-2 1-2 1-1 3-5 7-7 8l-2 2h1c-3 3-7 5-11 6-1 0-5 1-5 2-1 0-1 1-1 1l1 2c-1 2 0 3 1 5 0 1 1 2 1 3h-1c-1-2-1-3-2-4 0-1 0-2-1-3-1-2 0-3 0-5 1 0 1-1 2-1 1-1 2-1 3-2-1 1-3 1-4 1v-1h-1-2c0-1 1-1 1-1v-2c2-1 3-1 5-1h0 3 0l1-1c1 0 2 0 3-1l1-1v1l-1 1c1 0 1 0 1-1h1c2 0 3-1 4-2l2 1z" class="L"></path><path d="M69 459l2 1-6 3c-3 2-5 3-7 4h-1c-1 1-3 1-4 1v-1h-1-2c0-1 1-1 1-1v-2c2-1 3-1 5-1h0 3 0l1-1c1 0 2 0 3-1l1-1v1l-1 1c1 0 1 0 1-1h1c2 0 3-1 4-2z" class="G"></path><path d="M63 461l1-1v1l-1 1c1 0 1 0 1-1h1c-2 3-9 5-13 5 1-1 2-2 4-3h0 3 0l1-1c1 0 2 0 3-1z" class="I"></path><path d="M268 368c3 1 7 2 8 3h1c1 0 2 0 2 1 3 2 6 2 7 6h0c0 1 0 1 1 1h1l-1-1c1 0 1 0 1 1h1c1 1 2 2 2 3l3 3c0 1 1 2 2 3 2 5 4 9 4 14l1 1s1 0 1 1v1 1c0 1 1 2 2 3h0c1 1 1 1 1 2v5l1-1 1 1 1-1v1 1h0v2l-5 32c-2 4-3 8-4 12h-1c0-1 0-2-1-3l-2-5c-3-6-7-9-14-11 0-1 0-2-1-2 0-1-1-1-1-2h3 0c0-1-1-1-2-1-1-1-3-3-5-3l-4-1c-5-1-11-2-16 2-2 2-4 5-5 8 0 4 1 9 3 11 1 2 2 3 4 3 1 0 2 0 3-1s2-2 2-4c0-1 0-2-1-3s-2 0-3-2h0c1-1 1-1 2 0 2 1 3 2 3 5 0 2 0 4-1 6-2 1-4 2-6 2h-4c0-1-2-2-3-4-2-1-2-3-3-5v-1c-1-6 2-11 6-15 4-5 10-6 16-6 2 1 4 1 6 0l4 2c2 2 4 3 6 5 1 0 1 0 2 1l3 3c3 5 6 8 8 13h0c-1-3-2-5-3-7 0-3-1-4-2-7-1-1-2-3-2-5-1 0-1-1-1-2v-2c0-3 0-5 1-8 1 2 3 4 5 5 0-2 0-2 1-4 2 4 3 7 3 10 2-11 1-25-5-35-5-9-13-17-24-20-7-2-13-2-20 2-4 2-7 7-9 11-1 4-1 8 1 12 2 3 4 5 7 5 2 1 4 0 6-1s3-3 3-5c1-1 1-3 0-4s-2-2-4-2c-1 1-1 2-2 3v-1-1c0-1 1-1 1-2 1 0 3 0 4 1 1 0 2 2 3 3 0 2 0 5-1 6-2 3-4 4-7 5-5 1-8 0-12-2-3-2-4-5-6-8 0-1 1-1 1-2v-6c1-2 2-5 2-7 1-1 0-2 0-2 0-1 1-2 1-2h-2 0c0-1 1-2 1-2 3-3 6-5 9-7h1c3-1 6-1 9-2l2-1 1-1h-4v-1h5c1 0 2 0 3-1h0 2 1 2z" class="B"></path><path d="M268 368c3 1 7 2 8 3h1c1 0 2 0 2 1 3 2 6 2 7 6h0c0 1 0 1 1 1h1l-1-1c1 0 1 0 1 1h1c1 1 2 2 2 3v1c1 1 1 2 2 3l-1 1c0-1-1-2-1-3l-1-1c-1 1 2 3 2 5l-1-1c-1-2-2-4-3-5l-1 1-1-1c-1 0-1 0-1-1-1 0-2-1-3-1v1c1 1 2 1 3 2v-1c1 1 2 1 2 2 1 1 2 2 2 3l2 2v1c-2-1-3-3-5-5s-7-6-11-7c-2-1-5-2-8-3h3c1 0 1 0 2 1h1 1s-1 0-1-1l-1-1c-1-1-4-1-6-2-2 0-5-2-7-2h-4v-1h5c1 0 2 0 3-1h0 2 1 2z" class="O"></path><path d="M268 368c3 1 7 2 8 3h1c1 0 2 0 2 1 3 2 6 2 7 6h0c-1-1-2-2-4-2l-6-3c-3-1-7-2-10-3-2 0-4-1-6-1 1 0 2 0 3-1h0 2 1 2z" class="K"></path><path d="M268 368c3 1 7 2 8 3-1 0-3 0-5-1h-5c-2 0-4-1-6-1 1 0 2 0 3-1h0 2 1 2z" class="G"></path><defs><linearGradient id="l" x1="262.005" y1="368.344" x2="238.434" y2="406.582" xlink:href="#B"><stop offset="0" stop-color="#434343"></stop><stop offset="1" stop-color="#626263"></stop></linearGradient></defs><path fill="url(#l)" d="M259 370c2 0 5 2 7 2 2 1 5 1 6 2l1 1c0 1 1 1 1 1h-1-1c-1-1-1-1-2-1h-3c-5-1-13-1-17 3-5 4-12 10-12 17-1 3-1 6 0 9 1 2 2 3 2 6-3-2-4-5-6-8 0-1 1-1 1-2v-6c1-2 2-5 2-7 1-1 0-2 0-2 0-1 1-2 1-2h-2 0c0-1 1-2 1-2 3-3 6-5 9-7h1c3-1 6-1 9-2l2-1 1-1z"></path><defs><linearGradient id="m" x1="211.363" y1="295.29" x2="184.184" y2="247.072" xlink:href="#B"><stop offset="0" stop-color="#222"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#m)" d="M148 229c3 6 6 10 12 13l13 3c4 0 8 0 11 2h2l6 4c2 2 4 4 7 5l5 1v1l-3 1c-1-1-6-2-7-1h1 1c1 1 4 1 5 2l-1 2c-1 0-2-1-3-1h-1c3 2 6 3 8 5v1l1 2c2 1 5 2 7 4h2l1 1c-1 1-2 1-3 1h0v1l1 1c2 0 3 1 4 2l2 2c3 2 6 4 8 7l-3-1h-1l2 2 4 3c-1 1-1 2-1 4h-1l-2-1c0-1-3-2-4-3-8-3-16-2-24-2-2 0-6 0-8 1 0 0-1 0-1-1-2 0-3-1-5-1-2-1-5-3-7-4v-1c2 0 4 1 5 1l1-1c-1 0-1-1-1-1-1-2-3-6-5-7h-1c0-1 0-1 1-2h-1l-1-1c-2-1-2 0-5-1h-3c-1-1-1-1-2-1-1-1-3-3-4-5v-3c-1 0-1-1-1 0v-4-7c-1-1-1-3-2-4 0-2-1-3-1-4s0-1-1-1c-1-1-2-3-3-5-2-2-3-3-4-6v-3z"></path><path d="M174 254c4 3 9 4 14 6 3 0 6 1 8 1h1-1c3 2 6 3 8 5v1l1 2c2 1 5 2 7 4h2l1 1c-1 1-2 1-3 1-3-1-5-1-8-2s-5-4-7-5c-4-2-9-3-13-6-4-2-7-5-11-7l1-1z" class="B"></path><path d="M201 267c1 1 3 1 4 2 2 1 5 2 7 4-5-1-8-3-11-6z" class="O"></path><path d="M188 260c3 0 6 1 8 1h1-1c3 2 6 3 8 5v1l1 2c-1-1-3-1-4-2-4-3-8-5-13-7z" class="C"></path><path d="M193 277c-4-3-7-7-10-11h0c3 3 5 6 9 9 0 0 1 1 2 1h5c2 0 5 1 7 2 1 1 2 1 2 1 3 0 7 3 10 4 0 1 1 1 1 1v1l1 1h0l-3-1v1h-1c3 1 6 3 9 3l4 3c-1 1-1 2-1 4h-1l-2-1c0-1-3-2-4-3h0c-3-3-7-4-10-7-2-1-5-3-8-4h-2c0-1-1-1-1-1h-2 0c-2-1-3-2-5-3z" class="M"></path><path d="M193 277h1 4c4 0 6 2 9 4 1 1 2 1 3 2-1 0-1 0-2-1h-1 0c1 1 4 3 4 3h0c-2-1-5-3-8-4h-2c0-1-1-1-1-1h-2 0c-2-1-3-2-5-3z" class="O"></path><path d="M173 259l6 6c2 3 3 6 5 9 6 9 15 10 25 12l-6-5c3 1 6 3 8 4 3 3 7 4 10 7h0c-8-3-16-2-24-2-2 0-6 0-8 1 0 0-1 0-1-1-2 0-3-1-5-1-2-1-5-3-7-4v-1c2 0 4 1 5 1l1-1c-1 0-1-1-1-1-1-2-3-6-5-7h-1c0-1 0-1 1-2h-1l-1-1h0c1-1-1-4-1-5 0 0 1 0 2 1h0c1 0 1 1 2 1h0v-1c-1-1-1-1-1-2-1 0-1-1-1-1l-1-1c0-1-1-3-2-5l1-1h0z" class="B"></path><path d="M173 268s1 0 2 1c1 3 3 7 5 10 1 1 2 1 3 2 1 2 3 2 4 4-1 0-3 0-4-1v1c1 1 1 2 3 3 3 2 7 2 11 2-2 0-6 0-8 1 0 0-1 0-1-1-2 0-3-1-5-1-2-1-5-3-7-4v-1c2 0 4 1 5 1l1-1c-1 0-1-1-1-1-1-2-3-6-5-7h-1c0-1 0-1 1-2h-1l-1-1h0c1-1-1-4-1-5z" class="C"></path><defs><linearGradient id="n" x1="195.003" y1="289.173" x2="173.897" y2="261.615" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient></defs><path fill="url(#n)" d="M173 259c2 3 3 5 5 7 3 5 5 10 10 13 2 2 4 3 6 4s6 3 8 5c-3-1-6-1-9-2-1 0-4-1-6-1-1-2-3-2-4-4-1-1-2-1-3-2-2-3-4-7-5-10h0c1 0 1 1 2 1h0v-1c-1-1-1-1-1-2-1 0-1-1-1-1l-1-1c0-1-1-3-2-5l1-1z"></path><path d="M148 229c3 6 6 10 12 13l13 3c4 0 8 0 11 2h2l6 4c2 2 4 4 7 5l5 1v1l-3 1c-1-1-6-2-7-1h1 1c1 1 4 1 5 2l-1 2c-1 0-2-1-3-1h-1c-2 0-5-1-8-1-5-2-10-3-14-6l-1 1c-1-1-2-2-3-2 0 2 2 4 3 6h0l-1 1c1 2 2 4 2 5l1 1s0 1 1 1c0 1 0 1 1 2v1h0c-1 0-1-1-2-1h0c-1-1-2-1-2-1 0 1 2 4 1 5h0c-2-1-2 0-5-1h-3c-1-1-1-1-2-1-1-1-3-3-4-5v-3c-1 0-1-1-1 0v-4-7c-1-1-1-3-2-4 0-2-1-3-1-4s0-1-1-1c-1-1-2-3-3-5-2-2-3-3-4-6v-3z" class="B"></path><defs><linearGradient id="o" x1="187.977" y1="252.523" x2="177.492" y2="243.699" xlink:href="#B"><stop offset="0" stop-color="#090708"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#o)" d="M173 245c4 0 8 0 11 2h2l6 4h-2c-1 0-3-1-4-1-2-1-8-3-10-3h-1s-1 0-2-1v-1z"></path><path d="M160 242l13 3v1c1 1 2 1 2 1h1c3 3 7 5 11 7 0 1 3 1 3 2-5-1-9-3-13-5h0c-4-1-7-3-10-4s-5-3-7-5z" class="C"></path><path d="M167 247c1-1 1-1 3-1 2 1 6 3 7 5h0c-4-1-7-3-10-4zm7 7c-1-1-2-1-3-2h-1c-2-1-3-2-5-3l1-1c4 2 8 4 11 6 6 2 11 3 17 5h1v-1h1c1 1 4 1 5 2l-1 2c-1 0-2-1-3-1h-1c-2 0-5-1-8-1-5-2-10-3-14-6z" class="D"></path><path d="M152 238c5 4 10 6 14 10l-1 1c2 1 3 2 5 3h1c1 1 2 1 3 2l-1 1c-1-1-2-2-3-2 0 2 2 4 3 6h0l-1 1c1 2 2 4 2 5l1 1s0 1 1 1c0 1 0 1 1 2v1h0c-1 0-1-1-2-1h0c-1-1-2-1-2-1 0 1 2 4 1 5h0c-2-1-2 0-5-1h-3c-1-1-1-1-2-1-1-1-3-3-4-5v-3c-1 0-1-1-1 0v-4-7c-1-1-1-3-2-4 0-2-1-3-1-4s0-1-1-1c-1-1-2-3-3-5z" class="L"></path><path d="M159 252h0c2 2 2 7 1 9v5-3c-1 0-1-1-1 0v-4-7z" class="C"></path><path d="M164 252l3 3 2 2v1l3 4c1 2 1 2 2 3l1 1s0 1 1 1c0 1 0 1 1 2v1h0c-1 0-1-1-2-1h0c-1-1-2-1-2-1-1 0-3-5-4-6v3c1 1 1 3 1 4v1c-1-2-1-3-2-5 0-1-1-3-1-5-1-2-3-5-3-8z" class="H"></path><path d="M152 238c5 4 10 6 14 10l-1 1c2 1 3 2 5 3h1c1 1 2 1 3 2l-1 1c-1-1-2-2-3-2 0 2 2 4 3 6h0l-1 1c1 2 2 4 2 5-1-1-1-1-2-3l-3-4v-1l-2-2-3-3h0c-1-1-1-2-2-2v-1l-4-4c-1 0-2-1-2-1 0-1 0-1-1-1-1-1-2-3-3-5z" class="C"></path><path d="M172 260h0c0-1-1-1-1-2-1-1-2-2-3-4h0l-2-2h1 1l2 1c0 2 2 4 3 6h0l-1 1z" class="D"></path><path d="M118 342l2-2c2-3 3-6 4-8h1c0 2-1 3-1 4v2h0 1c-1 2-1 3-1 5l-1 8c0 7-2 13-3 20-1 6-1 11-2 17-2 9-9 15-16 22 0 2-3 3-4 5 1 0 1 0 2-1v1l-2 2c-2 1-4 3-6 5 0 0-1 1-1 2h0-1c0 1-2 2-3 2v1c-3 2-5 6-7 10-4 5-7 12-13 16-3 2-6 4-10 4-2 0-4-1-6-2-1-1-3-2-4-3 0-1-1-2-2-3h-1l1-1c1 0 5-2 6-3h-3-3c1 0 3 0 4-1 3-1 5-5 8-7l8-6v-1c-1 0-2 0-2 1l-5 3v-1c2-3 4-6 5-10 1-3 3-6 4-8 0-2 2-4 2-5h0l-1 1v-1c1-4 4-8 8-10h0l1-1c2-4 5-7 9-8v-2l-6 2h1c0-1 1-1 1-3h0c-1 0-2 0-3 1l-1-1 2-1-1-1c-1-1 0-1 0-3h0c3-3 6-6 10-8 2-2 5-3 8-4h1 0c0-1 1-1 1-2v-1l1-1h0l-2 1-1-1 1-1s1 0 2-1l3-3c2-1 3-2 4-3 2-2 3-5 4-7 2-4 4-7 7-10z" class="G"></path><path d="M103 377h0c1-1 4-4 6-4-2 4-4 8-7 11l-1 1c0-2 1-2 0-3-1 1-2 3-3 5l-4 4-1-1c2-1 3-3 4-5 2-3 3-6 6-8z" class="B"></path><path d="M90 398h1l5-4h0 0c-1 2-5 4-6 6v1c-1 0-2 2-2 3-2 2-5 4-5 7v3h-1c-1 2-2 4-4 4v-2-7h1 0c1 0 2 0 2-1s1-2 2-3c0 0 0-1 1-1 0-1 1-1 2-3l1-1c0-1 1-2 2-3l1 1h0z" class="K"></path><path d="M79 409h0c1 0 2 0 2-1 1 1 1 2 0 4h-1c-1-1 0-1 0-2 0 0-1 0-2-1h1z" class="E"></path><defs><linearGradient id="p" x1="89.137" y1="398.651" x2="77.471" y2="405.62" xlink:href="#B"><stop offset="0" stop-color="#535153"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#p)" d="M100 378h0c1-1 1-1 3-1-3 2-4 5-6 8-1 2-2 4-4 5l1 1c-2 2-4 3-5 5v1c1-1 2-1 2-2l1 1-2 2h0l-1-1c-1 1-2 2-2 3l-1 1c-1 2-2 2-2 3-1 0-1 1-1 1-1 1-2 2-2 3s-1 1-2 1h0v-2h0l-3 3c3-6 3-12 9-17 1-1 3-2 4-3 3-2 5-5 7-8h0c1 0 1 0 1-1l2-2h1v-1z"></path><path d="M85 393c1-1 3-2 4-3 0 2-1 3-3 5l-3 3-1 2h-1c0-1 1-2 1-2 1-2 2-3 3-4v-1z" class="K"></path><path d="M100 378h0c1-1 1-1 3-1-3 2-4 5-6 8-1 2-2 4-4 5l-9 9-1-1 3-3c2-2 3-3 3-5 3-2 5-5 7-8h0c1 0 1 0 1-1l2-2h1v-1z" class="G"></path><path d="M118 342l2-2c2-3 3-6 4-8h1c0 2-1 3-1 4v2h0l-1 3-2 2c-1 4-2 7-3 11h0c-1 2-3 4-4 6-2 3-6 6-9 7-2 0-4 1-6 2v-1l1-1h0l-2 1-1-1 1-1s1 0 2-1l3-3c2-1 3-2 4-3 2-2 3-5 4-7 2-4 4-7 7-10z" class="Q"></path><path d="M118 342l2-2c2-3 3-6 4-8h1c0 2-1 3-1 4v2h0l-1 3-2 2h-1v2c-1 1-1 3-2 4h-1l1-2v-1h0c0-1 1-2 0-3v-1z" class="P"></path><defs><linearGradient id="q" x1="104.793" y1="371.942" x2="84.923" y2="384.48" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#7c7b7d"></stop></linearGradient></defs><path fill="url(#q)" d="M113 367c2-3 4-5 5-8l1 1c-2 3-5 6-7 9 0 1-1 1-2 2 0 1-1 2-1 2-2 0-5 3-6 4h0c-2 0-2 0-3 1h0v1h-1l-2 2c0 1 0 1-1 1h0c-1 1-2 2-3 2-3 2-5 4-7 5l-6 2h1c0-1 1-1 1-3h0c-1 0-2 0-3 1l-1-1 2-1-1-1c-1-1 0-1 0-3h0c3-3 6-6 10-8 2-2 5-3 8-4h1c2-1 3-2 5-2l6-3c0-1 1-1 2-2 2 0 3-2 4-4h1 1 0l-2 4c-1 1-1 2-2 3z"></path><defs><linearGradient id="r" x1="90.672" y1="381.508" x2="85.041" y2="389.984" xlink:href="#B"><stop offset="0" stop-color="#50524f"></stop><stop offset="1" stop-color="#727074"></stop></linearGradient></defs><path fill="url(#r)" d="M100 378v1h-1l-2 2c0 1 0 1-1 1h0c-1 1-2 2-3 2-3 2-5 4-7 5l-6 2h1c0-1 1-1 1-3h0c-1 0-2 0-3 1l-1-1 2-1c3-1 7-3 10-5l10-4z"></path><path d="M98 371c2-1 3-2 5-2l6-3c0-1 1-1 2-2 2 0 3-2 4-4h1 1 0l-2 4c-1 1-1 2-2 3s-3 2-4 3-2 1-3 1l-1 1-8 3h-1c-1 1-2 1-3 1-1 1-2 1-3 2h1c-2 1-3 2-4 3-2 1-3 2-4 3s-3 1-4 2c-1-1 0-1 0-3h0c3-3 6-6 10-8 2-2 5-3 8-4h1z" class="Q"></path><path d="M93 384c1 0 2-1 3-2-2 3-4 6-7 8-1 1-3 2-4 3-6 5-6 11-9 17l3-3h0v2h-1v7 2c2 0 3-2 4-4 0 3-1 4-3 6s-4 4-7 5l-1 1c-3 1-6 2-8 5l-5 3v-1c2-3 4-6 5-10 1-3 3-6 4-8 0-2 2-4 2-5h0l-1 1v-1c1-4 4-8 8-10h0l1-1c2-4 5-7 9-8v-2c2-1 4-3 7-5z" class="B"></path><path d="M86 389c2-1 4-3 7-5-2 4-4 5-7 7v-2z" class="C"></path><path d="M82 414c0 3-1 4-3 6s-4 4-7 5c2-2 4-4 5-7 0-1 0-1 1-2h0v2c2 0 3-2 4-4z" class="J"></path><path d="M67 415h0c1 1 0 2 0 3v1c1-1 2-2 2-3h1v1c1 1 2 1 3 1-2 2-4 5-7 7l3-6c-1 1-2 1-3 2 0 1-1 2-1 2h-2c1-3 3-6 4-8z" class="D"></path><path d="M67 415c0-2 2-4 2-5h0l-1 1v-1c1-4 4-8 8-10 1 3-1 7 0 10l-3 8c-1 0-2 0-3-1v-1h-1c0 1-1 2-2 3v-1c0-1 1-2 0-3h0z" class="G"></path><path d="M125 338c-1 2-1 3-1 5l-1 8c0 7-2 13-3 20-1 6-1 11-2 17-2 9-9 15-16 22 0 2-3 3-4 5 1 0 1 0 2-1v1l-2 2c-2 1-4 3-6 5 0 0-1 1-1 2h0-1c0 1-2 2-3 2v1c-3 2-5 6-7 10-4 5-7 12-13 16-3 2-6 4-10 4-2 0-4-1-6-2-1-1-3-2-4-3 0-1-1-2-2-3h-1l1-1c1 0 5-2 6-3h-3-3c1 0 3 0 4-1 3-1 5-5 8-7l8-6v-1c-1 0-2 0-2 1 2-3 5-4 8-5l1-1c3-1 5-3 7-5s3-3 3-6h1c2-2 3-6 5-9 3-3 7-5 11-8 1-1 3-2 4-3-1 2-4 5-6 7s-3 4-5 6l12-9c-1 2-3 3-4 4-3 2-4 4-6 7l5-3c-2 2-4 4-6 7 3-1 5-3 7-4 2-2 3-3 4-5 3-3 5-6 7-10 3-5 3-11 5-17 1-4 2-7 3-11 2-8 3-17 4-25l1-3h1z" class="B"></path><path d="M83 414c2-2 3-6 5-9 1 1 2 2 2 4v1h0c-1 5-5 9-9 12-2 2-3 2-5 3-1 0-2 1-3 1h-2l1-1c3-1 5-3 7-5s3-3 3-6h1z" class="D"></path><path d="M79 420h1c1 0 1-1 2-2l7-10v1c0 1 0 1-1 2l-5 7c0 2-2 2-2 4h0c-2 2-3 2-5 3-1 0-2 1-3 1h-2l1-1c3-1 5-3 7-5z" class="F"></path><path d="M102 410c0 2-3 3-4 5 1 0 1 0 2-1v1l-2 2c-2 1-4 3-6 5 0 0-1 1-1 2h0-1c0 1-2 2-3 2-2 1-3 2-4 3l-9 9-4 7v-1c0-2 1-4 2-5l6-9h-1v-1c1-1 2-1 2-2h0 0v-1c5-3 11-6 15-9 3-2 6-5 8-7z" class="E"></path><path d="M71 426h2l-5 4c4-1 7-3 11-4v1h0 0c0 1-1 1-2 2v1h1l-6 9c-1 1-2 3-2 5v1l4-7 9-9c1-1 2-2 4-3v1c-3 2-5 6-7 10-4 5-7 12-13 16-3 2-6 4-10 4-2 0-4-1-6-2-1-1-3-2-4-3 0-1-1-2-2-3h-1l1-1c1 0 5-2 6-3h-3-3c1 0 3 0 4-1 3-1 5-5 8-7l8-6v-1c-1 0-2 0-2 1 2-3 5-4 8-5z" class="B"></path><path d="M70 433l7-4v1h1l-6 9c-1 1-2 3-2 5v1c-1 2-2 4-4 5h0c0-1 0-1-1-2v-1h0c-1 1-2 2-4 3l-1-1-2 2c-1 1-2 1-4 1h0c1 0 1 0 1-1 2-1 4-3 6-5 1 0 1-1 2-2 0-1 2-2 2-3l1-4c1-2 3-3 4-4z" class="P"></path><path d="M70 433l7-4v1c-2 1-4 4-7 6l-5 5 1-4c1-2 3-3 4-4z" class="J"></path><defs><linearGradient id="s" x1="76.759" y1="437.414" x2="50.961" y2="434.652" xlink:href="#B"><stop offset="0" stop-color="#5b5c5f"></stop><stop offset="1" stop-color="#8f8c8b"></stop></linearGradient></defs><path fill="url(#s)" d="M71 426h2l-5 4c4-1 7-3 11-4v1h0 0c0 1-1 1-2 2l-7 4c-1 1-3 2-4 4l-1 4c0 1-2 2-2 3-1 1-1 2-2 2h0c-3 2-6 6-10 4-1 0-2 0-3-1h0-2 0-1-1l1-1c1 0 5-2 6-3h-3-3c1 0 3 0 4-1 3-1 5-5 8-7l8-6v-1c-1 0-2 0-2 1 2-3 5-4 8-5z"></path><path d="M52 445c2-1 3-2 4-3l9-7c1-1 1-1 2-1l-1 1h0c1-1 2-1 3-2h1c-1 1-3 2-4 4l-4 4-1-1c-3 0-5 6-9 5z" class="G"></path><path d="M62 441l4-4-1 4c0 1-2 2-2 3-1 1-1 2-2 2h0c-3 2-6 6-10 4-1 0-2 0-3-1h0-2 0-1-1l1-1c1 0 5-2 6-3h1c4 1 6-5 9-5l1 1z" class="E"></path><path d="M52 445c4 1 6-5 9-5l1 1c-2 2-4 5-7 6v1h-2l-1 1h-4 0-2 0-1-1l1-1c1 0 5-2 6-3h1z" class="Q"></path><path d="M186 180l20-15v2l-6 9-2 4h1c1-1 1-3 3-4v1h0v1h0c0 1-1 1-1 2v2l-4 12c-1 4-3 9-1 12 1 2 3 4 5 6 3 3 7 7 9 11 0 2 1 3 0 5-2 7-9 8-14 12-2 2-3 4-4 6h-2l-1 1c-2-1-3-1-5 0-3-2-7-2-11-2l-13-3c-6-3-9-7-12-13v3l-1-5c0-2-1-5-2-7v-6-1c1-5 4-9 7-13l4-3 1-1c1 0 1-1 2-1h0v-1h2v-1h-3l-1-1v-1c2 0 3 0 4-1h-1 1v-1c1 0 2 0 3-1h-1c2-1 5-1 8-2h2c2-1 3-1 4-3 2 0 3-1 5-1v-1l4-1z" class="B"></path><defs><linearGradient id="t" x1="169.727" y1="225.967" x2="163.631" y2="218.627" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#62605f"></stop></linearGradient></defs><path fill="url(#t)" d="M161 215h1 0c1 2 2 4 4 5 3 3 6 6 10 9-4-1-7-3-10-5-2-3-4-5-5-9z"></path><path d="M156 221l1-5h0c0 4 1 7 4 9 2 2 5 4 8 4h1 0l3 2h-4c-5 0-10-3-14-7 2 0 2 1 3 2v-1l-1-1-1-3z" class="F"></path><defs><linearGradient id="u" x1="176.539" y1="209.631" x2="159.418" y2="203.829" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#u)" d="M167 192l1 1c-1 4-6 6-5 11-1 3-1 6 1 9 3 4 7 6 11 9-1 1-2 0-2 0-2 0-4 0-6-1 0-1-1-1-1-1-2-1-3-3-4-5h0-1 0c-3-4-2-9-1-14 1-3 3-5 5-7l2-2z"></path><path d="M182 183l3-1v2l-3 8c-1 5-2 9-4 14h0 0-2v1 1h-1l-1-1v-1c0-1-1-2-1-3l1-6v-1c0-5 5-10 8-13z" class="G"></path><path d="M177 202h1c1-2 1-5 2-7h0v3-2c1 0 0-1 0-2 1-1 1-2 2-2-1 5-2 9-4 14h0 0c-1-1-1-3-1-4z" class="L"></path><path d="M175 198l1-3v1c0 2 0 4 1 6 0 1 0 3 1 4h-2v1 1h-1l-1-1v-1c0-1-1-2-1-3l1-6v3-4h1v4h0v-2z" class="K"></path><path d="M175 198l1-3v1c0 2 0 4 1 6 0 1 0 3 1 4h-2v1c-1-2-1-7-1-9z" class="J"></path><path d="M189 183c1-1 2-3 3-4h0l-1 3c-1 2-2 4-2 6h1v1 4c-1 6-2 15-6 18l-2 1-1-1c1-1 1-2 1-3s0-3 1-4v-3c-1-2 0-5 0-7v-2c0-1 0-2 1-3v1l3-6c1 0 1 0 2-1h0z" class="D"></path><path d="M187 184c1 0 1 0 2-1h0c-1 3-2 7-3 10 0 2 0 4-1 6 0 0 0 1-1 1 0-1 0-3 1-4v-4c0-3 2-5 2-8z" class="G"></path><defs><linearGradient id="v" x1="189.942" y1="204.361" x2="181.73" y2="195.679" xlink:href="#B"><stop offset="0" stop-color="#1e1e1d"></stop><stop offset="1" stop-color="#4a4a4b"></stop></linearGradient></defs><path fill="url(#v)" d="M189 188h1v1 4c-1 6-2 15-6 18l-2 1-1-1c1-1 1-2 1-3s0-3 1-4v1l1-3h1c0 1-1 4-1 5 2-1 2-5 3-7s1-3 1-5l-1-1c0-1 1-1 1-2v-1l1-3z"></path><path d="M186 180l20-15v2l-6 9-2 4c-1 3-3 5-4 8v1c-1 1-2 3-4 4v-4-1h-1c0-2 1-4 2-6l1-3h0c-1 1-2 3-3 4h0c-1 1-1 1-2 1l-3 6v-1l1-4v-1-2l-3 1v-1-1l4-1z" class="E"></path><path d="M186 180h1c0 2-1 3-2 5v-1-2l-3 1v-1-1l4-1z" class="L"></path><path d="M193 183h1l2-2c1-3 2-4 4-5l-2 4c-1 3-3 5-4 8v-1c-1-1-1-3-1-4z" class="H"></path><path d="M190 188c1-4 3-7 5-10l-2 5c0 1 0 3 1 4v1 1c-1 1-2 3-4 4v-4-1z" class="M"></path><path d="M190 189h2v-2c1 0 1 1 2 2-1 1-2 3-4 4v-4z" class="N"></path><path d="M182 182v1c-3 3-8 8-8 13v1l-1 6c0 1 1 2 1 3v1l-1 1 1 1h-1l-2-1-3-3c1 3 2 6 5 7 3 2 10 1 13 1-1 1-2 2-4 3-2 2-5 2-8 1-4-1-8-5-10-9 0-1-1-2-1-3v-1c-1-5 4-7 5-11l-1-1-2 2h-1c0-1 0-1-1-2 0 1-1 1-2 2v-1h-3l-1-1v-1c2 0 3 0 4-1h-1 1v-1c1 0 2 0 3-1h-1c2-1 5-1 8-2h2c2-1 3-1 4-3 2 0 3-1 5-1z" class="L"></path><path d="M182 182v1c-3 3-8 8-8 13v1l-1 6c0 1 1 2 1 3v1l-1 1 1 1h-1l-2-1c1 0 1-1 1-1 1-1 0-3 0-4 0-3 0-6 1-8 1-3 3-6 4-9l-9 5h-1v1l-2 2h-1c0-1 0-1-1-2 0 1-1 1-2 2v-1h-3l-1-1v-1c2 0 3 0 4-1h-1 1v-1c1 0 2 0 3-1h-1c2-1 5-1 8-2h2c2-1 3-1 4-3 2 0 3-1 5-1z" class="C"></path><path d="M167 191v1l-2 2h-1c0-1 0-1-1-2 1 0 2 0 2-1h2z" class="D"></path><path d="M163 188c2-1 5-1 8-2h2s-2 1-3 1c0 1-1 1-1 1-2 2-4 3-6 3-1 1-2 1-2 2h-3l-1-1v-1c2 0 3 0 4-1h-1 1v-1c1 0 2 0 3-1h-1z" class="H"></path><defs><linearGradient id="w" x1="193.333" y1="226.207" x2="147.288" y2="215.438" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#w)" d="M163 192c1 1 1 1 1 2h1c-2 2-4 4-5 7-1 5-2 10 1 14h0c1 4 3 6 5 9v1l4 4h-1c-3 0-6-2-8-4-3-2-4-5-4-9h0l-1 5 1 3 1 1v1c-1-1-1-2-3-2l-3-6c-1-1-1-2-2-3 1 6 1 10 6 13 5 4 11 6 16 10-6 0-11-1-16-4 3 3 8 7 13 7h9c5 1 9 3 12 5l-1 1c-2-1-3-1-5 0-3-2-7-2-11-2l-13-3c-6-3-9-7-12-13v3l-1-5c0-2-1-5-2-7v-6-1c1-5 4-9 7-13l4-3 1-1c1 0 1-1 2-1h0v-1h2c1-1 2-1 2-2z"></path><path d="M163 192c1 1 1 1 1 2l-2 1h-1c-1 1-2 1-4 2l-1 1v-1l1-1c1 0 1-1 2-1h0v-1h2c1-1 2-1 2-2z" class="K"></path><path d="M145 213c0 1 0 2 1 3h1c0 5 0 8 1 13v3l-1-5c0-2-1-5-2-7v-6-1z" class="I"></path><path d="M156 197v1c-6 4-8 11-9 18h-1c-1-1-1-2-1-3 1-5 4-9 7-13l4-3z" class="B"></path><path d="M164 194h1c-2 2-4 4-5 7-1 5-2 10 1 14h0c1 4 3 6 5 9v1c-5-5-6-11-8-17 0 2 0 7-1 8h0l-1 5v-2c0-4 0-12 1-16 1-3 3-6 5-8l2-1z" class="H"></path><defs><linearGradient id="x" x1="159.985" y1="223.695" x2="164.898" y2="218.221" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#x)" d="M157 216c1-1 1-6 1-8 2 6 3 12 8 17l4 4h-1c-3 0-6-2-8-4-3-2-4-5-4-9z"></path><path d="M128 493v-2c0-2-1-4 0-6v1l1-1h0c1 4 3 7 6 10s7 7 9 11c1 2 3 6 3 8v1c0 1 0 3-1 5 0 1-1 2 0 4h-1v1c0 1-1 2-1 3-1 4-2 6-4 9v1l-3 3c-1 3-4 4-6 7l-1 1c-1 1-2 1-3 2v-1c-2 0-4 3-6 4l1 1-6 6h-1v-1h0c1-1 1-2 1-3l-7 7c-1 2-7 16-8 16h-1v-1c-1 2-2 6-2 9h0 0c0-1 1-2 1-3l3-3c2-3 6-7 8-8 3-1 10-6 12-6-2 2-6 3-8 4-6 3-14 11-16 18-1 2-1 6-1 8-1 3-2 5-2 8-1 7 2 15 4 22 1 2 2 4 1 6-2-3-3-6-4-10-1-2-1-5-3-7 0 2 1 4 1 6 0 4 1 7 2 11h-1l-1-1v-1h-1c0 2 1 3 1 5l3 11v1c0 1 0 2-1 2v1c1 0 1 1 1 1l-1 2v-1-1 2h0c-1 2 0 5 0 7 0-2 0-5-1-7-1-5-4-9-6-13 1-2-1-6-2-8v-1-1-2c-1 0-1 0-1-1h-1v-2c0-1-1-1-1-2s1-5 0-6v1c-1-1-1-2-1-2 0-2 0-5 1-7h-1v-1c1-5 2-9 4-13s5-8 7-12c0-1 1-2 1-4h-1l-1 2v-2l-2 3c0-1 0-1 1-2v-1l-1 1h0c-2 1-3 3-4 3l-1-1v1c-1 0-2 1-2 1h-1 0c0 1-1 2-1 2l-1-1c2-3 3-7 5-10 0-2 1-3 0-4-1 2-2 3-3 5l-2 3s0-1-1-1c0-1 0-1-1-2v3l-1 1h0l-2-1c-1-2-1-3-1-4 0-3 0-5 1-7h0v-1l-1 1h0-1c0-1 0-3 1-4v-1l12-14c1-2 2-3 3-5 2-2 3-4 5-6 3-6 6-13 10-18l1-2h1 0 0c1-1 3-3 5-3 1 0 2 0 3-1l-4 1c0-1 1-1 1-1 3-2 7-4 9-7 2-1 3-3 4-5 1-1 2-3 2-4l-1-3 1-3 1-1z" class="C"></path><path d="M133 532c0-1 0-1 1-2v-3l1 1s0 1 1 2c-1 1-2 2-3 2z" class="J"></path><path d="M118 555s2-2 2-3h1c-1 1-2 2-2 3v1l4-4h0c2-1 3-2 4-3 4-3 6-6 10-8-1 3-4 4-6 7l-1 1c-1 1-2 1-3 2v-1c-2 0-4 3-6 4l-6 7c1-2 3-4 3-6z" class="N"></path><path d="M143 521l1 1c-2 3-4 5-7 6l-2-1v1l-1-1v3c-1 1-1 1-1 2-1 1-2 2-4 2h0-1-1c2 0 2 0 3-1 0-1 0-1-1-2v-1c0-4 6-3 8-4 3-1 5-3 6-5z" class="M"></path><path d="M87 630c-1-2-1-4-1-7 0 0 2 10 2 11 1 2 1 4 2 5h1 0c0 4 3 8 4 11 1 1 1 3 2 3l-1 2v-1-1 2h0c-1 2 0 5 0 7 0-2 0-5-1-7-1-5-4-9-6-13 1-2-1-6-2-8v-1-1-2z" class="E"></path><path d="M98 547c1 0 2-1 2-1 1-1 2-1 2-2 2-2 6-4 8-5h1 3v1h1l-1 1 1 1c-1 1-3 1-5 1-3 2-8 5-12 4z" class="F"></path><path d="M114 540h1l-1 1c-3 0-6 3-9 3l1-1c1-1 3-2 5-3h3z" class="H"></path><path d="M93 581c1-1 3-2 4-4-1 3-1 6-2 9-4 9-9 16-11 25h-1v-1c1-5 2-9 4-13s5-8 7-12c0-1 1-2 1-4h-1l-1 2v-2z" class="I"></path><path d="M136 534h0 0c-4 3-8 5-12 8-2 1-4 2-7 3-3 2-5 4-8 6h-1c0-1 1-1 1-2 0-2 2-3 3-4 3-2 7-3 10-5 3-1 5-3 8-4 2-1 4-1 6-2z" class="B"></path><path d="M88 575h1c1-1 3-4 4-6l6-10c1-2 2-4 4-6 1-2 3-5 5-6-1 2-2 4-2 6-2 8-3 17-9 24-1 0-1 0-2 1l-4 4c-2 1-3 3-4 3l-1-1c1-1 3-2 5-4 8-6 12-16 13-27l-12 19c-1 2-2 4-5 5 1-1 1-2 1-2z" class="M"></path><defs><linearGradient id="y" x1="99.25" y1="573.09" x2="85.03" y2="569.878" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#y)" d="M88 575s0 1-1 2c3-1 4-3 5-5l12-19c-1 11-5 21-13 27-2 2-4 3-5 4v1c-1 0-2 1-2 1h-1 0c0 1-1 2-1 2l-1-1c2-3 3-7 5-10h1v-2h1z"></path><path d="M121 554l1 1-6 6h-1v-1h0c1-1 1-2 1-3l-7 7c-1 2-7 16-8 16h-1v-1c-1 2-2 6-2 9h0 0c0-1 1-2 1-3l3-3c2-3 6-7 8-8 3-1 10-6 12-6-2 2-6 3-8 4-6 3-14 11-16 18-1 2-1 6-1 8-1 3-2 5-2 8-1 7 2 15 4 22 1 2 2 4 1 6-2-3-3-6-4-10-1-2-1-5-3-7 0 2 1 4 1 6 0 4 1 7 2 11h-1l-1-1v-1h-1c-1-4-1-8-1-12 0-5 0-10 1-15 0-3 1-5 2-8l3-12c0-3 2-6 2-10h0 0l1 1c0 1-1 2 0 3 4-5 5-12 8-16h0c3-3 6-6 9-8 0 2-2 4-3 6l6-7zm7-61v-2c0-2-1-4 0-6v1l1-1h0c1 4 3 7 6 10s7 7 9 11c0 1 2 5 2 7h0c1 1 0 3 0 5v2c-1 0-1 1-1 2h0l-1-1 1-3h-1v1l-1 1v1c-1 2-3 4-6 5-2 1-8 0-8 4v1c1 1 1 1 1 2-1 1-1 1-3 1-1 0-4 2-5 3-1 0-2 1-3 1v-2l-1-1v1c-1 0-1 1-1 2h0c-1 0-1 0-2-1 1-1 1-2 2-2h3v-1c-1-1-2 0-3 0h0-1l-2 2c0 1 1 2 0 3h-3-1c-2 1-6 3-8 5 0 1-1 1-2 2 0 0-1 1-2 1s-2 1-3 0c-3 3-7 7-9 11-1 2-2 5-3 9l11-10c0 2-2 3-2 5-1 2-2 5-2 8-1 1-1 3-2 3-1-1 0-1-1-2 0 1-1 1-1 2-1 2-2 3-3 5l-2 3s0-1-1-1c0-1 0-1-1-2v3l-1 1h0l-2-1c-1-2-1-3-1-4 0-3 0-5 1-7h0v-1l-1 1h0-1c0-1 0-3 1-4v-1l12-14c1-2 2-3 3-5 2-2 3-4 5-6 3-6 6-13 10-18l1-2h1 0 0c1-1 3-3 5-3 1 0 2 0 3-1l-4 1c0-1 1-1 1-1 3-2 7-4 9-7 2-1 3-3 4-5 1-1 2-3 2-4l-1-3 1-3 1-1z" class="B"></path><path d="M79 578c1-1 1-3 2-4 0 1 0 2 1 3l1 1-2 3s0-1-1-1c0-1 0-1-1-2zm-4-1c0-3 0-5 1-7v2c1 3 0 7 3 9l-1 1h0l-2-1c-1-2-1-3-1-4z" class="F"></path><path d="M139 503c0 1 1 2 1 3s1 2 0 3v2c0 3 0 5-2 7 0 1 0 1-1 1 1-3 1-5 1-7v-5l1-1c0-1-1-2 0-3z" class="I"></path><path d="M87 551h1 1c-6 6-9 12-13 19v-1l-1 1h0-1c0-1 0-3 1-4v-1l12-14z" class="H"></path><path d="M122 516l1 1c-1 0-2 1-2 2-3 3-6 6-10 9-3 2-5 5-8 8 1-3 3-8 6-10 2-1 4-4 6-5 3-2 5-3 7-5z" class="C"></path><defs><linearGradient id="z" x1="138.244" y1="514.027" x2="125.562" y2="494.135" xlink:href="#B"><stop offset="0" stop-color="#1f201f"></stop><stop offset="1" stop-color="#4d4b4e"></stop></linearGradient></defs><path fill="url(#z)" d="M128 486l1-1h0c1 4 3 7 6 10h-1c-1-1-2-2-2-3l-1-2c-1 0-1-1-2-2v-1c0 1-1 4 1 5s3 4 4 5v1h-1 0l1 1 2 2h2v1l1 1c-1 1 0 2 0 3l-1 1v-1c-2 2-1 7-3 10 0 2 0 4-1 5h-1c2-4 1-8 1-12l-1 4h0v-3l-1 1c0-2 1-4 0-6 0-1-1-3-1-4s-1-1-1-2c0-2-1-3-1-4-1-3-1-6-1-9z"></path><path d="M135 516c1-3 1-7 1-10-1-2-3-5-2-7l2 2h2v1l1 1c-1 1 0 2 0 3l-1 1v-1c-2 2-1 7-3 10z" class="J"></path><path d="M136 501h2v1l1 1c-1 1 0 2 0 3l-1 1v-1c0-1-2-4-2-5z" class="C"></path><path d="M134 499l-1-1h0 1v-1c-1-1-2-4-4-5s-1-4-1-5v1c1 1 1 2 2 2l1 2c0 1 1 2 2 3h1c3 3 7 7 9 11 0 1 2 5 2 7h0c1 1 0 3 0 5v2c-1 0-1 1-1 2h0l-1-1 1-3h-1v1l-1 1v1c-1 2-3 4-6 5-2 1-8 0-8 4v1c1 1 1 1 1 2-1 1-1 1-3 1-1 0-4 2-5 3-1 0-2 1-3 1v-2l-1-1v1c-1 0-1 1-1 2h0c-1 0-1 0-2-1 1-1 1-2 2-2h3v-1c-1-1-2 0-3 0h0-1l-2 2c0 1 1 2 0 3h-3-1c-2 1-6 3-8 5 0 1-1 1-2 2 0 0-1 1-2 1s-2 1-3 0c2-3 4-10 9-12 3-1 5-3 8-4s6-2 10-3c7-2 14-3 20-9-1-2-1-4-1-6s0-3-1-4c1-1 0-2 0-3s-1-2-1-3l-1-1v-1h-2l-2-2z" class="H"></path><path d="M140 506c1 1 2 1 3 2v1c1 2 2 4 1 7 0 1-1 3-2 3-1-2-1-4-1-6s0-3-1-4c1-1 0-2 0-3z" class="J"></path><path d="M140 506c1 1 2 1 3 2v1c-1 1 0 4-1 6v1h-1v-3c0-2 0-3-1-4 1-1 0-2 0-3z" class="M"></path><path d="M128 493v-2c0-2-1-4 0-6v1c0 3 0 6 1 9 0 1 1 2 1 4 0 1 1 1 1 2s1 3 1 4c1 2 0 4 0 6l1-1v3c0 2 0 4-1 6s-1 3-3 4h0v-3h0c-1 2-2 3-4 4-1 0-1 0-2 1h0c0-2 1-3 1-4v-1c-3 5-6 7-11 10 1-1 2-3 3-4 2-2 4-4 5-6v-1c0-1 1-2 2-2l-1-1c-2 2-4 3-7 5-2 1-4 4-6 5h-1c-2 2-4 5-6 8l-3 3c-3 4-5 11-10 14h-1-1c1-2 2-3 3-5 2-2 3-4 5-6 3-6 6-13 10-18l1-2h1 0 0c1-1 3-3 5-3 1 0 2 0 3-1l-4 1c0-1 1-1 1-1 3-2 7-4 9-7 2-1 3-3 4-5 1-1 2-3 2-4l-1-3 1-3 1-1z" class="F"></path><path d="M128 493c1 2 1 10 0 13 0-2 0-5-1-7v1l-1-3 1-3 1-1z" class="J"></path><path d="M108 526l1-2c1-1 1-1 1-2l1-1c1-1 10-6 12-6l-1 1c-2 2-4 3-7 5-2 1-4 4-6 5h-1z" class="I"></path><path d="M127 500v-1c1 2 1 5 1 7-1 3-3 6-7 8-1 1-5 1-6 2l-4 1c0-1 1-1 1-1 3-2 7-4 9-7 2-1 3-3 4-5 1-1 2-3 2-4z" class="B"></path><path d="M131 501c0 1 1 3 1 4 1 2 0 4 0 6l1-1v3c0 2 0 4-1 6s-1 3-3 4h0v-3h0c-1 2-2 3-4 4-1 0-1 0-2 1h0c0-2 1-3 1-4v-1c0-1 2-4 3-5l-3 8 3-3c3-6 5-11 4-17v-2h0z" class="I"></path><path d="M132 505c1 2 0 4 0 6l1-1v3c0 2 0 4-1 6-1-2-1-5 0-7v-7z" class="F"></path><defs><linearGradient id="AA" x1="184.687" y1="542.851" x2="137.599" y2="681.365" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#AA)" d="M140 538v-1c2-3 3-5 4-9 0-1 1-2 1-3v-1h1c-1-2 0-3 0-4 1-2 1-4 1-5 0 1 0 2 1 3 0 3-1 6-1 9 0 5 1 11-1 17h0 0v1h-1v-2c-1 4-2 8-4 12h0c-2 4-4 9-6 12-1 2-2 4-2 5 0 2 0 4-1 5 0 1 0 3-1 4v1 1c-1 7-2 15 3 20 2 2 4 3 6 4h0-1l1 1c1 1 2 2 3 4 1 0 2 1 2 2 1 1 2 1 3 2 3 2 7 3 11 5h2c-2 2-7 2-9 2-3 0-5 0-7-1-3-1-9-6-12-5h0c0 1-1 2 0 3 2 1 4 3 7 3l2 1c-2 1-4 1-6 1h-7 3v1 1l11 4 7 3c1 1 2 2 4 3h2l1 1-1 1-3-1v1c8 5 19 10 29 13l2 1 1 1 3 1c-3 0-5-1-7-1h-3 0c2 0 3 1 5 1h0c2 1 5 0 7 1h-1l1 1h6 5c1-1 2-1 3-1 2 1 3 0 5 0 2-1 4-3 7-4l-2 2s-1 0-1 1h0-1s-1 1-2 1c-2 0-3 1-5 2h1c1 1 1 1 2 0h3 3c1 0 4-1 5-2v1c-1 1-2 2-4 2h-1c-1 1-2 0-3 1-2 1-6-2-9 1l1 1h-1c0-1-1-1-2-1h0l-17-5c-1 0-4-2-5-1h-1v1h1l7 2h2v1h-2c1 1 5 1 7 2l4 2c-5-1-9-3-14-4-2 0-4-1-5-1-2 1-7-1-8-1-7-3-13-7-20-11-4-2-9-3-13-5-9-3-18-9-23-16l-3-6h0v5h0l-1 2c0 1 1 3 0 4h0v2c-1-1-1-2-1-3l-1 1h0c-1 1 1 5 2 6 0 1 1 2 1 3 1 1 2 3 2 4l4 8v-1c-1 0-1-1-2-2l-2-5v4c1 1 1 2 1 3 0 0-1-1-1-2 0 2 1 3 1 4s1 1 1 1c0 1 1 1 1 1s0 1 1 1c2 1 3 5 4 7 3 3 5 5 7 8h0v1c4 4 9 8 13 12-10-2-16-8-22-15h-1v2h-2c0-1-1-2-1-4l-2-5c0-2-1-4-3-6 0-1-1-2-2-3 0-1-1-3-1-3-1-3-2-5-3-8-1-2-3-5-3-7 1-2 0-4-1-6-2-7-5-15-4-22 0-3 1-5 2-8 0-2 0-6 1-8 2-7 10-15 16-18 2-1 6-2 8-4-2 0-9 5-12 6-2 1-6 5-8 8l-3 3c0 1-1 2-1 3h0 0c0-3 1-7 2-9v1h1c1 0 7-14 8-16l7-7c0 1 0 2-1 3h0v1h1l6-6-1-1c2-1 4-4 6-4v1c1-1 2-1 3-2l1-1c2-3 5-4 6-7l3-3z"></path><path d="M130 549v1c-2 2-4 4-5 6h-1v-2c0-1 1-2 2-3h0l-4 4-1-1c2-1 4-4 6-4v1c1-1 2-1 3-2z" class="J"></path><path d="M113 660c1 2 3 4 3 6 0 1 1 1 1 2v2h-2c0-1-1-2-1-4l-2-5 1-1z" class="I"></path><path d="M123 623h1c1 0 1 0 2 1 1 0 1 1 2 1h0l3 3h0s-1 0-2 1c-1 0-3-2-4-2 0 0 0-1-1-1h1v-1l-3-1s1 0 1-1z" class="M"></path><path d="M123 623h1l3 3h-1-1v-1l-3-1s1 0 1-1z" class="I"></path><path d="M122 624l-4-3h-1c-1-1-2-2-3-4-2-2-4-5-4-8 1 1 1 3 2 4 3 4 7 8 11 10 0 1-1 1-1 1zm-13 6s0-1-1-1v-1c-3-6-2-13-1-19 0-1 0-3 1-5v5 12 2c1 1 1 2 1 3s1 3 0 4h0z" class="J"></path><defs><linearGradient id="AB" x1="141.162" y1="616.242" x2="152.532" y2="617.55" xlink:href="#B"><stop offset="0" stop-color="#3e3d3d"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#AB)" d="M159 621h-4c-6-1-11-4-15-7v-1l2 1v-1l-1-1c-1 0-1 0-2-1h1 2c0 1 1 1 1 1 1 0 2 1 2 2 1 1 2 1 3 2 3 2 7 3 11 5z"></path><path d="M149 638h4 0v1c8 5 19 10 29 13l2 1 1 1 3 1c-3 0-5-1-7-1l-8-3-6-3c-3 0-5-1-8-2-2-1-4-3-7-4-1-1-3-1-5-2 1 0 1 0 2-1h0c0 1 1 1 1 1h2 0 0c0-1-1-1-1-1-1 0-1-1-2-1z" class="K"></path><path d="M128 625l2 1c1 0 1 1 2 1l11 4 7 3c1 1 2 2 4 3h2l1 1-1 1-3-1h0-4-2c-1-1-4-2-5-2h-1c2 0 5 1 7 1h0l-5-2c-2 0-6-1-7-2-1 0-1-1-2-1-2-1-3-3-5-3 1-1 2-1 2-1h0l-3-3h0z" class="C"></path><path d="M103 641c1-3-1-6-1-9-2-9-3-19-1-28 2-8 7-17 12-24v2c-1 1-2 3-3 4-2 6-5 13-7 20-2 9-1 18 1 27l6 18 3 9-1 1c0-2-1-4-3-6 0-1-1-2-2-3 0-1-1-3-1-3-1-3-2-5-3-8z" class="B"></path><path d="M140 538v-1c2-3 3-5 4-9 0-1 1-2 1-3v-1h1c-1-2 0-3 0-4 1-2 1-4 1-5 0 1 0 2 1 3 0 3-1 6-1 9 0 5 1 11-1 17h0 0v1h-1v-2c-1 4-2 8-4 12 0-2 1-3 2-5v-2c1-1 1-2 1-2 0-1 0-2 1-3v-2-5c1-2 1-4 1-6 0 0-1 0-1 1s-1 3-2 4l-5 8c-1 2-3 4-3 5 1 0 1-1 2 0v1c-1 1-2 3-3 4h1c1-1 3-6 5-6v2l-4 6-1-1h1-2c-3 3-5 6-8 8-1 1-1 1-1 2-3 3-8 5-11 6-1 1-2 2-4 2h0 0l1-2c1-2 4-4 6-6 4-2 7-5 9-7 5-5 8-9 12-14 1-2 2-3 2-5z" class="N"></path><path d="M110 572c1-1 2-1 2-2 1 0 2 0 2-1 1-2 3-3 5-5 3-2 6-4 8-6l9-10c0 1 0 1-1 2 0 1-1 2-2 4l-3 3c-4 4-9 7-13 11 3-1 5-3 8-4-3 3-8 5-11 6-1 1-2 2-4 2h0z" class="J"></path><path d="M114 572h0c3 0 7-2 10-4l-2 2h0c-1 0-2 1-2 2-1 1-2 2-3 2l-1 1h0c-1 1-3 2-3 3v1 1c-5 7-10 16-12 24-2 9-1 19 1 28 0 3 2 6 1 9-1-2-3-5-3-7 1-2 0-4-1-6-2-7-5-15-4-22 0-3 1-5 2-8 0-2 0-6 1-8 2-7 10-15 16-18z" class="N"></path><path d="M148 518l1-6c3-8 10-18 18-22 5-3 12-5 18-3 3 1 5 2 7 5 1 2 2 4 3 7 1 2 2 5 2 7 1 3 0 7 0 10-1 5-9 19-8 23 0 1-1 2-1 3-1-2 0-2 0-4h-1c-1 3-3 6-4 9s-2 6-3 10c-2 4-4 8-5 13l1 1c0-1 0-1 1-1 0 1 0 1-1 1v1h1c0-1 0-2 1-3h1c0 2-1 3-1 4-1 2-1 4-2 5h0c0 2 0 4-1 5h0c-3 5-7 10-12 14-3 2-6 4-9 7l15-5-2 1c1 0 1 1 1 1h0l-9 2c2 0 4 0 6 1h-6l1 1h2c-6 0-11 1-16 2v1l-6-1h0c-2-1-4-2-6-4-5-5-4-13-3-20v-1-1c1-1 1-3 1-4 1-1 1-3 1-5 0-1 1-3 2-5 2-3 4-8 6-12h0c2-4 3-8 4-12v2h1v-1h0 0c2-6 1-12 1-17 0-3 1-6 1-9z" class="M"></path><path d="M170 495h0v2h0c2-1 2-1 4-1 0 1 1 2 1 3s-1 2-1 2l-4 3c-1-2-2-4-2-6 0-1 0-2 2-3z" class="N"></path><path d="M165 496c1 2 0 2 0 4-1 3-1 6-2 9s-3 6-4 10c-1 2 0 4-1 6 0 1 0 2 1 3 0 1 1 4 1 5h0c-1-1-2-3-2-5v-2s-2-2-3-2c0-1-1-1-1-2 1 0 2 1 3 1 1-2 1-5 2-6l3-11c1-1 2-5 2-6 0 0-1 0-1-1l2-3zm21 6h1c1 1 1 2 2 3h0c-1 2 1 6 2 8 1 4-1 10-3 14h0v-1l1-3v-3c1 0 1-1 1-1v-1c1-1 0-2 0-3v1c-1 2-1 4-3 4h0v-2l-1-1s1-2 1-3v-8h0v-2-1l-1-1z" class="J"></path><path d="M187 503v1 2h0v8c0 1-1 3-1 3-1 3-2 6-4 8-3 5-7 9-10 13 1-5 4-9 6-13 4-7 7-14 9-22zm-1-1v-5-1h0l3 3c1 1 1 4 2 5l1-1v-3c1 0 1 1 2 2l1 2c1 2 0 5 1 7v4l1 1c-1 5-9 19-8 23 0 1-1 2-1 3-1-2 0-2 0-4h-1c-1 3-3 6-4 9s-2 6-3 10c-2 4-4 8-5 13l1 1c0-1 0-1 1-1 0 1 0 1-1 1v1h1c0-1 0-2 1-3h1c0 2-1 3-1 4-1 2-1 4-2 5h0c0 2 0 4-1 5h0-1c-1 1-1 2-2 2-1 3-3 5-6 8-2 2-4 4-7 5-3 3-7 6-12 7h-3c0-1 0-1-1-2h1c3-1 6-3 8-4v-1c4-2 9-6 11-10 3-6 6-12 7-19 0-5-1-11 0-17 1-7 3-15 9-19-1 2-2 4-3 5-2 4-2 9-3 13-1 3-1 5-1 8h1l6-12c2-3 2-7 4-10l3-6v-1c1-1 1-2 2-3h0c2-4 4-10 3-14-1-2-3-6-2-8h0c-1-1-1-2-2-3h-1z" class="B"></path><path d="M175 573v1c0 2-1 4-1 5h1c0 1 0 2-1 3h-1 0c-2 1-2 2-3 3 1-4 3-8 5-12z" class="F"></path><path d="M175 570l1 1c0-1 0-1 1-1 0 1 0 1-1 1v1h1c0-1 0-2 1-3h1c0 2-1 3-1 4-1 2-1 4-2 5h0c-1 0 0 1-1 1h-1c0-1 1-3 1-5v-1c-1-1 0-2 0-3z" class="N"></path><path d="M175 579c1 0 0-1 1-1 0 2 0 4-1 5h0-1c-1 1-1 2-2 2-1 3-3 5-6 8-2 2-4 4-7 5h0c1-2 3-3 4-5 3-2 5-5 7-8 1-1 1-2 3-3h0 1c1-1 1-2 1-3z" class="L"></path><path d="M186 502v-5-1h0l3 3c1 1 1 4 2 5l1-1v-3c1 0 1 1 2 2l1 2c1 2 0 5 1 7v4l1 1c-1 5-9 19-8 23 0 1-1 2-1 3-1-2 0-2 0-4h-1c0-1 1-3 2-4l2-7c1-4 4-8 3-12v-1c-1-3-4-6-5-9h0c-1-1-1-2-2-3h-1z" class="I"></path><path d="M192 503v-3c1 0 1 1 2 2l1 2c1 2 0 5 1 7v5h-1c-2-4-1-8-3-12v-1zm-28 55c1-3 2-5 3-7 0 5-3 9-4 14-1 3-2 7-3 10v5c2-4 5-9 6-14 0 9-2 21-9 28-1 1-3 3-5 4h0v1c-2 1-5 3-8 4h-1c1 1 1 1 1 2h3c5-1 9-4 12-7 3-1 5-3 7-5 3-3 5-5 6-8 1 0 1-1 2-2h1c-3 5-7 10-12 14-3 2-6 4-9 7l15-5-2 1c1 0 1 1 1 1h0l-9 2c2 0 4 0 6 1h-6l1 1h2c-6 0-11 1-16 2-2 0-4-2-6-3-3-2-5-5-5-8-1-4 3-9 6-13 2-3 5-6 7-10 4-5 9-10 12-15l1 1 1-1h2z" class="B"></path><path d="M137 600h2l1 1c4 1 9-2 12-3h0v1c-2 1-5 3-8 4h-1c1 1 1 1 1 2h3-3c-3-1-5-3-7-5z" class="I"></path><path d="M143 589c2-2 4-3 5-5s3-7 5-8v1s-1 1-1 2v1c-1 0-1 2-2 2v1h1c0 2 0 2-1 3-1 0-1 1-2 1l1 1-2 2c-3 2-9 5-10 9-1-2-1-4 0-6l6-4z" class="F"></path><path fill="#b4b3b5" d="M161 559l1-1h2c-2 5-4 8-7 13-1 2-2 4-4 6h0v-1c-2 1-4 6-5 8s-3 3-5 5l-6 4c-1 2-1 4 0 6v1c2 2 4 4 7 5h3c5-1 9-4 12-7 3-1 5-3 7-5 3-3 5-5 6-8 1 0 1-1 2-2h1c-3 5-7 10-12 14-3 2-6 4-9 7l15-5-2 1c1 0 1 1 1 1h0l-9 2c2 0 4 0 6 1h-6l1 1h2c-6 0-11 1-16 2-2 0-4-2-6-3-3-2-5-5-5-8-1-4 3-9 6-13 2-3 5-6 7-10 4-5 9-10 12-15l1 1z"></path><path d="M143 583l2-1c0 2-2 3-2 4v3l-6 4h0 0c1-4 4-7 6-10z" class="D"></path><defs><linearGradient id="AC" x1="158.235" y1="574.001" x2="147.124" y2="570.966" xlink:href="#B"><stop offset="0" stop-color="#111115"></stop><stop offset="1" stop-color="#484744"></stop></linearGradient></defs><path fill="url(#AC)" d="M161 559l1-1h2c-2 5-4 8-7 13-1 2-2 4-4 6h0v-1c-2 1-4 6-5 8s-3 3-5 5v-3c0-1 2-2 2-4l-2 1 9-12c3-4 7-7 9-12z"></path><defs><linearGradient id="AD" x1="170.951" y1="548.072" x2="138.724" y2="518.388" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#AD)" d="M148 518l1-6c3-8 10-18 18-22 5-3 12-5 18-3 3 1 5 2 7 5 1 2 2 4 3 7 1 2 2 5 2 7 1 3 0 7 0 10l-1-1v-4c-1-2 0-5-1-7l-1-2c-1-1-1-2-2-2v3l-1 1c-1-1-1-4-2-5l-3-3c0-1-1-2-2-3 0-1-2-2-3-2-1-1-3-1-4-1-2 0-7 2-9 3l-3 3-2 3c0 1 1 1 1 1 0 1-1 5-2 6l-3 11c-1 1-1 4-2 6-1 0-2-1-3-1 0 1 1 1 1 2 1 0 3 2 3 2v2c0 2 1 4 2 5h0c1 1 2 2 2 3l2 6c1-1 2-3 2-4 1-5 0-10 1-15 0 0 0-1 1-2v1l3-6c2-4 5-8 8-11-3 7-10 14-9 22h1c2-4 4-6 7-9l-6 9c-2 6-2 12-4 18l-3 6c-2 2-4 5-5 7-3 5-8 10-12 15-2 4-5 7-7 10-3 4-7 9-6 13 0 3 2 6 5 8 2 1 4 3 6 3v1l-6-1h0c-2-1-4-2-6-4-5-5-4-13-3-20v-1-1c1-1 1-3 1-4 1-1 1-3 1-5 0-1 1-3 2-5 2-3 4-8 6-12h0c2-4 3-8 4-12v2h1v-1h0 0c2-6 1-12 1-17 0-3 1-6 1-9z"></path><path d="M150 533h0l1 1c0 2 1 3 0 5l-1-1c0-1-1-3 0-5z" class="J"></path><path d="M154 522v-2-1-1h1l-1-1h0l6-8c1-2 1-3 2-5h0l-1-1h1 0v-1h-1l-4 3h0c0-1 1-2 2-3 2-1 3-2 4-3 0 1 1 1 1 1 0 1-1 5-2 6l-3 11c-1 1-1 4-2 6-1 0-2-1-3-1z" class="F"></path><path d="M162 536l2 6c1-1 2-3 2-4 1-5 0-10 1-15 0 0 0-1 1-2v1l3-6c2-4 5-8 8-11-3 7-10 14-9 22h1c2-4 4-6 7-9l-6 9c-2 6-2 12-4 18l-3 6c-2 2-4 5-5 7-3 5-8 10-12 15-2 4-5 7-7 10-3 4-7 9-6 13 0 3 2 6 5 8 2 1 4 3 6 3v1l-6-1h0c-2-1-4-2-6-4-5-5-4-13-3-20v-1-1c1-1 1-3 1-4 1-1 1-3 1-5 0-1 1-3 2-5 2-3 4-8 6-12h0c2-4 3-8 4-12v2h1v-1h0c-2 17-13 31-13 48 2-4 2-7 3-11 2-6 5-12 8-18 1-2 2-6 3-8 1 0 1 0 2 1h0c0-1 0-1 1-2h4 0c1-2 1-4 2-6v1c0 1-1 3-1 5 1 0 1 0 1 1h0c2-4 2-7 3-10 1 0 1 1 1 2h0c2-3 1-7 2-10v-1zm49-241c5-1 9-1 13 1h1c2 1 4 2 5 4l1 1v1h0c2 2 4 5 5 8 1 1 1 4 1 5l-3-4-1-1v1h0c-1 2 0 4 0 6v3c0 3 0 5 1 7 0 1-1 12-1 12v1h-1l-1-2c0 2 0 3 1 4v7c-1 5-3 10-4 15-1 8-2 17-5 25l-1 4-1 2-3 9v5l-1 2c0 10 0 21 1 30 1 20 3 40 6 59 1 8 2 16 4 23 0 2 1 5 3 7 1 1 2 1 4 1 5 0 9 1 11 7v4h0c0 2-1 3-3 4s-4 1-6 1c-5 0-11 0-15-1-2-1-2-1-3-2-2-4-2-8-2-12l-3-6-2-5c-1-5-4-11-5-16v-3c0-1 0-2 1-4-2-5-2-10-4-15-1-4-3-9-4-13-2-6-4-11-6-16h-1v-1c-1-2-3-4-5-6h0c3 1 6 2 9 4h0v1c1 1 2 4 2 5v1h0c1-1 1-1 2-1 2-2 3-4 4-6 2-6 0-14-2-20-3-6-9-10-15-12-5-2-10-2-15-4-8-3-12-9-15-17l1 1c2 4 8 13 12 14h3c0-1-1-2-1-3h0-1c-1-2-2-3-3-5l-4-5c-2 0-2-1-3-2h0-1c-1 0-2-3-3-4-3-4-5-10-7-15v-1c-1-4 0-9-2-12 1-4 3-7 4-10l1-1c1-5 3-8 6-12v1c-1 2-2 3-3 6l1 1 1-1h0l-2 7 1 1c1-4 3-9 5-13 1-4 3-9 5-12 2-1 3-3 5-5 1 2 0 3-1 5l-3 11v1h0c1-1 2-1 3-2h0c4-4 10-6 15-8 1-2 2-2 3-3v-1l1-1c-1 1-2 1-3 1h0l-4 2c2-2 4-3 6-5 2-1 4-5 5-8 2-3 2-9 6-12 0-1 3-4 3-5l3-3h0l2-1v-1-1l2-1v-1h0z" class="B"></path><path d="M217 403h0v2c1 1 1 2 1 4l-1 2c0-1 0-1-1-2 0-2 0-4 1-6z" class="F"></path><path d="M208 498l2 10v-1c-1-1-1-1-1-2h-2v-3c0-1 0-2 1-4zm5 19c1 2 2 5 2 7v1l-1 1-2-5c1-1 1-2 1-4z" class="N"></path><path d="M230 338l1-1v1c0 2 0 3 1 4v7l-2 1v2h0v-2l-1 3c0-3 0-8 1-11 0-1 1-3 0-4z" class="F"></path><path d="M217 326h1l1 1c0 1 1 2 2 2v1c2 2 2 5 3 7-2-2-4-8-8-9h1c1 3 2 5 3 7h0c0-1-1-3-2-4s-3-4-3-5l1 1h0c1 0 2 1 2 1h1l-2-2z" class="I"></path><path d="M210 341c4 0 9 1 12 3l2 2c-1-1-6-2-7-1 0 1 2 1 2 2l-2-1c0-1-1-1-1-2-1 0-2-1-2-1h-3c0-1 0-1-1-1h2c-1-1-2-1-2-1h0zm-41 64c4 1 9 5 11 9-2-1-6-4-7-4h-1c-1-2-2-3-3-5zm38 100h2c0 1 0 1 1 2v1l3 9c0 2 0 3-1 4-1-5-4-11-5-16z" class="F"></path><path d="M209 319c-1-2-3-4-3-6s-1-6 1-8v1l1 4v2l1 1v1l1 2c1 1 2 1 2 2v1c0-1 0-1-1-1h-1v1h0c1 2 2 4 2 6v2c-1-4-1-6-3-8z" class="O"></path><path d="M211 295h0c1 1 2 1 2 1h5c-1 0-1 0-2 1h2 0-4v1c-2 0-3 0-4 1s-1 1-1 2v1c-2 2 0 6 0 9-1-1-1-1-1-2h0c-1-1 0-2 0-3h-1c1 1 1 2 1 4l-1-4v-1c-2 2-1 6-1 8s2 4 3 6l-1-1c-2-3-4-8-3-12v-1h-1c-1 1-2 3-2 5v-1-2l-1 1h0c0-1 3-4 3-5l3-3h0l2-1v-1-1l2-1v-1z" class="I"></path><path d="M217 403c0-2 1-4 1-6v-4 1c0-3 0-7 1-9 0-1-1-2 0-3h0l1 2h0v-5l1 4v5c1-1 2-3 2-4v5l-1 4-1 2-3 9v5c0-2 0-3-1-4v-2h0z" class="O"></path><path d="M221 383v5c1-1 2-3 2-4v5l-1 4c-2-2-1-7-1-10z" class="N"></path><path d="M218 404v-3c0-1 0-3 1-5v-6h1c0 1 0 3 1 4v1l-3 9z" class="D"></path><path d="M229 353l1-3v2h0v-2l2-1c-1 5-3 10-4 15-1 8-2 17-5 25v-5l3-16v-6l3-6v-3zm-27-43c0-2 1-4 2-5h1v1c-1 4 1 9 3 12l3 12h-1v-1-1c0-1 0-2-1-2v-1-2c-1-1-1-2-2-3 0 0-1 0-1-1h-1 0v2h0v2 1c1 0 1 1 1 1 0 1 0 1 1 2v1 1l1 1v1c0 2 1 3 1 4h-2c0-1-1-2-1-3-2-2-3-4-3-7 0-2-1-4-1-6v-9z" class="C"></path><defs><linearGradient id="AE" x1="195.211" y1="423.245" x2="181.492" y2="401.571" xlink:href="#B"><stop offset="0" stop-color="#121111"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#AE)" d="M171 401c2 0 4 2 6 3l4 1c2 1 3 1 5 1 1 1 5 3 7 3 5 4 8 8 10 14-3-3-6-6-10-9 2 3 5 6 6 10l-16-13 3 5c-5-2-8-7-12-11-2-2-3-3-5-3 1-1 1-1 2-1z"></path><path d="M171 401c2 0 4 2 6 3l4 1v1c-2 0-3-1-5-1h-2c-2-2-3-3-5-3 1-1 1-1 2-1z" class="C"></path><path d="M190 356c0-1 2-3 3-4s4-4 4-6h0v-3l1-1v3-1h1 0v-3h0v-2h1 0l1 1h0v1c0 2 2 4 4 5 1 2 4 3 6 4h-1 0c-2-1-3-1-4-2h-1c-1-1-3-3-4-5 0-1-1-1-1-2v1h0l1 2c1 1 1 2 1 2l2 2s1 2 2 2h1 1l1 1h1l1 1c-3-1-6-2-8-4h-1c2 2 4 4 7 5l1 1c-1 0-2-1-3-1-2-1-6-4-6-6h0l-1-2c-1 0-1 1-2 1v1 1l1-1 1 1c0 1 0 2-1 4 0 1 1 3 0 3v2c-1 2-2 3-3 5s-2 3-1 6h0c1 1 1 4 2 5v-2c-1-1-1-3 0-4v-2c0-1 0-2 1-3h0 0c0 2 0 4-1 6 0 2 1 4 1 6-2-1-3-5-4-7l1 8c-3-2-6-7-7-10v-1c0-2 2-7 2-8z" class="F"></path><defs><linearGradient id="AF" x1="233.45" y1="311.532" x2="207.993" y2="305.895" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#3b3a3a"></stop></linearGradient></defs><path fill="url(#AF)" d="M211 295c5-1 9-1 13 1h1c2 1 4 2 5 4l1 1v1h0c2 2 4 5 5 8 1 1 1 4 1 5l-3-4-1-1v1h0c-1 2 0 4 0 6v3c0 3 0 5 1 7 0 1-1 12-1 12v1h-1l-1-2v-1l-1 1v-2h1c0-1-1-2-1-4h1l1-8c-1-1-1-3-1-5l-1-1h0c-1-3-5-7-7-8l-1-1h0c-1 0-1 0-2 1h0-1l4 6h-1c0-1-2-3-3-4 0-1 0-1-1-2v-2h0 0c-1 0-1 0-1 1v1s0 1-1 1c0 1-1 1-1 1 1 1 2 1 2 2h1l3 3h0c-2-2-5-4-8-5l6 6v1l-2-2-2-2-1-1c-1 0-1 1-2 2h0s0 1 1 1h0v1c-1-1-2-1-2-2-1-1-1-2-2-3v-2c0-3-2-7 0-9v-1c0-1 0-1 1-2s2-1 4-1v-1h4 0-2c1-1 1-1 2-1h-5s-1 0-2-1z"></path><path d="M211 295c5-1 9-1 13 1h1c2 1 4 2 5 4l1 1v1h0c2 2 4 5 5 8 1 1 1 4 1 5l-3-4h2l-1-1c-1-2-2-2-4-3l-3 1h1-2l-7-3h-1c-2-2-5-5-5-7v-1h4 0-2c1-1 1-1 2-1h-5s-1 0-2-1z" class="C"></path><path d="M211 295c5-1 9-1 13 1h1c2 1 4 2 5 4h0c-2 0-4-1-5-1-4-1-7-1-10-1 2 4 3 5 8 7l8 2-3 1h1-2l-7-3h-1c-2-2-5-5-5-7v-1h4 0-2c1-1 1-1 2-1h-5s-1 0-2-1z" class="J"></path><defs><linearGradient id="AG" x1="206.922" y1="384.136" x2="163.825" y2="351.916" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#484848"></stop></linearGradient></defs><path fill="url(#AG)" d="M163 345c1-4 3-9 5-12 2-1 3-3 5-5 1 2 0 3-1 5l-3 11v1h0c1-1 2-1 3-2-1 4-3 8-2 12 1 2 4 4 6 5l5 1c2 1 3 0 5 0v1h-1v1h0c2 1 4 6 5 8 0 2 1 3 2 4 4 3 9 6 13 8 2 1 6 3 7 5h-1c-3-3-7-3-11-3-1 0-4 0-4 1l-1 1c-1-1-2-2-4-2s-3-2-5-3h0l-1-1c-1 0-2-2-3-2h0c0-2-3-3-4-5h0c1 0 2-1 2-1-2-1-5-3-7-5 0-1 0-1-1-1h0l-1 1v-2c0-1-2-2-2-3v1c-3-1-2-3-4-5l-1 2h0l-1 1c0 1 0 2-1 3v2h0v5l-1-2h0l-1-3v1h-1l-1-2c-1-3 0-5 0-8 1-4 3-9 5-13z"></path><path d="M174 361v1c2 1 4 1 6 2 3 2 5 4 7 6-5-2-10-4-13-9z" class="B"></path><path d="M163 345c1-4 3-9 5-12 2-1 3-3 5-5 1 2 0 3-1 5l-3 11-1 5h-1c-1 1 0 4-1 4h-1c2 4 5 11 8 14h1c2 3 6 5 10 7 1 1 3 2 5 3h-1l-8-4c-2-1-5-3-7-5 0-1 0-1-1-1h0l-1 1v-2c0-1-2-2-2-3v1c-3-1-2-3-4-5l-1 2h0l-1 1c0 1 0 2-1 3v2h0v5l-1-2h0l-1-3v1h-1l-1-2c-1-3 0-5 0-8 1-4 3-9 5-13z" class="I"></path><path d="M165 359v-1h1c0 1 0 1 1 1 1 1 1 3 2 4v1c-3-1-2-3-4-5z" class="C"></path><path d="M169 335l1 1c-2 4-3 8-3 13-1 1 0 4-1 4h-1 0c-1-5 2-13 4-18z" class="K"></path><path d="M163 345c1-4 3-9 5-12 2-1 3-3 5-5 1 2 0 3-1 5l-3 11-1 5h-1c0-5 1-9 3-13l-1-1h0c-3 3-4 7-5 10h-1z" class="C"></path><path d="M163 345h1c-1 6-1 12-3 18h1c1-2 1-5 2-7h0c1 0 1 0 1 1-1 1-1 2-1 4l-1 1c0 1 0 2-1 3v2h0v5l-1-2h0l-1-3v1h-1l-1-2c-1-3 0-5 0-8 1-4 3-9 5-13z" class="E"></path><defs><linearGradient id="AH" x1="211.772" y1="339.37" x2="174.461" y2="333.911" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#484848"></stop></linearGradient></defs><path fill="url(#AH)" d="M201 308h0l1-1v2 1 9c0 2 1 4 1 6h0v2 1s0 1 1 1v1h-1v-2l-1-1v-1h-1 0-1v2l1 1h0v1l1 1h1l1 1 3 3h0l1 1h1-1c-2-1-4-3-6-4 2 4 4 7 8 9h0s1 0 2 1h-2c1 0 1 0 1 1h0 1c1 0 1 1 2 1v1h-1 1c1 1 0 1 1 1h-2-1c0-1-1-1-2-1s-1-1-2-1l-2-1c-1-1-1 0-1-1-1 0-1 0-2-1h-1 0c1 2 3 3 4 4 1 0 1 0 1 1h1c2 1 5 3 7 4h0c-3-1-6-2-8-4-2-1-4-4-6-6h0l-1-1h0-1v2h0v3h0-1v1-3l-1 1v3h0c0 2-3 5-4 6s-3 3-3 4c-2 2-5 3-7 4h-2c0-1 0-1 1-1 2-1 7-5 8-8h0c-2 2-5 4-7 5s-3 2-5 1c-2 0-5-3-6-4v-7c1-2 8-4 10-5-2-1-7 2-9 3v-1c3-3 8-3 11-5l4-3h-1c1-2 2-2 3-3v-1l1-1c-1 1-2 1-3 1h0l-4 2c2-2 4-3 6-5 2-1 4-5 5-8 2-3 2-9 6-12z"></path><path d="M195 336h0v1c-2 3-4 8-8 9h0l-7 3h-1c2-4 7-4 10-6 2-2 4-5 6-7z" class="B"></path><path d="M184 338v1c4-2 7-5 10-8v1l-1 1c-2 3-7 8-11 8h0c-2-1-7 2-9 3v-1c3-3 8-3 11-5z" class="F"></path><path d="M201 308h0l1-1v2 1 9c0 2 1 4 1 6h0c-2-4-2-9-2-13l-3 5c-3 6-5 10-10 14l-4 2c2-2 4-3 6-5 2-1 4-5 5-8 2-3 2-9 6-12z" class="N"></path><defs><linearGradient id="AI" x1="211.261" y1="398.123" x2="165.906" y2="406.882" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#AI)" d="M160 343v1c-1 2-2 3-3 6l1 1 1-1h0l-2 7 1 1c0 3-1 5 0 8l1 2h1v-1l1 3h0l1 2v-5h0v-2c1-1 1-2 1-3l1-1h0l1-2c2 2 1 4 4 5v-1c0 1 2 2 2 3v2l1-1h0c1 0 1 0 1 1 2 2 5 4 7 5 0 0-1 1-2 1h0c1 2 4 3 4 5h0c1 0 2 2 3 2l1 1h0c2 1 3 3 5 3s3 1 4 2c4 0 8 1 11 4h0-2-4c4 2 6 3 9 7v1h0-1c0-1-1-1-2-2l-1 1h0c2 1 3 2 4 3s2 3 3 4h0c1 1 1 3 1 3 1 1 1 2 1 2h-1 0c-1-2-4-5-6-6h-1c2 3 5 5 6 8l1 3v1l1 4v9c0 1 0 2-1 4v2c0 4-1 9-2 12-1-5-2-11-2-17 0-2 0-4-1-6 0-1-1-2-1-3-1-1-2-3-3-4 0-1 1-1 0-1-1-3-3-5-5-7 0-1-1-2-1-2v-1h0-2c1 1 2 1 2 2 2 2 4 5 5 7-3-2-6-6-10-6-2 0-6-2-7-3-2 0-3 0-5-1l-4-1c-2-1-4-3-6-3-1 0-1 0-2 1l-4-2c-2 0-2-1-3-2h0-1c-1 0-2-3-3-4-3-4-5-10-7-15v-1c-1-4 0-9-2-12 1-4 3-7 4-10l1-1c1-5 3-8 6-12z"></path><path d="M169 363c0 1 2 2 2 3-1 1-1 2-2 2v-4-1z" class="F"></path><path d="M163 362h1v1 5c1 2 0 3 1 4v4c1 2 1 4 2 6l-1 1c0-1-1-2-1-2 0-2-1-3-2-4-1-3-1-7-1-10h0v-2c1-1 1-2 1-3z" class="D"></path><path d="M157 372l1-1h1c2 7 5 14 12 17l2 1c2 1 4 2 6 2 1 1 2 1 3 2h2 1c2 1 3 1 4 2h0c-1 0-2-1-4-1l-4-1h0c4 2 8 3 12 5 1 2 3 3 4 5h-1s-1-1-1 0l1 1h-1 0c-1-2-2-3-4-3h0v-1h2l-2-2c-1 0-2-1-3-1-4-2-8-3-11-5-3-1-5-1-7-2-6-4-11-11-13-18z" class="N"></path><defs><linearGradient id="AJ" x1="198.41" y1="396.436" x2="172.995" y2="369.644" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#3c3b3b"></stop></linearGradient></defs><path fill="url(#AJ)" d="M172 367h0c1 0 1 0 1 1 2 2 5 4 7 5 0 0-1 1-2 1h0c1 2 4 3 4 5h0c1 0 2 2 3 2l1 1h0c2 1 3 3 5 3s3 1 4 2c4 0 8 1 11 4h0-2c-3-1-6 0-9-1h-1c-2 0-5-2-7-1h-3c-1 1 0 1-2 1-1 0-3-1-4-2s-3-2-3-4h0v-1-1l1 1c-1-1-2-2-2-3-2-4-2-8-3-12h0l1-1z"></path><path d="M172 367c2 3 3 6 4 9 0 2 2 4 2 5l-3-3c1 2 1 4 3 6-1 0-2-1-2-1-1-1-2-2-2-3-2-4-2-8-3-12h0l1-1z" class="B"></path><defs><linearGradient id="AK" x1="192.597" y1="375.756" x2="152.515" y2="372.806" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#AK)" d="M160 343v1c-1 2-2 3-3 6l1 1 1-1h0l-2 7 1 1c0 3-1 5 0 8l1 5h-1l-1 1c2 7 7 14 13 18 2 1 4 1 7 2 3 2 7 3 11 5 1 0 2 1 3 1l2 2h-2v1h0c2 0 3 1 4 3h0 1l-1-1c0-1 1 0 1 0 1 1 2 2 3 2l-1 1h0-2c1 1 2 1 2 2 2 2 4 5 5 7-3-2-6-6-10-6-2 0-6-2-7-3-2 0-3 0-5-1l-4-1c-2-1-4-3-6-3-1 0-1 0-2 1l-4-2c-2 0-2-1-3-2h0-1c-1 0-2-3-3-4-3-4-5-10-7-15v-1c-1-4 0-9-2-12 1-4 3-7 4-10l1-1c1-5 3-8 6-12z"></path><path d="M191 398l2 2h-2v1h0c-1 0-5-1-7-2h0 0 8l-1-1zm-34-26v-1c0-2 0-6-1-8 0-2 0-4 1-6l1 1c0 3-1 5 0 8l1 5h-1l-1 1z" class="F"></path><defs><linearGradient id="AL" x1="172.842" y1="376.274" x2="148.938" y2="371.585" xlink:href="#B"><stop offset="0" stop-color="#3c3b3a"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#AL)" d="M154 355v-1c1-1 1-1 2-3l1-3c0 1-1 2-1 3s0 1-1 2c0 4-1 7-2 11-1 3 0 7 0 11 0 2 0 4 1 5v1 1c3 6 6 12 11 16l6 3c-1 0-1 0-2 1l-4-2c-2 0-2-1-3-2h0-1c-1 0-2-3-3-4-3-4-5-10-7-15v-1c-1-4 0-9-2-12 1-4 3-7 4-10l1-1z"></path><path d="M154 382c2 0 3 3 4 4 3 2 6 4 10 6 1 0 1 0 2 1h0l-4-1c-2 0-5-1-6-3 0 1 1 3 2 4s2 1 3 2c2 1 4 2 6 2 2 1 4 1 6 1h6s1 0 1 1h0 0c2 1 6 2 7 2 2 0 3 1 4 3h0 1l-1-1c0-1 1 0 1 0 1 1 2 2 3 2l-1 1h0-2c1 1 2 1 2 2 2 2 4 5 5 7-3-2-6-6-10-6-2 0-6-2-7-3-2 0-3 0-5-1l-4-1c-2-1-4-3-6-3l-6-3c-5-4-8-10-11-16z" class="O"></path><g class="N"><path d="M154 382c2 0 3 3 4 4 3 2 6 4 10 6-3 0-5-1-7-3-1 0-2-1-3-1h0c1 1 2 2 2 4l3 3c1 1 2 1 2 2v1c-5-4-8-10-11-16z"></path><path d="M165 397c1 1 2 1 4 2h0c2 1 4 1 6 1 2 1 5 2 7 2 3 1 6 1 8 2 2 0 4 1 5 0l1 1s2 0 2 1h0-2c1 1 2 1 2 2 2 2 4 5 5 7-3-2-6-6-10-6-2 0-6-2-7-3-2 0-3 0-5-1l-4-1c-2-1-4-3-6-3l-6-3v-1z"></path></g><path d="M165 397c1 1 2 1 4 2h0c2 1 4 1 6 1v1c3 1 6 1 9 2l-2 1h1-1-3c-1-1-1-1-2 0-2-1-4-3-6-3l-6-3v-1z" class="F"></path><path d="M197 290c8 0 16-1 24 2 1 1 4 2 4 3v1h-1c-4-2-8-2-13-1h0v1l-2 1v1 1l-2 1h0l-3 3c0 1-3 4-3 5-4 3-4 9-6 12-1 3-3 7-5 8-2 2-4 3-6 5l4-2h0c1 0 2 0 3-1l-1 1v1c-1 1-2 1-3 3-5 2-11 4-15 8h0c-1 1-2 1-3 2h0v-1l3-11c1-2 2-3 1-5-2 2-3 4-5 5-2 3-4 8-5 12-2 4-4 9-5 13l-1-1 2-7h0l-1 1-1-1c1-3 2-4 3-6v-1c-3 4-5 7-6 12l-1 1c-1 3-3 6-4 10 2 3 1 8 2 12v1c2 5 4 11 7 15 1 1 2 4 3 4h1 0c1 1 1 2 3 2l4 5c1 2 2 3 3 5h1 0c0 1 1 2 1 3h-3c-4-1-10-10-12-14l-1-1c3 8 7 14 15 17 5 2 10 2 15 4 6 2 12 6 15 12 2 6 4 14 2 20-1 2-2 4-4 6-1 0-1 0-2 1h0v-1c0-1-1-4-2-5v-1h0c-3-2-6-3-9-4h0c2 2 4 4 5 6v1h1c-1-1-2-1-3-1-2-3-4-5-7-6l-1 1 2 2-1 1c0 1-1 0-1 1h0c1 1 2 2 3 4h-1l-4-1h0c2 2 3 4 4 6v1l-1-1v1c1 3 2 5 4 7 2 3 6 4 7 7h-1c-2 0-5-1-7-1 0-1-1-1-1-1l-7-2h0v2c2 1 5 3 7 4 5 4 8 10 12 16 2 2 3 6 3 8 1 3 1 6 1 9-1 6-1 11-4 16l-1 2c0-2 0-2-1-3l-7 13c-1-4 7-18 8-23 0-3 1-7 0-10 0-2-1-5-2-7-1-3-2-5-3-7-2-3-4-4-7-5-6-2-13 0-18 3-8 4-15 14-18 22l-1 6c-1-1-1-2-1-3v-1c0-2-2-6-3-8-2-4-6-8-9-11s-5-6-6-10h0l-1 1v-1c-1 2 0 4 0 6v2l-1 1-1 3 1 3c0 1-1 3-2 4-1 2-2 4-4 5-2 3-6 5-9 7 0 0-1 0-1 1l4-1c-1 1-2 1-3 1-2 0-4 2-5 3h0 0-1l-1 2 1-1h-1c-2 1-3 3-4 4s-2 1-4 2l-6 3v1c-2 0-4 0-6 1-3 1-5 3-9 3v1h-1c1 0 1 0 2 1h-1c-2 1-3 1-4 1-4 1-6-1-10-3h0 0c-1 0-2-1-2-2v-1c-1-1-1-3-1-4-1 2-1 4-2 6v1c0-4-2-8 0-12 0-1 1-3 1-4h1l2-4 6-6s2-1 3-2c2 0 4-3 6-5h1 0l6-5c1-1 1-2 2-3h-1 0l1-2c1-2 3-3 4-5l1-1v-1c1-2 3-6 2-9 1-4 2-7 2-12l-1-16c0-2 0-5 1-7l-1-1c-1 2-1 3-1 4s-1 2-1 3 0 2-1 3c0-5 0-8 2-13v-1c-1 1-1 2-1 3-1 2-1 2-3 3-1-3 3-8 4-11 2-5 5-9 8-12v-1h-1 0v-1h0-1l2-2v-1c-1 1-1 1-2 1 1-2 4-3 4-5 7-7 14-13 16-22 1-6 1-11 2-17 1-7 3-13 3-20l1-8c0-2 0-3 1-5h-1 0v-2c0-1 1-2 1-4h-1v-1c1-1 1-2 2-3 0-1 0-2 1-2v-1l-1-1h0c1-2 1-2 1-4 1 0 2-2 2-3l5-6c3-2 6-5 8-7 3-1 6-3 9-5 2-1 3-3 5-4 1 0 2-1 4-1l-2-1 30-3c0 1 1 1 1 1 2-1 6-1 8-1z" class="B"></path><path d="M151 489h4c0 1 1 1 1 2h2l-1 1c-1 0-2 1-3 0h1v-1h-2l-1-1-1-1z" class="I"></path><path d="M145 392h0c1 2 1 4 2 5v2h-3l-2-2c1-1 1 0 2-1s1-3 1-4z" class="D"></path><path d="M173 481c1 1 2 3 3 4h0-1l-8-3v-1h1c2 0 3 1 5 0z" class="F"></path><path d="M145 492c1 1 2 1 3 2h0v1c0 1 1 2 1 2v2h-1l-4-4 1-1c-1-1-1-1 0-2z" class="M"></path><defs><linearGradient id="AM" x1="158.832" y1="486.767" x2="151.02" y2="489.218" xlink:href="#B"><stop offset="0" stop-color="#0e0f0e"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#AM)" d="M150 487h4c2 1 7 1 9 2-1 1-6 0-8 0h-4c0-1 0-1-1-1v-1h0z"></path><path d="M168 470c-3-4-5-7-6-11 0-2 0-3 1-5 0 2 2 5 2 7v3c1 1 1 2 2 3s1 1 1 3z" class="N"></path><path d="M171 440c-2-1-4-1-6-2-4-2-6-6-8-10 2 1 4 5 6 7 2 1 5 3 8 4l1 1h-1z" class="O"></path><path d="M135 489c1 0 1 1 2 0v-2h1c1 1 1 2 3 3 1 1 2 3 3 4h0v-1c0-1-1-1-1-2h1v-1s1 0 1 1v1c-1 1-1 1 0 2l-1 1-5-5 4 7a30.44 30.44 0 0 1-8-8z" class="C"></path><path d="M171 439l1-1 5 3c2 1 5 2 6 4h-2 0c-1 0-1 0-2-1h-1l-1 1c-1-1-3-2-4-3h-1c0-1 0-1-1-2h1l-1-1z" class="I"></path><defs><linearGradient id="AN" x1="152.946" y1="495.689" x2="147.759" y2="492.002" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#AN)" d="M149 488h1c1 0 1 0 1 1l1 1 1 1h2v1h-1l-2 1v1c1 0 2 1 3 1v1c-2 0-4 0-6-1l-1-1h0c-1-1-2-1-3-2v-1l2 2h0c1-1 2-2 3-2h0c0-1 0-1-1-2v-1z"></path><path d="M149 488h1c1 0 1 0 1 1l1 1 1 1 1 1h-3l-1-1h0c0-1 0-1-1-2v-1z" class="O"></path><path d="M137 448l-1-1v-1c2 1 3 2 4 3l1 1h1v1h0c1 0 1 1 1 2h0v1h0c1 1 1 1 1 2 1 1 2 2 2 3-2 0-5-4-6-5s-2-3-3-3l-1-2 1-1z" class="D"></path><path d="M137 448c1 0 3 2 4 3 0 1 0 2 1 4v-1h-2c-1-1-2-3-3-3l-1-2 1-1z" class="K"></path><path d="M177 445l1-1h1c1 1 1 1 2 1h0 2c5 1 10 1 13 4l1 1 1 1h-1c-3-2-6-3-9-4h0c2 2 4 4 5 6v1h1c-1-1-2-1-3-1-2-3-4-5-7-6l-1 1c-1 0-2-1-3-2-1 0-2-1-3-1z" class="N"></path><defs><linearGradient id="AO" x1="156.881" y1="447.645" x2="148.974" y2="437.331" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#AO)" d="M144 439c4 0 8 0 11 2v-1l5 3h1c1 0 2 1 2 1 0 2-1 3-2 4s-3 1-4 1h-1c-2-1-3-2-5-3h1 1 1c-1 0-1 0-2-1-1-3-6-3-8-6z"></path><path d="M155 440l5 3h1c1 0 2 1 2 1 0 2-1 3-2 4s-3 1-4 1h-1l1-1c1-1 2-2 4-2l-6-5v-1z" class="N"></path><path d="M196 526c1-2 2-5 2-7 2-11 0-21-7-30 3 2 4 3 6 6l1-1c2 2 3 6 3 8 1 3 1 6 1 9-1 6-1 11-4 16l-1 2c0-2 0-2-1-3z" class="M"></path><path d="M198 494c2 2 3 6 3 8 0 1 0 1-1 0 0-1-1-2-1-3-1-1-1-3-2-4l1-1z" class="I"></path><path d="M121 438c0-1 0-2-1-2l-1-3v-1-4l1 2c1 2 2 4 4 6 1 0 1 0 1 1l3 3h0c1 1 2 1 3 2l9 5h-1 0l1 2c-1-1-2-2-4-3v1l1 1-1 1 1 2c-2-1-4-2-5-3-3-1-5-2-7-4-1-2-2-4-4-6z" class="G"></path><path d="M129 444l7 5 1 2c-2-1-4-2-5-3h0c0-2-2-3-4-4h1z" class="H"></path><path d="M125 444c1-1 1-2 2-2l2 2h-1c2 1 4 2 4 4h0c-3-1-5-2-7-4z" class="L"></path><path d="M120 426c1 2 2 3 3 4 3 3 9 6 14 7 2 1 5 1 7 2 2 3 7 3 8 6 1 1 1 1 2 1h-1-1-1c-5-3-10-5-16-6-1-1-1-1-2-1h-1 0-1-1v1h0c1 0 1 1 2 1l-1 1c-1-1-2-1-3-2h0l-3-3c0-1 0-1-1-1-2-2-3-4-4-6v-4z" class="E"></path><path d="M126 392c3 4 6 8 10 10h1l1 1c2 1 4 2 5 3 2 1 3 3 5 4 4 4 8 8 12 13 2 2 4 5 5 8v1l-1-1v2h0c-4-3-7-7-9-11 0-2-1-4-2-5-1 0-2-1-3-1-3-1-5-3-8-6h0c-1 0-3-4-4-5-2-2-5-4-7-5l-3-3-1 1c-1-2-1-4-1-6z" class="D"></path><path d="M135 467c5 5 12 10 19 13 4 1 8 2 11 5-6-2-14-2-20-5h0v2c3 2 6 3 9 5h-4 0v1h-1c-1-1-2-1-3-2-1 0-2-1-3-2v1l2 3h0l-2-2h0c-1-2-2-3-3-4v-2l-2-5c-1-1-2-3-2-4-1-1-1-2-1-3v-1z" class="H"></path><path d="M142 481l-2-5h0c2 1 4 3 5 4v2l-3-1z" class="J"></path><path d="M142 481l3 1c3 2 6 3 9 5h-4c-3-1-5-3-8-6z" class="N"></path><defs><linearGradient id="AP" x1="150.722" y1="444.388" x2="135.17" y2="451.146" xlink:href="#B"><stop offset="0" stop-color="#1b1c1b"></stop><stop offset="1" stop-color="#5f5d5e"></stop></linearGradient></defs><path fill="url(#AP)" d="M131 442l1-1c-1 0-1-1-2-1h0v-1h1 1 0 1c1 0 1 0 2 1h0 1c1 1 4 2 5 3 3 2 4 5 7 7 2 1 4 1 6 2 1 0 1 0 2 1-1 1-1 2-1 4l-3 3c-1 0-3 0-4-1s-2-2-4-3c0-1 0-1-1-2h0v-1h0c0-1 0-2-1-2h0v-1h-1l-1-1-1-2h0 1l-9-5z"></path><path d="M154 452c1 0 1 0 2 1-1 1-1 2-1 4l-3 3c-1 0-3 0-4-1h2c0-1 1-2 2-3 0 1 0 1-1 2h1s2-3 2-5h0v-1z" class="I"></path><defs><linearGradient id="AQ" x1="163.547" y1="486.379" x2="145.938" y2="460.965" xlink:href="#B"><stop offset="0" stop-color="#303031"></stop><stop offset="1" stop-color="#545453"></stop></linearGradient></defs><path fill="url(#AQ)" d="M139 469c0-1-1-1-1-2h1 1c5 1 10 5 15 6l1-1c2 1 5 1 8 2 2 1 5 2 8 4l1-1c1 2 3 4 3 6 0 1 0 2 1 2l-1 1-1-1h1 0c-1-1-2-3-3-4-2 1-3 0-5 0h-1v1c-3-1-7-2-10-3-7-2-12-6-18-10z"></path><path d="M156 472c2 1 5 1 8 2 2 1 5 2 8 4l1-1c1 2 3 4 3 6 0 1 0 2 1 2l-1 1-1-1h1 0c-1-1-2-3-3-4h0c-1-1-2-3-4-4l-14-4 1-1z" class="I"></path><defs><linearGradient id="AR" x1="163.806" y1="428.288" x2="151.717" y2="430.602" xlink:href="#B"><stop offset="0" stop-color="#2d2d2c"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#AR)" d="M145 416c2 1 4 4 5 6h0c0-2-2-3-1-5 1 0 2 0 2 1 2 1 3 4 4 6s2 4 2 5c1 5 4 9 8 11l4 2c-3 1-7-1-10-2-2-1-3-2-5-2 2 2 5 3 7 5h-1l-5-3c-2-1-3-2-4-4-2-1-3-2-4-4h1c0-1 0-2-1-3h0c-1-5-2-9-2-13z"></path><path d="M145 416c2 1 4 4 5 6 0 1-1 1 0 2s1 5 2 7c0 2 1 3 2 5l-1 1c-1-1-3-4-4-6v-1s0-1-1-1h-1 0c-1-5-2-9-2-13zm19-97l4-1c1 1 1 1 2 1h0c-1 1-1 1-1 2h-1c-2 2-3 4-5 6-3 3-7 6-9 9s-3 6-5 9v-7c-3 4-4 8-4 13l1 6-3-5h0l-1-1v-1c-1-2-1-4-1-6v-1l2-2v-3h0 1c1-2 2-3 3-4 4-3 10-7 11-12h0c1-1 2-1 3-2 1 0 2 0 3-1z" class="K"></path><path d="M147 334c0 2 0 3-1 4l-1 1c-1 0-1 1-2 2h0v-3h0 1c1-2 2-3 3-4z" class="L"></path><path d="M127 398l1-1 3 3c2 1 5 3 7 5 1 1 3 5 4 5l3 6c0 4 1 8 2 13h0c1 1 1 2 1 3h-1l-1-2-1 1v3h-1c-2-4-3-8-7-12h3c-1-2-2-4-4-5v-1l-3-3c-1-2-2-4-4-5-1-2-1-3-1-5 0-1-1-3-1-4v-1h0z" class="E"></path><defs><linearGradient id="AS" x1="130.918" y1="401.693" x2="150.038" y2="429.11" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#6d6b6c"></stop></linearGradient></defs><path fill="url(#AS)" d="M127 398l1-1 3 3c2 1 5 3 7 5 1 1 3 5 4 5l3 6c0 4 1 8 2 13h0c1 1 1 2 1 3h-1l-1-2h0c-1-2-1-3-2-5 0-1 0-3-1-4 0-2-1-3-2-5 0-2-1-2-2-3h0c-1-2-2-3-3-4-2-2-5-4-8-6 0-1-1-3-1-4v-1h0z"></path><path d="M127 399v-1c2 4 7 5 9 8 1 2 3 4 3 7h0c-1-2-2-3-3-4-2-2-5-4-8-6 0-1-1-3-1-4zm-7-5l1 14c1 3 1 6 2 9v-1l1 1 2 3c3 6 6 11 13 13l5 2v-1h1v-3l1-1 1 2c1 2 2 3 4 4 1 2 2 3 4 4v1c-3-2-7-2-11-2-2-1-5-1-7-2-5-1-11-4-14-7-1-1-2-2-3-4l-1-2c-2-7-1-17 0-24 0-2 0-4 1-6z" class="B"></path><path d="M144 434h1v-3l1-1 1 2c1 2 2 3 4 4-3 0-7 1-10 0h3v-1-1z" class="C"></path><defs><linearGradient id="AT" x1="136.537" y1="424.438" x2="127.202" y2="428.789" xlink:href="#B"><stop offset="0" stop-color="#535354"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#AT)" d="M123 417v-1l1 1 2 3c3 6 6 11 13 13l5 2v1h-3c-3 0-6 0-9-2-6-4-8-10-9-17z"></path><path d="M122 388v-14c1 1 1 3 1 4 0 2 0 5 1 8 0 1 0 2 1 3 0 1 0 2 1 3 0 2 0 4 1 6h0v1c0 1 1 3 1 4 0 2 0 3 1 5 2 1 3 3 4 5l3 3v1c2 1 3 3 4 5h-3c4 4 5 8 7 12v1l-5-2c-7-2-10-7-13-13l-2-3-1-1v1c-1-3-1-6-2-9l-1-14c2-2 1-4 1-6v-1c0-1 1-2 0-4 0-1 0-6 1-8 0 3-1 12 0 13h0z" class="P"></path><path d="M121 408c2 2 3 5 4 8 0 1 1 2 1 4h0l-2-3-1-1v1c-1-3-1-6-2-9z" class="K"></path><path d="M125 409c1 2 3 4 5 6l6 7c0 1 1 2 1 4h-1l-1-1h-1l-1-1c-2-3-5-4-6-7-1-1-1-2-1-2 0-1 0-1-1-2v-4z" class="E"></path><path d="M130 415l6 7c0 1 1 2 1 4h-1l-1-1c-2-2-2-3-3-5-1-1-2-2-2-3-1 0-1-1 0-2z" class="G"></path><path d="M126 420c3 5 6 7 12 9l-4-4h1l1 1h1c0-2-1-3-1-4v-1l1 1c4 4 5 8 7 12v1l-5-2c-7-2-10-7-13-13h0z" class="B"></path><path d="M122 388v-14c1 1 1 3 1 4 0 2 0 5 1 8 0 1 0 2 1 3 0 1 0 2 1 3 0 2 0 4 1 6h0v1c0 1 1 3 1 4 0 2 0 3 1 5 2 1 3 3 4 5l3 3v1c2 1 3 3 4 5h-3l-1-1v1l-6-7c-2-2-4-4-5-6-1-7-3-13-3-21z" class="P"></path><path d="M122 388v-14c1 1 1 3 1 4 0 2 0 5 1 8 0 1 0 2 1 3 0 1 0 2 1 3 0 2 0 4 1 6h0v1c-2-3-3-7-3-11-1 3 0 5 0 8 0 6 1 13 5 18 2 2 6 4 7 7v1l-6-7c-2-2-4-4-5-6-1-7-3-13-3-21z" class="H"></path><defs><linearGradient id="AU" x1="201.978" y1="313.477" x2="143.493" y2="347.579" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#6a696a"></stop></linearGradient></defs><path fill="url(#AU)" d="M209 298v1l-2 1h0c-1 0-5 1-6 2l-3 2c-3 2-6 5-9 8l-13 12c-1 2-1 3-3 4-2 2-3 4-5 5-2 3-4 8-5 12-2 4-4 9-5 13l-1-1 2-7h0l-1 1-1-1c1-3 2-4 3-6v-1c-3 4-5 7-6 12l-1 1h-1c0 1-1 3-2 4-1-1-1-1-1-2 0-3 1-6 2-9 5-11 14-21 22-30 2-2 4-3 6-5l7-5h-1l8-7h-1l4-1h2c1 0 4-2 5-2 2-1 4-1 6-1z"></path><path d="M209 298v1l-2 1h0c-1 0-5 1-6 2l-3 2v-1l1-1v-1c-2 1-6 2-8 4-2 1-3 3-5 4h-1l8-7h-1l4-1h2c1 0 4-2 5-2 2-1 4-1 6-1z" class="M"></path><path d="M198 303v1c-3 2-6 5-9 8l-13 12c-3 1-4 3-6 5h0-1v1h-1 0c0-2 2-3 3-4 2-2 4-5 6-6 2-3 5-5 8-7 4-4 8-7 13-10z" class="I"></path><path d="M168 330h0 1v-1h1 0c2-2 3-4 6-5-1 2-1 3-3 4-2 2-3 4-5 5-2 3-4 8-5 12-2 4-4 9-5 13l-1-1 2-7h0l-1 1-1-1c1-3 2-4 3-6v-1c-3 4-5 7-6 12l-1 1h-1c4-10 10-19 16-26z" class="M"></path><path d="M160 343c1-3 3-5 5-7l-2 5-3 3v-1z" class="D"></path><path d="M160 344l3-3c-1 3-2 6-4 9h0l-1 1-1-1c1-3 2-4 3-6z" class="H"></path><defs><linearGradient id="AV" x1="185.191" y1="307.329" x2="191.809" y2="327.429" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#AV)" d="M198 304l3-2c1-1 5-2 6-2l-3 3c0 1-3 4-3 5-4 3-4 9-6 12-1 3-3 7-5 8-2 2-4 3-6 5l4-2h0c1 0 2 0 3-1l-1 1v1c-1 1-2 1-3 3-5 2-11 4-15 8h0c-1 1-2 1-3 2h0v-1l3-11c1-2 2-3 1-5 2-1 2-2 3-4l13-12c3-3 6-6 9-8z"></path><path d="M197 309c1 2-1 3-2 5 0 1-1 2-1 3s-1 2-1 4c-1 0-1 1-2 2v-1-1c-2 0-3 3-6 2 5-4 9-9 12-14z" class="F"></path><path d="M185 323c3 1 4-2 6-2v1 1c0 1-1 2-2 3-3 2-8 3-12 4 1-1 2-2 4-3v-1c1-1 3-2 4-3z" class="B"></path><path d="M185 323c3 1 4-2 6-2v1c-3 2-7 5-10 5v-1c1-1 3-2 4-3z" class="C"></path><path d="M197 309c2-3 4-6 7-6 0 1-3 4-3 5-4 3-4 9-6 12-1 3-3 7-5 8-2 2-4 3-6 5l4-2h0c1 0 2 0 3-1l-1 1v1c-1 1-2 1-3 3-5 2-11 4-15 8h0c-1 1-2 1-3 2h0v-1l3-11 1 1v-1l1-1c1-1 2-2 3-2 4-1 9-2 12-4 1-1 2-2 2-3 1-1 1-2 2-2 0-2 1-3 1-4s1-2 1-3c1-2 3-3 2-5z" class="H"></path><path d="M172 333l1 1v-1l1-1c0 1-1 3-2 5l1 1c0 2-1 3-1 5h0c-1 1-2 1-3 2h0v-1l3-11z" class="D"></path><path d="M197 309c2-3 4-6 7-6 0 1-3 4-3 5-4 3-4 9-6 12 0-1 0-2 1-3v-1-1l1-1h0c0-1 0-2 1-3-2 2-3 4-3 5 0 2-1 4-2 5 0-2 1-3 1-4s1-2 1-3c1-2 3-3 2-5z" class="C"></path><path d="M172 442h1c1 1 3 2 4 3 1 0 2 1 3 1 1 1 2 2 3 2l2 2-1 1c0 1-1 0-1 1h0c1 1 2 2 3 4h-1l-4-1h0c2 2 3 4 4 6v1l-1-1v1c1 3 2 5 4 7 2 3 6 4 7 7h-1c-2 0-5-1-7-1 0-1-1-1-1-1l-7-2h0v2h0l8 9c1 1 3 3 4 5-2-1-3-2-4-3h-1c-2-1-4-2-4-3-4-2-7-5-10-9-2-1-3-2-4-3 0-2 0-2-1-3s-1-2-2-3v-3c0-2-2-5-2-7v-1c1-5 5-8 9-11z" class="F"></path><path d="M174 448c2 1 3 4 5 5v1c1 1 2 0 2 1h0v1h-1c-1-1-2-1-3-2-2-2-3-4-3-6z" class="M"></path><path d="M165 461c2 5 6 8 7 12-2-1-3-2-4-3 0-2 0-2-1-3s-1-2-2-3v-3zm17 21h0c-1-2-5-6-6-7l1-1c3 4 8 7 10 11h-1c-2-1-4-2-4-3z" class="J"></path><path d="M176 464c-1-1-2-2-2-4-1-1-1-3-1-5 1 2 2 5 3 6l3 3c1 1 2 2 4 3 0 1 1 1 2 2v1c-2 0-3-2-5-2-1-1-2-3-4-4h0z" class="N"></path><path d="M168 463c2 2 4 4 6 5s4 2 5 4h0v2h0c-1 0-2-1-3-2h-2c-2-2-5-6-6-9z" class="I"></path><path d="M174 463v1c1 0 2 1 3 2 0-1-1-1-1-2h0c2 1 3 3 4 4 2 0 3 2 5 2v-1c1 1 2 3 4 3 1 0 1 1 1 1 1 1 2 1 2 1 1 1 2 1 2 2-2 0-5-1-7-1 0-1-1-1-1-1l1-1c-1-1-2-1-3-2l-6-3c-2-2-3-3-4-5z" class="M"></path><path d="M173 442c1 1 3 2 4 3 1 0 2 1 3 1 1 1 2 2 3 2l2 2-1 1c0 1-1 0-1 1h0c1 1 2 2 3 4h-1l-4-1c0-1-1 0-2-1v-1c-2-1-3-4-5-5l-1-6z" class="J"></path><path d="M168 463c-1-4-2-8 0-12 1-3 3-4 4-6-2 5-3 11 0 16 0 1 1 2 2 2 1 2 2 3 4 5l6 3c1 1 2 1 3 2l-1 1-7-2c-1-2-3-3-5-4s-4-3-6-5z" class="B"></path><defs><linearGradient id="AW" x1="210.535" y1="422.338" x2="140.632" y2="406.385" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#AW)" d="M149 366c2 3 1 8 2 12v1c2 5 4 11 7 15 1 1 2 4 3 4h1 0c1 1 1 2 3 2l4 5c1 2 2 3 3 5h1 0c0 1 1 2 1 3h-3c-4-1-10-10-12-14l-1-1c3 8 7 14 15 17 5 2 10 2 15 4 6 2 12 6 15 12 2 6 4 14 2 20-1 2-2 4-4 6-1 0-1 0-2 1h0v-1c0-1-1-4-2-5v-1h0 1s0 1 1 1v3l1 2c2-6 3-15 0-21v2c1 2 1 3 1 4s1 6 0 8v2h0l-1 1v2 1c0-2-1-5-3-7h0 1c-1-1-1-2-1-2 0-1-2-3-3-3 2 0 4 1 5 2h1v-1c0-1 0-1-1-2v2c-9-5-20-6-28-12-9-8-14-19-18-30-2-6-4-10-5-16v-5c-1-3-1-6 0-10 0-2 1-4 1-6z"></path><path d="M174 422c2 1 4 1 7 2 1 0 4 0 5 2h-1c-2-1-5-1-8-2-1-1-2-1-3-2z" class="F"></path><path d="M158 410l3 3c1 3 2 5 4 7l6 6c2 1 5 2 7 2 4 2 8 3 12 5-1 0-3-1-5-1-3-1-7-1-10-3-7-3-14-11-17-19z" class="B"></path><path d="M149 366c2 3 1 8 2 12v1c2 5 4 11 7 15 1 1 2 4 3 4h1 0c1 1 1 2 3 2l4 5c1 2 2 3 3 5h1 0c0 1 1 2 1 3h-3c-4-1-10-10-12-14l-1-1-2-2 1-1h-1v-1c-1-1-2-3-3-5 0-1-1-2-1-3s-1-3-2-5v-2c-1 0-1-1-1-2v-2c-1 0 0 0-1 1v4c1 1 0 3 0 4 1 1 1 1 0 2v1-5c-1-3-1-6 0-10 0-2 1-4 1-6z" class="D"></path><path d="M197 290c8 0 16-1 24 2 1 1 4 2 4 3v1h-1c-4-2-8-2-13-1h0v1l-2 1v1c-2 0-4 0-6 1-1 0-4 2-5 2h-2l-4 1h1l-8 7h1l-7 5c-2 2-4 3-6 5h-1c-2 0-2 2-4 2h1c0-1 0-1 1-2h0c-1 0-1 0-2-1l-4 1c-1 1-2 1-3 1-1 1-2 1-3 2h0c-1 5-7 9-11 12-1 1-2 2-3 4h-1 0v3l-2 2v1c0 2 0 4 1 6v1l1 1c0 2 3 8 3 10h0v5 1c-2 0-4-3-5-4h0c-3-1-6-5-8-7l-1-1c0-1-1-2-2-2 0-1-1-2-1-3-1-3-1-6-2-9 1 0 1-1 1-1v-3c0-1 0-3 1-4 0-9 4-15 8-21v-2h-1l-2 2c-2 2-4 4-5 6s-1 4-2 6l-1-1h0c1-2 1-2 1-4 1 0 2-2 2-3l5-6c3-2 6-5 8-7 3-1 6-3 9-5 2-1 3-3 5-4 1 0 2-1 4-1l-2-1 30-3c0 1 1 1 1 1 2-1 6-1 8-1z" class="B"></path><path d="M137 336c1-2 2-5 5-6 0-1 0-1 1 0-1 1-3 4-3 6-1 1-2 4-2 6l-2 2h0l1-8z" class="P"></path><path d="M143 316h0 1c3 0 6-4 9-3-1 1-2 1-3 2-1 0-2 1-3 2v1s-1 0-2 1h-1c-1 0-2 1-3 1-3 1-5 2-8 4 3-4 5-6 10-8z" class="E"></path><path d="M134 311c3-2 6-5 8-7 3-1 6-3 9-5 2-1 3-3 5-4l-1 1-1 1v1c-1 0-1 0-1 1 1 0 3-1 4-1 3-1 9-2 12-1v1c-8 1-16 2-24 7a30.44 30.44 0 0 0-8 8v-2h-1l-2 2c-2 2-4 4-5 6s-1 4-2 6l-1-1h0c1-2 1-2 1-4 1 0 2-2 2-3l5-6z" class="D"></path><defs><linearGradient id="AX" x1="152.919" y1="327.19" x2="138.805" y2="321.33" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#AX)" d="M150 315c2 0 5-2 7-3s4-1 5-1c-2 2-4 3-6 5-2 1-3 3-5 4-1 1-4 5-6 5l-1 4-1 1c-1-1-1-1-1 0-3 1-4 4-5 6h-1l-2 4-1-3c1-9 5-12 11-18h1c1-1 2-1 2-1v-1c1-1 2-2 3-2z"></path><path d="M136 336c2-4 5-7 7-10h0c1 0 2 0 2-1l-1 4-1 1c-1-1-1-1-1 0-3 1-4 4-5 6h-1z" class="K"></path><defs><linearGradient id="AY" x1="172.732" y1="312.579" x2="160.106" y2="296.677" xlink:href="#B"><stop offset="0" stop-color="#3d3d3e"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#AY)" d="M197 295h1c-2 1-4 2-5 3-2 1-3 2-4 2l-5 1c-2 1-4 2-7 2h-2c-2 0-5 1-7 1-4 1-8 1-12 3-3 1-5 2-7 4h0c-5 2-9 4-13 6l6-6c15-14 37-10 55-16h0z"></path><path d="M177 303v-1c-1-1-6 0-8-1 2-1 12 0 15 0-2 1-4 2-7 2z" class="H"></path><path d="M140 336c1-1 1-2 2-3h0 0l1 1c0-2 1-2 2-3 1 0 1 0 2 1-2 2-3 4-4 6h0v3l-2 2v1c0 2 0 4 1 6v1l1 1c0 2 3 8 3 10h0v5 1c-2 0-4-3-5-4h0c-3-1-6-5-8-7l-1-1c0-1-1-2-2-2 0-1-1-2-1-3-1-3-1-6-2-9 1 0 1-1 1-1v-3c0-1 0-3 1-4 0 3 0 6 1 9 2 8 9 14 15 20l1 1c-1-3-3-6-5-9-1-2-3-5-4-8 0-1 1-3 1-5s1-5 2-6z" class="G"></path><path d="M197 290c8 0 16-1 24 2 1 1 4 2 4 3v1h-1c-4-2-8-2-13-1h0l-17 5c2-2 4-3 7-5h0-3-1 0c-3-1-6 0-9 0l-19 3v-1c-3-1-9 0-12 1-1 0-3 1-4 1 0-1 0-1 1-1v-1l1-1 1-1c1 0 2-1 4-1l-2-1 30-3c0 1 1 1 1 1 2-1 6-1 8-1z" class="H"></path><path d="M175 294l-4 1v-1c3-1 9-1 13 0h-9z" class="D"></path><path d="M184 294h0c6-1 11-2 17-2h1c-1 1-3 2-5 3h0c-3-1-6 0-9 0h0c-1 0-2-1-3 0h-1c-2 0-7 0-9-1h9z" class="F"></path><path d="M198 295h3 0c-3 2-5 3-7 5l17-5v1l-2 1v1c-2 0-4 0-6 1-1 0-4 2-5 2h-2l-4 1h1l-8 7h1l-7 5c-2 2-4 3-6 5h-1c-2 0-2 2-4 2h1c0-1 0-1 1-2h0c-1 0-1 0-2-1l-4 1c-1 1-2 1-3 1-1 1-2 1-3 2h0c-1 5-7 9-11 12-1 1-2 2-3 4h-1c1-2 2-4 4-6-1-1-1-1-2-1-1 1-2 1-2 3l-1-1h0 0c-1 1-1 2-2 3 0-2 2-5 3-6l1-1 1-4c2 0 5-4 6-5 2-1 3-3 5-4 2-2 4-3 6-5-1 0-3 0-5 1s-5 3-7 3c1-1 2-1 3-2-3-1-6 3-9 3h-1 0c2-2 4-3 6-4v-1h0c2-2 4-3 7-4 4-2 8-2 12-3 2 0 5-1 7-1h2c3 0 5-1 7-2l5-1c1 0 2-1 4-2 1-1 3-2 5-3z" class="B"></path><defs><linearGradient id="AZ" x1="149.688" y1="317.747" x2="146.049" y2="333.238" xlink:href="#B"><stop offset="0" stop-color="#272626"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#AZ)" d="M156 316c-1 3-3 6-4 8l-3 3v1c1 0 1-1 2-1 0 2-3 4-4 5-1-1-1-1-2-1-1 1-2 1-2 3l-1-1h0 0c-1 1-1 2-2 3 0-2 2-5 3-6l1-1 1-4c2 0 5-4 6-5 2-1 3-3 5-4z"></path><path d="M184 301l5-1c-1 2-1 3-3 4-4 1-8 2-11 3-3 0-6 1-9 2-1 1-4 2-4 2-1 0-3 0-5 1s-5 3-7 3c1-1 2-1 3-2h0c3-1 5-3 8-4l11-3c1-1 3-1 4-1v-1c-1 0-1 0-2-1h1 2c3 0 5-1 7-2z" class="F"></path><defs><linearGradient id="Aa" x1="162.022" y1="312.316" x2="158.486" y2="306.136" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#Aa)" d="M149 311h0c2-2 4-3 7-4 4-2 8-2 12-3 2 0 5-1 7-1h-1c1 1 1 1 2 1v1c-1 0-3 0-4 1l-11 3c-3 1-5 3-8 4h0c-3-1-6 3-9 3h-1 0c2-2 4-3 6-4v-1z"></path><path d="M149 311h0c2-2 4-3 7-4 4-2 8-2 12-3 2 0 5-1 7-1h-1c1 1 1 1 2 1h-1-3 0-2s-1 1-2 1h1c-4 1-10 1-14 3-2 1-4 2-6 4v-1z" class="H"></path><path d="M192 302h1l-8 7h1l-7 5c-2 2-4 3-6 5h-1c-2 0-2 2-4 2h1c0-1 0-1 1-2h0c-1 0-1 0-2-1l-4 1c-1 1-2 1-3 1-1 1-2 1-3 2h0c0-1-1-2 0-3 0-2 2-3 3-5 9-7 21-6 31-12z" class="D"></path><path d="M170 317l15-8h1l-7 5-8 3h-1z" class="J"></path><path d="M170 317h1c2-1 5-2 8-3-2 2-4 3-6 5h-1c-2 0-2 2-4 2h1c0-1 0-1 1-2h0c-1 0-1 0-2-1l-4 1h0c2-1 4-2 6-2z" class="F"></path><defs><linearGradient id="Ab" x1="184.165" y1="421.107" x2="102.52" y2="395.463" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#Ab)" d="M127 325c1-2 1-4 2-6s3-4 5-6l2-2h1v2c-4 6-8 12-8 21-1 1-1 3-1 4v3s0 1-1 1c1 3 1 6 2 9 0 1 1 2 1 3 1 0 2 1 2 2l1 1c2 2 5 6 8 7h0c0 2 2 4 3 5l1 1v6c0 5-1 11 0 16h0c0 1 0 3-1 4s-1 0-2 1l2 2 2 3c3 2 5 3 7 6v1c-1-1-2-1-2-1 0 1 0 1 1 2h1c1 1 2 3 3 4-2-1-6-5-8-4-2-1-3-3-5-4-1-1-3-2-5-3l-1-1h-1c-4-2-7-6-10-10-1-1-1-2-1-3-1-1-1-2-1-3-1-3-1-6-1-8 0-1 0-3-1-4v14h0c-1-1 0-10 0-13-1 2-1 7-1 8 1 2 0 3 0 4v1c0 2 1 4-1 6-1 2-1 4-1 6-1 7-2 17 0 24l1 2v4l-1-2v4 1l1 3c1 0 1 1 1 2s1 3 2 4c4 6 10 11 16 15 4 3 10 5 15 6 2 0 5 1 7 1l2 2h-1l-11 1 13 4c4 1 6 3 9 6l-1 1c-3-2-6-3-8-4-3-1-6-1-8-2l-1 1c-5-1-10-5-15-6h-1-1c0 1 1 1 1 2l-9-9c-4-5-8-9-10-15-1-2-2-5-3-7l-1-4h0c0 2 1 3 1 5 2 8 7 14 12 21 1 2 2 3 3 4v1c2 0 2 1 3 2v1c0 1 0 2 1 3 0 1 1 3 2 4l2 5v2c1 1 2 2 3 4h0l2 2h0l-2-3v-1c1 1 2 2 3 2 1 1 2 1 3 2v1c1 1 1 1 1 2h0c-1 0-2 1-3 2h0l-2-2c0-1-1-1-1-1v1h-1c0 1 1 1 1 2v1h0c-1-1-2-3-3-4-2-1-2-2-3-3h-1v2c-1 1-1 0-2 0-3-6-5-12-7-18-1 4 2 9 1 14h0l-1 1v-1c-1 2 0 4 0 6v2l-1 1-1 3 1 3c0 1-1 3-2 4-1 2-2 4-4 5-2 3-6 5-9 7 0 0-1 0-1 1l4-1c-1 1-2 1-3 1-2 0-4 2-5 3h0 0-1l-1 2 1-1h-1c-2 1-3 3-4 4s-2 1-4 2l-6 3v1c-2 0-4 0-6 1-3 1-5 3-9 3v1h-1c1 0 1 0 2 1h-1c-2 1-3 1-4 1-4 1-6-1-10-3h0 0c-1 0-2-1-2-2v-1c-1-1-1-3-1-4-1 2-1 4-2 6v1c0-4-2-8 0-12 0-1 1-3 1-4h1l2-4 6-6s2-1 3-2c2 0 4-3 6-5h1 0l6-5c1-1 1-2 2-3h-1 0l1-2c1-2 3-3 4-5l1-1v-1c1-2 3-6 2-9 1-4 2-7 2-12l-1-16c0-2 0-5 1-7l-1-1c-1 2-1 3-1 4s-1 2-1 3 0 2-1 3c0-5 0-8 2-13v-1c-1 1-1 2-1 3-1 2-1 2-3 3-1-3 3-8 4-11 2-5 5-9 8-12v-1h-1 0v-1h0-1l2-2v-1c-1 1-1 1-2 1 1-2 4-3 4-5 7-7 14-13 16-22 1-6 1-11 2-17 1-7 3-13 3-20l1-8c0-2 0-3 1-5h-1 0v-2c0-1 1-2 1-4h-1v-1c1-1 1-2 2-3 0-1 0-2 1-2v-1z"></path><path d="M127 326c0 3-1 6-2 10v2h-1 0v-2c0-1 1-2 1-4h-1v-1c1-1 1-2 2-3 0-1 0-2 1-2z" class="G"></path><path d="M111 485c1-4 4-7 7-10-2 4-4 7-7 10z" class="L"></path><path d="M164 471c4 1 6 3 9 6l-1 1c-3-2-6-3-8-4h0c1 0 1 0 1-1h0l1 1s0 1 1 1l1-1c-1-1-3-2-4-3h0zm-32-6c2 0 2 1 3 2v1c0 1 0 2 1 3 0 1 1 3 2 4l2 5v2c-3-4-5-11-7-16l-1-1z" class="O"></path><path d="M129 460c1 2 2 3 3 4v1l1 1-1 1c0 1 0 2 1 3v3c1 0 1 1 0 2-1-1-4-13-4-15z" class="D"></path><path d="M132 467c-1-1-1-2-1-3h1v1l1 1-1 1z" class="L"></path><path d="M92 436v-1c1-1 1-2 1-3s3-3 3-5l1-1h0v3h0l-1 6h0c-1-1-1-2-1-3l-1 1-2 4v-1z" class="P"></path><path d="M130 460h2c1 0 2 2 2 2 3 3 6 5 10 6 2 0 4 2 7 3 1 0 3 1 5 1l-1 1c-5-1-10-5-15-6h-1-1c0 1 1 1 1 2l-9-9z" class="H"></path><path d="M128 393l-3-12c-1-5 0-10 0-16 0-1 0-5 1-6 0 7-1 14 0 21 0 2 1 4 1 6 1 2 2 4 2 7h-1z" class="K"></path><defs><linearGradient id="Ac" x1="131.1" y1="361.607" x2="126.08" y2="343.48" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#777577"></stop></linearGradient></defs><path fill="url(#Ac)" d="M131 361c0-1-2-2-3-4 0 0 0-1-1-2v-5c0-2-1-6-1-8h1v-1 1c1 3 1 6 2 9 0 1 1 2 1 3 1 1 2 3 3 4v2 1 2c-1 0-1-1-2-2z"></path><path d="M137 375c1 0 1 1 2 2h0l3 5h1v2c0 2 1 4 2 6v2c0 1 0 3-1 4s-1 0-2 1l2 2 2 3c-2 0-2-1-4-2-1-2-5-6-5-9-1-3-3-5-5-8h0l1-1c0 1 1 2 2 3 0 1 1 2 1 3 1 2 3 4 4 6 1 1 2 2 3 2 1-1 1-2 1-3h0v-1l-2-5 1-1-1-1c-1-4-4-6-5-10h0z" class="L"></path><path d="M133 361c2 1 3 3 4 4l-1 1-2 6v3c0 2 2 4 2 5 1 2 2 5 3 7-1-1-2-3-3-4-2-3-5-4-6-8 0-2 1-6 2-8l1-1c0-1-1-3-2-4v-1c1 1 1 2 2 2v-2zm-27 60c-2 12 1 23 1 34-1 0-1-1-1-2l-3-6c-3-9-1-18 3-26z" class="B"></path><path d="M100 459v-2h1v-4h1v2 1h0c0 1 0 2 1 3v1c1 2 0 5 0 7s1 5 1 6c0 5 0 10-2 14v2h-1v-2c0-1-1-1-2-1v-4-9c1-3 0-7 1-10v-4z" class="E"></path><path d="M100 459c2 3 1 7 1 11v-3l-1 1v-5-4z" class="D"></path><defs><linearGradient id="Ad" x1="103.61" y1="477.624" x2="96.744" y2="473.553" xlink:href="#B"><stop offset="0" stop-color="#323031"></stop><stop offset="1" stop-color="#545554"></stop></linearGradient></defs><path fill="url(#Ad)" d="M99 473c1-3 0-7 1-10v5l1-1v3 14c0 1 1 2 1 3v2h-1v-2c0-1-1-1-2-1v-4-9z"></path><path d="M130 354c1 0 2 1 2 2l1 1c2 2 5 6 8 7h0c0 2 2 4 3 5l1 1v6c0 5-1 11 0 16h0v-2c-1-2-2-4-2-6v-2h-1l-3-5h0c-1-1-1-2-2-2h0c-2-1-2-2-3-3l2-6 1-1c-1-1-2-3-4-4v-1-2c-1-1-2-3-3-4z" class="E"></path><path d="M137 365v2c3 2 6 8 7 11h0l1-1v-1c0 5-1 11 0 16h0v-2c-1-2-2-4-2-6v-2c0-2 0-4-1-6-1-3-3-8-6-10l1-1z" class="H"></path><path d="M130 354c1 0 2 1 2 2l1 1c2 2 5 6 8 7h0c0 2 2 4 3 5l1 1v6 1l-1 1h0c-1-3-4-9-7-11v-2c-1-1-2-3-4-4v-1-2c-1-1-2-3-3-4z" class="D"></path><path d="M133 358l1 1c2 2 3 3 4 5 1 0 1 1 1 1 0 1 1 2 2 4s3 5 4 8l-1 1h0c-1-3-4-9-7-11v-2c-1-1-2-3-4-4v-1-2z" class="E"></path><path d="M127 386h1c1 0 1 1 2 2v1h1l4 4 1-1-1-1s1 0 1 1l1-1c0 3 4 7 5 9 2 1 2 2 4 2 3 2 5 3 7 6v1c-1-1-2-1-2-1 0 1 0 1 1 2h1c1 1 2 3 3 4-2-1-6-5-8-4-2-1-3-3-5-4-1-1-3-2-5-3l-1-1c-3-3-6-5-9-9h1c0-3-1-5-2-7z" class="G"></path><path d="M130 389h1l4 4 1-1-1-1s1 0 1 1l1-1c0 3 4 7 5 9l-1-1c-3-1-9-7-11-10z" class="E"></path><path d="M129 393c2 3 5 5 8 8 1 1 2 2 4 3s5 2 7 3c1 0 3 0 3 1s0 1 1 2h1c1 1 2 3 3 4-2-1-6-5-8-4-2-1-3-3-5-4-1-1-3-2-5-3l-1-1c-3-3-6-5-9-9h1z" class="M"></path><path d="M97 429l3-6c2-2 2-4 4-6h1 0c-3 3-7 10-8 15h0c0 2-1 7 0 9v3c-1 2-1 5 0 6l-1 1 1 2h1c0 1 0 3 1 4v14c0 2-1 5-1 7-1 2-1 5-2 7-1 0-1-1-1-1l1-1v-1h-1c-1 2 0 3-2 5v1l-1 1h0c0-3 2-6 2-9 2-4 1-10 1-15h0v-5c-1 1-1 3-1 4l-1-16c0-2 0-5 1-7l-1-1c-1 2-1 3-1 4s-1 2-1 3 0 2-1 3c0-5 0-8 2-13l2-4 1-1c0 1 0 2 1 3h0l1-6z" class="D"></path><path d="M97 441v3c-1 2-1 5 0 6l-1 1 1 2c1 6 1 12 1 18-1 3-1 5-2 8v-7c0-4 1-8 0-11v-7c0-1-1-2 0-2v-1-5c0-1 0-3 1-5z" class="G"></path><path d="M92 437l2-4 1-1c0 1 0 2 1 3h0c-1 2 0 4-1 6 0 2-1 4-1 6 0 5 1 9 1 14v4h0v-5c-1 1-1 3-1 4l-1-16c0-2 0-5 1-7l-1-1c-1 2-1 3-1 4s-1 2-1 3 0 2-1 3c0-5 0-8 2-13z" class="E"></path><path d="M102 455s1 0 1 1c0 2 2 4 2 6l3 8h0c1-1 2-1 2-2 1-1 1-3 1-4 0-5 0-10-1-15v-6c-1-3-1-7-1-10s-1-6-1-9h1c0 2 0 3 1 4v6c0 1 1 2 1 3v4-2c1 1 1 1 1 2 0-3-1-7-1-10 0-1-1-2-1-3v-9c1-3 2-7 3-10 1-2 1-4 2-5h0l-3 14c-1 4-1 10 0 14 0 5 2 9 1 13v2c2 7 3 16 1 22l-4 3-1 4c-1-1-2-2-2-3l-2-1-1 1c0-1-1-4-1-6s1-5 0-7v-1c-1-1-1-2-1-3h0v-1z" class="L"></path><path d="M105 472c0-1-1-1-1-2v-3l1-1 1 1v2 1l1 3-2-1z" class="E"></path><path d="M107 473c0 1 1 2 2 3l1-4 4-3c2-6 1-15-1-22v-2c3 10 7 20 2 29-4 7-12 11-12 19h0l8-8c3-3 5-6 7-10 0-1 1-2 1-3 2-7 1-11 0-17h0c1 0 2 1 2 1h0c1 3 5 6 5 9 1 2 2 5 2 6-1 4 2 9 1 14h0l-1 1v-1c-1 2 0 4 0 6v2l-1 1-1 3 1 3c0 1-1 3-2 4-1 2-2 4-4 5-2 3-6 5-9 7 0 0-1 0-1 1l4-1c-1 1-2 1-3 1-2 0-4 2-5 3h0 0-1l-1 2 1-1h-1c-2 1-3 3-4 4s-2 1-4 2l-6 3v1c-2 0-4 0-6 1-3 1-5 3-9 3v1h-1c1 0 1 0 2 1h-1c-2 1-3 1-4 1-4 1-6-1-10-3h0 0c-1 0-2-1-2-2v-1c-1-1-1-3-1-4-1 2-1 4-2 6v1c0-4-2-8 0-12 0-1 1-3 1-4h1l2-4 6-6s2-1 3-2c2 0 4-3 6-5h1 0l6-5c1-1 1-2 2-3h-1 0l1-2c1-2 3-3 4-5l1-1v-1c1-2 3-6 2-9 1-4 2-7 2-12 0-1 0-3 1-4v5h0c0 5 1 11-1 15 0 3-2 6-2 9h0l1-1v-1c2-2 1-3 2-5h1v1l-1 1s0 1 1 1c1-2 1-5 2-7 0-2 1-5 1-7v-14c-1-1-1-3-1-4h-1l-1-2h1c1 1 1 2 2 3v2 3c0 2 1 5 0 7 0 1 0 1 1 1 0 1 0 1-1 2v4h0v9 4c1 0 2 0 2 1v2h1v-2c2-4 2-9 2-14l1-1 2 1z" class="B"></path><path d="M119 455c1 0 2 1 2 1h0c1 3 5 6 5 9 0 1 0 2-1 3v-1h-1v1l-1 1-4-14z" class="K"></path><path d="M118 479c1-3 1-6 3-8v10c-1 6-6 12-10 16l-1 1c0-3 3-6 5-8 1-2 3-5 3-7 1-1 1-3 1-4h-1z" class="O"></path><path d="M91 530h-5c-2 1-5-1-7-2-4-4-4-7-5-12 1 1 1 2 2 3 2 3 5 5 8 7h0 1c1 0 1 1 2 1 3 1 6 0 9-1l1 1-6 3z" class="D"></path><defs><linearGradient id="Ae" x1="94.989" y1="522.777" x2="69.399" y2="471.422" xlink:href="#B"><stop offset="0" stop-color="#373738"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#Ae)" d="M94 464c0-1 0-3 1-4v5h0c0 5 1 11-1 15 0 3-2 6-2 9-1 1-2 2-2 3-2 4-5 7-8 10-2 3-5 5-7 8-4 5-6 13-5 19 1 3 2 5 4 6-1 1-1 0-2 0h-1c-2-1-4-4-5-6h0v-5-1c0-4 1-7 3-11 1 0 1 0 1-1 0-2 5-7 7-9h0l6-5c1-1 1-2 2-3h-1 0l1-2c1-2 3-3 4-5l1-1v-1c1-2 3-6 2-9 1-4 2-7 2-12z"></path><path d="M67 509s2-1 3-2c2 0 4-3 6-5h1c-2 2-7 7-7 9 0 1 0 1-1 1-2 4-3 7-3 11v1 5h0c1 2 3 5 5 6h1c1 0 1 1 2 0h2v1h-1c1 0 1 0 2 1h-1c-2 1-3 1-4 1-4 1-6-1-10-3h0 0c-1 0-2-1-2-2v-1c-1-1-1-3-1-4-1 2-1 4-2 6v1c0-4-2-8 0-12 0-1 1-3 1-4h1l2-4 6-6z" class="H"></path><path d="M65 524l1-1v1 5h0c1 2 3 5 5 6h1c1 0 1 1 2 0h2v1h-1s-1 1-2 1-2 0-4-1v-1h-1c-3-3-3-7-3-11z" class="N"></path><path d="M57 523h0l3-3c0 2 0 5-1 8-1 2-1 4-2 6v1c0-4-2-8 0-12z" class="E"></path><path d="M67 509s2-1 3-2c2 0 4-3 6-5h1c-2 2-7 7-7 9 0 1 0 1-1 1-2 4-3 7-3 11v1-1l-1 1v-1-2h0l-1-1v2h-1c0-1 0-3 1-3v-1-1-2c1 0 1-1 1-2h-1c-1 1-2 3-4 5 0 1 0 1-1 1l2-4 6-6z" class="C"></path><defs><linearGradient id="Af" x1="113.414" y1="507.31" x2="102.22" y2="498.958" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient></defs><path fill="url(#Af)" d="M118 479h1c0 1 0 3-1 4 0 2-2 5-3 7-2 2-5 5-5 8l-2 4c-1 4 0 9-1 13 0 2-1 4-2 6-2 1-3 3-4 4s-2 1-4 2l-1-1c-3 1-6 2-9 1-1 0-1-1-2-1h-1 4c4 0 7-4 10-7l3-6c1-5 0-10 2-14 3-8 10-14 15-20z"></path><path d="M98 519v1c-1 2-3 3-4 5 0-1 1-1 2-2 1 0 1 0 1-1 2 0 2 0 3-1 1-2 2-4 3-5v-1l1-1h0 0v2c-1 2-3 7-5 8h-1l-2 2c-3 1-6 2-9 1-1 0-1-1-2-1h-1 4c4 0 7-4 10-7z" class="F"></path><defs><linearGradient id="Ag" x1="81.714" y1="495.585" x2="97.837" y2="496.232" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#Ag)" d="M96 451h1c1 1 1 2 2 3v2 3c0 2 1 5 0 7 0 1 0 1 1 1 0 1 0 1-1 2v4h0v9 4l-2 12c-1 7-2 13-6 19l-4 4-1 1c0-2 1-3 2-4 2-3 3-5 4-8h-1c-1 3-6 7-9 9l6-9v-1c-2 3-5 6-8 7l4-7c-2 1-5 5-6 7 2-6 6-12 9-17 1-2 3-4 4-7h-1c0-1 1-2 2-3h0l1-1v-1c2-2 1-3 2-5h1v1l-1 1s0 1 1 1c1-2 1-5 2-7 0-2 1-5 1-7v-14c-1-1-1-3-1-4h-1l-1-2z"></path><path d="M91 510c1-2 2-5 3-6h0 1c0 2-2 5-3 6h-1zm-7-1c1 0 2-2 2-3l6-8c0 2-1 5-2 7l-2 4c-2 3-5 6-8 7l4-7z" class="D"></path><path d="M126 465c1 2 2 5 2 6-1 4 2 9 1 14h0l-1 1v-1c-1 2 0 4 0 6v2l-1 1-1 3 1 3c0 1-1 3-2 4-1 2-2 4-4 5-2 3-6 5-9 7 0 0-1 0-1 1l4-1c-1 1-2 1-3 1-2 0-4 2-5 3h0 0-1l-1 2 1-1h-1c1-2 2-4 2-6 1-4 0-9 1-13l1 1c2-6 9-9 13-14 2-2 2-4 2-7 0-4 0-9-1-13l1-1v-1h1v1c1-1 1-2 1-3z" class="H"></path><path d="M109 509v1h0l1-1c1-2 3-4 5-4 0 1-1 1-1 2l-2 2-2 2-1 1c-1-1-1-2 0-3z" class="C"></path><path d="M115 505c1-2 3-4 5-5 3-3 5-6 6-10 0-2 1-4 1-6v10l-1 3 1 3c0 1-1 3-2 4h-1l1-2h-1c-3 3-6 6-10 8h0v-3h0c0-1 1-1 1-2z" class="F"></path><path d="M126 497l1 3c0 1-1 3-2 4h-1l1-2h-1c0-1 2-4 2-5z" class="I"></path><path d="M124 502h1l-1 2h1c-1 2-2 4-4 5-2 3-6 5-9 7 0 0-1 0-1 1l4-1c-1 1-2 1-3 1-2 0-4 2-5 3h0 0-1l-1 2 1-1h-1c1-2 2-4 2-6 1-4 0-9 1-13l1 1v6c-1 1-1 2 0 3l1-1 2-2 2-2h0v3h0c4-2 7-5 10-8z" class="M"></path><path d="M107 515l1 1 2 1h0v1c-2 0-3 1-4 2h0l-1 2 1-1h-1c1-2 2-4 2-6z" class="I"></path><path d="M124 502h1l-1 2c-2 5-8 8-13 11l-2 1c2-2 3-3 3-7l2-2h0v3h0c4-2 7-5 10-8zM73 389c-1-1 0-3-1-5l-1-9c-6-39-8-78-4-116 5-53 23-111 65-146 28-23 62-34 97-39 17-2 33-3 50-1 13 1 25 3 38 7 15 5 30 12 44 20 17 10 32 21 44 36 10 12 18 25 25 39 6 10 11 19 15 29 9 24 12 50 14 76 3 26 3 51 1 77-2 12-3 24-6 36-10 46-34 93-71 123-9 7-20 14-30 21-5 3-9 6-14 9 9 12 19 24 30 35 7 8 15 16 24 22 7 6 16 10 26 12 10 1 20-2 28-8s14-15 15-25c1-7 1-15-4-21-2-3-4-5-8-5-3 0-6 0-9 2l-2 3c2-6 4-10 10-13 6-2 13-1 19 1 6 3 10 7 12 13 5 12 0 27-5 38-9 18-25 35-44 42-7 2-13 3-20 4-6 1-12 1-18 1-7 0-13-1-20-2-9-1-18-4-27-8-26-12-48-33-66-55-7-8-13-16-19-23-1 0-1-1-1-2v-1c3-1 6-1 9-2 2-1 3-1 4-2 2-1 3-2 5-2l2-2c3-4 6-11 11-14h0c4-3 8-5 13-7 4-2 9-6 12-9 16-15 26-34 33-53l6-24c1-6 3-12 3-18 4-31 3-62 3-93 0-30 1-61-1-91-1-6-1-13-2-20-3-19-8-37-17-55-11-24-33-44-58-53-21-7-42-8-63-3-10 2-19 4-27 8-19 10-32 23-43 41-3 6-6 11-8 17l-5-5c-2-2-5-7-7-7s-2 0-3 1l1 1v-1 3h0c1 2 1 3 2 5 1 1 2 2 1 4 3 4 5 7 9 9 2 1 4 1 6 2h3l1-1c8 2 18 0 27-2-1 2-2 2-4 3h-2c-3 1-6 1-8 2h1c-1 1-2 1-3 1v1h-1 1c-1 1-2 1-4 1v1l1 1h3v1h-2v1h0c-1 0-1 1-2 1l-1 1-4 3c-3 4-6 8-7 13v1 6c1 2 2 5 2 7l1 5c1 3 2 4 4 6 1 2 2 4 3 5 1 0 1 0 1 1s1 2 1 4c1 1 1 3 2 4v7 4c0-1 0 0 1 0v3c1 2 3 4 4 5 1 0 1 0 2 1h3c3 1 3 0 5 1l1 1h1c-1 1-1 1-1 2h1c2 1 4 5 5 7 0 0 0 1 1 1l-1 1c-1 0-3-1-5-1v1c2 1 5 3 7 4 2 0 3 1 5 1l-30 3 2 1c-2 0-3 1-4 1-2 1-3 3-5 4-3 2-6 4-9 5-2 2-5 5-8 7l-5 6c0 1-1 3-2 3 0 2 0 2-1 4h0l1 1v1c-1 0-1 1-1 2-1 1-1 2-2 3v1c-1 2-2 5-4 8l-2 2c-3 3-5 6-7 10-1 2-2 5-4 7-1 1-2 2-4 3l-3 3c-1 1-2 1-2 1l-1 1 1 1 2-1h0l-1 1v1c0 1-1 1-1 2h0-1c-3 1-6 2-8 4-4 2-7 5-10 8h0c0 2-1 2 0 3l1 1-2 1-3 2c0-1-1-1-2-1z" class="B"></path><path d="M79 383c0 2-1 2 0 3l1 1-2 1-3 2c0-1-1-1-2-1l6-6z" class="T"></path><path d="M112 323l-5 1c4-3 7-6 13-7v1l-4 1v1c1 0 2 0 2 1-2 1-4 1-6 2z" class="S"></path><defs><linearGradient id="Ah" x1="125.414" y1="233.445" x2="118.356" y2="235.82" xlink:href="#B"><stop offset="0" stop-color="#757477"></stop><stop offset="1" stop-color="#888988"></stop></linearGradient></defs><path fill="url(#Ah)" d="M126 228c1-1 2-2 3-2-1 1-2 3-3 5-1 1-1 4-2 5-2 2-4 4-6 5 1-5 5-10 8-13z"></path><path d="M133 215l1-1c1 1 0 2 0 3s0 1-1 2l-1 1c-2 3-4 5-6 7l-6 3 13-15zm-11 97c2 0 3-1 4-1l2-1h0 0c-2 2-3 4-5 5s-4 2-6 2c-3 0-6-3-8-5 5 1 9 0 13 0z" class="R"></path><path d="M278 550l2-2c3-4 6-11 11-14h0c-6 7-9 17-17 21-2 2-5 3-8 3-1 1-4 0-5 1-1 0-1-1-1-2v-1c3-1 6-1 9-2 2-1 3-1 4-2 2-1 3-2 5-2z" class="T"></path><defs><linearGradient id="Ai" x1="268.611" y1="551.798" x2="264.819" y2="557.843" xlink:href="#B"><stop offset="0" stop-color="#151c1d"></stop><stop offset="1" stop-color="#332f31"></stop></linearGradient></defs><path fill="url(#Ai)" d="M260 556c3-1 6-1 9-2 2-1 3-1 4-2 2-1 3-2 5-2-3 4-7 6-11 7h-7v-1z"></path><defs><linearGradient id="Aj" x1="150.03" y1="192.26" x2="132.655" y2="217.889" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#7c7c7d"></stop></linearGradient></defs><path fill="url(#Aj)" d="M133 214c-3 1-5 1-8 1 2-2 4-4 7-6l6-6c6-4 14-7 21-9v1h0c-1 0-1 1-2 1l-1 1-4 3c-3 1-6 3-9 5l-7 4c1 0 3-1 4-1 0 2-4 4-5 5-1 0-2 0-2 1h0z"></path><path d="M118 321c4-2 7-5 11-7 1-1 2-2 3-2 0-1 1-1 2-1l-5 6c0 1-1 3-2 3l-2 2c-1 0-2 1-2 2h0v1l-4 3h0c0 1-1 1-2 2l-1-1 2-2c-1 0-2 0-3 1-4 2-7 3-11 5 2-4 4-7 8-9v-1c2-1 4-1 6-2z" class="R"></path><path d="M118 321c4-2 7-5 11-7 1-1 2-2 3-2 0-1 1-1 2-1l-5 6c0 1-1 3-2 3l-2 2c-1 0-2 1-2 2h0v1l-4 3h0c0 1-1 1-2 2l-1-1 2-2 2-2c-1 0-1-1-2-1 2-1 6-4 7-6-2 0-6 3-8 4-1 1-3 1-4 2h-1v-1c2-1 4-1 6-2z" class="G"></path><path d="M127 259c1 1 1 3 1 4s0 2-1 3c1 2-1 4-1 6l-1 1-13 12c2-8 4-13 8-19v-1c1-1 3-2 4-3h0l3-3z" class="R"></path><path d="M127 259c1 1 1 3 1 4s0 2-1 3c1 2-1 4-1 6l-1 1v-2l1-1v-4h-1c-2 2-3 5-5 6 1-3 3-6 5-10l-5 4v-1c1-1 3-2 4-3h0l3-3z" class="G"></path><path d="M127 240l1 2c-1 0-1 0-1 1l1 1h0c-1 2-2 6-2 8l2-1v1c1 1 1 3 1 4h-1l-1 3-3 3h0c-1 1-3 2-4 3l-6 6c0-6 3-10 6-16h0c0-1 1-2 2-3 1-2 3-4 2-6h-1c0-1 1-1 1-1 0-1 1-2 2-2 0 0 0 1 1 1v-1-3z" class="S"></path><path d="M127 240l1 2c-1 0-1 0-1 1l1 1h0c-1 2-2 6-2 8l2-1v1c1 1 1 3 1 4h-1l-1 3-3 3h0c1-2 2-3 2-6v-1l-7 7c1-1 2-3 3-4v-2s1-1 0-2h0l-2 1h0c0-1 1-2 2-3 1-2 3-4 2-6h-1c0-1 1-1 1-1 0-1 1-2 2-2 0 0 0 1 1 1v-1-3z" class="O"></path><path d="M123 246c0-1 1-1 1-1 0-1 1-2 2-2 0 0 0 1 1 1l-2 8v2c-1 1-2 3-3 4v-2s1-1 0-2h0l-2 1h0c0-1 1-2 2-3 1-2 3-4 2-6h-1z" class="R"></path><path d="M132 220c1 2-1 4-2 6-2 6-1 12-2 18l-1-1c0-1 0-1 1-1l-1-2v3 1c-1 0-1-1-1-1-1 0-2 1-2 2 0 0-1 0-1 1h1c1 2-1 4-2 6-1 1-2 2-2 3l-6 3 1-5c-1-7 5-12 9-16v-1c1-1 1-4 2-5 1-2 2-4 3-5-1 0-2 1-3 2v-1c2-2 4-4 6-7z" class="J"></path><path d="M120 247c2-3 4-6 7-8h0v1 3 1c-1 0-1-1-1-1-1 0-2 1-2 2 0 0-1 0-1 1-1 0-1 1-3 1z" class="P"></path><path d="M123 246h1c1 2-1 4-2 6-1 1-2 2-2 3l-6 3 1-5h1 0l4-6c2 0 2-1 3-1z" class="S"></path><defs><linearGradient id="Ak" x1="127.662" y1="240.288" x2="114.64" y2="242.931" xlink:href="#B"><stop offset="0" stop-color="#6d6b6e"></stop><stop offset="1" stop-color="#8c8c8c"></stop></linearGradient></defs><path fill="url(#Ak)" d="M124 237c1-2 2-5 4-7 0 2-1 5-1 7-2 2-4 4-5 6-3 3-5 7-6 10h-1c-1-7 5-12 9-16z"></path><defs><linearGradient id="Al" x1="114.259" y1="320.744" x2="108.661" y2="346.452" xlink:href="#B"><stop offset="0" stop-color="#666665"></stop><stop offset="1" stop-color="#807f82"></stop></linearGradient></defs><path fill="url(#Al)" d="M118 327l-2 2 1 1c1-1 2-1 2-2h0l4-3v-1h0c0-1 1-2 2-2l2-2c0 2 0 2-1 4h0c-1 2-3 4-3 6-5 7-13 12-21 14-2 1-5 1-7 1l16-13c1-1 4-3 4-4h0c1-1 2-1 3-1z"></path><defs><linearGradient id="Am" x1="132.785" y1="300.141" x2="136.597" y2="315.279" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#7c7b7d"></stop></linearGradient></defs><path fill="url(#Am)" d="M131 303c2 0 3-1 4-1 4-2 8-4 11-6 2 0 4-1 5-1s3 0 4-1c1 0 2-1 3-1l2 1c-2 0-3 1-4 1-2 1-3 3-5 4-3 2-6 4-9 5-2 2-5 5-8 7-1 0-2 0-2 1-1 0-2 1-3 2-4 2-7 5-11 7 0-1-1-1-2-1v-1l4-1v-1c0-1 2-1 3-2 2-1 3-3 5-5h0 0l-2 1c-1 0-2 1-4 1v-1h0c1 0 2-1 3-1v-1h1v-1l2-2h-1l1-1c1-1 2-2 3-2z"></path><path d="M128 306h2 1 0 0 1 0 2c1 0 1-1 2 0h0c-1 1-3 2-4 3h-2-1c-1 0-1 1-2 0h-1 0v-1l2-2z" class="E"></path><defs><linearGradient id="An" x1="96.477" y1="370.537" x2="88.079" y2="361.967" xlink:href="#B"><stop offset="0" stop-color="#69696a"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#An)" d="M99 356c1 0 2-1 3-3h0 1c-1 2-3 4-5 6 0 1 1 1 2 1-1 1-2 2-4 2v1h3l-1 1c2-1 3-2 4-2h1l-3 3c-1 1-2 1-2 1l-1 1 1 1 2-1h0l-1 1v1c0 1-1 1-1 2h0-1c-1 0-1 0-2 1-3 1-6 1-9 2-2 1-3 2-5 3v-1c2-9 7-17 15-22h0 2 0c1 0 1-1 1-1h2c-1 1-2 2-2 3z"></path><path d="M98 364c2-1 3-2 4-2h1l-3 3c-1 1-2 1-2 1-3 0-6 2-8 3v-1c3-1 5-3 8-4z" class="E"></path><path d="M99 356c1 0 2-1 3-3h0 1c-1 2-3 4-5 6l-1 1c-1 1-2 2-3 2h-1c0-1 1-1 1-2h0c1-2 3-3 5-4z" class="Q"></path><defs><linearGradient id="Ao" x1="113.977" y1="350.968" x2="95.759" y2="343.08" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#Ao)" d="M123 330c0-2 2-4 3-6l1 1v1c-1 0-1 1-1 2-1 1-1 2-2 3v1c-1 2-2 5-4 8l-2 2c-3 3-5 6-7 10-1 2-2 5-4 7-1 1-2 2-4 3h-1c-1 0-2 1-4 2l1-1h-3v-1c2 0 3-1 4-2-1 0-2 0-2-1 2-2 4-4 5-6h-1 0c-1 2-2 3-3 3 0-1 1-2 2-3h-2s0 1-1 1h0-2 0c-2 0-5 1-7 2 3-4 7-8 12-10 2-1 5-2 7-3 4-2 6-4 10-6 2-2 4-4 5-7z"></path><path d="M102 362c3-3 4-9 7-13 2-2 4-3 5-5 5-3 8-7 10-13h0v1c-1 2-2 5-4 8l-2 2c-3 3-5 6-7 10-1 2-2 5-4 7-1 1-2 2-4 3h-1z" class="B"></path><defs><linearGradient id="Ap" x1="131.32" y1="288.363" x2="111.123" y2="292.592" xlink:href="#B"><stop offset="0" stop-color="#707070"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#Ap)" d="M127 259l1-3h1c1 4 2 10 5 13v1h-1c-1 1-1 5-1 6 0 5-1 10-4 14-1 1-1 3-2 3v1l-1-1-5 8c-1 1-1 2-1 2l-1 1v1l-1-2c0 3 0 4 1 6l-1 1h0c-2-1-3-3-4-5-1-3 0-6 0-10 0-3 1-7 3-10s5-5 7-8c1-1 2-3 3-5h0c0-2 2-4 1-6 1-1 1-2 1-3s0-3-1-4z"></path><defs><linearGradient id="Aq" x1="133.501" y1="262.609" x2="116.577" y2="276.869" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#707071"></stop></linearGradient></defs><path fill="url(#Aq)" d="M127 259l1-3h1c1 4 2 10 5 13v1h-1c-1 1-1 5-1 6v-1h-1c0 1-1 1-2 1-2 3-4 7-8 8 0-1 0-1 1-2 2-2 6-7 6-10v-2l-2 2h0c0-2 2-4 1-6 1-1 1-2 1-3s0-3-1-4z"></path><path d="M121 284c4-1 6-5 8-8 1 0 2 0 2-1h1v1c0 5-1 10-4 14-1 1-1 3-2 3v1l-1-1-5 8c-1 1-1 2-1 2l-1 1v1l-1-2v-2c0-3 0-5 1-7 0-2 1-3 2-4l2-2c0-1 1-2 2-3 1 0 1-1 1-2-2 1-6 4-8 5l4-4z" class="Q"></path><path d="M117 301c0-1 0-2 1-2l2 2c-1 1-1 2-1 2l-1 1v1l-1-2v-2z" class="P"></path><path d="M122 288c1 0 2-1 2-2 1-1 4-3 5-4 0 3-3 6-6 9-1 1-2 3-4 5h0c1-3 3-5 4-7h0c-1 0-2 1-3 1l2-2z" class="E"></path><path d="M121 284c4-1 6-5 8-8 1 0 2 0 2-1h1v1c0 5-1 10-4 14-1 1-1 3-2 3v1l-1-1c1-1 2-2 3-4 2-3 3-8 3-12l-2 5c-1 1-4 3-5 4 0 1-1 2-2 2 0-1 1-2 2-3 1 0 1-1 1-2-2 1-6 4-8 5l4-4z" class="K"></path><defs><linearGradient id="Ar" x1="152.838" y1="196.706" x2="129.982" y2="168.291" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#Ar)" d="M127 163l1 1v-1 3h0c1 2 1 3 2 5 1 1 2 2 1 4 3 4 5 7 9 9 2 1 4 1 6 2h3l1-1c8 2 18 0 27-2-1 2-2 2-4 3h-2c-3 1-6 1-8 2h1c-1 1-2 1-3 1v1h-1 1c-1 1-2 1-4 1v1l1 1-5 1c-6 2-15 2-18 8 0 1-1 1-1 2s-1 1-1 2h-1v-5c-1-5-1-11-3-15l-1-8-2-9v-5l1-1z"></path><path d="M163 188h1c-1 1-2 1-3 1-3 1-6 1-9 1v-1l11-1z" class="C"></path><defs><linearGradient id="As" x1="148.555" y1="186.452" x2="141.105" y2="189.915" xlink:href="#B"><stop offset="0" stop-color="#3c3c3d"></stop><stop offset="1" stop-color="#585657"></stop></linearGradient></defs><path fill="url(#As)" d="M146 190h-4c-2-1-5-4-6-6 2 1 3 2 5 3s4 0 6 1c1 1 3 1 5 1v1h-6 0z"></path><path d="M129 186v-3c2 1 3 8 3 10l2 11c0 1-1 1-1 2h-1v-5c-1-5-1-11-3-15z" class="T"></path><path d="M161 189v1h-1 1c-1 1-2 1-4 1v1l1 1-5 1c1-1 1-1 2-1 0-1 0-1 1-2h0 1 1l1-1h0c-1 0-3 0-4 1s-2 1-2 1h-1c-3 1-10 1-12 0l-1-1h7v-1h0 6c3 0 6 0 9-1z" class="L"></path><defs><linearGradient id="At" x1="139.31" y1="201.93" x2="137.487" y2="247.506" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#At)" d="M135 213c1-1 5-3 5-5-1 0-3 1-4 1l7-4c3-2 6-4 9-5-3 4-6 8-7 13v1 6c1 2 2 5 2 7-2 2-1 5-2 8h0c0 1 0 1-1 2l-1-1c1-2 2-3 1-5-1 2-2 3-3 5l-1 1h-1c-2 1-3 3-5 5v-1l-2 2h0l-4 8-2 1c0-2 1-6 2-8h0c1-6 0-12 2-18 1-2 3-4 2-6l1-1c1-1 1-1 1-2s1-2 0-3l-1 1v-1h0c0-1 1-1 2-1z"></path><path d="M128 244v3h0 0c1 0 1-1 1-1 1-2 1-2 3-3l-4 8-2 1c0-2 1-6 2-8z" class="Q"></path><path d="M142 214h0c0 3-2 5-3 8s-1 8-3 10c0-4 0-8 2-11 1-2 3-5 4-7z" class="B"></path><path d="M135 213h0c2 0 3-2 5-2-1 1-3 4-3 6h0c0 2-3 3-4 5 0 1 1 1 1 2 0 0-1 2-1 3h0v-2-1-1-1c-1 2-2 3-2 5l-1-1c1-2 3-4 2-6l1-1c1-1 1-1 1-2s1-2 0-3l-1 1v-1h0c0-1 1-1 2-1z" class="G"></path><path d="M134 241c1-2 3-3 4-5v-1h-1 0c1-1 2-3 3-4v-1c1-3 2-7 3-10 0-3 0-4 2-6v6c1 2 2 5 2 7-2 2-1 5-2 8h0c0 1 0 1-1 2l-1-1c1-2 2-3 1-5-1 2-2 3-3 5l-1 1h-1c-2 1-3 3-5 5v-1z" class="E"></path><path d="M145 220c1 2 2 5 2 7-2 2-1 5-2 8h0c0 1 0 1-1 2l-1-1c1-2 2-3 1-5 1-4 1-8 1-11z" class="H"></path><path d="M134 269h0c2 0 3 2 4 3l12 5c2 1 4 2 5 3h6c2 0 4 1 5 0l9-4h1c2 1 4 5 5 7 0 0 0 1 1 1l-1 1c-1 0-3-1-5-1v1c2 1 5 3 7 4 2 0 3 1 5 1l-30 3c-1 0-2 1-3 1-1 1-3 1-4 1s-3 1-5 1c-3 2-7 4-11 6-1 0-2 1-4 1-1 0-2 1-3 2l-1 1h1l-2 2v1h-1v1c-1 0-2 1-3 1h0c-2 0-3 0-5-1l1-1c-1-2-1-3-1-6l1 2v-1l1-1s0-1 1-2l5-8 1 1v-1c1 0 1-2 2-3 3-4 4-9 4-14 0-1 0-5 1-6h1v-1z" class="B"></path><path d="M145 282v-1h1c1 1 2 3 3 3l2 3c3 3 5 4 9 5-6 0-9 0-13-4-1-1-1-2-2-2 0 1 1 3 2 5-2-2-4-3-6-5h3v-3l1-1z" class="K"></path><path d="M145 282v-1h1c1 1 2 3 3 3l2 3c0 1 0 1-1 1-2-2-5-4-5-6z" class="G"></path><defs><linearGradient id="Au" x1="154.881" y1="290.756" x2="153.471" y2="274.348" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#5c5b5a"></stop></linearGradient></defs><path fill="url(#Au)" d="M140 275c4 1 6 3 8 4s4 1 6 2c1 0 2 1 4 1 4 3 7 6 12 9-5 0-9-1-14-3-1-1-5-4-7-4-1 0-2-2-3-3h-1v1l-1 1c-1-2-1-4-2-6-1 0-1-1-2-2z"></path><path d="M119 303c4 1 9-2 13-4 2-1 3-2 6-2 2 0 5-1 7-1l-14 7c-1 0-2 1-3 2l-1 1h1l-2 2v1h-1v1c-1 0-2 1-3 1h0c-2 0-3 0-5-1l1-1c-1-2-1-3-1-6l1 2v-1l1-1z" class="G"></path><path d="M117 303l1 2v-1h2c1 1 4 3 6 3 0 0 1 0 1-1h1l-2 2v1h-1v1c-1 0-2 1-3 1h0c-2 0-3 0-5-1l1-1c-1-2-1-3-1-6z" class="B"></path><path d="M117 303l1 2c1 2 1 3 3 4h4v1c-1 0-2 1-3 1h0c-2 0-3 0-5-1l1-1c-1-2-1-3-1-6z" class="Q"></path><path d="M132 276c0-1 0-5 1-6h1l1 1c0 1 1 2 1 4 1 1 1 2 2 3 1 2 1 5 2 7 0 2 1 6 2 7 4 2 9 1 13 2-5 0-9 1-13 1-2 0-3 1-4 1 0-1 0-1 1-1h1v-1h-1l-1-10c-1 2-1 4-2 6-1 3-5 6-7 8-2 1-3 1-5 2h-1l3-6v-1c1 0 1-2 2-3 3-4 4-9 4-14z" class="E"></path><path d="M131 289l1-1c1 0 1 1 2 1-2 3-4 5-6 7 1-2 2-4 3-7z" class="Q"></path><path d="M132 276c0-1 0-5 1-6h1l1 1c0 3 0 6 1 8 0 3-1 8-2 10-1 0-1-1-2-1l-1 1h0c-2 2-3 5-4 7-1 1-3 2-3 4h-1l3-6v-1c1 0 1-2 2-3 3-4 4-9 4-14z" class="G"></path><path d="M134 269h0c2 0 3 2 4 3l12 5c2 1 4 2 5 3h6c2 0 4 1 5 0l9-4h1c2 1 4 5 5 7 0 0 0 1 1 1l-1 1c-1 0-3-1-5-1v1c-3-1-6-3-9-4 3 2 6 5 9 7 1 1 3 1 4 2h-5c-6-2-11-8-17-8-2 0-3-1-4-1-2-1-4-1-6-2s-4-3-8-4c1 1 1 2 2 2 1 2 1 4 2 6v3h-3v-1h-1c-1-2-1-5-2-7-1-1-1-2-2-3 0-2-1-3-1-4l-1-1v-1z" class="D"></path><path d="M141 285l-2-7c0-1-1-3-1-4h1l1 1c1 1 1 2 2 2 1 2 1 4 2 6v3h-3v-1z" class="G"></path><path d="M144 237c1-1 1-1 1-2h0c1-3 0-6 2-8l1 5c1 3 2 4 4 6 1 2 2 4 3 5 1 0 1 0 1 1s1 2 1 4c1 1 1 3 2 4v7 4c0-1 0 0 1 0v3c1 2 3 4 4 5 1 0 1 0 2 1h3c3 1 3 0 5 1l1 1h1c-1 1-1 1-1 2l-9 4c-1 1-3 0-5 0h-6c-1-1-3-2-5-3l-12-5c-1-1-2-3-4-3h0c-3-3-4-9-5-13 0-1 0-3-1-4v-1l4-8h0l2-2v1c2-2 3-4 5-5h1l1-1c1-2 2-3 3-5 1 2 0 3-1 5l1 1z" class="B"></path><path d="M158 274c2 1 3 3 5 3l3 1c-2 0-4 1-7 0 1 0 1 0 1-1h-1l1-1-2-2zm3-2c3 1 6 1 8 2v1c-1 1-4 0-6-1h-2v-2z" class="C"></path><path d="M153 268c1 1 5 6 5 6l2 2-1 1h1c0 1 0 1-1 1l-2-1h0c-1-2-2-3-3-5v-1c-1-1-1-2-1-3z" class="H"></path><path d="M134 242c2-2 3-4 5-5l-1 4c-2 4-5 10-4 15 0 0 1 1 1 2s-1 3-2 4l-3-12c0-3 2-6 4-8z" class="Q"></path><path d="M146 265v-2h1c0-1 0-1-1-2v-2c0-1 0-1 1-2v1c0 1 1 2 1 3l5 7c0 1 0 2 1 3v1c1 2 2 3 3 5h0-1c-5-1-8-4-10-8v-4z" class="L"></path><path d="M139 237h1l1-1c1-2 2-3 3-5 1 2 0 3-1 5l1 1v1c1 1 1 1 1 2-1 7-2 15-1 23h0l1-1v1h1v2 4c-1 1 0 2 0 3-1 0-1-1-1-1-1-1-2-1-2-2-1-1-2-1-3-2 0-1-1-2-2-2v1c-1-1-2-4-2-6v-3h-1v1c0-1-1-2-1-2-1-5 2-11 4-15l1-4z" class="E"></path><path d="M143 236l1 1v1l-1 4v-1-1c0-1 0-1-1-3l1-1z" class="D"></path><path d="M144 238c1 1 1 1 1 2-1 7-2 15-1 23h0l1-1v1h1v2 4c-1 1 0 2 0 3-1 0-1-1-1-1-1-1-2-1-2-2v-1c-1-3-1-6-1-9 0-2-1-4 0-7 0-3 1-6 1-10h0l1-4z" class="P"></path><path d="M144 263l1-1v1h1v2 4c-1 1 0 2 0 3-1 0-1-1-1-1 0-2-1-6-1-8z" class="C"></path><path d="M139 237h1l1-1c1-2 2-3 3-5 1 2 0 3-1 5l-1 1c-1 2-2 4-2 5v2c0 1-1 2-1 3-1 2-2 7 0 9 1-2 0-5 1-7v1c0 3 2 15 0 17 0-1-1-2-2-2v1c-1-1-2-4-2-6v-3h-1v1c0-1-1-2-1-2-1-5 2-11 4-15l1-4z" class="Q"></path><path d="M138 241h1l-1 1c0 1-1 2-1 3-1 3-2 7-2 9h1v-2c1 3 0 6 0 8v-3h-1v1c0-1-1-2-1-2-1-5 2-11 4-15z" class="G"></path><defs><linearGradient id="Av" x1="140.838" y1="233.35" x2="141.48" y2="241.423" xlink:href="#B"><stop offset="0" stop-color="#4a4a4a"></stop><stop offset="1" stop-color="#636163"></stop></linearGradient></defs><path fill="url(#Av)" d="M139 237h1l1-1c1-2 2-3 3-5 1 2 0 3-1 5l-1 1c-1 2-2 4-2 5v2c0 1-1 2-1 3v-2-3l-2 3c0-1 1-2 1-3l1-1h-1l1-4z"></path><path d="M144 237c1-1 1-1 1-2h0c1-3 0-6 2-8l1 5c1 3 2 4 4 6 1 2 2 4 3 5 1 0 1 0 1 1s1 2 1 4c1 1 1 3 2 4v7 4c-1 2-2 4-1 6l3 3v2c-3-2-4-3-6-6l-2-3-3-3c-1-2-1-4-2-6l-1 1c-1 1-1 1-1 2v2c1 1 1 1 1 2h-1v2-2h-1v-1l-1 1h0c-1-8 0-16 1-23 0-1 0-1-1-2v-1z" class="E"></path><path d="M154 262l1 1v5l-2-3 1-3z" class="F"></path><path d="M155 254v9l-1-1c0-2 0-5 1-8z" class="C"></path><path d="M145 240l1-4v4c1 1 1 1 1 2 0 3-1 5-2 8 0 4 0 9 1 13h-1v-1l-1 1h0c-1-8 0-16 1-23zm6 7l-2-7c1 0 2 2 3 3 0 2 1 3 2 5 0 2 1 5 1 6-1 3-1 6-1 8l-1 3-3-3c1-1 1-1 1-2 1-4 0-8 0-13z" class="D"></path><path d="M151 247h0c1 4 3 11 1 14v1l-1-1v-1c1-4 0-8 0-13z" class="C"></path></svg>