<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="51 40 513 628"><!--oldViewBox="0 0 608 752"--><style>.B{fill:#e3e2e4}.C{fill:#242324}.D{fill:#444445}.E{fill:#3a3a3b}.F{fill:#cbc9ca}.G{fill:#ebe9ea}.H{fill:#dad9da}.I{fill:#131313}.J{fill:#292829}.K{fill:#797879}.L{fill:#bcbbbc}.M{fill:#d3d2d3}.N{fill:#343434}.O{fill:#303030}.P{fill:#a2a0a1}.Q{fill:#f0eeef}.R{fill:#4e4d4e}.S{fill:#6b6a6b}.T{fill:#727171}.U{fill:#818080}.V{fill:#5c5c5c}.W{fill:#f5f3f5}.X{fill:#636263}.Y{fill:#c3c2c2}.Z{fill:#8d8c8c}.a{fill:#888687}.b{fill:#191819}.c{fill:#404040}.d{fill:#b2b0b2}.e{fill:#1d1c1c}.f{fill:#1e1d1e}.g{fill:#a5a4a3}.h{fill:#585758}.i{fill:#0a0909}.j{fill:#acabac}.k{fill:#969596}.l{fill:#a5a5a5}.m{fill:#fcfcfc}.n{fill:#939294}.o{fill:#2d2d2d}.p{fill:#525253}</style><path d="M493 389h1c1 0 1 0 1 2h0-2c-1 0-1 0-1-1l1-1z"></path><path d="M120 472h2v3h-1-2l-1-1c1 0 2-1 2-2z" class="R"></path><path d="M107 586c0 1 1 2 2 3l1 1-1 2-4-5c1 0 1 0 2-1z" class="f"></path><path d="M476 326c-2 1-3 2-5 3v-1c0-1 1-2 2-3 1 0 2 0 3 1z" class="J"></path><path d="M80 508h2c1 1 2 1 3 1v2l-9-2h4v-1zm67-219h1l2 2c-1 1-1 2-2 2s-1-1-2-1c0-2 0-2 1-3z" class="C"></path><path d="M69 509c1 0 5 0 6-1-2 0-3 1-4 0v-1c1 1 7 1 9 1v1h-4-7z" class="e"></path><path d="M110 590l6 3-2 1h0c0 1 0 1-1 1-1-1-3-2-4-3l1-2z" class="D"></path><path d="M392 544l5 2-2 5c-1-1 0-1 0-2s-3-3-4-4l1-1z" class="O"></path><path d="M156 426l2-5v10h0l-1-1s0 1-1 1v-1l-1-1s1-2 1-3z" class="J"></path><path d="M93 276h1l2 2c0 1 0 1-1 2h-2s-1-1-1-2 0-1 1-2z"></path><path d="M524 482h2c-2 2-3 4-3 6v1l-1 1v-2h-1l-1-1 3-3 1-2z" class="e"></path><path d="M518 619c-1-1-2-2-1-3 0-1 0-2 1-3 1 0 2 0 3 1h-1v2c0 1-2 2-2 3h0zm-418-40l1-1 1 1c1 3 4 5 5 7-1 1-1 1-2 1-2-2-3-5-5-8z" class="I"></path><path d="M228 463c0-5 0-10 1-15v2c1 1 1 1 1 2v5 1h0v1l-1-1-1 5z" class="V"></path><path d="M229 450c1 1 1 1 1 2v5 1h0v-5h-1v-3z" class="D"></path><path d="M521 614c1 0 1 1 2 2 0 1 0 2-1 3s-2 1-3 1c0 0 0-1-1-1h0c0-1 2-2 2-3v-2h1z"></path><path d="M96 583h1l2 2c0 1 0 2-1 3-1 0-2 0-3-1-1 0-1-1-1-2l2-2z" class="i"></path><path d="M439 337c3 2 5 4 7 7l1 2c-2 0-5-5-6-6s-2-2-2-3z" class="E"></path><path d="M515 480h-2c-1 0-2 1-3 1 0-1-1-1-1-1-1-1 0-2 0-3 1 0 1-1 2-1v-1l1 1c-1 1-1 1-1 2 1 0 2 0 3-1v-1c0 1 1 2 1 3v1z" class="D"></path><path d="M92 320h1c2 1 2 1 2 3 0 1 0 1-1 2h-2c-1-1-2-1-2-3 0-1 1-1 2-2z" class="I"></path><path d="M522 430c1 0 1 0 2 1s1 1 1 2-1 1-2 2h-1c-1-1-2-1-2-3l2-2z"></path><path d="M494 443c-1 1-1 2-1 3s1 1 0 2v1c-1 0-1-1-2-1 0-1-1-2-1-3s1-2 2-3 3-1 4-1v1l-1 1h-1z" class="N"></path><path d="M90 508c1 0 2 0 3 1 2 1 6 0 7 1-3 0-5-1-8 0-2 0-5 1-7 1v-2c-1 0-2 0-3-1h8z" class="E"></path><path d="M528 418c1 0 1 1 2 1 1 1 1 1 1 3 0 1 0 1-1 2-2 0-2 0-3-1 0-1-1-2-1-3l2-2z" class="e"></path><path d="M114 355h0c2 0 5-1 7 0 0 2 1 4 2 6h-1c-1-1-2-2-3-2v1 1c-1-1-1-3-1-4h1l-1-2c-1 0-2 1-4 0h0z" class="V"></path><path d="M326 420h1c-2 2-3 5-5 8 0-1 0-2-1-3 1-1 1-3 1-5 1 1 0 2 1 3 1-1 1-2 2-3h0 1z" class="L"></path><path d="M254 633h1c-1 2 0 5 1 7v1l1-1v-1c1 0 2 0 3 1l1 3h0l-1-1c-1 0-2 1-3 2v-1c-2-1-3-6-4-8l1-2z" class="N"></path><path d="M99 569c1 3 1 5 3 7v1 2l-1-1-1 1c-1-3-2-6-3-10h2z" class="f"></path><path d="M73 562h1c2 1 2 1 2 2 0 2 0 2-1 3h-2c-1-1-2-2-2-3s1-2 2-2z"></path><path d="M541 454c1-1 1 0 2 0 1 1 1 1 1 2s0 2-1 2l-1 1h-2c0-1-1-2-1-3s1-2 2-2z" class="C"></path><path d="M324 388h0c1-1 2 0 3 1v2 2c-1 1-1 1-1 2s0 1-1 2v-1-1h-1v-7z" class="W"></path><path d="M135 502c1-1 1-2 2-2 1 2 0 6 0 8 1 3 2 5 3 7h0c-4-4-5-8-5-13z"></path><path d="M97 556l1 1v2 4 1h0l1 5h-2v-2-3c-1-2-1-5 0-8z" class="N"></path><path d="M97 564v-3h0c1 0 1 1 1 2v1h0v1h-1 0v2-3z" class="e"></path><path d="M97 567v-2h0 1v-1l1 5h-2v-2z" class="I"></path><path d="M293 422c1 0 1 0 1-1 0-2 0-7 1-9 1 1 0 5 0 6v2h1 0c0-1 1-2 2-2v3 2h-3l-1 1-1-1h1v-1h-1z" class="g"></path><path d="M396 640h1l10 5h0l2 2h1 1 1 0l2 1c-3 1-7 0-9-2l-3-3-6-3z" class="e"></path><path d="M79 531c-3 2-4 8-7 9h-1c2-3 3-6 5-9 1-2 2-3 3-5l1 2-1 1-1 1h1v1z"></path><path d="M297 400l1-1c1-1 1-1 2-1s1 1 1 1h2c1 0 1 3 2 5h0v2c1 0 1 1 1 2h0-1 0l-1-2h-1v1h-1l1-1c-1-2 0-5-1-6h-3-1-1z" class="F"></path><path d="M343 595c2-1 5-2 7-2 1 1 2 1 3 1-2 2-5 3-8 4-1-1 0-1-1-2-1 0-1 1-3 1v-1l2-1z" class="X"></path><path d="M98 564c2 1 2 1 2 3 2 4 2 7 5 10l-1 2-2-2v-1c-2-2-2-4-3-7l-1-5h0z" class="P"></path><path d="M319 390h2v-3h1v3c1 2 1 4 1 6-1 1-1 3-2 4l-1-1v1-1c0-3 1-7-1-9z" class="B"></path><path d="M295 207c2-2 5-6 8-6 1 0 2 0 2 1 2 1 4 6 5 8h-2l-2-3v-1c0-1-1-3-3-4-3 1-5 3-8 5h0z" class="b"></path><path d="M118 357c-2 0-4 0-6-2-2-1-4-7-4-9h1c3 2 3 6 5 9h0c2 1 3 0 4 0l1 2h-1zm377-53c2-2 5-6 8-6 1 1 1 1 0 2 0 1-1 3-2 5h-1l-1 1v-1-2c-1 0-3 1-4 1z"></path><path d="M379 201v3c0 1 0 1-1 2v3-2l-1-1c-1 0-2 1-2 2h-1c-2 0-3 1-4 2 0-1 1-1 1-2 2-3 5-5 8-7z" class="p"></path><path d="M60 455h1c1 0 1 0 2 1s1 1 1 3c0 1 0 1-2 2h-2c-1-1-1-2-2-3 1-1 1-2 2-3z"></path><path d="M512 516c2-1 5-1 7 0h1l1 1c0 1 2 2 2 3 0 2-1 2-2 4h0c-1-2 1-4-2-5-2-1-3-1-5-1v-1h0l-2-1z" class="C"></path><path d="M99 426c1-1 2-1 4 0 0 1 1 2 1 3-1 1-1 2-2 2h-2s-1 0-1-1c-1 0-2-1-2-2 1-2 1-2 2-2z"></path><path d="M305 438h1v2c1 0 1 0 1-1v-1s1 0 1-1v1h0v2l-1 3c0 1-1 1-1 2v1c-1 1-1 1-1 2h-1-1v-5c1-1 1-3 2-5z" class="W"></path><path d="M546 577c1 0 2 0 3 1s1 2 1 3c-1 1-2 2-3 2h0c-2 1-2 0-3-1v-3c1-1 1-2 2-2z"></path><path d="M133 504h2v-2c0 5 1 9 5 13h0v1 1h0c-4-5-7-7-7-13z" class="G"></path><defs><linearGradient id="A" x1="230.487" y1="467.554" x2="227.677" y2="459.48" xlink:href="#B"><stop offset="0" stop-color="#39373b"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M230 458h0c0 1 0 2 1 3v6c-1 1-1 3-2 5s0 6 0 8h-1v-17l1-5 1 1v-1z"></path><path d="M509 352c3 3 6 6 6 11 0 2-1 3-1 5l-1 1c-1-1-1-3-2-4 0-2-2-4-3-5-2-2-2-4-3-6 1 0 1 0 1-1 1 6 5 7 7 12h0 1v-2c0-4-2-7-5-10v-1h0zm-9 75c1-1 3-2 5-2 1 0 3 0 4 1 3 1 5 3 7 6-2-1-4-1-6-1s-5 2-7 1l1-2h0 1v1h0 1c2-1 4-1 6-1-3-3-5-5-10-3h-2 0z"></path><path d="M145 416l2 2c0 1 1 1 1 2h-1v1c1 1 1 5 1 7-1 0-1 0-1 1 0 0-1 1-1 2v-2 2c-1 1-1 1-1 2l-1 1c0-2 1-4 1-5v-10-3z" class="Y"></path><path d="M302 382l1 1v1h0c-1 1-2 3-2 5 2-1 2-5 4-5h0l-2 4c0 1 1 3 0 4h-1v-2c-1 0-1 1-2 2h-1s-1-1-2-1c1-1 1-4 2-5s2-2 3-4z" class="H"></path><path d="M90 419s0 1-1 1l-1-1c-2-2-3-3-6-3s-5 0-7 2c-4 4-1 10-5 14v-1h0c1-4 0-7 2-11 1-2 3-4 6-5s5-1 8 0l4 3v1z"></path><path d="M78 496c-5-5-6-11-7-18 2 3 3 4 6 6h0 0l-1 1-3-3c2 4 3 9 6 12l-1 2z" class="C"></path><path d="M280 644c1-1 2-2 4-2 3 0 5 2 7 3 1 1 3 1 4 2h0v1h-4c-4-1-6-4-10-4h0-1z" class="N"></path><path d="M412 368c0 1 1 1 2 1v-1c1 1 1 1 1 2s0 2 1 3 1 2 1 4h0v1h1v5l-1 1c-1-2-1-7-1-9h-1c0 1 1 7 1 8l-1-1c-1-2 0-4-1-6 0-1 0-1-1-1-1-2-1-4-1-7z" class="D"></path><path d="M362 504h16 20c-4 2-8 3-13 2h-3v-1h-12-5 0l-3-1z" class="j"></path><path d="M502 343c2 3 2 5 2 8h1c-2 3-3 5-5 6-2 0-3 0-5-1-1 0-2-1-2-3-1-1-1-2 0-3 1-2 1-2 2-2h1l-1 4 1 1h0c-1 0-1-1-2-1v-2c-1 1-1 1 0 2 1 2 2 3 3 3 2 0 3 0 5-1 2-2 2-5 1-7 0-1-1-2-1-4z"></path><path d="M228 485c3 0 3 0 5 2l1-1h2l2 2c0 1 1 1 1 2l-7-1h-3-1v-4z" class="J"></path><path d="M233 487l1-1h2l2 2h-1-2l-2-1z" class="E"></path><path d="M228 485c3 0 3 0 5 2l2 1h-6 0v1h3-3-1v-4z" class="V"></path><path d="M89 485l1 1c1 9 7 16 13 22 1 1 1 1 2 1 1 2 4 3 6 4l-1 1c-1-1-3-3-4-3v1c-1 0-2-1-3-1v-1h-1v-1l-1-1h0c0-1-5-5-6-7-3-5-5-10-6-16z" class="C"></path><path d="M123 269h0c3 0 4-2 7-2v1c-1 1-2 0-3 1-7 2-9 7-10 13v3c-1 1-1 1-1 3l-1-2c0-2 0-6 1-9s3-5 5-8l1 1 1-1z" class="R"></path><path d="M144 250c3-1 8-2 10-1l1 1h0c-2 2-3 2-5 2-1 1-2 3-4 4l-2-2c1 0 1 0 1-1l-1-1c1 0 1-1 0-2z" class="C"></path><path d="M518 486h2v1l1 1h1v2l1-1v-1l1 1c0 2-1 4-2 6-1 0-2-1-2-1-1 1-2 3-3 3h-1c0-2 2-5 2-7v-1l1 1v-1c0-1-1-2-1-3z" class="S"></path><path d="M518 486h2v1l1 1h1v2s-1 2-2 2c0-1-1-1-2-1l1-1v-1c0-1-1-2-1-3z" class="I"></path><path d="M478 425c-1 3-5 8-6 11v1h0c-1-1-2-2-2-3-1-1-4-6-4-7 1 1 3 5 4 5 1-1 1-1 1-2 1-3 1-5-1-8h0v-1c1 0 1 1 2 2 1 3 1 5 0 8 0 2-1 3-1 4 2-3 4-7 5-11h2v1h0z" class="O"></path><path d="M455 629l1 3c1 3 4 4 6 7h0 1c0 1-1 2 0 3h0 1c-2 1-2 3-3 4s-2 1-3 2l1 1v-1l1 1h-2l-2-2c1-2 3-1 5-2 2-8-6-8-8-13-1-1-1-2 0-3h0c0 1 1 3 2 4v-1l-1-3h1z"></path><path d="M473 404c2 4 4 8 5 13h0c0-3 0-5-1-7h1c1 4 2 10 1 14h0l-1 1h0v-1h-2c1-7-1-13-4-19l1-1z" class="D"></path><path d="M100 598c1 0 1-1 2 0 2 1 3 2 3 4 0 1-1 2-2 3h-1c-2 0-2 0-3-1s-1-2-1-4c0-1 1-2 2-2z"></path><path d="M188 621h8c-1 3-3 6-5 10v-1h-1-1c0-2 0-3 1-4 0-1-1-4-2-5z" class="C"></path><path d="M190 626c0-1 0-1 2-1v2c0 1-1 2-1 3h-1-1c0-2 0-3 1-4z" class="H"></path><path d="M122 481l1 1c1-2 1-4 3-5-2 5-4 11-3 16-1 1-2 5-1 6l1 1h-1v2c-1-2-1-2-2-3 0-5 0-13 2-18z" class="C"></path><path d="M144 434l1-1c0-1 0-1 1-2v-2 2c0-1 1-2 1-2 0-1 0-1 1-1-1 2-1 4-1 6l-1 3-3 6-3 6-1-2c0-1 1-3 2-4 1-2 2-4 2-6 0-1 0-2 1-3z" class="F"></path><path d="M265 257v1l-1 3-4 11-4 12-2 1h0c3-10 5-19 11-28z"></path><defs><linearGradient id="C" x1="124.111" y1="600.325" x2="119.383" y2="592.161" xlink:href="#B"><stop offset="0" stop-color="#47464b"></stop><stop offset="1" stop-color="#5a5b5a"></stop></linearGradient></defs><path fill="url(#C)" d="M116 593c2 2 5 2 8 3 2 1 4 1 7 0h1l1 1c-2 1-3 1-5 2-5 1-11-2-15-4 1 0 1 0 1-1h0l2-1z"></path><path d="M239 566c2-1 5 2 7 1h0v4c0 1 0 2-1 3 0 2 1 3 2 5v2c-4-2-6-11-8-15z" class="L"></path><path d="M191 630v1h0v6 2l-4 4c2-1 4-3 5-5l1-1h1v1c0 3-5 6-7 8h0c-1-1 0-2-1-2l-2 1v-1c1-2 2-2 4-4 1 0 0 0 1-1 0-2 0-4-1-5 0-1 1-2 1-3h1v-1h1z"></path><defs><linearGradient id="D" x1="233.829" y1="389.506" x2="227.751" y2="378.87" xlink:href="#B"><stop offset="0" stop-color="#101312"></stop><stop offset="1" stop-color="#2f2b30"></stop></linearGradient></defs><path fill="url(#D)" d="M229 394v-12c0-1 0-4 1-5h2 3c-1 0-1 1-2 1 0 2-2 10-1 12 0 0 0 1-1 1v1l-2 2h0z"></path><path d="M306 207l2 3h2c1 4 3 9 5 13-2 0-2 0-2 1l-2-4c0 1 0 1-1 2l-1-3c-1-2-1-4-2-5l-2-6c1 0 1 0 1-1z" class="e"></path><path d="M310 216l1 4c0 1 0 1-1 2l-1-3h1v-3z" class="M"></path><path d="M306 207l2 3 2 6v3h-1c-1-2-1-4-2-5l-2-6c1 0 1 0 1-1z" class="B"></path><path d="M122 481c2-5 4-9 5-14 1-2 0-5 0-8 1-3 1-5 3-7h1c0 4-2 8-2 12v1c0 2-1 3-1 5 0 1-2 6-2 7-2 1-2 3-3 5l-1-1z" class="i"></path><path d="M102 577l2 2c1 1 2 3 3 4l4 3c3 2 6 4 9 5l-1 1h-3c-3-1-5-3-7-3-1-1-2-2-2-3-1-2-4-4-5-7v-2z" class="N"></path><path d="M181 621h7c1 1 2 4 2 5-1 1-1 2-1 4h1v1h-1-3c-2-1-1-2-1-3l-1-1-1-1v-1h1v-1c0-1 0-1-1-2-1 0-1 0-2-1zm197-50h1c3-1 5-3 8-5-1 2-4 6-6 6h-1v1h1c-1 2-2 2-3 3h-2-1c-1-1-2 0-4 0l-1-1h-1 0c0-1-1-1-2-2-1 0 0 1-1 0h-1s-1 0-1-1h0 0 1c1 1 2 1 3 1h1c2 0 4-1 6-1 1 0 2 0 3-1z" class="I"></path><path d="M297 400h1 1 3c1 1 0 4 1 6l-1 1v-1h-1c-1 0-1-2-2-2 0 0 0 1-1 1-1 1-3 2-5 2v-1h-1v-1c1-1 1-1 1-2 0 0 0-1 1-1h0c1-1 1-1 1-2h1v1 1l1-2z" class="g"></path><path d="M326 591h3v1c1 1 1 3 3 3v1h2l-1 1c0 2 0 3 2 4l-3 1v-3h-1c0 2 0 2-1 3l-2 1v-5c0-1 0-2-1-3s-2 0-3-1c1-1 1-2 2-3z" class="V"></path><path d="M504 351h1c1-1 1-1 2-1l2 2h0v1c3 3 5 6 5 10v2h-1 0c-2-5-6-6-7-12 0 1 0 1-1 1h0v-3h-1z" class="B"></path><path d="M518 490c-1 1-1 1-2 1h-1c-1 0-2 1-3 2-1 0-2 0-3-1v-1-1-3c1-1 2-2 3-2h1l1 1 1 1h1l2-1c0 1 1 2 1 3v1l-1-1v1z" class="E"></path><path d="M257 630c1 0 4 0 6 1h0c-2 2-3 6-3 9-1-1-2-1-3-1v1l-1 1v-1c-1-2-2-5-1-7h-1c1-1 1-1 1-2h1v-1h1z" class="p"></path><path d="M255 633l1-1h1v1c-1 1 0 2-1 4l1 2v1l-1 1v-1c-1-2-2-5-1-7z" class="F"></path><path d="M473 440l-1-1v-1l1-1c1-2 2-5 3-6s2-2 2-3c1-1 1-2 2-3 1 0 1 0 2 1-1 1-4 3-4 5 1 0 1 0 2-1l1 1c-2 3-4 6-5 9v1c-1 0-1 1-1 1v1 1c-1-1-1-2-2-4z" class="U"></path><path d="M473 440s1 0 1-1v-1-1h1v3h1v1c-1 0-1 1-1 1v1 1c-1-1-1-2-2-4z" class="n"></path><path d="M158 409c0 4 1 10-1 14-1 1-1 2-1 3s-1 3-1 3v3c-1 3-5 9-7 10h0c1-6 5-12 7-18v-1c0-2 1-3 1-5s0-4 1-5 1-3 1-4z" class="p"></path><path d="M503 520c2-2 6-4 9-4l2 1h0v1s-1 1-2 1-2 0-4 1c0 0-3 3-4 3-2 2-5 5-7 8v-1s-1 0-1-1c1-4 4-6 7-9z" class="V"></path><path d="M503 520l1 1c-3 2-5 4-6 6 2-1 4-3 6-4h0c-2 2-5 5-7 8v-1s-1 0-1-1c1-4 4-6 7-9z" class="I"></path><path d="M515 479c1 0 2-1 3 0h0c2 0 2-1 3 0s1 1 1 2l2 1-1 2-3 3v-1h-2l-2 1h-1l-1-1-1-1c1-1 2-3 2-5v-1z" class="K"></path><path d="M520 486h-2l1-3h1v1h1v-1h1l1 1-3 3v-1z" class="k"></path><path d="M515 479c1 0 2-1 3 0h0l1 1h-2v2h1c-1 1-2 2-2 3v2h-1l-1-1-1-1c1-1 2-3 2-5v-1z" class="f"></path><path d="M258 389v-1c2 2 1 6 2 8l1-8c0 5 2 10 2 15 0 0 0 1 1 1v1h-1v3h1l-1 1h0l-1-2c-1 0-1 0-2-1h-1v-1h-1c1-1 1-1 2-1l-1-2c0-1-1-2-1-3 0 0 1 0 1-1s-1-2-1-3v-6z" class="D"></path><path d="M495 304c1 0 3-1 4-1v2 1l1-1h1c-2 2-3 3-4 6-1 1-2 0-3 2 1 1 1 2 2 3h0l-3-3-1 1-1-1c-2 0-3-1-4 0v-1h1 2c1-4 3-6 5-8z" class="I"></path><path d="M305 448c0-1 0-1 1-2l-1 5h1c0-1 1-1 1-2h0v5c0 1-1 2-1 3 1 0 1 1 2 1h0l-1 1-1-1-2 1h0c-1 1-1 2-2 2v-1h-1c-1 1-1 1-1 3-1-2-1-2-2-3h1v-2-1c0-2 0-3 1-4h0l1-1h1c0 1 1 1 1 2h0c1-2 2-4 2-6z" class="f"></path><path d="M305 451h1c0-1 1-1 1-2h0v5c0 1-1 2-1 3 1 0 1 1 2 1h0l-1 1-1-1-2 1c0-1 0-1 1-2 0-2-1-4 0-6z" class="C"></path><path d="M488 473h1 0c1-3 4-7 7-8h2c1 0 1 1 2 1v1l-1 1c-1-1-1-2-3-2-3 2-7 8-7 11-1 3-1 9 0 11 0 2 0 3-1 4v2l-1-1h0v-1c1-2 0-6 0-8-1-2-1-4-1-6-1-1-1-3-1-4h1l-1-1c1-1 1-1 1-2l-1-1v-3h1c1 0 1 1 1 2v1l-1 1h0v3c1 0 0 2 1 3 0-1 1-3 1-4z" class="O"></path><path d="M496 496c2-2 4-5 6-5 1-1 2-1 3-1v1c-3 2-4 5-5 8-2 3-6 7-10 9v-1c0-2 1-3 2-5s3-3 5-5l-1-1z" class="N"></path><path d="M492 502h2l1-1 1-1h0 1c-2 3-4 6-7 7 0-2 1-3 2-5z" class="B"></path><path d="M497 497l2-1h0c0 1-1 3-2 4h-1 0l-1 1-1 1h-2c1-2 3-3 5-5z" class="L"></path><path d="M520 588l1 1h0c1-1 2-1 4-1h-1c-5 3-10 3-17 3 0 1 0 1 1 1 2 0 4 2 6 3h-2-2l-8-4h-1c-1 0-3-1-4-2s-2-1-3-1c3 0 6 0 9 1 6 1 11 0 17-1z" class="o"></path><path d="M338 590l3 4 2 1-2 1v1c2 0 2-1 3-1 1 1 0 1 1 2-2 1-5 1-7 2-1 0-2 0-3 1-2-1-2-2-2-4l1-1h-2v-1c3-1 3-1 5-4l1-1z" class="Z"></path><path d="M335 596l1 1h-1c0 1 0 1 1 2h0l2-1v2h0c-1 0-2 0-3 1-2-1-2-2-2-4l1-1h1z" class="M"></path><path d="M338 590l3 4h-1l-1-1h-1c0 1 0 2-1 2-1 1-1 1-2 1h0-1-2v-1c3-1 3-1 5-4l1-1z" class="c"></path><defs><linearGradient id="E" x1="87.89" y1="465.538" x2="93.481" y2="471.524" xlink:href="#B"><stop offset="0" stop-color="#8e908c"></stop><stop offset="1" stop-color="#a8a6ab"></stop></linearGradient></defs><path fill="url(#E)" d="M89 485v-3-10c0-4 2-11 5-14v1h0c1 0 1 0 2 1l-2 4h1c0 1-2 5-2 5h-1l-1 3c-1 3 0 7 0 10 0-1 0-1-1-1v5l-1-1z"></path><path d="M94 459c1 0 1 0 2 1l-2 4-1-1 1-4z" class="Y"></path><path d="M135 486h1 1c-2 4-3 7-4 11-1 2 0 4 0 7 0 6 3 8 7 13v4l3 6-1-1c-1-1-2-3-2-4 0-3-5-8-7-11-1-1-1-2-2-3v-10-1h0c1-4 3-7 4-10v-1z"></path><path d="M500 427h0 2c5-2 7 0 10 3-2 0-4 0-6 1h-1 0v-1h-1 0l-1 2c-3-1-5-1-7-1l-1-1c-2 1-2 1-4 1 1-1 3-2 4-3l2-1h3z" class="F"></path><path d="M495 430c2 0 3-1 4-2h1c1 1 0 1 0 2h4l-1 2c-3-1-5-1-7-1l-1-1z" class="i"></path><path d="M131 384c3 0 6 1 8 2 1 1 4 3 4 3l2 2h0l-2 1h-1v1h1c0 1 1 2 1 3 0 0-1 0-2 1h0c-2 0-3 0-5-1-1-1-1-1-1-2 0 0 0-1 1-2l1-1v-1h1v-1h0v-1c-1 0-3-2-4-2-1-1-3 0-5-1 1 0 1 0 1-1z" class="W"></path><path d="M292 390l-1-1c0-1 1-4 1-5 1-1 1-2 1-4 1 0 1 1 2 1l4 1h3c-1 2-2 3-3 4s-1 4-2 5h-1 0-1c-1 0-2-1-3-1z" class="F"></path><path d="M292 390l-1-1c0-1 1-4 1-5 1-1 1-2 1-4 1 0 1 1 2 1v1l1 1c0 2 1 2 0 4v1c-1 0-1 1-2 2h0v-3c-1 1-1 2-2 3z" class="G"></path><path d="M103 497c-1-2-2-4-2-7-1-4 4-10 7-13 3-4 7-4 12-5 0 1-1 2-2 2-2 2-4 3-6 6h-1l1-1c1-2 3-3 4-5-1 0-2 1-3 2l-4 6c-1 0-1 0-1 1s-1 2-2 2l-1 3c0-1 0-2-1-3h0v-1c-2 2-2 4-2 6v3c0 1 1 2 2 3l-1 1z" class="D"></path><path d="M104 485c0-1 0-1 1-2 1-3 5-7 8-8-2 2-4 3-4 7h0c-1 0-1 0-1 1s-1 2-2 2l-1 3c0-1 0-2-1-3z" class="m"></path><path d="M325 420c1-4 3-9 3-13-2 3-5 7-5 11v1h-1 0v-5c-1 2-2 3-4 5s-4 3-5 5l-1-1c1-2 3-3 4-5 2-2 3-5 4-7h0 1 0c1 0 1 0 2 1 0-2 1-4 1-5h1v2c1-1 2-2 3-2 1-1 2-3 3-5 0 3-1 4-2 6l-3 12h-1 0z" class="g"></path><path d="M218 626c1-1 1-2 2-3h0c1 0 1 0 1 1h0l3 1h0c1 0 2 0 2 1h2 0 2c1 1 2 0 3 1l1-1h-1c-1 0-1 0-2-1h1 0l7 1c1 0 2 0 3 1 1 0 2 0 3 1-3 0-6 1-9 2-6 0-12-2-18-4z" class="W"></path><path d="M139 389h0c-4-3-9-3-13 0s-7 6-11 7c-3 0-6-1-8-3-2-1-4-3-4-6 0-2 1-3 2-5h1c-1 1-1 1-1 2-1 1-1 2-1 3v1c1 2 3 4 5 5h0c-1-1-2-3-3-4 0-2 0-4 1-6l1-1-1-1h0 3c1 1 1 1 0 2l-2 1v2c-1 3 1 5 2 7 1 1 3 1 4 1 2 0 5-1 6-2 3-3 7-5 10-7 2 1 4 0 5 1 1 0 3 2 4 2v1h0z"></path><path d="M376 236c1 1 3 2 4 3 2 3 3 6 5 10 0 1 0 3 1 4s0 3 0 5c-1-1-1-2-1-3-1-2-1-3-2-4l-1-1v1l-1 1h-1c0-1 0 0-1-1h0v-1-2c0-2-4-6-5-8 1 0 2 1 4 2h0c-2-3-2-3-2-6z" class="o"></path><path d="M381 252c0-1 0-2-1-2v-2h1c1 1 2 2 2 3l-1-1v1l-1 1z" class="I"></path><path d="M376 236c1 1 3 2 4 3-1 2-1 2-1 3l1 1-1 1c0-1-1-2-1-2-2-3-2-3-2-6z" class="O"></path><path d="M154 367v-1-5c1-1 1-1 1-2 2 0 2 0 3 1h0c-1 1-3 2-4 4 1 1 1 1 1 2 1 0 1 0 1-1 2 0 2 0 3 1-1 2-2 3-3 4-3 4-3 9-3 14 1 2 2 5 3 8 0 1 0 2-1 3v1 2c-1 1-1 2-1 4h0l-1-1v-1c0-1 0-2 1-3v-1l-1-1c0-2 0-3 1-4 0-2-1-4-1-6-1-2-1-5-1-7v3c-1-2-1-4 0-5 0-1 0-1-1-2 1-2 2-5 3-6v-1z" class="S"></path><path d="M153 395c0-2 0-3 1-4 0 1 1 3 0 5l-1-1zm1-28c1 0 1 1 1 2l-3 9v3c-1-2-1-4 0-5 0-1 0-1-1-2 1-2 2-5 3-6v-1z" class="B"></path><path d="M117 285v6c2-1 3-2 4-3h1c2-1 4 0 5 0 1 1 3 2 3 3v2c-1 1-2 1-3 1h0c1-1 1-1 1-2s0-1-1-2c-3 0-5 1-7 2l-2 2c1 2 2 7 5 8 2 2 6 0 8 3v1h0c-1 0-1 0-1-1-1 0-1-1-2-1h-2c-1-1-3-1-3-1h0c-3-2-5-5-6-9-2 2-3 4-3 7 0 1 1 3 0 4h0l-1-1c0-4 1-8 3-10v-6c0-2 0-2 1-3z"></path><path d="M140 442l1 1c-1 1-2 3-2 4l1 2-1 2v1l-3 6v1 1h0c-2 4-5 7-8 10 0-2 1-3 1-5h0c3-4 4-6 5-10s2-9 5-13v1l1-1z" class="D"></path><path d="M140 442l1 1c-1 1-2 3-2 4l1 2-1 2-5 8c1-6 2-11 5-16l1-1z" class="G"></path><path d="M130 510v-2h1c1 1 1 2 2 3 2 3 7 8 7 11 0 1 1 3 2 4l1 1-3-6v-4h0l1 1v-1l1-4v5h1c-1 2 0 3 1 5s1 4 2 7c0 0-1 0-1 1l2 4-2-1c0-1 0-1-1-1l-1-1h1 0v-1c-1-2-4-5-5-7-1-1-1-3-2-4s-2-2-2-3c-2-2-3-5-5-7z" class="M"></path><path d="M142 513v5h1c-1 2 0 3 1 5s1 4 2 7c0 0-1 0-1 1l-1-1c-1-1-1-3-1-4 0-3-2-5-2-8v-1l1-4z" class="O"></path><path d="M242 318c0-2 2-4 2-6l3-9c1-5 3-9 5-14h1 1l-5 16c-1 2-2 4-1 7h1v2l-1 1c0 1 0 1-1 2v-1h-1v1c-1 0-1 1-2 1l-1 1v-1h-1z" class="I"></path><path d="M248 312h1v2l-1 1c0 1 0 1-1 2v-1h-1v1c-1 0-1 1-2 1 1-2 2-5 4-6z" class="h"></path><path d="M149 473c3 2 5 2 7 5h1c-1-1-1-2-2-3v-1l2 2 1 16h0c-1-1-1-2-1-4v1l-2 2c-1 1-2 1-3 1h-1l-1-2h0c0-1-1-2-2-3l-1 1v-1c2-2 3 1 6 1 1-1 2-1 2-3s-1-4-3-5c0-1-1-1-2-2h0 2 0 1l-2-2c-1-1-2-1-3-2l1-1z" class="f"></path><path d="M150 490l1-1h1c0 1 0 1 1 2-1 0-1 1-1 1h-1l-1-2z" class="I"></path><path d="M151 428c1 0 2-1 2-2l1-2h1c-2 6-6 12-7 18l-12 17v-1l3-6v-1l1-2 3-6 3-6 1-3 1-3 1 1 1 1c0-2 1-3 1-5z" class="F"></path><path d="M148 431l1 1 1 1c-1 1-1 3-2 4l-1 1-1-1 1-3 1-3z" class="f"></path><path d="M140 449l3-6 3-6 1 1 1-1c-2 6-6 10-9 15v-1l1-2z" class="C"></path><path d="M276 499l53 4h-4-7-3v1h0l-9-1h-7c-5 0-10-1-15-1l-3-1c-1-1-2-1-3-2h-2z" class="j"></path><path d="M299 503c1-1 1-1 2-1h11c-1 0-5 0-6 1h0-7z" class="Y"></path><path d="M312 502c2 0 4 0 6 1h-3v1h0l-9-1h0c1-1 5-1 6-1z" class="M"></path><path d="M317 595l4 2h1v-1c2-1 3 0 4 0l1-1c1 1 1 2 1 3v5h-3l-2 1h0c-2 0-4 0-5-1 0-1 0-1-1-2v1c-2-1-3-2-3-4l2-3h1z" class="P"></path><path d="M318 603h1c1-1 1-2 2-4l1 1c1 0 1 0 2 2l-1 1v1h0c-2 0-4 0-5-1z" class="W"></path><path d="M316 595l3 2v1h-3v1h0c1 1 1 0 2 1l-1 1v1c-2-1-3-2-3-4l2-3z" class="R"></path><path d="M322 600l3-3c1 0 2 1 3 1v5h-3l-2 1v-1l1-1c-1-2-1-2-2-2z" class="G"></path><path d="M109 589c2 0 4 2 7 3h3l1-1c3 1 6 1 9 1l6-1c2-1 3-2 5-2-1 2-2 4-4 5l-3 3-1-1h-1c-3 1-5 1-7 0-3-1-6-1-8-3l-6-3-1-1z" class="a"></path><path d="M124 596c2 0 4-1 7 0h0c1-2 2-2 4-2h0 1l-3 3-1-1h-1c-3 1-5 1-7 0z" class="S"></path><path d="M135 591v1c-2 1-4 1-7 2h-5l1-2h4 1l6-1z" class="E"></path><path d="M120 591c3 1 6 1 9 1h-1-4l-1 2s-2 0-3-1c-2 0-3-1-4-1h3l1-1z" class="D"></path><path d="M239 639c-5 1-10 1-15 0-12-3-21-8-27-18h6c1 0 3 2 4 2v1c-1-1-3-2-5-3 2 2 4 5 6 6v1c1 1 4 3 5 3 0 2 0 2 2 3 1 1 2 1 4 2 3 1 6 2 9 2 3 1 7 0 11 1h0z" class="f"></path><path d="M260 272v2h1l-8 23c1-1 1 0 1-1s1-2 1-3c1 0 1 1 2 2-1 2-2 5-3 7v3c-1 0-1 0-1 1h0c0 1-1 2-1 3-1 1-1 2-3 3h0-1c-1-3 0-5 1-7l5-16h-1-1l2-4h0l2-1 4-12z" class="S"></path><path d="M254 285h0l2-1c0 2-1 3-2 5h-1-1l2-4z" class="i"></path><path d="M249 305v1l2-1c1 1-1 3-1 4h1c0-1 0-2 1-3v-1c1-1 1-2 2-3h0v3c-1 0-1 0-1 1h0c0 1-1 2-1 3-1 1-1 2-3 3h0-1c-1-3 0-5 1-7zm14 326l9 1c1 0 2 0 3 1h0c0 2-1 3-2 4s-3 2-4 2c1 2 2 4 3 4 2 1 6 2 8 1h0 1 0v1c-2 1-5 2-7 2-3-1-5-2-6-4-3-3-5-7-5-11h0v-1h0z" class="V"></path><path d="M272 632c1 0 2 0 3 1h0l-1 1c-2 2-3 3-5 4l3-6z" class="B"></path><path d="M261 274c0-1 0-2 1-3l1 1c-1 1-2 3-2 5l3-3h0 1l-1 2h1 0l1 1h1l2-2v1c-1 1-2 2-2 3s-1 1-1 2v1 1l-1-1c-2 2-4 6-5 8s-2 3-3 5c-1-1-1-2-2-2 0 1-1 2-1 3s0 0-1 1l8-23z" class="I"></path><path d="M259 289l1 1c-1 2-2 3-3 5-1-1-1-2-2-2 1-1 0-1 1-1l1-1c0-1 0-1 1-1 0-1 1-1 1-1z" class="V"></path><path d="M262 279h1c0 1 1 1 1 2h1 0c0-1 0-2 1-3l1 1c0 1-1 1-1 2v1 1l-1-1c-2 2-4 6-5 8l-1-1c1-2 2-4 2-6v-1c1-1 1-2 1-3z" class="S"></path><path d="M261 277l3-3h0 1l-1 2h1 0l1 1h1l2-2v1c-1 1-2 2-2 3l-1-1c-1 1-1 2-1 3h0-1c0-1-1-1-1-2h-1 0c-1 1-2 2-3 4 1-2 1-4 2-6h0z" class="T"></path><path d="M261 277l3-3h0 1l-1 2h1 0l1 1c-1 0-2 1-3 2 0-1-1-1-1-1h-1v-1h0z" class="P"></path><path d="M188 640h0c-1 0-2 1-3 1h0c0-1 1-1 2-2h0v-1c-1 0-1-1-2-1-2 0-4-1-6-2-1-1-2-2-2-4s3-5 4-6v-2c-1-1-1-1-2-1-1 1-1 1-2 1h-1v-1c1 0 2-1 2-1h3c1 1 1 1 2 1 1 1 1 1 1 2v1h-1v1l1 1 1 1c0 1-1 2 1 3h3c0 1-1 2-1 3 1 1 1 3 1 5-1 1 0 1-1 1z" class="b"></path><path d="M184 627l1 1c0 1-1 2 1 3h3c0 1-1 2-1 3 1 1 1 3 1 5l-1-1v-2c-1-1-1-3-2-4h-2l-1-1c-1-1-2-2-2-3 1-1 1-1 3-1z" class="W"></path><path d="M318 398c-1 1-1 3-2 3 0 1-1 1-1 2-1 0-1-1-2-1 0 1 0 1-1 2-1-1-1-1-1-2h0l-1 1c-2 1-1 0-3-1 0 4 1 6 3 9h-1c-1-1-2-3-3-6h0v-1c0-2 0-7 1-9l1 1-1 4h1v-1c1-1 1 0 2 0 1-2 2-3 2-5v-1h2c2 0 3-2 4-4l1 1c2 2 1 6 1 9v1l-1 2h0-1v-3-1z" class="L"></path><path d="M318 398v-2c0-1 0-4 1-4v6 1l1 1-1 2h0-1v-3-1z" class="F"></path><path d="M371 208c0 1-1 1-1 2 1-1 2-2 4-2 0 1-1 2-2 2-1 1-2 2-2 3l-2 1h-1c0 1 0 2-1 3h-1c0 1-2 2-2 3h-2-1l2 1c-1 1-1 1-2 1h-2c0 1 0 1-1 1h-1l-1-1c-1 0-2 1-3 1h-2v1c1 1 3 1 4 2h0c0 1 1 1 1 2-1 0-2 0-3-1l-1 1-4-2c-1-1-3-1-4-1h-1c1-1 1-1 2-1l-1-2 4 1c7 0 19-11 24-15z" class="R"></path><path d="M367 214h-1 0l3-3c1-1 2-1 3-1-1 1-2 2-2 3l-2 1h-1z" class="c"></path><path d="M343 222l4 1 2 1-2 2c-1-1-3-1-4-1h-1c1-1 1-1 2-1l-1-2z" class="N"></path><path d="M358 222h-3c2-1 3-2 5-3l1-2c2 1 2 0 3 0h1c0 1-2 2-2 3h-2-1l2 1c-1 1-1 1-2 1h-2z" class="U"></path><path d="M295 207h0c3-2 5-4 8-5 2 1 3 3 3 4v1c0 1 0 1-1 1l2 6h-1 0v-1c-1-1-2-2-3-2-2-1-4 0-6 1 0 0-1-1-1 0-1 0-1 2-3 1l-6 10c0-1 0-1-1-1 0 0-1 0-2 1 0-1 1-1 1-2s0 0 1-1c0 0 0-1 1-2v-1c1 0 1-1 1-1 1-1 1-2 2-3l3-4c0-1 1-1 2-2z" class="Q"></path><path d="M293 213c2-3 5-6 7-8 1-1 2-1 3-1 1 1 2 3 2 4l2 6h-1 0v-1c-1-1-2-2-3-2-2-1-4 0-6 1 0 0-1-1-1 0-1 0-1 2-3 1z"></path><path d="M321 425c1 1 1 2 1 3l1 1c-1 1-1 1 0 2l-1 1 1 2 2-2h1v1c-1 1-1 1-1 2v1h0v1l-2 2h-1c-1 1-1 3-2 4v1h-2-1-1v2l-1-1h0l-1-1-1 2-1-1v-3c1 0 1-1 1-2v-1l1-2 2-4c0-1 0-1 1-2 0 1 0 2-1 3v2c2-3 4-6 5-11z" class="d"></path><path d="M322 432l1 2 2-2h1v1c-1 1-1 1-1 2v1h0v1l-2 2h-1l1-3h-1s-1 1-1 2h-1c1-2 1-4 2-6z" class="Q"></path><path d="M143 581v1l1-1h1c-1 2-2 3-3 3l-1 3-1 2c-2 0-3 1-5 2l-6 1c-3 0-6 0-9-1s-6-3-9-5v-1c-1 0-1 0 0-1 0 0 1 0 2 1h0 0 2c3 1 5 2 8 2 0 1 1 1 2 1 8 0 12-1 18-7z" class="G"></path><path d="M143 581v1l1-1h1c-1 2-2 3-3 3-4 4-9 5-14 6h-4c-3 0-9-3-11-5h2c3 1 5 2 8 2 0 1 1 1 2 1 8 0 12-1 18-7z" class="L"></path><path d="M497 531h1 0c2-2 13-7 16-7l1 1h0c-1 2-4 2-5 3-2 3-3 7-6 8l-7 4h-1c0-1-1-1-1-2h-1l-1 1c-1-1-1-1-2-1 1-1 2-3 3-4 0 1 0 1 1 1h1 0c0-2 0-3 1-5v1z" class="c"></path><path d="M496 535h0c2 0 4-2 5-2 0 1-1 1-2 2l1 1c1-1 1-3 2-3v-1-1h2 1l-2 2 1 1c0-1 1-2 2-3h0l1-1h0c0 2-2 4-4 5l1 1-7 4h-1c0-1-1-1-1-2h-1l-1 1c-1-1-1-1-2-1 1-1 2-3 3-4 0 1 0 1 1 1h1 0z" class="H"></path><path d="M491 538c1-1 2-3 3-4 0 1 0 1 1 1h1c0 1 1 2 1 3 1 0 2 0 3-1l3-2 1 1-7 4h-1c0-1-1-1-1-2h-1l-1 1c-1-1-1-1-2-1z" class="b"></path><path d="M291 596c1 0 2-1 3-1h4l-1 2v1 1c-2 1-4 2-5 3h1c0 1 0 1 1 1-2 1-5 0-6 0h-3c-1 0 0-1-1-2-1 1-2 1-3 1h0-1c-1 0-1 0-2-1h1-1-2l-5-2v-1c3 0 6-1 9-2 1 1 1 1 2 1h0c1 0 1 0 1-1h2 2 0 4z" class="Q"></path><path d="M288 598c1 1 2 0 3 0l-1 3c-2 0-4 1-5 2h3-3c-1 0 0-1-1-2-1 1-2 1-3 1 0-1 1-1 1-1l1-1c1 0 2 0 3-1 1 0 1-1 2-1z" class="G"></path><path d="M291 596c1 0 2-1 3-1h4l-1 2c-3 1-5 3-7 4l1-3c-1 0-2 1-3 0h0l3-2z" class="M"></path><path d="M297 597v1 1c-2 1-4 2-5 3h1c0 1 0 1 1 1-2 1-5 0-6 0h-3c1-1 3-2 5-2 2-1 4-3 7-4z" class="R"></path><path d="M280 596c1 1 1 1 2 1h0c1 0 1 0 1-1h2 2 0c-3 3-8 4-11 5l-5-2v-1c3 0 6-1 9-2z" class="D"></path><path d="M315 223h11c1 0 3 0 4-1h0c2 1 5 0 7 0h6l1 2c-1 0-1 0-2 1l-2 1-4-2h-1l-1 1h0c-1 0-1 0-2 1h0c-2-1-3-1-4-1v2h-1c-1-1-1-1-2-1s-1 1-1 1l1 3h0l-1 1-2-2-1 1-3-2h-1v1 2s1 0 1 1c-1 0-1 0-2-1s-2-3-2-4l-1-3c0-1 0-1 2-1z" class="i"></path><path d="M317 228h0v-1c2 0 4 0 5-1h0c0 1 0 2-1 2 0 1 1 1 1 1l-1 1-3-2h-1z" class="C"></path><path d="M322 226h1c1-1 1-1 2 0-1 0-1 1-1 1l1 3h0l-1 1-2-2s-1 0-1-1c1 0 1-1 1-2h0z" class="D"></path><path d="M313 591l1 1c1 0 1 1 2 1l1 2h-1l-2 3c0 2 1 3 3 4l-1 1c-2 0-3-2-5-3 0 1-1 2-2 3s-1 1-2 0v-3h0c-3 1-4 1-6 3h0c-1-1 0-2 0-2l1-1c-3 1-5 2-6 4-1-1-1-1-2-1s-1 0-1-1h-1c1-1 3-2 5-3s4-3 7-4c1 0 2 0 3 1h1 0c2 0 4-3 5-5z" class="E"></path><path d="M297 599c2-1 4-3 7-4 1 0 2 0 3 1-4 2-9 3-14 6h-1c1-1 3-2 5-3z" class="j"></path><path d="M314 592c1 0 1 1 2 1l1 2h-1l-2 3c0 2 1 3 3 4l-1 1s-1-1-2-1l-4-5 4-5h0z" class="H"></path><path d="M473 325c2-3 4-5 6-7 1-1 2-1 3-2v-2c1 1 1 1 3 0h1 0l-1-1 1-1s1-1 2-1v1h-1v1c1-1 2 0 4 0l1 1 1-1 3 3h0l1 1c0 1-1 2-2 3-1 0-1-1-2 0s-1 1 0 2v1h-1c-1 0-3 1-4 0v-1-2c-1-1-2-1-4-1h-1v-1c-2 1-2 1-3 2l1 1c1 0 1-1 2-2v1l-2 3c-1 1-4 2-5 3-1-1-2-1-3-1z"></path><path d="M492 314l1-1 3 3c0 1-1 1-1 1l-1 1c-1-2-2-3-2-4z" class="m"></path><path d="M309 450v-1c1-2 1-3 1-5h-1l-1 1v-1-2h0l1-4c0-1 1-1 1-2 1-2 2-3 2-4v-1h0l1 1c0 1-1 2-1 3-1 0-1 1-1 2 1-1 1-2 2-3l1 1c-1 0-1 0-1 1v1 1 1 1c0 1 0 2-1 2v3l1 1 1-2 1 1h0l1 1v-2h1c0 1 0 2-1 3v1c0 1-1 2-2 3v-1 1h1c1 1 1 3 0 5h0l-1 1c0 1 0 1 1 2l-2 2s-1 1-2 1c0-1 0-1-1-2 1-1 1-2 1-4h-1l-1 2h-1 0c-1 0-1-1-2-1 0-1 1-2 1-3l2-4z" class="L"></path><path d="M312 451l1-3h1v2 1h1c1 1 1 3 0 5h0l-1 1c0 1 0 1 1 2l-2 2s-1 1-2 1c0-1 0-1-1-2 1-1 1-2 1-4h-1l-1 2h-1 0c-1 0-1-1-2-1 0-1 1-2 1-3l2-4v1 2c1-1 1-1 1-2l2-3v3z" class="E"></path><path d="M313 454h1l1 2-1 1c0 1 0 1 1 2l-2 2h0v-1c0-1-1-3-1-4 1-1 1 0 1-2z" class="S"></path><path d="M312 451l1-3h1v2 1h1c1 1 1 3 0 5h0l-1-2h-1c0 1 0 1-1 1v-4z" class="N"></path><path d="M309 450v1 2c1-1 1-1 1-2 1 1 1 3 1 4l-1 1-1-1c-1 1-1 1-1 3-1 0-1-1-2-1 0-1 1-2 1-3l2-4z" class="I"></path><path d="M491 431c2 0 2 0 4-1l1 1-5 2c3 0 8 1 9 3 1 1 1 3 1 4s-1 3-2 3c-1 1-3 1-4 1l-1-1h1l2-2c1-1 1-2 1-3-1-1-2-2-4-2s-5 0-6 2c-3 1-4 4-6 5-2 2-4 2-6 4 0 0 1 1 0 2 0-2-1-3-1-5v-1c2-4 5-7 9-9 2 0 4 0 6-1 0 0 1-1 1-2z" class="E"></path><path d="M156 390l1-1c0-2 1-4 1-6v26c0 1 0 3-1 4s-1 3-1 5-1 3-1 5v1h-1l-1 2c0 1-1 2-2 2 0-1 1-3 1-4 1-5 1-10 1-15 0-3 0-5-1-8-1-1-1-1 0-2h0l-1-1v-4c1 0 1 1 2 1l1 1v1c-1 1-1 2-1 3v1l1 1h0c0-2 0-3 1-4v-2-1c1-1 1-2 1-3v-2z" class="j"></path><path d="M156 390l1-1c0-2 1-4 1-6v26c0 1 0 3-1 4v-5c-2 4-1 9-4 14h0l3-20c1-3 1-6 1-9l-1-3z"></path><defs><linearGradient id="F" x1="416.703" y1="645.42" x2="412.522" y2="636.439" xlink:href="#B"><stop offset="0" stop-color="#29292c"></stop><stop offset="1" stop-color="#484746"></stop></linearGradient></defs><path fill="url(#F)" d="M396 640c-3-1-4-3-5-5 2 0 5 1 7 2 2 0 3 1 4 1h0c2 0 3 1 4 1 5 2 9 4 14 2h2l1 1c1-1 1-1 2 0h1l-5 3v1c-2 1-3 1-5 1 0 0-1 1-2 1l-2-1h0-1-1-1l-2-2h0l-10-5h-1z"></path><path d="M397 640v-1l-1-1c0-1 2-1 3 0 3 1 6 3 9 4l9 3h2l-1 1h0c-1 0-1 1-2 1 0 0-1 1-2 1l-2-1h0-1-1-1l-2-2h0l-10-5z" class="G"></path><path d="M407 645c4 0 6 1 10 0h2l-1 1h0c-1 0-1 1-2 1 0 0-1 1-2 1l-2-1h0-1-1-1l-2-2h0zM155 429l1 1v1c1 0 1-1 1-1l1 1h0v5c1 3 0 7 0 10 1 1 0 3 0 4h0v-5c-6 1-10 4-15 8h0v-2c-2 3-4 6-7 9h0v-1l12-17h0c2-1 6-7 7-10v-3z" class="b"></path><path d="M156 431c1 0 1-1 1-1l1 1c-1 1-1 3-1 4s-1 1-1 2c-3 3-6 7-9 10h-1c3-5 8-9 10-15v-1z" class="Y"></path><path d="M158 436c1 3 0 7 0 10 1 1 0 3 0 4h0v-5c-6 1-10 4-15 8h0v-2h1c2-2 4-5 7-6 1 0 1-1 2-1 2-1 3-1 5-3v-4c-1 1-1 2-1 3-1 2-3 3-5 4 1-2 3-3 4-5 1-1 1-2 2-3z" class="B"></path><path d="M79 494c-3-3-4-8-6-12l3 3 1-1h0 1v1c5 1 9 5 11 10 2 3 3 6 6 9-6-2-12-4-17-8l1-2z"></path><path d="M77 484h1v1c1 0 2 1 3 2 3 2 4 6 5 9-3-3-6-8-9-12h0z" class="G"></path><path d="M79 494c-3-3-4-8-6-12l3 3 9 12c1 0 2 2 3 2 0 1 2 0 3 2l-4-2c-3 0-6-2-8-5z" class="Q"></path><path d="M316 332h3c1 0 2 2 3 0h1 0c2 1 4 1 5 1h1c1 0 1 1 2 1 1 1 1 1 1 2-2 0-4 0-6 1h0c-1 0-3 2-3 3h0c-1 1-2 1-3 1h-4c-1 1-2 1-4 1h-3v-1s1-1 1-2l1-1c-2 1-2 3-3 4h-2v-1h0v-1c1-2 2-3 4-4l-1-1h0c1-2 5-3 7-3z" class="K"></path><path d="M316 341h-1 0c1-2 3-3 5-5 1 0 2-1 2-2h7l-3 2c-1 0-3 2-4 3l-1 1-1 1h-4z" class="J"></path><path d="M309 335h0c1-2 5-3 7-3h1c-1 4-4 5-5 8l-1 1c1-1 3-4 6-5v-1c1 0 2-1 2-1l1 1c-1 0-1 0-2 1s-4 3-5 5l-1 1h-3v-1s1-1 1-2l1-1c-2 1-2 3-3 4h-2v-1h0v-1c1-2 2-3 4-4l-1-1z" class="h"></path><path d="M121 269c3-3 7-8 10-11 0-1-2-1-2-2v-1h0c1 0 2 1 3 1v-1c0-1-1-1-1-2s0-1 1-2l1 1c2 1 2 2 2 3v1c1-1 1-2 2-2 1-1 1-1 2-1l-1 2h1l2-2v-1c1-1 2-1 3-2 1 1 1 2 0 2l1 1c0 1 0 1-1 1l-2 3v1l2 1c0 1-1 1-2 2h-2c-1-1-1-2-2-2h0c0 1 0 2 1 2 1 1 1 2 2 3 0 1 0 1 1 2h-1l-1-1v1 1h-1v-1l-1 1c0 1 0 1-1 1-2 0-2 0-3-1 0-1 1-3 1-3-1-1-2-3-3-4h0v1l-1 1-1-1c-1 1-2 2-3 4l-4 4-1 1-1-1z"></path><path d="M380 630c-1 0-2 1-2 1-3 2-6 6-9 8 2-5 6-9 10-12 3-2 6-3 9-4s6-2 8-4c0 2 0 3 1 4h0 0c0 1 0 1 1 2v1 1c1 2 2 3 2 4l1 1h0v1c2 2 3 4 5 6-1 0-2-1-4-1l-5-6c-1 0-3-1-4-2-2 0-3-1-4-2-2 0-5 1-7 1-1 1-1 1-2 1z" class="i"></path><path d="M393 625l1 1s-1 0-1 1h0c-4 0-7 0-11 2h0c-1 1-1 1-2 1 4-5 8-4 13-5h0z" class="F"></path><path d="M393 625c-1-1-4 0-6 0l8-4h0c0 3 0 6 1 8v1l-1-1c0-1-1-1-2-2h0c0-1 1-1 1-1l-1-1z" class="H"></path><path d="M382 629h0c4-2 7-2 11-2 1 1 2 1 2 2l1 1v-1l1 1c0 1-1 1 0 2-1 0-3-1-4-2-2 0-3-1-4-2-2 0-5 1-7 1z" class="p"></path><path d="M397 623h0 0c0 1 0 1 1 2v1 1c1 2 2 3 2 4l1 1h0v1c2 2 3 4 5 6-1 0-2-1-4-1l-5-6c-1-1 0-1 0-2 0 1 0 1 1 1 1-1-1-3-1-4v-2c-1 0-1-1 0-2z" class="O"></path><path d="M69 509l-1 1c-3 1-5 3-7 5l2-3c1-3 4-4 7-6 2-1 5-3 7-4 8-2 18 3 24 6h0l1 1v1h-2c-1-1-5 0-7-1-1-1-2-1-3-1h-8-2c-2 0-8 0-9-1v1c1 1 2 0 4 0-1 1-5 1-6 1z"></path><path d="M78 503h1c2 0 5 1 6 2h0-2-10l5-2z" class="M"></path><path d="M71 507c6-2 13-1 19 1h-8-2c-2 0-8 0-9-1z" class="B"></path><path d="M147 487v1l1-1c1 1 2 2 2 3h0l1 2h1c1 0 2 0 3-1l2-2v-1c0 2 0 3 1 4h0v4c0 4-1 8 0 11v1l-4-4c-2-1-4-1-6-2l-1-1h0c-2-1-3-3-3-6-1-3 1-5 3-8z" class="Q"></path><path d="M147 487v1l-1 3c-1 2-1 5 0 7l3 3c2 0 5 1 7 0l1-1h-2l-1-1h2v-1h0-2 0c2-2 2-2 3-4v-2c-1 0-1 0-2 1h-5c-1 0-1-1-2-2 1 0 2 0 3 1h1c1 0 2 0 3-1l2-2v-1c0 2 0 3 1 4h0v4c0 4-1 8 0 11v1l-4-4c-2-1-4-1-6-2l-1-1h0c-2-1-3-3-3-6-1-3 1-5 3-8zm239 61h0 2v-1l-1-2h0c1 0 2 1 3 2l-1 1c0 1-1 1-2 1v1l7 2-3 5c-1 3-2 6-5 7-2 0-3 0-5-1 0-2-1-2-2-3l-3-2s0-1 1-1l-1-1h0l3-3 1 2 1-1h1l-3-3c0-1 1-2 1-3v-1c0-1 0-1 1 0s1 2 3 3h1v-1h0c0-1 1-1 1-1z"></path><path d="M377 557l-1-1h0l3-3 1 2 1 1c1-1 1-1 1-2 1 1 1 1 1 2v1l2 1v-1c1 0 2 0 2 1v1c1 0 3-2 3-2h1c-1 3-2 6-5 7-2 0-3 0-5-1 0-2-1-2-2-3l-3-2s0-1 1-1z" class="i"></path><path d="M377 557l-1-1h0l3-3 1 2 1 1c1 1 1 2 2 3v3c-2-2-4-4-6-5z" class="Q"></path><path d="M370 357l9-3c0 1 2 3 2 4l2 4c-1 1-1 1-1 2-1 1-1 3-2 4l-5-1-20-5 15-5z" class="c"></path><path d="M378 358l1 1c0 1 1 3 0 5h-1v-6z" class="V"></path><path d="M376 364c0 1 0 1 1 1h0c0-3 0-5-1-7v-1l1-1c0 1 1 1 1 2v6c0 1 0 1 1 2h1c1-3 1-5 1-8l2 4c-1 1-1 1-1 2-1 1-1 3-2 4l-5-1v-3h1z" class="C"></path><path d="M375 367l-20-5 15-5v7h1 1l-1-2v-3l1-1v4l1 1s0 1 1 1h0v-2-3h0c1 0 1 1 1 2h0c1 1 1 2 1 3h-1v3z" class="N"></path><path d="M122 354c-1-6-2-12-1-18 1-5 4-10 6-15 0-2 0-4-1-6s-3-4-5-6c3 1 5 3 6 6 1 2 2 5 2 7 0 1-1 2-2 3l-3 6c-1 2-1 4-1 6h0c1-3 2-6 3-8l1 2c-1 1-3 5-3 7 0 1 1 1 1 1v1h0c-1 8 1 15 4 22 0 2 2 5 2 6v1h0l-4-3c0-1-1-2-2-3l-2-2c-1-2-2-4-2-6h1v-1z" class="U"></path><path d="M122 354l3 8v1l-2-2c-1-2-2-4-2-6h1v-1z" class="Q"></path><path d="M123 350c1 0 0-2 0-3 0-3 1-5 2-7h0c-1 8 1 15 4 22 0 2 2 5 2 6v1h0l-4-3c0-1-1-2-2-3v-1l1-1-1-1-2-7v-2-1z" class="T"></path><path d="M125 362l1-1-1-1-2-7v-2-1c1 3 2 5 3 7 1 4 3 8 5 12h0l-4-3c0-1-1-2-2-3v-1z" class="F"></path><path d="M247 628h4c2 1 4 1 6 2h-1v1h-1c0 1 0 1-1 2l-1 2v-1c-4 2-9 4-14 5h0c-4-1-8 0-11-1-3 0-6-1-9-2-2-1-3-1-4-2-2-1-2-1-2-3 4 2 8 4 12 4 3 1 5 1 7 1l2-1 3-1 5-1c0-1-1-1-1-2l7-3h-1z" class="X"></path><path d="M242 633h0c-1 1-3 2-5 3h0-5l2-1 3-1 5-1z" class="U"></path><path d="M247 628h4c-1 1-2 3-4 3-1 1-3 2-5 2h0c0-1-1-1-1-2l7-3h-1z" class="l"></path><path d="M213 631c4 2 8 4 12 4 3 1 5 1 7 1h5v1c-4 0-9 0-12-1h-2c-1 0-1 0-2-1h-1l-1 1c-2-1-3-1-4-2-2-1-2-1-2-3z" class="D"></path><path d="M439 616h5l1 1c4 1 6 4 9 7 4 3 10 6 12 11 1 2 0 3-1 5 0 1-1 2-1 2h-1 0c-1-1 0-2 0-3h-1 0c-2-3-5-4-6-7l-1-3h-1v-1c1-1-1-2-1-3s-1-2-3-4c0 2 1 3 1 5-1 2-1 3-2 5-1 1-3 2-5 2-1 0-2-1-3-2v-3h0l1 1c1 1 2 1 3 1l2-2c1-1 1-3 0-5 0-2-1-3-3-4l-1-1-1-1c-1-1-3-1-4-1h1z" class="J"></path><path d="M455 629h1l5 5h1c1 1 1 2 1 3 0 0 0 1-1 2h0c-2-3-5-4-6-7l-1-3z" class="W"></path><path d="M463 639c0-2 1-4-1-6s-7-5-9-9c3 2 6 4 9 7 1 0 2 1 2 2 1 1 1 3 2 4-1 2-1 3-2 5h-1 0c-1-1 0-2 0-3z" class="m"></path><path d="M432 370h1l3 6c0 1 1 2 2 3s1 2 2 3c0 0 0 1 1 2 0 3 2 5 3 8s2 6 2 10h0c-3-2-3-4-4-8l-1 1v2h-1l-2-2h-1c0-3-1-5-2-7-1-3-1-5-3-8 0 3 1 8 3 10v1c1 1 2 2 1 3 0 0-1 0-1-1s0-1-1-1h0l-3-10c0-2-1-4 0-6 1 0 1 1 2 2l1-2-1-1h-2l-1-4c0-1 1-1 2-1h0z" class="i"></path><path d="M432 370h0l3 7h0c-1 0-1 0-1-1l-1-1h-2l-1-4c0-1 1-1 2-1zm6 25h1l-2-6v-1c-2-2-3-6-3-8 1 1 2 3 2 4 1 1 1 3 2 4s2 1 2 3c0 0-1 0-1 1 0 0 0 1 1 1v-1l2 2-1 1v2h-1l-2-2z" class="P"></path><path d="M347 339h1c0 2-2 3-4 4-2 3-5 6-7 9l-3 6c-2 4-4 8-6 13l-4 11c0 1-1 1-1 2l-2-1c-1 0-1 0-2-1 0-2 2-4 2-6 3-6 6-13 9-18 2-4 4-7 7-10 1-2 3-4 6-6 0-1 1-2 2-2 0 0 1 0 2-1z"></path><path d="M140 372v-1l-1-2c0-1 1-1 2-1 1 1 2 1 3 2l2 4c1 1 1 3 2 4h2l1-4c1 1 1 1 1 2-1 1-1 3 0 5v-3c0 2 0 5 1 7 0 2 1 4 1 6-1 1-1 2-1 4-1 0-1-1-2-1s-1-1-2-2c-1-2-2-5-4-7-2-3-5-5-7-7l1-2 3 3h0l-3-3v-1h1v-3z" class="Y"></path><path d="M146 378v-2-2c1 1 1 3 2 4 1 0 1 1 1 1-1 3 1 6 1 9 0 0-1 0-1-1v-1c-1-2-2-5-3-8z" class="K"></path><path d="M140 372v-1l-1-2c0-1 1-1 2-1 1 1 2 1 3 2l2 4v2 2l3 8v1c-1 0-1 0-1-1-1-1-1-2-2-3 0-1-1-2-2-3l-3-6c0-1 0-1-1-2z" class="G"></path><path d="M141 368c1 1 2 1 3 2l2 4v2 2c-3-1-3-3-4-6-1-1-1-2-1-4z" class="j"></path><path d="M472 367c1-6 3-12 6-17 4-5 10-12 17-11 2 0 5 1 6 1s2-1 2-1c2-1 4-2 6-2h5c5 1 10 2 14 5-2 0-4 1-5 1-1 1-3 2-4 2-4 0-8 1-11 0-2 0-4-2-5-3l-1-1c-4-1-8-2-12-1-6 3-12 10-14 16-1 2-2 3-2 5s-1 5-2 6z"></path><path d="M506 340h-2l1-1c5-1 11-2 16 1h-11-4z" class="Q"></path><path d="M521 340h1c-4 1-9 0-13 0 4 1 7 1 10 1l-1 1c-1 1-2 1-3 1h-1 0v1h-3 0-1c-1 0-1-1-1-1l-3-3h4 11z" class="g"></path><path d="M511 344c0-1 0-1 1-2 2-1 4-1 6 0-1 1-2 1-3 1h-1 0v1h-3z" class="M"></path><path d="M519 341l1 1h2l1 1h-1 1c-1 1-3 2-4 2-4 0-8 1-11 0-2 0-4-2-5-3l-1-1h1 1l1 1c1 0 2 1 3 1h1s0 1 1 1h1 0 3v-1h0 1c1 0 2 0 3-1l1-1z" class="X"></path><path d="M143 389h1c1 0 2 1 2 2h0c1 1 1 2 2 3v1c0 1 1 1 1 2l1 1h1l1 1h0c-1 1-1 1 0 2 1 3 1 5 1 8 0 5 0 10-1 15 0 1-1 3-1 4 0 2-1 3-1 5l-1-1-1-1-1 3c0-2 0-4 1-6 0-2 0-6-1-7v-1h1c0-1-1-1-1-2l-2-2h-1l1-1h0 1c1 1 1 1 2 0h0 1c0-1-1-1-2-2h0c0-1 1-1 1-2 0 0 0-1 1-1h0l-1-1-1-1c0-1-3-1-3-2l-2-2h1 1c0-1 1-3 2-4h2c1 1 2 2 2 3h1 0c-1-3-3-7-5-9-1 0-2-1-4-2h1l2-1h0l-2-2z"></path><path d="M147 404h0c2 0 2 0 3 1 2 2 2 5 1 7h0c-1-1-1-2-1-4l-2-2c-1 0-1 0-2-1l1-1z" class="H"></path><path d="M148 415h0 1c0-1-1-1-2-2h0c0-1 1-1 1-2 0 0 0-1 1-1 0 1 1 3 3 4l-1 1h0v1 1h-1c-1 2 0 2-1 3h-1c0-1-1-1-1-2l-2-2h-1l1-1h0 1c1 1 1 1 2 0z" class="B"></path><path d="M148 420h1c1-1 0-1 1-3h1c0 3 0 6-1 9-1 2-1 3-2 5l-1 3c0-2 0-4 1-6 0-2 0-6-1-7v-1h1z" class="j"></path><path d="M454 380c1 1 1 2 1 3h0 1c2 5 4 14 3 20h1c2-1 4-1 6 0 1 0 1 0 1 2h-1c-1 0-1-1-2-1s-2 1-3 3c-2 3-2 6-3 10l-4-7c0-2-1-4-1-6 0-1 0-2-1-2v-1c-1-2-2-3-2-6-1-1-2-3-3-5l1 1c1 1 1 2 2 3h0l2-1s0 1 1 1c0-1 1-1 1-2s0-1 1-1v1l1-1c0-2-1-5-1-8l-1-3z" class="f"></path><path d="M454 401c-1-1-1-2-1-4l1-1c1 2 1 3 0 5z" class="V"></path><path d="M454 396h0c1 0 1-1 2-1 0 1-1 3 0 3 0 1 1 1 1 1v4h0v3l-1 1h-1c0-1-1-2-1-3v-3c1-2 1-3 0-5z" class="D"></path><path d="M518 597c1-3 7-7 10-8 2-1 5-2 7-1h3c-4 5-6 11-13 12h-2c-1 0-1 0-2 1h1c1 1 1 1 2 1h0c0 1 1 1 1 1h1c0 1 1 1 2 1h0c1 1 2 1 3 1s1 0 2 1l-1 1c-5 0-10-3-14-6-1-1-2-3-4-4l-1 2c0-2 0-3-1-4h2c1 1 3 2 4 2z" class="i"></path><path d="M522 594c2-2 5-5 8-5 1-1 3-1 4 0h2 0c-1 3-3 5-5 7-3 2-7 3-11 3-1 0-1-1-2-2l2 1c2 0 4 0 5-1l6-5 3-3c-4 1-8 6-11 8h-3c0-2 1-2 2-3z" class="W"></path><path d="M263 403l1-1v-3-4h1v4c1 2 1 5 2 7 1-2 1-5 1-7v-1h0c2 1 3 1 4 3 1 1 1 3 1 4v1c1 2 2 4 2 6h1v-1h0 0l3 6v-8h1c0 2 0 6 1 8l1 1-2 1h-1-1c-1 1-1 2-1 3l-1 2v-1l-3 5v-3c-1 1-1 1-1 2h0l-1 1v-3h-1v-3c0-1 0-1 1-1v-3-3c-1-1-1-2-2-3 0 1 0 1-1 1v-3h-1c0 1-1 2-1 3h0c0-2 0-3-1-4l-1-1h-1v-3h1v-1c-1 0-1-1-1-1z" class="R"></path><path d="M264 408h-1v-3h1c0 1 1 1 1 2 1 1 1 2 2 3 0 1-1 2-1 3h0c0-2 0-3-1-4l-1-1z" class="o"></path><path d="M268 398c2 1 3 1 4 3 1 1 1 3 1 4-1 1 0 4 0 6h0c-1-2-2-6-3-9h0l-2-4z" class="G"></path><path d="M267 406c1-2 1-5 1-7v-1h0l2 4h0v1c0 2 1 5 1 8h-1c-1-1-2-4-3-5z" class="g"></path><path d="M271 415c0-1 0-1 1-2l1 1v3c1 0 1-1 1-2h1v3c1 0 1-1 1-2 1 1 2 2 4 2l1-1 1 1-2 1h-1-1c-1 1-1 2-1 3l-1 2v-1l-3 5v-3c-1 1-1 1-1 2h0l-1 1v-3h-1v-3c0-1 0-1 1-1v-3-3z" class="F"></path><path d="M275 418c1 0 1-1 1-2 1 1 2 2 4 2l1-1 1 1-2 1h-1-1c-1 1-1 2-1 3l-1 2v-1-2-2c-1 0-1 1-1 2-1 1 0 1-1 2h0c-1-1 0-2 0-3 0 0 1-1 1-2z" class="L"></path><path d="M522 601c2 0 4 2 6 2 5 1 9 0 13 1 4 0 9 3 12 6 5 3 8 7 9 13 0 2-1 6-3 8 0 1-1 1-2 1h0v-1c2-1 2-3 3-5-2-5-7-10-12-12-4-2-8-2-11-5-1 0-1-1-2-1-1-1-1-1-3-1l1-1c-1-1-1-1-2-1s-2 0-3-1h0c-1 0-2 0-2-1h-1s-1 0-1-1h0c-1 0-1 0-2-1z"></path><path d="M535 608v-1c3 0 5 1 7 1v2h0c-2 1-4 0-5-1-1 0-1-1-2-1z" class="B"></path><path d="M542 608c7 2 13 6 16 12l1 1h0l-6-6c-3-2-8-4-11-5h0 0v-2z" class="Q"></path><path d="M321 577l1 1h0c1 0 1-1 2-1v1h-1c0 1 0 2 1 3v1c1 0 1 1 1 1v1c2 1 2 2 2 4h0c1 1 2 2 2 3h-3c-1 1-1 2-2 3 1 1 2 0 3 1l-1 1c-1 0-2-1-4 0v1h-1l-4-2-1-2c-1 0-1-1-2-1l-1-1c-1 2-3 5-5 5h0-1c-1-1-2-1-3-1l6-6c3-3 6-7 9-10h0 1 1v-2z" class="G"></path><path d="M319 593c1 0 2-1 3 0v2h-1l-2-2z" class="B"></path><path d="M321 577l1 1h0c1 0 1-1 2-1v1h-1c0 1 0 2 1 3v1c1 0 1 1 1 1l-1 1h-1c-1 1-1 2-1 3l-1 1h0-1c0-2 0-3 1-4v-1c-1-1-1-2-1-4h1v-2z" class="X"></path><path d="M322 578h0c1 0 1-1 2-1v1h-1c0 1 0 2 1 3v1c1 0 1 1 1 1l-1 1h-1l-1-1c0-1 0-1 1-1v-1l-1-3z" class="g"></path><path d="M314 589c1-1 1-3 2-4h1c1 1 1 2 1 3 0 2 0 3 1 5l2 2 1 1v1h-1l-4-2-1-2c-1 0-1-1-2-1l-1-1 1-2z" class="c"></path><path d="M313 591l1-2 2 1c0 1 1 2 0 3-1 0-1-1-2-1l-1-1z" class="O"></path><path d="M324 584l1-1v1c2 1 2 2 2 4h0c1 1 2 2 2 3h-3v-1c-1 0-2 0-2 1h-2c-1-1-1-2-2-2v-1h1 0l1-1c0-1 0-2 1-3h1z" class="L"></path><path d="M325 584c2 1 2 2 2 4h0c1 1 2 2 2 3h-3v-1h0c0-2 0-4-1-6z" class="X"></path><path d="M324 584v3c0 1 1 2 0 3v1h-2c-1-1-1-2-2-2v-1h1 0l1-1c0-1 0-2 1-3h1z" class="P"></path><path d="M131 481c1-1 3-3 5-4 1-1 5-2 7-1 1 0 1 1 1 1h0c2 1 3 3 5 4h0c-2 0-3-1-5-2-3-1-8 2-10 4l-6 7c3-1 4-3 7-4v1c-1 3-3 6-4 10h0v1 10h-1v2c0 1 0 2 1 3l-1 2c0-1-1-2-2-2h0l-2-9c-1-6-1-12 0-17 0-1 0-3 1-4l2-2h0l1-1 1 1z" class="C"></path><path d="M128 497v1 2l2 2c1-1 0-3 1-4v10h-1v2c0 1 0 2 1 3l-1 2c0-1-1-2-2-2 0-1 1-2 1-3s-1-5-1-7v-2c-1 0-1-1 0-1v-3z" class="g"></path><path d="M128 497h0c1-1 1-2 2-2v-1l-2 1h0v-2c1 0 1-1 1-1 1-2 4-5 6-5-1 3-3 6-4 10h0v1c-1 1 0 3-1 4l-2-2v-2-1z" class="B"></path><path d="M131 481c1-1 3-3 5-4 1-1 5-2 7-1 1 0 1 1 1 1-5 1-9 2-12 6-2 2-3 5-5 6l-1 1h0v-3c0-1 0-3 1-4l2-2h0l1-1 1 1z" class="G"></path><path d="M127 483l2-2h0l1-1 1 1c-2 3-3 5-4 8l-1 1h0v-3c0-1 0-3 1-4z" class="f"></path><path d="M256 376c4 0 8 0 11 1h2 1 1v1l-1 1 1 1h0c0 1-1 1-1 2s0 4 1 5c-1 1-1 2-1 3l-1 1h0c0-3-1-5-2-7 0 1 1 3 0 4-1-1-1-2-1-3h-1v3h0c-1-1-1-3-1-5h-1v3h-1v-2l-1-1v-6h-1v4l-2 1v1c-1 2-1 4-1 6h1v6c0 1 1 2 1 3s-1 1-1 1c0 1 1 2 1 3l1 2c-1 0-1 0-2 1v4l-1-1c-1-2 1-4-1-6h0c-1 3 0 5-1 7h0v-5-3h0v-1l-1-1v-2c-1 0-1-1-1-2 0-2-1-4-1-6v-3c1-1 1-2 1-3s0-2 1-2v-2c1 0 1-1 1-2h2 0l-1-1z" class="U"></path><path d="M256 381l2-4h0c1 1 1 3 0 4v1h0v1h-1c0-1 0-1-1-2z" class="M"></path><path d="M256 381c1 1 1 1 1 2h1c-1 2-1 4-1 6h1v6c0 1 1 2 1 3s-1 1-1 1l-1-4v-9c-1 0-1 2-2 2-1-3 0-5 1-7z" class="W"></path><path d="M253 395c1 0 1 0 1-1l2-4h0c0 1 0 3 1 5h0l1 4c0 1 1 2 1 3l1 2c-1 0-1 0-2 1v4l-1-1c-1-2 1-4-1-6h0c-1 3 0 5-1 7h0v-5-3h0v-1l-1-1v-2c-1 0-1-1-1-2z" class="I"></path><path d="M499 520c5-6 13-7 20-7 2 0 6 1 7 0s1-2 1-3 0-1-1-2l-2 1v-1-1c1 0 1-1 2-1s2 0 3 1c1 0 2 1 2 3 1 1 1 2 0 3-2 3-7 3-10 4l-1-1h-1c-2-1-5-1-7 0-3 0-7 2-9 4-3 3-6 5-7 9 0 1 1 1 1 1-1 2-1 3-1 5h0-1c-1 0-1 0-1-1-1 1-2 3-3 4l-2 3c-1 8-7 15-12 20 0-1 1-2 2-4 2-2 4-5 6-8 1-2 2-5 3-7v-1h0v-1c0-2 1-4 2-6s2-4 4-6l-1-1 3-6 1-3 2 2z" class="V"></path><path d="M496 521l1-3 2 2c-1 3-3 6-5 8l-1-1 3-6z" class="B"></path><path d="M489 541v-1c2-3 2-7 4-10 3-3 5-8 8-11 6-5 12-4 19-4 3 0 6 0 9-3 1-1 0-2 0-4 1 1 1 1 1 2v2c-2 3-7 4-10 4h-1c-2-1-5-1-7 0-3 0-7 2-9 4-3 3-6 5-7 9 0 1 1 1 1 1-1 2-1 3-1 5h0-1c-1 0-1 0-1-1-1 1-2 3-3 4l-2 3z" class="H"></path><path d="M496 529c0 1 1 1 1 1-1 2-1 3-1 5h0-1c-1 0-1 0-1-1l2-5z" class="i"></path><path d="M208 623l-1-2h0c4 1 8 4 11 5 6 2 12 4 18 4 3-1 6-2 9-2h2 1l-7 3c0 1 1 1 1 2l-5 1-3 1-2 1c-2 0-4 0-7-1-4 0-8-2-12-4-1 0-4-2-5-3v-1c-2-1-4-4-6-6 2 1 4 2 5 3v-1h1z" class="b"></path><path d="M220 630c6 2 14 3 21 1 0 1 1 1 1 2l-5 1-3 1-2 1c-2 0-4 0-7-1h0c0-1-3-2-5-3h1l-2-2h1z" class="B"></path><path d="M225 635h0c0-1-3-2-5-3h1c2 1 4 1 7 2h9l-3 1-2 1c-2 0-4 0-7-1z" class="g"></path><path d="M213 631c-1 0-4-2-5-3v-1c-2-1-4-4-6-6 2 1 4 2 5 3v-1h1c2 1 4 2 5 3 3 2 5 3 7 4h-1l2 2h-1c2 1 5 2 5 3h0c-4 0-8-2-12-4z" class="Y"></path><path d="M272 245h0c1 0 2 0 2 1 1 1 0 4 0 5 1 1 2 2 2 4l-2 2h0l-1 2c-1 2-2 3-2 5l-1 2h1v3l-1 1h1l1 2c-1 0-1 1-2 1 0 1 0 1-1 2h0l-2 2h-1l-1-1h0-1l1-2h-1 0l-3 3c0-2 1-4 2-5l-1-1c-1 1-1 2-1 3h-1v-2l4-11 1-3v-1c0-1 1-3 1-3l6-9z" class="K"></path><path d="M265 274h0v-1l-1-1c1 0 1-2 2-2v-1-2h1c0 1 0 1 1 1 0-1 1-2 1-3l1 1h1v3l-1 1h1l1 2c-1 0-1 1-2 1 0 1 0 1-1 2h0l-2 2h-1l-1-1h0-1l1-2z" class="h"></path><path d="M271 266v3l-1 1h1l-1 1h-1c-1-2 1-3 1-5h1z" class="N"></path><path d="M265 276c1-1 1-2 2-3 1 0 1 0 2 1v-2l1 1c0 1 0 1-1 2h0l-2 2h-1l-1-1z" class="l"></path><defs><linearGradient id="G" x1="269.954" y1="245.489" x2="269.271" y2="256.081" xlink:href="#B"><stop offset="0" stop-color="#1e181d"></stop><stop offset="1" stop-color="#3f4642"></stop></linearGradient></defs><path fill="url(#G)" d="M272 245h0c1 0 2 0 2 1 1 1 0 4 0 5 1 1 2 2 2 4l-2-2-1-1-2-3c-1 3-3 5-3 8-2 2-3 5-4 7 0 1 0 3-1 4h-1c1-2 2-5 2-7h0l1-3v-1c0-1 1-3 1-3l6-9z"></path><path d="M271 249l2-1c1 1 1 2 1 3 1 1 2 2 2 4l-2-2-1-1-2-3z" class="c"></path><path d="M271 249l2 3 1 1 2 2-2 2h0l-1 2c-1 2-2 3-2 5h-1c-1 0-1 0-2 1v-1c-1 0-1 1-1 1v1h-1v-2s0-1 1-2c0-1 1-3 1-4l1-1h-1c0-3 2-5 3-8z" class="Z"></path><path d="M271 249l2 3-1 2h-1l-1 1h0c0 1 0 2-1 2-1 2-2 5-3 7 0 0 0-1 1-2 0-1 1-3 1-4l1-1h-1c0-3 2-5 3-8z" class="V"></path><path d="M273 252l1 1 2 2-2 2h0l-1 2c-1-1-1-2-2-3 0 2-1 5-2 6h0l2-8h1l1-2z" class="R"></path><path d="M271 256l1-1h1s1 1 1 2h0l-1 2c-1-1-1-2-2-3z" class="S"></path><path d="M271 380c0-1 0-2 1-3h1c1 1 2 1 3 1h0c0 1 0 2-1 3l1 1h0c1-1 1-2 2-3h1c-1 2-2 3-1 5 1 0 1 0 1-1 0 1 1 2 1 3 1 1 0 1 1 2 0 1 1 1 2 2 0 1 0 2 1 2v-1h0c1 2 3 4 3 6v1h1v-1c1 0 0 0 1 1 0 2-1 5 1 6v-4h1v2h1 0c0-1 0-1 1-2l1 2c-1 0-1 1-1 1 0 1 0 1-1 2v1 2c-1 0-2 0-3-1-1 0-3-2-3-3l-1-1c0 1 1 2 0 2h0s-1-1-1-2c-1-1-2-1-2-2h-1c-1 0-2-1-2-1v-1h-1c-1-1-1-2-2-3s-1-1-1-2 0-3-1-4c0 1-1 3-1 4v-6c-1 0-1 2-2 2v-3c-1-1-1-4-1-5s1-1 1-2h0z" class="l"></path><path d="M271 380c0-1 0-2 1-3h1c1 1 2 1 3 1h0c0 1 0 2-1 3v7h0c1-1 1-4 2-6v1 2h1 0c2 1 0 3 1 4 1 0 1 1 1 2v4 3l-3-3v-2c-1 0-1 1-2 1h0c0-1 0-3-1-4 0 1-1 3-1 4v-6c-1 0-1 2-2 2v-3c-1-1-1-4-1-5s1-1 1-2h0z" class="N"></path><path d="M273 377c1 1 2 1 3 1-1 1-1 2-2 3 0-1-1-2-1-4z" class="B"></path><path d="M130 358c-1-3-2-8-1-12 1-3 3-5 4-7 1-3 1-5 3-8 1 7 4 17 2 24-1 3-2 5-4 8 0 1 0 3 1 4v2l4 6v1l3 3h0l-3-3-1 2c-1-3-5-8-7-9h0v-1c0-1-2-4-2-6h1v-1-3z" class="E"></path><path d="M131 357h1c-1 1-1 2-2 4h0v-3l1-1z" class="D"></path><path d="M130 361h0c1 2 1 4 2 5 1 2 2 2 3 4s2 4 4 6h0l-1 2c-1-3-5-8-7-9h0v-1c0-1-2-4-2-6h1v-1z" class="L"></path><path d="M131 357c0-2-1-5-1-7-1-5 3-10 5-15 1 6 3 14 1 21 0 2-2 4-3 6 0 1-1 1-1 2 0-2 1-4 2-6 0-4-1-8-2-12v11h-1z" class="H"></path><path d="M279 502h0c1-1 1-1 2-1 1 1 2 1 3 1 5 0 10 1 15 1h7l9 1h0v-1h3 7 4c3-1 7 0 9 0 2 1 4 0 6 1h0 18l3 1h0 5 12v1h3v1h3c0 1 0 1-1 1h-2s-1 0-1 1h-1 0c-4-1-8-1-12-1h-18c-24-2-49-3-74-6z" class="i"></path><path d="M332 505l1-1h11l1 1h4 5l-1 1h-5l-16-1z" class="M"></path><path d="M329 503c3-1 7 0 9 0 2 1 4 0 6 1h0 0-11l-1 1-17-1h0v-1h3 7 4z" class="F"></path><path d="M318 503h7v1h-10v-1h3z" class="H"></path><defs><linearGradient id="H" x1="376.175" y1="502.967" x2="355.853" y2="507.6" xlink:href="#B"><stop offset="0" stop-color="#858684"></stop><stop offset="1" stop-color="#b5b3b7"></stop></linearGradient></defs><path fill="url(#H)" d="M344 504h18l3 1h0 5 12v1h3v1c-3-1-6 0-9 0-9 0-19 0-28-1h5l1-1h-5-4l-1-1h0z"></path><path d="M119 360v-1c1 0 2 1 3 2h1l2 2c1 1 2 2 2 3l4 3c2 1 6 6 7 9 2 2 5 4 7 7 2 2 3 5 4 7 1 1 1 2 2 2v4h-1l-1-1c0-1-1-1-1-2v-1c-1-1-1-2-2-3h0c0-1-1-2-2-2h-1s-3-2-4-3c-2-1-5-2-8-2l-7-2c-4-2-6 1-10 3h0v-2c1-3 5-4 8-5 1-1 3-1 4-2s1-2 2-3v1l-1 1 1 1c3-1 6 1 10 2-5-4-8-8-13-11-5 0-7 1-10 4h-1v-1c2-3 6-4 9-4-1-2-3-3-4-5h0v-1z" class="J"></path><path d="M119 360v-1c1 0 2 1 3 2h1l2 2c1 1 2 2 2 3h-1c-3-1-6-3-7-6z" class="Z"></path><path d="M123 379c1-1 3-2 4-2s3 0 4 1c4 1 10 3 12 6v1c-1 0-1 0-1-1h-1c-1-1-3-2-5-2-4-2-8-3-13-2v-1z" class="H"></path><path d="M124 382c-4-2-6 1-10 3h0v-2c1-3 5-4 8-5l1 1v1c5-1 9 0 13 2 2 0 4 1 5 2h1l-1 2c-1-1-3-2-4-2h0c-1 0-1-1-2-1h-2c-1-1-2 0-3-1h-6z" class="N"></path><path d="M122 500h1l-1-1c-1-1 0-5 1-6v3 2c0 1 0 1 1 2v4h2l2 9h0c1 0 2 1 2 2l1-2c-1-1-1-2-1-3 2 2 3 5 5 7 0 1 1 2 2 3s1 3 2 4c1 2 4 5 5 7v1h0-1l1 1-1 1-1-1-2 1c-1-1-2-1-3-1l-1-1v-1c-1-1-3-1-3-1-1 0-1-1-1-1-1-1-1-1-2-1h-3l-1-1c2-1 3 0 4 0-1-1-1-1-2-1 1 0 1 0 2-1l-12-7h3 0c-1 0-1 0-1-1v-1l5 3h0v-2c-1-2-2-5-2-8l-1-7v-2z" class="T"></path><path d="M131 513l5 10h0-1c-2-1-4-6-5-8l1-2z" class="Y"></path><path d="M130 510c2 2 3 5 5 7 0 1 1 2 2 3s1 3 2 4c1 2 4 5 5 7v1h0l-1-1-13-9h0 1l1 1c1 1 2 1 3 2h0l4 2c-1-1-2-2-2-4h-1l-5-10c-1-1-1-2-1-3z" class="b"></path><path d="M122 500h1l-1-1c-1-1 0-5 1-6v3 2c0 1 0 1 1 2v4h2l2 9h0c1 4 3 7 4 9-2-2-5-5-6-8-1-2-2-3-3-5l-1-7v-2z" class="W"></path><path d="M122 500v2c1 0 1 1 1 1 1 1 1 2 1 3 1 2 2 5 2 8-1-2-2-3-3-5l-1-7v-2z" class="F"></path><path d="M120 516l5 3c1 1 3 3 5 3l13 9 1 1h-1l1 1-1 1-1-1-2 1c-1-1-2-1-3-1l-1-1v-1c-1-1-3-1-3-1-1 0-1-1-1-1-1-1-1-1-2-1h-3l-1-1c2-1 3 0 4 0-1-1-1-1-2-1 1 0 1 0 2-1l-12-7h3 0c-1 0-1 0-1-1v-1z" class="M"></path><path d="M130 525l8 5c2 1 3 2 4 2l1-1 1 1h-1l1 1-1 1-1-1-2 1c-1-1-2-1-3-1l-1-1v-1c-1-1-3-1-3-1-1 0-1-1-1-1-1-1-1-1-2-1h-3l-1-1c2-1 3 0 4 0-1-1-1-1-2-1 1 0 1 0 2-1z" class="N"></path><path d="M136 531c2 0 4 1 6 2h0l-2 1c-1-1-2-1-3-1l-1-1v-1z" class="Y"></path><path d="M264 500l9 1c1 1 5 1 6 1 25 3 50 4 74 6h18c4 0 8 0 12 1h0 1l-1 1h0-2c-1-1-3 0-4 0-4 1-8 0-11 0l-11 1c-3 0-6-1-10-1l-15-1c-9-1-18-1-26-2h0l-32-4c-3 0-6-1-9-2h0l1-1z" class="Q"></path><path d="M264 500l9 1c-1 1-2 1-2 1h-3 0 2c1 0 1 0 2 1-3 0-6-1-9-2h0l1-1z" class="B"></path><path d="M154 642c-1-1-4-3-5-5 0-1 0-2 1-3 2-2 4-3 5-5l-1-4-1-1c-1 2 0 5-3 5-1 0-1-1-2-1 0-1 0-2 1-3s4-1 5-1l13-2-3 9-3 9c0 1 0 2 1 3 0 0 2 1 3 1-2 3-5 5-8 7-1-1-2-2-2-3 1-1 1-2 2-3 0-1-1-2-2-2l-1-1z"></path><path d="M154 642h0c0-1-1-1-2-2s-1-2-2-3l3-3c1 2 0 2 0 4v1c1 1 3 2 4 3v1h-2l-1-1z" class="a"></path><path d="M142 513c0-4 3-8 6-11-2 4-6 11-4 16l3 3c2 2 4 5 4 8h1c0-2 2-4 2-6 0-1-4-3-5-5-1-1-1-3-1-4 0-2 0-2 1-3 2-1 6 0 8 1h1l-1 3v1c-1 0-1-1-2-1h-2v1 1c0 1-1 1-1 1l1 1c2 1 4 3 5 6-1-1-2-1-3-2h-1v2c1 2 3 3 3 5 0 1-1 3-1 5l-2 13v-1c-1-3-3-7-6-10-2-1-3-2-5-3l1-1c1 0 1 0 1 1l2 1-2-4c0-1 1-1 1-1-1-3-1-5-2-7s-2-3-1-5h-1v-5z" class="i"></path><path d="M157 515h0c-1-1-3-1-4-1-1 1-2 2-2 4h-1v-2c-1-1-1-3-1-4 2-1 4 0 6 0h1 1 1l-1 3z" class="G"></path><path d="M143 518c2 3 5 5 6 9 1 2 1 4 1 5h0c-1 0-1-2-2-3 0-2-1-4-2-5 0 2 2 6 1 8l-1-2c-1-3-1-5-2-7s-2-3-1-5z" class="d"></path><path d="M152 529v1h0v1c1-1 0-3 2-4 1 0 2 2 2 3v2l-2 11c-1-2-2-4-2-6v-1-1h-1l-1 1v-1l3-2c-1 0-1-1-1-1h-2l1-1v-2h1z" class="l"></path><path d="M103 511c1 0 2 1 3 1v-1c1 0 3 2 4 3l1-1 7 5 12 7c-1 1-1 1-2 1h-1-5v-1l-2-1v1c-2 0-4-2-5-2-1-1-2-1-2-2-1 0-1-1-1-1-1-1-3-1-4-2-2 1-4 2-7 3-2 1-5 1-7 2-5 1-11 5-15 8v-1h-1l1-1 1-1-1-2c3-2 5-5 8-6l4-4c3-3 8-4 12-5z" class="W"></path><path d="M90 520c0 1-1 2-2 3-2 2-5 3-8 5h0l-1-2c3-2 5-5 8-6h2 1z" class="E"></path><path d="M101 516h0c4 0 10 1 14 4 1 1 3 2 6 3 0 0 0 1-1 1v1c-2 0-4-2-5-2-1-1-2-1-2-2-1 0-1-1-1-1-1-1-3-1-4-2-2 1-4 2-7 3-2 1-5 1-7 2-5 1-11 5-15 8v-1h-1l1-1v1c5-3 10-6 15-8 3-1 5-1 8-2 2-1 3-1 5-2-6 0-11 1-16 2 2-2 4-2 6-3h4v-1z" class="J"></path><path d="M103 511c1 0 2 1 3 1v-1c1 0 3 2 4 3l1-1 7 5 12 7c-1 1-1 1-2 1h-1-5v-1l-2-1c1 0 1-1 1-1-3-1-5-2-6-3-4-3-10-4-14-4h0l-1-1c-1 0-1-1 0-2-3 1-7 2-9 4 0 1-1 2-1 3h-1-2l4-4c3-3 8-4 12-5z" class="f"></path><path d="M120 522c2 1 5 3 7 4h-5v-1l-2-1c1 0 1-1 1-1l-1-1z" class="M"></path><path d="M101 516c0-1-1-1 0-2s3-1 4-1c3 1 6 4 9 5l6 3v1l1 1c-3-1-5-2-6-3-4-3-10-4-14-4z" class="Q"></path><path d="M95 439l-1-1h0c1 0 1-1 2 0 2 2 2 5 3 8h0c1-2 2-4 4-6 2 0 2 0 3 1 1 2 1 4 0 6-1 3-3 6-5 9 3-1 5-1 7-2s3-3 5-4c1 0 2 0 3 1 1 0 3 1 3 2 0 2-2 3-4 4-3 1-6 0-9-1-1 0-2 1-3 1v1c2 3 4 4 4 8 0 1 0 2-1 3-1 0-2 1-3 0-2-1-5-6-6-8l-2 3h-1l2-4c-1-1-1-1-2-1h0v-1-1l-1-1c-1-1-2-2-2-4h-1v-2-1-2-1l4 3c0-3 0-6 1-9h0v-1z"></path><path d="M95 439c2 2 3 8 3 11v4c-1-1-2-4-4-5 1-2 2-7 1-9h0v-1z" class="W"></path><path d="M103 441h1c1 0 1 1 2 1 0 3-1 4-2 6s-1 4-3 5l-1-1v-1c1-1 1-2 1-3l-1 2-1 1v-4l1-1c0-2 1-4 3-5z" class="m"></path><path d="M90 447c2 1 3 3 4 3 1 1 2 3 3 4h-1-1v1c1 0 1 1 1 1 2 0 3-1 4-1h0c-1 1-1 2-1 3l2 2c1-1 1-1 2 0 1 0 3 2 3 3v2h0c-1-2-2-3-3-4h-1c0 2 2 1 2 4 0 1 1 1 2 1 0 1 0 1-1 2h-2c-1-1-4-5-5-6v-1h1l3 3h1l-3-3c0-1 0-1-1-1-2-1-3-2-3-4-2 0-3-1-3-2-1 0-2-1-2-2h-1v-2-1-2z" class="Q"></path><path d="M282 418l1 2h0c0-5-1-9-1-13l3 12c1-5 1-8 1-13 1 3 1 5 2 8s3 6 5 8h1v1h-1l-3-3c0-1-1-2-2-2h0c0 1 1 2 1 2v1c-1 2 0 5 0 8-1 1-1 1-1 2s-1 2-1 3v1c-1 1-1 1-1 2h0l-1 1h1c0 1 0 2 1 3l1 5v3h-2l-1-1c0-1 0-2-1-3 0 0-2 1-3 1v-1h-2c-1-1-2-3-2-4-1 0-1-1-2-1l-1 1h0v-2h1c0-1 0-2-1-3v-1c0-1 0-2-1-2-1-2 0-3 0-5l3-5v1l1-2c0-1 0-2 1-3h1 1l2-1z" class="P"></path><path d="M287 421c1 1 1 2 1 4-1 0-1 0-2 1v1l-1 3h-1c-1 0-1-2-2-4 0-1 0-2 1-3h1c1 1 1 1 2 1 1-1 1-2 1-3z" class="Q"></path><path d="M277 422c0-1 0-2 1-3h1 1c1 1 1 2 2 3 0 1-1 4-1 5-1-1-1-2-1-3-1 1-1 1-1 2l-1 1c-1 0 0-2 0-3-1-1-1-1-1-2z" class="B"></path><path d="M279 426h0c1 1 0 3 1 4 0-1 0-1 1-2h0 1c0 2 0 3 1 5v1h1c0 2 0 2-1 3l1 1c-1 1-1 1-1 2l-1-2c-1 1-1 1-2 0v-2l-1 1-1-3c0-1 1-2 1-3s0-2-1-3v-1l1-1z" class="R"></path><path d="M279 426h0c1 1 0 3 1 4 0-1 0-1 1-2-1 2-1 3-1 5v3l-1 1-1-3c0-1 1-2 1-3s0-2-1-3v-1l1-1z" class="K"></path><path d="M277 422c0 1 0 1 1 2 0 1-1 3 0 3v1c1 1 1 2 1 3s-1 2-1 3l-2-1v1c-1 1-1 1-2 1 0-1 0-2-1-2-1-2 0-3 0-5l3-5v1l1-2z" class="F"></path><path d="M276 433c-1-2-1-6-1-8l1 7 2-4c1 1 1 2 1 3s-1 2-1 3l-2-1z" class="X"></path><path d="M276 423v1 1h-1c0 2 0 6 1 8v1c-1 1-1 1-2 1 0-1 0-2-1-2-1-2 0-3 0-5l3-5z" class="C"></path><path d="M287 421v-4h1v1h0c0 1 1 2 1 2v1c-1 2 0 5 0 8-1 1-1 1-1 2s-1 2-1 3v1c-1 1-1 1-1 2h0l-1 1h1c0 1 0 2 1 3l1 5v3h-2l-1-1c0-1 0-2-1-3 0 0-2 1-3 1v-1h-2c-1-1-2-3-2-4-1 0-1-1-2-1l-1 1h0v-2h1c0-1 0-2-1-3v-1c1 0 1 0 2-1v-1l2 1 1 3 1-1v2c1 1 1 1 2 0l1 2c0-1 0-1 1-2l-1-1c1-1 1-1 1-3 0-1 0-2 1-4l1-3v-1c1-1 1-1 2-1 0-2 0-3-1-4z" class="M"></path><path d="M282 441h1v3c-1-1-1-1-1-3h0z" class="B"></path><path d="M287 441l1 5c-1 0-1 0-2-1 0-2-1-3 1-4z" class="G"></path><path d="M286 427v1c0 2 0 4-1 5 0 2 1 5 0 7-1 0-1-1-1-2l-1-1c1-1 1-1 1-3 0-1 0-2 1-4l1-3z" class="C"></path><path d="M276 433l2 1 1 3-1 2s-1-2-2-3h0v2c0 1 1 2 1 3-1 0-1-1-2-1l-1 1h0v-2h1c0-1 0-2-1-3v-1c1 0 1 0 2-1v-1z" class="R"></path><path d="M129 424v-2c-1-2 0-4 2-5 0 0 1-1 2-1 1 2 2 4 2 6h1c1 0 4-1 6 0v2c-1 2-3 3-4 5h1c-2 0-3 1-5 1 3 3 7 4 7 9 0 1-1 2-1 3l-1 1v-1-2c-1 0-3 0-4-1-2-1-3-2-4-2v1 2c1 3-2 6-4 8h-1c-1 0-1-1-2-2 0-2-1-4 0-6h0c0-2 1-2 2-4h0v-1h-1c-1 1-2 2-3 2-3 0-6 0-8-2v-1c0-1 0-1 1-2 2-2 4-2 7-2 1 0 1 0 1-1-1-1-1-2-1-3s0-2 1-2 4-1 5 1c0 0 0 1 1 1l1-1-1-1z"></path><path d="M129 424h2 1l-1-1h-1v-3c1-1 1-2 2-2s1 0 2 2c0 1 0 2-1 3 0 1-1 2-1 4h-1v-1l-1-1-1-1z" class="Q"></path><path d="M135 422h1v1h1c2 0 2-1 4 0v1c-1 2-3 4-5 5-1 0-1 0-2 1h0l-1-1v-1c1 0 2-1 2-2h-1c0-2 0-3 1-4z" class="G"></path><path d="M125 435h-1c0 1-1 1-2 1h-3c0-1-1-2-1-2h-1 0l2 2h-1c-1 0-2-1-4-1 1-1 0-1 1-1 1-1 3-3 5-4 1 0 3 1 5 1l1 1c-2 1-3 2-5 3h0c1 0 1 0 2-1h2 0l1 1h-1z" class="H"></path><path d="M513 442c0-1 1-1 2-1s1 0 2 1c0 1 0 2-1 3 0 2-2 3-4 3-3 1-6 1-8 1l-8 3c-2 2-4 3-5 5 0 0-1 1-1 2 0 0 1 0 0 1s-1 3-2 4c1 0 2-2 3-3l6-6h-1c-1 0-3 2-4 3h0c3-4 7-6 12-8 2 0 5 0 7 1h3c7 2 12 6 16 12h1 0c-1 2 0 4 0 6 0 3-4 6-3 9l1 1 2-2v1l-2 3c-1 0-2 1-3 1h-2l-2-1c0-1 0-1-1-2s-1 0-3 0h0c-1-1-2 0-3 0 0-1-1-2-1-3-2-3-4-1-7-3-2-1-3-3-3-6h0c2-1 4 0 5 1h0v1l2-1c3 1 4 3 7 5 1 2 3 3 5 4 0-1 1-2 1-3h1l1 1 1-1c0-1-1-3-1-4v-1h0l2 2h1c0-1 0-1-1-1v-3c-1-5-5-9-9-11-5-3-12-4-17-2h0-2c-6 3-9 8-11 13 1-1 3-3 6-4h4l-1 1c-1-1-2-1-3 0-2 1-4 3-5 4v1c-1 2-1 3-2 4 0 1-1 3-1 4-1-1 0-3-1-3v-3h0l1-1v-1c0-1 0-2-1-2h-1v-1h1c3-9 8-17 18-18 3-1 8 0 10-2v-1c-1 0-1 0-1-1-1-1-1-1 0-2z" class="i"></path><path d="M513 442h0 1c1 0 1 0 2 1 0 1 0 2-1 2l-1 1v-1c-1 0-1 0-1-1-1-1-1-1 0-2z" class="W"></path><path d="M312 572h1l1-1h1v-1c0-1 3-3 4-3l1 1h0l2 1c0 1 1 1 1 2h1c1 0 1 0 1 1l1 1c-1 0-1 1-2 1 1 1 1 2 2 2v5h0c-1-1-2-2-2-3v-1c-1 0-1 1-2 1h0l-1-1v2h-1-1 0c-3 3-6 7-9 10l-6 6c-3 1-5 3-7 4v-1-1l1-2h-4c-1 0-2 1-3 1h-4 0-2-2c0 1 0 1-1 1h0c-1 0-1 0-2-1 2 0 4-1 6-2 5-2 9-6 14-10l5-4c1-2 2-3 4-4l1-1 2-3z" class="G"></path><path d="M320 572c0 1 0 2-1 3v1l-2 2-4 4-1-2c2-1 4-3 5-5l3-3z" class="Z"></path><path d="M294 595l8-6c1 0 2-1 3-2 3-2 5-5 7-7l1 2h0c-3 2-4 4-6 6s-4 4-6 5c-1 1-2 2-3 2h-4zm28-26c0 1 1 1 1 2h1c1 0 1 0 1 1l1 1c-1 0-1 1-2 1 1 1 1 2 2 2v5h0c-1-1-2-2-2-3v-1c-1 0-1 1-2 1h0l-1-1v2h-1-1 0l-2-1 2-2v-1c1-1 1-2 1-3l2-3z" class="F"></path><path d="M320 575l2-3c0 1 0 1 1 2h1c-2 0-2 1-3 2l-1-1z" class="R"></path><path d="M323 571h1c1 0 1 0 1 1l1 1c-1 0-1 1-2 1h0-1c-1-1-1-1-1-2l1-1z" class="C"></path><path d="M322 569c0 1 1 1 1 2l-1 1-2 3-1 1v-1c1-1 1-2 1-3l2-3z" class="a"></path><path d="M319 576l1-1 1 1v1 2h-1-1 0l-2-1 2-2z" class="V"></path><path d="M312 572h1l1-1h1v-1c0-1 3-3 4-3l1 1h0c-2 1-3 4-5 6l-8 8h-2v-2c1-2 2-3 4-4l1-1 2-3z" class="p"></path><defs><linearGradient id="I" x1="294.143" y1="594.205" x2="301.14" y2="579.368" xlink:href="#B"><stop offset="0" stop-color="#313333"></stop><stop offset="1" stop-color="#4e4b4c"></stop></linearGradient></defs><path fill="url(#I)" d="M300 584l5-4v2h2c-3 3-6 5-9 7-4 2-7 5-11 7h-2-2c0 1 0 1-1 1h0c-1 0-1 0-2-1 2 0 4-1 6-2 5-2 9-6 14-10z"></path><path d="M313 582l4-4 2 1c-3 3-6 7-9 10l-6 6c-3 1-5 3-7 4v-1-1l1-2c1 0 2-1 3-2 2-1 4-3 6-5s3-4 6-6h0z" class="T"></path><path d="M91 482c0-3-1-7 0-10l1-3h1c-2 10 0 22 5 30 4 5 10 9 15 12v-1c-2-2-3-4-4-7-1-2-4-5-6-6l1-1c-1-1-2-2-2-3v-3c0-2 0-4 2-6v1h0c1 1 1 2 1 3l1-3c1 0 2-1 2-2s0-1 1-1l4-6c1-1 2-2 3-2-1 2-3 3-4 5l-1 1h1c0 1-1 2-1 3v4c1 5 1 10 3 14 2 5 4 13 8 15-1-5-3-11-2-17 1 1 1 1 2 3l1 7c0 3 1 6 2 8v2h0l-5-3v1c0 1 0 1 1 1h0-3l-7-5c-2-1-5-2-6-4-1 0-1 0-2-1-6-6-12-13-13-22v-5c1 0 1 0 1 1z" class="I"></path><path d="M113 504l4 10c-2-3-5-6-6-10h2z" class="B"></path><path d="M111 501c-3-5-3-9-2-15l1-2c1 6 1 11 2 17h-1z" class="W"></path><path d="M104 496c-1-1-2-2-2-3v-3c0-2 0-4 2-6v1 6c1 2 1 4 2 6-1 0-1 0-2-1z" class="m"></path><path d="M105 509h0c0-1-1-1-1-1l-1-1v-1c1 1 2 2 3 2 3 1 5 3 8 4 2 2 4 3 6 4v1c0 1 0 1 1 1h0-3l-7-5c-2-1-5-2-6-4z" class="B"></path><path d="M90 486v-5c1 0 1 0 1 1 1 10 5 18 13 24l2 2c-1 0-2-1-3-2v1l1 1s1 0 1 1h0c-1 0-1 0-2-1-6-6-12-13-13-22zm19-4l4-6c1-1 2-2 3-2-1 2-3 3-4 5l-1 1c-2 4-4 8-3 14 0 2 1 5 1 7l1 1h1v-1h1l1 3h-2c-1-2-3-4-4-6 0-1-1-3-1-5-1-2-1-3-1-5l1-3c1 0 2-1 2-2s0-1 1-1z" class="Q"></path><path d="M126 398c4-1 7 0 11 2 2 1 5 2 6 4h-1l2 2c0 1 3 1 3 2l1 1 1 1h0c-1 0-1 1-1 1 0 1-1 1-1 2h0c1 1 2 1 2 2h-1 0c-1 1-1 1-2 0h-1 0l-1 1h1v3c-1-2-3-5-6-7h-2-2s0 1-1 1c-1-1-3-1-4-1s-1 0-2-1h-1c-1 1 0 3-1 4h0c-2 2-2 3-4 3s-3 0-4-1c-2-2-2-5-2-7v-2c-5 4-11 9-17 11h-1c-2 1-5 0-8 0v-1c3 1 6 1 9 0s7-3 9-5l3-3 6-3 1-2c2-4 4-6 8-7z" class="J"></path><path d="M121 411c1-1 2-2 4-2h1c1 1 2 1 2 2h-1c-1 1 0 3-1 4 0-1 0-2-1-4-1 0-2 0-4 1v-1z" class="H"></path><path d="M141 405c1 1 2 2 2 3 1 0 1 1 2 1l-1 1h0c-2 0-4 0-5-1v-1h1 1c0-1 0-1-1-1l1-2z" class="L"></path><path d="M124 401h5v1c-1 0-2 0-3 1-2 0-4 1-6 2 1-2 2-3 4-4z" class="H"></path><path d="M130 412v-2l1-1-2-2h0c2 0 3 1 4 3 1 1 1 2 1 3-1-1-3-1-4-1z" class="j"></path><path d="M144 416c-1-2-2-3-4-4v-1c1 0 2 0 3 1v1l1-1c2 1 3 1 4 3-1 1-1 1-2 0h-1 0l-1 1z" class="G"></path><path d="M129 401c2 0 4 1 6 2s4 1 6 2l-1 2h-1l-3-3h-4c-1 0-2-1-3-1h-3c1-1 2-1 3-1v-1z" class="Y"></path><path d="M123 404c5-1 10 1 14 2l1 1-1 1c-3 0-7-2-10-2h-2-1v-1l-1-1z" class="B"></path><path d="M123 404l1 1v1h1c1 0 3 0 3 1 1 1 1 1 1 2h-1c-1-1-3-2-5-1-1 0-2 1-3 2v1h1v1l1 1c1 0 1-1 2-1l1 1v1c0 1-1 2-2 2h-2s-1 0-1-1c-1-2-1-5-1-8 1-1 3-2 4-3z" class="Q"></path><path d="M126 398c4-1 7 0 11 2 2 1 5 2 6 4h-1l2 2c0 1 3 1 3 2l1 1 1 1h0c-1 0-1 1-1 1 0 1-1 1-1 2h0c1 1 2 1 2 2h-1 0c-1-2-2-2-4-3l-1 1v-1l1-2h0l1-1c-1 0-1-1-2-1 0-1-1-2-2-3-2-1-4-1-6-2s-4-2-6-2h-5l1-1 1-1h1l-1-1z" class="D"></path><path d="M144 410h0l1-1 2 1v1c-1 1-1 1-2 1h-1l-1 1v-1l1-2z" class="F"></path><path d="M115 523c1 0 3 2 5 2v-1l2 1v1h5 1c1 0 1 0 2 1-1 0-2-1-4 0l1 1h3c1 0 1 0 2 1 0 0 0 1 1 1 0 0 2 0 3 1v1l1 1-2 1c-9-1-16 0-24 6h0l-3 3c-5 7-8 13-7 21v2l-1 1c0-2 0-2-2-3v-1-4-2l-1-1c0-3 1-6 2-9l-1-1c1-6 8-9 12-12h-5c-3-1-6-3-8-5l1-1h3c1-1 9-1 10-1 1-1 3 0 4-1v-3z" class="R"></path><path d="M100 529h3c1 1 2 1 2 2 2 1 7 1 9 1 1-1 1-1 2 0l-5 1c-1 1-7 0-9-1-1-1-2-1-2-3z" class="M"></path><defs><linearGradient id="J" x1="125.844" y1="526.788" x2="106.836" y2="530.192" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#666465"></stop></linearGradient></defs><path fill="url(#J)" d="M115 523c1 0 3 2 5 2v-1l2 1v1h5 1c1 0 1 0 2 1-1 0-2-1-4 0l1 1h3c1 0 1 0 2 1 0 0 0 1 1 1h-5c-3 0-6 1-9 2l-1-1c-1 0-1 0-2 1-1-1-1-1-2 0-2 0-7 0-9-1 0-1-1-1-2-2h-3 2l1-1h-2c1-1 9-1 10-1 1-1 3 0 4-1v-3z"></path><path d="M128 530c-1-1-2-1-3-1 2-1 3-1 5-1 1 0 1 0 2 1 0 0 0 1 1 1h-5z" class="D"></path><path d="M115 523c1 0 3 2 5 2v-1l2 1v1c-2 0-4 1-6 1-2-1-4 0-5 0 1-1 3 0 4-1v-3z" class="B"></path><path d="M133 530s2 0 3 1v1l1 1-2 1c-9-1-16 0-24 6h0l-3 3c-5 7-8 13-7 21v2l-1 1c0-2 0-2-2-3v-1-4c0-3 1-5 2-8 3-9 11-15 19-19 3-1 6-2 9-2h5z" class="d"></path><path d="M379 201h3c2 2 1 5 0 7l-6 27v1c0 3 0 3 2 6h0c-2-1-3-2-4-2l-4-4c-2-2-4-4-6-4 0 0-2-1-2-2l-2-1-6-3c-1-1-3-1-4-2v-1h2c1 0 2-1 3-1l1 1h1c1 0 1 0 1-1h2c1 0 1 0 2-1l-2-1h1 2c0-1 2-2 2-3h1c1-1 1-2 1-3h1l2-1c0-1 1-2 2-3 1 0 2-1 2-2h1c0-1 1-2 2-2l1 1v2-3c1-1 1-1 1-2v-3z" class="e"></path><path d="M373 228v3h-3c1 1 2 2 3 2 1-1 1-5 2-6v-2l1-3 4-15 1-4h1c0 1 0 2-1 3l-7 28h0c-2 1-5-3-7-4-1-1-2-1-3-2v-1-1c2 0 3 1 5 0 1-1 1-1 2-1l1 1h0 0c0 1 0 1 1 2z" class="D"></path><path d="M371 225l1 1h0 0-2l2 2-1 1h-3l-1 1c-1-1-2-1-3-2v-1-1c2 0 3 1 5 0 1-1 1-1 2-1z" class="T"></path><path d="M364 227h3 1 0v2l-1 1c-1-1-2-1-3-2v-1z" class="V"></path><path d="M354 226c-1-1-3-1-4-2v-1h2c1 0 2-1 3-1l1 1-1 1h1c2 1 5 3 8 4 1 1 2 1 3 2 2 1 5 5 7 4h0c1 0 1 1 2 1v1c0 3 0 3 2 6h0c-2-1-3-2-4-2l-4-4c-2-2-4-4-6-4 0 0-2-1-2-2l-2-1-6-3z" class="I"></path><path d="M374 208h1c0-1 1-2 2-2l1 1v2h0l-1 1-1 1v1h1c0 1-1 1-1 2h0v1c0 3-1 5-2 7 0 2 0 4-1 6-1-1-1-1-1-2h0 0l-1-1c-1 0-1 0-2 1-2 1-3 0-5 0v1 1c-3-1-6-3-8-4h-1l1-1h1c1 0 1 0 1-1h2c1 0 1 0 2-1l-2-1h1 2c0-1 2-2 2-3h1c1-1 1-2 1-3h1l2-1c0-1 1-2 2-3 1 0 2-1 2-2z" class="X"></path><path d="M370 224c-1-1-2-1-3-1l1-1h1c2 0 2 1 2 2h-1z" class="K"></path><path d="M369 222v-1c-1 0-2 0-2-1h0 3 1l1 1v2h1v1h-2c0-1 0-2-2-2z" class="T"></path><path d="M374 218h1v-1h-1c0-1 1-2 2-2 0 3-1 5-2 7 0 2 0 4-1 6-1-1-1-1-1-2h0 0l-1-1h-1v-1h1 2v-1h-1v-2l-1-1h3v-2h0z" class="V"></path><path d="M374 208h1c0-1 1-2 2-2l1 1v2h0l-1 1-1 1v1h1c0 1-1 1-1 2h0v1c-1 0-2 1-2 2h1v1h-1c-1 0-1 0-2 1h0-7l4-3c-1 0-1-1-1-2l2-1c0-1 1-2 2-3 1 0 2-1 2-2z" class="k"></path><path d="M374 208h1c0-1 1-2 2-2l1 1v2h0l-1 1-1 1v-1c-1 0-1 0-2 1h-1c-1 1-1 1-2 1l-1 1c0-1 1-2 2-3 1 0 2-1 2-2z" class="U"></path><path d="M229 502l19 5s0 1-1 1h0v1 1c2 3 2 9 1 12-1 2-2 3-2 4l-3 2v-1c0 1-1 1-1 1-1 2-3 4-3 6v1l-3 4c-1 2-1 6-2 8l-1-4c-2-5-1-11-2-16 0-3-1-6-1-9l-1-14v-2z" class="I"></path><path d="M229 504l2 1c1 1 2 2 3 2 2 4 3 7 3 11-1-2-2-5-4-7-1-1-2-2-2-3-1-1-1-2-2-4z" class="B"></path><path d="M231 517v-2-1c4 4 6 9 6 14 0 3 0 5-2 7-1-1 0-5 0-7-1-4-2-7-4-11z" class="Q"></path><path d="M231 517c2 4 3 7 4 11 0 2-1 6 0 7 0 2-1 4-2 6v2c-2-5-1-11-2-16 0-3-1-6-1-9h0c1 1 1 1 1 2h1l-1-3z" class="K"></path><path d="M229 502l19 5s0 1-1 1h0v1 1c2 3 2 9 1 12-1 2-2 3-2 4l-3 2v-1c0 1-1 1-1 1 1-3 3-5 3-8h0c-1 2-2 5-4 6h-1-1c-2-2 0-7-1-9 0-3 0-5-1-7l-3-3c-1 0-2-1-3-2l-2-1h0v-2z"></path><path d="M231 505c4 1 6 1 8 5 2 3 2 12 1 16h0-1c-2-2 0-7-1-9 0-3 0-5-1-7l-3-3c-1 0-2-1-3-2z" class="d"></path><path d="M241 518c0-3 0-7-1-9l-3-3 10 2h0v1l-1-1c-3 3-3 6-3 9-1 2-1 3-1 4h-1v-3z" class="W"></path><path d="M241 518v3h1c0-1 0-2 1-4 0-3 0-6 3-9l1 1v1c2 3 2 9 1 12-1 2-2 3-2 4l-3 2v-1c0 1-1 1-1 1 1-3 3-5 3-8h0c-1 2-2 5-4 6v-8z" class="l"></path><path d="M247 510c2 3 2 9 1 12-1 2-2 3-2 4l-3 2v-1c2-2 3-5 3-8 1-3 1-6 1-9z"></path><path d="M330 566l1 1 3 3c0 1 1 2 2 3 1 2 3 3 4 4l2 1 7 5v1c4 2 7 4 12 5-1 1-2 2-3 2-1 1-2 2-3 2s-1 1-2 1-2 0-3-1c-2 0-5 1-7 2l-2-1-3-4-1 1c-2 3-2 3-5 4-2 0-2-2-3-3v-1c0-1-1-2-2-3h0c0-2 0-3-2-4v-1s0-1-1-1v-1c-1-1-1-2-1-3h1c0 1 1 2 2 3h0v-5c-1 0-1-1-2-2 1 0 1-1 2-1l-1-1c0-1 1-1 1-2l3-3 1-1z" class="Z"></path><path d="M326 573h0c1 1 2 3 3 3h1c4 3 8 8 12 11h0-1l-2-2-6-5-2-2-1-1h-1v3h-1c0-1-1-2-2-4-1 0-1-1-2-2 1 0 1-1 2-1z" class="N"></path><path d="M335 587l1-1h2l1-1 2 2c2 2 6 4 9 5v1c-2 0-5 1-7 2l-2-1-3-4c-2-1-2-1-3-3z" class="Q"></path><path d="M330 566l1 1 3 3c-1 0-2 0-2 1v1c1 1 2 3 4 5 0 0 1 1 2 1l-1 2c-1-1-4-4-5-4s-1-1-2-1v1h-1c-1 0-2-2-3-3h0l-1-1c0-1 1-1 1-2l3-3 1-1z" class="L"></path><path d="M326 570c2 2 3 3 4 5v1h-1c-1 0-2-2-3-3h0l-1-1c0-1 1-1 1-2z" class="a"></path><path d="M330 566l1 1 3 3c-1 0-2 0-2 1v1c-2-2-3-3-3-5l1-1z" class="B"></path><path d="M326 576c1 2 2 3 2 4h1v-3h1l1 1 2 2 6 5-1 1h-2l-1 1c1 2 1 2 3 3l-1 1c-2 3-2 3-5 4-2 0-2-2-3-3v-1c0-1-1-2-2-3h0c0-2 0-3-2-4v-1s0-1-1-1v-1c-1-1-1-2-1-3h1c0 1 1 2 2 3h0v-5z" class="M"></path><path d="M333 583c1 2 2 3 2 4v1c-1 1-3 1-4 1v-2c1-1 1-3 2-4z" class="E"></path><path d="M333 580l6 5-1 1h-2l-1 1h0c0-1-1-2-2-4h-2 0v-2l2-1z" class="B"></path><path d="M326 576c1 2 2 3 2 4h1v-3h1l1 1-1 1c0 2 0 3-1 4l1 1h0-2v1h0c1 1 2 2 2 3h-3 0c0-2 0-3-2-4v-1s0-1-1-1v-1c-1-1-1-2-1-3h1c0 1 1 2 2 3h0v-5z" class="j"></path><path d="M335 587h0c1 2 1 2 3 3l-1 1c-2 3-2 3-5 4-2 0-2-2-3-3v-1c0-1-1-2-2-3h3c0 1 0 1 1 2v-1c1 0 3 0 4-1v-1z" class="U"></path><path d="M329 592v-1c1-1 1-1 2-1 1 1 1 2 2 3 1-1 2-1 3-2h0 1c-2 3-2 3-5 4-2 0-2-2-3-3z" class="G"></path><path d="M334 570c0 1 1 2 2 3 1 2 3 3 4 4l2 1 7 5v1c4 2 7 4 12 5-1 1-2 2-3 2-1 1-2 2-3 2l-2-1c-4-1-8-4-11-6-1-1-2-2-2-3h0l-3-3 1-2c-1 0-2-1-2-1-2-2-3-4-4-5v-1c0-1 1-1 2-1z" class="T"></path><path d="M337 580l1-2c2 3 5 5 8 7 0 1 1 1 1 2l1 1h-1c-2-1-5-3-7-5h0l-3-3z" class="P"></path><path d="M347 584v1h1c0-1 0-1 1-1 4 2 7 4 12 5-1 1-2 2-3 2-4-1-7-2-11-4 0-1-1-1-1-2l1-1z" class="F"></path><path d="M334 570c0 1 1 2 2 3 1 2 3 3 4 4l2 1 7 5v1c-1 0-1 0-1 1h-1v-1l-1 1c-3-2-6-4-8-7-1 0-2-1-2-1-2-2-3-4-4-5v-1c0-1 1-1 2-1z" class="H"></path><path d="M340 577l2 1 7 5v1c-1 0-1 0-1 1h-1v-1h-1c-2-2-4-4-6-7z" class="Y"></path><path d="M131 306l2 4h1c-1-2-2-3-2-5-1-3-5-3-3-6 1 0 3 1 4 2 3 3 4 7 5 11 0 3-1 6-3 9v1-1c3-1 4-2 7-1h0-1c-1 1-2 1-4 1v1h4c1 0 2 0 2-1h2c3 0 6 2 8 4 3 3 4 7 4 11s-2 8-6 11c-1 2-3 2-5 3v2c-1 0-3-2-4-3s-1-4-1-5c1-2 2-3 4-4 1 0 1-1 2-1 0 1 1 1 2 1 0-3-1-6-2-9-1-2-3-3-5-4-3-1-6 0-9 1-4 3-6 7-8 12h0v-1s-1 0-1-1c0-2 2-6 3-7l-1-2c1-1 1-3 2-4 0-1 1-2 2-3s2-3 3-5 1-4 0-6c-2-5-5-3-9-5-1-1-1-1-1-2v-1s2 0 3 1h2c1 0 1 1 2 1 0 1 0 1 1 1h0z" class="C"></path><path d="M147 339c0 1 1 1 2 1 0 1-1 1 0 2l-1 1c-2 0-3 1-4 1v-1c0-1 1-2 2-3 1 0 1 0 1-1z" class="G"></path><path d="M149 342c1-2 1-5 0-7 0-3-1-4-2-6v-1c2 2 3 5 3 7 1 4 1 7-2 11 0 1-1 2-3 2 0-1-1-1-1-2v-1c0-1 1-1 1-1 1 0 2-1 3-1l1-1z" class="m"></path><path d="M146 324c3 1 5 3 7 6 1 4 1 8 0 12v1h-1c0-6 0-11-4-15l-3-3 1-1z" class="H"></path><path d="M127 331v-1c2-3 5-6 9-7 1 0 3-1 5-1l5 2-1 1c-4-1-8-1-12 2-5 2-7 7-8 12 0 0-1 0-1-1 0-2 2-6 3-7z" class="G"></path><path d="M258 405h1v1h1c1 1 1 1 2 1l1 2h0l1-1 1 1c1 1 1 2 1 4h0c0-1 1-2 1-3h1v3c1 0 1 0 1-1 1 1 1 2 2 3v3 3c-1 0-1 0-1 1v3h1v3l1-1h0c0-1 0-1 1-2v3c0 2-1 3 0 5 1 0 1 1 1 2v1c1 1 1 2 1 3h-1v2h0l1 1-1 2 1 1v2l-1 1h-2v-1c-2 0-3-1-4-2h-1c-1-2-1-3 0-6h1l-1-2h-1v1h-1c0 1 0 1-1 2 0 1 0 1-1 2h-2v2l-1 1v-1h-3c-1-1-2-1-2-3l-1-1c0-2-1-5-1-8 1 1 1 1 2 1 0-2-1-4-1-5l1-1 1-3v-4c0-2 0-4 1-6v-4l1-1v-4z" class="Y"></path><path d="M267 423v3h1 1c0-1-1-2-1-3 1-1 1-1 1-2v6c0 3 0 7 1 10v1h-1c0-3 0-6-1-9h-1l-1 4c0-3 0-6 1-10z" class="O"></path><path d="M262 426c1-1 1-2 2-3h0c0 1 0 2 1 3 0 1-1 3-1 4h0-1l-2 1c0-1-1 0-2-1v-2-2l1 1h1c1 0 1 0 1-1h0z" class="P"></path><path d="M259 428l1-1v1c1 1 2 2 4 2h0-1l-2 1c0-1-1 0-2-1v-2z" class="L"></path><path d="M267 410h1v3c1 0 1 0 1-1 1 1 1 2 2 3v3c-1 1-1 2-2 3h0 0c0 1 0 1-1 2 0 1 1 2 1 3h-1-1v-3c0-1 0-2 1-3h-1c0-2 1-3 1-5-1 1-1 1-2 1v-3c0-1 1-2 1-3z" class="M"></path><path d="M268 420l2-5c0 2-1 4-1 6h0c0 1 0 1-1 2 0 1 1 2 1 3h-1-1v-3c0-1 0-2 1-3z" class="C"></path><path d="M271 418v3c-1 0-1 0-1 1v3h1v3l1-1h0c0-1 0-1 1-2v3c0 2-1 3 0 5 1 0 1 1 1 2v1h-2v2c-1 1-1 2-1 3-1-1-1-2-1-3v-1c-1-3-1-7-1-10v-6h0 0c1-1 1-2 2-3z" class="L"></path><path d="M272 438v-2h2c1 1 1 2 1 3h-1v2h0l1 1-1 2 1 1v2l-1 1h-2v-1c-2 0-3-1-4-2h-1c-1-2-1-3 0-6h1v1h1v-2h1c0 1 0 2 1 3 0-1 0-2 1-3z" class="I"></path><path d="M272 438v-2h2c1 1 1 2 1 3h-1v2h0l1 1-1 2c-1-2-2-4-2-6z" class="F"></path><path d="M264 408l1 1c1 1 1 2 1 4h0v3c1 0 1 0 2-1 0 2-1 3-1 5-1 2-2 4-2 6-1-1-1-2-1-3h0c-1 1-1 2-2 3h0c-1-1 0-4 0-5-1-2-1-4-2-5v-3h-1c0-1 1-2 1-4 0 1 0 1 1 1l2-1h0l1-1z" class="E"></path><path d="M263 421v1h1v1h0c-1 1-1 2-2 3v-4c0-1 0-1 1-1z" class="J"></path><path d="M265 409c1 1 1 2 1 4l-2 4c-1-2 0-4 0-5h0v-2l1-1z" class="H"></path><path d="M264 408l1 1-1 1v2h0l-1-1c-1 1-1 2-1 2h-1v-2h-1v2h-1c0-1 1-2 1-4 0 1 0 1 1 1l2-1h0l1-1z" class="d"></path><path d="M266 413h0v3c1 0 1 0 2-1 0 2-1 3-1 5-1 2-2 4-2 6-1-1-1-2-1-3v-1h-1v-1c0-2 0-3 1-4l2-4z" class="I"></path><path d="M258 405h1v1h1c1 1 1 1 2 1l1 2-2 1c-1 0-1 0-1-1 0 2-1 3-1 4h1v3c1 1 1 3 2 5 0 1-1 4 0 5 0 1 0 1-1 1h-1l-1-1v2s0 1-1 2h-1-2v-3l1-3v-4c0-2 0-4 1-6v-4l1-1v-4z" class="K"></path><path d="M260 425c-1-1-2-1-2-2v-1c-1 0-1-1-1-1h1l1 1h1v3z" class="U"></path><path d="M256 424h1v6h-2v-3l1-3z" class="P"></path><path d="M260 416c1 1 1 3 2 5 0 1-1 4 0 5 0 1 0 1-1 1h-1l-1-1h1v-1-3-1c1-2 0-3 0-5z" class="n"></path><path d="M258 405h1v1h1c1 1 1 1 2 1l1 2-2 1c-1 0-1 0-1-1h-1c-1 2-1 3-1 4v2h-1v-1-4l1-1v-4z" class="Y"></path><path d="M254 428l1-1v3h2 1c1-1 1-2 1-2v2c1 1 2 0 2 1-1 1 0 2 0 3s0 2 1 2h3v2c0 1 0 1-1 2 0 1 0 1-1 2h-2v2l-1 1v-1h-3c-1-1-2-1-2-3l-1-1c0-2-1-5-1-8 1 1 1 1 2 1 0-2-1-4-1-5z" class="X"></path><path d="M260 441h0c-1 1-1 1-2 1v-1c0-1 1-1 1-2l-1-1v1l-1-1c1-1 1-2 2-2 0 2 1 3 1 5z" class="h"></path><path d="M257 430h1c1-1 1-2 1-2v2c1 1 2 0 2 1-1 1 0 2 0 3l-1-1h-1l-1 1v-2c-1 0-2-1-3-2h2z" class="k"></path><path d="M259 433h1l1 1c0 1 0 2 1 2h3v2c0 1 0 1-1 2 0 1 0 1-1 2h-2l-1-1c0-2-1-3-1-5v-3z" class="I"></path><path d="M298 421v2h1c1-1 1-2 2-4v-2h1c1 2-1 4 0 5h1 1 1c-1 1-2 1-2 3h0c1 0 1-1 2-1h0v1l2-1v1s0 1-1 1c-1 1-1 3-2 4 0 2-1 5-1 8 1-2 2-7 3-8 0 3-1 5-1 8-1 2-1 4-2 5v5h1 1c0 2-1 4-2 6h0c0-1-1-1-1-2h-1l-1 1h0c-1 1-1 2-1 4v1 2c-1-1-1-1-1-2l-2 2v-1c-2 2 0 3-2 5v-3l-1 1h-1v-2l-1 1h0c0 1 0 2-1 3-1-1-1-2-1-4h1v-4l-2-2-1 2-1-1c1-1 1-2 1-2l1-2h-1l-1-2h2v-3l-1-5c-1-1-1-2-1-3h-1l1-1h0c0-1 0-1 1-2v-1c0-1 1-2 1-3s0-1 1-2c0-3-1-6 0-8v-1s-1-1-1-2h0c1 0 2 1 2 2l3 3 1 1 1-1h3v-2z" class="W"></path><path d="M295 423h3c-1 2-1 2-2 3-1-1-1-2-1-3z" class="P"></path><path d="M304 430c0 2-1 5-1 8 1-2 2-7 3-8 0 3-1 5-1 8-1 2-1 4-2 5v5h1 1c0 2-1 4-2 6h0c0-1-1-1-1-2h-1l-1 1h0v-6c-1 0-1 1-2 1v-2-4h-1v-2c-1 1-1 1-2 1v-4c1-2 1-4 2-6v1 6l2-6v4h0l2-3c0 2-1 5 0 7 1-1 0-2 1-3 0-2 1-5 2-7h0z" class="X"></path><path d="M298 442h1v1h1l1-1c1 1 1 3 1 4 0 2-1 4-1 6l-1 1h0v-6c-1 0-1 1-2 1v-2-4z" class="Q"></path><path d="M289 429h0s1 0 1-1 0-1 1-2c1 1 0 2 0 3 1 0 1 0 1 1l1 1c0 2 0 4-1 6v4c1 0 1-1 1-1v-1c1-2 0-5 1-6 1 1 1 3 1 4v4c1 0 1 0 2-1v2h1v4h-1v2l-1-1v-3h-1c0 1 0 2-1 3v-3c-1 1-1 3-2 4-1-1-1-2-1-3-1 0-1 1-2 2 0-1 0-3-1-4 0-2-1-4-2-6 0-1 0-1 1-2v-1c0-1 1-2 1-3s0-1 1-2z" class="B"></path><path d="M287 441c-1-1-1-2-1-3h-1l1-1h0c1 2 2 4 2 6 1 1 1 3 1 4 1-1 1-2 2-2 0 1 0 2 1 3 1-1 1-3 2-4v3c1-1 1-2 1-3h1v3l1 1v-2h1v2c1 0 1-1 2-1v6c-1 1-1 2-1 4v1 2c-1-1-1-1-1-2l-2 2v-1c-2 2 0 3-2 5v-3l-1 1h-1v-2l-1 1h0c0 1 0 2-1 3-1-1-1-2-1-4h1v-4l-2-2-1 2-1-1c1-1 1-2 1-2l1-2h-1l-1-2h2v-3l-1-5z" class="D"></path><path d="M288 451c0-1 0-2 1-2v-1c1 1 1 1 1 2h1v-1h1v1c0 1 0 1 1 2v5c-1 1-2 3-3 3v-4l-2-2-1 2-1-1c1-1 1-2 1-2l1-2z" class="I"></path><path d="M297 448v-2h1v2c1 0 1-1 2-1v6c-1 1-1 2-1 4v1 2c-1-1-1-1-1-2l-2 2v-1h0c0-1 0-2-1-3 0-1 0-2-1-3 0-1 1-3 2-4v-1h1z" class="i"></path><path d="M298 448c1 0 1-1 2-1v6c-1 1-1 2-1 4v1 2c-1-1-1-1-1-2-1-1 0-4 0-6v-4z" class="I"></path><path d="M296 546c3 2 4 6 4 10 1-1 1-1 1-3h1l1 6c0-1 0-1 1-2h0v1h1c0 1 1 1 1 2v2l3 1c-1 1-3 3-5 4l1 1c0 2 1 2 1 4h1l1-2v1l-1 1 3 3-1 1c-2 1-3 2-4 4l-5 4c-5 4-9 8-14 10-2 1-4 2-6 2-3 1-6 2-9 2v1c-1-1-1-1-3-2l-5-2h0c-1 0-1 0-2-1h1v-1-1c1 1 1 1 2 1h2c2 0 4 1 6 0h2v-1h2 0c1 0 2-1 3-2h2l1-1h1l1-1c1 0 1 0 2-1l1-2c1 0 2-1 2-1 1-1 1 0 1-1 1-1 3-2 3-4h-1v-1c0-1 1-2 2-3h-2l1-1h0v-1l-1-1c-1 1-2 1-3 2 0-2 1-2 0-3h0v-1c3-3 4-6 6-11-1-1 0-3-1-4v-2h1v-3c1 0 1-1 1-2v-1-1z" class="j"></path><path d="M298 561l2 1c0 2 0 4-1 6v-4h-1v-3z" class="d"></path><path d="M299 564v4 2h-1v-2-2l1-2z" class="L"></path><path d="M301 562c0 1 0 2 1 3v-1h0l-1 7h-1v-1c1-2 1-5 1-8z" class="H"></path><path d="M298 556v5 3h1l-1 2h-2v2 1c-1-2-1-2 0-4 0-2 1-3 2-5v-4z" class="a"></path><path d="M262 592c1 1 1 1 2 1h2l3 1c1 0 2 0 2 1h-6-2 0 0c-1 0-1 0-2-1h1v-1-1z" class="F"></path><path d="M301 553h1l1 6c-1 2 0 4-1 5h0v1c-1-1-1-2-1-3 0-2 0-4-1-6 1-1 1-1 1-3z" class="B"></path><path d="M296 548c1 3 2 5 2 8v4c0-2-2-4-2-6 0-1 0-1-1-1v-3c1 0 1-1 1-2z" class="h"></path><path d="M296 569h0c0 2 0 5-2 6h-2l1-1h0v-1l-1-1s1-1 2-1c0-1 1-2 2-2z" class="F"></path><path d="M300 570v1h1c-1 5-3 9-7 12-1 2-3 3-4 4h-1 0-2 0s1-1 2-1c1-1 4-4 5-6 2-2 4-4 5-7 0-1 1-2 1-3z" class="L"></path><path d="M295 553c1 0 1 0 1 1 0 2 2 4 2 6-1 2-2 3-2 5-1 2-1 2 0 4h0c-1 0-2 1-2 2-1 0-2 1-2 1-1 1-2 1-3 2 0-2 1-2 0-3h0v-1c3-3 4-6 6-11-1-1 0-3-1-4v-2h1z" class="O"></path><path d="M289 571c2-1 3-1 5 0-1 0-2 1-2 1-1 1-2 1-3 2 0-2 1-2 0-3h0z" class="U"></path><path d="M295 553c1 0 1 0 1 1 0 2 2 4 2 6-1 2-2 3-2 5v-2c-1 2-2 4-3 5v-1c0-1 1-4 2-5v-3c-1-1 0-3-1-4v-2h1z" class="p"></path><path d="M294 587c2 0 5-4 6-5 0 1-1 1 0 2-5 4-9 8-14 10-2 1-4 2-6 2-3 1-6 2-9 2v1c-1-1-1-1-3-2l-5-2h0 2 5c5 0 9-1 14-3 2-1 3-3 5-3v-1c1 0 2 0 3-1h2z" class="F"></path><path d="M289 588c1 0 2 0 3-1h2c-8 5-16 9-26 10l-5-2h0 2 5c5 0 9-1 14-3 2-1 3-3 5-3v-1z" class="e"></path><defs><linearGradient id="K" x1="300.529" y1="561.673" x2="304.901" y2="567.429" xlink:href="#B"><stop offset="0" stop-color="#232324"></stop><stop offset="1" stop-color="#474445"></stop></linearGradient></defs><path fill="url(#K)" d="M303 559c0-1 0-1 1-2h0v1h1c0 1 1 1 1 2v2l3 1c-1 1-3 3-5 4l1 1c0 2 1 2 1 4h1l1-2v1l-1 1 3 3-1 1c-2 1-3 2-4 4l-5 4c-1-1 0-1 0-2-1 1-4 5-6 5h-2c-1 1-2 1-3 1l1-1c1-1 3-2 4-4 4-3 6-7 7-12l1-7c1-1 0-3 1-5z"></path><path d="M304 558h1c0 1 1 1 1 2v2l3 1c-1 1-3 3-5 4 1-3 1-6 0-9z" class="G"></path><path d="M300 582l5-9h1c1 1 0 1 0 2l1 1v-2l2 2c-2 1-3 2-4 4l-5 4c-1-1 0-1 0-2z" class="B"></path><path d="M228 501s0-1 1-1h0c-1-2-1-7-1-9h1c6 2 12 3 18 3l29 5h2c1 1 2 1 3 2l3 1c-1 0-2 0-3-1-1 0-1 0-2 1h0c-1 0-5 0-6-1l-9-1-1 1h0-5c1 2 6 3 8 4h0 0c1 0 1 1 1 1l1 1h-1c3 2 6 3 8 4 5 2 8 5 12 7 2 2 5 3 6 5h1c0 2 1 3 2 4v-1c-2-1-2-1-3-1s-1 0-2-1v1c-3-2-5-4-8-6-1 0-2 0-2-1-1 0-1 0-2-1h-1c0-1 0 0-1-1 0 0-3-2-4-2s-2 0-3-1h-3 0c-7-2-13-5-19-6l-19-5-1-1z" class="Z"></path><path d="M249 498c1 0 3 0 4 1h3c2 0 3 0 5 1h3l-1 1h0-5l-9-1v-2z" class="M"></path><path d="M230 497l-1-1h5c1 1 4 0 5 1h3c1 0 5 1 5 1h2v2l-19-3z" class="g"></path><path d="M228 501l10 2h1c1 0 2 0 4 1h0c3 1 5 2 7 2l4 1c2 1 4 2 6 2s6 3 8 2h0 0s1 0 1 1h1v1h-3 0c-7-2-13-5-19-6l-19-5-1-1z" class="M"></path><path d="M250 502c-2-1-6-2-8-2-5-1-9-1-13-2v-1h1l19 3 9 1c1 2 6 3 8 4h0 0c1 0 1 1 1 1l1 1h-1l-2-1c-1 0-2 1-2 1-1 0-3-1-3-2-2 0-4-1-6 0h0-1l-1 1c-1-1-2-1-3-2h1c0-1 0-1-1-2h1z"></path><path d="M250 502c5 0 11 2 15 4-1 0-2 1-2 1-1 0-3-1-3-2-2 0-4-1-6 0h0-1l-1 1c-1-1-2-1-3-2h1c0-1 0-1-1-2h1z" class="d"></path><path d="M252 506l-1-2h3c2 1 4 0 6 1-2 0-4-1-6 0h0-1l-1 1z" class="L"></path><defs><linearGradient id="L" x1="269.298" y1="521.166" x2="280.685" y2="510.256" xlink:href="#B"><stop offset="0" stop-color="#bfc1bc"></stop><stop offset="1" stop-color="#f4f2f4"></stop></linearGradient></defs><path fill="url(#L)" d="M253 505h1 0c2-1 4 0 6 0 0 1 2 2 3 2 0 0 1-1 2-1l2 1c3 2 6 3 8 4 5 2 8 5 12 7 2 2 5 3 6 5h1c0 2 1 3 2 4v-1c-2-1-2-1-3-1s-1 0-2-1v1c-3-2-5-4-8-6-1 0-2 0-2-1-1 0-1 0-2-1h-1c0-1 0 0-1-1 0 0-3-2-4-2s-2 0-3-1v-1h-1c0-1-1-1-1-1h0 0c-2 1-6-2-8-2h1c-1-1-1-1-2-1l-6-3z"></path><path d="M420 327h1c2 0 3 1 5 1 4 3 9 5 13 9 0 1 1 2 2 3s4 6 6 6c2 1 8 16 8 19 1 1 1 1 0 1l-3-5 6 20-3-4 1 6h-1 0c0-1 0-2-1-3 0-2 0-5-2-7v2h-1v-2h0-1 0c0-1-1-1-1-2l-4-7h0-1c0 1-1 1-2 1v-1-1c-1-3 0-7-2-9h-1l-2-3c-1 0-2-1-3-3l-1-1-1 1h-1c-2-3-6-5-7-8l-2-1-2-2h-1c-1-1-1-2-2-3s-2-2-3-2l6-5z" class="I"></path><path d="M420 330l-1-1h1 1c1 0 2 1 3 1l-1 1c-1 0-2-1-3-1z" class="R"></path><path d="M423 331l1-1c5 3 8 5 12 9 3 2 5 4 7 7-2-1-3-2-5-4l-2-2-4-4c-2-1-3-2-4-2l-1 1h-1l-2-2h1l-2-2z" class="S"></path><path d="M445 352c1 3 4 5 5 8 0 2 1 3 2 5 0 1 0 1 1 2l-1 1c0-2-1-3-2-4s-1-2-2-3c0 1 2 3 2 5h0c1 1 1 2 1 3v1c0 1 1 2 1 3v2h-1v-2h0-1 0c0-1-1-1-1-2l1-1c-1-2-3-4-3-6-1-2-3-4-2-6l1 1h1c-1-1-2-2-2-4h0c0-1-1-2 0-3z" class="U"></path><path d="M440 354c-1-3-4-6-6-8l1-1c2 1 3 3 4 5h1l1 1 1 2h0c1-1 1-1 2-1h1c-1 1 0 2 0 3h0c0 2 1 3 2 4h-1l-1-1c-1 2 1 4 2 6 0 2 2 4 3 6l-1 1-4-7h0-1c0 1-1 1-2 1v-1-1c-1-3 0-7-2-9z" class="D"></path><path d="M442 364c1-1 1-1 1-3h1v3c0 1-1 1-2 1v-1z" class="N"></path><path d="M442 353h0c1-1 1-1 2-1h1c-1 1 0 2 0 3l-1 1c-1-1-2-2-2-3z" class="O"></path><path d="M414 332l6-5 1 2h-1-1l1 1c1 0 2 1 3 1l2 2h-1l2 2h1l1-1c1 0 2 1 4 2l4 4 2 2v1c1 1 2 2 2 3l2 4c-1 0-1 1-1 1l-1-1h-1c-1-2-2-4-4-5l-1 1c2 2 5 5 6 8h-1l-2-3c-1 0-2-1-3-3l-1-1-1 1h-1c-2-3-6-5-7-8l-2-1-2-2h-1c-1-1-1-2-2-3s-2-2-3-2z" class="U"></path><path d="M419 337c0-1 0-1 1-2h-1v-1h1c1 0 2 1 2 2l2 2h1l-1 1s-1 0-1-1l-1 1-2-2h-1z" class="D"></path><path d="M440 350c-1-3-3-6-6-7h0v-1-1c2 1 4 4 6 5h0l2 4c-1 0-1 1-1 1l-1-1z" class="N"></path><path d="M424 340c3 1 6 4 8 6l1 1-1 1h-1c-2-3-6-5-7-8z" class="R"></path><path d="M426 338c-1-1-3-3-5-4 2 0 3 0 4 1h1 1l1-1c1 0 2 1 4 2l4 4 2 2v1c-2-2-4-4-6-5h0v1 1c-1-1-2-2-4-3v1l1 1h0c-2 0-2 0-3-1z" class="V"></path><path d="M414 332l6-5 1 2h-1-1l1 1c1 0 2 1 3 1l2 2h-1l2 2h-1c-1-1-2-1-4-1 2 1 4 3 5 4l-4-2c0-1-1-2-2-2h-1v1h1c-1 1-1 1-1 2-1-1-1-2-2-3s-2-2-3-2z" class="J"></path><path d="M420 330c1 0 2 1 3 1l2 2h-1c-1 0-2-1-3-1h-1v-2z" class="D"></path><path d="M339 401h0c-1-2-2-6-1-8l1 1c1 0 2 1 3 1 0 0 1 0 1 1 2 1 3 1 4 2l3 1c-1 1-1 2-1 4 0 0-1 1-1 2v1c0 1 0 2-1 3v2 2 1h0l-1-1v1c0 1-1 4 0 5h0l-1-1c0 1 0 2-1 3l1 2c-1 1-1 2-1 3h-2l-2 1v1c0 1 0 2-1 3l-1 2h-1l1-1-1-1c0 1-1 2-1 4h0l-1 1c1 1 2 2 2 3 0 0-1 1-1 2-1 0-1 1-2 1h-2l-1-1-1 3c-1-1 0-2-1-4l-1 3v-2-1h0l-2 2v-2l1-1h0c-1-1-1-2-2-2v-1h0v-1c0-1 0-1 1-2v-1h-1l-2 2-1-2 1-1c-1-1-1-1 0-2l-1-1c2-3 3-6 5-8l2-4c2-3 3-7 4-10v8c1-3 2-6 4-9 1-1 1-2 2-4z" class="a"></path><path d="M336 423v-1-6-10h1v8c0 3 1 6 1 9v2 4 3l-1-1v-4c0-1-1-1-1-1v-3z" class="B"></path><path d="M332 417c0 1 0 5 1 6 1-2 0-6 0-9 2 1 2 7 3 9v3s1 0 1 1v4h-2c-1-1-1-3-1-4h0c0 2 0 3-1 5s-1 2-3 2h0v-10c0-2 0-5 2-7z" class="W"></path><path d="M329 416l1 1 2-2h0v2c-2 2-2 5-2 7v10h0c2 0 2 0 3-2s1-3 1-5h0c0 1 0 3 1 4h2c0 1-1 2-1 4h0l-1 1c1 1 2 2 2 3 0 0-1 1-1 2-1 0-1 1-2 1h-2l-1-1-1 3c-1-1 0-2-1-4l-1 3v-2-1h0l-2 2v-2l1-1h0c-1-1-1-2-2-2v-1h0v-1c0-1 0-1 1-2v-1h-1l-2 2-1-2 1-1c-1-1-1-1 0-2l-1-1c2-3 3-6 5-8l2-4z" class="l"></path><path d="M335 431h2c0 1-1 2-1 4h-2c0-2 0-3 1-4z" class="B"></path><path d="M334 435h2 0l-1 1c0 1-2 3-2 5l1 1h-2l-1-1c1-1 2-3 2-4s1-2 1-2z" class="j"></path><path d="M335 436c1 1 2 2 2 3 0 0-1 1-1 2-1 0-1 1-2 1l-1-1c0-2 2-4 2-5z" class="E"></path><path d="M327 439c0-2 1-3 1-4 2 1 0 2 2 3 0 1 0 2-1 2l-1 3v-2-1h0l-2 2v-2l1-1h0z" class="V"></path><path d="M323 429v-1c3-1 4-4 5-7 1-1 1-1 1-2v8c0 2 0 6-1 8-1 0-2 1-3 1h0v-1c0-1 0-1 1-2v-1h-1l-2 2-1-2 1-1c-1-1-1-1 0-2z" class="W"></path><path d="M339 401h0c-1-2-2-6-1-8l1 1c1 0 2 1 3 1 0 0 1 0 1 1 2 1 3 1 4 2l3 1c-1 1-1 2-1 4 0 0-1 1-1 2v1c0 1 0 2-1 3v2 2 1h0l-1-1v1c0 1-1 4 0 5h0l-1-1c0 1 0 2-1 3l1 2c-1 1-1 2-1 3h-2l-2 1v1c0 1 0 2-1 3l-1 2h-1l1-1v-3-4c1-2 1-3 1-5v-3c0-3-1-6-1-9 0-1 1-2 1-3v-4z" class="M"></path><path d="M339 417v-2l1-1v1l1 4c-1 0-1 0-2 1v-3z" class="B"></path><path d="M342 426c0-2 0-4 1-6 0 0 0-1 1-1 0-1 0-1 1-1 0 1 0 2-1 3l1 2c-1 1-1 2-1 3h-2z" class="Q"></path><path d="M339 420c1-1 1-1 2-1 0 3 0 5-1 8v1c0 1 0 2-1 3l-1 2h-1l1-1v-3-4c1-2 1-3 1-5z" class="W"></path><path d="M479 587h9c2 0 5 0 6 1 1 0 2 0 3 1s3 2 4 2h1l8 4h2c1 1 1 2 1 4-1 3-6 7-9 8s-5 3-6 6h0v1 4c2 0 2-1 3-1 0 1 0 2-1 3-2 2-5 3-8 5v-1c-1 0-1-1 0-2-2 0-2 1-3 0 0-2 4-3 3-5-2-2-6-4-7-6h0c-1-1-1-2-1-4 2-2 5-3 8-4l-2-6-3 2c-1 0-2 0-3-1h0l-1 1 1 1c2 1 3 0 5-1v1l-1 1h-4c-1 0-2-1-2-2-1-1 0-4 0-5v-1c-3-2-7-2-10-3 1 0 2-1 3-1l-2-1v-1h6z"></path><path d="M483 594l1-1c1 0 3 0 4 1l2 1-1 1c-1 0-5-1-6-2z" class="H"></path><path d="M492 598c1 0 2 0 3 1v2c-1 1-1 1-2 1h0c-1-1-1-3-1-4z" class="F"></path><path d="M493 595h4c-1 1-1 1-2 3 1 1 1 2 0 3h0v-2c-1-1-2-1-3-1h0c0-1 1-1 1-1v-2z" class="I"></path><path d="M493 595c-1-1 0-2 0-3 1-1 0-1 2-2 1 2 2 2 3 4l-1 1h-4z" class="l"></path><path d="M494 604h3v1h-1v1l1-1c1 0 1 0 2 1l-1 1h1 2v-2h2c0-1 1-1 2-1h0c0 1-1 2-2 2-1 1-5 4-7 3-1-1 0 0 0-1s-1-2-2-2v-2z" class="Y"></path><path d="M475 589h1c2 1 4 1 6 1 2-1 3-1 4-1v1h-2c1 1 2 1 3 1 1 1 1 2 1 3-1-1-3-1-4-1l-1 1h-1v-1c-3-2-7-2-10-3 1 0 2-1 3-1z" class="L"></path><path d="M499 592h1c1 0 2 1 3 2l-1 1c0 1 0 1 1 2v-3h1l2 1c1 0 2 0 3 1l-1 2c-2 1-3 2-5 2-2-1-2-2-4-3v-1-1h1 1c-1-1-2-2-2-3z" class="M"></path><path d="M492 617v-1c-1-1-3-2-4-4v-1h1c-2-1-2-1-3-3 1-1 3-1 4-2h0 1c1 1 1 2 1 3 1 1 1 2 2 2s1 1 2 1l-1 1v2h0 1 1l1-1v4c2 0 2-1 3-1 0 1 0 2-1 3-2 2-5 3-8 5v-1c-1 0-1-1 0-2-2 0-2 1-3 0 0-2 4-3 3-5z" class="R"></path><path d="M433 347l1 1c1 2 2 3 3 3l2 3h1c2 2 1 6 2 9v1 1c1 0 2 0 2-1h1 0l4 7c0 1 1 1 1 2h0 1 0v2h1v-2c2 2 2 5 2 7l1 3c0 3 1 6 1 8l-1 1v-1c-1 0-1 0-1 1s-1 1-1 2c-1 0-1-1-1-1l-2 1h0c-1-1-1-2-2-3l-1-1c1 2 2 4 3 5 0 3 1 4 2 6v1c1 0 1 1 1 2 0 2 1 4 1 6-2-1-4-3-5-5l-3-3h0c0-4-1-7-2-10s-3-5-3-8c-1-1-1-2-1-2-1-1-1-2-2-3s-2-2-2-3l-3-6h-1 0c-1 0-2 0-2 1-1-1-1-3-1-4-1-1 0-2-1-3 0-1-1-1-1-1l-1-4h0 2 2 0c1 1 1 1 2 1-1-1-1-2-1-4v-1l1-1c-1-2-2-1-3-1l-2-2h2l1-1v-1l1-1h1l1-1z" class="g"></path><path d="M440 373c1 0 2 1 2 2l2 1c0 1 1 4 1 4l-1 1v2c1 2 2 3 2 5l-2-2v-2l-1-1c0-1-1-1-1-2l-1-4c-1-1-2-3-1-4z" class="Z"></path><path d="M438 365c-1-1-1-2-1-3l2 3c1 0 1 1 2 1l1 1 1 1c1 3 1 6 2 9 0 1 0 2 1 3l2 7h0c-2-2-2-5-3-7 0 0-1-3-1-4l-2-1c0-1-1-2-2-2-1-3-3-6-5-8h0 3z" class="J"></path><path d="M435 365h3l5 9c0 1 1 1 1 2l-2-1c0-1-1-2-2-2-1-3-3-6-5-8h0z" class="R"></path><path d="M444 384v2l2 2s1 0 1 1v1h0c1 2 2 4 3 5 0 3 1 4 2 6v1c1 0 1 1 1 2 0 2 1 4 1 6-2-1-4-3-5-5v-1h1c-3-3-2-5-4-9l-1-5v-1c-1 0-1-1-1-2v-1h-1c0-1 0-1 1-2z" class="S"></path><path d="M444 384v2l2 2s1 0 1 1v1h0c1 2 2 4 3 5 0 3 1 4 2 6v1 1c-1 0-1-1-2-2 0 0-1-1-1-2l-1-1c-1-2-1-5-3-8v-1c-1 0-1-1-1-2v-1h-1c0-1 0-1 1-2z" class="P"></path><path d="M428 359h2 0-1c0 1 0 1 1 1 0 1 0 2 1 3 1 0 1 0 2 1h2v1h0v1c0 2 1 3 1 5 1 1 2 3 3 5 0 1 1 2 1 4 1 1 1 3 1 4-1-1-1-2-1-2-1-1-1-2-2-3s-2-2-2-3l-3-6h-1 0c-1 0-2 0-2 1-1-1-1-3-1-4-1-1 0-2-1-3 0-1-1-1-1-1l-1-4h0 2z" class="X"></path><g class="U"><path d="M431 363c1 0 1 0 2 1h2v1h0v1c0 2 1 3 1 5-2-3-4-5-5-8z"></path><path d="M429 367c-1-1 0-2-1-3 0-1-1-1-1-1l-1-4h0 2v3l1 1c1 0 1 1 1 2h0v1h1 1v1c1 2 2 3 3 4v1c0 1 1 1 1 2v1h1c0 2 2 4 3 5s1 3 1 4c-1-1-1-2-1-2-1-1-1-2-2-3s-2-2-2-3l-3-6h-1 0c-1 0-2 0-2 1-1-1-1-3-1-4z"></path></g><path d="M429 367h1c1 1 1 1 1 2l1 1c-1 0-2 0-2 1-1-1-1-3-1-4z" class="a"></path><path d="M433 347l1 1c1 2 2 3 3 3l2 3h1c2 2 1 6 2 9v1 1 2l-1-1c-1 0-1-1-2-1l-2-3c0 1 0 2 1 3h-3v-1h-2c-1-1-1-1-2-1-1-1-1-2-1-3-1 0-1 0-1-1h1c1 1 1 1 2 1-1-1-1-2-1-4v-1l1-1c-1-2-2-1-3-1l-2-2h2l1-1v-1l1-1h1l1-1z" class="K"></path><path d="M437 356l3 6c0 1 0 3 1 4-1 0-1-1-2-1 0-3-2-6-3-8l1-1z" class="D"></path><path d="M433 347l1 1c1 2 2 3 3 3l2 3v2 1c-1-1-3-2-4-4v1l2 2-1 1c-2-2-4-5-5-7h-1v-1l1-1h1l1-1z" class="p"></path><path d="M437 351l2 3v2c-1-1-3-2-3-4l1-1z" class="S"></path><path d="M433 347l1 1c1 2 2 3 3 3l-1 1c-1-1-1-1-2-1h-1c-1 0-1-1-1-1h-1-1v-1l1-1h1l1-1z" class="X"></path><path d="M433 347l1 1-2 2h-1-1v-1l1-1h1l1-1z" class="c"></path><path d="M430 350h1c1 2 3 5 5 7 1 2 3 5 3 8l-2-3c0 1 0 2 1 3h-3v-1h-2c-1-1-1-1-2-1-1-1-1-2-1-3-1 0-1 0-1-1h1c1 1 1 1 2 1-1-1-1-2-1-4v-1l1-1c-1-2-2-1-3-1l-2-2h2l1-1z" class="D"></path><path d="M431 363c-1-1-1-2-1-3-1 0-1 0-1-1h1c1 1 1 1 2 1l3 4h-2c-1-1-1-1-2-1z" class="K"></path><path d="M430 350h1c1 2 3 5 5 7 1 2 3 5 3 8l-2-3c0-1-2-4-3-5h-1l-2-1v-1l1-1c-1-2-2-1-3-1l-2-2h2l1-1z" class="a"></path><path d="M432 354l2 3h-1l-2-1v-1l1-1z" class="T"></path><path d="M444 364h1 0l4 7c0 1 1 1 1 2h0 1 0v2h1v-2c2 2 2 5 2 7l1 3c0 3 1 6 1 8l-1 1v-1c-1 0-1 0-1 1s-1 1-1 2c-1 0-1-1-1-1l-2 1h0c-1-1-1-2-2-3l-1-1h0v-1c0-1-1-1-1-1 0-2-1-3-2-5v-2l1-1c1 2 1 5 3 7h0l-2-7c-1-1-1-2-1-3-1-3-1-6-2-9l-1-1v-2c1 0 2 0 2-1z" class="d"></path><path d="M450 373h1 0v2h1v-2c2 2 2 5 2 7l1 3c0 3 1 6 1 8l-1 1v-1c-1 0-1 0-1 1s-1 1-1 2c-1 0-1-1-1-1-1-2-2-2-2-3v-1h1s0 1 1 2h0c1-2 1-4 1-5 0-3-3-7-3-9 0-1 1-2 1-2h-1v-1-1z" class="Z"></path><path d="M450 373h1 0v2h1v-2c2 2 2 5 2 7l1 3c-2-2-3-5-4-8h-1v-1-1z" class="T"></path><path d="M444 364h1 0l4 7c0 1 1 1 1 2h0v1 1h1-2v-1h-1c0 1 0 1 1 2v3c1 1 2 4 2 6h0c-1-2-1-3-2-4v-1c-1 0-1 0-1 1 0 0 1 0 1 1h0c0 2 1 2 1 4h-1c-1-2-1-5-2-7-1 0-1 0-1 1-1-1-1-2-1-3-1-3-1-6-2-9l-1-1v-2c1 0 2 0 2-1z" class="Z"></path><path d="M444 364h1v5l1 1 1 3h-1c-1-2-1-4-2-5h-1l-1-1v-2c1 0 2 0 2-1z" class="c"></path><path d="M137 533c1 0 2 0 3 1l2-1 1 1c2 1 3 2 5 3 3 3 5 7 6 10v1 2h0l-2 11-2-2c0-1 0-2-1-4v-3h-1v1h-1-1s-1-1-1-2c0 1-1 1-1 2h-2c0 1 0 1 1 2l-1 3c0 1 0 2-1 2 0 2-1 3-2 4-1 0-3 0-4-1-1 0-1 0-1-1l2-3v-1-2c1-2 1-2 0-4h-2c1-1 1-1 1-2-1-1-1-2-3-3-1 0-2 0-4 1-1 1-3 2-4 3h-1v-1c-1 0-1-1-3-2-1 0-1 0-2-1h0l-2 1c0-1-1-2-1-3h0l2-2h-3c0-1 0-1 1-2h0c-1-2-1-1-2-1h-2 0c8-6 15-7 24-6l2-1z" class="B"></path><path d="M130 538h1c0 1 1 2 1 3h3c1 1 2 1 3 3 0 1 1 1 1 2-1 0-1-1-1-2h0c-3-2-6-2-9-2l-2-2 1-1h1l1-1z" class="J"></path><path d="M128 546c2-1 5-1 7 0 1 0 2 1 4 1v1c-1 1-1 2-2 2l-1 1-1-1c-1-1-1-2-3-3-1 0-2 0-4 1-1 1-3 2-4 3h-1v-1c1-1 3-2 5-3v-1z" class="O"></path><path d="M138 559l-1-1c2-2 1-6 2-9 1-1 1-1 2-1 2-1 4 0 6 1 0 1 1 1 1 2l1 1h-1v1h-1-1s-1-1-1-2c-1 0-2-1-3-1s-2 1-2 2c-1 2 0 5-1 7h-1z" class="E"></path><path d="M138 559h1c1-2 0-5 1-7 0-1 1-2 2-2s2 1 3 1c0 1-1 1-1 2h-2c0 1 0 1 1 2l-1 3c0 1 0 2-1 2 0 2-1 3-2 4-1 0-3 0-4-1-1 0-1 0-1-1l2-3h2z" class="Q"></path><path d="M115 541c1 1 4 1 6 0l3-3c2 0 3-1 5-1l-5 4c0 1 0 3 1 4l3 1v1c-2 1-4 2-5 3-1 0-1-1-3-2-1 0-1 0-2-1h0l-2 1c0-1-1-2-1-3h0l2-2h-3c0-1 0-1 1-2z" class="E"></path><path d="M123 550c0-1-1-1-1-2v-1s1-1 2-1 3 0 4 1c-2 1-4 2-5 3zm-6-7h1c1 0 2 0 3-1 1 0 2 2 3 3l-4 2c-1 0-2-1-3-1s-1-1-2-1l2-2z" class="B"></path><path d="M137 533c1 0 2 0 3 1l2-1 1 1c2 1 3 2 5 3 3 3 5 7 6 10v1 2h0l-2 11-2-2c0-1 0-2-1-4v-3l-1-1c0-1-1-1-1-2v-2c-1-3-5-5-9-7-3-2-7-3-10-4-2-1-6 1-8 2-2 0-4 1-5 3-1-2-1-1-2-1h-2 0c8-6 15-7 24-6l2-1z" class="O"></path><path d="M138 540c0-1 1-1 0-2s-4-2-5-2h-1-1-1c1-1 3-1 4-1h1c1 1 2 1 3 2 1 0 2 2 3 2 2 1 3 2 3 3 1 1 2 1 3 1h1s2 2 2 3c1 3 2 5 2 7v1s-1 0-1 1v-1 2h-1s0-1-1-1v-3l-1-1c0-1-1-1-1-2v-2c-1-3-5-5-9-7z" class="c"></path><path d="M150 546c1 3 2 5 2 7v1s-1 0-1 1v-1c-1-1-1-3-1-5v-3z" class="C"></path><path d="M137 533c1 0 2 0 3 1l2-1 1 1c2 1 3 2 5 3 3 3 5 7 6 10v1 2h0l-2 11-2-2c0-1 0-2-1-4 1 0 1 1 1 1h1v-2 1c0-1 1-1 1-1v-1c0-2-1-4-2-7 0-1-2-3-2-3-3-4-8-8-13-9l2-1z" class="g"></path><path d="M142 533l1 1c2 1 3 2 5 3l-1 1c-2 0-5-3-7-4l2-1z" class="M"></path><path d="M152 553h1c0-2-1-5-2-7 0-1-1-3-1-4 2 2 2 6 4 7v1l-2 11-2-2c0-1 0-2-1-4 1 0 1 1 1 1h1v-2 1c0-1 1-1 1-1v-1z" class="O"></path><path d="M350 340l6 1-5 19c-2 0-3 1-4 1l3-9c2-3 4-6 5-10h-1l-8 19c-2 1-5 1-7 2h6c-2 9-5 17-5 25l1 1 1-11 2-10c1-2 1-3 2-5l-1 29h0c-7-3-14-6-20-8h-1c4-12 9-25 17-35 3-4 5-7 9-9zm135 179c1-2 0-3 0-5v-3l1 1h1v-3h0v-1c0 1-1 1-1 2 0-7 0-13-1-20 0-6-1-13-2-19 0-3-1-6-2-9v-2s-1-2-1-3l1 3h1v-1c-1-2 0-4 1-6l3 6s1 1 1 2c0 0-1 0-1 1s-1 2-1 3-1 1-1 2c0 2 0 3 1 5v1l1 1h-1c0 1 0 3 1 4 0 2 0 4 1 6 0 2 1 6 0 8v1h0l1 1v-2c1-1 1-2 1-4h0c1-2 1-4 2-6 0-3 1-5 2-7l4 3c2 0 4-1 6-1-1 1-1 3-2 4l-3 3c-3 3-6 7-7 10-2 4-2 8-3 11l2-3c1-3 4-4 6-6l1 1c-2 2-4 3-5 5s-2 3-2 5v1c-1 1-1 1-1 2 1 0 3-2 5-3 0 0 1-1 2-1 2 0 3 0 5 1l1 1c0 3-4 5-3 8h0c-1 0-1 1-2 2l-1 3-3 6 1 1c-2 2-3 4-4 6s-2 4-2 6v1c-3 4-5 11-9 13v-2c0-1 1-2 1-2 1-2 2-3 3-4v-1c-1 0-1 1-2 1-2 0-3 0-4-1l1-1h0c1 1 2 1 3 1l2-2-1-1h-1l-1 2h-1v-1c1-1 2-2 2-3l-1-1c0-2 0-4 1-6 0-1 1-1 1-2s1-2 1-3v-1c0-1 0-2 1-2v-2-2c1-1 0-1 1-2z"></path><path d="M495 507v2l-2 4c-1-1-1-1-2-1l-1-1c0-1 3-1 3-2s1-1 2-2z" class="L"></path><path d="M490 486h1c1 0 1-1 2-1v2l-4 7c0-1 0-2 1-2v-6z" class="M"></path><path d="M490 486c0-1 1-4 2-6 1-1 1-1 3-2v1c1 2-2 3-2 5v1c-1 0-1 1-2 1h-1z" class="G"></path><path d="M493 485c1-2 3-5 5-6h1l1 1c-1 2-5 5-7 7v-2zm0 31l2 1v2c-1 1-2 3-3 4s-1 3-2 4h-1l-1-1 1-2v-3c1 0 1 1 2 1h0v-1c1-1 1-2 2-3v-2z" class="B"></path><path d="M489 524l1-1c1-1 1 0 2 0h0c-1 1-1 3-2 4h-1l-1-1 1-2zm-4-5h0l1-1c1 1 1 5 0 6 0 2 1 4 1 6-1-1-1-1-1-2h0-1v1h-1 0v-1-1h-1c0-1 0-2 1-2v-2-2c1-1 0-1 1-2z" class="G"></path><path d="M495 519v-1l1-1h0c1-1 1-2 2-3 0 1 0 2-1 2v2c-1 1-1 2-1 3l-3 6c-1 1-3 2-3 4l-2 1v-2-1h0l1 1 1-1v-2c1-1 1-3 2-4s2-3 3-4z" class="J"></path><path d="M493 527l1 1c-2 2-3 4-4 6s-2 4-2 6v1c-3 4-5 11-9 13v-2h0c3-1 4-5 6-7 0-1 0-1 1-2 0-3 2-6 2-9v-1-1l2-1c0-2 2-3 3-4z" class="d"></path><path d="M496 506c2 0 3 0 5 1l1 1c0 3-4 5-3 8h0c-1 0-1 1-2 2l-1 3c0-1 0-2 1-3v-2c1 0 1-1 1-2-1 1-1 2-2 3h0l-1 1v1-2-1c2 0 2-2 3-3 0-1 0-2 1-3l-1-1h-1-2v-2h1v-1z" class="E"></path><path d="M495 509h2 1l1 1c-1 1-1 2-1 3-1 1-1 3-3 3v1l-2-1v2l-1-1h-3s-1 0-1-1v-2c0-1 0-1 1-1h2v-1c1 0 1 0 2 1l2-4z" class="D"></path><path d="M489 517c0-1 1-2 1-2-1-1-1 0-1-1v-1c2 1 3 2 4 3v2l-1-1h-3z" class="F"></path><path d="M483 527h1v1 1h0 1v-1h1 0c0 1 0 1 1 2 0 1-1 2-1 3v2c0 2-1 5-2 7-1 0-1-1-2-2v-4-1c1 1 2 1 3 1l-2-2c-1 0-1 0-2-1 0-1 1-1 1-2s1-2 1-3v-1z" class="B"></path><path d="M145 551c0 1 1 2 1 2h1 1v-1h1v3c1 2 1 3 1 4l2 2-4 11c-2 4-3 6-5 9-6 6-10 7-18 7-1 0-2 0-2-1h-1v-1h4 0 1l4-4c1-2 1-4 0-6-1-1-2-1-3-1-2 1-5-5-7-6l-4-1c-1 0-1-1-2-2-1 0-1-1-1-2 2-3 6-4 9-5h3c0-2 0-2 1-3 0-1 1-2 2-3 1 0 4-1 5-1h2c1 2 1 2 0 4v2 1l-2 3c0 1 0 1 1 1 1 1 3 1 4 1 1-1 2-2 2-4 1 0 1-1 1-2l1-3c-1-1-1-1-1-2h2c0-1 1-1 1-2z" class="m"></path><path d="M145 551c0 1 1 2 1 2l1 3h-3v-1c1-1 0-1 0-2s1-1 1-2z" class="M"></path><path d="M146 553h1 1v-1h1v3c1 2 1 3 1 4-1 1-1 2-2 2l-1-5-1-3z"></path><path d="M148 561c1 0 1-1 2-2l2 2-4 11h-1c1-4 1-7 1-11z" class="I"></path><path d="M144 553c0 1 1 1 0 2v1c-2 7 0 16-5 23h0v1c-1 1-3 2-4 3-1 0-1 0-2-1 1-2 1-4 1-6v-1c0-2-1-3-1-4l4-2c-1-2 2-4 2-5 1-1 2-2 2-4 1 0 1-1 1-2l1-3c-1-1-1-1-1-2h2z" class="o"></path><path d="M137 569v1 1l-1 1h1s1 0 1-1h1c-1 1-1 3-2 4h-3c0-2-1-3-1-4l4-2z" class="Q"></path><path d="M134 576l4 2-2 2v1c1-1 2-1 3-2v1c-1 1-3 2-4 3-1 0-1 0-2-1 1-2 1-4 1-6z" class="G"></path><path d="M139 564c1-1 2-2 2-4 1 0 1-1 1-2v6c-1 2-2 5-3 7h0-1c0 1-1 1-1 1h-1l1-1v-1-1c-1-2 2-4 2-5z" class="B"></path><path d="M121 561h0c1 1 3 0 4 0l1 1-1 1v1l1 1-1 1h0c1 0 2 0 3 1v1l1 1h0v1 2h1 2 0l1-1c0 1 1 2 1 4v1c0 2 0 4-1 6 0 1 0 3-1 4 1 0 2-1 3-1a25.03 25.03 0 0 0 12-13h1c-2 4-3 6-5 9-6 6-10 7-18 7-1 0-2 0-2-1h-1v-1h4 0 1l4-4c1-2 1-4 0-6-1-1-2-1-3-1-2 1-5-5-7-6l-4-1h1l-1-1c-1 0-1-1-2-2 1-1 3-2 4-3 1 0 1 0 2-1z" class="b"></path><path d="M129 569h0v1 2 1h0c-2-1-3-1-4-2l1-1c1-1 2-1 3-1z" class="H"></path><path d="M121 561h0c1 1 3 0 4 0l1 1-1 1v1c-1 0-2 1-3 1-1-1-1-2-1-4z" class="M"></path><path d="M125 566c1 0 2 0 3 1v1l1 1c-1 0-2 0-3 1l-1 1c-1-1-2-2-3-2v-1c1 0 2-1 2-1l1-1z" class="F"></path><path d="M134 552h2c1 2 1 2 0 4v2 1l-2 3c0 1 0 1 1 1 1 1 3 1 4 1 0 1-3 3-2 5l-4 2-1 1h0-2-1v-2-1h0l-1-1v-1c-1-1-2-1-3-1h0l1-1-1-1v-1l1-1-1-1c-1 0-3 1-4 0h0c-1 1-1 1-2 1-1 1-3 2-4 3 1 1 1 2 2 2l1 1h-1c-1 0-1-1-2-2-1 0-1-1-1-2 2-3 6-4 9-5h3c0-2 0-2 1-3 0-1 1-2 2-3 1 0 4-1 5-1z" class="I"></path><path d="M130 562c-1-1-1-1-2-1l2-2h0-1-1v-3c2-2 5-2 8-3l-1 4-1 1c-1 1 0 1-1 3 0 0-1 1-3 1z" class="L"></path><path d="M133 561c1-2 0-2 1-3h2v1l-2 3c0 1 0 1 1 1 1 1 3 1 4 1 0 1-3 3-2 5l-4 2-1 1h0-2-1v-2-1h0l-1-1v-1c-1-1-2-1-3-1h0l1-1h0c1-1 2-1 3-1 0-1 0-1 1-2 2 0 3-1 3-1z"></path><path d="M133 561v2 1 1c-2 0-3 0-4-1 0-1 0-1 1-2 2 0 3-1 3-1z" class="f"></path><path d="M143 453c5-4 9-7 15-8v5 2 5 1 3c0 3 0 6-1 8v3h0v-1-1l-1-1-1 2 2 2 1 2-1 1-2-2v1c1 1 1 2 2 3h-1c-2-3-4-3-7-5l-1 1c1 1 2 1 3 2l2 2h-1 0-2 0l-1-1c-1-1-1-1-2-1-1-1-2-1-3 0v1h0s0-1-1-1c-2-1-6 0-7 1-2 1-4 3-5 4l-1-1-1 1h0l-2 2c-1 1-1 3-1 4-1 5-1 11 0 17h-2v-4c-1-1-1-1-1-2v-2-3c-1-5 1-11 3-16 0-1 2-6 2-7 3-3 6-6 8-10 3-3 5-6 7-9v2h0z" class="o"></path><path d="M152 468l2 2v1c0 1 3 3 4 4l-1 1-2-2c-2-2-3-3-5-4 1 0 1-1 2-2z" class="k"></path><path d="M145 462c1 0 1-1 2-1h3c1-1 3-1 4 0 1 0 1 1 2 1v2c-4-2-6-3-11-2z" class="K"></path><path d="M158 457c-3-2-5-2-8-1h-2 1c2-1 7-4 9-4v5z" class="Q"></path><path d="M140 471v-1c2-3 8-2 10-3h1l1 1c-1 1-1 2-2 2l-1-1h0-2c-1 0-3 0-4 1h-1l-1 1h-1z" class="L"></path><path d="M140 471h1l1-1h1c1-1 3-1 4-1l1 2c-3 0-4 0-6 2h-1c0 1-2 1-3 1-1 1-3 2-4 2h-1c1-1 1-2 2-2s2-1 3-1 2 0 2-1h0v-1z" class="V"></path><path d="M158 461h0s0-1-1-2v2h0l-3-2c-2-1-5-1-7 0l-3 1c2-2 4-3 6-3 3-1 6-1 8 1v3z" class="G"></path><path d="M147 469h2 0l1 1c2 1 3 2 5 4v1c1 1 1 2 2 3h-1c-2-3-4-3-7-5h-8 1c2-2 3-2 6-2l-1-2z" class="g"></path><path d="M147 469h2 0l1 1c2 1 3 2 5 4v1c-3-2-5-3-7-4l-1-2z" class="E"></path><path d="M145 453c2-3 5-4 8-6h4 0 0v3 1h0c-3 1-7 2-9 4-2 1-4 3-6 4 0-1 0-1-1-2 2-1 3-2 4-3v-1z" class="P"></path><path d="M145 462c5-1 7 0 11 2 1 0 1 0 1 1 0 0 1 1 0 2 0 1-1 1-2 2-1 0-2-2-3-3s-2-1-3-1c-3 1-6 1-9 3-4 2-8 7-11 11 0-1 1-3 2-4 3-6 8-11 14-13z" class="m"></path><defs><linearGradient id="M" x1="139.333" y1="481.45" x2="120.598" y2="484.561" xlink:href="#B"><stop offset="0" stop-color="#d1cdcd"></stop><stop offset="1" stop-color="#fafafc"></stop></linearGradient></defs><path fill="url(#M)" d="M143 451v2h0l-3 3c0 1 0 1-1 2h1 0c1-2 3-4 5-5v1c-1 1-2 2-4 3 1 1 1 1 1 2-6 4-11 11-14 18 0 2-1 4-1 6-1 1-1 3-1 4-1 5-1 11 0 17h-2v-4c-1-1-1-1-1-2v-2-3c-1-5 1-11 3-16 0-1 2-6 2-7 3-3 6-6 8-10 3-3 5-6 7-9z"></path><path d="M397 344h1v-1c1 2 3 3 4 4 1 3 3 6 4 9l1 2c1 1 1 2 2 3 0 1 1 2 1 3h0l2 4c0 3 0 5 1 7 1 0 1 0 1 1 1 2 0 4 1 6l-2-1-4-2-3-2h-2l-16-6-5-2-3-1c1-1 1-3 2-4 0-1 0-1 1-2l-2-4c0-1-2-3-2-4 6-3 12-6 18-10z"></path><path d="M398 361c0-1 1-1 1-2s-1-1 0-2c1 1 1 1 1 2v2c1 0 1 0 1 1v1l1 2v1 1h0v1 6 1c-1-2-1-4-2-6v4 1l-1-1v-7c0-2-1-3-1-5z" class="T"></path><path d="M385 366c0-2 0-6 1-7 1-2 1-5 1-6 2 5 2 11 1 17v1l-5-2h0c1-1 1-2 2-3z" class="c"></path><path d="M385 366h1v1c0 1 0 2 1 2l1 1v1l-5-2h0c1-1 1-2 2-3z" class="J"></path><path d="M399 373v-1h-2c0-1-1-3-1-4s1-2 0-3v2c-1 2 0 4-1 5-1-2 0-6 0-8h-1c-1-2-1-4-1-5 0-2-1-3-1-4 1-3 2-5 3-7l2 1c1 1 2 2 3 2v-1c1 1 2 1 2 2h0c-1 0-1 1-1 1h0l-1-1-1 1c0 1 0 1-1 2v6c0 2 1 3 1 5v7z" class="O"></path><path d="M397 344h1v-1c1 2 3 3 4 4 1 3 3 6 4 9l1 2c1 1 1 2 2 3 0 1 1 2 1 3h0l2 4c0 3 0 5 1 7 1 0 1 0 1 1 1 2 0 4 1 6l-2-1-4-2-3-2h-2l-2-10h0v-1-1l-1-2v-1c0-1 0-1-1-1v-2c0-1 0-1-1-2-1 1 0 1 0 2s-1 1-1 2v-6c1-1 1-1 1-2l1-1 1 1h0s0-1 1-1h0c0-1-1-1-2-2v-2c-1-1-2-3-3-4z" class="a"></path><path d="M413 381h1l-2-2c1-1 1-2 2-3 1 2 0 4 1 6l-2-1z" class="d"></path><path d="M405 366l1 1h0c1-1 1-1 0-2v-2h0 1c0 2 1 6 2 7 1-2 0-3 0-4h0c1 1 1 2 1 3l1 1v5h-1l-1-1v1 4l-3-2v-5c-1-1 0-2 0-3-1-1-1-2-1-3z" class="U"></path><path d="M398 361v-6c1-1 1-1 1-2l1-1 1 1h0l2 2h0l-1 1v-1h-1c0 2 2 2 2 4h0c0 1 1 1 1 2v1 2l1 2c0 1 0 2 1 3 0 1-1 2 0 3v5h-2l-2-10h0v-1-1l-1-2v-1c0-1 0-1-1-1v-2c0-1 0-1-1-2-1 1 0 1 0 2s-1 1-1 2z" class="K"></path><path d="M111 540h2c1 0 1-1 2 1h0c-1 1-1 1-1 2h3l-2 2h0c0 1 1 2 1 3l2-1h0c1 1 1 1 2 1 2 1 2 2 3 2v1h1c1-1 3-2 4-3 2-1 3-1 4-1 2 1 2 2 3 3 0 1 0 1-1 2-1 0-4 1-5 1-1 1-2 2-2 3-1 1-1 1-1 3h-3c-3 1-7 2-9 5 0 1 0 2 1 2 1 1 1 2 2 2l4 1c2 1 5 7 7 6 1 0 2 0 3 1 1 2 1 4 0 6l-4 4h-1 0-4v1h1c-3 0-5-1-8-2h-2 0 0c-1-1-2-1-2-1-1 1-1 1 0 1v1l-4-3c-1-1-2-3-3-4l1-2c-3-3-3-6-5-10l1-1v-2c-1-8 2-14 7-21l3-3z" class="m"></path><path d="M117 584c3 1 6 2 9 2h0-4v1h1c-3 0-5-1-8-2l2-1z" class="J"></path><path d="M109 578c3 2 5 4 8 6l-2 1h-2 0c-2-2-4-4-5-6 1 0 1 0 1-1z" class="N"></path><path d="M104 568c2 4 3 7 5 10 0 1 0 1-1 1-3-2-4-6-5-9 1-1 1-1 1-2z" class="e"></path><path d="M109 553l1-3h0l1 2c1 2 1 3 3 4 1 1 2 1 3 1 3-1 4-4 6-6h0 1c-2 3-4 7-7 7-2 1-4 0-6-1-1-2-1-3-2-4z"></path><path d="M100 567l1-1 2 4c1 3 2 7 5 9 1 2 3 4 5 6h0c-1-1-2-1-2-1-1 1-1 1 0 1v1l-4-3c-1-1-2-3-3-4l1-2c-3-3-3-6-5-10z" class="F"></path><path d="M105 577c2 2 2 3 2 6-1-1-2-3-3-4l1-2z" class="L"></path><path d="M108 543l1 1-3 6c-2 5-2 10-1 15 1 3 4 6 7 8 2 1 5 1 8 3-5-1-9-1-13-5-1-1-1-3-2-4h0l-1 1c0 1 0 1-1 2l-2-4v-2c-1-8 2-14 7-21z" class="i"></path><path d="M111 540h2c1 0 1-1 2 1h0c-1 1-1 1-1 2h3l-2 2h0c0 1 1 2 1 3l2-1h0c1 1 1 1 2 1 2 1 2 2 3 2v1h0c-2 2-3 5-6 6-1 0-2 0-3-1-2-1-2-2-3-4l-1-2h0l-1 3c0-3 0-6 1-8l-1-1-1-1 3-3z" class="W"></path><path d="M111 552v-5c0 1 0 2 1 3 0 1 1 1 1 2 1 1 1 3 1 4-2-1-2-2-3-4z" class="F"></path><path d="M111 540h2c1 0 1-1 2 1h0c-1 1-1 1-1 2h3l-2 2h0c0 1 1 2 1 3s0 1-1 1v1h-1c-1-1-2-3-2-4h0l-1 1v5l-1-2h0l-1 3c0-3 0-6 1-8l-1-1-1-1 3-3z" class="b"></path><path d="M114 543h3l-2 2h0c0 1 1 2 1 3s0 1-1 1v1h-1l1-1c-1-2-2-3-1-6z" class="F"></path><path d="M414 332c1 0 2 1 3 2s1 2 2 3h1l2 2 2 1c1 3 5 5 7 8l-1 1v1l-1 1h-2l2 2c1 0 2-1 3 1l-1 1v1c0 2 0 3 1 4-1 0-1 0-2-1h0-2-2 0l1 4s1 0 1 1c1 1 0 2 1 3 0 1 0 3 1 4l1 4h2l1 1-1 2c-1-1-1-2-2-2-1 2 0 4 0 6-1 2 1 5 1 8v1 1c0-1-1-1-1-1 0-1 0-1-1-1h-1c-2-1-2-5-2-7h-1v4 1c-1-1-1-1-1-2l-1 1-1-1v-2h-1v1h-1 0c-1 0-1-1-2-1 0-1 0-6-1-7v1h-1v-1h0c0-2 0-3-1-4s-1-2-1-3 0-1-1-2v1c-1 0-2 0-2-1l-2-4h0c0-1-1-2-1-3-1-1-1-2-2-3l-1-2c-1-3-3-6-4-9-1-1-3-2-4-4l3-1 2-2 6-4c1-1 4-3 5-4z" class="V"></path><path d="M417 357l2-1c1 0 0 0 1-1h0 1l-1 1 3 8v1c1 1 0 3 0 3v-3h-2c0-2-3-6-4-7v-1z" class="o"></path><path d="M416 357l1 1c1 1 4 5 4 7h2v3c0 2 0 3 1 4v2l-1 1v2h-1l-2-1s0-4-1-5h-1 0c0-1 1-1 1-2-1-2 0-5-1-7l-1-1v-2c-1 0-1-1-1-1v-1z" class="T"></path><path d="M421 365h2v3c0 2 0 3 1 4v2l-1 1v2h-1v-3c1-1-2-6-1-9z" class="b"></path><path d="M403 340v1l2 1h1c2 0 2 0 3 1 0 2 1 2 1 4h-2l1 1 3 5h2c1 1 2 2 3 4v1l-1-1v1s0 1 1 1v2l1 1c1 2 0 5 1 7 0 1-1 1-1 2h0 1c1 1 1 5 1 5 1 3 1 6 1 9h0c-1 0-1-1-2-1 0-1 0-6-1-7v1h-1v-1h0c0-2 0-3-1-4s-1-2-1-3 0-1-1-2v1c-1 0-2 0-2-1l-2-4h0c0-1-1-2-1-3-1-1-1-2-2-3l-1-2c-1-3-3-6-4-9-1-1-3-2-4-4l3-1 2-2z" class="Z"></path><path d="M412 353h2c1 1 2 2 3 4v1l-1-1c-1 0-2-1-2-1-1-1-2-2-2-3z" class="p"></path><path d="M405 342h1c2 0 2 0 3 1 0 2 1 2 1 4h-2c-1-2-2-3-3-5z" class="R"></path><path d="M403 340v1 1c3 3 4 7 7 10 1 1 2 2 2 4 0 1 0 1 1 2l-3-1-1-1c-1 1-1 1-1 2h-1l-1-2c-1-3-3-6-4-9-1-1-3-2-4-4l3-1 2-2z" class="E"></path><path d="M403 340v1 1c3 3 4 7 7 10 1 1 2 2 2 4 0 1 0 1 1 2l-3-1-1-1c-1 1-1 1-1 2h-1l-1-2h2c0-2 1-2 1-4h-1c-1-1-3-3-3-4-1-3-3-4-4-6h0l2-2z" class="h"></path><path d="M407 358h1c0-1 0-1 1-2l1 1 3 1c2 3 2 7 4 11 0 1 1 3 2 4 1 3 1 7 1 10v1h-1c0-1 0-6-1-7v1h-1v-1h0c0-2 0-3-1-4s-1-2-1-3 0-1-1-2v1c-1 0-2 0-2-1l-2-4h0c0-1-1-2-1-3-1-1-1-2-2-3z" class="R"></path><path d="M410 364v-1l1 1h2c1 2 1 3 1 4v1c-1 0-2 0-2-1l-2-4z" class="J"></path><path d="M407 358h1c0-1 0-1 1-2l1 1c0 1 0 2 1 2 0 2 1 3 1 4l-1 1-1-1v1h0c0-1-1-2-1-3-1-1-1-2-2-3z" class="T"></path><path d="M414 332c1 0 2 1 3 2s1 2 2 3h1l2 2 2 1c1 3 5 5 7 8l-1 1v1l-1 1h-2l2 2h-2l3 4v1h-3c0-2 1-1 0-2h-2v-1c-1 0-1 1-1 2l-2-2h0c0 2 0 2 1 4h0l-1-1c0-1-1-2-1-3h-1 0c-1 1 0 1-1 1l-2 1c-1-2-2-3-3-4h-2l-3-5-1-1h2c0-2-1-2-1-4-1-1-1-1-3-1h-1l-2-1v-1l6-4c1-1 4-3 5-4z" class="C"></path><path d="M419 342s0 1 1 2 1 2 1 4c0 1 0 0-1 1-1-2-2-3-3-5l-1-1h2l1-1z" class="b"></path><path d="M425 355l1-1c-1-1-1-1-2-1 0-1-1-2-1-3-1 0-1-1-2-2h1v-2h-1l1-1v2c1 1 3 3 4 3 0 0 1 0 1 1l2 2h-2l3 4v1h-3c0-2 1-1 0-2h-2v-1z" class="J"></path><path d="M427 353h0c-2-2-5-4-5-6 1 1 3 3 4 3 0 0 1 0 1 1l2 2h-2z" class="X"></path><path d="M420 337l2 2 2 1c1 3 5 5 7 8l-1 1v1l-1 1h-2c0-1-1-1-1-1v-1c-1-2-1-2-1-3l-1-1h0v-1s-1 0-1-1l-1-1v2h-1v-1l-1 1c-1-1-1-2-1-2l-1-1c1-1 1 1 3 1 0-2-1-3-1-5z" class="f"></path><path d="M424 345l6 4v1l-1 1h-2c0-1-1-1-1-1v-1c-1-2-1-2-1-3l-1-1h0z" class="S"></path><path d="M413 346l-2-2h1l1 1c1 0 1 0 1-1l1 1 1 1c0-1 1-1 1-2 1 2 2 3 3 5l1 1c-1 0-2-1-2-1h-1v1c0 1 1 1 1 3-1-1-1-1-2-1 0-1-1-2-2-2h0v1h-2v1l1 1h-2l-3-5-1-1h2c0-2-1-2-1-4l4 4v-1z" class="D"></path><path d="M409 343l4 4c0 1 1 2 1 3v1c-1-1-1-2-2-3h-1 0l-1 1v-1h-1l-1-1h2c0-2-1-2-1-4z" class="S"></path><path d="M413 346l-2-2h1l1 1c1 0 1 0 1-1l1 1 1 1 1 1c1 1 1 2 1 3h-2l-3-4z" class="h"></path><path d="M409 336h0c3 2 5 4 7 7l1 1c0 1-1 1-1 2l-1-1-1-1c0 1 0 1-1 1l-1-1h-1l2 2v1l-4-4c-1-1-1-1-3-1h-1l-2-1v-1l6-4z" class="c"></path><path d="M414 344l1-1-1-1c-1 0-2 0-2 1l-2-4h0l-2-2 1-1c3 2 5 4 7 7l1 1c0 1-1 1-1 2l-1-1-1-1z" class="X"></path><path d="M414 332c1 0 2 1 3 2s1 2 2 3h1c0 2 1 3 1 5-2 0-2-2-3-1l1 1-1 1h-2c-2-3-4-5-7-7h0c1-1 4-3 5-4z"></path><path d="M303 508v-1h1c8 1 17 1 26 2l15 1c4 0 7 1 10 1l11-1c3 0 7 1 11 0 1 0 3-1 4 0h2 0c-7 3-13 5-19 9l-2 2-8 4v-1c-1-1-1-1-1-2l-1 1v1c-1 0-1 0-1 1h-1l-1 1c-1 1-1 1-2 1 0 0-1 1-2 1-1 1-2 1-3 2-1-1-1-1-2-1h-2 0-3-1 0c-2 0-5-1-7-2v-1c-2-1-3-2-3-4l-1 1-1-1-3 2v1h-2-1c-1 0-3 0-4-1s-2-3-4-4v-1h-4v-1l1-1h-1v-2c-1-3-5-6-7-8 2 1 4 1 5 1h1z" class="I"></path><path d="M345 521l1-4c1-1 4-1 6 0v1c-1 1-2 1-3 2l-4 1z" class="W"></path><path d="M346 511h8l-2 4c-2 0-4 1-6 1l-1-1v-1l1-1v-2zm35-1h2 0c-7 3-13 5-19 9-1 0-2-1-3 0l-2-1c-1-1-2-4-3-6l14-1c3 0 7 0 11-1h0z" class="m"></path><path d="M334 514c1-1 2-1 2-1 1 1 2 1 2 2h0v1c1 1 2 2 3 4 0 1 0 2 1 4h1v1h1v-2s1 0 1-1v-1l4-1c1-1 2-1 3-2l1-1h1v1c2 1 4 1 5 1h2c1-1 2 0 3 0l-2 2-8 4v-1c-1-1-1-1-1-2l-1 1v1c-1 0-1 0-1 1h-1l-1 1c-1 1-1 1-2 1 0 0-1 1-2 1-1 1-2 1-3 2-1-1-1-1-2-1h-2 0-3-1 0c-2 0-5-1-7-2l1-1h1 1l-1-1v-1c0-1 1-3 1-5h-1v-1h3 3c-1-2-2-3-3-4h2z" class="H"></path><path d="M334 524c-1-1-1-1 0-2 0-1 1-1 2-1v1l2 1c-1 1-1 3-1 4 1 1 2 1 3 2h-2 0-3l1-1v-1-1l-2-2z" class="E"></path><path d="M336 522c1-1 0-1 1-1 1-1 1-1 1-2 0-2-1-3-2-4h1l1 1c1 1 2 2 3 4 0 1 0 2 1 4h-1v2h-1-2v-3h0l-2-1z" class="Q"></path><path d="M334 514c1-1 2-1 2-1 1 1 2 1 2 2h0v1l-1-1h-1c1 1 2 2 2 4 0 1 0 1-1 2-1 0 0 0-1 1v-1c-1 0-2 0-2 1-1 1-1 1 0 2h-2 0c-1-1-1-1-1-2l1-1c1-1 2-1 3-1v-2h0c-1-2-2-3-3-4h2z" class="d"></path><path d="M332 514h2c1 1 2 3 3 4v1c-1 0-1 0-2-1h0c-1-2-2-3-3-4z" class="G"></path><path d="M329 518h3 3 0v2c-1 0-2 0-3 1l-1 1c0 1 0 1 1 2h0 2l2 2v1 1l-1 1h-1 0c-2 0-5-1-7-2l1-1h1 1l-1-1v-1c0-1 1-3 1-5h-1v-1z" class="N"></path><path d="M332 524h2l2 2h-2c-1 0-2-1-2-2z" class="L"></path><path d="M352 518l1-1h1v1c2 1 4 1 5 1h2c1-1 2 0 3 0l-2 2-8 4v-1c-1-1-1-1-1-2l-1 1h-1s0 1-1 1c-1-1-3 0-4-1v-1h2c1-1 1-1 2-1l2-2v-1zm-47-1h-1v-2c-1-3-5-6-7-8 2 1 4 1 5 1h1l43 3v2l-1 1v1l1 1h-1c-1 2-1 4-1 6l-1 1c-1-1 0-2 0-3-2-1-3-4-5-5h0c0-1-1-1-2-2 0 0-1 0-2 1h-2c1 1 2 2 3 4h-3-3v1h1c0 2-1 4-1 5v1l1 1h-1-1l-1 1v-1c-2-1-3-2-3-4l-1 1-1-1-3 2v1h-2-1c-1 0-3 0-4-1s-2-3-4-4v-1h-4v-1l1-1z" class="Q"></path><path d="M305 517v-4c2-1 3-2 4-2 2-2 5 2 7 3h3 1v2l-2 2v1l-1 1h0l-2 1v3l2 1h-1c-1 0-3 0-4-1s-2-3-4-4v-1h-4v-1l1-1z" class="J"></path><path d="M308 520h5c1 1 0 2 0 3l-1 1c-1-1-2-3-4-4z" class="Y"></path><path d="M308 518h-2 0v-4c2-2 3-2 5-3 1 1 2 1 4 2l-1 1c-1 0-4-1-5 0v1s0 1-1 1v1 1h0z" class="G"></path><path d="M315 513v1c1 0 2 1 3 1-2 1-4 2-6 2s-3 1-4 1h0v-1-1c1 0 1-1 1-1v-1c1-1 4 0 5 0l1-1z" class="W"></path><path d="M320 514h0c2 0 3-1 4-2 1 0 3 0 5-1 0 1 2 2 3 3s2 2 3 4h-3-3v1h1c0 2-1 4-1 5v1l1 1h-1-1l-1 1v-1c-2-1-3-2-3-4l-1 1-1-1-3 2v1h-2l-2-1v-3l2-1h0l1-1v-1l2-2v-2z" class="C"></path><path d="M329 517l-1-2h1c1 0 2 1 2 2 1 0 1 0 1 1h-3v-1z" class="L"></path><path d="M329 517v1 1h1c0 2-1 4-1 5v-1c-1-1-2-1-2-1v-2c1-1 1-2 2-3z" class="n"></path><path d="M324 522c1-1 1-2 1-3l-1 1-1-1c0-1 1-1 1-1h1 1s0-1 1-1v1c0 1-1 3-1 4h1s1 0 2 1v1 1l1 1h-1-1l-1 1v-1c-2-1-3-2-3-4z" class="g"></path><path d="M317 525l-2-1v-3l2-1h0l1-1v-1l2-2v1s-1 1-1 2l1 1-2 3c1-1 2-2 4-2h1c0 1 0 0-1 1l-3 2v1h-2z" class="P"></path><path d="M472 367c1-1 2-4 2-6s1-3 2-5c2-6 8-13 14-16 4-1 8 0 12 1l1 1-1 1c0 2 1 3 1 4 1 2 1 5-1 7-2 1-3 1-5 1-1 0-2-1-3-3-1-1-1-1 0-2v2c1 0 1 1 2 1h0 1c1 0 2 1 3-1 1 0 1-2 1-3 0-3-1-5-3-6s-5-1-7-1c-5 1-9 7-12 11-2 4-3 8-4 13-1 2-1 5-1 8 3-5 5-9 11-11 3 0 8 1 11 3 6 3 9 9 11 15v6c1-1 3-4 4-5-2 4-4 6-5 10v3h0c1 0 1-1 1-1 0-1 1-2 1-3h3c1-2 1-3 1-5 0-1-1 0 0-1v-3h0v-1c1 2 1 3 1 5s-1 4-2 6c-1 3-5 7-7 10-1 1-2 3-3 4 4-2 7-4 12-4l1 1c-1 1-6 2-7 2l-6 3-4 4v3c0 2-1 4-2 5-4 5-11 5-14 11l-1-1c-1 1-1 1-2 1 0-2 3-4 4-5-1-1-1-1-2-1l1-2v-1c-1-6 4-10 7-15 0-1 1-3 2-5 1-3 2-6 4-9v1h1c1 5 0 11-1 16 2-3 4-9 7-11h0 1c-1-3-2-6-1-8 0-2 1-3 2-3 1-2-1-7-1-9a19.81 19.81 0 0 0-11-11c-3-2-5-2-8-1-2 1-4 2-5 4v1c3 0 4-1 5 2 1 0 1 1 1 1l-1 1h-1-1l2-2h-1c-1 0-3 0-4 1s-1 2-1 4v11c1-1 2-1 3-2s7-5 8-5 2 1 2 1l1 1c-1 1-1 1-2 1 0 0-1 0-1-1-5 3-11 5-13 10 0 2 0 3 1 4l2 11h-1c1 2 1 4 1 7h0c-1-5-3-9-5-13l-1 1c-1-2-2-4-2-6 1 0 1-1 0-1-1-3-1-6-2-9 0-2-1-5 0-7 1-4 2-6 4-9h0c-1 0-1-1-1-1 0-2 0-4 1-5z"></path><path d="M476 382v1c1 4 0 7-2 11h-1c1-5 1-8 3-12z" class="Q"></path><path d="M469 383c0-4 2-7 4-9-1 3-2 6-2 10h0v4h0v-1l-1-1v-4l-1 1z" class="F"></path><path d="M489 418c1-2 2-5 4-7h0l1 3-1 1c0 1-1 2-1 3h-1c-1 1-2 3-3 3 0-1 0-2 1-3z" class="M"></path><path d="M502 399c0 6-4 9-6 14l-1-1c0-2 2-4 3-6s2-4 3-7h0 1z" class="H"></path><path d="M488 409c2-4 3-9 5-13v9c-1 1-2 3-4 4h-1z" class="B"></path><path d="M471 384c1-2 1-7 3-9h1c-1 3-2 5-2 8-1 5-1 9-2 14-1-5-3-9-2-14l1-1v4l1 1v1h0v-4z" class="Q"></path><path d="M473 404c-1-2-1-4-1-5l2-4 3 15c1 2 1 4 1 7h0c-1-5-3-9-5-13z" class="P"></path><path d="M487 418h0l1-1 1 1c-1 1-1 2-1 3 1 0 2-2 3-3h1 1c0 2-3 4-4 5-3 2-6 4-9 7-1 1-1 1-2 1 0-2 3-4 4-5 1-2 1-2 1-4 1-2 2-3 4-4z" class="Z"></path><path d="M493 405v5c-1 1-2 2-2 3l-3 4-1 1h0c0-1 0-1-1-2-2 1-2 2-3 4l-1-1c0-2 2-3 3-5 1-1 2-3 3-5h1c2-1 3-3 4-4z" class="Y"></path><path d="M472 367c1-1 2-4 2-6s1-3 2-5c2-6 8-13 14-16 4-1 8 0 12 1l1 1-1 1c0 2 1 3 1 4 1 2 1 5-1 7-2 1-3 1-5 1-1 0-2-1-3-3-1-1-1-1 0-2v2c1 0 1 1 2 1h0 1v1h3c1-1 2-2 2-3 1-2 0-5-1-7s-3-3-5-3c-2-1-3-1-4 0h0c-5 1-10 6-12 10-4 6-6 13-7 20 0 1-1 1-1 2-1 0-1-1-1-1 0-2 0-4 1-5z" class="H"></path><path d="M258 501h5c3 1 6 2 9 2l32 4h0-1v1h-1c-1 0-3 0-5-1 2 2 6 5 7 8v2h1l-1 1v1h4v1c2 1 3 3 4 4s3 1 4 1h1 2v-1l3-2 1 1 1-1c0 2 1 3 3 4v1c2 1 5 2 7 2h0l1 1c0 1 0 2-1 3h0v3h-1c-1 1-1 1-1 2h-2v1 2 2h-2v2c1 0 1 0 2 1l-1 1c1 0 2 0 3 1l-1 2-4 5h-1c0 1-1 2-1 2h-1v1h0l-6-9c-7-10-15-18-23-27l-1 1 1 1-2-1c-1-2-4-3-6-5-4-2-7-5-12-7-2-1-5-2-8-4h1l-1-1s0-1-1-1h0 0c-2-1-7-2-8-4z" class="G"></path><path d="M329 547c1 0 2 0 3 1l-1 2-4 5h-1c0 1-1 2-1 2h-1c-1-1-1-1-1-2v-5s1-2 2-2h1l3-1z" class="J"></path><path d="M329 547c1 0 2 0 3 1l-1 2-4 5c-1-2 0-6-1-7l3-1z" class="G"></path><path d="M266 505c3 0 5 2 8 3 7 4 15 8 21 14h0l-1 1 1 1-2-1c-1-2-4-3-6-5-4-2-7-5-12-7-2-1-5-2-8-4h1l-1-1s0-1-1-1h0z" class="S"></path><path d="M258 501h5c3 1 6 2 9 2l32 4h0-1v1h-1c-1 0-3 0-5-1 2 2 6 5 7 8v2h1l-1 1h-1v-3-1l-3-3h0c-3-3-7-3-11-3 0 1-1 1-1 1h-1v1c1 0 1 1 2 2 3 1 9 4 11 7h-1l-6-4c-1-1-2-1-3-2-2-1-5-1-7-2v-1c-1-3-7-3-9-5-2 0-5-1-7-1-1 0-1 0-2-1h0v1l1 1c-2-1-7-2-8-4z" class="a"></path><path d="M274 505c2 0 5 0 7 1l5 1h0l-3 3c-1-3-7-3-9-5z" class="F"></path><path d="M304 519h4v1c2 1 3 3 4 4s3 1 4 1h1 2v-1l3-2 1 1 1-1c0 2 1 3 3 4v1c2 1 5 2 7 2h0l1 1c0 1 0 2-1 3h0v3h-1c-1 1-1 1-1 2h-2v1 2 2h-2v2c1 0 1 0 2 1l-1 1-3 1h-1c-1-1-3-1-4-1v1h-1c0-2-1-4-1-5 0-2-1-4-1-6 1-2 1-4 0-6-1-1-2-2-4-2h0l1 2h0v1c-1-1-1-2-2-3v-1c-1-1-5-2-6-3l-6-2c6 8 14 13 18 22-2-1-3-3-4-5s-3-4-5-7c-4-4-9-8-13-12h0c3-1 5-1 7-2z"></path><path d="M319 525v-1l3-2 1 1 1-1c0 2 1 3 3 4v1c2 1 5 2 7 2h0l1 1c0 1 0 2-1 3h0v3h-1c-1 1-1 1-1 2h-2v1 2 2h-2v2c1 0 1 0 2 1l-1 1-3 1h-1c-1-1-3-1-4-1 1-1 2 0 4-1h-1 0c-1-1-2-2-2-3v-4h-1c1-1 1-1 2-1-1-1-2-1-2-2l1-1v-2h1v1h1l-1-1 1-1v-2c0-1-1-1-2-1v-1l1-1v-1-1-1c-2 0-2 1-3 2l-1-1z" class="f"></path><path d="M330 541h0c-1-1-1 0-1-1-1 0-1-1-1-1v-1h1l1 1v2zm3-5c-1-1-1-1-1-2h-1l-1 1h-1c0-1-1-2-2-3l1-1 1-1h0 2l3 3v3h-1z" class="I"></path><path d="M324 532l1-1h2v2c0 2-1 4-1 6 0 0 0 1-1 1l1 2-1 1h-1v-1c0-1-1-2-1-3v-1l1-1c0-1-1-2-2-2v-2h1v1h1l-1-1 1-1z" class="l"></path><path d="M297 311l2 1v1c1 1 2 2 3 2h2v-1h2s0-1 1-1h0 1c2 2 3 1 5 2h2 1l1 1h2 0c1-1 3-1 5-1 0 1 0 1-1 2l1 1-9 4h1 0c-2 1-4 1-5 2h4 1c0 1 0 1 1 1l-2 2h0c2 1 4 0 6 1h0l-1 1v1c1-1 2-1 3 0h0l-2 1c0 1 0 1 1 1-1 2-2 0-3 0h-3c-2 0-6 1-7 3h0l1 1c-2 1-3 2-4 4v1h0l-1 1h-2l-2 1-1-1h-2-1-4v1c-1-1-1-1-2-1h-3-2l-6-1h-5c-2 0-4 0-6-1h0 2-1v-3c0-3 1-6 1-9 1 0 2-1 3-2h0c0-1 1-1 2-2 0-1 1-2 2-3v-2c2 0 2-1 3-2h3l2-1h0-1l1-1v-1h1 4 1c2-1 3-2 5-3z" class="g"></path><path d="M295 326h0 2 2l-1 1h0c-2 1-4 1-5 2l-1-1c1 0 2-1 3-2z" class="j"></path><path d="M287 325c2-1 2-3 3-4l1 1c3 0 6-1 8 2h1c1 0 2 1 3 1-1 1-2 1-3 1v-1h-3s0-1-1-1-1 0-2-1c-3 1-4 2-6 3l-1-1z" class="V"></path><path d="M291 321h0c3 1 5 1 7 0v-1c-1 0-2 0-3-1h1c1 0 1 0 2 1 1 0 1 0 2 1l-1 1h4 3c1 0 1 1 2 1h0c-1 1-3 2-5 2-1 0-2-1-3-1h-1c-2-3-5-2-8-2l-1-1h1z" class="J"></path><path d="M286 325h1l1 1h-1 2c1-1 3-1 5-1-3 1-6 3-8 5l-1 1h0l-1-1c-1 1-1 2-2 3-1-1-1-1-1-2-1 1-1 1-1 2-1 0-2-1-2-2v-1h-1l2-1h1v2c2-1 3-3 4-4 1 0 2-1 2-2z" class="R"></path><path d="M304 315l1 1-2 1 1 1h1l2 1h-1c-1 1-2 1-4 1v1h1v1h-4l1-1c-1-1-1-1-2-1-1-1-1-1-2-1l2-2v-1h0l4-1h2z" class="U"></path><path d="M304 315l1 1-2 1 1 1h-1l-1-1h0c-1 0-2 0-3 1h0c0 1-1 1-1 2-1-1-1-1-2-1l2-2v-1h0l4-1h2z" class="E"></path><path d="M297 311l2 1v1c1 1 2 2 3 2l-4 1h0v1l-2 2h-1c1 1 2 1 3 1v1c-2 1-4 1-7 0h0l-2-2h-1-1l-1-1 1-1-1-1h0-1l1-1v-1h1 4 1c2-1 3-2 5-3z" class="I"></path><path d="M287 314h0 3l2 2c-1 0-2 0-3 1v2h-1-1l-1-1 1-1-1-1h0-1l1-1v-1h1z" class="N"></path><path d="M286 315h1 1v1h-2-1l1-1z" class="C"></path><path d="M288 316l1 1v2h-1-1l-1-1 1-1-1-1h0 2z" class="D"></path><path d="M297 311l2 1v1c1 1 2 2 3 2l-4 1h0-6l-2-2h-3 0 4 1c2-1 3-2 5-3z" class="K"></path><path d="M292 314h3c1 0 2 0 3 2h0 0-6l-2-2h-3 0 4 1z" class="c"></path><path d="M295 314c1 0 2 0 3 2h-4v-1h1v-1z" class="V"></path><path d="M307 313h1c2 2 3 1 5 2h2 1l1 1h2 0c1-1 3-1 5-1 0 1 0 1-1 2l1 1-9 4h-3v-1h-3-1 0-5-1v-1c2 0 3 0 4-1h1l-2-1h-1l-1-1 2-1-1-1v-1h2s0-1 1-1h0z" class="n"></path><path d="M307 319h4 0l-2 2h-1 0-5-1v-1c2 0 3 0 4-1h1 0z" class="L"></path><path d="M319 316h3 1c-2 2-5 3-7 4l-2-1h0c1-2 1-2 3-3h2 0z" class="J"></path><path d="M307 313h1c2 2 3 1 5 2h2 1l1 1c-2 1-2 1-3 3h0l2 1h-3 0v-1h-1c-1-1-3 0-5 0h0l-2-1h-1l-1-1 2-1-1-1v-1h2s0-1 1-1h0z" class="C"></path><path d="M307 313h1c2 2 3 1 5 2-3 0-5 0-8 1l-1-1v-1h2s0-1 1-1h0z" class="a"></path><path d="M315 315h1l1 1c-2 1-2 1-3 3h0l2 1h-3 0v-1h-1c-1-1-3 0-5 0h0l-2-1h2c2 0 5-1 7-1 1-1 1-1 1-2z" class="V"></path><path d="M286 316l1 1-1 1 1 1h1 1l2 2h-1c-1 1-1 3-3 4h-1c0 1-1 2-2 2-1 1-2 3-4 4v-2h-1l-2 1v-1c1-1 1-2 1-3h-1 0c0-2 1-3 2-4h-1c-1 1-1 2-2 2 0-1 1-2 2-3v-2c2 0 2-1 3-2h3l2-1z" class="U"></path><path d="M283 321c1-1 0-1 0-2 1-1 2-1 3-1l1 1h1-2c-1 2-2 2-3 3v-1z" class="S"></path><path d="M279 329c0-2 1-3 3-4h0l-1 2c3-1 4-3 6-4h0l-1 2h0c0 1-1 2-2 2-1 1-2 3-4 4v-2h-1z" class="X"></path><path d="M286 316l1 1-1 1c-1 0-2 0-3 1 0 1 1 1 0 2-1 0-2 0-3 1h-1-1c-1 1-1 2-2 2 0-1 1-2 2-3v-2c2 0 2-1 3-2h3l2-1z" class="K"></path><path d="M276 324c1 0 1-1 2-2h1c-1 1-2 2-2 4h0 1c0 1 0 2-1 3v1h1v1c0 1 1 2 2 2 0-1 0-1 1-2 0 1 0 1 1 2 1-1 1-2 2-3l1 1c-1 1-1 2-2 3h1c1-1 2-3 4-4 0 3-2 4-2 6s0 1-1 3c0 1 1 2 1 3l-6-1h-5c-2 0-4 0-6-1h0 2-1v-3c0-3 1-6 1-9 1 0 2-1 3-2h0c0-1 1-1 2-2z" class="b"></path><path d="M275 340h0c2 0 3-1 5 0v1h-5v-1z" class="C"></path><path d="M275 328l2 2h0c1 2 1 2 1 4l-1-3-1 1c0 1 0 4-1 5 0-1 0-3-1-4h-1v-1h1 1v-1c-1-1 0-2 0-3z" class="o"></path><path d="M276 324c1 0 1-1 2-2h1c-1 1-2 2-2 4h0 1c0 1 0 2-1 3v1h1v1c0 1 1 2 2 2l-2 2v-1c0-2 0-2-1-4h0l-2-2-1-2h0c0-1 1-1 2-2z" class="E"></path><path d="M274 326l1 2c0 1-1 2 0 3v1h-1-1v1h1c1 1 1 3 1 4v3 1c-2 0-4 0-6-1h0 2-1v-3c0-3 1-6 1-9 1 0 2-1 3-2z" class="b"></path><path d="M271 340v-2c0-2 0-4 1-6 1 1 0 6 1 8h0c1-2 0-5 0-7h1c1 1 1 3 1 4v3 1c-2 0-4 0-6-1h0 2z" class="n"></path><path d="M288 330h0c2-2 4-4 7-4-1 1-2 2-3 2l1 1c1-1 3-1 5-2 0 1-1 2-2 3h1l1-1c2 1 1 1 3 2l2-2h0c1 1 1 2 3 3h1v1h0c-3 2-6 4-9 5-2 1-6 2-7 4h-3-2c0-1-1-2-1-3 1-2 1-1 1-3s2-3 2-6z" class="R"></path><path d="M294 336l2-1h1c-1 2-3 3-5 4h0c0-2 1-2 2-3z" class="b"></path><path d="M303 329h0c1 1 1 2 3 3h1v1h0-1-1c-3-1-3 0-5 1h-1l1-2h0 1c1-1 2-1 2-2v-1z" class="a"></path><path d="M296 330h1l1-1c2 1 1 1 3 2l2-2v1c0 1-1 1-2 2h-1 0-1c-1 1-2 2-3 2 0-1 1-2 1-3-1 0-1 0-2 1v-1l1-1z" class="K"></path><path d="M295 332h1l-2 3v1c-1 1-2 1-2 3h0l-1 2h-2l1-1v-1c0-1 1-1 1-2h-2c1-1 1-2 2-3h1l1 1 2-3z" class="C"></path><path d="M298 327c0 1-1 2-2 3l-1 1v1l-2 3-1-1h-1-1c0-3 2-3 3-5 1-1 3-1 5-2z" class="X"></path><path d="M288 330h0c2-2 4-4 7-4-1 1-2 2-3 2l1 1c-1 2-3 2-3 5h1c-1 1-1 2-2 3h2c0 1-1 1-1 2v1l-1 1-1 1h-2c0-1-1-2-1-3 1-2 1-1 1-3s2-3 2-6z" class="T"></path><path d="M286 336c1-1 1-2 2-2l1 1c0 1-1 1-1 3l1-1h2c0 1-1 1-1 2v1l-1 1-1 1h-2c0-1-1-2-1-3 1-2 1-1 1-3z" class="I"></path><path d="M312 322h3 1 0c-2 1-4 1-5 2h4 1c0 1 0 1 1 1l-2 2h0c2 1 4 0 6 1h0l-1 1v1c1-1 2-1 3 0h0l-2 1c0 1 0 1 1 1-1 2-2 0-3 0h-3c-2 0-6 1-7 3h0l1 1c-2 1-3 2-4 4v1h0l-1 1h-2l-2 1-1-1h-2-1-4v1c-1-1-1-1-2-1 1-2 5-3 7-4 3-1 6-3 9-5h0v-1h-1c-2-1-2-2-3-3h1l2-2v-1c1-1 5-3 6-4z" class="L"></path><path d="M309 335l1 1c-2 1-3 2-4 4v1h0l-1 1h-2v-1c1-3 4-5 6-6zm-16 7c3-3 7-4 10-5 1-1 3-2 4-2h0l-3 3-2 2c0 1 0 2-1 3l-1-1h-2-1-4z" class="C"></path><path d="M300 342v-2h2c0 1 0 2-1 3l-1-1z" class="J"></path><path d="M312 322h3 1 0c-2 1-4 1-5 2h4c-1 1-2 1-3 1 1 1 1 1 1 2h0c1 1 3 0 3 1s-1 1-2 1c-2 1-4 3-7 4v-1h-1c-2-1-2-2-3-3h1l2-2v-1c1-1 5-3 6-4z" class="n"></path><path d="M306 327h0c1 0 2 0 2-1 1 0 3-1 4-1v2h-4c-1 1-2 2-4 2l2-2z" class="h"></path><path d="M312 322h3 1 0c-2 1-4 1-5 2h4c-1 1-2 1-3 1 1 1 1 1 1 2h-1v-2c-1 0-3 1-4 1 0 1-1 1-2 1h0v-1c1-1 5-3 6-4z" class="K"></path><path d="M248 507c6 1 12 4 19 6h0 3c1 1 2 1 3 1s4 2 4 2c1 1 1 0 1 1h1c1 1 1 1 2 1 0 1 1 1 2 1 3 2 5 4 8 6v-1c1 1 1 1 2 1s1 0 3 1v1c-1-1-2-2-2-4h-1l2 1-1-1 1-1c8 9 16 17 23 27l6 9h0v-1h1c0 1-1 2-1 3 1 1 1 0 2 0l2 2 1-1c0 2 0 4 1 5h0l-1 1-3 3c0 1-1 1-1 2 0-1 0-1-1-1h-1c0-1-1-1-1-2l-2-1h0l-1-1c-1 0-4 2-4 3v1h-1l-1 1h-1l-2 3-3-3 1-1v-1l-1 2h-1c0-2-1-2-1-4l-1-1c2-1 4-3 5-4l-3-1v-2c0-1-1-1-1-2h-1v-1h0c-1 1-1 1-1 2l-1-6h-1c0 2 0 2-1 3 0-4-1-8-4-10v1c-1-1-2-1-2-2-1-1-4-6-6-7v3l-1-1h1v-1c-1-3-7-8-9-9s-4-2-5-3h-1c-1-1-3-2-3-3-1 0-1 0-1-1h0 0c-6-2-12-1-18 2l-6 3 1-2h0c0-1 1-2 2-4 1-3 1-9-1-12v-1-1h0c1 0 1-1 1-1z" class="O"></path><path d="M281 524c2 1 5 4 7 3 4 3 7 6 10 10h0c-1 0-1-1-3-1h0c-2-2-4-4-7-6-2-2-5-3-8-5l1-1z" class="X"></path><path d="M303 551l-1-4c3 4 5 8 9 12-1 1-2 1-2 2v2l-3-1v-2c0-1-1-1-1-2h-1v-1h0c-1 1-1 1-1 2l-1-6v-1l1-1z" class="Q"></path><path d="M302 553v-1l1-1c0 1 1 5 1 6h0c-1 1-1 1-1 2l-1-6z" class="e"></path><path d="M326 560l2 2 1-1c0 2 0 4 1 5h0l-1 1-3 3c0 1-1 1-1 2 0-1 0-1-1-1h-1c0-1-1-1-1-2l-2-1h0c1 0 1-1 1-2v-1h1 0l4-5z" class="E"></path><path d="M322 565h3 1c-1 1-1 2-2 3-1-1-2-2-2-3h0z" class="M"></path><path d="M326 560l2 2-2 3h-1-3l4-5z" class="d"></path><path d="M295 536c2 0 2 1 3 1l14 17c3 3 5 6 7 10l-7-7c-2-2-3-4-5-6-1-2-3-3-5-5-2-4-4-7-7-10z" class="G"></path><path d="M311 559l7 7-6 6-2 3-3-3 1-1v-1l-1 2h-1c0-2-1-2-1-4l-1-1c2-1 4-3 5-4v-2c0-1 1-1 2-2z" class="H"></path><path d="M309 561c2 1 2 2 3 3v2c0-1 0-1 1-1s1 0 2 1c-1 1-2 1-3 2h-2c-1 1-1 2-2 3v-1l-1 2h-1c0-2-1-2-1-4l-1-1c2-1 4-3 5-4v-2z" class="D"></path><path d="M305 568l2-2h1 1c0 1 1 1 1 2-1 1-1 2-2 3v-1l-1 2h-1c0-2-1-2-1-4z" class="H"></path><path d="M308 566h1c0 1 1 1 1 2-1 1-1 2-2 3v-1-4z" class="E"></path><path d="M293 523l2 1 9 9c5 6 10 13 15 21 2 2 4 5 5 7-1 0-2-1-3-3h-1 0c-1-1-2-1-2-2-1-1-1-2-2-3h0c-1-1-1-2-2-3v-1l-2-2-1-1c0-1-1-2-1-2-1-1-2-1-2-2l-3-3c0-1 0-1-1-1l-2-3h-1c0-2-3-5-5-6h-1c0-1 0-1-1-2l-3-2v-1c1 1 1 1 2 1s1 0 3 1v1c-1-1-2-2-2-4h-1z" class="G"></path><path d="M269 523c12 3 23 10 29 21 2 3 3 6 3 9 0 2 0 2-1 3 0-4-1-8-4-10v1c-1-1-2-1-2-2-1-1-4-6-6-7v3l-1-1h1v-1c-1-3-7-8-9-9s-4-2-5-3h-1c-1-1-3-2-3-3-1 0-1 0-1-1h0 0z" class="Q"></path><path d="M287 540h1v-1c-1-3-7-8-9-9s-4-2-5-3h-1c-1-1-3-2-3-3 7 3 15 8 21 14 2 3 4 5 5 8v1c-1-1-2-1-2-2-1-1-4-6-6-7v3l-1-1z" class="Z"></path><defs><linearGradient id="N" x1="292.417" y1="538.479" x2="298.528" y2="532.458" xlink:href="#B"><stop offset="0" stop-color="#d8d8d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#N)" d="M267 513h0 3c1 1 2 1 3 1s4 2 4 2c1 1 1 0 1 1h1c1 1 1 1 2 1 0 1 1 1 2 1 3 2 5 4 8 6l3 2c1 1 1 1 1 2h1c2 1 5 4 5 6h1l2 3c1 0 1 0 1 1l3 3c0 1 1 1 2 2 0 0 1 1 1 2l1 1 2 2v1c1 1 1 2 2 3h0c1 1 1 2 2 3 0 1 1 1 2 2h0c1 1 2 2 2 4 0 0 0 1-1 1-3-2-6-7-8-10l-15-16c-3-4-6-7-10-10-6-5-14-10-21-14z"></path><path d="M248 507c6 1 12 4 19 6 7 4 15 9 21 14-2 1-5-2-7-3l-1 1c-6-4-14-6-21-5-5 1-9 4-13 6h0c0-1 1-2 2-4 1-3 1-9-1-12v-1-1h0c1 0 1-1 1-1z" class="m"></path><path d="M248 507c6 1 12 4 19 6 7 4 15 9 21 14-2 1-5-2-7-3s-4-3-6-4c-8-5-19-10-28-12h0c1 0 1-1 1-1z" class="o"></path><path d="M404 501l1-1v5c0 9-2 19-4 28l-2-1v-1l-3-2h0c-4-4-8-5-13-6-2-1-5-1-6-2-2 1-3 2-4 2-2 1-5 2-7 4h1c-1 1-3 2-4 2-3 3-6 5-8 8l-3 4c-1 3-3 5-5 7l-8 8 2-1v1l-1 1v1l-1 2h-1c-1 1-2 2-2 3v1c1 1 1 2 2 3v1c0 2 1 3 2 4l1 1-1 1c1 1 2 2 2 4l-2-1c-1-1-3-2-4-4-1-1-2-2-2-3l-3-3-1-1h0c-1-1-1-3-1-5l-1 1-2-2c-1 0-1 1-2 0 0-1 1-2 1-3 0 0 1-1 1-2h1l4-5 1-2c-1-1-2-1-3-1l1-1c-1-1-1-1-2-1v-2h2v-2-2-1h2c0-1 0-1 1-2h1v-3h0c1-1 1-2 1-3l-1-1h1 3 0 2c1 0 1 0 2 1 1-1 2-1 3-2 1 0 2-1 2-1 1 0 1 0 2-1l1-1h1c0-1 0-1 1-1v-1l1-1c0 1 0 1 1 2v1l8-4 2-2c6-4 12-6 19-9l1-1h-1 1c0-1 1-1 1-1h2c1 0 1 0 1-1h-3v-1c5 1 9 0 13-2 2 0 4 0 6-1v-2z" class="a"></path><path d="M404 501l1-1v5h-1l-20 6c-6 2-12 6-19 9-1 0-1 0-2 1h-1l2-2c6-4 12-6 19-9l1-1h-1 1c0-1 1-1 1-1h2c1 0 1 0 1-1h-3v-1c5 1 9 0 13-2 2 0 4 0 6-1v-2z" class="b"></path><path d="M389 513l1-1h1c3 0 6-2 8-2l1 1-1 1-1 1h-2l-1 1c-2 0-3 1-4 1s-2 0-2 1h-1c-1 0-3 1-5 2v-1c-3 1-6 3-9 4-7 4-13 7-19 12-3 1-4 3-6 5s-4 4-5 6l-3 2v-1l-1 1-1-1 6-8h3v-1c9-10 22-16 33-21 3-1 5-3 8-3v1z" class="G"></path><path d="M345 537h3c-2 3-5 5-7 8l-1 1-1-1 6-8z" class="l"></path><path d="M389 513l1-1h1c3 0 6-2 8-2l1 1-1 1-1 1h-2l-1 1c-2 0-3 1-4 1s-2 0-2 1h-1c-1 0-3 1-5 2v-1c1-1 3-1 3-2h-2c1-1 4-2 5-2z" class="S"></path><path d="M405 505c0 9-2 19-4 28l-2-1v-1l-3-2h0c-4-4-8-5-13-6-2-1-5-1-6-2 2-1 4-2 7-3h-1c2-1 4-2 5-2h1c0-1 1-1 2-1s2-1 4-1l1-1h2l1-1 1-1-1-1c1-1 3-1 4-2s0-2 1-3h1z" class="V"></path><path d="M397 518c1-1 1-1 2 0h1c-1 0-2 0-2 1h-1v-1z" class="K"></path><path d="M398 519c0-1 1-1 2-1l1 1-1 2c-1 0-2-1-2-2z" class="Z"></path><path d="M400 511c1-1 2-1 3-1 0 1 0 2-1 3h0c-1 0-2-1-3-1l1-1z" class="U"></path><path d="M399 512c1 0 2 1 3 1l-5 3v-1c0-1 1-1 1-2l1-1z" class="n"></path><path d="M398 513c0 1-1 1-1 2v1l-2 1h-1 0c-1-1-1-1-1-2l2-1 1-1h2z" class="j"></path><path d="M398 513c0 1-1 1-1 2v1l-2 1c0-2 0-2 1-3v-1h2z" class="k"></path><path d="M397 518v1h1c0 1 1 2 2 2l1 1c-1 2 0 5-1 7l-1-1-2-4v-3c0-1-1-1-2-2l2-1z" class="T"></path><path d="M397 521c1 1 2 1 3 2 0 2 0 3-1 5l-2-4v-3z" class="K"></path><path d="M391 515c1 0 2-1 4-1l-2 1c0 1 0 1 1 2h0l1 2c1 1 2 1 2 2v3l2 4 1 1v2l-1 1v-1c-1-3-4-6-5-9-1-1-2-2-2-3v-1c-1-1-1-2-1-3z" class="H"></path><path d="M391 515c1 0 2-1 4-1l-2 1c0 1 0 1 1 2h0l1 2c1 1 2 1 2 2v3c-2-2-3-4-5-6v1-1c-1-1-1-2-1-3z" class="Z"></path><path d="M391 515c0 1 0 2 1 3v1c0 1 1 2 2 3 1 3 4 6 5 9l-3-2h0v-1c-2-4-6-6-10-9-1-1-1-1-2-1h-1c2-1 4-2 5-2h1c0-1 1-1 2-1z" class="U"></path><path d="M384 518c1 0 1 0 2 1 4 3 8 5 10 9v1c-4-4-8-5-13-6-2-1-5-1-6-2 2-1 4-2 7-3z" class="W"></path><path d="M355 533c6-5 12-8 19-12 3-1 6-3 9-4v1h1c-3 1-5 2-7 3s-3 2-4 2c-2 1-5 2-7 4h1c-1 1-3 2-4 2-3 3-6 5-8 8l-3 4c-1 3-3 5-5 7l-8 8 2-1v1l-1 1v1l-1 2h-1c-1 1-2 2-2 3v1c1 1 1 2 2 3v1c0 2 1 3 2 4l1 1-1 1c1 1 2 2 2 4l-2-1c-1-1-3-2-4-4-1-1-2-2-2-3l-3-3-1-1h0c-1-1-1-3-1-5h1c1-1 2-2 3-4h1c0-1 1-2 2-3 1-3 4-5 5-8l3-2c1-2 3-4 5-6s3-4 6-5z" class="K"></path><path d="M339 556l2-1v1l-1 1v1l-1 2h-1c-1 1-2 2-2 3v1h-1l-1-1-1 1-2-2 6-5 2-1z" class="e"></path><path d="M337 557h0l-1-1 12-13c1-1 2-2 4-2-1 3-3 5-5 7l-8 8-2 1z" class="H"></path><path d="M341 546l3-2c1-2 3-4 5-6s3-4 6-5c-3 4-6 8-10 12-3 3-5 6-9 9 1-3 4-5 5-8z" class="L"></path><path d="M333 557l1 1h0c-1 1-1 2-2 3h-1v1h0l2 2 1-1 1 1h1c1 1 1 2 2 3v1c0 2 1 3 2 4l1 1-1 1c1 1 2 2 2 4l-2-1c-1-1-3-2-4-4-1-1-2-2-2-3l-3-3-1-1h0c-1-1-1-3-1-5h1c1-1 2-2 3-4z" class="T"></path><path d="M340 574l-3-1v-1c1-1 2-1 3 0l1 1-1 1z" class="c"></path><path d="M333 557l1 1h0c-1 1-1 2-2 3h-1v1h0l2 2h0c0 1 0 1 1 2s1 2 2 3l1 1-1 1v-1c-1-2-2-3-4-5h0c-1 1-1 1-1 2l-1-1h0c-1-1-1-3-1-5h1c1-1 2-2 3-4z" class="C"></path><path d="M362 521h1c1-1 1-1 2-1l-7 4c-4 3-10 8-13 13l-6 8 1 1 1-1v1c-1 3-4 5-5 8-1 1-2 2-2 3h-1c-1 2-2 3-3 4h-1l-1 1-2-2c-1 0-1 1-2 0 0-1 1-2 1-3 0 0 1-1 1-2h1l4-5 1-2c-1-1-2-1-3-1l1-1c-1-1-1-1-2-1v-2h2v-2-2-1h2c0-1 0-1 1-2h1v-3h0c1-1 1-2 1-3l-1-1h1 3 0 2c1 0 1 0 2 1 1-1 2-1 3-2 1 0 2-1 2-1 1 0 1 0 2-1l1-1h1c0-1 0-1 1-1v-1l1-1c0 1 0 1 1 2v1l8-4z" class="N"></path><path d="M330 546l2-2 3 1-3 3c-1-1-2-1-3-1l1-1z" class="B"></path><path d="M332 544c2-2 4-3 6-5-1 3-2 4-3 6l-3-1z" class="M"></path><path d="M334 536h2v-1h0v-1c1 0 2 0 3 1h0c0 1 0 1 1 1-1 1-2 2-4 3h0c-1 1-4 1-5 0h-1v-1h2c0-1 0-1 1-2h1z" class="L"></path><path d="M352 523l1-1c0 1 0 1 1 2v1c0 1-1 1-2 1v-1c-1 1-1 2-1 3h-1c-1 2-3 4-5 6v-1l-3-3c1-1 2-1 3-2 1 0 2-1 2-1 1 0 1 0 2-1l1-1h1c0-1 0-1 1-1v-1z" class="B"></path><path d="M335 529h3 0 0c1 1 1 1 2 1 1 1 3 2 4 4v1l-2 2c-1 0-1-1-2-1h0 0c-1 0-1 0-1-1h0c-1-1-2-1-3-1v1h0v1h-2v-3h0c1-1 1-2 1-3l-1-1h1z" class="P"></path><path d="M332 551l4-6c1-2 3-3 5-5-1 1-2 3-2 5h0l1 1 1-1v1c-1 3-4 5-5 8-1 1-2 2-2 3h-1c-1 2-2 3-3 4h-1l-1 1-2-2c-1 0-1 1-2 0 0-1 1-2 1-3 0 0 1-1 1-2h1l4-5 1 1z" class="k"></path><path d="M327 555l4-5 1 1-3 6-3 3c-1 0-1 1-2 0 0-1 1-2 1-3 0 0 1-1 1-2h1z" class="I"></path><path d="M341 545v1c-1 3-4 5-5 8-1 1-2 2-2 3h-1c-1 2-2 3-3 4h-1l-1 1-2-2 3-3c1 0 1-1 2-1 2-3 5-7 9-10l1-1z" class="H"></path><path d="M331 556c-1 1-2 2-2 4l1 1h-1l-1 1-2-2 3-3c1 0 1-1 2-1z" class="l"></path><path d="M367 527h-1c2-2 5-3 7-4 1 0 2-1 4-2 1 1 4 1 6 2 5 1 9 2 13 6h0l3 2v1l2 1-2 6v1c-1 1-1 3-2 4h0c-1-4-10-9-13-11l3 6c1 2 3 4 5 5l-1 1c-2-1-4-1-5-1-1-1-2-1-2-2-1 1-1 1-2 1 1 2 3 3 4 5 0 0-1 0-1 1h0v1h-1c-2-1-2-2-3-3s-1-1-1 0v1c0 1-1 2-1 3l3 3h-1l-1 1-1-2-3 3h0l1 1c-1 0-1 1-1 1l3 2c1 1 2 1 2 3 1 2 0 3-1 5 0 1-1 2-2 2v1c-1 1-2 1-3 1-2 0-4 1-6 1h-1c-1 0-2 0-3-1h-1 0 0c0 1 1 1 1 1h1c1 1 0 0 1 0 1 1 2 1 2 2h0 1l1 1c2 0 3-1 4 0h1 2l-3 2c0 1-1 2-1 2-2 2-5 3-7 5-2 1-4 3-6 4-5-1-8-3-12-5v-1l-7-5c0-2-1-3-2-4l1-1-1-1c-1-1-2-2-2-4v-1c-1-1-1-2-2-3v-1c0-1 1-2 2-3h1l1-2v-1l1-1v-1l-2 1 8-8c2-2 4-4 5-7l3-4c2-3 5-5 8-8 1 0 3-1 4-2z" class="J"></path><path d="M362 532c1-1 3-2 4-2l1 1h0c-1 1-2 2-4 3l-1-1v-1zm5-5h-1c2-2 5-3 7-4 1 0 2-1 4-2 1 1 4 1 6 2-1 0-1 0-2 1-5 0-9 1-14 3z" class="L"></path><path d="M366 530c7-3 13-5 20-1 1 0 2 1 3 1h-2-2c-1-2-3-2-5-2 0 0-1 0-2 1h-1l-5 1c-2 0-3 1-5 1h0l-1-1z" class="M"></path><path d="M383 523c5 1 9 2 13 6h0l3 2v1l2 1-2 6c-1-2-3-3-4-5-1-1-1-1-1-2l1-1-1-1h-1c-2-2-4-3-6-4l-3-1-3-1c1-1 1-1 2-1z" class="O"></path><path d="M396 529l3 2v1l2 1-2 6c-1-2-3-3-4-5-1-1-1-1-1-2l1-1c1 1 3 3 4 3 0-2-2-3-3-4v-1z" class="b"></path><path d="M367 531c2 0 3-1 5-1l5-1h1c1-1 2-1 2-1 2 0 4 0 5 2-3-1-5-1-9-1-5 1-8 2-12 6l-3 3c-3 3-5 6-5 10 0 1 0 3 1 4h1 1c1 1 3 1 5 2 1 0 2 1 3 2v2c-2 1-2 2-5 2-2 0-3-2-5-3 0-1 0-1-1-2-1-3-1-6-1-10v-3h-1c1-2 1-3 2-4 1-2 3-4 6-6v1l1 1c2-1 3-2 4-3z" class="I"></path><path d="M356 538c1-2 3-4 6-6v1l1 1c-4 3-7 6-8 11v-3h-1c1-2 1-3 2-4z" class="P"></path><path d="M357 552h1 1c1 1 3 1 5 2h-1c2 1 2 1 2 2s0 1-1 1c-1 1-2 1-3 0-2 0-3-3-4-5z" class="H"></path><path d="M364 535h1v1l-2 3 1 1c0 2 1 3 2 4v1c1 1 3 1 4 2s1 1 1 2v1l-1 1c-2-1-5-1-8-1h-1c-1 1-2 1-2 2h-1-1c-1-1-1-3-1-4 0-4 2-7 5-10l3-3z" class="B"></path><path d="M361 550h0c-1-1-2-2-2-3s0-3 1-4l2 2c0 1 0 1 1 2-1 1-1 0-1 1v2h-1z" class="E"></path><path d="M364 535h1v1l-2 3v1c0 1-1 2-1 2 0 1 0 1-1 1h-1v-2c1-1 1-2 1-3l3-3z" class="W"></path><path d="M366 544v1c1 1 3 1 4 2s1 1 1 2v1l-1 1c-2-1-5-1-8-1v-2c0-1 0 0 1-1-1-1-1-1-1-2h2 0l2-1z" class="O"></path><path d="M366 544v1c1 1 3 1 4 2s1 1 1 2v1c-1-1-2-1-3-2-2 0-3-2-5-2v1h0c-1-1-1-1-1-2h2 0l2-1z" class="H"></path><path d="M364 535c4-4 7-5 12-6 4 0 6 0 9 1h2 2l6 4c1 2 3 3 4 5v1c-1 1-1 3-2 4h0c-1-4-10-9-13-11l3 6-1 1c-2-2-3-4-4-7-2-1-8 0-11 1-2 1-4 2-6 5v1h-1l-1-1 2-3v-1h-1z" class="e"></path><path d="M372 532h0c-1 1-2 2-4 2h1 0 0c1 0 2-1 2-1v1c-2 1-4 2-6 5v1h-1l-1-1 2-3 1-1c2-2 3-3 6-3z" class="o"></path><path d="M364 535c4-4 7-5 12-6 4 0 6 0 9 1h2 2l6 4c1 2 3 3 4 5v1c-3-1-6-4-9-6-6-4-12-4-18-2-3 0-4 1-6 3l-1 1v-1h-1z" class="G"></path><path d="M362 550c3 0 6 0 8 1l1 5h1c1 1 2 1 3 2h1l3 2c1 1 2 1 2 3 1 2 0 3-1 5 0 1-1 2-2 2v1c-1 1-2 1-3 1-2-1-5 0-8-1v-1c0-1 0-1 1-2-2 0-5-2-7-3-2-3-4-5-5-9v-1c1 1 1 1 1 2 2 1 3 3 5 3 3 0 3-1 5-2v-2c-1-1-2-2-3-2-2-1-4-1-5-2 0-1 1-1 2-2h1z" class="W"></path><path d="M361 565h2 1 2l1-1h0c1 0 2 0 3-1l1-1h0c0-1 1-1 1-1 1-1 1-2 2-2h1l2 2 2-1c1 1 2 1 2 3 1 2 0 3-1 5 0 1-1 2-2 2v1c-1 1-2 1-3 1-2-1-5 0-8-1v-1c0-1 0-1 1-2-2 0-5-2-7-3z" class="m"></path><path d="M377 567h1c1 0 1-1 2-2 0-1-1-2-1-3h0v-1l2 2c1 2 0 3-1 5-1 0-2 0-3-1z" class="b"></path><path d="M368 568c3 0 6 0 9-1 1 1 2 1 3 1 0 1-1 2-2 2v1c-1 1-2 1-3 1-2-1-5 0-8-1v-1c0-1 0-1 1-2z" class="C"></path><path d="M371 534c3-1 9-2 11-1 1 3 2 5 4 7l1-1c1 2 3 4 5 5l-1 1c-2-1-4-1-5-1-1-1-2-1-2-2-1 1-1 1-2 1 1 2 3 3 4 5 0 0-1 0-1 1h0v1h-1c-2-1-2-2-3-3s-1-1-1 0v1c0 1-1 2-1 3l3 3h-1l-1 1-1-2-3 3h0l1 1c-1 0-1 1-1 1h-1c-1-1-2-1-3-2h-1l-1-5 1-1v-1c0-1 0-1-1-2s-3-1-4-2v-1c-1-1-2-2-2-4h1v-1c2-3 4-4 6-5z" class="D"></path><path d="M381 547v-2c1 0 1 1 2 1l2 3h0v1h-1c-2-1-2-2-3-3z" class="g"></path><path d="M373 552v-1h1v-1h-1v-1c1 0 2 0 3-1h1 0l-1 1 1 2 1-1 1 1-3 3h0c1-1 1-2 1-3h0c-1 1-2 1-3 2-1 0-1 0-1-1z" class="F"></path><path d="M371 549c0-1 0-1 1-2v1 1c0 1 0 2 1 3 0 1 0 1 1 1 1-1 2-1 3-2h0c0 1 0 2-1 3h0l3-3h0l3 3h-1l-1 1-1-2-3 3h0l1 1c-1 0-1 1-1 1h-1c-1-1-2-1-3-2h-1l-1-5 1-1v-1z" class="e"></path><path d="M371 534c3-1 9-2 11-1 1 3 2 5 4 7l3 3c-1 1-5-2-6-3-1 1-2 3-4 3-3-1-2-3-4-5h-2c-1 2 1 4 2 6h-1c-2 1-4 0-6-1-1-1-2-2-3-4 2-3 4-4 6-5z" class="m"></path><path d="M355 537l1 1c-1 1-1 2-2 4h1v3c0 4 0 7 1 10v1c1 4 3 6 5 9 2 1 5 3 7 3-1 1-1 1-1 2v1c3 1 6 0 8 1-2 0-4 1-6 1h-1c-1 0-2 0-3-1h-1 0 0c0 1 1 1 1 1h1c1 1 0 0 1 0 1 1 2 1 2 2h0 1l1 1c2 0 3-1 4 0h1 2l-3 2c0 1-1 2-1 2h-1c-1 0-1 1-2 1-2 1-5 0-7 0-1-1-3-1-4-2l-1-5 1-1-4-3c-1-1-2-3-3-4-2-3-3-7-4-11 0-1 0-5-1-6l-1-1c2-2 4-4 5-7l3-4z" class="O"></path><path d="M350 553v-1c0-1 1-1 1-1 1 0 0-2 1-3v5c0 2 1 4 1 6l2 4-1-1c-3-2-3-6-4-9z" class="R"></path><path d="M356 570h4c1 1 2 3 3 4h1c2 2 6 3 8 3h2l1-1h1 2l-3 2c-5 1-11-1-15-5l-4-3z" class="C"></path><defs><linearGradient id="O" x1="349.557" y1="542.196" x2="352.8" y2="550.187" xlink:href="#B"><stop offset="0" stop-color="#131412"></stop><stop offset="1" stop-color="#313132"></stop></linearGradient></defs><path fill="url(#O)" d="M355 537l1 1c-1 1-1 2-2 4s-2 4-2 6c-1 1 0 3-1 3 0 0-1 0-1 1v1-1c-1 1-1 2-1 3 0-1 0-5-1-6l-1-1c2-2 4-4 5-7l3-4z"></path><path d="M354 542h1v3c0 4 0 7 1 10v1c-1 0-1-1-1-2h-1l1 4h-1v-2 2l-1 1c0-2-1-4-1-6v-5c0-2 1-4 2-6z" class="d"></path><path d="M352 553v-4h1c1 2 0 3 1 5l1 4h-1v-2 2l-1 1c0-2-1-4-1-6z" class="F"></path><path d="M360 573c4 4 10 6 15 5 0 1-1 2-1 2h-1c-1 0-1 1-2 1-2 1-5 0-7 0-1-1-3-1-4-2l-1-5 1-1z" class="W"></path><path d="M354 554h1c0 1 0 2 1 2 1 4 3 6 5 9 2 1 5 3 7 3-1 1-1 1-1 2v1c3 1 6 0 8 1-2 0-4 1-6 1-6-2-11-5-14-10l-2-4 1-1v-2 2h1l-1-4z" class="Y"></path><path d="M354 554h1c0 1 0 2 1 2 1 4 3 6 5 9 2 1 5 3 7 3-1 1-1 1-1 2v1l-1-1c-5-1-9-7-11-12l-1-4z" class="N"></path><path d="M347 548l1 1c1 1 1 5 1 6 1 4 2 8 4 11 1 1 2 3 3 4l4 3-1 1 1 5c1 1 3 1 4 2 2 0 5 1 7 0 1 0 1-1 2-1h1c-2 2-5 3-7 5-2 1-4 3-6 4-5-1-8-3-12-5v-1l-7-5c0-2-1-3-2-4l1-1-1-1c-1-1-2-2-2-4v-1c-1-1-1-2-2-3v-1c0-1 1-2 2-3h1l1-2v-1l1-1v-1l-2 1 8-8z" class="W"></path><path d="M338 560h0v3h1s1 1 1 2h0-1c1 2 2 4 3 5 1 2 3 3 4 4-2 0-3-1-4-3l-1-1v-1c-1 0-1-1-1-1h-1v1c1 1 2 1 2 3 1 2 3 3 4 4-2 0-3-2-4-3l-1-1c-1-1-2-2-2-4v-1c-1-1-1-2-2-3v-1c0-1 1-2 2-3z" class="G"></path><path d="M341 573c1 1 2 3 4 3 5 5 10 9 17 11l9-5c-2 0-3 1-4 1s-2-1-2-1c-2 1-4 2-6 2v-1c1 0 4-1 5-2h0c2 0 5 1 7 0 1 0 1-1 2-1h1c-2 2-5 3-7 5-2 1-4 3-6 4-5-1-8-3-12-5v-1l-7-5c0-2-1-3-2-4l1-1z" class="h"></path><path d="M346 574c-1-1-3-2-4-4-1-1-2-3-3-5h1c2 4 4 6 8 8v-1h1v-1c0-1 1-1 1-2h1l1-1h-1c1-1 2-1 2-2 1 1 2 3 3 4l4 3-1 1 1 5c1 1 3 1 4 2h0c-2 0-4 0-5-1-2-1-5-2-8-3-1-1-3-2-5-3z" class="N"></path><path d="M349 571c1 1 1 1 1 2v1h-1v-2-1z" class="f"></path><path d="M353 566c1 1 2 3 3 4l4 3-1 1-1-1-1 1v1 1s-1-1-1 0v1 1c-2-1-3-1-4-2v-1c0-1 1-2 3-3h0l-2-1c-1-1-1-1-1-3h-1c1-1 2-1 2-2z" class="G"></path><path d="M347 548l1 1c1 1 1 5 1 6 1 4 2 8 4 11 0 1-1 1-2 2h1l-1 1h-1c0 1-1 1-1 2v1h-1v1c-4-2-6-4-8-8h0c0-1-1-2-1-2h-1v-3h0 1l1-2v-1l1-1v-1l-2 1 8-8z" class="W"></path><path d="M348 572c0-1 1-4 1-6h1l1 2h1l-1 1h-1c0 1-1 1-1 2v1h-1z" class="o"></path><path d="M347 548l1 1a30.44 30.44 0 0 1-8 8l1-1v-1l-2 1 8-8z" class="J"></path><path d="M338 560h1l1-2c0 2 0 4 1 5l1 1 1-2h0c0-1 1-2 2-2s2 0 2 1c1 1 1 2 1 3-1 1-1 1-3 2-2 0-3 0-5-1 0-1-1-2-1-2h-1v-3h0z" class="o"></path><path d="M343 562v1c1 0 1-2 2-2h1c0 1 0 2-1 3h-2-1l1-2z" class="F"></path><path d="M274 271v-1c1 1 2 1 3 1h4v1c-1 0-1 0-1 1h1 2 3l3 2c-1 1-4 0-3 2 1 1 1 2 3 2l1-1 7-3c0-1 1-1 2-1l-2 1c0 1 0 1 1 2l1-1v-1h2c1-1 2-1 3-1 1 1 1 1 2 1h0c2 0 2 0 3 1 1 0 2 0 3-1v2h-2c-1 0-2 1-3 1v1c-1 0-1 0-2 1h1l3-1s1 0 1 1l-9 3h-2c0 1 1 1 1 1v1l-1 1h-2v1c1 1 0 1 1 2 1 2 3 2 4 3s2 1 2 2h0-2-1l-5 1h0v1h-1c-1 1-3 2-4 2l1 1-1 1h3l1 1c-1 0-2 1-3 1 0 1 1 1 2 1-2 1-3 2-4 3h-1l-2 2-1-1v1l-2 2-2 2 1 1v1c1 1 2 0 3 0v1l-1 1h1 0l-2 1h-3c-1 1-1 2-3 2v2c-1 1-2 2-2 3-1 1-2 1-2 2h0c-1 1-2 2-3 2 0 3-1 6-1 9v3h1-2 0-7 0-3-5-10-9l3-12c1-4 3-7 4-10h1v1l1-1c1 0 1-1 2-1v-1h1v1c1-1 1-1 1-2l1-1v-2h0c2-1 2-2 3-3 0-1 1-2 1-3h0c0-1 0-1 1-1v-3c1-2 2-5 3-7s2-3 3-5 3-6 5-8l1 1v-1-1c0-1 1-1 1-2s1-2 2-3v-1h0c1-1 1-1 1-2 1 0 1-1 2-1l1-2 1 1z" class="i"></path><path d="M256 311v2s1 1 2 1l-1 1-1 6c-1-3-1-5-1-8l1-2z" class="K"></path><path d="M249 312h0c2-1 2-2 3-3 0-1 1-2 1-3h0c0-1 0-1 1-1-1 3-1 7-3 10v2h1l-1 1h-1v-1c-1-1-1-2-2-2v2h-1c1-1 1-1 1-2l1-1v-2z" class="S"></path><path d="M257 307h0c-1 1-3 5-4 5 1-3 3-10 6-12h0 0c0 3 1 3 2 5v5c0 2-1 4 0 6v5h0v-4h-1c0 1-1 2-2 3 0-2 0-4-1-5l1-1c-1 0-2-1-2-1v-2l1-4z" class="D"></path><path d="M257 307h0c1-1 1-1 1-2h0c1 1 2 1 2 2s0 1-1 2c0 1-1 3-1 5-1 0-2-1-2-1v-2l1-4z" class="a"></path><path d="M258 320c1-1 2-2 2-3h1v4 2h1v2h1c-1 2-2 6-1 8s1 4 2 6l-2 1h0-3c-2-1 0-4-1-6 1-1 0-1 1-2-2-3-2-9-1-12z" class="J"></path><path d="M262 340l-1-2s-1 0-1-1 0-1-1-2h0v-2h0c1-1 2-1 3 0 1 2 1 4 2 6l-2 1h0z"></path><path d="M258 320c1-1 2-2 2-3h1v4 2l-1 1v1c-1 2-1 4-1 5h1v-2h1s0 1-1 2v1 1h-1c-2-3-2-9-1-12z" class="X"></path><path d="M260 325h-2v-2h1 1v1 1z" class="T"></path><path d="M247 317h1v-2c1 0 1 1 2 2v1h1l1-1h0c1 1 0 4 0 6 0 0-1 1-1 2s-1 2 0 3c0-3 1-5 2-7 1 0 1 1 1 1v1c-1 1-1 2-1 3-1 1-1 1-1 2l1-2h1v2 1c1 1 1 3 1 5 1-1 1-2 1-2l2 2c1 2-1 5 1 6h-5-10-9l3-12c1-4 3-7 4-10h1v1l1-1c1 0 1-1 2-1v-1h1v1z" class="n"></path><path d="M248 319l1-1 2 2c-1 3-2 5-2 7v1 1c1-1 1-2 1-2 0-1 0-2 1-2 0 1-1 2 0 3v1 1c-2 1-1 3-2 5v4h0-1c0-2 0-3-1-5v-1-1c0-1 1-2 1-4 0-1 2-5 1-6 0 0-1 0-1-1v-2z" class="T"></path><path d="M247 317h1v-2c1 0 1 1 2 2v1h1l1-1h0c1 1 0 4 0 6 0 0-1 1-1 2-1 0-1 1-1 2 0 0 0 1-1 2v-1-1c0-2 1-4 2-7l-2-2-1 1-1-1v4c-1 0-1 1-1 2l-1 1v-3l1-1v-2h-1v2l-1-1h-1v-1l1-1c1 0 1-1 2-1v-1h1v1z" class="U"></path><path d="M245 321v-2h1v2l-1 1v3l1-1c0-1 0-2 1-2v-4l1 1v2c0 1 1 1 1 1 1 1-1 5-1 6h-1c0-1 0-2 1-4v-1h0 0c-1 0-1 0-1 1s-1 2-1 2c0 1 0 2-1 3s1 4 0 6c-1 0-1 0-1 1v3h-1v-2c-1 1-1 2-2 2v-1c0-3 0-7 1-11h1c0-1 1-2 1-4 0 0 0-1 1-2z" class="P"></path><path d="M242 318h1v1 1h1l1 1c-1 1-1 2-1 2 0 2-1 3-1 4h-1c-1 4-1 8-1 11v1c1 0 1-1 2-2v2h1v-3h1c0 1 0 3-1 4h-9l3-12c1-4 3-7 4-10z"></path><path d="M243 320h1l1 1c-1 1-1 2-1 2 0 2-1 3-1 4h-1 0c0 1 0 1-1 2 0 1-1 3-1 4-1-4 2-10 3-13z" class="n"></path><path d="M276 292c1-1 1-1 2-1v2h1l1 1c2 0 4-1 5-2l1 1c-1 0-1 1-2 1h-1c0 1 0 2-1 2l1 1v1c-1 0-1 1-1 1 1 0 2 1 3 0h2c2 1 2 0 4-1l1 1-1 1h3l1 1c-1 0-2 1-3 1 0 1 1 1 2 1-2 1-3 2-4 3h-1l-2 2-1-1v1l-2 2-2 2 1 1v1c1 1 2 0 3 0v1l-1 1h1 0l-2 1h-3c-1 1-1 2-3 2v2c-1 1-2 2-2 3-1 1-2 1-2 2h0c-1 1-2 2-3 2 0 3-1 6-1 9v3h1-2 0-7l2-1c-1-2-1-4-2-6s0-6 1-8c0-1 0-2 1-3l2-4-1-1-1 1v-1c0-2-1-4 0-6h0v-1l-1-1c1-1 1-2 1-3l-1 1h-1c1-2 1-2 2-3h0-1l-1-1c0-1 1-2 2-3h1v-1l3-2c0-1 1-2 3-2v-1c1 0 2 0 2-1 1-1 2-1 3-1z" class="e"></path><defs><linearGradient id="P" x1="265.027" y1="328.351" x2="261.446" y2="330.025" xlink:href="#B"><stop offset="0" stop-color="#464845"></stop><stop offset="1" stop-color="#5f5c5f"></stop></linearGradient></defs><path fill="url(#P)" d="M263 325l1-1c0 1 1 2 1 3h-1c-1 3 0 6 0 9 1 1 1 2 1 3h-1c-1-2-1-4-2-6s0-6 1-8z"></path><path d="M266 327l1 1v1c-1 1-1 2 0 2l1-1c1 2 0 7 0 9h-1 0c0-1-1-2-1-2-1-1-1-1-1-2-1-3 0-5 1-8z" class="K"></path><path d="M276 310c1 1 1 1 1 2l2 1c-1 1 0 1-1 1 0 1-2 3-2 3-2 0-3 2-5 3 1-1 2-3 3-4-1 0-2 2-3 3s-1 3-2 5c0-3 1-5 1-7l2-3v-1c1-1 0 0 2 0l2-3z" class="T"></path><path d="M272 307c1-1 2-2 3-2l-1 1h3 1v1s-1 0-1 1c-1 0-1 0 0 1l-1 1-2 3c-2 0-1-1-2 0v1l-2 3v-1l-1 1v2c-1 1-2 2-2 3l-1 1v-2s0-1 1-1c-2 1-2 2-3 3h0v-1l2-4-1-1-1 1v-1c0-2-1-4 0-6v6c1-1 2-4 3-4v-1l2-2h0c0-1 1-1 2-2 0 0 1 0 1-1z" class="c"></path><path d="M272 307c1-1 2-2 3-2l-1 1h3c-2 1-3 2-5 3l-3 3v2c0 1-1 1-1 1 0 1 0 2-1 3h0v2c-2 1-2 2-3 3h0v-1l2-4-1-1-1 1v-1c0-2-1-4 0-6v6c1-1 2-4 3-4v-1l2-2h0c0-1 1-1 2-2 0 0 1 0 1-1z" class="J"></path><path d="M285 307h1v1l-2 2-2 2 1 1v1c1 1 2 0 3 0v1l-1 1h1 0l-2 1h-3c-1 1-1 2-3 2v2c-1 1-2 2-2 3-1 1-2 1-2 2h0c-1 1-2 2-3 2 0 3-1 6-1 9v3h1-2 0v-1c-1-3 0-7 1-10s1-6 3-9l3-3s2-2 2-3c1 0 0 0 1-1l5-5 1-1z" class="K"></path><path d="M272 324c1 1 1 2 2 2h0c-1 1-2 2-3 2l1-4z" class="C"></path><path d="M276 319h2l-1 1 1 1c-1 1-2 2-2 3-1 1-2 1-2 2-1 0-1-1-2-2 1-2 3-3 4-5z" class="f"></path><path d="M282 312l1 1v1c1 1 2 0 3 0v1l-1 1h1 0l-2 1h-3c-1 1-1 2-3 2v2l-1-1 1-1h-2l3-3 2-2c0-1 1-2 1-2z" class="O"></path><path d="M282 312l1 1v1c1 1 2 0 3 0v1l-1 1h1 0l-2 1c0-1 0-2-1-2h-2c0 1-1 1-2 1h0l2-2c0-1 1-2 1-2z" class="E"></path><path d="M291 300h3l1 1c-1 0-2 1-3 1 0 1 1 1 2 1-2 1-3 2-4 3h-1l-2 2-1-1h-1l-1 1-5 5-2-1c0-1 0-1-1-2l1-1c-1-1-1-1 0-1 0-1 1-1 1-1v-1h-1-3l1-1c3-2 5-2 8-3h1 0c2-1 5-1 7-2z" class="I"></path><path d="M291 300h3l1 1c-1 0-2 1-3 1 0 1 1 1 2 1-2 1-3 2-4 3h-1v-1c0-1 1-1 1-2h0c-1 0-2 0-2-1h-1-3 0c2-1 5-1 7-2z" class="J"></path><path d="M275 305c3-2 5-2 8-3l-4 4c2 0 4 0 6 1l-1 1-5 5-2-1c0-1 0-1-1-2l1-1c-1-1-1-1 0-1 0-1 1-1 1-1v-1h-1-3l1-1z" class="C"></path><path d="M277 309c1 0 2-1 2-2 1 0 1 1 2 1h0c1-1 2-1 3-1v1l-5 5-2-1c0-1 0-1-1-2l1-1z" class="X"></path><path d="M276 292c1-1 1-1 2-1v2h1l1 1c2 0 4-1 5-2l1 1c-1 0-1 1-2 1h-1c0 1 0 2-1 2l1 1v1c-1 0-1 1-1 1 1 0 2 1 3 0h2c2 1 2 0 4-1l1 1-1 1c-2 1-5 1-7 2h0-1c-3 1-5 1-8 3-1 0-2 1-3 2 0 1-1 1-1 1-1 1-2 1-2 2h0l-2 2v1c-1 0-2 3-3 4v-6h0v-1l-1-1c1-1 1-2 1-3l-1 1h-1c1-2 1-2 2-3h0-1l-1-1c0-1 1-2 2-3h1v-1l3-2c0-1 1-2 3-2v-1c1 0 2 0 2-1 1-1 2-1 3-1z" class="I"></path><path d="M277 297l1-1v1h0c1 0 1 1 2 1s2 0 3-1v1c-1 0-1 1-1 1h-2-3c-1 1-1 1-2 1 1-1 1-2 2-3zm-6 7c2-1 4-2 5-2 0 1-1 1-2 2h0 0c-1 1-2 2-2 3s-1 1-1 1c-1 1-2 1-2 2-1-1 0-1-1-1h0c0-2 2-3 4-4v-1h-1z" class="E"></path><path d="M287 299c2 1 2 0 4-1l1 1-1 1c-2 1-5 1-7 2h0-1c-3 1-5 1-8 3-1 0-2 1-3 2 0-1 1-2 2-3 2 0 4-2 6-2 3-1 5-1 7-3z" class="S"></path><path d="M276 292c1-1 1-1 2-1v2h1l1 1c2 0 4-1 5-2l1 1c-1 0-1 1-2 1h-1c0 1 0 2-1 2l1 1c-1 1-2 1-3 1s-1-1-2-1h0v-1l-1 1c-2-1-4-1-6-1l-1 1c0 1 0 1 1 1h0-2l-1-1c0-1 1-2 3-2v-1c1 0 2 0 2-1 1-1 2-1 3-1z" class="C"></path><path d="M285 292l1 1c-1 0-1 1-2 1h-1c0 1 0 2-1 2l1 1c-1 1-2 1-3 1s-1-1-2-1h0v-1s1 0 2-1h0-1-2-2c1-1 2-2 3-2h1l1 1c2 0 4-1 5-2z" class="V"></path><path d="M265 299l3-2 1 1h2c0 2 0 2-1 3l-1 2c1 0 2 0 2 1h1v1c-2 1-4 2-4 4h0c1 0 0 0 1 1h0l-2 2v1c-1 0-2 3-3 4v-6h0v-1l-1-1c1-1 1-2 1-3l-1 1h-1c1-2 1-2 2-3h0-1l-1-1c0-1 1-2 2-3h1v-1z" class="U"></path><path d="M270 301l-1 2c1 0 2 0 2 1h1-2l-5 5v1l-1 1v-1l-1-1c1-1 1-2 1-3l-1 1h-1c1-2 1-2 2-3l1 1 1-1h2v-1l2-2z" class="O"></path><path d="M265 299l3-2 1 1h2c0 2 0 2-1 3l-2 2v1h-2l-1 1-1-1h0-1l-1-1c0-1 1-2 2-3h1v-1z" class="c"></path><path d="M265 299l3-2 1 1h2c0 2 0 2-1 3l-2 2v1h-2l1-1c0-1 0-1 1-1 0-1 0-1 1-2l-1-1v1h-2l-1-1z" class="E"></path><path d="M274 271v-1c1 1 2 1 3 1h4v1c-1 0-1 0-1 1h1 2 3l3 2c-1 1-4 0-3 2 1 1 1 2 3 2l1-1 7-3c0-1 1-1 2-1l-2 1c0 1 0 1 1 2l1-1v-1h2c1-1 2-1 3-1 1 1 1 1 2 1h0c2 0 2 0 3 1 1 0 2 0 3-1v2h-2c-1 0-2 1-3 1v1c-1 0-1 0-2 1h1l3-1s1 0 1 1l-9 3h-2c0 1 1 1 1 1v1l-1 1h-2v1c1 1 0 1 1 2 1 2 3 2 4 3s2 1 2 2h0-2-1l-5 1h0v1h-1c-1 1-3 2-4 2-2 1-2 2-4 1h-2c-1 1-2 0-3 0 0 0 0-1 1-1v-1l-1-1c1 0 1-1 1-2h1c1 0 1-1 2-1l-1-1c-1 1-3 2-5 2l-1-1h-1v-2c-1 0-1 0-2 1-1 0-2 0-3 1 0 1-1 1-2 1v1c-2 0-3 1-3 2l-3 2v1h-1l1-3-1-2-1-1c-1 1-2 3-3 4 0-2 1-4 2-6v-1l-1-1c1-1 1-2 2-3l3-4v-1-1c0-1 1-1 1-2s1-2 2-3v-1h0c1-1 1-1 1-2 1 0 1-1 2-1l1-2 1 1z" class="T"></path><path d="M274 280v-1c1-1 3-1 4-2 1 0 2-1 3 0h1v1h-1c-1 0-1 1-2 2l-4 1-1-1z" class="a"></path><path d="M274 280h2c2-2 3-2 5-2-1 0-1 1-2 2l-4 1-1-1z" class="K"></path><path d="M279 280c1 0 2-1 3 0-1 1-4 2-5 2-2 1-5 2-6 4-3 1-4 3-6 5-1 0-1 0-2 1 1-1 1-1 1-2 3-3 7-6 11-9l4-1z" class="c"></path><path d="M274 271v-1c1 1 2 1 3 1l2 1h0c-2 1-3 1-5 2 0 0-1 0-1 1-1 0-2 1-3 1 0 1 0 1 1 2h-1c-1 0-1-1-1-2v-1h0c1-1 1-1 1-2 1 0 1-1 2-1l1-2 1 1z" class="Z"></path><path d="M273 270l1 1v2h0 0c-1 0-2 0-3 1l-1 1h-1c1-1 1-1 1-2 1 0 1-1 2-1l1-2z" class="P"></path><path d="M271 278c1-1 3-3 4-3h1s1 0 1-1h3 0c-2 2-3 2-5 2l-3 3c-2 1-4 3-5 5l-3 3h-1l3-4v-1-1c0-1 1-1 1-2s1-2 2-3c0 1 0 2 1 2h1z" class="g"></path><path d="M269 276c0 1 0 2 1 2l-4 4v-1c0-1 1-1 1-2s1-2 2-3z" class="K"></path><path d="M267 284v1c0 1-3 4-3 5h0c0 1 0 1-1 2 1-1 1-1 2-1 2-2 3-4 6-5v1c-1 1-7 6-8 7s-2 3-3 4c0-2 1-4 2-6v-1l-1-1c1-1 1-2 2-3h1l3-3z" class="V"></path><path d="M283 273h3l3 2c-1 1-4 0-3 2 1 1 1 2 3 2l1-1 7-3c0-1 1-1 2-1l-2 1c0 1 0 1 1 2h0c-1 0-1 0-1 1-1 1-1 1-2 1-2 2-5 4-7 4h-5 0c0 1-1 1-1 1h-1c-1 1-3 1-4 1-1-1-1-1-2 0-2 0-3 1-4 2v-1c1-2 4-3 6-4 1 0 4-1 5-2-1-1-2 0-3 0 1-1 1-2 2-2h1v-1h-1c0-1 1-2 1-3h0l-1-1h2z" class="D"></path><path d="M281 277l3-3v1 1c-1 1-1 2-2 2v-1h-1z" class="V"></path><path d="M283 273h2v1h-1l-3 3c0-1 1-2 1-3h0l-1-1h2z" class="S"></path><path d="M286 273l3 2c-1 1-4 0-3 2 1 1 1 2 3 2l1-1v1c-1 0-2 1-3 1v-1h-2l-1-1c1-1 2-3 2-4v-1z" class="N"></path><path d="M288 283h1l-3 1v1h1c0 1 0 1-1 1 0 1-1 1-2 1l-1 1c-2 0-3 1-6 1h-1c-1 0-1 1-1 1l-2 1c1 0 2 0 3 1-1 0-2 0-3 1 0 1-1 1-2 1v1c-2 0-3 1-3 2l-3 2v1h-1l1-3-1-2-1-1c1-1 7-6 8-7s2-2 4-2c1-1 1-1 2 0 1 0 3 0 4-1h1s1 0 1-1h0 5z" class="C"></path><path d="M272 289c0-1 1-1 2-2h0c1 1 1 1 0 1l1 1h0 1c-1 0-1 1-1 1l-2 1c1 0 2 0 3 1-1 0-2 0-3 1 0 1-1 1-2 1v1c-2 0-3 1-3 2l-3 2v1h-1l1-3-1-2c2-3 5-4 8-6z" class="V"></path><path d="M265 297c1-1 3-2 5-3l1 1c-2 0-3 1-3 2l-3 2v1h-1l1-3z" class="P"></path><path d="M272 289c0-1 1-1 2-2h0c1 1 1 1 0 1l1 1h0 1c-1 0-1 1-1 1l-2 1c-2 0-2 1-4 2h0c1-1 3-3 3-4z" class="J"></path><path d="M299 276v-1h2c1-1 2-1 3-1 1 1 1 1 2 1h0c2 0 2 0 3 1 1 0 2 0 3-1v2h-2c-1 0-2 1-3 1v1c-1 0-1 0-2 1h1l3-1s1 0 1 1l-9 3h-2c0 1 1 1 1 1v1l-1 1h-2v1c1 1 0 1 1 2 1 2 3 2 4 3s2 1 2 2h0-2-1l-5 1h0v1h-1c-1 1-3 2-4 2-2 1-2 2-4 1h-2c-1 1-2 0-3 0 0 0 0-1 1-1v-1l-1-1c1 0 1-1 1-2h1c1 0 1-1 2-1l-1-1c-1 1-3 2-5 2l-1-1h-1v-2c-1 0-1 0-2 1-1-1-2-1-3-1l2-1s0-1 1-1h1c3 0 4-1 6-1l1-1c1 0 2 0 2-1 1 0 1 0 1-1h-1v-1l3-1h-1c2 0 5-2 7-4 1 0 1 0 2-1 0-1 0-1 1-1h0l1-1z" class="p"></path><path d="M294 286h2c0 1 0 1 1 1 1 1 0 1 1 2h-1l-1-1h-2v-1-1z" class="T"></path><path d="M286 291v-2h1c2-1 2-1 4-1h0c1-1 2-1 3-2v1 1h2l-3 1c-1 0-2-1-3 0v1c-1 0-3 1-4 1z" class="U"></path><path d="M276 289h1l-1 1v1h1c2-1 4-2 6-1 0 1-1 1-1 2l-3 1h-1v-2c-1 0-1 0-2 1-1-1-2-1-3-1l2-1s0-1 1-1z" class="O"></path><path d="M286 291c1 0 3-1 4-1v-1c1-1 2 0 3 0l-1 1c-1 0-2 1-3 2v1h0 1v1c-1 0-1 0-2 2 0 0-1 0-1 1v-1c-1-2-2-1-3-2 1 0 1-1 2-1l-1-1c-1 1-3 2-5 2l-1-1 3-1 4-1z" class="P"></path><path d="M285 292h2c1-1 1-1 2-1l-2 2h2 1v1c-1 0-1 0-2 2 0 0-1 0-1 1v-1c-1-2-2-1-3-2 1 0 1-1 2-1l-1-1z" class="U"></path><defs><linearGradient id="Q" x1="293.437" y1="278.564" x2="300.612" y2="282.499" xlink:href="#B"><stop offset="0" stop-color="#5c5c5a"></stop><stop offset="1" stop-color="#727174"></stop></linearGradient></defs><path fill="url(#Q)" d="M299 276v-1h2c1-1 2-1 3-1 1 1 1 1 2 1h0c2 0 2 0 3 1 1 0 2 0 3-1v2h-2c-1 0-2 1-3 1-3 0-4 1-6 3-2 1-3 2-5 3-1 1-3 2-5 2 0 0 0 1-1 1h-1-2 0s0-1 1-1h0c1 0 1-1 1-1h2v-1h-1-1v-1h-1c2 0 5-2 7-4 1 0 1 0 2-1 0-1 0-1 1-1h0l1-1z"></path><path d="M299 276v-1h2c1-1 2-1 3-1 1 1 1 1 2 1h0c2 0 2 0 3 1l-2 1c-1 0-3 0-4 1-4 1-6 3-10 3-1 1-3 2-4 3v-1h-1c2 0 5-2 7-4 1 0 1 0 2-1 0-1 0-1 1-1h0l1-1z" class="N"></path><path d="M299 276v-1h2c1-1 2-1 3-1 1 1 1 1 2 1h0c2 0 2 0 3 1l-2 1c0-1 0-1-1-1-2 1-4 1-7 0z" class="h"></path><path d="M293 289l3-1 1 1h1c1 2 3 2 4 3s2 1 2 2h0-2-1l-5 1h0v1h-1c-1 1-3 2-4 2-2 1-2 2-4 1h-2c-1 1-2 0-3 0 0 0 0-1 1-1v-1l-1-1c1 0 1-1 1-2h1c1 1 2 0 3 2v1c0-1 1-1 1-1 1-2 1-2 2-2v-1h-1 0v-1c1-1 2-2 3-2l1-1z" class="Z"></path><path d="M295 294l3-2c1 0 2 1 3 2l-5 1h0-2l1-1z" class="M"></path><path d="M293 289l3-1 1 1c-2 2-5 3-8 4v-1c1-1 2-2 3-2l1-1zm-5 7c2-1 4-1 5-2h-2c1-1 2-1 3-1l1 1-1 1h2v1h-1c-1 1-3 2-4 2-2 1-2 2-4 1h-2c-1 1-2 0-3 0 0 0 0-1 1-1v-1l-1-1c1 0 1-1 1-2h1c1 1 2 0 3 2v1c0-1 1-1 1-1z" class="Y"></path><path d="M285 299h0c1-1 8-3 9-3h1c-1 1-3 2-4 2-2 1-2 2-4 1h-2z" class="h"></path><path d="M283 297l-1-1c1 0 1-1 1-2h1c1 1 2 0 3 2v1c-2 0-3 0-4 1v1h2c-1 1-2 0-3 0 0 0 0-1 1-1v-1z" class="X"></path><path d="M245 376h11l1 1h0-2c0 1 0 2-1 2v2c-1 0-1 1-1 2s0 2-1 3v3c0 2 1 4 1 6 0 1 0 2 1 2v2l1 1v1h0v3 5h0c1-2 0-4 1-7h0c2 2 0 4 1 6l1 1-1 1v4c-1 2-1 4-1 6v4l-1 3-1 1c0 1 1 3 1 5-1 0-1 0-2-1 0 3 1 6 1 8l1 1c0 2 1 2 2 3l2 1v1c0 1 0 1-1 1-2 0-2 0-3 1 0 1 0 1 1 1v1l-2 1v2s0 1 1 1l1 1h0c1 1 1 1 1 2h1l-1-1 1-1h0l1 1h0l1 1 1 1v1c1 1 2 1 3 1v-1l2 2c1 0 2 0 2 1l1-1 2 2-2 1h0l-2-2c-2 2-3 3-5 3h-1c-1-1-2-1-3-1l-2-1c-2 0-4 2-6 2v1l-1 1v1c-1 1 0 1-1 0v2h0v1c-1 0-1 0-1 1l1 1h-1v1c-2 0-4 1-4 2v1c1 1 1 0 2 0l1 1v1c1 0 2 0 4-1 0 0 1 0 2-1h1 0 1 2s1 0 1-1l1 1 1 1v3h0v-1c-2 1-2 6-3 8v2 1h-3v1l-14-2c0-1-1-1-1-2l-2-2h-2l-1 1c-2-2-2-2-5-2v-5h1c0-2-1-6 0-8s1-4 2-5v-6c-1-1-1-2-1-3v-1-5c0-1 0-1-1-2v-2-3-51h0l2-2v-1c1 0 1-1 1-1-1-2 1-10 1-12 1 0 1-1 2-1h-3c4-1 9-1 13-1z"></path><path d="M248 415l1 1v1c-1 2 0 4-1 6 0 1 0 0-1 1-1-4 0-6 1-9z" class="Z"></path><path d="M251 380l2-1v1l1 1c-1 0-1 1-1 2-1 2-2 4-2 6v-1-5h-1v1c-1 0-1 0-1-1 1-1 2-2 2-3z" class="B"></path><path d="M251 412v-14c1 1 1 3 1 5 0 4 0 8-1 12-1-1 0-2 0-3z" class="L"></path><path d="M229 445c0 1 1 1 2 2s1 2 1 3l1 1v-2c1 1 1 1 0 3 0 1 1 3 1 4h-1c0-1 0-1-1-2h0c0-1 0-2-1-2h-1c0-1 0-1-1-2v-2-3z" class="E"></path><path d="M233 378c2 0 3-1 4 0v2h0c-2 4-3 8-3 13-1-2-1-4-1-7l1-1c-1 0-1 1-1 1v1c0 1-1 2-1 3-1-2 1-10 1-12z" class="T"></path><path d="M251 412c0 1-1 2 0 3v7l1 1c0 2 0 3-1 4 0 1 0 2 1 3 0 1 0 2-1 2l-1 1c-1-1 0-2-1-3v1l-1 4h-1v-4c1-1 1-1 1-2h0l1-2c0-1-1-3 0-4v-5c0-2 1-5 2-6z" class="U"></path><path d="M249 427h1l1-4h0v4c0 1 0 2 1 3 0 1 0 2-1 2l-1 1c-1-1 0-2-1-3v1l-1 4h-1v-4c1-1 1-1 1-2h0l1-2z" class="b"></path><path d="M243 387h0v3c1 3 0 6 0 9h0c1-1 1-2 1-4 0-1 1-2 1-3h1l-2 11v1c0 3-1 7-2 10 0-5 1-10 0-14v-7c0-1-1-2 0-3 0-1 0-2 1-3z" class="L"></path><path d="M245 376h11l1 1h0-2c0 1 0 2-1 2v2l-1-1v-1l-2 1c0 1-1 2-2 3 0 1 0 1 1 1l-1 1v-2c-1 1-1 2-2 2v-1c-1-1-1-2-1-3l-1-1 1-1v-2c-1 0-1 1-1 1h-2v-1c1 0 2-1 2-1z" class="E"></path><path d="M251 380c0-2 0-3 1-3h2v3h-1v-1l-2 1z" class="Y"></path><path d="M247 384v-3c1-2 1-3 3-4h0c0 2-1 4-1 6-1 1-1 2-2 2v-1z" class="L"></path><path d="M237 431c1-4 2-9 4-12h1v1s0 1 1 1v3s-1 0-1 1v-2h-1c0 2-1 4-1 6h0c1-1 1-1 1-2v-2h1c0 2 0 4-1 6s-1 6-1 9c0 1 0 3-1 4h0 0c1 1 1 1 1 3-1 0-1 0-2 1v-2c-3-5-1-10-1-15z" class="S"></path><path d="M238 446c0-3 0-5-1-8 1 2 2 3 2 5v1h0c1 1 1 1 1 3-1 0-1 0-2 1v-2z" class="J"></path><path d="M252 403h1v-2l1-1 1 1v3 5h0c1-2 0-4 1-7h0c2 2 0 4 1 6l1 1-1 1v4c-1 2-1 4-1 6v4l-1 3-1 1c0 1 1 3 1 5-1 0-1 0-2-1h0c0-1 0-1-1-2s-1-2-1-3c1-1 1-2 1-4l-1-1v-7c1-4 1-8 1-12z" class="R"></path><path d="M255 409c1-2 0-4 1-7h0c2 2 0 4 1 6l1 1-1 1v4c-1 2-1 4-1 6v4l-1 3-1 1c0 1 1 3 1 5-1 0-1 0-2-1h0v-5-5c1-1 1-3 1-4 0-3 1-6 1-9z" class="U"></path><path d="M254 428c0-4 0-8 2-12 0-1 0-4 1-6v4c-1 2-1 4-1 6v4l-1 3-1 1z" class="N"></path><path d="M232 377c4-1 9-1 13-1 0 0-1 1-2 1v1c-1 2-1 3-1 5l-1 1v5c-1 2 0 4 0 5v6c1 5 0 10-1 15-1-5 0-12 0-17v-3l-1-1c0-1 0-5 1-6h-1c-1 1-3 10-3 13l-1-1v-2c-2-4 0-8 1-11-1-2 1-5 1-7h0v-2c-1-1-2 0-4 0 1 0 1-1 2-1h-3z" class="j"></path><path d="M237 388v-1c0-3 1-5 2-8 0 0 0-1 1-1 1-1 1 0 2 0v1c-1 1-1 1-1 2s0 2-1 3v3l-1-1c0-1 1-3 1-5-1 2-2 4-2 6 0 1-1 1-1 2 0 3-1 5-1 7h-1c0-2 1-6 2-8z" class="I"></path><path d="M232 377c4-1 9-1 13-1 0 0-1 1-2 1v1c-1 2-1 3-1 5l-1 1h-1c1-1 1-2 1-3s0-1 1-2v-1c-1 0-1-1-2 0-1 0-1 1-1 1-1 3-2 5-2 8v1l-1-1h0c-1-2 1-5 1-7h0v-2c-1-1-2 0-4 0 1 0 1-1 2-1h-3z" class="D"></path><path d="M233 449v-2l-1-1v-1-1c-1-1-1-2-1-2 0-2 0-4 1-5v-2c0-1 0-2 1-3v-4-3h0v-1-3h1v-1h1 0v1c-1 1 0 3-1 3v2h1v-1-1s0-1 1-1h0v3l1-1v-1l1-2h0c1 0 0 1 0 2v3c-1 1-1 2-1 3v1c0 5-2 10 1 15v2l-1 1h1c0 1-1 3-1 4v1c-1 2-2 4-3 7 0 1 0 2-1 2 0-2 1-5 1-7 0-1-1-3-1-4 1-2 1-2 0-3z" class="i"></path><path d="M236 426l1-1v-1l1-2h0c1 0 0 1 0 2v3c-1 1-1 2-1 3v1c0 5-2 10 1 15v2l-1 1c-1-1-2-1-2-3 0-1 0-1-1-2v-1-1c0-3 0-6 1-9 0-1-1-1 0-2v-2h0c1-2 1-2 1-3z" class="I"></path><path d="M244 444l1-1v-1-2-4c-1 0-1-1-2-2v-1-1c0-1-1-2 0-4h0c0-2 0-3 1-4v-1c0-1 0-2-1-2v-2c0-1 1-1 1-2l1 1v2c-1 1 0 1 0 2v3c0 1 1 1 1 2 1 0 0 0 1 1h0 1v1c0 1 0 1-1 2v4h1l1-4v-1c1 1 0 2 1 3l1-1c1 0 1-1 1-2 1 1 1 1 1 2h0c0 3 1 6 1 8l1 1c0 2 1 2 2 3l2 1v1c0 1 0 1-1 1-2 0-2 0-3 1 0 1 0 1 1 1v1l-2 1v2s0 1 1 1c-1 1-2 1-3 1l-1-1h0c0 1 0 1-1 2h-2-1v-3h-1s0 1-1 2c0-1 0-2-1-3 1-1 1-2 1-3s1-1 1-2h0c-1-1-1-2-2-3z" class="C"></path><path d="M249 448c-1-1-1-2-2-4 0 0 0-1-1-2 1-1 1-1 1-2l1 1 1 2 1 1v4h-1z" class="I"></path><path d="M255 441c0 2 1 2 2 3l2 1v1c0 1 0 1-1 1-2 0-2 0-3 1 0 1 0 1 1 1v1l-2 1v2l-1 1c-1-1-1-1-1-2 0 0 1 0 2-1v-2h-2 0v1h-1c-2 1-1 2-2 3 0 0-1 0-1-1v-1c1-1 1-1 1-2v-1h1v-4c1 0 1-1 2 0 0 1 1 2 2 4l1-1c0-1 0-1-1-2v-1-2s1 0 1-1z" class="e"></path><path d="M252 430c1 1 1 1 1 2h0c0 3 1 6 1 8l1 1c0 1-1 1-1 1v2 1c1 1 1 1 1 2l-1 1c-1-2-2-3-2-4-1-1-1 0-2 0l-1-1-1-2v-1h-1v-1c0-1 0-2 1-4l1-4v-1c1 1 0 2 1 3l1-1c1 0 1-1 1-2z" class="N"></path><path d="M249 431h0c1 4 2 9 0 12l-1-2v-1h-1v-1c0-1 0-2 1-4l1-4z" class="K"></path><path d="M239 444h0c1-1 1-3 1-4 0-3 0-7 1-9 0 1 1 2 1 4 0 3 0 4 1 7 1 0 1 1 1 2 1 1 1 2 2 3h0c0 1-1 1-1 2s0 2-1 3c1 1 1 2 1 3 1-1 1-2 1-2h1v3h1 2c1-1 1-1 1-2h0l1 1c1 0 2 0 3-1l1 1h0c1 1 1 1 1 2h1l-1-1 1-1h0l1 1h0l1 1 1 1v1c1 1 2 1 3 1v-1l2 2c1 0 2 0 2 1l1-1 2 2-2 1h0l-2-2c-2 2-3 3-5 3h-1c-1-1-2-1-3-1l-2-1c-2 0-4 2-6 2-1 0-2 0-3-1-1 0-2 0-2-1l-1 1c0 1-1 1-1 2l1 1-2 1v1l-1-1v-1c-1 1-2 2-2 3h-1v2 1c-1 2 0 3-2 4v-1-1-2c0-1-1-2-1-2l1-3-1-1v2c-1-1-1 0-1-1 1-4 2-9 3-13h1l-1-1v-1c0-1 1-3 1-4h-1l1-1c1-1 1-1 2-1 0-2 0-2-1-3h0z" class="b"></path><path d="M252 458l2-2h0c1 0 1 1 3 1 1 1 2 1 2 1h1 1l-1-1 1 1v1c-3-1-6-1-9-1z" class="C"></path><path d="M242 468v-2l2-4v-1c1-1 2-2 3-2h0 0l-1 1h4 0v3h-5 0l-1 1c0 1-1 1-1 2l1 1-2 1z" class="J"></path><path d="M245 463c1 0 1 0 1-1 1-1 3-1 4-2v3h-5z" class="V"></path><path d="M252 458c3 0 6 0 9 1 1 1 2 1 3 1v-1l2 2c-2 0-3-1-5 0h-1c0-1-2-1-2-1h-1c-1-1-1 0-2 0h-5-4l1-1h0 2c1-1 2-1 3-1z" class="c"></path><path d="M250 460h5c1 0 1-1 2 0h1s2 0 2 1h1c2-1 3 0 5 0 1 0 2 0 2 1l1-1 2 2-2 1h0l-2-2c-2 2-3 3-5 3h-1c-1-1-2-1-3-1l-2-1c-2 0-4 2-6 2-1 0-2 0-3-1-1 0-2 0-2-1h0 5v-3h0z" class="K"></path><path d="M266 461c1 0 2 0 2 1l1-1 2 2-2 1h0l-2-2h-1c-2 0-3-1-5-1 2-1 3 0 5 0z" class="p"></path><path d="M250 460h5c1 0 1-1 2 0h1s2 0 2 1c-1 0-2 0-3 1 0 0 0 1-1 1s-1 0-2-1h-4v1-3h0z" class="S"></path><path d="M239 444h0c1-1 1-3 1-4 0-3 0-7 1-9 0 1 1 2 1 4 0 3 0 4 1 7 1 0 1 1 1 2 1 1 1 2 2 3h0c0 1-1 1-1 2s0 2-1 3c-1 0-1-1-1-2h-1c-1 2-2 4-2 6 0 1-1 2-1 3h0 0l2-3h1c0 1 0 2-1 3v1h0l-2 2c0-1 0-1-1-2v2c0 2-2 4-2 6l-1-1v2c-1-1-1 0-1-1 1-4 2-9 3-13h1l-1-1v-1c0-1 1-3 1-4h-1l1-1c1-1 1-1 2-1 0-2 0-2-1-3h0z" class="E"></path><path d="M237 449h1l1-1 2 2-1 2v-1h-1 0c0 1-1 1-1 2h-1c0-1 1-3 1-4h-1z" class="e"></path><path d="M242 450v-2h0-1v-1h2v-1l-3-3h3c0 2 1 3 1 4v1l1 1c0 1 0 2-1 3-1 0-1-1-1-2h-1z" class="C"></path><path d="M243 450v-2h1l1 1c0 1 0 2-1 3-1 0-1-1-1-2z" class="R"></path><path d="M237 453h1c0-1 1-1 1-2h0 1v1c0 1 0 2-1 3 0 1-1 2-1 3s0 1-1 3-1 4-2 6v2c-1-1-1 0-1-1 1-4 2-9 3-13h1l-1-1v-1z" class="f"></path><path d="M230 452h1c1 0 1 1 1 2h0c1 1 1 1 1 2h1c0 2-1 5-1 7 1 0 1-1 1-2 1-3 2-5 3-7l1 1h-1c-1 4-2 9-3 13 0 1 0 0 1 1v-2l1 1-1 3s1 1 1 2v2 1 1c2-1 1-2 2-4v-1-2h1c0-1 1-2 2-3v1l1 1v-1l2-1-1-1c0-1 1-1 1-2l1-1c0 1 1 1 2 1 1 1 2 1 3 1v1l-1 1v1c-1 1 0 1-1 0v2h0v1c-1 0-1 0-1 1l1 1h-1v1c-2 0-4 1-4 2v1c1 1 1 0 2 0l1 1v1c1 0 2 0 4-1 0 0 1 0 2-1h1 0 1 2s1 0 1-1l1 1 1 1v3h0v-1c-2 1-2 6-3 8v2 1h-3v1l-14-2c0-1-1-1-1-2l-2-2h-2l-1 1c-2-2-2-2-5-2v-5h1c0-2-1-6 0-8s1-4 2-5v-6c-1-1-1-2-1-3v-1-5z" class="i"></path><path d="M242 474l1-1h4v1c-2 0-4 1-4 2v1c1 1 1 0 2 0 0 1-1 2-1 2h-1c-2 1-3 2-3 4v1h-1c0-3 0-4 1-7h0c0-1 1-2 2-3z" class="n"></path><path d="M228 480h1l2 2c1 0 5 0 6 2 0 1-1 1-1 2h-2l-1 1c-2-2-2-2-5-2v-5z" class="C"></path><path d="M245 477l1 1v1c1 0 2 0 4-1-1 2-2 4-2 6-1 1-1 3-1 3-2 0-3 0-4-2-1-1-1-3 0-4 0-1 1-1 1-2 0 0 1-1 1-2z" class="F"></path><path d="M230 457h1c0 2 0 4 1 6 0 1 0 6 2 7h0l1 1v-1c-1 0-1 0-1-1v-1c0 1 0 0 1 1v-2l1 1-1 3s1 1 1 2v2 1h-1v3c1 0 1 1 1 1h-1c-2-1-3-4-3-6-1-3 0-5-1-7v-6c-1-1-1-2-1-3v-1z" class="J"></path><path d="M242 468l2-1-1-1c0-1 1-1 1-2l1-1c0 1 1 1 2 1 1 1 2 1 3 1v1l-1 1v1c-1 1 0 1-1 0v2h0v1c-1 0-1 0-1 1l1 1h-1-4l-1 1c0-1 1-1 1-2v-2c0-1 1 0 1-2l-2 1v-1z" class="S"></path><path d="M247 464c1 1 2 1 3 1v1l-1 1v1c-1 1 0 1-1 0h0-1l-2-2h0l2-2z" class="U"></path><path d="M230 452h1c1 0 1 1 1 2h0c1 1 1 1 1 2h1c0 2-1 5-1 7 1 0 1-1 1-2 1-3 2-5 3-7l1 1h-1c-1 4-2 9-3 13v1c0 1 0 1 1 1v1l-1-1h0c-2-1-2-6-2-7-1-2-1-4-1-6h-1v-5z" class="D"></path><path d="M230 452h1c1 0 1 1 1 2h0c1 1 1 1 1 2v3h-1c0-2 1-3-1-5v3h-1v-5z" class="O"></path><path d="M257 476l1 1 1 1v3h0v-1c-2 1-2 6-3 8v2c-2-1-3-2-4-2h-1v-1l-1-1-1 1c1-2 1-5 2-7l1-3h1 0 1 2s1 0 1-1z" class="L"></path><path d="M257 476l1 1c-1 1-1 1-2 3h0c0 3 0 4-2 6-1 0-1 0-2-1v-3c-1 0-1-1-1-2l1-3h1 0 1 2s1 0 1-1z" class="F"></path><path d="M257 476l1 1c-1 1-1 1-2 3h0l-1 2c-1 1-1 1-2 1-1-1 0-2 1-4-1 0-1-1-1-2h1 2s1 0 1-1z" class="G"></path><path d="M245 528l6-3c6-3 12-4 18-2h0 0c0 1 0 1 1 1 0 1 2 2 3 3h1c1 1 3 2 5 3s8 6 9 9v1h-1l1 1v-3c2 1 5 6 6 7 0 1 1 1 2 2v1c0 1 0 2-1 2v3h-1v2c1 1 0 3 1 4-2 5-3 8-6 11v1h0c1 1 0 1 0 3 1-1 2-1 3-2l1 1v1h0l-1 1h2c-1 1-2 2-2 3v1h1c0 2-2 3-3 4 0 1 0 0-1 1 0 0-1 1-2 1l-1 2c-1 1-1 1-2 1l-1 1h-1l-1 1h-2c-1 1-2 2-3 2h0-2v1h-2c-2 1-4 0-6 0h-2c-1 0-1 0-2-1-3 0-5-1-6-3h-1c-2-1-4-4-5-6l-3-2v-2c-1-2-2-3-2-5 1-1 1-2 1-3v-4h0c-2 1-5-2-7-1v-1c0-1-1-3-2-5l-3-9v-4c1-2 1-6 2-8l3-4v-1c0-2 2-4 3-6 0 0 1 0 1-1v1l3-2h0l-1 2z" class="Q"></path><path d="M269 549c2 0 4 2 6 3v2l-1 1v-1c-1-1-1-2-3-3 0-1-1-1-3-2h1 0z" class="D"></path><path d="M244 537c2-1 5-4 7-3 1 0 1 0 2 2 0 0-1 1-1 2h0l-9 9c-3 4-4 9-3 14 0 1 0 2 2 3 1 1 2 2 4 3-2 1-5-2-7-1v-1c0-1-1-3-2-5l-3-9c1-1 2-2 2-3 1-3 4-6 6-9l2-2z" class="b"></path><path d="M241 544c0 1 1 1 2 1l-3 7h-1c0-3 0-5 2-8z" class="d"></path><path d="M241 544c2-4 5-8 10-9h0v2c-2 1-3 3-4 4-2 1-3 3-4 4-1 0-2 0-2-1z" class="H"></path><path d="M264 544c1-2 3-3 4-4 1 0 1 0 1 1-1 0-1 1-2 2h0c1 1 1 1 1 2h-1s0-1-1-2c-1 0-2 1-2 2v1c-1 1-2 3-3 4-1 2-1 4-2 6h0c1 1 2 1 4 1v2l-2 3v4c0 1-1 2-1 2l1 2h-1v1h0 0-1l-1 1v1l-3-2h-1c0-1-1-2-2-2v-5c0-4 1-8 4-11v-1h0c0-1 0-1 1-1 1-2 3-6 5-7h1c-1 2-2 2-3 4v1c-1 0-1 1-1 1h1v-1c1-1 2-3 3-4 0 0 0-1 1-1z" class="O"></path><path d="M259 563c1 0 1-1 2-1v4c0 1-1 2-1 2h-1c-1 0-1-1-2-2l1-1-1-1v-1l2 1h0v-1z" class="Y"></path><path d="M259 556c1 1 2 1 4 1v2l-2 3c-1 0-1 1-2 1s-1-1-2-2l-1-1 1-2 1-1 1-1z" class="B"></path><path d="M259 556c1 1 2 1 4 1v2l-5-2-1 4-1-1 1-2 1-1 1-1z" class="c"></path><path d="M255 564v2c0 1 0 1 1 1l3 3 1 1h-1l-1 1v1l-3-2h-1c0-1-1-2-2-2v-5 1h2l1-1z" class="k"></path><path d="M256 552c0-1 0-1 1-1l1 1c0 1 0 1-1 1 0 1-1 3-1 4-1 2-2 5-1 7l-1 1h-2v-1c0-4 1-8 4-11v-1h0z" class="F"></path><path d="M263 557c0-2 2-6 4-8 0-1 1 0 2 0h0-1c2 1 3 1 3 2 2 1 2 2 3 3v1l1 1v2l1 1c-1 1-1 2-1 3l2 1h1l-2 2v-1h-1l-1 1c1 1 1 0 1 0l1 1 1 1h1c-2 1-4 1-6 2h0c-1 1-2 1-3 3-1 1-2 0-4 0h-2c-1-1-2-1-3-1h0v-1h1l-1-2s1-1 1-2v-4l2-3v-2z" class="I"></path><path d="M265 567l-1-2-1-1h0v-2l1-1c0-1 0-1 1-1 0 0 1 0 2 1h0c-1 1 0 3 0 4l1 1h0c0 1 0 2-1 3 0-1 0-1-2-2h0z" class="C"></path><path d="M268 566h2 0l-1 1v1c1 1 2 0 3 0v1h0c-1 1-2 1-3 3-1 1-2 0-4 0h-2c-1-1-2-1-3-1h0v-1h1l1-1v-2h1 1c0 1 1 0 1 0h0c2 1 2 1 2 2 1-1 1-2 1-3z" class="D"></path><path d="M268 549c2 1 3 1 3 2 2 1 2 2 3 3v1l1 1v2l1 1c-1 1-1 2-1 3l2 1h1l-2 2v-1h-1l-1 1c1 1 1 0 1 0l1 1 1 1h1c-2 1-4 1-6 2v-1c-1 0-2 1-3 0v-1l1-1h0-2 0l-1-1c0-1-1-3 0-4h0v-1c0-1 0-2 1-3v-4-1-3z" class="R"></path><path d="M271 551c2 1 2 2 3 3v1l1 1v2l-1 1h0c0-1-1-1-1-1-1-2-1-4-2-6v-1z" class="H"></path><path d="M268 549c2 1 3 1 3 2v1h-1c0 1 1 3 1 5h0c-1-1-2-3-3-4v-1-3z" class="L"></path><path d="M267 561v-1c0-1 0-2 1-3 0 1 0 2 1 2s1 0 2-1h0c0 1 1 2 2 2l1 1c0 1 0 1-1 2l-1 1c-1-1-2-1-2-2h-2l-1-1h0z" class="f"></path><path d="M267 561l1 1h2c0 1 1 1 2 2l1-1 2 1-1 1c1 1 1 0 1 0l1 1 1 1h1c-2 1-4 1-6 2v-1c-1 0-2 1-3 0v-1l1-1h0-2 0l-1-1c0-1-1-3 0-4z" class="L"></path><path d="M268 562h2c0 1 1 1 2 2l1-1 2 1-1 1c1 1 1 0 1 0l1 1 1 1h1c-2 1-4 1-6 2v-1c1-1 2-1 3-1h0v-1c-2 0-2-1-4 0l-2-2c-1-1-1-1-1-2z" class="a"></path><path d="M246 567v-1c-2-2-3-4-4-6-1-8 6-8 9-13v-1l-2 1h0c0-1 1-2 2-3 1-2 3-3 6-4 1 0 3 1 4 2h0c1 0 1 1 1 1l2 1c-1 0-1 1-1 1-1 1-2 3-3 4v1h-1s0-1 1-1v-1c1-2 2-2 3-4h-1c-2 1-4 5-5 7-1 0-1 0-1 1-2 0-3 1-4 2v1 1c0 2-1 4-1 6-1 3 0 6 0 9h-1c1 3 2 6 4 9l-1 1h-1c-1-3-2-5-3-8-1 1-1 1-1 2 1 2 1 4 3 6 1 2 3 4 5 6-2-1-4-5-7-5 1 0 1 1 1 1l-3-2v-2c-1-2-2-3-2-5 1-1 1-2 1-3v-4z" class="C"></path><path d="M252 556c0 2-1 4-1 6-1 3 0 6 0 9h-1c1 3 2 6 4 9l-1 1h-1l-3-8c-1 1-1 1-1 2-1-3-2-8 0-12 1-2 2-5 4-7z" class="P"></path><path d="M249 573c0-2-1-5 0-8l1 6c1 3 2 6 4 9l-1 1h-1l-3-8z" class="J"></path><path d="M261 542h0c1 0 1 1 1 1l2 1c-1 0-1 1-1 1-1 1-2 3-3 4v1h-1s0-1 1-1v-1c1-2 2-2 3-4h-1c-2 1-4 5-5 7-1 0-1 0-1 1-2 0-3 1-4 2-3 3-5 6-6 10-1-2-3-6-2-8s3-3 4-4h3v-1s-1 0-1-1l6-6c1-1 4-1 5-2z" class="G"></path><path d="M244 537h0c0-2 3-3 4-4l7-2c5-2 10-3 15-1 3 0 4 1 5 3l5 5c1 1 1 2 1 3l1 2v3 2 1c0 1-1 1-1 2-1 2-2 2-3 3-1 0-1 2-2 2h-1l-1-1 1-1v-2h0c2-2 2-6 2-8-1 0-1-1-2-2-5-5-14-4-21-4h0-2c0-1 1-2 1-2-1-2-1-2-2-2-2-1-5 2-7 3z" class="m"></path><path d="M260 532c2-1 2 0 3 0v1l-1 1h0c-1 0-2-1-3-1v-1h1z" class="b"></path><path d="M278 539c1-1 1-1 2-1 1 1 1 2 1 3l1 2-1 2h-1v3c0-3-1-6-2-9z" class="V"></path><path d="M270 530c3 0 4 1 5 3l5 5c-1 0-1 0-2 1-1-3-3-4-4-5s-2-3-4-4z" class="h"></path><path d="M254 538c5-2 11-2 16 0 4 1 6 3 7 6h0c-1 0-1-1-2-2-5-5-14-4-21-4h0z" class="C"></path><path d="M282 543v3 2 1c0 1-1 1-1 2-1 2-2 2-3 3-1 0-1 2-2 2h-1l-1-1 1-1v-2h0c2-2 2-6 2-8h0v1c1 2 0 3 0 5l-1 2h1c1-1 2-2 3-4v-3h1l1-2z" class="R"></path><path d="M245 528l6-3c6-3 12-4 18-2h0 0c0 1 0 1 1 1 0 1 2 2 3 3h1c1 1 3 2 5 3s8 6 9 9v1h-1c-1-1-2-3-3-4h-1-1l-1 1c1 0 1 1 2 1 0 2 0 2-2 3 0-1 0-2-1-3l-5-5c-1-2-2-3-5-3-5-2-10-1-15 1l-7 2c-1 1-4 2-4 4h0l-2 2c-2 3-5 6-6 9 0 1-1 2-2 3v-4c1-2 1-6 2-8l3-4v-1c0-2 2-4 3-6 0 0 1 0 1-1v1l3-2h0l-1 2z" class="B"></path><path d="M243 527v1l3-2h0l-1 2c-2 1-4 4-6 7v-1c0-2 2-4 3-6 0 0 1 0 1-1z" class="E"></path><path d="M236 539l1 1s4-5 5-7c3-4 8-7 12-9 2-1 3-1 5-1l1-1c2 0 6 0 8 1-1 1-2 0-3 0-2 0-3 1-5 1-7 1-14 6-19 12l1 1v2c-2 3-5 6-6 9 0 1-1 2-2 3v-4c1-2 1-6 2-8z" class="h"></path><defs><linearGradient id="R" x1="269.388" y1="526.198" x2="262.452" y2="538.65" xlink:href="#B"><stop offset="0" stop-color="#0e0d0d"></stop><stop offset="1" stop-color="#383839"></stop></linearGradient></defs><path fill="url(#R)" d="M242 537c4-5 9-8 16-10 6-2 11-2 17 1 3 2 7 5 9 8h-1-1l-1 1c1 0 1 1 2 1 0 2 0 2-2 3 0-1 0-2-1-3l-5-5c-1-2-2-3-5-3-5-2-10-1-15 1l-7 2c-1 1-4 2-4 4h0l-2 2v-2z"></path><path d="M275 528c3 2 7 5 9 8h-1-1l-1 1c1 0 1 1 2 1 0 2 0 2-2 3 0-1 0-2-1-3l1-1c0-1-1-2-1-3-1-2-4-3-5-5v-1z" class="C"></path><path d="M284 536c1 1 2 3 3 4l1 1v-3c2 1 5 6 6 7 0 1 1 1 2 2v1c0 1 0 2-1 2v3h-1v2c1 1 0 3 1 4-2 5-3 8-6 11v1h0c1 1 0 1 0 3 1-1 2-1 3-2l1 1v1h0l-1 1c-1 1-3 2-5 3h0c-3 0-5 2-8 2-1 0-2-1-4-1v1-1c-1-1-1-2-1-3h-1l-2-3h0s-1-1-2-1c1-2 2-2 3-3h0c2-1 4-1 6-2h-1l-1-1-1-1s0 1-1 0l1-1h1v1l2-2h-1l-2-1c0-1 0-2 1-3l-1-1v-2h1c1 0 1-2 2-2 1-1 2-1 3-3 0-1 1-1 1-2v-1-2-3l-1-2c2-1 2-1 2-3-1 0-1-1-2-1l1-1h1 1z" class="G"></path><path d="M287 543h1c-1 1-1 1-1 2 1 2 3 5 2 7h0 0l-3-6h0c-1 3-2 5-4 7 1-1 2-2 2-4l1-5c1 0 1 0 2-1z" class="o"></path><path d="M288 541v-3c2 1 5 6 6 7 0 1 1 1 2 2v1c0 1 0 2-1 2v3h-1v2c-1 3-2 5-4 8h-1c-1 1-2 1-3 0-1 0-2-1-2-2 1-1 2-2 3-2 0-1 1-1 2-1v-1c2 0 2-1 2-2h2c1-3 0-6-1-9-1-1-2-4-3-4l-1 1h-1c0-1 0-1 1-2z" class="C"></path><path d="M294 545c0 1 1 1 2 2v1c0 1 0 2-1 2l-1-5z" class="X"></path><path d="M293 555c-1 3-1 6-4 7-1 1-1 1-2 1l-1-1c0-1 1-2 1-3h0c0-1 1-1 2-1v-1c2 0 2-1 2-2h2z" class="P"></path><path d="M294 555c1 1 0 3 1 4-2 5-3 8-6 11v1h0c1 1 0 1 0 3-2 0-6 1-8 0l-8-1 2-2v-1l1 1c3 0 5 0 8-1h0c3-1 5-3 7-5v-1-1h-1c2-3 3-5 4-8z" class="S"></path><path d="M289 571c1 1 0 1 0 3-2 0-6 1-8 0h5 0v-1c1-1 2-1 3-2z" class="Z"></path><path d="M294 555c1 1 0 3 1 4-2 5-3 8-6 11l-7 2s1 0 2-1h2c1-1 4-4 5-6v-1-1h-1c2-3 3-5 4-8z" class="F"></path><path d="M269 572c1-2 2-2 3-3 1 0 2 1 3 2l-2 2 8 1c2 1 6 0 8 0 1-1 2-1 3-2l1 1v1h0l-1 1c-1 1-3 2-5 3h0c-3 0-5 2-8 2-1 0-2-1-4-1v1-1c-1-1-1-2-1-3h-1l-2-3h0s-1-1-2-1z" class="W"></path><path d="M293 574h0l-1 1c-1 1-3 2-5 3h0c-3 0-5 2-8 2-1 0-2-1-4-1v1-1c-1-1-1-2-1-3 1 1 2 1 4 1 5 0 11 0 15-3z" class="P"></path><path d="M284 536c1 1 2 3 3 4l1 1c-1 1-1 1-1 2-1 1-1 1-2 1l-1 5c0 2-1 3-2 4l-3 5h0c2 1 3 2 4 4-1 2-2 4-4 5h-1-1l-1-1-1-1s0 1-1 0l1-1h1v1l2-2h-1l-2-1c0-1 0-2 1-3l-1-1v-2h1c1 0 1-2 2-2 1-1 2-1 3-3 0-1 1-1 1-2v-1-2-3l-1-2c2-1 2-1 2-3-1 0-1-1-2-1l1-1h1 1z" class="D"></path><path d="M284 536c1 1 2 3 3 4l1 1c-1 1-1 1-1 2-1 1-1 1-2 1v-3c0-1 0-1-1-1v-1c0-1 0-1-1-1h0c-1 0-1-1-2-1l1-1h1 1z" class="N"></path><path d="M276 559c1 0 1-1 2 0 1 0 1 1 2 1v3h0v1c-1 1-1 2-2 2h0l-2-1 2-2h-1l-2-1c0-1 0-2 1-3z" class="j"></path><path d="M277 563c1-1 0-2 1-3 1 1 1 1 1 2l1 1h0v1c-1 1-1 2-2 2h0l-2-1 2-2h-1z" class="P"></path><path d="M256 552h0v1c-3 3-4 7-4 11v5c1 0 2 1 2 2h1l3 2v-1l1-1h1 0c1 0 2 0 3 1h2c2 0 3 1 4 0 1 0 2 1 2 1h0l2 3h1c0 1 0 2 1 3v1-1c2 0 3 1 4 1 3 0 5-2 8-2h0c2-1 4-2 5-3h2c-1 1-2 2-2 3v1h1c0 2-2 3-3 4 0 1 0 0-1 1 0 0-1 1-2 1l-1 2c-1 1-1 1-2 1l-1 1h-1l-1 1h-2c-1 1-2 2-3 2h0-2v1h-2c-2 1-4 0-6 0h-2c-1 0-1 0-2-1-3 0-5-1-6-3h-1c-2-1-4-4-5-6 0 0 0-1-1-1 3 0 5 4 7 5-2-2-4-4-5-6-2-2-2-4-3-6 0-1 0-1 1-2 1 3 2 5 3 8h1l1-1c-2-3-3-6-4-9h1c0-3-1-6 0-9 0-2 1-4 1-6v-1-1c1-1 2-2 4-2z" class="N"></path><path d="M272 579v2c-1 2-3 3-5 3v-2l2-2c0-1 2-1 3-1z" class="H"></path><path d="M252 569c1 0 2 1 2 2h1l3 2 2 1-1 1c0 1 1 2 0 2 0 1 0 2 1 3l-5-4c-2-2-2-5-3-7z" class="P"></path><path d="M255 571l3 2 2 1-1 1-1-1h-2c-1 0-2-1-3-2 1 0 1 0 2-1z" class="j"></path><path d="M260 571h0c1 0 2 0 3 1h2c2 0 3 1 4 0 1 0 2 1 2 1h0l2 3-3-1c0 1 0 2 1 3 0 0 0 1 1 1-1 0-3 0-3 1h-1c-1 1-1 3-2 4-1 0-2-1-2-1h-2v-2c-1 0-2 0-2-1-1-1-1-2-1-3 1 0 0-1 0-2l1-1-2-1v-1l1-1h1z" class="M"></path><path d="M260 574c0 1 1 1 1 1h1 2v2h1l-1 1h0c1 0 1 0 2 1h-2-2v2c-1 0-2 0-2-1-1-1-1-2-1-3 1 0 0-1 0-2l1-1z" class="Y"></path><path d="M260 571h0c1 0 2 0 3 1h2c2 0 3 1 4 0 1 0 2 1 2 1h0l2 3-3-1c0 1 0 2 1 3 0 0 0 1 1 1-1 0-3 0-3 1h-1c0-1 1-1 1-2 1 0 1 0 1-1h-1c-2-1-2-1-4 0h-1v-2h-2-1s-1 0-1-1l-2-1v-1l1-1h1z" class="H"></path><path d="M258 572h1c1 1 2 2 3 2h1 0c1 0 3 0 4 1h3c0 1 0 2 1 3 0 0 0 1 1 1-1 0-3 0-3 1h-1c0-1 1-1 1-2 1 0 1 0 1-1h-1c-2-1-2-1-4 0h-1v-2h-2-1s-1 0-1-1l-2-1v-1z" class="P"></path><path d="M250 571h1c2 5 4 9 9 12s8 3 14 2c2 0 3-1 5-2h-2v-2h0-1c0 1 0 2-1 2-1 1-2 1-3 1h-1c2-2 2 0 3-1s1-2 1-3h0v-1c2 0 3 1 4 1 3 0 5-2 8-2h0c2-1 4-2 5-3h2c-1 1-2 2-2 3-6 6-14 9-22 9-6 1-12-3-16-7-2-3-3-6-4-9z" class="H"></path><path d="M287 578c1 0 2 0 3-1 0 0 1 0 2-1h0c-2 2-3 4-5 5h-1 0c-2 1-3 1-5 1-1 0-1 1-2 1h0 0-2v-2h0-1c0 1 0 2-1 2-1 1-2 1-3 1h-1c2-2 2 0 3-1s1-2 1-3h0v-1c2 0 3 1 4 1 3 0 5-2 8-2h0z" class="G"></path><path d="M248 575c0-1 0-1 1-2l3 8h1l1-1c4 4 10 8 16 7 8 0 16-3 22-9v1h1c0 2-2 3-3 4 0 1 0 0-1 1 0 0-1 1-2 1l-1 2c-1 1-1 1-2 1l-1 1h-1l-1 1h-2c-1 1-2 2-3 2h0-2v1h-2c-2 1-4 0-6 0h-2c-1 0-1 0-2-1-3 0-5-1-6-3h-1c-2-1-4-4-5-6 0 0 0-1-1-1 3 0 5 4 7 5-2-2-4-4-5-6-2-2-2-4-3-6z" class="M"></path><path d="M292 578v1c-6 5-15 10-24 10-6-1-11-2-14-6l-2-2h1l1-1c4 4 10 8 16 7 8 0 16-3 22-9z" class="c"></path><path d="M250 583s0-1-1-1c3 0 5 4 7 5h1 1c6 4 13 4 19 3h0 1l1-1h1 1c1-1 3-2 5-2-1 1-1 1-2 1l-1 1h-1l-1 1h-2c-1 1-2 2-3 2h0-2v1h-2c-2 1-4 0-6 0h-2c-1 0-1 0-2-1-3 0-5-1-6-3h-1c-2-1-4-4-5-6z" class="l"></path><path d="M297 212c2-1 4-2 6-1 1 0 2 1 3 2v1h0 1c1 1 1 3 2 5l1 3c1-1 1-1 1-2l2 4 1 3c0 1 1 3 2 4s1 1 2 1c0-1-1-1-1-1v-2-1h1l3 2 1-1 2 2 1-1h0l-1-3s0-1 1-1 1 0 2 1h0l6 5 2 2h1 1l2 1 1 2 3-1c1 1 1 1 2 1l1 1v-1h1c1 0 1 0 1 1 1 0 2 1 3 2v1c2 2 2 3 4 4l2 3c2 5 6 9 8 13 1 2 2 3 3 5l1 2v3 1s-1 1-1 2l-1 2 1-1v2h0c1 1 1 1 1 2s0 1-1 2c0 1-1 1-2 2l-1-1c0 2 0 2-2 3v-1 1h-1v-2c-1-1-1-1-2-3 0-1-2-2-2-3h0c-1-1-2-1-3-2-1 1-1 1-1 2h-1l1 1-1 1c-1 0-1 0-2 1l-1-1h0l-1 1v1c-2-1-3 0-4 0l-8 1c0 1-1 1-1 1-1 1 0 1 0 2h-1-1c-1-1-2 0-3 0h-3c-1-1-2-1-4-1h0 0-1c2-1 3-1 4-1-1-1-2-1-2-2h0 0v-2c-1 0-2-1-3-1h-6s0 1-1 1h-4l-1 1c0-1-1-1-1-1l-3 1h-1c1-1 1-1 2-1v-1c1 0 2-1 3-1h2v-2c-1 1-2 1-3 1-1-1-1-1-3-1h0c-1 0-1 0-2-1-1 0-2 0-3 1h-2v1l-1 1c-1-1-1-1-1-2l2-1c-1 0-2 0-2 1l-7 3-1 1c-2 0-2-1-3-2-1-2 2-1 3-2l-3-2h-3-2-1c0-1 0-1 1-1v-1h-4c-1 0-2 0-3-1v1l-1-1-1 2-1-2h-1l1-1v-3h-1l1-2c0-2 1-3 2-5l1-2h0l2-2c0-2-1-3-2-4 0-1 1-4 0-5 0-1-1-1-2-1h0l8-12c0-1 0-2 1-3l1-4c1 0 1-1 1-1 0-1 1-2 1-2 1-1 2-1 2-1 1 0 1 0 1 1l6-10c2 1 2-1 3-1 0-1 1 0 1 0z" class="b"></path><path d="M278 254h1c0 1-1 2 0 3v1h-1-1v-3l1-1z" class="o"></path><path d="M289 268h0 2v1c-2 0-1 0-2 1h-2 0l-1 1-1-1v-1l1-1h1c1 1 2 0 2 0z" class="e"></path><path d="M286 248c1 2 1 2 1 4v3 1l-2-1c-1-1-1-3-2-5h1 0c1 0 1-1 2-2z" class="B"></path><path d="M277 259l2 2h2-1v-2h1 1c1 0 1 0 2 1h1 0c0 1 0 1 1 2-1 1-1 1-2 1h-1 0-1c-1-1-2-1-3-1h-1c0-1-1-2-1-3z" class="e"></path><path d="M272 268l1-1v-2c0-1 1-2 1-3l1 1c0 1 1 1 1 2s0 3-1 4v1c1 0 2-1 3 0h2l3 3h-2-1c0-1 0-1 1-1v-1h-4c-1 0-2 0-3-1v1l-1-1c-1-1-1-1-1-2z" class="E"></path><path d="M273 259l1-2c0 1 0 1 1 2v1c1-1 1-1 1-2l1 1c0 1 1 2 1 3h1c1 0 2 0 3 1-1 1-1 1-1 2h0l-1 1c-1-1-1-2-2-3-1 0-1 0-1-1h-1v3c0-1-1-1-1-2l-1-1c0 1-1 2-1 3v2l-1 1c0 1 0 1 1 2l-1 2-1-2h-1l1-1v-3h-1l1-2c0-2 1-3 2-5z" class="N"></path><path d="M279 262c1 0 2 0 3 1-1 1-1 1-1 2h0c-1 0-1-1-1-2l-1-1z" class="o"></path><path d="M273 259l1-2c0 1 0 1 1 2 0 1-1 1-1 2s-1 2-2 3v4c0 1 0 1 1 2l-1 2-1-2h-1l1-1v-3h-1l1-2c0-2 1-3 2-5z" class="D"></path><defs><linearGradient id="S" x1="287.639" y1="236.271" x2="280.486" y2="232.889" xlink:href="#B"><stop offset="0" stop-color="#c6c6c7"></stop><stop offset="1" stop-color="#f1eef1"></stop></linearGradient></defs><path fill="url(#S)" d="M284 223c1-1 2-1 2-1 1 0 1 0 1 1-4 7-6 13-4 21 0 2 2 3 3 4-1 1-1 2-2 2h0-1c-2-4-3-8-3-12v-5h0c0-1 0-2 1-3l1-4c1 0 1-1 1-1 0-1 1-2 1-2z"></path><path d="M297 275c-1-1-2-3-3-3h-2v-1c0-1 1-1 1-1v-1s0-1 1-1c-1-1-1-1-2-1l-1-2 1-1h2v1h2c0-1 0-1 1-1l1 1c0 1-1 1-1 1-1 0-2 1-3 1 2 0 5 0 7-1 2 0 5-1 6-1-1 1-2 1-3 2h0l7-1-2 2s-1 0-2 1h1v2h1c0 1-1 1-2 2h0-1-1v-1l-1 1v1c-1 0-2 0-3 1h-2v1l-1 1c-1-1-1-1-1-2l2-1c-1 0-2 0-2 1z" class="C"></path><path d="M300 273c-1 0 0-1-1-2l-1 1v-2c-1 0-2 0-3-1h4l2-2h1 2 0l-1 1-1 1h3 0c-1 1-1 1-3 1h-1c0 1 1 1 1 2 0 0-1 1-2 1z" class="E"></path><path d="M304 267l7-1-2 2s-1 0-2 1h1v2h1c0 1-1 1-2 2h0-1-1v-1l-1 1v1c-1 0-2 0-3 1h-2v1l-1 1c-1-1-1-1-1-2l2-1 1-1c1 0 2-1 2-1 0-1-1-1-1-2h1c2 0 2 0 3-1h0-3l1-1 1-1z" class="O"></path><path d="M302 272l6-3-3 3-1 1v1c-1 0-2 0-3 1h-2v1l-1 1c-1-1-1-1-1-2l2-1 1-1c1 0 2-1 2-1z" class="S"></path><path d="M297 212c2-1 4-2 6-1 1 0 2 1 3 2v1h0 1c1 1 1 3 2 5l1 3c1-1 1-1 1-2l2 4 1 3-1 1v2s-1 0-1 1h-3c-1 0-2 0-3 1h-1-2l-1 1h1c0 1-1 1-2 1h-1c-2 0-3 0-5-1h0-1c-1 0-2 0-3 1h0l1 1h-2l-1-1v-1h1v-1h-2l-1-1h1c1 0 2 0 3-1h0l-2-1h-1l4-1h0c-1 0-2-1-3-1v-1h3l-2-1v-1c1 0 2 0 3-1h-2v-1c1 0 2 0 2-1h-1v-1h2c0-1 1-1 1-2 0 0 1 0 1-1 0 0 1-1 1-2 0 0 1 0 1-1h-1v-1c1 0 2 0 3-1h-1-2z" class="g"></path><path d="M294 232h4v1 1c-1 0-2 0-3-1h0 0l-1-1z" class="a"></path><path d="M304 227c1 0 3-1 4 0h0 2l1 1-2 1c-1 0-3-1-4-2h-1zm7-7l2 4 1 3-1 1-3-5v-1c1-1 1-1 1-2z" class="F"></path><path d="M298 227c3-1 5-1 8-1 1-1 2-1 4-1l-2 2c-1-1-3 0-4 0-2 0-4 1-6 0z" class="H"></path><path d="M291 230c2-1 3 0 5 0v1h-5v1h3l1 1h0-1c-1 0-2 0-3 1h0l1 1h-2l-1-1v-1h1v-1h-2l-1-1h1c1 0 2 0 3-1h0z" class="U"></path><path d="M306 221h2v1s1 1 1 2c-1 1-6 1-8 1h-2c0-1 1-1 2-1h2l1-2 2-1z" class="F"></path><path d="M299 232h0-1l-2-2c1-1 1-1 2-1 1-1 1-1 2-1l-1-1c-1 1-4 1-5 0h4c2 1 4 0 6 0h1l-5 1v1h4v1h-2c-1 0-2 1-3 2h0z" class="P"></path><path d="M302 230h2v-1h-4v-1l5-1c1 1 3 2 4 2h2 0l-2 2c-1 0-2 0-3 1h-1-2l-1 1c-1 0-2 0-3-1h0c1-1 2-2 3-2z" class="H"></path><path d="M299 232h0c1-1 2-2 3-2v1c1 0 2 0 3 1h-2l-1 1c-1 0-2 0-3-1z" class="d"></path><path d="M297 212c2-1 4-2 6-1 1 0 2 1 3 2v1h0 1c1 1 1 3 2 5l1 3v1l-1 1c0-1-1-2-1-2v-1h-2l-2 1-1 2h-2c-1-1-2-1-2-2-1 0-2 0-2-1v-1h0-3 0c0-1 1-1 1-2 0 0 1 0 1-1 0 0 1-1 1-2 0 0 1 0 1-1h-1v-1c1 0 2 0 3-1h-1-2z" class="P"></path><path d="M299 222l1-1v-1c1 0 2-1 2-1 0-1 0-1-1-2 0 0-1 0-1-1h3 2v1l-1 1c1 0 2 1 3 1 0 1 0 1-1 1v1l-2 1-1 2h-2c-1-1-2-1-2-2z" class="d"></path><path d="M304 222c-1 0-1 0-1-1v-1h3v1l-2 1z" class="l"></path><path d="M297 212c2-1 4-2 6-1 1 0 2 1 3 2v1h0 1c1 1 1 3 2 5l1 3v1l-1 1c0-1-1-2-1-2v-1h-2v-1c1 0 1 0 1-1-1 0-2-1-3-1l1-1v-1h-2c0-1 1-1 2-1v-1c-1 0-2-1-3-1h-1l-1-1h-1-2z" class="S"></path><path d="M314 227c0 1 1 3 2 4v1c1 3 2 5 4 7l1 1h2 0c1 1 1 2 1 3v1h0v1h1 1l-2 2h-1 0v1h1l1 2c-1 0-2 1-3 1h-1c-1 1-2 1-3 2h1 0 2c1 1 1 1 1 2h-1v1c-2 1-3 1-5 2h-3c0 1 0 1 1 2h-5v1h1 0c0 1-1 1-1 2-1 1-5 2-6 2h0c-1 0-1 1-2 1h0c-2 1-5 1-7 1 1 0 2-1 3-1 0 0 1 0 1-1l-1-1c-1 0-1 0-1 1h-2v-1h1l1-1-4-1h-1-2c0-2 0-3 1-5 0 0-1 0-1-1s0-4 1-5l-1-1c-1-1-1-2-1-3 1-1 2 0 3-1h-2v-1h3v-1h-3-1c-1-1-1-1-1-2-1-2-1-6 0-8v-1c1 0 1 0 1-1h2v1h-1v1l1 1h2l-1-1h0c1-1 2-1 3-1h1 0c2 1 3 1 5 1h1c1 0 2 0 2-1h-1l1-1h2 1c1-1 2-1 3-1h3c0-1 1-1 1-1v-2l1-1z" class="Z"></path><path d="M294 239h2l1 1h-4l1-1z" class="P"></path><path d="M291 237h3l2 2h-2-2v-1h-1v-1z" class="K"></path><path d="M300 247c2 1 4 1 6 1l1 1h-1c-1 0-2 1-3 1s-1 0-1-1h-2-1c-1 0-1 0-1-1l1-1h1z" class="k"></path><path d="M306 248h1c1 1 1 1 3 1 1 0 3 0 5-1l-1 1c-1 0-2 0-3 1v1h0c-3 0-6 0-8-1 1 0 2-1 3-1h1l-1-1z" class="M"></path><path d="M288 232h2v1h-1v1l1 1h2c0 1 0 1-1 2v1h1v1h-2v1h2c1 1 2 1 3 1v2c-2 0-4 0-6 1h-1c-1-1-1-1-1-2-1-2-1-6 0-8v-1c1 0 1 0 1-1z" class="S"></path><path d="M290 235h2c0 1 0 1-1 2v1h1v1h-2v1h-1v-1l1-1-2-1v-2l1 1 1-1z" class="T"></path><path d="M303 241l2 1 1 1h-3v1c2 1 2 1 4 1v-1-1-1h1c1 1 2 1 4 1h0l6 1 3-1h1c1 1 1 1 2 1h0v1h1 1l-2 2-1-1h0c-3 2-6 3-9 3l1-1c-2 1-4 1-5 1-2 0-2 0-3-1h-1c-2 0-4 0-6-1h-1-3v-1c1 0 2 0 3-1h-1c-1 0-1 0-2-1h0 6v-1c-1 0-2 0-2-1h0c1-1 2 0 3 0v-1z" class="d"></path><path d="M307 245v-1-1-1h1c1 1 2 1 4 1h0l6 1 3-1-1 1c-4 2-8 1-13 1z" class="W"></path><path d="M300 247h-1v-1h6c1-1 1 0 2 0 3 1 6 1 9 0h6c-2 1-5 2-7 2-2 1-4 1-5 1-2 0-2 0-3-1h-1c-2 0-4 0-6-1zm14-20c0 1 1 3 2 4v1c1 3 2 5 4 7l1 1h2 0c1 1 1 2 1 3v1c-1 0-1 0-2-1h-1l-3 1-6-1h0c-2 0-3 0-4-1h-1v1 1 1c-2 0-2 0-4-1v-1h3l-1-1-2-1h-4c-1 0-1-1-2-1l-1-1-2-2h-3c1-1 1-1 1-2l-1-1h0c1-1 2-1 3-1h1 0c2 1 3 1 5 1h1c1 0 2 0 2-1h-1l1-1h2 1c1-1 2-1 3-1h3c0-1 1-1 1-1v-2l1-1z" class="G"></path><path d="M307 238c1 0 1 0 2 1l-2 1s-1 0-1-1v-1h1z" class="Y"></path><path d="M302 238c1-1 1-1 2 0l1 1c-1 1-4 0-5 0h-1 3v-1z" class="g"></path><path d="M309 239h1 2 1 1-2v2l-5-1 2-1z" class="M"></path><path d="M303 235h2v1h-2c1 1 1 1 1 2-1-1-1-1-2 0h-2v-1c1-1 2-1 3-1v-1z" class="Y"></path><path d="M310 234l1 1s1 0 2 1c-1 0-1 0-1 1-1 0-2-1-3 0l1 2h-1c-1-1-1-1-2-1 1-1 1-1 2-1v-1l1-2z" class="H"></path><path d="M294 237h1 0v1h3 2 2v1h-3v2c-1 0-1-1-2-1l-1-1-2-2z" class="k"></path><path d="M311 235c0-1 1-1 1-1 1 1 2 2 3 2v2h0c1 0 1 1 1 1v1h-1-1v-1h-1-1-2l-1-2c1-1 2 0 3 0 0-1 0-1 1-1-1-1-2-1-2-1z" class="L"></path><path d="M310 239l-1-2c1-1 2 0 3 0s2 1 3 2l-1 1v-1h-1-1-2z" class="G"></path><path d="M292 235l-1-1h0c1-1 2-1 3-1h1 0c2 1 3 1 5 1h1c1 1 1 1 2 1v1c-1 0-2 0-3 1v1h-2-3v-1h0-1-3c1-1 1-1 1-2z" class="l"></path><path d="M300 234h1c1 1 1 1 2 1v1c-1 0-2 0-3 1 0-1 0-1-1-2h1v-1z" class="j"></path><path d="M292 235l-1-1h0c1-1 2-1 3-1l3 3h2v1h-4 0-1-3c1-1 1-1 1-2z" class="a"></path><path d="M314 227c0 1 1 3 2 4v1c1 3 2 5 4 7l1 1h2 0c1 1 1 2 1 3v1c-1 0-1 0-2-1h-1l-3 1-6-1h0l3-1h0l-1-1h-2v-2h2v1h1 1v-1s0-1-1-1h0v-2c-1 0-2-1-3-2 0 0-1 0-1 1l-1-1c-1-1-2-2-4-2 1-1 2-1 3-1h3c0-1 1-1 1-1v-2l1-1z" class="l"></path><path d="M323 240h0c1 1 1 2 1 3-1 0-2-2-3-3h2z" class="E"></path><path d="M314 241h4v2 1l-6-1h0l3-1h0l-1-1z" class="Y"></path><path d="M314 227c0 1 1 3 2 4v1c1 3 2 5 4 7 0 1 0 1-1 1l-2-2h0c0-2-2-4-3-5l-2 1s-1 0-1 1l-1-1c-1-1-2-2-4-2 1-1 2-1 3-1h3c0-1 1-1 1-1v-2l1-1z" class="F"></path><path d="M314 227c0 1 1 3 2 4v1h-3l-1 1h-2c1-1 2-1 2-2s1-1 1-1v-2l1-1z" class="L"></path><path d="M291 246l1 1h0-2v1c2 0 5-1 6-1v2h-6v1h4c1 0 1 0 2 1h5v1s0 1-1 1c3 0 9-1 11-2h0v-1c1-1 2-1 3-1 3 0 6-1 9-3h0l1 1h-1 0v1h1l1 2c-1 0-2 1-3 1h-1c-1 1-2 1-3 2h1 0 2c1 1 1 1 1 2h-1v1c-2 1-3 1-5 2h-3c0 1 0 1 1 2h-5v1h1 0c0 1-1 1-1 2-1 1-5 2-6 2h0c-1 0-1 1-2 1h0c-2 1-5 1-7 1 1 0 2-1 3-1 0 0 1 0 1-1l-1-1c-1 0-1 0-1 1h-2v-1h1l1-1-4-1h-1-2c0-2 0-3 1-5 0 0-1 0-1-1s0-4 1-5l-1-1c-1-1-1-2-1-3 1-1 2 0 3-1z" class="C"></path><path d="M294 250c1 0 1 0 2 1h5v1s0 1-1 1l1 1v1h-2v-1h-2-3-3c1-1 3-1 4-2h0-5c1-1 2-1 3-1l1-1z" class="K"></path><path d="M294 254h3 2v1h2v-1c2 0 5 1 7 1l-3 1c-1 0-2 1-3 2h-1-2c-1 1-2 0-3 1-1 0-2-1-3-1s-2-1-2-2 1-1 2-1 1-1 1-1z" class="h"></path><path d="M298 256h4v1c-2 0-2 0-3 1s-2 0-3 1c-1 0-2-1-3-1v-1h6v-1h-1z" class="K"></path><path d="M301 254c2 0 5 1 7 1l-3 1c-1 0-2 1-3 2h-1-2c1-1 1-1 3-1v-1h-4-4v-1h1 4 2v-1z" class="n"></path><path d="M302 258h1l1 1c1 0 1 0 2 1 2-1 3-1 5-2h2c0 1 0 1 1 2h-5v1h1 0c0 1-1 1-1 2-1 1-5 2-6 2h0c-1 0-1 1-2 1h0c-2 1-5 1-7 1 1 0 2-1 3-1 0 0 1 0 1-1l-1-1c-1 0-1 0-1 1h-2v-1h1l1-1-4-1c2-1 5 0 7 0 0-1 1-1 2-1-3-1-5 1-8 0v-1h3v-1c1-1 2 0 3-1h2 1z" class="T"></path><path d="M302 258h1l1 1c1 0 1 0 2 1l-3 1 1-1-1-1c0 1-3 1-4 1 0-1 2-1 2-2h1z" class="U"></path><path d="M311 258h2c0 1 0 1 1 2h-5v1c-2 0-5 1-8 1v-1h2l3-1c2-1 3-1 5-2z" class="M"></path><path d="M314 249c3 0 6-1 9-3h0l1 1h-1 0v1h1l1 2c-1 0-2 1-3 1h-1c-1 1-2 1-3 2h1 0 2c1 1 1 1 1 2h-1v1c-2 1-3 1-5 2h-3-2c-2 1-3 1-5 2-1-1-1-1-2-1l-1-1h-1c1-1 2-2 3-2l3-1c-2 0-5-1-7-1l-1-1c3 0 9-1 11-2h0v-1c1-1 2-1 3-1z" class="G"></path><path d="M321 251v-1c-1 0-2 1-3 1h-2c1-1 2-1 3-1 1-1 2-2 4-3v1h1l1 2c-1 0-2 1-3 1h-1z" class="H"></path><path d="M311 251h3 0 0c-1 0-2 1-3 1h-1v2h-3v1c3 0 6-2 8-1 0 0 0 1-1 1h-2c-1 1-2 1-3 1l-1 1h4 0c1-1 3 0 4-1s3-1 5-1v1c-2 1-3 1-5 2h-3-2c-2 1-3 1-5 2-1-1-1-1-2-1l-1-1h-1c1-1 2-2 3-2l3-1c-2 0-5-1-7-1l-1-1c3 0 9-1 11-2h0z" class="L"></path><path d="M303 258c1-1 2-1 3-1l1 1h0c2-1 3-1 4 0-2 1-3 1-5 2-1-1-1-1-2-1l-1-1z" class="Z"></path><path d="M325 226c1 0 1 0 2 1h0l6 5 2 2h1 1l2 1 1 2 3-1c1 1 1 1 2 1l1 1v-1h1c1 0 1 0 1 1 1 0 2 1 3 2v1c2 2 2 3 4 4l2 3c2 5 6 9 8 13 1 2 2 3 3 5l1 2v3 1s-1 1-1 2l-1 2 1-1v2h0c1 1 1 1 1 2s0 1-1 2c0 1-1 1-2 2l-1-1c0 2 0 2-2 3v-1 1h-1v-2c-1-1-1-1-2-3 0-1-2-2-2-3h0c-1-1-2-1-3-2-1 1-1 1-1 2h-1l1 1-1 1c-1 0-1 0-2 1l-1-1h0l-1 1v1c-2-1-3 0-4 0l-8 1c0 1-1 1-1 1-1 1 0 1 0 2h-1-1c-1-1-2 0-3 0h-3c-1-1-2-1-4-1h0 0-1c2-1 3-1 4-1-1-1-2-1-2-2h0 0v-2c-1 0-2-1-3-1h-6s0 1-1 1h-4l-1 1c0-1-1-1-1-1l-3 1h-1c1-1 1-1 2-1v-1c1 0 2-1 3-1h2v-2c-1 1-2 1-3 1-1-1-1-1-3-1h0c-1 0-1 0-2-1v-1l1-1v1h1 1 0c1-1 2-1 2-2h-1v-2h-1c1-1 2-1 2-1l2-2-7 1h0c1-1 2-1 3-2-1 0-4 1-6 1h0c1 0 1-1 2-1h0c1 0 5-1 6-2 0-1 1-1 1-2h0-1v-1h5c-1-1-1-1-1-2h3c2-1 3-1 5-2v-1h1c0-1 0-1-1-2h-2 0-1c1-1 2-1 3-2h1c1 0 2-1 3-1l-1-2h-1v-1h0 1l2-2h-1-1v-1h0v-1c0-1 0-2-1-3h0-2l-1-1c-2-2-3-4-4-7v-1c1 1 1 1 2 1 0-1-1-1-1-1v-2-1h1l3 2 1-1 2 2 1-1h0l-1-3s0-1 1-1z" class="S"></path><path d="M341 246h0v3h-2-1l-1-2h2 1l1-1z" class="a"></path><path d="M349 245c1 1 2 1 3 0 0 1 0 2 1 3s1 1 1 2l-1 1c0-1-1-2-2-3s-1-1-2-3z" class="g"></path><path d="M323 247c2 0 4-1 5-2h1c-1 2-2 3-4 5l-1-2h-1v-1h0z" class="Y"></path><path d="M340 237l3-1c1 1 1 1 2 1l1 1h0l-2 3c-1-1-1-1-2-1v-1h1l-3-2z" class="V"></path><path d="M321 251v1c1 0 2-1 3-1 2 0 4-2 5-3v-1h1c-1 2-3 3-4 5l-1 1c-1 0-2 1-3 2 0-1 0-1-1-2h-2 0-1c1-1 2-1 3-2z" class="F"></path><path d="M351 240v1c2 2 2 3 4 4l2 3h-3l-1-1v1c-1-1-1-2-1-3v-1c-1-1-2-1-2-3l1-1z" class="Z"></path><path d="M346 238v-1h1c1 0 1 0 1 1 1 0 2 1 3 2l-1 1c0 2 1 2 2 3v1c-1 1-2 1-3 0s-3-2-3-4c1 0 2 1 3 1v-1-1c-1-1-2-1-3-2h0z" class="a"></path><path d="M351 251l2 1h0 2 0l1-1c1 0 3 2 3 3v3h-1-1l-1-1h-1c-1 1-2 1-2 1-1-1-2-3-3-4 0-1 1-1 1-2z" class="I"></path><path d="M339 242h0c0-1-1-1-1-1l1-1 1 1h3 1c3 2 3 4 5 6v1l-3-3h0-1c0 1 0 2-1 3 1 1 1 1 2 1 0 2 2 3 1 5-1-2-2-3-4-4h1c-1-1-1-2-1-2l-2-2h0c-1-1-2-2-2-4z" class="Z"></path><path d="M339 242h2l2 2v1 1 2l-2-2h0c-1-1-2-2-2-4zm7 7c-1 0-1 0-2-1 1-1 1-2 1-3h1 0l3 3c1 1 2 2 2 3s-1 1-1 2c1 1 2 3 3 4v1h-1-1-1l-1-1v2l1 1v1l-1-1s-1-1-2-1h0s1 0 1-1-1-2-1-4h0c1-2-1-3-1-5z" class="l"></path><path d="M346 245h0l3 3c1 1 2 2 2 3s-1 1-1 2c-2-2-3-5-4-8z" class="O"></path><path d="M346 249l5 9h-1l-1-1v2l1 1v1l-1-1s-1-1-2-1h0s1 0 1-1-1-2-1-4h0c1-2-1-3-1-5zm-19-15l4 3h1c0-1 0-1-1-2 1 0 1 0 2 1v-1h2l1-1h1l2 1 1 2 3 2h-1-2-3s-1 0-1 1h-1c-1 0 1 1-1 1-1-1-2-1-3-1h0v-1c-1-1-2-1-3-2l-1-1h1 0c-1-1-1-1-1-2z" class="N"></path><path d="M337 234l2 1 1 2 3 2h-1-2l-1-1h-3-1c1-1 0-2 0-3l1-1h1z" class="f"></path><path d="M325 226c1 0 1 0 2 1h0l6 5 2 2h1l-1 1h-2v1c-1-1-1-1-2-1 1 1 1 1 1 2h-1l-4-3v-1c-1 0-1-1-2-1v1 1l-1-2v-1l1-1h0l-1-3s0-1 1-1z" class="X"></path><path d="M333 232v1c-2 1-2-1-3-1l-1 1c0-1 0-1-1-1s-2-2-2-3h0l3 2v-1c-1-1-2-1-3-2l1-1 6 5z" class="n"></path><path d="M341 246l2 2s0 1 1 2h-1c2 1 3 2 4 4h0c0 2 1 3 1 4s-1 1-1 1c-2-1-2-2-4-2h-1c-1 1-1 0-2 0h1-2-3l-1-1c1-1 1-2 2-3v-1c0-1 1-2 1-3h1 2v-3z" class="I"></path><path d="M341 252l1 2h1v2c-1 0-1 1-2 1h-2v-1h1 0v-1c0-1 1-2 1-3z" class="D"></path><path d="M341 246l2 2s0 1 1 2h-1v4h-1l-1-2v-2l-2-1h0 2v-3z" class="K"></path><path d="M338 249h1 0l2 1v2c0 1-1 2-1 3v1h0-1v1h-3l-1-1c1-1 1-2 2-3v-1c0-1 1-2 1-3z" class="U"></path><path d="M338 249h1 0v2h1c-1 1-1 1-3 1 0-1 1-2 1-3z" class="T"></path><path d="M339 249l2 1v2c0 1-1 2-1 3v1-5h-1v-2z" class="S"></path><path d="M337 253h0c1 1 1 1 2 1v2h1-1v1h-3l-1-1c1-1 1-2 2-3z" class="X"></path><path d="M317 228h1l3 2 1-1 2 2v1l1 2v-1-1c1 0 1 1 2 1v1c0 1 0 1 1 2h0-1l1 1c1 1 2 1 3 2v1h-1v1c0 2-2 3-3 4-1-1-2-1-3-1h0v-1c0-1 0-2-1-3h0-2l-1-1c-2-2-3-4-4-7v-1c1 1 1 1 2 1 0-1-1-1-1-1v-2-1z" class="R"></path><path d="M322 229l2 2v1l-1 1h0c-1-1-1-2-2-3l1-1z" class="E"></path><path d="M324 232l1 2v-1-1c1 0 1 1 2 1v1c0 1 0 1 1 2h0-1-1 0v2h-1l-1-1v1-1h1v-1-1l-2-2h0l1-1z" class="D"></path><path d="M317 229c1 1 2 1 2 3h0c1 2 3 4 4 6h-2v1l2 1h-2l-1-1c-2-2-3-4-4-7v-1c1 1 1 1 2 1 0-1-1-1-1-1v-2z" class="N"></path><path d="M324 238v-1l1 1h1v-2h0 1l1 1c1 1 2 1 3 2v1h-1v1c0 2-2 3-3 4-1-1-2-1-3-1h0v-1c0-1 0-2-1-3h0l-2-1v-1h2 1 0z" class="b"></path><path d="M324 238v-1l1 1h1v-2h0 1l1 1-1 1v1h-3l-1 1h0l-2-1v-1h2 1 0z" class="J"></path><path d="M326 252h1l3-3c1-1 1-2 1-3 1-1 0-2 1-3v-2l1 1h1l1 1 1 1v3h1l1 2c0 1-1 2-1 3v1c-1 1-1 2-2 3l1 1h3 2-1c1 0 1 1 2 0h1v1 1h-1c-1 0-1-1-2 0v1c-1 0-1 0-2-1l-1 1c0 1 1 1 1 1v1c-2-1-4-1-6-1s-5 0-7 2h0-1c-1 0-2 0-3-1l-2 2h-2c-1 1-2 1-3 1l-1 1c0-1-1-1-1-1l-1 1-7 1h0c1-1 2-1 3-2-1 0-4 1-6 1h0c1 0 1-1 2-1h0c1 0 5-1 6-2 0-1 1-1 1-2h0-1v-1h5c-1-1-1-1-1-2h3c2-1 3-1 5-2v-1h1c1-1 2-2 3-2l1-1z" class="P"></path><path d="M333 248c1 0 1 1 0 2 0 1-1 2-2 3h0c-5 4-10 7-16 9h-4c1-1 3-2 4-2v-1l-1 1c-1-1-1-1-1-2h3l1 1h1c1-1 1-1 2-1h1c4-1 9-4 11-7v-1c1-1 1-1 1-2z" class="H"></path><path d="M326 252h1l3-3c1-1 1-2 1-3 1-1 0-2 1-3v-2l1 1h1l1 1h-1v1 1c0 1 0 1-1 2v1c0 1 0 1-1 2v1c-2 3-7 6-11 7h-1c-1 0-1 0-2 1h-1l-1-1c2-1 3-1 5-2v-1h1c1-1 2-2 3-2l1-1z" class="l"></path><path d="M321 256c2-1 4-3 7-3-1 1-4 3-6 4l-2 1c-1 0-1 0-2 1h-1l-1-1c2-1 3-1 5-2z" class="G"></path><path d="M335 243l1 1v3h1l1 2c0 1-1 2-1 3v1c-1 1-1 2-2 3l1 1h3 2-1c1 0 1 1 2 0h1v1 1h-1c-1 0-1-1-2 0v1c-1 0-1 0-2-1l-1 1c0 1 1 1 1 1v1c-2-1-4-1-6-1s-5 0-7 2h0-1c-1 0-2 0-3-1l-2 2h-2l1-2h1c3-1 7-3 9-5 2-1 3-2 3-4h0c1-1 2-2 2-3 1-1 1-2 0-2v-1c1-1 1-1 1-2v-1-1h1z" class="j"></path><path d="M336 247h1l1 2c0 1-1 2-1 3v1c-1 1-1 2-2 3-1 2-3 2-5 3l-1-1c3-2 4-4 6-7 1-1 1-2 1-4z" class="n"></path><path d="M335 256l1 1h3 2-1c1 0 1 1 2 0h1v1 1h-1c-1 0-1-1-2 0v1c-1 0-1 0-2-1l-1 1c0 1 1 1 1 1v1c-2-1-4-1-6-1s-5 0-7 2h0-1c-1 0-2 0-3-1 3-1 6-2 8-4l1 1c2-1 4-1 5-3z" class="e"></path><path d="M359 254l2 2v1l4 5v-1c1 2 2 3 3 5l1 2v3 1s-1 1-1 2l-1 2 1-1v2h0c1 1 1 1 1 2s0 1-1 2c0 1-1 1-2 2l-1-1c0 2 0 2-2 3v-1 1h-1v-2c-1-1-1-1-2-3 0-1-2-2-2-3h0c-1-1-2-1-3-2h-1l-1-2h1c-1-2-4-3-6-4h0c-1 0-2 0-3-1v-1h-1v-1c0-2-5-3-6-4v-1s-1 0-1-1l1-1c1 1 1 1 2 1v-1c1-1 1 0 2 0h1v-1-1c2 0 2 1 4 2h0c1 0 2 1 2 1l1 1v-1l-1-1v-2l1 1h1 1 1v-1s1 0 2-1h1l1 1h1 1v-3z" class="d"></path><path d="M358 277l4 1v5c-1-1-1-1-2-3 0-1-2-2-2-3z" class="D"></path><path d="M358 268l1-1c2 1 3 4 4 6v4l-5-9z" class="Z"></path><path d="M357 268c2 3 3 7 5 10l-4-1h0c-1-1-2-1-3-2h-1l-1-2h1 1c0 1 1 1 1 1h1 0c0-1-1-1-1-2-1-1-1-1-1-2l2-2z" class="O"></path><path d="M367 272l1 2-1 2 1-1v2h0c1 1 1 1 1 2s0 1-1 2c0 1-1 1-2 2l-1-1c0 2 0 2-2 3v-1l1-5-1-2v-4c1 0 1 0 2 1v-1h1 0l1-1z" class="J"></path><path d="M363 273c1 0 1 0 2 1v1c0 1-1 1-1 2v2l-1-2v-4z" class="E"></path><path d="M367 272l1 2-1 2v1l-1-1-1-1v-1-1h1 0l1-1z" class="X"></path><path d="M359 254l2 2v1l4 5v-1c1 2 2 3 3 5l1 2v3 1s-1 1-1 2l-1-2-1 1h0-1v1c-1-1-1-1-2-1-1-2-2-5-4-6l-1 1c-1-3-3-5-4-8l-1-2v-1s1 0 2-1h1l1 1h1 1v-3z" class="V"></path><path d="M361 261c2 3 4 7 6 11l-1 1h0-1l-1-3h0c0-1-1-3-1-4h-1v2c-1-1-2-2-2-3 0-2 0-3 1-4z" class="T"></path><path d="M353 257s1 0 2-1h1l1 1c1 1 1 1 2 1l2 3c-1 1-1 2-1 4h0-1l-1-1v-1c-1-2-2-2-4-3l-1-2v-1z" class="c"></path><path d="M357 257c1 1 1 1 2 1l2 3c-1 1-1 2-1 4-1-2-2-4-3-5l-1-2 1-1z" class="X"></path><path d="M359 254l2 2v1l4 5v-1c1 2 2 3 3 5l1 2v3 1s-1 1-1 2l-1-2c-2-4-4-8-6-11l-2-3c-1 0-1 0-2-1h1 1v-3z" class="D"></path><path d="M359 254l2 2v1l4 5v-1c1 2 2 3 3 5l1 2c-2 0-2-1-3-3h-2s0-1-1-2c-1-2-2-5-4-6v-3zm-8 4h1c1 3 3 5 4 7l1 3-2 2c0 1 0 1 1 2 0 1 1 1 1 2h0-1s-1 0-1-1h-1c-1-2-4-3-6-4h0c-1 0-2 0-3-1v-1h-1v-1c0-2-5-3-6-4v-1s-1 0-1-1l1-1c1 1 1 1 2 1v-1c1-1 1 0 2 0h1v-1-1c2 0 2 1 4 2h0c1 0 2 1 2 1l1 1v-1l-1-1v-2l1 1h1z" class="V"></path><path d="M348 262h0c-2 0-3-1-4-3h0l1 1h2c0 1 1 1 1 2z" class="K"></path><path d="M347 259c1 0 2 1 2 1l1 1v-1l-1-1v-2l1 1h0c1 2 4 4 3 6h-1-1c-1-1-1-2-2-3l-1 1c0-1-1-1-1-2h-2c1 0 1 0 2-1z" class="X"></path><path d="M347 264h2s2 1 2 2c1 1 2 1 4 2l1 1c-1-1-1-2-2-3 1 0 1 0 2-1l1 3-2 2c0 1 0 1 1 2 0 1 1 1 1 2h0-1s-1 0-1-1h1l-2-2c0-1-1-2-2-2-1-1-2-1-2-1v-2s-2-1-3-2z" class="c"></path><path d="M348 269c0-1-1-1-2-2h-1l1-1h1c-1-1-5-3-6-4h0 2c2 1 3 2 4 2 1 1 3 2 3 2v2s1 0 2 1c1 0 2 1 2 2l2 2h-1-1c-1-2-4-3-6-4h0z" class="K"></path><path d="M332 261c2 0 4 0 6 1 1 1 6 2 6 4v1h1v1c1 1 2 1 3 1h0c2 1 5 2 6 4h-1l1 2h1c-1 1-1 1-1 2h-1l1 1-1 1c-1 0-1 0-2 1l-1-1h0l-1 1v1c-2-1-3 0-4 0l-8 1c0 1-1 1-1 1-1 1 0 1 0 2h-1-1c-1-1-2 0-3 0h-3c-1-1-2-1-4-1h0 0-1c2-1 3-1 4-1-1-1-2-1-2-2h0 0v-2c-1 0-2-1-3-1h-6s0 1-1 1h-4l-1 1c0-1-1-1-1-1l-3 1h-1c1-1 1-1 2-1v-1c1 0 2-1 3-1h2v-2c-1 1-2 1-3 1-1-1-1-1-3-1h0c-1 0-1 0-2-1v-1l1-1v1h1 1 0c1-1 2-1 2-2h-1v-2h-1c1-1 2-1 2-1l2-2 1-1s1 0 1 1l1-1c1 0 2 0 3-1h2l2-2c1 1 2 1 3 1h1 0c2-2 5-2 7-2z" class="V"></path><path d="M333 272c2 1 2 2 3 3h-3l-1-2 1-1z" class="N"></path><path d="M335 279c1 1 3 1 5 1l1-1h5c1 1 2 1 3 1v1c-2-1-3 0-4 0v-1h-3c-1 1-1 1-2 1h0l-1-1h-3c-1 1-2 1-3 1v-1c1 0 1 0 2-1h0z" class="U"></path><path d="M342 272l4 2h0c0 1 0 1-1 1v1l3 2c-2 0-2 1-4 1v-2c-2 0-3 1-4 0l2-1c0-1-1-1-2-2h1c0-1 1-1 1-2z" class="D"></path><path d="M342 272l4 2h0c0 1 0 1-1 1v1c-1 0-1-1-2-2h-2c0-1 1-1 1-2z" class="T"></path><path d="M326 281c0-1 1-1 2-1l1-1h-3v-1h3v-1l2 2h4 0c-1 1-1 1-2 1v1c1 0 2 0 3-1h3l1 1h0c1 0 1 0 2-1h3v1l-8 1c0 1-1 1-1 1-1 1 0 1 0 2h-1-1c-1-1-2 0-3 0h-3c-1-1-2-1-4-1h0 0-1c2-1 3-1 4-1-1-1-2-1-2-2h0 0 1z" class="P"></path><path d="M325 281h1 5l3 1c-1 1-2 1-3 2h-2l-2-1c-1-1-2-1-2-2h0 0z" class="R"></path><path d="M340 281h0c1 0 1 0 2-1h3v1l-8 1c0 1-1 1-1 1-1 1 0 1 0 2h-1-1c-1-1-2 0-3 0h-3c-1-1-2-1-4-1h0 0-1c2-1 3-1 4-1l2 1h2c1-1 2-1 3-2 1 0 2-1 3-1h3z" class="K"></path><path d="M327 283l2 1h0-5 0-1c2-1 3-1 4-1z" class="g"></path><path d="M317 274h5c4 0 7 1 11 1h3 0c1 1 2 1 3 1h0c0 1-1 1-3 1h0c-1-1-1-1-2-1h-2c-1 0-1 0-2-1-1 0-3-1-4 0v1c1 0 2 1 3 1v1h-3v1h3l-1 1c-1 0-2 0-2 1h-1v-2c-1 0-2-1-3-1h-6s0 1-1 1h-4l-1 1c0-1-1-1-1-1l-3 1h-1c1-1 1-1 2-1v-1c1 0 2-1 3-1h2v-2c1 0 3 0 5-1z" class="K"></path><path d="M312 277h1s0 1 1 1 1 0 2-1l-2-1h0 3c2 1 4 1 5 2h-6s0 1-1 1h-4l-1 1c0-1-1-1-1-1l-3 1h-1c1-1 1-1 2-1v-1c1 0 2-1 3-1h2z" class="T"></path><path d="M311 270h7c2 0 4-1 7 0h2l2 1 2 2 1-1h1l-1 1 1 2c-4 0-7-1-11-1h-5c-2 1-4 1-5 1-1 1-2 1-3 1-1-1-1-1-3-1h0c-1 0-1 0-2-1v-1l1-1v1h1 1 0c1-1 2-1 2-2l2-1z" class="K"></path><path d="M317 273c2-1 6-1 9-1l-4 1v1h-5v-1z" class="R"></path><path d="M331 273l1-1h1l-1 1 1 2c-4 0-7-1-11-1v-1l4-1c1 1 3 1 5 1z" class="E"></path><path d="M311 270h7c2 0 4-1 7 0h2c-1 0-2 0-2 1-1 0-1-1-2 0-2 0-8-1-9 0l-2 2-1-1-1 1c0-1 0-1 1-3z" class="S"></path><path d="M309 271l2-1c-1 2-1 2-1 3v1h4c1 0 2-1 3-1v1c-2 1-4 1-5 1-1 1-2 1-3 1-1-1-1-1-3-1h0c-1 0-1 0-2-1v-1l1-1v1h1 1 0c1-1 2-1 2-2z" class="X"></path><path d="M325 263h0c2-2 5-2 7-2 0 1 0 1 1 1v1 1 1c-1 0-3 0-4 1 1 0 1 0 2 1v1h-2v1h3v1h0c-1 0-2 0-3 1l-2-1h-2c-3-1-5 0-7 0h-7l-2 1h-1v-2h-1c1-1 2-1 2-1l2-2 1-1s1 0 1 1l1-1c1 0 2 0 3-1h2l2-2c1 1 2 1 3 1h1z" class="b"></path><path d="M322 266c1-1 2-1 3 0l1 1c-2 0-5 1-7 0h-1-3c2 0 4 0 6-1h1z" class="J"></path><path d="M319 264h0l2 2c-2 1-4 1-6 1 0-1-1-1-2-1l1-1c1 0 2 0 3-1h2z" class="d"></path><path d="M329 266c1 0 1 0 2 1v1h-2v1h3v1h0c-1 0-2 0-3 1l-2-1h-2c1-1 2 0 3-2l-1-1c1 0 1 0 2-1h0z" class="O"></path><path d="M325 263h0c2-2 5-2 7-2 0 1 0 1 1 1v1 1 1c-1 0-3 0-4 1h0c-1 1-1 1-2 1h-1l-1-1c-1-1-2-1-3 0h-1l-2-2h0l2-2c1 1 2 1 3 1h1z" class="K"></path><path d="M319 264h5v1l-2 1h-1l-2-2z" class="P"></path><path d="M324 265c2 0 3-1 5 1-1 1-1 1-2 1h-1l-1-1c-1-1-2-1-3 0l2-1z" class="E"></path><path d="M325 263h0c2-2 5-2 7-2 0 1 0 1 1 1v1 1h-3-1c-1-1-3-1-4-1z" class="X"></path><path d="M333 262v1 1h-3v-1c1 0 2 0 3-1z" class="V"></path><path d="M332 261c2 0 4 0 6 1 1 1 6 2 6 4v1h1v1c1 1 2 1 3 1h0c2 1 5 2 6 4h-1l1 2h1c-1 1-1 1-1 2h-1l1 1-1 1c-1 0-1 0-2 1l-1-1h0c-1-1-2-1-2-1l-3-2v-1c1 0 1 0 1-1h0l-4-2h0c-3-1-6-2-10-2v-1h-3v-1h2v-1c-1-1-1-1-2-1 1-1 3-1 4-1v-1-1-1c-1 0-1 0-1-1z" class="k"></path><path d="M337 269c2-1 4 0 6 0 1 0 2 1 3 1-2 0-4-1-6 0-1 0-3-1-3-1z" class="U"></path><path d="M346 270h0c1-1 2-1 2-1 2 1 5 2 6 4h-1l1 2-2-1c-1-2-4-3-6-4z" class="c"></path><path d="M331 268h3c1-1 4-2 6-2l3 3c-2 0-4-1-6 0h-6 1-3v-1h2z" class="K"></path><path d="M346 274c1-1 1-1 2-1v1h1 3l2 1h1c-1 1-1 1-1 2h-1l1 1-1 1c-1 0-1 0-2 1l-1-1h0c-1-1-2-1-2-1l-3-2v-1c1 0 1 0 1-1h0z" class="a"></path><path d="M352 277h1l1 1-1 1c-1 0-1 0-2 1l-1-1c0-1 1-1 2-2z" class="g"></path><path d="M352 274l2 1h1c-1 1-1 1-1 2h-1-1 0c-2-1-3-1-4-3h0 1 3z" class="X"></path><path d="M332 261c2 0 4 0 6 1 1 1 6 2 6 4v1h1v1c1 1 2 1 3 1h0s-1 0-2 1h0 0c-1 0-2-1-3-1l-3-3c-2 0-5 1-6 2h-3v-1c-1-1-1-1-2-1 1-1 3-1 4-1v-1-1-1c-1 0-1 0-1-1z" class="D"></path><path d="M332 261c2 0 4 0 6 1 1 1 6 2 6 4v1c-1-1-4-2-6-3-2 0-4 0-5 1v-1-1-1c-1 0-1 0-1-1z" class="T"></path><path d="M373 274h1l1 1c-1 1-1 2-1 4 1-1 3-2 4-3h2v1c0 2 2 3 3 4v-5h1 0l1 6v-6h1c0 2 0 4-1 6 0 0 0 1 1 1l1 1c-3 5-5 10-3 15 1 4 4 7 6 11 1 2 1 5 1 7-2 1-4 2-7 2s-5-2-7-1v3c2 2 5 2 7 4-1 1-1 3-2 5-2 2-5 3-8 4-4 2-8 2-12 3h-1c-1-1-3 0-3-1l-2-1c-1 1-1 1-1 2 2 1 4 1 6 1h1 4-2l-1 1h-2-2 0c-1-1-2-1-3-1h0-1-1c-1-1-4 0-5-1 0 1-1 0-2 1h-1-3c0-1-1 0-2-1l-1 1h-3c-1 1-1 1-2 1s-1 0-2 1l-3-1-7 1h0c0-1 2-3 3-3h0c2-1 4-1 6-1 0-1 0-1-1-2-1 0-1-1-2-1h-1c-1 0-3 0-5-1h0-1c-1 0-1 0-1-1l2-1h0c-1-1-2-1-3 0v-1l1-1h0c-2-1-4 0-6-1h0l2-2c-1 0-1 0-1-1h-1-4c1-1 3-1 5-2h0-1l9-4-1-1c1-1 1-1 1-2-2 0-4 0-5 1h0-2l-1-1h-1-2c-2-1-3 0-5-2h-1 0c-1 0-1 1-1 1h-2v1h-2c-1 0-2-1-3-2v-1l-2-1c-2 1-3 2-5 3h-1-4-1c-1 0-2 1-3 0v-1l-1-1 2-2 2-2v-1l1 1 2-2h1c1-1 2-2 4-3-1 0-2 0-2-1 1 0 2-1 3-1l-1-1h-3l1-1-1-1c1 0 3-1 4-2h1v-1h0l5-1h1 2 0c0-1-1-1-2-2s-3-1-4-3c-1-1 0-1-1-2v-1h2l1-1v-1s-1 0-1-1h2l9-3 1-1h4c1 0 1-1 1-1h6c1 0 2 1 3 1v2h0 0c0 1 1 1 2 2-1 0-2 0-4 1h1 0 0c2 0 3 0 4 1h3c1 0 2-1 3 0h1 1c0-1-1-1 0-2 0 0 1 0 1-1l8-1c1 0 2-1 4 0v-1l1-1h0l1 1c1-1 1-1 2-1l1-1-1-1h1c0-1 0-1 1-2 1 1 2 1 3 2h0c0 1 2 2 2 3 1 2 1 2 2 3v2h1v-1 1c2-1 2-1 2-3l1 1c1-1 2-1 2-2l1-1h1v-1h2l1-3v-2z" class="m"></path><path d="M354 309h0l1-1h5c-1 1-3 1-4 2h-2c-2 1-3 2-5 2 0 0-1 0-1-1h0c1 0 2-1 2-1l2-1h0v1c1 0 1 0 2-1z" class="L"></path><path d="M326 325v1c-1 0-2 1-3 0-1 0-1 0-1-1-3 0-4 1-6 2h0 5 2c0 1 0 1-1 1 2 1 3 1 5 0 1-1 3-2 4-3h1c1 0 2 0 2-1h2s1 0 1-1h1 1 2 3c1-1 2 0 3 0h0c-1 1-3 0-4 1-2 0-3 0-4 1h-2c-1 0-1 0-2 1-2 0-6 2-8 3-1 1-1 1-2 1s-1 1-1 1h-1l-1 1h1-1c-1 0-1 0-1-1l2-1h0c-1-1-2-1-3 0v-1l1-1h0c-2-1-4 0-6-1h0l2-2c-1 0-1 0-1-1 2 0 4-1 5-1h1l-1 1v1c2-1 4-1 6-2 0 1-1 1-1 2z" class="B"></path><path d="M329 316l1 1-1 1h1 2v1c-1 0-3 0-4 1 1 0 1 0 1 1l-2 2c-2 1-4 1-6 2v-1l1-1h-1c-1 0-3 1-5 1h-1-4c1-1 3-1 5-2h0-1l9-4 4-2h1z" class="d"></path><path d="M329 316l1 1-1 1h1 2v1c-1 0-3 0-4 1-2 0-3 0-5 1l-7 1h-1l9-4 4-2h1z" class="h"></path><path d="M370 291l1 2v4c1 3 0 5 0 7s-1 3-2 4h-1l-1-1v1c-1 0-2 0-3 1l-2-2h0l-1-1v1h-1v-1l-1-1h0l1-2c1-2 2-1 3-3l1 1v1c1 0 1 1 1 2 1 1 2 1 3 1v-1-1l-1-3c0-1 0-3 1-4v-1c0-1 0-2 1-3l1-1z" class="H"></path><path d="M370 291l1 2c-1 1-1 2-1 3 0 2 0 3-1 4l-1 3-1-3c0-1 0-3 1-4v-1c0-1 0-2 1-3l1-1z" class="F"></path><path d="M370 291l1 2c-1 1-1 2-1 3 0 2 0 3-1 4 0-2 1-5 0-8l1-1z" class="M"></path><path d="M363 300l1 1v1c1 0 1 1 1 2 1 1 2 1 3 1v-1l1 1h-1c-1 0-1 0-2 1v1h-4l-1-1v1h-1v-1l-1-1h0l1-2c1-2 2-1 3-3z" class="h"></path><path d="M364 301v1c1 0 1 1 1 2 1 1 2 1 3 1v-1l1 1h-1c-1 0-1 0-2 1 0 0 0-1-1-1h0-2-1v-1l2-2v-1z" class="N"></path><path d="M340 312c1 0 2 0 2 1h0c2-1 4-1 5-1l-1 1c-1 1-2 2-2 4h-1c1 1 1 1 2 1h0c-2 2-5 2-8 3-2 1-4 3-6 3-1 1-3 1-5 1 0-1 1-1 1-2l2-2c0-1 0-1-1-1 1-1 3-1 4-1v-1h-2l1-1h1c2-1 4-4 6-4 1 0 2 0 2-1h0z" class="L"></path><path d="M335 320c2-1 4-2 5-3v2h1l-5 2c-1-1 0-1-1-1z" class="M"></path><path d="M332 317c2 1 3 0 5 0s2-1 4 0h-1c-1 1-3 2-5 3h0-2c-1 0-3 1-4 1 0-1 0-1-1-1 1-1 3-1 4-1v-1h-2l1-1h1z" class="l"></path><path d="M340 312c1 0 2 0 2 1h0c2-1 4-1 5-1l-1 1c-1 1-2 2-2 4h-1c1 1 1 1 2 1l-4 1h-1v-2h1c-2-1-2 0-4 0s-3 1-5 0c2-1 4-4 6-4 1 0 2 0 2-1h0z" class="M"></path><path d="M341 317c0-1 0-1 1-1v-1-1h0c2-1 3-1 4-1-1 1-2 2-2 4h-1c1 1 1 1 2 1l-4 1h-1v-2h1zm-9 19h6v-1l-2-1 1-1c1 0 3-1 4-1h2v-1h2l2-1h1c2-1 2 0 3 0h0c-1 0-1 1-2 1h-1 0l-1 1c-1 0-1 1-3 1l-3 1v1h1c1 0 1 0 1 1h1 1 2-1l-1-1 2-2h2c1-1 2-1 3-2 1 1 2 1 2 1h1c1-1 1-1 2-1h1c1 1 2 1 3 1v2h-3c-1-1-3-1-5 0h-1l-3 1c1 1 2 1 3 0h1l1-1h1v1h2l1 1-2-1c-1 1-1 1-1 2 2 1 4 1 6 1h1 4-2l-1 1h-2-2 0c-1-1-2-1-3-1h0-1-1c-1-1-4 0-5-1 0 1-1 0-2 1h-1-3c0-1-1 0-2-1l-1 1h-3c-1 1-1 1-2 1s-1 0-2 1l-3-1-7 1h0c0-1 2-3 3-3h0c2-1 4-1 6-1h0z" class="B"></path><path d="M332 336h0c2 0 4 0 6 1-3 1-5 2-8 2l-7 1h0c0-1 2-3 3-3h0c2-1 4-1 6-1z" class="J"></path><path d="M335 326h0c-2 1-4 1-6 3l2-1h2l1-1h1c1 1 2 0 3 0 1-1 3 0 4 0 6-2 12-2 17-4h2l1 1v1h-1-2l-4 1c-2 1-4 1-6 1 0 1 0 2-1 2h1s1 0 1-1h3 1c1-1 1-1 1 0 1 0 3-1 4 0v1h-1-2c-1 0-2 0-3 1h-2c-1 0-1-1-3 0h-1l-2 1h-2v1h-2c-1 0-3 1-4 1l-1 1 2 1v1h-6 0c0-1 0-1-1-2-1 0-1-1-2-1h-1c-1 0-3 0-5-1h0-1l1-1h1s0-1 1-1 1 0 2-1c2-1 6-3 8-3z" class="Q"></path><path d="M373 274h1l1 1c-1 1-1 2-1 4h0l-5 10h0 1 1c0 1-1 1-1 2l-1 1c-1 1-1 2-1 3v1c-1 1-1 3-1 4l1 3v1 1c-1 0-2 0-3-1 0-1 0-2-1-2v-1l-1-1c-1 2-2 1-3 3l-1 2h0l1 1v1h1v-1l1 1h0l-2 1h-5l-1 1h0c-1 1-1 1-2 1v-1h0c1-1 1-1 2-1v-1c-1-1-3-2-4-3h-2-3l-1 1v1h-1l-1 1c1-1 1-1 1-2l-1-1-2-1h0c-1-1-1-1-1-2h-2-1c1-1 1-1 2-1h0v-1h-1l-1-1h2l-1-1-1-1-1-1h0c1-1 2-1 3-1 0-1 0-2 1-4l-1-1h-1v-2c-1-1-3-1-5-1 1-1 2-1 2-1h1 1c0-1-1-1 0-2 0 0 1 0 1-1l8-1c1 0 2-1 4 0v-1l1-1h0l1 1c1-1 1-1 2-1l1-1-1-1h1c0-1 0-1 1-2 1 1 2 1 3 2h0c0 1 2 2 2 3 1 2 1 2 2 3v2h1v-1 1c2-1 2-1 2-3l1 1c1-1 2-1 2-2l1-1h1v-1h2l1-3v-2z" class="i"></path><path d="M354 283l3 2h1 0l1 1h-6c0-1 1-2 1-3z" class="h"></path><path d="M354 289c1 0 1 0 1 1 0 2 0 4-1 6v1c-1-2 0-6 0-8z" class="l"></path><path d="M347 287h3-1c-2 2-4 2-7 2v1c1 0 2-1 2-1 1 0 2 0 2-1l1 1h0 1 2l1 1h-3v3c-1 0-1-1-2-1v-1-1h-2 0c0 1-1 1-1 1h-1c-1-1-2-1-4-2h-1v-2l3 1c3 0 5 0 7-1z" class="C"></path><path d="M351 290h0c1 3 0 7 2 9h1c2-2 4-8 6-9 1 1 1 1 1 2 1 2 0 4-1 6 0 1-2 2-3 3h0-6c-3-1-4-4-6-6 0-2-1-3-1-5h2v1 1c1 0 1 1 2 1v-3h3z" class="G"></path><path d="M350 279h0l1 1 3 3c0 1-1 2-1 3l-2 1h-1-3c-2 1-4 1-7 1l-3-1c-1-1-3-1-5-1 1-1 2-1 2-1h1 1c0-1-1-1 0-2 0 0 1 0 1-1l8-1c1 0 2-1 4 0v-1l1-1z" class="T"></path><path d="M351 281l1 1v1h-3v-1l2-1zm-4 4h3l1 2h-1-3v-2z" class="k"></path><path d="M345 281c1 0 2-1 4 0h2l-2 1v1h0c-1 1-2 1-2 1l-1-1 2-1h-1c-2 0-4 1-6 1s-2 0-4-1l8-1z" class="Z"></path><path d="M334 285h1 1c0-1-1-1 0-2 0 0 1 0 1-1 2 1 2 1 4 1 0 1 2 1 2 1 1 1 3 1 4 1v2c-2 1-4 1-7 1l-3-1c-1-1-3-1-5-1 1-1 2-1 2-1z" class="U"></path><path d="M340 288l1-1 2-1v-1c-2 1-4 1-6 1v-1h1c2-1 3-1 5-1 1 1 3 1 4 1v2c-2 1-4 1-7 1z" class="P"></path><path d="M373 274h1l1 1c-1 1-1 2-1 4h0l-5 10h0 1 1c0 1-1 1-1 2l-1 1c-1 1-1 2-1 3v1c-1 1-1 3-1 4v-1c-2-4-1-8-3-11-1-1-4-2-5-2l-1-1h0-1l-3-2-3-3c1-1 1-1 2-1l1-1-1-1h1c0-1 0-1 1-2 1 1 2 1 3 2h0c0 1 2 2 2 3 1 2 1 2 2 3v2h1v-1 1c2-1 2-1 2-3l1 1c1-1 2-1 2-2l1-1h1v-1h2l1-3v-2z" class="T"></path><path d="M355 275c1 1 2 1 3 2h0c0 1 2 2 2 3-1 0-2 0-3-1l-1-1-2-1c0-1 0-1 1-2z" class="h"></path><path d="M351 280c1-1 1-1 2-1l1 1h1 1v1c1 1 2 1 3 1l1 1v1c-1 1-1 1-2 1h0-1l-3-2-3-3z" class="a"></path><path d="M351 280c1-1 1-1 2-1l1 1h1v2c1 0 2 1 2 1v2l-3-2-3-3z" class="k"></path><path d="M373 274h1l1 1c-1 1-1 2-1 4h0l-5 10h0 1 1c0 1-1 1-1 2l-1 1c-1 1-1 2-1 3v1c-1 1-1 3-1 4v-1c0-2 0-6-1-7l-2-5 3-3c1 0 2-1 3-2s1-1 2-3h0l1-3v-2z" class="Y"></path><path d="M338 289c2 1 3 1 4 2h1s1 0 1-1h0c0 2 1 3 1 5 2 2 3 5 6 6h6 0 1c2 0 3-1 5-1-1 2-2 1-3 3l-1 2h0l1 1v1h1v-1l1 1h0l-2 1h-5l-1 1h0c-1 1-1 1-2 1v-1h0c1-1 1-1 2-1v-1c-1-1-3-2-4-3h-2-3l-1 1v1h-1l-1 1c1-1 1-1 1-2l-1-1-2-1h0c-1-1-1-1-1-2h-2-1c1-1 1-1 2-1h0v-1h-1l-1-1h2l-1-1-1-1-1-1h0c1-1 2-1 3-1 0-1 0-2 1-4l-1-1z" class="j"></path><path d="M342 291h1s1 0 1-1h0c0 2 1 3 1 5 2 2 3 5 6 6h6 0 1v1c-4 0-6 1-9-1l-1-1c-3-1-5-6-6-8v-1z" class="O"></path><path d="M345 299c1 1 3 3 4 3 1 1 1 1 2 1 1 1 2 1 2 2h3c0-1 0-1 1-2h3l-1 2h0l1 1v1h1v-1l1 1h0l-2 1h-5l-1 1h0c-1 1-1 1-2 1v-1h0c1-1 1-1 2-1v-1c-1-1-3-2-4-3h-2c-1-1-2-1-3-2s-1-2-2-3h2z" class="h"></path><path d="M353 305h3c0-1 0-1 1-2h3l-1 2h0l1 1v1h1v-1l1 1h0l-2 1h-5l-1 1h0v-1h1c1-1 2-1 2-2l-1-1-1 1h-1l-1-1zm-15-16c2 1 3 1 4 2v1l-1-1h0c0 3 3 4 3 7l1 1h-2c1 1 1 2 2 3s2 1 3 2h-3l-1 1v1h-1l-1 1c1-1 1-1 1-2l-1-1-2-1h0c-1-1-1-1-1-2h-2-1c1-1 1-1 2-1h0v-1h-1l-1-1h2l-1-1-1-1-1-1h0c1-1 2-1 3-1 0-1 0-2 1-4l-1-1z" class="S"></path><path d="M339 293h1c2 2 0 1 0 3 0 0 3 1 3 2h-1c-1 1-1 1-2 1-1-1-1-1-2-3 0-1 0-1 1-3zm4 6c1 1 1 2 2 3s2 1 3 2h-3l-1 1v1h-1l-1 1c1-1 1-1 1-2l-1-1-2-1h0v-1-2s1 0 1 1h1c0-1 1-1 1-2z" class="R"></path><path d="M310 280l1-1h4c1 0 1-1 1-1h6c1 0 2 1 3 1v2h0 0c0 1 1 1 2 2-1 0-2 0-4 1h1 0 0c2 0 3 0 4 1h3c1 0 2-1 3 0 0 0-1 0-2 1 2 0 4 0 5 1v2h1l1 1c-1 2-1 3-1 4-1 0-2 0-3 1h0l1 1 1 1 1 1h-2l1 1h1v1h0c-1 0-1 0-2 1h1 2c0 1 0 1 1 2h0l2 1 1 1c0 1 0 1-1 2l1-1h1v-1l1-1h3 2c1 1 3 2 4 3v1c-1 0-1 0-2 1l-2 1s-1 1-2 1h0l-1 1c-1 0-3 0-5 1h0c0-1-1-1-2-1h0c0 1-1 1-2 1-2 0-4 3-6 4h-1l-1 1h-1l1-1-1-1h-1l-4 2-1-1c1-1 1-1 1-2-2 0-4 0-5 1h0-2l-1-1h-1-2c-2-1-3 0-5-2h-1 0c-1 0-1 1-1 1h-2v1h-2c-1 0-2-1-3-2v-1l-2-1c-2 1-3 2-5 3h-1-4-1c-1 0-2 1-3 0v-1l-1-1 2-2 2-2v-1l1 1 2-2h1c1-1 2-2 4-3-1 0-2 0-2-1 1 0 2-1 3-1l-1-1h-3l1-1-1-1c1 0 3-1 4-2h1v-1h0l5-1h1 2 0c0-1-1-1-2-2s-3-1-4-3c-1-1 0-1-1-2v-1h2l1-1v-1s-1 0-1-1h2l9-3z" class="K"></path><path d="M331 307c2-1 5-2 6-3h0 2c0 1 0 1-1 1-3 2-6 3-10 4h-3v-1c2 0 4-1 6-1z" class="N"></path><path d="M319 308h1l1 1h1c1 0 2 0 3 1h2 1l-1 1v1h4 0 1 2c1 0 2-1 3-1h0c1 0 1 1 1 1h2 0c0 1-1 1-2 1-2 0-4 3-6 4h-1l-1 1h-1l1-1-1-1 4-2v-1h-3 0-1c-1-1-2-1-4-1l1-1h-1c-2 0-3-1-5-1l-1-2z" class="Z"></path><path d="M322 312l1-1 1 1h0l1 1v-1c2 0 3 0 4 1h1 0 3v1l-4 2h-1l-4 2-1-1c1-1 1-1 1-2-2 0-4 0-5 1h0l-1-1-1-1v-1c2 0 3-1 5-1z" class="M"></path><path d="M322 312h2-1c-2 1-3 2-6 2v-1c2 0 3-1 5-1z" class="B"></path><path d="M324 315v-1h2v2h2l-4 2-1-1c1-1 1-1 1-2z" class="Y"></path><path d="M311 306c1-1 1-1 2 0h0c1 1 3 3 4 3 0 0 2 0 3 1 2 0 3 1 5 1h1l-1 1v1l-1-1h0l-1-1-1 1c-2 0-3 1-5 1v1l1 1 1 1h-2l-1-1h-1-2c-2-1-3 0-5-2h-1 0-1c-1 0-2-1-3-1 1-1 4-2 5-3h0-2l-2-1 2-2c2 1 3 0 4 0h1z" class="F"></path><path d="M318 310h2v1c-1 1-3 1-4 2h-3 0l5-3z" class="B"></path><path d="M312 312h1l-1 1v1h2c2 0 3 0 4 1l1 1h-2l-1-1h-1-2c-2-1-3 0-5-2 1-1 3-1 4-1z" class="g"></path><path d="M311 306h1c-1 2-3 1-4 3h-2l-2-1 2-2c2 1 3 0 4 0h1zm-4 7l-1-1c1-1 2-1 4-2h0c1-2 2-3 4-3v1c0 1 0 1-1 2l-1 2c-1 0-3 0-4 1h-1z" class="B"></path><path d="M348 304h2c1 1 3 2 4 3v1c-1 0-1 0-2 1l-2 1s-1 1-2 1h0l-1 1c-1 0-3 0-5 1h0c0-1-1-1-2-1h-2s0-1-1-1h0c-1 0-2 1-3 1-1-1-4-1-4-1 1-2 4-2 6-3 1 0 1-1 2-2 2 0 3 0 4-2l1 1c0 1 0 1-1 2l1-1h1v-1l1-1h3z" class="V"></path><path d="M337 311c1-1 2-2 4-3 0 1 1 1 1 1v1 1h0l2-1c1 1 1 1 2 1h2 0l-1 1c-1 0-3 0-5 1h0c0-1-1-1-2-1h-2s0-1-1-1h0z" class="K"></path><path d="M348 304h2c1 1 3 2 4 3v1c-1 0-1 0-2 1l-2 1s-1 1-2 1h-2c-1 0-1 0-2-1l-2 1 1-1h2v-1h-1s0-1-1-1c0-1 0 0-1-1h1v-1h1v-1l1-1h3z" class="E"></path><path d="M344 306v-1l1-1c0 1 1 1 2 1v1 1c-1 0-1 1-1 1-1-1-1-2-2-2z" class="D"></path><path d="M344 306c1 0 1 1 2 2v2c1 0 1 0 2-1 0 0 2 0 2 1 0 0-1 1-2 1h-2c-1 0-1 0-2-1l-2 1 1-1h2v-1h-1s0-1-1-1c0-1 0 0-1-1h1v-1h1z" class="S"></path><path d="M327 292h3c2 0 4 1 6 1h0l-2 2c1 1 1 1 2 1l1 1 1 1h-2l1 1h1v1h0c-1 0-1 0-2 1h1 2c0 1 0 1 1 2h0l-1 1h-2 0c-1 1-4 2-6 3-2 0-4 1-6 1v1h3-6c0-1-1-1-2-1h-1l1 2c-1-1-3-1-3-1-1 0-3-2-4-3h0c-1-1-1-1-2 0h-1v-2c-1 0 0 0-1-1h2l-1-1c2 0 5-1 7-2 1-1 2-1 4-1 0 0 1 0 2-1h2l3-3h1v-1-1h-2v-1z" class="T"></path><path d="M330 301c1 0 2 0 3 1-1 0-1 0-2 1h-2-1c0-1 1-2 2-2z" class="P"></path><path d="M330 301h2c1-1 0-2 2-2v1h2c0 1-1 1-2 2h-1c-1-1-2-1-3-1z" class="Z"></path><path d="M310 304h0 4l1 1h-1v1c1 0 2 0 2 1 1 0 2 1 3 1h0l1 2c-1-1-3-1-3-1-1 0-3-2-4-3h0c-1-1-1-1-2 0h-1v-2z" class="d"></path><path d="M339 301c0 1 0 1 1 2h0l-1 1h-2 0c-1 1-4 2-6 3l-1-1h2c-1-1-2-1-2-1 0-1 1-1 1-2 1-1 1-1 2-1h1 2c1-1 2-1 3-1z" class="U"></path><path d="M319 308v-1l1-1h-2v-1c3 0 5-1 7-2l2 1c1 0 2 1 3 1 0 0 1 0 2 1h-2l1 1c-2 0-4 1-6 1v1h3-6c0-1-1-1-2-1h-1 0 0z" class="E"></path><path d="M322 308l-1-1v-1h2c1-1 2-1 3-1l1 2c-1 0-3 0-5 1z" class="f"></path><path d="M327 307c1-1 2-1 3-1l1 1c-2 0-4 1-6 1v1h3-6c0-1-1-1-2-1h-1 0 0 3c2-1 4-1 5-1z" class="D"></path><path d="M327 292h3c2 0 4 1 6 1h0l-2 2c1 1 1 1 2 1l1 1h0-1c-1 0-2 0-3 1-1 0-2 0-3 1h1v1h-4c-3 2-7 3-10 3 2-1 8-2 9-4h-1 0l-5 1c-2 1-8 2-10 4h0c-1 0 0 0-1-1h2l-1-1c2 0 5-1 7-2 1-1 2-1 4-1 0 0 1 0 2-1h2l3-3h1v-1-1h-2v-1z" class="a"></path><path d="M322 293l5-1v1h2v1 1h-1l-3 3h-2c-1 1-2 1-2 1-2 0-3 0-4 1-2 1-5 2-7 2l1 1h-2c1 1 0 1 1 1v2c-1 0-2 1-4 0l-2 2 2 1h2 0c-1 1-4 2-5 3 1 0 2 1 3 1h1c-1 0-1 1-1 1h-2v1h-2c-1 0-2-1-3-2v-1l-2-1c-2 1-3 2-5 3h-1-4-1c-1 0-2 1-3 0v-1l-1-1 2-2 2-2v-1l1 1 2-2h1c1-1 2-2 4-3-1 0-2 0-2-1 1 0 2-1 3-1l-1-1h-3l1-1-1-1c1 0 3-1 4-2h1v-1h0l5-1h1 2 0c3 0 6-1 7 1 2 1 5 0 7-1 1 0 3 0 4-1z" class="K"></path><path d="M327 293h2v1 1h-1-4-1l-2 1v-1c2 0 4-1 6-2zm-21 4l2-1h4v2l-4 1-2-2z" class="k"></path><path d="M322 293l5-1v1c-2 1-4 2-6 2-2 2-3 1-5 1h0-5v-1c2 1 5 0 7-1 1 0 3 0 4-1zm-10 5c1-1 2 0 3 0 0 0 0-1 1-1h1 1c-1 2-3 1-4 2v1c1 0 2 0 2-1l1 1c-2 1-5 2-7 2-1-1-1-1-2-1h-2c-1 0-2 1-3 2h-2l-1-1c3 0 4-1 7-2 0-1 1-1 1-1l4-1z" class="S"></path><path d="M301 303h2c1-1 2-2 3-2h2c1 0 1 0 2 1l1 1h-2c1 1 0 1 1 1v2c-1 0-2 1-4 0-1 0-2 0-3-1l-1 1h-3l-1-2 3-1z" class="F"></path><path d="M302 306v-1c1-1 2-1 2-1l1-1h2 1v1c-2 1-4 1-5 1l-1 1z" class="M"></path><path d="M302 298c2 0 3-1 4-1l2 2s-1 0-1 1c-3 1-4 2-7 2l1 1-3 1c0 1-1 1-1 1-2 2-4 2-6 3h-1 0 1l-1-1c-1 0-1 0-1 1-1 0-1 1-2 1h0v-1h0l2-2h1c1-1 2-2 4-3 1 0 3-1 4-2s2-1 4-2v-1z" class="Y"></path><path d="M302 298c2 0 3-1 4-1l2 2s-1 0-1 1l-1-1h-4v-1z" class="l"></path><path d="M290 306h1c3-2 6-3 9-4l1 1-3 1c0 1-1 1-1 1-2 2-4 2-6 3h-1 0 1l-1-1c-1 0-1 0-1 1-1 0-1 1-2 1h0v-1h0l2-2h1z" class="R"></path><path d="M301 294h1 2 1c0 1 0 1-1 1-1 1-2 1-3 1 1 1 1 1 1 2v1c-2 1-3 1-4 2s-3 2-4 2-2 0-2-1c1 0 2-1 3-1l-1-1h-3l1-1-1-1c1 0 3-1 4-2h1v-1h0l5-1z" class="X"></path><path d="M292 299c2-1 4-1 6-2l1 1c-1 1-2 2-3 2 0 1-1 1-1 1l-1-1h-3l1-1z" class="D"></path><path d="M301 294h1 2 1c0 1 0 1-1 1-1 1-2 1-3 1l-3 1c-2 1-4 1-6 2l-1-1c1 0 3-1 4-2h1v-1h0l5-1z" class="F"></path><path d="M291 308c2-1 4-1 6-3 0 0 1 0 1-1l1 2h3l1-1c1 1 2 1 3 1l-2 2 2 1h2 0c-1 1-4 2-5 3 1 0 2 1 3 1h1c-1 0-1 1-1 1h-2v1h-2c-1 0-2-1-3-2v-1l-2-1c-2 1-3 2-5 3h-1-4-1c-1 0-2 1-3 0v-1l-1-1 2-2 2-2v-1l1 1h0v1h0c1 0 1-1 2-1 0-1 0-1 1-1l1 1h-1 0 1z" class="n"></path><path d="M304 308l2 1c-2 1-3 1-4 2h-4v-1l2-1h0l2-1h2z" class="M"></path><path d="M291 308c2-1 4-1 6-3 0 0 1 0 1-1l1 2h3l1-1c1 1 2 1 3 1l-2 2h-2l-2 1h0l-1-1c0 1-1 1-2 1v-1-1l-5 2-1-1z" class="d"></path><path d="M297 307h2l1 1h1 1l-2 1h0l-1-1c0 1-1 1-2 1v-1-1z" class="P"></path><path d="M287 308h0v1h0c1 0 1-1 2-1 0-1 0-1 1-1l1 1h-1 0c0 1-2 2-2 3h0 1c0-1 1-1 2-1l1 1c1-1 2-1 3-1 1-1 1-1 2 0h1l-1 1c-2 1-3 2-5 3h-1-4-1c-1 0-2 1-3 0v-1l-1-1 2-2 2-2v-1l1 1z" class="D"></path><path d="M295 310c1-1 1-1 2 0h1l-1 1c-2 1-3 2-5 3h-1l-1-1v-1c2 0 5-1 6-2h-1z" class="Z"></path><path d="M282 312l2-2 3 3c1-1 3-1 3-1v1l1 1h-4-1c-1 0-2 1-3 0v-1l-1-1z" class="K"></path><path d="M282 312l2-2 3 3h-4l-1-1z" class="O"></path><path d="M310 280l1-1h4c1 0 1-1 1-1h6c1 0 2 1 3 1v2h0 0c0 1 1 1 2 2-1 0-2 0-4 1h1 0 0c2 0 3 0 4 1h3c1 0 2-1 3 0 0 0-1 0-2 1 2 0 4 0 5 1v2h1l1 1c-1 2-1 3-1 4-1 0-2 0-3 1h0l1 1c-1 0-1 0-2-1l2-2h0c-2 0-4-1-6-1h-3l-5 1c-1 1-3 1-4 1-2 1-5 2-7 1-1-2-4-1-7-1 0-1-1-1-2-2s-3-1-4-3c-1-1 0-1-1-2v-1h2l1-1v-1s-1 0-1-1h2l9-3z" class="K"></path><path d="M330 287c0-1 0-1 1-1h1c2 0 4 0 5 1v2h-1c-2-1-4-2-6-2z" class="N"></path><path d="M316 288c2-1 6-2 8-1h0v1l-2 1-3 1c-1-1-2-1-4-1h0v-1h1z" class="k"></path><path d="M324 284c2 0 3 0 4 1h3c1 0 2-1 3 0 0 0-1 0-2 1h-1c-1 0-1 0-1 1-2 0-3-1-4-1h-1c-1 1-5 1-6 0l5-2z" class="D"></path><path d="M322 289c1 0 3 0 4-1 2 0 5 0 6 1l3 1h0c0 1-1 1-1 1-1 0-3 0-4 1h0-3l-5 1c1 0 1-1 2-2l-1-1c-1 0-2 1-3 1h-3-1v-1h3l3-1z" class="C"></path><path d="M332 289l3 1h0c0 1-1 1-1 1-1 0-3 0-4 1-1 0-2 0-2-1-1 0-1-1-2-1l1-1h5zm-35-2v-1h2l4 1v1s1 0 2-1l1 1h2l1-1h5c1 1 1 1 2 1h-1v1h0c2 0 3 0 4 1h-3v1h1 3c1 0 2-1 3-1l1 1c-1 1-1 2-2 2-1 1-3 1-4 1-2 1-5 2-7 1-1-2-4-1-7-1 0-1-1-1-2-2s-3-1-4-3c-1-1 0-1-1-2z" class="b"></path><path d="M302 292c1 0 2 0 3 1 3 1 7-1 11 1h2c-2 1-5 2-7 1-1-2-4-1-7-1 0-1-1-1-2-2z" class="N"></path><path d="M306 288h2l1-1h5c1 1 1 1 2 1h-1v1h0c2 0 3 0 4 1h-3v1h-5c-2 0-8-1-9-3h1s1 0 2-1l1 1z" class="K"></path><path d="M306 288h2l1-1h5c1 1 1 1 2 1h-1v1h0c2 0 3 0 4 1h-3s-1 1-2 1l-2-2c-2 0-4-1-6-1z" class="a"></path><path d="M310 280l1-1h4c1 0 1-1 1-1h6c1 0 2 1 3 1v2h0 0c0 1 1 1 2 2-1 0-2 0-4 1h1 0 0l-5 2-5 1h-5l-1 1h-2l-1-1c-1 1-2 1-2 1v-1l-4-1 1-1v-1s-1 0-1-1h2l9-3z" class="U"></path><path d="M323 284l-1-1c0-1-1-1-2-2h5c0 1 1 1 2 2-1 0-2 0-4 1z" class="X"></path><path d="M311 285c1 0 3-2 4-2h3v1c-1 1-1 2-3 2h-4v-1z" class="K"></path><path d="M318 284h6 0 0l-5 2-5 1h-5l-1 1h-2l-1-1 6-1h4c2 0 2-1 3-2z" class="k"></path><path d="M310 280l1-1h4c1 0 1-1 1-1h6c1 0 2 1 3 1v2h0-8v-1h0 5v-1c-3-1-5 1-9 1-2 1-10 3-12 3l9-3z" class="Z"></path><path d="M301 283c2 0 10-2 12-3v1h1v1l-5 2h0c-1 1-2 1-4 1v1h4c1-1 1-1 2-1v1l-6 1c-1 1-2 1-2 1v-1l-4-1 1-1v-1s-1 0-1-1h2z" class="S"></path><path d="M313 281h1v1l-5 2h0l-9 1c3-2 9-3 13-4z" class="a"></path><path d="M498 541c6-3 17-6 23-4 7 1 12 4 16 10s4 14 3 21c-2 6-5 11-9 15-2 2-4 4-6 5-2 0-3 0-4 1h0l-1-1c-6 1-11 2-17 1-3-1-6-1-9-1-1-1-4-1-6-1h-9-6v1l2 1c-1 0-2 1-3 1l-7-1c6 3 5 5 7 9 1 2 3 4 4 6 1 4 1 7 0 10s-4 6-7 7c-2 0-5 0-7-1 0 0-1-1-1-2-1-1 0-2 0-3 1-1 1-1 2-1s2 1 2 2c1 0 1 1 1 1-1 1-1 1-2 1h-1c0-1 1-1 1-2v-1h-1c-1 1-1 1-1 3 1 1 1 1 3 1 1 0 2-1 3-1 1-2 1-4 1-6-2-5-6-10-11-13-2 0-3-1-5-2l-1 1v2h-1c-2-1-3-1-5-2-5-1-11 0-15 3l-9 5c4 1 12 3 16 0h0c1-1 2-2 3-2 0 1-1 3-2 4 1 0 1 0 2-1 1 0 3 1 5 1l10 1-1 1c-2 2-4 4-6 5-1 1-3 1-4 2l-1-1h-5-1c1 0 3 0 4 1l1 1c-2 0-4 0-6 1-2 4-3 10-5 15-1 3-4 7-6 8h0-1c-1-1-1-1-2 0l-1-1h-2c-5 2-9 0-14-2-2-2-3-4-5-6v-1h0l-1-1c0-1-1-2-2-4v-1-1c-1-1-1-1-1-2h0 0c-1-1-1-2-1-4l6-3c7-4 13-7 19-11 7-5 14-10 20-15 9-8 18-16 25-26 4-6 6-11 10-17-1 4-1 9-2 12l-3 6 1 1c3-4 5-8 7-12 4-2 6-9 9-13h0v1c-1 2-2 5-3 7-2 3-4 6-6 8-1 2-2 3-2 4 5-5 11-12 12-20l2-3c1 0 1 0 2 1l1-1h1c0 1 1 1 1 2h1l1 1z"></path><path d="M450 591c-1 0-5 1-7 1v-1c2-1 4-1 7-2h5l-1 1c-1 0-2 0-4 1z" class="Z"></path><path d="M472 566c-1 0-2 2-3 2 0-2 0-2-1-3h0c1-3 3-6 4-8 0 3-1 6-2 8v1l1-1 1 1z" class="Q"></path><path d="M448 594c-3 0-5 1-8 1-1 1-3 1-5 1 2-2 9-3 11-4l2 1v1z" class="H"></path><path d="M428 609h8l1 1c-1 1-2 1-3 1-1 1-3 0-4 0l1 1-1 1c-1 0-1 0-1-1-1 0-2 0-3-1h0 2v-2z" class="M"></path><path d="M471 572l5 1h0-4l-2 2c-2 2-4 3-7 4h1c-1 1-1 1-2 1 3-3 6-6 9-8z" class="S"></path><path d="M539 559c0 6-1 12-5 17l-1-1 3-5c1-3 1-6 1-9l2-2z" class="B"></path><path d="M470 575c0 1 1 1 2 1 0-1 1-1 2-1v-1c1 0 1 0 2 1 1 0 1 0 1 1h0l-13 3h-1c3-1 5-2 7-4z" class="F"></path><path d="M440 613c2-1 3-2 4-3h0c-2 2-4 3-7 4l1-2c0-1 1-2 2-2v-1c1 0 1 0 2-1 1 0 2 0 3 1h2 1v1c-2 1-4 3-7 4l-1-1z" class="Q"></path><path d="M455 589h6 0-3c1 1 3 0 4 1s2 1 3 2c1 0 2 1 3 2l-1 1c-5-3-11-4-17-4 2-1 3-1 4-1l1-1z" class="a"></path><path d="M467 566h1v3c-2 1-4 3-6 5s-5 4-7 6h-1l13-14z" class="H"></path><path d="M489 548c2-2 4-4 7-5 3-3 10-5 14-5h0l-1 1c-7 2-13 6-19 10l-4 4c1-1 2-3 3-5z" class="B"></path><path d="M455 585c-1-1-2-1-3 0h-3v-1c2-1 5-1 7-2 1 0 3-1 5-1 4-1 9-1 14-3h0l1 1h0l-9 3c-3 0-5 0-7 2v1h-2-3z" class="g"></path><path d="M448 609c1 0 3-1 4 0 1 0 2 1 3 1-2 2-4 4-6 5-1 1-3 1-4 2l-1-1h-5-1-1 0l3-3 1 1c3-1 5-3 7-4v-1h0z" class="k"></path><path d="M448 609h0 4v1h-1c-2 1-4 3-5 5-1 0-2 0-3 1-1-1-2-1-3-1 0 1-1 1-1 1h-1-1 0l3-3 1 1c3-1 5-3 7-4v-1z" class="M"></path><path d="M504 543c7-3 13-5 21-2 5 2 9 6 11 10 1 3 2 6 3 8l-2 2c0-2-1-5-2-7-2-5-5-9-11-11s-13-2-19 1h-1v-1z" class="Q"></path><path d="M414 612l7-5 7 2v2h-2l-4-1h-2c3 1 5 3 8 3l2 1v1c-1 0-4 1-5 1s-3 0-4 1h2c2 1 4 0 5-1h1v1c-2 1-4 2-7 2-2-1-3-2-3-3v-1h-1l-1-1c0-1 0 0 0 0l-1-1c-1 0-1-1-2-1z" class="G"></path><path d="M473 570c-4 1-6 4-10 6-1 1-2 3-4 4h-2l9-8c3-2 5-3 7-5l13-13c5-5 11-8 18-11v1h1c-8 4-14 8-20 14l-4 4-8 8z" class="H"></path><path d="M458 586h10c3 1 6 1 8 1h3-6v1l2 1c-1 0-2 1-3 1l-7-1c6 3 5 5 7 9 1 2 3 4 4 6-1 1-1 3-2 4h0v4c0-7-2-9-6-14h1l-1-1v-1l-1-1 1-1c-1-1-2-2-3-2-1-1-2-1-3-2s-3 0-4-1h3 0l1-1h-1 0c-1-2-4-1-5-1h-1 0l3-1z" class="I"></path><path d="M461 589c1 0 2 0 3 1s2 0 4 1c1 1 2 2 2 3 1 2 1 3 1 4-1-2-2-3-3-4s-2-2-3-2c-1-1-2-1-3-2s-3 0-4-1h3z" class="j"></path><path d="M498 541c6-3 17-6 23-4h0c3 1 6 2 9 4h1c5 4 7 10 9 16h-1c-1-1-1-3-2-4 0-2-1-4-2-6h-1c-2-3-5-5-8-7-3-1-6-1-9-1-2-1-6 0-8 0l1-1h0c-4 0-11 2-14 5-3 1-5 3-7 5v-2l4-7 1-1h1c0 1 1 1 1 2h1l1 1z" class="S"></path><path d="M493 539l1-1h1c0 1 1 1 1 2h1l1 1c-3 0-8 6-9 5l4-7z" class="G"></path><path d="M446 592c6 0 13 0 18 3l4 3c4 5 6 7 6 14-1 1-2 3-3 4h0v-1h0c0-4-2-10-5-13-5-6-11-7-18-8v-1l-2-1z" class="F"></path><path d="M471 615v-1c0-1 0-1 1-1 0-4 0-7-3-10v-1c-1-2-4-5-6-6h-1l2-1 4 3c4 5 6 7 6 14-1 1-2 3-3 4h0v-1z" class="Q"></path><path d="M493 556l1 1c1 0 2-1 3-1h1 1l1-1v1c-2 2-5 4-8 5-1 0-2-1-3-1-2 1-5 2-7 5-1 0-1 0-1 1 0 0-1 0-1 1l-6 4c4 0 7 1 10 3l2 2c2 0 6 0 8-1l1 1c-3 1-6 1-8 0h-5c-2 0-3 0-5 1v-1h0c0-1 0-1-1-1-1-1-1-1-2-1v1c-1 0-2 0-2 1-1 0-2 0-2-1l2-2h4 0l-5-1 2-2h0l8-8 4-4h1v1h1l6-3z" class="C"></path><path d="M476 573h4l1 1c-1 1-3 1-4 2 0-1 0-1-1-1-1-1-1-1-2-1v1c-1 0-2 0-2 1-1 0-2 0-2-1l2-2h4 0z" class="P"></path><path d="M485 566h5c1 0 2 1 2 2l4 3h0l2 2v1 1h-4c-2 1-6 1-8 1l-2-2c-3-2-6-3-10-3l6-4 5-1z" class="W"></path><path d="M485 566h5c1 0 2 1 2 2l4 3h0l2 2h-3l-1-1h-1 0-2c0-1-1-1-2-1-1-1-1-1-2-1l-2-2h0v-1-1z" class="B"></path><path d="M410 614c3 5 1 10 0 16-1 1-1 1-2 3 0 1 2 3 3 4-3-1-5-2-7-4l-1 1-2-2h0l-1-1c0-1-1-2-2-4v-1-1c-1-1-1-1-1-2h0 0c-1-1-1-2-1-4l6-3 1 1-1 1c0 2-1 3-1 5s1 3 2 4c2 1 3 1 4 0 1 0 2-1 2-3v-2c-2 0-2 2-4 3v-1c-1-1-1-2-1-3 1-3 4-5 6-7z" class="m"></path><path d="M396 619l6-3 1 1-1 1h0c-2 2-3 3-3 6s2 6 4 8l1 1-1 1-2-2h0l-1-1c0-1-1-2-2-4v-1-1c-1-1-1-1-1-2h0 0c-1-1-1-2-1-4z" class="I"></path><path d="M476 579l6-1c2 0 4 0 6 1l9 1c-1 1-1 1-2 1v1h0-2c-3-1-7 0-10 1v1h2c0 1 1 0 2 1s5 1 7 1h-8 0l2 1h-9-3c-2 0-5 0-8-1h-10l-3 1h0c-3 0-5 0-8 1v-1c1-1 4-2 6-2 0 1 1 0 2 0h0 3 2v-1c2-2 4-2 7-2l9-3h0z" class="Y"></path><path d="M458 586h3 8c3-1 6-1 10-1v1h-2s-1 0-1 1c-2 0-5 0-8-1h-10z" class="k"></path><path d="M476 579v1h-1 0c-1 0-1 0-1 1-2 0-4 1-5 2h5 1c1 1 3 0 4 0v1c-3 0-6-1-8 0-4 0-7 1-11 1v-1c2-2 4-2 7-2l9-3z" class="I"></path><path d="M476 579l6-1c2 0 4 0 6 1l9 1c-1 1-1 1-2 1v1h0-2c-3-1-7 0-10 1v1h0-4v-1c-1 0-3 1-4 0h-1-5c1-1 3-2 5-2 0-1 0-1 1-1h0 1v-1h0z" class="p"></path><path d="M474 583c0-1-1-1-2-1 1-1 1-1 2-1s1 0 2 1c0 0 1 0 2-1h3c2 0 4-1 7-1v1h0c-2 0-3 1-5 1-2 1-5 1-8 1h-1z" class="L"></path><path d="M488 579l9 1c-1 1-1 1-2 1v1h0-2c-3-1-7 0-10 1v1h0-4v-1c-1 0-3 1-4 0 3 0 6 0 8-1 2 0 3-1 5-1h0l1-1h-1-1v-1h1z" class="J"></path><path d="M505 544c6-3 13-3 19-1s9 6 11 11c1 2 2 5 2 7 0 3 0 6-1 9v-3h0c-1-1 0-4 0-5v-1c-1 0-1-1-1-1l-1-2c-1-5-4-9-8-11-6-4-13-3-19-1 0 3-1 5-3 8v4c0 2-1 3-2 5-2 1-4 2-5 2h0c1-2 4-3 6-5 1-1 0-4 0-6-1 1-2 2-3 2v-1l-1 1h-1-1c-1 0-2 1-3 1l-1-1-6 3h-1v-1h-1c6-6 12-10 20-14z" class="i"></path><path d="M486 558c4-3 9-6 13-9l6-3v1 2c-2 5-7 6-12 7l-6 3h-1v-1z" class="W"></path><path d="M414 612c1 0 1 1 2 1l1 1s0-1 0 0l1 1h1v1c0 1 1 2 3 3 3 0 5-1 7-2v-1h1c2 0 3-2 6-2v3l-1 1c0 3-2 5-2 7l-3 8c1 1 1 1 2 1-1 3-4 7-6 8h0-1c-1-1-1-1-2 0l-1-1h-2c-5 2-9 0-14-2-2-2-3-4-5-6v-1l2 2 1-1c2 2 4 3 7 4-1-1-3-3-3-4 1-2 1-2 2-3 1-6 3-11 0-16l1-1 3-1z" class="m"></path><path d="M418 615h1v1c0 1 1 2 3 3 3 0 5-1 7-2l4 4c0 3-1 6-2 9l-2 1v-1c1-3 2-6 2-9-1-1-1-2-2-2h-2c-2 1-4 1-6 1-2-1-3-3-3-5z"></path><defs><linearGradient id="T" x1="428.325" y1="634.96" x2="413.969" y2="635.924" xlink:href="#B"><stop offset="0" stop-color="#131112"></stop><stop offset="1" stop-color="#2f312e"></stop></linearGradient></defs><path fill="url(#T)" d="M410 614l1-1c1 3 1 5 2 7v4c-1 4-2 12 2 13 3 1 6 1 8-1 2-1 4-4 6-6v1l2-1c-2 4-4 7-7 9-5 2-10 1-15-1-2-1-4-2-6-4l1-1c2 2 4 3 7 4-1-1-3-3-3-4 1-2 1-2 2-3 1-6 3-11 0-16z"></path><path d="M429 616h1c2 0 3-2 6-2v3l-1 1c0 3-2 5-2 7l-3 8c1 1 1 1 2 1-1 3-4 7-6 8h0-1c-1-1-1-1-2 0l-1-1h-2c-5 2-9 0-14-2-2-2-3-4-5-6v-1l2 2c2 2 4 3 6 4 5 2 10 3 15 1 3-2 5-5 7-9 1-3 2-6 2-9l-4-4v-1z" class="H"></path><path d="M430 633c1 1 1 1 2 1-1 3-4 7-6 8h0-1c-1-1-1-1-2 0l-1-1c4-2 6-4 8-8z" class="O"></path><path d="M500 556c1 0 2-1 3-2 0 2 1 5 0 6-2 2-5 3-6 5h0c1 0 3-1 5-2 1-2 2-3 2-5v-4c2-3 3-5 3-8 6-2 13-3 19 1 4 2 7 6 8 11l1 2s0 1 1 1v1c0 1-1 4 0 5h0v3l-3 5 1 1c-1 2-2 4-4 5 0 1 1 1 1 2-2 2-4 4-6 5-2 0-3 0-4 1h0l-1-1c-6 1-11 2-17 1-3-1-6-1-9-1-1-1-4-1-6-1l-2-1h0 8c-2 0-6 0-7-1s-2 0-2-1h-2v-1c3-1 7-2 10-1h2 0v-1c1 0 1 0 2-1l-9-1c-2-1-4-1-6-1l-6 1-1-1 2-1c2-1 3-1 5-1h5c2 1 5 1 8 0l-1-1h4v-1-1l-2-2h0l-4-3c0-1-1-2-2-2h-5l-5 1c0-1 1-1 1-1 0-1 0-1 1-1 2-3 5-4 7-5 1 0 2 1 3 1 3-1 6-3 8-5z" class="m"></path><path d="M520 555c2 3 3 6 3 10l-1-1v-3h-1c1 2 1 2 1 4-1-2-1-5-2-6v-1-3z" class="e"></path><path d="M522 565c0-2 0-2-1-4h1v3l1 1c1 2 0 6-1 8 0 1-1 2-1 3-1 0-1 0-1-1h0c-1 1-2 1-3 1h-1c3-3 5-6 6-11z" class="C"></path><path d="M482 565h0c3 0 5-2 8-2l4 4 3 3-1 1h0l-4-3c0-1-1-2-2-2h-5l-5 1c0-1 1-1 1-1 0-1 0-1 1-1z" class="O"></path><path d="M497 570l3 3c5-4 10-9 14-14 1-1 2-2 2-3 1-2 0-6-1-7 2 2 4 4 5 6v3l-3-3s0 2-1 3c-3 4-7 8-11 12-2 2-4 4-6 5h0c-2 1-3 1-4 1l-1-1h4v-1-1l-2-2 1-1z" class="I"></path><path d="M516 558c1-1 1-3 1-3l3 3v1c1 1 1 4 2 6-1 5-3 8-6 11-3 4-7 6-12 7 0-2 1-2 2-3-1-1-2-1-3-1-1 1-2 2-2 1l-2-2-2 2-9-1c-2-1-4-1-6-1l-6 1-1-1 2-1c2-1 3-1 5-1h5c2 1 5 1 8 0 1 0 2 0 4-1h0c2-1 4-3 6-5 4-4 8-8 11-12z" class="W"></path><path d="M516 558c1-1 1-3 1-3l3 3v1c1 1 1 4 2 6-1 5-3 8-6 11-3 4-7 6-12 7 0-2 1-2 2-3h0 0c0-1 0-2 1-3v-1l1-1h1l-1 1h1l1-1h0c0-1 1-1 1-3h0c0-1 1-2 2-3h0c0 3-2 5-2 8v1c2-1 3-2 4-3v-1h1v-1c0-1 1-1 1-2h0v-2-1l1-3v-1c-1 0-1 0-1-1v-1-1h0c-1-1-1-2-1-3z" class="M"></path><path d="M516 558c1-1 1-3 1-3l3 3v1c-1 2-1 3 0 6h-1 0-1v-1c-1 0-1 0-1-1v-1-1h0c-1-1-1-2-1-3z" class="B"></path><path d="M535 560s0 1 1 1v1c0 1-1 4 0 5h0v3l-3 5 1 1c-1 2-2 4-4 5 0 1 1 1 1 2-2 2-4 4-6 5-2 0-3 0-4 1h0l-1-1c-6 1-11 2-17 1-3-1-6-1-9-1-1-1-4-1-6-1l-2-1h0 8c-2 0-6 0-7-1s-2 0-2-1h-2v-1c3-1 7-2 10-1h2 0v-1c1 0 1 0 2-1l2-2 2 2c0 1 1 0 2-1 1 0 2 0 3 1-1 1-2 1-2 3 5-1 9-3 12-7h1c1 0 2 0 3-1h0c0 1 0 1 1 1 3 0 6-1 10-3l1-2h-1c-2 0-3 1-5 1 1-2 4-1 6-3 1-1 1-4 1-6 0-1 1-2 2-3z" class="J"></path><path d="M514 583c2-1 4-3 6-5v3 1c-2 1-5 1-7 2h0c0-1 0-1 1-1z" class="d"></path><path d="M494 586h3c2 1 5 1 8 2v1h-2c-3-1-6-1-9-1-1-1-4-1-6-1l-2-1h0 8z" class="F"></path><path d="M531 573l1-1v-2c0-1 1-2 2-3v5c-2 4-6 9-11 11-6 3-11 4-19 4 3-1 6-2 8-2 3 0 7-1 9-3l1-1c2-1 3-3 5-3h0c1-1 2-1 2-2 1-1 1-2 2-3z" class="M"></path><path d="M533 575l1 1c-1 2-2 4-4 5 0 1 1 1 1 2-2 2-4 4-6 5-2 0-3 0-4 1h0l-1-1c-6 1-11 2-17 1h2v-1h10c8-1 14-7 18-13z" class="Y"></path><path d="M520 588c4-2 7-4 10-7 0 1 1 1 1 2-2 2-4 4-6 5-2 0-3 0-4 1h0l-1-1z" class="C"></path><path d="M531 573h0c-1 1-1 2-2 3 0 1-1 1-2 2h0c-2 0-3 2-5 3l-1 1h-1v-1-3c-2 2-4 4-6 5v-1c-5 3-13 5-18 4 0 0-1 0-1-1h-8c-1-1-2 0-2-1h-2v-1c3-1 7-2 10-1h2 0v-1c1 0 1 0 2-1l2-2 2 2c0 1 1 0 2-1 1 0 2 0 3 1-1 1-2 1-2 3 5-1 9-3 12-7h1c1 0 2 0 3-1h0c0 1 0 1 1 1 3 0 6-1 10-3z" class="F"></path><path d="M497 580l2-2 2 2-3 2h-3v-1c1 0 1 0 2-1z" class="C"></path><path d="M531 573h0c-1 1-1 2-2 3 0 1-1 1-2 2h0c-2 0-3 2-5 3l-1 1h-1v-1-3c-2 2-4 4-6 5v-1c2-1 3-3 4-4-5 3-9 5-15 6-1 1-3 1-5 1h-1c0-1-1-1-1-1h0 2 3c1 0 2 0 3-1 5-1 9-3 12-7h1c1 0 2 0 3-1h0c0 1 0 1 1 1 3 0 6-1 10-3z" class="b"></path><path d="M524 577c1-1 3-1 4-1 0 1-1 1-1 2-2 0-3 2-5 3l-1 1h-1v-1-3l4-1z" class="W"></path><path d="M520 578l4-1c-1 1-2 3-2 4l-1 1h-1v-1-3z" class="Y"></path><defs><linearGradient id="U" x1="327.905" y1="498.761" x2="333.168" y2="443.601" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262525"></stop></linearGradient></defs><path fill="url(#U)" d="M350 399l2 1c1 1 1 1 2 1l9 7 7 5 5 5c0 1 1 1 1 1l4 5 6 8 9 15v2c6 14 9 29 10 44v7l-1 1c-14 0-28 1-42 1l-29-3-31-2-21-2-28-3v-1h3v-1-2c1-2 1-7 3-8v1h0v-3l-1-1-1-1c0 1-1 1-1 1h-2-1 0-1c-1 1-2 1-2 1-2 1-3 1-4 1v-1l-1-1c-1 0-1 1-2 0v-1c0-1 2-2 4-2v-1h1l-1-1c0-1 0-1 1-1v-1h0v-2c1 1 0 1 1 0v-1l1-1v-1c2 0 4-2 6-2l2 1c1 0 2 0 3 1h1c2 0 3-1 5-3l2 2h0l2-1-2-2-1 1c0-1-1-1-2-1l-2-2v1c-1 0-2 0-3-1v-1l-1-1-1-1h0l-1-1h0l-1 1 1 1h-1c0-1 0-1-1-2h0l-1-1c-1 0-1-1-1-1v-2l2-1v-1c-1 0-1 0-1-1 1-1 1-1 3-1 1 0 1 0 1-1v-1l-2-1h3v1l1-1v-2h2c1-1 1-1 1-2 1-1 1-1 1-2h1v-1h1l1 2h-1c-1 3-1 4 0 6h1c1 1 2 2 4 2v1h2l1-1v-2l-1-1 1-2-1-1 1-1c1 0 1 1 2 1 0 1 1 3 2 4h2v1c1 0 3-1 3-1 1 1 1 2 1 3l1 1 1 2h1l-1 2s0 1-1 2l1 1 1-2 2 2v4h-1c0 2 0 3 1 4 1-1 1-2 1-3h0l1-1v2h1l1-1v3c2-2 0-3 2-5v1l2-2c0 1 0 1 1 2h-1c1 1 1 1 2 3 0-2 0-2 1-3h1v1c1 0 1-1 2-2h0l2-1 1 1 1-1h1l1-2h1c0 2 0 3-1 4 1 1 1 1 1 2 1 0 2-1 2-1l2-2c-1-1-1-1-1-2l1-1h0c1-2 1-4 0-5h-1v-1 1c1-1 2-2 2-3v-1c1-1 1-2 1-3h1 2v-1c1-1 1-3 2-4h1l2-2c1 0 1 1 2 2h0l-1 1v2l2-2h0v1 2l1-3c1 2 0 3 1 4l1-3 1 1h2c1 0 1-1 2-1 0-1 1-2 1-2 0-1-1-2-2-3l1-1h0c0-2 1-3 1-4l1 1-1 1h1l1-2c1-1 1-2 1-3v-1l2-1h2c0-1 0-2 1-3l-1-2c1-1 1-2 1-3l1 1h0c-1-1 0-4 0-5v-1l1 1h0v-1-2-2c1-1 1-2 1-3v-1c0-1 1-2 1-2 0-2 0-3 1-4z"></path><path d="M300 472h1v2l-1 1-1-1 1-2z" class="C"></path><path d="M397 465v-1c1 1 2 4 2 6l1 1-2 1v-4l-1-3z" class="h"></path><path d="M270 459h2c0 1 0 2 1 3h1c0-1 0-1 1-1 1 1 1 2 2 3h-3v-2c-2 0-3-1-4-2v-1z" class="O"></path><path d="M283 482c2 0 2 0 3 1l1-1c0 2-1 2-1 4h-1-1c-1-1-1-1-1-3 1 0 1-1 0-1z" class="i"></path><path d="M286 471c1 0 2-1 2-1h2 0c0 2 0 3-1 5-1-1-2-2-3-4z" class="N"></path><path d="M270 488h2l1 2h0c1 0 2 1 2 2-1 0-3-1-5 0h0c-1-1 0-1 0-2v-2z" class="I"></path><path d="M305 471h0c0 1 1 1 1 1v1c-2 1-2 1-2 2l-1 1-1 1v-2-3h1v1h1c0-1 0-2 1-2z" class="J"></path><path d="M269 484c0-1 0-1 1-2s2 0 3 0l-1 1v1 4h-2l1-2-2-2z" class="e"></path><path d="M269 461v-1h1c1 1 2 2 4 2v2h1v2l-1-1h-1c0 1 0 1-1 1v1l-1-1c1-2 0-2 0-3h0l-2-2z" class="X"></path><path d="M278 480l2 2v3c1 1 1 1 1 2l1 1-1 1c-1-1 0-2-2-3h0c0 1 0 2-1 3v-7-2z" class="J"></path><path d="M292 472s1 1 1 2v2h1v2c0 2 0 2-1 3l-1-1c-2-2-1-6 0-8z" class="e"></path><path d="M395 447v2l-2 2c0 1 0 2 1 2l2 12c-2-2-1-4-2-6s-1-3-1-5c0-1 0-3-1-4 0-2 1-2 3-3z" class="O"></path><path d="M344 490v1h2 2 0l3-1-3 5h0l-1-1h0-1-2c-1-1-1-2-1-3l1-1zm35-3l1 1c0 1 0 1 1 2h1v1c1 2 1 4 0 6-1 1-1 1-3 1v-1c1 0 1 0 1-1s0-1 1-2v-1l-3-3s1-1 1-2c-1 0-1 0 0-1z" class="j"></path><path d="M290 490l7 4h-8c-1 0-2 0-4-1h0 1l1-1h-2c1 0 3 0 4-1h1v-1z" class="f"></path><path d="M376 490h2l3 3v1c-1 1-1 1-1 2s0 1-1 1v1h0l-1 1-1-2c0 1 0 1-1 2 0-3 1-5 0-9h0z" class="d"></path><path d="M296 484h1 1c1-2 2-3 3-4h1v1l1 1c-2 2-3 4-5 6-1 0-1 0-2-1v-3h0z" class="K"></path><path d="M280 482c1-2-2-4-2-5h2 0l1 1c1 0 1 1 2 2v2c1 0 1 1 0 1 0 2 0 2 1 3l-2 2-1-1c0-1 0-1-1-2v-3z" class="E"></path><path d="M300 468l1-3c0 1 0 2 1 2v1l-1 1v-1c-1 0-1 0-1 1-1 0-1 0-1 1v1h0v-1c1 1 1 1 0 2h1l-1 2h0-1c0 1-1 2-2 3h-1v-3c1-1 1-2 1-3h0 1c0-1 0-2-1-3h2l1 1c0-1 0-1 1-1z" class="e"></path><path d="M295 477v-3c1-1 1-2 1-3h0 1c1 0 1 1 2 0 0 2-2 4-3 5l-1 1z" class="E"></path><path d="M302 489h1c0-1 1-1 1-2s1-1 1-2c0 2-1 4 0 5h0 0c1-1 2-2 3-4l1 1 1-1v1c1 0 1 1 1 1-1 1 0 1 0 2-1 0-1 1-2 2-1 0-2 0-3-1h0-1c-2-1-3-1-3-2z" class="Y"></path><path d="M267 489l-1 1c-2 1-5 0-7 0 0-3-1-5 0-7v3c1 0 1-1 2-1 0 0 2 2 3 2v1c2 1 1-1 3 1z" class="M"></path><path d="M308 486h0c1-2 4-5 6-6 1 1 0 2 0 3l1 1-4 7h0v-1c0-1-1-1 0-2 0 0 0-1-1-1v-1l-1 1-1-1z" class="d"></path><path d="M304 459l2-1 1 1-1 1c0 1 0 2 1 4-1 0-1 1-1 1l-1-2h0l-1 2-1 1h0l-1 1c-1 0-1-1-1-2l-1 3-1-1c0-2 0-3 1-4 0-2 0-2 1-3h1v1c1 0 1-1 2-2h0z" class="V"></path><path d="M304 459l2-1 1 1-1 1c0 1 0 2 1 4-1 0-1 1-1 1l-1-2h0l-1 2h-1c-1-1-1-2-1-3l1-1 1-2h0z" class="n"></path><path d="M340 487c1-1 1-1 2-1s1 0 2-1 2-2 4-3h0l2 2c-2 1-5 3-6 5v1l-1 1h0c-1 0-2 1-3 1h-1v-1l-1-2s0-1 1-1l1-1h0z" class="f"></path><path d="M274 464h3c2 2 3 4 4 6 1 1 2 2 2 4l-1 1 1 1c-1 0-1 0-2 1v1l-1-1-1-2-3-2 2-2h1l1-1v-1h-2c-1 0 0-1-1-1 0-1-1-1-1-2l1-1-1-1h-1-1z" class="E"></path><g class="C"><path d="M281 470c1 1 2 2 2 4l-1 1c0-2-2-3-2-4 1 0 1 0 1-1z"></path><path d="M279 475v-1c2 0 2 1 3 2v-1l1 1c-1 0-1 0-2 1v1l-1-1-1-2z"></path></g><path d="M275 464h1l1 1-1 1c0 1 1 1 1 2 1 0 0 1 1 1h2v1l-1 1h-1l-2 2h0l-1-1h-4c0-1 0-1-1-2l1-1h1 0l-1-1 1-1v-1c1 0 1 0 1-1h1l1 1v-2z" class="h"></path><path d="M276 473c0-1-1-2-1-4h1c0 1 1 1 1 2h1l-2 2h0z" class="R"></path><path d="M272 467v-1c1 0 1 0 1-1h1l1 1h0c-1 1-1 2-2 2 1 1 1 0 1 2l-1 1 2 1h-4c0-1 0-1-1-2l1-1h1 0l-1-1 1-1z" class="T"></path><path d="M259 478c0 1 1 1 1 2 2 0 2-2 4 0l1 1 2-2 1 1h-1c0 3 1 6 0 9-2-2-1 0-3-1v-1c-1 0-3-2-3-2-1 0-1 1-2 1v-3-2h0v-3z" class="B"></path><path d="M273 482h1l2-2s-1 0 0-1c1 0 1 0 2 1v2 7c0 2-1 2-3 3 0-1-1-2-2-2h0l-1-2v-4-1l1-1z" class="H"></path><path d="M310 473h4c-1 1-1 1-1 2l-1 1s-1 0-2 1l-2 2c-1-1-1-1-2 0-1 0-2 1-3 3l-1-1v-1h-1c-1 1-2 2-3 4h-1-1c1-4 3-6 6-7l1-1 1-1 6-2z" class="d"></path><path d="M310 473h4c-1 1-1 1-1 2l-1 1h0v-1c-3 2-5 1-8 3h0-1v-2l1-1 6-2z" class="B"></path><path d="M398 468v4l2-1c0 7 1 15-3 20h0v-1-1l-1-1v-2h0v-1l-1 2h0c-1-2 1-6 1-9 0-1 0-3 1-4 0-1 0-2 1-3v-3z" class="S"></path><path d="M398 468v4c1 3 1 5 1 7l-1-1-1-1v-3c0-1 0-2 1-3v-3z" class="O"></path><path d="M397 481l1 1c0 3 0 5-1 8v-1l-1-1v-2h0v-1c0-1 1-3 1-4z" class="f"></path><path d="M397 474v3l1 1 1 1c-1 1-1 2-1 3l-1-1c0 1-1 3-1 4l-1 2h0c-1-2 1-6 1-9 0-1 0-3 1-4z" class="T"></path><path d="M397 477l1 1 1 1c-1 1-1 2-1 3l-1-1v-4z" class="J"></path><path d="M298 458c0 1 0 1 1 2h-1c1 1 1 1 2 3-1 1-1 2-1 4l1 1c-1 0-1 0-1 1l-1-1h-2c1 1 1 2 1 3h-1 0c0 1 0 2-1 3v3-1h-1-1v-2c0-1-1-2-1-2 0-2 0-3 1-5 0-2 0-2 1-3 2-2 0-3 2-5v1l2-2z" class="C"></path><path d="M293 467h1c0 1 1 2 1 3s0 3-2 4c0-1-1-2-1-2 0-2 0-3 1-5zm5-7c1 1 1 1 2 3-1 1-1 2-1 4l1 1c-1 0-1 0-1 1l-1-1h-2 0s-1-1-1-2 1-4 1-5h1c0 1 0 1 1 1v-2z" class="E"></path><path d="M389 473c1 0 1 1 1 1l3 6s0 1 1 1h1v-4h0v1h1c0 3-2 7-1 9h0c0 2-1 4-2 5v-1-2c1-1 1-1 0-3h-1v4h-1c0 1-1 2-1 3l-1 1v-1c-1-1-1-1-2-1 0-2 0-4 1-6s2-6 1-8l-1-1c0-2 1-3 1-4z" class="c"></path><path d="M388 486v3c1 0 1 2 1 2h1v-3l2 2h-1c0 1-1 2-1 3l-1 1v-1c-1-1-1-1-2-1 0-2 0-4 1-6z" class="J"></path><path d="M392 486l-1 1c-1-2 1-4 1-5 0-2-2-4-2-5v-3l3 6s0 1 1 1h1v-4h0v1h1c0 3-2 7-1 9h0c0 2-1 4-2 5v-1-2c1-1 1-1 0-3h-1z" class="R"></path><path d="M367 486c1-1 1-1 2 0 1 0 1 0 1 1l1-1 3 2 1 1c1 0 1 0 1 1s-1 1-1 2l-1 3c0 1 0 2-1 3h-5c-1 0-3 0-4-1v-4l1-2v-1-1c1-1 1-2 1-3h1z" class="L"></path><path d="M365 490h1c0-1 1-1 1-2h1c1 1 1 2 0 4v-2c-1 0-2 0-3 1v-1z" class="M"></path><path d="M365 491c1-1 2-1 3-1v2c0 1-1 1-2 2-1 0-1-1-2-1l1-2z" class="H"></path><path d="M367 486c1-1 1-1 2 0 1 0 1 0 1 1l1-1 3 2 1 1c1 0 1 0 1 1s-1 1-1 2l-1 3h-2c0-1-1-1-1-2 0 1-1 2-1 3-2-1 0-5 0-7l-1-1s0-1-1-1v-1h-1z" class="M"></path><path d="M371 493c0-1 0-1 1-2l1 1c0 1-1 2-1 3 0-1-1-1-1-2z" class="Y"></path><path d="M374 488l1 1c1 0 1 0 1 1s-1 1-1 2c-1 0-1 0-1-1v-3z" class="G"></path><path d="M374 491c0 1 0 1 1 1l-1 3h-2c0-1 1-2 1-3l1-1z" class="H"></path><path d="M308 458h1l1-2h1c0 2 0 3-1 4 1 1 1 1 1 2 1 0 2-1 2-1l2-2 1 2c0 1-1 1-1 2v3 2h1 0c0 1 0 1-1 2 1 0 1 1 2 1v1l-3 1h0-4l-6 2c0-1 0-1 2-2v-1s-1 0-1-1h0v-3c1-1 1-2 1-3 0 0 0-1 1-1-1-2-1-3-1-4l1-1 1-1z" class="J"></path><path d="M313 465c1 0 1 1 2 1v2h1 0c0 1 0 1-1 2-1-1-2-3-2-5z" class="D"></path><path d="M311 468l1 1v3c1 0 1 1 2 1h0-4v-1l1-4z" class="X"></path><path d="M313 461l2-2 1 2c0 1-1 1-1 2v3c-1 0-1-1-2-1h-1v4l-1-1s0-1-1-1v-1h-1c0-1 1-1 2-2v-2c1 0 2-1 2-1z" class="N"></path><path d="M309 466h1v1c1 0 1 1 1 1l-1 4v1l-6 2c0-1 0-1 2-2v-1s-1 0-1-1h0v-3c1 1 1 1 1 2h1v-1l1-1h1v-2z" class="D"></path><path d="M310 467c1 0 1 1 1 1l-1 4h-1c-1 0-1 0-2 1v-1l2-1s1-1 1-2v-2z" class="O"></path><path d="M308 458h1l1-2h1c0 2 0 3-1 4 1 1 1 1 1 2v2c-1 1-2 1-2 2v2h-1l-1 1v1h-1c0-1 0-1-1-2 1-1 1-2 1-3 0 0 0-1 1-1-1-2-1-3-1-4l1-1 1-1z" class="b"></path><path d="M308 458h1l1-2h1c0 2 0 3-1 4 0 1 0 2-1 3v-2c-1 1-1 2-2 3-1-2-1-3-1-4l1-1 1-1z" class="l"></path><path d="M386 453l-1-2 1-1 2 3c0 1 1 1 1 2 1 2 3 4 3 6l1 1c1-1 1-2 1-3 1 2 0 4 2 6v3c0-1 0-2 1-3l1 3v3c-1 1-1 2-1 3-1 1-1 3-1 4h-1v-1h0v4h-1c-1 0-1-1-1-1l-3-6s0-1-1-1h1v-2l-3-5h1c1 0 1 1 2 1 0-2-3-7-2-9h0v1c1 0 1 1 2 2h0l1-1c-1-1-1-2-2-4 0-1-2-2-3-3z" class="C"></path><path d="M393 470l1 2s0 1 1 1v4 4h-1c-1 0-1-1-1-1 0-3 0-6-1-9 1 0 1-1 1-1z" class="T"></path><path d="M386 453l-1-2 1-1 2 3c0 1 1 1 1 2 1 2 3 4 3 6l1 1 1 2c0 1 0 3-1 4v2s0 1-1 1l-2-10h0l1-1c-1-1-1-2-2-4 0-1-2-2-3-3z" class="U"></path><path d="M392 461l1 1 1 2c0 1 0 3-1 4-1-2-1-5-1-7z" class="T"></path><path d="M394 459c1 2 0 4 2 6v3c0-1 0-2 1-3l1 3v3c-1 1-1 2-1 3-1 1-1 3-1 4h-1v-1h0v-4c-1 0-1-1-1-1l-1-2v-2c1-1 1-3 1-4l-1-2c1-1 1-2 1-3z" class="E"></path><path d="M394 464c1 3 1 6 1 9-1 0-1-1-1-1l-1-2v-2c1-1 1-3 1-4z" class="S"></path><path d="M397 465l1 3v3c-1 1-1 2-1 3-1 1-1 3-1 4h-1v-1c0-2 0-7 1-9 0-1 0-2 1-3z" class="i"></path><path d="M353 482h2l1 3h3c0 2-1 3-2 4s-1 3-1 4c1 1 1 1 2 1 2-2 2-6 4-8 2-1 3-1 4 0 0 1 0 2-1 3-2 0-2 0-3 1s-2 5-3 6h-1l-2 1-1-1c-1-1-3 0-3-3v-4l3-3c-1 0-2 1-3 3l-1 1-3 1h0-2-2v-1-1c1-2 4-4 6-5 1-1 2-1 3-1v-1z" class="L"></path><path d="M352 493v-4c1 1 2 2 3 2-1 1-1 2-2 2h-1z" class="H"></path><path d="M353 482h2l1 3h-3 0l-3 1h0l3-3v-1z" class="B"></path><path d="M362 486c2-1 3-1 4 0 0 1 0 2-1 3-2 0-2 0-3 1h0c-1-2 0-2 1-3l-1-1zm-7 0h1 1 1 0c-1 2-2 3-3 5-1 0-2-1-3-2l3-3z" class="G"></path><path d="M350 484c1-1 2-1 3-1l-3 3h0 0c-2 1-3 2-4 4h-1c-1 0-1 0-1-1 1-2 4-4 6-5z" class="Y"></path><path d="M386 432l9 15c-2 1-3 1-3 3 1 1 1 3 1 4 0 2 0 3 1 5 0 1 0 2-1 3l-1-1c0-2-2-4-3-6 0-1-1-1-1-2l-2-3-1 1 1 2h0l-2-2s0-1-1-1l-1 2v1l-2-2v1c-1 1-2 2-3 4 0-2 1-3 1-4s0-2 1-2c1-1 2 0 3 0l1-2v1l1-1c-1 0 0-1 0-1v-1c0-1 1-3 2-4h2 2c-2-3-4-7-4-10h0z" class="E"></path><path d="M385 447v-1c1-1 1-2 2-3 1 0 2 1 2 2 1 1 2 3 3 5 1 1 1 3 1 4 0 2 0 3 1 5 0 1 0 2-1 3l-1-1c0-2-2-4-3-6 0-1-1-1-1-2 0-2-2-4-3-6z" class="X"></path><path d="M385 447v-1c1-1 1-2 2-3 1 0 2 1 2 2 1 1 2 3 3 5 1 1 1 3 1 4-1-2-3-3-4-5 0-1-1-2-2-3v1s1 1 1 2c1 1 2 3 3 4-2-1-4-3-4-4v-1c-1-1-1-1-2-1z" class="p"></path><path d="M370 480c2 0 4 0 6 1h1c0 1 0 1 1 2v2l1 1v1c-1 1-1 1 0 1 0 1-1 2-1 2h-2 0c0-1 0-1-1-1l-1-1-3-2-1 1c0-1 0-1-1-1-1-1-1-1-2 0h-1c-1-1-2-1-4 0-2 2-2 6-4 8-1 0-1 0-2-1 0-1 0-3 1-4s2-2 2-4h-3l-1-3c2 0 4 0 5-1h4v-1h6z" class="W"></path><path d="M374 482v-1c0-1 1 0 2 0h1c0 1 0 1 1 2v2c-1-1-3-1-3-3h-1 0z" class="B"></path><path d="M370 480c2 0 4 0 6 1-1 0-2-1-2 0v1h-1-5s-1-1-2-1v1h-1v-1h-1v-1h6z" class="F"></path><path d="M305 485c0-1 1-1 1-2 1 0 1 0 1-1 1-1 4-3 5-4h2v-1c1 0 1-1 2-2v1c0 2-1 3-1 5h0 1c0-1 1-2 2-3 1 1 0 2 1 3h0c1-1 2-1 3 0l1 1 1-1c0 1 0 2 1 3v1 1c1-1 1-2 2-3v2 1l1 1h-1l-2 3c-1 1-2 2-3 4l-2-1c-2 0-3 0-5-1-1-2 0-2 0-4l3-6h0c-2 0-2 1-3 2l-1-1c0-1 1-2 0-3-2 1-5 4-6 6h0c-1 2-2 3-3 4h0 0c-1-1 0-3 0-5z" class="B"></path><path d="M315 488v1c1 0 1 0 2-1h1v1l1 1v-1c2 0 2-3 2-5 0-1 0-1 1-2h0l1 1v1c-1 2-2 4-1 6v1c1-1 1-1 1-2h1c1-2 1-2 3-2l-2 3c-1 1-2 2-3 4l-2-1c-2 0-3 0-5-1-1-2 0-2 0-4z" class="L"></path><path d="M382 452l1-2c1 0 1 1 1 1l2 2h0c1 1 3 2 3 3 1 2 1 3 2 4l-1 1h0c-1-1-1-2-2-2v-1h0c-1 2 2 7 2 9-1 0-1-1-2-1h-1v-1c-1-2-2-3-3-5 0 2 1 4 1 6-1 0-1-1-2-2v3c1 1 1 2 1 3l-1 2c0-1 0-1-1-2-1 1-1 1-3 2v-2c-1-1-1-1-1-3 0 0 0-1-1-2l-1 2-1-3c-2-3-2-6-2-9v-2h1v3c1 0 2 0 3 1v-1c1-2 2-3 3-4v-1l2 2v-1z" class="K"></path><path d="M377 465c2 0 4 3 5 5-1 1-1 1-3 2v-2c-1-1-1-1-1-3 0 0 0-1-1-2h0z" class="C"></path><path d="M382 452c1 1 1 1 1 2h-1c1 1 1 1 1 2s-1 1-1 2 0 0-1 1v1h-1l-1-2v-3c2 0 2-1 2-1l1-1v-1z" class="Z"></path><path d="M373 455v-2h1v3c0 1 0 3 1 4h0c1 0 1-1 1-1v-1c1 1 1 1 1 2s-1 1-1 3l-1 1c-2-3-2-6-2-9z" class="a"></path><path d="M377 460c1 0 1-1 2-1v2c1 2 2 3 2 5h0c-1 0-1-1-1-2l-1-1h-1s-1 1-1 2h0l-1 2-1-3 1-1c0-2 1-2 1-3z" class="U"></path><path d="M274 441l1-1c1 0 1 1 2 1 0 1 1 3 2 4h2v1c1 0 3-1 3-1 1 1 1 2 1 3l1 1 1 2h1l-1 2s0 1-1 2l1 1 1-2 2 2v4h-1c0 2 0 3 1 4 0 1 0 2-1 4 1 0 1 1 1 2h0-2s-1 1-2 1v-1c-1-2 0-4-1-6h0v1c0 1 0 1-1 1h0c-1-1-2-2-2-4h0-2c0-2-1-3-2-5h0c-1 0-1 0-2-1l1-1c0-1 0-2 1-3h0v3h1v-3h1l-1-1-1 1v-2c-1-1-1 0-2-1v-1l-1 1c-1 0-1-1-1-1-1 0-1 1-2 1v-1h2l1-1v-2l-1-1 1-2-1-1z" class="R"></path><path d="M286 455c-1-1-1-1-1-2l-1-1v-3l1-1 1 1 1 2h1l-1 2s0 1-1 2z" class="C"></path><path d="M287 456l1-2 2 2v4h-1c0 2 0 3 1 4 0 1 0 2-1 4h0l-1 1c-1-3 0-7 0-10h-1v2c-1-2 0-3 0-5h0z" class="h"></path><path d="M274 441l1-1c1 0 1 1 2 1 0 1 1 3 2 4h0c1 3 3 4 3 6 0 0 1 0 1 1l1 1v1h-1v-1c-1-1-1 0-2 0h-1v-1l-1-1-1 1v-2c-1-1-1 0-2-1v-1l-1 1c-1 0-1-1-1-1-1 0-1 1-2 1v-1h2l1-1v-2l-1-1 1-2-1-1z" class="I"></path><path d="M275 442c0 2 1 3 1 5v2-1l-1 1c-1 0-1-1-1-1-1 0-1 1-2 1v-1h2l1-1v-2l-1-1 1-2z" class="e"></path><defs><linearGradient id="V" x1="389.252" y1="480.222" x2="373.349" y2="479.665" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#2b2a2b"></stop></linearGradient></defs><path fill="url(#V)" d="M384 470c0-1 0-2-1-3v-3c1 1 1 2 2 2 0-2-1-4-1-6 1 2 2 3 3 5v1l3 5v2h-1c0 1-1 2-1 4l1 1c1 2 0 6-1 8s-1 4-1 6c1 0 1 0 2 1-1 0-1 1-1 1-1 1-1 2-2 2h0-1l-1 1h0v-3c0-1-1-2-2-3v-1h-1c-1-1-1-1-1-2l-1-1v-1l-1-1v-2c-1-1-1-1-1-2h-1c-2-1-4-1-6-1 1 0 3 0 4-1l-1-1h1v-2c0-1-1-2-1-3 0 0-1 0-1-1l2-1h1l4 3-2-5c0-1 0-1-1-2l1-2c1 1 1 2 1 2 0 2 0 2 1 3v2c2-1 2-1 3-2 1 1 1 1 1 2l1-2z"></path><path d="M385 474l2 1v1c-1 3 1 5-1 7-1-2-1-4-3-6 1-1 2-1 2-3z" class="J"></path><path d="M382 470c1 1 1 1 1 2l1-2c1 2 1 3 1 4 0 2-1 2-2 3v-2l-1 1v1l-1-1v-1l-1-2-1-1c2-1 2-1 3-2z" class="o"></path><path d="M380 473h2 0v1s0 1 1 1l-1 1v1l-1-1v-1l-1-2z" class="C"></path><path d="M377 465c1 1 1 2 1 2 0 2 0 2 1 3v2l1 1 1 2v1l-2-2c0 1-1 2-1 2-1 1-1 0-2 0h-2c0-1-1-2-1-3 0 0-1 0-1-1l2-1h1l4 3-2-5c0-1 0-1-1-2l1-2z" class="c"></path><path d="M377 481l3 1c3 3 5 5 5 9v5l-1 1h0v-3c0-1-1-2-2-3v-1h-1c-1-1-1-1-1-2l-1-1v-1l-1-1v-2c-1-1-1-1-1-2z" class="g"></path><path d="M377 481l3 1v1c0 2 1 3 2 5v1h0l-2-1-1-1v-1l-1-1v-2c-1-1-1-1-1-2z" class="H"></path><path d="M265 438h1v-1h1l1 2h-1c-1 3-1 4 0 6h1c1 1 2 2 4 2v1 1c1 0 1-1 2-1 0 0 0 1 1 1l1-1v1c1 1 1 0 2 1v2l1-1 1 1h-1v3h-1v-3h0c-1 1-1 2-1 3l-1 1c1 1 1 1 2 1h0c-1 1-1 2-2 2l-2-2h-2v2h-2v1h-1v1l-1 1c0-1-1-1-2-1l-2-2v1c-1 0-2 0-3-1v-1l-1-1-1-1h0l-1-1h0l-1 1 1 1h-1c0-1 0-1-1-2h0l-1-1c-1 0-1-1-1-1v-2l2-1v-1c-1 0-1 0-1-1 1-1 1-1 3-1 1 0 1 0 1-1v-1l-2-1h3v1l1-1v-2h2c1-1 1-1 1-2 1-1 1-1 1-2z" class="e"></path><path d="M259 456v-1h1 1c2 1 4 1 6 2-1 0-1 0-2 1h-1l-1-1-2 1-1-1-1-1z" class="o"></path><path d="M261 458l2-1 1 1h1c1-1 1-1 2-1v1l-2 1h-1v1c-1 0-2 0-3-1v-1z" class="N"></path><path d="M254 453v-2l2-1h0 1c0 1 1 2 1 3l-2-1v1h0v2l-1-1c-1 0-1-1-1-1z" class="O"></path><path d="M264 459h1l2-1c1 0 2 1 3 1v1h-1v1l-1 1c0-1-1-1-2-1l-2-2z" class="h"></path><path d="M261 442h2c-1 1-1 1-1 2 1 1 2 1 3 3-1 1-1 1-2 1 0 0 0-1-1-1h-2v1c0 1 1 2 2 2l1 1v-1c1 0 1 0 1 1 1 0 1 1 2 1 0 0 1 0 1 1h-1l1 1v1c-2-1-3-2-3-3v-1h-2c0 1 0 1 1 2h0v1h-1l-1-1h-3c0-1-1-2-1-3h-1 0v-1c-1 0-1 0-1-1 1-1 1-1 3-1 1 0 1 0 1-1v-1l-2-1h3v1l1-1v-2z" class="J"></path><path d="M257 450h1l3 3h-3c0-1-1-2-1-3z" class="C"></path><path d="M265 438h1v-1h1l1 2h-1c-1 3-1 4 0 6h1c1 1 2 2 4 2v1 1c1 0 1-1 2-1 0 0 0 1 1 1l1-1v1c1 1 1 0 2 1v2l1-1 1 1h-1v3h-1v-3h0c-1 1-1 2-1 3l-1 1c1 1 1 1 2 1h0c-1 1-1 2-2 2l-2-2h-2c-1 0-2-1-3-2h0v-1c-1-1-1-1-2-1 0-1-1-1-1-1-1 0-1-1-2-1 0-1 0-1-1-1v1l-1-1c-1 0-2-1-2-2v-1h2c1 0 1 1 1 1 1 0 1 0 2-1-1-2-2-2-3-3 0-1 0-1 1-2s1-1 1-2c1-1 1-1 1-2z" class="D"></path><path d="M268 445c1 1 2 2 4 2v1c-1-1-1 1-2 1v-1h-1l-1 1c-1 0-2 0-2-1v-2h1l1-1z" class="J"></path><path d="M269 455h1l1-1h1c0 1 0 1 1 2h1 0c1 0 1 0 2-1v1c1 1 1 1 2 1h0c-1 1-1 2-2 2l-2-2h-2c-1 0-2-1-3-2h0z" class="N"></path><path d="M271 463h0c0 1 1 1 0 3l1 1-1 1 1 1h0-1l-1 1c1 1 1 1 1 2h4l1 1h0l3 2 1 2h0-2c0 1 3 3 2 5l-2-2c-1-1-1-1-2-1-1 1 0 1 0 1l-2 2h-1c-1 0-2-1-3 0s-1 1-1 2l-1 1v-5l-1-1-2 2-1-1c-2-2-2 0-4 0 0-1-1-1-1-2l-1-1-1-1c0 1-1 1-1 1h-2-1 0-1c-1 1-2 1-2 1-2 1-3 1-4 1v-1l-1-1c-1 0-1 1-2 0v-1c0-1 2-2 4-2v-1h1l-1-1c0-1 0-1 1-1v-1h0v-2c1 1 0 1 1 0v-1l1-1v-1c2 0 4-2 6-2l2 1c1 0 2 0 3 1h1c2 0 3-1 5-3l2 2h0l2-1z" class="m"></path><path d="M248 473l2-1c0 1-1 1-1 2l1 1c2-1 3-1 5 0h-3c-2 0-2 1-3 2 1 0 2 0 2-1h2 2 2c0 1-1 1-1 1h-2-1 0-1c-1 1-2 1-2 1-2 1-3 1-4 1v-1l-1-1c-1 0-1 1-2 0v-1c0-1 2-2 4-2v-1h1z" class="B"></path><path d="M245 477c-1 0-1 1-2 0v-1c0-1 2-2 4-2-1 1-1 1 0 2 1 0 2-1 3 0-1 1-3 1-4 2l-1-1z" class="Y"></path><path d="M271 463h0c0 1 1 1 0 3l1 1-1 1 1 1h0-1l-1 1c1 1 1 1 1 2l-3-1c-4-1-9 0-13 0l-5 1h0l-2 1-1-1c0-1 0-1 1-1v-1h0v-2c1 1 0 1 1 0v-1l1-1v-1c2 0 4-2 6-2l2 1c1 0 2 0 3 1h1c2 0 3-1 5-3l2 2h0l2-1z" class="Z"></path><path d="M253 466h3c1 0 2 0 2 1h-1-3l-1 1-1-1 1-1z" class="P"></path><path d="M271 463h0c0 1 1 1 0 3l-3 1v-1c0-1 0-1-1-1v-1h2 0l2-1z" class="K"></path><path d="M256 468h4c1 0 2-1 3-1s2 1 3 1c-1 2-4 1-6 2h-2l-2-2z" class="L"></path><path d="M250 472v-2c3 0 3-2 6-2l2 2c-1 1-2 0-3 1l-5 1z" class="l"></path><path d="M266 468c1 1 1 1 2 1v2c-4-1-9 0-13 0 1-1 2 0 3-1h2c2-1 5 0 6-2z" class="d"></path><path d="M268 466v1l3-1 1 1-1 1 1 1h0-1l-1 1c1 1 1 1 1 2l-3-1v-2-1h-1c-1 0-1 0-1-1h-2l-1-1h1 2 2z" class="P"></path><path d="M268 466v1l3-1 1 1-1 1c-2 0-3-1-5-2h2z" class="a"></path><path d="M331 470c2 0 4 0 5 1l2 2c1 1 2 1 3 2l1 1v2h1 1c0 1 1 1 1 2v2c-1 1-1 1-2 1s-1 1-2 2c0 1 0 1-1 2h0l-1 1c-1 0-1 1-1 1-1 2-2 4-4 5h-6c-1-1-1-2 0-3-1-1-2-1-3-1l2-3h1l-1-1v-1-2c-1 1-1 2-2 3v-1-1c-1-1-1-2-1-3l-1 1-1-1c-1-1-2-1-3 0h0c-1-1 0-2-1-3-1 1-2 2-2 3h-1 0c0-2 1-3 1-5v-1c-1 1-1 2-2 2v1h-2c-1 1-4 3-5 4 0 1 0 1-1 1 0 1-1 1-1 2s-1 1-1 2-1 1-1 2h-1v-1c0-4 3-7 6-9l2-2c1-1 2-1 2-1l1-1c0-1 0-1 1-2h0l3-1c1-1 3-1 5-1h0l9-1z" class="Q"></path><path d="M320 476h1v1h1v-1h1v1 1c-1 0-2 1-3 0h-1l1-2z" class="M"></path><path d="M328 487l2-2h0c1 1-1 4-2 5v1c-1-1-2-1-3-1l2-3h1z" class="e"></path><path d="M331 470c2 0 4 0 5 1l2 2h0c-1 0-2 1-2 2h-2 0-2l-3-1c-2-2-5-1-7-3l9-1z" class="F"></path><g class="M"><path d="M329 474c1-1 2-1 3-2l1 1v1l1 1h0-2l-3-1z"></path><path d="M341 475l1 1v2h1 1c0 1 1 1 1 2v2c-1 1-1 1-2 1s-1 1-2 2c0 1 0 1-1 2h0l-1 1c-1 0-1 1-1 1-1 2-2 4-4 5h-6c-1-1-1-2 0-3v-1h2c1-1 2-3 3-4v-1c1 0 1 0 2 1 0 1-1 2-1 4 1-1 1-3 1-4h0c1-1 1-1 1-2-1 0-1 0-3-1-1 0-3 0-4-1v-3h4c1 1 1 1 1 2s0 1 1 1h0l1-2v-1c1 1 2 1 3 2h1l1-1v-2c1-2 0-2 0-3z"></path></g><path d="M335 486h0c1-1 1-1 1-2-1 0-1 0-3-1-1 0-3 0-4-1v-3h4c1 1 1 1 1 2l-1-1v1h-1-1v1l2 1h1 2 0c0-1 0-1 1-2h1c0 1 1 1 1 1v1l-1 1h-1c0 1 0 2-1 2h-1z" class="B"></path><path d="M345 480v2c-1 1-1 1-2 1s-1 1-2 2c0 1 0 1-1 2h0l-1 1c-1 0-1 1-1 1-1 2-2 4-4 5h-6c-1-1-1-2 0-3v-1h2c1-1 2-3 3-4h1v2c0 1 0 2 1 3 0-1 0-2 1-2 1-1 2-2 2-3h0 2v-2c2 0 2-1 2-3l1-1h2z" class="P"></path><path d="M335 491h0c-1 1-2 1-3 1h0c0-2 1-3 2-4 0 1 0 2 1 3z" class="L"></path><path d="M352 461c1 1 1 1 2 3v1l1-1v-2h1v1l2 2h0l-1 1c1 1 2 3 3 3 1 1 0 1 1 2 1 0 1 0 2 1h1l2 2 2-1 1 1 3 1 1-2c0 1 1 2 1 3v2h-1l1 1c-1 1-3 1-4 1h-6v1h-4c-1 1-3 1-5 1h-2v1c-1 0-2 0-3 1l-2-2h0c-2 1-3 2-4 3s-1 1-2 1-1 0-2 1c1-1 1-1 1-2 1-1 1-2 2-2s1 0 2-1v-2c0-1-1-1-1-2h-1-1v-2l-1-1c-1-1-2-1-3-2l-2-2c-1-1-3-1-5-1h0 1c1 0 2 0 3-1l-1-1 1-1h1c2 0 3-2 3-2l3-3c1-1 1-1 2 0v1 1h0c2 0 2 0 4-2l1 1c1 0 1-1 2-1l1-1z" class="I"></path><path d="M356 462v1l2 2h0l-1 1c0-1 0-1-1-1s-2 0-3 1h-2v-1l3-1v1l1-1v-2h1z" class="J"></path><path d="M335 469c2 1 4 1 6 1h0l1-1c1 0 2-1 3 0h0 2l-1 1h-2v1h2l-1 1h0l-2 1s-2 0-3-1c-1 0-3 0-4-1s-3-1-5-1h0 1c1 0 2 0 3-1z" class="h"></path><path d="M356 475c1 0 1 0 2-1v-1-1l1 1c3 0 4 2 6 4 0 1 1 1 1 2h2v-2-1l-2-2 2-1 1 1 3 1 1-2c0 1 1 2 1 3v2h-1l1 1c-1 1-3 1-4 1h-6v1h-4-2c0-1 0-2 1-3l-3-3z" class="C"></path><path d="M368 473l1 1h0c0 1 0 2 1 3l-1 1-1-2-2-2 2-1z" class="N"></path><path d="M356 475c1 0 1 0 2-1v-1-1l1 1 2 3c1 1 1 2 1 4h2v1h-4-2c0-1 0-2 1-3l-3-3z" class="E"></path><path d="M347 469c1 0 2 1 3 1 3 2 5 3 6 5l3 3c-1 1-1 2-1 3h2c-1 1-3 1-5 1h-2v1c-1 0-2 0-3 1l-2-2h0c-2 1-3 2-4 3s-1 1-2 1-1 0-2 1c1-1 1-1 1-2 1-1 1-2 2-2s1 0 2-1v-2c0-1-1-1-1-2h-1-1v-2l-1-1c-1-1-2-1-3-2l-2-2c1 1 3 1 4 1 1 1 3 1 3 1l2-1h0l1-1h-2v-1h2l1-1z" class="j"></path><path d="M350 478v-1h1c0 1 0 1 1 2l1 1 2 1-1 1h-1v1c-1 0-2 0-3 1l-2-2h1v-1c1 0 1 0 2 1l1-1h0c0-1-1-2-2-3z" class="c"></path><path d="M336 471c1 1 3 1 4 1 1 1 3 1 3 1l2-1c0 1 1 1 1 2-1 1-1 1-1 3 0 1 0 2 1 3l1 1h1v1c-2 1-3 2-4 3s-1 1-2 1-1 0-2 1c1-1 1-1 1-2 1-1 1-2 2-2s1 0 2-1v-2c0-1-1-1-1-2h-1-1v-2l-1-1c-1-1-2-1-3-2l-2-2z" class="k"></path><path d="M347 469c1 0 2 1 3 1 3 2 5 3 6 5l3 3c-1 1-1 2-1 3h2c-1 1-3 1-5 1h-2 1l1-1-2-1-1-1c-1-1-1-1-1-2h-1v1c-1 0-1-2-1-3l-3-1c0-1-1-1-1-2h0l1-1h-2v-1h2l1-1z" class="X"></path><path d="M349 475c1 0 2 0 3 1 1-1 1-1 2-1h1c0 2 1 2 2 3 0 1 0 2-1 2l-1-1-2 1-1-1c-1-1-1-1-1-2h-1v1c-1 0-1-2-1-3z" class="n"></path><path d="M346 470h1v1h1c1 0 3 1 4 2l-1 1h3 1v1h-1c-1 0-1 0-2 1-1-1-2-1-3-1l-3-1c0-1-1-1-1-2h0l1-1h-2v-1h2z" class="T"></path><path d="M372 445v1l2-3 1 1h1 1v1h0 0c1 2 2 2 1 4h0l1 1c-1 0-1 1-1 2s-1 2-1 4v1c-1-1-2-1-3-1v-3h-1v2c0 3 0 6 2 9l1 3c1 1 1 1 1 2l2 5-4-3h-1l-2 1c0 1 1 1 1 1l-1 2-3-1-1-1-2 1-2-2h-1c-1-1-1-1-2-1-1-1 0-1-1-2-1 0-2-2-3-3l1-1h0l-2-2v-1h-1v2l-1 1v-1c-1-2-1-2-2-3v-1c0-3 2-3 2-5 0-1 1-1 1-1h0c1-1 1-1 1-2h1l1-1v-2h1c1 2 1 2 2 3 0-1 0-1 1-2l-1-1h2c0 1 0 2 1 3 1-2 1-3 1-4l1 1v1h1v-2c1 0 1 0 1 1 1 0 1-2 2-3v2h1v-2-1h1z" class="C"></path><path d="M362 463h-1c-1-1-1-1-1-2s1-1 2-1h1l-1 1v2z" class="e"></path><path d="M358 459c0 1 1 2 1 2 0 1 1 2 1 3h0c-1 1-1 1-1 2v-1h-1 0l-2-2v-1l2-3z" class="b"></path><path d="M358 449h1c1 2 1 2 2 3 0-1 0-1 1-2v5 2h-1l-2-3v-2h-2l1-1v-2z" class="E"></path><path d="M362 461c1 1 2 1 2 2 2 1 2 1 3 2h0l-1 2c0 1-1 2-3 3l-1-1v-1h-1v-2c0-2 1-2 1-3v-2z" class="N"></path><path d="M357 452h2v2l-1 1c1 1 2 2 2 3h0c-1 0-1 0-1-1-1 1-1 1-1 2h0l-2 3h-1v2l-1 1v-1c-1-2-1-2-2-3v-1c0-3 2-3 2-5 0-1 1-1 1-1h0c1-1 1-1 1-2h1z" class="O"></path><path d="M357 452h2v2l-1 1v1c-1 1-2 1-3 1v-1-2c1-1 1-1 1-2h1z" class="b"></path><path d="M362 469l1 1c2-1 3-2 3-3l1-2 1 2 1-1c1 1 2 3 3 3l1-1c1 0 1 1 2 2v1h-1l-2 1c0 1 1 1 1 1l-1 2-3-1-1-1-2 1-2-2c-1-1-1-1-2-3z" class="O"></path><path d="M362 469l1 1c2-1 3-2 3-3l1-2 1 2h1l1 3h0c-1 0-1 0-1-1-2 0-2 0-3 1 0 1 1 2 2 3l-2 1-2-2c-1-1-1-1-2-3z" class="c"></path><path d="M372 445v1l2-3 1 1h1 1v1h0 0c1 2 2 2 1 4h0l1 1c-1 0-1 1-1 2s-1 2-1 4v1c-1-1-2-1-3-1v-3h-1v2c0 3 0 6 2 9l1 3c1 1 1 1 1 2l2 5-4-3v-1c-1-1-1-2-2-2l-1 1c-1 0-2-2-3-3l-4-8-1-3h0l-1-1v2c-1-1 0-3 0-3h0 1v-1c1-2 1-3 1-4l1 1v1h1v-2c1 0 1 0 1 1 1 0 1-2 2-3v2h1v-2-1h1z" class="I"></path><path d="M376 444h1v1h0 0c1 2 2 2 1 4h0l1 1c-1 0-1 1-1 2v-2l-2-4c0-1 0-2-1-2h1z" class="f"></path><path d="M370 463c-1-2-2-2-3-4-1 0-1-1-1-1 0-2 0-3 1-4 1 0 1 0 2 1l1-1 1 1h2c0 3 0 6 2 9l1 3c1 1 1 1 1 2l-2-3h-1c-2-1-3-2-4-3z" class="E"></path><path d="M371 455h2c0 3 0 6 2 9l1 3c1 1 1 1 1 2l-2-3h-1c-2-1-3-2-4-3 1 0 1 0 2 1h0 1l-1-2v-1c0-1-1-4-1-4v-2h0z" class="C"></path><path d="M325 437c1 0 1 1 2 2h0l-1 1v2l2-2h0v1 2l1-3c1 2 0 3 1 4l1-3 1 1c1 2 1 3 0 5l-1 1h1v1l1 1v5h0 1l1 3v1h1c0-1 0-2 1-3v-1h2v3 4 1 2s-1 2-3 2h-1l-1 1 1 1c-1 1-2 1-3 1h-1 0l-9 1h0c-2 0-4 0-5 1v-1c-1 0-1-1-2-1 1-1 1-1 1-2h0-1v-2-3c0-1 1-1 1-2l-1-2c-1-1-1-1-1-2l1-1h0c1-2 1-4 0-5h-1v-1 1c1-1 2-2 2-3v-1c1-1 1-2 1-3h1 2v-1c1-1 1-3 2-4h1l2-2z" class="Z"></path><path d="M321 453c0 3 0 4-1 7-1-1-1-2-1-3v-1c1-1 1-2 2-3z" class="L"></path><path d="M326 442l2-2h0v1c-1 3-1 6-2 9 0 0-1 0-1-1-1-2 0-4 1-6v-1z" class="B"></path><path d="M330 453v4c1 0 1 1 1 2h0l-1 1-1 2v2h0c-1 1-2 1-2 1v-3c1-1 1-2 1-3v-1c1-1 1-1 1-2h1c-1-1-1-2 0-3z" class="U"></path><path d="M332 449l1 1v5h0c-1 1 0 2 0 3l-1 1h-1c0-1 0-2-1-2v-4l-1-1h0l1-1h1 0l1-2z" class="B"></path><path d="M329 440c1 2 0 3 1 4l1-3 1 1c1 2 1 3 0 5l-1 1c0 1 0 1-1 1v-1c-1 1-1 2-1 2l-1 1c0-3-1-6 0-8l1-3z" class="G"></path><path d="M314 450v1c1-1 2-2 2-3 0 1 0 2 1 3v1l1 1v3h1v1c0 1 0 2 1 3h-1l1 1 1 2h1c1 1 1 2 1 3s-1 2-1 2h-1v2h0c1 0 1 1 1 1-2 0-4 0-5 1v-1c-1 0-1-1-2-1 1-1 1-1 1-2h0-1v-2-3c0-1 1-1 1-2l-1-2c-1-1-1-1-1-2l1-1h0c1-2 1-4 0-5h-1v-1z" class="K"></path><path d="M317 452l1 1v3l-1-1c-1-1 0-1 0-3z" class="c"></path><path d="M319 461c-1 0-1 0-2-1v-2l-1-1h1 2c0 1 0 2 1 3h-1v1z" class="P"></path><path d="M319 460l1 1 1 2h1c1 1 1 2 1 3s-1 2-1 2h-1v2h0c1 0 1 1 1 1-2 0-4 0-5 1v-1c-1 0-1-1-2-1 1-1 1-1 1-2v-3h1v2h1v-3c0-1 1-2 1-3v-1z" class="N"></path><path d="M319 460l1 1 1 2-1 1v4c0 1 0 2-1 3h0c-1-1-1-1-1-2 1-1 1-3 0-5 0-1 1-2 1-3v-1z" class="o"></path><path d="M325 437c1 0 1 1 2 2h0l-1 1v2 1c-1 2-2 4-1 6 0 1 1 1 1 1 0 1-1 3-1 3-2 1-2 2-3 2v-4c0 1-1 2-1 2-1 1-1 2-2 3h-1v-3l-1-1v-1c-1-1-1-2-1-3v-1c1-1 1-2 1-3h1 2v-1c1-1 1-3 2-4h1l2-2z" class="Y"></path><path d="M322 439h1c0 1-1 2-1 3s-1 1-1 1v1c1 1 2 2 2 4-1 0-1 0-2 1 0-1-1-4-1-5v-1c1-1 1-3 2-4z" class="H"></path><path d="M317 444h1 2c0 1 1 4 1 5-1-1-1-1-2-1h-1c0 1 0 2-1 3-1-1-1-2-1-3v-1c1-1 1-2 1-3z" class="M"></path><path d="M317 451c1-1 1-2 1-3h1c0 1 0 2 2 2h1v1c0 1-1 2-1 2-1 1-1 2-2 3h-1v-3l-1-1v-1z" class="p"></path><path d="M325 437c1 0 1 1 2 2h0l-1 1v2 1h-1s-1 1-2 1l-1-1-1 1v-1s1 0 1-1 1-2 1-3l2-2z" class="U"></path><path d="M337 455h2v3 4 1 2s-1 2-3 2h-1l-1 1 1 1c-1 1-2 1-3 1h-1 0l-9 1h0s0-1-1-1h0v-2h1l1 1c0-1 2-2 2-3v-1h1 1s1 0 2-1h0v-2l1-2 1-1h0 1l1-1c0-1-1-2 0-3h1l1 3v1h1c0-1 0-2 1-3v-1z" class="O"></path><path d="M333 455h1l1 3h-1l-1 1c0 1 0 3-1 4-1-1-1-3-1-4h0 1l1-1c0-1-1-2 0-3z" class="Z"></path><path d="M329 464c0-1 0-1 1-1 1 1 1 1 1 2l-1 1h1 1v3 1h-1-3l-1-1 1-1c0-1 1-2 1-3v-1h0z" class="b"></path><path d="M327 465s1 0 2-1v1c0 1-1 2-1 3l-1 1 1 1h3 0l-9 1h0s0-1-1-1h0v-2h1l1 1c0-1 2-2 2-3v-1h1 1z" class="O"></path><path d="M337 455h2v3 4 1 2s-1 2-3 2h-1l-1 1 1 1c-1 1-2 1-3 1v-1h1v-1-1-3h1v-2-3h-1l1-1h1v1h1c0-1 0-2 1-3v-1z" class="E"></path><path d="M333 468c1-1 1-2 2-3 0-1 0-2 1-3l1 1-1 2v1l1-1c0-1 1-2 2-3v1 2s-1 2-3 2h-1l-1 1 1 1c-1 1-2 1-3 1v-1h1v-1z" class="f"></path><path d="M350 399l2 1c1 1 1 1 2 1l9 7 7 5 5 5c0 1 1 1 1 1l4 5 6 8h0c0 3 2 7 4 10h-2-2c-1 1-2 3-2 4v1s-1 1 0 1l-1 1v-1l-1 2c-1 0-2-1-3 0l-1-1h0c1-2 0-2-1-4h0 0v-1h-1-1l-1-1-2 3v-1h-1v1 2h-1v-2c-1 1-1 3-2 3 0-1 0-1-1-1v2h-1v-1l-1-1c0 1 0 2-1 4-1-1-1-2-1-3h-2l1 1c-1 1-1 1-1 2-1-1-1-1-2-3h-1v2l-1 1h-1c0 1 0 1-1 2h0s-1 0-1 1c0 2-2 2-2 5v1l-1 1c-1 0-1 1-2 1l-1-1c-2 2-2 2-4 2h0v-1-1c-1-1-1-1-2 0l-3 3v-2-1-4-3h-2v1c-1 1-1 2-1 3h-1v-1l-1-3h-1 0v-5l-1-1v-1h-1l1-1c1-2 1-3 0-5h2c1 0 1-1 2-1 0-1 1-2 1-2 0-1-1-2-2-3l1-1h0c0-2 1-3 1-4l1 1-1 1h1l1-2c1-1 1-2 1-3v-1l2-1h2c0-1 0-2 1-3l-1-2c1-1 1-2 1-3l1 1h0c-1-1 0-4 0-5v-1l1 1h0v-1-2-2c1-1 1-2 1-3v-1c0-1 1-2 1-2 0-2 0-3 1-4z" class="F"></path><path d="M351 436c1 0 1-1 2-2 1 2 0 8 0 10l-1 2h0v-1c-1-1 0-7-1-9z" class="G"></path><path d="M350 431h1c0 2-1 5 0 5 1 2 0 8 1 9v1c-1 1-1 1-1 2h0c-1 0-1 0-2 1v-5c-1-2-1-4-1-6l-1 1 1-3c0-2 1-4 2-5z" class="H"></path><path d="M354 414h1v1h1l1 1c0 1 0 3-1 4v1c-1 1-1 2-1 3l1 1v-1 8h0-1v-1c-1-1 1-3-1-4-1 0-1 1-1 2l-1 1-1-1v-1l2-9 1-4v-1z" class="p"></path><path d="M353 419l1-2 1-1c0 2-1 4-1 6-1 1 0 3-1 4v3l-1 1-1-1v-1l2-9z" class="I"></path><path d="M359 413c1-1 2-2 3-2l-1 6v9l-2 1v1s0 1-1 2l-1-7h0l-1 1v1l-1-1c0-1 0-2 1-3v-1c1-1 1-3 1-4h0c1-1 1-2 2-3z"></path><path d="M357 423h1c0-1 1-2 1-4v-1c0-1 0-2 1-2 0 1-1 2-1 3s0 0 1 0l1-2v9l-2 1v1s0 1-1 2l-1-7z" class="X"></path><path d="M350 399l2 1c1 1 1 1 2 1l9 7v1l-1 2c-1 0-2 1-3 2s-1 2-2 3h0l-1-1h-1v-1h-1v1l-1 4-2 9v1c-1 1-1 1-1 2-1 1-2 3-2 5l-1-1c0-1 0-5 1-6l-1-2c1-1 1-4 1-5h0c-1 0-3-1-4-1 1-1 1-2 1-3l1 1h0c-1-1 0-4 0-5v-1l1 1h0v-1-2-2c1-1 1-2 1-3v-1c0-1 1-2 1-2 0-2 0-3 1-4z" class="F"></path><path d="M354 415c-1 0-1 1-2 1v-4l1-1 1 1v2 1z" class="B"></path><path d="M348 429c0-3 2-6 3-9h1c-1 1-1 4-1 5-1 1-1 1-1 2l1 1v1c-1 1-1 1-1 2-1 1-2 3-2 5l-1-1c0-1 0-5 1-6z" class="J"></path><path d="M347 414h1v-4h0 1v4c1 2 1 2 1 4h0c-1 1-2 2-2 4h0 0c-1 0-3-1-4-1 1-1 1-2 1-3l1 1h0c-1-1 0-4 0-5v-1l1 1z" class="B"></path><path d="M354 401l9 7v1l-1 2c-1 0-2 1-3 2s-1 2-2 3h0l-1-1h-1v-1h-1v-2-1-10z" class="b"></path><path d="M355 404c1 0 2 0 2 1 1 1 1 1 1 2v2c-1-2-2-2-3-3v-2z" class="F"></path><path d="M358 407h1v6c-1 1-1 2-2 3h0l-1-1c1-2 1-3 2-5v-1-2z" class="I"></path><path d="M354 411c1-2 0-5 1-7v2c1 1 2 1 3 3v1c-1 2-1 3-2 5h-1v-1h-1v-2-1z" class="L"></path><path d="M355 406c1 1 2 1 3 3v1h-3v-4z" class="H"></path><path d="M363 408l7 5v2c1 1 0 2 1 3v6 2c-1 1-1 1-1 2 0 2-1 4-1 7v2l-2 2v-1l-3 5h0c1 2 0 4 1 5 0 1 0 2-1 4-1-1-1-2-1-3h-2l1 1c-1 1-1 1-1 2-1-1-1-1-2-3h-1v2l-1 1h-1 0c-1 0-1-1-1-1-1-2-1-2 0-3l-1-2h-2l1-2h3v-2c1-1 1-2 1-3 1-1 2-2 2-3 0-2 0-5-1-6 1-1 1-2 1-2v-1l2-1v-9l1-6 1-2v-1z" class="N"></path><path d="M364 443c1 2 0 4 1 5 0 1 0 2-1 4-1-1-1-2-1-3-1-2 0-4 1-6z" class="k"></path><path d="M364 415h0c0 3 0 5 1 7 0 1 1 2 1 3s0 3-1 4l-1 8-1 1h0c1-2 1-5 1-7 0-5-2-11 0-16z" class="j"></path><path d="M363 409c1 2-1 5-1 7-1 7 2 14 0 22h-1v-1-1c1-2 0-7 0-10v-9l1-6 1-2z" class="L"></path><path d="M361 426c0 3 1 8 0 10v1 1h1c0 1-1 3-1 4 0 3 1 5 0 7l1 1c-1 1-1 1-1 2-1-1-1-1-2-3h-1v2l-1 1h-1 0c-1 0-1-1-1-1-1-2-1-2 0-3l-1-2h-2l1-2h3v-2c1-1 1-2 1-3 1-1 2-2 2-3 0-2 0-5-1-6 1-1 1-2 1-2v-1l2-1z" class="c"></path><path d="M358 444l1-1h0c0 2 0 4-1 6v2c-1-2-1-3-1-5l1-2z" class="l"></path><path d="M359 449c0-1 1-2 1-3v-4c0-2 0-4 1-5v1h1c0 1-1 3-1 4 0 3 1 5 0 7l1 1c-1 1-1 1-1 2-1-1-1-1-2-3z" class="g"></path><path d="M356 442h1v2h1l-1 2c0 2 0 3 1 5l-1 1h-1 0c-1 0-1-1-1-1-1-2-1-2 0-3l-1-2h-2l1-2h3v-2z" class="K"></path><path d="M356 442h1v2h1l-1 2-1 2h0-1c0-2 0-3 1-4v-2z" class="D"></path><path d="M361 426c0 3 1 8 0 10v-4h0c-2 3-1 6-4 9v1h-1c1-1 1-2 1-3 1-1 2-2 2-3 0-2 0-5-1-6 1-1 1-2 1-2v-1l2-1z" class="k"></path><path d="M370 413l5 5c0 1 1 1 1 1l4 5 6 8h0c0 3 2 7 4 10h-2-2c-1 1-2 3-2 4v1s-1 1 0 1l-1 1v-1l-1 2c-1 0-2-1-3 0l-1-1h0c1-2 0-2-1-4h0 0v-1h-1-1l-1-1-2 3v-1h-1v1 2h-1v-2c-1 1-1 3-2 3 0-1 0-1-1-1v2h-1v-1l-1-1c-1-1 0-3-1-5h0l3-5v1l2-2v-2c0-3 1-5 1-7 0-1 0-1 1-2v-2-6c-1-1 0-2-1-3v-2z" class="I"></path><path d="M378 434v-3l1 1v2c1 0 1-1 1-2l1 1v3-1h0 1c0 1 0 2-1 2v1c0 2-1 4-1 5l-1 1v-1h-1l-1 2h0 0v-1h-1v-2c1-1 1-2 1-4h1v4h0c1-2 1-3 2-5h0v-2c-1 0-1 1-1 1v1h-1v-3z" class="D"></path><path d="M378 434v3h1v-1s0-1 1-1v2h0c-1 2-1 3-2 5h0v-4h-1c0 2 0 3-1 4v2h-1l-1-1-2 3v-1h-1v1 2h-1v-2c-1 1-1 3-2 3 0-1 0-1-1-1v2h-1v-1l-1-1c-1-1 0-3-1-5h0l3-5v1l2-2v-2h2v2c0 1 1 1 1 1h0c1-2 0-3 1-4l1 1v2h1c0-1 0-2 1-3v1 2h1v-1l1-2z" class="S"></path><path d="M371 446h-1c-1-1 0-4 0-5h1 0v4 1z" class="k"></path><path d="M371 435v2c0 1 1 1 1 1-1 1-1 1-2 1 0 0-1 1-1 2h0c-1 0-1-1-2-2l2-2v-2h2z" class="J"></path><path d="M371 435v2h-1-1v-2h2z" class="e"></path><path d="M372 445v-1c0-2 0-2 1-2 0-2 1-3 1-4h1c0 2 0 3 1 4h0v2h-1l-1-1-2 3v-1z" class="a"></path><path d="M370 413l5 5c0 1 1 1 1 1l4 5c0 1-1 2-1 3-1 1-1 2-2 3 0 0 0 1-1 1 1 1 1 2 1 3v2 1h-1v-2-1c-1 1-1 2-1 3h-1v-2l-1-1c-1 1 0 2-1 4h0s-1 0-1-1v-2h-2c0-3 1-5 1-7 0-1 0-1 1-2v-2-6c-1-1 0-2-1-3v-2z" class="O"></path><path d="M374 426c0 1 1 1 1 1v1c-1 1 0 3-1 4-1-1-1-1-1-2s0-2 1-4z" class="R"></path><path d="M371 435v-4c0-1 1-1 1-1v8h0s-1 0-1-1v-2z" class="b"></path><path d="M370 413l5 5c0 1 1 1 1 1v2c0 2 0 4-1 6 0 0-1 0-1-1h0 0c-1-1-1-3-1-4 0-2 0-4-1-5h0v7c0 1 0 1-1 1v-1-6c-1-1 0-2-1-3v-2z" class="i"></path><path d="M375 418c0 1 1 1 1 1v2c0 2 0 4-1 6 0 0-1 0-1-1h0c0-2 0-6 1-8z" class="a"></path><path d="M375 427c1-2 1-4 1-6v-2l4 5c0 1-1 2-1 3-1 1-1 2-2 3 0 0 0 1-1 1 0 1 0 2-1 3v-6-1z" class="i"></path><path d="M344 421c1 0 3 1 4 1h0c0 1 0 4-1 5l1 2c-1 1-1 5-1 6l1 1-1 3 1-1c0 2 0 4 1 6v5c1-1 1-1 2-1h0c0-1 0-1 1-2h0 2l1 2c-1 1-1 1 0 3 0 0 0 1 1 1h0c0 1 0 1-1 2h0s-1 0-1 1c0 2-2 2-2 5v1l-1 1c-1 0-1 1-2 1l-1-1c-2 2-2 2-4 2h0v-1-1c-1-1-1-1-2 0l-3 3v-2-1-4-3h-2v1c-1 1-1 2-1 3h-1v-1l-1-3h-1 0v-5l-1-1v-1h-1l1-1c1-2 1-3 0-5h2c1 0 1-1 2-1 0-1 1-2 1-2 0-1-1-2-2-3l1-1h0c0-2 1-3 1-4l1 1-1 1h1l1-2c1-1 1-2 1-3v-1l2-1h2c0-1 0-2 1-3l-1-2z" class="j"></path><path d="M345 437s0-1 1-1h0c0 1 0 2 1 3h0l1-1c0 2 0 4 1 6v5 1l-1 1c-1 0-1 1-1 1-1-1-1-3-1-4-1-4-1-7-1-11z" class="G"></path><path d="M337 431l1 1-1 1h1l1-2c1 0 1 1 2 1v1h2v3h1v-1-1l1-1v2l1-1 1 1v1h-1 0c-1 0-1 1-1 1h-2v2h-1v-2h-1v2h-1v-2h-1v2l-1 1h0v-2l-1 1c0-1-1-2-2-3l1-1h0c0-2 1-3 1-4z" class="T"></path><path d="M336 436l1-2c1 1 2 1 2 1l1 1c-1 0-1 0-1 1l-1 1-2-2z" class="O"></path><path d="M337 431l1 1-1 1h1l1-2c1 0 1 1 2 1v1h0c0 1-1 1-2 1v1s-1 0-2-1l-1 2v-1h0c0-2 1-3 1-4z" class="D"></path><path d="M344 421c1 0 3 1 4 1h0c0 1 0 4-1 5l1 2c-1 1-1 5-1 6l1 1-1 3h0c-1-1-1-2-1-3h1v-1l-1-1-1 1v-2l-1 1v1 1h-1v-3h-2v-1c-1 0-1-1-2-1 1-1 1-2 1-3v-1l2-1h2c0-1 0-2 1-3l-1-2z" class="E"></path><path d="M344 421c1 0 3 1 4 1l-2 4c-1-1-1-2-1-3l-1-2z" class="H"></path><path d="M346 434c0-1-1-1-1-2v-2l1-1 1-2 1 2c-1 1-1 5-1 6l1 1-1 3h0c-1-1-1-2-1-3h1v-1l-1-1z" class="X"></path><path d="M345 423c0 1 0 2 1 3l-1 1c0 1 0 1-1 2 0 1 0 3-1 4v2h1v1h-1v-3h-2v-1c-1 0-1-1-2-1 1-1 1-2 1-3v-1l2-1h2c0-1 0-2 1-3z" class="R"></path><path d="M342 426h2c-1 2-2 3-2 4l-1 1-1-3v-1l2-1z" class="B"></path><path d="M340 447h1c0-2-1-6 1-7l1-1v1c1 3 1 5 2 8 0 2 0 5-1 7 0 2-1 4-2 7l-3 3v-2-1-4l2-2v-1-3-2c-1-1-1-2-1-3z" class="G"></path><path d="M341 456v5l1 1-3 3v-2-1-4l2-2z" class="N"></path><path d="M352 446h2l1 2c-1 1-1 1 0 3 0 0 0 1 1 1h0c0 1 0 1-1 2h0s-1 0-1 1c0 2-2 2-2 5v1l-1 1c-1 0-1 1-2 1l-1-1c-2 2-2 2-4 2h0v-1-1c-1-1-1-1-2 0 1-3 2-5 2-7h1l1 2c1-1 1-3 2-3l1 1v-1c1-2 2-4 2-6h0c0-1 0-1 1-2h0z" class="O"></path><path d="M344 463c1-1 2-4 4-4h1c0 2-1 2-1 3-2 2-2 2-4 2h0v-1z" class="R"></path><path d="M351 448l1-1h1c1 2 0 3 0 5-2 1-3 2-4 4h0v-1-1c1-2 2-4 2-6h0z" class="C"></path><path d="M352 446h2l1 2c-1 1-1 1 0 3 0 0 0 1 1 1h0c0 1 0 1-1 2h0s-1 0-1 1c0 2-2 2-2 5v1l-1 1-1-1c0-1 1-2 1-3 2-2 2-4 2-6s1-3 0-5h-1l-1 1c0-1 0-1 1-2h0z" class="R"></path><path d="M337 441l2-1s0 1 1 1v6c0 1 0 2 1 3v2 3 1l-2 2v-3h-2v1c-1 1-1 2-1 3h-1v-1l-1-3h-1 0v-5l-1-1v-1h-1l1-1c1-2 1-3 0-5h2c1 0 1-1 2-1h1z" class="F"></path><path d="M333 455s1 0 2-1c0 0 0-1 1-1v2l1 1c-1 1-1 2-1 3h-1v-1l-1-3h-1 0z" class="d"></path><path d="M336 441h1v5c1 3 2 6 2 9h-2v-2c0-1 0 0-1-1 0-1 0-2-1-2h0c-1-3-1-5 0-8h-1c1 0 1-1 2-1z" class="L"></path><path d="M335 442l1-1v4 3c1 2 1 3 1 5 0-1 0 0-1-1 0-1 0-2-1-2h0c-1-3-1-5 0-8z" class="G"></path><path d="M337 441l2-1s0 1 1 1v6c0 1 0 2 1 3v2 3 1l-2 2v-3c0-3-1-6-2-9v-5z" class="W"></path><path d="M386 253c2-13 3-26 3-39-1-6-1-12-2-17-3-16-10-31-19-44-14-20-37-34-61-38-8-2-17-2-25-2-9 0-18 1-27 5-5 2-10 5-14 8-9 6-10 15-12 25-2 11-2 22-3 32v44 116c17 0 33 0 50 2 3 0 7 0 11 1l-1 1c1 0 1-1 2-1 14 1 28-1 42-3-7 6-15 14-19 23h1c4-7 9-13 15-18 1-2 3-4 5-5 3-2 8-2 11-3-10 9-17 22-22 34-1 3-2 5-3 8-22-7-47-10-71-9l-22 1v79c0 27 0 52 3 78 1 8 1 15 3 22 4 15 9 27 20 37 14 14 34 18 53 18 11 1 22 0 33-3 16-4 31-13 43-25 21-20 29-52 29-80 0-31-8-62-30-84-10-10-21-18-34-24 0-9 0-19 2-28 1 1 3 1 4 1v3 7l1-10c7 2 15 3 23 5 13 4 26 10 38 16 9 4 18 9 26 15 9 7 17 17 23 26 4 7 7 13 10 20 3 8 5 16 7 24 2 9 2 18 2 26 1 9 0 18-2 27-1 6-3 12-6 17-12 27-34 47-58 63-15 10-33 18-51 23-23 6-47 7-71 4-16-1-32-5-48-8-10-2-20-4-30-5-12-1-25-1-38-1-10 1-21 2-32 4l-25 6-24 5c-7 2-15 3-23 2-8-2-14-7-18-14-2-2-4-5-3-8 0-1 1-2 1-2 2-1 4-2 5-1 2 0 4 2 5 3l6 3c15 6 36 3 51-4 17-9 27-24 33-42 4-11 6-24 7-35 2-24 1-48 1-72l1-113V238l-1-42c0-10 0-20-1-30-1-13-3-28-9-40s-18-25-32-29c-8-3-17-3-25-2s-15 2-21 5c-2 1-4 2-5 3-3 2-5 5-9 4-1 0-1-1-2-2-1-2 0-4 1-5 3-8 9-12 16-15 9-3 18-3 27-2s18 2 27 4l25 4c17 2 33 4 50 4 14 0 28 0 42-2l32-5c20-2 41-2 62 0 14 2 29 5 42 10 8 2 16 6 23 10 28 14 54 36 64 66 5 14 6 27 6 42 0 14-1 28-6 41-2 5-4 9-6 14-7 12-15 23-24 34-7 8-14 15-22 21-3 3-6 5-9 7-2 1-4 3-6 4-1 1-3 2-4 3-2 1-4 3-6 4-9 5-19 9-28 12l-11 3c2-5 3-13 6-18 1 0 2 0 3 1l1-1c0 1 0 1 1 1h0c1 0 3 1 5 1s5-2 7-3c2 0 5-4 6-6v-1c-3 0-7 3-9 4-2 0-4 1-6 1h-4-1c-2 0-4 0-6-1 0-1 0-1 1-2l2 1c0 1 2 0 3 1h1c4-1 8-1 12-3 3-1 6-2 8-4 1-2 1-4 2-5-2-2-5-2-7-4v-3c2-1 4 1 7 1s5-1 7-2c0-2 0-5-1-7-2-4-5-7-6-11-2-5 0-10 3-15l-1-1c-1 0-1-1-1-1 1-2 1-4 1-6h-1v6l-1-6h0-1v5c-1-1-3-2-3-4v-1h-2c-1 1-3 2-4 3 0-2 0-3 1-4l-1-1h-1v2l-1 3h-2v1h-1l-1 1c1-1 1-1 1-2s0-1-1-2h0v-2l-1 1 1-2c0-1 1-2 1-2v-1-3l-1-2c-1-2-2-3-3-5-2-4-6-8-8-13l-2-3c-2-1-2-2-4-4v-1c-1-1-2-2-3-2 0-1 0-1-1-1h-1v1l-1-1c-1 0-1 0-2-1l-3 1-1-2-2-1h-1-1l-2-2-6-5h0 1v-2c1 0 2 0 4 1h0c1-1 1-1 2-1h0l1-1h1l4 2 2-1h1c1 0 3 0 4 1l4 2 1-1c1 1 2 1 3 1 0-1-1-1-1-2h0l6 3 2 1c0 1 2 2 2 2 2 0 4 2 6 4l4 4c1 2 5 6 5 8v2 1h0c1 1 1 0 1 1h1l1-1v-1l1 1c1 1 1 2 2 4 0 1 0 2 1 3 0-2 1-4 0-5z"></path><path d="M391 318v1c0 2-3 5-5 6-1 0 0-1 0-1-1-2-1-2-3-3h-2v1h0l-1-1c-1 0-2-1-2-2 1-1 3 1 5 1s6-1 8-2z" class="Q"></path><path d="M392 285h1l-1 3c0 3-1 8-4 9-1 1-1 1-2 1h-1c-1-3 1-7 2-9l2-2v1c-1 1-2 5-2 7l3-8v5c2-2 1-5 1-7h1z" class="m"></path><path d="M394 275v1c5-1 9-2 13-3 11-2 23 0 32 6l2 1c-9-3-17-7-27-6-4 0-7 0-11 1-2 1-5 1-8 2h0 0c-1 0-1 0-2-1l1-1z" class="d"></path><path d="M392 318h2c0 1-1 3-1 4v1c4 2 10 6 13 10h0c-3-1-5-4-7-5-3-2-5-3-8-5 2 4 6 7 8 10h0c-1-1-6-7-8-7l-1 1 1 2 4 7c0 1 1 2 1 3h0l-7-11h0c-1 2-3 3-5 4 0-1-1-1-1-1l1-1c2-4 8-7 8-12h0z" class="Q"></path><path d="M392 318c0-3 0-5-1-7-2-4-4-7-6-11 3-1 5-2 7-5v-1h1c0 5-1 9 0 13 0 3 0 6 1 9 5 2 9 3 14 6 1 1 3 1 4 3-5-3-12-7-18-7h-2z" class="m"></path><path d="M390 269c4-6 8-10 15-14-1 2-3 3-4 4-4 3-8 6-10 11 1 0 1 1 2 2 6-6 15-12 23-14 4-2 9-3 13-4h4c-5 1-10 2-15 4-6 2-22 9-25 15v1h1 0c1-1 3-2 5-3 6-3 15-5 22-5 3 1 6 1 9 2h2c-5 0-10-1-15-1-8 1-17 4-23 8l-1 1c1 1 1 1 2 1h0l-1 1 1 1h0c3-1 6-1 9-1 12 1 23 3 33 8-1 0-2-1-3-1l-9-3c-9-2-20-4-29-2h-4c-2 0-4 3-5 4l-1-1h0 0c1-2 1-4 2-6v3 1l1-1v-1c0-1 1-2 1-2v2h1 0c1-2 1-4 1-6l-1-1s-1-1-1-2v-1z" class="Q"></path><path d="M327 227h1v-2c1 0 2 0 4 1h0c1-1 1-1 2-1h0l1-1h1l4 2 2-1h1c1 0 3 0 4 1l4 2 1-1c1 1 2 1 3 1 0-1-1-1-1-2h0l6 3 2 1c0 1 2 2 2 2 2 0 4 2 6 4l4 4c1 2 5 6 5 8v2 1h0c1 1 1 0 1 1h-1c1 2 3 4 4 7s3 5 3 8h1v-1h1v1l-1 1v1c1 1 1 2 1 3h0v-1-2-1h0c1-2 1-3 1-4 2-3 3-5 4-8 0-1 1-2 2-3 0-1 0-1 1-2l1-1c1-2 3-3 4-5 2-2 4-4 6-5l1-1c1-1 3-2 5-3 3-1 7-4 10-5h0v1h0c-1 0-1 0-2 1-3 2-8 4-11 6l-3 3h-1l-2 2c-1 1-3 2-4 4v1c-1 0-1 1-1 1l-1 1c-3 3-6 8-7 13h0c0 1-1 2-1 2v2 1 1c0 1 1 2 1 2l1 1c0 2 0 4-1 6h0-1v-2s-1 1-1 2v1l-1 1v-1-3c-1 2-1 4-2 6h0 0c-1 0-1-1-1-1 1-2 1-4 1-6h-1v6l-1-6h0-1v5c-1-1-3-2-3-4v-1h-2c-1 1-3 2-4 3 0-2 0-3 1-4l-1-1h-1v2l-1 3h-2v1h-1l-1 1c1-1 1-1 1-2s0-1-1-2h0v-2l-1 1 1-2c0-1 1-2 1-2v-1-3l-1-2c-1-2-2-3-3-5-2-4-6-8-8-13l-2-3c-2-1-2-2-4-4v-1c-1-1-2-2-3-2 0-1 0-1-1-1h-1v1l-1-1c-1 0-1 0-2-1l-3 1-1-2-2-1h-1-1l-2-2-6-5h0z" class="D"></path><path d="M379 252l-2-2c0-1-1-1-2-2h0c1-1 2-1 3-1l1 1v2 1h0c1 1 1 0 1 1h-1z" class="I"></path><path d="M381 263h0l4 8c0-2 0-3-1-4l-2-6h0c1 1 1 1 1 2l3 6v3c1 1 1 2 2 3v1h0c1-1 0-1 1-2v-1c0-1 0-1 1-2h0v-1h0c0 1 1 2 1 2l1 1c0 2 0 4-1 6h0-1v-2s-1 1-1 2v1l-1 1v-1-3c-1 2-1 4-2 6h0 0c-1 0-1-1-1-1 1-2 1-4 1-6h-1v6l-1-6c0-4-2-9-3-13z"></path><path d="M362 230c0 1 2 2 2 2 2 0 4 2 6 4l4 4c1 2 5 6 5 8l-1-1c-1-1-1-2-2-3-1 0-1 0-2-1 0 0 0-1-1-1v1c1 0 1 1 1 2h0l-3-2h0c0 1 0 2 1 3h0l-4-4s0-1-1-1c-1-1-2-2-2-3-1 0-2-1-3-2h0c-1-1-2-1-3-1s-2-1-3-2c1 0 1 0 1-1h-2 3c1-1 1 0 2 0l2-2z" class="a"></path><path d="M362 230c0 1 2 2 2 2 2 0 4 2 6 4h0c-1 0-1 1-2 1-1-1-2-2-4-3l3 3v1h-1l-1-2h-3 0c-1-1-2-1-3-1s-2-1-3-2c1 0 1 0 1-1h-2 3c1-1 1 0 2 0l2-2z" class="p"></path><path d="M364 233c0 1 1 2 0 3-1 0-1-1-2-1v-1h1l1-1z" class="E"></path><path d="M362 230c0 1 2 2 2 2v1l-1 1-1-1c-1-1-3 0-5-1h-2 3c1-1 1 0 2 0l2-2z" class="S"></path><path d="M359 235c1 0 2 0 3 1h0c1 1 2 2 3 2 0 1 1 2 2 3 1 0 1 1 1 1l4 4c2 3 5 6 6 9v1 1c0 2 2 5 3 6h0c1 4 3 9 3 13h0-1v5c-1-1-3-2-3-4v-1-1l1-1c0-2 1-3 0-4h-1v3h-1v-4c-1-3-2-5-3-7s-1-3-2-4v2h-1c0-1-1-2-1-2 0-1-1-2-2-3 0-1-1-1-1-2l-1 1-1-1h1l-1-2s0-1-1-1l-1-2 1-1c-1-1-1-1-1-2l1-1h0v-1l-1-2h-1l-1-1c0-1-2-3-3-4l-1-1z" class="R"></path><path d="M366 244c4 4 6 7 7 12 0-1-1-2-2-3 0-1-1-2-2-2v1l-1 1-1-2s0-1-1-1l-1-2 1-1c-1-1-1-1-1-2l1-1z" class="b"></path><path d="M359 235c1 0 2 0 3 1h0c1 1 2 2 3 2 0 1 1 2 2 3 1 0 1 1 1 1l4 4c2 3 5 6 6 9v1 1c0 2 2 5 3 6h0c1 4 3 9 3 13h0-1v5c-1-1-3-2-3-4l1-1c0-1 1-2 1-3 0-2-1-3-1-4 0-2 0-3-1-4 0-2-1-3-1-4-1-1-1-2-2-3l-3-6c-1-3-4-6-6-8h-2v-1l-1-2h-1l-1-1c0-1-2-3-3-4l-1-1z" class="n"></path><path d="M355 245h1c1 1 1 3 2 3h3l1 2h1l1 1 2 2h1v-2l1 2h-1l1 1 1-1c0 1 1 1 1 2 1 1 2 2 2 3 0 0 1 1 1 2h1v-2c1 1 1 2 2 4s2 4 3 7v4h1v-3h1c1 1 0 2 0 4l-1 1v1h-2c-1 1-3 2-4 3 0-2 0-3 1-4l-1-1h-1v2l-1 3h-2v1h-1l-1 1c1-1 1-1 1-2s0-1-1-2h0v-2l-1 1 1-2c0-1 1-2 1-2v-1-3l-1-2c-1-2-2-3-3-5-2-4-6-8-8-13l-2-3z" class="S"></path><path d="M375 275l1-1v2l1-1v-1c1 0 1 1 2 1h1v1h-2c-1 1-3 2-4 3 0-2 0-3 1-4z" class="Q"></path><path d="M370 263l1-1c1 2 5 9 4 11 0 0-1 0-1-1h0c0-1-2-3-2-4h0v-1-1l-2-3z" class="c"></path><path d="M369 275l1-1v-1c0-1 1-2 2-2v1c0 2-1 1-1 3h1l1-2v1 2l-1 3h-2v1h-1l-1 1c1-1 1-1 1-2s0-1-1-2h0c1 0 1-1 1-2z" class="R"></path><path d="M368 277c1 0 1-1 1-2v1h2l1 1h0l1-1-1 3h-2v1h-1l-1 1c1-1 1-1 1-2s0-1-1-2h0z" class="h"></path><path d="M367 251l1 2h-1l1 1c1 1 2 3 3 4 1 2 1 3 3 4 2 3 4 7 4 11h0c-1-1-2-3-3-5-2-5-5-10-9-14h0v-1h1v-2z" class="D"></path><path d="M355 245h1c1 1 1 3 2 3 1 1 1 2 1 3 1 1 2 1 3 3 0 1 1 3 2 4 3 3 4 8 6 12 0 1 0 2-1 3 0 1-1 2-1 2l-1 1 1-2c0-1 1-2 1-2v-1-3l-1-2c-1-2-2-3-3-5-2-4-6-8-8-13l-2-3z" class="c"></path><path d="M358 248h3l1 2h1l1 1 2 2v1 1c2 2 3 4 5 7l-1 1-1-3h0l-1 1c1 1 2 3 2 4h0c-2-2-3-5-5-8l-1 1c-1-1-2-3-2-4-1-2-2-2-3-3 0-1 0-2-1-3z" class="V"></path><path d="M327 227h1v-2c1 0 2 0 4 1h0c1-1 1-1 2-1h0l1-1h1l4 2 2-1h1c1 0 3 0 4 1l4 2 1-1c1 1 2 1 3 1 0-1-1-1-1-2h0l6 3 2 1-2 2c-1 0-1-1-2 0h-3 2c0 1 0 1-1 1 1 1 2 2 3 2l1 1c1 1 3 3 3 4l1 1h1l1 2v1h0l-1 1c0 1 0 1 1 2l-1 1 1 2c1 0 1 1 1 1v2h-1l-2-2-1-1h-1l-1-2h-3c-1 0-1-2-2-3h-1c-2-1-2-2-4-4v-1c-1-1-2-2-3-2 0-1 0-1-1-1h-1v1l-1-1c-1 0-1 0-2-1l-3 1-1-2-2-1h-1-1l-2-2-6-5h0z" class="T"></path><path d="M361 248l-2-1v-1l1-1c1 1 2 1 2 2v1h0l1 2h-1l-1-2zm-4-5l-1 1h-1v-1c-1-1 0-2 0-3 1 0 1 0 2 1v2z" class="k"></path><path d="M357 241h1l1 1h0c0 1 0 1 1 2 0 0-1 1-1 2l-1-1c-1 0-1-1-1-2v-2z" class="n"></path><path d="M354 233h1 1c1 1 2 2 3 2l1 1-1 2-4-3-1-2zm10 11l-1-1v-1c2 0 2 1 3 1v1h0l-1 1c0 1 0 1 1 2l-1 1 1 2c1 0 1 1 1 1v2h-1l-2-2-1-1-1-2v-1h2l1-2-1-1z" class="E"></path><path d="M366 250c1 0 1 1 1 1v2h-1l-2-2c1-1 1-1 2-1z" class="J"></path><path d="M360 236c1 1 3 3 3 4l1 1h1l1 2c-1 0-1-1-3-1v1l1 1c-1 0-1 1-2 1v-1h-2 0c-1-1-1-1-1-2h0l-1-1c1 0 1 0 1-1 1 0 2 1 3 1l-2-2h-1v-1l1-2z" class="R"></path><path d="M358 241c1 0 1 0 1-1 1 0 2 1 3 1l1 1-1 1-1-1c0 1-1 1-1 2h0c-1-1-1-1-1-2h0l-1-1z" class="U"></path><path d="M337 227h0c1 0 2 1 3 1 2 1 3 3 4 4v1h-1c0-1-1-1-2-2v1l1 1v1h0l1 2-3 1-1-2v-2c-1-1-4-3-5-4v-1c2 0 2 0 3-1z" class="k"></path><path d="M342 233v1h0l1 2-3 1-1-2v-2c1 1 1 1 3 1v-1z" class="K"></path><path d="M347 231v-1c2 1 3 1 5 2l2 1 1 2v1h0l1 2h-1-2c-2-1-3 0-4-1h-1c-1 0-2-1-2-2h0c0-1 0-1-1-2 0-1 0-2 1-3l1 1z" class="f"></path><path d="M347 231v-1c2 1 3 1 5 2l2 1 1 2v1h0c-2-1-3-2-4-3s-3-1-4-2z" class="a"></path><path d="M327 227h1v-2c1 0 2 0 4 1h0c1-1 1-1 2-1h0l1-1h1v1l2 2h-1 0c-1 1-1 1-3 1v1c1 1 4 3 5 4v2l-2-1h-1-1l-2-2-6-5h0z" class="p"></path><path d="M337 234c-2-2-5-4-7-6v-1c1 0 1-1 2-1 1 1 1 1 2 1v-1c1 0 2 0 3 1-1 1-1 1-3 1v1c1 1 4 3 5 4v2l-2-1z" class="a"></path><path d="M340 226l2-1h1c1 0 3 0 4 1l4 2 1-1c1 1 2 1 3 1 0-1-1-1-1-2h0l6 3 2 1-2 2c-1 0-1-1-2 0h-3 2c0 1 0 1-1 1h-1-1l-2-1c-2-1-3-1-5-2l-7-4z" class="Z"></path><path d="M354 226l6 3c-1 1-2 1-3 1-1-1-1-1-2-1-1 1-3 0-4-1l1-1c1 1 2 1 3 1 0-1-1-1-1-2h0z" class="T"></path><defs><linearGradient id="W" x1="342.402" y1="226.581" x2="351.127" y2="229.876" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#49494b"></stop></linearGradient></defs><path fill="url(#W)" d="M340 226l2-1h1c2 1 5 3 8 4 1 1 2 2 4 3h2c0 1 0 1-1 1h-1-1l-2-1c-2-1-3-1-5-2l-7-4z"></path></svg>