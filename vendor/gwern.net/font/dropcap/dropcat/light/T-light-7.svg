<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="45 48 616 624"><!--oldViewBox="0 0 696 752"--><style>.B{fill:#dcdbdb}.C{fill:#e6e5e6}.D{fill:#d2d1d2}.E{fill:#c8c7c8}.F{fill:#959494}.G{fill:#ecebec}.H{fill:#f3f2f3}.I{fill:#a4a3a3}.J{fill:#bebcbd}.K{fill:#7d7c7c}.L{fill:#cdcccc}.M{fill:#d7d6d6}.N{fill:#484748}.O{fill:#c2c1c1}.P{fill:#b9b9b8}.Q{fill:#b0b0b0}.R{fill:#abaaaa}.S{fill:#3b3a3b}.T{fill:#9d9c9d}.U{fill:#f6f5f6}.V{fill:#515151}.W{fill:#414040}.X{fill:#ebeaea}.Y{fill:#565556}.Z{fill:#878586}.a{fill:#1f1e1f}.b{fill:#313031}.c{fill:#616060}.d{fill:#e0dfe0}.e{fill:#b6b4b4}.f{fill:#5c5b5c}.g{fill:#686667}.h{fill:#242324}.i{fill:#767475}.j{fill:#8d8b8c}.k{fill:#151415}.l{fill:#282828}.m{fill:#2d2d2d}.n{fill:#171718}.o{fill:#717071}.p{fill:#363535}.q{fill:#6c6b6c}.r{fill:#4e4d4e}.s{fill:#0c0c0c}.t{fill:#fbfafb}</style><path d="M107 372c1 0 1-1 2-1l1 2c-1 1-1 1-2 1 0 0-1-1-1-2z" class="E"></path><path d="M108 396h2v1l-2 2v-3z" class="P"></path><path d="M608 455h2c0 1 1 1 1 3h-1l-2-1v-2z" class="O"></path><path d="M86 450c1 0 1 0 2 1 0 1 0 1-1 2h0c-1-1-1-2-1-3z" class="C"></path><path d="M554 344l-2-1v-2h1l2 1v2h-1zm18 29l1-1 1 1v1l1-1 1 1v1h-2c-1 0-1-1-2-2z" class="X"></path><path d="M583 381c-1-1-1-1-1-2l-1-1c1-1 1 0 2 0 1 1 1 2 2 3l-1 1-1-1z" class="D"></path><path d="M68 451c0-1 0-1 1-2l2 5h0c-1-1-2-1-2-2l-1-1z" class="R"></path><path d="M536 524l2-1v1c0 2 1 3 2 4v1c-2-1-3-3-4-5z" class="e"></path><path d="M569 523v5h0-1c-1-1-1-1-3-1h0 2c0-1 0-2 1-2l1-2z" class="X"></path><path d="M118 348h2c-1 2-1 2-3 3h-2l-1 1s0-1-1-1h-1c3 0 4-2 6-3z" class="H"></path><path d="M246 656v-2c1 0 1-1 2-1l-1 1h1l1-1c2 0 5 0 7 1h-1c-2 0-4 0-6 1h-1c-1 0-2 1-2 1z" class="G"></path><path d="M560 404l2 5-1 2h0c-1-1-1-2-2-3 1-2 1-3 1-4z" class="H"></path><path d="M179 283c1 0 1 1 1 2 1 1 1 0 1 2h0c0-1-1-1-1-2-2 1-4 2-7 3 2-2 4-3 6-5h0z" class="D"></path><path d="M599 453h1v1c-1 1-1 2-2 2l-1-1v-2h1 0 1z" class="C"></path><path d="M598 444c1 0 2 0 3 1 0 1 1 1 2 2 0 1-1 1-1 2h-1c0-2-1-3-2-4v1l-1 1-1-1 1-2z" class="H"></path><path d="M605 470c0-1 1-1 1-2 2-1 4-2 6-2-2 2-4 3-7 4z" class="C"></path><path d="M316 674l1-1v2h1 1v2c-1 1 0 1 0 2l-1 2c-1-1-1-2-1-4-1-1 0-2-1-3z" class="e"></path><path d="M577 388c0-1 0-2 1-2 1-1 1-2 2-3l1 2v2c-2 0-1 1-2 1h-2z" class="M"></path><path d="M638 495c0-1 1-3 0-4v-1h1 0c2 1 3 2 3 3h-1l-3 2z" class="D"></path><path d="M119 445h0l-4-5v-1l9 8c-2-1-4-1-5-2z" class="d"></path><path d="M118 348c2-2 3-5 4-7h0c1 1 1 1 1 2l-1 1c0 1 0 1-1 2 0 1 0 2-1 2h-2z" class="M"></path><path d="M112 388l4 8v1h-1v2l-1-2c-1-2-1-3-1-5-1-1-1-3-1-4zm477 54c1 1 1 1 1 2l-1 2h0-5l3-3c1 0 2-1 2-1z" class="G"></path><path d="M584 468l12 3h-4 0-1c-2-1-6-1-8-1l1-2z" class="o"></path><path d="M195 611l3-1c-1 1-2 2-3 2l-1 1h-1l-1 1h1l-3 1c-1 1-3 1-4 1l2-2c2-1 5-1 7-3z" class="H"></path><path d="M570 558c1 1 1 2 2 5 0 1 1 2 2 4h-1c-1 0-1-1-1-2-1 0-1-1-1-2-1 0-1-1-2-2h1l-1-1v-1l1-1z" class="C"></path><path d="M583 470c2 0 6 0 8 1l-1 1h0l-1 2v-1h-1c0-1 0-1-1-1l-1-1-1 1v-1c-2 0-3 0-4-1h2z" class="T"></path><path d="M97 418h2l6 3h0c-1 1-2 1-3 1l-1-1h-1 0-1l-2-2v-1h0zm492 103h0c1 1 1 2 1 3s1 2 2 3h1l1 1-1 1c-1-1-4-2-5-4v-3h-1c0-1 1-1 2-1z" class="C"></path><path d="M236 651c4-1 8-1 11 0-1 1-4 1-6 1s-2 0-3-1h-2z" class="G"></path><path d="M640 430c-1-5-2-9-5-13h1c1 1 1 2 2 3 1 3 3 5 4 9l-2 1z" class="I"></path><path d="M607 440h1c1 0 2 2 3 3 0 1 1 2 0 4h0c-1 0-2-1-3-2v-2c0-1 0-1-1-2v-1z" class="G"></path><path d="M575 520v-1c1 1 1 1 1 2s1 2 2 3c0 1 0 2 1 3h1c1 2 1 2 1 4h0c-2-1-3-4-4-6 0-2-1-3-2-5zM104 291c0 2-2 2-3 2s-1 1-1 1h0v1c-2 1-4 3-7 3l1-2c3-1 5-3 7-4l3-1z" class="O"></path><path d="M604 459c1-1 2-1 3-2 2 1 2 2 3 2v1h0s-1 1-1 2l-1 1h0c-1-2-1-2-2-2-1-1-1-2-2-2z" class="G"></path><path d="M542 285c2-1 2 0 4 0 0 1 1 1 1 1l1-1v1l1 1h0l1 1c-2 0-4 1-5 0-1 0-2-1-2-1-1-1-1-1-1-2z" class="B"></path><path d="M545 288c1-1 3-1 4-1l1 1c-2 0-4 1-5 0z" class="D"></path><path d="M407 437c2 4 4 7 5 11l-2-1-3-6v-4z" class="c"></path><path d="M600 539h0c3 0 5 0 8-2l1-2v1c-2 3-4 4-8 5h-5-1v-1h1c2-1 2-1 4-1z" class="K"></path><path d="M182 615l6-1-2 2h-3c-2 1-5 1-7 0 0 0-1-1-2-1h-1 9z" class="M"></path><path d="M185 655h0-3c-1-1-2-1-3-2h1l-1-1c0-1 0-1 1-2l1 1 1 1h2l2 1v1s-1 0-1 1z" class="U"></path><path d="M519 437h0c3-1 2-4 3-5 0-1 1-1 2-1 0 2 0 2-1 3 0 2-1 3-1 5-1 0-1 1-2 1v1c0-1 0-2-1-4z" class="R"></path><path d="M571 339l-1-1v-1l-1 1h1v1c-2-2-6-1-8-1h-2v-1c0-1 1-1 1-1l1-1c1 0 1 1 2 1s3 0 4 1h3 0v2h0z" class="C"></path><path d="M69 418h1c1 0 2-1 3-1 1-1 2-1 3-2 1 0 3-1 4-1 0 1 0 1-1 1-4 1-7 4-11 6 0-1-1-1-2-2l3-1z" class="o"></path><path d="M179 283l3-3 2 1c-1 1-2 1-2 3 1 1 1 1 2 3l-3 2v-2h0c0-2 0-1-1-2 0-1 0-2-1-2z" class="E"></path><path d="M586 392h0c0-2 1-6 0-8v-1h0c1 2 1 3 1 5v1c1 1 0 2 0 3v1 2l-1 1v1h-1 0 0c-1-1 0-2 0-2v-1c0-1 0-2 1-2z" class="M"></path><path d="M566 463h5c-2 1-2 1-4 1h2c1 1 1 2 3 1 3 0 5 1 7 2h-5-5l-3-1h2l1-1h-1c-1 0-2-1-2-1v-1z" class="C"></path><path d="M206 633c-1-2-1-1-2-2h1c1-1 2-3 3-3h2v1c0 1 0 3-1 4h0-3z" class="H"></path><path d="M587 432v-3h1v3h1c2 0 4-1 6-2h0 1l-9 6v-2h0v-1h0v-1z" class="Y"></path><path d="M95 303l2-1c3-1 6-2 9-1h0v1h-5c-1 0-1 0-2 1h-1c-1 1-1 1-1 2 0 0 0-1-1 0l-3 2c0-2 1-2 2-4z" class="E"></path><path d="M234 643l1-1h0l4 1 1 1c2 0 4 1 5 2s2 1 2 1l1 2h-4l-2-2v-1-1h-2l-4-1c-1-1-1-1-2-1z" class="J"></path><path d="M206 633h3c-1 1-2 3-3 4 0 1-1 2-3 2 0-1-1-1-1-2 1-2 3-3 4-4z" class="X"></path><path d="M189 620l1 1c-1 1-1 1-1 2-1 2-3 5-3 7v1l-1 1-1-1c0-1 1-1 1-1v-1-2c1-1 1-2 1-3v-1l3-3z" class="B"></path><path d="M584 326h3 0-1l-2 1c-1 0-1 0-2 1h-1 0v1c-1 1-3 1-4 2s-2 2-2 4h-1c0-1 0-2 1-3v-1l-1-1h1c2 0 3-2 5-3h1c1-1 2-1 3-1z" class="C"></path><path d="M641 448v1h0v1 1l-1 1v1c0 1-1 4-1 5h-1v1s-1 0-1 1 0 1 1 2c0 1-1 1-2 2h0-1v1c-1 1-2 2-2 4l-1-1s1-1 1-2h0c2-3 3-5 4-8 0-1 0 0 1-1l1-2v-1c1-2 1-4 2-6z" class="D"></path><path d="M108 455h3c0 1 0 2 1 3 1 0 2 0 3 1h-1-1l-4-1c-1 1-2 1-3 1v-2l-1-1c1-1 1-1 3-1z" class="C"></path><path d="M184 652c1 0 1 0 2-1h1l1-1 1-1c1 0 1-1 1-1l1-1h1c-1 1-1 1-1 3h1 0c-1 1-2 2-2 3l-2 1c-1 1-2 1-3 1 0-1 1-1 1-1v-1l-2-1z" class="H"></path><path d="M572 393v-3c0-2 1-3 1-5h1c0-1 0-2 1-2h0v2h0l-1 2v1 1c0 1-1 2 0 3 0 3-2 5-4 7h0-1c2-2 3-3 3-6z" class="X"></path><path d="M106 417c1 0 4 1 5 0 1 0 1-1 2 0h2 0l2 3 6 4v1c-4-3-8-6-12-7l-5-1h0z" class="V"></path><path d="M562 409c1 2 1 3 1 5 1 1 1 2 1 3l-1 2c0-2 0-2-1-3h-1c0 2 0 4-1 5h0c-1-2-1-5-1-7l2 2v-1-4l1-2z" class="G"></path><path d="M542 285c1-1 0-2 1-2 0-1 2-3 3-4l1 1h0c0 2 1 3 1 5l-1 1s-1 0-1-1c-2 0-2-1-4 0z" class="L"></path><path d="M429 504l1-1c1 1 1 2 0 4l-2 1v-1 1l-2 3s-1 0-1 1v1h-1c0 1-1 2-1 3l-2 3-2 3v1c-1 1-1 1-1 2h-1c0-1 2-4 2-5l2-3 6-9 1-2 1-2z" class="G"></path><path d="M601 460h1c1-1 2-1 2-1 1 0 1 1 2 2 1 0 1 0 2 2h0c-1 1-1 0-1 1-1 0-1 0-2 1 0-1 0-1-1-2 0 0 0-1-1-1l1-1h1l-1-1c-1 0 0 0-1 1h0v3h0-1c-1-1-1-3-1-4z" class="H"></path><path d="M601 460h1c1 1 0 2 0 3v1c-1-1-1-3-1-4z" class="Q"></path><path d="M372 674l1-1s1 0 1 1v1l1 1c-1 3-1 5-3 8-1-1-1-3-1-4h0c0-2 1-4 1-6z" class="J"></path><path d="M372 674l1-1s1 0 1 1v1c-1 2-1 4-3 5h0c0-2 1-4 1-6z" class="B"></path><path d="M177 314v1h1 2c-1 1-3 4-3 5l-3 3h-1c1-2 1-4 1-6l3-3z" class="W"></path><path d="M66 419c1 1 2 1 2 2-4 1-6 5-9 7-1-2-2-4-2-6 1-1 1-2 2-3v1l-1 1c0 2 0 3 1 5h1l4-6 2-1z" class="V"></path><path d="M51 499c-1 0-1-2-1-3 0-2 0-4 2-5h0c1 1 1 3 1 5-1 1-1 1 0 3 0 1 1 3 2 4h0c-3-1-3-2-4-4z" class="O"></path><path d="M642 411h0c-1-1-1-2-2-3l1-1 1 1 1-1c1 0 1 0 2 1v3c1 2 1 4 2 6h0c-1 0-1 0-1-1v-1-1h-1v2c1 1 1 1 1 2h0c-1-2-2-5-3-7h-1z" class="H"></path><path d="M580 527l-1-5v-1l1 1c0 2 1 3 2 4v1h0c1 2 2 4 4 5 1 1 2 1 3 2h-2-1c-1-1-1-1-2-1 0-1-2-2-3-2 0-2 0-2-1-4z" class="e"></path><path d="M117 300c-1-1-1-3-2-5s-3-3-4-4h1c3 2 6 7 7 11 1 3 1 5 0 7h-1 0c0-3 0-6-1-9z" class="j"></path><path d="M86 439h0 2c-2 1-3 1-4 3-1 0-1 1-1 1 0 2-1 4-1 6 0-1-1-1-1-1-2-1-1-2-1-4v-1c2-2 3-3 6-4z" class="X"></path><path d="M614 517c1 1 1 1 1 2 0 3-1 6-3 8v1c-2 2-5 5-8 5-1 1-2 1-3 0 2-1 2-1 4-1l1-1c2-1 4-2 6-5h0v-1c1-1 2-4 1-5h0l1-3z" class="P"></path><defs><linearGradient id="A" x1="576.824" y1="465.641" x2="573.706" y2="470.583" xlink:href="#B"><stop offset="0" stop-color="#777a75"></stop><stop offset="1" stop-color="#8d868c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M574 467h5c1 1 3 1 5 1l-1 2h-2c-1 0-3-1-4-1h-8v-2h5z"></path><path d="M585 471v1l1-1 1 1c1 0 1 0 1 1h1v1c0 1-1 2-1 2-1 0-2 0-3-1l-5-1v-1h1c1 0 1-1 2-1l2-1z" class="C"></path><path d="M585 475h0 1v-2c1-1 2 0 3 0v1c0 1-1 2-1 2-1 0-2 0-3-1z" class="G"></path><path d="M234 643c1 0 1 0 2 1l4 1h2v1 1c-3-1-7-1-10-1v-1c-2 0-3 0-5-1 2-1 2 0 4 0h2l1-1z" class="C"></path><path d="M574 330l1 1v1c-1 1-1 2-1 3h1v4c-1 1-1 2 0 3s1 2 2 2l-1 2c-1-2-2-3-2-4l-2-2h0l-1-1h0v-2c2-1 2-5 3-7z" class="D"></path><path d="M402 428c2 1 4 7 5 9v4l-2-3c-2-3-4-5-3-10z" class="Y"></path><path d="M148 615h0s0 1 1 1h1c-4 1-8 2-10 5-1 2 0 7 1 9h-1l-1-2c-1-3-1-5 0-8 0-1 1-2 3-3l6-2z" class="o"></path><path d="M559 464c2-2 4-3 6-4l1 1c3 1 6 1 10 1h3c1 0 2 0 3 1-1 1-1 1-2 1-2-1-3-1-5-1h-4-5-1-2c-1 1-3 0-4 1z" class="Q"></path><path d="M150 357c0 1 0 1 1 2-1 0-1 1-2 1 0 2-1 2-2 3v3c-2 2-3 5-4 8v-1c-1 0-1-1-1-1 0-3 2-6 4-8v-2c1-2 2-3 4-5z" class="R"></path><path d="M59 419c0-1 0-1 1-1l2-2h0c1 1 1 1 0 2v1c0 1 0 1-1 2h0 1c1 0 1 0 2-1l-4 6h-1c-1-2-1-3-1-5l1-1v-1z" class="d"></path><path d="M613 520c1 1 0 4-1 5v1h0c-2 3-4 4-6 5l1-3v-1h-2c2-1 3-2 4-3 2-1 3-3 4-4z" class="X"></path><path d="M553 341h2s1 0 1 1v-1l1 1h0v1h1v-1c0-1 0-1 1-1 0 0 1-1 1-2h1 1 0l1 1c1 0 1 0 1 1v1c0 1-1 1-1 2h-2-2-1-1 0-1l-1 1h0l-1-1h0 1v-2l-2-1z" class="H"></path><path d="M112 295c2 1 3 2 3 4 1 1 1 2 2 3v-2c1 3 1 6 1 9 0 0-1 1-2 1 0-1 0-3-1-4 0-4-1-8-4-11h1z" class="e"></path><path d="M469 483v1 1h0 1l2-2h0v2c-2 2-4 5-6 6-1 0-2 1-4 1h0c1-1 2-1 2-2v-2c2-1 3-3 5-5z" class="P"></path><path d="M426 503l1 1c0 1 0 1 1 2l-1 2-6 9v-2l-1-1v-1l2-2 2-4c1-1 1-2 2-4z" class="F"></path><path d="M426 503l1 1c0 1 0 1 1 2l-1 2h-2c-1 2-1 2-3 3l2-4c1-1 1-2 2-4z" class="K"></path><path d="M605 398c2 0 4 0 5 1l1-1v-1l1-1v1 1c2 0 3 0 5 1 2 0 7-1 8 1h-1s-1 0-1 1v1c-1-1-2-1-3-1h0c-1 0-2 0-2-1h-3c-3-1-7 0-10-1v-1z" class="M"></path><path d="M177 292c2 0 2 0 3-1l1 1-1 1v1l3-1v1h-1c-1 1-3 2-3 4h1l-3 3-1-2s1 0 1-1h0c0-1 1-1 1-2l-1-1c-1 1-1 1-1 2 0-1-1-1-2-2 0-1 1-1 2-2l1-1z" class="U"></path><path d="M408 544c1-3 1-6 3-9v3h0c0 2-1 3-1 5 0 1-1 3-1 4v2h0v1 1s-1 1 0 2v1c0 1 0 0 1 0v-2c1-1 0-1 0-2l2-1c-1 2-1 4-2 7v1h-1c-2-2-1-5-1-8 0-1 1-2 0-3v-2z" class="L"></path><path d="M119 309v1c1 0 1 0 1-1 1-5 0-11-2-17 3 3 4 11 3 15 0 2 0 4 1 6v1h3c0-1 1-1 1-2l1 1v1c-2 1-2 1-4 1-1 0-2 0-2-1-2-1-3-3-3-5h1z" class="F"></path><path d="M371 680h0c0 1 0 3 1 4-2 2-4 3-6 4l-1 1-1-1c-1 0-2-1-2-2l5-3h0l1 1c1-1 1-2 2-3v-1h1z" class="E"></path><path d="M548 616l3 3c2 0 4 0 6-1v-1l1 1-2 1-1 1c-1 1-2 1-3 2l-1 1h-1v-1c-1-1-1 0-3-1l-1-1c0-2 0-3 1-4h1z" class="X"></path><path d="M519 437c1 2 1 3 1 4-2 4-4 9-7 13v-1c0-2 1-3 1-5 1-2 1-5 3-7 0-1 1-2 2-4z" class="g"></path><path d="M104 291h3c1-1 2-1 2-1l1 1h2-1c1 1 3 2 4 4s1 4 2 5v2c-1-1-1-2-2-3 0-2-1-3-3-4v-1h-1l-1-1c-3-1-7 0-10 2v-1h0s0-1 1-1 3 0 3-2z" class="X"></path><path d="M109 360l5-2h5l3 3h0c-1 0-2-1-2-1-1 0-2-1-3 0h-1-1c0-1-1 0-2 0l-1 1h-1v2h-1c0 1-1 1-1 2-1 0-1 1-2 1h0v-1-1c-1 1-2 1-2 2l-1-2c1-1 3-2 5-4z" class="M"></path><path d="M49 471l-2-1c-1 0-2-1-3-2v-2c2-1 5 0 7 1 1 1 2 1 2 2v1 2c-1 0-1-1-2-1h-2 0z" class="F"></path><path d="M49 471c0-1-1-2-1-3l5 2v2c-1 0-1-1-2-1h-2 0z" class="H"></path><path d="M629 478h1c-6 4-13 5-19 5h-7v-1h-2l2-1h0 7c1-1 2-1 3-1v1c6 0 10-1 15-3z" class="J"></path><path d="M604 481h0c1 0 2 0 3 1h3l1 1h-7v-1h-2l2-1z" class="D"></path><path d="M615 512v-3l1 1s0 1 1 1h0l-1-4 1-1c1 3 2 6 2 10 1 1 1 3 1 4l-1 2v-1-1c-1 3-2 8-4 10v-1c1-2 2-5 2-8 1-2 1-3 1-5-1 0-1-1-1-2v-1-1c-1 0-1 0-1-1v2l-1-1z" class="B"></path><path d="M482 607v1l1 1h1c1 0 2 0 3 1l1-1h0l1 1c-1 1-1 1-1 2l-1 1h0c0-1 0-1-1-2h0c-1 0-2 0-3-1v1c0 1 0 2 1 3v1c-1 0-1 0-2 2 0 0 1 0 2 1h0v1h-1v1l-1 1v1c1 1 1 1 2 1-1 1-1 1-2 1l-1 1c0-6 0-13 1-18z" class="O"></path><path d="M98 442c1-1 1 0 2-1 1 1 2 1 3 2l1 1v5c2 0 3 2 3 2v1c-1 0-2 0-2-1l-1-1h-1v2h0v1l-1-1v-3c1-1 1-1 1-2 0 0-1-1-2-1-1-1-2-1-3 0l-1-1c0-1 0-2 1-3z" class="G"></path><path d="M504 629c0-1 0-1-1-1 0-1 0-1-1-2h0c-1-1-1-2-2-3h1l1 1c1 1 2 2 2 3 1 0 0 0 1 1h1v-4c1 1 1 1 1 2h0c1 1 0 2 0 2l-1 2v1h0c1 0 3 1 3 2 1 1 1 2 0 4h0c0 1 0 1-1 1h0v-1c0-1-1-2-2-2l-1-1-1-1h0-2s0-1-1-1h0 1 1c1 0 2 0 2-1s0-1-1-2z" class="X"></path><path d="M95 321l1-1c1-3 4-5 6-7 0-1 0-1 1-1h0c-2 3-7 6-7 9v1h0c2-1 3-2 4-2h1c3-1 6-1 10-1 1 0 2 1 3 0 0 0 1-1 1-2 0 2 0 2-1 3h-1c-3-1-10-1-13 1-2 1-2 2-3 4-1 1-2 3-3 5v-5c0-1 1-2 1-4h0z" class="j"></path><path d="M618 477v-1c2-3 7-5 10-8 1-1 1-2 2-3 1-2 2-2 4-3-1 2-1 3-2 5-1 0-1 1-1 1-1 1-2 2-3 2s-1 2-3 3l-1 1c-1 0-1 0-2 1h0l-2 1c-1 0-1 0-2 1z" class="C"></path><path d="M71 402c-1 0-1-1-1-2 1 0 1 0 2-1 2-1 4-1 5 0 0 0-1 1-2 1v1c1 0 1 0 1 1v1h1v1 1 1h0c1 0 1 0 1 1v1h0c-1-1-1-1-2-1 0 0-1 0-1-1h-1-1c-1-1-2-2-2-3v-1z" class="G"></path><path d="M71 402h0c1-1 0-1 1-1l1-1h0c0 1 0 2-1 3h0c-1-1 0-1-1 0v-1z" class="X"></path><path d="M605 421c2-1 3-1 5-1l1 1h1l2 2v1c-3-2-4-3-7-3l-1 1h-1-1a30.44 30.44 0 0 0-8 8h-1 0c-2 1-4 2-6 2 2-1 3-2 4-3l2-1 1-3 2 1c1-2 3-4 5-5h2 0z" class="I"></path><path d="M596 425l2 1c-1 0-2 1-3 2l1-3z" class="e"></path><path d="M572 340h0l2 2c0 1 1 2 2 4 2 1 3 3 5 3 1 0 2-1 3-1l3 1h1 1 2 2c1 0 2-1 3 0-1 0-1 1-2 1s-2 0-3 1l1 1h0-3c-1 0-3 0-3-1-2 0-4 0-5-1-5-2-8-6-9-10z" class="I"></path><path d="M473 470l1-1 1 1c-1 1-1 1-1 2s0 1-1 2c-1 3-4 6-5 8l-1-1-1-1h0v-1h-1l2-2c1 0 2-1 2-1v-2h0c1-1 2-3 4-4z" class="e"></path><path d="M571 521l1-1v-1c1-1 1-1 1-2l2-2v1c1-1 1-2 1-2h0 1c1 1 1 1 1 2l-2 2h1l2 2c2 0 2 0 3 1v1 5-1c-1-1-2-2-2-4l-1-1v1l1 5h-1c-1-1-1-2-1-3-1-1-2-2-2-3s0-1-1-2v1h-1l-1 1h-2z" class="X"></path><path d="M468 529c2 0 5-1 8-2 7-2 16-3 24-2l-1 1h-3c-2 0-6 0-8 1-6 2-13 1-19 3h-2l1-1z" class="e"></path><path d="M580 514v3h1c2 1 3 1 5 2 1 0 2 1 3 2-1 0-2 0-2 1-1 1-1 1 0 2l-1 1-4-4c-1-1-1-1-3-1l-2-2h-1l2-2c0-1 0-1-1-2h3z" class="D"></path><path d="M580 514v3h1-1l-1 1h1 0v1c-1 0-2-1-3-1h-1l2-2c0-1 0-1-1-2h3z" class="G"></path><path d="M605 473c3-1 5-2 7-3l1 1c-7 3-13 4-21 3-1 0-1-1-2-2h0l1-1h1 0 4 3-2v1c1 0 2 1 2 0l4-1c1 1 2 1 2 2z" class="e"></path><path d="M599 472l4-1c1 1 2 1 2 2h0c-2 0-4 0-6-1z" class="E"></path><path d="M592 471h0 4 3-2v1c-1 1-4 0-5-1z" class="V"></path><path d="M575 453l4-2c-2 2-3 2-5 4h0l1-1h3 0 1c1 0 1 0 2 1s3 1 4 1v1c0 1 0 1-1 2h-1-1c0 1-1 1-1 1l-1-2c-2-1-3 0-4 0h-1 0v-1l-1-1c-1 0-1 1-3 1v-1c0-1 1-1 2-1 0-1 1-1 1-2h1z" class="G"></path><path d="M620 401h0c1 0 2 0 3 1l-1 1c-2 1-2 2-4 3h-1c0 1 0 1-1 1h-1v-1c1-1 1 0 2-1 0 0-1-2-2-2h-6c-2 0-4 1-6 1-2 1-4 3-6 3l-1-1c1 0 3-1 4-2h1c2-1 6-2 9-2 1 0 4 0 5-1h2c1 1 1 1 2 1l1 1v-1-1z" class="E"></path><path d="M609 403h6c1-1 1-1 3 0l1 1-2 1s-1-2-2-2h-6z" class="C"></path><path d="M640 467l4-2h1c1 0 2-1 2-1 1 1 1 2 1 3v1l-1 1c0 1 0 1-1 2h-1v-1l1-1v-2l1-1h-1c-1 1-2 1-2 3-1 2-5 3-6 4-3 2-5 4-8 5h0-1l9-6c0-1 2-1 2-2h-2l-1-1 1-1c1 0 1 0 1-1h1z" class="E"></path><path d="M447 643c2 1 3 0 4 0h2 2c1-1 1 0 2-1h5 1c1 1 1 0 2 1h2c1 0 2 1 3 2h0-2c0-1 0-1-1-1h-1-3c-2-1-4 0-5 0h-2-1c-1 0-1 1-1 1-1-1-2 0-3 0h-1c-1 0-1 0-2 1h9 1 2v1c-2 0-5-1-7-1-3 0-6 1-9 1l-1-1c2-1 3-1 4-3z" class="L"></path><path d="M484 618h1v2l-1-1v1 1h-1v1h1c1 2 2 2 2 3s-1 1-1 1c0 1 0 1 1 2h1 0l3 3h-1-1-1l-1-1c0 1 0 1 1 2h0c0 1 0 1 1 2v1l1 1v2h0c-1 0-1 0-1-1-1-1-1-1-1-2v-1c-1 0-1-1-1-2h0c-1-2-2-2-3-4h-1l-1 3v-6l1-1c1 0 1 0 2-1-1 0-1 0-2-1v-1l1-1v-1h1v-1z" class="G"></path><path d="M42 476h0c1 1 2 3 4 4 1 0 1 1 2 2h1c3 2 8 4 11 6 3 1 7 1 10 1h7-2c1 1 2 1 2 1h0 0c-4 1-8 1-12 0-7-1-14-5-19-9-1-1-2-1-3-3-1 0-1-1-1-2z" class="R"></path><path d="M112 388h0c-1-2-1-3-2-4h-1c0-1 0-1 1-1s2 1 2 2c1 1 1 1 1 2v1c1-1 1-2 1-2v-1c1 1 1 1 1 2 1 3 2 5 4 8 0 2 2 3 3 5v2l-3-3c-1-1-2-2-2-3h0l-1-1v1l-4-8z" class="L"></path><path d="M82 457l1-1c1 0 1 1 1 1 2 1 3 3 5 3 1 1 2 0 3 0 0 0 1 1 1 2v2h-1 0v-2h-1v2h-2 0-1 0c-2 0-2 0-3-1h-2c0-2-1-2-1-4-1 0-1 0-1-1h0c0-1 1-1 1-1h0z" class="U"></path><path d="M422 660h0v-1h1 1-1l1 1h0 1c3 1 6 5 7 8s0 5-1 8c-2 4-6 6-11 8-4 1-10 2-15 1v-1h11l4-1c4-1 8-3 10-7 1-3 1-6 0-9s-5-6-8-7z" class="I"></path><path d="M630 431c2 1 2 3 4 6h0v3c1 3 0 6 0 9v1c-1-2 0-4-1-6v-1l-1-1h0v-2 1l-1 1v2l-1 1v3h0v-4-4-4c-1-2-1-4 0-5z" class="B"></path><path d="M348 698h1c1 0 2 2 3 2 1 1 1 1 2 1h0l1 1c0 1 1 2 1 2 2 3 4 4 6 6h-1c-1 0-1 0-2-1h-1s-1 0-2-1h0l-1-1-4-3c-1 0-2-1-2-1-1-1-1 0-2-1 0 0 0-1 1-1v-1-2z" class="H"></path><path d="M430 652l8-1-1 1h-2c2 1 3 1 4 1 2 0 5 0 7 1h2 1 0c-1 1-4 1-5 2h-5-1l-9-1h0c1 0 1 0 2-1h-2v-1s1 0 1-1z" class="L"></path><path d="M448 654h1 0c-1 1-4 1-5 2h-5v-1c3 0 6 0 9-1z" class="M"></path><path d="M430 652l8-1-1 1h-2c2 1 3 1 4 1-2 0-5 0-8 1h-2v-1s1 0 1-1z" class="C"></path><path d="M434 496c1-4 2-5 5-8 2-2 5-5 8-7 2-1 3-2 6-2l-11 10c-3 2-5 5-7 6l-1 1z" class="E"></path><path d="M56 459c-1-2-1-4-1-6-1 0-1-2-1-2 0-1 1-2 1-3 1-2 2-5 3-7 1-1 2-3 3-4s2-2 3-4c0-1 0-2 1-2h0c0 1 0 2 1 3l1 3v1h-1v-1s-1-1-1-2h0c-1 0-3 2-4 3-1 3-3 6-4 9-1 0-1 2-1 3v2c0 2 0 5 1 6l4 6 1 2h0c-1 0-2-2-3-3s-1-2-2-4h-1z" class="Q"></path><path d="M129 461c2-1 2 0 4 0 1 1 2 1 3 2h-1c-1 1-2 1-3 1 0 1 1 1 1 1v1c-2 0-4-1-6 0s-4 0-5 0l4-1h0c-4-1-7-1-11-1h-1 0c-2 1-7 1-8 0h0c2-1 3-1 5-1 5-1 9 0 14 1 2 0 6 0 7-1 0-1 0-1-1-1s-2-1-2-1z" class="e"></path><path d="M187 275l2 2s1-1 2-1v1l-3 3 1 1h0v1c-1 2-3 4-5 5-1-2-1-2-2-3 0-2 1-2 2-3l-2-1 5-5z" class="i"></path><path d="M191 276v1l-3 3 1 1h0l-6 1 6-5s1-1 2-1z" class="S"></path><path d="M69 418c4-3 8-5 12-5h0l1 1s1 0 1-1h0c1-1 2-1 3-1 5 0 8 1 12 2 3 2 5 2 8 3h0-1c-4 0-8-2-12-3-3 0-6-2-9-1v1c2 0 4 0 5 1 3 0 6 2 8 3h0c-5-2-10-3-15-3h-3c1 0 1 0 1-1-1 0-3 1-4 1-1 1-2 1-3 2-1 0-2 1-3 1h-1z" class="K"></path><path d="M348 685c2 0 3 1 5 1h2c1 1 1 1 2 0 1 0 2 0 2-1 1 1 1 1 2 1h1 0c0 1 1 2 2 2l1 1h-2l-3 1c-1 1-3 0-5 1h-2c-1-1-2-2-3-2v-1c-1-1-2-2-2-3z" class="B"></path><path d="M362 686h0c0 1 1 2 2 2l1 1h-2-3v-2h1l1-1z" class="M"></path><path d="M355 691l1-2c1 0 2-1 3-1s0 1 1 2c-1 1-3 0-5 1z" class="U"></path><path d="M348 685c2 0 3 1 5 1h1c-1 1-2 1-3 1h-1v1c-1-1-2-2-2-3z" class="G"></path><path d="M103 350h3c1 1 2 1 3 2-2 0-4 2-6 4v1 1h1l4-2h0v1c0 1 0 1 1 1-2 0-3 1-4 2h0l-2 2v-1h0c1-1 1-1 1-2h-1c-1 1-2 2-2 4-1 0-1 1-2 1 0-1 1-2 1-4 1-2 1-4 2-6l1-1h0v-1l-2 1-1-1 1-1 2-1z" class="C"></path><path d="M105 360h0c0-2 1-2 3-3 0 1 0 1 1 1-2 0-3 1-4 2h0z" class="H"></path><path d="M106 350c1 1 2 1 3 2-2 0-4 2-6 4v1c0 1 0 1-1 2v-1c0-2 0-3 2-4l1-1v-1h-2c1-1 2-1 3-2z" class="O"></path><path d="M642 429c1 6 1 13-1 19-1 2-1 4-2 6v1l-1 2c-1 1-1 0-1 1-1 3-2 5-4 8h0c0 1-1 2-1 2l-1 1v-1s0-1 1-1c1-2 1-3 2-5 2-4 3-8 5-13 1-3 2-7 2-11 0-3 0-6-1-8l2-1z" class="R"></path><path d="M537 417c-2-2-2-4-2-6-1-3 0-6 2-8s6-4 9-5v1 1h0 0c-1 1 0 1-1 1h0c-1 0-1 1-2 1v1l-1 1c-2 1-3 3-4 5h0v2h-1s0-1-1-1h-1 1v4h0c0 1 0 0 1 1v2z" class="E"></path><path d="M465 479h1v1h0l1 1 1 1v1h1c-2 2-3 4-5 5-1 0-1 0-2-1l-1 1c0 1-1 2-2 2h0v-1-1c0-1 1-1 1-2l-1-1h0l3-4c1-1 2-2 3-2z" class="C"></path><path d="M465 479h1v1h0l-1 2c-1 1-2 2-2 3h-1v-1c0-1 1-1 1-2l-1-1c1-1 2-2 3-2z" class="L"></path><path d="M584 326h5 0c2 0 6 3 7 5l1 1c0 1 1 2 1 3l2 2h0 0c1 1 1 3 1 4 1-1 1-2 1-3l-1-1c1 0 1-1 1-2l1 4c0 1 0 2-1 3h-1c0-2-2-5-3-6-2-4-6-6-10-7l-3 1-3-1h-1v-1h0 1c1-1 1-1 2-1l2-1h1 0-3z" class="G"></path><path d="M582 329c2-1 3-1 4-1l1 1h1l-3 1-3-1z" class="B"></path><path d="M642 411h1c1 2 2 5 3 7 0 3 1 7 2 10 1 1 1 2 1 3v1 1 3 1l-1 1h0v2 1c0 1-1 2-1 2h-1v-1c1-1 1-2 1-3l-1 1h-1l1-3c1-5 0-12-1-16l-3-10z" class="J"></path><path d="M83 422h0c0-1 0-1 1-1h2 0 1c1 0 2 1 2 1h1c0 1 1 1 1 2h1c1 0 2 2 3 3 1 2 4 4 7 5l3 3 1 1h1v1h-1c0 1 0 1 1 2v1c0 1 0 1 1 2v1h-2s0-1-1-1c0-2-1-4-2-5-1-3-5-5-7-7-2-1-3-3-4-5-2-1-4-2-6-2-1-1-2-1-3-1z" class="j"></path><path d="M622 359v4c0 1-1 1-1 1v2c0 1 0 2-1 3v1c-1 2-2 4-3 5v1l-1 1v1l-1 1h0v1s1-2 2-3c0 0 1-1 1-2l1-2s1-1 1-2l2-2c0-1 1-2 1-3l2-3h0c-1 4-3 7-5 11-4 5-7 11-11 16-2 1-3 2-4 4l-1-1 2-3 2-2 2-3 1-1v-2-1h1v2c3-5 6-11 8-16 1-3 1-5 2-8h0z" class="B"></path><path d="M96 409h1 1c5 3 9 5 15 7l-5-5h1c1 1 2 2 4 2l2 4h0-2c-1-1-1 0-2 0-1 1-4 0-5 0-3-1-5-1-8-3h1s1-1 0-1c0-1 0-2-1-3-1 0-1 0-2-1z" class="S"></path><path d="M96 409h1c5 4 9 6 14 8-1 1-4 0-5 0-3-1-5-1-8-3h1s1-1 0-1c0-1 0-2-1-3-1 0-1 0-2-1z" class="X"></path><path d="M51 499c1 2 1 3 4 4h0c-1-1-2-3-2-4-1-2-1-2 0-3 0 1 1 3 1 4l1 1c1 1 5 2 7 2l-4 1v1c1 1 2 1 4 1s3-1 5-2l1 1-2 2h1c-1 1-2 2-4 2s-5-1-7-2c-3-2-5-5-5-8z" class="T"></path><path d="M59 507c-2 0-2 0-3-2h0l1-1 1 1c1 1 2 1 4 1-1 1-2 1-3 1z" class="J"></path><path d="M62 506c2 0 3-1 5-2l1 1-2 2c-2 1-4 1-7 0h0c1 0 2 0 3-1z" class="H"></path><path d="M177 320v1 3c-1 1-2 3-2 4h1v1l-3 4v1h-2l-5 4c2-3 4-5 4-8 1-2 2-6 3-7h1l3-3z" class="E"></path><path d="M177 321v3c-1 1-2 3-2 4h1v1l-3 4v1h-2l3-8c1-2 2-4 3-5z" class="o"></path><path d="M62 503c5-1 8-3 12-5l2 1-3 2v2c-2 1-4 3-6 4h-1l2-2-1-1c-2 1-3 2-5 2s-3 0-4-1v-1l4-1z" class="K"></path><path d="M73 501v2c-2 1-4 3-6 4h-1l2-2-1-1h0l6-3z" class="O"></path><path d="M115 315h1c1 1 1 2 1 3v3h-1c0 1-1 2-2 3-1 0-1-1-1-1-3-1-7-1-10-1h-1s-1 1-2 1h0c-1 1-2 2-3 2 1-2 1-3 3-4 3-2 10-2 13-1h1c1-1 1-1 1-3v-2z" class="D"></path><path d="M109 321h-2v-1h3l-1 1z" class="C"></path><path d="M115 317v-2l1 2v2 1h0c-1 1-1 2-2 2-2 0-3 0-5-1l1-1h3 1c1-1 1-1 1-3z" class="X"></path><path d="M246 656s1-1 2-1h1c2-1 4-1 6-1v1h0c2 2 5 2 8 2h0l2 1c-1 1-1 1 0 1l1 1h-4-3-4c0-1 0-2-1-3h-2 0c-2 1-3 3-5 3h-1c2-2 4-3 6-4v-1c-1 0-3 1-4 1-1 1-2 3-4 4l-1-1 3-3z" class="Q"></path><path d="M263 657h0l2 1c-1 1-1 1 0 1l1 1h-4-3c1-1 3-1 4-3z" class="j"></path><path d="M77 399h2l1 1v2c1-1 1-1 2-1s1 0 2-1c1 0 2 0 3 1 2 0 3 0 5 1l1 3-1 1v-1c-2-1-3-1-5-1l-1 1h-1c-2 0-4 0-5-1h-3v-1h-1v-1c0-1 0-1-1-1v-1c1 0 2-1 2-1z" class="H"></path><path d="M191 271c3-5 5-10 7-15l1 3c-2 3-3 7-5 10-1 1-1 1-1 3 1 0 1 1 2 1h1 0c-1 1-2 1-3 3l-1 2h0c0 1-2 2-3 3l-1-1 3-3v-1c-1 0-2 1-2 1l-2-2 4-4z" class="b"></path><path d="M191 271h1c0 2-2 3-2 3 0 1 1 1 1 1h0v1c-1 0-2 1-2 1l-2-2 4-4z" class="f"></path><path d="M577 331l1 1h0c-1 1-1 2-2 2v1h1v2l1 3c1 1 1 2 2 2l1 1c1 0 1 0 2-1h2l-2 1v2h0 0l1 2h-1l1 1c-1 0-2 1-3 1-2 0-3-2-5-3l1-2c-1 0-1-1-2-2s-1-2 0-3v-4c0-2 1-3 2-4z" class="G"></path><path d="M580 342l1 1c1 0 1 0 2-1h2l-2 1v2h0 0l1 2h-1l-3-1 1-1h0l-1-1h2v-1h-1l-1-1z" class="C"></path><path d="M577 331l1 1h0c-1 1-1 2-2 2v1c0 3-1 5 0 7s3 3 4 4l3 1 1 1c-1 0-2 1-3 1-2 0-3-2-5-3l1-2c-1 0-1-1-2-2s-1-2 0-3v-4c0-2 1-3 2-4z" class="P"></path><path d="M106 432h2v1c3 1 4 3 6 5l1 1v1l4 5h0 0c-1 0-2-1-2-2l-2-2v1h-1-1c-1-1-1-1-2-1h0c1 1 2 1 2 2h0c-1-1-2-1-3-2h0c-1 0-2-1-3-2s-1-1-1-2h1v-1h-1v-2c-1 0-2-1-3-2l1-1 1 1h1z" class="X"></path><path d="M106 434c-1 0-2-1-3-2l1-1 1 1 5 3c-1 0-1 0-2-1v1h0c-1 0-2 0-2-1z" class="H"></path><path d="M107 436c1 0 2 1 2 1l5 4c-1 0-3-1-4-1v1h0c-1 0-2-1-3-2s-1-1-1-2h1v-1z" class="D"></path><path d="M582 322h-1 0c1-2 2-3 3-4 3 1 5 1 7 2 3 0 6 1 8 3l-1 1h0v3c-1 0-4-2-5-3-4-1-7-2-11-2z" class="C"></path><path d="M593 324v-1l2 1c1 0 2 1 3 1v-1h0v3c-1 0-4-2-5-3z" class="O"></path><path d="M582 322h-1 0c1-2 2-3 3-4 3 1 5 1 7 2 3 0 6 1 8 3l-1 1h0 0c-1-2-4-3-6-3l-1-1h-1-6c-1 1-1 2-2 2h0z" class="I"></path><path d="M145 280h1c2 0 3 1 4 3v4c-1 1-4 2-5 2-1-1-3-1-5-1l-6-6c-3-1-7-2-9-4 0-1 0-1 1-1 2 1 5 3 7 4l4 3c2 2 4 3 8 3v-1-1l-1-1c0-2 1-2 1-4z" class="E"></path><path d="M145 287v-1-1l-1-1c0-2 1-2 1-4l1 1c1 0 1 0 2 1s1 1 1 2 0 2-1 2c-1 1-2 1-3 1h0z" class="H"></path><path d="M266 663h0c0 1-1 1-1 1-3 2-3 5-4 7v1c0 1 1 4 2 5v1c2 2 7 4 10 5h0 2c12 3 28-2 38-9 3-1 6-3 8-6l2 2h-1v1h-1 0c-1 1-1 1-1 2l-2 2h-1v-2l-1 1c-3 1-6 3-10 5-9 4-23 8-33 5-5-2-9-3-11-8-2-2-2-5-1-7 1-3 2-5 5-6z" class="c"></path><path d="M513 453v1c-3 6-7 11-11 15-2 2-5 4-7 6 1 1 1 1 1 2l1 1-1 1c-1-1-1-2-3-1-6 6-14 11-21 16l-1-1c5-3 11-7 16-11 2-2 3-4 5-6 3-2 5-4 7-6 6-5 10-11 14-17z"></path><path d="M576 534c1-1 1-1 2-1s1 0 2 1c6 3 11 5 18 5h2c-2 0-2 0-4 1h-1v1h1c-6 0-11 0-16-3-2-1-3-2-4-4z" class="Z"></path><path d="M582 536c2 1 6 2 7 3 0 1-2 0-2 0-2 0-4-1-5-1v-2z" class="B"></path><path d="M576 534c1-1 1-1 2-1s1 0 2 1h0l2 2v2h0-1c-2-1-3-2-4-4h-1z" class="X"></path><path d="M177 301c0 1 1 1 1 2v1h1c1 0 2-1 2 0 1-1 2-2 3-2l1 1h0 2c-2 2-5 3-7 6h0v1l-3 4-3 3h0c0-4 1-5 3-7-1 0-3 2-4 2 1-1 3-3 3-5v-3-1l1-2z" class="c"></path><path d="M592 424l14-6h2 1c3-2 6 0 8 1v1c2 1 3 2 4 3h-1c-2-1-4-3-6-4h-2-1-3 0c-1 1-3 1-4 1 0 1 0 1 1 1h0-2c-2 1-4 3-5 5l-2-1-1 3-2 1c-1-1-1-2-1-3h0-1-1l-1-1 3-1z" class="R"></path><path d="M591 426c0-1 1-1 1-1 1 0 1 0 2-1h2v1l-1 3-2 1c-1-1-1-2-1-3h0-1z" class="L"></path><path d="M607 485c2 0 4 1 6 1 8 1 14 0 21-3l1 1-3 2v1c-4 2-9 2-13 2-2 0-4 1-5 0-3-1-5-2-7-4z" class="E"></path><path d="M456 489l3-4h0l1 1c0 1-1 1-1 2v1 1h0c1 0 2-1 2-2l1-1c1 1 1 1 2 1v2c0 1-1 1-2 2h0v1h0c-2 0-3 1-5 2-1 1-3 1-4 1s-1 0-1-1v1h-3l2-2c1-2 3-3 5-5z" class="U"></path><path d="M456 489l3-4h0l1 1c0 1-1 1-1 2l-6 6-1 1v1h-3l2-2c1-2 3-3 5-5z" class="B"></path><path d="M451 494h2l-1 1v1h-3l2-2z" class="O"></path><path d="M137 537c1 1 1 3 0 4-2 2-2 4-4 5l-1 1-1 2c1-1 2-2 3-2l3-3c2 0 3-2 4-3l1 1c-2 1-3 3-4 5l-1 1-3 3c0-1 1-2 2-3l2-3h0c-1 0-1 1-2 1h0l-1 1h-1c0 1 0 1-1 1h1l-8 8c-1 0-1 1-2 2v-1c0-1 1-2 1-2v-1h-1l1-2 4-3c1 0 2-2 2-3 1-2 2-4 4-6 1-1 1-2 2-3z" class="G"></path><path d="M129 549l1 1c0 1-2 3-3 4h0l-1 1h-1v-1h-1l1-2 4-3z" class="H"></path><path d="M65 318c-1 3-1 8-1 12 1 4 0 8 1 12v2c0-1 0-2 1-3v-1h-1 0v-1-2c1-1 0-4 1-7v-4c0-3 1-5 2-7 0-1 0-2 1-4l1 1-2 6c-1 5 0 9-1 14 0 1 0 1-1 2v8c1 3 0 5 1 7v2c-3-8-3-17-3-26l-1 2v-9c1-1 1-3 1-4h1z" class="B"></path><path d="M595 338s1 0 1 1c1 2 1 3 1 5v4c0 1-1 1-1 2v1c-1 0-1 0-1 1h-1v1h0-2l-3-1h3 0l-1-1c1-1 2-1 3-1s1-1 2-1c-1-1-2 0-3 0h-2-2-1-1l-3-1-1-1h1l-1-2h0 0c4 1 7 0 10-1 1 0 2-1 3-2 0-1-1-2-1-4z" class="E"></path><path d="M583 345h0c4 1 7 0 10-1l1 2v1c-2 0-3 0-5-1h-5c4 1 6 2 10 2l-1 1h0-2-2-1-1l-3-1-1-1h1l-1-2h0z" class="B"></path><path d="M119 394c-1-3-2-6-3-10-1-1-1-2-2-2v-1-1c4 4 4 9 6 13s5 9 9 11v1 2h1c-1 1-1 1-1 2h0l-2-2c-1 0-2-1-4-2-2-2-5-5-7-8v-1-1l1 1h0c0 1 1 2 2 3l3 3v-2c-1-2-3-3-3-5v-1z" class="Q"></path><path d="M119 394l3 5c1 2 3 3 4 4s2 3 2 4l-3-3c-1-1-2-1-3-2v-2c-1-2-3-3-3-5v-1z" class="G"></path><path d="M92 402c2 0 4 1 6 3 1 0 1 0 2 1 1 0 1 1 2 2l6 3 5 5c-6-2-10-4-15-7h-1-1l-3-2-1-1h-1c-1-1-3-1-5-1l1-1c2 0 3 0 5 1v1l1-1-1-3z" class="X"></path><path d="M92 402c2 0 4 1 6 3 1 0 1 0 2 1-1 1-1 1-2 1h0l-3-3c-1 1-1 1-1 2 1 1 3 2 4 3h-1-1l-3-2-1-1h-1c-1-1-3-1-5-1l1-1c2 0 3 0 5 1v1l1-1-1-3z" class="B"></path><path d="M133 601l1 1c3 2 5 4 8 5 2 1 4 2 5 3 2 1 4 3 6 3 1 0 2 1 3 1l1 1c0 2 0 2-1 2v1l-6-2h-1c-1 0-1-1-1-1h0c-2-1-3-2-4-3l-12-11h1z" class="V"></path><path d="M153 614h3l1 1c0 2 0 2-1 2h-2l-2-1c0-1 0-2 1-2z" class="U"></path><path d="M152 616c-3-1-6-3-9-6 3 1 7 3 10 4-1 0-1 1-1 2z" class="B"></path><path d="M132 601h0c-4-4-6-11-7-17 0-5 1-9 3-14 4-8 8-14 14-21 1-2 4-5 6-6l1-1 5-3v1l-5 3-5 5c-2 2-4 4-6 7s-4 5-6 8c-4 10-7 19-4 30 1 3 3 5 5 8h-1z" class="Q"></path><defs><linearGradient id="C" x1="553.609" y1="581.46" x2="548.973" y2="588.399" xlink:href="#B"><stop offset="0" stop-color="#c7c9cb"></stop><stop offset="1" stop-color="#eceae9"></stop></linearGradient></defs><path fill="url(#C)" d="M550 575l-1-3-1-4-1-3h0 0c2 3 3 7 4 10l2 7s1 1 1 2l1 1h0v2 1c1 0 1 1 1 2h0v3l-1 1v-1h0v-1c0-2 0-3-1-4v-1-2h-1v1 2h1c0 2 0 5-1 7h0v1h-1s0-1-1-2h1v-2h-1c0-1 0-2-1-3v1h0v1c-1 1-1 2-2 2l1-2v-1h0v-1c1-5 1-10 1-14z"></path><path d="M122 266c1-1 3-1 5-1 0 0 0 1 1 1h0 2c1 1 3 2 4 2l2 2c-1-1-2-1-2-1h-1v1s1 1 1 2h0v1l-1 1c0 1 0 1-1 2-1-1-2-2-4-3 0 0-1-1-2-1h-1l-1-1c-1 0-1 0-2-1 0 0-1-1-2-1l1-1c1-1 1-1 1-2z" class="M"></path><path d="M122 270s-1-1-2-1l1-1h1c1 1 1 0 2 1h1 1 0 1c0 1 1 1 1 1l2 1 1 1h1c1 1 1 2 1 2 0 1 0 1-1 2-1-1-2-2-4-3 0 0-1-1-2-1h-1l-1-1c-1 0-1 0-2-1z" class="X"></path><path d="M165 523l2 1c1 2 1 2 3 3l1 1h0v2h0-4l-8 3c-1 0-1 0-1 1l-1-1c-1 0-2 1-3 2-4 2-8 5-11 8l-17 18v-1h0l1-1c2-3 5-5 7-8l3-3 1-1c1-2 2-4 4-5 3-3 9-8 13-9 1 0 1 0 2-1s4-1 6-2h1l1-1h1l-1-2v-3-1z" class="J"></path><path d="M171 528h0v2h0-4c1-1 2-2 4-2z" class="I"></path><path d="M191 522l2 3c1 0 3-1 5-1v2l-2 2c-2 0-3-1-5 0-1 0-2 0-4 1l-12 2c-1 0-3 1-4 1 0-1 1-1 1-1 1-1 2 0 3-1s2 0 4 0c0-1 0-1 1-1h2c1-1 1 0 2 0 1-1 1-1 2-1h2v-1h1 1 0 5v-1h-1c0 1 0 0-1 0-1 1-3 1-4 1h-2c-1 1-1 0-2 0-1 1-1 1-2 1h-3v1l-1-1c0 1-1 1-1 1h-1-1c0 1-1 1-2 1h0-2-1 0v-2c1 0 2 0 3-1h0 0 0 3l1-1h3c1-1 2-1 3-1h1 2v-1c2 1 3 1 4 0v-2z" class="E"></path><path d="M174 527h0l1 2-4 1v-2c1 0 2 0 3-1z" class="Q"></path><path d="M187 525l1 1h-1l-10 2c-1 0-2 1-2 1l-1-2h0 0 3l1-1h3c1-1 2-1 3-1h1 2z" class="R"></path><path d="M174 527h0c2 0 2 0 3 1-1 0-2 1-2 1l-1-2z" class="F"></path><path d="M187 525l1 1h-1-2l-1-1h1 2z" class="I"></path><path d="M633 469h0c1 0 1 0 1-1l3-3v-2l2-2s-1 0-1-1h0 2v-1c0-1 1-2 1-3l1-2v-1c1-1 1-1 1-2 0 0 0-1 1-1v-1l1-2 1-3v-1h1v1 1l-1 1h1c0 1-1 2-1 3v1c-1 0 0 0-1 1 0 0 0 1-1 1v1s0 1-1 1c-1 1-1 3-2 4 0 2-1 3-2 5-2 3-3 5-6 8-3 2-7 4-11 6-1 0-3 1-5 2h-7l2-1c2-1 4-1 6-1 1-1 1-1 2-1l2-1h0c1-1 1-1 2-1l1-1c2-1 2-3 3-3s2-1 3-2v1l1-1 1 1z" class="E"></path><defs><linearGradient id="D" x1="135.953" y1="464.683" x2="143.151" y2="462.015" xlink:href="#B"><stop offset="0" stop-color="#c8c3c6"></stop><stop offset="1" stop-color="#dee2de"></stop></linearGradient></defs><path fill="url(#D)" d="M129 461h-1c-3-1-5-3-8-2l-1-1h-1v1h0v-3l-1-1-1 1v1c-1 0-1-1-1-2-1 0 0-1 0-1 2-1 6 1 9 2h1 0c5 1 9 4 13 6 2 0 4 1 6 2h0c1 1 4 2 5 3 0 1 0 1 1 2l-3-1h0c-4-2-7-4-11-5-1-1-2-1-3-2-2 0-2-1-4 0z"></path><path d="M588 384c1 2 1 4 0 5v5h0c-1 2-1 5-2 7-1 3-4 8-7 10l-1 2c-1 1-2 1-3 1l1-2v-1l3-3c0-1-1 0-1-1 0-2 0-3 1-4s2-2 2-3c1-2 1-6 2-7h2v3h-1v2c-1 1-2 3-2 4v1h1v-2-1c1 0 1-1 2-1v1h1v-1-1s0-1 1-1v-3h0v-1l1-1c0-1-1-1 0-2v-6z" class="M"></path><path d="M580 403h0v3h-1c0-1 0-2 1-3z" class="d"></path><path d="M576 412l2-3c1 0 1 1 1 2l-1 2c-1 1-2 1-3 1l1-2z" class="P"></path><path d="M622 271l2 1c0 3 0 8 1 11v4l4 9v2 1l1 4 1 1-1 1v-1l-1-1v-1c0-1 0-1-1-2 0-1 0-1-1-2l-1-4v-1h-1c1 1 1 2 1 2-1 0-1 0-1-1h0 0-1l-2-5v-2c-2-5-2-11 0-16z" class="H"></path><path d="M180 328l1 1h0l-5 8c-1 2-3 3-5 5l-3 2-1-1c1-1 2-1 3-2l-1-1c-1 1-2 2-3 2h-1v1c-1-1-1-1-1-2 1-1 2-2 2-3l5-4h2v-1l3-4h1c1 1 1 1 2 1l1-1v-1z" class="i"></path><path d="M177 329c1 1 1 1 2 1l1-1c0 1 0 1-1 2-1 0-1 1-1 1-1 1 0 1-1 1h-4l3-4h1z" class="N"></path><defs><linearGradient id="E" x1="172.881" y1="337.198" x2="164.246" y2="338.028" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#848283"></stop></linearGradient></defs><path fill="url(#E)" d="M171 334h2v-1h4c-1 1-1 0-2 1-2 1-4 4-6 5v1h0c-1 1-2 2-3 2h-1v1c-1-1-1-1-1-2 1-1 2-2 2-3l5-4z"></path><path d="M83 422v1h-1-1-2l-1 1v-2h0c0-1 1-1 1-2h0c-1 0-1-1-2 0h-2l-1-1c1 0 1 0 2-1h1 0 6c1 0 1 0 2 1h2c1 0 0 0 1 1h1c2 0 4 3 6 4 0 1 0 0 0 0l1 1h1c0 1 1 1 1 1l1 1 1 1 2 1c1 1 2 1 3 2l1 1h-1l-1-1-1 1c1 1 2 2 3 2v2l-1-1-3-3c-3-1-6-3-7-5-1-1-2-3-3-3h-1c0-1-1-1-1-2h-1s-1-1-2-1h-1 0-2c-1 0-1 0-1 1h0z" class="C"></path><path d="M571 541c-1-1-2-2-4-3l-4-4c-3-2-4-4-5-7l1-1v1c2 5 5 7 9 10 3 2 6 5 9 6 7 4 16 4 23 1 4-1 9-5 11-9l2-2 1 1c-2 1-3 3-4 5-4 5-11 8-18 9-3 0-8 0-12-1-2 0-4 0-6-1-1-1-1-1-1-2 1 0 1 0 2-1l-4-2z" class="Q"></path><path d="M575 543c2 1 3 3 5 4-2 0-4 0-6-1-1-1-1-1-1-2 1 0 1 0 2-1z" class="D"></path><path d="M107 439c1 1 2 2 3 2h0c1 1 2 1 3 2h0c2 2 4 5 6 6s5 1 7 2v1l1 1c1 0 1 0 2 1h0-1-2s-1 0-2-1h0-2l-3-1h-2l-1-1h-1-1c-1 0-2-1-2-1-3-2-5-4-6-7h0 2v-1c-1-1-1-1-1-2v-1z" class="O"></path><path d="M119 449c2 1 5 1 7 2v1l1 1-5-2h-2s0-1-1-2z" class="H"></path><path d="M107 439c1 1 2 2 3 2h0c1 1 2 1 3 2v2 1h0c-2 0-3-2-5-3h-2 0 2v-1c-1-1-1-1-1-2v-1z" class="F"></path><path d="M110 441c1 1 2 1 3 2v2l-3-2v-1-1h0z" class="J"></path><path d="M559 464c1-1 3 0 4-1h2 1v1s1 1 2 1h1l-1 1h-2l3 1h5-5v2h-3l-1 1h0-3-5c-3-1-8-1-11-1 1 0 3-1 4-1 1-1 3-1 4-2l2-1 3-1z" class="T"></path><path d="M554 466l2-1c1 1 2 1 3 1h2c1 0 4 1 5 0l3 1h-14l-1-1z" class="e"></path><path d="M559 464c1-1 3 0 4-1h2 1v1s1 1 2 1h1l-1 1h-2c-1 1-4 0-5 0h-2c-1 0-2 0-3-1l3-1z" class="B"></path><path d="M559 464c1-1 3 0 4-1h2v1l-1 1-2-1h-1 0v2h-2c-1 0-2 0-3-1l3-1z" class="O"></path><path d="M621 453v1c-1 1-1 3-2 4 0 1 1 1 1 2v-1h1c0 2-2 5-3 7-1 1-2 2-2 4 0 0-2 1-3 1l-1-1c-2 1-4 2-7 3 0-1-1-1-2-2l-4 1c0 1-1 0-2 0v-1h2l6-1c3-1 5-2 7-4 4-3 7-9 9-13z" class="N"></path><path d="M619 458c0 1 1 1 1 2-1 3-3 7-6 8-1 0-2 0-2 1l7-11z" class="R"></path><path d="M620 460v-1h1c0 2-2 5-3 7-1 1-2 2-2 4 0 0-2 1-3 1l-1-1c-2 1-4 2-7 3 0-1-1-1-2-2 3 0 6-1 9-2 0-1 1-1 2-1 3-1 5-5 6-8z" class="L"></path><path d="M108 396v3l2-2 1 5 3 5c2 4 3 8 5 12 1 1 4 2 5 3v1h1v3h0l-2-2-6-4-2-3-2-4c-2 0-3-1-4-2-1 0-1-1-1-1l-1-3 3 2-1-2h1v1c1 1 2 3 3 4 0 1 1 2 2 2h0c-1-1-1-2-2-3v-1l-1-1c0-1-1-2-2-3v-2l-1-1v-1l-1-1v-1h0l-1-1h0-1c1-2 1-2 2-3z" class="M"></path><path d="M107 407l3 2c1 1 2 3 3 4-2 0-3-1-4-2-1 0-1-1-1-1l-1-3z" class="F"></path><path d="M108 399l2-2 1 5 3 5h-2s0-1-1-1c0-1-1-2-1-4 0-1-1-1-1-2-1 0-1 0-1-1z" class="O"></path><path d="M112 407h2c2 4 3 8 5 12 1 1 4 2 5 3v1h1v3h0l-2-2-6-4 1-1-6-12z" class="Q"></path><path d="M169 340l1 1c-1 1-2 1-3 2l1 1 3-2h1 1v1 1c-1 2-3 3-4 4-2 1-4 2-6 2-2 1-5 2-6 2v-1h0c0-1-1-1-1-1 2-2 2-3 4-5 1-1 2-3 4-4 0 1 0 1 1 2v-1h1c1 0 2-1 3-2z" class="N"></path><path d="M162 347l2-1c1 1 2 0 4 0-3 2-5 3-7 4-1-1-1-1-1-2 1-1 1-1 2-1z" class="g"></path><path d="M168 344l3-2h1 1v1c-2 1-4 3-5 3-2 0-3 1-4 0 1-1 2-1 4-2z" class="c"></path><defs><linearGradient id="F" x1="165.596" y1="340.933" x2="164.533" y2="345.985" xlink:href="#B"><stop offset="0" stop-color="#696967"></stop><stop offset="1" stop-color="#7d7b7e"></stop></linearGradient></defs><path fill="url(#F)" d="M169 340l1 1c-1 1-2 1-3 2l1 1c-2 1-3 1-4 2l-2 1c0-1 0-1-1-1 1-2 3-3 4-3v-1h1c1 0 2-1 3-2z"></path><path d="M164 341c0 1 0 1 1 2-1 0-3 1-4 3 1 0 1 0 1 1-1 0-1 0-2 1 0 1 0 1 1 2l-4 1h0c0-1-1-1-1-1 2-2 2-3 4-5 1-1 2-3 4-4z" class="F"></path><path d="M638 495l3-2h1v6c-1 2-3 5-5 6-1 2-3 3-5 4h-1-4l-1-1s0-1-1-1-2-1-2-1v-1h2c2 2 4 2 6 2 0-1 0 0 1-1v-1c-2 0-2 0-3-1 0 0 1 0 1-1h0l3-1 4-4 1-3z" class="Q"></path><path d="M637 505h0l1-2h0c1-1 2-4 3-5l1 1c-1 2-3 5-5 6z" class="R"></path><path d="M633 502l4-4c0 2-1 3-3 5v1h-2l1-1v-1z" class="E"></path><path d="M633 502v1l-1 1h2v1c-1 1-1 1-3 2 0-1 0 0 1-1v-1c-2 0-2 0-3-1 0 0 1 0 1-1h0l3-1z" class="F"></path><path d="M212 225l-2 16v2c-1 1-1 1-1 2-2 1-2 2-3 4h0c-1 1-1 2-1 3-1 0 0 0-1 1 0-2 0-4 1-6h0c-1 1-2 3-2 4h-1c0-1 0-2-1-3 1-3 3-6 4-8l7-15z" class="K"></path><path d="M602 317s0-1-1-1l1 1c0 1 0 1 1 2v1h0v2c-1 0-1-1-1-1-2-3-4-5-6-6-1-1-1 0-1-1l-2-1c-1-1-1-1-2-1-1-1-2-2-3-2 0-2 0-2 1-4h1c2-1 2-1 4-1 0 1 1 1 1 1 1 1 1 0 1 1 1 0 1 0 2 1h1c1 1 2 1 2 2v1c0 2 1 2 0 4l2 2h-1z" class="H"></path><path d="M599 308c1 1 2 1 2 2v1c0 2 1 2 0 4-2-2-4-3-6-5 2 0 3 0 4-2z" class="D"></path><path d="M206 611l1 1v-1c1 1 3 2 4 3 1 0 1 1 1 2v1l1 9h-2c-2-3-3-1-5-2v-2-1c0-1 1-2 1-3l-2-1h-3c-1 0-1-1-2-2h0v-1-1h3v-1c0-1 1-1 2-1h1z" class="H"></path><path d="M206 611l1 1c-1 1-2 1-4 1v-1c0-1 1-1 2-1h1z" class="P"></path><path d="M632 487c5-2 13-6 16-10l2-2h0c-1 6-10 10-15 12-7 4-16 5-24 3l-1-1-1 1v2l-7-5h-1c-1 0-3-2-4-3l1-1 1 1 1-1-1-1h3 2v1 1l3 1c2 2 4 3 7 4 1 1 3 0 5 0 4 0 9 0 13-2z" class="I"></path><path d="M599 482h3 2v1 1c-1 0-3-1-4-1 3 3 6 4 10 6l-1 1v2l-7-5h-1c-1 0-3-2-4-3l1-1 1 1 1-1-1-1z" class="M"></path><path d="M615 501c1-1 1-1 0-3h0c1 0 4 3 5 4 0 0 1 0 1 1 1 1 3 1 4 2h-2v1s1 1 2 1 1 1 1 1l1 1h-1c-2-1-5-2-6-5l-1-1c-1 0-1-1-2-1 0 1 0 1 1 2 4 9 3 20 0 29-3 8-10 16-18 20-7 3-15 3-23 1h0 1c9 1 18 1 26-4 7-4 13-14 14-22l1-6 1-2c0-1 0-3-1-4 0-4-1-7-2-10 0-2-1-3-2-5z" class="I"></path><path d="M453 479c5-5 12-11 12-19 0-5-2-11-6-14l-1-2h0c2 0 4 3 5 4 2 4 4 10 3 14-1 7-8 13-12 17l-8 8c-5 4-10 8-14 13-1 1-2 2-2 3l-1 1-1 2c-1-1-1-1-1-2l-1-1c0-1 2-4 2-6l1 1v3l1-1s2-2 2-3v-3l1 3h0l1-1 1-1c2-1 4-4 7-6l11-10z" class="r"></path><path d="M428 497l1 1v1c0 2-1 3-1 5h0 1l-1 2c-1-1-1-1-1-2l-1-1c0-1 2-4 2-6z" class="i"></path><path d="M607 284c2 4 5 7 7 11 8 15 13 32 11 49 0 5-1 10-3 15h0c-1 3-1 5-2 8-2 5-5 11-8 16v-2c2-3 5-9 7-12 1-3 1-6 2-9l3-12c2-10 0-19-2-29-1-6-3-12-6-17-2-7-6-12-10-18h1z" class="K"></path><path d="M467 467c2-6 3-10 0-16-1-3-3-4-5-7h1c3 2 5 7 7 10h0c1-3 0-5-2-8 2 1 3 3 4 5v2c0 1-1 2 0 4s1 4 1 7c-1 2-2 3-2 5v1h2c-2 1-3 3-4 4h0c-1 1-1 1-3 1v-1h0 0l-2 1-1-2c1-1 2-1 2-2l1-1v-1c1-1 1-1 1-2z"></path><path d="M466 474l5-5v1h2c-2 1-3 3-4 4h0c-1 1-1 1-3 1v-1h0z" class="d"></path><path d="M467 467h1c1-2 2-6 3-8 0-1 0-1 1-2 0 6-1 10-5 15l-1 2-2 1-1-2c1-1 2-1 2-2l1-1v-1c1-1 1-1 1-2z" class="C"></path><path d="M176 337h1c1-1 1-3 3-3v1h0v2l1 1v-1h1l-1 5v1l-1 3c0 1-1 2-2 3l-1-2-2 1c-1 1-2 3-4 3h-1c0-1 0 0 1-1 1 0 1-1 2-2h-1c-2 1-5 2-7 3 0 0-1 0-2 1v-2c2 0 4-1 6-2 1-1 3-2 4-4v-1-1h-1-1c2-2 4-3 5-5z" class="q"></path><path d="M181 338v-1h1l-1 5v1l-1 3c0 1-1 2-2 3l-1-2-2 1h0c0-2 2-3 3-5 1-1 2-3 3-5z" class="K"></path><path d="M181 342v1l-1 3c0 1-1 2-2 3l-1-2h0l4-5zm-5-5h1c1-1 1-3 3-3v1h0v2c-2 2-3 4-5 5l-2 2v-1-1h-1-1c2-2 4-3 5-5z" class="Y"></path><path d="M574 392l3-3c-1 2-1 3-2 4v1 1l1-1-1 4-3 3c-2 2-4 3-5 5l-4 3c1 1 1 1 1 2l1 3h-2c0-2 0-3-1-5l-2-5c-2-2-4-5-7-6-2 0-3-1-5 0l-1-1c1 0 1-1 2-1h0c5 0 9 4 12 8h1c4-3 8-6 10-11 0 3-1 4-3 6h1 0c2-2 4-4 4-7z" class="i"></path><path d="M574 392l3-3c-1 2-1 3-2 4v1 1c0 3-2 4-4 6-1 0-2 0-2 1-1 1-4 4-6 5h0c-1-1-1-1 0-2l2-2c1-1 2-3 4-4h0 1 0c2-2 4-4 4-7z" class="B"></path><path d="M232 646c3 0 7 0 10 1l2 2h4c1 1 2 1 4 2 1 0 3 0 3 1h-1l-7-1c-3-1-7-1-11 0l-7 2h0c0-1 1-2 2-2v-1h-1c-2 1-4 1-6 3l-1-1v-1c-1 1-2 1-3 2h-1c0 1 0 1-1 1h0c0-2 1-2 2-3l3-2c3-2 6-2 9-3z" class="Q"></path><path d="M228 648h8v1s-1 0-1 1l-4 1h0v-1h-1c-2 1-4 1-6 3l-1-1v-1c1-2 3-2 5-3z" class="O"></path><path d="M228 648l2 1c-1 0-1 1-2 1-2 0-3 1-5 2v-1c1-2 3-2 5-3z" class="C"></path><path d="M201 248c1 1 1 2 1 3h1c0-1 1-3 2-4h0c-1 2-1 4-1 6v1c0 2-1 5 0 6 0 2-1 3-1 4h-1v-1h0c-1 1-1 1-1 2v1l-1 1-2 4s-1 2-2 2h0-1c-1 0-1-1-2-1 0-2 0-2 1-3 2-3 3-7 5-10l-1-3 3-6v-2z" class="n"></path><path d="M199 259l1-1h1l-1 4h0v1c0 1 0 1-1 2h0c0 1-2 3-3 4h-2 0c2-3 3-7 5-10z" class="s"></path><path d="M201 248c1 1 1 2 1 3h1c0-1 1-3 2-4h0c-1 2-1 4-1 6v1c0 2-1 5 0 6 0 2-1 3-1 4h-1v-1l1-3-1-1c-1 1-1 2-1 3h-1l1-4h-1l-1 1-1-3 3-6v-2z" class="f"></path><path d="M202 259h0v-3h0c1-1 1-1 1-2h1c0 2-1 5 0 6 0 2-1 3-1 4h-1v-1l1-3-1-1z" class="N"></path><path d="M201 250h0v8h0-1l-1 1-1-3 3-6z" class="l"></path><path d="M654 460v-4h0c1 0 2 0 3 1v4c-1 1-1 2-1 3v1c-1 3-3 5-4 8h0c-1 1-1 2-2 2h0l-2 2c-3 4-11 8-16 10v-1l3-2-1-1c5-1 9-5 12-9 2-2 4-3 5-5s1-4 2-6c1-1 1-2 1-3z" class="B"></path><path d="M654 460l1 1c0 1 0 1-1 2v1c-1 2 0 0-1 1 0 1 0 1-1 2v2l-5 5c-1 2-1 3-2 4-3 3-7 5-10 6l-1-1c5-1 9-5 12-9 2-2 4-3 5-5s1-4 2-6c1-1 1-2 1-3z" class="P"></path><path d="M594 385l1 1h0l2 1-3 6h0c-1 3-2 5-2 8-1 1-2 2-3 4-1 1-2 2-4 3-2 3-5 5-7 7v-2l1-2c3-2 6-7 7-10 1-2 1-5 2-7h0v-5h2v8c1-1 1-2 2-3l1-4h0l1-2v-3z" class="T"></path><path d="M595 386l2 1-3 6h-1l2-7z" class="D"></path><path d="M594 393h0c-1 3-2 5-2 8-1 1-2 2-3 4h0c-1-2 2-7 3-8 0-1 1-2 1-4h1z" class="B"></path><path d="M590 389v8c-1 2-2 8-4 9v-1c2-3 2-7 3-11h-1v-5h2z" class="E"></path><path d="M136 594c0 3 2 5 4 7h1c4 3 13 4 15 10l1 1h-1-2l-1 1c-2 0-4-2-6-3-1-1-3-2-5-3-3-1-5-3-8-5l1-1h1l-3-3v-1c1 0 1-1 1-2 0 1 0 2 1 2 0-1 0-2 1-3z" class="C"></path><path d="M142 607c2 0 3 1 4 1s1 1 2 1c2 1 5 2 8 2l1 1h-1-2l-1 1c-2 0-4-2-6-3-1-1-3-2-5-3z" class="T"></path><path d="M77 497c1-1 3-2 4-3 2-2 4-3 7-4v1c-1 0-1 1-2 1-1 2-3 3-3 4-2 1-3 1-4 2v1c-1 0-2 1-2 2-2 3-4 6-5 9-2 9 0 21 5 29 4 7 10 13 19 15 8 2 16 1 23-1 2-1 3-1 4-2h1l-1 1c-2 1-4 2-7 2-8 3-17 3-26-1-8-4-14-12-17-20-3-9-4-20 1-29l-1-1v-2l3-2 1-2z" class="Z"></path><path d="M77 497v1h0c-1 2-2 5-3 6l-1-1v-2l3-2 1-2z" class="L"></path><path d="M148 595v-1c3 3 5 6 8 8 2 2 4 5 7 5h0c2 2 4 3 6 3v1c5 1 9 1 14 2 2 0 3 0 4-1 3 0 5-1 8-2v1c-2 2-5 2-7 3l-6 1h-9s-2 0-2-1c-2-1-4-1-6-2-4-3-7-6-11-9h-1c-2-1-4-4-5-5 0-1 0-2-1-3h1z" class="F"></path><path d="M147 595h1c1 2 3 4 5 6 0 1 1 1 1 2h-1c-2-1-4-4-5-5 0-1 0-2-1-3z" class="d"></path><path d="M187 612c3 0 5-1 8-2v1c-2 2-5 2-7 3l-6 1c-1 0-7 0-8-1 3 0 5 0 8-1-3 0-7 1-10-1h0l-3-1h0c5 1 9 1 14 2 2 0 3 0 4-1z" class="q"></path><defs><linearGradient id="G" x1="194.533" y1="279.897" x2="190.53" y2="290.674" xlink:href="#B"><stop offset="0" stop-color="#191817"></stop><stop offset="1" stop-color="#313132"></stop></linearGradient></defs><path fill="url(#G)" d="M192 278h2 0v1 1h2 0 2v1h1 1l1 1-4 4-2-1-3 3c-2 1-5 3-7 4-1 0-2 0-2 1h0l-3 1v-1l1-1-1-1c-1 1-1 1-3 1l3-2 1-1 3-2c2-1 4-3 5-5v-1h0c1-1 3-2 3-3z"></path><path d="M180 290h2l2-2 1 1h1 0c-1 1-3 3-5 3l-1-1c-1 1-1 1-3 1l3-2z" class="V"></path><path d="M187 288c0-1 1-1 2-2 1-2 1-4 4-6v1h3 0c-3 3-6 6-9 7z" class="W"></path><path d="M192 278h2 0v1l-1 1c-3 2-3 4-4 6-1 1-2 1-2 2h-3l-2 2h-2l1-1 3-2c2-1 4-3 5-5v-1h0c1-1 3-2 3-3z" class="Y"></path><path d="M53 465l-2-2-1-2-1-2c-3-4-4-9-6-13v-2c0-1 0 0-1-1v-2-1c-1-1-1-2-1-2v-2-1h-1v-2h0v-5h1v5h0v1l1 1v3s0 1 1 1v1h0l1 3v1 2s2 1 2 2v1h1c-1-1-1-1-1-2 0-3 0-7-1-10-1-5 0-11 1-16 0-3 0-6 1-9 1-1 2-3 3-5l2-2h0c-1 2-3 4-3 7h0c-1 1-1 1-1 2v1c-1 3-1 6-2 9s0 10 0 14c1 3 1 7 2 10 0 1 1 1 1 2v1c-1 1 2 4 2 5s0 1 1 2h0c-1 1-1 1-1 2l1 1c1 2 1 3 2 4l2 3 1 1h-1l-1-1c-1-1-2-2-2-3z" class="J"></path><path d="M132 563l1 1v5c0 1 0 3-1 4v3c-1 3-1 5-1 8 0 4 0 9 2 13v1l3 3h-1l-1 1-1-1c-2-3-4-5-5-8-3-11 0-20 4-30z" class="H"></path><path d="M133 569c0 1 0 3-1 4v3c-1 3-1 5-1 8 0 4 0 9 2 13v1l3 3h-1l-1-1-1-1c0-1-1-1-1-2-1-2-2-5-2-7-1-7 0-15 3-21z" class="P"></path><path d="M100 295c3-2 7-3 10-2l1 1h1v1h-1c-2-1-4-2-6-1-1 0-3 1-4 1-2 1-6 4-7 6 0 1 0 1 1 2h0c-1 2-2 2-2 4l-4 5-5 8-1 4 2-1-1 2c-1 3-2 6-2 10 0 0 0 3-1 3v2c0-2 0-5-1-6 0-4 1-7 1-10l1-5 1-3c1-3 3-5 4-7 2-4 3-8 6-11 3 0 5-2 7-3z" class="I"></path><path d="M83 320h1l-1 4v3l-1 1v-1c0-2 0-4 1-7z" class="e"></path><path d="M83 320c1-4 3-7 5-11v1c0 2-1 3-2 5h0c1-1 1-2 2-3h1l-5 8h-1z" class="E"></path><path d="M94 301c0 1 0 1 1 2h0c-1 2-2 2-2 4l-4 5h-1c-1 1-1 2-2 3h0c1-2 2-3 2-5v-1c1-3 3-6 6-8z" class="B"></path><path d="M581 338c1 0 0-1 1-1 0-1 0-1 1-2 1 0 4-1 5-1s2 1 3 1c1 1 2 2 4 3h0c0 2 1 3 1 4-1 1-2 2-3 2-3 1-6 2-10 1h0 0v-2l2-1h-2c-1 1-1 1-2 1l-1-1c-1 0-1-1-2-2 1 0 1 0 1-1h2v-1z" class="H"></path><path d="M581 338l3 3h2c0-1 0-2-1-2v-1l1-1c1 1 1 3 2 4h1l1-2h1c0 1 1 1 0 1 0 1-1 2-2 3-1 0-2-1-3-1h-1-2c-1 1-1 1-2 1l-1-1c-1 0-1-1-2-2 1 0 1 0 1-1h2v-1z" class="M"></path><path d="M42 476c-1 0-2-1-2-2s0-1-1-2v-2c-1-1-2-2-2-3s-1-2-1-4v-1c-1-1 0-2-1-2v-1h1c0 1 0 2 1 3h0v1 1 1l1 1 1 3 1 2c0 1 1 1 1 2l1-1h-1l-1-3-1-3c-1-1-1-2-1-4h-1v-2-1h0 0c1 0 1 0 1 1v2c1 2 2 4 2 6l1 1h0c0 1 1 1 1 2 1-1 0-2 1-2 0 0 2 1 2 2v2c0 1 1 1 1 2 0 0 0 1 1 1 0 2 1 2 2 3 5 4 11 6 16 8 1 0 2 0 3 1h9 0c1-1 2 0 3 0-1 0-2 1-3 1h-7c-3 0-7 0-10-1-3-2-8-4-11-6h-1c-1-1-1-2-2-2-2-1-3-3-4-4h0z" class="E"></path><path d="M592 318v-1c1-1 5 2 7 3 3 3 5 9 6 13 1 2 0 6 0 9s1 7 1 10c0 5 0 10-1 15h0 0l-1 1v-3h-1l1-5h-1c0-2-1-6 0-8v-1h1l1-2c0-2-1-4-1-5-1-2-1-3-1-5l-1-4c-1-3-1-6-4-8v-3h0l1-1c-2-2-5-3-8-3l1-1v-1z" class="Q"></path><path d="M605 349v6h-1v-1l-1-2v-1h1l1-2z" class="L"></path><path d="M603 360c0-2-1-6 0-8l1 2v6h-1z" class="K"></path><path d="M592 318c1 0 2 1 4 2 1 0 2 1 3 3h0c-2-2-5-3-8-3l1-1v-1z" class="H"></path><path d="M598 324c6 6 6 12 6 19v1c-1-2-1-3-1-5l-1-4c-1-3-1-6-4-8v-3h0z" class="j"></path><path d="M629 350c3-12 2-27-1-39-1-5-2-9-3-13-3-7-7-16-5-24 1-3 2-5 5-7 2-1 4-1 7-1 1 1 3 2 3 4 1 1 0 1 0 2-1 1-1 2-3 2 0 0-1 0-1-1v-2h0c0 1 1 2 1 2h1l1-1-1-1c-2 0-1-1-3-2 0 0-3 1-4 1l-2 2-2-1c-2 5-2 11 0 16v2l2 5h1c1 4 2 8 3 11 3 12 4 23 4 35-1 3-1 6-2 10h-1z" class="Z"></path><path d="M622 271c2-1 3-2 4-3h6c1 0 1 1 2 2v2l-1-1c-2 0-1-1-3-2 0 0-3 1-4 1l-2 2-2-1z" class="P"></path><defs><linearGradient id="H" x1="607.8" y1="373.776" x2="592.479" y2="383.364" xlink:href="#B"><stop offset="0" stop-color="#b6b7b5"></stop><stop offset="1" stop-color="#eae5ea"></stop></linearGradient></defs><path fill="url(#H)" d="M603 360h1l-1 5h1v3l1-1h0 0c0 3 0 6-1 9s-1 6-1 8c-1 1-1 1-1 2s-1 1-1 2v1h-1c-1 1-1 2-2 3h-1v-1h-1l-1 3-1-1h0l3-6-2-1h0c1-2 2-3 2-5l1-2c2-4 1-7 1-11l1 2h1v-4l1-4v-1l1-1z"></path><path d="M603 365h1v3 2-3c-1 1-1 1-1 2h0v-4z" class="P"></path><path d="M597 391c2-2 3-4 4-7v-2-1c1 1 1 1 1 2v3c0 1-1 1-1 2v1h-1c-1 1-1 2-2 3h-1v-1z" class="D"></path><path d="M603 360h1l-1 5v4l-2 3v-2-4l1-4v-1l1-1z" class="F"></path><path d="M599 368l1 2h1v2 3c0 2 0 4-1 6l-4 10-1 3-1-1h0l3-6-2-1h0c1-2 2-3 2-5l1-2c2-4 1-7 1-11z" class="e"></path><path d="M598 379h1c0 3-2 5-2 7v1l-2-1h0c1-2 2-3 2-5l1-2z" class="E"></path><path d="M601 487h1l7 5c6 5 13 10 21 11h0c0 1-1 1-1 1 1 1 1 1 3 1v1c-1 1-1 0-1 1-2 0-4 0-6-2-1-1-3-1-4-2 0-1-1-1-1-1-1-1-4-4-5-4h0c1 2 1 2 0 3 1 2 2 3 2 5l-1 1 1 4h0c-1 0-1-1-1-1l-1-1v3l1 1c0 2 0 4-1 6 0-1 0-1-1-2l-1 3h0c-1 1-2 3-4 4v-1h1c2-2 3-4 3-6v-4c0-1 1-1 1-2 0-6-1-11-5-15-1-2-2-3-3-4-1-2-3-3-5-5z" class="K"></path><path d="M615 512h0l1 1c0 2 0 4-1 6 0-1 0-1-1-2l1-5z" class="J"></path><path d="M615 512c0-4 0-8-2-12 0-1-1-3-2-4v-2c1 1 2 2 2 4l2 3c1 2 2 3 2 5l-1 1 1 4h0c-1 0-1-1-1-1l-1-1v3h0z" class="L"></path><path d="M540 512l4 1v2 1c0 1 0 2 1 3v8c0 2 1 4 0 6-1-2-3-3-5-4v-1c-1-1-2-2-2-4v-1l-2 1h0c0-2-1-3-1-4v-1c-1-1-2-2-2-4v-1l1 1h1 1 0c0-1-1-2-1-3h0c1 0 2 1 3 1h1l1-1z" class="P"></path><path d="M543 524h0c0 2 0 3 1 5v1h1v-3c0 2 1 4 0 6-1-2-3-3-5-4v-1l2 1 1-1v-4z" class="E"></path><path d="M538 524c2-1 2-1 4-1l1 1v4l-1 1-2-1c-1-1-2-2-2-4z" class="U"></path><path d="M539 518l1 1h2v3s-1 0-1 1h1c-2 0-2 0-4 1v-1l-2 1h0c0-2-1-3-1-4 1-1 1-1 2-1l1 1h0l1-2z" class="d"></path><path d="M539 518l1 1 1 1c-1 1-1 2-2 2l-1 1-1-1v-1c0-1 0-1 1-1h0l1-2z" class="U"></path><path d="M534 515h1 1 0c0-1-1-2-1-3h0c1 0 2 1 3 1h1c1 0 1 0 2 1h2c1 1 0 3 0 4l-1 1h-2l-1-1-1 2h0l-1-1c-1 0-1 0-2 1v-1c-1-1-2-2-2-4v-1l1 1z" class="G"></path><path d="M533 515v-1l1 1 3 2h1v-2l1-1c2 2-1 4 4 4l-1 1h-2l-1-1-1 2h0l-1-1c-1 0-1 0-2 1v-1c-1-1-2-2-2-4z" class="O"></path><path d="M535 519c0-1 0-1-1-2l1-1 1 1c1 1 1 1 3 1h0l-1 2h0l-1-1c-1 0-1 0-2 1v-1z" class="H"></path><path d="M433 651h0c3 0 3-2 4-3 2 0 4-1 6-2l1 1c3 0 6-1 9-1 2 0 5 1 7 1l6 1c3 1 6 2 9 4 2 1 4 4 4 6 1 2 0 3-1 5l-2 2c1-3 4-5 2-9-1-2-4-4-6-4v1h-1 0l-1 1 1 1-1 1h0l-1-1c-1-3-3-3-6-4h-1c2 1 4 2 4 4-1-1-3-2-4-3-2-1-4-1-6-2-3-1-6-1-10-1-2 0-5 1-8 2l-8 1c1-1 2-1 3-1z" class="i"></path><path d="M446 649c3-1 5-1 7-1 0-1 1 0 1 0l1-1h0l1 2v1c-3-1-6-1-10-1z" class="Z"></path><path d="M462 651h0l-5-4c2 1 4 2 6 2h1c3 1 6 1 8 3v1h-1 0l-1 1 1 1-1 1h0l-1-1c-1-3-3-3-6-4h-1z" class="O"></path><path d="M464 649c3 1 6 1 8 3v1h-1 0l-1 1 1 1-1-2c-2-1-3-2-5-3h-1v-1z" class="H"></path><path d="M416 650h0c1 1 1 1 2 1 1 1 2 0 3 0h3c-1 1-2 1-2 3h1v1c2 1 3 0 5 0h1l9 1h1 5c2 0 4 1 5 3v1c-2 1-3 0-5-1-1 0-2-2-3-2h-2l-1 1 1 1c-1 0-1 0-2 1h0c-1 0-2 1-3 1h0l-10-2h-1-1v1h0-3c0-1 1-1 1-1l1-1h-1-3-2-3c2-2 3-5 4-8z" class="F"></path><path d="M430 659c1-1 2-1 3-1s3 1 4 2c-1 0-2 1-3 1h0c-1-1-2-1-4-2z" class="G"></path><path d="M424 657c2 0 4 0 6 1h-3 0c1 1 2 1 3 1 2 1 3 1 4 2l-10-2h-1-1v1h0-3c0-1 1-1 1-1l1-1h-1-3v-1c2 0 4 1 7 0z" class="Z"></path><path d="M429 655l9 1h1c-3 1-5 0-7 2h-2c-2-1-4-1-6-1l-1-1h2l3-1h1z" class="h"></path><path d="M416 650h0c1 1 1 1 2 1 1 1 2 0 3 0h3c-1 1-2 1-2 3h1v1c2 1 3 0 5 0l-3 1h-2l1 1c-3 1-5 0-7 0v1h-2-3c2-2 3-5 4-8z" class="J"></path><path d="M424 651c-1 1-2 1-2 3h-5v-1h1c1-1 1-1 3-2h3z" class="F"></path><path d="M417 657h-2v-1h1 9-2l1 1c-3 1-5 0-7 0z" class="b"></path><path d="M416 650h0c1 1 1 1 2 1 1 1 2 0 3 0-2 1-2 1-3 2h-1v1c0 1-1 1-1 2h-1v1h2v1h-2-3c2-2 3-5 4-8z" class="S"></path><defs><linearGradient id="I" x1="445.946" y1="496.407" x2="435.997" y2="503.826" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#I)" d="M464 475l2-1h0 0v1c2 0 2 0 3-1v2s-1 1-2 1l-2 2c-1 0-2 1-3 2l-3 4-3 4c-2 2-4 3-5 5l-2 2-2 1-3 3h0c-2 2-5 3-5 5l-1 1h-1v-1h0c-1 0-2 1-2 2-1 0-2 2-2 3l-6 6s0 1-1 1v-1h0c1-2 2-3 3-5 3-4 7-8 10-12 4-3 8-6 12-10s8-10 13-14z"></path><path d="M469 474v2s-1 1-2 1l-2 2c-1 0-2 1-3 2l-3 4-3 4c-2 0-2 1-3 2-2 1-4 3-6 5v-1c0-1 3-3 4-5 2-1 3-3 5-5 1-1 3-2 4-4l1-1c1-2 3-4 5-6v1c2 0 2 0 3-1z" class="Q"></path><path d="M138 452c-1-1-3-2-4-4l-6-6c-4-4-10-8-15-11-2-1-4-2-6-4-2-1-5-3-7-4s-3-1-5-2c-1 0-2 0-3-1h0 2c3 0 6 2 9 3 11 6 22 14 31 23 1 2 2 3 4 5s4 5 6 6c1 2 3 3 5 4l1-1 6 7c0 1-1 1-1 1v1 1l-5-1c-1-1-1-1-1-2-1-1-4-2-5-3h0v-2c-1-2-1-2-2-3l-2-1c-2-2-2-3-2-6z" class="R"></path><path d="M138 452l17 16v1 1l-5-1c-1-1-1-1-1-2-1-1-4-2-5-3h0v-2c-1-2-1-2-2-3l-2-1c-2-2-2-3-2-6z" class="U"></path><path d="M110 327l2-1c1 0 1 0 1-1h1v1c1 1 3 2 5 2 1 1 4 3 4 4 1 0 1 1 1 2 1-1 1-1 3 0h0c0 1-1 1-1 1-1 0-1 0-2 1 0 1 0 2-1 3 0 0 1 0 1 1-1 1-1 0-2 1-1 2-2 5-4 7-2 1-3 3-6 3l-3 1c-1-1-2-1-3-2h-3l-2 1v-1l1-1v-1c0-1 0-1 1-2 1 0 1 1 2 1h1c2 0 5 1 7 0s3-3 4-4l1-1s0-1 1-2v-1-1l-1-1v-2h1c0-1-1-2-1-3-1-1-2-3-4-3v-1c-1 0-2 0-4-1z" class="E"></path><path d="M103 350v-1h0 3c1 1 1 1 2 1 3 0 7 0 10-3 1-1 2-4 3-6 0-1 1-2 1-3l1 1s1 0 1 1c-1 1-1 0-2 1-1 2-2 5-4 7-2 1-3 3-6 3l-3 1c-1-1-2-1-3-2h-3z" class="R"></path><path d="M110 327l2-1c1 0 1 0 1-1h1v1c1 1 3 2 5 2l-1 1 2 2c0 2 1 6-1 8v1-1-1l-1-1v-2h1c0-1-1-2-1-3-1-1-2-3-4-3v-1c-1 0-2 0-4-1z" class="X"></path><path d="M51 451c-2-9-3-18 0-27 1-2 2-5 4-6h2 0v1 2h-1v-2c-2 1-3 4-4 5-1 5-2 10-1 14 0 8 1 14 4 20v1c1 2 2 3 2 6l4 4c1 1 2 1 3 2s2 3 3 4h1c1 1 3 2 4 2 6 3 12 3 18 2l8-2 1 1h-2c-2 1-4 2-6 2h0c-1 0-3 0-4 1h-7l-2-1h-2l-9-4-6-3c1 1 2 2 2 4l-3-3c1-1 1 0 1-1h-1c-1-1-2-3-4-4h1l-1-1-2-3c-1-1-1-2-2-4l-1-1c0-1 0-1 1-2h0c-1-1-1-1-1-2s-3-4-2-5h0s0 1 1 1c0 2 1 2 2 4v1l1 1c0-1 0-1-1-1v-1h0v-1c-1 0-1 0-1-1v-2h0v-1z" class="T"></path><path d="M49 451h0s0 1 1 1c0 2 1 2 2 4v1l1 1c0-1 0-1-1-1v-1h0v-1c-1 0-1 0-1-1v-2h0v-1s1 1 1 2c0 3 2 5 3 8 0 1 1 3 1 5l5 5h0v2c1 1 2 2 2 4l-3-3c1-1 1 0 1-1h-1c-1-1-2-3-4-4h1l-1-1-2-3c-1-1-1-2-2-4l-1-1c0-1 0-1 1-2h0c-1-1-1-1-1-2s-3-4-2-5z" class="D"></path><path d="M192 288l3-3 2 1-3 3c0 1-3 4-2 4h1v1h-1v1h0 4l-1 2c2-1 3-2 4-2-1 1-2 2-4 3l-1 1-2 1-3 2s-1 1-2 1h-2 0l-1-1c-1 0-2 1-3 2 0-1-1 0-2 0h-1v-1c0-1-1-1-1-2h0l3-3h-1c0-2 2-3 3-4h1v-1h0c0-1 1-1 2-1 2-1 5-3 7-4z" class="S"></path><path d="M195 297l-1 1-4 1h0l3-3c-3 1-5 3-8 3 0-1 6-4 7-4h0 4l-1 2z" class="a"></path><path d="M181 300h3c0 1 0 1-1 1v1c2-1 5-1 7-2 0-1 1-1 2-1v1h0l-3 2s-1 1-2 1h-2 0l-1-1c-1 0-2 1-3 2 0-1-1 0-2 0h-1v-1l3-3z" class="N"></path><path d="M192 288l3-3 2 1-3 3c0 1-3 4-2 4h1v1h-1c-1 0-2 0-2 1h-1v-1c-2 0-3 1-4 2s-1 2-2 3c-1 0-1 0-2 1l-3 3c0-1-1-1-1-2h0l3-3h-1c0-2 2-3 3-4h1v-1h0c0-1 1-1 2-1 2-1 5-3 7-4z" class="f"></path><path d="M192 288l3-3 2 1-3 3c0 1-3 4-2 4h1v1h-1c-1 0-2 0-2 1h-1v-1l1-1c-1-1-2 0-3-1l6-3-1-1z" class="b"></path><path d="M163 352c1-1 2-1 2-1 2-1 5-2 7-3h1c-1 1-1 2-2 2-1 1-1 0-1 1h1l-1 1c-1 1-2 1-3 2-2 1-4 2-6 4-1 0-2 1-2 0-2 1-4 2-5 4-5 3-8 9-10 15-1 1-1 1-2 1 0-1 0-2 1-4 1-3 2-6 4-8v-3c1-1 2-1 2-3 1 0 1-1 2-1-1-1-1-1-1-2 1-2 2-3 4-4 1-1 2-1 3-2h0v1c1 0 4-1 6-2v2z" class="c"></path><path d="M157 351v1c1 0 4-1 6-2v2s-1 0-2 1-3 1-5 1l1-1h0-2-1c1-1 2-1 3-2h0z" class="i"></path><path d="M159 358c-2-1-2 1-4 1v-1h1c1-1 1 0 1-1 1 0 2-1 2-1l9-4h2 0c-1 1-2 1-3 2-2 1-4 2-6 4-1 0-2 1-2 0z" class="V"></path><path d="M150 357c1-2 2-3 4-4h1 2 0l-1 1-1 1h2c-1 1-3 2-4 3 0 0 1 0 1 1h0-1 0c-3 2-4 5-6 7v-3c1-1 2-1 2-3 1 0 1-1 2-1-1-1-1-1-1-2z" class="i"></path><path d="M150 357c1-2 2-3 4-4h1 2 0l-1 1-1 1c-1 0-2 1-2 2h0l-2 2c-1-1-1-1-1-2z" class="I"></path><path d="M263 657h2l7 1h3c1 1 2 1 4 1 1 0 1 1 2 0l2 1-1 1c1 1 3 2 4 3 0 1 0 1-1 1-3 0-5-2-7-2h-1c-2 0-5 1-7 2l1 1-1 1v1c-1 1-2 2-2 3-1 1-1 2 0 3l2 2 1 1h-1l1 1h0-1c-1 0-3-1-4-2-1 0-2 0-2-1-1-1-1-2-1-3v-2l1-1s-1-1 0-2v-1c-1 2-2 3-2 5v1c0 1 1 4 1 5h0c-1-1-2-4-2-5v-1c1-2 1-5 4-7 0 0 1 0 1-1h0v-1h2v-1h1c-1 0-3-1-4-1h-3 4l-1-1c-1 0-1 0 0-1l-2-1z" class="X"></path><path d="M272 658h3c1 1 2 1 4 1 1 0 1 1 2 0l2 1-1 1c-3-2-7-1-10-1v-2z" class="Z"></path><path d="M270 665l1 1-1 1v1c-1 1-2 2-2 3l-1-1-1-1c1-2 2-3 4-4z" class="J"></path><path d="M263 657h2l7 1v2h-6l-1-1c-1 0-1 0 0-1l-2-1z" class="K"></path><path d="M266 676l-2-1v-1-4c1-1 0-2 1-4v1 1h0l1 1 1 1 1 1c-1 1-1 2 0 3l2 2 1 1h-1l1 1h0-1c-1 0-3-1-4-2z" class="C"></path><path d="M224 653l-1 1v2 1c-1 0-3 0-3 1-1 0-2 0-2 1h-1v1h2c1 1 2 3 3 4 0 0-3 2-4 3h0-3c-2-1-2-2-3-4v-1-3h-1v2s0 1-1 1h0c-1-2 0-3 1-5l1-3s0-1 1-1v-1c1-2 5-4 7-5 0-1 1-1 1-1 0-1 1-1 1-1h1 1c1-1 1-1 2-1h1c2 1 3 1 5 1v1c-3 1-6 1-9 3l-3 2c-1 1-2 1-2 3h0c1 0 1 0 1-1h1c1-1 2-1 3-2v1l1 1z" class="H"></path><path d="M452 495c0 1 0 1 1 1s3 0 4-1c2-1 3-2 5-2l-2 2v1c2 0 4-1 6-1l1-1h2l2-1 1 1-6 3-2 1-1 1c0 1-1 1-1 2h0c0 1 0 1-1 2h-1-1-1v1h-1c-1 0-4 0-6-1-1 1-2 1-3 1l-6 4h0c0-2-1-1-2-2-1 0 0-1-1-1 0-2 3-3 5-5h0l3-3 2-1h3v-1z" class="N"></path><path d="M462 501c-1 0-2 0-3-1h1c1-2 2-2 4-2l-1 1c0 1-1 1-1 2h0z" class="o"></path><path d="M460 495v1c2 0 4-1 6-1l1-1h2l-10 5c-1-1-2-1-3-1v-1c1-1 2-1 3-1l1-1z" class="H"></path><path d="M452 495c0 1 0 1 1 1s3 0 4-1c2-1 3-2 5-2l-2 2-1 1c-1 0-2 0-3 1v1c1 0 2 0 3 1l-5 2c-1-1-1-1-3 0v-1s1-1 1-2h1-1-2l2-2v-1z" class="B"></path><path d="M447 497l2-1h3l-2 2h2 1-1c0 1-1 2-1 2v1c2-1 2-1 3 0l-3 2c-1 1-2 1-3 1l-6 4h0c0-2-1-1-2-2-1 0 0-1-1-1 0-2 3-3 5-5h0l3-3z" class="P"></path><path d="M451 501c2-1 2-1 3 0l-3 2c-1 1-2 1-3 1l1-1 2-2z" class="Q"></path><path d="M447 497c1 1 1 1 2 1-1 2-2 3-4 4h0v-2h0-1l3-3z" class="e"></path><path d="M106 464c1 1 6 1 8 0h0 1c4 0 7 0 11 1h0l-4 1-3 1v1h-1c-4 0-7 1-11 2h-4 0c-1 1-2 2-2 3-1 0-3 0-4 1 0-1 0-2-1-2h-3 0-4c-1 0-4-1-6-1 0-1 1-1 1-1l-3-3c1 1 3 1 4 2h1c0 1 0 1 1 1 1-1 1 0 2 0v-1h0v-2h1 3 2 1v1-2h2 1c1-1 1-1 2-1s1 0 2-1h3z" class="C"></path><path d="M90 467h3v1c-1 1-2 1-3 1s-1 0-1-2h1z" class="D"></path><path d="M84 470c3 0 5 1 8 1h-3v1c-1 0-4-1-6-1 0-1 1-1 1-1z" class="V"></path><path d="M103 469l7-1c3-1 6-1 9-1v1h-1c-4 0-7 1-11 2h-4v-1z" class="q"></path><path d="M92 471l11-2v1h0c-1 1-2 2-2 3-1 0-3 0-4 1 0-1 0-2-1-2h-3 0-4v-1h3z" class="W"></path><path d="M103 470c-1 1-2 2-2 3-1 0-3 0-4 1 0-1 0-2-1-2 2-1 4-2 7-2z" class="P"></path><path d="M97 282l1 1c2-2 4-3 7-4 4-2 9-3 13-2 2 0 3 1 4 2l1 1c3 2 7 4 9 6-6-2-10-6-16-6v1h0 2 0c3 1 5 4 8 5l1 1h-1c-3-1-5-4-9-4v-1c-5-1-12 2-16 5-2 2-4 3-6 5h-1l-2 2c-1 0-1 0-1-1l-2 1c2-4 5-9 8-12z" class="I"></path><path d="M118 277c2 0 3 1 4 2l1 1-4-1h-6v-1l-1-1c2 0 4 1 6 0z" class="d"></path><path d="M97 282l1 1c2-2 4-3 7-4 4-2 9-3 13-2-2 1-4 0-6 0l1 1v1c-1 0-3 0-4 1-4 0-7 2-9 5v1l-6 6-2 2c-1 0-1 0-1-1l-2 1c2-4 5-9 8-12z" class="F"></path><path d="M112 277l1 1v1c-1 0-3 0-4 1l-2-1h0l2-1c1 0 2 0 3-1z" class="G"></path><path d="M107 279h0l2 1c-4 0-7 2-9 5v1l-6 6-2 2c-1 0-1 0-1-1 4-3 6-9 11-12 1-1 2-1 3-1l2-1z" class="B"></path><path d="M115 399v-2h1c2 3 5 6 7 8 2 1 3 2 4 2l2 2c-1 1-1 3-2 5-1 3-2 6-2 9h-1v-1c-1-1-4-2-5-3-2-4-3-8-5-12l-3-5h2 1c0-2 0-2-1-3 0 0 1-1 1-2l1 2z" class="t"></path><path d="M120 415c2 2 3 4 4 7-1-1-4-2-5-3l1-1c-1-1-1-2 0-3z" class="J"></path><path d="M114 397l1 2c1 4 1 8 3 12 0 1 1 3 2 4-1 1-1 2 0 3l-1 1c-2-4-3-8-5-12l-3-5h2 1c0-2 0-2-1-3 0 0 1-1 1-2z" class="M"></path><path d="M581 329h1l3 1 3-1c4 1 8 3 10 7 1 1 3 4 3 6h1c1-1 1-2 1-3 0 2 0 3 1 5 0 1 1 3 1 5l-1 2h-1v1c-1 2 0 6 0 8l-1 1v1c0-2 0-4-1-6 0-1-1-3-1-4 0-2-1-4-2-5l-1 1v-4c0-2 0-3-1-5 0-1-1-1-1-1h0c-2-1-3-2-4-3-1 0-2-1-3-1s-4 1-5 1c-1 1-1 1-1 2-1 0 0 1-1 1v1h-2c0 1 0 1-1 1l-1-3v-2h-1v-1c1 0 1-1 2-2h0l-1-1c1-1 3-1 4-2z" class="I"></path><path d="M602 361v-7-4c0-1 0-1 1-2v3 1c-1 2 0 6 0 8l-1 1z" class="T"></path><path d="M603 339c0 2 0 3 1 5 0 1 1 3 1 5l-1 2h-1v-3l-2-6h1c1-1 1-2 1-3z" class="B"></path><path d="M581 329h1l3 1c-2 0-5 2-6 3-1 2-1 3-2 4v-2h-1v-1c1 0 1-1 2-2h0l-1-1c1-1 3-1 4-2zm6 2c1-1 2 0 3 0l-1 2h1c0 1 1 1 1 2-1 0-2-1-3-1s-4 1-5 1c-1 1-1 1-1 2-1 0 0 1-1 1h-1s-1-1-1-2c1-2 1-3 3-4h1c1-1 2-1 4-1z" class="D"></path><path d="M587 331c1-1 2 0 3 0l-1 2h-3l1-2z" class="d"></path><path d="M590 331c4 1 6 3 8 7 3 6 4 12 3 18 0-1-1-3-1-4 0-2-1-4-2-5l-1 1v-4c0-2 0-3-1-5 0-1-1-1-1-1h0c-2-1-3-2-4-3 0-1-1-1-1-2h-1l1-2z" class="M"></path><path d="M606 410h4l1 1c5 1 9 3 13 5 0 1 1 1 1 1 2 1 3 2 5 3h1l1 1h1v-1h1c1 1 1 2 1 3v1h0c-1 1-1 2-2 3h0c-2-2-3-3-5-4l-2-2-1-1h-1-1l-3-2h-2 0-1-1c0 1 1 1 1 1-2-1-5-3-8-1h-1-2l-14 6h-2 0l-1-1c0-2 3-3 4-3h1c1-1 2-1 2-1 2 0 3 0 4-1h0 2l4-2h1v-1h-3l-6 2h-2-1-2c0 1-1 1-2 2v-1c1 0 2-1 4-2 3-1 9-1 12-4v-1h-4c1 0 2 0 3-1z" class="H"></path><path d="M606 410h4l1 1c5 1 9 3 13 5 0 1 1 1 1 1v1l-1-1-4-2h-3 0-2c-7-2-14 0-20 2h-2c0 1-1 1-2 2v-1c1 0 2-1 4-2 3-1 9-1 12-4v-1h-4c1 0 2 0 3-1z" class="M"></path><path d="M647 446l1-3 2-5c0-3 1-6 1-9v-4c1-1 0-2 0-3v-2-1c1 2 1 8 1 10 0 1 0 3-1 4 0 3 0 5-1 8-1 5-3 10-5 14-1 3-2 5-4 7-1 1-2 2-3 4 1 1 1 1 2 1h-1c0 1 0 1-1 1l-1 1 1 1h2c0 1-2 1-2 2l-9 6c-5 2-9 3-15 3v-1c-1 0-2 0-3 1h-7 0l-2 1h-3c-1 0-3-1-4-1-3-1-4-2-5-3-4-1-8-3-12-4v-1c1 1 1 1 2 1l5 1c1 1 2 1 3 1 5 1 10 3 14 3h8 7c2-1 4-2 5-2 4-2 8-4 11-6 3-3 4-5 6-8 1-2 2-3 2-5 1-1 1-3 2-4 1 0 1-1 1-1v-1c1 0 1-1 1-1 1-1 0-1 1-1v-1c0-1 1-2 1-3z" class="F"></path><path d="M590 478l7 1v1h0-2v1c-3-1-4-2-5-3z" class="M"></path><path d="M597 479l7 2-2 1h-3c-1 0-3-1-4-1v-1h2 0v-1z" class="E"></path><path d="M638 466c1 1 1 1 2 1h-1c0 1 0 1-1 1l-1 1 1 1c-1 1-1 1-2 1-2 1-3 3-5 5-2 1-5 2-8 2 5-3 11-5 14-10l1-2z" class="M"></path><path d="M638 470h2c0 1-2 1-2 2l-9 6c-5 2-9 3-15 3v-1c3 0 6-1 9-2 3 0 6-1 8-2 2-2 3-4 5-5 1 0 1 0 2-1z" class="X"></path><path d="M597 348l1-1c1 1 2 3 2 5 0 1 1 3 1 4 1 2 1 4 1 6l-1 4v4h-1l-1-2c0 4 1 7-1 11l-1 2c0 2-1 3-2 5l-1-1v-2-8-6c-1-4-2-9-5-12h0v-1l1 1h1l-1-2c1-1 1-1 3-1v-1h-1 2 0v-1h1c0-1 0-1 1-1v-1c0-1 1-1 1-2z" class="R"></path><path d="M595 360c0-2-1-2-1-4l1 1 1-1v2l1 1c0 2 0 3-1 4 0-1 0-2-1-3z" class="J"></path><path d="M592 353h2c1 1 1 2 2 3l-1 1-1-1c0 2 1 2 1 4-1-2-2-3-3-4h-1c2 2 3 4 3 6 1 2 1 4 1 6-1-4-2-8-5-11h0 1l-1-2c1-1 1-1 3-1v-1h-1z" class="M"></path><path d="M597 381c0-1 1-4 1-6 0-4 0-8-1-11 0-1 0-1 1-2h0l1 2v-1c0 1 1 2 2 3h0v4h-1l-1-2c0 4 1 7-1 11l-1 2z" class="g"></path><path d="M599 363c0 1 1 2 2 3h0v4h-1l-1-2v-4-1z" class="E"></path><path d="M597 348l1-1c1 1 2 3 2 5 0 1 1 3 1 4 1 2 1 4 1 6l-1 4h0c-1-1-2-2-2-3l-1-3c0-2-1-5-2-7l-1-1c0-1 0-1 1-1v-1c0-1 1-1 1-2z" class="G"></path><path d="M596 353h2c1 2 0 5 0 7 0-2-1-5-2-7z" class="M"></path><path d="M603 317c2 1 2 4 3 6 1 5 3 9 3 14h1v-4-1h1l1 13c0 3 1 5 0 7v3c0 4-1 8-2 12-1 6-3 11-5 16h1v1c-1 2-3 4-3 7h1l2-3v2l-2 3 1 1c-2 1-4 3-7 5h-3c-1 1-2 2-3 2 0-3 1-5 2-8l1 1 1-3h1v1h1c1-1 1-2 2-3h1 0 0v-1l1-1c0-1 1-2 2-3v-1c0-1 1-2 1-3 1-1 1-1 1-2v-1-1h1v-1h0c0-1 0-1 1-2h-1-1v-1c1-1 1-1 1-2v-1-2c1 0 1-1 1-2v-2c1-1 0-2 0-2l1-1v-1-4c1-1 1-1 1-2v-2h0v-3l-1 1c1 1 0 3 0 4 0 0-1 0-2 1 0-1 1-1 1-2h0v-1c1-5 0-10-1-15 0-3 1-7-1-9v-2c-1-1-1-1-1-2l-1-4-2-2h1z" class="B"></path><path d="M610 333v-1h1l1 13c0 3 1 5 0 7v3-1h-1v3 1c-1 2-1 4-1 6 0 1-1 1-1 2v3h-1c0-2 0-3 1-5l1-8c0-2 1-4 1-6-1-4-1-8-2-12v-1h1v-4zm-13 61c3-1 4-3 5-6 1-2 2-3 3-5h1v1c-1 2-3 4-3 7h1l2-3v2l-2 3 1 1c-2 1-4 3-7 5h-3c-1 1-2 2-3 2 0-3 1-5 2-8l1 1 1-3h1v1l-1 1 1 1z" class="E"></path><path d="M595 394l1-3h1v1l-1 1 1 1-4 6 1-5 1-1z" class="G"></path><path d="M606 388v2l-2 3 1 1c-2 1-4 3-7 5h-3s0-1 1-1c1-1 2-2 4-3l4-4 2-3z" class="Q"></path><path d="M624 442c1-2 3-5 4-7 0-2 0-5 1-6l1 2c-1 1-1 3 0 5v4 4 4c-1 3-1 6-2 9 0 1 0 2 1 3-1 2-2 6-4 7h-1c-1 0-1 0-1-1-2 1-3 3-4 3h-1l-2 1c0-2 1-3 2-4 1-2 3-5 3-7h-1v1c0-1-1-1-1-2 1-1 1-3 2-4v-1c-1 0 0-2 0-2v-4c1-2 2-4 3-5z" class="G"></path><path d="M624 451c0-1 0-4 1-5 0-1 1-1 1-2 1 1 0 3 0 4-1 4-2 7-3 11l-1-1v-1c0-1 0-1 1-2l1-3v-1z" class="I"></path><path d="M624 442c1-2 3-5 4-7 0-2 0-5 1-6l1 2c-1 1-1 3 0 5v4 4 4c-1 3-1 6-2 9-1 1-1 2-2 3v-1c1-2 2-4 2-6 2-6 0-11 1-17h0c-1 1-3 5-5 6h0z" class="h"></path><path d="M621 447c1 1 2 2 3 4h0v1l-1 3c-1 1-1 1-1 2v1l1 1c-1 1-1 2-2 3-1 2-3 4-3 7l-2 1c0-2 1-3 2-4 1-2 3-5 3-7h-1v1c0-1-1-1-1-2 1-1 1-3 2-4v-1c-1 0 0-2 0-2v-4z" class="P"></path><path d="M623 455l-1-1v-2h2l-1 3z" class="E"></path><path d="M614 303h1c0-1 0-2-1-2 0-1-1-2 0-3 0 1 1 3 2 4h0c3 5 5 11 6 17 2 10 4 19 2 29l-3 12c-1 3-1 6-2 9-2 3-5 9-7 12h-1v1 2l-1 1-2 3-2 2v-2c0-1 1-3 1-4 1-2 0-3 2-5l1-3 1 1h0l1-1 2-2h0c2-5 4-10 5-15 1-3 1-6 1-9 1-2 1-4 1-6 1-7 0-14-1-21l-1-2c0-1 0-3-1-4v-2c0-2-1-4-2-6 0-1 0-2-1-3h0v-1s0-1-1-1v-1z" class="C"></path><path d="M606 388c0-1 1-3 1-4 1-2 0-3 2-5v1c0 2-1 4-2 6h0 2c0-1 0-1 1-2l1-2v2l-1 1-2 3-2 2v-2z" class="L"></path><path d="M500 653h-1c0-1-1-1-1-2l-1-1v-1-1c-1 0-1-1-1-2h0c-1-3-1-8 0-10 0-1 1-2 0-3v-2s0-1-1-1v-1c-1-1-3-4-5-5-1 0-1 0-2-1-1 0-1 0-1-1l-2-2 1-1c2 0 3 0 4 1 2 2 4 2 5 4h1c2 1 4 4 5 5 0 1 1 1 1 2h2v-2c1 1 1 1 1 2s-1 1-2 1h-1-1 0c1 0 1 1 1 1l-1 1c0 2 0 5 1 7v1c0 2 1 3 2 5 1 0 1 1 1 1 1 1 0 0 1 0 1 1 1 2 2 2s1 1 2 1c0 0 1 0 1-1v1h0c0 1-1 1-1 2h1c1 0 2-1 2-2 1 0 0 0 1-1h0 1c-1 2-2 3-4 4h0l-1 1h-6-1c-1-1-1 0-1-1-1 0-2-1-2-1z" class="C"></path><path d="M497 637h1v4l-1 1v-1-3h0v-1zm3 16h0c0-1 0-1-1-2l-1-1v-1l-1-1c0-1 0-1 1-2l1 1c0 1 1 1 1 2h0c1 2 2 3 4 4v-1-1c-1 0-1 0-2-1 0 0-1-1-1-2s0 0-1-1v-1c-1-1 0-2 0-3-1-2-1-4-1-5h1v1 3h0 1v2c1 2 1 3 3 3 1 0 1 1 1 1 1 1 0 0 1 0 1 1 1 2 2 2s1 1 2 1c0 0 1 0 1-1v1h0c0 1-1 1-1 2h1c1 0 2-1 2-2 1 0 0 0 1-1h0 1c-1 2-2 3-4 4h0l-1 1h-6-1c-1-1-1 0-1-1-1 0-2-1-2-1z" class="X"></path><path d="M578 359h-1-1 0c0-1-1-2 0-3 0-1 2-1 4-1v1c3 0 4 0 6 1l1 1 1-1h0v-1h1v1h0c3 3 4 8 5 12v6 8 2 3l-1 2h0l-1 4c-1 1-1 2-2 3v-8h-2c1-1 1-3 0-5l-1-11-1-1v-1s0-1-1-2h0c-1 1-1 1-2 0 0-1 0-2 1-3 0-2-1-2-2-4h0c-1 0-1-1-1-1-1-1-2-1-3-2z" class="G"></path><path d="M578 359h-1-1 0c0-1-1-2 0-3 0-1 2-1 4-1v1h0c0 1 1 1 1 1v1l2 2v1c-1 0-1 0-1-1l-3-1h-1z" class="H"></path><path d="M584 366h1v2c1 0 1 0 2 1v2c1 1 1 2 1 3v3 1c1 1 1 2 1 3v2 2l1-1c0-1-1-4 0-5 1 3 0 7 0 10h-2c1-1 1-3 0-5l-1-11-1-1v-1s0-1-1-2h0c-1 1-1 1-2 0 0-1 0-2 1-3z" class="D"></path><path d="M590 379c0-2 0-4-1-5v-1-1-2l-1-4h0c-1-1-1-2-2-3h0l-3-3 1-1c4 3 5 9 6 13l1 7c0 2 0 14 1 15-1 1-1 2-2 3v-8c0-3 1-7 0-10z" class="O"></path><path d="M591 379c0-1 0-1 1-2h0c-1-3 0-6-1-8v-1-2l-1-1h0l1-1c0 1 0 1 1 2v1s0 1 1 2h1v6 8 2 3l-1 2h0l-1 4c-1-1-1-13-1-15z" class="M"></path><path d="M594 369v6l-1 1v-3c-1-1 0-3 0-4h1z" class="C"></path><path d="M593 390c0-1 0-1-1-1v-1c0-1 0-4 1-5h1v2 3l-1 2h0z" class="O"></path><path d="M105 360c1-1 2-2 4-2v2c-2 2-4 3-5 4l1 2c0-1 1-1 2-2v1 1h0c1 0 1-1 2-1 0-1 1-1 1-2v1l-1 1-1 1c1 1 1 1 2 1l1-1c0-1 0-1 1-1l1-1v3c0 1-1 1-2 1h-1l-1 3c-1 0-1 1-2 1 0 1 1 2 1 2-1 0-1 0-2 1s-2 2-2 3h0c-1 2-1 3-1 5v1 4 1h1v-2-1c1 1 2 2 2 3h-1c0-1 0-1-1-2v2 1s1 0 0 1v3c0 1 0 1 1 2v2l1 1 3 6c0 1 0 1 1 2h-1l1 2-3-2c-1-1-2-3-3-4l-3-6c-1-2-2-5-3-7v-5c0-3-1-6-1-9l1-7c1-2 2-3 1-5 1 0 1-1 2-1 0-2 1-3 2-4h1c0 1 0 1-1 2h0v1l2-2h0z" class="B"></path><path d="M107 372v-1c1-1 1-3 2-3h1l-1 3c-1 0-1 1-2 1z" class="P"></path><path d="M101 377l1 1v2c0 5 0 7 1 11v2h-1v-1c-2-5-2-10-1-15z" class="O"></path><path d="M105 360c1-1 2-2 4-2v2c-2 2-4 3-5 4 0 2-1 3-1 4-1 2-2 3-2 5v-5-1c0-1 1-2 2-3h1c0-1 1-2 2-3h0l-1-1z" class="C"></path><path d="M104 403l1-1s0-1-1-2v-1c0-1-1-1-1-2v-1h0v-1c1 1 1 2 2 4 1 3 3 5 4 8l1 2-3-2c-1-1-2-3-3-4z" class="J"></path><path d="M104 364l1 2c-1 0-1 1-1 2-1 1-1 2-1 3h0 1 0c0-1 0-2 1-3h0v1h0c0 2-2 5-2 7 0 1 0 2-1 3v1-2l-1-1h-1c0-1 0-3 1-4 0-2 1-3 2-5 0-1 1-2 1-4z" class="L"></path><path d="M500 525c5 0 10 2 15 3 2 1 5 1 7 2 3 1 5 1 7 3 1 0 2 0 2 1 1 0 2 1 3 1s2 1 3 2c3 1 6 4 9 6 0 1-1 0 1 1 1 1 4 4 5 6 0 0 1 1 2 1l3 3c0 1 0 1-1 1v1c1 1 1 1 1 2v2h1v2l2 2c2 3 4 7 5 11 2 6 2 15 0 21 0 2-2 4-3 6l-7 9c-1 1-2 3-4 5v-1c1-2 3-4 5-7v-1h0c-1 0-1 1-2 2s-2 3-4 4c-1 1-2 2-2 3h-1l1-1c0-1 1-2 1-3 2-2 4-4 5-6l3-3 2-5c1-1 1-2 1-4 1 0 1-1 1-2s0-2 1-3v1 1 2s0 1-1 2l-1 4v1c-1 1-1 2-2 2v1 1c3-2 6-7 7-10 1-5 1-12-1-17-1-2-2-5-3-7-1-4-3-7-5-10l-1-1v-1c-1-2-12-11-14-11-1-1-2-1-2-1-1-2-5-4-7-4-1-1-3-2-4-2l-4-2 1-1c1 0 2 1 3 1 4 2 7 4 10 6 1 1 2 1 3 2 1-1 1-3 1-4 0 0-1-1-2-1l-1-1-4-3s-1 0-1-1l-9-3c-10-3-19-6-29-7h3l1-1z" class="J"></path><path d="M557 560c0-1-3-4-4-5v-1c0-1-2-2-3-4h1 1c0 1 0 1 1 1h1l3 3c0 1 0 1-1 1v1c1 1 1 1 1 2v2z" class="X"></path><path d="M101 350v1l-1 1 1 1 2-1v1h0l-1 1c-1 2-1 4-2 6 0 2-1 3-1 4 1 2 0 3-1 5l-1 7c0 3 1 6 1 9v5c1 2 2 5 3 7l3 6c1 1 2 3 3 4l1 3s0 1 1 1h-1l-6-3c-1-1-1-2-2-2-1-1-1-1-2-1l1-1c-2-1-4-3-5-4l-4-3v-3l1 1v-1l1-1 1 1v-3l-1-1v-1h0c0-2 0-3-1-4l-1-3 1-1c-1-1-1-1 0-2 0 2 1 4 2 7 1 2 1 3 2 5l1-3-4-12v-3c0-2 0-3 1-4h0c0 1 0 2 1 2 1-1 1-3 1-4 1-7 2-12 6-17z" class="K"></path><path d="M98 398c1-1 0-2 1-3l4 9h0c-2-1-4-2-5-3v-3z" class="C"></path><path d="M92 393l1 1c1 2 3 5 5 7 1 1 3 2 5 3h0s1 1 0 1c-2 0-3-1-4-1-2-1-4-3-5-4l-4-3v-3l1 1v-1l1-1z" class="Q"></path><path d="M90 394l1 1c1 1 2 3 3 5l-4-3v-3z" class="I"></path><path d="M91 379c0 2 1 4 2 7 1 2 1 3 2 5l1-3c1 2 3 5 3 7-1 1 0 2-1 3v3c-2-2-4-5-5-7v-3l-1-1v-1h0c0-2 0-3-1-4l-1-3 1-1c-1-1-1-1 0-2z" class="H"></path><path d="M96 388c1 2 3 5 3 7-1 1 0 2-1 3-1-2-2-4-3-7l1-3z" class="E"></path><path d="M100 352l1 1 2-1v1h0l-1 1c-1 2-1 4-2 6 0 2-1 3-1 4 1 2 0 3-1 5l-1 7c0 3 1 6 1 9-1 0-1 1-1 2h0-1v-5c0-4-1-7 0-11 1-7 0-13 4-19z" class="Q"></path><path d="M100 352l1 1c-1 2-1 4-2 7 0 1-1 2-1 3l-1 7c0 1 0 2-1 3v-2c1-7 0-13 4-19z" class="B"></path><path d="M583 378c1 1 2 1 2 3 0 0 1 0 1 1v1h0v1c1 2 0 6 0 8h0l-1-1v1l-1-2v2l-1 1c-1 1-1 5-2 7 0 1-1 2-2 3s-1 2-1 4c0 1 1 0 1 1l-3 3v1l-1 2-1 2h-2c-2 1-3 5-5 5v-1c0 1-1 1-1 2v-2h-1l-1 2c1-2 0-4 0-5s0-2-1-3h2l-1-3c0-1 0-1-1-2l4-3c1-2 3-3 5-5l3-3 1-4-1 1v-1-1c1-1 1-2 2-4v-1h2c1 0 0-1 2-1v-2h2c-1-1-1-2 0-4l1 1 1-1c-1-1-1-2-2-3z" class="C"></path><path d="M580 393v1c1 0 1 1 0 1h-1c0-1 0-1 1-2zm3-12l1 1h0c-1 2 0 4 0 6h-1 0c-1 0 0-2 0-3-1-1-1-2 0-4z" class="B"></path><path d="M577 388h2c1 0 0-1 2-1-1 3-3 5-5 7l-1 1v-1-1c1-1 1-2 2-4v-1z" class="e"></path><path d="M575 398l1-1c1-1 1-3 2-4 1 1 1 2 1 3-2 3-3 6-4 9s-4 6-5 9l-3 6c0 1-1 1-1 2v-2h-1l-1 2c1-2 0-4 0-5s0-2-1-3h2l-1-3c0-1 0-1-1-2l4-3c1-2 3-3 5-5l3-3z" class="t"></path><path d="M572 401c-1 2-1 3-2 4s-2 1-3 1c1-2 3-3 5-5z" class="U"></path><path d="M565 414h0c1 2 1 4 1 6 1-2 2-4 4-6l-3 6c0 1-1 1-1 2v-2h-1l-1 2c1-2 0-4 0-5s0-2-1-3h2z" class="Z"></path><path d="M353 330c2 1 6-1 8-1h6c7-1 14 1 20 3 2 0 4 1 4 3h0v16c-5-5-11-10-18-13-5-3-10-4-14-6-2 0-4 0-6-1-1 0 0 0-1-1h1z"></path><path d="M591 284c2 1 4 3 6 5-1 1-2 1-3 1 1 1 3 1 4 2l-1 1h-1-1-3v1 1h-1c-2-1-6-2-8-1h0c-2 1-3 2-4 3v1h0c-1 1-1 2-1 2v1 1c-1 0 0 1-1 1v5 1h-1c0 1 0 2-1 3h-2-3v-1c-1 0-2-1-3-1v-2h0c0-2-1-4 0-6v-2c1-1 1-1 1-2 1-1 2-3 3-5 0-1 1-1 1-2h0l2-2 1-1h1c1-1 1-2 2-2l1-1c1 0 2 0 3-1h1 3c1 1 2 0 3 1h1l1-1z" class="U"></path><path d="M591 284c2 1 4 3 6 5-1 1-2 1-3 1-4-1-9-2-12 0-2 0-2 1-4 2-1 1-2 1-3 3v1c-1 0-1 1-2 2h0c0 1-1 2-1 3h0l-1 2h0c0 2 0 4 1 5v1l2 2c1 0 1-1 2-1v-1h0c0 1 0 2-1 3h-2 0c-1-2-2-3-2-4h0l-1-1v-3c1 0 1-1 1-2v-1c1-1 1-4 3-5v-1l3-3v-1c4-3 9-4 14-3v-1l-2-1c-2 0-4-1-5 0h-2-1-1l-2 1h0c-1 1-2 2-3 2h0-1l1-1h1c1-1 1-2 2-2l1-1c1 0 2 0 3-1h1 3c1 1 2 0 3 1h1l1-1z" class="D"></path><path d="M576 309h0v1c-1 0-1 1-2 1l-2-2v-1c-1-1-1-3-1-5h0l1-2h0c0-1 1-2 1-3h0c1-1 1-2 2-2v-1c1-2 2-2 3-3 2-1 2-2 4-2 3-2 8-1 12 0 1 1 3 1 4 2l-1 1h-1-1-3v1 1h-1c-2-1-6-2-8-1h0c-2 1-3 2-4 3v1h0c-1 1-1 2-1 2v1 1c-1 0 0 1-1 1v5 1h-1z" class="U"></path><path d="M578 301c-1-1-1-2 0-2v-2c-1 0-1-1-2-1h0l3-3h0c1-1 1-1 2-1l1-1h2 0c1 0 4-1 5 0h2c2 1 3 1 5 2h-1-3v1 1h-1c-2-1-6-2-8-1h0c-2 1-3 2-4 3v1h0c-1 1-1 2-1 2v1z" class="G"></path><path d="M603 303s1 0 1 1l2 3c1 2 2 3 2 4 1 1 2 2 2 4v-1l1 1v-1-1h0l1 3h1l1 3c1 3 1 7 1 10 1 4 1 8 1 12 2 4 0 9-1 13 0 1 0 3-1 4v1c1 5-1 12-4 17l-1 3c-2 2-1 3-2 5 0 1-1 3-1 4l-2 3h-1c0-3 2-5 3-7v-1h-1c2-5 4-10 5-16 1-4 2-8 2-12v-3c1-2 0-4 0-7l-1-13h-1v1 4h-1c0-5-2-9-3-14-1-2-1-5-3-6l-2-2c1-2 0-2 0-4v-1c0-1-1-1-2-2h-1c-1-1-1-1-2-1 0-1 0 0-1-1 0 0-1 0-1-1 1 0 2 0 3 1h1c1 0 3 2 4 2 1 2 2 3 3 4l3 2v2c1 0 1 1 1 1v1c0 1 1 2 1 2v2l1 1h0v-1c0-1 0-2-1-3 0-1-1-1-1-2s1 0 0-1v-1l-1-1c0-2 0-2-1-4-1-1-2-3-3-5-1 0-1-1-1-2z" class="T"></path><path d="M610 333h-1c0-2-1-4 0-6h0c0-1 0 0 1-1h0v1c0 1 0 3 1 5h0-1v1z" class="R"></path><path d="M616 341c2 4 0 9-1 13 0 1 0 3-1 4v1l-1 2 1-8v-1c1-1 1-2 1-4s0-4 1-7z" class="Q"></path><path d="M594 305c1 0 2 0 3 1h1c1 0 3 2 4 2 0 1-1 2 0 2 1 2 2 2 3 4 0 1 1 3 2 4v1 2c-2-1-2-6-4-6h-1c0 1 0 1 1 2l-2-2c1-2 0-2 0-4v-1c0-1-1-1-2-2h-1c-1-1-1-1-2-1 0-1 0 0-1-1 0 0-1 0-1-1z" class="e"></path><path d="M614 359c1 5-1 12-4 17l-1 3c-2 2-1 3-2 5 0 1-1 3-1 4l-2 3h-1c0-3 2-5 3-7v-1l7-22 1-2z" class="D"></path><path d="M603 303s1 0 1 1l2 3c1 2 2 3 2 4 1 1 2 2 2 4v-1l1 1v-1-1h0l1 3c0 1 1 1 1 2l1 7h0c1 6 2 13 1 19-1 2-1 4-2 6 0-5 0-10-1-14v-1c0-3 0-5-1-7-1-5-3-11-6-15v-1l3 2v2c1 0 1 1 1 1v1c0 1 1 2 1 2v2l1 1h0v-1c0-1 0-2-1-3 0-1-1-1-1-2s1 0 0-1v-1l-1-1c0-2 0-2-1-4-1-1-2-3-3-5-1 0-1-1-1-2z" class="O"></path><path d="M610 315v-1l1 1v-1-1h0l1 3c0 1 1 1 1 2l1 7h-1c-1-2-1-4-1-6-1-1-1-3-2-4zM53 465c0 1 1 2 2 3l1 1c2 1 3 3 4 4h1c0 1 0 0-1 1l3 3c0-2-1-3-2-4l6 3 9 4h2l2 1h7c1-1 3-1 4-1h0c2 0 4-1 6-2h2l-1-1c1 0 1 0 1-1h2l2-1h2v-1c-2 1-3 1-4 0h0-4 0c1-1 3-1 4-1 0-1 1-2 2-3h0 4c4-1 7-2 11-2 0 1 0 2-1 3h1-1c-3 2-5 4-7 4l-4 1-2 1h-2c-1 0-1 0-1 1 1 0 1 0 2-1 0 1-1 1-2 1v1c-2 2-5 2-8 4l1 1s1 0 1-1h2 0c1-1 4-2 6-3l5-2 1-1h2c2-1 3-1 4-1h1 0c-1 1-2 1-3 1-1 1-1 1-2 1h0-1-1v1l-4 1c-2 1-3 2-5 3 0 1-1 1-1 2-2 1-3 2-5 3 0 1-1 1-1 1-2 1-3 2-5 2v-1c-3 1-5 2-7 4-1 1-3 2-4 3l-1 2-2-1 4-3c0-1 1-1 2-2h-1l1-1v-1-1h-3 0s-1 0-2-1h2c1 0 2-1 3-1-1 0-2-1-3 0h0-9c-1-1-2-1-3-1 7 0 14 0 20-4h-2c-5 1-10 1-14-1v-1h-2c-3 0-6-3-8-5-1 0-2-1-3-2s-2-1-3-2v-2-1c0-1-1-1-2-2l2-2z" class="B"></path><path d="M93 485c0-1 0-1 1-1l1 1h0c-1 1 0 1 0 2-1 0-1 1-1 1 0 1-1 1-1 1-1-1 0-1-1-2 1-1 1-1 1-2z" class="L"></path><path d="M103 470h4l-1 1c-1 1-2 1-3 2v1h-1l-1-1c0-1 1-2 2-3h0z" class="O"></path><path d="M67 476l9 4h2-3l-1 1c-1 0-3-2-4-2-2-1-2-2-3-3z" class="L"></path><path d="M57 474l-1-2h0c1 0 1 0 2 1 1 0 2 0 2 1l3 3 3 3c-2-1-3-2-5-3l-1-1-1-1c-1 0-2-1-2-1z" class="X"></path><path d="M53 465c0 1 1 2 2 3l1 1c2 1 3 3 4 4h1c0 1 0 0-1 1 0-1-1-1-2-1-1-1-1-1-2-1h0l1 2h-1c-1-1-2-1-3-2v-2-1c0-1-1-1-2-2l2-2z" class="d"></path><path d="M89 484s1 0 2-1v1h0c-1 1-1 0-1 1l1 1c-2 1-4 2-5 3-1 0-1-1-2-1-2 1-4 2-7 2 0 0-1 0-2-1h2c1 0 2-1 3-1l2-1 7-3z" class="T"></path><path d="M89 484s1 0 2-1v1h0c-1 1-1 0-1 1l1 1c-2 1-4 2-5 3-1 0-1-1-2-1 2-1 4-2 5-4zm18-14c4-1 7-2 11-2 0 1 0 2-1 3h1-1c-2 0-3 0-4 1h-3c-1 0 0 0-1 1h0-2-1v-2l1-1z" class="D"></path><path d="M93 485c0 1 0 1-1 2 1 1 0 1 1 2-2 1-3 2-5 2v-1c-3 1-5 2-7 4-1 1-3 2-4 3l-1 2-2-1 4-3c0-1 1-1 2-2h-1l1-1v-1-1h-3 0c3 0 5-1 7-2 1 0 1 1 2 1 1-1 3-2 5-3l2-1z" class="i"></path><path d="M92 487c1 1 0 1 1 2-2 1-3 2-5 2v-1l4-3z" class="d"></path><path d="M84 488c1 0 1 1 2 1-3 2-6 5-8 6 0-1 1-1 2-2h-1l1-1v-1-1h-3 0c3 0 5-1 7-2z" class="C"></path><path d="M110 327c2 1 3 1 4 1v1c2 0 3 2 4 3 0 1 1 2 1 3h-1v2l1 1v1 1c-1 1-1 2-1 2l-1 1c-1 1-2 3-4 4s-5 0-7 0h-1c-1 0-1-1-2-1-1 1-1 1-1 2v1l-1 1c-4 5-5 10-6 17 0 1 0 3-1 4-1 0-1-1-1-2v-1c-1-1 0-3-1-5-1-1 0-3 0-5v-6l3-9-1-1c0-1 1-2 1-3s1-1 1-2l4-5c0-1 1-2 2-2s1-1 2-2 5 0 6-1z" class="I"></path><path d="M92 358v-6l1 2 1-2-1 5c0 1-1 1-1 1z" class="D"></path><path d="M95 343h1l1 1c-1 3-1 6-3 8l-1 2-1-2 3-9z" class="d"></path><path d="M93 368v-4c0-2 0-5 1-7 1-4 3-8 4-12 1 2 2 2 4 3v1l-1 1c-4 5-5 10-6 17 0 1 0 3-1 4-1 0-1-1-1-2v-1z" class="G"></path><path d="M110 327c2 1 3 1 4 1v1c2 0 3 2 4 3 0 1 1 2 1 3h-1 0l-1-1-1 1h0c0-1-1-2-1-3-2 0-3-1-5-1-1-1-2-1-3-1-4 2-8 6-9 9-1 2-1 3-1 5l-1-1h-1l-1-1c0-1 1-2 1-3s1-1 1-2l4-5c0-1 1-2 2-2s1-1 2-2 5 0 6-1z" class="D"></path><path d="M107 330h-3v-1c3 0 6-1 9 0h0c1 1 3 2 3 3h1v1l1 2-1-1-1 1h0c0-1-1-2-1-3-2 0-3-1-5-1-1-1-2-1-3-1z" class="G"></path><path d="M99 339c2-3 4-6 7-7 2 0 3 0 6 1 0 0 1 0 1 1l-1 1h2 0c1 1 1 2 1 3-1 1-1 2-3 3h-1v1h2c1 0 1 0 2-1 2-2 1-4 1-6l1-1 1 1h0v2l1 1v1 1c-1 1-1 2-1 2l-1 1c-1 1-2 3-4 4s-5 0-7 0h-1c-1 0-1-1-2-1-1 1-1 1-1 2 0-1-1-1-1-1-1-1-4-3-4-5 0-1 1-2 2-3z" class="U"></path><path d="M112 344c1-1 4 0 5-1-1 1-2 3-4 4 0-1-1-2-1-3z" class="d"></path><path d="M112 333s1 0 1 1l-1 1h2 0c1 1 1 2 1 3-1 1-1 2-3 3h-1v1l-3-1h0l-1-1c-1 0-2-1-2-2 1 0 1-1 2-1l1-1c1-1 2-1 3-2 0 0 0-1 1-1zm-13 6l1 1c0 2 2 3 3 3h0l2 1h3c1 1 1 2 2 2h2 0 0l-1-1 1-1c0 1 1 2 1 3-2 1-5 0-7 0h-1c-1 0-1-1-2-1-1 1-1 1-1 2 0-1-1-1-1-1-1-1-4-3-4-5 0-1 1-2 2-3z" class="G"></path><path d="M150 506c1 0 2-1 3-1l1-1 2 1 1-1c1 1 2 1 2 2h0c0 1 0 2 1 3h0c2-1 1 0 3 0v-1-1l2-2c0 1 0 2 1 2-1 1-1 2-1 4 0 1-1 3-2 3l1 1v1 4l1 3v1 3l1 2h-1l-1 1h-1c-2 1-5 1-6 2s-1 1-2 1c-4 1-10 6-13 9l-1-1 6-8-1-1-2 1 1-2v-7h0v-3c1 0 1-1 1-2v-2-1c1-1 1-3 1-4v-1c1-1 2-3 3-5z" class="U"></path><path d="M149 529c1-1 1-2 1-3l1 1v1c-1 1-1 1-2 1z" class="G"></path><path d="M157 504c1 1 2 1 2 2h0-4l1-1 1-1z" class="L"></path><path d="M158 512h3v1c-1 1-2 2-2 3-1 0-2 1-4 1h-1v-1h2c1 0 2-1 3-2l-2-2h1z" class="e"></path><path d="M154 512c1 0 3-1 4 0h0-1l2 2c-1 1-2 2-3 2h-2v1l-1-1c1-2 1-3 1-4z" class="G"></path><path d="M154 512c1 0 3-1 4 0h0-1-1c-1 1-1 1-1 2l-1 1v1 1l-1-1c1-2 1-3 1-4z" class="M"></path><path d="M150 517v-1-1l-1-1c-1-1 0-2 0-3v-1c1-2 3-3 5-4v1c-1 1-2 3-2 4s1 1 2 1c0 1 0 2-1 4l-1-1h-1c-1 1-1 1-1 2z" class="G"></path><path d="M145 524c2 1 2 2 3 3h1c0-1 0-2 1-2 0-1 0-2 1-2v-4h1c0 2 0 3-1 5 0 1 0 1-1 2 0 1 0 2-1 3 0 1-1 3-2 4l-1-1-2 1 1-2v-7z" class="J"></path><path d="M145 531v-1c1 0 1-1 2-1 0-1 1 0 2 0 0 1-1 3-2 4l-1-1-2 1 1-2zm5-25c1 0 2-1 3-1l1-1 2 1-1 1h-1c-2 1-4 2-5 4v1c0 1-1 2 0 3l1 1v1 1c1 1 2 1 2 2h-1v4c-1 0-1 1-1 2-1 0-1 1-1 2h-1c-1-1-1-2-3-3h0v-3c1 0 1-1 1-2v-2-1c1-1 1-3 1-4v-1c1-1 2-3 3-5z" class="D"></path><path d="M146 519c0 1 1 4 0 4 0 1 0 1-1 1v-3c1 0 1-1 1-2z" class="O"></path><path d="M163 507l2-2c0 1 0 2 1 2-1 1-1 2-1 4 0 1-1 3-2 3l1 1v1 4l1 3v1 3l1 2h-1l-1 1h-1 0l-2-3h0c-1-1-2-5-2-6s1-3 2-5v-3c1 0 1-1 1-1 0-2-1-2-2-3 2-1 1 0 3 0v-1-1z" class="L"></path><path d="M164 516v4l1 3v1c-1 0-1-1-2-1v-4c0-1 0-2 1-4v1z" class="R"></path><path d="M163 519v3h-1v-1l-1-1c0-1 1-3 1-4s0-2 1-2h0l1 1c-1 2-1 3-1 4z" class="B"></path><path d="M161 527l1-1c0-1-1-1-1-2v-1h0 1 0c0 2 2 3 3 4l1 2h-1l-1 1h-1 0l-2-3z" class="D"></path><path d="M625 294h0 0c0 1 0 1 1 1 0 0 0-1-1-2h1v1l1 4c1 1 1 1 1 2 1 1 1 1 1 2v1l1 1v1l1-1c0 3 1 6 1 8 1 2 1 5 2 7 2 15 0 29-5 43-1 4-3 9-5 13l-9 12c-1 1-3 3-3 4l-3 3-4 4v1c3 1 7 0 10 1h3c0 1 1 1 2 1v1 1l-1-1c-1 0-1 0-2-1h-2c-1 1-4 1-5 1-3 0-7 1-9 2h-1c-1 1-3 2-4 2l1 1c-1 1-3 1-4 2l-5 3c-1 1-4 2-5 3-3 2-5 3-8 5l-1-1 4-4c2-2 5-4 7-7 2-1 3-2 4-3 1-2 2-3 3-4 1 0 2-1 3-2h3c3-2 5-4 7-5 1-2 2-3 4-4 4-5 7-11 11-16 2-4 4-7 5-11 2-4 3-8 4-13h1c1-4 1-7 2-10 0-12-1-23-4-35-1-3-2-7-3-11z" class="N"></path><path d="M595 399h3c-1 0-1 1-2 2s-5 3-7 4c1-2 2-3 3-4 1 0 2-1 3-2z" class="I"></path><path d="M626 365v1h0c1 0 1-1 1-1-2 6-5 10-8 15-2 2-3 5-5 7-2 1-2 2-3 3h-1c0-1 2-3 3-4s1-2 2-3c4-6 7-11 11-18z" class="C"></path><path d="M630 350c-1 5-2 10-4 15-4 7-7 12-11 18-1 1-1 2-2 3s-3 3-3 4h1c-5 5-9 10-15 11 1-1 1-2 2-2 3-2 5-4 7-5 1-2 2-3 4-4 4-5 7-11 11-16 2-4 4-7 5-11 2-4 3-8 4-13h1z" class="j"></path><path d="M605 399c3 1 7 0 10 1h3c0 1 1 1 2 1v1 1l-1-1c-1 0-1 0-2-1h-2c-1 1-4 1-5 1-3 0-7 1-9 2h-1c-1 1-3 2-4 2l-16 9h1c1-2 2-3 3-4 6-4 13-10 21-12z" class="G"></path><path d="M625 294h0 0c0 1 0 1 1 1 0 0 0-1-1-2h1v1l1 4c1 1 1 1 1 2 1 1 1 1 1 2v1l1 1v1c1 2 1 4 2 6v2c0 4 1 8 2 12 1 14-1 28-7 40 0 0 0 1-1 1h0v-1c2-5 3-10 4-15 1-4 1-7 2-10 0-12-1-23-4-35-1-3-2-7-3-11zM496 526c10 1 19 4 29 7l9 3c0 1 1 1 1 1l4 3 1 1c1 0 2 1 2 1 0 1 0 3-1 4-1-1-2-1-3-2-3-2-6-4-10-6-1 0-2-1-3-1l-1 1-4-2c-6-1-11-2-16-3h-8-5l-1 1c-9 0-17 1-25 3-9 3-19 7-26 14-1 0-1 0-1-1 1-1 0-2 0-3-1-1-1-1-2-1l4-4 2-1c1 0 2 0 3 1v-1l13-4-1-1 1-2h1v-1h0c2-2 5-3 7-3l2-1-1 1h2c6-2 13-1 19-3 2-1 6-1 8-1z" class="t"></path><path d="M466 530l2-1-1 1h2c-3 1-7 2-10 4v-1h0c2-2 5-3 7-3z" class="E"></path><path d="M504 532c7 0 14 2 21 5l-1 1-4-2c-6-1-11-2-16-3h-8 0 8v-1z" class="h"></path><path d="M488 532c5-1 11-1 16 0v1h-8 0-5l-1 1c-9 0-17 1-25 3-9 3-19 7-26 14-1 0-1 0-1-1 1-1 0-2 0-3-1-1-1-1-2-1l4-4 2-1c1 0 2 0 3 1v-1l13-4c5-2 10-3 15-4 5 0 10-1 15-1z" class="s"></path><path d="M488 532c5-1 11-1 16 0v1h-8 0-5c1 0 1-1 2-1h3 2 0c-3-1-6 0-10 0z" class="n"></path><path d="M546 400c2-1 3-1 5 0 1 0 1 1 2 2 1 0 2 2 2 3 1 1 2 3 3 5 0 1 0 3 1 4 0 2 0 5 1 7l-1 2h-1c0 5-2 9-3 14l-1-1h0c-1 0-1 1-1 1l-1 2h-1c-1 0-1 1-2 2l-3 4-1 3-1-1-2 2c0-2 0-2 1-4v-1c-1 0-2 1-3 2 0-2 1-3 2-4s1-1 1-2c1-1 2-1 2-2h-1 0c-2 1-3 2-5 3-1-1-2-2-3-2s-1 0-2-1c2-3 2-6 3-9 2-2 3-4 5-6l-1-1v-1c1-1 1-1 1-2s0-1 1-2h0v-1-1h-2l-3-3v-3h0c1-2 2-4 4-5l1-1v-1c1 0 1-1 2-1h0c1 0 0 0 1-1h0z" class="t"></path><path d="M555 405c1 1 2 3 3 5 0 1 0 3 1 4 0 2 0 5 1 7l-1 2h-1c0 1 0 1-1 1 1-3 1-5 1-7l-2-1h-1-1l1-1c0-2 0-4-1-6v-1l-1-1 1-1c1 0 1 0 1-1z" class="E"></path><path d="M555 415h1s1 0 1-1h0l1 3-2-1h-1-1l1-1z" class="U"></path><path d="M557 424c1 0 1 0 1-1 0 5-2 9-3 14l-1-1h0c-1 0-1 1-1 1l-1 2h-1c-1 0-1 1-2 2l-3 4-1 3-1-1-2 2c0-2 0-2 1-4v-1c-1 0-2 1-3 2 0-2 1-3 2-4s1-1 1-2c1-1 2-1 2-2h-1l6-4c4-3 6-5 7-10z" class="Z"></path><path d="M548 436l6-3c-1 2-2 3-4 5-1 0-1-1-2-2z" class="H"></path><path d="M548 436c1 1 1 2 2 2-1 1-1 2-2 3l-4 6-2 2c0-2 0-2 1-4v-1c-1 0-2 1-3 2 0-2 1-3 2-4s1-1 1-2c1-1 2-1 2-2l3-2z" class="B"></path><path d="M543 445c1 0 1-1 1-1 1-2 2-2 4-3l-4 6-2 2c0-2 0-2 1-4z" class="D"></path><path d="M546 400c2-1 3-1 5 0 1 0 1 1 2 2 1 0 2 2 2 3s0 1-1 1l-1 1s0-1-1-2c0-1-2-2-3-3l2 8c1 2 1 3 2 5h0c-1 2-2 2-4 3-1 0-1 1-2 1-2 2-4 2-5 4l-1-1v-1c1-1 1-1 1-2s0-1 1-2h0v-1-1h-2l-3-3v-3h0c1-2 2-4 4-5l1-1v-1c1 0 1-1 2-1h0c1 0 0 0 1-1h0z" class="J"></path><path d="M553 402c1 0 2 2 2 3s0 1-1 1c-1-2-2-2-3-4h2z" class="d"></path><path d="M547 411h1l1-1h1v4c-1 0-1 1-3 1h0v-4z" class="H"></path><path d="M547 411c1-3 0-7 0-10 1 1 1 3 2 4 0 2 1 3 1 5h-1l-1 1h-1z" class="C"></path><path d="M543 403l3-1v9 1c0 1 0 2-1 3h-2-2l-3-3v-3h0c1-2 2-4 4-5l1-1z" class="t"></path><path d="M591 426h1 0c0 1 0 2 1 3-1 1-2 2-4 3h-1v-3h-1v3 1h0v1h0v2 3c0 1 0 1 1 2l1 1s-1 1-2 1l-3 3c0 1-5 5-5 5l-4 2h-1c0 1-1 1-1 2-1 0-2 0-2 1v1c2 0 2-1 3-1l-2 2h-2c-1 1-3 2-5 2-2 1-4 2-6 4l-3 1-2 1c-1 1-3 1-4 2-1 0-3 1-4 1h0c-1 0-1 0-2-1h0c0-1-1-1-2-2h1l1-1h0c2 0 2-1 3-2-1 1-2 2-3 2 0-1 1-2 1-3 1-2 5-5 7-7 0 0 1-1 1-2l3-3c7-7 14-15 23-19 2-1 5-2 7-3s3-1 4-2h1z" class="H"></path><path d="M575 449c0-1 0-1 1-2h0v-1c-1 0-1 0-3-1 2-1 3-1 5-1-1 1-1 2-1 2l1 2c-1 0-2 1-3 1z" class="C"></path><path d="M578 441c-2 1-10 3-11 3 0-1 1-2 2-2 2-1 4-1 6-2 1 1 1 1 3 1z" class="d"></path><path d="M587 439c0 1 0 1 1 2l1 1s-1 1-2 1-2 1-4 2c-1 1-3 1-5 3l-1-2s0-1 1-2c3-1 5-1 7-3 1-1 1-1 1-2h-2l1-1 1 1h1z" class="M"></path><path d="M587 432v1h0v1h0v2 3h-1l-1-1-1 1c-2 1-4 2-6 2s-2 0-3-1c5-1 9-2 12-6-3 0-5 1-8 2h-2c4-1 6-3 10-4z" class="L"></path><path d="M556 450v1h0 0 1c1-1 2-2 4-2 1-1 2-1 3-1 2-1 5 0 7-2l1 1c0 1-2 1-1 3l-2 1c-1-1-1-2-2-2-3-1-8 2-10 4-1 0-3 2-4 2h-1 0s1-1 1-2l3-3z" class="C"></path><path d="M578 448c2-2 4-2 5-3 2-1 3-2 4-2l-3 3c0 1-5 5-5 5l-4 2c0-2 2-2 3-3-1-1-3 1-5 1-1 0-3 1-5 1l-6 2v-1c1-1 5-1 7-2l2-1 4-1c1 0 2-1 3-1z" class="K"></path><path d="M552 455h0 1c1 0 3-2 4-2 2-2 7-5 10-4 1 0 1 1 2 2-2 1-6 1-7 2v1l6-2c1 0 1 1 1 1 1 1 1 0 1 2-1 1-2 0-3 1h-2c-3 0-7 1-9 2s-3 2-5 3c-2 0-3 1-4 2s-2 2-3 2c0-1 1-2 1-3 1-2 5-5 7-7z" class="U"></path><path d="M568 452c2 0 4-1 5-1 2 0 4-2 5-1-1 1-3 1-3 3h-1c0 1-1 1-1 2-1 0-2 0-2 1v1c2 0 2-1 3-1l-2 2h-2c-1 1-3 2-5 2-2 1-4 2-6 4l-3 1-2 1c-1 1-3 1-4 2-1 0-3 1-4 1h0c-1 0-1 0-2-1h0c0-1-1-1-2-2h1l1-1h0c2 0 2-1 3-2s2-2 4-2c2-1 3-2 5-3s6-2 9-2h2c1-1 2 0 3-1 0-2 0-1-1-2 0 0 0-1-1-1z" class="B"></path><path d="M557 462v-1c1-1 2-1 3-2l1 2-4 1z" class="D"></path><path d="M543 466c1-1 2 0 3 0h0c1-1 2-1 3-1h0l-5 3c0-1-1-1-2-2h1z" class="E"></path><path d="M561 461c2-1 4-1 6-2 1-1 2-1 3-1-1 1-3 2-5 2-2 1-4 2-6 4l-3 1-2 1c-1 1-3 1-4 2-1 0-3 1-4 1h0c-1 0-1 0-2-1h0l5-3c1 0 2-1 3-1 2-1 3-1 5-2l4-1z" class="p"></path><path d="M57 447c1-3 3-6 4-9 1-1 3-3 4-3h0c0 1 1 2 1 2v1h1v-1c1 1 2 3 2 4v1c1 2 1 3 1 5v1l-1 1h0c-1 1-1 1-1 2l1 1c0 1 1 1 2 2h0l5 8c1 2 3 4 5 5h0l3 3s-1 0-1 1c2 0 5 1 6 1h4 0 3c1 0 1 1 1 2h0 4 0c1 1 2 1 4 0v1h-2l-2 1h-2c0 1 0 1-1 1l-8 2c-6 1-12 1-18-2-1 0-3-1-4-2h-1c-1-1-2-3-3-4s-2-1-3-2l-4-4c0-3-1-4-2-6h1 1c1 2 1 3 2 4s2 3 3 3h0l-1-2-4-6c-1-1-1-4-1-6v-2c0-1 0-3 1-3z" class="H"></path><path d="M61 464c3 1 5 3 7 5h-3c-1 0-2-2-3-3l-1-2z" class="B"></path><path d="M93 472h0 3c1 0 1 1 1 2h0c-1 1-3 1-4 2v-1l1-1c-1 0-1-1-1-2z" class="J"></path><path d="M72 477l1-1c2 1 5 2 7 1 1 0 3 0 5 1h-2-6c2 1 3 1 5 1h2 6c-6 1-12 1-18-2z" class="X"></path><path d="M79 470c1 0 3 1 4 1 2 0 5 1 6 1h4c0 1 0 2 1 2l-1 1v1h-1c-4 0-8-1-12-3v-2c-1 0-1-1-1-1z" class="C"></path><path d="M56 459h1c1 2 1 3 2 4s2 3 3 3h0c1 1 2 3 3 3h3c3 3 7 7 12 8-2 1-5 0-7-1l-1 1c-1 0-3-1-4-2h-1c-1-1-2-3-3-4s-2-1-3-2l-4-4c0-3-1-4-2-6h1z" class="E"></path><path d="M57 447c1-3 3-6 4-9 1-1 3-3 4-3h0c0 1 1 2 1 2v1h1v-1c1 1 2 3 2 4v1c1 2 1 3 1 5v1l-1 1h0c-1 1-1 1-1 2l1 1c0 1 1 1 2 2h0l5 8c1 2 3 4 5 5h0l3 3s-1 0-1 1c-1 0-3-1-4-1-1-1-2-1-3-2h-1c-4-1-7-7-9-10h0c-1-1-1-2-1-3-1-1-1-3-1-4h-1c1-1 1-1 0-2v-1-1 3c-1 1-1 1-2 1v2l1 1c0 1 1 2 1 3h0v1c1 0 1 0 1 2 1 0 0 0 0 1h0l-3-6c-1-4-1-7 1-11h0l1-1c1-1 1-3 0-4h0v-1h0c-1 1-2 2-2 3 0 2-1 4-3 5h0-1v1z" class="C"></path><path d="M69 452c0 1 1 1 2 2v1l-1 4c-1-3-2-4-1-7z" class="e"></path><path d="M71 454h0l5 8-2 2v1l-2-4-2-2 1-4v-1z" class="F"></path><path d="M71 455c1 3 2 4 1 6h0l-2-2 1-4z" class="I"></path><defs><linearGradient id="J" x1="80.302" y1="465.648" x2="75.216" y2="466.867" xlink:href="#B"><stop offset="0" stop-color="#777373"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#J)" d="M74 465v-1l2-2c1 2 3 4 5 5h0l3 3s-1 0-1 1c-1 0-3-1-4-1-1-1-2-1-3-2l-2-3z"></path><path d="M67 437c1 1 2 3 2 4v1c1 2 1 3 1 5v1l-1 1h0c-1 1-1 1-1 2h-2c-1 2 0 3 0 4v1h0-1v-2c-1 0 0-1-1-2v-3c-1-1 0-2 0-3v-2c0-1 1-1 2-2 0-1-1-4 0-5v1h1v-1z" class="E"></path><path d="M66 437v1c0 1 0 1 1 2 0 1 0 1 1 2v1 4h0-3c0-1 1-3 1-5 0-1-1-4 0-5z" class="U"></path><path d="M538 409v3l3 3h2v1 1h0c-1 1-1 1-1 2s0 1-1 2v1l1 1c-2 2-3 4-5 6-1 3-1 6-3 9 1 1 1 1 2 1-1 1-1 1-1 3h0l-1 1c0 3-1 6-3 8-2 5-6 9-10 13-6 5-12 10-19 14-2 1-3 2-5 3l-2 1-2 1 2-2h-1-1c-1-1-1 0-2 0h0c1-1 2-2 3-2s1 0 1 1h0l1 1v-1-1l1-1-1-1c0-1 0-1-1-2 2-2 5-4 7-6 4-4 8-9 11-15 3-4 5-9 7-13v-1c1 0 1-1 2-1 0-2 1-3 1-5 1-1 1-1 1-3h1v-1c1-3 3-6 6-8 1 0 2-1 2-2h1l3-3h0v-2c-1-1-1 0-1-1h0v-4h-1 1c1 0 1 1 1 1h1v-2z" class="E"></path><path d="M534 425l1-1v-2-1c1-1 2-1 3-2h1c0 1 0 1-1 1 0 1 0 2-1 3 0 1-1 1-1 2h-2z" class="C"></path><path d="M538 409v3l3 3h2v1 1h0c-1 1-1 1-1 2s0 1-1 2v1l-3 4-1-1c0-1 1-1 1-2l1-1v-1s0-1 1-1v-1-1l1-1h1l-1-1c-1 0-1 1-2 1h-2 0v-2c-1-1-1 0-1-1h0v-4h-1 1c1 0 1 1 1 1h1v-2z" class="D"></path><path d="M531 422c1 0 2-1 2-2 1 1 1 2 1 3s0 0-1 1l-4 10c-1 3-1 5-3 7h0v-1h-1c0-3 1-4 1-6 0-1-1-2-1-3v-1c1-3 3-6 6-8z" class="U"></path><path d="M531 422v2c0 2-2 5-3 7-1 3-2 6-2 9h-1c0-3 1-4 1-6 0-1-1-2-1-3v-1c1-3 3-6 6-8z" class="Q"></path><path d="M528 431h-1-1c0-2 1-2 2-4 0-1 1-2 3-3 0 2-2 5-3 7z" class="J"></path><path d="M522 439l2-2c0-2 0-3 1-4v2 1c0 1 0 0-1 1h0c0 1 1 2 0 3h0c-1 1-1 1-1 2s0 1-1 1c1 3 0 5-2 8h0l-5 8c-2 2-3 3-4 5-2 2-5 4-7 6-1 0-2 2-3 2l-3 3v1l-1 2-1-1c0-1 0-1-1-2 2-2 5-4 7-6 4-4 8-9 11-15 3-4 5-9 7-13v-1c1 0 1-1 2-1z" class="X"></path><path d="M522 443c1 3 0 5-2 8h0c0-3 1-5 2-8z" class="B"></path><path d="M534 425h2c-2 4-4 8-5 11h0l1-1h1c0 2-1 4-1 6h0c1-1 2-2 2-3 1 1 1 1 2 1-1 1-1 1-1 3h0l-1 1c0 3-1 6-3 8-2 5-6 9-10 13-6 5-12 10-19 14-2 1-3 2-5 3l-2 1-2 1 2-2h-1-1c-1-1-1 0-2 0h0c1-1 2-2 3-2s1 0 1 1h0l1 1v-1-1l1-1 1-2 2-1c1-1 2-3 4-4 6-4 10-11 15-17 1-2 3-3 4-5 3-5 5-10 7-15 2-3 3-6 4-9z" class="t"></path><path d="M531 436h0l1-1h1c0 2-1 4-1 6h0c1-1 2-2 2-3 1 1 1 1 2 1-1 1-1 1-1 3h0l-1 1c0 3-1 6-3 8v-3c1-2 2-4 1-5h-1c-1 1-2 2-2 3 0-2 0-8 2-10z" class="B"></path><path d="M97 305c0-1 0-1 1-2h1c1-1 1-1 2-1-1 3-3 4-5 6h-1v2 1-1c2-1 3-3 5-4h0c1 0 2-1 2-1 1 0 2 1 2 1v2 3l-1 1c-1 0-1 0-1 1-2 2-5 4-6 7l-1 1h0c0 2-1 3-1 4v5c-1 2-1 4-1 6v5 2 1h1v-2l1 1-3 9v6c0 2-1 4 0 5 1 2 0 4 1 5v1h0c-1 1-1 2-1 4v3l4 12-1 3c-1-2-1-3-2-5-1-3-2-5-2-7-1 1-1 1 0 2l-1 1 1 3c1 1 1 2 1 4h0v1l1 1v3l-1-1c-2-2-3-5-4-7-2-5-5-10-5-15v-7c-1-2-1-3-1-4v-3c0-2-1-5 0-7v-4c-1-3-1-5-1-8 1 0 1-3 1-3 0-4 1-7 2-10l1-2-2 1 1-4 5-8 4-5 3-2c1-1 1 0 1 0z" class="d"></path><path d="M83 359c-1-1-1-1-1-2h1v-2c0-1 0-2-1-3 0-1 0-2 1-3v-6c-1 0-1 0-1-1l1 1v11l4 18h0c-1-2-1-3-2-5v-2c-1-1-1-3-1-4v2 4c1 2 2 2 1 5 0-2-1-4-1-7v-1h-1v-5zm1-34l3-2-3 12h0-1-1c0-4 1-7 2-10z" class="L"></path><path d="M95 321c0 2-1 3-1 4v5c-1 2-1 4-1 6h-1c0 3 1 6 0 8l-1 1v1h0v-3h-1c0-8 0-14 5-22z" class="c"></path><path d="M95 321h-1v-1h0v-1h0c1-3 3-4 5-6h0-1l-3 3-1-1h1v-2h0l4-4c1-1 2-2 3-2s1 1 2 1h0v3l-1 1c-1 0-1 0-1 1-2 2-5 4-6 7l-1 1z" class="G"></path><path d="M93 307l3-2c1-1 1 0 1 0h1l-4 3c0 5-3 7-6 11 0 2-1 3-1 4l-3 2 1-2-2 1 1-4 5-8 4-5z" class="F"></path><path d="M89 314l-1 5c0 2-1 3-1 4l-3 2 1-2c1-3 2-6 4-9z" class="P"></path><path d="M94 308c0 5-3 7-6 11l1-5c1-2 3-5 5-6z" class="O"></path><g class="D"><path d="M82 357h0v1 2h0l1-1v5h1v1c0 3 1 5 1 7 1-3 0-3-1-5v-4-2c0 1 0 3 1 4v2c1 2 1 3 2 5h0l2 7v1 1l1 1 1 3c1 1 1 2 1 4h0v1l1 1v3l-1-1c-2-2-3-5-4-7-2-5-5-10-5-15v-7c-1-2-1-3-1-4v-3z"></path><path d="M90 343h1v3h0v-1l1-1c1-2 0-5 0-8h1v5 2 1h1v-2l1 1-3 9v6c0 2-1 4 0 5 1 2 0 4 1 5v1h0c-1 1-1 2-1 4v3l4 12-1 3c-1-2-1-3-2-5-1-3-2-5-2-7 0-1-1-4-2-5v-6c-1-5-3-13-1-18h0c1-1 1-1 1-2 2-1 1-3 1-5z"></path></g><path d="M94 342l1 1-3 9v6c0 2-1 4 0 5 1 2 0 4 1 5v1h0c-1 1-1 2-1 4v3-1h-1c-1-4-1-8-1-12 0-7 2-13 4-19v-2z" class="Z"></path><path d="M57 340v-1-11c1-1 0-3 1-4v-2-1-1l2-11c1-1 1-2 1-3v-1l4-11 2-5h0v-2h1v-2-2h0c1-2 1-9 0-10h0v-1-1c-1-1-1-1-3-2h-2v-1l-1-1s-1 0-1 1h-1c-1 0-1 1-1 2h-1 0v-1l2-2h1l1-1c1 0 3 0 5 1h0 1l1 1c1 0 2 1 2 2v1l1 1 1 2v1c1 1 1 5 1 6l-1 1v2 3c-1 0-1 1-1 1v2s0 1-1 1v1 2l-3 6-1 4v2c-1 1-1 1-1 2v1 1h-1v3 1 4h0-1c0 1 0 3-1 4v9l1-2c0 9 0 18 3 26 2 6 4 11 6 16l6 9c3 2 4 6 7 9l-1 1c0 1 1 1 1 2h-1c1 2 3 4 5 5l4 3c1 1 3 3 5 4l-1 1c-2-2-4-3-6-3-2-1-3-1-5-1 1-1 0-1 1-1l-1-1c-5-3-8-8-11-12-4-4-7-9-10-13-4-9-7-19-8-28-1-2-1-4-1-6z" class="G"></path><path d="M63 331l1-2c0 9 0 18 3 26 2 6 4 11 6 16l6 9c3 2 4 6 7 9l-1 1c-1-1-1-2-2-2-1-3-3-5-4-7-4-4-6-9-8-13s-3-7-5-10c-2-7-3-13-3-20v-7z" class="K"></path><path d="M57 340v-5l1 7v4l2 8c1 6 3 12 6 18s8 11 12 16c2 3 5 7 8 9l1 1 1-1c-1-1-2-1-3-2-3-4-5-8-7-11-4-7-9-13-12-20h1c1 3 3 6 4 9 2 3 5 7 8 10l6 9c1 2 3 4 5 5l4 3c1 1 3 3 5 4l-1 1c-2-2-4-3-6-3-2-1-3-1-5-1 1-1 0-1 1-1l-1-1c-5-3-8-8-11-12-4-4-7-9-10-13-4-9-7-19-8-28-1-2-1-4-1-6z" class="m"></path><path d="M536 439c1 0 2 1 3 2 2-1 3-2 5-3h0 1c0 1-1 1-2 2 0 1 0 1-1 2s-2 2-2 4c1-1 2-2 3-2v1c-1 2-1 2-1 4l2-2 1 1c-2 2-3 4-5 6v2c-1 1-2 3-4 4v-1c-2 4-5 7-8 10-1 1-3 2-5 4l-2 2c3 1 3-1 6-1h1v-1l1 1c-4 2-9 3-13 6 0 0-2 0-2 1l-29 17c-4 1-7 4-10 5h-1c-1 0-1 1-2 1h-1-1c-1 0-1-1-2-2h-3l-3-1c0-1 1-1 1-2l1-1 2-1 6-3c7-5 15-10 21-16 2-1 2 0 3 1v1 1l-1-1h0c0-1 0-1-1-1s-2 1-3 2h0c1 0 1-1 2 0h1 1l-2 2 2-1 2-1c2-1 3-2 5-3 7-4 13-9 19-14 4-4 8-8 10-13 2-2 3-5 3-8l1-1h0c0-2 0-2 1-3z" class="H"></path><path d="M494 491v-1c1-1 4-4 6-4h2c-2 2-5 4-8 5z" class="d"></path><path d="M542 449l2-2 1 1c-2 2-3 4-5 6v2c-1 1-2 3-4 4v-1s1-1 1-2c2-3 4-5 5-8z" class="I"></path><path d="M466 497l1 1c1 0 2 0 3-1l2-1h2l2-2h1l3-2 2-1h1c1-1 1-1 2-1-1 1-2 1-3 2l-4 2c-4 3-8 5-12 6 0 1-1 1-1 2l-3-1c0-1 1-1 1-2l1-1 2-1z" class="P"></path><path d="M463 499l1 1h2 0c0 1-1 1-1 2l-3-1c0-1 1-1 1-2z" class="R"></path><path d="M521 475c3 1 3-1 6-1h1v-1l1 1c-4 2-9 3-13 6 0 0-2 0-2 1l-29 17c-4 1-7 4-10 5h-1l6-6 4 1 10-7c3-1 6-3 8-5l19-11z" class="S"></path><path d="M523 464h0l-3 3h1 0 0l-2 2c0 1 1 1 1 1-2 2-3 3-5 4-4 2-15 10-19 10-1 0-4 2-5 3l-3 1-1 1s-1 0-1 1v-1l2-2h1l1-1c2-1 5-3 7-5 2-1 3-2 5-3 4 0 10-6 13-7 3-2 5-5 8-7z" class="D"></path><path d="M472 494c7-5 15-10 21-16 2-1 2 0 3 1v1 1l-1-1h0c0-1 0-1-1-1s-2 1-3 2h0c1 0 1-1 2 0h1 1l-2 2 2-1 2-1c-2 2-5 4-7 5l-1 1h-1l-2 2v1h-1c-1 0-1 0-2 1h-1l-2 1-3 2h-1l-2 2h-2l-2 1c-1 1-2 1-3 1l-1-1 6-3z" class="G"></path><path d="M536 439c1 0 2 1 3 2 2-1 3-2 5-3h0 1c0 1-1 1-2 2 0 1 0 1-1 2s-2 2-2 4c-1 2-2 3-3 5-4 7-10 15-17 19 0 0-1 0-1-1l2-2h0 0-1l3-3h0c-3 2-5 5-8 7-3 1-9 7-13 7 7-4 13-9 19-14 4-4 8-8 10-13 2-2 3-5 3-8l1-1h0c0-2 0-2 1-3z" class="Q"></path><path d="M523 464h0c3-3 6-6 8-9 2-2 2-4 4-5 0 1-1 1-1 2v2c1 0 2-2 3-3-4 7-10 15-17 19 0 0-1 0-1-1l2-2h0 0-1l3-3h0z" class="O"></path><path d="M340 639c1 2-2 9-3 12h0l-3 5c-1 1-1 2-2 3h-2c-1 1-1 1-1 2-2 1-3 3-4 4l-3 2-1 1c-2 3-5 5-8 6-10 7-26 12-38 9h-2 0c-3-1-8-3-10-5v-1h0c0-1-1-4-1-5v-1c0-2 1-3 2-5v1c-1 1 0 2 0 2l-1 1v2c0 1 0 2 1 3 0 1 1 1 2 1 1 1 3 2 4 2h1 0l-1-1h1l-1-1-2-2c-1-1-1-2 0-3 0-1 1-2 2-3v-1l1-1-1-1c2-1 5-2 7-2h1c2 0 4 2 7 2 1 0 1 0 1-1 2 1 5 2 6 4l6 2h6 0l4-2h-1l3-3h0c1 0 1 0 2-1s1-2 2-3 3-1 4-2v-1h0l2-1 1-1 1-2c1 0 2-1 3-2l1-1 3-3c1-1 1-1 1-2v-1l1 1 3-1 2-4h0c0-1 1-1 1-2h3 0z" class="U"></path><path d="M322 664l4-3-1 4-3 2v-3z" class="B"></path><path d="M313 674l1-1v-1c1-1 3-2 3-3 2-1 4-3 5-5v3l-1 1c-2 3-5 5-8 6z" class="C"></path><path d="M310 665h0c1 0 1 0 2-1s1-2 2-3h4l-1 1 1 1-1 2v1c-2 2-7 5-10 6l-3-1v-1l4-2h-1l3-3z" class="D"></path><path d="M310 665h0c1 0 1 0 2-1s1-2 2-3h4l-1 1-9 6h-1l3-3z" class="F"></path><path d="M340 639c1 2-2 9-3 12h0l-3 5c-1 1-1 2-2 3h-2c-1 1-1 1-1 2-2 1-3 3-4 4l1-4c1-2 4-3 5-5v-2l1-1c1-1 1-1 1-2l1-2c1-1 2-3 2-5h0v-1c-2 4-4 7-6 10 0 2 0 2-1 3-3 3-6 4-9 6-1 2-2 3-3 4v-1l1-2-1-1 1-1h-4c1-1 3-1 4-2v-1h0l2-1 1-1 1-2c1 0 2-1 3-2l1-1 3-3c1-1 1-1 1-2v-1l1 1 3-1 2-4h0c0-1 1-1 1-2h3 0z" class="D"></path><path d="M330 646v-1l1 1 3-1c-2 4-4 7-7 10l-2-1c-1 1-3 2-4 2l1-2c1 0 2-1 3-2l1-1 3-3c1-1 1-1 1-2z" class="Y"></path><path d="M329 648l1 1h-1c-1 2-2 4-4 5-1 1-3 2-4 2l1-2c1 0 2-1 3-2l1-1 3-3z" class="r"></path><path d="M325 654l2 1-5 3-2 4c-1 2-2 3-3 4v-1l1-2-1-1 1-1h-4c1-1 3-1 4-2v-1h0l2-1 1-1c1 0 3-1 4-2z" class="i"></path><path d="M318 661c1-1 3-2 4-3l-2 4c-1 2-2 3-3 4v-1l1-2-1-1 1-1z" class="E"></path><path d="M340 639c1 2-2 9-3 12h0l-3 5c-1 1-1 2-2 3h-2c1-2 3-3 3-5 0-1 0-1 1-2 1-3 2-7 3-11h-1c0-1 1-1 1-2h3 0z" class="T"></path><path d="M270 665c2-1 5-2 7-2h1c2 0 4 2 7 2 1 0 1 0 1-1 2 1 5 2 6 4l6 2h6 0v1c-8 4-16 7-25 7h-6c-1-1-1-1-2-1h0l-1-1-2-2c-1-1-1-2 0-3 0-1 1-2 2-3v-1l1-1-1-1z" class="F"></path><path d="M292 668l6 2c-2 2-3 2-5 3-2 0-4 0-5-1h3 0c1-1 1-1 2-1v-1l-1-2z" class="e"></path><path d="M270 668v1l1 1c2 1 2 2 4 3 0 1-1 0 0 0l2 2h0 7 1l1-1 1 1c1 0 3-1 4 0-5 1-9 2-14 2-2 0-3 0-4-1l-3-1v1l-2-2c-1-1-1-2 0-3 0-1 1-2 2-3z" class="B"></path><path d="M270 668v1l1 1v1c0 1 0 1 1 2l2 2-5-3h0c0 2 0 2 1 3v1l-2-2c-1-1-1-2 0-3 0-1 1-2 2-3z" class="Q"></path><path d="M271 668l1-1-1-1h1 2l1 1h1c1 0 1 0 2 1l1 2h0c3 0 4 2 6 3l1 1h0l-1 1h-1-7 0l-2-2c-1 0 0 1 0 0-2-1-2-2-4-3l-1-1 1-1z" class="U"></path><path d="M271 668l1-1-1-1h1 2l1 1h1c-1 0-2 1-2 2l2 2h0l-1 1c0-1 0-1-1-2 0-1-1-1-3-2z" class="C"></path><path d="M270 665c2-1 5-2 7-2h1c2 0 4 2 7 2 1 0 1 0 1-1 2 1 5 2 6 4l1 2v1c-1 0-1 0-2 1h0-3-3c-1-1-2-1-3-2l-3-3-1 1c-1-1-1-1-2-1h-1l-1-1h-2-1l1 1-1 1-1 1v-1-1l1-1-1-1z" class="E"></path><path d="M293 670v1c-1 0-1 0-2 1h0-3-3l1-1-1-1c1 0 3 0 4 1h3l1-1z" class="O"></path><path d="M136 463c4 1 7 3 11 5h0l3 1 5 1v-1-1s1 0 1-1l5 5c1 0 2 1 2 2l10 4c2 1 3 2 5 2 0 0 1 1 2 1v2c-6-1-11-4-16-5-6-2-12-4-19-4-1-1-5-1-7 0h2 0l-1 1c-1 1-5 2-7 2-3 1-6 3-8 4l-1 1 1-3s0-1 1-1c-1 0-2 0-3 1-2 2-5 4-7 4 0 0-1 0-1 1h-2l2-2h0 0l-3 1c-3 1-4 3-7 5h-1l-1 1-1 1c-1 2-2 3-3 4 0 1 0 1 1 2-2 1-3 3-3 5l-1 3h0-1v-2c-1 1-1 3-1 4-1 3-3 5-3 8l-1 3v2h0c-2 0-4-2-5-3-1 0-1-1-2 0h0l-2-5c0-3 0-7 1-10 0-2 1-3 2-5 0-1 2-2 3-4 1 0 1-1 2-1 2 0 3-1 5-2 0 0 1 0 1-1 2-1 3-2 5-3 0-1 1-1 1-2 2-1 3-2 5-3l4-1v-1h1 1 0c1 0 1 0 2-1 1 0 2 0 3-1h0-1c-1 0-2 0-4 1h-2l-1 1-5 2c-2 1-5 2-6 3h0-2c0 1-1 1-1 1l-1-1c3-2 6-2 8-4v-1c1 0 2 0 2-1-1 1-1 1-2 1 0-1 0-1 1-1h2l2-1 4-1c2 0 4-2 7-4h1-1c1-1 1-2 1-3h1v-1l3-1c1 0 3 1 5 0s4 0 6 0v-1s-1 0-1-1c1 0 2 0 3-1h1z" class="G"></path><path d="M88 513l1-1h1v2l-1 3-1-1v-3z" class="P"></path><path d="M97 490c2-1 2-3 6-4l-2 2-1 1c-1 2-3 4-4 6 0-2 0-3 1-5z" class="E"></path><path d="M90 502v5h1l1-1h1c-1 3-3 5-3 8v-2h-1l-1 1c0-1 1-1 1-2v-4 1-1l1-5z" class="D"></path><path d="M89 507c-1-3 0-6 1-9l2-3v-1l2-2c0 2-1 2-1 3-2 2-2 6-3 7l-1 5v1-1z" class="L"></path><path d="M119 468c2-1 4 0 6 0h0c2 1 3 1 5 1l-12 2h-1c1-1 1-2 1-3h1z" class="E"></path><path d="M90 493l1-1v1l-1 2c-3 5-3 9-3 14l-1 1h0c-1-3-1-5-1-8 0-4 3-7 5-9z" class="M"></path><path d="M103 486c1-1 2-2 4-3 3-2 5-3 8-3 0 0-1 1-1 2l-3 1c-3 1-4 3-7 5h-1l-1 1-1 1-1-1 1-1 2-2z" class="P"></path><path d="M156 467l5 5c1 0 2 1 2 2l-14-3c-2-1-5-1-8-2h6 0 1l-1-1 3 1 5 1v-1-1s1 0 1-1z" class="T"></path><path d="M156 467l5 5c-1 0-5-1-6-2v-1-1s1 0 1-1zm-70 25v1c0 1 0 1-1 2-2 4-4 11-3 16 1 0 1 0 1 1h0c0 1 1 2 1 3h1v1h1c0 1 1 1 2 2 1 0 1 0 1 1h0c-2 0-4-2-5-3-1 0-1-1-2 0h0l-2-5c0-3 0-7 1-10 0-2 1-3 2-5 0-1 2-2 3-4z" class="B"></path><path d="M81 501c1 2 0 3 0 5h0v3c0 1 0 1 1 2v2h0c0 1 1 1 1 1l1 2c-1 0-1-1-2 0h0l-2-5c0-3 0-7 1-10z" class="G"></path><path d="M100 489l1 1c-1 2-2 3-3 4 0 1 0 1 1 2-2 1-3 3-3 5l-1 3h0-1v-2c-1 1-1 3-1 4h-1l-1 1h-1v-5c1-1 1-5 3-7 0-1 1-1 1-3h0v-1c1-1 2 0 3-1-1 2-1 3-1 5 1-2 3-4 4-6z" class="C"></path><path d="M100 489l1 1c-1 2-2 3-3 4 0 1 0 1 1 2-2 1-3 3-3 5l-1 3h0-1v-2c-1 1-1 3-1 4h-1c1-4 2-8 4-11 1-2 3-4 4-6z" class="I"></path><path d="M136 463c4 1 7 3 11 5h0l1 1h-1 0-6c-3-1-7-1-11 0-2 0-3 0-5-1h0c-2 0-4-1-6 0v-1l3-1c1 0 3 1 5 0s4 0 6 0v-1s-1 0-1-1c1 0 2 0 3-1h1z" class="F"></path><path d="M136 463c4 1 7 3 11 5l-1 1c-1 0-2-1-3-1-4-1-6-3-10-3 0 0-1 0-1-1 1 0 2 0 3-1h1z" class="V"></path><path d="M115 480c2-1 3-2 5-3 6-2 12-3 18-3h2 0l-1 1c-1 1-5 2-7 2-3 1-6 3-8 4l-1 1 1-3s0-1 1-1c-1 0-2 0-3 1-2 2-5 4-7 4 0 0-1 0-1 1h-2l2-2h0 0c0-1 1-2 1-2z" class="D"></path><path d="M115 480c2-1 3-2 5-3 6-2 12-3 18-3h2 0l-1 1c-4 0-9 0-13 2-2 0-4 1-6 2-2 0-4 2-5 3s-1 1-1 2h-2l2-2h0 0c0-1 1-2 1-2z" class="T"></path><path d="M583 415c1-1 4-2 5-3l5-3c1 0 1 1 1 1 1 0 3-1 3 0v1c1 0 1 0 3-1h6c-1 1-2 1-3 1h4v1c-3 3-9 3-12 4-2 1-3 2-4 2v1c1-1 2-1 2-2h2 1 2l6-2h3v1h-1l-4 2h-2 0c-1 1-2 1-4 1 0 0-1 0-2 1h-1c-1 0-4 1-4 3l1 1h0 2l-3 1 1 1c-1 1-2 1-4 2s-5 2-7 3c-9 4-16 12-23 19l-3 3c0 1-1 2-1 2-2 2-6 5-7 7 0 1-1 2-1 3 1 0 2-1 3-2-1 1-1 2-3 2h0l-1 1h-1c1 1 2 1 2 2h0c1 1 1 1 2 1l-1 1c-4 1-10 1-14 4h-2l-1-1v1h-1c-3 0-3 2-6 1l2-2c2-2 4-3 5-4 3-3 6-6 8-10v1c2-1 3-3 4-4v-2c2-2 3-4 5-6l1-3 3-4c1-1 1-2 2-2h1l1-2s0-1 1-1h0l1 1c1-5 3-9 3-14h1l1-2h0c1-1 1-3 1-5h1c1 1 1 1 1 3l1-2c0 1 1 3 0 5l1-2h1v2c0-1 1-1 1-2v1c2 0 3-4 5-5h2l1-2c1 0 2 0 3-1v2l-4 4 1 1c3-2 5-3 8-5z" class="U"></path><path d="M589 423l1 1h0 2l-3 1c-1 1-2 1-3 2h-1c-1 1-3 1-4 2v-1c2-2 5-4 8-5z" class="G"></path><path d="M559 423c1 0 1 0 1 1v3 1h-1v2l-1 2v1l-1 2v1c0 2-2 4-3 6 0 2-1 3-3 4l4-9c1-5 3-9 3-14h1z" class="B"></path><path d="M583 415c1-1 4-2 5-3l5-3c1 0 1 1 1 1 1 0 3-1 3 0v1c1 0 1 0 3-1h6c-1 1-2 1-3 1-2 1-4 1-6 1l-2 1c-3 0-5 1-8 2-1 0-2 1-2 1-1 0-2-1-2-1z" class="J"></path><path d="M564 417c0 1 1 3 0 5l1-2h1v2 3 1h0c1 0 1 1 1 1l-1 2-1 1-1 1h-1l1-1-2-2v1l-1 2c0 1-1 1-1 2h-1c0-1 0-1 1-2v-3l1-1h0v-2c1-1 2-4 2-6h0l1-2z" class="M"></path><path d="M564 422l1-2h1v2 3 1h0c1 0 1 1 1 1l-1 2-1 1c-1-2-1-6-1-8z" class="N"></path><path d="M566 429l-1-1v-3l1 1c1 0 1 1 1 1l-1 2z" class="p"></path><path d="M581 429c1-1 3-1 4-2h1c1-1 2-1 3-2l1 1c-1 1-2 1-4 2s-5 2-7 3c-9 4-16 12-23 19l-3 3h-1c1-2 3-3 4-5l6-6c6-5 12-10 19-13z" class="E"></path><path d="M578 413v2l-4 4 1 1-8 7s0-1-1-1h0v-1-3c0-1 1-1 1-2v1c2 0 3-4 5-5h2l1-2c1 0 2 0 3-1z" class="Q"></path><path d="M567 420v1c2 0 3-4 5-5h2c-2 2-3 4-5 6-1 0-2 1-2 2 0 0-1 0-1 1v-3c0-1 1-1 1-2z" class="B"></path><path d="M574 419l1 1-8 7s0-1-1-1h0l3-3c2-1 4-3 5-4z" class="S"></path><defs><linearGradient id="K" x1="545.42" y1="460.641" x2="541.234" y2="456.16" xlink:href="#B"><stop offset="0" stop-color="#cbcaca"></stop><stop offset="1" stop-color="#efeeef"></stop></linearGradient></defs><path fill="url(#K)" d="M554 442v3c1-1 2-3 2-4 2-1 3-3 5-4-1 2-3 3-4 5 0 1-1 2-2 3s-1 2-1 3c-1 0-1 1-1 1-2 1-3 4-4 6h0 1l2-2h1c0 1-1 2-1 2-2 2-6 5-7 7 0 1-1 2-1 3 1 0 2-1 3-2-1 1-1 2-3 2h0l-1 1h-1c1 1 2 1 2 2h0c1 1 1 1 2 1l-1 1c-4 1-10 1-14 4h-2l-1-1v1h-1c-3 0-3 2-6 1l2-2h1 3c1-1 1-1 2-1 3-1 6-5 9-8 5-6 10-10 13-18 2-1 3-2 3-4z"></path><path d="M534 471c0-1 1 0 1-1l2-2h0 1 0v1 1l-4 1z" class="D"></path><path d="M539 467v-1l2-1c2-1 3-2 4-3 0 1-1 2-1 3 1 0 2-1 3-2-1 1-1 2-3 2h0l-1 1h-1c-1 1-1 1-3 1z" class="C"></path><path d="M542 466c1 1 2 1 2 2h0c-2 1-4 2-6 2v-1l1-2c2 0 2 0 3-1z" class="M"></path><path d="M538 470c2 0 4-1 6-2 1 1 1 1 2 1l-1 1c-4 1-10 1-14 4h-2l-1-1v1h-1c-3 0-3 2-6 1l2-2h1 3c1-1 1-1 2-1 1 1 4 0 5-1l4-1z" class="i"></path><path d="M552 439l1-2s0-1 1-1h0l1 1-4 9c-3 8-8 12-13 18-3 3-6 7-9 8-1 0-1 0-2 1h-3-1c2-2 4-3 5-4 3-3 6-6 8-10v1c2-1 3-3 4-4v-2c2-2 3-4 5-6l1-3 3-4c1-1 1-2 2-2h1z" class="o"></path><path d="M549 441c1-1 1-2 2-2h1c0 2-1 3-1 4v1c-1 0-1 0-1-1h-1v-2z" class="I"></path><path d="M546 445l3-4v2h1c0 1 0 1 1 1-1 2-1 4-2 6-1-1-1-1-2-1 1-1 0-2 0-3l-1-1z" class="Q"></path><defs><linearGradient id="L" x1="535.691" y1="456.661" x2="545.956" y2="458.003" xlink:href="#B"><stop offset="0" stop-color="#c2c2be"></stop><stop offset="1" stop-color="#ece8ef"></stop></linearGradient></defs><path fill="url(#L)" d="M546 445l1 1c0 1 1 2 0 3 1 0 1 0 2 1l-1 1c-2 5-7 9-11 13-2 2-5 4-7 6h-1l4-4 3-6c2-1 3-3 4-4v-2c2-2 3-4 5-6l1-3z"></path><path d="M546 445l1 1c0 1 1 2 0 3 1 0 1 0 2 1l-1 1-1-1c-2 0-2 2-4 4-1 0-2 1-3 2v-2c2-2 3-4 5-6l1-3z" class="O"></path><path d="M576 503c0-1 1-3 2-4l2 2h1c2 0 3-1 4-2h1l-1 1c0 3-2 5-3 8-1 2-1 4-2 6h-3-1 0s0 1-1 2v-1l-2 2c0 1 0 1-1 2v1l-1 1-1 1-1 1-1 2c-1 0-1 1-1 2h-2c-1-1-2-1-2-2h-2c-1 1-1 1-2 1v1-1l-1 1c1 3 2 5 5 7l4 4c2 1 3 2 4 3l4 2c-1 1-1 1-2 1 0 1 0 1 1 2 0 0-1 0-2 1s-2-1-4-1c-1 0-1-1-1-2h-2 0-2c0 1 1 2 2 3s0 0 1 0c1 1 1 2 3 3h0c1 1 2 1 3 2h1v1h0-1s-1 0-1-1h-2v1c0 1 1 3 1 4v1l-1 1v1l1 1h-1 0c0-1-1-1-2-2-3-4-7-7-10-10l-2-2c-1-2-2-2-3-4l-2-2c-2-1-3-2-5-3l-2-2h1 0c2 0 3 2 5 3v-1s-1 0-1-1c0 0 0-1-1-1v-2h0l-1 1c0-1-1-2-1-2 1-2 0-4 0-6v-8c-1-1-1-2-1-3v-1-2-2l-1-1c0-1 1 0 2-1l1-1c0-1 1-3 1-3 1-2 2-3 3-3 1-1 2-2 3-2h0c0 1 0 1-1 2h2v1c1 0 1 0 1 1 1-1 2-3 3-3l1-1h6 0 1c1 0 2 0 3-1 1 1 2 1 2 2l1 1c1 0 2 1 2 2h1l1-1z" class="H"></path><path d="M571 541l4 2c-1 1-1 1-2 1h0c-2-1-3-2-4-2v-1h2z" class="B"></path><path d="M560 504c1-1 2-2 3-2s1 1 2 1h0c2 1 3 1 4 3l-1 1-2-2-1-1h-4-1z" class="J"></path><path d="M567 544h1c1 0 2 0 4 1v2c-1 1-2-1-4-1-1 0-1-1-1-2z" class="C"></path><path d="M565 521h-5-2c-1 1-1 2-1 4l-1-1c0-1 0-5 1-7 1 2 2 2 4 2h2l1-1h1v1h5v1h-2l-3 1z" class="g"></path><path d="M568 520h2 0v1 1l-1 1-1 2c-1 0-1 1-1 2h-2c-1-1-2-1-2-2s0-1 1-1c0-1 1-1 1-1 1-1 1-1 1-2h-1l3-1z" class="B"></path><path d="M568 520h2 0v1c-1 1-1 1-2 1v-2z" class="C"></path><path d="M557 517h0c1-1 1-1 1-2l1-1c1 1 1 1 1 2h0c1 0 1 0 2 1h1l1-1h0v-1h4v1c-1 0-1 0-1 1 1 0 1 0 2-1h1v3h-5v-1h-1l-1 1h-2c-2 0-3 0-4-2z" class="U"></path><path d="M554 502v1c1 0 1 0 1 1h0c-1 1-2 2-2 4-1 1-2 8-3 9-1 0-1 1-1 1v2h0 0l-1 1v-5l1-1c-1-1-1-1-1-2l3-8c0-1 1-2 1-3h0 2z" class="Q"></path><path d="M549 515l1-4 3-8c-1 5-3 9-3 14-1 0-1 1-1 1v2h0 0l-1 1v-5l1-1z" class="U"></path><path d="M553 500h0c0 1 0 1-1 2h0c0 1-1 2-1 3-1 2-2 5-3 8 0 1 0 1 1 2l-1 1v-4h0-3v7c-1-1-1-2-1-3v-1-2-2l-1-1c0-1 1 0 2-1l1-1c0-1 1-3 1-3 1-2 2-3 3-3 1-1 2-2 3-2z" class="B"></path><path d="M550 502c0 1-1 3-2 4l-3 6v7c-1-1-1-2-1-3v-1-2-2l-1-1c0-1 1 0 2-1l1-1c0-1 1-3 1-3 1-2 2-3 3-3z" class="Q"></path><path d="M566 505l2 2 1-1h0c1 1 2 1 3 1v1 1 3h0l-3 3h0c-1 0-1 1-1 1v-1h-4v-2-1c0-1 1-2 1-2l-2-2 3-3z" class="M"></path><path d="M569 510c1 1 0 3 0 4 0 0-1 0-1 1h-4v-2-1l1 1h1 1l1-1s1-1 1-2z" class="B"></path><path d="M569 510v-2c1 0 2 1 3 1v3h0l-3 3h0c-1 0-1 1-1 1v-1c0-1 1-1 1-1 0-1 1-3 0-4z" class="C"></path><path d="M550 541h0c1 1 2 2 3 2 0-2-1-3-3-4v-1l-1-1h1s0 1 1 1c0 1 1 2 2 2 1 2 3 4 4 5l2 1c1 0 1 1 2 1 2 1 3 4 4 5l5 5v1l-1 1v1l1 1h-1 0c0-1-1-1-2-2-3-4-7-7-10-10l-2-2c-1-2-2-2-3-4l-2-2z" class="B"></path><path d="M576 503c0-1 1-3 2-4l2 2h1c2 0 3-1 4-2h1l-1 1c0 3-2 5-3 8-1 2-1 4-2 6h-3-1 0s0 1-1 2v-1l-2 2c0 1 0 1-1 2v1l-1 1-1 1v-1-1h0v-1-3h-1c-1 1-1 1-2 1 0-1 0-1 1-1 0 0 0-1 1-1h0l3-3h0v-3-1-1c-1 0-2 0-3-1h0c-1-2-2-2-4-3h0c-1 0-1-1-2-1s-2 1-3 2c0-1-1-1-2-1l-1 1h-2 0c1-1 2-3 3-3l1-1h6 0 1c1 0 2 0 3-1 1 1 2 1 2 2l1 1c1 0 2 1 2 2h1l1-1z" class="L"></path><path d="M577 512c0-2 1-2 2-3v2 2h-2v-1z" class="C"></path><path d="M584 502c-1 3-4 6-5 9v-2c0-1 0-3 1-4 0-1 3-3 4-3z" class="H"></path><path d="M565 503l1-1c1 0 2 0 4 2h0l1 1v-2h1 0c0 1 1 1 1 1v1l1 1v2h-2v-1c-1 0-2 0-3-1h0c-1-2-2-2-4-3z" class="B"></path><path d="M576 503c0-1 1-3 2-4l2 2h1c2 0 3-1 4-2h1l-1 1c0 1-1 1-1 2-1 0-4 2-4 3-1 1-1 3-1 4-1 1-2 1-2 3l-1-1v-4-4z" class="P"></path><path d="M448 504c1 0 2 0 3-1 2 1 5 1 6 1h1l1 1c-2 3-4 3-7 4l-2 1 1 1v1l-4 2c1 1 2 1 3 2v1l-1 1h2v2h0l-1 1c0 2-2 5-3 7 1 0 1 0 2 1v1h0 0l1-1 1 2c-1 1-2 3-4 4l-2 2-1 1-2 3-2 1-4 4h-2c-1 1-1 1-2 1l-3 1v-1c-1 0-2 0-2 1-1 0-1 1-1 2l-4 4-1-1h0c-1 1-1 2-1 4l-1 1 1 1c-1 1-2 4-4 5v-1h0v-2c-1 1-1 2-2 3-1 2-1 5-2 8-1-1-2-2-2-4 1-1 0-2 0-4 1-1 1-4 1-5v-2l-1-1c1-3 1-5 2-7 1-6 3-12 5-17 0-1 1-1 1-2v-1l2-4 1-1 1-1 1-3 1-1c1-1 1-2 2-3v1c1 0 1-1 1-1l6-6c0-1 1-3 2-3 0-1 1-2 2-2h0v1h1l1-1c1 0 0 1 1 1 1 1 2 0 2 2h0l6-4z" class="H"></path><path d="M430 522v1l2-2 1 1-1 2h0v1c-1 1-1 2 0 2v1l-1 1 1 1h-1l-2 1c-1 2-4 3-5 5l-4 5c0 1 0 2-1 2 0 1-1 1-2 1l1-2h-1c0-1 1-2 1-3 0 0 0-1 1-1 2-3 3-5 5-7 1-2 4-4 4-6 1-1 1-2 2-3z" class="M"></path><path d="M424 533c2-2 4-4 6-7 0 2-1 4-1 5h0c-1 2-4 3-5 5v-3z" class="X"></path><path d="M424 533v3l-4 5c0 1 0 2-1 2 0 1-1 1-2 1l1-2 1-2v-1c2-2 3-4 5-6z" class="d"></path><path d="M435 507c0-1 1-2 2-2h0v1h1c0 2-2 3-2 4v1c-1 1-2 1-3 2-1 2-4 3-5 5-1 1-1 2-1 3-2 2-4 4-5 7-1 2-2 3-4 5h0l-1-1c0-1 1-1 1-2v-1l2-4 1-1 1-1 1-3 1-1c1-1 1-2 2-3v1c1 0 1-1 1-1l6-6c0-1 1-3 2-3z" class="B"></path><path d="M418 533l1-3c1-1 1-2 1-3h1c1-2 2-4 3-5v-1c1-1 1-1 2-1l1 1c-2 2-4 4-5 7-1 2-2 3-4 5z" class="G"></path><path d="M435 507c0-1 1-2 2-2h0v1h1c0 2-2 3-2 4v1c-1 1-2 1-3 2-1 2-4 3-5 5v-1l5-6v-1c0-1 1-3 2-3z" class="E"></path><path d="M435 507v1c0 1-1 2-2 3v-1c0-1 1-3 2-3z" class="D"></path><path d="M417 532l1 1c-1 3-5 9-4 11h0c1 1 1 1 0 2-1 2-2 6-2 9h0c1-2 1-4 2-6 1-3 3-5 3-7h1l-1 2c-2 5-5 14-3 19 1-2 2-6 3-8 1 1 1 2 1 2-1 1-1 2-1 3-1 1-1 2-1 2v1-2c-1 1-1 2-2 3-1 2-1 5-2 8-1-1-2-2-2-4 1-1 0-2 0-4 1-1 1-4 1-5v-2l-1-1c1-3 1-5 2-7 1-6 3-12 5-17z" class="T"></path><path d="M431 530c0 1 1 1 1 2v1c-4 3-7 7-10 11-1 1-2 3-3 4v2l-1 2-1 3h0c-1 2-2 6-3 8-2-5 1-14 3-19 1 0 2 0 2-1 1 0 1-1 1-2l4-5c1-2 4-3 5-5l2-1z" class="H"></path><path d="M448 504c1 0 2 0 3-1 2 1 5 1 6 1h1l1 1c-2 3-4 3-7 4l-2 1 1 1v1l-4 2c1 1 2 1 3 2v1l-1 1-1 2c-2 1-5 4-7 4h-2v1l-3 1-1 2h-2c-1 0 0 1-1 0h0v-1c-1 0-1-1 0-2v-1h0l1-2-1-1-2 2v-1l6-6 1-2h0c-1 0-2 0-3-1h-1c1-1 2-1 3-2v-1c0-1 2-2 2-4l1-1c1 0 0 1 1 1 1 1 2 0 2 2h0l6-4z" class="d"></path><path d="M436 511h1c0 1-1 1-1 2h0c1 0 1 0 2 1l-1 1-1 1 1-2h0c-1 0-2 0-3-1h-1c1-1 2-1 3-2z" class="C"></path><path d="M443 512v1l-2 2 1 1-2 1h0c-1-1-1-1-2 0v-2c2-1 3-2 5-3z" class="T"></path><path d="M444 508h1c2-1 5-2 7-3v-1h5c-2 1-3 1-5 1 0 1-1 2-2 2h-2c-2 2-1 2-4 1h0z" class="D"></path><path d="M448 504c1 0 2 0 3-1 2 1 5 1 6 1h0-5v1c-2 1-5 2-7 3h-1l-2 1h0v-1l6-4z" class="K"></path><path d="M439 505c1 0 0 1 1 1 1 1 2 0 2 2h0v1h0c-1 1-1 1-2 1h-1l1-1h-1c-1 0-2 1-3 1 0-1 2-2 2-4l1-1z" class="D"></path><path d="M445 511h0c1 0 1 0 2-1h3 0l1 1v1l-4 2-2 2-1 1h0l-2 1-3 3-1-1c0-2 1-2 2-3h0l2-1-1-1 2-2v-1l2-1z" class="P"></path><path d="M445 511h0c1 0 1 0 2-1h3c-2 1-4 3-5 3h0v-2z" class="B"></path><path d="M440 517h0l2-1-1-1 2-2 1 1c1 0 1-1 2 0-1 1-1 1-3 1 0 1-1 1-1 1v2l-3 3-1-1c0-2 1-2 2-3z" class="Q"></path><path d="M437 515h1v2c1-1 1-1 2 0-1 1-2 1-2 3l1 1-1 1h0c-2 1-2 2-3 4h1l-1 2h-2c-1 0 0 1-1 0h0v-1c-1 0-1-1 0-2v-1h0l1-2-1-1-2 2v-1l6-6 1-1z" class="O"></path><path d="M438 517c1-1 1-1 2 0-1 1-2 1-2 3l1 1-1 1-1-1v-1h0c-1 1-2 2-3 4v-2l4-5z" class="Z"></path><path d="M434 524c1-2 2-3 3-4h0v1l1 1h0c-2 1-2 2-3 4h1l-1 2h-2c-1 0 0 1-1 0h0v-1l2-3z" class="K"></path><path d="M447 514c1 1 2 1 3 2v1l-1 1-1 2c-2 1-5 4-7 4h-2v1l-3 1h-1c1-2 1-3 3-4h0l1-1 3-3 2-1h0l1-1 2-2z" class="q"></path><path d="M444 517l1 2v1h-1c-1 1-3 3-4 3h-2v-1h0l1-1 3-3 2-1z" class="p"></path><path d="M447 514c1 1 2 1 3 2v1l-1 1-1 2h-3v-1l-1-2h0l1-1 2-2z" class="c"></path><path d="M447 514c1 1 2 1 3 2v1c-2 0-3 0-5-1l2-2z" class="W"></path><path d="M449 518h2v2h0l-1 1c0 2-2 5-3 7 1 0 1 0 2 1v1h0 0l1-1 1 2c-1 1-2 3-4 4l-2 2-1 1-2 3-2 1-4 4h-2c-1 1-1 1-2 1l-3 1v-1c-1 0-2 0-2 1-1 0-1 1-1 2l-4 4-1-1h0c-1 1-1 2-1 4l-1 1 1 1c-1 1-2 4-4 5v-1h0v-1s0-1 1-2c0-1 0-2 1-3 0 0 0-1-1-2h0l1-3 1-2v-2c1-1 2-3 3-4 3-4 6-8 10-11v-1c0-1-1-1-1-2h1l-1-1 1-1h0c1 1 0 0 1 0h2l1-2 3-1v-1h2c2 0 5-3 7-4l1-2z" class="X"></path><path d="M439 525c-1 1-1 2-2 4-1 1-3 3-5 4h0v-1c0-1-1-1-1-2h1l-1-1 1-1h0c1 1 0 0 1 0h2l1-2 3-1z" class="P"></path><path d="M432 530h0c1-1 2-1 3-1h0c-1 2-2 2-3 3 0-1-1-1-1-2h1z" class="G"></path><path d="M450 521h0c0 2-2 5-3 7-1 1-2 3-3 3h-1c-1 0-3 1-4 2l8-9 3-3z" class="E"></path><path d="M449 518h2v2h0l-1 1h0l-3 3h-2l-2 2c-1 0-1 0-3 1-1 0-2 1-3 2 1-2 1-3 2-4v-1h2c2 0 5-3 7-4l1-2z" class="D"></path><path d="M445 524l1-1 3-3 1 1-3 3h-2z" class="B"></path><path d="M444 531c1 0 2-2 3-3 1 0 1 0 2 1v1h0 0l1-1 1 2c-1 1-2 3-4 4l-2 2-1 1-1-1c0-1 1-1 0-2h-1v-1h0c1-1 1-2 2-3z" class="e"></path><path d="M450 529l1 2c-1 1-2 3-4 4l-2 2s-1-1 0-2c0-1 1-2 2-3h0l2-2h0l1-1z" class="I"></path><path d="M432 533c-2 2-3 4-5 6h0c-1 1-1 2-1 3 3-2 5-5 7-7 2-1 4-2 5-4h0c0 2 0 2-1 3s-3 2-4 3c-1 2-3 3-4 4-2 2-3 4-5 6 0 2-1 4-3 6-1 1-1 2-1 4l-1 1 1 1c-1 1-2 4-4 5v-1h0v-1s0-1 1-2c0-1 0-2 1-3 0 0 0-1-1-2h0l1-3 1-2v-2c1-1 2-3 3-4 3-4 6-8 10-11h0z" class="L"></path><path d="M424 547c0 2-1 4-3 6 0-1 0-2 1-3 0-2 1-2 2-3z" class="d"></path><path d="M419 550v-2c1-1 2-3 3-4 0 2-1 4-2 6h-1z" class="B"></path><path d="M437 534h3c1-1 1-2 2-2v1 1h0v1h1c1 1 0 1 0 2l1 1-2 3-2 1-4 4h-2c-1 1-1 1-2 1l-3 1v-1c-1 0-2 0-2 1-1 0-1 1-1 2l-4 4-1-1h0c2-2 3-4 3-6 2-2 3-4 5-6 1-1 3-2 4-4 1-1 3-2 4-3z" class="U"></path><path d="M434 546c0-1 2-3 3-4l3-3v3l-4 4h-2z" class="B"></path><path d="M440 539c1-1 1-1 1-2-1-1-10 8-11 9l-1-1c2-2 4-3 6-5l7-6v1h1c1 1 0 1 0 2l1 1-2 3-2 1v-3zM340 639c2-1 5-1 7-1h0c1 1 1 1 1 2l1 1c1 0 1 0 1-1l1-1 1-1 4 2 1 2c0 1 1 2 1 3l-1 2v-1l-1 1 1 4h-1 0-1c1 2 2 4 4 6l-1 1h-1 1v1c0 1 1 1 1 1 0 1 0 1 1 1h0c0 1 1 2 1 3l-1 1h0v4c1 1 0 3-1 4h0c-1 2-2 3-4 4v1c-1 1-3 1-5 1l-1 1h1v1l1 1c-1 0-2-1-2 0l-1 1h0v1c-1 0-1-1-1-2v-1l-1 1c-1 2-2 3-4 4l-1 1h-1c-1 1-2 1-4 1h-2c-2 1-3 2-4 3l-1-1c-1 0-3-1-5-2-3-1-5-4-6-7l1-2c0-1-1-1 0-2v-2h-1l2-2c0-1 0-1 1-2h0 1v-1h1l-2-2 1-1 3-2c1-1 2-3 4-4 0-1 0-1 1-2h2c1-1 1-2 2-3l3-5h0c1-3 4-10 3-12z" class="D"></path><path d="M327 675c-1-1-1-2-1-3s0-1 1-2v1c0 1 1 2 1 3h0c-1 0-1 1-1 1z" class="B"></path><path d="M329 661v1h0c-1 1-1 2-2 3-1 2-3 3-4 5l-2-2 1-1 3-2c1-1 2-3 4-4z" class="q"></path><path d="M318 675l2-2c0-1 0-1 1-2h0 1v-1l1 2h-1l-1-1v1 1c-1 1 0 7 0 9v1c-1-1-1-2-2-4 0-1-1-1 0-2v-2h-1z" class="I"></path><path d="M334 656v2c0 2-1 2-1 4h1c1 0 1 1 1 1 1 1 1 1 1 2 0 0 0 1-1 1h0l-1-1h0c0 3 0 6 3 9v1h-1c-1-1-2-2-3-4 0-4-1-7 0-11h0c-1 2-2 3-3 5h0v6c-1-2-1-3-1-5 0-1-1-1-2-1 1-1 1-2 2-3h0v-1c0-1 0-1 1-2h2c1-1 1-2 2-3z" class="i"></path><path d="M330 659h2c-1 2-2 3-2 5h0c-1 1-1 1-1 2 0-1-1-1-2-1 1-1 1-2 2-3h0v-1c0-1 0-1 1-2z" class="F"></path><path d="M327 675s0-1 1-1c1 2 3 4 4 5 1 0 2 1 3 1v1h3l1 1h2 5c-1 2-2 3-4 4l-1 1h-1v-1c-1-1-2-1-3-1-2-1-8-3-9-6l-1-4z" class="H"></path><path d="M341 681c-1 0-2 0-4-1h0c-1 0-2 0-2-1h-1c-1-1-3-2-4-4v-1h-1v-1c2 1 3 2 4 3l1 1c1 0 2 1 3 1 0-1-1-1-2-2-1 0-3-2-4-3s-1-2-1-4h1v1c1 1 1 1 2 1 1 2 2 3 3 4h1c1 0 2 1 3 2 2 2 3 2 5 1 1 0 2 0 3-1l1 1c2 1 4 0 6-1v1c-1 1-3 1-5 1l-1 1h1v1l1 1c-1 0-2-1-2 0l-1 1h0v1c-1 0-1-1-1-2v-1l-1 1h-5v-1z" class="G"></path><path d="M347 679l1 1h1 1v1l1 1c-1 0-2-1-2 0l-1 1h0v1c-1 0-1-1-1-2v-1l-1 1h-5v-1h4s0-1 1-1h1v-1z" class="C"></path><path d="M337 675c1 0 2 1 3 2 2 2 3 2 5 1 1 0 2 0 3-1l1 1c2 1 4 0 6-1v1c-1 1-3 1-5 1l-1 1h-1l-1-1h-1c-1 0-2 1-3 1-3 0-5-3-7-5h1z" class="J"></path><path d="M327 686l-2-2c0-1-1-2-2-3v-2h1c-1-3-1-6-1-9 2 2 2 4 2 7 3 4 6 6 11 9l1-1c1 0 2 0 3 1v1c-1 1-2 1-4 1h-2c-2 1-3 2-4 3l-1-1c-1 0-3-1-5-2-3-1-5-4-6-7l1-2c1 2 1 3 2 4 0 1 0 1 1 1 1 1 2 2 4 2h1z" class="B"></path><path d="M340 686v1c-1 1-2 1-4 1h0v-2h1c1 1 0 1 2 1l1-1z" class="C"></path><path d="M329 690h1c-1-1-1-1-1-2h3 4 0-2c-2 1-3 2-4 3l-1-1z" class="H"></path><path d="M319 679c1 2 1 3 2 4 0 1 0 1 1 1 1 1 2 2 4 2h1 1 1 0l1 1h-1 0 0v1 1c-2 0-3-1-5-1-3-1-5-4-6-7l1-2z" class="M"></path><path d="M337 675v-1c-3-3-3-6-3-9h0l1 1h0c1 0 1-1 1-1l4 2c2 1 3 2 5 2h2l2 2h0v3l-1 1v2c-1 1-2 1-3 1-2 1-3 1-5-1-1-1-2-2-3-2z" class="H"></path><path d="M345 669h2l2 2h-5v-2h1z" class="B"></path><path d="M337 671v-2-1c1 0 2 1 3 1l-1 2c1 0 1 1 2 2v1h0s-1 0-1-1c-1-1-2-2-3-2h0zm3 6c3 0 4-1 7-1h0l1-1v2c-1 1-2 1-3 1-2 1-3 1-5-1z" class="C"></path><path d="M336 665l4 2c2 1 3 2 5 2h-1v2c-1 0-3-1-4-2-1 0-2-1-3-1v1 2c-1-1-1-2-2-3v-2h0c1 0 1-1 1-1z" class="D"></path><path d="M356 663c0-1 0-1 1-2 0 1 0 2 1 3v-2h1l1 3h0v4c1 1 0 3-1 4h0c-1 2-2 3-4 4s-4 2-6 1l-1-1v-2l1-1v-3h0l-2-2v-1l4-1s0-1 1-1l1-1c1-1 1-2 2-3l1 1z" class="G"></path><path d="M349 674c1 1 0 2 0 4l-1-1v-2l1-1z" class="M"></path><path d="M358 664v-2h1l1 3h0v4c1 1 0 3-1 4h0 0-1c-1 0-2 0-2-1h0 1 1c1-1 1-2 1-3 0-2 0-3-2-3 0 0 0-2 1-2z" class="D"></path><path d="M356 663c0-1 0-1 1-2 0 1 0 2 1 3-1 0-1 2-1 2 0 2-1 2-2 4-1 1-2 2-4 3h0v-1c2 0 4-2 4-4h0 0l-2 2-1-1 1-1v-1h0c1-1 1-1 1-2l2-2z" class="e"></path><path d="M352 666l1-1c1-1 1-2 2-3l1 1-2 2c0 1 0 1-1 2h0v1l-1 1 1 1c-1 1-2 1-4 1h0l-2-2v-1l4-1s0-1 1-1z" class="I"></path><path d="M353 667v1l-1 1 1 1c-1 1-2 1-4 1h0l-2-2h2c1-1 2-1 4-2z" class="M"></path><path d="M340 639c2-1 5-1 7-1h0c1 1 1 1 1 2l1 1c1 0 1 0 1-1l1-1 1-1 4 2 1 2c0 1 1 2 1 3l-1 2v-1l-1 1 1 4h-1 0-1c1 2 2 4 4 6l-1 1h-1 1v1c0 1 1 1 1 1 0 1 0 1 1 1h0c0 1 1 2 1 3l-1 1-1-3h-1v2c-1-1-1-2-1-3-1 1-1 1-1 2l-1-1c-1 1-1 2-2 3l-1 1c-1 0-1 1-1 1l-4 1v1h-2c-2 0-3-1-5-2l-4-2c0-1 0-1-1-2 0 0 0-1-1-1h-1c0-2 1-2 1-4v-2l3-5h0c1-3 4-10 3-12z" class="Z"></path><path d="M351 644v1c0 2 2 4 2 6-1-1-1-2-2-3-1 1 0 1 0 2s-1 2-2 2c0 1 0 1-1 2h0l-2 1v-1h0v-1c0-1-1-1-1-1v-1l1-1h1l1 1h0c2-2 2-4 3-7z" class="V"></path><path d="M349 655h1v-1c1 0 1 1 1 2l1-1v-3c1 1 1 3 2 4 0 2 0 2-1 4-1 1-2 1-2 2h-2v-1c1-1 1-2 1-3-1-1-1-2-2-3h0 1z" class="G"></path><path d="M352 638l4 2 1 2c0 1 1 2 1 3l-1 2v-1l-1 1 1 4h-1 0-1c0-3-1-5-2-8 0-1-1-2-2-3 0-1 0-1 1-2z" class="J"></path><path d="M357 642c0 1 1 2 1 3l-1 2v-1l-1 1v-1c0-2 0-3 1-4z" class="B"></path><defs><linearGradient id="M" x1="338.546" y1="640.069" x2="342.076" y2="649.249" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#M)" d="M340 639c2-1 5-1 7-1h0c1 1 1 1 1 2l1 1c1 0 1 0 1-1 0 1 0 3 1 4-1 3-1 5-3 7h0l-1-1h-1l-1 1v1s1 0 1 1v1c-2-1-3 0-5-1l-2-2h0c-1 1-1 1-2 0h0c1-3 4-10 3-12z"></path><path d="M341 651v-4l1-2c0 1 0 2 1 3 0 1 0 2 1 2-1 0-2 1-3 1z" class="K"></path><path d="M341 651c1 0 2-1 3-1h2l-1 1v1s1 0 1 1v1c-2-1-3 0-5-1l-2-2h0 2z" class="Y"></path><path d="M342 645c0-2 2-4 2-6h0c1-1 2-1 3-1 1 1 1 1 1 2h0l-1-1c-1 0-1 0-2 1l-1 4 1 1h-1v2h-1v1c-1-1-1-2-1-3z" class="F"></path><path d="M345 645l-1-1 1-4c1-1 1-1 2-1l1 1v1 4c-1 1-2 0-3 0z" class="e"></path><path d="M348 640l1 1c1 0 1 0 1-1 0 1 0 3 1 4-1 3-1 5-3 7h0l-1-1h-1-2c-1 0-1-1-1-2v-1h1v-2h1c1 0 2 1 3 0v-4-1h0z" class="n"></path><path d="M348 641v7 1c0-1-1-2-1-2h-2-1v-2h1c1 0 2 1 3 0v-4z" class="j"></path><path d="M347 647s1 1 1 2l-1 1h-1-2c-1 0-1-1-1-2v-1h1 1 2z" class="I"></path><path d="M345 647h2v1l-1 1c-1-1-1-1-1-2z" class="E"></path><path d="M334 656l3-5c1 1 1 1 2 0h0l2 2c2 1 3 0 5 1h0v1l2-1h0l1 1h-1 0c1 1 1 2 2 3 0 1 0 2-1 3v1h2c0-1 1-1 2-2 1-2 1-2 1-4 1 0 1 1 2 2l-1 1v2 1c-1 1-1 2-2 3l-1 1c-1 0-1 1-1 1l-4 1v1h-2c-2 0-3-1-5-2l-4-2c0-1 0-1-1-2 0 0 0-1-1-1h-1c0-2 1-2 1-4v-2z" class="D"></path><path d="M348 665h1c1-1 1-2 2-3 0 2 1 3 1 4-1 0-1 1-1 1h-2v-1h-3 1l1-1z" class="X"></path><path d="M340 666c2 1 5 2 7 2v1h-2c-2 0-3-1-5-2v-1z" class="R"></path><path d="M334 656l3-5c1 1 1 1 2 0h0c0 1-1 2-1 2 0 1-1 2-2 4 0 2 0 3 1 5 0 1 1 2 2 3l1 1h0v1l-4-2c0-1 0-1-1-2 0 0 0-1-1-1h-1c0-2 1-2 1-4v-2z" class="c"></path><path d="M354 656c1 0 1 1 2 2l-1 1v2 1c-1 1-1 2-2 3l-1 1c0-1-1-2-1-4-1 1-1 2-2 3h-1-3c-3 0-4-1-5-3h1 1v1c1 1 3 1 5 1l1-1c1 0 2 0 3-1 0-1 1-1 2-2 1-2 1-2 1-4z" class="Q"></path><path d="M351 662c1 0 3-2 3-3h1v2 1c-1 1-1 2-2 3l-1 1c0-1-1-2-1-4z" class="D"></path><path d="M339 651l2 2h-2l3 3c1 0 1 1 2 2-1 1-1 2-2 3v1h-1-1c-2-2-2-3-3-5h-1c1-2 2-3 2-4 0 0 1-1 1-2z" class="j"></path><path d="M339 653l3 3c-1 0-2 1-3 1v-4z" class="Q"></path><path d="M342 656c1 0 1 1 2 2-1 1-1 2-2 3v1h-1c-2-2-2-3-2-5 1 0 2-1 3-1z" class="G"></path><path d="M339 653h2c2 1 3 0 5 1h0v1l2-1h0l1 1h-1 0c1 1 1 2 2 3 0 1 0 2-1 3v1h2c-1 1-2 1-3 1l-1 1c-2 0-4 0-5-1v-1-1c1-1 1-2 2-3-1-1-1-2-2-2l-3-3z" class="D"></path><path d="M346 654v1l2-1h0l1 1h-1 0c1 1 1 2 2 3h-1c-2 0-1-2-3-3v-1z" class="R"></path><path d="M344 658v1c2 0 2 0 4 1l-1 1h-1l-1 1c1 1 1 1 2 1l2-2v1h2c-1 1-2 1-3 1l-1 1c-2 0-4 0-5-1v-1-1c1-1 1-2 2-3z" class="C"></path><path d="M199 527v-2c1 0 3 1 4 1 2 1 6 2 8 1l5 1c1 1 3 1 4 1l-1 1c0 1 1 1 2 1l3 1c1 0 1 1 2 1h-3v1h-1 0c-7-1-14-2-21-1l-10 1h0 1l-1 1c-2 0-2 0-4 1h-1-1l-4 1h-1v1l5-1s-2 1-3 1h-1c-1 1-1 1-2 1s-2 1-2 2l-6 3c-3 1-7 4-10 7-1 1-3 2-4 3-2 1-3 2-4 3-2 3-4 5-5 8h0c-2 6-3 14-1 20l1 7 1-2c1 0 1 0 1 1 1 0 1 1 1 1 1 2 2 4 4 5v1 1 1h0l1 2h0c-3-2-5-5-8-8v1h-1c1 1 1 2 1 3l-1-2c-2 1-2 1-2 2h-1l-1-1c-1 0-3-1-3-3l-1-1c0-1-1-2-1-3v-1c-1-1-1-2-1-3-1-1 0-3-1-5v9c1 5 1 7 5 11h-1c-2-2-4-4-4-7-1 1-1 2-1 3-1 0-1-1-1-2 0 1 0 2-1 2-2-4-2-9-2-13 0-3 0-5 1-8v-3c1-1 1-3 1-4v-5l-1-1c2-3 4-5 6-8s4-5 6-7l5-5 5-3v-1l7-4c3-2 7-3 10-3 1 0 3-1 4-1l12-2c2-1 3-1 4-1 2-1 3 0 5 0l2-2 1 1z" class="U"></path><path d="M134 595l-1-2v-10-3c0-1 1-2 2-2 0-1 0-2 1-3l1 1v1c-1 1-2 3-2 4v10l1 3c-1 1-1 2-1 3-1 0-1-1-1-2zm65-68v-2c1 0 3 1 4 1 2 1 6 2 8 1l5 1c1 1 3 1 4 1l-1 1c0 1 1 1 2 1l3 1c1 0 1 1 2 1h-3l-14-3h-5v1h-1c-1 0-2 1-3 0h0-1c0-1 0-1-1-2h-1l-1-1h0l2-2 1 1z" class="M"></path><path d="M196 528l2-2 1 1-1 1h-2 0z" class="F"></path><path d="M171 532c1 0 3-1 4-1l12-2c2-1 3-1 4-1 2-1 3 0 5 0h0l1 1h-3c-2 0-3 1-5 1l-2 1c1 0 2 0 3-1 1 1 1 0 2 0h3 2l-1 1c-1 1-2 1-3 1h-7c-2 0-4 1-6 1-1 1-2 1-4 1-1 0-2 0-3 1h-1-1v1l-4 1-7 4h0c-1 0-1 0-2 1s-2 2-4 3c-1 1-2 2-3 2v1h-1l-1 1-1 1c-2 1-2 2-4 3-1 1-2 3-3 4 0 0-1 0-1 1l-1 1c0 1-1 1-1 2v1c-1 1-1 2-2 3v1l-2 8c0 1 0 2-1 3v-1-3h-1c1-1 1-3 1-4v-5l-1-1c2-3 4-5 6-8s4-5 6-7l5-5 5-3v-1l7-4c3-2 7-3 10-3z" class="d"></path><path d="M171 532c1 0 3-1 4-1l12-2c2-1 3-1 4-1 2-1 3 0 5 0h0c-5 1-9 2-13 3-7 1-16 2-22 5l-2 1c-1 0-1 1-2 1l-3 2v-1l7-4c3-2 7-3 10-3z" class="I"></path><path d="M191 534h1l-1 1c-2 0-2 0-4 1h-1-1l-4 1h-1v1l5-1s-2 1-3 1h-1c-1 1-1 1-2 1s-2 1-2 2l-6 3c-3 1-7 4-10 7-1 1-3 2-4 3-2 1-3 2-4 3-2 3-4 5-5 8h0c-2 6-3 14-1 20l1 7 1-2c1 0 1 0 1 1 1 0 1 1 1 1 1 2 2 4 4 5v1 1 1h0l1 2h0c-3-2-5-5-8-8v1h-1c1 1 1 2 1 3l-1-2c-2 1-2 1-2 2h-1l-1-1c-1 0-3-1-3-3l-1-1c0-1-1-2-1-3v-1c-1-1-1-2-1-3-1-1 0-3-1-5v9c1 5 1 7 5 11h-1c-2-2-4-4-4-7l-1-3v-10c0-1 1-3 2-4v-1l3-9c4-7 9-13 15-18 4-3 8-6 12-8s10-4 15-5c1-1 3-1 5-2h4z" class="T"></path><path d="M141 579v-5c1 1 1 3 1 5h-1z" class="G"></path><path d="M136 581s0-1 1-1h0c1-2 1-4 2-6 0-3 1-6 3-8 0 3-2 5-2 8-1 5 0 9 1 13 0 3 0 6 1 8l1 2c-1 0-3-1-3-3l-1-1c0-1-1-2-1-3v-1c-1-1-1-2-1-3-1-1 0-3-1-5z" class="C"></path><path d="M141 579h1l3 8v1c0 2 2 4 3 5h1l-1-1 1-2c1 0 1 0 1 1 1 0 1 1 1 1 1 2 2 4 4 5v1 1 1h0l1 2h0c-3-2-5-5-8-8v1h-1c1 1 1 2 1 3l-1-2c-2 1-2 1-2 2h-1l-1-1-1-2 1-1v-3c-2-4-2-8-2-12z" class="B"></path><path d="M143 591c0-2 0-4-1-6h0c1 1 0 2 1 3 1 0 1 1 1 2 1 1 1 1 1 2h0c1 1 2 2 2 3 1 1 1 2 1 3l-1-2c-2 1-2 1-2 2h-1l-1-1-1-2 1-1v-3z" class="G"></path><path d="M143 594c1 2 2 3 2 4h-1l-1-1-1-2 1-1z" class="R"></path><path d="M180 538l5-1s-2 1-3 1h-1c-1 1-1 1-2 1s-2 1-2 2l-6 3c-3 1-7 4-10 7-1 1-3 2-4 3-2 1-3 2-4 3-2 3-4 5-5 8h0c-2 6-3 14-1 20l1 7 1 1h-1c-1-1-3-3-3-5v-1l-3-8c0-2 0-4-1-5 1-4 2-7 4-11 3-6 9-12 15-16 2-2 4-3 6-4 4-3 9-4 14-6v1z" class="U"></path><path d="M180 537v1c-1 1-3 1-4 2-2 0-4 1-7 2l-1 1h0c-2 2-5 4-8 4 2-2 4-3 6-4 4-3 9-4 14-6zm384-276c2-1 6-1 8 0h1l3 1 6 2c7 3 22 14 25 20h-1c4 6 8 11 10 18h0c-1-1-2-3-2-4-1 1 0 2 0 3 1 0 1 1 1 2h-1v1c1 0 1 1 1 1v1h0c1 1 1 2 1 3 1 2 2 4 2 6v2c1 1 1 3 1 4l1 2c1 7 2 14 1 21 0 2 0 4-1 6 0 3 0 6-1 9-1 5-3 10-5 15h0l-2 2-1 1h0l-1-1c3-5 5-12 4-17v-1c1-1 1-3 1-4 1-4 3-9 1-13 0-4 0-8-1-12 0-3 0-7-1-10l-1-3h-1l-1-3h0v1 1l-1-1v1c0-2-1-3-2-4 0-1-1-2-2-4l-2-3c0-1-1-1-1-1l-1-1c-1-2-4-4-6-5l-1-1-1-1h-2v-1-1h3 1 1l1-1c-1-1-3-1-4-2 1 0 2 0 3-1-2-2-4-4-6-5s-3-2-5-3c-5-2-12-2-18 0-3 2-6 4-9 5 2-3 9-6 13-7 0 0 1-1 2-1h0 1l-5-1c-4 1-7 3-11 4-3 1-6 5-9 7l-1-1h0c3-1 5-3 7-6-2 0-3-1-3-2l-1-1s0-1-1-2h0v-4c0-1 0-2 1-3v-1l1-1v-1c1-1 1-2 2-2 2-1 3-2 5-3h1 0 3z" class="H"></path><path d="M564 261c2-1 6-1 8 0h1l3 1 6 2c7 3 22 14 25 20h-1l-1-1h-1l6 11 1 1-1 1h0v-1c-2-3-3-5-4-7-2-3-4-5-6-8-1-2-2-3-3-5-1-1-3-2-4-4l-3-1c-1-1-1-1-2-1l-8-4-1-1-2-1h-1c-1 0-1 0-2-1h0-1c-1 0-3 0-4-1-1 0-3 1-4 0h-1z" class="E"></path><path d="M614 374s3-10 3-11c3-12 4-26 2-38l-2-11-2-7-2-5v-2c-1 0-1-1-2-2l1-1c0 1 1 2 1 3l1 3v1c1 0 1 1 1 1v1h0c1 1 1 2 1 3 1 2 2 4 2 6v2c1 1 1 3 1 4l1 2c1 7 2 14 1 21 0 2 0 4-1 6 0 3 0 6-1 9-1 5-3 10-5 15h0z" class="O"></path><path d="M556 281c1-1 3-1 5-2h-1-1c-2 0-2-1-3-2-2-1-2-3-2-5 0-3 1-5 3-7l1 1h-1 0c-1 2-2 5-2 7s1 3 2 4c3 2 9 0 12 0 5-2 11-1 16 0 1 1 3 1 4 2l5 3c-3-5-11-9-17-12-3-1-5-1-7-2h3 2c2 1 3 2 5 3 5 2 10 5 14 9 0 2 1 3 2 4v1c1 1 1 3 2 4h-1c-2-2-4-4-6-5s-3-2-5-3c-5-2-12-2-18 0-3 2-6 4-9 5 2-3 9-6 13-7 0 0 1-1 2-1h0 1l-5-1c-4 1-7 3-11 4-3 1-6 5-9 7l-1-1h0c3-1 5-3 7-6z" class="I"></path><path d="M570 277c3-1 10 0 13 1 1 0 1 1 2 1-3 0-5-1-8-1-2 0-3 1-5 1 0 0 1-1 2-1h0 1l-5-1z" class="M"></path><path d="M558 270v-1c3-3 8-5 12-5 7 1 14 4 19 8 1 1 3 2 4 3 4 4 7 9 9 14 3 5 7 11 8 17l3 10h-1l-1-3h0v1 1l-1-1v1c0-2-1-3-2-4 0-1-1-2-2-4l-2-3c0-1-1-1-1-1l-1-1c-1-2-4-4-6-5l-1-1-1-1h-2v-1-1h3 1 1l1-1c-1-1-3-1-4-2 1 0 2 0 3-1h1c-1-1-1-3-2-4v-1c-1-1-2-2-2-4-4-4-9-7-14-9-2-1-3-2-5-3h-2-3c-1 1-2 0-4 1h0-1l-2 1c-1 0-1 0-1 1h-2v2c-2-1-1-2-2-3z" class="Z"></path><path d="M597 289h1l2 4c-1 0-1 0-2-1s-3-1-4-2c1 0 2 0 3-1z" class="O"></path><path d="M607 306h0c1-2-1-4-1-7h0l1 2c2 4 4 8 4 12h0v1 1l-1-1v1c0-2-1-3-2-4 0-1-1-2-2-4 1 0 1-1 1-1z" class="L"></path><path d="M598 292c1 1 1 1 2 1 3 4 6 8 7 13 0 0 0 1-1 1l-2-3c0-1-1-1-1-1l-1-1c-1-2-4-4-6-5l-1-1-1-1h-2v-1-1h3 1 1l1-1z" class="M"></path><path d="M592 295v-1-1h3c1 1 2 1 2 2h1c1 1 2 2 2 3v1-1h-1l-2-2h-1-1l-1-1h-2z" class="U"></path><path d="M558 270h1c1-1 2-2 3-2 3-1 6-2 9-2 1 0 3 0 4 1 2 0 4 1 6 2h1c7 3 11 7 15 14h0l-3-3c-4-4-9-7-14-9-2-1-3-2-5-3h-2-3c-1 1-2 0-4 1h0-1l-2 1c-1 0-1 0-1 1h-2v2c-2-1-1-2-2-3z" class="X"></path><path d="M359 647h1v-2h1l2 2c1 1 1 1 2 1 0 1 0 2 1 2 1 1 1 1 2 1v1h1l1 1h3l-1 1h-1l1 1c-1 0-2 1-2 1 1 1 2 2 2 3l1 1v1c1 1 3 2 5 4l2 1s-1 0-1 1l6 3h2v-1l1-1h1 3c7 0 15-5 20-10h3 2 3 1l-1 1s-1 0-1 1h3c3 1 7 4 8 7s1 6 0 9c-2 4-6 6-10 7l-4 1h-11v1c-5-1-9-2-13-3l-9-3c-2-1-5-3-7-4l-1 1-1-1v-1c0-1-1-1-1-1l-1 1c0 2-1 4-1 6h-1v1c-1 1-1 2-2 3l-1-1h0l-5 3h0-1c-1 0-1 0-2-1 0 1-1 1-2 1-1 1-1 1-2 0h-2c-2 0-3-1-5-1v-1-1h0l1-1c0-1 1 0 2 0l-1-1v-1h-1l1-1c2 0 4 0 5-1v-1c2-1 3-2 4-4h0c1-1 2-3 1-4v-4h0l1-1c0-1-1-2-1-3h0c-1 0-1 0-1-1 0 0-1 0-1-1v-1h-1 1l1-1c-2-2-3-4-4-6h1 0 1l-1-4 1-1v1l1-2 1 2z" class="U"></path><path d="M413 660h4c-1 0-2 1-4 2l1 1c-1 0-2 1-3 1v-1c1-1 1-1 2-3z" class="C"></path><path d="M415 658h2 3 1l-1 1s-1 0-1 1h-2-4l2-2z" class="I"></path><path d="M360 656c2 2 3 4 4 7l-5-6 1-1z" class="B"></path><path d="M357 651l3 5-1 1h0c-2-2-3-4-4-6h1 0 1z" class="E"></path><path d="M358 645l1 2c1 1 2 3 3 4v2c-2-2-3-4-5-6l1-2z" class="L"></path><path d="M414 663c3-1 5-1 7 0s4 3 5 4v1c-1 1-1 1-2 1v-2c-1-1-2-1-3-2s-2-1-4-2c-2 1-3 1-5 2l-1-1c1 0 2-1 3-1z" class="I"></path><path d="M417 663c2 1 3 1 4 2s2 1 3 2v2c1 0 1 0 2-1v-1c0 2 1 3 0 5h0c0 2-2 3-4 4h-1v-1c-2 1-4 1-7 2h0c-2-1-3 0-4 0 4-2 8-3 11-6 0 0 0-1-1-1 0-1-1-1-1-2v-1c0-1-1-1-2-2v-2z" class="e"></path><path d="M421 665c1 1 2 1 3 2-1 1 0 1-1 2-1-1-1-1-1-2h0l-1-1v-1z" class="J"></path><path d="M414 677c2-2 4-3 7-4v1 1c-2 1-4 1-7 2z" class="D"></path><path d="M417 663c2 1 3 1 4 2v1l-1 1c1 1 1 2 1 3v1s0-1-1-1c0-1-1-1-1-2v-1c0-1-1-1-2-2v-2z" class="B"></path><path d="M359 657h0l5 6c3 3 6 6 9 8 9 7 21 11 32 13v1c-5-1-9-2-13-3l-9-3c-2-1-5-3-7-4l-1 1-1-1v-1c0-1-1-1-1-1l-1 1c0 2-1 4-1 6h-1v1c-1 1-1 2-2 3l-1-1h0c1-1 2-3 3-4 0-1 1-3 0-4h-1-3v-1l-1 1v-1-1h-2c1-1 1-1 1-2l-1-1v-2-1c0-2 0-2-1-3 0-1-1-1-1-2s0 0-1-1h0 0c-1 0-1 0-1-1 0 0-1 0-1-1v-1h-1 1l1-1z" class="F"></path><path d="M363 668h2v2h0l-1 1-1-1v-2z" class="O"></path><path d="M364 671l1-1h0l1 1h4 0c0 1 0 3 1 4l1-1c0 2-1 4-1 6h-1v1c-1 1-1 2-2 3l-1-1h0c1-1 2-3 3-4 0-1 1-3 0-4h-1-3v-1l-1 1v-1-1h-2c1-1 1-1 1-2z" class="J"></path><path d="M364 671l1-1h0l1 4-1 1v-1-1h-2c1-1 1-1 1-2z" class="B"></path><path d="M359 647h1v-2h1l2 2c1 1 1 1 2 1 0 1 0 2 1 2 1 1 1 1 2 1v1h1l1 1h3l-1 1h-1l1 1c-1 0-2 1-2 1 1 1 2 2 2 3l1 1v1c1 1 3 2 5 4l2 1s-1 0-1 1l6 3v1h-3v1l2 1h0l1 1h2c0 1 1 1 2 1 1 1 2 1 3 1l1 1c1 0 1 0 2 1h0-1s-1 0-2-1c-1 0-1 0-2-1-5-1-9-3-13-6-1 0-1-1-2-2-3-1-5-4-7-6-1-1-3-2-4-4-1-1-1-2 0-3l-2-2v-2c-1-1-2-3-3-4z" class="C"></path><path d="M362 651c3 3 7 6 10 8l1 1v1c1 1 3 2 5 4l2 1s-1 0-1 1c-2-1-4-3-5-4-4-2-7-5-10-8l-2-2v-2z" class="R"></path><path d="M359 647h1v-2h1l2 2c1 1 1 1 2 1 0 1 0 2 1 2 1 1 1 1 2 1v1h1l1 1h3l-1 1h-1l1 1c-1 0-2 1-2 1 1 1 2 2 2 3-3-2-7-5-10-8-1-1-2-3-3-4z" class="W"></path><path d="M370 656c-2-2-7-4-8-6l1-1c1 1 3 2 4 3h1 1l1 1h3l-1 1h-1l1 1c-1 0-2 1-2 1z" class="Q"></path><path d="M360 661h0c1 1 1 0 1 1s1 1 1 2c1 1 1 1 1 3v1 2l1 1c0 1 0 1-1 2h2v1 1l1-1v1h3 1c1 1 0 3 0 4-1 1-2 3-3 4l-5 3h0-1c-1 0-1 0-2-1 0 1-1 1-2 1-1 1-1 1-2 0h-2c-2 0-3-1-5-1v-1-1h0l1-1c0-1 1 0 2 0l-1-1v-1h-1l1-1c2 0 4 0 5-1v-1c2-1 3-2 4-4h0c1-1 2-3 1-4v-4h0l1-1c0-1-1-2-1-3z" class="E"></path><path d="M350 684c2-1 4 0 6-1 1-1 2-1 3-1-3 3-3 3-7 3-1 0-1 0-2-1h0z" class="C"></path><path d="M363 673h2v1 1c-1 3-3 5-6 7-1 0-2 0-3 1-2 1-4 0-6 1l-2-1 1-1c0-1 1 0 2 0l2-1c1 0 1 0 2-1h1c4-1 6-4 7-7z" class="H"></path><path d="M355 686l9-6c2-1 3-3 5-4h1v3c-1 1-2 3-3 4l-5 3h0-1c-1 0-1 0-2-1 0 1-1 1-2 1-1 1-1 1-2 0z" class="G"></path><path d="M360 661h0c1 1 1 0 1 1s1 1 1 2c1 1 1 1 1 3v1 2l1 1c0 1 0 1-1 2-1 3-3 6-7 7h-1c-1 1-1 1-2 1l-2 1-1-1v-1h-1l1-1c2 0 4 0 5-1v-1c2-1 3-2 4-4h0c1-1 2-3 1-4v-4h0l1-1c0-1-1-2-1-3z" class="L"></path><path d="M361 673v1c0 1-2 3-3 4s-1 1-2 1l-1 1h-3c0 1 0 1 1 1l-2 1-1-1v-1h-1l1-1c2 0 4 0 5-1 2-2 4-3 6-5z" class="B"></path><path d="M360 661h0c1 1 1 0 1 1s1 1 1 2c1 1 1 1 1 3h-1c0 2 0 4-1 6h0c-2 2-4 3-6 5v-1c2-1 3-2 4-4h0c1-1 2-3 1-4v-4h0l1-1c0-1-1-2-1-3z" class="I"></path><path d="M389 668h3c7 0 15-5 20-10h3l-2 2c-1 2-1 2-2 3v1l1 1c2-1 3-1 5-2v2c1 1 2 1 2 2v1c0 1 1 1 1 2 1 0 1 1 1 1-3 3-7 4-11 6 1 0 2-1 4 0h0c3-1 5-1 7-2v1c-6 2-14 2-20 0-5-1-11-3-16-5v-1h2v-1l1-1h1z" class="K"></path><path d="M388 668h1 0c1 1 3 1 4 1h1c-1 1-1 1-2 1l-1 1c-1 0-3-1-4-1v-1l1-1z" class="N"></path><path d="M403 673c-2 0-5 0-8-1 2-1 3-1 5-2l1 1h3v1l-1 1z" class="E"></path><path d="M407 664c2-2 4-4 6-4-1 2-1 2-2 3v1l1 1c-1 1-1 1-2 1 0 0-1-1-2-1l-1-1z" class="L"></path><path d="M408 665c1-1 2-1 3-2v1l1 1c-1 1-1 1-2 1 0 0-1-1-2-1z" class="M"></path><path d="M402 670c-2-1-4 0-6 1h-3v-1l9-3v1h3 0c-1 1-2 1-3 2z" class="Q"></path><path d="M402 667l5-3 1 1c1 0 2 1 2 1-1 1-3 1-5 2h0 0-3v-1z" class="J"></path><path d="M387 670c1 0 3 1 4 1h-1c1 1 2 1 3 1 2 1 6 2 8 3v1c-5-1-11-3-16-5v-1h2z" class="c"></path><path d="M405 668v1c-1 1-2 1-4 2 4 0 7-1 10-3 1 0 2 0 3 1h-1l-1 1c-3 2-6 3-9 3l1-1v-1h-3l-1-1h2c1-1 2-1 3-2h0z" class="M"></path><path d="M412 665c2-1 3-1 5-2v2c-1 1-1 3-3 4-1-1-2-1-3-1-3 2-6 3-10 3 2-1 3-1 4-2v-1c2-1 4-1 5-2 1 0 1 0 2-1z" class="C"></path><path d="M417 665c1 1 2 1 2 2v1c0 1 1 1 1 2 1 0 1 1 1 1-3 3-7 4-11 6l-8-2c3-1 7-2 10-4l2-1-1-1h1c2-1 2-3 3-4z" class="H"></path><path d="M417 665c1 1 2 1 2 2v1c-2 0-4 1-4 2l-1 1h-2l2-1-1-1h1c2-1 2-3 3-4z" class="L"></path><defs><linearGradient id="N" x1="475.333" y1="507.669" x2="490.66" y2="524.778" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#N)" d="M474 503h1c3-1 6-4 10-5l2 1-2 1v1h-1-1-1v1h0 1l-1 1v1h1 3c7-3 13-4 20-4 3 0 6 1 10 3 2 1 4 2 6 4s3 4 3 7v2c-1 1-2 1-4 1-1 0-2 0-3 1h-1s-1 0-2 1l-3 3 1 1v1l1 1h-3l-1-1h-1s-1 0-2-1h-3-2c-2-1-3 0-5-1h-2c-4 0-8 0-12 1-5 0-11 2-16 3l-1 1h0-2l-2 1c-1 0-1 0-1 1h0 4l1 1c-2 0-5 1-7 3h0v1h-1l-1 2 1 1-13 4v1c-1-1-2-1-3-1l2-3 1-1 2-2c2-1 3-3 4-4l-1-2-1 1h0 0v-1c-1-1-1-1-2-1 1-2 3-5 3-7l1-1h0v-2h-2l1-1v-1c-1-1-2-1-3-2l4-2v-1l-1-1 2-1c3-1 5-1 7-4l-1-1v-1h1 1 1c1-1 1-1 1-2h0l3 1h3c1 1 1 2 2 2h1 1c1 0 1-1 2-1z"></path><path d="M504 502c1 0 3 0 5 1l1 1h3c-2 0-4 0-6 1l-2 1h-2v-1l1-2v-1z" class="Y"></path><path d="M466 524h0c2-4 8-9 12-11 1-1 3-2 5-3s3-1 4-2 2-1 3-1l-3 3h0c-3 1-5 2-7 4-3 1-5 3-7 5-2 1-5 5-7 5z" class="b"></path><path d="M463 527l4-1-1 1h0-2l-2 1c-1 0-1 0-1 1h0 4l1 1c-2 0-5 1-7 3h0v1h-1l-1 2 1 1-13 4v1c-1-1-2-1-3-1l2-3 1-1 2-2 1 1c3-4 10-7 15-9z" class="D"></path><path d="M450 537h3v-1h0v2c1-1 3-1 3-2h0 0c0-1 1-1 2-2l-1 2 1 1-13 4v1c-1-1-2-1-3-1l2-3 1-1 2-2 1 1-1 1h2 1 0z" class="B"></path><path d="M445 537l2-2 1 1-1 1h2 1 0l-2 1c-2 1-2 2-4 3h1v1c-1-1-2-1-3-1l2-3 1-1z" class="p"></path><defs><linearGradient id="O" x1="470.376" y1="514.825" x2="465.942" y2="508.131" xlink:href="#B"><stop offset="0" stop-color="#d2d0d2"></stop><stop offset="1" stop-color="#f6f5f5"></stop></linearGradient></defs><path fill="url(#O)" d="M474 503h1c3-1 6-4 10-5l2 1-2 1v1h-1-1-1v1h0 1l-1 1v1h1 3c-6 2-11 6-16 9-3 3-7 5-10 7-3 3-6 7-9 11l-1-2-1 1h0 0v-1c-1-1-1-1-2-1 1-2 3-5 3-7l1-1h0v-2h-2l1-1v-1c-1-1-2-1-3-2l4-2v-1l-1-1 2-1c3-1 5-1 7-4l-1-1v-1h1 1 1c1-1 1-1 1-2h0l3 1h3c1 1 1 2 2 2h1 1c1 0 1-1 2-1z"></path><path d="M459 511c3-2 7-4 11-6-3 5-8 8-12 11h0-1l-2 1-1-1c0-1 3-2 4-3 0-1 1-2 1-2z" class="M"></path><path d="M455 517l2-1h1c-1 1-2 2-2 4-1 3-4 6-6 9l-1 1h0 0v-1c-1-1-1-1-2-1 1-2 3-5 3-7l1-1h2c0-1 1-2 2-3z" class="S"></path><path d="M453 520c-1 3-2 8-4 10v-1c-1-1-1-1-2-1 1-2 3-5 3-7l1-1h2z" class="T"></path><path d="M482 502h1l-1 1v1h1 3c-6 2-11 6-16 9h0v-1c-1 0-1 1-2 1-2 1-4 3-5 4-1 0-1 0-2-1 2-2 4-3 6-4 0 0 1-1 2-1v-1h1c1-1 3-2 4-3h1c1-1 2-3 3-3 2 0 3-1 4-2h0z" class="D"></path><path d="M462 501l3 1h3c1 1 1 2 2 2h1l-1 1c-4 2-8 4-11 6 0 0-1 1-1 2-1 1-4 2-4 3l1 1c-1 1-2 2-2 3h-2 0v-2h-2l1-1v-1c-1-1-2-1-3-2l4-2v-1l-1-1 2-1c3-1 5-1 7-4l-1-1v-1h1 1 1c1-1 1-1 1-2h0z" class="g"></path><path d="M452 509h2c2-1 3-1 4-1-1 1-5 2-6 4h-1v-1l-1-1 2-1z" class="E"></path><path d="M462 501l3 1h3c1 1 1 2 2 2-4 1-8 2-12 4-1 0-2 0-4 1h-2c3-1 5-1 7-4l-1-1v-1h1 1 1c1-1 1-1 1-2h0z" class="D"></path><path d="M462 501l3 1h3c-3 2-6 2-9 2l-1-1h1 1 1c1-1 1-1 1-2h0z" class="V"></path><path d="M455 513l4-2s-1 1-1 2c-1 1-4 2-4 3l1 1c-1 1-2 2-2 3h-2 0v-2h-2l1-1v-1c-1-1-2-1-3-2l4-2h1 2l1 1z" class="E"></path><path d="M451 512h1 2l1 1-5 4v-1c-1-1-2-1-3-2l4-2z" class="f"></path><path d="M504 502v1l-1 2v1h2l2-1c2-1 4-1 6-1l3 1h1l-1-2c2 1 4 2 6 4s3 4 3 7v2c-1 1-2 1-4 1-1 0-2 0-3 1h-1s-1 0-2 1l-3 3 1 1v1l1 1h-3l-1-1h-1s-1 0-2-1h-3-2c-2-1-3 0-5-1h-2c-4 0-8 0-12 1-5 0-11 2-16 3l-4 1c1-2 2-2 3-3 2 0 5-4 7-5 2-2 4-4 7-5 2-2 4-3 7-4h0l3-3c3-3 6-4 11-4l3-1z" class="i"></path><path d="M489 521v-1c-2 1-2 1-3 1h-1l6-7c3-2 7-6 11-6l-1 1-3 3c-2 2-5 4-7 6-1 1-1 2-2 3z" class="R"></path><path d="M506 509h1l1 1-7 4h0c-2 1-3 2-4 3s-3 1-4 2c-1 0-2 1-2 2h-2c1-1 1-2 2-3 2-2 5-4 7-6l3-3 2 1c1 0 2-1 3-1z" class="G"></path><path d="M501 509l2 1c-2 1-3 2-5 2l3-3z" class="B"></path><path d="M501 514v1h2v3c-1 0-2 1-3 1v2h1 0c1 1 2 1 4 1h0l1 1h1-3-2c-2-1-3 0-5-1h-2l-1-1h-3 0c0-1 1-2 2-2 1-1 3-1 4-2s2-2 4-3h0z" class="E"></path><path d="M501 515h2v3c-1 0-2 1-3 1v2h-3v-1c0-3 2-3 4-5h0z" class="X"></path><path d="M505 515c0 2 0 3 1 4h1c1 0 2-1 3 0l1 1v2h1 0l1 1v1l1 1h-3l-1-1h-1s-1 0-2-1h-1l-1-1h0c-2 0-3 0-4-1h0-1v-2c1 0 2-1 3-1h0l1-1c0-1 1-1 1-2z" class="B"></path><path d="M501 521c0-1 0-1 1-2 1 1 1 1 1 2-1 1-1 0-2 0h0z" class="C"></path><path d="M487 510h1c-1 1-4 3-4 4v2c-1 1-2 2-2 3l-1 1c-1 0-1 1-1 1l1 1h-5c-1 1-2 1-3 1l-1-1 8-8c2-2 4-3 7-4z" class="I"></path><path d="M504 502v1l-1 2h-2 0l-11 9c-2 2-4 5-6 7-1 0-1 0-2 1h-1 0l-1-1s0-1 1-1l1-1c0-1 1-2 2-3v-2c0-1 3-3 4-4h-1 0l3-3c3-3 6-4 11-4l3-1z" class="E"></path><defs><linearGradient id="P" x1="494.161" y1="506.429" x2="489.008" y2="508.356" xlink:href="#B"><stop offset="0" stop-color="#6d6e6d"></stop><stop offset="1" stop-color="#858384"></stop></linearGradient></defs><path fill="url(#P)" d="M504 502v1l-1 2h-2 0c-2 0-3 0-4 1h-1c-3 2-5 3-7 5l-5 5v-2c0-1 3-3 4-4h-1 0l3-3c3-3 6-4 11-4l3-1z"></path><path d="M504 502v1l-1 2h-2 0c-2 0-3 0-4 1h-1v-1h1c0-1 1-1 1-1 1 0 2 0 2-1h1l3-1z" class="K"></path><path d="M516 503c2 1 4 2 6 4s3 4 3 7v2c-1 1-2 1-4 1-1 0-2 0-3 1h-1s-1 0-2 1l-3 3h0-1v-2l-1-1c-1-1-2 0-3 0h-1c-1-1-1-2-1-4 0 1-1 1-1 2l-1 1h0v-3h-2v-1l7-4-1-1h-1c-1 0-2 1-3 1l-2-1 1-1s2-1 3-2l2-1c2-1 4-1 6-1l3 1h1l-1-2z" class="U"></path><path d="M512 509h6v-1l2 2v1h-1v1l1 1c-2 0-3 0-5 1h-2 0c2-2 3-2 5-3-3 0-6-1-9 2h0c-1 0-3 1-3 2h-1c0 1-1 1-1 2l-1 1h0v-3h-2v-1l7-4h0 1 1c1-1 1-1 2-1z" class="M"></path><path d="M516 503c2 1 4 2 6 4s3 4 3 7v2c-1 1-2 1-4 1-1 0-2 0-3 1h-1s-1 0-2 1l-3 3h0c0-1 1-3 2-4 3-1 5-2 8-2h0l1-1h-2 0v-1h2v-1h-3l-1-1v-1h1v-1l-2-2v1h-6c-1 0-1 0-2 1h-1-1 0l-1-1h-1c-1 0-2 1-3 1l-2-1 1-1s2-1 3-2l2-1c2-1 4-1 6-1l3 1h1l-1-2z" class="F"></path><path d="M516 503c2 1 4 2 6 4v1h-1s-1 0-1-1h0c-1-1-2-1-2-1h-1s0-1-1-1h1l-1-2z" class="N"></path><path d="M507 505h4 1c1 0 1 0 1 1h3v2c-1 1-2 0-4 1-1 0-1 0-2 1h-1-1 0l-1-1h-1c-1 0-2 1-3 1l-2-1 1-1s2-1 3-2l2-1z" class="E"></path><path d="M506 509c2-1 4-2 5-3l1 1c-1 1-2 2-4 3h0l-1-1h-1z" class="d"></path><path d="M69 315c1-4 3-8 5-12l6-12 6-9c4-5 11-10 17-14l3-2c4-2 8-4 12-5 3-1 6-1 9 0h2c1 0 1 0 2 1h2s1 1 2 1v1c2 1 3 2 3 3l1 1c1 1 1 2 2 3 0 0 1 1 1 2v4h0c0 1 0 1-1 2 0 1-1 2-2 2l-2 3-4-3c2 0 3 0 4-2h0l1-1v-2-1-2c-1-1-1-1-1-2-1-1-1 0-1-1h0l-2-2c-1 0-3-1-4-2h-2 0c-1 0-1-1-1-1-2 0-4 0-5 1 0 1 0 1-1 2l-1 1c1 0 2 1 2 1-6-1-13 1-18 5l-7 7c-3 3-6 8-8 12l2-1c0 1 0 1 1 1l2-2h1l-3 3 1 1h1l-1 2c-3 3-4 7-6 11-1 2-3 4-4 7l-1 3-1 5c0 3-1 6-1 10 1 1 1 4 1 6v-2c0 3 0 5 1 8v4c-1 2 0 5 0 7v3c0 1 0 2 1 4v7c0 5 3 10 5 15 1 2 2 5 4 7l-1 1v1l-1-1v3c-2-1-4-3-5-5h1c0-1-1-1-1-2l1-1c-3-3-4-7-7-9l-6-9c-2-5-4-10-6-16v-2c-1-2 0-4-1-7v-8c1-1 1-1 1-2 1-5 0-9 1-14l2-6-1-1z" class="H"></path><path d="M122 266c0 1 0 1-1 2l-1 1c1 0 2 1 2 1-6-1-13 1-18 5l-7 7c-3 3-6 8-8 12s-5 8-6 13l-4 11-1 5-1-1c3-7 4-15 7-22 3-8 9-16 15-22 2-2 4-3 6-5 3-1 7-3 11-4h2 2c0-1 1-2 2-3z" class="Y"></path><path d="M91 293c0 1 0 1 1 1l2-2h1l-3 3 1 1h1l-1 2c-3 3-4 7-6 11-1 2-3 4-4 7l-1 3-1 1v-2h0-2l4-11c1-5 4-9 6-13l2-1z" class="C"></path><defs><linearGradient id="Q" x1="84.995" y1="306.062" x2="80.412" y2="318.298" xlink:href="#B"><stop offset="0" stop-color="#b7b7b7"></stop><stop offset="1" stop-color="#e4e3e3"></stop></linearGradient></defs><path fill="url(#Q)" d="M83 307l2-2c0-1 1-1 2-1-2 3-4 7-4 11v1l-1 3-1 1v-2h0-2l4-11z"></path><path d="M91 293c0 1 0 1 1 1l2-2h1l-3 3c-1 2-2 4-4 6 0 1-1 2-1 3-1 0-2 0-2 1l-2 2c1-5 4-9 6-13l2-1zm-22 22c1-4 3-8 5-12l6-12 6-9c4-5 11-10 17-14l3-2c4-2 8-4 12-5 3-1 6-1 9 0h0-2c-1 0-2 0-4 1h-4l-2 1-3 1c-1 1-2 1-3 2-4 2-7 4-11 7-2 2-3 4-5 6l-9 12c-1 2-2 5-3 7-3 6-5 13-7 20l-1 3h-1c1-4 2-6 3-9l6-16v-1-2l-2 2c-1 1-2 3-2 4l-2 2-5 14v1l-1-1z" class="Q"></path><path d="M79 295c3-6 6-12 12-16-2 4-6 8-8 12-1 1-1 3-2 5v-1-2l-2 2z" class="X"></path><path d="M79 295l2-2v2 1l-6 16c-1 3-2 5-3 9h1l-1 11c-1 3 0 7 0 10 0 6 0 12 2 18 1 4 1 7 2 10 0 2 2 3 2 5 1 1 1 2 2 3h-1v2l-6-9c-2-5-4-10-6-16v-2c-1-2 0-4-1-7v-8c1-1 1-1 1-2 1-5 0-9 1-14l2-6v-1l5-14 2-2c0-1 1-3 2-4z" class="C"></path><path d="M79 378h0c-2-3-4-7-5-11 0-1 0-3-1-5 0-3-2-6-2-9-1-2 0-5 0-7 0-9 0-17 1-25h1l-1 11c-1 3 0 7 0 10 0 6 0 12 2 18 1 4 1 7 2 10 0 2 2 3 2 5 1 1 1 2 2 3h-1z" class="J"></path><path d="M81 318h0v2l1-1-1 5c0 3-1 6-1 10 1 1 1 4 1 6v-2c0 3 0 5 1 8v4c-1 2 0 5 0 7v3c0 1 0 2 1 4v7c0 5 3 10 5 15 1 2 2 5 4 7l-1 1v1l-1-1v3c-2-1-4-3-5-5h1c0-1-1-1-1-2l1-1c-3-3-4-7-7-9v-2h1c-1-1-1-2-2-3 0-2-2-3-2-5-1-3-1-6-2-10-2-6-2-12-2-18 0-3-1-7 0-10 2 6 0 14 0 20 1 1 1 2 1 3v-2-1-1c-1-2 0-5 0-7v-1h0l1 1h0v-1h0c1-2 1-4 1-6v-1h0v-4-2-1c0-1 1-2 1-3l1-3v-1l1 1 1-5h2z" class="U"></path><path d="M77 355h1c2 5 4 11 5 16 0 5 3 10 5 15 1 2 2 5 4 7l-1 1c-3-4-5-10-7-14l-7-25z" class="K"></path><path d="M72 332c2 6 0 14 0 20 1 1 1 2 1 3v-2-1-1c-1-2 0-5 0-7v-1h0l1 1h0v-1h0c0 1 1 1 0 2 0 3 0 5 1 8v2h0v2 1s1 0 1 1v1 1h0v1c1 0 1 1 1 2v1l1 2v2h0l-1 1v-4l-1-2c0-1 0-2-1-2v-3l-1 1c-2-6-2-12-2-18 0-3-1-7 0-10zm8 46c1 0 2 1 3 2h1c2 4 4 10 7 14v1l-1-1v3c-2-1-4-3-5-5h1c0-1-1-1-1-2l1-1c-3-3-4-7-7-9v-2h1z" class="B"></path><path d="M86 389l4 5v3c-2-1-4-3-5-5h1c0-1-1-1-1-2l1-1z" class="T"></path><path d="M81 318h0v2l1-1-1 5c0 3-1 6-1 10 1 1 1 4 1 6v-2c0 3 0 5 1 8v4c-1 2 0 5 0 7v3c0 1 0 2 1 4v7c-1-5-3-11-5-16h-1v-2c-1-2-2-5-2-7v-9-1h0v-4-2-1c0-1 1-2 1-3l1-3v-1l1 1 1-5h2z" class="O"></path><path d="M81 318h0v2l1-1-1 5c-1 1-1 1-2 1l-1 4v-1c0-2 0-3-1-5v-1l1 1 1-5h2z" class="d"></path><path d="M81 318h0v2l1-1-1 5c-1 1-1 1-2 1 0-3 0-4 2-7z" class="M"></path><path d="M80 334c1 1 1 4 1 6v-2c0 3 0 5 1 8v4c-1 2 0 5 0 7v3c-1-1 0-1-1-1h0l-1-2c-1-3 0-5 0-8 1-5 0-10 0-15z" class="e"></path><defs><linearGradient id="R" x1="72.83" y1="342.011" x2="80.686" y2="337.059" xlink:href="#B"><stop offset="0" stop-color="#bbbdb9"></stop><stop offset="1" stop-color="#d8d4da"></stop></linearGradient></defs><path fill="url(#R)" d="M75 337v-1h0v-4-2-1c0-1 1-2 1-3l1-3c1 2 1 3 1 5v1 2l1 20c0 2 0 3-1 4h-1v-2c-1-2-2-5-2-7v-9z"></path><path d="M78 331l1 20h-1v-5c-1-5-1-10 0-15z" class="I"></path><path d="M138 474c2-1 6-1 7 0 7 0 13 2 19 4 5 1 10 4 16 5 8 3 16 8 24 13l6 4v1l-9-5-3-1h-2c1 1 2 1 2 2l-15-4-1 2h-1l-1 1c-2 1-5 1-8 3 0 1-2 2-3 3h-1l-3 3h0l-2 2v1 1c-2 0-1-1-3 0h0c-1-1-1-2-1-3h0c0-1-1-1-2-2l-1 1-2-1-1 1c-1 0-2 1-3 1-1 2-2 4-3 5v1c0 1 0 3-1 4v1l-1-1c0-1 0-1-1-2-1-2-1-3-2-5l-4-6h0c-4-4-10-5-14-5h-1c-1 0-1 0-2 1v-1l-2-1v1 1c0 1-1 1-2 2l-2 2h-1c-1-2-2-3-3-4v-4-4c-1 0-1-1-1-1 0-1-1-1-1-1 1-2 2-3 3-5h2c0-1 1-1 1-1 2 0 5-2 7-4 1-1 2-1 3-1-1 0-1 1-1 1l-1 3 1-1c2-1 5-3 8-4 2 0 6-1 7-2l1-1h0-2z" class="U"></path><path d="M127 492l5 3h0l-1 1h-1c-1-1-3-2-3-4z" class="X"></path><path d="M126 489h0c1-3 3-4 6-4l1 1c-3 1-5 1-6 3h-1z" class="Q"></path><path d="M148 475h0c2 1 6 1 8 2l-1 1h-3l-11-2c3 0 4 1 7-1z" class="J"></path><path d="M132 485c6-2 11-4 17-5v1c-1 0-3 0-4 1l-4 1c-2 0-3 2-4 3h-4l-1-1z" class="R"></path><path d="M121 492l1-3c2-5 5-9 10-11h0c-1 1-1 2-2 3-1 0-2 1-3 2-1 0-1 1-1 2l-2 2c-1 2-1 4-2 7 0 0 1 0 1 1-1 0-1 0-2-1 1-1 1-1 0-2h0z" class="C"></path><path d="M143 504h1c1 0 2 0 3-1s3-1 4-1l2-2 1 1c-2 2-5 2-7 4h-1v1c1 1 1 1 2 1l2-1c-1 2-2 4-3 5-1-2-2-5-4-7z" class="d"></path><path d="M138 474c2-1 6-1 7 0h0c1 1 2 1 3 1-3 2-4 1-7 1l-9 2c-5 2-8 6-10 11l-1 3c-1-2 0-4 1-6l2-5c2-1 5-3 8-4 2 0 6-1 7-2l1-1h0-2z" class="I"></path><path d="M138 503c1 0 1-1 2-1v-1l1 1 2 2c2 2 3 5 4 7v1c0 1 0 3-1 4v1l-1-1c0-1 0-1-1-2-1-2-1-3-2-5l-4-6z" class="X"></path><path d="M147 512l-2-1c0-2-2-6-4-8v-1l2 2c2 2 3 5 4 7v1z" class="P"></path><path d="M144 489h3c1 0 1 0 2 1h1c1 1 1 2 2 3h0 0l-1-1h-1 0c0 1 0 1 1 2v1h-1c-1 0-1-1-2-1h-2-1-2l-1 1c-1 0-1 1-1 1l-1 1c-1-1-1-1-1-2v-1c0-1 0-2 1-4l4-1z" class="H"></path><path d="M137 486c1-1 2-3 4-3l4-1c-1 1-1 1-1 2 1 1 1 0 2 0s1 0 1-1c2 0 2 1 4 1-1 1-2 1-3 1s-1 0-1 1v1l-3 2-4 1c-1 2-1 3-1 4v1h-1c-2 1-3 1-4 0h-2 0l-5-3s-2-1-2-2l1-1h1c1-2 3-2 6-3h4z" class="E"></path><path d="M137 486c0 1 0 2 1 3-1 0-1 1-1 2l-1-1v-2-1l1-1z" class="X"></path><path d="M132 495v-1l-2-1 1-1v1l1-1v-1l2 2c1-1 1-2 2-3l1 1c-1 1-3 3-3 4h-2 0z" class="d"></path><path d="M136 487v1c-1 1-1 2-2 3-2-1-3-1-5 1h-1c-1-1-1-1-1-2l1-1 1 1c2 0 4-1 6-2l1-1z" class="C"></path><path d="M138 489v-2h1l1 1v2h0c-1 2-1 3-1 4v1h-1c-2 1-3 1-4 0 0-1 2-3 3-4 0-1 0-2 1-2z" class="O"></path><path d="M137 486c1-1 2-3 4-3l4-1c-1 1-1 1-1 2 1 1 1 0 2 0s1 0 1-1c2 0 2 1 4 1-1 1-2 1-3 1s-1 0-1 1v1l-3 2-4 1h0v-2l-1-1h-1v2c-1-1-1-2-1-3h0z" class="M"></path><path d="M147 486v1l-3 2-4 1h0v-2h1 0c2-1 4-2 6-2z" class="I"></path><path d="M114 484c0-1 1-1 1-1 2 0 5-2 7-4 1-1 2-1 3-1-1 0-1 1-1 1l-1 3 1-1-2 5c-1 2-2 4-1 6h0c1 1 1 1 0 2 1 1 1 1 2 1h2v1h-3l1 1 1 1h-1c-1 0-1 0-2 1v-1l-2-1v1 1c0 1-1 1-2 2l-2 2h-1c-1-2-2-3-3-4v-4-4c-1 0-1-1-1-1 0-1-1-1-1-1 1-2 2-3 3-5h2z" class="t"></path><path d="M119 497v1 1c0 1-1 1-2 2l-1-2c1-1 1-1 3-2z" class="d"></path><path d="M112 484h2l-3 7c-1 0-1-1-1-1 0-1-1-1-1-1 1-2 2-3 3-5z" class="L"></path><path d="M124 481l-2 5h-1c-1 1-2 3-2 5v6h0v1-1c-1-1-1-3-1-4 0-4 2-9 5-11l1-1z" class="Q"></path><path d="M119 491c0-2 1-4 2-5h1c-1 2-2 4-1 6h0c1 1 1 1 0 2 1 1 1 1 2 1h2v1h-3l1 1 1 1h-1c-1 0-1 0-2 1v-1l-2-1h0v-6z" class="L"></path><path d="M119 491h1c0 2 0 3 1 5v2h0l-2-1h0v-6z" class="C"></path><path d="M145 474c7 0 13 2 19 4 5 1 10 4 16 5 8 3 16 8 24 13l6 4v1l-9-5-3-1h-2c1 1 2 1 2 2l-15-4-2-1h0l-5-2-2-2c-1-1-3-1-4-2h0c-2-1-4-1-6-2-4-1-9-1-13 0-2 0-2-1-4-1 0 1 0 1-1 1s-1 1-2 0c0-1 0-1 1-2s3-1 4-1v-1c1 0 3-1 5-1v-1h-2 3l1-1c-2-1-6-1-8-2h0c-1 0-2 0-3-1h0z" class="F"></path><path d="M195 492l3 3h-2c-1 0-1-1-2-2l1-1z" class="D"></path><defs><linearGradient id="S" x1="165.48" y1="477.371" x2="161.349" y2="483.265" xlink:href="#B"><stop offset="0" stop-color="#cdd0cf"></stop><stop offset="1" stop-color="#f4edef"></stop></linearGradient></defs><path fill="url(#S)" d="M152 478h3c3 0 6 0 9 1s7 2 10 3v1c-4 0-7-1-10-2-1 0-3 0-4-1h-1c-2 0-3-1-5-1v-1h-2z"></path><path d="M160 480c1 1 3 1 4 1 3 1 6 2 10 2v-1c3 1 7 1 8 3 1 0 1 1 2 1l1 1c0 1-1 2-2 3l-1-1c-2-2-4-3-7-4-2-1-5-2-8-2-2-1-5-2-7-2v-1z" class="T"></path><path d="M182 489l1-1c0-1-1-1 0-2h1l1 1c0 1-1 2-2 3l-1-1z" class="E"></path><path d="M174 483v-1c3 1 7 1 8 3v1c-2 0-4-1-5-2-1 0-2 0-3-1zm1 2c3 1 5 2 7 4l1 1c1-1 2-2 2-3 1 0 2 1 3 1h-1v1c0 1 0 1 1 2l1 1h-1c-2 0-4-1-6-1h0c-1 1 0 1-1 1h0l-5-2-2-2h2v-1h3c-1 0-2-1-4-1v-1z" class="M"></path><path d="M185 487c1 0 2 1 3 1h-1v1c0 1 0 1 1 2-2 0-3-1-5-1 1-1 2-2 2-3z" class="I"></path><path d="M187 488l3 2c1 0 4 2 5 2l-1 1c1 1 1 2 2 2 1 1 2 1 2 2l-15-4-2-1c1 0 0 0 1-1h0c2 0 4 1 6 1h1l-1-1c-1-1-1-1-1-2v-1z" class="J"></path><path d="M154 479c2 0 3 1 5 1h1v1c2 0 5 1 7 2 3 0 6 1 8 2v1c2 0 3 1 4 1h-3v1h-2c-1-1-3-1-4-2h0c-2-1-4-1-6-2-4-1-9-1-13 0-2 0-2-1-4-1 0 1 0 1-1 1s-1 1-2 0c0-1 0-1 1-2s3-1 4-1v-1c1 0 3-1 5-1z" class="U"></path><path d="M154 479c2 0 3 1 5 1h1v1c-2 0-4-1-6-1l-5 1v-1c1 0 3-1 5-1z" class="C"></path><path d="M151 484c4-1 9-1 13 0 2 1 4 1 6 2h0c1 1 3 1 4 2l2 2 5 2h0l2 1-1 2h-1l-1 1c-2 1-5 1-8 3 0 1-2 2-3 3h-1l-3 3h0l-2 2v1 1c-2 0-1-1-3 0h0c-1-1-1-2-1-3h0c0-1-1-1-2-2 1 0 1 0 1-1h-1l-1-1v1l-1-1c1-1 1-1 1-2 0-2-1-3-2-4h0l-2-3h0c-1-1-1-2-2-3h-1c-1-1-1-1-2-1h-3l3-2v-1c0-1 0-1 1-1s2 0 3-1z" class="B"></path><path d="M150 490c0-1 0-1 1-2l1 1h2 1l1 1h-4c1 2 2 4 2 6l-2-3h0c-1-1-1-2-2-3z" class="T"></path><path d="M154 489h0 0v-1l1-1h3c2 0 5 0 7 1h-3 0v1h3c-1 1-2 1-3 2-1 0-3 1-4 2 0 1-1 1-1 2h-1v-1c1-1 3-2 4-4h0c-1 0-2 0-3 1 0 0 0 1-1 1l-1-1 1-1h0l-1-1h-1z" class="P"></path><path d="M163 497v1c1 0 1 1 1 2l2-1c1 1 1 1 2 1v2l-3 3h0l-2 2c0-1-1-1-1-2-1-2-3-3-5-4v-2c1-1 3-1 5-1h1v-1z" class="U"></path><path d="M151 484c4-1 9-1 13 0 2 1 4 1 6 2h0c1 1 3 1 4 2l2 2-1 1h-1v1l-7-2-2-1h-3v-1h0 3c-2-1-5-1-7-1h-3l-1 1v1h0 0-2l-1-1c-1 1-1 1-1 2h-1c-1-1-1-1-2-1h-3l3-2v-1c0-1 0-1 1-1s2 0 3-1z" class="J"></path><path d="M170 486c1 1 3 1 4 2l2 2-1 1h-1c-1 0-2-1-2-1-1-2-2-2-3-3l1-1z" class="Q"></path><path d="M165 489h-3v-1h0 3 2c1 1 3 1 5 2 0 0 1 1 2 1v1l-7-2-2-1z" class="I"></path><path d="M147 487l6-2h9c2 1 4 1 6 2 0 0 0 1 1 1h-2-2c-2-1-5-1-7-1h-3l-1 1v1h0 0-2l-1-1c-1 1-1 1-1 2h-1c-1-1-1-1-2-1h-3l3-2z" class="D"></path><path d="M165 489l2 1 7 2v-1h1l1-1 5 2h0l2 1-1 2h-1l-1 1c-2 1-5 1-8 3 0 1-2 2-3 3h-1v-2c-1 0-1 0-2-1l-2 1c0-1 0-2-1-2v-1 1l-1-2h-1l-1 1c-1 0-2 0-3-1v-1c0-1 1-1 1-2 1-1 3-2 4-2 1-1 2-1 3-2z" class="G"></path><path d="M181 492h0l2 1-1 2h-1l-3-1c1-1 1-2 3-2z" class="i"></path><path d="M176 490l5 2c-2 0-2 1-3 2l-4-2v-1h1l1-1z" class="Z"></path><path d="M165 489l2 1h-2v2c-2 1-3 2-5 2h0c-1-1-1-1-2-1 1-1 3-2 4-2 1-1 2-1 3-2z" class="C"></path><path d="M163 497l1-1h2c0-1 1-1 1-1l1-1h-1c1-1 1-1 2-1h1 1l1 1c0 1-1 1-1 2h0c-2 1-4 3-5 3l-2 1c0-1 0-2-1-2v-1z" class="d"></path><path d="M539 478c7-1 16-3 22 1 2 1 3 2 4 3h2s1 0 1 1c1 2 2 4 4 5h0 0c1 1 1 3 2 4 0 2 0 3-2 4l-1 1c1 1 1 2 0 4 0-1-1-1-2-2-1 1-2 1-3 1h-1 0-6l-1 1c-1 0-2 2-3 3 0-1 0-1-1-1v-1h-2c1-1 1-1 1-2h0c-1 0-2 1-3 2-1 0-2 1-3 3 0 0-1 2-1 3l-1 1c-1 1-2 0-2 1l1 1v2l-4-1-1 1h-1c-1 0-2-1-3-1h0c0 1 1 2 1 3h0-1-1l-1-1v1c-2 1-2 5-3 7v1c-1 2-3 3-4 4l-1 1c0 1-1 1-2 0-1 1-1 1-2 0l-2-1s-1 0-2-1h-1c-1 0-1 0-2-1l-1-1v-1l-1-1 3-3c1-1 2-1 2-1h1c1-1 2-1 3-1 2 0 3 0 4-1v-2c0-3-1-5-3-7s-4-3-6-4c-4-2-7-3-10-3-7 0-13 1-20 4h-3-1v-1l1-1h-1 0v-1h1 1 1v-1l2-1 3-2c1 0 2 0 2-1 1 0 2-1 3-1h0 1l2-1 2-1c1 0 2-1 3-1h0c0 1 0 1-1 2v1h1 4s1-1 2-1c0-1 1-2 1-2l1-1-1-1c2 0 3-1 5-2s4-3 6-4c1 0 3-1 4-1 3-1 5-2 8-3l3-2h3z" class="H"></path><path d="M545 490l3 1h-1c-1 1-1 2-2 2h-1l1-3z" class="L"></path><path d="M568 488c1 1 2 3 1 5h2 1v1s0 1-1 1v1l-3-3v-2h0 0v-3z" class="C"></path><path d="M540 502c1-1 1-1 2-1 2-1 2-1 3-3h0 1c1 1 1 1 0 2-1 0-2 0-2 2h0c-1 1-2 1-4 0zm-6 1c1 0 2-1 2-1h2v2h0v1c-2 0-2 0-3 1-1-1-1-2-1-3z" class="d"></path><path d="M536 502h2v2h0l-1 1-1-1v-2z" class="O"></path><path d="M555 492v2h1c2 0 4 0 6-1l1 1c-2 1-5 2-8 2l-1-1v-3h1z" class="P"></path><path d="M553 500v-1c1 0 2-1 4-1h0 1 2 3c-2 1-8 2-9 4h-2c1-1 1-1 1-2h0z" class="G"></path><path d="M557 491c1 0 2 0 2 1 1 0 2 0 3-1l1 1-1 1c-2 1-4 1-6 1h-1v-2c1 0 2-1 2-1z" class="H"></path><path d="M565 482h2s1 0 1 1v1c1 2 2 5 3 7v2h-2c1-2 0-4-1-5l-3-6zm-2 16c3 0 4 0 6 1-1 1-2 1-3 1h-1 0-6l-1 1c-1 0-2 2-3 3 0-1 0-1-1-1v-1c1-2 7-3 9-4z" class="P"></path><path d="M568 483c1 2 2 4 4 5h0 0c1 1 1 3 2 4 0 2 0 3-2 4l-1 1v-1-1c1 0 1-1 1-1v-1h-1v-2c-1-2-2-5-3-7v-1z" class="D"></path><path d="M540 502c2 1 3 1 4 0 1 1 1 2 2 2 1 1 0 1 1 1 0 0-1 2-1 3-1-1-2-1-4-1 0-1-1-2-1-3-1-1-2 0-3 0v-2h2z" class="L"></path><path d="M541 504h2c0 1 0 2-1 3 0-1-1-2-1-3z" class="B"></path><path d="M557 491c1 0 1-1 1-1l1-1c-1 0-1 0-1-1s0-2 1-3c2 1 5 2 6 4 0 1 1 2 0 3l-2 2-1-1 1-1-1-1c-1 1-2 1-3 1 0-1-1-1-2-1z" class="J"></path><path d="M559 492c1-1 2-2 2-3h0 1 1c-1 1-1 1-1 2h1c1 0 1-1 2-2 0 1 1 2 0 3l-2 2-1-1 1-1-1-1c-1 1-2 1-3 1zm-10-9c2 0 3 1 5 2h1l1 1c1 0 1-1 2-1h1c-1 1-1 2-1 3s0 1 1 1l-1 1s0 1-1 1c0 0-1 1-2 1v2-2h-1l-1-1c0-1 0-3 1-4-1-1-2-1-3-1h0c-1-1-2-2-2-3z" class="B"></path><path d="M554 487c1 2 1 3 1 5h-1l-1-1c0-1 0-3 1-4z" class="F"></path><path d="M551 486h0c1 0 2 0 3 1-1 1-1 3-1 4l1 1v3l-3-2c-2-1-2-2-3-2l-3-1h-2l1-1h3l1-1h1 0l2-2z" class="e"></path><path d="M551 493h0c1-2 0-4 1-6h1v4h0l1 1v3l-3-2z" class="B"></path><path d="M538 504c1 0 2-1 3 0 0 1 1 2 1 3 2 0 3 0 4 1l-1 1c-1 1-2 0-2 1l1 1v2l-4-1c-3-1-5-1-7-1 0-2 1-3 2-5 1-1 1-1 3-1v-1h0z" class="H"></path><path d="M536 478h3c-1 0-3 1-5 2l-4 1c1 0 2 1 2 0h2 0 2c7-1 16 0 22 4-1 0-1 1-2 1l-1-1h-1c-2-1-3-2-5-2 0 1 1 2 2 3l-2 2h0-1l-1 1h-3l-1 1-1 1v-2l-2 1v1h-1l-2-1c-2-2-3-3-6-4l-1-1c-2-1-4-1-6 0l-3 1-1 1c-2 1-3 2-4 3-2 0-3 1-5 1l-1-1c2 0 3-1 5-2s4-3 6-4c1 0 3-1 4-1 3-1 5-2 8-3l3-2z" class="G"></path><path d="M538 485c2-1 4 0 7 0 2 1 2 1 4 3h-1l-1 1h-3l-1 1-1 1v-2-1l-1-1c-1 0-1-1-1-1h-1-1 0v-1z" class="Q"></path><path d="M541 487c1-1 2 0 3 0 2 0 3 1 4 1l-1 1h-3l-1 1-1 1v-2-1l-1-1z" class="D"></path><path d="M536 481c7-1 16 0 22 4-1 0-1 1-2 1l-1-1h-1c-2-1-3-2-5-2l-4-1c-2-1-5-1-7 0-2 0-7 2-9 1 2-1 5-2 7-2z" class="E"></path><path d="M530 485h3 0c2 0 3-1 5 0v1h0 1 1s0 1 1 1l1 1v1l-2 1v1h-1l-2-1c-2-2-3-3-6-4l-1-1z" class="d"></path><path d="M538 486h1 1s0 1 1 1l1 1v1l-2 1c0-2-1-3-2-4z" class="F"></path><path d="M521 486l3-1c2-1 4-1 6 0l1 1c3 1 4 2 6 4l2 1h1v-1l2-1v2h-1c-1 2-2 4-3 5-1 2-2 2-3 3h-7c0-1 0-3 1-4 0-1 0-1 1-1h1v-1c-3 0-5 1-8 2-1 0-2 0-2 1-1 0-1 0-2 1h-2v-1l1-1-2-2c-1 1-2 1-3 2h-1v-1c1 0 1-1 2-1s2-1 3-2l-1-1c1-1 2-2 4-3l1-1z" class="P"></path><path d="M531 493h2c0 1 1 1 1 1 0 1-1 2-1 3 0 0-1 0-1 1h0-3c0-1 0-1 1-2v-2h1v-1z" class="G"></path><path d="M525 488c5 0 8 0 12 3 0 1 1 2 1 3-1 1-2 3-3 4h-1-1c1-1 2-3 2-4s-1-1-2-2c-2-1-5 0-7 0h-1v-4z" class="H"></path><path d="M521 486l3-1c2-1 4-1 6 0l1 1c3 1 4 2 6 4v1c-4-3-7-3-12-3v4h1c-2 1-3 1-5 2-1 1-2 1-3 2v-1l-2-2c-1 1-2 1-3 2h-1v-1c1 0 1-1 2-1s2-1 3-2l-1-1c1-1 2-2 4-3l1-1z" class="F"></path><path d="M521 486l3 1-3 2-1-2 1-1z" class="O"></path><path d="M516 490c1-1 2-2 4-3l1 2-4 2-1-1z" class="Q"></path><path d="M521 486l3-1c2-1 4-1 6 0l1 1c-2 0-4 0-7 1l-3-1z" class="M"></path><path d="M516 493c3-2 6-3 9-5v4h1c-2 1-3 1-5 2-1 1-2 1-3 2v-1l-2-2z" class="C"></path><path d="M511 491c2 0 3-1 5-1l1 1c-1 1-2 2-3 2s-1 1-2 1v1h1c1-1 2-1 3-2l2 2-1 1v1h2c1-1 1-1 2-1 0-1 1-1 2-1 3-1 5-2 8-2v1h-1c-1 0-1 0-1 1-1 1-1 3-1 4l-1-1h-1 0c-1 1-2 1-2 2 1 1 1 2 2 2 1 1 1 1 1 2 1 1 2 3 3 3l1-1v-1l2-2h1c0 1 0 2 1 3-1 2-2 3-2 5 2 0 4 0 7 1l-1 1h-1c-1 0-2-1-3-1h0c0 1 1 2 1 3h0-1-1l-1-1v1c-2 1-2 5-3 7v1c-1 2-3 3-4 4l-1 1c0 1-1 1-2 0-1 1-1 1-2 0l-2-1s-1 0-2-1h-1c-1 0-1 0-2-1l-1-1v-1l-1-1 3-3c1-1 2-1 2-1h1c1-1 2-1 3-1 2 0 3 0 4-1v-2c0-3-1-5-3-7s-4-3-6-4c-4-2-7-3-10-3-7 0-13 1-20 4h-3-1v-1l1-1h-1 0v-1h1 1 1v-1l2-1 3-2c1 0 2 0 2-1 1 0 2-1 3-1h0 1l2-1 2-1c1 0 2-1 3-1h0c0 1 0 1-1 2v1h1 4s1-1 2-1c0-1 1-2 1-2l1-1z" class="U"></path><path d="M525 514c1 1 1 3 0 4 0 1-2 3-3 3h-2l1-1h2v-3h-2c2 0 3 0 4-1v-2z" class="O"></path><path d="M517 518h1c1-1 2-1 3-1h2v3h-2l-1 1c0-2-1-2-3-3zm-17-25c1 0 2-1 3-1h0c0 1 0 1-1 2v1h1v1h-6c-2 1-1 0-2-1h0 1l2-1 2-1z" class="D"></path><path d="M512 522l3-3h0c1 1 1 2 1 2h1v2 3h-1c-1 0-1 0-2-1l-1-1v-1l-1-1zm-29-20s1-1 2-1c3 0 6-1 9-1h0 2 1 0c1-1 3-1 4-1h0 2 0c1 0 2 0 3 1-7 0-13 1-20 4h-3-1v-1l1-1z" class="C"></path><path d="M523 503c3 1 5 5 5 8l2 4c0 3-1 5-2 7-1 1-1 1-2 1 0-1 1-2 2-3v-1c0-3 0-7-2-10-1-2-2-4-4-6h1z" class="J"></path><path d="M511 491c2 0 3-1 5-1l1 1c-1 1-2 2-3 2s-1 1-2 1v1 1c1 1 3 2 4 2 3 1 5 3 7 5h-1c-1-1-2-2-3-2-2-1-3-2-5-3l-7-3s1-1 2-1c0-1 1-2 1-2l1-1z" class="I"></path><path d="M513 495c1-1 2-1 3-2l2 2-1 1v1h2c1-1 1-1 2-1 0-1 1-1 2-1 3-1 5-2 8-2v1h-1c-1 0-1 0-1 1-1 1-1 3-1 4l-1-1h-1 0c-1 1-2 1-2 2 1 1 1 2 2 2 1 1 1 1 1 2v1c0 1 0 1 1 2 0 0 0 1 1 2 0 2 2 4 1 6h0l-2-4c0-3-2-7-5-8-2-2-4-4-7-5-1 0-3-1-4-2v-1h1z" class="X"></path><path d="M513 495c1-1 2-1 3-2l2 2-1 1c0-1 0 0-1-1l-1 1c-1 0-1 0-2-1z" class="D"></path><path d="M531 505l2-2h1c0 1 0 2 1 3-1 2-2 3-2 5 2 0 4 0 7 1l-1 1h-1c-1 0-2-1-3-1h0c0 1 1 2 1 3h0-1-1l-1-1v1c-2 1-2 5-3 7v1c-1 2-3 3-4 4l-1 1c0 1-1 1-2 0v-1s1-1 2-1c0-1 0-2 1-3 1 0 1 0 2-1 1-2 2-4 2-7h0c1-2-1-4-1-6-1-1-1-2-1-2-1-1-1-1-1-2v-1c1 1 2 3 3 3l1-1v-1z" class="M"></path><path d="M531 505l2-2h1c0 1 0 2 1 3-1 2-2 3-2 5h0v2h-1l-1-4v-3-1z" class="E"></path><path d="M531 505c1 1 1 1 1 2s0 1-1 2v-3-1z" class="O"></path><path d="M183 493l15 4c0-1-1-1-2-2h2l3 1 9 5v-1c5 2 10 5 15 9 2 0 2 1 4 2l3 2v-2c-1 0-2-2-3-2h0v-2l3 3 1 1 2 2 2 1h0v1l4 4-1 1 2 2c0 1 1 1 1 2l-1 1c1 1 1 2 2 2 2 2 2 4 4 5h1v1h0-1-2l2 1h0c1 2 2 3 2 4l-2-1c-1 1-2 1-3 2l-6-4-3-1h-1l-4-1c-2-1-3-2-5-2-1 0-3-1-5-1l-1-1c-1 0-3 0-4-1l-5-1c-2 1-6 0-8-1-1 0-3-1-4-1v2l-1-1v-2c-2 0-4 1-5 1l-2-3v2c-1 1-2 1-4 0v1h-2-1c-1 0-2 0-3 1h-3l-1 1h-3 0 0 0c-1 1-2 1-3 1h0l-1-1c-2-1-2-1-3-3l-2-1-1-3v-4-1l-1-1c1 0 2-2 2-3 0-2 0-3 1-4-1 0-1-1-1-2h0l3-3h1c1-1 3-2 3-3 3-2 6-2 8-3l1-1h1l1-2z" class="R"></path><path d="M192 510l3 2h-2v1l-1-2v-1z" class="P"></path><path d="M176 508c1-1 4-1 6-1l1 1h-3l-1 1 1 1-1 1c-1-1-3-1-3-3z" class="B"></path><path d="M172 517c-1 0-2 0-3-1v-2h1l1 1c1 1 2 1 4 1 1 0 2 0 2 1h1 0-6z" class="F"></path><path d="M172 513s1-1 1 0c2 0 3 2 5 2 1 0 2 1 3 2s1 2 1 4h-2v-2c0-2-1-1-2-2h0-1c0-1-1-1-2-1l-3-3z" class="M"></path><path d="M193 513v-1h2c2 1 6 3 7 6h0l2 2c-1 0-2-1-2-1h-1-1c-1 0-4-3-5-4l-2-2z" class="J"></path><path d="M195 515c3 0 4 1 5 3h2 0l2 2c-1 0-2-1-2-1h-1-1c-1 0-4-3-5-4z" class="M"></path><path d="M169 510c2-3 6-6 10-7l1 1h5c2 0 3 0 5 1h-5-4c-1 0-3 0-4 1s-2 1-3 1h0v1l-1 1c-1 0-2 1-3 2l-1-1z" class="W"></path><path d="M190 505h4 4l2 2 3 3c-1 0-2 0-2 1l-4-1h0v1 1l6 6h-1 0c-1-3-5-5-7-6l-3-2c-1-2-4-2-6-3-2 0-3-1-5-2h4 5z" class="V"></path><path d="M197 512l-6-5v-1c4 0 7 2 10 5l-4-1h0v1 1z" class="J"></path><path d="M176 508c0 2 2 2 3 3l1-1v1c1 0 1 1 2 1 2 0 5 2 6 4 1 1 1 2 2 3 0 1 1 2 1 3v2c-1 1-2 1-4 0v1h-2v-1s0-1-1-1c0 0-1 0-1-1-1 0-1 0-1-1 0-2 0-3-1-4s-2-2-3-2c-2 0-3-2-5-2 0-1-1 0-1 0h0-1l1-1v-1c1 0 2-2 3-2h0l1-1z" class="C"></path><path d="M186 521h1 1c0 2 0 2-1 3v1h-2v-1l1-3z" class="P"></path><path d="M181 517c2 0 4 1 5 3v1l-1 3s0-1-1-1c0 0-1 0-1-1-1 0-1 0-1-1 0-2 0-3-1-4z" class="U"></path><path d="M176 508c0 2 2 2 3 3 2 1 5 3 6 5h-1c-2-2-5-5-8-5h-2v1c2 1 3 2 4 3-2 0-3-2-5-2 0-1-1 0-1 0h0-1l1-1v-1c1 0 2-2 3-2h0l1-1z" class="J"></path><path d="M172 517h6c1 1 2 0 2 2v2h2c0 1 0 1 1 1 0 1 1 1 1 1 1 0 1 1 1 1v1h-1c-1 0-2 0-3 1h-3l-1 1h-3 0 0 0c-1 1-2 1-3 1h0l-1-1c-2-1-2-1-3-3l-2-1-1-3h5c-1-1-2-1-2-2l1-1h4z" class="U"></path><path d="M174 523c1 1 2 3 3 4h-3c0-1 1-2 0-3v-1z" class="C"></path><path d="M169 523c1 1 2 2 3 4h2c-1 1-2 1-3 1h0l-1-1c0-2-1-2-1-4z" class="D"></path><path d="M174 524h-1c-1-1-1-1-1-2s0-2 1-3l1 1v3h0v1z" class="O"></path><path d="M170 521h0l-2 1c0 1 0 0 1 1 0 2 1 2 1 4-2-1-2-1-3-3v-3h3z" class="B"></path><path d="M164 520h5l1 1h-3v3l-2-1-1-3z" class="L"></path><path d="M182 521c0 1 0 1 1 1 0 1 1 1 1 1 1 0 1 1 1 1v1h-1c-1 0-2 0-3 1h-3s1-1 1-2 0-1-1-2v-1-1 1h1 1 0 2z" class="C"></path><path d="M180 510l-1-1 1-1h3 0c1 1 2 0 3 1 1 0 3 1 4 2 4 3 7 8 12 10 0-1-1-1-2-2h1 1s1 1 2 1l-2-2h1l3 2 1-1 5 4 1 2 1 1c-1 1-2 0-3 1-2 1-6 0-8-1-1 0-3-1-4-1v2l-1-1v-2c-2 0-4 1-5 1l-2-3c0-1-1-2-1-3-1-1-1-2-2-3-1-2-4-4-6-4-1 0-1-1-2-1v-1z" class="H"></path><path d="M203 518l3 2 1-1 5 4 1 2 1 1c-1 1-2 0-3 1-2 1-6 0-8-1-1 0-3-1-4-1v2l-1-1v-2-1c-1 0-1 0-1-1-1-1-3-2-3-3 1 1 4 4 6 4 0 0 1 0 1 1 1 0 2-1 2-1l1-1-2-1c0-1-1-1-2-2h1 1s1 1 2 1l-2-2h1z" class="R"></path><path d="M207 519l5 4 1 2-1 1-6-6 1-1z" class="d"></path><path d="M200 519h1 1s1 1 2 1c2 1 3 3 5 4 1 1 2 1 3 3-2-1-5-2-7-2-1-1-3-1-4-1 1 0 2-1 2-1l1-1-2-1c0-1-1-1-2-2z" class="B"></path><defs><linearGradient id="T" x1="164.059" y1="508.463" x2="206.937" y2="506.535" xlink:href="#B"><stop offset="0" stop-color="#d0cfd0"></stop><stop offset="1" stop-color="#f7f5f6"></stop></linearGradient></defs><path fill="url(#T)" d="M183 493l15 4 7 3v1c1 1 2 2 4 2l-1 1h-5l-9-3c0 1 0 2 1 3 1 0 2 0 3 1h-4-4c-2-1-3-1-5-1h-5l-1-1c-4 1-8 4-10 7-2 2-2 3-2 6v1h1l-1 1c0 1 1 1 2 2h-5v-4-1l-1-1c1 0 2-2 2-3 0-2 0-3 1-4-1 0-1-1-1-2h0l3-3h1c1-1 3-2 3-3 3-2 6-2 8-3l1-1h1l1-2z"></path><path d="M170 503c4-2 11-6 15-5-3 2-6 2-9 3l-2 1c-3 2-6 4-8 8v1c-1 1-1 3-2 5v-1l-1-1c1 0 2-2 2-3 0-2 0-3 1-4l4-4z" class="Q"></path><path d="M180 496h1 0c0 1-1 1-1 1l-3 1c-3 1-4 2-7 4v1l-4 4c-1 0-1-1-1-2h0l3-3h1c1-1 3-2 3-3 3-2 6-2 8-3z" class="J"></path><path d="M183 493l15 4 7 3v1c-6-3-11-3-17-4-2-1-4-1-6-2l1-2z" class="c"></path><path d="M179 503c3-2 7-2 11-2h4c0 1 0 2 1 3 1 0 2 0 3 1h-4-4c-2-1-3-1-5-1h-5l-1-1z" class="h"></path><path d="M190 501h4c0 1 0 2 1 3-1 0-3-1-4-1h-1v-2z" class="a"></path><path d="M194 501l9 3 15 8c1 1 1 2 2 3 0 1-1 1-1 1-1 0-1-1-2-1h-2-1l1 1 2 3 7 6 2 1h1 0l1 1-2 2h0v2c-1 0-3-1-5-1l-1-1c-1 0-3 0-4-1l-5-1c1-1 2 0 3-1l-1-1-1-2-5-4-1 1-3-2-6-6v-1-1h0l4 1c0-1 1-1 2-1l-3-3-2-2c-1-1-2-1-3-1-1-1-1-2-1-3z" class="D"></path><path d="M212 522l3 3c1 1 1 2 2 2h1-1l-1 1-5-1c1-1 2 0 3-1l-1-1-1-2v-1z" class="T"></path><path d="M203 510l4 3 1 2h-1c-2 0-4-2-6-4 0-1 1-1 2-1z" class="F"></path><path d="M207 519c-1 0-2-1-2-2h0c1-1 2 0 3 0 0 0 1 0 1 1s1 2 1 2l2 2v1l-5-4z" class="O"></path><path d="M207 513h2c2 1 4 3 5 4l2 2h1l7 6 2 1h1 0l1 1-2 2h0v2c-1 0-3-1-5-1l-1-1c-1 0-3 0-4-1l1-1h1 1l1-1h0l-5-6-1-1c-2-1-4-3-6-4l-1-2z" class="j"></path><path d="M215 520l1-1 3 3v1h1l2 2v1h0c0 1 0 2-1 2s-1 0-2-1l1-1h0l-5-6z" class="T"></path><path d="M224 525l2 1h1 0l1 1-2 2h0v2c-1 0-3-1-5-1l-1-1c-1 0-3 0-4-1l1-1h1 1c1 1 1 1 2 1s1-1 1-2c0 0 1-1 2-1z" class="K"></path><path d="M221 530s1 0 1-1c1 0 1-1 1-2h1c1 1 2 1 2 2v2c-1 0-3-1-5-1z" class="T"></path><path d="M194 501l9 3 15 8c1 1 1 2 2 3 0 1-1 1-1 1-1 0-1-1-2-1h-2-1l1 1 2 3h-1l-2-2c-1-1-3-3-5-4h-2l-4-3-3-3-2-2c-1-1-2-1-3-1-1-1-1-2-1-3z" class="s"></path><path d="M200 507c2 0 4 1 6 2 1 1 2 1 4 2 2 0 3 2 5 3v1h-1l1 1 2 3h-1l-2-2c-1-1-3-3-5-4h-2l-4-3-3-3z" class="p"></path><path d="M198 495l3 1 9 5v-1c5 2 10 5 15 9 2 0 2 1 4 2l3 2v-2c-1 0-2-2-3-2h0v-2l3 3 1 1 2 2 2 1h0v1l4 4-1 1 2 2c0 1 1 1 1 2l-1 1c1 1 1 2 2 2 2 2 2 4 4 5h1v1h0-1-2l2 1h0c1 2 2 3 2 4l-2-1c-1 1-2 1-3 2l-6-4-3-1h-1l-4-1c-2-1-3-2-5-2v-2h0l2-2-1-1h0-1l-2-1-7-6-2-3-1-1h1 2c1 0 1 1 2 1 0 0 1 0 1-1-1-1-1-2-2-3l-15-8h5l1-1c-2 0-3-1-4-2v-1l-7-3c0-1-1-1-2-2h2z" class="l"></path><path d="M238 532c0 1 0 2 1 3l-3-1v-1l2-1z" class="p"></path><path d="M227 518l3 3h1c-1 0-2 1-3 1v-1-1s-1-1-1-2z" class="b"></path><path d="M236 530l2 2-2 1v1h-1v-2h0 1l-1-2h1zm-1-5l2 2 6 6c-2 0-3-1-4-1-1-1-1-2-2-2 0-1-1-1-1-1-1-1-1-3-1-4z" class="S"></path><path d="M223 513c2 1 5 4 7 5l1 1c0 1 0 1-1 2l-3-3-5-4 1-1z" class="J"></path><path d="M229 524c3 1 5 4 7 6h-1l1 2h-1 0v2l-4-1c-2-1-3-2-5-2v-2h0l2-2 2-1-1-2z" class="V"></path><path d="M235 532c-1 0-3-1-3-3h1l2 1 1 2h-1 0z" class="b"></path><path d="M230 526c1 1 1 2 1 3l-1 1c0 1 0 2 1 3-2-1-3-2-5-2v-2h0l2-2 2-1z" class="o"></path><path d="M215 516l-1-1h1 2c1 0 1 1 2 1l4 3 5 5h1l1 2-2 1-1-1h0-1l-2-1-7-6-2-3z" class="q"></path><path d="M228 524h1l1 2-2 1-1-1v-1l1-1z" class="c"></path><path d="M215 516l-1-1h1 2c1 0 1 1 2 1l4 3c-1 1-1 1-2 1l-1-2c-1-1 0 0-1 0-1-1-1-2-2-2h-1-1z" class="i"></path><path d="M215 516h1 1c2 4 6 6 10 10h-1l-2-1-7-6-2-3z" class="T"></path><path d="M231 517c2 1 3 2 5 4 1 2 3 4 5 6 1 2 3 5 5 6l2 1h0c1 2 2 3 2 4l-2-1-5-4-6-6-2-2-4-4h-1c1-1 1-1 1-2l-1-1 1-1z" class="F"></path><path d="M231 519c2 2 7 5 8 8h-2l-2-2-4-4h-1c1-1 1-1 1-2z" class="e"></path><path d="M198 495l3 1 9 5v-1c5 2 10 5 15 9 2 0 2 1 4 2l3 2v-2c-1 0-2-2-3-2h0v-2l3 3 1 1 2 2 2 1h0v1l4 4-1 1 2 2c0 1 1 1 1 2l-1 1c1 1 1 2 2 2 2 2 2 4 4 5h1v1h0-1-2c-2-1-4-4-5-6-2-2-4-4-5-6-2-2-3-3-5-4l-1 1c-2-1-5-4-7-5l-1 1-4-2-15-8h5l1-1c-2 0-3-1-4-2v-1l-7-3c0-1-1-1-2-2h2z" class="Q"></path><path d="M213 503l5 2c0 1 1 1 2 2l3 2c2 1 2 2 4 2 0 1 1 1 2 1 1 2 2 3 3 4 2 1 4 3 4 4v1c-2-2-3-3-5-4-1-1-3-2-4-3l-5-3-5-3v-1c-2-1-3-2-4-3v-1z" class="O"></path><path d="M229 507l3 3 1 1 2 2 2 1h0v1l4 4-1 1 2 2c0 1 1 1 1 2l-1 1c-3-3-5-6-8-9-1-1-2-2-4-3l-5-4c2 0 2 1 4 2l3 2v-2c-1 0-2-2-3-2h0v-2z" class="D"></path><path d="M233 511l2 2 2 1h0v1h-1c-2 0-2-1-3-2v-2z" class="L"></path><defs><linearGradient id="U" x1="204.314" y1="502.533" x2="204.471" y2="494.748" xlink:href="#B"><stop offset="0" stop-color="#b2adaf"></stop><stop offset="1" stop-color="#ccd0cc"></stop></linearGradient></defs><path fill="url(#U)" d="M198 495l3 1 9 5c1 0 1 1 2 1l1 1h0v1c1 1 2 2 4 3v1l5 3 5 3c1 1 3 2 4 3l-1 1c-2-1-5-4-7-5l-1 1-4-2-15-8h5l1-1c-2 0-3-1-4-2v-1l-7-3c0-1-1-1-2-2h2z"></path><path d="M209 503c2 1 5 3 7 5s5 3 7 5l-1 1-4-2-15-8h5l1-1z" class="B"></path><path d="M562 470h3 0l1-1h3 8c1 0 3 1 4 1 1 1 2 1 4 1l-2 1c-1 0-1 1-2 1h-1v1c-1 0-1 0-2-1v1c4 1 8 3 12 4 1 1 2 2 5 3 1 0 3 1 4 1l1 1-1 1-1-1-1 1c1 1 3 3 4 3 2 2 4 3 5 5 1 1 2 2 3 4 4 4 5 9 5 15 0 1-1 1-1 2v4c0 2-1 4-3 6h-1v1c-1 1-2 2-4 3-1 0-4 0-5 1l-1 2h-1c-1 0-3 0-5-1l1-1-1-1h-1c-1-1-2-2-2-3s0-2-1-3h0c-1-1-2-2-3-2-2-1-3-1-5-2h-1v-3c1-2 1-4 2-6 1-3 3-5 3-8l1-1h-1c-1 1-2 2-4 2h-1l-2-2c-1 1-2 3-2 4l-1 1h-1c0-1-1-2-2-2l-1-1c1-2 1-3 0-4l1-1c2-1 2-2 2-4-1-1-1-3-2-4h0 0c-2-1-3-3-4-5 0-1-1-1-1-1h-2c-1-1-2-2-4-3-6-4-15-2-22-1h-3l-3 2c-3 1-5 2-8 3-1 0-3 1-4 1-2 1-4 3-6 4s-3 2-5 2l1 1-1 1s-1 1-1 2c-1 0-2 1-2 1h-4-1v-1c1-1 1-1 1-2h0c-1 0-2 1-3 1l-2 1-2 1h-1 0c-1 0-2 1-3 1 0 1-1 1-2 1l-3 2-2-1 29-17c0-1 2-1 2-1 4-3 9-4 13-6h2c4-3 10-3 14-4l1-1h0c3 0 8 0 11 1h5z" class="H"></path><path d="M562 470h3 0l1-1h3 8c-2 0-3 0-4 1h-1l1 1h0-2l-9-1z" class="E"></path><path d="M597 484l-4-1c2 2 4 4 5 6-1-1-2-1-3-2-2-2-4-4-7-6-2-1-4-1-5-3h0c2 0 3 1 4 2h1s1 1 2 1c1 1 2 0 3 1 2 0 3 1 5 1l-1 1zm-51-15c3 0 8 0 11 1-4 0-8-1-11 1-1 1-3 1-5 1-3 1-5 2-8 2h-2c4-3 10-3 14-4l1-1h0z" class="B"></path><path d="M577 469c1 0 3 1 4 1 1 1 2 1 4 1l-2 1c-1 0-1 1-2 1h-1v1c-1 0-1 0-2-1v1h-3c0-1-1-2-2-2 0-1-1 0-2-1h2 0l-1-1h1c1-1 2-1 4-1z" class="D"></path><path d="M555 474c10 1 21 4 29 9v1l-1 1c-1-1-1-1-2-1h-1 0l-1-1-1-1c-2-2-6-4-9-4l-13-3h-1v-1z" class="I"></path><path d="M485 498l29-17h0c-1 2-2 2-4 3-2 2-5 3-8 5l-3 2c1 0 1-1 2-1h0l2-1 1-1c1 0 2-1 3-2h1s1 0 1-1c1 0 2 0 3-1h0l1-1h1 1l-2 2h-1c-1 0-1 1-2 1-1 1-2 1-3 1l-1 1-1 1h-1s-1 1-2 1l-2 1-2 2-3 1h-1c-1 1-1 1-2 1v1c0 1-1 1-2 1l-3 2-2-1z" class="E"></path><path d="M592 489l1 1c1 0 2 0 3 1h2l3 3v1c1 1 2 3 2 5l1 3h0c0 1 0 2 1 3 0 2-1 4-1 5 1 0 1 0 1-1v-1h0v-4h1v4c-1 2-2 4 0 6v1c1-1 1-1 1-2l1-2c1 0 1-1 1-1l1-2v-1-1-1c1-2 1-3 1-4l-1-1v-2l-1-1v-1-1c4 4 5 9 5 15 0 1-1 1-1 2v4h-1c-1 1-2 3-3 4-1-1-2-1-3-1h-3l-2-2c0-2 1-3 1-4-1-3-2-7-3-9-1-3-1-6-2-8-1-3-3-5-6-7h0-1c1-1 1-1 2-1z" class="d"></path><path d="M592 489l1 1c4 4 7 8 7 14v1h0-1c-1-3-1-6-2-8-1-3-3-5-6-7h0-1c1-1 1-1 2-1z" class="Z"></path><path d="M538 475c2 0 3-1 5-1 3-1 9-1 12 0v1h1v1c2 1 5 2 7 2h1c4 3 6 5 8 10h0c-2-1-3-3-4-5 0-1-1-1-1-1h-2c-1-1-2-2-4-3-6-4-15-2-22-1h-3l-3 2c-3 1-5 2-8 3-1 0-3 1-4 1-2 1-4 3-6 4s-3 2-5 2l1 1-1 1s-1 1-1 2c-1 0-2 1-2 1h-4-1v-1c1-1 1-1 1-2h0c-1 0-2 1-3 1l-2 1-2 1h-1 0c-1 0-2 1-3 1v-1c1 0 1 0 2-1h1l3-1 2-2 2-1c1 0 2-1 2-1h1l1-1 1-1c1 0 2 0 3-1 1 0 1-1 2-1h1l2-2c5-2 10-4 14-5l9-3z" class="R"></path><path d="M525 481c1-1 2-2 4-2h0 1l4-2h1v1h1l-3 2h-1s-1 0-1-1c-2 0-4 2-6 2z" class="J"></path><path d="M538 475c2 0 3-1 5-1 3-1 9-1 12 0v1h1v1c-2 0-5-1-7-1-3 0-6 1-9 1l-2-1zm-13 6c2 0 4-2 6-2 0 1 1 1 1 1h1l-8 3c-1 0-3 1-4 1-2 1-4 3-6 4s-3 2-5 2l1 1-1 1s-1 1-1 2c-1 0-2 1-2 1h-4-1v-1c1-1 1-1 1-2h0c-1 0-2 1-3 1 1-1 3-2 4-3h1 1l1-1c1-1 2-1 3-2 2-1 5-3 8-4 1 0 2 0 3-1l2-1h2z" class="E"></path><path d="M502 494c2-2 3-2 5-2h3s-1 1-1 2c-1 0-2 1-2 1h-4-1v-1z" class="P"></path><path d="M556 475l13 3c3 0 7 2 9 4l1 1 1 1h0 1c1 0 1 0 2 1l1 3h1c0 2 1 3 1 5l1 2c0 1-1 3-1 4h-1c-1 1-2 2-4 2h-1l-2-2c-1 1-2 3-2 4l-1 1h-1c0-1-1-2-2-2l-1-1c1-2 1-3 0-4l1-1c2-1 2-2 2-4-1-1-1-3-2-4h0c-2-5-4-7-8-10h-1c-2 0-5-1-7-2v-1z" class="U"></path><path d="M586 493l1 2c0 1-1 3-1 4h-1v-5h0l1-1z" class="T"></path><path d="M574 492v6 2l-2 2-1-1c1-2 1-3 0-4l1-1c2-1 2-2 2-4z" class="G"></path><path d="M580 484h1c1 0 1 0 2 1l1 3h1c0 2 1 3 1 5l-1 1h0c0-1-1-2-1-3-1-2-2-4-4-7z" class="F"></path><path d="M564 478h1c1 1 3 3 4 2 1 1 2 1 2 2 1 2 2 3 1 6h0c-2-5-4-7-8-10z" class="G"></path><path d="M578 486c1 1 1 1 1 3h1c0 2 1 7 0 9v1c-1-2-1-4-1-6s-1-4-1-6v-1z" class="B"></path><path d="M556 475l13 3 4 3c-2-1-3-1-4-2s-2-1-4-2h0-4 2 0v1c-2 0-5-1-7-2v-1z" class="X"></path><path d="M569 478c3 0 7 2 9 4l1 1-2 1 1 2v1c-2-2-3-4-5-6l-4-3z" class="D"></path><path d="M584 483c1 1 3 2 3 2 2 1 3 2 5 4-1 0-1 0-2 1h1 0c3 2 5 4 6 7 1 2 1 5 2 8 1 2 2 6 3 9 0 1-1 2-1 4l2 2h3c1 0 2 0 3 1 1-1 2-3 3-4h1c0 2-1 4-3 6h-1v1c-1 1-2 2-4 3-1 0-4 0-5 1l-1 2h-1c-1 0-3 0-5-1l1-1-1-1h-1c-1-1-2-2-2-3s0-2-1-3h0c-1-1-2-2-3-2-2-1-3-1-5-2h-1v-3c1-2 1-4 2-6 1-3 3-5 3-8l1-1c0-1 1-3 1-4l-1-2c0-2-1-3-1-5h-1l-1-3 1-1v-1z" class="t"></path><path d="M609 521c1-1 2-3 3-4h1c0 2-1 4-3 6h-1v1c-1 1-2 2-4 3-1 0-4 0-5 1h-1 0c-1-1-1-2-1-3h0c-1-1-2-2-3-2v-1h1c1 1 2 1 3 1 2 1 6 1 8-1l2-1z" class="e"></path><path d="M590 520l1-1c2 0 3 2 4 3v1c1 0 2 1 3 2h0c0 1 0 2 1 3h0 1l-1 2h-1c-1 0-3 0-5-1l1-1-1-1h-1c-1-1-2-2-2-3s0-2-1-3c1 0 1-1 1-1z" class="L"></path><path d="M589 521c1 0 1-1 1-1 0 2 1 3 2 5l1-1v-1h1v2c1 2 4 2 4 5-1 0-3 0-5-1l1-1-1-1h-1c-1-1-2-2-2-3s0-2-1-3z" class="G"></path><path d="M591 490h0c3 2 5 4 6 7-1 0-1 0-2 1v8l-1 4c0 1 0 1-1 2v1c0 1-1 2-2 2s-2 0-3-1v-2l1-2h0 1c0-1 0-1 1-2h1v-1-1c1-1 1-2 1-3v-5-1l1 1v-1c0-2-2-5-3-7z" class="D"></path><path d="M593 497l1 1c0 3 0 6-1 8v2h0v2l-1 1v2l-1 1h-2c0-1 0-1-1-2l1-2h0 1c0-1 0-1 1-2h1v-1-1c1-1 1-2 1-3v-5-1z" class="G"></path><path d="M584 483c1 1 3 2 3 2 2 1 3 2 5 4-1 0-1 0-2 1h1c1 2 3 5 3 7v1l-1-1v1 5c0 1 0 2-1 3v1 1h-1c-1 1-1 1-1 2h-1 0 0c1-2 1-4 0-6 0-4 1-7-1-11h-1v-1 3l-1-2c0-2-1-3-1-5h-1l-1-3 1-1v-1z" class="d"></path><path d="M589 504h1 0v-1c0-1 1-1 1-2v2h0v-2c1 2 1 4 0 5v1 1c-1 1-1 1-1 2h-1 0 0c1-2 1-4 0-6z" class="X"></path><path d="M584 483c1 1 3 2 3 2v2l1 2h-2l-1-1h0-1l-1-3 1-1v-1z" class="P"></path><path d="M584 483c1 1 3 2 3 2v2c-2-1-2-2-3-3v-1z" class="j"></path><path d="M587 485c2 1 3 2 5 4-1 0-1 0-2 1h1c1 2 3 5 3 7v1l-1-1h-1c0-3-2-5-4-8l-1-2v-2z" class="g"></path><path d="M111 483l3-1h0 0l-2 2c-1 2-2 3-3 5 0 0 1 0 1 1 0 0 0 1 1 1v4 4c1 1 2 2 3 4h1l2-2c1-1 2-1 2-2v-1-1l2 1v1c1-1 1-1 2-1h1c4 0 10 1 14 5h0l4 6c1 2 1 3 2 5 1 1 1 1 1 2l1 1v2c0 1 0 2-1 2v3h0v7l-1 2 2-1 1 1-6 8c-1 1-2 3-4 3l-3 3c-1 0-2 1-3 2l1-2 1-1c2-1 2-3 4-5 1-1 1-3 0-4-1 1-1 2-2 3-2 2-3 4-4 6 0 1-1 3-2 3l-4 3-1 2-1-1c1 0 1 0 1-1h-1l1-1h-1c-1 1-2 1-4 2-7 2-15 3-23 1-9-2-15-8-19-15-5-8-7-20-5-29 1-3 3-6 5-9 0-1 1-2 2-2v-1c1-1 2-1 4-2-1 2-2 3-2 5-1 3-1 7-1 10l2 5h0c1-1 1 0 2 0 1 1 3 3 5 3h0v-2l1-3c0-3 2-5 3-8 0-1 0-3 1-4v2h1 0l1-3c0-2 1-4 3-5-1-1-1-1-1-2 1-1 2-2 3-4l1-1 1-1h1c3-2 4-4 7-5z" class="t"></path><path d="M101 546c6 0 10 0 15-2v1c-2 1-4 2-6 2-3 0-6 1-9 0h1l-1-1z" class="I"></path><path d="M101 547h-1c-7 0-14-3-19-9h0c1 0 1 0 2 1 5 5 11 6 18 7l1 1h-1z" class="T"></path><path d="M81 528l-5-6h1l1 1c1 2 3 3 5 5 2 1 5 2 8 2l-1 1h3l-2 1v1h0l1 1h-1c-2 0-4-1-7-2-2-1-2-2-3-4z" class="F"></path><path d="M91 533c-3-1-6-1-8-3h0l6 1h1 3l-2 1v1z" class="B"></path><path d="M73 511l1-1c1 1 0 3 0 5 1 1 1 3 1 4l1 2h1v-1c-1 0-1 0-1-1h0c2 0 3 2 3 4h1-2l-1-1h-1c1 1 5 6 5 6l-1 1c0 2 1 3 3 4h0-2c-4-4-6-10-7-15-1-3-1-5-1-7z" class="J"></path><path d="M94 526l4-2c1 0 2 1 2 1-1 2-3 3-5 4h0l1 1c-1 0-2 1-3 1h-3l1-1c-3 0-6-1-8-2v-3h1c3 1 6 2 9 1h1z" class="U"></path><path d="M95 529l1 1c-1 0-2 1-3 1h-3l1-1c1 0 2 0 4-1z" class="T"></path><path d="M77 501h0v2c-1 2 0 4 0 6s1 4 1 6 0 2 1 3c1 3 2 6 5 7h-1v3c-2-2-4-3-5-5h2-1c0-2-1-4-3-4h0c0 1 0 1 1 1v1h-1l-1-2c0-1 0-3-1-4 0-2 1-4 0-5l-1 1-1-1c1-3 3-6 5-9z" class="C"></path><path d="M108 498c0-1 1-2 2-3h1 0v4c0 1 0 1 1 2l1 2v5h-1c1 1 1 1 1 2s1 1 1 2 0 1 1 2c0 2 2 3 3 4v1l3 3h-1l-5-5v1c1 2-1 5-2 7h0c-1 3-3 5-6 6h-1l1-1c2-1 4-3 5-5 2-3 2-5 2-9h0l-1-1h0l-3-3 1-4c-1-1-1-3-2-4-1-2-1-4-1-6z" class="J"></path><g class="B"><path d="M111 508h0v3 1l1-1c1 1 1 1 1 2h0c0 1 1 2 1 3l-1-1h0l-3-3 1-4z"></path><path d="M108 498c0-1 1-2 2-3h1 0v4c0 1 0 1 1 2l1 2v5h-1c-1-2-2-5-2-8l-1-2h-1z"></path></g><path d="M108 498c0-1 1-2 2-3h1c-1 2-1 3-1 5l-1-2h-1z" class="E"></path><path d="M91 533c3 0 5-1 8 0l-6 2c-1 1-2 1-3 1h-1c2 2 5 0 8 2 4-1 8-2 12-4 1-1 3-3 4-3 1 1 1 1 1 2l-1 1h0l-1 2c-3 3-8 4-13 4-2 1-5 0-8 0-1 1-3 1-5 0-1 0-2-1-3-2h-1c-1-1-2-3-4-4l1-1 3 3c3 3 6 3 11 3l1-1c-2 0-4 1-5 0-1 0-1 0-1-1s1-1 1-2h2v-1h1l-1-1z" class="R"></path><path d="M114 533l-1 1h0-1c-2 2-4 4-7 4-2 1-4 1-6 1v-1h4c1 0 1-1 1-1 2 0 4-2 6-3 1-1 2-1 4-1z" class="d"></path><path d="M114 516c0 4 0 6-2 9-1 2-3 4-5 5 0-2 1-3 2-5v-1h-3v-1l-6 4c-1 1-2 3-4 3l-1-1h0c2-1 4-2 5-4 0 0-1-1-2-1 1-1 1-1 1-2l-1-1c4-1 7-3 10-4h1c2-1 2 1 4 1 0-1 1-1 1-2z" class="G"></path><path d="M100 525h1 1c1 0 1 0 2-1 0 0 0-1 1-1 1-1 2-1 2-2l2-2 1 1c-1 1-2 3-4 3l-6 4c-1 1-2 3-4 3l-1-1h0c2-1 4-2 5-4z" class="C"></path><path d="M77 501c0-1 1-2 2-2v-1c1-1 2-1 4-2-1 2-2 3-2 5-1 3-1 7-1 10l2 5c0 2 1 5 3 6s4 1 6 2l2-1h0v1c1 0 1 1 1 2h-1c-3 1-6 0-9-1s-4-4-5-7c-1-1-1-1-1-3s-1-4-1-6-1-4 0-6v-2h0z" class="Q"></path><path d="M79 518h0l2 2v1c1 2 3 3 5 3h0c0-1-1-1-2-2s-2-1-2-3l-1-1c-1-2-1-4-1-6v-1l2 5c0 2 1 5 3 6s4 1 6 2l2-1h0v1c1 0 1 1 1 2h-1c-3 1-6 0-9-1s-4-4-5-7z" class="F"></path><path d="M138 523v1c1-1 1-2 1-3h1c1 2 0 8-1 10l-1 1c0 2 0 3-1 5-1 1-1 2-2 3-2 2-3 4-4 6 0 1-1 3-2 3l-4 3-1 2-1-1c1 0 1 0 1-1h-1l1-1h-1l1-1c0-1 0-1 1-1l2-2c1 0 2-1 3-2 0-1 0-1 1-2 0-1 1-1 1-2 1-1 2-2 2-3h0c1-1 1-1 1-3-1 3-3 5-5 7v1c-1 2-4 4-6 5 0 0-1 0-2 1h-1c0-1 1-1 1-2h-4l1-1h1l4-2c1-1 2-2 3-2 1-1 1-1 2-1 1-2 2-2 3-4h1c0-1 1-1 1-2l1-1c0-1 0-2 1-3v-3c1-1 1-1 1-2 1-1 0-2 1-3h0 0z" class="C"></path><path d="M128 548c2-2 3-4 4-6l6-9v-1h0c0 2 0 3-1 5-1 1-1 2-2 3-2 2-3 4-4 6 0 1-1 3-2 3l-4 3-1 2-1-1c1 0 1 0 1-1h-1l1-1h-1l1-1 4-2z" class="J"></path><path d="M124 550l4-2c0 1-1 2-3 4h0l-1 2-1-1c1 0 1 0 1-1h-1l1-1h-1l1-1z" class="L"></path><path d="M133 514c1 0 2 0 3 1s1 1 1 2h1c1 2-1 5 0 6h0 0c-1 1 0 2-1 3 0 1 0 1-1 2v3c-1 1-1 2-1 3l-1 1c0 1-1 1-1 2h-1c-1 2-2 2-3 4-1 0-1 0-2 1-1 0-2 1-3 2l-4 2h-1l-1 1v1h0-1c-1 0-3 1-4 0h0 1c0-1 1-1 2-1v-1h1 1v-1h3l-2-1c-1 0-4 2-4 2l-1 1c-1 1-2 1-4 0 2 0 4-1 6-2v-1c1-1 3-3 5-3l2-1c4-2 8-6 10-10 1-1 1-2 1-4v-4-1c-2 2-3 3-3 5l-2 1h-2l-1 1-1-1v-1l-1-2h-1v-1c-1 0-2 0-2-1l-3-3 2-1c2 1 7 3 9 2h0 1l-1-2c2 0 3-1 4-2-1 0-1 0-1-1h1v-1z" class="Z"></path><path d="M127 527l2-1c2-2 4-5 5-7h0 1l-1 7v-4-1c-2 2-3 3-3 5l-2 1h-2z" class="H"></path><path d="M120 518c2 1 7 3 9 2h0 1c1-1 2-1 3-1 0 1 0 1-1 1-1 2-3 4-5 4-1 1-2 0-3 0h-1v-1c-1 0-2 0-2-1l-3-3 2-1z" class="B"></path><path d="M132 520c-1 2-3 4-5 4-1 1-2 0-3 0h-1v-1h2 1c1 0 1-1 2-1h0c1 0 2 0 2-1 1 0 1 0 2-1z" class="C"></path><path d="M133 514c1 0 2 0 3 1s1 1 1 2h1c1 2-1 5 0 6h0 0c-1 1 0 2-1 3 0 1 0 1-1 2v3c-1 1-1 2-1 3l-1 1c0 1-1 1-1 2h-1c-1 2-2 2-3 4-1 0-1 0-2 1-1 0-2 1-3 2l-4 2h-1l-1 1v1h0-1c-1 0-3 1-4 0h0 1c0-1 1-1 2-1v-1h1 1v-1h3l-2-1c-1 0-4 2-4 2l-1 1c-1 1-2 1-4 0 2 0 4-1 6-2 1-1 3-2 4-3 4-2 8-4 11-7 0-1 1-2 2-3 2-4 3-7 3-12 0-2-2-4-3-5v-1z" class="H"></path><path d="M111 483l3-1h0 0l-2 2c-1 2-2 3-3 5 0 0 1 0 1 1 0 0 0 1 1 1v4h0-1c-1 1-2 2-2 3 0 2 0 4 1 6 1 1 1 3 2 4l-1 4 3 3h0l1 1h0c0 1-1 1-1 2-2 0-2-2-4-1h-1c-3 1-6 3-10 4l1 1c0 1 0 1-1 2l-4 2c0-1 0-2-1-2v-1h0l-2 1c-2-1-4-1-6-2s-3-4-3-6h0c1-1 1 0 2 0 1 1 3 3 5 3h0v-2l1-3c0-3 2-5 3-8 0-1 0-3 1-4v2h1 0l1-3c0-2 1-4 3-5-1-1-1-1-1-2 1-1 2-2 3-4l1-1 1-1h1c3-2 4-4 7-5z" class="t"></path><path d="M98 521l1 1c0 1 0 1-1 2l-4 2c0-1 0-2-1-2v-1h0c1 0 1 0 2-1 1 0 1 0 2-1v1 1h1v-2z" class="R"></path><defs><linearGradient id="V" x1="113.092" y1="491.602" x2="105.27" y2="509.276" xlink:href="#B"><stop offset="0" stop-color="#d5d4d5"></stop><stop offset="1" stop-color="#fdfdfd"></stop></linearGradient></defs><path fill="url(#V)" d="M111 483l3-1h0 0l-2 2c-1 2-2 3-3 5 0 0 1 0 1 1 0 0 0 1 1 1v4h0-1c-1 1-2 2-2 3 0 2 0 4 1 6 1 1 1 3 2 4l-1 4c0-1-1-1-1-1v-1c-1 0-1 0-2-1h0c-1-1-2-2-2-3v-1c-1-2-2-5-1-7 0-3 1-6 3-9h0c1-3 3-4 4-6z"></path><path d="M104 498h0l1-1c0-2 2-4 3-7 0 2 1 2 1 4-1 1-3 1-4 3s1 6 0 9v-1c-1-2-2-5-1-7z" class="E"></path><path d="M111 483l3-1h0 0l-2 2c-1 2-2 3-3 5l-1 1c-1 3-3 5-3 7l-1 1h0c0-3 1-6 3-9h0c1-3 3-4 4-6z" class="K"></path><path d="M104 488c3-2 4-4 7-5-1 2-3 3-4 6h0-1c-3 2-5 8-6 12 0 4 1 9 3 13h1l-2 2h-1c-1-1-1-1-2-3-1 0-1-1-1-2-2-2-3-4-4-7h0 1 0l1-3c0-2 1-4 3-5-1-1-1-1-1-2 1-1 2-2 3-4l1-1 1-1h1z" class="T"></path><path d="M101 490l1-1 1-1h1c-1 2-3 5-4 8v-1l-1 1c-1-1-1-1-1-2 1-1 2-2 3-4z" class="D"></path><path d="M99 496l1-1v1c-2 6-1 11 1 17 1 1 1 1 0 2 0-1-1-1-2-2h0c-1 0-1-1-1-2-2-2-3-4-4-7h0 1 0l1-3c0-2 1-4 3-5z" class="X"></path><path d="M119 498v-1l2 1v1c1-1 1-1 2-1h1c4 0 10 1 14 5h0l4 6c1 2 1 3 2 5 1 1 1 1 1 2l1 1v2c0 1 0 2-1 2v3h0v7l-1 2 2-1 1 1-6 8c-1 1-2 3-4 3l-3 3c-1 0-2 1-3 2l1-2 1-1c2-1 2-3 4-5 1-1 1-3 0-4 1-2 1-3 1-5l1-1c1-2 2-8 1-10h-1c0 1 0 2-1 3v-1c-1-1 1-4 0-6h-1c0-1 0-1-1-2s-2-1-3-1v1h-1c0 1 0 1 1 1-1 1-2 2-4 2l1 2h-1 0c-2 1-7-1-9-2l-2 1v-1c-1-1-3-2-3-4-1-1-1-1-1-2s-1-1-1-2 0-1-1-2h1v-5l-1-2c-1-1-1-1-1-2 1 1 2 2 3 4h1l2-2c1-1 2-1 2-2v-1z" class="t"></path><path d="M124 506c1 0 2 0 3 1h-1-2v2h2v1c-2 0-4-1-5-2l1-1c1 0 1-1 2-1z" class="G"></path><path d="M144 533l2-1 1 1-6 8c-1 1-2 3-4 3 2-2 4-4 5-6l1-4 1-1z" class="E"></path><path d="M120 504c1-1 1-1 2-1 2-2 5-1 8-2h2l2 1 1 1h1v2l1-1c1 1 3 3 4 5h1c1 2 1 3 2 5 1 1 1 1 1 2l1 1v2c0 1 0 2-1 2v3h0v7l-1 2-1 1v-7l-1 2c1 3 0 4-1 7 1-5 1-9 0-14-1-4-2-8-4-12-1-2-1-5-3-6s-2-1-3 0h-3 0l-1 3c-1-1-2-1-3-1s-1 1-2 1l-1 1c-1 1-2 1-2 2v3 1h1l-1 1c-1-1-1-2-1-3l-1-1c0-1 1-2 1-3h0c0-2 1-2 2-4h0z" class="L"></path><path d="M118 508l1 1v-1c1-2 1-3 2-4h1c1-1 2-1 4-2l1 1c0 1-1 1-1 2-1 0-1 0-2 1h0c-1 0-1 1-2 1l-1 1c-1 1-2 1-2 2v3 1h1l-1 1c-1-1-1-2-1-3l-1-1c0-1 1-2 1-3z" class="B"></path><path d="M137 504c1 1 3 3 4 5h1c1 2 1 3 2 5 1 1 1 1 1 2l1 1v2c0 1 0 2-1 2v3h0v7l-1 2-1 1v-7l-1 2c0-3 0-6-1-9 0-3-2-7-3-11-1-1-1-3-2-4l1-1z" class="H"></path><path d="M142 509c1 2 1 3 2 5 1 1 1 1 1 2l1 1v2c0 1 0 2-1 2v-2c0-1 0-1-1-2v-1c-1-3-2-5-3-7h1z" class="P"></path><path d="M119 498v-1l2 1v1c1-1 1-1 2-1h1c4 0 10 1 14 5h0l4 6h-1c-1-2-3-4-4-5l-1 1v-2h-1l-1-1-2-1h-2c-3 1-6 0-8 2-1 0-1 0-2 1h0c-1 2-2 2-2 4h0c0 1-1 2-1 3l1 1c0 1 0 2 1 3l1-1h-1v-1-3c0-1 1-1 2-2 1 1 3 2 5 2v-1c1 1 3 2 4 3v1c1 1 2 1 3 1v1h-1c0 1 0 1 1 1-1 1-2 2-4 2l1 2h-1 0c-2 1-7-1-9-2l-2 1v-1c-1-1-3-2-3-4-1-1-1-1-1-2s-1-1-1-2 0-1-1-2h1v-5l-1-2c-1-1-1-1-1-2 1 1 2 2 3 4h1l2-2c1-1 2-1 2-2v-1z" class="F"></path><path d="M115 514v-1h0c1 2 3 4 5 5l-2 1v-1c-1-1-3-2-3-4z" class="G"></path><path d="M113 503v1c1 1 0 2 1 3h1v6h0v1c-1-1-1-1-1-2s-1-1-1-2 0-1-1-2h1v-5z" class="E"></path><path d="M111 499c1 1 2 2 3 4h1v4h-1c-1-1 0-2-1-3v-1l-1-2c-1-1-1-1-1-2z" class="T"></path><path d="M117 502c3-2 6-3 9-3 5 0 8 2 11 5l-1 1v-2h-1l-1-1-2-1h-2c-3 1-6 0-8 2-1 0-1 0-2 1v-1c-1 0-2 0-3-1z" class="C"></path><path d="M129 518c-2 1-4 1-6 1v-1c-1 0-2 0-3-1l-3-3-1-3c0-3 0-7 1-9 1 1 2 1 3 1v1h0c-1 2-2 2-2 4h0c0 1-1 2-1 3l1 1c0 1 0 2 1 3l1-1h-1v-1-3c0-1 1-1 2-2 1 1 3 2 5 2v-1c1 1 3 2 4 3v1c1 1 2 1 3 1v1h-1c0 1 0 1 1 1-1 1-2 2-4 2h0z" class="U"></path><path d="M126 509c1 1 3 2 4 3v1c1 1 2 1 3 1v1h-1c0 1 0 1 1 1-1 1-2 2-4 2h0v-1h-1-1v-1c0-1 0-1 1-1 0-1-1-1-1-2h1 1 0c0-1-1-1-1-2-1 0-1 0-2-1h0v-1z" class="B"></path><path d="M191 534h0l10-1c7-1 14 0 21 1 3 1 6 1 8 2v1l8 6h0c2 0 3 1 4 2s4 4 6 5v1h-1c0 1 0 1 1 1v2c-2 0-3-1-5-2l-3-3h-1l-1-1-1-1v1c-1 0-1 1-2 1l-3-2c-2-1-4-2-6-2 0 1 0 1-1 1 2 1 4 1 7 3v2l-1 1h-2 0v2l4 5v1c1 1 1 2 2 3 0 0 0 1 1 2-1 1-3 1-3 2-1 0-2 1-3 2v-1h0c-1 0-3 1-4 1l-1 1-1-2c0-1 0-2 1-4h-1l-1 2h-2l-2 2-2-1-1 1c-1 2-3 2-4 4l-2 3c0 1-1 1-2 2l-1 1v1h0c1 1 1 2 2 2 1 3 1 5 1 8l-1 1v1h0v5l-1 1-1 1v3h-1v1l-1 2h-1c-1 1-1 2-2 3l-1 1-3 2-3 1v-1c-3 1-5 2-8 2-1 1-2 1-4 1-5-1-9-1-14-2v-1c-2 0-4-1-6-3h0c-3 0-5-3-7-5h0l-1-2h0v-1-1-1c-2-1-3-3-4-5 0 0 0-1-1-1 0-1 0-1-1-1l-1 2-1-7c-2-6-1-14 1-20h0c1-3 3-5 5-8 1-1 2-2 4-3 1-1 3-2 4-3 3-3 7-6 10-7l6-3c0-1 1-2 2-2s1 0 2-1h1c1 0 3-1 3-1l-5 1v-1h1l4-1h1 1c2-1 2-1 4-1l1-1h-1z" class="t"></path><path d="M225 544l1 1c0 1 0 1-1 1l-4-1c2 0 3 0 4-1z" class="i"></path><path d="M219 543l6 1c-1 1-2 1-4 1s-3 0-5-1c1 0 2 0 3-1z" class="q"></path><path d="M221 566c0-1 1-2 1-3h-4v-1h1 6v1l-1 1-1 2h-2z" class="H"></path><path d="M172 590c0 1 1 2 2 2h0l-1 1-1 1v2s-1 0-2 1c-1 0-1 0-1-1v-1c-1-1-1-1-3-1h0l1-1h0c1 0 2 0 3 1h1 0v-1l1-3z" class="M"></path><path d="M207 562h1 1l-1 1c-1 1-2 1-3 2s-1 2-2 3h-1v1l-1-1 1-1c0-1 1-2 2-3-1 0-1 0-1-1h-1l1-1c1 0 1 1 2 1l2-1z" class="H"></path><path d="M204 598c1 1 2 1 2 2v1 1l-1 2h-1-3l3-6z" class="I"></path><path d="M225 563c1 0 1 1 2 2 1 0 2 1 3 2v1c-1 0-3 1-4 1l-1 1-1-2c0-1 0-2 1-4h-1l1-1z" class="D"></path><path d="M225 563c1 0 1 1 2 2 1 0 2 1 3 2l-1 1h-2v-1c0-1-1-2-2-3h-1l1-1z" class="P"></path><path d="M205 579v-3c1-1 1-3 2-4 1-2 2-5 4-6 1-1 2-1 3-2h1 0c-1 2-2 2-3 3s-2 3-3 4c-2 3-3 4-2 7h0v1h-2z" class="C"></path><path d="M205 593c1 0 2-1 3-2v1l1 4-1 1-1 1v3h-1v-1c0-1-1-1-2-2 0-2 1-3 1-5z" class="J"></path><path d="M201 604h3c-1 1-1 2-2 3l-1 1-3 2-3 1v-1l6-6z" class="j"></path><path d="M207 579h0c1 1 1 2 2 2 1 3 1 5 1 8l-1 1v1h0v5l-1-4v-1c-1 1-2 2-3 2 1-5 3-9 0-14h2z" class="D"></path><path d="M161 551c1 0 2-1 4-2h1c1-1 2-2 3-2-1 1-1 2-2 3h-1l-4 5c-2 2-2 4-4 6 0 0-1 2-1 3h-1c0-2 1-2 2-4v-1s1 0 1-1c0 0 1-1 1-2-2 1-3 5-4 7h-1c1-2 2-4 3-5v-1c2-2 3-4 5-6l-6 3c-2 1-3 3-4 5v-2c1-1 2-2 4-3 1-1 3-2 4-3z" class="C"></path><path d="M192 572h1v-2c0-1 1-2 2-2s1 0 1 1h1c1 0 1 1 1 2v1l1-1v-1c0-1 0-2 1-2v4h0c-1 1-1 3-1 5-1 1 0 3 0 4 0 2 0 3-1 4h0l-1 3s0 1-1 2v-1-1c0-1 1-2 1-3v-2l1-1h-2v1c-1-1-1-1-1-2h1l-1-1h0v-2h0-1v-1c0-1 0-2-1-2h0c-1-1-1-2-1-3z" class="H"></path><path d="M192 572c1 1 2 1 3 2l2-2v1l-1 1 1 1v2h0v2h-1 0v-3h-1 0l-1 1c0-1 0-2-1-2h0c-1-1-1-2-1-3z" class="X"></path><path d="M153 593c2 1 2 2 4 2 0-1 0-1-1-2h0 1c1 1 2 3 4 4l1 1 1 1c2 1 6 3 8 3h1c1 1 2 0 4 0h0c1-1 2-1 2-1h1c-1 2-3 2-4 2s-1 1-2 1v2h0v1h-3v1c0 1 0 1 1 1l-2 1c-2 0-4-1-6-3-1-1-2-3-2-4h0 1c1 0 1 1 2 1l2 1 1-1c-1 0-1-1-2-1h-1c0-1-1-1-1-1h-1l-1-1-1-1c-1 0-1-1-2-1l-1-1c-1 0-3-3-4-5z" class="d"></path><path d="M153 557v2l-2 3c-1 1-1 2-1 2v1c-1 1-1 3-1 4v3h0v1c-1 4 1 10 2 14v1c0 1 0 1 1 2l1 1v2c1 2 3 5 4 5l1 1c1 0 1 1 2 1l1 1 1 1h1s1 0 1 1h1c1 0 1 1 2 1l-1 1-2-1c-1 0-1-1-2-1h-1 0c0 1 1 3 2 4h0c-3 0-5-3-7-5h0l-1-2h0v-1-1-1c-2-1-3-3-4-5 0 0 0-1-1-1 0-1 0-1-1-1l-1 2-1-7c-2-6-1-14 1-20h0c1-3 3-5 5-8z" class="D"></path><path d="M155 597c1 1 3 2 4 4h0c-2 0-3-1-4-1v-1-1-1z" class="X"></path><path d="M166 550v1 1h1l1 1v1h1c-1 2-3 3-4 6-2 2-2 4-3 7h-1v1 2h1v2h0-1c0-1-1-1-1-1l-2 1v2c-1 0-2 0-2 1s-1 4 0 5h0v1h0c0 2 0 3 1 5 1 1 2 2 2 4 3 2 5 2 8 3l-1 1h0v1 1h-2c-1-1-1-1-3-1 0 0-1 0-1-1-1-1-2-1-2-2h-1l-1-2c-1-2-1-4-1-6-1-7-1-13 2-20 0-1 1-3 1-3 2-2 2-4 4-6l4-5z" class="R"></path><path d="M161 595h0c1-1-3-4-4-5v-1l-1-1v-1h0 0l1 1v1c1 0 1 1 2 1 0 1 1 2 2 2 0 1 4 2 5 2h0 0v1 1h-2c-1-1-1-1-3-1z" class="G"></path><path d="M158 564h1c0 1-1 2-1 3l1 1c1 0 1-1 2-1h1-1v1 2h1v2h0-1c0-1-1-1-1-1l-2 1v2c-1 0-2 0-2 1 0-1-1-2 0-2v-3l1-3c0-1 0-2 1-3z" class="B"></path><path d="M156 570h1 0 2 1v1h0l-2 1v2c-1 0-2 0-2 1 0-1-1-2 0-2v-3z" class="D"></path><path d="M168 554h1c-1 2-3 3-4 6-2 2-2 4-3 7h-1c-1 0-1 1-2 1l-1-1c0-1 1-2 1-3h-1c0-1 1-2 1-3 1-1 2-2 4-3 1-1 3-3 5-4z" class="U"></path><path d="M159 561h2l-2 3h-1c0-1 1-2 1-3z" class="D"></path><path d="M159 561c1-1 2-2 4-3 0 2-1 2-2 3h-2z" class="L"></path><path d="M173 606h3c0 1 1 0 2 0h0 3c1-1 3-1 4-1l3-2 2-1c3-1 5-4 7-6 1 0 1-1 2-2h0c1-1 2-4 2-5s1-1 1-1h1l-1 1c0 1 0 2 1 3 0 1 0 2-1 2 0 1-1 2-1 3h-1c0 1-1 2-1 2-1 1-1 1-1 2h-1c0 1-1 2-1 2l-5 5h0c-1 1-2 2-4 3h-1l1 1c-1 1-2 1-4 1-5-1-9-1-14-2v-1l2-1c-1 0-1 0-1-1v-1h3v-1z" class="G"></path><path d="M171 609c5 1 11 2 15 1 2-1 3-2 5-2h0 0c-1 1-2 2-4 3h-1l1 1c-1 1-2 1-4 1-5-1-9-1-14-2v-1l2-1z" class="L"></path><path d="M191 534h0l10-1c7-1 14 0 21 1 3 1 6 1 8 2v1l8 6h0c2 0 3 1 4 2s4 4 6 5v1h-1c0 1 0 1 1 1v2c-2 0-3-1-5-2l-3-3h-1l-1-1-1-1v1c-1 0-1 1-2 1l-3-2c-2-1-4-2-6-2l-1-1-6-1c-1 1-2 1-3 1h-13c-7 2-16 6-22 11 0 1-1 1-1 2v1s-1 1-1 2l-3 6v1l-1 1v1c1 0 1-1 1-1l1-1h0c1 1 1 1 1 2h-1c0 1 0 2-1 3h1 2c1 1 1 1 1 2 1 1 2 0 3 0h4l1 1h1c1 1 3 2 3 3v1h1c0 1 0 1 1 2h0v2h-1v1l-1 3v1 1l-3 4c0 2-1 2-2 4-1 0-2 0-2-1l-1-1h-1-2-2c-1 0-1 0-2-1h-1 0c-1-1-2-1-2-2h0c-1 0-2-1-2-2l-1 3v1h0-1c-1-1-2-1-3-1h0c-3-1-5-1-8-3 0-2-1-3-2-4-1-2-1-3-1-5h0v-1h0c-1-1 0-4 0-5s1-1 2-1v-2l2-1s1 0 1 1h1 0v-2h-1v-2-1h1c1-3 1-5 3-7 1-3 3-4 4-6h-1v-1l-1-1h-1v-1-1h1c1-1 1-2 2-3-1 0-2 1-3 2h-1c-2 1-3 2-4 2 3-3 7-6 10-7l6-3c0-1 1-2 2-2s1 0 2-1h1c1 0 3-1 3-1l-5 1v-1h1l4-1h1 1c2-1 2-1 4-1l1-1h-1z" class="t"></path><path d="M232 543c1 0 3 1 4 2 0 1 0 1 1 2v1c-1 0-1 1-2 1l-3-2c1 0 2 0 3-1v-1c-1 0-2-1-3-2z" class="C"></path><path d="M178 550l1 1c-6 5-9 11-10 18h-1c-1-1 0-3-1-4 1-1 2-4 3-5 2-4 5-7 8-10z" class="E"></path><path d="M174 579v-1c1 0 1 0 2 1v3 1h1l1-1 1-1c1-1 1-2 2-2v1l-2 2-1 1-1 1h0l2 3c1 1 2 1 3 1h1l1-1c0 1 1 2 0 3h-4l-3-3c-1-1-2-3-2-4-1-2-1-3-1-4z" class="B"></path><path d="M181 579h1v1h1v1l1 1c0 1 1 2 1 3 1 0 1 1 2 2h-3l-1 1h-1c-1 0-2 0-3-1l-2-3h0l1-1 1-1 2-2v-1z" class="t"></path><path d="M180 557c1-3 4-5 6-6 10-7 21-10 33-8-1 1-2 1-3 1h-13c-7 2-16 6-22 11 0 1-1 1-1 2h0z" class="c"></path><path d="M174 579l-1-2c0-3 0-6 1-8v-2c1-3 3-6 5-9 0 0 0-1 1-1h0v1s-1 1-1 2l-3 6v1l-1 1v1c1 0 1-1 1-1l1-1h0c1 1 1 1 1 2h-1c0 1 0 2-1 3h1 2c1 1 1 1 1 2 1 1 2 0 3 0h4l1 1h1c1 1 3 2 3 3v1h1c0 1 0 1 1 2h0v2h-1v1l-1 3v1 1l-3 4c0 2-1 2-2 4-1 0-2 0-2-1l-1-1h-1-2-2c-1 0-1 0-2-1h-1 0c-1-1-2-1-2-2h0c-1 0-2-1-2-2-1 0-2 0-3-1l-2-4c0-1 1 0 2 0v-1c0-1-1-2-1-3v-2h-1l1-1h1c0 2 1 4 2 6 0 2 1 3 1 5 3 0 4 3 7 3 1 1 1 1 2 1v-1h5c1 1 1 0 2 0l1-1v-1h0c-1 0-2-1-3-1s-1 1-2 1c1-1 0-2 0-3h3c-1-1-1-2-2-2 0-1-1-2-1-3l-1-1v-1h-1v-1h-1c-1 0-1 1-2 2l-1 1-1 1h-1v-1-3c-1-1-1-1-2-1v1z" class="X"></path><path d="M180 574c1 1 2 0 3 0-1 1-2 2-3 2l-2-1h0l2-1z" class="T"></path><path d="M176 582v-4c1 0 1 0 1 1 1 1 1 1 1 2v1l-1 1h-1v-1z" class="U"></path><path d="M184 582h5c1 1 1 3 1 4l-1 2c-1 0-1 0-2-1s-1-2-2-2c0-1-1-2-1-3z" class="B"></path><path d="M168 581v-2h-1l1-1h1c0 2 1 4 2 6 0 2 1 3 1 5 2 2 3 3 6 4 2 1 4 1 6 1v-1h2 1v1c-1 1-2 1-3 1h0-1-2-2c-1 0-1 0-2-1h-1 0c-1-1-2-1-2-2h0c-1 0-2-1-2-2-1 0-2 0-3-1l-2-4c0-1 1 0 2 0v-1c0-1-1-2-1-3z" class="E"></path><path d="M168 553c1-1 3-2 4-3 2-2 4-2 6-4 2 0 3-1 5-2 3-2 7-3 12-4l-1 1v1c-1 1-3 2-4 2-3 1-5 2-7 3s-3 3-4 4l-1-1c-3 3-6 6-8 10-1 1-2 4-3 5 1 1 0 3 1 4h1c-1 3-1 6 0 9h-1l-1 1h1v2c0 1 1 2 1 3v1c-1 0-2-1-2 0l2 4c1 1 2 1 3 1l-1 3v1h0-1c-1-1-2-1-3-1h0c-3-1-5-1-8-3 0-2-1-3-2-4-1-2-1-3-1-5h0v-1h0c-1-1 0-4 0-5s1-1 2-1v-2l2-1s1 0 1 1h1 0v-2h-1v-2-1h1c1-3 1-5 3-7 1-3 3-4 4-6h-1v-1z" class="U"></path><path d="M169 589c1 1 2 1 3 1l-1 3c-1-1-1-2-2-4z" class="B"></path><path d="M159 590h3c1 1 3 1 4 1 0 0 1 1 1 2h0c-3-1-5-1-8-3z" class="X"></path><path d="M158 575l1 1c1 0 1 0 1 1h1c-1 1-2 1-2 2h-1v1h0c1 0 2 1 2 2v1l-2-2-2-2c1 0 1-1 1-1v-1c0-1 0-1 1-2z" class="G"></path><path d="M156 575c0-1 1-1 2-1v1c-1 1-1 1-1 2v1s0 1-1 1l2 2 2 2c0 1 1 1 1 2h0-2v-1-1h-1l-2-2h0 0v-1h0c-1-1 0-4 0-5z" class="B"></path><path d="M167 570v-5c1 1 0 3 1 4h1c-1 3-1 6 0 9h-1l-1 1h1v2c0 1 1 2 1 3v1c-1 0-2-1-2 0h-1l-1-2v-1l-1-1c-1-2-1-5 0-8 0-1 2-2 3-3z" class="X"></path><path d="M167 570v-5c1 1 0 3 1 4h1c-1 3-1 6 0 9h-1l-1 1h1v2c-1-1-1-2-1-3h0c-1-2-1-5 0-8zm1-17c1-1 3-2 4-3 2-2 4-2 6-4 2 0 3-1 5-2 3-2 7-3 12-4l-1 1v1c-1 1-3 2-4 2-3 1-5 2-7 3s-3 3-4 4l-1-1v-1c-1-1-3 0-3 1-3 2-7 7-9 10-1 2-2 4-3 7-1 1-1 2-1 3h-1v-2-1h1c1-3 1-5 3-7 1-3 3-4 4-6h-1v-1z" class="M"></path><path d="M191 534h0l10-1c7-1 14 0 21 1 3 1 6 1 8 2v1l8 6h0c2 0 3 1 4 2s4 4 6 5v1h-1c0 1 0 1 1 1v2c-2 0-3-1-5-2l-3-3h-1l-1-1-1-1c-1-1-1-1-1-2-1-1-3-2-4-2-4-2-9-3-13-4-8-2-17 0-24 1-5 1-9 2-12 4-2 1-3 2-5 2-2 2-4 2-6 4-1 1-3 2-4 3l-1-1h-1v-1-1h1c1-1 1-2 2-3-1 0-2 1-3 2h-1c-2 1-3 2-4 2 3-3 7-6 10-7l6-3c0-1 1-2 2-2s1 0 2-1h1c1 0 3-1 3-1l-5 1v-1h1l4-1h1 1c2-1 2-1 4-1l1-1h-1z" class="R"></path><path d="M201 534h10v1h-7 1v-1h-4z" class="E"></path><path d="M223 537c-1-1-3-1-4-2 3 0 6 0 9 2h0 0-5z" class="M"></path><path d="M201 534h4v1h-1l-12 2s-1 0-1-1c-3 0-11 3-14 5 0-1 1-2 2-2s1 0 2-1h1c1 0 3-1 3-1l-5 1v-1h1l4-1h1 1c2-1 2-1 4-1h2c2-1 4 0 5 0 1-1 2-1 3-1z" class="B"></path><path d="M206 536c5-1 11 0 16 1h1 5 0 2l8 6h0c2 0 3 1 4 2s4 4 6 5v1h-1c0 1 0 1 1 1v2c-2 0-3-1-5-2l-3-3h-1l-1-1-1-1c-1-1-1-1-1-2-1-1-3-2-4-2-4-2-9-3-13-4v-1c-5-1-9-1-14 0l1-2z" class="J"></path><path d="M238 543c2 0 3 1 4 2s4 4 6 5v1h-1c0 1 0 1 1 1v2c-2 0-3-1-5-2l2-2-3-3v-1l-2-1c0-1-1-1-1-2h-1z" class="e"></path><path d="M206 536c5-1 11 0 16 1 4 1 8 3 12 5 1 0 2 0 3 1v1c1 1 1 2 2 3v2h1-1l-1-1-1-1c-1-1-1-1-1-2-1-1-3-2-4-2-4-2-9-3-13-4v-1c-5-1-9-1-14 0l1-2z" class="L"></path><path d="M206 536c5-1 11 0 16 1 4 1 8 3 12 5h-1-1l-1-1h-1c-1-1-2-1-3-2h-3s-1 0-1-1h-4c-5-1-9-1-14 0l1-2z" class="d"></path><path d="M186 539h0c1-1 3-2 5-2 2 1 4 0 6 0 3 0 6 0 9-1l-1 2c5-1 9-1 14 0v1c-8-2-17 0-24 1-5 1-9 2-12 4-2 1-3 2-5 2-2 2-4 2-6 4-1 1-3 2-4 3l-1-1h-1v-1-1h1c1-1 1-2 2-3-1 0-2 1-3 2h-1c-2 1-3 2-4 2 3-3 7-6 10-7 3 0 5-2 6-3 2-1 3-1 5-2h0 4z" class="F"></path><path d="M169 548h0c1-1 3-1 4-1-1 1-2 3-4 4v-3z" class="E"></path><path d="M169 548v3c-1 0-1 1-2 1h-1v-1-1h1l2-2z" class="P"></path><path d="M186 539h0c1-1 3-2 5-2 2 1 4 0 6 0 3 0 6 0 9-1l-1 2c-7 0-15 1-22 4h-1v-1l-1-1h0 2c1 0 2-1 3-1z" class="C"></path><path d="M171 544c3 0 5-2 6-3 2-1 3-1 5-2h0 4c-1 0-2 1-3 1h-2 0l1 1v1h1c-3 2-6 3-9 4l-1 1c-1 0-3 0-4 1h0l-2 2c1-1 1-2 2-3-1 0-2 1-3 2h-1c-2 1-3 2-4 2 3-3 7-6 10-7z" class="B"></path><path d="M174 546l1-1c1-2 5-4 7-4v1h1c-3 2-6 3-9 4z" class="E"></path><path d="M496 533h8l16 3 4 2 4 2c1 0 3 1 4 2 2 0 6 2 7 4-1 0-1 0-2 1 0 2 5 6 6 8s2 5 3 7l1 3h0 0l1 3 1 4 1 3c0 4 0 9-1 14v1h0v1l-1 2c-2 5-6 10-10 12h-2c-4 4-9 8-15 9 0 1 0 2-1 3-3 0-7 2-10 1h-1 0l-3-1c-2 0-4-1-5-2h-1l1-1c0 1 0 1 1 1h1 0 0c-1 0-1-1-1-1h-1l-1-1h-2c-1 0-2-1-2-1v-1c-1-1-2 0-2 0l-2-2h-1l-1-1h-1l1 1-1 1c0 1-1 1-1 2 0-1 0-1 1-2l-1-1h0l-1 1c-1-1-2-1-3-1h-1l-1-1v-1c-2-1-5-1-7-1l2-1c1-1 2-2 3-4h0c1-1 1-2 2-3v-2c1-1 1-2 1-3h-1c-1-1-1-1-2-3-1-1-2-3-3-5-1-1-2-1-2-2-1-1-2-1-3-1h0v1h0c-1-1-1-2-1-3l1-1 1-1c-2-1-2-3-3-5-1-1-2-1-3-2s-1-2-2-2c0-1 0-2 1-3h-3-1c-1 0-4-1-5-1s-2-1-2-1h0v-1h-6-1v-2c1-1 1-2 2-4 1 0 1-1 2-2h0l2-2c-2 0-2 0-3 1l-1-1c-6 3-12 6-17 11h-1c-1 1-2 2-4 3h0l-1-1c1-1 2-2 2-4h1s1-2 2-2c2-3 5-6 7-9 7-7 17-11 26-14 8-2 16-3 25-3l1-1h5z" class="t"></path><path d="M502 597c1 0 1 1 2 2h0c-2 0-2 0-3-1h0l1-1z" class="I"></path><path d="M463 548c1 0 2 0 2-1v1c1 1 1 0 1 1-1 1-2 1-3 2-1-1 0-1-1-1 0 0 0-1-1-1l2-1z" class="B"></path><path d="M503 594l2 1h1l2 1-1 1c0 1-1 2-1 3l-1-1h-1 0c-1-1-1-2-2-2v-2l1-1zm-45-39c-1 3-3 3-4 6 0 0 1 0 2 1l1-1c0-1-1-1 0-1 2-2 3-3 4-5 1-1 1-2 2-2h0c0 1-2 2-2 4 1-1 2-3 3-4h1c0 1 0 1-1 1 0 1 0 1-1 2l-1 1-2 2c0 1-1 2-1 2-1-1-1-1-2-1 0 2-1 3-2 4v-1c-1-1-1 0-1-1l-1-1c0-2 3-4 5-6z" class="G"></path><path d="M517 574h0v-1-1l-1-1c0-1 0-3 1-4v1h0c1 1 1 2 2 2v-2l1-1c0 1 0 2 1 3h0v2l1 1v3c-1 0-1 0-2-1h-1l-2-1z" class="H"></path><path d="M465 553l1-1h0l1-1 1 1c-1 2-3 3-4 4 0 1 0 2 1 2l-3 3-1 3c0 1 0 1 1 2-1 0-4-1-5-1s-2-1-2-1h0c1-1 2-2 2-4 1 0 1 0 2 1 0 0 1-1 1-2l2-2 1-1c1-1 1-1 1-2 1 0 1 0 1-1z" class="B"></path><path d="M464 556c0 1 0 2 1 2l-3 3-1-1c0-2 1-1 2-2 0-1 0-1 1-2z" class="X"></path><path d="M461 549c1 0 1 1 1 1 1 0 0 0 1 1h-2c0 2-1 2-2 4h-1c-2 2-5 4-5 6l1 1c0 1 0 0 1 1h-6-1v-2c1-1 1-2 2-4 1 0 1-1 2-2h0l2-2h1v-1h1c2-1 3-2 5-3z" class="L"></path><path d="M459 555l-1-2h0c1-1 2-1 3-2 0 2-1 2-2 4z" class="B"></path><path d="M454 553h1c0 1 1 2 1 3l-3 3h-1v-3-1l2-2z" class="d"></path><path d="M486 577h1c1 0 2 1 2 2 1-1 1-2 1-3l-1-1 1-1v-1-1-1 1h-1v-1c0-2-4-4-5-5s-2-3-2-4h-1-4v-1h-1c0-2 0-2 1-2 3 0 4 0 5 2 2 1 2 3 4 4l3 3c1 1 1 2 1 3v1c1 1 0 2 1 3v2h1c0 1-1 2-1 3-1 1-1 1-2 1h-1c-1 2 0 3 0 4h-1l-1 1h-1 0c-1-2 0-4-1-7v-1h0c1 0 1 0 2-1z" class="C"></path><path d="M484 578c1 1 2 1 3 3h0-1v5h-1 0c-1-2 0-4-1-7v-1z" class="R"></path><path d="M503 594c0-1-2-2-3-3v-1h-1c-1 0-1 0-2-1 0-1 0-2-1-3v-5l-1-1c-2-2-1-6-1-8-1-1-1-1-1-2v-1-2h1v1c1 1 1 1 1 2v5c0 1 0 3 1 4l1-1v-4c1 0 1-1 2-1 1 2-1 4-1 6l2-2h1c2-2 4-3 7-3 2 0 4 1 6 2l2 2c0 3 0 6-2 8l-2 1c-1 1-1 1-2 1h0v-1c0-1 0-2-1-2v-3h-2l-2 1v2c-1 0-1 0-2 1v2c1 0 2 1 3 2 0 0 1 0 1 1 1 0 2 0 2 2h0v1h-1c2 1 5 1 7 0v1l2-1v1h-1 0c-1 0-1 1-2 1h0c-2-1-4 0-6 0l-2-1h-1l-2-1z" class="M"></path><path d="M507 591c1 0 2 0 2 2h0v1h-1-1-1c-1-1-2-1-3-2v-1c1 0 2-1 3 0h0 1z" class="d"></path><path d="M501 577c2-2 4-3 7-3 2 0 4 1 6 2l2 2c0 3 0 6-2 8l-2 1c0-1 1-2 1-3h1v-3c-1-2-4-3-6-4-1 0-2 0-3 1l-2 1c-1 1-2 2-2 4v1h-1l1-3c0-1 1-2 2-3h0c1-1 3-2 4-2 3 0 5 2 7 3v1-1h0c0-1-1-2-2-3-2-1-4-1-6-1h-1c-2 1-4 2-5 4 0 1-2 5-1 6v1h-1l-1-1c1-2 1-4 2-5 0-1 1-2 1-3h1z" class="K"></path><path d="M505 578c1-1 2-1 3-1 2 1 5 2 6 4v3h-1c0 1-1 2-1 3-1 1-1 1-2 1h0v-1c0-1 0-2-1-2v-3h-2l-2 1v2c-1 0-1 0-2 1v2l-1-1c-1-1-1-1-1-2 0 0 1-1 1-2s0-2 1-2c0-1 1-2 2-3zm-36-14l1-1c1 1 1 0 1 1 1 1 2 1 2 2l2-1h0c1 1 2 1 2 2 1 0 2 1 2 2 2 1 4 3 5 5l-1 1h-1l-1 1 2 2h0 1l2-1c-1 1-1 1-2 1h0v1c1 3 0 5 1 7h0v2c1 0 2 0 3 1-1 1-1 2-1 4 0 3 2 6 3 9 0 1 1 1 1 2 1 1 2 3 4 4h0c0 1 1 1 1 1 1 1 2 1 3 2h-1c2 2 5 3 7 4h3 1c2 1 5 1 7 0l5-1c0 1 0 2-1 3-3 0-7 2-10 1h-1 0l-3-1c-2 0-4-1-5-2h-1l1-1c0 1 0 1 1 1h1 0 0c-1 0-1-1-1-1h-1l-1-1h-2c-1 0-2-1-2-1v-1c-1-1-2 0-2 0l-2-2h-1l-1-1h-1l1 1-1 1c0 1-1 1-1 2 0-1 0-1 1-2l-1-1h0l-1 1c-1-1-2-1-3-1h-1l-1-1v-1c-2-1-5-1-7-1l2-1c1-1 2-2 3-4h0c1-1 1-2 2-3v-2c1-1 1-2 1-3h-1c-1-1-1-1-2-3-1-1-2-3-3-5-1-1-2-1-2-2-1-1-2-1-3-1h0v1h0c-1-1-1-2-1-3l1-1 1-1c-2-1-2-3-3-5-1-1-2-1-3-2s-1-2-2-2c0-1 0-2 1-3 0-1 1-1 3-2z" class="G"></path><path d="M481 583h2v2h-2v1 1l-1-2h0l1-1v-1z" class="C"></path><path d="M475 566l6 5h-1c-2 0-3-2-4-3l-1 1h-1c0-2 0-2 1-3z" class="Q"></path><path d="M477 585c1-1 1-1 0-2 1 0 2 1 2 1l1 1h0l1 2c0 1 0 2 1 3v1 2c-1-1-1-1-2-3-1-1-2-3-3-5z" class="E"></path><path d="M476 579s1 0 2 1h1l1-1h1v1c-1 0-2 0-2 1h0c1 1 2 1 2 2h0v1l-1 1h0 0l-1-1s-1-1-2-1c1 1 1 1 0 2-1-1-2-1-2-2-1-1-2-1-3-1h0v1h0c-1-1-1-2-1-3l1-1 1-1c1 1 1 1 2 1h1z" class="B"></path><path d="M481 583v1c-1-1-2-1-2-2v-1c1 1 2 1 2 2z" class="L"></path><path d="M476 579s1 0 2 1h-1l1 1-1 1-1-1-1 2c-1-1-2-1-3-1h0v1h0c-1-1-1-2-1-3l1-1 1-1c1 1 1 1 2 1h1z" class="D"></path><path d="M476 579s1 0 2 1h-1l1 1-1 1-1-1c-1-1-2-2-4-2l1-1c1 1 1 1 2 1h1z" class="C"></path><path d="M483 593c1 3 0 6 1 9-1 1-2 3-2 4h3v1c-1 0-1 0-2 1v1l-1-1v-1c-2-1-5-1-7-1l2-1c1-1 2-2 3-4h0c1-1 1-2 2-3v-2c1-1 1-2 1-3z" class="L"></path><path d="M498 611h-2c0-1-1-1-1-1-1-1-1 0-1-1-1-1-2-1-3-2h-2c-1-1-3-3-3-5 0 0 0-1 1-1-1-1-1-2-1-3h0c0-2 0-4-1-6 0-1 0-1-1-2l1-2h0c1 0 2 0 3 1-1 1-1 2-1 4 0 3 2 6 3 9 0 1 1 1 1 2 1 1 2 3 4 4h0c0 1 1 1 1 1 1 1 2 1 3 2h-1z" class="J"></path><path d="M475 569l1-1c1 1 2 3 4 3h1c1 1 2 2 2 3h0c-1 0-1 1-2 2 0 1 0 1 1 1l-1 1h-1-2c-2-1-3-3-6-2 0-1 1-1 1-2s0-1-1-2h-1l1-1h2v1h1l-1-2 1-1z" class="C"></path><path d="M475 572s1 1 2 1l1 1v1h-2l-1-1c0-1 0-1-1-1v-1h1z" class="e"></path><path d="M475 569l1-1c1 1 2 3 4 3v1c-1 1-2 1-3 1s-2-1-2-1l-1-2 1-1z" class="G"></path><path d="M469 564l1-1c1 1 1 0 1 1 1 1 2 1 2 2l2-1v1c-1 1-1 1-1 3h1l-1 1 1 2h-1v-1h-2l-1 1h1c1 1 1 1 1 2s-1 1-1 2c1 1 2 2 4 3h-1c-1 0-1 0-2-1-2-1-2-3-3-5-1-1-2-1-3-2s-1-2-2-2c0-1 0-2 1-3 0-1 1-1 3-2z" class="L"></path><path d="M469 564l1-1c1 1 1 0 1 1v1 4h-1l-2 2-1-1h0l2-1v-2l1-1c0-1-1-2-1-2z" class="Q"></path><path d="M471 564c1 1 2 1 2 2l2-1v1c-1 1-1 1-1 3h1l-1 1 1 2h-1v-1h-2v-1c-1 0-1-1-2-1h1v-4-1z" class="M"></path><path d="M471 564c1 1 2 1 2 2v1h-2c0 1 0 1 1 2 0 1 1 1 2 2h-2v-1c-1 0-1-1-2-1h1v-4-1z" class="J"></path><path d="M520 536l4 2 4 2c1 0 3 1 4 2 2 0 6 2 7 4-1 0-1 0-2 1 0 2 5 6 6 8s2 5 3 7l1 3h0 0l1 3 1 4 1 3c0 4 0 9-1 14v1h0v1l-1 2c-2 5-6 10-10 12h-2c-4 4-9 8-15 9l-5 1c-2 1-5 1-7 0h-1-3c-2-1-5-2-7-4h1c-1-1-2-1-3-2 0 0-1 0-1-1h0c-2-1-3-3-4-4 0-1-1-1-1-2l1-1c1 0 1 0 2-1 0 0-1-1-1-2v-1h-1v-2-2c-1 0-1-1-1-2h0v-1s0-1-1-2h0v-2-1-1c1 1 1 1 1 2 1 1 1 1 2 1h1c1 1 2 1 2 2 1 1 0 3 1 4 0 1 2 2 2 3h0c-1 0-2-1-2-1l-1-1h0v-2l-1 1v2h0c0 1 1 1 1 2v1c1 1 2 1 2 2 4 4 9 7 15 7h5 0v-1-2l-2-1h0c-2 1-3 0-4-1-1 0-2-1-3-2h-2c0-1 1-2 1-3l1-1c2 0 4-1 6 0h0c1 0 1-1 2-1h0 1v-1l-2 1v-2c2-1 3-2 4-3h2l1-1s0-1 1-1c0-1 1-2 2-3h3 0l2-1h0c0 2-1 4-1 5s0 1 1 2h0l2-2 1-1s0-1 1-2h0 1v-2c3-6 3-12 3-18 0-2-1-5 0-7h1l-1-1h0c-1-1-1-1-1-2v-1-1-1c0-1-1-1-1-2-1-1-1-1-1-2v-1h0l-2-1c-2-2-4-4-6-5-3-1-7-2-9-3v-1c0 1 1 0 2 0v-2z" class="G"></path><path d="M527 608l1 1c-3 1-7 3-10 4-4 0-7 0-11-1-1 0-2 0-3-1h1c2 0 4 1 5 1 2 0 5-1 7-1 4-1 7-1 10-3z" class="h"></path><path d="M491 593h1v2 1c1 2 2 4 4 7l1 1 3 4 1 1 1 1c1 0 1 0 2 1s2 1 3 1c-1 1-1 1-2 1h-2c-1-1-3-1-4-2s-2-1-3-2c0 0-1 0-1-1h0c-2-1-3-3-4-4 0-1-1-1-1-2l1-1c1 0 1 0 2-1 0 0-1-1-1-2v-1h-1v-2-2z" class="E"></path><path d="M493 600v2 2h-2c0-1-1-1-1-2l1-1c1 0 1 0 2-1zm3 9h0c1-1 1-1 1-2h0v-1c1 1 2 1 2 2 1 1 2 2 3 2s1 0 2 1 2 1 3 1c-1 1-1 1-2 1h-2c-1-1-3-1-4-2s-2-1-3-2z" class="D"></path><path d="M538 566c0-2-1-5 0-7h1c0 2 1 4 2 5 1 5 1 10 0 15l-2 6-1 2h-2 0l-1-3c3-6 3-12 3-18z" class="U"></path><path d="M538 592c-4 7-12 14-20 16-5 2-9 1-14-1-1-1-6-5-7-6v-1c4 4 9 7 15 7h5 0v-1-2l-2-1h2c4-1 8-2 11-3l1-1c4-1 6-3 8-7h1z" class="i"></path><path d="M517 603l1 1c3 0 7-2 10-3h2c-4 3-9 6-13 6h0v-1-2l-2-1h2z" class="B"></path><path d="M530 596c1 0 2 0 3-1h0c1-1 2-1 2-2 1-1 1-1 2-1-2 4-4 6-8 7l-1 1c-3 1-7 2-11 3h-2 0c-2 1-3 0-4-1-1 0-2-1-3-2h-2c0-1 1-2 1-3l1-1c2 0 4-1 6 0h0c1 0 1-1 2-1h0 1c1-1 2-1 3-1v1l1 1v2h5c0-1 1-1 1-1 1 0 1 0 2-1h1z" class="U"></path><path d="M508 596c2 0 4-1 6 0v2c-1-1-1-1-1-2l-1 1h0v1h-1l-1 1h-1v1c1 0 1 1 2 2h0c-1 0-2-1-3-2h-2c0-1 1-2 1-3l1-1z" class="C"></path><path d="M545 575c2 8-2 15-5 21 0 1 0 2-1 3 1-1 1-1 2-1s2-5 3-6l1 1-1 1c-1 2-2 4-3 5-2 2-4 4-5 6-4 4-9 8-15 9l-5 1c-2 1-5 1-7 0h-1-3c-2-1-5-2-7-4h1c1 1 3 1 4 2h2c1 0 1 0 2-1 4 1 7 1 11 1 3-1 7-3 10-4l-1-1h0l1-1c11-8 15-19 17-32z" class="j"></path><path d="M541 598c-2 2-4 5-7 6l6-8c0 1 0 2-1 3 1-1 1-1 2-1z" class="D"></path><path d="M507 612c4 1 7 1 11 1h0c-1 1-1 1-2 1h0-8c-1 0-2 0-3-1h0c1 0 1 0 2-1z" class="L"></path><path d="M530 584h0c0 2-1 4-1 5s0 1 1 2h0l2-2 1-1s0-1 1-2h0 1v-2l1 3h0 2l1-2c1 1 1 1 1 2h0c0 1 0 1-1 2v1h0l-1 2h-1c-1 0-1 0-2 1 0 1-1 1-2 2h0c-1 1-2 1-3 1h-1c-1 1-1 1-2 1 0 0-1 0-1 1h-5v-2l-1-1v-1c-1 0-2 0-3 1v-1l-2 1v-2c2-1 3-2 4-3h2l1-1s0-1 1-1c0-1 1-2 2-3h3 0l2-1z" class="C"></path><path d="M525 590h1c0 2 0 2-1 4h-3-1l1-1h0l3-3z" class="H"></path><path d="M525 585h3 0l1 1c-1 1-1 2-3 3h-4s0-1 1-1c0-1 1-2 2-3z" class="U"></path><path d="M535 586v-2l1 3h0 2l1-2c1 1 1 1 1 2h0c0 1 0 1-1 2v1h0l-1 2h-1c-1 0-1 0-2 1 0 1-1 1-2 2h0c-1 1-2 1-3 1h0c0-1 1-2 1-3-1 0-2 0-3 1h-1 0 0l3-3 2-2 1-1s0-1 1-2h0 1z" class="B"></path><path d="M535 586v-2l1 3h0 2v1l-1 1h0l-1 1v-1h-1 0v-3z" class="d"></path><path d="M520 536l4 2 4 2c1 0 3 1 4 2 2 0 6 2 7 4-1 0-1 0-2 1 0 2 5 6 6 8s2 5 3 7l1 3h0 0l1 3 1 4 1 3c0 4 0 9-1 14v1h0v1l-1 2c-2 5-6 10-10 12h-2c1-2 3-4 5-6 1-1 2-3 3-5l1-1-1-1c-1 1-2 6-3 6s-1 0-2 1c1-1 1-2 1-3 3-6 7-13 5-21v-8-2-4h0l-1-1h0l-2-4-1-2h-1c0-1-1-2-2-3l-3-3-2-1c-2-2-4-4-6-5-3-1-7-2-9-3v-1c0 1 1 0 2 0v-2z" class="B"></path><path d="M545 593h0c2-5 3-12 3-17h1l1-1c0 4 0 9-1 14v1h0v1l-1 2c-2 5-6 10-10 12h-2c1-2 3-4 5-6 1-1 2-3 3-5l1-1z" class="U"></path><path d="M549 590h0v1l-1 2c-2 5-6 10-10 12h-2c1-2 3-4 5-6 1-1 2-3 3-5h0c0 2 0 3-2 5v1l3-3v-1c1 0 1 0 1-1s0-1 1-2l1-2v-1h1z" class="M"></path><defs><linearGradient id="W" x1="451.288" y1="594.486" x2="508.643" y2="545.022" xlink:href="#B"><stop offset="0" stop-color="#d7d5d5"></stop><stop offset="1" stop-color="#fcfbfc"></stop></linearGradient></defs><path fill="url(#W)" d="M496 533h8l16 3v2c-1 0-2 1-2 0v1c2 1 6 2 9 3 2 1 4 3 6 5l2 1h0v1c0 1 0 1 1 2 0 1 1 1 1 2v1 1 1c0 1 0 1 1 2h0l1 1h-1c-1 2 0 5 0 7 0 6 0 12-3 18v2h-1 0c-1 1-1 2-1 2l-1 1-2 2h0c-1-1-1-1-1-2s1-3 1-5h0l-2 1h0-3c-1 1-2 2-2 3-1 0-1 1-1 1l-1 1h-2c-1 1-2 2-4 3v2-1c-2 1-5 1-7 0h1v-1h0c0-2-1-2-2-2 0-1-1-1-1-1-1-1-2-2-3-2v-2c1-1 1-1 2-1v-2l2-1h2v3c1 0 1 1 1 2v1h0c1 0 1 0 2-1l2-1c2-2 2-5 2-8v2h1 0 1l-1-1c0-1 1-1 1-2-1-1-1-2-1-3l2 1h1c1 1 1 1 2 1v-3l-1-1v-2h0c-1-1-1-2-1-3l-1 1v-1c-2-8-8-15-15-19s-14-5-22-5c-2 0-4 0-7 1h0c-4 1-6 2-10 3 0 1-1 1-2 1l-2 1c-2 1-3 2-5 3h-1v1h-1c-2 0-2 0-3 1l-1-1c-6 3-12 6-17 11h-1c-1 1-2 2-4 3h0l-1-1c1-1 2-2 2-4h1s1-2 2-2c2-3 5-6 7-9 7-7 17-11 26-14 8-2 16-3 25-3l1-1h5z"></path><path d="M523 583h2v2c-1 1-2 2-2 3-1-1 0-2 0-4v-1z" class="J"></path><path d="M450 553l12-6 1 1-2 1c-2 1-3 2-5 3h-1v1h-1c-2 0-2 0-3 1l-1-1z" class="b"></path><path d="M517 574l2 1h1c1 1 1 1 2 1 0 1 0 2-1 3v2h0c0 1-1 1-1 2l-1 3c-1 1-1 1-2 1v-1s0-1-1-1v1h-2 0c2-2 2-5 2-8v2h1 0 1l-1-1c0-1 1-1 1-2-1-1-1-2-1-3z" class="C"></path><path d="M516 585l1-2c0-1 1-1 2-2h1c0 1-1 1-1 2l-1 1c0 1-1 1-1 2 0 0 0-1-1-1z" class="H"></path><path d="M462 547c6-2 13-5 19-5 1 0 1 0 1-1-6 0-11 1-17 2-1 0-2 0-4 1h0c2-1 3-1 5-2 6-2 12-2 19-1 5 0 12 1 17 4l2 2c8 5 12 12 16 20l-1 1v-1c-2-8-8-15-15-19s-14-5-22-5c-2 0-4 0-7 1h0c-4 1-6 2-10 3 0 1-1 1-2 1l-1-1z" class="g"></path><path d="M465 537c8-2 16-3 25-3-1 0-2 0-3 1h0v1c-2 0-6 0-8 1 3 0 6 0 8-1h1c0 1 1 1 2 1h0 2c1 0 1 0 2 1h2l2 1h1 2v1h2l3 2c1 0 1 1 2 2 1 0 2 1 3 2 2 1 5 4 6 6s3 5 5 7v1c1 1 1 2 2 4l1-1h1l-1 1s0 1-1 1l1 1-2 2v-1c0-1 0-2-1-4-2-4-4-7-6-11-1-2-4-4-6-5-1-1-2-2-4-3v-1c-1 0-1 0-2-1h0-1l-2-1c-1 0-2-1-2-1l-4-1h-2c-1-1 0-1-1-1h-3-1c-4-1-10-1-15 0h-5l-3-1z" class="O"></path><path d="M496 533h8l16 3v2c-1 0-2 1-2 0v1c2 1 6 2 9 3 2 1 4 3 6 5l2 1h0v1c0 1 0 1 1 2 0 1 1 1 1 2v1 1 1c0 1 0 1 1 2h0l1 1h-1c-1 2 0 5 0 7 0 6 0 12-3 18v2h-1 0c-1 1-1 2-1 2l-1 1-2 2h0c-1-1-1-1-1-2s1-3 1-5h0l-2 1h0-3v-2h-2v-2h1c1-1 1-3 1-4v-1-7s-1-1-2-1l2-2-1-1c1 0 1-1 1-1l1-1h-1l-1 1c-1-2-1-3-2-4v-1c-2-2-4-5-5-7s-4-5-6-6c-1-1-2-2-3-2-1-1-1-2-2-2l-3-2h-2v-1h-2-1l-2-1h-2c-1-1-1-1-2-1h-2 0c-1 0-2 0-2-1h-1c-2 1-5 1-8 1 2-1 6-1 8-1v-1h0c1-1 2-1 3-1l1-1h5z" class="H"></path><path d="M532 589v-2c0-1 0-1 1-2v1h1c-1 1-1 2-1 2l-1 1z" class="d"></path><path d="M524 554c2 1 3 2 4 3-1 1-1 1-2 0s-2-2-2-3z" class="C"></path><path d="M533 565c1 1 1 1 1 3l-1 1c-1 1-2 2-3 2v2h-1-1c0-1 1-1 1-1v-2h2s2-2 1-3v-1l1-1z" class="O"></path><path d="M528 557v-3h1c1 1 2 2 3 4l-1 1v1c1 1 1 2 2 3h-1s-1 0-1-1l-1-1h-2l1-2-1-2z" class="L"></path><path d="M526 576h2l1-1c1 0 1 1 2 0l1 1h0c-1 1-1 1-2 1s-1 1-3 0v1c1 0 2 0 3 1v-1l1 1h0c-1 1-1 1-2 1s-2 1-2 1l-1 1h-1c0-1 0 0 1-1v-5zm6-18c3 4 5 11 4 16v1-1c-1 0-1 0-2-1v-3h0l-1-1 1-1c0-2 0-2-1-3h0l-1-2h1c-1-1-1-2-2-3v-1l1-1z" class="C"></path><path d="M523 568l2-2c0 2 1 3 1 5v5 5c-1 1-1 0-1 1h1 5 0l-1 2-2 1h0-3v-2h-2v-2h1c1-1 1-3 1-4v-1-7s-1-1-2-1z" class="P"></path><path d="M525 583h2 2 0v1c-1 0-1 0-1 1h-3v-2z" class="C"></path><path d="M528 553h1l2 2 1-1 3 4 3 8c0 6 0 12-3 18v2h-1c1-3 2-8 2-11v-1c1-5-1-12-4-16-1-2-2-3-3-4l-1-1z" class="c"></path><path d="M502 538c5 0 8 2 12 4l5 4c2 2 5 4 7 5 0 0 1 2 2 2l1 1h-1v3c-1-1-2-2-4-3-2-2-4-3-5-5-3-2-5-4-8-6h0c-2-1-3-1-4-2-2-1-3-2-5-3z" class="P"></path><path d="M496 533h8l16 3v2c-1 0-2 1-2 0v1c2 1 6 2 9 3 2 1 4 3 6 5l2 1h0v1c0 1 0 1 1 2 0 1 1 1 1 2v1 1 1c0 1 0 1 1 2h0l1 1h-1c-1 2 0 5 0 7l-3-8-3-4-1 1-2-2h-1c-1 0-2-2-2-2-2-1-5-3-7-5l-5-4c-4-2-7-4-12-4-4-1-10-2-15-2-2 1-5 1-8 1 2-1 6-1 8-1v-1h0c1-1 2-1 3-1l1-1h5z" class="H"></path><path d="M513 540l7 4c0 1 0 2-1 2l-5-4s-1-1-1-2z" class="V"></path><path d="M527 548c3 0 5 2 7 5 1 1 1 2 1 4v1l-3-4-5-6z" class="D"></path><path d="M504 533l16 3v2c-1 0-2 1-2 0v1l-2-1h-1-1l-1-1h-2c-1-1-2-1-3-1h0c1-1 1-1 2-1h1 0-3c0-1 0-1-1-1s-2 0-3-1z" class="C"></path><path d="M487 535c6 0 12 0 18 2 3 1 5 2 8 3 0 1 1 2 1 2-4-2-7-4-12-4-4-1-10-2-15-2-2 1-5 1-8 1 2-1 6-1 8-1v-1z" class="W"></path><path d="M520 544c3 1 5 2 7 4l5 6-1 1-2-2h-1c-1 0-2-2-2-2-2-1-5-3-7-5 1 0 1-1 1-2z" class="o"></path><path d="M226 199c1-1 2-2 3-2 1-1 2-1 3-1v-1c2 0 6 0 8 1 1 1 2 1 4 2v1c4 3 6 6 9 11v1l3 6c1 2 2 4 3 5h2c2-1 5 0 7 0 1 0 4-1 5 0 2 0 4 1 5 2 2 2 6 4 8 5 4 1 7 3 9 5l2 1c-2 0-3-1-5-1h0l3 3 2 2c4 1 7 5 9 8l2 1 2 2v1l1 2c-1 0-2 1-2 2 1 2 2 3 2 6v1c0 1 0 2-1 3v1h0l1-1c1 1 1 1 2 1l-1 1v2c2 1 1 5 1 7v2h0-1l-2-1-1-2h-1l-1 1c1-1 0-2 0-3h-1 0c-1 1-2 1-3 2-1 0-1 0-1-1-2 1-1 1-2 0h-2-1v1l2 1c-2 0-3 1-5 1h-1l-1 1 1 1c-1 1-1 1-2 1v1h1l-2 2 1 2c-3 1-5 2-8 3v1h-1-1-1-3 0l-1-1h-1 1-1c-2 0-2 1-3 2-1 0-2 1-2 2l-1 1h0 0c-2 0-3 0-4 1v1h-2c-1 0-3 0-4 1-3 1-5 1-7 1h-3l-7-1h-1l1 1c-1 0-1 1-2 1h-3-1c-1 0-2 1-4 1h-2l1 1h0l-12 3c-2 1-3 1-5 2-2 0-3 2-4 3-1 0-2 0-3 1v-1l-1-1-3 2c-1 0-2 1-3 2l-6 4-6 6c-2 1-5 2-6 4l-2 3v1l-1 1c-1 0-1 0-2-1h-1v-1h-1c0-1 1-3 2-4v-3-1c0-1 2-4 3-5h-2-1v-1l3-4v-1h0c2-3 5-4 7-6 1 0 2-1 2-1l3-2 2-1 1-1c2-1 3-2 4-3-1 0-2 1-4 2l1-2h-4 0v-1h1v-1h-1c-1 0 2-3 2-4l3-3 4-4-1-1h-1-1v-1h-2 0-2v-1-1h0-2 0l1-2c1-2 2-2 3-3 1 0 2-2 2-2l2-4 1-1v-1c0-1 0-1 1-2h0v1h1c0-1 1-2 1-4-1-1 0-4 0-6v-1c1-1 0-1 1-1 0-1 0-2 1-3h0c1-2 1-3 3-4 0-1 0-1 1-2v-2l2-16 3-9c0-1 0 0 1-1v-1l3-7 1-1h0c1-1 2-2 2-3 1-2 3-3 4-4z" class="s"></path><path d="M225 249l1-1 3 2h1v2l2 1h0-2c1 1 1 1 1 2h0c-2-1-3-1-4-2s-1-1-2-1h-1 0l1-1s1 1 2 1v-1h1c0-1-2-2-3-2z" class="W"></path><path d="M232 209l-2-1v-1h2l1-1v-1h-2c0-1 2-1 2-2 2 0 5 1 6 2-1 0-1 1-2 1-1-1-1 0-2 0v1l-3 1v1z" class="f"></path><path d="M224 255c1 1 1 2 2 4 0 3 0 6-1 9 0 1-1 2-1 2h1 0c1-1 2-2 3-2 0 1 1 1 2 1l-1 1c-2 0-2-1-3 0 0 1 1 1 1 1 0 1 0 1-1 1l-3-1v-1c1-3 2-6 2-9 0-1-1-3-1-4v-2z" class="b"></path><path d="M237 210c1-1 2-1 4 0h0-1l-3 1v1h4v1h-4c-2 1-5 1-6 0h6l-3-1c-2 0-4-1-6-1l8-1h1zm-13 47c0 1 1 3 1 4 0 3-1 6-2 9v1c0 1 0 2 1 3l1 1-2-1h0l-3-3 1-2c1-1 2-3 2-5v-4l1-1v-2z" class="R"></path><path d="M239 205c2 1 3 2 5 3v1s1 1 2 1l2 1h0c-2 0-3 0-4-1h-1-2 0c-2-1-3-1-4 0l-3-1h-2v-1l3-1v-1c1 0 1-1 2 0 1 0 1-1 2-1z" class="i"></path><path d="M234 209v-1c1 0 3 0 5-1 1 1 3 2 4 2l1 1h-1-2 0c-2-1-3-1-4 0l-3-1z" class="Q"></path><path d="M228 255c4 2 5 3 7 7h-1c0 1-1 1 0 3l-2 2c-1 1-2 0-4 0h0v-2c1-1 2 0 2-2 0 0 0-1-1-2h1l2 1v-1c-1-2-3-4-4-6h0z" class="W"></path><path d="M228 267h3c0-2 2-1 2-3l-2-1h0v-1h3c0 1-1 1 0 3l-2 2c-1 1-2 0-4 0z" class="r"></path><path d="M249 208l3 7c-2 1-3 1-5 0h-1-1c-1-1-2-2-4-2v-1h-4v-1l3-1h1 2 1c1 1 2 1 4 1h0l-2-1h2l1 1v1l1-1c-1-1-1-2-1-3z" class="k"></path><path d="M240 210c3 1 5 2 7 3-2 0-5 0-6-1h-4v-1l3-1z" class="P"></path><path d="M225 252c1 0 1 0 2 1s2 1 4 2h0l3 1c1 0 2 0 3-1v1l3 1h-1c-1 1-1 1-2 1v1c2 1 6 1 9 1h0c-1 1-2 1-3 1h-1-1v1h0c-1 0-1 1-2 1l-1-1-1 1 1 1c-1 0-2 1-3 1h-1c-1-2 0-2 0-3h1c-2-4-3-5-7-7l-3-3z" class="q"></path><path d="M237 255v1c0 1 0 1-1 2h-1v-1l-1-1c1 0 2 0 3-1z" class="c"></path><path d="M231 255l3 1 1 1v1c-2-1-3-2-4-3h0z" class="Y"></path><path d="M225 237c1 0 1 0 2 1l-1 1-1-1c0 2 1 2 2 3-1 0-3-1-3 0l3 2 1 1c1 1 0 2 1 3 0 0 1 1 2 1h-1-1v2l-3-2-1 1c-2-2-5-4-6-7l1-1c0 1 1 2 1 2h1s-1-1-1-2c1 0 1 0 1-1s-1-1-1-2h1 1c0-1 1-1 2-1z" class="N"></path><path d="M227 243l1 1c1 1 0 2 1 3 0 0 1 1 2 1h-1-1v2l-3-2 1-1-4-3 1-1 2 1 1-1z" class="o"></path><path d="M232 217c-2 0-4-1-6-2h3c2 1 4 1 6 1l-1-1c-2 0-5-1-6-2h3c1 1 4 1 6 0h4c2 0 3 1 4 2h-1-2c1 1 2 1 3 2 0 0 0 1-1 1v1l-6-1h-1l-1-1h-3-1z" class="P"></path><path d="M226 199h0 1c1 0 1-1 2-1l1-1 1 1c-8 6-12 14-15 23 0 0-1 0-1-1v-4c0-1 0 0 1-1v-1l3-7 1-1h0c1-1 2-2 2-3 1-2 3-3 4-4z" class="d"></path><path d="M223 260c0-2 0-3-1-4-2-3-5-4-7-7-5-8-2-20 0-29 0 1 1 1 1 1-1 7-2 13-1 20 0 3 1 5 2 7 2 3 5 5 7 7h0v2 2l-1 1zm3-61c1-1 2-2 3-2 1-1 2-1 3-1v-1c2 0 6 0 8 1 1 1 2 1 4 2v1c4 3 6 6 9 11v1c0 2 2 5 3 8 2 3 3 7 4 11 2 3 3 5 5 8 0 0 0 1-1 2l-1-2c-4-7-6-14-9-21l-2-2-3-7h0c-2-3-4-6-7-8l-3-2c-3-1-5-1-8 0l-1-1-1 1c-1 0-1 1-2 1h-1 0z" class="D"></path><path d="M230 197l3-1h4 0c2 1 4 1 6 3l-1 1-3-2c-3-1-5-1-8 0l-1-1z" class="X"></path><path d="M225 237c-1-1-1-2-2-3 2 0 3 1 4 2h2 1l-1-1c1-2 7 0 9-1h0c2 1 2 1 4 1h1l1 1 1 1c1 0 2 1 2 2h1l1 2h0c-2 1-3 0-5 2h0-1-2v1c-2 0-5-2-7-2h-1-2l-1-1h-2c0 1 0 1 1 1-1 1 0 1-1 1v1l-1-1-3-2c0-1 2 0 3 0-1-1-2-1-2-3l1 1 1-1c-1-1-1-1-2-1z" class="B"></path><path d="M244 236l1 1c0 1 1 2 2 3-1-1-2-1-3-2l-2-1 2-1z" class="M"></path><path d="M245 237c1 0 2 1 2 2h1l1 2h0c-2 1-3 0-5 2h0-1-2v1c-2 0-5-2-7-2h-1-2c1-1 2-1 2-1h2 3c1 1 2 1 4 1v-1h0 3 3v-1h-1c-1-1-2-2-2-3z" class="E"></path><path d="M225 237c-1-1-1-2-2-3 2 0 3 1 4 2h2 1l-1-1c1-2 7 0 9-1h0c2 1 2 1 4 1h1l1 1-2 1c-1-1-2-1-3-1s-2-1-3 0h0v1s0 1-1 1v1h2c1 0 1 0 2 1h1c-1 1-1 1-2 1v-1h-2s-1 0-1-1h-3c0 1 1 1 1 2 0 0-1 0-2 1l-1-1h-2c0 1 0 1 1 1-1 1 0 1-1 1v1l-1-1-3-2c0-1 2 0 3 0-1-1-2-1-2-3l1 1 1-1c-1-1-1-1-2-1z" class="D"></path><path d="M230 238h3c0 1-1 1-1 1 0 1 1 1 1 2 0 0-1 0-2 1l-1-1-2-1v-1c1 0 1 0 1-1h1z" class="I"></path><path d="M225 237c-1-1-1-2-2-3 2 0 3 1 4 2h2 1l-1-1 3 1c1 0 2 0 3 1-2 0-6-1-7 0 1 1 1 1 2 1h-1c0 1 0 1-1 1v1l2 1h-2c0 1 0 1 1 1-1 1 0 1-1 1v1l-1-1-3-2c0-1 2 0 3 0-1-1-2-1-2-3l1 1 1-1c-1-1-1-1-2-1z" class="Z"></path><path d="M224 223v-2h1l1-1 1-1h0l-1-1v-1h1 1c1 0 2 1 3 1l1-1h1 3l1 1-3 1h-1c1 0 2 1 3 1h0c0 1-1 1-1 1v1s1 0 2 1c0-1 1-1 2-1l-1 2h2 1 1c0 1 0 1 1 2h2v1h-1s-2 0-3 1h0 0l-1 1h-1-2l-1 1h2 1-2c0 1-1 1-1 2h-2v1c1 1 2 1 4 1-2 1-8-1-9 1l1 1h-1-2l1-1c0-1-4-3-5-4v-1c1 1 2 1 3 1h0l-1-2c1 0 1 0 1-1-1 0-1-1-2-2h0l-2-2 1-1s0 1 1 1v-1z" class="i"></path><path d="M224 223c1 0 2 1 2 1l2 2-1 1h-1c-1-1-1-1-2-1l-2-2 1-1s0 1 1 1v-1z" class="k"></path><path d="M234 233c-1 1-5 0-6-1h4v-1l-3-1h0c1-1 5-1 7 0h2 1-2c0 1-1 1-1 2h-2v1z" class="E"></path><path d="M224 223v-2h1l1-1 1-1h0l-1-1v-1h1 1l-1 1c1 1 3 1 4 1v1l-3 1h-1c1 1 2 1 2 1v1c0 1-1 1-2 1h-1s-1-1-2-1z" class="l"></path><path d="M232 217h1 3l1 1-3 1h-1c1 0 2 1 3 1h0c0 1-1 1-1 1v1s1 0 2 1l-1 1h-2 0c0-1 0-1-1-2-1 0-2 0-3-1h0 4v-1c-1 0-2-1-3-1s-3 0-4-1l1-1c1 0 2 1 3 1l1-1z" class="T"></path><path d="M237 223c0-1 1-1 2-1l-1 2h2 1 1c0 1 0 1 1 2h2v1h-1s-2 0-3 1h0 0l-1 1h-1-2c-2-1-5-1-6-1 0-1 1-1 1-1h1v-1c-1 0-1 0-2-1h1l2-1h0 2l1-1z" class="Q"></path><path d="M238 224h2l-1 1-2 2h0c-1-1-2-1-3-1h0v-1c1-1 2-1 4-1z" class="E"></path><path d="M241 224h1c0 1 0 1 1 2h2v1h-1s-2 0-3 1h0c-1 0-3-1-4-1l2-2 1-1h1z" class="M"></path><path d="M245 215h1 1c2 1 3 1 5 0l2 2c3 7 5 14 9 21 0 1 1 3 1 4s1 3 2 4l4 4c-1 0-1 0-1 1v2c-2-2-4-4-5-6-2-2-2-5-3-7-1-1-3-2-3-4-1-1-2-2-2-4h-1c-1-1-2-1-3-2v-1c0-1-1-1-3-2-1 0-1-1-2-2h-2v1h-2c-1-1-1-1-1-2h-1-1-2l1-2c-1 0-2 0-2 1-1-1-2-1-2-1v-1s1 0 1-1h0c-1 0-2-1-3-1h1l3-1h1l6 1v-1c1 0 1-1 1-1-1-1-2-1-3-2h2 1z" class="a"></path><path d="M245 215h1 1c2 1 3 1 5 0l2 2c-2 1-2 1-3 0h-2c-1 0-2-1-3-1 0 0-1 0-1-1h-1 1z" class="h"></path><path d="M247 225c1 1 3 2 4 1 2 1 4 3 5 4l1 2h-1-1c-1-1-2-1-3-2v-1c0-1-1-1-3-2-1 0-1-1-2-2z" class="S"></path><path d="M245 217c3 0 4 1 5 3v1 1c2 1 3 2 5 4-2-1-3-1-5-2v1h0 0c-2-1-3-1-5-2v-1h5l-6-4c1 0 1-1 1-1z" class="r"></path><path d="M237 218h1l6 1v-1l6 4h-5v1c2 1 3 1 5 2h0l1 1c-1 1-3 0-4-1h-2v1h-2c-1-1-1-1-1-2h-1-1-2l1-2c-1 0-2 0-2 1-1-1-2-1-2-1v-1s1 0 1-1h0c-1 0-2-1-3-1h1l3-1z" class="L"></path><path d="M239 222l2 1h0v1h-1-2l1-2z" class="J"></path><path d="M238 218l6 1h0v1h-5c-1 0-1-1-1-2z" class="B"></path><path d="M245 226v-1h2c1 1 1 2 2 2 2 1 3 1 3 2v1c1 1 2 1 3 2l-1 1v2c0 1 1 1 1 2l-2 1 4 3 1 1v3l1 1v1c-1 0-2 0-2-1-1 1-2 1-3 2l2 1v2h-1c-1-1-3-2-5-2l-1-1-3-1c-2-1-3-2-5-3v-1h2 1 0c2-2 3-1 5-2h0l-1-2h-1c0-1-1-2-2-2l-1-1-1-1h-1c-2 0-2 0-4-1h0c-2 0-3 0-4-1v-1h2c0-1 1-1 1-2h2-1-2l1-1h2 1l1-1h0 0c1-1 3-1 3-1h1v-1z" class="R"></path><path d="M240 231h4 0c1 1 1 1 2 1-1 1-1 1-2 1h-1v1c-1-1-3-1-4-2l1-1z" class="L"></path><path d="M245 226v-1h2c1 1 1 2 2 2 0 1-1 1-2 1h0c-1 1-2 1-3 1h-1c-1 0-1 0-2-1h0 0c1-1 3-1 3-1h1v-1z" class="E"></path><path d="M239 230l1 1-1 1c1 1 3 1 4 2v-1l4 2v1c-1 0-3-1-4-1h-1c-2 0-2 0-4-1h0c-2 0-3 0-4-1v-1h2c0-1 1-1 1-2h2z" class="B"></path><path d="M238 234v-1h1c1 1 1 1 2 1s1 0 1 1c-2 0-2 0-4-1z" class="C"></path><path d="M253 238l-4-3h1 2c-1-1-3-2-4-3h-2v-1c2-1 3 0 4 0l-1-2c1 0 2 1 3 1 1 1 2 1 3 2l-1 1v2c0 1 1 1 1 2l-2 1h0z" class="a"></path><path d="M252 240c-2-1-3-2-5-3h0c2-1 3 0 5 1h1 0l4 3 1 1v3l1 1v1c-1 0-2 0-2-1-1 1-2 1-3 2l2 1v2h-1c-1-1-3-2-5-2l-1-1-3-1c0-1 0-2 1-3h0c1-1 1-1 2-1h1v-2c1 0 1 0 2-1z" class="g"></path><path d="M252 240l3 3h-1-3l-1-2c1 0 1 0 2-1z" class="j"></path><path d="M250 241l1 2h-2v1l1 1c0 2-1 2-1 3l-3-1c0-1 0-2 1-3h0c1-1 1-1 2-1h1v-2z" class="F"></path><path d="M257 241l1 1v3l1 1v1c-1 0-2 0-2-1-1 1-2 1-3 2h0c-1-1-2-2-2-3h1l1-1h1 2c0-1-2-1-2-2 1-1 1-1 2-1z" class="b"></path><path d="M230 241l1 1h2 1c2 0 5 2 7 2 2 1 3 2 5 3l3 1 1 1c2 0 4 1 5 2h1c1 1 3 2 4 3v1h0c-1 0-1 0-2-1 0 1-1 1-1 2h0l-1 1c-1 0-2 0-3 1l1 1h0-4c-1 1-2 1-4 1-3 0-7 0-9-1v-1c1 0 1 0 2-1h1l-3-1v-1c-1 1-2 1-3 1l-3-1c0-1 0-1-1-2h2 0l-2-1v-2h-1v-2h1 1c-1 0-2-1-2-1-1-1 0-2-1-3v-1c1 0 0 0 1-1-1 0-1 0-1-1h2z" class="L"></path><path d="M245 257l-1-1v-1l1-1 1 1v1 1h-1z" class="C"></path><path d="M241 248l2 1c-1 0-3 0-5-1h1 2z" class="e"></path><path d="M234 246c1 1 2 1 3 1h4v1h-2-1-2l-1 1h-1l1-2h0l-1-1z" class="J"></path><path d="M236 244c1 1 2 1 3 2l2 1h-4c-1 0-2 0-3-1l-1-1c1-1 2 0 3-1z" class="L"></path><path d="M234 242c2 0 5 2 7 2 2 1 3 2 5 3l3 1 1 1v1c-2 0-3-1-5-1h-2l-2-1v-1l-2-1c-1-1-2-1-3-2l-2-2z" class="B"></path><path d="M230 241l1 1h2 1l2 2c-1 1-2 0-3 1l1 1 1 1h0l-1 2v-1c-1 2-2 0-3 1l-1 1h-1v-2h1 1c-1 0-2-1-2-1-1-1 0-2-1-3v-1c1 0 0 0 1-1-1 0-1 0-1-1h2z" class="F"></path><path d="M233 242h1l2 2c-1 1-2 0-3 1l1 1 1 1h0l-1 2v-1c-1 0-2 0-2-1v-1c-1-1-3-2-4-3 2 0 4 0 5-1z" class="e"></path><path d="M250 249c2 0 4 1 5 2l1 1c0 1-1 1-1 1h-1l-3-1c-1 1-2 1-3 1v1c-1 1-1 1-2 1l-1-1h0c-2 0-1-3-3-4h-1-1-1c2-1 4-1 6 0v-1c2 0 3 1 5 1v-1z" class="B"></path><path d="M245 254c1-1 2 0 3 0-1 1-1 1-2 1l-1-1h0z" class="J"></path><path d="M250 249c2 0 4 1 5 2l1 1c0 1-1 1-1 1h-1l-3-1c-2-1-4-1-6-2v-1c2 0 3 1 5 1v-1z" class="P"></path><path d="M234 248v1h1l2 1h1c2 1 3 1 4 2l-1 1-1 1v1h-1-2 0c-1 1-2 1-3 1l-3-1c0-1 0-1-1-2h2 0l-2-1v-2l1-1c1-1 2 1 3-1z" class="q"></path><path d="M234 248v1h1l2 1c-2 1-2 1-3 3v-2c-1 0-2-1-4-1l1-1c1-1 2 1 3-1z" class="I"></path><path d="M237 250h1c2 1 3 1 4 2l-1 1-1 1v1h-1l-1-1c-1 0-3-1-4-1 1-2 1-2 3-3z" class="P"></path><path d="M237 250h1 0c-1 1-1 1-2 1h0c1 1 2 1 3 1v1c-1 0-1 0-1 1-1 0-3-1-4-1 1-2 1-2 3-3z" class="R"></path><path d="M255 251h1c1 1 3 2 4 3v1h0c-1 0-1 0-2-1 0 1-1 1-1 2h0l-1 1c-1 0-2 0-3 1l1 1h0-4c-1 1-2 1-4 1-3 0-7 0-9-1v-1c1 0 1 0 2-1h1l-3-1v-1h0 2 1c1 1 3 2 5 2h0 1v-1-1c1 0 1 0 2-1v-1c1 0 2 0 3-1l3 1h1s1 0 1-1l-1-1z" class="Z"></path><path d="M255 251h1c1 1 3 2 4 3h-4c-1 1 0 1-1 1v-2s1 0 1-1l-1-1zm-16 4h1c1 1 3 2 5 2h0c2 1 2 1 3 1s2-1 2-2l1 2h-1c-3 1-4 1-7 1h0c-1 0-2-1-3-2l-3-1v-1h0 2z" class="R"></path><path d="M251 252l3 1h1v2s-1 0-2 1h-3c0 1-1 2-2 2s-1 0-3-1h0 0 1v-1-1c1 0 1 0 2-1v-1c1 0 2 0 3-1z" class="L"></path><path d="M248 254h3c-2 1-3 3-6 3h0 0 1v-1-1c1 0 1 0 2-1z" class="M"></path><path d="M251 252l3 1c-1 1-2 1-3 1h-3v-1c1 0 2 0 3-1z" class="G"></path><path d="M257 256c0-1 1-1 1-2 1 1 1 1 2 1h0v-1c1 1 2 2 2 3 2 1 3 2 4 3v1h-1v1c0 2-2 2-2 3v1h-1c-1 1 0 1 0 2 0 0-1 1-2 1 0 0-1 0-1-1l-1 1h1l-1 2-1-1h0v-1c-1-1-1-1-1-2h-1c-1 1-1 0-2 0h-2v2h-1c-1 0-1 1-2 1v1c-1 1-1 1-2 1 0 0-1-1-2-1h0c0 1 1 1 1 3-1 1-7 0-9 0h-1c-1 0-1-1-1-2h0c-1 1-1 2-2 2-2 1-3 2-5 2-1 0-2-1-2-1h0l-1-1c-1-1-1-2-1-3v-1 1l3 1c1 0 1 0 1-1 0 0-1 0-1-1 1-1 1 0 3 0l1-1c-1 0-2 0-2-1v-1h0c2 0 3 1 4 0l2-2h1c1 0 2-1 3-1l-1-1 1-1 1 1c1 0 1-1 2-1h0v-1h1 1c1 0 2 0 3-1h0c2 0 3 0 4-1h4 0l-1-1c1-1 2-1 3-1l1-1h0z" class="S"></path><path d="M228 273v-1h2 2v-1h-2v-1h0 3v1h0l1 1c-1 1-1 2-2 2v-1l-1 1h-2s0-1-1-1z" class="r"></path><path d="M223 270v1l3 1c1 1 1 1 2 1s1 1 1 1h2l1-1v1c-2 1-3 2-5 2-1 0-2-1-2-1h0l-1-1c-1-1-1-2-1-3v-1z" class="K"></path><path d="M223 270v1l3 1c1 1 1 1 2 1s1 1 1 1h0-5c-1-1-1-2-1-3v-1z" class="N"></path><path d="M234 265h1c1 0 2-1 3-1l-1-1 1-1 1 1c1 0 1-1 2-1h0v-1h1 1c1 0 2 0 3-1v2h0c-1 0-1-1-2-1l-1 1h-1c0 1 0 1-1 2h-2c0 1-1 2-2 2h-1c0 2 0 2-1 3h-4-1c-1 0-2 0-2-1v-1h0c2 0 3 1 4 0l2-2z" class="f"></path><path d="M234 272c0-1 1-2 1-3l2 1c0-1 0-2-1-3h2c0-1 0-1 1-1l1 2 3 3h1c0 1 1 1 1 3-1 1-7 0-9 0h-1c-1 0-1-1-1-2z" class="s"></path><path d="M240 268v-2-1c1 0 2 1 3 1h0v-2c1-1 3-1 4 0h1c0-1 0-2-1-3h0c2 0 2 0 3 1s4 2 5 4l1 1h-1c-1 1-1 0-2 0h-2v2h-1c-1 0-1 1-2 1v1c-1 1-1 1-2 1 0 0-1-1-2-1h0-1l-3-3z" class="k"></path><path d="M257 256c0-1 1-1 1-2 1 1 1 1 2 1h0v-1c1 1 2 2 2 3 2 1 3 2 4 3v1h-1v1c0 2-2 2-2 3v1h-1c-1 1 0 1 0 2 0 0-1 1-2 1 0 0-1 0-1-1l-1 1h1l-1 2-1-1h0v-1c-1-1-1-1-1-2l-1-1c-1-2-4-3-5-4l1-1-1-1c1-1 3 0 4 0l1-1v-1c1-1 1 1 3 0 0-1-1-1-1-2h0z" class="p"></path><path d="M251 261l-1-1c1-1 3 0 4 0l3 3 1 1-1 1h0c-1 0-2 0-2 1-1-2-4-3-5-4l1-1z" class="k"></path><path d="M251 261c1 1 3 3 5 3h1v-1l1 1-1 1h0c-1 0-2 0-2 1-1-2-4-3-5-4l1-1z" class="a"></path><path d="M257 256c1 0 3-1 4 0s2 3 3 5c0 0-1 0-1 1l-2-1v1 2h0c-3-1-5-3-6-5v-1c1-1 1 1 3 0 0-1-1-1-1-2h0z" class="n"></path><path d="M215 216v4c-2 9-5 21 0 29 2 3 5 4 7 7 1 1 1 2 1 4v4c0 2-1 4-2 5l-1 2c-1 1-1 1-1 2l-3 2v-2h-1l-1 3v1h0l-1-1c-2-1-3-2-4-2-1-1-2-5-3-6s-2-1-2-2v-1h-1v-1c0-1 1-2 1-4-1-1 0-4 0-6v-1c1-1 0-1 1-1 0-1 0-2 1-3h0c1-2 1-3 3-4 0-1 0-1 1-2v-2l2-16 3-9z" class="s"></path><path d="M212 255c1 2 2 2 2 4l1 6 1 1c1-2 1-4 0-5v-3l1-1c1 1 1 2 1 3 0 0 1 1 2 1l1 2c1 1 1 1 2 1 0 2-1 4-2 5l-1 2c-1 1-1 1-1 2l-3 2v-2h-1l-1 3c-2-1-4-4-5-6v-1h1 2c-1-2-1-4 0-6h0v-1c1-1 0-5 0-7z" class="l"></path><path d="M215 265v1l-1-1c-1 0-1-1-1-2s0-2 1-4l1 6z" class="N"></path><path d="M215 272l-1 1v-1l-1-1c0-2 1-2 2-4h0 2l-1 1v2c0 1 0 1-1 2z" class="s"></path><path d="M219 262l1-1 1 2c1 1 1 1 2 1 0 2-1 4-2 5l-1 2c-1 1-1 1-1 2l-3 2v-2h-1v-1c1-1 1-1 1-2v-2c2 0 2-1 2-3l1-1v-2z" class="b"></path><path d="M218 271c1-1 1-1 1-2s1-1 1-2h1c0 1-1 1 0 2h0l-1 2c-1 1-1 1-1 2l-3 2v-2l2-2z" class="g"></path><path d="M219 262l1 1v2l-1 1-2 4 1 1-2 2h-1v-1c1-1 1-1 1-2v-2c2 0 2-1 2-3l1-1v-2z" class="S"></path><defs><linearGradient id="X" x1="207.523" y1="272.98" x2="209.166" y2="255.58" xlink:href="#B"><stop offset="0" stop-color="#515150"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#X)" d="M204 253c1-1 0-1 1-1 0-1 0-2 1-3h0c1-2 1-3 3-4 0-1 0-1 1-2v-2 3c0 2 0 3 1 5 0 2 0 4 1 6 0 2 1 6 0 7v1h0c-1 2-1 4 0 6h-2-1v1c1 2 3 5 5 6v1h0l-1-1c-2-1-3-2-4-2-1-1-2-5-3-6s-2-1-2-2v-1h-1v-1c0-1 1-2 1-4-1-1 0-4 0-6v-1z"></path><path d="M209 264l1 1v4h0-1v1c-1-2-1-4 0-6z" class="S"></path><path d="M210 258l1 1v4h1 0c-1 2-1 4 0 6h-2 0v-4l-1-1c1-2 1-3 1-6z" class="r"></path><path d="M204 260c0-1 1-3 1-4h1c0 2-1 3 0 5h0c-1 2 0 5 0 7-1-1-2-1-2-2v-1h-1v-1c0-1 1-2 1-4z" class="p"></path><path d="M204 253c1-1 0-1 1-1 0-1 0-2 1-3h0c1-2 1-3 3-4 0-1 0-1 1-2v-2 3c0 2 0 3 1 5 0 2 0 4 1 6 0 2 1 6 0 7v1h-1v-4l-1-1v-3h-1c0 1-1 1-2 1v-1l-1 1h0-1c0 1-1 3-1 4-1-1 0-4 0-6v-1z" class="q"></path><path d="M207 255v-6h1c1 2 1 4 1 6 0 1-1 1-2 1v-1z" class="i"></path><path d="M210 255c0-1 0-4-1-5 0-2 1-5 1-6 0 2 0 3 1 5 0 2 0 4 1 6 0 2 1 6 0 7v1h-1v-4l-1-1v-3z" class="Y"></path><path d="M211 259l1-2h0v5 1h-1v-4z" class="g"></path><path d="M265 238l-5-8c-1-4-2-8-4-11-1-3-3-6-3-8l3 6c1 2 2 4 3 5h2c2-1 5 0 7 0 1 0 4-1 5 0 2 0 4 1 5 2 2 2 6 4 8 5 4 1 7 3 9 5l2 1c-2 0-3-1-5-1h0l3 3 2 2c4 1 7 5 9 8l2 1 2 2v1l1 2c-1 0-2 1-2 2 1 2 2 3 2 6v1c0 1 0 2-1 3v1h0l1-1c1 1 1 1 2 1l-1 1v2c2 1 1 5 1 7v2h0-1l-2-1-1-2h-1l-1 1c1-1 0-2 0-3h-1 0c-1 1-2 1-3 2-1 0-1 0-1-1-2 1-1 1-2 0h-2c1-1 1-1 1-2s-1-1-2-2-1-1-1-2h1v-4h0l-2-3h0l-2-4-2 1-1-2h-1v1l1 2v1 1l-1 1v-1c-1 0-2-1-2-2h-1-2c0 1-1 1-1 2h-2v-1h-3v1h-1l-1-1v-1h0l-1-1-1-1h0v1h-2c-1 0 0 0-1 1l-2-2v1 1l-1 1c0 1 0 1-1 2l-1-1v-1c-1-1-2-2-4-3 0-1-1-2-2-3v1-1c-1-1-3-2-4-3v-2l-2-1c1-1 2-1 3-2 0 1 1 1 2 1v-1l-1-1v-3l-1-1-4-3 2-1c0-1-1-1-1-2v-2l1-1h1c0 2 1 3 2 4 0 2 2 3 3 4 1 2 1 5 3 7 1 2 3 4 5 6v-2c0-1 0-1 1-1l-4-4c-1-1-2-3-2-4s-1-3-1-4l1 2c1-1 1-2 1-2z" class="s"></path><path d="M269 251l2 4h0l-1 1c-1-1-1-2-1-3v-2z" class="n"></path><path d="M265 238l3 3c-1 0-1 1-2 1l-2-2c1-1 1-2 1-2z" class="O"></path><path d="M293 247h1v1c0 1 1 2 2 3-1 1-1 2-2 2h-1c1-1 1 0 1-1s-1-2-1-2v-3z" class="n"></path><path d="M293 253h1c1 1 2 3 2 4l1 2-2 2-2-4h0 0c1 0 1 0 2-1l-2-2v-1z" class="b"></path><path d="M268 222c1 0 4-1 5 0 2 0 4 1 5 2h-5c-3 0-4-1-5-2z" class="l"></path><path d="M268 241c3 2 5 5 8 7l-1 1-2-1h0l-7-6c1 0 1-1 2-1z" class="R"></path><path d="M276 234c0-1-1-2-2-3s-2-1-3-2h2v-1h-1c-1-1-2-1-2-2h0 0l-1-1h0c1 0 2 1 3 1h1v1c0 1 1 1 2 2h-1 0l1 2c1 1 3 3 4 3 3 1 5 3 8 5l5 5v1c-2-2-4-5-7-6l-3-2s-1-1-2-1-2-1-4-2z" class="a"></path><path d="M263 238l1 2 2 2 7 6h0l2 1 1-1h1v1c-1 1 0 1-1 1s-1 1-3 0c-1 0 0-1-2-1v1h-1l-4-4c-1-1-2-3-2-4s-1-3-1-4z" class="W"></path><path d="M276 234c2 1 3 2 4 2s2 1 2 1l3 2-1 1 1 1v3 1h-1l-2-3-5-4c-1-1-2-2-3-4h0 1 1 0z" class="k"></path><path d="M277 238c2 0 3 0 4 1 0 0 1 2 1 3h0l-5-4z" class="a"></path><path d="M273 224h5c2 2 6 4 8 5 4 1 7 3 9 5l2 1c-2 0-3-1-5-1-1-1-1-1-3-1l-1 1c-1-1-2-2-4-2h-2c0-1-1-1-1-2l-5-2h3s1 0 1 1h1 1l1 1h2l1 1h0 1 1l2 1c2 0-1 0 1 0-3-2-7-3-10-5-1-1-2-1-3-1s-4-1-5-2z" class="h"></path><path d="M281 230l8 3-1 1c-1-1-2-2-4-2h-2c0-1-1-1-1-2z" class="W"></path><path d="M275 258l1-1v-1h3c1 0 1 1 3 1h0v-2l1-1c0 1 1 2 2 2v-1c1-2 0-3-1-5l2-2c1 0 2 1 3 2l1 2v1c1 1 2 2 3 4h0l-2 1-1-2h-1v1l1 2v1 1l-1 1v-1c-1 0-2-1-2-2h-1-2c0 1-1 1-1 2h-2v-1h-3v1h-1l-1-1v-1h0l-1-1z" class="m"></path><path d="M276 259h1l-1-2h1s1 0 2 1c1 0 1 0 1 1h3v-2h0l1-1 1 2 1 1h-2c0 1-1 1-1 2h-2v-1h-3v1h-1l-1-1v-1z" class="N"></path><path d="M285 258c2-1 1-2 2-4l-1-2h0 2c0-1 0-1 1-2l1 2v1c1 1 2 2 3 4h0l-2 1-1-2h-1v1l1 2v1 1l-1 1v-1c-1 0-2-1-2-2h-1l-1-1z" class="S"></path><path d="M289 257l1 2v1 1l-1 1v-1c-1 0-2-1-2-2l1 1c0-2 0-2 1-3h0z" class="N"></path><path d="M289 256c-1-1-2-2-2-3 1 0 1 1 2 2h1v-2c1 1 2 2 3 4h0l-2 1-1-2h-1z" class="Y"></path><path d="M258 242c3 3 5 5 7 8 1 3 3 5 4 7v1 1l-1 1c0 1 0 1-1 2l-1-1v-1c-1-1-2-2-4-3 0-1-1-2-2-3v1-1c-1-1-3-2-4-3v-2l-2-1c1-1 2-1 3-2 0 1 1 1 2 1v-1l-1-1v-3z" class="k"></path><path d="M261 253c1 1 2 2 1 4 0-1-1-2-2-3l1-1z" class="n"></path><path d="M256 249c1 0 2 1 2 2 2 0 2 0 3 2l-1 1v1-1c-1-1-3-2-4-3v-2z" class="l"></path><path d="M265 250c1 3 3 5 4 7v1 1l-1 1c0 1 0 1-1 2l-1-1v-1l1 1v-1l-1-1v-1h2 0c0-1 0-1-1-2 0 0-1-1-1-2-1-1-1-2-2-3l1-1z" class="a"></path><defs><linearGradient id="Y" x1="299.32" y1="243.569" x2="288.657" y2="258.04" xlink:href="#B"><stop offset="0" stop-color="#09090a"></stop><stop offset="1" stop-color="#292829"></stop></linearGradient></defs><path fill="url(#Y)" d="M273 226l3 2 5 2c0 1 1 1 1 2h2c2 0 3 1 4 2l1-1c2 0 2 0 3 1h0l3 3 2 2c4 1 7 5 9 8l2 1 2 2v1l1 2c-1 0-2 1-2 2 1 2 2 3 2 6v1c0 1 0 2-1 3v1h0l1-1c1 1 1 1 2 1l-1 1v2c2 1 1 5 1 7v2h0-1l-2-1-1-2h-1l-1 1c1-1 0-2 0-3h-1 0c-1 1-2 1-3 2-1 0-1 0-1-1-2 1-1 1-2 0h-2c1-1 1-1 1-2s-1-1-2-2-1-1-1-2h1v-4h0l-2-3h0l2-2-1-2c0-1-1-3-2-4 1 0 1-1 2-2-1-1-2-2-2-3v-1h-1c0-1-1-1-1-2v-1l-5-5c-3-2-5-4-8-5-1 0-3-2-4-3l-1-2h0 1c-1-1-2-1-2-2v-1z"></path><path d="M312 278c-1-1-1-1-1-2v-1h2v1 2h0-1zm-28-43h2l2 1v2c-1-1-3-2-4-3z" class="k"></path><path d="M282 232h2l2 3h-2l-3-3h1z" class="l"></path><path d="M307 273v-2h2v4h-1l-1 1c1-1 0-2 0-3z" class="I"></path><path d="M301 257c1 0 2 0 3 1 0 1 1 3 1 5-2-2-3-4-4-6z" class="m"></path><path d="M284 232c2 0 3 1 4 2h0c0 1 1 1 0 2l-2-1-2-3z" class="b"></path><path d="M310 266h0l1-1c1 1 1 1 2 1l-1 1v2c2 1 1 5 1 7v-1-3l-1-1h-1v-2c-1-1-1-2-1-3z" class="N"></path><path d="M273 226l3 2 5 2c0 1 1 1 1 2h-1l-6-3c-1-1-2-1-2-2v-1z" class="p"></path><path d="M289 233c2 0 2 0 3 1h0l3 3 2 2h0c1 1 1 2 2 3h-1 0c-1 0-1 0-2-1h0 0c1 1 1 2 1 4h0c-2-3-6-6-9-7v-2c1-1 0-1 0-2h0l1-1z" class="a"></path><path d="M295 237l2 2h0c1 1 1 2 2 3h-1l-4-3c1 0 1-1 1-2z" class="b"></path><path d="M289 233c2 0 2 0 3 1h0l3 3c0 1 0 2-1 2-2-1-4-3-6-5h0l1-1z" class="p"></path><path d="M297 239c4 1 7 5 9 8l2 1 2 2v1l1 2c-1 0-2 1-2 2 1 2 2 3 2 6v1c0 1 0 2-1 3v-2h0c0-3-1-7-3-8-1-2-2-4-3-5l-3-3c-1-1-1-3-3-5h0 1c-1-1-1-2-2-3h0z" class="m"></path><path d="M306 247l2 1 2 2v1l1 2c-1 0-2 1-2 2 0-2-2-4-3-6 0 0-1-1-1-2h1z" class="n"></path><path d="M306 247l2 1 2 2c-1 0-2 0-3-1h0-1s-1-1-1-2h1z" class="a"></path><path d="M294 248l7 9c1 2 2 4 4 6 0 1 1 2 2 3 0 2 0 3 1 4l1 1h-2v2h-1 0c-1 1-2 1-3 2-1 0-1 0-1-1-2 1-1 1-2 0h-2c1-1 1-1 1-2s-1-1-2-2-1-1-1-2h1v-4h0l-2-3h0l2-2-1-2c0-1-1-3-2-4 1 0 1-1 2-2-1-1-2-2-2-3z" class="K"></path><path d="M296 251c1 2 2 4 3 5l-1 1h-2c0-1-1-3-2-4 1 0 1-1 2-2z" class="h"></path><path d="M300 263v-1-1l1-1 3 5s0 1 1 2l-2 1-2-4s0-1-1-1z" class="W"></path><path d="M301 264l2 4 2-1c1 2 1 4 1 6h0c-1 1-2 1-3 2 1-2 0-1 0-2v-3c-1-1-2-4-2-6z" class="K"></path><path d="M305 267c1 2 1 4 1 6h0c-1 0-1-1-1-1l-1-1c0-1-1-2-1-3l2-1z" class="c"></path><path d="M299 256l2 4-1 1v1 1s0 1-1 2c-1-1-1-1-2-1l-2-3h0l2-2-1-2h2l1-1z" class="N"></path><path d="M300 263c1 0 1 1 1 1 0 2 1 5 2 6v3c0 1 1 0 0 2-1 0-1 0-1-1-2 1-1 1-2 0h-2c1-1 1-1 1-2s-1-1-2-2-1-1-1-2h1v-4h0c1 0 1 0 2 1 1-1 1-2 1-2z" class="i"></path><path d="M297 264c1 1 2 2 2 3s1 1 1 2c-2 0-2 0-3-1v-4z" class="j"></path><path d="M300 263c1 0 1 1 1 1 0 2 1 5 2 6l-1 1-1-1c0-1-1-3-2-3 0-1-1-2-2-3h0c1 0 1 0 2 1 1-1 1-2 1-2z" class="c"></path><path d="M289 256h1l1 2 2-1 2 4h0l2 3h0v4h-1c0 1 0 1 1 2s2 1 2 2 0 1-1 2h-1v1l2 1c-2 0-3 1-5 1h-1l-1 1 1 1c-1 1-1 1-2 1v1h1l-2 2 1 2-8 3v1h-1-1-1-3 0l-1-1h-1 1-1c-2 0-2 1-3 2-1 0-2 1-2 2l-1 1h0 0c-2 0-3 0-4 1v1h-2c-1 0-3 0-4 1-3 1-5 1-7 1h-3l-7-1h-1l1 1c-1 0-1 1-2 1h-3-1c-1 0-2 1-4 1h-2l1 1h0l-12 3c-2 1-3 1-5 2-2 0-3 2-4 3-1 0-2 0-3 1v-1l-1-1-3 2c-1 0-2 1-3 2l-6 4-6 6c-2 1-5 2-6 4l-2 3v1l-1 1c-1 0-1 0-2-1h-1v-1h-1c0-1 1-3 2-4v-3-1c0-1 2-4 3-5h-2-1v-1l3-4v-1h0c2-3 5-4 7-6 1 0 2-1 2-1l3-2 2-1 1-1c2-1 3-2 4-3-1 0-2 1-4 2l1-2h-4 0v-1h1v-1h-1c-1 0 2-3 2-4l3-3 4-4-1-1h-1-1v-1h-2 0-2v-1-1h0-2 0l1-2c1-2 2-2 3-3 1 0 2-2 2-2l2-4 1-1v-1c0-1 0-1 1-2h0v1h1v1h1v1c0 1 1 1 2 2s2 5 3 6c1 0 2 1 4 2l1 1h0v-1l1-3h1v2l3-2c0-1 0-1 1-2l3 3h0l2 1h0s1 1 2 1c2 0 3-1 5-2 1 0 1-1 2-2h0c0 1 0 2 1 2h1c2 0 8 1 9 0 0-2-1-2-1-3h0c1 0 2 1 2 1 1 0 1 0 2-1v-1c1 0 1-1 2-1h1v-2h2c1 0 1 1 2 0h1c0 1 0 1 1 2v1h0l1 1 1-2h-1l1-1c0 1 1 1 1 1 1 0 2-1 2-1 0-1-1-1 0-2h1v-1c0-1 2-1 2-3v-1h1l1 1c1-1 1-1 1-2l1-1v-1-1l2 2c1-1 0-1 1-1h2v-1h0l1 1 1 1h0v1l1 1h1v-1h3v1h2c0-1 1-1 1-2h2 1c0 1 1 2 2 2v1l1-1v-1-1l-1-2v-1z" class="s"></path><path d="M230 295h-2v-1l1-1h-2c-2 0-4 0-7-1 2 0 3 0 5-1h1v1l3 1h3l-2 1v1zm-21-15l3-2v1c0 1 0 1-1 2h1l2-1 1 1v1c0 1-2 2-3 3v-1c0-1 1-1 1-2l-1-1c0 1-1 1-2 1v-1-1h-1z" class="k"></path><path d="M212 281l2-1 1 1v1l-1 1v-2h-2z" class="n"></path><path d="M228 296c3 0 6 0 8 1v1h0c-1 0-2 1-4 1 1 0 1-1 1-1-1-1-10 0-12 0 2-1 5-1 7-2h0z" class="h"></path><defs><linearGradient id="Z" x1="233.788" y1="294.473" x2="239.639" y2="297.433" xlink:href="#B"><stop offset="0" stop-color="#191717"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#Z)" d="M230 295h4c2 0 5 0 7 1l1 1c-1 0-1 1-2 1h-3-1 0v-1c-2-1-5-1-8-1 1-1 2-1 3-1h-1z"></path><path d="M200 304l-1 1c1 0 2 0 2-1 2-2 10-4 12-4l-6 3c-1 1-3 2-5 3-1 1-3 3-4 3v-1h-2c-1 0-1 0-1-1 1-1 1-1 3-2l2-1z" class="a"></path><path d="M196 308l4-2h2c-1 1-3 3-4 3v-1h-2z" class="l"></path><defs><linearGradient id="a" x1="216.299" y1="302.269" x2="217.058" y2="305.636" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#a)" d="M206 307c8-4 15-7 24-8l1 1h0l-12 3c-2 1-3 1-5 2-2 0-3 2-4 3-1 0-2 0-3 1v-1l-1-1z"></path><path d="M233 282v-1c1-1 1 0 3 0l-2 1c1 0 1 1 1 1h3c0 1-1 1-1 1-1 0-1 0-2 1h0c1 0 1 1 1 1l1 1v1h-5l-1 1h-2l-1-1h0 2l-4-1h0v-1l4-1s1-1 2-1h0 2 0v-1l-1-1z" class="l"></path><path d="M235 283h3c0 1-1 1-1 1-1 0-1 0-2 1h0c1 0 1 1 1 1l1 1v1h-5 1l-1-1v-1c1 0 2-1 3-2v-1z" class="b"></path><path d="M195 298c1 1 2 1 3 1 1-1 3-2 4-3h0c-1 2-3 4-5 6 1 0 2 1 3 1h1c0 1-1 1-1 1l-2 1-1-2c-1 0 0 0-1 1-1 0-1 1-3 1l-2 2h-2-1c-2 1-3 2-5 3h0c0-1 0-1 1-1 2-1 3-4 6-4 1 0 1-1 2-1v-1h-2l-1-1 3-2 2-1 1-1z" class="h"></path><path d="M194 299v1h1s0-1 1-1v1l-4 3h-2l-1-1 3-2 2-1z" class="m"></path><path d="M187 303c1 0 2-1 2-1l1 1h2v1c-1 0-1 1-2 1-3 0-4 3-6 4-1 0-1 0-1 1h0c2-1 3-2 5-3h1 2c-2 2-3 2-5 3-2 2-4 3-6 5h0-2-1v-1l3-4v-1h0c2-3 5-4 7-6z" class="p"></path><defs><linearGradient id="b" x1="226.701" y1="279.249" x2="222.32" y2="271.847" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#3a393b"></stop></linearGradient></defs><path fill="url(#b)" d="M219 273c0-1 0-1 1-2l3 3h0l2 1h0s1 1 2 1c2 0 3-1 5-2 1 0 1-1 2-2h0c0 1 0 2 1 2h1l-2 2h0 1 3c0 1 0 1-1 1-1-1-4 0-5 0s-2 1-3 1h-2c-3 0-6 0-8 2h0-2 0l-1-1h-2l-1 1v-1s0-1 1-2h0v-1l1-3h1v2l3-2z"></path><path d="M215 273h1v2l-1 1c1 1 1 1 1 3h-1l-1-2v-1l1-3z" class="V"></path><path d="M219 273c0-1 0-1 1-2l3 3h0l-3 2h-1c-1 0-1 0-1-1s0-1 1-1v-1z" class="S"></path><defs><linearGradient id="c" x1="197.133" y1="310.343" x2="178.603" y2="312.109" xlink:href="#B"><stop offset="0" stop-color="#39363a"></stop><stop offset="1" stop-color="#585956"></stop></linearGradient></defs><path fill="url(#c)" d="M191 307l2-2c2 0 2-1 3-1 1-1 0-1 1-1l1 2c-2 1-2 1-3 2 0 1 0 1 1 1h2v1l-2 1c-2 1-4 3-6 4l-1 1c-1-1-1-1-2-1v-1c-1 1-4 3-5 4l-2 3h0l-3 4v-3-1c0-1 2-4 3-5h0c2-2 4-3 6-5 2-1 3-1 5-3z"></path><path d="M180 315c1 1 0 2-1 3h0v1c1 0 1 1 1 1l-3 4v-3-1c0-1 2-4 3-5h0z" class="g"></path><path d="M187 313c3-1 5-3 8-6 0 1 0 1 1 1h2v1l-2 1c-2 1-4 3-6 4l-1 1c-1-1-1-1-2-1v-1z" class="m"></path><path d="M256 267c0 1 0 1 1 2v1h-1c-1 0-1 0-1 1h-2 0c-1 0-1 1-2 1 0 0-1 1-1 2h-3c0 1 0 1 1 2h-2v1h1v1h-1l-1 1c-1 1-2 1-4 1 0 1-2 1-3 1 0 1 1 1 2 2h0v1c-3 0-3 0-4 2 0 0 0-1-1-1h0c1-1 1-1 2-1 0 0 1 0 1-1h-3s0-1-1-1l2-1c-2 0-2-1-3 0v1h0l-1-1h-1c-1 1-3 2-4 1 1 0 3-1 4-1 2-2 4-3 6-4 1 0 1 0 1-1h-3-1 0l2-2c2 0 8 1 9 0 0-2-1-2-1-3h0c1 0 2 1 2 1 1 0 1 0 2-1v-1c1 0 1-1 2-1h1v-2h2c1 0 1 1 2 0h1z" class="S"></path><path d="M237 281v-1c1-1 2-1 3-1l-1 1h1c-1 1-2 1-3 1z" class="b"></path><path d="M235 276h8 1v1c-2 0-4-1-5 0 1 1 2 1 3 1l-1 1h-1c-1 0-2 0-3 1v1h-1c-2 0-2-1-3 0v1h0l-1-1h-1c-1 1-3 2-4 1 1 0 3-1 4-1 2-2 4-3 6-4 1 0 1 0 1-1h-3z" class="n"></path><path d="M256 267c0 1 0 1 1 2v1h-1v-1h-1-1c-1 1-3 1-5 2v1c-1 1-2 1-3 1v1l-1 1h-2 0v1h-8-1 0l2-2c2 0 8 1 9 0 0-2-1-2-1-3h0c1 0 2 1 2 1 1 0 1 0 2-1v-1c1 0 1-1 2-1h1v-2h2c1 0 1 1 2 0h1z" class="l"></path><path d="M189 315l1-1c2-1 4-3 6-4 1 0 1 1 1 1h0c1-1 2-1 3 0l-6 4-6 6c-2 1-5 2-6 4l-2 3v1l-1 1c-1 0-1 0-2-1h-1v-1h-1c0-1 1-3 2-4l3-4h0l2-3c1-1 4-3 5-4v1c1 0 1 0 2 1z" class="m"></path><path d="M189 315l1-1c2-1 4-3 6-4 1 0 1 1 1 1-1 1-3 2-4 2-2 1-3 2-4 3h0v-1h0z" class="k"></path><path d="M180 320h0c0 1 0 2 1 3-1 2-2 4-4 6h-1v-1h-1c0-1 1-3 2-4l3-4z" class="Y"></path><path d="M180 320l2-3c1-1 4-3 5-4v1c1 0 1 0 2 1h0c-4 1-6 5-8 8-1-1-1-2-1-3z" class="S"></path><path d="M202 263v1h1v1h1v1c0 1 1 1 2 2s2 5 3 6c1 0 2 1 4 2l1 1c-1 1-1 2-1 2v1l1-1h2l1 1-2 1-1-1-2 1h-1c1-1 1-1 1-2v-1l-3 2h-1l3-3c-1 0-1 0-2-1 0 0-1-1-1-2h0-1c-1 0-2 2-2 2-1 2-3 4-4 6l-1-1h-1-1v-1h-2 0-2v-1-1h0-2 0l1-2c1-2 2-2 3-3 1 0 2-2 2-2l2-4 1-1v-1c0-1 0-1 1-2h0z" class="l"></path><path d="M203 274l1-5c1 1 1 2 2 3 0 1-1 1-1 2h-2z" class="n"></path><path d="M201 271v1c1-1 1-2 2-3 0 2-1 3 0 5h0 0 2l-1 2h1c-1 2-3 4-4 6l-1-1h-1-1v-1h-2 0 0 0c3-2 4-6 5-9z" class="k"></path><path d="M202 263v1h1v1c0 1 0 2-1 3 0 1 0 1-1 1v2c-1 3-2 7-5 9h0 0-2v-1-1h0-2 0l1-2c1-2 2-2 3-3 1 0 2-2 2-2l2-4 1-1v-1c0-1 0-1 1-2h0z" class="s"></path><path d="M202 263v1h1v1c0 1 0 2-1 3 0 1 0 1-1 1l1-6h0z" class="W"></path><path d="M198 271c0 2-1 4-3 6v1h-1 0-2 0l1-2c1-2 2-2 3-3 1 0 2-2 2-2z" class="k"></path><path d="M236 286c1-2 1-2 4-2-1 1-2 2-2 3h0c0 1 1 2 0 3l2 1h0 0 0c1-1 2-2 4-2h1 2c2 0 4 0 5-1h1v2c2 0 4 0 6 1l3 1 1 1 1-1h0l1 1h-4v1h2v1c-1 0-3 0-4 1-3 1-5 1-7 1h-3l-7-1h-1c-2-1-5-1-7-1h-4v-1l2-1h-3l-3-1v-1c1 0 2 0 3-2h2l1-1h5v-1l-1-1z" class="c"></path><path d="M244 289h1 0c0 1 1 1 1 1v1c-1 0-1 0-1 1-2 0-3 0-5-1h0c1-1 2-2 4-2z" class="F"></path><path d="M242 296v-1l1-1c-1 0-3 0-4-1h0 4c1 2 3 1 5 2 1 0 2 1 4 2h-3l-7-1z" class="Z"></path><path d="M243 293h4c1 0 2 1 3 1 2 0 3 1 5 1 1 1 3 0 4 1-3 1-5 1-7 1-2-1-3-2-4-2-2-1-4 0-5-2z" class="I"></path><path d="M229 293l1-1h1c-1-1-1-1-1-2h2c1 1 4 1 6 1h2 0v1h-7l1 1h2c0 1-1 1-2 1v1h-4v-1l2-1h-3z" class="b"></path><path d="M236 286c1-2 1-2 4-2-1 1-2 2-2 3h0c0 1 1 2 0 3l2 1h-2c-2 0-5 0-6-1h-2c0 1 0 1 1 2h-1l-1 1-3-1v-1c1 0 2 0 3-2h2l1-1h5v-1l-1-1z" class="Y"></path><path d="M231 289h5l-4 1h-2c0 1 0 1 1 2h-1l-1 1-3-1v-1c1 0 2 0 3-2h2z" class="a"></path><path d="M252 288h1v2c2 0 4 0 6 1l3 1 1 1 1-1h0l1 1h-4v1h2v1c-1 0-3 0-4 1-1-1-3 0-4-1-2 0-3-1-5-1-1 0-2-1-3-1l1-1h-3 0c0-1 0-1 1-1v-1s-1 0-1-1h0 2c2 0 4 0 5-1z" class="E"></path><path d="M245 292h4c1-1 2-1 2-1l1 2h-2c-1-1-1-1-2-1h-3z" class="T"></path><path d="M262 292l1 1c-3 0-5-1-7 0v1c-2 0-3 0-4-1l-1-2c3 1 8 2 11 1h0z" class="Q"></path><path d="M264 292l1 1h-4v1h2v1c-1 0-3 0-4 1-1-1-3 0-4-1-2 0-3-1-5-1-1 0-2-1-3-1l1-1c1 0 1 0 2 1h2c1 1 2 1 4 1v-1c2-1 4 0 7 0l1-1h0z" class="F"></path><path d="M245 279l1-1h1v-1h-1v-1h2c-1-1-1-1-1-2h3c0-1 1-2 1-2 0 1 0 2-1 3 1 0 2 1 3 1l-1 2h-2c1 1 0 1 1 1l2 1h3l1 1v-1h0 4 2c1 1 2 1 3 1v1h-2v1c0 1 1 1 1 2h-1l-1 1c1 0 1 1 2 2v1c-1 0-2-1-3-1h-2 0v1c1 0 1 0 1 1h0s-1 0-2 1c-2-1-4-1-6-1v-2h-1c-1 1-3 1-5 1h-2-1c-2 0-3 1-4 2h0 0 0l-2-1c1-1 0-2 0-3h0c0-1 1-2 2-3v-1h0c-1-1-2-1-2-2 1 0 3 0 3-1 2 0 3 0 4-1z" class="K"></path><path d="M240 284v-1h0c-1-1-2-1-2-2 1 0 3 0 3-1 2 0 3 0 4-1l-2 2c-1 0-2 1-2 1l1 1 2 1h0c-2 1-2 2-4 3 0 2 2 1 1 3h-3c1-1 0-2 0-3h0c0-1 1-2 2-3z" class="f"></path><path d="M245 279l1-1h1v-1h-1v-1h2c-1-1-1-1-1-2h3c0-1 1-2 1-2 0 1 0 2-1 3 1 0 2 1 3 1l-1 2h-2c1 1 0 1 1 1l-1 1h-4c-1 0-1 0-2 1 0 0-1 1-2 1v1l-1-1s1-1 2-1l2-2z" class="c"></path><path d="M250 275c1 0 2 1 3 1l-1 2h-2c1 1 0 1 1 1l-1 1h-4c1 0 2 0 2-1v-1c0-1 1 0 2-2h-1l1-1z" class="i"></path><path d="M253 280h3l1 1h-1c-1 1-3 1-4 1v1l-3 1s1 0 2 1l1 1v1h-2v1h2c-1 1-3 1-5 1h-2-1c-1 0-1-1-1-1h-2c1-1 1-1 2-1 1-1 2-2 3-2h1v-1l-1-1h0 0c2-2 3-3 5-2 1 0 1 0 2-1z" class="I"></path><path d="M244 289c-1 0-1-1-1-1h-2c1-1 1-1 2-1v1c1 0 4-1 5 0l-1 1h-2-1z" class="j"></path><path d="M257 281v-1h0 4 2c1 1 2 1 3 1v1h-2v1c0 1 1 1 1 2h-1l-1 1c1 0 1 1 2 2v1c-1 0-2-1-3-1h-2 0v1c1 0 1 0 1 1h0s-1 0-2 1c-2-1-4-1-6-1v-2h-1-2v-1h2v-1l-1-1c-1-1-2-1-2-1l3-1v-1c1 0 3 0 4-1h1z" class="M"></path><path d="M262 283h1v1h0-1v-1z" class="B"></path><path d="M258 285h1 2c1 1 1 1 1 2l-1 1c-1 0-1 0-2-1h-3v-1c1-1 1-1 2-1z" class="C"></path><path d="M253 288c2 0 3 0 4 1 1 0 1-1 2-1h2 1-2 0v1c1 0 1 0 1 1h0s-1 0-2 1c-2-1-4-1-6-1v-2z" class="P"></path><path d="M257 281v-1h0 4-3v1c1 1 2 0 3 1h0-2v3h-1l-1-1h-1-1l-1 1h0l1 2h-3 0v-1l-1-1c-1-1-2-1-2-1l3-1v-1c1 0 3 0 4-1h1z" class="O"></path><path d="M289 256h1l1 2 2-1 2 4h0l2 3h0v4h-1c0 1 0 1 1 2s2 1 2 2 0 1-1 2h-1v1l2 1c-2 0-3 1-5 1h-1l-1 1 1 1c-1 1-1 1-2 1v1h1l-2 2 1 2-8 3v1h-1-1-1-3 0l-1-1h-1 1-1c-2 0-2 1-3 2-1 0-2 1-2 2l-1 1h0 0c-2 0-3 0-4 1v1h-2v-1h-2v-1h4l-1-1h0l-1 1-1-1-3-1c1-1 2-1 2-1h0c0-1 0-1-1-1v-1h0 2c1 0 2 1 3 1v-1c-1-1-1-2-2-2l1-1h1c0-1-1-1-1-2v-1h2v-1c-1 0-2 0-3-1h-2-4 0v1l-1-1h-3l-2-1c-1 0 0 0-1-1h2l1-2c-1 0-2-1-3-1 1-1 1-2 1-3 1 0 1-1 2-1h0 2c0-1 0-1 1-1h1 0l1 1 1-2h-1l1-1c0 1 1 1 1 1 1 0 2-1 2-1 0-1-1-1 0-2h1v-1c0-1 2-1 2-3v-1h1l1 1c1-1 1-1 1-2l1-1v-1-1l2 2c1-1 0-1 1-1h2v-1h0l1 1 1 1h0v1l1 1h1v-1h3v1h2c0-1 1-1 1-2h2 1c0 1 1 2 2 2v1l1-1v-1-1l-1-2v-1z" class="T"></path><path d="M261 290h0c0-1 0-1-1-1v-1h0 2c1 0 2 1 3 1h1c-1 0-2 0-2 1h-2-1z" class="R"></path><path d="M267 271l1-1c1 0 2 1 3 1v1 1h-2l-2-2z" class="Q"></path><path d="M277 283l1 1-2 2h0v1 1h-1v-1h0c0-1-1-2-2-2v-1l1-1c0 1 1 1 2 1l1-1z" class="F"></path><path d="M279 273s-1 0-1 1c-2 0-3-1-4-3-1-1-2-1-2-2l1-1c1 2 1 3 3 3h0c1 0 3 1 3 2z" class="P"></path><path d="M276 275h1v1 1c0 1-1 1-2 2h-1v1h1v1c-1 0-1 1 0 2h-3l1-2h0l-2-1v-1c1 0 2 1 3 0v-1c0-1 1-1 1-1 1-1 1-1 1-2z" class="Q"></path><path d="M274 273l2 2c0 1 0 1-1 2 0 0-1 0-1 1v1c-1 1-2 0-3 0v1-1c-1-1 0-2 0-3h3v-1l-1-1 1-1z" class="J"></path><path d="M276 271c-1-2-2-4-4-5 2 0 4 0 6 1 0 2 1 4 3 5h0v1h-1-1c0-1-2-2-3-2z" class="M"></path><path d="M282 262l6 5 1 2-3-3c-1 0-1 0-2-1-1 0-1 0-2 1-1 0-1 1-2 0l-1-1h-1v1c-1-1-1-2-1-2v-2l1 1c1-1 1-1 2-1v1c1 0 1-1 2-1h0z" class="R"></path><path d="M273 281h0l-1 2c-1 1-1 2-2 2l-1 1h1l-1 1h1v1h-4v1h-1v-1c-1-1-1-2-2-2l1-1h1c2 0 2 0 3-1 0 0-1-1-1-2 2 0 2 1 4 1l2-2z" class="P"></path><path d="M265 289v-1c-1-1-1-2-2-2l1-1h1l-1 1h0l4 1h0l-2 1v1h-1z" class="O"></path><path d="M278 266v-1h1l1 1c1 1 1 0 2 0v1c0 1 1 1 2 2l-1 1 1 1c-1 0 0 0-1-1h-1-1c0 1 1 2 2 3h1v1h-1c-1 0-2 0-3-1h1v-1h0c-2-1-3-3-3-5h0v-1z" class="e"></path><path d="M267 290s1 0 2-1h3v-2h3v1c-2 0-2 1-3 2-1 0-2 1-2 2l-1 1h0 0c-2 0-3 0-4 1v1h-2v-1h-2v-1h4l-1-1v-1c1 0 2-1 3-1z" class="o"></path><path d="M264 292v-1c1 0 2-1 3-1 0 0 1 1 1 2-1 1-1 1-3 1l-1-1z" class="Z"></path><path d="M286 259h1c0 1 1 2 2 2v1l2 2h0c-1 1-1 2-1 2l-1 1h-1l-6-5-1-1h2c0-1 1-1 1-2h2z" class="K"></path><path d="M286 259h1c0 1 1 2 2 2-2 1-3 1-4 0h-1c-1 1-1 1-2 1l-1-1h2c0-1 1-1 1-2h2z" class="f"></path><path d="M269 257l2 2c1-1 0-1 1-1h2v-1h0l1 1 1 1h0v1c0 1 0 2-1 3h-2v-1l-1 1c0 1 0 2 1 4h-1c-1 0-1 0-2-1-1 2 0 2-1 3-1 0-1 0-2-1l-2 1v3h-3c0 1 1 1 0 2-1 0-2 0-3-1h0 0v-1l1-1v-1l-1-1h-1l1-1c0 1 1 1 1 1 1 0 2-1 2-1 0-1-1-1 0-2h1v-1c0-1 2-1 2-3v-1h1l1 1c1-1 1-1 1-2l1-1v-1-1z" class="Z"></path><path d="M259 273h1v-1c1-1 2-1 3-1l1-1s0-1 1-2h2l-2 1v3h-3c0 1 1 1 0 2-1 0-2 0-3-1z" class="F"></path><path d="M269 257l2 2c1-1 0-1 1-1h2v-1h0l1 1 1 1h-1v2l-2 1-1-1c-1 0-1 1-1 1l-1 1v1l-1 1-1-2-1 1 1 1c-2 1-2 0-3 1-1 0-1 1-2 1v-1-1c0-1 2-1 2-3v-1h1l1 1c1-1 1-1 1-2l1-1v-1-1z" class="S"></path><path d="M281 277c-1 0-3-1-3-2h0 1c2 1 3 1 6 1 1 2 4 2 5 3l1 1v1h1l-2 2 1 2-8 3v1h-1-1-1-3 0l-1-1h-1 1v-1-1h0l2-2-1-1-2-1v-1h1c1 0 1-1 1-2h2l2-2z" class="i"></path><path d="M281 277c-1 0-3-1-3-2h0 1c2 1 3 1 6 1 1 2 4 2 5 3 0 0 0 1-1 1l-8-3z" class="c"></path><path d="M291 281h1l-2 2 1 2-8 3c1-2 2-3 4-4 0-2-1-1-1-3 0 0 1 0 2 1h0l3-1z" class="P"></path><path d="M280 286v-1c0-1-2-1-2-2 1-1 3 0 5 1h1c-1 2-2 2-2 4h0l1 1h-1-1-1-3 0l-1-1c1-1 2-2 4-2z" class="f"></path><path d="M276 288c1-1 2-2 4-2 1 0 1 1 2 2-1 0-1 0-1 1h1-1-1-3 0l-1-1z" class="V"></path><path d="M276 288c1-1 2-2 4-2-1 1-1 2-2 3h-1l-1-1z" class="c"></path><path d="M259 269l1 1v1l-1 1v1h0 0c1 1 2 1 3 1 1-1 0-1 0-2h3l2-1 2 2h2v-1-1c1 1 2 1 3 2l-1 1 1 1v1h-3c0 1-1 2 0 3v1l2 1-2 2c-2 0-2-1-4-1 0 1 1 2 1 2-1 1-1 1-3 1 0-1-1-1-1-2v-1h2v-1c-1 0-2 0-3-1h-2-4 0v1l-1-1h-3l-2-1c-1 0 0 0-1-1h2l1-2c-1 0-2-1-3-1 1-1 1-2 1-3 1 0 1-1 2-1h0 2c0-1 0-1 1-1h1 0l1 1 1-2z" class="L"></path><path d="M264 279c1-1 2-1 3-1l2 2h-2c-2 0-2 0-3-1z" class="D"></path><path d="M253 276l1 1h2 0l1 1 1 1h-1c-1 0-2-1-3-1 0 1 0 1 1 2h1-3l-2-1c-1 0 0 0-1-1h2l1-2z" class="Q"></path><path d="M259 269l1 1v1l-1 1v1h0 0c1 1 2 1 3 1 1-1 0-1 0-2h3l2-1 2 2h-1 0c1 1 1 1 1 2h-2c0 1 1 1 1 1v1c-2 0-1-1-3-1h-3-2v1c0 1 0 0-1 1l-3-1h-2l-1-1c-1 0-2-1-3-1 1-1 1-2 1-3 1 0 1-1 2-1h0 2c0-1 0-1 1-1h1 0l1 1 1-2z" class="P"></path><path d="M259 273h0c1 1 2 1 3 1h-1v1l1 1c-1 0-2 0-3-1h-1l1-1c-1 0-1 0-1-1h1z" class="I"></path><path d="M256 274c1-1 2-1 3-1h0-1c0 1 0 1 1 1l-1 1h-1c0 1 2 2 3 2 0 1 0 0-1 1l-3-1h-2l1-1h1c-1-1-1-1-1-2h1z" class="F"></path><path d="M259 269l1 1v1l-1 1v1c-1 0-2 0-3 1h-1c0 1 0 1 1 2h-1l-1 1-1-1c-1 0-2-1-3-1 1-1 1-2 1-3 1 0 1-1 2-1h0 2c0-1 0-1 1-1h1 0l1 1 1-2z" class="g"></path><path d="M259 269l1 1v1l-1 1v1c-1 0-2 0-3 1l-1-2h0 1 1v-1-1l1 1 1-2z" class="K"></path><path d="M289 256h1l1 2 2-1 2 4h0l2 3h0v4h-1c0 1 0 1 1 2s2 1 2 2 0 1-1 2h-1v1l2 1c-2 0-3 1-5 1h-1l-1 1 1 1c-1 1-1 1-2 1l-1-1c-1-1-4-1-5-3-3 0-4 0-6-1l2-1h0 1c1 0 1 1 1 0h1v-1h-1c-1-1-2-2-2-3h1 1c1 1 0 1 1 1l-1-1 1-1c-1-1-2-1-2-2v-1c1-1 1-1 2-1 1 1 1 1 2 1l3 3-1-2h1l1-1s0-1 1-2h0l-2-2 1-1v-1-1l-1-2v-1z" class="F"></path><path d="M290 273c-1 0-1 0-2-1 0 0 0-1 1-1 2 1 3 2 4 3h1 2v1h-4 0l-2-2z" class="f"></path><path d="M288 274c-1 0-3 0-4-1l1-1c1 1 3 1 4 2 1 0 1 0 1-1l2 2h0 0v1 1c-1-1-3-2-4-3z" class="c"></path><path d="M282 266c1-1 1-1 2-1 1 1 1 1 2 1l3 3 1 1c-1 0-2 1-2 1-2-1-3-2-4-2-1-1-2-1-2-2v-1z" class="O"></path><path d="M288 274c1 1 3 2 4 3h1l-1 1 1 1c-1 1-1 1-2 1l-1-1c-1-1-4-1-5-3 0 0 0-1 1-1h1l1-1z" class="K"></path><path d="M289 256h1l1 2 2-1 2 4h0l2 3h0v4h-1c0 1 0 1 1 2s2 1 2 2 0 1-1 2h-1v1l2 1c-2 0-3 1-5 1h-1-1v-1-1h0 4v-1h-2-1c0-1 0-2-1-2v-1l3 1h0c0-1-1-1-1-1l-2-2v-1l-3-1 1-1s0-1 1-2h0l-2-2 1-1v-1-1l-1-2v-1z" class="o"></path><path d="M289 267l1-1s0-1 1-2c1 2 1 3 1 4l-3-1z" class="Z"></path><path d="M290 259c1 1 2 2 2 3s-1 2-1 2l-2-2 1-1v-1-1zm7 15c-1-1-1-1-1-2h1v-1l-3-3 1-1 1 1c0 1 0 1 1 2s2 1 2 2 0 1-1 2h-1z" class="Y"></path><path d="M295 267s-1-1-1-2c-1-1-1-2-1-3h0s1 1 2 1v-2l2 3h0v4h-1l-1-1z" class="F"></path><path d="M306 273h1c0 1 1 2 0 3l1-1h1l1 2v1c1 1 2 3 3 5v-2c0-1 1-2 2-3h0c2 1 1 2 1 3l1 1c1 1 0 1 1 2 0 1 1 2 1 3v2 1s-1 1-1 2h0c1 0 1-1 2-1 0 1 0 1 1 2v-1s0-1 1-1c-1-1-1-2-2-4 1 1 2 1 2 2h1 2 1c-1 1-1 1-1 3 0 1 1 1 2 2v1l-1 1-1 1h0v3c0 1-1 2-1 2 0 1 1 1 1 1 1 3-1 3 1 5h1 1c0 1 1 2 1 4h2 0l1 1h0c0 1 0 1-1 2h1v1 1l-1 1v1s1 1 0 2c0 0-1 0-1-1-1 0-1-1-2-1-1 1-1 1-1 2v5c-1-1-2-1-2-2v-1h-1-1v-1-1h-1v-1h-2v-1h-2 0v2l-1 1h1v1l1 1h1v2 1l-1 1c-1 0-2 0-2 1-1 0-1 1-2 1s-2 1-2 1l-1 2 1 1h-1c0 1 0 1 1 2h0 1l-2 2v1h-1v1h1c-2 1-3 1-4 2h-2l1 1c1 1 2 1 3 1h-3 0v1l1 2-5 4-2 2h0c-2 3-6 7-7 11l-3 5 1 1c-1 1-1 3-2 4h0c1 0 1-1 2-1h1v1c0 1 1 1 1 2v2l-1 1v4 1c-1 1-1 1-1 2-1 1-2 2-4 2l-1-1-2 1-1-1c-1 0-2 1-3 2h-2l1-1c-1 0 0 0-1-1 0 0-1 1-2 1 0-1-1-1-2-1s-1 0-2-1h1v-1-1c-2 1-3 0-5 0h-1v-1h-1c-2-1-5-1-8-1 1-1 1-2 2-3l1-2h0-1-1c-1 0-2 0-3 1h-2v-1c-2 0-2 1-3 2h-2v2l-1 1v1h-1c-1 2-3 2-5 3h-1-1l-1 1h0c-1 1-2 2-4 3 0-1 1-2 1-4v-1h0c-1 0-1-1-1-1l1-1v-1h-1-1l1-1h0l-1-1c-1 1-2 1-3 1l-1-2c-1 1-2 2-4 2 0 1-1 1-2 1h-1c-1 0-2 0-4 1-1 0-1-1-2-1l1-1c0-1 0-1 1-1v-1c0-1 0-1-1-1h-4 0c-2-1-3 0-4 0l-6-1h0 2c0-1 0-1-1-1s-2-1-3-1v-1h2l1-1c-4-1-6-2-8-5l-1-1-4-5c-1-1-2-2-2-4h-1l-1 1-1-3-2 2v-1h0v1 1h0v3c1 1 0 1 1 1v1l-1-1c-1-1-2-2-2-4h0c0-1 0-1-1-2 0-1 0-2-1-2v-3h-2v-1l1-2c0-1 0-2-1-3l1-3v-1l1-5h-1v1l-1-1v-2h0v-1c-2 0-2 2-3 3h-1l5-8h0l-1-1 2-3c1-2 4-3 6-4l6-6 6-4c1-1 2-2 3-2l3-2 1 1v1c1-1 2-1 3-1 1-1 2-3 4-3 2-1 3-1 5-2l12-3h0l-1-1h2c2 0 3-1 4-1h1 3c1 0 1-1 2-1l-1-1h1l7 1h3c2 0 4 0 7-1 1-1 3-1 4-1h2v-1c1-1 2-1 4-1h0 0l1-1c0-1 1-2 2-2 1-1 1-2 3-2h1-1 1l1 1h0 3 1 1 1v-1c3-1 5-2 8-3l-1-2 2-2h-1v-1c1 0 1 0 2-1l-1-1 1-1h1c2 0 3-1 5-1l-2-1v-1h1 2c1 1 0 1 2 0 0 1 0 1 1 1 1-1 2-1 3-2h0z" class="s"></path><path d="M231 313c2 0 3-1 4 0-1 1-2 1-3 1l-1-1h0z" class="a"></path><path d="M236 344l4-3v1c-1 2-2 3-3 4v-1l-1-1z" class="h"></path><path d="M277 289h3 1 0c-2 1-2 1-4 1v2c-1 0-1 1-1 1v-1l-1-1c0-1 1-1 2-2z" class="b"></path><path d="M317 292v1h2c-1 1 0 2 0 3h0l-1-1c-1 0-2 0-3 1h0l-1-1 3-3z" class="k"></path><path d="M318 292h0c1 0 1-1 2-1 0 1 0 1 1 2v1c0 1 0 1-1 1l-1 1c0-1-1-2 0-3h-2v-1h1z" class="a"></path><path d="M271 330c-2 1-6 2-7 1v-1l2-2h1c1 1 1 1 3 1l1 1z" class="n"></path><path d="M290 297v-1c-1-2-1-3-1-5 1-1 2-1 3-2-1 3-2 5-2 8z" class="h"></path><path d="M286 312h2l2 2h2c0 1 1 1 1 2l-2 1 1-1c-1-1-6-3-7-4h1z" class="b"></path><path d="M292 281l2-1v1h3l-1 1c-2 1-3 1-5 3l-1-2 2-2z" class="B"></path><path d="M297 286c1 1 1 2 1 3h0c-1 0-2 1-3 2v1l-1-2-1-1 4-3z" class="K"></path><path d="M231 313c2-1 3-2 5-2 2-1 3-1 5-1-1 1-4 3-6 3-1-1-2 0-4 0z" class="n"></path><path d="M273 305c-1 0-2 0-2-1-1 0-2 0-2-1 1-1 2-1 3-1s0-1 1-2h1l1 1h0-2 0l2 3h0-2v1z" class="a"></path><path d="M220 326c1 0 1 1 2 1s1-1 2-1l1-1v1c0 1-1 1-2 2-1-1-2-1-3-1h0c-1 0-2 1-3 1-1 2-3 3-5 4l7-8c0-1 0 0 1-1v1l-3 4 3-2z" class="n"></path><path d="M290 321h4v1h-2l1 1h1v1h-1c0 1-1 1-1 2h-1c-1 0-2 0-2-1-1 0-2-1-3-2h1 3 1l-1-2z" class="W"></path><path d="M209 346c1 0 1 0 1-1 4 2 7 3 11 2l5-1h-1c-2 1-4 1-6 2-2 0-6-1-9-2 1 1 2 2 3 2h2c2 2 4 1 5 2h0-6c-1-1-4-3-5-4z" class="h"></path><path d="M231 352c2 0 3-1 5-1 0 0 1 0 1 1l-10 4-1-1h0-1c-1 0-2 0-3-1l6-1c1 0 2 0 2-1h1z" class="S"></path><path d="M242 329c2 0 4-3 6-4s4-4 7-4c-1 2-3 3-5 5-3 3-8 5-13 7 2-2 3-3 5-4z" class="l"></path><path d="M310 278c1 1 2 3 3 5-1 2-1 4-2 6-1-2-2-5-3-6l-1-1h2v-2h0c0-1 0-1 1-2z" class="E"></path><path d="M231 313l1 1c-4 2-8 3-10 6h0l-2 1h-1l-2 1-1-1c1 0 3-1 3-2v-1c1 0 3-2 4-2 3 0 5-1 8-3z" class="l"></path><path d="M231 350c1-1 2-2 3-2 0 1 1 1 1 2h0c4 0 7-2 10-3-3 2-5 3-8 5h0c0-1-1-1-1-1-2 0-3 1-5 1h-4l-1-1c2 0 3-1 5-1h0z" class="a"></path><path d="M205 339c0-1 0-1 1-1-1-1-1-1-1-2h6c3 1 6 1 9 1h3l-8 1h0 8c-2 1-5 1-7 1-2-1-4 0-6-1l-1-1c-1 0-1 0-2 1h1-1v1h-2 0z" class="n"></path><path d="M220 350h3v-1l1 1c1-1 2-1 2-2h1l3-2 2 1v-1h2c0-1 1-1 2-1v-1l1 1v1l-3 2c-1 0-2 1-3 2l-1-1c-3 1-6 2-9 1h-1 0z" class="a"></path><path d="M236 344l1 1v1l-3 2c-1 0-2 1-3 2l-1-1c1-1 5-3 6-5z" class="m"></path><path d="M267 328c2-1 5 0 7-2h1c2-1 3 0 5 1v1h0c-2 0-3 0-4 1l-5 1-1-1c-2 0-2 0-3-1z" class="a"></path><path d="M227 356h-2c-2 1-3 1-5 1h2l1 1h1c0-1 2-1 3-1h0 1c1-1 0-1 1-1s1 0 1 1h2v-1c2 0 4-1 6-1h1 3c-1 0-2 1-2 1-1 0-2 0-2 1-4 1-9 1-12 1h-4c-1 0 0-1-1-1s-2 0-3-1h3c2 0 3 0 5-1l1 1z" class="h"></path><path d="M210 323h1l-1 1v1h0l1-2v1c0 1-1 1-1 2h0c0 2-3 3-5 4l-2-1v-1s0-1-1-1c1-1 1-2 2-3v2h-1c2 0 5-2 7-3z" class="k"></path><path d="M244 319h1c-2 2-4 3-6 4-5 3-10 6-15 8-1 0 0 0-1-1 1-1 4-1 6-2 3-2 5-5 8-6l7-3z" class="p"></path><path d="M220 323c3-2 5-3 8-4 2-1 3-2 5-3 3-2 6-5 10-4-1 0-3 1-5 2-1 1-3 2-4 3-2 0-5 3-7 4-2 2-5 3-7 5l-3 2 3-4v-1z" class="h"></path><path d="M280 327c2 0 5 2 6 3l1 1h2l2 1c0 1 1 1 1 1h1v1c-2 1-6 1-8 1h-3c0-1-2 0-3-1 3-1 6 1 9 0 1 0 2 0 2-1 0 0 0-1-1-1-2 1-3 0-5-1-3-1-5-1-8-2 1-1 2-1 4-1h0v-1z" class="n"></path><path d="M279 298l-1-1c1 0 1 0 1-1h-1v-1c2-1 4-3 7-3v3c-1 0-2 0-3 1s0 2 0 3c1 0 1 1 2 2s2 2 2 4l-3-3c-2 0-3 0-4-1 0 0 0-1-1-1h0l-1-1h2v-1z" class="R"></path><path d="M279 298c1 1 3 2 4 4-2 0-3 0-4-1 0 0 0-1-1-1h0l-1-1h2v-1z" class="f"></path><path d="M229 342c3-1 6-3 8-4-1 3-8 7-11 8l-5 1v-1s-1-1-2-1-1 0-2-1l1-1 11-1z" class="b"></path><path d="M297 311c1 0 1 0 2 1v2c1 0 2 0 2-1l2 1c-2 1-2 2-3 2h-2v1l-1 1c-1 0-2 1-3 2v1h-4c-1-1-2-1-3-2h0 0 4v-1-1l2-1c0-1-1-1-1-2 1 0 3 0 5-1v-1-1z" class="V"></path><path d="M301 313l2 1c-2 1-2 2-3 2h-2v1c-1 0-2 0-3 1h0c-1-1-1-1-1-2h2v-1l3-1c1 0 2 0 2-1z" class="Z"></path><path d="M297 311c1 0 1 0 2 1v2l-3 1c-1 0-2 0-3 1 0-1-1-1-1-2 1 0 3 0 5-1v-1-1z" class="l"></path><path d="M218 314h1l-1 1c0 1-1 1-2 2l-2 2c1 0 1 0 2-1h1 1 1v-1h0v1 1c0 1-2 2-3 2l1 1 2-1h1l-3 3-1 1c-1 1-2 2-4 3h-1l1-2h-1-1 0c0-1 1-1 1-2v-1l-1 2h0v-1l1-1h-1c2-2 4-5 6-6l2-3z" class="a"></path><path d="M211 326l2-3h0l1 1-2 4 3-3h1c-1 1-2 2-4 3h-1l1-2h-1z" class="k"></path><path d="M293 334h0c2 0 2 0 3-1l1-1c0 1 1 1 1 1v1c-2 1-4 2-6 2l-8 2 1-1h0c-1-1-1-1-2 0l-12 2c-3 0-6 1-8 1 4-2 9-3 14-4 1 0 1-1 2-2 1 1 3 0 3 1h3c2 0 6 0 8-1z" class="c"></path><path d="M279 334c1 1 3 0 3 1h3l-8 1c1 0 1-1 2-2z" class="a"></path><path d="M205 339h0 2v-1h1l3 1c1 1 3 1 4 1 5 0 10 0 14-1 2 0 3-1 4-1-1 1-3 2-5 3l1 1-11 1c-3 0-6-1-8-2 0 0-1 0-1-1h-4c-1 0-1 0-1-1v-1l1 1z" class="h"></path><path d="M242 329h-1c-2 1-5 2-8 3-2 1-7 3-8 3v-1l6-3c3-1 5-3 8-4 2 0 4-2 5-3 3-1 5-2 7-4 2 0 3-1 4-2 1 0 1-1 2-1v1h1l-3 3c-3 0-5 3-7 4s-4 4-6 4z" class="k"></path><path d="M293 289l1 1 1 2v-1c1-1 2-2 3-2l-1 3h1l1 2h-1c1 1 1 1 2 3-1 2-2 3-4 4h0l-1 1h1-2l-1-1c-1-1-2-3-3-4 0-3 1-5 2-8h1z" class="f"></path><path d="M294 290l1 2v1h-2v-2h-1c1 0 1 0 2-1z" class="o"></path><path d="M295 292v-1c1-1 2-2 3-2l-1 3h1l1 2h-1c1 1 1 1 2 3-1 2-2 3-4 4h0l-1 1-1-1h1c0-3 1-6 0-8v-1z" class="Z"></path><path d="M296 301c1-1 2-2 2-4 0-1-1-1-1-3h1c1 1 1 1 2 3-1 2-2 3-4 4h0z" class="b"></path><path d="M198 338h2 0c2-1-1 0 1-1h0 1c1 1 2 1 2 1v1c0 1 0 1 1 1h4c0 1 1 1 1 1 2 1 5 2 8 2l-1 1c1 1 1 1 2 1s2 1 2 1v1c-4 1-7 0-11-2l-1-1h-3 0-1-1l-2-1-2-3-1-1-1-1z" class="S"></path><defs><linearGradient id="d" x1="204.92" y1="337.67" x2="205.696" y2="343.784" xlink:href="#B"><stop offset="0" stop-color="#101111"></stop><stop offset="1" stop-color="#2c2a2b"></stop></linearGradient></defs><path fill="url(#d)" d="M198 338h2 0c2-1-1 0 1-1h0 1c1 1 2 1 2 1v1c0 1 0 1 1 1 2 1 5 2 6 4h-2-3 0-1-1l-2-1-2-3-1-1-1-1z"></path><path d="M198 338h2l2 3-2-1-1-1-1-1z" class="a"></path><path d="M200 340l2 1c1 1 3 1 4 3h-1-1l-2-1-2-3z" class="S"></path><path d="M306 273h1c0 1 1 2 0 3l1-1h1l1 2v1c-1 1-1 1-1 2h0v2h-2 0c-3-2-7-1-10-1h-3v-1l-2 1h-1v-1c1 0 1 0 2-1l-1-1 1-1h1c2 0 3-1 5-1l-2-1v-1h1 2c1 1 0 1 2 0 0 1 0 1 1 1 1-1 2-1 3-2h0z" class="B"></path><path d="M294 280h2 1v1h-3v-1h0z" class="X"></path><path d="M301 278h3v1 1h-2-3 0c0-1 1-1 2-2z" class="H"></path><path d="M307 276l1-1h1l1 2v1c-1 1-1 1-1 2h0c-1-1-3-3-5-3l1-1c1 0 1 1 2 0z" class="Q"></path><path d="M306 273h1c0 1 1 2 0 3s-1 0-2 0l-1 1h-2v-1c-1 0-2 1-2 1-2 1-4 2-6 2v1h0l-2 1h-1v-1c1 0 1 0 2-1l-1-1 1-1h1c2 0 3-1 5-1l-2-1v-1h1 2c1 1 0 1 2 0 0 1 0 1 1 1 1-1 2-1 3-2h0z" class="R"></path><path d="M306 273h1c0 1 1 2 0 3s-1 0-2 0h0l-1-1c-1 1-2 1-3 1-1-1-1-1-2 0l-2-1v-1h1 2c1 1 0 1 2 0 0 1 0 1 1 1 1-1 2-1 3-2h0z" class="F"></path><defs><linearGradient id="e" x1="302.805" y1="296.88" x2="285.827" y2="304.738" xlink:href="#B"><stop offset="0" stop-color="#c4c3c4"></stop><stop offset="1" stop-color="#f1f0f1"></stop></linearGradient></defs><path fill="url(#e)" d="M286 305c0-2-1-3-2-4s-1-2-2-2c0-1-1-2 0-3s2-1 3-1c2 4 4 6 8 9 2 1 4 2 7 1 2 0 5-1 6-3v-1h1v4c-3 2-6 3-9 4-1 0-1 0-2-1v1l-5-1c0-1-2-2-2-2l-3-1z"></path><path d="M297 286h1c0-1 1-1 2-2l1 1c0 1 1 2 1 3h1c1 0 1 0 1-1h0c1 0 2 0 2 1 1 1 0 3 0 4 1 3 0 5-1 6-1 2-3 3-4 3-2 2-3 1-5 1h-1l1-1h0c2-1 3-2 4-4-1-2-1-2-2-3h1l-1-2h-1l1-3h0c0-1 0-2-1-3z" class="Z"></path><path d="M303 295c0-1 1-2 1-3h2v2c-1 1-2 2-3 4h-1l1-3z" class="F"></path><path d="M302 295h1l-1 3c0 1-1 1-1 3h0c-2 2-3 1-5 1h-1l1-1h0c2-1 3-2 4-4h0l1 1c0-1 1-2 1-3z" class="q"></path><path d="M297 286h1c0-1 1-1 2-2l1 1c0 1 1 2 1 3 1 2 1 4 0 7 0 1-1 2-1 3l-1-1h0c-1-2-1-2-2-3h1l-1-2h-1l1-3h0c0-1 0-2-1-3z" class="E"></path><path d="M298 292l1-1h1v6h0c-1-2-1-2-2-3h1l-1-2z" class="p"></path><path d="M297 286h1c0-1 1-1 2-2l1 1h-1v6h-1l-1 1h-1l1-3h0c0-1 0-2-1-3z" class="W"></path><path d="M275 288h1-1 1l1 1h0c-1 1-2 1-2 2l1 1v1h-2l-3 2h0l-1 2h2 1 1 1c-2 1-2 1-4 1l-8 3c-1 0-4 1-5 1l-3 1c1-1 2-2 3-2h1v-1h-1-3l-2-1-9 1h-2c-1 0-3-1-5-1v-1h3c1 0 1-1 2-1l-1-1h1l7 1h3c2 0 4 0 7-1 1-1 3-1 4-1h2v-1c1-1 2-1 4-1h0 0l1-1c0-1 1-2 2-2 1-1 1-2 3-2z" class="a"></path><path d="M263 299c2 0 6-1 8-1h0l-8 3c-1-1-1-1-2-1v-1h2z" class="V"></path><path d="M258 300c1 0 1 0 2-1s2-1 4-1c1-1 1-1 2-1l-3 2h-2v1c1 0 1 0 2 1-1 0-4 1-5 1l-3 1c1-1 2-2 3-2h1v-1h-1z" class="Y"></path><path d="M275 288h1-1 1l1 1h0c-1 1-2 1-2 2l1 1v1h-2c-2 0-3 1-5 1v1l1 1c-1 0-2 1-4 1-1 0-1 0-2 1-2 0-3 0-4 1s-1 1-2 1h-3l-2-1c2 0 4 0 6-1 2 0 5-2 8-3v-1l2-1h0 0l1-1c0-1 1-2 2-2 1-1 1-2 3-2z" class="N"></path><path d="M275 288h1-1c-1 2-1 3-2 4-1-1-1-1-1-2 1-1 1-2 3-2z" class="Y"></path><path d="M269 293h0l1-1c0-1 1-2 2-2 0 1 0 1 1 2-2 1-3 1-4 1z" class="c"></path><path d="M263 295h2v-1c1-1 2-1 4-1l-2 1v1c-3 1-6 3-8 3-2 1-4 1-6 1l-9 1h-2c-1 0-3-1-5-1v-1h3c1 0 1-1 2-1l-1-1h1l7 1h3c2 0 4 0 7-1 1-1 3-1 4-1z" class="g"></path><path d="M242 296l7 1h3 2l-1 1c-4 1-10-1-14 1h1c1 1 2 1 2 1h0c-1 0-3-1-5-1v-1h3c1 0 1-1 2-1l-1-1h1z" class="Y"></path><path d="M242 296l7 1h1l-1 1h-9c1 0 1-1 2-1l-1-1h1z" class="N"></path><path d="M292 336l1 1h0 0l-3 1c-13 4-25 9-37 15h0l-5 3c0 1-1 2-2 3v1l2-1c1-1 1-1 3-1-2 2-5 3-7 4l-3 2c-2 1-5 2-7 3v-2s1-1 1-2l-1-1 4-2 4-2h-1v-1h-3c0-1 1-1 2-1 0 0 1-1 2-1l6-3 2 1c7-4 15-8 22-11 4-1 8-3 12-4l8-2z" class="I"></path><path d="M242 355l6-3 2 1-8 5h-1v-1h-3c0-1 1-1 2-1 0 0 1-1 2-1z" class="Y"></path><path d="M248 356c0 1-1 2-2 3v1l2-1c1-1 1-1 3-1-2 2-5 3-7 4l-3 2c-2 1-5 2-7 3v-2s1-1 1-2l13-7z" class="h"></path><path d="M274 300c2 0 2 0 4 1v-1h0c1 0 1 1 1 1 1 1 2 1 4 1l3 3 3 1s2 1 2 2l5 1v3h1v1c-2 1-4 1-5 1h-2l-2-2h-2-1-2c-1 0 0 0-1-1h0c-2 0-4 0-6-1v1c-1-1-2-1-3-2l-2-2c1-1 2 0 2-1v-1-1h2 0l-2-3h0 2 0l-1-1z" class="m"></path><path d="M277 306h0c1-1 2 0 2 0l1 2h0-1c-1 0-1-1-2-2z" class="N"></path><path d="M283 308h1c3 0 3 2 6 2 0 1 0 1 1 1h-3c-1-1-1-1-2-1s-2-1-3-2z" class="W"></path><path d="M275 304h1v1h-2l1 3s-1 0-2 1l-2-2c1-1 2 0 2-1v-1-1h2 0z" class="h"></path><path d="M291 308l5 1v3c-2-1-2 0-4 0v-1l1-2-2-1z" class="p"></path><path d="M283 308c1 1 2 2 3 2s1 0 2 1h-1l1 1h-2-1-2l-2-2h0 1l-1-1 1-1h1z" class="l"></path><path d="M282 310c2 0 2 0 4 1v1h-1-2l-2-2h0 1z" class="S"></path><path d="M288 311h3c0 1 0 1 1 1 2 0 2-1 4 0h1v1c-2 1-4 1-5 1h-2l-2-2-1-1h1z" class="a"></path><path d="M274 300c2 0 2 0 4 1v-1h0c1 0 1 1 1 1 1 1 2 1 4 1l3 3 3 1h-3c-1-1-2-1-3-1-1-1-3-1-4-1-2-1-2-3-4-3l-1-1z" class="N"></path><path d="M253 353h0c12-6 24-11 37-15h1l1 1c0 1 0 1-1 1-2 1-3 2-5 4h0c-1 0-2 1-3 1l-10 5c-3 0-5 1-8 2v1h0c-1 0-3 1-4 1-3 1-6 2-9 4h-1c-2 0-2 0-3 1l-2 1v-1c1-1 2-2 2-3l5-3z"></path><path d="M253 353l-3 3h1c-1 2-4 2-5 4v-1c1-1 2-2 2-3l5-3z" class="k"></path><path d="M306 301c0-1 1-2 2-3l1 1-1 1v4c1 1 1 1 2 1v-3c1 0 2 0 2-1v-1h0l1 1c0 1 1 2 1 3l2 1h0c1 1 2 1 2 2v1l-2 2h1c1 0 1 0 2-1v1 2h0v1c0 1-1 1-1 2h1v2c1 0 1 0 2-1v1c0 1-1 2-1 2h-2 0v2l-1 1-1-2v-1-1h-1l-2 1c-1 1-1 2-2 2h0l-1 1-1-1v-1s-1-1-2-1l-2 2h-2l-1-1c1-1 1-2 1-3l1-1 3-3v-1c-1 0-2 1-3 1l-1 1-2-1c0 1-1 1-2 1v-2c-1-1-1-1-2-1v1h-1v-3-1c1 1 1 1 2 1 3-1 6-2 9-4v-4h-1z" class="W"></path><path d="M297 311l2-2h3 0c0 1 1 1 1 1v1c0 1 0 1-1 1l-1 1h0c0 1-1 1-2 1v-2c-1-1-1-1-2-1z" class="S"></path><path d="M301 313h2c2-1 4-3 6-5 0 1 0 2 1 2v2l3 1 1-1v2h1v-1l1 1h-1v1l-1-1c0 1 0 1-1 1-2 0-3-2-4-3h-2c-1 0-2 1-3 1l-1 1-2-1h0z" class="i"></path><path d="M310 305v-3c1 0 2 0 2-1v-1h0l1 1c0 1 1 2 1 3l2 1h0c1 1 2 1 2 2v1l-2 2h1c1 0 1 0 2-1v1 2h0c-1 1-2 2-3 2v-1l-1-1v-2h0-1c0 1 0 1-1 2l-1-1v-2s-1-1-2-1v-3h0z" class="k"></path><path d="M307 312h2c1 1 2 3 4 3 1 0 1 0 1-1l1 1 1 1v-1l1 1v1h1 1c-1 1-1 1-1 2h0v2l-1 1-1-2v-1-1h-1l-2 1c-1 1-1 2-2 2h0l-1 1-1-1v-1s-1-1-2-1l-2 2h-2l-1-1c1-1 1-2 1-3l1-1 3-3v-1z" class="R"></path><path d="M309 321h1c0-1 1-2 1-3h0l1-1 1 1v1c-1 1-1 2-2 2h0l-1 1-1-1zm-2-8c1 0 2 1 2 2h1c0 1-1 2-1 2l-2 2-2 2h-2l-1-1c1-1 1-2 1-3l1-1 3-3z" class="E"></path><path d="M306 315h2v1h-1l-1 1v-1-1z" class="B"></path><path d="M307 313c1 0 2 1 2 2h1c0 1-1 2-1 2v-1s0-1-1-1h-2l-2 1 3-3z" class="P"></path><path d="M303 317h2v4h-2l-1-1c1-1 1-2 1-3z" class="B"></path><path d="M320 287c1 1 2 1 2 2h1 2 1c-1 1-1 1-1 3 0 1 1 1 2 2v1l-1 1-1 1h0v3c0 1-1 2-1 2 0 1 1 1 1 1 1 3-1 3 1 5h1 1c0 1 1 2 1 4h2 0l1 1h0c0 1 0 1-1 2h1v1 1l-1 1v1s1 1 0 2c0 0-1 0-1-1-1 0-1-1-2-1-1 1-1 1-1 2v5c-1-1-2-1-2-2v-1h-1-1v-1-1h-1v-1h-2v-1s1-1 1-2v-1c-1 1-1 1-2 1v-2h-1c0-1 1-1 1-2v-1h0v-2-1c-1 1-1 1-2 1h-1l2-2v-1c0-1-1-1-2-2h0l-2-1c0-1-1-2-1-3 1 0 2-1 2-1v-1c1-1 1-2 2-2l2-1h0l1-1c1 0 1 0 1-1v-1-1s0-1 1-1c-1-1-1-2-2-4z" class="F"></path><path d="M322 291l1 2h0v1c0 1-1 1-1 1-1 1 0 1-1 2l-1-1h0v-1c1 0 1 0 1-1v-1-1s0-1 1-1z" class="i"></path><path d="M320 287c1 1 2 1 2 2h1 2 1c-1 1-1 1-1 3 0 1 1 1 2 2v1l-1 1-1 1h0l-1 1h0v1 1c-1-1-1-2-1-3l1-1 1-1c-1 0-1-1-2-1v-1h0l-1-2c-1-1-1-2-2-4z" class="e"></path><path d="M320 287c1 1 2 1 2 2h1l1 2c0 1 0 1-1 2l-1-2c-1-1-1-2-2-4z" class="T"></path><path d="M317 304c0-2 2-5 3-6h1 1 1v1h-1c-1 0-1 1-2 1v1c0 1 0 3 1 4v3h1v1h0l-1 1h-1v-2h-1-1v-1c0-1-1-1-2-2l1-1z" class="o"></path><path d="M317 304l1 1 1-2h1c0 2-1 3-1 5h-1v-1c0-1-1-1-2-2l1-1z" class="g"></path><path d="M324 300v-1-1h0l1-1v3c0 1-1 2-1 2 0 1 1 1 1 1 1 3-1 3 1 5h1 1c0 1 1 2 1 4h2 0l1 1h0c0 1 0 1-1 2h1l-1 1h-1l-1-3-2-1v-2c-2-1-3-2-4-3 0-1-1-4-1-5s1-1 2-2z" class="E"></path><path d="M320 295v1h0l1 1-1 1c-1 1-3 4-3 6l-1 1h0l-2-1c0-1-1-2-1-3 1 0 2-1 2-1v-1c1-1 1-2 2-2l2-1h0l1-1z" class="N"></path><g class="a"><path d="M315 300v-1c1-1 1-2 2-2h2c-1 1-2 2-4 3z"></path><path d="M313 301c1 0 2-1 2-1h1 1c0 1 0 1-1 1-1 1-1 2-2 3 0-1-1-2-1-3z"></path></g><path d="M319 312h0v-2-1c-1 1-1 1-2 1h-1l2-2h1 1v2h1l1-1v1h2 0c1 1 0 1 0 2h0 2 0v1 1h1c1 1 1 2 1 3 1-1 1-1 2-1v4h1v-1s1 1 0 2c0 0-1 0-1-1-1 0-1-1-2-1-1 1-1 1-1 2v5c-1-1-2-1-2-2v-1h-1-1v-1-1h-1v-1h-2v-1s1-1 1-2v-1c-1 1-1 1-2 1v-2h-1c0-1 1-1 1-2v-1z" class="l"></path><path d="M324 312h2 0v1c0 1-1 1-1 1v1c-1 1-1 1-2 1v-1-1l1-1v-1zm-4 0l1-1c1 1 1 1 1 2s0 1-1 2v1h-1c-1-2-1-3 0-4z" class="k"></path><path d="M323 323c0-2 2-2 1-4h-1v-1c1-1 2-1 3-1l1 1c-1 2-2 3-3 5h-1z" class="a"></path><path d="M200 311c1-1 2-2 3-2h0c0 1 0 2 1 3h0 2 1c2 1 4-1 6-2 0 1-2 2-2 3h0c0 1-1 2-2 2v1l4-2c1-1 2-1 3-2h0l2-1h2 0 2 1l-3 2-1 1h-1l-2 3c-2 1-4 4-6 6-2 1-5 3-7 3h1v-2l2-2h0l1-2-1 1c-1 1-3 2-4 2s-1 0-2-1v1h-1l-3 3v3h-1c0-1-1-1-1-2h-1-1l-3 3c-1-1-2-1-3-1 1-1 1-2 1-3s1-2 1-3v-2l6-6 6-4z" class="m"></path><path d="M218 311h2 0 2 1l-3 2v-1c-1 0-2 1-3 1h0l-1-1 2-1z" class="h"></path><g class="b"><path d="M213 314l1 1c-2 1-6 5-9 5 1-2 2-2 4-3v-1l4-2z"></path><path d="M206 322s3-2 4-3l3-3c1-1 3-1 4-2h1l-2 3c-2 1-4 4-6 6-2 1-5 3-7 3h1v-2l2-2z"></path></g><path d="M200 311c1-1 2-2 3-2h0c0 1 0 2 1 3h0-1l1 1-4 2c0 1 0 1-1 1v1h0c-1 0-2 1-3 2h0l-2 1v1c2-1 4-3 5-4l1 1-2 2c-1 1-2 4-2 6v3h-1c0-1-1-1-1-2h-1-1l-3 3c-1-1-2-1-3-1 1-1 1-2 1-3s1-2 1-3v-2l6-6 6-4z" class="a"></path><path d="M200 311c1-1 2-2 3-2h0c0 1 0 2 1 3h0-1l-4 3h-2-1c0 1-1 1-1 2-3 1-5 4-7 6v-2l6-6 6-4z" class="W"></path><path d="M236 298h1v1c2 0 4 1 5 1h2l9-1 2 1h3 1v1h-1c-1 0-2 1-3 2l3-1c-1 1-2 1-3 1s-2 0-3 1h0-1-3c-4 0-7 1-10 2-2 0-3 0-5 1h-2l-1 1h0-2v1l-2 1-3 1h-1-2 0-2l-2 1h0c-1 1-2 1-3 2l-4 2v-1c1 0 2-1 2-2h0c0-1 2-2 2-3-2 1-4 3-6 2h-1-2 0c-1-1-1-2-1-3h0l3-2 1 1v1c1-1 2-1 3-1 1-1 2-3 4-3 2-1 3-1 5-2l12-3h0l-1-1h2c2 0 3-1 4-1z" class="a"></path><path d="M206 307l1 1v1h1 2c1 0 2 0 2-1l1 1-7 3h-2 0c-1-1-1-2-1-3h0l3-2z" class="p"></path><path d="M236 298h1v1c2 0 4 1 5 1h2 0c-1 1-2 1-2 1v1c2 0 4 0 6 1h4-2c-1 1-4 1-5 0-2 0-3 0-4-1-1 0-1-1-2-1h-1c-2-1-4-1-7-1l-1-1h2c2 0 3-1 4-1z" class="W"></path><path d="M244 300l9-1 2 1h3 1v1h-1c-1 0-2 1-3 2h-3-4c-2-1-4-1-6-1v-1s1 0 2-1h0z" class="c"></path><path d="M244 300l9-1 2 1c-4 0-8 1-11 1v1h2s1 0 2 1c-2-1-4-1-6-1v-1s1 0 2-1h0z" class="Y"></path><path d="M231 300c3 0 5 0 7 1-1 1-3 1-4 1l-9 2c-3 1-7 2-11 2v-1c2-1 3-1 5-2l12-3h0z" class="s"></path><path d="M216 312h-2 0c-1 1-1 1-2 1v1-1-1c1 0 2-1 2-2v-1s2-1 3-1l6-2c1-1 7-2 9-1-1 2-4 2-6 3l-1 1c-2 1-3 2-5 1-1 0-1 1-2 1l-2 1h0z" class="n"></path><path d="M194 340v-4c-1-1-1-2-1-3s1-1 1-2h1l1 4 2 3 1 1 1 1 2 3 2 1h1 1 0 3l1 1c0 1 0 1-1 1 1 1 4 3 5 4h6 1c3 1 6 0 9-1l1 1h0c-2 0-3 1-5 1l1 1h4-1c0 1-1 1-2 1l-6 1c1 1 2 1 3 1h1 0c-2 1-3 1-5 1h-3c1 1 2 1 3 1s0 1 1 1h4l-13 1c-5-1-8-1-13-4l-1-1c1 0 1-1 2-1h0v-2c-1 0-2-1-2-1v-1s-3-4-3-5c-2-1-2-2-2-4z" class="N"></path><path d="M206 344h3l1 1c0 1 0 1-1 1s-2-1-3-2z" class="p"></path><path d="M199 339l1 1 2 3 4 4h0c2 1 5 2 6 4 2 2 8 2 10 2s4-1 6 0l-6 1h-4c-3-1-5-1-7-1-2-1-4-1-6-2-1-1-2-1-3-2 0-1 0-1-1-1 2 0 3 1 4 1l1-1h-1l-3-3-1-1-1-1c-1-2-2-2-1-4z" class="f"></path><path d="M194 340v-4c-1-1-1-2-1-3s1-1 1-2h1l1 4 2 3 1 1c-1 2 0 2 1 4l1 1 1 1 3 3h1l-1 1c-1 0-2-1-4-1 1 0 1 0 1 1l-2-2c-1 0-1-1-1-1l-1 1-2-3h0c-2-1-2-2-2-4z" class="Y"></path><path d="M194 340v-4c-1-1-1-2-1-3s1-1 1-2h1l1 4c0 2 0 3 1 4h-1v-1h-1c0 2 2 5 4 7l1 2c-1 0-1-1-1-1l-1 1-2-3h0c-2-1-2-2-2-4z" class="S"></path><path d="M196 344h0l2 3 1-1s0 1 1 1l2 2c1 1 2 1 3 2 2 1 4 1 6 2 2 0 4 0 7 1h4c1 1 2 1 3 1h1 0c-2 1-3 1-5 1h-3c1 1 2 1 3 1s0 1 1 1h4l-13 1c-5-1-8-1-13-4l-1-1c1 0 1-1 2-1h0v-2c-1 0-2-1-2-1v-1s-3-4-3-5z" class="N"></path><path d="M199 349c1 1 3 3 4 3 2 0 5 1 6 3h-3v-1h-1c-1-1-2-1-2-1h-1 0c0 1-1 2-2 2l-1-1c1 0 1-1 2-1h0v-2c-1 0-2-1-2-1v-1z" class="m"></path><path d="M196 344h0l2 3 1-1s0 1 1 1l2 2c1 1 2 1 3 2v1h0c-1-1-1-1-2 0-1 0-3-2-4-3 0 0-3-4-3-5z" class="W"></path><path d="M211 353c2 0 4 0 7 1h4c1 1 2 1 3 1h1 0c-2 1-3 1-5 1l-6-1h-5v-1h1l-1-1h1z" class="c"></path><path d="M211 353c2 0 4 0 7 1h4c1 1 2 1 3 1-2 0-5 0-6-1-3-1-6 0-8 0l-1-1h1z" class="W"></path><path d="M200 355c1 0 2-1 2-2h0 1s1 0 2 1v1l1 1c1 0 2 1 3 1h1l3 1c2 0 5 1 7-1h1c1 0 0 1 1 1h4l-13 1c-5-1-8-1-13-4z" class="S"></path><path d="M303 314l1-1c1 0 2-1 3-1v1l-3 3-1 1c0 1 0 2-1 3l1 1h2l2-2c1 0 2 1 2 1v1l1 1 1-1h0c1 0 1-1 2-2l2-1h1v1 1l1 2h1v1l1 1h1v2 1l-1 1c-1 0-2 0-2 1-1 0-1 1-2 1s-2 1-2 1c-1 0-1 0-2-1h0-1l-2 1h-2c-1-1-3 0-4 1h-5c-1 0-3 1-4 1h-1s-1 0-1-1l-2-1h2v-1h-3l-1-1s1 0 1-1h0l1-3c0 1 1 1 2 1h1c0-1 1-1 1-2h1v-1h-1l-1-1h2v-1-1c1-1 2-2 3-2l1-1v-1h2c1 0 1-1 3-2z" class="F"></path><path d="M299 318l4-1c0 1 0 2-1 3-1-1-1 0-2 0l-1-2z" class="E"></path><path d="M291 331c2-1 3-1 4-2 0 1 1 0 1 1-1 1-2 2-3 2l-1 1s-1 0-1-1l-2-1h2z" class="W"></path><path d="M294 321v-1c1-1 2-2 3-2v1h0c0 1-1 3-1 3-1 0-1 1-1 1 0 1 0 1-1 2l2 1h-4c0-1 1-1 1-2h1v-1h-1l-1-1h2v-1z" class="K"></path><path d="M289 325c0 1 1 1 2 1h1 4v1h-1c-1 0-1 0-2 1h0 2v1c-1 1-2 1-4 2v-1h-3l-1-1s1 0 1-1h0l1-3z" class="S"></path><path d="M289 325c0 1 1 1 2 1l1 1h-2c-1 0-1 0-2 1l1-3z" class="m"></path><path d="M299 318l1 2c1 0 1-1 2 0l1 1h2l2-2c1 0 2 1 2 1-1 0-2 1-2 1 0 1 1 1 0 2h-2c-1 0-2 1-3 2v1h2c-1 0-1 0-2 1v1h-1l-1 1 1 1h-2s-1-1-1-2 1-1 2-2c-1-1-2 0-3-1 0-1 1-2 1-3l1-1v-1-2z" class="P"></path><path d="M313 319l2-1h1v1 1l1 2h1v1l1 1h1v2 1l-1 1c-1 0-2 0-2 1-1 0-1 1-2 1s-2 1-2 1c-1 0-1 0-2-1h0-1-9l-1-1 1-1h1v-1c1-1 1-1 2-1h-2v-1c1-1 2-2 3-2h2c1-1 0-1 0-2 0 0 1-1 2-1v1l1 1 1-1h0c1 0 1-1 2-2z" class="G"></path><path d="M316 323h1 1l1 1h1v2c0-1-2-1-3-2-1 0-1 0-1-1z" class="C"></path><path d="M302 328l4-1-3 1 1 1c1 0 4 0 5-1h1 2v1s0 1-1 1h0-1-9l-1-1 1-1h1z" class="B"></path><path d="M313 319l2-1h1v1 1l1 2h1v1h-1-1c-1 0-2-1-3-1 0 1-1 3-2 3 0 0-1 0-2-1 0 1-1 1-1 2-1 0-2 0-2 1l-4 1v-1c1-1 1-1 2-1h-2v-1c1-1 2-2 3-2h2c1-1 0-1 0-2 0 0 1-1 2-1v1l1 1 1-1h0c1 0 1-1 2-2z" class="D"></path><path d="M311 330h0c1 1 1 1 2 1l-1 2 1 1h-1c0 1 0 1 1 2h0 1l-2 2v1h-1v1h1c-2 1-3 1-4 2h-2l1 1c1 1 2 1 3 1h-3 0v1l1 2-5 4-2 2h0c-1 0-2 0-3 1h0v-1c1 0 1-1 1-1v-1c-1-1-1-1-2-1-2 1-4 3-5 4s-3 2-4 4h0l-1-1c1-1 4-3 5-6l-1-1c1 0 1-1 1-1v-1l1-2h1c-1-1-1-1-2-1l-1-1c-1 0-2 1-2 1l-1 2c-1 0-1 0-1-1-1-1-1-1-1-2h0c2-2 3-3 5-4 1 0 1 0 1-1l-1-1h-1l3-1h0 0l-1-1c2 0 4-1 6-2v-1s-1 0-1-1l-1 1c-1 1-1 1-3 1h0v-1c1 0 3-1 4-1h5c1-1 3-2 4-1h2l2-1h1z" class="O"></path><path d="M307 338c-1 0-1-1-2-1v-1h3v1l-1 1z" class="B"></path><path d="M308 336c1-1 1-1 3-1v2h-3v-1z" class="L"></path><path d="M299 341l5-2 1 1-1 1v1l-1-1h-2-2z" class="N"></path><path d="M297 342l2-1h2 2l1 1c-2 0-2 0-3 1v2l-1 1c-1-1-1-2-1-3-1-1 0-1-1-1h-1z" class="c"></path><path d="M311 337h0c1 0 1 1 1 1v1h-1v1h1c-2 1-3 1-4 2h-2v1l-2-2 1-1-1-1c1-1 2-1 3-1l1-1h3z" class="K"></path><path d="M311 337h0v1c-1 0-1 1-2 1s-1-1-1-2h3z" class="r"></path><path d="M305 340h1c0 1 1 1 1 2h1-2v1l-2-2 1-1z" class="j"></path><path d="M308 337c0 1 0 2 1 2l-3 1h-1l-1-1c1-1 2-1 3-1l1-1z" class="c"></path><path d="M310 330h1c0 1 0 2-1 3l-1 1h-1-3c-1 1-2 1-3 2h3v1h-4c0-1-1-1-2-1v-1h1l-2-1v-1s-1 0-1-1l-1 1c-1 1-1 1-3 1h0v-1c1 0 3-1 4-1h5c1-1 3-2 4-1h2l2-1z" class="V"></path><path d="M300 335c1 0 1 0 2-1h-1c0-1-1-1-1 0v-1h1 6 0l1 1h-3c-1 1-2 1-3 2h3v1h-4c0-1-1-1-2-1v-1h1z" class="I"></path><path d="M298 334l2 1h-1v1c1 0 2 0 2 1l-2 1c0 1 0 1-1 1 0 1-3 1-3 2l1 1-4 1-1 1c-1 0-2 1-2 1l-1 2c-1 0-1 0-1-1-1-1-1-1-1-2h0c2-2 3-3 5-4 1 0 1 0 1-1l-1-1h-1l3-1h0 0l-1-1c2 0 4-1 6-2z" class="p"></path><path d="M299 338c0 1 0 1-1 1 0 1-3 1-3 2l1 1-4 1c-1 0-1 0-1-1 2-1 4-2 7-3l1-1z" class="P"></path><path d="M286 344c2 0 3-1 5-2 0 1 0 1 1 1l-1 1c-1 0-2 1-2 1l-1 2c-1 0-1 0-1-1-1-1-1-1-1-2h0z" class="R"></path><path d="M298 334l2 1h-1v1c1 0 2 0 2 1l-2 1-1 1c0-1-1-1-1-2h-4 0 0l-1-1c2 0 4-1 6-2z" class="i"></path><path d="M304 342v-1l2 2v-1l1 1c1 1 2 1 3 1h-3 0v1l1 2-5 4-2 2h0c-1 0-2 0-3 1h0v-1c1 0 1-1 1-1v-1c-1-1-1-1-2-1-2 1-4 3-5 4s-3 2-4 4h0l-1-1c1-1 4-3 5-6l-1-1c1 0 1-1 1-1v-1l1-2h1c-1-1-1-1-2-1l-1-1 1-1 4-1h1 1c1 0 0 0 1 1 0 1 0 2 1 3l1-1v-2c1-1 1-1 3-1z" class="e"></path><path d="M304 342v-1l2 2-5 2v-2c1-1 1-1 3-1z" class="K"></path><path d="M292 349l1-1h1 1l1 1c-1 1-2 2-4 2h0l-1-1c1 0 1-1 1-1z" class="W"></path><path d="M296 342h1 1c1 0 0 0 1 1 0 1 0 2 1 3-1 0-1 0-2 1-1 0-1 1-1 1-1 1-1 0-1 1l-1-1h-1-1l-1 1v-1l1-2h1c-1-1-1-1-2-1l-1-1 1-1 4-1z" class="o"></path><path d="M307 344v1l1 2-5 4-2 2h0c-1 0-2 0-3 1h0v-1c1 0 1-1 1-1v-1c-1-1-1-1-2-1l4-2 2-2c1-1 3-2 4-2z" class="q"></path><path d="M307 344v1h0l-2 2h0l-1-1h-1c1-1 3-2 4-2z" class="K"></path><path d="M299 351c0-1 1-1 1-1 1 0 1 0 2-1v-1h1v1l-1 1 1 1-2 2h0c-1 0-2 0-3 1h0v-1c1 0 1-1 1-1v-1z" class="T"></path><path d="M188 321v2c0 1-1 2-1 3s0 2-1 3c1 0 2 0 3 1l3-3h1 1c0 1 1 1 1 2v2h-1c0 1-1 1-1 2s0 2 1 3v4c0 2 0 3 2 4 0 1 3 5 3 5v1s1 1 2 1v2h0c-1 0-1 1-2 1l1 1c5 3 8 3 13 4l13-1c3 0 8 0 12-1h3v1h1l-4 2-4 2h0c-2 2-5 4-7 4-2 2-6 2-9 3h-2c-1 0-2 0-3-1-2 0-5 0-7-1l-2-1-2 1h1c-1 1-1 1-1 2h-2l-1-1h-1l-1-1-4-5c-1-1-2-2-2-4h-1l-1 1-1-3-2 2v-1h0v1 1h0v3c1 1 0 1 1 1v1l-1-1c-1-1-2-2-2-4h0c0-1 0-1-1-2 0-1 0-2-1-2v-3h-2v-1l1-2c0-1 0-2-1-3l1-3v-1l1-5h-1v1l-1-1v-2h0v-1c-2 0-2 2-3 3h-1l5-8h0l-1-1 2-3c1-2 4-3 6-4z" class="g"></path><path d="M194 359c0-1-1-1-1-2l1-1v1-3h0c1 1 2 2 2 3h-1c0 1 0 1 1 1l-1 1h-1z" class="f"></path><path d="M184 337l1 1c1 1 0 3 0 5 0 1-1 1-1 2l-1-3-1-1 2-4z" class="K"></path><path d="M193 349l1 1h0c1 1 2 3 3 4l4 3c-1 0-1 1-2 1-2-3-5-5-6-9z" class="W"></path><path d="M199 368c-1-2-3-4-3-6 1 2 2 3 4 4h1 0l1 1h1c-1 1-1 1-1 2h-2l-1-1z" class="V"></path><path d="M182 341l1 1 1 3c-1 2-1 3-3 4 0-1 0-2-1-3l1-3 1-2z" class="Z"></path><path d="M191 357c2 1 3 3 5 5h0c0 2 2 4 3 6h-1l-1-1-4-5c-1-1-2-2-2-4v-1z" class="f"></path><path d="M180 335l1-2h3v1 1l1 1-1 1-2 4-1 2v-1l1-5h-1v1l-1-1v-2h0z" class="c"></path><path d="M180 335l1-2h3v1 1l1 1-1 1h-1v-2h0l-2 1v-1h-1 0z" class="f"></path><path d="M184 334c1 0 1 0 3-1v1h2c0 2 0 5 1 6 0-1 0-1 1-1 1 5 3 10 7 14l-1 1c-1-1-2-3-3-4h0l-1-1c-2-2-3-4-4-6v-1l-2-1v-1-2c-1-1-1-1-1-2v-1l-1 1-1-1v-1z" class="V"></path><path d="M191 336v-1h0c1 0 1 0 1 1s1 3 2 4c0 2 0 3 2 4 0 1 3 5 3 5v1s1 1 2 1v2h0c-1 0-1 1-2 1l-1-1c-4-4-6-9-7-14v-3z" class="n"></path><path d="M196 358c-1 0-1 0-1-1h1l2 2c3 3 8 6 13 7h1c2 1 3 1 5 1v1h-4c-2 0-5 0-7-1l-2-1-2 1-1-1v-1c-2-1-3-3-4-4l-3-2h1l1-1z" class="N"></path><path d="M196 358l1 2v1l-3-2h1l1-1z" class="V"></path><path d="M201 365c1 0 2 0 3 1l-2 1-1-1v-1z" class="K"></path><path d="M185 344c0-1 1-2 1-2 2 1 1 6 2 8 0 3 2 5 3 7v1h-1l-1 1-1-3-2 2v-1h0v1 1h0v3c1 1 0 1 1 1v1l-1-1c-1-1-2-2-2-4h0c0-1 0-1-1-2 0-1 0-2-1-2v-3c0-1 0-1 1-2 2-1 2-4 2-6z" class="W"></path><path d="M186 357v-5-1c1 1 1 4 2 5l-2 2v-1z" class="V"></path><path d="M182 352c0-1 0-1 1-2 2-1 2-4 2-6v5c0 1 0 2-1 3v4 1h-1c0-1 0-2-1-2v-3z" class="N"></path><path d="M188 321v2c0 1-1 2-1 3s0 2-1 3c1 0 2 0 3 1l3-3h1 1c0 1 1 1 1 2v2h-1c0 1-1 1-1 2s0 2 1 3v4c-1-1-2-3-2-4s0-1-1-1h0v1 3c-1 0-1 0-1 1-1-1-1-4-1-6h-2v-1c-2 1-2 1-3 1v-1h-3l-1 2v-1c-2 0-2 2-3 3h-1l5-8h0l-1-1 2-3c1-2 4-3 6-4z" class="S"></path><path d="M182 325l1 1v2h0l1-1v1l1 1c-1 1-2 1-2 2 1 1 1 2 2 2h1 1c-2 1-2 1-3 1v-1h-3l-1 2v-1c-2 0-2 2-3 3h-1l5-8h0l-1-1 2-3z" class="N"></path><path d="M187 326c0 1 0 2-1 3 1 0 2 0 3 1l3-3h1 1c0 1 1 1 1 2v2h-1c0 1-1 1-1 2s0 2 1 3v4c-1-1-2-3-2-4s0-1-1-1h0v1c-1 0 0 0-1-1h0v-2-3l-1 1c-1-1-3-1-4-2l2-3z" class="l"></path><path d="M198 353l1 1 1 1c5 3 8 3 13 4l13-1c3 0 8 0 12-1h3v1h1l-4 2-4 2h0c-2 2-5 4-7 4-2 2-6 2-9 3h-2c-1 0-2 0-3-1h4v-1c-2 0-3 0-5-1h-1c-5-1-10-4-13-7l1-1h0c1 0 1-1 2-1l-4-3 1-1z" class="N"></path><path d="M216 361c2-1 3-1 4 0l-1 1c-1 0-2 0-3-1z" class="f"></path><path d="M234 361h0c0-1 2-2 3-2v1h1l-4 2h0v-1z" class="Y"></path><path d="M234 361v1c-2 2-5 4-7 4h0c-1-1-2-1-3-1 1-1 1-1 2-1l-1-1h-3c2-1 4-1 5-1s2-1 4-1v1l3-1z" class="g"></path><path d="M198 353l1 1 1 1c5 3 8 3 13 4h7 2v1l-2 1c-1-1-2-1-4 0-2 0-3 0-5-1l-3-1c-2-1-5-1-7-2l-4-3 1-1z" class="q"></path><path d="M238 357h3v1h-3c-4 0-9 3-13 3-1 0-1 0-2 1h-2v-1h1v-1h0v-1h-2-7l13-1c3 0 8 0 12-1z" class="f"></path><path d="M222 363h3l1 1c-1 0-1 0-2 1 1 0 2 0 3 1h0c-2 2-6 2-9 3h-2c-1 0-2 0-3-1h4v-1c-2 0-3 0-5-1h-1c2 0 4 1 5 0v-1c1-1 2-1 3-1l3-1z" class="c"></path><path d="M222 363h3l1 1c-1 0-1 0-2 1-3 1-5 1-8 1v-1c1-1 2-1 3-1l3-1z" class="Z"></path><path d="M201 357c2 1 5 1 7 2 1 2 5 4 7 4h3l1 1c-1 0-2 0-3 1v1c-1 1-3 0-5 0-5-1-10-4-13-7l1-1h0c1 0 1-1 2-1z" class="V"></path><path d="M199 358c2 2 4 3 6 4 4 1 6 2 10 2 1-1 2-1 3-1l1 1c-1 0-2 0-3 1v1c-1 1-3 0-5 0-5-1-10-4-13-7l1-1z" class="o"></path><path d="M283 345c1 0 2-1 3-1 0 1 0 1 1 2 0 1 0 1 1 1l1-2s1-1 2-1l1 1c1 0 1 0 2 1h-1l-1 2v1s0 1-1 1l1 1c-1 3-4 5-5 6l1 1h0c1-2 3-3 4-4s3-3 5-4c1 0 1 0 2 1v1s0 1-1 1v1h0c1-1 2-1 3-1-2 3-6 7-7 11l-3 5 1 1c-1 1-1 3-2 4h0c1 0 1-1 2-1h1v1c0 1 1 1 1 2v2l-1 1v4 1c-1 1-1 1-1 2-1 1-2 2-4 2l-1-1-2 1-1-1c-1 0-2 1-3 2h-2l1-1c-1 0 0 0-1-1 0 0-1 1-2 1 0-1-1-1-2-1s-1 0-2-1h1v-1-1c-2 1-3 0-5 0h-1v-1h-1c-2-1-5-1-8-1 1-1 1-2 2-3l1-2h0-1-1c-1 0-2 0-3 1h-2v-1c-2 0-2 1-3 2h-2v2l-1 1v1h-1c-1 2-3 2-5 3h-1-1l-1 1h0c-1 1-2 2-4 3 0-1 1-2 1-4v-1h0c-1 0-1-1-1-1l1-1v-1h-1-1l1-1h0l-1-1c-1 1-2 1-3 1l-1-2c-1 1-2 2-4 2 0 1-1 1-2 1h-1c-1 0-2 0-4 1-1 0-1-1-2-1l1-1c0-1 0-1 1-1v-1c0-1 0-1-1-1h-4 0c-2-1-3 0-4 0l-6-1h0 2c0-1 0-1-1-1s-2-1-3-1v-1h2l1-1c-4-1-6-2-8-5h1l1 1h2c0-1 0-1 1-2h-1l2-1 2 1c2 1 5 1 7 1 1 1 2 1 3 1h2c3-1 7-1 9-3 2 0 5-2 7-4h0l1 1c0 1-1 2-1 2v2c2-1 5-2 7-3l3-2c2-1 5-2 7-4h1c3-2 6-3 9-4 1 0 3-1 4-1h0v-1c3-1 5-2 8-2l10-5z" class="K"></path><path d="M252 364c1-1 3-2 4-3l6-3c0 1-1 2-3 3h0c-2 0-2 1-4 2l-2 1h-1z" class="b"></path><path d="M265 353h0v-1c3-1 5-2 8-2l-8 5c-1 0-2 1-2 1h0l3-3h-1z" class="n"></path><path d="M269 353h1c4-2 8-4 12-5h0c-4 2-7 5-10 8-1-2-1-2-3-3z" class="a"></path><path d="M265 353h1l-3 3h0c-3 3-7 4-10 6l-2-1-3 2h-2c0 1-1 1-1 1-1 1-1 0-2 0h-2l3-2c2-1 5-2 7-4h1c3-2 6-3 9-4 1 0 3-1 4-1z" class="N"></path><path d="M251 358h1c3-2 6-3 9-4-1 2-2 2-4 4h-2c-1 1-3 2-4 3-1 0-2 0-3 1h-4c2-1 5-2 7-4z" class="b"></path><path d="M241 364h2c1 0 1 1 2 0 0 0 1 0 1-1h2l3-2 2 1-3 2h-2l-1 1h-1c-3 0-4 2-7 3 0 1-1 0-2 1l-5 3-2 1-2 1h0c-2-1-3 0-5-1l3-1v-1h1c1 0 2-1 3-2h-3l4-3c1 0 2-1 3-1v2c2-1 5-2 7-3z" class="c"></path><path d="M223 373l3-1v1h2l1-1v1c1 0 1 0 1-1h2l-2 1-2 1h0c-2-1-3 0-5-1z" class="i"></path><path d="M252 364h1l2-1c2-1 2-2 4-2-1 2-3 3-4 4 0 2 0 2 1 3-2 1-4 3-6 5l-3 1-2-2c-1 1-2 1-3 1h-1l-5 2v-2c1 0 2-2 4-3l1-1c1-1 3-1 5-1 2-2 4-3 6-4z" class="V"></path><path d="M240 370l1-1c1-1 3-1 5-1h-1c-1 2-3 2-5 3v1c1 0 2 0 2-1l3-1c1-1 3-2 4-2l1 1-5 3c-1 1-2 1-3 1h-1l-5 2v-2c1 0 2-2 4-3z" class="o"></path><defs><linearGradient id="f" x1="254.287" y1="369.953" x2="246.359" y2="369.162" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#f)" d="M255 365c0 2 0 2 1 3-2 1-4 3-6 5l-3 1-2-2 5-3c1 0 4-3 5-4z"></path><path d="M237 369l3 1c-2 1-3 3-4 3v2l5-2h1l-2 1-5 3-3 1 1 1h-2c-1 1-2 2-4 2 0 1-1 1-2 1h-1c-1 0-2 0-4 1-1 0-1-1-2-1l1-1c0-1 0-1 1-1v-1c0-1 0-1-1-1h-4 0c0-2 1-1 1-3l6-3c1-1 2-1 4-1h0v1l-3 1c2 1 3 0 5 1h0l2-1 2-1 5-3z" class="Q"></path><path d="M228 375h2c-1 1-2 1-2 1h-1-2c1-1 2-1 3-1z" class="I"></path><path d="M230 373l2 1c-1 1-1 1-2 1h-2v-1l2-1z" class="F"></path><path d="M221 374c1 1 3 1 4 2h-1s-1 0-1 1c-1 0-2-1-3-1v-1l1-1z" class="T"></path><path d="M221 374s1-1 2-1c2 1 3 0 5 1h0v1c-1 0-2 0-3 1h0c-1-1-3-1-4-2z" class="Z"></path><path d="M219 381h3c0-1 1-1 1-1 2 1 3 1 4 1 0 1-1 1-2 1h-1c-1 0-2 0-4 1-1 0-1-1-2-1l1-1z" class="I"></path><path d="M237 369l3 1c-2 1-3 3-4 3v2c-1 0-2 1-3 1-1-1 0-1-1-2l-2-1 2-1 5-3z" class="Z"></path><path d="M245 372l2 2-2 1c2 1 2 1 4 1v1 1h2l1 1h-2v2l-1 1v1h-1c-1 2-3 2-5 3h-1-1l-1 1h0c-1 1-2 2-4 3 0-1 1-2 1-4v-1h0c-1 0-1-1-1-1l1-1v-1h-1-1l1-1h0l-1-1c-1 1-2 1-3 1l-1-2h2l-1-1 3-1 5-3 2-1c1 0 2 0 3-1z" class="F"></path><path d="M249 382l-1-1h-2c1-1 2-2 3-2l1 1v1l-1 1z" class="Z"></path><path d="M233 379c1-1 4-2 6-1h0v2c-1 0-2 0-3 1h0 0l-1-1c-1 1-2 1-3 1l-1-2h2z" class="P"></path><path d="M245 372l2 2-2 1c2 1 2 1 4 1v1 1c-1 1-2 1-4 2h-1v-1l1-1h-3v-1l-2-1c-2 0-3 2-5 1l5-3 2-1c1 0 2 0 3-1z" class="K"></path><path d="M245 372l2 2-2 1c-1 1-3 0-5-1l2-1c1 0 2 0 3-1z" class="V"></path><path d="M236 381h1 0c2-1 4-2 5-2 0 1 0 1-1 2h1 2 1s0 1 1 1h0c1 0 2 0 2 1-1 2-3 2-5 3h-1-1l-1 1h0c-1 1-2 2-4 3 0-1 1-2 1-4v-1h0c-1 0-1-1-1-1l1-1v-1h-1-1l1-1h0z" class="I"></path><path d="M236 381h1 0c2-1 4-2 5-2 0 1 0 1-1 2-1 0-2 0-3 1v1h0 1c1 1 0 1 1 2l1 1-1 1h0c-1 1-2 2-4 3 0-1 1-2 1-4v-1h0c-1 0-1-1-1-1l1-1v-1h-1-1l1-1h0z" class="R"></path><path d="M237 385h0c1 0 1-1 1-2l1 1c0 1 0 1 1 3h0c-1 1-2 2-4 3 0-1 1-2 1-4v-1h0z" class="J"></path><path d="M234 362h0l1 1c0 1-1 2-1 2-1 0-2 1-3 1l-4 3h3c-1 1-2 2-3 2h-1 0c-2 0-3 0-4 1l-6 3c0 2-1 1-1 3-2-1-3 0-4 0l-6-1h0 2c0-1 0-1-1-1s-2-1-3-1v-1h2l1-1c-4-1-6-2-8-5h1l1 1h2c0-1 0-1 1-2h-1l2-1 2 1c2 1 5 1 7 1 1 1 2 1 3 1h2c3-1 7-1 9-3 2 0 5-2 7-4z" class="Z"></path><path d="M213 374h2c1 0 1 0 1 1h0c0 2-1 1-1 3-2-1-3 0-4 0v-1h0c1-1 1-1 2-3z" class="i"></path><path d="M216 375h0c0 2-1 1-1 3-2-1-3 0-4 0v-1h1s0-1 1-1l3-1z" class="T"></path><path d="M206 373l7 1c-1 2-1 2-2 3h0v1l-6-1h0 2c0-1 0-1-1-1s-2-1-3-1v-1h2l1-1z" class="c"></path><path d="M204 366l2 1c2 1 5 1 7 1 1 1 2 1 3 1h2 0c-1 1-2 2-4 3-1 0-4-1-6 0h-3l-1-1c-2 0-2-1-4-2h2c0-1 0-1 1-2h-1l2-1z" class="i"></path><path d="M208 369l5 2h-5l-2-1 2-1z" class="Y"></path><path d="M203 367l5 2-2 1c-1 0-3 0-4-1 0-1 0-1 1-2z" class="N"></path><path d="M202 369c1 1 3 1 4 1l2 1-3 1-1-1c-2 0-2-1-4-2h2z" class="g"></path><path d="M283 345c1 0 2-1 3-1 0 1 0 1 1 2 0 1 0 1 1 1l1-2s1-1 2-1l1 1c1 0 1 0 2 1h-1l-1 2v1s0 1-1 1l1 1c-1 3-4 5-5 6l1 1-4 4-3 3-1 1c-1 1-2 2-3 2h0 0l-2 1c-1 1-3 1-4 2s0 1-1 1c2-2 3-5 5-7l-3 1-4 6-3 2-2 2v1h2c0 1-1 1-2 1 0 1-1 1-2 1l1-2h0-1-1c-1 0-2 0-3 1h-2v-1c-2 0-2 1-3 2l-1-1h-2v-1-1c-2 0-2 0-4-1l2-1 3-1c2-2 4-4 6-5-1-1-1-1-1-3 1-1 3-2 4-4h0c2-1 3-2 3-3l7-5c2 1 2 1 3 3 3-3 6-6 10-8h1v-1h-1l1-2z" class="Z"></path><path d="M260 375c1-1 1-2 2-2h2c-1 1-2 3-3 3l-1-1z" class="V"></path><path d="M279 359l1 1c-2 2-3 4-5 5l-3 1 7-7z" class="l"></path><path d="M259 370l1-1 2 2h1 0c0-1 0-1 1-2 0 0 1 1 2 1h1l-3 3h-2c-1 0-1 1-2 2l-1 1h-1c0-1 1-1 1-2 0 0 1-1 1-2 0 0-1-1-1-2zm29-23l1-2s1-1 2-1l1 1c1 0 1 0 2 1h-1l-1 2-4 2-3 2s-1 1-2 1v-1c0-1 0-1-1-2 1-1 2-1 3-1 1-1 2-1 3-2h0z" class="S"></path><path d="M285 350h3l-3 2v-2z" class="r"></path><path d="M285 349h0v1 2s-1 1-2 1v-1c0-1 0-1-1-2 1-1 2-1 3-1z" class="h"></path><path d="M288 347l1-2s1-1 2-1l1 1c1 0 1 0 2 1h-1 0c-2 1-2 2-5 1h0 0z" class="f"></path><path d="M260 369l9-9c0 2-1 3-1 4v1h1l1-1 1 1-1 1-2 2-1 2h-1c-1 0-2-1-2-1-1 1-1 1-1 2h0-1l-2-2z" class="b"></path><path d="M268 368c-1 0-1 0-2-1l2-1c1-1 1 0 2 0l-2 2z" class="Y"></path><path d="M283 345c1 0 2-1 3-1 0 1 0 1 1 2 0 1 0 1 1 1h0c-1 1-2 1-3 2-1 0-2 0-3 1-3 2-6 4-8 6-2 1-4 3-5 4h0l-9 9-1 1-2-1 3-3 10-9 2-1c3-3 6-6 10-8h1v-1h-1l1-2z" class="j"></path><path d="M258 366h2l-3 3 2 1c0 1 1 2 1 2 0 1-1 2-1 2h-2 0-1c-1 1-2 1-3 2h-1c-1 1-2 1-3 1v-1c-2 0-2 0-4-1l2-1 3-1c2-2 4-4 6-5l2-2z" class="g"></path><path d="M249 376c1-1 2-2 3-2h0l-1 2h1c-1 1-2 1-3 1v-1z" class="q"></path><path d="M258 366h2l-3 3c-1 2-2 3-4 4h0-3c2-2 4-4 6-5l2-2z" class="S"></path><path d="M269 360c1-1 3-3 5-4 2-2 5-4 8-6 1 1 1 1 1 2v1l-10 10c-1 1-2 1-2 2l-1-1-1 1h-1v-1c0-1 1-2 1-4h0z" class="p"></path><path d="M283 352v1l-10 10c-1 1-2 1-2 2l-1-1-1 1h-1v-1c0-1 1-2 1-4h0c1 0 2 1 2 1 1 0 1-1 2-1 0-1 1 0 2-1s2-1 2-2c1-1 2-3 4-3 1 0 1-1 2-2z" class="l"></path><path d="M270 364c1-1 1-2 2-3h1v2c-1 1-2 1-2 2l-1-1z" class="V"></path><path d="M262 358l7-5c2 1 2 1 3 3l-2 1-10 9h-2l-2 2c-1-1-1-1-1-3 1-1 3-2 4-4h0c2-1 3-2 3-3z" class="k"></path><path d="M258 366c1-1 2-1 2-2 1 0 0 0 1-1l3-3c2-1 3-2 5-3h1l-10 9h-2z" class="m"></path><defs><linearGradient id="g" x1="268.477" y1="362.628" x2="292.324" y2="359.68" xlink:href="#B"><stop offset="0" stop-color="#0c090a"></stop><stop offset="1" stop-color="#2c2e2f"></stop></linearGradient></defs><path fill="url(#g)" d="M291 350l1 1c-1 3-4 5-5 6l1 1-4 4-3 3-1 1c-1 1-2 2-3 2h0 0l-2 1c-1 1-3 1-4 2s0 1-1 1c2-2 3-5 5-7 2-1 3-3 5-5l-1-1c2-2 4-4 6-5 2-2 4-3 6-4z"></path><path d="M277 368c3-4 6-8 10-11l1 1-4 4-3 3-1 1c-1 1-2 2-3 2h0z" class="q"></path><path d="M297 350c1 0 1 0 2 1v1s0 1-1 1v1h0c1-1 2-1 3-1-2 3-6 7-7 11l-3 5 1 1c-1 1-1 3-2 4h0c1 0 1-1 2-1h1v1c0 1 1 1 1 2v2l-1 1v4 1c-1 1-1 1-1 2-1 1-2 2-4 2l-1-1-2 1-1-1c-1 0-2 1-3 2h-2l1-1c-1 0 0 0-1-1 0 0-1 1-2 1 0-1-1-1-2-1s-1 0-2-1h1v-1-1c-2 1-3 0-5 0h-1v-1h-1c-2-1-5-1-8-1 1-1 1-2 2-3 1 0 2 0 2-1 1 0 2 0 2-1h-2v-1l2-2 3-2 4-6 3-1c-2 2-3 5-5 7 1 0 0 0 1-1s3-1 4-2l2-1h0 0c1 0 2-1 3-2l1-1 3-3 4-4h0c1-2 3-3 4-4s3-3 5-4z" class="g"></path><path d="M271 378h2v2h-4v-1l2-1h0z" class="S"></path><path d="M278 377c1-1 2 0 3-1v1 1h-3c-1 0-2 1-3 1 0-2 1-2 2-3l1 1z" class="b"></path><path d="M277 375l1 2-1-1c-1 1-2 1-2 3l-2 1v-2h-2l-1-1h0c1 0 2 0 2-1h1l4-1z" class="Y"></path><path d="M273 378l4-2c-1 1-2 1-2 3l-2 1v-2z" class="N"></path><path d="M284 371h4s-1 1-2 1l2 2h0l-1 1c-1 0-4 2-6 3v-1-1c2 0 3-1 4-2v-1h-1v-2z" class="h"></path><path d="M282 372c1-1 1-1 2-1v2h1v1c-1 1-2 2-4 2-1 1-2 0-3 1l-1-2 3-1 2-2z" class="S"></path><path d="M282 372c1-1 1-1 2-1v2h1c-2 0-2 1-3 1h-2l2-2z" class="b"></path><path d="M282 379h1v-1h1c1 0 2-1 3-2-1 1-1 2-2 4-1 1-2 1-4 2-1 1-2 1-3 1v-1l1-2c1 0 3-1 3-1z" class="n"></path><path d="M282 379c0 1-1 2-1 2v1c-1 1-2 1-3 1v-1l1-2c1 0 3-1 3-1z" class="a"></path><path d="M268 383l1-1c2 0 4 0 7-1 1 0 2-1 3-1l-1 2v1l-4 1c-2 1-3 0-5 0h-1v-1z" class="b"></path><path d="M292 373h1v1c0 1 1 1 1 2v2l-1 1h0l-5 3v-1c0-1-1-1-1-1v-1c1-2 2-4 3-5h0c1 0 1-1 2-1z" class="s"></path><path d="M287 380c2-1 4-2 7-2l-1 1h0l-5 3v-1c0-1-1-1-1-1z" class="Y"></path><path d="M292 373h1v1c-2 2-4 4-6 5 1-2 2-4 3-5h0c1 0 1-1 2-1z" class="S"></path><path d="M270 373l3-2h1v1c0 1-1 1-3 2h0c1 0 3 0 3 1h0c-1 0-1 1-1 1h-1c0 1-1 1-2 1h0l1 1h0l-2 1v1c-2 1-3 1-5 1v-1h2c0-1 1-1 2-1v-1h1l-1-1h-5v-1l2-2 3-2 2 1z" class="N"></path><path d="M268 372l2 1-4 2-1-1 3-2z" class="m"></path><path d="M272 366l3-1c-2 2-3 5-5 7 1 0 0 0 1-1s3-1 4-2l2-1h0 0c1 0 2-1 3-2v1c0 1 0 1 1 2 0 1 0 1-1 1l-1 2h0 0 3l-2 2-3 1-4 1s0-1 1-1h0c0-1-2-1-3-1h0c2-1 3-1 3-2v-1h-1l-3 2-2-1 4-6z" class="h"></path><path d="M279 372h-1c-1 0-1 1-2 1v-1c1-1 2-2 4-2h0l-1 2z" class="l"></path><path d="M288 382l5-3h0v4 1c-1 1-1 1-1 2-1 1-2 2-4 2l-1-1-2 1-1-1c-1 0-2 1-3 2h-2l1-1c-1 0 0 0-1-1 0 0-1 1-2 1 0-1-1-1-2-1s-1 0-2-1h1v-1-1l4-1c1 0 2 0 3-1 2-1 3-1 4-2 0 1 0 1-1 2l1 1 3-1z" class="V"></path><path d="M285 385c-1 1-1 1-2 1v-4h1l1 1v2z" class="f"></path><path d="M275 387v-1c1 0 3-1 4-1v2s-1 1-2 1c0-1-1-1-2-1z" class="W"></path><path d="M279 385c1 0 2-1 3-2l1 1c0 1-1 1-1 2l-2 2c-1 0 0 0-1-1v-2z" class="p"></path><path d="M288 382l5-3h0v4c-2 1-5 3-6 3s-2-1-2-1v-2l3-1z" class="k"></path><path d="M297 350c1 0 1 0 2 1v1s0 1-1 1v1h0c1-1 2-1 3-1-2 3-6 7-7 11l-3 5c-1 1-1 2-1 3-1 0-2 0-2 1v1h0 0l-2-2c1 0 2-1 2-1h-4c-1 0-1 0-2 1h-3 0 0l1-2c1 0 1 0 1-1-1-1-1-1-1-2v-1l1-1 3-3 4-4h0c1-2 3-3 4-4s3-3 5-4z" class="s"></path><path d="M297 350c1 0 1 0 2 1v1s0 1-1 1v1l-2-1c-1 1-3 1-4 1 1-1 3-3 5-4z" class="a"></path><path d="M288 358h0c1 0 1 1 0 2h2v1c-1 0-2 2-3 2-1 1-2 0-3 2h-3l3-3 4-4z" class="n"></path><path d="M288 371c1 0 1-1 1-1v-1l-2 1-1-1 1-1h2 1l1-2c1-2 2-1 3-2l-3 5c-1 1-1 2-1 3-1 0-2 0-2 1v1h0 0l-2-2c1 0 2-1 2-1z" class="b"></path><defs><linearGradient id="h" x1="161.392" y1="445.18" x2="209.267" y2="417.591" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232323"></stop></linearGradient></defs><path fill="url(#h)" d="M180 346c1 1 1 2 1 3l-1 2v1h2v3c1 0 1 1 1 2 1 1 1 1 1 2h0c0 2 1 3 2 4l1 1v-1c-1 0 0 0-1-1v-3h0v-1-1h0v1l2-2 1 3 1-1h1c0 2 1 3 2 4l4 5 1 1c2 3 4 4 8 5l-1 1h-2v1c1 0 2 1 3 1s1 0 1 1h-2 0l6 1c1 0 2-1 4 0h0 4c1 0 1 0 1 1v1c-1 0-1 0-1 1l-1 1c1 0 1 1 2 1 2-1 3-1 4-1h1c1 0 2 0 2-1 2 0 3-1 4-2l1 2c1 0 2 0 3-1l1 1h0l-1 1h1 1v1l-1 1s0 1 1 1h0v1c0 2-1 3-1 4 2-1 3-2 4-3h0l1-1h1 1c2-1 4-1 5-3h1 1c-1 1 0 1 0 3 0 0 0 1 1 2l-1 1-1-1-1 2v1h0s0 1 1 0c0-1 1-1 1-2 1 1 1 1 2 1 0 0-1 1-1 2 1 0 1 1 2 2l-1 1c0 1 0 1 1 2v1l-2-1v1 2h-1-1c-2 1-3 3-4 4h0v2l-1 1h-2v1s0 1-1 1v-3h0l-1 1v1 1 1c-1 1-1 2-2 4l-1 2c1 1 2 1 2 2l-1 4h0c-1 1-1 2-2 2h0 0l-1 1c1 1-1 7-1 9l-2 3 1 1c-1 2-1 3-2 4l1 1c-2 1-3 3-4 5v6h0v6c-1-1 0-3-1-4 0-1-1-1-1-2 1 4 1 8 0 11v3c1 1 1 2 1 2-1 4-1 7 0 10 1 2 2 4 4 6h0l-1 1v1h0c0 1 1 2 0 3l3 3h-2l1 1c4 2 7 3 11 5l1 1-1 1c-2 0-5-1-7 0l2 1c3 2 7 3 10 5h0c2 0 3 0 4 1s2 0 3 1 3 1 4 2c2 0 3 2 4 0h0l2 2h-2-1v1l-2-1h0l1 2-1 2v3c1 0 1 1 1 1 0 1 1 2 2 2 1 2 1 4 2 6 1 0 1 1 2 2 1 2 3 4 4 6h-1l-2-2c-1 0-1-1-1-1-3-2-4-3-6-5l-7-8c-1-1-3-2-5-3l-4-4-6-5-3-1c0-1-2-2-3-2h-1c1 2 1 2 0 3l-3-3v2h0c1 0 2 2 3 2v2l-3-2c-2-1-2-2-4-2-5-4-10-7-15-9l-6-4c-8-5-16-10-24-13v-2c-1 0-2-1-2-1-2 0-3-1-5-2l-10-4c0-1-1-2-2-2l-5-5-6-7-1 1c-2-1-4-2-5-4-2-1-4-4-6-6s-3-3-4-5h0c0-1 0-2-1-3 0-1 0-1-1-2v-1c0-1-1-1-1-2-2-5-6-8-8-13v-1l2 2h0v-3c0-3 1-6 2-9 1-2 1-4 2-5h0c0-1 0-1 1-2 3-5 7-7 11-9-1-6-1-13 1-20 1 0 1 0 2-1 2-6 5-12 10-15 1-2 3-3 5-4 0 1 1 0 2 0 2-2 4-3 6-4 1-1 2-1 3-2l1-1c2 0 3-2 4-3l2-1 1 2c1-1 2-2 2-3z"></path><path d="M209 472s1 0 1 1h0v1c-1-1-1-1-1-2z" class="m"></path><path d="M193 464c1 0 1 0 1 1v1h-1v-1-1z" class="k"></path><path d="M193 479l3 3h-3l-2-2 2-1z" class="R"></path><path d="M190 437c2-1 3-1 5-1v1h0s-1 1-2 1h-3v-1z" class="N"></path><path d="M154 415h1c0 1 0 1 1 1-1 1-2 2-2 3-1 0-1-1-2-2l2-2z" class="d"></path><path d="M216 480l2-2c0 3 0 5-1 8-1-2-1-4-1-6z" class="k"></path><path d="M163 375l1 1c-1 3-1 5 0 8l-1 1h0c0-3-1-7 0-10z" class="a"></path><path d="M193 438c-4 1-10 0-14 0l11-1v1h3z" class="W"></path><path d="M204 458c-1 1-3 1-5 1-1 0-3-1-4 0h-1v-1c2 0 5 0 6-1h-2v-1h2c1 1 2 1 3 1h1 0v1h0z" class="S"></path><path d="M154 409v-3h1c1 3 2 7 1 10-1 0-1 0-1-1v-2c0-2-1-3-1-4z" class="X"></path><path d="M212 464h0s0 1 1 1v1l-1 1v1l-1 1v-1c-2 3-2 5-3 8-1 0 0 0-1 1 0-2 1-6 2-7v-1l1-2v-1l2-2z" class="b"></path><path d="M200 454c0 1-1 0-1 0l-1-1h-2v-1c1 0 2 0 3-1l1 1h5l1 1c-2 0-4 0-6 1z" class="a"></path><path d="M176 462c-1-2-2-5-1-8l5 13-1 1-1-2c-1-1-1-3-2-4z" class="F"></path><path d="M202 433l1-1 2-1 1 1c-1 0-2 1-3 2h0c0 1-1 1-2 2-2 0-4 1-6 1h0v-1c2-1 4-1 6-2 0 0 1 0 1-1z" class="r"></path><path d="M221 476c0-2 1-5 2-6h1c0 1 0 6-1 7v1c-1 0-1-1-1-2h-1z" class="l"></path><path d="M222 476l1-1v1 1 1c-1 0-1-1-1-2z" class="b"></path><path d="M168 420c2 0 3 1 4 1l1 1h1v1h-3v1c-3 0-6-2-8-3h4c1 0 1 0 2 1h1 0 2-2c0-1-1-1-2-2h0z" class="n"></path><path d="M205 459c3 1 6-2 9-3h1c-3 2-6 4-10 6v-1c0-1-1-1-1-2h1z" class="S"></path><path d="M149 405l1-1c1 0 2-1 3-1 1 1 2 2 2 3h-1v3h-1l-4-4z" class="L"></path><path d="M139 400c1 0 2 0 2-1 1 0 1-1 2-1 2-1 3 0 5 1 0 0 0 1-1 1l-1 1h-1 0c-1 0-3 0-4-1l-1 1-1-1z" class="E"></path><path d="M157 365h1 1l-2 3c-1 2-3 6-3 8h0v2l-2 2h0c0-1 0-2 1-3l1-3-3 3h-1 0v-1l1-1v-2l1 1h1l1-1v-1l1-1 1-2v-1l1-2h1l-1-1zm64 111h1c0 1 0 2 1 2 0 3 1 5 1 7l-1 1c0-2 0-3-1-4h0c-1 1-1 2-1 3-1-3-1-6 0-9z" class="a"></path><path d="M224 456l1 1c-1 1-1 1-1 2v2l-6 12v-1c0-2 2-5 3-8l3-8z" class="N"></path><path d="M167 414h0c-2-1-5-2-6-4 0-2-2-3-2-4v-1l2 2h0v1c2 2 3 2 5 3l6 1c-2 1-2 1-4 1l-1 1z" class="a"></path><path d="M148 399c3 1 4 2 5 4-1 0-2 1-3 1l-1 1h0c-1-2-2-3-4-4h1l1-1c1 0 1-1 1-1z" class="J"></path><path d="M161 407c2-1 2 0 3 0l1 1c2 0 4 1 6 1l1 2c-2 0-4-1-6 0-2-1-3-1-5-3v-1h0z" class="b"></path><path d="M161 407c2-1 2 0 3 0v2c-1 0-2-1-3-2h0zm29 70c0-1 0-3-1-4v-2l-1-4v-7h1c1 5 0 10 2 14v1c0 1 1 2 1 3l-2-1z" class="k"></path><path d="M166 411c2-1 4 0 6 0h6 3v1h2 0c-2 2-9 1-11 0l-6-1z" class="V"></path><path d="M178 411h3v1h2 0c-3 0-6 1-8 0h-1-1l5-1z" class="g"></path><path d="M205 462c-2 2-5 2-8 3-1 1-1 0-2 0h0l2-2c2 0 3 0 5-1h0-3v-1c1-1 3-1 5-2 0 1 1 1 1 2v1z" class="m"></path><path d="M218 453c1-1 2-2 3-2 0 2-2 3-5 5h-1-1c-3 1-6 4-9 3l-1-1h0c2-1 5-1 7-2s5-2 7-3zm-8-4c-1-1-3-1-5-1v-1l1-1h0-6v1c-2 0-6 0-7-1v-1h1c1-1 1-1 2-1s1 0 2-1c0 1 0 1-1 1 0 1-1 1-2 1l1 1h4c3-1 6-1 9-1 1 1 0 2 1 2h2v1s-1 1-2 1z" class="b"></path><path d="M226 441h0l1 1h0l1-1c-1 1-2 2-2 4l-2 3-3 3c-1 0-2 1-3 2l-1-1c-1 0-2 1-3 1 0-1 0-1 1-1 1-1 3-2 3-3 4-2 6-5 8-8z" class="W"></path><path d="M209 429l1 1h1c-2 1-3 2-5 2l-1-1-2 1-1 1c-2 1-4 2-7 2v-1c-4 1-8 2-12 1l9-2c1 0 3-1 5-1h0c4-1 8-1 12-3z" class="g"></path><path d="M159 443h2c0 1 1 2 2 3 1-1 0-3 0-4-1-2 0-2 0-3v-2h0c1 0 1 1 2 2-1 1-1 2 0 3-1 2-1 3-1 5h-1-1 0 0v2h0c1 3 2 5 3 7h-1c0-1-1-3-2-4-1 0-1 0-1 1l-1-2-2-4c1 0 1-1 2-2l-1-2z" class="h"></path><path d="M160 445l2 7c-1 0-1 0-1 1l-1-2-2-4c1 0 1-1 2-2z" class="I"></path><path d="M164 384c1 2 1 3 2 4l1 1h0c1 1 3 2 4 4h-2c-1-1-1-2-2-1 0 2 3 2 3 5h-3-1c-1-1-2-1-2-2h0l3 1-3-4h0c-1-1-2-2-2-4 1 0 1 0 1-1h1l-1-2 1-1z" class="S"></path><path d="M164 387v1c1 1 1 3 2 5h0c-1 0-1-1-2-1h0c-1-1-2-2-2-4 1 0 1 0 1-1h1z" class="n"></path><path d="M232 437l1 1c-1 2-1 3-2 4l1 1c-2 1-3 3-4 5v6h0v6c-1-1 0-3-1-4 0-1-1-1-1-2 0 0 0-1-1-1v-1l1-2v-1c0-1 1-2 1-3l-1-1c0-2 1-3 2-4l2-2v1c1-1 1-2 2-3z" class="g"></path><path d="M228 441l2-2v1l-2 5-1 1-1-1c0-2 1-3 2-4z" class="m"></path><path d="M228 445c-1 2-1 3-1 5 1 1 1 3 1 4v6c-1-1 0-3-1-4 0-1-1-1-1-2 0 0 0-1-1-1v-1l1-2v-1c0-1 1-2 1-3l1-1z" class="N"></path><path d="M166 397h1c2 2 5 3 7 4l-1 1h0c1 1 3 1 3 2l-1 1h-2-1s-1-1-2-1v1c-3-1-5-2-7-4l-1-1h0s0 1 1 1v-1h1l-1-2v-1c2 2 3 2 5 3h2l-2-1-2-1v-1z" class="a"></path><path d="M175 405c-1-1-1-2-2-2-2 0-3-1-4-1l-2-2 3 1c1 0 2 0 3 1h0c1 1 3 1 3 2l-1 1z" class="S"></path><path d="M219 457v1 3h0 0c0 1 0 1-1 2h0v-1c-1 1-2 3-3 4 0 2 0 3-1 4l1 1c1 0 1-1 2-1-1 2-2 3-2 5l-1 2-2 5-1-5 1-1v-2h0l-1-1c0-3 1-5 2-8 1 0 1-1 1-1 0-1 1-2 2-2 0-1 0-1 1-1 0-2 1-3 2-4z" class="b"></path><path d="M214 470l1 1c1 0 1-1 2-1-1 2-2 3-2 5l-1 2h-1 0c-1-2 0-5 1-7z" class="W"></path><path d="M218 449c0 1-2 2-3 3-1 0-1 0-1 1 1 0 2-1 3-1l1 1c-2 1-5 2-7 3s-5 1-7 2v-1h0-1c-1 0-2 0-3-1h2c0-1-1-1-2-2 2-1 4-1 6-1h3c0-1 8-3 9-4z" class="N"></path><path d="M206 453h3c-1 0-2 1-4 1 0 1-1 1-2 2h-1c0-1-1-1-2-2 2-1 4-1 6-1z" class="W"></path><path d="M225 452v1c-1 1-1 1-1 2v1c-1 2-2 5-3 8s-3 6-3 8v1 5l-2 2 2-10c-1 2-2 3-3 4v1c0-2 1-3 2-5-1 0-1 1-2 1l-1-1c1-1 1-2 1-4 1-1 2-3 3-4v1h0c1-1 1-1 1-2h0 0v-3-1c0-1 5-4 6-5z" class="m"></path><path d="M219 461l3-4c-1 3-3 11-5 13-1 0-1 1-2 1l-1-1c1-1 1-2 1-4 1-1 2-3 3-4v1h0c1-1 1-1 1-2z" class="N"></path><path d="M217 424l4-2c-1 1-2 1-2 2-1 1 0 1 0 2l2-2v1l-1 1 1 1c-1 1-2 1-2 2v1l-2 1c-1 0-5 3-5 3-2 0-3 1-4 1-2 1-5 1-7 1 1-1 2-1 2-2h0c1-1 2-2 3-2 2 0 3-1 5-2h-1l-1-1c2 0 3-1 5-3h0c1 0 2-1 3-2z" class="o"></path><path d="M217 424l4-2c-1 1-2 1-2 2-1 1 0 1 0 2l-4 2h-1 0v-2h0c1 0 2-1 3-2z" class="Z"></path><path d="M203 434c3-1 6-1 9-3 1-1 2-2 3-2h0c-2 2-4 3-7 5 4-1 7-3 11-5v1l-2 1c-1 0-5 3-5 3-2 0-3 1-4 1-2 1-5 1-7 1 1-1 2-1 2-2z" class="N"></path><path d="M172 392v-1h0 3c2 2 4 4 6 5h2l1 1v1h2s1 0 1 1h-4-2c-2 0-4-1-7-1 1 1 2 1 3 2h-1-1l-1 1c-2-1-5-2-7-4h3c0-3-3-3-3-5 1-1 1 0 2 1h2l2 1h0l-1-2z" class="V"></path><defs><linearGradient id="i" x1="175.086" y1="390.594" x2="182.249" y2="399.19" xlink:href="#B"><stop offset="0" stop-color="#343535"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#i)" d="M172 392v-1h0 3c2 2 4 4 6 5h2l1 1v1h2s1 0 1 1h-4-2c-1-2-2-2-3-3-2 0-3-1-4-3 0-1-1-1-1-2l-1 1z"></path><path d="M194 419c1-1 2-1 3-1h1c0 1-1 2 0 3h0c-2 1-4 1-6 2-3 0-6 1-9 1h0s1 0 2 1h-1l-1 1h0 1 5c-3 1-6 1-10 2v-1c1 0 2 0 3-1h-3c-1 0-1 0-1-1h0c-2 0-5-1-7-1v-1h3v-1h1c1 0 3-1 4-1 4 0 8 0 12-1l3-1z" class="l"></path><path d="M183 424c-2 0-6 1-8 0l1-1h2c1 0 2 0 2 1h3 0z" class="W"></path><defs><linearGradient id="j" x1="185.17" y1="417.988" x2="192.029" y2="425.102" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#767576"></stop></linearGradient></defs><path fill="url(#j)" d="M194 419c1-1 2-1 3-1h1c0 1-1 2 0 3h0c-2 1-4 1-6 2-3 0-6 1-9 1h-3c0-1-1-1-2-1 5-1 11-1 16-3v-1z"></path><path d="M165 442c-1-1-1-2 0-3 2 5 3 10 5 15 2 3 4 5 5 8h1c1 1 1 3 2 4l-1 1c0-1-1-2-1-2-2 0-2 0-3-1v-1c-1-2-3-4-4-6l-1-1c-1 1-1 1-1 2v-1c-1-1-1-1-2-1-1-2-2-4-3-7h0v-2h0 0 1 1c0-2 0-3 1-5z" class="Y"></path><path d="M165 442c0 4 1 8 3 10 1 1 2 3 2 5h-1l-1-1c-1 1-1 1-1 2v-1c-1-1-1-1-2-1-1-2-2-4-3-7h0v-2h0 0 1 1c0-2 0-3 1-5z" class="l"></path><path d="M162 449h1c1 1 1 2 2 3s2 3 3 4c-1 1-1 1-1 2v-1c-1-1-1-1-2-1-1-2-2-4-3-7z" class="E"></path><path d="M218 432l4-3c0 1 1 1 1 2l-1 1c-1 2-4 5-6 5h0-1l-18 5h-2l1-1c-2-1-5 0-7 0h-5l11-2c6 0 11-2 17-5 0 0 4-3 5-3v1h1z" class="p"></path><path d="M212 434s4-3 5-3v1h1c-2 1-3 2-5 3s-3 2-6 3c-2 1-5 1-7 2h-5v-1c6 0 11-2 17-5z" class="f"></path><defs><linearGradient id="k" x1="171.834" y1="433.429" x2="187.368" y2="424.689" xlink:href="#B"><stop offset="0" stop-color="#0d0d0e"></stop><stop offset="1" stop-color="#393837"></stop></linearGradient></defs><path fill="url(#k)" d="M199 427c1 0 1-1 2 0h2v2l-6 3c-2 0-4 1-5 1h-1-11 0c-3 0-9-2-11-4h0c2 0 4 1 7 1s7-1 10-1c2 0 4-1 6 0 2 0 5-1 6-2h1z"></path><path d="M192 429c2 0 5-1 6-2v1 1c-5 1-10 3-15 3h-3l-1-1h1c4 0 8-2 12-2z" class="a"></path><path d="M199 427c1 0 1-1 2 0h2v2l-6 3c-2 0-4 1-5 1h-1-11c1-1 1 0 1-1h2c5 0 10-2 15-3v-1-1h1z" class="f"></path><path d="M199 427c1 0 1-1 2 0h2v2l-6 3c-2 0-4 1-5 1h-1c2-1 3-1 5-2 1 0 1-1 2-2h0v-1-1h1z" class="N"></path><path d="M199 427c1 0 1-1 2 0-1 1-2 1-3 1v-1h1z" class="h"></path><path d="M224 431c1 1 2 2 2 3-2 1-4 3-5 4-5 4-12 4-18 5-1 1-2 1-3 1-1-1-1-1-2-1h0l-1 1c-1 0-6 1-7 0h0v-1h0 2l3-1h2l18-5h1 0c2 0 5-3 6-5l2-1z" class="a"></path><path d="M195 442h2c1 1 2 0 3 1h-1c-2 0-5 1-7 0l3-1z" class="V"></path><defs><linearGradient id="l" x1="201.134" y1="437.995" x2="205.815" y2="442.888" xlink:href="#B"><stop offset="0" stop-color="#585558"></stop><stop offset="1" stop-color="#6b6d6a"></stop></linearGradient></defs><path fill="url(#l)" d="M197 442l18-5h1c-5 3-11 5-16 6-1-1-2 0-3-1z"></path><path d="M227 429h2c0-1 1-1 1-2h0v1h1c0 4-3 7-6 10h-1c-2 2-2 3-5 4-1 0-2 1-3 2l-1 1c0 1-1 2-2 3h-1v-1h-2c-1 0 0-1-1-2-3 0-6 0-9 1h-4l-1-1c1 0 2 0 2-1 1 0 1 0 1-1 1 0 1 0 2 1 1 0 2 0 3-1 6-1 13-1 18-5 1-1 3-3 5-4 0-1-1-2-2-3 1-1 2-2 3-2z" class="W"></path><path d="M227 429h2c0-1 1-1 1-2h0v1c-1 2-2 4-4 6 0-1-1-2-2-3 1-1 2-2 3-2z" class="b"></path><defs><linearGradient id="m" x1="202.446" y1="427.987" x2="217.324" y2="417.598" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#5b595a"></stop></linearGradient></defs><path fill="url(#m)" d="M209 422c3 0 5-2 7-3 1 0 2 0 3-1 1 0 2-1 3-1h1v1c-1 1-1 1-1 2s-1 1-1 2l-4 2c-1 1-2 2-3 2h0c-2 2-3 3-5 3-4 2-8 2-12 3h0l6-3v-2h-2c-1-1-1 0-2 0 1-2 3-2 5-3 2 0 3 0 5-2z"></path><path d="M214 424l1-1c2-1 5-5 8-5-1 1-1 1-1 2s-1 1-1 2l-4 2v-1c-1 1-2 1-2 1h-1z" class="T"></path><path d="M214 424h1s1 0 2-1v1c-1 1-2 2-3 2h0c-2 2-3 3-5 3-4 2-8 2-12 3h0l6-3c2-1 4-1 5-2l6-3z" class="F"></path><defs><linearGradient id="n" x1="176.178" y1="406.243" x2="180.086" y2="412.218" xlink:href="#B"><stop offset="0" stop-color="#2a2c29"></stop><stop offset="1" stop-color="#494549"></stop></linearGradient></defs><path fill="url(#n)" d="M163 401c2 2 4 3 7 4v-1c1 0 2 1 2 1h1c2 1 4 1 6 1v1h0c1 0 1 1 2 1s2 0 3-1h7c3-1 4-2 5-3h2c-1 1-1 2-1 3 1 1 2 0 3 1v1h-1c-1 1-3 1-5 1h-1c-2 2-7 2-10 2h-2v-1h-3-6l-1-2c-2 0-4-1-6-1v-2s0-1-1-2h-1c-1-1 0-2 0-3z"></path><path d="M184 407h7 0v1c-1 0-2 0-3 1h-11 0l4-1c1 0 2 0 3-1z" class="o"></path><path d="M192 408h1 1 0l-1 1h0v1h0 0c-2 2-7 2-10 2h-2v-1l7-1v-1c1-1 2-1 3-1h1z" class="Z"></path><path d="M191 408h1c-1 1-2 2-4 2v-1c1-1 2-1 3-1z" class="c"></path><path d="M196 404h2c-1 1-1 2-1 3 1 1 2 0 3 1v1h-1c-1 1-3 1-5 1h-1 0 0v-1h0l1-1h0-1-1-1v-1h0c3-1 4-2 5-3z" class="j"></path><path d="M163 401c2 2 4 3 7 4v-1c1 0 2 1 2 1h1c2 1 4 1 6 1v1h0c1 0 1 1 2 1l-4 1h0-3-1c-2 0-5-1-6-3h-1-1s0-1-1-2h-1c-1-1 0-2 0-3z" class="N"></path><path d="M173 406h1c1 1 3 0 5 2h-5v1h-1c-2 0-5-1-6-3 2 1 4 1 6 1v-1z" class="S"></path><path d="M163 401c2 2 4 3 7 4 1 1 2 1 3 1v1c-2 0-4 0-6-1h-1-1s0-1-1-2h-1c-1-1 0-2 0-3z" class="k"></path><path d="M172 412c2 1 9 2 11 0 0 1 1 1 1 1v1h0v1h1 0c-2 1-3 1-4 2v1h6 1c3-1 7-2 11-3h2v1c1-1 2-1 3-1h0v1l1 1-2 1h0c-1 2-3 2-5 3h0 0c-1-1 0-2 0-3h-1c-1 0-2 0-3 1l-3 1c-4 1-8 1-12 1-1 0-3 1-4 1h-1-1l-1-1c-1 0-2-1-4-1-1-1-5-2-6-3 2 0 3 1 4 1 2 1 5 1 7 1 1 0 1 0 1-1-2 0-4-1-5-1h3c2 0 4 0 5-1h-1c-1-1-2-1-3-1l-6-1 1-1c2 0 2 0 4-1z" class="b"></path><path d="M179 415h-4v-1l9-1v1h0v1h-5 0z" class="g"></path><path d="M175 422c-1-1-1-2-2-2h-1v-1c2 0 5 0 7 1v1c-1 0-3 1-4 1z" class="W"></path><path d="M179 420l11-1h-1l-1 1h3c-4 1-8 1-12 1v-1z" class="c"></path><path d="M184 415h1 0c-2 1-3 1-4 2v1h6c-2 1-5 0-7 1h0c-2-1-3 0-5-1l1-1h2l2-2h-1 0 5z" class="N"></path><path d="M204 415h0v1l1 1-2 1h0c-1 2-3 2-5 3h0 0c-1-1 0-2 0-3h-1c-1 0-2 0-3 1l-3 1h-3l1-1h1c3-1 7-2 10-3h1c1-1 2-1 3-1z" class="K"></path><path d="M204 415h0v1c-1 0-2 1-3 1 0 1-1 1-2 0l1-1h1c1-1 2-1 3-1z" class="F"></path><path d="M234 424c1-1 1-2 2-3h0 0v1h1v-1l1 1h0c-1 1-1 2-2 2h0 0l-1 1c1 1-1 7-1 9l-2 3c-1 1-1 2-2 3v-1l-2 2-1 1h0l-1-1h0c-2 3-4 6-8 8-1 1-9 3-9 4h-3l-1-1h-5l-1-1 11-2c1 0 2-1 2-1h1c1-1 2-2 2-3l1-1c1-1 2-2 3-2 3-1 3-2 5-4h1c3-3 6-6 6-10 0-1 1-2 2-3 0 0 1 0 1-1z" class="V"></path><path d="M225 440c2-1 3-4 5-6-1 3-2 5-4 7h0l-1-1z" class="S"></path><path d="M234 424c1-1 1-2 2-3h0 0v1h1v-1l1 1h0c-1 1-1 2-2 2h0 0l-1 1c0 1-1 3-2 4h-1c1-2 2-3 2-5z" class="j"></path><path d="M225 440l1 1c-2 3-4 6-8 8-1 1-9 3-9 4h-3l-1-1c4-1 7-2 11-4h0c3-3 6-5 8-7l1-1z" class="l"></path><path d="M212 403h0v2h-1v1c1 1 1 1 3 1l1-1c1-1 3-1 4-2v1h0c-1 1-1 1-2 1l2 2c-1 0-2 1-3 2s-1 2-2 2c-2 2-4 2-6 3-1 1-2 1-3 2l-1-1v-1h0c-1 0-2 0-3 1v-1h-2c-4 1-8 2-11 3h-1-6v-1c1-1 2-1 4-2h0-1v-1h0v-1s-1 0-1-1h0c3 0 8 0 10-2h1c2 0 4 0 5-1h1v-1l2-1h0l2-1c3-1 5-2 8-3z" class="j"></path><path d="M202 407h0l2-1c1 1 2 1 3 1l1 1c-1 1-1 1-3 1l-1-1h0l-2-1z" class="K"></path><path d="M204 408h4c-1 1-1 1-3 1l-1-1z" class="i"></path><path d="M199 415c1-2 2-3 5-3h1 2c1 0 2-1 3-1 1-1 1-1 2-1h0 1l-2 2c-2 1-5 2-7 3-1 0-2 0-3 1v-1h-2z" class="K"></path><path d="M202 407l2 1h0l1 1c-3 2-7 4-11 6-1 0-5 1-6 1v2h-1-6v-1c1-1 2-1 4-2h0-1v-1h0v-1s-1 0-1-1h0c3 0 8 0 10-2h1c2 0 4 0 5-1h1v-1l2-1z" class="q"></path><path d="M202 407l2 1c-2 1-3 1-4 1v-1l2-1zm-18 6c4 0 8 0 12-1h0c-1 1-3 2-4 2h-2c-1 1-1 1-2 0-1 1-1 2-3 1h0 0-1v-1h0v-1z" class="i"></path><path d="M184 414h4c-1 1-1 2-3 1h0 0-1v-1z" class="V"></path><path d="M193 410h1c2 0 4 0 5-1v1c-1 1-2 1-3 2-4 1-8 1-12 1 0 0-1 0-1-1h0c3 0 8 0 10-2z" class="Y"></path><defs><linearGradient id="o" x1="223.959" y1="425.522" x2="223.76" y2="419.795" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#8f8d8e"></stop></linearGradient></defs><path fill="url(#o)" d="M240 407v1 1 1c-1 1-1 2-2 4l-1 2c1 1 2 1 2 2l-1 4-1-1v1h-1v-1h0 0c-1 1-1 2-2 3 0 1-1 1-1 1-1 1-2 2-2 3h-1v-1h0c0 1-1 1-1 2h-2c-1 0-2 1-3 2l-2 1 1-1c0-1-1-1-1-2l-4 3h-1v-1l2-1v-1c0-1 1-1 2-2l-1-1 1-1v-1l-2 2c0-1-1-1 0-2 0-1 1-1 2-2 0-1 1-1 1-2s0-1 1-2v-1c1 0 2-1 4-2l1-1v1l3-1 4-6 1 1v1h0l2-3h2z"></path><path d="M222 420l2-1c0 1 0 1 1 2-2 1-3 2-4 3l-2 2c0-1-1-1 0-2 0-1 1-1 2-2 0-1 1-1 1-2z" class="F"></path><path d="M224 425c1-1 3-2 5-3 1 0 1 1 2 1l-1 2-2 1h0c-1-1-1-1-2 0-1 0-1 0-2-1z" class="j"></path><path d="M219 430l3-3c1 0 1-1 2-2 1 1 1 1 2 1 1-1 1-1 2 0h0c-2 1-4 1-6 3h0l-4 3h-1v-1l2-1z" class="K"></path><path d="M228 426l2-1v1c-1 1-2 2-3 2v1c-1 0-2 1-3 2l-2 1 1-1c0-1-1-1-1-2h0c2-2 4-2 6-3z" class="W"></path><path d="M240 407v1c-1 2-2 4-3 5l-2 2v-1c0-1 1-1 1-2h-1c-1 1-3 5-4 5h0c1-2 3-6 5-7h0l2-3h2z" class="E"></path><path d="M235 408l1 1v1c-2 1-4 5-5 7l-1-1-1 1-3 3-1 1c-1-1-1-1-1-2l-2 1c0-1 0-1 1-2v-1c1 0 2-1 4-2l1-1v1l3-1 4-6z" class="I"></path><path d="M226 420c-1-1-1-1-1-2 1 0 1-1 2-1h2l-3 3z" class="Q"></path><defs><linearGradient id="p" x1="230.305" y1="424.3" x2="235.327" y2="421.472" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#595b59"></stop></linearGradient></defs><path fill="url(#p)" d="M240 408v1 1c-1 1-1 2-2 4l-1 2c1 1 2 1 2 2l-1 4-1-1v1h-1v-1h0 0c-1 1-1 2-2 3 0 1-1 1-1 1-1 1-2 2-2 3h-1v-1h0c0 1-1 1-1 2h-2v-1c1 0 2-1 3-2v-1l1-2c-1 0-1-1-2-1 1-2 2-3 3-4l3-4v1l2-2c1-1 2-3 3-5z"></path><path d="M236 421l1-1v-4c1 1 2 1 2 2l-1 4-1-1v1h-1v-1z" class="F"></path><path d="M235 414v1l2-2c-1 2-1 4-2 5h-3l3-4z" class="J"></path><path d="M232 418h3l-4 5c-1 0-1-1-2-1 1-2 2-3 3-4z" class="R"></path><path d="M177 347l1 2-4 4v1 1l-2 3h0v1h0c-1 1-2 2-2 4l2-2c2 0 3-2 4-3v1l-1 1c-1 2-3 4-5 6 0 1-1 2-2 3-1 2-3 4-4 7l-1-1c1-1 2-3 2-5l-1 1-1-1c-1 1-1 1-2 1-2 2-4 3-5 6v1c-1-1 0-2-1-4l-1 2c0-2 2-6 3-8l2-3h-1-1c-4 2-6 8-9 11v-1c1-3 4-7 6-10 2-1 3-2 4-3 2-1 3-2 4-4h-1c2-2 4-3 6-4 1-1 2-1 3-2l1-1c2 0 3-2 4-3l2-1z" class="N"></path><path d="M174 353v1 1l-2 3h0v1l-3 2h0l-3 2-2 2c-1 0-1 0-1 1 0-1 0-1-1-2h0l-2 2v-1c0-1 1-2 2-3s3-3 5-4c2-2 5-3 7-5z" class="g"></path><path d="M166 360c1-1 2-2 4-2-1 1-1 1-1 2v1h0l-3 2v-3z" class="c"></path><path d="M166 360v3l-2 2c-1 0-1 0-1 1 0-1 0-1-1-2h0c1-1 2-2 2-3l2-1z" class="r"></path><path d="M162 364c1-1 2-2 2-3l1 1-1 2v1c-1 0-1 0-1 1 0-1 0-1-1-2h0z" class="N"></path><defs><linearGradient id="q" x1="166.609" y1="373.695" x2="165.415" y2="359.211" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#q)" d="M172 361c2 0 3-2 4-3v1l-1 1c-1 2-3 4-5 6 0 1-1 2-2 3-1 2-3 4-4 7l-1-1c1-1 2-3 2-5l-1 1-1-1c-1 1-1 1-2 1-2 2-4 3-5 6v1c-1-1 0-2-1-4 0 0 1-1 1-2l4-6 2-2h0c1 1 1 1 1 2 0-1 0-1 1-1l2-2 3-2h0l3-2h0c-1 1-2 2-2 4l2-2z"></path><path d="M172 359h0c-1 1-2 2-2 4-1 2-3 3-4 4 1-2 2-4 4-5l-1-1h0l3-2z" class="N"></path><defs><linearGradient id="r" x1="179.47" y1="410.323" x2="204.336" y2="390.014" xlink:href="#B"><stop offset="0" stop-color="#2a282a"></stop><stop offset="1" stop-color="#585958"></stop></linearGradient></defs><path fill="url(#r)" d="M213 395l3-1 1 2 3-2 1 1-8 4c-1 1 0 1 0 3l-1 1h0l-8 3-2 1h0l-2 1c-1-1-2 0-3-1 0-1 0-2 1-3h-2c-1 1-2 2-5 3h-7c-1 1-2 1-3 1s-1-1-2-1h0v-1c-2 0-4 0-6-1h2l1-1c0-1-2-1-3-2h0l1-1 1-1h1 1c-1-1-2-1-3-2 3 0 5 1 7 1h2 4c3 0 5-1 8-1v1h3c1 1 1 1 2 1h1c3-1 7-2 9-4h2 0l1-1z"></path><path d="M213 395l3-1 1 2c-2 0-5 2-6 1h0c1 0 1 0 1-1h0 0l1-1z" class="i"></path><path d="M179 406c3-1 6-1 10-1-2 1-3 1-5 2-1 1-2 1-3 1s-1-1-2-1h0v-1z" class="V"></path><path d="M189 405h2c2 0 4-1 5-1-1 1-2 2-5 3h-7c2-1 3-1 5-2zm-12-5c1 0 3 1 4 1l6 2h1 2c-6 1-10-1-16-2l1-1h1 1z" class="c"></path><defs><linearGradient id="s" x1="199.526" y1="408.234" x2="210.107" y2="398.639" xlink:href="#B"><stop offset="0" stop-color="#a1a0a1"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#s)" d="M198 404c5-1 10-3 15-5-1 1 0 1 0 3l-1 1h0c-3 1-5 2-8 3l-2 1h0l-2 1c-1-1-2 0-3-1 0-1 0-2 1-3z"></path><path d="M187 399c3 0 5-1 8-1v1h3c1 1 1 1 2 1h1c-1 1-2 1-3 2-3 0-5 1-8 1h-2-1l-6-2c-1 0-3-1-4-1-1-1-2-1-3-2 3 0 5 1 7 1h2 4z" class="q"></path><path d="M198 402c-1-1-2-2-3-2 1-1 2-1 3-1 1 1 1 1 2 1h1c-1 1-2 1-3 2z" class="g"></path><path d="M177 400c-1-1-2-1-3-2 3 0 5 1 7 1h2 8c-3 1-7 2-10 2-1 0-3-1-4-1z" class="i"></path><defs><linearGradient id="t" x1="189.884" y1="430.688" x2="199.532" y2="416.848" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#t)" d="M230 406c2-2 4-3 5-4s1-2 2-2c1-1 1-1 2 0l-1 1s1 0 1 1h0l1 1h0l1 1v2l-1 1h-2l-2 3h0v-1l-1-1-4 6-3 1v-1l-1 1c-2 1-3 2-4 2h-1c-1 0-2 1-3 1-1 1-2 1-3 1-2 1-4 3-7 3-2 2-3 2-5 2-2 1-4 1-5 3h-1c-1 1-4 2-6 2-2-1-4 0-6 0 2-1 5-1 6-2l-1-1h-2-5-1 0l1-1h1c-1-1-2-1-2-1h0c3 0 6-1 9-1 2-1 4-1 6-2h0c2-1 4-1 5-3l12-4 1-1c1 0 1 0 2-1h2l-1 1 1 1 2-2h0l1-1 7-5z"></path><path d="M198 421h0 1c-1 1-2 2-4 2h-2-1c2-1 4-1 6-2z" class="V"></path><path d="M192 423h1c-2 1-7 3-9 2h1c-1-1-2-1-2-1h0c3 0 6-1 9-1z" class="b"></path><path d="M215 414l1-1c1 0 1 0 2-1h2l-1 1 1 1c-5 1-9 4-13 5h-1l9-5h0z" class="T"></path><defs><linearGradient id="u" x1="195.216" y1="429.346" x2="196.761" y2="422.514" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#525051"></stop></linearGradient></defs><path fill="url(#u)" d="M191 426c1-1 3 0 5-1 1-1 3-1 4-2h1c2 0 2 0 3 1-2 1-4 1-5 3h-1c-1 1-4 2-6 2-2-1-4 0-6 0 2-1 5-1 6-2l-1-1z"></path><path d="M230 406c2-2 4-3 5-4s1-2 2-2c1-1 1-1 2 0l-1 1s1 0 1 1h0l1 1h0l1 1v2l-1 1h-2l-2 3h0v-1l-1-1-4 6-3 1v-1l-1 1c-2 1-3 2-4 2h-1c-1 0-2 1-3 1-1 1-2 1-3 1-2 1-4 3-7 3 1-1 5-2 7-4h-1c-2 1-5 2-8 3h0c2-2 5-3 7-4l6-3c1 0 2 0 3-1l-1-1h0l1-1 7-5z" class="F"></path><path d="M237 403c0 3-1 3-2 5l-4 6-3 1v-1c1 0 2-1 3-3s4-5 6-8z" class="P"></path><path d="M237 403h0c0-1 0-1 1-2 0 0 1 0 1 1h0l1 1h0l1 1v2l-1 1h-2l-2 3h0v-1l-1-1c1-2 2-2 2-5z" class="M"></path><path d="M237 403h0c0-1 0-1 1-2 0 0 1 0 1 1h0c-1 2-2 5-3 7l-1-1c1-2 2-2 2-5z" class="R"></path><path d="M215 418c2-1 3-2 5-3 1 0 3-1 5-2h0l4-4h1c-1 2-3 4-4 5-1 0-2 1-3 1v1l3-1h1c-2 1-3 2-4 2h-1c-1 0-2 1-3 1-1 1-2 1-3 1-2 1-4 3-7 3 1-1 5-2 7-4h-1z" class="K"></path><path d="M248 383h1 1c-1 1 0 1 0 3 0 0 0 1 1 2l-1 1-1-1-1 2v1h0s0 1 1 0c0-1 1-1 1-2 1 1 1 1 2 1 0 0-1 1-1 2 1 0 1 1 2 2l-1 1c0 1 0 1 1 2v1l-2-1v1 2h-1-1c-2 1-3 3-4 4h0v2l-1 1h-2v1s0 1-1 1v-3h0v-2l-1-1h0l-1-1h0c0-1-1-1-1-1l1-1c-1-1-1-1-2 0-1 0-1 1-2 2s-3 2-5 4l-7 5-1 1h0l-2 2-1-1 1-1h-2c-1 1-1 1-2 1l-1 1-12 4h0l2-1c1-1 2-1 3-2 2-1 4-1 6-3 1 0 1-1 2-2s2-2 3-2l-2-2c1 0 1 0 2-1h0v-1c-1 1-3 1-4 2l-1 1c-2 0-2 0-3-1v-1h1v-2h0 0l1-1c0-2-1-2 0-3l8-4s2-2 3-2 2-1 3-2c3 0 5-4 8-4h0c1 0 2-1 2-1 0 2-1 3-1 4 2-1 3-2 4-3h0l1-1h1 1c2-1 4-1 5-3z" class="R"></path><g class="F"><path d="M219 405l1 1 1-1v1l-2 2-2-2c1 0 1 0 2-1z"></path><path d="M219 404v-1c1 0 1 0 1-1 1-1 2-1 3-2h0c0 2-2 4-4 5v-1z"></path></g><path d="M212 405h0c1 0 1 0 2-1 0 0 2-1 3-1h1c-1 1-3 1-3 2v1l-1 1c-2 0-2 0-3-1v-1h1z" class="T"></path><path d="M226 405h0c1 0 2 0 3-1 0 1 1 1 1 2l-7 5c0-1-1-1-1-1-2 0-3 2-4 2 1-1 1-2 3-4h0c1-1 3-2 5-3h0z" class="P"></path><path d="M236 394l1-2 1 2 1-1h0c-1 2-3 3-4 4 0 1-1 1-1 2l-2 1v-1l-8 5h-1 0l2-2c1-2 3-3 4-4 3-1 5-3 7-4z" class="J"></path><path d="M236 394l1-2 1 2 1-1h0c-1 2-3 3-4 4 0 1-1 1-1 2l-2 1v-1l1-2 3-3h0z" class="Q"></path><path d="M227 391c3 0 5-4 8-4h0c1 0 2-1 2-1 0 2-1 3-1 4-2 2-4 3-7 4l-1 1c-5 3-10 5-15 7 0-2-1-2 0-3l8-4s2-2 3-2 2-1 3-2z" class="D"></path><path d="M228 395h-3l2-2h1c1 0 1 0 1 1l-1 1z" class="C"></path><path d="M248 383h1 1c-1 1 0 1 0 3 0 0 0 1 1 2l-1 1-1-1-1 2v1h0s0 1 1 0c0-1 1-1 1-2 1 1 1 1 2 1 0 0-1 1-1 2 1 0 1 1 2 2l-1 1c0 1 0 1 1 2v1l-2-1v1 2h-1-1c-2 1-3 3-4 4h0v2l-1 1h-2v1s0 1-1 1v-3h0v-2l-1-1h0l-1-1h0c0-1-1-1-1-1l1-1c-1-1-1-1-2 0-1 0-1 1-2 2s-3 2-5 4c0-1-1-1-1-2-1 1-2 1-3 1h0c1-2 4-3 5-5h1l2-1c0-1 1-1 1-2 1-1 3-2 4-4h0c1-1 1-1 1-2 0 0 0-1 1-2h0 0-1v-1h1l-1-1h0l1-1h1 1c2-1 4-1 5-3z" class="E"></path><path d="M232 400l2-1c-1 1-1 2-1 3h-1-1v-2h1z" class="O"></path><path d="M242 401c1-1 2-1 3-1 0 1-1 2-1 3l1 1h0v2l-1 1h-2v1s0 1-1 1v-3c1-1 1-1 1-2v-3z" class="D"></path><path d="M239 393h2c0 1-1 2 0 3l-2 2h-2c-1 1-1 2-3 2h0c0-1 1-2 1-3 1-1 3-2 4-4z" class="M"></path><path d="M249 391c0-1 1-1 1-2 1 1 1 1 2 1 0 0-1 1-1 2 1 0 1 1 2 2l-1 1c0 1 0 1 1 2v1l-2-1v1 2h-1-1c-2 1-3 3-4 4l-1-1c0-1 1-2 1-3-1 0-2 0-3 1v-1l2-2 1-2v-1c1 0 1 0 2-1s2-1 3-2l-1-1z" class="B"></path><path d="M249 391c0-1 1-1 1-2 1 1 1 1 2 1 0 0-1 1-1 2-1 1-1 1-1 2v1h-2c-1 1-2 3-3 5h0c-1 0-2 0-3 1v-1l2-2 1-2v-1c1 0 1 0 2-1s2-1 3-2l-1-1z" class="P"></path><path d="M248 383h1 1c-1 1 0 1 0 3 0 0 0 1 1 2l-1 1-1-1-1 2v1h0s0 1 1 0l1 1c-1 1-2 1-3 2s-1 1-2 1v1l-1 2h-3v-1-1c-1-1 0-2 0-3h-2 0c1-1 1-1 1-2 0 0 0-1 1-2h0 0-1v-1h1l-1-1h0l1-1h1 1c2-1 4-1 5-3z" class="E"></path><path d="M241 393h1c0 1-1 0 0 1 2 0 3-3 4-3 1 1 1 2 1 3-1 1-1 1-2 1v1l-1 2h-3v-1-1c-1-1 0-2 0-3z" class="D"></path><path d="M241 397l1-1h3l-1 2h-3v-1z" class="B"></path><path d="M248 383h1 1c-1 1 0 1 0 3 0 0 0 1 1 2l-1 1-1-1c1-1 0-3 0-4h0-1v1l-4 4h-2l-2 2s0-1 1-2h0 0-1v-1h1l-1-1h0l1-1h1 1c2-1 4-1 5-3z" class="e"></path><path d="M241 386h1 1c-1 2-1 2-2 2l-1-1h0l1-1z" class="F"></path><defs><linearGradient id="v" x1="223.04" y1="475.762" x2="240.937" y2="544.722" xlink:href="#B"><stop offset="0" stop-color="#c9c6c6"></stop><stop offset="1" stop-color="#fcfdfd"></stop></linearGradient></defs><path fill="url(#v)" d="M165 456c1 0 1 0 2 1v1c0-1 0-1 1-2l1 1c1 2 3 4 4 6v1c1 1 1 1 3 1 0 0 1 1 1 2l1-1 1 2 1-1 1 2c2 3 6 6 9 8l2 1 1 1-2 1 2 2h3c6 5 13 9 19 12 3 0 5 1 8 2h3c1 0 0-1 1 0h0 0 1 3 0 1 0v-1c4 2 7 3 11 5l1 1-1 1c-2 0-5-1-7 0l2 1c3 2 7 3 10 5h0c2 0 3 0 4 1s2 0 3 1 3 1 4 2c2 0 3 2 4 0h0l2 2h-2-1v1l-2-1h0l1 2-1 2v3c1 0 1 1 1 1 0 1 1 2 2 2 1 2 1 4 2 6 1 0 1 1 2 2 1 2 3 4 4 6h-1l-2-2c-1 0-1-1-1-1-3-2-4-3-6-5l-7-8c-1-1-3-2-5-3l-4-4-6-5-3-1c0-1-2-2-3-2h-1c1 2 1 2 0 3l-3-3-3-3h0c-3-2-7-4-11-6l-8-4c-2-2-4-2-6-4-4-2-8-5-12-8-6-5-12-9-17-15-1-2-3-4-4-6-2-2-3-3-4-5h1z"></path><path d="M244 510c1-1 2 0 4 0h1c0 1 0 2-1 3-1-1-3-2-4-3z" class="f"></path><path d="M226 504c2 0 3 1 5 1 0 0 2 1 2 2h-1c1 2 1 2 0 3l-3-3-3-3z" class="Z"></path><path d="M215 494c3 0 5 1 8 2 1 0 1 0 2 1 0 1-1 1-1 2l-9-5z" class="b"></path><path d="M168 456l1 1c1 2 3 4 4 6v1h-1l-3-3-2-3c0-1 0-1 1-2z" class="D"></path><path d="M169 461c2 0 3 1 4 2v1h-1l-3-3z" class="B"></path><path d="M225 497h1l1 1c2 2 5 2 6 4 2 0 2 0 3-1v1l2 1v1h-3c-4-2-8-3-11-5 0-1 1-1 1-2z" class="h"></path><path d="M223 496h3c1 0 0-1 1 0h0 0c3 1 8 2 10 3v1l-1 1c-1 1-1 1-3 1-1-2-4-2-6-4l-1-1h-1c-1-1-1-1-2-1z" class="n"></path><path d="M238 503c3 2 7 3 10 5v2c-2 0-3-1-4 0l-1-1c-2-1-3-2-5-3l-3-2h3v-1z" class="K"></path><path d="M238 506c3 0 5 1 7 3h-2c-2-1-3-2-5-3z" class="I"></path><path d="M232 495c4 2 7 3 11 5l1 1-1 1c-2 0-5-1-7 0v-1l1-1v-1c-2-1-7-2-10-3h1 3 0 1 0v-1z" class="p"></path><path d="M172 464h1c1 1 1 1 3 1 0 0 1 1 1 2l1-1 1 2 1-1 1 2c2 3 6 6 9 8l2 1 1 1-2 1 2 2v1h-1c-1-1-2-2-3-2-7-5-13-10-17-17z" class="X"></path><path d="M178 466l1 2 1-1 1 2c-1 0-1 0-2 1-1-1-1-2-2-3l1-1z" class="j"></path><defs><linearGradient id="w" x1="186.681" y1="470.592" x2="185.082" y2="478.236" xlink:href="#B"><stop offset="0" stop-color="#989c94"></stop><stop offset="1" stop-color="#afacb1"></stop></linearGradient></defs><path fill="url(#w)" d="M179 470c1-1 1-1 2-1 2 3 6 6 9 8l2 1 1 1-2 1c-2-1-4-2-6-4l-6-6z"></path><path d="M248 508c2 0 3 0 4 1s2 0 3 1 3 1 4 2c2 0 3 2 4 0h0l2 2h-2-1v1l-2-1h0l1 2-1 2v3c0-1-1-1-2-2-2 0-3-1-5-2-1-1-3-3-5-4 1-1 1-2 1-3h-1v-2h0z" class="e"></path><path d="M248 508h0c2 1 4 2 5 3l1 1h-2 0c-1-1-2-1-3-2h-1v-2z" class="Z"></path><path d="M252 512h2c2 2 4 3 6 6h0v3c0-1-1-1-2-2-2 0-3-1-5-2l1-1c1 1 1 1 2 1v-1l-2-2c0-1-1-2-2-2z" class="K"></path><path d="M249 510c1 1 2 1 3 2h0c1 0 2 1 2 2l2 2v1c-1 0-1 0-2-1l-1 1c-1-1-3-3-5-4 1-1 1-2 1-3z" class="Y"></path><path d="M154 435l2-4h1v2 4l1 1c1 1 1 3 1 5l1 2c-1 1-1 2-2 2l2 4 1 2c0-1 0-1 1-1 1 1 2 3 2 4 1 2 2 3 4 5 1 2 3 4 4 6 5 6 11 10 17 15 4 3 8 6 12 8 2 2 4 2 6 4l8 4c4 2 8 4 11 6h0l3 3v2h0c1 0 2 2 3 2v2l-3-2c-2-1-2-2-4-2-5-4-10-7-15-9l-6-4c-8-5-16-10-24-13v-2c-1 0-2-1-2-1-2 0-3-1-5-2l-10-4c0-1-1-2-2-2l-5-5-6-7c-2-2-6-6-7-9v-1l5 6v-2l-1-1c1 1 1 1 1 2l1 1h0c1 1 1 2 2 2h1c-1-1-2-2-2-3v-1-1h-1l1-1c1 0 1 0 1 1h0l1 1v-1-2c-1-1-1-11 0-12v1h0c1-1 1-2 2-4v-1z" class="t"></path><path d="M154 455c2 1 3 3 4 5 1 1 1 2 1 3-2-2-4-5-5-7v-1z" class="C"></path><path d="M161 453c0-1 0-1 1-1 1 1 2 3 2 4 1 2 2 3 4 5 1 2 3 4 4 6-1 0-2 0-3-1-3-3-5-4-7-7v-2-1c-1-1-1-2-1-3z" class="X"></path><path d="M154 435l2-4h1v2 4l1 1c1 1 1 3 1 5l1 2c-1 1-1 2-2 2l2 4 1 2c0 1 0 2 1 3v1 2c-3-3-6-7-7-11h0c-1-2-2-7-1-9v-1-1c1 0 1 0 1-1h0v-1c-1 2-2 3-2 4v2c-1 3-1 8 0 11l1 3v1l-1 1-3-3v-1h-1l1-1c1 0 1 0 1 1h0l1 1v-1-2c-1-1-1-11 0-12v1h0c1-1 1-2 2-4v-1z" class="M"></path><path d="M160 451l1 2c0 1 0 2 1 3v1l-2-1c-1-2-1-3-2-5h1 1z" class="U"></path><path d="M157 444v-1l-1-1-2 1h0c0-3 1-5 3-8v4c-1 1 0 3 0 5z" class="O"></path><path d="M157 435v-2 4l1 1c1 1 1 3 1 5l1 2c-1 1-1 2-2 2l-1-3c0-2-1-4 0-5v-4z" class="Y"></path><path d="M143 450l5 6v-2l-1-1c1 1 1 1 1 2l1 1h0c1 1 1 2 2 2h1c-1-1-2-2-2-3v-1l3 3 1-1c1 2 3 5 5 7 5 8 12 11 19 17-2 0-3-1-5-2l-10-4c0-1-1-2-2-2l-5-5-6-7c-2-2-6-6-7-9v-1z" class="N"></path><defs><linearGradient id="x" x1="155.361" y1="466.48" x2="154.927" y2="457.034" xlink:href="#B"><stop offset="0" stop-color="#c8c6c9"></stop><stop offset="1" stop-color="#e8e8e7"></stop></linearGradient></defs><path fill="url(#x)" d="M148 456v-2l-1-1c1 1 1 1 1 2l1 1h0c1 1 1 2 2 2h1c-1-1-2-2-2-3v-1l3 3c3 3 5 7 8 11 1 1 2 2 3 4-4-2-8-7-11-11-2-1-4-3-5-5z"></path><path d="M130 409c2-4 5-7 9-9l1 1 1-1c1 1 3 1 4 1h0c2 1 3 2 4 4h0l4 4h1c0 1 1 2 1 4v2h-1l-2 2c1 1 1 2 2 2l-2 2c1 0 2 0 4 1v3c0 1 1 2 1 3v2c-1 1-3 3-3 4v1 1c-1 2-1 3-2 4h0v-1c-1 1-1 11 0 12v2 1l-1-1h0c0-1 0-1-1-1l-1 1h1v1 1c0 1 1 2 2 3h-1c-1 0-1-1-2-2h0l-1-1c0-1 0-1-1-2l1 1v2l-5-6v1c1 3 5 7 7 9l-1 1c-2-1-4-2-5-4-2-1-4-4-6-6s-3-3-4-5h0c0-1 0-2-1-3 0-1 0-1-1-2v-1c0-1-1-1-1-2-2-5-6-8-8-13v-1l2 2h0v-3c0-3 1-6 2-9 1-2 1-4 2-5h0 1z" class="X"></path><path d="M137 438l1 1h0v1c1 1 1 1 1 2l1 2c0 1 1 3 2 4 0 0 1 1 1 2v1c1 3 5 7 7 9l-1 1c-2-1-4-2-5-4h1l-3-3 1-1-1-2c-1-2-2-5-3-7-1-1-1-3-1-4-1-1-1-1-1-2z" class="O"></path><path d="M134 432h-1v-1-1c-1 0-1 0-1-1v-1l-1-1v4l-1-1v-1c0-2-1-6 0-8 1-1 1-3 2-5v-1c0-1 0-2 1-3v1c0 5-1 12 1 17v1 1h0z" class="D"></path><defs><linearGradient id="y" x1="125.21" y1="429.667" x2="128.918" y2="417.426" xlink:href="#B"><stop offset="0" stop-color="#747475"></stop><stop offset="1" stop-color="#908d8d"></stop></linearGradient></defs><path fill="url(#y)" d="M129 409h0 1v1l-3 8v8c1 4 4 8 4 12-2-5-6-8-8-13v-1l2 2h0v-3c0-3 1-6 2-9 1-2 1-4 2-5z"></path><path d="M137 413c0-1 0-3 1-4l1-1h1 0v3 1 3h1v1l1 3v2h-1c-1-1-2-1-3-3h0c0 2 1 2 2 3 0 2-1 2-2 4-1 3-1 6-2 9v3h-1v-1l-1-4h0v-1-1c-2-5-1-12-1-17v-1c1-1 2-2 2-4 1 1 1 2 1 3l1 2z" class="H"></path><path d="M133 412c1-1 2-2 2-4 1 1 1 2 1 3l1 2c0 1 1 1 1 2v2h-1v-3s-1 0-1-1v-1h-1-1-1z" class="C"></path><path d="M140 401l1-1c1 1 3 1 4 1h0c2 1 3 2 4 4h0l4 4h1c0 1 1 2 1 4v2h-1l-2 2c1 1 1 2 2 2l-2 2h-4-2c-2 0-3-1-4-2l-1-3v-1h-1v-3-1-3h0-1l-1 1c-1 1-1 3-1 4l-1-2c0-1 0-2-1-3 0 2-1 3-2 4v1-1c1-3 2-5 4-7 1-2 2-2 3-4z"></path><path d="M145 401c2 1 3 2 4 4v2l-1-1c0-1-1-1-2-1-1-1-1-2-1-4h0z" class="b"></path><path d="M140 412l1-1h0c1 1 2 3 4 3h2l1 1h-2c1 1 0 1 1 1v1c-1 0-2 1-3 1s-2-1-2-1c0-1-1-1-1-1v-1h-1v-3z" class="L"></path><path d="M138 405c1-1 2-1 2-2l2 1 1 1h-1c-1 2-1 4-2 6v-3h0-1l-1 1c-1 1-1 3-1 4l-1-2c0-1 0-2-1-3l3-3z" class="Q"></path><path d="M138 405h1c0 3-3 3-3 6 0-1 0-2-1-3l3-3z" class="D"></path><path d="M149 405h0l4 4h1c0 1 1 2 1 4v2h-1l-2 2c1 1 1 2 2 2l-2 2h-4-2c-2 0-3-1-4-2l-1-3s1 0 1 1c0 0 1 1 2 1s2-1 3-1v-1c-1 0 0 0-1-1h2l-1-1h0l4-4c0-1-1-2-2-3v-2z" class="N"></path><path d="M151 410l1 1c-1 2-1 3-3 4l-2 2v-1c-1 0 0 0-1-1h2l-1-1h0l4-4zm2-1h1c0 1 1 2 1 4v2h-1v-2c-1 1-1 2-1 3-2 1-3 2-5 3h-1v-1h1c1-1 2-2 3-2 1-2 2-5 2-7z" class="J"></path><path d="M148 419c2-1 3-2 5-3 0-1 0-2 1-3v2l-2 2c1 1 1 2 2 2l-2 2h-4-2c-2 0-3-1-4-2l-1-3s1 0 1 1c0 0 1 1 2 1s2 1 3 1h1z"></path><path d="M148 421c2-1 3-2 4-4 1 1 1 2 2 2l-2 2h-4z" class="D"></path><path d="M136 434c1-3 1-6 2-9 1-2 2-2 2-4-1-1-2-1-2-3h0c1 2 2 2 3 3h1v-2c1 1 2 2 4 2h2 4c1 0 2 0 4 1v3c0 1 1 2 1 3v2c-1 1-3 3-3 4v1 1c-1 2-1 3-2 4h0v-1c-1 1-1 11 0 12v2 1l-1-1h0c0-1 0-1-1-1l-1 1h1v1 1c0 1 1 2 2 3h-1c-1 0-1-1-2-2h0l-1-1c0-1 0-1-1-2l1 1v2l-5-6c0-1-1-2-1-2-1-1-2-3-2-4l-1-2c0-1 0-1-1-2v-1h0l-1-1-1-4z" class="G"></path><path d="M155 424v-1h0v2l1 1v-1c0 1 1 2 1 3v2c-1 1-3 3-3 4v1 1c-1 2-1 3-2 4h0v-1-2-1h-1 0l1-2c0-1 0-3 1-4h0v-1h0c-1 1-2 2-3 2v2h0c-1 1-1 1-1 2v1h-1 0v-2c1-1 1-3 2-4l1-1c1 0 1-1 1-2v-1c1 0 2-1 2-1l1-1z" class="L"></path><path d="M155 424v-1h0v2l1 1v-1c0 1 1 2 1 3-1 0-1 0-2 1v1l-2 2h0c1-2 1-4 1-7l1-1z" class="j"></path><path d="M153 432h0l2-2v-1c1-1 1-1 2-1v2c-1 1-3 3-3 4v1 1c-1 2-1 3-2 4h0v-1-2-1h-1 0l1-2 1-2z" class="E"></path><path d="M142 419c1 1 2 2 4 2h2 4c1 0 2 0 4 1v3 1l-1-1v-2h0v1l-1 1s-1 1-2 1v1c0 1 0 2-1 2l-1 1c-1 1-1 3-2 4v-2l-2 3v1 2l-1 1v-5l2-8v-1-2l-5-2v-2z" class="D"></path><path d="M145 434l2-8-1 2 1 1c1 1 0 3 0 4h-1l-1 1z" class="Q"></path><path d="M142 419c1 1 2 2 4 2h2 4c1 0 2 0 4 1v3 1l-1-1v-2h0v1l-1-1h-1-2c0 1 0 2-1 3 0-1 1-2-1-3h-1v2h-1v-2l-5-2v-2z" class="E"></path><path d="M136 434c1-3 1-6 2-9 1-2 2-2 2-4-1-1-2-1-2-3h0c1 2 2 2 3 3h1l5 2v2 1l-2 8v5 2 6c-2-2-2-4-3-6s-2-3-3-5v-2h-1 0c0-1 0-2-1-2 0 2 0 4 1 7l-1-1-1-4z"></path><path d="M137 432l1-3 2-6h1v5l-3 6h0c0-1 0-2-1-2z" class="B"></path><path d="M180 346c1 1 1 2 1 3l-1 2v1h2v3c1 0 1 1 1 2 1 1 1 1 1 2h0c0 2 1 3 2 4l1 1v-1c-1 0 0 0-1-1v-3h0v-1-1h0v1l2-2 1 3 1-1h1c0 2 1 3 2 4l4 5 1 1c2 3 4 4 8 5l-1 1h-2v1c1 0 2 1 3 1s1 0 1 1h-2 0l6 1c1 0 2-1 4 0h0 4c1 0 1 0 1 1v1c-1 0-1 0-1 1l-1 1c1 0 1 1 2 1 2-1 3-1 4-1h1c1 0 2 0 2-1 2 0 3-1 4-2l1 2c1 0 2 0 3-1l1 1h0l-1 1h1 1v1l-1 1s0 1 1 1h0v1s-1 1-2 1h0c-3 0-5 4-8 4-1 1-2 2-3 2s-3 2-3 2l-1-1-3 2-1-2-3 1-1 1h0-2c-2 2-6 3-9 4h-1c-1 0-1 0-2-1h-3v-1c-3 0-5 1-8 1 0-1-1-1-1-1h-2v-1l-1-1h-2c-2-1-4-3-6-5h-3 0v1l1 2h0l-2-1c-1-2-3-3-4-4h0l-1-1c-1-1-1-2-2-4-1-3-1-5 0-8s3-5 4-7c1-1 2-2 2-3 2-2 4-4 5-6l1-1v-1c-1 1-2 3-4 3l-2 2c0-2 1-3 2-4h0v-1h0l2-3v-1-1l4-4c1-1 2-2 2-3z" class="a"></path><path d="M177 366c1 2 1 5 1 8h0 0c-1 1 0 3-1 5v-11-2z" class="N"></path><path d="M177 379c1-2 0-4 1-5h0c1 2 2 4 3 7l-1 1c-1 0-1 0-2 1 0-2-1-3-1-4z" class="r"></path><path d="M180 382l1-1c2 3 4 5 8 6h1 2 2l-2-1h4c1 0 1 0 2 1v1h1l-2 2c-2 0-4 0-5-1h-2v1c2 0 4 1 5 1-1 1-2 1-2 2h0 0v1l-1-1c-1-1-3-1-5-2s-5-2-6-4h0c-2-1-2-3-3-4 1-1 1-1 2-1z" class="i"></path><path d="M195 388h3 1l-2 2v-1c-1 0-2 0-2-1z" class="K"></path><path d="M192 386h4c1 0 1 0 2 1v1h-3-1 0c-1 0-2 0-4-1h2 2l-2-1z" class="Y"></path><path d="M180 382l1 2c1 1 2 2 3 2 2 1 4 3 5 4-2-1-4-1-6-3-1 0-1 0-2-1v1h0c-2-1-2-3-3-4 1-1 1-1 2-1z" class="f"></path><path d="M214 387h0l-2 2c0 1 0 1 1 1h-1l1 1-1 1h-1l1 1h-2c-2 1-5 2-7 2l1 1 5-1c1 0 1 1 1 1-2 2-6 3-9 4h-1c-1 0-1 0-2-1h-3v-1c-3 0-5 1-8 1 0-1-1-1-1-1h-2v-1l-1-1c1 0 1 0 1-1 1 0 2 0 2-1h-2c-1-1-2-1-3-2h1 1c1 0 2 0 3 1h6l1 1v-1h0 0c0-1 1-1 2-2-1 0-3-1-5-1v-1h2c1 1 3 1 5 1l2-2c1 0 2 0 3-1 2 0 3 0 5 1h2 1c1 0 2-1 4-1z" class="i"></path><path d="M204 396c-1 0-2 1-3 0h0-2c1-1 2-1 3-1h1l1 1z" class="K"></path><path d="M184 397c3 0 6 0 9-1 2 1 4 0 6 1-1 1-3 1-4 1-3 0-5 1-8 1 0-1-1-1-1-1h-2v-1z" class="f"></path><path d="M195 391c2 0 2 0 3 1h1c0 1-1 1-2 1l1 1h1-1l-1 1c-1 1-2 1-4 1-3 1-6 1-9 1l-1-1c1 0 1 0 1-1 1 0 2 0 2-1h-2c-1-1-2-1-3-2h1 1c1 0 2 0 3 1h6l1 1v-1h0 0c0-1 1-1 2-2z" class="m"></path><path d="M195 391c2 0 2 0 3 1h1c0 1-1 1-2 1l1 1h1-1l-1 1h-2c-2 0-8-1-9-2h6l1 1v-1h0 0c0-1 1-1 2-2z" class="K"></path><path d="M214 387h0l-2 2c0 1 0 1 1 1h-1l1 1-1 1h-1c-3 1-7 1-10 2h-2-1l-1-1c1 0 2 0 2-1h-1c-1-1-1-1-3-1-1 0-3-1-5-1v-1h2c1 1 3 1 5 1l2-2c1 0 2 0 3-1 2 0 3 0 5 1h2 1c1 0 2-1 4-1z" class="I"></path><path d="M214 387h0l-2 2c0 1 0 1 1 1h-1c-2 0-5 1-8 1v-1c1 0 2 0 2-1h1l3-1c1 0 2-1 4-1z" class="P"></path><path d="M199 388c1 0 2 0 3-1 2 0 3 0 5 1h2 1l-3 1c-2 0-7 0-8 2 1 0 2-1 2 0v1s-1 0-1 1l1 1h-2-1l-1-1c1 0 2 0 2-1h-1c-1-1-1-1-3-1-1 0-3-1-5-1v-1h2c1 1 3 1 5 1l2-2z" class="Z"></path><defs><linearGradient id="z" x1="205.083" y1="386.668" x2="210.129" y2="374.436" xlink:href="#B"><stop offset="0" stop-color="#8c8a8b"></stop><stop offset="1" stop-color="#b4b2b2"></stop></linearGradient></defs><path fill="url(#z)" d="M215 378h0 4c1 0 1 0 1 1v1c-1 0-1 0-1 1l-1 1-2 1c-1 0-2 0-3 1h2v1c0 1-1 1-1 2h0c-2 0-3 1-4 1h-1-2c-2-1-3-1-5-1-1 1-2 1-3 1h-1v-1c-1-1-1-1-2-1h-4v-1l-2-3c-1 0-2-1-3-2h0c1 0 2 0 3-1h0c1 0 2 0 2-1h0 4c1 1 4 1 6 1-1-1-2 0-3-1 1 0 1 0 2-1 1 1 2 1 4 1v-1l6 1c1 0 2-1 4 0z"></path><path d="M195 383c1 1 2 1 3 1 1 1 2 1 3 1h3c-3 1-6 1-8 1-1-1-1-2-1-3z" class="K"></path><path d="M190 382h1c1 1 3 1 4 1 0 1 0 2 1 3h-4v-1l-2-3z" class="q"></path><path d="M204 379c3 1 7 0 9 1v1c-1 0-2 1-3 1h0c-1 0-2 0-3 1 0-1 0-2-1-3 0 0-1 0-2-1z" class="F"></path><defs><linearGradient id="AA" x1="199.568" y1="389.169" x2="212.915" y2="382.689" xlink:href="#B"><stop offset="0" stop-color="#6b686a"></stop><stop offset="1" stop-color="#878787"></stop></linearGradient></defs><path fill="url(#AA)" d="M205 385c2-1 5-1 7 0h0l1-1h2v1c0 1-1 1-1 2h0c-2 0-3 1-4 1h-1-2c-2-1-3-1-5-1-1 1-2 1-3 1h-1v-1c-1-1-1-1-2-1 2 0 5 0 8-1h1z"></path><path d="M205 385c2-1 5-1 7 0h0 0c-1 0-2 1-3 1 0 0 0-1-1-1h-3z" class="j"></path><defs><linearGradient id="AB" x1="197.023" y1="383.31" x2="200.83" y2="377.56" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#898889"></stop></linearGradient></defs><path fill="url(#AB)" d="M192 378h4c1 1 4 1 6 1h2c1 1 2 1 2 1 1 1 1 2 1 3l-12-1-3-1-1 1h-1c-1 0-2-1-3-2h0c1 0 2 0 3-1h0c1 0 2 0 2-1h0z"></path><path d="M190 379c1 0 1 0 2 1h0c1 1 2 2 3 2l-3-1-1 1h-1c-1 0-2-1-3-2h0c1 0 2 0 3-1z" class="K"></path><path d="M231 379l1 2c1 0 2 0 3-1l1 1h0l-1 1h1 1v1l-1 1s0 1 1 1h0v1s-1 1-2 1h0c-3 0-5 4-8 4-1 1-2 2-3 2s-3 2-3 2l-1-1-3 2-1-2-3 1-1 1h0-2s0-1-1-1l-5 1-1-1c2 0 5-1 7-2h2l-1-1h1l1-1-1-1h1c-1 0-1 0-1-1l2-2h0 0c0-1 1-1 1-2v-1h-2c1-1 2-1 3-1l2-1c1 0 1 1 2 1 2-1 3-1 4-1h1c1 0 2 0 2-1 2 0 3-1 4-2z" class="E"></path><path d="M228 385l-1-1 1-1 1-1c2-1 3-1 5 0-2 2-4 3-6 3z" class="M"></path><path d="M220 383c2-1 3-1 4-1h1c-2 1-4 2-5 3-2 1-4 1-6 2h0 0c0-1 1-1 1-2v-1h-2c1-1 2-1 3-1l2-1c1 0 1 1 2 1z" class="j"></path><path d="M218 382c1 0 1 1 2 1h0c-1 0-2 1-3 1l-1-1 2-1zm-5 8c2 0 4-1 6-2 1 0 3-2 4-1h0l-5 4h0 1v-1h2l1 1-4 1h-1c-1 1-2 1-3 0 0 1-1 2-1 3l-1 1h0-2s0-1-1-1l-5 1-1-1c2 0 5-1 7-2h2l-1-1h1l1-1-1-1h1z" class="F"></path><path d="M209 395c1 0 2-1 3-1v2h0-2s0-1-1-1z" class="j"></path><path d="M234 382h1 1 1v1l-1 1s0 1 1 1h0v1s-1 1-2 1h0c-3 0-5 4-8 4-1 1-2 2-3 2s-3 2-3 2l-1-1-3 2-1-2-3 1c0-1 1-2 1-3 1 1 2 1 3 0h1l4-1-1-1c2-2 4-2 6-4 0 0 1 0 1-1 2 0 4-1 6-3z" class="O"></path><path d="M227 386l2 1-6 3-1 1-1-1c2-2 4-2 6-4z" class="Q"></path><path d="M234 382h1 1 1v1l-1 1s0 1 1 1h0l-2 1-1-1c-2 0-3 2-5 2l-2-1s1 0 1-1c2 0 4-1 6-3z" class="e"></path><path d="M218 392v1h3c2 0 4-2 6-2-1 1-2 2-3 2s-3 2-3 2l-1-1-3 2-1-2-3 1c0-1 1-2 1-3 1 1 2 1 3 0h1z" class="I"></path><path d="M216 394s1-1 2-1 1 1 2 1 2-1 4-1c-1 0-3 2-3 2l-1-1-3 2-1-2z" class="Z"></path><path d="M168 369l1 1 1 1c1-1 1-2 2-3s2-3 3-4c-1 7-1 14 1 20v1 1c0 2 3 5 5 6 1 1 2 1 3 2h2c0 1-1 1-2 1 0 1 0 1-1 1h-2c-2-1-4-3-6-5h-3 0v1l1 2h0l-2-1c-1-2-3-3-4-4h0l-1-1c-1-1-1-2-2-4-1-3-1-5 0-8s3-5 4-7z" class="s"></path><path d="M169 387c-1-1-1-1-1-2h1 0 0l1-1h-1c0-3-1-6 0-9v3 2l1 1v-4-1c0 2 0 4 1 6v1s1 2 1 3h-1v2s-1 0-1-1l-1-1h0v1z" class="k"></path><path d="M170 387s1 0 1-1c-1-1-1-2-1-4l1 1s1 2 1 3h-1v2s-1 0-1-1z" class="h"></path><path d="M170 387c0 1 1 1 1 1v-2h1l1 2 2 3h-3 0v1l1 2h0l-2-1c-1-2-3-3-4-4 1 0 1 0 2 1l1 1c0-2-1-3-1-4v-1h0l1 1z" class="l"></path><path d="M173 388c1 0 1-1 1-1 0-1 0-1-1-2-1-3-1-5-1-8 0 1 0 1 1 2h0c1 2 1 4 2 6l1-1v1 1c0 2 3 5 5 6 1 1 2 1 3 2h2c0 1-1 1-2 1 0 1 0 1-1 1h-2c-2-1-4-3-6-5l-2-3z" class="k"></path><path d="M180 346c1 1 1 2 1 3l-1 2v1h2v3c0 3 0 6 1 10h-1 0c0 1 0 2-1 3h0v1l3 3 2 2c2 2 4 3 6 4h0c0 1-1 1-2 1h0c-1 1-2 1-3 1h0c1 1 2 2 3 2l2 3v1l2 1h-2-2-1c-4-1-6-3-8-6-1-3-2-5-3-7h0c0-3 0-6-1-8v2c-1-2 0-4-1-6 0-1 0-1-1-2l1-1v-1c-1 1-2 3-4 3l-2 2c0-2 1-3 2-4h0v-1h0l2-3v-1-1l4-4c1-1 2-2 2-3z" class="W"></path><path d="M176 359c1 0 1-1 2-1v2c-1 2-1 4-1 6v2c-1-2 0-4-1-6 0-1 0-1-1-2l1-1z" class="h"></path><path d="M181 368v1l3 3-1 2-2-2-2-2 1-1h1v-1z" class="V"></path><path d="M192 385c-2 0-4 0-5-1 0-1-1-1-1-2-1 0-1 0-1-1-1 0-2-1-2-2 1 1 2 1 3 1h1c1 1 2 2 3 2l2 3z" class="c"></path><path d="M184 372l2 2c2 2 4 3 6 4h0c0 1-1 1-2 1h0c-1 1-2 1-3 1h0-1l-3-4h0c-2-1-2-2-2-4l2 2 1-2z" class="g"></path><path d="M181 372l2 2c1 1 2 2 3 2l-1 1h0c-1-1-1-1-2-1h0c-2-1-2-2-2-4z" class="N"></path><path d="M180 352h2v3c0 3 0 6 1 10h-1 0c0 1 0 2-1 3-1 0-1-1-2-1v-2c1-2 1-5 1-8l-1-1h1v-1h0v-1l-2 1c0-1 1-2 2-3z" class="V"></path><path d="M180 346c1 1 1 2 1 3l-1 2v1c-1 1-2 2-2 3l-6 6-2 2c0-2 1-3 2-4h0v-1h0l2-3v-1-1l4-4c1-1 2-2 2-3z" class="F"></path><defs><linearGradient id="AC" x1="172.293" y1="357.741" x2="178.461" y2="354.603" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#747475"></stop></linearGradient></defs><path fill="url(#AC)" d="M180 351v1c-1 1-2 2-2 3l-6 6-2 2c0-2 1-3 2-4h0v-1c3-2 5-5 8-7z"></path><path d="M182 355c1 0 1 1 1 2 1 1 1 1 1 2h0c0 2 1 3 2 4l1 1v-1c-1 0 0 0-1-1v-3h0v-1-1h0v1l2-2 1 3 1-1h1c0 2 1 3 2 4l4 5 1 1c2 3 4 4 8 5l-1 1h-2v1c1 0 2 1 3 1s1 0 1 1h-2 0v1c-2 0-3 0-4-1-1 1-1 1-2 1 1 1 2 0 3 1-2 0-5 0-6-1h-4c-2-1-4-2-6-4l-2-2-3-3v-1h0c1-1 1-2 1-3h0 1c-1-4-1-7-1-10z" class="b"></path><path d="M193 362l4 5 1 1c2 3 4 4 8 5l-1 1h-2v1c1 0 2 1 3 1s1 0 1 1h-2l-7-1h5c0-1 0-1-1-1-1-1-1-2-2-3h0c-3-2-6-7-7-10z" class="N"></path><path d="M181 368c1-1 1-2 1-3h0 1l3 3h0c1 2 2 3 4 4s5 4 8 4l7 1h0v1c-2 0-3 0-4-1-1 1-1 1-2 1 1 1 2 0 3 1-2 0-5 0-6-1h-4c-2-1-4-2-6-4l-2-2-3-3v-1h0z" class="F"></path><path d="M186 374c2 0 2 0 4 1 1 1 4 2 6 2l1 1h-1-4c-2-1-4-2-6-4z" class="I"></path><path d="M181 368c1-1 1-2 1-3h0 1l3 3h0c1 2 2 3 4 4h-4l-1-1-1 1-3-3v-1h0z" class="g"></path><path d="M181 368c1-1 1-2 1-3h0 1l3 3-1 1-1 1-2-2-1 1v-1h0z" class="Y"></path><defs><linearGradient id="AD" x1="190.931" y1="373.989" x2="189.418" y2="355.962" xlink:href="#B"><stop offset="0" stop-color="#0f0e10"></stop><stop offset="1" stop-color="#323231"></stop></linearGradient></defs><path fill="url(#AD)" d="M182 355c1 0 1 1 1 2 1 1 1 1 1 2h0c0 2 1 3 2 4l1 1v-1c-1 0 0 0-1-1v-3h0v-1-1h0v1l2-2 1 3c1 2 4 6 3 8h1c1 2 2 3 4 5h2v2h-2c-2-1-3-1-5-2-1-1-3-2-4-3s-1-1-2-1h0l-3-3c-1-4-1-7-1-10z"></path><path d="M188 356l1 3c1 2 4 6 3 8l-6-9 2-2z" class="Y"></path><path d="M303 206v-34-8c0-1 0-3-1-4h-40c-24 1-49 2-71 14-3 1-6 3-9 5a30.44 30.44 0 0 0-8 8c-5 4-8 8-11 13-10 16-15 36-16 55h-36l6-174h20 1c4 5 10 8 16 11 15 7 31 10 48 11h59 106 73c15 0 30 0 46-1 20-2 39-4 58-12 2-1 5-2 7-4 1 0 4-4 5-4 2 0 5 0 7 1h7c1 0 3-1 4 0l12 172h-38c0-12-2-24-6-35l-3-9c-2-4-3-8-5-12-4-7-10-15-17-20-13-11-30-17-46-19-10-1-21 0-31 0h-38v178c-2-1-3-1-5-2V155h41c11 0 22 0 33 1 20 2 39 9 53 24 6 5 10 11 14 18 8 17 13 34 14 53h29L569 87h-9-3c-1 0-4 3-5 4-3 1-5 3-8 4-16 7-34 10-51 11l-50 1h-59l-165 1c-12 0-23 0-35-2-8-1-16-4-24-7-6-2-13-6-19-9l-3-3c-1 0-2-1-2-1-1-1-3 0-4 0h-11l-5 165h27c2-20 7-40 18-57 5-6 11-12 17-17 5-4 9-6 15-9 24-12 53-12 80-12h34v47c2-2 4-4 6-5v-48c-28 0-56-1-83 2-10 1-20 2-29 6-13 4-25 12-35 22-13 13-21 31-25 49-1 5-3 10-3 16h-17l6-154c2 0 5 0 8 1s7 5 10 6c13 8 27 12 42 14 19 2 38 1 56 1h86 101c25 0 51 1 77-2 14-2 30-6 43-13 2-1 5-3 7-5 2-1 6-1 8 0l9 152h-17c-2-16-5-31-12-45-2-4-4-8-7-12-2-3-4-6-7-9a57.31 57.31 0 0 0-11-11c-7-5-14-9-22-12-21-7-43-6-65-6h-42v182c-10-4-20-5-30-4-3 0-6 1-8 1h0c-1-2 0-4 1-5h-1c-1 1-2 1-4 1h-1c-3 0-6 0-9 1h-1c-1 0-1 1-2 1h0l-1-1h-1c0-1 0-1-1-2-1 0 0 0-1-1 1 0 1 0 1-1v-1l-2 1h-1l1-1c1-1 0-2 0-2v-1l1-1v-1-1h-1c1-1 1-1 1-2h0l-1-1h0-2c0-2-1-3-1-4h-1-1c-2-2 0-2-1-5 0 0-1 0-1-1 0 0 1-1 1-2v-3h0l1-1 1-1v-1c-1-1-2-1-2-2 0-2 0-2 1-3h-1-2-1c0-1-1-1-2-2 1 2 1 3 2 4-1 0-1 1-1 1v1c-1-1-1-1-1-2-1 0-1 1-2 1h0c0-1 1-2 1-2v-1-2c0-1-1-2-1-3-1-1 0-1-1-2l-1-1c0-1 1-2-1-3h0c-1 1-2 2-2 3v2c-1-2-2-4-3-5v-1l2 1h1 0v-2c0-2 1-6-1-7v-2l1-1c-1 0-1 0-2-1l-1 1h0v-1c1-1 1-2 1-3v-1c0-3-1-4-2-6 0-1 1-2 2-2l-1-2v-1l-2-2-2-1c-2-3-5-7-9-8l-2-2-3-3h0c2 0 3 1 5 1l-2-1c-2-2-5-4-9-5-2-1-6-3-8-5-1-1-3-2-5-2 4 0 6 0 9-2h0c3-2 6-3 9-5 1-1 3-2 5-3l1-1 5-3c0-1 0-1 1-2z"></path><path d="M361 263l1 1-3 2 1-1h-3l4-2z" class="W"></path><path d="M353 275c2 0 3 0 4-1l1 2-3 2c0-1 0-1-1-2l-1-1z" class="I"></path><path d="M342 254h0v-4c1 0 1 0 2 1v3h-2z" class="m"></path><path d="M332 237s1 0 2 1 4 1 5 2c-1 0-4 0-6 1l-2-2h0 2l1-1-2-1zm29 26c2-2 6-4 9-4-2 1-4 2-6 4 0 0-1 1-2 1l-1-1z" class="S"></path><path d="M344 174v4l-2 4h-1v-1c0-1 0-2 1-3v-2c1 0 1 0 2-2z" class="e"></path><path d="M352 319c2-1 5-1 7-2l3 1c-1 0-1 0-1 1-2 0-6 0-8 1v1c-1-1-1-1-1-2z" class="g"></path><path d="M357 274l11-1c1-1 4-1 5-1h-1l-14 4-1-2z" class="T"></path><path d="M341 192h-2c1-1 1-1 2-1v-2c0-1 1-2 2-2 1 1 2 1 4 1v1l-6 3z" class="s"></path><path d="M351 272h0l1 1h1 0l-1 1 1 1 1 1c1 1 1 1 1 2h-1l1 1v1h0c-2-2-4-2-6-2h0 2v-1c1-1 1-2 1-3h-1-1l1-2z" class="J"></path><path d="M359 317c6 0 13-1 18 0l-16 2c0-1 0-1 1-1l-3-1z" class="Y"></path><path d="M332 248c2 2 3 4 4 7h1c0-1 0-2 1-2 0 1 0 2 1 3v1l-1-1c-1 1-1 2-1 3h-1c0-1-1-3-1-4l-2-4-1-1v-2z" class="r"></path><path d="M357 265h3l-1 1c-2 2-4 3-6 5h0l-1-1c-1 0-1 1-2 1v-3h-1l-2-2 1-1v2h3v2c2-1 4-3 6-4z" class="V"></path><path d="M332 223c0-1 0-1 1-2h0l1-1v-3h0l3-3v3c-2 1-3 3-3 5h0c2 0 3-1 5-2-2 3-4 5-6 7l5-1c-1 1-3 2-4 3h0l-2-2c0-1 1-1 1-2l-1-2z" class="Y"></path><path d="M342 254h2v1 6h0l2 2 1 1h-1c-2 1-2 0-3-1-1 0 0-1-1-2h-1c0-1 0-2-1-2h-1v-2-1l1 1c1-1 1-2 2-3z" class="b"></path><path d="M335 231c1 1 2 1 3 2h1v2 1l-1-1c-3 0-5 1-8 1-1 0 0 0-1 1l1 1h0 0-3c0-1 1-1 1-2l-1-1h-1v-1h2 1v1c1 0 1 0 2-1h0 0c1-2 3-2 4-3z" class="T"></path><path d="M335 231c1 1 2 1 3 2h1v2c-3-1-5-2-8-1h0c1-2 3-2 4-3z" class="s"></path><path d="M347 301h1c2 0 4-3 6-4l1-3c0-1 0-1 1-1-1 4-1 7-5 10l-1 1c-1 1-3 1-5 0-1-1-1-1-1-2l3-1z" class="D"></path><path d="M353 321h1 6l9-1c1-1 3-1 5-1-3 1-5 2-8 2l-12 2v1h-1c-1 1-2 1-4 1h-1c-1-1-2-1-3-2h2 3c1-1 2-1 3-2z" class="i"></path><path d="M351 285l-1-2 1-1c1 1 2 4 2 5 0 3 0 7-2 9-1 1-2 2-4 3h-1c-2 0-2-1-3-2l1-1c1 0 2 0 3-1h-1 1l1-1c1-2 1-5 1-7 1 0 1-1 2-2z" class="L"></path><path d="M347 295h2l2-2c-1 2-2 4-4 5v1h-1c-2 0-2-1-3-2l1-1c1 0 2 0 3-1h-1 1z" class="N"></path><path d="M351 285v8h0l-2 2h-2l1-1c1-2 1-5 1-7 1 0 1-1 2-2z" class="l"></path><path d="M321 236l1-4v-2h1v2l1 1h3 1v1h-2v1h1l1 1c0 1-1 1-1 2h3 0 0l-1-1c1-1 0-1 1-1l1 1h1l2 1-1 1h-2 0l2 2c-1 0-2 0-3 1l-1-1c-1 1-1 1-2 1l-1 2-1-2-1-2h-1 0l-2-4z" class="K"></path><path d="M326 238h0v1c0 1 0 2 1 3h0l-1 2-1-2-1-2h0c1 0 1-1 2-2z" class="Y"></path><path d="M327 242v-3c2 1 2 1 4 0h0l2 2c-1 0-2 0-3 1l-1-1c-1 1-1 1-2 1h0z" class="Z"></path><path d="M321 236l1-4v-2h1v2l1 1h3l-1 1h-2v1h1v1c-1 0-1 0-1 1l2 1c-1 1-1 2-2 2h0-1 0l-2-4z" class="a"></path><path d="M334 199h1 1 0l2-1h1 1v2h0l2-1v1c-1 1-3 2-3 4-1 1-2 3-3 4v1h0v-1c-2 0-2 1-4 1h-2l1-2h-1v-1c1-1 2-3 2-5h0l2-2z" class="T"></path><path d="M341 301c2 1 4 0 6 0l-3 1c0 1 0 1 1 2 2 1 4 1 5 0l1 1 1-1c1 1 0 4 0 5h0c-1-1-1-1-2-1h0l1 1v2h-2v1h1c1 0 1 1 2 1v1 2h0-1 0c-1-1-2-3-3-4v1h-1c-3-4-5-7-6-12z" class="h"></path><path d="M345 283h1c2 0 2 3 3 4 0 2 0 5-1 7l-1 1h-1 1c-1 1-2 1-3 1l-1 1c-1-2-1-3-2-5h0c0-2 0-3 1-4v-2c1-2 1-2 3-3z" class="b"></path><path d="M341 292h0c0-2 0-3 1-4v1c1 0 2 0 3 1-1 0-2 1-3 1l-1 1z" class="n"></path><path d="M346 290l1 1v1l-1 1v2h0 1c-1 1-2 1-3 1l-1 1c-1-2-1-3-2-5l1-1c1 0 2-1 3-1h1z" class="W"></path><path d="M345 283h1c2 0 2 3 3 4 0 2 0 5-1 7l-1 1h-1 0v-2l1-1v-1l-1-1c0-2-1-5-1-7z" class="B"></path><path d="M336 209c1-1 2-1 3-2v1l-3 3h1c1 0 2-1 2-1h1l-2 2c0 1 0 1-1 2l-3 3h0v3l-1 1h0c-1 1-1 1-1 2h-1 0c-1-1-1-1-2-1v-1h-1v-1h-1v-1l1-1h-1v-1l1-1-2-1c1 0 2-1 3-1 0-1-1-1-2-1 1-1 2-1 2-2s0-1 1-2h2c2 0 2-1 4-1v1h0z" class="F"></path><path d="M329 221c1-1 2-2 3-2h0c0 1 0 3-1 4h0c-1-1-1-1-2-1v-1z" class="P"></path><path d="M333 213h2v1l-2 1c0 1 1 1 0 2h-1-1v-1c1 0 1 0 2-1h-2v-1l2-1z" class="L"></path><path d="M336 209c1-1 2-1 3-2v1l-3 3c-1 0-1 0-2 1h-1-2c0-1 1-2 1-3 2 0 2-1 4-1v1h0z" class="e"></path><path d="M323 214l1 2h1s0-1 1-1l2 1-1 1v1h1l-1 1v1h1v1h1v1c1 0 1 0 2 1h0 1l1 2c0 1-1 1-1 2l2 2-3 1v1h4c-1 1-3 1-4 3h0 0c-1 1-1 1-2 1v-1h-1v-1h-1-3l-1-1v-2h-1v2l-1 4v-6c0-5 1-10 2-16z" class="S"></path><path d="M327 220h1v1c-1 1-1 1-1 2v1 1c0 2-1 2-1 3v1c0 1 0 1 1 1h1 3v1h4c-1 1-3 1-4 3h0 0c-1 1-1 1-2 1v-1h-1v-1h-1-3v-1h2v-1c-1 0-1 0-1-1v-1l-1-1 1-1 1-1h0-1v-3c0-1 1-1 2-2v-1z" class="j"></path><path d="M328 221h1v1c1 0 1 0 2 1h0 1l1 2c0 1-1 1-1 2l2 2-3 1h-3-1c-1 0-1 0-1-1v-1c0-1 1-1 1-3v-1-1c0-1 0-1 1-2z" class="I"></path><path d="M328 230l-1-1c2-2 3-1 5-2l2 2-3 1h-3z" class="Q"></path><path d="M330 195h0 2l1 2c0 1-1 1-1 2h2l-2 2h0c0 2-1 4-2 5v1h1l-1 2c-1 1-1 1-1 2s-1 1-2 2c1 0 2 0 2 1-1 0-2 1-3 1s-1 1-1 1h-1l-1-2c-1 6-2 11-2 16h-1-1l1-5c-1-10 5-21 8-30 0 1 0 2 1 3l1-3z" class="E"></path><defs><linearGradient id="AE" x1="323.79" y1="208.714" x2="326.224" y2="215.187" xlink:href="#B"><stop offset="0" stop-color="#211d22"></stop><stop offset="1" stop-color="#353635"></stop></linearGradient></defs><path fill="url(#AE)" d="M326 207c1 1 1 2 2 3v1h1c0 1-1 1-2 2 1 0 2 0 2 1-1 0-2 1-3 1s-1 1-1 1h-1l-1-2c2-2 2-5 3-7z"></path><path d="M330 195h0 2l1 2c0 1-1 1-1 2h2l-2 2h0c0 2-1 4-2 5v1h1l-1 2c-1 1-1 1-1 2h-1v-1c-1-1-1-2-2-3l3-9 1-3z" class="h"></path><path d="M335 180l4-4c2-1 3-2 5-2-1 2-1 2-2 2v2c-1 1-1 2-1 3v1h1v1h3l-1 1c-1 0-2 1-3 2 1 1 2 0 3 0l-1 1c-1 0-2 1-2 2v2c-1 0-1 0-2 1h2c0 1-1 1-2 2h2l-1 2h0c0 1 0 1-1 2h0-1l-2 1h0-1-1-2c0-1 1-1 1-2l-1-2h-2 0l-1 3c-1-1-1-2-1-3l4-9 1-3c1-1 1-2 2-3z" class="F"></path><path d="M333 192c2 0 3-1 5-1-2 1-3 2-5 2v1c1 1 1 0 2 0-1 1-1 1-2 1h-1 0-2 0c0-1 1-2 1-3h1 1z" class="a"></path><path d="M335 184c1 1 1 1 1 2v1h2c-1 1-3 1-3 2h2c-1 1-2 2-4 3h-1-1l2-3h0l2-5z"></path><path d="M335 180l4-4c2-1 3-2 5-2-1 2-1 2-2 2v2c-1 1-1 2-1 3-1 2-2 3-3 4h-1l-1 1c0-1 0-1-1-2l-2 5h0l-2 3c0 1-1 2-1 3l-1 3c-1-1-1-2-1-3l4-9 1-3c1-1 1-2 2-3z" class="B"></path><path d="M332 186v1c0 2-2 4-2 6v-1l1-2c0-1 1-1 2-1l-2 3c0 1-1 2-1 3l-1 3c-1-1-1-2-1-3l4-9z" class="D"></path><path d="M342 176v2c-1 1-1 2-1 3-1 2-2 3-3 4h-1l-1 1c0-1 0-1-1-2 1-1 2-2 3-4 1-1 2-3 4-4z" class="p"></path><path d="M338 180c0 2 0 3-1 4v1l-1 1c0-1 0-1-1-2 1-1 2-2 3-4z" class="h"></path><path d="M333 251l2 4c0 1 1 3 1 4h1c0-1 0-2 1-3l1 1v2h1c1 0 1 1 1 2h1c1 1 0 2 1 2 1 1 1 2 3 1h1c1-3 3-5 5-7 1-1 2-3 3-4 0 1-2 3-2 4l-5 8-1 1 2 2h1v3c1 0 1-1 2-1l1 1-2 1h0l-1 2h1 1c0 1 0 2-1 3v1h-2 0c-2 0-3 1-4 1-2 1-3 1-4 2l-1 2v-3l-1-1h0c-1-1 0-2 0-4v-5-2l-1-1v-2h-1c0 1-1 1-2 2h0 0c0-2 0-4-1-6v-1l-3-6c1-1 1-1 1-2l1-1z" class="P"></path><path d="M337 262h1l2-2v1c0 2 0 2 1 3h-1l-1 1v-1c-1 0-2 0-3-1l1-1z" class="E"></path><path d="M339 264v-3h1c0 2 0 2 1 3h-1l-1 1v-1z" class="L"></path><path d="M339 265l1-1h1c0 1 1 1 2 2h0 2 1v2h2v1h0l2 2c1 0 1-1 2-1l1 1-2 1h0 0-1-1 0c0-1 0-1-1-2-1 0-2-1-3-2h0v-1h-1l-1 1h-2v3c0-1-1-1-1-1v-1c1 0 1-1 1-1l-2-1v-2z" class="D"></path><path d="M341 271v-3h2l1-1h1v1h0c1 1 2 2 3 2 1 1 1 1 1 2h0 1 1 0l-1 2h1 1c0 1 0 2-1 3v1h-2 0c-2 0-3 1-4 1h-2c0-1 1-1 1-1l1-1v-1c0-1-1-1-1-2v-1c0-1-1-2-2-2h-1z" class="C"></path><path d="M333 251l2 4c0 1 1 3 1 4h-1v1h1 0c1 1 1 1 1 2l-1 1c1 1 2 1 3 1v1 2l2 1s0 1-1 1v1s1 0 1 1h1c1 0 2 1 2 2v1c0 1 1 1 1 2v1l-1 1s-1 0-1 1h2c-2 1-3 1-4 2l-1 2v-3l-1-1h0c-1-1 0-2 0-4v-5-2l-1-1v-2h-1c0 1-1 1-2 2h0 0c0-2 0-4-1-6v-1l-3-6c1-1 1-1 1-2l1-1z" class="F"></path><path d="M342 271c1 0 2 1 2 2v1c0 1 1 1 1 2-1 0-2-2-3-3v-2z" class="D"></path><path d="M340 280c0-2 0-2 1-4h1 1c0 2-2 3-2 5l-1 2v-3z" class="p"></path><path d="M336 281h0v-6l1-1h1l1 1c0 2-1 3 0 4h0l1 1v3c-2 3-2 6-1 10l2 8c1 5 3 8 6 12h1l4 6c0 1 0 1 1 2h0c-1 1-2 1-3 2h-3-2c1 1 2 1 3 2-3 0-6 0-9 1h-1c-1 0-1 1-2 1h0l-1-1h-1c0-1 0-1-1-2-1 0 0 0-1-1 1 0 1 0 1-1v-1l-2 1h-1l1-1c1-1 0-2 0-2v-1l1-1v-1-1h-1c1-1 1-1 1-2h0l-1-1h0-2c0-2-1-3-1-4h-1-1c-2-2 0-2-1-5 0 0-1 0-1-1 0 0 1-1 1-2v-3h0l1-1 1-1v-1c-1-1-2-1-2-2 0-2 0-2 1-3 0 1 1 2 2 2s1 0 1-1l-1-1h0l2-2 1 1v-3h1v3h1c0-1 2-2 2-3s0-2 1-3v-1z" class="U"></path><path d="M342 321h0 5c-1-1-1-2-2-2h0c-1-1-2-1-2-2h0 1c0 1 1 1 1 1l1 1h1s1 1 1 2l-1 2h-2 0c-2 0-2-1-3-2z" class="G"></path><path d="M336 281h0v-6l1-1h1l1 1c0 2-1 3 0 4h0l1 1v3c-2 3-2 6-1 10-2-2-1-4-1-6v-7c-1 0-1 1-2 1z" class="J"></path><path d="M332 316c1 1 2 2 2 3l2-2v-1l2 2v1l-1-1c-1 1-1 1-1 2s1 1 1 2h1c1-1 2-1 4-1 1 1 1 2 3 2h0c1 1 2 1 3 2-3 0-6 0-9 1h-1c-1 0-1 1-2 1h0l-1-1h-1c0-1 0-1-1-2-1 0 0 0-1-1 1 0 1 0 1-1v-1l-2 1h-1l1-1c1-1 0-2 0-2v-1l1-1v-1z" class="K"></path><path d="M338 322c1-1 2-1 4-1 1 1 1 2 3 2-2 0-4 0-5 1-1-1-1-2-2-2z" class="L"></path><path d="M332 316c1 1 2 2 2 3l2-2v-1l2 2v1l-1-1c-1 1-1 1-1 2s1 1 1 2h1c1 0 1 1 2 2h-3-1v1h-1c0-1 1-1 1-3h-1l-1 1v-1c0-1 1-1 1-2l-1-1-1 1h-1v-3-1z" class="O"></path><path d="M331 285h1v3h1c0-1 2-2 2-3s0-2 1-3v-1c0 3-1 5-1 8 0 2 0 5-1 7 0 1-1 2-1 3s-1 2-1 2l-1 1 1 1c1 1 1 2 2 4s1 5 2 7v1h0l-1-1h-1l-1 1c1 0 1 1 1 2h2l-2 2c0-1-1-2-2-3v-1h-1c1-1 1-1 1-2h0l-1-1h0-2c0-2-1-3-1-4h-1-1c-2-2 0-2-1-5 0 0-1 0-1-1 0 0 1-1 1-2v-3h0l1-1 1-1v-1c-1-1-2-1-2-2 0-2 0-2 1-3 0 1 1 2 2 2s1 0 1-1l-1-1h0l2-2 1 1v-3z" class="C"></path><path d="M331 312c0-1 0-1 1-2 1 0 1 0 1-1 1 1 1 2 1 3-1 1-1 1-2 1l-1-1zm-1-8h1l1 2c1 1 1 1 0 2l-1 1v-1c-2 0-2-1-2-2s0-1 1-1v-1z" class="U"></path><path d="M331 285h1v3h1c0-1 2-2 2-3s0-2 1-3v-1c0 3-1 5-1 8 0 2 0 5-1 7v-1c-1-1-1-1-1-2v-1l-1 1c0 2 0 3-1 4l-1 1v-1c0-1-1-1-2-2v1 2h-2c-1 1-1 1-1 2v-3h0l1-1 1-1v-1c-1-1-2-1-2-2 0-2 0-2 1-3 0 1 1 2 2 2s1 0 1-1l-1-1h0l2-2 1 1v-3z" class="D"></path><path d="M331 285h1v3h1c0-1 2-2 2-3s0-2 1-3v-1c0 3-1 5-1 8h0l-1 1c0 1 0 1-1 1 0-1 0-1-1-1v2l-1-1v-1-2-3z" class="O"></path><path d="M327 242c1 0 1 0 2-1l1 1c-1 1-1 2 0 3 0 1 1 2 2 3h0v2l1 1-1 1c0 1 0 1-1 2l3 6v1c1 2 1 4 1 6h0 0c1-1 2-1 2-2h1v2l1 1v2 5l-1-1h-1l-1 1v6h0v1c-1 1-1 2-1 3s-2 2-2 3h-1v-3h-1v3l-1-1-2 2h0l1 1c0 1 0 1-1 1s-2-1-2-2h-1-2-1c0-1-1-1-2-2 1 2 1 3 2 4-1 0-1 1-1 1v1c-1-1-1-1-1-2-1 0-1 1-2 1h0c0-1 1-2 1-2v-1-2c0-1-1-2-1-3-1-1 0-1-1-2l-1-1c0-1 1-2-1-3h0c-1 1-2 2-2 3v2c-1-2-2-4-3-5v-1l2 1h1 0v-2c0-2 1-6-1-7v-2l1-1c-1 0-1 0-2-1l-1 1h0v-1c1-1 1-2 1-3v-1c0-3-1-4-2-6 0-1 1-2 2-2l-1-2v-1h1v-2s1-1 2-1c0 1 1 2 2 2 0 1 1 1 2 1l-1-1 1-1 2 3c1 1 2 2 3 2l-1-2c1 0 1 0 2-1v-1-1c0-2-1-3-1-4l3 1-2-2 2-1 1 2 1-2z" class="W"></path><path d="M325 260l2 4-1 1c-1-1-1-2-2-2v-2h1v-1z" class="c"></path><path d="M323 256l2 4v1h-1l-1-1-1-2v-1l1-1z" class="i"></path><path d="M328 275v-3c1 1 1 2 1 2v-3h2v4c1 1 1 2 0 2 0 1-1 1-2 1h-1v-3z" class="V"></path><path d="M324 263c1 0 1 1 2 2l1-1c1 1 1 3 2 4v3c-1 1-1 1-1 3h0c-1 1-1 2-1 2h-2v-2s-1-1-1-2c1 1 1 1 2 1v-3c0-1-1-3-1-5l-1-2z" class="r"></path><path d="M328 260s0-1 1-1h1 0c0 1 1 3 2 4 0 3 0 5 1 7-1 1 0 3 0 4-1 0-1 1-2 1v-4l-2-7-1-4z" class="i"></path><path d="M328 260s0-1 1-1h1 0v5h-1l-1-4z" class="Z"></path><path d="M331 275c1 0 1-1 2-1v6l1 1v3l-1 1v-2h-1l-1 2v3l-1-1c0-1-1-2-1-4-2-1-2-2-4-3 0-1-1-1-2-2v-1c0-1 0-1-1-2l3-1v2h2s0-1 1-2h0v1 3h1c1 0 2 0 2-1 1 0 1-1 0-2z" class="j"></path><path d="M331 275c1 0 1-1 2-1v6c-1 0-1 0-1 1-2 0-3 0-4-1v-3h0c-1 0-1 0-2 1l-1-1v-1h2s0-1 1-2h0v1 3h1c1 0 2 0 2-1 1 0 1-1 0-2z" class="q"></path><path d="M319 261h1v-1-1h1v1l1-1 1 1 1 1v2l1 2c0 2 1 4 1 5v3c-1 0-1 0-2-1 0 1 1 2 1 2l-3 1c0-1 0-1-1-2s-1-2-1-3l-1-2h1v-3l-1-4z" class="W"></path><path d="M320 265c1 1 1 2 2 3v2l-1 1c-1 0-1 0-1-1l-1-2h1v-3z" class="m"></path><path d="M322 270l1-1 1 3c0 1 1 2 1 2l-3 1c0-1 0-1-1-2s-1-2-1-3c0 1 0 1 1 1l1-1z" class="o"></path><path d="M335 267c1-1 2-1 2-2h1v2l1 1v2 5l-1-1h-1l-1 1v6h0v1c-1 1-1 2-1 3s-2 2-2 3h-1v-3h-1l1-2h1v2l1-1v-3l-1-1v-6c0-1-1-3 0-4 1 0 1-2 1-3h1 0z" class="R"></path><path d="M338 267l1 1v2c-1 0-1 1-2 2 0-2 0-3 1-5z" class="q"></path><path d="M335 267h0c1 3 1 5 1 8-2 2-1 3-2 5v-4c1-1 1-2 1-2-1-1-1-2-1-3 1-1 0-3 0-4h1z" class="o"></path><g class="N"><path d="M333 270c1 0 1-2 1-3 0 1 1 3 0 4 0 1 0 2 1 3 0 0 0 1-1 2v4 1l-1-1v-6c0-1-1-3 0-4z"></path><path d="M327 242c1 0 1 0 2-1l1 1c-1 1-1 2 0 3 0 1 1 2 2 3h0v2l1 1-1 1c0 1 0 1-1 2l3 6v1c1 2 1 4 1 6h0-1c0 1 0 3-1 3-1-2-1-4-1-7-1-1-2-3-2-4h0-1c-1 0-1 1-1 1l-1-1c0-1-1-2-2-3v-1l-2-3 1-1-1-1v-1-1c0-2-1-3-1-4l3 1-2-2 2-1 1 2 1-2z"></path></g><path d="M323 248c2 1 3 2 3 4v1l-2-2-1-1v-1-1z" class="n"></path><path d="M325 256l1-1c1 0 2 1 2 1l1 1 1 1v1h-1c-1 0-1 1-1 1l-1-1c0-1-1-2-2-3z" class="o"></path><path d="M330 258l-1-4h0l2 2h0v2c1 3 2 5 3 7 1 1 1 1 1 2h0-1c0 1 0 3-1 3-1-2-1-4-1-7-1-1-2-3-2-4h0v-1z" class="b"></path><path d="M327 242c1 0 1 0 2-1l1 1c-1 1-1 2 0 3 0 1 1 2 2 3h0v2l1 1-1 1c0 1 0 1-1 2-1-1-2-3-2-4-2-2-3-3-4-5l-2-2 2-1 1 2 1-2z" class="j"></path><path d="M329 250l1 1c1 0 2 0 2 1s0 1-1 2c-1-1-2-3-2-4z" class="K"></path><path d="M327 242c1 0 1 0 2-1l1 1c-1 1-1 2 0 3 0 1 1 2 2 3h0v2c-1-1-1-1-2-1-1-2-3-3-4-5l1-2z" class="q"></path><path d="M313 247c0 1 1 2 2 2 0 1 1 1 2 1l-1-1 1-1 2 3c1 1 2 2 3 2l1 3-1 1v1l1 2-1-1-1 1v-1h-1v1 1h-1l1 4v3h-1c-1-1-1-2-1-2h-2v-1-1h-2c-1 0-1 1-1 2-1 0-1 0-2-1l-1 1h0v-1c1-1 1-2 1-3v-1c0-3-1-4-2-6 0-1 1-2 2-2l-1-2v-1h1v-2s1-1 2-1z" class="h"></path><path d="M311 262l1 1h1v-2c2 1 2 2 3 3h0-2c-1 0-1 1-1 2-1 0-1 0-2-1l-1 1h0v-1c1-1 1-2 1-3z" class="b"></path><path d="M311 253l1 1c1 3 2 5 1 8h-1l-1-1c0-3-1-4-2-6 0-1 1-2 2-2zm3 0c1 1 1 1 1 2l3 6c1 1 1 2 1 4h1c0 1 0 1-1 2l-1-2c-1-2-3-5-4-7 0-1-1-4 0-5z" class="k"></path><path d="M313 247c0 1 1 2 2 2v1l3 6c-1 0-2-1-3-1 0-1 0-1-1-2l-1-1c0 1 0 2-1 2l-1-1-1-2v-1h1v-2s1-1 2-1z" class="m"></path><path d="M313 247c0 1 1 2 2 2v1c-2-1-2-1-3 0v1h-2v-1h1v-2s1-1 2-1z" class="S"></path><path d="M317 250l-1-1 1-1 2 3c1 1 2 2 3 2l1 3-1 1v1l1 2-1-1-1 1v-1h-1v1 1h-1c0-2-1-3-1-5l-3-6v-1c0 1 1 1 2 1z" class="r"></path><path d="M320 254c1 1 1 2 2 3v1h-1-1v-4z" class="f"></path><path d="M317 250l-1-1 1-1 2 3c1 1 2 2 3 2l1 3-1 1c-1-1-1-2-2-3l-3-4z" class="K"></path><path d="M313 266c0-1 0-2 1-2h2v1 1h2s0 1 1 2l1 2c0 1 0 2 1 3s1 1 1 2c1 1 1 1 1 2v1c1 1 2 1 2 2 2 1 2 2 4 3 0 2 1 3 1 4l-2 2h0l1 1c0 1 0 1-1 1s-2-1-2-2h-1-2-1c0-1-1-1-2-2 1 2 1 3 2 4-1 0-1 1-1 1v1c-1-1-1-1-1-2-1 0-1 1-2 1h0c0-1 1-2 1-2v-1-2c0-1-1-2-1-3-1-1 0-1-1-2l-1-1c0-1 1-2-1-3h0c-1 1-2 2-2 3v2c-1-2-2-4-3-5v-1l2 1h1 0v-2c0-2 1-6-1-7v-2l1-1z" class="V"></path><path d="M317 273c1 2 1 2 2 2 0 2 0 3 1 4l1 1h0 0c0 2 0 2 1 3v2l3 4h-2-1c0-1-1-1-2-2h0v-3c0-1 0-1-1-2h-1v-2l-1-1v-5-1z" class="R"></path><path d="M316 265v1h2s0 1 1 2l1 2c0 1 0 2 1 3s1 1 1 2c1 1 1 1 1 2h0l-1-1c-1 0-2-1-3-2h0v1c-1 0-1 0-2-2v1c-1 0 0 0-1 1 0 0 0 1-1 2 0-1 0-3-1-4-1-2 0-4 0-6h0c0-1 1-2 2-2z" class="T"></path><path d="M316 265v1h2s0 1 1 2l1 2h-1l-1 1h-1v-3l-1-1c0 1 0 1-1 2 0-1 0-2-1-2h0c0-1 1-2 2-2z" class="o"></path><path d="M317 268v3h1l1-1h1c0 1 0 2 1 3s1 1 1 2c1 1 1 1 1 2h0l-1-1c-1 0-2-1-3-2h0v1c-1 0-1 0-2-2h-1v1h-1v-1c0-2 1-4 2-5z" class="O"></path><path d="M319 275v-1h0c1 1 2 2 3 2l1 1h0v1c1 1 2 1 2 2 2 1 2 2 4 3 0 2 1 3 1 4l-2 2h0l1 1c0 1 0 1-1 1s-2-1-2-2h-1l-3-4v-2c-1-1-1-1-1-3h0 0l-1-1c-1-1-1-2-1-4z" class="C"></path><path d="M323 278c1 1 2 1 2 2 2 1 2 2 4 3 0 2 1 3 1 4l-2 2h0l-1-1c1-2 1-2 0-3l-2-2c0-1-1-2-1-2-1-1-1-2-1-3z" class="O"></path><defs><linearGradient id="AF" x1="302.88" y1="233.149" x2="306.186" y2="189.295" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#353536"></stop></linearGradient></defs><path fill="url(#AF)" d="M313 199c1-1 3-2 4-4l9-8c3-3 6-5 9-7-1 1-1 2-2 3l-1 3-4 9c-3 9-9 20-8 30l-1 5h1 1v6l2 4h0 1l1 2-2 1 2 2-3-1c0 1 1 2 1 4v1 1c-1 1-1 1-2 1l1 2c-1 0-2-1-3-2l-2-3-1 1 1 1c-1 0-2 0-2-1-1 0-2-1-2-2-1 0-2 1-2 1v2h-1l-2-2-2-1c-2-3-5-7-9-8l-2-2-3-3h0c2 0 3 1 5 1l-2-1c-2-2-5-4-9-5-2-1-6-3-8-5-1-1-3-2-5-2 4 0 6 0 9-2h0c3-2 6-3 9-5 1-1 3-2 5-3l1-1 5-3c0-1 0-1 1-2v1h1c1-2 2-2 4-3 1-2 3-3 5-5z"></path><path d="M317 204l2-3h0v2h1l-3 7h-1 0c1-1 1-2 0-3l1-3z" class="S"></path><path d="M294 217c1 0 2 0 3-1h0c2-1 6-2 9-2-2 1-3 1-4 2l1 1h0c-2 0-7 1-9 0z" class="b"></path><path d="M317 214h1v1c-1 1-1 0-1 1v1l-4 11h-1c1-4 2-6 3-9 0-1 1-3 2-4v-1z" class="n"></path><path d="M273 222c4 0 6 0 9-2v2h-1l-1 1c2 1 6 1 8 3h-1v1l-1 2c-2-1-6-3-8-5-1-1-3-2-5-2z" class="S"></path><path d="M320 225l-1 5h1 1v6l2 4h0 1l1 2-2 1 2 2-3-1-3-3v-3c-1-1-1-1-1-2l1 1v-1c0-4 0-7 1-11z" class="I"></path><path d="M319 238c2 1 3 3 4 5l2 2-3-1-3-3v-3zm-31-12l4 2c2 0 4 1 7 1h1v1h-1v1c1 1 1 1 1 2l1 1c-2 0-3 0-4-1h-1l-1 1c-2-2-5-4-9-5l1-2v-1h1z" class="V"></path><path d="M292 228c2 0 4 1 7 1h1v1h-1v1c1 1 1 1 1 2l-8-5z" class="n"></path><path d="M297 211v1 1 1c1-1 3-1 4-1 3-1 6-2 9-4 1-1 3-3 5-4h1l-1 2 1 1-1 2c-1 1-2 2-3 4v-2c-2 1-4 2-6 2-3 0-7 1-9 2h0c-1 1-2 1-3 1v-1h1c0-1 0-2-1-2 0 0-1 1-2 1h-1c1-1 3-2 5-3l1-1z" class="p"></path><path d="M297 211v1 1 1c1-1 3-1 4-1 3-1 6-2 9-4 1-1 3-3 5-4h1l-1 2 1 1-1 2h-1c0-2 0-2 1-3v-1l-1 1c-1 1-4 2-5 4-2 1-4 2-7 2-2 1-3 2-5 2-1-1-1-1-1-2v-1h0l1-1z" class="W"></path><path d="M300 229c4 0 8 1 12 2 1 1 2 2 4 3h0c1 1 2 1 3 3l-1-1c0 1 0 1 1 2v3c-1 0-2-1-3-2-3 0-6-4-9-6-1 1-6-3-7-3v-1z" class="h"></path><path d="M316 234c1 1 2 1 3 3l-1-1c0 1 0 1 1 2v3c-1 0-2-1-3-2 0-1-1-2-3-3v-1l3 2c1-1 0-1 0-2-1 0 0-1 0-1z" class="S"></path><path d="M307 233h1l2 1c1 0 1 0 1-1 2 1 2 1 2 2v1c2 1 3 2 3 3-3 0-6-4-9-6z" class="m"></path><path d="M300 233c0-1 0-1-1-2v-1h1c1 0 6 4 7 3 3 2 6 6 9 6 1 1 2 2 3 2l3 3c0 1 1 2 1 4v1 1c-1 1-1 1-2 1l1 2c-1 0-2-1-3-2l-2-3h0c0-1-1-1-2-2v-1c-1-1-2-3-4-3-2-2-4-4-7-5l-3-3h0l-1-1z" class="k"></path><path d="M301 234h2c1 1 3 2 4 3h-3l-3-3z" class="a"></path><path d="M317 244c1 0 1-1 2 0 2 2 3 3 4 5v1c-1 1-1 1-2 1-1-3-3-5-4-7z" class="m"></path><defs><linearGradient id="AG" x1="316.456" y1="244.984" x2="319.236" y2="250.571" xlink:href="#B"><stop offset="0" stop-color="#373738"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#AG)" d="M317 244h0c1 2 3 4 4 7l1 2c-1 0-2-1-3-2l-2-3h0c0-1-1-1-2-2v-1c1 0 1-1 2-1z"></path><path d="M304 237h3c2 1 3 2 5 3h1l4 4c-1 0-1 1-2 1-1-1-2-3-4-3-2-2-4-4-7-5z" class="l"></path><path d="M295 234l1-1h1c1 1 2 1 4 1h0l3 3c3 1 5 3 7 5 2 0 3 2 4 3v1c1 1 2 1 2 2h0l-1 1 1 1c-1 0-2 0-2-1-1 0-2-1-2-2-1 0-2 1-2 1v2h-1l-2-2-2-1c-2-3-5-7-9-8l-2-2-3-3h0c2 0 3 1 5 1l-2-1z" class="f"></path><path d="M312 246v-1c2 1 2 1 3 1 1 1 2 1 2 2h0l-1 1 1 1c-1 0-2 0-2-1-1 0-2-1-2-2l-1-1z" class="g"></path><path d="M307 246l1-1v-2l1-1 3 4 1 1c-1 0-2 1-2 1v2h-1l-2-2-1-2z" class="r"></path><path d="M292 234c2 0 3 1 5 1l3 3c3 2 5 5 7 8l1 2-2-1c-2-3-5-7-9-8l-2-2-3-3h0z" class="W"></path><path d="M313 199c1-1 3-2 4-4l9-8c3-3 6-5 9-7-1 1-1 2-2 3h0 0l-1 1c-2 2-3 5-5 7-2 4-5 6-6 10l-1 2h-1v-2h0l-2 3-1 3v1l-1-1 1-2h-1c-2 1-4 3-5 4-3 2-6 3-9 4-1 0-3 0-4 1v-1-1-1l5-3c0-1 0-1 1-2v1h1c1-2 2-2 4-3 1-2 3-3 5-5z" class="o"></path><path d="M315 202c1-2 2-3 4-4l3-3c0 1-2 3-2 5 0 0 0 1 1 1l-1 2h-1v-2h0l-2 3h0v-3h-1l-1 1z" class="Y"></path><path d="M313 199v1c0 2-2 3-2 4-2 1-4 3-7 4v-1c1-2 2-2 4-3 1-2 3-3 5-5z" class="j"></path><path d="M315 202l1-1h1v3h0l-1 3v1l-1-1 1-2h-1c-2 1-4 3-5 4-3 2-6 3-9 4-1 0-3 0-4 1v-1-1-1l5-3-1 1c-1 1-2 3-4 4 3-1 5-2 7-3h1v-1c2 0 4-1 5-3 2-1 4-3 5-4z" class="N"></path><path d="M318 321v-2h0 2v1h2v1h1v1 1h1 1v1c0 1 1 1 2 2v-5c0-1 0-1 1-2 1 0 1 1 2 1 0 1 1 1 1 1l-1 1h1l2-1v1c0 1 0 1-1 1 1 1 0 1 1 1 1 1 1 1 1 2h1l1 1h0c1 0 1-1 2-1h1c3-1 6-1 9-1h1c2 0 3 0 4-1h1c-1 1-2 3-1 5h0v1h-1c1 1 0 1 1 1 2 1 4 1 6 1l-1 1c11 3 25 11 34 20l-1 31v55 59c0 18 0 38 4 56 3 14 9 26 18 37 4 4 9 8 14 11 4 3 8 5 12 7 9 5 21 7 32 8h0v12H223v-12c14-2 28-3 41-8 6-2 11-6 16-9s9-6 12-10c7-9 14-19 16-30 6-22 5-44 5-66v-86-25-16l-4 4c0 1-1 2-1 2-1 1-1 6-1 8v24l1 86-1 39c0 7-1 13-2 19-3 18-13 36-28 47-4 2-9 5-14 7-12 5-23 7-36 7l-10 1v23l186 1 50-1c7 0 16 0 23 1l1-24c-17 0-32-4-46-13-3-2-7-4-10-7-11-11-19-25-22-40-1-9-2-18-2-27V337l5 1v59 21c0 3-1 7 0 10-1 5 1 7 3 10l2 3 3 6 2 1 3 6c2 3 4 8 6 13 4 7 8 15 10 23 1 2 1 3 1 4v3c0 1-2 3-2 3l-1 1v-3l-1-1c0 2-2 5-2 6-1 2-1 3-2 4l-2 4-2 2v1l1 1v2l-2 3c0 1-2 4-2 5-1 3-2 9-6 10h0 0c-2 3-2 6-3 9v2c1 1 0 2 0 3 0 3-1 6 1 8h1v-1l1 1v2c0 1 0 4-1 5 0 2 1 3 0 4 0 2 1 3 2 4 1-3 1-6 2-8 1-1 1-2 2-3v2h0v1c2-1 3-4 4-5l-1-1 1-1c0-2 0-3 1-4h0l1 1 4-4c0-1 0-2 1-2 0-1 1-1 2-1v1l3-1c1 0 1 0 2-1h2c1 0 1 0 2 1 0 1 1 2 0 3 0 1 0 1 1 1-2 3-5 6-7 9-1 0-2 2-2 2h-1c0 2-1 3-2 4l1 1h0c2-1 3-2 4-3h1c5-5 11-8 17-11l1 1c1-1 1-1 3-1l-2 2h0c-1 1-1 2-2 2-1 2-1 3-2 4v2h1 6v1h0s1 1 2 1 4 1 5 1h1 3c-1 1-1 2-1 3 1 0 1 1 2 2s2 1 3 2c1 2 1 4 3 5l-1 1-1 1c0 1 0 2 1 3h0v-1h0c1 0 2 0 3 1 0 1 1 1 2 2 1 2 2 4 3 5 1 2 1 2 2 3h1c0 1 0 2-1 3v2c-1 1-1 2-2 3h0c-1 2-2 3-3 4l-2 1c2 0 5 0 7 1-1 5-1 12-1 18v6c0 2 0 5-1 8h-19c-3 0-6-1-8 0l-6 4c-1 2-2 2-4 3s-4 2-6 2c-1 1-1 3-4 3h0c-1 0-2 0-3 1 0 1-1 1-1 1v1h2c-1 1-1 1-2 1h0-1c-2 0-3 1-5 0v-1h-1c0-2 1-2 2-3h-3c-1 0-2 1-3 0-1 0-1 0-2-1h0c-1 3-2 6-4 8-5 5-13 10-20 10h-3-1l-1 1v1h-2l-6-3c0-1 1-1 1-1l-2-1c-2-2-4-3-5-4v-1l-1-1c0-1-1-2-2-3 0 0 1-1 2-1l-1-1h1l1-1h-3l-1-1h-1v-1c-1 0-1 0-2-1-1 0-1-1-1-2-1 0-1 0-2-1l-2-2h-1v2h-1l-1-2c0-1-1-2-1-3l-1-2-4-2-1 1-1 1c0 1 0 1-1 1l-1-1c0-1 0-1-1-2h0c-2 0-5 0-7 1h0-3c0 1-1 1-1 2h0l-2 4-3 1-1-1v1c0 1 0 1-1 2l-3 3-1 1c-1 1-2 2-3 2l-1 2-1 1-2 1h0v1c-1 1-3 1-4 2s-1 2-2 3-1 1-2 1h0l-3 3h1l-4 2h0-6l-6-2c-1-2-4-3-6-4-1-1-3-2-4-3l1-1-2-1c-1 1-1 0-2 0-2 0-3 0-4-1h-3l-7-1h-2 0c-3 0-6 0-8-2h0v-1h1 4 2c0 1 0 1 1 1h4s-1-1-2-1h0-1-1l-3-1-1-1h0-1-1l-1-1c0 1 0 1 1 1h0c-1 1-2 1-2 0h-1 1c0-1-2-1-3-1-2-1-3-1-4-2l-1-2 1 1h1 0v1c1-1 0-1 0-2-1-1-1-1-2-1v-1h-1c0-1 0-1-1-2l-1-1h0c-1-1-3-2-5-3-1-1-4 0-6 0-6-1-13 0-20 0v-13l-1-9v-1c0-1 0-2-1-2-1-1-3-2-4-3v1l-1-1h-1c1-2 1-2 0-3s-2 0-4 0l1-1c1-1 1-2 2-3h1l1-2v-1h1v-3l1-1 1-1v-5h0v-1l1-1c0-3 0-5-1-8-1 0-1-1-2-2h0v-1l1-1c1-1 2-1 2-2l2-3c1-2 3-2 4-4l1-1 2 1 2-2h2l1-2h1c-1 2-1 3-1 4l1 2 1-1c1 0 3-1 4-1h0v1c1-1 2-2 3-2 0-1 2-1 3-2-1-1-1-2-1-2-1-1-1-2-2-3v-1l-4-5v-2h0 2l1-1v-2c-3-2-5-2-7-3 1 0 1 0 1-1 2 0 4 1 6 2l3 2c1 0 1-1 2-1v-1l1 1 1 1h1l3 3c2 1 3 2 5 2v-2c-1 0-1 0-1-1h1v-1c-2-1-5-4-6-5s-2-2-4-2h0l-8-6v-1c-2-1-5-1-8-2h0 1v-1h3c-1 0-1-1-2-1l-3-1c-1 0-2 0-2-1l1-1 1 1c2 0 4 1 5 1 2 0 3 1 5 2l4 1h1l3 1 6 4c1-1 2-1 3-2l2 1c0-1-1-2-2-4h0l-2-1h2 1 0v-1h-1c-2-1-2-3-4-5-1 0-1-1-2-2l1-1c0-1-1-1-1-2l-2-2 1-1-4-4v-1h0l-2-1-2-2-1-1c1-1 1-1 0-3h1c1 0 3 1 3 2l3 1 6 5 4 4c2 1 4 2 5 3l7 8c2 2 3 3 6 5 0 0 0 1 1 1l2 2h1c-1-2-3-4-4-6-1-1-1-2-2-2-1-2-1-4-2-6-1 0-2-1-2-2 0 0 0-1-1-1v-3l1-2-1-2h0l2 1v-1h1 2l-2-2h0c-1 2-2 0-4 0-1-1-3-1-4-2s-2 0-3-1-2-1-4-1h0c-3-2-7-3-10-5l-2-1c2-1 5 0 7 0l1-1-1-1c-4-2-7-3-11-5l-1-1h2l-3-3c1-1 0-2 0-3h0v-1l1-1h0c-2-2-3-4-4-6-1-3-1-6 0-10 0 0 0-1-1-2v-3c1-3 1-7 0-11 0 1 1 1 1 2 1 1 0 3 1 4v-6h0v-6c1-2 2-4 4-5l-1-1c1-1 1-2 2-4l-1-1 2-3c0-2 2-8 1-9l1-1h0 0c1 0 1-1 2-2h0l1-4c0-1-1-1-2-2l1-2c1-2 1-3 2-4v-1-1-1l1-1h0v3c1 0 1-1 1-1v-1h2l1-1v-2h0c1-1 2-3 4-4h1 1v-2-1l2 1v-1c-1-1-1-1-1-2l1-1c-1-1-1-2-2-2 0-1 1-2 1-2-1 0-1 0-2-1 0 1-1 1-1 2-1 1-1 0-1 0h0v-1l1-2 1 1 1-1c-1-1-1-2-1-2 0-2-1-2 0-3h-1v-1l1-1v-2h2c1-1 1-2 3-2v1h2c1-1 2-1 3-1h1 1 0l-1 2c-1 1-1 2-2 3 3 0 6 0 8 1h1v1h1c2 0 3 1 5 0v1 1h-1c1 1 1 1 2 1s2 0 2 1c1 0 2-1 2-1 1 1 0 1 1 1l-1 1h2c1-1 2-2 3-2l1 1 2-1 1 1c2 0 3-1 4-2 0-1 0-1 1-2v-1-4l1-1v-2c0-1-1-1-1-2v-1h-1c-1 0-1 1-2 1h0c1-1 1-3 2-4l-1-1 3-5c1-4 5-8 7-11h0l2-2 5-4-1-2v-1h0 3c-1 0-2 0-3-1l-1-1h2c1-1 2-1 4-2h-1v-1h1v-1l2-2h-1 0c-1-1-1-1-1-2h1l-1-1 1-2s1-1 2-1 1-1 2-1c0-1 1-1 2-1l1-1v-1-2h-1l-1-1v-1h-1l1-1z"></path><path d="M333 639c3-1 5-1 7 0h-3c0 1-1 1-1 2h0-1c-1 0-1-1-1-2h-1z" class="s"></path><path d="M297 424c1-1 2-2 3-2-2 2-3 4-5 5l-2-1c1-1 3-1 4-2z" class="m"></path><path d="M296 436l7-4c-2 2-4 4-7 5 1 0 1-1 2-1v-1l-2 1z" class="a"></path><path d="M426 589c0-2 0-3 1-4 0 0 1 1 2 1-1 2-1 3-2 4-1 0-1-1-1-1z" class="R"></path><path d="M333 349l3-3v1c0 2 0 3-2 5v-1h-2l1-2zm94 236l2-4c1 1 1 2 2 2l-2 3c-1 0-2-1-2-1z" class="J"></path><path d="M412 501l2 1v2h1v-1-1l1 2v2c-1 1-1 3 0 4v3c-1-2 0-4-2-6v-1-2c-1 0-1 0-1-1l-1-2z" class="h"></path><path d="M260 593c0 1 0 2 1 3l1 2-1 1c-1-2-3-3-4-5v-1h3z" class="e"></path><path d="M293 433l9-4c-2 2-5 5-8 6l-1-2z" class="m"></path><path d="M298 397c2-1 3-2 5-2-2 2-4 3-6 5l-3 1 1-1v-1h1c1 0 1-1 2-2z" class="h"></path><path d="M296 436l2-1v1c-1 0-1 1-2 1-2 1-4 2-6 2l1-1h0v-1c1 0 2 0 4-1h1z" class="l"></path><path d="M269 474c2 0 2 0 3 1 1 0 1 1 2 1 1 1 1 1 3 2v1h-1c0-1 0-1-1-1l1 1s1 1 2 1h-1c-2 0-3-2-4-3l-4-3z" class="n"></path><path d="M420 514l1 1v2l-2 3h-2l2-5 1-1z" class="R"></path><path d="M286 441c1 0 2 0 3-1h3 0c2 1 7-1 8 0-1 0-2 1-3 1h-1c-1 1-2 1-3 1l-1-1h0 1-6-1z" class="a"></path><path d="M291 491l-3-2h0l-2-1v-1s1 0 1 1c1 0 1 0 2 1l-1-1s-1 0-1-1h-1l-1-1h0c1 0 2 1 3 1h1l3 3h-1v1z" class="k"></path><path d="M270 640v-1c0-1 1 0 2 0l3 3h0v3c-3-1-3-4-5-5z" class="F"></path><path d="M384 639l1 1h0c1 0 1-1 1-1h2 1l-1 1v2c0 1-1 1-1 2-1 0-1 0-1 1v-1-1c-1-1-1-1-2-1v-1c1 0 0-1 0-2z" class="e"></path><path d="M440 639h4c-1 0-2 1-3 1-2 1-3 3-5 5l-1-2 5-4z" class="I"></path><path d="M432 578v1h1 2l-4 4c-1 0-1-1-2-2l3-3z" class="D"></path><path d="M283 509c2 2 3 4 3 7 0 1 1 2 1 3l-4-6v-4z" class="a"></path><path d="M280 504l3 5v4l-4-6 1-3z" class="b"></path><path d="M295 379h0c1 0 1 0 2-1l3-1 1-1c0-1 1-1 1-1h1l-2 2-2 2c-1 1-3 2-3 3l-1 3-1 1h0c0-3 0-5 1-7z" class="h"></path><path d="M295 400h-3c0-1 0-4 1-5h1c1 1 2 1 4 2-1 1-1 2-2 2h-1v1z" class="a"></path><path d="M412 455c-1 0-2-1-2-2-1 0-2-1-2-2-3-3-5-6-7-10 3 3 5 6 8 8v1 1h0c1 1 2 3 3 4z" class="p"></path><path d="M388 642c1-2 2-3 3-4v3c-1 2-3 5-3 7h-2c1-1 1-1 1-2l-1-1c0-1 0-1 1-1 0-1 1-1 1-2z" class="D"></path><path d="M256 644l-5-5h0 1c2 0 5 2 7 4h-1l1 1c-1 0-1 0-1 1l-2-1z" class="I"></path><path d="M304 357l2 2h1-2c-2 3-3 6-5 9v-1h0c0 1-1 1-1 2h0c0 1-1 2-2 3 2-5 4-11 7-15z" class="k"></path><path d="M370 641h1 1l-1-2h3v1 1c2-1 2-2 3-2l1 1c-3 2-4 3-7 3l-2 1-1-1h1l1-2z" class="J"></path><path d="M295 385l1 1h1l4-4c1 0 1 0 1 1-1 1-2 3-3 3-1 1-2 2-3 2-1 1-1 2-3 2l1-4 1-1z" class="n"></path><path d="M366 642c-1 0-2-1-3-1-1-1-1-1-1-2 1-1 4-1 6 0 1 0 1 1 2 2l-1 2h-1s-1-1-2-1z" class="E"></path><path d="M294 458l7-2 1 1c-1 1-3 1-4 1h-1c1 1 2 1 4 1v1h-2-1l-1 1h-6v-1h0c1-1 2-1 3-2h0 0z" class="h"></path><path d="M255 473l1-2c1 0 1-1 2-1 0 1 1 2 2 3-1 1-1 1-1 2l1 1h0c0 2 1 3 2 4h-1c-2-2-5-4-6-7z" class="m"></path><path d="M286 432c1 0 1 0 2-1h1 2s1 0 2-1h0c1 0 1-1 2-1s0 0 1-1h0 1 1c2-2 3-3 5-4-1 3-3 4-6 6l-1 1c-1 0-2 1-2 1h-1c-2-1-5 0-8 1l-1-1h2z" class="s"></path><path d="M417 520h2c0 1-2 4-2 5-1 3-2 9-6 10h0l6-15z" class="J"></path><path d="M287 455c3 2 10-1 13-1-2 2-6 2-10 3-3 0-8 0-11-2 2-1 6-1 8 0z" class="W"></path><path d="M313 641h0c-1-1-1-1-1-2h2c0 1 1 1 2 2h0c1 0 2 1 3 2 2 1 3 1 5 1v2h0-1c-4-1-7-2-10-5z" class="R"></path><path d="M296 468l1 1h0c1 1 1 1 2 1l3 3c-1 0-3 0-4-1-2 0-6-2-7-1h-1c-1 0-2-1-4-2h1c2 0 3 1 5 1l1-1 3-1z" class="l"></path><path d="M363 359c1 1 1 2 2 3 2 5 4 10 5 16-2-4-3-7-5-11-1-3-3-5-3-8h1z" class="r"></path><path d="M401 491l1 3h0c-1 1-1 3 0 4l1 3h0c1 3 1 5 2 7-1 2-1 2 0 4v2s0 2 1 2v1c-1-2-1-4-3-6-2-7-2-13-2-20z" class="h"></path><path d="M284 483c2 1 6 2 8 4s5 4 6 7h0-1l-1-1c-1 0-1-1-2-1l-1-1c1 2 3 3 3 4h-2-3v-1l-1 1c1 0 1 1 2 1v1c-1 0-2-2-3-3h1 2c1-1 1 0 2 0-1-1-1-2-2-2l-1-1v-1h1 1l-1-1c-1-3-6-5-8-6z" class="n"></path><path d="M422 571c1-1 3-3 3-4l5-5h-1c0 2-1 3-2 4l1 1h0c2-1 3-2 4-3-2 3-4 5-5 7-1-1-2-1-3-1l-1 1c-1 0-1 1-1 1-2 3-3 7-5 9 0-1 0-3 1-4v-1c1-2 2-4 4-5z" class="E"></path><path d="M416 504h0v-2-1-1c-1-1 0-2 0-3v-2h0 1l1 4c0 2 1 4 1 7l1 2v3h1l-1 2v1l-1 1c0-1 0-2 1-3 0-1-1-7-2-7-1 1 0 2-1 3v-1c1-1 1-2 1-3h1c-1 0-1 0-1-1v-1 2h-1-1v1c1 1 1 1 1 2v1c0 3 0 5-1 8h0c0-1 1-5 0-7v-3-2zm-168-41v-1c1-1 1-2 1-3h2l3 9v2l-1-1c0-1-1-4-2-6 0 2 0 4 1 5v1h-1v2c-1-1-1 0-2-1v-1l-1-6z" class="k"></path><path d="M294 539l1-1v-1h0c1 2 2 4 2 6 2 4 3 9 2 14l-1 2c0-3 0-7-1-11l-3-9z" class="T"></path><path d="M345 337c0 1-1 2 0 3 0 0 1 0 1 1 1 0 1 0 2-1l1-1 1 1c-1 1-2 4-2 4-1 1-3 0-5 1l-1-1s-2 0-2-1v-1c1-2 3-3 5-5z" class="C"></path><path d="M275 453l4 2c3 2 8 2 11 2 2 1 2 1 4 1h0 0c-1 1-2 1-3 2h0v1c-5-2-10-2-14-5 0-1-2-2-2-3z" class="S"></path><path d="M279 455c3 2 8 2 11 2 2 1 2 1 4 1h0-2c-5 0-10 1-14-3h1z" class="k"></path><path d="M418 499l1-2c1 2 1 4 2 6h1v-1h1c1 2 1 3 1 5l-2 4-2 2 1-2h-1v-3l-1-2c0-3-1-5-1-7z" class="a"></path><defs><linearGradient id="AH" x1="293.84" y1="448.896" x2="285.48" y2="447.122" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#575657"></stop></linearGradient></defs><path fill="url(#AH)" d="M297 445h1c1 0 2-1 3-1 0 1 0 1-1 1h-1c-1 0-1 1-1 1h-1c-1 0-1 0-2 1h0v1-1h2 0c1-1 1 0 1 0s-1 1-2 1l-2 2c-3 0-6 1-10 0h0c0-1-1-2-2-3h2 1 1 2c3 0 6 0 9-2z"></path><defs><linearGradient id="AI" x1="297.624" y1="462.175" x2="284.151" y2="465.341" xlink:href="#B"><stop offset="0" stop-color="#1c1c1b"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#AI)" d="M283 460c4 1 7 4 11 5 3 0 5 0 7 2h0c-1 1-2 1-3 1l3 2h1-2l-1-1h-2l-1-1c-2 0-3-1-5-1l-3-2c-2-2-4-3-7-4h0l2-1z"></path><path d="M253 588h0l2 2v1c0 1 1 1 2 2v1h0l-1 1c2 1 3 2 4 4h0c-4 0-7-3-10-5h1v-3-1c1 0 1-1 2-2z" class="O"></path><path d="M253 588h0l2 2v1c0 1 1 1 2 2v1h0l-1 1-1-2v-1c-2 0-3 0-4-1h0v-1c1 0 1-1 2-2z" class="f"></path><path d="M300 415l1-1-1-1h1 1c0-1 0-2 1-3 0 3 0 5-1 7h0v1c0 1-1 1-2 2h2 0c0 1-1 2-2 2h0c-1 0-2 1-3 2h-2c-1 0-2 0-4 1 2-2 3-3 6-4l-1-2 3-2h-1s0-1 1-1l1-1h0z" class="s"></path><path d="M299 417s1-1 2-1h1c0 1-1 1-1 2-1 0-2 0-2-1z" class="a"></path><path d="M299 417c0 1 1 1 2 1l-2 1-2 2-1-2 3-2z" class="l"></path><path d="M297 421l2-2c-1 2-2 3-4 5-1 0-2 0-4 1 2-2 3-3 6-4z" class="a"></path><path d="M446 639v1c-2 2-5 3-7 5-2 1-4 4-6 6-1 0-2 0-3 1 0 1-1 1-1 1v1h2c-1 1-1 1-2 1h0-1c-2 0-3 1-5 0v-1h-1c0-2 1-2 2-3s1-1 2 0h0c1 1 1 1 2 1h0l1-1c1 0 1 0 2-1 3-2 6-5 9-8l6-3z" class="D"></path><path d="M424 651c1-1 1-1 2 0h0c1 1 1 1 2 1l-5 2h-1c0-2 1-2 2-3z" class="I"></path><path d="M401 481h1c0 1 0 1 1 2 3 2 5 8 8 12v2h-1v-2h0v-1h-1v-1s0-1-1-1c-1-2-3-7-5-8v1 1l2 2c0 1 1 2 1 3s1 1 1 2h0c1 1 1 2 2 3h0c1 1 1 2 2 3 0 2 1 3 1 4s0 2 1 3l-1 1h0 0-1v-2h-1v2c-1 1-1 1-1 2h0c-1-2-1-4-2-6h-1l1-1h2l3 2c-1-2-1-4-3-5l-4-9-2-4-1-2h-1v-3z" class="l"></path><path d="M410 447l2 1 3 6c2 3 4 8 6 13h-1c-1-1-1-4-3-5-1 0-1 0-2-1l-1-1v-1c-1-2 1 1 0-1-1 0-1-1-2-2v-1c-1-1-2-3-3-4h0v-1-1h0c1 1 1 1 2 1 0-1-1-1-1-3h0z" class="S"></path><path d="M410 447l2 1 3 6h-1c-2 0-3-2-5-4v-1h0c1 1 1 1 2 1 0-1-1-1-1-3h0z" class="a"></path><path d="M359 640v-1l1-1c2 2 3 4 5 5l1-1c1 0 2 1 2 1l1 1 2-1 1 1-3 1c-1 1-2 1-2 2h0c-1 0-2 0-2-1-1 1-1 1-2 1l-2-2h-1v2h-1l-1-2c0-1-1-2-1-3l-1-2c1 0 2 0 3 1v-1z" class="o"></path><path d="M369 644l2-1 1 1-3 1c-1 1-2 1-2 2h0c-1 0-2 0-2-1-1 1-1 1-2 1l-2-2 1-1c1 1 3 1 4 1 1-1 1-1 2-1h1z" class="j"></path><path d="M356 640c1 0 2 0 3 1v-1h0c2 2 2 3 4 3v1h-1l-1 1h-1v2h-1l-1-2c0-1-1-2-1-3l-1-2z" class="m"></path><path d="M404 455l-3-3v-1h0v-1h1c6 4 10 9 15 15 1 3 2 5 3 8h-1c-1-3-3-6-4-8-1-1-2-3-4-3 0-1-1-1-1-2h-1-1l-4-5z" class="q"></path><path d="M446 639c1 0 1-1 2-1s1 0 1 1h2 2l-6 4c-1 2-2 2-4 3s-4 2-6 2c-1 1-1 3-4 3h0c2-2 4-5 6-6 2-2 5-3 7-5v-1z" class="W"></path><path d="M408 544v2c1 1 0 2 0 3 0 3-1 6 1 8h1v-1l1 1v2c0 1 0 4-1 5 0 2 1 3 0 4 0-1-1-1-2-2-1-4-3-8-3-12 0-2 1-5 2-7l1-3z" class="F"></path><path d="M408 544v2c1 1 0 2 0 3 0 3-1 6 1 8h1v1c0 1 0 3-1 4-3-3-1-8-2-12v-3l1-3z" class="J"></path><defs><linearGradient id="AJ" x1="287.315" y1="428.579" x2="284.543" y2="424.031" xlink:href="#B"><stop offset="0" stop-color="#131012"></stop><stop offset="1" stop-color="#353635"></stop></linearGradient></defs><path fill="url(#AJ)" d="M291 425c2-1 3-1 4-1h2c-1 1-3 1-4 2l2 1c-2 1-3 2-5 2l-2 1h-7-1-1l-2-1s-1-1-2-1l1-1h0 4c4-1 8-1 11-2z"></path><defs><linearGradient id="AK" x1="292.06" y1="429.24" x2="283.966" y2="425.991" xlink:href="#B"><stop offset="0" stop-color="#343235"></stop><stop offset="1" stop-color="#5d605c"></stop></linearGradient></defs><path fill="url(#AK)" d="M293 426l2 1c-2 1-3 2-5 2l-2 1h-7-1-1l-2-1c2 0 5 0 8-1h4c2-1 3-2 4-2z"></path><path d="M276 462l1-1c1 1 2 2 3 2l1-2c3 1 5 2 7 4l3 2c2 0 3 1 5 1l-3 1-1 1c-2 0-3-1-5-1h0c-1-1-1-1-2-1l-1 1-4-5-1 1-2-1h-1l-2-2h2 0z" class="W"></path><path d="M288 465l3 2h1v1c-2-1-3-1-5-2l1-1z" class="r"></path><path d="M276 462h0l4 2-1 1-2-1h-1l-2-2h2z" class="g"></path><path d="M281 461c3 1 5 2 7 4l-1 1-7-3 1-2z" class="Y"></path><defs><linearGradient id="AL" x1="420.871" y1="487.152" x2="426.195" y2="482.448" xlink:href="#B"><stop offset="0" stop-color="#212121"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#AL)" d="M417 465c0 1 1 1 1 2s0 0 1 1v2l2 2 2 4v1c1 3 3 6 3 9l1 5h0 0l-1-1v2h-1l-1-1c-1-2-1-4-1-6l-3-6v-2c-1 0-1 0-1-1h0c0-1-1-2-1-3h1 1l-3-8z"></path><path d="M451 572h0 1c2 0 4 1 6 2l-1 1s-1 0-1-1c-1 1-1 1-2 1-2 0-2 0-3 1l-3 2 1 1-1 1-1-1v-1c-1 1-2 1-3 2h-2-2v1h0v-1l-1-1-1-1c4-3 8-4 13-5v-1z" class="P"></path><path d="M443 577v2l-1 1h-2v-1c1-1 2-1 3-2z" class="X"></path><path d="M443 577c3 0 5 0 8-1l-3 2 1 1-1 1-1-1v-1c-1 1-2 1-3 2h-2l1-1v-2z" class="M"></path><path d="M294 401l3-1-2 2h-1l1 1v-1h1 0l1-1h1 2c1 0 2-1 3-1v2c-1 1-1 2-2 3h0s-1 1-2 1c-1 1-2 1-2 1l-1 1c-1 0-2 0-2 1-1 1-1 1-2 1h-1c0 1 0 1-1 1h-1-1-4 0c2 0 5 0 8-1l-1-2 1-4v-2-1h2z" class="k"></path><path d="M298 402l1 1v-1h2 1c-2 2-3 2-5 2h-1l2-2z" class="s"></path><path d="M292 404c2 0 4-2 6-2l-2 2c-1 1-3 2-4 4 2-1 3-2 5-3l1 1c-1 0-2 1-2 1-2 1-3 2-4 3l-1-2 1-4z" class="S"></path><path d="M270 487c0-3-4-4-4-7l3 3c1 1 2 2 4 3l3 2 1-1c2 0 3 2 5 3l2 1c0 1 1 1 2 1 0 1 1 1 1 1v1h0c0 1-2 0-3 0 0 0-1 0-1-1h-1-1 0-1c-1 0-2 0-2-1v1s1 0 1 1h-1c1 1 1 2 2 2h1v1c-2-1-5-4-7-5-1-2-4-3-4-5z" class="m"></path><path d="M273 486l3 2 1-1c2 0 3 2 5 3l2 1c0 1 1 1 2 1 0 1 1 1 1 1v1h0c0 1-2 0-3 0v-1h-2c0-1-1-1-2-1h0c-3-1-6-3-8-5l1-1z" class="S"></path><path d="M401 484h1l1 2 2 4 4 9c2 1 2 3 3 5l-3-2h-2l-1 1c-1-1-1-2-2-3l-1 1-1-3c-1-1-1-3 0-4h0l-1-3v-7z" class="r"></path><path d="M402 498v-2c1 0 0-1 1 0 0 1 0 1 1 2v2l-1 1-1-3z" class="Y"></path><path d="M403 492v-1c-1-1-1-3-1-4l1-1 2 4-2 2z" class="g"></path><path d="M405 490l4 9h0c-1 0-1 0-1-1l-1-1c-1 0-1 0-1 1h-1c-1-1-1-1-1-2l2-1c0-1-2-2-3-3l2-2z" class="f"></path><path d="M376 643c0-1 2-2 3-3l1-1h1l1 2h1c-2 3-4 3-6 5l-6 3v1h3c0 1-1 1-2 1h0-4c-1 0-1 0-2-1-1 0-1-1-1-2-1 0-1 0-2-1 1 0 1 0 2-1 0 1 1 1 2 1h0c0-1 1-1 2-2l3-1c1 0 3-1 4-1z" class="K"></path><path d="M363 647c1 0 1 0 2-1 0 1 1 1 2 1l1 1h1c1 1 1 2 1 3h2 0-4c-1 0-1 0-2-1-1 0-1-1-1-2-1 0-1 0-2-1z" class="o"></path><path d="M372 644c1 0 3-1 4-1-1 1-1 1-1 2l-1 1c-2 1-4 1-7 1h0c0-1 1-1 2-2l3-1z" class="e"></path><path d="M376 643c0-1 2-2 3-3l1-1h1l1 2c-2 2-5 4-8 5l1-1c0-1 0-1 1-2z" class="O"></path><path d="M263 642c1-1 1 0 1-1s0-2 1-3c1 0 2 0 3 1l2 2v-1c2 1 2 4 5 5l1 2v2 4l1 2h0l-1-1c0-1-1-1-2-1-2-2-3-4-5-6s-4-3-6-5z" class="m"></path><path d="M404 500c1 1 1 2 2 3h1c1 2 1 4 2 6h0c0-1 0-1 1-2v-2h1v2h1 0 0l1-1c-1-1-1-2-1-3 0 1 1 2 1 2v1c0 1 0 2 1 2v1h0v1l-1 5-1-5-1-1h0c0-1 0-2-1-2 0 1 1 2 0 3v4h0 0s0-1-1-1h-1c0 1 1 2 1 2v6l-3-8v7l-3-9c2 2 2 4 3 6v-1c-1 0-1-2-1-2v-2c-1-2-1-2 0-4-1-2-1-4-2-7h0l1-1z" class="k"></path><path d="M404 500c1 1 1 2 2 3h1c1 2 1 4 2 6v2 1c-1-1-1-1-2-1 0 1 1 2 0 3-1-2-2-4-2-6-1-2-1-4-2-7h0l1-1z" class="S"></path><path d="M404 500c1 1 1 2 2 3h1l-1 1h0l-3-3h0l1-1z" class="V"></path><defs><linearGradient id="AM" x1="295.077" y1="447.106" x2="286.344" y2="440.485" xlink:href="#B"><stop offset="0" stop-color="#141114"></stop><stop offset="1" stop-color="#4d4f4d"></stop></linearGradient></defs><path fill="url(#AM)" d="M287 441h6-1 0l1 1c1 0 2 0 3-1h1v1h1l2-1 1 1c-1 2-3 3-5 3h-2-1s-1 1-2 1h3c1 0 1 0 2-1h1c-3 2-6 2-9 2h-2-1-1-1-2v-1h1c0-2-1-2-1-3-1-1-1-1-2-1h-1 0 3c2 0 4 0 6-1z"></path><path d="M286 447v-1h1 0l1 1h-2z" class="S"></path><path d="M283 447c1-1 1-1 2-1h0v1h-1-1z" class="N"></path><defs><linearGradient id="AN" x1="289.212" y1="443.265" x2="281.669" y2="440.995" xlink:href="#B"><stop offset="0" stop-color="#2e2b2e"></stop><stop offset="1" stop-color="#4e4f4d"></stop></linearGradient></defs><path fill="url(#AN)" d="M287 441h6-1 0l1 1c1 0 2 0 3-1h1v1h1c-2 1-4 1-7 2h-1-4-3c-1 0-2-1-2-1-1-1-1-1-2-1h-1 0 3c2 0 4 0 6-1z"></path><path d="M293 442c1 0 2 0 3-1h1v1h1c-2 1-4 1-7 2l-3-1h0c2-1 3-1 5-1z" class="n"></path><path d="M332 351h2v1c-2 3-4 6-7 7-2 1-3 1-5 1v-1c-1 0-3 2-4 1l-2 1v-2l-1 1h-1v-2s0-1 1-2v1h2c2-1 3-2 5-3 1 0 3 0 4-1h2c2-1 3-1 4-2z" class="C"></path><path d="M208 597h1c1 2 1 3 1 5 1 0 1 1 2 2h1l1 2h0l-2 1v1c1 3 0 6 0 9v-1c0-1 0-2-1-2-1-1-3-2-4-3v1l-1-1h-1c1-2 1-2 0-3s-2 0-4 0l1-1c1-1 1-2 2-3h1l1-2v-1h1v-3l1-1z" class="H"></path><path d="M207 598l2 3h0c-1 1-2 1-3 1v-1h1v-3z" class="C"></path><path d="M204 604h1 0s1 0 1 1 0 1-1 2h-3c1-1 1-2 2-3z" class="M"></path><path d="M206 611c0-1 0-2 1-3h1 1v-1l-1-1 1-1h2l1 2v1c1 3 0 6 0 9v-1c0-1 0-2-1-2-1-1-3-2-4-3v1l-1-1z" class="C"></path><path d="M321 642c-1-1-3-1-3-3h5 1 2 1 0 0 5 1 1c0 1 0 2 1 2h1l-2 4-3 1-1-1v1h0-4c-1 1-2 1-3 0h1 0v-2c-2 0-3 0-5-1l2-1z" class="E"></path><path d="M332 640c0 2-2 3-3 4-1 0-1 1-1 1h-1c-1-1-2-1-3-1-2 0-3 0-5-1l2-1 1 1c2 0 5 0 6-1h1c1-1 2-2 3-2z" class="S"></path><path d="M332 639h1 1c0 1 0 2 1 2h1l-2 4-3 1-1-1v1h0-4c-1 1-2 1-3 0h1 0v-2c1 0 2 0 3 1h1s0-1 1-1c1-1 3-2 3-4v-1z" class="F"></path><path d="M334 639c0 1 0 2 1 2h1l-2 4-3 1-1-1c1-2 4-3 4-6z" class="S"></path><path d="M294 450h1c1-1 2-1 3-1h1c0-1 1-2 1-2 0-1 0-1 1-2v1l-1 2c-1 1-3 2-5 2l1 1h2c1-1 1-1 1 0h0c0 1-1 1-2 1h0c-1 1-2 1-3 1h0c-2 1-5 2-7 2-2-1-6-1-8 0l-4-2-1-1 1-1c-1 0-2-1-2-2h1 2c3 1 6 1 8 1h0c4 1 7 0 10 0z" class="k"></path><path d="M275 451c4 1 7 2 11 2 2 1 7-1 8 0-2 1-5 2-7 2-2-1-6-1-8 0l-4-2-1-1 1-1z" class="f"></path><path d="M420 467h1c4 7 8 15 10 23 1 2 1 3 1 4v3c0 1-2 3-2 3l-1 1v-3l-1-1c0 2-2 5-2 6-1 2-1 3-2 4 0-2 0-3-1-5h-1c0-1 0-1 1-2l1 2v-1l1-2c-1-2-1-5-1-7v-1l1 1h1v-2l1 1h0 0c1 0 1 3 1 4 1 1 1 2 1 3h1l1-1v-2c0-1-1-1-1-2v-2c-1-1-2-2-2-4h0 0c0-1 0-1-1-2 0-1-1-3-2-5v-1h0v-2-1c-1-1-1-1-1-2l-1-1c0-1-1-2-1-3h-1c0-1-1-2-1-3z" class="l"></path><defs><linearGradient id="AO" x1="427.86" y1="495.885" x2="422.399" y2="500.662" xlink:href="#B"><stop offset="0" stop-color="#151514"></stop><stop offset="1" stop-color="#2f2d2f"></stop></linearGradient></defs><path fill="url(#AO)" d="M425 499c0-1 1-2 0-3v-2l1-1c1 1 2 3 2 4 0 2-2 5-2 6-1 2-1 3-2 4 0-2 0-3-1-5h-1c0-1 0-1 1-2l1 2v-1l1-2z"></path><path d="M263 466h2c2 1 4 3 6 4 1 0 2 2 3 2 1 1 1 0 1 1l1-1 2 2h1l1 1 3 1c1 0 1 0 2 1l1 1c2 2 3 2 5 3 1 0 2 2 3 2 2 2 4 4 5 6 1 1 1 2 2 3l-1-1c-3-4-6-6-10-8h-2 0l-1-1h0-2s0-1-1-1c-1-1-3-2-5-3h0c-1-1-2-1-2-2h-1 0c-1-1-1-1-2-1l-1-1c-2-1-2-2-4-2 1 1 1 1 3 2v1c-1-1-1-1-3-1l-3-3-2-2 1-1h0l-2-2h0z" class="a"></path><path d="M280 475l3 2 1 1c1 2 5 3 7 5-2-1-8-3-9-4h1c-2-1-2-2-3-4z" class="W"></path><path d="M263 466h2c2 1 4 3 6 4 1 0 2 2 3 2 1 1 1 0 1 1l1-1 2 2 2 1c1 2 1 3 3 4h-1c-3-1-6-3-8-5-2-1-3-3-4-3-2-1-4-2-5-3h0l-2-2h0z" class="N"></path><path d="M260 457v-1h0l-1-2h1l2 3c1 0 1 0 1 1s0 1 1 1l2 1 3 3c2 3 5 5 7 7l1-1c3 3 7 5 11 7h1l2 1c1 0 2 1 4 1 1 0 2 1 3 2-1-1-4-1-5-2h-3-1c-1-1-1 0-2-1s-4-2-6-3-3-2-5-4c-1-1-2-2-4-2l4 3 1 1c0 1 1 1 1 1l1 1h-1l-2-2-1 1c0-1 0 0-1-1-1 0-2-2-3-2-2-1-4-3-6-4h-2l-1-2h1l-1-3c0-1-1-2-2-4z" class="b"></path><path d="M277 469c3 3 7 5 11 7h1l2 1h-1c-3 0-5-1-7-2-3-2-5-3-7-5l1-1z" class="N"></path><path d="M262 461l1-1c3 2 4 4 6 6s4 4 7 6l-1 1c0-1 0 0-1-1-1 0-2-2-3-2-2-1-4-3-6-4h-2l-1-2h1l-1-3z" class="f"></path><path d="M270 460l-2-2 1-1 5 5 2 2h1l2 1 1-1c1 1 4 5 4 5l1-1c1 0 1 0 2 1h0-1c2 1 3 2 4 2h1c2 2 7 4 9 7-4-2-6-4-10-5h-1l5 3c-2 0-3-1-5-1v1h-1c-4-2-8-4-11-7l-1 1c-2-2-5-4-7-7v-1-2h1z" class="b"></path><path d="M289 475l-4-2-2-1h0c-1-1-2-1-2-1v-1c1 0 2 1 2 1 1 0 2 0 3 1h1c1 1 1 1 2 1h0l5 3c-2 0-3-1-5-1z" class="S"></path><path d="M270 460l-2-2 1-1 5 5 2 2h1l2 1 1-1c1 1 4 5 4 5l2 1c-2 0-2 0-3-1-1 0-2-1-2-1h-3l-2-1h0c0 1 1 1 1 2l-1 1c-2-2-5-4-7-7v-1-2h1z" class="f"></path><path d="M270 460l-2-2 1-1 5 5 2 2 1 2h0 0c-2 0-6-4-7-6z" class="K"></path><path d="M259 643c2 2 4 4 7 5-3-3-7-6-10-9 3 0 5 1 7 3s4 3 6 5 3 4 5 6l3 4h-1c0 1 0 1-1 1h-3l-7-1h-2 0c-3 0-6 0-8-2h0c2 0 4 1 6 0 2 0 6 1 8 0h0l-1-1h2c-2-2-3-3-5-4s-2-1-4-1l-2-2 1-1c-1-1-1-1-2-1 0-1 0-1 1-1l-1-1h1z" class="Q"></path><path d="M265 650v-1h2c0 1 1 1 1 2l3 2c1 0 0 0 1 1h0l-1 1c0-1-1-1-1-1-2-2-3-3-5-4z" class="J"></path><path d="M268 656c3 0 6 0 8 1 0 1 0 1-1 1h-3l-7-1h3v-1z" class="l"></path><path d="M255 655c2 0 4 1 6 0 2 1 5 1 7 1v1h-3-2 0c-3 0-6 0-8-2h0z" class="a"></path><path d="M305 639h4 1 0l3 3v-1c3 3 6 4 10 5 1 1 2 1 3 0h4 0c0 1 0 1-1 2l-3 3c-1-1-1-1-2-1h-3-1c-1 0-3 0-4-1 0 0-2-1-2-2v-2c-1-1-2-1-3-1 0 1 0 1 1 2v1c-1-1-2-1-3-2 0-1 0-1-1-2l-1 1c-1 0-1 0-1 1h-1c-1 0-2-1-2-1l-1-2v-1c1 0 0-1 0-2h-1c0 1 0 1-1 2-1-1-1-1-1-2h-1c0 2 0 2-1 3h-1-2v-1h0 1c-1-1-1-2-1-2h11z" class="E"></path><path d="M305 639h4c-1 1-1 2-1 3v1c-1-1-2-3-3-4z" class="m"></path><path d="M313 641c3 3 6 4 10 5 1 1 2 1 3 0h4 0c0 1 0 1-1 2l-3 3c-1-1-1-1-2-1h-3-1c1-1 4 0 6 0-3-2-6-3-9-5h-1c-2-1-2-2-3-3v-1z" class="g"></path><path d="M326 646h4c-1 1-2 2-3 2l-1-2z" class="Y"></path><path d="M325 349c1-1 3-2 4-2h0l-2 2h-1v1h0s1 0 1-1l1 1h2 1c0-1 1-1 2-1l-1 2c-1 1-2 1-4 2h-2c-1 1-3 1-4 1-2 1-3 2-5 3h-2v-1c-1 1-1 2-1 2v2h1c-2 1-1 0-2 0h-2-3-1v-1h-1l-2-2c1-1 2-2 2-3h3c1-1 2-2 3-2 0-1 1-1 2-2v1h0c1 0 2 0 2-1h1v1s1-1 2-1c2 0 4-1 6-1z" class="Z"></path><path d="M325 349c1-1 3-2 4-2h0l-2 2h-1v1h0s1 0 1-1l1 1h2 1c-2 1-3 1-4 2h-3c-1 1-2 1-3 1s-3 1-4 2l1-1c-1-1-1 0-2-1l1-2s1-1 2-1c2 0 4-1 6-1z" class="n"></path><path d="M314 351h0c1 0 2 0 2-1h1v1l-1 2c1 1 1 0 2 1l-1 1c-1 0-2 0-3 1v-1c-1 0-3 1-4 2v1 1c1 0 1 1 1 1h-3-1v-1h-1l-2-2c1-1 2-2 2-3h3c1-1 2-2 3-2 0-1 1-1 2-2v1z" class="h"></path><path d="M306 359c0-1-1-2 0-2 1-1 3-1 5-2h0v1c-1 0-1 1-2 2 0 1 0 1-1 2h-1v-1h-1z" class="i"></path><path d="M444 580c1-1 2-1 3-2v1l-1 2v3l1 1h0-2l-4 4c-2 1-3 2-4 3l-2 4h0l-1-1 2-3-1-1-2 3c-1 0-2 0-2-1 0-2 2-4 3-5-2 1-3 3-4 4h-1v-1c0-3 2-6 4-7 1-2 3-4 5-6l1 1 1 1v1h0v-1h2 2z" class="D"></path><path d="M438 578l1 1 1 1c-1 2-4 3-4 5l-1 1h-1v-1l-1-1c1-2 3-4 5-6z" class="C"></path><path d="M444 580c1-1 2-1 3-2v1l-1 2v3l1 1h0-2l-4 4c-2 1-3 2-4 3l-2 4h0l-1-1 2-3-1-1h0c1-3 4-8 7-9l2-2h0z" class="D"></path><path d="M444 580c1-1 2-1 3-2v1l-1 2c-2 1-4 2-5 4h-1c-1 1-1 2-2 3 0 2-1 3-2 4l-1-1h0c1-3 4-8 7-9l2-2h0z" class="c"></path><path d="M383 641v-2h1c0 1 1 2 0 2v1c1 0 1 0 2 1v1 1l1 1c0 1 0 1-1 2h2c0 1-1 1-2 2v1h-1l1 1h1v1c-1 1-3 1-4 1v1c-1 1-3 0-5 1-1 0-3 0-4-1h-1 0c1 0 1 1 1 2l-2-2-1-1h1l1-1h-3l-1-1h-1v-1h4 0c1 0 2 0 2-1h-3v-1l6-3c2-2 4-2 6-5z" class="O"></path><path d="M386 648h0c-1-1-1-1-1-2h0l-1-1c-1-1 0-1 0-2h1l1 1v1l1 1c0 1 0 1-1 2z" class="B"></path><path d="M377 646c1 1 2 0 3-1l1-1h1c0 1-1 2-2 2 0 1-1 2-2 2l-2 2h0c2 1 2 0 3 0v-1c1 0 2 0 2 1h1c1-1 3 0 4 0v1h-1l1 1c-3 0-5 0-7 1-1 0-2 0-3-1l1-1c-1 0-1 0-2-1h0-1-3v-1l6-3z" class="R"></path><path d="M374 650h1 0c1 1 1 1 2 1l-1 1c1 1 2 1 3 1 2-1 4-1 7-1h1v1c-1 1-3 1-4 1v1c-1 1-3 0-5 1-1 0-3 0-4-1h-1 0c1 0 1 1 1 2l-2-2-1-1h1l1-1h-3l-1-1h-1v-1h4 0c1 0 2 0 2-1z" class="j"></path><path d="M374 650h1 0c1 1 1 1 2 1l-1 1-6 1-1-1h-1v-1h4 0c1 0 2 0 2-1z" class="N"></path><path d="M368 651h4v1h-3-1v-1z" class="S"></path><path d="M373 653c3 1 7 1 10 1v1c-1 1-3 0-5 1-1 0-3 0-4-1h-1 0c1 0 1 1 1 2l-2-2-1-1h1l1-1z" class="L"></path><path d="M243 639h0c2 0 3-1 5 0 1 0 1 0 2 1s4 4 6 4l2 1c1 0 1 0 2 1l-1 1 2 2c2 0 2 0 4 1s3 2 5 4h-2l1 1h0c-2 1-6 0-8 0-2 1-4 0-6 0v-1h1 4 2c0 1 0 1 1 1h4s-1-1-2-1h0-1-1l-3-1-1-1h0-1-1l-1-1c0 1 0 1 1 1h0c-1 1-2 1-2 0h-1 1c0-1-2-1-3-1-2-1-3-1-4-2l-1-2 1 1h1 0v1c1-1 0-1 0-2-1-1-1-1-2-1v-1h-1c0-1 0-1-1-2l-1-1h0c0-1-2-2-3-3h0 2z" class="B"></path><path d="M243 639h0c2 0 3-1 5 0v1c0 1 1 1 1 2l-1 1h0c-2-2-3-3-5-4z" class="S"></path><path d="M256 649c2 0 2 2 5 3v-2l1 1v-1l-1-1c2 0 2 0 4 1s3 2 5 4h-2c-4 0-9-2-13-4l1-1z" class="Z"></path><path d="M248 639c1 0 1 0 2 1s4 4 6 4l2 1c1 0 1 0 2 1l-1 1 2 2 1 1v1l-1-1v2c-3-1-3-3-5-3l-1 1c-1-1-3-2-5-4 0-1 0-1-1-1-1-1-1-1-1-2l1-1c0-1-1-1-1-2v-1z" class="m"></path><path d="M250 640c1 1 4 4 6 4l2 1c1 0 1 0 2 1l-1 1c-1 0-2-1-3-1-2-1-5-3-6-5v-1z" class="Z"></path><path d="M256 649c-2-2-4-3-6-4l1-1c1 0 1 0 2 1 3 2 6 3 8 5v2c-3-1-3-3-5-3z" class="j"></path><path d="M281 430h7l-2 2h-2l1 1c3-1 6-2 8-1l-5 1v1h-2v1c2 0 5-1 7-2l1 2h0c0 1 0 1 1 1-2 1-3 1-4 1v1h0l-1 1h-1l-1 1h-1-2 0l1 1h1c-2 1-4 1-6 1h-3 0l-3-1-2-1h2l1-1h-3c-1 0-1 0-1-1h0c1 0 2-1 3-1l-2-1h5l-1-1h-5c0-1 1-1 2-1v-1l1-1 1-1-1-1h5 1z" class="S"></path><path d="M278 436c3 0 6-1 9-1 0 1-1 1-2 1l-4 1c-2 0-5 0-7 1l-1 1c-1 0-1 0-1-1h0c1 0 2-1 3-1l-2-1h5z" class="K"></path><path d="M285 436c3 0 6 0 9-1 0 1 0 1 1 1-2 1-3 1-4 1-3 1-8 2-10 1v-1l4-1z" class="n"></path><path d="M285 433c3-1 6-2 8-1l-5 1h-1c-3 2-7 2-10 2h-5c0-1 1-1 2-1 3-1 6-1 8-1h3z" class="c"></path><path d="M281 430h7l-2 2h-2l1 1h-3c-2 0-5 0-8 1v-1l1-1 1-1-1-1h5 1z" class="N"></path><path d="M280 430h1l-2 2h-4l1-1-1-1h5z" class="f"></path><path d="M273 439l1-1c2-1 5-1 7-1v1c2 1 7 0 10-1v1h0l-1 1h-1l-1 1h-1-2 0l1 1h1c-2 1-4 1-6 1h-3 0l-3-1-2-1h2l1-1h-3z" class="V"></path><path d="M276 439c0-1 1-1 2 0h1v1h0c-1 1-1 1-1 2h0l-3-1-2-1h2l1-1z" class="c"></path><path d="M281 442l-1-1h1c3-1 5-2 8-2l-1 1h-1-2 0l1 1h1c-2 1-4 1-6 1zm120 26h1c0 4 5 10 8 14 2 2 3 5 4 8 1 1 2 3 2 5h0v2c0 1-1 2 0 3v1 1 2h0l-1-2v1 1h-1v-2l-2-1v-1l-1-1v-1-1-2c-3-4-5-10-8-12-1-1-1-1-1-2h-1v-3-5-5z" class="p"></path><path d="M414 502h0v-5h0c0 1 0 3 1 4v1 1 1h-1v-2z" class="m"></path><path d="M401 473c1 2 1 3 2 4 0 0 0 1 1 1-1 1-2 1-3 0v-5z" class="h"></path><path d="M402 481v-2h1c0 1 1 2 1 3 2 1 3 3 4 5s3 5 3 7v1c-3-4-5-10-8-12-1-1-1-1-1-2z" class="W"></path><path d="M431 638h1c1 0 1 1 2 1h2 1 3l-5 4 1 2c-2 1-5 3-6 5h1c-1 1-1 1-2 1l-1 1h0c-1 0-1 0-2-1h0c-1-1-1-1-2 0h-3c-1 0-2 1-3 0-1 0-1 0-2-1h0l-1-1c1-1 1-3 1-4v-4c1-1 1-2 1-2h0l2-1h1s1 1 2 0c2 0 5 0 7 1 1 0 2 0 2-1z" class="b"></path><path d="M420 638s1 1 2 0v2h2c-1 1-2 1-3 2h-1v-4z" class="l"></path><path d="M419 638v1h0c-1 2-1 5-1 7-2-2-1-5-1-7l2-1z" class="C"></path><path d="M417 639h0c0 2-1 5 1 7l-2 4h0l-1-1c1-1 1-3 1-4v-4c1-1 1-2 1-2z" class="D"></path><path d="M431 638h1c1 0 1 1 2 1h2 1s-1 0-1 1c-1 0-1 0-2 1h0c-2 1-3 3-6 3h0c1-2 4-3 4-5l-1-1z" class="n"></path><path d="M435 643l1 2c-2 1-5 3-6 5h1c-1 1-1 1-2 1l-1 1h0c-1 0-1 0-2-1 1-1 5-3 5-5 1-1 2-2 4-3zm-4-5l1 1c0 2-3 3-4 5-3 2-5 4-8 5 1-4 6-7 9-10 1 0 2 0 2-1z" class="T"></path><path d="M311 343h2l1-1c1 0 1 1 2 1v2l1 1v1c-1 0-1 0-1 1-1 1-1 2-2 3v-1c-1 1-2 1-2 2-1 0-2 1-3 2h-3c0 1-1 2-2 3-3 4-5 10-7 15-1 2-2 4-2 7-1 2-1 4-1 7l-1-1v-1-1-4l1-1v-2c0-1-1-1-1-2v-1h-1c-1 0-1 1-2 1h0c1-1 1-3 2-4l-1-1 3-5c1-4 5-8 7-11h0l2-2 5-4-1-2v-1h0 3l1-1z" class="Q"></path><path d="M306 351c0 3-3 3-3 5-1 3-3 4-4 6h-1-2c2-3 4-6 7-9 1 0 2-1 3-2z" class="h"></path><path d="M311 343h2l1-1c1 0 1 1 2 1v2l1 1v1c-1 0-1 0-1 1-1 1-1 2-2 3v-1c-1 1-2 1-2 2-1 0-2 1-3 2h-3l2-1-1-1s1-1 1-2h0c1 0 1-1 2-1l1-1v-1l-2 2h-1v-1-1l-1-2v-1h0 3l1-1z" class="J"></path><path d="M308 353l3-3v1l1 1c-1 0-2 1-3 2h-3l2-1z" class="m"></path><path d="M311 343v1c0 1 0 1-1 2h0c-1 1-2 1-2 1l-1-2v-1h0 3l1-1z" class="Z"></path><path d="M306 351h0c1-1 1-1 2-1 0 1-1 2-1 2l1 1-2 1c0 1-1 2-2 3-3 4-5 10-7 15-1 2-2 4-2 7-1 2-1 4-1 7l-1-1v-1-1-4l1-1v-2c0-1-1-1-1-2v-1h-1c-1 0-1 1-2 1h0c1-1 1-3 2-4l2-4 2-4h2 1c1-2 3-3 4-6 0-2 3-2 3-5z" class="I"></path><path d="M294 366l2-4h2 1c-1 5-4 10-5 14 0-1-1-1-1-2v-1h-1c-1 0-1 1-2 1h0c1-1 1-3 2-4l2-4z" class="n"></path><path d="M292 370l2-4v4 2l-1 1h-1c-1 0-1 1-2 1h0c1-1 1-3 2-4z" class="a"></path><path d="M294 370v2l-1 1h-1c0-1 1-2 2-3z" class="k"></path><path d="M349 325c2 0 3 0 4-1h1c-1 1-2 3-1 5h0v1h-1c1 1 0 1 1 1 2 1 4 1 6 1l-1 1c-2 0-4-1-6 0v1h2c1 1 2 2 4 3-3 0-5-3-7-1l1 1v1c1 1 3 2 4 4l7 8c6 7 10 15 14 23 1 4 3 7 4 11 0 1 0 2 1 3-3-5-5-11-8-16-4-9-10-18-17-26-2-2-4-5-7-7 1 3 4 5 5 8 3 4 6 8 8 13h-1c-3-7-8-13-12-19l-1-1-1 1c-1 1-1 1-2 1 0-1-1-1-1-1-1-1 0-2 0-3 1-2 2-3 4-5l3-4v-1h-1l-2 3c-1 2-3 4-5 5l-3 3v-1c1-3 2-4 1-7h0c0-1 0 0-1-1-1 0-1 0-1-1h-3l-1-1c1 0 1-1 2-1h1c3-1 6-1 9-1h1z" class="U"></path><path d="M345 337c1-2 2-3 4-5 0 2 0 2-1 3 0 1-1 2-1 2h-2z" class="X"></path><path d="M336 327c1 0 1-1 2-1h1c3-1 6-1 9-1h1c-1 1-1 1-3 2 0-1-1-1-1-1h-2-2 0v2h0s1 1 1 2h0c0-1 0 0-1-1-1 0-1 0-1-1h-3l-1-1z" class="B"></path><path d="M342 330h1c0 1 0 2 1 3h1c1-2 1-2 1-4 1 0 2 0 3 1h0c-1 2-3 4-5 5l-3 3v-1c1-3 2-4 1-7z" class="d"></path><path d="M327 336h1c0-2 1-3 2-4v1c-1 1-1 1-1 2 2-1 2-2 3-2l2 1c2 1 3 0 4 0l1 1v2 2c-2 1-3 4-5 5l-5 3h0c-1 0-3 1-4 2-2 0-4 1-6 1-1 0-2 1-2 1v-1h-1c0 1-1 1-2 1h0c1-1 1-2 2-3 0-1 0-1 1-1v-1l-1-1v-2c-1 0-1-1-2-1l-1 1h-2l-1 1c-1 0-2 0-3-1l-1-1h2c1-1 2-1 4-2h-1v-1h1v-1l2-2h0 1s1 0 1-1h1c0 1 1 1 1 1h1 3 1 0 4z" class="H"></path><path d="M327 337v1h1l1 1-5 4h-1v-1c1-1 4-3 4-5z" class="C"></path><path d="M316 343l1-1c1 0 1 0 2-1h1l1 1-1 2c-1 1-2 2-3 2v1c1 1 1 1 1 2v1l2-2v1c1 0 1-1 2-1s2 0 3 1c-2 0-4 1-6 1-1 0-2 1-2 1v-1h-1c0 1-1 1-2 1h0c1-1 1-2 2-3 0-1 0-1 1-1v-1l-1-1v-2z" class="O"></path><path d="M314 336h1s1 0 1-1h1c0 1 1 1 1 1h1 3 1 0 4v1c0 2-3 4-4 5l-1-1c-1 0-1 0-1 1l-1-1h-1c-1 1-1 1-2 1l-1 1c-1 0-1-1-2-1l-1 1h-2l-1 1c-1 0-2 0-3-1l-1-1h2c1-1 2-1 4-2h-1v-1h1v-1l2-2h0z" class="D"></path><path d="M319 336h3v2h-2c-1 1-2 1-2 0s0-1 1-2z" class="V"></path><path d="M314 336h2v3h-2c-1 0-2 1-2 1h-1v-1h1v-1l2-2h0z" class="f"></path><path d="M318 321v-2h0 2v1h2v1h1v1 1h1 1v1c0 1 1 1 2 2v-5c0-1 0-1 1-2 1 0 1 1 2 1 0 1 1 1 1 1l-1 1h1l2-1v1c0 1 0 1-1 1 1 1 0 1 1 1 1 1 1 1 1 2h1l1 1h0l1 1h3c0 1 0 1 1 1 1 1 1 0 1 1h0c1 3 0 4-1 7v1 1h-2v-2-2l-1-1c-1 0-2 1-4 0l-2-1c-1 0-1 1-3 2 0-1 0-1 1-2v-1c-1 1-2 2-2 4h-1-4 0-1-3-1s-1 0-1-1h-1c0 1-1 1-1 1h-1 0-1 0c-1-1-1-1-1-2h1l-1-1 1-2s1-1 2-1 1-1 2-1c0-1 1-1 2-1l1-1v-1-2h-1l-1-1v-1h-1l1-1z" class="B"></path><path d="M315 330h1c0 2 0 2-1 3l-2 1-1-1 1-2s1-1 2-1z" class="V"></path><path d="M325 333h-1-3-1-2-1c0-1 0-2 1-3h1l1 2c1-1 1-1 2-1h1 0c2 0 3 0 4 1v1h-2z" class="Y"></path><path d="M318 321v-2h0 2v1h2v1h1v1 1h1 1v1c0 1 1 1 2 2v1c1 1 1 1 1 2l1 1 1 1h0-1v1c-1 0-1 1-1 1h-3 2v-1c-1-1-2-1-4-1 1-1 2-1 3-1l1 1v-1l-3-2c0-1 0-1-1-2-1 1-1 1-2 1l-1-1v-2h-1l-1-1v-1h-1l1-1z" class="O"></path><path d="M323 325l1-1 1 1v2h2c1 1 1 1 1 2l1 1h-1c-1-1-3-1-4-2v-2l-1-1z" class="R"></path><path d="M318 321v-2h0 2v1h2v1h1v1 1h1 1v1c0 1 1 1 2 2v1h-2v-2l-1-1-1 1c-1-1-1-2-1-3l-2 1c-1 0-1-1-1-2h-1z" class="j"></path><path d="M327 321c0-1 0-1 1-2 1 0 1 1 2 1 0 1 1 1 1 1l-1 1h1l2-1v1c0 1 0 1-1 1 1 1 0 1 1 1 1 1 1 1 1 2h1l1 1h0l1 1h3c0 1 0 1 1 1 1 1 1 0 1 1h0c1 3 0 4-1 7v1 1h-2v-2-2-2h-3l-6-2h0l-1-1-1-1c0-1 0-1-1-2v-1-5z" class="k"></path><path d="M336 327h0l1 1h3c0 1 0 1 1 1 1 1 1 0 1 1-2 0-5 0-8-1h0l2-2z" class="D"></path><path d="M327 321c0-1 0-1 1-2 1 0 1 1 2 1 0 1 1 1 1 1l-1 1h1s0 1-1 1v1 1c-1 1-1 1-1 2h-1v-2c1 0 1 0 1-1s0-3-1-4l-1 1z" class="a"></path><path d="M434 546h2c1 0 1 0 2 1 0 1 1 2 0 3 0 1 0 1 1 1-2 3-5 6-7 9-1 0-2 2-2 2l-5 5c0 1-2 3-3 4v-1c-1 1-2 1-3 2-1 2-2 4-3 5l-1-1-2-2c0-2 1-9 3-11v1c2-1 3-4 4-5l-1-1 1-1c0-2 0-3 1-4h0l1 1 4-4c0-1 0-2 1-2 0-1 1-1 2-1v1l3-1c1 0 1 0 2-1z" class="F"></path><path d="M417 567h0c1 0 1-1 2-2 0 1-1 2-1 3l1 1h0c-1 1-1 2-2 3h-1c0-2 0-3 1-5z" class="I"></path><path d="M421 553h0l1 1 4-4c0-1 0-2 1-2 0-1 1-1 2-1v1l3-1c-2 4-6 6-9 8-1 1-2 3-3 4l-1-1 1-1c0-2 0-3 1-4z" class="d"></path><path d="M425 560c1-1 3-3 5-4 1-1 1-1 3-1-2 2-5 4-6 7l-6 3c-1 1-2 3-2 4l-1-1c0-1 1-2 1-3-1 1-1 2-2 2h0l1-3c0-1 1-3 2-4 2 0 2 1 3 2l2-2z" class="J"></path><path d="M418 564c0-1 1-3 2-4 2 0 2 1 3 2h-1-1v1l-1 1s-1 1-2 1v-1z" class="E"></path><path d="M433 555l5-5c0 1 0 1 1 1-2 3-5 6-7 9-1 0-2 2-2 2l-5 5c0 1-2 3-3 4v-1c-1-1 4-7 5-8 1-3 4-5 6-7z" class="K"></path><path d="M438 547c0 1 1 2 0 3l-5 5c-2 0-2 0-3 1-2 1-4 3-5 4l-2 2c-1-1-1-2-3-2l2-2 8-7c1-1 2-2 3-2l2-2h3z" class="G"></path><path d="M420 560l2-2v1h1l1 1h1l-2 2c-1-1-1-2-3-2z" class="D"></path><path d="M253 432c1 1 1 2 2 3 1 2 2 3 4 5l2 2c2 2 5 3 7 4s4 2 6 2v1h-1c0 1 1 2 2 2l-1 1 1 1c0 1 2 2 2 3 4 3 9 3 14 5h6l1-1h1c-1 1-1 1-2 1 2 1 4 1 5 1v1c-2 1-4 1-6 1h-1 0c-1 1 0 1-1 1-4-1-7-4-11-5l-2 1h0l-1 2c-1 0-2-1-3-2l-1 1h0-2l-5-5-1 1 2 2h-1v2 1l-3-3-2-1c-1 0-1 0-1-1s0-1-1-1l-2-3v-1l-5-8v-1h1v-1c-1-2-1-3-1-5l-1-3-2-3h1z" class="n"></path><path d="M262 445c0-1-1-2-1-3 2 2 5 3 7 4s4 2 6 2v1h-1c0 1 1 2 2 2l-1 1 1 1c0 1 2 2 2 3-3-2-6-5-8-7l-1 1c-2-1-4-2-5-4l-1-1z" class="V"></path><path d="M263 446h2c1 0 3 1 4 3l-1 1c-2-1-4-2-5-4z" class="l"></path><path d="M262 445l1 1c1 2 3 3 5 4l7 6c3 2 5 3 8 4l-2 1h0l-1 2c-1 0-2-1-3-2l-1 1h0-2l-5-5c0-1-4-3-5-4 0-1-1-2-2-3l1-1c0-1-1-2-1-3v-1z" class="c"></path><path d="M274 459c-3-1-8-7-10-9 1 0 2 1 4 1l6 6 1-1c3 2 5 3 8 4l-2 1h0l-1 2c-1 0-2-1-3-2l-1 1h0c-1-1-1-2-2-3z" class="m"></path><path d="M275 456c3 2 5 3 8 4l-2 1h0l-1 2c-1 0-2-1-3-2l-1 1h0c-1-1-1-2-2-3 2 0 4 2 6 2l-6-4 1-1z" class="V"></path><path d="M253 432c1 1 1 2 2 3 1 2 2 3 4 5l2 2c0 1 1 2 1 3v1c0 1 1 2 1 3l-1 1c1 1 2 2 2 3 1 1 5 3 5 4l-1 1 2 2h-1v2 1l-3-3-2-1c-1 0-1 0-1-1s0-1-1-1l-2-3v-1l-5-8v-1h1v-1c-1-2-1-3-1-5l-1-3-2-3h1z" class="Z"></path><path d="M253 432c1 1 1 2 2 3 1 2 2 3 4 5l2 2c0 1 1 2 1 3v1c0 1 1 2 1 3l-1 1c-1-2-3-5-4-7s-2-4-3-5l-1-3-2-3h1z" class="V"></path><path d="M259 440l2 2c0 1 1 2 1 3v1c-2-2-3-3-3-6z" class="o"></path><path d="M260 453h1c-2-3-4-6-5-9 2 2 3 3 4 5s2 4 3 5l1-1c1 1 5 3 5 4l-1 1 2 2h-1v2 1l-3-3-2-1c-1 0-1 0-1-1s0-1-1-1l-2-3v-1z" class="c"></path><path d="M264 453c1 1 5 3 5 4l-1 1 2 2h-1v2 1l-3-3c-1-1-2-1-2-3h2l-3-3 1-1z" class="o"></path><path d="M250 440l2 2 1-1c1 1 1 2 2 3v1l5 8v1h-1l1 2h0v1c1 2 2 3 2 4l1 3h-1l1 2h0l2 2h0l-1 1 2 2 3 3 4 3c1 1 2 3 4 3h1c1 1 2 2 3 2h1 1l1 1c2 1 7 3 8 6 0 0-1-1-2-1 0-1-1-1-1-1-2-2-5-3-7-4h-1-1 0l1 1s0 1 1 1h0v1l-3-2h0l-1-1h-1c-1-1-2-3-4-3l1 2c1 0 1 0 1 1l1 2h1c0 1 0 1 1 2 1 0 1 1 2 1l2 2c-2-1-3-3-5-3l-1 1-3-2c-2-1-3-2-4-3l-3-3c0 3 4 4 4 7l-3-3c0-1-1-1-2-2 0-1-1-2-1-2v-1l-2-1c0-1-1-2-2-3h0c1 1 0 1 1 2 0 1 1 1 2 3h-1c-1-1-2-2-2-4h0l-1-1c0-1 0-1 1-2-1-1-2-2-2-3-1 0-1 1-2 1l-1 2-1-3v-2-1c0-1 0-3-1-4v-4-4-1-1c0-1-1-2-1-3 1 0 1 0 1-1v-1c-1-2-2-5-3-8z" class="h"></path><path d="M277 487c-1 0-1-1-2-2h0c-2-2-4-4-5-6 2 0 3 2 4 3 1 0 1 0 1 1l1 2h1c0 1 0 1 1 2 1 0 1 1 2 1l2 2c-2-1-3-3-5-3z" class="b"></path><path d="M262 467c0-1 1-1 1-1l2 2h0l-1 1 2 2 3 3 4 3-1 1c-2-1-3-2-4-4-2-1-4-3-5-5h1l-2-2z" class="m"></path><path d="M256 462h1l3 3c1 0 0 0 1-1l1 3 2 2h-1l-1 1c1 1 2 3 3 4v1h0c-2-2-4-4-6-7l-3-6z" class="V"></path><path d="M254 459l1-1c0 1 1 3 1 4l3 6v1c1 3 4 6 5 9-2-1-3-4-4-5s-2-2-2-3c-1 0-1 1-2 1l-1 2-1-3v-2-1c0-1 0-3-1-4v-4l2 5s0 1 1 2c0-3-1-5-2-7z" class="S"></path><path d="M250 440l2 2 1-1c1 1 1 2 2 3v1l5 8v1h-1l1 2h0v1c1 2 2 3 2 4l1 3h-1l1 2h0s-1 0-1 1l-1-3c-1 1 0 1-1 1l-3-3h-1c0-1-1-3-1-4l-1 1c1 2 2 4 2 7-1-1-1-2-1-2l-2-5v-4-1-1c0-1-1-2-1-3 1 0 1 0 1-1v-1c-1-2-2-5-3-8z" class="q"></path><path d="M253 441c1 1 1 2 2 3v1l-1 1-2-4 1-1z" class="Y"></path><path d="M259 461h1l2 3 1 2h0s-1 0-1 1l-1-3-2-3z" class="p"></path><path d="M254 459v-1-3l2 2h1c0 1 1 1 1 2s1 2 1 2l2 3c-1 1 0 1-1 1l-3-3h-1c0-1-1-3-1-4l-1 1z" class="K"></path><path d="M255 445l5 8v1h-1l1 2h0v1h0-1c-2-3-3-7-5-11l1-1z" class="S"></path><path d="M253 449c2 3 2 5 4 8h-1l-2-2v3 1c1 2 2 4 2 7-1-1-1-2-1-2l-2-5v-4-1-1c0-1-1-2-1-3 1 0 1 0 1-1z" class="j"></path><path d="M404 455l4 5h1 1c0 1 1 1 1 2 2 0 3 2 4 3 1 2 3 5 4 8h-1c0 1 1 2 1 3h0c0 1 0 1 1 1v2l3 6c0 2 0 4 1 6v1c0 2 0 5 1 7l-1 2v1l-1-2c-1 1-1 1-1 2v1h-1c-1-2-1-4-2-6l-1 2-1-4h-1c0-2-1-4-2-5-1-3-2-6-4-8-3-4-8-10-8-14h-1c0-1 0-3 1-4l-1-2c0-1 0-2 1-3 1-2 2-2 2-4z" class="V"></path><path d="M414 481h0v-1-1h1l1 2c1 2 3 6 4 7 0 0 0 1 1 1h1v2h-1v-1l-1 1h0c-1-1-2-2-3-4l-3-6z" class="g"></path><path d="M417 495h1l1 1h0c1-1 0-4-1-5 1 1 3 6 4 7l1-1c1-1 1-4 1-5 0 2 0 5 1 7l-1 2v1l-1-2c-1 1-1 1-1 2v1h-1c-1-2-1-4-2-6l-1 2-1-4z" class="S"></path><path d="M402 468h0c2 1 4 4 5 6 2 2 4 4 5 7 2 2 4 5 3 8l-1 1c-1-3-2-6-4-8-3-4-8-10-8-14z" class="j"></path><path d="M401 462c2 0 2 0 3 1l2 4c1 1 5 3 6 4v1l2-1v1l-1 2c2 2 3 4 4 7h-1l-1-2h-1v1 1h0c-3-5-7-10-11-15-1-1-1-1-1-2l-1-2z" class="i"></path><path d="M414 471v1l-1 2-1-2 2-1z" class="V"></path><path d="M406 467c1 1 5 3 6 4-1 1-1 2-2 2-1-2-3-4-4-6z" class="c"></path><path d="M411 462c2 0 3 2 4 3 1 2 3 5 4 8h-1c0 1 1 2 1 3h0c0 1 0 1 1 1v2l3 6c0 2 0 4 1 6v1c0 1 0 4-1 5-1-3 1-4-1-6v-2h-1c-1 0-1-1-1-1-1-1-3-5-4-7h1c-1-3-2-5-4-7l1-2 3 4h1c0-1 0-1-1-2h0c0-1 0-2-1-3 0-1-2-2-2-3v-1c0-2-1-4-3-5z" class="N"></path><defs><linearGradient id="AP" x1="411.85" y1="475.947" x2="422.982" y2="484.982" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#858485"></stop></linearGradient></defs><path fill="url(#AP)" d="M414 472l3 4 3 8c1 2 1 4 2 5h-1c-1 0-1-1-1-1-1-1-3-5-4-7h1c-1-3-2-5-4-7l1-2z"></path><defs><linearGradient id="AQ" x1="402.346" y1="459.621" x2="417.553" y2="469.933" xlink:href="#B"><stop offset="0" stop-color="#1a191b"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#AQ)" d="M404 455l4 5h1 1c0 1 1 1 1 2 2 1 3 3 3 5v1c0 1 2 2 2 3 1 1 1 2 1 3h0c1 1 1 1 1 2h-1l-3-4v-1l-2 1v-1c-1-1-5-3-6-4l-2-4c-1-1-1-1-3-1 0-1 0-2 1-3 1-2 2-2 2-4z"></path><path d="M408 460h1 1c0 1 1 1 1 2 2 1 3 3 3 5v1l-6-8z" class="o"></path><defs><linearGradient id="AR" x1="407.531" y1="461.752" x2="405.664" y2="467.864" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#4e4e4f"></stop></linearGradient></defs><path fill="url(#AR)" d="M402 459c2 0 7 6 8 8s2 3 4 4l-2 1v-1c-1-1-5-3-6-4l-2-4c-1-1-1-1-3-1 0-1 0-2 1-3z"></path><path d="M275 639c1-1 3 0 5 0h14s0 1 1 2h-1 0v1h2 1c1-1 1-1 1-3h1c0 1 0 1 1 2 1-1 1-1 1-2h1c0 1 1 2 0 2v1l1 2s1 1 2 1h1c0-1 0-1 1-1l1-1c1 1 1 1 1 2 1 1 2 1 3 2v-1c-1-1-1-1-1-2 1 0 2 0 3 1v2c0 1 2 2 2 2 1 1 3 1 4 1h1 3c1 0 1 0 2 1l-1 1c-1 1-2 2-3 2l-1 2-1 1-2 1v-1h-3-5c-4 2-10 0-14-1 0 0-4-2-5-3h1l-1-1c-2-3-5-5-6-8l-1-2h0l-2 1h-1c1-1 1-2 2-3h0 0l-1-1h-7z" class="Q"></path><path d="M294 650c1-1 1-2 2-3l1 2c0 1 0 1-1 1h-2z" class="B"></path><path d="M290 646c1-1 0-1 0-2l-1-1c2 0 2 1 4 1h0c0 1 2 3 3 3h0c-1 1-1 2-2 3h-1l-2-2-1-2z" class="L"></path><path d="M285 643c2 0 2 0 3 1l2 2 1 2 2 2h1 2c1 2 3 2 4 3l-1 1c-1 0-1-1-2-1s-1 0-2-1c-1 0-1 0-2-1h-1s0 1-1 1c-2-3-5-5-6-8v-1z" class="C"></path><path d="M275 639c1-1 3 0 5 0h14s0 1 1 2h-1 0l-1-1h-1v1h0v2l1 1h0c-2 0-2-1-4-1l1 1c0 1 1 1 0 2l-2-2c-1-1-1-1-3-1v1l-1-2h0l-2 1h-1c1-1 1-2 2-3h0 0l-1-1h-7z" class="P"></path><path d="M287 639h2v1h-1l-1 1v-1-1z" class="J"></path><path d="M292 643s-1 0-1-1h-1 0c-1-1 0-2 0-3h1c0 1 0 1 1 2h0v2z" class="L"></path><path d="M283 640h3 1c-1 1-2 1-2 3v1l-1-2h0l-2 1h-1c1-1 1-2 2-3h0z" class="R"></path><path d="M291 652c1 0 1-1 1-1h1c1 1 1 1 2 1 1 1 1 1 2 1s1 1 2 1l1-1c3 0 6 2 9 2h2c1 0 4 0 6 1h-7v1c-4 2-10 0-14-1 0 0-4-2-5-3h1l-1-1z" class="B"></path><path d="M292 653c1 0 1 0 2 1s4 1 5 2h1c3 1 7 0 10 0v1c-4 2-10 0-14-1 0 0-4-2-5-3h1z" class="P"></path><path d="M313 648l-1-1h2c0 1 2 2 2 2 1 1 3 1 4 1h1 3c1 0 1 0 2 1l-1 1c-1 1-2 2-3 2l-1 2-1 1-2 1v-1h-3-5v-1h7c-2-1-5-1-6-1h-2l1-1h0v-1h3v-1c1-1 1-1 2-1l1-1c-1 0-2-1-3-2z" class="T"></path><path d="M313 653h4v1h-7 0v-1h3z" class="L"></path><path d="M318 651c2 1 4 1 7 1-1 1-2 2-3 2h0-3 0l4-2c-3 0-5 1-8 0h0 2l1-1z" class="o"></path><path d="M311 655c2-1 5 0 7 0h0v1l-1 1h3l-2 1v-1h-3-5v-1h7c-2-1-5-1-6-1z" class="R"></path><path d="M313 648l-1-1h2c0 1 2 2 2 2 1 1 3 1 4 1h1 3c1 0 1 0 2 1l-1 1c-3 0-5 0-7-1l-2-1c-1 0-2-1-3-2z" class="Y"></path><path d="M294 641v1h2 1c1-1 1-1 1-3h1c0 1 0 1 1 2 1-1 1-1 1-2h1c0 1 1 2 0 2v1l1 2s1 1 2 1h1c0-1 0-1 1-1l1-1c1 1 1 1 1 2 1 1 2 1 3 2v-1c-1-1-1-1-1-2 1 0 2 0 3 1v2h-2l1 1c1 1 2 2 3 2l-1 1c-1 0-1 0-2 1v1h-3v1c-2 0-3-1-4-2-2 0-2-1-3-2v1h0v1h-1c-3-1-2-2-4-4l-5-5c0-1 0-1-1-2h0v-1h1l1 1z" class="B"></path><path d="M298 648s1 0 2-1h0c0-1-1-2-2-3v-2h0c1 0 2 1 3 1-1 1-1 1-1 2 1 1 1 2 1 3v1c1 0 2 1 3 1 0 0 1-1 2-1h1c0-1-1-2-2-2v-1h1l2 2c1 0 1 0 1 1v1h-2v1c2 1 2 1 4 0 1-1 1-2 2-3 1 1 2 2 3 2l-1 1c-1 0-1 0-2 1v1h-3v1c-2 0-3-1-4-2-2 0-2-1-3-2v1h0v1h-1c-3-1-2-2-4-4z" class="E"></path><path d="M432 564h1c5-5 11-8 17-11l1 1c1-1 1-1 3-1l-2 2h0c-1 1-1 2-2 2-1 2-1 3-2 4v2h1 6v1h0c-3 0-5 0-8 1h0 0c-1 1-2 1-2 2-2 0-3 0-4 1 1 0 2 0 2-1h3 0c1 0 1 0 1-1 4 0 11 0 14 3l4 3-1 1h0l-1-1h-1c1 1 1 2 1 2-2 1-4-1-5-2s-2-1-4-2v1h-2v1h-1 0c-4 0-8 2-12 3-1 1-2 3-4 4h-2-1v-1l-3 3-2 4c-1 1-1 2-1 4l-2-2-2-2c-1-1-2-2-3-2 0-1 0-1-1-2l1-1c1-2 3-5 3-8 0 0 0-1 1-1l1-1c1 0 2 0 3 1 1-2 3-4 5-7z" class="B"></path><path d="M425 586v-1c3-6 7-9 12-13h1c-2 2-5 4-6 6l-3 3-2 4c-1 1-1 2-1 4l-2-2 1-1z" class="Y"></path><path d="M449 563h6v1h0c-3 0-5 0-8 1h0c-8 1-15 9-20 15l-3 4c0 1 1 1 1 2l-1 1-2-2h0v-1h0c1-2 2-5 3-5l5-8h3v1c2-2 4-4 6-5 3-2 7-3 10-4z" class="c"></path><path d="M430 571h3v1c-2 3-5 5-7 7h-1l5-8z" class="Q"></path><path d="M438 572s1 0 2-1c0-1 1-1 1-1h1l2-1h2l3-1h7c2 1 3 1 5 1l4 3-1 1h0l-1-1h-1c1 1 1 2 1 2-2 1-4-1-5-2s-2-1-4-2v1h-2v1h-1 0c-4 0-8 2-12 3-1 1-2 3-4 4h-2-1v-1c1-2 4-4 6-6z" class="G"></path><path d="M456 568c2 1 3 1 5 1l4 3-1 1h0l-1-1h-1 0c-2-1-5-1-6-4z" class="C"></path><path d="M439 575l1-1c4-3 9-4 14-4v1h-2v1h-1 0c-4 0-8 2-12 3z" class="L"></path><path d="M432 564h1c5-5 11-8 17-11l1 1c1-1 1-1 3-1l-2 2h0c-1 1-1 2-2 2-1 2-1 3-2 4v2h1c-3 1-7 2-10 4-2 1-4 3-6 5v-1h-3l-5 8c-1 0-2 3-3 5h0v1h0c-1-1-2-2-3-2 0-1 0-1-1-2l1-1c1-2 3-5 3-8 0 0 0-1 1-1l1-1c1 0 2 0 3 1 1-2 3-4 5-7z" class="S"></path><path d="M430 571c2-3 5-6 8-9v2c-1 2-4 4-5 7h-3z" class="F"></path><path d="M422 572s0-1 1-1l1-1c1 0 2 0 3 1-3 4-6 6-8 10v-1c1-2 3-5 3-8z" class="R"></path><path d="M440 560c2-2 5-3 8-4 1-1 2-1 4-1-1 1-1 2-2 2-1 2-1 3-2 4v2h1c-3 1-7 2-10 4-2 1-4 3-6 5v-1c1-3 4-5 5-7v-2l2-2z" class="J"></path><path d="M440 560v1c0 1 1 1 1 2h0-2l-1 1v-2l2-2z" class="R"></path><path d="M275 639h7l1 1h0 0c-1 1-1 2-2 3h1l2-1h0l1 2c1 3 4 5 6 8l1 1h-1c1 1 5 3 5 3 4 1 10 3 14 1h5 3v1h0v1c-1 1-3 1-4 2s-1 2-2 3-1 1-2 1h0l-3 3h1l-4 2h0-6l-6-2c-1-2-4-3-6-4-1-1-3-2-4-3l1-1-2-1c-1 1-1 0-2 0-2 0-3 0-4-1 1 0 1 0 1-1h1l-3-4c1 0 2 0 2 1l1 1h0l-1-2v-4-2l-1-2v-3h0v-3z" class="L"></path><defs><linearGradient id="AS" x1="308.748" y1="653.927" x2="303.879" y2="661.339" xlink:href="#B"><stop offset="0" stop-color="#c3c4c0"></stop><stop offset="1" stop-color="#f0eef3"></stop></linearGradient></defs><path fill="url(#AS)" d="M296 656c4 1 10 3 14 1h5 3v1h0v1c-6 0-12 5-17 3h4v-1c-2 0-3 0-5-1l-1-1h0 0-1c-1-1-1-2-2-3z"></path><path d="M318 658v1c-6 0-12 5-17 3h4v-1c-2 0-3 0-5-1l-1-1c7 1 13 1 19-1z" class="K"></path><path d="M283 651l-1-1 1-1 2 1c0 3 1 4 3 6 2 1 3 1 5 2s4 1 6 1h0l1 1c2 1 3 1 5 1v1h-4c-2 0-4 0-5-1l-6-1c-4-1-7-3-9-7h2c0-1-1-1 0-2z" class="g"></path><path d="M283 651h0c1 1 2 2 2 4 1 0 2 1 3 1l3 3h-2c-2-1-5-4-6-6 0-1-1-1 0-2z" class="F"></path><path d="M281 653h2c1 2 4 5 6 6h2c1 0 2 1 3 1s1 1 2 1l-6-1c-4-1-7-3-9-7z" class="K"></path><path d="M281 643h1l2-1h0l1 2c1 3 4 5 6 8l1 1h-1c1 1 5 3 5 3 1 1 1 2 2 3h1 0c-2 0-4 0-6-1s-3-1-5-2c-2-2-3-3-3-6l-2-1c0-1-1-2-1-3s0-2-1-3h0z" class="E"></path><path d="M281 643h1l2-1h0l1 2c1 3 4 5 6 8l1 1h-1l-2-1c-1-1-1-2-3-2 0-1 0-2-1-2 0-1-1-3-2-3l-1 1c0-1 0-2-1-3h0z" class="F"></path><path d="M289 652l2 1c1 1 5 3 5 3 1 1 1 2 2 3h1 0c-2 0-4 0-6-1 0-1-1-1-1-1-1-2-3-3-4-5h1z" class="X"></path><path d="M275 639h7l1 1h0 0c-1 1-1 2-2 3h0c1 1 1 2 1 3s1 2 1 3l-1 1 1 1c-1 1 0 1 0 2h-2c2 4 5 6 9 7 2 2 3 3 5 3 1 1 3 1 5 2h6 3 1l-3 3h1l-4 2h0-6l-6-2c-1-2-4-3-6-4-1-1-3-2-4-3l1-1-2-1c-1 1-1 0-2 0-2 0-3 0-4-1 1 0 1 0 1-1h1l-3-4c1 0 2 0 2 1l1 1h0l-1-2v-4-2l-1-2v-3h0v-3z" class="H"></path><path d="M276 649c1 4 2 8 5 10-1 1-1 0-2 0-2 0-3 0-4-1 1 0 1 0 1-1h1l-3-4c1 0 2 0 2 1l1 1h0l-1-2v-4z" class="W"></path><path d="M275 639h7l1 1-2 1h0c-1 1-2 2-2 3h0v-1h-1-2 0v4l-1-2v-3h0v-3z" class="d"></path><path d="M275 639h7l1 1-2 1h0c-1-1-1-1-2-1l-1 1-1-1h-1 0c0 1 0 1-1 2v-3z" class="D"></path><path d="M283 640h0 0c-1 1-1 2-2 3h0c1 1 1 2 1 3s1 2 1 3l-1 1 1 1c-1 1 0 1 0 2h-2c-1-2-1-3-1-5-1-1-1-2-1-4h0c0-1 1-2 2-3h0l2-1z" class="Z"></path><path d="M279 644h0l2 1 1 3v2c-1 0 0 0-1-1 0 0-1 0-1-1-1-1-1-2-1-4z" class="T"></path><defs><linearGradient id="AT" x1="299.03" y1="669.5" x2="299.174" y2="667.616" xlink:href="#B"><stop offset="0" stop-color="#555354"></stop><stop offset="1" stop-color="#6b6969"></stop></linearGradient></defs><path fill="url(#AT)" d="M283 660c7 5 15 10 24 8h0 1l-4 2h0-6l-6-2c-1-2-4-3-6-4-1-1-3-2-4-3l1-1z"></path><path d="M251 398v-1l2 1c0 1 1 2 2 3-1 1-1 1-1 2l2 2 1 1-1 1-3-3 2 5c1 2 1 3 2 4s1 1 1 2v1s1 1 1 2h-1 0-2l5 5h1l1 2h1c0 1 0 1 1 2l-1 1c1 1 2 1 3 1l-1 1 3 2 3 3h0 5l1 1h-5l2 1c-1 0-2 1-3 1h0c0 1 0 1 1 1h3l-1 1h-2l2 1 3 1h1c1 0 1 0 2 1 0 1 1 1 1 3h-1v1h2 1-2c1 1 2 2 2 3-2 0-5 0-8-1h-2v-1c-2 0-4-1-6-2s-5-2-7-4l-2-2c-2-2-3-3-4-5-1-1-1-2-2-3h-1c0-1 0-2-1-3s-1-2-1-3c-1-1-1-2-1-3l-1-2h-1v1h-1c0-2-1-3-1-5-1-2-2-5-1-8l1-2v-1-2h0c1-1 2-3 4-4h1 1v-2z" class="P"></path><path d="M253 421c2 0 3 1 4 2-1 1-1 2-2 3 0-1-1-2-1-3s-1-2-1-2z" class="f"></path><path d="M257 413c1 1 1 1 1 2v1s1 1 1 2h-1 0-2l-1-1 1-1h1s-1-1-1-2l1-1z" class="I"></path><path d="M257 423l2 2h0l1 2c-1 1-1 1-2 1l-1-1s-1-1-2-1c1-1 1-2 2-3z" class="g"></path><path d="M257 427c0-1 0-1 2-2l1 2c-1 1-1 1-2 1l-1-1z" class="N"></path><path d="M250 408v-1c1 0 2 0 2 1v1h1l1 1c1 1 1 1 1 2v1l-1-2c-1 1-1 1-1 2h0c-1-2-2-3-2-4l-1-1z" class="I"></path><path d="M250 426l1-2h2s1 1 0 2c0 1 0 1-1 2v1h0c0 1 1 2 1 3h-1c0-1 0-2-1-3s-1-2-1-3z" class="R"></path><path d="M268 437l-8-4c-2-1-4-3-4-5 1 1 5 4 7 4s5 2 6 3l-1 2z" class="F"></path><path d="M269 435c1 0 3 1 4 1l2 1c-1 0-2 1-3 1h0c0 1 0 1 1 1h3l-1 1h-2l2 1 1 1-1 1-4-1 1-1c-1 0-3-1-4-2h0 3l-3-2 1-2z" class="Z"></path><path d="M256 433v-1l5 4c1 1 2 1 3 1s3 1 4 1v1h0c1 1 3 2 4 2l-1 1c-1 0-2-1-3-1v1c-1 0-1 0-2-1h-1l-2-2c-3-1-5-4-7-6z" class="F"></path><path d="M260 427l1 1h1 2c1 1 2 1 3 1l-1 1 3 2 3 3h0 5l1 1h-5c-1 0-3-1-4-1-1-1-4-3-6-3-2-2-3-3-5-4 1 0 1 0 2-1z" class="V"></path><path d="M262 428h2c1 1 2 1 3 1l-1 1-4-2z" class="F"></path><path d="M253 416v-1c1 0 2 1 2 2l1 1 5 5h1l1 2h1c0 1 0 1 1 2l-1 1h-2-1l-1-1-1-2h0l-2-2c-1-1-2-2-4-2l-1-1 1-1h0 1l-1-2v-1z" class="Q"></path><path d="M259 425s1-1 2-1c1 2 0 2 0 4l-1-1-1-2h0z" class="R"></path><path d="M253 416v-1c1 0 2 1 2 2l1 1 5 5h1l-1 1h-1c-3-2-5-5-7-8zm-2-18c1 2 1 4 2 6v1h-1c-1-1-2-4-3-6-1 4-1 5 1 8v1l1 1v2c0 1-1 1-1 2 1 2 0 3 1 5 0 1 1 1 0 3h0l-1 1 1 1-1 1v1-1-1h0v-1c0-1-1-2-1-3v3 1l-1-2h-1v1h-1c0-2-1-3-1-5-1-2-2-5-1-8l1-2v-1-2h0c1-1 2-3 4-4h1 1v-2z" class="L"></path><path d="M245 404c2 1 2 1 2 2v5l-1-3-1-1v-1-2z" class="O"></path><path d="M245 407l1 1 1 3v2c1 2 1 5 2 6v3 1l-1-2h-1v1h-1c0-2-1-3-1-5-1-2-2-5-1-8l1-2z" class="e"></path><path d="M248 421h0 0v-1c-2-2-2-4-2-6l1-1c1 2 1 5 2 6v3 1l-1-2z" class="I"></path><path d="M255 435v-2h1c2 2 4 5 7 6l2 2h1c1 1 1 1 2 1v-1c1 0 2 1 3 1l4 1 1-1-1-1 3 1h1c1 0 1 0 2 1 0 1 1 1 1 3h-1v1h2 1-2c1 1 2 2 2 3-2 0-5 0-8-1h-2v-1c-2 0-4-1-6-2s-5-2-7-4l-2-2c-2-2-3-3-4-5z" class="c"></path><path d="M275 441l3 1h1l-1 1h-3l1-1-1-1z" class="K"></path><path d="M275 445c1 0 2 0 3 1 1 2 3 1 4 1 1 1 2 2 2 3-2 0-5 0-8-1h2v-2c-1 0-2-1-3-2h0z" class="o"></path><path d="M263 439l2 2h1c1 1 1 1 2 1s2 1 3 1c1 1 2 1 4 2h0c1 1 2 2 3 2v2h-2-2v-1c-2 0-4-1-6-2 1 0 2 0 3-1h-1l-4-2c-2-1-2-2-3-4z" class="T"></path><path d="M271 443c1 1 2 1 4 2h0c1 1 2 2 3 2v2h-2-2v-1h1l-2-2c-2-1-1-1-2-3z" class="Z"></path><defs><linearGradient id="AU" x1="270.83" y1="441.902" x2="253.814" y2="437.132" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#c8c6c6"></stop></linearGradient></defs><path fill="url(#AU)" d="M255 435v-2h1c2 2 4 5 7 6 1 2 1 3 3 4l4 2h1c-1 1-2 1-3 1-2-1-5-2-7-4l-2-2c-2-2-3-3-4-5z"></path><path d="M256 405l1-1h1 0c3 3 5 3 8 4l4 1c1 1 2 1 4 1l2 1h8 0-1c-1 0-3 0-4 1h-1v1c1 0 1-1 2 0h0c1-1 3 0 4 0h1c0-1 2-1 2-1h1 1 1l1-1h2 0 1l1-1h1l-2 1h2l1 1-1 1h1l2-1v1s-1 0-1 1h0 0 1s1 0 1 1h0l-1 1c-1 0-1 1-1 1h1l-3 2 1 2c-3 1-4 2-6 4-3 1-7 1-11 2h-4 0l-1 1c1 0 2 1 2 1l2 1h1-5l1 1-1 1-1 1v1c-1 0-2 0-2 1h0l-3-3-3-2 1-1c-1 0-2 0-3-1l1-1c-1-1-1-1-1-2h-1l-1-2h-1l-5-5h2 0 1c0-1-1-2-1-2v-1c0-1 0-1-1-2s-1-2-2-4l-2-5 3 3 1-1-1-1z" class="j"></path><path d="M263 425c1 0 2 1 3 2s2 2 4 3h0c-1 0-2 0-3-1-1 0-2 0-3-1l1-1c-1-1-1-1-1-2h-1z" class="T"></path><path d="M275 428h0c1 0 2 1 2 1l2 1h1-5s-1-1-2-1l1 1h-3s1 0 1-1h-1v-1h1l1 1s1 0 1-1h1z" class="K"></path><path d="M259 418c1 1 2 2 4 3v1c-1 0-1 0-1 1h-1l-5-5h2 0 1z" class="T"></path><path d="M275 428c-4-1-8-3-11-5l4 1c2 1 3 1 5 2 1 0 2 1 3 1l-1 1h0z" class="g"></path><path d="M274 430l-1-1c1 0 2 1 2 1l1 1-1 1-1 1v1c-1 0-2 0-2 1h0l-3-3 1-1h1v-1h3z" class="q"></path><path d="M271 430h3v1l-3 1-1-1h1v-1z" class="Z"></path><path d="M270 431l1 1c2 1 2 1 3 1v1c-1 0-2 0-2 1h0l-3-3 1-1z" class="f"></path><path d="M276 423h7 1l-4 2c-1 0-1 0-1 1h-1c-1 0-1 0-2 1h0c-1 0-2-1-3-1-2-1-3-1-5-2 1 0 3 1 4 1 1-1 3-1 4-2z" class="o"></path><path d="M276 423c-4 0-9-1-12-4-1-1-4-3-4-4 1 1 2 2 4 3v-1l-1-2c1 0 2 0 4 1 1 1 3 2 5 2h5 8 2l1 1 1 1-2 1h0l1 1 3-1h0l5-2 1 2c-3 1-4 2-6 4-3 1-7 1-11 2h-4c1-1 1-1 2-1h1c0-1 0-1 1-1l4-2h-1-7z" class="f"></path><path d="M265 419c1-1 2 0 3 0l2 1 6 1c-1 1-2 0-3 1-3-1-5-1-8-3z" class="T"></path><path d="M282 421h5l1 1c-5 0-8 1-13 0h0l7-1z" class="b"></path><path d="M263 415c1 0 2 0 4 1 1 1 3 2 5 2h1v1h-2l-1 1-2-1c-1 0-2-1-3 0h-1v-1-1l-1-2z" class="K"></path><defs><linearGradient id="AV" x1="292.109" y1="423.418" x2="282.36" y2="424.428" xlink:href="#B"><stop offset="0" stop-color="#39393a"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#AV)" d="M296 419l1 2c-3 1-4 2-6 4-3 1-7 1-11 2h-4c1-1 1-1 2-1h4v-1h0c3-2 8-2 9-3v-1h0l5-2z"></path><path d="M272 418h5 8 2l1 1 1 1-2 1h0-5l-7 1h-2c1-1 2 0 3-1l-6-1 1-1h2v-1h-1z" class="g"></path><path d="M285 418h2l1 1 1 1-2 1h0-5 0v-1h-4 0c1-1 2-1 3-1v-1h-4 8z" class="Y"></path><path d="M285 418h2l1 1 1 1-2 1h0-5 0 2l-1-1h1-1c1-1 2-1 3-2h-1z" class="r"></path><path d="M256 405l1-1h1 0c3 3 5 3 8 4l4 1c1 1 2 1 4 1l2 1h8 0-1c-1 0-3 0-4 1h-1v1c1 0 1-1 2 0h0c1-1 3 0 4 0h1c0-1 2-1 2-1h1 1 1l1-1h2 0 1l1-1h1l-2 1h2l1 1-1 1h1l2-1v1s-1 0-1 1h0 0 1s1 0 1 1h0l-1 1c-1 0-1 1-1 1h1l-3 2-5 2h0l-3 1-1-1h0l2-1-1-1-1-1h-2-8-5c-2 0-4-1-5-2-2-1-3-1-4-1s-2-1-3-2-2-1-3-2v-3-1l-1 1s0 1 1 1v1c-1-1-1 0-1-1h-1l-2-5 3 3 1-1-1-1z" class="S"></path><path d="M279 415h4-1v1l1 1h-4c1 0 1 0 2-1-1 0-2 0-3-1h1z" class="W"></path><path d="M275 415h4-1c1 1 2 1 3 1-1 1-1 1-2 1h-3s0-1-1-1h0v-1z" class="Y"></path><path d="M267 416h0 1c0-1 0-1 1-1h1 1 4 0v1h0c1 0 1 1 1 1h-4v1c-2 0-4-1-5-2z" class="c"></path><path d="M297 413l2-1v1s-1 0-1 1h0 0 1s1 0 1 1h0l-1 1c-1 0-1 1-1 1h1l-3 2-5 2h0l-3 1-1-1h0l2-1-1-1-1-1c1-1 1-1 2-1 0 1 0 1-1 1 1 1 1 1 2 1v-1h1l4-2v-1c0-1 1-1 1-2 1 0 1 1 1 0z" class="k"></path><path d="M290 418c3 0 5-1 7-2-2 2-6 4-10 5l2-1-1-1-1-1c1-1 1-1 2-1 0 1 0 1-1 1 1 1 1 1 2 1v-1z" class="N"></path><defs><linearGradient id="AW" x1="287.345" y1="416.744" x2="280.865" y2="409.82" xlink:href="#B"><stop offset="0" stop-color="#2e2d30"></stop><stop offset="1" stop-color="#6b6a68"></stop></linearGradient></defs><path fill="url(#AW)" d="M293 411h0 1l1-1h1l-2 1h2l1 1-1 1h1c0 1 0 0-1 0 0 1-1 1-1 2-4 1-7 1-11 1l-1-1h0-4-4 0v-1l-1-1c2 0 5 1 7 0h3 1c0-1 2-1 2-1h1 1 1l1-1h2z"></path><path d="M284 413h1c0-1 2-1 2-1h1 1 1l1-1h2l1 1h2c-1 1-2 1-3 2h0v-2c-4 1-8 2-12 1h3z" class="h"></path><defs><linearGradient id="AX" x1="293.074" y1="412.444" x2="288.334" y2="416.298" xlink:href="#B"><stop offset="0" stop-color="#131311"></stop><stop offset="1" stop-color="#2a282c"></stop></linearGradient></defs><path fill="url(#AX)" d="M293 411h0 1l1-1h1l-2 1h2l1 1-1 1h1c0 1 0 0-1 0 0 1-1 1-1 2-4 1-7 1-11 1l-1-1c4 0 7 0 10-1h0c1-1 2-1 3-2h-2l-1-1z"></path><path d="M256 405l1-1h1 0c3 3 5 3 8 4l4 1c1 1 2 1 4 1l2 1h8 0-1c-1 0-3 0-4 1h-1v1c1 0 1-1 2 0h0c1-1 3 0 4 0h-3c-2 1-5 0-7 0l1 1v1h-4-1-1c-1 0-1 0-1 1h-1 0c-2-1-3-1-4-1s-2-1-3-2-2-1-3-2v-3-1l-1 1s0 1 1 1v1c-1-1-1 0-1-1h-1l-2-5 3 3 1-1-1-1z" class="T"></path><path d="M257 408c2 2 3 3 4 3 2 1 3 2 4 3h4c1 0 1 0 2 1h0-1-1c-1 0-1 0-1 1h-1 0c-2-1-3-1-4-1s-2-1-3-2-2-1-3-2v-3z" class="g"></path><path d="M258 404c3 3 5 3 8 4l4 1c-1 1-2 2-2 3-2-1-5-1-6-3-1-1-3-2-4-2v-1-2z" class="f"></path><defs><linearGradient id="AY" x1="277.331" y1="410.559" x2="269.366" y2="412.044" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#494748"></stop></linearGradient></defs><path fill="url(#AY)" d="M270 409c1 1 2 1 4 1l2 1h8 0-1c-1 0-3 0-4 1h-1v1c1 0 1-1 2 0h0c1-1 3 0 4 0h-3c-2 1-5 0-7 0l1 1v1h-4 0c-1-1-1-1-2-1h-4c2 0 3 0 4-1v-1h-1c0-1 1-2 2-3z"></path><path d="M269 412c1 0 4 0 5 1l1 1v1h-4 0c-1-1-1-1-2-1h-4c2 0 3 0 4-1v-1z" class="i"></path><path d="M394 639c1-1 4 0 6-1l17 1s0 1-1 2v4c0 1 0 3-1 4l1 1c-1 3-2 6-4 8-5 5-13 10-20 10h-3-1l-1 1v1h-2l-6-3c0-1 1-1 1-1l-2-1c-2-2-4-3-5-4v-1l-1-1c0-1-1-2-2-3 0 0 1-1 2-1l2 2c0-1 0-2-1-2h0 1c1 1 3 1 4 1 2-1 4 0 5-1v-1c1 0 3 0 4-1v-1h-1l-1-1h1v-1c1-1 2-1 2-2 0-2 2-5 3-7v1c1-1 1-2 2-3h1z" class="J"></path><path d="M412 648c0-3 1-5 0-8v-1h3l1 2v4c0 1 0 3-1 4 0 2-1 2-2 4h-1c-1 0-1-1-1-2l1-3z" class="t"></path><path d="M407 646c1-2 1-4 0-5 1 0 2 0 2 1 1 3 1 6 0 9-1 1-2 3-4 4l-1-1 2-2h0-1c-2 1-3 3-6 4-2 1-7 3-9 2h-1c1-1 3-1 4-1l2-2s1 0 2-1v-2s0-1-1-1l-1-1c0-1 1-1 2-2l3-3 1-2c2 1 2 1 4 3 0-1 1-2 2-2v2z" class="j"></path><path d="M397 652h1c1 0 2 0 3-1l2 2c-3 1-7 3-10 4l2-2s1 0 2-1v-2h0z" class="D"></path><path d="M400 645l1-2c2 1 2 1 4 3 0-1 1-2 2-2v2 2c-1 2-3 4-4 5l-2-2c-1 1-2 1-3 1h-1 0s0-1-1-1l-1-1c0-1 1-1 2-2l3-3z" class="B"></path><path d="M397 652c2-2 3-2 5-3 2 0 3-1 5-1-1 2-3 4-4 5l-2-2c-1 1-2 1-3 1h-1z" class="O"></path><path d="M400 645l1-2c2 1 2 1 4 3-1 0-3 2-4 2-1 1-3 0-4 0l3-3z" class="L"></path><path d="M391 642c1-1 1-2 2-3h1c1 1 1 2 2 4h1v-3h0 2c0 1 0 1 1 1v2 2l-3 3c-1 1-2 1-2 2l1 1c1 0 1 1 1 1v2c-1 1-2 1-2 1l-2 2c-1 0-3 0-4 1-1-1-3 0-4 0-2 0-4 0-6-1l-1-1c2-1 4 0 5-1v-1c1 0 3 0 4-1v-1h-1l-1-1h1v-1c1-1 2-1 2-2 0-2 2-5 3-7v1z" class="P"></path><path d="M391 642c1-1 1-2 2-3h1c1 1 1 2 2 4-1 1-2 3-3 4l-3 1v-1-1l1-3v-1z" class="B"></path><path d="M391 643c1 1 2 1 2 2s-1 1-2 1h-1l1-3z" class="X"></path><path d="M397 643v-3h0 2c0 1 0 1 1 1v2 2l-3 3c-1 1-2 1-2 2-1 0-2 2-2 2-1 1-2 1-3 2-1-1-1-2-2-3 1 0 1 0 1-1 2 0 3-1 4-3 1-1 2-3 3-4h1z" class="Q"></path><path d="M397 643v-3h0 2c0 1 0 1 1 1v2l-3 3h-1c0-1 0-2 1-3z" class="L"></path><path d="M395 650l1 1c1 0 1 1 1 1v2c-1 1-2 1-2 1l-2 2c-1 0-3 0-4 1-1-1-3 0-4 0-2 0-4 0-6-1l-1-1c2-1 4 0 5-1 2-1 5-1 7-1 1-1 2-1 3-2 0 0 1-2 2-2z" class="U"></path><path d="M395 650l1 1-3 3h0v-2s1-2 2-2z" class="D"></path><path d="M406 656c2-1 3-3 3-4 2-3 2-7 1-10v-2h1 0c1 1 1 2 1 3v5l-1 3c-2 3-3 5-5 7l-1 1c-1 1-1 2-2 3h-2l-1 1h-1c-1-1 0-1-1-1-3 1-7 3-9 2h-1v-1l-9-3 1-1c-2 0-4-1-6-2 0-1 0-2-1-2h0 1c1 1 3 1 4 1l1 1c2 1 4 1 6 1 1 0 3-1 4 0h1c2 1 7-1 9-2 3-1 4-3 6-4h1 0l-2 2 1 1h0l1 1z" class="T"></path><path d="M398 662h1c1-1 2-1 3-2-1 1-1 2-1 2l-1 1h-1c-1-1 0-1-1-1z" class="L"></path><path d="M405 659c-1 1-1 2-2 3h-2s0-1 1-2c1 0 2 0 3-1z" class="B"></path><path d="M405 655l1 1c-1 1-2 2-4 3h0l-1-2 4-2z" class="D"></path><path d="M401 657l1 2c-2 1-7 3-10 3h0l9-5z" class="M"></path><path d="M380 659l3 1c3 1 5 2 8 2v1h0l-3 1v-1l-9-3 1-1z" class="Y"></path><path d="M374 657c0-1 0-2-1-2h0 1c1 1 3 1 4 1l1 1c2 1 4 1 6 1 1 0 3-1 4 0h1c2 1 7-1 9-2h1c-1 2-4 3-6 3-1 0-2 1-3 1-2 1-5 0-8 0l-3-1c-2 0-4-1-6-2z" class="D"></path><path d="M411 651c0 1 0 2 1 2h1c1-2 2-2 2-4l1 1c-1 3-2 6-4 8-5 5-13 10-20 10h-3-1l-1 1v1h-2l-6-3c0-1 1-1 1-1l-2-1c-2-2-4-3-5-4v-1l-1-1c0-1-1-2-2-3 0 0 1-1 2-1l2 2c2 1 4 2 6 2l-1 1 9 3v1h1c2 1 6-1 9-2 1 0 0 0 1 1h1l1-1h2c1-1 1-2 2-3l1-1c2-2 3-4 5-7z" class="G"></path><path d="M379 667c0-1 1-1 1-1 3 1 5 2 8 2l-1 1v1h-2l-6-3z" class="Y"></path><path d="M379 663v-1l-2-2h2l9 3c0 1-1 1-1 1s-1 0-1 1c-2-1-5-1-7-2z" class="P"></path><path d="M372 655l2 2c2 1 4 2 6 2l-1 1h-2l2 2v1l-6-3-1-1c0-1-1-2-2-3 0 0 1-1 2-1z" class="I"></path><path d="M255 378h2c1-1 2-1 3-1h1 1 0l-1 2c-1 1-1 2-2 3 3 0 6 0 8 1h1v1h1c2 0 3 1 5 0v1 1h-1c1 1 1 1 2 1s2 0 2 1c1 0 2-1 2-1 1 1 0 1 1 1l-1 1h2c1-1 2-2 3-2l1 1 2-1 1 1c2 0 3-1 4-2 0-1 0-1 1-2v1l1 1h0l-1 4v3h1 2l-2 2h-1c-1 1-1 4-1 5h3l-1 1h-2v1 2l-1 4 1 2c-3 1-6 1-8 1h-8l-2-1c-2 0-3 0-4-1l-4-1c-3-1-5-1-8-4h0-1l-1 1-2-2c0-1 0-1 1-2-1-1-2-2-2-3v-1c-1-1-1-1-1-2l1-1c-1-1-1-2-2-2 0-1 1-2 1-2-1 0-1 0-2-1 0 1-1 1-1 2-1 1-1 0-1 0h0v-1l1-2 1 1 1-1c-1-1-1-2-1-2 0-2-1-2 0-3h-1v-1l1-1v-2h2c1-1 1-2 3-2v1z" class="E"></path><path d="M256 389c1-2 1-3 2-5 0 1 1 1 1 1l1 1-1 1v1l2 1 1 1h-1l-4-1h0-1zm2 11h2c2 0 3 1 4 2v1h0l-1 1c1 2 2 2 4 3l1 1h-2c-3-1-5-1-8-4h0-1l-1 1-2-2c0-1 0-1 1-2l2 1c2 0 4 2 6 2-1-1-3-3-5-4z" class="R"></path><defs><linearGradient id="AZ" x1="263.962" y1="401.886" x2="260.488" y2="394.111" xlink:href="#B"><stop offset="0" stop-color="#918f91"></stop><stop offset="1" stop-color="#b4b3b2"></stop></linearGradient></defs><path fill="url(#AZ)" d="M255 393c2 1 2 2 4 2l12 5h-3v1h0c-2 0-4 0-6-1-1-1-1-1-2 0h-2 0c-1-2-3-3-4-6h0-1v-1h2 0z"></path><path d="M255 378h2c1-1 2-1 3-1h1l-1 2-4 6-2 4h-1l-1 1c-1 0-1 0-2-1 0 1-1 1-1 2-1 1-1 0-1 0h0v-1l1-2 1 1 1-1c-1-1-1-2-1-2 0-2-1-2 0-3h-1v-1l1-1v-2h2c1-1 1-2 3-2v1z" class="R"></path><path d="M255 378h2c1-1 2-1 3-1h1l-1 2-4 6v-1-1h-1l-1 1h-2c0 1 0 0-1 0h0v-2h0l3-3 1-1z" class="Z"></path><path d="M255 378h2c1-1 2-1 3-1h1l-1 2h-2l-2 2h0-1c0-1 0-1-1-2h0l1-1z" class="W"></path><path d="M260 400c1-1 1-1 2 0 2 1 4 1 6 1h0v-1h3 0 10 1v2c1 0 2 0 2 1l6-1 1 1h0l1-1v2l-1 4 1 2c-3 1-6 1-8 1h-8l-2-1c-2 0-3 0-4-1l-4-1h2l-1-1c-2-1-3-1-4-3l1-1h0v-1c-1-1-2-2-4-2z" class="i"></path><path d="M268 401h0v-1h3 0 3v1h-6z" class="T"></path><path d="M281 400h1v2c1 0 2 0 2 1h-3-5l1-2h4v-1z" class="g"></path><defs><linearGradient id="Aa" x1="275.099" y1="403.75" x2="268.479" y2="403.698" xlink:href="#B"><stop offset="0" stop-color="#535252"></stop><stop offset="1" stop-color="#6c6a6b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M267 404c1 0 1-1 1-1h2 6 5 0c-2 1-3 1-4 1h-1c2 1 4 0 6 1h-6c-3 0-6 0-9-1z"></path><path d="M284 403l6-1 1 1h-1c0 1-1 1-2 1-2 0-4 1-6 1-2-1-4 0-6-1h1c1 0 2 0 4-1h0 3z" class="S"></path><defs><linearGradient id="Ab" x1="274.707" y1="408.614" x2="265.416" y2="402.143" xlink:href="#B"><stop offset="0" stop-color="#7b7a7b"></stop><stop offset="1" stop-color="#a2a0a0"></stop></linearGradient></defs><path fill="url(#Ab)" d="M264 402c1 1 2 1 3 2 3 1 6 1 9 1l1 1v1c-1 0-1 1-2 1 0 1-1 1-1 2h0c-2 0-3 0-4-1l-4-1h2l-1-1c-2-1-3-1-4-3l1-1h0v-1z"></path><defs><linearGradient id="Ac" x1="289.527" y1="408.467" x2="278.128" y2="406.336" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#Ac)" d="M292 402v2l-1 4 1 2c-3 1-6 1-8 1h-8l-2-1h0c0-1 1-1 1-2 1 0 1-1 2-1v-1l-1-1h6c2 0 4-1 6-1 1 0 2 0 2-1h1 0l1-1z"></path><path d="M290 406c-1 0-2 1-3 0h0c1-1 2-1 3-1v1h0z" class="b"></path><path d="M292 402v2l-1 4h0l-1 1v-3h0c1-1 0-2 0-3h1 0l1-1zm-15 4h1 1l-1 1v1c-1 1 0 2-2 3l-2-1h0c0-1 1-1 1-2 1 0 1-1 2-1v-1z" class="o"></path><path d="M259 382c3 0 6 0 8 1h1v1h1c2 0 3 1 5 0v1 1h-1c1 1 1 1 2 1s2 0 2 1c1 0 2-1 2-1 1 1 0 1 1 1l-1 1h2c1-1 2-2 3-2l1 1 2-1 1 1c2 0 3-1 4-2 0-1 0-1 1-2v1l1 1h0l-1 4v3h1 2l-2 2h-1c-1 1-1 4-1 5h3l-1 1h-2v1l-1 1h0l-1-1-6 1c0-1-1-1-2-1v-2h-1-10 0l-12-5c-2 0-2-1-4-2l-1-2h0 1 2c0-1-1-1-1-1h0v-1h1 0l4 1h1l-1-1-2-1v-1l1-1-1-1s-1 0-1-1l1-2z" class="f"></path><path d="M275 392h2v1h0-2v-1z" class="N"></path><path d="M256 389h1 0v1c2 1 7 2 10 3-3 1-6-1-9-1h0c4 2 8 3 12 3 0 1 0 1-1 1h-3c-2 0-3 0-5-1h-2c-2 0-2-1-4-2l-1-2h0 1 2c0-1-1-1-1-1h0v-1z" class="I"></path><path d="M277 388c1 0 2-1 2-1 1 1 0 1 1 1l-1 1h-1l1 1h0c-1 1-2 1-3 1l-2 1h1v1h2c-1 1-2 1-3 0-2 0-5-1-7 0-3-1-8-2-10-3v-1l4 1h1l-1-1h3c1 0 3 0 5 1h1v-1h3c1 0 3-1 4-1z" class="r"></path><path d="M261 389h3c1 0 3 0 5 1v1h-2c-2 0-4 0-6-1h1l-1-1z" class="F"></path><path d="M277 388c1 0 2-1 2-1 1 1 0 1 1 1l-1 1h-1l1 1h0c-1 1-2 1-3 1l-2 1c-2 0-5 0-7-1h2v-1h1v-1h3c1 0 3-1 4-1z" class="K"></path><path d="M277 388c1 0 2-1 2-1 1 1 0 1 1 1l-1 1h-1c-3 1-6 1-8 1v-1h3c1 0 3-1 4-1z" class="S"></path><path d="M259 382c3 0 6 0 8 1h1v1h1c2 0 3 1 5 0v1 1h-1c1 1 1 1 2 1s2 0 2 1c-1 0-3 1-4 1h-3v1h-1c-2-1-4-1-5-1h-3l-2-1v-1l1-1-1-1s-1 0-1-1l1-2z" class="o"></path><path d="M264 387h1c2 1 3 1 5 1v1 1h-1c-2-1-4-1-5-1v-2z" class="g"></path><path d="M269 384c2 0 3 1 5 0v1 1h-1c1 1 1 1 2 1s2 0 2 1c-1 0-3 1-4 1l1-1h1c-1 0-2-1-3-1l-3-3z" class="c"></path><path d="M264 385h2v1l-1 1h-1v2h-3l-2-1v-1l1-1-1-1h5z" class="j"></path><path d="M259 387c2 0 3 1 4 0h1v2h-3l-2-1v-1z" class="K"></path><path d="M259 382c3 0 6 0 8 1h1v1l-3-1c-1 1-1 1-1 2h-5s-1 0-1-1l1-2z" class="f"></path><path d="M292 386c0-1 0-1 1-2v1l1 1h0l-1 4v3h1 2l-2 2h-1c-1 1-1 4-1 5h3l-1 1h-2v1l-1 1h0l-1-1-6 1c0-1-1-1-2-1v-2h-1-10 0l-12-5h2c2 1 3 1 5 1h3c1 0 1 0 1-1l4 1h3l3-2-1-1v-1h4l1-1c1-2 2-2 4-3 2 0 3-1 4-2z" class="Y"></path><path d="M282 399c3-1 6 0 9-1v2h-1 0c-2 1-4 1-6 1 0 0-1-1-2-1v-1z" class="N"></path><path d="M279 398l9-2c1 0 2-1 3 0v2c-3 1-6 0-9 1-2 1-5 1-7 1l-1-1v-1h5 0z" class="m"></path><path d="M292 386c0-1 0-1 1-2v1l1 1h0l-1 4v3h1 2l-2 2h-1c-1 1-1 4-1 5h3l-1 1h-2v1l-1 1v-3-2-2c-1-1-2 0-3 0l-9 2-6-1-7-1h3c1 0 1 0 1-1l4 1h3c2 1 5 0 7 0h1c2-1 4-1 5-1h1c1-1 0-2 0-3l1-5v-1z" class="K"></path><path d="M270 395l4 1h3v1h-4l-7-1h3c1 0 1 0 1-1z" class="F"></path><path d="M288 388c2 0 3-1 4-2v1l-1 5c0 1 1 2 0 3h-1c-1 0-3 0-5 1h-1c-2 0-5 1-7 0l3-2-1-1v-1h4l1-1c1-2 2-2 4-3z" class="g"></path><path d="M284 391h2c1 1 2 1 4 1l-5 1h-1l-1-1 1-1z" class="h"></path><path d="M280 394l-1-1v-1h4l1 1h1c-1 0-2 1-4 0-1 0-1 0-1 1z" class="W"></path><path d="M288 388c2 0 3-1 4-2v1l-1 5h-1c-2 0-3 0-4-1h-2c1-2 2-2 4-3z" class="k"></path><path d="M288 388c2 0 3-1 4-2v1c-2 1-4 3-6 4h-2c1-2 2-2 4-3z" class="p"></path><path d="M447 565h0c3-1 5-1 8-1 0 0 1 1 2 1s4 1 5 1h1 3c-1 1-1 2-1 3 1 0 1 1 2 2s2 1 3 2c1 2 1 4 3 5l-1 1-1 1c0 1 0 2 1 3h0v-1h0c1 0 2 0 3 1 0 1 1 1 2 2 1 2 2 4 3 5 1 2 1 2 2 3h1c0 1 0 2-1 3v2c-1 1-1 2-2 3h-1 0c-1 2-2 3-4 4h-1l-1 1c-6 0-11-1-17-2-4-1-13-2-15-6v-2-1l-2 3c-1-1-2-1-3-2h0c1-1 1-2 1-3v-1c1-1 2-2 4-3l4-4h2 0l-1-1v-3l1-2 1 1 1-1-1-1 3-2c1-1 1-1 3-1 1 0 1 0 2-1 0 1 1 1 1 1l1-1c-2-1-4-2-6-2v-1h2v-1c2 1 3 1 4 2s3 3 5 2c0 0 0-1-1-2h1l1 1h0l1-1-4-3c-3-3-10-3-14-3 0 1 0 1-1 1h0-3c0 1-1 1-2 1 1-1 2-1 4-1 0-1 1-1 2-2h0z" class="G"></path><path d="M475 588l-2 1c-1-1-1-1-1-2l1-1c1 1 2 1 2 2z" class="J"></path><path d="M463 566h3c-1 1-1 2-1 3h0c-1-1-2-1-2-2v-1z" class="B"></path><path d="M466 583h2l-2 7-2-1h1c1-2 1-3 1-6h0z" class="P"></path><path d="M466 575l2 4v4h-2c0-2 0-4-1-6h0v-1l1-1z" class="O"></path><path d="M452 588c1 0 2 1 3 1 1 1 2 1 1 3-1 1-3 4-4 4h0l3-4-1-1-3-3h0 1z" class="f"></path><path d="M464 589l2 1c-1 2-3 4-5 5v1c-2 1-6 1-8 3h0v-2h0c3 0 5-1 6-3 1 0 1 0 2-1h1c1-1 2-3 2-4z" class="Q"></path><path d="M462 600c1 1 1 2 2 2 1-1 2-2 4-3 0-1 0-2-1-3s-2-1-3-2h-1c1-1 1-1 2-1 1 1 3 2 4 3l1 3 1 1h-1 0c-1 0-2 0-3 1l-2 2h-2c-1-1-1-1-1-3z" class="I"></path><path d="M462 579l1 1v1c0 1 1 1 1 2h2c0 3 0 4-1 6h-1c0 1-1 3-2 4l-1-1c0-2 1-3 1-5 0-1 1-3 0-4v-4z" class="D"></path><path d="M454 570c2 1 3 1 4 2s3 3 5 2c0 0 0-1-1-2h1l1 1h0l2 2-1 1v1h0c1 2 1 4 1 6h0-2c0-1-1-1-1-2v-1l-1-1h0c-1-1-1-1-1-2-1 1-2 1-2 2v1h-1v-1s0-1 1-1l-1-1c0-2-2-2-4-2 1 0 1 0 2-1 0 1 1 1 1 1l1-1c-2-1-4-2-6-2v-1h2v-1z" class="B"></path><path d="M464 573h0l2 2-1 1-1-1v-2z" class="J"></path><path d="M437 592c1-1 2-2 4-3l4-4h2c0 1 0 1 1 1h2l1 1 1 1h-1c-3 0-4 1-6 2-1 2-2 4-4 6h0v-1l-2 3c-1-1-2-1-3-2h0c1-1 1-2 1-3v-1z" class="Q"></path><path d="M441 595v-3h0c-1 1-2 2-2 4h0-1 0c0-2 1-3 2-4 1 0 2-1 3-2 2-2 4-3 7-4l1 1 1 1h-1c-3 0-4 1-6 2-1 2-2 4-4 6h0v-1z" class="p"></path><path d="M451 576c1-1 1-1 3-1s4 0 4 2l1 1c-1 0-1 1-1 1v1h1v-1c0-1 1-1 2-2 0 1 0 1 1 2h0v4c1 1 0 3 0 4 0 2-1 3-1 5l-1-1h-1c-1-2-2-3-3-3l-1 1c-1 0-2-1-3-1l-1-1-1-1h-2c-1 0-1 0-1-1h0l-1-1v-3l1-2 1 1 1-1-1-1 3-2z" class="t"></path><path d="M458 580h1v-1c0-1 1-1 2-2 0 1 0 1 1 2h0v4c1 1 0 3 0 4v-1-2h-1 0s-1-1-1-2-1-1-2-2z" class="H"></path><path d="M451 576c1-1 1-1 3-1 0 1-1 1-1 2h0c-1 1-1 1-1 2h-1v3 1c2 1 3 3 5 5l-1 1c-1 0-2-1-3-1l-1-1-1-1h-2c-1 0-1 0-1-1h0l-1-1v-3l1-2 1 1 1-1-1-1 3-2z" class="e"></path><path d="M447 585l1-1v1c2 0 3 0 4 1v1h-1l-1-1h-2c-1 0-1 0-1-1h0z" class="J"></path><path d="M447 579l1 1v4l-1 1-1-1v-3l1-2z" class="U"></path><path d="M451 576c1-1 1-1 3-1 0 1-1 1-1 2h0c-1 1-1 1-1 2h-1 0c0 1-1 1-1 2 0 2 0 2 1 3h0-2v-5h0l-1-1 3-2z" class="C"></path><path d="M472 583v-1h0c1 0 2 0 3 1 0 1 1 1 2 2 1 2 2 4 3 5 1 2 1 2 2 3h1c0 1 0 2-1 3v2c-1 1-1 2-2 3h-1 0c-1 2-2 3-4 4h-1l-1 1c-6 0-11-1-17-2l1-2-1-1v-2h1 0c1 1 2 2 4 2h0v-1-1l1 1c0 2 0 2 1 3h2l2-2c1-1 2-1 3-1h0 1l-1-1 1-1c1-1 1-1 2-1l1-1v-1h1 2v-2c-1-1-2 0-3 0v-1c1-1 1-2 1-3v-1c0-1-1-1-2-2h1c0-1 0-1-1-1v-2h-1z" class="M"></path><path d="M467 601c1-1 2-1 3-1 0 1 0 2-1 3 0 1-1 1-2 1h0v-3z" class="C"></path><path d="M472 583v-1h0c1 0 2 0 3 1 0 1 1 1 2 2 1 2 2 4 3 5v5 1c0 1-2 2-2 4h0l-1 1c0 1 0 0-1 1h0c0-3 2-4 2-7v-1-1c0-2-1-6-3-8l-2-2h-1z" class="R"></path><path d="M480 590c1 2 1 2 2 3h1c0 1 0 2-1 3v2c-1 1-1 2-2 3h-1 0c-1 2-2 3-4 4h-1c0-1 1-2 2-3h0c1-1 1 0 1-1l1-1h0c0-2 2-3 2-4v-1-5z" class="J"></path><path d="M241 406h0v3c1 0 1-1 1-1v-1h2l1-1v1l-1 2c-1 3 0 6 1 8 0 2 1 3 1 5h1v-1h1l1 2c0 1 0 2 1 3 0 1 0 2 1 3s1 2 1 3l2 3 1 3c0 2 0 3 1 5v1h-1c-1-1-1-2-2-3l-1 1-2-2 3 8v1c0 1 0 1-1 1 0 1 1 2 1 3v1 1 4 4c1 1 1 3 1 4v1l-3-9h-2c0 1 0 2-1 3v1l1 6v1c1 1 1 0 2 1s1 1 1 2v1 1l2 2 22 24h0c-2 0-5-3-6-4l-17-14c-4-2-8-5-12-8l-1 1c0-1-1-1-2-2l-4-4c-1-1-2-3-3-6l-1-2c-1-2-1-5-1-8h-1v-6c1-2 2-4 4-5l-1-1c1-1 1-2 2-4l-1-1 2-3c0-2 2-8 1-9l1-1h0 0c1 0 1-1 2-2h0l1-4c0-1-1-1-2-2l1-2c1-2 1-3 2-4v-1-1-1l1-1z" class="H"></path><path d="M240 467c2 0 3 1 4 1 1 1 3 2 4 2h1c1 1 1 0 2 1s1 1 1 2v1 1l2 2c-3 0-4-2-6-3-3-2-7-3-10-6v-1h2z" class="M"></path><path d="M249 470c1 1 1 0 2 1s1 1 1 2v1 1c-1-2-2-3-4-5h1z" class="l"></path><path d="M235 442h2l2 1v2c-1 2-3 4-4 6l1 1h0 2 0c0 1 1 1 2 2v2h0v1h-1-1s0 1-1 1h-1v2c0 1 0 2 1 3v1c0 1 1 1 1 2v1 1c-2 0-2-1-3-2s-2-2-2-3h0c1-1 0-2 0-2 0-1 0-3-1-4v1-1-1-2h0v-2-1l3-3h1v-1h-1l-1 1-3 4v7 1c1 1 1 1 1 2s1 3 1 4c1 4 5 7 8 9h0l-1 1c0-1-1-1-2-2l-4-4c-1-1-2-3-3-6l-1-2c-1-2-1-5-1-8h-1v-6c1-2 2-4 4-5l2-1h1z" class="B"></path><path d="M234 460l1-1 1 1c0 1 0 2 1 3v1c0 1 1 1 1 2v1h0c-2-1-3-4-4-7z" class="l"></path><path d="M235 451l1 1h0 2 0c0 1 1 1 2 2v2h0v1h-1-1s0 1-1 1h-1v2l-1-1-1 1c-1-1-1-2-1-3 0-2 1-5 2-6z" class="b"></path><path d="M236 452h2 0c0 1 1 1 2 2v2h0v1h-1-1s0 1-1 1h-1v-6z" class="l"></path><path d="M238 452c0 1 1 1 2 2v2h-2l-1-1c0-1 0-2 1-3z" class="p"></path><path d="M235 442h2c-1 1-2 1-3 2h0c1 0 1 1 2 1l-1 1c-2 2-4 5-5 8 0 2 0 4 1 6 0 2 1 4 2 6 1 4 5 7 8 9h0l-1 1c0-1-1-1-2-2l-4-4c-1-1-2-3-3-6l-1-2c-1-2-1-5-1-8h-1v-6c1-2 2-4 4-5l2-1h1z" class="J"></path><path d="M235 442h2c-1 1-2 1-3 2h0c1 0 1 1 2 1l-1 1-1-1c-1 1-1 2-2 3l-2 2-2-2c1-2 2-4 4-5l2-1h1z" class="L"></path><path d="M244 437c1 0 1 0 2-1v3l1 4c1 1 1 2 2 2 1 2 1 4 1 6 1 1 2 2 3 4v4 4c1 1 1 3 1 4v1l-3-9h-2c0 1 0 2-1 3v1l1 6v1h-1c-1 0-3-1-4-2-1 0-2-1-4-1h-2v-1c0-1-1-1-1-2v-1c-1-1-1-2-1-3v-2h1c1 0 1-1 1-1h1 1v-1h0v-2c-1-1-2-1-2-2h0-2 0l-1-1c1-2 3-4 4-6v-2h1s1 0 1-1c1 0 1-2 2-2l1-1v-2z" class="N"></path><path d="M241 452l1-2 1 1c-1 1-1 2-1 3v1l-1-1v-2h0z" class="W"></path><path d="M243 461c1-1 0-3 2-3v-1 5l-1-1h0-1z" class="p"></path><path d="M240 452h1v2l1 1c0 2 0 3 1 4h-2c0-1 0-2-1-3h0 0v-2-2z" class="S"></path><path d="M240 452h1v2 2h-1 0 0v-2-2z" class="N"></path><path d="M240 456h0c1 1 1 2 1 3h2v2h1 0l1 1c-1 0-1 1-1 1-1 0-2 1-3 1l-1-1c-1-1-2-3-3-5 1 0 1-1 1-1h1 1v-1z" class="m"></path><path d="M237 458c1 0 1-1 1-1h1c0 2 0 4 1 6-1-1-2-3-3-5z" class="n"></path><path d="M236 458h1c1 2 2 4 3 5l1 1c1 0 2-1 3-1v3c-1 1-3 0-4 1h-2v-1c0-1-1-1-1-2v-1c-1-1-1-2-1-3v-2z" class="k"></path><path d="M247 454l1-2c0 1-1 2 0 3s1 1 2 1c0 1 0 2 1 3h-2c0 1 0 2-1 3v1l1 6v1h-1c-1 0-3-1-4-2h0c1-1 1-2 1-4l2-10z" class="m"></path><path d="M249 469c-1 0-1 0-2-1 0-2 0-4 1-5l1 6z" class="W"></path><defs><linearGradient id="Ad" x1="239.61" y1="451.11" x2="243.579" y2="439.916" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#Ad)" d="M240 443s1 0 1-1c1 0 1-2 2-2l1-1c1 2 2 5 1 7v1-3c-1 1-1 6-1 7h-1l-1-1-1 2h0-1v2c-1-1-2-1-2-2h0-2 0l-1-1c1-2 3-4 4-6v-2h1z"></path><path d="M240 451v-1h1v2h0-1v-1z" class="V"></path><path d="M240 451v1 2c-1-1-2-1-2-2h0 1l1-1z" class="W"></path><defs><linearGradient id="Ae" x1="248.839" y1="447.874" x2="241.74" y2="443.852" xlink:href="#B"><stop offset="0" stop-color="#494947"></stop><stop offset="1" stop-color="#767477"></stop></linearGradient></defs><path fill="url(#Ae)" d="M244 437c1 0 1 0 2-1v3l1 4c1 1 1 2 2 2 1 2 1 4 1 6 1 1 2 2 3 4v4 4c1 1 1 3 1 4v1l-3-9c-1-1-1-2-1-3-1 0-1 0-2-1s0-2 0-3l-1 2h-3v-2c1-2 1-3 1-4v-1-1c1-2 0-5-1-7v-2z"></path><path d="M247 443c1 1 1 2 2 2 1 2 1 4 1 6 1 1 2 2 3 4v4 4c1 1 1 3 1 4v1l-3-9c-1-1-1-2-1-3-1-4-2-8-3-13z" class="K"></path><defs><linearGradient id="Af" x1="255.901" y1="431.368" x2="237.195" y2="427.112" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#cdcccd"></stop></linearGradient></defs><path fill="url(#Af)" d="M241 406h0v3c1 0 1-1 1-1v-1h2l1-1v1l-1 2c-1 3 0 6 1 8 0 2 1 3 1 5h1v-1h1l1 2c0 1 0 2 1 3 0 1 0 2 1 3s1 2 1 3l2 3 1 3c0 2 0 3 1 5v1h-1c-1-1-1-2-2-3l-1 1-2-2 3 8v1c0 1 0 1-1 1 0 1 1 2 1 3v1 1c-1-2-2-3-3-4 0-2 0-4-1-6-1 0-1-1-2-2l-1-4v-3c-1 1-1 1-2 1v2l-1 1c-1 0-1 2-2 2 0 1-1 1-1 1h-1v2-2l-2-1h-2-1l-2 1-1-1c1-1 1-2 2-4l-1-1 2-3c0-2 2-8 1-9l1-1h0 0c1 0 1-1 2-2h0l1-4c0-1-1-1-2-2l1-2c1-2 1-3 2-4v-1-1-1l1-1z"></path><path d="M253 448v1c0 1 0 1-1 1v-1l1-1z" class="F"></path><path d="M238 414h1 1 0c0 1-1 3-1 4h0c0-1-1-1-2-2l1-2z" class="I"></path><path d="M250 440c0-3-2-6-2-10h1l1 2c0 2 1 4 1 5 1 1 2 3 2 4l-1 1-2-2z" class="f"></path><path d="M241 406h0v3c1 0 1-1 1-1h1v2c0 2-2 3-3 4h0 0-1-1c1-2 1-3 2-4v-1-1-1l1-1z" class="Q"></path><path d="M250 432c1 1 2 2 3 4l1-1 1 3c0 2 0 3 1 5v1h-1c-1-1-1-2-2-3 0-1-1-3-2-4 0-1-1-3-1-5z" class="R"></path><path d="M239 418h0c0 3 1 6 2 8v-5l1-1v9c-1-1-1 0-1-1-1 2 0 3-1 4-1-1-1-3-2-4s0-3 0-4h0v-2h0l1-4z" class="Q"></path><path d="M238 424h0c0 1-1 3 0 4s1 3 2 4c1-1 0-2 1-4 0 1 0 0 1 1 0 2 0 4 1 5h1 0c0-1 1-1 1-2 1 0 1 0 1 1 1 2 1 4 0 6v-3c-1 1-1 1-2 1v-1l-1 1-1 1v-2c0-1 0-1-1-2v3h-1v-3s0-1-1-1l-1-1v-1h0c-1-1-1-2-1-3 1-1 1-3 1-4z" class="I"></path><path d="M245 417c0 2 1 3 1 5h1v-1h1l1 2c0 1 0 2 1 3 0 1 0 2 1 3s1 2 1 3l2 3-1 1c-1-2-2-3-3-4l-1-2h0c0-2-1-5-2-6v1c-1 2-1 3-2 4h0c-1-1-1-1-1-3h0c0-3-1-7 1-9z" class="D"></path><path d="M238 422v2c0 1 0 3-1 4 0 1 0 2 1 3h0v1l1 1c1 0 1 1 1 1v3h1v-3c1 1 1 1 1 2v2l1-1 1-1v1 2l-1 1c-1 0-1 2-2 2 0 1-1 1-1 1h-1v2-2l-2-1h-2-1l-2 1-1-1c1-1 1-2 2-4l-1-1 2-3c0-2 2-8 1-9l1-1h0 0c1 0 1-1 2-2z" class="Z"></path><path d="M238 432l1 1c1 0 1 1 1 1h-1v5h-1v-7zm0-10v2c0 1 0 3-1 4 0 1 0 2 1 3h0l-1 1v2c-1-1-1-1-1-2 1-3 0-5 0-8h0c1 0 1-1 2-2z" class="F"></path><path d="M243 437l1-1v1 2l-1 1c-1 0-1 2-2 2 0 1-1 1-1 1v-4c1 0 1-1 1-1h1v1h0c1-1 1-1 1-2z" class="o"></path><path d="M233 437c1-1 2-3 2-4h1v1c-1 2 0 3 0 5 0 1 1 1 1 2v1h-2-1l-2 1-1-1c1-1 1-2 2-4v-1z" class="g"></path><path d="M233 437h1c0 1-1 3-1 4h1v1l-2 1-1-1c1-1 1-2 2-4v-1z" class="Y"></path><path d="M228 454h1c0 3 0 6 1 8l1 2c1 3 2 5 3 6l4 4c1 1 2 1 2 2l1-1c4 3 8 6 12 8l17 14c1 1 4 4 6 4h0c1 1 2 2 4 3l-1 3 4 6 4 6c0 1 1 3 2 5 2 4 4 8 5 12v3l3 9c1 4 1 8 1 11l-2 5-1-4v-2c-1-3-2-6-2-9-1-2-1-3-2-4h0c-1 0-1-1-1-1l-1-3c0-1-1-2-1-3v2 2c-2-1-3-2-5-4l-1-1c0-1 0-3-1-4l-1-2c-1 0-1 0-2 1l-1-1h-1-1c-1-1-1-2-3-2 0 1 0 1-1 1v-1h-1c0-1-1-2-2-3l-1-1c-1-1-2-3-2-4l-2-2c0-1-1-2-2-3l-1-2h0l2 1v-1h1 2l-2-2h0c-1 2-2 0-4 0-1-1-3-1-4-2s-2 0-3-1-2-1-4-1h0c-3-2-7-3-10-5l-2-1c2-1 5 0 7 0l1-1-1-1c-4-2-7-3-11-5l-1-1h2l-3-3c1-1 0-2 0-3h0v-1l1-1h0c-2-2-3-4-4-6-1-3-1-6 0-10 0 0 0-1-1-2v-3c1-3 1-7 0-11 0 1 1 1 1 2 1 1 0 3 1 4v-6h0z" class="t"></path><path d="M289 524c2 4 4 8 5 12h-1v-1l-1-1c-1-1-1-2-2-4 0-2-1-4-1-6z" class="C"></path><path d="M228 454h1c0 3 0 6 1 8l1 2c-1 0-1 0-2-2v-1h0v-2h0l-1 9v-8-6h0z" class="L"></path><path d="M226 454c0 1 1 1 1 2 1 1 0 3 1 4v8c1 5 4 9 8 13h-1c-3-2-5-5-6-8h0c-1-1-1-2-2-3 0 0 0-1-1-2v-3c1-3 1-7 0-11z" class="W"></path><path d="M281 520c2 3 4 8 5 12 2 4 4 8 5 13h0 0c-1 0-1-1-1-1l-1-3c0-1-1-2-1-3l-9-18h2z" class="J"></path><path d="M241 475c4 3 8 6 12 8l17 14c1 1 4 4 6 4h0c1 1 2 2 4 3l-1 3c-5-7-12-11-18-16l-9-7-12-8 1-1z" class="N"></path><path d="M227 470c1 1 1 2 2 3h0c1 3 3 6 6 8h1l3 3v4c0-1-1-1-2-2 0 1 0 2-1 3-1 0-3-2-4-3h-1c-2-2-3-4-4-6-1-3-1-6 0-10z" class="T"></path><path d="M229 473c1 3 3 6 6 8 0 1 1 1 1 2h0l-5-4c-1-1-3-1-3-3 0-1 0-2 1-3z" class="E"></path><path d="M227 480v-2c2 0 8 6 9 7l1 1h0c0 1 0 2-1 3-1 0-3-2-4-3h-1c-2-2-3-4-4-6z" class="H"></path><path d="M239 484l15 10-1 1c-1-1-2-1-3-1l2 2c-2 1-3-1-5-1l-1 1h0v1s0 1 1 1v1l1 1h-5 0c-4-2-7-3-11-5l-1-1h2l-3-3c1-1 0-2 0-3h0v-1l1-1h0 1c1 1 3 3 4 3 1-1 1-2 1-3 1 1 2 1 2 2v-4z" class="U"></path><path d="M239 492c1 0 2 1 3 1h1v3l-3-2h1l-2-2z" class="d"></path><path d="M243 493l3 3v1s0 1 1 1v1l1 1h-5 0c-4-2-7-3-11-5l-1-1h2 1c1 0 1 1 2 1 2 1 5 3 8 3h0c0-1 0-1-1-2v-3z" class="M"></path><path d="M231 486h1c1 1 3 3 4 3 1-1 1-2 1-3 1 1 2 1 2 2l8 6c-2-1-3-1-4-2 0 1 0 1-1 1s-2-1-3-1l-5-3c-1 0-2 0-3-1v-1l-1 1v-1l1-1h0z" class="D"></path><path d="M239 484l15 10-1 1c-1-1-2-1-3-1l2 2c-2 1-3-1-5-1l-1 1h0l-3-3h-1c1 0 1 0 1-1 1 1 2 1 4 2l-8-6v-4z" class="F"></path><path d="M252 496l-2-2c1 0 2 0 3 1l1-1c3 1 4 4 6 6 1 1 2 1 3 2 7 4 13 11 18 18h-2l9 18v2 2c-2-1-3-2-5-4l-1-1c0-1 0-3-1-4l-1-2c-1 0-1 0-2 1l-1-1h-1-1c-1-1-1-2-3-2 0 1 0 1-1 1v-1h-1c0-1-1-2-2-3l-1-1c-1-1-2-3-2-4l-2-2c0-1-1-2-2-3l-1-2h0l2 1v-1h1 2l-2-2h0c-1 2-2 0-4 0-1-1-3-1-4-2s-2 0-3-1-2-1-4-1h0c-3-2-7-3-10-5l-2-1c2-1 5 0 7 0l1-1-1-1h0 5l-1-1v-1c-1 0-1-1-1-1v-1h0l1-1c2 0 3 2 5 1z" class="C"></path><path d="M281 533c2 1 3 2 4 4-1 0-1 0-2 1l-1-1c0-1 0-3-1-4z" class="D"></path><path d="M276 529c1-1 1-1 1-2s0-1 1-2l2 6c-1 0-1 0-2 1l-1-1-1-2z" class="E"></path><path d="M283 538c1-1 1-1 2-1l3 3v2c-2-1-3-2-5-4z" class="P"></path><path d="M243 500h0l3 1c1 0 2 1 3 1 0 1 0 1-1 1l-5-1 1-1-1-1z" class="f"></path><path d="M246 501c3 0 5 1 7 1l2 1v1h-2l-5-1c1 0 1 0 1-1-1 0-2-1-3-1z" class="K"></path><path d="M255 510h2 0 0l-1-1c-1-1-2-1-3-1v-1h1c1 0 2 0 2 1 2 1 5 2 7 4-1 2-2 0-4 0-1-1-3-1-4-2z" class="L"></path><path d="M253 502c2 0 4 1 6 2h0 0l1 1c1 1 2 1 4 2v1l3 2h0c-5-2-9-4-14-6h2v-1l-2-1z" class="F"></path><path d="M246 496h0l12 6 2 1-1 1h0 0c-2-1-4-2-6-2s-4-1-7-1l-3-1h5l-1-1v-1c-1 0-1-1-1-1v-1z" class="E"></path><path d="M263 512c2 0 2-1 4 0 2 2 3 3 4 5 2 3 6 4 7 8-1 1-1 1-1 2s0 1-1 2l-3-6c-2-4-4-7-8-9l-2-2z" class="D"></path><path d="M252 496l-2-2c1 0 2 0 3 1l1-1c3 1 4 4 6 6 1 1 2 1 3 2l-1 1h-2l-2-1-12-6 1-1c2 0 3 2 5 1z" class="c"></path><path d="M256 498c2 1 3 2 4 2 1 1 2 1 3 2l-1 1h-2l-2-1c1 0 0 0 1-1l-3-3z" class="j"></path><path d="M252 496l-2-2c1 0 2 0 3 1l1-1c3 1 4 4 6 6-1 0-2-1-4-2l-4-2z" class="D"></path><path d="M263 502c7 4 13 11 18 18h-2l-3-6c-2-2-4-3-5-5l-1 1v1 1c-1-1-2-1-3-2h0l-3-2v-1c-2-1-3-1-4-2l-1-1 1-1h2l1-1z" class="I"></path><path d="M260 505c1-1 2-1 3-1 1 1 1 1 3 2l-2 2v-1c-2-1-3-1-4-2z" class="P"></path><path d="M266 506c2 1 3 2 5 3l-1 1v1 1c-1-1-2-1-3-2h0l-3-2 2-2z" class="E"></path><path d="M263 514h2c4 2 6 5 8 9l3 6 1 2h-1-1c-1-1-1-2-3-2 0 1 0 1-1 1v-1h-1c0-1-1-2-2-3l-1-1c-1-1-2-3-2-4l-2-2c0-1-1-2-2-3l-1-2h0l2 1v-1h1z" class="R"></path><path d="M271 529c1 0 1 0 2-1v-1h1c1 1 1 3 2 4h-1c-1-1-1-2-3-2 0 1 0 1-1 1v-1h0z" class="M"></path><g class="r"><path d="M270 523c2 1 2 2 2 4l-1 2h0-1c0-1-1-2-2-3 2-1 2-1 2-3z"></path><path d="M261 516l-1-2h0l2 1v-1h1c1 4 6 5 7 9h0c0 2 0 2-2 3l-1-1c-1-1-2-3-2-4l-2-2c0-1-1-2-2-3z"></path></g><path d="M263 519c1 0 1-1 3-1v2l-1 1-2-2z" class="p"></path><path d="M267 525l1-3h1l1 1h0c0 2 0 2-2 3l-1-1z" class="l"></path><path d="M232 510c1-1 1-1 0-3h1c1 0 3 1 3 2l3 1 6 5 4 4c2 1 4 2 5 3l7 8c2 2 3 3 6 5 0 0 0 1 1 1l2 2h1c-1-2-3-4-4-6-1-1-1-2-2-2-1-2-1-4-2-6-1 0-2-1-2-2 0 0 0-1-1-1v-3l1-2c1 1 2 2 2 3l2 2c0 1 1 3 2 4l1 1c1 1 2 2 2 3h1v1c1 0 1 0 1-1 2 0 2 1 3 2h1 1l1 1c1-1 1-1 2-1l1 2c1 1 1 3 1 4l1 1c2 2 3 3 5 4v-2-2c0 1 1 2 1 3l1 3s0 1 1 1h0c1 1 1 2 2 4 0 3 1 6 2 9v2l1 4-3 8c-1 1-2 3-3 4s-2 1-2 3l-2 2-2 2h0l-1 1c-1 0-2-1-2-2l-1 1 1 2h0c-1 1-2 3-3 4h0c-1-4-3-6-5-8-2-3-4-5-7-7l-8-4c0-1-1-1-2-2v-1l1-1v-1h1 1l-2-2c-2-3-6-6-9-9v-2c-1 0-1 0-1-1h1v-1c-2-1-5-4-6-5s-2-2-4-2h0l-8-6v-1c-2-1-5-1-8-2h0 1v-1h3c-1 0-1-1-2-1l-3-1c-1 0-2 0-2-1l1-1 1 1c2 0 4 1 5 1 2 0 3 1 5 2l4 1h1l3 1 6 4c1-1 2-1 3-2l2 1c0-1-1-2-2-4h0l-2-1h2 1 0v-1h-1c-2-1-2-3-4-5-1 0-1-1-2-2l1-1c0-1-1-1-1-2l-2-2 1-1-4-4v-1h0l-2-1-2-2-1-1z" class="B"></path><path d="M274 558c1 1 1 0 2 0 1 2 2 3 3 5 1 1 1 2 2 3h1 1l-1 1-2-1c-2-2-4-5-6-7v-1z" class="F"></path><path d="M270 554h1c-1-2-3-4-4-6-1 0-1 0-1-1h1c2 2 4 5 6 8l3 3c-1 0-1 1-2 0l-4-4z" class="T"></path><path d="M282 558h0c-2-4-5-8-6-13 0-3-3-5-4-7v-1l8 13 2 3 1 1h1v-2h1c0 2 1 3 1 5h-1l-1 1-1-1-1 1z" class="e"></path><path d="M280 550l2 3 1 1h1v-2h1c0 2 1 3 1 5h-1l-1 1c-1-3-3-5-4-8z" class="d"></path><path d="M284 558l1-1h1l1 2c1 1 1 2 1 2 1 2 3 5 3 6v4l1 1h1c-1 1-2 3-3 4s-2 1-2 3c-1-2-2-5-3-7l-2-2c-1-1-2-2-3-4l2 1 1-1h0c1-1 1-1 2-3-1-1-2-3-3-5l1-1 1 1z" class="K"></path><path d="M284 558l1-1h1l1 2c-1 1 0 2-1 3l-2-4z" class="E"></path><path d="M285 567c1 0 2 0 2 1s1 2 2 3l-2-1c-1 1-2 1-2 2l-2-2 2-1v-2z" class="q"></path><path d="M285 563v4 2l-2 1c-1-1-2-2-3-4l2 1 1-1h0c1-1 1-1 2-3z" class="Z"></path><path d="M285 572c0-1 1-1 2-2l2 1c0 2 0 4 1 5-1 1-2 1-2 3-1-2-2-5-3-7z" class="r"></path><path d="M286 562c1-1 0-2 1-3 1 1 1 2 1 2 1 2 3 5 3 6l-1 1v4l-3-6v-1l-1-3z" class="R"></path><path d="M286 562c1-1 0-2 1-3 1 1 1 2 1 2-1 2 1 4-1 4l-1-3z" class="J"></path><path d="M254 522l7 8c2 2 3 3 6 5 0 0 0 1 1 1l2 2c1 1 2 3 3 4l-1 1c1 2 3 5 4 7 1 1 1 1 1 2 1 2 2 4 3 5 1 2 1 2 1 4h-1l-3-6-3-3c-2-3-4-6-7-9-1-1-2-3-3-3h0l-2-2-2-3c-3-3-5-6-7-9l1-1v-3z" class="U"></path><path d="M260 532c1 0 2 2 2 3v3l-2-3v-3z" class="D"></path><path d="M254 522l7 8c2 2 3 3 6 5 0 0 0 1 1 1l2 2c1 1 2 3 3 4l-1 1c-3-5-9-9-13-13h0 0l1 2v3c-3-3-5-6-7-9l1-1v-3z" class="E"></path><path d="M272 537c-1-3-4-6-6-8v-3h1c0 1 1 2 2 2v1h1 1v1c1 0 1 0 1-1 2 0 2 1 3 2h1 1l1 1c1-1 1-1 2-1l1 2c1 1 1 3 1 4v2c0 1 0 1 1 1-1 2-1 3 0 5 0 1-1 1-1 1 0 2 0 5 2 6v2h-1l-1-1-2-3-8-13z" class="t"></path><path d="M277 531l1 1c1-1 1-1 2-1l1 2c1 1 1 3 1 4v2c0 1 0 1 1 1-1 2-1 3 0 5 0 1-1 1-1 1l-3-9-3-6h1z" class="P"></path><path d="M283 540c-1 0-1 0-1-1v-2l1 1c2 2 3 3 5 4v-2-2c0 1 1 2 1 3l1 3s0 1 1 1h0c1 1 1 2 2 4 0 3 1 6 2 9v2l1 4-3 8h-1l-1-1v-4c0-1-2-4-3-6 0 0 0-1-1-2l-1-2c0-2-1-3-1-5h-1c-2-1-2-4-2-6 0 0 1 0 1-1-1-2-1-3 0-5z" class="H"></path><path d="M283 545v-1h2 0c0 1-1 2-2 3 1 2 2 4 2 5h-1c-2-1-2-4-2-6 0 0 1 0 1-1z" class="D"></path><path d="M283 540c-1 0-1 0-1-1v-2l1 1c2 2 3 3 5 4 2 3 2 7 4 10v2l-1 1c-1-4-2-7-3-10h0c-1-1-1-1-3-1h-2v1c-1-2-1-3 0-5z" class="T"></path><path d="M283 540l1 1v2h0c1 0 1 0 2-1 0 0 1 1 2 1v2h0c-1-1-1-1-3-1h-2v1c-1-2-1-3 0-5z" class="E"></path><path d="M288 538c0 1 1 2 1 3l1 3s0 1 1 1h0c1 1 1 2 2 4 0 3 1 6 2 9v2l1 4-3 8h-1l-1-1 2-2c1-5-1-9-2-14l1-1v-2c-2-3-2-7-4-10v-2-2z" class="O"></path><path d="M232 510c1-1 1-1 0-3h1c1 0 3 1 3 2l3 1 6 5 4 4c2 1 4 2 5 3v3l-1 1c2 3 4 6 7 9l2 3 2 2c-2-1-5-5-7-7 0 1 0 2-1 3l3 4c-2 0-2-2-4-3v1l-1 1 2 4h0l-5-4-1-1c0-1-1-2-2-4h0l-2-1h2 1 0v-1h-1c-2-1-2-3-4-5-1 0-1-1-2-2l1-1c0-1-1-1-1-2l-2-2 1-1-4-4v-1h0l-2-1-2-2-1-1z" class="g"></path><path d="M250 529l1 3c-1 0-1 1-2 0h-1l1-1-1-1 2-1z" class="J"></path><path d="M251 532c1 1 2 3 2 5l-1-1c-1-1-2-2-3-2h-1 0l-2-1h2 1 0v-1c1 1 1 0 2 0z" class="e"></path><path d="M252 529h1c2 1 3 2 4 4 0 1 0 2-1 3l-4-7z" class="D"></path><path d="M248 534h1c1 0 2 1 3 2l1 1 1 2h0l2 4h0l-5-4-1-1c0-1-1-2-2-4z" class="O"></path><path d="M241 519l1 1 3 3 3 4c1 1 1 2 2 2l-2 1 1 1-1 1c-2-1-2-3-4-5-1 0-1-1-2-2l1-1c0-1-1-1-1-2l-2-2 1-1z" class="E"></path><path d="M245 523l3 4c1 1 1 2 2 2l-2 1v-3c-1 0-3-1-4-2s-1 0-1-1c1 0 1 0 2-1h0z" class="R"></path><path d="M232 510c1-1 1-1 0-3h1c1 0 3 1 3 2l3 1v3c0 1 3 2 3 3 1 1 1 1 2 1v1h-1l-1 2-1-1-4-4v-1h0l-2-1-2-2-1-1z" class="f"></path><path d="M237 514c2 0 4 3 6 4l-1 2-1-1-4-4v-1z" class="o"></path><path d="M232 510c1-1 1-1 0-3h1c1 0 3 1 3 2l-1 1c1 0 1 1 1 1 0 1 0 1-1 2l-2-2-1-1z" class="q"></path><path d="M245 515l4 4c2 1 4 2 5 3v3l-1 1c2 3 4 6 7 9l2 3 2 2c-2-1-5-5-7-7-1-2-2-3-4-4h-1c-1-1-2-3-2-4h0c-1-1-1-1-1-2-1-1-2-2-3-4s-2-2-1-4z" class="F"></path><path d="M249 519c2 1 4 2 5 3v3l-1 1c-1-2-3-4-4-7z" class="M"></path><path d="M220 529l1 1c2 0 4 1 5 1 2 0 3 1 5 2l4 1h1l3 1 6 4c1-1 2-1 3-2l2 1 1 1 5 4h0l-2-4 1-1v-1c2 1 2 3 4 3 2 2 2 4 3 5 0 1 1 2 1 3v1c2 2 4 3 7 5l4 4v1c2 2 4 5 6 7 1 2 2 3 3 4l2 2c1 2 2 5 3 7l-2 2-2 2h0l-1 1c-1 0-2-1-2-2l-1 1 1 2h0c-1 1-2 3-3 4h0c-1-4-3-6-5-8-2-3-4-5-7-7l-8-4c0-1-1-1-2-2v-1l1-1v-1h1 1l-2-2c-2-3-6-6-9-9v-2c-1 0-1 0-1-1h1v-1c-2-1-5-4-6-5s-2-2-4-2h0l-8-6v-1c-2-1-5-1-8-2h0 1v-1h3c-1 0-1-1-2-1l-3-1c-1 0-2 0-2-1l1-1z" class="d"></path><path d="M280 572l6 9-2 2c0-1 0-2-1-3 0-2-1-4-3-6v-1-1z" class="f"></path><path d="M248 537l2 1 1 1v3l-6-3c1-1 2-1 3-2z" class="S"></path><path d="M251 539l5 4h0c1 1 1 0 1 1l1 1-2 2-5-5v-3z" class="V"></path><path d="M247 544c2 2 5 3 5 6-1 0-1 0-2-1h-2v1c-2-1-5-4-6-5 2-1 2-1 4 1h0l1-1v-1z" class="o"></path><path d="M262 548l1 1c2 2 4 3 7 5l4 4v1c-5-3-10-6-15-10 1 0 2 0 3-1z" class="c"></path><path d="M230 536c6 2 11 5 17 8v1l-1 1h0c-2-2-2-2-4-1-1-1-2-2-4-2h0l-8-6v-1z" class="Y"></path><path d="M255 538v-1c2 1 2 3 4 3 2 2 2 4 3 5 0 1 1 2 1 3v1l-1-1c-1 1-2 1-3 1l-1-1-2-1 2-2-1-1c0-1 0 0-1-1l-2-4 1-1z" class="O"></path><path d="M255 538c2 2 3 5 5 7h-2l-1-1c0-1 0 0-1-1l-2-4 1-1z" class="p"></path><path d="M260 545l2 3c-1 1-2 1-3 1l-1-1-2-1 2-2h2z" class="S"></path><path d="M256 547l2-2 1 1c0 1-1 2-1 2l-2-1z" class="c"></path><path d="M248 550v-1h2c1 1 1 1 2 1 2 1 4 2 6 4 2 0 4 1 5 2l8 6c2 1 3 2 4 2 2 1 3 3 4 5 0 1 1 2 1 3v1 1c2 2 3 4 3 6 1 1 1 2 1 3h0-1c-2-2-3-4-4-6-1-1-1-2-1-3l-3-3c1-1 0-2 0-3v-1c-1-2-1-1-3-2l-1-1h-3v-1l-11-8-4-3-1-1-1 1-3-1v-1z" class="O"></path><path d="M248 550v-1h2c1 1 1 1 2 1 2 1 4 2 6 4-2 0-4-3-6-3l-1 1-3-1v-1z" class="f"></path><path d="M275 568l6 10c1 0 1 1 2 2h0c1 1 1 2 1 3h0-1c-2-2-3-4-4-6-1-1-1-2-1-3l-3-3c1-1 0-2 0-3z" class="Z"></path><path d="M248 551l3 1 1-1 1 1 4 3 11 8v1h3l1 1c2 1 2 0 3 2v1c0 1 1 2 0 3l3 3c0 1 0 2 1 3 1 2 2 4 4 6h1l-1 1c-1 0-2-1-2-2l-1 1 1 2h0c-1 1-2 3-3 4h0c-1-4-3-6-5-8-2-3-4-5-7-7l-8-4c0-1-1-1-2-2v-1l1-1v-1h1 1l-2-2c-2-3-6-6-9-9v-2c-1 0-1 0-1-1h1z" class="F"></path><path d="M270 570c1 1 2 1 2 3l-1 1c0-1-1-3-1-4z" class="Y"></path><path d="M261 567c-1-1-1-2-2-3l1-1 1 1c1 1 0 2 1 3h-1z" class="Z"></path><path d="M257 566h1c0 1 1 2 1 3-1 0 0 0-1 1 0-1-1-1-2-2v-1l1-1z" class="i"></path><path d="M248 551l3 1 1-1 1 1-1 3-4-3c-1 0-1 0-1-1h1z" class="V"></path><path d="M252 555l1-3 4 3c1 2 1 3 3 4l-1 1h-1c-2-1-4-4-6-5z" class="W"></path><path d="M262 567c0 1 1 2 1 2 0 1-1 1 0 2 1 0 1 0 1-1l1 1c0 2 1 2 2 3 1 2 4 3 6 4l1 1h0c-1 1-1 1-1 2-2-3-4-5-7-7l-8-4c1-1 0-1 1-1h2v-2h1z" class="c"></path><path d="M257 555l11 8v1l2 1h-2l-1 2-2-2c-2-2-3-3-5-4l-1-1 1-1c-2-1-2-2-3-4z" class="r"></path><path d="M268 563v1l2 1h-2l-1 2-2-2c2 0 2-1 3-2z" class="q"></path><path d="M268 564h3l1 1c2 1 2 0 3 2v1c0 1 1 2 0 3l3 3c0 1 0 2 1 3 1 2 2 4 4 6h1l-1 1c-1 0-2-1-2-2l-1 1 1 2h0c-1 1-2 3-3 4h0c-1-4-3-6-5-8 0-1 0-1 1-2h0l-1-1-2-3c-1 0-1 0-2-1 0-1-1-1-1-2h0l3 2h0l1-1c0-2-1-2-2-3l-3-3 1-2h2l-2-1z" class="g"></path><path d="M267 567l1-2h2c1 2 1 4 2 6l5 5h0v2l-1-1h0c-1-1-2-3-4-4 0-2-1-2-2-3l-3-3z" class="N"></path><path d="M268 564h3l1 1c2 1 2 0 3 2v1c0 1 1 2 0 3l-1-1-2 1c-1-2-1-4-2-6l-2-1z" class="Q"></path><path d="M270 565l1 1h0c2 1 2 2 3 4l-2 1c-1-2-1-4-2-6z" class="K"></path><path d="M271 575h1l6 6 1 1h0l1-1c-1-1-2-2-2-3l1-1c1 2 2 4 4 6h1l-1 1c-1 0-2-1-2-2l-1 1 1 2h0c-1 1-2 3-3 4h0c-1-4-3-6-5-8 0-1 0-1 1-2h0l-1-1-2-3z" class="N"></path><path d="M279 582h0l1-1c-1-1-2-2-2-3l1-1c1 2 2 4 4 6h1l-1 1c-1 0-2-1-2-2l-1 1 1 2h-1-2l-1-1v-1-1h1 1z" class="Y"></path><path d="M226 545c2 0 4 1 6 2l3 2c1 0 1-1 2-1v-1l1 1 1 1h1l3 3c2 1 3 2 5 2 3 3 7 6 9 9l2 2h-1-1v1l-1 1v1c1 1 2 1 2 2l8 4c3 2 5 4 7 7 2 2 4 4 5 8h0v1c-1-1-2-1-2-1h-1v2c0 1-1 2-2 2l-2 1h-1 0c-1 0-1 0-2 1-2 2-4 2-6 3l-1-2c-1-1-1-2-1-3h-3c-1-1-2-1-2-2v-1l-2-2h0c-1 1-1 2-2 2v1 3h-1v1s-1 1-1 2h0 1c3 0 5 2 7 4-6 2-11 3-17 3l-8 1h-2c-1 0-3 1-5 1h-8c-2-2-4-6-4-9-1 1-1 1-1 2 0 2 1 4 1 5h-1c-1-1-1-2-2-2 0-2 0-3-1-5h-1l1-1v-5h0v-1l1-1c0-3 0-5-1-8-1 0-1-1-2-2h0v-1l1-1c1-1 2-1 2-2l2-3c1-2 3-2 4-4l1-1 2 1 2-2h2l1-2h1c-1 2-1 3-1 4l1 2 1-1c1 0 3-1 4-1h0v1c1-1 2-2 3-2 0-1 2-1 3-2-1-1-1-2-1-2-1-1-1-2-2-3v-1l-4-5v-2h0 2l1-1v-2c-3-2-5-2-7-3 1 0 1 0 1-1z" class="C"></path><path d="M222 588h1c1 1 1 1 1 2v2l-1 1-1 1v-1-3h0v-2z" class="M"></path><path d="M248 565s1 0 1-1c1 0 2 1 3 1 1 1 1 0 3 0 0 1 0 2 1 2v1c-2 0-5-2-8-3z" class="K"></path><path d="M218 579v-2h3v1 1 3 1c-1 0-2-1-2-1h-1v-2h0v-1z" class="B"></path><path d="M218 580h2v1 1h-1-1v-2z" class="G"></path><path d="M216 592c0 2 0 4 2 6v-1c0-2 1-3 1-5h1c0 1 0 2-1 3v4h0-1v1 1h-1c0-1-1-2-2-3 0-2 0-4 1-6z" class="E"></path><path d="M235 570h1v1h-1c-2 1-4 5-5 6v1c-1 5 0 9 0 14h0c0-1-1-1-1-2h0v-2c-1-4-1-9 2-13h0c1-2 2-4 4-5zm31 4c3 2 5 4 7 7 2 2 4 4 5 8h-1v-1c-3-4-6-6-10-9 1 0 2 0 3 1h0l3 3c0-1-1-2-2-3l-5-6z" class="L"></path><path d="M224 564h1c-1 2-1 3-1 4l1 2h1v2c0 1 0 1-1 2h-1 0c-1-1-2-2-3-2 0-1 1-1 1-1v-1-1c0-1 0-2 1-3l1-2z" class="I"></path><path d="M224 568l1 2h1c-1 1-1 1-2 1v1h-1c0-2 0-3 1-4z" class="e"></path><path d="M226 570v2c0 1 0 1-1 2 0-1-1-2-1-2v-1c1 0 1 0 2-1z" class="O"></path><path d="M230 605v-1s-1 0-1-1c-1-1-2-3-3-5h0v-2c1-1 1-1 3-2h1v3h0v2c1 1 2 2 2 3v3h-2z" class="H"></path><path d="M237 557l-1-2v-1h1c0 1 1 2 1 3l1 1 1-1-1-1v-1l3 3c1 1 2 3 3 4h1s0 1 1 1c0 1-1 1 0 2h0-2-4c-1 1-1 0-2 0 1 0 1-1 2-1 0-2-1-3-2-4s-1-2-2-3z" class="P"></path><path d="M240 557c0 1 3 3 3 4 1 1 1 3 1 4-2-2-4-4-5-7l1-1z" class="G"></path><path d="M221 572c1 0 2 1 3 2h0l1 1s0 2 1 2v2c1-1 1-1 1-2h0c0 2 0 2-1 4-1 0-1 1 0 2v2h0-1l1-1-1-1-2 2-2-3c0-1 0-2 1-3h1 0c1-1 1-1 1-2-1-1-1 0-3 0-1-1-2-3-2-4v-1h2z" class="U"></path><path d="M226 577v2s0 1-1 1h0c-1-1 0-2 1-3z" class="M"></path><path d="M249 572h-5c-1 0-3 1-4 0 2 0 5-1 7-1 6 1 11 3 16 6 1 0 3 2 4 2 4 3 7 5 10 9v1h1 0v1c-1-1-2-1-2-1h-1-1c-1-1-2-2-3-4l-4-4c-5-5-12-7-18-9z" class="Y"></path><path d="M226 545c2 0 4 1 6 2l3 2c3 1 5 3 7 5l-1 1c1 0 2 1 2 2l-1 1-3-3v1l1 1-1 1-1-1c0-1-1-2-1-3h-1v1l1 2h-1l-1 1c-1-1-1-2-1-3l1-1h-2l-2-2 1-1v-2c-3-2-5-2-7-3 1 0 1 0 1-1z" class="B"></path><path d="M232 549l2 1v1c0 1 1 1 1 1l1 1-1 1h-2l-2-2 1-1v-2z" class="U"></path><path d="M226 545c2 0 4 1 6 2l3 2c3 1 5 3 7 5l-1 1c-1-2-5-4-7-5l-2-1c-3-2-5-2-7-3 1 0 1 0 1-1z" class="K"></path><path d="M249 572c6 2 13 4 18 9l4 4c1 2 2 3 3 4h1v2c0 1-1 2-2 2l-2 1c-1-1-1-2-2-3-2-6-7-10-12-14-2-1-3-2-5-2l-3-2v-1z" class="B"></path><path d="M271 585c1 2 2 3 3 4h1v2c0 1-1 2-2 2 0 0-1-1-1-2l-1-6z" class="F"></path><path d="M218 580h0v2h1s1 1 2 1c-1 3-4 5-5 9-1 2-1 4-1 6 1 1 2 2 2 3h1v-1-1h1 0v3h1c0 1 1 1 1 2l-1 1-2-2s0 1-1 2c2 1 5 0 6 0 0-2-2-4-2-7h1c0 1 0 1 1 2v1c0 2 1 3 2 5h-8c-2-2-4-6-4-9-1 1-1 1-1 2-1-2-2-5-1-7h0l1-1c0-2 0-3 1-4 0-1 1-2 1-2 1-2 2-3 3-4l1-1z" class="J"></path><path d="M213 587l1 1c0 1 0 2-1 3l-1 1v3c1 1 1 2 1 2-1 1-1 1-1 2-1-2-2-5-1-7h0l1-1c0-2 0-3 1-4z" class="F"></path><path d="M237 547l1 1 1 1h1l3 3c2 1 3 2 5 2 3 3 7 6 9 9l2 2h-1-1v1l-1 1c-1 0-1-1-1-2-2 0-2 1-3 0-1 0-2-1-3-1 0 1-1 1-1 1h-1 0c-1-1 0-1 0-2-1 0-1-1-1-1h-1c-1-1-2-3-3-4l1-1c0-1-1-2-2-2l1-1c-2-2-4-4-7-5 1 0 1-1 2-1v-1z" class="B"></path><path d="M252 562l1-1c1 0 1 1 2 2h2l2 2h-1-1v1l-1 1c-1 0-1-1-1-2v-1c-1 0-2-1-3-2z" class="G"></path><path d="M242 554c1 1 1 1 1 2l2 2c1 2 3 4 5 6h1c-1-1-1-2-1-3h0c1 0 1 0 2 1s2 2 3 2v1c-2 0-2 1-3 0-1 0-2-1-3-1 0 1-1 1-1 1h-1 0c-1-1 0-1 0-2-1 0-1-1-1-1h-1c-1-1-2-3-3-4l1-1c0-1-1-2-2-2l1-1z" class="L"></path><path d="M242 558l1-1c1 1 3 3 3 5h-1c-1-1-2-3-3-4zm-34 19c1-1 2-1 2-2l2-3c1-2 3-2 4-4l1-1 2 1h0 1l1 1-1 1c0-1-1-1-2 0l1 1h0-2v2 1h-1l-1-1c-1 0-2 1-3 2 2 1 2-1 3 1l1 1c1-1 1-1 2-1v3 1l-1 1c-1 1-2 2-3 4 0 0-1 1-1 2-1 1-1 2-1 4l-1 1h0c-1 2 0 5 1 7 0 2 1 4 1 5h-1c-1-1-1-2-2-2 0-2 0-3-1-5h-1l1-1v-5h0v-1l1-1c0-3 0-5-1-8-1 0-1-1-2-2h0v-1l1-1z" class="G"></path><path d="M211 592c0-4 0-9 2-12 1 0 3 0 4 1-1 1-2 2-3 4 0 0-1 1-1 2-1 1-1 2-1 4l-1 1h0zm-3-15c1-1 2-1 2-2l2-3c1-2 3-2 4-4l1-1 2 1h0 1l1 1-1 1c0-1-1-1-2 0l1 1h0-2c-1-1-1-1-2 0-2 1-3 4-5 5 1 0 2 2 2 2-2 0-3-1-4-1z" class="D"></path><path d="M237 595c-3-4-4-9-2-14 0-3 2-5 5-6h0c3-2 6-2 9-2l3 2c2 0 3 1 5 2 5 4 10 8 12 14 1 1 1 2 2 3h-1 0c-1 0-1 0-2 1-2 2-4 2-6 3l-1-2c-1-1-1-2-1-3h-3c-1-1-2-1-2-2v-1l-2-2h3 0l-1-2-1-1h-1c0-1-1-2-2-2-2-1-2-1-3-2-2-2-1-2-1-4-1-1-1-2-3-2-1 0-3 0-4 1l-1 1c-2 1-3 3-4 5 0 4 0 8 2 12 2 1 5 2 5 4-1-1-3-2-5-3z" class="F"></path><path d="M261 586h3v4 1c-1-1-2-4-3-5z" class="O"></path><path d="M263 585l2 2c1 1 1 2 2 3h-3v-4l-1-1z" class="K"></path><path d="M258 580c2 1 4 3 5 5l1 1h-3v-1c0-1 0-1-1-2 0 0 0 1-1 1v-1c0-1-1-1-2-1v-1-1h1z" class="e"></path><path d="M256 588c0 1 3 4 4 5h-3c-1-1-2-1-2-2v-1l-2-2h3 0z" class="B"></path><path d="M256 583c0-1 1-1 1-1 1 0 2 0 2 1v1l3 6s1 1 1 2 0 3-1 4c-1-1-2-4-3-5v-1l-3-6c-1 0-1-1-1-2l1 1z" class="O"></path><path d="M256 583c0-1 1-1 1-1 1 0 2 0 2 1v1l3 6h-1c-1 0-2-5-3-6 0 0-1-1-2-1z" class="d"></path><path d="M244 575c6-1 8 0 12 3l2 2h-1v1 1s-1 0-1 1l-1-1c-1-1-2-1-3-3l-1-1h-1c1 2 3 4 4 5v1s1 1 1 2l-1-1h-1c0-1-1-2-2-2-2-1-2-1-3-2-2-2-1-2-1-4-1-1-1-2-3-2z" class="D"></path><path d="M237 595c-3-4-4-9-2-14 0-3 2-5 5-6h0c3-2 6-2 9-2l3 2c2 0 3 1 5 2 5 4 10 8 12 14 1 1 1 2 2 3h-1 0c-1 0-1 0-2 1 1-2 0-3-1-5-1-1-1-2-2-3l-2-2c-1-2-3-4-5-5l-2-2c-4-3-6-4-12-3-1 0-3 0-4 1l-1 1c-2 1-3 3-4 5 0 4 0 8 2 12 2 1 5 2 5 4-1-1-3-2-5-3z" class="N"></path><path d="M235 582c1-2 2-4 4-5l1-1c1-1 3-1 4-1 2 0 2 1 3 2 0 2-1 2 1 4 1 1 1 1 3 2 1 0 2 1 2 2h1l1 1 1 2h0-3 0c-1 1-1 2-2 2v1 3h-1v1s-1 1-1 2h0 1c3 0 5 2 7 4-6 2-11 3-17 3v-1-1c-1-1-1-2-2-2h-1c0-1-1-2-1-3v-1l1-1c2 1 4 2 5 3 0-2-3-3-5-4-2-4-2-8-2-12z" class="H"></path><path d="M242 587c1 0 2 0 3-1 0 0 1 0 1 1 2 0 3-1 5 0 1 1 1 1 2 1-1 1-1 2-2 2v1c-2-2-6-2-8-2-3 1-4 3-6 5l3-6v-1l1 1 1-1z" class="g"></path><path d="M235 582c1-2 2-4 4-5l1-1c1-1 3-1 4-1 2 0 2 1 3 2 0 2-1 2 1 4 1 1 1 1 3 2 1 0 2 1 2 2h1l1 1 1 2h0-3 0c-1 0-1 0-2-1-2-1-3 0-5 0 0-1-1-1-1-1-1 1-2 1-3 1 0-1 0-1 1-1 0-1 1-1 2-1v-1c0-2-1-2-2-3h-1v1 1h-1l-1-1c1-1 1-2 2-3h2l1-1c-1-1-1-1-1-2h0c1 1 1 1 2 1h0c0-1 0-1-1-1h-2-1l1 1h-2c-1 1-3 3-3 4l-1 1h-2z" class="d"></path></svg>