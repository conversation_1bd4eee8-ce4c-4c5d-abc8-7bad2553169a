<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="50 37 513 632"><!--oldViewBox="0 0 600 752"--><style>.B{fill:#dfdfdf}.C{fill:#cbcbcb}.D{fill:#bebdbd}.E{fill:#e7e6e7}.F{fill:#d2d1d1}.G{fill:#908f8f}.H{fill:#c5c4c4}.I{fill:#d9d8d8}.J{fill:#898889}.K{fill:#979696}.L{fill:#b4b3b3}.M{fill:#6e6d6d}.N{fill:#5d5d5d}.O{fill:#797878}.P{fill:#aba9aa}.Q{fill:#afaeae}.R{fill:#f7f6f6}.S{fill:#a1a0a0}.T{fill:#3b3a3b}.U{fill:#ececec}.V{fill:#a5a4a5}.W{fill:#403f40}.X{fill:#565555}.Y{fill:#343334}.Z{fill:#828181}.a{fill:#4a494a}.b{fill:#4f4e4e}.c{fill:#9c9b9c}.d{fill:#454445}.e{fill:#252425}.f{fill:#373737}.g{fill:#292829}.h{fill:#666565}.i{fill:#302f30}.j{fill:#2d2c2d}.k{fill:#202020}.l{fill:#747373}.m{fill:#f1f1f1}.n{fill:#b9b8b8}.o{fill:#7d7c7c}.p{fill:#1a1a1a}.q{fill:#f9f9f9}.r{fill:#141414}</style><path d="M446 404h1l-3 4h-1l1-1h-1v-1c1 0 2-1 3-2z" class="p"></path><path d="M416 377s-1 1-2 1h0-1v-1h1c1-1 1-1 0-2h1 0v1h1v1z" class="k"></path><path d="M397 384c1 0 1-1 2-1v2l-3 1 1-2z" class="X"></path><path d="M215 530h1 1l1 1v1h-1-2 0l-1 1h-1c1-2 1-3 2-3z" class="e"></path><path d="M272 184c2-1 4-1 6 0-1 0-2 1-3 2l-2-1-1-1z" class="M"></path><path d="M365 233c1 1 1 2 2 3v3l-2 1v-1-3-3z" class="e"></path><path d="M278 184h1l2 1h0c-1 1-2 1-3 1v1l-3-1c1-1 2-2 3-2z" class="o"></path><path d="M231 216c1 0 2 0 2 1l-1 1c-2 1-3 2-5 2l4-4z" class="O"></path><path d="M214 258c1 0 1 1 1 2l1 1c-1 1-1 1-1 2l-1 1c-1 0-1-1-1-1l1-5z" class="T"></path><path d="M346 239v-1c0-1 1-1 2-1 0 0 1-1 2 0 0 0 0 2 1 3h-1c0-2-2-1-3-2l-1 1z" class="X"></path><path d="M215 255l1 1 1-2h1c-1 1-2 3-2 5h0v2l-1-1c0-1 0-2-1-2l1-3z" class="d"></path><path d="M375 316c-1-1-1-2-1-3 1 0 3 3 4 4v2c-1 0-1 0-1 1-1-1-2-3-2-4z" class="f"></path><path d="M356 235h0 1c0 1 0 1-1 2 1 0 2 0 3-1l1 1 1 1h-1-2v1c-1 0-1 1-2 1v-2h1c-1-1-1-1-1-2h1l-1-1z" class="e"></path><path d="M424 335v1c1 1 1 2 2 3 0 2-1 4-2 6l-1-3c1-1 1-2 1-3v-4z" class="f"></path><path d="M439 396h0v1h1c-3 2-5 2-8 3h0 0c1-1 2-1 3-2s2-1 4-2z" class="e"></path><path d="M202 324v-1h2v-1h2c1 1 1 1 2 1h0-1v1c1 1 2 1 3 1-3 0-5 0-8-1z" class="S"></path><path d="M362 242v-1h2c2 2 1 3 2 4l-2 3c-1 0-1-1-1-2s1-1 2-2c0-1 0-1-1-2h-2z" class="p"></path><path d="M435 400h0v1h-1 1 3c-3 0-6 2-8 2h-4-1c1-1 5-1 6-1 2-1 3-1 4-2z" class="e"></path><path d="M412 324v-4h1c1 0 2 2 3 3h0c-1 1-1 1-1 2-1-1-1-2-3-2v1z" class="k"></path><path d="M232 377h2 0c-1 1-2 2-3 2-1 1-3 1-4 1 0-1 1-1 2-1l1-1h-1-1-4 0c2 0 6 0 8-1z" class="g"></path><path d="M257 644c2 1 6 1 8 2h1c1 0 1 0 1 1-4 0-9-1-13-2h3 0v-1z" class="L"></path><path d="M204 358c0-1 0-2 1-3h1c1-1 2-2 4-3l-3 6s-1-1-2-1h0c0 1 0 1-1 1h0z" class="f"></path><path d="M418 386h0v1h1l-1 1c-2 0-4 2-6 3-1 0-2 1-3 0h2 0v-1l-2 1c1-1 7-4 9-5z" class="e"></path><path d="M225 406c0-1 0-1-1-2 0-2 1-3 2-5 1 1 1 1 1 2v2l-1 2-1 1z" class="k"></path><path d="M379 159c1 2 0 5-1 7l-1-1c-1 0-1-1-1-1l3-5z" class="Y"></path><path d="M224 228h0c1 0 1 0 2 1h1c-2 1-5 3-8 4 1-2 3-4 5-5z" class="O"></path><path d="M428 343c0 1 0 3-1 4 0 3-1 6-2 9h0c-1-1 0-2 0-3 0-3 1-7 3-10z" class="g"></path><path d="M231 411l1-1c2 0 2 1 2 2 1 0 1-1 2 0v3l1 1v1c-2-1-3-5-6-6z" class="O"></path><path d="M421 333v-6c2 3 3 5 3 8v4c-1-2-2-4-2-6h-1z" class="b"></path><path d="M436 357c-1-2-1-3-2-4 0-1-1-2-1-3 3 3 4 6 5 10h-2v-3z" class="X"></path><path d="M393 389c5-1 9-3 13-5h0c-3 2-5 3-7 4h1c-1 1-1 1-2 1-2 1-6 3-8 2 1-1 2-1 3-2z" class="j"></path><path d="M202 363c1 1 1 2 2 3v-1-2h1c1 0 1 0 2-1h0v7c-2 0-2-1-3-2h0c0-1-1-1-1-2-1 0-1-1-1-2z" class="e"></path><path d="M336 251c0-1 0-1-1-2v-2c1 0 1 0 1-1h1v-3c0 4 0 7 1 10v3h0c-1-2-1-3-2-5z" class="Q"></path><path d="M436 357v3h2c1 1 1 3 1 5l-1-2-1 1v1l-2-1c0-2 1-5 1-7z" class="W"></path><path d="M390 398c4 0 8 1 12 2-2 1-3 1-5 1l-3-1h0-2l1-1c-1 0-2 0-3-1z" class="h"></path><path d="M413 354h1 0v-2-1h1c-1 3-1 5-2 8 0 1 0 2-1 4 0 1-1 3-1 4l-1 1c-1-1 0-1 1-2v-1-2h0v-3c1-2 2-4 2-6z" class="j"></path><path d="M450 393v1c0 1 0 2-1 3h0v1c-3 3-8 6-12 7l-1-1h0 1c2-1 4-1 6-2v-1c4-1 5-4 7-8z" class="p"></path><path d="M231 416h1c0 3 1 5 3 7l4 4c-4-2-6-4-8-7l-1-1v-1h1v-2z" class="Q"></path><path d="M379 308h-1c-2 0-3 0-5-2v-1c0-1 0-2 1-2h0c0 2 1 2 2 3 2 0 3 0 5-1 0 2-1 2-2 3z" class="K"></path><path d="M216 383l-5-1c-2-1-6-3-7-5v-1c2 2 4 4 6 5h4c0-1 1-1 2-1h0c1 1 2 1 4 1l-1 1c-1 0-2 0-3 1z" class="f"></path><path d="M216 481c3-2 5-3 9-2 1 1 3 2 3 3v1h-1c-1-2-3-2-5-2s-4 0-6 1v-1z" class="L"></path><path d="M253 180c2 0 3 1 5 2l6 3c-1 0-1 0-1 1-2-1-3-1-4-1v1c-2-1-3-3-4-4l-2-2z" class="X"></path><path d="M435 377h1c3 0 4 1 6 2v8h-1c0-3 0-5-2-7-1-1-3-2-4-2h-1v-1h1z" class="N"></path><path d="M528 579c3 1 6 0 9-1v2c-4 2-8 1-12 0 1 0 2 0 3-1z" class="C"></path><path d="M419 112c2 0 4 0 5 2 1 1 1 2 2 3v2h-1c0-1-1-2-2-2 0 0-1 0-2-1 0 0 0-1-1-1v-1l1-1-2-1h0z" class="E"></path><path d="M238 443c1 0 2 1 2 1 0 2 0 3-1 5v1l-8 5c1-1 0-1 1-1 4-3 5-6 6-11h0z" class="Z"></path><path d="M212 371l-3-6c1 1 2 3 3 3 1 1 2 2 3 2 1 1 2 1 3 1h4v1l-1 1c-1-1-2-1-3-1h-1l1 1h-2c-1 0-2-2-3-3l-1 1z" class="b"></path><path d="M201 346h0l-2 3c-1 2-1 4 0 6 0 2 1 2 2 4h-1-2v-1c-2-1-1-5-1-7 1-2 2-4 4-5z" class="o"></path><path d="M227 426l1-1v-2c0-2 0-3 1-4h1l1 1c-1 1-1 2-2 4 1 2 2 5 3 7l2 1c-1 0-3-2-4-3l-3-3z" class="M"></path><path d="M421 333h1c0 2 1 4 2 6 0 1 0 2-1 3 0 1 0 1-1 1v-1c-1-1-1-2-2-3 1-2 0-3 0-4l1-2z" class="N"></path><path d="M269 187l1-1h2 0 1 0l-1-1h-2v-1h2l1 1 2 1 3 1 2 2v1l-4-1v-1c-1 0-2-1-4-1h0c0 1 1 1 1 2-1 0-1-1-2-1l-2-1z" class="Y"></path><path d="M362 242h2c1 1 1 1 1 2-1 1-2 1-2 2s0 2 1 2v1c-1 0-1 2-2 2h-2l1-1-1-2v1c0-2 1-2 1-4v-2l1-1z" class="i"></path><path d="M361 250l1-2c1 0 1 0 2 1-1 0-1 2-2 2h-2l1-1z" class="e"></path><path d="M350 198c2-1 5-1 7-2 2 0 4-1 5-1s1 1 1 1l-8 3c1 0 4 0 5 1-1 1-2 0-2 0h-3c-1-1-1-1-3-1h-3c0-1 0-1 1-1z" class="h"></path><path d="M359 217l1 1h1v1h0c0 1 0 1-1 2 1 0 1 1 2 1v3l1 2-1 1c1 1 1 1 1 2h-1v1h-1v-1s0-1 1-1l-2-1 1-1-1-1v-1c0-1 0-1 1-2v-1l-2 1v-1c0-1 0-1-1-1h0v-2h1l-1-1 1-1h0z" class="k"></path><path d="M431 382h0l-8 8c-3 2-8 3-12 4 2-1 5-2 6-4h1 0v-1 1c1 1 0 0 0 1h0c2 1 4-1 5-2h1c0-1 1-1 1-2h-1c0 1 0 1-1 1h-1c-1 1-1 1-2 1v-1c4 0 8-4 11-6z" class="e"></path><path d="M204 358h0c1 0 1 0 1-1h0c1 0 2 1 2 1v4c-1 1-1 1-2 1h-1v2 1c-1-1-1-2-2-3 0-2 1-3 2-5z" class="j"></path><path d="M206 338c1 1 1 1 2 1l1 1c1 0 1 0 2-1v-1 1c1 0 1 0 2 1v-1 1l-1 1v1h0 0c-1 0-1 1-2 1h-1 0c0 1 0 1-1 2l-1-1v-1h-2c0-1 0-1 1-1v-4z" class="g"></path><path d="M206 342v-1h4v1c-1 0-1 0-2 1-1 0-1 0-2-1h0z" class="T"></path><path d="M240 437c1 1 2 2 2 3l-1 1 2 1c0 1-1 2-1 3s-1 2-1 3l-1 1-1 2v-1-1c1-2 1-3 1-5 0 0-1-1-2-1 1-2 1-4 2-6z" class="a"></path><path d="M412 324v-1c2 0 2 1 3 2 0-1 0-1 1-2h0c1 1 2 3 1 5l1 3c-1 0-2-1-2-2-1 0-1 1-2 0h-1 0l-1-2v-3z" class="g"></path><path d="M412 327l2-2c2 1 1 2 3 3h0l1 3c-1 0-2-1-2-2-1 0-1 1-2 0h-1 0l-1-2z" class="T"></path><path d="M250 475l2-2v1c0 1 0 1 1 2l-1 1-1-1c-1 2-1 3-2 5 0 0-1 1-1 2-1 2-3 3-5 4 2-2 4-4 4-7v-1h0l1-4h2z" class="O"></path><path d="M248 475h2 0c-1 0-1 0-1 1v2c0 1-1 1-2 1h0l1-4z" class="h"></path><path d="M365 236v3 1 1h-3-1-1c-1 0-1-1-2-2v-1h2 1l-1-1-1-1h2 4z" class="i"></path><path d="M205 337c-1 1-2 3-2 4v1h-1-1l1-2-1-1v-3l1-1c0-1 0-1 1-1v-1c1-1 2-2 3-2h1l-3 4h0l1-1h1v1c0 1-1 1-1 2z" class="g"></path><path d="M204 335h0l1-1h1v1c0 1-1 1-1 2h-1s-1 1-1 2h-1c0-2 1-3 2-4z" class="k"></path><path d="M237 416l1 1c1 1 1 2 2 3v-1l2-1c1 3 2 6 5 9h-1c-4-2-7-5-9-10v-1z" class="K"></path><path d="M437 365v-1l1-1 1 2c-1 4-4 9-7 12l-1-1c1-1 5-4 5-6h-1l-2 1v-1-1l2-5 2 1z" class="e"></path><path d="M435 364l2 1c0 1-1 2-2 3l-2 2v-1l2-5z" class="Y"></path><path d="M208 488c1-1 1-2 1-3 2-3 4-6 7-7l1 1-1 1v1 1c-2 2-3 3-4 6-1 0-2 1-3 1l-1-1zm30 7l-1-1c-1-1-3-3-4-5 0-2 0-4 1-6h1l-1 4c0 1 1 2 2 2 2 2 4 3 7 3h0 1c-1 2-2 2-4 2-1 0-1 0-2 1z" class="K"></path><path d="M242 452l1-1c1 0 1 1 2 2-3 4-6 7-11 8-2 0-3 0-5-1h0 2c1 0 3 0 5-1 1-1 1-2 2-2h0c1-1 4-3 4-5z" class="c"></path><path d="M241 175c1-1 3-1 5-2 2 1 6 5 7 7l2 2v2l-7-7c-1 0-2-1-3-1h-3l-1-1z" class="E"></path><path d="M205 501h0c-1-1-3-4-3-5h1l2 2c0 1 4 5 5 5h1l6 5-1 1c-2-1-3-1-4-2-3-1-5-3-7-6z" class="j"></path><path d="M241 415l1-1c1 1 2 3 2 5 1 1 3 4 4 4s2 1 2 2v1l1 1h-3-1c-3-3-4-6-5-9l-1-3z" class="p"></path><path d="M385 398h3 2c1 1 2 1 3 1l-1 1h2 0c-1 1-2 1-3 1h0c0 1-1 1-2 1h-2-1c-2 1-3 1-5-1h-2l1-1 5-1v-1h0z" class="Z"></path><path d="M388 398h2c1 1 2 1 3 1l-1 1h2c-2 0-4 1-6 0v-1-1z" class="M"></path><path d="M234 206l2 1v1h1v2c1 2 0 3 1 5l-2 1-2 1h-1c0-1-1-1-2-1l1-3 1-3 1-4z" class="N"></path><path d="M232 213l2 1v1 2h-1c0-1-1-1-2-1l1-3z" class="M"></path><path d="M234 206l2 1v1h1v2c1 2 0 3 1 5l-2 1v-4h1l-1-1h-2l-1-1 1-4z" class="T"></path><path d="M205 501c2 3 4 5 7 6 0 1-1 2-1 2 0 1 1 2 1 2l-1 1v-1c-3 0-4-1-6-3v-2l-1-1c0-1-1-1-1-2l2-2z" class="h"></path><path d="M205 506h2l1 1c-1 0-1 1-3 1v-2z" class="J"></path><path d="M208 507h0c1 1 1 1 2 1v2c1 0 1 0 1 1-3 0-4-1-6-3 2 0 2-1 3-1zm4-136l1-1c1 1 2 3 3 3 4 2 8 2 12 3l4-2 1 1-1 2h0c-2 1-6 1-8 1-5-1-9-4-12-7z" class="O"></path><path d="M241 448c0 1 0 2 1 3h-1l1 1c0 2-3 4-4 5l-1-1c-4 1-6 1-10 1-2 0-4-1-5-3h0 1l1 1c2 1 4 1 6 1 4 0 6-2 9-5l1-2 1-1z" class="J"></path><path d="M241 448c0 1 0 2 1 3h-1l1 1c0 2-3 4-4 5l-1-1c1-1 1-2 2-3s1-2 1-4l1-1z" class="M"></path><path d="M226 405c0 1 0 2 1 3-1 1-1 0 0 1-2 3-3 6-3 8-2 5 1 12 3 16-1-1-1-1-1-2-4-4-5-9-3-15-1-1 0-3 0-5 0-1 1-2 2-4v-1l1-1z" class="O"></path><path d="M223 416c-1-1 0-3 0-5 0-1 1-2 2-4 0 1 0 1 1 1-1 3-3 5-3 8h0z" class="d"></path><path d="M206 314h-2c-1-1-3-3-3-4 2 2 3 3 6 4 3 0 6 0 9-2h3l-1 1c0 1 1 1 1 1l1 1h-1c-1 1-4 2-5 2s-4-1-5-1c-1-1-1-1-2-1l-1-1z" class="S"></path><path d="M218 313c0 1 1 1 1 1l1 1h-1-3l2-2z" class="J"></path><path d="M216 312h3l-1 1-2 2c-3 0-6 0-9-1 3 0 6 0 9-2z" class="O"></path><path d="M213 497h0 1v-1c0-2 0-3 2-5h2c1-1 2 0 3 1h1l-1 1v-1h-3c-1 1-2 1-3 2 0 2 0 4 1 6 2 1 4 3 5 4h0v1l3 2h1c0 1 0 1-1 1s-4-2-5-3c-2-2-3-3-5-4 0-1-1-3-1-4z" class="T"></path><path d="M206 338c3-3 6-5 9-7l1 1h0c-1 1 0 2-1 3 0 1-1 3-1 4h-1c-1-1-2-1-2-1v1c-1 1-1 1-2 1l-1-1c-1 0-1 0-2-1z" class="i"></path><path d="M243 492v-1l2-1 2 1c0 1-1 1-1 2h0c1 0 1 0 1 1s0 1-1 2h-2l-1 1h-5-1c-4 0-5-2-7-4 2 0 4 2 5 2h3 0c1-1 1-1 2-1 2 0 3 0 4-2h-1z" class="Z"></path><path d="M209 359h1l2 2c1 2 4 4 7 5 3 0 6-1 9-2l-1 2h-2v1c1 0 3 0 4 1h-1c-3 1-8 1-12-1-3-1-5-4-7-8z" class="V"></path><defs><linearGradient id="A" x1="219.262" y1="382.466" x2="226.177" y2="381.06" xlink:href="#B"><stop offset="0" stop-color="#515150"></stop><stop offset="1" stop-color="#6a6869"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M231 379h4c-1 1-3 1-4 2v3h-1-1c-4-1-9-1-13-1 1-1 2-1 3-1l1-1c-2 0-3 0-4-1h0c3 0 7 1 11 0 1 0 3 0 4-1z"></path><path d="M229 384c-1-1-1-1-2-1 1-1 3-1 4-2h0v3h-1-1z" class="J"></path><path d="M393 310c4 4 6 9 9 13 1 1 2 2 2 4v1c1 1 1 2 1 3l-3-3c0-1-1-3-2-4s-1-2-2-3h0c0-1-1-2-1-2v1c-1-1-1-2-2-3 0-1-1-2-1-3v-1c-1-1-1-2-1-3z" class="f"></path><path d="M359 231c1-1 1-1 2-1v1h1 1l2 2v3h-4-2c-1 1-2 1-3 1 1-1 1-1 1-2h-1 0c-1 1-1 1-2 1v-1c1 0 1-1 2-2h-8 2 5 2l2-2z" class="W"></path><path d="M359 231c1-1 1-1 2-1v1h1 1c0 1 0 1-1 1v1 2h1l-1 1c-2 0-2-1-3-2-1 0-2 0-2-1l2-2zM212 507c1 1 2 1 4 2l1-1c1 1 3 2 5 3 1 0 1-1 2-1h0 3v2h-5 0-3-2 0l1 1-1 1-4-2h-2l1-1s-1-1-1-2c0 0 1-1 1-2z" class="X"></path><path d="M213 512c1 0 1 0 2-1 1 0 1 0 2 1h0l1 1-1 1-4-2z" class="a"></path><path d="M346 239l1-1c1 1 3 0 3 2s-1 5-3 6c-2 2-4 2-6 1-1 0-1-1-2-2 1 0 1-1 1-1v-1h1l2 2c2-1 2-1 3-3 1-1 1-2 0-3h0z" class="M"></path><path d="M245 440h1c0 1 0 2 1 3v-1l1-1c0 1 0 3 1 4h-1v-1h-2c-1 1-1 1-1 2v1l2 2-2 4c-1-1-1-2-2-2l-1 1-1-1h1c-1-1-1-2-1-3s1-2 1-3 1-2 1-3l2-2z" class="N"></path><path d="M242 451v-5c0-1 1-1 1-2l1 1v5h0l1-3 2 2-2 4c-1-1-1-2-2-2l-1 1-1-1h1z" class="G"></path><path d="M366 274c8 6 15 14 17 24 1 3 1 6-1 9 0 1-1 1-2 2l-1-1c1-1 2-1 2-3 1-2 1-6 1-9-3-9-9-16-17-21l1-1z" class="J"></path><path d="M413 329h0 1c1 1 1 0 2 0 0 1 1 2 2 2 0 2 0 5 2 6v-2c0 1 1 2 0 4 1 1 1 2 2 3v1c1 0 1 0 1-1l1 3-1 4c0-2-1-4-2-6s-3-4-5-6-3-5-3-8z" class="a"></path><path d="M390 316c-1-1 0-3-1-4v-1c0-2-1-4-2-7-1 0 0-1-1-2v-3l7 11c0 1 0 2 1 3v1c0 1 1 2 1 3s0 1-1 2l-1-1c-1-1 0-2 0-3s-2-3-2-4h-1v5z" class="k"></path><path d="M190 323h1 0c3 4 7 4 12 6l-4 1c-3 2-5 1-8 0-3-2-3-4-4-7 3 3 5 5 10 5-2-1-3-1-5-2-1-1-1-2-2-3h0z" class="h"></path><path d="M231 384c3 0 5 0 8-1l1 1-8 3v1l-4 1c0 1-1 1-1 1l-2-1h-1-1c-5 0-8 3-12 7l-1-1c2-1 3-3 5-4h0c2-1 4-3 6-3 2-1 7-1 9-3v-1h1z" class="j"></path><path d="M225 389c1-1 2-1 4-1h1l2-1v1l-4 1c0 1-1 1-1 1l-2-1z" class="Y"></path><path d="M224 389h1l2 1c-1 1-1 2-2 2-2 1-4 2-4 4-2 1-3 3-4 5h0v1c-1 1-1 6-1 7 1 1 1 0 1 1h-1v-2c-2-3-2-9 0-12 1-3 4-5 7-6l1-1z" class="d"></path><path d="M224 389h1l2 1c-1 1-1 2-2 2-1-1-2 0-3 0h-1c1-1 2-1 2-2l1-1z" class="W"></path><path d="M238 375l1 1-1 1h1c1-1 1 0 2 0s2 0 4-1c-2 2-4 3-6 4v1h0 0-2v1h2l1 1h-1c-3 1-5 1-8 1v-3c1-1 3-1 4-2h-4c1 0 2-1 3-2h0c0-1 3-2 4-2h0z" class="c"></path><path d="M235 379h1v2c-1 1-1 1-2 1l-1-1h-2c1-1 3-1 4-2z" class="J"></path><path d="M238 375l1 1-1 1h1c1-1 1 0 2 0-2 1-3 2-5 2h-1-4c1 0 2-1 3-2h0c0-1 3-2 4-2h0zm165-10v1c1-1 1-2 1-2h1c0 3-2 6-3 8l-2 2v2c-1 1-3 2-4 3v1h-1 0l1 1 1-1c1 0 1-1 2-1 0 0 0-1 1-1l1-1 1-1h0 0v-1c1-1 2-1 3-2h0 1l1-1v1c-4 4-9 8-14 11l-1-1c1-3 5-4 6-8h-1 0c0-1 0-2 1-3v-1c0 1 0 0 1 1v-1h1c1-1 1-2 2-3 0-1 0-2 1-3z" class="d"></path><path d="M398 372v-1c0 1 0 0 1 1v-1h1l-2 4h-1 0c0-1 0-2 1-3z" class="N"></path><path d="M248 496c1 0 2-1 3-1v1c1 0 2 0 2 1l-2 2-3 3c-2 0-4 1-5 2h-1-1l2-1-1-1 3-3c-1-1-5 1-6 1h-4v-1c2 0 6 0 8-2h0-5 5l1-1c1 0 2 1 2 1 1 0 1 0 2-1z" class="X"></path><path d="M248 496c1 0 2-1 3-1v1l-2 2h-5c0-1 0-1-1-1l1-1c1 0 2 1 2 1 1 0 1 0 2-1z" class="N"></path><path d="M249 498l2-2c1 0 2 0 2 1l-2 2-3 3c-2 0-4 1-5 2h-1-1l2-1 6-5z" class="e"></path><path d="M202 284h1 0c1 1 2 3 3 4l1 1h-1c-1 2-2 4-2 5-1 1-1 1-2 1v1c-1 1-1 1-2 3-1-2-1-4-1-6 0-4 1-6 3-9z" class="b"></path><path d="M202 284h1 0c1 1 2 3 3 4l1 1h-1c-1 2-2 4-2 5-1 1-1 1-2 1v-1c0-2 0-5 1-6l1-1-2-3z" class="p"></path><path d="M479 437c-1-4-1-8 0-12l-2-2v-1h1c1 0 2 1 2 2l3 3c0 2 0 5-1 7v5c0 2 1 3 0 5-1-2-2-4-3-7z" class="U"></path><path d="M501 466l3-3c0-1 1-2 0-3l-1-1v-1c1-1 2-1 4 0 1 0 3 2 4 3-1 4-1 4-4 6 0 1-1 1-2 1 0 1-1 4-1 5 0-2 1-6 0-7 0-1-1-1-1-1l-2 1h0z" class="E"></path><path d="M549 565c0-1 1-2 1-3 0 0 1 0 2-1h0 2c-1 7-4 12-10 16l-2 1s0-1 1-2l1-1c1-1 1-2 2-3 1-2 2-5 3-7z" class="Z"></path><defs><linearGradient id="C" x1="407.357" y1="396.699" x2="396.266" y2="408.42" xlink:href="#B"><stop offset="0" stop-color="#606261"></stop><stop offset="1" stop-color="#918e91"></stop></linearGradient></defs><path fill="url(#C)" d="M402 400c3 0 6 1 9 1l9 3-1 1-10-2c-7-1-13-1-20 1v-1c0-1-1-1-2-1h2c1 0 2 0 2-1h0c1 0 2 0 3-1l3 1c2 0 3 0 5-1z"></path><defs><linearGradient id="D" x1="406.734" y1="375.952" x2="389.88" y2="385.771" xlink:href="#B"><stop offset="0" stop-color="#575758"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#D)" d="M407 373c0 1 0 1-1 2 1-1 2-1 3-2h0c-3 4-6 7-10 10-1 0-1 1-2 1l-1 2-4 2-7 2 1-1h0l1-1-1-1c3-1 5-2 7-3 5-3 10-7 14-11z"></path><path d="M387 388l10-4-1 2-4 2-7 2 1-1h0l1-1z" class="M"></path><path d="M252 373h1l-1 2c1 0 2 1 3 2l-7 4-4 1c-1 0-3 1-4 1l-1-1h-2v-1h2 0 0v-1c2-1 4-2 6-4 1 0 3-1 4-2 1 1 1 1 2 1h1v-2z" class="L"></path><path d="M239 381c2-1 4-1 7-1l-1 1h0l-1 1c-1 0-3 1-4 1l-1-1h-2v-1h2 0z" class="G"></path><path d="M252 373h1l-1 2c1 0 2 1 3 2l-7 4-4 1 1-1h0l1-1 5-5h1v-2z" class="J"></path><path d="M227 409l1-1h1l-1 2h2c1 2 1 4 1 5v1 2h-1v1h-1c-1 1-1 2-1 4v2l-1 1c-1-2-1-4-2-7 0-1 0-1-1-2 0-2 1-5 3-8z" class="S"></path><path d="M227 409l1-1h1l-1 2v1c-3 3-3 5-3 8 0-1 0-1-1-2 0-2 1-5 3-8z" class="g"></path><path d="M228 410h2c1 2 1 4 1 5v1 2h-1-1l-2-2v-1c1-1 1-3 1-4v-1z" class="n"></path><path d="M404 361v2h0c1-1 2-3 2-4s0-2 1-3v3h1v-1h1 0 1s0 1 1 2c-1 1-1 2-1 3v2c-1 1-1 1-1 2-1 0-1 1-2 1h0c-1 1-1 2-2 3-1 2-3 3-4 4l-1 1v-2l2-2c1-2 3-5 3-8h-1s0 1-1 2v-1-4h1z" class="T"></path><path d="M409 358h1s0 1 1 2c-1 1-1 2-1 3v2c-1 1-1 1-1 2-1 0-1 1-2 1h0c-1 1-1 2-2 3-1 2-3 3-4 4 0-1 2-2 3-4 0-1 1-1 1-2 2-2 2-4 3-6s1-3 1-5z" class="i"></path><path d="M234 228l4 1v1h1 1c0 1 1 1 1 2l-1 1h0l-3-1c-1 0-3 0-4 1h-2c-1 0-2 1-4 1v1l-1 1c-5 2-7 4-11 8 3-6 9-10 15-13l1-1 1-1h-4c2-1 4-1 6-1z" class="O"></path><path d="M240 230c0 1 1 1 1 2l-1 1h0l-3-1c1 0 2-1 3-2z" class="S"></path><path d="M234 228l4 1v1c-3 0-6 0-8 1l1-1 1-1h-4c2-1 4-1 6-1z" class="i"></path><path d="M241 175l1 1c-4 5-5 12-5 18 1 4 2 7 3 11h0v2h-1l-1-1h-1v2h-1v-1l-2-1 1-3v-1-4c1-1 1-2 1-3v-2c0-6 0-13 5-18z" class="I"></path><path d="M236 195l3 12-1-1h-1v2h-1v-1l-2-1 1-3v-1-4c1-1 1-2 1-3z" class="i"></path><path d="M235 203c1 1 2 1 2 3-1 0-1 1-1 1l-2-1 1-3z" class="N"></path><path d="M514 558c1 1 1 1 1 2v1c1 0 0 1 0 1 0 3 0 7 2 9l1 1v1c2 3 6 5 10 6-1 1-2 1-3 1-2 0-4-1-6-2-3-2-5-5-6-9s-1-8 1-11z" class="B"></path><path d="M352 245v-2l1-1 1 1c0 1 0 2-1 3 0 1 0 2-1 3h1v-1h1c1 0 2-1 2-2 1 0 1-1 2-1v-3h2v-1 1h0v1h1 0v2c0 2-1 2-1 4l-1 2c-1 0-1 0-2-1h0c0 1-1 1-1 2s-1 1 0 1h-2-1-2-1-2l-1-1c1-1 1 0 1-2 0 0 2-1 2-2h0c0-1 1-2 2-3z" class="j"></path><path d="M353 252c0-1 1-2 2-2 1-1 1 0 2 0 0 1-1 1-1 2h-3z" class="a"></path><path d="M357 250c1-1 2-2 2-5v-1l2-1h0v2c0 2-1 2-1 4l-1 2c-1 0-1 0-2-1z" class="k"></path><path d="M352 245v1c0 1-1 2-1 2v1h1c-1 1-1 1-2 1l-1 2h3 1 3c0 1-1 1 0 1h-2-1-2-1-2l-1-1c1-1 1 0 1-2 0 0 2-1 2-2h0c0-1 1-2 2-3zm15-72l9-9s0 1 1 1l1 1c-1 2-1 4-2 6-1 1-3 3-3 4h1v1l-2 1h-1v-1h-2-2c-1-1 0-1-1-2l-1-1 2-1z" class="W"></path><path d="M373 171c0 1 0 2-1 3 0 0-2 1-3 1v1h3v2h-1v-1h-2-2c-1-1 0-1-1-2l-1-1 2-1c0 1 0 1 1 1 2 0 3-1 5-3z" class="a"></path><path d="M367 173l9-9s0 1 1 1c-1 2-2 4-4 6s-3 3-5 3c-1 0-1 0-1-1z" class="e"></path><path d="M214 501c2 1 3 2 5 4 1 1 4 3 5 3s1 0 1-1h-1l-3-2v-1c2 1 4 2 7 3 5 1 10-2 14-5l1 1-2 1h1 1v1h-2l1 1h-1l-1 1 1 1h0c-2 1-4 1-6 1h-3c-2 1-7-1-8 1h0c-4-2-8-5-10-9z" class="b"></path><path d="M358 185c6 1 10-3 14-5l-9 6v1c1 0 2-1 3-1h1c-1 2-6 3-8 4h-2-2c-1 0-2 1-3 1-2 0-4 0-6 2h0-1-2 0c2-2 4-5 6-6 3 2 7-1 9-2z" class="h"></path><path d="M345 193h0c1-2 4-3 6-4l1 2c-2 0-4 0-6 2h0-1z" class="V"></path><path d="M355 187l1 1-1 2c-1 0-2 1-3 1l-1-2 4-2z" class="c"></path><path d="M355 187c1 0 2 0 3-1l1 1-1 1h2v1l-1 1h-2-2l1-2-1-1z" class="J"></path><path d="M267 457c5-1 8-2 12-5l-6 7h-2c-1 0-2 1-3 1-2 1-4 1-7 2-1 0-2 2-4 2-1 1-3 3-5 4-2 2-3 5-4 7l-1 4v-2-1-2c0-1 0-3 1-4l1-2c2-4 9-7 13-8h0c2-1 5-1 6-1v-1h1c2 0 2 0 3-1-1-1 0 0-1 0h-4z" class="W"></path><path d="M248 470h1c2-2 4-5 7-6l-4 4c-2 2-3 5-4 7l-1 4v-2-1-2c0-1 0-3 1-4z" class="g"></path><path d="M371 313v3h-1v1l-1 1c1-1 2-1 3-1v1-1-1-1c1 0 1 0 2 1v1-1h1c0 1 1 3 2 4 0-1 0-1 1-1l1 1v-2h1c0 1 1 4 1 5v2h0v3c0 1 0 2-1 2h-1s0-1-1-1h0c-1-2-2-4-4-5l-1-2c1 0 1 1 2 0h0l-1-1h0c-1 0-2-1-2-2v-1h-1c-1 1-3 2-4 3h0l-1-1c3-2 4-4 5-7z" class="W"></path><path d="M371 313v3h-1v1l-1 1c1-1 2-1 3-1v1-1-1-1c1 0 1 0 2 1v1-1h1c0 1 1 3 2 4 1 2 3 5 2 8h0c-1-1-1-2-2-3-1-3-2-7-4-9l1 5h0c-1 0-2-1-2-2v-1h-1c-1 1-3 2-4 3h0l-1-1c3-2 4-4 5-7z" class="e"></path><path d="M405 388l1-1h1l2-1 3-3c2-1 4-2 5-3l3-3h0s1 1 2 0v-1l1-1 2-3 1-1c1-1 1-4 2-5-2 10-8 17-16 22l-2 1-1-1c-3 2-6 3-9 3h0l1-1c1-1 2-1 3-2h1z" class="j"></path><path d="M405 388h0c2 0 4-2 6-3 0 0 1-1 2-1v1c-1 1-3 2-4 3-3 2-6 3-9 3h0l1-1c1-1 2-1 3-2h1z" class="f"></path><path d="M240 368c1-1 3-1 4-1h1l-6 4h-1c-1 0-1 1-2 1l-4 2-4 2c-4-1-8-1-12-3h2l-1-1h1c1 0 2 0 3 1l1-1v-1c2 1 6 1 8 0 1-1 3-2 5-2 1 1 3-1 5-1z" class="Z"></path><path d="M236 371h2c-1 0-1 1-2 1l-4 2-4 2c-4-1-8-1-12-3h2c2 1 4 1 7 1s8 0 11-3z" class="a"></path><path d="M240 368c1-1 3-1 4-1h1l-6 4h-1-2c-3 1-6 1-9 1h-5v-1c2 1 6 1 8 0 1-1 3-2 5-2 1 1 3-1 5-1z" class="f"></path><path d="M339 279c2-2 3-4 6-4l3-3h0l1-1 1 1c2 1 5 3 7 4 6 5 12 10 15 18l1 3h0c-5-10-13-20-23-24h-2c3 4 8 8 12 13h-1l-6-6h-3 0l-2-2v1c-1-1-1-2-3-3v1c1 1 2 2 2 3h-1l-1-2h-1c-2-1-1 0-2 1h-3z" class="Q"></path><path d="M348 278l-2-3 7 5h-3 0l-2-2z" class="k"></path><defs><linearGradient id="E" x1="224.25" y1="502.034" x2="206.456" y2="497.143" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#858586"></stop></linearGradient></defs><path fill="url(#E)" d="M211 503c-3-5-4-10-3-15l1 1c1 0 2-1 3-1 0 3 0 6 1 9 0 1 1 3 1 4 2 4 6 7 10 9-1 0-1 1-2 1-2-1-4-2-5-3l-6-5z"></path><path d="M348 203c0 1 1 1 1 1 2-1 3-1 4-1h1s1-1 2-1h1 0l-1 1v1h0 2v1h-2l-2 1h-1l2 1v1c0 1 0 2-1 4h0c1 1 1 2 1 3l1 1 2-1 1 1 4-3h0v1l-4 3h0v-1c-1 1-1 1-2 1 0 0-2-1-3-1h0c0-1-2-2-2-4-1 0-2-1-3-1h0c0-1 0-1-1-2h0v1h-2l-1-2h0s-1 0-1-1h0-1l1-1v1l1-1c1 0 0-1 1-1 0-1 1-2 2-2z" class="j"></path><path d="M348 206l1-2 2 1 2-1v1c-1 1-1 1-2 1 0 0-1 0-2 1l-1-1z" class="p"></path><path d="M348 208c1 1 1 1 1 2 1 0 1-1 2-1 0 0 1 0 1 1v2c-1 0-2-1-3-1h0c0-1 0-1-1-2h0v-1z" class="W"></path><path d="M348 206l1 1-1 1v1 1h-2l-1-2c1 0 1-1 1-1 1 0 1 0 2-1zm61 197l10 2 1-1c2 1 5 2 7 3h-1 0s-1 0-1-1h-1-1v1l-1-1c-1 0-3-1-4-1s-1 1-1 0h-1-3c-1-1-2-1-4-1h0-2v1h0c1 1 1 1 2 1 3 0 5 1 7 1h2 1 1c1 0 1 0 2 1h1c2 0 3 1 5 2h1c1 0 1 1 1 1h1l-6-1c-2 1-7-1-11-1 1 1 1 0 2 1-2 0-3-1-4-1h-4l-1-1h-3-10v-1c2-1 3-1 5-1 2-1 4-1 6-1h1v-1h0l3-1z" class="e"></path><path d="M394 407c2-1 3-1 5-1 0 1 0 1 1 2 2-1 4-1 5-1s2-1 3 0h6c4 0 8 1 11 3-2 1-7-1-11-1 1 1 1 0 2 1-2 0-3-1-4-1h-4l-1-1h-3-10v-1z" class="Z"></path><path d="M206 289l1 1 1 6-1 2 2 2v1c-1 0-2 1-2 2 1 1 3 2 5 3l1 1c2 1 4 1 7 2-2 0-3 0-4 1h0c-3 0-7-2-9-3s-3-2-4-3l-2-3-1-2c1-2 1-2 2-3v-1c1 0 1 0 2-1 0-1 1-3 2-5z" class="W"></path><path d="M204 301l4 5c-1-1-2-2-4-3l-1 1-2-3h3z" class="N"></path><path d="M204 294v3 2h-1c0 1 1 1 1 2h-3l-1-2c1-2 1-2 2-3v-1c1 0 1 0 2-1z" class="h"></path><path d="M204 294v3 2h-1l-1-3v-1c1 0 1 0 2-1z" class="Y"></path><path d="M206 289l1 1 1 6-1 2 2 2v1c-1 0-2 1-2 2l-3-6h0v-3c0-1 1-3 2-5z" class="M"></path><path d="M204 297h1c2 1 3 2 4 4-1 0-2 1-2 2l-3-6z" class="c"></path><path d="M237 394l1-2 2 2-3 2 1 1c-1 1-2 2-2 3-1 2-1 3-1 4h1v1c-1 2-1 4 0 7-1-1-1 0-2 0 0-1 0-2-2-2l-1 1v4c0-1 0-3-1-5h-2l1-2h-1l-1 1c-1-1-1 0 0-1-1-1-1-2-1-3l1-2 1-1h1c1-2 2-2 3-3 0-1 3-3 4-4 0 0 0-1 1-1z" class="Z"></path><path d="M237 394l1-2 2 2-3 2c-1 1-2 2-3 4l-1 8h-1c0-1 0-3-1-3 0-3 2-4 1-6 0-1 3-3 4-4 0 0 0-1 1-1z" class="X"></path><path d="M237 394l1-2 2 2-3 2c-1 1-2 2-3 4 0 0 0-1-1-1 1-1 1-2 2-3 1 0 0 0 1-1 0 0 0-1 1-1z" class="W"></path><path d="M232 399c1 2-1 3-1 6l-1 1v4h-2l1-2h-1l-1 1c-1-1-1 0 0-1-1-1-1-2-1-3l1-2 1-1h1c1-2 2-2 3-3z" class="V"></path><path d="M228 402h1v2c-1 1-2 2-2 4-1-1-1-2-1-3l1-2 1-1z" class="Z"></path><path d="M232 399c1 2-1 3-1 6l-1 1v-3h0l-1 1v-2c1-2 2-2 3-3z" class="K"></path><path d="M241 508c2 0 3 1 5 1-3 3-6 5-10 6h-1-3c-3 0-6 1-8 1l-7-2 1-1-1-1h0 2 3 0 5v-2h-3c1-2 6 0 8-1h3c2 0 4 0 6-1z" class="Y"></path><path d="M227 510l2 1h2l1 1h0 1c-2 1-3 0-5 1h-1c-1 0-1 0-2 1h-2l-2-1s-1-1-2-1h3 0 5v-2z" class="d"></path><path d="M227 510l2 1s1 0 1 1c-3 1-6-1-9 1 0 0-1-1-2-1h3 0 5v-2z" class="b"></path><path d="M249 428c1 0 2 1 2 1h2 0 2c2 1 4 1 5 2-2 0-4 1-5 3h0l-3 2c0-1 0-3-1-4 0 1 0 2-1 3h0v2c-1 1-1 3-2 4l-1 1v1c-1-1-1-2-1-3h-1l-2 2-2-1 1-1c0-1-1-2-2-3h0c2-5 4-6 9-8v-1z" class="k"></path><path d="M246 437v-1c1 0 1-1 2-2 1 0 2 1 2 1h0v2c-1 1-2 1-3 1 0 0 0-1-1-1z" class="T"></path><path d="M246 437c1 0 1 1 1 1 1 0 2 0 3-1-1 1-1 3-2 4l-1 1v1c-1-1-1-2-1-3h-1c0-1 1-2 1-3z" class="X"></path><path d="M249 428c1 0 2 1 2 1h2 0-1l-1 1c-1 0-2 1-3 1v1h-1c-3 2-5 5-5 8 0-1-1-2-2-3h0c2-5 4-6 9-8v-1z" class="W"></path><defs><linearGradient id="F" x1="208.252" y1="524.11" x2="200.21" y2="524.288" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#F)" d="M199 519c4-3 7-7 13-6h0 1c0 1 0 1 1 1l1 1c-1 0-2 0-3-1 0 0-1-1-2 0h0c0 1 0 1 1 1h-2v-1c-1 0-2 0-3 1h0v1 1c-1 0-1 0 0 1h0-1v4l1-1h0c-1 0-1-1-1-2l1-1 1 1v1c0 1 1 1 2 1h0 2l-2 2c1 0 1 0 2 1-1 0-1 0-1 1 0-1-1-1-1-2v2l-1 1v2 2 1h0 1v2 1h-1v1c1 0 1 0 2-1h1c-2 1-3 1-4 0-1 1-1 1-1 2-2-4-3-7-5-11l-1-4-1-2z"></path><path d="M200 521h3v3c0 1-1 1-2 1l-1-4z" class="X"></path><path d="M405 327c0-2-1-3-2-4 0-1-2-4-3-4v-1h0v-2h0c4 6 7 13 10 20v1l1 4 1 4v1 2h0c0 2 1 3 0 5v3l1-2v-1 1c0 2-1 4-2 6v3h0-1c0-1 0-2 1-3-1-1-1-2-1-2h-1 0-1l1-3c-2-3-1-9-1-13-1-2-1-4-2-6 0-1-1-4-1-5s0-2-1-3v-1h1z" class="g"></path><path d="M410 348v-3c0-1 1-1 1-2 1 6 1 11 0 17-1-1-1-2-1-2h-1 0-1l1-3 1-7z" class="Y"></path><path d="M404 327h1c1 1 1 3 2 4l1 1c1 3 1 6 2 9v7l-1 7c-2-3-1-9-1-13-1-2-1-4-2-6 0-1-1-4-1-5s0-2-1-3v-1z" class="p"></path><path d="M379 389h1 1c2 0 3-1 5-2h0l1 1-1 1h0l-1 1 7-2v1h0 1c-1 1-2 1-3 2 2 1 6-1 8-2h1c0 1 0 1 1 1h0 1 0l-1 1h0c3 0 6-1 9-3l1 1c-1 0-2 1-2 1h-1s-1 0-1 1h-1l-7 2h0-1c-1 0-2 1-4 1-3 0-6 0-9 1-1 0-2 0-3-1-2 0-3 1-4 0h-5-1c-1 0-2 1-4 1v-1h1c1 0 2 0 3-1 1-2 6-3 8-4z" class="e"></path><path d="M379 389h1 1c2 0 3-1 5-2h0l1 1-1 1h0l-1 1c-5 2-9 3-14 3 1-2 6-3 8-4z" class="O"></path><path d="M247 363v-1c2-1 2-1 3-2v1l1 2h0c0 1 1 2 1 2v1l-2 2h1c0 1 1 1 1 2-1 1-2 2-4 3l-1 1h2c-1 1-3 2-4 2-2 1-3 1-4 1s-1-1-2 0h-1l1-1-1-1h0c-1 0-4 1-4 2h-2 0l1-2-1-1 4-2c1 0 1-1 2-1h1l6-4v1l3-2v-2l-1-1z" class="S"></path><path d="M236 372l-2 2c1 1 2 1 4 1-1 0-4 1-4 2h-2 0l1-2-1-1 4-2z" class="G"></path><defs><linearGradient id="G" x1="247.093" y1="368.12" x2="244.584" y2="375.434" xlink:href="#B"><stop offset="0" stop-color="#9c9e9d"></stop><stop offset="1" stop-color="#bab7b8"></stop></linearGradient></defs><path fill="url(#G)" d="M251 368c0 1 1 1 1 2-1 1-2 2-4 3l-1 1h2c-1 1-3 2-4 2-2 1-3 1-4 1s-1-1-2 0h-1l1-1-1-1h1 1c0-1 1-1 1-2 2-1 5-1 7-2l3-3z"></path><path d="M240 375h3c2-1 3-1 4-1h2c-1 1-3 2-4 2-2 1-3 1-4 1s-1-1-2 0h-1l1-1-1-1h1 1z" class="J"></path><path d="M247 363v-1c2-1 2-1 3-2v1l1 2h0c0 1 1 2 1 2v1l-2 2h1l-3 3v-2l-1-1v1c-2 1-5 2-8 2l6-4v1l3-2v-2l-1-1z" class="Z"></path><path d="M248 369l1-1h-1c0-1 0-1 1-2l1-1c0-1 0-1-1-2v-2h1l1 2h0c0 1 1 2 1 2v1l-2 2h1l-3 3v-2z" class="O"></path><path d="M260 498v2c0 2 0 2-2 4-1 1-2 2-4 3-1 1-2 1-3 2s-2 2-4 3l-2 1-4 2c-1 1-3 1-4 1l-1-1c4-1 7-3 10-6-2 0-3-1-5-1h0l-1-1 1-1h1l-1-1h2v-1c1-1 3-2 5-2l3-3 2 2 2-1h0c0 1-1 1-2 2l1 1c1-1 2-2 4-2l2-3z" class="X"></path><path d="M260 498v2c0 2 0 2-2 4-1 1-2 2-4 3-1 1-2 1-3 2s-2 2-4 3c2-4 4-5 6-7v-1h1 0 1l3-3 2-3z" class="a"></path><path d="M248 502l3-3 2 2 2-1h0c0 1-1 1-2 2l1 1c-2 1-4 1-6 2l1 1c-1 1-2 3-3 3-2 0-3-1-5-1h0l-1-1 1-1h1l-1-1h2v-1c1-1 3-2 5-2z" class="N"></path><path d="M248 505l1 1c-1 1-2 3-3 3-2 0-3-1-5-1h0 1l6-3z" class="d"></path><path d="M248 502l3-3 2 2 2-1h0c0 1-1 1-2 2h0c-2 1-4 1-6 2l-5 2v1 1h-1l-1-1 1-1h1l-1-1h2v-1c1-1 3-2 5-2z" class="X"></path><path d="M248 502l3-3 2 2c-2 1-2 1-4 1h-1z" class="h"></path><path d="M339 194c4-5 10-8 15-12 4-2 8-5 11-8l1 1c1 1 0 1 1 2h2 2v1l-13 7c-2 1-6 4-9 2-2 1-4 4-6 6h0 2 1 1c-1 1-2 1-2 1v1c1 0 4-2 6-2h0c-1 1-3 2-5 3v-1l-3 1-1-1v1 1h-1s-1 0-2 1v-2l-2 2-2 2v-3l4-3z" class="N"></path><path d="M339 196h0c1-1 2-2 3-2v1 1 1h-1s-1 0-2 1v-2z" class="g"></path><path d="M367 177h2c0 1-1 1-2 2-1 0-2 0-3 1-2 0-5 2-8 2 2-1 3-2 5-2 1-1 2-1 3-2 1 0 2 0 3-1h0z" class="K"></path><path d="M369 177h2v1l-13 7c-2 1-6 4-9 2 4-2 8-4 12-5 1-1 3-1 3-2 1-1 2-1 3-1 1-1 2-1 2-2z" class="G"></path><path d="M366 186l1-1c1 0 2 0 3-1h1c0 1 0 1-1 1v1c-1 2-5 5-7 6h12c-2 1-5 1-7 2-1 0-3 2-5 2 0 0 0-1-1-1s-3 1-5 1c-2 1-5 1-7 2-1 0-1 0-1 1h-2c-1 0-1 0-2-1h-1c0-1 1-1 2-2h0c2-1 4-2 5-3h0c-2 0-5 2-6 2v-1s1 0 2-1h-1 0c2-2 4-2 6-2 1 0 2-1 3-1h2 2c2-1 7-2 8-4h-1z" class="j"></path><path d="M355 190h2l2 1v1l-5 1h5c1-1 3-1 4-1v1h-2l-1 1c0 1-2 1-3 1-2 0-4 0-6 1-1 0-1 1-1 2-1 0-1 0-1 1h-2c-1 0-1 0-2-1h-1c0-1 1-1 2-2h0c2-1 4-2 5-3h0c-2 0-5 2-6 2v-1s1 0 2-1h-1 0c2-2 4-2 6-2 1 0 2-1 3-1z" class="O"></path><path d="M346 196v1l1-1h4c-1 0-1 1-1 2-1 0-1 0-1 1h-2c-1 0-1 0-2-1h-1c0-1 1-1 2-2z" class="X"></path><path d="M242 176h3c1 0 2 1 3 1l7 7 1 2v1c1 1 1 2 1 3h1v2h-1c-2 0-3-1-4-2h0c-2-1-2-2-4-2h-1-2-1-1v-1l1-1v-2c0-2-1-3-1-4-2 0-3 0-4 1-1 3-1 6-1 9s0 7 1 10c0 2 0 3 1 4l-1 1c-1-4-2-7-3-11 0-6 1-13 5-18z" class="M"></path><path d="M240 181c1-2 2-3 3-4h1l2 2c0 2 1 4 2 6v3h-2-1-1v-1l1-1v-2c0-2-1-3-1-4-2 0-3 0-4 1z" class="G"></path><path d="M248 177l7 7 1 2v1c1 1 1 2 1 3h1v2h-1c-1-2-1-3-2-4-1 0-2-1-2-2v-1c-1-1-4-2-5-4-1-1-1-2 0-4z" class="a"></path><path d="M252 468c2-1 4-3 5-4 2 0 3-2 4-2 3-1 5-1 7-2 1 0 2-1 3-1h2l-4 5h0c-2 3-5 5-6 8-1 0-1 1-2 1l-1-1c-1 0-1 0-1 1h0-2c-1 1-1 1-2 1h-1 0-2v-1l-2 2h-2c1-2 2-5 4-7z" class="b"></path><path d="M254 473c2 0 2-1 3-3h1c0 1 0 2 1 3h-2c-1 1-1 1-2 1h-1 0v-1z" class="X"></path><path d="M252 473l1-2c0-1 2-3 3-4s2-1 3-1h0c0 1-1 1-1 1-2 2-3 3-4 6h0v1h-2v-1z" class="Y"></path><path d="M260 472h0c0-1 2-3 2-3 0-1-1-1-1-2 3-1 5-3 7-4h1v1c-2 3-5 5-6 8-1 0-1 1-2 1l-1-1z" class="i"></path><path d="M250 435c1-1 1-2 1-3 1 1 1 3 1 4l3-2c0 1 0 1-1 2l-1 2c-1 1-1 1-1 2h0v4h1c0 2 2 3 3 5v-1l-1-2h0 2c1 1 2 1 4 1 0 1 0 1 1 1s2 1 2 0c1 0 1 1 2 1h0c0 1-1 1-2 1v1l2 2h-2c-3 1-5 0-8 1h0-1c-2 0-5-2-6-4-1 0-1-1-2-1l-2-2v-1c0-1 0-1 1-2h2v1h1c-1-1-1-3-1-4 1-1 1-3 2-4v-2h0z" class="M"></path><path d="M264 453h-4l1-1c1 0 2-2 3-2v1l2 2h-2z" class="d"></path><path d="M256 449v-1l-1-2h0 2c1 1 2 1 4 1 0 1 0 1 1 1s2 1 2 0c1 0 1 1 2 1h-3c0 1-1 1-1 1-2 0-4 0-6-1zm-8-4h1c1 3 4 5 7 7h0l1 1h0-2c-4-1-6-4-8-7l1-1z" class="W"></path><path d="M250 435c1-1 1-2 1-3 1 1 1 3 1 4l3-2c0 1 0 1-1 2l-1 2c-1 1-1 1-1 2h0v4c0 1 0 1-1 2 1 2 3 4 5 5h1c-1 1 0 1-1 1h0c-3-2-6-4-7-7-1-1-1-3-1-4 1-1 1-3 2-4v-2h0z" class="P"></path><path d="M250 435c1-1 1-2 1-3 1 1 1 3 1 4l3-2c0 1 0 1-1 2l-1 2c-1 1-1 1-1 2h0v4c0 1 0 1-1 2-1-1-1-2-1-4l1-1c0-2 1-5 0-6h-1 0z" class="Z"></path><path d="M339 198c1-1 2-1 2-1h1v-1-1l1 1 3-1v1h0c-1 1-2 1-2 2h1c1 1 1 1 2 1h2 3c2 0 2 0 3 1h3c-1 1-1 1-1 2h0 0-1c-1 0-2 1-2 1h-1c-1 0-2 0-4 1 0 0-1 0-1-1-1 0-2 1-2 2-1 0 0 1-1 1l-1 1v-1l-1 1-1 1-1-1-1 1v1 1c-2 1-1-1-3-2l-1 1h-1c-1-3-1-6 0-9l2-2 2-2v2z" class="k"></path><path d="M339 202c1 1 1 2 1 3h1v-1l1-1v4h-1l-1 1h-1c0-2 1-2 1-3s0-1-1-2v-1z" class="g"></path><path d="M348 203c2-1 5-2 8-2l1 1h-1c-1 0-2 1-2 1h-1c-1 0-2 0-4 1 0 0-1 0-1-1z" class="N"></path><path d="M347 201h0c-2-1-3-2-4-3h1 1c1 1 1 1 2 1h2 3 0l1 1-1 1c-1 0-4-1-5 0z" class="a"></path><path d="M339 198c1-1 2-1 2-1h1v-1-1l1 1 3-1v1h0c-1 1-2 1-2 2h-1c1 1 2 2 4 3h0-2c-2 0-3-1-4-1h-2v-2z" class="Y"></path><path d="M337 198c1 1 1 2 2 3v1 1c1 1 1 1 1 2s-1 1-1 3h1v1 1c-2 1-1-1-3-2l-1 1h-1c-1-3-1-6 0-9l2-2z" class="i"></path><path d="M336 201h1v5s-1 0-1 1c0-2 0-2-1-4l1-2z" class="k"></path><path d="M279 184c3 0 5 0 8-1 0 1 0 1 1 1h3 0c0-1-1-1-1-1 2-1 5 0 7 1h1v-1h0 1l1-1h1c1 0 4 0 5 2h1v1l1-1v-1c1 1 1 2 2 3l2-2h0c0 1 0 1 1 1-1 1 0 2-1 2l-1-1h-1c0 1-1 1-1 1h-1c-1 0-2-1-3 0h-1s-1-1-2-1v2h-1c0-1 0-1-1-1h0v2l-1 1c-1-1-2-2-3-2 0 1-1 1-2 1 0-1-1-1-2-1l-1 1v2h-1v-1c-1-1-2-1-3-1l-1 1v1l-2-1v2c-1-1-2-1-2-1h-1l-1-1v-1l-2-2v-1c1 0 2 0 3-1h0l-2-1z" class="S"></path><path d="M281 185l2 1-1 1 1 1h0-2c0 2 0 2 1 3h-1l-1-1v-1l-2-2v-1c1 0 2 0 3-1h0z" class="J"></path><path d="M279 184c3 0 5 0 8-1 0 1 0 1 1 1h3 0c0-1-1-1-1-1 2-1 5 0 7 1h1v-1h0 1l1-1h1c1 0 4 0 5 2-1 0-4 1-5 0h-1v2h0c-2-1-2-1-4-1 0 1 0 1 1 2h-1s-1-1-2 0c-3 0-5-1-8-1h0l2 2h-1l-4-2-2-1-2-1z" class="M"></path><defs><linearGradient id="H" x1="326.97" y1="185.313" x2="339.89" y2="183.155" xlink:href="#B"><stop offset="0" stop-color="#5f5e60"></stop><stop offset="1" stop-color="#8b8a89"></stop></linearGradient></defs><path fill="url(#H)" d="M336 180l6-2v2c-1 1-1 2-1 2v2h0c-1 1-1 2-2 3-2 0-2 0-3 1l-1 1h-1l-1-1h-2 0c-1-1-1-1-2-1 0 1-1 1-1 1 0 1 0 1-1 1 0-1 0-2-1-2s0 1 0 2c-2 0-1-1-2-1-1-1-1-1-2-1h0c-1-1-1-1-2-1l-1 1h-2 0l-1-1c-1 0-2 0-3-1-1 0-1 0-1-1h0l-2 2c-1-1-1-2-2-3v1l-1 1v-1c0-1 0-1-1-2h1 2 3c1 1 2 2 3 2v-1l-1-1h1 3c2 1 3 0 5 2h1v-1-1h1c3 2 7 0 11-2z"></path><path d="M334 187h2v1l-1 1c-1-1-1-2-1-2z" class="l"></path><path d="M335 184l2-2 1 1c-1 1-1 2-3 2v-1z" class="M"></path><path d="M336 180l6-2v2c-1 1-1 2-1 2v2h0c-1-1-1 0-3 0v-1l-1-1-2 2c-1 0-1 0-2-1l3-3z" class="G"></path><path d="M307 184c0-1 0-1-1-2h1 2 3c1 1 2 2 3 2l1 2h1l-1-2h1 1v-1c1 0 2 1 3 1s2 2 3 3l1-1v-1c1 0 1 0 2 1 1 0 2-1 3-2 2 1 2 1 4 1v2s0 1 1 2h-1l-1-1h-2 0c-1-1-1-1-2-1 0 1-1 1-1 1 0 1 0 1-1 1 0-1 0-2-1-2s0 1 0 2c-2 0-1-1-2-1-1-1-1-1-2-1h0c-1-1-1-1-2-1l-1 1h-2 0l-1-1c-1 0-2 0-3-1-1 0-1 0-1-1h0l-2 2c-1-1-1-2-2-3v1l-1 1v-1z" class="Z"></path><path d="M290 191h1v-2l1-1c1 0 2 0 2 1 1 0 2 0 2-1 1 0 2 1 3 2l1-1 1 1h0l2 2 2 2c-1 1-1 1-2 1h-2l1 2s1 1 1 2h0l1 3 1 1h-2v1c0 1 0 1-1 2 0-1-1-3-3-4v-1c-1-1-2-3-4-4l-1 1h1l-1 1-2-2h-2l-2-2c-1-1-3-2-4-3v-2l2 1v-1l1-1c1 0 2 0 3 1v1z" class="Q"></path><path d="M299 201h1v-1l-1-2h0c2 0 2 1 4 1l1 3 1 1h-2v1c0 1 0 1-1 2 0-1-1-3-3-4v-1z" class="K"></path><path d="M303 204c-1-1-2-3-2-4h0c1 1 2 2 3 2h0l1 1h-2v1z" class="G"></path><path d="M284 192v-2l2 1c2 1 6 4 7 6l1 1h1l-1 1-2-2h-2l-2-2c-1-1-3-2-4-3z" class="L"></path><path d="M290 191h1v-2l1-1c1 0 2 0 2 1 1 0 2 0 2-1 1 0 2 1 3 2l1-1 1 1h0l2 2 2 2c-1 1-1 1-2 1h-2l1 2h-1c-1-1-2-1-3-2v1 1h-3c-2-2-4-4-5-6h0z" class="D"></path><path d="M294 190h1c1 0 3 2 3 3v2h-1s0-1-1-1c-1-1-2-2-2-4z" class="C"></path><path d="M300 189l1 1h0l2 2 2 2c-1 1-1 1-2 1h-2-1v-1s1-1 1-2-1-1-2-2l1-1z" class="L"></path><defs><linearGradient id="I" x1="254.289" y1="487.814" x2="246.384" y2="495.697" xlink:href="#B"><stop offset="0" stop-color="#606060"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#I)" d="M248 483c0-1 1-2 1-2 1-2 1-3 2-5l1 1c1 1 2 2 3 2s1 1 1 1h4c0 1 0 3-1 4l-1 1v1c-1 2-2 5-3 7 0 1-1 3-2 4 0-1-1-1-2-1v-1c-1 0-2 1-3 1-1 1-1 1-2 1 0 0-1-1-2-1h2c1-1 1-1 1-2s0-1-1-1h0c0-1 1-1 1-2l-2-1 3-3 1-3-1-1z"></path><path d="M251 488c2-2 3-4 4-6l1 1h1c0 1 0 3-1 4v-1h-1c-2 1-3 3-5 4l-1-1 2-1z" class="T"></path><path d="M256 483h1c0 1 0 3-1 4v-1h-1c0-2 0-2 1-3z" class="a"></path><path d="M248 487l3-1v2l-2 1c0 1-1 1-2 2v1c1 0 2-1 3-1h0c0 1-2 2-3 3 0-1 0-1-1-1h0c0-1 1-1 1-2l-2-1 3-3z" class="O"></path><defs><linearGradient id="J" x1="257.167" y1="488.477" x2="248.954" y2="494.29" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#J)" d="M257 483v1 1h1v1c-1 2-2 5-3 7 0 1-1 3-2 4 0-1-1-1-2-1v-1c-1 0-2 1-3 1 1-1 3-3 5-4 1-2 2-4 3-5s1-3 1-4z"></path><path d="M251 495c1 0 3-2 4-3v1c0 1-1 3-2 4 0-1-1-1-2-1v-1z" class="i"></path><path d="M248 483c0-1 1-2 1-2 1-2 1-3 2-5l1 1c1 1 2 2 3 2s1 1 1 1h4c0 1 0 3-1 4l-1 1h-1v-1-1h-1l-1-1c-1 2-2 4-4 6v-2l-3 1 1-3-1-1z" class="f"></path><defs><linearGradient id="K" x1="250.379" y1="479.342" x2="251.292" y2="485.823" xlink:href="#B"><stop offset="0" stop-color="#585658"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#K)" d="M249 484c0-1 2-4 2-5v-1h1c1 2 0 3 0 5h1l2-2v1c-1 2-2 4-4 6v-2l-3 1 1-3z"></path><path d="M313 185c1 1 2 1 3 1l1 1h0 2l1-1c1 0 1 0 2 1h0c1 0 1 0 2 1 1 0 0 1 2 1 0-1-1-2 0-2s1 1 1 2 0 2 1 3h0v1c1 2 1 4 1 6 0 0 0 1-1 1l-1-1h0l-1-1h0-1 0c0 1 0 1-1 1h0c-2-1-3-2-4-4l-1-2-3-3-1 1h0c-1-1-1-2-2-2h-4s-1 0-1 1h0v1c1 1 2 2 2 3h-1v3h-1l-2-2h-1v-1l-2-2-2-2h0l-1-1v-2h0c1 0 1 0 1 1h1v-2c1 0 2 1 2 1h1c1-1 2 0 3 0h1s1 0 1-1h1l1 1c1 0 0-1 1-2z" class="H"></path><path d="M301 190c2-1 2-1 4-1 1 0 2 2 2 2l1 2c0 1 1 1 0 2h-2-1v-1l-2-2-2-2z" class="I"></path><path d="M303 192h0c2 0 2 1 3 2 1 0 1-1 2-1 0 1 1 1 0 2h-2-1v-1l-2-2zm13-2c1-1 1-1 2-1 0 0 1 0 1 1 2-1 2-2 3-1 1 2 1 4 3 6l-1 1c-1-1-1-2-2-3v1c-1 0-2 0-3-1l-3-3z" class="C"></path><path d="M322 187c1 0 1 0 2 1 1 0 0 1 2 1 0-1-1-2 0-2s1 1 1 2 0 2 1 3h0v1c1 2 1 4 1 6 0 0 0 1-1 1l-1-1h0l-1-1h0-1 0c0 1 0 1-1 1h0c-2-1-3-2-4-4l-1-2c1 1 2 1 3 1v-1c1 1 1 2 2 3l1-1c-1-1-1-1 0-2v-2s0-1-1-2h0l-2-2z" class="L"></path><path d="M328 192c0 1 0 2-1 2h0v-2h1 0z" class="H"></path><path d="M319 193c1 1 2 1 3 1v-1c1 1 1 2 2 3l1-1c0 1 0 1 1 2l-1 1c-1-1-1-1-1-2l-4-1-1-2z" class="D"></path><defs><linearGradient id="L" x1="242.836" y1="213.433" x2="234.068" y2="219.63" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#373638"></stop></linearGradient></defs><path fill="url(#L)" d="M237 208v-2h1l1 1c1 4 1 9 2 14 1 2 2 4 3 7h-1-2l-2 2h-1v-1l-4-1c-2 0-4 0-6 1h-1-1c-1-1-1-1-2-1h0c-1-1-1-1-1-2 1-2 3-2 4-3v-2-1c2 0 3-1 5-2l1-1h1l2-1 2-1c-1-2 0-3-1-5v-2z"></path><path d="M232 223c3 0 5 0 7 2l1 1h-4 0l-2-1c-1-1-2-1-4-1 1 0 1 0 2-1z" class="l"></path><path d="M236 226h4c1 1 2 1 3 2h-2l-2 2h-1v-1l-1-2-1-1h0z" class="c"></path><path d="M230 224c2 0 3 0 4 1l2 1h0 0l1 1 1 2-4-1c-2 0-4 0-6 1h-1-1c-1-1-1-1-2-1 2-2 4-3 6-4z" class="G"></path><path d="M230 224c2 0 3 0 4 1-1 0-3 0-5 1h-1l-2 1c1 1 2 2 3 1h1l-1-1 5 1c-2 0-4 0-6 1h-1-1c-1-1-1-1-2-1 2-2 4-3 6-4z" class="Z"></path><path d="M232 218h2s1-1 2 0h1 1v3h-1c-1 0-1 0-2 1-1 0-2 0-3 1h0 0c-1 1-1 1-2 1-2 1-4 2-6 4h0c-1-1-1-1-1-2 1-2 3-2 4-3v-2-1c2 0 3-1 5-2z" class="N"></path><path d="M232 218h2s1-1 2 0h1c-3 2-6 4-10 5v-2-1c2 0 3-1 5-2z" class="T"></path><path d="M244 382l4-1-2 2c1 1 0 2 0 3h1c0 3-1 5 0 8h0-3v-1l-2-1-2 2-2-2-1 2c-1 0-1 1-1 1-1 1-4 3-4 4-1 1-2 1-3 3h-1l-1 1v-2c0-1 0-1-1-2l3-3c-1 0-4 2-5 2h0c2-2 5-5 8-5 1 0 1 0 2-1h0-2-4c-2 1-4 3-7 4h0c0-2 2-3 4-4 1 0 1-1 2-2 0 0 1 0 1-1l4-1v-1c2-1 5-2 8-3l-1-1h1c1 0 3-1 4-1z" class="k"></path><path d="M244 382l4-1-2 2-14 5v-1c2-1 5-2 8-3l-1-1h1c1 0 3-1 4-1z" class="a"></path><path d="M228 389l1 1c2-1 4-1 7-2v1c-1 1-2 1-4 1-1 1-2 1-4 2s-4 3-7 4h0c0-2 2-3 4-4 1 0 1-1 2-2 0 0 1 0 1-1z" class="j"></path><path d="M231 396c0-1 1 0 2-1l1 1v-1c1 0 1 0 1-1h2c-1 0-1 1-1 1-1 1-4 3-4 4-1 1-2 1-3 3h-1l-1 1v-2c0-1 0-1-1-2l3-3h2z" class="Y"></path><path d="M229 396h2c0 2-2 4-3 6l-1 1v-2c0-1 0-1-1-2l3-3z" class="g"></path><path d="M246 386h1c0 3-1 5 0 8h0-3v-1l-2-1-2 2-2-2c2-1 3-2 5-3 1 0 2-1 3-1v-2z" class="f"></path><path d="M242 392l2-1h1v1l-1 1v1-1l-2-1z" class="b"></path><defs><linearGradient id="M" x1="418.576" y1="365.569" x2="400.237" y2="364.504" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#585657"></stop></linearGradient></defs><path fill="url(#M)" d="M407 372c0-1 1-1 2-1v-1h0c2-1 4-3 4-5v-1c2-4 3-7 4-11 0-3 1-6 0-10v-1c2 4 4 12 3 17 0 1-1 2-1 3-1 1-1 3-1 4v1c0 2-2 2-3 3 1 0 2 1 3 0h1v-2h1v2l1-1v-1h-1v-1h1l1-1h0c0 2-1 3-1 5h0c-2 2-2 4-4 6v1h0l-1-1v-1c1-1 1-2 2-3-1 0-1-1-1 0l-2 1c-2 1-2 2-3 3h-1l1 1h0c-3 0-5 3-7 4l-6 3v-2c4-3 7-6 10-10h0c-1 1-2 1-3 2 1-1 1-1 1-2v-1z"></path><defs><linearGradient id="N" x1="389.003" y1="381.749" x2="384.56" y2="378.124" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#N)" d="M394 362v-1l1-1h0l1 2c0 1 0 2 1 3v1 3c0 1 1 2 1 3-1 1-1 2-1 3h0 1c-1 4-5 5-6 8l1 1c-2 1-4 2-7 3h0c-2 1-3 2-5 2h-1-1c-2 1-7 2-8 4-1 1-2 1-3 1l14-10 1-2c3-3 5-6 6-10v-4-2h1v-1h0c0 2 1 4 1 5 0 2-1 3-1 4 1-2 3-4 3-6 1-2 1-4 1-6z"></path><g class="b"><path d="M397 366v3c0 1 1 2 1 3-1 1-1 2-1 3h0 1c-1 4-5 5-6 8l1 1c-2 1-4 2-7 3h0c-2 1-3 2-5 2h-1-1c2-2 6-4 8-5 6-4 9-10 10-17v-1z"></path><path d="M394 362v-1l1-1h0l1 2c0 1 0 2 1 3v1 1c-1 0-1 0-2 1-2 5-4 9-8 13-2 1-3 2-4 3h-1l1-2c3-3 5-6 6-10v-4-2h1v-1h0c0 2 1 4 1 5 0 2-1 3-1 4 1-2 3-4 3-6 1-2 1-4 1-6z"></path></g><path d="M395 365h2v1 1c-1 0-1 0-2 1v-3z" class="Y"></path><path d="M394 362v-1l1-1h0l1 2c0 1 0 2 1 3h-2l-1-3z" class="W"></path><path d="M242 392l2 1v1h3 0v1 2h0c-1 0-2 2-3 2h0v1 2l1-1 1 1 1-1h1c0 1 1 2 2 3l-1 1-2 2h0c-2 2-2 3-3 5 0 2 0 4 1 6l-1 1c0-2-1-4-2-5l-1 1 1 3-2 1v1c-1-1-1-2-2-3l-1-1-1-1v-3c-1-3-1-5 0-7v-1h-1c0-1 0-2 1-4 0-1 1-2 2-3l-1-1 3-2 2-2z" class="V"></path><path d="M237 414l1-1v4l-1-1-1-1h1v-1z" class="c"></path><path d="M236 405l1-1c0 3-1 8 0 10v1h-1v-3c-1-3-1-5 0-7z" class="L"></path><path d="M237 400c1-2 2-4 4-4l1 1-2 2-1 2v1h1 0c-2 1-2 2-3 3 0-1 0-1 1-2v-1c0-1 0-1-1-2z" class="G"></path><path d="M240 419c0-5-2-9-1-13v2l1 1v4h1v2l1 3-2 1z" class="J"></path><path d="M242 392l2 1v1h3l-1 2c-2 0-4 3-6 3l2-2-1-1c-2 0-3 2-4 4-1 1-1 3-1 4h-1c0-1 0-2 1-4 0-1 1-2 2-3l-1-1 3-2 2-2z" class="M"></path><path d="M242 392l2 1v1h3l-1 2c-2 0-4 3-6 3l2-2c1 0 2-1 2-2-1-1-3-1-4-1l2-2z" class="X"></path><path d="M247 394v1 2h0c-1 0-2 2-3 2h0v1 2l-2 3-1-1c-1 2-1 3-1 5l-1-1v-2c0-1 1-2 1-4h0-1v-1l1-2c2 0 4-3 6-3l1-2h0z" class="N"></path><path d="M244 399v1 2l-2 3-1-1c0-2 2-3 3-5z" class="G"></path><path d="M246 396v1h0c-2 1-4 3-6 5h-1v-1l1-2c2 0 4-3 6-3z" class="O"></path><path d="M244 402l1-1 1 1 1-1h1c0 1 1 2 2 3l-1 1-2 2h0c-2 2-2 3-3 5 0 2 0 4 1 6l-1 1c0-2-1-4-2-5l-1 1v-2h-1v-4c0-2 0-3 1-5l1 1 2-3z" class="N"></path><path d="M246 404v-1c1-1 2 1 4 1l-1 1-2 2h0v-1c0-2-1-2-1-2z" class="h"></path><path d="M241 404l1 1c-1 2-1 6-1 8h-1v-4c0-2 0-3 1-5z" class="c"></path><path d="M246 404s1 0 1 2v1c-2 2-2 3-3 5v1h-1c-1-1-1-3-1-4 1-3 2-4 4-5z" class="J"></path><path d="M260 344l3-2h0c-1 3-3 7-3 10-1 3-1 7 1 9l1 2-1 1v1c0 2 0 4-2 6v1l-1 1-3 4c-1-1-2-2-3-2l1-2h-1v2h-1c-1 0-1 0-2-1h-2l1-1c2-1 3-2 4-3 0-1-1-1-1-2h-1l2-2v-1s-1-1-1-2h0v-1c0-1 0-3 1-4 1-6 4-10 8-14z" class="b"></path><path d="M255 369h0c1-1 1-3 2-4h0c0 3 0 4-1 6-1-1-1-1-1-2z" class="M"></path><path d="M257 371c0-1 2-2 2-3 1-2 0-3 0-5h1v1l1 1c0 2 0 4-2 6v1-1h-2z" class="N"></path><path d="M252 358v4c0 2 1 3 2 4l-2 4h0c0-1-1-1-1-2h-1l2-2v-1s-1-1-1-2h0v-1c0-1 0-3 1-4z" class="J"></path><path d="M252 362h1v-2-1c1 1 1 1 1 2v1l2-2c0 1-1 2-1 3s0 2-1 3c-1-1-2-2-2-4z" class="h"></path><path d="M252 370l1 1 2-2c0 1 0 1 1 2l-1 1c1 0 2 0 2-1h2v1l-1 1-3 4c-1-1-2-2-3-2l1-2h-1v2h-1c-1 0-1 0-2-1h-2l1-1c2-1 3-2 4-3h0z" class="O"></path><path d="M249 374c1-1 2-1 3-2v1 2h-1c-1 0-1 0-2-1z" class="G"></path><path d="M251 346c2-4 7-8 11-9-1 1-1 2-1 3h-1c0 1 0 1-1 2h1v2c-4 4-7 8-8 14-1 1-1 3-1 4v1l-1-2v-1c-1 1-1 1-3 2v1l1 1v2l-3 2v-1h-1c-1 0-3 0-4 1-2 0-4 2-5 1 1-1 2-2 3-4h-1c0-1 1-2 2-2 2-3 4-5 6-8 0-2 1-4 2-5s2-2 3-4c0 1 1 0 1 0z" class="L"></path><path d="M245 359l-2 4c-1 2-2 3-3 3h-1c0 1 0 1 1 2-2 0-4 2-5 1 1-1 2-2 3-4h-1c0-1 1-2 2-2h0v2h0v1c1-1 2-3 4-5l2-2z" class="G"></path><path d="M245 359l1-1h1c0 2-1 3-1 5h1 0l1 1v2l-3 2v-1h-1c-1 0-3 0-4 1-1-1-1-1-1-2h1c1 0 2-1 3-3l2-4z" class="l"></path><path d="M250 346c0 1 1 0 1 0v2h1c0 1-1 2-1 3-1 2-2 5-3 7v-4-1l-3 3v-1c0-2 1-4 2-5s2-2 3-4z" class="H"></path><path d="M259 342h1v2c-4 4-7 8-8 14-1 1-1 3-1 4v1l-1-2v-1-2c0-7 4-12 9-16z" class="E"></path><path d="M202 324c-3-1-5-4-6-6 0-2-1-5-1-7 0-1 1-2 2-4v6c0 1 1 2 2 3 2 2 5 4 8 3h0c-3-1-5-2-7-5l4 3c1-1 1-2 2-3l1 1c1 0 1 0 2 1 1 0 4 1 5 1s4-1 5-2h1c1-1 4-1 5-1v1h3 0 1c3-2 5-2 8-2v1c-3 1-7 2-8 4v1h1 0 2c-1 1-4 2-5 1v-1h0l-2 1h0l-2 1-3 1-10 3c-1 0-2 0-3-1v-1h1 0c-1 0-1 0-2-1h-2v1h-2v1z" class="K"></path><path d="M207 319c-3-1-5-2-7-5l4 3c1 1 6 2 7 2 2 0 5 0 6-1h3v1c-2 1-8 1-10 1l-3-1z" class="Q"></path><path d="M206 314l1 1c1 0 1 0 2 1 1 0 4 1 5 1s4-1 5-2h1c1-1 4-1 5-1v1c-2 1-3 1-5 2-1 0-2 1-3 1-1 1-4 1-6 1-1 0-6-1-7-2 1-1 1-2 2-3z" class="o"></path><path d="M229 315c3-2 5-2 8-2v1c-3 1-7 2-8 4v1h1 0 2c-1 1-4 2-5 1v-1h0l-2 1h0l-2 1-3 1-10 3c-1 0-2 0-3-1v-1h1 3l1-1c1-2 5-1 7-2 2-2 5-2 7-4h2l1-1h0z" class="V"></path><path d="M223 319h4l-2 1h0l-2 1v-2z" class="G"></path><path d="M220 322v-1c0-1 2-1 3-2v2l-3 1z" class="K"></path><defs><linearGradient id="O" x1="555.539" y1="544.395" x2="542.758" y2="542.839" xlink:href="#B"><stop offset="0" stop-color="#919191"></stop><stop offset="1" stop-color="#bdbcbd"></stop></linearGradient></defs><path fill="url(#O)" d="M532 514c3 1 6 3 8 5 10 8 14 19 15 31 1 3 0 8-1 11h-2 0c-1 1-2 1-2 1 0 1-1 2-1 3v-5c1-2 1-5 1-8l-1-5v-1l1 1h0v-2c0-1 0-3-1-5-1-1-1-2-2-3l-2-4-2-3h1v-1c-2-4-7-8-11-10h2 2c-2-1-3-3-5-5z"></path><path d="M550 545l1 8v-1c0 2 0 4-1 6v-6l-1-5v-1l1 1h0v-2z" class="J"></path><path d="M545 533v-1-2s1 1 2 1h0c1 1 3 4 3 6 0-1 0-1-1-1v-2l-1-1-1 1v2 1l-2-4z" class="C"></path><path d="M532 514c3 1 6 3 8 5v1c1 1 1 2 2 3l1 1 4 7h0c-1 0-2-1-2-1v2 1l-2-3h1v-1c-2-4-7-8-11-10h2 2c-2-1-3-3-5-5z" class="I"></path><path d="M378 317v-2-1c1 0 2 1 3 1v-2h1l3 3c0 1 1 3 2 3h0c1 1 1 1 1 2h2 0l-1-1v-1c-1-2-2-3-2-4-2-2-3-4-4-6h1c1 1 2 3 3 4 1 0 1 1 1 1 1 1 0 1 1 1 1 1 0 1 1 2v-1-5h1c0 1 2 3 2 4s-1 2 0 3l1 1c1-1 1-1 1-2 1 1 1 2 2 3v-1s1 1 1 2h0-1c1 2 1 3 0 4v1c1 1 1 1 0 2v2h0l-2-2h-1c-1-1-2-1-2-1l-3 1h-1 1v2 1c0 1 2 5 2 5l-1 1c0-2-1-4-2-5s-1-2-2-2c-1 1-1 2-1 3h0c-1-1-1-2-2-3h-1v1l-1-1v-2-3h0v-2c0-1-1-4-1-5h-1v2l-1-1v-2z" class="i"></path><path d="M385 321c2 1 3 1 3 2l1-1 1 1h1c1 0 0-1 0-2-1-1-1-2-1-2 1 1 1 2 2 3l2 4c-1 0-1 0-2-1h-2l-2-1c-1-1-2-1-3-3z" class="W"></path><path d="M397 320v-1s1 1 1 2h0-1c1 2 1 3 0 4v1c1 1 1 1 0 2v2h0l-2-2h1l-2-2-2-4 1-1v1c1 0 1 1 2 1 0-1 0-1 1-2h1 0v-1z" class="T"></path><path d="M392 322l1-1v1c1 0 1 1 2 1 1 2 1 3 1 4v1l-2-2-2-4z" class="g"></path><path d="M383 324h0v-1c-1-2-2-3-2-5 0 0 1 0 1-1h0 1c1 1 2 3 2 4 1 2 2 2 3 3l2 1h2c1 1 1 1 2 1l2 2h-1-1c-1-1-2-1-2-1h-3c-1 0-1-1-2-1h0v1h-1 0c0-1 0-2-1-3 0-1 0-1-1-1l-1 1z" class="a"></path><path d="M383 324l1-1c1 0 1 0 1 1 1 1 1 2 1 3h0 1v-1h0c1 0 1 1 2 1h3l-3 1h-1 1v2 1c0 1 2 5 2 5l-1 1c0-2-1-4-2-5s-1-2-2-2c-1 1-1 2-1 3h0c-1-1-1-2-2-3h-1v1l-1-1v-2-3h0v-2c1 1 2 3 2 4h1l-1-3z" class="X"></path><path d="M348 209h0c1 1 1 1 1 2h0c1 0 2 1 3 1 0 2 2 3 2 4h0c1 0 3 1 3 1 1 0 1 0 2-1v1l-1 1 1 1h-1v2h0c1 0 1 0 1 1v1l2-1v1c-1 1-1 1-1 2v1l1 1-1 1 2 1c-1 0-1 1-1 1-1 0-1 0-2 1l-2 2h-2-5-2 0l-4 1 1-1c1 0 1-1 1-1 0-1 0-1-1-1l-1 1v-1c1-2 0-4-1-5h0c-2-2-1-7-1-9l1 1c1-1 1-1 1-2s0-3-1-4c1 0 2 0 3-1v1h1l1-2v-1z" class="S"></path><path d="M349 221c1 1 1 1 1 2h0c0 1-1 1-1 2v1h0-1v-2l-1-1h0c1 0 2-1 2-2z" class="G"></path><path d="M349 217l2 2c0 1-1 1-2 2h0c0 1-1 2-2 2-1-2 0-2 1-4l1-2z" class="O"></path><path d="M349 226v-1c0-1 1-1 1-2 0 1 1 1 1 2h1c0-1 1-1 2-1h1c0 1-1 1-1 2h0c-1 1-3 2-4 2l-1-1c1 0 1 0 2-1h-2zm1 7c1 0 2-1 2-1h1c1 0 1-1 2-1v-1c-1 0-2-1-3-1v1l-1-1c1-1 1-1 2-1 2 0 3-1 3 1v2 1l-1 1h-5z" class="J"></path><path d="M353 228l2-1h3 2v1l2 1c-1 0-1 1-1 1-1 0-1 0-2 1l-2 2h-2l1-1v-1-2c0-2-1-1-3-1z" class="O"></path><path d="M358 227h2v1l2 1c-1 0-1 1-1 1-1 0-1 0-2 1 0 0 0-1-1-1 0-1-1-1-2-2l2-1z" class="N"></path><path d="M343 226h2 0 2l1 1v1l-1 1 1 1h0 4v1h0-1l-2 1h0s0 1-1 1l-4 1 1-1c1 0 1-1 1-1 0-1 0-1-1-1l-1 1v-1c1-2 0-4-1-5z" class="n"></path><path d="M347 229h-1v-2h1l1 1-1 1z" class="H"></path><path d="M355 222c0-1 2-2 2-3l1-1 1 1h-1v2h0c1 0 1 0 1 1v1l2-1v1c-1 1-1 1-1 2v1l1 1-1 1v-1h-2-3c2-1 3-2 4-3h-1l-4 2c0-1 1-1 1-2h-1v-2h1 0z" class="a"></path><path d="M355 222h3c0 1-2 2-3 2h0-1v-2h1 0z" class="M"></path><path d="M346 211v1l2 2-1 1h1c1 0 1 0 1 1v1l-1 2h-1c-1 1-2 2-2 3v4h-2 0c-2-2-1-7-1-9l1 1c1-1 1-1 1-2s0-3-1-4c1 0 2 0 3-1z" class="K"></path><path d="M347 219v-2-1h1 1v1l-1 2h-1z" class="P"></path><path d="M348 209h0c1 1 1 1 1 2h0c1 0 2 1 3 1 0 2 2 3 2 4h0c1 0 3 1 3 1 1 0 1 0 2-1v1l-1 1-1 1c0 1-2 2-2 3h0-1v2c-1 0-2 0-2 1h-1c0-1-1-1-1-2h0c0-1 0-1-1-2h0c1-1 2-1 2-2l-2-2v-1c0-1 0-1-1-1h-1l1-1-2-2h1l1-2v-1z" class="J"></path><path d="M352 218c2-1 2-1 4 0 0 1-1 1-1 2 0 0 1 0 0 1 0 0 0 1-1 1v2c-1 0-2 0-2 1h-1c0-1-1-1-1-2h0c1-1 2-1 3-2h1c-1-1-1-2-2-3z" class="l"></path><path d="M348 209h0c1 1 1 1 1 2h0c1 0 2 1 3 1 0 2 2 3 2 4h0c1 0 3 1 3 1 1 0 1 0 2-1v1l-1 1-1 1c0 1-2 2-2 3h0-1c1 0 1-1 1-1 1-1 0-1 0-1 0-1 1-1 1-2-2-1-2-1-4 0l-1-2c0-2-1-3-1-4h-1v1c-1 0-1-1-2-1l1-2v-1z" class="N"></path><path d="M392 327s1 0 2 1h1l2 2v1l1 1v1l1 1c0 2 1 3 1 5v6c-1 2-1 5-2 7v3c-1 1-2 3-2 5h-1 0l-1 1v1c0 2 0 4-1 6 0 2-2 4-3 6 0-1 1-2 1-4 0-1-1-3-1-5h0v1h-1l-1-4v-2-2h1c1 0 2-2 2-3v-2l-1 1c0-1-1-1-1-2h-1v-2l1-1v-2l1-3c1-2 1-4 0-6v-1l1-1s-2-4-2-5v-1-2h-1 1l3-1z" class="M"></path><path d="M393 342c1 3 1 6 1 8l-2 4-1 1v-2l1-3c1-3 1-5 1-8z" class="X"></path><path d="M392 327s1 0 2 1h1l2 2v1l1 1v1l1 1c0 2 1 3 1 5v6c-1-2-1-3-1-4-1-2-1-3-1-4-1-2-2-5-3-5v1h-1c0-1-2-3-3-4-1 0-1 0-2-1l3-1z" class="h"></path><path d="M390 337l1-1c1 2 1 4 2 6 0 3 0 5-1 8l-1 3-1 1c0-1-1-1-1-2h-1v-2l1-1v-2l1-3c1-2 1-4 0-6v-1z" class="a"></path><path d="M389 347h1v5l2-6h0v4l-1 3-1 1c0-1-1-1-1-2h-1v-2l1-1v-2z" class="f"></path><path d="M389 349v1c1 1 0 1 0 2h-1v-2l1-1z" class="i"></path><path d="M394 350c2-3 2-4 2-8 1 1 1 2 1 2v5c0 2 0 3-1 5v1l-1 1c0 2-1 3-2 5-1 1-1 1-1 2s1 1 1 1v1 3c0 2-2 4-3 6 0-1 1-2 1-4 0-1-1-3-1-5h0v1h-1l-1-4v-2-2h1c1 0 2-2 2-3l1-1 2-4z" class="o"></path><path d="M397 349c0 2 0 3-1 5v1l-1 1c0 2-1 3-2 5v-4c1-1 3-2 3-4v-1h0c0-1 1-2 1-3h0z" class="l"></path><path d="M391 355l1-1v2c-1 2-1 4-2 6v1 2 1h-1l-1-4v-2-2h1c1 0 2-2 2-3z" class="M"></path><path d="M388 360v-2h1c0 2 0 3 1 5v2 1h-1l-1-4v-2z" class="Y"></path><path d="M397 330h0v-2c1-1 1-1 0-2v-1c1-1 1-2 0-4h1c1 1 1 2 2 3s2 3 2 4l3 3c0 1 1 4 1 5 1 2 1 4 2 6 0 4-1 10 1 13l-1 3v1h-1v-3c-1 1-1 2-1 3s-1 3-2 4h0v-2h-1v4c-1 1-1 2-1 3-1 1-1 2-2 3h-1v1c-1-1-1 0-1-1v1c0-1-1-2-1-3v-3-1c-1-1-1-2-1-3l-1-2h1c0-2 1-4 2-5v-3c1-2 1-5 2-7v-6c0-2-1-3-1-5l-1-1v-1l-1-1v-1z" class="T"></path><path d="M399 357l3-3v1 2l-1 2c0-1-1-2-2-2z" class="N"></path><path d="M406 336c1 2 1 4 2 6 0 3-1 5-2 8v-14z" class="j"></path><path d="M402 357s0 1 1 1c0-2 1-5 2-7h1l-2 10h-1v4c-1 1-1 2-1 3-1-1-1 0-2 0v-1-2c-1-1 0-2 0-3l1-3 1-2z" class="b"></path><path d="M400 362c1 2 1 3 1 5v-1-1-1c1 0 1-1 1-2l1-1v4c-1 1-1 2-1 3-1-1-1 0-2 0v-1-2c-1-1 0-2 0-3z" class="X"></path><path d="M408 342c0 4-1 10 1 13l-1 3v1h-1v-3c-1 1-1 2-1 3s-1 3-2 4h0v-2l2-10v-1c1-3 2-5 2-8z" class="g"></path><defs><linearGradient id="P" x1="401.238" y1="370.134" x2="396.682" y2="361.303" xlink:href="#B"><stop offset="0" stop-color="#545355"></stop><stop offset="1" stop-color="#6c6c6c"></stop></linearGradient></defs><path fill="url(#P)" d="M399 357c1 0 2 1 2 2l-1 3c0 1-1 2 0 3v2 1c1 0 1-1 2 0-1 1-1 2-2 3h-1v1c-1-1-1 0-1-1v1c0-1-1-2-1-3v-3-1c-1-1-1-2-1-3l-1-2h1l2-2 1-1z"></path><path d="M398 358h1c0 2-1 3-3 4l-1-2h1l2-2z" class="o"></path><path d="M401 337l1-1c0 1 1 3 2 4 0 5 0 7-1 11-1 1-1 2-2 3h-1v-1l-2 2v-3c1-2 1-5 2-7v-6l1-2h0z" class="k"></path><path d="M401 337h0c1 5 1 12-1 16l-2 2v-3c1-2 1-5 2-7v-6l1-2z" class="b"></path><path d="M397 330h0v-2c1-1 1-1 0-2v-1c1-1 1-2 0-4h1c1 1 1 2 2 3s2 3 2 4l1 4c1 2 1 3 1 4v1 3c-1-1-2-3-2-4l-1 1h0l-1 2c0-2-1-3-1-5l-1-1v-1l-1-1v-1z" class="X"></path><path d="M400 331c0-2-1-2-1-4v-3h1c1 1 2 3 2 4l1 4c-1-1-2-1-3-1h0z" class="M"></path><path d="M400 331h0c1 0 2 0 3 1 1 2 1 3 1 4v1 3c-1-1-2-3-2-4l-1 1h0l-2-6h1z" class="Y"></path><path d="M400 331c1 0 2 0 3 1 1 2 1 3 1 4v1c-1-2-3-4-4-6z" class="N"></path><path d="M260 500h1v5c1 1 1 3 1 5 1 3 2 5 4 8l-2 1v-2l-1-1h0c-1 1-1 1 0 2v2l-1 4v-2h-1-1v1h-1-1c-1 1-1 2-3 2v-2l1-1h-2v1c-2 1-3 0-4 3v1h-1c1 1 1 0 2 0 0 1 1 1 1 2-1 0-2 0-2 1l-2 1h-1c1-1 1-2 1-3v-2-1c2-1 3-2 5-3l2-2h-2c-1 0-2 2-4 2v-1h-1l1-1c1 0 1 0 2-1l-1-1-5 2c-3 0-5 1-7 1h0c-3 1-6 2-8 1-2 0-3 0-4-1 0-3-1-3-2-5 2 0 5-1 8-1h3 1l1 1c1 0 3 0 4-1l4-2 2-1c2-1 3-2 4-3s2-1 3-2c2-1 3-2 4-3 2-2 2-2 2-4z" class="T"></path><path d="M235 515h1l1 1c1 0 3 0 4-1-1 1-2 2-4 2v1h0c-2-1-4 0-6-1l4-2z" class="b"></path><path d="M256 522c2-1 4-3 6-6-1 2-2 4-2 5v1 1h-1-1c-1 1-1 2-3 2v-2l1-1z" class="i"></path><path d="M245 520c-1 0-1 0-1-1h-1 2 0l1-1h1-1c0-1 0 0 1-1h0 1l1-1c1 0 3-2 4-2 2 0 3-2 5-2h0c-3 3-5 4-8 6h0l-5 2z" class="a"></path><path d="M258 504h1c-1 4-4 6-7 8-1 1-2 1-4 1 0 1 0 1-1 1h-1l-1-1 2-1c2-1 3-2 4-3s2-1 3-2c2-1 3-2 4-3z" class="j"></path><path d="M224 516c2 0 5-1 8-1h3l-4 2h-1l-1 1c1 0 1 1 2 2l-3 1h1 1 3c1 0 1 1 2 0h3 0c-3 1-6 2-8 1-2 0-3 0-4-1 0-3-1-3-2-5z" class="e"></path><path d="M373 163l6-4h0l-3 5-9 9-2 1c-3 3-7 6-11 8-5 4-11 7-15 12l-4 3v3c-1 3-1 6 0 9v2c-1 0-1 1-2 0v2c0 1 0 2 1 3h0-1c-1-1-1-2-2-3v-2h-1c2-3 0-6 0-9l-2-2c1 0 1-1 1-1 0-2 0-4-1-6v-1h0c-1-1-1-2-1-3 1 0 1 0 1-1 0 0 1 0 1-1 1 0 1 0 2 1h0 2l1 1h1l1-1c1-1 1-1 3-1 1-1 1-2 2-3h0v-2s0-1 1-2v-2l4-1 8-3 4-2c2-2 5-3 8-4 2-1 5-3 7-5z" class="H"></path><path d="M335 194h0l1-2h1l1 1 1 1-4 3h0c0-1 1-2 0-3z" class="Q"></path><path d="M336 188c1-1 1-1 3-1h0c-2 2-5 4-6 6l-1-1c1 0 1-1 1-2-1-1-2 0-3-1l-1-2c1 0 1 0 2 1h0 2l1 1h1l1-1z" class="K"></path><path d="M373 163c0 1 0 2-1 3l-5 4c0-1 2-1 1-2l-6 3c-1 0-2 1-4 1 2-2 5-3 8-4 2-1 5-3 7-5z" class="P"></path><path d="M327 189c1 0 1 0 1-1 0 0 1 0 1-1l1 2c1 1 2 0 3 1 0 1 0 2-1 2 0 1-1 2-2 2v-1-1l-2 1v-1h0c-1-1-1-2-1-3z" class="S"></path><path d="M330 193c0-1 0-1 1-2 0-1 1-1 2-1 0 1 0 2-1 2 0 1-1 2-2 2v-1z" class="Q"></path><path d="M346 177l8-3-1 2c-1 0-3 2-5 2-1 0-2 0-2 1s1 2 2 2h0v1l-1 1c-1-1-2-1-4-1 0-1 1-2 2-3l1-2z" class="n"></path><path d="M342 178l4-1-1 2c-1 1-2 2-2 3 2 0 3 0 4 1-2 0-7 4-8 4h0c1-1 1-2 2-3h0v-2s0-1 1-2v-2z" class="V"></path><path d="M353 176c2-1 6-3 8-3h0c0 2-6 5-8 6-1 0-2 0-3 1h-1-2v-1l1-1c2 0 4-2 5-2z" class="F"></path><path d="M330 194c1 0 2-1 2-2l1 1v1c-1 3-3 4-2 8 1-2 1-3 2-5 1-1 1-2 2-3 1 1 0 2 0 3h0v3c-1 3-1 6 0 9v2c-1 0-1 1-2 0v2c0 1 0 2 1 3h0-1c-1-1-1-2-2-3v-2h-1c2-3 0-6 0-9l-2-2c1 0 1-1 1-1 0-2 0-4-1-6l2-1v1 1z" class="S"></path><path d="M328 193l2-1v1 1 3 2c-1 1 0 2 0 3l-2-2c1 0 1-1 1-1 0-2 0-4-1-6z" class="K"></path><path d="M335 194c1 1 0 2 0 3h0v3c-1 3-1 6 0 9v2c-1 0-1 1-2 0-1-2-1-3-1-5-1-3 0-6 1-9 1-1 1-2 2-3z" class="Z"></path><path d="M343 207h1 0c0 1 1 1 1 1h0l1 2h2l-1 2h-1v-1c-1 1-2 1-3 1 1 1 1 3 1 4s0 1-1 2l-1-1c0 2-1 7 1 9h0c1 1 2 3 1 5v1l1-1c1 0 1 0 1 1 0 0 0 1-1 1l-1 1-1 2c-3 1-5 4-6 7h0v3h-1c0 1 0 1-1 1v2c1 1 1 1 1 2-1-1-3-1-3-2l-2-1h0-1v-1h-2v-10l-2-5v-2l3 1c0 1 0 2 1 2l1-1v-2-3-4-3c1-1 1-1 1-2v-2c-1-1-1-2-1-3 1 1 1 2 2 3h1 0c-1-1-1-2-1-3v-2c1 1 1 0 2 0v-2h1l1-1c2 1 1 3 3 2v-1-1l1-1 1 1 1-1z" class="G"></path><path d="M336 213v1 1h-1l-1-1c0-1 1-1 2-2v1z" class="V"></path><path d="M334 218c1 2 1 6 1 9h-1-1c0-1 1-2 1-2v-2c-1-1-1-2-1-3s0-2 1-2z" class="Z"></path><path d="M335 209h1l1-1c2 1 1 3 3 2 0 1 0 2-1 2l-3 1v-1l-1-1v-2z" class="l"></path><path d="M342 217c0-1 0-2-1-2h0v1l-2-2s1-1 1-2l1 1v1l2-2c1 1 1 3 1 4s0 1-1 2l-1-1z" class="P"></path><path d="M331 213c1 1 1 2 2 3v1h0c1 0 1 0 1 1-1 0-1 1-1 2s0 2 1 3c-1 1-2 1-2 2l-1 5h0v-3-4-3c1-1 1-1 1-2v-2c-1-1-1-2-1-3z" class="J"></path><path d="M343 207h1 0c0 1 1 1 1 1h0l1 2h2l-1 2h-1v-1l-2-1c-1 1-1 1-2 1 0-1-1-1-2-2h0v-1l1-1 1 1 1-1z" class="Y"></path><path d="M336 218c0-1 0-2 1-3 0 0 2 0 2 1 1 0 0 1 1 1l1-1 1 1c0 2-1 7 1 9h0c1 1 2 3 1 5v1l-1 1c-1 0-1 1-2 1 0 0 0-1-1-1v-1-4c-1-2-1-3-2-5l-1-1v-1c0-1 0-2-1-3z" class="T"></path><path d="M336 218c0-1 0-2 1-3 0 0 2 0 2 1 1 0 0 1 1 1l1-1 1 1c0 2-1 7 1 9h0c1 1 2 3 1 5v1l-1 1c0-2-1-4-1-6-1-3 0-6-2-8h-1-1v-1h-2z" class="J"></path><path d="M331 232h1v2h0l3-4h0l1-1v-1h1l1-2h0l2 2v4 1c1 0 1 1 1 1 1 0 1-1 2-1l1-1 1-1c1 0 1 0 1 1 0 0 0 1-1 1l-1 1-1 2c-3 1-5 4-6 7h0v3h-1c0 1 0 1-1 1v2c1 1 1 1 1 2-1-1-3-1-3-2l-2-1h0-1v-1h-2v-10l-2-5v-2l3 1c0 1 0 2 1 2l1-1z" class="V"></path><path d="M329 237v-2l1-1 1 1c0 1 0 2-1 3l-1-1z" class="L"></path><path d="M329 237l1 1v1h1l1-2 1 1v2 2l-1 1 2 2c-1 1-1 2-1 3v1l-2-1h0-1v-1h-2v-10h0 1z" class="K"></path><path d="M331 241c1-1 1-1 2-1v2l-1 1c0 2 0 3-1 3s-1 0-1-1c1-2 0-3 1-4z" class="J"></path><path d="M331 241c1-1 1-1 2-1v2h-2v-1z" class="Z"></path><path d="M328 237h0c1 2 1 4 2 6-1 1-1 1 0 2 0 1 0 1 1 1s1-1 1-3l2 2c-1 1-1 2-1 3v1l-2-1h0-1v-1h-2v-10z" class="L"></path><path d="M334 245v-2c1-1 1-3 2-4 0-2-1-4 0-5v-1c0-1 0-1 1-2 0-1 1-2 2-3 1 1 1 3 1 4h0v1c1 0 1 1 1 1 1 0 1-1 2-1l1-1 1-1c1 0 1 0 1 1 0 0 0 1-1 1l-1 1-1 2c-3 1-5 4-6 7h0v3h-1c0 1 0 1-1 1v2c1 1 1 1 1 2-1-1-3-1-3-2v-1c0-1 0-2 1-3z" class="C"></path><path d="M338 230h1c0 1 0 3 1 4v1c0 1-1 2-2 3h0c-2-1 0-5 0-7h0v-1z" class="F"></path><path d="M514 558l4-4c7-4 11 0 17 1h1 1v2l-2 2h0l-2 2c-1 0-1 0-1 1l-1 1-1 1-1 1v1h0-1-2 0c2 1 3 3 6 3 1 1 1 2 2 3-1 1-3 0-5 0 2 1 4 1 5 1 1-1 1 0 2 0h0c1 0 2 0 3-1h3c1-1 3-2 4-3v3c-1 1-1 2-2 3l-1 1c-1 1-1 2-1 2-1 1-3 2-5 2v-2c-3 1-6 2-9 1-4-1-8-3-10-6v-1l-1-1c-2-2-2-6-2-9 0 0 1-1 0-1v-1c0-1 0-1-1-2z" class="R"></path><path d="M520 571c-1-1-2-2-2-3-1-2-1-5 0-6 1-3 3-4 6-5 0 1 0 1 1 2-2 0-4 1-6 3 0 2-1 4 0 6 1 1 1 1 1 2v1z" class="L"></path><path d="M536 573c1 0 2 0 3-1h3c1-1 3-2 4-3v3c-1 1-1 2-2 3l-1 1c-1 1-1 2-1 2-1 1-3 2-5 2v-2c1 0 3 0 4-1s1-1 1-2h-1-1c-2 1-5 2-7 2-1-1-2-1-2-1h-2c-3 0-7-2-9-5v-1l2 1c1 0 1-1 1-1 1 0 1 0 1 1 1 1 4 1 5 1 2 1 4 1 5 1 1-1 1 0 2 0h0z" class="D"></path><path d="M520 570c0-1 0-1-1-2-1-2 0-4 0-6 2-2 4-3 6-3h2l1 1c0 1-1 3-2 3l-1 1c0 1 0 1 1 2 2 1 3 3 6 3 1 1 1 2 2 3-1 1-3 0-5 0-1 0-4 0-5-1 0-1 0-1-1-1 0 0 0 1-1 1l-2-1z" class="R"></path><path d="M514 558l4-4c7-4 11 0 17 1h1 1v2l-2 2h0l-2 2c-1 0-1 0-1 1l-1 1-1 1-1 1v1h0-1-2 0c-1-1-1-1-1-2l1-1c1 0 2-2 2-3l-1-1h-2c-1-1-1-1-1-2h2l1-1c-3-1-4-1-6 0h-1c-2 1-3 3-5 4 0-1 0-1-1-2z" class="F"></path><path d="M521 556l-1-1c2-1 6-1 8 0 1 0 1 1 2 1l-1 1c-1 0-2 0-2-1-3-1-4-1-6 0z" class="S"></path><path d="M530 556c1 1 2 2 2 3-2 1-4 4-6 4 1 0 2-2 2-3l-1-1h-2c-1-1-1-1-1-2h2l1-1c0 1 1 1 2 1l1-1z" class="P"></path><path d="M524 557h2l2 2h-1-2c-1-1-1-1-1-2z" class="S"></path><path d="M532 559c1 0 1-1 2-1 0 1 1 1 1 1h0l-2 2c-1 0-1 0-1 1l-1 1-1 1-1 1v1h0-1-2 0c-1-1-1-1-1-2l1-1c2 0 4-3 6-4z" class="B"></path><path d="M233 233c1-1 3-1 4-1l3 1h0v1h1c1 0 2 1 3 2s2 1 3 2h1c2 0 3 0 4-1h2 0v1c-1 0-2 1-3 2-1 0-1 1-2 1l1 1h1v1c-2 1-2 0-3 1 0 2-1 2 0 3l-1 2s-1 0-2 1c-1-1-2-2-2-3h-3-4l-2 1h-1c-2 1-3 2-4 3-2 1-3 2-4 3-1 2-2 3-3 4h0l-5 6-2 2h-1v-2l1-1c0-1 0-1 1-2v-2h0c0-2 1-4 2-5h-1l-1 2-1-1c1-1 3-4 3-5 2-6 4-9 9-13h0l-1-1 1-1v-1c2 0 3-1 4-1h2z" class="N"></path><path d="M229 244l3-2v1l-1 2h0l-3 1 1-2z" class="P"></path><path d="M227 235v-1c2 0 3-1 4-1l1 1 1 1h0c0 1-1 1-1 1v-1h0-5zm-1 5l3-1v1c2 1 3 1 4 1h1l-2 2v-1h0-2-3l-1-2z" class="S"></path><path d="M224 243c0-1 1-2 2-3l1 2h3-1c-1 1-2 2-4 3-1 0-2 0-3 1s-1 1-2 1v-1c2-1 3-2 4-3z" class="J"></path><path d="M224 243c0-1 1-2 2-3l1 2h3-1c-1 0-4 1-5 1zm-6 11h0c1-2 3-3 5-5 2-3 3-4 6-5l-1 2c-1 1-3 2-3 4l1 1c-4 2-7 4-10 8 0-2 1-4 2-5z" class="K"></path><path d="M228 246l3-1v1h3v1 1h-1c-2 1-3 2-4 3-2 1-3 2-4 3-1 2-2 3-3 4h0l-5 6-2 2h-1v-2l1-1c0-1 0-1 1-2v-2h0c3-4 6-6 10-8l-1-1c0-2 2-3 3-4z" class="W"></path><path d="M216 259c1 1 1 2 1 3l2-2 2-2h1l-5 6-2 2h-1v-2l1-1c0-1 0-1 1-2v-2z" class="j"></path><path d="M228 246l3-1v1h3v1l-8 4-1-1c0-2 2-3 3-4z" class="D"></path><path d="M233 233c1-1 3-1 4-1l3 1h0v1h1c1 0 2 1 3 2s2 1 3 2h1c2 0 3 0 4-1h2 0v1c-1 0-2 1-3 2-1 0-1 1-2 1l1 1h1v1c-2 1-2 0-3 1 0 2-1 2 0 3l-1 2s-1 0-2 1c-1-1-2-2-2-3h-3-4l-2 1v-1-1h-3v-1h0l1-2 2-2h-1c-1 0-2 0-4-1v-1l2-1s0-1-1-1c1-1 1-1 2-1 0 0 1 0 1-1h0l-1-1-1-1h2z" class="F"></path><path d="M235 236h2 2l-1 1c0 1-1 1-2 1l-1-2z" class="I"></path><path d="M236 241l-1-1 1-1c0 1 0 2 1 3h1c0 1-1 1-2 1l-2 1 2-3z" class="H"></path><path d="M233 241h3 0l-2 3-3 1 1-2 2-2h-1z" class="L"></path><path d="M238 234h0c1 1 2 1 3 2 1 0 1 1 2 2 0 1 1 2 1 3l-2-1v-1l-3-3h-2-2l-1 1v-2c1-1 2-1 4-1z" class="H"></path><path d="M231 238l3-1v1h3l-1 1-1 1 1 1h0-3c-1 0-2 0-4-1v-1l2-1z" class="D"></path><path d="M233 233c1-1 3-1 4-1l3 1-2 1h0c-2 0-3 0-4 1v2h0l-3 1s0-1-1-1c1-1 1-1 2-1 0 0 1 0 1-1h0l-1-1-1-1h2z" class="P"></path><path d="M233 233c1-1 3-1 4-1l3 1-2 1h0c-1 0-2-1-2-1h-3z" class="V"></path><path d="M240 233h0v1h1c1 0 2 1 3 2s2 1 3 2h1c2 0 3 0 4-1h2 0v1c-1 0-2 1-3 2-1 0-1 1-2 1h-1c-1 0-1 1-1 1h-3 0v-1c0-1-1-2-1-3-1-1-1-2-2-2-1-1-2-1-3-2l2-1z" class="Q"></path><g class="D"><path d="M241 236h2l2 2c0 1 1 1 1 3h-2c0-1-1-2-1-3-1-1-1-2-2-2z"></path><path d="M240 245h3v-1-1c-1 0-2 0-3-1v-1c0-1 1-1 2-1l2 1v1h0 3s0-1 1-1h1l1 1h1v1c-2 1-2 0-3 1 0 2-1 2 0 3l-1 2s-1 0-2 1c-1-1-2-2-2-3h-3-4l-2 1v-1-1c2 0 4-1 6-1z"></path></g><path d="M240 245c2 0 4 0 5 1h0c-1 1-1 1-2 1h-3-4l-2 1v-1-1c2 0 4-1 6-1z" class="G"></path><path d="M515 469c2 0 5-1 6 0s2 1 2 2c0 2-1 4-1 6 2 0 4 0 6 1l1 1c0 1-1 2-1 2 2 2 4 2 4 5 1 1 1 2 0 3h0c-1-1-1-2-3-3-2 0-4 1-6 2-3 1-7 4-10 6h4 2 0 0c-3 2-6 4-7 7l-2-1h-2v1c1 0 1 1 1 1v1 1c-2-2-4-4-6-5h-2l-1-1v-1h-1l-1 1h0l4-9c0-1 1-1 1-1 1-1 2-2 2-3l3-3c2-2 4-4 5-6 0-2 1-4 1-6l1-1z" class="R"></path><path d="M513 476c0-2 1-4 1-6 1 4 0 10-3 14h-1 0l-1 1-1-1v-2c2-2 4-4 5-6z" class="I"></path><path d="M503 488c1-1 2-2 2-3l3-3v2l1 1 1-1h0c-2 3-6 7-8 10 2 0 13-7 14-6-5 2-8 5-12 8l6-1c1-1 2-1 3-1h4 2 0 0c-3 2-6 4-7 7l-2-1h-2v1c1 0 1 1 1 1v1 1c-2-2-4-4-6-5h-2l-1-1v-1h-1l-1 1h0l4-9c0-1 1-1 1-1z" class="C"></path><path d="M517 494h2 0 0c-3 2-6 4-7 7l-2-1h-2v1c1 0 1 1 1 1-2-1-4-2-5-4 2-1 5-2 7-3s4 0 6-1h0z" class="R"></path><path d="M350 248h0c0 1-2 2-2 2 0 2 0 1-1 2l1 1h2 1 2 1 2c-1 0 0 0 0-1s1-1 1-2h0c1 1 1 1 2 1l1-2v-1l1 2-1 1h2s-1 1-2 1c0 1-1 2-2 3v1h1c5-2 10-6 16-7l2-1v1h-1c-2 0-5 2-7 3-5 3-12 6-17 10l15-1c8 0 17 1 25 4 5 1 9 3 13 5v1c-14-7-27-9-42-9-3 1-7 0-10 2h-2l1 1h0c4-1 7-1 11-1h11c2 1 4 1 6 2 2 0 6 1 8 2h0c-1 0-1 0-2-1-8-2-17-2-25-2-2 0-7 0-9 1h0c0 1 0 0 0 0 2 1 4 1 6 2 13 2 26 9 35 18l3 4c-3-2-5-5-8-7-10-8-24-15-37-15 2 1 4 2 6 2 3 1 6 3 9 4l-1 1c-3-1-6-3-8-4-2 0-3-1-4-1-2 0-2 1-3 2l-1-1-1 1h0l-3 3c-3 0-4 2-6 4-2-1-2-1-4-1-1-1-1-1-1-3h1l2-2-1-3c1-1 3-2 3-3 1 0 1-1 1-1l1-1c0-1-1-4-2-5l-1-4v-3l1 1c1-1 1-2 2-3h1c3 0 6-2 8-3z" class="V"></path><path d="M338 253l1 1c1-1 1-2 2-3h1l-1 1-1 1c0 2 0 4 1 5l-2 2-1-4v-3z" class="W"></path><path d="M360 249v-1l1 2-1 1h2s-1 1-2 1c0 1-1 2-2 3v1h1l-6 4v-1-1c-1 0-2-1-3-1v-1h0c-1-1-2-1-2-2v-1h0 2 1 2 1 2c-1 0 0 0 0-1s1-1 1-2h0c1 1 1 1 2 1l1-2z" class="b"></path><path d="M360 249v-1l1 2-1 1h2s-1 1-2 1l-1-1-2 3-1-1c-1 0 0 0 0-1s1-1 1-2h0c1 1 1 1 2 1l1-2z" class="Y"></path><path d="M348 253h2v1h1c2 0 2 0 4 2h0v2h-1l-1-1v1c-1 0-2-1-3-1v-1h0c-1-1-2-1-2-2v-1h0z" class="X"></path><path d="M341 261h1c0-3-1-5 0-7l1-1 1 1s0 1 1 1c2 1 2 2 3 5 1 1 2 2 3 4l-1 1v2l1 1h-1-1v3l-1 1h0l-3 3c-3 0-4 2-6 4-2-1-2-1-4-1-1-1-1-1-1-3h1l2-2-1-3c1-1 3-2 3-3 1 0 1-1 1-1l1-1c0-1-1-4-2-5l2-2v3h0z" class="E"></path><path d="M345 269h-1v-1l1-1c-1-1-1-1-1-2l2-2 1 1v1c0 2 0 2-1 3v1h0-1z" class="R"></path><path d="M341 258v3h0c1 1 2 4 1 6 0 1-1 2-2 3l-3 3-1-3c1-1 3-2 3-3 1 0 1-1 1-1l1-1c0-1-1-4-2-5l2-2z" class="a"></path><path d="M346 269c1 0 2-1 3-2 0-1 1-1 1-2h0v2l1 1h-1-1v3l-1 1h0l-3 3c-3 0-4 2-6 4-2-1-2-1-4-1-1-1-1-1-1-3h1v1h2c3 0 6-2 8-5h1v-1h0-1v-1h1z" class="H"></path><defs><linearGradient id="Q" x1="489.539" y1="483.133" x2="503.931" y2="498.878" xlink:href="#B"><stop offset="0" stop-color="#757274"></stop><stop offset="1" stop-color="#8b8c8b"></stop></linearGradient></defs><path fill="url(#Q)" d="M511 461c1 1 2 3 2 4 1 1 1 3 2 4l-1 1c0 2-1 4-1 6-1 2-3 4-5 6l-3 3c0 1-1 2-2 3 0 0-1 0-1 1l-4 9c-2 1-3 3-4 4l-1 1-1 2c-4 4-5 8-8 13 0 1-1 2-2 3s-2 2-3 4l-4 5c0-2 3-5 4-7-1-1-1-1-1-2 1-3 2-5 3-8l2-2 3-7c0-1 1-2 1-3l2 2 2-6-1 1v1-5l1-3v-2c1-2 1-4 2-6 1-4 3-7 5-10 1-3 2-5 3-7l2-1s1 0 1 1c1 1 0 5 0 7 0-1 1-4 1-5 1 0 2 0 2-1 3-2 3-2 4-6z"></path><path d="M504 482h1c-1 2-2 4-2 6 0 0-1 0-1 1-2 1-3 2-4 4 0 1-2 3-3 4 1-4 3-6 4-8l5-7z" class="V"></path><path d="M486 504c0-1 1-2 1-3l2 2-4 8-1 1-1-1 3-7z" class="C"></path><path d="M495 497c1-1 3-3 3-4 1-2 2-3 4-4l-4 9c-2 1-3 3-4 4l-1 1-1 2 3-8z" class="P"></path><path d="M481 513l2-2 1 1 1-1c-1 4-4 8-6 12-1-1-1-1-1-2 1-3 2-5 3-8z" class="L"></path><path d="M513 465c1 1 1 3 2 4l-1 1c0 2-1 4-1 6-1 2-3 4-5 6l-3 3c0 1-1 2-2 3 0-2 1-4 2-6h-1l1-1c0-2 1-3 3-5s4-6 4-9c1 0 1-1 1-2h0z" class="G"></path><path d="M505 482c0-1 1-2 2-3 1 0 1-1 2-2 1 0 3-1 4-1-1 2-3 4-5 6l-3 3c0 1-1 2-2 3 0-2 1-4 2-6z" class="P"></path><path d="M501 466l2-1s1 0 1 1c1 1 0 5 0 7-4 8-9 16-13 24h0l-1 1v1-5l1-3v-2c1-2 1-4 2-6 1-4 3-7 5-10 1-3 2-5 3-7z" class="R"></path><path d="M347 280c0-1-1-2-2-3v-1c2 1 2 2 3 3v-1l2 2h0 3l6 6h1c6 7 10 17 11 27-1 3-2 5-5 7h-1l-2 1-1-1-1-1-2-1c-2 0-3-1-5-2 0-1 0-2-1-3s-1-3-2-4v-3h1c0-1-1-2-1-3h1 1v-3c-1-1-1-3-1-3 0-3-1-5-2-7-1-3-2-7-4-10h1z" class="r"></path><path d="M360 286c6 7 10 17 11 27-1 3-2 5-5 7h-1l-2 1-1-1-1-1h2v-1c1 0 2 0 3-1 2-1 3-3 3-5 0-3 0-6-1-9-2-6-5-11-9-17h1z" class="G"></path><path d="M363 319h2 1c-1 0-1 1-1 1l-2 1-1-1-1-1h2z" class="P"></path><path d="M360 294h0c1 1 1 2 2 2 1 3 2 5 3 7 1 3 2 6 2 9-1 0-1 0-1 1v2l-1 1-1-1v-1c-1-1-1-1-2-1l-1-1h-1l-1-2h0c-1-2-1-2-1-3 1-1 2-2 2-3s0-1-1-2l1-2c0-1 1-2 0-3v-3z" class="e"></path><path d="M359 310v-2h1l1 4h-1l-1-2z" class="k"></path><path d="M362 307c1 0 1 0 1 1 1 1 1 1 1 2-1 0-2-1-2-2v-1z" class="i"></path><path d="M360 304c0-1 1-3 2-4h0v2 1c-1 1-1 3-2 4h-2c1-1 2-2 2-3z" class="r"></path><path d="M347 280c0-1-1-2-2-3v-1c2 1 2 2 3 3v-1l2 2h0l3 6c1 0 2 1 2 2 0 0 1 1 0 1 2 1 4 3 5 5v3c1 1 0 2 0 3l-1 2c1 1 1 1 1 2s-1 2-2 3c0 1 0 1 1 3h0l1 2h-2 0v1c1 2 2 4 4 5h1v1h-2l-2-1c-2 0-3-1-5-2 0-1 0-2-1-3s-1-3-2-4v-3h1c0-1-1-2-1-3h1 1v-3c-1-1-1-3-1-3 0-3-1-5-2-7-1-3-2-7-4-10h1z" class="O"></path><path d="M353 300c1 3-1 6 1 10v2h-1v1c-1-1-1-3-2-4v-3h1c0-1-1-2-1-3h1 1v-3z" class="d"></path><path d="M359 302c1 1 1 1 1 2s-1 2-2 3c0 1 0 1 1 3h0l1 2h-2 0v1c1 2 2 4 4 5h1v1h-2l-2-1c-2 0-3-1-5-2 0-1 0-2-1-3v-1h1c1 1 2 3 4 4v-1l-1-2v-1-1c-1-3 0-6 2-9h0z" class="Y"></path><path d="M347 280c0-1-1-2-2-3v-1c2 1 2 2 3 3 3 7 8 15 8 22 0 2 1 2 0 3h-1c-1-2-1-4-1-6-2-6-4-12-7-18z" class="e"></path><path d="M353 286c1 0 2 1 2 2 0 0 1 1 0 1 2 1 4 3 5 5v3c1 1 0 2 0 3l-1 2h0-1-1v-1c-1-5-3-11-4-15z" class="p"></path><path d="M357 295c1 0 1 0 2 1l1 3v1l-1 2h0-1c1-2 0-5-1-7z" class="N"></path><path d="M355 289c2 1 4 3 5 5v3c1 1 0 2 0 3v-1l-1-3c-1-1-1-1-2-1v-1c0-1-2-4-2-5zM219 520c1-1 2-2 2-3l1 1v1c1 3 2 4 5 5h7 2c4-1 8-1 12-3h1v1c2 0 3-2 4-2h2l-2 2c-2 1-3 2-5 3v1 2c0 1 0 2-1 3h1l2-1c0-1 1-1 2-1 0-1-1-1-1-2-1 0-1 1-2 0h1v-1c1-3 2-2 4-3v-1h2l-1 1v2c2 0 2-1 3-2h1 1l-2 5v5l-1-1v1c-2 3-5 4-7 7-1 0-1 0-1 1-1-1-1-2-1-3l-1-2v-2h-1v3l-3 2-2 2c-1-1-2-1-4-1h-3c-2 0-3-1-5-1l-1-1c-1 0-2-1-2-2h-3l-1-1v-1l-3-3c0-1-1-2-1-3v-1-1c-1-1 0-5 1-6z" class="W"></path><path d="M248 524c0 1-1 2-1 2h-3c1-1 2-2 3-2h1 0z" class="Y"></path><path d="M253 526h1c1 1 2 1 2 1l-1 1-1 1s-1-1-1-2v-1z" class="b"></path><path d="M219 520c1-1 2-2 2-3l1 1v1l-1-1v1l1 1h-1v2 1c-1-1-1-2-1-3h-1z" class="e"></path><path d="M227 524h7c-1 1-2 1-3 2-2 0-3-1-4-2z" class="Y"></path><path d="M250 530h3v1h0s1-1 2-1v1c-1 1-3 3-4 3h-1 0l2-2h-1l-2 1-1-1v-1l2-1z" class="a"></path><path d="M258 528v5l-1-1v1c-2 3-5 4-7 7-1 0-1 0-1 1-1-1-1-2-1-3 2-1 3-2 4-3 2-1 3-3 5-4h0c1-1 1-2 1-3z" class="M"></path><path d="M248 521h1v1c2 0 3-2 4-2h2l-2 2c-2 1-3 2-5 3v-1h0c0-1 1-1 1-1l-1-1c-1 2-3 2-5 2-2 1-6 2-7 1v-1c4-1 8-1 12-3z" class="b"></path><path d="M219 520h1c0 1 0 2 1 3v-1l1 2c3 3 6 5 10 5 1 0 1 1 2 1h7l1 1 1-1 1 1v-1c0-1 0-1 1-1h0 2c-1 2-1 3-1 5h0v3l-3 2-2 2c-1-1-2-1-4-1h-3c-2 0-3-1-5-1l-1-1c-1 0-2-1-2-2h-3l-1-1v-1l-3-3c0-1-1-2-1-3v-1-1c-1-1 0-5 1-6z" class="i"></path><path d="M226 536l7 2c-2 0-3 0-4 1l-1-1c-1 0-2-1-2-2z" class="g"></path><path d="M483 434c0-1 0-2 1-3 0-1 2-2 4-3s5 0 7 1 3 2 4 4v1c-3 2-5 4-6 7-1 2 1 5 2 7 2 3 3 8 3 12 0 1-1 2 0 3 0 0 2 1 2 2l1 1h0c-1 2-2 4-3 7-2 3-4 6-5 10-1 2-1 4-2 6v2h-1c-1 1-1 1-1 2-1 0-1-1-1-2-1-3 0-7 0-10 1-12 0-22-4-33l-2-4c1-2 0-3 0-5v-5h1z" class="R"></path><path d="M490 433c1-1 3-2 5-2 1 0 2 1 3 2-2-1-5 0-7 1 0-1-1-1-1-1z" class="J"></path><path d="M482 434h1c1 5 1 10 1 14l-2-4c1-2 0-3 0-5v-5z" class="B"></path><path d="M490 433s1 0 1 1c2-1 5-2 7-1h0v1h-2c-2 1-3 1-5 3h0c-1-1-1-2-2-2l-2 1 3-3z" class="O"></path><path d="M487 436l2-1c1 0 1 1 2 2-1 1-1 2-1 3v3c-1 0-1 1-1 2h-1s-1-1-2-1v-5l1-3z" class="N"></path><path d="M486 439l2 2h0v3l1 1h-1s-1-1-2-1v-5z" class="O"></path><path d="M499 433v1c-3 2-5 4-6 7-1 2 1 5 2 7l-1 1c-2 1-3-1-5-1-1-1-1-1-2-1l-1-3c1 0 2 1 2 1h1c0-1 0-2 1-2v-3c0-1 0-2 1-3h0c2-2 3-2 5-3h2v-1h1z" class="G"></path><path d="M491 437h0c0 2 1 6 1 7-1 1-1 1-1 2h-1c-1-1-1-1-2-1h1c0-1 0-2 1-2v-3c0-1 0-2 1-3z" class="Z"></path><path d="M487 447c1 0 1 0 2 1l1 2c1 2 2 5 3 7 0 4 1 8 1 11 0 2 0 3-1 4h0v8c2-3 4-7 5-10 1-2 2-3 2-5l1 1h0c-1 2-2 4-3 7-2 3-4 6-5 10-1 2-1 4-2 6v2h-1c-1 1-1 1-1 2v-2-2c1-1 2-4 2-6 1-10 0-19-2-28-1-2-2-5-2-8h0z" class="F"></path><path d="M493 472c-1-4-1-8-2-11-1-4-2-8-2-12l1 1c1 2 2 5 3 7 0 4 1 8 1 11 0 2 0 3-1 4h0z" class="U"></path><path d="M489 448c2 0 3 2 5 1l1-1c2 3 3 8 3 12 0 1-1 2 0 3 0 0 2 1 2 2 0 2-1 3-2 5-1 3-3 7-5 10v-8h0c1-1 1-2 1-4 0-3-1-7-1-11-1-2-2-5-3-7l-1-2z" class="I"></path><path d="M489 448c2 0 3 2 5 1 0 1 1 1 0 2s0 4-1 6h0c-1-2-2-5-3-7l-1-2z" class="H"></path><path d="M385 333c0-1 0-2 1-3 1 0 1 1 2 2s2 3 2 5v1c1 2 1 4 0 6l-1 3v2l-1 1v2h1c0 1 1 1 1 2l1-1v2c0 1-1 3-2 3h-1v2 2l1 4v2 4c-1 4-3 7-6 10l-1 2-14 10h-1c-1 1-6 3-7 3 1-2 4-3 5-3v-1-2s1-1 1-2h1l1-2c0-2 0-3-1-4v-3h0v-1c0-1-1-3-1-4h-1c0 1 0 1-1 1-1-3-1-5-1-7 1-3 2-5 3-7 0-1 2-3 3-4 0-2 1-4 2-6l2-1c0-1 0-2 1-2 2-1 3-2 4-3h0c2-3 4-4 4-8 1-1-1-3 0-5v-2-1h1c1 1 1 2 2 3h0z" class="k"></path><path d="M375 374c0 1 1 2 1 3h0c0 3-1 5-2 7 0-2 1-4 2-6h-2 0v-1c0-1 1-2 1-3h0z" class="Y"></path><path d="M375 374c0-1-1-1-1-2 0-2 0-3 2-5 0 3 1 6 0 9v1c0-1-1-2-1-3z" class="f"></path><path d="M367 380c1 1 2 2 3 2l1-1c1 1 1 1 1 2l-1 1h0l-3 3c0-2 0-3-1-4v-3z" class="e"></path><path d="M390 354l1-1v2c0 1-1 3-2 3h-1v2h-1c-1 1-2 1-4 2v-2s3-1 3-2c2-1 3-2 4-4z" class="W"></path><path d="M376 367v-1h1c1 0 2-1 3-1 1-1 0-2 1-2l1 2v2h0-1 0l-1 3h0l-1-2-1 1c0 1-1 2-1 3v5h-1 0v-1c1-3 0-6 0-9z" class="o"></path><path d="M369 369h3c1 1 0 1 0 2-1 2-1 3-1 5l1 4-1 1v-1c-1-1-2-1-3-3-1-3 0-6 1-8z" class="i"></path><path d="M368 377h2c0-2 0-3 1-4v3l1 4-1 1v-1c-1-1-2-1-3-3z" class="W"></path><path d="M376 357l1 1c-2 1-5 4-6 6l1 1c-1 2-2 3-3 4-1 2-2 5-1 8 1 2 2 2 3 3v1h0l-1 1c-1 0-2-1-3-2h0v-1c0-1-1-3-1-4l1-6c1-5 6-9 9-12z" class="G"></path><path d="M387 346h0v-2h1 0 0 0v2c0 1-1 3-1 5l1-1v2c-3 3-5 5-8 7-3 1-6 4-8 6l-1-1c1-2 4-5 6-6l-1-1c2-2 4-3 6-5 3-1 4-3 5-6z" class="M"></path><path d="M388 350v2c-3 3-5 5-8 7 0-3 2-3 3-5 2-1 3-2 4-3l1-1z" class="W"></path><defs><linearGradient id="R" x1="384.16" y1="357.467" x2="366.362" y2="355.093" xlink:href="#B"><stop offset="0" stop-color="#595a5a"></stop><stop offset="1" stop-color="#827f81"></stop></linearGradient></defs><path fill="url(#R)" d="M385 346h2c-1 3-2 5-5 6-2 2-4 3-6 5-3 3-8 7-9 12l-1 6h-1c0 1 0 1-1 1-1-3-1-5-1-7 1-3 2-5 3-7 0-1 2-3 3-4s2-3 3-4h0v1h1c1 0 2-1 3-2 3-3 6-4 9-7z"></path><path d="M363 369c1-3 2-5 3-7h0c0 2 0 3-1 4h0v4h0c1 0 1-1 1-1h0 1l-1 6h-1c0 1 0 1-1 1-1-3-1-5-1-7z" class="o"></path><path d="M366 369c0-1 0-1 1-1 1-5 5-11 10-13 1-1 3-3 4-3h1c-2 2-4 3-6 5-3 3-8 7-9 12h-1z" class="X"></path><path d="M385 333c0-1 0-2 1-3 1 0 1 1 2 2s2 3 2 5v1c1 2 1 4 0 6l-1 3v2l-1 1-1 1c0-2 1-4 1-5v-2h0 0 0-1v2h0-2c-3 3-6 4-9 7-1 1-2 2-3 2h-1v-1h0c-1 1-2 3-3 4 0-2 1-4 2-6l2-1c0-1 0-2 1-2 2-1 3-2 4-3h0c2-3 4-4 4-8 1-1-1-3 0-5v-2-1h1c1 1 1 2 2 3h0z" class="h"></path><defs><linearGradient id="S" x1="385.573" y1="336.225" x2="389.947" y2="336.462" xlink:href="#B"><stop offset="0" stop-color="#666465"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#S)" d="M385 333c0-1 0-2 1-3 1 0 1 1 2 2s2 3 2 5v1c0 2 1 4 0 5h0c-1 0-1-1-1-2 0 0 0-1-1-2h0c0-2-2-4-2-6h-1z"></path><path d="M383 345h1c1-2 1-4 1-6l-1-1v-4h1v2c1 1 1 2 2 2 0 2 0 4-1 5 0 1 0 2-1 3-3 3-6 4-9 7-1 1-2 2-3 2h-1v-1h0c-1 1-2 3-3 4 0-2 1-4 2-6l2-1c0-1 0-2 1-2 2-1 3-2 4-3h0 2c1 0 2-1 2-2h1v1z" class="o"></path><path d="M380 346c1 0 2-1 2-2h1v1c-1 2-3 4-5 5h-1-1l4-4z" class="M"></path><path d="M378 346h2l-4 4c-1 1-2 1-3 1 0-1 0-2 1-2 2-1 3-2 4-3h0z" class="X"></path><path d="M373 351c1 0 2 0 3-1h1 1c-1 2-4 3-6 4-1 1-2 3-3 4 0-2 1-4 2-6l2-1z" class="h"></path><path d="M383 362c2-1 3-1 4-2h1v2l1 4v2 4c-1 4-3 7-6 10l-1 2-14 10h-1c-1 1-6 3-7 3 1-2 4-3 5-3v-1-2s1-1 1-2h1 0c2 0 3 0 4-1s2-2 3-4 2-4 2-7h1v-5c0-1 1-2 1-3l1-1 1 2h0l1-3h0 1 0v-2c1-1 1-2 1-3h0z" class="c"></path><path d="M367 389h0c2 0 3 0 4-1-1 2-3 4-6 5v-2s1-1 1-2h1z" class="Y"></path><path d="M383 371c0 1 0 2 1 3 0 2-1 4-2 6l-4 4h0l3-6v-1c1-1 1-1 1-3 0-1 0-2 1-3z" class="G"></path><path d="M376 384c2-2 3-5 3-8 1-1 1-3 2-4v1 5l-3 6-4 3c1-1 1-2 2-3z" class="Q"></path><path d="M385 368h1v2h2l1 2c-1 4-3 7-6 10 0-1 0-2 1-2v-2s0-1 1-1c0-2 0-4 1-5l-1-1-1 1c0-2 0-2 1-4z" class="J"></path><path d="M383 362c2-1 3-1 4-2h1v2l1 4v2 4l-1-2h-2v-2h-1c-1 2-1 2-1 4v2c-1-1-1-2-1-3s-1-3-1-4h0v-2c1-1 1-2 1-3h0z" class="l"></path><path d="M383 362c2 0 2 3 4 3v1c-1 0-1-1-2-1h-2v2 1l-1-1v-2c1-1 1-2 1-3z" class="h"></path><path d="M385 368v-1h2l1-2h0c0 1 0 2 1 3h0v4l-1-2h-2v-2h-1z" class="o"></path><path d="M383 362c2-1 3-1 4-2h1v2c-1 1-1 1-1 3-2 0-2-3-4-3h0z" class="b"></path><path d="M463 421c-2-2-3-4-2-6v-2l1-1c2 2 1 6 4 6l1-1 2 3c2-1 2-3 5-2 1 1-1 5 0 7 0 4 2 9 4 12h1c1 3 2 5 3 7l2 4c4 11 5 21 4 33 0 3-1 7 0 10 0 1 0 2 1 2 0-1 0-1 1-2h1l-1 3v5-1l1-1-2 6-2-2c0 1-1 2-1 3l-3 7-2 2v-2l-1-1 2-7v-4l1-7c-1-2 0-5-1-7h0v4h0v1h0 0v2 2c-1 2-1 3-1 5h-1c1-2 1-5 1-8v-3c-1-2 0-3-1-5 0-2 0-3-1-4v-1c0-7-1-15-4-22 0-2-1-4-2-5-1 0-3-5-3-6-3-5-5-9-8-13-6-6-10-9-18-10-2 1-5-1-7-1 4-2 9-1 13-1s7 1 10 4c1 0 2 1 3 2 1 0 2-1 3-1l2 2c-1-2-4-5-5-6z" class="E"></path><path d="M468 436c2 3 3 6 5 9h-1c-2-2-3-5-4-8v-1z" class="F"></path><path d="M482 485l1-10v9c2-1 1-2 2-4h0v1c0 3-1 6-2 9v2c-1-2 0-5-1-7z" class="D"></path><path d="M444 422h1 3c4 1 8 3 11 4 4 2 7 7 9 10v1c-2-2-3-5-6-5-6-6-10-9-18-10z" class="C"></path><path d="M463 421c-2-2-3-4-2-6v-2l1-1c2 2 1 6 4 6l1-1 2 3c2-1 2-3 5-2 1 1-1 5 0 7 0 4 2 9 4 12h1c1 3 2 5 3 7l2 4c4 11 5 21 4 33 0 3-1 7 0 10 0 1 0 2 1 2 0-1 0-1 1-2h1l-1 3v5-1l1-1-2 6-2-2c0 1-1 2-1 3l-3 7-2 2v-2l-1-1 2-7v-4l1-7v-2c1-3 2-6 2-9v-1h0c0-7-1-14-3-21 0-2-1-5-2-7 0-1-1-3-1-4s-1-2-2-3c0-1-1-3-2-4h0c0-2 0-3-1-4 0-3-2-4-3-6 0-1 0-1-1-2s0-3-1-4c-2-2-3-3-4-5l-2 1z" class="H"></path><path d="M487 501c2-2 2-5 3-7v5-1l1-1-2 6-2-2z" class="B"></path><path d="M485 480c0-1 0-2 1-3 0 3 0 7 1 9v6l-1-1c-1-2-1-3 0-6 0-1 0-3-1-4v-1z" class="P"></path><path d="M485 481c1 1 1 3 1 4-1 3-1 4 0 6l1 1-1 2h0-1c-1 1-1 2-1 3l-1 2h-1l1-7v-2c1-3 2-6 2-9z" class="c"></path><path d="M482 499h1l1-2c0-1 0-2 1-3h1v3c-1 3-1 4 0 7l-3 7-2 2v-2l-1-1 2-7v-4z" class="Q"></path><path d="M482 503c0-1 1-2 2-3v1l-3 10-1-1 2-7z" class="O"></path><path d="M482 499h1l1-2c0-1 0-2 1-3h1v3l-2 4v-1c-1 1-2 2-2 3v-4z" class="G"></path><path d="M463 421c-2-2-3-4-2-6v-2l1-1c2 2 1 6 4 6l1-1 2 3c2-1 2-3 5-2 1 1-1 5 0 7 0 4 2 9 4 12h0c1 4 3 8 5 11l3 9c1 5 2 11 1 17-1-3-1-5-2-8-1-6-3-13-6-18 0-1-1-2-2-3 0-1-1-3-2-4h0c0-2 0-3-1-4 0-3-2-4-3-6 0-1 0-1-1-2s0-3-1-4c-2-2-3-3-4-5l-2 1z" class="R"></path><path d="M309 194h1c0-1-1-2-2-3v-1h0c0-1 1-1 1-1h4c1 0 1 1 2 2h0l1-1 3 3 1 2c1 2 2 3 4 4h0c1 0 1 0 1-1h0 1 0l1 1h0l1 1 2 2c0 3 2 6 0 9h1v2c0 1 0 2 1 3v2c0 1 0 1-1 2v3 4 3 2l-1 1c-1 0-1-1-1-2l-3-1v2l2 5v10h2v1h-1v1l-1 1-1-1h0s-1 0-2 1h0l-1-1-2-2h0c0-1-1-2-1-3l-1-1h0c-1-1-1-2-3-3l-1 1c-1 1-1 2-1 3l-1-1c0-2-1-2-3-3h-9-4c-1-1-2-1-3-1 0-1 1-1 1-1h1l-1-1 1-1h0c2 1 6 1 7 1l1-1c-1 0-2 0-2-1h0-1c-1-1-5-3-5-4v-1h1 0c0-1 1-2 1-2h1 1l2-2h1c1 0 2 1 3 1v-2l-1-2v-4h0c1 1 2 1 3 1h1l-1-2-1-1v-1h-1c-2-1-2-4-3-6l-1 1c1-1 1-1 1-2l-1-2-1-1c1-1 1-1 1-2v-1h2l-1-1-1-3h0c0-1-1-2-1-2l-1-2h2c1 0 1 0 2-1v1h1l2 2h1v-3z" class="X"></path><path d="M326 221l-1-1c-1-1-1-2-1-2l3 2-1 1z" class="b"></path><path d="M321 220l1-1h1l1 2 1 1v3h-1c-1-1-2-2-2-3l-1-2z" class="W"></path><path d="M325 213l2 1c1 1 2 2 3 2v1c-1 1-1 1-1 2h-1l-3-6z" class="M"></path><path d="M320 206h0 2v1c1 0 1 1 1 2 0 0 1 1 1 2h0c-1 1-1 1-1 2 1 1 1 1 1 3h-1v-2h-2 0 0c0-2 0-2-1-4v-1-3z" class="O"></path><path d="M320 206h0 2v1h0v2h-2v-3z" class="J"></path><path d="M330 217c1 0 1 0 2 1 0 1 0 1-1 2v3 4 3 2l-1 1c-1 0-1-1-1-2-1-1-1-3-2-4v-2c0-1-1-3-1-4l1-1c1 1 1 2 2 3v-4c0-1 0-1 1-2z" class="d"></path><path d="M330 217c1 0 1 0 2 1 0 1 0 1-1 2v3c0-2-1-4-1-6z" class="l"></path><path d="M327 227c1 0 2 1 3 1 0 1 0 1 1 1v-2 3 2l-1 1c-1 0-1-1-1-2-1-1-1-3-2-4z" class="N"></path><path d="M320 210c1 2 1 2 1 4h0 0v6l1 2c0 1 1 2 2 3v1c2 0 2 0 3-1v2c1 1 1 3 2 4l-3-1v-1c-1 0-2-1-2-1l-1-1v-1l-2 2h0c0-1-1-2-2-2 0-1-1-1-1-2l-2-2-1-2c-1-1-1-2-2-3 1-1 1-2 1-3h1s1 1 1 2h1v-1l1-1 1 2h1v-2l-1-1c1-1 1-2 1-3z" class="M"></path><path d="M315 220c0-1-1-2 0-3h0c1 1 3 3 3 5h-2l-1-2z" class="N"></path><path d="M320 210c1 2 1 2 1 4h0 0v6l1 2c-1 0-1 1-2 2l-1-1v-2c-1-1-1-2-1-3h0 1v-2h1v-2l-1-1c1-1 1-2 1-3z" class="h"></path><path d="M320 214c0 2 1 3 0 5h0l-1-1v-2h1v-2z" class="O"></path><path d="M317 198l2 4c0 1 0 3 1 4v3 1c0 1 0 2-1 3l1 1v2h-1l-1-2-1 1v1h-1c0-1-1-2-1-2h-1c-1 0-1 0-1-1-1-1-2-2-1-4v-3c-1 0 0-2 0-2v-1h-1c0-2 0-1 1-3 0 1 2 3 3 4v-2l1 1h1l1-1-2-3 1-1z" class="o"></path><path d="M318 202l1 4h-2c-1-1-1-2-1-3h1l1-1z" class="S"></path><path d="M312 200c0 1 2 3 3 4v1h1l-1 1c-1 0-2-2-2-3h-1-1c0-2 0-1 1-3z" class="J"></path><path d="M312 204h0c0 1 1 2 1 3 0 0 0 1 1 1 1 1 0 2 2 4v1h1 1v1l-1 1v1h-1c0-1-1-2-1-2h-1c-1 0-1 0-1-1-1-1-2-2-1-4v-3c-1 0 0-2 0-2z" class="Z"></path><path d="M317 198l2 4c0 1 0 3 1 4v3 1c0 1 0 2-1 3l1 1v2h-1l-1-2v-1-2-2h-1v-3h2l-1-4-2-3 1-1z" class="G"></path><path d="M320 209v1c0 1 0 2-1 3l-1-3 2-1z" class="M"></path><path d="M317 198l2 4c0 1 0 3 1 4v3l-2 1 1-4-1-4-2-3 1-1z" class="l"></path><defs><linearGradient id="T" x1="313.637" y1="200.301" x2="314.744" y2="189.33" xlink:href="#B"><stop offset="0" stop-color="#adadad"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#T)" d="M309 194h1c0-1-1-2-2-3v-1h0c0-1 1-1 1-1h4c1 0 1 1 2 2h0c4 3 5 7 6 11h-2l-2-4-1 1 2 3-1 1h-1l-1-1v2c-1-1-3-3-3-4-1 0-1 0-1-1l-2 1c0-1-1-2-1-3h0 1v-3z"></path><path d="M309 194h1c0 1 1 1 1 2 1 1 1 2 1 2h1c0-2-1-3-1-4h1 0l1 1h1c1 1 1 2 2 3l-1 1 2 3-1 1h-1l-1-1v2c-1-1-3-3-3-4-1 0-1 0-1-1l-2 1c0-1-1-2-1-3h0 1v-3z" class="P"></path><path d="M315 202h0c0-2 0-3-1-5h1l1 2 2 3-1 1h-1l-1-1z" class="Q"></path><defs><linearGradient id="U" x1="328.858" y1="206.071" x2="317.677" y2="202.556" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#U)" d="M316 190l3 3 1 2c1 2 2 3 4 4h0c1 0 1 0 1-1h0 1 0l1 1h0l1 1 2 2c0 3 2 6 0 9h1v2c0 1 0 2 1 3v2c-1-1-1-1-2-1v-1c-1 0-2-1-3-2l-2-1c0-1-1-1-1-2h0c0-1-1-2-1-2 0-1 0-2-1-2v-1h-2 0c-1-1-1-3-1-4h2c-1-4-2-8-6-11l1-1z"></path><path d="M325 202l1 1-2 2h0v-2l1-1z" class="Z"></path><path d="M321 202c1 1 1 1 1 2l-2 2h0c-1-1-1-3-1-4h2z" class="K"></path><path d="M324 199h0c1 0 1 0 1-1h0 1 0l1 1h0v2 3l-1-1-1-1c0-1-1-2-1-3z" class="P"></path><path d="M327 199l1 1 2 2c0 3 2 6 0 9h1v2c0 1 0 2 1 3v2c-1-1-1-1-2-1v-1c-1 0-2-1-3-2v-2l1-1c-1-1-1-3-1-5h0v-2-3-2z" class="J"></path><path d="M327 212l1 2c1 0 2-1 3-1 0 1 0 2 1 3v2c-1-1-1-1-2-1v-1c-1 0-2-1-3-2v-2z" class="o"></path><path d="M327 201l1 3s0 3 1 3v5c-1 0-1 0-1-1-1-1-1-3-1-5h0v-2-3z" class="S"></path><path d="M301 195h2c1 0 1 0 2-1v1h1l2 2h0c0 1 1 2 1 3l2-1c0 1 0 1 1 1-1 2-1 1-1 3h1v1s-1 2 0 2v3c-1 2 0 3 1 4 0 1 0 1 1 1 0 1 0 2-1 3 1 1 1 2 2 3l1 2 2 2c-1 0-2 0-3 1h-1s1 1 1 2v2 7-2l-1-3v2h-1l-1-3c-1-1-2-1-4-2h0l-1-1v-2l-1-2v-4h0c1 1 2 1 3 1h1l-1-2-1-1v-1h-1c-2-1-2-4-3-6l-1 1c1-1 1-1 1-2l-1-2-1-1c1-1 1-1 1-2v-1h2l-1-1-1-3h0c0-1-1-2-1-2l-1-2z" class="O"></path><path d="M305 203l1 1-2 1 1 1 1 1c0 1 0 1-1 2 1 0 1 1 1 1 1 1 1 2 1 3 1 1 2 1 2 3 0 0 0 1 1 2h-1l-1-1v-1h-1c-2-1-2-4-3-6l-1 1c1-1 1-1 1-2l-1-2-1-1c1-1 1-1 1-2v-1h2z" class="Z"></path><path d="M306 223v-4h0c1 1 2 1 3 1h1v1l4 10v2h-1l-1-3c-1-1-2-1-4-2h0l-1-1v-2l-1-2z" class="W"></path><path d="M306 223v-4h0c1 1 2 1 3 1h1v1c-1 0-1 0-2 1h-1v1c0 1 2 2 1 4v1l-1-1v-2l-1-2z" class="a"></path><path d="M308 209h1c1-1 0-1 0-2h1v1l2 1c-1 2 0 3 1 4 0 1 0 1 1 1 0 1 0 2-1 3 1 1 1 2 2 3l1 2 2 2c-1 0-2 0-3 1h-1s1 1 1 2v2 7-2c0-1 0-5-1-6v-2l-1-1h0v-1c-1-4-2-7-3-10-1-2-1-3-2-5z" class="J"></path><path d="M310 208l2 1c-1 2 0 3 1 4 0 1 0 1 1 1 0 1 0 2-1 3-1-2-1-5-2-7h-1v-2z" class="l"></path><path d="M301 195h2c1 0 1 0 2-1v1h1l2 2h0c0 1 1 2 1 3l2-1c0 1 0 1 1 1-1 2-1 1-1 3h1v1s-1 2 0 2v3l-2-1v-1h-1c0 1 1 1 0 2h-1l-1-2-1-3-1-1-1-1-1-3h0c0-1-1-2-1-2l-1-2z" class="V"></path><path d="M305 195h1l2 2h0l-1 1c-2-1-2-1-2-3h0z" class="c"></path><path d="M308 197c0 1 1 2 1 3l2-1c0 1 0 1 1 1-1 2-1 1-1 3h1v1s-1 2 0 2v3l-2-1v-1h-1c0 1 1 1 0 2h-1l-1-2 2-1h0c-1-1-1-2-1-2v-1l1 1 1-1c-1-2-2-3-3-5l1-1z" class="G"></path><path d="M315 229v-2c0-1-1-2-1-2h1c1-1 2-1 3-1 0 1 1 1 1 2 1 0 2 1 2 2h0l2-2v1l1 1s1 1 2 1v1 2l2 5v10h2v1h-1v1l-1 1-1-1h0s-1 0-2 1h0l-1-1-2-2h0c0-1-1-2-1-3l-1-1h0c-1-1-1-2-3-3l-1 1c-1 1-1 2-1 3l-1-1c0-2-1-2-3-3h-9-4c-1-1-2-1-3-1 0-1 1-1 1-1h1l-1-1 1-1h0c2 1 6 1 7 1l1-1c-1 0-2 0-2-1h0-1c-1-1-5-3-5-4v-1h1 0c0-1 1-2 1-2h1 1l2-2h1c1 0 2 1 3 1l1 1h0c2 1 3 1 4 2l1 3h1v-2l1 3v2-7z" class="F"></path><path d="M318 227l1 2h-1c0 2 0 5-1 6-1 0-1-2-1-3l1-1v-3l1-1z" class="B"></path><path d="M314 231l1 3v2l-1 2h-1c-1-1-2-1-2-2v-2h2v-1h1v-2z" class="N"></path><path d="M317 235c1-1 1-4 1-6h1l1 1v3c-1 1-1 2-2 3v1l-1-1v-1z" class="U"></path><path d="M302 235c-1-1-5-3-5-4v-1h1 0c0-1 1-2 1-2h1 1l2-2h1c1 0 2 1 3 1l1 1h0c2 1 3 1 4 2l1 3v1h-2v2-1c-1-1-1-2-2-3h-1c0-1-1-3-2-3 0-1-2-1-2-2-1 1-1 1-1 2-1 0-2 0-2 1 1 2 2 3 4 4 1 1 2 3 4 4 1 0 1 1 2 2h-9-4c-1-1-2-1-3-1 0-1 1-1 1-1h1l-1-1 1-1h0c2 1 6 1 7 1l1-1c-1 0-2 0-2-1h0-1z" class="H"></path><path d="M308 228c2 1 3 1 4 2l1 3v1h-2v2-1c-1-1-1-2-1-4l-2-3z" class="M"></path><path d="M315 229v-2c0-1-1-2-1-2h1c1-1 2-1 3-1 0 1 1 1 1 2 1 0 2 1 2 2h0l2-2v1l1 1s1 1 2 1v1 2l2 5v10h2v1h-1v1l-1 1-1-1h0s-1 0-2 1h0l-1-1-2-2h0c0-1-1-2-1-3l-1-1h0c-1-1-1-2-3-3h0v-1l1-2v-1c1-1 1-2 2-3v-3l-1-1-1-2-1-1-1 1c0 1 0 1-1 2z" class="C"></path><path d="M321 239c1 0 2 2 2 3 1 0 1 0 1 1l-1 1h-1c-1-1-1-1-2-1h0l1-1v-3z" class="D"></path><path d="M317 239h2 0 2v3l-1 1c-1-1-1-2-3-3h0v-1z" class="S"></path><path d="M321 228h0l2-2v1l1 1s1 1 2 1v1 2c-1 1-1 1-2 1v-1c-1-1-3-3-3-4z" class="Q"></path><path d="M320 230h0c1 1 1 1 2 1 1 2 2 3 3 5 0 1 2 2 2 2v3 1 3c0-2-1-3-2-4 0-1-1-2-2-3h-1l-2-2h-1-1c1-1 1-2 2-3v-3z" class="I"></path><path d="M275 299h1l2 1c0 1-1 1-1 2v2h-1l1 1c1-1 0-2 1-3h1c-1 1-1 2-1 2 0 1 0 2-1 3l-1 7c0 2 0 3-1 5v1c0 1-1 2-2 3 1 1 1 2 1 3-2 4-3 5-6 8l-6 3c-4 1-9 5-11 9 0 0-1 1-1 0-1 2-2 3-3 4s-2 3-2 5c-2 3-4 5-6 8-1 0-2 1-2 2h0c-3 2-5 3-9 3h1c-1-1-3-1-4-1v-1h2l1-2c-3 1-6 2-9 2-3-1-6-3-7-5l-2-2h-1 0c0-1 0-3 1-4v-1l1-1c1-1 2-2 3-2 0-1 1-1 1-1 1 0 1-1 1-1 1 0 1-1 2-2l-1-1c-1-5-1-10 1-15 1-3 4-6 6-8 2 0 5 0 6-1 1 0 1-1 2-1h0 1v1h1l1 1 3 3c1 1 2 1 3 1h2v1h1c1 0 2 0 3-1h1c3 0 5-1 8-2 0 1 1 1 1 1h1 0 3c1-2 2-2 2-3 1-1 1-2 1-3v1h1l2-2h1v1h0s1 0 1 1h0l2-5v-1h-1l-1-1h2 1c0-2 1-5 1-7l1-3v-2l1-3z" class="k"></path><path d="M232 346c1 0 1 0 2 1l-1 1-2 4h-2l3-6h0z" class="N"></path><path d="M229 352h2v1c0 1 0 1 1 1v1l-3 1-1 1v-1c0-1-1-1-2-1h0l3-3h0z" class="h"></path><path d="M229 356v-1h0c0-1 1-1 1-1l1-1c0 1 0 1 1 1v1l-3 1z" class="l"></path><path d="M211 353v2 2 1c1 0 1-1 2-1h1v1 1h0c1 1 1 2 1 3h-1 0c-1-1-2-1-2-1l-2-2h-1 0c0-1 0-3 1-4v-1l1-1z" class="i"></path><path d="M241 333l2 1c-5 2-10 4-13 8l-3 3v-2c2-3 4-5 6-7l8-3z" class="P"></path><path d="M223 338v3 1c0 2-1 6 0 8h1c0 2-1 4-2 5-1 0-1 0-2-1l1-1c-1-1-2-2-2-3l1-1 1-5h0l2-6z" class="N"></path><path d="M223 342c0 2-1 6 0 8h1c0 2-1 4-2 5-1 0-1 0-2-1l1-1 2-11z" class="Q"></path><path d="M232 346v-1h0l-2 1v-1c0-1 1-2 1-2 2-2 3-3 5-3 1 0 2-1 3-1h0v2 1l1-1c0-1 0-1 1-2 1-2 2-2 4-2l-4 4c0 2 0 3-1 4s-1 1-1 2l-3 3v1c-1 2-2 3-4 4v-1c-1 0-1 0-1-1v-1l2-4 1-1c-1-1-1-1-2-1h0z" class="T"></path><path d="M232 346c1-1 2-3 3-4h1c1 0 1-1 1-1h1c0 1-1 3-2 3v3c-1 1-1 3-2 3 0-1 0-1-1-2l1-1c-1-1-1-1-2-1z" class="d"></path><path d="M234 347c0-1 1-2 2-3h0v3c-1 1-1 3-2 3 0-1 0-1-1-2l1-1z" class="O"></path><path d="M236 347c0 1 0 1 1 1l1-1v-2c1-1 1-2 2-3 0 0 1 0 1-1 0 2 0 3-1 4s-1 1-1 2l-3 3v1c-1 2-2 3-4 4v-1c-1 0-1 0-1-1v-1l2-4c1 1 1 1 1 2 1 0 1-2 2-3z" class="N"></path><path d="M233 348c1 1 1 1 1 2-1 1-1 3-2 4-1 0-1 0-1-1v-1l2-4z" class="Z"></path><path d="M239 347v1c-1 2-2 3-2 5-1 1-1 2-1 3s-1 2-1 2h0c-1 1-3 3-3 4h-1c-1 1-2 2-3 2-3 1-6 2-9 2-3-1-6-3-7-5 0 0 1 0 2 1h0 1c1 1 2 1 3 1v-1c0-1 0-1-1-2h0 0 1c1 1 3 2 4 2v-1c-2 0-2-1-4-2v-3h1c1 0 1 1 1 1 1 0 1 0 1 1h1 0c2 0 4-1 6-1l1-1 3-1c2-1 3-2 4-4v-1l3-3z" class="f"></path><path d="M239 347v1c-1 2-2 3-2 5-1 1-1 2-1 3s-1 2-1 2h0c-1 1-3 3-3 4h-1v-2l-3 1h-4c0-1 0-1 1-2h-3s-1 0-1-1h0 1 0c2 0 4-1 6-1l1-1 3-1c2-1 3-2 4-4v-1l3-3z" class="G"></path><path d="M222 359h5c1-1 2-1 3-1 2-1 4-2 5-3 0 1-4 3-4 4 1 0 2 0 3-1h1c-1 1-3 3-3 4h-1v-2l-3 1h-4c0-1 0-1 1-2h-3z" class="X"></path><path d="M247 346l1 1c0 1-1 2-1 3-1 1-2 3-2 5-2 3-4 5-6 8-1 0-2 1-2 2h0c-3 2-5 3-9 3h1c-1-1-3-1-4-1v-1h2l1-2c1 0 2-1 3-2h1c0-1 2-3 3-4h0s1-1 1-2 0-2 1-3c0-2 1-3 2-5 0 1 0 1 1 1v1h2c1 0 1 0 2 1 1-2 2-4 3-5z" class="B"></path><path d="M237 360l1 1c-2 1-3 2-4 3l-3 1 1 1s1-1 2-1h2 1c-3 2-5 3-9 3h1c-1-1-3-1-4-1v-1h2l1-2c1 0 2-1 3-2h1v1 1c2-1 3-2 5-4z" class="c"></path><path d="M231 362h1v1 1c-1 1-3 1-5 2l1-2c1 0 2-1 3-2z" class="H"></path><path d="M239 348c0 1 0 1 1 1v1h2c1 0 1 0 2 1l-1 1c-1 2-3 8-5 9l-1-1c-2 2-3 3-5 4v-1-1c0-1 2-3 3-4h0s1-1 1-2 0-2 1-3c0-2 1-3 2-5z" class="F"></path><path d="M239 348c0 1 0 1 1 1-1 1-1 2-1 3v1 2l1 1c0 1-1 1-1 2-1-2-1-3-1-5h0v-1-1-1c0 2 0 2-1 3 0-2 1-3 2-5z" class="C"></path><path d="M239 352c0-1 0-2 1-3v1h2c1 0 1 0 2 1l-1 1c-1 2-3 8-5 9l-1-1c0-1 1-2 2-2 0-1 1-1 1-2l-1-1v-2-1z" class="D"></path><path d="M239 352c0-1 0-2 1-3v1h2c1 0 1 0 2 1l-1 1c-1 1-2 2-3 2 0-1 0-1-1-1v-1z" class="Q"></path><path d="M256 325c0 1 1 1 1 1h0c0 2 0 2-1 3-2 1-4 2-5 3h0l-8 2-2-1-8 3c-2 2-4 4-6 7v2l-3 5h-1c-1-2 0-6 0-8v-1l1-1v-2c0-1 1-1 1-2h0c3-3 5-6 9-8 1 0 3 0 4-2 1 1 2 1 3 1h2v1h1c1 0 2 0 3-1h1c3 0 5-1 8-2z" class="E"></path><path d="M233 336l1-1c2-2 8-5 11-5h1l-1 1h1l-5 2-8 3z" class="F"></path><path d="M256 325c0 1 1 1 1 1h0c0 2 0 2-1 3-2 1-4 2-5 3h0l-8 2-2-1 5-2h-1l1-1h-1l3-3c3 0 5-1 8-2z" class="H"></path><path d="M246 330l6-2c1-1 2-1 4-1-3 3-6 3-10 4h-1l1-1z" class="U"></path><path d="M256 325c0 1 1 1 1 1h0l-1 1c-2 0-3 0-4 1l-6 2h-1l3-3c3 0 5-1 8-2z" class="C"></path><path d="M238 326c1 1 2 1 3 1h2v1c-1 0-1 1-2 1-5 2-13 6-15 12-1 1-1 2-1 3h0l2-2c1-2 4-5 6-6-2 2-4 4-6 7v2l-3 5h-1c-1-2 0-6 0-8v-1l1-1v-2c0-1 1-1 1-2h0c3-3 5-6 9-8 1 0 3 0 4-2z" class="H"></path><path d="M238 326c1 1 2 1 3 1h2v1c-1 0-1 1-2 1-5 0-8 1-12 4l-3 3c0 2-1 3-2 4v-2c0-1 1-1 1-2h0c3-3 5-6 9-8 1 0 3 0 4-2z" class="n"></path><path d="M232 321h1v1h1l1 1 3 3c-1 2-3 2-4 2-4 2-6 5-9 8h0c0 1-1 1-1 2v2l-1 1v-3l-2 6h0l-1 5-1 1-1-3-1-1c-1-5-1-10 1-15 1-3 4-6 6-8 2 0 5 0 6-1 1 0 1-1 2-1h0z" class="S"></path><path d="M235 323l3 3c-1 2-3 2-4 2l-1-1c1 0 1 0 2-1h-1c0-1 0-2 1-3z" class="K"></path><path d="M226 329c2-1 5-3 7-3h0c0 1 0 1-1 1-3 0-6 3-7 6-1 1-2 3-2 5h0l-2 6h0c-1 1-1 1-1 2-1-1-1-1-1-3v1c1-1 0-3 1-5v-2c0-1 1-1 1-2h0c1-3 3-5 5-6z" class="Q"></path><path d="M224 323c2 0 5 0 6-1 1 0 1-1 2-1h0v2c-1 1-2 2-3 2-1 1-2 2-3 4-2 1-4 3-5 6h0c0 1-1 1-1 2v2c-1 2 0 4-1 5v-1c0 2 0 2 1 3 0-1 0-1 1-2l-1 5-1 1-1-3-1-1c-1-5-1-10 1-15 1-3 4-6 6-8z" class="V"></path><path d="M220 334c-1 2-1 3-1 5h-1v-3h0c0-1 1-2 2-3l1 1h-1z" class="Q"></path><path d="M224 323c2 0 5 0 6-1 1 0 1-1 2-1h0v2c-1 1-2 2-3 2-1 1-2 2-3 4-2 1-4 3-5 6l-1-1h1l3-6c0-1 2-1 2-2 1-1 1-1 1-2l-6 6c-1 0-2 0-3 1 1-3 4-6 6-8z" class="G"></path><defs><linearGradient id="V" x1="264.595" y1="314.717" x2="272.096" y2="321.753" xlink:href="#B"><stop offset="0" stop-color="#afadad"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#V)" d="M275 299h1l2 1c0 1-1 1-1 2v2h-1l1 1c1-1 0-2 1-3h1c-1 1-1 2-1 2 0 1 0 2-1 3l-1 7c0 2 0 3-1 5v1c0 1-1 2-2 3 1 1 1 2 1 3-2 4-3 5-6 8l-6 3c-4 1-9 5-11 9 0 0-1 1-1 0-1 2-2 3-3 4 0-1 1-2 1-3l-1-1c-1 1-2 3-3 5-1-1-1-1-2-1h-2v-1c-1 0-1 0-1-1v-1c0-1 0-1 1-2s1-2 1-4l4-4c1-2 5-3 7-4v-1h-1 0c1-1 3-2 5-3 1-1 1-1 1-3h0 1 0 3c1-2 2-2 2-3 1-1 1-2 1-3v1h1l2-2h1v1h0s1 0 1 1h0l2-5v-1h-1l-1-1h2 1c0-2 1-5 1-7l1-3v-2l1-3z"></path><path d="M274 315c0-2 2-4 1-6v-1l1 1v-1s0-1 1-2v1l-1 7c-1 1-1 1-1 2-1 0-1 0-1-1z" class="D"></path><path d="M275 299h1l2 1c0 1-1 1-1 2v2h-1v-1-1c-1 1-1 1-2 1v1-2l1-3z" class="c"></path><path d="M264 320v1h1l2-2h1v1h0s1 0 1 1c-2 4-4 6-9 8h0l-2 1c-2 1-3 1-5 2-1 1 0 1-1 1v-1h-1 0c1-1 3-2 5-3 1-1 1-1 1-3h0 1 0 3c1-2 2-2 2-3 1-1 1-2 1-3z" class="Z"></path><path d="M264 320v1h1l2-2h1v1c-2 2-4 5-7 6h0c1-2 2-2 2-3 1-1 1-2 1-3z" class="B"></path><path d="M259 333l4-2c7-4 9-9 11-16 0 1 0 1 1 1 0-1 0-1 1-2 0 2 0 3-1 5v1c0 1-1 2-2 3h0c0 1 0 1-1 1 0 1 0 2-1 3h0c-3 1-5 3-7 5l-6 3c-1 1-2 2-4 2v1h1c0 1-1 1-1 2-2-1-4 1-5 0s0-2 0-3c1-1 3-2 4-3l1 1c2 0 4-1 5-2z" class="H"></path><path d="M273 323c1 1 1 2 1 3-2 4-3 5-6 8l-6 3c-4 1-9 5-11 9 0 0-1 1-1 0-1 2-2 3-3 4 0-1 1-2 1-3l-1-1 2-3 1-1h2l2-2c0-1 1-1 1-2h-1v-1c2 0 3-1 4-2l6-3c2-2 4-4 7-5h0c1-1 1-2 1-3 1 0 1 0 1-1h0z" class="B"></path><path d="M250 342h2c-1 2-2 3-2 4-1 2-2 3-3 4 0-1 1-2 1-3l-1-1 2-3 1-1z" class="D"></path><path d="M258 330l2-1c0 1 1 1 1 1l-2 3h0c-1 1-3 2-5 2l-1-1c-1 1-3 2-4 3 0 1-1 2 0 3s3-1 5 0l-2 2h-2l-1 1-2 3c-1 1-2 3-3 5-1-1-1-1-2-1h-2v-1c-1 0-1 0-1-1v-1c0-1 0-1 1-2s1-2 1-4l4-4c1-2 5-3 7-4 1 0 0 0 1-1 2-1 3-1 5-2z" class="V"></path><path d="M258 330l2-1c0 1 1 1 1 1l-2 3h0c-1 1-3 2-5 2l-1-1c2-1 5-2 5-4z" class="F"></path><path d="M250 342c-1-1-2 1-4 0h0c1-2 2-4 3-5 0 1-1 2 0 3s3-1 5 0l-2 2h-2z" class="Q"></path><path d="M243 344h0l1-1c0-1 1-2 2-2h0c0 2-1 2-1 3-1 1-1 0 0 1h0 2c0-1-1-1 0-2h2l-2 3c-1 1-2 3-3 5-1-1-1-1-2-1h-2v-1c-1 0-1 0-1-1v-1c0-1 0-1 1-2h1 0c1 0 1 0 1-1h1z" class="K"></path><path d="M240 345h1 0c1 0 1 0 1-1h1c-1 2-2 3-3 4v1c-1 0-1 0-1-1v-1c0-1 0-1 1-2z" class="Q"></path><path d="M240 181c1-1 2-1 4-1 0 1 1 2 1 4v2l-1 1v1h1 1 2 1c2 0 2 1 4 2h0c1 1 2 2 4 2h1v-2h-1c0-1 0-2-1-3v-1l-1-2v-2c1 1 2 3 4 4v-1c1 0 2 0 4 1 0-1 0-1 1-1l5 2 2 1c1 0 1 1 2 1 0-1-1-1-1-2h0c2 0 3 1 4 1v1l4 1 1 1h1s1 0 2 1 3 2 4 3l2 2h2l2 2 1-1h-1l1-1c2 1 3 3 4 4v1c2 1 3 3 3 4l1 1 1 2c0 1 0 1-1 2l1-1c1 2 1 5 3 6h1v1l1 1 1 2h-1c-1 0-2 0-3-1h0v4l1 2h-1-2l-1-1v-1c-2-2-3-6-5-7v1h-1c-1 0-1-1-2-1h-1 0-2c-1 0-1 1-2 1s-1-1-2 1h0v1h-3-3v1h-2c-1 0-2 1-3 1l-3 1-1-1h-4c-2-1-4 0-6 0l-1 1c-1 0-2 1-4 1v-1h0c-1 2-3 3-4 4s-1 2-2 3v1l-1 1-2 2h0c0 2-1 2-2 3h-1-2c-1-1-2-2-3-2h-1v-1l1-1c0-1-1-1-1-2h-1l2-2h2 1c-1-3-2-5-3-7-1-5-1-10-2-14h1v-2h0l1-1c-1-1-1-2-1-4-1-3-1-7-1-10s0-6 1-9z" class="V"></path><path d="M262 209h1c-1 0-1 0-2 1l1 1h-5v-1c2-1 3-1 5-1zm-18-5c1 1 1 3 3 3 0 1 0 1 1 1-1 1-2 1-3 2h0c0-2-1-4-1-6z" class="L"></path><path d="M253 201c-3-1-6-1-8-3h1c3 0 6 2 9 2l5 1v1c-3 0-5-1-7-1z" class="Z"></path><path d="M245 188h1 2 1c1 1 2 2 2 3h0v1h1c0 1-2 1-2 2-2-1-3-1-4-2h-2v-1c1 0 2 0 2-1l-1-2z" class="c"></path><path d="M249 188c2 0 2 1 4 2 0 1 2 2 3 3h-2c1 2 3 2 4 3 0 1 1 1 1 2h-2c-3-2-5-3-7-4 0-1 2-1 2-2h-1v-1h0c0-1-1-2-2-3z" class="J"></path><path d="M247 205h0c2 1 3 2 5 3h0 2c2 0 4 1 6-1 1 0 2 0 3 1l-1 1c-2 0-3 0-5 1-1-1-3 0-4 0h-1c-1 0-3 0-4-1v-2h-1v-2z" class="G"></path><path d="M246 203c1 0 2-1 3-1 2 1 4 1 6 2l-1 2c2 1 4 1 6 1-2 2-4 1-6 1h-2 0c-2-1-3-2-5-3h0c0-1-1-1-1-2z" class="h"></path><path d="M246 203c1 0 2-1 3-1 2 1 4 1 6 2l-1 2-8-3z" class="K"></path><path d="M257 198h2 1c2 0 5 2 7 2 2 1 3 1 5 3h1c-1 0-1 1-1 1-2 0-9 0-9 1h2c-2 0-5 0-7-2-1 1-2 1-3 1-2-1-4-1-6-2h0l4-1c2 0 4 1 7 1v-1l-5-1h1c1-1 1-1 1-2z" class="J"></path><path d="M257 198h2 1 0c1 1 1 2 3 2v1h-3l-5-1h1c1-1 1-1 1-2z" class="c"></path><path d="M253 201c2 0 4 1 7 1l9 1v1c-4 0-7-1-11-1-1 1-2 1-3 1-2-1-4-1-6-2h0l4-1z" class="P"></path><path d="M273 203c1 1 4 3 5 2h1l3 1-1 1h0l1 1c-1 0-2 1-3 1h-1c-2 0-3 1-6 1-1-1-4 1-6 1h-4l-1-1c1-1 1-1 2-1h-1l1-1c-1-1-2-1-3-1-2 0-4 0-6-1l1-2c1 0 2 0 3-1 2 2 5 2 7 2h-2c0-1 7-1 9-1 0 0 0-1 1-1z" class="S"></path><path d="M265 205h7c2 1 4 1 6 1-2 1-6 1-8 1-2-1-3-1-5-2z" class="K"></path><path d="M273 203c1 1 4 3 5 2h1l3 1-1 1c-1 0-2 0-3-1-2 0-4 0-6-1h-7 0-2c0-1 7-1 9-1 0 0 0-1 1-1z" class="M"></path><path d="M263 209c3-1 6-2 10-2 1 1 2 0 3 0 0 1 1 1 2 1l3-1 1 1c-1 0-2 1-3 1h-1c-2 0-3 1-6 1-1-1-4 1-6 1h-4l-1-1c1-1 1-1 2-1z" class="C"></path><path d="M255 182c1 1 2 3 4 4 7 6 15 15 24 19l-1 1-3-1h-1c-1 1-4-1-5-2h-1c-2-2-3-2-5-3-2 0-5-2-7-2h-1c0-1-1-1-1-2-1-1-3-1-4-3h2c-1-1-3-2-3-3h0c1 1 2 2 4 2h1v-2h-1c0-1 0-2-1-3v-1l-1-2v-2z" class="F"></path><path d="M266 198h1s1 0 1 1h3l8 6h-1c-1 1-4-1-5-2h-1c-2-2-3-2-5-3l-1-2z" class="a"></path><path d="M258 192v-2h-1c0-1 0-2-1-3v-1l15 13h-3c0-1-1-1-1-1h-1c-1-1-3-1-4-2h0 2 1c-2-2-4-3-7-4z" class="W"></path><defs><linearGradient id="W" x1="261.703" y1="193.272" x2="255.257" y2="193.997" xlink:href="#B"><stop offset="0" stop-color="#5c5c5d"></stop><stop offset="1" stop-color="#7a7879"></stop></linearGradient></defs><path fill="url(#W)" d="M253 190h0c1 1 2 2 4 2h1c3 1 5 2 7 4h-1-2 0c1 1 3 1 4 2l1 2c-2 0-5-2-7-2h-1c0-1-1-1-1-2-1-1-3-1-4-3h2c-1-1-3-2-3-3z"></path><path d="M264 185l5 2 2 1c1 0 1 1 2 1 0-1-1-1-1-2h0c2 0 3 1 4 1v1h0l2 2 3 3 1 1c1 2 3 3 5 5 0 1 0 1 1 2v1 2l2 1-1 1-4-2h-2c-9-4-17-13-24-19v-1c1 0 2 0 4 1 0-1 0-1 1-1z" class="Y"></path><path d="M264 187c1 0 2 0 3 1s2 1 3 2h-1v1c1 0 1 1 2 2l-7-6z" class="a"></path><g class="b"><path d="M264 185l5 2 2 1h-3c1 0 1 1 2 2-1-1-2-1-3-2s-2-1-3-1l-1-1c0-1 0-1 1-1z"></path><path d="M270 190l2 2c1 0 2 1 3 1 2 2 5 3 6 5h0c-1-1-2-1-4-2h0v2h0c-2-1-5-3-6-5-1-1-1-2-2-2v-1h1z"></path></g><path d="M277 198h0v-2h0c2 1 3 1 4 2h0c2 1 3 2 4 3 1 2 2 3 3 4l2 1-1 1-4-2h1v-1c-1-1-1-2-3-2l-1-1c-2-1-3-2-5-3h0z" class="d"></path><path d="M275 193h1c1 0 2 1 4 1l1 1v-1l1 1c1 2 3 3 5 5 0 1 0 1 1 2v1 2c-1-1-2-2-3-4-1-1-2-2-4-3-1-2-4-3-6-5z" class="O"></path><path d="M271 188c1 0 1 1 2 1 0-1-1-1-1-2h0c2 0 3 1 4 1v1h0l2 2 3 3v1l-1-1c-2 0-3-1-4-1h-1c-1 0-2-1-3-1l-2-2c-1-1-1-2-2-2h3z" class="N"></path><path d="M272 192c1-1 1-1 2-1s2 1 2 2h-1c-1 0-2-1-3-1z" class="M"></path><path d="M239 190l1-2h2v2c0 1 0 1 1 1v2c-1 1-1 2 0 4-1 1-1 1-1 3 1 1 1 3 2 4 0 2 1 4 1 6h0v1h1c1 0 1 0 2 1l1-1h3 5c0 1-1 1 0 2 3 0 7-1 9-2 2 0 5-2 6-1 3 0 4-1 6-1h1c1 0 2-1 3-1h4c-1 0-2 1-3 2-1 0-3 0-4 1h0c-2 1-3 1-5 2l-2 1c-1 1-2 1-3 1-1 1-2 2-4 3h0-2c-2 2-3 3-5 4h0c-1 2-3 3-4 4s-1 2-2 3v1l-1 1-2 2h0c0 2-1 2-2 3h-1-2c-1-1-2-2-3-2h-1v-1l1-1c0-1-1-1-1-2h-1l2-2h2 1c-1-3-2-5-3-7-1-5-1-10-2-14h1v-2h0l1-1c-1-1-1-2-1-4-1-3-1-7-1-10z" class="D"></path><path d="M240 205l1-1c0 2 0 3 1 4l3 14h-1l-1-1v-4h0v-1c-1 0-1-1-1-1v-2c-1-1 0-2-1-3l-1-5h0z" class="L"></path><defs><linearGradient id="X" x1="235.063" y1="190.449" x2="251.3" y2="219.755" xlink:href="#B"><stop offset="0" stop-color="#ababaa"></stop><stop offset="1" stop-color="#d2cfd2"></stop></linearGradient></defs><path fill="url(#X)" d="M239 190l1-2h2v2c0 1 0 1 1 1v2c-1 1-1 2 0 4-1 1-1 1-1 3 1 1 1 3 2 4 0 2 1 4 1 6 0 3 1 5 1 7 0 1 1 1 1 2v1 3h-1-1v-1l-3-14c-1-1-1-2-1-4-1-1-1-2-1-4-1-3-1-7-1-10z"></path><path d="M282 208h4c-1 0-2 1-3 2-1 0-3 0-4 1h0c-2 1-3 1-5 2l-2 1c-1 1-2 1-3 1-1 1-2 2-4 3h0-2c-2 2-3 3-5 4h0-3c-1 1-1 1-2 1l1 2-4 4c-1 1-2 3-4 3h0c0-1 1-1 1-2s0-2 1-3h-1-1c-1-1-1-1-1-2h0 1 1s1 0 1-1v-3h0v-1c1-1 1-1 1-2h-1v-1-5l1-1h3 5c0 1-1 1 0 2 3 0 7-1 9-2 2 0 5-2 6-1 3 0 4-1 6-1h1c1 0 2-1 3-1z" class="P"></path><path d="M250 220c1-1 5-2 7-1-1 1-1 1-3 2 0 0-1 0-2 1h-1c-1-1-1-1-1-2z" class="X"></path><path d="M248 221l2-1c0 1 0 1 1 2h1c-1 2-2 3-4 4h-1v-1s1 0 1-1v-3h0z" class="Z"></path><path d="M272 210c3 0 4-1 6-1h1c-1 1-3 2-4 2l-1 1h0c-3 0-6 2-8 4-2 0-2 1-4 1 0-1-1 0-1 0v-1c1-1 1-2 2-2l1-1 8-3z" class="I"></path><path d="M266 211c2 0 5-2 6-1l-8 3-1 1c-1 0-1 1-2 2v1h-1c-1 1-3 1-4 1v-2c-2 1-3 1-4 2h-3-1v-1-5l1-1h3 5c0 1-1 1 0 2 3 0 7-1 9-2z" class="G"></path><path d="M248 212l1-1h3v1c-1 1-1 1-2 0h-2z" class="O"></path><path d="M256 216l3-1h0-2v-1h2c1 0 3-1 5-1l-1 1c-1 0-1 1-2 2v1h-1c-1 1-3 1-4 1v-2z" class="H"></path><path d="M276 189l4 1 1 1h1s1 0 2 1 3 2 4 3l2 2h2l2 2 1-1h-1l1-1c2 1 3 3 4 4v1c2 1 3 3 3 4l1 1 1 2c0 1 0 1-1 2l1-1c1 2 1 5 3 6h1v1l1 1 1 2h-1c-1 0-2 0-3-1h0v4l1 2h-1-2l-1-1v-1c-2-2-3-6-5-7v1h-1c-1 0-1-1-2-1h-1 0-2c-1 0-1 1-2 1s-1-1-2 1h0v1h-3-3v1h-2c-1 0-2 1-3 1l-3 1-1-1h-4c-2-1-4 0-6 0l-1 1c-1 0-2 1-4 1v-1c2-1 3-2 5-4h2 0c2-1 3-2 4-3 1 0 2 0 3-1l2-1c2-1 3-1 5-2h0c1-1 3-1 4-1 1-1 2-2 3-2h-4l-1-1h0l1-1 1-1h2l4 2 1-1-2-1v-2-1c-1-1-1-1-1-2-2-2-4-3-5-5l-1-1-3-3-2-2h0z" class="M"></path><path d="M297 211l1 3h0-1c-1 0-1-1-1-2l1-1h0z" class="N"></path><path d="M303 223c0-1 0-2 1-2h0l2 2 1 2h-1-2l-1-1v-1z" class="O"></path><path d="M272 214l2-1c2-1 3-1 5-2-1 1-2 1-3 2v1c-1 1-2 0-3 2 1 1 3 1 4 1h2v-1l1 1h1 0c2-1 4-1 5-2h2l2-2v-1h0c1 0 1 1 2 1-1 1-1 1-1 2 0 0-1 0-1 1h-3c0 1-1 1-1 1-1 0-1 0-2 1h-3 0-1c-1 0-1 1-2 1h-1c-2-1-3 0-5 0h-2v-1h-2v-1h1c2 0 2-2 3-3z" class="a"></path><path d="M294 203h0l-2-2v-1h1l1 1c1 0 1 0 1 1h1 1v-1c1 0 1 1 2 1h0c2 1 3 3 3 4l1 1 1 2c0 1 0 1-1 2l1-1c1 2 1 5 3 6h1v1h-1-2l-1-1v1c-1 1 0 2 0 3h-1c-1-1-1-1-2-3 0-1 0-3-1-4h0l1-1c0-1-1-2-1-3v-2 3h-1l-2-4-3-3z" class="O"></path><path d="M303 213c1 0 1 1 2 2v1c-1-1-2-1-2-1v-2z" class="Z"></path><path d="M304 209l-2 2v-4h1l1 2z" class="M"></path><path d="M294 203c1 0 1-1 2 0 2 1 3 3 4 4v3h-1l-2-4-3-3z" class="N"></path><path d="M286 208h3c2 1 3 2 4 4 1 0 1 1 1 2h-1l-1-1c-1 0-1-1-2-1h0v1l-2 2h-2c-1 1-3 1-5 2h0-1l-1-1v1h-2c-1 0-3 0-4-1 1-2 2-1 3-2v-1c1-1 2-1 3-2h0c1-1 3-1 4-1 1-1 2-2 3-2z" class="f"></path><path d="M276 189l4 1 1 1h1s1 0 2 1 3 2 4 3l2 2h2l2 2 1-1h-1l1-1c2 1 3 3 4 4v1h0c-1 0-1-1-2-1v1h-1-1c0-1 0-1-1-1l-1-1h-1v1l2 2h0l3 3 2 4c0 1 0 1-1 1h-1 0l-1-1-1 1s-1 0-2 1c-1-2-2-3-4-4h-3-4l-1-1h0l1-1 1-1h2l4 2 1-1-2-1v-2-1c-1-1-1-1-1-2-2-2-4-3-5-5l-1-1-3-3-2-2h0z" class="J"></path><path d="M290 200c1 0 1 1 2 2v1h-1c-1-1-1-2-1-3z" class="O"></path><path d="M282 195h0c3 1 3 2 5 3h0 2v-1l-1-1v-1l2 2h2l2 2 1-1c0 1 1 1 1 2v1l-1-1s-1 0-2-1l-1 1v-1h-1l-1 1c0-1-1-1-2-2l-1 2c-2-2-4-3-5-5z" class="G"></path><path d="M276 189l4 1 1 1h1s1 0 2 1 3 2 4 3v1l1 1v1h-2 0c-2-1-2-2-5-3h0l-1-1-3-3-2-2h0z" class="c"></path><path d="M283 205h2l4 2 1-1h0c2 1 3 2 4 3l3-3 2 4c0 1 0 1-1 1h-1 0l-1-1-1 1s-1 0-2 1c-1-2-2-3-4-4h-3-4l-1-1h0l1-1 1-1z" class="l"></path><path d="M283 205h2l4 2c1 0 2 1 3 2-1 0-2 0-3-1h-3-4l-1-1h0l1-1 1-1z" class="H"></path><path d="M240 247h3c0 1 1 2 2 3 1-1 2-1 2-1h2 0c1 0 2 0 3 1h0l-2 1-1 1v1l1 1h-1v1h2v1h-1l-1 1h0 4v1h-1l1 1 1 1h-2v1h2v1h-2l1 1h1 1v1c-1 0-2 0-2 1h3 1 0c1 0 2 1 2 1l1 1h0 1c0 1 0 1 1 2l1 1h-1v1c1 1 1 1 2 1h0c0 1 0 2-1 3h-1c-1 1-2 1-2 2l-4 3c1-1 1-1 1-2h1c0-1 1-2 2-2-1-1-2-1-3-1s-1 0-2 1l1-3h-3c-1 0-1-1-2 0-1 0-2 0-3 1v1c-1 3-2 5-2 8l-1 9v5h0c1 2 1 3 1 5l-2 4c-1 3-4 5-7 7-3 0-5 0-8 2h-1 0-3v-1c-1 0-4 0-5 1l-1-1s-1 0-1-1l1-1h-3l1-1v-1h-1 0c1-1 2-1 4-1-3-1-5-1-7-2l-1-1c-2-1-4-2-5-3 0-1 1-2 2-2v-1l-2-2 1-2-1-6-1-1h1l-1-1c-1-1-2-3-3-4h0c-1-2-1-4-1-6 0-4 0-8 2-10 2-1 3-1 5-1l3 1h0v-4l1-1s0 1 1 1v2h1l2-2 5-6h0c1-1 2-2 3-4 1-1 2-2 4-3 1-1 2-2 4-3h1l2-1h4z" class="D"></path><path d="M224 286v1c1 0 2-1 2-2 1-1 2-1 3-2-2 3-4 5-8 6h-1c1-1 3-2 4-2v-1z" class="C"></path><path d="M207 286c1 1 2 2 4 2v2l-4-1-1-1 1-2z" class="V"></path><path d="M203 284c1-1 2-1 3-1v1 1l1 1-1 2c-1-1-2-3-3-4z" class="c"></path><path d="M229 266v2l-1 1v1h2c0 2-2 3-2 4-1 2-1 3-2 5v-5c0-3 1-5 3-8z" class="L"></path><path d="M226 274v5c-1 3-3 5-6 7 0-1 2-2 2-3 2-1 2-2 2-4h-1c0 1-1 2-2 3l-1-1c1 0 1 0 1-1 1-1 2-2 2-3h2c0-1 1-1 1-3z" class="K"></path><path d="M221 282c1-1 2-2 2-3h1c0 2 0 3-2 4 0 1-2 2-2 3-1 1-2 1-4 1-1 0-1 0-1-1l3-2c1-1 2-1 3-2z" class="L"></path><path d="M217 280v2h2s1 0 1-1l1 1c-1 1-2 1-3 2l-3 2h-4v-1l1-2h0c1-1 3-1 5-2v-1z" class="Z"></path><path d="M241 253h4 2 2l1 1h-1v1h2v1h-1l-1 1h0c-1 0-2 1-2 2h0-1l-1-1c1 0 1 0 2-1h-2l-1-1c1 0 1 0 1-1-2-1-4 0-6 0l1-1 1-1z" class="L"></path><path d="M221 275h3 0c0 1-1 1-1 2h0c0 1-1 2-2 3 0 1 0 1-1 1 0 1-1 1-1 1h-2v-2 1l-1-1v-2l2-3h2v1l1-1z" class="c"></path><path d="M223 277h0c0 1-1 2-2 3l-1-1c1-1 1-2 3-2z" class="P"></path><path d="M218 275h2v1l-3 4v1l-1-1v-2l2-3z" class="T"></path><path d="M235 255c2-1 3-1 5-1l-1 1c-1 0-2 0-3 1s-3 3-4 3l-2 1-4 4-1 1h-1v-1h0c0-2 1-3 2-5l2-1 2-1 5-2z" class="O"></path><path d="M235 255c2-1 3-1 5-1l-1 1c-1 0-2 0-3 1s-3 3-4 3c0-1-1-1-1-2 1 0 4-1 5-2h-1z" class="c"></path><path d="M230 262c2-1 3-2 4-2l2-1c-2 2-5 4-7 7s-3 5-3 8c0 2-1 2-1 3h-2 0c0-1 1-1 1-2h0-3l-1 1v-1h-2l1-1h1l1-3 1-1v-2c1-2 1-3 2-4h0v1h1l1-1 4-4v2z" class="h"></path><path d="M226 264l1 1-3 5c0-2 0-4 1-5l1-1z" class="J"></path><path d="M223 272h1c1 1 1 2 0 3h0-3c1-1 1-2 2-3z" class="l"></path><path d="M226 264l4-4v2l-3 3-1-1z" class="K"></path><path d="M222 270s0 2 1 2c-1 1-1 2-2 3l-1 1v-1h-2l1-1h1l1-3 1-1z" class="b"></path><path d="M224 286c2-2 4-5 5-9v-2c1 0 1 0 1-1v-1h0l1-1v-3c2-2 5-4 7-7 1-1 3-2 4-2 1-1 2-1 3-1l-1 1v1h0l1 1-2 1c-3 2-6 4-8 7-1 1-1 2-2 3l-3 6v1 1c0 1-1 1-1 2-1 1-2 1-3 2 0 1-1 2-2 2v-1z" class="F"></path><path d="M222 258h0c1-1 2-2 3-4 1-1 2-2 4-3 1 0 1 0 2-1v1c0 1-1 1-1 2-2 1-4 3-5 5l1 1c-1 2-2 3-2 5-1 1-1 2-2 4v2l-1 1-1 3h-1l-1 1-2 3h-1c1-3 1-5 0-8 1-2 2-4 2-6l5-6z" class="N"></path><path d="M222 262c1-2 2-3 3-4l1 1c-1 2-2 3-2 5-1 1-1 2-2 4v2l-1 1-1 3h-1s0-1-1-1h0-1c0-3 1-4 3-7v-1c1-1 1-2 2-3z" class="M"></path><path d="M221 271c-1-2-1-2 0-4 0 1 1 1 1 1v2l-1 1z" class="o"></path><path d="M222 262c1-2 2-3 3-4l1 1c-1 2-2 3-2 5-1 1-1 2-2 4 0 0-1 0-1-1v-2c1 0 2-1 2-2l-1-1z" class="J"></path><path d="M240 247h3c0 1 1 2 2 3 1-1 2-1 2-1h2 0c1 0 2 0 3 1h0l-2 1-1 1v1h-2-2-4l-1 1c-2 0-3 0-5 1l-5 2-2 1-2 1-1-1c1-2 3-4 5-5 0-1 1-1 1-2v-1c-1 1-1 1-2 1 1-1 2-2 4-3h1l2-1h4z" class="H"></path><path d="M249 249c1 0 2 0 3 1h0l-2 1-1 1v1h-2-2-4 1c1-1 1-1 3-1h-1s-1 0-1-1h-1v-1h3c1-1 2-1 2-1h2 0z" class="D"></path><path d="M247 249h2v1c-1 1-2 2-4 2-1-1-1-1-3-1v-1h3c1-1 2-1 2-1z" class="P"></path><path d="M236 247h4l1 1c0 1 0 1-1 1-1 2-3 1-5 2-1 1-5 4-5 5v1l-2 1-2 1-1-1c1-2 3-4 5-5 0-1 1-1 1-2v-1c-1 1-1 1-2 1 1-1 2-2 4-3h1l2-1z" class="L"></path><path d="M234 248l2-1v1 2h-2l-1-2h1z" class="H"></path><path d="M236 247h4l1 1c0 1 0 1-1 1l-4 1v-2-1z" class="F"></path><path d="M231 250l2 1h0l-1 1c-2 1-3 3-4 5v1l-2 1-1-1c1-2 3-4 5-5 0-1 1-1 1-2v-1z" class="P"></path><path d="M249 257h4v1h-1l1 1 1 1h-2v1h2v1h-2l1 1h1c-2 0-6 1-7 2v1c-1 0-1 1-2 1-2 1-3 2-4 3l-3 3c-1 1-2 3-3 5 0 1-1 2-1 2h-1v-1l1-1-1-1v-1h0c-1 2-2 4-3 5v-1-1l3-6c1-1 1-2 2-3 2-3 5-5 8-7l4-1v-1h-2v-1h2v-1h0c0-1 1-2 2-2z" class="H"></path><path d="M252 260v1h2v1h-2l1 1h1c-2 0-6 1-7 2v1c-1 0-1 1-2 1-2 1-3 2-4 3l-3 3c-1 1-2 3-3 5 0-2 1-3 2-5 2-4 7-5 10-9v-1c1-1 3-1 4-1l1-2z" class="n"></path><path d="M249 257h4v1h-1l1 1 1 1h-2l-1 2c-1 0-3 0-4 1-4 1-8 5-12 8v-1c2-3 5-5 8-7l4-1v-1h-2v-1h2v-1h0c0-1 1-2 2-2z" class="P"></path><defs><linearGradient id="Y" x1="223.393" y1="280.593" x2="235.941" y2="285.322" xlink:href="#B"><stop offset="0" stop-color="#adabab"></stop><stop offset="1" stop-color="#e5e4e5"></stop></linearGradient></defs><path fill="url(#Y)" d="M238 273c3 0 3-1 5-1h0-1c0 1 0 1-1 1-1 2-2 2-3 4 0 1-1 1-1 3h0c1 2-2 6-3 8l-1 1-1 2h-1l-1-1c-2 2-4 3-7 4-1 1-3 1-5 1h0c-1 1-2 1-3 1h-1 0v-3c-1 0-1 0-2-1l2-1h6c2-1 4-1 6-2h0 1 0c3-3 4-7 6-12l1 1-1 1v1h1s1-1 1-2c1-2 2-4 3-5z"></path><path d="M232 286c1 1 1 2 1 3l-1 2h-1l-1-1c0-1 2-3 2-4z" class="G"></path><path d="M237 280c1 2-2 6-3 8l-1 1c0-1 0-2-1-3l1-1 4-5z" class="V"></path><path d="M214 291h6c2-1 4-1 6-2h0 1c-3 2-6 3-9 3h-1c2 2 4 2 6 2-1 1-3 1-5 1h0c-1 1-2 1-3 1h-1 0v-3c-1 0-1 0-2-1l2-1z" class="G"></path><path d="M214 293l4 2h0c-1 1-2 1-3 1h-1 0v-3z" class="W"></path><path d="M212 264l1-1s0 1 1 1v2h1l2-2c0 2-1 4-2 6 1 3 1 5 0 8h1v2l1 1c-2 1-4 1-5 2h0l-1 2v1-1c-2 0-3-1-5-1v-1c-1 0-2 0-3 1h0c-1-2-1-4-1-6 0-4 0-8 2-10 2-1 3-1 5-1l3 1h0v-4z" class="r"></path><path d="M213 276c1 0 1 1 1 2-1 0 0 0-1 1-1 0-1 1-2 2l-1-1-1-1v-1h1v1h1c0-2 1-2 2-3z" class="X"></path><path d="M215 278h1v2l1 1c-2 1-4 1-5 2h-3l1-1v-2l1 1c1-1 1-2 2-2 1-1 0-1 1-1h1 0z" class="f"></path><path d="M215 278h1v2c-1 0-2 1-3 1 1-1 1-2 2-3h0z" class="X"></path><path d="M203 284c-1-2-1-4-1-6 0-4 0-8 2-10 2-1 3-1 5-1l-1 1c-3 1-4 2-5 5-1 2 0 4 1 6 1 3 3 3 5 4h3 0l-1 2v1-1c-2 0-3-1-5-1v-1c-1 0-2 0-3 1h0z" class="J"></path><path d="M211 285l-2-1v-1h0 3l-1 2z" class="K"></path><path d="M212 264l1-1s0 1 1 1v2h1l2-2c0 2-1 4-2 6 1 3 1 5 0 8h0-1c0-1 0-2-1-2v-1c0-2 0-3-1-6h0l-4-1 1-1 3 1h0v-4z" class="i"></path><path d="M212 264l1-1s0 1 1 1v2h1l2-2c0 2-1 4-2 6 0 1 0 1-1 2h-1v-1c0-2 1-5-1-7z" class="e"></path><defs><linearGradient id="Z" x1="211.7" y1="294.295" x2="245.972" y2="289.582" xlink:href="#B"><stop offset="0" stop-color="#c3c1c2"></stop><stop offset="1" stop-color="#f3f3f3"></stop></linearGradient></defs><path fill="url(#Z)" d="M254 263h1v1c-1 0-2 0-2 1h3 1 0c1 0 2 1 2 1l1 1h0 1c0 1 0 1 1 2l1 1h-1v1c1 1 1 1 2 1h0c0 1 0 2-1 3h-1c-1 1-2 1-2 2l-4 3c1-1 1-1 1-2h1c0-1 1-2 2-2-1-1-2-1-3-1s-1 0-2 1l1-3h-3c-1 0-1-1-2 0-1 0-2 0-3 1v1c-1 3-2 5-2 8l-1 9v5h0c1 2 1 3 1 5l-2 4c-1 3-4 5-7 7-3 0-5 0-8 2h-1 0-3v-1c-1 0-4 0-5 1l-1-1s-1 0-1-1l1-1h-3l1-1v-1h-1 0c1-1 2-1 4-1-3-1-5-1-7-2l-1-1c-2-1-4-2-5-3 0-1 1-2 2-2v-1l-2-2 1-2-1-6-1-1h1l4 1 3 1-2 1c1 1 1 1 2 1v3h0 1c1 0 2 0 3-1h0c2 0 4 0 5-1 3-1 5-2 7-4l1 1h1l1-2 1-1c1-2 4-6 3-8h0c0-2 1-2 1-3 1-2 2-2 3-4 1 0 1 0 1-1h1 0c-2 0-2 1-5 1l3-3c1-1 2-2 4-3 1 0 1-1 2-1v-1c1-1 5-2 7-2z"></path><path d="M224 309h7 0c-1 1-1 1 0 1-1 1-2 1-2 1v1c-1 0-1-1-1-1v-2h-4z" class="M"></path><path d="M233 295c1 1 1 1 2 1l1-1h0l1 1c0-1 0-1 1-2v-1 4c-1 0-1 0-1 1-1 1-2 3-3 3v1-2h-1l-2 1v-1l2-3v-1h-1l1-1z" class="B"></path><path d="M220 309h4 4v2c-4 0-6 1-9 1h-3l1-1v-1h-1 0c1-1 2-1 4-1z" class="i"></path><path d="M241 270v1c1 0 1 0 1-1 1 0 4-2 5-2h1v3c-1 1-3 3-4 5v1c-1 1-1 3-2 4v-1s0-1 1-2c0-1-1-1 0-2s0 0 0-1l1-1c-2 1-2 3-3 4-2 3-2 7-4 10-1 3-3 5-4 7l-1 1h1v1l-2 3v-3-1l-2 2-1-1c1 0 1-1 1-1l1-1c1-1 2-2 2-4l1-2 1-1c1-2 4-6 3-8h0c0-2 1-2 1-3 1-2 2-2 3-4 1 0 1 0 1-1h1 0c-2 0-2 1-5 1l3-3z" class="F"></path><defs><linearGradient id="a" x1="216.369" y1="296.789" x2="228.309" y2="304.457" xlink:href="#B"><stop offset="0" stop-color="#a0a09f"></stop><stop offset="1" stop-color="#d7d5d6"></stop></linearGradient></defs><path fill="url(#a)" d="M208 296l1 1h0 0c1 2 2 2 3 3l1 1c1-1 2-1 2-1 1 0 3 1 4 1h1c0 1 1 1 2 1 3 0 5-1 7-2 0 0 2-2 2-3v3 1l2-1h1v2h-1c0 1-1 2-2 2-1 1-3 2-5 2h-5 0c-2 0-4-1-6-1-1 0-1 1-2 1v1l-1-1c-2-1-4-2-5-3 0-1 1-2 2-2v-1l-2-2 1-2z"></path><path d="M209 297h0c1 2 2 2 3 3l1 1c2 1 3 2 5 2h1c3 2 9 2 13 0l1-1c0 1-1 2-2 2-1 1-3 2-5 2s-4 0-6-1c-1 0-2-1-3-1-2-1-3-1-4-2-2-1-4-2-4-5h0z" class="l"></path><path d="M208 296l1 1c0 3 2 4 4 5 1 1 2 1 4 2 1 0 2 1 3 1 2 1 4 1 6 1h-5 0c-2 0-4-1-6-1-1 0-1 1-2 1v1l-1-1c-2-1-4-2-5-3 0-1 1-2 2-2v-1l-2-2 1-2z" class="J"></path><path d="M210 303l5 2c-1 0-1 1-2 1v1l-1-1-2-2v-1h0z" class="D"></path><path d="M207 303c0-1 1-2 2-2l1 2h0v1l2 2c-2-1-4-2-5-3z" class="Q"></path><path d="M239 303c4-1 3-4 5-6h1c1 2 1 3 1 5l-2 4c-1 3-4 5-7 7-3 0-5 0-8 2h-1 0-3v-1c-1 0-4 0-5 1l-1-1s-1 0-1-1l1-1c3 0 5-1 9-1 0 0 0 1 1 1v-1s1 0 2-1c-1 0-1 0 0-1h0v-1c3 0 5-2 7-4l1-1z" class="G"></path><path d="M219 314c1-1 3-2 5-2 1 0 2 1 3 1 0 1-1 1-2 1s-4 0-5 1l-1-1z" class="c"></path><path d="M238 304l2 1 2-2h0c0 1 0 2-1 3-2 2-7 5-10 6h-2v-1s1 0 2-1c-1 0-1 0 0-1h0v-1c3 0 5-2 7-4z" class="h"></path><path d="M207 289l4 1 3 1-2 1c1 1 1 1 2 1v3h0 1c1 0 2 0 3-1h0c2 0 4 0 5-1 3-1 5-2 7-4l1 1h1c0 2-1 3-2 4l-1 1s0 1-1 1l1 1 2-2v1c0 1-2 3-2 3-2 1-4 2-7 2-1 0-2 0-2-1h-1c-1 0-3-1-4-1 0 0-1 0-2 1l-1-1c-1-1-2-1-3-3h0 0l-1-1-1-6-1-1h1z" class="n"></path><path d="M222 297l3-1 1 1h1c-1 1-4 2-5 2h-4c1-1 2 0 3-1h-2v-1h3z" class="S"></path><path d="M209 297l1-2h1c0 1 1 1 2 2l1-1h1l7 1h-3v1h2c-1 1-2 0-3 1l-1-1c-1 0-2-1-3-1h-1l-1 1 1 1-1 1c-1-1-2-1-3-3z" class="J"></path><path d="M212 300l1-1-1-1 1-1h1c1 0 2 1 3 1l-1 1c1 0 3 1 4 2h5l1-1h3c-2 1-4 2-7 2-1 0-2 0-2-1h-1c-1 0-3-1-4-1 0 0-1 0-2 1l-1-1z" class="K"></path><defs><linearGradient id="b" x1="224.45" y1="295.785" x2="224.632" y2="290.981" xlink:href="#B"><stop offset="0" stop-color="#4d4b4b"></stop><stop offset="1" stop-color="#646365"></stop></linearGradient></defs><path fill="url(#b)" d="M230 290l1 1c-1 3-3 4-6 5l-3 1-7-1c1 0 2 0 3-1h0c2 0 4 0 5-1 3-1 5-2 7-4z"></path><path d="M207 289l4 1 3 1-2 1c1 1 1 1 2 1v3h0l-1 1c-1-1-2-1-2-2h-1l-1 2h0 0l-1-1-1-6-1-1h1z" class="j"></path><path d="M209 297c0-1-1-2-1-4 0 0 0-1 1-1 1 1 3 2 5 4h0l-1 1c-1-1-2-1-2-2h-1l-1 2h0z" class="O"></path><path d="M254 263h1v1c-1 0-2 0-2 1h3 1 0c1 0 2 1 2 1l1 1h0 1c0 1 0 1 1 2l1 1h-1v1c1 1 1 1 2 1h0c0 1 0 2-1 3h-1c-1 1-2 1-2 2l-4 3c1-1 1-1 1-2h1c0-1 1-2 2-2-1-1-2-1-3-1s-1 0-2 1l1-3h-3c-1 0-1-1-2 0-1 0-2 0-3 1v1c-1 3-2 5-2 8l-1 9v5h0-1c-2 2-1 5-5 6l3-6v-5c0-2-1-4-1-6s1-4 1-5c1-1 1-3 2-4v-1c1-2 3-4 4-5v-3h-1c-1 0-4 2-5 2 0 1 0 1-1 1v-1c1-1 2-2 4-3 1 0 1-1 2-1v-1c1-1 5-2 7-2z" class="S"></path><path d="M256 265h1 0c1 0 2 1 2 1l1 1-2 1c0-1-1-1-2-1v-2z" class="Q"></path><path d="M258 268l2-1h0 1c0 1 0 1 1 2l1 1h-1v1c1 1 1 1 2 1h0-3s-1 0-1-1v-3h-2z" class="L"></path><path d="M253 269l1-1c1 0 3 0 4 1h0c0 1 0 1-1 1-2 0-4 1-6 2h-1l1-1 3-1-1-1z" class="J"></path><path d="M264 272c0 1 0 2-1 3h-1c-1 1-2 1-2 2l-4 3c1-1 1-1 1-2h1c0-1 1-2 2-2-1-1-2-1-3-1 1-1 3-2 4-3h3z" class="G"></path><path d="M248 274v1c-1 3-2 5-2 8l-1 9v5c-1-2-1-5-1-7 0-6 0-11 4-16zm-6 7c1-1 1-3 2-4v-1c1-2 3-4 4-5l-2 4c-5 6-2 15-4 22v-5c0-2-1-4-1-6s1-4 1-5z" class="C"></path><path d="M247 266c1 0 2-1 3-1v1h-1l1 1c1 0 3 1 3 2l1 1-3 1-1 1c-1 1-2 2-4 3l2-4v-3h-1c-1 0-4 2-5 2 0 1 0 1-1 1v-1c1-1 2-2 4-3 1 0 1-1 2-1z" class="D"></path><path d="M250 267c1 0 3 1 3 2l1 1-3 1v-1-2l-1-1z" class="P"></path><defs><linearGradient id="c" x1="507.218" y1="566.641" x2="505.529" y2="523.657" xlink:href="#B"><stop offset="0" stop-color="#c8c6c7"></stop><stop offset="1" stop-color="#fbfafa"></stop></linearGradient></defs><path fill="url(#c)" d="M498 498h0l1-1h1v1l1 1h2c2 1 4 3 6 5v-1-1s0-1-1-1v-1h2l2 1-2 1c0 1 1 2 2 3 2 1 5 2 8 1 2 0 3-2 4-3 1 0 1 1 1 1-1 3-2 5-4 7 4 0 8 1 11 3 2 2 3 4 5 5h-2-2c4 2 9 6 11 10v1h-1l2 3 2 4c1 1 1 2 2 3 1 2 1 4 1 5v2h0l-1-1v1l1 5c0 3 0 6-1 8v5c-1 2-2 5-3 7v-3c-1 1-3 2-4 3h-3c-1 1-2 1-3 1h0c-1 0-1-1-2 0-1 0-3 0-5-1 2 0 4 1 5 0-1-1-1-2-2-3-3 0-4-2-6-3h0 2 1 0v-1l1-1 1-1 1-1c0-1 0-1 1-1l2-2h0l2-2c1 1 1 2 1 3 1 1 1 2 1 3 1-1 1-2 2-3 0-2-1-4-1-7-1-1-1-3-1-5l-1-2-1-1h-2c-1 0-3 0-4-1h1l-1-1h1c-3-2-8-3-11-3h1l-1-1-1-2h1c-3-1-6 0-10 0l-1-1v1l-3 1h0c-2-1-3-1-4-1l-3-1c-2-1-4 0-7 1-1 1-3 2-4 3h-2l-1 1-5 2c-1 1-3 2-4 2l-13 14v-3h0-1c1-2 3-4 4-5l-1-1c1-1 2-3 3-4l-1-1c-1 1-3 4-5 5-1 0-2 1-3 2l1-2 7-10 2-4c1 0 1 0 2-1 1 0 3-4 3-5l4-5c1-2 2-3 3-4s2-2 2-3c3-5 4-9 8-13l1-2 1-1c1-1 2-3 4-4z"></path><path d="M481 540l1 1c0 1-1 1-1 2-1 1-3 2-4 2 1-2 3-3 4-5z" class="D"></path><path d="M472 541l1 1c-1 1-1 2-1 4-1 1-3 4-5 5l-1-1c1-1 2-3 3-4l3-5z" class="P"></path><path d="M545 564c1 0 1 0 1-1 1 1 1 1 1 2h0c0-1 0-2 1-3v3h-1c0 1 0 2-1 3v1c-1 1-3 2-4 3h-3c-1 1-2 1-3 1 1-1 2-2 4-2 2-2 4-4 5-7zm-58-32l1-1c2-1 4-3 6-4s7-4 10-5c1 1 1 1 2 1h4c-8 1-14 4-20 8-2 1-4 3-6 3l3-2z" class="F"></path><path d="M509 527c10-1 19-1 27 6 7 5 10 13 11 21v8h-1v-2-4c0-7-4-15-9-20-7-6-13-9-22-9-2 0-4 1-6 0h0 0z" class="c"></path><path d="M481 540c5-4 10-7 16-9 4-2 8-4 12-4h0 0c2 1 4 0 6 0l-3 1c-1 1-1 1-2 1 0 0 0 1-1 1-3 1-6 1-10 3 3 0 6 0 8-1h1l1 1 1 3v1l-3 1h0c-2-1-3-1-4-1l-3-1c-2-1-4 0-7 1-1 1-3 2-4 3h-2l-1 1-5 2c0-1 1-1 1-2l-1-1z" class="C"></path><path d="M499 533c3 0 6 0 8-1h1l1 1 1 3v1l-3 1h0c-2-1-3-1-4-1l-3-1c-2-1-4 0-7 1-1 1-3 2-4 3h-2-2s0 1-1 1l1-1c1-1 2-2 3-2 2-1 4-2 5-3 2-1 4-2 6-2z" class="R"></path><defs><linearGradient id="d" x1="531.181" y1="551.116" x2="523.275" y2="517.651" xlink:href="#B"><stop offset="0" stop-color="#949394"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#d)" d="M504 522c9-3 20-4 28 0 4 2 8 5 11 8l2 3 2 4c1 1 1 2 2 3 1 2 1 4 1 5v2h0l-1-1v1c-1-1-2-5-2-6-2-3-5-7-7-9-5-5-13-8-20-9h-10-4c-1 0-1 0-2-1z"></path><path d="M500 515h0l-2 2-3 3c-1 0-1 1-1 1-1 0-2 1-3 1l-3 3c-1 1-4 3-3 4-3 4-8 8-10 13-1 1-2 3-3 4 0-2 0-3 1-4l-1-1-3 5-1-1c-1 1-3 4-5 5-1 0-2 1-3 2l1-2 7-10 2-4c1 0 1 0 2-1 1 0 3-4 3-5l4-5c1-2 2-3 3-4s2-2 2-3v3l1-1c1-2 3-3 4-4h2v2c0-1 1-1 1-1l4-1c1-1 3-1 4-1z" class="U"></path><path d="M468 545c1-3 3-6 4-7h0c0 1-1 2-1 4h0 0l1-1c0-1 1-2 2-2l-2 2-3 5-1-1z" class="F"></path><path d="M475 530l4-5c1-2 2-3 3-4s2-2 2-3v3l-11 15c-2 1-3 3-5 4l2-4c1 0 1 0 2-1 1 0 3-4 3-5z" class="V"></path><path d="M468 540c2-1 3-3 5-4l-1 2c-1 1-3 4-4 7-1 1-3 4-5 5-1 0-2 1-3 2l1-2 7-10z" class="Q"></path><defs><linearGradient id="e" x1="478.244" y1="526.098" x2="483.078" y2="536.91" xlink:href="#B"><stop offset="0" stop-color="#9d989c"></stop><stop offset="1" stop-color="#b4b6b4"></stop></linearGradient></defs><path fill="url(#e)" d="M500 515h0l-2 2-3 3c-1 0-1 1-1 1-1 0-2 1-3 1l-3 3c-1 1-4 3-3 4-3 4-8 8-10 13-1 1-2 3-3 4 0-2 0-3 1-4l-1-1 2-2c5-7 10-14 17-20v-1c0-1 1-1 1-1l4-1c1-1 3-1 4-1z"></path><path d="M500 515h0l-2 2-1 1c-1 0-2 0-3 1-1 0-2 1-3 1h0v-1-1c0-1 1-1 1-1l4-1c1-1 3-1 4-1z" class="O"></path><path d="M503 509h1c1 2 1 4 1 6h5c4-1 9-1 13 0 2 0 3 0 4 1h1v1c2 1 4 2 5 2 4 2 9 6 11 10v1h-1c-3-3-7-6-11-8-8-4-19-3-28 0-3 1-8 4-10 5s-4 3-6 4l-1 1c-2 0-2 1-3 2-1 0-2 1-3 2l1 1-6 5c1-2 3-5 4-6 2-2 5-4 5-6v-1c-1-1 2-3 3-4l3-3c1 0 2-1 3-1 0 0 0-1 1-1l3-3 2-2h0v-1c1 1 1 1 2 0 1-2 2-2 1-4v-1z" class="R"></path><path d="M489 526h0c1-1 6-6 8-6h0c1 0 7-3 7-4l1-1h5c4-1 9-1 13 0 2 0 3 0 4 1h1v1c-2 0-4-1-7-1v-1c-4 0-13 0-16 3-3 1-5 2-8 4-3 1-5 3-8 4h0z" class="B"></path><path d="M503 509h1c1 2 1 4 1 6l-1 1c0 1-6 4-7 4h0c-2 0-7 5-8 6h0l-4 4v-1c-1-1 2-3 3-4l3-3c1 0 2-1 3-1 0 0 0-1 1-1l3-3 2-2h0v-1c1 1 1 1 2 0 1-2 2-2 1-4v-1z" class="Q"></path><path d="M498 498h0l1-1h1v1l1 1h2c2 1 4 3 6 5v-1-1s0-1-1-1v-1h2l2 1-2 1c0 1 1 2 2 3 2 1 5 2 8 1 2 0 3-2 4-3 1 0 1 1 1 1-1 3-2 5-4 7 4 0 8 1 11 3 2 2 3 4 5 5h-2-2c-1 0-3-1-5-2v-1h-1c-1-1-2-1-4-1-4-1-9-1-13 0h-5c0-2 0-4-1-6h-1v1c1 2 0 2-1 4-1 1-1 1-2 0v1c-1 0-3 0-4 1l-4 1s-1 0-1 1v-2h-2c-1 1-3 2-4 4l-1 1v-3c3-5 4-9 8-13l1-2 1-1c1-1 2-3 4-4z" class="U"></path><g class="B"><path d="M503 509c-1 0-2 0-3-1 1-1 1-1 2-1s2-1 3-2c2 2 4 2 6 2l-1 1h-1 3-7c0-1 0-1-1 0v1h-1z"></path><path d="M496 516c0-1 1-1 1-1v-4h-1v-1s0-1 1-1h1 0l-1 2c1 1 1 1 2 1 2 0 3-1 4-2 1 2 0 2-1 4-1 1-1 1-2 0v1c-1 0-3 0-4 1z"></path></g><path d="M485 520c1-1 1-3 1-4l3-3c1-3 2-6 5-8h0c-1 1-1 2-2 3v1l2-1v1 1h-2c0 1-1 2-1 3v1c1-1 2-2 3-2h0c0 1 0 1-1 2l-2 1v1h0l1 1s-1 0-1 1v-2h-2c-1 1-3 2-4 4z" class="I"></path><path d="M498 498h0l1-1h1v1l1 1c-2 2-6 4-7 6-3 2-4 5-5 8l-3 3c0 1 0 3-1 4l-1 1v-3c3-5 4-9 8-13l1-2 1-1c1-1 2-3 4-4z" class="D"></path><path d="M515 527c9 0 15 3 22 9 5 5 9 13 9 20v4l-1 4c-1 3-3 5-5 7-2 0-3 1-4 2h0c-1 0-1-1-2 0-1 0-3 0-5-1 2 0 4 1 5 0-1-1-1-2-2-3-3 0-4-2-6-3h0 2 1 0v-1l1-1 1-1 1-1c0-1 0-1 1-1l2-2h0l2-2c1 1 1 2 1 3 1 1 1 2 1 3 1-1 1-2 2-3 0-2-1-4-1-7-1-1-1-3-1-5l-1-2-1-1h-2c-1 0-3 0-4-1h1l-1-1h1c-3-2-8-3-11-3h1l-1-1-1-2h1c-3-1-6 0-10 0l-1-1-1-3-1-1h-1c-2 1-5 1-8 1 4-2 7-2 10-3 1 0 1-1 1-1 1 0 1 0 2-1l3-1z" class="U"></path><path d="M541 560h2c0 2-1 3-1 4-1 2-3 3-5 5v-2h-1-3 2c2-1 2-2 3-3l1-1c1-1 1-2 2-3h0z" class="L"></path><path d="M541 560h2c0 2-1 3-1 4h0-4l1-1c1-1 1-2 2-3h0z" class="c"></path><path d="M529 565l4 2h3 1v2c-2 1-3 0-5 0-3 0-4-2-6-3h0 2 1 0v-1z" class="C"></path><path d="M539 548c1 1 2 2 2 3h1c1 3 1 6 1 9h-2 0c0-2-1-4-1-7-1-1-1-3-1-5z" class="Z"></path><path d="M539 548c1 1 2 2 2 3v3c1 1 1 1 1 3-1 1-1 2-1 3h0c0-2-1-4-1-7-1-1-1-3-1-5z" class="S"></path><path d="M537 557c1 1 1 2 1 3 1 1 1 2 1 3l-1 1c-1 1-1 2-3 3h-2l-4-2 1-1 1-1 1-1c0-1 0-1 1-1l2-2h0l2-2z" class="p"></path><path d="M537 557c1 1 1 2 1 3l-7 3 1-1c0-1 0-1 1-1l2-2h0l2-2z" class="H"></path><path d="M509 533c1-2 2-2 5-3 6-1 13 3 19 7 5 3 7 8 9 14h-1c0-1-1-2-2-3l-1-2-1-1h-2c-1 0-3 0-4-1h1l-1-1h1c-3-2-8-3-11-3h1l-1-1-1-2h1c-3-1-6 0-10 0l-1-1-1-3z" class="r"></path><path d="M521 537c6 1 12 3 16 8h-2c-1 0-3 0-4-1h1l-1-1h1c-3-2-8-3-11-3h1l-1-1-1-2h1zM288 219v-1h0c1-2 1-1 2-1s1-1 2-1h2 0 1c1 0 1 1 2 1h1v-1c2 1 3 5 5 7v1l1 1h2 1v2c-1 0-2-1-3-1h-1l-2 2h-1-1s-1 1-1 2h0-1v1c0 1 4 3 5 4h1 0c0 1 1 1 2 1l-1 1c-1 0-5 0-7-1h0l-1 1 1 1h-1s-1 0-1 1c1 0 2 0 3 1h4 9c2 1 3 1 3 3l1 1 1 1v3 3h-1-3c-1-1-1-1-2-1l-2 1c2 0 2 0 3 1h1v1c0 1 0 2-1 2h-1c-1 2-3 3-4 5-1 0-2 1-3 1h-1v1 1c0 1 0 1-1 2h0-8v1h-6c-1 2-2 2-3 2 0 1 1 1 1 1v1c0 1 1 1 2 2h-4v1h-5l-2 1c-2 1-4 2-6 2 0-1-1-1-2-2l-2-1v-1h-2c-1 0-1 0-2-1v-1h1l-1-1c-1-1-1-1-1-2h-1 0l-1-1s-1-1-2-1h0-1-3c0-1 1-1 2-1v-1h-1-1l-1-1h2v-1h-2v-1h2l-1-1-1-1h1v-1h-4 0l1-1h1v-1h-2v-1h1l-1-1v-1l1-1 2-1h0c-1-1-2-1-3-1h0-2l1-2c-1-1 0-1 0-3 1-1 1 0 3-1v-1h-1l-1-1c1 0 1-1 2-1 1-1 2-2 3-2v-1h0-2c-1 1-2 1-4 1h-1c-1-1-2-1-3-2h2 1c1-1 2-1 2-3h0l2-2 1-1v-1c1-1 1-2 2-3s3-2 4-4h0v1c2 0 3-1 4-1l1-1c2 0 4-1 6 0h4l1 1 3-1c1 0 2-1 3-1h2v-1h3 3z" class="I"></path><path d="M253 259c2 0 2 0 3 1l-1 2c1 0 2-1 2 1v1 1h-1-3c0-1 1-1 2-1v-1h-1-1l-1-1h2v-1h-2v-1h2l-1-1zm-4-6v-1l1-1c1 1 2 2 3 2 0 1 0 1 1 2l-2 1h0c1 1 2 0 3 1-1 0-1 1-2 1v-1h-4 0l1-1h1v-1h-2v-1h1l-1-1z" class="H"></path><path d="M249 241c1 0 1-1 2-1 1-1 2-2 3-2 0 1 0 2-1 3l1 1h0-1v-2c-1 0-2 1-2 1 0 1 1 1 1 2 0 0 1 0 1-1h1c-1 1-2 2-4 2v1c0 1-1 2-1 3l-1 1h1 0-2l1-2c-1-1 0-1 0-3 1-1 1 0 3-1v-1h-1l-1-1zm17 7v-1c1-2 1-3 2-4 1 0 1 0 2-1l-1-1h1 2c1-1 3-1 5-1-2 2-4 1-6 2 0 1-1 2-1 2 0 1-1 2-1 2l-1 1c-1 1-2 1-2 1z" class="C"></path><path d="M272 267v-1h-4c-2-1-3-1-4-1 1-1 1-1 2-1 0-1-3-1-4-2 0-1 0-1 1-2h1c0 1 1 1 1 2h3l-1 1v1h2s0 1 1 1c1 1 3 1 4 1h0l-2 1h0z" class="H"></path><path d="M265 228c1-1 2-2 3-2h1v-1h-1l-1-1h3c1 1 2 0 2 1h1 2l1 1-2 2h0c-2-1-3-1-4-2h0-1l1 1h0-1c-1 1-2 1-4 1z" class="C"></path><path d="M272 225h1c0 1 1 1 1 2h0c-2 0-2 0-3-1h0l1-1z" class="I"></path><path d="M277 240c1 0 1-1 2 0l1 1h-3v1c-1 1-3 2-4 3h0c-1 2-1 2-3 3h-2v-1l1-1s1-1 1-2c0 0 1-1 1-2 2-1 4 0 6-2z" class="L"></path><path d="M266 248s1 0 2-1v1h2l-1 1 2 1v1h-1c-1 0-2 1-3 0-1 1-2 1-2 2h1v1h-1c-2 1-2 1-3 3l2 2c-1-1-2-1-3-1 0-1 0-2 1-3v-1c0-1 1-2 2-3v-2s1 0 2-1z" class="D"></path><path d="M268 248h2l-1 1 2 1v1h-1c-1 0-2 1-3 0v-1c-1 0-1 0-1-1l2-1z" class="G"></path><path d="M291 234c2 1 4 1 6 2l-1 1 1 1h-1s-1 0-1 1c1 0 2 0 3 1h4l-5 1h-1-6c-4-1-7 0-10 0l-1-1h4v-1h1 10c-2-1-3-2-4-3h0l1-1v-1h0z" class="L"></path><path d="M291 234c2 1 4 1 6 2l-1 1c-2-1-3 0-6-1l1-1v-1h0z" class="F"></path><path d="M279 240h4v-1h1c4 1 7 0 10 1l1-1h0c1 0 2 0 3 1h4l-5 1h-1-6c-4-1-7 0-10 0l-1-1z" class="P"></path><path d="M257 265h5 0v1l2 2c0 1 1 2 2 2h3c-1-1-2-1-2-1 0-1 0-1 1-1 0-1 1-1 2-1l1 1c0 1-1 1-1 2h0 3 1c-1 1-1 1-2 1l1 2h-2c-1 0-2 0-3 1l-2-1v-1h-2c-1 0-1 0-2-1v-1h1l-1-1c-1-1-1-1-1-2h-1 0l-1-1s-1-1-2-1z" class="C"></path><path d="M274 266h2c1 1 1 1 2 1h0v3h0 1l1 1h-1 0l-2 2h0 1l-2 1c-2 1-4 2-6 2 0-1-1-1-2-2 1-1 2-1 3-1h2l-1-2c1 0 1 0 2-1h-1-3 0c0-1 1-1 1-2l-1-1h2 0l2-1z" class="n"></path><path d="M276 274c-1 0-1-2-2-2l1-2 1 1h3l-2 2h0 1l-2 1z" class="Q"></path><g class="K"><path d="M274 266h2c1 1 1 1 2 1h0v3h0 1l1 1h-1 0-3c0-1 1-1 1-2h-1c-1 0-1-1-2-1-1-1-1 0-2-1l2-1z"></path><path d="M264 259l-2-2c1-2 1-2 3-3 0 1 0 2-1 3h2 1 3v1h-1l1 1v1c1 1 3 0 4 1v1h-2v1l2 1v1h3 5-1c0 1-1 1-2 1h-3-2 0c-1 0-3 0-4-1-1 0-1-1-1-1h-2v-1l1-1h-3c0-1-1-1-1-2l1-1h-1z"></path></g><path d="M268 262l1-1 1 1v2c3 2 6 0 9 2h-3-2 0c-1 0-3 0-4-1-1 0-1-1-1-1h-2v-1l1-1h0 0z" class="P"></path><path d="M264 259l-2-2c1-2 1-2 3-3 0 1 0 2-1 3h2 1v2 1h-1v1c1 1 2 1 2 1h0 0-3c0-1-1-1-1-2l1-1h-1z" class="L"></path><path d="M285 262h2l-1 1v1h1l6 1h0v1h-6c-1 2-2 2-3 2 0 1 1 1 1 1v1c0 1 1 1 2 2h-4v1h-5-1 0l2-2h0 1l-1-1h-1 0v-3h0c-1 0-1 0-2-1h3c1 0 2 0 2-1h1v-1-1c1 0 2 0 3-1z" class="N"></path><path d="M285 262h2l-1 1v1h1-5v-1c1 0 2 0 3-1z" class="f"></path><path d="M278 267h4v1c-1 0-2 1-2 1v1h2 0v2h1v1h-5-1 0l2-2h0 1l-1-1h-1 0v-3h0z" class="J"></path><path d="M270 224c3-2 4-1 7-1 2-1 2-1 3 0v2c-1 0-1 1-2 2l-2 2c-2 0-2 0-3 1-1 0-1 1-2 1v1c1 0 4 1 5 0h1 0 4c1-1 1-1 2-1l2-2c1 0 3 1 4 1h0c1 0 1 1 2 1s1 1 2 1v1c-1 1-1 1-2 1h0 0s-1 0-1-1c-1 0-1-1 0-1v-1h-1c0-1 0-1-1 0h-2l-2 2h-3s-1 0-2 1h-1c-1 0-1 1-2 1-2 0-4 0-6 1h0-1-2c-1 0-2 0-2-1h1 4 0 0 0c-2-1-4-1-6-2h4 3l-1-1-1-1h0c1 0 1 0 2-1h0l-1-1h1l1-1h1 1 0l2-2-1-1h-2-1c0-1-1 0-2-1z" class="D"></path><path d="M278 257h1 3 1 3c2 1 5 1 7 2 0 0 1 1 2 1h2c1 1 3 1 4 3v2h-8 0l-6-1h-1v-1l1-1h-2c-1 1-2 1-3 1v1 1h-5-3v-1l-2-1v-1h2v-1c-1-1-3 0-4-1v-1l-1-1h1c1-1 2-1 2-1h2 4z" class="M"></path><path d="M278 257h1 3v1l1 1h-3c-1 0 0-1-2-2z" class="o"></path><path d="M284 260c2 0 3 0 4 1-1 1-2 1-3 1-1 1-2 1-3 1v1 1h-5c0-1-1-1 0-2s2-1 2-1v-1h-2l1-1c2 1 4 1 6 1v-1z" class="b"></path><path d="M270 258c1-1 2-1 2-1h2 4c2 1 1 2 2 2-1 1-1 1-2 1h-1-3 0l1 1-1 1v-1c-1-1-3 0-4-1v-1l-1-1h1z" class="O"></path><path d="M286 257c2 1 5 1 7 2 0 0 1 1 2 1h2c1 1 3 1 4 3v2h-8 0l-6-1h-1v-1l1-1h-2c1 0 2 0 3-1-1-1-2-1-4-1h-1c1-1 1-1 2-1 1-1 1-1 1-2z" class="a"></path><path d="M287 262c3 1 8-1 11 1h0l1 1v1h-6l-6-1h-1v-1l1-1z" class="j"></path><path d="M286 257c2 1 5 1 7 2 0 0 1 1 2 1h2c-2 1-6 2-8 1h-1c-1-1-2-1-4-1h-1c1-1 1-1 2-1 1-1 1-1 1-2z" class="T"></path><path d="M288 219v-1h0c1-2 1-1 2-1s1-1 2-1h2 0 1c1 0 1 1 2 1h1v-1c2 1 3 5 5 7v1l1 1h2 1v2c-1 0-2-1-3-1h-1l-2 2h-1-1s-1 1-1 2h0-1v1c0 1 4 3 5 4h1 0c0 1 1 1 2 1l-1 1c-1 0-5 0-7-1h0c-2-1-4-1-6-2h0c1 0 1 0 2-1v-1c-1 0-1-1-2-1s-1-1-2-1h0c-1 0-3-1-4-1l-2 2c-1 0-1 0-2 1h-4 0-1c-1 1-4 0-5 0v-1c1 0 1-1 2-1 1-1 1-1 3-1l2-2c1-1 1-2 2-2v-2c-1-1-1-1-3 0-3 0-4-1-7 1h-3l1 1h1v1h-1c-1 0-2 1-3 2-1 0-2 1-3 1v2c-3 1-3 3-6 4 0 2 0 2-2 2h0-2c-1 1-2 1-4 1h-1c-1-1-2-1-3-2h2 1c1-1 2-1 2-3h0l2-2 1-1v-1c1-1 1-2 2-3s3-2 4-4h0v1c2 0 3-1 4-1l1-1c2 0 4-1 6 0h4l1 1 3-1c1 0 2-1 3-1h2v-1h3 3z" class="Q"></path><path d="M254 233c2-1 2-1 4-1-1 1-1 2-3 3-1-1-1-1-1-2z" class="V"></path><path d="M277 232c-1-1-2-1-3-2l1-1c1 0 2 1 3 1v1l-1 1h0z" class="S"></path><path d="M300 225l2-1h1l1 1s-1 0-1 1h-1l-1 1h-1 1l-1-2z" class="P"></path><path d="M296 228c0-1 1-1 1-2l1-1h2l1 2h-1l-1-1c-1 1-1 2-1 3-1 0-2 0-2-1z" class="S"></path><path d="M254 233v-1h1l-1-1v-1c0-1 1-2 2-2v1h2c1-1 1 0 2 0 0 0-1 0-1 1-1 1-1 1-1 2h0c-2 0-2 0-4 1z" class="o"></path><path d="M256 228v-1-1c2-2 5-2 8-2h1v1h0-3c0 1 1 1 2 1v1c-1 1-2 1-3 2h-1c-1 0-1-1-2 0h-2v-1z" class="M"></path><path d="M283 231c0-1-1-2-1-3h1c1 1 2 1 3 0h4c1 1 2 1 4 0v2c0 3 1 3 4 4 1 0 1 1 2 1h2 1 0c0 1 1 1 2 1l-1 1c-1 0-5 0-7-1h0c-2-1-4-1-6-2h0c1 0 1 0 2-1v-1c-1 0-1-1-2-1s-1-1-2-1h0c-1 0-3-1-4-1l-2 2z" class="n"></path><path d="M258 222v1c2 0 3-1 4-1l1-1c2 0 4-1 6 0h4c-1 0-3 0-5 1v2c-1 0-2 0-3-1v1h-1c-3 0-6 0-8 2v1 1c-1 0-2 1-2 2v1l1 1h-1v1c0 1 0 1 1 2-1 0-2 0-2 1h2l-1 1h0-2c-1 1-2 1-4 1h-1c-1-1-2-1-3-2h2 1c1-1 2-1 2-3h0l2-2 1-1v-1c1-1 1-2 2-3s3-2 4-4h0z" class="c"></path><path d="M247 238v-1h5c-1 1-2 1-4 1h-1z" class="K"></path><path d="M251 231c1 2 1 1 1 3 0 0-1 0-2 1 0 0 0 1-1 1h-3 1c1-1 2-1 2-3h0l2-2z" class="Q"></path><path d="M288 219v-1h0c1-2 1-1 2-1s1-1 2-1h2 0 1c1 0 1 1 2 1h1v-1c2 1 3 5 5 7v1h-1l-2 1h-2l-1 1c0 1-1 1-1 2-1 0-2 0-3-1h-5-1 0c-1-1-2 0-3 0h1v-2c0-1 2-2 2-3 0 0-1-1-2-1l-1 1h0c-1 0-1-1-2-1s-1 1-2 1-2-1-3-1c1 0 2-1 3-1h2v-1h3 3z" class="c"></path><path d="M288 219v-1h0c1-2 1-1 2-1s1-1 2-1h2 0 1c1 0 1 1 2 1h1c0 1 0 2 1 3l-1 3h-1s0-1-1-2v-1c0-2-1-2-2-3h-2s0 1-1 1h-2l1 1-1 1h-1v-1z" class="O"></path><path d="M298 217v-1c2 1 3 5 5 7v1h-1l-2 1h-2l-1 1c0 1-1 1-1 2-1 0-2 0-3-1-1 0-2-1-2-1h-1v-1c-1-1-1-2-1-2 0-1 1-1 1-1 1-1 1-1 2-1h1v-1h1l1 2v1l1 1 1-1h1l1-3c-1-1-1-2-1-3z" class="G"></path><path d="M294 225c0 1 0 1-1 1s-2 0-3-1l1-1h1v-1l1 2h1z" class="Z"></path><path d="M292 223h3l1 1v1c-1 1-1 0-2 0h-1l-1-2z" class="O"></path><path d="M302 240h9c2 1 3 1 3 3l1 1 1 1v3 3h-1-3c-1-1-1-1-2-1l-2 1c2 0 2 0 3 1h1v1c0 1 0 2-1 2h-1c-1 2-3 3-4 5-1 0-2 1-3 1h-1v1 1c0 1 0 1-1 2h0v-2c-1-2-3-2-4-3h-2c-1 0-2-1-2-1-2-1-5-1-7-2h-3-1-3-1-4-2s-1 0-2 1v-1h-3-1-2c1-1 1-2 1-3h1v-1h-1c0-1 1-1 2-2 1 1 2 0 3 0h1v-1l-2-1 1-1c2-1 2-1 3-3h0c1-1 3-2 4-3v-1h3c3 0 6-1 10 0h6 1l5-1z" class="M"></path><path d="M272 252v-2h3c1-1 3-1 4-1 0 1 0 1-1 2-2 1-4 1-6 1zm18-11h6 1v1s-1 0-1 1c-1 0-1 0-1 1l-2 1-2-2h-1v-1c-2 0-3 0-4 1-2 0-4 1-6 0v-1c2-1 3-1 4-1l1 1c2-2 3-2 5-1z" class="X"></path><path d="M296 241h1v1s-1 0-1 1c-1 0-1 0-1 1l-2 1-2-2h-1v-1h0c1-1 4-1 5-1h1z" class="g"></path><path d="M267 251c1 1 2 0 3 0l-1 1v1l-1 1h4 3 1c-2 2-4 2-6 3h-3-1-2c1-1 1-2 1-3h1v-1h-1c0-1 1-1 2-2z" class="J"></path><path d="M275 254h1c-2 2-4 2-6 3h-3-1l1-1c2 0 4 0 5-1v-1h3z" class="o"></path><path d="M281 248h-2l-1-1h8v-1h-3c1-1 2-1 3-2v-1h4 1c-1 0-2 1-3 1 1 2 3 4 4 5-1 0-1 1-2 1s-4-1-5-1-3 0-4-1z" class="Z"></path><path d="M281 248c1 1 3 1 4 1s4 1 5 1c3 1 6 3 10 4-1 1-1 2-3 3v-1l-1 2s0-1-1-1v1l-1 1 1 1c-1 0-2-1-2-1-2-1-5-1-7-2h-3-1-3-1-4-2s-1 0-2 1v-1c2-1 4-1 6-3h-1l-1-1c0-1-1-1-2-1 2 0 4 0 6-1 1-1 1-1 1-2 0 0 1 0 2-1z" class="h"></path><path d="M288 251c2 0 2 1 4 2-2 0-3 0-4-1v-1z" class="M"></path><path d="M283 256c3 0 5-1 7-3-1 0-4 1-5 1 0-1 2-1 3-2 1 1 2 1 4 1l1 2-1 1h-4v1h5v1 1c-2-1-5-1-7-2h-3v-1z" class="O"></path><path d="M288 251v1c-1 1-3 1-3 2 1 0 4-1 5-1-2 2-4 3-7 3-1 0-3 1-4 0v-1c1-2 0 0 2-1l1-2c2-1 4 0 5-1h0 1z" class="c"></path><path d="M276 254h2v-2h2c0-1 2-2 2-2l1 1h2 2 0c-1 1-3 0-5 1l-1 2c-2 1-1-1-2 1v1c1 1 3 0 4 0v1h-1-3-1-4-2s-1 0-2 1v-1c2-1 4-1 6-3z" class="J"></path><path d="M302 240h9c2 1 3 1 3 3l1 1 1 1v3 3h-1-3c-1-1-1-1-2-1l-2 1c2 0 2 0 3 1h1v1c0 1 0 2-1 2h-1c-1 2-3 3-4 5-1 0-2 1-3 1h-1v1 1c0 1 0 1-1 2h0v-2c-1-2-3-2-4-3h-2l-1-1 1-1v-1c1 0 1 1 1 1l1-2v1c2-1 2-2 3-3-4-1-7-3-10-4 1 0 1-1 2-1-1-1-3-3-4-5 1 0 2-1 3-1l2 2 2-1c0-1 0-1 1-1 0-1 1-1 1-1v-1l5-1z" class="F"></path><path d="M311 252h1v1c0 1 0 2-1 2h-1l-1 1c-1-1-2 0-3-1l1-1c1-1 3-1 4-2z" class="h"></path><path d="M295 244c0 2 1 4 3 5h5l3-1 1 1v2c-2 0-4 1-6 0h-1c-3 0-5-3-7-6l2-1z" class="T"></path><path d="M303 249l3-1 1 1v2c-2 0-4 1-6 0 0 0-1 0-1-1h0s2 0 3-1z" class="g"></path><path d="M300 254h7l-1 1c1 1 2 0 3 1l1-1c-1 2-3 3-4 5-1 0-2 1-3 1h-1v1 1c0 1 0 1-1 2h0v-2c-1-2-3-2-4-3h-2l-1-1 1-1v-1c1 0 1 1 1 1l1-2v1c2-1 2-2 3-3z" class="M"></path><path d="M309 256l1-1c-1 2-3 3-4 5-1 0-2 1-3 1h-1 0v-1h-1c-1-1-3-1-3-2h0 1c1 0 4 0 5-1 2-1 3-1 5-1z" class="J"></path><path d="M302 240h9c2 1 3 1 3 3l1 1 1 1v3 3h-1-3c-1-1-1-1-2-1l-2 1h-1v-2l-1-1-3 1h-5c-2-1-3-3-3-5 0-1 0-1 1-1 0-1 1-1 1-1v-1l5-1z" class="p"></path><path d="M307 249l2-3 1 1c0 1-1 1-1 3h1l-2 1h-1v-2z" class="Y"></path><path d="M304 243l1-1h1l2 2c0 2-1 3-2 4l-3 1h-5c-2-1-3-3-3-5 0-1 0-1 1-1 0-1 1-1 1-1 1 1 2 2 4 2l3-1z" class="n"></path><path d="M296 243c0-1 1-1 1-1 1 1 2 2 4 2l3-1c-1 2-2 3-4 4 0 0-1 0-1-1-1-1-2-1-2-3h-1z" class="Y"></path><path d="M317 240c2 1 2 2 3 3h0l1 1c0 1 1 2 1 3h0l2 2 1 1h0c1-1 2-1 2-1h0l1 1 1-1v-1h1 1 0l2 1c0 1 2 1 3 2 1 2 1 3 2 5h0l1 4c1 1 2 4 2 5l-1 1s0 1-1 1c0 1-2 2-3 3l1 3-2 2h-1c0 2 0 2 1 3-2 1-7 4-9 3h-1-2l-7 1h0c-1 1-3 0-4 0l-2 1h0v-1-1c-2 1-3 1-4 1-2 1-4 2-5 4h-1v1c-2 0-3 2-4 3l-3 6h-1 0c-2 2-3 4-4 6v1 1c-1 0-1 0-2-1v-2c0-1-1-1-2-2h-2-1v-1c-1 2-2 3-2 4h-1c-1 1 0 2-1 3l-1-1h1v-2c0-1 1-1 1-2l-2-1h-1l-1 3v2l-1 3c0 2-1 5-1 7h-1-2l1 1h1v1l-2 5h0c0-1-1-1-1-1h0v-1h-1l-2 2h-1v-1c0 1 0 2-1 3 0 1-1 1-2 3h-3 0-1s-1 0-1-1c-3 1-5 2-8 2h-1c-1 1-2 1-3 1h-1v-1h-2c-1 0-2 0-3-1l-3-3-1-1h-1v-1h-1 0c-1 0-1 1-2 1-1 1-4 1-6 1 1-1 2-1 3-2-1 0-1-1-2-1h0l2-1h0v1c1 1 4 0 5-1h-2 0-1v-1c1-2 5-3 8-4v-1c3-2 6-4 7-7l2-4c0-2 0-3-1-5h0v-5l1-9c0-3 1-5 2-8v-1c1-1 2-1 3-1 1-1 1 0 2 0h3l-1 3c1-1 1-1 2-1s2 0 3 1c-1 0-2 1-2 2h-1c0 1 0 1-1 2l4-3c0-1 1-1 2-2h1c1-1 1-2 1-3h0 2v1l2 1c1 1 2 1 2 2 2 0 4-1 6-2l2-1h5v-1h4c-1-1-2-1-2-2v-1s-1 0-1-1c1 0 2 0 3-2h6v-1h8 0c1-1 1-1 1-2v-1-1h1c1 0 2-1 3-1 1-2 3-3 4-5h1c1 0 1-1 1-2v-1h-1c-1-1-1-1-3-1l2-1c1 0 1 0 2 1h3 1v-3-3l-1-1c0-1 0-2 1-3l1-1z" class="E"></path><path d="M261 283l3 1c-1 1-1 1-1 2v1s1-1 2 0c1 0 1-1 2-1h4c-2 1-5 4-8 5-1 0-2 2-3 3v1c-1 2-2 4-2 6-1 1-1 4-1 5h0c-1 0-1 0-1-1l-1-1c-1-5 0-9 3-14 0-2 1-3 2-4 0-1 1-2 1-3z" class="I"></path><path d="M261 283l3 1c-1 1-1 1-1 2v1c-2 1-3 2-4 4 0 1 0 1-1 2h0c0-1 1-2 0-3 0-2 1-3 2-4 0-1 1-2 1-3z" class="P"></path><path d="M260 286c1 2-1 4-2 7h0c0-1 1-2 0-3 0-2 1-3 2-4z" class="S"></path><path d="M258 290c1 1 0 2 0 3 0 3-1 5-2 7 0 2 1 4 0 5l-1-1c-1-5 0-9 3-14z" class="G"></path><path d="M264 272h2v1l2 1c1 1 2 1 2 2 2 0 4-1 6-2l2-1h5 1c-1 0-1 0-2 1-2 1-3 1-5 2-1 1-2 1-3 2s-2 1-4 1c-1 0-1 1-2 1l-1 1-2 2c-1 0-1 1-1 1l-3-1c1 0 1-1 1-2-2 0-2 1-4 2l-2 3 3-5c-1-1-1 0-2 0-2 1-3 3-4 5s-1 4-2 6v-1c0-3 1-5 2-8h1l-1-1c0-2 2-4 3-5h1v1h0c0 1 0 1-1 2l4-3c0-1 1-1 2-2h1c1-1 1-2 1-3h0z" class="D"></path><path d="M278 273h5 1c-1 0-1 0-2 1-2 1-3 1-5 2s-4 1-6 1c-2 1-3 2-4 1l2-1 1-1c2 0 4-1 6-2l2-1z" class="I"></path><path d="M264 272h2v1l2 1c1 1 2 1 2 2l-1 1-2 1c-2 0-3 2-6 2 0-1 1-1 1-2h-1l-1-1c0-1 1-1 2-2h1c1-1 1-2 1-3h0z" class="V"></path><path d="M263 275c1-1 2-1 4 0-1 1-1 1-1 2h-1c-1-1-2-1-3-2h1z" class="K"></path><path d="M264 272h2v1l2 1c1 1 2 1 2 2l-1 1h0c-1-1-1-2-2-2-2-1-3-1-4 0 1-1 1-2 1-3h0z" class="Q"></path><path d="M302 261h1v1 5h2 0v1h0-1c-1 2-1 2-1 3-1 0-2 1-2 1h0c0 1-1 1-1 1 0 1 1 1 1 2l-1 1c-2 0-3 1-5 2l-3 1h-1-2l-8 3c-1 0-2 1-3 1s-3 1-5 1c-1 1-2 1-2 2h-4c-1 0-1 1-2 1-1-1-2 0-2 0v-1c0-1 0-1 1-2 0 0 0-1 1-1l2-2 1-1c1 0 1-1 2-1 2 0 3 0 4-1s2-1 3-2c2-1 3-1 5-2 1-1 1-1 2-1h-1v-1h4c-1-1-2-1-2-2v-1s-1 0-1-1c1 0 2 0 3-2h6v-1h8 0c1-1 1-1 1-2v-1-1z" class="C"></path><path d="M301 269l3-1c-1 2-1 2-1 3-1 0-2 1-2 1-2-1-3-1-4-1v-1l4-1z" class="U"></path><path d="M299 273v1c-1 1-3 1-4 1-2 1-4 2-6 2h0-2c0 1 0 0-1 0 1 0 2-1 2-1 3-2 8-1 11-3z" class="h"></path><defs><linearGradient id="f" x1="284.381" y1="279.266" x2="278.16" y2="278.534" xlink:href="#B"><stop offset="0" stop-color="#737074"></stop><stop offset="1" stop-color="#8a8987"></stop></linearGradient></defs><path fill="url(#f)" d="M280 278c3-1 5-2 8-2 0 0-1 1-2 1 1 0 1 1 1 0h2l-11 5-1-1v-1l3-2z"></path><path d="M302 261h1v1 5h2 0v1h0-1l-3 1-4 1-10 2c-1-1-2-1-2-2v-1s-1 0-1-1c1 0 2 0 3-2h6v-1h8 0c1-1 1-1 1-2v-1-1z" class="e"></path><path d="M302 261h1v1 5h2 0v1h0-1l-3 1v-1h0v-1l-8-1v-1h8 0c1-1 1-1 1-2v-1-1z" class="c"></path><path d="M284 273h0 2c1 0 3-1 4-1h4 3 1l-1 1h2c-3 2-8 1-11 3-3 0-5 1-8 2l-3 2v1l1 1-3 1h-1c-1 1-1 1-2 1 0 0 0-1-1-1l-3 2h-2-1c0 1-1 1-2 1h0c0-1 0-1 1-2 0 0 0-1 1-1l2-2 1-1c1 0 1-1 2-1 2 0 3 0 4-1s2-1 3-2c2-1 3-1 5-2 1-1 1-1 2-1z" class="L"></path><path d="M274 283l-1-1h-1c-3 0-4 1-6 1l4-4c2 0 5 1 7 0 1-1 2-1 3-1l-3 2v1l1 1-3 1h-1z" class="V"></path><path d="M275 283l-1-1c1 0 1 0 1-1 1-1 1-1 2-1v1l1 1-3 1z" class="K"></path><path d="M248 274c1-1 2-1 3-1 1-1 1 0 2 0h3l-1 3c1-1 1-1 2-1s2 0 3 1c-1 0-2 1-2 2h-1 0v-1h-1c-1 1-3 3-3 5l1 1h-1c-1 3-2 5-2 8v1c1 2 0 4 0 7l-1-2h0v-3-1-3c0 2-1 3-1 4v2l-2 1v1c1 2 0 4 0 6 1 2 0 4 0 6l3-5v1c0 1-1 2 0 3h1l1-1h0c0 1 0 2-1 3-1 4-2 7-6 10h-1c-2 1-5 1-7 0h-2l-1 1h-1v-1h-1 0c-1 0-1 1-2 1-1 1-4 1-6 1 1-1 2-1 3-2-1 0-1-1-2-1h0l2-1h0v1c1 1 4 0 5-1h-2 0-1v-1c1-2 5-3 8-4v-1c3-2 6-4 7-7l2-4c0-2 0-3-1-5h0v-5l1-9c0-3 1-5 2-8v-1z" class="I"></path><path d="M244 306c0 3-1 4-4 7-1 1-2 1-3 1v-1c3-2 6-4 7-7z" class="L"></path><path d="M250 275c1-1 2-1 3-1 0 1 0 1-1 1l1 1h1c-2 2-3 4-5 6v-2c0-2 3-3 3-5h-2z" class="H"></path><path d="M248 274c1-1 2-1 3-1 1-1 1 0 2 0h3l-1 3h-1-1l-1-1c1 0 1 0 1-1-1 0-2 0-3 1-2 2-3 6-4 8 0-3 1-5 2-8v-1z" class="P"></path><defs><linearGradient id="g" x1="254.723" y1="275.748" x2="251.002" y2="284.092" xlink:href="#B"><stop offset="0" stop-color="#969596"></stop><stop offset="1" stop-color="#b2b0b1"></stop></linearGradient></defs><path fill="url(#g)" d="M255 276c1-1 1-1 2-1s2 0 3 1c-1 0-2 1-2 2h-1 0v-1h-1c-1 1-3 3-3 5l-2 2c0 1-1 2-2 2-1 2 0 2-2 3 0-2 1-3 1-4s1-2 1-3c2-2 3-4 5-6h1z"></path><path d="M253 282l1 1h-1c-1 3-2 5-2 8v1c1 2 0 4 0 7l-1-2h0v-3-1-3c0 2-1 3-1 4v2l-2 1v1l-1-1v-7h0l1 3v-4c2-1 1-1 2-3 1 0 2-1 2-2l2-2z" class="B"></path><path d="M251 284c-1 4-2 7-3 10v1l-1-2v-4c2-1 1-1 2-3 1 0 2-1 2-2z" class="C"></path><path d="M243 314h0l4-4h1s-1 1-1 2h0l1-1 1 1v1l1-1c0-1 0-1 1-2v1c-1 4-2 7-6 10h-1c-2 1-5 1-7 0h-2l-1 1h-1v-1h-1 0c-1 0-1 1-2 1-1 1-4 1-6 1 1-1 2-1 3-2-1 0-1-1-2-1h0l2-1h0v1c1 1 4 0 5-1 4-1 7-3 11-5z" class="D"></path><path d="M243 314c-1 3-7 6-10 7h0-1 0c-1 0-1 1-2 1-1 1-4 1-6 1 1-1 2-1 3-2-1 0-1-1-2-1h0l2-1h0v1c1 1 4 0 5-1 4-1 7-3 11-5z" class="a"></path><path d="M271 286c0-1 1-1 2-2 2 0 4-1 5-1-1 0-2 1-3 1 1 0 1 1 2 1 1-1 2-1 3-2l-1 1c-2 1-4 3-5 4 0 1-2 2-2 3v2h0c-1 3-3 5-4 8v3 3l1-1v1c0 3 0 4 1 6l1 1h-2l1 1h1v1l-2 5h0c0-1-1-1-1-1h0v-1h-1l-2 2h-1v-1c0 1 0 2-1 3 0 1-1 1-2 3h-3 0-1s-1 0-1-1c-3 1-5 2-8 2h-1c-1 1-2 1-3 1h-1v-1h-2c-1 0-2 0-3-1l-3-3-1-1 1-1h2c2 1 5 1 7 0-1 1-1 1-2 1l2 1h1c4-1 6-3 8-7h1c2-3 1-6 3-8 1-1 0-5 1-6v-1c0-2 1-4 2-6v-1c1-1 2-3 3-3 3-1 6-4 8-5z" class="U"></path><path d="M250 321c2-1 3-3 4-5v2c0 2-1 3-2 4s-2 2-3 2c1-1 1-1 1-3z" class="B"></path><path d="M261 316l1 1c1 1 2 2 2 3s0 2-1 3c0 1-1 1-2 3h-3 0-1s-1 0-1-1c1-1 4-3 4-5h0c1-1 1-3 1-4z" class="R"></path><path d="M261 316l1 1c-1 1 0 3-1 5 0 1-2 2-2 3 0 0 1 0 2 1h-3 0-1s-1 0-1-1c1-1 4-3 4-5h0c1-1 1-3 1-4zm8-2h-2c-1-1-1-2-2-4h0c0-2-1-4 0-6l1-4c0-1 0-2 1-3h0v2 5-1-1l1-1v3 3l1-1v1c0 3 0 4 1 6l1 1h-2 0z" class="B"></path><path d="M268 304v3l1-1v1c0 3 0 4 1 6l1 1h-2 0c-1-1-1-2-1-3-1-2-1-5 0-7z" class="M"></path><path d="M244 323h1c4-1 6-3 8-7h1 0c-1 2-2 4-4 5 0 2 0 2-1 3-2 1-3 2-5 2h0c1 1 2 1 3 1-1 1-2 1-3 1h-1v-1h-2c-1 0-2 0-3-1l-3-3-1-1 1-1h2c2 1 5 1 7 0-1 1-1 1-2 1l2 1z" class="I"></path><path d="M244 323h1c4-1 6-3 8-7h1 0c-1 2-2 4-4 5-3 3-6 5-10 4-1 0-2-1-3-2h0 1 6z" class="c"></path><path d="M260 306c1-4 2-8 5-11h0 0c-2 4-5 11-3 16 0 2 1 4 3 5 1 0 4 0 5-1h1v1l-2 5h0c0-1-1-1-1-1h0v-1h-1l-2 2h-1v-1c0-1-1-2-2-3l-1-1c-1-2-2-3-2-5l1-5z" class="n"></path><path d="M261 316c-1-2-2-3-2-5l1-5c0 3 0 5 1 7s3 4 5 5v1h1c0-1 1-1 2-1h0l-1 2h0v-1h-1l-2 2h-1v-1c0-1-1-2-2-3l-1-1z" class="F"></path><path d="M300 276l1 1c3 1 6 0 9 0h1 3 2 1c1 1 1 1 2 1h0c2-1 5-1 7 0 0 0-1 1-2 1l-1 2-7 1h0c-1 1-3 0-4 0l-2 1h0v-1-1c-2 1-3 1-4 1-2 1-4 2-5 4h-1v1c-2 0-3 2-4 3l-3 6h-1 0c-2 2-3 4-4 6v1 1c-1 0-1 0-2-1v-2c0-1-1-1-2-2h-2-1v-1c-1 2-2 3-2 4h-1c-1 1 0 2-1 3l-1-1h1v-2c0-1 1-1 1-2l-2-1h-1l-1 3v2l-1 3c0 2-1 5-1 7h-1l-1-1c-1-2-1-3-1-6v-1l-1 1v-3-3c1-3 3-5 4-8h0v-2c0-1 2-2 2-3 1-1 3-3 5-4l1-1c-1 1-2 1-3 2-1 0-1-1-2-1 1 0 2-1 3-1s2-1 3-1l8-3h2 1l3-1c2-1 3-2 5-2z" class="S"></path><path d="M285 285c1-1 2-1 3-1 2-1 3-2 5-3h4v2c-1 1-4 3-5 4v-2-1h-3l-2 2-2-1z" class="J"></path><path d="M300 276l1 1c3 1 6 0 9 0h1 0c-1 1-1 1-2 1h-1-2c-1 1-2 1-3 2v1 1-1h1 0 6c-2 1-3 1-4 1-2 1-4 2-5 4l-1-1c1-1 3-2 3-3h0c-2 0-2 0-3-1h0l-1-1c0-1 1-1 1-2-5 0-8 2-12 3-2 1-4 2-6 2 0 1-1 1-2 2-1 0-2 1-3 2s-1 1-1 2l-4 4h0v-2c0-1 2-2 2-3 1-1 3-3 5-4l1-1c-1 1-2 1-3 2-1 0-1-1-2-1 1 0 2-1 3-1s2-1 3-1c3-1 5-2 8-3h2 1l3-1c2-1 3-2 5-2z" class="I"></path><path d="M300 278c1 0 1 0 2 1 0 1-1 2-2 2h0l-1-1c0-1 1-1 1-2z" class="L"></path><path d="M311 277h3 2 1c1 1 1 1 2 1h0c2-1 5-1 7 0 0 0-1 1-2 1l-1 2-7 1h0c-1 1-3 0-4 0l-2 1h0v-1-1h-6 0-1v1-1-1c1-1 2-1 3-2h2 1c1 0 1 0 2-1h0z" class="U"></path><path d="M312 282v-1c1-1 3-2 4-2v1l1 1-1 1h0c-1 1-3 0-4 0z" class="I"></path><path d="M317 281c3-1 5-2 7-2l-1 2-7 1 1-1z" class="C"></path><defs><linearGradient id="h" x1="270.199" y1="294.506" x2="274.466" y2="300.355" xlink:href="#B"><stop offset="0" stop-color="#383536"></stop><stop offset="1" stop-color="#646666"></stop></linearGradient></defs><path fill="url(#h)" d="M287 286l2-2h3v1 2c-2 1-3 2-5 3 0 1-1 2-2 2h-3 0c-1 1-2 1-3 2h-1c-1 0-2 2-2 3l-1 2h0l-1 3v2l-1 3c0 2-1 5-1 7h-1l-1-1c-1-2-1-3-1-6v-1l-1 1v-3-3c1-3 3-5 4-8l4-4 2-1c1-2 5-2 7-3h0l2 1z"></path><path d="M273 298c0-2 3-5 4-7l-3 7 1 1h0l-1 3h-1l1-3-1-1z" class="M"></path><path d="M285 285l2 1c-1 0-2 0-4 1v2-1h-1c0 1-1 2-2 2-1 1-1 1-2 0l1-1h-1v-1c1-2 5-2 7-3h0z" class="Y"></path><path d="M287 286l2-2h3v1 2c-2 1-3 2-5 3h-2v-2c-1 1-1 1-2 1v-2c2-1 3-1 4-1z" class="g"></path><defs><linearGradient id="i" x1="268.927" y1="302.457" x2="273.773" y2="311.324" xlink:href="#B"><stop offset="0" stop-color="#8b898a"></stop><stop offset="1" stop-color="#a7a7a7"></stop></linearGradient></defs><path fill="url(#i)" d="M273 298l1 1-1 3h1v2l-1 3c0 2-1 5-1 7h-1l-1-1c-1-2-1-3-1-6h0c1-2 1-4 2-7l2-2z"></path><path d="M273 298l1 1-1 3h1v2l-1 3c-1-2 0-4 0-6l-2-1 2-2z" class="Z"></path><path d="M300 281c1 1 1 1 3 1h0c0 1-2 2-3 3l1 1h-1v1c-2 0-3 2-4 3l-3 6h-1 0c-2 2-3 4-4 6v1 1c-1 0-1 0-2-1v-2c0-1-1-1-2-2h-2-1v-1c-1 2-2 3-2 4h-1c-1 1 0 2-1 3l-1-1h1v-2c0-1 1-1 1-2l-2-1h-1 0l1-2c0-1 1-3 2-3h1c1-1 2-1 3-2h0 3c1 0 2-1 2-2 2-1 3-2 5-3 1-1 4-3 5-4 1 0 2-1 3-2h0z" class="d"></path><path d="M300 281c1 1 1 1 3 1l-5 3-6 3v-1c1-1 4-3 5-4 1 0 2-1 3-2h0z" class="C"></path><path d="M287 292v2c2 1 5-4 7-5h1l-3 6h-2s-2 2-2 3c-1-1 1-2 0-3l-2 1s0-2-1-3l1-1h1z" class="M"></path><path d="M303 282h0c0 1-2 2-3 3l1 1h-1v1c-2 0-3 2-4 3l-3 6h-1 0c-2 2-3 4-4 6-1 0-1 0-1-1v-1h1v-2c0-1 2-3 2-3h2l3-6c2-1 3-2 4-4h-1l5-3z" class="c"></path><path d="M287 290c2-1 3-2 5-3v1l-5 4h-1l-1 1c1 1 1 3 1 3l2-1c1 1-1 2 0 3v2h-1v1c0 1 0 1 1 1v1 1c-1 0-1 0-2-1v-2c0-1-1-1-2-2h-2-1v-1c-1 2-2 3-2 4h-1c-1 1 0 2-1 3l-1-1h1v-2c0-1 1-1 1-2l-2-1h-1 0l1-2c0-1 1-3 2-3h1c1-1 2-1 3-2h0 3c1 0 2-1 2-2z" class="G"></path><path d="M278 300l1-3c1-1 2-1 3-2s2-2 4-3l-1 1-2 3s-1 1-1 2c1 0 2 0 2 1h-2-1v-1c-1 2-2 3-2 4h-1c-1 1 0 2-1 3l-1-1h1v-2c0-1 1-1 1-2z" class="L"></path><path d="M283 296l2-3c1 1 1 3 1 3l2-1c1 1-1 2 0 3v2h-1v1c0 1 0 1 1 1v1 1c-1 0-1 0-2-1v-2c0-1-1-1-2-2 0-1-1-1-2-1 0-1 1-2 1-2z" class="G"></path><path d="M283 296l1 2c2 0 2 0 3-1v3 1c0 1 0 1 1 1v1 1c-1 0-1 0-2-1v-2c0-1-1-1-2-2 0-1-1-1-2-1 0-1 1-2 1-2z" class="P"></path><path d="M317 240c2 1 2 2 3 3h0l1 1c0 1 1 2 1 3h0l2 2 1 1h0c1-1 2-1 2-1h0l1 1 1-1v-1h1 1 0l2 1c0 1 2 1 3 2 1 2 1 3 2 5h0l1 4c1 1 2 4 2 5l-1 1s0 1-1 1c0 1-2 2-3 3l1 3-2 2h-1c0 2 0 2 1 3-2 1-7 4-9 3h-1-2l1-2c1 0 2-1 2-1-2-1-5-1-7 0h0c-1 0-1 0-2-1h-1-2-3-1c-3 0-6 1-9 0l-1-1 1-1c0-1-1-1-1-2 0 0 1 0 1-1h0s1-1 2-1c0-1 0-1 1-3h1 0v-1h0-2v-5-1c1 0 2-1 3-1 1-2 3-3 4-5h1c1 0 1-1 1-2v-1h-1c-1-1-1-1-3-1l2-1c1 0 1 0 2 1h3 1v-3-3l-1-1c0-1 0-2 1-3l1-1z" class="R"></path><path d="M308 263c2 1 3 0 5 0l-3 3-1 2-2-1c1-1 1-3 1-4h0z" class="E"></path><path d="M303 271l4 1 1 1h-1-1s-1 0-2 1h0-1-1l-1 1c0-1-1-1-1-2 0 0 1 0 1-1h0s1-1 2-1z" class="B"></path><path d="M322 247h0l2 2 1 1h0c1-1 2-1 2-1h0l1 1 1-1v-1h1 1 0l2 1c0 1 2 1 3 2 1 2 1 3 2 5h0l1 4c1 1 2 4 2 5l-1 1h0v-2l-1-1c-2-3-1-5-2-8l-1-3-4-2h-2c0 1-1 2-2 3-2 3-4 6-7 6h-1c-1 1-2 2-4 2h0c-1 1-2 2-3 2h0c-2 0-3 1-5 0 1-1 2-1 3-2 2-1 6-3 8-3h1l3-3-1-1c1 0 1 0 2-1v-3c-1-1-2-2-2-3z" class="I"></path><path d="M333 275c-1-1-1-3-2-4 0 0-1 0-1-1-2 0-4-1-5-3v-1c1 0 1 1 2 2 1 0 1-1 2-1 4-1 8-1 11-1h0s0 1-1 1c0 1-2 2-3 3l1 3-2 2h-1c0 2 0 2 1 3-2 1-7 4-9 3h-1-2l1-2c1 0 2-1 2-1 1-1 3-1 4-2h1c1 0 1-1 2-1z" class="T"></path><path d="M333 275c0 1 0 1-1 2-1 2-4 3-7 4h-2l1-2c1 0 2-1 2-1 1-1 3-1 4-2h1c1 0 1-1 2-1z" class="H"></path><path d="M340 266h0s0 1-1 1c0 1-2 2-3 3l-1 1c-1 1-1 2-1 3h-1v-3h1l-3-3-1 1c-1 0-2-1-3-1 1 0 1-1 2-1 4-1 8-1 11-1z" class="L"></path><path d="M317 240c2 1 2 2 3 3h0l1 1c0 1 1 2 1 3s1 2 2 3v3c-1 1-1 1-2 1l1 1-3 3h-1c-2 0-6 2-8 3-1 1-2 1-3 2h0c0 1 0 3-1 4l2 1h-4 0v-1h0-2v-5-1c1 0 2-1 3-1 1-2 3-3 4-5h1c1 0 1-1 1-2v-1h-1c-1-1-1-1-3-1l2-1c1 0 1 0 2 1h3 1v-3-3l-1-1c0-1 0-2 1-3l1-1z" class="Q"></path><path d="M310 250c1 0 1 0 2 1h3c1 1 1 2 1 4h0v1h0c-1 1 0 0-1 0h-3v-1h1v-1l-1-1v-1h-1c-1-1-1-1-3-1l2-1z" class="D"></path><path d="M305 268h1c-1-1-2-2-2-3v-1-2c2-1 3-1 5-1h0 2 0c-1 1-2 1-3 2h0c0 1 0 3-1 4l2 1h-4 0z" class="H"></path><path d="M307 267h0c0-1-1-2-1-2-1-1-1-2-1-3h1l2 1c0 1 0 3-1 4z" class="I"></path><path d="M317 240c2 1 2 2 3 3h0l1 1c0 1 1 2 1 3s1 2 2 3v3c-1 1-1 1-2 1h-1l-2 3c-1 0-2 0-2-1-1 0-1-1-1-1h0c0-2 0-3-1-4h1v-3-3l-1-1c0-1 0-2 1-3l1-1z" class="R"></path><path d="M317 240c2 1 2 2 3 3h0l1 1h-1c-1 0-2 0-2-1h-1c-1 1-1 1-1 2l-1-1c0-1 0-2 1-3l1-1zm-1 8c1 1 2 2 2 3 1 0 2 1 3 1s2 0 2-1l1-1v3c-1 1-1 1-2 1h-1l-2-1c-2-1-2-2-3-5z" class="B"></path><path d="M316 248h0 0c1 3 1 4 3 5l2 1-2 3c-1 0-2 0-2-1-1 0-1-1-1-1h0c0-2 0-3-1-4h1v-3z" class="M"></path><path d="M339 279h3c1-1 0-2 2-1h1l1 2c2 3 3 7 4 10 1 2 2 4 2 7 0 0 0 2 1 3v3h-1-1c0 1 1 2 1 3h-1v3c1 1 1 3 2 4s1 2 1 3c2 1 3 2 5 2l2 1 1 1 1 1 2-1h1l1 1h0c1-1 3-2 4-3h1v1c0 1 1 2 2 2h0l1 1h0c-1 1-1 0-2 0l1 2c2 1 3 3 4 5h0c1 0 1 1 1 1h1c1 0 1-1 1-2v2l1 1v2c-1 2 1 4 0 5 0 4-2 5-4 8h0c-1 1-2 2-4 3-1 0-1 1-1 2l-2 1c-1 2-2 4-2 6-1 1-3 3-3 4-1 2-2 4-3 7 0 2 0 4 1 7 1 0 1 0 1-1h1c0 1 1 3 1 4v1h0v3c1 1 1 2 1 4l-1 2h-1c0 1-1 2-1 2v2 1c-1 0-4 1-5 3 1 0 6-2 7-3v1l-2 1c-2 1-5 2-7 2h0c-1 0-2 0-3 1 0 0 0 1-1 1h0c-1 1-2 1-4 1-3 1-7 1-10 3-6 2-12 4-18 7h-1-2l-3 2c-1 1-2 1-3 1h0-2l1-1v-1c-2 0-4 1-6 1h0l-2 1c-2 1-5 2-8 3l-1 2-3 1-1-1c-1 1-1 2-1 3 0 0 1 0 1 1-1 0-1 1-1 1v1h0c1 1 1 2 1 3v1h1 1v1c1 0 1 0 2-1v2c-2 1-4 4-7 5l-1 1-4 2c0 1-1 1-1 2 1 1 3 0 3 1-2 1-4 3-6 5l-7 3-6 3-2-2v-1c1 0 2 0 2-1h0c-1 0-1-1-2-1 0 1-1 0-2 0s-1 0-1-1c-2 0-3 0-4-1h-2 0l1 2v1c-1-2-3-3-3-5h-1v-4h0c0-1 0-1 1-2l1-2c1-1 1-1 1-2h0c1-2 3-3 5-3-1-1-3-1-5-2h-2 0-2s-1-1-2-1l-3-1h1 1 3l-1-1v-1c0-1-1-2-2-2s-3-3-4-4l1-1c-1-2-1-4-1-6 1-2 1-3 3-5h0l2-2 1-1c-1-1-2-2-2-3h-1l-1 1-1-1-1 1v-2-1h0c1 0 2-2 3-2h0v-2-1c-1-3 0-5 0-8h-1c0-1 1-2 0-3l2-2 7-4 3-4 1-1v-1c2-2 2-4 2-6v-1l1-1-1-2c-2-2-2-6-1-9 0-3 2-7 3-10h0l-3 2v-2h-1c1-1 1-1 1-2h1c0-1 0-2 1-3l6-3c3-3 4-4 6-8 0-1 0-2-1-3 1-1 2-2 2-3v-1c1-2 1-3 1-5l1-7c1-1 1-2 1-3 0 0 0-1 1-2 0-1 1-2 2-4v1h1 2c1 1 2 1 2 2v2c1 1 1 1 2 1v-1-1c1-2 2-4 4-6h0 1l3-6c1-1 2-3 4-3v-1h1c1-2 3-3 5-4 1 0 2 0 4-1v1 1h0l2-1c1 0 3 1 4 0h0l7-1h2 1c2 1 7-2 9-3 2 0 2 0 4 1z" class="R"></path><path d="M289 308c1 1 2 1 3 2h0l-1 4c-1-2-1-4-2-6z" class="I"></path><path d="M312 331c-1-1-1-2-1-4 1 2 2 4 4 5-1 1-1 1-1 2h-1c-1-1-1-2-1-3z" class="b"></path><path d="M300 314l2-2c2 1 2 1 2 3h-1l-1 4c-1-2-1-3-2-5z" class="B"></path><path d="M304 315h1c0-1 0-2 1-3 1 0 2 0 2-1h0c1-1 2 0 3 0l1 5v-1c-1 0-1-1-1-2-1-1-1-1-2-1h0l-2 2v2h-1v-1c-1 0-1 1-1 1-1 0-1 0-1-1z" class="E"></path><path d="M291 332v-1c1-2 3-4 4-7h1v1 3l-1 1h0c-1 1-1 3-3 4 0-1 0-1-1-1z" class="B"></path><path d="M275 337v1l-2 2c-2 2-4 4-5 7l-1 1v-2l-1-1c2-4 5-6 9-8z" class="N"></path><path d="M277 317c1 1 1 1 1 2 0 2 0 3-1 5l2-4 1-1h0c-1 5-1 9-5 13 0-1 0-2 1-3 0-2 1-4 0-6l1-6z" class="S"></path><path d="M260 352v5c0 1 1 1 1 2v1s0 1 1 1v-3c-1-1-1-2 0-3v-1c0-1 0-1 1-2h0c-1 4 0 7 2 12l1 2v1c-2-1-3-3-4-4l-1-2c-2-2-2-6-1-9z" class="H"></path><path d="M266 345l1 1v2l-1 4v3h0c-1-1-1-2-1-3-1 1-1 2-1 4v3c1 1 1 2 1 2v3c-2-5-3-8-2-12h0l3-7z" class="b"></path><path d="M276 323c1 2 0 4 0 6-1 1-1 2-1 3-1 0-2 1-3 2-1 0-1 0-2 1h-2v-1c3-3 4-4 6-8l2-3z" class="O"></path><path d="M275 337c1-1 2-2 3-2h0c2 0 5-4 6-5s1-4 2-4v3c-1 3-3 7-6 9-2 0-4 1-6 2h-1 0l2-2v-1z" class="K"></path><path d="M279 312v-3c0-1 1-2 1-3l1-1c1 2 0 4 0 5h1c0 1-1 7-2 9h0l-1 1-2 4c1-2 1-3 1-5 0-1 0-1-1-2 0-1 1-4 1-5h1z" class="B"></path><path d="M277 317c0-1 1-4 1-5h1c0 2 0 5-1 7 0-1 0-1-1-2z" class="H"></path><path d="M312 331c0 1 0 2 1 3h1c0-1 0-1 1-2l1 2c1 0 2 1 2 2v1l-1-2h-1v6l-3 4 1-3h-1 0c-1 0-1 0-2-1h0c1-3 1-7 1-10z" class="O"></path><path d="M316 334c1 0 2 1 2 2v1l-1-2h-1v6l-3 4 1-3c0-2 1-4 1-7h0v-1h0 1z" class="D"></path><path d="M291 332c1 0 1 0 1 1 2-1 2-3 3-4h0c0 3-1 6-3 8h0-1l-8 5-1-1h0-2c-1 1-4 2-6 3h-1c3-3 8-3 11-5 0-1 1-1 1-1 3-1 5-4 6-6z" class="L"></path><path d="M262 337l6-3v1h2c1-1 1-1 2-1l-1 1-5 5c-1 1-2 2-3 2h0l-3 2v-2h-1c1-1 1-1 1-2h1c0-1 0-2 1-3z" class="c"></path><path d="M260 340l4-2 1 1-2 2v1l-3 2v-2h-1c1-1 1-1 1-2z" class="F"></path><path d="M264 338c3-1 5-3 7-3l-5 5c-1 1-2 2-3 2h0v-1l2-2-1-1z" class="H"></path><path d="M286 329c0 4 0 6-2 9-1 0-1 0 0 1-3 2-8 2-11 5h0l-1-1c-1 1-3 2-3 3-1 0-1 0-1 1 1-3 3-5 5-7h0 1c2-1 4-2 6-2 3-2 5-6 6-9z" class="B"></path><path d="M273 340h0 1c2-1 4-2 6-2-2 1-3 1-5 2l-3 3c-1 1-3 2-3 3-1 0-1 0-1 1 1-3 3-5 5-7z" class="H"></path><path d="M289 345c2-2 6-6 7-9h0 1c0 2-2 4-4 5h1c1-1 3-3 3-4s0 0 1-1v-1c0-1 1-2 2-3 1-3 1-7 1-9-1-1-1-2-1-3 1 1 2 3 2 4v1 7 2l-2 4-1 2-2-2-1 2-5 4-2 1z" class="C"></path><path d="M299 336c0-1 0-2 1-3h1l1 1-2 4-1 2-2-2 2-2z" class="D"></path><path d="M299 336c1 0 1 1 1 2l-1 2-2-2 2-2z" class="V"></path><path d="M279 302c0-1 1-2 2-4v1h1 2c1 1 2 1 2 2v2c1 1 1 1 2 1l-1 2c0 1 0 3-1 3 0-1-1-2-1-4h-1v-2h-1c-1 2-1 5-1 7h-1c0-1 1-3 0-5l-1 1c0 1-1 2-1 3v3h-1c0 1-1 4-1 5l-1 6-2 3c0-1 0-2-1-3 1-1 2-2 2-3v-1c1-2 1-3 1-5l1-7c1-1 1-2 1-3 0 0 0-1 1-2z" class="C"></path><path d="M281 299h1 2c1 1 2 1 2 2-1 0-2 0-3 1 0 0 0 1-1 1l-1-4z" class="D"></path><path d="M319 318c-1-2-1-3 0-5v-1c1 1 1 1 1 2 1 4 3 7 4 10 0 1 1 3 1 4h1 0c0 1 1 2 1 3 0 0 0 1-1 1h0-1v1l-1 2v2-1l-1-1v2h-1v-1c0-1 0-2-1-3h-1 0l-1-3h1v-2c-1-2-1-3-1-5-1-1-1-2-1-4l1-1z" class="F"></path><path d="M319 323c2 3 4 8 4 12l-1 1c0-1 0-2-1-3h-1 0l-1-3h1v-2c-1-2-1-3-1-5zm0-5c-1-2-1-3 0-5v-1c1 1 1 1 1 2 1 4 3 7 4 10 0 1 1 3 1 4h1 0c0 1 1 2 1 3 0 0 0 1-1 1h0c-1-2-2-2-3-4-2-3-3-6-4-10h0z" class="E"></path><path d="M297 338l2 2-1 2-2 3c-1 1-2 2-3 4-2 1-4 4-7 4l-1 1c-1 0-3 1-4 1h-3-1-1l1-3c1 0 2-2 3-3l1-1v1c1-1 2-2 3-2 2-1 3-1 5-2l2-1 5-4 1-2z" class="O"></path><path d="M297 338l2 2-1 2-2 3c0-1 0-2 1-3 0-1-1-1-1-2l1-2z" class="G"></path><path d="M283 352c1-1 3-2 4-2h1l4-4c0-1 1-1 2-2v1c0 1-1 1-2 2l1 2c-2 1-4 4-7 4v-1h-3z" class="a"></path><path d="M289 345l2-1c-1 1-2 3-4 3-1 0-4 3-6 4v1h1 1 3v1l-1 1c-1 0-3 1-4 1h-3-1-1l1-3c1 0 2-2 3-3l1-1v1c1-1 2-2 3-2 2-1 3-1 5-2z" class="n"></path><path d="M281 351v1h1 1 3v1l-1 1c-1 0-3 1-4 1h-3c1-1 2-3 3-4z" class="W"></path><path d="M298 342v1 2c-1 0-1 1-1 2h0c1-1 1-2 2-3s2-2 3-4v1c0 1 0 2 1 4h1c1-2 1-3 2-5l-1 7s-1 2-2 3l-1 2-3 4h0l-1-1s-1 1-2 1l-2 1h0l-4 2-2-1c-3 1-5 2-9 3v-1h0v-1c0-2 1-3 2-4 1 0 3-1 4-1l1-1c3 0 5-3 7-4 1-2 2-3 3-4l2-3z" class="V"></path><path d="M288 358c1-1 2-1 4-1 1-1 3-2 4-3v2l-2 1h0l-4 2-2-1z" class="K"></path><path d="M296 354l6-5 1 1-1 2-3 4h0l-1-1s-1 1-2 1v-2z" class="o"></path><path d="M296 349l3-3c0 1 0 2-1 3h1l1-1h0c0 2-3 4-4 5 0 1-1 1-1 1-2 1-2 1-4 0 2-1 4-2 6-4l-1-1z" class="J"></path><path d="M296 349l1 1c-2 2-4 3-6 4-1 1-2 1-3 2-3 1-7 2-9 4h0v-1c0-2 1-3 2-4 1 0 3-1 4-1h1c4-1 7-2 10-5z" class="b"></path><defs><linearGradient id="j" x1="307.877" y1="356.563" x2="302.78" y2="346.636" xlink:href="#B"><stop offset="0" stop-color="#575657"></stop><stop offset="1" stop-color="#747273"></stop></linearGradient></defs><path fill="url(#j)" d="M305 347v2h1c2-1 3-6 5-8h0 0c1 1 1 1 2 1h0 1l-1 3-2 6c0 1-1 2-1 3-1 2-2 3-3 5-1 1-1 2-2 2l-1 1c-2 2-4 5-5 7h0l-1 3v1h-1-2l-1-1 1-1-1-1-3 1h0l-1-1c0-1 0 0 1-1h0c1-1 1-1 2-1h0v-1c0 1-1 1-1 1h-3-1-1l2-2h0c-1 0-2 1-3 1l3-3v-1c0-1 0-1 1-2h0 0-2l1-1 1-1 4-2h0l2-1c1 0 2-1 2-1l1 1h0l3-4 1-2c1-1 2-3 2-3z"></path><path d="M299 356h0c1 2 1 1 2 2-2 3-6 6-10 6 0 0-1 1-2 1v-1c1 0 1-1 2-1 2-1 7-4 8-7z" class="G"></path><path d="M296 356c1 0 2-1 2-1l1 1c-1 3-6 6-8 7-1 0-1 1-2 1v-1c0-1 0-1 1-2h0 0-2l1-1 1-1 4-2h0l2-1z" class="a"></path><path d="M290 359l4-2v1c0 2-1 2-2 3s-1 1-1 2c-1 0-1 1-2 1v-1c0-1 0-1 1-2h0 0-2l1-1 1-1z" class="f"></path><path d="M290 367h0c0-1 1-2 1-2l8-3c2-1 4-1 6-3 1-2 3-4 5-5-1 2-2 3-3 5-1 1-1 2-2 2l-1-1c-4 4-9 7-14 7z" class="c"></path><path d="M305 347v2h1c2-1 3-6 5-8h0 0c-1 6-4 10-7 14l-3 3c-1-1-1 0-2-2l3-4 1-2c1-1 2-3 2-3z" class="F"></path><path d="M302 352c0 1-1 2 0 3h2l-3 3c-1-1-1 0-2-2l3-4z" class="P"></path><path d="M290 367c5 0 10-3 14-7l1 1-1 1c-2 2-4 5-5 7h0l-1 3v1h-1-2l-1-1 1-1-1-1-3 1h0l-1-1c0-1 0 0 1-1h0c1-1 1-1 2-1h0v-1c0 1-1 1-1 1h-3l1-1z" class="d"></path><path d="M294 370c0-1 1-1 2-1 1-1 1-1 2-1l1 1-1 3v1h-1-2l-1-1 1-1-1-1z" class="f"></path><path d="M295 371v-1c1 1 2 1 3 2v1h-1-2l-1-1 1-1z" class="a"></path><path d="M279 360v1c4-1 6-2 9-3l2 1-1 1-1 1h2 0 0c-1 1-1 1-1 2v1l-3 3c1 0 2-1 3-1h0l-2 2h1 1 3s1 0 1-1v1h0c-1 0-1 0-2 1h0c-1 1-1 0-1 1-2 3-3 6-6 9-2 2-3 3-6 4h-3c-2 1-3 1-4 0h-2 0c-2-1-2-1-2-2h1l2-1h2 0l-1-1c0-1-1-2-1-3 0-4 3-7 5-10h1-1c0-1 0-1-1-2l2-1c0-1 2-2 3-3h0z" class="k"></path><path d="M278 383l-1-1v-1c2-1 5-2 7-2-2 2-3 3-6 4z" class="Y"></path><path d="M279 360v1c4-1 6-2 9-3l2 1-1 1-1 1h2 0 0c-1 1-1 1-1 2v1l-3 3c1 0 2-1 3-1h0l-2 2h1l-6 3v1h2v1c0 1 0 1-2 2l-1-2h-2l-4 2c0 1 0 1-1 1v-1s0-1 1-1h0-1s0 1-1 1v-1l1-1h-1c0-1 1-1 1-2v-1c1 0 2 1 4 0s5 0 7-2h-1-2c-1 0-1 1-2 0-3 0-4 0-5-2h1-1c0-1 0-1-1-2l2-1c0-1 2-2 3-3h0z" class="i"></path><path d="M275 375h0l1-1v-1h-1l1-1c2 1 3 1 4 0v1h-1l-4 2z" class="d"></path><path d="M288 362v1h0c-2 1-4 3-6 3l-1-1c0 1-1 1-2 2h-2v-2c0-1 1-2 2-2 1 1 1 1 2 1s1-1 2-1c2 0 3-1 5-1z" class="p"></path><path d="M279 360v1c4-1 6-2 9-3l2 1-1 1-1 1v1c-2 0-3 1-5 1-1 0-1 1-2 1s-1 0-2-1c-1 0-2 1-2 2l-1 1h-1c0-1 0-1-1-2l2-1c0-1 2-2 3-3h0z" class="J"></path><path d="M279 363c3-2 6-2 10-3l-1 1v1c-2 0-3 1-5 1-1 0-1 1-2 1s-1 0-2-1z" class="T"></path><path d="M325 333v-1c2 4 2 11 1 15l-1 2-3 6-2 1h0-1c-1 1-2 2-3 4 0 0-1 0-1 1-2 1-3 4-6 6h0l-2 2c-2 1-4 2-7 4h-1l-2 5c0-2 0-3 1-5v-1l1-3h0c1-2 3-5 5-7l1-1c1 0 1-1 2-2 1-2 2-3 3-5 0-1 1-2 1-3l2-6 3-4v-6h1l1 2v-1l2 3h1c0-2 0-4-1-6h1c1 1 1 2 1 3v1h1v-2l1 1v1-2l1-2z" class="I"></path><path d="M316 341c-2 7-5 12-8 18v1c1-2 2-3 3-4 1 1 0 2 1 2 3-2 4-6 7-9v2l-3 6c-1 0-3 3-4 3s-1 0-1 1l-3 2c0-1 2-3 2-4l-2 2-1-1v-1c1-2 2-3 3-5 0-1 1-2 1-3l2-6 3-4z" class="H"></path><path d="M308 361l2-2c0 1-2 3-2 4l3-2c0-1 0-1 1-1s3-3 4-3l-3 3-5 5-2 2 1 2c-2 1-4 2-7 4h-1l-2 5c0-2 0-3 1-5v-1l1-3h0c1-2 3-5 5-7l1-1c1 0 1-1 2-2v1l1 1z" class="S"></path><path d="M307 363v1c-1 1-3 3-4 5 0 1-1 1-1 1l-2 2v1h-1l-2 5c0-2 0-3 1-5v-1l1-3h0v1h1c2-2 5-4 7-7z" class="G"></path><path d="M307 359v1l1 1-1 2c-2 3-5 5-7 7h-1v-1c1-2 3-5 5-7l1-1c1 0 1-1 2-2z" class="S"></path><path d="M307 359v1l1 1-1 2c-2 0-2 0-3-1l1-1c1 0 1-1 2-2z" class="n"></path><defs><linearGradient id="k" x1="326.513" y1="345.723" x2="321.589" y2="340.917" xlink:href="#B"><stop offset="0" stop-color="#8d8d8d"></stop><stop offset="1" stop-color="#b2afb1"></stop></linearGradient></defs><path fill="url(#k)" d="M325 333v-1c2 4 2 11 1 15l-1 2-3 6-2 1h0-1c-1 1-2 2-3 4 0 0-1 0-1 1-2 1-3 4-6 6h0l-2 2-1-2 2-2 5-5 3-3 3-6v-2c1-4 1-9-1-12v-1l2 3h1c0-2 0-4-1-6h1c1 1 1 2 1 3v1h1v-2l1 1v1-2l1-2z"></path><path d="M321 342c0-2 1-3 1-5 0 2 1 4 1 6h0l2-1h0c0 1 0 1-1 2h0-1-1v-3l-1 1z" class="S"></path><path d="M322 337h1v-2l1 1v1-2l1-2c0 3 0 5-2 8l-1-4z" class="C"></path><path d="M319 351c1-1 2-3 3-4v1c-1 5-5 9-9 13-1 2-3 3-4 4h-1l5-5 3-3 3-6z" class="X"></path><path d="M318 337v-1l2 3v5l1-2 1-1v3h1v2h0l-1 2v-1c-1 1-2 3-3 4v-2c1-4 1-9-1-12z" class="O"></path><path d="M321 342l1-1v3h1v2h0l-1-1c0 1 0 1-1 1l-1-2 1-2z" class="J"></path><path d="M283 342l8-5h1c-2 2-4 3-6 5-1 1-2 2-2 3l-3 3-1 1c-1 1-2 3-3 3l-1 3h1 1 3c-1 1-2 2-2 4v1c-1 1-3 2-3 3l-2 1c1 1 1 1 1 2h1-1c-2 3-5 6-5 10 0 1 1 2 1 3l1 1h0-2l-2 1v-2h0l-1-1-2 2-1-1-2 1v-1h-2 1c2-1 3-2 4-4 0 0 0-1 1-2 1 0 0 0 0-1 0 0 1-1 1-2v-1h-2 0c1-2 1-1 2-2l-1-1-1-2v-3s0-1-1-2v-3c0-2 0-3 1-4 0 1 0 2 1 3h0v-3l1-4 1-1c0-1 0-1 1-1 0-1 2-2 3-3l1 1h0 1c2-1 5-2 6-3h2 0l1 1z" class="M"></path><path d="M278 355h3c-1 1-2 2-2 4v1c-1 1-3 2-3 3l-1-1c0-3 1-4 3-7z" class="g"></path><path d="M280 346c2-2 4-3 6-4-1 1-2 2-2 3l-3 3-1 1c-1 1-2 3-3 3s-1 1-2 1h0c1-3 3-5 5-7z" class="V"></path><path d="M270 359v-1c1-2 1-5 2-7s3-4 5-5l1 1c-1 0-2 1-3 2s-1 3-2 4c1 1 1 1 0 2v4h0l2-2h0c-1 2-1 4-1 6h-2l-1-1c-1-1-1-2-1-3z" class="g"></path><path d="M283 342l8-5h1c-2 2-4 3-6 5-2 1-4 2-6 4-1 0-2 0-2 1l-1-1c-2 1-4 3-5 5s-1 5-2 7v1c-1-3-1-5 0-7 3-7 7-8 13-10z" class="a"></path><path d="M273 344h1c2-1 5-2 6-3h2 0l1 1c-6 2-10 3-13 10-1 2-1 4 0 7 0 1 0 2 1 3h-1c-2-2-3-4-4-7v-3l1-4 1-1c0-1 0-1 1-1 0-1 2-2 3-3l1 1h0z" class="D"></path><path d="M268 347c0-1 0-1 1-1 0-1 2-2 3-3l1 1c-1 1-2 2-3 4s-2 5-3 6h-1v-2l1-4 1-1z" class="F"></path><path d="M265 364v-3s0-1-1-2v-3c0-2 0-3 1-4 0 1 0 2 1 3h0c1 3 2 5 4 7h1l1 1h2c0 2-1 3-3 5h0c-2 3-2 6-2 9 0 1 1 2 2 2h0l1 1h0-2l-2 1v-2h0l-1-1-2 2-1-1-2 1v-1h-2 1c2-1 3-2 4-4 0 0 0-1 1-2 1 0 0 0 0-1 0 0 1-1 1-2v-1h-2 0c1-2 1-1 2-2l-1-1-1-2z" class="g"></path><path d="M264 379c1-1 1-1 1-3h1v2h1l-2 2-1-1z" class="Y"></path><path d="M271 362l1 1h2c0 2-1 3-3 5h0c0-1-1-2-2-3v-1l1 1h1 1v-1l-2-2h1z" class="W"></path><path d="M267 370l1-1h1v1c0 1 0 1-1 2l1 1-1 1h0c-1 1-2 1-2 2l-1-1s0-1 1-2c1 0 0 0 0-1 0 0 1-1 1-2z" class="k"></path><path d="M294 370l1 1-1 1 1 1h2 1c-1 2-1 3-1 5l2-5c1 1 2 1 2 2-1 1-1 2-2 3 0 3-1 5-2 7-2 4-7 8-11 10h-1c-1 0-2 1-3 1h-4 0-1c-2 0-4 0-6-2l-3-3c-1-1-1-2-1-2 0-3 0-4 2-6h0 2c1 1 2 1 4 0h3c3-1 4-2 6-4 3-3 4-6 6-9l1 1h0l3-1z" class="G"></path><path d="M283 386h4l1 1c-1 0-2 1-4 1 0 0-1 0-1 1h-2l-4-1v-1c1 0 0 0 2 1h2l2-2zm-12 8c1 0 1 0 1-1v-1-1l-1-2v-1c1 1 2 2 2 3h0c1 3 2 4 5 5h0-1c-2 0-4 0-6-2z" class="O"></path><path d="M271 388v-2c2 0 4 1 5 1h1v1l1 2-1 1-2-1c-1 0-1 1-2 1h0c0-1-1-2-2-3z" class="L"></path><path d="M286 381h0c2 1 2 1 4 0v1c0 2-1 2-2 3h1 1c-1 1-1 2-2 2l-1-1h-4-3v-1h5v-3l1-1z" class="X"></path><path d="M286 381h0c2 1 2 1 4 0v1c-2 1-3 2-5 3v-3l1-1z" class="o"></path><path d="M294 370l1 1-1 1h0c-2 4-5 7-8 9l-1 1c-2 1-4 1-6 2 4-2 7-4 9-8 1-2 2-4 3-5h0l3-1z" class="T"></path><path d="M294 370l1 1-1 1h0-2l-1-1 3-1z" class="j"></path><path d="M294 372l1 1h2 1c-1 2-1 3-1 5 0 1-3 7-5 8 0 1 0 1-1 1l-1-1v1c-2 1-5 2-7 2 0-1 1-1 1-1 2 0 3-1 4-1s1-1 2-2h-1-1c1-1 2-1 2-3v-1c-2 1-2 1-4 0h0c3-2 6-5 8-9h0z" class="f"></path><path d="M293 378h2c0 1-1 1-1 2v1h-1c0 1 0 1-1 2 0 1-1 2-2 2h-1-1c1-1 2-1 2-3l2-1c0-1 1-1 2-2l-1-1z" class="a"></path><path d="M294 372l1 1h0c0 1 0 1 1 2-1 1-1 2-1 3h-2l1 1c-1 1-2 1-2 2l-2 1v-1c-2 1-2 1-4 0h0c3-2 6-5 8-9h0z" class="N"></path><path d="M295 373h0c0 1 0 1 1 2-1 1-1 2-1 3h-2l-1-1c1-1 2-3 3-4z" class="d"></path><path d="M299 373c1 1 2 1 2 2-1 1-1 2-2 3 0 3-1 5-2 7-2 4-7 8-11 10h-1c-1 0-2 1-3 1h-4c-3-1-4-2-5-5h0 0c1 0 1-1 2-1l2 1 1-1-1-2 4 1h2c2 0 5-1 7-2v-1l1 1c1 0 1 0 1-1 2-1 5-7 5-8l2-5z" class="p"></path><path d="M273 391c1 0 1-1 2-1l2 1h1l3 1-1 1h2v1c-3 0-6-1-9-3z" class="O"></path><path d="M283 389c2 0 5-1 7-2v-1l1 1c-3 2-7 4-10 5l-3-1h-1l1-1-1-2 4 1h2z" class="J"></path><path d="M277 388l4 1v1c-1 0-2 1-3 1h-1l1-1-1-2zm23-102c1 1 2 1 2 1 1 0 1 1 1 1 1 0 1-1 2 0h0v1h0c0 2 0 3-1 4h0c1 1 1 2 1 2-1 1-1 2-1 3h1l1 1v1h1l1-1h1c0 1 0 1 1 1l1-1h1 0 1c0 2 1 1 2 2 0-1 1-1 2-2v2c1 0 1 0 2-1l1-1h0c0 1-1 2-1 3h1c0 1-1 3-1 4h1c1 1 1 2 1 3h0c1-1 1-2 1-3h1v1c0 1 1 2 1 3h1v-2l1-1v3l1-1h0l1-1h1c1 1 1 1 1 2h0c1 1 1 2 1 3l-1-1c-1 1-1 1-2 1 1 2 2 5 2 7v1h0c-1 0-1-1-1-1l-1-1c-2 0-2-1-3-2h-1c-1-1-1-2-1-3h0-1l-1 1-1-1c0-1 0-1-1-2v1c-1 2-1 3 0 5l-1 1c0-2-1-4-1-6 0-1 0-3-1-4h-1c-2 2-2 6-2 9h0l-1-2-1-5c-1 0-2-1-3 0h0c0 1-1 1-2 1-1 1-1 2-1 3h-1 0c0-2 0-2-2-3l-2 2v-1l-2-3h-1c-1 0-1 0-2-2 0 2 0 4-1 5 0-1 0-3-1-4l-1 1h0c-1-1-2-1-3-2v-2h-2l1-2v-1-1c1-2 2-4 4-6h0 1l3-6c1-1 2-3 4-3v-1z" class="P"></path><path d="M296 307l1-1c0-1 0-3 1-3v1c0 2 2 4 2 6v3l-2-3h0l-1-1c-1-1-1-1-1-2z" class="B"></path><path d="M292 307l2-6v4c0 1 0 3 1 4 0-3 0-5 1-8v6c0 1 0 1 1 2l1 1h0-1c-1 0-1 0-2-2 0 2 0 4-1 5 0-1 0-3-1-4l-1 1h0v-3z" class="U"></path><path d="M319 306h1c1 1 1 2 1 3h0c1-1 1-2 1-3h1v1c0 1 1 2 1 3h1c0 1 0 3-1 3h0v-2c-1 0-2-1-3 0h-1l-1-2v1c-1 0-1 1-1 1v1h-1v-4l1 1c1-1 1-2 1-3z" class="K"></path><path d="M295 296l2-2v1c1 0 2-1 2-1 0 1 0 0 1 1l1-2h0c0 1 0 3-1 4h1 0 1l-1 6c-1 0 0 1 0 2-1-1-1-1-1-2h-1c0-2 0-4 1-6v-1l-1 1c0-1-1-1-1-1l-2 2v-1h0l-1-1z" class="G"></path><path d="M319 300l1-1h0c0 1-1 2-1 3h1c0 1-1 3-1 4s0 2-1 3l-1-1 1-6-3 4c-1-1 0-2 0-3v-1-1c0-1 1-1 2-2v2c1 0 1 0 2-1z" class="a"></path><path d="M288 302c1-2 2-4 4-6h0v2c-2 2-2 4-2 6h0c1 1 1 2 1 3h1v3c-1-1-2-1-3-2v-2h-2l1-2v-1-1z" class="C"></path><path d="M288 303h1c1 0 1 0 1 1-1 1-1 2-1 4v-2h-2l1-2v-1zm12 7h1c1 0 1-1 2 0v1h1l1-2v-1l1 1v1h1l2-2c1 1 2 1 2 2v1c-1 0-2-1-3 0h0c0 1-1 1-2 1-1 1-1 2-1 3h-1 0c0-2 0-2-2-3l-2 2v-1-3z" class="I"></path><path d="M327 309l1-1h1c1 1 1 1 1 2h0c1 1 1 2 1 3l-1-1c-1 1-1 1-2 1 1 2 2 5 2 7v1h0c-1 0-1-1-1-1l-1-1c-2 0-2-1-3-2h-1c-1-1-1-2-1-3h0-1v-2h1c1 1 1 2 1 3 0 0 1-1 1-2 1 1 1 2 1 3v1c1-1 1-1 1-2h-1v-5l1-1h0z" class="H"></path><path d="M327 309c0 2 1 4 0 6h-1v-5l1-1z" class="V"></path><path d="M300 286c1 1 2 1 2 1 1 0 1 1 1 1 1 0 1-1 2 0h0v1h0l-3 3v1h1v3l-1 1h0-1 0-1c1-1 1-3 1-4h0l-1 2c-1-1-1 0-1-1 0 0-1 1-2 1v-1l-2 2c-1 1-1 2-2 3h-1 0v-1-2h1l3-6c1-1 2-3 4-3v-1z" class="d"></path><path d="M296 290c1-1 2-3 4-3h1 1l1 1h1v1c-1 0-1 1-2 1 0 1 0 1-1 1h-4c0-1 0 0-1-1z" class="f"></path><path d="M301 287h1l1 1h1v1c-1 0-1 1-2 1h-3c0-1 1-2 2-3z" class="g"></path><path d="M302 297h0l1-1v-3h-1v-1l3-3c0 2 0 3-1 4h0c1 1 1 2 1 2-1 1-1 2-1 3h1l1 1v1h1l1-1h1c0 1 0 1 1 1l1-1h1 0 1c0 2 1 1 2 2v1 1c0 1-1 2 0 3h0c0 2-1 3-2 4 0-1-1-1-1-2h0l-1-1-1-1h-3v2h-1c0-1-1-2-1-3 0 1-1 2-1 3-1-1-1-2-2-2 0 1 0 2-1 2v-3c0-1-1-2 0-2l1-6z" class="Z"></path><path d="M307 300l1-1h1c0 1 0 1 1 1l1-1h1 0 1c0 2 1 1 2 2v1c-1 1-1 2-3 3h0l-1-3h-1l-2 2h-1v-2-2z" class="W"></path><path d="M302 297h0l1-1v-3h-1v-1l3-3c0 2 0 3-1 4h0c1 1 1 2 1 2-1 1-1 2-1 3h1l1 1v1h1v2l-1 1v-3c-1 1-1 1-1 2l-1 2c-1-1-1-1-1-2-1 0-1 1-2 1l1-6z" class="f"></path><defs><linearGradient id="l" x1="253.427" y1="385.741" x2="253.906" y2="400.082" xlink:href="#B"><stop offset="0" stop-color="#969497"></stop><stop offset="1" stop-color="#b8b6b7"></stop></linearGradient></defs><path fill="url(#l)" d="M261 365v-1l1-1c1 1 2 3 4 4v-1l1 1c-1 1-1 0-2 2h0 2v1c0 1-1 2-1 2 0 1 1 1 0 1-1 1-1 2-1 2-1 2-2 3-4 4h-1 2v1l2-1 1 1 2-2 1 1h0v2h-1c0 1 0 1 2 2-2 2-2 3-2 6 0 0 0 1 1 2l3 3c2 2 4 2 6 2h1 0 4l-1 2h0 0-4v1c-2 0-4-1-7 0l-2-1-2 2c-1 0-2 1-3 1h-1c0 2-1 2-3 3-1 1-2 2-4 2h-1 0-1-3v-1h-1l1-1c-1-1-2-2-2-3h-1l-1 1-1-1-1 1v-2-1h0c1 0 2-2 3-2h0v-2-1c-1-3 0-5 0-8h-1c0-1 1-2 0-3l2-2 7-4 3-4 1-1v-1c2-2 2-4 2-6z"></path><path d="M251 394h3v2c1 0 1 0 2-1l2 3v1c-4-1-4-2-7-5z" class="X"></path><path d="M249 395c2 4 4 5 8 6-1 0-2 0-3 1-2-1-3-2-4-4-1-1-1-2-1-3z" class="d"></path><path d="M253 391c0-1 1-1 2-2 0 1 0 3-1 4v1h-3c-1-2-1-3-1-5l1 2h1 1z" class="a"></path><path d="M252 384h1c0 1 0 2-1 3l1 1v3h-1-1l-1-2v-2c1-1 2-2 2-3z" class="Y"></path><path d="M252 387l1 1v3h-1c-1 0-1 0-1-1s0-2 1-3z" class="e"></path><path d="M257 401h5c0 2-1 2-3 3h-3 3l-5-2c1-1 2-1 3-1z" class="i"></path><path d="M247 395h1v-2h1v2c0 1 0 2 1 3 1 2 2 3 4 4l5 2h-3 3c-1 1-2 2-4 2h-1 0-1-3v-1h-1l1-1c-1-1-2-2-2-3h-1l-1 1-1-1-1 1v-2-1h0c1 0 2-2 3-2h0v-2z" class="O"></path><defs><linearGradient id="m" x1="253.453" y1="406.078" x2="249.106" y2="398.565" xlink:href="#B"><stop offset="0" stop-color="#232425"></stop><stop offset="1" stop-color="#414140"></stop></linearGradient></defs><path fill="url(#m)" d="M245 401l3-2c1 1 2 3 4 3 1 1 3 2 4 2h3c-1 1-2 2-4 2h-1 0-1-3v-1h-1l1-1c-1-1-2-2-2-3h-1l-1 1-1-1z"></path><path d="M260 379h2v1l-3 3c0 1 0 1-1 2 1 1 1 1 1 2l-1 1c0 1-1 2-1 4h1v2h-1v1c1 1 1 1 1 3l-2-3c-1 1-1 1-2 1v-2-1c1-1 1-3 1-4-1 1-2 1-2 2v-3l-1-1c1-1 1-2 1-3h-1l-1-2 2-2c2 1 2 1 3 1h1c1 0 2-1 3-2z" class="Y"></path><path d="M258 385c1 1 1 1 1 2l-1 1c0 1-1 2-1 4h1v2h-1v1c-1 0-1-1-2-1 1-1 1-1 1-2 0-2 1-5 2-7z" class="b"></path><path d="M260 379h2v1l-3 3c-2 1-4 2-6 5l-1-1c1-1 1-2 1-3h-1l-1-2 2-2c2 1 2 1 3 1h1c1 0 2-1 3-2z" class="k"></path><path d="M253 380c2 1 2 1 3 1h1c-1 1-3 2-4 3h-1l-1-2 2-2z" class="M"></path><path d="M261 365v-1l1-1c1 1 2 3 4 4v-1l1 1c-1 1-1 0-2 2h0 2v1c0 1-1 2-1 2 0 1 1 1 0 1-1 1-1 2-1 2-1 2-2 3-4 4h-1c-1 1-2 2-3 2h-1c-1 0-1 0-3-1l-2 2 1 2c0 1-1 2-2 3h0c0-1-1-1-1-1 0-1 0-1-1-1l-1 1h-1c0-1 1-2 0-3l2-2 7-4 3-4 1-1v-1c2-2 2-4 2-6z" class="T"></path><path d="M260 373l1-1c0-1 1-2 1-2 1 0 1 1 1 2l1-1v-2h1 0c0 1 1 3 0 4h0c-1 1-2 3-3 4h-1-2l1-4z" class="d"></path><path d="M262 377h-1l2-4v-1h1l1 1c-1 1-2 3-3 4z" class="X"></path><path d="M261 365v-1l1-1c1 1 2 3 4 4v-1l1 1c-1 1-1 0-2 2h-1v2l-1 1c0-1 0-2-1-2 0 0-1 1-1 2l-1 1h-2l1-1v-1c2-2 2-4 2-6z" class="f"></path><path d="M258 373h2l-1 4c-2 2-4 2-6 3l-2 2 1 2c0 1-1 2-2 3h0c0-1-1-1-1-1 0-1 0-1-1-1l-1 1h-1c0-1 1-2 0-3l2-2 7-4 3-4z" class="g"></path><path d="M251 382l1 2c0 1-1 2-2 3h0c0-1-1-1-1-1 0-1 0-1-1-1 0-1 2-3 3-3z" class="J"></path><path d="M267 378l1 1h0v2h-1c0 1 0 1 2 2-2 2-2 3-2 6 0 0 0 1 1 2l3 3c2 2 4 2 6 2h1 0 4l-1 2h0 0-4v1c-2 0-4-1-7 0l-2-1-2 2c-1 0-2 1-3 1v-1c-2-1-3-1-5-1v-1c0-2 0-2-1-3v-1h1v-2h-1c0-2 1-3 1-4l1-1c0-1 0-1-1-2 1-1 1-1 1-2l3-3 2-1 1 1 2-2z" class="W"></path><path d="M261 388c1 0 1 1 2 1v2h0c0 1 1 1 1 2 1 0 1 1 2 1h0l1-2c3 4 6 5 10 6v1c-2 0-4-1-7 0l-2-1c-4-3-5-4-7-9v-1z" class="c"></path><defs><linearGradient id="n" x1="264.226" y1="380.271" x2="263.186" y2="392.73" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#b1afb1"></stop></linearGradient></defs><path fill="url(#n)" d="M267 378l1 1h0v2h-1c-1 1-2 3-3 5 0 2 1 4 3 6l-1 2h0c-1 0-1-1-2-1 0-1-1-1-1-2h0v-2c-1 0-1-1-2-1v1c0-1 0-2-1-3l-1 1c0-1 0-1-1-2 1-1 1-1 1-2l3-3 2-1 1 1 2-2z"></path><path d="M264 379l1 1c-2 2-4 4-4 7v1 1c0-1 0-2-1-3l-1 1c0-1 0-1-1-2 1-1 1-1 1-2l3-3 2-1z" class="W"></path><path d="M259 387l1-1c1 1 1 2 1 3 2 5 3 6 7 9l-2 2c-1 0-2 1-3 1v-1c-2-1-3-1-5-1v-1c0-2 0-2-1-3v-1h1v-2h-1c0-2 1-3 1-4l1-1z" class="i"></path><path d="M258 394v-2h-1c0-2 1-3 1-4 0 1 0 1 1 2v2h1v-1c1 2-1 2 0 4l-2-1z" class="W"></path><path d="M257 395v-1h1l2 1s1 1 2 1l3 3c1 0 0 0 1 1-1 0-2 1-3 1v-1c-2-1-3-1-5-1v-1c0-2 0-2-1-3z" class="h"></path><path d="M339 279h3c1-1 0-2 2-1h1l1 2c2 3 3 7 4 10 1 2 2 4 2 7 0 0 0 2 1 3v3h-1-1c0 1 1 2 1 3h-1l-1 1v1h0l-2-3v-2h-1c0 1 0 1-1 1v-4h0c-1 1-1 1-1 2h-1v-3h-1l-1 3c0 2 0 6 1 8l1 4v1l-1-1v-1c-1 0-1-1-2-2v2c-1-1-1-2-2-2s-1 1-1 1l-2-2h-1v1c0 1 0 1-1 1h0l-2-4h-1l-1 2h0c0-1 0-1-1-2h-1l-1 1h0l-1 1v-3l-1 1v2h-1c0-1-1-2-1-3v-1h-1c0 1 0 2-1 3h0c0-1 0-2-1-3h-1c0-1 1-3 1-4h-1c0-1 1-2 1-3h0l-1 1c-1 1-1 1-2 1v-2c-1 1-2 1-2 2-1-1-2 0-2-2h-1 0-1l-1 1c-1 0-1 0-1-1h-1l-1 1h-1v-1l-1-1h-1c0-1 0-2 1-3 0 0 0-1-1-2h0c1-1 1-2 1-4h0v-1h0c-1-1-1 0-2 0 0 0 0-1-1-1 0 0-1 0-2-1h1c1-2 3-3 5-4 1 0 2 0 4-1v1 1h0l2-1c1 0 3 1 4 0h0l7-1h2 1c2 1 7-2 9-3 2 0 2 0 4 1z" class="r"></path><path d="M340 287h0l1 3h-1c-1-2-1-2 0-3z" class="e"></path><path d="M342 293l1-1 1-1v-1c0 1 0 1 1 2v-1c1 1 1 2 1 3v1c-1 1-1 2-1 4h-1v-7l-2 1z" class="p"></path><path d="M346 295v1h0c1 0 1-1 1-1 0-1 1-1 1-2l-1-1c0-1-1-2-1-4h0c1 1 1 1 1 2 1 1 1 2 1 3 1 0 1 1 1 1 0 1 1 1 1 2-1 1-1 1-1 2v-1l-1 1-1-1v6c0 1 0 1-1 1v-4-1h-1c0-2 0-3 1-4z" class="k"></path><path d="M342 293l2-1v7h1 1v1h0c-1 1-1 1-1 2h-1v-3h-1l-1 3v1h-1c-1-3 0-7 1-9v-1z" class="i"></path><path d="M307 300h-1v-1l-1-1h-1c0-1 0-2 1-3v2c1-1 1-2 2-1v1c0 1 1 1 1 1h2c3 0 5-2 7-3-1 1-3 3-3 4h1c1 0 2 0 2-1l1 1 1 1c-1 1-1 1-2 1v-2c-1 1-2 1-2 2-1-1-2 0-2-2h-1 0-1l-1 1c-1 0-1 0-1-1h-1l-1 1z" class="j"></path><path d="M348 298l1-1v1c0-1 0-1 1-2 1 1 1 2 1 3h0v4c0 1 1 2 1 3h-1l-1 1v1h0l-2-3v-2h-1v-6l1 1z" class="b"></path><path d="M348 298l1-1v1c0-1 0-1 1-2 1 1 1 2 1 3h0l-1 1v2l-1 1c-1-2-1-3-1-5z" class="g"></path><defs><linearGradient id="o" x1="335.631" y1="294.258" x2="344.539" y2="302.582" xlink:href="#B"><stop offset="0" stop-color="#10100f"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#o)" d="M338 299v-2c0-2 0-4 1-6v1c1-1 1-1 2-1v1 1h1v1c-1 2-2 6-1 9h1v5h-1c-1-1-1-2-1-3h0l-1-1v-1c-1-1-1-3-1-4z"></path><path d="M317 298h0c2-1 2-1 4-1 1 0 2 0 4-1h2s1 0 1-1h4 0 1 1c1-1 2-3 3-4 0 1 1 3 0 4l1 4c0 1 0 3 1 4v1l1 1h0c0 1 0 2 1 3h1v-5-1c0 2 0 6 1 8l1 4v1l-1-1v-1c-1 0-1-1-2-2v2c-1-1-1-2-2-2s-1 1-1 1l-2-2h-1v1c0 1 0 1-1 1h0l-2-4h-1l-1 2h0c0-1 0-1-1-2h-1l-1 1h0l-1 1v-3l-1 1v2h-1c0-1-1-2-1-3v-1h-1c0 1 0 2-1 3h0c0-1 0-2-1-3h-1c0-1 1-3 1-4h-1c0-1 1-2 1-3h0l-1 1-1-1-1-1z" class="d"></path><path d="M332 297h0v-1c1 1 0 2 0 3h1c0-1 1-2 1-3h1 1 1v-1l1 4c0 1 0 3 1 4v1l1 1c-2-1-2-1-2-3h-1l-1-1v-1h-1-1v-1-1l-1 1c0 1 0 1-1 2v-4h0z" class="Y"></path><path d="M317 298h0c2-1 2-1 4-1 1 0 2 0 4-1h2s1 0 1-1h4 0 1 1c1-1 2-3 3-4 0 1 1 3 0 4v1h-1-1-1c0 1-1 2-1 3h-1c0-1 1-2 0-3v1h0c-1 0-2 1-4 1h0c-1 0-2 3-3 3v-1h-1l-2 2v-2-1c-1 1-1 2-1 3h-1-1c0-1 1-2 1-3h0l-1 1-1-1-1-1z" class="k"></path><path d="M321 302c0 1 0 3 1 3l2-4v5h1c1-2 1-3 1-5 1 1 1 2 2 2v-3l1 1h2v3l1-2h1l1 3h1v-2h1c1 1 2 3 2 4h1c0-1 0-1 1-2 0 1 0 2 1 3h1v-5-1c0 2 0 6 1 8l1 4v1l-1-1v-1c-1 0-1-1-2-2v2c-1-1-1-2-2-2s-1 1-1 1l-2-2h-1v1c0 1 0 1-1 1h0l-2-4h-1l-1 2h0c0-1 0-1-1-2h-1l-1 1h0l-1 1v-3l-1 1v2h-1c0-1-1-2-1-3v-1h-1c0 1 0 2-1 3h0c0-1 0-2-1-3h-1c0-1 1-3 1-4h1z" class="O"></path><path d="M327 309l1-6 2 4h1v-1c1 1 1 2 2 2 0 1 1 2 1 3h1c0-1 0-2 1-2v1h-1v1c0 1 0 1-1 1h0l-2-4h-1l-1 2h0c0-1 0-1-1-2h-1l-1 1z" class="S"></path><path d="M342 303v-1c0 2 0 6 1 8l1 4v1l-1-1v-1c-1 0-1-1-2-2v-1s-1-2-2-2v1h0l-1-1-1-1-1 1v-2h-1v1h-1v-2h1v-2h1c1 1 2 3 2 4h1c0-1 0-1 1-2 0 1 0 2 1 3h1v-5z" class="X"></path><path d="M339 279h3c1-1 0-2 2-1h1l-1 2c-1 0-1-1-2-1h-2c0 1 1 2 1 3l-1 1-3 3c-1 1-3 1-4 2-2 0-4 1-6 1h-2-1 0c-3 2-9 1-12 1-2-1-3-1-4-2h-2l-1 1v-1h0c-1-1-1 0-2 0 0 0 0-1-1-1 0 0-1 0-2-1h1c1-2 3-3 5-4 1 0 2 0 4-1v1 1h0l2-1c1 0 3 1 4 0h0l7-1h2 1c2 1 7-2 9-3 2 0 2 0 4 1z" class="Z"></path><path d="M310 281v1 1s-1 0-1 1c-2 1-3 1-5 2l2 2h0l-1 1v-1h0c-1-1-1 0-2 0 0 0 0-1-1-1 0 0-1 0-2-1h1c1-2 3-3 5-4 1 0 2 0 4-1z" class="M"></path><path d="M331 281c2 0 4-1 6 0h0 1l1 1c-2 1-5 4-6 6-2 0-4 1-6 1h-2-1 0-3c-2 0-5 0-7-1 3 0 8 1 10 0h1c0-1 1-1 1-1 3 0 7-1 8-3 1-1 1-2 0-3h-3z" class="I"></path><path d="M314 288h-1 0c0-1 1-1 1-2 2 0 3-1 4-1l6-3h1c1 0 4-1 4-1 1 0 1 1 2 0h3c1 1 1 2 0 3-1 2-5 3-8 3 0 0-1 0-1 1h-1c-2 1-7 0-10 0z" class="E"></path><path d="M330 310l1-2h1l2 4h0c1 0 1 0 1-1v-1h1l2 2s0-1 1-1 1 1 2 2v-2c1 1 1 2 2 2v1l1 1v-1c3 4 6 7 10 9-1 1-2 1-3 1h-1c1 1 1 2 2 2 0 2 1 4 2 6-1 1-1 3-2 5v1 1c0 3-1 5-2 7v1-4l-3 3c-1 1-1 3-2 4h0v-3c-1 2-1 3-2 4 0 0-1-1 0-2v-1l-2 3-3 5c-2 2-4 3-7 5h0l1-2-2 1-1-1-6 4-2 1-7 3h-1c-1 1-1 1-3 1h0l-1-1h0c3-2 4-5 6-6 0-1 1-1 1-1 1-2 2-3 3-4h1 0l2-1 3-6 1-2c1-4 1-11-1-15h1 0c1 0 1-1 1-1 0-1-1-2-1-3h0-1c0-1-1-3-1-4-1-3-3-6-4-10l1 1 1-1h1 0c0 1 0 2 1 3h1c1 1 1 2 3 2l1 1s0 1 1 1h0v-1c0-2-1-5-2-7 1 0 1 0 2-1l1 1c0-1 0-2-1-3z" class="G"></path><path d="M339 337h2v4 1h-1-1v-5z" class="l"></path><path d="M340 345c1 2 1 4 1 6l-3 5c-1-1-1-1-1-3h0c1-1 3-6 3-8z" class="L"></path><path d="M332 357c1-1 3-4 5-4h0c0 2 0 2 1 3-2 2-4 3-7 5h0l1-2-2 1-1-1 3-2z" class="S"></path><path d="M332 357c1-1 3-4 5-4h0l-5 6h0l-2 1-1-1 3-2z" class="h"></path><path d="M341 337l1-1c1 1 2 2 2 4s-1 5-1 7v1l-2 3c0-2 0-4-1-6l1-3v-1-4z" class="G"></path><path d="M341 341v-1c2 1 2 3 2 5v2 1l-2 3c0-2 0-4-1-6l1-3v-1z" class="P"></path><defs><linearGradient id="p" x1="329.146" y1="337.662" x2="337.46" y2="331.772" xlink:href="#B"><stop offset="0" stop-color="#3d3a3f"></stop><stop offset="1" stop-color="#51524f"></stop></linearGradient></defs><path fill="url(#p)" d="M331 324c2 1 3 1 4 2 0 1 1 4 2 4v-1c1 1 1 3 2 4v1c-1 0-1 0-1 1-1 2 0 3-1 5v4l1 1c0 2-2 3-3 4v-2c-1-1-1-1 0-3h-1l-1 1v-1c0-1-1-1-1-2 1-4 1-9 0-13 0-1 0-3-1-5z"></path><path d="M337 330v-1c1 1 1 3 2 4v1c-1 0-1 0-1 1-1 2 0 3-1 5v4l1 1c0 2-2 3-3 4v-2c1-1 1-4 2-5 0-1-1-1-1-2v-3c0-2 0-5 1-7z" class="o"></path><defs><linearGradient id="q" x1="336.647" y1="342.49" x2="343.928" y2="331.423" xlink:href="#B"><stop offset="0" stop-color="#2d2d2f"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#q)" d="M335 326h0 1 1l7 5 3 8c0 2 1 4 0 6v1c-1 1-1 3-2 4h0v-3c-1 2-1 3-2 4 0 0-1-1 0-2v-1-1c0-2 1-5 1-7s-1-3-2-4l-1 1h-2v-3-1c-1-1-1-3-2-4v1c-1 0-2-3-2-4z"></path><path d="M339 333h1v-1-1l1 1c3 3 4 8 4 12 0 1-1 2 0 3-1 2-1 3-2 4 0 0-1-1 0-2v-1-1c0-2 1-5 1-7s-1-3-2-4l-1 1h-2v-3-1z" class="O"></path><path d="M339 333h1v-1-1l1 1c-1 2 0 4 0 5h-2v-3-1z" class="N"></path><path d="M332 342c0 1 1 1 1 2v1l1-1h1c-1 2-1 2 0 3v2c-1 2-3 4-4 6l-1 2h2l-3 2-6 4-2 1-7 3h-1c-1 1-1 1-3 1h0l-1-1h0c3-2 4-5 6-6 0-1 1-1 1-1 1-2 2-3 3-4h1 0l2-1c1-1 3-1 4-1l2-3c2-3 3-6 4-9z" class="T"></path><path d="M322 355c1-1 3-1 4-1-2 1-3 3-5 4h-1v-2l2-1z" class="V"></path><path d="M313 366l7-4 1 2-7 3h-1c-1 1-1 1-3 1h0l1-1c1 0 1 0 2-1z" class="Z"></path><path d="M320 362c2-1 5-2 6-3 2-1 4-3 5-4l-1 2h2l-3 2-6 4-2 1-1-2z" class="l"></path><path d="M309 367h0c3-2 4-5 6-6 0-1 1-1 1-1 1-2 2-3 3-4h1 0v2h1l-4 4c-1 1-4 3-4 4-1 1-1 1-2 1l-1 1-1-1z" class="M"></path><path d="M320 356h0v2h1l-4 4-1-1 1-1c1-1 2-2 3-4z" class="G"></path><defs><linearGradient id="r" x1="323.076" y1="327.837" x2="331.2" y2="326.715" xlink:href="#B"><stop offset="0" stop-color="#c7c6c7"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#r)" d="M322 314h1 0c0 1 0 2 1 3h1c1 1 1 2 3 2l1 1s0 1 1 1h0l1 3c1 2 1 4 1 5 1 4 1 9 0 13-1 3-2 6-4 9l-2 3c-1 0-3 0-4 1l3-6 1-2c1-4 1-11-1-15h1 0c1 0 1-1 1-1 0-1-1-2-1-3h0-1c0-1-1-3-1-4-1-3-3-6-4-10l1 1 1-1z"></path><path d="M332 329c1 4 1 9 0 13-1 3-2 6-4 9v-3h0c0-1 0-2 1-3h0c1-1 1-2 1-4h1 0c0-1 1-2 0-3 0 0 1-1 1-2v-7z" class="F"></path><defs><linearGradient id="s" x1="320.24" y1="336.545" x2="329.596" y2="333.798" xlink:href="#B"><stop offset="0" stop-color="#cac9c9"></stop><stop offset="1" stop-color="#f3f2f3"></stop></linearGradient></defs><path fill="url(#s)" d="M324 324v-1c-1-2-1-3-1-5 2 3 5 6 6 10v6c0 2 1 4 0 6v5c-1 1-1 2-1 3h0v3l-2 3c-1 0-3 0-4 1l3-6 1-2c1-4 1-11-1-15h1 0c1 0 1-1 1-1 0-1-1-2-1-3h0-1c0-1-1-3-1-4z"></path><path d="M330 310l1-2h1l2 4h0c1 0 1 0 1-1v-1h1l2 2s0-1 1-1 1 1 2 2v-2c1 1 1 2 2 2v1l1 1v-1c3 4 6 7 10 9-1 1-2 1-3 1h-1c1 1 1 2 2 2 0 2 1 4 2 6-1 1-1 3-2 5v1 1c0 3-1 5-2 7v1-4l-3 3v-1c1-2 0-4 0-6-1-3-2-5-3-8-3-2-4-3-7-5h-1-1 0c-1-1-2-1-4-2l-1-3v-1c0-2-1-5-2-7 1 0 1 0 2-1l1 1c0-1 0-2-1-3z" class="Q"></path><path d="M349 325l3 1c0 2 1 4 2 6-1 1-1 3-2 5v-7c-1-1-2-3-3-5z" class="T"></path><defs><linearGradient id="t" x1="349.898" y1="322.44" x2="342.289" y2="315.001" xlink:href="#B"><stop offset="0" stop-color="#333231"></stop><stop offset="1" stop-color="#5e5c5e"></stop></linearGradient></defs><path fill="url(#t)" d="M341 311c1 1 1 2 2 2v1l1 1v-1c3 4 6 7 10 9-1 1-2 1-3 1h-1c1 1 1 2 2 2l-3-1c-1-2-3-4-4-6-1-1-2-3-3-4s-1-2-1-2v-2z"></path><path d="M340 318v-1c0-1 0-1-1-2h1c1 1 2 3 4 5 0 1 1 1 1 2 2 2 4 4 5 6l-1 1-3-3c-1 0-1-1-2-2v2h0c0 1 1 2 1 2v1c1 1 1 2 1 3-1-2-3-4-4-7v-2l1-1c-1-1-3-3-3-4z" class="I"></path><path d="M346 332c0-1 0-2-1-3v-1s-1-1-1-2h0v-2c1 1 1 2 2 2l3 3c0 2 1 3 1 4 1 1 1 5 2 5h0v1c0 3-1 5-2 7v1-4l-3 3v-1c1-2 0-4 0-6l-3-8h0c1 1 1 2 2 3h0v-2z" class="J"></path><path d="M348 330c-1-1-2-2-2-4l3 3c0 2 1 3 1 4 1 1 1 5 2 5h0v1c0 3-1 5-2 7v1-4c0-3 1-6 0-9h0c-1-2-1-3-2-4z" class="d"></path><path d="M346 332c0-1 0-2-1-3v-1s-1-1-1-2h0v-2c1 1 1 2 2 2 0 2 1 3 2 4 0 1 1 2 1 3s-1 1-1 2v3 1h-1l-3-8h0c1 1 1 2 2 3h0v-2z" class="C"></path><path d="M330 310l1-2h1l2 4h0c1 0 1 0 1-1v-1h1l2 2v2c0 1 1 3 2 4 0 1 2 3 3 4l-1 1v2c1 3 3 5 4 7v2h0c-1-1-1-2-2-3h0l-7-5h-1-1 0c-1-1-2-1-4-2l-1-3v-1c0-2-1-5-2-7 1 0 1 0 2-1l1 1c0-1 0-2-1-3z" class="B"></path><path d="M334 312h0c1 0 1 0 1-1v-1h1l2 2v2h-1c0 2 0 2-1 3l-1-1c0-2-1-3-1-4z" class="n"></path><path d="M332 313l3 3 1 1h0c0 2 1 4 2 5h0-1c0 1 1 2 1 3h-1-1c0-1-1-1-2-1l-1-1v-2l-1-3v-3l1 1-1-3z" class="E"></path><path d="M330 310l1-2h1l2 4c0 1 1 2 1 4l-3-3 1 3-1-1v3l1 3v2l1 1 3 2h-1-1 0c-1-1-2-1-4-2l-1-3v-1c0-2-1-5-2-7 1 0 1 0 2-1l1 1c0-1 0-2-1-3z" class="F"></path><path d="M332 318c0-2-1-4-1-6h1v1l1 3-1-1v3z" class="B"></path><path d="M330 321v-1l3 3 1 1 3 2h-1-1 0c-1-1-2-1-4-2l-1-3z" class="a"></path><path d="M321 364l2-1-1 2h0c-1 2-6 3-6 5-2 2-2 4-3 6s0 3 0 5l-2-2-2 7c1 1 1 2 2 3h-2l-1-1v-2h-2c0 1 1 2 2 3 1 2 2 3 3 4 2 1 3 2 4 2h0c2 1 3 1 5 1h-2l-1 1-2 2v1c0 1-1 1-1 2h-1v1h-1l1-1-1-2c0 1-1 2-1 3h0c0 1 0 1-1 2v1l2-1c0 1-2 2-3 3 0 1-1 1-2 1l-1 1-3 3h0l1 1c-2 1-5 2-8 3l-1 2-3 1-1-1c-1 1-1 2-1 3 0 0 1 0 1 1-1 0-1 1-1 1v1h0c1 1 1 2 1 3v1h1 1v1c1 0 1 0 2-1v2c-2 1-4 4-7 5l-1 1-4 2c0 1-1 1-1 2 1 1 3 0 3 1-2 1-4 3-6 5l-7 3-6 3-2-2v-1c1 0 2 0 2-1h0c-1 0-1-1-2-1 0 1-1 0-2 0s-1 0-1-1c-2 0-3 0-4-1h-2 0l1 2v1c-1-2-3-3-3-5h-1v-4h0c0-1 0-1 1-2l1-2c1-1 1-1 1-2h0c1-2 3-3 5-3-1-1-3-1-5-2h-2 0-2s-1-1-2-1l-3-1h1 1 3l-1-1v-1c0-1-1-2-2-2s-3-3-4-4l1-1c-1-2-1-4-1-6 1-2 1-3 3-5h0l2-2h1v1h3 1 0 1c2 0 3-1 4-2 2-1 3-1 3-3h1c1 0 2-1 3-1l2-2 2 1c3-1 5 0 7 0v-1h4 0 0l1-2c1 0 2-1 3-1h1c4-2 9-6 11-10 1-2 2-4 2-7 1-1 1-2 2-3 0-1-1-1-2-2h1c3-2 5-3 7-4l2-2 1 1h0c2 0 2 0 3-1h1l7-3z" class="r"></path><path d="M283 408h1 0c-1 1-2 1-3 2v1h-1v-1c1-1 2-1 3-2z" class="e"></path><path d="M267 408c0-1-1-2 0-3 0-1 1-1 2-1v-2h1 0c-1 1-1 3-1 4l-2 2z" class="j"></path><path d="M304 391h1v-2h0 3c1 2 2 3 3 4-3 0-3-1-5-3l-1 1 1 1h-1l-1-1z" class="g"></path><path d="M281 398h7 1l-8 3c0-1 1-2 1-2l-1-1z" class="M"></path><path d="M308 399c1-1 1-2 1-3 1 0 2 0 3 1h2l-1 2-1 1c-1-1-1-2-3-1 0 0-1 0-1 1v-1z" class="g"></path><path d="M314 397h1v2 1c0 1-1 1-1 2h-1v1h-1l1-1-1-2h0l1-1 1-2z" class="T"></path><path d="M313 399h1c0 1-1 1-1 2v1 1h-1l1-1-1-2h0l1-1z" class="d"></path><path d="M300 394h1c0 3-1 6-3 8-1 1-2 2-3 2l1 1v1h0l-6 1c2-1 3-2 5-3 2-3 4-6 5-10z" class="j"></path><path d="M300 394v-2h1l1 1v2c0 3-2 7-5 9l-1 1-1-1c1 0 2-1 3-2 2-2 3-5 3-8h-1z" class="k"></path><path d="M298 407l1 1c-2 0-4 1-5 2-2 0-4 1-5 3h-1 0c-1 0-1 0-1-1h-3c3-2 7-3 10-4 2 0 3 0 4-1z" class="j"></path><path d="M277 398h4 0l1 1s-1 1-1 2h-5c-1 0-2-1-3-1l-3-1c3-1 5 0 7 0v-1z" class="G"></path><path d="M273 400c2-1 4-1 6 0-1 1-2 1-3 1s-2-1-3-1z" class="Z"></path><path d="M281 398h0l1 1s-1 1-1 2h-5c1 0 2 0 3-1l2-2z" class="O"></path><path d="M302 395c1 1 1 3 1 4 1 2 0 3-1 4 0 1-1 1-1 2l-1-1c-1 1-1 1-1 2s0 1 1 1l-1 1-1-1c-1 0-1-1-2-1h0v-1l1-1c3-2 5-6 5-9z" class="f"></path><path d="M296 406c2-1 4-3 5-5h1 0v1l-2 2c-1 1-1 1-1 2s0 1 1 1l-1 1-1-1c-1 0-1-1-2-1h0z" class="e"></path><path d="M302 393c1 1 3 2 4 2 1 1 1 1 1 2v2c-2 3-3 6-6 8h-1c-1 0-1 0-1-1s0-1 1-2l1 1c0-1 1-1 1-2 1-1 2-2 1-4 0-1 0-3-1-4v-2z" class="i"></path><path d="M302 393c1 1 3 2 4 2-1 2-1 3-3 4 0-1 0-3-1-4v-2z" class="e"></path><path d="M301 407v-1c1-2 3-3 4-5v-2l1-1 1 1c-2 3-3 6-6 8z" class="W"></path><path d="M273 438v1c1-1 1-2 2-3 1 2 1 4 2 6 0 2-1 2-2 3l-3 3v-3-1h-1l-1-1c1-1 1-1 1-2v-1c0-1 0-1 1-1 0-1 0-1 1-1z" class="j"></path><path d="M273 438v1c1-1 1-2 2-3 1 2 1 4 2 6h-1 0l-1 1v-2h0-1c-1 1-1 2-2 3-1-1 1-3 1-4v-2z" class="k"></path><path d="M270 443l1 1h1v1 3c-1 0-2 0-2 1v1h0 2l-6 3-2-2v-1c1 0 2 0 2-1h0c-1 0-1-1-2-1 0 1-1 0-2 0h1c3-1 5-3 7-5z" class="T"></path><path d="M284 412h3c0 1 0 1 1 1-1 1-2 1-3 2 1 0 1 0 1 1h1l-1 2h1l-1 1h-1c-1 0-1 0-2 1h0c0 1-1 2-1 3h0-1 0c-1 1-1 3-2 3h-1 0c-1 1-1 2-1 3v-1c-1-5 1-9 3-12l3-3 1-1z" class="a"></path><path d="M281 419c0-1 1-2 2-3v1l-1 1c0 1 1 1 1 1v1c0 1-1 2-1 3h0c0-1-1-1-1-2v-2h0z" class="N"></path><path d="M281 419h0v2c0 1 1 1 1 2h-1 0c-1 1-1 3-2 3h-1 0c0-3 1-5 3-7z" class="h"></path><path d="M284 412h3c0 1 0 1 1 1-1 1-2 1-3 2 1 0 1 0 1 1h1l-1 2h1l-1 1h-1c-1 0-1 0-2 1h0v-1h0c1-1 1-2 1-3h0v-1h-2 1 0l1-1h-1l-2 2h-1l3-3 1-1z" class="T"></path><path d="M284 412h3c0 1 0 1 1 1-1 1-2 1-3 2 1 0 1 0 1 1h-1c-1-1 0-1-1-2 0 0 0-1-1-1l1-1z" class="Y"></path><path d="M299 378l1 1v-1h1v-1c1 0 1 0 1 1-1 2-1 4-1 7l-1-1c-1 1-2 5-3 7s-3 4-5 6c-1 0-2 1-3 1h-1-7 0l1-2c1 0 2-1 3-1h1c4-2 9-6 11-10 1-2 2-4 2-7z" class="f"></path><path d="M268 430l1-1c0 1 1 2 1 2l-1 1c1 1 2 1 2 2 1 1 1 1 2 1h-2v1c0 1 0 4-1 4l1 1h0c0 1 0 1-1 2-2 2-4 4-7 5h-1c-1 0-1 0-1-1-2 0-3 0-4-1h0c2 0 4 0 6-1h1s0-1 1-1h0l-1-1h0l-1-1v-1c0-2 0-6 1-8h-1 2v-1h-1c1 0 1 0 2-1h-1c1-1 2-1 3-1z" class="e"></path><path d="M268 430l1 1v1c-1 0-1-1-2-1v1c-1 0-1 0-1-1h-1c1-1 2-1 3-1z" class="Y"></path><path d="M267 433c1 0 3 0 4 1l-1 1h-3v-1h0v-1z" class="W"></path><path d="M266 431c0 1 0 1 1 1v1 1h0l-3-1h-1 2v-1h-1c1 0 1 0 2-1z" class="d"></path><path d="M268 440l2-2v3c-2 2-3 4-5 4h-1s0-1 1-1h0l-1-1h0c2-1 3-2 4-3z" class="f"></path><path d="M263 441c0-2 0-6 1-8l3 1v1s0 1 1 2v2 1c-1 1-2 2-4 3l-1-1v-1z" class="r"></path><path d="M260 431c1 0 2 1 4 1h1v1h-2 1c-1 2-1 6-1 8v1l1 1h0l1 1h0c-1 0-1 1-1 1h-1c-2 1-4 1-6 1h0-2 0l1 2v1c-1-2-3-3-3-5h-1v-4h0c0-1 0-1 1-2l1-2c1-1 1-1 1-2h0c1-2 3-3 5-3z" class="i"></path><path d="M261 435h1l1 1-2 2h-1c0-2 1-2 1-3z" class="p"></path><path d="M261 443v-2l-1-1c1 0 1-1 2 0v2h1v-1 1l1 1h0-3zm-5-7c1-2 2-2 4-3v3c-1 0-2 1-3 1v1h-1-1v-1s0-1 1-1z" class="k"></path><path d="M260 431c1 0 2 1 4 1h1v1h-2-3c-2 1-3 1-4 3h-2c1-1 1-1 1-2h0c1-2 3-3 5-3z" class="X"></path><path d="M252 444v-4h0c0-1 0-1 1-2l1 1c1 0 1 0 2-1v1l-1 1c2 2 3 3 5 3h0 1 3l1 1h0c-1 0-1 1-1 1h-1c-2 1-4 1-6 1h0-2 0l1 2v1c-1-2-3-3-3-5h-1z" class="a"></path><path d="M255 444l2-1c1 1 2 1 4 2h1 0 1c-2 1-4 1-6 1l-2-2z" class="W"></path><path d="M253 444v-1c1 0 1 0 2 1l2 2h0-2 0l1 2v1c-1-2-3-3-3-5z" class="Y"></path><path d="M284 422l2-3h1c0 1-1 2-1 3h0c1 0 1 0 1-1 1 0 2 1 2 1v2h1v1h0c1 1 1 2 1 3v1h1 1v1c1 0 1 0 2-1v2c-2 1-4 4-7 5l-1 1-4 2c0 1-1 1-1 2 1 1 3 0 3 1-2 1-4 3-6 5 0-2 0-3 1-5h0c0-2-1-3 0-5v-1c0-1 0-1-1-1h0l-1-1h0c1-1 1-2 0-3h-1v-2c0-1 0-2 1-3h0 1c1 0 1-2 2-3h0 1 0 2v-1z" class="X"></path><path d="M281 429l1 1h2v1l-3 2v-4zm-2-3c1 0 1-2 2-3h0c0 2 0 3-1 5 0 1 0 3-1 5v-7z" class="M"></path><path d="M280 437h0c1-1 1-1 1-2l1-1v7h0c1 1 3 0 3 1-2 1-4 3-6 5 0-2 0-3 1-5h0c0-2-1-3 0-5z" class="e"></path><path d="M284 422c1 0 1 1 1 1l2 2h0v6h0v2 4l-4 2c0-2 0-4 1-6v-2-1h-2l-1-1c1-2 1-3 2-5l1-1v-1z" class="Z"></path><path d="M284 430v-1l1-1v7l-1-2v-2-1z" class="W"></path><path d="M285 435v1h0c1-1 1-2 2-3v4l-4 2c0-2 0-4 1-6l1 2z" class="i"></path><path d="M281 429c1-2 1-3 2-5 1 1 1 1 1 2h1c1 1 1 1 0 2l-1 1v1h-2l-1-1z" class="l"></path><path d="M284 422l2-3h1c0 1-1 2-1 3h0c1 0 1 0 1-1 1 0 2 1 2 1v2h1v1h0c1 1 1 2 1 3v1h1 1v1c1 0 1 0 2-1v2c-2 1-4 4-7 5l-1 1v-4-2h0v-6h0l-2-2s0-1-1-1z" class="N"></path><path d="M287 431h0c1-2 1-2 1-4h0 1l1 2c0 3-1 4-2 6v1l-1 1v-4-2h0z" class="G"></path><path d="M290 425h0c1 1 1 2 1 3v1h1 1v1c1 0 1 0 2-1v2c-2 1-4 4-7 5v-1c1-2 2-3 2-6v-4z" class="a"></path><path d="M307 397c1 1 1 2 0 3l1-1v1c0-1 1-1 1-1 2-1 2 0 3 1h0c0 1-1 2-1 3h0c0 1 0 1-1 2v1l2-1c0 1-2 2-3 3 0 1-1 1-2 1l-1 1-3 3h0l1 1c-2 1-5 2-8 3l-1 2-3 1-1-1c-1 1-1 2-1 3 0 0 1 0 1 1-1 0-1 1-1 1h-1v-2s-1-1-2-1c0 1 0 1-1 1h0c0-1 1-2 1-3h-1l-2 3v1h-2c0-1 1-2 1-3h0c1-1 1-1 2-1h1l1-1h-1l1-2h-1c0-1 0-1-1-1 1-1 2-1 3-2h0 1c1-2 3-3 5-3 1-1 3-2 5-2l1-1h1c3-2 4-5 6-8v-2z" class="k"></path><path d="M308 400c0-1 1-1 1-1 2-1 2 0 3 1h0c0 1-1 2-1 3-2 1-3 3-5 3 0-1 2-3 2-4v-2z" class="T"></path><path d="M287 418c1-1 1-2 3-2-1 1-2 2-2 3v1c2-1 2-1 3-2v1h0c-1 1-1 2-1 3 0 0 1 0 1 1-1 0-1 1-1 1h-1v-2s-1-1-2-1c0 1 0 1-1 1h0c0-1 1-2 1-3h-1l-2 3v1h-2c0-1 1-2 1-3h0c1-1 1-1 2-1h1l1-1z" class="a"></path><path d="M311 403h0c0 1 0 1-1 2v1l2-1c0 1-2 2-3 3 0 1-1 1-2 1v-1c-2 2-3 3-6 3l5-5c2 0 3-2 5-3z" class="h"></path><path d="M301 411c3 0 4-1 6-3v1l-1 1-3 3h0l1 1c-2 1-5 2-8 3 1-3 3-4 5-6z" class="J"></path><path d="M321 364l2-1-1 2h0c-1 2-6 3-6 5-2 2-2 4-3 6s0 3 0 5l-2-2-2 7c1 1 1 2 2 3h-2l-1-1v-2h-2c0 1 1 2 2 3h-3 0v2h-1 0c-1 0-1-1-2-2 0-1-1-2-1-4h0c0-3 0-5 1-7 0-1 0-1-1-1v1h-1v1l-1-1c1-1 1-2 2-3 0-1-1-1-2-2h1c3-2 5-3 7-4l2-2 1 1h0c2 0 2 0 3-1h1l7-3z" class="l"></path><path d="M313 367h1c0 1 0 1 1 2h0l-1 2h-1l-1 1h-1v-1c1 0 1-1 1-1 1 0 1-1 2-1l-1-2zm-4 19c0-1 0-2-1-3 0-2 1-4 1-6l1-1c0 1 0 1 1 1h0v2l-2 7z" class="M"></path><path d="M321 364l2-1-1 2h0c-1 2-6 3-6 5-2 2-2 4-3 6s0 3 0 5l-2-2v-2l3-6 1-2h0c-1-1-1-1-1-2l7-3z" class="i"></path><path d="M321 364l2-1-1 2h0c-3 1-5 2-7 4h0c-1-1-1-1-1-2l7-3z" class="N"></path><path d="M308 371l3-2-1 1c-1 1-1 2-2 3 0 2-1 3-1 4-1 0-1 1-1 1-1 2-1 4-1 6l1 2c0 1 1 2 2 3h-3 0v2h-1 0c-1 0-1-1-2-2l2-3-1-4c1-4 2-8 5-11z" class="T"></path><path d="M304 386c1 2 1 3 0 5h0c-1 0-1-1-2-2l2-3z" class="d"></path><defs><linearGradient id="u" x1="304.81" y1="383.238" x2="301.834" y2="374.01" xlink:href="#B"><stop offset="0" stop-color="#747275"></stop><stop offset="1" stop-color="#8d8c8b"></stop></linearGradient></defs><path fill="url(#u)" d="M309 367l1 1c-1 0-3 1-4 2l2 1c-3 3-4 7-5 11l1 4-2 3c0-1-1-2-1-4h0c0-3 0-5 1-7 0-1 0-1-1-1v1h-1v1l-1-1c1-1 1-2 2-3 0-1-1-1-2-2h1c3-2 5-3 7-4l2-2z"></path><path d="M301 385h1c1-1 1-1 1-3l1 4-2 3c0-1-1-2-1-4z" class="h"></path><path d="M301 375s1-1 1-2h2c-1 2-1 3-2 5 0-1 0-1-1-1v1h-1v1l-1-1c1-1 1-2 2-3z" class="a"></path><path d="M309 367l1 1c-1 0-3 1-4 2s-1 2-2 3h-2c0 1-1 2-1 2 0-1-1-1-2-2h1c3-2 5-3 7-4l2-2z" class="Y"></path><path d="M270 402c1 0 2 0 3 1 2 2 3 4 4 6v5 1c-1 2-2 5-4 7-1 1-2 3-3 3-2 2-4 3-7 4h-1l1 1h0c1 0 1 0 2 1h1c-1 1-1 1-2 1-2 0-3-1-4-1-1-1-3-1-5-2h-2 0-2s-1-1-2-1l-3-1h1 1 3l-1-1v-1c0-1-1-2-2-2s-3-3-4-4l1-1c-1-2-1-4-1-6 1-2 1-3 3-5h0l2-2h1v1h3 1 0l3 1c1 1 1 0 2 0v1 1l-2 1c0 1-1 1 0 2l1-1c1-1 1-1 2-1l1-1c1 0 1-1 2-1v-1c1-1 1-1 1-2h1c1 2-1 4 1 5h1v-2l2-2c0-1 0-3 1-4h0z" class="k"></path><path d="M252 420c-1-1-1-2-1-4 0-1 1-2 1-3l1 1c0 1 0 2 1 2l-1 3-1 1z" class="G"></path><path d="M270 402c1 2 2 6 1 9h0 0l-1-1h-1v-3-1c0-1 0-3 1-4z" class="d"></path><path d="M269 407l1 1c1 1 1 2 1 3l-1-1h-1v-3z" class="X"></path><path d="M253 419l2 1c1 1 3 2 4 2h2 0c-1 1-2 1-4 1s-3-1-5-3l1-1z" class="P"></path><path d="M247 407l2-2h1v1h3 1 0l3 1c1 1 1 0 2 0v1 1h-6c-1 0-1-1-2-1 0-1-1-1-1-1v-1c-1 1-2 1-3 1zm-2 11c1 2 2 3 3 4h2c0 1 0 1 1 1h3c0 1 1 1 2 1h1 2c2-1 4 0 5-1h0l1-1h2l-2 1c-1 1-2 1-4 1l-1 1h-4c-1-1-2-1-4-1l-1 1-1 1v-1c0-1-1-2-2-2s-3-3-4-4l1-1z" class="g"></path><path d="M258 412l1 1v1h1 0l1 1-1 1h-1 0c0 1 0 1 1 1l-1 1-1-1-2 1-2-2c-1 0-1-1-1-2l2 1c0-1 1-2 1-3h0 1 1z" class="T"></path><path d="M258 412l1 1c0 1 0 2-1 2h-1v-3h1zm-5 2l2 1c1 0 1 1 2 1l1 1-2 1-2-2c-1 0-1-1-1-2z" class="g"></path><path d="M261 415l1-1s1 0 1 1v2c0 1 0 1 1 1 1 1 2 0 3 0-1 1-1 2-3 3v-1h-2l1-1-1-1c0 1-1 1-1 1h-2c-1 0-2 0-3-1l2-1 1 1 1-1c-1 0-1 0-1-1h0 1l1-1z" class="X"></path><path d="M254 416l2 2c1 1 2 1 3 1h2s1 0 1-1l1 1-1 1h2v1c-1 1-2 1-3 1h0-2c-1 0-3-1-4-2l-2-1 1-3z" class="Z"></path><path d="M254 416l2 2c1 1 2 1 3 1h-3v1h-1l-2-1 1-3z" class="l"></path><path d="M269 406v1 3h1l1 1h0c0 2-2 5-3 7h-1c-1 0-2 1-3 0-1 0-1 0-1-1h1c1-1 1-2 2-4 0-1 0-2 1-3v-2l2-2z" class="b"></path><path d="M269 406v1 3h1v1c-1 1-2 1-2 3 0 0-1 1-1 2l-1-1c1 0 1-1 1-2h-1c0-1 0-2 1-3v-2l2-2z" class="f"></path><path d="M260 410l1-1c1 0 1-1 2-1v-1c1-1 1-1 1-2h1c1 2-1 4 1 5h1c-1 1-1 2-1 3-1 2-1 3-2 4h-1v-2c0-1-1-1-1-1l-1 1-1-1h0-1v-1l-1-1c1-1 2-1 2-2h0z" class="j"></path><g class="d"><path d="M260 410l1 1-1 3h-1v-1l-1-1c1-1 2-1 2-2z"></path><path d="M260 414c1-1 2-2 3-4h1c0 1 1 1 1 2l-1 2-1 1c0-1-1-1-1-1l-1 1-1-1z"></path></g><path d="M269 419h0l1-2c1 0 1-1 1-1v-1c1-1 1-1 1-2v-1-1l1-1c1-1 1-1 2-1v2h1c0-1 0-1 1-2v5 1c-1 2-2 5-4 7l-1-1-3 3-1-1-1 1-2-1 2-1c1-1 1-2 2-3z" class="i"></path><path d="M272 415c1-2 1-3 1-4h1v3l1 1v1l-1 2-1-1c0-1 0-2-1-2z" class="T"></path><path d="M272 415c1 0 1 1 1 2l1 1c0 1-1 2-2 2v1l-3 3-1-1-1 1-2-1 2-1c1-1 1-2 2-3 1 0 1-1 2-1 1-1 1-3 1-3z" class="d"></path><path d="M268 423l1-1c0-1 2-2 3-2h0v1l-3 3-1-1z" class="b"></path><path d="M272 421l1 1c-1 1-2 3-3 3-2 2-4 3-7 4h-1l1 1h0c1 0 1 0 2 1h1c-1 1-1 1-2 1-2 0-3-1-4-1-1-1-3-1-5-2h-2 0-2s-1-1-2-1l-3-1h1 1 3l-1-1 1-1 1-1c2 0 3 0 4 1h4l1-1c2 0 3 0 4-1l2 1 1-1 1 1 3-3z" class="h"></path><path d="M265 423l2 1-2 1c-3 0-3 1-6 0h-2-1 4l1-1c2 0 3 0 4-1z" class="X"></path><path d="M272 421l1 1c-1 1-2 3-3 3s-3 1-5 2c1-1 3-2 4-3l3-3z" class="T"></path><path d="M251 425h2 2 0l-1 1h0-1c0 1 0 1 1 2l-1 1h-2s-1-1-2-1l-3-1h1 1 3l-1-1 1-1z" class="O"></path><path d="M253 429l1-1c-1-1-1-1-1-2h1 0l2 2c2 0 5 0 7 1h-1l1 1h0c1 0 1 0 2 1h1c-1 1-1 1-2 1-2 0-3-1-4-1-1-1-3-1-5-2h-2 0z" class="J"></path><path d="M255 429h7l1 1h0c1 0 1 0 2 1h1c-1 1-1 1-2 1-2 0-3-1-4-1-1-1-3-1-5-2z" class="T"></path><path d="M342 302l1-3h1v3h1c0-1 0-1 1-2h0v4c1 0 1 0 1-1h1v2l2 3h0v-1l1-1v3c1 1 1 3 2 4s1 2 1 3c2 1 3 2 5 2l2 1 1 1 1 1 2-1h1l1 1h0c1-1 3-2 4-3h1v1c0 1 1 2 2 2h0l1 1h0c-1 1-1 0-2 0l1 2c2 1 3 3 4 5h0c1 0 1 1 1 1h1c1 0 1-1 1-2v2l1 1v2c-1 2 1 4 0 5 0 4-2 5-4 8h0c-1 1-2 2-4 3-1 0-1 1-1 2l-2 1c-1 2-2 4-2 6-1 1-3 3-3 4-1 2-2 4-3 7 0 2 0 4 1 7 1 0 1 0 1-1h1c0 1 1 3 1 4v1h0v3c1 1 1 2 1 4l-1 2h-1c0 1-1 2-1 2v2 1c-1 0-4 1-5 3 1 0 6-2 7-3v1l-2 1c-2 1-5 2-7 2h0c-1 0-2 0-3 1 0 0 0 1-1 1h0c-1 1-2 1-4 1-3 1-7 1-10 3-6 2-12 4-18 7h-1-2l-3 2c-1 1-2 1-3 1h0-2l1-1v-1c-2 0-4 1-6 1h0l-2 1-1-1h0l3-3 1-1c1 0 2 0 2-1 1-1 3-2 3-3l-2 1v-1c1-1 1-1 1-2h0c0-1 1-2 1-3l1 2-1 1h1v-1h1c0-1 1-1 1-2v-1l2-2 1-1h2c-2 0-3 0-5-1h0c-1 0-2-1-4-2-1-1-2-2-3-4-1-1-2-2-2-3h2v2l1 1h2c-1-1-1-2-2-3l2-7 2 2c0-2-1-3 0-5s1-4 3-6c0-2 5-3 6-5h0l1-2 6-4 1 1 2-1-1 2h0c3-2 5-3 7-5l3-5 2-3v1c-1 1 0 2 0 2 1-1 1-2 2-4v3h0c1-1 1-3 2-4l3-3v4-1c1-2 2-4 2-7v-1-1c1-2 1-4 2-5-1-2-2-4-2-6-1 0-1-1-2-2h1c1 0 2 0 3-1-4-2-7-5-10-9l-1-4c-1-2-1-6-1-8z" class="k"></path><path d="M349 385l1 1c0 1 0 3-1 4v-1-4z" class="j"></path><path d="M372 348c1 0 1 0 2 1-1 0-1 1-1 2l-2 1 1-2-1-1 1-1z" class="e"></path><path d="M339 386l3-8v5c-1 1-1 3-2 4l-1-1z" class="T"></path><path d="M362 361h1c-3 3-6 7-10 8h-1c0-2 1-2 1-3 2-1 3-1 4-1 2 0 4-3 5-4z" class="j"></path><path d="M306 386h2v2l1 1h2 0c3 4 7 5 12 6h-8 0c-1 0-2-1-4-2-1-1-2-2-3-4-1-1-2-2-2-3z" class="h"></path><path d="M369 345h1s0-1 1-1v1 1h1v1 1l-1 1h-1l-1 1-1 1c-1 1-2 1-3 2h-1l3-6h0 1l1-2z" class="e"></path><path d="M368 347l1 1v2l-1 1-1-2 1-2z" class="Y"></path><path d="M369 345h1s0-1 1-1v1 1h1v1 1l-1 1h-1l-1 1v-2l-1-1h0l1-2z" class="W"></path><path d="M371 345v1h1v1 1l-1 1h-1-1l2-4z" class="X"></path><path d="M327 405c-1-1 0-2 0-2v-1-2s1 0 1-1 1-4 1-6c1 0 3-1 4-1 0 0 0 1 1 1h1 1v2l-1 4c0-1-1-1-1-1v-1h-1-1 0l1-2h-1c-1 0-1 0-2 1h0-1c-1 2-1 8-2 9z" class="T"></path><path d="M332 397l1-1c1-1 1-1 2-1h1l-1 4c0-1-1-1-1-1v-1h-1-1 0z" class="Y"></path><defs><linearGradient id="v" x1="348.513" y1="384.765" x2="339.254" y2="394.414" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#494748"></stop></linearGradient></defs><path fill="url(#v)" d="M341 394h1c0-1 0-1 1-1 0-2 1-3 1-5s1-4 2-6h0c1 1 1 4 1 6-1 1 0 1-1 2-1 2-1 4-2 6l-2 2v-1l-1-1v-2z"></path><path d="M339 386l1 1c-1 1-1 3-1 5l-1 3c0 2-1 3-1 5l-1 1-1 1c0 1-1 1-3 2l1-1c1-1 1-2 2-3h0v-1l1-4v-2l-1-2c0-2 2-2 3-3l1-2z" class="X"></path><path d="M351 357c1 0 1 0 2-1l1 1-4 3 1 1h0 1c-2 1-4 2-4 4-1 1-1 4 0 5 0 1 2 2 3 3h0c-1 0-2 0-3-1-2-1-2-2-3-4h0c0-1 0-2-1-3v-1c2-3 5-5 7-7z" class="V"></path><path d="M351 357c1 0 1 0 2-1l1 1-4 3c-2 1-4 2-5 5v3c0-1 0-2-1-3v-1c2-3 5-5 7-7z" class="d"></path><defs><linearGradient id="w" x1="358.95" y1="351.232" x2="353.921" y2="361.913" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#b5b4b7"></stop></linearGradient></defs><path fill="url(#w)" d="M365 348c1-1 1-2 2-2v1l-3 6c-4 4-7 6-12 8h-1 0l-1-1 4-3c3-1 6-2 8-5 1-1 1-3 1-4s0-1 1-2l1 2z"></path><path d="M364 346l1 2c0 2-1 3-3 4 1-1 1-3 1-4s0-1 1-2z" class="W"></path><path d="M340 387c1-1 1-3 2-4v4c1 1 1 2 0 2l-1 5v2l-1 1c0 1-1 2-2 3l-1 1v1h1c0-1 1-1 1-1l1-1h0c1 0 1-1 2-2h1 0 0c1 0 1-1 2-2v1c-1 1-3 3-5 4-1 1-2 2-4 3-1 0-1 0-2 1 1-2 2-3 3-5 0-2 1-3 1-5l1-3c0-2 0-4 1-5z" class="d"></path><path d="M340 387c1-1 1-3 2-4v4c1 1 1 2 0 2v-2h0c-2 2-2 4-2 6h-1v-1c0-2 0-4 1-5z" class="Y"></path><path d="M327 405c1-1 1-7 2-9h1 0c1-1 1-1 2-1h1l-1 2h0 1 1v1s1 0 1 1v1h0c-1 1-1 2-2 3l-1 1c2-1 3-1 3-2l1-1 1-1c-1 2-2 3-3 5h-2c-2 0-3 1-5 2-1 0-1 0-2 1v-1l2-2z" class="b"></path><path d="M332 397h1 1v1s1 0 1 1v1l-2 1c0 1 0 1-1 1h0v-5z" class="T"></path><path d="M333 397h1v1s1 0 1 1v1l-2 1v-4z" class="d"></path><path d="M340 362c1 0 3-1 4-2v4 1c-1 2-1 3-1 6 1 1 1 3 2 4s2 2 2 4c-3-1-6-3-8-7v-1c-1-2-1-3-1-5 1-2 1-3 2-4z" class="S"></path><path d="M340 362c1 0 3-1 4-2v4 1c-1 2-1 3-1 6v-1-4l-1-1c-2 0-2 1-3 2v4c-1-2-1-3-1-5 1-2 1-3 2-4z" class="J"></path><path d="M359 389v1l1-2h1l-1 1-1 2v1 1 1c1 0 2 0 3-1h1c1 0 1 0 2 1-1 0-4 1-5 3 1 0 6-2 7-3v1l-2 1c-2 1-5 2-7 2h0c-1 0-2 0-3 1 0 0 0 1-1 1h0c-1 1-2 1-4 1-3 1-7 1-10 3-6 2-12 4-18 7h-1-2s1 0 2-1v-1h1c1 1 2 0 4 0v-1h1c1 0 2-1 3-1l1-1h1 0 1l1-1h1c1-1 1-1 2-1h1v-1h1 1c1 0 1-1 2-1h1 1l1-1h3v-1c3 1 6-1 8-2 0-1 0-2-1-2 0 0 0-1-1-1l2-2c1-1 1-3 2-4h1z" class="Y"></path><path d="M354 391c-1-2-1-5-1-7v-7h2 1v-1h0 1v2h-1v5h0c1 1 1 2 1 3l2-1v3c-1 0-1 1-1 1-1 1-1 3-2 4l-2 2c1 0 1 1 1 1h0l-2 2c-1 0-2 1-3 1l-2 1h-2v-1c4-1 6-5 8-8z" class="e"></path><path d="M354 391v-1c1-1 1-2 1-3v-2h1c0 1 0 1 1 2 0 1 0 2-1 2v1l-2 4c-1 1-1 1-1 3 0 1-1 1-2 1l-1 1-2 1h-2v-1c4-1 6-5 8-8z" class="i"></path><path d="M363 369c0 2 0 4 1 7 1 0 1 0 1-1h1c0 1 1 3 1 4v1h0v3c1 1 1 2 1 4l-1 2h-1c0 1-1 2-1 2v2 1c-1-1-1-1-2-1h-1c-1 1-2 1-3 1v-1-1-1l1-2 1-1h-1l-1 2v-1h-1s0-1 1-1v-3l-2 1c0-1 0-2-1-3h0v-5h1v3 1l1-4v-3l1-1 1 1h1v-3c1-1 2-2 2-3z" class="g"></path><path d="M358 378c1 0 2 0 3-1 1 2 1 6 0 8 0 2-1 3-2 4h-1s0-1 1-1v-3l-2 1c0-1 0-2-1-3h0v-5h1v3 1l1-4z" class="T"></path><path d="M364 376c1 0 1 0 1-1h1c0 1 1 3 1 4v1h0v3c1 1 1 2 1 4l-1 2h-1c0 1-1 2-1 2v2 1c-1-1-1-1-2-1h-1c-1 1-2 1-3 1v-1-1c3-4 4-11 5-16h0z" class="W"></path><path d="M364 376c1 0 1 0 1-1h1c0 1 1 3 1 4l-3-3h0z" class="M"></path><path d="M367 380h0v3c-1 0-1 1-1 1 0 1 0 1-1 1v-4c1-1 1-1 2-1z" class="T"></path><path d="M359 393c2-1 3-1 4-3 0-1 1-2 2-3 0 0 1-1 1-2h1v2c-1 0-1 1-1 1v1c0 1-1 2-1 2v2 1c-1-1-1-1-2-1h-1c-1 1-2 1-3 1v-1z" class="a"></path><path d="M317 397l7-1h0v2c0 1-1 2-1 3-1 2-2 5-3 7s-4 4-4 5c-1 1-2 1-3 1h0-2l1-1v-1c-2 0-4 1-6 1h0l-2 1-1-1h0l3-3 1-1c1 0 2 0 2-1 1-1 3-2 3-3l-2 1v-1c1-1 1-1 1-2h0c0-1 1-2 1-3l1 2-1 1h1v-1h1c0-1 1-1 1-2v-1l2-2z" class="b"></path><path d="M315 406c0-1 0-1 1-2h1 0c0 1-1 1-1 2h1l-2 2h0l-1-1h1v-1z" class="X"></path><path d="M317 406h1 1l-1 3-1 1c0-1-1-2-2-2l2-2z" class="N"></path><path d="M317 397l7-1h0v2c0 1-1 2-1 3 0-1 0-1-1-1l-2 2-3 3c0-1 0-1 1-2h-1 0v-1h-1c0-1 0-1-1-1h0c0 1-1 1-1 1 0-1 1-1 1-2v-1l2-2z" class="d"></path><path d="M317 397l7-1h0v2c-1 0-2 1-3 1-2 0-3 0-4 1l-1 1v-1s0-1-1-1l2-2z" class="f"></path><path d="M315 406v1h-1l1 1h0c1 0 2 1 2 2l-1 2-3 2h-2l1-1v-1c-2 0-4 1-6 1h0l-2 1-1-1h0l3-3 1-1c1 0 2 0 2-1h1c1 0 3-1 5-2z" class="N"></path><path d="M306 410v1h1 2 1c0-1 1-1 2-1-2 1-5 2-6 3l-2 1-1-1h0l3-3z" class="h"></path><path d="M315 408c1 0 2 1 2 2l-1 2-3 2h-2l1-1v-1c-2 0-4 1-6 1h0c1-1 4-2 6-3 1 0 1 0 2-1l1-1h0z" class="O"></path><path d="M315 408c1 0 2 1 2 2l-1 2c-1-2-1-2-2-3l1-1h0z" class="M"></path><defs><linearGradient id="x" x1="351.183" y1="353.143" x2="341.709" y2="349.802" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#x)" d="M352 339c1 1 1 2 2 2v1 2l1-1h0 1l1 1h0 0c0 1 0 2 1 2l1-1c-1 3-2 5-2 7-1 2-2 3-4 4-1 1-1 1-2 1-2 2-5 4-7 7v-4c-1 1-3 2-4 2-1 1-1 2-2 4v-1c-3 2-5 5-7 7v1h1c-1 1-1 1-2 1 0 1 1 2 1 2v2c0 1-1 1-1 1-1 0-2-1-3-1-1-1-2-3-2-5 0-3 2-6 4-8 1-1 1-3 2-4 3-2 5-3 7-5l3-5 2-3v1c-1 1 0 2 0 2 1-1 1-2 2-4v3h0c1-1 1-3 2-4l3-3v4-1c1-2 2-4 2-7z"></path><path d="M345 350c2 0 2 0 3-1-1 3-3 5-6 7l2-3c0-1 1-1 1-2v-1h0z" class="a"></path><path d="M347 346l3-3v4l-2 2c-1 1-1 1-3 1 1-1 1-3 2-4z" class="X"></path><path d="M341 351l2-3v1c-1 1 0 2 0 2-3 5-6 7-10 11l-4 3c1-1 1-3 2-4 3-2 5-3 7-5l3-5z" class="o"></path><path d="M355 343h1l1 1h0 0c0 1 0 2 1 2l1-1c-1 3-2 5-2 7-1 2-2 3-4 4-1 1-1 1-2 1-2 2-5 4-7 7v-4c-1 1-3 2-4 2l1-2h0l2-1c1-2 2-3 4-4 2-2 4-4 5-6 0-1 1-2 2-2 1-1 1-3 1-4z" class="X"></path><path d="M352 349l2 1c-2 2-3 4-5 4l-2 1h0c2-2 4-4 5-6z" class="T"></path><path d="M357 344h0c0 1 0 2 1 2-1 3-3 5-4 7 0-2 1-3 1-4 0-2 1-4 2-5z" class="f"></path><path d="M355 343h1l1 1h0c-1 1-2 3-2 5l-1 1-2-1c0-1 1-2 2-2 1-1 1-3 1-4z" class="e"></path><path d="M343 359l1-1h1c1-1 2-1 2-1l3-1 1 1c-2 2-5 4-7 7v-4c-1 1-3 2-4 2l1-2h0l2-1z" class="M"></path><path d="M333 362c3 0 4-1 5-2h1c0 1 0 2-1 3l3-3-1 2c-1 1-1 2-2 4v-1c-3 2-5 5-7 7v1h1c-1 1-1 1-2 1 0 1 1 2 1 2v2c0 1-1 1-1 1-1 0-2-1-3-1-1-1-2-3-2-5 0-3 2-6 4-8l4-3z" class="g"></path><path d="M328 369h2c-1 2-1 4-2 5l-1-1v-3h0l1-1zm10-6l3-3-1 2c-1 1-1 2-2 4v-1c-3 2-5 5-7 7h-1c1-3 5-5 6-7 1-1 1-2 2-2z" class="Y"></path><path d="M329 359l1 1 2-1-1 2h0c-1 1-1 3-2 4-2 2-4 5-4 8 0 2 1 4 2 5 1 0 2 1 3 1 0 0 1 0 1-1v-2s-1-1-1-2c1 0 1 0 2-1l3-3h1v2l1 1c1 2 1 3 1 6s-1 5-3 8c-2 2-5 3-8 3-3 1-7 0-10-2s-4-4-4-7c0-2-1-3 0-5s1-4 3-6c0-2 5-3 6-5h0l1-2 6-4z" class="b"></path><path d="M330 387h-3-1-1 0c-1 0-1-1-2-2h1 4c2 0 3-1 4-1s1 0 2-1h1 0c-1 2-3 3-5 4z" class="W"></path><path d="M324 381c2 0 5 1 6 1s2-2 3-2c1-1 2-1 2-1 0 1 0 2-1 3h-1c-2 2-5 2-7 2h-1c-1-1-1-2-1-3z" class="k"></path><path d="M335 387c-2 0-3 1-5 1 0 0-1 0-2 1h0-3-2l-1-1h-1s-1 0-2-1c-3-2-6-5-6-9 0-1 0-1 1-1 0 0 0 1 1 1h0l1 4c0 1 1 2 1 2v1c2 2 5 2 8 3 2 0 3 0 5-1s4-2 5-4h1c0-1 1-2 1-3v-2h1v1c0 3-1 5-3 8z" class="M"></path><path d="M332 373l3-3h1v2l1 1c-2 2 1 4-2 6 0 0-1 0-2 1-1 0-2 2-3 2s-4-1-6-1l-1-1h1s1 0 1-1h1c2 1 3 1 5 1v-1h-1s1 0 1-1v-2s-1-1-1-2c1 0 1 0 2-1z" class="T"></path><path d="M332 373l3-3h1v2 2s-1 0-1 1h0c-1 0-1 1-1 2s-1 1-1 1c-1 1-2 1-2 1h-1s1 0 1-1v-2s-1-1-1-2c1 0 1 0 2-1z" class="i"></path><path d="M316 370v2 2 2l-1 2h0c-1 0-1-1-1-1-1 0-1 0-1 1 0 4 3 7 6 9 1 1 2 1 2 1h1l1 1h2 3 0c1-1 2-1 2-1 2 0 3-1 5-1-2 2-5 3-8 3-3 1-7 0-10-2s-4-4-4-7c0-2-1-3 0-5s1-4 3-6z" class="h"></path><path d="M329 359l1 1 2-1-1 2h0c-1 1-1 3-2 4-2 2-4 5-4 8 0 2 1 4 2 5 1 0 2 1 3 1h1v1c-2 0-3 0-5-1h-1c0 1-1 1-1 1h-1l1 1c0 1 0 2 1 3h-1c-1-1-4-5-4-7v-1h0c-1 3 0 4 1 6h0-1c-2-1-2-2-2-4-1-1-2-1-2-2v-2-2-2c0-2 5-3 6-5h0l1-2 6-4z" class="g"></path><path d="M320 369l1 1c-1 2-2 2-3 3v-2l2-2z" class="i"></path><path d="M316 374l2-3v2 5c-1-1-2-1-2-2v-2z" class="d"></path><path d="M329 359l1 1 2-1-1 2-2 2-2 1c-1-1-2 0-3 1s-3 2-4 4l-2 2-2 3v-2-2c0-2 5-3 6-5h0l1-2 6-4z" class="X"></path><path d="M330 360l2-1-1 2-2 2c-1-1-2 0-3-1 1-1 2-2 4-2z" class="M"></path><defs><linearGradient id="y" x1="324.378" y1="367.188" x2="330.5" y2="373.621" xlink:href="#B"><stop offset="0" stop-color="#838284"></stop><stop offset="1" stop-color="#9b9b9a"></stop></linearGradient></defs><path fill="url(#y)" d="M329 363l2-2h0c-1 1-1 3-2 4-2 2-4 5-4 8 0 2 1 4 2 5 1 0 2 1 3 1h1v1c-2 0-3 0-5-1-2-2-3-3-3-6 0-4 2-7 4-9l2-1z"></path><path d="M342 302l1-3h1v3h1c0-1 0-1 1-2h0v4c1 0 1 0 1-1h1v2l2 3h0v-1l1-1v3c1 1 1 3 2 4s1 2 1 3c2 1 3 2 5 2l2 1 1 1 1 1 2-1h1l1 1h0c1-1 3-2 4-3h1v1c0 1 1 2 2 2h0l1 1h0c-1 1-1 0-2 0l1 2c2 1 3 3 4 5h0c1 0 1 1 1 1h1c1 0 1-1 1-2v2l1 1v2c-1 2 1 4 0 5 0 4-2 5-4 8h0c-1 1-2 2-4 3-1-1-1-1-2-1v-1-1h-1v-1-1c-1 0-1 1-1 1h-1l-1 2h-1 0v-1c-1 0-1 1-2 2l-1-2c-1 1-1 1-1 2s0 3-1 4c-2 3-5 4-8 5l-1-1c2-1 3-2 4-4 0-2 1-4 2-7l-1 1c-1 0-1-1-1-2h0 0l-1-1h-1 0l-1 1v-2-1c-1 0-1-1-2-2v-1-1c1-2 1-4 2-5-1-2-2-4-2-6-1 0-1-1-2-2h1c1 0 2 0 3-1-4-2-7-5-10-9l-1-4c-1-2-1-6-1-8z" class="O"></path><path d="M363 326l2 1c1 1 2 6 2 7v1c-1 2-1 4-1 6-1-3-1-6-2-9 0-1-1-3-1-4v-2z" class="S"></path><path d="M348 315l1 1c1 1 3 3 5 4h4l1 1c0 1 3 4 4 5v2c-2-2-4-5-6-6-1 0-1-1-2 0h-1c-2-2-4-4-6-7h0z" class="P"></path><path d="M359 318l2 1 1 1 1 1h-1c0 2 3 3 4 4v4 1h1v5-1c0-1-1-6-2-7l-2-1c-1-1-4-4-4-5l-1-1 1-2z" class="J"></path><path d="M359 318l2 1 1 1-3 1-1-1 1-2z" class="H"></path><path d="M357 323c3 2 5 5 6 9h0c1 4 1 9 0 12 0 1 0 2-1 2h0v-4c1-5-2-12-5-16l-1-2 1-1z" class="L"></path><path d="M357 326c3 4 6 11 5 16v4c-2 3-3 6-5 9 3-2 4-4 6-7 0 1 0 3-1 4-2 3-5 4-8 5l-1-1c2-1 3-2 4-4 0-2 1-4 2-7l-1 1c-1 0-1-1-1-2l1-4v-7c1 0 1 0 1-1-1-1-2-4-2-6h0z" class="f"></path><path d="M358 340l1 1v4l-1 1c-1 0-1-1-1-2l1-4z" class="T"></path><path d="M354 323l2 1 1 2h0c0 2 1 5 2 6 0 1 0 1-1 1v7l-1 4h0 0l-1-1h-1 0l-1 1v-2-1c-1 0-1-1-2-2v-1-1c1-2 1-4 2-5-1-2-2-4-2-6-1 0-1-1-2-2h1c1 0 2 0 3-1z" class="p"></path><path d="M354 332v3c1 2 0 4 0 6-1 0-1-1-2-2v-1-1c1-2 1-4 2-5z" class="j"></path><path d="M354 323l2 1 1 2h0c0 2 1 5 2 6 0 1 0 1-1 1h-2c-1-1-1-4-2-6l-3-3c1 0 2 0 3-1z" class="e"></path><path d="M367 321c1-1 3-2 4-3h1v1c0 1 1 2 2 2h0l1 1h0c-1 1-1 0-2 0l-1-1-1 2h0c-1 0-1 1-1 1 2 3 2 9 1 12v1l-2 8-1 2h-1 0v-1c-1 0-1 1-2 2l-1-2 4-7v-3c1-4 0-8-2-11-1-1-4-2-4-4h1l2-1h1l1 1h0z" class="k"></path><path d="M368 339c0 2-1 2-1 4l-1 1h1 1v1l-1 1c-1 0-1 1-2 2l-1-2 4-7z" class="e"></path><path d="M367 321c1-1 3-2 4-3h1v1c0 1 1 2 2 2h0l1 1h0c-1 1-1 0-2 0l-1-1-1 2h0c-1 0-1 1-1 1 2 3 2 9 1 12v1c0-4 0-6-1-9-2-2-2-5-3-7z" class="T"></path><path d="M367 321c1-1 3-2 4-3h1v1h-1c-1 0-2 1-2 2 0 2 1 5 1 7-2-2-2-5-3-7z" class="Y"></path><path d="M342 302l1-3h1v3h1c0-1 0-1 1-2h0v4c1 0 1 0 1-1h1v2l2 3h0v-1l1-1v3c1 1 1 3 2 4s1 2 1 3c2 1 3 2 5 2l-1 2h-4c-2-1-4-3-5-4l-1-1h0c2 3 4 5 6 7h1l2 1-1 1-2-1c-4-2-7-5-10-9l-1-4c-1-2-1-6-1-8z" class="O"></path><path d="M346 308l1-1c1 2 2 3 3 5h0c-1 1-1 2-1 4l-1-1h0v-1c-1-2-1-4-2-6z" class="D"></path><path d="M348 305l2 3h0v-1l1-1v3c1 1 1 3 2 4s1 2 1 3c-2-1-3-2-4-4h0c-1-2-2-3-3-5l-1 1h0v-1-1l1 1h1v-2z" class="J"></path><path d="M349 316c0-2 0-3 1-4 1 2 2 3 4 4s3 2 5 2l-1 2h-4c-2-1-4-3-5-4z" class="F"></path><path d="M343 310c0-2-1-2 1-4 1 1 1 5 2 6s1 2 2 3c2 3 4 5 6 7h1l2 1-1 1-2-1c-4-2-7-5-10-9l-1-4z" class="D"></path><path d="M371 323l1-2 1 1 1 2c2 1 3 3 4 5h0c1 0 1 1 1 1h1c1 0 1-1 1-2v2l1 1v2c-1 2 1 4 0 5 0 4-2 5-4 8h0c-1 1-2 2-4 3-1-1-1-1-2-1v-1-1h-1v-1-1c-1 0-1 1-1 1h-1l2-8v-1c1-3 1-9-1-12 0 0 0-1 1-1h0z" class="N"></path><path d="M371 323l1-2 1 1 1 2c2 1 3 3 4 5h0c0 1 1 2 1 3h-2c0-3-3-4-3-6l-1-3v-1c-1 1-1 1-2 1z" class="X"></path><path d="M371 337v-1c1-3 1-9-1-12 0 0 0-1 1-1 0 1 0 1 1 2 2 2 2 8 2 11-1 2-2 6-3 8-1 0-1 1-1 1h-1l2-8zm10-9v2l1 1v2c-1 2 1 4 0 5 0 4-2 5-4 8h0c-1 1-2 2-4 3-1-1-1-1-2-1v-1-1l3-6h1v3h1c2-3 2-8 2-11 0-1-1-2-1-3 1 0 1 1 1 1h1c1 0 1-1 1-2z" class="b"></path><path d="M376 344c1 1 1 1 2 1v1c-1 1-2 2-4 3-1-1-1-1-2-1v-1c2 0 3-1 4-3z" class="i"></path><path d="M376 344h1 1c1-1 2-4 3-5l1-1c0 4-2 5-4 8h0v-1c-1 0-1 0-2-1z" class="T"></path><path d="M319 85l3 1h1c1 1 2 1 4 2v1c1 0 3 2 4 3h0c1 0 2 1 3 2 0-1 0 0-1-1 0-1 0-1 1-1l-1-1h0l8 4 8 3c4 2 8 3 12 4 2 1 7 1 9 3 0-2 0-4 2-4 1-1 2-1 2-1 2 1 3 3 4 4-1 2-2 4-5 4l-1 1 1 1 3 3 1 1c1 0 2 1 3 1h2l1 1c3 1 6 2 8 3l1-2v2l-1 1v1c3 1 5 2 8 4 3 3 9 12 12 11v-1l2-2v-1h2c1 0 1 0 2 1l1-1s1-1 1-2l2 2 1 1c2-1 3-1 5-2 1 0 2-1 3-1 2-1 2-2 3-4h0l-1-1c-3 0-3-3-6-4-1 0-1-1-2-1l1-1h1 0 1l2 2c3 2 5 5 7 7 6 3 12 7 19 6 5-1 8-3 11-7 2-3 3-9 3-13-1-2-2-4-4-5-1-1-3-2-5-2v-5h27 5 2c0 1 0 1 1 1v3c-1 1-3 1-4 1-2 1-5 5-6 7-2 6-2 13-2 20v19l1 43v85c0 5 0 11 4 15 2 2 3 2 5 3h2c1 0 1 1 1 1v2c-2 1-41 1-47 1h-14-8c-1-1 0-3 0-4s3-1 4-1c10-3 10-14 9-22 0-8-2-16-3-24-6-31-16-61-35-87-6-8-12-16-19-23-25-22-57-30-89-28-25 1-50 10-67 29-16 18-27 40-33 63l-3 12c-1 4-1 7-2 10l-3 21c-2 10-3 20-3 30l-2 64 1 62 3 37c2 26 5 53 14 77l1 2 1 4c2 4 3 7 5 11l2 4c2 1 1-1 2-1 1-1 2-1 3-1 1-1 4-1 5-2v-1h4 0l1 1h3c0 1 1 2 2 2l1 1c2 0 3 1 5 1h3c2 0 3 0 4 1l2-2 3-2v-3h1v2l1 2c0 1 0 2 1 3 0-1 0-1 1-1 2-3 5-4 7-7v-1l1 1 1 5c1 3 2 5 4 7l1 1h-1-1c0 1 0 1-1 2l-1 1c1 1 3 2 5 3l1 1c1 1 3 2 4 2l8 4c1 0 3 1 4 2h0 1c0 1 1 1 2 2-1 0-2 1-4 1 0-1-1 0-2 0h-1v1h0 2c-1 1-2 1-3 1l1 1h2c1 1 1 1 1 2v1h0-1c1 1 1 1 2 1v1 1h-1l1 1c-1 0-1 1-1 1h-3c-2 0-7 0-8 1v1l-1 1c-3 1-5 2-8 4-2 2-3 4-4 7l5 2 6 2c1 1 3 2 4 2l5 1 1-1c0-1 1-1 1-1h3s1 0 1 1c1-1 2-1 3-2-1 2-1 4 0 5h1c2 1 3 1 5 1l2 1c3 0 6 1 8 1 7 1 14 1 20 0h11l2-1h1 5c1-1 2-1 3-1l6-1 10-3c4-1 9-2 12-5v-1c-1-1-1-2-1-3v-2c0-2 2-3 4-4 1 0 2-1 4 0 0 1 0 1 1 2 0 1 1 3 1 4h0c-1 1 0 2 0 2-1 0-2-1-3-1l-1-2h0v1 5c-1-1-1-3-3-4v1c0 2 0 3-1 5v1l-1 1c0 1-1 1-2 2l-2 1-1 1v2l-1 2h1c-1 2-3 3-4 4-2 1-3 2-6 2h0c-1 0-3 0-4 1l-2 2h-1l-3 1c-1 0-3 0-5 1 1 0 2 1 3 2v3l1 1c-1 2-2 3-4 4h0l-2 1c-1 0-2-1-3 0 0 1 0 1-1 2-3 2-4 3-7 2h-2-1l2 2c-1-1-1-1-2-1 0 0-1-1-2-1-1 1-1-1-2-1h-1c-1-1-2-1-3-1-2 0-4 2-6 1-1 1-1 1-2 0h-1l-1-1c-1 0-1 0-2-1l-2-1-2-1c-1-2-1-3-1-4-2 1-2 1-3 0h0c-1-2 0-4 0-6h0-1v2h-2-2l-1 3v1h-4v-3c-1 0-1 1-2 2v-1c-1 0-4-1-6 0-1 0-3 0-4-1l-1 1v1-1c-1 1-2 2-3 4v1l-3 3-1 1-6-3c-1 0-2-1-2-2l-1-1c-1-1 1-4 2-5-3-1-6-1-9-1-4 0-8 0-12-1h0l1-1c-9-3-18-7-23-15l-1-3c0-2 0-4 1-6v-1c0-2 1-2 3-3l-1-1c2-2 6-1 8-2-4-3-8-6-12-11-10-11-18-24-24-39-2-5-3-10-5-15l-1 2-3-9v-4-5c1 0 0-1 0-2-1-3-1-6-2-9l-2-11c0-2 0-5-1-7l-2-15-3-29-1-22-1-83v-10-21c1-6 1-13 1-20h0l-1-3c1-4 1-8 2-13 0-4 1-8 1-13h-1-1c-1 1-2 1-3 0l3-4 1-3 1 1v-2-7-8c1-3 2-6 3-8l-1-2 1-1c1 0 2-1 3-2v2h2 0c0-1 1-2 1-3l2-9c2-6 5-12 7-18l-1-1c3-7 8-13 10-19l2-2c1-1 0-1 0-1l3-3v-1h-1l-3 3v1s-1 0-2 1c0 0-1 0-1-1h-1l2-1c2 0 3-2 4-4v-5c0-1-1-2-1-3 0 0 1-1 1-2l-1-2h1c1-2 1-2 3-2 1 0 1 1 1 2 1 0 1 0 1 1h2c3-2 4-5 4-8 3-1 2-3 4-5 0-1 2-1 2-1 1-1 0-1 1-3l3-3c4-3 8-7 12-9h3c2-1 3-2 5-2 6-2 13-2 19-4 0 0 1-1 2-1-1-1-1-2-1-3h0 0c0-1 1-2 1-3l-1-1h2c4 0 7 0 10 1l1 1c1-1 1-2 2-2l4-3h0c1 0 2 2 3 2s2-1 3-1v-4h0 0c1 3 2 5 4 8h5 0l1-1c1 0 2-1 3-1v-1c1-1 2 1 3 1 1-1 1-2 2-3h1c1-1 0-3 1-4 2-3 5-4 9-5z" class="R"></path><path d="M463 154c-1 1-3 2-5 2v-1c1-1 3-1 4-1h0 1z" class="E"></path><path d="M420 161l-2 1c-1-1-1-1-1-2s1-1 1-1c0-1-1-2-1-2 1 0 2 1 3 1l-1 1 1 2z" class="F"></path><path d="M463 164l4-2 1 1c0 1 0 1-1 2-2 0-2 0-4-1z" class="G"></path><path d="M475 169c0 1 0 2-1 3v1c-1-1-2-2-3-2h-1v-1c3 0 3 0 5-1z" class="D"></path><path d="M277 596l1-1c0-1 1-1 1-1h3s1 0 1 1c-1 0-1 1-2 2l-4-1z" class="f"></path><path d="M473 159h1l2-1h0v2c-1 0-1 0-2 1l-2 1c-1 0-1-1-2-1 1-1 2-2 3-2z" class="P"></path><path d="M283 595c1-1 2-1 3-2-1 2-1 4 0 5l-5-1c1-1 1-2 2-2z" class="b"></path><path d="M451 245c1 0 2 1 3 2h1 4l-2 1h0l-3 6v-1l3-6-5 2c-1-2-1-3-1-4z" class="C"></path><path d="M456 165l-1-2c1 0 2 0 3 1h4c-1 1-1 2-2 3 0-1-1-1-1-1-1-1-2-1-3-1z" class="G"></path><path d="M475 288v-1h1c0 1 1 3 0 5h-5c1-1 3-2 4-4z" class="C"></path><path d="M454 187l4-4 2 1h0l-2 4h-1v-1c-1 0-2-1-3 0z" class="G"></path><path d="M183 499c1 2 3 6 2 9h0c-1 0-1-1-1-1-1 1-1 2-1 3v-4-5c1 0 0-1 0-2z" class="E"></path><path d="M477 154h1 0v2 1c-1 1-1 3-1 5v-1h-1v-1-2h0l-2 1h-1c2-2 3-3 4-5z" class="K"></path><path d="M374 588h0c-1-1-1-1-1-2 1-1 1-2 2-2s1 0 2 1h0v1 5c-1-1-1-3-3-4v1z" class="B"></path><path d="M447 182c2 1 3 2 4 1l2-1c1 0 1 1 1 1 0 1 0 1-1 1v1c-1 0-1 0-1 1-1 1-1 1-1 2s-1 1-1 2l-2-1 1-1c1-1 1-1 1-3 0-1-1-1-2-2l-1-1zm5-26c0 1 0 1-1 2l-1 1-1-1v1 1c2 0 3 1 3 3v2h-1c-1-2-2-4-4-7 1-1 4-2 5-2z" class="U"></path><path d="M459 272l-1 1c-1 0-1 1-2 0-1 0-2-1-3-2 0-1-1-1 0-2h0c2 1 3 1 4 1h1 1c1 1 0 1 0 2z" class="Q"></path><path d="M462 154c3-2 4-3 5-5v-1-2h0v1c1 1 1 2 2 3h0c0 1 1 1 1 2s-1 2-2 3h0 0l1-6c-1 1-2 2-2 3-1 1-2 2-4 2h-1z" class="B"></path><path d="M186 249l-1-1v-1c-1-2 0-4 0-6 1-4 1-9 4-13l-3 21z" class="E"></path><path d="M462 273c1 0 1 0 2 1 0 1 0 2 1 3-1 0-2 0-3 1-1 0-4 1-5 1-2 0-3-1-5-2h1c1 0 2 1 3 1 3-1 4-3 6-5h0z" class="K"></path><path d="M426 173c2 0 3 1 5 1 1 0 3 0 3 1 1 0 1 1 2 1h0c-1 0-2 1-3 1s-2 1-3 1c-2-1-3-4-4-5zm26 76l-2 2h2l1 1h0c-2 3-2 6-4 9 0-4 1-8-1-11 0-1 0-2-1-3v-1c0-1-1-2 0-3 0 0 1 5 2 6h1c1-1 1-3 1-5v1c0 1 0 2 1 4z" class="I"></path><path d="M476 112h1v11c0 6 2 14 1 20h-1v-2c-1-2 0-5 0-8v-13c0-3-1-5-1-8z" class="F"></path><path d="M180 207v2h2 0l-4 16c0 1 0 1-1 1l2-13v-1h-2l-1-2 1-1c1 0 2-1 3-2z" class="L"></path><path d="M180 207v2l-1 4v-1h-2l-1-2 1-1c1 0 2-1 3-2z" class="F"></path><path d="M462 164h1c2 1 2 1 4 1 0 1-1 1-1 2h-2v1c-1 0-2 1-2 2v1l-3-1-3-5c1 0 2 0 3 1 0 0 1 0 1 1 1-1 1-2 2-3z" class="c"></path><path d="M454 187c1-1 2 0 3 0v1h1c-2 3-6 9-8 9h-3c-1 1-1 1-2 1v-1-2l1 1h2c1 0 3-1 4-2s1-3 1-5v-2h1z" class="S"></path><path d="M454 187c1-1 2 0 3 0v1h-3l-1-1h1z" class="J"></path><path d="M446 209c0-1 1-1 1-1 1 0 2 0 3 1 0 1 1 1 1 2h-1c-2 0-1-2-3-1h0l1 1c-1 0-1 1-1 2h0c0 1-1 1-1 2v1l-2-1h-2l-1-1-1-1c0-2 1-2 2-3h1c1 1 2 1 3 1l1-1-1-1z" class="U"></path><path d="M441 214v-1c2 1 3 1 4 1v-2h1l1 1h0c0 1-1 1-1 2v1l-2-1h-2l-1-1z" class="I"></path><path d="M449 237v2c1 2 3 4 4 4 2 1 3 1 4 1h2c0 1 1 2 0 3h-4-1c-1-1-2-2-3-2v-1c-2-1-2-2-3-3v-1l-1-1s1-1 1-2h1z" class="P"></path><path d="M457 244h2c0 1 1 2 0 3h-4l-1-1c1-2 1-2 3-2z" class="J"></path><path d="M448 183c1 1 2 1 2 2 0 2 0 2-1 3l-1 1h-2c-1 2 0 4-1 5 0 0 0-1-1-2-1 0-1 1-2 1h-1c0-1-1-2-1-2l1-1c0-1 1-2 2-3 1 0 3 0 4 1 1-1 1-1 1-2s0-1-2-2c1-1 1-1 2-1z" class="H"></path><path d="M441 190c0-1 1-2 2-3l1 1v1 2h-3v-1z" class="B"></path><path d="M441 183c2 0 3 0 4 1h1c2 1 2 1 2 2s0 1-1 2c-1-1-3-1-4-1-1 1-2 2-2 3l-1 1v-1c1-1 1-3 3-4 0 1 1 1 1 0 1 0 1 0 2 1h2v-1h0c-1-2-2-1-3-1h-2-1c-1 1-3 2-3 3s-1 2-1 3h-2c-1-1-1-2-1-4l3-3c1 0 2-1 3-1z" class="m"></path><path d="M442 292h1c-2 3-3 4-4 6v1h1 24 0-6l-1 1c2 0 4-1 6 0l-1 1c-3-1-6-1-10-1h-18c3-3 6-5 8-8z" class="B"></path><path d="M480 158l1 3h0v46c-1 3 0 5 0 7v11c0 2 0 4-1 5v-2-70z" class="H"></path><path d="M462 139h1a19.81 19.81 0 0 0 11-11v11c-2 0-4-1-6 1-1 0-1 1-2 1v-1l-1-1c-1 1-1 1-2 1l-1-1zm18 19l-1-27c0-6 0-12 2-18h0c1-3 3-6 6-8h0 0c-6 6-7 12-7 19l1 37h0l-1-3z" class="C"></path><path d="M478 171v14 27c-1-1-1-2-1-4h-2l-1-1v-1l1-1v-2c0-1 0-3-2-4 1 0 2 0 3-1v-2l1 1v-4-5c0-1-1-1-1-2l1-2h0v3h0 1v-16z" class="D"></path><path d="M476 196l1 1v11h-2l-1-1v-1l1-1v-2c0-1 0-3-2-4 1 0 2 0 3-1v-2z" class="E"></path><path d="M177 212h2v1l-2 13h0c-1 3-1 7-3 9v-7-8l3-8z" class="m"></path><path d="M416 147l5-2v1c1-1 2-1 3-1l-1 1h2 1 2l1-1h2c-2 1-3 2-4 4-1 0-1 0-1 1-1 1-2 2-2 3s1 1-1 2l-1-1-3-3h0l1-1h0c-2-2-2 0-4 0v-1h1v-1s-1 0-1-1z" class="C"></path><path d="M416 147l5-2v1c1-1 2-1 3-1l-1 1v1h1c0 1 0 1 1 2h-1 0c-1 0-2 0-3 1h-1 0c-2-2-2 0-4 0v-1h1v-1s-1 0-1-1z" class="E"></path><path d="M416 147l5-2v1c1-1 2-1 3-1l-1 1v1c-2 0-4 0-6 1 0 0-1 0-1-1z" class="L"></path><path d="M462 170h1v3c0 1 1 2 1 2 1 1 4 1 5 0 0 1-1 2-1 3h1c-1 1-2 1-2 2h0l-1 2 1 1h-2c-1 0-1 0-1 1h0c-1 1-2 1-2 2v2c-1-2-1-3-2-4h0l-2-1c2-1 4-1 6-2 1-1 0-1 0-2h-1-1c1-2 0-2 0-3-2-2-3-3-3-6l3 1v-1z" class="V"></path><path d="M467 180s-1 0-1-1c-1 0-1 0-1-1l2-1 1 1h1c-1 1-2 1-2 2h0z" class="H"></path><path d="M460 184v-1c2 0 2 0 4 1h0c-1 1-2 1-2 2v2c-1-2-1-3-2-4h0z" class="E"></path><path d="M214 546l1 1c1-1 2-2 3-2v3 1h1v1h2v1 2h1v2c0 1 0 1-1 2 1 1 1 1 0 2l-1 1-3-4-2-3-2-5h1v-2z" class="W"></path><path d="M217 556c0-1 1-1 1-1 1-1 1-1 1-3l1 1 1 2h1c0 1 0 1-1 2 1 1 1 1 0 2l-1 1-3-4z" class="N"></path><path d="M214 546l1 1c1-1 2-2 3-2v3c-1 0-1 1-1 1 0 2-1 3-2 4l-2-5h1v-2z" class="b"></path><path d="M478 156v15 16h-1 0v-3h0l-1-4h0c-1 0-1 1-2 2 0-1 0-1-1-2v-1c1 0 1 0 2-1 0-1 0-1-1-2h-1c0-1 1-2 1-3v-1c1-1 1-2 1-3 0-2 1-4 0-5v-1-1l-1-1c1-1 1-1 2-1v1h1v1c0-2 0-4 1-5v-1z" class="L"></path><path d="M474 176l1-1h-1v-1l2-1h0c0 1 0 2 1 4v7h0l-1-4h0c-1 0-1 1-2 2 0-1 0-1-1-2v-1c1 0 1 0 2-1 0-1 0-1-1-2z" class="F"></path><path d="M471 141c2 0 3 0 4 1s3 3 3 5v7h0 0-1l-1-2h0c-1 1-2 2-3 2 0-1 1-3 2-4v-2c-1 1-1 2-2 3v-1-1c-1-2-3-3-4-4l-1-1c1-2 2-2 3-3z" class="L"></path><path d="M475 143c0 1 1 1 1 2 1 1 1 2 0 3 0-1-1-1-1-2-1-1 0-2 0-3z" class="I"></path><path d="M471 141l2 1v3c-1 0-2 0-3-1v1h-1l-1-1c1-2 2-2 3-3z" class="J"></path><path d="M177 226c1 0 1 0 1-1l-6 47h0l-1-3c1-4 1-8 2-13 0-4 1-8 1-13h-1-1c-1 1-2 1-3 0l3-4 1-3 1 1v-2c2-2 2-6 3-9h0z" class="H"></path><path d="M173 236l1 1c0 2-1 3-1 4v2h-1c-1 1-2 1-3 0l3-4 1-3z" class="P"></path><path d="M222 535l1 1h-2 0c-1 2-3 3-4 6v1h0l1 1v1c-1 0-2 1-3 2l-1-1v2h-1l-2-2-3-6c2 1 1-1 2-1 1-1 2-1 3-1 1-1 4-1 5-2v-1h4 0z" class="T"></path><path d="M208 540c2 1 1-1 2-1 1-1 2-1 3-1l-2 2v2c1 1 1 1 1 2l-1 2-3-6z" class="Y"></path><path d="M214 546c-1 0-1-1-1-2-1-1-1-2-1-3 1-1 3-2 4-3-1 3-1 5-2 8z" class="X"></path><path d="M216 538c1 0 1-1 2-1h1c1-1 1-1 2-1-1 2-3 3-4 6v1h0l1 1v1c-1 0-2 1-3 2l-1-1c1-3 1-5 2-8z" class="h"></path><path d="M454 201v1h2v-1h-1v-2-1l1 1v-1 1c1 1 2 1 3 2v2c-1 0-2-1-3-1l-3 1c-1 1-1 1-1 2h-1c0 1 2 3 3 3l-1 1c-1 2-3 3-3 5h-1v-2l-1-1-1-1h0c2-1 1 1 3 1h1c0-1-1-1-1-2-1-1-2-1-3-1 0 0-1 0-1 1h-1v-1c0-1-1-1-1-1-1-1-1-1-1-2l2-1 3-3v1 1h1c1-1 1-2 1-3h1l1-1c0 1 0 2 1 2h1z" class="B"></path><path d="M444 207c1-2 1-2 2-3h1v1c-1 1-1 2-2 2v2-1c0-1-1-1-1-1zm9 2h-2l-1-1c0-1-1-2-1-2l-1-1h1c0-1 1-2 2-2 1-1 1 0 2 0-1 1-1 1-1 2h-1c0 1 2 3 3 3l-1 1z" class="U"></path><path d="M442 292c0-2 1-4 1-6 0-1 0-2 1-2v-3c0-3 0-6-1-8 0-2 1-4 0-6v-3c-1-2-1-4-1-6v-2l-1-2v-2h0c0-1 0-2-1-3v-4h0l-1-3-1-5v-1c-1-1-1-2-1-3v-1c-1-1-1-2-1-3h0c-1-1-1-3-1-3 0-1 0-2-1-3 0-1 0-2-1-3 0-1-1-3-1-4s-1-2-1-3h1c1 2 1 5 2 7 1 1 1 2 1 3 0 0 0 1 1 2l1 5 1 5c0 2 1 4 2 6v2l3 13c0 2 1 5 1 8v1 3c1 3 1 7 1 10 0 2 0 4-1 6 0 1 1 2 0 3h0v2c0 1-1 2-1 4h0c0 1-1 1-1 1v1-1h1l1-1c0 1-1 1-1 2s-2 3-3 4h-1v-1c1-2 2-3 4-6h-1z" class="E"></path><path d="M436 128h-1c1 3 2 4 4 5 0 0 1 1 2 1v1c5 2 13 4 18 2 3-1 8-4 10-7h0c3-5 4-10 3-16v-1c-1-2-1-3-2-4v-1l2 2v1c1 1 1 1 1 2h0c2 5 0 11-2 15s-5 7-9 9-11 2-16 1l-1-1c-1 0-6-3-7-3h0c-2-1-3-3-4-5v-1c0-1 0-1-1-2h0l-1-1c-3 0-3-3-6-4-1 0-1-1-2-1l1-1h1 0 1l2 2c3 2 5 5 7 7z" class="F"></path><path d="M468 163c1 1 1 2 1 3s1 2 1 3l-1 1h1v1h1c1 0 2 1 3 2 0 1-1 2-1 3h1c1 1 1 1 1 2-1 1-1 1-2 1v1s-1 1-2 1c-1-1-1-2-2-3h-1c0-1 1-2 1-3-1 1-4 1-5 0 0 0-1-1-1-2v-3h-1c0-1 1-2 2-2v-1h2c0-1 1-1 1-2 1-1 1-1 1-2z" class="U"></path><path d="M469 175h2v1c0 1 2 2 2 3v1s-1 1-2 1c-1-1-1-2-2-3h-1c0-1 1-2 1-3z" class="S"></path><path d="M468 163c1 1 1 2 1 3s1 2 1 3l-1 1h1v1h1-4 0l1-2v-3h-1c0 1-1 1-1 2s-1 1-1 1c-1 0-1-1-1-1v-1h2c0-1 1-1 1-2 1-1 1-1 1-2z" class="B"></path><path d="M183 510c0-1 0-2 1-3 0 0 0 1 1 1h0c1 5 3 10 5 15 6 18 15 34 28 49 2 2 4 5 7 7 4 3 8 5 12 7l-1 1h-1v1l-1 2h-1-1v-2c0-1 1-2 1-3-2-1-3-2-5-3-4-3-8-6-12-11-10-11-18-24-24-39-2-5-3-10-5-15l-1 2-3-9z" class="D"></path><path d="M183 510c0-1 0-2 1-3 0 0 0 1 1 1l1 6c0 1 1 2 1 3l-1 2-3-9z" class="U"></path><path d="M477 269l1 1h-1l1 1c0 1 0 1 1 2 0 1 0 4-1 5 0 2-1 3-1 5-2 0-2-2-3-1 0 2 1 3 2 4v1h-1v1c0-1 0-2-1-3 0-2-1-3-3-3-1-1-2-2-4-2l-1-1h0 0l-1-1c-2 1-4 3-6 3-2 1-5 0-7-2-1-1-2-2-2-5 1 1 1 2 2 3 2 1 3 2 5 2 1 0 4-1 5-1 1-1 2-1 3-1-1-1-1-2-1-3h1c0 1 0 2 1 2l2 2h2v-2h2 1l1-2h1 1 0l-1-3 2-2z" class="I"></path><path d="M475 271h2v1c1 2 1 5 0 7l-1 1-1 1h-2c-1-1-3-2-5-2h0c-1 0-1 0-2-1h-4c1-1 2-1 3-1-1-1-1-2-1-3h1c0 1 0 2 1 2l2 2h2v-2h2 1l1-2h1 1 0l-1-3z" class="P"></path><path d="M475 271h2v1 3c0 1-1 2-2 3 0 1-1 1-1 1h-1v-1c1 0 2-1 2-2 1-1 0-2 0-2h1 0l-1-3z" class="K"></path><path d="M475 274s1 1 0 2c0 1-1 2-2 2h-3v-2h2 1l1-2h1z" class="E"></path><path d="M237 586c3 1 7 2 8 5v1l1 1h0v1c2 0 3 1 5 2l13 5c5 2 10 3 15 4l-2 1c-2-1-5-2-7-2l-15-5-6-3c-1 0-3-1-4-1h-1 0l-1-1c-1 1-2 1-3 1v1l-2 1h-1l-1 1-1-1h0v1l-2-1c0-2 1-4 1-6v-1l1-2v-1h1l1-1z" class="F"></path><path d="M235 588c0 2 0 3 1 4h1s1 0 1-1l1 1h-1v1 1c-1 0-1-1-2-1h-1v1c1 1 2 2 2 3l-1 1-1-1h0v1l-2-1c0-2 1-4 1-6v-1l1-2z" class="E"></path><path d="M235 588v-1h1c3 1 5 2 8 4v2h-3v2h-1c-1-1-1-2-1-3l-1-1c0 1-1 1-1 1h-1c-1-1-1-2-1-4z" class="R"></path><path d="M243 123l1 1c2-1 4-2 5-3s2-1 3-2c1 0 2 0 3-1h1 3c-2 1-5 2-6 3-5 3-9 5-13 7-5 4-11 9-15 13l-5 6-3 3c-2 1-3 3-4 5h0v-1h0l-1 1h-1c9-12 19-24 32-32z" class="C"></path><path d="M450 140h6 1 1s0-1 1-1h3l1 1c1 0 1 0 2-1l1 1v1 2l-1 2h0c-1 1-1 2-1 2-1 2-3 3-4 4h0c-1 1-2 1-3 2v-2c-1-3-3-3-5-5-3-2-5-5-7-7l5 1z" class="B"></path><path d="M457 151l2-2s1-2 2-2 1-1 1-1c0-1 0-2-1-3h1c1 0 2 1 3 2h0c-1 1-1 2-1 2-1 2-3 3-4 4h0c-1 1-2 1-3 2v-2zm-7-11h6 1 1s0-1 1-1h3l1 1c1 0 1 0 2-1l1 1v1 2c-1-1-1-2-2-2-1 1-2 1-3 1h-1 0 0c-1 0-2 0-2 1h0c-3 0-4-2-6-2l-2-1z" class="E"></path><path d="M223 536h3c0 1 1 2 2 2l1 1c2 0 3 1 5 1h3c2 0 3 0 4 1l-6 3c-2 1-4 1-6 1-1 0-2-1-3-1v-1h-1l-1 2c-1 2-1 4-3 6v-1h-2v-1h-1v-1-3-1l-1-1h0v-1c1-3 3-4 4-6h0 2z" class="p"></path><path d="M237 540c2 0 3 0 4 1l-6 3c-1-1-1-1-2-1l1-1h2c1-1 0-1 1 0h0l1-1h1 0l-2-1z" class="e"></path><path d="M217 542l1 1h2l4-4v3l-1 1c1-1 1-1 2 0l-1 2c-1 2-1 4-3 6v-1h-2v-1h-1v-1-3-1l-1-1h0v-1z" class="b"></path><path d="M218 544h2l1 1-2 4h-1v-1-3-1z" class="j"></path><path d="M306 604h2c2 1 17 1 19 0h2c3-1 7 0 10-1 2-1 3 0 4-1h2 1l12-3c5-1 9-3 14-4h0c0 1-1 1-2 2l-2 1h-1-2l-8 2c-7 2-14 3-22 4-2 1-4 1-6 1h-4-5-11v2 1c-1 0-2 0-3 1h1 0c-2 1-4 2-5 4-1 1 0 1 0 2s0 2 1 3l-1 1c0-1-1-2-1-3-1 2-1 3 0 4-2 1-2 1-3 0h0c1-2 1-2 2-2 0-2 0-4-1-6l-3-1h3 0l1-1h0l-1-1c-2 0-5 1-6 0h-2c0-1-1-1-2-1h-1c-1 0-3 0-4-1h-3 0l-1-1h-3l2-1c3 0 6 1 9 2 3 0 5 1 8 1h6c2 0 3-1 4-2l-1-1h1v-1z" class="B"></path><path d="M460 184c1 1 1 2 2 4v-2c0-1 1-1 2-2l5 13s1 1 1 2h-5l-1 1c1 1 1 2 2 3h2v1c-1 0-2 0-3 2h-1c-1-3-1-6-1-10 0-2-1-4-2-6v-1h-1l-1 2v2c1 1 1 2 1 3 1 2 1 4 2 6-1 0-2-1-3-1-1-1-2-1-3-2v-1 1l-1-1v1 2h1v1h-2v-1h-1c-1 0-1-1-1-2l-1 1h-1l-1-1-1 1c-1-1-2-1-3-2 1 0 1 0 2-1h3c2 0 6-6 8-9l2-4z" class="I"></path><path d="M452 199h1c1 1 1 1 1 2h-1c-1 0-1-1-1-2z" class="m"></path><path d="M457 192c1 1 1 4 1 5v1h-1v-1c-1-1-2-1-2-2l2-3z" class="E"></path><path d="M460 184c1 1 1 2 2 4h0-1l-1-1v1h0c-1 1-3 3-3 4l-2 3c-1 1-1 1-3 2l-1 1h-2l-2-1h3c2 0 6-6 8-9l2-4z" class="F"></path><path d="M462 188v-2c0-1 1-1 2-2l5 13c-1 1-2 1-3 1h-2c0-3-1-7-2-10h0z" class="R"></path><path d="M469 178c1 1 1 2 2 3 1 0 2-1 2-1 1 1 1 1 1 2 1-1 1-2 2-2h0l1 4-1 2c0 1 1 1 1 2v5 4l-1-1v2c-1 1-2 1-3 1 2 1 2 3 2 4v2l-1 1h-1c-1 0-1-1-1-2l-1-1v-1c0-1 0-2-1-3 0-1-1-2-1-2l-5-13h0c0-1 0-1 1-1h2l-1-1 1-2h0c0-1 1-1 2-2z" class="I"></path><path d="M471 194c1-1 2 0 3-1v-1l1-1c1 1 1 1 1 2v2 1 2c-1 1-2 1-3 1v-1c-1-1-1-1 0-2-1-1-1-2-2-2z" class="R"></path><path d="M474 182c1-1 1-2 2-2h0l1 4-1 2c0 1 0 2-1 3h-1c-1 1 0 2-2 3v-3l-1-1c1-1 1-1 2-1l-1-1h0v-1h1v-1c0-1 1-1 1-2z" class="m"></path><path d="M469 178c1 1 1 2 2 3 1 0 2-1 2-1 1 1 1 1 1 2s-1 1-1 2v1h-1v1h0l1 1c-1 0-1 0-2 1l-2-1-1-1c0-2-1-2-1-3l-1-1 1-2h0c0-1 1-1 2-2z" class="E"></path><path d="M473 180c1 1 1 1 1 2s-1 1-1 2v1l-1-1-1-3c1 0 2-1 2-1z" class="Q"></path><path d="M464 184c0-1 0-1 1-1h2c0 1 1 1 1 3l1 1c0 2 1 3 1 5 1 0 1 1 1 2 1 0 1 1 2 2-1 1-1 1 0 2v1c2 1 2 3 2 4v2l-1 1h-1c-1 0-1-1-1-2l-1-1v-1c0-1 0-2-1-3 0-1-1-2-1-2l-5-13h0z" class="c"></path><path d="M309 607v-2h11 5 4c2 0 4 0 6-1 8-1 15-2 22-4l8-2h2 1l-1 1c-3 2-6 3-9 4l-8 2h-2-3c-5 1-9 1-14 2-1 0-1 0-2 1h-2-1c-1 0-1 1-2 1s-3 1-5 2h-1c0 1 1 2 2 2h0c1 0 1 1 2 2h-3l-1-1-1-1-1 1h-1l-1-1c-2-2-4-4-7-4h-1c1-1 2-1 3-1v-1z" class="R"></path><path d="M309 607c2 2 6 4 9 4h0c0 1 1 2 2 2h0c1 0 1 1 2 2h-3l-1-1-1-1-1 1h-1l-1-1c-2-2-4-4-7-4h-1c1-1 2-1 3-1v-1z" class="F"></path><path d="M314 613v-2c1 1 3 1 3 2l-1 1h-1l-1-1z" class="I"></path><path d="M448 163c1 1 2 2 3 2h1l5 10-1 1-3 1c-1 2-5 3-6 5l1 1c-1 0-1 0-2 1h-1c-1-1-2-1-4-1-1 0-2 1-3 1l-3 3v-1c0-1 2-2 3-3 0-1-1-3-1-4v-1l-1-2h0c1 0 1 0 1-1 0 0 1 0 1-1h0c1 0 2-1 3-1h1c2-1 3-3 4-4v-1l1-1c0-2 0-3 1-4h0z" class="B"></path><path d="M447 173h0 1c-1 1 0 2-1 2 0 2-1 1-2 3v3c0 1 0 1-1 2-1-2-1-4-1-5l3-3 1-2z" class="I"></path><path d="M448 163c1 1 2 2 3 2h1l5 10-1 1-3 1h0v-6c-1-1-2-2-2-3h-1s-1 0-1-1l-1 1h-1v1h1 1 0c0 1-1 2-1 3-1-1-1-1-2-1 0 1-2 3-2 3-1 0-2 1-2 1l-1 1c1 2 0 3 1 5l-1 2c-1 0-2 1-3 1l-3 3v-1c0-1 2-2 3-3 0-1-1-3-1-4v-1l-1-2h0c1 0 1 0 1-1 0 0 1 0 1-1h0c1 0 2-1 3-1h1c2-1 3-3 4-4v-1l1-1c0-2 0-3 1-4h0z" class="C"></path><path d="M436 176c1 0 1 0 1-1 0 0 1 0 1-1 0 1 0 2 1 3-1 1-1 1-2 1h0l-1-2h0z" class="D"></path><path d="M222 553c2 1 3 3 5 4s6 2 8 4h1l1 2 1-1 5-5v2 1c-1 0-1 1-2 2 0 0 1 0 1 1l1-1c1 0 3 0 3 1s-1 1-2 1l1 2c-1 0-2 1-4 2l-3 3-1 1-1 2s-1 0-1 1a57.31 57.31 0 0 1-11-11l-4-4 1-1c1-1 1-1 0-2 1-1 1-1 1-2v-2z" class="i"></path><path d="M231 562h1 1 0l-1 1 1 4v3h-1c0-1 0-2-1-4v-4z" class="a"></path><path d="M228 560l1-1h0v2l2 1v4h-2-1v-2c1-1 1-2 1-3l-1-1z" class="T"></path><path d="M237 572v-2c1-2 6-5 7-6l1 2c-1 0-2 1-4 2l-3 3-1 1z" class="N"></path><path d="M238 562l5-5v2 1c-1 0-1 1-2 2 0 0 1 0 1 1-2 1-4 2-6 2v-1l1-1 1-1z" class="O"></path><path d="M235 561h1l1 2-1 1v1c0 1-1 2-1 3l-1-1h0-1l-1-4 1-1h0c1 0 1 0 2-1z" class="N"></path><path d="M233 567l-1-4 1-1c0 1 1 2 2 3l-1 2h-1z" class="W"></path><path d="M227 557c2 1 6 2 8 4-1 1-1 1-2 1h-1-1l-2-1v-2h0l-1 1c0 1 0 1-1 1l-1-1 1-2-1-1h1z" class="b"></path><path d="M222 553c2 1 3 3 5 4h-1c-1 2-2 5-2 7l-4-4 1-1c1-1 1-1 0-2 1-1 1-1 1-2v-2z" class="M"></path><path d="M243 123c4-2 8-4 13-6 20-9 43-10 65-7l19 3 15 6c5 2 9 4 13 7 19 11 34 27 45 47l9 15-1 1-3-6-6-12c-10-16-24-32-39-41-7-5-14-8-21-11-4-2-9-3-13-4-13-4-25-5-37-5-8 0-15 1-23 2-7 1-14 3-20 6h-3-1c-1 1-2 1-3 1-1 1-2 1-3 2s-3 2-5 3l-1-1z" class="D"></path><path d="M245 595c1 0 3 1 4 1l6 3 15 5c2 0 5 1 7 2h3l1 1h0 3c1 1 3 1 4 1h1c1 0 2 0 2 1h2c1 1 4 0 6 0l1 1h0l-1 1h0-3l3 1c1 2 1 4 1 6-1 0-1 0-2 2-1-2 0-4 0-6h0-1v2h-2-2l-1 3v-3l-3-3c-1-1-2-1-3-2-2-1-4-2-6-2-2-1-4 0-6 0 0-1-1-1-1-1h-2 1v-1l-10-2c-5-1-10-2-14-5-2-1-3-1-4-3 1 0 1-1 1-2h0z" class="m"></path><path d="M272 608l24 3 3 1c1 2 1 4 1 6-1 0-1 0-2 2-1-2 0-4 0-6h0-1v2h-2-2l-1 3v-3l-3-3c-1-1-2-1-3-2-2-1-4-2-6-2-2-1-4 0-6 0 0-1-1-1-1-1h-2 1z" class="H"></path><path d="M291 612h4 2v2 2h-2-2l1-1c0-1-1-2-1-2h-1c0-1-1-1-1-1z" class="F"></path><path d="M294 615v-1h2l-1 2h-2l1-1z" class="C"></path><path d="M286 611c1 0 4 0 5 1 0 0 1 0 1 1h1s1 1 1 2l-1 1-1 3v-3l-3-3c-1-1-2-1-3-2z" class="I"></path><path d="M289 613h1 2v3l-3-3z" class="F"></path><path d="M358 603c3-1 6-2 9-4v2l-1 2h1c-1 2-3 3-4 4-2 1-3 2-6 2h0c-1 0-3 0-4 1l-2 2h-1l-3 1c-1 0-3 0-5 1v-1-1h0l-1-1h-1c-3 0-6 1-9 0-2 0-3 0-5 1l-2 2-1 2c-1 0-2 1-3 1 0 2 1 5 1 7 2 1 3 1 3 3 0 0-1 0-2-1l-1-1c-1-2-2-4-2-6 1-1 1-2 0-4h3c-1-1-1-2-2-2h0c-1 0-2-1-2-2h1c2-1 4-2 5-2s1-1 2-1h1 2c1-1 1-1 2-1 5-1 9-1 14-2h3 2l8-2z" class="E"></path><path d="M358 603c3-1 6-2 9-4v2l-1 2h0c0-1-1-1-1-1l-3 2-1-1h1c1-1 2-1 2-1v-1l-3 2h-3z" class="I"></path><path d="M366 603h1c-1 2-3 3-4 4-2 1-3 2-6 2h0l9-6h0zm-24 9l11-2-2 2h-1l-3 1c-1 0-3 0-5 1v-1-1z" class="Q"></path><path d="M322 615c1-2 3-3 6-4h0c4-2 7-1 10-1h7c-1 0-3 0-4 1h-1c-3 0-6 1-9 0-2 0-3 0-5 1l-2 2-1 2c-1 0-2 1-3 1 0 2 1 5 1 7 2 1 3 1 3 3 0 0-1 0-2-1l-1-1c-1-2-2-4-2-6 1-1 1-2 0-4h3z" class="H"></path><path d="M307 609c3 0 5 2 7 4l1 1h1l1-1 1 1 1 1c1 2 1 3 0 4 0 2 1 4 2 6l1 1c1 1 2 1 2 1l3 2h-1l2 2c-1-1-1-1-2-1 0 0-1-1-2-1-1 1-1-1-2-1h-1c-1-1-2-1-3-1-2 0-4 2-6 1-1 1-1 1-2 0h-1l-1-1c-1 0-1 0-2-1l-2-1-2-1c-1-2-1-3-1-4-1-1-1-2 0-4 0 1 1 2 1 3l1-1c-1-1-1-2-1-3s-1-1 0-2c1-2 3-3 5-4h0z" class="C"></path><path d="M312 622h-1v-2c1-1 1-1 2-1h0l-1 3z" class="D"></path><path d="M302 615c1 0 2 1 3 1v1c0 1 1 3 1 4v1l-1-1c0-1-2-2-2-3-1-1-1-2-1-3z" class="E"></path><path d="M312 617l2-1v1l-1 1 1 1h2v2l-1 2h-1l-2 1v-2l1-3h0c0-1 0-1-1-2z" class="I"></path><path d="M316 621c0 2 0 3-2 4s-4 2-6 2c-1 0-1 0-2-1l1-1h2c1 0 1 0 1-1h2l2-1h1l1-2z" class="D"></path><path d="M301 620c-1-1-1-2 0-4 0 1 1 2 1 3 1 1 2 2 2 3 1 1 2 1 3 3l-1 1-2-1-2-1c-1-2-1-3-1-4z" class="U"></path><path d="M317 613l1 1-2 5h-2l-1-1 1-1v-1l-2 1c-1 0-1 1-2 1v2h-1c0-1-1-2-2-2v-2h6l1-2h1 1l1-1z" class="E"></path><path d="M307 609c3 0 5 2 7 4l1 1h-1l-1 2h-6-2c-1 0-2-1-3-1 0-1-1-1 0-2 1-2 3-3 5-4h0z" class="R"></path><path d="M318 614l1 1c1 2 1 3 0 4 0 2 1 4 2 6l1 1c1 1 2 1 2 1l3 2h-1l2 2c-1-1-1-1-2-1 0 0-1-1-2-1-1 1-1-1-2-1h-1c-1-1-2-1-3-1-2 0-4 2-6 1-1 1-1 1-2 0h-1l-1-1c2 0 4-1 6-2s2-2 2-4v-2l2-5z" class="G"></path><path d="M318 620h1v2h-2v-1l1-1z" class="J"></path><path d="M310 628c1 0 2-1 3-1l6-3 2 2v-1l1 1c1 1 2 1 2 1l3 2h-1l2 2c-1-1-1-1-2-1 0 0-1-1-2-1-1 1-1-1-2-1h-1c-1-1-2-1-3-1-2 0-4 2-6 1-1 1-1 1-2 0z" class="Q"></path><path d="M326 612c2-1 3-1 5-1 3 1 6 0 9 0h1l1 1h0v1 1c1 0 2 1 3 2v3l1 1c-1 2-2 3-4 4h0l-2 1c-1 0-2-1-3 0 0 1 0 1-1 2-3 2-4 3-7 2h-2l-3-2c0-2-1-2-3-3 0-2-1-5-1-7 1 0 2-1 3-1l1-2 2-2z" class="R"></path><path d="M336 624h6l-2 1c-1 0-2-1-3 0 0 1 0 1-1 2h-1l-1-1 2-2z" class="P"></path><path d="M331 611c3 1 6 0 9 0-1 2-3 3-5 4 0-1-1-1-2-2-1 0-1-1-2-2z" class="U"></path><path d="M324 614l2-2c0 2 1 4 1 6h2v1c1 1 2 3 3 4s3 1 4 1l-2 2 1 1h1c-3 2-4 3-7 2h-2l-3-2c0-2-1-2-3-3 0-2-1-5-1-7 1 0 2-1 3-1l1-2z" class="C"></path><path d="M332 623c1 1 3 1 4 1l-2 2 1 1c-1 0-3 1-4 0-1 0-1 0-1-1h0 2 1l-1-1v-2z" class="n"></path><path d="M324 614l2-2c0 2 1 4 1 6h-1v3h-1c-1-1-1-5-1-7h0z" class="E"></path><path d="M327 618h2v1c1 1 2 3 3 4v2l-2-1v1 1l-2-2c-1-1-1-2-2-2l-1-1h1v-3h1z" class="I"></path><path d="M330 624c0-2-1-3-2-5h1c1 1 2 3 3 4v2l-2-1z" class="H"></path><path d="M471 252s1 0 1 1c1 1 0 2 1 4 1 0 2 1 3 2v1l1 1c1 0 1 1 2 2 0 2 0 4-2 6h0l-2 2 1 3h0-1-1l-1 2h-1-2v2h-2l-2-2c-1 0-1-1-1-2h-1c-1-1-1-1-2-1l1-1v1l2-2h-1-2c0-1-1-1-2-1v1l-1 1c0-1 1-1 0-2h-1 0c0-1 0-1-1-1l-1-2-1 1h0v-2-1-3l1-1c1 0 2 0 3-1h0c1-1 1-2 2-2 3-2 5-2 7-3h1v-1c1 0 0 1 1 1h0 1v-3z" class="B"></path><path d="M468 273l1 1c-1 1-2 1-3 2-1 0-1-1-1-2l3-1z" class="K"></path><path d="M464 261c2-1 2-1 4 0h1c0 1-1 1-1 1l-2 1-2-2z" class="S"></path><path d="M472 263v-1c1-1 1-1 2-1 1 1 1 2 0 3 0 1-1 2-2 2h0c0-2 1-2 0-3z" class="G"></path><path d="M468 261c2-1 4-1 6-1v1c-1 0-1 0-2 1v1c-1-1-3-1-4-1 0 0 1 0 1-1h-1z" class="D"></path><path d="M455 262h1v3 1h1c1 1 2 1 3 3h-1c-1-1-3-2-4-3v-1-3z" class="C"></path><path d="M461 266v2h1l1-1c1 2 2 2 4 2 0 1 0 1-1 2-1 0-1 0-1-1h-1c-1 0-3-1-3-2h-1l1-2z" class="D"></path><path d="M459 260h1c0 2 1 4 2 5l1 2-1 1h-1v-2c-1-1-1-2-2-3v-3h0z" class="L"></path><path d="M455 262l1-1c1 0 2 0 3-1v3c1 1 1 2 2 3l-1 2c0-1 0-2-1-2v-1h-3v-3h-1z" class="F"></path><path d="M477 261c1 0 1 1 2 2 0 2 0 4-2 6h0l-2 2 1 3h0-1l-1-1c0-2 0-3-1-4 2-1 4-2 5-4 0-2 0-3-1-4zm-14-2l1 2 2 2c2 1 3 2 3 3h-1c-1 1-2 1-3 0-2-1-3-4-3-6l1-1z" class="G"></path><path d="M471 252s1 0 1 1c1 1 0 2 1 4 1 0 2 1 3 2v1h0-1l-3-3h0-5c-1 1-3 1-4 2l-1 1-1-2c3-2 5-2 7-3h1v-1c1 0 0 1 1 1h0 1v-3z" class="K"></path><path d="M473 269c1 1 1 2 1 4l1 1h-1l-1 2h-1-2v2h-2l-2-2c1-1 2-1 3-2l-1-1c1-2 4-3 5-4z" class="S"></path><path d="M472 276v-1h-1v-1l2-2 1 1 1 1h-1l-1 2h-1z" class="I"></path><path d="M469 274l1-2v1c0 1-1 2 0 3v2h-2l-2-2c1-1 2-1 3-2z" class="B"></path><path d="M246 534h1v2c-1 1-1 1-1 3l1 1c2 6 5 11 10 14l-9 1-3 1-2 1-5 5-1 1-1-2h-1c-2-2-6-3-8-4s-3-3-5-4h-1v-2c2-2 2-4 3-6l1-2h1v1c1 0 2 1 3 1 2 0 4 0 6-1l6-3 2-2 3-2v-3z" class="a"></path><path d="M242 547c2-1 3-3 5-4v1c1 1 1 2 0 3h-1c-1 1-2 2-4 3l-3 3-1-1 2-4 2-1z" class="l"></path><path d="M242 550c2-1 3-2 4-3l2 1v1l3 2v1h-1c-1 0-2 0-4 1h0-4c0-1 1-1 1-2s1-1 2-2c-1 0-2 0-3 1z" class="X"></path><path d="M242 550c2-1 3-2 4-3l2 1v1c-2 0-3 1-5 2 0-1 1-1 2-2-1 0-2 0-3 1z" class="Y"></path><path d="M246 534h1v2c-1 1-1 1-1 3l1 1-4 3-9 4h-2c-1-1-2-1-2-1h-1v-1c2 0 4 0 6-1l6-3 2-2 3-2v-3z" class="h"></path><path d="M246 539l1 1-4 3v-2l1-1c1 0 2-1 2-1h0z" class="N"></path><path d="M242 550c1-1 2-1 3-1-1 1-2 1-2 2s-1 1-1 2h4 1l-2 3-2 1-5 5v-5l1-4 3-3z" class="g"></path><path d="M225 543h1v1c1 0 2 1 3 1v1h1s1 0 2 1h2c1 0 1 0 2 1l1-1c1 0 3-1 5 0l-2 1-2 4 1 1-1 4v5l-1 1-1-2h-1c-2-2-6-3-8-4s-3-3-5-4h-1v-2c2-2 2-4 3-6l1-2z" class="d"></path><path d="M236 548l1-1c1 0 3-1 5 0l-2 1-2 1c0 1-1 1-2 1l-1 1h-3c1-1 3-2 4-3z" class="g"></path><path d="M225 543h1v1c1 0 2 1 3 1v1h1s1 0 2 1h2c1 0 1 0 2 1-1 1-3 2-4 3-2-1-4 0-6-1-1 0-2 1-3 1l1-1c0-1 1-2 2-2s1 0 1-1c-2 0-2 0-2-2h-1l1-2z" class="i"></path><path d="M225 543h1v1c1 0 2 1 3 1v1h1s1 0 2 1h2c-1 0 0 0-1 1-2 0-3 0-5-1h-1c-2 0-2 0-2-2h-1l1-2z" class="a"></path><path d="M225 543h1v1h0v1c1 0 2 1 2 2h-1c-2 0-2 0-2-2h-1l1-2z" class="T"></path><path d="M224 545h1c0 2 0 2 2 2 0 1 0 1-1 1s-2 1-2 2l-1 1c2 2 5 2 7 2 2 1 4 1 5 0 1 0 2-1 2-1h1l-1 3 1 2v5l-1 1-1-2h-1c-2-2-6-3-8-4s-3-3-5-4h-1v-2c2-2 2-4 3-6z" class="k"></path><path d="M237 555l1 2v5l-1 1-1-2h0 0l-2-2c1-1 2 0 3-1-1-1 0-2 0-3z" class="f"></path><path d="M474 207l1 1h2c0 2 0 3 1 4v8l1 9 1-1v2c1-1 1-3 1-5v-11c0-2-1-4 0-7v67c0 5 0 12 1 17 0 2 1 4 2 6v1c-1-1-1-2-2-3 0-1-1-3-1-5h0c-1-4-1-8-1-12 0-1-1-2-1-2v-6h0v-2l-1 2-1-1h0c2-2 2-4 2-6-1-1-1-2-2-2l-1-1v-1c-1-1-2-2-3-2-1-2 0-3-1-4 0-1-1-1-1-1v3h-1 0c-1 0 0-1-1-1s-2-1-3-2c-1 0-1-1-2-1 0-1 0-2 1-3l2-1v-1h0c3-1 2-1 2-3l1 1v-2l2-2c1-2 3-4 4-6 0-8-1-16-3-23l-1-2 1-2h1z" class="F"></path><path d="M477 252c1 1 1 2 0 4-1 0-1-1-1-2h0c1-1 1-1 1-2h0z" class="C"></path><path d="M473 257c0-2 1-2 2-3h1c0 1 0 2 1 2l1 1v3h-1l-1-1c-1-1-2-2-3-2z" class="B"></path><path d="M471 252l1-2 1 1h2s1 1 2 1c0 1 0 1-1 2h0-1c-1 1-2 1-2 3-1-2 0-3-1-4 0-1-1-1-1-1z" class="I"></path><path d="M473 251h2s1 1 2 1c0 1 0 1-1 2l-3-3z" class="F"></path><path d="M476 241v2h1 0v-1 10h0c-1 0-2-1-2-1h-2l-1-1-1 2v-2l1-1h1 2c1-1 1-1 1-2h-1l-1-2 1-3 1-1z" class="P"></path><path d="M472 240c1-2 3-4 4-6v2l-1 3c-1 1-1 2-1 3h1l-1 3 1 2h1c0 1 0 1-1 2h-2-1l-1 1v2 3h-1 0c-1 0 0-1-1-1s-2-1-3-2c-1 0-1-1-2-1 0-1 0-2 1-3l2-1v-1h0c3-1 2-1 2-3l1 1v-2l2-2z" class="l"></path><path d="M467 247v-1h0c3-1 2-1 2-3l1 1v2c1-1 2-2 3-1v1h-1c-2 1-3 1-3 2l-1 1h0c0-1 0-1-1-2z" class="I"></path><path d="M468 249l1-1c0-1 1-1 3-2 0 1 1 1 1 1v2h-1l-1 1v2 3h-1 0c-1 0 0-1-1-1s-2-1-3-2c-1 0-1-1-2-1 0-1 0-2 1-3l2-1c1 1 1 1 1 2h0z" class="l"></path><path d="M468 249l1-1c0-1 1-1 3-2 0 1 1 1 1 1v2h-1-1 0l-2 2h-1v-2z" class="h"></path><path d="M467 247c1 1 1 1 1 2v3c-1 0-2-1-2 0-1 0-1-1-2-1 0-1 0-2 1-3l2-1z" class="B"></path><defs><linearGradient id="z" x1="470.204" y1="226.857" x2="480.325" y2="221.038" xlink:href="#B"><stop offset="0" stop-color="#716f72"></stop><stop offset="1" stop-color="#a5a4a2"></stop></linearGradient></defs><path fill="url(#z)" d="M474 207l1 1h2c0 2 0 3 1 4v8l1 9c0 5 0 9-2 13v1h0-1v-2l-1 1h-1c0-1 0-2 1-3l1-3v-2c0-8-1-16-3-23l-1-2 1-2h1z"></path><path d="M474 207l1 1h0c0 1-1 1-1 2l-1 1-1-2 1-2h1z" class="G"></path><path d="M475 239c0-1 1-1 2-2v3l-1 1-1 1h-1c0-1 0-2 1-3z" class="X"></path><path d="M475 208h2c0 2 0 3 1 4v8c0-1-1-2-1-2-1-3-2-7-2-10h0z" class="C"></path><path d="M250 540c2-3 5-4 7-7v-1l1 1 1 5c1 3 2 5 4 7l1 1h-1-1c0 1 0 1-1 2l-1 1c1 1 3 2 5 3l1 1c1 1 3 2 4 2l8 4h1l-1 1h0-1v1h-1c-1 0-1 1-1 1l-6-1-1 1h3c-1 1-1 2-2 2v2c-2 1-5 0-8 1h-1l-3 1h0-3c-2 1-4 1-7 2-1-1 0-1-2-1h-1l-2 1-1-2c2-1 3-2 4-2l-1-2c1 0 2 0 2-1s-2-1-3-1l-1 1c0-1-1-1-1-1 1-1 1-2 2-2v-1-2l2-1 3-1 9-1c-5-3-8-8-10-14l-1-1c0-2 0-2 1-3l1 2c0 1 0 2 1 3 0-1 0-1 1-1z" class="H"></path><path d="M261 561h2c2-1 4-1 6 0l-1 1h-7v-1z" class="L"></path><path d="M246 561h3c2 0 2-1 3-1 1-1 5 0 6 0s1 0 2 1h0 1v1c-2 0-4 1-6 1v-1h-2-1c-2-1-4 0-6-1z" class="Q"></path><path d="M260 561h1v1c-2 0-4 1-6 1v-1h-2c2 0 5 0 7-1z" class="V"></path><path d="M257 554h2l5 2c-1 1-3 1-4 2s-4 1-5 1h-3c-2 1-4 1-6 1 0-1-1-1-1-1l1-1c1-1 0-1 1-1l1-2 9-1z" class="Q"></path><path d="M250 540c2-3 5-4 7-7v-1l1 1 1 5c1 3 2 5 4 7l1 1h-1-1c0 1 0 1-1 2l-1 1c1 1 3 2 5 3l1 1c-5-2-10-4-14-8-1-1-2-3-3-4 0-1 0-1 1-1z" class="W"></path><path d="M258 541h1v2h-1-3-1c0-2 1-1 2-1s2-1 2-1z" class="Y"></path><path d="M259 538c1 3 2 5 4 7l1 1h-1l-2-1h-3v-2h1v-2h-1c-1 0-1-1-1-1h-2c1-1 2-1 3-2h1z" class="d"></path><path d="M255 543h3v2h3l2 1h-1c0 1 0 1-1 2l-1 1-1-1c-2-1-3-2-3-3-1 0-1 0-2-1l1-1h0z" class="M"></path><path d="M259 548v-1c1-1 1-1 3-1 0 1 0 1-1 2l-1 1-1-1z" class="l"></path><path d="M255 543h3v2h3-5c-1 0-1 0-2-1l1-1h0z" class="N"></path><path d="M255 543h3v2c-1 0-2 0-3-2h0z" class="X"></path><path d="M250 540c2-3 5-4 7-7v-1l1 1 1 5h-1l-1-1c-1 0-1 1-2 1-1 2-3 2-5 2z" class="b"></path><path d="M248 555l-1 2c-1 0 0 0-1 1l-1 1s1 0 1 1l-1 1h1c2 1 4 0 6 1h1 2v1c2 0 4-1 6-1h7 3c-1 1-1 2-2 2v2c-2 1-5 0-8 1h-1l-3 1h0-3c-2 1-4 1-7 2-1-1 0-1-2-1h-1l-2 1-1-2c2-1 3-2 4-2l-1-2c1 0 2 0 2-1s-2-1-3-1l-1 1c0-1-1-1-1-1 1-1 1-2 2-2v-1-2l2-1 3-1z" class="C"></path><path d="M245 566l5-2-2 2h-1 0 1v1c2 0 4-1 5-1 3 0 4 1 7 1l-3 1h0-3c-1 0-2 0-3-1s-4 1-6 0v-1z" class="Q"></path><path d="M243 562h4c1 0 1 0 2 1l3-1h1 2v1l-5 1-5 2-1-2c1 0 2 0 2-1s-2-1-3-1z" class="Z"></path><path d="M245 566v1c2 1 5-1 6 0s2 1 3 1c-2 1-4 1-7 2-1-1 0-1-2-1h-1l-2 1-1-2c2-1 3-2 4-2z" class="K"></path><path d="M248 555l-1 2c-1 0 0 0-1 1l-1 1s1 0 1 1l-1 1h1c2 1 4 0 6 1l-3 1c-1-1-1-1-2-1h-4l-1 1c0-1-1-1-1-1 1-1 1-2 2-2v-1-2l2-1 3-1z" class="c"></path><path d="M278 559c1 0 3 1 4 2h0 1c0 1 1 1 2 2-1 0-2 1-4 1 0-1-1 0-2 0h-1v1h0 2c-1 1-2 1-3 1l1 1h2c1 1 1 1 1 2v1h0-1c1 1 1 1 2 1v1 1h-1l1 1c-1 0-1 1-1 1h-3c-2 0-7 0-8 1v1l-1 1c-3 1-5 2-8 4-2 2-3 4-4 7l-8-4-7-5-1-1-6-4c0-1 1-1 1-1l1-2 1-1 3-3 1 2 2-1h1c2 0 1 0 2 1 3-1 5-1 7-2h3 0l3-1h1c3-1 6 0 8-1v-2c1 0 1-1 2-2h-3l1-1 6 1s0-1 1-1h1v-1h1 0l1-1h-1z" class="K"></path><path d="M257 577l4-1c1-1 3-1 4-1-2 1-4 2-5 4v1s0 1-1 1h0v-2h-3s1-1 1-2z" class="e"></path><path d="M248 575h0c0 1-1 3 0 4l2 1h2c2-1 4-2 5-3 0 1-1 2-1 2 0 1-1 1-2 2h0l-1 1-4 3-7-5c1-1 2-1 3-3l3-2z" class="d"></path><path d="M260 580c3-2 6-3 10-4v1l-1 1c-3 1-5 2-8 4-2 2-3 4-4 7l-8-4 4-3 1-1h0c1-1 2-1 2-2h3v2h0c1 0 1-1 1-1z" class="p"></path><path d="M257 568v1h0c-2 0-3 1-4 1s-1 1-2 1c1 1 1 1 2 1 0 0-2 2-3 2l-2 1h0l-3 2c-1 2-2 2-3 3l-1-1-6-4c0-1 1-1 1-1l1-2 1-1 3-3 1 2 2-1h1c2 0 1 0 2 1 3-1 5-1 7-2h3z" class="f"></path><path d="M244 576h0c0-1 1-1 2-2h1l1 1-3 2c-1 0-1 0-1-1z" class="e"></path><path d="M244 576c0 1 0 1 1 1-1 2-2 2-3 3l-1-1v-2c1 0 2 0 3-1z" class="k"></path><path d="M257 568v1h0c-2 0-3 1-4 1s-1 1-2 1c1 1 1 1 2 1 0 0-2 2-3 2 0-1 0 0 1-1h0-4 0 0c0-1 0-1 1-2h-1c-2 0-4 1-5 2 0-1 1-2 2-3v-1h1c2 0 1 0 2 1 3-1 5-1 7-2h3z" class="X"></path><path d="M241 568l1 2 2-1v1c-1 1-2 2-2 3l-1 1s-1 1-1 2h1v1 2l-6-4c0-1 1-1 1-1l1-2 1-1 3-3z" class="T"></path><path d="M238 571l2 1-3 2h-1l1-2 1-1z" class="M"></path><path d="M241 568l1 2-2 2-2-1 3-3z" class="Z"></path><path d="M278 559c1 0 3 1 4 2h0 1c0 1 1 1 2 2-1 0-2 1-4 1 0-1-1 0-2 0h-1v1h0 2c-1 1-2 1-3 1l1 1h2c1 1 1 1 1 2v1h0-1c1 1 1 1 2 1v1 1h-1l1 1c-1 0-1 1-1 1h-3c-2 0-7 0-8 1-4 1-7 2-10 4v-1c1-2 3-3 5-4h0c2 0 4-1 5-2h0-1c-1 0-3 1-5 1s-4 1-7 2h0c-1 0-2 1-3 1v-1s1-1 1-2l-1-1s1-1 1-2c0 0-1 0-2-1 1 0 2-1 4-1h0v-1h0l3-1h1c3-1 6 0 8-1v-2c1 0 1-1 2-2h-3l1-1 6 1s0-1 1-1h1v-1h1 0l1-1h-1z" class="D"></path><path d="M257 572v-1-1s0-1 1-1h0c2-1 4-1 6 0h3l-8 1h0l-1-1c0 1-1 2-1 3z" class="L"></path><path d="M261 567h6l-1 1c-1 0-2 0-2 1-2-1-4-1-6 0h0c-1 0-1 1-1 1v1 1h1c0 1 0 2-1 3v1h0c-1 0-2 1-3 1v-1s1-1 1-2l-1-1s1-1 1-2c0 0-1 0-2-1 1 0 2-1 4-1h0v-1h0l3-1h1z" class="V"></path><path d="M275 567l2-1 1 1h2c1 1 1 1 1 2v1h0-1c1 1 1 1 2 1v1 1h-1l1 1c-1 0-1 1-1 1h-3l1-1h0l-2-2c-1 0-1-1-2-2h-1c-1 0-2-1-3-1h-4-3c0-1 1-1 2-1l1-1h8z" class="Z"></path><path d="M271 569h7v1c0 1 0 1 1 2 1 0 1 1 2 1l1 1c-1 0-1 1-1 1h-3l1-1h0l-2-2c-1 0-1-1-2-2h-1c-1 0-2-1-3-1z" class="P"></path><path d="M278 559c1 0 3 1 4 2h0 1c0 1 1 1 2 2-1 0-2 1-4 1 0-1-1 0-2 0h-1v1h0 2c-1 1-2 1-3 1l-2 1h-8-6c3-1 6 0 8-1v-2c1 0 1-1 2-2h-3l1-1 6 1s0-1 1-1h1v-1h1 0l1-1h-1z" class="G"></path><path d="M278 559c1 0 3 1 4 2h0c-2 1-4 2-6 1h-1s0-1 1-1h1v-1h1 0l1-1h-1z" class="P"></path><path d="M271 562c1 0 3 1 5 2l-1 1h-3v1h1c1 0 1 0 2 1h-8-6c3-1 6 0 8-1v-2c1 0 1-1 2-2z" class="L"></path><path d="M274 103c1 0 2 0 3 1h0c3-1 5-2 8-1 2 0 5 0 6 1v1h-2 3l-15 2c-11 2-21 5-30 9-3 2-7 3-9 5-9 6-16 14-23 22-4 6-10 12-14 19-3 5-5 10-8 15 0 0 0 2-1 2l-1-1c3-7 8-13 10-19l2-2c1-1 0-1 0-1l3-3v-1h-1l-3 3v1s-1 0-2 1c0 0-1 0-1-1h-1l2-1c2 0 3-2 4-4v-5c0-1-1-2-1-3 0 0 1-1 1-2l-1-2h1c1-2 1-2 3-2 1 0 1 1 1 2 1 0 1 0 1 1h2c3-2 4-5 4-8 3-1 2-3 4-5 0-1 2-1 2-1 1-1 0-1 1-3l3-3c4-3 8-7 12-9h3c2-1 3-2 5-2 6-2 13-2 19-4l3-1c2 0 4-1 5-1 1 1 1 1 2 0z" class="I"></path><path d="M204 139c1-2 1-2 3-2 1 0 1 1 1 2 1 0 1 0 1 1l-1 1h4c-2 1-4-1-5 2v1c1 0 0 0 1 1h-1c-1 1-1 2-1 3-1 1-1 2-2 3v-5c0-1-1-2-1-3 0 0 1-1 1-2l-1-2h1z" class="C"></path><path d="M204 139c1-2 1-2 3-2 1 0 1 1 1 2 1 0 1 0 1 1l-1 1-1-1v1c-1 0-1-1-2-1l-1 1h0l-1-2h1z" class="L"></path><path d="M236 114v3h1l-1 1c-1 1-3 3-5 4s-3 3-6 4c-3 2-4 6-7 9 0-2 1-5 2-6v-1c1 0 1-1 1-1 2-1 2-1 3-3l4-4c3-2 5-4 8-6z" class="C"></path><path d="M222 123l3-3c4-3 8-7 12-9h3c2-1 3-2 5-2l-6 3-3 2c-3 2-5 4-8 6l-4 4c-1 2-1 2-3 3 0 0 0 1-1 1v1c-1 1-2 4-2 6l-6 6h-4l1-1h2c3-2 4-5 4-8 3-1 2-3 4-5 0-1 2-1 2-1 1-1 0-1 1-3z" class="V"></path><path d="M433 126c1 1 1 1 1 2v1c1 2 2 4 4 5h0c2 3 4 5 6 7 0 1 1 2 1 3 1 2 3 4 4 5 2 3 4 6 7 8h0c-2 0-2-1-3-2l-1 1c-1 0-4 1-5 2 2 3 3 5 4 7-1 0-2-1-3-2h0c-1 1-1 2-1 4l-1 1v1c-1 1-2 3-4 4h-1c-1 0-2 1-3 1h0c0 1-1 1-1 1 0 1 0 1-1 1s-1-1-2-1c0-1-2-1-3-1-2 0-3-1-5-1-2-1-3-2-4-3l1-1c1 0 1-1 2-1-1-1-2-1-3-3 1 0 1-1 1-2l1-1c0-1 0-1-1-2-1 0-2 0-3 1l-1-2 1-1h1c1-1 3-1 5-1 1-1 2-3 3-5 0 0 2-1 2-2s0-1 1-1v-1h0c0-1 0-1-1-2h-1c-1 1-1 2-1 3-1 1-2 1-3 1 0-1 0-1 1-1 1-2 2-3 4-4h-2l-1 1h-2-1-2l1-1c-1 0-2 0-3 1v-1l1-1c2-1 3-2 3-4h0s1-1 1-2c-1 0-1-1-2-1-1-1 0-1 0-2h-1v3l-1-1c0-1 0-1-1-2v2l-1 1v-1l-1-1c0-2-2-2-2-3l1-1s1-1 1-2l2 2 1 1c2-1 3-1 5-2 1 0 2-1 3-1 2-1 2-2 3-4z" class="m"></path><path d="M441 170c-1-1-1-1-1-2 1-1 2 0 3 0l-2 2z" class="B"></path><path d="M433 147c1 0 2 0 3 1h2c-1 1-3 1-4 2l-4 2c1-1 3-3 3-4v-1z" class="I"></path><path d="M439 144l2 4h0-1 0c-1-1-2-1-3-2 1 0 1-1 2-2z" class="S"></path><path d="M436 159c0-1 1-2 1-2 1-1 1-1 2-1l1 1 1 2c-2-1-2-1-3 0v1l-2-1z" class="o"></path><path d="M431 170c-1 0-1 0-1-1-1 0-1-1-1-2l1-1c2 0 3 0 4 1-1 1-1 1-2 1-1 1-1 1-1 2z" class="F"></path><path d="M432 173c2 0 4-1 6-2l-1 3h0 1 0c0 1-1 1-1 1 0 1 0 1-1 1s-1-1-2-1c0-1-2-1-3-1l1-1z" class="P"></path><path d="M438 160v-1c1-1 1-1 3 0h0v2c-1 1-2 2-3 2l-1-1c-1-1-1-2-1-3l2 1z" class="G"></path><path d="M438 160v-1c1-1 1-1 3 0h0l-1 1h-2z" class="M"></path><path d="M429 145c1-1 1-2 3-2 0 1 1 2 1 2h2c0 1 1 1 1 1h1c1 1 2 1 3 2h0c0 1-1 0-2 0h-2c-1-1-2-1-3-1-1-1-1-2-2-2h0-2z" class="H"></path><path d="M425 168c1 0 1 1 1 2 2 2 3 3 6 3l-1 1c-2 0-3-1-5-1-2-1-3-2-4-3l1-1c1 0 1-1 2-1z" class="C"></path><path d="M425 168c1 0 1 1 1 2-1 0-2 0-3-1 1 0 1-1 2-1z" class="I"></path><path d="M434 167l2-3h1v1 2h1l1-1v-1c0-1 1-1 2 0l-1 1c-1 1-1 1-2 1h0c-1 1-2 2-3 2-1 1-3 1-4 1 0-1 0-1 1-2 1 0 1 0 2-1z" class="B"></path><path d="M440 157l1 1 2-1 1 1v1h-1c0 1 1 1 2 2 0 1 0 1 1 2h1c0-1-1-1 0-2 1 0 1 0 1 1v1h0c-1 1-1 2-1 4l-1 1v1c-1 1-2 3-4 4h-1c-1 0-2 1-3 1h-1 0l1-3c1 0 2-1 3-1l2-2c1-1 1-1 1-2 0-3-1-3-3-5v-2h0l-1-2z" class="S"></path><path d="M433 126c1 1 1 1 1 2v1c1 2 2 4 4 5h0c2 3 4 5 6 7 0 1 1 2 1 3 1 2 3 4 4 5 2 3 4 6 7 8h0c-2 0-2-1-3-2l-1 1c-1 0-4 1-5 2 2 3 3 5 4 7-1 0-2-1-3-2v-1c0-1 0-1-1-1-1 1 0 1 0 2h-1c-1-1-1-1-1-2-1-1-2-1-2-2h1v-1l-1-1s1-1 2-1c0 0 0 1 1 1h2c1-1 3-1 3-2-1-2-2-4-4-6 0-1-1-2-1-3-1-1-2-2-2-3l-1-1c-3-4-7-8-10-12-4 1-7 2-9 5h-1v3l-1-1c0-1 0-1-1-2v2l-1 1v-1l-1-1c0-2-2-2-2-3l1-1s1-1 1-2l2 2 1 1c2-1 3-1 5-2 1 0 2-1 3-1 2-1 2-2 3-4z" class="K"></path><path d="M427 136c2-1 3-1 5 0 1 0 1 0 2 1s2 2 2 3l3 4c-1 1-1 2-2 2h-1s-1 0-1-1h-2s-1-1-1-2c-2 0-2 1-3 2l-1 1h-2-1-2l1-1c-1 0-2 0-3 1v-1l1-1c2-1 3-2 3-4h0s1-1 1-2l1-2z" class="D"></path><path d="M425 140h2l1 1h1v-1s1 0 2 1h1v1h0-2 0c-1 0-1 1-2 1h-2c0 1-1 2-2 2s-2 0-3 1v-1l1-1c2-1 3-2 3-4h0z" class="G"></path><path d="M429 140c3-3 4 1 7 1v-1l3 4c-1 1-1 2-2 2h-1l1-2h-2c-1 0-1-1-1-1l-2-1h0v-1h-1c-1-1-2-1-2-1z" class="J"></path><path d="M427 136c2-1 3-1 5 0 1 0 1 0 2 1s2 2 2 3v1c-3 0-4-4-7-1v1h-1l-1-1h-2s1-1 1-2l1-2z" class="o"></path><path d="M427 136c2-1 3-1 5 0 1 0 1 0 2 1 0 1 0 1-1 1h-4l-2-2z" class="M"></path><path d="M319 85l3 1h1c1 1 2 1 4 2v1c1 0 3 2 4 3s2 2 2 3h1c0 1-1 1-1 2h1c-2 1-3 1-5 1-1 2-1 2-3 3h0c-1 1-2 2-4 2l1 1c2 2 6 2 9 3l12 3c13 3 27 10 38 18 5 4 10 9 15 14 2 0 3 0 4 1h0v1h1c0 1-1 1-2 1h1v1l11 15c8 11 15 25 20 38 4 9 7 19 9 29l6 27c1 5 1 11 3 17 0 2 2 3 3 5h-1c-1-1-1-2-2-3l-1-1v1c-2 7 0 16-6 21 0-1 1-1 1-2 2-3 3-5 3-8v-9c0-7 0-14-1-20 0-6-2-11-3-17-8-36-23-72-49-99-13-13-28-21-45-27-5-2-10-3-15-4s-10-2-14-2l-28-2h-3 2v-1c-1-1-4-1-6-1-3-1-5 0-8 1h0c-1-1-2-1-3-1-1 1-1 1-2 0-1 0-3 1-5 1l-3 1s1-1 2-1c-1-1-1-2-1-3h0 0c0-1 1-2 1-3l-1-1h2c4 0 7 0 10 1l1 1c1-1 1-2 2-2l4-3h0c1 0 2 2 3 2s2-1 3-1v-4h0 0c1 3 2 5 4 8h5 0l1-1c1 0 2-1 3-1v-1c1-1 2 1 3 1 1-1 1-2 2-3h1c1-1 0-3 1-4 2-3 5-4 9-5z" class="C"></path><path d="M397 142c2 0 3 0 4 1h0v1h1c0 1-1 1-2 1h1v1l-4-4z" class="m"></path><path d="M326 99l3-1h0c-1 2-1 2-3 3h-4c-1 1-3 1-4 1l1-1 1-1h1 2 2c1 0 1 0 1-1z" class="H"></path><path d="M308 94h1l3 3h0c3 2 4 3 8 3l-1 1-1 1-3-1v-1c-1 0-2 1-3 1-3 2-9 1-12 1h0 4l1-1-1-1h-1l1 1v1h-2v-1-1l-1-2h-1c1 0 2-1 3-1v-1c1-1 2 1 3 1 1-1 1-2 2-3z" class="P"></path><path d="M290 91h0c1 3 2 5 4 8h5 0l1-1h1l1 2v1 1h2v-1l-1-1h1l1 1-1 1h-4 0c-2 1-5 0-7 0-1 1-1 1-3 1s-4-1-7-1c-2 0-4 1-7 1h-2c-1 1-1 1-2 0-1 0-3 1-5 1l-3 1s1-1 2-1c-1-1-1-2-1-3h0 0c0-1 1-2 1-3l-1-1h2c4 0 7 0 10 1l1 1c1-1 1-2 2-2l4-3h0c1 0 2 2 3 2s2-1 3-1v-4h0z" class="D"></path><path d="M280 97c1 0 1 0 2 1v1c-2 0-4 1-6 0h2c1-1 1-2 2-2z" class="o"></path><path d="M265 97h2 0c0 1 0 2-1 3l8-1-1 1c-2 1-4 1-6 2h-1 0 0 3l1 1-3 1-3 1s1-1 2-1c-1-1-1-2-1-3h0 0c0-1 1-2 1-3l-1-1z" class="V"></path><path d="M267 97c4 0 7 0 10 1l1 1h-2-2l-8 1c1-1 1-2 1-3h0z" class="F"></path><path d="M282 99c1 0 2-1 3-1 2 2 6 2 9 3v-2h5 0l1-1h1l1 2v1 1h2v-1l-1-1h1l1 1-1 1h-4-7c-1 0-2-1-4-1-2-1-4-1-7-2z" class="S"></path><path d="M300 98h1l1 2c0 1 0 1-1 1l-2-2 1-1z" class="K"></path><path d="M294 99h5c-2 1-2 2-4 2h-1v-2z" class="n"></path><path d="M290 91h0c1 3 2 5 4 8v2c-3-1-7-1-9-3-1 0-2 1-3 1h0v-1c-1-1-1-1-2-1l4-3h0c1 0 2 2 3 2s2-1 3-1v-4h0z" class="H"></path><path d="M284 94v2c0 1 2 0 3 0v1l-2 1c-1 0-2 1-3 1h0v-1c-1-1-1-1-2-1l4-3z" class="J"></path><path d="M319 85l3 1h1c1 1 2 1 4 2v1c1 0 3 2 4 3s2 2 2 3h1c0 1-1 1-1 2h1c-2 1-3 1-5 1h0l-3 1c0 1 0 1-1 1h-2-2-1c-4 0-5-1-8-3h0l-3-3c1-1 0-3 1-4 2-3 5-4 9-5z" class="R"></path><path d="M326 89l6 6v1l-1 1c0-1-1-1-1-2h-2-1c0-2-1-4-1-6z" class="U"></path><path d="M316 93h1 2c1 0 3 0 4 1 0 1-1 2-1 3 1 2 2 2 4 2h0c0 1 0 1-1 1h-2-2v-2l-2-1h-1c-1 1-1 1-2 1v-1h1v-3l-1-1z" class="I"></path><path d="M322 97l-4-3h5c0 1-1 2-1 3z" class="Q"></path><path d="M317 88l1-1c1-1 2-1 4-1h1c1 1 2 1 4 2v1c1 0 3 2 4 3s2 2 2 3h1c0 1-1 1-1 2h1c-2 1-3 1-5 1h0l2-1 1-1v-1l-6-6h-1v-1c-1-1-2-1-3 0h-1l2 2v1c-1-1-2-1-2 0h-1v-1h-1-2v-2z" class="B"></path><path d="M319 85l3 1c-2 0-3 0-4 1l-1 1v2c-1 0-2 1-2 2l1 1 1 1v3h-1v1c1 0 1 0 2-1h1l2 1v2h-1c-4 0-5-1-8-3h0l-3-3c1-1 0-3 1-4 2-3 5-4 9-5z" class="m"></path><path d="M319 97l2 1v2h-1c-4 0-5-1-8-3l2 1h1l1 1h3v-2z" class="E"></path><path d="M317 88v2c-1 0-2 1-2 2l1 1 1 1v3h-1c-1 0-2-1-3-2s0-2 0-4c1-1 2-2 4-3z" class="D"></path><path d="M317 88v2c-1 0-2 1-2 2l1 1 1 1c-1 1-1 1-2 1v-3c-1-1-1-1-2-1 1-1 2-2 4-3z" class="H"></path><path d="M228 582c2 1 3 2 5 3 0 1-1 2-1 3v2h1 1v1c0 2-1 4-1 6l2 1v-1h0l1 1 1-1h1l2-1v-1c1 0 2 0 3-1l1 1h0 1 0c0 1 0 2-1 2 1 2 2 2 4 3 4 3 9 4 14 5l10 2v1h-1 2s1 0 1 1c2 0 4-1 6 0 2 0 4 1 6 2 1 1 2 1 3 2l3 3v3 1h-4v-3c-1 0-1 1-2 2v-1c-1 0-4-1-6 0-1 0-3 0-4-1l-1 1v1-1c-1 1-2 2-3 4v1l-3 3-1 1-6-3c-1 0-2-1-2-2l-1-1c-1-1 1-4 2-5-3-1-6-1-9-1-4 0-8 0-12-1h0l1-1c-9-3-18-7-23-15l-1-3c0-2 0-4 1-6v-1c0-2 1-2 3-3l-1-1c2-2 6-1 8-2z" class="m"></path><path d="M228 582c2 1 3 2 5 3 0 1-1 2-1 3v1h-1l-2-1c1 0 1 0 2-1 0-1 0-2-1-3h0-1c-3 0-6-1-8 1l-1-1c2-2 6-1 8-2z" class="E"></path><path d="M222 592l1 1h2 0c0 1-1 2 0 3v1c1 1 1 2 2 3s1 2 3 4l2 2h0c-4-2-8-5-9-9-1-2-1-4-1-5z" class="P"></path><path d="M275 618c-2-1-3-2-4-3 2 1 4 1 5 2h3c0-1 1-1 2-1h-1c0-1 0 0 0 0 1 0 1-2 2-2l1 1c1 0 1 1 2 1s1 0 2-1h1v2c-1 0-1 1-2 2v-1c-1 0-4-1-6 0-1 0-3 0-4-1l-1 1v1-1z" class="B"></path><path d="M241 613h2c2 0 3 0 5 1 3 0 6 0 10 1h2c1 0 2 1 2 1-1 2-2 3-2 6l-1-1c-1-1 1-4 2-5-3-1-6-1-9-1-4 0-8 0-12-1h0l1-1z" class="I"></path><path d="M236 603v-1c0-1 0-2 1-2s2 1 3 2c1 3 4 5 6 6l1 1c-4-1-9-2-11-6z" class="S"></path><path d="M240 602s1 0 1-1c1-1 0-1 1-1l2 1h0l1 1c4 1 6 4 10 4v1h0c1 1 1 1 1 2h1 0v-1c2-1 6 2 8 2 1 0 3 1 4 1 0 1 0 2-1 2l-1-1c-1-1-7-1-9-1-1-1-3-1-4-2-1 1-1 2-1 2-2 0-4-1-6-2l-1-1c-2-1-5-3-6-6z" class="F"></path><path d="M246 608c2 1 5 1 7 2v1c-2 0-4-1-6-2l-1-1z" class="L"></path><path d="M254 609c-2-2-4-3-7-4-1-1-3-2-5-3 3 0 6 2 9 4 1 1 2 1 4 1h0 0c1 1 1 1 1 2h1 0v-1c2-1 6 2 8 2 1 0 3 1 4 1 0 1 0 2-1 2l-1-1c-1-1-7-1-9-1-1-1-3-1-4-2z" class="H"></path><path d="M224 589c2-1 2-1 3-1h2l2 1h1v-1 2h1 1v1c0 2-1 4-1 6l2 1c0 1 0 1-1 2l-2 2h-2v-1c0 2 0 2 1 3 0 1 0 1 1 2l-2-2c-2-2-2-3-3-4s-1-2-2-3v-1c-1-1 0-2 0-3h0-2l-1-1c1-2 2-2 2-3z" class="D"></path><path d="M225 593c2-2 3-2 5-2 0 1-1 1-2 2h-3 0z" class="L"></path><path d="M225 597v-1c0-1 1-1 2-2 1 0 1 1 1 2h-1v1 3c-1-1-1-2-2-3z" class="C"></path><path d="M224 589c2-1 2-1 3-1h2l2 1h1v-1 2h1 1v1c-1 0-2 0-2 1h-1l-1-2c-1 0-2-1-3-1h-2-1z" class="F"></path><path d="M224 589h1 2c1 0 2 1 3 1v1c-2 0-3 0-5 2h-2l-1-1c1-2 2-2 2-3z" class="K"></path><path d="M230 596l1-1c1 1 1 2 1 3h0l1-1 2 1c0 1 0 1-1 2l-2 2h-2v-1c0 2 0 2 1 3 0 1 0 1 1 2l-2-2c-2-2-2-3-3-4v-3-1h1 1c0 1 0 2 1 3v-3z" class="B"></path><path d="M230 596l1-1c1 1 1 2 1 3h0l1-1 2 1c0 1 0 1-1 2l-2 2h-2v-1c0 2 0 2 1 3 0 1 0 1 1 2l-2-2c0-1 0-2-1-2v-1h0c1-1 2 0 3 1v-1c-1-2-2-2-2-5z" class="U"></path><path d="M240 596v-1c1 0 2 0 3-1l1 1h0 1 0c0 1 0 2-1 2 1 2 2 2 4 3 4 3 9 4 14 5l10 2v1h-1 2s1 0 1 1c2 0 4-1 6 0-1 0-2 0-3 1h-2 4l-2 2h0c1 1 0 2 0 3h-1v-3c1 0 1 0 1-1-1-1-4 0-5 0h-2c0 1 0 1-1 0-1 0-3-1-4-1-2 0-6-3-8-2v1h0-1c0-1 0-1-1-2h0v-1c-4 0-6-3-10-4l-1-1h0l-2-1c-1 0 0 0-1 1 0 1-1 1-1 1-1-1-2-2-3-2s-1 1-1 2v1h-1v1c0-2-1-3-1-4 1-1 1-1 1-2v-1h0l1 1 1-1h1l2-1z" class="I"></path><path d="M240 596v-1c1 0 2 0 3-1l1 1h0 1 0c0 1 0 2-1 2 1 2 2 2 4 3-2 0-6-3-8-4z" class="D"></path><path d="M462 202c-1-2-1-4-2-6 0-1 0-2-1-3v-2l1-2h1v1c1 2 2 4 2 6 0 4 0 7 1 10h1c1-2 2-2 3-2v-1h-2c-1-1-1-2-2-3l1-1h5c1 1 1 2 1 3v1l1 1c0 1 0 2 1 2h1v1h-1l-1 2 1 2c2 7 3 15 3 23-1 2-3 4-4 6l-2 2v2l-1-1c0 2 1 2-2 3h0v1l-2 1v-4h-1c-1 0-1 1-2 2-1 0-2 1-3 1 1-1 0-2 0-3h-2c-1 0-2 0-4-1-1 0-3-2-4-4v-2h-1-1 0-1c0-1 1-1 1-2 0 0-1 0-1-1s-1-2-1-2c0-3 0-5-1-7 0-2-2-5-2-7l1-1 1-1v-1l2 1v-1c0-1 1-1 1-2h0c0-1 0-2 1-2l1 1v2h1c0-2 2-3 3-5l1-1c-1 0-3-2-3-3h1c0-1 0-1 1-2l3-1c1 0 2 1 3 1v-2c1 0 2 1 3 1z" class="R"></path><path d="M459 201c1 0 2 1 3 1v2h-1 0c-1-1-2-1-2-1v-2z" class="m"></path><path d="M466 242h1v-1l-1-2 1-1c0 1 1 1 2 1h0l1 1c-1 0-1 0-1 1v1h-2-1z" class="E"></path><path d="M452 224l1-1 2-2s-1 0-1-1c-2-1-4-2-6-4l1-1c1 1 2 3 4 4l3 1v1c-1 2-2 2-3 3h-1z" class="F"></path><path d="M459 244c2 0 3-2 5-2 1-1 1 0 2 0h1v1c-1 1 0 1-2 2v-1h-1c-1 0-1 1-2 2-1 0-2 1-3 1 1-1 0-2 0-3z" class="G"></path><path d="M469 239c0-2 0-2-1-3h-3v-1c2-1 2-2 3-3v-1 3h1l-1 1c1 2 2 4 4 5l-2 2v2l-1-1c0 2 1 2-2 3h0v1l-2 1v-4 1c2-1 1-1 2-2v-1h2v-1c0-1 0-1 1-1l-1-1z" class="D"></path><path d="M465 206c1 1 2 1 2 3h1 0v-3h1 0c0 3-2 8-1 10v1c-1-1-1-2-2-2 0 3 1 6 1 9v1c-1 1-1 1-1 2h0c0 1-1 3-2 4h0c0 1-1 1-2 2h0c1-2 2-3 2-4v-1c1-1 1-1 1-2v-1c1-2 1-5 0-7v-4c1-2 0-6-1-8h1z" class="E"></path><path d="M454 208c-1 0-3-2-3-3h1c1 1 3 3 5 4v1h0c-1 0-2 2-2 3-1 1-1 2 0 3s3 2 5 2c1-1 1-2 1-3 0-2 0-3-1-4h0c2 0 3 1 3 2 1 3 1 6 1 9-1 2-1 5-3 6l-1 1v-1l1-1c-1-2-2-2-2-4-1-1-1-2 0-4-2-1-4-2-5-4-1-1-1-2 0-2 0-2 2-3 1-4l-1-1z" class="B"></path><path d="M447 213c0 1 1 1 2 2l-1 1c2 2 4 3 6 4 0 1 1 1 1 1l-2 2-1 1h1c-2 2-3 3-4 5v1l1 1v2l3 4h-1c-1-1-2-1-2-3h-1v3h-1-1 0-1c0-1 1-1 1-2 0 0-1 0-1-1s-1-2-1-2c0-3 0-5-1-7 0-2-2-5-2-7l1-1 1-1v-1l2 1v-1c0-1 1-1 1-2z" class="E"></path><path d="M445 218l2-1s1 1 0 2h-3v-1h1z" class="F"></path><path d="M444 215l2 1v-1l1 2-2 1c0-1-1-1-1-2v-1z" class="B"></path><path d="M447 231h-1v-3l-1-2v-4c0 1 1 2 2 3 0 1 1 2 2 2l-1 1-1-1v1s0 1 1 2l-1 1z" class="R"></path><path d="M449 227s0-1 1-1v-1l-3-3 1-1 2 2c1 0 1 0 1 1h1 1c-2 2-3 3-4 5v1l1 1v2h-1c-1 0-2 1-2 2l-1-1c0-1 1-1 2-1h0c-1-1-1-1-1-2l1-1c-1-1-1-2-1-2v-1l1 1 1-1z" class="B"></path><path d="M470 199c1 1 1 2 1 3v1l1 1c0 1 0 2 1 2h1v1h-1l-1 2 1 2c2 7 3 15 3 23-1 2-3 4-4 6-2-1-3-3-4-5l1-1h-1v-3c0-1 0-1 1-2 1-4 1-9-1-12v-1c-1-2 1-7 1-10h0-1v3h0-1c0-2-1-2-2-3 1-2 2-2 3-2v-1h-2c-1-1-1-2-2-3l1-1h5z" class="R"></path><path d="M472 209c-1-1-2-2-2-3 0-2 0-2 1-3l1 1c0 1 0 2 1 2h1v1h-1l-1 2z" class="Q"></path><path d="M468 216c3 2 3 5 3 9 0 3-1 6-2 9h-1v-3c0-1 0-1 1-2 1-4 1-9-1-12v-1z" class="L"></path><path d="M211 155h1l1-1h0v1h0 0c-3 6-6 11-9 17-6 11-11 24-15 36-2 9-4 18-5 26-4 19-5 39-5 58l-1 17v36 43l1 19 2 34 2 21 4 26c2 8 4 16 7 24 1 5 2 10 4 15 2 4 3 8 5 12 2 3 4 6 6 10 5 7 10 14 16 20l6 6 2 2h0c1 0 2 1 2 2 1 0 2 1 3 1 2 2 5 5 8 6 0 0 0 1 1 1h0c2 2 4 2 6 3 1 0 2 1 3 2 1 0 2 0 2 1 1 0 3 1 4 1 14 6 29 8 44 10v1h-1c-18-2-38-6-55-14-6-4-13-9-19-14-10-7-17-16-23-26-15-25-23-54-27-84l-5-54-1-59v-30l1-20v-28c2-32 7-66 20-96 4-9 9-17 15-25z" class="F"></path><path d="M331 92h0c1 0 2 1 3 2 0-1 0 0-1-1 0-1 0-1 1-1l-1-1h0l8 4 8 3c4 2 8 3 12 4 2 1 7 1 9 3 0-2 0-4 2-4 1-1 2-1 2-1 2 1 3 3 4 4-1 2-2 4-5 4l-1 1 1 1 3 3 1 1c1 0 2 1 3 1h2l1 1c3 1 6 2 8 3l1-2v2l-1 1v1c3 1 5 2 8 4 3 3 9 12 12 11v-1l2-2v-1h2c1 0 1 0 2 1 0 1 2 1 2 3l1 1v1l1-1v-2c1 1 1 1 1 2l1 1v-3h1c0 1-1 1 0 2 1 0 1 1 2 1 0 1-1 2-1 2h0c0 2-1 3-3 4l-1 1-5 2c0 1 1 1 1 1v1h-1-1c-2 0-7-2-8-3l-1-1-1 1-3-2h-1v-1h0c-1-1-2-1-4-1-5-5-10-10-15-14-11-8-25-15-38-18l-12-3c-3-1-7-1-9-3l-1-1c2 0 3-1 4-2h0c2-1 2-1 3-3 2 0 3 0 5-1h-1c0-1 1-1 1-2h-1c0-1-1-2-2-3z" class="q"></path><path d="M391 126v1c0 1 0 2-1 3h-1c0-1 1-2 1-3l1-1z" class="F"></path><path d="M402 142h3c1 0 1 1 2 2h-2c-1-1-2-1-3-1v-1z" class="P"></path><path d="M341 103h4c0 1-1 2-2 2l-3-1 1-1z" class="E"></path><path d="M399 132c2 1 2 2 3 4-1 0 0 1-1 2-1-1-1-2-2-2v-4z" class="U"></path><path d="M367 107h0l1 1s1 1 2 1v1h0-3c-1 1-1 1-2 0-1 0-1 0-1-1l3-2z" class="L"></path><path d="M367 107h0l1 1s1 1 2 1v1c-1 0-2 0-3-1v-2z" class="P"></path><path d="M388 129l-2-1c1-2 1-3 1-5h1c0 1 0 1 1 2 1 0 1 1 2 1l-1 1c0 1-1 2-1 3l-1-1z" class="B"></path><path d="M388 129c0-1 0-2 1-2h1c0 1-1 2-1 3l-1-1z" class="U"></path><path d="M367 107l2-1c1 1 3 2 3 3l1 1 3 3-1 1c-1-2-2-3-4-4h-1 0v-1c-1 0-2-1-2-1l-1-1z" class="D"></path><path d="M340 104c-3-1-8-1-10-3v-1c2 0 3 1 4 1 2 1 5 1 7 2l-1 1z" class="I"></path><path d="M420 138l1-1v-2c1 1 1 1 1 2l1 1v-3h1c0 1-1 1 0 2 1 0 1 1 2 1 0 1-1 2-1 2h0-2c-1-1-1-1-2 0-2 1-3 2-5 2 2-1 4-2 4-4z" class="B"></path><path d="M413 132h2c1 0 1 0 2 1 0 1 2 1 2 3l1 1c-1 0-2-1-2-1-2 0-4 0-6 1v-1h-1v-1l2-2v-1z" class="U"></path><path d="M413 132h2v1c-1 2-1 2-3 3h-1v-1l2-2v-1z" class="L"></path><path d="M415 132c1 0 1 0 2 1 0 1 2 1 2 3h-1c-2-1-2-1-2-2l-1-1v-1z" class="B"></path><path d="M401 143l1-1v1c1 0 2 0 3 1h2c1 1 3 2 4 3h5 0c0 1 1 1 1 1v1h-1-1c-2 0-7-2-8-3l-1-1-1 1-3-2h-1v-1z" class="H"></path><path d="M345 103c2 1 3 1 5 2v1h1c1 1 1 1 2 1l1 1h0l-1 2h-1l-2-2h-1c-2 1-2-1-3-1 0-1-1 0-1-1-1 0-1 0-2-1 1 0 2-1 2-2z" class="m"></path><path d="M376 113l1 1c1 0 2 1 3 1h2l1 1h0c1 1 1 1 1 2h-1l-2-1c0 2-2 3-3 5h-1c-1-1-3-2-4-2 2-1 4-3 4-5-1 0-1-1-2-1l1-1z" class="I"></path><path d="M376 120h-1l1-1c0-1 1-1 1-2h1c1 1 1 2 0 3l-1 1-1-1z" class="E"></path><path d="M377 114c1 0 2 1 3 1h2l1 1h0c1 1 1 1 1 2h-1l-2-1h-3c-1-1-1-2-1-3z" class="C"></path><path d="M350 105l2 1h1c2 1 3 2 5 3h1c2 1 4 3 6 3v1h-2 0v1h2c1 2 1 2 3 2h0c-1-1-1-1-1-3 1-1 1-1 2-1l1-1 1-1c2 1 3 2 4 4 1 0 1 1 2 1 0 2-2 4-4 5-3-2-8-3-11-6-2-1-3-1-5-2-1-1-3-1-4-2l1-2h0l-1-1c-1 0-1 0-2-1h-1v-1z" class="E"></path><path d="M374 115h1v1h-1l-1 1v-1l1-1z" class="R"></path><path d="M371 113h1 1c-1 2-1 3-2 4h-1l-1-1 2-1v-2z" class="m"></path><path d="M383 116l8 3 1-2v2l-1 1v1c3 1 5 2 8 4 3 3 9 12 12 11h1v1l-4 2-6-3c-1-2-1-3-3-4-1 0-2-1-2-2h0c-2 0-2 2-4 2-1 0-1 0-2-1h0c1 0 1 0 2 1v-4l1-1c0 1 1 1 1 1h1l-2-2s-1-1-2-1l-1-1h0l1-1v-1c-1 0-1 1-2 1s-2-2-2-2c-2-1-3-2-5-3h1c0-1 0-1-1-2h0z" class="I"></path><path d="M358 398l1 1 1-1h1 2l2-1h2l1-1h3c2 0 5 1 7 1 2 1 5 1 7 1h0v1l-5 1-1 1h2c2 2 3 2 5 1h1c1 0 2 0 2 1v1c7-2 13-2 20-1l-3 1h0v1h-1c-2 0-4 0-6 1-2 0-3 0-5 1v1h10 3l1 1h4c1 0 2 1 4 1-1-1-1 0-2-1 4 0 9 2 11 1l6 1 3 3c3 1 5 2 7 4h1l1 1c2 0 5 0 7 1-4 0-9-1-13 1 2 0 5 2 7 1 8 1 12 4 18 10 3 4 5 8 8 13 0 1 2 6 3 6 1 1 2 3 2 5 3 7 4 15 4 22v1c1 1 1 2 1 4 1 2 0 3 1 5v3c0 3 0 6-1 8h1c0-2 0-3 1-5v-2-2h0 0v-1h0v-4h0c1 2 0 5 1 7l-1 7v4l-2 7 1 1v2c-1 3-2 5-3 8 0 1 0 1 1 2-1 2-4 5-4 7 0 1-2 5-3 5-1 1-1 1-2 1l-2 4-7 10-1 2c1-1 2-2 3-2 2-1 4-4 5-5l1 1c-1 1-2 3-3 4l1 1c-1 1-3 3-4 5l-13 15h0l-6 7-2 2-7 6c-1 1-4 4-6 4h0c-2 1-4 5-6 5-1 1-1 1-1 2-1 1-2 2-3 2h-1l3-4c0-1 5-6 6-7l-1-1c-2 1-3 2-4 2 0-1 1-1 2-2v-1-2l-1 1h0v-1-1c-1 0-1-2-1-3v1c-1 1-2 2-3 4-2 2-5 4-7 6s-4 4-7 6c2-3 6-6 7-9-2 2-4 5-7 6v-1h0-1l-1-1-19 15c-6 4-13 9-21 10-2 0-4 0-5 1-2 1-4 1-6 2h-2 0c0 1-1 1-2 0 0 0-1 0-1-1v-2h2c-1-1-2 0-2-1l-1 1h0v3l1 1h-1v-1l-1-1v-3c-1-1-2-2-3-2 2-1 4-1 5-1l3-1h1l2-2c1-1 3-1 4-1h0c3 0 4-1 6-2 1-1 3-2 4-4h-1l1-2v-2l1-1 2-1c1-1 2-1 2-2l1-1v-1c1-2 1-3 1-5v-1c2 1 2 3 3 4v-5-1h0l1 2c1 0 2 1 3 1 0 0-1-1 0-2h0c0-1-1-3-1-4-1-1-1-1-1-2-2-1-3 0-4 0-2 1-4 2-4 4v2c0 1 0 2 1 3v1c-3 3-8 4-12 5l-10 3-6 1c-1 0-2 0-3 1h-5-1l-2 1h-11c-6 1-13 1-20 0-2 0-5-1-8-1l-2-1c-2 0-3 0-5-1h-1c-1-1-1-3 0-5-1 1-2 1-3 2 0-1-1-1-1-1h-3s-1 0-1 1l-1 1-5-1c-1 0-3-1-4-2l-6-2-5-2c1-3 2-5 4-7 3-2 5-3 8-4l1-1v-1c1-1 6-1 8-1h3s0-1 1-1l-1-1h1v-1-1c-1 0-1 0-2-1h1 0v-1c0-1 0-1-1-2h-2l-1-1c1 0 2 0 3-1h-2 0v-1h1c1 0 2-1 2 0 2 0 3-1 4-1-1-1-2-1-2-2h-1 0c-1-1-3-2-4-2l-8-4c-1 0-3-1-4-2l-1-1c-2-1-4-2-5-3l1-1c1-1 1-1 1-2h1 1l-1-1c-2-2-3-4-4-7l-1-5v-5l2-5v-1h1 1v2l1-4v-2c-1-1-1-1 0-2h0l1 1v2l2-1c-2-3-3-5-4-8 0-2 0-4-1-5v-5h-1v-2l-2 3c-2 0-3 1-4 2l-1-1c1-1 2-1 2-2h0l-2 1-2-2 2-2c1-1 2-3 2-4 1-2 2-5 3-7v-1l1-1c1-1 1-3 1-4h-4s0-1-1-1-2-1-3-2l1-1c-1-1-1-1-1-2h2 0 1c1 0 1 0 2-1h2 0c0-1 0-1 1-1l1 1c1 0 1-1 2-1 1-3 4-5 6-8h0l4-5 6-7c-4 3-7 4-12 5-5 0-8-1-12-3h1 0c3-1 5 0 8-1h2l6-3 7-3c2-2 4-4 6-5 0-1-2 0-3-1 0-1 1-1 1-2l4-2 1-1c3-1 5-4 7-5v-2c-1 1-1 1-2 1v-1h-1-1v-1c0-1 0-2-1-3h0v-1s0-1 1-1c0-1-1-1-1-1 0-1 0-2 1-3l1 1 3-1 1-2c3-1 6-2 8-3l2-1h0c2 0 4-1 6-1v1l-1 1h2 0c1 0 2 0 3-1l3-2h2 1c6-3 12-5 18-7 3-2 7-2 10-3 2 0 3 0 4-1h0c1 0 1-1 1-1 1-1 2-1 3-1z" class="p"></path><path d="M313 538l3 2h-3 0v-2z" class="Y"></path><path d="M431 465c1-1 2-1 4-1-1 1-1 1 0 3-1-1-3-2-4-2z" class="g"></path><path d="M368 445c-3 1-5 3-9 3 2-1 6-3 8-3h1z" class="k"></path><path d="M377 520h1 0 0c0 1 1 2 1 3l-1 1v-1h-1v1h-1c0-1 1-2 1-4z" class="e"></path><path d="M436 530s2 0 3 1c-1 0-1 1-1 1h-4l-1 1v-1c0-1 1-1 3-2z" class="C"></path><path d="M399 417h3c3 2 8 0 11 1h-13-4c1-1 2-1 3-1z" class="J"></path><path d="M422 518c1 1 1 3 1 4s0 2-1 3c0 0-1-1-1-2h-1c0-1 0-1 1-2h0c1-1 1-2 1-3z" class="k"></path><path d="M305 568h1c1 1 3 0 5 1-1 0-1 1-2 1h-2-1 0l1-1h-2 0c-1 1-2 0-3 0-1-1-1 0-1 0h-1c-1 1-2 0-3 0 1 0 2 0 3-1h0c1 0 2 0 3 1 1 0 1-1 2-1z" class="j"></path><path d="M324 501c0 1 0 2 1 3 0 3-2 7 0 9v1 1 3c-1-3-1-6-1-9v-1c-1-1-1-2-1-3 1-1 1-3 1-4z" class="e"></path><path d="M343 530v2c1 0 1 0 1 1-2 2-4 4-7 5 2-2 4-5 6-8z" class="d"></path><path d="M378 506v2 2c0 2 0 3-1 5 0 2-1 4-2 7 0-3 1-5 1-8 1-1 1-3 1-5 0-1 0-2 1-3z" class="W"></path><path d="M324 501c0-1 1-1 1-2s0-2 1-3l1 2c-1 2-1 3-1 4s-1 2-1 3c1 3 0 5 0 8-2-2 0-6 0-9-1-1-1-2-1-3zm-15 3h0c1-1 1-2 1-3 0 4-1 9 0 13l3 9-1-2c-1-1-1-3-2-5-1-3-1-7-2-11l1-1z" class="g"></path><path d="M354 447h1 0c-5 3-12 6-16 10l-1 1-1-1 17-10z" class="e"></path><path d="M352 569l1 1c-3 1-5 1-7 2v1c-3 1-5 1-8 1 1-1 3-1 4-2l10-3z" class="a"></path><path d="M352 569c2-1 4-1 6-1h0 0 0l1 1-5 2-8 2v-1c2-1 4-1 7-2l-1-1z" class="j"></path><path d="M398 513c1 1 2 3 3 4l-1 2-1 2v-1h-1c0 1 0 1-1 2h-1v-1c1-1 1-1 1-2 1-2 1-4 1-6z" class="f"></path><path d="M398 520v-1l2-2h0v2h0l-1 2v-1h-1z" class="i"></path><path d="M314 493c0-2 0-3 1-5 0-2 0-5 1-7v-1h0v2-1c1 0 1 0 1-1h0 0l-1 9h0 1c0-2 0-3 1-4l1-1v1c-1 0-1 1-1 1 0 1-1 2-1 3v1l-1 2c0-1-1-1-1-2l-1 3zm66-44c4 0 11 0 15 2h-1-1 0 0v1l-1-1c-5-1-11-1-17-1 1 0 4-1 5-1z" class="g"></path><path d="M359 552v1h0l-1 2c-1 2-3 4-6 4h-1l-1-1 1-1c2 0 6-3 8-5z" class="b"></path><path d="M304 548c2 1 3 1 5 1 2 1 3 1 4 0h4 8 4l-1 1c-1 0-2-1-2 0h-1-3-8s-2 0-2 1h-3c-2-1-4-1-6-3h1z" class="T"></path><path d="M423 540c0-1 0-1-1-2 0-1 1-2 1-2 1-1 1-1 0-2v-2l1-1h2c0 1 1 2 1 2-1 1-1 1-1 2s-1 1-1 1v1h0l-1 1h1c1 0 1 0 2 1h-1-1c-1 0-1 1-2 1z" class="g"></path><path d="M425 532h1c0 1 0 2-1 3-1-1 0-1-1-1l1-2zm-117 32l7 2h-2-1 0-1c-1 0-1 0-2-1v1c1 1 1 1 2 1s3 1 4 2h0-4c-2-1-4 0-5-1h2v-1c-2 0-4-1-6-1 1-1 3 0 4 0l1-1h1v-1z" class="Y"></path><path d="M305 492c1 3 2 6 1 9h1v3h-1v-3c-1 1-1 4-1 6 0-1 0-2-1-3 0-1 0-1-1-1v-7 1c1-1 1-3 2-4v-1h0z" class="j"></path><path d="M374 543h0 0c-3 5-9 11-14 13h-1c2-1 3-3 5-5 3-3 7-5 10-8z" class="T"></path><path d="M344 519c1 1 1 2 2 2l1 1h0v2c0 3-1 6-3 9 0-1 0-1-1-1v-2c2-4 2-7 1-11z" class="N"></path><path d="M341 510l2 1 1 2 1 1c1-1 1-1 1-2 0 1 1 3 1 5s1 3 0 5h0l-1-1c-1 0-1-1-2-2 0-4-2-6-3-9z" class="M"></path><path d="M346 512c0 1 1 3 1 5v3h-1c0-2-1-5-2-7h0l1 1c1-1 1-1 1-2z" class="O"></path><path d="M380 488c2 0 3 2 5 3 1 2 2 5 3 7 0 1 1 2 1 4h-1c0-2-1-3-2-5 0-2-1-3-2-4v-1h-1v1c1 1 1 1 1 2v2l-1-1h-1l-1-1c0-1-1-2-1-3l1 1v-3l-1-2z" class="T"></path><path d="M381 490l2 6h-1l-1-1c0-1-1-2-1-3l1 1v-3z" class="G"></path><path d="M363 520c1 0 2 0 3-1-1 2-1 4-1 6-1 2-4 6-6 7h0-1l-2 1c0-2 3-4 4-5 2-3 3-5 3-8z" class="Y"></path><path d="M409 490l1 1v-2-3c1 1 2 4 2 5h0c-1 3 0 7 0 10s-1 5-2 8v-5c-1-2 0-5 0-7 0-3-1-5-1-7z" class="g"></path><path d="M361 460c4-2 9-2 13-2h3l2 2h-2 0l1 2h-1-1l-4-1c-2 0-3-1-5-1h-6z" class="T"></path><path d="M374 458h3l2 2h-2 0l1 2h-1-1 0c0-1-3-2-4-3h0l2-1z" class="a"></path><path d="M356 533l2-1h1c-4 5-11 11-17 13l1-2c2-1 3-1 4-2v-2h1c0-1 1-1 1-1 2-2 4-3 6-4 1 0 1-1 1-1z" class="S"></path><path d="M375 497c1 0 2 1 3 2v1c1 1 1 2 1 3l1 1 1-1v1c0 2 0 3 1 5h0l-2-1-1-1h-1v-2h0v1c-1 1-1 2-1 3-1-2-2-5-2-8v-1c1-1 0-2 0-3z" class="a"></path><path d="M346 512c0-3-2-6-3-9 0 1 0 1 1 1l2 3 3 2v-1h0c1 1 1 2 1 4l-1 1 1 2h-1 0l-1 1h1v1l-1 1h0v4s0 1-1 2v-2c1-2 0-3 0-5s-1-4-1-5z" class="d"></path><path d="M346 507l3 2v-1h0c1 1 1 2 1 4l-1 1c0-2-2-4-3-6z" class="h"></path><path d="M425 496c0 2 1 3 1 5h-1c-1-3-2-7-4-11l-2-6c-1-1-1-1-1-2-1-2-2-4-3-5 1 0 1 1 2 2 0-1 1-1 1-1 1-1 1 0 2 0v1l-1-1c-1 1-1 1-1 2l1 1 2 4c1 1 1 2 2 4s2 5 2 7zm-89 24h-1v1s1 1 2 1h0c1 1 2 2 2 3h-1-1v1h0l-1 1v-1c-1 0-1 0-1 1h-1c-1 1-1 0-3-1h0 1 0 2c-1-1-2-2-2-3l-1-1c-1-2-2-5-2-7h0c-1 0-1 1-1 2-1 1-1 1-2 1v-1h0v-1h0c0-2 0-3 1-4v4c1-1 1-2 2-3 0 2 1 5 2 6h0v-3c1 2 2 3 3 5 0 2 2 3 3 4h1c0-2-4-3-3-5h1 0z" class="i"></path><path d="M395 416c1 0 3 0 4 1-1 0-2 0-3 1h4l-8 1-6 2c-3-1-6 0-9-1v-1h4l6-2 8-1z" class="N"></path><path d="M381 419h7 4l-6 2c-3-1-6 0-9-1v-1h4z" class="a"></path><path d="M349 508c1 0 3 3 3 4l1 2c0 1 1 1 1 2h1v-3c1 1 1 2 1 3h-1c-2 3-2 7-3 9 0-2 0-5-1-7v2c-1 0-1 0-1 1h-1l-1-1 1-1c1 0 1-1 1-1h0c-1-1-1-2-1-3h1l-1-2 1-1c0-2 0-3-1-4z" class="T"></path><path d="M349 508c1 0 3 3 3 4h0v2l-1 1v2h-1v-2l-1-2 1-1c0-2 0-3-1-4z" class="b"></path><path d="M406 430h1 2c0 1 1 1 2 1v-1-1h1 0-1l2 2h1l1 1c1 0 1 0 2 1v1c-1 0-5-1-7-1-3 0-7 0-10-1-2 0-4-1-6-1 1-1 3-1 4-1h8zm-21 72l1 3c1 4 0 8 0 11v4-5h-1c-1-2-1-2-1-4h-1l-1 1v1 4c1 1 1 2 0 3h0c0-2 0-5-1-7 0-2-1-3-1-5l2 1h0c-1-2-1-3-1-5l1 1c0 1 0 1 1 2l1-1v-1c1-1 1-2 1-3z" class="d"></path><path d="M385 502l1 3c0 2 0 5-1 7v1l-1-4h-2 0 0c-1-2-1-3-1-5l1 1c0 1 0 1 1 2l1-1v-1c1-1 1-2 1-3z" class="X"></path><path d="M419 481h2c0-1 0-1 1-1 1 2 2 4 4 6 0 0 1 0 1 1v1h1v-1c0 1 2 4 1 5v1-1c-1 1-1 1-2 0l-1-1h-1c0-1-1-2-2-2-1-2-1-3-2-4l-2-4z" class="d"></path><path d="M426 486s1 0 1 1v1h1v-1c0 1 2 4 1 5v1-1c-1-1-2-3-3-5v-1z" class="T"></path><path d="M421 485h1c2 1 3 4 4 6h-1c0-1-1-2-2-2-1-2-1-3-2-4z" class="b"></path><path d="M392 426c1 0 1 1 2 1h1l1 1h0c2-1 7-1 9 0h-7c1 1 5 0 6 1h2v1h-8c-1 0-3 0-4 1h-1c-4-1-7-1-11-1 1 0 7-1 7-2 1 0 0 0 1-1h2v-1z" class="g"></path><defs><linearGradient id="AA" x1="423.162" y1="504.976" x2="431.414" y2="495.816" xlink:href="#B"><stop offset="0" stop-color="#323034"></stop><stop offset="1" stop-color="#474845"></stop></linearGradient></defs><path fill="url(#AA)" d="M423 489c1 0 2 1 2 2h1l1 1-1 1c1 2 3 4 3 7h0c0 2 1 7 2 10v5 2c0 2 0 3 1 5h0-1c0-1 0-1-1-2v-4c-1-7-2-13-5-20 0-2-1-5-2-7z"></path><defs><linearGradient id="AB" x1="315.626" y1="503.977" x2="313.156" y2="503.936" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#444445"></stop></linearGradient></defs><path fill="url(#AB)" d="M314 493l1-3c0 1 1 1 1 2h0c-2 8-2 19 1 26h1v2h0v-1h-1l-2-2h-1c-2-7-2-17 0-24z"></path><path d="M312 478c2-2 1-3 2-4h1 1l-1 1c-1 2-1 5-1 8-1 3-1 5-2 7 0 2 0 4-1 6s-1 3-1 5c0 1 0 2-1 3h0c-1-3-1-8 0-11v-4h1v4l1-1c0-3 0-5 1-7v-7z" class="Y"></path><path d="M423 540c1 0 1-1 2-1h1c1 2 2 3 2 4v4 1c-1 2-1 4-2 5-1-1 0-3-2-4l-1-1h-1l1-1c-1-3-1-4 0-7z" class="E"></path><path d="M401 500v1c0 1 0 2 1 2v1l1-1h0 0l1 2c0 2-1 5-1 7h0l-1 3-1 2c-1-1-2-3-3-4l2-8h0v-4l1-1z" class="b"></path><path d="M400 505v-4c1 0 1 2 1 3 0 0 1 0 1 1 0 2-2 7 0 8 0-1 0-1 1-1l-1 3c-2-2-1-4-1-7-1-1-1-2-1-3z" class="a"></path><path d="M400 505h0c0 1 0 2 1 3 0 3-1 5 1 7l-1 2c-1-1-2-3-3-4l2-8z" class="W"></path><path d="M341 517c1 2 0 3 0 6 0 0 0 1-1 2v1c-1 3-1 6-3 7h-2-1l-1-1v-2h0 0c-1-1-1-1-1-2h2 1 1 0l-1-1h1 1c2-1 2-1 3-3v-1h-1c-1 0-1-1-2-2l2 2h1c0-2-1-2-2-2l1-1-1-1s-1-1-1-2l1 1 1-1h0 1 1 0z" class="j"></path><path d="M339 517h0l1 3h-1l-1-1s-1-1-1-2l1 1 1-1z" class="b"></path><path d="M434 532h4c1 1 2 2 2 3h-1v1l-2 2h-2v3h-3v-1h0c-2 1-2 3-3 5h0l-1-2c0-1-1-2-2-4h1c0 1 1 2 2 2h1c1-1 0-2 1-3 0-2 2-4 2-5l1-1z" class="j"></path><path d="M435 538v3h-3v-1c0-1 2-2 3-2z" class="U"></path><path d="M434 532h4c1 1 2 2 2 3h-1v1l-2 2h-2v-1c-1-2-1-2 0-4l-1-1z" class="G"></path><path d="M435 537c0-1 1-1 2-2 1 0 1 1 2 1l-2 2h-2v-1z" class="M"></path><path d="M348 438l5-3 1-1c2-1 4-1 6-2l20-6h0-1v1h1c-5 1-12 3-17 5-1 1-2 2-4 3l-6 4c-2 1-3 2-5 2 0 0-1 0-1-1h0l1-2z" class="g"></path><defs><linearGradient id="AC" x1="378.998" y1="484.802" x2="377.855" y2="477.953" xlink:href="#B"><stop offset="0" stop-color="#504e51"></stop><stop offset="1" stop-color="#696a69"></stop></linearGradient></defs><path fill="url(#AC)" d="M367 474h1l5 3c4 1 7 4 10 7l2 3 2 2 1 3c1 1 1 2 1 3v-1c-2-1-2-4-3-5s-1-2-2-2h-1 1v1 1l3 5c0 1 0 1 1 1v1 2c-1-2-2-5-3-7-2-1-3-3-5-3-1-1-2-2-3-4 1 0 2 1 2 2l1-1h0c-3-5-8-8-13-10v-1z"></path><path d="M380 485c2 1 4 4 5 6-2-1-3-3-5-3-1-1-2-2-3-4 1 0 2 1 2 2l1-1h0z" class="M"></path><path d="M421 516v-8s-1 0 0-1v-4c0-5-2-10-3-15 0-1-1-2-1-3 0 0 0 1 1 2 1 4 4 8 5 12l3 8-2-1c0-1 0-1-1-2v2 7c0 1-1 3-1 5 0 1 0 2-1 3h0v-5z" class="f"></path><path d="M422 507l-1-6h1c0 1 0 2 1 3h-1v3z" class="b"></path><path d="M422 507v-3h1v2 7c0 1-1 3-1 5 0 1 0 2-1 3h0v-5c1-2 1-6 1-9z" class="X"></path><path d="M429 492v1c0 1 1 1 1 2 1 3 2 6 3 10 0 1 0 5-1 6 0 1 0 1 1 2 0-1 0-1 1-2 0 0 0 1 1 1l-1 1c0 2 1 6-1 8v-2l-2-2v-2-5c-1-3-2-8-2-10h0c0-3-2-5-3-7l1-1c1 1 1 1 2 0z" class="a"></path><path d="M431 510l1 5v1h1v1h1v-4c0 2 1 6-1 8v-2l-2-2v-2-5z" class="W"></path><path d="M431 515c1 2 2 2 2 4l-2-2v-2z" class="f"></path><path d="M419 467c2 1 4 4 6 5l1 1h0l1 2 4 6v-1c1-1 1-1 2 0l1 1c0-2-2-2-2-4-1-1-2-1-2-2h0v2l-1-1v-1h-1c0-1-1-2-2-3-2-1-3-3-4-5h1l9 9c2 2 3 4 4 6l1 2-5-4c1 1 2 3 2 5h0v2c0 1 0 1-1 2-1-1-1-2-2-4h0c0-1 0-1-1-2v-1l-1-1v1c-1-1-2-2-2-3h0c1 1 2 2 3 2l-3-4c-1-2-3-4-4-6-2-1-4-2-4-4z" class="i"></path><path d="M427 477h1c0 1 1 1 1 2l5 8c0 1 0 1-1 2-1-1-1-2-2-4h0c0-1 0-1-1-2v-1l-1-1v1c-1-1-2-2-2-3h0c1 1 2 2 3 2l-3-4z" class="W"></path><path d="M308 521h0c-2-1-2-2-3-4-1-1-2-3-2-5 0-7-2-13-1-19l1-1v4 7c1 0 1 0 1 1 1 1 1 2 1 3 0 6 3 13 7 18 1 2 3 4 4 6-3-2-6-6-8-10z" class="T"></path><path d="M423 506v-2c1 1 1 1 1 2l2 1c1 7 2 15-2 22h-1-1 0c1-1 1-1 0-2h0v-2c1-1 1-2 1-3s0-3-1-4c0-2 1-4 1-5v-7z" class="W"></path><path d="M423 506v-2c1 1 1 1 1 2 2 4 0 12-1 16 0-1 0-3-1-4 0-2 1-4 1-5v-7z" class="g"></path><path d="M327 498c0-3 2-7 3-10 0 2 0 6-1 7 0 2 0 3 1 4v8c0 1 1 3 2 5h0l-2 2 1 2h0v3h0c-1-1-2-4-2-6-1 1-1 2-2 3v-4-2-1-2h0v-1h0v-1l-1-1h0l-1 1c0-1 1-2 1-3s0-2 1-4z" class="d"></path><path d="M330 507c0 1 1 3 2 5h0l-2 2v-7z" class="X"></path><path d="M327 498c0-3 2-7 3-10 0 2 0 6-1 7l-2 4v2c-1 1 0 3 0 5h0v-1l-1-1h0l-1 1c0-1 1-2 1-3s0-2 1-4z" class="T"></path><defs><linearGradient id="AD" x1="368.849" y1="488.785" x2="360.184" y2="471.337" xlink:href="#B"><stop offset="0" stop-color="#8b898b"></stop><stop offset="1" stop-color="#aeadac"></stop></linearGradient></defs><path fill="url(#AD)" d="M357 474c3 0 7 0 10 1 5 2 10 5 13 10h0l-1 1c0-1-1-2-2-2s-2-1-3-2c-2-2-5-3-8-4h0c-2-1-8-2-10-1-1 0-1 0-2 1v-1h-4 0l-1-1c2 0 6-1 8-2z"></path><path d="M347 465v1c2 0 3-1 4-2 3 0 6-1 9-1-2 1-3 1-5 1-2 1-5 2-6 3-2 2-4 2-6 4h0c-2 1-3 3-5 4-1 2-3 4-5 6h0c-1 1-1 1-1 2-1 1-1 1-1 2l-1 3c-1 3-3 7-3 10l-1-2c0-3 2-8 3-10 3-7 6-11 11-16 2-2 5-4 7-5zm-1 91h1c2-1 3-1 5-1l-2 1c-2 1-4 2-6 2l-9 4c3 0 5-1 9-1-3 1-5 2-7 3h-6c-4 1-8 2-13 2 1 0 2-1 3-1s2-1 4-1c1 0 1 0 1-1 0 0-1 0-2 1l-1-1c4-1 7-2 11-3v-1c2-2 6-1 8-2 1 0 3 0 4-1z" class="Y"></path><path d="M419 563h1c1-2 1-4 1-5-1-4-1-7 0-10h1 1l1 1c2 1 1 3 2 4 0 4-1 8-3 11l-3 2c0 1-1 2-2 3l-1-1h1c0-1 0-2-1-3l2-2z" class="R"></path><path d="M435 507c1-1 1-2 1-3l1-1v4h0c0 4 1 8 1 11h1v3h1c0 1 0 1-1 2h-1v2 1h0l-1 1v2h1c1 0 1 1 1 1l1 1h-1c-1-1-3-1-3-1v-4c-1-1-1-1-1-2v-2h-1c0 1-1 3-1 3-1 1-1-1-2-1h-1v-1c1-1 1 0 2-1h0c-1-2-1-3-1-5l2 2v2c2-2 1-6 1-8l1-1v-1-4z" class="i"></path><path d="M437 507h0c0 4 1 8 1 11h-1c-1-2-1-5-1-7s0-3 1-4z" class="T"></path><path d="M431 485c1 2 1 3 2 4 1 3 1 7 3 9v1 2c1 0 1 0 1-1l1 1c0 1 0 2-1 2h0l-1 1c0 1 0 2-1 3v4 1c-1 0-1-1-1-1-1 1-1 1-1 2-1-1-1-1-1-2 1-1 1-5 1-6-1-4-2-7-3-10 0-1-1-1-1-2v-1c1-1-1-4-1-5h1c1 0 2 1 3 2-1-1-1-2-1-4z" class="W"></path><path d="M431 485c1 2 1 3 2 4 1 3 1 7 3 9v1 2c1 0 1 0 1-1l1 1c0 1 0 2-1 2h0l-1 1c0 1 0 2-1 3l-1-1v-2c1-1 1-3 1-4-1-1 0-2-1-3 0-2-1-4-2-6v-2c-1-1-1-2-1-4z" class="f"></path><defs><linearGradient id="AE" x1="339.594" y1="534.601" x2="336.669" y2="547.402" xlink:href="#B"><stop offset="0" stop-color="#a7a6a7"></stop><stop offset="1" stop-color="#d3d3d4"></stop></linearGradient></defs><path fill="url(#AE)" d="M347 535h0c1 1 1 1 3 0v1l-8 6 6-3h-1v2c-1 1-2 1-4 2l-1 2-9 3 1-1-1-1c-2 0-3 0-4-1v-1l-3-1v-1h0l-2-1c2 0 3-1 4 0l-2 2c7-1 15-3 21-8z"></path><path d="M427 479h-1c0-1-1-1-1-1-1 0 0 0-1-1h0v1l1 1c-1 0-1 0-1-1-1 0-1-1-1-1v-1h0c-1-1-2-3-4-3h0c-1-1-3-4-4-4-2-1-3-3-5-4l-9-6c-2-1-5-1-6-3h0l5 2c1 0 2-1 2-1l6 3c4 2 8 4 11 7 0 2 2 3 4 4 1 2 3 4 4 6l3 4c-1 0-2-1-3-2h0zm-112-12c0 1 1 1 0 2 0 1-1 1-1 2v1c-1 0 0 0-1 1h0v2 1c-1 1-1 1-1 2v7c-1 2-1 4-1 7l-1 1v-4h-1v4c-1 3-1 8 0 11l-1 1c0-1 1-3 0-4 0 1 0 2-1 2v-2h0-1c1-3 0-6-1-9v-10c0-1 0-3 1-4l1 1 2-3h0c0-1 0-1 1-2h0l2-2c0-2 1-4 3-5z" class="k"></path><path d="M307 489c0 4-1 8 0 12h0-1c1-3 0-6-1-9h0c2 0 2-2 2-3z" class="f"></path><path d="M306 478l1 1 2-3h0c0-1 0-1 1-2-1 5-3 10-3 15 0 1 0 3-2 3h0v-10c0-1 0-3 1-4z" class="b"></path><defs><linearGradient id="AF" x1="442.279" y1="453.932" x2="446.705" y2="458.495" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#AF)" d="M445 450h1c2 1 4 2 6 4l1 1 3 4-1 1v-1l-1 1c-1 0-2-1-3-1 0 0-1 1-2 1v3c0 2 3 4 4 6 0 2 1 4 1 6-1-3-2-5-4-7-1-2-4-5-6-5h0c-2-1-2-1-4-1h0c3 2 5 5 7 8-3-2-5-6-8-7h-2-3c2-1 4-1 6-3 3-2 4-6 5-10z"></path><path d="M453 455l3 4-1 1v-1l-1 1c-1 0-2-1-3-1 0 0-1 1-2 1v1c0-2 0-3 1-4h2c0-1 0-2 1-2z" class="h"></path><path d="M387 489v-1-1-3-1c1 2 2 3 3 4 1 2 2 3 2 5 1 1 2 4 2 6 0 1 0 2-1 4l1 1c1-1 1-3 1-4h0c1-1 1-2 1-3s0-1 1-2v1h0v2h1v1c0 1 0 2 1 2 0 2 0 3 1 5h0l-2 8c0 2 0 4-1 6 0 1 0 1-1 2v-3c1-1 1-2 1-3h0c-1 0-1 0-2-1v-2-1l-1-2h0-1v2c-1-1-1-4-1-5-1-1 0-3-1-5s-2-4-2-6c0-1 0-2-1-3l-1-3z" class="j"></path><path d="M395 499l1 1c0 2-1 5-1 8h-1-1v-1-5l1 1c1-1 1-3 1-4z" class="f"></path><path d="M395 499h0c1-1 1-2 1-3s0-1 1-2v1h0v2 6c-1 3 0 5-1 8l-1-1v-2c0-3 1-6 1-8l-1-1z" class="d"></path><path d="M387 489v-1-1-3-1c1 2 2 3 3 4 1 2 2 3 2 5 1 1 2 4 2 6-1 0-1 1-1 1v2h-1c0-2 0-3-1-5v1l-2-2c0-1 0-2-1-3l-1-3z" class="W"></path><path d="M404 450h-1 0l6 3c1 0 1 1 2 1 1 1 1 1 2 1l1 1c1 1 2 1 3 2h1s0 1 1 1h0c1 0 1 1 1 1 1 0 1 1 1 1 2 1 3 2 4 3 4 3 7 7 10 11 1 1 2 2 2 3s0 1 1 2c1 2 3 5 4 7l-1 1h0v-2l-1-1h0c-1 0 0 0-1 1h0 0c0-1 0-2-1-2h0v-1c-1-1-2-3-3-5-1-1-1-2-2-4-5-5-9-8-14-12 0 0 1 1 1 2v1h0l-10-8h1 0v-1c-2-2-6-3-8-4-2 0-3-1-4-2-2-1-4-1-6-2h6v-1h-7c2-1 5-1 7 0h2 1 1c2 1 4 2 7 3 1 1 3 2 5 3h0l-1 1v-1c-1 0-2-1-3-1-2-2-4-2-7-3v1z" class="g"></path><defs><linearGradient id="AG" x1="396.384" y1="489.156" x2="409.075" y2="473.474" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#AG)" d="M387 460c10 2 17 7 23 17 3 6 6 14 5 21-1 1-1 1 0 2 0 2-1 5-2 6 0-3 0-8-1-12v-3h0c0-1-1-4-2-5v3 2l-1-1c0-2-1-4-2-7h0c0-2 0-4-1-5-3-8-9-12-16-16-1-1-3-1-3-2z"></path><defs><linearGradient id="AH" x1="409.055" y1="432.343" x2="405.805" y2="420.986" xlink:href="#B"><stop offset="0" stop-color="#191519"></stop><stop offset="1" stop-color="#323431"></stop></linearGradient></defs><path fill="url(#AH)" d="M405 423c4 0 8 1 12 1 3 0 8 0 11 1l-3 2h0l-3 2c-1 0-2 1-3 0-1 0-2 1-3 0h-2l-3-1h-3-2-1c-2-1-7-1-9 0h0l-1-1h-1c-1 0-1-1-2-1-4 0-8 2-12 3v-1c8-2 16-5 25-5z"></path><path d="M406 425h5v1h-1c-2 0-8 1-9 0 1-1 3-1 5-1z" class="i"></path><path d="M405 423c4 0 8 1 12 1 3 0 8 0 11 1l-3 2h0l-1-1h-3l-1 1h-3c-1-1-1-1-2-1h-5 1v-1h-5-1-5c1-1 2-1 3-1h-1l3-1zm-93 128c1 0 2 0 2-1 2 0 4 0 6 1h1 0 1s0 1 1 2h2c1 0 2 1 2 1 0 1 1 1 1 1l-2 1h3-1l2-1h-1c1-1 2 0 4-1h-1 2 0 0 1 1 1s1-1 1 0h2c-1 1-1 1-2 1v1h0 2 0l1-1v1l-1 1h-1 1s1-1 1 0h1l1-1s1 1 1 0h2 0c-1 1-3 1-4 1-2 1-6 0-8 2l-11 1c-2 1-5 1-7 0l-1-1v-1h1v-2h1v-2h1l-1-1h-3c-2-2-3-2-5-2h3z" class="g"></path><path d="M312 551c1 0 2 0 2-1 2 0 4 0 6 1h1 0l2 2c1 1 3 1 4 2-3 0-6-1-9-1l-1-1h-3c-2-2-3-2-5-2h3z" class="W"></path><defs><linearGradient id="AI" x1="326.528" y1="553.58" x2="320.956" y2="561.501" xlink:href="#B"><stop offset="0" stop-color="#313330"></stop><stop offset="1" stop-color="#474349"></stop></linearGradient></defs><path fill="url(#AI)" d="M317 556c2-1 7-1 9 1 2 0 6-1 7 0v1l-9 1h-4c1 2 1 1 3 1-2 1-5 1-7 0l-1-1v-1h1v-2h1z"></path><path d="M315 559h9-4c1 2 1 1 3 1-2 1-5 1-7 0l-1-1z" class="Y"></path><path d="M377 458c2 0 8 0 10 2 0 1 2 1 3 2 7 4 13 8 16 16 1 1 1 3 1 5h0l-1 1h0c1 2 2 8 1 9l-1-6h0v-2h-1l1 1v1c1 1 0 3 1 5h0c-1-1-1-2-1-3-1-1-1-2-2-3v-3 2l1 3v1 2c1 2 1 4 0 6h0c0-7-2-13-5-20-1-1-2-3-2-4-1 0-1-1-1-1v-2l-3-3c-2-1-4-2-7-3l-1-1-2-1-5-2-2-2z" class="j"></path><path d="M398 473h1c1 0 2 1 2 2l1-1c2 3 3 7 4 10-3-2-2-5-5-7h-1c-1-1-2-3-2-4z" class="d"></path><path d="M384 462c3 0 4 1 7 2 4 1 9 6 11 10l-1 1c0-1-1-2-2-2h-1c-1 0-1-1-1-1v-2l-3-3c-2-1-4-2-7-3l-1-1-2-1z" class="a"></path><defs><linearGradient id="AJ" x1="359.956" y1="426.809" x2="362.088" y2="431.353" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#AJ)" d="M339 443l1-2c2-3 5-5 7-7 9-6 19-12 30-14 3 1 6 0 9 1-13 3-27 8-39 17h1l-1 2h0c0 1 1 1 1 1-2 1-3 2-4 3h-1v-1c-1 1-3 3-4 3l-1-1 1-2z"></path><path d="M342 442c2-2 4-3 5-4h1l-1 2h0c-1 1-3 2-4 2h-1z" class="k"></path><path d="M347 440c0 1 1 1 1 1-2 1-3 2-4 3h-1v-1c-1 1-3 3-4 3l-1-1 1-2c1 0 1 0 2-1h1 1c1 0 3-1 4-2z" class="W"></path><path d="M358 507v3l2-1v3l-1 4h1v-1h1 0l1-1h1v1h1l-1 5c0 3-1 5-3 8-1 1-4 3-4 5 0 0 0 1-1 1-2 1-4 2-6 4 0 0-1 0-1 1l-6 3 8-6v-1c-2 1-2 1-3 0h0c5-4 8-10 10-17v-6l1-5z" class="J"></path><path d="M361 515l1-1h1v1h1l-1 5c0 3-1 5-3 8h-1v-1l-1 1-1 1c-1 1-2 3-3 3h-1c3-3 5-5 6-9v-1c1-1 1-1 1-2l-1-1v-3h1v-1h1 0z" class="O"></path><path d="M361 515h0l-1 5-1-1v-3h1v-1h1zm2 0h1l-1 5c0 3-1 5-3 8h-1v-1c1-4 3-8 4-12z" class="K"></path><path d="M323 541h1l2 1h0v1l3 1v1c1 1 2 1 4 1l1 1-1 1-4 1h-4-8-4c-1 1-2 1-4 0-2 0-3 0-5-1h0c-1-1-3-2-4-3l-1-1h3c-1 0-2-1-2-2h1c2 1 5 2 7 1h1c1 1 1 1 2 1h1l-1 1c3 1 8 0 11 0-2-1-4-1-6-2 0 0 1 0 1-1l6-1z" class="I"></path><path d="M300 542h1c2 1 5 2 7 1h1c1 1 1 1 2 1h1l-1 1c-1 0-2 0-2-1h-3c1 1 3 2 4 2 4 1 8 2 12 2 1 0 2 0 3 1h-8-4c-1 1-2 1-4 0-2 0-3 0-5-1h0c-1-1-3-2-4-3l-1-1h3c-1 0-2-1-2-2z" class="n"></path><path d="M299 544h3c1 1 3 2 4 2 3 1 7 3 10 3h1-4c-1 1-2 1-4 0-2 0-3 0-5-1h0c-1-1-3-2-4-3l-1-1z" class="M"></path><path d="M286 525c0 1 1 1 1 2 2 0 6 5 7 6 3 2 5 4 8 5l1 1h2 1l1-1v1h4c0 1 0 1 2 1h0 3c1 0 2 1 3 1 1 1 3 0 4 0l-6 1c0 1-1 1-1 1 2 1 4 1 6 2-3 0-8 1-11 0l1-1h-1c-1 0-1 0-2-1h-1c-2 1-5 0-7-1h-1l-2-1c-1-1-1-2-2-3v1 1 1c-1 0-1 0-2-1l-1-1c-3-3-5-6-7-10l-1-2 1-1v-1z" class="H"></path><path d="M307 538v1h4c0 1 0 1 2 1h0 3c1 0 2 1 3 1 1 1 3 0 4 0l-6 1c-2 1-4 1-6 1-2-1-4-1-6-2v-2h1l1-1z" class="b"></path><path d="M305 539h1 0c1 1 3 1 4 1s1 1 2 2c-1 0-1 0-1 1-2-1-4-1-6-2v-2z" class="e"></path><path d="M287 527c2 0 6 5 7 6 3 2 5 4 8 5l1 1h2v2c-2-2-5-3-7-4 3 4 5 5 10 6-2 1-5 0-7-1h-1l-2-1c-1-1-1-2-2-3v1 1 1c-1 0-1 0-2-1 1 0 1 0 1-1 0-4-5-9-8-12z" class="G"></path><path d="M286 525c0 1 1 1 1 2 3 3 8 8 8 12 0 1 0 1-1 1l-1-1c-3-3-5-6-7-10l-1-2 1-1v-1z" class="E"></path><path d="M384 440l1-1c-2-1-5 0-7 0-1 0-2 1-2 0 3-1 6-1 8-1 8-1 15 0 22 2h5c0 1 1 1 2 1 0 0 1 0 1 1-1 0-1 0-1 1 1 0 2 1 3 1v-1l1-1v5h-1c-1-1-1-2-2-2h-1c-3-2-5-1-8-2h-1-4-14c-2 0-5 0-7 1-2 0-10 1-11 1h-1l11-3h0-3 0-1c2-2 7-2 10-2z" class="Y"></path><path d="M400 443l-2-1h2 2c-1-1-2 0-4 0 3-1 4-1 6-1v1 1h-4z" class="T"></path><path d="M378 442h0-3 0-1c2-2 7-2 10-2-1 0-2 0-3 1h0 5l1 1h-9z" class="j"></path><path d="M336 497v2c1 1 1 2 1 3l3 6-1-1v1l2 9h0-1-1 0l-1 1-1-1c0 1 1 2 1 2l1 1-1 1c1 0 2 0 2 2h-1l-2-2-1-1h0-1c-1 2 3 3 3 5h-1c-1-1-3-2-3-4-1-2-2-3-3-5h0l-1-2 2-2h0c-1-2-2-4-2-5v-8l1-1v4h1l1 1v-4h1v-1l1-1h1z" class="a"></path><path d="M331 498v4h1c0 2 0 5 1 6-1 2 0 2 0 4l3 6c-2-1-2-2-3-3 0-1 0 0-1-1v-2h0c-1-2-2-4-2-5v-8l1-1z" class="N"></path><path d="M332 502l1 1 6 14-1 1-1-1-2-2c-1-3-2-4-2-7-1-1-1-4-1-6z" class="M"></path><defs><linearGradient id="AK" x1="336.992" y1="511.976" x2="333.687" y2="498.849" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#5c5c5d"></stop></linearGradient></defs><path fill="url(#AK)" d="M336 497v2c1 1 1 2 1 3l3 6-1-1v1l2 9h0-1-1 0l-6-14v-4h1v-1l1-1h1z"></path><path d="M336 497v2c1 1 1 2 1 3l3 6-1-1s-1-1-2-1v-1h-1v-1c-1-2-1-4-1-7h1z" class="a"></path><path d="M294 540c1 1 1 1 2 1v-1-1-1c1 1 1 2 2 3l2 1c0 1 1 2 2 2h-3l1 1c1 1 3 2 4 3h0-1c2 2 4 2 6 3 2 0 3 0 5 2h3l1 1h-1v2h-1v2h-1-2l-1-1c-2 2-5 0-7 0-1 0-3-1-4-2-1 0-2-1-3-2l-2-2 1-1-2-2c1-1 0-1 0-1v-1c0-1-2-2-2-3-1-1-2-2-2-3l1 1c0-1 0-1 1-2l1 1z" class="b"></path><path d="M313 556v-1c1-1 2 0 4-1v2h-1-3z" class="Y"></path><path d="M298 541l2 1c0 1 1 2 2 2h-3c-1-1-1-2-2-2l1-1z" class="Z"></path><path d="M291 540l1 1c3 3 6 7 10 8l3 2c-1 1-1 1-2 1h0-2l-4-2-2-2c1-1 0-1 0-1v-1c0-1-2-2-2-3-1-1-2-2-2-3z" class="J"></path><path d="M296 551l1-1 4 2h2 0c1 0 1 0 2-1 1 1 5 2 6 3h-2c1 1 3 1 4 2h3v2h-1-2l-1-1c-2 2-5 0-7 0-1 0-3-1-4-2-1 0-2-1-3-2l-2-2z" class="N"></path><path d="M296 551l1-1 4 2c2 2 4 3 7 3 1 1 2 1 3 2-1 0-2 0-3-1-1 0-3 0-4-1-2-1-3-2-6-2l-2-2z" class="a"></path><path d="M305 551c1 1 5 2 6 3h-2c1 1 3 1 4 2v1h-2c-1-1-2-1-3-2-3 0-5-1-7-3h2 0c1 0 1 0 2-1z" class="O"></path><path d="M400 443h4 1c3 1 5 0 8 2h1c1 0 1 1 2 2h1 0l1 4c2 4 4 9 9 11 2 1 4 1 7 1h3l-2 1c-2 0-3 0-4 1l-2-1c-1-1-3-1-4-2h-1c-1-1-4-5-5-7h0-1c-1 0-1 0-2-1h0l-1 1c1 0 0 0 0 1-3-3-7-5-11-6v-1c3 1 5 1 7 3 1 0 2 1 3 1v1l1-1h0c-2-1-4-2-5-3-3-1-5-2-7-3h-1 1c-1 0-1-1-1-1h-1-7 6v-1c-8 0-18-1-26 1h-3 0l8-2h0c2-1 5-1 7-1h14z" class="f"></path><path d="M418 451v2h-1l-2-1c0-1 0-1-1-1s-2-1-3-2c-2-1-6-1-8-3h0c1 0 2 0 3 1l7 2c1 0 2 0 3 1v-1h0l1-1v-1l1 4z" class="j"></path><path d="M400 443h4 1c3 1 5 0 8 2h1c1 0 1 1 2 2h1 0v1l-1 1c-3-2-5-2-8-3s-5-2-8-2h-11-10 0c2-1 5-1 7-1h14z" class="k"></path><path d="M387 464c3 1 5 2 7 3l3 3v2s0 1 1 1c0 1 1 3 2 4 3 7 5 13 5 20l-1 8-1-2h0 0l-1 1v-1c-1 0-1-1-1-2v-1c-1-2-1-3-1-5s-1-4-2-7l-1-3s0-1-1-2l-2-4v-2c1-2-2-4-2-7-1 0-2-1-2-2-1-1-1-2-2-3-1 0-1 0-1-1z" class="N"></path><path d="M387 464c3 1 5 2 7 3v1c0 2 1 2 2 4h0l-3-2v1l-1-1c-1 0-2-1-2-2-1-1-1-2-2-3-1 0-1 0-1-1z" class="M"></path><defs><linearGradient id="AL" x1="397.575" y1="488.02" x2="397.422" y2="473.266" xlink:href="#B"><stop offset="0" stop-color="#727272"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#AL)" d="M392 470l1 1c2 3 4 5 5 9 2 2 4 4 4 7 0 1 0 1-1 2 1 3 2 5 2 9h-1v-1c0-3-1-9-4-11l-1-1s0-1-1-2l-2-4v-2c1-2-2-4-2-7z"></path><path d="M284 547c1 1 2 2 3 2l6 6 1-1 6 3 2 1v-1l-2-1 1-1c1 1 3 2 4 2 2 0 5 2 7 0l1 1h2v1l1 1c2 1 5 1 7 0l11-1v1c-4 1-7 2-11 3l1 1c1-1 2-1 2-1 0 1 0 1-1 1-2 0-3 1-4 1s-2 1-3 1h-3l-7-2h-2c-2-1-7-2-8-3v-2c-1-1-3-2-5-2-3-3-7-6-9-10z" class="f"></path><path d="M294 554l6 3 2 1c1 1 3 1 3 3-4-1-8-4-12-6l1-1z" class="J"></path><path d="M306 564c1-2 4 0 6 0h7 3 2c1-1 2-1 2-1 0 1 0 1-1 1-2 0-3 1-4 1s-2 1-3 1h-3l-7-2h-2z" class="a"></path><defs><linearGradient id="AM" x1="329.122" y1="555.505" x2="307.594" y2="563.826" xlink:href="#B"><stop offset="0" stop-color="#3f3f3e"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#AM)" d="M301 555c1 1 3 2 4 2 2 0 5 2 7 0l1 1h2v1l1 1c2 1 5 1 7 0l11-1v1c-4 1-7 2-11 3-6-1-13 0-18-2 0-2-2-2-3-3v-1l-2-1 1-1z"></path><path d="M305 557c2 0 5 2 7 0l1 1h2v1l1 1h-1c-3-1-6 0-8-2l-2-1z" class="a"></path><path d="M313 558h2v1l1 1h-1l-2-1v-1z" class="W"></path><defs><linearGradient id="AN" x1="374.864" y1="523.644" x2="360.023" y2="514.502" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#AN)" d="M373 495l2 6c0 3 1 6 2 8 0 2 0 4-1 5 0 3-1 5-1 8h0v1 1l-1 1c0 1 0 1-1 2h0c0 1-1 2-1 3h-1c0 2-1 3-2 4-2 3-3 5-6 7-4 3-7 7-12 9 4-4 11-7 12-13 1-3 3-5 4-7s2-4 2-5v-10h1v-4c0-1 0-2-1-3h3c0-2 0-4 1-5 0-1 0-2-1-3 0-1-1-3-1-4h0 0l2-1z"></path><path d="M373 503c1 1 1 3 1 4l1 3h0c-1 1-1 3-1 4 0 3 0 4-1 6l-1 1c0 1 0 2-1 2h0c-1-2 0-4-1-6v-2-4c0-1 0-2-1-3h3c0-2 0-4 1-5z" class="W"></path><path d="M369 508h3v2h-1v4h-1v-3c0-1 0-2-1-3z" class="b"></path><path d="M373 503c1 1 1 3 1 4 0 3-1 6-1 8-1-2-1-3-1-5v-2c0-2 0-4 1-5z" class="X"></path><path d="M330 437l1 1c0 2-1 6 0 8 1 1 3 2 4 2h2c2 0 5-3 6-4h1v1h0c-1 0-1 1-2 1h0c-1 1-3 3-4 3h-1 0c-1 0-2 0-3 1 0 2-1 2-2 4h0c1-1 3-2 4-3h0c1-1 1-1 2-1v1c-2 1-4 3-5 5-4 4-8 9-11 15v-1l1-3h-1-1l-1 1h0c1-1 1-3 2-5-1 1-1 1-1 2-1 0-1 0-1 1l-1-1v-1-2l1-1h0v3c0-1 1-1 1-2h0c1-1 1-3 2-4 0-1 0-1 1-2v-1l-1 1c0-2 1-2 2-3v-2h1l-1-1c0 1-1 2-2 2h-1c0-1 1-2 2-3v-2l-1 1c0-1 1-1 2-2 1-2 3-4 3-6 1-1 1-2 2-3z" class="e"></path><path d="M324 449h0c2-2 3-5 5-7h0v2c-1 2-4 4-4 6 0 1-1 2-2 2h-1c0-1 1-2 2-3zm0 7c0-1 1-1 1-1 0-1 1-2 2-3h0c0-2 1-3 2-4h1c0 1-1 3-2 3l1 1c0-1 1-2 1-3h2c-1 1-1 0-2 2h0c-1 1-2 2-2 3 0 2-1 2-2 3 0 1-1 3-1 4-2 2-4 3-4 6l-1 1h0c1-1 1-3 2-5-1 1-1 1-1 2-1 0-1 0-1 1l-1-1v-1-2l1-1h0v3c0-1 1-1 1-2h0c1-1 1-3 2-4 0-1 0-1 1-2z" class="i"></path><path d="M291 561c2 0 3 1 5 2s4 3 6 3 4 1 6 1v1h-2-1c-1 0-1 1-2 1-1-1-2-1-3-1h0c-1 1-2 1-3 1s-1 0-2-1v1h-2-2c1 1 2 1 3 1 2 1 8-1 9 1-1 1-1 1-1 2h-10 1 2v1c-3 0-6 1-9 1l2-1h0c-1-1-2-1-2-1h-4v-1-1c-1 0-1 0-2-1h1 0v-1c0-1 0-1-1-2h-2l-1-1c1 0 2 0 3-1h-2 0v-1h1c1 0 2-1 2 0 2 0 3-1 4-1l1-1c1 0 3-1 4-1h1z" class="W"></path><path d="M291 561c2 0 3 1 5 2s4 3 6 3 4 1 6 1v1h-2-1c-1 0-2-1-3-1h-2-2c-2-1-5 0-7-1 0 0 0-1-1-1l-2 1-1-1h-1 0l3-1v-1c-1 0-2 0-3-1 1 0 3-1 4-1h1z" class="N"></path><path d="M291 561c2 0 3 1 5 2v1l-4-1c-1 0-2 0-3 1v-1c-1 0-2 0-3-1 1 0 3-1 4-1h1z" class="M"></path><path d="M285 563l1-1c1 1 2 1 3 1v1l-3 1h0 1l1 1v1h1l3 1h-3-1-1c0 1 1 1 1 1 1 0 1 1 2 1 1 1 2 1 4 1h-2l-1 1 1 1h1 2v1c-3 0-6 1-9 1l2-1h0c-1-1-2-1-2-1h-4v-1-1c-1 0-1 0-2-1h1 0v-1c0-1 0-1-1-2h-2l-1-1c1 0 2 0 3-1h-2 0v-1h1c1 0 2-1 2 0 2 0 3-1 4-1z" class="a"></path><path d="M285 563l1-1c1 1 2 1 3 1v1l-3 1h0-4v1h4l-1 1h-2v1h1v1c0 1-1 1-1 1 1 1 3 0 4 2h-3v1h2-4v-1-1c-1 0-1 0-2-1h1 0v-1c0-1 0-1-1-2h-2l-1-1c1 0 2 0 3-1h-2 0v-1h1c1 0 2-1 2 0 2 0 3-1 4-1z" class="h"></path><path d="M285 563l1-1c1 1 2 1 3 1v1l-3 1c-2 0-4-1-7-1 1 0 2-1 2 0 2 0 3-1 4-1z" class="l"></path><path d="M326 441l2-1c0 2-2 4-3 6-1 1-2 1-2 2v1h-1l-1 1c0 1 0 1-1 2l-1 2c-1 2-1 3-1 5l-1 2v2l-1 1 1 1h0l-2 2c-2 1-3 3-3 5l-2 2h0c-1 1-1 1-1 2h0l-2 3-1-1c-1 1-1 3-1 4 0-3 0-6 1-9v-2-1l1-6 3-6c0-1 1-3 2-4l2-2c1-2 2-3 4-4v-1s1 0 1-1c2-2 3-4 4-7l1 1c-1 2-2 3-2 5 0 0 1 0 1 1l3-5z" class="N"></path><path d="M326 441l2-1c0 2-2 4-3 6-1 1-2 1-2 2v1h-1l-1 1c0 1 0 1-1 2l-1 2c-1 2-1 3-1 5l-1 2v2l-1 1 1 1h0l-2 2c-2 1-3 3-3 5l-2 2c1-2 1-4 2-6 1-3 2-7 4-11s5-7 7-11l3-5z" class="Y"></path><path d="M306 470l1-6 3-6c0-1 1-3 2-4l2-2c1-2 2-3 4-4l-6 12-6 18c-1 1-1 3-1 4 0-3 0-6 1-9v-2-1z" class="d"></path><path d="M379 460l5 2 2 1 1 1c0 1 0 1 1 1 1 1 1 2 2 3 0 1 1 2 2 2 0 3 3 5 2 7v2c-2-1-2-3-4-5-1-1-2-1-3-1v1h-1-1c0 1 1 1 0 2h-1l-1-2-3-3c-2 0-3-1-5-1v1l-3-1v-1s-1-1-2-1l1-1h1v-1l1-1h-1 0c-1-1-2-1-3-1-3-1-6-2-9-1-3 0-6 1-9 1-1 1-2 2-4 2v-1c3-1 5-2 7-3 2 0 5-1 7-2h6c2 0 3 1 5 1l4 1h1 1l-1-2h0 2z" class="M"></path><path d="M375 464c2 0 5 1 7 2h0l-1 1h-1c-2-1-3-2-5-3h0 0z" class="G"></path><path d="M383 470c1 0 2 2 4 3v1h-1-1c0 1 1 1 0 2h-1l-1-2c0-1 0-1 1-2l-1-2z" class="Z"></path><path d="M379 468c1 0 3 2 4 2l1 2c-1 1-1 1-1 2l-3-3h1c-1-2-1-2-2-3z" class="G"></path><path d="M382 465c1 1 3 1 4 2v1h0c1 1 1 2 2 3v1c-3-2-5-3-7-5l1-1h0v-1z" class="K"></path><path d="M382 465c1 1 3 1 4 2v1h0l-4-2h0v-1z" class="O"></path><path d="M373 465c1 1 3 1 4 2l2 1c1 1 1 1 2 3h-1c-2 0-3-1-5-1v1l-3-1v-1s-1-1-2-1l1-1h1v-1l1-1z" class="S"></path><path d="M373 465c1 1 3 1 4 2-1 0-3 0-4 1l2 2v1l-3-1v-1s-1-1-2-1l1-1h1v-1l1-1zm13-2l1 1c0 1 0 1 1 1 1 1 1 2 2 3 0 1 1 2 2 2 0 3 3 5 2 7-1-2-3-4-5-4 0-1-1-1-1-1v-1c-1-1-1-2-2-3h0v-1-2c-1 0-1-1-2-1 0 0 1-1 2-1z" class="G"></path><path d="M386 463l1 1c0 1 0 1 1 1 1 1 1 2 2 3l-4-3c-1 0-1-1-2-1 0 0 1-1 2-1z" class="h"></path><path d="M379 460l5 2 2 1c-1 0-2 1-2 1 1 0 1 1 2 1v2c-1-1-3-1-4-2v1c-2-1-5-2-7-2v-1c-1 0-2-1-3-2l4 1h1 1l-1-2h0 2z" class="N"></path><path d="M382 465l-4-3 6 2c1 0 1 1 2 1v2c-1-1-3-1-4-2z" class="Z"></path><path d="M344 478l5-2 1 1h0 4v1c1-1 1-1 2-1 2-1 8 0 10 1h0l-1 1-2-1h-3l3 2 2 1h0v1l1 1v1l-2-1-1 2-3-2c-2-1-4-1-6-1h0-8c-1 1-3 1-3 1-1 0-2 1-3 1 0 1-1 1-1 2-1 1-1 1-1 2-1 0-2 1-2 2-1 1-2 3-2 4 1 1 2 1 2 3h-1l-1 1v1h-1v4l-1-1h-1v-4l-1 1c-1-1-1-2-1-4 1-1 1-5 1-7l1-3 2-2c2 0 3-2 4-3 2-1 5-2 7-2h0z" class="S"></path><path d="M344 478h0c0 1 1 1 1 2-1 0-2 1-3 1l-2-1 4-2zm19 2l2 1h0v1l1 1v1l-2-1-1 2-3-2h1c1-1 1-1 3-1l-1-1h0v-1z" class="K"></path><path d="M335 485c1-2 3-3 5-5l2 1c-1 1-2 1-3 2 1 1 3-1 4 0-1 0-2 1-3 1-2 0-3 1-5 2v-1z" class="O"></path><path d="M354 478c1-1 1-1 2-1 2-1 8 0 10 1h0l-1 1-2-1h-3c-3 1-8 1-11 1 1-1 3-1 5-1z" class="n"></path><path d="M333 483c2 0 3-2 4-3 2-1 5-2 7-2l-4 2c-2 2-4 3-5 5v1c2-1 3-2 5-2 0 1-1 1-1 2-1 1-1 1-1 2-1 0-2 1-2 2-1 1-2 3-2 4 1 1 2 1 2 3h-1l-1 1v1h-1v4l-1-1h-1v-4l-1 1c-1-1-1-2-1-4 1-1 1-5 1-7l1-3 2-2z" class="N"></path><path d="M335 487h1c-2 2-3 4-4 7 0 1 0 1 1 2v1h-1-1c0 1 1 1 1 1v4h-1v-4c-1 0-1 0-1-1s0-2 1-3c0-3 2-5 4-7z" class="d"></path><path d="M333 483c2 0 3-2 4-3 2-1 5-2 7-2l-4 2c-2 2-4 3-5 5v1 1c-2 2-4 4-4 7-1 1-1 2-1 3s0 1 1 1l-1 1c-1-1-1-2-1-4 1-1 1-5 1-7l1-3 2-2z" class="X"></path><path d="M335 485v1 1c-2 2-4 4-4 7h-1c0-4 2-6 5-9z" class="M"></path><path d="M355 489l2 1c2 2 4 4 5 6 1 1 3 2 3 3 1 1 2 2 2 3v2l2 4c1 1 1 2 1 3v4h-1l-1-3h-1v3c0 1-1 3-1 4-1 1-2 1-3 1l1-5h-1v-1h-1l-1 1h0-1v1h-1l1-4v-3l-2 1v-3c0-1 0 0-1-1 0-3-2-5-4-8h0c0-2-1-2-2-3h-1v-2c2 2 3 3 5 4h0c-1-2-4-4-5-6h0 1 1 0l-1-2c2 0 2 0 3 2h1l-1-2h1z" class="K"></path><path d="M361 508c1 1 1 2 2 3l-2 4h-1v1h-1l1-4c1-1 1-3 1-4z" class="S"></path><path d="M361 508v-2c0-1 0-2 1-2h1v1l1 2-1 4c-1-1-1-2-2-3z" class="P"></path><path d="M364 507c1 3 1 5 1 8h-1-1v-1h-1l-1 1h0l2-4 1-4z" class="J"></path><path d="M351 489c2 0 2 0 3 2s2 3 3 5c1 1 3 2 3 4h0-1l1 3c1 1 1 1 1 2-1-1-2-3-3-4-1-2-2-3-3-4-1-2-4-4-5-6h0 1 1 0l-1-2z" class="O"></path><path d="M355 489l2 1c2 2 4 4 5 6 1 1 3 2 3 3l-1 2c0 1 0 1-1 1v2l-3-4h0c0-2-2-3-3-4-1-2-2-3-3-5h1l-1-2h1z" class="b"></path><path d="M362 496c1 1 3 2 3 3l-1 2-2-5z" class="h"></path><path d="M355 491c3 2 6 7 8 11v2l-3-4h0c0-2-2-3-3-4-1-2-2-3-3-5h1z" class="M"></path><path d="M365 499c1 1 2 2 2 3v2l2 4c1 1 1 2 1 3v4h-1l-1-3h-1v3c0 1-1 3-1 4-1 1-2 1-3 1l1-5h1c0-3 0-5-1-8l-1-2v-1h0v-2c1 0 1 0 1-1l1-2z" class="a"></path><path d="M365 499c1 1 2 2 2 3l-1 1h-2l-1 2v-1h0v-2c1 0 1 0 1-1l1-2z" class="N"></path><path d="M365 515v-1-5c1 1 1 4 2 6 0 1-1 3-1 4-1 1-2 1-3 1l1-5h1z" class="f"></path><path d="M367 512c0-1 1-2 0-4v-4h0l2 4c1 1 1 2 1 3v4h-1l-1-3h-1z" class="j"></path><defs><linearGradient id="AO" x1="399.392" y1="487.518" x2="386.884" y2="486.244" xlink:href="#B"><stop offset="0" stop-color="#444343"></stop><stop offset="1" stop-color="#69696a"></stop></linearGradient></defs><path fill="url(#AO)" d="M375 471v-1c2 0 3 1 5 1l3 3 1 2h1c1-1 0-1 0-2h1 1v-1c1 0 2 0 3 1 2 2 2 4 4 5l2 4c1 1 1 2 1 2l1 3c1 3 2 5 2 7s0 3 1 5l-1 1v4h0 0c-1-2-1-3-1-5-1 0-1-1-1-2v-1h-1v-2h0v-1c-1 1-1 1-1 2s0 2-1 3h0c0 1 0 3-1 4l-1-1c1-2 1-3 1-4 0-2-1-5-2-6 0-2-1-3-2-5-1-1-2-2-3-4v1 3 1 1l-2-2-2-3c1-2-1-2-1-4v-2c-1-1-3-3-5-4l1-1c-1-1-2-1-3-2z"></path><path d="M390 484c2 2 3 5 3 8h-1c0-2-1-3-2-5l-1-3h1z" class="M"></path><path d="M397 495l1 3 1-1c0-2 0-4-1-6 0-1 0-1-1-2l1-1c1 3 2 5 2 7s0 3 1 5l-1 1v4h0 0c-1-2-1-3-1-5-1 0-1-1-1-2v-1h-1v-2h0z" class="T"></path><path d="M387 474c3 2 5 7 6 11-3-4-4-8-7-11l4 10h-1l-4-6-1-2h1c1-1 0-1 0-2h1 1z" class="l"></path><path d="M375 471v-1c2 0 3 1 5 1l3 3 1 2 1 2 4 6 1 3c-1-1-2-2-3-4v1 3 1 1l-2-2-2-3c1-2-1-2-1-4v-2c-1-1-3-3-5-4l1-1c-1-1-2-1-3-2z" class="X"></path><path d="M382 478c1 1 3 3 3 5h-1c1 2 2 2 1 4l-2-3c1-2-1-2-1-4v-2z" class="M"></path><defs><linearGradient id="AP" x1="380.296" y1="476.721" x2="378.529" y2="469.564" xlink:href="#B"><stop offset="0" stop-color="#6d6c6d"></stop><stop offset="1" stop-color="#858585"></stop></linearGradient></defs><path fill="url(#AP)" d="M375 471v-1c2 0 3 1 5 1l3 3 1 2 1 2c-3-2-4-4-7-5-1-1-2-1-3-2z"></path><defs><linearGradient id="AQ" x1="351.644" y1="492.198" x2="350.836" y2="482.204" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#b6b4b4"></stop></linearGradient></defs><path fill="url(#AQ)" d="M343 483s2 0 3-1h8c4 1 7 3 11 5l3 3 3 6c0 1 1 3 1 4 1 1 1 2 1 3-1 1-1 3-1 5h-3l-2-4v-2c0-1-1-2-2-3 0-1-2-2-3-3-1-2-3-4-5-6l-2-1h-1l1 2h-1c-1-2-1-2-3-2h-2v-1l-1-1h0c0-1-1-1-1-2l-6 3-1 2v-1h-1-1v-1c0-1 0-1 1-2 0-1 1-1 1-2 1 0 2-1 3-1z"></path><path d="M364 491l1 1-2 1-1-1 2-1z" class="Z"></path><path d="M341 488c1-1 2-2 4-3 3-1 7-1 10 1 1 0 2 0 2 1h1c0 1 0 2 1 3 1 0 1 1 2 2-2-1-3-1-4-2l-2-1h-1l1 2h-1c-1-2-1-2-3-2h-2v-1l-1-1h0c0-1-1-1-1-2l-6 3z" class="K"></path><path d="M349 488c1-1 2-1 3-1 1 1 3 1 3 2h-1l1 2h-1c-1-2-1-2-3-2h-2v-1z" class="h"></path><defs><linearGradient id="AR" x1="368.148" y1="508.033" x2="363.181" y2="491.014" xlink:href="#B"><stop offset="0" stop-color="#59595a"></stop><stop offset="1" stop-color="#7d7c7c"></stop></linearGradient></defs><path fill="url(#AR)" d="M364 491c-1-1-2-3-3-4h1v1h1c0-1 1-1 2-1l3 3 3 6c0 1 1 3 1 4 1 1 1 2 1 3-1 1-1 3-1 5h-3l-2-4v-2c0-1-1-2-2-3 0-1-2-2-3-3-1-2-3-4-5-6 1 1 2 1 4 2-1-1-1-2-2-2-1-1-1-2-1-3h-1c2 1 5 3 5 5l1 1 2-1-1-1z"></path><path d="M363 493l2-1 1 1c0 3 1 4 2 5l-1 1c-1-1-1-2-2-3 0-1-1-2-2-3z" class="O"></path><defs><linearGradient id="AS" x1="372.804" y1="502.302" x2="363.586" y2="489.311" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#979596"></stop></linearGradient></defs><path fill="url(#AS)" d="M364 491c-1-1-2-3-3-4h1v1h1c0-1 1-1 2-1l3 3 3 6c0 1 1 3 1 4v6c-1-4-3-9-6-12v-1l-1-1-1-1z"></path><defs><linearGradient id="AT" x1="371.551" y1="494.064" x2="372.012" y2="478.982" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#b3b2b2"></stop></linearGradient></defs><path fill="url(#AT)" d="M366 478c3 1 6 2 8 4 1 1 2 2 3 2 1 2 2 3 3 4l1 2v3l-1-1c0 1 1 2 1 3l1 1h1l1 1 1 5c0 1 0 2-1 3v1l-1 1c-1-1-1-1-1-2l-1-1v-1l-1 1-1-1c0-1 0-2-1-3v-1c-1-1-2-2-3-2 0 1 1 2 0 3v1l-2-6-2 1h0 0l-3-6-3-3c-4-2-7-4-11-5h0c2 0 4 0 6 1l3 2 1-2 2 1v-1l-1-1v-1h0l-2-1-3-2h3l2 1 1-1z"></path><path d="M377 496l2-2c1 2 2 4 2 5-1 0-2-1-3-2l-1-1z" class="O"></path><path d="M374 482c1 1 2 2 3 2 1 2 2 3 3 4l1 2v3l-1-1c-1-2-3-4-4-5s-1-2-2-3v-1-1z" class="V"></path><path d="M381 495l1 1h1l1 1 1 5c0 1 0 2-1 3v1l-1 1c-1-1-1-1-1-2l-1-1v-1l-1 1-1-1c0-1 0-2-1-3v-1-2c1 1 2 2 3 2 0 2 1 3 2 5 0-2 0-3-1-5 0-2-1-3-1-4z" class="h"></path><defs><linearGradient id="AU" x1="370.146" y1="496.684" x2="370.525" y2="484.02" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#AU)" d="M365 481c1 1 2 1 3 2 3 1 5 4 6 6l1 2 2 5 1 1v2c-1-1-2-2-3-2 0 1 1 2 0 3v1l-2-6-2 1h0 0l-3-6-3-3c-4-2-7-4-11-5h0c2 0 4 0 6 1l3 2 1-2 2 1v-1l-1-1v-1z"></path><path d="M368 490h0c2 1 3 3 5 5l-2 1h0 0l-3-6z" class="X"></path><path d="M365 481c1 1 2 1 3 2h0v2h-1-1v1c0 1 0 0-1 0s-1-1-2-1l1-2 2 1v-1l-1-1v-1z" class="Z"></path><path d="M375 497h0v-1c-1-1-1-4-1-5h1l2 5 1 1v2c-1-1-2-2-3-2z" class="N"></path><path d="M283 516c1 2 3 4 5 6v-1c1-1 3-1 4-1l3 1c1 0 3 0 4 1h1 0c1 2 2 3 3 5 2 2 6 5 8 7 1 1 5 2 5 3-1 0-2 0-3-1v2 2h0 0c-2 0-2 0-2-1h-4v-1l-1 1h-1-2l-1-1c-3-1-5-3-8-5-1-1-5-6-7-6 0-1-1-1-1-2-2-3-3-6-3-9z" class="k"></path><path d="M295 521c1 0 3 0 4 1 0 1 0 1 1 2s1 2 1 3c-2-2-6-4-6-6z" class="j"></path><path d="M297 531c1 0 1 0 2 1 3 2 7 3 10 4 2 0 3 1 4 2v2h0 0c-2 0-2 0-2-1h-4v-1l-1 1h-1-2l-1-1h0v-3l-5-4z" class="T"></path><path d="M302 535c1 1 3 3 5 3l-1 1h-1-2l-1-1h0v-3z" class="D"></path><path d="M300 522h0c1 2 2 3 3 5 2 2 6 5 8 7 1 1 5 2 5 3-1 0-2 0-3-1-5-1-9-4-14-7-2-2-4-3-6-5h0c1 0 2 1 3 2 3 2 6 4 9 5l-4-4c0-1 0-2-1-3s-1-1-1-2h1z" class="f"></path><path d="M283 516c1 2 3 4 5 6 2 3 6 7 9 9l5 4v3h0c-3-1-5-3-8-5-1-1-5-6-7-6 0-1-1-1-1-2-2-3-3-6-3-9z" class="C"></path><defs><linearGradient id="AV" x1="445.37" y1="500.863" x2="433.601" y2="501.486" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#AV)" d="M434 487v-2h0c0-2-1-4-2-5l5 4c1 2 3 4 4 7v1c1 0 1 1 1 1 1 1 1 3 2 4 1 4 2 9 2 12v3 1c-1 2-2 7-1 9h1-1c-1 0-1-1-1-2h-1 0c-1 1-1 1-1 2l-1 1v1c0 1-1 1-1 1l-2 1h0v-1-2h1c1-1 1-1 1-2h-1v-3h-1c0-3-1-7-1-11h0v-4h0c1 0 1-1 1-2l-1-1c0 1 0 1-1 1v-2-1c-2-2-2-6-3-9 1-1 1-1 1-2z"></path><path d="M440 503v-1c1-1 0-2 0-3 1 1 1 3 1 4 0 2 1 3 1 4h0l-1 1v-1c-1-1-1-2-1-4z" class="Y"></path><path d="M439 504h0v3c0-1 0-2 1-4h0c0 2 0 3 1 4v1l1-1h0c0 3 1 13 0 15-1 0-1 1-1 1v1c0 1-1 1-1 1l-2 1h0v-1-2h1c1-1 1-1 1-2h-1v-3h-1c0-3-1-7-1-11 1-1 1-2 2-3z" class="f"></path><path d="M437 507c1-1 1-2 2-3 0 2-1 4-1 6 0 1 1 2 1 3v5h-1c0-3-1-7-1-11z" class="g"></path><defs><linearGradient id="AW" x1="292.687" y1="551.553" x2="284.465" y2="532.316" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#898889"></stop></linearGradient></defs><path fill="url(#AW)" d="M279 530h0c1 1 1 2 2 3v-1c0-1-1-2-1-3v-1c-1-2-1-5 0-6v1h2v1l1-1 3 6c2 4 4 7 7 10-1 1-1 1-1 2l-1-1c0 1 1 2 2 3 0 1 2 2 2 3v1s1 0 0 1l2 2-1 1 2 2c1 1 2 2 3 2l-1 1 2 1v1l-2-1-6-3-1 1-6-6c-1 0-2-1-3-2l-3-3c-1-2-2-2-3-3 0-1-1-2-1-3v-1c0-1 0-1 1-2l2 3c0-1 0-2-1-4l-1-2h1v-1-1z"></path><path d="M296 551l2 2c1 1 2 2 3 2l-1 1 2 1v1l-2-1-6-3c-1-1-1-2-2-2 3 0 4 1 6 3 0-2-1-2-2-3v-1h0z" class="o"></path><defs><linearGradient id="AX" x1="284.432" y1="537.364" x2="288.762" y2="531.886" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#c1c0c1"></stop></linearGradient></defs><path fill="url(#AX)" d="M282 523v1l1-1 3 6c2 4 4 7 7 10-1 1-1 1-1 2l-1-1c0 1 1 2 2 3 0 1 2 2 2 3v1s1 0 0 1c-5-4-11-13-13-19-1-2-1-4-2-6h2z"></path><path d="M282 524l1-1 3 6c2 4 4 7 7 10-1 1-1 1-1 2l-1-1c-4-5-7-10-9-16z" class="N"></path><defs><linearGradient id="AY" x1="290.996" y1="553.376" x2="280.04" y2="534.702" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#bbbabb"></stop></linearGradient></defs><path fill="url(#AY)" d="M279 531c3 4 4 9 7 13 1 1 3 3 4 5 1 0 2 2 2 3 1 0 1 1 2 2l-1 1-6-6c-1 0-2-1-3-2l-3-3c-1-2-2-2-3-3 0-1-1-2-1-3v-1c0-1 0-1 1-2l2 3c0-1 0-2-1-4l-1-2h1v-1z"></path><path d="M277 538v-1c0-1 0-1 1-2l2 3c2 4 4 8 7 11-1 0-2-1-3-2l-3-3c-1-2-2-2-3-3 0-1-1-2-1-3z" class="d"></path><path d="M341 488l6-3c0 1 1 1 1 2h0l1 1v1h2l1 2h0-1-1 0c1 2 4 4 5 6h0c-2-1-3-2-5-4v2h1c1 1 2 1 2 3h0c2 3 4 5 4 8 1 1 1 0 1 1l-1 5v6l-1-7v-1-2s-1 0-1-1h0c0-1 0-1-1-2 1 1 1 2 1 3v5 3h-1c0-1-1-1-1-2l-1-2c0-1-2-4-3-4h0v1l-3-2-2-3c-1 0-1 0-1-1 1 3 3 6 3 9 0 1 0 1-1 2l-1-1-1-2-2-1-1-2-3-6c0-1 0-2-1-3v-2c0-2-1-2-2-3 0-1 1-3 2-4 0-1 1-2 2-2v1h1 1v1l1-2z" class="J"></path><path d="M340 489v1 4l-2-2c0-1 1-2 2-3z" class="c"></path><path d="M337 492h1l2 2c0 1-1 2-1 3h-1c-1-1-1-2-1-4v-1z" class="G"></path><path d="M336 490c0-1 1-2 2-2v1h1 1c-1 1-2 2-2 3h-1c-1 0-1-1-1-2z" class="J"></path><path d="M336 490c0 1 0 2 1 2v1c0 2-1 4-1 6v-2c0-2-1-2-2-3 0-1 1-3 2-4z" class="O"></path><path d="M337 502v-1c1-1 1-1 1-3 1 1 1 4 2 5 1 2 2 5 3 8l-2-1-1-2-3-6zm8-11l3-3c0 1-1 1-1 1 0 2 1 2 2 3v1 1c0 1 3 3 4 4h0v4c-1-2-3-4-4-6-1 0-1-1-1-1-1-2-1-3-3-4z" class="l"></path><path d="M344 499h0c-1-2-1-6-1-8 1-1 1-3 3-4v1c-1 1-2 3-2 5h1 0s0 1 1 2c0 1 1 2 1 2l-1 1v1h-2z" class="M"></path><path d="M346 512c-1-1-2-1-2-2v-1c-2-2-4-8-4-11h1v2c1 0 1 1 2 1l-1-1v-2h1v2c1 1 1 1 2 1 1 1 1 3 2 4l2 3v1l-3-2-2-3c-1 0-1 0-1-1 1 3 3 6 3 9z" class="O"></path><path d="M345 491c2 1 2 2 3 4 0 0 0 1 1 1 1 2 3 4 4 6v-4c2 3 4 5 4 8 1 1 1 0 1 1l-1 5v6l-1-7v-1-2s-1 0-1-1h0c0-1 0-1-1-2 1 1 1 2 1 3v5 3h-1c0-1-1-1-1-2l-1-2c0-1-2-4-3-4h0l-2-3c-1-1-1-3-2-4l-1-2h2v-1l1-1s-1-1-1-2c-1-1-1-2-1-2v-2z" class="d"></path><path d="M353 498c2 3 4 5 4 8 1 1 1 0 1 1l-1 5c0-4-2-7-4-10v-4z" class="Z"></path><path d="M345 491c2 1 2 2 3 4 0 0 0 1 1 1 0 2 0 2 1 3s1 2 2 3v2c-1-2-2-3-4-4 0-1-1-2-1-3 0 0-1-1-1-2-1-1-1-2-1-2v-2z" class="X"></path><path d="M346 499v-1l1-1c0 1 1 2 1 3 2 1 3 2 4 4 1 1 2 4 2 6v3l-1 1-1-2c0-1-2-4-3-4h0l-2-3c-1-1-1-3-2-4l-1-2h2z" class="o"></path><path d="M344 499h2c0 1 2 4 2 5s-1 1-1 1c-1-1-1-3-2-4l-1-2z" class="h"></path><path d="M347 505s1 0 1-1c2 3 4 4 5 6h1v3l-1 1-1-2c0-1-2-4-3-4h0l-2-3z" class="N"></path><path d="M266 498l2 3c1 1 1 2 1 4s0 6 1 9l1 5c0 1 1 1 2 1 0 2 0 3 1 5h1l3 7 1 2c1 2 1 3 1 4l-2-3c-1 1-1 1-1 2v1c0 1 1 2 1 3 1 1 2 1 3 3l3 3c2 4 6 7 9 10 2 0 4 1 5 2v2c1 1 6 2 8 3h2v1h-1l-1 1c-1 0-3-1-4 0-2 0-4-2-6-3s-3-2-5-2l-2-1 1-1c-2-1-3-1-4-3-6-4-10-10-13-17-1-1-2-3-2-4 0-2 0-3-1-5v-1l1-1c-1-2 0-4-2-5l1-1-1-1h0-1-3l-1-2 2-1c-2-3-3-5-4-8 1 0 1 1 2 2h1v-3c0-3 0-8 1-11z" class="g"></path><path d="M290 557l4 3c-1 0-2-1-4-1-2-1-3-1-4-3 2 1 3 1 4 1z" class="h"></path><path d="M271 519c0 1 1 1 2 1 0 2 0 3 1 5h-1v3c-1-3-2-6-2-9z" class="Z"></path><path d="M290 559c2 0 3 1 4 1s1 1 2 1c3 2 7 4 11 4l-1 1c-1 0-3-1-4 0-2 0-4-2-6-3s-3-2-5-2l-2-1 1-1z" class="a"></path><path d="M265 509c1 4 1 6 3 9l2 2s0 1-1 1h0-1-3l-1-2 2-1c-2-3-3-5-4-8 1 0 1 1 2 2h1v-3z" class="S"></path><path d="M266 518l3 3h-1-3l-1-2 2-1z" class="i"></path><path d="M274 525h1l3 7 1 2c1 2 1 3 1 4l-2-3c-1 1-1 1-1 2v1c-2-3-4-7-4-10v-3h1z" class="X"></path><path d="M274 525h1l3 7 1 2c1 2 1 3 1 4l-2-3-4-10z" class="L"></path><defs><linearGradient id="AZ" x1="288.152" y1="544.199" x2="271.089" y2="543.795" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#AZ)" d="M270 529l1-1c3 12 10 21 19 29-1 0-2 0-4-1-6-4-10-10-13-17-1-1-2-3-2-4 0-2 0-3-1-5v-1z"></path><defs><linearGradient id="Aa" x1="364.673" y1="483.543" x2="363.563" y2="469.858" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#9e9d9e"></stop></linearGradient></defs><path fill="url(#Aa)" d="M360 463c3-1 6 0 9 1 1 0 2 0 3 1h0 1l-1 1v1h-1l-1 1c1 0 2 1 2 1v1l3 1c1 1 2 1 3 2l-1 1c2 1 4 3 5 4v2c0 2 2 2 1 4-3-3-6-6-10-7l-5-3h-1v1c-3-1-7-1-10-1-2 1-6 2-8 2l-5 2h0c-2 0-5 1-7 2-1 1-2 3-4 3l-2 2c0-1 0-1 1-2 0-1 0-1 1-2h0c2-2 4-4 5-6 2-1 3-3 5-4h0c2-2 4-2 6-4 1-1 4-2 6-3 2 0 3 0 5-1z"></path><path d="M368 474c1 0 3 0 5 1 0 0 0 1 1 1h-1v1l-5-3z" class="G"></path><path d="M357 474c1-1 5-1 7-1 1 0 2 0 3 1v1c-3-1-7-1-10-1z" class="o"></path><path d="M358 468c2-1 5-1 7-1s4 1 5 1 2 1 2 1v1l3 1c1 1 2 1 3 2l-1 1c-7-4-15-6-23-3-6 2-11 6-17 8 2-2 4-3 7-5 2-1 5-4 8-5l6-1z" class="O"></path><path d="M358 468c2-1 5-1 7-1s4 1 5 1 2 1 2 1v1c-5-1-10-2-15-1-2 1-3 1-5 0l6-1z" class="K"></path><path d="M360 463c3-1 6 0 9 1 1 0 2 0 3 1h0 1l-1 1v1h-1l-1 1c-1 0-3-1-5-1s-5 0-7 1l-6 1c-3 1-6 4-8 5-3 2-5 3-7 5l-4 4-2 2c0-1 0-1 1-2 0-1 0-1 1-2h0c2-2 4-4 5-6 2-1 3-3 5-4h0c2-2 4-2 6-4 1-1 4-2 6-3 2 0 3 0 5-1z" class="b"></path><path d="M363 464c3 0 6 0 9 1h1l-1 1v1h-1l-1 1c-1 0-3-1-5-1 1 0 2-1 3-2h-1c-2 0-3-1-4-1z" class="o"></path><path d="M358 467v1l-6 1c-3 1-6 4-8 5v-2c1 0 2-1 3-1 1-2 2-3 5-3h1c1 0 3-1 5-1z" class="N"></path><path d="M352 468l6-3c2 0 3-1 5-1 1 0 2 1 4 1h1c-1 1-2 2-3 2-2 0-5 0-7 1v-1c-2 0-4 1-5 1h-1z" class="l"></path><path d="M368 465c-1 1-2 2-3 2-2 0-5 0-7 1v-1l1-1h1c3 0 5-1 8-1z" class="M"></path><path d="M439 531h1c3 2 4 4 5 7h0c0 1 0 2-1 3l1 1h1l-3 4-1 3-4 7-2 3-5 8h0c-3 1-5 5-8 4l-1-1v1c-1 1-2 3-2 5l-2 1c0-1 1-2 2-4h0c0-1 1-2 1-2h0c-2 1-3 2-5 3l1 1v1h-2 0c1-3 4-5 6-7 1-2 1-3 2-5 2-3 3-7 3-11 1-1 1-3 2-5v-1-4l1 2h0c1-2 1-4 3-5h0v1h3v-3h2l2-2v-1h1c0-1-1-2-2-3 0 0 0-1 1-1z" class="D"></path><path d="M441 536c0 1 1 3 2 4 0 2-1 4 0 5v1h0l-1 3-2-1c0-1 1-1 1-2v-6h-1l1-1v-1-2zm-3 2c1 1 1 2 1 3-1 5-2 12-6 16-1 1-1 2-2 2 1-2 2-4 3-7l3-9c0-1 1-3 1-5z" class="F"></path><path d="M439 531h1c3 2 4 4 5 7h0c0 1 0 2-1 3l1 1h1l-3 4h0v-1c-1-1 0-3 0-5-1-1-2-3-2-4l-1-1c0-1-1-2-2-3 0 0 0-1 1-1z" class="B"></path><path d="M426 564c0 1 0 2 1 3 3-1 4-3 5-5l6-6h0l-2 3-5 8h0c-3 1-5 5-8 4l-1-1 4-6z" class="K"></path><path d="M435 538h2 1c0 2-1 4-1 5l-3 9c-1 3-2 5-3 7 0 2-1 2-2 3h0 0c-1-3 1-8 1-10 2-4 3-8 5-11v-3z" class="m"></path><path d="M428 543l1 2h0c1-2 1-4 3-5h0v1h3c-2 3-3 7-5 11v-2c-1 3-2 6-3 10 0 1-1 2-1 4l-4 6v1c-1 1-2 3-2 5l-2 1c0-1 1-2 2-4h0c0-1 1-2 1-2h0c-2 1-3 2-5 3l1 1v1h-2 0c1-3 4-5 6-7 1-2 1-3 2-5 2-3 3-7 3-11 1-1 1-3 2-5v-1-4z" class="I"></path><path d="M301 574h9-1-1c0 1-7 1-8 1 2 1 4 1 6 1 1-1 2-1 3-1 1-1 2 0 3-1h0-2c0-1 0-1-1-1l-1-1h0c2 1 3 1 5 1h5c2 1 3 0 5 0-1 1-4 1-5 2 1 1 1 1 3 1l-7 3-1-1-2 1c-1 1-4 3-5 4 1 1 2 1 2 3-1 1-1 2-2 3 0 0 0 1-1 1h1l1 1h-2c-1 1-3 2-4 3v2c0 1 0 3 1 4v1c-2 0-5-1-8-1l-2-1c-2 0-3 0-5-1h-1c-1-1-1-3 0-5v-2h0l2-2-1-1 1-1h0c-1-1-1-1-3-1 0 0-1 0-2-1h1 0c2-1 3-1 4-2h0c1-1 2-1 3-2 0 0 1-1 2-1l1-1c1 0 1-1 1-2 1 0 3-1 3-1v-1h-1c-1 0-1-1-2-1h6z" class="i"></path><path d="M291 581v3c-1 1-1 1-3 1v-2c1-1 2-1 3-2z" class="e"></path><path d="M288 587l2-2h1l2-2c0-1 1-1 1-2h1c0-1 1-1 1-1 1-1 2-1 4-1l-4 2v2 1h-1c-1 1-1 1-1 3h0v3l-3 3v1c-1 1-3 2-4 3v1h-1c-1-1-1-3 0-5v-2h0l2-2-1-1 1-1z" class="Z"></path><path d="M294 587h0v3l-3 3v-2c1-2 2-3 3-4z" class="G"></path><path d="M288 587l2-2h1l2-2c0-1 1-1 1-2h1c0-1 1-1 1-1 1-1 2-1 4-1l-4 2c-3 3-7 7-8 11-1 2-1 4-1 5v1h-1c-1-1-1-3 0-5v-2h0l2-2-1-1 1-1z" class="Y"></path><defs><linearGradient id="Ab" x1="301.298" y1="577.463" x2="300.357" y2="590.494" xlink:href="#B"><stop offset="0" stop-color="#747273"></stop><stop offset="1" stop-color="#9d9c9d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M304 577c3-1 7-2 10-2 2 1 2 0 4 0 1 1 1 1 3 1l-7 3-1-1-2 1v-1c-2 0-5 2-6 4h-1l-1 1-1-1c-2 2-4 3-5 5h0l-2 4-1-1v-3h0c0-2 0-2 1-3h1v-1-2l4-2 4-2z"></path><path d="M304 577c3-1 7-2 10-2 2 1 2 0 4 0 1 1 1 1 3 1l-7 3-1-1c1 0 2-1 3-2h0-1c-2 1-3 0-5 1h0c-3 0-4 1-6 0z" class="N"></path><path d="M297 587l3 1c0 1 0 1-1 2v1c0 2-1 3-1 4v1h1v-1l2 1c0 1 0 3 1 4v1c-2 0-5-1-8-1l-2-1c-2 0-3 0-5-1v-1c1-1 3-2 4-3v-1l3-3 1 1 2-4z" class="G"></path><path d="M291 593l3-3 1 1c0 1 0 2-1 4v1c-1-1-1-1-3 0v-2-1z" class="c"></path><path d="M287 597c1-1 3-2 4-3v2c2-1 2-1 3 0 0 1 1 3 0 4l-2-1c-2 0-3 0-5-1v-1z" class="J"></path><path d="M291 596c2-1 2-1 3 0 0 1 1 3 0 4l-2-1-1-1v-2z" class="V"></path><path d="M304 582h1c1-2 4-4 6-4v1c-1 1-4 3-5 4 1 1 2 1 2 3-1 1-1 2-2 3 0 0 0 1-1 1h1l1 1h-2c-1 1-3 2-4 3v2l-2-1v1h-1v-1c0-1 1-2 1-4v-1c1-1 1-1 1-2l-3-1h0c1-2 3-3 5-5l1 1 1-1z" class="c"></path><path d="M304 582v1h0v1l-1 1c0 1 1 2-1 2h-1v-1l1-1c0-1 1-2 1-2l1-1z" class="P"></path><path d="M304 586l2-3c1 1 2 1 2 3-1 1-1 2-2 3 0 0 0 1-1 1-2 0-3 1-4 2l3-6z" class="W"></path><path d="M304 586l2-3c1 1 2 1 2 3h-4z" class="f"></path><path d="M302 464c1-1 1-1 3 0v1h0c0 2 0 4 1 5h0v1 2c-1 3-1 6-1 9v10h0v1c-1 1-1 3-2 4v-1-4l-1 1c-1 6 1 12 1 19 0 2 1 4 2 5 1 2 1 3 3 4h0c-1 1-1 1-1 2h-1l-6-10v2l1 1c1 3 2 7 4 10h-1c-1-1-1-2-2-4v1 1h0l-1-1v-1c-1 0-1 0-1-1v1h0l-2-4h0c0-1-1-1-2-2 2-4-2-9-2-13-1-1-1-1-1-2-1-5-1-10-1-16v-4l1-1v3l1 2c1 0 1 0 1-1l1 1v-4h0c1 0 1-1 2-2v1c1 0 1 0 2-1v-1c-1-1-1-2-1-3-1 0-1-1-1-1l1-4c0-2 1-2 2-3l1-3z" class="e"></path><path d="M294 503c-1-1-1-1-1-2-1-5-1-10-1-16v-4l1-1v3l1 2c1 0 1 0 1-1l1 1-1 3v11 1c1 2 1 3 2 4l-1 1c0-1-1-1-1-2h-1 2-1v-2c-1-1-1-1-1-2-1 2 0 3 0 4h0z" class="Y"></path><path d="M295 484l1 1-1 3h-1c-1-1-1-3-1-5l1 2c1 0 1 0 1-1z" class="N"></path><path d="M294 488h1v11 1h-1c-1-3 0-6 0-8-1-2-1-3 0-4z" class="d"></path><path d="M300 479l1 4s-1 0-1 1h0v1c-1 2 0 3 0 5v8 11l-1-1c0-1 0-3-1-4 0-2-1-3-1-5v-8c1-1 1-4 1-6 0-1-1-2-2-4h0c1 0 1-1 2-2v1c1 0 1 0 2-1z" class="W"></path><path d="M300 479l1 4s-1 0-1 1l-2-4c1 0 1 0 2-1z" class="l"></path><path d="M302 464c1-1 1-1 3 0v1h0c0 2 0 4 1 5h0v1 2c-1 3-1 6-1 9v10h0v-2h-1-1c-1-1-2-3-3-4v-2h0c0-1 1-1 1-1l-1-4v-1c-1-1-1-2-1-3-1 0-1-1-1-1l1-4c0-2 1-2 2-3l1-3z" class="k"></path><path d="M305 465h0c0 2 0 4 1 5h0v1 2l-1-2-1-1c0-2 0-3 1-5z" class="f"></path><path d="M299 470c0-2 1-2 2-3 0 1-1 3-1 4 0 2 0 5 1 6s1 1 2 1v7h0c-1 0-1 0-1-1l-1-1-1-4v-1c-1-1-1-2-1-3-1 0-1-1-1-1l1-4z" class="j"></path><path d="M299 470c0-2 1-2 2-3 0 1-1 3-1 4v7c-1-1-1-2-1-3-1 0-1-1-1-1l1-4z" class="J"></path><path d="M305 471l1 2c-1 3-1 6-1 9v10h0v-2h-1-1c-1-1-2-3-3-4v-2h0c0-1 1-1 1-1l1 1c0 1 0 1 1 1h0v-7-3c1-1 1-2 2-4z" class="Y"></path><path d="M303 475c2 2 1 8 1 10 0 1 0 1-1 2l1 3h-1c-1-1-2-3-3-4v-2h0c0-1 1-1 1-1l1 1c0 1 0 1 1 1h0v-7-3z" class="d"></path><path d="M301 483l1 1c0 1 0 2-2 2v-2h0c0-1 1-1 1-1z" class="M"></path><path d="M262 524l1-4v-2c-1-1-1-1 0-2h0l1 1v2l1 2h3 1 0l1 1-1 1c2 1 1 3 2 5l-1 1v1c1 2 1 3 1 5 0 1 1 3 2 4 3 7 7 13 13 17 1 2 2 2 4 3l-1 1 2 1h-1c-1 0-3 1-4 1l-1 1c-1-1-2-1-2-2h-1 0c-1-1-3-2-4-2l-8-4c-1 0-3-1-4-2l-1-1c-2-1-4-2-5-3l1-1c1-1 1-1 1-2h1 1l-1-1c-2-2-3-4-4-7l-1-5v-5l2-5v-1h1 1v2z" class="b"></path><path d="M266 538v-1-2h1l1 1h2l1 1v1c-1 0-2-1-3-1 1 1 1 1 1 2l-2 2-1-3z" class="N"></path><path d="M267 530l-1-1v-2-1h2c2 1 2 2 2 3v1 3c-1-1-1-2-2-2 0 0-1 1-1 2h-1 0c0-2 0-2 2-4l-1 1z" class="f"></path><path d="M265 521h3 1 0l1 1-1 1c2 1 1 3 2 5l-1 1c0-1 0-2-2-3h-2v1 2l1 1h-2v-1l-1-1c0-2 0-5 1-7z" class="d"></path><path d="M269 521h0l1 1-1 1c0 1-1 1-2 1h-1l2-3h1z" class="e"></path><path d="M262 535s0-1 1-1v1c1 1 1 3 1 4 1 0 1-1 2-1l1 3h0v2c1 1 2 2 2 3 1 1 3 2 5 4v1l-1 1c-4-3-7-7-9-11l-2-3v-3z" class="f"></path><path d="M262 535s0-1 1-1v1c0 1 0 2-1 3v-3z" class="j"></path><defs><linearGradient id="Ac" x1="257.375" y1="528.184" x2="263.332" y2="540.499" xlink:href="#B"><stop offset="0" stop-color="#828282"></stop><stop offset="1" stop-color="#9f9fa0"></stop></linearGradient></defs><path fill="url(#Ac)" d="M261 522h1v2 6c1 1 1 1 0 2 0 1 1 1 1 2-1 0-1 1-1 1v3l2 3-1 1v3c-2-2-3-4-4-7l-1-5v-5l2-5v-1h1z"></path><path d="M261 522h1v2 6c1 1 1 1 0 2 0 1 1 1 1 2-1 0-1 1-1 1l-1-2c-1-2-1-3-1-5s1-4 1-6z" class="Y"></path><path d="M273 539c3 7 7 13 13 17 1 2 2 2 4 3l-1 1-1-1-6-2-5-3c-1 0-3-1-4-2l1-1v-1h0l-4-7h1l-1-1h-1v-1h1s0-1 1 0c1 2 2 5 4 6h0l-3-7 1-1z" class="i"></path><path d="M274 550c1 0 2 2 3 3v1c-1 0-3-1-4-2l1-1v-1h0z" class="W"></path><path d="M263 545v-3l1-1c2 4 5 8 9 11 1 1 3 2 4 2l5 3 6 2 1 1 2 1h-1c-1 0-3 1-4 1l-1 1c-1-1-2-1-2-2h-1 0c-1-1-3-2-4-2l-8-4c-1 0-3-1-4-2l-1-1c-2-1-4-2-5-3l1-1c1-1 1-1 1-2h1 1l-1-1z" class="c"></path><path d="M288 559l1 1 2 1h-1c-1 0-3 1-4 1l-1 1c-1-1-2-1-2-2 2 0 4 0 5-2z" class="o"></path><path d="M263 546h1l2 2c-2 0-2-1-3-1 0 1 0 2 1 3 1 0 1 1 1 2-2-1-4-2-5-3l1-1c1-1 1-1 1-2h1z" class="J"></path><path d="M263 545v-3l1-1c2 4 5 8 9 11 1 1 3 2 4 2l5 3s0 1 1 1h0c-1 0-2 1-3 0-6-2-10-5-14-10l-2-2-1-1z" class="L"></path><path d="M282 573h4s1 0 2 1h0l-2 1c3 0 6-1 9-1v-1h-2-1 10-3v1h2-6c1 0 1 1 2 1h1v1s-2 1-3 1c0 1 0 2-1 2l-1 1c-1 0-2 1-2 1-1 1-2 1-3 2h0c-1 1-2 1-4 2h0-1c1 1 2 1 2 1 2 0 2 0 3 1h0l-1 1 1 1-2 2h0v2c-1 1-2 1-3 2 0-1-1-1-1-1h-3s-1 0-1 1l-1 1-5-1c-1 0-3-1-4-2l-6-2-5-2c1-3 2-5 4-7 3-2 5-3 8-4l1-1v-1c1-1 6-1 8-1h3s0-1 1-1l-1-1h1z" class="M"></path><path d="M281 583h0c2 0 2-2 5-1h0c-1 1-1 2-2 3h0-2-3l2-2z" class="Z"></path><path d="M284 576h3c1 1 1 1 1 3-1 1-1 1-2 1-2 1-3 0-5 2 0-1-1-1-2-2h4c1-1 2-2 2-3l-1-1z" class="J"></path><path d="M257 589c1-3 2-5 4-7 1 1 1 2 2 3-1 2-3 2-3 4l2-1c0 1 0 1 1 1 0 1-1 1-1 2l-5-2z" class="N"></path><path d="M283 576h1l1 1c0 1-1 2-2 3h-4c-2 1-4 1-5 2l-1-1v-1c1-1 4-1 5-2h1v-1h1c1 0 2 0 3-1z" class="c"></path><path d="M275 577c3-1 5-1 8-1-1 1-2 1-3 1h-1v1h-1c-1 1-4 1-5 2v1l1 1-3 2c-1-1-2-1-2-1h-1c-1 0-3 0-3-1 0 0 1-2 2-2s2-1 3-2 3-1 5-1z" class="J"></path><path d="M270 578c1-1 3-1 5-1-3 2-6 3-8 5h-2 0s1-2 2-2 2-1 3-2z" class="O"></path><path d="M282 573h4s1 0 2 1h0l-2 1c3 0 6-1 9-1v-1h-2-1 10-3v1h2-6c1 0 1 1 2 1h1v1s-2 1-3 1c0 1 0 2-1 2l-1 1c-1 0-2 1-2 1-1 1-2 1-3 2h0 0v-1c1-2 2-2 3-2v-3c-1-1-3-1-4-1h-3-1c-3 0-5 0-8 1-2 0-4 0-5 1h-1l1-1v-1c1-1 6-1 8-1h3s0-1 1-1l-1-1h1z" class="d"></path><path d="M282 573h4s1 0 2 1h0l-2 1h-5s0-1 1-1l-1-1h1z" class="K"></path><path d="M291 580l1-1c2 0 2-3 4-3h1c-2 0-5 0-6-1h-2 2c1 0 2-1 4-1 1 0 1 1 2 1h1v1s-2 1-3 1c0 1 0 2-1 2l-1 1c-1 0-2 1-2 1-1 1-2 1-3 2h0 0v-1c1-2 2-2 3-2z" class="T"></path><path d="M274 584c1-1 2-1 4-1v1c1-1 2-1 3-1l-2 2v1h-1-1 0l1 1c-1 0-2 1-3 2s-1 1-2 1-2 0-3 1c0 1-1 1-2 2l-6-2c0-1 1-1 1-2h0c3-3 7-5 11-5z" class="W"></path><path d="M263 589h1c1 0 2 0 3-1l2-1h0c0 2-3 3-2 4 0 0 1 0 1-1 1 0 2 0 2 1s-1 1-2 2l-6-2c0-1 1-1 1-2z" class="i"></path><path d="M274 584c1-1 2-1 4-1v1c1-1 2-1 3-1l-2 2v1h-1-1 0l1 1c-1 0-2 1-3 2s-1 1-2 1c0-1 1-1 1-2h-2l-1-1c0-1 2-2 3-3h0z" class="h"></path><path d="M278 584c1-1 2-1 3-1l-2 2v1h-1-1 0l-2 1v-1c1-1 2-1 3-2z" class="o"></path><path d="M279 585h3 2-1c1 1 2 1 2 1 2 0 2 0 3 1h0l-1 1 1 1-2 2h0v2c-1 1-2 1-3 2 0-1-1-1-1-1h-3s-1 0-1 1l-1 1-5-1c-1 0-3-1-4-2 1-1 2-1 2-2 1-1 2-1 3-1s1 0 2-1 2-2 3-2l-1-1h0 1 1v-1z" class="e"></path><path d="M287 588l1 1-2 2h0v2c-1 1-2 1-3 2 0-1-1-1-1-1h-3c2-1 4-2 5-4 1-1 2-1 3-2z" class="T"></path><path d="M276 589c1 0 1 0 2 1-2 1-4 2-5 4l-1 1c-1 0-3-1-4-2 1-1 2-1 2-2 1-1 2-1 3-1s1 0 2-1h1z" class="a"></path><path d="M279 585h3 2-1c1 1 2 1 2 1-2 2-4 3-7 4-1-1-1-1-2-1h-1c1-1 2-2 3-2l-1-1h0 1 1v-1z" class="N"></path><path d="M279 585h3 2-1l-7 4h-1c1-1 2-2 3-2l-1-1h0 1 1v-1z" class="l"></path><defs><linearGradient id="Ad" x1="440.031" y1="498.022" x2="461.576" y2="505.176" xlink:href="#B"><stop offset="0" stop-color="#c6c4c5"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#Ad)" d="M449 460c1 0 2-1 2-1 1 0 2 1 3 1l1-1v1l1-1 1 2 4 12c4 19 1 37-5 55l-3 7s-1 1-1 2c-1 2-1 2-3 3 1 1 2 1 3 1l2-1c-1 1-1 2-2 3h-1v-1h0c-1-1-2-1-3-2l-2 2h0-1l-1-1c1-1 1-2 1-3h0v-2l1-1c0 1 0 1 1 1 1-3 2-5 3-8 6-18 8-35 4-53 0-2-1-4-1-6-1-2-4-4-4-6v-3z"></path><path d="M456 459l1 2 4 12-1 2c-1-3-1-5-2-8-1-2-3-4-4-7h0l1-1v1l1-1z" class="M"></path><path d="M461 473c4 19 1 37-5 55l-3 7s-1 1-1 2l-3 3v-1c0-1 1-2 1-3l5-9c1-2 2-4 2-6 4-12 6-23 5-35l-1-7c0-2-1-3-1-4h0l1-2z" class="L"></path><path d="M330 572c3 0 6-1 8-1h0 0v1h4c-1 1-3 1-4 2 3 0 5 0 8-1l8-2h1c-1 1-2 1-3 1-3 1-5 2-7 3h0c-1 1-3 2-4 4h-1l-4 4h-1v1l-2 3v-2l-2 1v-1h-3c-1 1-1 2-2 3h1v1l-2 1v1l3-2h0v1 1h1 0-1v1c-2 1-4 3-5 6v3h-1c-6 1-13 1-20 0v-1c-1-1-1-3-1-4v-2c1-1 3-2 4-3h2l-1-1h-1c1 0 1-1 1-1 1-1 1-2 2-3 0-2-1-2-2-3 1-1 4-3 5-4l2-1 1 1 7-3c-2 0-2 0-3-1 1-1 4-1 5-2h2c2 0 3 0 5-1z" class="W"></path><path d="M330 572h2v1c-1 1-4 3-5 3l-5 2c1-1 1-1 0-2h-1c-2 0-2 0-3-1 1-1 4-1 5-2h2c2 0 3 0 5-1z" class="X"></path><path d="M322 576c2-1 3-1 4-2l1 1v1l-5 2c1-1 1-1 0-2z" class="M"></path><path d="M311 588h2c1-1 2-3 3-4 6-4 12-8 18-10l-3 3h0s1 1 2 1l-5 2c1 0 1 1 1 1-2 1-4 2-5 3-2 1-3 2-4 3l-2 1-1-1h0l-1 1h0-3l-1 1c-1 1 0 2-1 3v1h0v-1c-1-1-1-2-1-4h1z" class="g"></path><path d="M317 587c2-3 8-6 11-7 1 0 1 1 1 1-2 1-4 2-5 3-2 1-3 2-4 3l-2 1-1-1z" class="b"></path><path d="M322 576c1 1 1 1 0 2-1 0-2 1-3 2h-1c-3 3-6 5-7 8h-1c-1 2-1 2-3 3l-1-1h-1c1 0 1-1 1-1 1-1 1-2 2-3 0-2-1-2-2-3 1-1 4-3 5-4l2-1 1 1 7-3h1z" class="J"></path><path d="M313 578l1 1-2 1c-2 2-3 4-4 6 0-2-1-2-2-3 1-1 4-3 5-4l2-1z" class="W"></path><defs><linearGradient id="Ae" x1="321.605" y1="577.257" x2="311.661" y2="581.212" xlink:href="#B"><stop offset="0" stop-color="#888789"></stop><stop offset="1" stop-color="#a5a3a4"></stop></linearGradient></defs><path fill="url(#Ae)" d="M322 576c1 1 1 1 0 2-1 0-2 1-3 2h-1c-1 0-2 1-2 1h-1c-2 1-4 4-6 5 1-1 1-1 1-2 1-1 1-2 1-3l1-1 2-1 7-3h1z"></path><path d="M354 571h1c-1 1-2 1-3 1-3 1-5 2-7 3h0c-1 1-3 2-4 4h-1l-4 4h-1v1l-2 3v-2l-2 1v-1h-3-1l1-1h0c-1 0-2 0-2 1h-1-2l1-1c1-1 3-2 5-3 0 0 0-1-1-1l5-2c2-1 3-3 5-4 3 0 5 0 8-1l8-2z" class="j"></path><path d="M335 583v1l-2 3v-2l-2 1v-1c1-1 3-1 4-2zm1 0v-1-1-1c1-1 2-1 4-1l-4 4z" class="i"></path><path d="M354 571h1c-1 1-2 1-3 1-3 1-5 2-7 3l-16 6s0-1-1-1l5-2c2-1 3-3 5-4 3 0 5 0 8-1l8-2z" class="N"></path><path d="M320 587c1-1 2-2 4-3l-1 1h2 1c0-1 1-1 2-1h0l-1 1h1c-1 1-1 2-2 3h1v1l-2 1v1l3-2h0v1 1h1 0-1v1c-2 1-4 3-5 6v3h-1c-6 1-13 1-20 0v-1c-1-1-1-3-1-4v-2c1-1 3-2 4-3h2c2-1 2-1 3-3 0 2 0 3 1 4v1h0v-1c1-1 0-2 1-3l1-1h3 0l1-1h0l1 1 2-1z" class="p"></path><path d="M306 596h0l1 1-1 1h-1v-1l1-1z" class="k"></path><path d="M312 597h1v1 1l-3-1h-1 0v-1h3zm2-4s1-1 2-1c0 1 0 1 1 2l-1 1c-1 0-2 0-3-1l1-1z" class="e"></path><path d="M305 591c1 0 2 1 3 1-1 1-2 1-3 1-1 1-3 1-4 1 1-1 3-2 4-3z" class="g"></path><path d="M310 588c0 2 0 3 1 4v1h-1l-2-1c-1 0-2-1-3-1h2c2-1 2-1 3-3z" class="Y"></path><path d="M316 588h0v2c0 1 1 1 1 2s0 1 1 2h0v-1c1-1 1-1 2-1v1h3 0c-1 2-1 3-2 4h0c-1-1-1-1-1-2h-1-1c-1 0-1 0-1-1-1-1-1-1-1-2-1 0-2 1-2 1l2-5z" class="i"></path><path d="M320 587c1-1 2-2 4-3l-1 1h2 1c0-1 1-1 2-1h0l-1 1h1c-1 1-1 2-2 3h1v1l-2 1v1c-1 0-1 1-1 2h-1 0-3v-1c-1 0-1 0-2 1v1h0c-1-1-1-1-1-2s-1-1-1-2v-2l1-1h0l1 1 2-1z" class="T"></path><g class="d"><path d="M326 588h1v1l-2 1h-1c0-1 1-2 1-3l1 1z"></path><path d="M320 587c1-1 2-2 4-3l-1 1h2 1c0-1 1-1 2-1h0l-1 1h1c-1 1-1 2-2 3l-1-1c0 1-1 1-1 1-1 0-2 2-3 1-1 1-1 1-2 1l-2-3h0l1 1 2-1z"></path></g><path d="M320 587c1-1 2-2 4-3l-1 1h2c-1 1-3 2-5 2h0zm6-2c0-1 1-1 2-1h0l-1 1h1c-1 1-1 2-2 3l-1-1c0 1-1 1-1 1-1 0-2 2-3 1 2-1 3-2 5-4h0z" class="Y"></path><path d="M367 566v1c0 2 2 5 4 7h0l6-1c0 2-1 2-1 4-1-1-2 0-3 0v1h0c0 1-1 1-1 2-1 0-2 1-2 2v1h0c1 1 0 2 1 3 0 1 0 2 1 3v1c-3 3-8 4-12 5l-10 3-6 1c-1 0-2 0-3 1h-5-1l-2 1h-11 1v-3c1-3 3-5 5-6v-1h1 0-1v-1-1h0l-3 2v-1l2-1v-1h-1c1-1 1-2 2-3h3v1l2-1v2l2-3v-1h1l4-4h1c1-2 3-3 4-4h0c2-1 4-2 7-3 1 0 2 0 3-1h-1l5-2 7-2 1-1z" class="p"></path><path d="M335 593h2 2c-1 2-1 3-2 4l1 2 3 1h-5 1c0-2-1-2-2-4v-3z" class="V"></path><path d="M350 598c1-2 3-3 5-4 3-2 6-7 8-11 0 2-1 3-1 5-1 0-2 2-2 2 0 1 1 1 1 1-1 2-3 3-3 4h2l-10 3z" class="f"></path><path d="M354 582h1l-2 2 1 1h-1c0 1 0 2 1 3v1l-1 1 2 1-3 2c-4 1-8 3-12 3 1 0 1-1 1-1 2-2 4-3 7-4h1l2-2c1 0 1-1 2-2v-1c-2 2-4 2-6 3-1 0-1 0-2 1h0v-1h0-4c0 1-1 0-1 0h0c1-2 2-4 4-5l1 1c1-1 1-2 2-2v2h1c1-2 4-2 6-3z" class="d"></path><path d="M347 592c2 0 3-1 4-2h1v2c-2 0-3 1-5 1v-1z" class="k"></path><path d="M345 585c1-1 1-2 2-2v2c-1 1-2 1-2 2l-1 1h0c-1-1-1-2 0-3h1z" class="g"></path><g class="e"><path d="M347 592v1c2 0 3-1 5-1v1c-4 1-8 3-12 3 1 0 1-1 1-1 2 0 4-2 6-3zm7-10h1l-2 2 1 1h-1-1l-3 2c-1 0-1 1-2 0 0-1 0-1 1-2 1-2 4-2 6-3z"></path><path d="M343 579c1-1 3-2 4-2 0 1-1 1-2 1h1 1 1l-1 1h-1l-1 1c0 1 1 1 1 2l-2 2h0c-2 1-3 3-4 5h0s1 1 1 0h4 0v1c-3 0-4 1-6 3h0-2-2v3c1 2 2 2 2 4h-1-1l-2 1h-11 1v-3c1-3 3-5 5-6v-1h1 0-1v-1-1h0l-3 2v-1l2-1v-1h-1c1-1 1-2 2-3h3v1l2-1v2l2-3v-1h1l4-4h1 2z"></path></g><path d="M331 585v1c-1 1-2 2-4 3v-1h-1c1-1 1-2 2-3h3z" class="W"></path><path d="M335 584h2c-1 1-2 1-2 2v1 1c-1 1-1 2-2 3-1 2 0 5 0 7 0-1-1-1-1-2-1-3 0-6 1-9l2-3z" class="o"></path><path d="M335 588c1 0 2-1 3-2 0 1-1 4-1 4-1 1-1 2-2 3v3c1 2 2 2 2 4h-1-1 0c-1-1-1-2-2-2 0-2-1-5 0-7 1-1 1-2 2-3z" class="G"></path><path d="M335 588c1 0 2-1 3-2 0 1-1 4-1 4-1 1-1 2-2 3v3-4h-1v1h-1v-2c1-1 1-2 2-3z" class="c"></path><defs><linearGradient id="Af" x1="339.497" y1="579.107" x2="340.184" y2="585.979" xlink:href="#B"><stop offset="0" stop-color="#797979"></stop><stop offset="1" stop-color="#918f91"></stop></linearGradient></defs><path fill="url(#Af)" d="M343 579c1-1 3-2 4-2 0 1-1 1-2 1h1 1 1l-1 1h-1c-1 0-1 0-2 1l-3 3-3 3c-1 1-2 2-3 2v-1-1c0-1 1-1 2-2h-2v-1h1l4-4h1 2z"></path><path d="M341 579h2c-2 1-4 4-6 5h-2v-1h1l4-4h1z" class="l"></path><path d="M328 592h1c0 1-1 3-1 4s0 1 1 1 1 1 2 1c0 2 0 2 2 3h-11 1v-3c1-3 3-5 5-6z" class="g"></path><path d="M344 580c1-1 1-1 2-1l-1 1c0 1 1 1 1 2l-2 2h0c-2 1-3 3-4 5h0s1 1 1 0h4 0v1c-3 0-4 1-6 3h0-2-2c1-1 1-2 2-3 0 0 1-3 1-4l3-3 3-3z" class="P"></path><path d="M337 590l1 1v1l-1 1h-2c1-1 1-2 2-3z" class="D"></path><path d="M344 580c1-1 1-1 2-1l-1 1c0 1 1 1 1 2l-2 2h0c-2 1-3 3-4 5 0-1-1-2-1-2 1-2 2-2 2-4l3-3z" class="G"></path><path d="M344 580c1-1 1-1 2-1l-1 1c0 1 1 1 1 2l-2 2v-1-1-2z" class="J"></path><path d="M367 566v1c0 2 2 5 4 7h0l6-1c0 2-1 2-1 4-1-1-2 0-3 0v1h0c0 1-1 1-1 2-1 0-2 1-2 2v1h0c1 1 0 2 1 3 0 1 0 2 1 3v1c-3 3-8 4-12 5h-2c0-1 2-2 3-4 0 0-1 0-1-1 0 0 1-2 2-2 0-2 1-3 1-5v-1c0-1 0-3-1-4-1 3-2 6-4 9-1 1-2 3-3 4l-2-1 1-1v-1c-1-1-1-2-1-3h1l-1-1 2-2h-1c-2 1-5 1-6 3h-1v-2c-1 0-1 1-2 2l-1-1h0l2-2c0-1-1-1-1-2l1-1h1l1-1h-1-1-1c1 0 2 0 2-1-1 0-3 1-4 2h-2c1-2 3-3 4-4h0c2-1 4-2 7-3 1 0 2 0 3-1h-1l5-2 7-2 1-1z" class="f"></path><path d="M345 575l8-1c-1 1-2 1-3 1-1 1-1 1-2 1l-1 1c-1 0-3 1-4 2h-2c1-2 3-3 4-4z" class="b"></path><path d="M365 583l1 1h1c-1 2-1 5-2 6v1c1-1 2-2 2-3 1 0 1 0 1 1h4v1c-3 3-8 4-12 5h-2c0-1 2-2 3-4 2-2 3-5 4-8z" class="e"></path><path d="M353 574c1-1 4-2 5-1h0l-1 2c0 1-1 1-2 2l2 1h-2v1h0v2l-1 1c-2 1-5 1-6 3h-1v-2c-1 0-1 1-2 2l-1-1h0l2-2c0-1-1-1-1-2l1-1h1l1-1h-1-1-1c1 0 2 0 2-1l1-1c1 0 1 0 2-1 1 0 2 0 3-1h0z" class="b"></path><path d="M353 574h1v1h2v1c-1 0-2 1-3 1-1 1-2 3-4 3l-3 2c0-1-1-1-1-2l1-1h1l1-1h-1-1-1c1 0 2 0 2-1l1-1c1 0 1 0 2-1 1 0 2 0 3-1h0z" class="N"></path><path d="M348 576h3c0 1-2 2-2 3v1l-3 2c0-1-1-1-1-2l1-1h1l1-1h-1-1-1c1 0 2 0 2-1l1-1z" class="O"></path><path d="M360 578c1 0 1 0 1-1l-2-2c0-1 0-1 1-2 2 0 4-1 5 0l-1 2h1l-1 1h1v7c-1 3-2 6-4 8 0 0-1 0-1-1 0 0 1-2 2-2 0-2 1-3 1-5v-1c0-1 0-3-1-4-1 3-2 6-4 9-1 1-2 3-3 4l-2-1 1-1v-1c-1-1-1-2-1-3h1l-1-1 2-2h-1l1-1v-2h0v-1h2 0c1 0 1 0 2-1h1 0v1z" class="d"></path><path d="M357 578h0c1 0 1 0 2-1h1 0v1h0c-2 1-3 2-4 3-1-1 0-2-1-3h2z" class="Y"></path><path d="M354 585h2c1 0 1-1 1-2h1v4c-1 1-2 3-3 4l-2-1 1-1v-1c-1-1-1-2-1-3h1z" class="b"></path><defs><linearGradient id="Ag" x1="390.441" y1="423.999" x2="388.25" y2="409.703" xlink:href="#B"><stop offset="0" stop-color="#acaaab"></stop><stop offset="1" stop-color="#f0eff0"></stop></linearGradient></defs><path fill="url(#Ag)" d="M389 404c7-2 13-2 20-1l-3 1h0v1h-1c-2 0-4 0-6 1-2 0-3 0-5 1v1h10 3l1 1h4c1 0 2 1 4 1-1-1-1 0-2-1 4 0 9 2 11 1l6 1 3 3c3 1 5 2 7 4h1l1 1c2 0 5 0 7 1-4 0-9-1-13 1h-1l-1 1c-2-1-4-1-6-2-5-1-11-1-16-2-3-1-8 1-11-1h-3c-1-1-3-1-4-1l-8 1-6 2h-4v1c-11 2-21 8-30 14-2 2-5 4-7 7l-1 2-1 2 1 1c1 0 3-2 4-3v1c-1 1-4 4-6 4h-2c-1 0-3-1-4-2-1-2 0-6 0-8l-1-1v-3c1-1 4-3 4-4 1 0 0-1 0-2 1 0 1-1 2-1s2-1 3-2h1l2-1v3c2-2 5-4 9-5l1-2c1 0 2-1 2-1 2-1 3-2 4-3l1 1c3 0 6-2 8-3 1 0 2-1 2-1l20-9z"></path><path d="M381 417c4-2 9-2 13-3-1 1-2 1-3 2-1 0-3 0-4 1l-6 2h-4c1-1 3-1 4-2z" class="P"></path><path d="M416 410c-1-1-1 0-2-1 4 0 9 2 11 1l6 1 3 3c-6-1-12-3-18-4z" class="b"></path><path d="M393 409c2-1 5-1 7-1l1 1c0 1 0 1 1 1-3 1-6 1-9 1l-9 2c-1 0-2 1-3 1h0c5-3 11-2 16-4l-4-1z" class="C"></path><path d="M358 416l1 1c3 0 6-2 8-3 1 0 2-1 2-1l2 1c-3 1-7 2-10 3-3 2-7 4-10 5l1-2c1 0 2-1 2-1 2-1 3-2 4-3z" class="K"></path><path d="M342 424v3c-5 4-8 6-11 11l-1-1v-3c1-1 4-3 4-4 1 0 0-1 0-2 1 0 1-1 2-1s2-1 3-2h1l2-1z" class="T"></path><path d="M389 406c2 0 4 0 6-1h1 1l-3 1v1 1h10 3l1 1h4c-2 1-3 1-5 1h-5c-1 0-1 0-1-1l-1-1c-2 0-5 0-7 1-3 1-6 1-9 1h-5l10-4z" class="H"></path><path d="M389 404c7-2 13-2 20-1l-3 1h0v1h-1c-2 0-4 0-6 1-2 0-3 0-5 1v-1l3-1h-1-1c-2 1-4 1-6 1l-10 4-8 4-2-1 20-9z" class="T"></path><path d="M389 406c4-1 9-2 13-2 1 0 2 0 3 1-2 0-4 0-6 1-2 0-3 0-5 1v-1l3-1h-1-1c-2 1-4 1-6 1z" class="n"></path><path d="M355 424h0c-2 1-5 2-7 3s-4 3-5 4c-2 3-4 5-5 8 0 1-1 3-1 5l1 1 1 1c1 0 3-2 4-3v1c-1 1-4 4-6 4h1v-1h-1-1c-1-1-1-1-2-1h0c-1 0-1-1-2-1v-1c0-2 0-4 1-5 4-9 14-12 22-15z" class="Y"></path><path d="M332 445l1-1h0c0-3 3-7 5-9v1c-1 1-2 3-3 4v4h2l1 1 1 1c1 0 3-2 4-3v1c-1 1-4 4-6 4h1v-1h-1-1c-1-1-1-1-2-1h0c-1 0-1-1-2-1z" class="N"></path><defs><linearGradient id="Ah" x1="340.532" y1="426.677" x2="375.007" y2="432.886" xlink:href="#B"><stop offset="0" stop-color="#a9a7a8"></stop><stop offset="1" stop-color="#c6c6c7"></stop></linearGradient></defs><path fill="url(#Ah)" d="M365 420c1-1 11-3 13-3 1-1 2 0 3 0-1 1-3 1-4 2v1c-11 2-21 8-30 14-2 2-5 4-7 7l-1 2-1 2-1-1c0-2 1-4 1-5 1-3 3-5 5-8 1-1 3-3 5-4s5-2 7-3h0c2-2 7-3 10-4z"></path><path d="M343 431c1 1 2 0 3 0l-6 6c0 1-1 1-2 2 1-3 3-5 5-8z" class="K"></path><path d="M365 420c-1 1-4 3-5 3-2 1-3 1-4 1-1 2-1 2-3 3s-5 2-7 4c-1 0-2 1-3 0 1-1 3-3 5-4s5-2 7-3h0c2-2 7-3 10-4z" class="Z"></path><defs><linearGradient id="Ai" x1="423.036" y1="425.832" x2="416.29" y2="408.192" xlink:href="#B"><stop offset="0" stop-color="#545355"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#Ai)" d="M401 413c3 0 5-1 8-1s6 1 10 1c3 1 7 2 10 3 4 1 8 2 12 2h1l1 1c2 0 5 0 7 1-4 0-9-1-13 1h-1l-1 1c-2-1-4-1-6-2-5-1-11-1-16-2-3-1-8 1-11-1h-3c-1-1-3-1-4-1l-8 1c1-1 3-1 4-1 1-1 2-1 3-2l7-1z"></path><path d="M394 414l7-1-2 1v1s-1 1-2 1h-2l-8 1c1-1 3-1 4-1 1-1 2-1 3-2z" class="J"></path><path d="M402 417c10-1 21 0 30 3 1 0 3 0 4 1h0l-1 1c-2-1-4-1-6-2-5-1-11-1-16-2-3-1-8 1-11-1z" class="K"></path><path d="M372 401c2 0 5 0 8-1l-1 1h2c2 2 3 2 5 1h1c1 0 2 0 2 1v1l-20 9s-1 1-2 1c-2 1-5 3-8 3l-1-1c-1 1-2 2-4 3 0 0-1 1-2 1l-1 2c-4 1-7 3-9 5v-3l-2 1h-1c-1 1-2 2-3 2s-1 1-2 1c0 1 1 2 0 2 0 1-3 3-4 4v3c-1 1-1 2-2 3l-2 1-3 5c0-1-1-1-1-1 0-2 1-3 2-5l-1-1c-1 3-2 5-4 7 0 1-1 1-1 1v1c-2 1-3 2-4 4l-2 2c-1 1-2 3-2 4l-3 6-1 6h0c-1-1-1-3-1-5h0v-1c-2-1-2-1-3 0l-1 3c-1 1-2 1-2 3l-1-4v-1c0-1 0-3-1-4l1-2c-1 0-1-1-2-2l2-3 1-1v1h0c1-1 1-2 2-3 1 0 1-1 1-1 3-5 5-9 8-13 1-2 2-3 3-4s2-1 3 0c1 0 2-2 3-3l8-7c0-1 2-1 3-2 0-1 0-2 1-3 4-3 9-5 14-7v-1h0c3-1 6-3 9-4 6-2 12-3 18-5z" class="U"></path><path d="M296 457l2-3c0 1 0 1 1 1h0 0c0 2-1 3-1 4-1 0-1-1-2-2z" class="Q"></path><path d="M301 459v1l-1 2c1 0 1 0 1 1 1 0 2 0 3-1l-1 1h-1v1l-1 3c-1 1-2 1-2 3l-1-4 1-4c1-1 1-2 2-3z" class="K"></path><path d="M299 453v1h0c1-1 1-2 2-3 1 0 1-1 1-1 3-5 5-9 8-13 1-2 2-3 3-4s2-1 3 0c-5 5-10 10-13 17-1 2-3 4-4 5h0 0c-1 0-1 0-1-1l1-1z" class="D"></path><path d="M359 409h0l-9 6v1l-2 1h-1c-1 0-2 1-2 1-2 1-3 1-5 2l-1 1c-1 0-1-1-1-1 7-4 13-8 21-11z" class="S"></path><path d="M329 430l1 1c-1 3-6 6-7 8-1 3-2 5-4 7 0 1-1 1-1 1h-1v-1c0-4 4-9 7-12l5-4z" class="Y"></path><path d="M364 404c2-1 5-1 7-1h1l-1 3c-4 1-7 3-11 5l-6 3-4 2v-1l9-6h0c1-1 4-2 6-3 0 0 1 0 1-1h2 1v1c0-1 1-2 2-3-3 1-5 2-7 1h0z" class="F"></path><path d="M338 420s0 1 1 1l1-1c2-1 3-1 5-2 0 0 1-1 2-1h1l-8 5c-2 2-5 3-7 5-1 1-4 2-4 3l-5 4c0-1 1-1 1-2v-1l1-1c5-3 8-7 12-10z" class="P"></path><path d="M301 459c-1-2 2-4 3-6s3-5 5-7c0-1 1-2 2-3h1l1 1c-2 1-2 3-3 4l-2 5c-2 1-4 5-4 8v1c-1 1-2 1-3 1 0-1 0-1-1-1l1-2v-1z" class="B"></path><path d="M333 427h3c-1 0-1 1-2 1 0 1 1 2 0 2 0 1-3 3-4 4v3c-1 1-1 2-2 3l-2 1-3 5c0-1-1-1-1-1 0-2 1-3 2-5l-1-1c1-2 6-5 7-8l-1-1c0-1 3-2 4-3z" class="b"></path><path d="M330 434v3c-1 1-1 2-2 3l-2 1c1-3 2-5 4-7z" class="d"></path><path d="M313 444c0 1 1 1 2 2h2v1h1v1c-2 1-3 2-4 4l-2 2c-1 1-2 3-2 4l-3 6-1 6h0c-1-1-1-3-1-5h0v-1c-2-1-2-1-3 0v-1h1l1-1v-1c0-3 2-7 4-8l2-5c1-1 1-3 3-4z" class="g"></path><path d="M310 448l2 1-1 1 1 1h-1 0c-1 1-1 2-1 3 0 0-2 1-2 2v-3l2-5z" class="f"></path><path d="M308 453v3c-2 2-2 4-4 6v-1c0-3 2-7 4-8z" class="G"></path><path d="M313 444c0 1 1 1 2 2h2v1c-2 0-3 1-4 2l-1 2-1-1 1-1-2-1c1-1 1-3 3-4z" class="a"></path><defs><linearGradient id="Aj" x1="352.99" y1="424.268" x2="363.779" y2="403.373" xlink:href="#B"><stop offset="0" stop-color="#2e3030"></stop><stop offset="1" stop-color="#575456"></stop></linearGradient></defs><path fill="url(#Aj)" d="M375 404h1l-9 7c-3 1-6 3-9 5-1 1-2 2-4 3 0 0-1 1-2 1l-1 2c-4 1-7 3-9 5v-3l-2 1h-1c-1 1-2 2-3 2h-3c2-2 5-3 7-5l8-5 2-1 4-2 6-3c4-2 7-4 11-5 1-1 3-1 4-2z"></path><path d="M342 424c3-1 7-4 10-4l-1 2c-4 1-7 3-9 5v-3z" class="d"></path><path d="M354 414c-1 3-4 4-7 6l-8 5c-1 1-2 2-3 2h-3c2-2 5-3 7-5l8-5 2-1 4-2z" class="N"></path><path d="M372 401c2 0 5 0 8-1l-1 1h2c2 2 3 2 5 1h1c1 0 2 0 2 1v1l-20 9s-1 1-2 1c-2 1-5 3-8 3l-1-1c3-2 6-4 9-5l9-7h-1c-1 1-3 1-4 2l1-3h-1c-2 0-5 0-7 1-6 1-13 4-19 6h0c3-1 6-3 9-4 6-2 12-3 18-5z" class="V"></path><path d="M371 403l5-1 1 1h-1l-1 1c-1 1-3 1-4 2l1-3h-1zm5 1l5-3c-3 4-8 9-13 10h-1l9-7z" class="H"></path><path d="M384 545c0-2 2-4 4-5 4-3 10-2 15-1s8 5 11 9c2 3 4 10 3 14-1 1-1 1-1 2h1c1-1 2-1 2-1l-2 2c1 1 1 2 1 3h-1l1 1c1-1 2-2 2-3l3-2c-1 2-1 3-2 5-2 2-5 4-6 7h0 2v-1l-1-1c2-1 3-2 5-3h0s-1 1-1 2h0c-1 2-2 3-2 4l-15 15-19 15c-6 4-13 9-21 10-2 0-4 0-5 1-2 1-4 1-6 2h-2 0c0 1-1 1-2 0 0 0-1 0-1-1v-2h2c-1-1-2 0-2-1l-1 1h0v3l1 1h-1v-1l-1-1v-3c-1-1-2-2-3-2 2-1 4-1 5-1l3-1h1l2-2c1-1 3-1 4-1h0c3 0 4-1 6-2 1-1 3-2 4-4h-1l1-2v-2l1-1 2-1c1-1 2-1 2-2l1-1v-1c1-2 1-3 1-5v-1c2 1 2 3 3 4v-5-1h0l1 2c1 0 2 1 3 1 0 0-1-1 0-2h0c0-1-1-3-1-4-1-1-1-1-1-2-2-1-3 0-4 0-2 1-4 2-4 4v2c-1-1 0-2-1-3h0v-1c0-1 1-2 2-2 0-1 1-1 1-2h0v-1c1 0 2-1 3 0 0-2 1-2 1-4l-6 1h0c-2-2-4-5-4-7v-1c0-2 0-5 2-6 2-3 6-5 9-6 5 0 13 0 17 3l-1-1s-1-2-2-3l-2-2-6-3v-1c-1-1-1-1 0-2z" class="R"></path><path d="M389 586v3 1c-1 2-2 4-4 4v1l3-7 1-2z" class="U"></path><path d="M384 582c2 0 4-1 5-1 0 0 1-1 1-2v2h1 0v2h0c-1 1-1 2-2 3l-1 2c0-1-1-2-1-2-1-1-2-2-3-2v-2h0z" class="E"></path><path d="M387 586c2-1 1-1 1-2 1-1 1-1 2-1h1c-1 1-1 2-2 3l-1 2c0-1-1-2-1-2z" class="I"></path><path d="M367 567h0c4 1 7 3 11 4 1-1 0-2 1-3v4c-1 1-1 1-2 1l-6 1h0c-2-2-4-5-4-7z" class="F"></path><path d="M381 581h0l2 2c1 0 1 0 1-1v2c1 0 2 1 3 2 0 0 1 1 1 2l-3 7c-2 3-5 5-8 7 1-3 3-5 4-8h0c1-2 1-4 1-6 0-3 0-5-1-7z" class="L"></path><path d="M381 581h0l2 2c1 0 1 0 1-1v2 2c1 0 2 1 2 1 1 1-1 5-2 6 0 1-1 1-2 2h0v-2c1-1 0-4 0-5 0-3 0-5-1-7z" class="G"></path><path d="M379 580l2 1c1 2 1 4 1 7 0 2 0 4-1 6h0c-1 3-3 5-4 8-2 3-5 5-8 5 1-1 2-3 3-4l3-2-2-2c2-2 4-5 4-8v-5-1h0l1 2c1 0 2 1 3 1 0 0-1-1 0-2h0c0-1-1-3-1-4-1-1-1-1-1-2z" class="F"></path><path d="M377 585l1 2c1 3 1 8-1 11h0c-1 1-2 2-2 3l-2-2c2-2 4-5 4-8v-5-1h0z" class="L"></path><path d="M374 588v-1c2 1 2 3 3 4 0 3-2 6-4 8l2 2-3 2c-1 1-2 3-3 4-6 3-13 5-19 5h1l2-2c1-1 3-1 4-1h0c3 0 4-1 6-2 1-1 3-2 4-4h-1l1-2v-2l1-1 2-1c1-1 2-1 2-2l1-1v-1c1-2 1-3 1-5z" class="H"></path><path d="M373 599l2 2-3 2h0v-1h0c0-1 1-2 1-3z" class="D"></path><path d="M373 594c1 1 1 1 1 2l-1 2c0 1-2 4-4 5 0 0-1 0-1 1s-1 1-2 2-2 1-3 1c1-1 3-2 4-4h-1l1-2v-2l1-1 2-1c1-1 2-1 2-2l1-1z" class="L"></path><path d="M368 598l2-1c0 2 0 2-1 4h-2v-2l1-1z" class="H"></path><path d="M379 568c1-2 2-3 5-4 2 0 4 0 6 1 2 2 4 5 4 7v1 1c1 1 1 3 1 4l-1 1c0-1 0-1-1-2h-1-2v1 1c0 1-1 2-1 2-1 0-3 1-5 1h0c0 1 0 1-1 1l-2-2h0l-2-1c-2-1-3 0-4 0-2 1-4 2-4 4v2c-1-1 0-2-1-3h0v-1c0-1 1-2 2-2 0-1 1-1 1-2h0v-1c1 0 2-1 3 0 0-2 1-2 1-4 1 0 1 0 2-1v-4z" class="b"></path><path d="M384 574c-1-1-1-2-1-2l-1-1v-1c0-1 2-2 2-3h2 2c2 1 2 2 3 3l-5 1h0 0c1 2 1 2 1 3h-3z" class="N"></path><path d="M387 574c0-1 0-1-1-3h0 0l5-1 3 3v1c1 1 1 3 1 4l-1 1c0-1 0-1-1-2h-1-2v1-1c-1-2-2-2-3-3z" class="E"></path><path d="M384 574h3c1 1 2 1 3 3v1 1c0 1-1 2-1 2-1 0-3 1-5 1l-1-1v-4c0-1-1-1-1-2s1-1 2-1z" class="e"></path><path d="M376 577h1 1c1 0 2-1 4-2 0 1 1 1 1 2v4l1 1h0c0 1 0 1-1 1l-2-2h0l-2-1c-2-1-3 0-4 0-2 1-4 2-4 4v2c-1-1 0-2-1-3h0v-1c0-1 1-2 2-2 0-1 1-1 1-2h0v-1c1 0 2-1 3 0z" class="W"></path><defs><linearGradient id="Ak" x1="390.734" y1="572.719" x2="406.237" y2="570.862" xlink:href="#B"><stop offset="0" stop-color="#c9c8c8"></stop><stop offset="1" stop-color="#f5f5f5"></stop></linearGradient></defs><path fill="url(#Ak)" d="M392 553c1-1 1-1 1-3h0 2c2 1 4 1 6 1 1 0 2 1 2 2l2 4c1-1 1-1 1-2h0v-1h1c2 5 2 13 1 18-2 6-5 15-10 19h-1c-1 1-2 2-4 3-2 2-3 3-6 4l3-4c4-7 7-14 6-22 2 2 3 4 3 7h1v-1c2-3 1-10 0-13-1-1-2-3-3-4 0-2-2-2-2-4l-1-1s-1-2-2-3z"></path><path d="M400 585c-1 2-3 3-4 5-1 1-2 3-3 4-2 2-3 3-6 4l3-4c3-1 7-7 10-9z" class="D"></path><path d="M400 565h1l-1-2v-1 1h1c1 1 1 2 1 3v1 6 2h0-1c0 2 0 3-1 5h-1v-1h1v-1c2-3 1-10 0-13z" class="U"></path><path d="M392 553c1-1 1-1 1-3h0 2c2 1 4 1 6 1 1 0 2 1 2 2l2 4h0l-2 1v2h0c-2-2-3-5-5-7-1-1-2 0-3 0v2s0 1-1 1c0 0-1-2-2-3z" class="n"></path><path d="M407 554c2 5 2 13 1 18-2 6-5 15-10 19h-1c-1 1-2 2-4 3 1-1 2-3 3-4 1-2 3-3 4-5l3-5c3-7 5-15 2-23h0c1-1 1-1 1-2h0v-1h1z" class="Q"></path><path d="M417 564c1-1 2-1 2-1l-2 2c1 1 1 2 1 3h-1l1 1c1-1 2-2 2-3l3-2c-1 2-1 3-2 5-2 2-5 4-6 7h0 2v-1l-1-1c2-1 3-2 5-3h0s-1 1-1 2h0c-1 2-2 3-2 4l-15 15-19 15c-6 4-13 9-21 10-2 0-4 0-5 1-2 1-4 1-6 2h-2 0c0 1-1 1-2 0 0 0-1 0-1-1v-2h2c-1-1-2 0-2-1l-1 1h0v3l1 1h-1v-1l-1-1v-3c-1-1-2-2-3-2 2-1 4-1 5-1 0 0-1 0-1 1h-1c0 1 0 1 1 1h0 0c1 1 3 0 4 0l14-3c2-1 5-1 7-2 6-3 12-8 16-12 3-1 4-2 6-4 2-1 3-2 4-3h1c5-4 8-13 10-19 0 6-4 10-6 15 1-1 2-3 3-4h0c1-1 1-1 1-2h1l-1-1 1-1 2-2v-1c0-1 0 0 1-1 0-1 1-1 2-2h0v-2c1 0 1-1 2-2v-1c1 0 1-1 1-1v-1l1-2h1z" class="B"></path><path d="M416 564h1c0 1 0 1-1 2l-1 4s-1 1-1 2c-2 4-4 6-7 9-2 3-5 7-7 10 0 1-1 1-1 1h-1 0c-3 3-6 7-9 9h-1c0 1-1 1-2 1 4-3 8-7 12-11 5-4 8-13 10-19 0 6-4 10-6 15 1-1 2-3 3-4h0c1-1 1-1 1-2h1l-1-1 1-1 2-2v-1c0-1 0 0 1-1 0-1 1-1 2-2h0v-2c1 0 1-1 2-2v-1c1 0 1-1 1-1v-1l1-2z" class="I"></path><defs><linearGradient id="Al" x1="386.091" y1="605.51" x2="376.484" y2="601.822" xlink:href="#B"><stop offset="0" stop-color="#a7a7a8"></stop><stop offset="1" stop-color="#cac8c8"></stop></linearGradient></defs><path fill="url(#Al)" d="M393 594c2-1 3-2 4-3h1c-4 4-8 8-12 11l-9 6c-4 2-8 4-12 5l-1-1c2-1 5-1 7-2 6-3 12-8 16-12 3-1 4-2 6-4z"></path><path d="M384 545c0-2 2-4 4-5 4-3 10-2 15-1s8 5 11 9c2 3 4 10 3 14-1 1-1 1-1 2l-1 2v1s0 1-1 1v1c-1 1-1 2-2 2v2h0c-1 1-2 1-2 2-1 1-1 0-1 1v1l-2 2-1 1 1 1h-1c0 1 0 1-1 2h0c-1 1-2 3-3 4 2-5 6-9 6-15 1-5 1-13-1-18h-1v1h0c0 1 0 1-1 2l-2-4c0-1-1-2-2-2-2 0-4 0-6-1h-2 0c0 2 0 2-1 3l-2-2-6-3v-1c-1-1-1-1 0-2z" class="m"></path><path d="M404 548l3 6h-1v1h0c0 1 0 1-1 2l-2-4c1-1 0-3 0-4l1-1z" class="S"></path><defs><linearGradient id="Am" x1="389.774" y1="542.906" x2="390.505" y2="547.545" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#Am)" d="M391 542c2 0 4 0 7 1 2 1 4 3 6 5l-1 1c0 1 1 3 0 4 0-1-1-2-2-2-2 0-4 0-6-1h-2 0c0 2 0 2-1 3l-2-2-6-3v-1c-1-1-1-1 0-2v1c2-2 4-3 7-4z"></path><path d="M391 542c2 0 4 0 7 1v2 1c-1-1-3-2-4-3s-2-1-3-1z" class="O"></path><path d="M384 548c1-1 2-1 3-1s2 1 3 2l1 1-1 1-6-3z" class="N"></path><path d="M398 543c2 1 4 3 6 5l-1 1c0 1 1 3 0 4 0-1-1-2-2-2-2 0-4 0-6-1h-2 0c0 2 0 2-1 3l-2-2 1-1h0v-2c2 0 5-2 8-1l1 1v-1l-2-1v-1-2z" class="Z"></path><path d="M358 398l1 1 1-1h1 2l2-1h2l1-1h3c2 0 5 1 7 1 2 1 5 1 7 1h0v1l-5 1c-3 1-6 1-8 1-6 2-12 3-18 5-3 1-6 3-9 4h0v1c-5 2-10 4-14 7-1 1-1 2-1 3-1 1-3 1-3 2l-8 7c-1 1-2 3-3 3-1-1-2-1-3 0s-2 2-3 4c-3 4-5 8-8 13 0 0 0 1-1 1-1 1-1 2-2 3h0v-1l-1 1-2 3c1 1 1 2 2 2l-1 2c1 1 1 3 1 4v1l1 4-1 4s0 1 1 1c0 1 0 2 1 3v1c-1 1-1 1-2 1v-1c-1 1-1 2-2 2h0v4l-1-1c0 1 0 1-1 1l-1-2v-3l-1 1v4c0 6 0 11 1 16 0 1 0 1 1 2 0 4 4 9 2 13 1 1 2 1 2 2h0l2 4h-1c-1-1-3-1-4-1l-3-1c-1 0-3 0-4 1v1c-2-2-4-4-5-6 0 3 1 6 3 9v1l-1 1 1 2-3-6-1 1v-1h-2v-1c-1 1-1 4 0 6v1c0 1 1 2 1 3v1c-1-1-1-2-2-3h0v1 1h-1l-3-7h-1c-1-2-1-3-1-5-1 0-2 0-2-1l-1-5c-1-3-1-7-1-9s0-3-1-4l-2-3c-1 3-1 8-1 11v3h-1c-1-1-1-2-2-2 0-2 0-4-1-5v-5h-1v-2l-2 3c-2 0-3 1-4 2l-1-1c1-1 2-1 2-2h0l-2 1-2-2 2-2c1-1 2-3 2-4 1-2 2-5 3-7v-1l1-1c1-1 1-3 1-4h-4s0-1-1-1-2-1-3-2l1-1c-1-1-1-1-1-2h2 0 1c1 0 1 0 2-1h2 0c0-1 0-1 1-1l1 1c1 0 1-1 2-1 1-3 4-5 6-8h0l4-5 6-7c-4 3-7 4-12 5-5 0-8-1-12-3h1 0c3-1 5 0 8-1h2l6-3 7-3c2-2 4-4 6-5 0-1-2 0-3-1 0-1 1-1 1-2l4-2 1-1c3-1 5-4 7-5v-2c-1 1-1 1-2 1v-1h-1-1v-1c0-1 0-2-1-3h0v-1s0-1 1-1c0-1-1-1-1-1 0-1 0-2 1-3l1 1 3-1 1-2c3-1 6-2 8-3l2-1h0c2 0 4-1 6-1v1l-1 1h2 0c1 0 2 0 3-1l3-2h2 1c6-3 12-5 18-7 3-2 7-2 10-3 2 0 3 0 4-1h0c1 0 1-1 1-1 1-1 2-1 3-1z" class="m"></path><path d="M375 398h10v1l-5 1c-3 1-6 1-8 1 0 0 1-1 2-1h2 3 0l1-1h-1c-2 1-4 0-6 0-2 1-3 1-5 0h4l3-1zm-105 74c0 3-1 7-2 10-1 1-1 3-1 4l-1 1v-4-2l3-8 1-1z" class="C"></path><path d="M304 442c0 2-1 3-2 5l-3 6-1 1-2 3-1 2-1-1c2-6 7-11 10-16z" class="J"></path><path d="M280 502c1 0 1 1 2 1h0c0-2 0-5 1-7 0 4 0 7 1 11 1 2 2 4 2 6-2-3-4-5-5-9l-1-2z" class="C"></path><path d="M350 401c2 0 3 0 4 1l-16 6v-1c2-1 5-2 7-3h-5c3-2 7-2 10-3z" class="X"></path><path d="M354 400h1c1 0 1-1 2-1h2c4 0 6-2 9-1 2 1 2 1 4 1h-4l-14 3c-1-1-2-1-4-1 2 0 3 0 4-1z" class="a"></path><path d="M358 398l1 1 1-1h1 2l2-1h2l1-1h3c2 0 5 1 7 1 2 1 5 1 7 1h0-10l-3 1c-2 0-2 0-4-1-3-1-5 1-9 1h-2c-1 0-1 1-2 1h-1 0c1 0 1-1 1-1 1-1 2-1 3-1z" class="T"></path><path d="M368 398c2-1 5 0 7 0l-3 1c-2 0-2 0-4-1z" class="W"></path><path d="M273 481c3-7 5-15 9-22 1-1 2-3 3-4-1 3-3 6-4 9-3 6-5 13-6 20l-3 7v11c-1-1-1-3-1-4 0-5 1-11 2-17z" class="P"></path><defs><linearGradient id="An" x1="271.123" y1="505.086" x2="269.011" y2="493.964" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#c8c8c8"></stop></linearGradient></defs><path fill="url(#An)" d="M268 501l1-1v-7c0-1 1-4 0-6l1-4v-1-1h1 1c0 2 0 4-1 6v2-2h-1 0v4 2 1c0-1 0-1 1-1h0v-2-1-1c1-1 1-2 1-2v-1-2c0-1 1-2 1-3h0c-1 6-2 12-2 17 0 1 0 3 1 4l-1 8-1 1v3c-1-3-1-7-1-9s0-3-1-4z"></path><path d="M281 504c1 4 3 6 5 9s4 6 7 7h0c1-1 1-1 1-2l2 2h2c0-1 0-1-1-1v-1h1l2 4h-1c-1-1-3-1-4-1l-3-1c-1 0-3 0-4 1v1c-2-2-4-4-5-6l-3-7h1v-2c-1 0 0-2 0-3z" class="V"></path><path d="M280 509h1c2 4 4 9 9 11h2c-1 0-3 0-4 1v1c-2-2-4-4-5-6l-3-7z" class="T"></path><path d="M266 481v2 4c0 3-1 6-1 9v3c-1 1 0 2 0 4h0c0-2 0-4 1-6h0v-1c0 1 1 2 0 2-1 3-1 8-1 11v3h-1c-1-1-1-2-2-2 0-2 0-4-1-5v-5h-1v-2-1h1 1 0l4-16z" class="L"></path><path d="M260 497h1 1l-1 8v-5h-1v-2-1z" class="W"></path><path d="M269 464c0 1 0 2 1 2 2 0 6-5 7-6-1 2-2 4-4 6l-3 6-1 1c-5 4-7 6-13 7 0 0 0-1-1-1s-2-1-3-2l1-1c-1-1-1-1-1-2h2 0 1c1 0 1 0 2-1h2 0c0-1 0-1 1-1l1 1c1 0 1-1 2-1 1-3 4-5 6-8h0z" class="H"></path><path d="M263 472h0c2 0 6-3 7-5-2 4-5 8-9 10v-4c1 0 1-1 2-1z" class="b"></path><path d="M259 473c0-1 0-1 1-1l1 1v4l-2 1h-3-1l-2-2c-1-1-1-1-1-2h2 0 1c1 0 1 0 2-1h2 0z" class="o"></path><path d="M254 474h1c1 0 1 0 2-1v4l-1 1c0-1-1-3-2-4z" class="l"></path><path d="M252 474h2 0c1 1 2 3 2 4h-1l-2-2c-1-1-1-1-1-2z" class="d"></path><path d="M259 473c0-1 0-1 1-1l1 1v4l-2 1v-1c-1 0-1-1 0-2l1-1-1-1z" class="W"></path><path d="M269 473l-3 8-4 16h0-1-1v1l-2 3c-2 0-3 1-4 2l-1-1c1-1 2-1 2-2h0l-2 1-2-2 2-2c1-1 2-3 2-4 1-2 2-5 3-7v-1l1-1c1-1 1-3 1-4h-4c6-1 8-3 13-7z" class="M"></path><path d="M261 495l-2 2c-1 0-2 2-4 2h-1l-1 1v-1c2-1 4-3 4-6 0-2 3-4 4-6h0 1c-1 0-1 2-1 3 1 1 0 3 0 5z" class="O"></path><path d="M269 473l-3 8-4 16h0-1-1c0-1 1-2 1-2 0-2 1-4 0-5l2-5h0c-1 1-2 1-3 2h0l-2-1v-1l1-1c1-1 1-3 1-4h-4c6-1 8-3 13-7z" class="a"></path><path d="M259 484h0 2 0c1-2 2-3 4-5l-1 3c-1 1-1 2-1 3h0c-1 1-2 1-3 2h0l-2-1v-1l1-1z" class="N"></path><defs><linearGradient id="Ao" x1="286.954" y1="490.492" x2="274.857" y2="482.188" xlink:href="#B"><stop offset="0" stop-color="#b0aead"></stop><stop offset="1" stop-color="#e8e8e8"></stop></linearGradient></defs><path fill="url(#Ao)" d="M275 500v-4h0l1-1c0-1-1-1 0-2v-1c1-1 1-3 1-4 1-7 4-14 8-21 0 1-1 3-1 5l-2 9v5l1 3v7c-1 2-1 5-1 7h0c-1 0-1-1-2-1l1 2c0 1-1 3 0 3v2h-1c-1-1-1-3-1-5l-1 1h0l-1-1v-1c-1 0-1-2-1-3h-1z"></path><path d="M281 486v-1l-1 1h0c0-2 1-6 2-8v3 5h-1z" class="C"></path><path d="M276 500v-2l1-1c2 2 0 5 1 8h0l-1-1v-1c-1 0-1-2-1-3z" class="c"></path><path d="M281 486h1l1 3v7c-1 2-1 5-1 7h0c-1 0-1-1-2-1l1-16z" class="B"></path><path d="M304 442h0c2-2 3-5 4-7l1-1h-1c-2 1-5 5-7 7l-10 14h-1c3-7 8-13 12-18 2-2 3-4 5-6 4-3 9-6 14-9l12-6c4-2 8-5 12-6h0v1c-5 2-10 4-14 7-1 1-1 2-1 3-1 1-3 1-3 2l-8 7c-1 1-2 3-3 3-1-1-2-1-3 0s-2 2-3 4c-3 4-5 8-8 13 0 0 0 1-1 1-1 1-1 2-2 3h0v-1l3-6c1-2 2-3 2-5z" class="F"></path><path d="M272 502v-11l3-7c0 3-1 6-1 10 0 2 1 4 1 7 0 1 0 1-1 2 0 1 1 3 1 4-1 1-1 2-1 3 0 2 1 3 2 5v-8c0-2-1-5-1-7h1c0 1 0 3 1 3v1l1 1h0l1-1c0 2 0 4 1 5l3 7c0 3 1 6 3 9v1l-1 1 1 2-3-6-1 1v-1h-2v-1c-1 1-1 4 0 6v1c0 1 1 2 1 3v1c-1-1-1-2-2-3h0v1 1h-1l-3-7h-1c-1-2-1-3-1-5-1 0-2 0-2-1l-1-5v-3l1-1 1-8z" class="D"></path><path d="M275 525l1-1h0c1 1 2 5 3 6v1 1h-1l-3-7z" class="C"></path><path d="M270 514v-3l1-1 2 10c-1 0-2 0-2-1l-1-5z" class="K"></path><path d="M277 503v1l1 1c0 4 1 7 2 10 0 3 2 6 3 8l-1 1v-1c-2-3-3-7-4-11-1-3-1-6-1-9z" class="G"></path><path d="M278 505l1-1c0 2 0 4 1 5l3 7c0 3 1 6 3 9v1l-1 1 1 2-3-6c-1-2-3-5-3-8-1-3-2-6-2-10h0z" class="I"></path><path d="M295 459l1-2c1 1 1 2 2 2l-1 2c1 1 1 3 1 4v1l1 4-1 4s0 1 1 1c0 1 0 2 1 3v1c-1 1-1 1-2 1v-1c-1 1-1 2-2 2h0v4l-1-1c0 1 0 1-1 1l-1-2v-3l-1 1v4c0 6 0 11 1 16 0 1 0 1 1 2 0 4 4 9 2 13 1 1 2 1 2 2h0-1v1c1 0 1 0 1 1h-2l-2-2-2-1c-1-1-2-2-3-4s-1-5-2-8c-3-14-1-27 4-40l3-7 1 1z" class="N"></path><path d="M291 510c1 1 1 2 2 2v-3c-1-2-2-5-2-7 1 0 1 2 1 3 1 4 2 8 3 11 1 1 1 2 1 3v1l-2-2-2-1v-1c1-1-1-2-1-3v-2 1l1-1h-1v-1z" class="Y"></path><path d="M295 459l1-2c1 1 1 2 2 2l-1 2c1 1 1 3 1 4v1l1 4-1 4s0 1 1 1c0 1 0 2 1 3v1c-1 1-1 1-2 1v-1c-1 1-1 2-2 2h0v4l-1-1c0 1 0 1-1 1l-1-2v-3c0-5-1-11 1-16 0-1 1-3 1-5z" class="K"></path><path d="M297 467v-2l-1-1h0c0-1 1-2 1-3 1 1 1 3 1 4v1l1 4-1 4v1l-1-1h0v-7z" class="D"></path><path d="M295 459l1-2c1 1 1 2 2 2l-1 2c0 1-1 2-1 3h0l1 1v2-1l-2 1c0-1 1-2 0-3l-1 1v-1c0-1 1-3 1-5z" class="S"></path><path d="M295 484v-1c-1-4-1-12 1-15h0c0 2 0 4 1 6h0 0l1 1v-1s0 1 1 1c0 1 0 2 1 3v1c-1 1-1 1-2 1v-1c-1 1-1 2-2 2h0v4l-1-1z" class="X"></path><path d="M297 474l1 1v-1s0 1 1 1c0 1 0 2 1 3v1c-1 1-1 1-2 1v-1s-1-1-1-2v-3z" class="K"></path><defs><linearGradient id="Ap" x1="300.632" y1="500.356" x2="279.906" y2="483.339" xlink:href="#B"><stop offset="0" stop-color="#3d3d3c"></stop><stop offset="1" stop-color="#5f5d60"></stop></linearGradient></defs><path fill="url(#Ap)" d="M291 465h1c0 1-2 2 0 3v1 1 1 1c-1 1 0 2-1 3v10c1 0 0 2 0 3v10h0c-1-2 0-5-1-7-1 2-1 4-1 6s0 4 1 7c0 1 2 6 1 6v1h1l-1 1v-1 2c0 1 2 2 1 3v1c-1-1-2-2-3-4s-1-5-2-8c-3-14-1-27 4-40z"></path><defs><linearGradient id="Aq" x1="311.65" y1="424.724" x2="308.176" y2="419.881" xlink:href="#B"><stop offset="0" stop-color="#cecdce"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#Aq)" d="M340 404h5c-2 1-5 2-7 3v1c-1 1-2 1-3 1l-7 3-12 7h-1c2-2 4-3 7-4h-1 0c-1 0-1 0-2 1-1 0-1 1-2 1-2 1-4 3-6 4l1 1h-1c-1 1-1 2-3 3h0c-2 0-3 1-4 2h0c1-1 2-1 4-2-1 2-4 4-6 5-2 2-4 3-6 5-4 2-7 4-11 7h0c0-1-2 0-3-1 0-1 1-1 1-2l4-2 1-1c3-1 5-4 7-5v-2c-1 1-1 1-2 1v-1h-1-1v-1c0-1 0-2-1-3h0v-1s0-1 1-1c0-1-1-1-1-1 0-1 0-2 1-3l1 1 3-1 1-2c3-1 6-2 8-3l2-1h0c2 0 4-1 6-1v1l-1 1h2 0c1 0 2 0 3-1l3-2h2 1c6-3 12-5 18-7z"></path><path d="M319 411h2 1c-6 4-13 7-19 12-3 2-5 5-8 8v-2l2-3c3-4 7-7 11-9 1-1 2-1 3-2h0v-1h2 0c1 0 2 0 3-1l3-2z" class="N"></path><path d="M306 413h0c2 0 4-1 6-1v1l-1 1v1h0c-1 1-2 1-3 2-4 2-8 5-11 9l-2 3c-1 1-1 1-2 1v-1h-1-1v-1c0-1 0-2-1-3h0v-1s0-1 1-1c0-1-1-1-1-1 0-1 0-2 1-3l1 1 3-1 1-2c3-1 6-2 8-3l2-1z" class="c"></path><path d="M291 419l1 1 3-1c-2 3-2 6-3 10h-1v-1c0-1 0-2-1-3h0v-1s0-1 1-1c0-1-1-1-1-1 0-1 0-2 1-3z" class="T"></path><path d="M293 429v-2c2-3 3-6 6-8 2-1 5-2 7-2 2-1 3-1 5-2h0c-1 1-2 1-3 2-4 2-8 5-11 9l-2 3c-1 1-1 1-2 1v-1z" class="j"></path><path d="M436 421h1c2 0 5 2 7 1 8 1 12 4 18 10 3 4 5 8 8 13 0 1 2 6 3 6 1 1 2 3 2 5 3 7 4 15 4 22v1c1 1 1 2 1 4 1 2 0 3 1 5v3c0 3 0 6-1 8h1c0-2 0-3 1-5v-2-2h0 0v-1h0v-4h0c1 2 0 5 1 7l-1 7v4l-2 7 1 1v2c-1 3-2 5-3 8 0 1 0 1 1 2-1 2-4 5-4 7 0 1-2 5-3 5-1 1-1 1-2 1l-2 4-7 10-1 2c1-1 2-2 3-2 2-1 4-4 5-5l1 1c-1 1-2 3-3 4l1 1c-1 1-3 3-4 5l-13 15h0l-6 7-2 2-7 6c-1 1-4 4-6 4h0c-2 1-4 5-6 5-1 1-1 1-1 2-1 1-2 2-3 2h-1l3-4c0-1 5-6 6-7l-1-1c-2 1-3 2-4 2 0-1 1-1 2-2v-1-2l-1 1h0v-1-1c-1 0-1-2-1-3v1c-1 1-2 2-3 4-2 2-5 4-7 6s-4 4-7 6c2-3 6-6 7-9-2 2-4 5-7 6v-1h0-1l-1-1 15-15 2-1c0-2 1-4 2-5v-1l1 1c3 1 5-3 8-4h0l5-8 2-3 4-7 1-3 3-4h0l2-2c1 1 2 1 3 2h0v1h1c1-1 1-2 2-3l-2 1c-1 0-2 0-3-1 2-1 2-1 3-3 0-1 1-2 1-2l3-7c6-18 9-36 5-55l-4-12-1-2-3-4-1-1c-2-2-4-3-6-4h-1c-1 4-2 8-5 10-2 2-4 2-6 3-3 0-5 0-7-1-5-2-7-7-9-11l-1-4h0v-5c1-5 2-8 5-13l3-2h0l3-2 7-3 1-1z" class="m"></path><path d="M446 441c1 0 1-1 2-1s1 0 2-1c1 0 2 1 2 2 0 0-1 0-1 1-2-1-3-1-5-1z" class="I"></path><path d="M467 529c1 0 1 1 2 1l-3 5-1-1c0-1 2-4 2-5z" class="l"></path><path d="M444 448h0c-2-3-2-5-2-8h1v2h1v2l1 1c0 1 0 2-1 3h0z" class="H"></path><path d="M429 448h1 5v1c0 1-1 1-2 2h-1c-2 0-3 1-4-1h1v-1-1z" class="E"></path><path d="M421 448v-1c1 3 3 6 6 7 0 1 1 1 1 1h0c2 1 4 0 5 0-1 1-3 2-5 1h-1l-2 1c-3-2-3-6-4-9z" class="D"></path><path d="M452 441c1 0 2 1 3 1 1 1 1 3 2 4l-1 1-1-1h-1c-1-1-2-2-4-3h-1 0v1c-2-1-2-1-4-1h-1v1-2c1-1 2-1 2-1 2 0 3 0 5 1 0-1 1-1 1-1z" class="J"></path><path d="M452 441c1 0 2 1 3 1 1 1 1 3 2 4l-1 1-1-1c-1-2-2-3-4-4 0-1 1-1 1-1z" class="C"></path><defs><linearGradient id="Ar" x1="440.859" y1="451.424" x2="426.62" y2="456.608" xlink:href="#B"><stop offset="0" stop-color="#7d7b7b"></stop><stop offset="1" stop-color="#aaa9aa"></stop></linearGradient></defs><path fill="url(#Ar)" d="M433 455c2-1 4-2 4-5 1-1 1-2 1-3-1 0-1-1-2-1v-1h1l1 2c1 1 4 2 3 3-1 3-3 7-6 8s-6 1-8 0c-1 0-1-1-2-1l2-1h1c2 1 4 0 5-1z"></path><path d="M421 448c-1-2-1-4-1-5v-1c1-3 3-5 5-7 1-3 3-5 6-6 2 0 3-1 5-1 2-1 5-1 7-1l2 1h2c4 1 8 5 11 8h1c1 1 2 3 3 4 3 4 5 8 7 12v1 1h0l-1-1c-1-3-3-7-5-10l-1-1v-1l-1-1c-1-2-3-4-5-5h-1c-2-2-5-4-8-5h0c-1-1-2-1-3-1s-2 0-3-1c-1 0-2 0-3 1h-4c-1 0-2 1-3 1l-1 1h-1l-1 1c-3 2-6 7-6 11-1 1 0 3-1 4v1z" class="B"></path><path d="M473 451c1 1 2 3 2 5 3 7 4 15 4 22v16c-1 2-1 4-1 6 0 1-1 2-1 3l-2 1c4-17 3-36-2-53z" class="O"></path><path d="M453 546h1c1-1 1-2 2-3 1-2 2-3 3-5s2-4 3-5c0-1 1-2 1-4l1-1c0-1 1-3 1-3 0-1 0-2 1-2 0-1 0-1 1-2v1s0 1-1 2v1c-1 1-1 3-2 4h0c-1 2-2 6-3 7v1c0 2-4 7-5 9h-1l-3 6c1-2 3-3 4-5 1-1 1-1 1-2 1 0 1 0 1-1h0v-1c2-1 2-3 4-4-1 3-4 6-6 9-5 5-9 11-14 17-2 1-3 3-5 4 3-6 8-10 11-15l1-2 4-6z" class="E"></path><path d="M444 444v-1h1c2 0 2 0 4 1v-1h0 1c2 1 3 2 4 3 3 5 7 11 7 16l-3-6c-1-2-1-4-2-5s-2-3-3-5c0 0-1 0-1-1h0l-1 1c1 2 3 3 3 5 1 1 0 2 0 3v2c2 1 3 3 3 5h0l-1-2-3-4-1-1c-2-2-4-3-6-4h-1c0-1 0-1-1-2h0c1-1 1-2 1-3l-1-1z" class="I"></path><path d="M444 444v-1h1c2 0 2 0 4 1v-1h0 1c0 2 0 3 1 5h1l-1 1c0-1-1-2-2-3 0 2 1 4 2 5s1 2 1 3c-2-2-4-3-6-4h-1c0-1 0-1-1-2h0c1-1 1-2 1-3l-1-1z" class="P"></path><path d="M444 444v-1h1c2 0 2 0 4 1 0 1-1 1-1 2-1 0-1 0-2-1-1 1-1 1-1 0l-1-1z" class="b"></path><path d="M455 442c2 1 5 4 6 6h0c5 9 9 20 9 31v6 7h-1v-5h-2c-1-1-1-6-2-8 0-6-2-11-4-17 0-5-4-11-7-16h1l1 1 1-1c-1-1-1-3-2-4z" class="D"></path><path d="M455 442c2 1 5 4 6 6h0v2c2 3 3 8 4 11h0c-1-1-1-2-2-3-3-3-4-7-7-11l1-1c-1-1-1-3-2-4z" class="B"></path><path d="M457 446c2 3 4 6 5 10l1 2c-3-3-4-7-7-11l1-1z" class="I"></path><path d="M461 448c5 9 9 20 9 31v4c0 1-1 0-1 1v1 1c-1-2-1-6-1-7v-5c-1-3-1-10-3-13-1-3-2-8-4-11v-2z" class="L"></path><defs><linearGradient id="As" x1="475" y1="539.402" x2="469.737" y2="500.382" xlink:href="#B"><stop offset="0" stop-color="#8b8c8c"></stop><stop offset="1" stop-color="#bfbbbc"></stop></linearGradient></defs><path fill="url(#As)" d="M479 478v1c1 1 1 2 1 4 1 2 0 3 1 5v3c0 3 0 6-1 8h1c0-2 0-3 1-5v-2-2h0 0v-1h0v-4h0c1 2 0 5 1 7l-1 7v4l-2 7 1 1v2l-3 8c0 1 0 1 1 2-1 2-4 5-4 7 0 1-2 5-3 5-1 1-1 1-2 1l-2 4-7 10-4 3c1-2 1-5 3-6l1-2s0-1 1-2c0-1 1-2 1-3h0v-1c1 0 1-1 1-1h-1c0-1 2-3 2-4l1 1 3-5c-1 0-1-1-2-1l8-25 2-1c0-1 1-2 1-3 0-2 0-4 1-6v-16z"></path><path d="M465 534l1 1c-1 4-3 7-5 10 0 0 0-1 1-2 0-1 1-2 1-3h0v-1c1 0 1-1 1-1h-1c0-1 2-3 2-4z" class="M"></path><path d="M474 524l1 2-2 3c0 2-1 3-2 5h0l-1-1c1-3 3-6 4-9z" class="l"></path><path d="M474 524l6-14 1 1v2l-3 8c-2 1-2 3-3 5l-1-2z" class="Z"></path><path d="M478 521c0 1 0 1 1 2-1 2-4 5-4 7 0 1-2 5-3 5-1 1-1 1-2 1l1-2h0c1-2 2-3 2-5l2-3c1-2 1-4 3-5z" class="c"></path><path d="M477 503c0-1 1-2 1-3 0-2 0-4 1-6 0 7 0 16-4 22 0-1 0-2-1-3 1-2 1-3 1-4l2-6z" class="K"></path><path d="M470 533l1 1-1 2-2 4-7 10-4 3c1-2 1-5 3-6 1 0 2-2 3-3 3-3 5-7 7-11z" class="M"></path><path d="M477 503l-2 6c0 1 0 2-1 4 1 1 1 2 1 3s-1 3-2 5l-4 9c-1 0-1-1-2-1l8-25 2-1z" class="J"></path><path d="M463 538h1s0 1-1 1v1h0c0 1-1 2-1 3-1 1-1 2-1 2l-1 2c-2 1-2 4-3 6l4-3-1 2c1-1 2-2 3-2 2-1 4-4 5-5l1 1c-1 1-2 3-3 4l1 1c-1 1-3 3-4 5l-13 15h0l-6 7-2 2-7 6c-1 1-4 4-6 4h0c-2 1-4 5-6 5-1 1-1 1-1 2-1 1-2 2-3 2h-1l3-4c0-1 5-6 6-7l-1-1c-2 1-3 2-4 2 0-1 1-1 2-2v-1-2s1-1 1-2c2-2 5-5 8-7 1-1 2-3 3-3s4-4 5-5c1 0 1-1 1-2 5-6 9-12 14-17 2-3 5-6 6-9l1-1z" class="K"></path><path d="M444 567h1c1 0 2-1 2-2 1-1 2-1 2-2-1 2-3 4-3 5s-1 1-1 2c-1-1-2-1-3-2 1 0 1 0 2-1z" class="o"></path><path d="M436 576c1-2 2-2 3-3 1 0 2-1 3-2h2c-2 3-5 5-7 7-1 2-3 4-4 5l1-3v-1-1c0-1 2-2 2-2z" class="h"></path><path d="M436 576h1c-1 2-2 3-3 4v-1-1c0-1 2-2 2-2zm13-6l1 1-6 7-2 2-7 6c-1 1-4 4-6 4h0l17-17c1-1 3-2 3-3z" class="N"></path><path d="M424 586c2-1 4-4 5-5l8-8 2-2 3-3c1 1 2 1 3 2l-1 1h-2c-1 1-2 2-3 2-1 1-2 1-3 3 0 0-2 1-2 2v1 1l-1 3c-1 1-5 5-6 5l-1-1c-2 1-3 2-4 2 0-1 1-1 2-2v-1z" class="O"></path><path d="M424 587c2-1 4-3 5-4l5-5v1 1l-1 3c-1 1-5 5-6 5l-1-1c-2 1-3 2-4 2 0-1 1-1 2-2z" class="X"></path><path d="M442 565c5-6 9-12 14-17-3 7-8 12-12 19-1 1-1 1-2 1l-3 3-2 2-8 8c-1 1-3 4-5 5v-2s1-1 1-2c2-2 5-5 8-7 1-1 2-3 3-3s4-4 5-5c1 0 1-1 1-2z" class="L"></path><defs><linearGradient id="At" x1="456.076" y1="553.153" x2="453.133" y2="551.561" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#787677"></stop></linearGradient></defs><path fill="url(#At)" d="M463 538h1s0 1-1 1v1h0c0 1-1 2-1 3-1 1-1 2-1 2l-1 2c-2 1-2 4-3 6l4-3-1 2-7 9h0l-2 2-1 1v-1-1l-1 1c0 1-1 1-2 2 0 1-1 2-2 2h-1c4-7 9-12 12-19 2-3 5-6 6-9l1-1z"></path><path d="M457 553l4-3-1 2-7 9h0l-2 2-1 1v-1-1l7-9z" class="G"></path><defs><linearGradient id="Au" x1="442.079" y1="561.191" x2="470.039" y2="557.997" xlink:href="#B"><stop offset="0" stop-color="#a5a6a0"></stop><stop offset="1" stop-color="#d2ced5"></stop></linearGradient></defs><path fill="url(#Au)" d="M460 552c1-1 2-2 3-2 2-1 4-4 5-5l1 1c-1 1-2 3-3 4l1 1c-1 1-3 3-4 5l-13 15h0l-1-1c0 1-2 2-3 3 0 0-1 0-2 1l1-3c1-2 3-3 4-5 2-1 3-3 4-5l7-9z"></path><path d="M466 550l1 1c-1 1-3 3-4 5l-13 15h0l-1-1 4-4c4-5 9-10 13-16z" class="M"></path><path d="M454 540c2-4 3-8 5-12 2-5 4-10 5-15 1-3 2-6 2-9h0l1 1c0 1-1 1-1 2v2 3c-1 2-1 4-1 6-1 3-1 5-2 8s-3 5-4 8l-6 12-4 6-1 2c-3 5-8 9-11 15 2-1 3-3 5-4 0 1 0 2-1 2-1 1-4 5-5 5s-2 2-3 3c-3 2-6 5-8 7 0 1-1 2-1 2l-1 1h0v-1-1c-1 0-1-2-1-3v1c-1 1-2 2-3 4-2 2-5 4-7 6s-4 4-7 6c2-3 6-6 7-9-2 2-4 5-7 6v-1h0-1l-1-1 15-15 2-1c0-2 1-4 2-5v-1l1 1c3 1 5-3 8-4h0l5-8 2-3 4-7 1-3 3-4h0l2-2c1 1 2 1 3 2h0v1h1c1-1 1-2 2-3z" class="H"></path><path d="M422 580c3-1 5-4 7-6-1 2-3 4-5 6h0 1c-1 1-1 2-2 3-1 0-1-2-1-3z" class="D"></path><path d="M446 542l2-2c1 1 2 1 3 2v1c-1 1-2 3-3 4-1 2-3 4-4 6 0 1-1 2-2 3s-2 2-2 4c-2 0-3 2-4 3l1-3-1-1 2-3 4-7 1-3 3-4h0z" class="G"></path><path d="M446 542l2 1c0 2-3 4-4 6l-7 11-1-1 2-3 4-7 1-3 3-4h0z" class="M"></path><path d="M436 559l1 1-1 3c1-1 2-3 4-3-1 1-2 3-4 3 0 1-1 2-1 3s-1 1-1 2l-5 6c-2 2-4 5-7 6v1c-1 1-2 2-3 4-2 2-5 4-7 6s-4 4-7 6c2-3 6-6 7-9 4-3 9-6 12-10l-1-1v1l-1-1c1-2 4-6 6-7l3-3 5-8z" class="S"></path><path d="M436 559l1 1-1 3-12 15-1-1v1l-1-1c1-2 4-6 6-7l3-3 5-8z" class="l"></path><path d="M431 567l-3 3c-2 1-5 5-6 7l1 1v-1l1 1c-3 4-8 7-12 10-2 2-4 5-7 6v-1h0-1l-1-1 15-15 2-1c0-2 1-4 2-5v-1l1 1c3 1 5-3 8-4h0z" class="Z"></path><path d="M422 571l1 1v1h0c-1 1-1 2-1 3v1c-1 0-2-1-2-1 0-2 1-4 2-5z" class="h"></path><path d="M420 576s1 1 2 1c-3 2-6 4-8 7-3 3-6 7-9 9h0-1l-1-1 15-15 2-1z" class="N"></path><path d="M141 596c-5-3-10-8-15-13-8-8-16-16-22-25-24-32-39-72-46-112-3-14-4-28-5-42l-1-35c1-58 12-117 39-169 5-9 10-18 16-26 7-10 14-18 22-27 10-11 20-21 32-30 14-10 29-17 44-24s30-12 46-14c12-3 25-4 37-4 7 0 14 0 21 1 14 1 28 2 42 5 22 6 44 16 63 29 1 0 2 2 3 2h2 0l2 1-1 1v1c1 0 1 1 1 1 1 1 2 1 2 1 1 0 2 1 2 2l-1 1c1 0 1 1 2 1 3 1 3 4 6 4l1 1h0c-1 2-1 3-3 4-1 0-2 1-3 1-2 1-3 1-5 2l-1-1-2-2c0 1-1 2-1 2l-1 1c-1-1-1-1-2-1h-2v1l-2 2v1c-3 1-9-8-12-11-3-2-5-3-8-4v-1l1-1v-2l-1 2c-2-1-5-2-8-3l-1-1h-2c-1 0-2-1-3-1l-1-1-3-3-1-1 1-1c3 0 4-2 5-4-1-1-2-3-4-4 0 0-1 0-2 1-2 0-2 2-2 4-2-2-7-2-9-3-4-1-8-2-12-4-3-1-5-2-8-3l-8-4h0l1 1c-1 0-1 0-1 1 1 1 1 0 1 1-1-1-2-2-3-2h0c-1-1-3-3-4-3v-1c-2-1-3-1-4-2h-1l-3-1c-4 1-7 2-9 5-1 1 0 3-1 4h-1c-1 1-1 2-2 3-1 0-2-2-3-1v1c-1 0-2 1-3 1l-1 1h0-5c-2-3-3-5-4-8h0 0v4c-1 0-2 1-3 1s-2-2-3-2h0l-4 3c-1 0-1 1-2 2l-1-1c-3-1-6-1-10-1h-2l1 1c0 1-1 2-1 3h0 0c0 1 0 2 1 3-1 0-2 1-2 1-6 2-13 2-19 4-2 0-3 1-5 2h-3c-4 2-8 6-12 9l-3 3c-1 2 0 2-1 3 0 0-2 0-2 1-2 2-1 4-4 5 0 3-1 6-4 8h-2c0-1 0-1-1-1 0-1 0-2-1-2-2 0-2 0-3 2h-1l1 2c0 1-1 2-1 2 0 1 1 2 1 3v5c-1 2-2 4-4 4l-2 1h1c0 1 1 1 1 1 1-1 2-1 2-1v-1l3-3h1v1l-3 3s1 0 0 1l-2 2c-2 6-7 12-10 19l1 1c-2 6-5 12-7 18l-2 9c0 1-1 2-1 3h0-2v-2c-1 1-2 2-3 2l-1 1 1 2c-1 2-2 5-3 8v8 7 2l-1-1-1 3-3 4c1 1 2 1 3 0h1 1c0 5-1 9-1 13-1 5-1 9-2 13l1 3h0c0 7 0 14-1 20v21 10l1 83 1 22 3 29 2 15c1 2 1 5 1 7l2 11c1 3 1 6 2 9 0 1 1 2 0 2v5 4l3 9 1-2c2 5 3 10 5 15 6 15 14 28 24 39 4 5 8 8 12 11-2 1-6 0-8 2l1 1c-2 1-3 1-3 3v1c-1 2-1 4-1 6l1 3c5 8 14 12 23 15l-1 1h0c4 1 8 1 12 1 3 0 6 0 9 1-1 1-3 4-2 5l1 1c0 1 1 2 2 2l6 3 1-1 3-3v-1c1-2 2-3 3-4v1-1l1-1c1 1 3 1 4 1 2-1 5 0 6 0v1c1-1 1-2 2-2v3h4v-1l1-3h2 2v-2h1 0c0 2-1 4 0 6h0c1 1 1 1 3 0 0 1 0 2 1 4l2 1 2 1c1 1 1 1 2 1l1 1h1c1 1 1 1 2 0 2 1 4-1 6-1 1 0 2 0 3 1h1c1 0 1 2 2 1 1 0 2 1 2 1 1 0 1 0 2 1l-2-2h1 2c3 1 4 0 7-2 1-1 1-1 1-2 1-1 2 0 3 0l2-1h0c2-1 3-2 4-4v1h1l-1-1v-3h0l1-1c0 1 1 0 2 1h-2v2c0 1 1 1 1 1 1 1 2 1 2 0h0 2c2-1 4-1 6-2 1-1 3-1 5-1 8-1 15-6 21-10l19-15 1 1h1 0v1c3-1 5-4 7-6-1 3-5 6-7 9 3-2 5-4 7-6s5-4 7-6c1-2 2-3 3-4v-1c0 1 0 3 1 3v1 1h0l1-1v2 1c-1 1-2 1-2 2 1 0 2-1 4-2l1 1c-1 1-6 6-6 7l-3 4h1c1 0 2-1 3-2 0-1 0-1 1-2 2 0 4-4 6-5h0c2 0 5-3 6-4l7-6 2-2 6-7h0l13-15h1 0v3l13-14c1 0 3-1 4-2l5-2 1-1h2c1-1 3-2 4-3 3-1 5-2 7-1l3 1c1 0 2 0 4 1h0l3-1v-1l1 1c4 0 7-1 10 0h-1l1 2 1 1h-1c3 0 8 1 11 3h-1l1 1h-1c1 1 3 1 4 1h2l1 1 1 2c0 2 0 4 1 5 0 3 1 5 1 7-1 1-1 2-2 3 0-1 0-2-1-3 0-1 0-2-1-3v-2h-1c0-3-2-4-3-6-3-1-5-2-8-3h-12c-12 2-22 9-32 16l-6 6c-1 1-2 2-4 3s-4 3-5 5c-1 1-3 2-4 4s-3 4-4 5l-11 9c-11 9-22 17-33 24-14 9-29 15-44 20-17 5-35 9-54 10-16 1-33 0-49-1 0-1 0-1-1-1h-1c-2-1-6-1-8-2v1h0-3c-7 0-15-2-22-4-28-7-55-18-79-35-4-3-9-6-12-10z" class="R"></path><path d="M412 132l1 1-2 2-3-3h4z" class="B"></path><path d="M404 120c1-2 2 0 3-1v-1-1h1c0 1 0 1 1 1v1h0c-1 0-1 1-2 1 0 1 0 1-1 1l-2-1h0z" class="I"></path><path d="M380 115v-3c2 0 4 1 6 1-1 1-3 2-4 2h-2z" class="Q"></path><path d="M386 113c2 0 3 2 5 1 0 1 1 2 1 2v1l-1 2c-2-1-5-2-8-3l-1-1c1 0 3-1 4-2z" class="D"></path><path d="M402 111c1 1 2 1 3 0 1 1 1 1 2 1l1 1c1 0 0 0 1 1h-1c-2-1-2-2-5-2 0 1-1 2-1 3s1 1 2 2l-1 1c-2-1-3-2-5-1h-2 0c1-1 1-1 2-1l-2-1v-1-1l1-1h1c1 0 1 1 1 1l1 1h0l1-1c1-1 1-1 1-2z" class="E"></path><path d="M396 114v-1l4 2h0c0 1 1 1 1 2-2-1-4-2-5-3z" class="I"></path><path d="M281 83h36c-1 1-1 1-2 1h-12-1v-1h-2v1c1 0 1 0 2 1h0c-3-2-4 0-6-1h-5-10-1l1-1z" class="C"></path><path d="M73 460c0 1 0 1 1 2v1 1l1 2c0 1 1 2 1 3 2 2 1 4 3 6l1 1c-1 0-2 1-3 2v2h-1c-1-2-1-4-1-6l-3-12 1-2zM302 85h0c-1-1-1-1-2-1v-1h2v1h1 12c3 1 7 1 10 1h-6c-4 1-7 2-9 5-1 1 0 3-1 4h-1v-3h0c0-1-1-2-1-2h-1c-1 0-3-3-4-3v-1z" class="B"></path><path d="M404 126c-3-3-4-5-8-6h-1l1-1c1 0 2 1 4 0h1c1 1 2 1 3 1h0l2 1c2 2 3 4 5 4 1 1 1 1 1 2v1l1 2c0 1 0 1-1 2h-4l-1-1c-2-2-2-3-3-5z" class="C"></path><path d="M404 126c1 0 1-1 2-1v2h-1c1 2 1 3 2 4-2-2-2-3-3-5z" class="I"></path><path d="M404 120l2 1c2 2 3 4 5 4v3h-1c0-2-2-3-4-5 0 0 0-1-1-1h0c-1 0-2-1-2-2h1z" class="D"></path><path d="M412 116l1-2c-3-3-7-5-10-7-7-4-15-8-23-11l-15-6c-5-2-11-4-17-5l-7-2c-3 0-6-1-9-1-1 0-3 0-4-1h-9c-2-1-4-1-7-1-2 0-5 0-7-1h0l18 1h0 3l13 2c12 2 25 5 36 10l19 9c5 2 9 5 14 8 1 1 3 2 5 3 1 0 1-1 1-2 1 0 2 2 3 2h2 0c-2 1-3 1-5 3-1 1-1 1-1 2l-1-1z" class="F"></path><path d="M212 104c1-2 7-4 9-5 11-4 22-7 33-9l14-3c1 0 4 0 5-1h0 1c1 1 1 2 2 3 2 1 4 1 6 1h2v-1l-1-1c-1-2-5-2-8-2-2-1-4-1-6-1-4 1-9 1-12 2l-32 8c-5 1-11 4-16 6l-1-1c2-1 5-2 7-3l10-3c5-2 9-3 14-4 14-4 28-6 42-7l-1 1h1l-1 1s2 1 3 1 1 1 2 2c1-1 2-2 4-2 1 0 1 1 1 1 2-1 4-2 5-3-2 2-4 4-5 7h0 0v4c-1 0-2 1-3 1s-2-2-3-2v-1-1l-1-1h-4-2-1c0-1-1-1-1-1h-1-4 2c0-1-1-1-1-1 1-1 2 0 3 0 0-1 0-1-1-1h-1-2 0-1v1h-5l-6 1c-7 1-16 3-23 5l-23 9h0z" class="I"></path><path d="M290 91c0 1 0 3-1 4-1-1-2-1-3-1s-1 0-1-1 0-3 1-4 2-1 3-1c1 1 1 1 1 3h0z" class="q"></path><path d="M281 84h10 5c2 1 3-1 6 1v1c1 0 3 3 4 3h1s1 1 1 2h0v3c-1 1-1 2-2 3-1 0-2-2-3-1v1c-1 0-2 1-3 1l-1 1h0-5c-2-3-3-5-4-8 1-3 3-5 5-7-1 1-3 2-5 3 0 0 0-1-1-1-2 0-3 1-4 2-1-1-1-2-2-2s-3-1-3-1l1-1z" class="m"></path><path d="M298 87h1c2 0 3 3 5 5h0v2c-1 0-2-2-3-2h0v-2l-1-1h-2c-1 1-2 1-2 2v1c0 1 0 2-1 3v-1-3c1-2 2-3 3-4z" class="E"></path><path d="M419 112l2 1-1 1v1c1 0 1 1 1 1 1 1 2 1 2 1 1 0 2 1 2 2l-1 1c1 0 1 1 2 1 3 1 3 4 6 4l1 1h0c-1 2-1 3-3 4-1 0-2 1-3 1-2 1-3 1-5 2l-1-1-2-2c0 1-1 2-1 2l-1 1c-1-1-1-1-2-1h-2v1l-1-1c1-1 1-1 1-2l-1-2v-1c0-1 0-1-1-2-2 0-3-2-5-4 1 0 1 0 1-1 1 0 1-1 2-1h0v-1c-1 0-1 0-1-1 1 0 3 0 4-1l1 1c0-1 0-1 1-2 2-2 3-2 5-3z" class="m"></path><path d="M416 123l2-1c2 0 3 1 4 1v1s0 1 1 1l-1 1-1-1v1s0 1 1 1v1c-1 1-2 0-3 0h0c-1 1-1 1-1 2h-1c-2-1-3-2-4-3 1-2 1-2 2-3l1-1z" class="R"></path><path d="M419 112l2 1-1 1v1c1 0 1 1 1 1 1 1 2 1 2 1 1 0 2 1 2 2l-1 1c-1 0-2 1-3 0s-2-1-3-1l-2-2v1s-1 0-1 1h0c-1-1-2-1-2-2s0-1 1-2c2-2 3-2 5-3z" class="q"></path><path d="M412 116l1 1c0 1 1 1 2 2h0l3 3-2 1-1 1c-1 1-1 1-2 3 1 1 2 2 4 3h1 1c0 1-1 2-1 2l-1 1c-1-1-1-1-2-1h-2v1l-1-1c1-1 1-1 1-2l-1-2v-1c0-1 0-1-1-2-2 0-3-2-5-4 1 0 1 0 1-1 1 0 1-1 2-1h0v-1c-1 0-1 0-1-1 1 0 3 0 4-1z" class="D"></path><path d="M413 132l2-2c1 0 2 1 2 1l1 1-1 1c-1-1-1-1-2-1h-2z" class="m"></path><path d="M409 118h3v1c-1 1-2 1-4 1v1h0c1 1 2 3 3 4-2 0-3-2-5-4 1 0 1 0 1-1 1 0 1-1 2-1h0v-1z" class="F"></path><path d="M412 116l1 1c0 1 1 1 2 2h0l3 3-2 1-1 1-1-1c-1-1-2-1-3-1l-1-1c1-1 3 0 4-1-1-1-1-1-2-1v-1h-3c-1 0-1 0-1-1 1 0 3 0 4-1z" class="Q"></path><path d="M415 119l3 3-2 1-2-3 1-1z" class="S"></path><path d="M133 584v-1c16 16 33 29 53 39 14 7 29 12 44 16 6 2 12 3 19 5 2 0 6 0 8 1v1h0-3c-7 0-15-2-22-4-28-7-55-18-79-35-4-3-9-6-12-10 2 1 4 3 6 4l7 6c2 1 5 4 8 5 1 1 2 2 4 2v-1c-1 0-1-1-2-1l-13-10c-1 0-2-1-3-2-1 0-3-1-3-1-2-2-3-4-4-5-3-3-5-6-8-9z" class="D"></path><path d="M133 584l-13-14c-24-28-41-62-51-97-4-15-6-29-8-44-2-17-4-34-4-51 0-16 1-31 2-47 4-50 18-101 44-144 14-22 32-42 51-59v1l-4 4-3 2c-15 14-29 30-40 48-34 51-47 114-48 174-2 70 9 143 52 201 6 9 14 17 22 25v1z" class="C"></path><path d="M76 480h1l3 6c5 15 11 29 18 42 4 7 8 14 13 20 3 5 6 10 10 15l13 13c15 17 34 32 55 42s44 18 67 22l30 3c12 1 25 1 37 0 4 0 9-1 13-1 14-2 29-6 43-11 7-2 14-6 21-9 16-9 31-20 45-32 17-16 33-36 57-45 5-2 10-3 16-3 5 0 9 0 13 2 1 1 3 1 4 1h2l1 1 1 2c0 2 0 4 1 5 0 3 1 5 1 7-1 1-1 2-2 3 0-1 0-2-1-3 0-1 0-2-1-3v-2h-1c0-3-2-4-3-6-3-1-5-2-8-3h2v-1h3c0-1-2-1-3-2-4-1-9 0-13 0-7 1-13 3-19 6-14 7-25 17-36 28-5 5-10 11-15 15-15 13-31 23-48 32-5 2-9 5-14 7-16 6-34 10-52 12-15 2-30 2-44 0-9 0-18-1-27-2-22-3-45-10-65-20-24-11-45-27-63-46-4-5-9-9-12-14-3-3-5-6-7-10-5-7-10-14-14-22-9-15-15-32-22-49z" class="H"></path><path d="M538 546l1 2c0 2 0 4 1 5 0 2 0 2-1 3-1 0-1-3-2-4l1 1c1-2 0-4 0-6v-1z" class="D"></path><path d="M533 549c2 0 3 1 4 2v1c1 1 1 4 2 4 1-1 1-1 1-3 0 3 1 5 1 7-1 1-1 2-2 3 0-1 0-2-1-3 0-1 0-2-1-3v-2h-1c0-3-2-4-3-6z" class="P"></path><path d="M317 83c5 0 10 1 15 2 26 4 50 13 73 26-1 1-2 1-3 0h-2c-1-1-3-1-5-2-1-1-2-2-3-2h-1c-1 0-1 1-2 2-2 0-4-1-7-2h-5c-1 1-2 2-4 3h0l-1-1 1-1c3 0 4-2 5-4-1-1-2-3-4-4 0 0-1 0-2 1-2 0-2 2-2 4-2-2-7-2-9-3-4-1-8-2-12-4-3-1-5-2-8-3l-8-4h0l1 1c-1 0-1 0-1 1 1 1 1 0 1 1-1-1-2-2-3-2h0c-1-1-3-3-4-3v-1c-2-1-3-1-4-2h-1l-3-1h6c-3 0-7 0-10-1 1 0 1 0 2-1z" class="F"></path><path d="M386 104c1 0 1 0 2 1 0 1-1 1-2 2h-2c1-1 2-2 2-3z" class="E"></path><path d="M212 104l23-9c7-2 16-4 23-5l6-1h5v-1h1 0 2 1c1 0 1 0 1 1-1 0-2-1-3 0 0 0 1 0 1 1h-2 4 1s1 0 1 1h1 2 4l1 1v1 1h0l-4 3c-1 0-1 1-2 2l-1-1c-3-1-6-1-10-1h-2l-2 1c-1 1-2 2-3 2-1-1-1-1-3-1h0c-2-2-11 1-14 2l-9 4c-1 1-3 2-5 2s-4 2-7 3v-1l1-1h-1-1c-3 1-6 3-9 5-1 1-2 1-2 2h2c-2 1-3 3-4 4l-1 1v1h-1c-2-1-6 0-8 0h-1v-1c-5 0-9 6-13 3h0v-1c-1 0 0-1 0-2 9-7 18-12 28-16h0z" class="R"></path><path d="M284 92v1 1h0l-4 3c-1 0-2 0-3-1-1 0-1-1-1-2 1 0 1 0 1 1h0 1c2 0 4-1 6-3z" class="U"></path><path d="M210 112c0 1 2 1 2 1-1 1-2 1-2 2h2c-2 1-3 3-4 4l-1 1v1h-1c-2-1-6 0-8 0h-1v-1l2-2h0c0 1 0 1 1 2h1c1 0 3-3 4-3 1-2 3-3 5-5z" class="K"></path><path d="M207 120c-1 0-1 0-2-1 0-2 3-3 5-4h2c-2 1-3 3-4 4l-1 1z" class="J"></path><defs><linearGradient id="Av" x1="197.845" y1="112.104" x2="183.544" y2="120.85" xlink:href="#B"><stop offset="0" stop-color="#929091"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#Av)" d="M184 120c9-7 18-12 28-16h0c-10 6-19 12-28 19v-1c-1 0 0-1 0-2z"></path><path d="M272 94h4c0 1 0 2 1 2 1 1 2 1 3 1-1 0-1 1-2 2l-1-1c-3-1-6-1-10-1h-2l-2 1c-1 1-2 2-3 2-1-1-1-1-3-1h0c-2-2-11 1-14 2l-9 4c-1 1-3 2-5 2s-4 2-7 3v-1l1-1h-1-1c-3 1-6 3-9 5 0 0-2 0-2-1l12-6c11-6 25-10 37-12h13z" class="V"></path><path d="M257 99c0-1 0-1-1-2h0v-1h4v1c1 0 2 1 3 1-1 1-2 2-3 2-1-1-1-1-3-1h0z" class="K"></path><path d="M272 94h4c0 1 0 2 1 2 1 1 2 1 3 1-1 0-1 1-2 2l-1-1c-3-1-6-1-10-1h-2l-2 1c-1 0-2-1-3-1v-1l12-2z" class="G"></path><path d="M265 97l1 1c0 1-1 2-1 3h0 0c0 1 0 2 1 3-1 0-2 1-2 1-6 2-13 2-19 4-2 0-3 1-5 2h-3c-4 2-8 6-12 9l-3 3c-1 2 0 2-1 3 0 0-2 0-2 1-2 2-1 4-4 5 0 3-1 6-4 8h-2c0-1 0-1-1-1 0-1 0-2-1-2-2 0-2 0-3 2h-1c0 1-1 2-2 3h0l-1-2 2-4-1-2v-2-1c-2-1-4-1-6-1h-5s0-1-1-1c0-1-1-1-1-3v-1c0-1 1-2 3-2 2-1 4-2 7-2 2 0 6-1 8 0h1v-1l1-1c1-1 2-3 4-4h-2c0-1 1-1 2-2 3-2 6-4 9-5h1 1l-1 1v1c3-1 5-3 7-3s4-1 5-2l9-4c3-1 12-4 14-2h0c2 0 2 0 3 1 1 0 2-1 3-2l2-1z" class="U"></path><path d="M221 108h1 1l-1 1v1c3-1 5-3 7-3l-12 6c-3 2-6 5-9 6 1-1 2-3 4-4h-2c0-1 1-1 2-2 3-2 6-4 9-5z" class="c"></path><path d="M221 108v1s-1 0-2 1c-1 0-2 2-3 2-1 1-3 2-4 3h-2c0-1 1-1 2-2 3-2 6-4 9-5z" class="o"></path><path d="M212 124c2 2 3 4 3 7v1c0 3-1 6-4 8h-2c0-1 0-1-1-1 0-1 0-2-1-2-2 0-2 0-3 2h-1c0 1-1 2-2 3h0l-1-2 2-4-1-2v-2-1c-2-1-4-1-6-1h-5s0-1-1-1c0-1-1-1-1-3h3 9v-1h0c1 0 3-1 4 0h4l3 2h1v-3z" class="B"></path><path d="M208 135v-1c1-2 3-2 4-3-2 0-2-3-3-4-1 0-3 0-4-1h0l3-1 3 2c1 1 1 2 2 3 0 1-1 2-1 3s-1 2-1 2l-1 1c-1 1-1 2-2 3 0-1 0-2-1-2l1-1v-1z" class="D"></path><path d="M210 136c-1 0-1 0-2-1 1 0 1-1 1-1h1l1 1-1 1z" class="Q"></path><path d="M201 131h2c2 1 3 2 5 4v1l-1 1c-2 0-2 0-3 2h-1c0 1-1 2-2 3h0l-1-2 2-4-1-2v-2-1z" class="G"></path><path d="M202 136c0-1 0-1 1 0 1 0 1 1 1 2v1h-1c0 1-1 2-2 3h0l-1-2 2-4z" class="M"></path><path d="M212 124c2 2 3 4 3 7v1c0 3-1 6-4 8h-2c0-1 0-1-1-1 1-1 1-2 2-3l1-1s1-1 1-2 1-2 1-3c-1-1-1-2-2-3h1v-3z" class="B"></path><path d="M188 126h3 9 2v1h1c1 1 3 2 4 3v2c-1 0-1 0-2-1h-2-2c-2-1-4-1-6-1h-5s0-1-1-1c0-1-1-1-1-3z" class="R"></path><defs><linearGradient id="Aw" x1="218.672" y1="109.204" x2="262.677" y2="114.774" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#e8e7e9"></stop></linearGradient></defs><path fill="url(#Aw)" d="M265 97l1 1c0 1-1 2-1 3h0 0c0 1 0 2 1 3-1 0-2 1-2 1-6 2-13 2-19 4-2 0-3 1-5 2h-3c-4 2-8 6-12 9l-3 3c-1 2 0 2-1 3 0 0-2 0-2 1-2 2-1 4-4 5v-1c0-3-1-5-3-7l-3-2c1 0 1 1 2 1 2-2 5-5 7-6 7-6 17-10 26-14 2-1 5-1 7-2h4l2-2c2 0 2 0 3 1 1 0 2-1 3-2l2-1z"></path><path d="M251 101h4 1v1l-5 1v-1-1z" class="V"></path><path d="M257 99c2 0 2 0 3 1-1 1-2 2-4 2v-1h-1l2-2z" class="L"></path><path d="M244 103c2-1 5-1 7-2v1 1l-6 2v-1l-1-1z" class="c"></path><path d="M225 118v2c-1 1-3 2-3 3-1 2 0 2-1 3 0 0-2 0-2 1-2 2-1 4-4 5v-1c1-1 1-1 1-2 2-4 5-8 9-11z" class="J"></path><path d="M215 124c0-1 1-1 1-2 2-2 3-4 5-6h1c1-1 2-1 3-1-4 3-9 7-9 12v2c0 1 0 1-1 2 0-3-1-5-3-7l-3-2c1 0 1 1 2 1s1 1 2 1h1 1z" class="K"></path><path d="M218 117c7-6 17-10 26-14l1 1v1c-7 1-15 5-20 10-1 0-2 0-3 1h-1c-2 2-3 4-5 6 0 1-1 1-1 2h-1-1c-1 0-1-1-2-1 2-2 5-5 7-6z" class="J"></path><path d="M218 117l2-1c-2 2-3 4-5 7v1h-1-1c-1 0-1-1-2-1 2-2 5-5 7-6z" class="o"></path><path d="M225 118l9-6c5-3 11-5 16-7 5-1 11 0 15-4h0c0 1 0 2 1 3-1 0-2 1-2 1-6 2-13 2-19 4-2 0-3 1-5 2h-3c-4 2-8 6-12 9l-3 3c0-1 2-2 3-3v-2z" class="K"></path><path d="M172 122v1h-2c-3 1-5 3-7 6h0c-2 1-5 4-7 6-10 8-20 19-28 29-11 14-21 28-29 44-2 4-5 9-7 13v2 1c-1 1-1 1-1 2l-1 3h0l3 3 1-1 1 4h0c2 3 3 5 5 8 1 1 2 3 3 4h1l2 2h1c1 1 2 2 3 2l2 2h1 1c1 0 1 1 1 1h4v-1h0-3c-1-1-2-1-3-1 0-1-1-1-1-2-1 0-3-1-4-2-1 0-2-1-3-2h1c-1-1-3-2-4-4-1-1-2-2-2-3-3-6-5-13-3-20v-1-1h0v3 4c0 5 1 10 4 14 4 6 11 11 18 12h1c1 0 3 0 4 1s5 1 6 0c3-1 6-2 8-3-1 1-2 2-4 3l-1 1v1c-1 1-2 1-3 1 0 1-1 1-1 1-1 0-1 0-1-1h1l-1-1c-2 0-5 0-6 1l-1 1h-1-3c0 1-1 1-1 2l-2 2c-1 2-1 4-2 6-1 1-1 2-3 3l-1 1h-1c0-1-1-2-1-3h-2l-1 1c-2 1-2 0-3 1h-1c-1 0-2-1-3-2l1-1c-1-1-1-1-1-2-1-1-1-1 0-1l-1-2-1-1-3-3c-1 0-1-2-2-2h-1s0-1-1-2l-1-14c-1 2-2 4-2 6l-5 15c-9 31-13 63-15 95-1 31 0 61 6 91l2 9c0 2 0 4 1 6l-1 2c-5-22-8-43-9-65-1-14 0-28 0-41 2-40 8-81 22-119l3-8c2-5 4-9 6-13 6-12 12-24 20-34 12-19 28-36 45-50 4-4 7-8 13-10z" class="C"></path><path d="M97 249h0c2 0 2 2 4 2h0l1-1c1 0 2 0 2 1s0 1-1 2l-2-2-1 1-3-3z" class="n"></path><path d="M100 252l1-1 2 2c1 1 2 1 2 3h-1c-1-1-2-1-2-1l-2-1v-2z" class="V"></path><path d="M90 234c2 0 3 0 5 1h0 0s-1 0-1 1 0 1-1 2c-1 0-1-1-3-1h0v-3z" class="D"></path><path d="M90 234v-5h0l3 3 1-1 1 4h0c-2-1-3-1-5-1z" class="P"></path><path d="M100 239c2 3 5 5 8 7s6 4 9 4c3 1 6 1 8 2l-4 1c0 1-1 1-2 0h-3c-1-1-2-1-3-1 0-1-1-1-1-2-1 0-3-1-4-2-1 0-2-1-3-2h1c-1-1-3-2-4-4-1-1-2-2-2-3z" class="E"></path><path d="M89 246c-1-5-3-11 0-16h0c0 3 0 13 3 15 1 1 2 1 3 1l2 1v1h-2-1c-1 0-2 1-4 2-1-2-1-2-1-4z" class="R"></path><path d="M89 246c2 2 4 2 6 2h-1c-1 0-2 1-4 2-1-2-1-2-1-4z" class="F"></path><path d="M111 262c0-1 1-2 0-3l-3-6v-1c1 0 2 0 2 1 1 0 3 1 4 1 1 1 2 1 3 1 0 1-1 1-1 2l-2 2c-1 2-1 4-2 6-1 1-1 2-3 3l-1 1h-1c0-1-1-2-1-3h1c1 0 2-1 3-2l1-2z" class="G"></path><path d="M110 253c1 0 3 1 4 1-1 1-1 3-1 4h-1c0-2-1-3-2-4h0v-1z" class="O"></path><path d="M90 250c2-1 3-2 4-2s2 1 2 1h1l3 3v2h-1c0 1-1 1-2 2h-6c-1 0-1-2-2-2v-1c0-2 1-2 1-3z" class="R"></path><path d="M96 249h1l3 3v2h-1v-1h-1c-1-2-2-2-2-4z" class="E"></path><path d="M100 254l2 1s1 0 2 1h1l1-1s1 0 1 1c0 0 1 1 1 2 1 1 2 2 3 4h0l-1 2c-1 1-2 2-3 2h-1-2l-1 1c-2 1-2 0-3 1h-1c-1 0-2-1-3-2l1-1c-1-1-1-1-1-2-1-1-1-1 0-1l-1-2-1-1-3-3h6c1-1 2-1 2-2h1z" class="R"></path><path d="M105 256l1-1s1 0 1 1c0 0 1 1 1 2 1 1 2 2 3 4h0l-1 2c0-2 0-3-1-4 0-1-2-2-3-2 0 1-1 1-2 1l-1-2c1 0 1-1 2-1z" class="B"></path><path d="M99 268c-1 0-2-1-3-2l1-1c-1-1-1-1-1-2-1-1-1-1 0-1 1 1 2 3 4 3 0 1 1 0 2 0l5 1h-1-2l-1 1c-2 1-2 0-3 1h-1z" class="H"></path><path d="M100 254l2 1s1 0 2 1h1c-1 0-1 1-2 1-2 0-4 1-6 2h-1 0c0-1 0-1 1-1v-1c-1 0-1 0-2 1 0 0-1 0-1 1l-3-3h6c1-1 2-1 2-2h1z" class="I"></path><path d="M100 254l2 1c-2 1-2 1-4 1h-1c1-1 2-1 2-2h1z" class="H"></path><path d="M172 122l2-1c1-1 3-2 4-4 2-1 4-3 7-4h0l-5 4-2 2c-1 0 0 1-1 1 0 1-1 1-2 2h1 1 1 2s0-1 1-1v-1h1 2c0 1-1 2 0 2v1h0c4 3 8-3 13-3v1h1c-3 0-5 1-7 2-2 0-3 1-3 2v1c0 2 1 2 1 3 1 0 1 1 1 1h5c2 0 4 0 6 1v1 2l1 2-2 4 1 2h0c1-1 2-2 2-3l1 2c0 1-1 2-1 2 0 1 1 2 1 3v5c-1 2-2 4-4 4l-2 1h1c0 1 1 1 1 1 1-1 2-1 2-1v-1l3-3h1v1l-3 3s1 0 0 1l-2 2c-2 6-7 12-10 19l1 1c-2 6-5 12-7 18l-2 9c0 1-1 2-1 3h0-2v-2c-1 1-2 2-3 2l-1 1 1 2c-1 2-2 5-3 8v8 7 2l-1-1-1 3v-4c0 1 0 1-1 1h0-1c-3 2-6 0-9 1l-2-2c-2-1-1-6-2-8 0-2-2-6-3-8-1 1-1 2-2 3v1h0l2 1-1 1-4 2h0l-2 1-1 1h-2c1 2 1 4 2 6 0 1 0 3 1 4l-1 4c0 1-1 3-1 4h0v2c-1 1-2 1-3 3l-2 2h0-2v1c-1 0-2 1-2 2h1v3c-1-1-2-2-3-2 0 1 0 1-1 2l-3-4-1-1s1 0 1-1c1 0 2 0 3-1v-1l1-1c2-1 3-2 4-3-2 1-5 2-8 3-1 1-5 1-6 0s-3-1-4-1h-1c-7-1-14-6-18-12-3-4-4-9-4-14v-4-3h0v1 1c-2 7 0 14 3 20 0 1 1 2 2 3 1 2 3 3 4 4h-1c1 1 2 2 3 2 1 1 3 2 4 2 0 1 1 1 1 2 1 0 2 0 3 1h3 0v1h-4s0-1-1-1h-1-1l-2-2c-1 0-2-1-3-2h-1l-2-2h-1c-1-1-2-3-3-4-2-3-3-5-5-8h0l-1-4-1 1-3-3h0l1-3c0-1 0-1 1-2v-1-2c2-4 5-9 7-13 8-16 18-30 29-44 8-10 18-21 28-29 2-2 5-5 7-6h0c2-3 4-5 7-6h2v-1z" class="R"></path><path d="M118 235l2-2c2 1 3 2 5 3-3 0-4-1-7-1z" class="B"></path><path d="M188 136l1 1c-1 2-2 5-4 6 0-1 1-1 1-2l1-1h-1c-1 1-1 1-3 2h0l1-1c1-1 3-3 4-5zm-70 99c3 0 4 1 7 1h0c1 0 2 0 3-1l1 1c-1 0-1 1-2 1-2 1-5 0-7 0l-2-2z" class="H"></path><path d="M102 230c1 0 2 0 2-1 1-1 1-4 2-6l2 5s-1 0-2 1l-2 2h-1 0-1 0v-1z" class="K"></path><path d="M90 229l1-3c0-1 0-1 1-2l2 7-1 1-3-3h0z" class="Z"></path><path d="M168 146c1 1 1 1 2 1 0 0 1 0 1 1h0l1-1c2 0 3-1 4-2h1l1 1c-2 1-3 1-4 2h-1c-3 1-5 1-8 0l3-2h0zm-3-11l2 2-1 1v-1c-2 1-5 4-7 4v-1c1-2 3-4 5-5l1 1v-1z" class="H"></path><path d="M159 141c2 0 5-3 7-4v1l-1 2v1h2c-1 1-1 0-2 0-2 1-3 0-4 2 0 1 0 1 1 2h-1c-1-1-2-3-2-4z" class="E"></path><path d="M165 135c-1-1-2-2-1-4h0v2c1 1 5 4 7 4h2l-2 2h1c0 1-1 1-1 2-1-1-2-1-4-1v1h-2v-1l1-2 1-1-2-2z" class="D"></path><path d="M167 140v-2c2-1 3 0 4 1h1c0 1-1 1-1 2-1-1-2-1-4-1z" class="C"></path><path d="M102 231l-1-3c-1-2 0-3 0-5 0-7 1-14 5-21 0 2-1 4-1 5l-2 11c-1 3-2 7-2 10 1 0 1 1 1 2v1z" class="L"></path><path d="M167 140c2 0 3 0 4 1l-2 3c-1 0-1 1-2 1v1h1l-3 2-1-1c-2 0-2-1-3-2h1c-1-1-1-1-1-2 1-2 2-1 4-2 1 0 1 1 2 0v-1z" class="R"></path><path d="M162 145l1 1h1c1-1 2-1 3-1v1h1l-3 2-1-1c-2 0-2-1-3-2h1z" class="C"></path><path d="M178 138c1 0 2 0 3 1h0v1c1 1 1 1 2 1h0 1l-1 1-5 4-1-1h-1c-1 1-2 2-4 2l-1 1h0c0-1-1-1-1-1-1 0-1 0-2-1h2c1 0 2 0 2-1h1 0c3-2 5-5 5-7z" class="F"></path><path d="M181 139h0v1c1 1 1 1 2 1h0 1l-1 1-5 4-1-1 3-5v-1h1z" class="V"></path><defs><linearGradient id="Ax" x1="150.701" y1="151.321" x2="148.198" y2="145" xlink:href="#B"><stop offset="0" stop-color="#a9aaae"></stop><stop offset="1" stop-color="#c8c5c2"></stop></linearGradient></defs><path fill="url(#Ax)" d="M139 158c2-2 16-16 16-17 0-4 4-6 6-8h1 0c-3 2-6 5-6 8-1 1 0 1 0 2s0 1-1 1l-4 4-8 9s-2 3-3 4v-1l2-3v-1c1 0 2-1 2-2l-4 4h-1z"></path><path d="M174 137c0-1 1-2 1-2h1l2 3c0 2-2 5-5 7h0-1c0 1-1 1-2 1h-2 0-1v-1c1 0 1-1 2-1l2-3c0-1 1-1 1-2h-1l2-2h1z" class="V"></path><path d="M172 140l1 1 1-1s1-1 2-1c-1 1-1 1-1 2-1 1-1 2-2 3h-1c0-2 1-3 0-4h0z" class="G"></path><path d="M174 137c0-1 1-2 1-2h1v4c-1 0-2 1-2 1l-1 1-1-1v-1h-1l2-2h1z" class="J"></path><path d="M124 173h0c-4 8-9 17-12 26-1 3-1 6-1 9-1 4 0 8 0 12 0 1 1 2 1 3l1 2c1 3 4 5 7 6 1 1 1 1 2 1l-1 1-2-1s-1-1-2-1c-3-2-5-5-6-9l-1-6c-1-4 0-8 0-12 1-3 2-5 2-7v-1-1c1-2 2-4 3-5 0-2 2-4 2-6 1-1 2-3 3-4 1-3 2-5 4-7z" class="E"></path><defs><linearGradient id="Ay" x1="122.372" y1="181.8" x2="138.266" y2="193.006" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#c2c0c1"></stop></linearGradient></defs><path fill="url(#Ay)" d="M139 158h1l4-4c0 1-1 2-2 2v1l-2 3v1l-9 12c-8 13-14 29-12 45 0 3 1 6 1 9-2-5-4-9-5-15 0-14 7-29 15-41 3-4 5-9 9-13z"></path><defs><linearGradient id="Az" x1="143.904" y1="162.545" x2="147.857" y2="165.135" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#9a9a99"></stop></linearGradient></defs><path fill="url(#Az)" d="M123 210c0-4 1-9 2-13 5-17 16-31 28-45 1-1 3-5 4-6 1 2 2 2 3 3v1h-1c-3 2-5 5-8 8-6 8-12 16-16 25-1 4-2 8-4 12v-3 1c0-1 0-3 1-4l3-9c-2 2-3 4-4 6-4 7-6 14-7 22v2-2l-1 3h0v-1z"></path><defs><linearGradient id="BA" x1="137.885" y1="211.081" x2="123.236" y2="205.41" xlink:href="#B"><stop offset="0" stop-color="#939192"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#BA)" d="M123 210v1h0l1-3v2-2c1-8 3-15 7-22 1-2 2-4 4-6l-3 9c-1 1-1 3-1 4v-1 3h0c0 4-2 8-2 11-2 7-1 13 0 20 0 1 1 2 1 2l1 4-1 2-1-1c-5-6-7-15-6-23z"></path><path d="M108 228c2 3 3 6 6 8 0 1 3 3 3 3l4 1c3 1 6 1 9-1 1-1 2-1 2-3v1c1 1 1 1 1 2h1 0 1 2v1s-1 0-1 1l-1 1s0 1 1 1c-1 1-1 1-2 1-3 2-7 3-11 3-2 0-4-1-6-1-1 0-2-1-2-1-5-3-11-9-13-14h1 0 1l2-2c1-1 2-1 2-1z" class="D"></path><path d="M103 231h1 2c1 0 1 2 2 3-1 0-1 1-2 1h0c-1-1-1-2-2-3h-1v-1z" class="C"></path><path d="M110 237h1c1 1 2 2 3 4l-1 1h0c-1 0-1-1-1-1-1-1-2-1-2-2-1-1 0-1 0-2z" class="P"></path><path d="M117 246c1-1 2 0 3 0h5l4-1c1-1 2-1 4-2 0-1 1-1 2-1 0 0 0 1 1 1-1 1-1 1-2 1-3 2-7 3-11 3-2 0-4-1-6-1zm15-10v1c1 1 1 1 1 2h1 0 1 2v1s-1 0-1 1c-2-1-5 1-6 1h-1c-2 0-5 0-7-1-2 0-4-1-5-2h0l4 1c3 1 6 1 9-1 1-1 2-1 2-3z" class="S"></path><path d="M159 150c1 1 2 1 2 2l-6 6c-10 12-19 27-21 42-1 3-1 5-1 7h-1v3c-1 3-1 5-1 7v2c1 3 1 5-1 7h-1c-1-7-2-13 0-20 0-3 2-7 2-11h0c2-4 3-8 4-12 4-9 10-17 16-25 3-3 5-6 8-8z" class="q"></path><path d="M172 122l2-1c1-1 3-2 4-4 2-1 4-3 7-4h0l-5 4-2 2c-1 0 0 1-1 1 0 1-1 1-2 2h1 1 1 2s0-1 1-1v-1h1 2c0 1-1 2 0 2v1h0c4 3 8-3 13-3v1h1c-3 0-5 1-7 2-2 0-3 1-3 2v1c0 2 1 2 1 3 1 0 1 1 1 1h5c2 0 4 0 6 1v1 2c-1 0-2-1-3-1h-2 0v1h0c-1 0-3 0-5 1l-2 2-1-1c-1 2-3 4-4 5h-1 0c-1 0-1 0-2-1v-1h0c-1-1-2-1-3-1l-2-3h-1s-1 1-1 2v-2h0v-1c-1 1-2 1-3 1 0-1-1-1-1-2-1 0-1 0-1-1v-1c3-1 4 1 7 1h0l-1-1v-3l1-1h1v-1c-3-2-3-3-7-3h2v-1z" class="E"></path><path d="M188 133h1v1c1 1 1 1 2 1l-2 2-1-1h0v-3z" class="n"></path><path d="M180 132c-1-1-3 0-4-1 1-1 2-1 2-2 2 0 2 1 3 1 1 1 1 1 1 2h-2z" class="m"></path><path d="M188 136h0c-1 2-3 4-4 5h-1 0c-1 0-1 0-2-1v-1h1l6-3z" class="K"></path><path d="M182 120h2c0 1-1 2 0 2v1h0l-4 2c-2 0-3-1-4-2h0 4c1-1 2-2 2-3z" class="L"></path><path d="M180 132h2 0c2 1 3 2 3 4-2 1-3 0-4 0-2-1-3-2-4-4h1 2z" class="C"></path><path d="M180 132h2 0v2h-1c-1 0-2-1-3-2h2z" class="B"></path><path d="M184 123c4 3 8-3 13-3v1h1c-3 0-5 1-7 2-2 0-3 1-3 2v1c0 2 1 2 1 3 1 0 1 1 1 1h5l-6 2v1h-1v-1-4c-1-1-2-2-3-2-2-1-3 0-5-1h0l4-2z" class="F"></path><path d="M195 130c2 0 4 0 6 1v1 2c-1 0-2-1-3-1h-2 0v1h0c-1 0-3 0-5 1-1 0-1 0-2-1v-1-1l6-2z" class="K"></path><path d="M189 134c1-1 3-2 4-2 2 0 4 0 5 1h-2 0v1h0c-1 0-3 0-5 1-1 0-1 0-2-1z" class="P"></path><defs><linearGradient id="BB" x1="140.911" y1="245.433" x2="125.933" y2="232.364" xlink:href="#B"><stop offset="0" stop-color="#d7d6d7"></stop><stop offset="1" stop-color="#fffefe"></stop></linearGradient></defs><path fill="url(#BB)" d="M131 219h1c0 1 1 1 1 2h1c2 1 6 2 8 4l-1 1c1 1 1 2 2 3h1c1 2 1 4 2 6 0 1 0 3 1 4l-1 4c0 1-1 3-1 4h0v2c-1 1-2 1-3 3l-2 2h0-2v1c-1 0-2 1-2 2h1v3c-1-1-2-2-3-2 0 1 0 1-1 2l-3-4-1-1s1 0 1-1c1 0 2 0 3-1v-1l1-1c2-1 3-2 4-3-2 1-5 2-8 3-1 1-5 1-6 0s-3-1-4-1h3 1l-2-2c1 0 1 1 2 1s1 0 2-1c1 1 3 0 4 0h0c1 0 2-1 3-1s2-1 3-2h-2c-3 2-7 3-11 2h0c4 0 8-1 11-3 1 0 1 0 2-1-1 0-1-1-1-1l1-1c0-1 1-1 1-1v-1h-2-1 0-1c0-1 0-1-1-2v-1l-2-2 1-2-1-4s-1-1-1-2h1c2-2 2-4 1-7z"></path><path d="M133 235h2l1-1-1-2c1-1 1-1 2-1l1 1v2c-1 1-2 2-3 2l-2-1z" class="E"></path><path d="M143 229h1c1 2 1 4 2 6 0 2 0 3-1 5-1-3-1-5-1-8-1-1-1-2-1-3zm-12-10h1c0 1 1 1 1 2h1c2 1 6 2 8 4l-1 1c-2-1-4-3-7-3-2 1-3 3-4 5 0 0-1-1-1-2h1c2-2 2-4 1-7z" class="F"></path><path d="M137 243h0v-1c1-2 3-2 4-3-1 3-2 6-4 8-2 1-5 2-7 2 3-1 5-2 7-4-1 0-2 0-3-1 1 0 1 0 2-1h1z" class="Q"></path><path d="M136 243h1 1c0 1-1 2-1 2-1 0-2 0-3-1 1 0 1 0 2-1z" class="D"></path><path d="M138 232c1 1 2 2 3 4v3c-1 1-3 1-4 3v1h0-1c-1 0-1-1-1-1l1-1c0-1 1-1 1-1v-1h-2-1 0-1c0-1 0-1-1-2v-1l-2-2 1-2c0 1 1 3 2 3l2 1c1 0 2-1 3-2v-2z" class="G"></path><path d="M138 232c1 1 2 2 3 4-1 1-1 1-2 1h-1v-3-2z" class="O"></path><path d="M146 235c0 1 0 3 1 4l-1 4c0 1-1 3-1 4-5 5-9 7-15 7 1 0 2 0 3-1l1-1c5-2 8-7 11-12 1-2 1-3 1-5z" class="L"></path><path d="M145 247h0v2c-1 1-2 1-3 3l-2 2h0-2v1c-1 0-2 1-2 2h1v3c-1-1-2-2-3-2 0 1 0 1-1 2l-3-4-1-1s1 0 1-1c6 0 10-2 15-7z" class="M"></path><path d="M130 256c1 0 2 0 3 1 0-1 0-1 1-1s1 0 1 1h1 1v3c-1-1-2-2-3-2 0 1 0 1-1 2l-3-4z" class="P"></path><path d="M198 133c1 0 2 1 3 1l1 2-2 4 1 2h0c1-1 2-2 2-3l1 2c0 1-1 2-1 2 0 1 1 2 1 3v5c-1 2-2 4-4 4l-2 1h1c0 1 1 1 1 1 1-1 2-1 2-1v-1l3-3h1v1l-3 3s1 0 0 1l-2 2c-2 6-7 12-10 19l1 1c-2 6-5 12-7 18l-2 9c0 1-1 2-1 3h0-2v-2c-1 1-2 2-3 2l-1 1 1 2c-1 2-2 5-3 8v8 7 2l-1-1-1 3v-4c0 1 0 1-1 1h0-1c-3 2-6 0-9 1l-2-2c-2-1-1-6-2-8 0-2-2-6-3-8-1 1-1 2-2 3v1h0l2 1-1 1-4 2h0l-2 1-1 1h-2-1c-1-1-1-2-2-3l1-1c-2-2-6-3-8-4h-1c0-1-1-1-1-2h-1v-2c0-2 0-4 1-7v-3h1c0-2 0-4 1-7 2-15 11-30 21-42l6-6c0-1-1-1-2-2h1l6 3h7l1-1c1 0 1 0 2-1 1-2 3-2 5-4l4-4c2-1 3-4 4-6l2-2c2-1 4-1 5-1h0v-1h0 2z" class="m"></path><path d="M153 196h3c1 1 1 2 2 2v1h-1-1v-1c-1-1-1-1-3-1v-1z" class="L"></path><path d="M154 209h5 0c-1 1-1 1-2 1v1 1h-1c0-1-1-2-1-2-1 0-1 0-1-1zm7 6h6 1c-1 1-3 2-3 3-1 1-1 1-2 1-1-1-2-3-2-4z" class="S"></path><path d="M181 204v1c0 1-1 1-1 2-1 1-2 2-3 2h-2 0v-4c3 1 4 0 6-1z" class="O"></path><path d="M175 171l2 1-3 11-1-1c0-1 1-3 1-4l-1-1-1 1c0-2 2-5 3-7z" class="D"></path><path d="M175 169h1l-1 2c-1 2-3 5-3 7v1l-2-1c0-2 1-3 1-5 2-1 3-2 4-4z" class="Z"></path><path d="M179 187v5l2-2c0 2 0 3-1 5v4h0c1 1 1 1 1 2 0 2 1 2 0 4v-1c0-2-2-3-3-4v-4c0-3 0-6 1-9z" class="Q"></path><path d="M173 182l1 1c-2 4-3 10-7 13-2 1-2 1-4 1v-1c5-2 7-9 10-14z" class="c"></path><path d="M175 209h2l-1 1 1 2c-1 2-2 5-3 8 0 0-1 1-2 0 1-2 0-4 0-5-1-1-1-1-1-3 1 1 1 1 3 0 0-1 1-2 1-3z" class="G"></path><path d="M158 201v2c1 0 1 1 2 1v-1c0-1 0-1 1-2v2c1 2 2 2 3 3 2 0 3 0 4-1h0 1c-1 1-3 2-4 2s-3-2-5-2c-1 0-2-1-3-1-2 0-5-1-6-2s-1-1-1-3c1-1 1-2 3-3v1c2 0 2 0 3 1v1h1 1v2z" class="B"></path><path d="M152 199h1v2 1c-1-1-2-1-2-2l1-1z" class="U"></path><path d="M158 201l-1 1-2-2h-1v-1h2 1 1v2z" class="F"></path><defs><linearGradient id="BC" x1="177.39" y1="167.975" x2="189.807" y2="179.384" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#999897"></stop></linearGradient></defs><path fill="url(#BC)" d="M189 158l2-3c1-2 1-2 3-3-1 3-3 7-4 10 0 0 0 1 1 1-1 1-1 2-2 2-1 1-2 3-3 5h1c-2 4-3 8-5 12l-1 7v1l-2 2v-5c0-3 1-7 2-11s2-9 4-13l4-9v4z"></path><path d="M190 162s0 1 1 1c-1 1-1 2-2 2-1 1-2 3-3 5l-2 4 3-9 3-3z" class="B"></path><path d="M189 158l2-3c1-2 1-2 3-3-1 3-3 7-4 10l-3 3c0-1 0-3 1-4 0-1 1-2 1-3z" class="Q"></path><path d="M198 133c1 0 2 1 3 1l1 2-2 4 1 2h0c1-1 2-2 2-3l1 2c0 1-1 2-1 2 0 1 1 2 1 3v5c-1 2-2 4-4 4l-2 1c-1 1 0 1 0 2-1 1-1 0-3 0l-4 6-2 2c0 2-1 3-2 4h-1c1-2 2-4 3-5 1 0 1-1 2-2-1 0-1-1-1-1 1-3 3-7 4-10-2 1-2 1-3 3l-2 3v-4l-4 9v-2c-1-2 0-4 1-6 0-1 1-3 2-4l-1-1h0-1c4-5 6-9 10-14l1-1-1-1h0v-1h0 2z" class="J"></path><path d="M197 144c0-2 1-3 1-5 0 1 0 3 1 4l1 4h-3l1-3h-1z" class="P"></path><path d="M198 133c1 0 2 1 3 1l1 2-2 4h0c-1 1-1 2-1 3-1-1-1-3-1-4h0c1-2 0-3-1-4l-1-1h0v-1h0 2zm-2 3h1c0 2-5 10-6 12l-2 6-4 9v-2c-1-2 0-4 1-6 0-1 1-3 2-4l-1-1h0-1c4-5 6-9 10-14z" class="F"></path><path d="M201 142h0c1-1 2-2 2-3l1 2c0 1-1 2-1 2 0 1 1 2 1 3v5c-1 2-2 4-4 4l-2 1c-1 1 0 1 0 2-1 1-1 0-3 0l-4 6-2 2c0 2-1 3-2 4h-1c1-2 2-4 3-5 1 0 1-1 2-2-1 0-1-1-1-1 1-3 3-7 4-10l3-8h1l-1 3h3l-1-4c0-1 0-2 1-3h0l1 2z" class="S"></path><path d="M195 158c1-1 1-2 3-2-1 1 0 1 0 2-1 1-1 0-3 0z" class="C"></path><path d="M199 143c0-1 0-2 1-3h0l1 2c1 3 3 6 1 9-1 2-3 3-5 4h0v-2c1-1 2-1 3-2v-4l-1-4z" class="E"></path><path d="M197 144h1l-1 3c0 6-3 12-6 16-1 0-1-1-1-1 1-3 3-7 4-10l3-8z" class="U"></path><path d="M191 135c2-1 4-1 5-1l1 1-1 1c-4 5-6 9-10 14h1c-3 5-5 10-7 15-1 1-1 3-2 4l-1 3-2-1 1-2h-1v-2c0-1 0-3-1-4l-4 3-1 2v-1-2c0-1-1-1-1-2s1-1 1-2v-1h0v-2c1 0 0-1 0-2h-4c0-1-1 0-1 0h-1-1v-1l1-1c1-1 2-1 3-1h7l1-1c1 0 1 0 2-1 1-2 3-2 5-4l4-4c2-1 3-4 4-6l2-2z" class="S"></path><path d="M176 169h0c0-1 1-3 1-3 0-1-1-1-1-1v-3c1-1 1-2 2-2h2 0c-1 2-2 4-2 6v3l-1 3-2-1 1-2z" class="L"></path><path d="M180 160c0-1 1-2 1-3l2-2c0-2 2-4 3-5h1c-3 5-5 10-7 15-1 1-1 3-2 4v-3c0-2 1-4 2-6h0z" class="C"></path><path d="M176 159c2-4 4-7 8-11 1-1 2-3 4-4 0 2-1 2-1 3-2 2-3 4-5 6-1 0 0 0-1 1l-2 2v3h-2l-1 1c-1 0-1 1-1 1l1-2z" class="K"></path><path d="M191 135c2-1 4-1 5-1l1 1-1 1c-3 1-4 2-6 5 0 1-1 2-2 3-2 1-3 3-4 4-4 4-6 7-8 11l-1-1 1-3c1-3 3-5 5-8l4-4c2-1 3-4 4-6l2-2z" class="N"></path><path d="M181 147c-2 3-4 5-5 8l-1 3 1 1-1 2s-1 1-1 2l-4 3-1 2v-1-2c0-1-1-1-1-2s1-1 1-2v-1h0v-2c1 0 0-1 0-2h-4c0-1-1 0-1 0h-1-1v-1l1-1c1-1 2-1 3-1h7l1-1c1 0 1 0 2-1 1-2 3-2 5-4z" class="S"></path><path d="M175 158l1 1-1 2s-1 1-1 2l-4 3v-1c2-1 3-5 5-7z" class="O"></path><path d="M173 153l1-1v1c0 1-1 2-1 2-1 1-1 3-1 5h-2l-1 1v-1h0v-2c1 0 0-1 0-2h-4c0-1-1 0-1 0h-1-1v-1l1-1c1-1 2-1 3-1h7z" class="D"></path><path d="M173 153l1-1v1c0 1-1 2-1 2h-1c-1 0-1 1-2 1h-1c0-1 1-2 1-3h2 1z" class="V"></path><path d="M202 156v-1l3-3h1v1l-3 3s1 0 0 1l-2 2c-2 6-7 12-10 19l1 1c-2 6-5 12-7 18l-2 9c0 1-1 2-1 3h0-2v-2c0-1 1-1 1-2 1-2 0-2 0-4 0-1 0-1-1-2h0v-4c1-2 1-3 1-5v-1l1-7c2-4 3-8 5-12 1-1 2-2 2-4l2-2 4-6c2 0 2 1 3 0 0-1-1-1 0-2h1c0 1 1 1 1 1 1-1 2-1 2-1z" class="R"></path><path d="M182 182h1c2 0 2 0 3-1-1 2-1 3-2 4l-3 14c-1-3 0-7 0-10l1-7z" class="U"></path><path d="M191 178l1 1c-2 6-5 12-7 18l-2 9c0 1-1 2-1 3h0-2v-2c0-1 1-1 1-2 1-2 0-2 0-4 0-1 0-1-1-2h0v-4c1-2 1-3 1-5v-1c0 3-1 7 0 10 1 1 1 1 1 2 2-3 3-6 4-9l5-14z" class="D"></path><path d="M198 156h1c0 1 1 1 1 1 1-1 2-1 2-1h0c-1 1-1 1-3 1h0c0 1 1 2 0 3 0 1 0 1-1 1-1 1-1 3-2 4-2 2-3 4-5 5-1 2-2 3-2 5-1 1-1 2-2 3 0 1 0 2-1 3s-1 1-3 1h-1c2-4 3-8 5-12 1-1 2-2 2-4l2-2 4-6c2 0 2 1 3 0 0-1-1-1 0-2z" class="B"></path><path d="M191 170s-1 0-1-1c1-1 2-3 4-4h2c-2 2-3 4-5 5z" class="I"></path><path d="M198 158l1 1c0 1-1 2-2 2h-1c-2 1-3 2-5 3l4-6c2 0 2 1 3 0z" class="F"></path><path d="M155 175c1-1 1-1 3-2 1 2 3 2 4 2h5l4-2c0 2-1 3-1 5l2 1c-1 1-2 3-2 4-1 1-2 3-3 4l-2 2c-1 0-2 1-4 1v1h0-3v1c0 2 1 2 2 4l-5-3h-3c-2 2-3 4-4 7 0 2 0 5 2 7l3 3 1-1c0 1 0 1 1 1 0 0 1 1 1 2h1v-1c1 2 3 3 4 4 0 1 1 3 2 4 1 0 1 0 2-1v2h4 3c1 1 2 0 2 0v8 7 2l-1-1-1 3v-4c0 1 0 1-1 1h0-1c-3 2-6 0-9 1l-2-2c-2-1-1-6-2-8 0-2-2-6-3-8l-1-3-1 1c-2-3-5-4-7-6-1-1-1-3-2-4-1-2 0-6 0-9l1-3-1-1c1-2 1-3 1-4l2-4c1-2 3-6 4-6s1 0 1 1l2 1c0-1 0-1 1-1v-1l1-1s0-1 1-1l1-2c-1-1-1-1-2-1z" class="H"></path><path d="M164 221c1 1 2 2 2 3-1 1-2 1-3 0l1-2h0v-1z" class="Q"></path><path d="M160 188l1 1v1 1h0-3v1-1l2-3z" class="U"></path><path d="M152 193c1 0 1-1 2-2l3-3c0 1 0 3-1 4 0 0-1 0-1 1h-3z" class="I"></path><path d="M155 218c0-1-1-2-1-2-1-1 0-2-1-3-1 0-1 0-1-1h1l2 2c1 1 1 2 2 3l-2 1z" class="F"></path><path d="M155 218l2-1c1 1 2 1 3 1 1 1 1 1 1 2h2v2h1l-1 2v1c0 1 0 2-1 3-1 0-2 1-3 1v-1c-1-1-1-1 0-2-1-2-1-5-1-7-1 0-2 0-2-1h-1z" class="I"></path><path d="M143 198v3l1 1c1-1 0-2 0-3s0-1 1-2c0-1 1-2 1-3 1-2 1-3 2-4l1 1s0 1 1 1h0l-4 6c-1 1 0 7 0 9 2 3 5 6 7 9l-1 1c-2-3-5-4-7-6-1-1-1-3-2-4-1-2 0-6 0-9z" class="L"></path><path d="M155 179l1 1v-1h1l1 1v1h-1s-1 0-1 1c-1 1-1 1-2 1l-1 5-3 4h0c-1 0-1-1-1-1l-1-1c-1 1-1 2-2 4 0 1-1 2-1 3-1 1-1 1-1 2s1 2 0 3l-1-1v-3l1-3-1-1c1-2 1-3 1-4l2-4c1-2 3-6 4-6s1 0 1 1l2 1c0-1 0-1 1-1v-1l1-1z" class="S"></path><path d="M146 186c1-2 3-6 4-6s1 0 1 1l2 1c-4 4-7 8-9 13l-1-1c1-2 1-3 1-4l2-4z" class="m"></path><path d="M155 175c1-1 1-1 3-2 1 2 3 2 4 2h5l4-2c0 2-1 3-1 5l2 1c-1 1-2 3-2 4-1 1-2 3-3 4l-2 2c-1 0-2 1-4 1v1-1-1l-1-1c0-2 0-2 1-3-1-1 0-2-1-3-4 1-5 4-7 6l1-5c1 0 1 0 2-1 0-1 1-1 1-1h1v-1l-1-1h-1v1l-1-1s0-1 1-1l1-2c-1-1-1-1-2-1z" class="Q"></path><path d="M168 176h0c0 1 1 2 1 2v1c0 1 0 2 1 4-1 1-2 3-3 4-2-2 1-6 1-8v-3h0z" class="V"></path><path d="M171 173c0 2-1 3-1 5l2 1c-1 1-2 3-2 4-1-2-1-3-1-4v-1s-1-1-1-2h0l-1-1 4-2z" class="G"></path><path d="M155 175c1-1 1-1 3-2 1 2 3 2 4 2h5l1 1h0c-2 1-3 2-5 1h-1 0c-2 0-4 0-6 1l1-2c-1-1-1-1-2-1z" class="K"></path><path d="M161 185l2-4c0-1 2-3 3-3l1 1-1 1v2 3c-1 1-1 3-1 4-1 0-2 1-4 1v1-1-1l-1-1c0-2 0-2 1-3z" class="H"></path><path d="M161 185l2-4c0-1 2-3 3-3l1 1-1 1c-1 1-2 3-3 4s-1 3-1 5c-1 0-1 1-1 1v1-1-1l-1-1c0-2 0-2 1-3z" class="F"></path><path d="M165 220h4 3c1 1 2 0 2 0v8 7 2l-1-1-1 3v-4c0 1 0 1-1 1h0-1c-3 2-6 0-9 1l-2-2v-2h-1c1-3 3-4 5-5l1-1v-1c1-1 1 0 3-1l-1-1c0-1-1-2-2-3l1-1z" class="R"></path><path d="M166 233c-1 1-1 2-1 2-1 0-1 0-2-1l3-1z" class="U"></path><path d="M170 225l1-1 1 1c0 3 0 6-1 8 0-2 0-6-1-8z" class="m"></path><path d="M165 220h4c1 2 1 3 2 4l-1 1-2-2-1 1h0v3c-1 2 0 4-1 6l-3 1v-2l1-1h1v-2-1h-2l1-1v-1c1-1 1 0 3-1l-1-1c0-1-1-2-2-3l1-1z" class="E"></path><path d="M172 220c1 1 2 0 2 0v8 7 2l-1-1-1 3v-4c0 1 0 1-1 1h0-1c1-1 1-2 1-3 1-2 1-5 1-8l-1-1c-1-1-1-2-2-4h3z" class="L"></path><path d="M172 225c0 2 1 5 1 8v3l-1 3v-4c0 1 0 1-1 1h0-1c1-1 1-2 1-3 1-2 1-5 1-8z" class="B"></path><path d="M160 150l6 3c-1 0-2 0-3 1l-1 1v1h1 1s1-1 1 0h4c0 1 1 2 0 2v2h0v1c0 1-1 1-1 2s1 1 1 2v2 1l1-2 4-3c1 1 1 3 1 4v2c-1 2-2 3-4 4l-4 2h-5c-1 0-3 0-4-2-2 1-2 1-3 2 1 0 1 0 2 1l-1 2c-1 0-1 1-1 1l-1 1v1c-1 0-1 0-1 1l-2-1c0-1 0-1-1-1s-3 4-4 6l-2 4c0 1 0 2-1 4l1 1-1 3c0 3-1 7 0 9 1 1 1 3 2 4 2 2 5 3 7 6l1-1 1 3c-1 1-1 2-2 3v1h0l2 1-1 1-4 2h0l-2 1-1 1h-2-1c-1-1-1-2-2-3l1-1c-2-2-6-3-8-4h-1c0-1-1-1-1-2h-1v-2c0-2 0-4 1-7v-3h1c0-2 0-4 1-7 2-15 11-30 21-42l6-6c0-1-1-1-2-2h1z" class="H"></path><path d="M155 175c1 0 1 0 2 1l-1 2c-1 0-1 1-1 1l-1 1v1c-1 0-1 0-1 1l-2-1c0-2 3-5 4-6z" class="J"></path><path d="M158 165c-1 1-2 0-2 1-1 0-3 3-4 3 1-2 2-8 5-9 0 2 0 3 1 5z" class="F"></path><path d="M135 204v-2c1-1 1-3 1-3 1-1 0-2 1-3h0c0-4 2-8 3-11v2h0 0 1c-1 3-2 6-2 9l-1 2v-1c-1 1-2 6-3 7z" class="Q"></path><path d="M164 156s1-1 1 0h4c0 1 1 2 0 2v2h0v1c0 1-1 1-1 2s1 1 1 2v1h-1c0 1-1 1-1 1h-3 0-3v-1l-3-1c-1-2-1-3-1-5 1-2 3-4 5-4h2z" class="E"></path><path d="M160 161c1 0 2 0 3 1h2v1h-4c-1-1-1-1-1-2zm4-5s1-1 1 0h4c0 1 1 2 0 2v2h0v1c0 1-1 1-1 2s1 1 1 2v1h-1c0 1-1 1-1 1h-3 0c1 0 1 0 2-1h0c2-3 0-3 1-5 0-1 1-1 1-2-1 0-1-1-2-1s-1 0-2-1h0l-2-1h2z" class="C"></path><path d="M162 156l2 1-1 2h0c-1 0-2 0-3 1v1c0 1 0 1 1 2h4l1 1v-2l1 1h-1v3c-1 1-1 1-2 1h-3v-1l-3-1c-1-2-1-3-1-5 1-2 3-4 5-4z" class="R"></path><path d="M174 163c1 1 1 3 1 4v2c-1 2-2 3-4 4l-4 2h-5c-1 0-3 0-4-2-1-1-1-2-1-3l2-2h3l-1-1h3 0 3s1 0 1-1h1v-1 2 1l1-2 4-3z" class="E"></path><path d="M159 168h3l1 1h-1l-1 1h5l-2 2h-2c-1-1-2-2-3-4z" class="n"></path><path d="M169 165v2 1c-1 1-2 1-3 2h-5l1-1h1l-1-1-1-1h3 0 3s1 0 1-1h1v-1z" class="Q"></path><path d="M139 196c0-1 1-2 1-3 1-2 2-5 3-8 1-2 1-5 3-6 1-1 1-1 2-1 0 2-2 6-2 7v1l-2 4c-1 3-2 5-3 9 0 1-1 2 0 3v2c1 1 1 4 2 5h0-1-1v-1 1h-1v2 2l1 1c-1 0-1 0-2 1l-1-2h-1l-1-2v1c-1-1-1-1-1-2h0v-6c1-1 2-6 3-7v1l1-2z" class="F"></path><path d="M135 204c1-1 2-6 3-7v1h0c0 2 1 3 1 5s-1 4 0 6v1c0 1 1 1 1 2h0v1l1 1c-1 0-1 0-2 1l-1-2h-1l-1-2v1c-1-1-1-1-1-2h0v-6z" class="L"></path><path d="M138 213v-3h0l2 2v1l1 1c-1 0-1 0-2 1l-1-2z" class="l"></path><path d="M135 204c1-1 2-6 3-7v1h0c-1 3-1 5-1 7s-1 4-1 6v1c-1-1-1-1-1-2h0v-6z" class="S"></path><path d="M143 209h0c-1-1-1-4-2-5v-2c-1-1 0-2 0-3 1-4 2-6 3-9 0 1 0 2-1 4l1 1-1 3c0 3-1 7 0 9 1 1 1 3 2 4 2 2 5 3 7 6l1-1 1 3c-1 1-1 2-2 3v1h0l2 1-1 1-4 2h0l-2 1-1 1h-2-1c-1-1-1-2-2-3l1-1c-2-2-6-3-8-4h-1c0-1-1-1-1-2h-1v-2c0-2 0-4 1-7v-3h1c0-2 0-4 1-7 0 3-1 8 1 10h0c0 1 0 1 1 2v-1l1 2h1l1 2c1-1 1-1 2-1l-1-1v-2-2h1v-1 1h1 1z" class="E"></path><path d="M131 217c2 1 2 2 3 4h-1c0-1-1-1-1-2h-1v-2z" class="I"></path><path d="M151 222v3l-2 2-2 1v-2h-1c2-1 3-2 5-4z" class="l"></path><path d="M142 225c1 1 2 1 4 1h1v2l-1 1h-2-1c-1-1-1-2-2-3l1-1z" class="M"></path><path d="M152 217l1-1 1 3c-1 1-1 2-2 3v1h0l2 1-1 1-4 2h0l2-2v-3h0c1-2 1-3 1-5z" class="G"></path><path d="M149 216c0 1 1 1 1 3-1 1-3 4-4 5h-2-1l1-1v-1c-1 0-2 0-3-1v-1h2c1 0 2 0 3 1 1-1 1-2 2-2 0-1 0-2 1-3z" class="F"></path><path d="M137 213h1l1 2c1-1 1-1 2-1h1v1c0 1-1 2-1 3h1l1 2h-2v1c1 1 2 1 3 1v1l-1 1c-3-2-5-4-6-7l-1-2h1v-2z" class="H"></path><path d="M139 215c1-1 1-1 2-1h1v1c0 1-1 2-1 3-1-1-2-2-2-3z" class="h"></path><path d="M134 200c0 3-1 8 1 10h0c0 1 0 1 1 2v-1l1 2v2h-1l1 2-1 2c1 0 1 1 2 2h-1c-4-4-4-8-4-14 0-2 0-4 1-7z" class="I"></path><path d="M135 210c0 1 0 1 1 2v-1l1 2v2h-1c-1-1-1-3-1-5z" class="P"></path><path d="M140 213v-2-2h1v-1 1h1 1c1 2 2 4 4 5l2 2c-1 1-1 2-1 3-1 0-1 1-2 2-1-1-2-1-3-1l-1-2h-1c0-1 1-2 1-3v-1h-1l-1-1z" class="D"></path><path d="M142 215c1 1 2 1 2 2v1h-2-1c0-1 1-2 1-3z" class="K"></path><path d="M140 213v-2-2h1v-1 1h0c1 3 2 4 4 6h1c-2 0-3-1-4-1h-1l-1-1z" class="I"></path><path d="M154 219c1 2 3 6 3 8 1 2 0 7 2 8l2 2c3-1 6 1 9-1h1 0c1 0 1 0 1-1v4l-3 4c1 1 2 1 3 0h1 1c0 5-1 9-1 13-1 5-1 9-2 13l1 3h0c0 7 0 14-1 20v21 10l1 83 1 22 3 29h-1c0 1 0 1-1 1 0-1-1-2-1-3 0 0-1 0-1 1-2-3-5-7-8-8-5-2-7-1-12 1-1 0-2 0-2-1h-1c1-1 1-3 0-4l1-1v-1h-1l-1-1c-1-1-1-2-1-2-2 0-3 0-4-1v-1h1c0-1-1-1-2-1l-1-2h-1-1l1-2-2-1 1-1v-1l-1-1h0l1-1-3-3c-1-1-1-3-2-3-1-1-2-2-2-3h1v-1h0 1v1-1c2 1 3 4 5 5h0l1-2c0-1 1-1 2-1 4-2 8-3 11-4l3-2v-1-1h-2l-1-1v-3c3-1 2 1 5 1h1c1-1 0-3 0-4v-1l-4 1c-1 0-2 0-3 1h-2-1v1c-6 0-12-1-18 0h-1v-2-1h-1l-1-1c-1 2-1 4-3 5-2 2-3 4-5 5l-3 2c-1 1-2 1-3 1h0c-1 0-2 1-2 1-1 1-2 1-3 1h-2-4c-1-1-2-1-3-1 0-1-1-1-2-2-1 0-2 0-3-1h0l5 5c1 1 1 1 2 1v1l-1 3h1v2 1c-1 1-2 1-3 0v1h-1c-1 0-1 0-1 1l-1-1c-1 0-2 1-3 1-1 1-1 2-1 3h0 0c0-2 0-2-1-3h0c-1 0-3-1-4 0 0 0 0 1-1 1v1l1 1h1 2v1c-2 2-5 2-7 4h0c-2 0-4 0-5 1l-1 1c0-1 0-1-1-1-1 2 0 4 0 5l3 6c0 2-1 4-2 4h-1-1c-1 2 1 6 1 8 1 1 0 1 0 1 0 1 1 2 1 3l2 6c0 2 1 3 0 5-2-2-1-4-3-6 0-1-1-2-1-3l-1-2v-1-1c-1-1-1-1-1-2-1-2-1-4-1-6l-2-9c-6-30-7-60-6-91 2-32 6-64 15-95l5-15c0-2 1-4 2-6l1 14c1 1 1 2 1 2h1c1 0 1 2 2 2l3 3 1 1 1 2c-1 0-1 0 0 1 0 1 0 1 1 2l-1 1c1 1 2 2 3 2h1c1-1 1 0 3-1l1-1h2c0 1 1 2 1 3h1l1-1c2-1 2-2 3-3 1-2 1-4 2-6l2-2c0-1 1-1 1-2h3 1l1-1c1-1 4-1 6-1l1 1h-1c0 1 0 1 1 1l1 1 3 4c1-1 1-1 1-2 1 0 2 1 3 2v-3h-1c0-1 1-2 2-2v-1h2 0l2-2c1-2 2-2 3-3v-2h0c0-1 1-3 1-4l1-4c-1-1-1-3-1-4-1-2-1-4-2-6h2l1-1 2-1h0l4-2 1-1-2-1h0v-1c1-1 1-2 2-3z" class="q"></path><path d="M83 311h1c-1 2-2 3-3 5h-1v-2l3-3zm6-9c1 0 1 1 1 1 1 1 2 1 4 1l-6 1 1-3z" class="m"></path><path d="M85 353c0 1 0 2 1 3l-1 1c-1 0-2-1-2-1h-1l3-3z" class="V"></path><path d="M106 287l1 3h0c0 2-1 4-2 6v-4-1c1-1 1-2 1-4z" class="H"></path><path d="M77 288h0c0-2 1-4 2-6h1c-1 1-1 2-1 3s1 1 2 2v1l-1-1c-2 0-2 0-3 1h0z" class="F"></path><path d="M70 426c-1-1-1-1-1-2l1-1c2 1 2 1 4 1v2c-1 1-3 0-4 0z" class="H"></path><path d="M74 424c0 1 0 2 1 2h1 1v2h-1c-1 0-2 1-3 1h0c0-1 0-1 1-2h-3l-1-1c1 0 3 1 4 0v-2z" class="U"></path><path d="M100 297l1 1h0c0-1 0-2 1-2s1 0 2 1l-4 4h-1c0-2 1-2 1-4z" class="H"></path><path d="M85 353h3c1 2 1 3 2 4v1h-1c-1-1-1-2-2-3h-1l1 2-1-1c-1-1-1-2-1-3z" class="B"></path><path d="M78 402h1c1 1 1 2 2 3l3 3h-2c-2-1-3-2-4-4v-2z" class="E"></path><path d="M82 324v-1c-1-1-2-2-3-4h1 0 0v1l4 4 1 1 2-1c0 1 1 1 1 2h-1-2c-1 0-2-1-3-2z" class="J"></path><path d="M91 323h3v2c-2-1-3 1-4 1-1 1-2 1-3 1v-1h1l3-3z" class="F"></path><path d="M87 324v-4h0l1 1 2 1h1l1 1h-1l-3 3c0-1-1-1-1-2z" class="G"></path><path d="M75 434h0c2 0 4-4 6-6v1 2l-1 1v1h1v1 1c-1-1-2-1-3-1h-3 0z" class="m"></path><path d="M83 272l-1 2-2-2c1-1 1-2 2-3h2l1 1c0 1 0 1-1 2h-1z" class="E"></path><path d="M85 270c0 1 0 1-1 2h-1-1v-1-1h2 1z" class="U"></path><path d="M105 283l1-1 3 3-2 5h0l-1-3-1-4z" class="V"></path><path d="M77 336h1 0c0 1 1 3 2 4h0l2-2c1 0 1 1 2 1s1 1 1 2h-2 0-3s-1 0-2-1-1-3-1-4z" class="K"></path><path d="M104 293h0l1-1v4l-1 1c-1-1-1-1-2-1s-1 1-1 2h0l-1-1-1-1h-1l3-3 1 1h1l1-1z" class="n"></path><path d="M84 338c1-1 2-3 4-4 1 0 2 1 3 1l-1 1c0 1-1 1-2 2l-1 2c-1 0-2 0-2 1 0-1 0-2-1-2v-1z" class="D"></path><path d="M84 338h2v-1l2 1-1 2c-1 0-2 0-2 1 0-1 0-2-1-2v-1z" class="S"></path><path d="M82 324c1 1 2 2 3 2l1 1c-1 1-2 3-3 3-2 1-3 0-5 0l2-1c0-1 0-3 1-4l1-1z" class="U"></path><path d="M90 310l-2 2c-2 3-1 5-1 8h0v4l-2 1-1-1c1-2 0-5 1-8 0-2 3-5 5-6z" class="L"></path><path d="M79 346c1-2 1-2 2-2v-2c-3 1-4 1-6 3v-2h1c-1-1-1-2-1-3v-1c0-1-1-1-1-1h-2-1v-1s1-1 2-1h3 1c0 1 0 3 1 4s2 1 2 1c1 1 2 1 3 1-1 2-1 2-2 3-1 0-1 0-2 1z" class="U"></path><path d="M73 304l3 2c1 0 2 0 2 1l2 1c0 1-1 2-2 3-2 2-5 6-8 8v-1c1-1 3-3 5-4l-2-10z" class="E"></path><path d="M76 306c1 0 2 0 2 1l2 1c0 1-1 2-2 3v-2c0-1-1-1-1-1-1 0-1-1-1-2z" class="F"></path><path d="M111 276c1-1 2-1 3-2l1 2c-1 0-2 1-2 2l-1 1c-1 2-1 5-3 6l-3-3-1 1v-1c1-1 1-2 2-2l2-5 2 1z" class="E"></path><path d="M107 280l2-5 2 1-2 2v2 2h0l-2-2z" class="H"></path><path d="M111 276c1-1 2-1 3-2l1 2c-1 0-2 1-2 2l-1 1-3 1v-2l2-2z" class="F"></path><path d="M87 327c-1 2-2 4-4 6-1-1-3-1-4-1-1 1-1 1-2 1h0l1-1v-1l-3-2v1c0 1 0 1-1 2v-1-2h0c-1 1-1 1-2 1s-1 0-2-1v-1c1-1 0-1 1-1h2 1 0c-1-1-1-2-2-2h-2c-1-1-1-1 0-2s2-1 3-1l1 1c-1 1-2-1-3 1h1 1c2 2 2 5 5 6h0c2 0 3 1 5 0 1 0 2-2 3-3l-1-1h2v1z" class="c"></path><path d="M71 369v-1l1 1c-2 8-2 17 0 26 1 2 3 5 3 7-1-1-2-3-4-4h0c1-1 1-2 0-2-1-3-1-5-1-8-1-7 0-12 1-19z" class="H"></path><path d="M77 288h0v3h0v1c0 1 1 1 2 2v2l1 1c0 1-2 3-2 4v3c0 1 1 1 2 2-1 0-1 0-2 1 0-1-1-1-2-1l-3-2v-1-2c1-1 1-2 3-2 0 1 0 1 1 1-1-4-1-8 0-12h0z" class="B"></path><path d="M73 303c1 0 1 0 1-1 0 0 0-1 1-1h1 0c-1 1-1 1 0 2h0c1 0 1 1 2 1 0 1 1 1 2 2-1 0-1 0-2 1 0-1-1-1-2-1l-3-2v-1z" class="R"></path><path d="M98 296h1l1 1c0 2-1 2-1 4h1c-2 1-3 2-4 3h-2c-2 0-3 0-4-1 0 0 0-1-1-1h-2v-1l1-1s0-1 1-1l-1-1c-1 1-3 0-4 0l-1-1c3 1 5 1 8 1v-1c1 0 2 0 3 1l2-1 2-1z" class="C"></path><path d="M98 296h1c-1 1-1 3-2 3 0-1 0-1-1-2l2-1z" class="Q"></path><path d="M96 297c1 1 1 1 1 2l-1-1c0 1-1 1-1 1h-2v-1h1l2-1z" class="D"></path><path d="M88 300c1 0 3 0 5 1v1h2c0 1 1 1 1 2h-2c-2 0-3 0-4-1 0 0 0-1-1-1h-2v-1l1-1z" class="B"></path><path d="M81 358v2c-1 1-2 2-2 3-5 10-5 22-1 33 2 5 5 10 8 14 1 1 3 3 3 4-1-1 0-1-1-1-1-2-3-3-4-5l-3-3c-1-1-1-2-2-3h-1c-1-2-2-5-3-7-1-7-2-17 0-25 1-4 3-9 6-12z" class="H"></path><path d="M79 294l4 3 1 1c1 0 3 1 4 0l1 1c-1 0-1 1-1 1l-1 1v1h2l-1 3-1 1c-2 1-3 0-6 0h-1c-1-1-2-1-2-2v-3c0-1 2-3 2-4l-1-1v-2z" class="I"></path><path d="M81 306c1-1 2-1 2-2h3v1l1 1c-2 1-3 0-6 0z" class="B"></path><defs><linearGradient id="BD" x1="84.596" y1="376.912" x2="77.928" y2="373.547" xlink:href="#B"><stop offset="0" stop-color="#a5a1a4"></stop><stop offset="1" stop-color="#c1c1c0"></stop></linearGradient></defs><path fill="url(#BD)" d="M81 358c0-1 0-2 1-2h1s1 1 2 1l1-1 1 1v1 1c-6 10-8 19-6 31h-1c0-1 0-1-1-2-1-8-1-15 2-22v-1-1-1-1-1l-2 2c0-1 1-2 2-3v-2z"></path><path d="M81 358c0-1 0-2 1-2h1v2l1 1 1-1h1v1l-5 7v-1-1-1-1-1l-2 2c0-1 1-2 2-3v-2zm-5 58l2 1h1c2 1 5 3 8 4 0 1 0 1 1 1s1 1 2 1c1 1 3 1 4 2l-3 3h0c-1 0-3-1-4 0 0 0 0 1-1 1v-4h0c-1-2-3-2-4-3 0 0-1 0 0 1l1 2h1 1v1h0c-1 1-1 2-2 2 0 1-1 2-2 3v-2-1c-2 2-4 6-6 6h0c0-1 0-1 1-2 1 0 2-2 3-3v-3c-1-3-1-4-3-6h-1l1-1h-1c-1-1-2-1-3-1h-1c1-1 4-2 5-2z" class="B"></path><path d="M87 423c1 0 2 1 2 1v3h-1c-1-1 0-1-1-2v-2z" class="U"></path><path d="M90 336c0 3-1 6-3 8h-1v3c-2 1-3 2-4 3-2 1-3 3-3 5-3 4-5 8-7 14l-1-1v1c0-3 1-7 2-10 1-2 3-4 3-6-2 0-3 0-5-1l-2-2v-1c0-1 1-4 1-6-1-1-1-2 0-3h0c0 1 0 2 1 2 0 2 1 4 0 5v3c1 1 2 1 3 2h1v-2h3v1h1l1-1c1-1 1-1 1-2-1-1-2-1-2-2h0c1-1 1-1 2-1 1-1 1-1 2-3-1 0-2 0-3-1h3 0 2c0-1 1-1 2-1l1-2c1-1 2-1 2-2z" class="F"></path><path d="M87 340c-1 2-3 4-4 5s-1 2-2 2v1c-1-1-2-1-2-2h0c1-1 1-1 2-1 1-1 1-1 2-3-1 0-2 0-3-1h3 0 2c0-1 1-1 2-1z" class="D"></path><path d="M71 409c-1-1-3-2-4-3h1 0 3c1 0 2 0 3-1 1 0 1 0 1-1 1 2 2 3 3 5 2 2 4 4 7 6v1h0c-3-2-5-3-7-6l-1-1c-1 0-1 0-1 1 1 1 1 2 2 3s1 2 1 3c-1-2-3-4-5-5 1 1 1 2 2 3v1c1 0 1 1 2 1 3 1 5 2 7 3 1 0 3 1 4 2l1-1c1 0 2-1 4-1h0v1l-2 1h1 0c0 1 1 1 1 2h1v-1h1l2 2c1 0 2-1 3-1h1v2 1c-1 1-2 1-3 0v1h-1c-1 0-1 0-1 1l-1-1c-1 0-2 1-3 1-1 1-1 2-1 3h0 0c0-2 0-2-1-3l3-3c-1-1-3-1-4-2-1 0-1-1-2-1s-1 0-1-1c-3-1-6-3-8-4h-1l-2-1c-1-2-2-3-3-5 0-1 0-1-1-2h-1z" class="C"></path><path d="M101 423h1v2c-1 0-1 0-2-1h0l-1 1h-2-1c0-1 0-1 1-1h1c1 0 2-1 3-1z" class="D"></path><path d="M71 409l1-2h2c0 1 1 3 1 4-1 0-1-1-2 0 0-1 0-1-1-2h-1z" class="E"></path><path d="M87 252c1 1 1 2 1 2h1c1 0 1 2 2 2l3 3 1 1 1 2c-1 0-1 0 0 1 0 1 0 1 1 2l-1 1c1 1 2 2 3 2h1c1-1 1 0 3-1l1-1h2c0 1 1 2 1 3h1l1-1c1 0 1 1 2 1-2 2-4 4-5 6l-3 4c-1 0-1 1-2 2l1 1h-1c-1 0-1 0-2 1-1-3-4-3-5-6l-1-1h-1c0-1 0-2 1-3h0c1-2 2-3 3-3 1-1 1-1 1-2s-1-1-1-1c-2 0-6 3-7 5v2h-3c-1 1 1 4-1 4-1-1-1-3-1-4 1-1 1-1 1-2l1 1c0-1 1-2 1-2 1-1 0-2 0-3s0-2 1-3c0 0 0-1 1-2v-2-1c-1 0-2 0-2-1-1-1-1-2-1-4 0-1 0-2 1-3z" class="B"></path><path d="M101 275c1 0 1 1 1 1h0l1 1c0 2-1 2-2 2h0c-1-1-1-2-1-3 1 0 1 0 1-1z" class="E"></path><path d="M89 254c1 0 1 2 2 2l3 3 1 1c-2 0-2-1-3-1h-1c-1 0-2-1-2-1 0-1 0-1 1-2l-2-2h1z" class="D"></path><path d="M96 270h3c0 2-1 2-2 3h0-2c-1 0-1 1-2 0h0c1-2 2-3 3-3z" class="C"></path><path d="M97 273h2v1h2v1c0 1 0 1-1 1 0 1 0 2 1 3h0v2l1 1h-2v-1c-1-1-1-2-1-3v-1l-1-1s-1 0-2-1c0-1 0-1 1-2h0zm-10-5c2 0 2-3 3-5h1c0 1 1 3 1 4-1 2-3 3-3 5-2 1-3 0-5 2 1-1 1-1 1-2l1 1c0-1 1-2 1-2 1-1 0-2 0-3z" class="U"></path><path d="M93 273c1 1 1 0 2 0h2c-1 1-1 1-1 2 1 1 2 1 2 1l1 1v1c0 1 0 2 1 3v1h2-1c-1 0-1 0-2 1-1-3-4-3-5-6l-1-1h-1c0-1 0-2 1-3z" class="I"></path><path d="M93 276h0c1-2 1-2 2-3v1 3h-1l-1-1z" class="B"></path><path d="M99 268h1c1-1 1 0 3-1l1-1h2c0 1 1 2 1 3h1l1-1c1 0 1 1 2 1-2 2-4 4-5 6h-1l1-2h0c-1 0-4 0-5-1v-3l-2-1z" class="H"></path><path d="M99 268h1c1-1 1 0 3-1l1-1h2c0 1 1 2 1 3h1c-1 1-2 1-4 1h0-1c-1 0-1-1-2-1l-2-1z" class="Q"></path><path d="M90 310s0-1 1-1c1-1 2-1 3-2h1c2 0 3-1 5-2 1 0 3-2 4-3 2-3 3-5 4-8v-2c1-1 1-2 2-3 1-2 2-5 3-7 0 0 1-1 1-2 0 0 1 0 1-1v-2c1-1 4-1 6-2l2 1c0 1 1 1 1 3l-2 1v1h0l2 2v1h-2-3 0 0l1 1v1s0 1-1 1h-3v2l-1 1h2c0 1 1 2 2 2l-1 1c-1 1-4 3-4 4v6 1c0 1 0 3-1 5l-1 2c0 1-1 2-2 3 0 1-1 1-1 1h-1v1h-3c-2-1-2-1-3-2 0 1-2 1-2 2l-1 1c-2 1-3 2-5 1-1 2-2 3-4 4l-2-1-1-1c0-3-1-5 1-8l2-2z" class="m"></path><path d="M115 284c0-2 1-5 2-6h1c1 2 0 3 0 5l1 1h0-3-1z" class="K"></path><path d="M88 321l1-1v-3c0-1 1-2 2-3h0v2 1c-1 1 0 1 0 2 1-1 1-1 3-2v1c-1 2-2 3-4 4l-2-1z" class="B"></path><path d="M119 284l1 1v1s0 1-1 1h-3-1-1l1-3h1 3z" class="O"></path><path d="M115 284h1v3h3-3-1-1l1-3z" class="Z"></path><path d="M94 317c1-2 1-3 2-5 2 0 3 1 5 1h0l1 1c0 1-2 1-2 2l-1 1c-2 1-3 2-5 1v-1h0z" class="G"></path><path d="M97 316v-2h2c0 1-1 1-2 2z" class="Z"></path><path d="M99 314c1 1 1 1 1 2h0l-1 1c-2 1-3 2-5 1v-1h0 1c1 0 1 0 2-1s2-1 2-2z" class="J"></path><path d="M114 287h1 1v2l-1 1h2c0 1 1 2 2 2l-1 1c-1 1-4 3-4 4v6 1c0 1 0 3-1 5l-1 2c0 1-1 2-2 3 0 1-1 1-1 1h-1v1h-3c-2-1-2-1-3-2l-1-1 3-2v-3h1c1 1 1 1 2 1l1-1c0-1 3-5 4-5 0-1-1-2-1-3 0-4 1-9 2-13h1z" class="G"></path><path d="M117 290c0 1 1 2 2 2l-1 1c-1 1-4 3-4 4v6l-1-3c0-2 0-4 1-5 1-2 1-3 3-5zm-11 23h0c1 0 1-1 1-1 2-1 4-3 5-4 1-2 1-3 2-4 0 1 0 3-1 5l-1 2s-2 0-2 1l-2 2h-2v-1z" class="P"></path><path d="M106 313v1h2l2-2c0-1 2-1 2-1 0 1-1 2-2 3 0 1-1 1-1 1h-1v1h-3c-2-1-2-1-3-2l-1-1 3-2v1l1 1h1z" class="n"></path><path d="M120 255h1l1-1c1-1 4-1 6-1l1 1h-1c0 1 0 1 1 1l1 1 3 4c1-1 1-1 1-2 1 0 2 1 3 2v2 3s-1 2-2 2l-6 1h-1v1l1 1v1l1 1v2h1c1 3 3 6 4 9-1 0-1 0-2 1l-4-1h0-2l-1-1s0-1-1-2h-1l-2 1v-1l2-1c0-2-1-2-1-3l-2-1c2-1 2-1 2-3-1 0-2-1-3-1-1 1-2 2-3 2-1 1-1 2-2 3l-1-2c-1 1-2 1-3 2l-2-1-2 5c-1 0-1 1-2 2v1l1 4c0 2 0 3-1 4v1l-1 1h0l-1 1h-1l-1-1-3 3-2 1-2 1c-1-1-2-1-3-1v1c-3 0-5 0-8-1l-4-3c-1-1-2-1-2-2v-1h0v-3c1-1 1-1 3-1l1 1v-1c1-1 1-1 1-2s-1-2-1-4h1v1h3c0 1 1 1 1 2s0 1 1 2c0 0 1 1 1 2h1c-1-1 0-2 0-4l-2-1v-1h1c1 1 1 1 1 2l2 1c3 0 6 0 8-2 1-1 1-1 2-1h1l-1-1c1-1 1-2 2-2l3-4c1-2 3-4 5-6-1 0-1-1-2-1 2-1 2-2 3-3 1-2 1-4 2-6l2-2c0-1 1-1 1-2h3z" class="U"></path><path d="M124 257v2 1l-3-1c1-1 2-1 3-2z" class="c"></path><path d="M77 288c1-1 1-1 3-1l1 1c-2 1-2 2-4 3v-3z" class="q"></path><path d="M115 272c2 2 3-2 5-2v1c-1 1-2 2-3 2-1 1-1 2-2 3l-1-2s0-1 1-2zm-21 15h2s1 1 1 2 1 0 1 2l-1 1c-1-1-1-1-2-1s-1-1-1-1l1-2-1-1z" class="B"></path><path d="M109 275c1-1 3-3 4-5l1 1 1 1c-1 1-1 2-1 2-1 1-2 1-3 2l-2-1z" class="V"></path><path d="M121 259l3 1h-2v1h3v1c-1 0-1 1-2 1-1 1-1 1-1 2l-1 1v-1c-1-2-1-3 0-6z" class="G"></path><path d="M101 293v-2c1-1 1-3 2-4 1 1 2 3 2 4v1l-1 1h0l-1 1h-1l-1-1z" class="E"></path><path d="M103 287c1 1 2 3 2 4v1l-1 1h0c-1-1-2-4-1-6z" class="G"></path><path d="M112 265l1 2h1c1-1 1-1 1-2h0l-1-1c1 0 1-1 2-1s1-1 1-2c0 3 0 4 1 6v1h-3-1c-1 0-2 1-3 1s-1-1-2-1c2-1 2-2 3-3z" class="Q"></path><path d="M79 294c-1-1-2-1-2-2v-1c1 1 2 1 3 2h5c-1 1-1 2-1 3 2 1 4 1 5 1h1 1v1c-3 0-5 0-8-1l-4-3z" class="L"></path><path d="M124 257h3c1 1 2 2 2 3v1h1l-1 1v1 1l-1 1h0v-2h0-2l-1-1v-1h-3v-1h2v-1-2z" class="G"></path><path d="M124 259h0 3v1 1h1v2h-2l-1-1v-1h-3v-1h2v-1z" class="h"></path><path d="M117 255h3c-1 1-2 3-3 5v1c0 1 0 2-1 2s-1 1-2 1l1 1h0c0 1 0 1-1 2h-1l-1-2c1-2 1-4 2-6l2-2c0-1 1-1 1-2z" class="V"></path><path d="M82 285c0-1-1-2-1-4h1v1h3c0 1 1 1 1 2s0 1 1 2c0 0 1 1 1 2h-1 0v2c1 0 1-1 2-1 0 1 1 1 2 1v1h-3 0v2l-1 1-1-1c0-2 0-3-1-4l-2-2c0-1-1-1-1-2z" class="B"></path><path d="M101 281c1-1 1-2 2-2 0 1 0 2-1 4 0 2-2 3-2 6l-2 2c0-2-1-1-1-2s-1-2-1-2h-2c-1-1-4-1-5-3l2 1c3 0 6 0 8-2 1-1 1-1 2-1h1l-1-1z" class="H"></path><path d="M96 287c1-1 1-1 2-1 0 1 0 1 1 2l1 1-2 2c0-2-1-1-1-2s-1-2-1-2z" class="D"></path><path d="M133 260c1-1 1-1 1-2 1 0 2 1 3 2v2 3s-1 2-2 2l-6 1h-1-1l-1 1v-3c0-1 1-1 0-2v-1h2 0v2h0l1-1v-1-1l1-1h-1v-1c1 0 1 1 2 1h-1c0 2 0 3-1 4v1h2l2-1v-5h0z" class="H"></path><path d="M133 260c1 0 1 1 2 1 0 1 1 2 1 3-1 1-2 2-4 2 0 0 0 1-1 1h-1-2v-1l1-1v1h2l2-1v-5zm-10 3c1 0 1-1 2-1l1 1v1c1 1 0 1 0 2v3l1-1h1v1l1 1v1l1 1v2h1c1 3 3 6 4 9-1 0-1 0-2 1l-4-1h0-2l-1-1s0-1-1-2h-1l-2 1v-1l2-1c0-2-1-2-1-3l-2-1c2-1 2-1 2-3s-1-4-2-6l1-1c0-1 0-1 1-2z" class="E"></path><path d="M125 271h0c2 3 1 6 3 9v1l-1 2-1-1s0-1-1-2h-1l1-1c1-3 0-5 0-8h0z" class="D"></path><path d="M123 263c1 0 1-1 2-1l1 1v1c1 1 0 1 0 2v3l1-1h1v1l1 1v1l-2 1c0-1-1-3-1-4-1 1-1 2-1 3h0c-1-3-2-5-2-8z" class="B"></path><path d="M121 266l1-1c0-1 0-1 1-2 0 3 1 5 2 8h0c0 3 1 5 0 8l-1 1-2 1v-1l2-1c0-2-1-2-1-3l-2-1c2-1 2-1 2-3s-1-4-2-6z" class="c"></path><path d="M129 279h0c-1-1-1-1-1-2l1-1h0v-3l1 1h1c1 3 3 6 4 9-1 0-1 0-2 1l-4-1h0 0v-2l1-1-1-1z" class="R"></path><path d="M129 279v-2h1c1 0 1 0 2 2 0 1 1 3 1 4h-4 0 0v-2l1-1-1-1z" class="q"></path><path d="M124 280h1c1 1 1 2 1 2l1 1h2 0l4 1c1-1 1-1 2-1 0 2 1 4 2 6v1c1 1 1 1 1 2 0 0-1 0-2 1v1l-1 1v1h-1v1c3 3 5 5 9 7 1 1 2 1 3 2l6 3c2 1 4 4 5 6 0 3 1 5-1 7-1 0-1 1-3 1l-1-1c0 1 0 2 1 3-1 0-2 1-3 1-1-1-1-2-2-2v-1l-1-1c-1-1-1-1-2-1v1h-1c-1-2-3-3-4-4v-1h0c-1-1-2-2-1-4v-1h-1v1h-2 0c0 1 0 2 1 2 0 1 0 1-1 2v1c-1 0-2 1-2 1h-2 0l-2 1v1 2c-1 1-1 1-2 1h-1c0 1 0 1-1 1h-1v1c0 1-2 2-2 3h-1-2v1l-1 1c0-2 0-3-1-4l-1 1h-1c-1 0-1 0-2-1h0c-1 0-1 0-2-1v-1h-3-1 0-2c-1 0-2 0-3-1l1-1-4-1-1-1v-3-1l1-1c0-1 2-1 2-2 1 1 1 1 3 2h3v-1h1s1 0 1-1c1-1 2-2 2-3l1-2c1-2 1-4 1-5v-1-6c0-1 3-3 4-4l1-1c-1 0-2-1-2-2h-2l1-1v-2h3c1 0 1-1 1-1v-1l-1-1h0 0 3 2v-1l-2-2h0l2-1z" class="R"></path><path d="M129 290l1-1c2 1 3 5 4 7v1c-1-2-2-3-3-5 0 2 0 3 1 4v3c-2-3-2-6-3-9zm22 30c1-2 2-3 3-5 0-1-1-2-1-2-2-2-6-3-9-3v-1-1c3 1 9 3 11 6v2l-2 3c-1 0-1 1-2 1h0z" class="B"></path><path d="M124 280h1c1 1 1 2 1 2v1h-1c-1 2 0 9 0 11l1 1v2l-2 1c0 1 1 0 1 1 0 0-1 1-1 2 0-2 0-2-1-4h0c-1-2 1-1 1-3-1-1-1-1-2-1l1-2v-1l1-1v-4c-1-1-1-1-2 0h-2l-1-1h0 0 3 2v-1l-2-2h0l2-1z" class="H"></path><path d="M120 285h2c1-1 1-1 2 0v4l-1 1v1l-1 2c-1 0-2 1-2 1l-2-1 1-1c-1 0-2-1-2-2h-2l1-1v-2h3c1 0 1-1 1-1v-1z" class="l"></path><path d="M119 292l1-1c1 0 2-1 3 0l-1 2c-1 0-2 1-2 1l-2-1 1-1z" class="K"></path><path d="M120 285h2c1-1 1-1 2 0h-1c0 2-1 3-3 5l-4-1v-2h3c1 0 1-1 1-1v-1z" class="D"></path><path d="M139 313c1 2 3 4 5 5 1 0 2-1 2-2l1-1h0l1 5h3 0v2h1c0 1 0 2 1 3-1 0-2 1-3 1-1-1-1-2-2-2v-1l-1-1c-1-1-1-1-2-1v1h-1c-1-2-3-3-4-4v-1h0c-1-1-2-2-1-4z" class="J"></path><path d="M151 322h1c0 1 0 2 1 3-1 0-2 1-3 1-1-1-1-2-2-2l1-2h2z" class="Z"></path><path d="M135 283c0 2 1 4 2 6v1c1 1 1 1 1 2 0 0-1 0-2 1v1l-1 1v1h-1c-1-2-2-6-4-7l-1 1c0-1 0-2 1-3h2l1-1v-1c1 0 0-1 0-1 1-1 1-1 2-1z" class="E"></path><path d="M137 290c1 1 1 1 1 2 0 0-1 0-2 1 0-1-2-1-3-1h0v-2h4z" class="R"></path><path d="M129 290c0-1 0-2 1-3h2v1h1v2 2h0c1 0 3 0 3 1v1l-1 1v1h-1c-1-2-2-6-4-7l-1 1z" class="H"></path><path d="M133 292c1 0 3 0 3 1v1h-1c-1 0-1-1-2-2z" class="E"></path><defs><linearGradient id="BE" x1="134.372" y1="287.689" x2="128.596" y2="301.517" xlink:href="#B"><stop offset="0" stop-color="#b3b3b3"></stop><stop offset="1" stop-color="#e3e2e1"></stop></linearGradient></defs><path fill="url(#BE)" d="M126 282l1 1h2 0l4 1s1 1 0 1v1l-1 1h-2c-1 1-1 2-1 3 1 3 1 6 3 9 2 2 4 4 5 8-10-7-8-13-11-23h0v-1-1z"></path><path d="M126 282l1 1h2 0l4 1s1 1 0 1c0 1 0 1-1 1-1 1-1 0-2 0-1-1-2-2-3-2h-1v-1-1zm-2 19c0-1 1-2 1-2 0-1-1 0-1-1l2-1 1 2c0 2-1 4 0 5 0 2 5 6 7 7l2 1v1h0c0 1 0 2 1 2 0 1 0 1-1 2v1c-1 0-2 1-2 1h-2 0c-2-2-5-3-8-5v-2l1-1c1 1 1 1 2 1h1c-1-1-1-2-2-3h-1c0-1 0-1 1-2h0c-1-2-1-3-1-4v-2h-1z" class="L"></path><path d="M128 312h0c0 1 0 1 1 2 0 0 0 1 1 2v1h2l-1-1h1l-1-1c1 0 2-1 3 0h1 0 1v2 1c-1 0-2 1-2 1h-2 0c-2-2-5-3-8-5v-2l1-1c1 1 1 1 2 1h1z" class="H"></path><path d="M132 319v-3c2-1 1 0 2 0s2 0 2-1v2 1c-1 0-2 1-2 1h-2z" class="B"></path><path d="M114 303v-6c0-1 3-3 4-4l2 1s1-1 2-1 1 0 2 1c0 2-2 1-1 3h0c1 2 1 2 1 4h1v2c0 1 0 2 1 4h0c-1 1-1 1-1 2h1c-1 1-1 1-3 1-1 0-2 0-2-1h-1l-2 2c0 1 1 2 1 3-1 1-1-1-2-1-1-1-1-1-1-3l-3-1c1-2 1-4 1-5v-1z" class="I"></path><path d="M123 297c1 2 1 2 1 4h1v2c0 1 0 2 1 4h0c-1 1-1 1-1 2v-1c-2-1-2-8-2-11z" class="C"></path><path d="M126 309c1 1 1 2 2 3h-1c-1 0-1 0-2-1l-1 1v2c3 2 6 3 8 5l-2 1v1 2c-1 1-1 1-2 1h-1c0 1 0 1-1 1h-1v1c0 1-2 2-2 3h-1-2v1l-1 1c0-2 0-3-1-4l-1 1h-1c-1 0-1 0-2-1h0c-1 0-1 0-2-1v-1h-3-1 0-2c-1 0-2 0-3-1l1-1-4-1-1-1v-3-1l1-1c0-1 2-1 2-2 1 1 1 1 3 2h3v-1h1s1 0 1-1c1-1 2-2 2-3l1-2 3 1c0 2 0 2 1 3 1 0 1 2 2 1 0-1-1-2-1-3l2-2h1c0 1 1 1 2 1 2 0 2 0 3-1z" class="m"></path><path d="M126 309c1 1 1 2 2 3h-1c-1 0-1 0-2-1l-1 1-1-1 1-1v1l-1-1c2 0 2 0 3-1z" class="E"></path><path d="M119 324c1 0 2-1 3-2 0 0 0-1 1-1l2 2c-1 2-2 2-4 3-1 0-2 0-2-1v-1z" class="G"></path><path d="M125 323c1 1 1 1 2 1 0 1 0 1-1 1h-1v1c0 1-2 2-2 3h-1-2v1l-1 1c0-2 0-3-1-4l-1 1h-1c-1 0-1 0-2-1 1 0 2 0 3-2h2c0 1 1 1 2 1 2-1 3-1 4-3z" class="n"></path><path d="M100 316c0-1 2-1 2-2 1 1 1 1 3 2h3v-1h1c0 2-1 4-2 5l-1 1c-1 0-1-1-2-2v-2h-1c-1 2-2 2-4 2v2-3-1l1-1z" class="C"></path><path d="M104 323c2 1 5 1 7-1 1 0 1-1 2-2v1c1 2 3 2 4 3h2v1h-2c-1 2-2 2-3 2h0c-1 0-1 0-2-1v-1h-3-1 0-2c-1 0-2 0-3-1l1-1z" class="K"></path><path d="M117 324h2v1h-2c-1 2-2 2-3 2h0c-1 0-1 0-2-1v-1h-3-1 1c1 0 2 0 3-1 1 1 3 1 5 1v-1z" class="Q"></path><path d="M94 318c2 1 3 0 5-1v1 3l1 1 4 1-1 1c1 1 2 1 3 1h2 0 1 3v1c1 1 1 1 2 1h0c1 1 1 1 2 1h1l1-1c1 1 1 2 1 4l1-1v-1h2 1c0-1 2-2 2-3v-1h1l1 2 1 1h-1v3 4c0 3 1 6 2 9-1 2-1 4-2 6-3-1-6-2-10-2l-1 1c-2 1-3 3-4 5v1c0 1 0 2 1 3l-1 1 2 1c2 3 5 7 4 10 1 1 1 2 1 4v4 1c-1 1-1 1-1 2h-2c-2 1-1 2-3 1l-2 2-1-1v-1h-1c-1 0-1-1-2-1v-2h-1v1c-1-1-1-1-1-3 1-2 3-1 5-3 1 0 1-2 1-3v-1l-2-1c-1-1-2-1-3 0h-2c0-1-1-2-1-2l-1-1c-1 1-1 2-2 2h-1c-1 0-2-2-3-2-1-1-1-1-2 0l-1-1v-3l1 1c1 1 3 2 5 3l1-1c0-1-1-1-1-2-1-2-3-5-5-6h0c0-1-1-1-1-2-1 1-1 1-2 1-1-1-1-2-2-3 1-1 1-2 1-2 1-1-1-2-1-3-2-1-2-1-3-1v-3h1c2-2 3-5 3-8l1-1c-1 0-2-1-3-1l3-3 1-2c1-1 2-2 2-3v-1-2h-3 1l-1-1h-1c2-1 3-2 4-4z" class="E"></path><path d="M97 332h1s0 1 1 1v3h-1c0-1 0-1-1-2h0v-2z" class="I"></path><path d="M108 325h1 3v1c1 1 1 1 2 1h0c1 1 1 1 2 1h1l1-1c1 1 1 2 1 4 1 0 2 1 1 2h-1-3-1s-1 0-2-1c-1 1-1 3-2 4h-1v1h0 0l-1 1h-1c-1 0-1 0-2-1s-2-1-2-2v-1h1l-1 1h1l1-1v-3l-1-2c2 0 2-2 3-4h0z" class="B"></path><path d="M114 327h0c1 1 1 1 2 1h1v2 1c-1 1-1 1-2 1-1-1-1-1-2-1h-3v1l-1 1v-3s0-1 1-1h2c0-1 1-1 2-2z" class="F"></path><path d="M114 327h0c1 1 1 1 2 1h1v2 1h-1v-2c-1 0-2 1-2 1h-2v-1c0-1 1-1 2-2z" class="D"></path><path d="M108 325h1 3v1c1 1 1 1 2 1-1 1-2 1-2 2h-2c-1 0-1 1-1 1v3l-1 3h-1 0v-5h-1l-1-2c2 0 2-2 3-4h0z" class="C"></path><path d="M109 325h3v1c-1 1-1 2-3 2h-1c0-1 0-2 1-3z" class="B"></path><path d="M94 318c2 1 3 0 5-1v1 3l1 1 4 1-1 1c1 1 2 1 3 1h2c-1 2-1 4-3 4-2-1-1 0-3 0l-1-1-1-1h0 2v-1h-1-2v3c-1 1-2 1-3 2l-1 1h0l1 1v1c-1 1-2 1-3 1h-1-1c-1 0-2-1-3-1l3-3 1-2c1-1 2-2 2-3v-1-2h-3 1l-1-1h-1c2-1 3-2 4-4z" class="F"></path><path d="M91 331c1 0 2 0 3 1v2c-1 0-2 0-2 1h-1c-1 0-2-1-3-1l3-3z" class="Q"></path><path d="M100 322l4 1-1 1c1 1 2 1 3 1h2c-1 2-1 4-3 4-2-1-1 0-3 0l-1-1v-1c1 0 1 0 1-1 1 0 1 0 1-1-1 0-1-1-1-1l-2-2h0z" class="D"></path><path d="M94 318c2 1 3 0 5-1v1 3l1 1h0l-3 5h-1l-1 1 1 1h0c-2 0-3 1-4 0 1-1 2-2 2-3v-1-2h-3 1l-1-1h-1c2-1 3-2 4-4z" class="P"></path><path d="M94 318c2 1 3 0 5-1v1h-1v4l-2 1v-1l-2 4h0v-1-2h-3 1l-1-1h-1c2-1 3-2 4-4z" class="K"></path><path d="M92 323l3-3 1 1v1l-2 4h0v-1-2h-3 1z" class="H"></path><path d="M120 330v-1h2 1c0-1 2-2 2-3v-1h1l1 2 1 1h-1v3 4c0 3 1 6 2 9-1 2-1 4-2 6-3-1-6-2-10-2l-1 1c-2 1-3 3-4 5v1c0 1 0 2 1 3l-1 1h0c-1-2-1-3-1-5l-1 1h0l-2-3s-1 0-1-1c1-1 2-3 4-5h-1c0-1-1-1-1-2h1c2 0 7 0 9-1s3 0 5-2c0-1-1-2 0-3v-1l-1-1c-1 1-1 1-1 2-1 1-2 3-4 2v-1h-1v2h-2l-2-2v-3l2-2v-1h1 3 1c1-1 0-2-1-2l1-1z" class="F"></path><path d="M120 330l6 5v1c-1 0-1-1-2-2h-3c-1 0-2 0-3 1-1 0-2 0-3-1v-1h1 3 1c1-1 0-2-1-2l1-1z" class="D"></path><path d="M113 336l2-2c1 1 2 1 3 1 1-1 2-1 3-1v2c0 2-3 2-3 4v-1h-1v2h-2l-2-2v-3z" class="B"></path><path d="M113 336l2-2c1 1 2 1 3 1-1 1-2 2-3 2h-1l-1-1z" class="C"></path><path d="M111 346l1 1h0 4 1l-2 1c-2 2-3 4-4 6l-1 1h0l-2-3s-1 0-1-1c1-1 2-3 4-5z" class="R"></path><path d="M89 348h0 1c1 0 2-1 3-2 0-1 0-1 1-2 0-1 0-1 1-1 1-2 2-4 4-4h0c1-1 2-1 4-1 0 1 1 2 3 3h0 0c0 1 1 2 1 2 1 0 2 0 3 1h0-1c0 1 1 1 1 2h1c-2 2-3 4-4 5 0 1 1 1 1 1l2 3h0l1-1c0 2 0 3 1 5h0l2 1c2 3 5 7 4 10 1 1 1 2 1 4v4 1c-1 1-1 1-1 2h-2c-2 1-1 2-3 1l-2 2-1-1v-1h-1c-1 0-1-1-2-1v-2h-1v1c-1-1-1-1-1-3 1-2 3-1 5-3 1 0 1-2 1-3v-1l-2-1c-1-1-2-1-3 0h-2c0-1-1-2-1-2l-1-1c-1 1-1 2-2 2h-1c-1 0-2-2-3-2-1-1-1-1-2 0l-1-1v-3l1 1c1 1 3 2 5 3l1-1c0-1-1-1-1-2-1-2-3-5-5-6h0c0-1-1-1-1-2-1 1-1 1-2 1-1-1-1-2-2-3 1-1 1-2 1-2 1-1-1-2-1-3z" class="B"></path><path d="M110 355c0 1 0 1-1 1h-1c-1-1-1-1-1-3h0l1-1 2 3zm-2 2l2 1c1 2 2 3 4 5h-1-1v-1c-2-1-3-2-5-4 0 0 1 0 1-1z" class="F"></path><path d="M102 343h0 0v-1c1-1 2-1 4-1 0 1 1 2 1 2 0 2 0 2-2 3h-2l-1-3z" class="m"></path><path d="M102 343h2l1 1-1 1-1 1-1-3z" class="E"></path><path d="M106 379l1-2c1 0 1-1 3 0 1 1 1 2 2 4-1 1-1 1-2 1h-1c-1 0-1-1-2-1v-2h-1z" class="R"></path><path d="M119 378c-1 0-1 1-2 1-3 0-1-4-2-6v-3l1-1 2 1c1 1 1 2 1 4v4zm-32-20c1 0 1 0 2 1s3 2 4 3v3l1 1c1-1 1-1 2 0 1 0 2 2 3 2h1c1 0 1-1 2-2l1 1s1 1 1 2h2c1-1 2-1 3 0l2 1v1c0 1 0 3-1 3-2 2-4 1-5 3 0 2 0 2 1 3v-1h1v2c1 0 1 1 2 1h1v1l1 1 2-2v1c1 1 2 0 3 1h0c0 1 1 2 2 3 3 0 5-1 8 1l2-1h0l-1-2c1 0 1-1 2-2 1 1 2 0 3 1l1 1h0c0 1 1 1 2 1v1l2 1c0 2-1 4-2 6-1 0-1 0-1 1 0 0-1 0-1 1l-1 1-2 1c0 1-1 3-2 4-1 2-1 4-3 5-2 2-3 4-5 5l-3 2c-1 1-2 1-3 1h0c-1 0-2 1-2 1-1 1-2 1-3 1h-2-4c-1-1-2-1-3-1 0-1-1-1-2-2-1 0-2 0-3-1h0c-9-7-14-13-16-25 1 1 1 1 1 2h1c-2-12 0-21 6-31v-1z" class="U"></path><path d="M104 369h2v1l-3 2h0l-1-2c1-1 1-1 2-1z" class="Q"></path><path d="M106 369c1-1 2-1 3 0l2 1v1c-2-1-3-1-5-1v-1z" class="D"></path><path d="M105 394h1c1 1 1 1 1 2h1 0v-2h1l1 2v1h-2c-1 0-2-1-2-1l-1-2z" class="C"></path><path d="M102 393h1v-3c1 0 2 1 3 2v1h-1-1c0 1-1 1-1 2h0c-1-1-1-1-1-2z" class="H"></path><path d="M102 400h2 0s1 1 1 2v-1h2c1 1 0 1 0 2v1c-1 1-2 1-3 1 0-1 0-1-1-2h1v-1c0-1-1-1-2-2z" class="E"></path><path d="M111 394c1 0 1 1 2 2v1c-1 0-2 1-4 1 1 1 1 1 2 1-1 0-1 1-2 0 0 0-1 0-1-1h0s-1 0-1-1c-1 0-2 0-2-1v-2l1 2s1 1 2 1h2v-1l1-1v-1z" class="I"></path><path d="M107 403l2 1v1l-3 3h-1l-2-2-1-2 1-1c1 1 1 1 1 2 1 0 2 0 3-1v-1z" class="H"></path><path d="M93 385c0 1 1 1 1 2h0v-1-1c0-1 1-1 1-1v1c1 0 1 1 2 1v4 1 2c-1 0-1 0-2-1l-2-7z" class="B"></path><path d="M95 385c1 0 1 1 2 1v4c-1-1-2-3-2-5h0z" class="F"></path><path d="M101 379h1v3c0 1 1 3 2 4-1 1-1 0-2 0s-2-1-2-1c-1-1-1-1-2-1v-2l3-3z" class="D"></path><path d="M125 392v-1-3l-4-1c-1 1-1 1-1 2h0-1c-1 1-1 1-2 1v-1-1l1-1-1-1-1 1c-1 0-2-1-2-2h0l2-1c0 1 1 2 2 3 3 0 5-1 8 1v2 1 2h0c-1 0-1 0-1-1z" class="F"></path><path d="M111 394c-2-2-3-3-4-6h1c2 1 5 3 7 4s2 2 3 3c0 1-1 2-2 3s-3 0-5 1c-1 0-1 0-2-1 2 0 3-1 4-1v-1c-1-1-1-2-2-2z" class="D"></path><path d="M97 386c1 1 1 1 1 3v1c1 2 1 3 3 4 0 0 1 0 1-1 0 1 0 1 1 2h0c0-1 1-1 1-2h1c-1 1-1 1-1 2v1c1 1 0 2 0 4h-2c-2-1-6-3-7-6v-2c1 1 1 1 2 1v-2-1-4z" class="I"></path><path d="M102 404c0 1-1 1-1 1-1 0-3-1-3-1-3-2-6-5-7-7-3-4-4-8-5-12s-1-7 0-11h0c1-4 2-5 4-8 1-1 1-1 2-1 0 1 1 2 1 2v1 1h1c1 0 1 0 2 1h1 1c1 2 1 2 0 3v1h1c-1 2-2 4-3 5h-1v-1h-3c-1 1-1 2-1 3s1 3 2 4h0l2 7v2c1 3 5 5 7 6 1 1 2 1 2 2v1h-1l-1 1z" class="q"></path><path d="M95 394l-2-2c-4-6-5-15-3-22 0-1 0-1 1-1h1c-1 1-1 1-1 2 2 2 3 3 7 3h0 1c-1 2-2 4-3 5h-1v-1h-3c-1 1-1 2-1 3s1 3 2 4h0l2 7v2z" class="n"></path><path d="M81 390c-2-12 0-21 6-31 1 1 1 1 1 2v2 1h2 0v1c-1 1-2 0-3 1l-1 3c-3 8-2 18 2 25l1 1c1 3 4 6 6 8 1 0 2 1 3 2l2 2c1 0 2 0 3-1l2 2h1l3-3 2-1c2 0 3-2 5-2 1-1 2-2 3-2 2-4 2-8 2-12h2c1 0 1 1 2 1v2h-1v1 2h-1v-3h-1c0 1 0 2 1 3h2v1-1l-1-1 1-1c0 1 0 1 1 1h0v-2-1-2l2-1h0l-1-2c1 0 1-1 2-2 1 1 2 0 3 1l1 1h0c0 1 1 1 2 1v1l2 1c0 2-1 4-2 6-1 0-1 0-1 1 0 0-1 0-1 1l-1 1-2 1c0 1-1 3-2 4-1 2-1 4-3 5-2 2-3 4-5 5l-3 2c-1 1-2 1-3 1h0c-1 0-2 1-2 1-1 1-2 1-3 1h-2-4c-1-1-2-1-3-1 0-1-1-1-2-2-1 0-2 0-3-1h0c-9-7-14-13-16-25 1 1 1 1 1 2h1z" class="R"></path><path d="M103 406l2 2h1c-1 0-1 0-2 1-1 0-2-1-4-2 1 0 2 0 3-1z" class="F"></path><path d="M127 391c0 1 0 2 1 3v2l-1 1c-1-1-2-1-3-1h0l1-1v-1l-1-1 1-1c0 1 0 1 1 1h0v-2h1z" class="I"></path><path d="M128 396h2 1l-1 2c0 1-1 3-2 4-1 2-1 4-3 5 0 0 0-1 1-1v-1c-1 0-1-1-1-1l1-1v-3c0-1 1-2 1-3l1-1z" class="K"></path><path d="M125 404s0 1 1 1v1c-1 0-1 1-1 1-2 2-3 4-5 5-2-1-3 0-4-1v-1h2c2-2 4-3 4-5v-1h2 1z" class="G"></path><path d="M127 385c1 0 1-1 2-2 1 1 2 0 3 1l1 1h0c0 1 1 1 2 1v1l2 1c0 2-1 4-2 6-1 0-1 0-1 1 0 0-1 0-1 1l-1 1-2 1 1-2h-1-2v-2c-1-1-1-2-1-3h-1v-1-2l2-1h0l-1-2z" class="D"></path><path d="M131 387h1c0 1 1 1 2 2l-1 1h-1l-2-2 1-1z" class="H"></path><path d="M131 387l-1-1 1-1h2c0 1 1 1 2 1v1h-3-1z" class="E"></path><path d="M129 393c1-1 1-2 2-2s2 1 2 1c1 0 1 1 2 2-1 0-1 0-1 1 0 0-1 0-1 1v-1c-1-1-2-1-4-2z" class="B"></path><path d="M128 393h1c2 1 3 1 4 2v1l-1 1-2 1 1-2h-1-2v-2-1z" class="Q"></path><path d="M127 385c1 0 1-1 2-2 1 1 2 0 3 1l1 1h0-2l-1 1 1 1-1 1v2l-1-1v-2h0c0-1 0-1-1-1v1l-1-2z" class="F"></path><path d="M128 387v-1c1 0 1 0 1 1h0v2l1 1c-1 1-1 2-2 3v1c-1-1-1-2-1-3h-1v-1-2l2-1h0z" class="C"></path><path d="M126 388l2-1c0 2 0 3-1 4h-1v-1-2z" class="E"></path><path d="M79 388c1 1 1 1 1 2h1c1 2 2 5 3 7 2 4 8 11 12 14 4 1 9 2 13 1 2-1 5-3 7-2h0v1c1 1 2 0 4 1l-3 2c-1 1-2 1-3 1h0c-1 0-2 1-2 1-1 1-2 1-3 1h-2-4c-1-1-2-1-3-1 0-1-1-1-2-2-1 0-2 0-3-1h0c-9-7-14-13-16-25z" class="V"></path><path d="M154 219c1 2 3 6 3 8 1 2 0 7 2 8l2 2c3-1 6 1 9-1h1 0c1 0 1 0 1-1v4l-3 4c1 1 2 1 3 0h1 1c0 5-1 9-1 13-1 5-1 9-2 13l1 3h0c0 7 0 14-1 20v21 10l1 83 1 22 3 29h-1c0 1 0 1-1 1 0-1-1-2-1-3 0 0-1 0-1 1-2-3-5-7-8-8-5-2-7-1-12 1-1 0-2 0-2-1h-1c1-1 1-3 0-4l1-1v-1h-1l-1-1c-1-1-1-2-1-2-2 0-3 0-4-1v-1h1c0-1-1-1-2-1l-1-2h-1-1l1-2-2-1 1-1v-1l-1-1h0l1-1-3-3c-1-1-1-3-2-3-1-1-2-2-2-3h1v-1h0 1v1-1c2 1 3 4 5 5h0l1-2c0-1 1-1 2-1 4-2 8-3 11-4l3-2v-1-1h-2l-1-1v-3c3-1 2 1 5 1h1c1-1 0-3 0-4v-1l-4 1c-1 0-2 0-3 1h-2-1v1c-6 0-12-1-18 0h-1v-2-1h-1l-1-1c1-1 2-3 2-4l2-1 1-1c0-1 1-1 1-1 0-1 0-1 1-1 1-2 2-4 2-6l-2-1v-1c-1 0-2 0-2-1h0l-1-1c-1-1-2 0-3-1-1 1-1 2-2 2l1 2h0l-2 1c-3-2-5-1-8-1-1-1-2-2-2-3h0c-1-1-2 0-3-1v-1c2 1 1 0 3-1h2c0-1 0-1 1-2v-1-4c0-2 0-3-1-4 1-3-2-7-4-10l-2-1 1-1c-1-1-1-2-1-3v-1c1-2 2-4 4-5l1-1c4 0 7 1 10 2 1-2 1-4 2-6-1-3-2-6-2-9v-4-3h1l-1-1-1-2c1 0 1 0 1-1h1c1 0 1 0 2-1v-2-1l2-1h0 2s1-1 2-1v-1c1-1 1-1 1-2-1 0-1-1-1-2h0 2v-1h1v1c-1 2 0 3 1 4h0v1c1 1 3 2 4 4h1v-1c1 0 1 0 2 1l1 1v1c1 0 1 1 2 2 1 0 2-1 3-1-1-1-1-2-1-3l1 1c2 0 2-1 3-1 2-2 1-4 1-7-1-2-3-5-5-6l-6-3c-1-1-2-1-3-2-4-2-6-4-9-7v-1h1v-1l1-1v-1c1-1 2-1 2-1 0-1 0-1-1-2v-1c-1-2-2-4-2-6-1-3-3-6-4-9h-1v-2l-1-1v-1l-1-1v-1h1l6-1c1 0 2-2 2-2v-3-2-3h-1c0-1 1-2 2-2v-1h2 0l2-2c1-2 2-2 3-3v-2h0c0-1 1-3 1-4l1-4c-1-1-1-3-1-4-1-2-1-4-2-6h2l1-1 2-1h0l4-2 1-1-2-1h0v-1c1-1 1-2 2-3z" class="q"></path><path d="M163 344h1l1 4c-1 1-2 1-4 2 0 0-1 0-1-1s2-1 3-1v-4z" class="E"></path><path d="M155 348h1c1 0 2 0 3-1v1c-1 2 0 3 0 5l-1 2c0-2-1-5-3-6v-1z" class="c"></path><path d="M161 383l2 1c0 1-1 3 0 4 1 2 2 3 3 5h-1 0c-1-2-3-3-4-5v-1-4z" class="P"></path><path d="M149 348c1-1 1-2 2-3h1c0 1 0 1 1 2s1 1 2 1v1h-2v1l1 3h0l-1-1c0-1-1-2-2-2h0-1l-1-2z" class="H"></path><path d="M150 419c-1 1-2 2-3 4l-3 2 2 1c1-1 2-3 4-4-1 2-3 4-4 5-1 0-3-1-4-2l3-2h0-3s0 1-1 1l-2-2h0 1c4 0 7-2 10-3z" class="E"></path><path d="M161 360c0-1 1-2 1-3h1 1 0c-1 2 0 4-1 6v2 3c1 1 1 1 2 1 1 1 0 3 0 4h0c-1 0-1-1-1-2-1 0-1 0-1 1l-1-9c-1-1-1-2-1-3z" class="B"></path><path d="M153 415l4 1c0 1 0 1-1 1l-6 2c-3 1-6 3-10 3h-1l1-2c0-1 1-1 2-1 4-2 8-3 11-4zm6-62l2 7c0 1 0 2 1 3 0 1 1 9 1 9v7h2 1l-1 1h-2v4l-2-1c1-6 1-11 0-16l-3-12 1-2z" class="D"></path><path d="M156 326c2 1 4 1 5 4 0 1 1 2 1 4l-2 3 2 4c0 1 0 2-1 3h-2c-1-2 2-7 1-8-2-1-4 0-5 0h-2v-3c2 0 3-1 4-1h3v-3c-1-2-3-2-5-2l-1-1h2z" class="L"></path><path d="M171 269l1 3h0c0 7 0 14-1 20v21 10-3h-1 0v-3-9l1-24v-15z" class="F"></path><path d="M153 308c2 1 4 3 6 5h0v-1h1c1 1 1 2 2 3s0 4 0 6c-2 0-3 1-4 2-1 0-3 1-5 1v1c-1-1-1-2-1-3l1 1c2 0 2-1 3-1 2-2 1-4 1-7-1-2-3-5-5-6l1-1z" class="I"></path><path d="M159 313h0v-1h1c1 1 1 2 2 3l-1 1c0 1 0 2-1 4l-1-1c1-2 0-4 0-6h0z" class="E"></path><path d="M168 397v5 2c1 4 0 11-3 14v1c-2-1-2-2-4-2-1 0-3 1-4 0h-1c1 0 1 0 1-1l-4-1 3-2 3-1c-1 1-2 2-2 3l1 1c1 0 2 0 2-1 2-1 3-2 5-4l2-7c1-2 1-4 1-7z" class="I"></path><path d="M165 411c0 1 0 3-2 4l-1 1h0l-2-1c2-1 3-2 5-4z" class="C"></path><path d="M150 422c3-1 6-1 9 0 1 0 1 1 2 1l1 1v1l-2 2c-3 0-5-1-7 1-1 0-1 1-2 1 0 1 1 1 1 2-1-1-2-1-3-2h1l-1-1v1h-2c-1-1-1-1-1-2 1-1 3-3 4-5z" class="S"></path><path d="M151 429v-1c0-1 0-1-1-1h0v-1h2v-1c1-1 2-1 3-1-1 1 0 1 0 2 0 0-2 1-2 2-1 0-1 1-2 1z" class="D"></path><path d="M155 424h1c1-1 1-1 2 0v1l2-1 1-1 1 1v1l-2 2c-3 0-5-1-7 1 0-1 2-2 2-2 0-1-1-1 0-2z" class="H"></path><path d="M155 349c2 1 3 4 3 6l3 12c1 5 1 10 0 16v4h0c-1-1-1-2-1-3-1-7 0-15-2-21-1-4-2-7-4-10l-1-3v-1h2z" class="m"></path><path d="M164 252l2-1c0 1 2 1 2 2s2 4 2 4c-1 2-3 4-4 5l-2 2h-1c-1-2-2-4-4-6h1c-2-2-3-3-3-5l1-2c1 0 2 1 4 1h2z" class="B"></path><path d="M164 264v-3h1l1 1-2 2z" class="R"></path><path d="M162 252h2c1 0 2 1 2 2h-2c-1 0-2 0-2-1v-1zm-5 1c1 0 1 0 1 1l2 2c1 0 2 1 2 1v2h0c-1 0-1-1-2-1-2-2-3-3-3-5z" class="m"></path><path d="M160 427l2-2v1c0 1-1 2-2 3h5c1 0 1 0 2 1l1 1 2 1h-2c-1 0-3 3-4 4s-2 1-2 2v1h0-2c-1-1-2-1-2-3h0-1c-1-2-3-4-5-5h0c0-1-1-1-1-2 1 0 1-1 2-1 2-2 4-1 7-1z" class="C"></path><path d="M163 430h4l1 1c-2 0-5 1-7 2h-1c1 0 1-1 1-1 1-1 2-1 2-2z" class="G"></path><path d="M160 427l2-2v1c0 1-1 2-2 3h5c1 0 1 0 2 1h-4-1c-1-1-2 0-4-1l2-2z" class="B"></path><path d="M146 303l2 1c1 0 1-1 2-1h0 2c1-1 1-1 1-2v-1h0v-1h2c1 0 2 0 2-1l3 1h2v1c1 1 3 2 3 4 0 1 0 1-1 2l-1 2h0c1 1 2 1 2 2l-1 2c-1 0-1-1-2-1h0l-8-5c-1-1-2-1-3-1 0-1-1 0-2 0h0c1 2 3 2 4 3l-1 1-6-3v-2-1z" class="E"></path><path d="M154 306v-1l1 1 1-1h0c0 1 1 1 2 2 1 0 2 1 4 2v2l-8-5z" class="B"></path><path d="M170 320h0 1v3l1 83 1 22 3 29h-1c0 1 0 1-1 1 0-1-1-2-1-3v-2l1 1v-2c-2-9-2-19-2-28l-2-104z" class="C"></path><path d="M174 452l1 5c0 1 0 1-1 1 0-1-1-2-1-3v-2l1 1v-2z" class="E"></path><path d="M166 275v4c1 2 0 4 1 6v2h0-1v3c-1 1-1 2-1 3h0 2c0 2-1 3-1 4-1 1-2 2-4 2h-2c-1-1-3-2-4-2l1-1c-1 0-1-1-1-2l1-1h-1c1-1 1-1 1-2l1-1-2-2h1c1 0 1-1 2-1 1-2-1-4 0-6 0-1 1-4 1-5h1l1-1h3 1z" class="m"></path><path d="M166 275v4c-1-1-1-2-1-4h1z" class="I"></path><path d="M158 290h0c1 1 1 1 2 1l1 1c-1 1-3 1-4 1h-1c1-1 1-1 1-2l1-1zm-1 6l1 1h2c2 0 3-1 4-3 1-1 0-1-1-2 1-1 1-1 1-2-1 0-1-1-1-1 1-2 0-3-1-4h1c2 2 2 3 3 5-1 1-1 2-1 3h0 2c0 2-1 3-1 4-1 1-2 2-4 2h-2c-1-1-3-2-4-2l1-1z" class="H"></path><path d="M171 236h0c1 0 1 0 1-1v4l-3 4c1 1 2 1 3 0-2 3-3 7-6 8l-2 1h-2c-2 0-3-1-4-1l-1 1c0-1 1-2 1-3l-1-1h-1l-1 1h0c0-3 3-6 3-8 1-2 0-4 1-4h2 0c3-1 6 1 9-1h1z" class="C"></path><path d="M159 241c0-1 0-1 1-1s2 1 3 1c-1 1-1 1-2 1v1 1h-2v-3z" class="E"></path><path d="M171 236h0c1 0 1 0 1-1v4l-3 4c-1 2-3 4-5 4-1 0-3 0-3-1l-2-2h2c1 1 2 1 3 1 2 0 3-4 3-5l3-3h1v-1z" class="R"></path><path d="M158 241h1v3l2 2c0 1 2 1 3 1 2 0 4-2 5-4 1 1 2 1 3 0-2 3-3 7-6 8l-2 1h-2c-2 0-3-1-4-1l-1 1c0-1 1-2 1-3l-1-1h-1l-1 1h0c0-3 3-6 3-8zm-7 48h1 2v-1h2l2 2-1 1c0 1 0 1-1 2h1l-1 1c0 1 0 2 1 2l-1 1c1 0 3 1 4 2l-3-1c0 1-1 1-2 1h-2v1h0v1c0 1 0 1-1 2h-2 0c-1 0-1 1-2 1l-2-1v1 2c-1-1-2-1-3-2-4-2-6-4-9-7v-1h1v-1l1-1h2 1c1-1 0-1 1-3h0c0-1 0-1-1-2l1 1h1 1l1-1h0c1 0 2 1 3 0h5z" class="F"></path><path d="M144 297h0c1 0 1 1 1 1l-1 1-1-1 1-1z" class="B"></path><path d="M141 298h1c1 1 0 1 0 2l-1-1h0v-1z" class="D"></path><path d="M143 304h1c-1-1-1-1-2-1v-2c1 0 1 0 2 1 1 0 1 1 2 1v1 2c-1-1-2-1-3-2z" class="I"></path><path d="M138 294h1v1c1 1 1 1 2 1-1 1-1 1-1 2-2 0-3-1-4-2h-1v-1l1-1h2z" class="L"></path><path d="M136 294h2v1c-1 1-1 1-2 1h-1v-1l1-1z" class="K"></path><path d="M156 293h1l-1 1c0 1 0 2 1 2l-1 1c-1-1-3-1-3-1l-1 1c-1 0-1-1-2-1l-1 1h-1l1-2c1-1 0-1 1-2l2 1c1 0 2 0 3-1h1z" class="c"></path><path d="M150 293l2 1c0 1-1 1-2 2h0l-1 1h-1l1-2c1-1 0-1 1-2z" class="G"></path><path d="M140 291h0c0-1 0-1-1-2l1 1h1 1c1 1 2 1 3 3 1-1 2 0 4 1v1l-1 2c-1 0-2 0-2-1h-2v-1c-2 1-2 0-3 1-1 0-1 0-2-1v-1c1-1 0-1 1-3z" class="U"></path><path d="M139 294c1-1 0-1 1-3 1 2 2 3 4 4-2 1-2 0-3 1-1 0-1 0-2-1v-1z" class="G"></path><path d="M151 289h1 2v-1h2l2 2-1 1c0 1 0 1-1 2h-1c-1 1-2 1-3 1l-2-1c-1 1 0 1-1 2v-1c-2-1-3-2-4-1-1-2-2-2-3-3l1-1h0c1 0 2 1 3 0h5z" class="C"></path><path d="M156 293l-1-1v-1l1-1 1 1c0 1 0 1-1 2zm-6 0c-1 0-2-1-3-1v-1c1 0 3-1 4 0h1 0v-1l1-1 1 1v1h-1l2 2c-1 1-2 1-3 1l-2-1z" class="U"></path><defs><linearGradient id="BF" x1="160.206" y1="248.448" x2="156.458" y2="272.188" xlink:href="#B"><stop offset="0" stop-color="#d3d2d3"></stop><stop offset="1" stop-color="#f8f8f8"></stop></linearGradient></defs><path fill="url(#BF)" d="M155 249h0l1-1h1l1 1c0 1-1 2-1 3l1-1-1 2c0 2 1 3 3 5h-1c2 2 3 4 4 6-1 0-2 1-3 2 1 0 1 1 2 1 2 0 2 0 3 2 1 0 2 0 3 1 0 1 0 1-1 3h-3v1l2 1h-1-3l-1 1h-1c0-1 0-1-1-2-1 0-2 1-2 2l-1 1c0-2 0-3-1-5-1 0-2-1-3-2h-1c-1 0-2 0-3-1-1 1-1 2-1 3-2-1-3-1-4-2v-2l2-1c-1-1-1-1-2-1 1-1 1-2 2-2h-2l-2-1 2-1h0l-1-2c1-1 1-1 2-1h1l2-1c1-1 2-1 3-2 0-1 1-1 1-2l2-2v-1l2-2h0z"></path><path d="M160 266v-1l1-1c1-3-2-4-4-6h1 1c2 2 3 4 4 6-1 0-2 1-3 2z" class="U"></path><path d="M153 268v-1l6 6 1 1 1 2h-1c0-1 0-1-1-2-1 0-2 1-2 2l-1 1c0-2 0-3-1-5 0-1-1-3-2-4z" class="C"></path><path d="M160 274c3-1 2-2 4-4v-1-1l1 1c1 0 2 0 3 1 0 1 0 1-1 3h-3v1l2 1h-1-3l-1 1-1-2z" class="E"></path><path d="M153 252v-1l2-2c0 3-2 5-3 7 0 1 1 1 1 2v1h0v2 5 1 1c1 1 2 3 2 4-1 0-2-1-3-2h-1c-1 0-2 0-3-1-1 1-1 2-1 3-2-1-3-1-4-2v-2l2-1c-1-1-1-1-2-1 1-1 1-2 2-2h-2l-2-1 2-1h0l-1-2c1-1 1-1 2-1h1l2-1c1-1 2-1 3-2 0-1 1-1 1-2l2-2z" class="B"></path><path d="M148 261h0 1v1h0v1h-1s-1 1-2 1h0-1l1-2h0c1-1 1-1 2-1z" class="H"></path><path d="M145 264h0 1 0c1 2 0 2 1 4v1l-2-2c-1-1-1-1-2-1 1-1 1-2 2-2z" class="L"></path><path d="M151 270c0-1 0-2-1-3 0 0 0-1 1-1h0c1 0 1 1 2 2s2 3 2 4c-1 0-2-1-3-2h-1z" class="E"></path><path d="M145 267l2 2h0v-1l1 1h0c-1 1-1 2-1 3-2-1-3-1-4-2v-2l2-1z" class="I"></path><path d="M153 252v-1l2-2c0 3-2 5-3 7 0 1 0 2-1 2v1 3h-2v-1h-1 0c-1 0-1 0-2 1h0l-1 2h0-2l-2-1 2-1h0l-1-2c1-1 1-1 2-1h1l2-1c1-1 2-1 3-2 0-1 1-1 1-2l2-2z" class="C"></path><path d="M150 256c0-1 1-1 1-2l2-2-1 2v2c-1 1-1 1-1 2-1 1-2 2-2 3h-1 0v-2h-3l2-1c1-1 2-1 3-2z" class="V"></path><path d="M143 262l-1-2c1-1 1-1 2-1h1 3v2c-1 0-1 0-2 1h0l-1 2h0-2l-2-1 2-1h0z" class="n"></path><path d="M143 262l1-1s1 0 2 1l-1 2h0-2l-2-1 2-1h0z" class="Z"></path><defs><linearGradient id="BG" x1="128.97" y1="429.176" x2="175.858" y2="442.336" xlink:href="#B"><stop offset="0" stop-color="#bfbebe"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#BG)" d="M132 418h1v-1h0 1v1-1c2 1 3 4 5 5l2 2c1 0 1-1 1-1h3 0l-3 2c1 1 3 2 4 2 0 1 0 1 1 2h2v-1l1 1h-1c1 1 2 1 3 2h0c2 1 4 3 5 5h1 0c0 2 1 2 2 3h2 0v-1c0-1 1-1 2-2s3-4 4-4h2c0 1 1 4 0 5v1c-1 1-1 2-2 2l-3 2c3 2 5 4 7 8 0 1 1 2 1 3v2s-1 0-1 1c-2-3-5-7-8-8-5-2-7-1-12 1-1 0-2 0-2-1h-1c1-1 1-3 0-4l1-1v-1h-1l-1-1c-1-1-1-2-1-2-2 0-3 0-4-1v-1h1c0-1-1-1-2-1l-1-2h-1-1l1-2-2-1 1-1v-1l-1-1h0l1-1-3-3c-1-1-1-3-2-3-1-1-2-2-2-3z"></path><path d="M165 442c0-1 0-1-1-1l1-1c1 0 2-1 3 0l-3 2z" class="F"></path><path d="M139 430c1 1 2 1 3 3h0l-2 1h-1l1-2-2-1 1-1z" class="L"></path><path d="M170 432c0 1 1 4 0 5v1h-1l-2 1c-1-1-1-2-2-2 0-1-1-1-1-1 1-1 3-4 4-4h2z" class="E"></path><path d="M144 437c4 1 8 2 10 7v2c-1 1-2 2-3 2h-1-1c1-1 1-3 0-4l1-1v-1h-1l-1-1c-1-1-1-2-1-2-2 0-3 0-4-1v-1h1z" class="U"></path><path d="M143 266h0c1 0 1 0 2 1l-2 1v2c1 1 2 1 4 2 0-1 0-2 1-3 1 1 2 1 3 1h1c1 1 2 2 3 2 1 2 1 3 1 5l1-1c0-1 1-2 2-2 1 1 1 1 1 2s-1 4-1 5c-1 2 1 4 0 6-1 0-1 1-2 1h-1-2v1h-2-1-5c-1 1-2 0-3 0h0l-1 1h-1-1l-1-1c1 1 1 1 1 2h0c-1 2 0 2-1 3h-1-2v-1c1-1 2-1 2-1 0-1 0-1-1-2v-1c-1-2-2-4-2-6-1-3-3-6-4-9h-1v-2c1 0 0 0 1-1h4 1c1 0 1-1 2-1h1c0-1 0-1 1-2l1-2h2z" class="I"></path><path d="M140 283c1-1 1-2 2-2v3 1h-1-1-1-1c0-1 0-2 1-3l1 1z" class="E"></path><path d="M143 283l6 3c-2 1-2 2-3 3s-2 0-3 0h0l-1 1h-1c0-1-2-1-2-3l2-2h1v-1l1-1z" class="K"></path><path d="M130 272c1 0 0 0 1-1h4 0l1 1h3c-1 1-2 2-2 4l1 1h0c0 1 0 2 1 3-1 1-1 0-2 0v4c0 1 1 4 0 5-1-2-2-4-2-6-1-3-3-6-4-9h-1v-2z" class="F"></path><path d="M135 271l1 1h3c-1 1-2 2-2 4l-2-1-1-1c-1-1-1-1 0-2l1-1z" class="R"></path><path d="M136 272h3c-1 1-2 2-2 4l-2-1 1-1v-2z" class="E"></path><path d="M131 274h0c2 2 4 6 6 6v4c0 1 1 4 0 5-1-2-2-4-2-6-1-3-3-6-4-9z" class="G"></path><path d="M143 266h0c1 0 1 0 2 1l-2 1v2c1 1 2 1 4 2 0 1 1 2 2 3l1 2c-1 1-2 2-3 2-2 1-3 2-4 3v1l-1 1v-3c-1 0-1 1-2 2l-1-1c1-1 1-2 2-2l1-1v-1l-1 1c0-1-1-1-2-1l-1-1-1-1c0-2 1-3 2-4h-3l-1-1h0 1c1 0 1-1 2-1h1c0-1 0-1 1-2l1-2h2z" class="D"></path><path d="M147 279c-1 0-1-1-1-1-1-2-1-4-2-5l-1-2v-1c1 1 2 1 4 2 0 1 1 2 2 3l1 2c-1 1-2 2-3 2z" class="P"></path><path d="M140 268l1-2h2l-2 1c1 0 1 2 1 2-1 2 0 2 0 3l1 1c0 1 1 3 1 4s-1 2-1 3h-2l1-1v-1l-1 1c0-1-1-1-2-1l-1-1-1-1c0-2 1-3 2-4h-3l-1-1h0 1c1 0 1-1 2-1h1c0-1 0-1 1-2z" class="Q"></path><path d="M135 271h1c1 0 1-1 2-1h1c0-1 0-1 1-2v1s1 0 1 1v1l-1 1v1h1v1 1l-1-1c0-1-1-1-1-2h-3l-1-1h0z" class="c"></path><path d="M139 272c0 1 1 1 1 2l1 1 1 3-1 1c0-1-1-1-2-1l-1-1-1-1c0-2 1-3 2-4z" class="R"></path><path d="M140 274l1 1 1 3-1 1c0-1-1-1-2-1 1-1 1-3 1-4z" class="U"></path><path d="M148 269c1 1 2 1 3 1h1c1 1 2 2 3 2 1 2 1 3 1 5l1-1c0-1 1-2 2-2 1 1 1 1 1 2s-1 4-1 5c-1 2 1 4 0 6-1 0-1 1-2 1h-1-2v1h-2-1-5c1-1 1-2 3-3l-6-3v-1c1-1 2-2 4-3 1 0 2-1 3-2l-1-2c-1-1-2-2-2-3s0-2 1-3z" class="R"></path><path d="M156 277c1 2 1 2 0 4v1c-1 0-1 0-1-1h-1v-1c1-1 2-1 2-3h0z" class="D"></path><path d="M148 269c1 1 2 1 3 1h-1c0 1-1 1-2 0v2c0 1 1 2 1 2 2 1 4 0 5 1 0 1 0 1-1 1-2 0 0-2-3-1v2h0l-1-2c-1-1-2-2-2-3s0-2 1-3z" class="U"></path><path d="M156 277l1-1c0-1 1-2 2-2 1 1 1 1 1 2s-1 4-1 5c-1 2 1 4 0 6-1 0-1 1-2 1h-1-2v1h-2-1-5c1-1 1-2 3-3h0l1 1c1 0 3 0 4-1 2-1 2-2 2-4v-1c1-2 1-2 0-4h0z" class="L"></path><path d="M149 286h0l1 1h2c0 1-1 1-1 2h-5c1-1 1-2 3-3z" class="P"></path><path d="M154 219c1 2 3 6 3 8 1 2 0 7 2 8l2 2h0-2c-1 0 0 2-1 4 0 2-3 5-3 8h0l-2 2v1l-2 2c0 1-1 1-1 2-1 1-2 1-3 2l-2 1h-1c-1 0-1 0-2 1l1 2h0l-2 1 2 1h2c-1 0-1 1-2 2h0-2l-1 2c-1 1-1 1-1 2h-1c-1 0-1 1-2 1h-1-4c-1 1 0 1-1 1l-1-1v-1l-1-1v-1h1l6-1c1 0 2-2 2-2v-3-2-3h-1c0-1 1-2 2-2v-1h2 0l2-2c1-2 2-2 3-3v-2h0c0-1 1-3 1-4l1-4c-1-1-1-3-1-4-1-2-1-4-2-6h2l1-1 2-1h0l4-2 1-1-2-1h0v-1c1-1 1-2 2-3z" class="K"></path><path d="M155 239h1c0 2 0 4-1 6l-1 1h0c0-1 0-1-1-1 1-2 2-4 2-6z" class="G"></path><path d="M154 224v-2h0c2 2 2 7 2 10v1s0 1-1 2v-3-1l-1 1v-2h-3c1-2 2-3 2-5l1-1z" class="L"></path><path d="M154 230c-1-1-1-2 0-3l1-1c1 2 1 5 1 7 0 0 0 1-1 2v-3-1l-1 1v-2z" class="I"></path><path d="M153 245h0c1 0 1 0 1 1s-1 3-2 4c0 2-1 4-2 6-1 1-2 1-3 2 1-1 1-1 1-2l-1-1 2-3 1-2c0-1 1-2 1-2 1-1 1-2 2-3h0z" class="Z"></path><path d="M154 232l1-1v1 3 4c0 2-1 4-2 6h0l-1-1c0-4 1-8 2-12z" class="E"></path><path d="M151 230h3v2c-1 4-2 8-2 12l1 1h0-3v-5c0-1-1-3 0-4 0-2 1-4 1-6z" class="H"></path><path d="M149 227l4-2c0 2-1 3-2 5 0 2-1 4-1 6-1 1 0 3 0 4v5h3c-1 1-1 2-2 3 0 0-1 1-1 2l-1-2c0-1 1-1 0-2v-1h-1-1l-2 4h0v-2h0c0-1 1-3 1-4l1-4c-1-1-1-3-1-4-1-2-1-4-2-6h2l1-1 2-1h0z" class="S"></path><path d="M147 228l2-1h0c0 1 0 2-1 3h-1v1 2c0 1 0 1 1 2l1 1c0 2 0 5-1 7l1 2h0-1-1l-2 4h0v-2h0c0-1 1-3 1-4l1-4c-1-1-1-3-1-4-1-2-1-4-2-6h2l1-1z" class="Z"></path><path d="M147 239l1-1v4c0 1-1 1-1 1h-1l1-4z" class="M"></path><path d="M145 249h0l2-4h1 1v1c1 1 0 1 0 2l1 2-1 2-2 3 1 1c0 1 0 1-1 2l-2 1h-1c-1 0-1 0-2 1l1 2h0l-2 1 2 1h2c-1 0-1 1-2 2h0-2l-1 2c-1 1-1 1-1 2h-1c-1 0-1 1-2 1h-1-4c-1 1 0 1-1 1l-1-1v-1l-1-1v-1h1l6-1c1 0 2-2 2-2v-3-2-3h-1c0-1 1-2 2-2v-1h2 0l2-2c1-2 2-2 3-3z" class="P"></path><path d="M148 249v-3h1c1 1 0 1 0 2l-1 1z" class="D"></path><path d="M148 249l1-1 1 2-1 2c0-1-1-1-2-1v-1l1-1z" class="C"></path><path d="M143 262c-2 0-2-1-3-3l5-5c1-1 1-3 2-4v1c1 0 2 0 2 1l-2 3 1 1c0 1 0 1-1 2l-2 1h-1c-1 0-1 0-2 1l1 2h0z" class="H"></path><path d="M147 255l1 1c0 1 0 1-1 2l-2 1h-1l3-4z" class="l"></path><path d="M136 257c0-1 1-2 2-2v-1h2 0l2-2c1-2 2-2 3-3 0 1-1 4-3 5-1 1-2 1-2 2-1 1-1 1-2 1v3c1 2 1 3 3 3h0l2 1h2c-1 0-1 1-2 2h0-2l-1 2c-1 1-1 1-1 2h-1c-1 0-1 1-2 1h-1-4c-1 1 0 1-1 1l-1-1v-1l-1-1v-1h1l6-1c1 0 2-2 2-2v-3-2-3h-1z" class="G"></path><path d="M128 269c2-1 2-1 4 0h1v1c-1 1-1 1-2 0h-1-1l-1-1z" class="O"></path><path d="M138 260c1 2 1 3 3 3h0l2 1-1 1c-2 0-3 1-4 3v-8z" class="M"></path><path d="M151 350c1 0 2 1 2 2l1 1h0c2 3 3 6 4 10 2 6 1 14 2 21 0 1 0 2 1 3h0v1c1 2 3 3 4 5h0 1c1 1 1 2 2 4 0 3 0 5-1 7l-2 7c-2 2-3 3-5 4 0 1-1 1-2 1l-1-1c0-1 1-2 2-3l-3 1v-1-1h-2l-1-1v-3c3-1 2 1 5 1h1c1-1 0-3 0-4v-1l-4 1c-1 0-2 0-3 1h-2-1v1c-6 0-12-1-18 0h-1v-2-1h-1l-1-1c1-1 2-3 2-4l2-1 1-1c0-1 1-1 1-1 0-1 0-1 1-1 1-2 2-4 2-6l-2-1v-1h4c1-1 1-2 3-2h0c1-1 4-2 5-2l2 1 9 3-3-3c1-5 1-10 0-15 0-3-1-7-2-10-1-2-3-6-2-8z" class="m"></path><path d="M156 411h2 3l-2 1-3 1v-1-1z" class="B"></path><path d="M158 385l2-1c0 1 0 2 1 3h0v1l-3-2v-1z" class="n"></path><path d="M130 398l2-1-2 6h1 2 0c-1 1-2 1-3 1v-1h-1l-1-1c1-1 2-3 2-4z" class="C"></path><path d="M165 393h1c1 1 1 2 2 4 0 3 0 5-1 7l-1-2v-1c1-3 0-5-1-8z" class="c"></path><path d="M131 403c4-2 11-1 15-1v1h0 0c-3 0-4 0-7 1h-1c-2-1-3-1-4-1 0 1 0 1-1 0h0-2z" class="E"></path><path d="M154 353c2 3 3 6 4 10 2 6 1 14 2 21l-2 1v-7c0-9-1-16-4-25h0z" class="B"></path><path d="M161 411v-1c1 0 1-1 2-2 0-2-1-4-1-5l-1-1h1c0 1 1 2 2 2h1l1-2 1 2-2 7c-2 2-3 3-5 4 0 1-1 1-2 1l-1-1c0-1 1-2 2-3l2-1z" class="J"></path><path d="M141 387h0l2-2c2 0 4 0 6 2 4 1 9 3 11 8v2h0v1c-1 0-2 1-3 1-2-2-1-5-2-7h-1v-1l-1-1h-2v1h-2c-2-1-5-3-8-4z" class="B"></path><path d="M141 387h0l2-2c2 0 4 0 6 2l1 2h-1c-1 0-2-1-3-1s-2-1-3-1h-1-1zm-4 1l8 3s2 1 3 1h1v-1h2v-1h2l1 1v1h1c1 2 0 5 2 7l-2 2c-3-1-3-2-6-3h0-1v-1c-2 0-4-1-6-1-2-1-5-1-8-1 0-1 0-1 1-1 1-2 2-4 2-6z" class="U"></path><path d="M148 397h0c0-1 0-2-1-3 1 0 1-1 2-1 0 1 1 1 1 2h0c0 1 1 2 0 3h0-2v-1z" class="B"></path><path d="M151 391v-1h2l1 1v1c-1 2-1 4-1 6h0c-2-1-1-4-2-6v-1z" class="F"></path><path d="M136 313h2v-1h1v1c-1 2 0 3 1 4h0v1c1 1 3 2 4 4h1v-1c1 0 1 0 2 1l1 1v1c1 0 1 1 2 2 1 0 2-1 3-1v-1 1c1 1 2 1 3 1h-2l1 1c2 0 4 0 5 2v3h-3c-1 0-2 1-4 1v3h2l-1 1c-1 1 1 3 0 4h0 0c-1-1-1-2-2-2-2 0-1 1-2 2l-3 4-1 2c0 2 0 4 1 7 2 5 4 10 5 15 0 3-2 8-2 11 1 1 2 0 3 0 0 0 0 1 1 1 1-2 1-5 1-7 0-3-1-7-2-11 0-3-1-6-2-9-1-2-2-3-2-6l1 2h1 0c-1 2 1 6 2 8 1 3 2 7 2 10 1 5 1 10 0 15l3 3-9-3-2-1c-1 0-4 1-5 2h0c-2 0-2 1-3 2h-4c-1 0-2 0-2-1h0l-1-1c-1-1-2 0-3-1-1 1-1 2-2 2l1 2h0l-2 1c-3-2-5-1-8-1-1-1-2-2-2-3h0c-1-1-2 0-3-1v-1c2 1 1 0 3-1h2c0-1 0-1 1-2v-1-4c0-2 0-3-1-4 1-3-2-7-4-10l-2-1 1-1c-1-1-1-2-1-3v-1c1-2 2-4 4-5l1-1c4 0 7 1 10 2 1-2 1-4 2-6-1-3-2-6-2-9v-4-3h1l-1-1-1-2c1 0 1 0 1-1h1c1 0 1 0 2-1v-2-1l2-1h0 2s1-1 2-1v-1c1-1 1-1 1-2-1 0-1-1-1-2h0z" class="I"></path><path d="M132 373l1-2 1-1 2 2c-1 0-2 1-4 1z" class="E"></path><path d="M146 347c-2-2-2-3-2-4l1-1 2 3-1 2z" class="m"></path><path d="M139 353v1c3 3-2 4-3 7v2h0c0-1-1-2 0-3 0 0 0-1 1-1h-1s0-1 1-1c2-1 1-2 2-4v-1z" class="F"></path><path d="M138 320c0 3 0 4-1 7-1 1-1 2-3 3h0v-2-1-1c0-1 2-3 3-4 0-1 1-1 1-2z" class="C"></path><path d="M127 331l1-1h0c0 1 0 1 1 2h0c0 2 0 2 1 4v4 3l-1 1c-1-3-2-6-2-9v-4z" class="E"></path><path d="M134 346l1-1c1 1 1 2 2 3 0 2 0 3-1 4 0 2-1 3 0 4v1l-1-1c0 1-1 2-1 3l1 2-1 1h-1 0c0-1 1-1 0-2h0 0c0 1-1 3-2 4h-1l1 1 1 1s1 1 1 2h-1s-1 0-1-1-1-2-1-4c-1 0-1 1-2 1l-1-2 1-1v-1c2-1 3-2 4-4v-4h1c1-2 1-4 1-6z" class="H"></path><path d="M132 356v-3h1l2 2-1 2c-1 2-2 3-3 4-1 0-1 1-2 1l1 1c-1 0-1 1-2 1l-1-2 1-1v-1c2-1 3-2 4-4z" class="V"></path><path d="M128 361l1 1 1 1c-1 0-1 1-2 1l-1-2 1-1z" class="F"></path><path d="M137 371h0c1 0 2 0 3 1h0c1 1 3 3 3 5v4c0 1 0 2-1 3h0c-2 0-2 1-3 2h-4c-1 0-2 0-2-1h0l2-1c1-1 3-2 3-5v-5c0-1-1-2-1-3z" class="D"></path><path d="M143 381h0c-1 1-1 1-3 2v-3c0-1-1-1-1-1v-2c0-2 0-4-1-5v-1c1 1 1 1 2 1 1 1 3 3 3 5v4z" class="L"></path><path d="M132 373c2 0 3-1 4-1l1-1c0 1 1 2 1 3v5c0 3-2 4-3 5l-2 1-1-1c-1-1-2 0-3-1l1-3 2-1h0l1-1c0-1 0-2-1-4v-1z" class="m"></path><path d="M138 374v5c0 3-2 4-3 5l-2 1-1-1c-1-1-2 0-3-1l1-3 2-1-1 1c-1 1-1 1 0 2h1c1 0 2-1 3-2 0-1 0 0 1-1l1 1 1-1h-1 0c0-1-1-2-2-2v-1h2 1v-2z" class="B"></path><path d="M136 313h2v-1h1v1c-1 2 0 3 1 4h0l-1 1s0 1-1 2c0 1-1 1-1 2-1 1-3 3-3 4v1 1l-3 3c-1 0-2 0-2-1l-1-2-1-1-1-2c1 0 1 0 1-1h1c1 0 1 0 2-1v-2-1l2-1h0 2s1-1 2-1v-1c1-1 1-1 1-2-1 0-1-1-1-2h0z" class="D"></path><path d="M127 324h1c1 0 1 1 2 1s1 1 1 1v1h-4l-1-2c1 0 1 0 1-1z" class="K"></path><path d="M136 313h2v-1h1v1c-1 2 0 3 1 4h0l-1 1s0 1-1 2c0 1-1 1-1 2-1-1-1-1-2-1h0v-1s1 0 1-1v-1-1c1-1 1-1 1-2-1 0-1-1-1-2h0z" class="H"></path><path d="M138 320c1-1 1-2 1-2l1-1v1c1 1 3 2 4 4h1v-1c1 0 1 0 2 1l1 1v1c1 0 1 1 2 2 1 0 2-1 3-1v-1 1c1 1 2 1 3 1h-2l1 1c2 0 4 0 5 2v3h-3c-1 0-2 1-4 1v3h2l-1 1c-1 1 1 3 0 4h0 0c-1-1-1-2-2-2-2 0-1 1-2 2h-1c-1 1-1 1-2 1-1-1-2-2-4-3-1 0-1-1-2-2h-1c0-2-1-2-3-2-1 0-3 1-4 2 0 1 0 2-1 3l-1-1c0-1 0-2 1-3 2-2 4-3 6-3 1 0 1-1 1-1l-1-3c-1-1-1-2-1-2 1-3 1-4 1-7z" class="m"></path><path d="M148 323v1c1 0 1 1 2 2h-1l-1 1c1 0 1 0 2 1l-1 1-2-2c0-2 0-2 1-4z" class="F"></path><path d="M153 325v-1 1c1 1 2 1 3 1h-2v1 1l-2 2c-1 0-2 0-3-1l1-1c-1-1-1-1-2-1l1-1h1c1 0 2-1 3-1z" class="B"></path><path d="M140 318c1 1 3 2 4 4h1v-1c1 0 1 0 2 1l-2 1h-1 0l1 3c1 1 0 2-1 3h0l-3-7-1-4z" class="C"></path><path d="M138 320c1-1 1-2 1-2l1-1v1l1 4h0c-1-1-1-1-1-2l-1-2c0 4 0 8 1 11 1 2 2 3 3 5l-1 1s-1 0-1-1v-3h-1c0-1-1-2-1-3h-1v1c-1-1-1-2-1-2 1-3 1-4 1-7z" class="E"></path><path d="M152 339c-2-1-3-2-4-4-2-1-4-3-3-6h1 0v2c1 0 2 1 3 2l1 1 3-1v3h2l-1 1c-1 1 1 3 0 4h0 0c-1-1-1-2-2-2z" class="C"></path><path d="M130 343c1 1 3 1 4 2v1h0c0 2 0 4-1 6h-1v4c-1 2-2 3-4 4v1l-1 1 1 2c0 1 2 5 1 6l3 4h0c1 2 1 3 1 4l-1 1h0l-2 1-1 3c-1 1-1 2-2 2l1 2h0l-2 1c-3-2-5-1-8-1-1-1-2-2-2-3h0c-1-1-2 0-3-1v-1c2 1 1 0 3-1h2c0-1 0-1 1-2v-1-4c0-2 0-3-1-4 1-3-2-7-4-10l-2-1 1-1c-1-1-1-2-1-3v-1c1-2 2-4 4-5l1-1c4 0 7 1 10 2 1-2 1-4 2-6l1-1z" class="R"></path><path d="M125 378c-1 0-1 0-3-1l1-1c1 0 1-1 3-1l-1 3z" class="H"></path><path d="M120 379c0 1 0 2 1 3l-2 2h0c-1-1 0-2 0-3h-1c0-1 0-1 1-2h1z" class="E"></path><path d="M120 355h0c3-1 7-2 9-2 1 1 0 2 0 2v1c-1 0-1-1-3-2v1c-2 1-4 1-6 1v2h-1c0-1 0-2 1-3z" class="I"></path><path d="M112 355h4 1 1 2c-1 1-1 2-1 3v1h-1c-1 0-3 0-5-1-1-1-1-2-1-3z" class="U"></path><path d="M116 355h1v1l-2 2-1-2h1l1-1z" class="I"></path><path d="M126 360h2v1l-1 1 1 2c0 1 2 5 1 6 0 1 1 3 0 4l-2-1v1h0c-1-3-1-7-2-10 0-1-1-3 0-4h1z" class="H"></path><path d="M126 360h2v1l-1 1v1c-1-1-1-2-1-3zm3 10l3 4h0c1 2 1 3 1 4l-1 1h0l-2 1-1 3c-1 1-1 2-2 2 0-1-1-1-1-2l1-1v-3c-1-1-1-1-2-1l1-3h0s0-1 1-1v-1l2 1c1-1 0-3 0-4z" class="L"></path><path d="M127 374v-1l2 1v1h1l1 1c-1 1-1 2-2 2h0c-1-1-1-1-2-1l-1-2s0-1 1-1z" class="Q"></path><path d="M120 358c2 0 2 1 3 2-1 1-2 0-3 1v1c0 5 1 12 0 17h-1v-1-4c0-2 0-3-1-4 1-3-2-7-4-10l-2-1 1-1c2 1 4 1 5 1h1v-1h1z" class="H"></path><path d="M118 359h1 1v3c-2 0-3-1-5-2 1-1 2-1 3-1z" class="D"></path><path d="M125 407c2-1 2-3 3-5l1 1h1v1 2h1c6-1 12 0 18 0v-1h1 2c1-1 2-1 3-1l4-1v1c0 1 1 3 0 4h-1c-3 0-2-2-5-1v3l1 1h2v1 1l-3 2c-3 1-7 2-11 4-1 0-2 0-2 1l-1 2h0c-2-1-3-4-5-5v1-1h-1 0v1h-1c0 1 1 2 2 3 1 0 1 2 2 3l3 3-1 1h0l1 1v1l-1 1 2 1-1 2h1 1l1 2c1 0 2 0 2 1h-1v1c1 1 2 1 4 1 0 0 0 1 1 2l1 1h1v1l-1 1c1 1 1 3 0 4h1c0 1 1 1 2 1 5-2 7-3 12-1 3 1 6 5 8 8 0-1 1-1 1-1 0 1 1 2 1 3 1 0 1 0 1-1h1l2 15c1 2 1 5 1 7l2 11c1 3 1 6 2 9 0 1 1 2 0 2v5 4l3 9 1-2c2 5 3 10 5 15 6 15 14 28 24 39 4 5 8 8 12 11-2 1-6 0-8 2l1 1c-2 1-3 1-3 3v1c-1 2-1 4-1 6l1 3c5 8 14 12 23 15l-1 1h0c4 1 8 1 12 1 3 0 6 0 9 1-1 1-3 4-2 5l1 1c0 1 1 2 2 2l6 3 1-1 3-3v-1c1-2 2-3 3-4v1-1l1-1c1 1 3 1 4 1 2-1 5 0 6 0v1c1-1 1-2 2-2v3h4v-1l1-3h2 2v-2h1 0c0 2-1 4 0 6h0c1 1 1 1 3 0 0 1 0 2 1 4l2 1 2 1c1 1 1 1 2 1l1 1h1c1 1 1 1 2 0 2 1 4-1 6-1 1 0 2 0 3 1h1c1 0 1 2 2 1 1 0 2 1 2 1 1 0 1 0 2 1l-2-2h1 2c3 1 4 0 7-2 1-1 1-1 1-2 1-1 2 0 3 0l2-1h0c2-1 3-2 4-4v1h1l-1-1v-3h0l1-1c0 1 1 0 2 1h-2v2c0 1 1 1 1 1 1 1 2 1 2 0h0 2c2-1 4-1 6-2 1-1 3-1 5-1 8-1 15-6 21-10l19-15 1 1h1 0v1c3-1 5-4 7-6-1 3-5 6-7 9 3-2 5-4 7-6s5-4 7-6c1-2 2-3 3-4v-1c0 1 0 3 1 3v1 1h0l1-1v2 1c-1 1-2 1-2 2 1 0 2-1 4-2l1 1c-1 1-6 6-6 7l-3 4h1c1 0 2-1 3-2 0-1 0-1 1-2 2 0 4-4 6-5h0c2 0 5-3 6-4l7-6 2-2 6-7h0l13-15h1 0v3l13-14c1 0 3-1 4-2l5-2 1-1h2c1-1 3-2 4-3 3-1 5-2 7-1l3 1c1 0 2 0 4 1h0l3-1v-1l1 1c4 0 7-1 10 0h-1l1 2 1 1h-1c3 0 8 1 11 3h-1l1 1h-1c-4-2-8-2-13-2-6 0-11 1-16 3-24 9-40 29-57 45-14 12-29 23-45 32-7 3-14 7-21 9-14 5-29 9-43 11-4 0-9 1-13 1-12 1-25 1-37 0l-30-3c-23-4-46-12-67-22s-40-25-55-42l-13-13c-4-5-7-10-10-15-5-6-9-13-13-20-7-13-13-27-18-42l-3-6v-2c1-1 2-2 3-2l-1-1c1-2 0-3 0-5l-2-6c0-1-1-2-1-3 0 0 1 0 0-1 0-2-2-6-1-8h1 1c1 0 2-2 2-4l-3-6c0-1-1-3 0-5 1 0 1 0 1 1l1-1c1-1 3-1 5-1h0c2-2 5-2 7-4v-1h-2-1l-1-1v-1c1 0 1-1 1-1 1-1 3 0 4 0h0c1 1 1 1 1 3h0 0c0-1 0-2 1-3 1 0 2-1 3-1l1 1c0-1 0-1 1-1h1v-1c1 1 2 1 3 0v-1-2h-1l1-3v-1c-1 0-1 0-2-1l-5-5h0c1 1 2 1 3 1 1 1 2 1 2 2 1 0 2 0 3 1h4 2c1 0 2 0 3-1 0 0 1-1 2-1h0c1 0 2 0 3-1l3-2c2-1 3-3 5-5z" class="R"></path><path d="M176 528c1 1 1 2 1 3l-1 1h-1v-2c1-1 1-1 1-2z" class="E"></path><path d="M118 554s0 1 1 0h2 1c-1 1-1 1-2 3h0c-1-1-1-1-1-2-1 0-1 0-1-1z" class="U"></path><path d="M98 523s1 1 2 1 1 0 1 1h0l-1 1c-2-1-2-2-2-3z" class="m"></path><path d="M173 500c2-1 4-1 6-1l-3 3h0l-2-2h-1z" class="E"></path><path d="M180 528c1 1 2 2 3 4h0l1 1h-2c-1-1-1-1-1-2v-1h-2l1-2z" class="U"></path><path d="M204 567h2v2c-1 0-2 1-2 1h-1c0-1-1-2-1-3h2z" class="F"></path><path d="M204 567h2v2h-1l-1-2z" class="B"></path><path d="M102 512h1 1 3c-1 1-1 2-2 2-2 0-3 0-5-1 1 0 1-1 2-1z" class="D"></path><path d="M183 544l2 2c-1 1-2 3-4 3h0-2c2-2 3-3 4-5z" class="S"></path><path d="M186 543c0 3 0 4-1 6h-1 0l1-1c-2-1-1 1-3 2h-1v-1h0c2 0 3-2 4-3l1-1v-2z" class="D"></path><path d="M175 587l1 1 4 5c-1 0-2-1-2-1-1 0-3-2-4-3 0-1 0-1 1-2z" class="m"></path><path d="M88 487l4-4 1 2-1 2h0c-1 0-2 1-4 0z" class="V"></path><path d="M177 512c-1 1-2 2-4 2v-1s1-1 1-2v-1l3-1c-1 0-2 1-2 1v1h2v1h0z" class="I"></path><path d="M134 479v-1c1-1 3-1 4-1h0l-3 3c0 1 0 1 1 1 0 1 0 2 1 3l-1 1c0-2-1-2-2-4v-2zm49 55c1-1 2-1 3-1l1-1v-1l1-1h0c1 2 2 4 2 6l-2-1c-1-1-3-1-5-1z" class="B"></path><path d="M180 516c-1 1-2 3-4 4 0 1-1 3-2 4v-1c1-1 2-3 3-5l-1-1c0-1 2-1 3-2 0 1 0 1 1 1z" class="F"></path><path d="M206 567c1 0 1 0 2 1v4l-2 1-2-3s1-1 2-1v-2z" class="C"></path><path d="M117 546l1 1s-1 2 0 2c1 1 1 2 2 3v2h1-2c-1 1-1 0-1 0l-2-2c0-1 1-1 1-2v-1-3z" class="E"></path><path d="M194 563s1 1 1 2-1 2-2 4c0 1-1 1-2 2v1-1c-1-1-2 0-2-2h0 2 0 1c0-1 2-2 2-3v-3z" class="B"></path><path d="M183 544c0-1 0-2-1-2l-1-1-1-1h1v-1c2 1 2 1 3 2l2 2v2l-1 1-2-2h0z" class="C"></path><path d="M184 541l2 2v2l-1 1-2-2h0c1-1 1-2 1-3z" class="L"></path><path d="M514 540h7c3 0 8 1 11 3h-1l1 1h-1c-4-2-8-2-13-2 2-1 4 0 6 0h2 0 1 2c-1-1-3-1-5-1l-10-1z" class="U"></path><path d="M139 482c1 0 2 0 3 1 0 2-2 5-2 7-1 2-2 5-2 7l-1 1h0c0-2 0-4 1-6 1-3 2-7 1-10z" class="E"></path><path d="M194 590v-2c-1 0-1-1-1-1l2 2 2 1v3l2 2c0 1 1 2 2 3h-1c-2-1-4-4-5-5v-1c-1 0-1-1-1-2z" class="I"></path><path d="M175 530v-1c0-1-1-1-1-2h0c-1-1 0-1 0-2h1 4l-1 1c1 0 2 1 2 2l-1 2-2-2c0-1 0 0-1-1v1c0 1 0 1-1 2z" class="B"></path><path d="M172 541c0-2 0-4 1-6 0 1 1 1 1 2s0 2 1 2c0 0 1-1 2-1 0-1 2-2 3-3v1c-2 2-5 4-6 7v-1c-1 1-1 1-2 1v-2z" class="C"></path><path d="M165 483l3-3c-1-2-2-3-4-4h0c1-1 2 0 3 0s3 2 3 3v3c-1 1-3 1-4 2l-1-1z" class="V"></path><path d="M199 553l6 10h-1l-5-7h-3c0-1 0-2 1-3h2z" class="I"></path><path d="M155 526c-1-3-2-7-1-11 0-3 1-5 3-7v2l-1 1s0 1-1 1v3 6c0 2 1 4 0 5z" class="G"></path><path d="M231 620l18 3-1 1h-1c-4 0-9-1-13-2-1 0-3 0-4-1l1-1z" class="J"></path><path d="M189 562c1-1 3-1 4-1 0 1 1 1 1 2v3c0 1-2 2-2 3h-1c0-1-1-2-1-3-1-1-1-1-1-2v-2z" class="U"></path><path d="M151 498c0 1 1 2 0 2v2c1 0 1-1 1-2l1-1v-1l3-3-1 3c-1 1-1 2-2 3 0 0 0 1-1 1v3l-2 9c-1-6 0-10 1-16z" class="K"></path><path d="M108 513c1 1 1 1 1 2h2c1 0 3 0 4 1-1 0-2 2-3 3v2c-1 0-1 0-2-1l-2-1s1 0 1-1c0 0 0-1-1-2l-1 1v-1l1-1v-2z" class="F"></path><path d="M108 519s1 0 1-1c0 0 0-1-1-2l-1 1v-1l1-1c0 1 1 1 1 1 0 1 0 2 1 2h1c0 1-1 2-1 2l-2-1z" class="U"></path><path d="M198 589h1v1c1 2 1 2 3 2-1 2-1 4 1 6 1 1 2 2 3 4h-1 0c-2-1-3-3-4-4h0c-1-1-2-2-2-3l-2-2v-3l1-1z" class="C"></path><path d="M186 577v-3h2c0 1 0 2 1 2l1 2 1 2c-1 1-1 3-2 5l-3-8z" class="K"></path><path d="M155 526c1-1 0-3 0-5v-6-3c1 0 1-1 1-1v9c1 1 1 1 0 2 0 3 2 10 2 11s-1 1-1 1c-1-2-2-5-2-8z" class="S"></path><path d="M196 556h3l5 7c-2-1-5-1-7-1 0-1-1-2-1-4l-1-1v-1h1zM90 496l-2-1c-2-1-3-2-4-3 0-1 0-1-1-2h0c-1-2-1-3-2-4 1-1 1-1 2-1h1l1 1v1l1 1v1l1 1v2c0 1 1 1 1 2l2 2z" class="E"></path><path d="M85 487l1 1v1h-2c-1-1-1-1-1-2h2z" class="P"></path><path d="M84 489h2l1 1v2c-2-1-2-2-3-3z" class="Q"></path><path d="M150 478l1 1c-1 3-3 7-3 10-1 3-1 5-2 8h-1c0-1 1-3 1-4h-2 0l1-1h1c1-1 1-2 1-3s-1-1-1-2l1-4c0-2 1-4 3-5z" class="G"></path><path d="M108 481c3 2 6 4 8 8l-1 1c-2-1-3-2-4-3 0 0-1-1-1-2l-2-1-1 1h1l-1 1c-1 0-2-2-2-2s1 0 1 1v-2-1l2-1z" class="V"></path><path d="M170 531c1 1 2 1 4 2v1l-1 1c-1 2-1 4-1 6 0-1 0-2-1-2v1h-1l-1-2c-1 1-1 3-1 4l-1-3v-1l1 1v-1l1-2v-1-2-1l1-1z" class="D"></path><path d="M169 533l2 1c0 1 0 1-1 1v3 1h1v1h-1l-1-2c-1 1-1 3-1 4l-1-3v-1l1 1v-1l1-2v-1-2z" class="L"></path><path d="M168 542c0-1 0-3 1-4l1 2h1v-1c1 0 1 1 1 2v2c1 0 1 0 2-1v1c0 2 0 3-1 4v4s0-1-1-2v-4c-1 1-1 1-2 1v-1l-1 1-1-4z" class="P"></path><path d="M172 543c1 0 1 0 2-1v1c0 2 0 3-1 4h0 0c-1-1-1-3-1-4z" class="L"></path><path d="M148 498h1c-1 2-2 3-2 5v14l2 16c0 1 0 1-1 1 0-2 0-3-1-5-1-9-2-18 0-27 0-1 1-3 1-4z" class="S"></path><path d="M161 554l3 3v1c2 1 3 3 5 5 2 3 4 7 5 11-3-4-6-7-8-11-2-2-3-6-5-9z" class="U"></path><path d="M158 496h1 1 1-1l1 1h2v1l1-1h0v1c-1 0-1 0-1 1-2 0-3-1-4 1v1c-2 0-4 1-5 2l-2 2v-3c1 0 1-1 1-1 1-1 1-2 2-3s2-2 3-2z" class="I"></path><path d="M158 496h1 1 1-1v1c-2 2-6 2-6 6l-2 2v-3c1 0 1-1 1-1 1-1 1-2 2-3s2-2 3-2z" class="H"></path><path d="M198 575c3 0 4 1 6 3-3 0-6-1-8 1-1 0-2 1-3 2 0 3 1 5 2 8l-2-2s0 1 1 1v2c-1-1-1-2-2-3 0-3 0-7 2-9h0v-1h0c1-1 2-2 3-2h1z" class="B"></path><path d="M101 485c-1-1-2-2-3-4 1-1 1-1 2-1 2-1 5 0 8 1l-2 1v1 2c0-1-1-1-1-1l-2-2h-1c1 1 2 3 3 5l-2 1v-1l-2-2z" class="G"></path><path d="M155 470c1 0 2 0 2-1 2 0 8 1 10 1 1 1 3 2 3 3-3-2-6-3-10-2-3 1-7 4-8 7l-1 1-1-1h0c1-1 1-1 1-2 0-3 3-4 4-6z" class="F"></path><path d="M117 546h-4v-1h2c2 0 3-1 3-2l1-1h2l1 1c-1 1-1 1-1 2h-1 0v1c0 1 0 1 1 2h0c0 1 1 2 1 2h1s0 1-1 1v1c0 1 1 2 1 3h-1v-1h-1-1v-2c-1-1-1-2-2-3-1 0 0-2 0-2l-1-1z" class="I"></path><path d="M86 488h1l1-1c2 1 3 0 4 0h1c-1 1-1 1-2 1v1l1 1s0 1 1 2h2-4v1l2 1-1 1 1 1c0 1-1 1-1 2-1-1-2-1-2-2l-2-2c0-1-1-1-1-2v-2l-1-1v-1z" class="D"></path><path d="M88 494c2 0 3 0 4 1h0l1 1c0 1-1 1-1 2-1-1-2-1-2-2l-2-2z" class="C"></path><path d="M91 493c-2-1-3-3-3-4h0c1 0 1 1 2 1l1-1 1 1s0 1 1 2h2-4v1z" class="I"></path><path d="M428 596h3c-8 7-17 13-26 19-3 2-6 3-9 5-1 0-2 1-2 1l-1-1c1-1 4-2 5-2l1-1c7-3 14-8 20-12 1-1 4-3 4-4h-1v-1c3-1 4-3 6-4z" class="C"></path><path d="M195 571c1 0 2-1 3-1 1-1 1 0 2 1 2 2 5 5 7 8v3 1c-1-1 0-2-1-2 0-1-1-2-2-3-2-2-3-3-6-3h-1-2c0-1-1-1-1-2l1-2z" class="D"></path><path d="M195 571c1 0 2-1 3-1 1-1 1 0 2 1h-1l-2 2v1s1 0 1 1h-1-2c0-1-1-1-1-2l1-2z" class="F"></path><path d="M161 554c-1-2-5-6-5-9l1-1v2 1l1 1h2c0 1 1 3 3 3 0 1 1 1 2 2 0 1 1 1 1 2s1 1 1 2c0 0 1 1 1 2v2c1 0 1 1 1 2-2-2-3-4-5-5v-1l-3-3z" class="I"></path><path d="M176 588c3 1 4 3 7 5l1-1 13 10c-1 1-1 1-1 2-1-1-2-1-3-2-4-3-9-6-13-9l-4-5z" class="V"></path><path d="M157 510l1 1h0l1 3c-1 2-1 3-1 4v1h0v4c-1 2 0 6 0 8h0c1 1 1 1 1 2-1 1 0 2 0 2v1h1c0 1 1 2 0 3-1 0-2 1-2 2h-1v-2c-1-1-1-1-1-2v-1c0 1 1 1 1 2l1-1-1-3s1 0 1-1-2-8-2-11c1-1 1-1 0-2v-9l1-1z" class="D"></path><path d="M158 519c-1-2-1-5-1-8h1l1 3c-1 2-1 3-1 4v1z" class="U"></path><path d="M101 485l2 2v1h0l-2 3h-3l-3 1h-2c-1-1-1-2-1-2l-1-1v-1c1 0 1 0 2-1h-1 0l1-2c3 2 4 2 7 1 0 0 1 0 1-1z" class="L"></path><path d="M100 488c1 0 1-1 2-1l1 1-2 3h-3l2-3z" class="F"></path><path d="M92 490h2l2-2h0l1 1c1 0 2 0 3-1l-2 3-3 1h-2c-1-1-1-2-1-2z" class="C"></path><path d="M151 498c1-1 1-2 1-2 1-3 3-5 5-7 2-1 4-1 6 0-1 0-1 1-1 1l-2 2-2 4c-1 0-2 1-3 2l1-3-3 3v1l-1 1c0 1 0 2-1 2v-2c1 0 0-1 0-2h0z" class="G"></path><path d="M156 495h0c1-1 2-2 4-3l-2 4c-1 0-2 1-3 2l1-3z" class="n"></path><path d="M197 602c8 5 16 10 26 11 1 1 8 1 9 2v2c-2 1-6-1-7-1-6-2-12-4-18-7l-11-5c0-1 0-1 1-2z" class="G"></path><path d="M132 512c0 1 0 1 1 1v1l1-1c0-2 0-3-1-5l-2-15 1-1c0 1 0 3 1 4 0-1 1-1 1-2-1 10 1 19 3 28 1 3 2 5 2 9-3-6-6-13-7-19z" class="F"></path><path d="M158 519v-1c0-1 0-2 1-4 2 4 5 9 5 13l1 1c-2 2-5 0-6 2v1h-1c0-2-1-6 0-8v-4h0z" class="R"></path><path d="M158 519l4 9h2v-1l1 1c-2 2-5 0-6 2v1h-1c0-2-1-6 0-8v-4z" class="F"></path><path d="M183 534c2 0 4 0 5 1l2 1c2 3 3 6 4 9 0 3-2 5-4 7-1 0-1 0-2 1v1h-1-1l1-1 2-2c2-4 2-6 2-10-2-1-3-3-4-4-2 0-2-1-3-2-1 0-2 1-4 1v-1c1 0 2-1 3-1z" class="H"></path><path d="M184 535c2 0 4 0 5 1 1 2 2 3 2 5-2-1-3-3-4-4-2 0-2-1-3-2z" class="U"></path><defs><linearGradient id="BH" x1="166.898" y1="576.543" x2="170.7" y2="574.534" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#929090"></stop></linearGradient></defs><path fill="url(#BH)" d="M162 564h0c2 3 4 6 6 10 5 6 10 13 16 18l-1 1c-3-2-4-4-7-5l-1-1c0-1-3-5-4-5-3-4-6-9-9-14 0-1 0-2-1-3h0v-1h1 0z"></path><path d="M106 474c2 1 4 2 6 4 4 3 7 7 10 11 5 6 8 15 10 23h-2 0c-2-4-3-9-4-13-3-7-7-12-12-18-3-2-5-5-8-6v-1z" class="K"></path><path d="M188 564v-1l1 1c0 1 0 1 1 2 0 1 1 2 1 3h0-2 0c0 2 1 1 2 2v1 1h1 1 0c0-1 1-1 2-2h0l-1 2c0 1 1 1 1 2h2c-1 0-2 1-3 2h0l-3 3h0l-1-2-1-2c-1 0-1-1-1-2h-2v3c-1-5 0-9 2-13z" class="Q"></path><path d="M189 575h0c-1-1-2-5-2-6 1-1 1-2 3-3 0 1 1 2 1 3h0-2 0c0 2 1 1 2 2v1 1h0c-1 0-1-1-2-1v3z" class="C"></path><path d="M192 573h1 0c0-1 1-1 2-2h0l-1 2c0 1 1 1 1 2h2c-1 0-2 1-3 2h0l-3 3h0l-1-2c0-1 0-2-1-3v-3c1 0 1 1 2 1h0 1z" class="I"></path><path d="M192 573h1 0c0-1 1-1 2-2h0l-1 2c0 1 1 1 1 2h-2c-1-1-1-1-1-2z" class="B"></path><path d="M144 530c-1-5-3-11-3-17 0-3 0-14 2-16h2c0 2-1 5-1 8s0 6-1 9c0 5 1 10 2 15v1h0-1z" class="G"></path><path d="M208 568l1 1 2 1 3 3v1h-1 1v2c-1 2-2 2-2 4v1c1 1 0 2 0 3l1 2-1 3h0-1c-1 1-1 3-1 4v-5-9l-3-4-1-2 2-1v-4z" class="F"></path><path d="M208 568l1 1c0 1-1 2 0 3l-2 3-1-2 2-1v-4zm3 2l3 3v1h-1l-2 2s-1-1-1-2l1-4z" class="B"></path><path d="M213 574h1v2c-1 2-2 2-2 4v1c1 1 0 2 0 3l1 2-1 3h0-1c-1 1-1 3-1 4v-5c1-4 0-9 1-12l2-2z" class="V"></path><defs><linearGradient id="BI" x1="200.93" y1="613.302" x2="202.277" y2="608.24" xlink:href="#B"><stop offset="0" stop-color="#5a5a58"></stop><stop offset="1" stop-color="#888687"></stop></linearGradient></defs><path fill="url(#BI)" d="M191 605c10 5 20 10 31 13h0c0 1 0 1-1 1l-2-1h-5c-1-1-2-1-3-1l-7-3-8-3-9-5 1-1h1l1 1 1-1z"></path><path d="M195 589c-1-3-2-5-2-8 1-1 2-2 3-2 2-2 5-1 8-1 1 1 2 2 2 3v2h-5l-4 4c0 1 0 1 1 2l-1 1-2-1z" class="R"></path><path d="M204 614l7 3c1 0 2 0 3 1h5l2 1c1 0 1 0 1-1h0l9 2-1 1c1 1 3 1 4 1v1c0 1 0 1 1 2h0 0-2-1c-5 0-11-2-16-5-1-1-2-1-4-2-1 0-1 0-2-1-2 0-4-1-6-3z" class="D"></path><path d="M214 618h5l2 1c1 0 1 0 1-1h0l9 2-1 1h-4c-4 1-8-1-12-3z" class="K"></path><path d="M214 574c1 0 3 1 3 2l1 1c0 1 1 7 2 7h0l1 1c-2 1-3 1-3 3v1c-1 2-1 4-1 6v-2l-2-2v-1c-1-1-2-2-2-4l-1-2c0-1 1-2 0-3v-1c0-2 1-2 2-4v-2h-1 1z" class="H"></path><path d="M215 590l1-1h1v4l-2-2v-1h0z" class="P"></path><path d="M214 574l1 1c0 2-1 4-1 7v3c-1 2 0 3 1 5h0 0c-1-1-2-2-2-4l-1-2c0-1 1-2 0-3v-1c0-2 1-2 2-4v-2zm-109-87c-1-2-2-4-3-5h1l2 2s1 2 2 2l1-1h-1l1-1 2 1-2 2c-1 0-1 1-2 2h0l2 1c-1 1-1 2-2 3 0 1 0 1-1 2h-1-1c-2 0-3 0-4 1h-2-3-1l-1-1 1-1-2-1v-1h4l3-1h3l2-3h0l2-1z" class="L"></path><path d="M105 487c0 1 0 2-1 4l-3 3h-3l3-3 2-3h0l2-1z" class="P"></path><path d="M98 491h3l-3 3h-4-1l-2-1v-1h4l3-1z" class="E"></path><path d="M160 548c1 0 2 0 3 1v1c1 1 2 1 3 2s2 3 2 4c2 4 3 11 7 14h1c1 1 1 3 2 5 2 4 4 9 7 13 3 3 6 6 9 8 2 1 4 2 5 4-3-1-6-3-8-6-2-1-4-3-6-5-1-2-3-7-6-7-1-1-4-7-5-8-1-4-3-8-5-11 0-1 0-2-1-2v-2c0-1-1-2-1-2 0-1-1-1-1-2s-1-1-1-2c-1-1-2-1-2-2-2 0-3-2-3-3z" class="F"></path><path d="M115 490l1-1c5 9 8 19 12 30l3 8c1 2 2 3 2 5l-1 1-13-29c0-4-3-11-4-14z" class="c"></path><path d="M164 517l-1-3h1l1 1h1c3 2 4 3 7 3 0 3-1 6-1 8-1 2-1 3-2 4-2-1-3-2-4-2 0-2-1-3-1-4v-2c0-1-1-1-1-2h1c-1-1-1-2-1-3z" class="F"></path><path d="M164 517l-1-3h1l1 1c1 2 3 4 4 6v4h-1c-1-1-1-3-2-4-1-2-2-3-2-4z" class="E"></path><path d="M166 528c1 0 2 1 4 2h-1c0 1 0 1 1 1l-1 1v1 2 1l-1 2v1l-1-1v1c-2-1-5 0-7 0 1-1 0-2 0-3h-1v-1s-1-1 0-2c0-1 0-1-1-2h0 1v-1c1-2 4 0 6-2h1z" class="R"></path><path d="M165 537l1-1h3l-1 2v1l-1-1h-1-1v-1z" class="I"></path><path d="M166 528c1 0 2 1 4 2h-1c0 1 0 1 1 1l-1 1v1 2l-3-3v-1c1-1 1-2 1-2l-2-1h1z" class="E"></path><path d="M158 531h1c0 1 0 1 1 2l1-1h3 0l-2 2v1 1h-1-1-1v-1s-1-1 0-2c0-1 0-1-1-2h0z" class="H"></path><path d="M162 535h0c1 0 1-1 2-1v2l1 1v1h1 1v1c-2-1-5 0-7 0 1-1 0-2 0-3h1 1v-1z" class="F"></path><path d="M133 532l14 26c2 4 6 8 7 12v1 1h0l-5-6-1 1c-3-3-4-6-7-9v-1-2c2 3 4 6 7 8-2-4-5-8-7-13-3-5-6-11-9-17l1-1z" class="K"></path><path d="M160 506c2 0 4 1 6 1 1 0 1 0 2 1l1 2h0c1 0 1 0 2-1l1 1h-2 0c-2 2-5 1-7 3l3 2h-1l-1-1h-1l1 3c0 1 0 2 1 3h-1c0 1 1 1 1 2v2c0 1 1 2 1 4h-1l-1-1c0-4-3-9-5-13l-1-3h0l-1-1v-2c1-1 2-1 3-2z" class="V"></path><path d="M157 508c1-1 2-1 3-2l1 1s1 1 0 2l-2 2h-1l-1-1v-2z" class="M"></path><path d="M166 507c1 0 1 0 2 1l1 2h-3c-1 1-3 1-4 2 0-1-1-2-1-3 2-1 3-1 5-2z" class="E"></path><path d="M169 510h0c1 0 1 0 2-1l1 1h-2 0c-2 2-5 1-7 3l3 2h-1l-1-1h-1l1 3c0 1 0 2 1 3h-1c0 1 1 1 1 2v2c-1-2-1-3-2-4s-2-2-2-3 0-2-1-3c1-1 1-2 2-2 1-1 3-1 4-2h3z" class="D"></path><path d="M130 512h2 0c1 6 4 13 7 19 2 5 4 10 6 14 7 15 17 29 27 41h0c-7-6-12-14-17-22-6-9-12-18-16-28-4-8-6-16-9-24z" class="o"></path><defs><linearGradient id="BJ" x1="164.788" y1="553.647" x2="143.661" y2="544.826" xlink:href="#B"><stop offset="0" stop-color="#7f7c7c"></stop><stop offset="1" stop-color="#aaaaab"></stop></linearGradient></defs><path fill="url(#BJ)" d="M145 529h0 1c0 1 0 2 1 3v-3c1 2 1 3 1 5 1 0 1 0 1-1 1 6 3 12 5 18 2 4 5 8 8 13h0-1v1h0c1 1 1 2 1 3-3-3-6-9-8-14-2-3-4-7-5-10-2-4-4-9-5-14h1 0v-1z"></path><path d="M145 529h0c1 2 1 3 2 4 0 2 0 4 1 6 0 1 1 3 1 4v1c-2-4-4-9-5-14h1 0v-1zm12 18c2-1 4-2 6-2 3 3 5 6 7 9 0-3 0-5-1-8l1-1v1c1 0 1 0 2-1v4c1 1 1 2 1 2 0 7 1 13 3 19h-1c-4-3-5-10-7-14 0-1-1-3-2-4s-2-1-3-2v-1c-1-1-2-1-3-1h-2l-1-1z" class="K"></path><path d="M182 503h0l1 3v4l3 9c1 3 1 6 2 9-1 2-3 4-4 5l-1-1h0c-1-2-2-3-3-4 0-1-1-2-2-2l1-1c2 0 3 1 4 1h0 1c0-1 1-1 1-2s0-2-1-4v-2c0-1-1 0-2-1v-1h-2 0c-1 0-1 0-1-1v-1-1l-2-1h0v-1h-2v-1s1-1 2-1 2 0 2-1c2-1 2-3 3-5z" class="H"></path><path d="M177 512l3-3h1c0 2-1 4-2 5v-1l-2-1h0zm5-1l1 1c0 1 1 4 0 5h-1v-1h-2c1-2 2-3 2-5z" class="E"></path><path d="M181 509c0 1 0 1 1 2v-1 1c0 2-1 3-2 5h0c-1 0-1 0-1-1v-1c1-1 2-3 2-5z" class="D"></path><path d="M259 621l1 1c0 1 1 2 2 2 0 1 0 2 1 3v1c0 1-2 0-3 0-5 0-10-1-15-1h0c-4 0-8-1-13-2h1 2 0 0c-1-1-1-1-1-2v-1c4 1 9 2 13 2h1l1-1c2 0 6 0 8 1l1-1 1-2z" class="n"></path><path d="M245 627h-1-2c-1-1-2-1-3-2h0c4 0 7 0 10 1 4 0 8 1 12 1h2v1c0 1-2 0-3 0-5 0-10-1-15-1h0z" class="Z"></path><path d="M259 621l1 1c0 1 1 2 2 2 0 1 0 2 1 3h-2c-4 0-8-1-12-1l1-2h-3 1l1-1c2 0 6 0 8 1l1-1 1-2z" class="G"></path><path d="M259 621l1 1c0 1 1 2 2 2 0 1 0 2 1 3h-2 0c-1-1-2-1-3-2h1v-1h-2 0l1-1 1-2z" class="K"></path><path d="M250 624l8 1c1 1 2 1 3 2h0c-4 0-8-1-12-1l1-2z" class="F"></path><defs><linearGradient id="BK" x1="498.248" y1="537.234" x2="508.713" y2="548.655" xlink:href="#B"><stop offset="0" stop-color="#9f9d9f"></stop><stop offset="1" stop-color="#c9c9c7"></stop></linearGradient></defs><path fill="url(#BK)" d="M510 536l1 1c4 0 7-1 10 0h-1l1 2 1 1h-1-7c-8 1-16 4-23 8-3 1-5 2-7 3l2-2 2-2h-1 1v-1l1-1c4-2 9-4 14-5 0-1 1-1 1-2h-2l1-1c1 0 2 0 4 1h0l3-1v-1z"></path><path d="M489 545v1l-1 1v-1l1-1z" class="c"></path><path d="M503 537c1 0 2 0 4 1h0c0 1-1 1-2 2h-2c0-1 1-1 1-2h-2l1-1zm-309 8c1 1 2 2 2 3v1l3 4h-2c-1 1-1 2-1 3h-1v1l1 1c0 2 1 3 1 4-1 1-1 2-2 3 0-1-1-2-1-2 0-1-1-1-1-2-1 0-3 0-4 1h0l-2-2c-1 1-2 2-2 3h-1-1l1-2h-1l3-7h1 1v-1c1-1 1-1 2-1 2-2 4-4 4-7z" class="D"></path><path d="M196 549l3 4h-2c-1 1-1 2-1 3h-1c0-2 0-5 1-7z" class="C"></path><path d="M194 550c1 2 1 3 1 4l-3 3c1 1 2 3 1 4h0c-1 0-3 0-4 1h0l-2-2c2-3 5-6 7-10z" class="P"></path><path d="M194 545c1 1 2 2 2 3l-2 2c-2 4-5 7-7 10-1 1-2 2-2 3h-1-1l1-2h-1l3-7h1 1v-1c1-1 1-1 2-1 2-2 4-4 4-7z" class="B"></path><path d="M186 554h1 1v-1c1-1 1-1 2-1 0 2-2 3-3 5l-3 4h-1l3-7z" class="L"></path><defs><linearGradient id="BL" x1="152.247" y1="483.724" x2="156.407" y2="490.982" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#BL)" d="M148 498l1-4c1-4 3-11 7-13 1-1 2-1 3 0 2 0 3 2 4 4 1 0 2-1 2-2l1 1v1 1l-2 1c2 0 2 0 3 1 0 1-1 1-1 3v1l-1-1v-2h-2c-2-1-4-1-6 0-2 2-4 4-5 7 0 0 0 1-1 2v-1l-1-1-1 2h-1z"></path><path d="M165 483l1 1v1 1l-2 1h0-2c0-1-1 0-2 0h-1c-2 1-5 3-6 5v-2l2-2 1-1c1-1 3-2 5-2 0 0 0 1 1 1h1v-1c1 0 2-1 2-2z" class="G"></path><path d="M153 492c1-2 4-4 6-5h1c1 0 2-1 2 0h2 0c2 0 2 0 3 1 0 1-1 1-1 3v1l-1-1v-2h-2c-2-1-4-1-6 0-2 2-4 4-5 7 0 0 0 1-1 2v-1l-1-1 3-4z" class="F"></path><path d="M488 546v1h-1 1l-2 2-2 2c-1 0-2 1-3 2 0 0-2 1-2 2l-18 14-1 1c-3 0-4 1-5 3-1-1 0 0-1 0v-1l5-5c0-2 1-4 2-5 2 0 2-1 3-2 2-1 4-3 6-4l1 1c2-3 6-5 9-7l8-4z" class="K"></path><path d="M461 562c2 0 2-1 3-2 2-1 4-3 6-4l1 1c-2 1-10 9-12 10 0-2 1-4 2-5z" class="P"></path><defs><linearGradient id="BM" x1="465.516" y1="566.367" x2="472.143" y2="555.294" xlink:href="#B"><stop offset="0" stop-color="#a5a2a5"></stop><stop offset="1" stop-color="#c1c2bf"></stop></linearGradient></defs><path fill="url(#BM)" d="M461 569v-1c1-1 2-3 3-4 1-2 5-5 7-6s4-3 5-3c1-1 2 0 3 0l-18 14z"></path><path d="M120 484h1c1 0 3 1 3 2 2 1 2 2 4 3l1 1c1 1 2 0 3 1h1 1c1 1 0 2 0 3h0c0 1-1 1-1 2-1-1-1-3-1-4l-1 1 2 15c1 2 1 3 1 5l-1 1v-1c-1 0-1 0-1-1h0c-2-8-5-17-10-23 0-1 0-2-1-3 0 0 0-1-1-2z" class="m"></path><path d="M207 579c1 0 1 1 2 2-1 2-3 4-3 7 1 0 1 0 2 1h0c-1 2-1 4 0 5 1 4 2 5 4 8l2 2v1 1c-2-1-5-2-8-4-1-2-2-3-3-4-2-2-2-4-1-6-2 0-2 0-3-2v-1h-1c-1-1-1-1-1-2l4-4h5v-2c1 0 0 1 1 2v-1-3z" class="F"></path><path d="M197 587c1 0 2 0 3 1l-1 1h-1c-1-1-1-1-1-2zm9 3c1 1 0 3 1 5 0 1 1 2 0 2l-2-3v-3h1v-1z" class="H"></path><path d="M203 595l1-1h1l2 3v1h-1l-3-3z" class="B"></path><path d="M205 588s1 1 1 2v1h-1-1c0 1-1 1-1 2 0-1 0-1-1-2l3-3z" class="c"></path><path d="M200 588l2-2h2l1 2-3 3v1c-2 0-2 0-3-2v-1l1-1z" class="E"></path><path d="M203 593c0-1 1-1 1-2h1v3h-1l-1 1v1c2 4 7 8 11 9v1c-2-1-5-2-8-4-1-2-2-3-3-4-2-2-2-4-1-6v-1c1 1 1 1 1 2z" class="G"></path><path d="M203 593c0-1 1-1 1-2h1v3h-1l-1 1v1-3z" class="C"></path><defs><linearGradient id="BN" x1="176.226" y1="551.658" x2="189.954" y2="572.246" xlink:href="#B"><stop offset="0" stop-color="#c0bfbf"></stop><stop offset="1" stop-color="#eeeeef"></stop></linearGradient></defs><path fill="url(#BN)" d="M181 549v1h1c2-1 1-3 3-2l-1 1h0 1c1 2 1 3 2 4l-1 1-3 7h1l-1 2h1 1c0-1 1-2 2-3l2 2h0v2l-1-1v1l-1-1c-2 0-2 1-4 2 0 3-1 5 0 7v5l1 1v1c0 1 0 1 1 1l-1 1h0l-1-2s-1 0-1-1v-1c-2-2-2-4-3-6-1-5-2-10-1-16 0-2 0-4 1-6h2z"></path><path d="M185 563c0-1 1-2 2-3l2 2c-2 1-2 1-4 1z" class="L"></path><path d="M207 620c18 7 36 11 54 14 21 4 43 5 64 4 10-1 20-3 30-5 9-2 17-4 25-7l8-3 5-3 1 1h-1l-19 8c-5 2-11 4-16 5-36 8-74 6-110-1-14-3-28-7-42-12l1-1z" class="n"></path><path d="M128 476l1-1-1-1 1-1c1 0 2 1 3 1l-1 2s1 1 2 1v1l1 1v2c1 2 2 2 2 4v1c-1 1-1 3-1 4l-1 1h-1-1c-1-1-2 0-3-1l-1-1c-2-1-2-2-4-3 0-1-2-2-3-2h-1 0l1-1v-4h0l1-2c-1 0-1 0-2-1h0l1-1s1 1 2 1l1 1h2l2-1z" class="E"></path><path d="M127 483h1 0 1l1 1h1v-1 1 1h-1 0c0 1 1 1 1 2l-1 1c-1-1-2-2-3-4v-1z" class="L"></path><path d="M123 481h2l2 2v1 1c-1 1-1 1-2 1-1-1-2-3-2-5h0z" class="H"></path><path d="M120 476l1-1s1 1 2 1l1 1h2c1 1 1 1 1 2s1 3 1 4h0-1l-2-2h-2 0c-1-2 0-2 0-4h-1c-1 0-1 0-2-1h0z" class="F"></path><path d="M124 477h2c1 1 1 1 1 2h-1l-2-2z" class="L"></path><path d="M126 479h1c0 1 1 3 1 4h0-1l-2-2h-2c0-1 2-1 3-2z" class="P"></path><path d="M128 476l1-1-1-1 1-1c1 0 2 1 3 1l-1 2s1 1 2 1v1l1 1v2c1 2 2 2 2 4v1l-1-1h-1l-1 1c-1 0-1 0-2-1v-1-1 1h-1l-1-1h-1c0-1-1-3-1-4s0-1-1-2l2-1z" class="C"></path><path d="M133 478l1 1v2c0 1 0 1-1 2v-1c-1-2-1-3 0-4z" class="K"></path><path d="M134 481c1 2 2 2 2 4v1l-1-1-2-2c1-1 1-1 1-2z" class="Z"></path><path d="M129 483v-3c-1 0-1 0-1-1l1-1v-1l1-1v2c0 2 0 4 1 5v1h-1l-1-1z" class="P"></path><path d="M120 467h0v1l2-1v2c0 1-1 2-2 2l-1 1c0 1 0 1 1 2 0 1 0 1 1 1l-1 1h0c1 1 1 1 2 1l-1 2h0v4l-1 1h0c1 1 1 2 1 2 1 1 1 2 1 3-3-4-6-8-10-11-2-2-4-3-6-4l-1-1c-1 0-1 0-2-1h-1c-1-1-2-1-3-2v-1h3 2l1 1h1 1l1-1 2 1 2-2c1 0 3 2 4 2l1 1 1-1h0c0-1 1-2 2-3z" class="B"></path><path d="M116 472h1s0 1 1 1l-1 1h-1-1l1-2z" class="E"></path><path d="M110 472c1-1 1-1 2 0v3c-1-1-1-2-2-2v-1z" class="C"></path><path d="M120 476v2c-1 0-1-1-1-1-1-1-1-1 0-2h0l1 1h0z" class="E"></path><path d="M120 468l2-1v2c0 1-1 2-2 2h-1v-1h2v-1l-1-1h0z" class="H"></path><path d="M121 483l-3-3h1c1 0 1 0 1-1h0 1v4z" class="F"></path><path d="M148 567l1-1 5 6h0v-1-1c6 6 11 13 16 19 7 6 13 11 21 16l-1 1-1-1h-1l-1 1c-1-1-3-2-4-3l-11-9-4-3-6-5-10-13c-1-2-3-4-4-6z" class="J"></path><defs><linearGradient id="BO" x1="210.282" y1="597.746" x2="237.684" y2="602.344" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#c7c5c6"></stop></linearGradient></defs><path fill="url(#BO)" d="M213 586c0 2 1 3 2 4v1l2 2v2l1 3c5 8 14 12 23 15l-1 1c-4-1-7-2-11-3h0c-4 0-7-1-10-3-2 0-3-1-5-2v-1-1l-2-2h1v-2c-1-2-2-5-3-7 0-1 0-3 1-4h1 0l1-3z"></path><path d="M210 593c0-1 0-3 1-4h1c1 2 1 3 0 4v-1c-1 1-1 1-2 1z" class="G"></path><path d="M218 598c-2-1-3-1-3-3l-1-3 1-1 2 2v2l1 3z" class="c"></path><path d="M213 600l5 5c3 3 7 4 11 6h0c-4 0-7-1-10-3-2 0-3-1-5-2v-1-1l-2-2h1v-2z" class="I"></path><defs><linearGradient id="BP" x1="472.847" y1="548.139" x2="475.509" y2="552.855" xlink:href="#B"><stop offset="0" stop-color="#747273"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#BP)" d="M493 537c3-1 5-2 7-1l3 1-1 1h2c0 1-1 1-1 2-5 1-10 3-14 5l-1 1-8 4c-3 2-7 4-9 7l-1-1c-2 1-4 3-6 4-1 1-1 2-3 2l3-3 13-14c1 0 3-1 4-2l5-2 1-1h2c1-1 3-2 4-3z"></path><path d="M489 540h0c0 1 0 1-1 2h-2 0v-1l1-1h2z" class="o"></path><path d="M470 556c3-3 6-6 10-8l-3 3s2-1 3-1c-3 2-7 4-9 7l-1-1z" class="F"></path><path d="M493 537c3-1 5-2 7-1l3 1-1 1h2c0 1-1 1-1 2-5 1-10 3-14 5l-1 1-8 4c-1 0-3 1-3 1l3-3c0-1 1-1 1-1 2-1 4-3 6-4l1-1c1-1 1-1 1-2h0c1-1 3-2 4-3z" class="B"></path><path d="M489 540c2 0 4 0 5 1l-7 2 1-1c1-1 1-1 1-2z" class="c"></path><path d="M489 540c1-1 3-2 4-3 1 2 2 2 4 3-1 0-2 1-3 1-1-1-3-1-5-1h0z" class="D"></path><path d="M493 537c3-1 5-2 7-1l3 1-1 1-5 2c-2-1-3-1-4-3z" class="e"></path><path d="M108 490h0c0 1 0 1 1 2l5 5 1 1-1 1c0 1 1 2 0 3h0c-2-1-2-1-3-1-4 1-7 1-10 3h0v1c-1 2-2 3-1 5l2 2c-1 0-1 1-2 1h-1c0-1-1-2-1-3-1-2 0-4 0-6-2 0-3-1-5-1-1 0-1 0-2-1h0c-1-1-2-1-2-2h-1v-1c1 0 2 0 3-1h1c0-1 1-1 1-2h1 3 2c1-1 2-1 4-1h1 1c1-1 1-1 1-2 1-1 1-2 2-3z" class="C"></path><path d="M101 504h-1c2-3 5-3 7-4h2c2 0 2 0 2-1 2 1 2 2 3 3-2-1-2-1-3-1-4 1-7 1-10 3h0zm7-14h0c0 1 0 1 1 2l5 5c-1 0-1 1-2 1h-1v-1h0l-2 1h-2-1l-3-3h1 1c1-1 1-1 1-2 1-1 1-2 2-3z" class="D"></path><path d="M107 498v-1c0-2 0-2 1-4h1c0 2 1 3 2 4l-2 1h-2z" class="V"></path><path d="M97 496h2c1-1 2-1 4-1l3 3-2 3-1-1h-3c0 1-1 1-2 1h-3c0-1-1-1-1-1-1 1-1 2-1 3-1 0-1 0-2-1h0c-1-1-2-1-2-2h-1v-1c1 0 2 0 3-1h1c0-1 1-1 1-2h1 3z" class="E"></path><path d="M93 496h1 3c-2 1-5 3-7 3l-1 1h-1v-1c1 0 2 0 3-1h1c0-1 1-1 1-2z" class="D"></path><path d="M463 556h1 0v3l-3 3c-1 1-2 3-2 5l-5 5v1c1 0 0-1 1 0 1-2 2-3 5-3l-10 9-19 17h-3c1-1 1-2 1-3 3-2 5-4 6-7l7-6 2-2 6-7h0l13-15z" class="Q"></path><path d="M454 572v1c1 0 0-1 1 0 1-2 2-3 5-3l-10 9c-1 0-1 0-2 1 0-1-1-1-1 0-1 0-3 2-4 3s-3 2-4 4c0-2 0-2 1-3h0v-1c3-1 5-4 7-6s5-3 7-5z" class="S"></path><path d="M463 556h1 0v3l-3 3c-1 1-2 3-2 5l-5 5c-2 2-5 3-7 5s-4 5-7 6v1-1c1-1 1-1 2-3l2-2 6-7h0l13-15z" class="n"></path><path d="M134 572h0l1 1c1-1 1-1 2-1l3 4h0c1-1 0-1 1-2 2 2 5 5 7 8h0l-1 1 1 1c1-1 1-2 2-3 0-2-1-4-2-5h0c1 1 3 3 4 3 0 2-2 3-2 5h1c1 1 1 1 2 3 0-1 0-1 1-1h0s1 0 1-1c1-1 2 0 3 0h1l1 1h0 2l6 5 4 3c-1 1-1 1-2 1l-1 1h0 1 2v1h1v1c-1 0-1 0-1 1h2v1c-1 0-1 1-2 1v1l1 1c4 3 8 5 12 7 7 4 15 7 22 10l-1 1c-1-1-5-2-6-3-11-5-22-10-32-17l-12-9c-2-1-4-4-7-5h0c-2-1-3-3-5-4-4-3-8-7-10-11z" class="I"></path><path d="M149 587c0-2 0-2 2-3v1l1 1-1 1c-1-1-1-1-2 0zm13 7c0-1 0-1 1-2 1 0 1-1 2-1h0 1l-3 3h-1z" class="C"></path><path d="M160 593c-1 0-1-1-2-2 2-1 3-3 4-3h1l-3 4v1z" class="m"></path><path d="M154 586s1 0 1-1c1-1 2 0 3 0h1l-1 1h-1c-1 1 0 2 0 3h-1c-1-1-2-1-2-3z" class="R"></path><path d="M166 591h1 1l4 3c-1 1-1 1-2 1l-1 1h0 1 2v1h1v1c-1 0-1 0-1 1h2v1c-1 0-1 1-2 1v1h-1c-4-3-8-6-11-9v-1h2v2h1l3-3z" class="U"></path><path d="M172 597h1v1c-1 0-1 0-1 1h2v1c-1 0-1 1-2 1-1-1-2-1-3-1v-1c1-1 2-1 3-2z" class="H"></path><path d="M129 537s1 1 2 1l2 2v1c1 1 1 2 1 3l5 7s1 1 1 2c0 0 1 1 1 2v2 1c3 3 4 6 7 9 1 2 3 4 4 6l10 13h-2 0l-1-1h-1c-1 0-2-1-3 0 0 1-1 1-1 1h0c-1 0-1 0-1 1-1-2-1-2-2-3h-1c0-2 2-3 2-5-1 0-3-2-4-3h0c-2-2-3-4-4-6-4-5-8-11-11-16-2-3-2-7-4-9-2 1-4 1-6 1 0 1-1 2-2 2h0c-1-1-1-1-1-2v-1h0l2 1c2-1 4-1 5-2 1-2 1-3 0-4h0c-1 0-1 1-2 2h0-1l-1-1h1v-1-1l1 1 2-1v1c1 0 1 0 1-1 1-1 1-1 1-2z" class="B"></path><path d="M155 580l1 1-2 1-1-1 2-1z" class="F"></path><path d="M157 582c1 1 1 1 1 3-1 0-2-1-3 0 0 1-1 1-1 1h0c1-1 2-3 3-4z" class="C"></path><path d="M148 571l4 2 10 13h-2 0l-1-1h-1c0-2 0-2-1-3l-1-1-1-1c-2-3-5-6-7-9z" class="D"></path><path d="M141 561c0-2-1-4-2-7 0-1 0 0 1-1 0 0 1 1 1 2v2 1c3 3 4 6 7 9 1 2 3 4 4 6l-4-2-7-10z" class="V"></path><path d="M141 561c-2-2-4-5-5-8s-3-6-6-8v-1c0-1 0-1 1-2-1 0-1-1-1-1v-1c2 1 3 3 4 4l5 7s1 1 1 2c-1 1-1 0-1 1 1 3 2 5 2 7z" class="L"></path><path d="M175 476l2 3 1 2v-1h1v-1l2 11c1 3 1 6 2 9 0 1 1 2 0 2v5l-1-3h0c0-2 0-4-1-5-1 0-1 0-2 1-2 0-4 0-6 1h0c-1 0-1-1-2-1-1 1 0 1 0 2 0 2-1 4-3 4h-2c-1 0-2-2-3-3 2 0 5 1 7-1h0v-1c-1 0-3-1-3-1-1 0-3 0-3 1-2 0-3 0-5 1v-1c1-2 2-1 4-1 0-1 0-1 1-1v-1h0l-1 1v-1h-2l-1-1h1-1-1-1l2-4 2-2s0-1 1-1h2v2l1 1v-1c0-2 1-2 1-3-1-1-1-1-3-1l2-1v-1c1 1 2 2 3 2h2c2 0 3-1 4-3 1-1 1-3 1-5-1-1-1-2-1-3z" class="C"></path><path d="M161 496h0c0-1 0-1 1-1 0-1 1-2 1-2l1 1c-1 1-1 2-1 3h-2l-1-1h1z" class="B"></path><path d="M163 489h2v2l1 1h0v1c-1 0-1 1-2 1v-2-1-1h-2s0-1 1-1z" class="H"></path><path d="M166 491l6 3h0c2 0 4 1 5 0s1-2 2-3c1 1 1 3 1 4-1 1-1 2-3 3s-4 0-6 0c-3-1-4-3-5-5v-1h0v-1z" class="R"></path><path d="M175 476l2 3 1 2v-1h1v-1l2 11h0-1c-1 0-1 1-1 1-1 1-1 2-2 3s-3 0-5 0h0l-6-3c0-2 1-2 1-3-1-1-1-1-3-1l2-1v-1c1 1 2 2 3 2h2c2 0 3-1 4-3 1-1 1-3 1-5-1-1-1-2-1-3z" class="D"></path><path d="M166 485c1 1 2 2 3 2l1 2 3 2h-1-1c-1-1-2-2-4-3-1-1-1-1-3-1l2-1v-1z" class="K"></path><path d="M179 486v3h0c0 1 0 1-1 2h0c-1 0-2 1-3 1s-2-1-3-1h1l1-1c1 0 2-1 3-2v-1l2-1z" class="V"></path><path d="M179 486v3h0c-1 1-2 1-3 1l1-1v-1-1l2-1z" class="G"></path><path d="M175 476l2 3 1 2c1 1 1 2 1 4v4-3-1l-1-1-1 2h-1l-1-2c1-1 1-3 1-5-1-1-1-2-1-3z" class="K"></path><path d="M175 484l1 2h1l1-2 1 1v1l-2 1v1c-1 1-2 2-3 2l-1 1-3-2-1-2h2c2 0 3-1 4-3z" class="J"></path><path d="M121 548c1 0 2-1 2-2 2 0 4 0 6-1 2 2 2 6 4 9 3 5 7 11 11 16 1 2 2 4 4 6 1 1 2 3 2 5-1 1-1 2-2 3l-1-1 1-1h0c-2-3-5-6-7-8-1 1 0 1-1 2h0l-3-4c-1 0-1 0-2 1l-1-1h0c-2-2-2-5-4-7-1 0-1 0-1 1h-2v-1l1-1c-1-2-2-2-2-3v-2l-3-1h0c0 1 0 1 1 2l-1 1c0-1-1-1-1-2-1-2 1-1 1-3l-1-1h1c0-1-1-2-1-3v-1c1 0 1-1 1-1h-1s-1-1-1-2z" class="R"></path><path d="M123 555h2c2-1 3-2 4-3 1 0 1 0 1 1v2c1 1 1 1 1 2 0 2 2 5 3 7l3 2h-1l-3 1h0l1 1c3 0 5 3 6 5l1 1c-1 1 0 1-1 2h0l-3-4c-1 0-1 0-2 1l-1-1h0c-2-2-2-5-4-7-1 0-1 0-1 1h-2v-1l1-1c-1-2-2-2-2-3v-2l-3-1h0c0 1 0 1 1 2l-1 1c0-1-1-1-1-2-1-2 1-1 1-3l-1-1h1z" class="B"></path><path d="M151 460h0c1 0 1-1 1-2v-1h1c1 0 1-1 1-1v-1h1l2 2v1c0 5-1 8-4 11v1h1c0-1 1 0 1 0-1 2-4 3-4 6 0 1 0 1-1 2h0c-2 1-3 3-3 5v-2l-1-1c-1 0-2 0-2 1h-1-1c0-1-1-2-1-3-1 0-2-1-3-1h0c-1 0-3 0-4 1v1l-1-1v-1c2-1 5-2 6-3l-1-1c0-1 1-2 1-2-2-1-3 0-5-2 0 0-1-1-2-1v-2-1c1-1 2-1 3-2l1-1v-1-1c1-1 1-1 2-1l3 1v1c2 0 3-1 4-1h0c1 1 1 1 1 2 0 2 2 3 3 3 1 1 1 0 2 0 1-1 0-3 0-5z" class="E"></path><path d="M145 463c1 1 1 1 2 3h-1l-2-2 1-1z" class="R"></path><path d="M144 479c-1 1-1 1-2 1 0-1 0-1-1-2l1-1 2 1v1z" class="C"></path><path d="M139 474l1-1v1c0 1 0 2 1 3h-3c-1 0-3 0-4 1v1l-1-1v-1c2-1 5-2 6-3z" class="L"></path><path d="M151 460h0c1 0 1-1 1-2v-1h1c1 0 1-1 1-1v-1h1c-1 2-1 5-1 7h-1c0 2-1 4-3 5l-3-1h0c-1-2-1-2-2-3v-3h0c1 1 1 1 1 2 0 2 2 3 3 3 1 1 1 0 2 0 1-1 0-3 0-5z" class="B"></path><path d="M144 468c2-1 2-1 4 0 1 1 0 2 0 4 1 0 2-1 3 0h0c-1 2-2 2-3 4-1 1-1 1-2 1-1 1-1 2-2 2v-1-1h0c-1-1-1-2-1-3s-1-2-1-3l2-2v-1z" class="I"></path><path d="M144 468c2-1 2-1 4 0 1 1 0 2 0 4l-1 1-1 1-1-1c0-1 0-2-1-3v-2z" class="C"></path><path d="M138 459l3 1v1c2 0 3-1 4-1v3l-1 1c-1 2-3 4-4 7h-1c-2-1-3 0-5-2 0 0-1-1-2-1v-2-1c1-1 2-1 3-2l1-1v-1-1c1-1 1-1 2-1z" class="Q"></path><path d="M139 464l2-2h1c0 2-2 4-3 6h-3s0-1 1-1c1-1 1-2 2-2h1v-1h-1z" class="S"></path><path d="M138 459l3 1c-1 0-2 1-2 1v2l-1 1h1 1v1h-1c-1 0-1 1-2 2-1 0-1 1-1 1l-2 1s-1-1-2-1v-2-1c1-1 2-1 3-2l1-1v-1-1c1-1 1-1 2-1z" class="J"></path><path d="M138 459c0 2-1 3-2 5l-1-1 1-1v-1-1c1-1 1-1 2-1z" class="h"></path><path d="M135 463l1 1h0c-1 1-1 2-1 3h-1l-2-1v-1c1-1 2-1 3-2z" class="O"></path><path d="M96 519c-1-1-2-3-1-5h1c2 0 5 3 6 5h1c0 1-1 2-1 2 1 2 1 2 1 3 1 0 1 0 1-1 2-1 3-2 4-4l2 1c1 1 1 1 2 1h1c3 2 6 4 8 7 1 2 3 4 4 5h1l1 2c1 0 1 1 2 2 0 1 0 1-1 2 0 1 0 1-1 1v-1l-2 1-1-1v1l-3-2c-1 1-2 1-3 2l-1-1v-1c-1 0-1 0-2 1h-1c-1 1-2 3-3 4v1h0l1 1h-2c-1 0 0-1-1-2h0c-1-1-1-2-1-3s0-2-1-2l-1-3c-1 0-1-1-2-1v-1c0-2 4-1 5-1v-1c0-1-1-2-2-3l-3-1h-1c-2 0-2 0-3-1l1-1h0c0-1 0-1-1-1s-2-1-2-1c-1-1-1-3-2-4z" class="F"></path><path d="M124 536h1c1 1 1 2 2 3l-2 1-1-1v-1-2zm-22-15c-1 0-1-1-2-2-1 0-2-1-3-1-1-1-1-1-1-2v-1c2 1 4 3 6 4h0 1c0 1-1 2-1 2z" class="U"></path><path d="M96 519c3 0 3 1 5 3l1 2s-1 0-1 1c0-1 0-1-1-1s-2-1-2-1c-1-1-1-3-2-4z" class="B"></path><path d="M108 519l2 1c1 1 1 1 2 1h1c-1 1-2 1-2 1l-1 1 1 1h-2v-1l-1-1c-1 1-2 1-2 2-1 0-1 0-2-1 2-1 3-2 4-4z" class="C"></path><path d="M114 537h-1c-1-1-2-3-2-5h0l3 3h0 1 4v-1l-1-1v-1c1 0 1 0 2 1 1 0 1 1 1 2h2l1 1c-3 1-7-1-9 0l-1 1z" class="H"></path><path d="M124 536h0v2 1 1l-3-2c-1 1-2 1-3 2l-1-1v-1c-1 0-1 0-2 1l-1-2 1-1c2-1 6 1 9 0z" class="E"></path><path d="M121 538h3v1 1l-3-2z" class="I"></path><path d="M114 537l1-1 5 1h0c-1 1-2 1-3 2v-1c-1 0-1 0-2 1l-1-2z" class="U"></path><path d="M101 525c1 1 3 0 5 0h1 1c1 1 1 1 1 2h3c0-1 1-2 1-2 1 0 3 1 3 1 2 1 2 2 3 3v1l-2-1c-1-1-2-2-4-2l-3 3s0 1-1 1c0-1-1-2-2-3l-3-1h-1c-2 0-2 0-3-1l1-1z" class="E"></path><path d="M109 531c1 0 1-1 1-1l3-3c2 0 3 1 4 2h0c0 2 0 3-1 4-1 0-1 1-1 2h-1 0l-3-3h0c0 2 1 4 2 5h1l1 2h-1c-1 1-2 3-3 4v1h0l1 1h-2c-1 0 0-1-1-2h0c-1-1-1-2-1-3s0-2-1-2l-1-3c-1 0-1-1-2-1v-1c0-2 4-1 5-1v-1z" class="I"></path><path d="M104 534c1 0 1 0 1-1h2v1l-1 1c-1 0-1-1-2-1z" class="E"></path><path d="M111 539h1v1c0 1-1 1-2 2h-1v-1l2-2z" class="q"></path><path d="M116 533l-1-1c0-1-1-2-1-2v-1h0 3c0 2 0 3-1 4z" class="U"></path><path d="M107 538c2 0 3 0 4 1l-2 2v1 1h0c-1-1-1-2-1-3s0-2-1-2z" class="E"></path><path d="M109 455c1-1 2-3 4-3h1l1 3h0c1 1 1 2 0 3 1-1 3-4 5-4 1 0 3 0 4 2l2 2c0 1 1 3 1 4v5 1c1-1 3-1 4-1l1 1c1 0 2 1 2 1 2 2 3 1 5 2 0 0-1 1-1 2l1 1c-1 1-4 2-6 3-1 0-2-1-2-1l1-2c-1 0-2-1-3-1l-1 1 1 1-1 1-2 1h-2l-1-1c-1 0-2-1-2-1-1 0-1 0-1-1-1-1-1-1-1-2l1-1c1 0 2-1 2-2v-2l-2 1v-1h0c-2 1-4 1-5 0l-4-1-2-2-1 1h1v1h-1c-2-2-3-4-3-6s0-2 1-4l1-1h1c0 1-1 1-1 2h1l1-2z" class="R"></path><path d="M110 459h1l-1 2c0 1-1 2-2 2l-1-1 3-3z" class="B"></path><path d="M115 467c2-1 3-1 5-3h0l-2-1v-1c2 0 3 0 4 1l1 1c-1 1-1 2-1 3l-2 1v-1h0c-2 1-4 1-5 0z" class="C"></path><path d="M128 476c-1-1-2-1-3-1v-1h0l1-2h1s0-1 1-1h1c1 0 2 0 3 1 1 0 1 0 2-1v1c0 1 0 1-1 2h-1c-1 0-2-1-3-1l-1 1 1 1-1 1z" class="E"></path><path d="M152 449c5-2 7-3 12-1 3 1 6 5 8 8 0-1 1-1 1-1 0 1 1 2 1 3 1 0 1 0 1-1h1l2 15c1 2 1 5 1 7v1h-1v1l-1-2-2-3s-1 0-1-1h-1c-1 0-2-1-3-2 0-1-2-2-3-3-2 0-8-1-10-1 0 1-1 1-2 1 0 0-1-1-1 0h-1v-1c3-3 4-6 4-11v-1l-2-2h-1v1s0 1-1 1h-1v1c0 1 0 2-1 2h0c0-2-1-3-2-5 2 0 2 0 4-2l-2-2c0-1 0-2 1-2z" class="B"></path><path d="M153 453c2-2 4-3 8-3 2 1 4 3 6 4h-1c-2-2-4-2-6-2h-1-1l-1 1h0c0 2 1 3 0 5h0v-1l-2-2h-1v1s0 1-1 1h-1v1c0 1 0 2-1 2h0c0-2-1-3-2-5 2 0 2 0 4-2z" class="C"></path><path d="M152 449c5-2 7-3 12-1 3 1 6 5 8 8 0-1 1-1 1-1 0 1 1 2 1 3 1 0 1 0 1-1h1l2 15c1 2 1 5 1 7v1h-1v1l-1-2-2-3s-1 0-1-1h-1l-1-2v-10c-1-3-3-7-5-9-2-1-4-3-6-4-4 0-6 1-8 3l-2-2c0-1 0-2 1-2z" class="q"></path><path d="M173 455c0 1 1 2 1 3 1 0 1 0 1-1h1l2 15c1 2 1 5 1 7v1h-1v1l-1-2h0c1-2 0-4-1-5-1-5-1-10-3-14 0-2-1-3-1-4s1-1 1-1z" class="F"></path><path d="M110 485c0 1 1 2 1 2 1 1 2 2 4 3 1 3 4 10 4 14l13 29c3 6 6 12 9 17 2 5 5 9 7 13-3-2-5-5-7-8 0-1-1-2-1-2 0-1-1-2-1-2l-5-7c0-1 0-2-1-3v-1l-2-2c-1 0-2-1-2-1-1-1-1-2-2-2l-1-2h-1c-1-1-3-3-4-5-2-3-5-5-8-7h-1v-2c1-1 2-3 3-3-1-1-3-1-4-1h-2c0-1 0-1-1-2l-1-1h-3-1-1l-2-2c-1-2 0-3 1-5v-1h0c3-2 6-2 10-3 1 0 1 0 3 1h0c1-1 0-2 0-3l1-1-1-1-5-5c-1-1-1-1-1-2h0l-2-1h0c1-1 1-2 2-2l2-2z" class="m"></path><path d="M115 498c1 2 2 4 2 6l-1 1c-2-1-4-2-5-4 1 0 1 0 3 1h0c1-1 0-2 0-3l1-1z" class="c"></path><path d="M120 516c2 4 4 9 6 13 1 2 3 4 4 6l1 3c-1 0-2-1-2-1-1-1-1-2-2-2l-1-2-6-14v-3z" class="D"></path><path d="M127 535h3l1 3c-1 0-2-1-2-1-1-1-1-2-2-2z" class="Q"></path><path d="M110 485c0 1 1 2 1 2 1 1 2 2 4 3 1 3 4 10 4 14-1-1-1-2-2-4-1-1-3-6-3-8l-3-1c-1 0-1 0-2 1-1-1-1-1-1-2h0l-2-1h0c1-1 1-2 2-2l2-2z" class="C"></path><path d="M110 485c0 1 1 2 1 2-1 1-1 1-3 0l2-2z" class="D"></path><path d="M108 490c2-1 2-1 4-1 1 1 1 2 2 3l-3-1c-1 0-1 0-2 1-1-1-1-1-1-2z" class="F"></path><path d="M104 512c1-2 3-3 5-4 2 0 4 0 6 1l3 3v2l2 2v3c-2-2-3-2-5-3-1-1-3-1-4-1h-2c0-1 0-1-1-2l-1-1h-3z" class="V"></path><path d="M109 511h2c0 2-2 2 0 4h-2c0-1 0-1-1-2l-1-1 2-1z" class="n"></path><path d="M104 512c1-2 3-3 5-4 2 0 4 0 6 1 0 2 0 2 1 4h-1s-1 0-1-1c-1 0-2-1-3-1h-2l-2 1h-3z" class="K"></path><path d="M109 511v-1h2c2 0 2 0 3 1v1c-1 0-2-1-3-1h-2z" class="J"></path><path d="M112 521v-2c1-1 2-3 3-3 2 1 3 1 5 3l6 14h-1c-1-1-3-3-4-5-2-3-5-5-8-7h-1z" class="R"></path><path d="M102 425v-1c1-1 2 1 4 1v-1c1 0 2 1 3 2h1c0 1 1 1 2 2-2 1-4 2-6 2-2 1-4 2-5 4l2-1c1-1 3-2 5-2h-2c-1 1-2 2-2 3h0c0 1 0 1-1 1 1 1 2 1 2 2l-2 2h1v2 1c1 2 2 3 3 4 1 0 1 0 2 1l1-1v1c0 1-1 2 0 3s1 1 2 1l1-1v2c-2 0-3 2-4 3l-1 2h-1c0-1 1-1 1-2h-1l-1 1c-1 2-1 2-1 4s1 4 3 6h1v-1h-1l1-1 2 2 4 1c1 1 3 1 5 0-1 1-2 2-2 3h0l-1 1-1-1c-1 0-3-2-4-2l-2 2-2-1-1 1h-1-1l-1-1h-2-3v1c1 1 2 1 3 2h1c1 1 1 1 2 1l1 1v1c-4-2-5-2-8-2-4 1-6 4-8 7v-1c0-2 2-4 4-6-3 1-5 1-7 2l-1 1h-1v-1c-1-1-4 0-5 1l-1-1c1-2 0-3 0-5l-2-6c0-1-1-2-1-3 0 0 1 0 0-1 0-2-2-6-1-8h1 1c1 0 2-2 2-4l-3-6c0-1-1-3 0-5 1 0 1 0 1 1l1-1c1-1 3-1 5-1h0c2-2 5-2 7-4v-1h-2-1l-1-1v-1c1 0 1-1 1-1 1-1 3 0 4 0h0c1 1 1 1 1 3h0 0c0-1 0-2 1-3 1 0 2-1 3-1l1 1c0-1 0-1 1-1h1v-1c1 1 2 1 3 0v-1z" class="F"></path><path d="M95 454h1l1 1-1 1h-1v-2z" class="H"></path><path d="M105 446c1 0 3 2 3 2v2h-1l-3-3 1-1z" class="U"></path><path d="M77 438l1-1c1-1 3-1 5-1h-3l1 1v2l-2-1-1 1h1c0 1 0 2-1 3v-1c-1-1-1-2-1-3z" class="H"></path><path d="M113 450v2c-2 0-3 2-4 3h-1v-2h-1 0v-1l4-1h1l1-1z" class="P"></path><path d="M100 438h-1c-1-1-2-2-2-3h0c0-1-1-1-1-1 0-1 0-2 1-3h1c0 1-1 1 0 2v1 1h1c0-1 1-2 2-3s3-2 5-2c-2 1-4 2-5 4-1 1-1 3-1 4z" class="C"></path><path d="M86 429c1 0 1-1 1-1 1-1 3 0 4 0h0c1 1 1 1 1 3h0c-2 2-4 3-6 5l-2 1c-1 0-1 0-1 1l-1 1h-1v-2l-1-1h3 0c2-2 5-2 7-4v-1h-2-1l-1-1v-1z" class="Q"></path><path d="M86 429c1 0 1-1 1-1 1-1 3 0 4 0h0-2 0c0 1 1 1 1 1-1 1-1 1-2 1l-1 1-1-1v-1z" class="H"></path><path d="M106 470c-3-2-5-4-5-7h-1v-5c0-1 2-3 3-4l1 1h0c0-1 1-1 1-1l1-1v2c-1 1-2 2-2 3h0l-1 1c0 2 0 3 1 5 1 1 3 3 4 3 1 1 2 1 2 0l1-1 4 1c1 1 3 1 5 0-1 1-2 2-2 3h0l-1 1-1-1c-1 0-3-2-4-2l-2 2-2-1-1 1h-1z" class="U"></path><path d="M101 434l2-1c1-1 3-2 5-2h-2c-1 1-2 2-2 3h0c0 1 0 1-1 1 1 1 2 1 2 2l-2 2h1v2c-1 1 0 2-1 3 0 0 0 2-1 3h0l1 1c2 2 3 2 3 5h-1l-1-1c-1-1-3-3-5-3-3-1-6 0-8 1-4 2-6 5-7 8s-1 6 0 9c0 1 1 2 2 3v1c-2 2-3 3-5 3-1-3-2-8-2-11v-1-1c1-4 4-10 8-13 4-2 8-3 12-2h1c-1-1 0-3-1-4v-2l1-2c0-1 0-3 1-4z" class="R"></path><path d="M103 435c1 1 2 1 2 2l-2 2c-1-2 0-2 0-4z" class="Q"></path><path d="M125 407c2-1 2-3 3-5l1 1h1v1 2h1c6-1 12 0 18 0v-1h1 2c1-1 2-1 3-1l4-1v1c0 1 1 3 0 4h-1c-3 0-2-2-5-1v3l1 1h2v1 1l-3 2c-3 1-7 2-11 4-1 0-2 0-2 1l-1 2h0c-2-1-3-4-5-5v1-1h-1 0v1h-1c0 1 1 2 2 3 1 0 1 2 2 3l3 3-1 1h0l1 1v1l-1 1 2 1-1 2h1 1l1 2c1 0 2 0 2 1h-1v1c1 1 2 1 4 1 0 0 0 1 1 2l1 1h1v1l-1 1c1 1 1 3 0 4h1c0 1 1 1 2 1-1 0-1 1-1 2l2 2c-2 2-2 2-4 2 1 2 2 3 2 5s1 4 0 5c-1 0-1 1-2 0-1 0-3-1-3-3 0-1 0-1-1-2h0c-1 0-2 1-4 1v-1l-3-1c-1 0-1 0-2 1v1 1l-1 1c-1 1-2 1-3 2v1 2l-1-1c-1 0-3 0-4 1v-1-5c0-1-1-3-1-4l-2-2c-1-2-3-2-4-2-2 0-4 3-5 4 1-1 1-2 0-3h0l-1-3h-1v-2l-1 1c-1 0-1 0-2-1s0-2 0-3v-1l-1 1c-1-1-1-1-2-1-1-1-2-2-3-4v-1-2h-1l2-2c0-1-1-1-2-2 1 0 1 0 1-1h0c0-1 1-2 2-3h2c-2 0-4 1-5 2l-2 1c1-2 3-3 5-4 2 0 4-1 6-2-1-1-2-1-2-2h-1c-1-1-2-2-3-2v1c-2 0-3-2-4-1v1-2h-1l1-3v-1c-1 0-1 0-2-1l-5-5h0c1 1 2 1 3 1 1 1 2 1 2 2 1 0 2 0 3 1h4 2c1 0 2 0 3-1 0 0 1-1 2-1h0c1 0 2 0 3-1l3-2c2-1 3-3 5-5z" class="R"></path><path d="M117 441l2 2h-1c-1 0-1 0-2-1h0l1-1z" class="B"></path><path d="M119 431c1 0 1 0 2 1h-1v1l-1 1c-1-1 0-2 0-3zm-6 6h0c1 0 1 0 2 1 2-1 2-1 3-2 0 1 0 2-1 3-2 0-3-1-5-2h1z" class="I"></path><path d="M128 442l1-1c1 1 1 1 1 2 1 1 1 2 2 2h1c0 1 0 2-1 3h-1v-2c-2-1-2-2-3-4z" class="F"></path><path d="M113 450c3-2 1-3 2-5 1-1 1-1 2-1 3 0 5 1 7 3h-2c-1-1-4 0-5 1h-1l-1 1v1c0 1-1 1-1 2h-1v-2z" class="G"></path><path d="M107 432s1-1 2-1c0-1 0-1 1-1h2v1c-2 3-8 7-8 11h0v-1-2h-1l2-2c0-1-1-1-2-2 1 0 1 0 1-1 1-1 2-1 3-2z" class="B"></path><path d="M107 432c0 1 0 2-1 3 0 1-1 1-1 2 0-1-1-1-2-2 1 0 1 0 1-1 1-1 2-1 3-2z" class="n"></path><path d="M109 426l1-1s1 0 1-1l2 2s0 1 1 1c0 2 1 2 1 4-1 1-3 2-3 4l1 2h-1l-1-1c1-1 1-3 1-4h0v-1-1h-2c-1 0-1 0-1 1-1 0-2 1-2 1-1 1-2 1-3 2h0c0-1 1-2 2-3h2c-2 0-4 1-5 2l-2 1c1-2 3-3 5-4 2 0 4-1 6-2-1-1-2-1-2-2h-1z" class="E"></path><path d="M134 456v1c1 1 1 2 1 3h1v1 1l-1 1c-1 1-2 1-3 2v1 2l-1-1c-1 0-3 0-4 1v-1-5c0-1-1-3-1-4s0-1 1-2l2 10c1-2 1-4 2-6v-2c1 0 1-1 1-1h0l2-1z" class="C"></path><path d="M133 461c0-1 1-2 1-4 1 1 1 2 1 3h1v1 1l-1 1c-1 1-2 1-3 2l1-4z" class="l"></path><path d="M133 461v1h0l2-1 1 1-1 1c-1 1-2 1-3 2l1-4z" class="G"></path><path d="M124 447c1 1 1 4 1 6h0l2 3c-1 1-1 1-1 2l-2-2c-1-2-3-2-4-2-2 0-4 3-5 4 1-1 1-2 0-3h0l-1-3c0-1 1-1 1-2v-1l1-1h1c1-1 4-2 5-1h2z" class="V"></path><path d="M115 450l2 1h2c0 2-2 2-3 3l-1 1-1-3c0-1 1-1 1-2z" class="O"></path><path d="M115 450v-1l1-1 1 1h0c1-1 3 0 5 1 1 0 2 1 3 3h0l-1-1h-1l-2-1h-2-2l-2-1z" class="L"></path><path d="M124 447c1 1 1 4 1 6-1-2-2-3-3-3-2-1-4-2-5-1h0l-1-1h1c1-1 4-2 5-1h2zm15-6l-4 1h0l1-1v-2h-1c1-1 1-2 2-3s1-2 2-2h1 1l1 2c1 0 2 0 2 1h-1v1c1 1 2 1 4 1 0 0 0 1 1 2l1 1h1v1l-1 1c1 1 1 3 0 4l-1-2c1-1 1-1 0-1v-2c0-1 0-1-1-2l-1-1h-1c-1-1-2-2-4-2h-1c1 1 1 1 1 2 1 1 2 7 1 9 0 1-1 1-1 1-2 1-5 3-5 4l-1 1v1h0-1l-2 1c2-4 3-6 6-9h1 1 0v1c1-1 1-1 1-2 0 0 0-1-1-1-1 1-2 1-4 1h-1 1c1-1 3-2 4-3v-2c0-1 0-1-1-1z" class="E"></path><path d="M139 441l-4 1h0l1-1v-2h-1c1-1 1-2 2-3s1-2 2-2h1 1l1 2c-2 0-3-1-5 1v2 1c1-1 1-1 1-2h1 1c0 1 0 1-1 2h0v1z" class="F"></path><path d="M135 456v-1l1-1c0-1 3-3 5-4 0 0 1 0 1-1 1-2 0-8-1-9 0-1 0-1-1-2h1c2 0 3 1 4 2h1l1 1c1 1 1 1 1 2v2c1 0 1 0 0 1l1 2h1c0 1 1 1 2 1-1 0-1 1-1 2l2 2c-2 2-2 2-4 2 1 2 2 3 2 5s1 4 0 5c-1 0-1 1-2 0-1 0-3-1-3-3 0-1 0-1-1-2h0c-1 0-2 1-4 1v-1l-3-1c-1 0-1 0-2 1h-1c0-1 0-2-1-3v-1h1 0z" class="R"></path><path d="M134 456h1 0l3 3c-1 0-1 0-2 1h-1c0-1 0-2-1-3v-1z" class="Z"></path><path d="M139 453h2c1 0 2 1 2 2s-1 2-2 3h-1c-1 0-1-1-2-2 0-1 1-2 1-3z" class="K"></path><path d="M148 446l1 2h1c0 1 1 1 2 1-1 0-1 1-1 2l2 2c-2 2-2 2-4 2-1-1-2-1-3-2 1-2 2-5 2-7z" class="J"></path><path d="M125 407c2-1 2-3 3-5l1 1h1v1 2h1c6-1 12 0 18 0v-1h1 2c1-1 2-1 3-1l4-1v1c0 1 1 3 0 4h-1c-3 0-2-2-5-1v3l1 1h2v1 1l-3 2c-3 1-7 2-11 4-1 0-2 0-2 1l-1 2h0c-2-1-3-4-5-5v1-1h-1 0v1h-1c0 1 1 2 2 3 1 0 1 2 2 3l3 3-1 1h0l1 1v1l-1 1h-2c-1 0-3 0-4-1-2 0-2 0-3 1 0 1 0 1-1 2h1c1 0 1 0 2 1v2 1c0 1 2 1 1 2v1 4 1c-1 0-1-1-2-2 0-1 0-1-1-2l-1 1v-1l-4-4 4 1 1-1c0-1 0-2-1-3s-2-3-3-3l-2-2c-2-2-4-1-6-1v1h-2v2c0-2-1-2-1-4-1 0-1-1-1-1l-2-2c0 1-1 1-1 1l-1 1c-1-1-2-2-3-2v1c-2 0-3-2-4-1v1-2h-1l1-3v-1c-1 0-1 0-2-1l-5-5h0c1 1 2 1 3 1 1 1 2 1 2 2 1 0 2 0 3 1h4 2c1 0 2 0 3-1 0 0 1-1 2-1h0c1 0 2 0 3-1l3-2c2-1 3-3 5-5z" class="F"></path><path d="M129 425h1v1c0 1 0 2-1 3 0-1 0-2-1-2v-1l1-1z" class="B"></path><path d="M132 418c0-1-1-2 0-3h2 0v2 1-1h-1 0v1h-1z" class="Q"></path><path d="M149 406v-1h1 2c1-1 2-1 3-1-2 1-3 3-4 4h-1l1-1v-1h-2z" class="U"></path><path d="M132 430s2-1 2-2c0 0 0-1 1-2h0v3 1c1 0 1 0 1 1-1 0-3 0-4-1z" class="I"></path><path d="M135 429c1-1 1-2 1-2l2 1 1 1v1l-1 1h-2c0-1 0-1-1-1v-1z" class="H"></path><path d="M128 441c1-1 1-1 1-2 1-1 1-1 2 0v4l1 1v1c-1 0-1-1-2-2 0-1 0-1-1-2l-1 1v-1z" class="B"></path><path d="M127 418l1-2-2-1v-1h-1l1-1c2 1 2 1 3 2l1 1c1 1 1 1 1 2-1 1 0 3-1 3h-1c-1 1-1 1-2 1h0c0-1 0-1 1-2 0-1 0-1-1-2z" class="n"></path><path d="M129 415l1 1v2l-1-1v-1-1z" class="Q"></path><path d="M125 407c2-1 2-3 3-5l1 1h1v1 2c0 1-1 1-1 2l-2 2c-3 0-3 1-5 3-1 1-3 1-5 1l3-2c2-1 3-3 5-5z" class="D"></path><path d="M151 408c0 2 0 2 1 3-1 0-17 0-20 2-1 0-1 1-1 1l-2-2c0-1 1-3 2-4h19 1z" class="q"></path><path d="M156 412v1l-3 2c-3 1-7 2-11 4-1 0-2 0-2 1l-1 2h0c-2-1-3-4-5-5v-2c2 1 5-1 7-1 5-1 10-1 15-2z" class="R"></path><path d="M95 413h0c1 1 2 1 3 1 1 1 2 1 2 2 1 0 2 0 3 1h4 2c1 0 2 0 3-1l1 1c2 0 4 1 6 2v1h1 0c0-1 1-2 2-3 2-1 4 0 5 1s1 1 1 2c-1 1-1 1-1 2h-1c-1-1-1-2-2-3-1 1-1 1-2 1v1h2v1h-2c-2 1-2 5-4 4v-1h-1v3h0v1h-2v2c0-2-1-2-1-4-1 0-1-1-1-1l-2-2c0 1-1 1-1 1l-1 1c-1-1-2-2-3-2v1c-2 0-3-2-4-1v1-2h-1l1-3v-1c-1 0-1 0-2-1l-5-5z" class="U"></path><path d="M102 420l1 1c0 1 1 2 1 3h2v1c-2 0-3-2-4-1v1-2h-1l1-3z" class="C"></path><path d="M119 420h1c-1 1-1 2-2 3h-2c-1 0-3-1-4-2l1-1h0 3l1 1c1 0 1 0 2-1z" class="D"></path><path d="M95 413h0c1 1 2 1 3 1 1 1 2 1 2 2 1 0 2 0 3 1h4 2c1 0 2 0 3-1l1 1c-1 1-2 2-2 3v2 1h0l-2-1v-1c1-1 1-2 1-3h0c-1 0-1 0-2 1l-1-1-2 1s-1 0-1-1h-4l-5-5z" class="I"></path><path d="M405 597c3-2 5-4 7-6s5-4 7-6c1-2 2-3 3-4v-1c0 1 0 3 1 3v1 1h0l1-1v2 1c-1 1-2 1-2 2 1 0 2-1 4-2l1 1c-1 1-6 6-6 7l-3 4h1c1 0 2-1 3-2 0-1 0-1 1-2 2 0 4-4 6-5h0c2 0 5-3 6-4-1 3-3 5-6 7 0 1 0 2-1 3-2 1-3 3-6 4v1h1c0 1-3 3-4 4-6 4-13 9-20 12l-1 1c-1 0-4 1-5 2l-5 3-8 3c-8 3-16 5-25 7-10 2-20 4-30 5-21 1-43 0-64-4-18-3-36-7-54-14-7-3-15-6-22-10-4-2-8-4-12-7l-1-1v-1c1 0 1-1 2-1v-1h-2c0-1 0-1 1-1v-1h-1v-1h-2-1 0l1-1c1 0 1 0 2-1l11 9c1 1 3 2 4 3l9 5 8 3c2 2 4 3 6 3 1 1 1 1 2 1 2 1 3 1 4 2 5 3 11 5 16 5 5 1 9 2 13 2h0c5 0 10 1 15 1 1 0 3 1 3 0v-1c-1-1-1-2-1-3l6 3 1-1 3-3v-1c1-2 2-3 3-4v1-1l1-1c1 1 3 1 4 1 2-1 5 0 6 0v1c1-1 1-2 2-2v3h4v-1l1-3h2 2v-2h1 0c0 2-1 4 0 6h0c1 1 1 1 3 0 0 1 0 2 1 4l2 1 2 1c1 1 1 1 2 1l1 1h1c1 1 1 1 2 0 2 1 4-1 6-1 1 0 2 0 3 1h1c1 0 1 2 2 1 1 0 2 1 2 1 1 0 1 0 2 1l-2-2h1 2c3 1 4 0 7-2 1-1 1-1 1-2 1-1 2 0 3 0l2-1h0c2-1 3-2 4-4v1h1l-1-1v-3h0l1-1c0 1 1 0 2 1h-2v2c0 1 1 1 1 1 1 1 2 1 2 0h0 2c2-1 4-1 6-2 1-1 3-1 5-1 8-1 15-6 21-10l19-15 1 1h1 0v1c3-1 5-4 7-6-1 3-5 6-7 9z" class="R"></path><path d="M356 627c3 0 5 0 8 1-3 1-6 1-10 2 0-1 1-2 1-3h1zm-160-16l8 3c2 2 4 3 6 3 1 1 1 1 2 1 2 1 3 1 4 2l-10-3c-2-1-4-2-7-3l-3-3z" class="C"></path><defs><linearGradient id="BQ" x1="277.973" y1="629.473" x2="281.499" y2="637.384" xlink:href="#B"><stop offset="0" stop-color="#b8bbba"></stop><stop offset="1" stop-color="#dcd9da"></stop></linearGradient></defs><path fill="url(#BQ)" d="M271 631c2 0 3 1 5 1 2 1 5 1 7 1h0l1 1c1 0 2 0 2-1l1 1h0l1 1h1l-1 1h-6c-1-1-3 0-4-1l-5-1-2-3z"></path><path d="M368 625c2 0 4-1 7-1 2-1 3-2 5-1l-10 3c-2 1-4 1-6 2-3-1-5-1-8-1h0-3 2l-1-1h5v-2h3c2 0 4-1 6 0v1z" class="K"></path><path d="M362 624c2 0 4-1 6 0v1c-3 0-6 1-9 1v-2h3zm-117 3c5 0 10 1 15 1 0 1 0 1 1 1 4 0 7 1 10 2l2 3-10-3c-3 0-6 0-8-1s-5-1-7-2l-3-1z" class="H"></path><path d="M172 594l11 9c1 1 3 2 4 3l9 5 3 3c-8-1-14-7-22-11h0c-1 0-1 0-1-1s0 0-1-1c0 1-1 1-2 2l-1-1v-1c1 0 1-1 2-1v-1h-2c0-1 0-1 1-1v-1h-1v-1h-2-1 0l1-1c1 0 1 0 2-1z" class="E"></path><path d="M342 624l2 2h2 4l3 1h3 0-1c0 1-1 2-1 3-2 1-6 0-8 1l-4 1h-4l-4 1 2-2h-3-5l-2-2h1 2c3 1 4 0 7-2 1-1 1-1 1-2 1-1 2 0 3 0l2-1h0z" class="D"></path><path d="M346 628h1c1 0 1 1 2 1v1h-2-2-1v-2h2 0z" class="C"></path><path d="M342 624l2 2h2 4-3l-1 2h0c-1 0-2 0-2-1-1 0-2 0-2 1l-1-1v-1l-1-1 2-1h0z" class="P"></path><path d="M342 624l2 2h-1l-1 1h-1v-1l-1-1 2-1h0z" class="K"></path><path d="M336 627c1-1 1-1 1-2 1-1 2 0 3 0l1 1c-1 1-2 1-3 1-2 1-3 3-5 4h-5l-2-2h1 2c3 1 4 0 7-2z" class="S"></path><path d="M336 631c1-1 3-3 5-3h0 2v3h3l-4 1h-4l-4 1 2-2z" class="F"></path><path d="M338 632h1c1-1 1-2 2-2s1 1 1 2h-4z" class="B"></path><defs><linearGradient id="BR" x1="392.215" y1="622.127" x2="374.86" y2="611.99" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#b9b9ba"></stop></linearGradient></defs><path fill="url(#BR)" d="M397 611l2-1 1 1c-1 1-3 2-4 3 1 0 2-1 3-1v1c-5 3-10 5-15 7-1 1-3 2-4 2-2-1-3 0-5 1-3 0-5 1-7 1v-1c-2-1-4 0-6 0l5-2c10-2 20-6 30-11z"></path><path d="M290 629h0v-1c1 0 2 0 3-1 1 1 2 1 3 1h1c2 0 3 1 5 2l-1-1c2 0 2 0 3 1 2 0 6 1 7 1 1-1 3-1 4-1 2-1 4-2 6-2h1c1 0 1 2 2 1 1 0 2 1 2 1 1 0 1 0 2 1h5 3l-2 2c-5 0-8 0-11-4h-1c-1 2-2 3-4 3s-4 0-6 1c-5 0-8-1-12-2v1l-3 3c-2-1-5-1-7-2v-1h-1l-2 2-1-1c0 1-1 1-2 1l-1-1c3-1 5-2 7-4z" class="C"></path><path d="M429 590h0c2 0 5-3 6-4-1 3-3 5-6 7 0 1 0 2-1 3-2 1-3 3-6 4v1l-23 13v-1c-1 0-2 1-3 1 1-1 3-2 4-3l-1-1-2 1c1-1 4-2 6-3 2-2 4-3 6-5s4-3 6-4c2-2 3-3 6-4l-3 4h1c1 0 2-1 3-2 0-1 0-1 1-2 2 0 4-4 6-5z" class="J"></path><path d="M422 600c1-1 1-2 2-3l2-2c1 0 2-1 3-2 0 1 0 2-1 3-2 1-3 3-6 4z" class="G"></path><path d="M418 599h1c-2 3-5 5-8 7l-12 7c-1 0-2 1-3 1 1-1 3-2 4-3 2-1 4-2 7-4 1-1 3-2 4-3l7-5z" class="V"></path><path d="M409 603c2-2 4-3 6-4 2-2 3-3 6-4l-3 4-7 5c-1 1-3 2-4 3-3 2-5 3-7 4l-1-1-2 1c1-1 4-2 6-3 2-2 4-3 6-5z" class="J"></path><path d="M297 614h1 0c0 2-1 4 0 6h0c1 1 1 1 3 0 0 1 0 2 1 4l2 1 2 1c1 1 1 1 2 1l1 1h1c1 1 1 1 2 0 2 1 4-1 6-1 1 0 2 0 3 1-2 0-4 1-6 2-1 0-3 0-4 1-1 0-5-1-7-1-1-1-1-1-3-1l1 1c-2-1-3-2-5-2h-1c-1 0-2 0-3-1-1 1-2 1-3 1v1h0c-2 2-4 3-7 4h0c-2 0-5 0-7-1-2 0-3-1-5-1-3-1-6-2-10-2-1 0-1 0-1-1 1 0 3 1 3 0v-1c-1-1-1-2-1-3l6 3 1-1 3-3v-1c1-2 2-3 3-4v1-1l1-1c1 1 3 1 4 1 2-1 5 0 6 0v1c1-1 1-2 2-2v3h4v-1l1-3h2 2v-2z" class="n"></path><path d="M294 618h1c1 1 1 1 1 2-1 0-1 0-2-1v-1z" class="Q"></path><path d="M286 620l1 1v2h-1v-3z" class="C"></path><path d="M295 622l1 1-1 1h-1l-1-1c1 0 2 0 2-1z" class="H"></path><path d="M298 620c1 1 1 1 3 0 0 1 0 2 1 4h-2l-1-1c-1-1-1-2-1-3z" class="I"></path><path d="M288 620h4c-1 1-1 3-3 3-1-1-1-2-1-3z" class="E"></path><path d="M309 628h-3c-2 1-3 0-5 0-1-1-3-1-4-3 2-1 4 1 5 1s2 0 2-1l2 1c1 1 1 1 2 1l1 1zm-37-5c1 0 1 0 2 1 0 1 1 2 2 2 1-1 2-1 3-3h0v2l-2 2c-1 0-1 0-1 1h-1c-2 0-2-1-4 0h0l-1-1-1-1 3-3z" class="V"></path><path d="M272 623c1 0 1 0 2 1-2 1-2 2-4 3l-1-1 3-3z" class="L"></path><path d="M262 624l6 3 1-1 1 1 1 1h0c2-1 2 0 4 0 1 1 2 1 3 1 3 0 8-1 9-2s0-1 1-1l1 1 1-1v1c0 1-1 1-1 2h1c-2 2-4 3-7 4h0c-2 0-5 0-7-1-2 0-3-1-5-1-3-1-6-2-10-2-1 0-1 0-1-1 1 0 3 1 3 0v-1c-1-1-1-2-1-3z" class="C"></path><path d="M262 624l6 3 1-1 1 1 1 1c3 3 6 4 10 4 3 0 6-1 8-3h1c-2 2-4 3-7 4h0c-2 0-5 0-7-1-2 0-3-1-5-1-3-1-6-2-10-2-1 0-1 0-1-1 1 0 3 1 3 0v-1c-1-1-1-2-1-3z" class="S"></path><defs><linearGradient id="BS" x1="405.838" y1="573.12" x2="369.022" y2="632.441" xlink:href="#B"><stop offset="0" stop-color="#bebebf"></stop><stop offset="1" stop-color="#edecec"></stop></linearGradient></defs><path fill="url(#BS)" d="M405 597c3-2 5-4 7-6s5-4 7-6c1-2 2-3 3-4v-1c0 1 0 3 1 3v1 1h0l1-1v2 1c-1 1-2 1-2 2 1 0 2-1 4-2l1 1c-1 1-6 6-6 7-3 1-4 2-6 4-2 1-4 2-6 4s-4 3-6 5c-2 1-5 2-6 3-10 5-20 9-30 11l-5 2h-3v2h-5l1 1h-2l-3-1h-4-2l-2-2c2-1 3-2 4-4v1h1l-1-1v-3h0l1-1c0 1 1 0 2 1h-2v2c0 1 1 1 1 1 1 1 2 1 2 0h0 2c2-1 4-1 6-2 1-1 3-1 5-1 8-1 15-6 21-10l19-15 1 1h1 0v1c3-1 5-4 7-6-1 3-5 6-7 9z"></path><path d="M399 602l2-2v1 1h0c-1 0-1 0-1 1h-1v-1h0z" class="I"></path><path d="M356 622l1 3-5-1h0-2v-1l6-1z" class="M"></path><path d="M358 622c0-1 4-1 5-1 2 1 2 1 4 1l-5 2h-3c-1 1-1 1-2 1h0l-1-3h2z" class="l"></path><path d="M356 622h2v1h0c0 1 0 1-1 2h0l-1-3z" class="N"></path><path d="M363 617c8-1 15-6 21-10l1 1h2c-4 3-10 5-14 7-4 1-6 2-10 2h0z" class="D"></path><path d="M346 620v1 1h1c1 0 1 0 2 1h1v1h2 0l5 1h0c1 0 1 0 2-1v2h-5l1 1h-2l-3-1h-4-2l-2-2c2-1 3-2 4-4z" class="J"></path><path d="M354 626c-1 0-2-1-3-1h-4c1-1 3-1 5-1h0l5 1h0c1 0 1 0 2-1v2h-5z" class="D"></path><defs><linearGradient id="BT" x1="419.09" y1="586.212" x2="415.983" y2="602.754" xlink:href="#B"><stop offset="0" stop-color="#454143"></stop><stop offset="1" stop-color="#5a5c5c"></stop></linearGradient></defs><path fill="url(#BT)" d="M422 589c1 0 2-1 4-2l1 1c-1 1-6 6-6 7-3 1-4 2-6 4-2 1-4 2-6 4v-1-3l13-10z"></path><defs><linearGradient id="BU" x1="405.825" y1="596.198" x2="384.454" y2="603.251" xlink:href="#B"><stop offset="0" stop-color="#585a59"></stop><stop offset="1" stop-color="#a19c9e"></stop></linearGradient></defs><path fill="url(#BU)" d="M412 588c-1 3-5 6-7 9l-4 3-2 2c-3 1-5 2-8 4l-4 2h-2l-1-1 19-15 1 1h1 0v1c3-1 5-4 7-6z"></path><path d="M412 588c-1 3-5 6-7 9l-4 3-2 2c-3 1-5 2-8 4 1-2 2-3 3-3 4-3 8-6 11-9 3-1 5-4 7-6z" class="K"></path><path d="M363 621c11-3 23-7 33-13 5-2 9-5 13-9v3 1c-2 2-4 3-6 5-2 1-5 2-6 3-10 5-20 9-30 11-2 0-2 0-4-1z" class="M"></path></svg>