<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="65 40 526 632"><!--oldViewBox="0 0 636 752"--><style>.B{fill:#212021}.C{fill:#2f2f2f}.D{fill:#3d3c3d}.E{fill:#fbfbfb}.F{fill:#1d1d1e}.G{fill:#ececec}.H{fill:#383838}.I{fill:#626161}.J{fill:#dfdfde}.K{fill:#454445}.L{fill:#787778}.M{fill:#565557}.N{fill:#9f9e9e}.O{fill:#bbb9ba}.P{fill:#aaa8a9}.Q{fill:#b4b2b3}.R{fill:#918f90}.S{fill:#686968}.T{fill:#ebe9ea}</style><path d="M150 632l1-1v1c1 1 1 2 2 3s1 2 1 3v1c-2-2-3-5-4-7z" class="H"></path><path d="M132 629h1c0 2 1 4 2 5l-1 2h0c-1-2-2-4-2-6v-1z" class="M"></path><path d="M141 632c-1-3-1-7-1-9 1 1 1 3 2 5s2 4 3 7c-1-1-1-1-2-1 0-1 0-2-1-3l-1 1z" class="H"></path><defs><linearGradient id="A" x1="143.067" y1="634.04" x2="145.266" y2="639.335" xlink:href="#B"><stop offset="0" stop-color="#515252"></stop><stop offset="1" stop-color="#696a6b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M141 632l1-1c1 1 1 2 1 3 1 0 1 0 2 1l2 5-1 1s0 1-1 1c-1-3-2-7-4-10z"></path><path d="M182 639c-1-2-2-4-1-5 1 1 1 2 2 3v-1-1l1-2c0 4 2 6 3 9h0c-2-1-3-1-4-3v-1l-1 1z" class="K"></path><path d="M120 557h4c3 0 5 1 8 3h-1-1-1l1 1c-3 0-8-2-10-4z" class="D"></path><path d="M179 667l6 2c2 2 5 2 8 3 1 1 2 1 4 1h1c2 0 4 0 6 1h5-4c-1 0-1 1-2 1-5 0-9-1-14-3h-1c-1 0-2-1-4-2s-4-1-5-2v-1z" class="F"></path><path d="M112 561c3-2 8-1 11-1 1 1 3 1 4 2-2 0-4-1-5-1 1 1 4 2 6 3-2 0-6-1-7 0-2 0-4-1-6-1s-4 0-6 1l2-2c0-1 0-1 1-1z" class="I"></path><path d="M112 561c3-2 8-1 11-1-1 1-5 1-7 1h-1-3z" class="L"></path><path d="M182 639l1-1v1c1 2 2 2 4 3h0c0 1 1 3 1 3 2 4 6 8 9 10 1 1 3 2 4 3h-1c-4-2-6-4-9-7h-1v-1l-1 2c-1-1-2-2-2-3-2-4-4-7-5-10z" class="M"></path><path d="M163 634v-1c-1-2-2-6-1-7 1 4 3 8 5 12 1 4 2 7 4 10s4 5 5 7h0c-6-6-10-12-13-21z" class="C"></path><path d="M183 635c-1-1-1-2-1-4 0-3 1-8 2-12 1-1 1-3 2-5 1-1 1-2 2-3 0-1 2-2 3-2v-1c1 0 1 0 2-1h2 0c-1 1-3 2-4 3l-1 1c-1 0-2 1-2 2l-1 1v1c-1 2-2 3-2 5-1 2-1 6 0 8 0 1 0 4 1 6l1 1v2l2 3v1 1c-1-1-2-2-2-3h0c-1-1-1-2-1-2v-1l-1-1-1-6h-1c0 1 1 3 1 4h0l-1 2z" class="F"></path><defs><linearGradient id="C" x1="145.998" y1="640.818" x2="148.949" y2="646.686" xlink:href="#B"><stop offset="0" stop-color="#777779"></stop><stop offset="1" stop-color="#8d8e90"></stop></linearGradient></defs><path fill="url(#C)" d="M145 642c1 0 1-1 1-1l1-1c5 10 10 18 19 25 3 2 6 3 9 5-2 0-4-1-5-2-6-4-11-8-16-13-3-4-7-9-9-13z"></path><path d="M69 298c0-4 1-8 2-12 2-14 4-28 8-42 1-5 2-10 4-14 0 2 0 3-1 5l-3 12c-1 3-1 5-2 8v2c-1 0-1 1-1 2s0 2-1 3h0c1 2 0 3 0 4h0v1l-1 1v1 2c0 1 0 1-1 1v1h0v2h0v2h-1c1 1 0 1 1 1h0 0 1v1c-1 0-1 0-1 1l-1 1h0v1c0 1 0 2-1 3v2h0v3s0 1-1 1v3 1h0v2c-1 1 0 3-1 4v-1-2z" class="G"></path><path d="M129 624h0 0c1 1 1 2 2 3l1 2v1h-1v-1h0l-1 2c0 1 1 3 1 4 3 10 8 20 15 28l9 9v1l-1-1c-3-2-5-4-7-7-4-4-7-8-10-13-5-9-9-17-8-28z" class="B"></path><path d="M150 632v-1c-3-7-2-15 1-22 0-2 1-4 2-6 1-1 1-2 1-2l1-1 1-1s0-1 1-1h0v1l-3 3-1 4c-1 1-1 2-1 3 0 0-1 1-1 2v1c-1 4 0 10 1 14 0 2 1 3 1 5h0v4c-1-1-1-2-2-3v-1l-1 1z" class="C"></path><path d="M179 616v2 2c-2 4-2 12 0 16l1 1v1s1 0 1 1v1c1 1 1 3 2 4 0 0 1 1 1 2 1 0 1 0 1 1h1c0 1 0 1-1 2-3-4-5-9-7-14h-1c1 2 1 4 2 6 1 1 1 3 2 4 1 2 2 3 3 4v1c1 0 0 0 1 1l1 1v1l-2-3c-1 0 0 0-1-1 0 0-1 0-1-1l-2-4c-1-1-2-3-3-5s-2-7-2-9c1-5 2-10 4-14z" class="B"></path><path d="M191 670c1-1 2 0 3 0 2 1 3 1 5 2 8 2 18 1 26-1 17-5 33-13 42-28 2-2 3-5 4-7 0-1 1-3 1-4 0 2 0 4-1 5-3 7-7 12-12 17l-7 6c-7 4-13 7-21 9-7 3-14 5-22 5h-5c-2-1-4-1-6-1l-9-3h2z" class="C"></path><defs><linearGradient id="D" x1="137.714" y1="637.022" x2="140.574" y2="652.253" xlink:href="#B"><stop offset="0" stop-color="#616262"></stop><stop offset="1" stop-color="#949495"></stop></linearGradient></defs><path fill="url(#D)" d="M134 636l1-2c3 7 6 12 10 18 8 12 20 19 33 24h1c1 0 1 0 2 1h-3l-1-1c-3-1-6-2-8-3-13-6-24-16-31-28-1-2-3-5-4-7v-2h0z"></path><path d="M134 636h0l1 1h0l-1 1v-2z" class="I"></path><defs><linearGradient id="E" x1="127.127" y1="617.61" x2="132.61" y2="615.739" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#3c3d3d"></stop></linearGradient></defs><path fill="url(#E)" d="M129 624v-8c0-10 4-23 12-30 2-2 8-6 12-6h1c-1 1-1 1-2 1-8 3-14 10-18 18-3 7-4 14-3 21 0 3 1 6 2 9h-1l-1-2c-1-1-1-2-2-3h0 0z"></path><path d="M189 641c2 4 5 7 8 9 7 6 16 7 25 6 7 0 15-4 20-9 4-2 6-6 9-9h0c-2 7-10 13-15 16-9 4-19 6-29 3-8-3-14-8-18-15v-1z" class="E"></path><path d="M171 583h0c-1 0-2 0-3-1h0l-1-1h-1 1c0 1 0 1 1 2h0v1h0c-3-1-6-3-7-5-2-2-7-5-9-6h-1 0-4s-1 0-2 1h-1-2c-1 1-3 1-4 2l-3 1-2 2c-1 0-2 1-3 2h-1l-1 1s0 1-1 1l-1 1h0l-1 1-1 1-2 2-1 2c-2 3-3 6-4 9l-1 3c-1 2-1 4-2 7 0 7 0 13 2 20 0 0 1 1 1 2s0 3 1 4l1 3 1 3c1 1 2 3 3 5h0v1h0l-2-2s0-1-1-2v-1l-2-4c0-1-1-1-1-1l-1-3v-2c-4-12-4-25 1-36 0-2 1-4 2-5 1-2 2-3 2-4h1v-1l2-1v-1c1-1 2-3 3-3 1-1 1 0 1-1 1 0 1-1 2-1l3-3c1 0 1 0 2-1h2v-1h1l2-1h1 1c0-1 1-1 1-1h1 8c1 0 1 0 2 1 1 0 2 1 3 2h0 1l1-1 12 9z" class="B"></path><path d="M153 635v-4h0c3 6 6 12 10 18 2 2 4 5 6 7 6 6 14 10 22 14h-2l9 3h-1c-2 0-3 0-4-1-3-1-6-1-8-3l-6-2v1c-11-7-20-17-25-29v-1c0-1 0-2-1-3z" class="D"></path><path d="M179 667c-3-3-13-10-14-13h0c7 7 15 12 24 16l9 3h-1c-2 0-3 0-4-1-3-1-6-1-8-3l-6-2z" class="B"></path><path d="M127 562c1 0 2 0 3 1h-1v1c1 0 1 1 2 1h0-2 0-3 0c0 1 1 1 1 2 1 0 1 0 1 1-1 1-3 0-4 1h-3c-1-1-2-1-3-1v2h1 0c-1 1-1 1-2 1v1h0-1v1h-1-1l-3 2v1l2 1c-1 1-1 2-1 2h-1c-2 2-4 3-5 5-3 3-5 7-6 11 0 1 0 0-1 1v-1c1-4 3-9 6-12 1-1 6-5 6-6-1 0-1-1-2 0-2 1-4 2-5 3-2 1-3 3-4 4-1 3-2 5-3 8h-1v-1c2-3 2-6 4-8 0-1 0 0 1-1 1-3 4-5 8-6l6-4c-3 1-6 1-10 3h-3v-1c5-1 10-3 15-4h0c-3 0-7 0-10 1h0c-2 0-4 1-5 2h0c0-1 0-1 1-2s4-1 4-2h-2c-1 1-2 1-3 1 2-1 4-4 6-4 1-1 2-1 3-1 2-1 5 0 7 0 1 0 2 0 3-1h0c1-1 5 0 7 0-2-1-5-2-6-3 1 0 3 1 5 1z" class="K"></path><path d="M108 566c1-1 2-1 3-1 2-1 5 0 7 0s4 0 5 1v1h-1-1c-1 1-3 1-4 1h-8 0c1-1 3-1 3-2-1 0-2 1-4 0z" class="M"></path><path d="M163 634v2h0v2h0c1 0 1 1 1 1h0v1l-1-1c0-1 0-2-1-3 0 3 2 5 3 7 1 3 3 6 5 9v1l-3-4c-5-6-8-16-9-24-1-2-1-4-1-7 0-7 2-15 8-21 2-2 5-5 10-5h3c1-1 1-1 2-1s1 0 2 1h-1c-1 1-2 1-3 1 0 0-1 0-2 1-1 0-1 0-2 1l-1 1c-1 0-1 1-2 1h0c-1 0-1 1-2 2-2 2-4 5-6 8 0 1 0 2-1 3v1 2c-1 5 0 8 0 12v1c-1 1 0 5 1 7v1z" class="B"></path><path d="M83 230c0-1 1-3 1-4l6-15c11-29 29-57 52-79 18-17 39-30 61-40 36-16 74-22 112-22 34 0 67 6 98 18 38 15 70 39 93 72 4 7 9 14 12 21 4 6 6 13 9 19 6 16 11 34 13 51 5 27 5 53 5 80 0 37 0 74-8 110-7 36-22 73-46 101-10 12-21 22-34 30l1 2 7 8 6 6 6 5c12 9 26 14 40 11 17-2 30-14 38-29 1-2 2-5 3-7 0-2 1-3 2-5l20 7c2 1 5 1 6 2v1c-2 8-5 16-9 24-9 21-24 39-44 50-23 12-49 14-74 6-18-5-34-15-47-28-4-4-7-8-10-11l-4-5-1-1s-8 3-9 3c-12 4-24 6-36 8-20 3-40 3-60 1-12 0-24-2-35-4-22-4-45-11-65-21l-14-8c-2-1-4-3-7-4l-12-9c-25-19-46-43-61-72-28-52-34-111-32-169l2-26c0-3 0-6 1-9v2 1c1-1 0-3 1-4v-2h0v-1-3c1 0 1-1 1-1v-3h0v-2c1-1 1-2 1-3v-1h0l1-1c0-1 0-1 1-1v-1h-1 0 0c-1 0 0 0-1-1h1v-2h0v-2h0v-1c1 0 1 0 1-1v-2-1l1-1v-1h0c0-1 1-2 0-4h0c1-1 1-2 1-3s0-2 1-2v-2c1-3 1-5 2-8l3-12c1-2 1-3 1-5z" class="J"></path><path d="M84 266h0v-1-2c1 0 0 0 1-1h0v-1-1-1l1-1v-2-1-1l1-1 1-4v-1c0-1 1-4 1-5h1v2c-1 0-1 1-1 1v2s0 1-1 1v1l-1 4v1c-1 2-1 5-2 7v1 2c0 1-1 1-1 2v2 1c-1 1-1 2-1 3v1h0v2c-1 1-1 2-1 3v1c-1 2 0 4-1 5v2c-1 4-1 8-2 11v1c0-1 0-4 1-5 0-1-1-2 0-3l2-11v-2-2-1-1-1h0c1-1 1-2 1-3h-1l1-1c0-1 0-2 1-3h0 0zm10 205c-1-5-9-28-9-32l1 1v2c0 1 1 2 1 2v-1-2l9 29h-1l-1 1zm421 5l5-12c4-15 7-31 9-46 0 2-1 4-1 6 0 1 0 3-1 5 1 0 1-1 1-1 0-1 1-2 1-3 0-2 0-3 1-5v-2-2h0v4c0 1 0 2-1 3v2 3l-1 3v4l-1 5-1 4c-1 2-1 5-2 8l-2 8v2c-1 1-1 2-1 3l-1 2v1l-3 9h0c-1-1-1-1-2-1z" class="G"></path><path d="M442 445c0-1 1-2 1-2v4c0 9-1 18-2 27-1 13-3 25-6 37-1 3-1 6-2 9h-2c1-2 1-4 2-6 2-7 4-15 5-22l4-47z" class="Q"></path><path d="M235 566c-11-11-21-23-28-36-9-17-15-34-20-52-2-9-5-18-5-27h0c2 10 4 19 7 29 3 11 6 22 11 33l5 10c5 8 9 16 14 23l8 10 6 6c1 1 2 2 2 3v1z" class="O"></path><path d="M333 82c10 1 20 1 30 3 34 6 66 19 94 39 5 4 11 9 16 14h-1c-1 0-2-2-3-3-2-1-3-3-5-4-24-20-53-33-83-41-17-4-34-6-52-7 1-1 2-1 4-1z" class="N"></path><path d="M442 445l1-51v-75l-1-56v-7-5c-1-1 0-2 0-3v1l1 13 1 18v109 36c0 7 0 15-1 22v-4s-1 1-1 2z" class="O"></path><path d="M515 476c1 0 1 0 2 1h0c-4 8-7 17-11 25-8 14-16 28-27 40-11 11-24 21-36 31-1 0-1 0-2-1l11-9c10-8 20-16 28-26 16-17 28-39 35-61z" class="E"></path><path d="M452 586c7 10 18 19 29 24 14 7 31 9 46 4 19-6 31-20 39-37 1 1 3 2 4 2v1h0l-1-1h-1 0 0v1h0c1 0 1 0 2 1-1 0-2-1-3 0h0c-2 1-3 4-4 6s-2 4-4 6l-4 5c-1 1-2 2-3 4-4 3-7 6-12 9-2 1-4 2-7 3v1h-2c-3 1-7 2-10 3h-1c-1 0-1 0-2 1h-4c-3 0-6 1-9 0h-4c-2-1-5-1-7-2l-6-2c-2 0-4-2-7-3-7-4-13-8-20-14l-9-9v-1l-1-1h0l1-1z" class="Q"></path><path d="M98 217c2-4 4-9 6-13 7-15 16-29 25-42l11-13c4-5 9-9 13-13 49-43 116-59 180-54-2 0-3 0-4 1-18-1-36-1-54 2-18 2-37 6-54 12l-17 7c-3 1-5 3-7 4-3 1-6 3-8 4-17 9-31 20-45 34l-8 8c-2 3-4 6-7 9-6 9-13 19-18 29-1 2-2 3-3 5l-3 8c-1 1-1 2-2 3l-4 9h-1zm333 303h2l-9 20c-1 4-3 7-5 10-3 3-5 7-8 10-9 11-22 21-35 26l-6 2v1c-14 6-29 7-44 9-8 1-16 0-24-1s-15-2-23-5a84.69 84.69 0 0 1-30-15c-5-3-9-7-14-11h0v-1l15 12c24 16 51 20 79 19 7-1 16-2 23-4l13-3c1-1 3-2 4-2l14-6c24-13 41-35 48-61z" class="P"></path><path d="M304 97c6 0 13 0 20 1h2c1 1 2 0 2 1h1 0c1 0 2 0 3 1h5 0 1c1 1 1 0 2 1h3c0 1 0 1 1 1h2l3 1h1 1l3 1h1c0 1 1 0 1 0l1 1h1l5 2c1 1 3 1 4 2 1 0 1 1 2 1s2 1 3 2c1 0 1 0 2 1 2 1 5 2 7 4l3 3c2 1 4 2 6 4h1c0 1 0 1 1 1v1c1 0 1 0 1 1 1 0 1 0 1 1l2 2 1 1v1h1 0l2 2h0-1c1 2 3 3 4 5l2 2s1 1 1 2c1 0 1 1 2 1h1v1l1 1h-1l1 2h1 0c1 0 1 0 1 1 1 0 1 1 2 2 1 0 1 1 1 1v1c1 0 1 0 1 1s1 1 1 2h0l1 1v2h0v1s2 4 2 5c-2-5-5-10-9-15-1-2-3-5-5-7-1-2-3-4-5-6-2-3-6-6-8-8-1-1-2-1-2-2l-7-6c-9-6-20-12-30-15-5-2-10-3-15-4-2-1-4-1-6-1-4-1-9-1-13-1-6-1-12-1-18 0-3 0-6 0-8 1-3 1-7 1-11 2-10 2-19 6-27 10-5 2-10 5-14 8-3 2-5 4-7 5l-6 6c-3 2-6 5-8 7-2 3-4 5-6 8l-2 3c-3 3-6 7-8 11-3 5-6 10-8 15l-1 4-1 1-3 8-1 1h0v1 2c-1 0-1 0-1 1v1c-1 0-1 0-1 1h0c-1 2-1 5-2 7l-1 4-1 5v1l-1 1v1 2l-1 1v3 1c-1 1-1 1-1 2h0v-3c1-1 1-3 1-4v-1l1-2v-1c0-1 0-1 1-2v-2c0-1 0-2 1-3 0-1 0-2 1-3h0c0-2 1-4 1-5s1-2 1-3v-1c1-1 1 0 1-1l1-3 3-9c1-1 1-2 2-3l3-6-1-1c1 0 1 0 1-1 1-1 3-4 4-6 0 0 0-1 1-2h0v-1l1-1v-1l1-1c0-2 2-2 2-4h0c1-1 2-3 3-3v-1c1 0 1-1 2-2h0l3-4 1-1h0c0-1 1-1 2-2h-1l1-1v-1c1 0 1-1 2-2h0c1-2 3-4 5-5 0-1 1-2 1-2 1 0 1 0 1-1 2-1 3-2 4-3l1-1 1-1v1-1h1l1-1c2-1 2-2 4-3l4-3 2-1c1 0 2-1 3-2 1 0 3-2 4-2 1-1 1-1 2-1h0c1-1 2-1 2-1 1 0 1 0 2-1h1l1-1h1c1-1 2-1 3-2h1 1l6-2 3-1c1 0 1 0 1-1h4c2-1 4-2 7-2h3c1-1 3-1 4-1h2c1-1 1-1 2-1v1c1 0 1 0 1-1 2 0 2 0 4-1z" class="G"></path><path d="M94 471l1-1h1c19 51 56 95 106 118 17 7 35 13 53 17 14 3 28 4 43 5 32 1 66-1 96-13l7-2c7 9 14 18 22 25 15 14 34 23 54 26 22 3 44-3 62-16 12-10 21-22 28-36 2-5 3-10 6-14 0 0-2 0-3-1-1 0-3-1-4-2-8 17-20 31-39 37-15 5-32 3-46-4-11-5-22-14-29-24 0 0-9-10-9-12h1c1-1 3 0 4 0 4 0 7 0 9-2l1 2 7 8 6 6 6 5c12 9 26 14 40 11 17-2 30-14 38-29 1-2 2-5 3-7 0-2 1-3 2-5l20 7c2 1 5 1 6 2v1c-3 1-6 3-8 5-1 1-3 1-3 2-1 1-2 3-2 4l-3 7c-7 16-16 30-30 40-17 13-39 20-60 17-27-3-50-16-68-37-4-5-7-10-11-15l-4 2c-12 5-25 8-38 10-20 4-41 4-61 3-30-1-60-6-88-18-43-18-79-50-102-91-5-10-9-21-14-31z" class="E"></path><path d="M448 574c4 0 7 0 9-2l1 2 7 8 6 6-3-1c-1-1-4-2-6-3-1 0-2-1-3-1-1-1-1 0-3-2h0-1v-1h0-2-1c-1 0-1 0-1-1-3-1-4-3-6-5h-1c1-1 3 0 4 0z" class="J"></path><path d="M448 574c4 0 7 0 9-2l1 2 7 8c-4-1-7-3-10-4-3-2-7-3-10-4h-1c1-1 3 0 4 0z" class="R"></path><path d="M448 574c4 0 7 0 9-2l1 2v1h-5 1l-1-1h-4-1z" class="N"></path><path d="M426 492h0 0 1c0 1 0 1-1 2v1 1h0v1-1c1 0 2 0 2-1h1c1 1 3 0 4 0s2-1 3-1c0-1 0-2 1-3v-1h0c1 0 1 1 1 2-1 7-3 15-5 22-1 2-1 4-2 6-7 26-24 48-48 61l-14 6c-1 0-3 1-4 2l-13 3c-7 2-16 3-23 4-28 1-55-3-79-19l-15-12c0-1-1-2-2-3l-6-6-8-10c-5-7-9-15-14-23l-5-10c1-1 1-2 1-2 1 0 1 0 2 1h0c1 1 1 1 2 1 0 1 1 1 2 1l2 2h1l2 2h0c1 1 2 1 2 2 1 0 2 0 2-1h1c11 20 27 41 48 52 0 0 1 1 2 1l14 6c2 1 5 2 8 3 1 1 2 1 4 2l3-1c4 1 8 1 12 2 3 0 6 0 8 1l7-1 12-2 1 1c2 0 5-2 6-2l14-3 13-4c7-3 13-6 19-10 24-17 32-45 38-72z" class="E"></path><path d="M355 581c-3 2-6 4-9 5l-1-1h-2l8-3 4-1z" class="G"></path><path d="M356 578l13-4-1 1c-1 1-2 1-3 1l-7 3c-1 1-2 1-3 2l-4 1c1-2 3-3 5-4z" class="Q"></path><defs><linearGradient id="F" x1="325.317" y1="588.966" x2="321.57" y2="575.064" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#a8a7a8"></stop></linearGradient></defs><path fill="url(#F)" d="M323 584l12-2 1 1c2 0 5-2 6-2l14-3c-2 1-4 2-5 4l-8 3c-16 6-34 3-50-2l3-1c4 1 8 1 12 2 3 0 6 0 8 1l7-1z"></path><path d="M316 585l7-1c-1 1-1 2-3 2-1 0-3-1-4-1z" class="R"></path><path d="M83 230c0-1 1-3 1-4l6-15c11-29 29-57 52-79 18-17 39-30 61-40 36-16 74-22 112-22 34 0 67 6 98 18 38 15 70 39 93 72 4 7 9 14 12 21 0 1 0 0 0 0l-1-1v-1l-1 1-2 1v1l-1 1c0 1 0 1-1 2h0c0 1 0 1-1 2h-1-1c-1-4-3-7-5-10l-1-1c0-1 0-1-1-2 0 0 0-1-1-1-2-5-6-10-10-15l-2-2c0-1-2-2-2-3-1-1 0-1-1-1l-10-11c-1-1-2-3-3-3-5-5-11-10-16-14-28-20-60-33-94-39-10-2-20-2-30-3-64-5-131 11-180 54-4 4-9 8-13 13l-11 13c-9 13-18 27-25 42-2 4-4 9-6 13v2c-1 1-1 1-1 2l-4 10c-1 3-1 6-2 9l-1 3h-1c0 1-1 4-1 5v1l-1 4-1 1v1 1 2l-1 1v1 1 1h0c-1 1 0 1-1 1v2 1h0c-1 0-1-1-2-2v-1s-1-1-1-2c0-2-1-1-3-2l1-1c-2-1 0-2-1-3h-1c1-3 1-5 2-8l3-12c1-2 1-3 1-5z" class="E"></path><path fill="#131313" d="M190 384l-2 1c-3-4-1-88-1-98l3-34c5-44 17-90 53-118 23-19 54-27 83-24 28 3 53 16 70 38 16 20 24 45 29 70 5 29 6 59 6 89v57l-1 82-2 28-2 17c-6 27-14 55-38 72-6 4-12 7-19 10l-13 4-14 3c-1 0-4 2-6 2l-1-1-12 2-7 1c-2-1-5-1-8-1-4-1-8-1-12-2l-3 1c-2-1-3-1-4-2-3-1-6-2-8-3l-14-6c-1 0-2-1-2-1-21-11-37-32-48-52-4-9-8-17-11-26-6-18-10-37-13-55l-1-9c-1-2-1-4 0-6-1 0-1-1-1-2l-1-7s-1-1 0-2c0-1 3-4 4-6 9-13 23-22 39-25l-1-1c-3 1-7 2-11 4-14 5-23 15-31 27-1-3-1-7-1-10 4-4 7-8 12-11 12-9 28-14 42-14 9-1 17 0 24 3 3 0 5 1 7 2 3 1 8 3 11 5v-1h0l-6-3c-3-2-7-3-10-4-18-6-37-5-54 2-2 1-3 2-5 3-8 5-15 10-22 17l1-4c-1-1-1-2-1-3s0-1 1-2l-1-1v-1c0-2 0-2 1-3 5-4 12-8 18-10 1-1 3-1 4-1 2-1 3-2 5-2 4-1 7-2 10-2 3-1 5-2 8-2h13 8c2 1 5 0 7 1 1 1 5 1 6 1h0c-1 0-2-1-3-1h0c-3-1-5 0-7-1-7-1-14-1-21-1-3 0-5 0-8 1s-8 2-12 2c-4 1-8 3-12 5l-15 8z"></path><path d="M367 273h6l-1 1s-1 1-2 1-2-1-3-2z" class="H"></path><path d="M393 412l3 6h-1v1c-1-1-3-5-3-6 1 0 1 0 1-1z" class="B"></path><path d="M402 498h1c2 3 2 8 4 13l-1-1c-2-4-3-8-4-12z" class="F"></path><path d="M388 407v-1h1 0l2 2c1 1 2 2 2 4 0 1 0 1-1 1l-4-6z" class="D"></path><path d="M258 435l3-1h6 0v1c-3 0-6 1-9 2v-2z" class="P"></path><path d="M391 521h0c0 5 1 10 1 15 0 2 0 4-1 5v-5c0-4 0-8-1-12 0 0 1 0 1-1v-2zm5-15c1 1 1 4 2 6l1 8c1 2 1 3 1 5l-1-2c-1-4-2-7-2-11-1-2-1-4-1-6z" class="F"></path><path d="M391 435v-1c3 4 4 8 5 12l-6-11h1z" class="C"></path><path d="M358 310c1 0 2 0 3 1 2 0 4 1 6 2s5 2 7 3l-1 1c-2-1-5-3-7-3-1-1-2-1-3-2-2 0-3 0-5-1v-1z" class="F"></path><path d="M339 291h0c1-1 1-1 2-1 0-1 1-1 2-1 0-1 0-1 1-1v-1 1c0 1-1 2-2 2-1 1-1 1-2 1h0c-1 1-1 1-2 1h-1c-1-1-2 0-3 0v1h0 1 0v1c1 0 1-1 2 0h-1-3v-1c-1 0-2 0-2-1h0c1 0 2 0 3-1h5z" class="C"></path><path d="M361 217c2 1 3 1 4 1h1v1h0c0 1 1 1 1 1l1 1h-4c-1-1-1-1-2-1h-1c-1-1-1-2-1-2l1-1z" class="J"></path><path d="M267 434c2 1 5 1 7 1 2-1 5-1 8 0 0 1 1 1 2 2v1c-6-3-11-3-17-3v-1z" class="L"></path><path d="M382 283h1l-1 1c-4 2-7 1-11 0l-2-1h1 12z" class="I"></path><path d="M299 300l4 4c-1 0-1 0-2-1-1 1-1 1-2 1h0c-1 0-2 0-2-1-1 0-1 1-2 0-1 0-2 0-3-1 0 0 0 1-1 0h-2-1-1-1-1l1-1h2c2 0 5 1 7 2h3c1-1 1-2 1-3z" class="B"></path><path d="M272 428c1 0 1 0 2 1h0 7v1c-1 0-1 0-2 1 1 0 1-1 2 0 0 0 1 1 2 1-3-1-6-1-8-2-1-1-4 0-4 0-1 0-2 0-3-1h1s2 0 2-1h1z" class="C"></path><path d="M247 221h1l2-1v1h4l-1 1c-2 0-3 1-5 1-1 0-3 0-4 1h-1v-1c1-1 2-2 4-2z" class="T"></path><path d="M375 537h1c1 3-5 17-7 21l-1-1c0-1 1-2 1-3 1-1 2-3 3-5 0-2 1-3 2-4 0-3 1-6 1-8z" class="F"></path><path d="M295 301c1 1 1 1 2 1 0-1 0-4 1-6h2v1h-2v2l1 1c0 1 0 2-1 3h-3c-2-1-5-2-7-2h-2 0l-2-1c2-1 3-1 4 0 1 0 2 0 3 1h1 3z" class="C"></path><path d="M363 541c1-5 5-9 6-13l1-1c0 1-1 1 0 2v-1h1c-2 3-3 7-5 10h-1c0 1-1 2-2 3z" class="B"></path><path d="M385 439c1 0 1 0 1 1h0c1 3 4 8 3 11-2-3-3-6-4-8h-1v-2-1c1 0 1 0 1-1h0z" class="C"></path><path d="M371 187c0 1 0 2-1 3l-1 1h0c-2 1-5 2-7 2v-1c2-2 5-3 7-5 1 1 1 0 2 0z" class="J"></path><path d="M330 507c0-2 1-4 2-6 1-4 2-8 2-12l1-6c0-1 0-3 1-4 0 2 0 4-1 5v4h0 0v1l1-1h0v3c-1 4-3 8-4 12 0 1-1 3-2 4z" class="B"></path><path d="M214 350c-2 0-7 0-8-1v-1h6c1 1 2 1 3 1-2-2-4-1-5-2h0c2-1 6 0 8 1h1l1 1c-1 0-3 0-4 1h-2z" class="L"></path><path d="M357 362c0 1 1 2 2 3 2 4 3 8 5 12v1l-1 1c0-3-2-5-3-7-1-3-2-6-4-8v-1l1-1z" class="C"></path><path d="M367 273h-1c0-1 0-1 1-1-2-1-3-1-4-2l1-1v1l1-1 1 1h2c2 1 4 0 5 0h3 1 0c-1 1-2 1-3 2h-2 0l1 1h-6zm14 265v1h1c-1 3-1 6-1 9-1 6-4 13-6 18h-1c2-3 2-6 3-9 1-2 2-4 2-5 1-5 1-10 2-14z" class="F"></path><path d="M328 266l2-2h1v12 1c-1 0-1 0-1-1-2-2-2-7-2-10z" class="B"></path><path d="M388 336v-1h0l-2-2s-1 0-1-1h0c2 1 4 1 6 2h1c2 1 2 2 4 4 1 1 3 2 4 4h-1c-3-2-7-5-11-6z" class="S"></path><path d="M392 426c3 7 6 14 7 22l-1 1v-1c0-1 0-1-1-2 0-3-1-5-2-8-1-1-1-1-1-2v-1-1c-1-2-2-5-3-7l1-1z" class="B"></path><path d="M358 311l-4-2c-4-1-7-1-10-1l-19-2c5-1 9 0 14 0 7 1 13 2 19 4v1zm21 21c1-1 3-1 4-1s3 1 5 0c1 0 2 1 2 1l1 2c-2-1-4-1-6-2h0c0 1 1 1 1 1l2 2h0v1h-2-1v-1c-1 0-2-1-2-1l-1-1h-3v-1z" class="K"></path><path d="M239 264l1 1h1l-1-1c2-1 6 1 8 1h3l-1 3h-2c-4 0-7-1-10-3v-1h1z" class="I"></path><path d="M354 218h-1v-1h3 5l-1 1s0 1 1 2h1c1 0 1 0 2 1h0c1 2 4 3 5 5-2 0-4-2-6-3l-1-1h-1 0-1c-2 0-2-1-3-2h2 0c-1-1-4-2-5-2z" class="E"></path><path d="M387 341l-2-2h1c-1-1-1 0-2-1l-2-1c1-1 2-1 3-1h1 1c0 1 1 1 2 1v1l1 1c2 1 3 2 4 3l-1 1c0-1-1-1-1-1h0l1 1h-1l-5-2z" class="H"></path><path d="M283 267l1-2c1 0 3 2 5 3 1 1 3 2 3 4h0c-2-1-6-1-9 0v-5z" class="P"></path><path d="M332 302c11 0 20 1 30 3 2 0 4 0 6 1-1 0-2 0-3-1h-2c-2 0-4 0-6-1-6-1-12-1-18-1-3 0-7 1-10 1v-1c1 0 2 0 3-1z" class="H"></path><path d="M219 358l8-2h3l1 1h0c-4 0-8 1-11 2-5 1-10 3-15 4-2 1-4 2-5 2 0-1 3-2 4-2 4-2 7-3 11-4 2 0 3-1 4-1z" class="O"></path><path d="M267 338h1 0c-2 3-4 4-6 6-2 1-5 3-6 5 1 0 2-1 3-1h0v1c-1 0-1 1-2 1h0-2c-1-1-1-1 0-1h-2-1v-1s0-1 1-1v-1h1 1c0-1 0-2 1-2h1c-1 1-2 3-3 4l13-10z" class="B"></path><path d="M241 269h2c2 0 4 0 5 1h0v1h1v1h0c-2 1-7 1-9 0s-4-1-5-2h0 5c0-1 0-1 1-1z" class="M"></path><path d="M247 240h6l1 1v1h-2v1l2 1h-4c0 1 1 1 1 1l1 1h-3 0l-3-1 1-1v-1h-2c0-1 1-2 2-3z" class="O"></path><path d="M332 298s-1 1-2 1-1 0-1 1h-1c-2 0-5 3-8 2-1 0-5 2-6 2h0v-1h0-1 0 0c2-2 3-5 3-8v-1-2h0c0-1 0-1 1-2v1c0 1 0-1 0 1v2 2-1h1v1h0c-1 1-1 1-1 2h-1v2c-1 1-1 1 0 2 0 1 1 0 1 0h1c1 0 1-1 2-1 0 0 1-1 2-1h0c1 0 3-1 4-1h4c0-1 1-1 2-1zm55 43l5 2h1l-1-1h0s1 0 1 1l1-1h1c1 2 3 4 3 6l-2-1h0c0 2 1 3 2 5h0c-2-2-2-4-4-5l-2-1c-1 0-1 0-2-1-1 0-1 0-1-1h-1c-1 0-2-1-3-2h1 1c-1-1-1-1 0-1z" class="D"></path><path d="M253 236h1c2-1 4-1 6-1 1 1 1 1 1 2h-1c-1 0-1 1-1 1h-2 0l2 2h0-5-1-6l3-1c0-1 1-1 1-2h2v-1z" class="E"></path><path d="M263 425c3-1 6-1 8 0h2c2 0 4 0 5 1 3 0 5 2 8 3h-1c-1 0-3-1-4-1-3-1-5-2-8-2h-2c-2-1-5-1-7 0h-3-3c0 1-1 0-2 1h-2s-1 0-2 1h0-2l1-1h1l1-1h1c1-1 2 0 3-1h6z" class="F"></path><path d="M345 271c2 0 3 1 5 1h2c1 1 2 1 3 1 1 1 2 3 3 4-2-1-3-1-5-1v1h1l-1 1c-1 0-3-1-4-2h0 2 0c-3-1-4-3-6-4v-1h0z" class="D"></path><path d="M232 380h2 1c2-1 13 0 15 0 2 1 3 0 4 1l7 1c2 0 4 1 5 2h-2c-1-1-3-1-5-1h-4l2-1h0-4c-2-1-4-1-6-1h-8-2-4l-1-1z" class="F"></path><path d="M349 293c2-1 5-1 7-1 6 0 12 0 18 1 1 1 2 1 3 1 2 0 3-1 4 1h0v1c-2-1-4-1-6-1-2-1-3-1-4-1h-4c-6-1-12-1-18 0v-1z" class="H"></path><path d="M256 436l2-1v2c-9 4-16 10-19 19-1 0-1 2-1 3h-1v-1l1-2c3-9 10-16 18-20z" class="N"></path><path d="M326 285h1c-1-1-1-1-3-1v-1h0c2-1 6 0 8-1h1c1-1 2-2 3-2h3v1h1-1 2v1h-1v1c-2 0-5 0-7 1-2 0-5 2-7 1z" class="D"></path><path d="M216 352h3 1 5 1c-2 1-3 1-4 1l-2 2h-2 0-4c-4-1-8 0-11 1h-1c1-1 2-2 4-2 3-1 6-1 10-2h0z" class="S"></path><path d="M378 328h3l-1 1c3 1 7-1 11 0h1c0 1 0 1 1 1h2c1 1 2 1 2 2h0-2 0c-1-1-1-1-2-1h-1 0v2 1h-1l-1-2s-1-1-2-1c-2 1-4 0-5 0s-3 0-4 1l1-1h1-2 0c-1-1-1-1-1-2l1-1h-1zM252 436h-3c-1 0-1 0-2 1h-2 0l6-3 1-1 5-2c1 0 2-1 4-2 3-1 7-1 11-1h-1c0 1-2 1-2 1h-1c1 1 2 1 3 1-3 0-5 0-8 1h-1c-1 0-1 1-2 1-1 2-5 2-6 3-1 0-2 1-2 1z" class="B"></path><path d="M251 223h3 0c1 0 1 0 1-1h2 1 2c-1 1-1 1-2 1l-1 1c0 1-1 1-2 1-1 1-3 2-4 2 0 1 1 2 1 3h1c-3 0-6 2-9 2 1-1 2-1 3-2s2-1 2-2h-2c0-1 2-2 3-3 0-1 1-1 1-2z" class="E"></path><path d="M369 424c-1-3-2-8-1-10 3 7 5 13 7 20 0 2 1 5 2 7v1c-1 0-1 1-1 1v-1-4c-1-1-1-1-1-2l-6-12z" class="C"></path><path d="M272 248v-1h0 0l2 2v-1l-1-1v-1h0 0l3 3c0 1 1 2 2 3 0 1 1 2 1 3l-1 1h0l2 2-1 1-2-1-4-4c-1 0 0 0-1-1l-1-1-1-1 1-1c2 0 4 3 6 5-1-1-1-2-2-2 0-1-1-2-1-3l-1-1-1-1z" class="H"></path><path d="M400 355h2c1 0 2 0 4 1 1 0 3 1 4 2l2 1 2 1-1 1h0c-1 0-1 1-1 1v1c1 0 1 1 2 1l-1 1c-1-1-3-3-5-4l-4-2c-1 0-2 0-3-1v-1h2 0l-1-1c-1 0-1 0-2-1z" class="C"></path><defs><linearGradient id="G" x1="369.026" y1="508.713" x2="353.267" y2="523.819" xlink:href="#B"><stop offset="0" stop-color="#232325"></stop><stop offset="1" stop-color="#3f3f3e"></stop></linearGradient></defs><path fill="url(#G)" d="M356 527c4-8 8-16 9-25v2h1c0 1-1 3-1 5-2 8-5 16-9 23v-2-3z"></path><defs><linearGradient id="H" x1="329.406" y1="503.875" x2="333.128" y2="506.321" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#5e5e5f"></stop></linearGradient></defs><path fill="url(#H)" d="M336 491v3-1-1c1 0 1-1 1-1v-1-3-2h1c-1 10-4 24-11 32v-1-3l3-6c1-1 2-3 2-4 1-4 3-8 4-12z"></path><path d="M236 300c1 0 2 0 3-1h1l5-1c1 0 4-1 5-1 1 2 2 1 4 1 0 1 1 1 1 1-4 0-7 2-11 3-3 0-5 0-7 2h0v-2h-3-1-1 0c1-1 2-2 4-2z" class="C"></path><path d="M327 513v3 1l-1 1c-4 7-11 12-18 16-3 2-6 3-9 4 2-2 5-3 8-5 9-5 15-12 20-20z" class="M"></path><path d="M214 355h4 0c0 1 1 1 1 2v1c-1 0-2 1-4 1v-1c-6 1-12 2-17 6 0 0 0-1 1-1 2-4 9-6 13-6h0c-3 0-7 1-10 1v-1c1 0 2 0 3-1 3 0 5-1 9-1z" class="L"></path><path d="M248 265c1-1 2-1 3-1h1 1c1 0 1-1 1-1h5l-2 2c0 1 0 1 1 1l-1 1-1 1-5 3h1s1 0 1-1l2-1h1l-2 2c-1 1-2 2-4 2l-1-1v-1h-1v-1h0c-1-1-3-1-5-1h-2c2 0 5 0 7-1h2l1-3h-3z" class="D"></path><path d="M251 265c1 0 2 0 2-1 2 0 1-1 3 0h0c-2 2-4 3-6 4l1-3z" class="M"></path><path d="M285 385c1 0 1 0 2 1h1l1 1c1 0 2 0 3 1 3 1 5 3 8 5h1l1 2h0l-1-1h-1v-1l-1 1 2 1c1 1 2 2 4 3h0c1 2 3 4 4 5s1 1 1 2h0-1c-5-5-10-10-17-15-2-1-5-3-7-4v-1z" class="K"></path><path d="M281 578c3 0 7 1 10 1 9-1 17-3 25-6 2-1 5-2 8-2-2 1-5 2-8 3-9 4-17 6-27 6l7 2-3 1c-2-1-3-1-4-2-3-1-6-2-8-3z" class="N"></path><path d="M265 302h11c2 0 7 1 8 2v1c-2-1-5-1-7-1-8-1-15 0-23 0-3 1-7 1-11 2-2 0-4 1-6 2v-1c5-4 20-4 28-5z" class="K"></path><path d="M258 221c1 0 2-1 3-1l1 1v1s0 1 1 2v1l-1 1c0 1-1 1-1 2h-1c-1 0-2 0-3 1-1 0-1 0-2 1h-2-1c0-1-1-2-1-3 1 0 3-1 4-2 1 0 2 0 2-1l1-1c1 0 1 0 2-1h-2-1-2c0 1 0 1-1 1h0-3-3c2 0 3-1 5-1h3c1-1 1-1 2-1z" class="G"></path><path d="M257 224h3c0 1-3 2-4 2h-2l1 1c2 0 3-1 5 1-1 0-2 0-3 1-1 0-1 0-2 1h-2-1c0-1-1-2-1-3 1 0 3-1 4-2 1 0 2 0 2-1z" class="T"></path><path d="M295 549c-3 1-6 1-9 1-9 0-18-2-27-5-2-1-13-6-14-8l11 6c12 5 23 5 36 5h0 3 2-1 0c-1 0-3 1-4 1h3z" class="E"></path><path d="M352 201l2-2v1h2c4 0 7-1 11-1v1l7 2-7 1h-8c-1 0-2 1-3 1s-3 1-5 1c0-1-1-1-1-2l2-2z" class="G"></path><path d="M350 203l2-2c2 1 2 2 4 2v1c-1 0-3 1-5 1 0-1-1-1-1-2z" class="Q"></path><path d="M367 200l7 2-7 1h-8-3v-1c2-2 8-2 11-2z" class="J"></path><path d="M238 459l-1 3v3 5c2 11 6 19 15 25 2 1 5 2 7 4h0c-2 0-6-2-8-3-3-3-7-6-10-9-1-2-2-3-2-5-1-1-1-3-2-4h0c0-1 0-1-1-1v-1c0-1-1-4-1-5 0-3 1-6 1-9 0-1 1-2 1-3h1z" class="B"></path><path d="M197 376h1c3-2 5-4 8-6l4-1c1-1 1-1 2-1s2 0 3-1h1 1v1h-1l-2 1h-2c-1 0 0 0-1 1h-2 0s-1 1-2 1l-1 1s-1 0-1 1c-1 0-1 0-2 1h0 2 0l-1 1 1 1-15 8c0-2 1-2 2-4 2-1 3-3 5-4z" class="F"></path><path d="M296 373c17 10 33 26 43 44 2 2 4 5 5 8h0l-1 1c-8-19-24-36-41-48-2-2-5-3-7-4v-1h1z" class="C"></path><path d="M363 541c1-1 2-2 2-3h1c-2 6-6 12-9 18-6 9-12 18-20 25h-1 0c10-11 19-23 25-37 1 0 2-3 2-3z" class="D"></path><path d="M362 283h1l1 1h1l2 2h-1v1c1 0 3 0 4 1 0 0 1 0 2 1h1s1 0 2 1h1 6v1h0-4v1h1 1c1 0 1 0 2 1l-1 1c-2 0-3-1-5-1h-3c-4-2-9-3-13-4 2-1 3-2 5-3h0s1 0 1-1l-4-2z" class="C"></path><path d="M292 548c12-2 25-6 35-14 3-2 6-5 9-8-2 3-4 5-6 7-6 6-16 11-25 14-3 1-7 2-10 2h-3c1 0 3-1 4-1h0 1-2-3 0z" class="J"></path><path d="M313 522h0l-3 3-1 1-15 9c-4 1-9 2-13 3h0c-2 1-5 1-8 1s-6 0-9-1c-3 0-5-1-8-2-6-2-12-4-17-8h-1v-1c8 5 18 10 27 10 2 0 4 1 5 0h13c2 0 4-1 6-2 2 0 4-1 6-1 5-3 9-6 14-9 1-1 2-2 4-3z" class="C"></path><path d="M334 260h0v-1l-1-1v-1l-3 2-1 2v-1h-1 0v-1l4-4c1 0 1-1 2-2 1 0 2-1 3-2 1 0 2-2 3-2l1 1-4 3c-1 0 0 0 0 1 0 0-2 1-2 2h0c1-1 1-1 2-1s2-1 2-1l8-5 1-1 1 1c-2 2-5 3-7 5-1 0-1 0-2 1h5c0 1 0 1 1 1h2l-1 1h0l-1-1c-1 0-1 0-2 1-1 0-2 1-3 1s-3-1-4 0c-1 0-3 2-3 2z" class="B"></path><path d="M231 335h4c1-1 1-3 4-5h0c1 0 2-1 3-1l1-1c1 0 1 0 2-1 5-4 13-9 20-11 1 0 1-1 2-1l5-2 3-1 4-1h0c-1 1-2 2-3 2l-15 5-5 3c-1 1-2 2-3 2l-3 2-8 5h-1s-1 1-2 1l-3 3 3 4c0-1 1-3 2-3h0v1c-1 2 0 3-1 4h-1c-1-1-1-2-2-3v-1c-1 0-1 0-1-1l-2 2h0l1 1h0-2c-1-1-1-2-2-3z" class="D"></path><path d="M276 432h1l6 2h2 1l2 1h1c4 1 10 6 12 10-1-1-2-1-2-2-2-1-4-3-5-4h-1c-1-1-1-1-2-1-2 0-4-1-6-2-1-1-2-1-3-1-3-1-6-1-8 0-2 0-5 0-7-1h0-6c1-1 2-2 3-2h5 7z" class="R"></path><path d="M276 432h1l6 2h2 1l2 1h1c4 1 10 6 12 10-1-1-2-1-2-2-2-1-4-3-5-4h-1c-1-1-1-1-2-1-4-3-10-4-15-6z" class="C"></path><path d="M265 571c2-1 6 0 8 0 3 1 5 1 8 1 13 0 25-2 37-6l1 2-14 3c-8 2-31 4-37 1h-1c-1 0-2-1-2-1z" class="I"></path><path d="M243 254l1-1c1 0 1 1 2 1 2 1 4 2 5 4v1s0 1 1 1 2 0 3 1c2 1 3 1 5 1h1l1-1v-1h-2 0 5 0c-1 1-2 1-2 2l-2 1h-2-5s0 1-1 1h-1-1c-1 0-2 0-3 1-2 0-6-2-8-1l1 1h-1l-1-1c1-1 1-1 2-1l-2-1 1-1h1c1 1 2 1 4 1-1 0 0-1 0-1h3v-1h-2c-1-1-2-1-3-1 0-1-1-1-2-2l1-1 1 1h1c0 1 1 1 2 1h1c1 1 1 1 2 1l1-1c-1-1-1-1-2-1l-4-3h-1z" class="K"></path><defs><linearGradient id="I" x1="271.329" y1="363.078" x2="253.343" y2="356.041" xlink:href="#B"><stop offset="0" stop-color="#6a6a6a"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#I)" d="M252 356h1c15 2 30 10 43 17h-1v1c-14-8-28-14-44-16l-1-1 1-1h1z"></path><path d="M252 356h1v2h-2l-1-1 1-1h1z" class="N"></path><path d="M342 206h0c8-6 15-14 24-19 1-1 3-2 4-1l1 1c-1 0-1 1-2 0-3 0-9 5-12 7-5 5-11 10-14 15l-10 8h-1c-1 0-2 0-3 1-1 0-1 0-2-1h1v-1c2-3 5-2 8-4 1 0 3-2 5-3 1-1 1-1 1-3z" class="L"></path><path d="M353 455h0c1 1 1 1 1 2v-2c1 2 1 4 2 5 1 7 1 13 1 19 0 3 0 5-1 7v6l-1 3v1 1c0 1 0 0-1 1v1 1 1c0 1 0 1-1 1v2 2c-1 2-1 4-2 5l-1 5c0 1-1 2-1 3h-1l-1 3-2 5-1 2c-1 1-1 2-2 2h0c1-3 3-7 5-10 0-2 1-3 2-5v-1l1-2 1-3v-1c1-2 1-4 1-5s0-1 1-2v-1-1-2c1 0 0 0 0-1 1-1 1-2 1-2v-3c1-1 0-2 1-3v-8c1-9 0-17-2-26zm-127 59h1 0c2 2 5 4 7 6l1 1 2 1 1 1c2 1 3 2 5 3l5 2 11 4 3 1c1 1 3 1 5 1 14 2 28-2 39-10l1-1h0l-1 1c-3 4-8 6-13 8-3 1-6 2-8 2-11 2-21 1-31-2l-2-1-2-1-2-1c-2 0-3-1-5-2h-1l-2-2c-1 0-2-1-3-1l-3-3c-1 0-1-1-2-1-1-1-1-2-2-3-2 0-3-2-4-3h0z" class="B"></path><path d="M362 192v1c2 0 5-1 7-2h0v1h1v1c-1 1-2 2-3 2l1 1c1 1 3 1 5 2-2 0-4 1-6 1-4 0-7 1-11 1h-2v-1c2-2 5-5 8-7z" class="E"></path><path d="M369 192h1v1c-1 1-2 2-3 2l-8 2c3-2 6-4 10-5z" class="G"></path><path d="M368 196c1 1 3 1 5 2-2 0-4 1-6 1-4 0-7 1-11 1 1-1 3-2 4-2s2 0 3-1l5-1z" class="J"></path><path d="M271 430s3-1 4 0c2 1 5 1 8 2 2 1 4 1 6 3h-1l-2-1h-1-2l-6-2h-1-7-5c-1 0-2 1-3 2l-3 1-2 1h-1l-5 2c-1 0-2 1-3 1v1c-2 1-4 3-5 4l-1 2c-1 1-2 3-3 4l-1 1v1c0 1-1 1-1 1h0l1-4c1-1 2-2 3-4 0 0 1-1 1-2 1-1 2-2 4-3l2-2h0l5-2s1-1 2-1c1-1 5-1 6-3 1 0 1-1 2-1h1c3-1 5-1 8-1z" class="D"></path><path d="M293 281c1 0 1-1 2-1s1 0 2-1v1h0c1-1 1-1 2-1h1l1-1h1l1-1v1h1v1h-1 4 0 1v1h1l1 1h1v-1c1 0 3 3 4 4h0c1 2 1 2 1 4h0v1l-1 1v4c0 1-1 1-1 2-2 1-4 1-5 1v-1h1 2s0-1 1-1v-3c1 0 1-1 2-2l-1-1c0-2-1-4-2-5-1 0-2-1-3-1h0-1c-2-1-4-1-6-1-1 0-2 0-3 1h0c-2-1-5 0-7 0h-1c1-1 1-1 2-1h0v-1z" class="C"></path><defs><linearGradient id="J" x1="323.812" y1="266.421" x2="324.672" y2="278.717" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#e6e6e6"></stop></linearGradient></defs><path fill="url(#J)" d="M323 278c-1-1-3-2-3-3-1-1-1-2-1-3 2-3 6-6 9-7v1c0 3 0 8 2 10 0 1 0 1 1 1-2 1-4 2-6 2h-1l-1-1z"></path><defs><linearGradient id="K" x1="306.794" y1="451.209" x2="292.125" y2="450.958" xlink:href="#B"><stop offset="0" stop-color="#373636"></stop><stop offset="1" stop-color="#4e4f50"></stop></linearGradient></defs><path fill="url(#K)" d="M282 435c1 0 2 0 3 1 2 1 4 2 6 2 1 0 1 0 2 1h1c1 1 3 3 5 4 0 1 1 1 2 2 4 5 8 16 7 23h0v1h-1v-4c-1-3-1-5-2-7-4-10-12-16-21-20v-1c-1-1-2-1-2-2z"></path><path d="M227 333h-5c1-1 3-2 5-3v-1c1-2 6-3 8-4-1 2-4 2-6 4h1l9-3c5-2 8-5 12-7 6-4 19-10 25-9-1 1-7 2-9 3-3 0-5 1-8 2-5 2-9 5-14 8-2 2-5 3-7 5l7-3c-2 2-5 4-7 5-3 0-7 2-10 3h0-1z" class="S"></path><path d="M244 249c1 1 1 1 2 1l1 1c1 1 3 2 5 2 4 1 5 5 10 4v1c1 1 4 2 6 2-1 1-2 0-3 0h0-5 0 2v1l-1 1h-1c-2 0-3 0-5-1-1-1-2-1-3-1s-1-1-1-1v-1c-1-2-3-3-5-4-1 0-1-1-2-1l-1 1-1-1c1-1 1-2 1-2 0-1 0-1 1-2zm88-32h1l10-8c-2 3-5 10-8 11-1 0-1 0-1 1-1 0-3 1-3 2 0 0 0 1-1 1v2c-1 1-1 2-2 3 0-1 1-1 0-2v-1c0 1 0 1-1 2 0 1 0 2-1 4l-1 1h-1 0v-1h-1l1-2h-1 0c0 1 0 2-1 2l2 2-1 1h0 0c0-1 0-1-1-2 0 1 0 2-1 2v1 2c-1 1-1 2-1 3h0c1-6 3-11 5-16 2-3 5-6 7-8z" class="D"></path><path d="M221 486c-1 0-2-2-2-3-2-3-3-5-4-8l-1-6h-1c0-3-2-5-2-8-1-2-1-5-1-8 0-5 0-9 2-13l2-8c0-1 0-1 1-2l1-3v-1c1-1 1-1 2-1h0l-2 3-1 5h-1l-1 7c-1 1-1 2-1 3-1 4 0 7 1 11 0 4 1 8 1 12l2 7 2 5 2 4c1 2 2 3 2 4h-1z" class="B"></path><path d="M222 486l3 5h0l2 2s0 1 1 1v1c2 2 4 5 6 6l1 1 1 1 5 4 4 2v1c1 0 2 0 2 1h1l1 1 4 2 3 1h1l4 1c8 2 17 2 25 0 5-2 9-4 13-7h0l-5 4s-1 1-2 1c-10 5-24 6-35 2l-4-1-6-3c-2-1-4-2-5-3-2-1-3-2-4-3-1 0-1-1-2-1 0-1-1-1-2-2l-4-4c-1-1-2-1-2-2s0 0-1-1l-1-1-1-1-2-3c0-1 0-1-1-2 0-1-1-2-1-3h1z" class="K"></path><defs><linearGradient id="L" x1="358.785" y1="539.185" x2="333.031" y2="548.057" xlink:href="#B"><stop offset="0" stop-color="#40403f"></stop><stop offset="1" stop-color="#605f63"></stop></linearGradient></defs><path fill="url(#L)" d="M356 527v3 2c-1 3-4 7-6 10-8 12-17 20-30 25l-1 1-1-2c14-5 25-15 32-27 1-3 3-5 4-7s1-3 2-5h0z"></path><path d="M204 401c1-2 4-5 6-6l1-1c1-1 2-1 3-2 1 0 1-1 1-1l9-4s1 0 1-1h2l1-1c2 0 3-1 4-1h0 2 0c2-1 4-1 5-1 2-1 3 0 4-1h9c1 1 2 1 3 1h4c2 0 4 0 5 1h2c5 2 11 4 16 6 0 1 1 1 2 2-4-2-7-3-11-4-6-2-13-3-19-4-3-1-7-1-9 0-4 0-7 0-11 1h-1c-1 0-1 0-2 1h-1 0-1c-2 1-2 1-3 1h-1c0 1-1 1-1 1-3 1-6 3-8 4l-1 1h-1c-2 2-4 3-5 4l-1 1c-2 1-2 3-4 3z" class="D"></path><path d="M270 296h1c1 1 2 1 3 2 3 0 7 1 10 2l2 1h0l-1 1h0 0-2-1s-2 0-2-1h-3 0 0l-1-1c-1 0-1 0-2 1v-1h-3-1c-1-1-3 0-4-1h-2-6v-1h-2 0c1-1 2-1 2 0h0v-1h-1-2 0v1h-1 1 0 1l-1 1h3s0 1 1 1c2 0 4-1 6 0-2 0-8 1-10-1 0 0-1 0-1-1-2 0-3 1-4-1-1 0-4 1-5 1l-5 1h-1c-1 1-2 1-3 1 1-2 3-2 5-3h2 0 1c3-1 5-1 8-1 4 0 9 1 13 1h4 0c1-1 0-1 1-1z" class="B"></path><path d="M274 298c3 0 7 1 10 2l2 1h0l-1 1h0c-1 0-1 0-2-1h-1-2c-2 0-3-1-5-2h0l-1-1z" class="H"></path><path d="M268 260l1 1h0 1c0 1 0 1 1 1l1 1-1 1v1h1v1l-4 3h-1-3-1c-1 0-1 0-2 1h-2-3c-1 1 0 1-2 1l2-2h-1l-2 1c0 1-1 1-1 1h-1l5-3 1-1 1-1c-1 0-1 0-1-1l2-2h2l2-1c0-1 1-1 2-2 1 0 2 1 3 0z" class="S"></path><path d="M257 267h3c1 0 2 0 3 1h0-2-5l1-1z" class="L"></path><path d="M259 263h2v1h5v1h-7-1l2 1v1h-3l1-1c-1 0-1 0-1-1l2-2z" class="M"></path><path d="M268 260l1 1h0 1c0 1 0 1 1 1l1 1-1 1v1h1v1l-4 3h-1l-1-1c-1 0-1 0-1-1 1 0 1 0 1-1v-1-1h-5v-1l2-1c0-1 1-1 2-2 1 0 2 1 3 0z" class="I"></path><path d="M268 260l1 1h0-2c0 1 1 1 1 1v1l1 1h-3-5v-1l2-1c0-1 1-1 2-2 1 0 2 1 3 0z" class="M"></path><path d="M261 263c1 0 2 0 3-1 1 0 3 1 4 1l1 1h-3-5v-1z" class="L"></path><path d="M354 218c1 0 4 1 5 2h0-2c1 1 1 2 3 2h1 0 1l1 1v1c0 1 0 2 1 2v2l3 3-5-1-1-1-6-1c-1-1-2-1-4-1 0-1 0-1-1-2h-2v-1h2c0-1 0-1-1-2l-1-1 1-1h2l1-1c1 0 1 0 2-1z" class="G"></path><path d="M349 222h3 3v1c-1 0-2 1-3 1v2s0 1-1 1c0-1 0-1-1-2h-2v-1h2c0-1 0-1-1-2z" class="P"></path><path d="M354 218c1 0 4 1 5 2h0-2-1c-1 1-2 1-4 1l-1 1h1-3l-1-1 1-1h2l1-1c1 0 1 0 2-1z" class="O"></path><path d="M361 229c-1 0-1-1-1-2h-2l-3-1v-1h2c1-1 5 2 6 3h1l3 3-5-1-1-1z" class="E"></path><path d="M377 385h0c-1-1-1-2-2-2v-1c-1-1-2-1-2-2h0l2 1 1-1 1 1h0l-1-1v-1c3 4 6 9 10 13 1 2 3 4 4 5 3 4 5 7 7 11 1 1 1 3 2 5l-1 1v-1c-2-5-7-9-10-14-4-6-7-14-14-19l11 16 7 9c1 3 3 5 4 8l-4-5c-1-2-3-3-4-5v1c-1-2-3-3-4-4v-1l1 1h1c-1-1-1-1-1-2l-1-1c-1-3-4-5-5-8-1-1-2-2-2-4z" class="D"></path><path d="M397 384h0l1 1c2 1 3 3 4 3l3 3h0c0-1-1-2-1-3h1c0 1 1 2 2 3v1c0 1 0 1 1 2l1 2c1 2 2 4 2 6h0l-1-2c-2-5-7-12-12-14 3 3 6 7 8 12 3 8 5 17 7 25v1h-1v-1c-1-3-2-7-2-10-2-5-4-11-7-16l-1-1h0l4 10c2 6 4 13 4 19-1-3-2-6-3-10l-4-10c0-1-1-2-1-3h0 0v1c0 2 1 5 2 7h-1v-1-1l-1-2v-1c-1-1-1-2-1-3v-1-1-1h1 0c0-1-1-3-1-4v-1c3 2 5 9 7 12h0v-2c-1 0-1-1-1-1v-2h-1v-1-1c-1-1-1-1-1-2s0 0-1-1l-2-4-3-4c-1-1-2-2-2-3v-1h0zm-200-8v-1l2-1c1-1 0 0 0-1 1 0 2-1 3-2 1 0 2-1 3-2s3-1 4-2h2c1 0 1 0 2-1h1 1 1l2-1h1c1-1 2-1 3-1h0 2 0l1-1h3l3-1c1 0 3 1 5 0 3 0 8-1 11 0 1 1 2 0 3 1h1 3 1 1c2 1 4 1 5 1 1 1 2 1 3 1l5 2c1 0 2 1 3 1s1 0 2 1h1l4 2c1 0 1 0 2 1h0l-7-2c-1-1-3-1-4-1-1-1-3-1-4-2h-2 0l-2-1c-1 0-2 0-3-1-1 0-3 0-4-1h-5s-2 0-3-1c-3 0-7 0-10 1h-4-3c-2 1-3 1-5 1-1 1-3 0-4 1-2 0-3 0-4 1h-1-1c-1 1-2 1-3 1s-1 0-2 1l-4 1c-3 2-5 4-8 6h-1z" class="B"></path><path d="M332 266l1-1 3 1c2 0 3 0 4 1 1 2 1 4 0 5-1 4-4 7-7 8s-5 1-7 0c-1 0-2-1-3-2l1 1h1c2 0 4-1 6-2v-1c1-3 1-7 1-10z" class="J"></path><path d="M254 244c1 0 2 1 3 2h-1-2c-1 0-1 0-1 1l1 2s1 0 1 1h0c1 0 1 1 2 1 1 1 2 1 3 2 1 0 1 1 2 1 1 1 1 1 2 1 0 1 1 0 2 1h0 1 1 0c-1 0-2-1-3-3l1-1 1 1h1s0 1 1 1h0c1 0 1 1 2 1l1 1h1c1 0 2 1 2 1 1 0 1 1 1 1-2 0-6-2-8-3 1 1 2 2 4 3-2-1-6-2-8-2 1 1 2 2 3 2h-5v-1c-5 1-6-3-10-4-2 0-4-1-5-2l-1-1c-1 0-1 0-2-1h1c1-1 2-1 2-1v-1s-1 0-1-1h0 3 0 3l-1-1s-1 0-1-1h4z" class="B"></path><path d="M368 424h0c0 2 4 13 3 13v1-1l-8-18c0 4 1 8 1 12-1-2-2-5-3-8 0-3-2-6-2-9h-1c0 2 1 5 2 7v4c-2-2-6-16-7-18 0 3 1 7 1 9l-6-17c0 4 1 7 1 11l-5-18h0s0 1 1 1c0 1 0 2 1 3v1c0 1 0 1 1 2v1h0v2h0 1v-2c-1-1-1-1 0-2v1-2h0c1 1 1 2 1 3 0 0 0 1 1 2v2 1l1 1v2c1 2 1 4 2 5v-1-4h1l2 5 1 2v2h0c0 1 1 1 1 2h0 0v-2-3h-1v-1h0v-2l1 1v-1c1 1 0 1 1 1v3c1 0 1 0 1 1v1c1 1 1 2 1 3v1l1 2s0 2 1 2v-2c-1 0 0 0-1-1v-3c-1-1-1-3-1-5h0v-1c-1-1-1-2-1-3v-2c1 3 3 6 4 8 1 1 2 2 2 3 1 2 1 4 2 5z" class="C"></path><path d="M307 469h1v-1h0c1 10-2 19-9 26-1 2-3 4-5 5s-4 3-6 4c-1 0-3 1-4 1s-1 1-2 1h-3c-1 0-1 0-1 1h-3-9l-1-1c2-1 8 0 10 0 1-1 2 0 3-1h2c1-1 2-1 3-1 1-1 2-1 3-1 1-1 2-2 3-2l2-2 2-1v-1c-1 1-2 1-3 2h-1-1 0c7-3 13-10 15-16l2-6c0 1 0 2-1 3 0 1-1 3-1 4-1 2-2 3-2 5h0 0c1-1 2-2 2-3 1-1 1-1 1-3h1v-2h1v-2h0v-2h0c1-1 1-5 1-7z" class="H"></path><path d="M348 231c0-1 1-1 1-1h1 0l-2-1v-1h6 1l6 1 1 1h-3v2h0v1 1h1c2 2 5 3 7 5v1h-1v1c-1 0-1 0-1 1 1 0 1-1 2 0h-1c-1 2-3 3-6 3 2-1 3-2 5-3h-3c-1 0 0 0 0-1-2 0-4 0-6 1h0c1-1 2-2 4-2l-1-1h-2-4c1-1 2-1 3-2h0-2c0-1 1-1 1-2-2 0-5 2-7 2 1-1 3-2 4-3h0c-1-1-2-1-3 0h0c-1-1 0-1 0-2l-1-1z" class="L"></path><path d="M348 231c0-1 1-1 1-1h1 0l-2-1v-1h6 1l6 1 1 1h-3v2h0v1 1h-3c-2-1-4 0-6-1l1-1c-1-1-2-1-3-1z" class="O"></path><path d="M355 228l6 1 1 1h-3v2h0v1c-1-1-2-1-3-2 0-1-1-2-2-3h1zm5 180c0-1-1-1-1-2v-1l-1-2v-1-1c-1 0-1-1-1-1 0-1 0-1-1-2v-1l-1-3v-1l-1-1v-1c0-1-1-2-1-2v-2c1 0 1 1 1 2h0v-1-1h0l1 1v1l1 1v1s0 1 1 2h0l3 7v1c0 1 1 2 2 4v-2c-1 0-1-1-1-2s0-2-1-3v-1c-1-1 0-1-1-1v-1-1l1 1c0-1 0-1-1-1v-3s0-1-1-1v-1h1s0 1 1 2v2c1 1 1 2 1 3l1 1h0v1c0 1 0 1 1 1v2 1l1 2v-1h0v-2h1 0v2h0c1 1 1 1 1 2h0l1 1-1 1c0-1-1-2-1-3v1 2c1 1 1 1 1 2s2 4 2 5c-1 2 0 7 1 10h-1c-1-1-1-3-2-5 0-1-1-2-2-3-1-2-3-5-4-8z" class="F"></path><path d="M378 328c-1 0-1-1-2-1l1-1h0c0 1 1 1 1 1h0l-1-1 1-1c0-1 0-1-1-2h-1 0c0-1 0-1 1-2h1l1-1h-2c0 1-1 1-1 1-1 1-1 1-1 2h-1c1-2 2-3 3-4 4-2 9-1 13-2l1 1c-1 0-1 0-2 1h-1-1c1 1 2 1 3 0l1 1h0c-1 1-1 1-2 1h-1l-1 1c2 0 5-1 6 0 0 1 0 1-1 1h-1c1 2 2 1 3 2 1 0 2 2 2 2v1c-1 0-2-1-2-1h-1c1 1 4 2 4 3h0c-2 0-3-1-4-1-1-1-3-1-4-1-2-1-6 0-8 0h-3z" class="K"></path><defs><linearGradient id="M" x1="308.928" y1="285.408" x2="301.182" y2="293.09" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#M)" d="M302 282c2 0 4 0 6 1h1 0c1 0 2 1 3 1 1 1 2 3 2 5l1 1c-1 1-1 2-2 2v3c-1 0-1 1-1 1h-2-1v1h-8v-1c-1-3-1-6-1-9 1-1 1-1 3-1v-1l1-1-2-2z"></path><path d="M309 296c-2 0-6 1-7 0 0-1 0-1 1-2 2 0 4-1 5 0s2 1 2 2h-1z" class="P"></path><path d="M343 211l4-4v3h-1c1 1 2 0 2 1s-1 1-2 1v1c1 0 2 0 3 1h-3l2 2h-1v1h4v1h-3-2v1h2c2 1 1 0 3 1h-2l-1 1 1 1c1 1 1 1 1 2h-2v1h2c1 1 1 1 1 2 2 0 3 0 4 1h-1-6v1l2 1h0-1s-1 0-1 1h-1l-1-1h-2 0l1-1v-1h-6v1-1l1-1h-1-3c0-2 1-3 1-4l3-6c1-1 2-5 3-6z" class="R"></path><path d="M337 223h3v-2c1 0 1 0 1-1 0 0 0-1 1-1v-1c0 1 1 1 1 2-1 1-1 1-1 2 0 0-1 0-1 1v1s-1 0-2 1c1 1 2 0 3 1-1 1-1 1-2 1h-1-3c0-2 1-3 1-4z" class="S"></path><path d="M337 223l3-6c1-1 2-5 3-6h1c0 1 1 1 2 0-1 1-2 2-3 2v1h1c0 1-1 1-2 1 1 1 2 1 2 2h-1l-1 1v1c-1 0-1 1-1 1 0 1 0 1-1 1v2h-3z" class="I"></path><path d="M347 231c1-1 1-1 1-2l-3-3c-2 0-2-1-2-2 1-1 2-1 4-1v-1l-1-1v-1s0-1-1-2h3-2v1h2c2 1 1 0 3 1h-2l-1 1 1 1c1 1 1 1 1 2h-2v1h2c1 1 1 1 1 2 2 0 3 0 4 1h-1-6v1l2 1h0-1s-1 0-1 1h-1z" class="N"></path><path d="M245 210h0 2c1 0 2-1 3-1h0c1 1 2 1 3 0h2v1c-1 0-1 0-2 1h0 6v1c1 1 2 1 4 1-1 1-1 1-2 1v1h1v2l1 1h-2v1h2v1h-2c-1 0-2 1-3 1s-1 0-2 1h-3l1-1h-4v-1l-2 1h-1c-1-1-1-1-2-1 0-1 0-1 1-2h-1l-3 1v-1c1-1 1-2 3-3h0c-1 0-2 0-3 1v-1c1-1 1-1 2-1v-1h0-1l-1-1h0c1-1 2-1 3-2z" class="J"></path><path d="M250 220c1 0 3-1 4-1v1 1h-4v-1z" class="G"></path><path d="M259 216l2 1v1 1h2v1h-2c-1 0-2 1-3 1 0-1 0-1 1-3h0-3c0-1 1-1 2-1l1-1z" class="E"></path><path d="M253 213l6-1c1 1 2 1 4 1-1 1-1 1-2 1v1h1v2l1 1h-2v-1l-2-1h-4l6-2c-3-1-6 1-8-1z" class="O"></path><path d="M245 215c1 1 1 1 2 0 2-1 5 1 7 0h0c-1 1-2 1-3 1 0 1 0 1-1 1-1 1-3 1-5 1l-3 1v-1c1-1 1-2 3-3z" class="G"></path><defs><linearGradient id="N" x1="254.521" y1="208.884" x2="248.551" y2="216.319" xlink:href="#B"><stop offset="0" stop-color="#dae0dc"></stop><stop offset="1" stop-color="#fffcff"></stop></linearGradient></defs><path fill="url(#N)" d="M244 213h1c2 0 4 0 6-1h1c0-1 0-1 1-1h0 0 6v1l-6 1v1h-3c-2 1-4 0-6 0h0v-1z"></path><path d="M245 210h0 2c1 0 2-1 3-1h0c1 1 2 1 3 0h2v1c-1 0-1 0-2 1h0c-1 0-1 0-1 1h-1c-2 1-4 1-6 1h-1 0-1l-1-1h0c1-1 2-1 3-2z" class="T"></path><path d="M344 257c1-1 1-1 2-1l1 1-2 1h0 3 1c0 1 0 1 1 2h0v2c1 1 1 1 1 2l1 1c1 1 1 2 2 3h-1v1c-1 1-1 1-2 1h1s1 0 2 1v1h-1-1-1l-2-1c-3-1-7-2-8-5l-1 1c-1-1-2-1-4-1l-3-1-1 1-1-1c1-1 1-1 2-1h2 1c-2-1-3-1-5-2 1 0 2-1 3-2 0 0 2-2 3-2 1-1 3 0 4 0s2-1 3-1z" class="M"></path><path d="M344 257v1h0c-2 0-2 1-4 1v1c-1 0-3 0-3 1 1 0 3 0 5 1-2 0-4 0-6 1h0c2 1 4 1 6 2h-1-1 0l1 1-1 1c-1-1-2-1-4-1l-3-1-1 1-1-1c1-1 1-1 2-1h2 1c-2-1-3-1-5-2 1 0 2-1 3-2 0 0 2-2 3-2 1-1 3 0 4 0s2-1 3-1z" class="C"></path><path d="M344 257c1-1 1-1 2-1l1 1-2 1h0 3 1c0 1 0 1 1 2h0v2c1 1 1 1 1 2l1 1c1 1 1 2 2 3h-1v1c-1 1-1 1-2 1h1s1 0 2 1v1h-1-1-1l-2-1c-3-1-7-2-8-5 2 1 5 1 7 1h1c-1-2-4-1-6-2h7 0c-1-1-3-1-5-1 1-1 2-1 3-1-1-1-2-1-3-1v-1h3v-1c-2 0-5-1-6-1s-1 1-1 1h-1v-1c2 0 2-1 4-1h0v-1z" class="D"></path><path d="M326 285c2 1 5-1 7-1 2-1 5-1 7-1v1h-2 1v1h1 1c-2 2-4 1-6 2-1 0-1 0-2 1-1 0-2 0-3 1h0-4c0 1 0 1 1 1h2v-1c1 0 3 0 3-1h1c1 1 2 1 2 0l1 1h0l1 1h1l1 1h-5c-1 1-2 1-3 1h0c0 1 1 1 2 1v1 1h2 0 1 1l1-1h1 1c3 0 6-1 9-1v1l-17 4c-1 0-2 0-2 1h-4c-1 0-3 1-4 1h0v-1c-1 0-1 0-2 1h0l-1-1v-1-1c0-1 0-1 1-2s3-4 3-5v1l-1-1h0l1-1h-1l-1 1h-1l1-1v-1h0c1-1 2-1 2-2l1-1v1c1-1 1-1 2-1z" class="K"></path><path d="M254 271c2 0 1 0 2-1h3 2c1-1 1-1 2-1h1 0c1 1 2 1 2 1 0 1 0 1-1 1 0 1-1 1-2 2v1l2 1c0 1-1 1-1 2v2h-1c-2 0-3 1-4 1h-4l-1 1h-1c0-1 0-1-1-1-2 1-5 2-8 2-1 0-2-1-3 0l-1-1h-2c-3-2-7-2-10-4h4 5v-1h0-1c-2-1-3-1-5-3 4 1 7 2 10 2s6-1 9-2c2 0 3-1 4-2z" class="I"></path><path d="M259 276v-1h-3l1-1c2 0 3 0 5-1v-1c-2 1-3 1-5 1v-1h4c1 0 2-1 4-1 0 1-1 1-2 2h-1c1 2 1 2 1 3s0 1-1 1-1-1-2-1h-1z" class="C"></path><path d="M237 277c2 1 5 0 7-1h4c2-1 3-2 5-2h1 0l-2 1h-1c-1 1-3 2-4 2l-2 2h-1c-2 1-4 2-6 2-3-2-7-2-10-4h4 5z" class="R"></path><path d="M259 276h1c1 0 1 1 2 1s1 0 1-1 0-1-1-3h1v1l2 1c0 1-1 1-1 2v2h-1c-2 0-3 1-4 1h-4l-1 1h-1c0-1 0-1-1-1-2 1-5 2-8 2-1 0-2-1-3 0l-1-1h-2c2 0 4-1 6-2h1l2-2c1 0 3-1 4-2h1v1h4 3z" class="D"></path><path d="M244 279h1l2-2c1 0 3-1 4-2h1v1h4c-4 1-8 3-12 3z" class="L"></path><path d="M334 211c4-1 6-3 8-5 0 2 0 2-1 3-2 1-4 3-5 3-3 2-6 1-8 4v1h-1c1 1 1 1 2 1-1 1-2 2-3 2l-1 1v1c-1 0-1 0-1 1l-1 1-1 2s-1 1-1 2l-1 2v2 1h0 0v2h-1 0-1 0v2h-1v1h0-1v3l-1-1v1h0c0-1 0-1-1-1 0-1 0-1-1-2v1-3c0-1 0-1-1-2v1-1h-1 0c1 1 0 2 0 3h0v-2-1c0-2 1-5 1-7s2-6 3-7c0 1-1 3-1 4v3c1-2 2-3 2-5l1-3c2-1 3-3 5-4 2-2 4-4 7-5 2 0 4 0 5 1z" class="B"></path><path d="M334 211c4-1 6-3 8-5 0 2 0 2-1 3-2 1-4 3-5 3-3 2-6 1-8 4v1h-1c-3 0-5 3-6 4h-1c1-1 3-2 4-3 2-2 4-6 7-6 2-1 2-1 3-1z" class="R"></path><path d="M233 381h4 2 8c2 0 4 0 6 1h4 0l-2 1c-1 0-2 0-3-1h-9c-1 1-2 0-4 1-1 0-3 0-5 1h0-2 0c-1 0-2 1-4 1l-1 1h-2c0 1-1 1-1 1l-9 4s0 1-1 1c-1 1-2 1-3 2l-1 1c-2 1-5 4-6 6-1 0-1 0-1 1-1 2-3 3-4 5 0 1-1 3-2 4s-2 4-3 5l-2 4v1 1 1c-1 0-1-1-1-2l-1-7s-1-1 0-2c0-1 3-4 4-6 9-13 23-22 39-25z" class="B"></path><path d="M359 203h8-3c1 1 7 3 8 4-1 0-4-1-5 0l4 2h0-3c0 1 0 1-1 2-1 0-1-1-2-1v1h1c0 1 1 1 2 2-1 1-2 0-3 0l1 1c2 1 2 1 3 3h-1-4l1 1c-1 0-2 0-4-1h-5-3v1h1c-1 1-1 1-2 1l-1 1c-2-1-1 0-3-1h-2v-1h2 3v-1h-4v-1h1l-2-2h3c-1-1-2-1-3-1v-1c1 0 2 0 2-1s-1 0-2-1h1v-3c1-2 2-3 3-4 0 1 1 1 1 2 2 0 4-1 5-1s2-1 3-1z" class="E"></path><path d="M354 213h1 5c-1 1-2 1-4 2 2 1 3 1 5 1 1 0 1 0 2 1h1l1 1c-1 0-2 0-4-1h-5c-1-1-3-1-5-2l1-1 2-1z" class="G"></path><path d="M350 203c0 1 1 1 1 2v1h3-2v2h5v1h-7c1 1 1 1 2 1h2v1h0-3c0 1 0 1 1 1s1 0 2 1l-2 1-1 1c2 1 4 1 5 2h-3v1h1c-1 1-1 1-2 1l-1 1c-2-1-1 0-3-1h-2v-1h2 3v-1h-4v-1h1l-2-2h3c-1-1-2-1-3-1v-1c1 0 2 0 2-1s-1 0-2-1h1v-3c1-2 2-3 3-4z" class="Q"></path><path d="M350 203c0 1 1 1 1 2v1h3-2l-2 2h0c-1 1-2 2-3 2v-3c1-2 2-3 3-4z" class="N"></path><path d="M263 206c2 2 4 5 6 8 0 0 1 1 1 2l1 1c0 1 0 1 1 2 0 1 1 3 1 4v1c1 0 1 1 1 2v1 1c0 1 1 1 1 1v2h0c-1-1 0-1-1-1 0-1-1-2-1-3v1 1h0 0c0 2 0 2-2 3 1 1 1 1 1 2h0-2v1 1l-2-3c-1 0-1 0-1-1-2 0-2-1-4-1l-1 1h0l3 3h-1c-1-1-2 0-4 0s-4 0-6 1h-1 0-1c-1 0-2 0-3 1h0-1c1-2 2-2 3-3 2-1 4-3 4-4 1-1 1-1 2-1 1-1 2-1 3-1h1c0-1 1-1 1-2l1-1v-1c-1-1-1-2-1-2v-1l-1-1h2v-1h-2v-1h2l-1-1v-2h-1v-1c1 0 1 0 2-1-2 0-3 0-4-1h2v-1h-6c2-1 5-1 7-2v-1h-3 0c1-1 3 0 4-2z" class="P"></path><path d="M262 221c1 0 2 0 2 1v1c-1 0-1 0-1 1h1c0 1-1 1-1 2v1s1 0 1 1-1 1-1 1c-1 0-2 1-2 1h0 0c-1-1-2 0-4-1 1-1 2-1 3-1h1c0-1 1-1 1-2l1-1v-1c-1-1-1-2-1-2v-1z" class="O"></path><path d="M267 232c0-1-2-2-3-3 2 0 4 0 5 1s1 2 2 2h0c1 1 1 1 1 2h0-2v1 1l-2-3c-1 0-1 0-1-1z" class="F"></path><path d="M255 230c1-1 1-1 2-1 2 1 3 0 4 1h0c-1 1-3 3-5 4h-4-1c2-1 4-3 4-4z" class="E"></path><path d="M269 214s1 1 1 2l1 1c0 1 0 1 1 2 0 1 1 3 1 4v1c1 0 1 1 1 2v1 1c0 1 1 1 1 1v2h0c-1-1 0-1-1-1 0-1-1-2-1-3v1 1h0 0c0 2 0 2-2 3h0c-1 0-1-1-2-2 0-1-1-2-1-2 0-1 2-1 3-1h-1c-1-1-1-1-1-2h0 2 1c0-1-1-1-1-2l-2-1h0 1l1-1c-1-1-1-1-3-1v-1h2c-1-1-1-2-2-3l1-2z" class="B"></path><defs><linearGradient id="O" x1="276.866" y1="258.808" x2="270.492" y2="261.947" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#O)" d="M262 258h5c-1 0-2-1-3-2 2 0 6 1 8 2-2-1-3-2-4-3 2 1 6 3 8 3h1l2 2v1c1 1 1 1 1 2v1h1 0l-1 1h3v2 5c3-1 7-1 9 0 0 2 0 2-1 4-2 2-4 4-7 5h-5c-4-2-7-6-8-9-1-2-1-3-1-4 2-1 3-2 5-2v-1h0c-1 0-1 0-3 1v-1h-1v-1l1-1-1-1c-1 0-1 0-1-1h-1 0l-1-1c-2 0-5-1-6-2z"></path><path d="M280 265h3v2 5c0 1 0 3-1 4l-1 1c-1-1-1-2-1-3v-9z" class="B"></path><path d="M275 266l5-1v6c-2-1-6 0-8-1-1 1-1 1-1 2-1-2-1-3-1-4 2-1 3-2 5-2z" class="N"></path><path d="M271 272c0-1 0-1 1-2 2 1 6 0 8 1v3c0 1 0 2 1 3l1-1c1-1 1-3 1-4 3-1 7-1 9 0 0 2 0 2-1 4-2 2-4 4-7 5h-5c-4-2-7-6-8-9z" class="J"></path><path d="M243 206c0-1-1-1-2-1h-1 0l3-2v-1h-4v-1c1 0 2-1 3-2-1-1-3 0-4-1 1 0 3-1 4-2-1-2-3-4-3-6 0-1 0-2 1-2 4 0 11 6 14 9 3 2 6 6 9 9-1 2-3 1-4 2h0 3v1c-2 1-5 1-7 2h6v1h-2v-1h-6 0c1-1 1-1 2-1v-1h-2c-1 1-2 1-3 0h0c-1 0-2 1-3 1h-2 0c-1 1-2 1-3 1 1-1 1-2 2-3h0-2l-1-1 2-1z" class="T"></path><path d="M243 206c2 0 2 0 4-1h2c0 1 0 1 1 2-2 0-4 1-5 0l-1 1h-2l-1-1 2-1z" class="E"></path><path d="M250 207h1-1v-1l-1-1h1c2 1 3 0 4 1-1 1 0 2-1 2-1 1-1 1-2 1h-1c-1 0-2 1-3 1h-2 0c-1 1-2 1-3 1 1-1 1-2 2-3h0l1-1c1 1 3 0 5 0z" class="J"></path><path d="M244 208h1 1 5v1h0-1c-1 0-2 1-3 1h-2 0c-1 1-2 1-3 1 1-1 1-2 2-3z" class="G"></path><path d="M254 197c3 2 6 6 9 9-1 2-3 1-4 2h0 3v1c-2 1-5 1-7 2h6v1h-2v-1h-6 0c1-1 1-1 2-1v-1h-2c-1 1-2 1-3 0h0 1c1 0 1 0 2-1 1 0 0-1 1-2-1-1-2 0-4-1l5-2h-5c1-1 3-1 4-1h0v-1h-1c-1-1-2-2-2-3 1 0 2 0 3-1h0z" class="E"></path><path d="M295 233v-1h0c-1 2 0 4-1 6h0c0-1-1-4 0-5v-3h-1 0v1 1 1 6 1h0-1v-2c0-2 1-4 0-6v1h-1-1-1-1 0c0 1 0 1-1 1 0-1-1-1-1-1-1-2-2-3-2-5h0c0-1-1-1-1-1v-1-1c-1 0-1 0-1-1v-1c0-1-1-1-1-2h0v-1l-4-3c-2-1-4-3-5-4-2-2-4-5-6-6l-4-4-7-6c-1-1-2-3-3-3-2-2-4-3-7-5-1 0-4-1-5-2v-1h1c1 1 1 1 2 1 2 1 3 1 5 2 7 5 13 12 20 17 1 1 3 3 5 4 1 1 3 1 4 2l2 1 1-1s-1-1-1-2h1l1 1c1-1 1-1 1-3 1 1 2 1 4 2h3 1c1 1 1 3 1 5l2 2 3 10c-1 1 0 2 0 3v3h-1z" class="C"></path><path d="M279 214l5 5v2h1c0-1 0-1 1-2v3l-1 1v1c-1 0 1 5 1 7h0c-2-4-3-8-4-11l-3-6z" class="S"></path><path d="M277 212l2 1 1-1s-1-1-1-2h1l1 1c1-1 1-1 1-3 1 1 2 1 4 2h3c1 1 1 1 1 3h0c1 1 1 2 1 2 1 2 0 3 1 5l2 8c-3-3-4-9-5-12 0 1 0 4 1 5l-1 1h0c-1-2-1-6-2-7v8c1 2 1 4 1 5v2c-1-2-2-5-2-8v-3c-1 1-1 1-1 2h-1v-2l-5-5h0l-2-1v-1z" class="L"></path><defs><linearGradient id="P" x1="269.97" y1="281.023" x2="299.817" y2="297.093" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#656465"></stop></linearGradient></defs><path fill="url(#P)" d="M285 282c2 0 5-1 7-2l1 1v1h0c-1 0-1 0-2 1h1c2 0 5-1 7 0h0c1-1 2-1 3-1l2 2-1 1v1c-2 0-2 0-3 1 0 3 0 6 1 9v1h-1v-1h-2c-1 2-1 5-1 6-1 0-1 0-2-1h-3-1c-1-1-2-1-3-1-1-1-2-1-4 0-3-1-7-2-10-2-1-1-2-1-3-2 4 0 8 1 12 1-2-1-4-1-5-1-2-1-4-1-6-1h0v-1h-2-1l-1-2c-1-1-2-1-3-1h5 1c-1 0-1-1-2-1h-1c1-1 1-2 1-3v-1h-2c0-1 0-1 1-2s3-2 5-2h2v-1c2 0 3 1 4 1 2 1 4 1 6 0z"></path><path d="M274 292l1-2v-2h3v1c-1 0-1 0-2 1h0l1 1 1 1c1 1 2 1 3 2l-7-2z" class="K"></path><path d="M300 296h0c-1-1-1-1-1-2 0 0-1-1-1-2h0c0-1-1-3 0-4 0-1 1-2 2-3h3v1c-2 0-2 0-3 1 0 3 0 6 1 9v1h-1v-1z" class="S"></path><path d="M285 282c2 0 5-1 7-2l1 1v1h0c-1 0-1 0-2 1h1c2 0 5-1 7 0l-2 2h-4c-4 0-8-1-11-1h0-4c0-1-2-2-3-2v-1c2 0 3 1 4 1 2 1 4 1 6 0z" class="D"></path><defs><linearGradient id="Q" x1="280.055" y1="289.825" x2="281.007" y2="302.084" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#Q)" d="M265 291h5 1l3 1 7 2c3 1 6 2 8 4 0 0 1 1 2 1h2l2 2h-3-1c-1-1-2-1-3-1-1-1-2-1-4 0-3-1-7-2-10-2-1-1-2-1-3-2 4 0 8 1 12 1-2-1-4-1-5-1-2-1-4-1-6-1h0v-1h-2-1l-1-2c-1-1-2-1-3-1z"></path><defs><linearGradient id="R" x1="242.727" y1="341.002" x2="225.938" y2="361.347" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#R)" d="M227 333h1c0 1 0 1 1 2h2c1 1 1 2 2 3h2 0l-1-1h0l2-2c0 1 0 1 1 1v1c1 1 1 2 2 3h1v1 1h1s0 1 1 1 1 0 1 1c1 0 1 0 1 1h1s0 1 1 1v1c1 2 2 3 2 4 1 1 1 1 1 2 1 0 2 1 2 1v1c1 0 2-1 3 0h1 3c1 1 1 1 2 1s2 0 3 1c-2-1-9-3-11-1h-1l-1 1c-7-1-13 0-19 0h0l-1-1h-3l-8 2v-1c0-1-1-1-1-2h2l2-2c1 0 2 0 4-1h-1-5-1-3l-11 1v-1-1c3-1 7-1 9 0v-1h2c1-1 3-1 4-1l-1-1h-1v-1l-3-1c-2 0-2 0-4-1h0l3-2h3l2 1h0c0-1 0-1-1-1s-3-1-4-2l1-1 1 1c0-1 1-1 1-1h4c0 1 1 1 1 1v-1c-1 0-1 0-2-1l1-1h1c1-1 2-1 3-1v-1h2 1c-1-1-2-1-2-2l1-1z"></path><path d="M222 340c-1 0-1 0-2-1l1-1h1c1-1 2-1 3-1l2 2-1 1c-1 0-1 0-2-1-1 0-1 0-1-1l-1 1h1v1h-1z" class="I"></path><path d="M228 336l2 2h1c0 1 1 2 1 2 0 1-1 1-2 2l-2-2c0-1-1-1-1-1l-2-2v-1h2 1z" class="C"></path><path d="M227 356c8-2 16-2 24 0l-1 1c-7-1-13 0-19 0h0l-1-1h-3z" class="Q"></path><path d="M218 348v-1l-3-1c-2 0-2 0-4-1h0l3-2h3l-1 1s0 1 1 1 1 0 2 1h2c1 0 1 1 2 1s2 1 3 1v1c-2 0-4 1-6 1-1 0-3 0-4 1h-2v-1h2c1-1 3-1 4-1l-1-1h-1zm-30 42l2-1c5-4 11-8 16-11 1 0 2 0 3-1 2-1 4-2 5-2 5-2 9-2 14-3 2-1 4-1 6-2 9-1 18 0 27 2h4c3 1 7 2 10 4 2 1 3 2 5 2 4 2 8 4 11 6 5 1 11 7 14 10h0-1l-1-1v1l-1-1s1 2 2 2h0c3 1 4 3 6 4 0 1 1 2 2 3 2 1 3 3 5 5-1 0-1-1-2-1-1-2-2-4-4-5s-4-3-6-4c-1 0-2-1-3-2h0l-1-2h-1c-3-2-5-4-8-5-1-1-2-1-3-1l-1-1h-1c-1-1-1-1-2-1h0l-6-3c-3-2-7-3-10-4-18-6-37-5-54 2-2 1-3 2-5 3-8 5-15 10-22 17l1-4c-1-1-1-2-1-3s0-1 1-2l-1-1z" class="D"></path><path d="M188 390l2-1c5-4 11-8 16-11 1 0 2 0 3-1 2-1 4-2 5-2 5-2 9-2 14-3 2-1 4-1 6-2 9-1 18 0 27 2h-1c-7 0-14-1-21-1-3 0-5 1-7 2-2 0-5 0-8 1-3 0-6 2-10 3-2 1-5 1-8 2-6 4-12 8-17 12l-1-1z" class="P"></path><path d="M188 393c1 0 1 0 2-1l9-6h0c1 0 1-1 2-2l2-1h1c2-1 3-2 5-2v-1h2 1l1-1h1l6-2h1 1c1-1 2-1 3-1 2-1 5-1 7-2h3 1c4-1 9-1 12 0h7c2 1 5 0 7 1h1c3 1 7 2 10 3s6 3 9 4c2 1 4 0 6 1l3 1h0c5 1 11 7 14 10h0-1l-1-1v1l-1-1s1 2 2 2h0c3 1 4 3 6 4 0 1 1 2 2 3 2 1 3 3 5 5-1 0-1-1-2-1-1-2-2-4-4-5s-4-3-6-4c-1 0-2-1-3-2h0l-1-2h-1c-3-2-5-4-8-5-1-1-2-1-3-1l-1-1h-1c-1-1-1-1-2-1h0l-6-3c-3-2-7-3-10-4-18-6-37-5-54 2-2 1-3 2-5 3-8 5-15 10-22 17l1-4c-1-1-1-2-1-3z" class="H"></path><path d="M373 383c0-1-1-1 0-1s3 2 4 3c0 2 1 3 2 4 1 3 4 5 5 8l1 1c0 1 0 1 1 2h-1l-1-1v1c1 1 3 2 4 4v-1c1 2 3 3 4 5h-1l-2-2h0-1v1c0 1 0 1 1 2 1 2 4 7 4 9h-1c-1-1-2-4-3-6 0 2 1 4 2 6l-1 1v1h0c0 2 1 4 2 6l-1 1c-1-3-2-6-4-9-1 4 1 8 2 12-2-2-6-11-8-13h0c1 3 2 7 3 10-2-3-4-6-5-10v-3 2h-1l4 13c-1 0-1 1-1 1-1-1-1-3-3-4 0-1 0-1-1-1 0-2-1-4-2-5 0-4-2-6-3-9l-6-12v1l4 11c1 1 1 2 2 3 1 2 2 3 3 5v1c1 3 1 8 2 11-1-1-2-3-2-4h0 0l1-1c-1-1 0-2-1-3v-2c-1-1-1-1-1-2s-1-1-1-2c-3-3-3-7-5-10 0 0 0-1-1-1 0-1 0-2-1-3v-1c0-1-1-1-1-2v-1-1h-1v-3c0-1 0-1-1-1h0v-1l1 1h0v-2 1l1 1v1c1 1 1 2 1 3h0l1 1v1l2 4c0 1 0 1 1 1 0 1 1 2 1 3h0c0 1 0 2 1 2v1c1 1 1 3 2 5 1 0 1 0 1 1l1 3v1c1 1 1 3 2 3l2 2h0 0v-1c-1-2-1-3-2-5 0-1 0-2-1-2v-1h-1s0-2-1-2c0-2-2-4-3-5v-1c0-1 0-2-1-2v-1c0-1-1-2-1-3v-1c-1-1-1-2-1-3 0 0-1 0-1-1v-1c0-1-1-2-1-2v-3h0c0-1 0-1 1-1h0v1l1 1v2c1 0 1 1 1 2 0 0 1 1 1 2v1l1 1v1h0l1 1v-2h-1v-1l-1-1-1-3v-1c0-1 0-2-1-2v-3h0c1 0 1 0 1-1v-2h0l1-1h0c0 2 2 4 2 5 1 2 2 3 3 5h0v-2l-1-2c0-1-1-2-2-3v-1c0-1-1-1-2-2h0v-1h0l1-1h-1l1-1v1l1-1z" class="C"></path><path d="M372 384h-1l1-1v1l1-1h0c1 3 6 6 5 10h0c-1 0-1 0-1-1-1-2-4-7-5-8z" class="H"></path><path d="M371 388c3 6 7 13 8 19h0l-1 1c-1-4-2-8-4-12-1-3-3-5-3-8zm-3 8h1l3 6 3 8 1 3h1l-1 1v1c-3-5-4-9-6-13 0-2-1-4-2-6zm2-4c2 1 6 13 7 16 1 1 1 2 1 4v-1h0c0-1 0-1-1-2v1h0l-5-11c-1-3-2-5-2-7z" fill="#131313"></path><path d="M272 266c2-1 2-1 3-1h0v1c-2 0-3 1-5 2 0 1 0 2 1 4 1 3 4 7 8 9h5l1 1c-2 1-4 1-6 0-1 0-2-1-4-1v1h-2c-2 0-4 1-5 2s-1 1-1 2h2v1c0 1 0 2-1 3h1c1 0 1 1 2 1h-1-5c1 0 2 0 3 1l1 2h1 2v1h0c2 0 4 0 6 1 1 0 3 0 5 1-4 0-8-1-12-1h-1c-7-2-13-2-20-2-3 0-7 2-9 1h-6l-1 1c-1 0-2 0-3-1v-1c1 0 1 0 2-1h-1l-1-1c-1 0-1 0-2-1h1c1 0 2 0 3-1h0-4l1-1 2-1h5v-1h-3v-1h2c-3 0-6-1-9-2h7c-2-1-3 0-5-1l1-1h11c1-1 2 0 3 0 3 0 6-1 8-2 1 0 1 0 1 1h1l1-1h4c1 0 2-1 4-1h1v-2c0-1 1-1 1-2l-2-1v-1c1-1 2-1 2-2 1 0 1 0 1-1 0 0-1 0-2-1h0 3 1l4-3z" class="B"></path><path d="M263 286h1v1c-1 1-2 1-3 2h-1l3-3z" class="F"></path><path d="M265 291c0-1 0-1-1-1l2-2h1v-1h-2 0l1-1c0-1 1-1 2-2-1 1-1 1-1 2h2v1c0 1 0 2-1 3h1c1 0 1 1 2 1h-1-5z" class="C"></path><path d="M233 290c3 0 5 0 7-1l9-2-6 4c-4 0-8 1-12 1-1 0-1 0-2-1h1c1 0 2 0 3-1h0z" class="I"></path><path d="M237 288l3-1h2 2c0-1 0-1 1-1h1 1c1-1 2-1 2-1 5-1 10-6 14-5h1l-1 1-1 1c-1 1-2 2-3 2v-1h1l-1-1c-2 0-4 2-5 3l-4 1c-1 0-1 0-2 1h-1-2s0 1-1 1h-1 0l-4 1h-1-5c-1 0-1-1-1-1h5z" class="D"></path><path d="M241 282c1-1 2 0 3 0 3 0 6-1 8-2 1 0 1 0 1 1l-7 2c-2 0-4 1-5 1l11-1-15 4h-3v-1h2c-3 0-6-1-9-2h7c-2-1-3 0-5-1l1-1h11z" class="R"></path><path d="M231 292c4 0 8-1 12-1h3 4 0c0 1-1 1-2 1v1h12c4 0 8 1 12 2h0c2 0 4 0 6 1 1 0 3 0 5 1-4 0-8-1-12-1h-1c-7-2-13-2-20-2-3 0-7 2-9 1h-6l-1 1c-1 0-2 0-3-1v-1c1 0 1 0 2-1h-1l-1-1z" class="I"></path><path d="M231 292c4 0 8-1 12-1h3c-2 1-4 2-6 2h-4c-1 1-4 1-5 1 1 0 1 0 2-1h-1l-1-1z" class="D"></path><defs><linearGradient id="S" x1="305.677" y1="239.02" x2="297.782" y2="228.955" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#6e6d6d"></stop></linearGradient></defs><path fill="url(#S)" d="M290 210l1-1 2 2v-2h7 1 5l23 1c-3 1-5 3-7 5-2 1-3 3-5 4l-1 3c0 2-1 3-2 5v-3c0-1 1-3 1-4-1 1-3 5-3 7s-1 5-1 7v1l-1 1h0v2l-1 1v1h0c0-3 0-3-1-5h0v-1 2h-1v-2c-1 1 0 2-1 2 0 2-1 4-3 5 0 1-1 1-2 1h-1-1c-1 0-1 0-1-1h-1v-2c0-1-1-1-1-2 0 3 0 6-1 8h0c0-3-1-7 0-10v-2h1v-3c0-1-1-2 0-3l-3-10-2-2c0-2 0-4-1-5z"></path><path d="M301 209h5c-1 2-1 3-1 5l1 1c-1 2-1 4-1 6h1v1h-1v-1l-1 1c0 1 1 2 0 2h-1 0v-6h1l-1-1c-1-2 1-5 0-7h-1c0 1 0 3-1 4v-1-4z" class="P"></path><path d="M290 210l1-1 2 2v-2h7c-1 2 0 4-1 6h0c0 1 0 1-1 2h0v-3c-1 1-1 1-1 2-1 1-1 2-1 4 1 1 1 2 1 3h-1c1 2 2 5 2 6v6c0 1 0 2-1 4h0c0-1-1-1-1-2 0 3 0 6-1 8h0c0-3-1-7 0-10v-2h1v-3c0-1-1-2 0-3l-3-10-2-2c0-2 0-4-1-5z" class="N"></path><path d="M296 227c0-1-2-7-2-9 0-1 1-5 1-6 1 3 0 5 0 7 0 1 1 3 1 4 1 2 2 5 2 6v6c0 1 0 2-1 4h0c0-1-1-1-1-2 0 3 0 6-1 8h0c0-3-1-7 0-10v-2h1v-3c0-1-1-2 0-3z" class="D"></path><path d="M306 209l23 1c-3 1-5 3-7 5-2 1-3 3-5 4 0-3 6-5 7-8-1 1-2 1-2 2l-3 3c-1 1-3 3-4 3s-1 0-1-1h0c-1 0-3 4-3 5v1h-1c1-2 1-4 2-6h0c-1 1-2 2-2 3-1 1-1 2-2 3v-1c1 0 1-1 1-1 0-3 2-6 3-8 0-1 1-2 1-3h-1c0 1-1 3-1 4-1 1-2 4-3 5h0v2h-1c0-1-1-2-1-3v-2c1-1 1-2 1-4 0-1 1-2 1-3h-1l-1 5-1-1c0-2 0-3 1-5z" class="Q"></path></svg>