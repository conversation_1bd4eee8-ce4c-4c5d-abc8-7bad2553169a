<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="59 50 469 624"><!--oldViewBox="0 0 564 752"--><style>.B{fill:#141414}.C{fill:#2e2d2d}.D{fill:#e4e3e4}.E{fill:#d3d2d3}.F{fill:#1f1e1f}.G{fill:#e8e7e8}.H{fill:#c6c5c5}.I{fill:#d2d1d3}.J{fill:#d9d7d8}.K{fill:#181819}.L{fill:#121112}.M{fill:#dddbdd}.N{fill:#c2c1c1}.O{fill:#f0eff0}.P{fill:#242323}.Q{fill:#3b3b3c}.R{fill:#edecec}.S{fill:#0f0e0e}.T{fill:#f5f4f5}.U{fill:#b1b0b0}.V{fill:#b8b7b8}.W{fill:#7e7c7d}.X{fill:#a2a1a2}.Y{fill:#606060}.Z{fill:#bcbabc}.a{fill:#fdfcfd}.b{fill:#5f5e5f}.c{fill:#666466}.d{fill:#999898}.e{fill:#858485}.f{fill:#343435}.g{fill:#929192}.h{fill:#4e4d4d}.i{fill:#444}</style><path d="M425 475c0-1 0-1 1-2h1v2s0 1-1 1l-1-1z" class="H"></path><path d="M200 423l1-2h1v1c0 1 0 2-1 3l-1-2z" class="I"></path><path d="M371 515h2v-1l1-1 1 1c0 1 0 2 1 3-2-2-3-2-5-2z" class="B"></path><path d="M415 467l1 1c1 0 2 1 3 1h1-1c-1 0-2 1-3 1l-1-1v-2z" class="F"></path><path d="M370 558c1-1 1-3 3-4v1 1l1 1c-1 0-2 1-4 1z" class="K"></path><path d="M419 458c1 0 1 1 3 1-1 0-1 1-1 1l-1 1-1 1c-1-2 0-3 0-4z" class="B"></path><path d="M175 499l4 1h1l-1 1c-2-1-2 0-4 0v-2z" class="U"></path><path d="M196 419v-1h1c0 1 1 1 1 2s0 1-1 3c0-1-1-1-1-1v-1-2z" class="Z"></path><path d="M420 318v-4c1 0 2-1 2-1v1 3h-2v1z" class="F"></path><path d="M173 468v1h1c0 1-1 2-2 3l-1 1-1-1c1-1 1-2 2-2l1-2z" class="V"></path><path d="M353 517h2v-1c-1 0-1 0-2-1v-1c1 0 1 1 2 1l1 2v2c-1 0-2-1-3-2z" class="d"></path><path d="M394 572l1 1v4h0c-1 0-1-1-2-2v-1-1l1-1z" class="J"></path><path d="M394 572l1 1v1h-2v-1l1-1zm3-62c0-1 0-1 1-2l1 1h2 0c-1 1-2 3-3 3 0 0 0-1-1-1h0v-1z" class="M"></path><path d="M392 325h1v1 1c-1 1-1 2-1 3h-1-1c0-2 1-4 2-5z" class="I"></path><path d="M402 325h2c0 1-1 3-1 4h-2c1-2 0-3 0-4h1z" class="E"></path><path d="M507 272c2 2 1 5 1 7 0 0 0 1-1 1l-1-1c1-2 1-4 1-6v-1z" class="e"></path><path d="M384 334c-1-1-1-2 0-3 1 0 1 0 2 1 1 0 1 1 1 1h-1v1h1v1h-1l-2-1h0z" class="c"></path><path d="M449 418c0-1 0-1 1-2l1 1c0 2 0 3-1 4-1 0-1 0-2 1l1-4z" class="M"></path><path d="M381 326l2-1c1-1 1-1 2-1v1h2l1 1c-1 0-1 1-2 1h0-2c-1-1-1-1-3-1z" class="B"></path><path d="M408 389h1v1h1v1h0c-1 0-1 0-2 1h1v1c-1 0-1 1-2 0v-2c0-1 0-1 1-1v-1h0z" class="W"></path><path d="M437 443c1-1 2-2 2-3v-1h0 1l2 1s0 1-1 1v1h1l-1 1h-1-1-2z" class="J"></path><path d="M355 520c-1 1-1 0-2 0l-1-1h0l-1 1h0-1v-1c0-1 1-1 1-2 1 0 1 0 1 1h1v-1c1 1 2 2 3 2l-1 1z" class="b"></path><path d="M170 512h4l1 1v2h-1l-1-1v1h-1c-1 0-1-1-1-1h0l-1-2z" class="N"></path><path d="M409 389h1v-2c1 1 1 2 3 3h1s0 1-1 1-3 1-4 1h-1c1-1 1-1 2-1h0v-1h-1v-1z" class="U"></path><path d="M415 310v1h1v-2h0 1l1 1h0c0 1-1 1-1 2s-1 2-1 3v1h-1c0-2-1-5 0-6z" class="e"></path><path d="M377 300l1 1c1 1 1 2 3 2-1 1-1 1-2 1 0 2 0 3-1 4h0c0-2-1-6-1-8z" class="M"></path><path d="M170 599l2 2c0-1 1-1 1-2 0 1 0 2 1 3-2 1-3 1-4 2-1-2-1-3 0-5z" class="E"></path><path d="M163 505v8h1c1 1 1 1 1 2h0c-1 2-1 4-1 5h-1v-2-1c-1-3-1-8 0-12z" class="X"></path><path d="M422 317h0v7c-1-1-2-2-2-3-1-1 0-2 0-3v-1h2z" class="P"></path><path d="M168 498c3 0 5 0 7 1v2h-3c-1 0-1-1-2-2h0l-2-1z" class="E"></path><path d="M349 435v-1c-1 0-1 0-2-1 0-1 0-1-1-2h0c0-1 0-1 1-2 0 1 0 1 1 2 0 1 1 1 1 1h1v-1c1-1 2-1 3-1-1 1-2 3-4 5z" class="K"></path><path d="M418 481l1-1c1 1 2 1 2 2 0 2-1 4-2 6 0-2 0-3 1-4h-1-1 0l-1-1 1-2z" class="V"></path><path d="M373 430h1c1 1 3 2 4 3l-1 2h-1v-2-1l-1 1c-1 0-2-1-3-1v-1l1-1zm0-128h0 1v-4c0 4 1 9 1 13l-1 1c-1-3-1-8-1-10z" class="W"></path><path d="M392 325c1-1 3 0 4 0l-1 4h-2v-3-1h-1z" class="C"></path><path d="M412 289c1 0 2 0 3 1-1 3 0 5-1 9 0-1 0-1-1-1 0 0 1-5 0-6l-1-3z" class="g"></path><path d="M376 532h0l1-1c2 1 2 1 3 2v1c-1 1-4 1-5 1h-1l2-3z" class="e"></path><path d="M387 499c1-1 1-1 2-1 1 1 1 2 3 2v1c-1 1-1 1-1 2h1c-1 0-1 1-2 2l-1-1c1 0 1-1 2-2-1-1-2-1-3-1v-1l-1-1z" class="P"></path><path d="M422 313v-2-2c1-1 2-3 4-4l1 1c-1 1-1 2-2 2h-1l-1 1c0 1 1 1 1 2s0 1-1 2l1 1h-2v-1z" class="g"></path><path d="M439 435c-1 1-1 2-3 2-1 1-4 1-5 0h-1-3l12-2z" class="B"></path><path d="M392 587c0 1 1 2 1 3h-1v1h-2 0l-1 1h-1l-1-1c-1 1 0 1-2 1h0c1-1 1-1 2-1 2-2 3-3 5-4z" class="H"></path><path d="M360 421h-2-1c-2-1-2 0-3-1v-1l1-1h1v2h1c1-1 1-1 1-2l1 1h0 2 1c-1 1-1 2-2 2z" class="F"></path><path d="M419 464c1 0 1 0 2 1v2l2-2h1s0 1-1 1v2l-1 1c-1-1-1-1-2-1s-1 1-1 1c-1 0-2-1-3-1 1-1 2 0 2-1 1 0 2-1 2-1 0-1-1-1-1-2z" class="M"></path><path d="M443 441v1c-1 2-3 3-3 5l1 1v2 1c-1-1-2-2-2-3h-1c0-1 0-1 1-2 0-1 1-2 2-3l1-1 1-1z" class="D"></path><path d="M168 600c-1-2-1-7 0-9 0 0 1 0 1-1l1 1c-1 1-1 1-1 2 2 1 4 2 5 3h-4l-1-1h-1v5z" class="g"></path><path d="M411 465c-1 0-1 0-2-1l2-1-1-1-1 2c-1 0-2-1-3-1l1-1 4-1c1 0 2 0 2 1-1 1-2 2-2 3z" class="M"></path><path d="M200 413h2 0l-1 2 1 1h0c1 1 1 2 1 3h0l-1 1v1h-1l-1 2v-4-1-1h0v-2-2z" class="E"></path><path d="M382 575c1-1 3-1 4-1v1c-1 1-2 1-3 1-2 0-3 1-4 1-2 1-5 0-7 0 2-1 4-1 6-1 1 0 1-1 1-1h3z" class="J"></path><path d="M411 312c0 1-1 1-2 1 0-1-1-1-1-2s0-3 1-4l1 1c1 0 1-1 2-1v1 1c-1 0-2 0-2 2h2 0c0 1 0 1-1 1z" class="V"></path><path d="M390 505c1-1 1-2 2-2h0l1 1h0v3-1h1l1 2-1 1c-2 0-5-2-6-3l2-1z" class="J"></path><path d="M168 600v-5h1l1 1h0c1 1 2 1 3 2v1c0 1-1 1-1 2l-2-2h-1c0 1 1 1-1 2v-1z" class="d"></path><path d="M368 333c1 0 1-1 1-1h1v1c-2 0-3 2-4 3v3c-1 1 0 1-1 2v1c-1-2 0-9 0-10 1 0 2 1 3 1z" class="I"></path><path d="M173 482v1 1c1 0 1-1 2-1h1l-1 1v1c2 1 4 1 5 2h3v1c-3 1-9-2-12-3 0-2 1-2 2-3z" class="D"></path><path d="M414 325c3 2 4 4 5 7h-1c-2-1-1-2-3-4h-2c-1 0-1-1-1-1v-2h2z" class="I"></path><path d="M380 413c1 0 1-1 2-2l1-1h1v1c-1 0-1 0-1 1l-3 3c0 1 1 1 1 1v2h0c1 1 2 1 2 2s0 1-1 1c-1-1-2-3-3-4-1 0-1 0-2-1l3-3z" class="K"></path><path d="M162 548h1v3c0 2 1 1 0 3l1 1h0-1v1 1h0c2 0 2 0 3 1l-2 1h0c-1-1-2-1-2-1v-10z" class="M"></path><path d="M414 445c0-2 0-3 1-4h1v1 1c0 1 1 2 1 3v1c-2 0-4 0-5-1h0-2v-1s1 0 1-1h0 1v1h1 1z" class="U"></path><path d="M386 581l2-1c0 1 1 2 1 3s-1 1-1 1l-7 2h-1v-1h0 2 0v-1-1h1v1h1v-2l2-1h0z" class="Z"></path><path d="M408 443v-3c2 0 4 1 6 0v1l-2 2h0l1 1 1 1h-1-1v-1h-1-2c-1 0-1 0-1-1z" class="b"></path><path d="M423 465h1 2v1 1 1 1 1h-1c-2 1-4 0-5-1h-1s0-1 1-1 1 0 2 1l1-1v-2c1 0 1-1 1-1h-1z" class="I"></path><path d="M459 380c2 0 2 0 3 1h1c0 2 1 2 2 3h0l4 5-1 1c-2-1-4-4-6-6 0-1-3-2-4-3l1-1z" class="X"></path><path d="M372 568h1l2 2c-1 1-2 1-3 1s-2 0-3 1h0-2c0-1-1-1-1-2 2-1 4-2 6-2z" class="i"></path><path d="M405 316l1-1s1-1 3-1c0 0 0 1-1 2 2-2 2-3 4-3 1 2 0 4 0 5-2 0-4-1-6-1 0-1 0-1-1-1z" class="d"></path><path d="M421 610h1c3 0 5 1 7 3 1 2 2 4 2 5v1l-1-1c0-1-1-3-2-4s-2-2-3-2c-1 1-1 1-2 1v-1h0-2l-1-1 1-1z" class="D"></path><path d="M506 279l1 1c1 0 1-1 1-1l-1 7h-1-1c-1 0-1-1-1-2v-4l1 2c1-1 1-2 1-3z" class="c"></path><path d="M504 280l1 2v2h1v-1c1 1 1 2 0 3h0-1c-1 0-1-1-1-2v-4z" class="b"></path><path d="M387 470h0 0c-1 0-1 0-2-1 1 0 1 0 1-1h-1v2c-1 0-1-1-1-1v-1c0-1 1-1 2-1h0 1c0-1 0-1 1-2h3l1 1-1 1-2 2c-1 0-1 0-2 1z" class="Q"></path><path d="M411 487c2-1 4-3 6-4l1 1h0 1 1c-1 1-1 2-1 4-1 0-1 1-2 1 0-1-1-2-1-2h-2l-1 1-1 1-1-2z" class="Z"></path><path d="M418 477c1 1 1 2 2 3v-1c2-1 4-3 4-5l1-1h0v2l1 1h-1v2c0 2-2 3-4 4 0-1-1-1-2-2l-1 1h0v-2-1h0v-1z" class="U"></path><path d="M181 469h1c1 1 1 1 1 2 0 0 1 0 2-1v1c1 2 7 2 9 2h0v1h-5 0l-2-1c-1 1-1 1-3 0-1 0-3-1-4-1 0-1 0-2 1-3z" class="H"></path><path d="M374 447h1c1 0 2 1 2 2 0 0 1 0 1 1l1-2h0c2 0 1 0 1 1l1 1c0 1 0 2-1 2v1l-1-1v-2l-1 1v1h-1c-1 0-2-1-3-2s0-2 0-3z" class="W"></path><path d="M392 439h2 2c1 1 2 2 2 3s0 1-1 2c-1 0-6-2-7-3 1-1 2-1 2-2z" class="Y"></path><path d="M417 290c0 1 0 2 1 3l-1 1v2h2c0 1-1 1-2 1l-1 3c-1-1-1-1-2-1 1-4 0-6 1-9h2z" class="B"></path><path d="M355 515h1c0 1 0 1 1 1 0 0 1-1 2-1s2 0 2 1v3 1h3-5c-1 1-3 1-4 0h0l1-1v-2l-1-2z" class="f"></path><path d="M425 444v-1h2c1 0 2 0 3-1h0 2 0c2-1 2-1 4-1l1-1v1c-1 0-1 1-2 2h0c-3 1-5 0-7 1 1 1 1 1 1 2l-2 2h-2v-4z" class="I"></path><path d="M166 508c1 1 2 1 3 2l1 2 1 2-1 1h-1v-2h0c-1 0-1 1-1 1s0 1-1 1h0c0 1 0 1-1 2v-1h-1v-1c0-1 0-1-1-2l1-1c0-1 1-1 1-2h0v-2h0z" class="M"></path><path d="M165 512h2v1 2h0c0 1 0 1-1 2v-1h-1v-1c0-1 0-1-1-2l1-1z" class="P"></path><path d="M411 465c0-1 1-2 2-3 1 1 2 1 2 2l-1 1h0l2-1h2 1c0 1 1 1 1 2 0 0-1 1-2 1 0 1-1 0-2 1l-1-1h-2l-1-1s-1 0-1-1z" class="I"></path><path d="M350 403c0 1-2 3-3 4h0c-1 0-1 1-2 2v1l2 1v1h0l-1-1c-1 0-1 1-1 1-1 0-2 0-2 1v-1l-2-2 3-3c2-1 3-3 6-4z" class="K"></path><path d="M167 495c1-1 2-1 3 0h0c-1 1-1 1-2 1l-1 1v1h1l2 1h0v2h-1c-2 0-4-1-6 0 0-2 1-3 1-4h1c1-1 1-2 2-2z" class="G"></path><path d="M395 577c1 1 2 2 2 3s0 1-1 1h-2c-1 1-2 1-4 2l-2-3c1-1 2-1 3-1 2-1 3-1 4-2h0z" class="E"></path><path d="M162 558s1 0 2 1h0c1 1 1 1 2 1l-1 2 3 3c-1 0-1 0-1 1h-1c-1 0-1-1-2-1h0v-1l-1 1c0 1 0 1-1 2-1-1 0-7 0-9z" class="N"></path><path d="M164 559h0c1 1 1 1 2 1l-1 2h-1-1c0-1 0-2 1-3z" class="B"></path><path d="M371 410h2 0c0 1 2 3 1 4h-1s-1-1-2-1-2 1-3 3l2 2v1c-2-2-4-1-6-1 1-2 2-3 3-4l4-4zm53 50v-1h1c1 0 1 1 2 1s2-1 3 0l1 1c-1 1-1 1-1 2l-3 3h0v2h-1v-1-1-1h-2c1-1 2 0 3-1 0-1 0-1-1-2 0 0-1 1-2 1h0c-1-1 0-2 0-3h0z" class="P"></path><path d="M424 460v-1h1c1 0 1 1 2 1s2-1 3 0l1 1c-1 1-1 1-1 2l-1-1c-2-1-3-1-5-2h0z" class="I"></path><path d="M371 515c2 0 3 0 5 2v2h1v-2c1 0 1 1 2 1-1 1-2 3-3 3-1-1-1 0-3-1h-2l-1-1c0-1 0-2 1-3v-1z" class="H"></path><path d="M420 469c1 1 3 2 5 1h1v2c-1 0-1 0-1 1h0l-1 1c0 2-2 4-4 5v1c-1-1-1-2-2-3v-1h2 0 1l-1 1h1c1 0 1-1 2-1 0-1 0-2 1-3v-1c-2 0-3 1-4 1h-1l1-4z" class="P"></path><path d="M202 422l1-2h0c1 1 1 3 2 5-1 1-2 1-2 2h-1c2 0 3-1 4 0-1 1-1 1-2 1 1 1 2 2 2 3h0c0 1 1 1 1 2h0c0 1-1 1-1 1-2-2-4-4-4-7 0-1-1-1-1-2 1-1 1-2 1-3z" class="H"></path><path d="M173 468l1-1v-3h0c1-1 2-1 3-2l2 2h-3v2h2v1c-1 1-1 1 0 2l1-2c1 0 1 1 1 1-1 1 0 3-2 3v-1l-1 1-3-2h-1v-1z" class="J"></path><path d="M405 316v-3-3c1-2 1-4 1-5h0l1-1h1v2l1 1c-1 1-1 3-1 4s1 1 1 2c1 0 2 0 2-1l1 1c-2 0-2 1-4 3 1-1 1-2 1-2-2 0-3 1-3 1l-1 1z" class="Q"></path><path d="M163 501c2-1 4 0 6 0h-3v3c0 1-1 1-1 2h2l1 1h-1-2v1h1 0v2h0c0 1-1 1-1 2l-1 1h-1v-8c0-2 1-3 0-4z" class="N"></path><path d="M385 550c1 0 1-1 1-2 0 2 1 6 3 8-1 1-1 1-1 2v1 1c-1-1-1-2-2-3-2 1-3 0-4 1h-1 0c-1 0-1-1-2-1 0 0 0-1-1-1 2-1 3-1 5-1l1 1h0c1 0 2 0 2-1s-1-2-1-2l-1-1c1 0 1 0 1-2z" class="Z"></path><path d="M194 418l6-7c1-2 1-3 3-4v3s-1 0 0 1h0c1 2 0 3-1 5l-1-1 1-2h0-2 0c-1 1-2 2-2 3l1 1c-1 1-2 0-3 1v1c-1-1-1-1-2-1z" class="N"></path><path d="M378 556c1 0 1 1 1 1 1 0 1 1 2 1-1 1-2 1-3 1s0-1 0-1h-1v1h-1v1c-1 0-5 1-6 1v-1h0l-2 1-1-1h0l3-2c2 0 3-1 4-1 1-1 3-1 4-1z" class="U"></path><path d="M187 455h0v-2l-1-1 1-1v1h0c1-1 2-1 3 0 1 0 1 0 1 1-1 1-2 1-2 2h1s1 0 1-1h2v1c0 1-1 1-1 2 2 0 3 1 5 2s3 2 5 3c-4-1-7-2-10-4-2-1-4-2-5-3z" class="Z"></path><path d="M416 443c1 1 2 1 3 0h2l1 1c1-1 1-1 2-1l1 1v4c-2 0-5 0-7-1h-1v-1c0-1-1-2-1-3z" class="N"></path><path d="M422 444v2s1 0 1 1h-1-1l-1-1 2-2z" class="H"></path><path d="M416 443c1 1 2 1 3 0h2l1 1h0l-2 2v-2h-1 0c0 2-1 2-1 3h-1v-1c0-1-1-2-1-3z" class="E"></path><path d="M444 432h1v1h2c0 2 0 4-1 6 0 0-1 2-2 2s-1-1-2-1h0l1 1-1 1h-1v-1c1 0 1-1 1-1l-2-1h1v-1c1-1 1-2 2-3 0-1 1-2 1-2v-1z" class="N"></path><path d="M407 501h1c-2 2-4 5-6 7h0v-1h-1v1h-1l-1 1-1-1c-1 1-1 1-1 2-1 0-1-1-2-1v1l-1-1 1-1h1l1-1s1 0 1-1v-1h1 1 0c1 0 1 0 1 1h1c0-1 0-2 1-2v-1-1l1-1h2 0 1z" class="V"></path><path d="M365 502h1 0c-1 1-2 2-3 2h0-1 2 1c1 0 2-1 2-2h3c0 1 1 1 1 2h-1v1c1 0 2-1 3-1h1v1c0 1-2 1-3 2-2-1-4-1-6-1h-4v-1c0-1 0-1 1-2v-1l1 1c1-1 2-1 2-1z" class="G"></path><path d="M361 516h3 7c-1 1-1 2-1 3l1 1h-7-3v-1-3z" class="V"></path><path d="M365 517h1l1 1-1 1h-1v-2z" class="E"></path><path d="M388 559c1 2 2 3 3 5s1 4 2 6c-1-1-1-1-2-1v1c0-1-1-2-1-2 0-2 0-3-1-4v1 2 1c-2-1-4 0-6 0 0-2-1-2-1-4h1c1 1 1 2 2 3v-2c0-1 2-2 4-2l-1-3v-1z" class="G"></path><path d="M427 306c0-1 0-2 1-4l1 1v2c-1 0-1 0-1-1v3h1c1-1 1-1 1-2h0l1-1 1 1-1 1h1c0-1 1-1 1-1l1 1c-1 1-1 1-1 3h0l-2 1c-1 1-1 1-2 1v-3h-1c-1 0-1 1-2 2l-1 1c0 1-1 2-1 3l-1-1c1-1 1-1 1-2s-1-1-1-2l1-1h1c1 0 1-1 2-2z" class="Y"></path><path d="M370 323h2s1 1 2 1c2 1 2 0 3 1l3 1h1 0c2 0 2 0 3 1h-4-4c-1 1 0 1-1 1h-2-2c0 1 1 2 0 3-1-1-1-3-2-4h0c0-1 0-2 1-4z" class="V"></path><path d="M379 320c1-1 1-1 1-2h-1v-1c1 0 2 0 3 1 2 1 5 0 6 2h1v1 1h-2c-1 1-3 1-5 0h0l-3 1-1-1h1v-2h0z" class="N"></path><path d="M374 535c0 1-2 1-2 1-2 0-3 1-4 0l-1 1h-1v-1-1c-1 1-1 1-1 2h-1c-2 0-2 0-3-2h0c3 0 6-1 9-2v-2h1c0 1 1 1 1 2h0 1 0l-1-1 1-1h2 0l1 1-2 3z" class="g"></path><path d="M167 481c1 0 2 0 2-1h0l1-1c1 2 2 2 3 3-1 1-2 1-2 3l-3-3c-1 2-1 5-2 7 1 1 2 0 2 2h-1-1v1c1 0 1 1 2 1-1 1-1 1-1 2-1 0-1 1-2 2h-1c1-5 1-12 3-16z" class="J"></path><path d="M382 575v-2l1-1h0c2-1 3-2 5-2l1-1h0l1 1h1v-1c1 0 1 0 2 1v2h1l-1 1v1 1c-1 0-1 1-2 0 0-1 1-1 1-2h-1-1c-1 0-1 0-2 1h-1l-1 1v-1c-1 0-3 0-4 1z" class="H"></path><path d="M391 569c1 0 1 0 2 1v2c0-1-1-1-2-1v-2z" class="E"></path><path d="M386 575l1-1h1c1-1 1-1 2-1h1 1c0 1-1 1-1 2 1 1 1 0 2 0 1 1 1 2 2 2-1 1-2 1-4 2-1 0-2 0-3 1h0l-2 1h0 0c0-1 0-1 1-2-1 0-1-1-1-2h-2l-1-1c1 0 2 0 3-1z" class="B"></path><path d="M387 479h0c-3-1-5 0-7-2 0-1 0-2 1-2 1-1 2-1 3-1l1 1c1 0 1-1 2 0-1 0-2 1-2 2h1c1-1 2-1 4-2v1 1h-2v1h1c2-1 2-1 4 0h1s-1 0-1 1h0c0 1-1 1-1 1-1 0-1 0-2 1l-3-2zm-223 76h2 1v2h1c2 0 6 2 8 4h-1c-2 1-4 1-6 1l-1-1-2-1c-1 0-1 0-2-1l2-1c-1-1-1-1-3-1h0v-1-1h1 0z" class="X"></path><path d="M166 558v1h1 1v1 1l-2-1c-1 0-1 0-2-1l2-1z" class="H"></path><path d="M443 413l1-2c1 1 2 1 3 1v-1h-2c1-1 2-2 3-2h1s1 1 1 2l1 1c-1 0-2 1-3 1h-1v2h-2 0v1l1 1h0-2v-1l-3 2v2h-1 0v-2h0v-1h-2 0l3-3c1 0 2-1 2-1z" class="B"></path><path d="M441 414c1 0 2-1 2-1 1 1 1 1 1 2h-1l-2-1z" class="F"></path><path d="M373 282c1 2 0 3 2 5 0 3 0 9 2 12h0c-1 3 0 5-1 7h0c-1-2-1-6-1-9l-1 1v4h-1 0c-1-6-1-14 0-20z" class="e"></path><path d="M206 402c1-1 6-5 6-6v-2l5-5 1 1v2s-2 2-2 3c-2 1-3 2-4 4 0 1-1 1-1 2h-1 1 1 0c0 2-1 2-1 3-1 0-1-1-2-1h-1 0c-1 0-2 0-2-1z" class="D"></path><path d="M361 458h0l1 1c-1 1-1 1-1 2 2 0 2-1 3-2l1 1c0 1 0 2-1 3h1c1-1 1-2 1-3 1 0 2 1 3 2h0-2v1h1v-1c1 1 1 1 1 2h0 0s1 0 1-1v-1c2 0 4 1 5 2-1 0-1 1-1 1v1h0c1-1 1-2 2-3l1 1-2 3h-1c-1 0-1-1-1-1-1 0-1 1-2 0 0 0 1-1 1-2v-1h-1c0 1 0 2-1 3-3 0-7-3-9-5-1 0-1 0-1-1l1-2z" class="b"></path><path d="M424 314c0-1 1-2 1-3l1-1c1-1 1-2 2-2h1v3h-2-1l1 1v2h1v1c-1 0-1 1-1 1 0 1-1 2-1 3-1 1-2 3-2 3-1 1-1 2-2 2v-7h0v-3h2z" class="Z"></path><path d="M395 510v-1c1 0 1 1 2 1v1h0c1 0 1 1 1 1-1 2-2 3-4 5l-2-2c-1 0-1 1-2 2h0c0 1-1 2-1 3h0c0 1 0 2-1 2h-1 0c0-1 0-1-1-2h0c1 0 1-1 1-2v-1c2-1 2-2 3-3 2-2 4-3 5-4z" class="H"></path><path d="M436 292h0l-1 5c0 2 0 3-1 4 0 2-1 3-2 4l-1-1-1 1h0c0 1 0 1-1 2h-1v-3c0 1 0 1 1 1v-2l-1-1 3-3v-1h0c1 0 1 1 2 1l1-1-2-1v-1c0-1 1-1 1-1v-1c0-1 1-1 2-1l1-1zM208 410l2 1h0 1 1l-1 2 1 1c0 1 0 1-1 3h1c1 3 3 5 4 8 1 2 1 4 1 5-3-2-5-5-6-9-1-1-1-2-1-2l-1-1v-3h1c-1 0-1-1-2-1l1-1c-1-1-1-2-1-3z" class="g"></path><path d="M430 460v-1c1 0 1 1 1 1h0 1v-3h1v1h0c1 0 1 1 2 1-1 2-2 4-2 6l-1 1-1-1v2s-1 1-1 2c-1 1-3 3-3 4h-1c-1 1-1 1-1 2v-2c0-1 0-1 1-1v-2-1-1h1v-2h0l3-3c0-1 0-1 1-2l-1-1z" class="N"></path><path d="M431 461v3l-1 1c0 1-1 2-2 2h-1v-1l3-3c0-1 0-1 1-2zm9-43v2h0 1v-2l3-2v1 1h0c1 1 1 1 1 2-1 1-3 1-5 2-6 2-11 0-18-1-1 0-3 0-4-1 1-1 4 0 5 1 3 0 8 0 11-1h2c1 0 1 0 2-1v1l2-2z" class="E"></path><path d="M393 479l2-1c0 1 1 1 1 2h0c1 0 3 1 4 0h1c1 2 1 3 1 5-1 0-1 0-2 1h-1-2-1c-1 0-1-1-2-2h0v-1h-1c-1 0-2-1-3-2 1-1 1-1 2-1 0 0 1 0 1-1z" class="I"></path><path d="M394 484h3 1v1l-2 1c-1 0-1-1-2-2z" class="E"></path><path d="M396 480c1 0 3 1 4 0h1c1 2 1 3 1 5-1 0-1 0-2 1h-1-2-1l2-1h2v-1c0-1-1-3-2-3h-4l-1 1v-1c1-1 2-1 3-1z" class="C"></path><path d="M388 506c-2-2-3-2-5-4-1-1-2-2-4-3l-2-1v-1h1c1-1 0-1 0-2h0c1 0 2 1 2 1 0 1 0 1-1 2l1 1h1v-3h1c1 0 2 0 3 1 0 0 0 1 1 2h1l1 1v1c1 0 2 0 3 1-1 1-1 2-2 2l1 1-2 1z" class="H"></path><path d="M181 447v1s-1 0-1 1-1 1-1 2 0 3 1 4h-1v2h0l-2-1c-1 1-1 2-2 3h1 2 1v1h-1c0 1-1 1-1 2-1 1-2 1-3 2h0v3l-1 1-1 2-1-1-1-1 3-3c1-2 0-2 0-4 0 0 0-1 1-1 0-2 1-4 2-6s2-5 4-7h0 1z" class="X"></path><path d="M386 449c0-1 1-1 1-1l2 1h0c1 0 1 0 1 1h1s1 0 1 1h3v2c0 1 1 1 1 1v1h-2 0l-1 1c-1 0-3 0-4-1h-2c-1 0-1-1-1-1h-2v-1-1c0-1 1-1 2-2v-1z" class="K"></path><path d="M203 407l1-1v-1c-1 1-2 2-3 2 1-2 3-4 5-5 0 1 1 1 2 1h0 1c1 0 1 1 2 1l-1 1-1 1 1 1h0l1-1h0v4l-1 1-2-1h0-2v2c-1 0-1 0-2-1h-1 0c-1-1 0-1 0-1v-3z" class="I"></path><path d="M205 409l1-3c0-1 1-2 1-2 1 1 1 2 1 3-1 1-2 1-3 2z" class="B"></path><path d="M205 409c1-1 2-1 3-2v3h-2v2c-1 0-1 0-2-1 0-1 0-1 1-2z" class="K"></path><path d="M402 443l1-1c1 0 1 1 2 1h0 3c0 1 0 1 1 1h2 0c0 1-1 1-1 1v1h2 0c-1 1-2 1-2 1 1 1 0 1 1 1h2v2l1 2h-3c-1-1-3-3-5-3-1-1-3-2-4-3l-1-1v1c-1-1-1-1-2-1 1-1 2-1 3-2z" class="W"></path><path d="M411 448h2v2l-1 1-1-3z" class="E"></path><path d="M411 452c0-1 0-1 1-1l1-1 1 2h-3z" class="X"></path><path d="M402 443l1-1c1 0 1 1 2 1h0 3c0 1 0 1 1 1h2 0c0 1-1 1-1 1v1h2 0c-1 1-2 1-2 1-1 0-1-1-2-2l-1 2h-2c0-1 0-2 1-3h-1 0c-1 1-1 1-1 2h-1c0-2 0-2-1-3h0zm-19-36l2-4 1 1c-2 3-5 6-6 9l-3 3h0c-1 2-2 5-4 6 0 1-2 3-2 3v2s1 1 2 1v1l-2 2c0 1 0 0-1 1 0-1-1-2-1-3v-1l-1-1c-1 0-2-1-2-2s1-1 2-1h0c0 1 0 2 1 2h0c1 0 1 0 2-1 0-1 4-7 4-8h-1v-1l1 1 2-2c-2 0-2-1-3-1 1-1-1-3-1-4h1l1 1c0 1 1 2 2 3 1 0 1-1 2-2s2-3 4-5z" class="c"></path><path d="M398 277c3 3 7 8 8 12v1l-2-2h-1l1 3h0c-1-1-2-2-3-1 0 1 1 1 1 2-1 0-1 0-2-1v1h0l-1 1-1-1-1 1-1-1v-1c1 0 1-1 2-1 1-1 1 0 2-1l-2-3h-1l3-3-2-3v-3z" class="V"></path><path d="M400 283l2 2c0 1 0 2 1 2v1c-2 0-3-2-5-2h-1l3-3z" class="E"></path><path d="M396 325c2 0 4-1 6 0h-1c0 1 1 2 0 4h2c-1 3-2 5-2 8 0-1 0-1-1-2-1 0-2 0-3 1-1-2-1-4-2-7l1-4z" class="J"></path><path d="M401 329c0 2-1 3-2 5-1-2-2-4-2-5v-2s1-1 1-2h3c0 1 1 2 0 4z" class="O"></path><path d="M408 324c-2-1-5-2-7-3-1 0-2 1-3 0-1 0-1-1-2-2v2h-1-4v-2h0l2 1h2c-1 0-1-1-1-2h0l2 1v-1c1 0 1 0 2 1l1-1h1v1h1v-1c2 0 2 1 2 1h2c1 1 2 1 4 1l1 2h0-1v1h0l2-1h1c1 2 1 2 2 3h-2v2c-1-2-2-2-4-3z" class="Z"></path><path d="M414 390l1 1 1 1h-1l-1 1c0 1 1 2 1 2h1 1v1c2 0 3 1 4 1h1c-1 1-1 2-2 2h1v1 1h-1v-1h0-1v1c-1-1-3-1-4-1h0l-1-1h-1l-2-2 1-1h-1-1l-1-1v-2-1c1 0 3-1 4-1s1-1 1-1z" class="c"></path><path d="M415 391l1 1h-1l-1 1c0 1 1 2 1 2h1 1v1c2 0 3 1 4 1h1c-1 1-1 2-2 2s-1-1-2-1c0-1-1 0-2-1l-1-1h0c-1-1-2-1-3-2h1v-1h-1l-1 1v-1-1h3c0-1 1-1 1-1z" class="H"></path><path d="M396 291v1 2l-1 1-1-1h-1v2c0 1 0 1-1 1v-1l-3 1v1h0c-1 1-1 2-1 3l-1-1c-1 0-1 1-1 1v3h0l-1-1h-1v1 1l-2-2c0 1 0 2-1 3h0l-1-1-1-1c1 0 1 0 2-1v-1l1-2c0-1 0-2 1-3 1 1 2 1 2 2 1-1 0-2 1-3h2c1-2 6-4 8-5z" class="X"></path><path d="M194 418c1 0 1 0 2 1v2 1s1 0 1 1l-3 2s-1 1-1 2h0c0 1 1 1 1 1v1 1h0-1v-1h-1v2h-2v1 1l2-1v2h-3l-1 1v-1h-1-1 0c1-2 2-5 3-6s1-2 2-3h0v-1c0-2 2-5 3-6z" class="V"></path><path d="M392 466h1v3c1 0 1 0 2-1 0 0-1-1-1-2h1c1 1 3 2 4 3 1 0 2 0 3 1l1 1h0c1 0 2 1 3 1-1 1-2 1-2 2l-1 1v-2l-1 1s0 1-1 1l-1-1h0 0c-1 0-2 1-3 1l1-1v-1l-1-1c-1 1-1 1-2 1v-3c0 1-1 1-1 2h0-1v-2-1l-1 2h0c-1 0-1-1-2-2-1 1-1 1-2 1h-1c1-1 1-1 2-1l2-2 1-1z" class="X"></path><path d="M447 398h2 0c0 2 2 3 3 4 0 2 1 2 2 3h1v1h0-1v1c1 1 1 1 2 1l-1 1c0 1 0 2-1 3-1 2-3 3-3 5l-1-1c-1 1-1 1-1 2h-1l-3 2c0-1 0-1-1-2h0v-1h2 0l-1-1v-1h0 2v-2h1c1 0 2-1 3-1l-1-1c0-1-1-2-1-2 2 0 3-2 5-3l-7-8z" class="V"></path><path d="M431 433l1-1h-1-2-1c2-2 5-1 7-2 1-1 3-2 4-2 0 1 0 2-1 2v1c1-1 2-1 2-2l-1-1 2-2c1 0 2 0 3-1 1 0 1-1 1-2l1 1 1 1c0 1-1 1 0 2l-1 3h-2c0 1 0 1-1 1h-1c-1 1-2 2-4 2h0l1-2h-1l-2 2h1l-1 1s0-1-1-1h-2l-1 2h-1v-2z" class="e"></path><path d="M489 375c3 0 5 1 8 1l8 3c6 3 13 5 18 9 0 1 1 1 1 2l-19-10-5 1h-1s-2-1-3-1c-2-1-5-2-8-3v-1c1 0 1-1 1-1z" class="Y"></path><path d="M488 376c2 0 4 1 6 2 4 0 7 1 11 2l-5 1h-1s-2-1-3-1c-2-1-5-2-8-3v-1z" class="B"></path><path d="M163 517v1 2h1c0-1 0-3 1-5h0v1h1v1l-1 1s1 1 1 2l-1 1s1 1 1 2c3 2 7 3 9 5v2h0-1c-3 1-7 0-8-1h-1-1v-1-1-1h1 0l-2-1h0v-4-4z" class="d"></path><path d="M170 472l1 1c0 1 0 1 1 2h0c0 1-1 1-1 2h2c1 2 2 2 4 3 2 3 7 3 11 4v1c-2 0-4 0-6-1-1 1-1 2-2 3-1-1-3-1-5-2v-1l1-1h-1c-1 0-1 1-2 1v-1-1c-1-1-2-1-3-3l-1 1h0c0 1-1 1-2 1l3-9z" class="H"></path><path d="M170 479l1-1h0c1 1 2 0 2 0v1 1l-2-1v1l1 1c1 0 2 0 2-1 1 1 1 1 1 2h1c2 0 4 1 6 2-1 1-1 2-2 3-1-1-3-1-5-2v-1l1-1h-1c-1 0-1 1-2 1v-1-1c-1-1-2-1-3-3z" class="F"></path><path d="M376 542h1l1 1v1c-1 2-4 2-5 2s-2 1-3 1c0 0-1 0-2 1 0 0-2 0-3 1h0 1 1v1c0 1 0 1-1 1l-1-1-1 1h-3v-1l-1-1c-1 0 0 1 0 2h-1-1v-2h-1c0 1 0 1-1 2l-1-1h-2c-1-1-2-1-3-2-1 0-1 0-2-1 2 0 7 1 9 1 1 1 2 0 4 0h2v-2h1s0 1 1 1l1-1c2 1 1 0 3-1v-1c0-1 1-1 2-1l2-1c1 0 0 0 0 1l1 1h1 1v-2z" class="i"></path><path d="M457 381h1c1 1 4 2 4 3v1c2 2 5 5 7 6 1 0 2 1 2 2 3 2 5 4 8 6 1 1 3 2 4 4 3 4 7 7 10 12h-1l-2-2c-1-2-3-4-4-5-4-4-8-7-11-11l-1-1c-1-1-2-1-3-2s-2-2-3-2c-2-1-2-2-4-3-1 0-2-1-3-1v-1c-1 0-3-2-3-3h1l1 1v-1s-1-2-2-2c0 1-1 1-1 1-1 1-1 1-2 0l-1-1h0l1-1h1 1z" class="b"></path><path d="M375 514c0-1 0-1 1-1 0-1 0-1 1-1 0-1 1-2 1-2h1c0 1 1 1 1 2v1l1 1c0-1 0 0 1 0v1 1l1-1c1 1 2 1 3 2h0l1 1c0 1 0 2-1 2h0c-1 2-1 3-2 3s-1-1-2-1l-1 1c-1 0-1-1-1-1h0c-1-1 0-2 0-2v-2h-1c-1 0-1-1-2-1v2h-1v-2c-1-1-1-2-1-3z" class="S"></path><path d="M380 522c0-1 0-2 1-2h0v-1h2c1 0 1 1 1 1h2 0c-1 2-1 3-2 3s-1-1-2-1l-1 1c-1 0-1-1-1-1z" class="F"></path><path d="M458 397l2 1h1c1 0 1 1 2 2 0 1 2 2 3 3l7 8c3 5 7 9 10 14 4 4 7 9 10 14 1 3 2 5 3 8 0 1 0 1 1 1v1h0c-2-5-6-10-9-15s-6-9-9-13l-8-11-3-3c-2-2-5-5-8-6 0-1-1-2-2-3v-1z" class="X"></path><path d="M455 402l2 1h0c2 0 3 1 4 2-1-1-2-3-3-3v-2c1 0 1 1 2 1v1c5 6 12 11 16 18-5-5-9-11-15-15h-1v2h-1v-2h-1c1 5 4 11 6 15 1 4 2 7 3 10 1 2 2 5 2 7-1-1-2-6-3-8l-4-10c-2-4-3-8-6-11-1 0-1 0-2-1v-1h1 0v-1c-1-1-1-2 0-3z" class="E"></path><path d="M419 293h3v2c0 1 1 3 1 4s-1 1-1 1v1c0 1-1 1-1 1 0 1-1 1-1 2 0 2-1 3-2 5v1h0 0l-1-1h-1 0v2h-1v-1-3-1c0-2 0-4 1-6l1-3c1 0 2 0 2-1h-2v-2l1-1h1z" class="W"></path><path d="M419 301l2 1c0 1-1 1-1 2 0 2-1 3-2 5v1h0 0l-1-1h-1c1-1 1-2 2-3-1-1-1-1-2-1 0-1 0-2 1-3v2h1c0-1 0-2 1-3z" class="C"></path><path d="M419 293h3v2c0 1 1 3 1 4s-1 1-1 1v1c0 1-1 1-1 1l-2-1-1-1v-1h1c0-2 0-2 1-3h-3v-2l1-1h1z" class="F"></path><path d="M418 293h1c0 1 1 1 1 3h-3v-2l1-1z" class="N"></path><path d="M389 520l1 1c0 2-2 3-4 4h-1c1 1 1 0 2 1v3h0l-1-2h-1l-1 2h0l3 1v1h-1-2s0 1 1 1l-1 2c1 2 3 4 3 6h-1l-1-1s0-1-1-1h0 0c0 1 1 2 1 3v2 3c1 0 1 1 1 2s0 2-1 2h0l-1-3c0-1-1-2-1-2-1-1-2-1-3-1l-1 1c0-1-1-2-1-3l1-1c0 1 1 2 1 2h1c1 0 2 1 2 1l1-1c-1-1-2-4-1-5v-1h-1c0-1 0-1 1-2-1 0-1-5 0-6 0-1 1-2 1-4-1 0-2-1-3-2l1-1c1 0 1 1 2 1s1-1 2-3c1 1 1 1 1 2h0 1c1 0 1-1 1-2zm38-72l2-2c0-1 0-1-1-2 2-1 4 0 7-1h0 2 2 1 1c-1 1-2 2-2 3-1 1-1 1-1 2h1c0 1-1 2-1 2h-1l-1 1c2 0 3 0 4 1-1 2-2 3-3 5l-2 2c-1 0-1-1-2-1h0v-1c1 0 1-1 1-2-1 0-1-1-1-2 1 0 1-1 2-2 0-1 1-2 2-3v-1l-10 1h0z" class="E"></path><path d="M434 455l1 1h0l2 1-2 2c-1 0-1-1-2-1h0v-1c1 0 1-1 1-2z" class="H"></path><path d="M361 362h5c-1-5-1-9-2-14l1-1h1v2h-1l1 1v2c-1 2 0 3 0 5v1h0c1 0 2 1 3 1v-1h1c1 0 1 1 2 1l1 1 1 1-1 1c1-1 1-1 2-1l1 1h0c2 0 2 0 3-1 0 1 0 2-1 2l1 1 1 2h0-2-1-2v1h-1-3v-1l-1 1h-1c-1 0-1-1-2-2v-1-1h-7l1-1z" class="J"></path><path d="M369 358h1c1 0 1 1 2 1l1 1 1 1-1 1h0c-1 0-5 0-6-1 0-1 0-2-1-3h0c1 0 2 1 3 1v-1z" class="i"></path><path d="M378 363h0l1 1 1 2h0-2-1-2v1h-1-3v-1l-1 1h-1c-1 0-1-1-2-2v-1-1h1c1 0 3 0 4 1h1 1c1-1 2-1 4-1z" class="g"></path><path d="M378 363h0l1 1 1 2h0-2-1-1v-1c1 0 2 0 2-1v-1z" class="E"></path><path d="M367 363h1c0 1 1 2 1 2 1 0 1 0 2 1l-1 1h-1c-1 0-1-1-2-2v-1-1z" class="N"></path><path d="M164 529h1c2 2 1 5 2 7l-1 1c0 1 1 1 2 1-1 1-2 1-2 2h3l1-1v1 1c0 1-1 1-1 2 2 4-1 1 0 3h1l1-1 1 1v1h1v-2l1 1c0 1-1 2-2 3-2 1-4 0-6-1-1 0-2-1-3-2v2h-1v-5c1-2 2-3 2-5h-2c0-1 0-1 1-2h1v-1h-1 0v-3-1l1-2z" class="D"></path><path d="M169 540l1-1v1 1c0 1-1 1-1 2-1 0-1 1-2 1h0l1-1v-1h-2v1c-1 0-1 0-1-1 1-1 2-1 3-2h1z" class="B"></path><path d="M164 565c1 0 1 1 2 1 0 1 1 2 2 2h1v-2h0c1 0 1 1 1 1 1 1 1 1 2 1h1c1 0 2 1 3 2-1 0-1 1 0 1l1 1h0-1-2 0c-2 0-5-1-6-2s-1-1-2-1l-1 1 1 1 1 2c1 2 2 3 1 5l1 1c1-1 1-1 2-1l1 1c-1 1-1 2-2 2v1h1c1-1 2-1 3-2h0v3h1 1v-1-1l1 1v2l-1 1c-3 0-8-2-11-4v-4c1-1 1 0 2-1h0l-1-1c-1 0-1 0-1-1h0l-1-1s-1-1-1-2-1-2 0-2h0 1v-4z" class="J"></path><path d="M374 276c0-5 1-11 2-16 1-1 1-2 3-3 0-1 1-1 1-1 7 0 22 19 26 24 2 3 4 6 6 8h8l-1 1c-1 0-2 0-2 1h-2c-1-1-2-1-3-1-7-9-14-18-22-26-3-2-7-6-10-6-2 1-2 2-2 3s1 1 2 1c0 1 1 1 1 2-1 1-1 0-2 0 0 1 1 2 2 3h-3v2s-1 0-1 1c-1 2-1 3-2 5h0l-1 2z" class="Z"></path><path d="M378 260c0 1 1 1 2 1 0 1 1 1 1 2-1 1-1 0-2 0 0 1 1 2 2 3h-3v2s-1 0-1 1c-1 2-1 3-2 5h0l3-14z" class="F"></path><path d="M375 311l1-1h1c-1 1-2 2-2 4h0c0 1 1 1 1 2 1 1 1 1 2 1 0 1-2 2-2 4h0c1-1 1-2 2-2l1 1h0v2h-1l1 1-2 2c-1-1-1 0-3-1-1 0-2-1-2-1h-2c-1 2-1 3-1 4h0 0v3l-2 2 1 1c-1 0-2-1-3-1 1-7 5-15 9-20l1-1z" class="d"></path><path d="M374 322h3s2-1 2-2v2h-1l1 1-2 2c-1-1-1 0-3-1h0v-2z" class="B"></path><path d="M373 316l1-1c1 1 1 1 2 3h0c-1 1-1 2-2 4v2h0c-1 0-2-1-2-1h-2-1c0-2 3-6 4-7z" class="L"></path><path d="M373 316l1-1c1 1 1 1 2 3h0c-1 1-1 2-2 4v2c0-3-1-1-2-2v-1c0-1 1-1 2-1-1-2 0-1 0-3 0-1 0-1-1-1z" class="S"></path><path d="M406 472c1 0 2 0 3 1l1 1c1 0 2 0 2 1 1 0 3 1 4 1h0l1-2 1 1c0-2 0-2 1-2h1c1 0 2-1 4-1v1c-1 1-1 2-1 3-1 0-1 1-2 1h-1l1-1h-1 0-2v1 1h0v1 2c-2 1-4 0-6 0-1 0-4-2-5-2s-1 0-2-1c-2 0-4-2-5-4h0l1 1c1 0 1-1 1-1l1-1v2l1-1c0-1 1-1 2-2z" class="g"></path><path d="M400 474h0l1 1c1 0 1-1 1-1l1-1v2c0 1 0 1 1 2 1 0 1 0 2-1h2v1l-1 1 1 1h1l1-2h1v1 1h1 0c1 0 2-1 3-1-1 1-1 1-2 1v1h1c1-1 2-1 4-1v2c-2 1-4 0-6 0-1 0-4-2-5-2s-1 0-2-1c-2 0-4-2-5-4z" class="M"></path><path d="M383 484c1 0 2 1 2 1l2 2 1 1c1 1 3 2 3 4 1 0 2 0 2 1v1c1 1 2 1 3 2 1 0 2 0 3 1 1 0 2 1 3 1 1 1 1 1 1 2h1v-1c0-1 2-3 3-4l1-1 1 1c0 1-1 1-2 3h0c-1 0-1 1-1 1v1c1 0 1 0 1 1h-1 0-2l-1 1v1 1c-1 0-1 1-1 2h-1c0-1 0-1-1-1h0-1-1v-1l1-1c1-1 0-2 0-2l-1-1h0-1v-2c-1 1-1 0-1 1h0-1v-2l-1 2-1-1h0l1-2h0-1l-2 1h-1v-1l2-1h-3-2c1-2 1-1 2-1l1-1v-1c-1 0-2 0-3 1v-1h-1c1-1 2-1 3-2h-1c-1 0-1 0-2 1h0c-1-1-2-2-3-2s-1-1-2-1l1-1h1l1 1v-2h0c-1 0-2 0-3 1h0-1v-1c1-1 2-1 3-2z" class="H"></path><path d="M398 500l1-1h0l1 1c1 0 1-1 2 0s0 2 0 2c-1 1-1 1-2 1v2h0-1-1v-1l1-1c1-1 0-2 0-2l-1-1z" class="P"></path><path d="M448 422c1-1 1-1 2-1v1l1-1c1 1 0 2 0 3l-1 1v1c0 1 0 2-1 3v1h0l-1-1-1 1 2 1-1 2h0-1v-1 1h-2v-1h-1c0 1-1 2-2 2 0 0-2 1-3 1l-12 2h-1-2-1c-1 0-1 0-2-1h0c-2 1-8 0-10-1-1 0-2 0-3-1h1c2 1 3 1 5 1 1-1 0-1 1-2h1v1h-1v1c2 0 2-1 4-2 1 1 1 2 2 3v-2l1-1c0 1 0 1 1 1h1v2h1v-2h0 2c1-1 1-1 2-1v2l2-2v2h1l1-2h2c1 0 1 1 1 1l1-1h-1l2-2h1l-1 2h0c2 0 3-1 4-2h1c1 0 1 0 1-1h2l1-3c-1-1 0-1 0-2s1-2 1-3z" class="H"></path><path d="M384 334h0l2 1h1l1 1c1 0 1 1 2 1 1 2 3 4 5 6l2 1h2s0 2 1 2h3v1l-1 1v4c-1 0-1-1-2 0 0 0 0 1 1 2l-1 1h-1v-2h-1v-1c-1-1-2-1-3-1 0-1-1-2-1-2-1 0-1 1-2 0v-2-1l-4-3c-1 0-1-1-2-2h0c0-1-1-1-2-1h0v-1s1-1 1-2h1v-1h-2v-2z" class="X"></path><path d="M392 347l1 1c1 0 2-1 3 0v1h0l1-1 1 1h1v-1h0c-1 0-1 0-1-1h1 0 2l1 1v4c-1 0-1-1-2 0 0 0 0 1 1 2l-1 1h-1v-2h-1v-1c-1-1-2-1-3-1 0-1-1-2-1-2-1 0-1 1-2 0v-2zm-8-13h0l2 1h1l1 1c1 0 1 1 2 1 1 2 3 4 5 6l-1 1h-1v-2l-1 2h-1l-1-1 2-1-1-1h-1c0 1 0 1-1 1v-1s0-1 1-1v-1h-1c-1 2-1 2-3 2h0c0-1-1-1-2-1h0v-1s1-1 1-2h1v-1h-2v-2z" class="Q"></path><path d="M186 434h0 1 1v1 2l1 1c1-1 3-1 4-2-1 1-2 3-3 3v1c1-1 2-1 3-2h0v2c1 1 2 1 2 2l-1 1v1h1l1-1h0v1l-1 1 1 1h2c0 1 1 1 2 2v1 1c-1 0-1 0-2-1v-2h-1v1 1c-2-1-3-2-4-3l1-1h0-1-1c0-1 1-1 1-2-1 0-1 0-2 1l-1-1c0-1-2-3-3-5h-1v-3h0l-1 3h0c-1 2-1 2-1 4v1h3 0l-2 2v3h2c1 1 1 1 1 2l-1 1-1 1 1 1v2h0c-3-2-5-4-7-6 0-1 1-1 1-1v-1l-1-1c0-2 1-3 2-4 1-3 2-6 4-8z" class="J"></path><path d="M399 486h1c1-1 1-1 2-1s2 0 3 1h1c1 1 3 2 4 2l1-1 1 2 1-1 1-1h2s1 1 1 2h-1v1c-1 1-2 2-3 4 0 0 0 1-1 1h-1c-1 2-1 5-3 6h-1c0-1 0-1-1-1v-1s0-1 1-1h0c1-2 2-2 2-3l-1-1-1 1c0-1-1-2-2-2l-4-2-1-1c-1-1-2-2-3-4h2z" class="E"></path><path d="M399 486h1c1-1 1-1 2-1s2 0 3 1c-2 1-3 1-4 1h0l-2-1zm2 5c1-1 2-1 4-2v2h1v1l-1 1-4-2z" class="H"></path><path d="M397 486h2l2 1h0c0 1 0 1 2 2l1-1h1v1c-2 1-3 1-4 2l-1-1c-1-1-2-2-3-4z" class="J"></path><path d="M373 345h1v-2c1 1 1 0 2 1l1 1v1 1h1v-1c2 0 2 3 4 2h0l1 2h0v2c1 1 2 1 3 2v1c-1 0-2-1-2-1-1 1 0 1-1 2v1h0c-2 0-3-1-5 0v1c-1 0-2 1-2 1v2 1l-1-1c-1 0-1 0-2 1l1-1-1-1-1-1c-1 0-1-1-2-1h-1s1 0 1-1c1-1 4-1 5-1h0l1-1 1 1 2-2h-3l-1-1h-2c-2-1-3-2-4-4v-1l1-1h1c1 1 1 1 2 1 1-1 0-1 1-2l-1-1h0z" class="H"></path><path d="M369 349h2 3v-1c1 0 1 1 2 1h-2v3h1c0-1 1-1 1-2l1 1-1 3-1-1h-2c-2-1-3-2-4-4z" class="d"></path><path d="M373 345h1v-2c1 1 1 0 2 1l1 1v1 1h1v-1c2 0 2 3 4 2h0l1 2-1 1c-1 1-1 0-2 0-1-1-2-1-3-2v1l-1-1c-1 0-1-1-2-1v1h-3-2v-1l1-1h1c1 1 1 1 2 1 1-1 0-1 1-2l-1-1h0z" class="f"></path><path d="M373 345h1v-2c1 1 1 0 2 1l1 1-1 1h0c-1 0-2-1-3-1z" class="P"></path><path d="M420 288c8 0 16 0 23 2h-3 1 0c0 2 0 3-1 4s-1 1-1 2h-1c-1-1-1-2 0-3v-1h-1-1 0c-1 0-1 0-2-1v-1c0-1-1 0-2 0-1 2-1 4-3 6l-1 1c0 1 1 1 0 2h-1l-1-2h-1v1h-1l-2-3v-2h-3-1c-1-1-1-2-1-3s1-1 2-1l1-1z" class="H"></path><path d="M425 297v-1h0l1-1v-4h-1v1 1 1h-1v-3c-1 0-1-1 0-1h3 1v1 1c1-1 1-2 1-2h2 1c-1 2-1 4-3 6l-1 1c0 1 1 1 0 2h-1l-1-2h-1z" class="P"></path><path d="M431 290h1c-1 2-1 4-3 6v-1c-1-2 1-3 2-5z" class="g"></path><path d="M465 384c0 1 1 1 1 1h2c1 1 1 1 2 1 1 1 2 2 3 2 8 6 17 11 24 17l4 4h0l-1 1c1 1 3 2 3 4l3 3h0c-2-1-3-2-5-4 0-1-1-1-1-2l-2-2h0-1c0 1 0 1 1 2l1 1-1 1-1-2h-1c-1-2-2-4-4-5-1-1-1-2-2-2-1-1-3-2-4-3s-2-2-3-2c-3-2-5-6-9-8-1 0-2-1-2-2h-1v1c1 0 2 1 2 2h0 0c-1 0-2-1-2-1-1 0-2 0-2-1h-1l1-1-4-5h0z" class="F"></path><path d="M439 372v1l-2 2c-1 0-2 0-2 1-1 0-2 0-2 1v1c-1 1-1 1 0 2 0 1-3 3-4 4h0l-2-1h-1 0-1-1 0c-1 1-4 1-5 2h-2c-2 0-4 1-6 1l-1 1v2h-1-1c-5 2-8 6-11 9-4 5-8 10-10 16 0 2-2 5-2 6l3 2c0 1 1 1 1 2l-4-3c-2 2-3 7-4 10 0 1-1 2-1 3v1l-1 1h-1v-1c1-1 2-3 2-5 1-1 2-3 2-5 1-1 1-2 1-3 2-6 4-11 6-16l7-8s1-1 1-2c2-2 4-4 6-7 2-1 3-2 5-3 1-1 3-2 4-1 1 0 5-1 7-2h2c1 0 1 0 1-1 1 0 1 0 1-1v-2c-1-1-1-2-1-3h0c2 2 2 3 3 5l-1 1h1c1 0 2-2 3-3s3-3 5-4c2 0 4-2 6-3z" class="Z"></path><path d="M427 383c1-1 1-2 2-3s2-2 4-3v1c-1 1-1 1 0 2 0 1-3 3-4 4h0l-2-1z" class="C"></path><path d="M391 330h1c0-1 0-2 1-3v-1 3h2c1 3 1 5 2 7 1-1 2-1 3-1 1 1 1 1 1 2l1 1h1v1c1 1 2 0 3 0-1 1-2 1-4 1v1h3c1 1 3 0 5 1h-2c-1 1-1 2-1 3h-2c-1 0-1 0-2 1v1-1h-3c-1 0-1-2-1-2h-2l-2-1c-2-2-4-4-5-6 0-1 0-1 1-2-1 0-1 0-1-1-1-1-1-2-2-3v-1h1 1 0 1z" class="Q"></path><path d="M397 336c1-1 2-1 3-1 1 1 1 1 1 2l1 1v1h-1c-2 0-3-2-4-3z" class="D"></path><path d="M402 342h6c-1 1-1 2-1 3h-2c0-1-3-2-4-2l1-1z" class="M"></path><path d="M391 330h1c0-1 0-2 1-3v-1 3 1c0 1 1 1 0 2l1 1c1 1 1 2 1 3 1 3 4 5 7 6l-1 1c-4-2-7-4-9-9-1-1-1-2-1-4z" class="H"></path><path d="M390 330h1c0 2 0 3 1 4 2 5 5 7 9 9 1 0 4 1 4 2-1 0-1 0-2 1v1-1h-3c-1 0-1-2-1-2h-2l-2-1c-2-2-4-4-5-6 0-1 0-1 1-2-1 0-1 0-1-1-1-1-1-2-2-3v-1h1 1 0z" class="B"></path><path d="M397 580v1c1 2 2 3 4 5v1h-1l3 8c2 4 3 9 5 13h1v-1h1 1l-1 1h-1c0 1 1 2 1 2l1 1c1-1 1 0 2 0s5-1 7 0l-1 1c-2 1-6 0-9 0-2-2-5-5-6-8h-1v-1h1l-1-1v-1l-2 1h0l1-2v-3l-1 1c-3 1-7 1-10 1h0c1-1 1-1 2-1h1 2c0-1 1-1 1-1 1 0 3-1 4-1v-1h-1c-1 1-2 1-4 2 0-1-1-1-1-1-1 1-2 1-3 1l1-1h1l-1-1v-1l-1-1s2-1 3-1v-1h0-2l-1-1h1c0-1-1-2-1-3h1c1 0 3-1 4-2 0-1-1-3-1-4 1 0 1 0 1-1z" class="I"></path><path d="M393 590c1 0 1 0 2-2v1h1c1-1 1-1 2-1 1 1 1 2 2 3l-1 1h0c-1 0-1 0-2 1h0 3 0v1c-2 1-4 1-6 2l-1-1v-1l-1-1s2-1 3-1v-1h0-2l-1-1h1z" class="B"></path><path d="M492 364h1l-4 11s0 1-1 1v1l8 3c1 0 3 1 3 1 8 4 16 9 23 14-3 0-5-3-7-4-5-3-22-13-28-13v1c1 2 4 3 6 4 8 5 16 10 22 16h-1c-7-6-13-11-20-15-2-1-5-2-7-3-1 0-2-1-2-1-1 0-1 1-2 1h-1c-2 1-2 1-4 1v-1h-3l1-1c1-1 2-1 3-2v-1l-2-1 1-1h1c1 0 1-1 1-1 1 0 2 0 3-1h0l1-1c1 0 1 0 2 1h0l2 1c1-1 2-4 2-5l1-1v-1c1-1 1-2 1-3z" class="N"></path><path d="M483 373h0l1-1c1 0 1 0 2 1h0-1v1 1 1h0l2-1c0 1-1 3-2 3 0 1-1 1-1 1-1 1-1 1-1 2h0-1c-2 1-2 1-4 1v-1h-3l1-1c1-1 2-1 3-2v-1l-2-1 1-1h1c1 0 1-1 1-1 1 0 2 0 3-1z" class="Y"></path><path d="M478 382l1-2c1 0 1 0 2 1h1c-2 1-2 1-4 1z" class="N"></path><path d="M479 375c1 0 1-1 1-1 1 0 2 0 3-1 0 1 0 2-1 2h-1v2 1c1 0 1 0 2-1h1c0 1-1 2-2 3-1-1-1-1-2-1h-2v2h-3l1-1c1-1 2-1 3-2v-1l-2-1 1-1h1z" class="U"></path><path d="M374 343l1-1c3 1 2 3 5 2h1l-1 1 1 1 2-2 1 1h2c1 0 2-1 2-2l4 3v1 2c1 1 1 0 2 0 0 0 1 1 1 2 1 0 2 0 3 1v1h1v2h1l1-1v1l-3 1c-1 1-1 2 0 3h-2-1v-1h-1l-1 1h-3 0v-2h1l-2-1h-2-3 0l-1 1v-1c1-1 0-1 1-2 0 0 1 1 2 1v-1c-1-1-2-1-3-2v-2h0l-1-2h0c-2 1-2-2-4-2v1h-1v-1-1l-1-1c-1-1-1 0-2-1z" class="W"></path><path d="M387 356c0-1 0-1 1-2 1 0 1 0 2-1h0l1 1h1v-1l1-1v2 1s-1 1-1 2c-1-1-2-1-3-1h-2z" class="h"></path><path d="M388 343l4 3-2 1v1 2h0c-1-2 0-2 0-3-3 0-4 0-6-2h2c1 0 2-1 2-2z" class="C"></path><path d="M377 346c1-1 1-1 2-1l1 1 1 1h1v-1h1c0 1 1 1 1 3 2 0 1-1 3 0v1c-1 1-1 1-2 1 0-1-1-1-1-1h-1 0l-1-2h0c-2 1-2-2-4-2v1h-1v-1z" class="V"></path><path d="M393 355c1-1 1-1 1-2v-1l1 2v1c1 0 2 0 2-1l1-1h1v2h1l1-1v1l-3 1c-1 1-1 2 0 3h-2-1v-1h-1l-1 1h-3 0v-2h1l-2-1c1 0 2 0 3 1 0-1 1-2 1-2z" class="C"></path><path d="M389 356c1 0 2 0 3 1h0 5 0c-1 1-2 1-2 1h-1l-1 1h-3 0v-2h1l-2-1z" class="d"></path><path d="M442 390c1 0 1 0 2-1 0 0 1 0 1-1 1 0 1 0 2-1l1-1c-1 0-1-1-2-1 0 0 0-1-1-1h1c1 0 3 2 5 3 0 0 1 1 1 2s1 2 2 2c1 2 3 3 4 5h-1c1 0 1 1 1 1v1c1 1 2 2 2 3h0c-1 0-1-1-2-1v2c1 0 2 2 3 3-1-1-2-2-4-2h0l-2-1c-1 1-1 2 0 3h-1c-1-1-2-1-2-3-1-1-3-2-3-4h0-2l-3-3v-1c-2 1-2 2-3 3l-1-1v-1c1 0 1-1 1-1v-2s0-1 1-1l-1-1h1z" class="Z"></path><path d="M455 402l-3-3c-2-2-4-3-6-6l1-1 3 3-3-5c1 1 2 3 3 3h1c-1-1-1-2-2-3-1 0-1 0-1-1h2c3 0 4 5 6 6 1 0 2 0 2 1h-1c1 0 1 1 1 1v1c1 1 2 2 2 3h0c-1 0-1-1-2-1v2c1 0 2 2 3 3-1-1-2-2-4-2h0l-2-1z" class="h"></path><path d="M387 479l3 2c1 1 2 2 3 2h1v1h0c1 1 1 2 2 2h1c1 2 2 3 3 4l1 1 4 2c1 0 2 1 2 2-1 1-3 3-3 4v1h-1c0-1 0-1-1-2-1 0-2-1-3-1-1-1-2-1-3-1-1-1-2-1-3-2v-1c0-1-1-1-2-1 0-2-2-3-3-4l-1-1-2-2s-1-1-2-1h-3v-1c0-1-1 0-2-1v-1h2c0 1 1 1 2 1v-1c1 0 1 0 2-1h2s1 0 1-1z" class="K"></path><path d="M418 372h1 0 1v1c1 0 1 1 1 1 1 1 1 1 1 2h0c0 1 0 2 1 3v2c0 1 0 1-1 1 0 1 0 1-1 1h-2c-2 1-6 2-7 2-1-1-3 0-4 1-2 1-3 2-5 3-2 3-4 5-6 7 0 1-1 2-1 2l-7 8-1-1v-1h0v-1c1-1 2-2 3-2l-1-1c-1 2-2 4-4 4 4-5 7-9 11-13h0c1-1 4-4 6-5 0 0 1 1 2 1 0-1 1-1 1-2 1 0 2-1 2-1l3-1v-1l-1-1h1c0-1 1-1 2-2l-2-1-2 1v-1h1v-3h1 0c1 0 1 0 2-1l1 1c1 1 1 1 2 1v-1h3 1l-2-2v-1z" class="Y"></path><path d="M413 379v1 1l1-1h1c0 2 0 2-2 3 0-1 0-1-1-1h-1l-1-1h1c0-1 1-1 2-2z" class="e"></path><path d="M397 391c0 2-1 3-2 4l-5 8 2-2c1-1 2-3 4-3l-7 8-1-1v-1h0v-1c1-1 2-2 3-2l-1-1c-1 2-2 4-4 4 4-5 7-9 11-13z" class="P"></path><path d="M418 372h1 0 1v1c1 0 1 1 1 1 1 1 1 1 1 2h0c0 1 0 2 1 3v2c0 1 0 1-1 1 0 1 0 1-1 1h-2-2c-1-1 0-1 0-3h0-1v1h-1v-1h-1l-1 1v-1-1l-2-1-2 1v-1h1v-3h1 0c1 0 1 0 2-1l1 1c1 1 1 1 2 1v-1h3 1l-2-2v-1z" class="i"></path><path d="M419 379c-1 0-1-1-2-2h0 3v1l-1 1z" class="B"></path><path d="M416 375h3c-1 0-2 0-2 1l-1 1c0 1 0 1 1 2l-1 1-1-1c0-1-2-2-2-3l1-1c1 1 1 1 2 1v-1z" class="C"></path><path d="M420 378h1v-1h-1v-1h2c0 1 0 2 1 3v2c0 1 0 1-1 1 0 1 0 1-1 1v-1h0-2-1l-1-1 1-1h2l-1-1 1-1z" class="Q"></path><path d="M418 382c1-1 2-1 3-2 1 1 1 1 2 1h0c0 1 0 1-1 1 0 1 0 1-1 1v-1h0-2-1z" class="b"></path><path d="M461 375l5 1h1c1 0 3 1 4 0h1 2 3l2 1v1c-1 1-2 1-3 2l-1 1h0-1c-1 1-1 2-1 2 1 2 3 4 4 5 0 1 1 1 1 2 11 7 22 16 31 27 2 2 4 4 5 6-2 0-5-5-6-7l-7-7h0l-4-4c-7-6-16-11-24-17-1 0-2-1-3-2-1 0-1 0-2-1h-2s-1 0-1-1c-1-1-2-1-2-3h-1c-1-1-1-1-3-1h0c0-1-1-1-2-1l2-2h0v-1c1 0 1 0 2-1z" class="E"></path><path d="M478 390c-1 0-2-1-2-1-1 0-1-1-2-1-2-2-5-4-7-5l7-2c-1 1-1 2-1 2 1 2 3 4 4 5 0 1 1 1 1 2z" class="B"></path><path d="M461 375l5 1h1c1 0 3 1 4 0h1 2 3l2 1v1c-1 1-2 1-3 2l-1 1h0v-1h-1c-2 0-8 2-9 2v-2c-1 0-2 1-2 1h-1c-1-1-1-1-3-1h0c0-1-1-1-2-1l2-2h0v-1c1 0 1 0 2-1z" class="Q"></path><path d="M467 376c1 0 3 1 4 0h1l-1 1h-1v1h-6l2-2h1z" class="Y"></path><path d="M461 375l5 1-2 2v-1c-2 0-1 0-2 1h-3 0v2c0-1-1-1-2-1l2-2h0v-1c1 0 1 0 2-1z" class="i"></path><path d="M474 376h3l2 1v1c-1 1-2 1-3 2l-1 1h0v-1h-1v-1c-1 0-3-1-4-1v-1h1l1-1h2z" class="e"></path><path d="M412 446c1 1 3 1 5 1h1c2 1 5 1 7 1h2 0l10-1v1c-1 1-2 2-2 3-1 1-1 2-2 2 0 1 0 2 1 2 0 1 0 2-1 2h-1v3h-1 0s0-1-1-1v1c-1-1-2 0-3 0s-1-1-2-1h-1v1l-2-1c-2 0-2-1-3-1-1-1-2-1-3-2s-1-2-1-3v-1h-1l-1-2v-2h-2c-1 0 0 0-1-1 0 0 1 0 2-1z" class="K"></path><path d="M413 448c2 1 4 1 6 3h-2v1c-1 0-1 1-2 1v-1h-1l-1-2v-2z" class="d"></path><path d="M427 454c1 0 1 0 2 1 1 0 2-1 2-1l1-1h1c0 1 0 2 1 2 0 1 0 2-1 2h-1v3h-1 0s0-1-1-1v1c-1-1-2 0-3 0s-1-1-2-1c1-1 3-2 3-3-1-1-1-1-1-2z" class="V"></path><path d="M419 451l1 2 1-1c1 0 2 1 3 1 0 1 0 1-1 2h1c0-1 1-1 1-1h2c0 1 0 1 1 2 0 1-2 2-3 3h-1v1l-2-1c-2 0-2-1-3-1-1-1-2-1-3-2s-1-2-1-3c1 0 1-1 2-1v-1h2z" class="H"></path><path d="M420 453h1l1 2h0l-1 1h-2v-1l1-2z" class="D"></path><path d="M433 377c0-1 1-1 2-1 0-1 1-1 2-1l1 2 1-1c1 1 2 1 3 1l1-2c2 0 2 1 3 2h-1v2h3 2l2 1v1l1-1c1 0 2 1 2 1l-1 1h0l1 1-1 1c1 0 1 0 1 2h0c-1 1-3-1-5-1v1l1 1c-2-1-4-3-5-3h-1c1 0 1 1 1 1 1 0 1 1 2 1l-1 1c-1 1-1 1-2 1 0 1-1 1-1 1-1 1-1 1-2 1h-1l-1-1v-1l-2 1c-1-1-3-1-4-3h1v-1c-1 1-1 1-2 1h0c-1 0-2-1-3-2h1l-1-1v1h-1 0c1-1 4-3 4-4-1-1-1-1 0-2v-1z" class="b"></path><path d="M435 379h3v1l-2 1-1-1v-1z" class="i"></path><path d="M433 386v-2h2v-1c1 0 1 0 2-1h0 1c1 0 1 0 2 1v1c-2 0-2-2-4 0 1 0 1 1 2 1s2 0 3 1v1h3v1 1l-2-1h-1v1l1 1h-1l-1-1v-1l-2 1c-1-1-3-1-4-3h1v-1c-1 1-1 1-2 1h0z" class="Q"></path><path d="M450 386c-1-1-2-3-4-4h-2c1-1 0-2 0-3h1 3 2l2 1v1l1-1c1 0 2 1 2 1l-1 1h0l1 1-1 1c1 0 1 0 1 2h0c-1 1-3-1-5-1v1z" class="W"></path><path d="M452 381l1-1c1 0 2 1 2 1l-1 1h0l1 1-1 1c-1 0-2-1-3-1 0-1 1-1 1-1v-1z" class="V"></path><path d="M433 377c0-1 1-1 2-1 0-1 1-1 2-1l1 2v1h-1c0-1 0-1-1-2-1 1-1 2-1 3v1l1 1 2-1c1 0 1 1 2 1h1 2v2h-1 0 0l-1-1h-3 0-1 0c-1 1-1 1-2 1v1h-2v2c-1 0-2-1-3-2h1l-1-1v1h-1 0c1-1 4-3 4-4-1-1-1-1 0-2v-1z" class="c"></path><path d="M433 377c0-1 1-1 2-1 0-1 1-1 2-1l1 2v1h-1c0-1 0-1-1-2-1 1-1 2-1 3v1 2l-1 1-1-1c1-1 1-2 1-3s-1-1-1-1v-1z" class="B"></path><path d="M455 383c1 1 1 1 2 0 0 0 1 0 1-1 1 0 2 2 2 2v1l-1-1h-1c0 1 2 3 3 3v1c1 0 2 1 3 1 3 3 6 5 9 7 3 4 7 7 10 10 1 2 1 3 2 4 4 4 9 9 11 13l2 2c-1 0-3-3-4-4l-7-7c-4-5-8-11-12-14h-3c0-1-1-1-2-1h-1-1v1c7 7 15 16 19 26h0l-10-14h-2c-1-1-1-1-2-1l-7-8c-1-1-3-2-3-3-1-1-1-2-2-2h-1l-2-1s0-1-1-1h1c-1-2-3-3-4-5-1 0-2-1-2-2s-1-2-1-2l-1-1v-1c2 0 4 2 5 1h0c0-2 0-2-1-2l1-1z" class="E"></path><path d="M468 402c3 4 6 7 9 10h-2c-1-1-1-1-2-1l-7-8c1 0 1 0 2-1z" class="C"></path><path d="M452 389s1-1 1 0c1 0 3 2 4 3 0 0 1 1 1 2 1 1 2 1 3 2 1 3 5 5 7 6-1 1-1 1-2 1-1-1-3-2-3-3-1-1-1-2-2-2h-1l-2-1s0-1-1-1h1c-1-2-3-3-4-5-1 0-2-1-2-2zm16 11c-3-2-5-4-8-6-2-2-4-4-5-6l3-2c3 1 4 3 7 6 3 2 6 5 10 8h-3c0-1-1-1-2-1h-1-1v1z" class="Q"></path><path d="M337 368c1 0 2 0 3-1v-1c1 0 2-1 2-2s-1-1-1-1v-3h1c1 1 1 3 2 5l17-3-1 1h7v1 1c1 1 1 2 2 2-3 1-7 2-10 2l-11 3c-1 1-1 1-2 1-2 0-3 1-4 1-1 1-3 2-4 4 0 0 0 1 1 1 0 1 1 2 2 3l-1 1c0 2-1 3-1 4h0c0 1 0 2-1 2 1 1 2 2 3 2v1l1 1c0 1-1 1-2 2-1 0-2 1-3 2v1 2 9 42c-1-4 0-9 0-13l-1-27v-24c0-4 0-8 1-12v-6-1z" class="g"></path><path d="M345 368c0-1 0-1-1-1h0c2-2 5-2 8-3-2 2-2 3-5 3h0l-2 1z" class="F"></path><path d="M337 389v1l2 2-1 2h1l1 1c-1 0-2 1-3 2v1-9z" class="L"></path><path d="M337 386v-8h1s0 1 1 1c0 1 1 2 2 3l-1 1-1 2-2 1z" class="P"></path><path d="M340 383c0 2-1 3-1 4h0c0 1 0 2-1 2 1 1 2 2 3 2v1l1 1c0 1-1 1-2 2l-1-1h-1l1-2-2-2v-1-3l2-1 1-2z" class="B"></path><path d="M337 369h0c2 0 3 0 4-1l3 4-7 4v-1-6z" class="S"></path><path d="M360 363h7v1 1c1 1 1 2 2 2-3 1-7 2-10 2l-11 3s0-1-1-1c0-1-1-2-2-3h0l2-1h0c3 0 3-1 5-3 3 0 6 0 8-1z" class="L"></path><path d="M347 371h2c1-2 4-2 7-3 1 1 2 1 3 1l-11 3s0-1-1-1z" class="P"></path><path d="M367 364v1c1 1 1 2 2 2-3 1-7 2-10 2v-1c1-2 5-1 7-3 1 0 1-1 1-1z" class="C"></path><path d="M426 383h0 1l2 1h1v-1l1 1h-1c1 1 2 2 3 2h0c1 0 1 0 2-1v1h-1c1 2 3 2 4 3l2-1v1l1 1 1 1c-1 0-1 1-1 1v2s0 1-1 1v1l1 1c-1 1-2 1-2 2h-2c-2 1-2 4-4 2h-5-1-1l-1-1-1 1h0l-2-1h-1v-1h-1c1 0 1-1 2-2h-1c-1 0-2-1-4-1v-1h-1-1s-1-1-1-2l1-1h1l-1-1-1-1h-1c-2-1-2-2-3-3l1-1c2 0 4-1 6-1h2c1-1 4-1 5-2h0 1 1z" class="E"></path><path d="M434 390c1 1 2 1 3 1h1 1c0 1-1 1-1 2h-2-2s-1 1-1 2h-1 0l1-2c-1-1-2 0-4-1 1 0 1 0 2-1h2v1h0 1v-2z" class="C"></path><path d="M441 392v2s0 1-1 1v1l1 1c-1 1-2 1-2 2h-2c-2 1-2 4-4 2h0l1-1v-1l-1-1c0 1-1 2-2 2v-1l1-1c2 0 2-1 3-1s2 0 2-1h0l2 1v-1c0-1 1-1 1-2h0s1-1 1-2z" class="Y"></path><path d="M424 391l2 2h1v-1h-1v-1c1 0 1-1 2 0v1h1v3l-1 1c0-1-1-1-1-1s0-1-1-1h0l-1 1h-1-1v2h-1-1c-1 0-2-1-4-1v-1h-1-1s-1-1-1-2l1-1 1 1c1-1 1-1 1-2l1 1h1s1 0 1 1c1-1 1-1 2-1l1 1h1v-2z" class="N"></path><path d="M417 396l1-1h4 0l1-1v1 2h-1-1c-1 0-2-1-4-1z" class="Y"></path><path d="M419 387c1-1 1-2 2-2h3 1c1 2 3 2 4 3 1 0 3 1 4 1v-1c1 1 1 1 1 2v2h-1 0v-1h-2c-1 1-1 1-2 1h0-1v-1c-1-1-1 0-2 0v1h1v1h-1l-2-2v-1-1c-2 0-3 0-4 2l-1-1 1-1c1 0 1 0 2-1-1-1-2-1-3-1z" class="F"></path><path d="M411 386c2 0 4-1 6-1 0 1 1 1 2 2h0c1 0 2 0 3 1-1 1-1 1-2 1l-1 1 1 1c1-2 2-2 4-2v1 1 2h-1l-1-1c-1 0-1 0-2 1 0-1-1-1-1-1h-1l-1-1c0 1 0 1-1 2l-1-1h1l-1-1-1-1h-1c-2-1-2-2-3-3l1-1z" class="C"></path><path d="M413 387h3c1 1 1 1 1 2h-3l-1-2z" class="F"></path><path d="M410 387l1-1c1 1 2 1 2 1l1 2h3l1 1h0c1 0 1 0 1 1l-1 1-1-1c0 1 0 1-1 2l-1-1h1l-1-1-1-1h-1c-2-1-2-2-3-3z" class="i"></path><path d="M426 383h0 1l2 1h1v-1l1 1h-1c1 1 2 2 3 2h0c1 0 1 0 2-1v1h-1c1 2 3 2 4 3l2-1v1l1 1 1 1c-1 0-1 1-1 1 0 1-1 2-1 2h-4v-1h2c0-1 1-1 1-2h-1-1c-1 0-2 0-3-1 0-1 0-1-1-2v1c-1 0-3-1-4-1-1-1-3-1-4-3h-1-3c-1 0-1 1-2 2h0c-1-1-2-1-2-2h2c1-1 4-1 5-2h0 1 1z" class="Q"></path><path d="M426 383h0 1l2 1h1v-1l1 1h-1c1 1 2 2 3 2h0c1 0 1 0 2-1v1h-1c1 2 3 2 4 3l2-1v1l1 1 1 1c-1 0-1 1-1 1 0 1-1 2-1 2h-4v-1h2c0-1 1-1 1-2h-1c-1-2-3-2-4-3l-8-5z" class="I"></path><path d="M405 381c1 1 1 1 1 2h0l-1 2h1c0 1-1 1-1 2-1 0-2-1-2-1-2 1-5 4-6 5h0c-4 4-7 8-11 13l-1-1-2 4c-2 2-3 4-4 5s-1 2-2 2c-1-1-2-2-2-3l-1-1h-1 0-2l-4 4c-1 1-2 2-3 4 0 0-1 1-1 2l-2 2-4 5c-3 4-6 8-9 11 0-1 1-2 1-3 2-2 3-4 4-5s1-2 2-2c2-3 3-4 5-7 1 0 1-1 2-2h-1-2 0l-1-1h0l1-2h-1l1-1v1l1-1h1 1v1h0 2c4-4 8-9 12-13 2-2 4-3 6-5v-2h-1v-1c1-1 2-1 3-2 0 0 0-1 1-1l-1-1 8-5-1-1h0c1-1 2-2 4-2h2l2-2h1v1h3 0 0c1 0 2-1 2-1z" class="X"></path><path d="M405 381c1 1 1 1 1 2h0-1s-1 0-2-1c1 0 2-1 2-1z" class="W"></path><path d="M382 396c1-1 2-1 4-1-2 1-3 2-4 3v-2z" class="Q"></path><path d="M362 415v1h0 2l-2 2h0l-1-1h0v2h-2 0l-1-1h0l1-2h-1l1-1v1l1-1h1 1z" class="P"></path><path d="M389 393c2-1 4-2 6-4 2-1 3-2 5-3 1 0 2-1 2-1h1c-1 0-1 0-1 1-2 1-4 3-5 4-1 2-3 2-4 4l-3 3-5 6-2 4-1-1c1-1 1-2 1-3v-1h1c1-1 2-2 2-4 1-1 3-2 4-3 0-1 0-1-1-2z" class="f"></path><path d="M384 402h0c2 0 2-1 3-2 0-1 0-1 1-1l1-2h1l-5 6-2 4-1-1c1-1 1-2 1-3v-1h1z" class="P"></path><defs><linearGradient id="A" x1="386.67" y1="395.276" x2="393.01" y2="384.288" xlink:href="#B"><stop offset="0" stop-color="#2c2c2e"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M397 383l2-2h1v1h3 0l-2 1-15 12c-2 0-3 0-4 1h-1v-1c1-1 2-1 3-2 0 0 0-1 1-1l-1-1 8-5-1-1h0c1-1 2-2 4-2h2z"></path><path d="M397 383l2-2h1v1h3 0l-2 1h-1c-1 0-2 2-4 3l-1-1h0c1-1 2-1 2-2h0z" class="c"></path><path d="M389 393c1 1 1 1 1 2-1 1-3 2-4 3 0 2-1 3-2 4h-1v1c0 1 0 2-1 3l1 1c-2 2-3 4-4 5s-1 2-2 2c-1-1-2-2-2-3l-1-1h-1 0-2c3-3 5-6 8-8l10-9z" class="L"></path><path d="M383 403c0 1 0 2-1 3l1 1c-2 2-3 4-4 5l-1-1c1-3 3-6 5-8z" class="Q"></path><path d="M379 402c0 1 0 2-2 3 0 1-1 2-2 3v1l1 1-1 1-1-1h-1 0-2c3-3 5-6 8-8z" class="C"></path><path d="M477 363h1l-1 1c2 1 3 0 4 0s2-1 3-1l2 2v1c-1 1-3 2-5 2 0 1-1 1-1 2 2 1 3 0 4 2l-1 1h0c-1 1-2 1-3 1 0 0 0 1-1 1h-1l-1 1h-3-2-1c-1 1-3 0-4 0h-1l-5-1c-1 1-1 1-2 1v1h0l-2 2c1 0 2 0 2 1h0l-1 1h-1-1-1s-1-1-2-1l-1 1v-1l-2-1h-2-3v-2h1c-1-1-1-2-3-2l-1 2c-1 0-2 0-3-1l-1 1-1-2 2-2v-1l1-1c1 0 1-1 2 0 2-1 8-1 9-2h0 3 1c0 1 1 1 2 1 2 0 3 0 4-2l2 1h0 0 0c1 0 1 0 2-1s2 0 4 0c0-1 0 0-1-1h0 1c0-1 1-2 2-3h1 3c0-1 1-1 2-1z" class="F"></path><path d="M439 376h1c1-1 2-2 2-3h0 1c1 0 1 1 2 1h1c-1 0-2 0-3 1l-1 2c-1 0-2 0-3-1z" class="f"></path><path d="M446 374c1 1 1 1 2 1 1-1 2-1 3 0h1 0c-1 1-1 1-2 1v1 2h-2-3v-2h1c-1-1-1-2-3-2 1-1 2-1 3-1z" class="Q"></path><path d="M457 373l4 2c-1 1-1 1-2 1v1h0l-2 2c1 0 2 0 2 1h0l-1 1h-1-1-1s-1-1-2-1l-1 1v-1l-2-1v-2-1c1 0 1 0 2-1h0 2l1-1s2 0 2-1z" class="h"></path><path d="M457 381l-1-2s0-1 1 0c1 0 2 0 2 1h0l-1 1h-1z" class="d"></path><path d="M452 375h2l-1 1h-1s1 3 0 4l-2-1v-2-1c1 0 1 0 2-1h0z" class="f"></path><path d="M457 373l4 2c-1 1-1 1-2 1h-3v1l-1-1v-2s2 0 2-1z" class="C"></path><path d="M469 368v-1h2v1h2c0 1 0 1 1 2v2c-1 0-1-1-2-1v1h-1c-1 1-1 1-1 2s0 1 1 2h0c-1 1-3 0-4 0h-1l-5-1-4-2c-3-1-7-2-10-2h-5c2-1 8-1 9-2h0 3 1c0 1 1 1 2 1 2 0 3 0 4-2l2 1h0 0 0c1 0 1 0 2-1s2 0 4 0z" class="W"></path><path d="M455 369c0 1 1 1 2 1 2 0 3 0 4-2l2 1h0 0l1 2h2v-1l1 1v1h1v1h-2-3c-3-1-6-2-8-2h-1v-1l1-1z" class="X"></path><path d="M463 369h0 0l1 2s-1 0-1 1c-1-1-1-1-2-1 1-1 1-1 1-2h1z" class="e"></path><g class="N"><path d="M467 372h1v1h-2-3c1-1 3-1 4-1z"></path><path d="M469 368v-1h2v1h2c0 1 0 1 1 2v2c-1 0-1-1-2-1v1h-1c-1 1-1 1-1 2s0 1 1 2h0c-1 1-3 0-4 0v-1h0c0-1 0-1-1-2h2v-1h-1v-1l-1-1v1h-2l-1-2h0c1 0 1 0 2-1s2 0 4 0z"></path></g><path d="M469 369h2v1h-2v-1z" class="I"></path><path d="M469 368v-1h2v1 1h-2c-1-1-2 0-3 0v1 1h-2l-1-2h0c1 0 1 0 2-1s2 0 4 0z" class="E"></path><path d="M467 371h3s0-1 1 0h1v1h-1c-1 1-1 1-1 2s0 1 1 2h0c-1 1-3 0-4 0v-1h0c0-1 0-1-1-2h2v-1h-1v-1z" class="d"></path><path d="M477 363h1l-1 1c2 1 3 0 4 0s2-1 3-1l2 2v1c-1 1-3 2-5 2 0 1-1 1-1 2 2 1 3 0 4 2l-1 1h0c-1 1-2 1-3 1 0 0 0 1-1 1h-1l-1 1h-3-2-1 0c-1-1-1-1-1-2s0-1 1-2h1v-1c1 0 1 1 2 1v-2c-1-1-1-1-1-2h-2v-1h-2v1c0-1 0 0-1-1h0 1c0-1 1-2 2-3h1 3c0-1 1-1 2-1z" class="g"></path><path d="M471 376h0c-1-1-1-1-1-2s0-1 1-2h1l1 2c2-1 2-1 4-1 1 1 1 2 2 2h-1l-1 1h-3-2-1z" class="Z"></path><path d="M477 373c1 1 1 2 2 2h-1l-1 1h-3c1-1 1-2 3-2h0v-1z" class="H"></path><path d="M477 363h1l-1 1c2 1 3 0 4 0 1 1 0 1 0 2v1s-1 0-1 1l-2 1v1h-1v-1l-3 1c-1-1-1-1-1-2h-2v-1h-2v1c0-1 0 0-1-1h0 1c0-1 1-2 2-3h1 3c0-1 1-1 2-1z" class="X"></path><path d="M473 368h3l1 1-3 1c-1-1-1-1-1-2z" class="I"></path><path d="M387 356h2l2 1h-1v2h0 3l1-1h1v1h-1v1c0 1 2 1 2 2v1c3 0 7 1 10 2 1 1 3 1 4 1l2 1v1c1 1 2 2 3 2 1 1 2 2 3 2v1l2 2h-1-3v1c-1 0-1 0-2-1l-1-1c-1 1-1 1-2 1h0-1v3h-1v1l2-1 2 1c-1 1-2 1-2 2h-1l1 1v1l-3 1s-1 1-2 1h-1l1-2h0c0-1 0-1-1-2 0-1-1-2-1-2-1-1-1-1-2-1h0l-1-1 1-1c0-1-1-1-2-2 0 0-1 1-2 1h0-2v-2c-1 0-1 1-2 1h-2c-1 1-1 3-3 2h-1l-1-1c-3-1-6 0-8 1l-1-1h1c1-1 1-2 1-3-1 1-1 1-2 1v-2h0c-1-1-1-1-1-2v-1h1l-1-1c-1 1-1 0-2 0h0v-1h2 1 2 0l-1-2-1-1c1 0 1-1 1-2-1 1-1 1-3 1h0v-1-2s1-1 2-1v-1c2-1 3 0 5 0h0l1-1h0 3z" class="P"></path><path d="M418 373l2 2h-1-3 0-2v-1l2-1h0l1 1h0l1-1z" class="F"></path><path d="M377 369c2 0 3 0 5 1h3c-1 1-2 1-3 2h0-2c-1 1-1 1-2 1v-2h0c-1-1-1-1-1-2z" class="U"></path><path d="M393 370v-1h2c3 0 3 0 5 1 1 0 1 0 2-1h0l4 1v1 1h-1c-1 0-2 1-3 1v1h2l-2 2c0-1-1-1-2-2 0 0-1 1-2 1h0-2v-2c-1 0-1 1-2 1l1-1c-2 0-2-1-3-1 1-1 2-1 3-2h-2z" class="W"></path><path d="M395 370h2l1 1-1 1h-2v1c-2 0-2-1-3-1 1-1 2-1 3-2z" class="Y"></path><path d="M402 369l4 1v1 1h-1c-1 0-2 1-3 1v1h2l-2 2c0-1-1-1-2-2 0 0-1 1-2 1h0-2v-2h2s1 0 1-1h2 2l1-1c-1 0-1-1-2-1v-1z" class="Q"></path><path d="M393 370h2c-1 1-2 1-3 2 1 0 1 1 3 1l-1 1h-2c-1 1-1 3-3 2h-1l-1-1c-3-1-6 0-8 1l-1-1h1c1-1 1-2 1-3h2 0c1-1 2-1 3-2h1 2 5z" class="H"></path><path d="M385 370h1l1 1c-1 0-1 0-1 1-1 0-2 1-2 2-1-1-2-1-2-2 1-1 2-1 3-2z" class="N"></path><path d="M393 370h2c-1 1-2 1-3 2-1 0-1 1-2 1s-1-1-1-1c1-1 1 0 2-1h0l-3-1h5z" class="X"></path><path d="M392 372c1 0 1 1 3 1l-1 1h-2c-1 1-1 3-3 2h-1c1-1 2-1 2-2h-1l1-1c1 0 1-1 2-1z" class="U"></path><path d="M406 371h5v1h0 0c-2 0-3 0-4 1h0 2 1 0l-1 1c0 1 0 1 1 1h1-1v3h-1v1l2-1 2 1c-1 1-2 1-2 2h-1l1 1v1l-3 1s-1 1-2 1h-1l1-2h0c0-1 0-1-1-2 0-1-1-2-1-2-1-1-1-1-2-1h0l-1-1 1-1 2-2h-2v-1c1 0 2-1 3-1h1v-1z" class="c"></path><path d="M409 379l2-1 2 1c-1 1-2 1-2 2-1 0 0 0-1-1h-1v-1z" class="Y"></path><path d="M408 384v-1h0l2-2 1 1v1l-3 1z" class="g"></path><path d="M410 375c-1 1-3 2-4 3v1-1l-1-1h-2v-1c2 0 4 0 5-1l1-1c0 1 0 1 1 1h1-1z" class="Q"></path><path d="M388 362l1 1c2 0 5 1 7 0 3 0 7 1 10 2 1 1 3 1 4 1l2 1v1c-2-1-6-2-7-2s-2 1-3 0l1-1h-1-1v1c-1 1-2 1-3 1v-1c-1 1 0 1-1 1 0 1-1 1-1 1h-2c-5-2-11-1-16-2h2 0l-1-2h1c1 0 2 0 3-1 0 1 1 1 1 1h1v-3c1 0 2 1 3 1z" class="X"></path><path d="M388 362l1 1c2 0 5 1 7 0 3 0 7 1 10 2 1 1 3 1 4 1l2 1v1c-2-1-6-2-7-2s-2 1-3 0l1-1h-1-1-1-1s0-1-1-1h0-4-1l-1 1h-1c-1 0-1-1-1-1-1 0-3 0-3 1h-1v-1h0-1v-3c1 0 2 1 3 1z" class="U"></path><path d="M387 356h2l2 1h-1v2h0 3l1-1h1v1h-1v1c0 1 2 1 2 2v1c-2 1-5 0-7 0l-1-1c-1 0-2-1-3-1v3h-1s-1 0-1-1c-1 1-2 1-3 1h-1l-1-1c1 0 1-1 1-2-1 1-1 1-3 1h0v-1-2s1-1 2-1v-1c2-1 3 0 5 0h0l1-1h0 3z" class="E"></path><path d="M378 358v-1c2-1 3 0 5 0v1h-5z" class="N"></path><path d="M387 356h2l2 1h-1v2l-2-1v1l1 1h-1l-1-1 1-1v-1l-2 1-2-2h0 3z" class="U"></path><path d="M387 359l1 1h1l-1-1v-1l2 1h0-1l1 1h3v1c-1 0-2 1-4 1v1l-1-1-2-3h1z" class="e"></path><path d="M390 359h3l1-1h1v1h-1v1c0 1 2 1 2 2v1c-2 1-5 0-7 0v-1c2 0 3-1 4-1v-1h-3l-1-1h1z" class="Y"></path><path d="M379 361h3c1 0 2 0 3-1v1h0v3h-1s-1 0-1-1c-1 1-2 1-3 1h-1l-1-1c1 0 1-1 1-2h0z" class="I"></path><path d="M379 361l2 1s0 1-1 2h-1l-1-1c1 0 1-1 1-2h0z" class="M"></path><path d="M371 366v1h3 1 0c1 0 1 1 2 0l1 1h-1v1c0 1 0 1 1 2h0v2c1 0 1 0 2-1 0 1 0 2-1 3h-1l1 1c-7 1-12 5-19 7 0 2 0 3-2 4 1 0 1 1 1 1l1 1h-1-1l-1 1h0c-1-1-1-2-2-2v-1l-4 2c-2 1-3 2-4 3l-3 2-7 3c1-1 2-2 3-2 1-1 2-1 2-2l-1-1v-1c-1 0-2-1-3-2 1 0 1-1 1-2h0c0-1 1-2 1-4l1-1c-1-1-2-2-2-3-1 0-1-1-1-1 1-2 3-3 4-4 1 0 2-1 4-1 1 0 1 0 2-1l11-3c3 0 7-1 10-2h1l1-1z" class="L"></path><path d="M371 366v1h3 1 0c1 0 1 1 2 0l1 1h-1v1c0 1 0 1 1 2h0c-1 2-1 2-3 3v-1c1 0 1 0 1-1v-1h-1-1c-2-1-3-1-4-2-3 0-7 0-10 1 0 1 0 2-1 2s-2 1-4 1c-1 0-4 1-5 2h0c-1 1-2 1-4 1l-1-1 1-2c1 0 1 0 2-1l11-3c3 0 7-1 10-2h1l1-1z" class="U"></path><path d="M353 372c3-1 5-2 7-2 0 1 0 2-1 2s-2 1-4 1l-2-1z" class="C"></path><path d="M350 375h-3v-1c2-1 4-2 6-2l2 1c-1 0-4 1-5 2h0z" class="F"></path><path d="M374 371v-1l-1-1v-1h1l1 1h2v-1 1c0 1 0 1 1 2h0c-1 2-1 2-3 3v-1c1 0 1 0 1-1v-1h-1-1z" class="X"></path><path d="M375 371h1v1c0 1 0 1-1 1v1c2-1 2-1 3-3v2c1 0 1 0 2-1 0 1 0 2-1 3h-1l1 1c-7 1-12 5-19 7 0 2 0 3-2 4 1 0 1 1 1 1l1 1h-1-1l-1 1h0c-1-1-1-2-2-2v-1l-4 2c-2 1-3 2-4 3l-3 2-7 3c1-1 2-2 3-2 1-1 2-1 2-2l-1-1v-1c-1 0-2-1-3-2 1 0 1-1 1-2h0c1 0 2-1 3-1 0-1 0-1 1-1v1h-1c1 1 3 2 3 3 1 0 0 2-1 2l1 1 3-3v-1l-2 1v-1h2 1 0 1l2-2v-1l-2 1c0-1-2-2-2-3h2c1 0 1 0 2-1h-2 0v-1-1c-1 1-1 1-2 1v-1c1-1 1-1 3-1l-1-1h-2-1v-1h2 1c1 0 1 0 2 1 0 1 0 1 1 1 2 0 2-1 4-2 1 0 4-2 5-2s2 1 2 1c1 0 2 0 3-1h0 2c1-2 3-1 5-2 0 0 1-1 1-2z" class="W"></path><path d="M360 383c0 2 0 3-2 4 1 0 1 1 1 1l1 1h-1-1l-1 1h0c-1-1-1-2-2-2v-1c1 0 1-1 2-2 1 0 2-1 3-2z" class="Q"></path><path d="M352 378c0 1 0 1 1 1 2 0 2-1 4-2 1 0 4-2 5-2s2 1 2 1c-2 1-8 4-10 4h-2v-2z" class="C"></path><path d="M378 371v2c1 0 1 0 2-1 0 1 0 2-1 3h-2c-2 1-4 1-6 2-3 1-5 3-8 4l-10 5v-1l-1-1c1-1 2-2 4-3 1-1 4-2 7-3l1 1v-2l5-1c2 0 4-1 6-2s2-1 3-3z" class="B"></path><path d="M380 261c1 0 1-1 3 0 3 2 4 7 7 8s6 5 8 8v3l2 3-3 3h1l2 3c-1 1-1 0-2 1-1 0-1 1-2 1-2 1-7 3-8 5h-2c-1 1 0 2-1 3 0-1-1-1-2-2-1 1-1 2-1 3l-1 2v1c-2 0-2-1-3-2l-1-1v-1h0c-2-3-2-9-2-12-2-2-1-3-2-5 0-2 0-4 1-6l1-2h0c1-2 1-3 2-5 0-1 1-1 1-1v-2h3c-1-1-2-2-2-3 1 0 1 1 2 0 0-1-1-1-1-2z" class="R"></path><path d="M381 266h0c-1 2-3 4-3 7l-2 8c-1 2-1 4-1 6-2-2-1-3-2-5 0-2 0-4 1-6l1-2h0c1-2 1-3 2-5 0-1 1-1 1-1v-2h3z" class="N"></path><path d="M375 274h0v1 2c0 1 0 3 1 4-1 2-1 4-1 6-2-2-1-3-2-5 0-2 0-4 1-6l1-2z" class="X"></path><path d="M380 261c1 0 1-1 3 0 3 2 4 7 7 8s6 5 8 8v3c-1-2-3-4-4-6-1 0-1-1-3-2-1 0-2-2-3-3h-1c0-1 0-1-1-2 0-2-3-2-4-3h-1v2h0c-1-1-2-2-2-3 1 0 1 1 2 0 0-1-1-1-1-2z" class="U"></path><path d="M408 324c2 1 3 1 4 3 0 0 0 1 1 1h2c2 2 1 3 3 4h1 0c0 1 1 2 1 3v3h0l1 1h-1c1 1 1 1 1 2v2c0 1 0 1-1 2h0l1 1c-1 1-2 3-3 4h0c-1 1 0 2 0 3l1 1c1-1 1-1 2-1h0 1l1 1v2h1l-1 1s-1 0-2 1l-2 1c0 1-1 1-2 2v3c1 0 1 1 1 1h4v3c1 0 1 1 1 1 1 1 0 3 0 3-1 1-2 0-3 1v-1h-1 0-1c-1 0-2-1-3-2-1 0-2-1-3-2v-1l-2-1c-1 0-3 0-4-1-3-1-7-2-10-2v-1c0-1-2-1-2-2v-1h1 1 2c-1-1-1-2 0-3l3-1v-1c-1-1-1-2-1-2 1-1 1 0 2 0v-4l1-1v-1c1-1 1-1 2-1h2c0-1 0-2 1-3h2c-2-1-4 0-5-1h-3v-1c2 0 3 0 4-1-1 0-2 1-3 0v-1l2-5 1-7 2-2z" class="L"></path><path d="M413 358h1v1h-1v-1z" class="K"></path><path d="M406 365l2-1c1 0 1 0 2-1h0c1 1 0 1 2 2-1 1-2 1-2 1-1 0-3 0-4-1z" class="C"></path><path d="M418 350c-1-2-1-5-1-7l1 3h2v-1l1 1c-1 1-2 3-3 4h0z" class="E"></path><path d="M408 342h2l2 2c0 1-1 1-1 2-1 0-2-1-2-1-1 0-1 1-2 0 0-1 0-2 1-3z" class="D"></path><path d="M412 365l2 2 2 2h0c1 0 1 1 2 1l2 2h-1 0-1c-1 0-2-1-3-2-1 0-2-1-3-2v-1l-2-1s1 0 2-1z" class="Q"></path><path d="M421 353h1l1 1v2h1l-1 1s-1 0-2 1l-2 1c0 1-1 1-2 2v-1c1-3 2-5 4-7z" class="Y"></path><path d="M423 354v2h1l-1 1s-1 0-2 1c0-1-1-1-1-2l2-1s0-1 1-1z" class="b"></path><path d="M409 334l1-1 1-2h1l1 1c0 2 0 3-2 5-1 1-3 2-5 2-1 0-2 1-3 0v-1l2-5c1 0 2 0 4 1z" class="G"></path><path d="M408 324c2 1 3 1 4 3 0 0 0 1 1 1l1 2h-1v2l-1-1h-1l-1 2-1 1c-2-1-3-1-4-1l1-7 2-2z" class="M"></path><path d="M410 328c1 0 1 0 2 2v1h-1l-1 2-1 1 1-6z" class="Z"></path><path d="M408 324c2 1 3 1 4 3 0 0 0 1 1 1l1 2h-1v2l-1-1v-1c-1-2-1-2-2-2-2-1-2-2-4-2l2-2z" class="C"></path><path d="M413 328h2c2 2 1 3 3 4h1 0c0 1 1 2 1 3v3h0l1 1h-1c1 1 1 1 1 2v2c0 1 0 1-1 2h0v1h-2l-1-3c0-5-1-9-3-13h0l-1-2z" class="I"></path><path d="M420 338l1 1h-1l-1 1v1h-1v-1-2h2zm-7-10h2c2 2 1 3 3 4h1l-1 1-1 1-1-1v-2-1h-2 0l-1-2z" class="E"></path><path d="M394 374c1 0 1-1 2-1v2h2 0c1 0 2-1 2-1 1 1 2 1 2 2l-1 1 1 1h0c1 0 1 0 2 1 0 0 1 1 1 2 0 0-1 1-2 1h0 0-3v-1h-1l-2 2h-2c-2 0-3 1-4 2h0l1 1-8 5 1 1c-1 0-1 1-1 1-1 1-2 1-3 2v1h1v2c-2 2-4 3-6 5-4 4-8 9-12 13h-2 0v-1h-1-1l-1 1v-1l-1 1h1l-1 2-2-2 1-1 2-2v-1c3-1 4-4 6-6v-1h-1l-3 4h0c-1 1-1 2-2 2h-1v1c-1 0-1 0-2-1-1 0-2 0-3-1v-1c0-1 1-1 1-1h0v-2h-2 0v-2l-1-2h-1v1c-3 1-4 3-6 4l-3 3-3 3h0c0-2 1-3 2-4-1 0-1 0-2-1l1-1-2 2v-9-2-1l7-3 3-2c1-1 2-2 4-3l4-2v1c1 0 1 1 2 2h0l1-1h1 1l-1-1s0-1-1-1c2-1 2-2 2-4 7-2 12-6 19-7 2-1 5-2 8-1l1 1h1c2 1 2-1 3-2h2z" class="e"></path><path d="M383 385c0 1 1 1 1 2-1 0-1 1-2 1l-1-2 2-1z" class="i"></path><path d="M404 379s1 1 1 2c0 0-1 1-2 1h0 0c-1-1-1-1-1-2l2-1z" class="Y"></path><path d="M369 397v-2l3-3 2 1c-1 2-4 3-5 4z" class="B"></path><path d="M372 392c1 0 2-1 3-1 1-1 2-3 3-3s2-1 3-2l1 2-8 5-2-1z" class="Q"></path><path d="M390 378h2l1 1s-1 1-1 2c-2 1-3 2-5 3l-3 3c0-1-1-1-1-2l2-2h0l-2-2c2 0 5-2 7-3z" class="C"></path><path d="M390 378h2l1 1s-1 1-1 2c-1-1-1-2-2-3z" class="b"></path><path d="M367 406h0c0-2-1-1-1-2s1-1 1-2v-2c1-1 2-1 3-2 2-2 5-4 7-5l5-3c0-1 1-1 2-2l1-1c1-1 3-2 4-3s3-2 4-3v1c-2 2-5 3-7 6h1c1-1 3-2 4-3l1 1-8 5c-2 2-7 5-8 8-2 1-3 2-4 3l-1 1v1l-3 4c-1-1-1-1-1-2z" class="Q"></path><path d="M391 385l1 1-8 5c-2 2-7 5-8 8-2 1-3 2-4 3l-1 1-3 3h0v-1h0c0-1 0-1 1-1v-1l1-1v-1c1 0 2 0 2-1l3-3c1 0 2-2 4-3h0l3-2c1-1 2-2 3-2l2-2c1-1 3-2 4-3z" class="B"></path><defs><linearGradient id="C" x1="373.248" y1="412.76" x2="369.268" y2="399.551" xlink:href="#B"><stop offset="0" stop-color="#191d1c"></stop><stop offset="1" stop-color="#332f31"></stop></linearGradient></defs><path fill="url(#C)" d="M384 391l1 1c-1 0-1 1-1 1-1 1-2 1-3 2v1h1v2c-2 2-4 3-6 5-4 4-8 9-12 13h-2 0v-1c-1-1-1-1-1-2 1-2 3-6 6-7 0 1 0 1 1 2l3-4v-1l1-1c1-1 2-2 4-3 1-3 6-6 8-8z"></path><path d="M378 384l5-3 2 2h0l-2 2-2 1c-1 1-2 2-3 2s-2 2-3 3c-1 0-2 1-3 1l-3 3v2 1c-1 0-2 1-2 1-1-1-2-3-3-3s-2-1-3-1c1-2 4-4 5-5l12-6z" class="K"></path><path d="M366 390l12-6c-2 3-7 4-9 7v1c-1-1-2-1-3-2z" class="C"></path><path d="M360 395h0c1 1 1 1 2 1 2 1 3 3 4 3-1 1-2 1-3 2v2c1 0 1-1 2-1h0v1c-1 0-1 0-1 1-1 1-3 3-3 5h0c-1 1-1 2-2 2h-1v1c-1 0-1 0-2-1-1 0-2 0-3-1v-1c0-1 1-1 1-1h0v-2h-2 0v-2c1-1 0-2 0-3 2-1 4-4 5-4s1 0 1-1l2-1z" class="K"></path><path d="M394 374c1 0 1-1 2-1v2c0 2 0 2-1 3-1 0-1 1-2 1l-1-1h-2c-2 1-5 3-7 3l-5 3-12 6c-1 1-4 3-5 5h-1l-2 1c0 1 0 1-1 1s-3 3-5 4c0 1 1 2 0 3l-1-2h-1v1c-3 1-4 3-6 4l-3 3-3 3h0c0-2 1-3 2-4-1 0-1 0-2-1l1-1-2 2v-9-2-1l7-3 3-2c1-1 2-2 4-3l4-2v1c1 0 1 1 2 2h0l1-1h1 1l-1-1s0-1-1-1c2-1 2-2 2-4 7-2 12-6 19-7 2-1 5-2 8-1l1 1h1c2 1 2-1 3-2h2z" class="V"></path><path d="M342 407h0c0 1-1 2-2 2s-1 0-2-1l1-1h3z" class="S"></path><path d="M394 374c1 0 1-1 2-1v2c0 2 0 2-1 3-1 0-1 1-2 1l-1-1s1 0 2-1h0c-2-1-4 0-6 1l1-2c2 1 2-1 3-2h2z" class="c"></path><path d="M363 387c1 0 1 0 2-1 2 0 2-2 4-1h0s0 1-1 1c1 0 1 1 2 1l-2 1c-1 0-2 1-3 2-2 1-3 2-4 3-1 0-1-1-2-2l-2-1 1-1h1 1l-1-1h2c1-1 1-1 2-1z" class="C"></path><path d="M387 375l1 1h1l-1 2c-3 1-7 2-10 4l-8 5c-1 0-1-1-2-1 1 0 1-1 1-1h0c-2-1-2 1-4 1-1 1-1 1-2 1 1-2 3-2 5-3 1-2 4-3 6-3l3-2h1 1c3-1 5-1 7-1h0l1-3z" class="Q"></path><path d="M379 376c2-1 5-2 8-1l-1 3h0c-2 0-4 0-7 1h-1-1l-3 2c-2 0-5 1-6 3-2 1-4 1-5 3-1 0-1 0-2 1h-2s0-1-1-1c2-1 2-2 2-4 7-2 12-6 19-7z" class="F"></path><path d="M355 387v1 2h1c1 1 3 2 4 3l-2 2h-1c-1 0-1 1-1 1l-1 1c-1 1-2 2-4 3v-3l-1 1v3c-1 0-2 0-3 1 0 2-3 3-4 5h-1 0-3l-2 2v-9-2-1l7-3 3-2c1-1 2-2 4-3l4-2z" class="L"></path><path d="M347 398c0-1 0-1-1-2 0-1 1-1 1-1l1 1c1 0 2 1 3 1l-1 1v3c-1 0-2 0-3 1 0 0-1-1-1-2h-1 0v-1h2v-1zm8-11v1 2h1c1 1 3 2 4 3l-2 2h-1c-1 0-1 1-1 1l-1 1v-1c0-1-1-1-1-1-1-1 0-1-1-2h2 0v-2c-1 0-1 0-2-1h0-1v2c-1 0-2-1-3 0h-2c1-1 2-2 4-3l4-2z" class="K"></path><path d="M337 397l7-3 1 1h-1c0 1 1 2 1 2 1 1 1 1 2 1v1h-2v1h0 1c0 1 1 2 1 2 0 2-3 3-4 5h-1 0-3l-2 2v-9-2-1z" class="B"></path><path d="M337 400c1-1 2-1 3 0h0c-1 1-1 1-1 3v1s1 0 1 1 1 1 2 2h-3l-2 2v-9z" class="P"></path><path d="M426 325h0c0-1 1-2 1-2l1 1c-1 0-1 1-1 2l1 1h1v-1l1-1 1 1s-1 1-1 2v1c1 0 2-1 3 0 0 1 0 1 1 2-1 1-1 1-1 3h0c1 1 2 3 3 4h0c1 1 2 1 3 1l1-1v-1c1 1 2 1 2 1l1-1-1-1v-1h1l1 1c1-1 1-1 2-1l2 1 4-4c1-1 4-2 6-2 2 1 4 0 6 1l-1 1-1 1c2 0 4 1 6 3 1-1 2-1 2 0l3 1v1c0 1 1 1 1 2h0v3h-1l-1 1v1h2 1 0l1 1-1 1h-1-1l-3 2-1 1v-1c-1 0-1 0-2 1v1 1l1 1-1 1h0v2h-1l-1 1-1-1c-1 0-1 0-1 1v2 1l2 1c0-1 1-2 2-2h0 2v1h0v2l-1 1h1 1c0 1 0 1 1 1-1 1-2 2-2 3h-1 0c1 1 1 0 1 1-2 0-3-1-4 0s-1 1-2 1h0 0 0l-2-1c-1 2-2 2-4 2-1 0-2 0-2-1h-1-3 0c-1 1-7 1-9 2-1-1-1 0-2 0l-1 1c-2 1-4 3-6 3-2 1-4 3-5 4s-2 3-3 3h-1l1-1c-1-2-1-3-3-5 0-1 0-1-1-2 0 0 0-1-1-1 1-1 2 0 3-1 0 0 1-2 0-3 0 0 0-1-1-1v-3h-4s0-1-1-1v-3c1-1 2-1 2-2l2-1c1-1 2-1 2-1l1-1h-1v-2l-1-1h-1 0c-1 0-1 0-2 1l-1-1c0-1-1-2 0-3h0c1-1 2-3 3-4l-1-1h0c1-1 1-1 1-2v-2c0-1 0-1-1-2h1l-1-1h0v-3c0-1-1-2-1-3l2 2 1-1h0l3-5c0-1 1-2 1-3z" class="L"></path><path d="M434 353c0 1 0 2 1 2v3c-1-2-1-2-3-3v-1c1-1 1-1 2-1zm-3 17c1-1 1-2 2-3l2 1h-2v3l-1 1h-1l-1 1 1-3z" class="C"></path><path d="M449 350h-3v-2c1-1 1-1 2-1 1-1 1 0 2 0l-1 1v2z" class="J"></path><path d="M454 349h2c1 1 2 1 3 2h-9c1-1 1-1 1-2l2 2 1-1v-1z" class="I"></path><path d="M449 350v-2l1-1c1 1 2 2 4 2h0v1l-1 1-2-2c0 1 0 1-1 2h-1v-1z" class="G"></path><path d="M428 372l2-3 1 1-1 3c-1 3-3 5-4 7-1-1-1-2-1-3 2-1 2-2 3-3v-2h0z" class="X"></path><path d="M442 335h1l1 1c1-1 1-1 2-1l2 1c-2 2-3 4-4 6-1-1-1-1-2-1v-1l-2-2v-1c1 1 2 1 2 1l1-1-1-1v-1z" class="H"></path><path d="M440 341h2c1 0 1 0 2 1v1c-1 2-3 7-4 8l-1-1s0-1 1-1c-1-2-1-3 0-4v-3-1z" class="V"></path><path d="M440 341h2c1 0 1 0 2 1v1l-4-1v-1z" class="J"></path><path d="M453 334c1-1 2-1 3-1l-1 2-2 3c0 1 0 2-1 3h-1v2-1l-1-1c0-1-1-2 0-3 0-2 2-4 3-4z" class="M"></path><path d="M453 334c1-1 2-1 3-1l-1 2-2 3v-1c1-2 1-2 0-3z" class="X"></path><path d="M455 364h-2l1-1-2-2h-1v-1h1v-1c-1 0-1-1-2-1h0l-1-1c1-1 1-2 2-2h2c2-1 1 0 3 0v-1h3l-1 2v2h0c-1 1-1 1-2 1h-2 0c1 1 1 1 2 1 0 1-1 2-2 2 0 1 0 1 1 2z" class="h"></path><path d="M427 364v-1c5 0 7 1 11 3h1 0c-1 0-1 1-1 2l-1 1c-1 0-2-1-2-1l-2-1c-1 1-1 2-2 3l-1-1-2 3c0-1 0-2-1-3 0-2 0-2-1-3 0-1 0-1 1-2h0z" class="Y"></path><path d="M430 369c0-1 0-2 1-3h1l-1 1h1c0 1 0 1 1 0-1 1-1 2-2 3l-1-1z" class="V"></path><path d="M432 366c2 0 5 1 6 2l-1 1c-1 0-2-1-2-1l-2-1c-1 1-1 1-1 0h-1l1-1z" class="Z"></path><path d="M426 364h0 1 0c-1 1-1 1-1 2 1 1 1 1 1 3 1 1 1 2 1 3h0v2c-1 1-1 2-3 3 0 1 0 2 1 3l-1 1c-1-2-1-3-3-5 0-1 0-1-1-2 0 0 0-1-1-1 1-1 2 0 3-1 0 0 1-2 0-3 0 0 0-1-1-1v-3h-4v-1h3 5z" class="g"></path><path d="M426 364h0 1 0c-1 1-1 1-1 2 1 1 1 1 1 3 0-1-1-1-1-1h-1v-1c-1-2-2-2-4-3h5z" class="e"></path><path d="M422 365c2 2 3 2 3 4v8c0 1 0 2 1 3l-1 1c-1-2-1-3-3-5 0-1 0-1-1-2 0 0 0-1-1-1 1-1 2 0 3-1 0 0 1-2 0-3 0 0 0-1-1-1v-3zm4-10l1-1c2 1 2 0 3 0l1 1h1c2 1 2 1 3 3l1 2h0c1 0 3 2 3 3v3h0-1c-4-2-6-3-11-3v1h-1 0-5-3v1s0-1-1-1v-3c1-1 2-1 2-2l2-1c1-1 2-1 2-1h1c1 0 2-1 2-2z" class="C"></path><path d="M419 362c5-2 12-1 17 2 1 0 2 1 3 2h-1c-4-2-6-3-11-3v1h-1 0c-3-3-5 0-7-1v-1z" class="U"></path><path d="M426 355l1-1c2 1 2 0 3 0l1 1h1c2 1 2 1 3 3l1 2h0c1 0 3 2 3 3h-1l-1-1v-1h-1c-1 0-1 0-2-1h0c-1 0-2 0-3 1l-1-2h0v-1c-2 0-7 0-9 2-1 0-2 1-3 2h1v1c2 1 4-2 7 1h-5-3v1s0-1-1-1v-3c1-1 2-1 2-2l2-1c1-1 2-1 2-1h1c1 0 2-1 2-2z" class="W"></path><path d="M432 355c2 1 2 1 3 3l1 2h0c1 0 3 2 3 3h-1l-1-1v-1h-1c-1 0-1 0-2-1h0c-1 0-2 0-3 1l-1-2h0v-1h1l2 2v-1s-1-1-1-2h-1-1l-1-1v-1l2 1v-1h1z" class="h"></path><path d="M464 353h1c1 0 1 1 1 2h-1c0 1 0 1 1 1l-1 1-1-1c-1 0-1 0-1 1v2 1l2 1c0-1 1-2 2-2h0 2v1h0v2l-1 1h1 1c0 1 0 1 1 1-1 1-2 2-2 3h-1 0c1 1 1 0 1 1-2 0-3-1-4 0s-1 1-2 1h0 0 0l-2-1c-1 2-2 2-4 2-1 0-2 0-2-1h-1c1 0 2-1 3-2h-2v-1h2l-2-2c-1-1-1-1-1-2 1 0 2-1 2-2-1 0-1 0-2-1h0 2c1 0 1 0 2-1h0v-2l1-2c2 0 3-1 5-1z" class="c"></path><path d="M464 353h1c1 0 1 1 1 2h-1c0 1 0 1 1 1l-1 1-1-1c-1 0-1 0-1 1-1 1-3 2-4 2l1-1v-1h-1l-1 1v-2l1-2c2 0 3-1 5-1z" class="Y"></path><path d="M465 361c0-1 1-2 2-2h0 2v1h0v2l-1 1h1 1c0 1 0 1 1 1-1 1-2 2-2 3h-1 0c1 1 1 0 1 1-2 0-3-1-4 0s-1 1-2 1h0 0c0-1 1-1 1-1v-2c-1 0-1 0-1 1h-1l-1-1c-1-1-3-2-4-3h1l1-1h-1-1v-1h1 1 1c1-1 2-1 3-1l2 1z" class="X"></path><path d="M458 363l1-1h-1-1v-1h1 1 1c0 1 1 1 1 2s0 1-1 1-1 0-2-1z" class="W"></path><path d="M465 361c0-1 1-2 2-2h0 2v1h0-2v1h-1c0 1 1 2 0 3h-1 0c-1-1-1-1-2-1h-1c-1-1 0-1 0-2l1-1 1 1h1z" class="H"></path><path d="M467 361v-1h2v2l-1 1h1 1c0 1 0 1 1 1-1 1-2 2-2 3h-1 0c1 1 1 0 1 1-2 0-3-1-4 0s-1 1-2 1h0 0c0-1 1-1 1-1v-2c-1 0-1 0-1 1h-1l-1-1 1-1v-2c2 1 1 1 2 2 1 0 1 0 1-1h1c1-1 0-2 0-3h1z" class="d"></path><path d="M467 361v-1h2v2l-1 1h-1v-2z" class="J"></path><path d="M469 363h1c0 1 0 1 1 1-1 1-2 2-2 3-1-1-2-1-2-1-1 0-1-1-1-2 1 0 2-1 3-1z" class="Y"></path><path d="M424 338h0c1 1 1 1 2 3h0v1c1 1 1 3 2 4 1 0 2-1 3-2 1 0 2 0 3 1v2l1-1h1 0 2l2-1c-1 1-1 2 0 4-1 0-1 1-1 1-1 0-1-2-3-2-1 1-2 1-2 1l-1 1 1 2v1c-1 0-1 0-2 1v1h-1l-1-1c-1 0-1 1-3 0l-1 1c0 1-1 2-2 2h-1l1-1h-1v-2l-1-1h-1 0c-1 0-1 0-2 1l-1-1c0-1-1-2 0-3h0c1-1 2-3 3-4l-1-1h0c1-1 1-1 1-2v-2c0-1 0-1-1-2h1c1-1 1 0 2 0l1-1z" class="Q"></path><path d="M424 356c0-1 0-1 1-2l1 1c0 1-1 2-2 2h-1l1-1z" class="e"></path><path d="M421 346c1 1 1 1 1 2 0 0 1 0 1 1v1h-1v-1c-1 1-2 2-3 2l-1-1c1-1 2-3 3-4z" class="h"></path><path d="M425 349h1v1c1 0 2 0 4 1 0 0 0 1 1 2h0c-1 0-2-1-2-1l-1 1h-1v-2h-1l-1 2h-1c-1-1-1-1-1-2l1-1c0 1 0 1 1 1h1c-1-1-1-1-1-2z" class="b"></path><path d="M438 346l2-1c-1 1-1 2 0 4-1 0-1 1-1 1-1 0-1-2-3-2-1 1-2 1-2 1h-3v-1h3c1-1 1-1 1-2h1 0 2z" class="Y"></path><path d="M438 346l2-1c-1 1-1 2 0 4l-1-1c-1 0-2-1-3-2h2z" class="i"></path><path d="M424 338h0c1 1 1 1 2 3h0v1c1 1 1 3 2 4 1 0 2-1 3-2 1 0 2 0 3 1v2c-1 0-1-1-1-1h-1c-1 0-1 1-2 0h0l-1 1s-1 0-1 1v1h0c-1 0-2 0-3-1 0-2-1-3-1-5l-2 1-1-1v-2c0-1 0-1-1-2h1c1-1 1 0 2 0l1-1z" class="h"></path><path d="M424 338h0v5h0l-2 1-1-1v-2c0-1 0-1-1-2h1c1-1 1 0 2 0l1-1z" class="B"></path><path d="M426 325h0c0-1 1-2 1-2l1 1c-1 0-1 1-1 2l1 1h1v-1l1-1 1 1s-1 1-1 2v1c1 0 2-1 3 0 0 1 0 1 1 2-1 1-1 1-1 3h0c1 1 2 3 3 4h0c1 1 2 1 3 1l1-1 2 2v1h-2v1 3l-2 1h-2 0-1l-1 1v-2c-1-1-2-1-3-1-1 1-2 2-3 2-1-1-1-3-2-4v-1h0c-1-2-1-2-2-3h0l-1 1c-1 0-1-1-2 0l-1-1h0v-3c0-1-1-2-1-3l2 2 1-1h0l3-5c0-1 1-2 1-3z" class="K"></path><path d="M435 342c-1 0-1 0-1-1-1 0-2-1-2-2-1-1-1-2-1-2l1-1h0l1 3c1 1 2 1 2 2v1z" class="Q"></path><path d="M435 341c1 0 2 1 3 1l2-2v1 1 3l-2 1v-1h0l-3-3v-1z" class="C"></path><path d="M426 325h0c0-1 1-2 1-2l1 1c-1 0-1 1-1 2l1 1h1v-1l1-1 1 1s-1 1-1 2h-1-1v1h1v1l-1 1-1-1h0v2c0 1-1 1-2 2v1c-1 0-1 0-1 1v2l-1 1c-1 0-1-1-2 0l-1-1h0v-3c0-1-1-2-1-3l2 2 1-1h0l3-5c0-1 1-2 1-3z" class="f"></path><path d="M419 332l2 2 1-1h0c0 1 1 3 0 4-1 0-1-1-2-2 0-1-1-2-1-3z" class="C"></path><path d="M458 333c0-1 1-1 1-2h1c1 1 0 2 0 4 1-1 1-2 2-2 2 0 4 1 6 3 1-1 2-1 2 0l3 1v1c0 1 1 1 1 2h0v3h-1l-1 1v1h2 1 0l1 1-1 1h-1-1l-3 2-1 1v-1c-1 0-1 0-2 1v1 1l1 1-1 1h0v2h-1c-1 0-1 0-1-1h1c0-1 0-2-1-2h-1l1-1v-1l-2-1c-1 1-2 1-3 1h0-1c-1-1-2-1-3-2h-2 0c1-1 2 0 4-2-3-1-5-2-7-4v-2h1c1-1 1-2 1-3l2-3 1-2h2 0z" class="E"></path><path d="M468 336c1-1 2-1 2 0v4c-1 0-1 1-2 2v-2c0-2 1-3 0-4z" class="C"></path><path d="M468 340v2c1-1 1-2 2-2 0 1-1 2-2 4 0 0-1 1-1 2l1 1c-3 2-5 3-8 4h-1c-1-1-2-1-3-2h-2 0c1-1 2 0 4-2h2c4-1 6-4 8-7z" class="Q"></path><path d="M456 349c5 0 8-1 12-5 0 0-1 1-1 2l1 1c-3 2-5 3-8 4h-1c-1-1-2-1-3-2z" class="H"></path><defs><linearGradient id="D" x1="456.44" y1="332.973" x2="461.776" y2="340.644" xlink:href="#B"><stop offset="0" stop-color="#080508"></stop><stop offset="1" stop-color="#242723"></stop></linearGradient></defs><path fill="url(#D)" d="M458 333c0-1 1-1 1-2h1c1 1 0 2 0 4h0v4 5l-1 1h0l-1-1v2h-1c-2-1-1-3-2-5l-1 1s0 1-1 1h-2v-2h1c1-1 1-2 1-3l2-3 1-2h2 0z"></path><path d="M456 333h2 0c-1 3-1 8 0 11v2h-1c-2-1-1-3-2-5h1c1-1 0-4 0-5 1 0 1-1 1-1l-1-1-1 1 1-2z" class="O"></path><path d="M455 335l1-1 1 1s0 1-1 1c0 1 1 4 0 5h-1l-1 1s0 1-1 1h-2v-2h1c1-1 1-2 1-3l2-3z" class="T"></path><path d="M470 336l3 1v1c0 1 1 1 1 2h0v3h-1l-1 1v1h2 1 0l1 1-1 1h-1-1l-3 2-1 1v-1c-1 0-1 0-2 1v1 1l1 1-1 1h0v2h-1c-1 0-1 0-1-1h1c0-1 0-2-1-2h-1l1-1v-1l-2-1c-1 1-2 1-3 1h0c3-1 5-2 8-4l-1-1c0-1 1-2 1-2 1-2 2-3 2-4v-4z" class="C"></path><path d="M470 336l3 1v1c0 3-3 7-5 9l-1-1c0-1 1-2 1-2 1-2 2-3 2-4v-4z" class="I"></path><path d="M443 290c3 0 6 1 9 2l14 3 2-2c7-5 13-11 20-15 5-3 9-6 15-7 2 0 3 0 4 1v1c0 2 0 4-1 6 0 1 0 2-1 3l-1-2v4c0 1 0 2 1 2h1 1l-2 9c0 3-1 8-3 10 0 6-2 11-4 16 0 2-1 4-2 6 0 4 2 7 2 10 0 5 0 9-1 14-1 2-1 4-1 5-1 3-2 6-3 8h0-1c0 1 0 2-1 3v1l-1 1c0 1-1 4-2 5l-2-1h0c-1-1-1-1-2-1-1-2-2-1-4-2 0-1 1-1 1-2 2 0 4-1 5-2v-1l-2-2c-1 0-2 1-3 1s-2 1-4 0l1-1h-1c-1 0-2 0-2 1h-3-1c-1 0-1 0-1-1h-1-1l1-1v-2h0v-1h-2 0c-1 0-2 1-2 2l-2-1v-1-2c0-1 0-1 1-1l1 1 1-1h1v-2h0l1-1-1-1v-1-1c1-1 1-1 2-1v1l1-1 3-2h1 1l1-1-1-1h0-1-2v-1l1-1h1v-3h0c0-1-1-1-1-2v-1l-3-1c0-1-1-1-2 0-2-2-4-3-6-3l1-1 1-1c-2-1-4 0-6-1-2 0-5 1-6 2l-4 4-2-1c-1 0-1 0-2 1l-1-1h-1v1l1 1-1 1s-1 0-2-1v1l-1 1c-1 0-2 0-3-1h0c-1-1-2-3-3-4h0c0-2 0-2 1-3-1-1-1-1-1-2-1-1-2 0-3 0v-1c0-1 1-2 1-2l-1-1-1 1v1h-1l-1-1c0-1 0-2 1-2l-1-1s-1 1-1 2h0l-1-1h-1v-2s1-2 2-3c0-1 1-2 1-3 0 0 0-1 1-1v-1h-1v-2l-1-1h1 2c1 0 1 0 2-1l2-1h0c0-2 0-2 1-3l-1-1s-1 0-1 1h-1l1-1c1-1 2-2 2-4 1-1 1-2 1-4l1-5h1 1v1c-1 1-1 2 0 3h1c0-1 0-1 1-2s1-2 1-4h0-1 3z" class="L"></path><path d="M491 363l1 1c0 1 0 2-1 3v1l-1 1v-1s-1 0-1-1c1-1 1-3 2-4z" class="W"></path><path d="M474 345s1-1 1-2c0 0 0-1 1-1h1c0 1 1 2 1 2l-2 2h0l-1-1h0-1z" class="P"></path><path d="M442 310c1 0 2 0 3 1v1l1 1c-1 0-1 0-2-1v1l-1 1c0-1-1-1-1-2v-2z" class="U"></path><path d="M500 277h0c1 0 1-1 1-1h3v1 2c-1 0-1 1-1 1v-1c-1-1-1-1-2-1h0l-1-1z" class="N"></path><path d="M481 339c2 1 4 2 5 3l-1 2v-1h-2v-1s-1 0-1-1h-1c-1 0 0-1-1-1l1-1zm-33-21h0 2l1 1h-1l-1 1c0 2 0 2-1 3s-2 1-3 0c2-1 2-2 3-4v-1z" class="F"></path><path d="M486 342h0c2 1 2 2 3 3s1 2 2 3h-2c-1 0-1-1-1-2-1-1-2-2-3-2l1-2z" class="B"></path><path d="M435 326l1-1c0-1 1-4 2-5l1-1h1c-1 2-1 3 0 4-1 0-1 0-2 1v1h-1l-1 1h1-2z" class="b"></path><path d="M449 308h0c0 2-1 4-1 6v4 1c-1-1-2-1-3-2 2-1 1-2 2-4l1-2c0-1 0-2 1-3z" class="U"></path><path d="M448 307l1 1c-1 1-1 2-1 3l-1 2c-1 2 0 3-2 4 0-1 0-1 1-2h0c-1-1-1-2-2-2v-1c1 1 1 1 2 1l-1-1v-5h1v1l1 1 1-2z" class="F"></path><path d="M447 331l2 1h0l1-1c1 0 1 0 2 1l-4 4-2-1c-1 0-1 0-2 1l-1-1h1 0l-1-1 1-1v1l3-3z" class="U"></path><path d="M437 326h-1l1-1h1v-1c1-1 1-1 2-1v3 2l1 1v1h-1l-1 1 1 1v1l-3-3 1-1-1-1v-2z" class="g"></path><path d="M428 319h3 0v1l-2 1 1 2h-2v1l-1-1s-1 1-1 2h0l-1-1h-1v-2s1-2 2-3c1 0 1 1 2 1v-1z" class="F"></path><path d="M444 313c1 0 1 1 2 2h0c-1 1-1 1-1 2 1 1 2 1 3 2-1 2-1 3-3 4h-1c0-2 1-2 2-3l-1-1c0 1-1 1-1 2h0-1v-2c0-1 0-1 1-2l-1-1h-1c0-1 0-1 1-2l1-1z" class="N"></path><path d="M490 315h2 1 2 1l1 1c-1 0-1 0-1 1-1 1-1 4-3 5 0 1-1 1-2 1 1-1 3-3 3-4 0 0-1-1-1-2l-1 1 1 1c0 1-1 2-2 3h0-1c1-1 2-2 2-3l-1-1-1 1-1-1v-1h1 1l-1-2z" class="M"></path><path d="M429 311c1 0 1 0 2-1l2-1c0 2 0 2-1 4-1 0-1 1-1 1-1 0 0 0-1 1 0 0-1 2-2 2v2 1c-1 0-1-1-2-1 0-1 1-2 1-3 0 0 0-1 1-1v-1h-1v-2l-1-1h1 2z" class="Q"></path><path d="M440 333v-1l-1-1 1-1h1v2l1 1v2 1l1 1-1 1s-1 0-2-1v1l-1 1c-1 0-2 0-3-1h0v-2c1 0 1 0 1-1v-1-1h2 1z" class="B"></path><path d="M445 299c1 1 1 2 1 3v4l1 1h1l-1 2-1-1v-1h-1v5-1c-1-1-2-1-3-1v-3h1l-1-1v-1c2-1 2-4 3-6z" class="X"></path><path d="M435 326h2v2l1 1-1 1 3 3h-1-2v1 1c0 1 0 1-1 1v2c-1-1-2-3-3-4h0c0-2 0-2 1-3v-1-1c1 0 1-1 1-1h0v-2z" class="C"></path><path d="M492 352h2l1-1v-2-1h1 0v2l1 1c-1 2-1 4-1 5l-3 8h0-1l-1-1c1 0 1-1 1-1l-1-1c1-1 0-2 1-3 0-2-1-4 0-6z" class="H"></path><path d="M481 337v-1l-3 1-2-2-1-1 2-1c1 0 2 0 3 1h1c1 0 1-1 1-1h1v2h0c1-1 1-1 2-1h2l1 1c1 0 2-1 3-1v2 1c1 0 2 1 2 2h0c-2-1-3-2-4-3l-2 2h-1c-1-1-4-1-5-1z" class="d"></path><path d="M450 295s1-1 1-2h0l1 1 2-1h0c0 1 0 1-1 2l1 1v2l1 1v1c-1 0-1-1-2 1-1 1-1 2-2 3v1h-1 0v-1-2h-1 0v1c-1 1-1 1 0 2-1 0-2 1-2 2l-1-1v-4c1-1 1-2 1-3 1-1 1-2 2-2 0-1 0-1 1-2z" class="F"></path><path d="M452 294l2-1h0c0 1 0 1-1 2l1 1v2l1 1v1c-1 0-1-1-2 1-1 1-1 2-2 3-1-1-1-2 0-3v-2c0-1 2-2 2-3l-1-1-1 1v2c-1 1-1 1-2 1v-1c0-1 1-2 2-3h1v-1z" class="e"></path><path d="M474 300v-1l-1 1h0v-2h-1c0 1-1 2-1 2v-1c0-2 2-4 4-5l1-1h-1c1-3 4-4 6-5l5-5c2-1 3-1 4-2 0-1 1-1 1-1h1c1 0 1-1 2-1l1 1h1c-2 2-4 3-5 5v-2h0-1c-2 2-5 5-8 6-2 1-4 4-6 5 0 2 0 2-1 3-1 0-1 0-2 1v1l2-1v1s0 1-1 1z" class="d"></path><path d="M491 336c2 0 3 1 5 1l-1 1v1h1c1 1 1 1 1 2h-1c-1 0-2 1-3 1l-1 2h1l-1 1h-1 0v-1-1c-1 0-1 0-1 1l-1 1c-1-1-1-2-3-3h0c-1-1-3-2-5-3v-1-1c1 0 4 0 5 1h1l2-2c1 1 2 2 4 3h0c0-1-1-2-2-2v-1z" class="C"></path><path d="M481 338c1 0 5 1 6 1l1 1h1v1l-1 1h-2 0c-1-1-3-2-5-3v-1z" class="U"></path><path d="M489 340v-2h0l1 1h0c0 1-1 1-1 1 1 1 2 1 3 2h1 0l-1 2h1l-1 1h-1 0v-1-1c-1 0-1 0-1 1l-1 1c-1-1-1-2-3-3h2l1-1v-1z" class="X"></path><path d="M447 331h1s1 0 1-1c2-1 3-2 5-2 2-1 4-1 6 0h1 1 1s1 0 1 1l1-1h1v2h0l1-1h1c2 0 4 2 6 3v3h-1v-1h-3 0l1 1s1 1 1 2h1l-3-1c0-1-1-1-2 0-2-2-4-3-6-3l1-1 1-1c-2-1-4 0-6-1-2 0-5 1-6 2-1-1-1-1-2-1l-1 1h0l-2-1z" class="d"></path><path d="M464 331l2 1h2c2 1 3 2 5 2h-3 0l1 1s1 1 1 2h1l-3-1c0-1-1-1-2 0-2-2-4-3-6-3l1-1 1-1z" class="P"></path><path d="M498 316l2-6c1-2 1-3 2-5 0 6-2 11-4 16 0 2-1 4-2 6 0 4 2 7 2 10 0 5 0 9-1 14l-1-1v-2h0-1v1 2l-1 1h-2c-1-1-1-3-1-4-1-1-1-2-2-3l1-1c0-1 0-1 1-1v1 1h0 1l1-1h-1l1-2c1 0 2-1 3-1h1c0-1 0-1-1-2h-1v-1l1-1c-2 0-3-1-5-1v-2c0-1 1-1 1-1l3-3c0-3 1-5 2-8 0-2 1-4 1-6z" class="Z"></path><path d="M493 342c1 0 2-1 3-1h1c-1 2 0 4-1 6h-2c0-1-1-2-1-3h-1l1-2z" class="F"></path><path d="M495 330c0 2 2 6 1 7-2 0-3-1-5-1v-2c0-1 1-1 1-1l3-3z" class="K"></path><path d="M443 290c3 0 6 1 9 2l14 3 2-2c7-5 13-11 20-15 5-3 9-6 15-7 2 0 3 0 4 1v1c-6-1-11 1-16 4-8 6-16 11-23 17-2 2-3 4-5 6l3-5c-3 0-5 0-7-1h-1l-1 1h-1v-1h0l-2 2-1-1c1-1 1-1 1-2h0l-2 1-1-1h0c0 1-1 2-1 2-1 1-1 1-1 2-1 0-1 1-2 2 0 1 0 2-1 3 0-1 0-2-1-3v-2s-1 0-2-1l-1 1c0-1-1-1-1-2v-1c1-2 1-2 0-4h0-1 3z" class="N"></path><path d="M445 293h1l1-1h1v2c-1 1-2 2-2 3h-1s-1 0-2-1c1-1 2-1 2-3z" class="F"></path><path d="M441 295c1 0 1 0 1-1s1-1 1-2c1-1 1-1 2-1v2c0 2-1 2-2 3l-1 1c0-1-1-1-1-2z" class="K"></path><path d="M445 297h1c0-1 1-2 2-3v1h2c-1 1-1 1-1 2-1 0-1 1-2 2 0 1 0 2-1 3 0-1 0-2-1-3v-2z" class="Z"></path><path d="M476 346h0l2-2 2 1h1v1c1 0 2 0 2 1s1 1 2 2v3h0c-1 1-1 2-1 3-1 1-1 1 0 2 0 1 0 1-1 2h-2l-1 1h2 1c0 1 1 2 1 3h0c-1 0-2 1-3 1s-2 1-4 0l1-1h-1c-1 0-2 0-2 1h-3-1c-1 0-1 0-1-1h-1-1l1-1v-2h0v-1h-2 0c-1 0-2 1-2 2l-2-1v-1-2c0-1 0-1 1-1l1 1 1-1h1v-2h0l1-1-1-1v-1-1c1-1 1-1 2-1v1l1-1 3-2h1 1l1-1z" class="F"></path><path d="M467 354h0l1-1-1-1v-1-1c1-1 1-1 2-1v1l1-1 1 1v2h0c1-1 2-1 3-2v2l-1 1h0c-1-1-1-1-2-1v1c-1 1-2 1-4 1z" class="Q"></path><path d="M467 356c0 1 1 1 1 2h1v-3h1v2h1 0c0-1 1-1 1-1h1c0 1-1 2-2 2l-1-1c0 1-1 2-1 2h-2 0c-1 0-2 1-2 2l-2-1v-1-2c0-1 0-1 1-1l1 1 1-1h1z" class="W"></path><path d="M476 346h0l2-2 2 1-1 3h1l-1 1s0-1-1-1c-1 1-1 1-2 1v2h0l-1-1h0-1c-1 1-2 1-3 2h0v-2l-1-1 3-2h1 1l1-1z" class="Y"></path><path d="M485 349v3h0c-1 1-1 2-1 3-1 1-1 1 0 2 0 1 0 1-1 2h-2l-1 1h2 1c0 1 1 2 1 3h0c-1 0-2 1-3 1s-2 1-4 0l1-1h-1c-1 0-2 0-2 1h-3-1c-1 0-1 0-1-1h-1-1l1-1v-2h0v-1s1-1 1-2l1 1c1 0 2-1 2-2h1 2l1-2h1c1-1 2-2 4-2v-2h0l1 1c1 0 1-1 2-2z" class="C"></path><path d="M485 349v3h0c-1 1-1 2-1 3l-2-1c1-1 1-1 1-2h0c-1 0-1 1-1 2s0 1 1 1v1c-2 0-2 0-3 1h-1c0-1-1-2-1-2h0l-1-1h1c1-1 2-2 4-2v-2h0l1 1c1 0 1-1 2-2z" class="d"></path><path d="M477 354l1 1h0s1 1 1 2h1l-3 2-1 1v2l1 1c-1 0-2 0-2 1h-3-1c-1 0-1 0-1-1h-1-1l1-1v-2h0v-1s1-1 1-2l1 1c1 0 2-1 2-2h1 2l1-2z" class="Z"></path><path d="M470 363c0-1 1-1 2-1v2h-1c-1 0-1 0-1-1z" class="i"></path><path d="M472 362l4-2v2l1 1c-1 0-2 0-2 1h-3v-2z" class="f"></path><path d="M477 354l1 1h0s1 1 1 2h1l-3 2-1-1c-2 0-2 1-3 2-1-1-1-1-1-2h-1c-1 1 0 2-1 3l-1-1v-1s1-1 1-2l1 1c1 0 2-1 2-2h1 2l1-2z" class="V"></path><path d="M496 280l2-1c1-1 0-1 1-2h1l1 1h0c1 0 1 0 2 1v1h1v4c0 1 0 2 1 2h1 1l-2 9c0 3-1 8-3 10-1 2-1 3-2 5l-2 6c-1 1-1 2-2 3v-2c0-1 0-1 1-1l-1-1h-1-2-1-2-1v-1c-1 0-1 0-2-1v-1-1l-2 2h-1c1-2 1-2 1-3l-1-1-1 1h0v-3-1l-2 2v-3l-1 1-1-1h-1v-2h-2v-1l1-2c-1 0-1 1-3 1v-1c1 0 1-1 1-1v-1l-2 1v-1c1-1 1-1 2-1 1-1 1-1 1-3 2-1 4-4 6-5 3-1 6-4 8-6h1 0v2c1-2 3-3 5-5z" class="R"></path><path d="M503 293v2h0 1c0-1 0-2 1-3v1 2c0 3-1 8-3 10h0c-1-4 1-9 1-12z" class="e"></path><path d="M504 284c0 1 0 2 1 2h1 1l-2 9v-2-1c-1 1-1 2-1 3h-1 0v-2c0-1 1-2 1-3-1-2 0-4 0-6z" class="Y"></path><path d="M217 389c0-1 3-3 3-4v-6-14-49-113-67c-20 3-39 8-56 17-28 14-50 34-66 61-5 9-9 19-13 29l-4 15H60l5-164h425l8 163h-21c-6-20-14-38-26-55-18-27-46-47-77-57-12-4-24-7-37-9v232 1 6c-1 4-1 8-1 12v24l1 27c0 4-1 9 0 13v51c0 10-1 20 0 31 2 22 11 41 27 56 15 15 36 25 57 29 9 2 19 3 29 3h3v7 18H104v-25c20 0 39-3 56-12 4-1 7-3 10-5 1-1 2-1 4-2 1-1 3-2 5-3 4-3 9-6 12-10 11-11 20-23 25-38 4-16 4-32 4-48v-34-80-1l-2 2-1-1z" class="a"></path><path d="M410 621l6 1c-2 1-3 1-5 1-1-1-2-1-4-1l3-1z" class="Y"></path><path d="M401 618l9 3-3 1c-1-1-4-1-5-2 0-1-1-2-1-2z" class="c"></path><path d="M402 620c1 1 4 1 5 2 2 0 3 0 4 1l1 1h-5c-1-1-2-1-2-1-2 0-3-1-4-2l1-1z" class="f"></path><path d="M352 587c0-1 1-1 1-2l8 8c-1 1-1 1-2 1l-7-7z" class="F"></path><path d="M370 136c1 0 4 1 5 2s1 1 2 1l1 1h2v1l-12-4c1 0 1 0 2-1z" class="Q"></path><path d="M369 601l8 5v2l-3-2-5-3h1l-1-1v-1z" class="C"></path><path d="M361 593a30.44 30.44 0 0 0 8 8v1l1 1h-1c-3-2-7-6-10-9 1 0 1 0 2-1z" class="B"></path><path d="M487 144l1 21c-2-2-1-6-2-9v-3-6h1v-3z" class="K"></path><path d="M392 614l6 3h0l3 1s1 1 1 2l-1 1s-1-1-2-1v1l-6-4-1-3z" class="c"></path><path d="M398 617l3 1s1 1 1 2l-1 1s-1-1-2-1c0-1-1-2-2-3h1z" class="Y"></path><path d="M418 639h10 3l4 1h-1s-1 0-1 1h-16c-1-1-1-1-1-2h2z" class="h"></path><path d="M454 195l1-1 5 7 1 1 3 6c0 1 0 2-1 2-2-5-6-10-9-15z" class="f"></path><path d="M71 114v-4c1 1 1 2 1 3v7 9c0 1-1 3 0 4-1 1-1 3-1 5 0 0 0 1-1 1v-7l1-18z" class="B"></path><defs><linearGradient id="E" x1="435.942" y1="641.845" x2="444.803" y2="637.865" xlink:href="#B"><stop offset="0" stop-color="#565355"></stop><stop offset="1" stop-color="#6b6c6c"></stop></linearGradient></defs><path fill="url(#E)" d="M447 626l1 1v14h-15c0-1 1-1 1-1h1l-4-1h10 1 3 1v-6c1 0 1-1 1-1 0-2 1-3 0-5-1 0 0 0 0-1z"></path><path d="M226 531c0 7-1 14-3 20l-1 2h-2l5-20v1c1-1 1-2 1-3z" class="Q"></path><defs><linearGradient id="F" x1="378.291" y1="612.592" x2="389.131" y2="610.143" xlink:href="#B"><stop offset="0" stop-color="#2f3030"></stop><stop offset="1" stop-color="#524f4f"></stop></linearGradient></defs><path fill="url(#F)" d="M377 606c5 3 10 6 15 8l1 3h-2c-1 0-2-1-3-2l-11-7v-2z"></path><path d="M447 627c1 2 0 3 0 5 0 0 0 1-1 1v6h-1-3-1v-1h-3 0c2 0 3 0 4-1 1-3 2-5 1-7h3l1-3z" class="W"></path><path d="M68 227v9 2l1-2c-1 4-1 9 0 13v1 1c1 1 1 1 2 1 2 1 3 1 5 1-2 1-8 0-10 0l2-26z" class="b"></path><path d="M68 214c1-2 0-4 0-6h1c0 8 1 17 0 25v3l-1 2v-2-9-13z" class="C"></path><defs><linearGradient id="G" x1="149.477" y1="640.968" x2="154.618" y2="638.834" xlink:href="#B"><stop offset="0" stop-color="#636065"></stop><stop offset="1" stop-color="#7b7b7a"></stop></linearGradient></defs><path fill="url(#G)" d="M147 637c1 0 2 0 2 1 2 1 5 1 7 2 2 0 4 0 6 1h15 9 3c-2 1-5 0-7 0h-11-30l2-1c1-1 2 0 4 0v-3z"></path><path d="M69 179h0v-5c2 2 0 14 1 17v12c0 1 0 4-1 5h0-1c0 2 1 4 0 6v-16l1-19z" class="b"></path><path d="M68 198l1-3v4 9h0-1c0 2 1 4 0 6v-16z" class="h"></path><path d="M490 241v-4h1l1 15h-9l-1-1 1-1 1-1c1 0 1-1 2-2h4v-1-5z" class="Q"></path><path d="M490 246v3h-6c1 0 1-1 2-2h4v-1z" class="I"></path><path d="M346 129l2 1c6 3 13 3 20 6h2c-1 1-1 1-2 1l-4-1-19-4h0 2 1c-1 0-2-1-2-1h0v-2z" class="C"></path><path d="M368 136h2c-1 1-1 1-2 1l-4-1h4z" class="f"></path><path d="M482 149h1c1 1 1 3 2 5l1-1v3c1 3 0 7 2 9v10 3-3c0-1 0-2-1-2v3l-1-6c0-3 0-5-1-7l-3-14z" class="U"></path><path d="M486 156c1 3 0 7 2 9v10 3-3c0-1 0-2-1-2v3l-1-6v-14z" class="b"></path><path d="M405 637c1-1 1-2 2-3l1 1c0 1 1 2 2 3 2 1 6 1 8 1h-2c0 1 0 1 1 2h-24c2-1 4 0 6 0 1-1 2-2 3-4h0 1c1 0 1-1 2-1v1z" class="W"></path><path d="M405 637c1-1 1-2 2-3l1 1c0 1 1 2 2 3-2 0-3 0-5-1z" class="B"></path><defs><linearGradient id="H" x1="132.715" y1="641.012" x2="132.446" y2="638.405" xlink:href="#B"><stop offset="0" stop-color="#494043"></stop><stop offset="1" stop-color="#525654"></stop></linearGradient></defs><path fill="url(#H)" d="M147 637h0v3c-2 0-3-1-4 0l-2 1h-21-7c-1-1 0-1-1-2 2 0 5 0 7-1v1c2 1 19 0 22 0h0 3c2 0 2-1 3-2z"></path><path d="M196 592v2c-2 1-3 3-5 5l-10 8c-5 3-11 7-16 9 1-2 4-3 4-5 0-1 2-1 2-2l8-4c6-4 11-8 17-13z" class="c"></path><path d="M225 452c1 6 1 12 1 18v38c1-1 0-4 0-5v-14c0-2 0-4 1-6v30c0 6 0 12-1 18 0 1 0 2-1 3v-1-81z" class="B"></path><path d="M120 641c-3 1-8 1-11 1v-12c0-1 0-3 1-4h1c1 1 3 1 5 1h9-4v1h1-1c-2 0-5 0-7 1-2 2-2 5-2 7v3c1 1 0 1 1 2h7z" class="H"></path><path d="M198 640c2-1 4 0 5 0h15l26-1h1v1l4 1h-60-3c0-1 1-1 1-1h3 8z" class="c"></path><path d="M474 232c1 1 2 2 3 2 0-1 0-2 1-2h0c1 2 3 4 4 7 0 1 2 5 2 6s0 1 2 1v1c-1 1-1 2-2 2l-1 1-1 1h0c-1-3-2-5-3-7-2-4-4-8-5-12z" class="H"></path><path d="M483 250c-1-1-1-3-1-4 1-1 1-1 2-1 0 1 0 1 2 1v1c-1 1-1 2-2 2l-1 1z" class="E"></path><path d="M464 208c3 2 4 6 6 9 0 1 0 2 1 2h1c1 2 2 4 3 7 0 1 1 2 2 3v1h1v2h0c-1 0-1 1-1 2-1 0-2-1-3-2 0-2-2-4-3-7-2-5-6-10-8-15 1 0 1-1 1-2z" class="X"></path><path d="M112 639v-3c0-2 0-5 2-7 2-1 5-1 7-1v1h2c0 1 1 2 1 3s-1 0-2 1c-1-1-2-2-3-2h-1c1 1 3 3 3 5-1 1-1 1-2 1l-3 1h3c-2 1-5 1-7 1z" class="S"></path><path d="M121 629h2c0 1 1 2 1 3s-1 0-2 1c-1-1-2-2-3-2h-1c-1 0-1 0-2-1 2-1 3-1 5-1z" class="G"></path><path d="M116 638l-1-1c-1-1-1-2-1-3 0-2 1-3 2-4 1 1 1 1 2 1 1 1 3 3 3 5-1 1-1 1-2 1l-3 1z" class="T"></path><path d="M416 622c8 3 17 4 25 4h6c0 1-1 1 0 1l-1 3h-3c-2-1-4-1-6-1l-1 1c0-1-1-2-1-2-1-1-2-1-2-1l-15-2c-3 0-6-1-9-1-1 1-3 2-3 3v-1c0-1 1-1 1-2h0 5l-1-1c2 0 3 0 5-1z" class="h"></path><path d="M441 626h6c0 1-1 1 0 1l-1 3h-3c-2-1-4-1-6-1l-1 1c0-1-1-2-1-2-1-1-2-1-2-1h5 3v-1z" class="d"></path><path d="M433 627h5c1 0 2 1 3 1v1h-4l-1 1c0-1-1-2-1-2-1-1-2-1-2-1z" class="N"></path><path d="M414 158h0v-1l1-1c15 10 30 23 40 38l-1 1c-4-5-8-9-12-14-8-9-18-17-28-23z" class="Q"></path><path d="M375 138l1-1 14 6c9 4 17 7 25 13l-1 1v1h0c-4-3-9-5-13-8-3-1-7-3-10-4-3-2-8-3-11-5v-1h-2l-1-1c-1 0-1 0-2-1z" class="h"></path><path d="M410 629v2h0 1c1 1 3 2 4 4v1l-2 1h14 9l2 1h3v1h-10-3-10c-2 0-6 0-8-1-1-1-2-2-2-3v-2c0-2 1-3 2-4z" class="E"></path><path d="M411 631c1 1 3 2 4 4v1l-2 1c-1 0-2-1-2-1-1-1-2-1-2-2s1-2 2-3z" class="B"></path><path d="M101 197v1h0v1c-2 3-4 7-6 11-6 10-11 20-15 31-2 4-2 8-4 12-2 0-3 0-5-1-1 0-1 0-2-1v-1l5 1 8-20 15-28 4-6z" class="Q"></path><path d="M281 638c3 2 14 1 18 1h0c-2 0-8 0-9 1l1 1h-34-8l-4-1v-1h-1 6c4-1 10 0 14-1 2 1 7 1 9 0h4 4z" class="W"></path><path d="M244 639h6 7c0 1-1 1-2 1h2v1h-8l-4-1v-1h-1z" class="h"></path><defs><linearGradient id="I" x1="151.765" y1="614.042" x2="136.669" y2="629.084" xlink:href="#B"><stop offset="0" stop-color="#6f6e6c"></stop><stop offset="1" stop-color="#a3a1a4"></stop></linearGradient></defs><path fill="url(#I)" d="M111 626c5-1 11 0 16-1 6-1 12-3 17-4l13-5c0 1-1 2-2 3l-3 3c-1 2-1 3-1 5l-2-2c-3-2-8 0-11 1l-13 1h-9c-2 0-4 0-5-1z"></path><path d="M141 158c1 0 1 0 1 1-4 2-8 6-12 9-7 6-13 12-19 18l-10 13v-1h0v-1h0c2-4 4-8 6-11 6-7 14-13 21-18 4-4 9-7 13-10z" class="W"></path><path d="M222 553l1-2v4c-1 5-4 10-7 15-1 2-2 5-4 7l-7 7c-3 3-5 7-9 10h0v-2c2-3 6-7 9-10 4-5 7-11 10-17l5-12h2z" class="g"></path><path d="M215 565l5-12h2c0 1-1 2-1 2-1 4-2 7-4 10h0-1v1l-1-1z" class="W"></path><path d="M487 234c0-2 0-4 1-6 1 4 1 9 2 13v5 1h-4v-1c-2 0-2 0-2-1s-2-5-2-6c-1-3-3-5-4-7v-2c1-1 3-1 4-1h2v3c0 2 0 3 2 5h1v-3z" class="R"></path><path d="M482 229h2v3h-3c1-1 1-2 1-3z" class="K"></path><path d="M482 238c1 2 2 4 4 5 1 1 0 2 0 3h0c-2 0-2 0-2-1s-2-5-2-6v-1z" class="B"></path><path d="M478 230c1-1 3-1 4-1 0 1 0 2-1 3 0 2 0 4 1 6v1c-1-3-3-5-4-7v-2z" class="L"></path><path d="M225 172h1v8 4h0c0-2 0-3 1-4v51h0c-1-3-1-8-1-11v1 7 20 105 22c0 2 0 5-1 8V172z" class="C"></path><path d="M69 179l1-47v7c1 0 1-1 1-1 0-2 0-4 1-5 0 2-1 5 0 7 1 1 1 2 1 3h-1l1 1h0l1-7v-1 10 1 4c-1 4-2 7-2 11l-1 13h-1c0 5 1 11 0 16-1-3 1-15-1-17v5h0z" class="W"></path><path d="M71 138c0-2 0-4 1-5 0 2-1 5 0 7 1 1 1 2 1 3h-1l1 1h0l1-7v-1 10 1 4c-1 4-2 7-2 11l-1 13h-1v-8l1-29z" class="N"></path><path d="M72 162v6c0 3 0 6 1 8v2c-1 2-1 6-1 9l1 19c0 1 0 4-1 6v5l-1 16c-1 0-1-1-1-2l-1 2c1-8 0-17 0-25h0c1-1 1-4 1-5v-12c1-5 0-11 0-16h1l1-13z" class="D"></path><path d="M157 616c4-1 8-3 12-5 0 2-3 3-4 5-3 1-5 3-7 5v1c-1 3-1 6 0 10 2 2 4 4 6 5h1 0v1c1 0 2 0 3 1h-1c-3 1-6 0-9 0-3-2-5-6-7-9 0 0 0-1 1-2l-1-1c0-2 0-3 1-5l3-3c1-1 2-2 2-3z" class="B"></path><path d="M152 628l1-4c1-2 2-4 4-5l1 1-1 1c-2 3-3 5-4 8 2 4 3 7 7 8 1 0 4 1 5 1s2 0 3 1h-1c-3 1-6 0-9 0-3-2-5-6-7-9 0 0 0-1 1-2z" class="N"></path><path d="M157 616c4-1 8-3 12-5 0 2-3 3-4 5-3 1-5 3-7 5v1l-1-1 1-1-1-1c-2 1-3 3-4 5l-1 4-1-1c0-2 0-3 1-5l3-3c1-1 2-2 2-3z" class="b"></path><path d="M124 632h2c1 1 3 1 4 1 4-1 8-1 11-3 1 1 1 1 2 1v1c0 1-1 3 0 4h0c0 1-1 2-2 3h0c-3 0-20 1-22 0v-1h-3l3-1c1 0 1 0 2-1 0-2-2-4-3-5h1c1 0 2 1 3 2 1-1 2 0 2-1z" class="F"></path><path d="M124 632h2c1 1 3 1 4 1 4-1 8-1 11-3 1 1 1 1 2 1-2 2-2 2-4 3-5 1-12 2-16-1h-1c1-1 2 0 2-1z" class="T"></path><path d="M143 636h0c0 1-1 2-2 3h0c-3 0-20 1-22 0 7-4 16 0 24-3z" class="O"></path><defs><linearGradient id="J" x1="433.791" y1="637.236" x2="418.114" y2="629.918" xlink:href="#B"><stop offset="0" stop-color="#1e1c1d"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#J)" d="M410 629l2-1 1 1 5 4h8c3 0 5-1 7-2h1l2-1 1-1c2 0 4 0 6 1 1 2 0 4-1 7-1 1-2 1-4 1h0l-2-1h-9-14l2-1v-1c-1-2-3-3-4-4h-1 0v-2z"></path><path d="M426 633c3 0 5-1 7-2h1c-2 2-3 3-6 3h-1 0 1v-1h-1-1z" class="a"></path><path d="M438 631s0-1 1 0h2c1 2 1 2 1 4l-2 2c-2 0-3 0-4-1-1 0-1-1-1-2 1-1 2-3 3-3z" class="O"></path><path d="M410 629l2-1 1 1 5 4h8 1 1v1h-1 0 1l-9 1h-4c-1-2-3-3-4-4h-1 0v-2z" class="D"></path><defs><linearGradient id="K" x1="476.571" y1="197.291" x2="499.78" y2="206.982" xlink:href="#B"><stop offset="0" stop-color="#6e7273"></stop><stop offset="1" stop-color="#a29c9d"></stop></linearGradient></defs><path fill="url(#K)" d="M487 176v-3c1 0 1 1 1 2v3-3c2 19 3 38 2 56 0 2 1 5 1 6h-1v4c-1-4-1-9-2-13-1 2-1 4-1 6l-1-21 1-1v-15c0-7-1-14 0-21z"></path><defs><linearGradient id="L" x1="173.855" y1="139.661" x2="175.475" y2="144.893" xlink:href="#B"><stop offset="0" stop-color="#6a6869"></stop><stop offset="1" stop-color="#898889"></stop></linearGradient></defs><path fill="url(#L)" d="M161 145l15-6c10-4 19-6 29-8-1 1-1 1-1 2h0l-21 6c-6 2-12 4-17 7l-13 6c-2 2-4 4-7 5-1 0-3 2-4 2 0-1 0-1-1-1v-1l9-6c3-2 7-4 11-6z"></path><path d="M224 126h2v1h1v5 9 25 14c-1 1-1 2-1 4h0v-4-8h-1v-3-7-31c-7 0-14 1-21 2h0c0-1 0-1 1-2 2 0 3-2 4-2 5-3 10-3 15-3z" class="F"></path><path d="M224 126h2v1h1c-6 1-12 1-18 3v-1h0c5-3 10-3 15-3z" class="M"></path><path d="M406 627c0-1 2-2 3-3 3 0 6 1 9 1l15 2s1 0 2 1c0 0 1 1 1 2l-2 1h-1c-2 1-4 2-7 2h-8l-5-4-1-1-2 1c-1 1-2 2-2 4v2l-1-1c-1 1-1 2-2 3v-1c-1 0-1 1-2 1h-1 0v-1c2-2 3-3 5-6h0 0c-1-1-1-2-1-3z" class="R"></path><path d="M407 630c1-1 2-1 3-2 2-2 4-1 7-1-1 0-3 0-4 1v1l-1-1-2 1c-1 1-2 2-2 4v2l-1-1c-1 1-1 2-2 3v-1c-1 0-1 1-2 1h-1 0v-1c2-2 3-3 5-6z" class="C"></path><path d="M417 627l9 1c3 0 5 0 7 1h1l-1 2c-2 1-4 2-7 2h-8l-5-4v-1c1-1 3-1 4-1z" class="K"></path><defs><linearGradient id="M" x1="80.459" y1="90.861" x2="107.375" y2="115.316" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#343332"></stop></linearGradient></defs><path fill="url(#M)" d="M71 114v-14l52-1v2l3 1v1h1c0 1 3 3 5 4h0c-3-1-5-3-8-4-7-2-14-1-21-1-8 0-16 0-24 1h-1v1c0 2 0 2-1 4-1-1-1-2-2-2s-1 0-2 1c-1 4 0 9-1 13v-7c0-1 0-2-1-3v4z"></path><path d="M81 225c1-1 1-1 3-2-1 3-2 4-3 7l1-2c0 1 1 1 0 1v2l-8 20-5-1v-1c-1-4-1-9 0-13v-3l1-2c0 1 0 2 1 2 0 1 1 1 1 2v-1c0-1 0-2 1-3h3c0-1 2-2 3-2 1-1 2-2 2-4z" class="R"></path><path d="M73 238v1c1 0 1 1 1 2s-1 3-1 4l-1 1h0c0-3 0-5 1-8z" class="B"></path><path d="M81 225c1-1 1-1 3-2-1 3-2 4-3 7-2 2-3 5-4 8l-1-7c0-1 2-2 3-2 1-1 2-2 2-4z" class="L"></path><path d="M138 626c3-1 8-3 11-1l2 2 1 1c-1 1-1 2-1 2 2 3 4 7 7 9 0 0-1 0-2 1-2-1-5-1-7-2 0-1-1-1-2-1h0c-1 1-1 2-3 2h-3c1-1 2-2 2-3h0c-1-1 0-3 0-4v-1c-1 0-1 0-2-1-3 2-7 2-11 3-1 0-3 0-4-1h-2c0-1-1-2-1-3h-2v-1h1-1v-1h4l13-1z" class="K"></path><path d="M143 632l1-1h0l1 3c-1 1-1 2-2 4 1-2 2-3 4-4h0c1 1 1 2 0 3s-1 2-3 2h-3c1-1 2-2 2-3h0c-1-1 0-3 0-4z" class="D"></path><path d="M125 627l13-1c1 0 1 0 3 1-6 0-13 2-19 1h-1v-1h4z" class="E"></path><path d="M138 626c3-1 8-3 11-1l2 2 1 1c-1 1-1 2-1 2l-2-1c-1-1-2-2-4-3-1 0-3 0-4 1-2-1-2-1-3-1z" class="G"></path><path d="M141 630c1 0 3-2 4-2 5 1 8 7 11 10-4-1-7-6-10-7-1 1-1 2-1 3l-1-3h0l-1 1v-1c-1 0-1 0-2-1z" class="J"></path><path d="M225 383c1-3 1-6 1-8v-22-105-20-7-1c0 3 0 8 1 11h0v252c-1 2-1 4-1 6v14c0 1 1 4 0 5v-38c0-6 0-12-1-18v-4-10-55z" class="S"></path><path d="M215 637c1-1 2-2 3-4h0c3 1 6 2 8 2 5 1 10 1 15 1h0c2 0 5 0 6 1h9c3 0 7 0 10-1h0v1c-1 0-1 0-2 1-4 1-10 0-14 1h-6l-26 1h-15c-1 0-3-1-5 0h-8-3s-1 0-1 1h-9-15c-2-1-4-1-6-1 1-1 2-1 2-1 3 0 6 1 9 0h1c-1-1-2-1-3-1v-1s0-1 1-1h4c11 2 23 1 35 1h9 1z" class="T"></path><path d="M215 637c1-1 2-2 3-4h0c3 1 6 2 8 2 5 1 10 1 15 1h0c2 0 5 0 6 1h-19c-4 0-9-1-13 0z" class="B"></path><path d="M158 639c3 0 6 1 9 0h1l30 1h-8-3s-1 0-1 1h-9-15c-2-1-4-1-6-1 1-1 2-1 2-1z" class="b"></path><path d="M72 120c1-4 0-9 1-13 1-1 1-1 2-1s1 1 2 2v1c1 0 2-1 2-1l1 1h0c1 0 2 1 2 0h1c1 0 3 1 3 2 1 0 1 0 1 1h0v1c1-2 0-2 0-3h1l1 1c0 1 1 2 0 3 0 2-2 4-3 5h-2c0 1-1 2 0 2 0 2-4 7-5 8 1 0 2 1 2 2-3 5-5 10-7 16v-1-10 1l-1 7h0l-1-1h1c0-1 0-2-1-3-1-2 0-5 0-7-1-1 0-3 0-4v-9z" class="R"></path><path d="M80 109c1 0 2 1 2 0h1c1 0 3 1 3 2 1 0 1 0 1 1h0v1c-1 2-2 3-3 4-3-1-4-3-5-5 1-1 1-2 1-3zm-6 27l2-25 2 4c1 3 3 4 6 4 0 1-1 2 0 2 0 2-4 7-5 8 1 0 2 1 2 2-3 5-5 10-7 16v-1-10z" class="B"></path><path d="M84 121c0 2-4 7-5 8h-1c-1-2 0-5 1-7 1-1 3-1 5-1z" class="G"></path><path d="M329 492v26 3c1 0 1 0 1-1v2l1 1v-3c0 12 1 24 5 35 2 6 4 11 7 16 1 1 5 6 5 7l5 7c0 1-1 1-1 2l7 7c3 3 7 7 10 9l5 3-1 1c1 1 2 1 3 2l4 2h-1l-6-3h0c-3-1-5-3-7-4v1l1 1c-1 0-1 0-1 1h1l-1 1h0c-1-1-2-1-3-2h0v-1c0-1-4-4-5-5h-2v1 1c-1-1-2-2-2-3h-1 0c0-1 1-1 1-2v-1h-1l-8-4h-3l1-2h0l-2-2h0 2c0-1 1-1 1-2-1-1-2-2-1-4h0l-1-1c0-1-1-1-1-2h1l-6-11c-1-3-2-7-5-9h0 1c-1-2-2-5-3-7-1-4-1-8-2-12l1-1v-3-31-2c0-4 0-8 1-11z" class="H"></path><path d="M344 576c1 1 2 1 2 3 1 0 3 3 3 3 0-2-1-2-1-4l5 7c0 1-1 1-1 2-1-1-2-3-3-4-2-2-4-4-5-7z" class="B"></path><defs><linearGradient id="N" x1="347.296" y1="596.189" x2="359.634" y2="590.839" xlink:href="#B"><stop offset="0" stop-color="#090b0c"></stop><stop offset="1" stop-color="#282425"></stop></linearGradient></defs><path fill="url(#N)" d="M342 579l8 10c5 5 10 9 14 13 1 0 2 1 2 2v1l1 1c-1 0-1 0-1 1h1l-1 1h0c-1-1-2-1-3-2h0v-1c0-1-4-4-5-5h-2v1 1c-1-1-2-2-2-3h-1 0c0-1 1-1 1-2v-1h-1l-8-4h-3l1-2h0l-2-2h0 2c0-1 1-1 1-2-1-1-2-2-1-4h0l-1-1c0-1-1-1-1-2h1z"></path><path d="M343 590h3 0c0 1 0 1 1 1-1 1-2 1-2 1h-3l1-2z" class="E"></path><path d="M347 591c2 1 5 2 6 4v1l-8-4s1 0 2-1z" class="D"></path><path d="M329 518v3c1 0 1 0 1-1v2l1 1v-3c0 12 1 24 5 35 2 6 4 11 7 16 1 1 5 6 5 7 0 2 1 2 1 4 0 0-2-3-3-3 0-2-1-2-2-3h0c-3-3-4-6-5-9-6-11-9-22-10-34-1-5 0-10 0-15z" class="P"></path><path d="M73 178c1 4 4 9 8 11 4 3 9 4 14 3 2 0 5-1 7-2-2 3-4 6-5 9-1 0-1 1-1 1-1 2-2 5-4 7-3 5-7 10-8 16-2 1-2 1-3 2 0 2-1 3-2 4-1 0-3 1-3 2h-3c-1 1-1 2-1 3v1c0-1-1-1-1-2l1-16v-5c1-2 1-5 1-6l-1-19c0-3 0-7 1-9z" class="B"></path><path d="M73 231c1-3 6-4 8-6 0 2-1 3-2 4-1 0-3 1-3 2h-3z" class="M"></path><path d="M75 214c1 0 2 0 3 1v4c1 0 1-1 2-2v1 2c-1 1-1 1-3 2v-1c-1-2-2-5-2-7z" class="H"></path><path d="M89 198v-2l9-2-5 8h0-1v1c0-2 0-3-1-4s-1-1-2-1z" class="T"></path><path d="M75 214l1-1c2-1 5-2 7-1 1 0 1 0 1 1 1 3 0 5-1 7l-1 1c-1-1-1-3-1-4v-2l-1-1-2 1c-1-1-2-1-3-1zm18-12c-2 3-4 5-6 7h-1v-2c-1 1-3 0-4 0-2 0-4 0-5-2-1-1-2-3-2-5 1-2 2-5 4-6 1-1 3-1 4 0 3 0 4 2 6 4h0c1 0 1 0 2 1s1 2 1 4v-1h1z" class="G"></path><path d="M89 201c1 0 1 1 1 2-1 1-1 2-2 2v-2h-1c0-1 1-2 2-2z" class="S"></path><path d="M89 198c1 0 1 0 2 1 0 2 0 2-1 4 0-1 0-2-1-2v-3h0z" class="K"></path><path d="M88 205l-1 1h-5c-1-1-3-2-4-3v-4c0-1 1-2 2-3h5c1 3-1 3-1 5v2h3 1v2z" class="B"></path><path d="M483 168h0 1c0-1 0-3-1-4v-2c2 1 2 2 2 4h0v-3c1 2 1 4 1 7l1 6c-1 7 0 14 0 21v15l-1 1 1 21v3h-1c-2-2-2-3-2-5v-3h-2c-1 0-3 0-4 1h-1v-1c-1-1-2-2-2-3-1-3-2-5-3-7h-1c-1 0-1-1-1-2-2-3-3-7-6-9l-3-6c1 1 2 2 2 3 1 1 1 1 1 2l2 2v-2c0-1-1-2-1-3l-1-2c-2-4-5-8-7-11 2 1 5 1 8 2 5 0 10-1 13-5h1l-1-1h-1c6-5 6-12 6-19z" class="L"></path><path d="M485 202c1 3 0 6 1 9v2-1c-1 0-3-2-3-3 2-2 2-4 2-7z" class="G"></path><path d="M477 229v-2h2c2 0 3 0 4 1l1 1h-2c-1 0-3 0-4 1h-1v-1z" class="O"></path><path d="M461 202c1 1 2 2 2 3 1 1 1 1 1 2l2 2v-2c0-1-1-2-1-3 2 5 5 10 7 15h-1c-1 0-1-1-1-2-2-3-3-7-6-9l-3-6z" class="G"></path><path d="M473 211c2 0 4 0 5 1 1 0 2 1 3 2 1 2 0 5-1 7l-1 1 1 1h0-2c0-1 0-1 1-2h0 0c-2-1-3-1-5-1-1 0 0 0-1-1 0-2-1-5-1-7l1-1z" class="J"></path><path d="M476 214h0c2 0 2 0 3 1v3c-2 0-2 0-3-1v-3z" class="F"></path><path d="M483 168h0 1c0-1 0-3-1-4v-2c2 1 2 2 2 4h0v-3c1 2 1 4 1 7l1 6c-1 7 0 14 0 21v15l-1 1v-2c-1-3 0-6-1-9l-1-24c-1 4-2 7-5 9v1l-1-1h-1c6-5 6-12 6-19z" class="D"></path><path d="M473 194c2 0 5 0 7 1 1 2 2 4 1 7-1 2-3 4-5 5-2 0-4 1-6 0h0l-1-1c-2-1-3-3-4-6l-4-5h6 3c1 0 2-1 3-1z" class="G"></path><path d="M477 197c2 0 3 1 3 3 0 1-1 2-2 4-1 1-2 2-4 2h-2l2-1s1-1 1-2h0c0-1 1-2 1-3 1-1 1-2 1-3z" class="C"></path><path d="M470 198h0l1-1c1-1 5-1 6 0 0 1 0 2-1 3 0 1-1 2-1 3h0c0 1-1 2-1 2l-2 1h0c-1-1-2-1-2-2-2-2-3-4-2-6h2z" class="L"></path><path d="M468 198h2 0c1 0 1 0 1 1 1 1 1 1 1 2s0 1-1 1c-1-1-3-2-3-4z" class="E"></path><defs><linearGradient id="O" x1="329.205" y1="323.493" x2="347.519" y2="323.478" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#353436"></stop></linearGradient></defs><path fill="url(#O)" d="M340 127c2 0 5 1 7 1 0 1 1 1 1 2l-2-1v2h0s1 1 2 1h-1-2 0l-13-2-1 390v3l-1-1v-2c0 1 0 1-1 1v-3-26-5-10-47-99-100-68-23c0-4-1-9 0-13 2 1 5 1 8 1v-1h3z"></path><path d="M340 127c2 0 5 1 7 1 0 1 1 1 1 2l-2-1c-3-1-6-1-9-1v-1h3z" class="E"></path><path d="M418 105c0 4-10 10-11 13l-1-1h-2c-1 0-3 1-4 2l-9 3 12 18c1 3 3 6 5 8 3 3 7 5 10 8 5 3 9 5 13 9 4 3 8 7 12 11 3 3 5 6 9 8 3 3 7 5 11 6s9 0 13-2l1-1h1l1 1h-1c-3 4-8 5-13 5-3-1-6-1-8-2 2 3 5 7 7 11l1 2c0 1 1 2 1 3v2l-2-2c0-1 0-1-1-2 0-1-1-2-2-3l-1-1-5-7c-10-15-25-28-40-38-8-6-16-9-25-13l-14-6h1c-1 0-2 0-3-1h2 0c2 0 4 0 5 1l1-1c-4-1-8-3-11-7-2-2-3-6-3-9 1-3 2-6 5-8 2-1 5-3 7-2 2 0 5 1 6 3 0 1 1 3 1 4l3 3 3-1 5-3c3-1 6-2 9-4 4-3 6-6 11-7z" class="R"></path><path d="M387 135c-5-3-13-5-14-12 0-1 0-3 1-4 1-2 2-3 4-3s5 2 7 3c2 2 4 5 6 8l11 18-21-8 1-1 5 1v-2z" class="L"></path><path d="M387 135c3 1 5 1 7 4v1c-3-1-5-2-7-3v-2z" class="E"></path><defs><linearGradient id="P" x1="182.246" y1="365.891" x2="230.941" y2="365.915" xlink:href="#B"><stop offset="0" stop-color="#b8b8b8"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#P)" d="M224 126c2 0 4-1 5-2 2-1 3-2 5-3 0-1 1-3 2-3l1 1c-1 2-1 2-2 3-2 1-4 3-5 4v3l1 195h0 1l1 1c0 1-2 2-2 3-1 1 0 3 0 4v18 3c0 2-1 6 0 8v-1l1 1h0l1 1v-1 1c1 1 0 2-1 4v5c-1 2 0 5 0 7 0 1-1 4 0 5 0-4 0-8 1-11 0-2 0-3 1-4v-1l1-1 1-1h0c-1 2-2 4-2 5-2 6-2 13-2 19v15c0 4 0 8 1 11 2 5 5 8 9 12l-1 1c-1-1-1-1-2-1h-4c-1 1-2 3-3 4 0 0 0 1-1 1v47 13c0 2-1 4 0 5v20c-1 4-1 9 0 13v1h-1v2-1l1 1c0 1 1 2 3 3l2 1v2h3v3c0 3 0 5 1 7 1 1 2 1 2 3v1l-2-1-1 3-1 1h0c0 1-1 1-1 1l-1-1h-1c-1-1-1-2-2-3-3 2-6 5-9 9-2 1-3 3-4 5 0 0 0 1-1 2-4 8-9 16-16 22-3 3-6 5-9 8s-7 7-11 9h-1c0-1-1-1-1-1l10-8c2-2 3-4 5-5h0c4-3 6-7 9-10l7-7c2-2 3-5 4-7 3-5 6-10 7-15v-4c2-6 3-13 3-20 1-6 1-12 1-18v-30-252-51-14-25-9-5h-1v-1h-2z"></path><path d="M230 533v-1l1 1c0 1 1 2 3 3l2 1v2c0 1-1 1-1 2l-1 7-6 6-4 4c2-5 3-11 4-17 1-3 2-5 2-8z" class="L"></path><path d="M234 536l2 1v2c0 1-1 1-1 2l-1-5z" class="P"></path><path d="M236 539h3v3c0 3 0 5 1 7 1 1 2 1 2 3v1l-2-1-1 3-1 1h0c0 1-1 1-1 1l-1-1h-1c-1-1-1-2-2-3-3 2-6 5-9 9-2 1-3 3-4 5 0 0 0 1-1 2h0v-1s0-1 1-1c0-1-1 0 0-1h0v-1c1 0 1 0 1-1l1-1h0s0-1 1-1v-1h0c-1-1 0-1 0-1v-1-1 2l1-1v-1l4-4 6-6 1-7c0-1 1-1 1-2z" class="E"></path><path d="M236 549h3 0l1 1c-1 1-1 1-1 2h1l-1 3-1 1h0c0 1-1 1-1 1l-1-1h-1c-1-1-1-2-2-3l3-4z" class="M"></path><path d="M239 549l1 1c-1 1-1 1-1 2h1l-1 3-1 1c-1-2 0-4 0-6l1-1z" class="H"></path><path d="M236 549v2 5h-1c-1-1-1-2-2-3l3-4z" class="f"></path><path d="M233 361v1c1 1 0 2-1 4v5c-1 2 0 5 0 7 0 1-1 4 0 5 0-4 0-8 1-11 0-2 0-3 1-4v-1l1-1 1-1h0c-1 2-2 4-2 5-2 6-2 13-2 19v15c0 4 0 8 1 11 2 5 5 8 9 12l-1 1c-1-1-1-1-2-1h-4c-1 1-2 3-3 4 0 0 0 1-1 1-1-9 0-18 0-27v-20c0-8 0-15 2-23v-1z" class="B"></path><path d="M326 127l2-1 1 1c-1 4 0 9 0 13v23 68 100 99 47 10 5c-1 3-1 7-1 11v2 31 3l-1 1c0-2-1-5-1-7v-1c-2 3-5 6-9 6h-5c-2 0-4 1-6 1-1 0-2-2-3-2-1-1-2 0-3 0l-2 1v2l-1-1v-2h0c1-2 1-5 3-6 2-2 4-1 6-1l4 2 1-1c1 1 1 2 2 3 2 1 3 1 5 1s4-1 5-3 2-5 3-8v-16-23c0-3-1-6-1-9v-81c0-8 1-17 0-26-2-8-5-17-12-22-4-2-9-4-13-3-6 1-13 7-16 12-2 2-4 5-6 8l-4-7c-4-6-11-12-18-13-4-1-9 0-12 3-4 2-7 6-9 10-1 2-1 3-2 4v1l-1-1h0l-1-1c1-2 2-4 3-7l5-5c5-5 10-7 17-7h1c1 0 1 0 2 1h1 3s0-1 1-1v-2-2h1c4-3 9-9 11-14 1-1 1-3 2-5 4 7 7 14 13 18 1 1 3 2 5 3 6 2 12 3 17 6 6 3 10 9 12 15 0 2 0 5 1 7v-21-13-6l-1-1c-6-2-8-7-10-13v-2c-1-2-3-3-5-3 2-1 2-1 3 0s1 0 1 1v-2h1c0 2 1 2 3 3s3 0 5-1c1-2 0-8 0-11 0-2 0-4 1-6 0-1 1-1 2-1v-4-53-68-24-15z" class="D"></path><path d="M267 346c1-1 1 0 2-1l3 4-1 1-4-4z" class="K"></path><path d="M256 341h1c1 0 1 0 2 1h1 3l6 3c-1 1-1 0-2 1-1 0-2-1-3-2l-8-3z" class="P"></path><path d="M272 349l6 7v1c4-5 8-12 14-14 1-1 4-1 5-1-7 3-14 9-17 16-1 1-1 2-2 3-1-4-4-7-7-11l1-1z" class="S"></path><path d="M297 537c1-2 1-5 3-6 2-2 4-1 6-1l4 2 3 3c-2 1-5 2-7 1-1-1 0-2 0-3h-2c-2 1-3 2-4 3v1l-2 1v2l-1-1v-2h0z" class="K"></path><defs><linearGradient id="Q" x1="317.2" y1="317.679" x2="332.283" y2="296.36" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#383437"></stop></linearGradient></defs><path fill="url(#Q)" d="M326 291v25c0 2 1 7 0 9v1h-1c-6-2-8-7-10-13v-2c-1-2-3-3-5-3 2-1 2-1 3 0s1 0 1 1v-2h1c0 2 1 2 3 3s3 0 5-1c1-2 0-8 0-11 0-2 0-4 1-6 0-1 1-1 2-1z"></path><path d="M315 311c4 3 5 7 7 11 1 1 2 2 4 3v1h-1c-6-2-8-7-10-13v-2z" class="O"></path><path d="M264 337h1c4-3 9-9 11-14 1-1 1-3 2-5 4 7 7 14 13 18 1 1 3 2 5 3v1c1 0 1 0 2 1h1-2-4 0 5c0 1 0 1-1 1s-4 0-5 1c-6 2-10 9-14 14v-1l-6-7-3-4-6-3s0-1 1-1v-2-2z" class="J"></path><path d="M281 331l4 4h-3c-1-1-1-2-1-3v-1z" class="b"></path><path d="M285 341h3l-4 4-2-1 1-1v-1h-1l1-1h2 0z" class="f"></path><path d="M272 335v2l-1 1-2 2c1 0 1 1 2 1l-2 1-4-2 7-5z" class="C"></path><path d="M276 331c1-2 2-4 2-5l3 5v1h-1c-2 0-3-1-4-1z" class="i"></path><path d="M276 331c1 0 2 1 4 1h1c0 1 0 2 1 3h3 0c1 2 5 4 6 5l-3 1h-3c1-1 2-1 2-1 0-1-1-2-2-2l-3-3v2h-4v-3-1c-2 1-4 3-5 5 0-1 0-1-1-1v-2l4-4z" class="B"></path><path d="M271 341h0c2 1 3 2 4 4h1l1-1h0v1h1c1-1 1-1 2-1s1-1 2-2h1v1l-1 1 2 1-6 7c-2-3-5-6-8-9l-1-1 2-1z" class="P"></path><path d="M271 341h0c2 1 3 2 4 4h1l1-1h0v1h1c1-1 1-1 2-1s1-1 2-2h1v1l-1 1-1 1h0c-2 1-2 2-3 4h0 0c-2-1-3-3-4-4s-2-2-4-2l-1-1 2-1z" class="N"></path><path d="M273 338c1-2 3-4 5-5v1 3h4v-2l3 3c1 0 2 1 2 2 0 0-1 0-2 1h0-2l-1 1c-1 1-1 2-2 2s-1 0-2 1h-1v-1h0l-1 1h-1c-1-2-2-3-4-4h0c-1 0-1-1-2-1l2-2 1-1c1 0 1 0 1 1z" class="F"></path><path d="M273 338c1-2 3-4 5-5v1 3h4l1 1c-1 1-1 2-2 2l-3 3-1-1v-1c-1-1-2-2-3-2l-1-1z" class="H"></path><path d="M274 339c1-1 2-1 3-1 0 1 1 1 1 2s0 2-1 2v-1c-1-1-2-2-3-2z" class="G"></path><path d="M278 340c0-1 1-1 2-1 0 0 0 1 1 1l-3 3-1-1c1 0 1-1 1-2z" class="D"></path><path d="M284 291c1 0 3 1 4 1v1c-2 0-3 0-5-1l2 4c1 2 2 3 4 4 5 3 10 3 16 3 1 0 3 0 5-1l1 1h-1l4 4v2c0-1 0 0-1-1s-1-1-3 0c2 0 4 1 5 3v2c2 6 4 11 10 13l1 1v6 13 21c-1-2-1-5-1-7-2-6-6-12-12-15-5-3-11-4-17-6-2-1-4-2-5-3-6-4-9-11-13-18-1 2-1 4-2 5-2 5-7 11-11 14h-1v2 2c-1 0-1 1-1 1h-3-1c-1-1-1-1-2-1h-1c-7 0-12 2-17 7l-5 5c-1 3-2 5-3 7v1c-1-2 0-6 0-8v-3-18c0-1-1-3 0-4 0-1 2-2 2-3 1 0 1-1 2-1 4-4 5-9 8-12 2-2 4-2 6-2s4 1 6 3h1l1 1c1 1 1 2 1 3v1c6-1 12-3 16-8h1v2h0c2-3 2-5 3-8l-2-2 1-3c1-2 0-2 2-3v-5c2-1 3 0 5 0z" class="L"></path><path d="M302 323l1-1c2 0 3 0 4 1v1h2c-2 0-4 1-7-1z" class="E"></path><path d="M251 323l3-1v1c-1 1-3 2-5 1h-2l1-1h3z" class="D"></path><path d="M257 341c1-1 2-2 4-2h1c0 1-1 2-2 3h-1c-1-1-1-1-2-1z" class="I"></path><path d="M264 337v2 2c-1 0-1 1-1 1h-3c1-1 2-2 2-3h-1l3-2z" class="G"></path><path d="M288 322c3 0 5 1 7 3v1h5-1c-3 1-6 0-8-2l-3-2z" class="Q"></path><path d="M234 346c0-1 0-2 1-2h2 0v1c-1 1-3 6-5 7l2-6z" class="H"></path><path d="M312 318l1-1c1 2 0 3-1 4 0 1-1 2-2 3h-1 0-2v-1h0 0l2-1c1-1 2-3 3-4zm-61-4c1 1 1 2 2 3 0 2-1 3-2 4s-1 1-2 1c-2-1-2-2-3-3h0c2 0 1 0 3 1h1c1-1 1-1 1-3 0 0-1-1-1-2l1-1z" class="G"></path><path d="M231 353c1-4 0-8 0-11h0v10l1-1v-2l1-1v-1-1-1l1 1-2 6 1 1h0 1c-1 3-2 5-3 7v1c-1-2 0-6 0-8z" class="S"></path><path d="M247 324c-2-1-3-2-4-4v-5c1-1 2-2 4-2 1 0 3 0 4 1l-1 1c-1 0-2-1-3 0-1 0-2 1-2 3v2c1 2 3 3 5 3h1-3l-1 1z" class="V"></path><path d="M274 310h1v2c-4 5-9 6-15 8-2 0-4 1-6 2v-1h0c1-1 1-2 2-3h0v-1c0-1-1-3-1-4h1l1 1c1 1 1 2 1 3v1c6-1 12-3 16-8z" class="J"></path><path d="M255 313h1l1 1c1 1 1 2 1 3-1 1 0 1-1 2h-1v-2c0-1-1-3-1-4z" class="T"></path><path d="M291 324c2 2 5 3 8 2h1 2l3 6h1v1c2 1 4 0 6 0-1-1-1-1 0-3 1-1 2-2 3-2 2 0 4 0 5 1 2 1 2 3 2 4s0 3-1 3c-2 3-7 3-10 3s-7-1-9-2c-4-1-7-3-9-5l-3-2-1-3c0-1 1-2 2-3z" class="a"></path><path d="M293 332h4c1 0 1-1 2-1l1 1c0 1-1 2 0 2 1 1 1 2 2 3-4-1-7-3-9-5z" class="O"></path><path d="M306 333c2 1 4 0 6 0h0c1 1 1 1 1 2s1 0 1 1v1h-8c-1-1-1-2-2-2s-1-1-2-1v-1c1-1 1 0 1 1h2l1-1z" class="D"></path><path d="M268 322c1 0 1 0 2-1h2 0v3l-1 1c-2 3-6 9-10 10l-4 2c-3 1-6 2-10 3-2 0-4-1-6-1s-4-1-6-2c-1-2-1-4 0-5 0-2 2-3 3-4 2 0 5 0 7 2 1 1 1 2 1 3v2c1 0 4-1 5-2l1-2c1-1 1-2 1-2 0-1 0-2 1-3h3 1c3 1 7-2 10-4z" class="O"></path><path d="M256 335h-1 0v-1c1 0 3-2 4-3h1c0-3 3-3 5-5h0 1c0 2-4 3-4 5l1 1c-2 1-5 2-7 3z" class="G"></path><path d="M263 332l5-5c0-1 1-1 1-2h2c-2 3-6 9-10 10l-4 2v-1c-1 0-3 0-3 1h-2c1-1 1-1 2-1l2-1c2-1 5-2 7-3z" class="H"></path><path d="M280 304c2 5 4 9 9 11 3 2 6 3 9 3h0 0c0-3 0-3 1-5 1-3 5-5 8-5 1-1 1-1 2 0h1c2 0 4 1 5 3v2h-1c-1-1-3-1-4-1h-1-1-1v1c3 1 4 1 6 4h0l-1 1c-1 1-2 3-3 4l-2 1h0 0c-1-1-2-1-4-1l-1 1h0l-6-3-1 1c-2 0-3 0-4-1h-1 0l-1-1v1l-2 1 1 1 3 2c-1 1-2 2-2 3l1 3c-3-2-6-6-7-10h0c-2-2-2-4-3-6l-1 1v2h-1c-1-2 0-7 0-9-1 3-1 5-2 7 0 3-3 7-4 9v-3h0-2c-1 1-1 1-2 1 3-3 5-6 7-10 2-3 2-5 3-8v2h1c1 0 1-1 1-2z" class="I"></path><path d="M309 309c2 1 4 2 5 4-1-1-3-1-4-1l-1-3z" class="P"></path><path d="M290 318l6 2-1 1c-2 0-3 0-4-1-1 0-1-1-1-2z" class="C"></path><path d="M286 317l4 1c0 1 0 2 1 2h-1 0l-1-1v1l-2 1-2-2c1-1 1-1 1-2z" class="K"></path><path d="M285 319c-2-3-3-6-4-10 1 3 3 5 5 7v1c0 1 0 1-1 2h0z" class="B"></path><path d="M303 312c2-2 4-3 6-3l1 3h-1-1-1v1c-1 0-2 0-3 1h-1c-1 1-1 2-2 2h0c1-2 1-3 2-4z" class="K"></path><path d="M283 320c0-1 0-1-1-2-1-2-2-4-2-6s-1-3-1-5v1c1 0 1 0 1 1v1h0v1c0 1 0 1 1 2h0c1 1 1 2 1 3l3 3h0l2 2 1 1 3 2c-1 1-2 2-2 3l1 3c-3-2-6-6-7-10z" class="E"></path><path d="M303 312c-1 1-1 2-2 4h0c1 0 1-1 2-2h1c1-1 2-1 3-1 3 1 4 1 6 4h0l-1 1c-1 1-2 3-3 4l-2 1h0 0c-1-1-2-1-4-1l-1 1h0c0-1-1-1-1-2s-1-1-1-2c0-3 1-5 3-7z" class="B"></path><path d="M303 316h1v1 1l-1 2v-2c-1-1-1-1 0-2z" class="D"></path><path d="M304 318c0 1 0 1 1 1h1c1-1 3-1 5-1-2 1-4 2-5 3-2 0-2 0-3-1l1-2z" class="N"></path><path d="M307 313c3 1 4 1 6 4h0l-1 1v-1l-1 1c-2 0-4 0-5 1h-1c-1 0-1 0-1-1v-1-1h-1c0-1 1-2 1-2 1-1 2-1 3-1z" class="L"></path><path d="M307 313c3 1 4 1 6 4h0l-1 1v-1c-1 0-1-1-2-1-1-1-2-2-3-1-2 0-2 1-3 2v-1h-1c0-1 1-2 1-2 1-1 2-1 3-1z" class="I"></path><path d="M284 291c1 0 3 1 4 1v1c-2 0-3 0-5-1l2 4c1 2 2 3 4 4 5 3 10 3 16 3 1 0 3 0 5-1l1 1h-1l4 4v2c0-1 0 0-1-1s-1-1-3 0h-1c-1-1-1-1-2 0-3 0-7 2-8 5-1 2-1 2-1 5h0 0c-3 0-6-1-9-3-5-2-7-6-9-11 0 1 0 2-1 2h-1v-2l-2-2 1-3c1-2 0-2 2-3v-5c2-1 3 0 5 0z" class="L"></path><path d="M277 299c1-2 0-2 2-3l1 8c0 1 0 2-1 2h-1v-2l-2-2 1-3z" class="D"></path><path d="M286 306c2-1 5 0 7 0l-1 4h-1v-1-2h-1v3h-2c-1 0-2-1-2-1v-3z" class="N"></path><path d="M329 588l1-2c1 0 2-1 3-1l1 1c-1 1-2 1-2 2v1c2-2 6-1 9-1l2 2h0l-1 2h3l8 4h1v1c0 1-1 1-1 2h0 1c0 1 1 2 2 3v-1-1h2c1 1 5 4 5 5v1h0c1 1 2 1 3 2h0l1-1h-1c0-1 0-1 1-1l-1-1v-1c2 1 4 3 7 4h0l6 3h1l-4-2c-1-1-2-1-3-2l1-1 3 2 11 7c1 1 2 2 3 2h2l6 4v-1c1 0 2 1 2 1 1 1 2 2 4 2 0 0 1 0 2 1h0c0 1-1 1-1 2v1c0 1 0 2 1 3h0 0c-2 3-3 4-5 6v1c-1 2-2 3-3 4-2 0-4-1-6 0h-4-10-6-12-10-27-4-29l-1-1c1-1 7-1 9-1h0c-4 0-15 1-18-1l-1-2c1-1 4-1 5-1h0c-3-3-3-5-4-9v-7h0v-3c-1-2-2-3-2-6 0-2 1-4 2-5 0 3 1 5 3 8h0 1c1-1 2-1 3-1 5 9 10 15 20 18 8 2 17 2 24-1l3-1 6-3c3-4 5-8 5-13 0-7-5-13-11-17-2-2-4-3-6-5h0v-2z" class="T"></path><path d="M332 629l3-1 2 1c-1 0-2 0-3 1l-2-1z" class="R"></path><path d="M335 628l6-3v2c-1 1-2 1-4 2l-2-1z" class="D"></path><path d="M350 623c1 0 2 1 3 1 0 2 0 2-1 4v1c-1-1-3-2-4-3l2-3z" class="F"></path><path d="M327 636c3 0 5-1 8-2h2l4 3-14-1h0zm19-8h0v1l1 2c0 1 0 1-1 2-2 1-3 1-5 1-1-1-2-1-2-1l7-5z" class="B"></path><g class="c"><path d="M324 639h33c4 0 10 0 14 1h0c-4 0-10-1-14 0 1 0 3 0 4 1h-10-27c0-1 4-1 5-1-2 0-3 0-4-1h-1z"></path><path d="M299 639h12 6 7 1c1 1 2 1 4 1-1 0-5 0-5 1h-4-29l-1-1c1-1 7-1 9-1h0z"></path></g><path d="M317 639h7 1c1 1 2 1 4 1-1 0-5 0-5 1h-4c-2-1-6-1-8-1h0 5c-1 0-5 0-6-1h6zm85-3v1c-1 2-2 3-3 4-2 0-4-1-6 0h-4-10-6-12c-1-1-3-1-4-1 4-1 10 0 14 0h0 17l8-1c2 0 4-1 6-3z" class="h"></path><path d="M402 636v1c-1 2-2 3-3 4-2 0-4-1-6 0h-4-10c3-1 6 0 8 0 1 0 3 0 4-1h-3l8-1c2 0 4-1 6-3z" class="c"></path><path d="M351 600c3 7 5 14 3 22l-1 2c-1 0-2-1-3-1 1-1 1-3 2-5 1-6-1-11-3-16 0 0 1 0 1-1 1 0 1-1 1-1z" class="C"></path><path d="M332 589c2-2 6-1 9-1l2 2h0l-1 2h3l8 4h1l-6-1c1 1 2 3 3 5 0 0 0 1-1 1 0 1-1 1-1 1-3-4-6-7-10-9-2-1-4-2-6-4h-1 0z" class="F"></path><path d="M332 589c2-2 6-1 9-1l2 2h0l-1 2c-3-1-6-3-10-3h0z" class="H"></path><path d="M354 622h3 1l-1 2c1 1 1 1 2 1l1 1c-2 3-4 5-6 8-2 1-3 3-5 3-2 1-6 1-7 0 3-2 7-4 10-7v-1-1c1-2 1-2 1-4l1-2z" class="K"></path><path d="M354 622h3 1l-1 2c-1 2-2 3-3 4l-2 2v-1-1c1-2 1-2 1-4l1-2z" class="M"></path><path d="M351 600c-1-2-2-4-3-5l6 1v1c0 1-1 1-1 2h0 1c0 1 1 2 2 3 1 2 1 4 1 5v1c1 1 1 2 1 3-1 3-1 6 0 9 1 1 1 1 1 2h-1-1-3c2-8 0-15-3-22z" class="R"></path><path d="M362 627h0l-5 5c2 1 4 1 6 1s4 0 6 1c1-1 1-2 1-3 2 0 8 3 10 2l1-1h2v-1l1-2s0 1 1 1c0 1 1 2 2 2l-1 1c-1 0-1 0-2-1 0 1-1 1-1 1v1c-1 1-2 1-2 2l-1 1c-4 1-8 0-12 0h-17l6-6 5-4z" class="B"></path><path d="M356 601v-1h2c1 1 5 4 5 5v1h0c1 1 2 1 3 2h0l5 5h0l3 3h0c0 1 0 1-1 1 0 0-1-1-2 0h1c-1 1-3 2-3 2-2 0-2 2-4 2-1 1-1 3-3 4l-2 1-1-1c-1 0-1 0-2-1l1-2h1c0-1 0-1-1-2-1-3-1-6 0-9 0-1 0-2-1-3v-1c0-1 0-3-1-5v-1z" class="J"></path><path d="M361 614c2-1 3-1 5-2 1 0 1 1 2 1 0 1-1 1-1 1h0c-1 0-2 1-3 2v2h-1c-1 0-2-1-3-3l1-1z" class="G"></path><path d="M356 601v-1h2c1 1 5 4 5 5v1h0v2h1v1l1 1h-1l2 2c-2 1-3 1-5 2h-1v-2-1h0c0-1 0-2-1-2h0v1l-1 1c0-1 0-2-1-3v-1c0-1 0-3-1-5v-1z" class="S"></path><path d="M356 601v-1h2c1 1 5 4 5 5v1h0v2h1v1l1 1h-1l-1-1c-1 0-1 1-2 0v-3c0-1-1-1-2-1h0-1c0-1 0-1 1-1v-1c-1 0-2-1-2-1l-1-1z" class="E"></path><path d="M358 611l1-1v-1h0c1 0 1 1 1 2h0v1 2h1l-1 1c1 2 2 3 3 3v1h3l2-2h3 1c-1 1-3 2-3 2-2 0-2 2-4 2-1 1-1 3-3 4l-2 1-1-1c-1 0-1 0-2-1l1-2h1c0-1 0-1-1-2-1-3-1-6 0-9z" class="L"></path><path d="M279 610c0-2 1-4 2-5 0 3 1 5 3 8h0c6 11 13 18 25 22 5 1 12 1 18 1h0l-30 1h-1c-2 0-7 0-8-1l-3-1h0c-3-3-3-5-4-9v-7h0v-3c-1-2-2-3-2-6z" class="S"></path><path d="M281 616c5 6 6 14 9 20 2 0 5 0 7 1h-1c-2 0-7 0-8-1l-3-1h0c-3-3-3-5-4-9v-7h0v-3z" class="D"></path><path d="M281 619h0c2 4 3 8 4 12 1 2 3 3 3 5l-3-1h0c-3-3-3-5-4-9v-7z" class="B"></path><path d="M366 604c2 1 4 3 7 4h0l6 3h1l-4-2c-1-1-2-1-3-2l1-1 3 2 11 7c1 1 2 2 3 2h2l6 4v-1c1 0 2 1 2 1 1 1 2 2 4 2 0 0 1 0 2 1h0c0 1-1 1-1 2v1c0 1 0 2 1 3h0 0c-2 3-3 4-5 6s-4 3-6 3v-1h0-4l-5-1c-2 1-5 1-7 0l1-1c0-1 1-1 2-2v-1s1 0 1-1c1 1 1 1 2 1l1-1c-1 0-2-1-2-2-1 0-1-1-1-1l-1 2v1h-2l-1 1c-2 1-8-2-10-2 0 1 0 2-1 3-2-1-4-1-6-1s-4 0-6-1l5-5h0v-2c2-1 2-3 3-4 2 0 2-2 4-2 0 0 2-1 3-2h-1c1-1 2 0 2 0 1 0 1 0 1-1h0l-3-3h0l-5-5 1-1h-1c0-1 0-1 1-1l-1-1v-1z" class="K"></path><path d="M381 636h0c2-1 6-2 7-4v-1h1 0 1v2c-1 1-3 3-5 3-1 1-3 1-4 0z" class="G"></path><path d="M375 625c1 0 3 0 4 1s1 3 2 4c0 0 1 0 1 1 0 0 0 1-1 1l-1 1c-2 1-8-2-10-2v-1h0l-1-1h1c2-1 3-2 5-4z" class="E"></path><path d="M373 608h0l6 3h1l-4-2c-1-1-2-1-3-2l1-1 3 2 11 7c1 1 2 2 3 2 5 3 8 6 9 12-1 3-1 4-4 6h-1l1-2c1-1 2-4 1-5-1-8-8-11-14-14-3-2-7-3-10-6h0z" class="D"></path><path d="M393 617l6 4v-1c1 0 2 1 2 1 1 1 2 2 4 2 0 0 1 0 2 1h0c0 1-1 1-1 2v1c0 1 0 2 1 3h0 0c-2 3-3 4-5 6s-4 3-6 3v-1h0-4l-5-1h4l1-1h1c1-1 1-1 2-1h1c3-2 3-3 4-6-1-6-4-9-9-12h2z" class="B"></path><path d="M399 620c1 0 2 1 2 1 1 1 2 2 4 2 0 0 1 0 2 1h0c0 1-1 1-1 2v1c0 1 0 2 1 3h0 0c-2 3-3 4-5 6s-4 3-6 3v-1h0-4 2c3-1 5-2 7-4 1-2 3-4 3-7-1-2-3-5-5-6v-1z" class="D"></path><path d="M362 627l1-1c1 0 1 0 2-1l3-3c3-3 7-3 11-2 5 0 9 1 13 6h1c1 1 1 4 1 5-2-2-3-4-6-6-4-3-10-2-14-1l1 1c-2 2-3 3-5 4h-1l1 1h0v1c0 1 0 2-1 3-2-1-4-1-6-1s-4 0-6-1l5-5z" class="R"></path><path d="M369 629v1c-1-1-1-2-1-3 1-1 4-2 6-3l1 1c-2 2-3 3-5 4h-1z" class="L"></path><path d="M236 365c1 0 3-2 3-3l1 1h0c-1 2-3 4-4 6-2 5-1 11-1 16v10 6s0 1 1 2h0c-1 7-1 12 3 18 3 4 7 7 11 10 7 6 16 14 20 23 2 5 3 10 4 15l3 15v3 5h0c0-3 1-5 1-8v4l1 1c1-2 1-4 1-5v-1 16c0 3 0 6-1 8l1 13c0 6-1 12 0 18l-1 15v18h-1l-1-5v-4-3c-1 1-1 1-1 2v-1 3c-1-4-1-8-1-12 0-3 0-5-1-7-1-3-2-7-4-10l2-1 1 2c0-1-1-4-1-5v-3l-1-7c-1-3-4-7-6-9-3-3-8-5-12-4-3 0-6 1-7 3-3 3-1 7-1 10h1c1 1 2 2 4 2h0c-1 1-2 1-4 0v2l1 1h0c-1 1-1 1-2 0l-1 1 2 3v1c-2 2-4 5-7 5h0l-1 1h1 1v2c-1 0-2-1-4-1l-2-1c-2-1-3-2-3-3l-1-1v1-2h1v-1c-1-4-1-9 0-13v-20c-1-1 0-3 0-5v-13-47c1 0 1-1 1-1 1-1 2-3 3-4h4c1 0 1 0 2 1l1-1c-4-4-7-7-9-12-1-3-1-7-1-11v-15c0-6 0-13 2-19 0-1 1-3 2-5z" class="S"></path><path d="M261 466c2 2 3 4 4 6-1-1-2-1-3-1v-3s-1-1-1-2z" class="L"></path><path d="M234 479c1-1 1-2 3-3h0v2c0 1 1 1 2 2-1 1-1 1-2 1s-2-1-3-2z" class="G"></path><path d="M249 477h-7v1h0c-1 0-2 0-2-1-1-1-1-1 0-1 2-1 4-1 7 0 0 0 1 0 2 1z" class="d"></path><path d="M253 460c3 1 5 0 7 0l-1 1s0 1-1 1v1c0 1 1 1 2 2 1 0 1 0 1 1-2-1-6-2-9-1h0c0-1 0-2 1-2v-2-1z" class="J"></path><path d="M274 528c0 3 0 6 1 9v6l-1 1c-1-3-2-7-4-10l2-1 1 2c0-1-1-4-1-5 1 1 1 1 1 2h0l1-1v-3zm-40-49c1 1 2 2 3 2s1 0 2-1h0 1l2 1c0 1-1 2-2 3s-3 1-4 1c-2 0-3-1-4-2 0 0 0-2-1-3h1l1 1c1 0 1-1 1-2z" class="G"></path><path d="M234 479c1 1 2 2 3 2s1 0 2-1h0c0 1 0 2-1 2-1 1-2 0-3 0 0 0-2 0-2-1 1 0 1-1 1-2z" class="L"></path><path d="M232 431c1-1 2-3 3-4h4c2 2 3 3 4 6 0 2-1 5-3 6 0 1-1 1-1 2v-1c0-2 2-3 2-5l-3 3c-2-2-5-4-6-6v-1z" class="G"></path><path d="M236 429h1c1 0 2 0 3 1s1 2 1 3l-3 2h-1l-3-3c1-1 1-2 2-3z" class="F"></path><path d="M242 485c4 2 10 4 13 7 4 3 7 7 10 11-4-2-7-4-12-4h-5s-1 0-1-1c-2-1-3-2-5-2h0c-4 0-8 3-10 5l10-16z" class="G"></path><path d="M246 491c3 0 5 1 7 2l6 6c-2-1-5-1-7-2s-4-4-6-6z" class="S"></path><path d="M232 514c1-2 2-4 4-5 1-2 2-3 3-4s1-2 1-3h-1v1l-1 1-1-1c1-2 1-2 3-3 1-1 2-2 4-2 1 0 1 0 2 1-2 3-5 5-6 8l1 1c2 0 4-2 6-3 8-3 17-1 22 5 4 5 5 11 5 18v3l-1 1h0c0-1 0-1-1-2v-3l-1-7c-1-3-4-7-6-9-3-3-8-5-12-4-3 0-6 1-7 3-3 3-1 7-1 10h1c1 1 2 2 4 2h0c-1 1-2 1-4 0v2c-1-1-1-2-2-3h0c0-1-1-1-2-2v-8c-3 2-6 4-9 4l-1-1z" class="O"></path><path d="M231 497v7 12l1-1v-1h0l1 1c3 0 6-2 9-4v8c1 1 2 1 2 2h0c1 1 1 2 2 3l1 1h0c-1 1-1 1-2 0l-1 1 2 3v1c-2 2-4 5-7 5h0l-1 1h1 1v2c-1 0-2-1-4-1l-2-1c-2-1-3-2-3-3l-1-1v1-2h1v-1c-1-4-1-9 0-13v-20z" class="K"></path><path d="M234 528c0-1-1-2-1-2 0-1 1-2 1-2 2-2 3-4 5-4h1l-1 3h0c-1 0-1 0-1-1-1 1-2 1-2 2-1 0 0 1-1 1v1 1l-1 1z" class="E"></path><path d="M242 519c1 1 2 1 2 2h0c1 1 1 2 2 3l1 1h0c-1 1-1 1-2 0l-1 1c-1 0-2-1-2-2v-5z" class="M"></path><path d="M231 530c1 2 3 3 5 4 1 1 2 1 2 2h1 1v2c-1 0-2-1-4-1l-2-1c-2-1-3-2-3-3l-1-1v1-2h1v-1z" class="G"></path><path d="M239 523v2c1-1 1-2 1-3h1v2l-2 1 1 2h0c-1 0-1 1-1 1v1h1v1c-2 0-4-1-5-2h-1l1-1v-1-1c1 0 0-1 1-1 0-1 1-1 2-2 0 1 0 1 1 1h0z" class="J"></path><path d="M236 365c1 0 3-2 3-3l1 1h0c-1 2-3 4-4 6-2 5-1 11-1 16v10 6s0 1 1 2h0c-1 7-1 12 3 18 3 4 7 7 11 10 7 6 16 14 20 23 2 5 3 10 4 15l3 15v3 5h0c0-3 1-5 1-8v4l1 1c1-2 1-4 1-5v-1 16c0 3 0 6-1 8l1 13c0 6-1 12 0 18l-1 15v18h-1l-1-5v-4-3c-1 1-1 1-1 2v-1 3c-1-4-1-8-1-12 0-3 0-5-1-7l1-1c1 0 1-1 1-1l-1-20v-9c0-3-1-6-2-9l-6-15c-4-6-11-11-18-12-1-1-2-1-2-1v-1c1-2 1-4 0-7v-1l3-4v1c0 1 0 3 2 4 1 0 5-1 6 1 1 1 1 2 0 3h-2v-1l1-1h-1l-2 2c0 1 2 2 3 3 2 1 4 3 6 5 4 4 6 9 8 13 1-1 2-1 2-2s-1-2-1-3c-2-5-5-10-7-16-1-2-2-4-4-6h0c0-1 0-1-1-1-1-1-2-1-2-2v-1c1 0 1-1 1-1l1-1c-2 0-4 1-7 0v1l-1-2c1-1 1-2 2-3s2-2 4-2c1 0 2 0 3 1 3 1 6 3 9 5-2-3-4-7-6-10-3-3-8-5-13-5-3 0-8 1-10 4-2 2-3 4-2 7 0 3 1 5 3 6h1s0-1-1-1c0-2 0-4 1-6s2-3 4-3c1 0 2 0 3 1s1 1 1 2c-1 0-2-1-3-2h-1c-5 2-1 8-2 11-1 1-1 1-2 1-1 1-2 0-3 0-3-2-7-5-7-8-1-3 0-8 1-11 1-2 2-4 4-5v1c-1 2-2 5-3 7 2-3 5-7 9-8 6-2 11 0 16 2-2-2-4-4-7-5-4-4-8-7-11-11-4-4-7-7-9-12-1-3-1-7-1-11v-15c0-6 0-13 2-19 0-1 1-3 2-5z" class="R"></path><path d="M274 486l1 17v-1c-1-2-1-4-2-6-1 0-1-2-2-3 1-1 2-1 2-2s-1-2-1-3h1v1c0 1 0 1 1 1v-4z" class="L"></path><path d="M269 467h0c1 1 1 4 2 5v-3-1s0-1-1-2h0 1l3 15v5 4c-1 0-1 0-1-1v-1c-2-7-2-14-4-21z" class="B"></path><path d="M236 365c1 0 3-2 3-3l1 1h0c-1 2-3 4-4 6-2 5-1 11-1 16-1-1-1-1-1-2v-2-6c-1 3 0 6-1 8 0 1 0 1 1 2h-1v3l-1 1c0-6 0-13 2-19 0-1 1-3 2-5z" class="M"></path><path d="M280 483v16c0 3 0 6-1 8l1 13h-2c0 2 1 8 0 10h0l-1-38c0-3 1-5 1-8v4l1 1c1-2 1-4 1-5v-1z" class="B"></path><path d="M278 520v-6c0-2-1-9 1-10h0v3l1 13h-2z" class="S"></path><path d="M253 460c1-2 2-3 3-5 3 0 5 1 7 2 3 2 6 4 8 8v1h-1 0c1 1 1 2 1 2v1 3c-1-1-1-4-2-5h0c-2-1-3-3-4-4-2-1-4-2-5-3-2 0-4 1-7 0z" class="L"></path><path d="M260 460c1 1 3 2 5 3 1 1 2 3 4 4 2 7 2 14 4 21h-1c-2-5-5-10-7-16-1-2-2-4-4-6h0c0-1 0-1-1-1-1-1-2-1-2-2v-1c1 0 1-1 1-1l1-1z" class="G"></path><path d="M278 520h2c0 6-1 12 0 18l-1 15v18h-1l-1-5v-4-3c-1 1-1 1-1 2v-1 3c-1-4-1-8-1-12 0-3 0-5-1-7l1-1c1 0 1-1 1-1v-4c1 1 1 2 1 3v1l1-8v-4h0c1-2 0-8 0-10z" class="K"></path><path d="M277 542l1-8v16c-1 2-1 5 0 8h0c0-2 0-4 1-5v18h-1l-1-5v-4-20z" class="c"></path><path d="M276 542v-4c1 1 1 2 1 3v1 20-3c-1 1-1 1-1 2v-1 3c-1-4-1-8-1-12 0-3 0-5-1-7l1-1c1 0 1-1 1-1z" class="E"></path><path d="M283 530c1-7 3-14 8-19 2-3 6-4 9-4 4 0 7 0 10 2 2 3 2 5 2 8 0 2-1 4-3 6h-1s0 1 1 1c0 1 1 2 0 2v1l1 1h2 0c0 1 0 2-1 2v1l-1 1-4-2c-2 0-4-1-6 1-2 1-2 4-3 6h0v2l1 1v-2l2-1c1 0 2-1 3 0 1 0 2 2 3 2 2 0 4-1 6-1h5c4 0 7-3 9-6v1c0 2 1 5 1 7 1 4 1 8 2 12 1 2 2 5 3 7h-1 0c3 2 4 6 5 9l6 11h-1c0 1 1 1 1 2l1 1h0c-1 2 0 3 1 4 0 1-1 1-1 2h-2 0c-3 0-7-1-9 1v-1c0-1 1-1 2-2l-1-1c-1 0-2 1-3 1l-1 2v2h0c2 2 4 3 6 5 6 4 11 10 11 17 0 5-2 9-5 13l-6 3-3 1c-7 3-16 3-24 1-10-3-15-9-20-18-1 0-2 0-3 1h-1 0c-2-3-3-5-3-8-1 1-2 3-2 5h-1c0-2 1-4 2-6v-6l-1-6v-13-8-18l1-15h0c1 0 1-1 2-2l1-6z" class="L"></path><path d="M328 586h1v1 1 2c-1-1-2-2-2-3l1-1z" class="G"></path><path d="M307 544l2-1c-1-1-1-1-1-2l1-1c0 1 1 1 1 1v1s1 1 1 2h-4z" class="K"></path><path d="M333 627c-4 1-8 1-11 2 3-2 6-3 10-4v1h-1c1 0 1 1 2 1z" class="T"></path><path d="M288 604v-2h0 0c2 1 4 1 6 1 1 1 0 2 0 3h0c-1 0-2-1-4-1 0-1-1-1-2-1z" class="V"></path><path d="M325 555v-1h3c1 1 2 3 3 5h0l-6-4z" class="D"></path><path d="M335 613h0c-1-1 0-2 0-3 1-1 1-1 3-2l2 2-1 1v1l-1 1-1-1h-2v1z" class="R"></path><path d="M295 522c-1-1-2-2-3-2-2 0-3 4-5 5v-1-1c1-2 2-4 4-5 2 0 4 2 5 3h-1v1z" class="J"></path><path d="M298 583c1 2 1 4 2 7 0 2-1 4-2 6-1-4 0-7 0-11v-2z" class="M"></path><g class="I"><path d="M314 545l-1-2c1 0 1 0 1-1 1 0 2-1 3-1 1 1 1 2 2 3-1 1-1 1-1 2-1 0-3-1-4-1z"></path><path d="M319 545v-1c1-1 0-3 1-5 1-1 1-1 2-1v9h0l-1-2h-2 0z"></path></g><path d="M340 610c1 1 1 1 1 2v2c-1 0-2 1-3 1s-2 0-3-1v-1-1h2l1 1 1-1v-1l1-1z" class="T"></path><path d="M321 550c-1-1-1-1 0-2l1-1c1 3 4 5 6 7h-3v1c-2-2-3-3-4-5z" class="I"></path><path d="M332 625c3-2 6-4 9-7 1 1 1 1 0 2 0 2-3 5-6 6-1 0-1 1-2 1s-1-1-2-1h1v-1z" class="G"></path><path d="M296 534v2l1 1h0v2l1 1c1 0 2 0 3 1v2c-3 0-4 0-6-1-1-1-1-3 0-4l1-4z" class="J"></path><path d="M296 534v2l1 1h0-1v3h-1v-2l1-4z" class="G"></path><path d="M288 604c1 0 2 0 2 1l1 1c1 0 1 1 3 1v1l3 8c-4-3-6-7-9-12z" class="X"></path><path d="M302 518c0 1 0 1-1 1-1-1-3-2-4-3v-2c0-2 1-2 2-3h3c1 1 2 2 2 3v3c-1 0-1 1-2 1z" class="D"></path><path d="M302 516c-1 0-2-1-3-1l1-1 1-1c1 1 1 1 1 2v1z" class="C"></path><path d="M302 515v-1h2v3c-1 0-1 1-2 1h0 0v-1-1-1z" class="O"></path><path d="M281 605c1-2 0-3 1-4v-1-1h1c1 5 3 9 5 13-1 0-2 0-3 1h-1 0c-2-3-3-5-3-8z" class="M"></path><path d="M296 521v3c-1 2-5 4-6 6-4 2-6 5-7 9l-1-1v-2h0c2-5 7-10 12-13h1v-1-1h1z" class="D"></path><path d="M302 523h1c2 0 4-1 5 0 0 0 0 1 1 1 0 1 1 2 0 2v1l1 1h2 0c0 1 0 2-1 2v1l-1 1-4-2c-2 0-4-1-6 1-2 1-2 4-3 6l-1-1v-2l-1-1c1-2 1-4 2-6 1-1 3-2 5-4z" class="a"></path><path d="M297 527c1-1 3-2 5-4 1 2 1 3 2 5h0c-1 1-2 1-3 0v-1-1c-2 0-2 0-3 2h0c0 1 1 0 2 2l1-1h3l2 1c-2 0-4-1-6 1-2 1-2 4-3 6l-1-1v-2l-1-1c1-2 1-4 2-6z" class="O"></path><path d="M297 527v1c1 1 1 1 2 1 0 1-1 3-2 3s-1 1-2 1c1-2 1-4 2-6z" class="I"></path><path d="M333 580c1-1 2-3 3-4h1c2 0 2 2 4 3 0 1 1 1 1 2l1 1h0c-1 2 0 3 1 4 0 1-1 1-1 2h-2 0c-3 0-7-1-9 1v-1c0-1 1-1 2-2l-1-1c-1 0-2 1-3 1l-1 2v-1-1h-1l5-5v-1z" class="E"></path><path d="M333 580c1 0 2 0 2 1l1 1h1 0c-1 1-1 1-2 1s-1-1-2-1v-1-1z" class="R"></path><path d="M334 586l5-2h0c1 1 1 1 2 1v1h-1l-1 1 2 1h0c-3 0-7-1-9 1v-1c0-1 1-1 2-2z" class="B"></path><defs><linearGradient id="R" x1="274.646" y1="567.506" x2="287.249" y2="565.032" xlink:href="#B"><stop offset="0" stop-color="#bcbeba"></stop><stop offset="1" stop-color="#e3dfe4"></stop></linearGradient></defs><path fill="url(#R)" d="M282 536h0v2l1 1c-1 5-1 11-2 17 0 10 0 21 1 32 0 3 1 7 1 11h-1v1 1c-1 1 0 2-1 4-1 1-2 3-2 5h-1c0-2 1-4 2-6v-6l-1-6v-13-8-18l1-15h0c1 0 1-1 2-2z"></path><path d="M280 598v-2c1 0 1 0 1 1v6h-1v1-6z" class="H"></path><path d="M296 583c0 4 0 11-3 15-1 1-1 2-3 2-1 0-2-1-3-2-3-3-2-22-2-27 3 6 2 13 3 19 1 3 2 5 3 7 1-4 2-7 2-11s0-7-1-10c-2-7-8-12-9-19-1-2 0-4 0-5 0-5 2-8 6-12-2 5-5 10-4 15 0 2 1 4 2 6v-2c1-5 4-10 8-12 1-1 3-2 5-3 0-1 0 0 1 0h1 5 4c1 1 2 1 2 2l1-1c1 0 3 1 4 1 0-1 0-1 1-2v1h0 2l1 2-1 1c-1 1-1 1 0 2 0 2 0 3-1 5 0 1-1 1-1 2h2c1 0 1 1 1 1 1 0 2 0 2 1h1c2 0 3 2 4 3l1 2c-3-3-5-5-8-6s-5 0-8 2l1 1h3c-2 1-4 2-6 4 0 0-1 1-1 2 0 2 1 4 2 6h-2v1c-1 0-3 0-4-1l-1 1-1-1-1 1h-1c-1-1-5-1-7-1 0 2 1 4 1 6-1 0-1 1-1 1 0 1 1 3 0 3z" class="R"></path><path d="M291 561h0c1 0 1 1 1 1h0v5l-1-1h0v-5z" class="O"></path><path d="M314 545c1 0 3 1 4 1 0 2-1 3-2 4-1 0-1 0-2-1 0-1 0-2-1-3l1-1z" class="B"></path><path d="M296 583l-3-14c-1-4-1-8 2-12 2-3 8-3 11-4-1-1-1-2-1-4h0 1c1 0 3 2 4 4v2c0 1-3 2-4 3v1h1c1 0 2 0 3-1 2-1 4-3 4-5h0v-1c1 0 3 3 4 4 1 0 1 0 2-1 0 1-1 1-1 2h2c1 0 1 1 1 1 1 0 2 0 2 1h1c2 0 3 2 4 3l1 2c-3-3-5-5-8-6s-5 0-8 2l1 1h3c-2 1-4 2-6 4 0 0-1 1-1 2 0 2 1 4 2 6h-2v1c-1 0-3 0-4-1l-1 1-1-1-1 1h-1c-1-1-5-1-7-1 0 2 1 4 1 6-1 0-1 1-1 1 0 1 1 3 0 3z" class="K"></path><path d="M310 564v3h1c0 2 1 4 2 6h-2l-1-1v-8z" class="Z"></path><path d="M299 568c1 0 2 0 3-1l1 1c-1 1-1 2-2 3h-3c0-1 0-1 1-2v-1z" class="b"></path><path d="M310 564c1-2 3-3 4-4l1 1h3c-2 1-4 2-6 4 0 0-1 1-1 2h-1v-3z" class="J"></path><path d="M296 564c0-1 0-1 1-2 2-1 4-1 6 0v3l-1 1v1c-1 1-2 1-3 1h-1l-1-1c-1-1-1-2-1-3z" class="H"></path><path d="M302 566v1c-1 1-2 1-3 1h-1v-1l1-1h3z" class="D"></path><path d="M296 564l1-1h0c1 1 1 1 2 1v1c-1 1-1 1-1 2v1l-1-1c-1-1-1-2-1-3z" class="I"></path><path d="M314 560c3-2 5-3 8-2s5 3 8 6c2 3 3 7 2 11-1 5-5 9-9 11-1 1-2 1-4 1 0 1-3 1-3 1 6 1 11 4 15 9 2 4 3 9 1 14-1 3-3 6-6 8-4 2-9 3-13 2-6-2-10-5-13-10-2-3-2-8-1-11s2-7 5-9c0-1 1-1 1-2l-1-2v1h-1v-1h-1 0l-1-1c0 2 0 2-1 4-1-3-1-5-2-7v2c-1-2-1-4-1-6s-1-4-1-6c2 0 6 0 7 1h1l1-1 1 1 1-1c1 1 3 1 4 1v-1h2c-1-2-2-4-2-6 0-1 1-2 1-2 2-2 4-3 6-4h-3l-1-1z" class="G"></path><path d="M304 587h1c1 1 2 1 3 2-2 0-3 1-4 2 0-1 1-1 1-2l-1-2z" class="P"></path><path d="M317 583c1-1 2-1 3-2l3 1c-3 2-6 3-8 4l2-3z" class="Q"></path><path d="M326 576l2-1c-1 3-3 5-5 7l-3-1c1 0 2-1 2-1 1-1 1-2 2-3 1 0 1-1 2-1z" class="C"></path><path d="M315 582v1h2l-2 3-7 3c-1-1-2-1-3-2h-1v1h-1v-1h-1 0c2-1 3-2 5-2h1 1c2-1 4-1 6-3z" class="F"></path><path d="M302 587c2-1 3-2 5-2h1 1v1c-1 1-1 1-2 1h-2-1v1h-1v-1h-1 0z" class="S"></path><path d="M318 561c2 1 3 0 5 1s3 2 5 4c0 1 1 2 1 3 0 2 0 4-1 6l-2 1c-1 0-1 1-2 1-1 1-1 2-2 3 0 0-1 1-2 1-1 1-2 1-3 2h-2v-1c-2 2-4 2-6 3h-1-1c-2 0-3 1-5 2l-1-1c0 2 0 2-1 4-1-3-1-5-2-7v2c-1-2-1-4-1-6s-1-4-1-6c2 0 6 0 7 1h1l1-1 1 1 1-1c1 1 3 1 4 1v-1h2c-1-2-2-4-2-6 0-1 1-2 1-2 2-2 4-3 6-4z" class="L"></path><path d="M329 569c0 2 0 4-1 6l-2 1v-2-2h2 0l1-3z" class="P"></path><path d="M323 562c2 1 3 2 5 4l-1 1c-1 0-1 0-1-1h0c-1-1-2-1-3-1-1-1 0-2 0-3z" class="F"></path><path d="M322 572l1-1v1c0 1-1 2-2 3s-3 0-5 0c-1-1-1-1 0-2s1 0 2 1c1 0 2-1 2-1l1-1h1z" class="G"></path><path d="M320 566h1l2 2v1c-1 0-2 0-2-1-1 0-2 0-2 1s0 0 1 1h1v1c-1 1-2 1-3 1 0 0 0-1 1-2-1 0-1-1-2-1 0-1 0-1 1-2 0-1 1-1 2-1z" class="R"></path><path d="M308 585c1-1 3-6 3-6 1-1 2 0 3 0v-3h1c0 2 1 3 1 5h-3v1h2c-2 2-4 2-6 3h-1zm5-12l1 2h0-17c0 2 0 5 1 7v1 2c-1-2-1-4-1-6s-1-4-1-6c2 0 6 0 7 1h1l1-1 1 1 1-1c1 1 3 1 4 1v-1h2z" class="V"></path><path d="M301 579c2-1 4 0 6 0v6c-2 0-3 1-5 2l-1-1c0 2 0 2-1 4-1-3-1-5-2-7v-1l2 1c1-1 0-3 1-4h0z" class="J"></path><path d="M298 582l2 1c1-1 0-3 1-4h0v7c0 2 0 2-1 4-1-3-1-5-2-7v-1z" class="F"></path><path d="M301 606c0-3 1-7 3-10 2-2 5-4 8-4 5-1 9 0 13 3 3 2 4 4 5 8 0 4-1 8-4 12-2 2-5 4-8 4-5 0-9-2-12-5s-4-5-5-8z" class="L"></path><path d="M320 603c1-1 2-1 2-2v-2h1v3c-1 1-1 1-2 1h-1z" class="D"></path><path d="M314 610c1-1 1-1 2-1 1 1 1 1 1 2s0 3-1 4h-1l-1-1v-4z" class="T"></path><path d="M314 610v-3c2-1 4 0 6 1h2c0 2 0 2-1 3s-1 1-2 0h-2c0-1 0-1-1-2-1 0-1 0-2 1z" class="M"></path><path d="M309 598c0-1 1-1 1-1 1 0 2 0 3 1s1 2 1 3c-1 1-1 2-2 3-2 0-3 0-5-1v-3c0-1 1-2 2-2z" class="O"></path><path d="M320 603h0l-2 1-1-1v-1-4c-1 0-1-1-2-2h1c1 0 2-1 3 0 1 0 3 1 4 3h-1v2c0 1-1 1-2 2z" class="Z"></path><path d="M301 606c2 0 3 1 4 1s4-1 5 0h1c1 1 0 2 0 3-1 1-2 1-3 2h-2v2c-3-3-4-5-5-8z" class="P"></path><path d="M310 607h1c1 1 0 2 0 3-1 1-2 1-3 2 0 0-1 0-2-1v-2c1 0 2 1 3 0 0-1-2 0-3-2h4z" class="G"></path><path d="M78 104v-1h1c8-1 16-1 24-1 7 0 14-1 21 1 3 1 5 3 8 4h0c-2-1-5-3-5-4h-1c3 1 6 2 9 4l4 4c0 1 1 1 1 2h2v-1h1l-5-6 1-1c6 3 11 9 18 12 1 0 3 1 5 2h0 5c2-4 4-8 8-9 2-1 4 0 6 1 3 2 6 4 6 8 1 3 0 6-1 9-2 3-6 5-9 7-1 0-2 1-3 2-1-1-2-1-4 0l-8 3h-1c1-2 2-2 3-3 4-2 7-3 10-5l5-3v-1c1 0 2-1 2-2 1-1 1-2 2-3 0-1 0-2-1-3h-1s-1 0-1-1c-1-1-3-3-5-3-7 3-10 10-14 17l-8 14h1l2-1h1 1c0-1 1-1 2-1h0c2-1-1 0 1-1v1c-4 2-8 4-11 6l-9 6v1c-4 3-9 6-13 10-7 5-15 11-21 18-2 3-4 7-6 11h0l-4 6-15 28v-2c1 0 0 0 0-1l-1 2c1-3 2-4 3-7 1-6 5-11 8-16 2-2 3-5 4-7 0 0 0-1 1-1 1-3 3-6 5-9-2 1-5 2-7 2-5 1-10 0-14-3-4-2-7-7-8-11v-2c-1-2-1-5-1-8v-6c0-4 1-7 2-11v-4c2-6 4-11 7-16 0-1-1-2-2-2 1-1 5-6 5-8-1 0 0-1 0-2h2c1-1 3-3 3-5 1-1 0-2 0-3l-1-1h-1c0 1 1 1 0 3v-1h0c0-1 0-1-1-1 0-1-2-2-3-2h-1c0 1-1 0-2 0h0l-1-1s-1 1-2 1v-1c1-2 1-2 1-4z" class="S"></path><path d="M127 124s0 1 1 1c0 1 1 1 1 1 0 2 0 4-1 6v-3h0c-1 2-1 4-1 6v-11z" class="F"></path><path d="M81 170l1 1h0c0 2 1 4 1 6v6c-1-1-1-2-1-3-1-3-1-6-1-10z" class="M"></path><path d="M79 108c2-1 2-1 4-1s4 2 5 3l1 1-1-1h-1c0 1 1 1 0 3v-1h0c0-1 0-1-1-1 0-1-2-2-3-2h-1c0 1-1 0-2 0h0l-1-1z" class="D"></path><path d="M118 127c1 0 1-1 2-2h2c-1 2-2 4-3 5-2 1-4 1-6 1v-1l2-3c1-1 2 0 3 0z" class="a"></path><path d="M104 115h0 1c5-2 13-1 18 2l3 2c-3-1-6-2-9-2-4-1-9-1-13-1-1 0-1 0-2-1h2z" class="J"></path><path d="M86 165l1 2-1 1c2 4 4 5 8 7h0c1 1 2 1 4 1-1 1 0 1-1 1-1-1-2 0-3 0-5-1-8-3-12-6h0l-1-1c1-2 2-3 3-4 1 0 1 0 2-1z" class="D"></path><path d="M86 165l1 2-1 1-2-2c1 0 1 0 2-1z" class="F"></path><path d="M129 151c2-1 5-1 7-2l3-3h2v1l-2 2h0c-1 0-1 0-2 1h-1v3h1c-3 2-6 4-9 5h-1l-1-1s1-2 1-3c-1 0-1-1-2-1h-2v-1h1c2 0 4 0 5-1h0z" class="J"></path><path d="M127 158l1-1v-1c1 0 2-2 3-2s2-1 3-2c1 0 1-1 2-2v3h1c-3 2-6 4-9 5h-1z" class="D"></path><path d="M94 128c1-1 2-3 4-4 4-3 9-2 14-2v1c1 0 4 1 5 2l1 2c-1 0-2-1-3 0l-2 3v1c1 1 2 1 2 2v2c-1 0-1-1-1-1h0c-1 0-1-1-1-1v-4h-5c-1 2-1 3-3 3-1 0-2 0-3 1-1-1-2-1-3-1-2 1-3 4-3 7v1 1l1 1c1 1 2 3 2 3-2 0-2 0-3-1-2-3-3-5-3-7 0-3 1-6 1-9z" class="K"></path><path d="M95 139c0-3 0-6 1-8 1-1 2-1 3-2l1 2-1 1c-2 1-3 4-3 7v1 1c0-1 0-2-1-2z" class="R"></path><path d="M112 123c1 0 4 1 5 2l1 2c-1 0-2-1-3 0l-2 3c0-2 0-4-1-6-1-1 0-1 0-1z" class="G"></path><path d="M93 137c0-1 0-2 1-2l1 1v3c1 0 1 1 1 2l1 1c1 1 2 3 2 3-2 0-2 0-3-1-2-3-3-5-3-7z" class="B"></path><path d="M102 128c0-1 0-2 2-3 1-1 2-1 3 0 1 0 2 1 2 1 0 2 0 2-1 3-1 2-1 3-3 3-1 0-2 0-3 1-1-1-2-1-3-1l1-1-1-2h3 0v-1z" class="M"></path><path d="M99 129h3c0 1-1 2 0 4-1-1-2-1-3-1l1-1-1-2z" class="F"></path><path d="M102 128c0-1 0-2 2-3 1-1 2-1 3 0 1 0 2 1 2 1 0 2 0 2-1 3-1 2-1 3-3 3 0 0-1 0-2-1l2-1c1 0 1 0 1-1h0c1-1 1-1 1-2l-1-1c-2 0-2 1-4 2h0z" class="R"></path><path d="M78 104v-1h1c8-1 16-1 24-1 7 0 14-1 21 1 3 1 5 3 8 4h0c-2-1-5-3-5-4h-1c3 1 6 2 9 4l4 4c0 1 1 1 1 2l2 2c4 6 5 11 3 18 0 3-3 6-5 7-4 2-10 3-15 1-1 0-2-1-3-2v-1c1 0 2 1 3 1 1 1 2 1 3 1h1c3 1 5 0 7-2 4-2 7-5 7-10 1-4-1-9-3-12-1-1-5-6-7-6v1c1 2 1 3 1 5 0 3 0 8-2 10h0v-6c0-4 0-9-3-12-3-2-7-3-10-4-8-1-17 0-26 0H78z" class="O"></path><path d="M86 165c-3-7-5-14-3-21 2-9 7-19 15-24 2-1 4-1 6-1 6-1 12 0 18 2 0-1 0-1 1-1h1c1-1 1-1 2-1s3 0 4 1v1 1h-2c-1 0-1 1-1 2v11c-1 1 0 2-1 3h0c-1-1 0-3 0-4 0-2 1-10 0-11s-1-1-2-1v2c-1 1-1 1-2 1h0-2c-1 1-1 2-2 2l-1-2c-1-1-4-2-5-2v-1c-5 0-10-1-14 2-2 1-3 3-4 4l-2 2v1l-3 6c-2 5-4 11-3 16 1 6 6 9 10 12-1 1-2 1-3 2 0-1-1-1-2-2v2c1 1 2 2 3 2 3 1 7-1 9-3 1-1 2-3 3-4h0c0 2 0 4-1 5-2 2-4 3-6 4-1 1-2 2-3 2-2 0-4 0-5-1-2-2-3-3-4-5l-1-2z" class="R"></path><path d="M112 122h0c4 0 7 2 10 2l2-2v2c-1 1-1 1-2 1h0-2c-1 1-1 2-2 2l-1-2c-1-1-4-2-5-2v-1z" class="F"></path><path d="M92 131v-1l2-2c0 3-1 6-1 9 0 2 1 4 3 7h-1v2h0c2 2 4 3 6 3s3-2 5-3c0-1 1-2 1-2v-1c3 2 6 4 7 8 2 5 0 11-3 15-2 4-7 8-12 9-1 1-4 1-5 0-4-2-6-3-8-7l1-1c1 2 2 3 4 5 1 1 3 1 5 1 1 0 2-1 3-2 2-1 4-2 6-4 1-1 1-3 1-5h0c-1 1-2 3-3 4-2 2-6 4-9 3-1 0-2-1-3-2v-2c1 1 2 1 2 2 1-1 2-1 3-2-4-3-9-6-10-12-1-5 1-11 3-16l3-6z" class="L"></path><path d="M93 151l1 1v3c-1-1-1-1-2-1 1-2 1-2 1-3z" class="W"></path><path d="M92 131v-1l2-2c0 3-1 6-1 9 0 2 1 4 3 7h-1v2l-1-2c-3-4-3-8-2-13z" class="E"></path><path d="M139 105c6 3 11 9 18 12 1 0 3 1 5 2h0 5c2-4 4-8 8-9 2-1 4 0 6 1 3 2 6 4 6 8 1 3 0 6-1 9-2 3-6 5-9 7-1 0-2 1-3 2-1-1-2-1-4 0l-8 3h-1c1-2 2-2 3-3 4-2 7-3 10-5l5-3v-1c1 0 2-1 2-2 1-1 1-2 2-3 0-1 0-2-1-3h-1s-1 0-1-1c-1-1-3-3-5-3-7 3-10 10-14 17l-8 14h1l2-1h1 1c0-1 1-1 2-1h0c2-1-1 0 1-1v1c-4 2-8 4-11 6l-9 6v1c-4 3-9 6-13 10-7 5-15 11-21 18-2 3-4 7-6 11h0l-4 6-15 28v-2c1 0 0 0 0-1l-1 2c1-3 2-4 3-7 1-6 5-11 8-16 2-2 3-5 4-7 0 0 0-1 1-1 1-3 3-6 5-9-2 1-5 2-7 2-5 1-10 0-14-3-4-2-7-7-8-11v-2c-1-2-1-5-1-8v-6c0-4 1-7 2-11v-4c2-6 4-11 7-16 2-3 3-5 5-8 3-4 6-7 10-10 3-2 6-5 10-7l-3 3-4 3c2-1 4-2 7-2 4-2 9-2 13 0 5 1 7 3 9 8-3-1-5-2-8-3-5-2-11-2-16 0h-2c1 1 1 1 2 1l-6 3c-2 0-3 2-5 3-6 6-11 15-14 23-4 11-8 24-2 36 1 3 4 7 8 8 5 2 11 1 16-2 4-2 8-5 11-8 2-2 4-4 6-7 2-2 5-4 8-7 3-2 6-4 9-7 5-3 10-5 13-9 7-9 11-19 18-28h-1c-1 1-2 1-4 0-4-1-9-3-13-6 1 2 3 4 3 7h0c1 3 1 7 1 10-1 2-1 4 0 5-2 5-5 10-9 13l-6 3h-1v-3h1c1-1 1-1 2-1h0l2-2v-1h-2l-3 3c-2 1-5 1-7 2-3 0-5 0-8-1h0v1c0 1 1 1 1 3l1-1c1 2 0 6 0 8s-2 5-4 7c-2 3-5 4-8 5-5 2-9 4-15 4h-2c1 0 2-1 3 0 1 0 0 0 1-1-2 0-3 0-4-1h0c1 1 4 1 5 0 5-1 10-5 12-9 3-4 5-10 3-15-1-4-4-6-7-8v1s-1 1-1 2c-2 1-3 3-5 3s-4-1-6-3h0v-2h1c1 1 1 1 3 1 0 0-1-2-2-3v-2c1-1 1-1 3-2 1-1 4 0 6 0h0c3 1 5 3 7 4 4 2 10 5 15 5 7-1 13-5 17-10l3-5c1-3 2-5 2-8-1-5-4-9-7-12l-5-6 1-1z" class="O"></path><path d="M148 132c0 3 0 3-1 5l-1 1c0-1-1-1-1-1l3-5z" class="D"></path><path d="M145 141h1c1 1 1 2 0 3l-1 1-1-1v-1l1-2z" class="L"></path><path d="M119 158h0c1 0 1 0 2 1-1 2-1 2-2 3l-1 1h0l1-5z" class="B"></path><path d="M179 129c0 1 0 1 1 2-1 0-2 1-3 1s-1 1-2 0h-1l5-3z" class="M"></path><path d="M175 116c2-1 3-1 5 0 1 1 2 3 2 4h-1s-1 0-1-1c-1-1-3-3-5-3z" class="C"></path><path d="M183 123v3c0 1-2 3-2 4l-1 1c-1-1-1-1-1-2v-1c1 0 2-1 2-2 1-1 1-2 2-3z" class="D"></path><path d="M97 142v-2c1-1 1-1 3-2 0 2 2 3 0 6h0c0 1 0 1-1 1 0 0-1-2-2-3z" class="G"></path><path d="M84 223c1-6 5-11 8-16 2-2 3-5 4-7h0c-1 3-2 5-3 7-1 3-4 6-5 9 0 1-1 1-1 2h1c1-1 2-3 3-5 0-2 1-4 2-5l1-1c0-1 1-2 1-2 0-1 0-1 1-2h1l-15 28v-2c1 0 0 0 0-1l-1 2c1-3 2-4 3-7z" class="Z"></path><path d="M246 520h-1c0-3-2-7 1-10 1-2 4-3 7-3 4-1 9 1 12 4 2 2 5 6 6 9l1 7v3c0 1 1 4 1 5l-1-2-2 1c2 3 3 7 4 10 1 2 1 4 1 7 0 4 0 8 1 12v-3 1c0-1 0-1 1-2v3 4l1 5h1v8 13l1 6v6c-1 2-2 4-2 6h1c0 3 1 4 2 6v3h0v7c1 4 1 6 4 9h0c-1 0-4 0-5 1l1 2h-4-4c-2 1-7 1-9 0 1-1 1-1 2-1v-1h0c-3 1-7 1-10 1h-9c-1-1-4-1-6-1h0c-5 0-10 0-15-1-2 0-5-1-8-2h0c-1 2-2 3-3 4h-1-9c-12 0-24 1-35-1h-4c-1 0-1 1-1 1h0-1c-2-1-4-3-6-5-1-4-1-7 0-10v-1c2-2 4-4 7-5 5-2 11-6 16-9 0 0 1 0 1 1h1c4-2 8-6 11-9s6-5 9-8c7-6 12-14 16-22 1-1 1-2 1-2 1-2 2-4 4-5 3-4 6-7 9-9 1 1 1 2 2 3h1l1 1s1 0 1-1h0l1-1 1-3 2 1v-1c0-2-1-2-2-3-1-2-1-4-1-7v-3h-3v-2c2 0 3 1 4 1v-2h-1-1l1-1h0c3 0 5-3 7-5v-1l-2-3 1-1c1 1 1 1 2 0h0l-1-1v-2c2 1 3 1 4 0h0c-2 0-3-1-4-2z" class="K"></path><path d="M246 520h0 2c1 0 2 1 2 2-2 0-3-1-4-2z" class="F"></path><path d="M236 569v-1h2c1 1 1 2 0 3h0c-1 0-1-1-2-2z" class="J"></path><path d="M250 575c-2 0-4 1-6 0h0c2-1 3-1 4-1h3l-1 1z" class="U"></path><path d="M245 581l1 1c0 1 1 2 1 3l-5-3c1-1 2 0 3-1z" class="N"></path><path d="M220 610c1 1 2 1 2 3-1 1-1 1-2 1h0c0-1-1-3 0-4z" class="I"></path><path d="M262 606v-2h2s1 0 1 1h-1v1h1c0 1-1 1-1 1h-1 0l-1-1z" class="M"></path><path d="M187 621c2 2 2 2 3 5v1h0 0-1v-1c0-1-2-2-3-2l1-3z" class="I"></path><path d="M258 555c3 1 4 2 5 4-3 0-3-1-5-3v-1z" class="C"></path><path d="M220 624c-2-1-3-3-4-5l7 4c-1 1-2 1-3 1z" class="Z"></path><path d="M233 573c1 1 2 3 4 3 1 0 2 0 3-1 0 1-1 2-2 3h0c-2 0-3 0-4-2-1-1-1-2-1-3z" class="C"></path><path d="M262 606l1 1h0c1 1 1 2 2 3h0c-1 1-4 3-5 4 1-3 2-5 2-8h0z" class="N"></path><path d="M242 582s-1 0-1-1v-2c1-1 2-1 3-1s1 0 2 1c-1 1-1 1-1 2h0c-1 1-2 0-3 1z" class="E"></path><path d="M217 579c0 1 1 2 1 3 0 0-1 1 0 1h2v1l-2 1v-1h-3l-1 1c0-1-1-1-1-2h1c2-1 2-2 3-4z" class="G"></path><path d="M265 606h0c1 0 1-1 1-1 0-1 1-1 2-2l2-3c-1 3-3 7-5 10h0c-1-1-1-2-2-3h1s1 0 1-1z" class="D"></path><path d="M254 518v-1c1 0 1-1 1-1h1 0c0-1 1-2 1-2 1 0 1-1 2-1v3l-3 3c-1 0-1 0-2-1z" class="E"></path><path d="M249 581c1 1 0 3 2 4h0c-1 1-1 1-1 2-1 1-1 0-1 1 0-1-1-1-1-2v-1h-1c0-1-1-2-1-3h2c1 0 1-1 1-1z" class="C"></path><path d="M248 574c2-1 4-1 6-1h3c1-1 3-3 3-4l-1 6h-9l1-1h-3z" class="V"></path><path d="M182 619c2 1 4 1 5 2l-1 3h-2c-1 0-1-1-2-1 0 0 0-1-1-1 1-1 1-1 1-2v-1z" class="J"></path><path d="M216 612c1-1 1-2 2-3h2v1c-1 1 0 3 0 4h0 0c-1 1-2 1-4 1v-3z" class="U"></path><path d="M216 612c1-1 1-2 2-3h2l-1 1v3h0 0c-1-1-2-1-3-1z" class="e"></path><path d="M227 572c1 1 3 2 3 4 1 1 1 3 2 4h0c1 0 4 1 5 2l1 1-2 1-3-3c-4-2-5-6-6-9z" class="F"></path><path d="M238 583l4 2c1 0 2 1 3 1s1 1 2 1h0c1 0 1 1 2 1h0c1 1 1 1 2 1 2 1 4 4 5 6v1c-3-7-13-10-20-12l2-1z" class="B"></path><path d="M185 616h2c1 0 2 0 3-1v1h1c1 1 1 1 1 2v1 2c-1 0-4-3-5-3-1-1-3-1-4-2h2z" class="H"></path><path d="M270 534c-1-2-3-4-5-5-1-2-4-3-5-5 1-1 1 0 2 0 4 2 9 5 10 9l-2 1z" class="E"></path><path d="M254 518c-1-1-2-2-2-4 0-1 0-2 1-2 1-1 3-1 4-1 1 1 2 1 2 2-1 0-1 1-2 1 0 0-1 1-1 2h0-1s0 1-1 1v1z" class="J"></path><path d="M257 562c0-1 0-1 1 0h0 2v3c-1 2-2 3-3 4h-1l-1-1c0-2-1-3-1-5 1-1 2-1 3-1z" class="E"></path><path d="M261 533c1 2 2 6 1 8h0c0 1-1 2-2 2h-3s-1-1-2-1c0-1 0-1 1-2 1 0 3 0 4-1 0-1-1-3-1-5 1 0 1-1 2-1z" class="I"></path><path d="M260 539h1c0 1 0 2-1 2l1 1c-2 1-2 1-4 1 0 0-1-1-2-1 0-1 0-1 1-2 1 0 3 0 4-1z" class="J"></path><path d="M233 573c0-2-1-4 0-6 1-1 2-1 3-2 1 1 2 1 2 3h-2v1h-1 0c0 2 0 3 1 4s1 1 2 1 2-1 3 0l-1 1c-1 1-2 1-3 1-2 0-3-2-4-3z" class="I"></path><path d="M249 581v-2c1-1 3-1 4 0h1c1 2 1 5 1 7l-1 1c-2 0-2-1-3-2h0c-2-1-1-3-2-4z" class="E"></path><path d="M223 623c3 1 6 3 9 4v1h0v2h0-3-1-1l-1-2c-2 0-4-2-6-4 1 0 2 0 3-1z" class="I"></path><path d="M232 627v1h0v2h0-3-1-1l-1-2h1c2 0 3-1 5-1z" class="F"></path><path d="M271 560v-19c2 4 4 12 3 17 0 1-1 2-1 3v-1c-1 1-1 2-2 3v-3z" class="R"></path><path d="M217 579c1-1 2-2 4-3 1 2 3 4 4 7h0c-1 0-2-1-3-1 1 2 4 2 6 4-1 0-1 1-2 1h-1c0-1 0-1-1-1h0-3c-1 0-2-1-3-1l2-1v-1h-2c-1 0 0-1 0-1 0-1-1-2-1-3z" class="D"></path><path d="M220 583l-1-1c0-2 1-2 1-3h1v3h1c1 2 4 2 6 4-1 0-1 1-2 1h-1c0-1 0-1-1-1h0-3c-1 0-2-1-3-1l2-1v-1z" class="M"></path><path d="M262 524c0-1 1-2 1-3s1-1 2-1 1 1 2 2v1c1 0 1-1 1-1 1 0 1-1 0-2h1 2l1 7v3c0 1 1 4 1 5l-1-2c-1-4-6-7-10-9z" class="B"></path><path d="M271 570c1 2 1 5 1 7 0 6 0 13-2 19-1 1-2 3-3 4h-2c-4-2-7-6-8-10 0-2 0-4 1-6v-4c1 4 1 14 5 17h1 1l-1-1c1 0 1 0 2-1h0c5-8 3-17 5-25z" class="J"></path><path d="M250 522c3 1 7 2 9 5v1c1 1 1 3 2 5-1 0-1 1-2 1l-3-3c-2-1-4-1-6 0l-3 1h0c-1-1-1-1-1-2v-1l-2-3 1-1c1 1 1 1 2 0h0l-1-1v-2c2 1 3 1 4 0z" class="T"></path><path d="M256 531h1 1v-1-1c0-1 1-1 1-1 1 1 1 3 2 5-1 0-1 1-2 1l-3-3zm-9-6c1 2 1 3 1 4s-1 2-1 2v1h0c-1-1-1-1-1-2v-1l-2-3 1-1c1 1 1 1 2 0h0z" class="D"></path><path d="M183 616c0-3 5-5 8-7v-2c1-1 2-3 3-4 3-2 4-2 7-2v1l-1 6h-1-2l-1 1c-2 1-4 1-5 3h0c1 1 2 1 3 1l1 1c1 0 1 1 1 2v1c-1 0-1 1-2 1l-2 1v-1c0-1 0-1-1-2h-1v-1c-1 1-2 1-3 1h-2-2z" class="I"></path><path d="M185 616c2-1 3-2 4-2l1 1c-1 1-2 1-3 1h-2z" class="C"></path><path d="M197 608h-3v-1c2-1 3-2 4-3s2-2 3-2l-1 6h-1-2z" class="K"></path><path d="M194 613l1 1c1 0 1 1 1 2v1c-1 0-1 1-2 1l-2 1v-1c0-1 0-1-1-2 0 0 0-1-1-1h0v-1c1-1 3-1 4-1z" class="G"></path><path d="M194 613l1 1-1 1c0 1-1 1-2 1v2h2l-2 1v-1c0-1 0-1-1-2 0 0 0-1-1-1h0v-1c1-1 3-1 4-1z" class="M"></path><path d="M246 530c0 1 0 1 1 2h0l3-1c2-1 4-1 6 0l3 3c0 2 1 4 1 5-1 1-3 1-4 1v-2l-1-1-2-1c-1 0-1 1-1 2h-3-9v-2h-1-1l1-1h0c3 0 5-3 7-5z" class="F"></path><path d="M246 530c0 1 0 1 1 2-2 1-4 3-5 4h1 7v-2c1-1 1-1 2-1s2 1 3 2v2l-2-1c-1 0-1 1-1 2h-3-9v-2h-1-1l1-1h0c3 0 5-3 7-5z" class="I"></path><path d="M215 584h3v1c1 0 2 1 3 1l3 2h0c-5 4-11 6-14 11-3-1-3-1-5 1v-2h0l-2-1s-1 1-2 1c0-1 0-1 1-2 3-3 8-6 13-8l-1-3h0l1-1z" class="F"></path><path d="M215 584c1 1 2 1 2 3v1h-2l-1-3h0l1-1z" class="D"></path><path d="M215 588h2 2v1c-2 2-5 2-8 3l-6 3c0 1-2 1-2 2 0 0-1 1-2 1 0-1 0-1 1-2 3-3 8-6 13-8z" class="I"></path><path d="M273 562v1l-2 3c1 1 0 1 2 2 0 1 0 1-1 2v-1h0l-1 1c-2 8 0 17-5 25h0l-2-7c0-3-1-7 0-10 1-4 3-8 6-12 1-1 2-3 3-4z" class="S"></path><path d="M256 549c2 0 2 0 4 1h0v1c2 1 4 2 5 4v1c1 2 2 4 2 6 1 2 0 3 1 5l2-1c-3 4-5 8-6 12-1 3 0 7 0 10l2 7c-1 1-1 1-2 1-2-4-3-14-2-19 0-2 1-5 2-8s0-6-1-10c-1-2-2-3-5-4l-4-1c-1-1-2-1-2-1l-1-2h3 0c2 0 1 0 2-2z" class="D"></path><path d="M203 597l2 1h0v2c2-2 2-2 5-1-2 1-3 3-4 5h0c-1 5-3 11-1 16 0 1 0 1-1 2h-2-1v-1l-1-1v-1c-1 0-2 0-3-1 0 0 0-1-1-1h0v-1c0-1 0-2-1-2l-1-1c-1 0-2 0-3-1h0c1-2 3-2 5-3l1-1h2 1l1-6v-1-2l-1-1h1c1 0 2-1 2-1z" class="M"></path><path d="M201 621v-2c0-2 1-3 1-4h0 1c0 2 1 5 1 7h-2-1v-1z" class="O"></path><path d="M205 598h0v2c2-2 2-2 5-1-2 1-3 3-4 5h0c0 1-1 1-1 2h-1-1c0-3 2-5 2-8z" class="Q"></path><path d="M203 606h1 1c0-1 1-1 1-2-1 5-3 11-1 16 0 1 0 1-1 2 0-2-1-5-1-7 0-3-1-6 0-9z" class="i"></path><path d="M194 613c-1 0-2 0-3-1h0c1-2 3-2 5-3v1l1 2h0c1-1 2-1 2-2h1c0 1-1 1-1 1 0 3 1 6 1 8-1 0-2 0-3-1 0 0 0-1-1-1h0v-1c0-1 0-2-1-2l-1-1z" class="F"></path><path d="M197 612h0c1-1 2-1 2-2h1c0 1-1 1-1 1v3h-2v-1-1z" class="K"></path><path d="M181 607s1 0 1 1l-7 5c-5 3-14 6-15 12-1 2 0 6 0 7h1 0c-1-2 0-4 1-5 3-4 8-7 13-7 2-1 5-1 7-1v1c0 1 0 1-1 2 1 0 1 1 1 1 1 0 1 1 2 1-1 0-1 0-2 1-3-2-7-3-10-2l-2 2h0l-1 1h-2l-1 1 1 1h1c-1 1-2 1-2 2 1 1 1 3 2 4l2 2h0-4c-1 0-1 1-1 1h0-1c-2-1-4-3-6-5-1-4-1-7 0-10v-1c2-2 4-4 7-5 5-2 11-6 16-9z" class="D"></path><path d="M166 636h-1c-1-1-3-2-3-4h0v-3c2-3 7-5 10-6l-2 2h0l-1 1h-2l-1 1 1 1h1c-1 1-2 1-2 2 1 1 1 3 2 4l2 2h0-4z" class="B"></path><path d="M236 537c2 0 3 1 4 1h9 3l-1 4v2c2 0 6 0 8 1s4 2 6 4c2 4 4 7 5 12l1-1v3c1-1 1-2 2-3v1 1c-1 1-2 3-3 4l-2 1c-1-2 0-3-1-5 0-2-1-4-2-6v-1c-1-2-3-3-5-4v-1h0c-2-1-2-1-4-1-1 2 0 2-2 2h0-3l1 2s1 0 2 1l-2 2v1h1c-1 0-3 1-4 1l-3-2-1-1v-1c-1 0-2-1-2-2h-1c0-2-1-2-2-3-1-2-1-4-1-7v-3h-3v-2z" class="T"></path><path d="M254 551l-1-4h1c0 1 0 2 2 2-1 2 0 2-2 2h0z" class="N"></path><path d="M259 545c2 1 4 2 6 4h-2c-2-1-3-2-4-4zm-14 0v3c1-1 1 0 1-1l1-1h1v1c-1 1-1 2-1 4 1 0 1 0 2-1v1c-2 1-3 2-4 4v-1c-1 0-2-1-2-2 0-3 1-4 2-7z" class="E"></path><path d="M239 542h2c1 2 2 2 3 3h1c-1 3-2 4-2 7h-1c0-2-1-2-2-3-1-2-1-4-1-7z" class="B"></path><path d="M249 550l1-1c1 0 1 1 1 2l1 2s1 0 2 1l-2 2v1h1c-1 0-3 1-4 1l-3-2-1-1c1-2 2-3 4-4v-1z" class="F"></path><path d="M245 555c1-2 2-3 4-4 0 1 0 2-1 3l-2 2-1-1z" class="Q"></path><path d="M236 537c2 0 3 1 4 1h9 3l-1 4v2c-3-1-7-1-9-2h-1-2v-3h-3v-2z" class="F"></path><path d="M236 537c2 0 3 1 4 1h9c-2 0-7 0-9 1 0 1 1 2 1 2l1 1h0-1-2v-3h-3v-2z" class="f"></path><path d="M172 623c3-1 7 0 10 2 1-1 1-1 2-1h2c1 0 3 1 3 2v1h1 0c1-1 2-1 3-2 3 0 5 3 6 5 2 1 3 2 4 4l2 2v1h0c-12 0-24 1-35-1h0l-2-2c-1-1-1-3-2-4 0-1 1-1 2-2h-1l-1-1 1-1h2l1-1h0l2-2z" class="S"></path><path d="M170 632c1 1 2 1 3 2l-1 1c-1 1-2 1-2 1l-2-2 2-2z" class="M"></path><path d="M168 628h1c-1 2 0 3 0 4h1l-2 2c-1-1-1-3-2-4 0-1 1-1 2-2z" class="H"></path><path d="M189 631c-1 0-1 1-1 1-1 1-1 1-2 1-2-1-2-1-3-2-2 0-3 1-5 2h-3l-1-1c1-2 3-4 3-6-1 0-2-1-2-2 2 1 3 1 5 1l2 2c1 1 2 1 4 1 1 0 1 0 2 1v1l1 1z" class="D"></path><path d="M182 625c1-1 1-1 2-1h2c1 0 3 1 3 2v1h1 0c1-1 2-1 3-2 3 0 5 3 6 5 2 1 3 2 4 4l2 2v1l-6-5c-2 0-4 0-5 1l-2 1c-1-1-2-1-2-2s0-1-1-1h0l-1-1v-1c-1-1-1-1-2-1-1-1-3-2-4-3z" class="O"></path><path d="M242 552h1c0 1 1 2 2 2v1l1 1 3 2c1 0 3-1 4-1-1 2-2 2-3 3-1 0-2 0-4-1h0c-1 1-1 1-1 2 1 1 1 1 1 3s0 5-2 7c0 1 0 1-1 1 0-1 0-1 1-1 0-2 1-4 0-6 0-1-1-2-2-3-3 0-6-1-9 0-2 1-4 4-5 6 0 1-1 3-1 4 1 3 2 7 6 9l3 3c7 2 17 5 20 12 1 3 2 5 2 7 0 5-1 8-3 11-4 4-9 7-14 7s-11-1-14-4c-2-3-4-7-4-11s2-9 6-12c4-4 9-5 14-5-3-1-6-1-9-2s-6-4-7-7c-3-4-3-7-2-11 1-5 3-7 7-9 1-1 1-1 2-1 1-1 1-1 1-2v-1h1l1 1s1 0 1-1h0l1-1 1-3 2 1v-1z" class="R"></path><path d="M236 556l1 1s1 0 1-1v2c1 0 2-1 3-1l-1 2c-3-1-5 0-8 1 1-1 1-1 2-1 1-1 1-1 1-2v-1h1z" class="B"></path><path d="M244 557c2 1 4 3 6 3-1 0-2 0-4-1h0c-1 1-1 1-1 2 1 1 1 1 1 3-1-3-3-4-6-5l1-2h0c2 1 2 0 3 0z" class="C"></path><path d="M242 552h1c0 1 1 2 2 2v1l1 1 3 2c1 0 3-1 4-1-1 2-2 2-3 3-2 0-4-2-6-3-1 0-1 1-3 0h0c-1 0-2 1-3 1v-2h0l1-1 1-3 2 1v-1z" class="H"></path><path d="M240 552l2 1c0 1 1 2 1 3l1 1c-1 0-1 1-3 0h0c-1 0-2 1-3 1v-2h0l1-1 1-3z" class="F"></path><path d="M240 552l2 1c0 1 1 2 1 3-1 0-2-1-4-1l1-3z" class="B"></path><path d="M238 592c3-1 7-1 9 0 3 1 5 4 6 7 2 2 2 6 1 9a19.81 19.81 0 0 1-11 11c-4 0-7 0-10-2s-6-6-7-10c0-4 0-7 2-10 3-3 6-5 10-5z" class="L"></path><path d="M234 596l2 1c-1 1-1 2-2 2v1 1l-1 1h-1l1-3c0-1 0-2 1-3z" class="I"></path><path d="M234 596c1-1 3-1 4-1l1 1h0c0 1-1 1-2 2l-1 2c-1 0-1 0-2-1 1 0 1-1 2-2l-2-1z" class="N"></path><path d="M250 606h1v1c0 1-1 2-2 3 0 1-2 3-3 3 1-1 1-1 1-2h-2c0-2-1-3-1-4l6-1z" class="D"></path><path d="M237 598h1 0 1l-1 2h0 2v1h-1v1c1 0 1 0 1 1s-1 1-2 1c-2 1-3 0-5 0-1-1-1-1-1-2h1l1-1v-1-1c1 1 1 1 2 1l1-2z" class="P"></path><path d="M243 597h1c1 0 2 0 2 1 2 0 3 2 3 3s-1 2-2 2l-1 1c-1 0-2 0-3-1s-1-2-1-3 1-2 1-3z" class="R"></path><path d="M241 611c0 1 0 3-1 4-2-1-1-3-3-4-2 0-3 1-5 1 0-1 0-2 1-3 0-1 1-2 2-2h6v4z" class="I"></path><path d="M241 607v4h0 0c-2-1-3-2-3-3h0l3-1z" class="J"></path><path d="M276 563v-3 1c0-1 0-1 1-2v3 4l1 5h1v8 13l1 6v6c-1 2-2 4-2 6h1c0 3 1 4 2 6v3h0v7c1 4 1 6 4 9h0c-1 0-4 0-5 1l1 2h-4-4c-2 1-7 1-9 0 1-1 1-1 2-1v-1h0c-3 1-7 1-10 1h-9c-1-1-4-1-6-1h0c-5 0-10 0-15-1-2 0-5-1-8-2h0c-1 2-2 3-3 4h-1-9 0v-1l-2-2c-1-2-2-3-4-4 1-2 1-3 2-5-1-1-2-3-3-4h0v-1h1c0 1 0 1 1 1v-1l1 1v1h1 2c1-1 1-1 1-2-2-5 0-11 1-16h0c1-2 2-4 4-5 3-5 9-7 14-11h0l-3-2h3 0c1 0 1 0 1 1h1c1 0 1-1 2-1 0 0 1 1 1 2-1 1-3 4-5 4-6 4-12 8-14 16-1 5 0 9 3 13 3 5 9 7 14 9h1 1 3l5 1c11 0 19-3 26-10 4-4 6-8 8-12 6-15 5-31 5-46z" class="O"></path><path d="M277 566l1 5h1v8c-1-1-1-2-1-2v3c-1-4-1-10-1-14z" class="b"></path><path d="M278 580v-3s0 1 1 2v13-4h-1v8-16z" class="Y"></path><path d="M211 627c1 2 5 4 6 6h-1c-2 0-4 1-6 0 0-1-1-2-1-3s1-1 2-2v-1z" class="P"></path><path d="M200 620l1 1v1h1 2c1-1 1-1 1-2 1 1 1 2 1 3l3 3h-1l-1-1h-1c0 1 1 2 1 3v1h-1c-1 0-1 0-1-1l-1 1c-1-1-2-2-3-4-1-1-2-3-3-4h0v-1h1c0 1 0 1 1 1v-1z" class="H"></path><path d="M205 620c1 1 1 2 1 3-1 0-2 0-3 1v1h-1l-1-1v-1l1-1h2c1-1 1-1 1-2z" class="f"></path><path d="M206 623l3 3h-1l-1-1h-1c0 1 1 2 1 3-1-1-2-2-2-3h-2 0v-1c1-1 2-1 3-1z" class="C"></path><path d="M201 625c1 2 2 3 3 4l1 1c3 3 6 5 9 7h-9 0v-1l-2-2c-1-2-2-3-4-4 1-2 1-3 2-5z" class="L"></path><path d="M201 625c1 2 2 3 3 4l1 1c-1 1-1 0-2 1 1 1 1 1 2 1l-2 2c-1-2-2-3-4-4 1-2 1-3 2-5z" class="B"></path><path d="M278 596v-8h1v4l1 6v6c-1 2-2 4-2 6s0 3-1 4c-5 5-6 11-9 17-1 2-2 3-2 4v1c-3 1-7 1-10 1h-9c-1-1-4-1-6-1h0c8-1 15-4 21-9 9-7 15-20 16-31z" class="L"></path><path d="M278 610h1c0 3 1 4 2 6v3h0v7c1 4 1 6 4 9h0c-1 0-4 0-5 1l1 2h-4-4c-2 1-7 1-9 0 1-1 1-1 2-1v-1h0v-1c0-1 1-2 2-4 3-6 4-12 9-17 1-1 1-2 1-4z" class="O"></path><path d="M281 626c1 4 1 6 4 9h-1-2c-1-2 0-2-1-4v-1-1h-1c1-1 1-2 1-2v-1z" class="D"></path><path d="M277 638l2-8 1 6 1 2h-4z" class="C"></path><path d="M278 610h1c0 3 1 4 2 6v3h0l-1-2h-1c0 2 1 5 0 6h-1-1c0-2 1-3 1-5l-1-1-1 1h0c0-1 1-2 1-3h0v-1c1-1 1-2 1-4z" class="M"></path><path d="M276 618h0 0c-1 4-1 9-2 13v2c-1 0-3 1-3 2-2 0-4 2-5 2v-1c1 0 3-1 4-3 1-1 1-4 2-6 1-3 2-7 4-9z" class="C"></path><path d="M277 614v1h0c0 1-1 2-1 3-2 2-3 6-4 9-1 2-1 5-2 6-1 2-3 3-4 3h0v-1c0-1 1-2 2-4 3-6 4-12 9-17z" class="O"></path><path d="M233 361c1-1 1-2 2-4 2-4 5-8 9-10 3-3 8-4 12-3 7 1 14 7 18 13l4 7c2-3 4-6 6-8 3-5 10-11 16-12 4-1 9 1 13 3 7 5 10 14 12 22 1 9 0 18 0 26v81c0 3 1 6 1 9v23 16c-1 3-2 6-3 8s-3 3-5 3-3 0-5-1c-1-1-1-2-2-3v-1c1 0 1-1 1-2h0-2l-1-1v-1c1 0 0-1 0-2-1 0-1-1-1-1h1c2-2 3-4 3-6 0-3 0-5-2-8-3-2-6-2-10-2-3 0-7 1-9 4-5 5-7 12-8 19l-1 6c-1 1-1 2-2 2h0c-1-6 0-12 0-18l-1-13c1-2 1-5 1-8v-16 1c0 1 0 3-1 5l-1-1v-4c0 3-1 5-1 8h0v-5-3l-3-15c-1-5-2-10-4-15-4-9-13-17-20-23-4-3-8-6-11-10-4-6-4-11-3-18h0c-1-1-1-2-1-2v-6-10c0-5-1-11 1-16 1-2 3-4 4-6h0l-1-1c0 1-2 3-3 3h0l-1 1-1 1v1c-1 1-1 2-1 4-1 3-1 7-1 11-1-1 0-4 0-5 0-2-1-5 0-7v-5c1-2 2-3 1-4v-1z" class="L"></path><path d="M280 416h4 0c0 2-1 2-2 4h0c-1-1-1-1-2-1v-3zm-8 9c-1-1-1-1-1-2l1-2 1-1h0c-1-2 0-3 1-5h2v2l-3 3c0 1 1 1 1 2-1 0-2 1-2 1v2z" class="Y"></path><path d="M244 414c1 1 2 1 3 0 1 0 1-1 2-1h2c0 1 0 3-1 3-2 0-6 1-7-1h0l1-1h0z" class="H"></path><path d="M279 419h1c1 0 1 0 2 1s1 1 1 3c0 1-2 2-3 3-1 0-2 1-3 0l1-1s-1 0-1-1c0 0 1-1 2-1h1v-2c0-1-1-1-2-2h1z" class="e"></path><path d="M278 386h0l1 9v5c0 1-1 1-1 2h0l-1-1c-1-2 0-5 0-7 0-3 0-5 1-8z" class="a"></path><path d="M244 414c1-1 4-3 6-4 0-1 1-1 2-2s2 0 4 0c0 1 0 2-1 3-1-1-1-2-3-1 0 1 0 2-1 3h-2c-1 0-1 1-2 1-1 1-2 1-3 0z" class="V"></path><path d="M303 408c3 2 8 4 10 7-1 0-1 0-1 1-1 0-3 0-4 1-1 0-2 0-3-1s0-3-1-5c0-1-2-1-2-2l1-1z" class="D"></path><path d="M306 412h1c2 1 3 1 3 3h-3l-1-3z" class="P"></path><path d="M246 397c3 0 5 1 7 1v2c0 2 0 3-1 4h-6-1c1-2 0-5 1-7z" class="I"></path><path d="M304 397c1 0 4 0 5 1 1 0 2 1 2 3 0 1 0 2-1 3h-1-5c-1-2-1-4-1-5s0-1 1-2z" class="O"></path><path d="M306 399c1 0 1 0 2 1 1 0 0 1 0 2h0-3v-1c0-1 0-1 1-2z" class="L"></path><path d="M275 390h0c1 1 1 2 1 3 0 2 0 4-2 6-2 1-5 2-8 1h-1v-3c2-1 3 0 5 0 2-2 3-4 5-7z" class="a"></path><path d="M280 388c2 2 2 5 4 7 0 1 1 2 3 3 1-1 2-1 3-2h0 1v3l-1 1c-1 1-2 1-4 1s-4-1-5-2c-2-3-1-9-1-11z" class="T"></path><path d="M279 460c0 3-1 7 0 10l1 1c-1 4-2 9-2 13 0 3-1 5-1 8h0v-5-3l-3-15v-3l2 1v-1h1l1-1h0l1-5z" class="I"></path><path d="M274 469v-3l2 1c1 3 1 6 1 9s1 6 0 8l-3-15z" class="B"></path><path d="M276 415l4 1v3h-1-1c1 1 2 1 2 2v2h-1c-1 0-2 1-2 1 0 1 1 1 1 1l-1 1c-1 0-1 0-2-1-1 0-1-1-2-1v1c1 1 2 1 3 2 1 0 4 0 5-1 1 0 2-2 3-2h0 0c0 1-1 2-2 3-2 1-4 1-7 1-2-1-2-1-3-3v-2s1-1 2-1c0-1-1-1-1-2l3-3v-2z" class="g"></path><path d="M276 415l4 1v3h-1c-1-1-2-1-3-2v-2z" class="c"></path><path d="M277 424c-1 0-2-1-2-1v-3c1-1 2-1 3-1 1 1 2 1 2 2v2h-1c-1 0-2 1-2 1z" class="S"></path><path d="M309 357c5 2 10 7 13 13 1 2 1 5 1 8 1 5 1 9 1 14v7l-1-4v6h0v2c-1-1-1-1-1-2v-1-1 2c-1-2-1-4-1-6 0-8 2-19-1-27-3-4-7-7-13-9 1 0 1 0 1-1 1 0 1 0 1-1z" class="O"></path><path d="M286 432l1 1h0v2h0s1 0 1 1c0 0-1 2-1 3 1-2 1-2 3-4-4 7-5 13-7 20-2 5-3 10-3 16l-1-1c-1-3 0-7 0-10 0-4 2-10 3-14 1-5 2-10 4-14z" class="E"></path><path d="M292 417c1-2 1-5 1-8 0-1-1-2-1-3-1-2 0-4 1-5 0-1 0-1 1-1s3 0 4 1c2 1 2 3 2 5s1 2 3 2l-1 1-1 1c-1 3-1 7-2 10-2 6-7 10-9 15-2 2-2 2-3 4 0-1 1-3 1-3 0-1-1-1-1-1h0v-2h0l-1-1c1-5 5-10 6-15z" class="T"></path><path d="M292 417c0 6-2 12-5 18h0v-2h0l-1-1c1-5 5-10 6-15z" class="V"></path><path d="M235 401c2-5 5-10 10-13 5-2 11-2 16 0 3 1 5 3 8 5-1-7 0-16-6-22-3-2-6-3-9-2-3 0-5 1-6 3-1 1-1 3-1 4 1 2 2 3 2 5 1-2 2-4 3-5 1 0 1 0 2 1s0 2 0 3c-1 1-3 2-5 1-1 0-2 0-3-1s-1-4-2-6c1-3 1-6 5-8 2-1 6-1 9 0 5 1 8 4 10 8 3 4 3 9 3 14 0 2 0 4-1 6 0 1 0 1-1 2-3-1-4-3-7-5-4-3-12-2-16 0-5 2-8 7-10 12h0c-1-1-1-2-1-2z" class="a"></path><defs><linearGradient id="S" x1="274.027" y1="431.282" x2="257.553" y2="432.699" xlink:href="#B"><stop offset="0" stop-color="#d2d1d2"></stop><stop offset="1" stop-color="#fcfcfb"></stop></linearGradient></defs><path fill="url(#S)" d="M256 408c0-2 0-6 1-7s3-1 4-1 2 1 3 1c1 2 1 4 0 6 0 1-1 2-1 3-1 4 1 9 2 13 2 3 4 7 5 10 5 10 7 21 8 32l-1 1h-1c-1-10-5-21-10-31-2-4-6-8-8-13-2-4-3-8-3-11 1-1 1-2 1-3z"></path><path d="M233 361c1-1 1-2 2-4 2-4 5-8 9-10 3-3 8-4 12-3 7 1 14 7 18 13l4 7c2-3 4-6 6-8 3-5 10-11 16-12 4-1 9 1 13 3 7 5 10 14 12 22 1 9 0 18 0 26v81c0 3 1 6 1 9v23 16c-1 3-2 6-3 8s-3 3-5 3-3 0-5-1c-1-1-1-2-2-3v-1c1 0 1-1 1-2h0-2l-1-1v-1c1 0 0-1 0-2-1 0-1-1-1-1h1c2-2 3-4 3-6 0-3 0-5-2-8-3-2-6-2-10-2-3 0-7 1-9 4-5 5-7 12-8 19l-1 6c-1 1-1 2-2 2h0c-1-6 0-12 0-18l-1-13c1-2 1-5 1-8v-16c0-4 1-9 2-13 1-9 5-19 11-26 3-4 8-8 13-12s10-9 13-14c2-5 2-11 0-17-1-4-4-8-8-10-6-3-13-2-19 1-2 2-3 3-6 4v-1c-1-2-1-4-1-5 0-4 0-7 1-10 1-5 3-9 7-12 3-2 8-4 12-3 2 1 5 3 6 5s1 4 1 6v1c0 1-1 1-1 2-1 1-3 2-5 3-1 0-2-1-3-1-1-1-1-2-1-3s0-2 1-2c2 0 2 2 3 3h1c1 0 2-2 2-3 1-1 0-3-1-4-1-2-3-3-5-3-3-1-6 0-8 2-7 5-7 14-8 22 4-4 8-6 13-7 5 0 10 1 13 4 3 2 8 7 8 11v1l1-1v-2 1 1c0 1 0 1 1 2v-2h0v-6l1 4v-7c0-5 0-9-1-14 0-3 0-6-1-8-3-6-8-11-13-13 0 1 0 1-1 1 0 1 0 1-1 1-7-2-13 1-19 5-4 3-7 6-10 10h0c-4-8-12-13-19-15-5-1-10-1-14 1-2 1-3 2-5 3h0l-1-1c0 1-2 3-3 3h0l-1 1-1 1v1c-1 1-1 2-1 4-1 3-1 7-1 11-1-1 0-4 0-5 0-2-1-5 0-7v-5c1-2 2-3 1-4v-1z" class="L"></path><path d="M320 524s0-1 1-1c1 1 1 2 0 4 0 1-1 2-2 3h0l-1-1c0-1 0-1 1-1 0-2 1-2 1-4z" class="D"></path><path d="M318 496v-1c0-1-1-1-1-2h0l-2-2h0l1-1 3 4h1c2 2 3 5 4 7-2-1-4-5-6-5z" class="E"></path><path d="M302 348c1 0 2-1 2 0 1 0 1 1 1 1 0 1 0 0 1 1h0c-1 1-1 2-2 3h-1c-1 0-2-1-2-1-1-1-1-1-1-2h0c1-1 1-2 2-2z" class="B"></path><path d="M253 347h2c1 1 2 1 2 2s-1 2-2 3-2 1-3 1v-1c0-2-1-3 1-5z" class="T"></path><path d="M293 466c2-2 4-3 7-3 1 0 2-1 3-2v-1h1v3c-3 3-6 2-9 3l-2 2c-1-1-1-1 0-2z" class="O"></path><path d="M317 513v-2-2l2-2c2 2 4 5 4 7v1c-1 1-4-1-5-1l-1-1z" class="a"></path><path d="M313 498c2 0 3 0 4 1s1 2 1 3h-2l-1 1h0 0c-1 1-1 1-1 2l-2-3c-2-2-3-2-5-3h1v-1c2 1 3 1 5 0z" class="D"></path><path d="M312 528v-1c1-1 2-2 2-4 0-1 1-1 1-2 1-1 2-1 3-1s2 2 3 3c-1 0-1 1-1 1 0 2-1 2-1 4-1 0-1 0-1 1h0c-1-1-2-1-3-2v-1l-3 2h0z" class="I"></path><path d="M318 529v-1c0-2-1-4-1-6 1 1 2 1 3 2 0 2-1 2-1 4-1 0-1 0-1 1h0z" class="C"></path><path d="M288 461v1h0c-3 3-2 5-3 8-1 1 0 2 0 2h0v-1c0-1 0-2 1-2 0-2 1-3 2-4l1-1h0c-4 5-4 10-4 16v5 1 2h-1 0v2c-1-1 0-2-1-4v-4c1-5 0-10 1-14 1-3 2-6 4-7z" class="B"></path><path d="M318 428l2-1c1 1 2 1 3 3 1 0 1 2 0 3 0 2-3 3-4 4l-3 1c-1 0-1-1-2-1 0-2-1-4 0-6 0-1 3-3 4-3z" class="a"></path><path d="M318 429h2c1 1 2 2 2 3 0 2-2 3-3 4-1-1-3-1-4-2v-2c0-1 2-2 3-3z" class="B"></path><path d="M289 464c3-2 7-4 11-4 0 0 1 0 1 1v1c0 1 0 1-1 1-2-1-4-1-5 0s-2 2-2 3c-1 1-1 1 0 2-3 7-5 14-7 20 0-2 0-6-1-8 0-6 0-11 4-16z" class="G"></path><path d="M239 362c3-2 5-4 8-5 5-2 12-1 17 1 5 3 12 8 14 14v1c4-7 8-12 15-15 5-2 11-3 16-1 0 1 0 1-1 1 0 1 0 1-1 1-7-2-13 1-19 5-4 3-7 6-10 10h0c-4-8-12-13-19-15-5-1-10-1-14 1-2 1-3 2-5 3h0l-1-1z" class="a"></path><path d="M284 496c2-6 6-13 11-18l9-6c-2 0-4 1-6 0v-2h0c1-1 3-1 4-2 2-1 5-4 6-3 1 0 1 1 1 1 1 2 1 5 1 6 0 2 1 3 1 4-7 1-15 5-20 11-3 5-6 12-7 18l-1-1v-2c0-2 0-4 1-6zm30 9c0-1 0-1 1-2h0 0l4 4-2 2v2 2l-3-1v7c0 3-1 4-2 6-1 1-2 2-2 3l-1-1v-1c1 0 0-1 0-2-1 0-1-1-1-1h1c2-2 3-4 3-6 0-3 0-5-2-8-3-2-6-2-10-2-3 0-7 1-9 4-5 5-7 12-8 19l-1-2v-2-2c0-1 0-3 1-4 1-4 2-7 4-10 4-4 7-6 12-7 6 0 10 2 15 6l1-1c0-1 0-1-1-3z" class="O"></path><path d="M312 525c-1-1-1-1-2-1 0-1 2-3 3-4 0 0 1 0 1-1 0 3-1 4-2 6z" class="M"></path><path d="M307 499c-6 0-10 1-15 4 3-4 6-8 10-11 2-2 5-3 7-5 2 0 3-2 5-2h0l1-2 4 1c2-1 3-2 4-4l1-2h0v2c0 2-1 3-2 4-2 2-6 1-8 1 0 1 1 2 1 2 2 3 4 5 5 7h-1l-3-4-1 1h0l2 2h0c0 1 1 1 1 2v1l-1 1h-2v1h-2c-2 1-3 1-5 0v1h-1z" class="G"></path><path d="M308 498v-1h7v1h-2c-2 1-3 1-5 0zm1-7h2s-1 0-1 1c-3 1-5 3-6 5-2 1-3 1-4 1h-2l1-1c3-3 6-5 10-6z" class="B"></path><path d="M282 470c1-9 5-19 11-26 3-4 8-8 13-12s10-9 13-14c2-5 2-11 0-17-1-4-4-8-8-10-6-3-13-2-19 1-2 2-3 3-6 4v-1c-1-2-1-4-1-5 0-4 0-7 1-10 1-5 3-9 7-12 3-2 8-4 12-3 2 1 5 3 6 5s1 4 1 6v1c0 1-1 1-1 2-1 1-3 2-5 3-1 0-2-1-3-1-1-1-1-2-1-3s0-2 1-2c2 0 2 2 3 3h1c1 0 2-2 2-3 1-1 0-3-1-4-1-2-3-3-5-3-3-1-6 0-8 2-7 5-7 14-8 22 4-4 8-6 13-7 5 0 10 1 13 4 3 2 8 7 8 11v1l1-1v-2 1 1c0 1 0 1 1 2v-2h0v-6l1 4c-1 7 0 14-4 20-1 3-3 5-5 7-1 1-2 2-3 4-3 3-7 6-10 9-2 1-5 3-6 4 4-2 11-4 16-2 3 1 7 4 9 7v2 1c0-4-3-8-5-11l-1-1v-1h1l1 2 4 4c2 4 3 9 2 13 0 2-3 6-5 7s-4 1-7 2c1-3 1-6 0-9 0-2 0-3-1-4h-1c-1 0-3 1-4 1v-1c1-1 2-1 3-1s2 0 3 1 1 3 2 5c0 1 0 3 1 4 1 0 1-1 2-2 1-2 2-5 1-7 0-2-2-4-4-6-4-2-9-2-13-1-5 1-8 3-10 7l-3 6c3-1 8-6 12-5 1 0 3 2 4 3v1h0c-1-1-3-3-5-3-4 0-7 3-10 6h0c-2 1-3 4-4 7-1 4 0 9-1 14v4 7 3h1c-1 2-1 4-1 6v2l1 1c-1 4-2 9-3 14 0 1 0 3 1 5v2 2l1 2-1 6c-1 1-1 2-2 2h0c-1-6 0-12 0-18l-1-13c1-2 1-5 1-8v-16c0-4 1-9 2-13z" class="a"></path><path d="M282 500l1-7v3h1c-1 2-1 4-1 6l-1 1v-3z" class="P"></path><path d="M282 470c0 2-1 5-1 7v2c-1 3 0 7 0 11v14-1-1c0-1 0-1 1-2h0v3l1-1v2l1 1c-1 4-2 9-3 14 0 1 0 3 1 5v2 2l1 2-1 6c-1 1-1 2-2 2h0c-1-6 0-12 0-18l-1-13c1-2 1-5 1-8v-16c0-4 1-9 2-13z" class="G"></path><path d="M276 100h209l1 30 1 9v5 3h-1v6l-1 1c-1-2-1-4-2-5h-1l3 14v3h0c0-2 0-3-2-4v2c1 1 1 3 1 4h-1 0c0 7 0 14-6 19l-1 1c-4 2-9 3-13 2s-8-3-11-6c-4-2-6-5-9-8-4-4-8-8-12-11-4-4-8-6-13-9-3-3-7-5-10-8-2-2-4-5-5-8l-12-18 9-3c1-1 3-2 4-2h2l1 1c1-3 11-9 11-13-5 1-7 4-11 7-3 2-6 3-9 4l-5 3-3 1-3-3c0-1-1-3-1-4-1-2-4-3-6-3-2-1-5 1-7 2-3 2-4 5-5 8 0 3 1 7 3 9 3 4 7 6 11 7l-1 1c-1-1-3-1-5-1h0-2c1 1 2 1 3 1h-1l-1 1c-1-1-4-2-5-2h-2c-7-3-14-3-20-6 0-1-1-1-1-2-2 0-5-1-7-1h-3v1c-3 0-6 0-8-1l-1-1-2 1v15 24 68 53 4c-1 0-2 0-2 1-1 2-1 4-1 6 0 3 1 9 0 11-2 1-3 2-5 1s-3-1-3-3h-1l-4-4h1c1 0 1-1 2-1l-1-1c1-2 3-4 4-6h0c0-1 1-2 2-4 1-3 2-6 1-8s0-2 0-4c-1-8-5-16-10-23l-7-11c-4-7-10-14-13-22-2-5-4-10-5-15h-1-1 0l-1-4c-1-3-1-7-1-10v-15-8c1-2 0-6 1-9 0-5 1-11 2-16 0-2 0-3 1-5l-1-1h0l-1 1h0c1-3 1-7 1-10-1-2-1-4-1-6h1 1c1 1 1 2 2 4l1-6c1-2 1-4 2-6h1l-1-1c-1-3-2-7-3-9v-2l-1-1c1-1 13-1 16-1h-16c-1 0-2 0-2-1h20-6c-6 0-12 0-18-1 1 0 1-1 1-1h-4z" class="L"></path><path d="M323 124l3 2c-2 0-2 0-4-1l1-1z" class="F"></path><path d="M313 211l-3 3v-2c1-1 1-1 3-1z" class="B"></path><path d="M309 171h2c1-1 1-1 3-1-2 2-2 2-4 3v-1l-1-1zm4-5c1 1 2 1 2 2l-1 1c-1-1-2 0-2-2l1-1z" class="D"></path><path d="M361 120h2l1 1c-1 1-1 2-1 3l-1-1h-1v-3z" class="P"></path><path d="M313 120c1 0 2-1 3-1l-1 4h0v-1c-1 1-2 1-2 1h-1c1-1 2-1 2-2l-1-1z" class="B"></path><path d="M340 120h1v2c-2 0-3 1-4 1v-1l1-1c1 0 1-1 2-1z" class="T"></path><path d="M316 119h2v3c0 1-1 2-1 2l-1-1h-1l1-4z" class="H"></path><path d="M307 151h5v3h-1 0c-2-2-3-2-5-3h1z" class="O"></path><path d="M312 167c0 2 1 1 2 2v1c-2 0-2 0-3 1h-2c1-1 1-2 1-2 0-1 1-1 2-2z" class="N"></path><path d="M355 111c2 0 7-1 9 0-2 1-6 2-9 2 1-1 1-1 2-1l-2-1z" class="G"></path><path d="M332 121c0-1-1-1-2-3v-1h1c1 0 2 0 3 1 0 1 0 2 1 3h-1-1-1z" class="H"></path><path d="M315 234l-1-1 1-1c2 0 4 0 6 1l2 2h-1 0-2c-1-1-3-1-5-1z" class="G"></path><path d="M442 131v-1c1 1 1 0 2 0 0 2 2 4 1 6h-2c0-1-1-1-2-2l2-2-1-1z" class="R"></path><path d="M316 152v-3c0-1 0-3-1-3 0-1-3-3-4-4v-1h3c1 1 3 3 3 4l-1 1c0 2 1 4 0 6z" class="E"></path><path d="M320 222c0 1 0 1 1 2-1 2-3 4-5 5h-1l-1-1c2-2 4-3 6-6z" class="J"></path><path d="M314 163s1 0 1-1c3 2 6 5 7 8l-1 1c-1-3-3-5-6-6l-1-2zm134-35v-1c0-1 1-2 2-2s3 0 4 1 1 1 1 3h-2 0c0-1 0-1 1-2l-2-1c-1 0-1 0-2 1l-1 2-1-1z" class="R"></path><path d="M293 218c-3-2-3-4-4-8 0 1 0 1 1 1 1 3 2 3 4 4v1c1 1 2 1 2 1l1 1h-1-2-1z" class="I"></path><path d="M448 128l1 1 1-2v1c1 1 3 2 4 3 1 0 2 0 3 1v1h-2c-3-1-6-2-7-4v-1z" class="D"></path><path d="M307 167v1l1 1v4h1l1-1v1 1c-1 0-1 1-2 1-2 0-2-2-3-3h-2l2-1h0 0c1-2 2-2 2-4z" class="N"></path><path d="M295 215l-2-2c-3-1-3-7-4-9 1 1 1 2 2 3 1 2 3 4 4 5v3z" class="Z"></path><path d="M312 123c-1 0-3 0-4-1s-1-1-1-2l2-2c2 0 3 1 4 2l1 1c0 1-1 1-2 2zm49 0l-1-1c-1-2-3-3-4-4l-3 3c-1-1-1 0-1-1v-1c2-1 3-2 4-2 2 0 3 1 4 2l1 1v3z" class="O"></path><path d="M314 163l1 2s-1 1-2 1l-1 1c-1 1-2 1-2 2 0 0 0 1-1 2l1 1-1 1h-1v-4c1-1 1-4 3-5 1 0 2 0 3-1h0z" class="B"></path><path d="M290 170l1 5 1-1c0 2 0 5 1 6-1 1-1 3 0 4 0 1-1 4-2 5 0-5-2-13-1-19z" class="I"></path><path d="M300 197h1c0 3 0 6 1 10 0 2 1 5 1 7l-1-1c-1-4-2-8-2-12 0-1-1-3 0-4z" class="H"></path><path d="M314 198v-1c-1-1-2-2-2-3 0-2 1-2 2-3h2c0-1 0-1 1-2l1 1c0 1 0 1-1 1 0 1 0 1-1 1 0 2 0 5-1 7l-1-1z" class="c"></path><path d="M476 121h1c2 2 2 7 2 9l-3-3c-1-1-2-3-3-5h0c1 0 2 0 3 1h0v-2z" class="a"></path><path d="M323 124l-2-3c-1-1-1-1-1-2h0c1 0 1 0 1 1 1 2 3 4 6 5 3 2 9 1 13 2h-3v1c-3 0-6 0-8-1l-1-1-2 1v-1l-3-2z" class="M"></path><path d="M304 190c1-2 2-3 4-4l2-1c0 2 0 5-1 6s-2 0-3 0c-1 1-2 1-3 1v-1l1-1z" class="T"></path><path d="M304 190l2-1v1h2l-1 1h-1c-1 1-2 1-3 1v-1l1-1z" class="a"></path><path d="M315 234c2 0 4 0 5 1h2 0c0 2 1 4 0 6v1h-1 0-1l-1-1c-1 0-1-2-1-3s-1-1-1-2-1-1-2-2z" class="F"></path><path d="M318 238v-1c1 0 1 0 2 1s1 2 1 3l-1 1-1-1c-1 0-1-2-1-3z" class="O"></path><path d="M313 132c1 0 2 0 3-1 1 0 1-1 3-1v1l-1 1-1 1 1 1c1-1 1 0 2-1v-3c-1-1-2-1-2-2 1-1 1 0 2 0 1 1 2 2 2 4 0 1-1 3-2 4-2 0-4 0-6-1h-1 0c-2 0-2-1-4-2 2 0 3 0 4-1z" class="I"></path><path d="M309 133c2 0 3 0 4-1h2l1 1c-1 0-1 0-1 1h-1-1v1c-2 0-2-1-4-2z" class="D"></path><path d="M442 131h-1c-3 0-4-1-6-3 4-3 7-5 11-6-1 3-2 5-2 8h0c-1 0-1 1-2 0v1zm-136 20h-4c-4 1-8 3-10 6-2 2-2 5-3 7s-3 5-4 8c1-7 2-15 8-19 4-2 9-3 14-2h-1z" class="G"></path><path d="M315 123h1l1 1v2l-1 1-3 3-1-1h0-1-4c-6 1-11 2-16 6v-1c1-1 3-2 4-3 4-1 7-3 10-4 2 0 7-1 9-2 0-1 0-1 1-2h0z" class="J"></path><path d="M290 170l1-1c0-4 6-9 9-11 2-1 4-1 6 0v3h-1l-1-1c-1 0-2 3-3 5-2 1-5 3-7 4-1 2-2 3-2 5l-1 1-1-5z" class="D"></path><defs><linearGradient id="T" x1="473.326" y1="139.248" x2="492.761" y2="121.44" xlink:href="#B"><stop offset="0" stop-color="#cbc8c9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#T)" d="M481 117v-1c1-1 0-3 1-4v-2l-1-1h1c0 3 0 5 1 7l3 31v6l-1 1c-1-2-1-4-2-5-1-4 0-10-1-14l-1-18z"></path><path d="M310 198h0 4l1 1c3 3 6 6 7 10v7 3c-1-1-1-1-2-1v-2c0-1 0-2-1-3 0 0 0-1-1-1 0-2-1-3-1-4h-2l-1-1h-1c0-1 0-1-1-1-1-2-2-2-3-2s-2-1-2-1v-1c0-1 2-3 3-4z" class="O"></path><path d="M314 207v-4c1 0 1 0 1 1l2 4h-2l-1-1z" class="U"></path><path d="M309 204c-1 0-2-1-2-1v-1c0-1 2-3 3-4 2 1 2 1 3 2v1h0c-2 1-3 2-4 3z" class="a"></path><path d="M301 165c1-2 2-5 3-5l1 1c0 2 1 4 2 6 0 2-1 2-2 4h0 0l-2 1-3 2c-5 3-5 5-7 10-1-1-1-3 0-4-1-1-1-4-1-6s1-3 2-5c2-1 5-3 7-4z" class="D"></path><path d="M296 172l3-3v3c-1 1-1 2-2 2l-2-1 1-1z" class="I"></path><path d="M299 169l2-1 4 3-2 1-3 2-1-2v-3z" class="R"></path><defs><linearGradient id="U" x1="292.653" y1="171.633" x2="294.635" y2="174.517" xlink:href="#B"><stop offset="0" stop-color="#242225"></stop><stop offset="1" stop-color="#272d29"></stop></linearGradient></defs><path fill="url(#U)" d="M294 169l2 3-1 1c-1 2-2 4-2 7-1-1-1-4-1-6s1-3 2-5z"></path><path d="M301 165v1c0 1 0 1-1 1l1 1-2 1-3 3-2-3c2-1 5-3 7-4z" class="C"></path><path d="M301 165c1-2 2-5 3-5l1 1c0 2 1 4 2 6 0 2-1 2-2 4h0 0l-4-3-1-1c1 0 1 0 1-1v-1z" class="S"></path><path d="M290 125l3-4c0 1 0 2 1 3h1l-4 7-1 3c-3 4-5 8-7 12 0-2 0-3 1-5l-1-1h0l-1 1h0c1-3 1-7 1-10-1-2-1-4-1-6h1 1c1 1 1 2 2 4l1-6v1h1 0 2v1z" class="I"></path><path d="M288 124h2v1c-1 1-2 4-3 5h-1c1-2 2-4 2-6h0z" class="B"></path><path d="M294 124h1l-4 7-1-1v-1c1-1 1-2 2-3 0-1 1-1 2-2z" class="D"></path><path d="M284 141l1-7h0 0c1-1 1-2 2-2 0-1 0 0 1-1h0 0v2l2 1c-3 4-5 8-7 12 0-2 0-3 1-5z" class="N"></path><path d="M481 113c-2 0-3 3-4 5-2 1-3 2-6 1-1 0-2-1-2-2-1-1-2-3-2-4 0-2 2-4 3-5s3-1 4-1c2 1 2 1 3 3l1 2 1-6s1 0 2-1c1 1 1 2 2 3v8c-1-2-1-4-1-7h-1l1 1v2c-1 1 0 3-1 4v1-4z" class="R"></path><path d="M473 110c1 0 2 1 3 2v1 2l-3-3v-2h0z" class="C"></path><path d="M481 113c-1-1-1-1-2-1v-4c1 1 1 2 2 3 1-1 0-3 1-4v1h1v8c-1-2-1-4-1-7h-1l1 1v2c-1 1 0 3-1 4v1-4z" class="T"></path><path d="M476 115l-3 2h0c-2-1-3-2-4-4 0-1 0-2 1-3h3 0v2l3 3z" class="F"></path><path d="M464 125l1 1 2 2-2 1h0c2 5 1 9-1 14-2 2-4 5-7 5-1 1-3 1-4 0s-3-2-3-4v-6h1c2-1 5-1 6 1 1 0 0 1 1 1 1-1 2-2 2-3 0-2 0-4-2-5 0-1 0-1-1-2l1-1 1 1s1 1 1 2c2 3 1 7 0 10-1 0-2 1-2 2v1h1c2-1 4-3 4-6 1-4 1-8-1-12 1 0 1-1 2-2z" class="a"></path><path d="M347 128c-2-2-3-5-4-8 0-2 1-4 3-5 2-3 5-4 9-4l2 1c-1 0-1 0-2 1-3 2-8 4-9 8 0 2 1 4 2 5 2 2 4 3 6 3 2 1 4 1 6-1 1-1 2-2 3-4 0-1 0-2 1-3h0c0 2 1 6-1 8-1 1-2 2-4 2h0c6 1 12 3 17 5h0 0-2c1 1 2 1 3 1h-1l-1 1c-1-1-4-2-5-2h-2c-7-3-14-3-20-6 0-1-1-1-1-2zm-44-26c4 0 7 0 11 1 0 1-1 1-1 1v1-1c1 0 2-1 4 0h0c-1 1-4 2-5 4 2-1 4-1 7-1h1 0c-1 2-2 2-4 3l-9 3c-4 2-9 7-12 11h-1c-1-1-1-2-1-3 4-7 10-14 17-17l-9-1h-16c-1 0-2 0-2-1h20z" class="O"></path><path d="M301 103l9 1c-7 3-13 10-17 17l-3 4v-1h-2 0-1v-1c1-2 1-4 2-6h1l-1-1c-1-3-2-7-3-9v-2l-1-1c1-1 13-1 16-1z" class="S"></path><path d="M286 107v-2c4 2 4 4 5 7l3-3c1-1 1-2 2-2h2v3c0 2-5 4-6 6-2 2-3 6-4 8h0-1v-1c1-2 1-4 2-6h1l-1-1-3-9zm35 64l1-1c2 3 2 6 1 10-2 4-7 7-11 9h0c0-2 2-5 3-6-5 0-9 0-13 3-3 3-5 7-5 12 0 3 0 6 1 9 1 2 1 4 2 6-4-6-7-14-6-21 2-5 3-10 8-12 3-2 7-4 11-3 2 1 4 2 6 4l3-3c1-2 0-5-1-7zm107-55c5-5 10-7 17-7 3 0 6 0 9 1 2 1 3 2 5 2-1-1-3-2-5-3l-2-1c-1 0-1-1-1-1 4 0 6 3 8 5 1 1 3 2 4 3l6 6c5 6 8 13 11 20 1 3 2 5 2 8l3 14v3h0c0-2 0-3-2-4v2c1 1 1 3 1 4h-1 0c0-13-5-24-11-35-2-3-3-6-6-8-1-2-4-4-6-6-1-1-6-4-8-4-6-2-15-2-21 1l-1 1c-1 0-1 0-2-1z" class="T"></path><path d="M314 207l1 1h2c0 1 1 2 1 4 1 0 1 1 1 1 1 1 1 2 1 3v2c1 0 1 0 2 1h-1-1c1 1 1 0 2 1 0 2 0 3-1 4-1-1-1-1-1-2-2 3-4 4-6 6l1 1h1l-5 4v3h-1v-2c-1-1 0-2 1-3v-1h0c-1 0-1-1-1-1-5 0-9-3-12-7h0l-2-1v-1c-1 0-2-1-3-2h1 2 1l-1-1s-1 0-2-1v-1l3 1v-1h-2v-3c2 3 4 3 7 5 3-1 5-1 8-3l3-3v-4h1z" class="D"></path><path d="M316 222c-1 0-1 0-2-1v-1h3c1-1 2-1 3-1 1 1 1 0 2 1 0 2 0 3-1 4-1-1-1-1-1-2h-1-3z" class="T"></path><path d="M316 222h3 1c-2 3-4 4-6 6l-1 1v-1c1 0 1-1 2-2l-1-1c0-2 1-2 2-3z" class="S"></path><path d="M296 221c2 0 4 1 6 1 4 1 8 2 12-1-2 2-5 3-8 3 2 1 3 1 4 2v1c-2 0-3-1-4-1h-1c-2-1-4-3-7-4h0l-2-1z" class="K"></path><path d="M317 208c0 1 1 2 1 4 1 0 1 1 1 1v3s-1 0-1 1h-1v-1h0l-1-1c-1 2-1 3-3 4-2 2-5 3-8 3-3-1-5-1-7-2h6c2-1 8-3 9-5s1-3 2-4v-3h2z" class="C"></path><path d="M317 208c0 1 1 2 1 4 1 0 1 1 1 1v3s-1 0-1 1h-1v-1h0l-1-1 1-1-1-1-1 1v2l-1-1c1-1 1-3 1-4h0v-3h2z" class="N"></path><path d="M337 123h0l-5 1c-2 0-4-1-5-2-2-2-3-6-3-8 0-3 1-6 3-8 3-2 5-3 8-4 8-1 16 0 24 0h53l2 1h0-1c-1 1-4 0-6 1h-14c-10 0-17 0-24 7-3 2-4 5-5 8v-1c0-2 2-6 3-9h1-1-13c-3 0-5 1-8 2-2 1-4 3-6 4h-2c-1-1-1-1-1-3 0-1 1-2 2-3 5-1 9-1 14-2l14-1c2 0 5 0 6-2h0c-2-1-29-1-33 0-2 0-3 0-5 1-3 0-6 2-8 5-1 2-1 5 0 8 0 1 2 2 3 3h2 1 1 1c-1-1-1-2-1-3 2 1 4 1 6 2-1 0-1 1-2 1l-1 1v1z" class="D"></path><path d="M412 102c3 0 6 0 8 1-1 1-1 1-2 1h0v1c-5 1-7 4-11 7-3 2-6 3-9 4l-5 3-3 1-3-3c0-1-1-3-1-4-1-2-4-3-6-3-2-1-5 1-7 2-3 2-4 5-5 8 0 3 1 7 3 9 3 4 7 6 11 7l-1 1c-1-1-3-1-5-1h0c-5-2-11-4-17-5h0c2 0 3-1 4-2 2-2 1-6 1-8v-2c1-3 2-6 5-8 7-7 14-7 24-7h14c2-1 5 0 6-1h1 0l-2-1z" class="S"></path><path d="M393 119c0-1 1-2 1-3s1-1 1-1c1 1 2 1 3 1l-5 3z" class="K"></path><path d="M412 102c3 0 6 0 8 1-1 1-1 1-2 1h0-3-8c2-1 5 0 6-1h1 0l-2-1z" class="J"></path><path d="M396 107h0 3c2 0 5 1 7 2-1 1-4 4-6 3h-2c-1-1-1-3-2-5z" class="D"></path><path d="M396 107c-1 2-1 4-3 5-1 2-1 3-3 3-1-1 0-3 0-4-1-1-2-1-3-2-2 0-3 0-4-1h-1c0-1 1-1 1-1 2-2 7-1 9-1 1 0 4 0 4 1h0z" class="O"></path><path d="M462 127l-2-2c-4-4-11-3-15-3-3 0-8 1-10 3 0 1-1 1-1 1h-1c0-1 1-1 2-3-4 1-7-1-10-3-1 3 0 5 0 8-1-2-2-4-2-6-1-4-1-9 2-13a19.81 19.81 0 0 0-11 11c-2 3-2 7-1 11 0 1 1 2 2 3 3 3 7 6 12 6 3 1 4 0 7-2h0c0 1 0 2-1 3-3 1-6 2-9 1-4-1-9-2-12-5v-1s-1-1-1-2c-2-5-2-9 0-14 3-7 9-13 16-16 3-1 6-2 10-2 7-1 14 0 21 0s16-1 22 2h-25c-7 0-14-1-20 1-4 1-7 3-9 7-2 2-2 5-1 7h1 1l-1-1 2-2c1 1 1 1 2 1l1-1c6-3 15-3 21-1 2 0 7 3 8 4 2 2 5 4 6 6 0 0-1 0-1 1l-1-1c-1 1-1 2-2 2z" class="G"></path><path d="M457 120h1c1 0 1 0 2 1v-2c2 2 5 4 6 6 0 0-1 0-1 1l-1-1c-1-1-5-4-7-5z" class="C"></path><path d="M427 119l-1-1 2-2c1 1 1 1 2 1h2v1h0v1l12-1v1c-5 0-11 2-16 1h-2v-1h1z" class="B"></path><path d="M427 119l-1-1 2-2c1 1 1 1 2 1h2v1h0v1h-5z" class="G"></path><defs><linearGradient id="V" x1="435.444" y1="115.702" x2="459.947" y2="118.117" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333232"></stop></linearGradient></defs><path fill="url(#V)" d="M431 116c6-3 15-3 21-1 2 0 7 3 8 4v2c-1-1-1-1-2-1h-1l-2-1c-4-1-7 0-11 0v-1l-12 1v-1h0v-1h-2l1-1z"></path><path d="M430 117l1-1c1 1 3 0 4 0 4 0 11 0 14 1-1 0-3 0-5 1l-12 1v-1h0v-1h-2z" class="M"></path><path d="M282 185v-16c1-6 0-10 2-15 1-4 3-8 6-12 5-6 12-8 19-9 2 1 2 2 4 2h0c4 2 7 5 8 9s1 11-1 14c-1 2-3 3-5 4 0 1-1 1-1 1h0c-1 1-2 1-3 1-2 1-2 4-3 5l-1-1v-1c-1-2-2-4-2-6h1c1 0 3 0 5-1s4-5 5-8c1-2 0-4 0-6l1-1c2 2 2 5 2 7v-1c1-2 0-5-1-7s-4-5-7-6c-4-2-7-1-12 1 4 1 7 2 10 5l3 3 1 1h-1l-3-3c-3-3-7-3-11-3-2 1-6 3-7 5-3 4-5 9-6 14-1 3-1 7 0 11 0 2-1 4-1 7 0 8 0 16 2 24 2 12 6 23 13 33 2 2 4 4 7 6 2 1 8 4 10 4 2-1 3-2 5-4h0 1v-1c1-2 0-4 0-6h1c1 2 1 4 1 7l-5 5c-1 1-2 0-3 0-4 0-7-1-11-2l11 14c4 6 7 11 9 18v8l1 2v4c-1 0-2 0-2 1-1 2-1 4-1 6 0 3 1 9 0 11-2 1-3 2-5 1s-3-1-3-3h-1l-4-4h1c1 0 1-1 2-1l-1-1c1-2 3-4 4-6h0c0-1 1-2 2-4 1-3 2-6 1-8s0-2 0-4c-1-8-5-16-10-23l-7-11c-4-7-10-14-13-22-2-5-4-10-5-15h-1-1 0l-1-4c-1-3-1-7-1-10v-15-1c1-1 1-2 1-4 1 3-1 7 1 11z" class="O"></path><path d="M325 285l1 2v4c-1 0-2 0-2 1l1-7z" class="Q"></path><path d="M319 279c1 3 1 5 1 7s-3 12-1 13c1-1 2-1 2-2 0 1 1 3 0 4-1 2-5 3-6 4h-1c1-1 1-2 2-3v-1h-1c0-1 0-1 1-2v-4c0-1 1-2 2-4 1-3 2-6 1-8s0-2 0-4z" class="B"></path><defs><linearGradient id="W" x1="282.605" y1="193.721" x2="279.893" y2="194.151" xlink:href="#B"><stop offset="0" stop-color="#0e0e0d"></stop><stop offset="1" stop-color="#2e2d2f"></stop></linearGradient></defs><path fill="url(#W)" d="M280 179v-1c1-1 1-2 1-4 1 3-1 7 1 11l2 23h-1-1 0l-1-4c-1-3-1-7-1-10v-15z"></path><path d="M477 187l-1 1c-4 2-9 3-13 2s-8-3-11-6c-4-2-6-5-9-8-4-4-8-8-12-11-4-4-8-6-13-9-3-3-7-5-10-8-2-2-4-5-5-8l-12-18 9-3c1-1 3-2 4-2h2l1 1c-1 3-2 8-1 11v-1-1c1 1 1 2 1 3 1 3 3 7 6 9 1 0 2 1 4 2 2 2 4 4 7 5 10 4 18-4 26-8v6c0 2 2 3 3 4s3 1 4 0c3 0 5-3 7-5 2-5 3-9 1-14h0l2-1-2-2c0-1 1-1 1-1 3 2 4 5 6 8 6 11 11 22 11 35 0 7 0 14-6 19z" class="L"></path><path d="M457 153s0 1 1 1l-1 3h-2v-1c0-1 1-2 2-3z" class="e"></path><path d="M466 159v4h-1v-1c-1 1-2 1-3 2v-2c1-2 3-3 4-3z" class="N"></path><path d="M474 174h0c0-1 0-1 1-2h0l1 2c-1 3-1 5-3 8l1-8z" class="D"></path><path d="M465 163c0 1-1 2-2 4-1 0-1 1-2 1-2-1-2-2-3-3 0-2 0-2 1-3l1-1c1 1 0 2 0 3h2 0c1-1 2-1 3-2v1z" class="H"></path><path d="M464 172l-3 1c-3-1-8-4-10-7v-2s0-1 1-1c0 1 1 2 1 2 1 2 3 4 6 5h0c2 0 3 0 4-1h0l2 2c-1 0-1 0-1 1h0z" class="J"></path><path d="M406 117l1 1c-1 3-2 8-1 11v-1-1c1 1 1 2 1 3h0c-1 2 0 5 0 6v1h0l-1 1v-1c0-1-1-2-2-2h0l-2-2c0-2-2-6-2-7h1c1 3 2 5 3 8 0-5 0-11 2-17z" class="H"></path><path d="M407 130c1 3 3 7 6 9l-1 1c-1 1-2 3-3 4-2-1-4-7-5-9h0c1 0 2 1 2 2v1l1-1h0v-1c0-1-1-4 0-6h0z" class="a"></path><path d="M453 174c1 0 2 1 3 1 4 2 9 0 13-2 1-1 3-2 4-3l-1-1c-2 3-6 5-10 5h0c1-1 3-1 4-2 3-1 5-4 7-4 1 0 1 0 2 1s1 3 1 5l-1-2h0c-1 1-1 1-1 2h0c-2 0-6 2-8 2-6 2-13 1-18-2h0 3c0 1 0 1 1 1s2 0 3 1l-1-1-1-1z" class="I"></path><path d="M467 128c5 6 8 18 7 25 0 4-2 8-3 12-1 1-1 4-2 4-1 2-3 3-5 3h0c0-1 0-1 1-1l-2-2c2-2 3-3 4-6h-1v-4c1-1 2-1 3-2 2-2 2-4 2-6-1-3-1-6-2-8 0-4 0-7-2-10 0-2-1-3-2-4h0l2-1z" class="G"></path><path d="M469 157l-2 6h-1v-4c1-1 2-1 3-2z" class="C"></path><path d="M413 139c1 0 2 1 4 2 2 2 4 4 7 5 10 4 18-4 26-8v6h-1c-2 0-4 1-6 4s-2 8-1 12c1 2 2 5 3 6 2 2 4 4 5 6 1 0 3 2 3 2l1 1 1 1c-1-1-2-1-3-1s-1 0-1-1h-3 0c-1 0-5-2-6-3-5-3-7-5-9-11-1-3 1-6 2-8-1 0-1 0-2 1h0c-1 1-2 3-3 4v-1c-1 0-1 1-2 1h0c-1 0-1 0-2-1h-1c-1 0-2-1-4-2-4-2-9-5-11-10 1 0 1-1 2-1h0 1v-1l-1-2 1-1z" class="G"></path><path d="M437 158h0c1 1 1 3 1 4l-1 1c-1-1-1-2-2-3 1-1 1-2 2-2zm-4-5c-3-1-6-1-9-2s-5-2-8-4c0-1-2-1-2-2h0l6 3c4 3 10 4 16 2h0 0l-1 2c-1 0-1 0-2 1h0zM123 99l92 1h58 3 4s0 1-1 1c6 1 12 1 18 1h6-20c0 1 1 1 2 1h16c-3 0-15 0-16 1l1 1v2c1 2 2 6 3 9l1 1h-1c-1 2-1 4-2 6l-1 6c-1-2-1-3-2-4h-1-1c0 2 0 4 1 6 0 3 0 7-1 10h0l1-1h0l1 1c-1 2-1 3-1 5-1 5-2 11-2 16-1 3 0 7-1 9v8 15c0 3 0 7 1 10l1 4h0 1 1c1 5 3 10 5 15 3 8 9 15 13 22l7 11c5 7 9 15 10 23 0 2-1 2 0 4s0 5-1 8c-1 2-2 3-2 4h0c-1 2-3 4-4 6l1 1c-1 0-1 1-2 1l-1-1c-2 1-4 1-5 1-6 0-11 0-16-3-2-1-3-2-4-4l-2-4c2 1 3 1 5 1v-1c-1 0-3-1-4-1-2 0-3-1-5 0v5c-2 1-1 1-2 3l-1 3 2 2c-1 3-1 5-3 8h0v-2h-1c-4 5-10 7-16 8v-1c0-1 0-2-1-3l-1-1h-1c-2-2-4-3-6-3s-4 0-6 2c-3 3-4 8-8 12-1 0-1 1-2 1l-1-1h-1 0l-1-195v-3c1-1 3-3 5-4 1-1 1-1 2-3l-1-1c-1 0-2 2-2 3-2 1-3 2-5 3-1 1-3 2-5 2-5 0-10 0-15 3-1 0-2 2-4 2-10 2-19 4-29 8l-15 6v-1c-2 1 1 0-1 1h0c-1 0-2 0-2 1h-1-1l-2 1h-1l8-14c4-7 7-14 14-17 2 0 4 2 5 3 0 1 1 1 1 1h1c1 1 1 2 1 3-1 1-1 2-2 3 0 1-1 2-2 2v1l-5 3c-3 2-6 3-10 5-1 1-2 1-3 3h1l8-3c2-1 3-1 4 0 1-1 2-2 3-2 3-2 7-4 9-7 1-3 2-6 1-9 0-4-3-6-6-8-2-1-4-2-6-1-4 1-6 5-8 9h-5 0c-2-1-4-2-5-2-7-3-12-9-18-12l-1 1 5 6h-1v1h-2c0-1-1-1-1-2l-4-4c-3-2-6-3-9-4v-1l-3-1v-2z" class="S"></path><path d="M261 162c0-1 1-1 1-1 1 0 1 0 2 1-1 0-1 1-1 1l-2-1z" class="K"></path><path d="M179 128h-1v-1l1-1c1-1 1 0 2 0 0 1-1 2-2 2zm65 69c1 0 2-1 3-1l1 1h0l-1 3-3-3z" class="C"></path><path d="M243 142c1-1 2-1 3-1 0 1-1 2-2 3h-1v-2z" class="G"></path><path d="M238 252c1-1 2-1 4-1v1l-1 1h-3v-1z" class="M"></path><path d="M200 119h0 1c1 0 1 0 1 2-1 1-3 2-4 3 0-1 0-2 1-2s1-1 1-2v-1z" class="E"></path><path d="M272 292c1 1 1 2 2 2 0 1 0 1-1 2 0 1-1 1-3 2 1-2 2-4 2-6z" class="C"></path><path d="M240 153l3 3c0 1 0 1 1 2l-2 1c-1-2-2-4-2-6z" class="F"></path><path d="M157 117c1-2 2-2 4-2 1 2 0 2 1 4-2-1-4-2-5-2zm73 12v-3c1-1 3-3 5-4v1l-1 1h0c0 1-1 2-2 2h-1c0 1 0 2-1 3z" class="P"></path><path d="M243 303c0-1 0-2 2-3 0 1 1 1 1 2s-1 2-2 2-1 0-1-1z" class="G"></path><path d="M273 100h3 4s0 1-1 1h-3-5c-1 0-1-1-1-1h3z" class="B"></path><path d="M224 117l2-1v2c0 1-1 2-2 3h-1v-1c0-1 0-2 1-3z" class="I"></path><path d="M241 165c1 1 1 3 1 4s1 2 3 3c-1 1-2 1-2 1l-1-1-1-1c-1 0 0 0-1-1s1-2 1-2v-3z" class="B"></path><path d="M235 168c1-2 2-3 3-4 2-1 2-1 3 0h0v1c-2 1-4 2-5 4 0-1-1-1-1-1z" class="D"></path><path d="M193 121v-1c1-1 2-2 4-3 2 0 3 1 4 2h-1 0c-2 0-4 0-5 1l-1 2-1-1zm45 25c1-2 2-3 5-4v2c-1 1-3 2-3 4v1c0-1 0-2-1-2l-1-1z" class="J"></path><path d="M239 201c0-1-1-1-1-2 0-2 0-8 1-9h0c1 3 0 7 1 10h0l-1 1z" class="Q"></path><path d="M276 302l2 2c-1 3-1 5-3 8h0v-2h-1l2-8zm-60-183c3 0 5-1 8-2-1 1-1 2-1 3v1h-3 0-3 0l-1-2z" class="H"></path><path d="M264 172c1 2 1 3 1 5 0 0 0 1 1 1l-1 10c0-3-1-6-2-8h0l1 1v-1-8z" class="G"></path><path d="M138 106c0-1-1-2-2-3h0l8-1v1c2 1 6 0 8 0l-10 1h-3v1l-1 1z" class="M"></path><path d="M253 163c0-2 0-3-1-5h0v-1h0c2 0 4 1 5 2 0 1-1 2-1 2-1 1-1 1-1 2h0v1c0-1 0-1-1-1h-1z" class="J"></path><path d="M176 121c1 1 1 0 2 1v1h-2c-1 0-1 0-2 1 1 1 2 0 4 0h0v2s-1 0-1 1h-3c-1-1-1-2-1-3 1-2 1-2 3-3z" class="E"></path><path d="M244 158h3c1 0 2 1 3 1 0 1 1 2 1 3v1h-1c-1 0-1 0-1-1h-1l-6-3 2-1z" class="C"></path><path d="M250 159c0 1 1 2 1 3v1h-1c-1 0-1 0-1-1l-2-1v-1l3-1zm-3-40c1 0 1 0 2 1 0 1 0 1-1 2s-2 1-4 1c-1 0-2 0-3-1l6-3z" class="G"></path><path d="M241 253l-3 3c0 1-1 2-3 2v-2-5c2 0 2 0 3 1v1h3z" class="I"></path><path d="M241 164h1c1 1 2 1 4 1v3l-2 1c2 1 2 2 2 4h0l-1-1c-2-1-3-2-3-3s0-3-1-4h0v-1z" class="J"></path><path d="M242 169v-2 1c1 0 1 0 1-1v1h0l1 1c2 1 2 2 2 4h0l-1-1c-2-1-3-2-3-3z" class="G"></path><path d="M256 196c1 1 1 2 1 4s0 5-1 7c0 1-1 3-1 4h1 0l-3 3c1-4 3-10 2-14v-1c0-1 0-2 1-3z" class="D"></path><path d="M267 202c1 3 0 6-1 8-2 3-4 4-7 6l-1-1 1-1c2-2 5-4 6-7 1-1 2-3 2-5z" class="J"></path><path d="M230 116v3c1 0 1-1 2-1v-1c-1 3-1 4-4 5s-7 2-10 1h-2c0-1 0-2-1-3l1-1 1 2h0 3 0 3 1 2c3-1 3-3 4-5z" class="E"></path><path d="M218 123h-2c0-1 0-2-1-3l1-1 1 2h0 3 0l2 1h0l-4 1z" class="R"></path><path d="M265 305h2c2 0 2 0 3 1h0v3 1l-1 1c-1 1-1 1-3 1l-3-3v-1l1 1 1-1-2-2c1-1 1-1 2-1z" class="Q"></path><path d="M162 102l12-1c-2 1-4 1-5 2h0-17c-2 0-6 1-8 0v-1h18z" class="D"></path><path d="M235 222c0-1 0-2 1-3v3c1-1 0-1 0-2 1 1 1 1 1 2 1 0 2-1 3 0l2 1c0 1 0 1-1 1l-1 1v1h-1v1h3c1 2 4 4 4 6v1c-2-1-4-4-6-6-1-1-3-2-4-3 0-1-1-2-1-3z" class="E"></path><path d="M237 222c1 0 2-1 3 0l2 1c0 1 0 1-1 1l-1 1h0c-2-1-3-1-3-3z" class="B"></path><path d="M240 183c1 1 2 2 3 2l1 1h0c-1 1-1 1-2 1-2 1-4-1-5-2-2-2-5-5-5-8 0-4 0-6 3-9 0 0 1 0 1 1-2 2-3 4-3 7 1 1 1 2 2 3s2 2 2 3c1 0 2 0 3 1z" class="G"></path><path d="M264 123c0 1 0 1 1 2h0 1c0 1 1 1 1 1 3 5 4 9 5 13 1 2 1 5 2 6v2c-2-3-3-7-5-9h0c-1-2-2-2-3-3l1-1 1 1h0l-4-8h-1l1-1v-3z" class="N"></path><path d="M240 222c1-1 2-1 3-1l2 2c1 1 1 5 3 4 5 0 10-4 13-7-4 5-9 8-15 10 0-1-1-1-2-2h0l-2-1h-3v-1h1v-1l1-1c1 0 1 0 1-1l-2-1z" class="T"></path><path d="M241 224l1 1h1 1l-1 1v1l1 1-2-1h-3v-1h1v-1l1-1z" class="R"></path><path d="M253 150h0c4 0 9 2 12 4 6 5 7 10 7 17l-4-9c-2-4-4-6-7-9-3-2-7-2-11-1-2 0-4 1-5 2v1h-1v-1c2-3 6-4 9-4z" class="T"></path><path d="M257 159l2 1 2 2 2 1c1 2 3 4 3 6v9c-1 0-1-1-1-1 0-2 0-3-1-5 0-2-2-4-3-6l-1 2h0c-1-1-2-3-3-3s-1-1-2-1h0v-1h0c0-1 0-1 1-2 0 0 1-1 1-2z" class="R"></path><path d="M259 160l2 2 2 1c1 2 3 4 3 6-1 0-2-1-3-2 0-1-1-2-1-3-1 0-4-2-4-3l1-1z" class="D"></path><path d="M233 299h3v3h2c1 0 1 0 2-1 1 0 1 1 1 2 1 1 1 3 0 5-1 1-2 1-3 2-1 0-3 0-5-1v-1-9z" class="T"></path><path d="M232 324c4-5 6-13 12-16 3-1 6-1 9 0 2 2 3 3 4 6l-1-1h-1c-2-2-4-3-6-3s-4 0-6 2c-3 3-4 8-8 12-1 0-1 1-2 1l-1-1z" class="E"></path><path d="M276 101h3c6 1 12 1 18 1h6-20c0 1 1 1 2 1h16c-3 0-15 0-16 1l-2-1h-7c-1 0-3 1-4 1-3-1-6 0-8 0l-12-1h-4l-1 1c0-1-1-1-1-1h0-3-1l23-1c4-1 7-1 11-1z" class="U"></path><path d="M243 103h-1l23-1h6l1 1h-3 0-4-13-4l-1 1c0-1-1-1-1-1h0-3z" class="V"></path><path d="M232 272v1c1 1 1 1 0 2 0 2 0 6 1 8v-1c0-1 0-1 1-2h0c0-2-1-4 0-5v-1-1c0-1 1-1 1-2h1c0 1-1 3-1 4 0 2 0 3 1 4v2 1c0 2 1 3 0 4 0 1 0 1 1 2v4 1c0 2-1 4-1 6h-3v9h0c-1-2-1-5-1-7v-17c-1-2-1-5-1-8 0-1 1-2 1-4z" class="H"></path><path d="M233 299v-1c1-3 1-8 1-11 0-2-1-3 0-4v-1l1-1v2l1 1v2h0c0 1 0 1 1 2v4 1c0 2-1 4-1 6h-3z" class="G"></path><path d="M157 112l-1 1h-1l-2-2c-1-2-3-2-5-3 4-1 8-1 12 0v-1c1-1 1-2 3-2 4 0 7 0 10 2l-3 3h-1c0-1-1-1-2-2h-1c-1 1-1 1-1 2 1 1 1 2 2 2 0 1-1 2-2 3 0 0-1 0-1-1-2 0-5-1-7-2z" class="T"></path><path d="M157 112c1-1 2-3 4-4 0 3 1 4 2 6h1c-2 0-5-1-7-2z" class="K"></path><path d="M236 128c0-1 1-2 2-3l5 1c-1-1-3-3-5-4v-1l2-2h0v1h-1c0 2 4 5 5 5 2 1 5 1 7 2 4 1 13 4 16 7l-1 1c-2-2-5-3-7-4-6-2-13-3-19-3l3 3s1 0 2 1c-2 1-6 4-8 4l-2-2c0-1-1-3-1-5h1l1-1z" class="M"></path><path d="M236 128h0 2c1 0 1 2 1 3 1 0 0 1 0 1 0 1-1 1-2 1-1-1-1-2-2-4l1-1z" class="F"></path><path d="M248 103h4l12 1c2 0 5-1 8 0-2 1-5 2-6 4 0 1 0 3-1 4l2 1h1 0l-1 1c0 1 0 1-1 2h-1c-2-2-6-4-7-7-1 1-2 1-2 2-3-3-5-6-8-8z" class="L"></path><path d="M258 109c0-1 0-1 2-2 2 1 4 4 5 5l2 1h1 0l-1 1c0 1 0 1-1 2h-1c-2-2-6-4-7-7z" class="T"></path><path d="M123 99l92 1c-3 1-9 0-13 0h-47v1h-1c2 0 8 0 8 1h-18l-8 1h0c1 1 2 2 2 3l5 6h-1v1h-2c0-1-1-1-1-2l-4-4c-3-2-6-3-9-4v-1l-3-1v-2z" class="F"></path><path d="M249 162c0 1 0 1 1 1h1 2 1c1 0 1 0 1 1h0c1 0 1 1 2 1s2 2 3 3h0l1-2c1 2 3 4 3 6v8 1l-1-1h0l-5-5c-1-1-3-2-5-3-2 0-5 1-6 2h-1v-1h0c2-1 0-4 1-5v-1-3h-1l1-1 1-1h1z" class="J"></path><path d="M249 167h2c0 2 0 2 1 3l-4 3 1-6z" class="S"></path><path d="M253 163h1c1 0 1 0 1 1h0c1 0 1 1 2 1s2 2 3 3h-2c-2 0-4 1-6 2-1-1-1-1-1-3h-2l1-2v-2h1 2z" class="B"></path><path d="M250 165c1 0 1 0 2 1l-1 1h-2l1-2z" class="F"></path><path d="M260 168l1-2c1 2 3 4 3 6v8 1l-1-1h0l-5-5c1 0 2-1 2-1h-1c-1-1-2-2-2-3h0l5 3c-1-2-2-4-2-6z" class="K"></path><path d="M260 174h2l1 6h0l-5-5c1 0 2-1 2-1z" class="E"></path><path d="M237 182c2-2 6-6 9-6 4 1 9 3 12 6 3 5 4 11 4 17-1 5-3 8-6 12h0-1c0-1 1-3 1-4 1-2 1-5 1-7s0-3-1-4v-1h1c0 1 0 2 1 3v-1-2h0v-1c0-2-1-5-3-7-4-4-10-4-15-4-1-1-2-1-3-1z" class="O"></path><path d="M258 194c2 3 1 10 0 13l-2 4h-1c0-1 1-3 1-4 1-2 1-5 1-7s0-3-1-4v-1h1c0 1 0 2 1 3v-1-2h0v-1z" class="C"></path><path d="M204 129c1-1 2-1 3-2 2-2 3-4 3-6 0-1-1-2-2-3-2-4-6-5-10-5-1 0-4-1-5-1l1-1c4-1 9 0 13 1 2 1 4 3 5 5 1 1 1 3 1 5-1 2-2 4-4 7-1 0-2 2-4 2-10 2-19 4-29 8l-15 6v-1c-2 1 1 0-1 1h0c-1 0-2 0-2 1h-1-1c4-2 7-4 11-6 5-2 11-4 16-5 4-2 8-3 12-4h1-1c-2-1-3-3-3-4-1-2-1-4 0-6h1l1 1h-1c2 1 3 2 3 4l1 2c3 0 5 1 7 1z" class="G"></path><path d="M193 122c2 1 3 2 3 4l1 2c3 0 5 1 7 1-1 0-2 1-3 0-3 0-5-1-7-2-1-2-1-3-1-5z" class="P"></path><path d="M243 103h3 0s1 0 1 1l1-1c3 2 5 5 8 8 0-1 1-1 2-2 1 3 5 5 7 7 1 2 3 4 4 6v2h-3l1 2s-1 0-1-1h-1 0c-1-1-1-1-1-2v3l-1 1c-3-6-7-9-13-13-3-1-13-4-14-6 0-1 0-1 1-1l1 1h1v-2h0 2l1 1v-1c1 0 3 0 4 1-1-2-2-3-3-4z" class="a"></path><path d="M259 114l3 3-1 2c-1 0-2-2-3-3l1-2z" class="J"></path><path d="M243 103h3 0s1 0 1 1l1-1c3 2 5 5 8 8l3 3-1 2-8-8-3-1h-1c-1-2-2-3-3-4z" class="G"></path><path d="M258 109c1 3 5 5 7 7 1 2 3 4 4 6v2h-3l1 2s-1 0-1-1h-1 0c-1-1-1-1-1-2h0c-1-1-2-3-3-4l1-2-3-3-3-3c0-1 1-1 2-2z" class="P"></path><path d="M262 117c2 2 3 4 4 7l1 2s-1 0-1-1h-1 0c-1-1-1-1-1-2h0c-1-1-2-3-3-4l1-2z" class="U"></path><path d="M174 101l31 1c5 0 13-1 18 0 3 1 5 2 7 4s2 3 2 5c1 2 1 4 0 6v1c-1 0-1 1-2 1v-3s1-1 1-2c0-3-1-5-3-7-2-1-3-2-6-3-5-1-11 0-17 0h-23c1 1 4 2 5 2 4 1 8 0 12 1 5 0 10 1 14 1 2 1 5 1 6 2v1c1 1 1 1 0 2 0 2-1 2-2 3-1 0-1-1-2-2l-1-1c-1-2-3-3-6-3-4-1-8-1-12-1-3 0-6 0-9-1h0c2 2 5 5 5 9 0 1-1 1-1 1 0-2-1-4-2-5-6-7-12-9-20-10h0c1-1 3-1 5-2z" class="O"></path><path d="M219 111c1 1 1 1 0 2 0 2-1 2-2 3-1 0-1-1-2-2l-1-1 1-1c1 0 1 0 2 1l1-1v-1 1 1h1v-2h0z" class="a"></path><path d="M240 200l3-3h1l3 3 2 2c-2 2-3 3-6 4 0 1 0 4 1 6s4 3 6 4c3 1 5 1 8 0 4-1 6-2 9-5-1 3-1 5-4 7-6 3-12 5-19 3h0-1c-1 0-2 0-3 1-1-1-2 0-3 0 0-1 0-1-1-2 0 1 1 1 0 2v-3c-1 1-1 2-1 3-1-2 0-6 0-8l-1-1c0-5 2-8 5-12l1-1z" class="G"></path><path d="M238 212v-1c5 5 8 8 16 9-2 1-3 1-5 0-4-1-7-3-10-5l-1-3z" class="B"></path><path d="M238 212l1 3h-2v2c1 0 1 1 1 1h0l-2 2c0 1 1 1 0 2v-3c-1 1-1 2-1 3-1-2 0-6 0-8l1-1 2 1v-2z" class="I"></path><path d="M246 164c-5-1-8-1-11-5-2-3-2-9-1-12 1-6 4-10 9-13 2-2 8 0 11 1 7 1 12 5 15 11 6 9 6 21 6 32 1-3 0-7 1-10h0c1 6 0 13 1 19 0 1-1 4 0 5l-3 15h0v-1 1c-1 0-1 0-1 1h0v1c-1 0-1 1-2 1-1 7-3 14-7 20-3 6-7 11-11 17-2 2-3 4-4 6-4 5-8 10-10 16-1 3-1 6-2 9v4h-1v-1-2c-1-1-1-2-1-4 0-1 1-3 1-4h-1c0 1-1 1-1 2v1 1c-1 1 0 3 0 5h0c-1 1-1 1-1 2v1c-1-2-1-6-1-8 1-1 1-1 0-2v-1l3-6c3-4 6-8 9-13 3-3 5-6 8-9l-4 3c-3 1-8 0-11-1-2 0-4-2-5-4 0-2 0-6 1-7l3-3c2 0 3 0 4 1 0 1 0 1 1 2-1 2-2 2-3 3-1 0 0-2-1-3-1 0-1 0-1 1-1 0-2 2-2 3s1 3 2 4c2 2 4 2 6 2 5 0 11-4 14-8 9-11 13-26 15-41 1-8 2-16 1-24v-2c1-1 0-3 0-4 0-5-1-9-3-13-2-5-4-9-9-10-3-1-7-1-10 1h-1c3-3 6-4 9-5-4-1-8-3-13-1h0-2c-3 2-6 7-7 10 0 3 0 6 1 8v-1c-1-3 0-6 1-9l1 1c1 0 1 1 1 2-1 1-1 3 0 4 0 2 1 4 2 6l6 3-1 1-1 1z" class="O"></path><defs><linearGradient id="X" x1="272.876" y1="188.056" x2="276.097" y2="188.449" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#353537"></stop></linearGradient></defs><path fill="url(#X)" d="M275 178c1-3 0-7 1-10h0c1 6 0 13 1 19 0 1-1 4 0 5l-3 15h0v-1 1c-1 0-1 0-1 1h0v1c-1 0-1 1-2 1 2-11 3-21 4-32z"></path><path d="M276 103h7l2 1 1 1v2c1 2 2 6 3 9l1 1h-1c-1 2-1 4-2 6l-1 6c-1-2-1-3-2-4h-1-1c0 2 0 4 1 6 0 3 0 7-1 10h0l1-1h0l1 1c-1 2-1 3-1 5-1 5-2 11-2 16-1 3 0 7-1 9v8 15c0-1-1-2-1-2v2l-1 2-1-3v-1c-1-1 0-4 0-5-1-6 0-13-1-19v-3l-1-10-1-6v-2-2c-1-1-1-4-2-6-1-4-2-8-5-13l-1-2h3v-2c-1-2-3-4-4-6h1c1-1 1-1 1-2l1-1h0-1l-2-1c1-1 1-3 1-4 1-2 4-3 6-4 1 0 3-1 4-1z" class="F"></path><path d="M280 152l-1 1-1-1v-5c1 1 1 1 2 1v4z" class="B"></path><path d="M275 138l1 11c-1-1-1-1-2 0v-2-2-2h0c1-1 1-2 0-3h0c1-1 1-2 1-2z" class="I"></path><path d="M274 149c1-1 1-1 2 0h1c1 5 0 11 0 15v4h0c0-1-1-2-1-3l-1-10-1-6z" class="J"></path><path d="M274 149c1-1 1-1 2 0h1l-1 6h-1l-1-6z" class="E"></path><path d="M275 124v3 11s0 1-1 2h0c1 1 1 2 0 3h0l-3-12 1-1c0-1 1-3 1-5l2-1z" class="J"></path><path d="M275 124v3l-1 3-1 1h-1v-1c0-1 1-3 1-5l2-1z" class="O"></path><path d="M269 122v1h1 0c0 1 1 1 2 2h1c0 2-1 4-1 5l-1 1 3 12v2c-1-1-1-4-2-6-1-4-2-8-5-13l-1-2h3v-2z" class="C"></path><path d="M269 123h1 0c0 1 1 1 2 2h1c0 2-1 4-1 5l-1 1c0-2-2-5-2-7 0-1 1 0 0-1z" class="I"></path><path d="M282 141h0l1-1h0l1 1c-1 2-1 3-1 5-1 5-2 11-2 16-1 3 0 7-1 9v8 15c0-1-1-2-1-2v2l-1 2-1-3v-1c-1-1 0-4 0-5-1-6 0-13-1-19v-3c0 1 1 2 1 3h0v-4c1 2 1 4 1 6h0c0-3 0-7 1-11h0l1-7v-4l2-7z" class="W"></path><path d="M279 171v7h1c0-2-1-5 0-7v8 15c0-1-1-2-1-2h-1c-1-4 0-8 1-11v-10z" class="U"></path><path d="M282 141h0l1-1h0l1 1c-1 2-1 3-1 5-1 5-2 11-2 16-1 3 0 7-1 9s0 5 0 7h-1v-7-12l1-7v-4l2-7z" class="M"></path><path d="M273 112c1-1 3-2 5-2s3 1 4 2c0 1 1 1 1 2h0 2v2 1l-1 1-4 3v2c1 8-1 17-2 25l-2-24h-1l-2 1h-1c-1-1-2-1-2-2s1-1 1-2l-1-1c1-1 1-2 2-3-1-2 0-3 1-5z" class="R"></path><path d="M276 114c1-1 2-1 3 0v1c0 1 0 1-1 2h-1s-1 0-1-1v-2z" class="C"></path><path d="M273 112v4c1 1 2 1 2 3l-1 1c-1-1-2-2-2-3-1-2 0-3 1-5z" class="J"></path><path d="M280 123v-1c-1-1-1 0-2-1l1-1s1-1 2-1c0-1 0-1 1-2h0l1-1s1 0 2 1l-1 1-4 3v2z" class="D"></path><path d="M272 117c0 1 1 2 2 3 2 1 2 1 2 3v1h-1l-2 1h-1c-1-1-2-1-2-2s1-1 1-2l-1-1c1-1 1-2 2-3z" class="S"></path><path d="M276 103h7l2 1 1 1v2c1 2 2 6 3 9l1 1h-1c-1 2-1 4-2 6l-1 6c-1-2-1-3-2-4h-1l-1-1c0-2 1-3 3-4 0-1-1-2-1-2l1-1v-1-2h-2 0c0-1-1-1-1-2-1-1-2-2-4-2s-4 1-5 2c-1 2-2 3-1 5-1 1-1 2-2 3l1 1c0 1-1 1-1 2h0-1v-1c-1-2-3-4-4-6h1c1-1 1-1 1-2l1-1h0-1l-2-1c1-1 1-3 1-4 1-2 4-3 6-4 1 0 3-1 4-1z" class="L"></path><path d="M265 116h1c1-1 1-1 1-2l3 9h-1v-1c-1-2-3-4-4-6zm11-13h7l2 1 1 1v2c-4-2-6-2-10-2l2-2h-2z" class="E"></path><path d="M276 103h2l-2 2c-2 0-4 1-6 3-1 1-1 4-2 5h0-1l-2-1c1-1 1-3 1-4 1-2 4-3 6-4 1 0 3-1 4-1z" class="D"></path><path d="M271 210c1 0 1-1 2-1v-1h0c0-1 0-1 1-1v-1 1l-2 8c-3 11-6 20-13 29-3 2-6 5-7 9-1 2-1 5 0 7 2 3 4 4 7 5h2l1 1c3 0 8-1 11 1h1c-2 2-5 1-7 2h2v4l2-1v2 1 1l2 2h0c0 1 0 1 1 2h-1v1c1 1 0 4 1 6v1h0c-1 1-2 2-2 3-1 0-6 1-6 2h3 0c1-1 2-1 3-1 0 2-1 4-2 6-5 5-11 5-17 5-2 0-4 0-6-2l-1-1-2-2c0-1-2-2-3-2 1 1 2 3 4 4-2 1-2 2-2 3 0-2-2-3-3-5-1-1-2-3-2-4l-1-1v-1-4c-1-1-1-1-1-2 1-1 0-2 0-4h1v-4c1-3 1-6 2-9 2-6 6-11 10-16 1-2 2-4 4-6 4-6 8-11 11-17 4-6 6-13 7-20z" class="L"></path><path d="M247 266c2 0 3 1 4 2 0 1 0 1-1 2 0 1-1 2-2 3h-1l-2-2v-1c0-2 1-3 2-4z" class="C"></path><path d="M236 282h1v-4c0 6 1 11 6 16 1 1 3 2 5 3h4c-2 1-5 1-7 1h-1c0-1-2-2-3-2 1 1 2 3 4 4-2 1-2 2-2 3 0-2-2-3-3-5-1-1-2-3-2-4l-1-1v-1-4c-1-1-1-1-1-2 1-1 0-2 0-4z" class="D"></path><path d="M262 266c3 0 8-1 11 1h1c-2 2-5 1-7 2h2v4l2-1v2 1 1l2 2h0c0 1 0 1 1 2h-1v1c1 1 0 4 1 6v1h0c-1 1-2 2-2 3-1 0-6 1-6 2h3 0c1-1 2-1 3-1 0 2-1 4-2 6-5 5-11 5-17 5-2 0-4 0-6-2l-1-1-2-2h1c2 0 5 0 7-1l2 2h3 2l3-3c0-1 0-1-1-1 0-1-1-1-2-1-3-2-6-3-8-6-2-4-2-7-1-11 2-6 7-8 12-11z" class="O"></path><path d="M266 297h2v2h-3 0 0 0v-1l-2 1v-1h0c1 0 2-1 3-1z" class="a"></path><path d="M253 278c0 1 1 1 2 2l-1 1h1l1 1c-1 1-1 3-2 4-1-2-2-5-1-8z" class="f"></path><path d="M252 297l2 2h3c-2 1-4 1-6 1s-4-1-5 0l-2-2h1c2 0 5 0 7-1z" class="P"></path><path d="M274 287v1h0c-1 1-2 2-2 3-1 0-6 1-6 2h3c-2 1-4 1-6 1v-1-1h4c0-1-1-1-1-2h-2c1 0 1-1 2 0 1 0 3-1 5-1 1 0 2-1 3-2z" class="E"></path><path d="M253 278c1-2 1-3 2-4 3-3 8-5 12-5 0 2-1 3-3 4 0-1 0-2-1-2s-3 1-4 2l-4 7c-1-1-2-1-2-2z" class="B"></path><path d="M267 269h2v4l2-1v2 1 1l2 2h0c0 1 0 1 1 2h-1v1c1 1 0 4 1 6-1 1-2 2-3 2-2 0-4 1-5 1-1-1-1 0-2 0s-3 1-5 1c-2-1-4-3-5-5 1-1 1-3 2-4l-1-1h-1l1-1 4-7c1-1 3-2 4-2s1 1 1 2c2-1 3-2 3-4z" class="K"></path><path d="M269 273l2-1v2 1 1c-1-1-2-1-3-3h1z" class="I"></path><path d="M259 273c1-1 3-2 4-2s1 1 1 2v1s1 1 1 2l4 5h0c0 1 0 1-1 1-2 1-3 2-5 5 0-1 0 0-1-1h0c-1 0-1-1-2-1 0-1 0-2-1-3h0-3l-1-1h-1l1-1 4-7z" class="R"></path><path d="M259 282h3c1 1 1 3 0 4h0c-1 0-1-1-2-1 0-1 0-2-1-3h0z" class="C"></path><path d="M261 274v-1l1 1h0c0 2 3 4 3 6-2-1-4-1-6-1h0c2-2 2-3 2-5z" class="f"></path><path d="M259 273c1-1 3-2 4-2s1 1 1 2v1s1 1 1 2l4 5h0c0 1 0 1-1 1 0-1-1-1-2-2h-1 0c0-2-3-4-3-6h0l-1-1v1c-1 0-1 0-2-1z" class="E"></path><path d="M279 192s1 1 1 2c0 3 0 7 1 10l1 4h0 1 1c1 5 3 10 5 15 3 8 9 15 13 22l7 11c5 7 9 15 10 23 0 2-1 2 0 4s0 5-1 8c-1 2-2 3-2 4h0c-1 2-3 4-4 6l1 1c-1 0-1 1-2 1l-1-1c-2 1-4 1-5 1-6 0-11 0-16-3-2-1-3-2-4-4l-2-4c2 1 3 1 5 1v-1c-1 0-3-1-4-1-2 0-3-1-5 0v5c-2 1-1 1-2 3 0-2 1-5 0-7-1 0-1 0-2-1 1 0 1 0 1-1s-1-2-2-2v-1c-1-2 0-5-1-6v-1h1c-1-1-1-1-1-2h0l-2-2v-1-1-2l-2 1v-4h-2c2-1 5 0 7-2h-1c-3-2-8-1-11-1l-1-1h-2c-3-1-5-2-7-5-1-2-1-5 0-7 1-4 4-7 7-9 7-9 10-18 13-29l2-8h0l3-15v1l1 3 1-2v-2z" class="L"></path><path d="M277 248h2c-1 3-1 7-1 10h-1-1c0-2 1-4 1-6-1-1-1-3 0-4z" class="T"></path><path d="M288 246c2 0 4 0 6 1 1 1 2 2 2 3s-1 3-2 3c0 1-1 1-2 1h0c0-2 1-3 0-5-1 0-2-1-3-1h-2 0c0-1 0-1 1-2zm-18 3c-2-1-4-2-6-1h0v1c1 1 1 3 0 5h0c-2-1-4-3-5-5 3-2 4-4 8-3 1 0 2 0 3 1 1 0 3 1 3 3 1 2 1 6 0 8v2c-1-1-1-3-1-3l1-1v-5l-1-1v-1c-1-1-1-1-3-2h0-1c-1-1-3-1-4 0 1-1 1 0 2 0h2v1c1 0 1 0 2 1h0z" class="a"></path><path d="M273 242c1-2 2-4 4-5 2 0 5 2 6 3v1 1c-1 1-1 2-2 2-1 1-2 1-3 1-1-1-4-1-5-3h0z" class="N"></path><path d="M280 241h0 3v1c-1 1-1 2-2 2 0-1 0-2-1-2v-1z" class="J"></path><path d="M273 242c2 0 3 0 5-1v-1h1l1 1v1c1 0 1 1 1 2-1 1-2 1-3 1-1-1-4-1-5-3h0z" class="D"></path><path d="M279 192s1 1 1 2c0 3 0 7 1 10h-1v5c0 5 0 10 2 14-1 0-1 0-1-1-1 1-1 2-1 3s-1 2-1 2c-1 1 0 2-1 2v1 1c-1-1-1-3-1-4-1-2 0-6-1-8l-1-1v-2c1-4 1-8 2-12l1-8 1-2v-2z" class="F"></path><path d="M279 192s1 1 1 2c0 3 0 7 1 10h-1v5l-1-15v-2z" class="H"></path><defs><linearGradient id="Y" x1="269.182" y1="225.603" x2="309.761" y2="246.032" xlink:href="#B"><stop offset="0" stop-color="#cdccce"></stop><stop offset="1" stop-color="#f7f7f7"></stop></linearGradient></defs><path fill="url(#Y)" d="M280 209v-5h1l1 4h0 1 1c1 5 3 10 5 15l-2 2c3 6 6 12 10 18 2 2 5 4 6 7 1 2 3 5 2 8-1 2-3 5-5 6-1 1-3 1-5 1l-1 1c-2 0-5 0-8-1-1 0-2 0-3 1l-1-1c1-2 2-3 2-5h0v-3h1v-1c-1-2-1-4 0-5 0 2 0 3 1 5 0 2 2 4 5 5 2 1 5 1 8 0 1-1 3-2 3-4 1-2 1-3 0-5-2-3-6-6-8-8-2-3-3-6-5-9-3-4-5-7-7-12-2-4-2-9-2-14z"></path><path d="M284 260h1c1 3 2 3 5 4 2 1 3 1 5 1l-1 1c-2 0-5 0-8-1-1 0-2 0-3 1l-1-1c1-2 2-3 2-5z" class="K"></path><path d="M282 208h1 1c1 5 3 10 5 15l-2 2c-3-5-4-11-5-17z" class="B"></path><path d="M277 192v1l1 3-1 8c-1 4-1 8-2 12v2c-1 7-5 13-8 19-2 2-3 5-5 7s-6 5-7 8c-1 2-1 5 0 7 1 1 4 2 6 3h4 0c2-1 4-2 5-3 1-2 2-6 1-8 0 0-1-1-1-2h0 0c-1-1-1-1-2-1v-1h-2c-1 0-1-1-2 0 1-1 3-1 4 0h1 0c2 1 2 1 3 2v1l1 1v5l-1 1s0 2 1 3v-2 1c2 2 3 3 6 3 5-2 3-4 3-8 0-2 0-3 1-5h0c1-2 3-2 5-3-1 1-1 1-1 2h0c-1 1-1 2-2 3s-1 3 0 5v1h-1v3h0c0 2-1 3-2 5-2 1-4 2-5 1-3-1-4-3-6-5v1c-3 2-6 3-10 3h-2c-3-1-5-2-7-5-1-2-1-5 0-7 1-4 4-7 7-9 7-9 10-18 13-29l2-8h0l3-15z" class="R"></path><path d="M277 192v1l1 3-1 8c-1 4-1 8-2 12v-3-2h0l1-1v-3-5 1c0 2-1 4-1 6-1 0-1 1-1 2s-1 3-2 4h0l2-8h0l3-15z" class="H"></path><path d="M289 223c3 8 9 15 13 22l7 11c5 7 9 15 10 23 0 2-1 2 0 4s0 5-1 8c-1 2-2 3-2 4h0c-1 2-3 4-4 6l1 1c-1 0-1 1-2 1l-1-1c-2 1-4 1-5 1-6 0-11 0-16-3-2-1-3-2-4-4l-2-4c2 1 3 1 5 1v-1c-1 0-3-1-4-1-2 0-3-1-5 0v5c-2 1-1 1-2 3 0-2 1-5 0-7-1 0-1 0-2-1 1 0 1 0 1-1s-1-2-2-2v-1c-1-2 0-5-1-6v-1h1c-1-1-1-1-1-2h0l-2-2v-1-1-2l-2 1v-4h-2c2-1 5 0 7-2h-1c-3-2-8-1-11-1l-1-1c4 0 7-1 10-3v-1c2 2 3 4 6 5 1 1 3 0 5-1l1 1c1-1 2-1 3-1 3 1 6 1 8 1l1-1c2 0 4 0 5-1 2-1 4-4 5-6 1-3-1-6-2-8-1-3-4-5-6-7-4-6-7-12-10-18l2-2z" class="L"></path><path d="M298 268c2 0 2 0 3 1 2 1 2 2 4 3 1 0 1 0 2-1 0 1 0 1 1 2h-2s-1 0-1 1v2c-2-3-4-5-7-8z" class="C"></path><path d="M307 271c1 0 2 1 3 1v-1c-1-1-2-1-3-1s-2-1-2-1c0-1 0-1 1-2 1 0 1-1 2-1s2 1 2 1c1 2 1 2 1 3l1 1c-1 2-2 2-3 3 0-1-1-1-1-1-1-1-1-1-1-2zm12 12c1 2 0 5-1 8-1 2-2 3-2 4h0 0l-2 1c-3 2-8 3-11 2h0c3 0 6-2 8-3 4-3 7-8 8-12z" class="D"></path><path d="M286 265c3 1 6 1 8 1l4 2c3 3 5 5 7 8 1 4 2 6 1 10-1 3-4 5-6 6-1 1-4 1-5 3 0 1 1 2 2 3 2 1 7 2 9 2 1 0 3 0 4 1h2l1 1c-1 0-1 1-2 1l-1-1c-2 1-4 1-5 1-6 0-11 0-16-3-2-1-3-2-4-4l-2-4c2 1 3 1 5 1v-1h1l-1-1 1-1c2 0 4 1 5 1 2 0 4-1 5-2 2-2 3-3 4-6v-4c-1-3-4-6-6-7-5-4-11-3-17-4 2-1 3-2 6-3z" class="G"></path><path d="M285 296l4 2v2c-2-1-3-2-4-4z" class="E"></path><path d="M292 294c0-1 0-1 1 0v1c0 1 0 1 1 2v2c-1 0-2-1-3-2 0-1 0-2 1-3z" class="H"></path><path d="M261 265c4 0 7-1 10-3v-1c2 2 3 4 6 5 1 1 3 0 5-1l1 1c1-1 2-1 3-1-3 1-4 2-6 3 6 1 12 0 17 4 2 1 5 4 6 7v4c-1 3-2 4-4 6-1 1-3 2-5 2-1 0-3-1-5-1l-1 1 1 1h-1c-1 0-3-1-4-1-2 0-3-1-5 0v5c-2 1-1 1-2 3 0-2 1-5 0-7-1 0-1 0-2-1 1 0 1 0 1-1s-1-2-2-2v-1c-1-2 0-5-1-6v-1h1c-1-1-1-1-1-2h0l-2-2v-1-1-2l-2 1v-4h-2c2-1 5 0 7-2h-1c-3-2-8-1-11-1l-1-1z" class="K"></path><path d="M292 286c1-1 1-3 3-4-1 2 0 2 0 4-2 1-2 0-3 0zm-6-4l5 1-1 1h-2c-1 0-1-1-2-2z" class="C"></path><path d="M289 279s1-1 1-2v5l1 1-5-1v-1c1-1 2-2 3-2z" class="D"></path><path d="M279 280c2-1 4-3 5-4 1 1 1 2 1 3s-1 2-1 2h0-1-1c-1 1-2 1-3 0v-1z" class="P"></path><path d="M281 287v-2c2 3 5 4 8 5l-1 1 1 1h-1c-1 0-3-1-4-1l-1-1c-1-1-1-1-2-3z" class="J"></path><path d="M297 272c2 1 5 4 6 7v4-2h-1c0-2 0-3-1-4l-3-3v1h0l-3 4v1h-2c-1-2 1-6 1-8h3z" class="C"></path><path d="M293 280c0-2 1-4 2-5 0-1 1-1 2-1l1 1-3 4v1h-2z" class="H"></path><path d="M290 277c0-2 2-3 1-6h0 0c2 0 2 0 3 1 0 2-2 6-1 8h2c1-1 2-1 3-2 1 0 2 0 2 1 1 1 1 2 1 3-2 1-4 0-6 0-2 1-2 3-3 4h-1l-1-2 1-1-1-1v-5z" class="O"></path><path d="M273 278c1 1 2 1 3 2h0l1-1h1l1 1v1c1 1 2 1 3 0h1 1c-1 1-2 2-2 3l-1 1v2c1 2 1 2 2 3l1 1c-2 0-3-1-5 0v5c-2 1-1 1-2 3 0-2 1-5 0-7-1 0-1 0-2-1 1 0 1 0 1-1s-1-2-2-2v-1c-1-2 0-5-1-6v-1h1c-1-1-1-1-1-2h0z" class="f"></path><path d="M278 287l1-1h0c1 0 1-1 1-1h1v2c1 2 1 2 2 3l1 1c-2 0-3-1-5 0 0-2 0-3-1-4z" class="C"></path><path d="M278 287c1 1 1 2 1 4v5c-2 1-1 1-2 3 0-2 1-5 0-7v-2c1-1 0-2 1-3z" class="H"></path><path d="M261 265c4 0 7-1 10-3v-1c2 2 3 4 6 5 1 1 3 0 5-1l1 1c1-1 2-1 3-1-3 1-4 2-6 3 6 1 12 0 17 4h-3c-1-1-1-1-3-1h0 0c1 3-1 4-1 6 0 1-1 2-1 2-1-1-2-1-3-3v-1h0l-2 1c-1 1-3 3-5 4l-1-1h-1l-1 1h0c-1-1-2-1-3-2l-2-2v-1-1-2l-2 1v-4h-2c2-1 5 0 7-2h-1c-3-2-8-1-11-1l-1-1z" class="P"></path><path d="M271 272c3-1 5-2 8-1 0 0 1 0 1 1v1h-2-1l-1 1-1-1h-2v1 2c-1-1-1-1-2-1v-1-2z" class="M"></path><path d="M271 274l2-2v1 1 2c-1-1-1-1-2-1v-1z" class="G"></path><path d="M273 273h2l1 1c0 2 1 3 2 5h0-1l-1 1h0c-1-1-2-1-3-2l-2-2v-1c1 0 1 0 2 1v-2-1z" class="E"></path><path d="M273 273h2l1 1c0 2 1 3 2 5h0-1l-1 1h0v-2c0-2-2-3-3-4v-1z" class="b"></path><path d="M283 272v-1h4v1c0 1-1 2-1 3l-2 1c-1 1-3 3-5 4l-1-1h0c-1-2-2-3-2-5l1-1h1 2l1 2v-1l2-2z" class="N"></path><path d="M283 272v-1h4l-1 1-3 1v-1z" class="D"></path><path d="M276 274l1-1h1 2l1 2v2s-1 1-2 1l-1 1c-1-2-2-3-2-5z" class="E"></path><path d="M279 278c-1-1-1-1-1-3h1 2v2s-1 1-2 1z" class="R"></path></svg>