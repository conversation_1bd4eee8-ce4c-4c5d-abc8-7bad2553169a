<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="25 30 570 672"><!--oldViewBox="0 0 620 752"--><style>.B{fill:#242424}.C{fill:#292929}.D{fill:#2e2e2f}.E{fill:#333334}.F{fill:#414141}.G{fill:#737273}.H{fill:#3a3a3a}.I{fill:#5c5c5c}.J{fill:#545454}.K{fill:#202020}.L{fill:#8c8b8c}.M{fill:#474648}.N{fill:#6e6d6d}.O{fill:#666566}.P{fill:#1a1a1a}.Q{fill:#9f9e9f}.R{fill:#4b4a4b}.S{fill:#8b8a8b}.T{fill:#d1d1d1}.U{fill:#1d1d1e}.V{fill:#e1e0e1}.W{fill:#929192}.X{fill:#7b7a7a}.Y{fill:#c8c7c8}.Z{fill:#848384}.a{fill:#4f4f4f}.b{fill:#b4b3b4}.c{fill:#c0bfc0}.d{fill:#abaaaa}.e{fill:#121212}.f{fill:#e7e6e6}.g{fill:#606060}.h{fill:#575658}.i{fill:#dfdedf}.j{fill:#d7d5d6}.k{fill:#696868}.l{fill:#efeeef}.m{fill:#bcbcbb}.n{fill:#838382}.o{fill:#a6a4a5}.p{fill:#0a0a0a}.q{fill:#fcfcfb}</style><path d="M218 640c1-1 2-1 3-1-1 1-2 1-3 1h0z" class="U"></path><path d="M276 656l4-1v1l-1 1c-1-1-2-1-3-1z" class="B"></path><path d="M319 659l1 1-3 1h-1c1-1 2-1 3-2z" class="K"></path><path d="M585 521v-1h1v2h0c-1 0-1 0-1-1z" class="F"></path><path d="M319 659c1 0 2-1 3-1h0c0 1-1 2-2 2l-1-1z" class="P"></path><path d="M389 223c0-1 0-1 1-2 1 1 1 1 0 2v1c0-1 0-1-1-1z" class="E"></path><path d="M473 599l-1-4 1-1 1 5h-1z" class="P"></path><path d="M533 538c2 1 2 2 3 3h0v1c-1-1-1-1-2-1 0-1 0-2-1-3z" class="B"></path><path d="M487 658v1c0 2-1 3-1 4v-2-1h0l1-2z" class="P"></path><path d="M576 497c1 1 1 2 2 3h-2l-1-2 1-1z" class="I"></path><path d="M49 587c0-2-1-4-1-6l1-1c0 2 1 3 1 5l-1 2z" class="U"></path><path d="M438 680l4-2h1c-1 0-1 0-1 1h1s-2 1-3 1h0-2z" class="C"></path><path d="M336 658c1-1 2-1 2-2h1 0v1c-1 1-1 1-2 1l-2 2v-2h1z" class="E"></path><path d="M280 655c2 0 3-1 5-1h0c0 1 1 1 1 1l-6 1v-1z" class="U"></path><path d="M505 675c1 1 1 1 1 2-1 0-1 1 0 2h-1c-1 1-1 0-2 0l2-4z" class="F"></path><path d="M363 688l5 2-1 1h-1c-1 0-1-1-2-1 0-1 0-1-1-2z" class="N"></path><path d="M59 433c1-3 3-5 3-8h1v2c-2 2-2 4-3 6h-1z" class="U"></path><path d="M68 600c0-3-2-7-2-9 1 1 2 5 3 6l-1 3z" class="P"></path><path d="M390 437v1l1 1-2 2h-2l3-4zM97 565v-1l4 6h0l-1 1-3-6z" class="K"></path><path d="M574 493c1 2 1 3 2 4l-1 1-2-3 1-2z" class="G"></path><path d="M478 590c-1-1-2-3-2-5l1-1c1 2 1 4 2 5v1l-1-2v1 1z" class="E"></path><path d="M280 673l-1-1c1 0 2 0 3-1h3 1l-6 2z" class="p"></path><path d="M47 425c1 2 1 3 1 5v2l-2-2c1-1 1-3 1-5z" class="N"></path><path d="M410 301c0 1 1 2 1 3v1c0 1 0 1 0 0h0c-1-1-2-2-4-2l1-1c1-1 1-1 2-1z" class="U"></path><path d="M198 646c3 0 6 0 8 1h-1-8l1-1z" class="H"></path><path d="M138 661l-5-3v-1c2 1 4 2 5 3h-1l1 1z" class="U"></path><path d="M413 435l5-2v2h1l-4 1-2-1z" class="Z"></path><path d="M395 427l2 1c-1 1-2 3-4 5v-3l2-3z" class="a"></path><path d="M64 473l1-1v6c-1 2 0 5 0 7-1 0-1 0-1-1v-11z" class="K"></path><path d="M578 500l2 3-2 2-2-5h2z" class="J"></path><path d="M49 554l1-2 3 11c-2-3-3-6-4-9z" class="H"></path><path d="M262 217h5c2-1 3-1 5-1h0c1 1 2 1 3 1-1 0-4 0-5 1-2-1-5-1-8-1z" class="B"></path><path d="M43 582c0-3-1-7-1-10h1c0 3 1 5 1 8l-1 2z" class="U"></path><path d="M335 487h4 0l4 1h-7c-2 0-3 0-4-1h3z" class="G"></path><path d="M92 642l2 2 1 2-1 1c-1-1-2-3-3-4l1-1z" class="R"></path><path d="M239 701h6 1c1 0 1 0 2 1-2 0-6 1-8 0-1-1-3-1-4-1h3z" class="K"></path><path d="M354 675h2c1 2 2 4 3 5l-1 1c-2-2-3-4-4-6z" class="G"></path><path d="M97 565c-2-1-4-5-4-7-1 0 0-1 0-1l4 7v1z" class="P"></path><path d="M522 527c1 0 2 0 2 1 1 0 1 0 1 1h0c1 2 2 3 2 5-2-2-4-5-5-7z" class="D"></path><path d="M521 524h2c0 1 2 3 2 4v1c0-1 0-1-1-1 0-1-1-1-2-1-1-1-1-2-2-3h1z" class="H"></path><path d="M350 696l6 2h0-2-1 1c1 0 0 0 1 1h-1l-5-2 1-1z" class="b"></path><path d="M307 219h3l7-1v1c-3 1-6 2-10 0z" class="a"></path><path d="M505 470c1-1 1-1 2-1h0 2l3 2h0-2c-2 0-3 0-5-1z" class="H"></path><path d="M355 662c1-4 3-8 5-11-2 4-3 7-3 12l-1-3c1 0 1-1 1-1-1 1-1 2-2 2v1z" class="R"></path><path d="M61 453c1-4 2-7 2-10h1c0 2-1 5-1 7s0 4-1 6v-3-3c0 1 0 2-1 3h0z" class="K"></path><path d="M306 218h1c3 0 6-1 9-1l1 1-7 1h-3l-1-1zm11 436h1v1c-2 1-3 1-5 2-1 1-2 2-3 2v-1h0-1c-1 1-1 1-2 1l10-5z" class="P"></path><path d="M282 686h1v1l-8 2h-2v-1l9-2z" class="F"></path><path d="M451 425c3-1 9-2 12-1-2 1-6 1-8 1-1 1-2 1-3 1l-1-1z" class="C"></path><path d="M575 595v5c1-1 1-1 1-2 0 4-1 7-1 10-1-3-1-9 0-13z" class="I"></path><path d="M112 633c-3-1-6-4-8-6l1-1c1 0 2 1 2 2 2 1 4 2 6 4-1-1-2-1-3-2l-3-2c0 1 1 1 2 2l3 3z" class="P"></path><path d="M571 505c2 2 4 4 5 6h-1 0c-1 0 0 1 0 2l-2-3c0-2-2-3-3-5h1z" class="O"></path><path d="M87 637v-1h1l4 6-1 1-4-6z" class="F"></path><path d="M88 636v-1l1 1c2 2 3 5 5 6v2l-2-2-4-6h0z" class="U"></path><path d="M451 425l1 1-6 1-4 1-1-1 10-2z" class="G"></path><path d="M429 709l8-2h1l-1 1h0c1 0 1 0 1 1l-5 1c-1-1-2-1-4-1z" class="I"></path><path d="M437 707l8-4h2c-3 2-6 4-9 4h-1z" class="G"></path><path d="M381 473l10-4 1 1-11 4v-1zM81 628h1s4 6 5 6c0 1 1 2 1 2h0-1v1c-3-3-4-6-6-9z" class="H"></path><path d="M186 645c2 0 4 0 6 1v1h2c1 0 3 1 4 1h1 0s-1 1-1 0h-6c0-1-1-1-2-1h-2c-1-1-2-1-2-2z" class="B"></path><path d="M208 655c4 1 8 1 12 2h-14s0-1 1-1h1v-1z" class="I"></path><path d="M46 430l2 2-1 2-2 8-1-1 2-11z" class="G"></path><path d="M230 699l7 1c1 0 2 0 2 1h-3-7c-1-1-2-1-3-1h0 5 0-5l4-1z" class="p"></path><path d="M322 492h-5c-2-1-3 0-4-1-3 0-5-1-7-2h2l2 1h0 1c0-1 1-1 2 0h1 0 0c1 1 1 1 2 1h0c2 0 4 1 6 1h0z" class="U"></path><path d="M47 434c0 1 1 2 1 3s-1 4-2 6v1c-1-1-1-1-1-2l2-8zm285 231c1-1 1-2 1-3 0-2-1-4 0-5h1l2 1h-1v2l-1 1c0 1-1 3 0 5-1 0-1-1-1-1h-1z" class="E"></path><path d="M334 657l2 1h-1v2l-1 1v-4z" class="D"></path><path d="M107 583l6 4c1 1 2 1 3 2v1c-1 0-1 0-2-1l-1-1h0-1l3 3h0l-9-7 1-1z" class="B"></path><path d="M371 467l9-6 1 1-9 5h-1z" class="E"></path><path d="M339 657v1c-1 3-1 7 0 10l-1 1c-1-1-1-2-1-4 0-1 1-5 0-7 1 0 1 0 2-1z" class="B"></path><path d="M322 218h1c-3 2-6 4-10 4 0 0-1 0-1-1h1 1c2-1 5-2 8-3z" class="I"></path><path d="M417 711l10-1h0c-1 2-4 1-6 2l-3 1c-1-1-1-1-2-1l1-1z" class="S"></path><path d="M282 686h0l10-3h0l-1 1h1l-9 3h0v-1h-1z" class="H"></path><path d="M520 636c1-3 1-8 3-11h0l-1 11h-2z" class="I"></path><path d="M333 668h-1c-3 0-4 2-7 4v-1h0c1-1 3-4 4-5h3v-1h1v3z" class="K"></path><path d="M42 379v2h1c-2 4-5 7-7 11 0 0 0 1-1 1 0-2 2-4 2-5l5-9z" class="N"></path><path d="M389 223c1 0 1 0 1 1-1 3-3 5-6 7h0c-1 1-1 1-2 1 1-1 1-2 1-3h2c1-1 3-4 4-6z" class="H"></path><path d="M512 515c4 2 6 5 9 9h-1c-3-2-7-5-8-9z" class="D"></path><path d="M192 646l5 1h8l-1 1h1c-1 1-1 1-2 1s-3 0-4-1h0-1c-1 0-3-1-4-1h-2v-1z" class="K"></path><path d="M553 586h0c0 2 1 2 2 3 1 3 1 7 1 10-2-4-3-9-3-13z" class="D"></path><path d="M427 710l2-1c2 0 3 0 4 1-1 0-2 0-3 1-1 0-2 1-3 1h-3c-1 1-2 0-2 0h-1c2-1 5 0 6-2h0z" class="Z"></path><path d="M417 381c0 2 1 3 1 5h0v4c0 1 0 2-1 2h0c-1-1-1-1 0-2h-1c0-1 1-2 0-2v-1-1l-1-2h1 1v-3z" class="C"></path><path d="M339 487l9-1h4c-1 1-4 0-5 1h4c1-1 3 0 4 0-4 0-8 1-12 1l-4-1h0z" class="X"></path><path d="M281 471c2 3 4 5 6 6 1 1 1 2 2 3-1 0-2 0-3-1-2-1-3-4-5-5v-3z" class="H"></path><path d="M59 433h1l-3 11c-1 2-2 5-2 7h-1 0l5-18z" class="C"></path><path d="M403 365l2 2c3 2 5 5 6 8l-2-2v-1l-3-3v1c0 1 1 1 1 1 0 1 0 1 1 2v2l-3-6c-1-1-2-3-2-4z" class="E"></path><path d="M405 439l8-4 2 1-9 4c-1 0-1 0-1-1z" class="G"></path><path d="M376 239c0-1 2-1 3-2 0-1 1-2 1-2 1 0 1 1 1 2l1 1h0l5-2c-2 1-4 3-6 3-1 0-3 0-4 1l-1-1h0 0z" class="f"></path><path d="M197 640c7-1 14 0 21 0h0c-4 1-8 2-12 1h3v-1h-12 0z" class="B"></path><path d="M401 412v1 1c1-2 1-3 2-4l1 1-3 12c0 2 0 4-1 6v-2-2-2c1 0 1-1 1-2v-1-1s1 0 0-1h0c1-1 1-1 1-2v-2l-1 1-1-1v-2h1z" class="K"></path><path d="M402 284c2 2 2 4 3 6-1 0-2-1-2 0-2 0-3-1-5-1l2-2h0 1 0l-1-1 1-1s1 0 1-1z" class="N"></path><path d="M431 688h2s-1 0-1 1l-2 1-5 2v-1h-1c0-1 0-1-1-2h3l4-1h1z" class="F"></path><path d="M423 689h3 1c-1 1-2 1-2 2h-1c0-1 0-1-1-2z" class="R"></path><path d="M461 504c1 0 2 0 3 1h2l1 1c0 1 1 1 1 1h1c0-1-1-2 0-3 0 1 0 1 1 0 0 1 0 1 1 2h1c-1 1-2 2-2 3-3-1-6-3-9-5z" class="S"></path><path d="M378 674c-1-1-2-2-2-4h0c-2-4-1-10 1-14 0 2-1 3-1 4v9c1 2 2 3 3 5h-1z" class="K"></path><path d="M510 659l2-2h0c0 1 0 2-1 3-1 3-2 6-4 9-1 1-1 2-2 2h-1c1-2 2-3 3-4 0-2 2-3 2-5l1-3z" class="B"></path><path d="M184 682c3 1 7 2 10 3 2 0 4 0 6 1h-1-2v1c-2 0-4-1-5-2l-9-2 1-1z" class="E"></path><path d="M414 712c-7-1-13 0-19 0 2-1 6-1 8-1h14l-1 1c1 0 1 0 2 1h-2l-2-1z" class="L"></path><path d="M510 511c5 5 9 8 13 13h-2c-3-4-5-7-9-9l-3-2 1-2z" class="a"></path><path d="M47 493c2 2 0 8 1 11 0 4 0 8 1 12h-1l-1-23z" class="F"></path><path d="M64 473l2-25h0v3h1v-3 1l-2 23-1 1z" class="P"></path><path d="M404 376l1-1c2 4 5 8 7 13h-1c-1-1-1-2-1-3l-2-2v-1h-1s0 1 1 2v1 3c-2-4-2-8-4-12z" class="D"></path><path d="M167 676c5 2 11 5 17 6l-1 1-18-7h1 0 1z" class="C"></path><path d="M344 218l10-3h0c-5 3-11 5-17 7l-1 1-1-1 3-2c2-1 4-1 6-2z" class="J"></path><path d="M520 637v-1h2l-1 3v3h0c-1 2-2 4-2 6v3l-2-1 3-12v-1z" class="F"></path><path d="M520 637v2h1v3l-1-1v-3-1z" class="M"></path><path d="M520 637v-1h2l-1 3h-1v-2z" class="J"></path><path d="M77 569c0-1-1-2 0-3l10 17-1 1-6-9-3-6z" class="E"></path><path d="M457 488v-1c2 0 4 1 6 1 4 1 8 1 10 3h0-2c-2-1-4-2-6-2h-1c-1 0-2 0-3-1v1l10 4 2 2-9-4-7-3z" class="D"></path><path d="M380 461l6-3c3-2 5-5 9-6h0c-1 2-2 2-4 3l1 1h1l-12 6-1-1z" class="F"></path><path d="M188 652l20 3v1h-1c-1 0-1 1-1 1l-17-2c1-1 2-1 3-1 1 1 2 1 3 1s5 1 6 0c-1 0-4 0-6-1l-7-1v-1z" class="O"></path><path d="M273 465h1c1 1 2 3 4 4 1 0 2 1 3 2v3l-1-1v-1c0-1-2-2-2-3h-1c0 1 1 1 1 2-1 0-1 0-2-1h0c1 2 2 4 4 5 1 1 2 3 3 5l-4-4c-2-4-4-7-6-11z" class="B"></path><path d="M524 620c1 1 1 1 1 2l-2 16-1 5-1-1h0v-3l1-3 1-11c0-2 1-3 1-5z" class="P"></path><path d="M485 501c5 3 9 5 13 9v1l-1-1-6-4c-1-1-2-2-3-2s-2 0-2 1l-3-2 2-2z" class="O"></path><path d="M360 469h1c-5 4-9 8-15 10h0v-1h-1c5-2 11-5 15-9z" class="N"></path><path d="M504 609l1 1v1l-2 12s0 2-1 3c0 1-1 2-2 3h-1c2-7 5-13 5-20z" class="M"></path><path d="M372 467c-1 2-3 3-5 4-4 3-7 6-12 8 5-4 10-10 16-12h1zm3 222h2c-1 0-1-1-2-1s-1 0-2-1c-1 0-3-1-4-2s-2-2-4-3l1-1 1 1c2 1 4 2 6 4 1 1 2 1 3 1 3 1 5 2 7 4-3 0-5-1-8-2z" class="C"></path><path d="M489 651v-1l1 1v1l-1 2c1-1 1-2 2-2v-1-1s1 0 1-1c0 0 0-1 1-2v-1h1c-1-1 0-1 0-2s1-3 2-4h0c0-1 1-2 0-4l2-1-2 7-9 17v-1c0-1 1-3 2-4v-3z" class="K"></path><path d="M426 699v-1c1 0 2-1 3-1s1 0 2-1h0c1-1 2-1 3-2-1 0-1 0-1 1-1 0-2 0-2 1l-1-1h1c0-1 1-1 1-1h1c3-1 5-3 7-4h1 1c-2 1-4 2-6 4 1-1 2-2 3-2 1-1 3-2 4-3v1c-4 3-8 5-12 7-2 1-4 2-5 2z" class="U"></path><path d="M49 398c1-1 3-5 4-5v1c-1 1-2 2-2 3v1h-1v2l2-2 1-2c0-1 1-1 1-2h1c-2 3-5 6-5 9l-3 8c-1 1-2 3-3 5 1-2 1-3 1-5 2-2 2-4 3-6 0-2 1-3 1-5v-2z" class="C"></path><path d="M463 424v1l2 1-19 3v-2l6-1c1 0 2 0 3-1 2 0 6 0 8-1z" class="U"></path><path d="M411 454c1 0 2 0 3 1h0l-14 4-9 3v-1c4-2 9-4 13-5 3 0 5-1 7-2z" class="O"></path><path d="M405 439c0 1 0 1 1 1-5 2-9 4-13 6-2 1-6 3-7 3l19-10z" class="I"></path><path d="M216 700l13 1h7c1 0 3 0 4 1h-24c1-1 3-1 5-1h-3c-2 0-3 0-4-1h2zm-51-61c3 1 7 2 11 3 7 2 15 3 22 4l-1 1-5-1c-2-1-4-1-6-1h-1-1c1-1 1 0 1-1h-1-2l-2-1h-1-1s-1 0-2-1h-2c-1 0-1 0-2-1-1 0-2 0-3-1h-2-1l-1-1z" class="F"></path><path d="M329 681c6 7 13 11 21 15l-1 1c-8-4-14-9-20-16h0z" class="Y"></path><path d="M77 654l1-1c4 4 8 8 13 12l6 6c-8-4-14-11-20-17z" class="a"></path><path d="M533 632c0 1 0 1 1 1h0 0c-1 6-3 12-5 18-1 3-2 5-4 8 0-1 1-3 1-4l3-9c2-5 3-9 4-14z" class="Q"></path><path d="M324 664c1 1 1 2 0 2v2l-9 6c-2 1-3 2-5 3h0c0-1 0 0-1-1l9-5c1-1 3-2 4-3s2-2 2-4z" class="C"></path><path d="M545 624c0-1 1-2 1-3h0v3c-1 10-3 19-7 29 0-2 1-5 2-7l3-9c0-4 1-9 1-13z" class="Z"></path><path d="M517 580l2 19v-1h-1v-1l-1-2h0c-1 0-1-1-1-2v-5l1-1v-1 1c1-1 1-2 0-3v-1-1-2h0z" class="g"></path><path d="M517 587c1 3 1 7 1 10l-1-2h0c-1 0-1-1-1-2v-5l1-1z" class="J"></path><path d="M44 425l1-2h0c0 3-1 5-1 8l-5 20-1-1 4-16c1-3 2-6 2-9z" class="G"></path><path d="M508 539c3 2 5 8 6 11l2 2 1 2-1 1c0 1 0 2 1 2v4c-3-7-5-15-8-21l-1-1z" class="L"></path><path d="M514 550l2 2 1 2-1 1c-1-1-2-3-2-5z" class="k"></path><path d="M316 667c3-2 7-4 10-7 2-1 3-3 4-4-1 3-6 7-5 10 1 1 1 1 1 2l-1 1c-1 0-1-1-1-1v-2c1 0 1-1 0-2v-1c-2 1-4 4-6 5v-2c-1 1-1 1-2 1h0z" class="K"></path><path d="M338 225c5-2 10-3 14-4 3-1 6-4 9-4-1 1-2 2-3 2-5 2-10 5-16 6 0 1-1 1-2 1-1-1-1-1-2-1h0z" class="R"></path><path d="M580 503c1 4 3 8 4 12 1 1 2 3 2 5h-1v1l-7-16 2-2z" class="H"></path><path d="M106 646v-1l4 4c1 1 2 2 4 3h1c0 1 1 2 1 2 1 0 1 1 2 1l1 2h-1l1 1c1 1 3 2 4 3l3 3h1 0l1 1h0c-2-1-4-3-6-5-6-4-12-9-16-14z" class="C"></path><path d="M381 476v1c1 1 7-1 9-2h6c-1 1-2 1-3 1h-3-1v1h-2-1-1-1c-1 1-1 1-2 1h-1 0v1h-1c-1 0-2 0-2 1h-2 0c-1 1-3 1-4 0v-1l9-3z" class="D"></path><path d="M322 492h2 3 5l1 1h4v-1h-3 0 6 1c1 0 2 0 3-1h3c-1 1-1 1-2 1 2 0 5 1 7 0h2 4c-3 1-5 1-8 1-9 1-19 0-28-1h0z" class="E"></path><path d="M421 684c3 0 4-1 7-2 5-3 9-5 14-8h-1c-2 3-6 5-9 6h2c0-1 1-1 1-1h0c2-1 6-2 8-3-2 2-4 3-6 4l-14 5-2-1z" class="H"></path><path d="M367 671c0-1-1-1-1-1 0-4 2-8 4-11 1-2 3-3 4-5 2-2 5-7 6-7-3 5-8 11-9 17-1 2-1 4-1 6h0c0-2 0-3-1-4l1-1v-3c1-1 1-1 1-2 1-1 2-3 3-5-3 2-5 6-6 9v1c-1 2-1 4-1 6z" class="B"></path><path d="M309 676c1 1 1 0 1 1-2 1-5 3-7 4-4 1-8 2-11 3h-1l1-1h0c1-1 5-2 6-2 4-2 7-3 11-5z" class="D"></path><path d="M504 440c7 2 13 5 19 8 3 2 6 4 8 6-9-5-17-10-27-12v-2z" class="c"></path><path d="M55 394c2-3 4-8 7-11-2 5-6 10-8 14-3 5-4 9-6 14h-1l3-8c0-3 3-6 5-9zm424 264h1c-1 0-1 1-2 2h1l1-2h1c-1 2-2 3-3 4-3 3-5 6-8 9 0 0-1 1-2 1h-1l4-5 6-6c1-1 1-3 2-3z" class="J"></path><path d="M505 522h1c2 2 4 4 6 7 4 5 7 11 10 16 0 1 0 2-1 3-3-9-9-19-16-26z" class="X"></path><path d="M279 476l4 4c4 4 9 6 13 9l-1 1-2-1c-6-3-11-7-15-13h1z" class="E"></path><path d="M512 662v2c-1 2-3 5-3 8h0v1h0v1c1 0 1-1 1-1h0c0 2-1 3-2 5l-2 2c-1 0-1 0-1-1h1c-1-1-1-2 0-2 0-1 0-1-1-2l7-13z" class="H"></path><path d="M509 673h0v1c1 0 1-1 1-1h0c0 2-1 3-2 5l-2 2c-1 0-1 0-1-1h1l3-6z" class="N"></path><path d="M544 492c2 0 2 0 2 1 1 1 1 0 1 1 1 0 2 2 3 3v-1h0v-1h1l7 9-1 1c-1-1-1-1-2-1l-3-3c-2-4-5-6-8-9z" class="Z"></path><path d="M375 258v-1c1 0 1 0 3 1h2 1c1 0 2 1 3 1l1 1 1-1c1 1 1 1 2 1l1 1h3v1h-1 0-2v1h-1-1c-2 0-3-1-4-1-3-2-5-3-8-4z" class="D"></path><path d="M274 451c-1-3-1-5-1-8l1 1c1 5 2 10 5 14 0 2 1 4 2 5 2 4 6 6 9 9 1 0 4 2 5 3h-1l-1-1c-1 0-2-1-3-1 0 0 0-1-1-1h0c-1-1-1-1-2-1-1-2-2-2-4-4h0c-1-2-2-3-3-5l-1-1v-1c-1-2-2-3-3-5 0-1 0-1-1-2 0-1-1-2-1-2z" class="H"></path><path d="M44 441l1 1c0 1 0 1 1 2l-2 7-3 10s-1-1-1-2l1-1h-1c1-5 2-11 4-17z" class="O"></path><path d="M294 480h2c9 3 16 4 26 4h-2c-2 1-9 0-10 0h-3c2 1 5 1 7 1h1 3 6l-6 1c-8-1-16-2-24-6z" class="N"></path><path d="M549 600c-1-6-2-13-4-19l-3-9c-1-3-2-5-2-9v1l5 15 5 17v3c0 1-1 1-1 1z" class="I"></path><path d="M355 662v-1c1 0 1-1 2-2 0 0 0 1-1 1l1 3v6h-1v1l-1 1h0 0l1 4h-2c0-1 0-2-1-3-1-3 1-7 2-10z" class="J"></path><path d="M353 672c0-2 0-3 1-4 0 1 0 2 1 3l1 4h-2c0-1 0-2-1-3z" class="N"></path><path d="M505 679c0 1 0 1 1 1l2-2v1c-1 2-6 8-9 9-1 0-2 1-2 1l6-10c1 0 1 1 2 0z" class="J"></path><path d="M525 529v-1l8 10c1 1 1 2 1 3l1 2v2l-1-1v1c-2-4-5-7-7-11 0-2-1-3-2-5h0z" class="M"></path><path d="M349 468c1 0 1-1 2-1 3-3 8-6 11-8h2 1l-3 3h-2c0-1 1-1 1-1h0-1l-1 1-1 1c-1 1-2 1-3 2l-1 1-2 1c-1 1-1 0-1 1-2 1-5 2-7 4-1 2-4 3-6 4h-1c-1 0-1 1-2 1h-1c1-1 3-3 5-4 1 0 2-1 4-1 2-2 4-3 6-4z" class="K"></path><path d="M53 409v6 1l-3 9c-1 1-1 3-1 4l-1 1c0-2 0-3-1-5 1-5 4-11 6-16z" class="I"></path><path d="M49 429c0-5 1-10 4-13l-3 9c-1 1-1 3-1 4z" class="J"></path><path d="M356 670v-1h1c1 6 4 11 9 15-1 0-2-1-3-1h0c-1 0-1-1-2-1l-2-2c-1-1-2-3-3-5l-1-4h0 0l1-1z" class="O"></path><path d="M356 670c1 2 1 3 2 5s3 5 3 7l-2-2c-1-1-2-3-3-5l-1-4h0 0l1-1z" class="R"></path><path d="M441 459c10 0 20 1 29 2 2 1 4 1 6 1h-1c0 1-1 1-2 0h-3-2 1 1c0 1 1 1 2 1h1s1 0 2 1h-1l-14-3c-6-1-13-1-19-1v-1z" class="I"></path><path d="M93 568c-2-4-4-7-6-12 3 4 5 9 8 13 3 5 8 10 12 14l-1 1-1-1c-1-2-3-4-5-6-3-2-5-6-7-9z" class="D"></path><path d="M488 643c1-2 2-5 4-6l-1 1v1c-1 0-1 1-1 1v2c-1 4-4 8-6 11-1 2-2 4-3 5h-1l-1 2h-1c1-1 1-2 2-2h-1l9-15zM360 256c2-1 4 0 6 0s5 0 7 1h1c0-1 0-1 1-1h0 3 1c1 1 3 1 4 2 1 0 2 1 3 1l-1 1-1-1c-1 0-2-1-3-1h-1-2c-2-1-2-1-3-1v1h-1-6l-4 1v-1c-1-1-1-1-2-1l-2-1z" class="F"></path><path d="M366 256c2 0 5 0 7 1h-7v-1z" class="I"></path><path d="M63 336c1-2 2-3 3-4h1v-2c0 3 1 7 0 11v-6c-2 3-4 5-6 8h-1l-1 1v-1-2-1c0-1 3-4 4-4z" class="g"></path><path d="M59 340c0-1 3-4 4-4 0 2 0 3-2 5 0 1-1 1-1 2l-1 1v-1-2-1z" class="R"></path><path d="M359 680l2 2c1 0 1 1 2 1h0c1 0 2 1 3 1l5 3v1 1c1 0 2 1 3 2h0c-6-1-12-5-16-10l1-1z" class="N"></path><path d="M106 646c-3-2-5-5-7-8-3-4-6-7-9-11l1-1 19 22h0v1l-4-4v1z" class="U"></path><path d="M377 240c1-1 3-1 4-1v2h-1v3 1h1l-2 2v-1c-1 0-2 0-3-1l-2 1v-1l-1-1v-1h-1v-1c1 0 2-1 3-2h2z" class="i"></path><path d="M377 242h1s0 1-1 1l1 1-2 1-2 1v-1l-1-1v-1l4-1z" class="V"></path><path d="M377 243l1 1-2 1-2 1v-1c1-1 2-2 3-2z" class="b"></path><path d="M377 240c1-1 3-1 4-1v2h-1v-1c-1 1-2 1-3 2l-4 1h-1v-1c1 0 2-1 3-2h2z" class="m"></path><path d="M548 579l1-1v1h0v-2h1v4 4c1 4 3 8 2 12v5l-2-6-5-17c1 0 1 1 2 2v2c1 0 0 1 1 1v1c0 1 0 2 1 2 0 1-1 1 0 2v2 1c1 0 1 1 1 1v2c0 1 0 1 1 1v-1-2 1c0-2 0-4-1-5s0-1 0-1c0-1-1-3-1-4-1-1-1-2-1-3v-2h0z" class="D"></path><path d="M548 579l1-1v1h0v-2h1v4 4l-2-6z" class="I"></path><defs><linearGradient id="A" x1="329.565" y1="224.161" x2="332.801" y2="214.191" xlink:href="#B"><stop offset="0" stop-color="#484749"></stop><stop offset="1" stop-color="#5f5f5d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M337 215l7-1-10 5-8 4h-2c-2 0-2 1-4 1l9-5c2-1 5-2 8-4z"></path><path d="M437 680h1c-3 2-6 4-9 5-2 0-6 1-7 2h-2-1 0 0-2-1c-1 0-1 0-2 1-2 1-6-1-8 1-1 0-2-1-3 0-2 0-7 1-9 0 9-1 19-1 29-4l14-5z" class="K"></path><path d="M486 505c0-1 1-1 2-1s2 1 3 2l6 4v2h-1c3 3 7 6 10 10h-1l-10-9c-2-2-3-4-5-5-1-1-3-2-4-3z" class="J"></path><path d="M496 512c-2 0-4-4-5-5v-1l6 4v2h-1z" class="H"></path><path d="M422 712s1 1 2 0h3 1 2l2 1c-4 0-8 0-11 1-7 2-14 5-21 6 4-3 9-4 14-5l-1-1c0-1 0-1 1-2l2 1h2l3-1h1z" class="W"></path><path d="M421 712h1l-8 3-1-1c0-1 0-1 1-2l2 1h2l3-1z" class="C"></path><path d="M414 712l2 1c-2 1-2 1-3 1 0-1 0-1 1-2z" class="D"></path><path d="M280 467c1 2 4 5 6 6 1 1 3 2 4 3 2 2 4 2 6 4h-2l-7-3c-2-1-4-3-6-6-1-1-2-2-3-2v-2h2z" class="O"></path><path d="M343 468v1c2 0 4-2 5-2l1 1c-2 1-4 2-6 4-2 0-3 1-4 1-2 1-4 3-5 4-2 1-5 2-6 2s-1-1-2-1c1 0 3-1 3-1 3-1 5-2 7-4h0v-1c2 0 4-2 6-3 1 0 1 0 1-1z" class="g"></path><path d="M523 587v-2h0c0 1 1 2 1 3 1 3 1 7 1 10 0 5 1 10 0 14 0 3-1 6-1 8s-1 3-1 5h0l1-10c0-9 0-19-1-28z" class="Q"></path><path d="M495 596l1 2c0 2 0 7-1 9l1 4-1 5-1 4c-1-1-1-2-1-3 0 1 0 1-1 2v2-2h-1v2h-1c0-1 1-2 1-4l4-21z" class="R"></path><path d="M493 617l2-10 1 4-1 5-1 4c-1-1-1-2-1-3z" class="F"></path><path d="M128 665c1 0 2 1 2 1l12 8 3 1c3 2 6 4 10 5 1 0 2 1 3 1 2 1 3 2 5 3h1l2 1 1 1h2l1-1c3 1 6 2 10 3l-1 1v1l-8-2-1-1c-3-1-5-2-7-3-8-3-16-7-23-11-4-2-8-5-12-8h0z" class="E"></path><path d="M171 688c2-1 5 0 7 1h1v1l-8-2z" class="F"></path><path d="M302 486c3-1 6 0 9 1h7 17-3c1 1 2 1 4 1-3 1-5 1-8 1-9 0-17-1-26-3z" class="I"></path><path d="M512 537c1 3 2 6 4 8 4 8 7 15 9 23 0 2 1 5 1 7h-1c-1-3-1-7-3-11-1-5-4-10-6-15l-4-9c-1-1-1-2 0-3z" class="n"></path><path d="M386 702c10 0 18-1 28-3 4-1 6-3 10-3-12 6-26 8-39 8-3-1-6 0-9-1h12 0l-2-1z" class="Z"></path><path d="M53 409c1-7 7-14 10-20 0 2 0 4-1 6-1 3-3 6-5 9-1 4-2 8-4 11v-6z" class="F"></path><path d="M415 343c2 2 3 8 3 11l1 7v2h-1v-1l-1 1-1-1c1-2-2-7-2-9h0l2 3c0-1 0-1-1-2l1-1-1-3v-1l1-1-2-4 1-1z" class="H"></path><path d="M414 353h0l2 3c0-1 0-1-1-2l1-1c1 3 1 6 2 9l-1 1-1-1c1-2-2-7-2-9z" class="e"></path><path d="M381 476c6-2 14-5 21-6l-2 1c0 1 1 2 1 3l-5 1h-6c-2 1-8 3-9 2v-1z" class="M"></path><defs><linearGradient id="C" x1="440.947" y1="431.495" x2="418.389" y2="429.927" xlink:href="#B"><stop offset="0" stop-color="#77767a"></stop><stop offset="1" stop-color="#8b8c8b"></stop></linearGradient></defs><path fill="url(#C)" d="M418 433c8-3 15-5 23-6l1 1-16 5-7 2h-1v-2z"></path><path d="M499 468h2 0 1c0 1 0 1 1 1s1 1 2 1c2 1 3 1 5 1l11 7c4 3 8 5 12 9-1 0-1 0-2-1l-1-1s-1 0-1-1c-1 0-2-1-3-2l-1-1h-1s-1-1-2-1c0-1-1-1-1-1 0 1 0 1 1 2 1 0 1 0 2 1l1 1c1 1 3 2 3 3h1 1c0 1 1 1 2 2v1c-1 0-6-5-7-5-5-4-10-8-15-10-1-1-3-2-4-3-2-1-5-2-7-3z" class="K"></path><path d="M314 498l10 1c1 1 3 1 4 1 4 1 7 1 10 1h8 0c-2 1-4 0-6 0l-1 1c-1 0-5 0-6-1-1 0-3 1-3 0h-1c-1 1-3 0-3 1h-1 1c3 1 8 1 12 1 0 0 3-1 3 0h-4c-5 0-10 0-15-1-2-1-3-1-5-1l-1-1-1-1c-1 0-2 0-3-1h2z" class="D"></path><path d="M170 685h-1c-1 0-3-2-5-1v-1h-1l-1-1h0-1c0-1-1-1-1-1h-1l-3-2h-1c-1-1-3-1-4-2v-1c-2-1-3-2-5-3l-5-3c-2-1-3-2-4-3h0c8 5 17 10 26 14 5 2 11 4 17 6v1c-4-1-7-2-10-3z" class="H"></path><path d="M522 545l2 3c0 1 1 3 2 4v-1 1l1-1 2 6-1 1c1 1 1 2 1 3v2h0c0 1-1 2-1 2l-7-17c1-1 1-2 1-3z" class="G"></path><path d="M528 557v1c1 1 1 2 1 3v2l-1-1c-1-1 0-3 0-5z" class="O"></path><path d="M526 552v-1 1l1-1 2 6-1 1v-1c-1-2-1-4-2-5z" class="e"></path><path d="M358 219v1c-1 0-2 1-3 1l-6 3h1 0 0c-1 1-2 1-3 1-2 1-6 2-8 4h-1c-1 0-2 1-3 2-1 0-3 2-4 2v-1l-1 1v-1h-2l-1-1c1 0 1 0 1-1h0c3 0 6-2 9-3 1-1 2-1 3-1s2 0 2-1c6-1 11-4 16-6z" class="K"></path><path d="M138 661l-1-1h1c5 3 9 6 14 8 5 3 11 5 15 8h-1 0-1c-1 0-3-1-3-1-5-2-10-4-14-7l-10-7z" class="B"></path><path d="M107 594h1c1 1 4 5 5 5v-1-1l2 1h0l2 2c1 1 2 1 2 2 1 1 2 2 2 3l-1 1c1 0 1 0 2 1h1 0s1 0 1 1h1c1 0 1 0 2 1 0 2 3 2 3 4h1l2 1 6 4h1s1 0 1 1l7 3c1 0 2 0 2 1h1c1 1 3 1 4 1v1l-15-6c-11-6-24-15-33-25z" class="F"></path><path d="M287 477l7 3c8 4 16 5 24 6h-4s1 0 2 1h2-7c-3-1-6-2-9-1h-2c-4-2-7-4-11-6-1-1-1-2-2-3z" class="M"></path><path d="M533 654h1l-1 2c1 1 1 1 1 2v1l-3 5c-4 7-8 13-14 18l-3 3c5-6 10-12 14-19 2-4 3-8 5-12z" class="o"></path><path d="M533 656c1 1 1 1 1 2v1l-3 5c-1-3 1-5 2-8z" class="L"></path><path d="M515 657h1v3h0l1 1h0c-1 4-3 9-7 12h0s0 1-1 1v-1h0v-1h0c0-3 2-6 3-8v-2c0-1 1-3 2-4h0v3l1-2v-2z" class="g"></path><path d="M515 657h1v3h0l1 1-1 1-1-1-1 1h0c0 1 0 1-1 1l1-2 1-2v-2z" class="a"></path><path d="M515 659h0v2l-1 1h0c0 1 0 1-1 1l1-2 1-2z" class="h"></path><path d="M512 662c0-1 1-3 2-4h0v3l-1 2-4 9h0c0-3 2-6 3-8v-2z" class="C"></path><path d="M529 602c1 6 1 12 0 17 0 4 0 8-1 12-1 10-5 20-8 29l-1-1c3-8 6-16 8-24 1-9 2-20 2-29h0v-4z" class="X"></path><path d="M345 478h1v1h0c-5 3-11 5-17 6-2 0-3 1-5 0h-6-3-1c-2 0-5 0-7-1h3c1 0 8 1 10 0h2c7-1 15-3 23-6z" class="G"></path><path d="M356 698h0c3 0 5 1 7 1 7 2 15 3 23 3l2 1h0-12-5c-5-1-12-2-17-4h1c-1-1 0-1-1-1h-1 1 2 0z" class="Q"></path><path d="M274 451s1 1 1 2h0c0 1 0 1-1 2 1 0 1 1 1 2 1 1 1 0 1 1s0 1 1 2c0 2 2 5 4 7l4 4c0 1 1 1 1 2-2-1-5-4-6-6h-2v2c-2-1-3-3-4-4 0-3-1-5-2-7 0-1 0-2-1-2v-1l1-2 1 6c1-1 1-1 2-1l-2-5c0-1 1-2 1-2z" class="F"></path><path d="M273 459c1-1 1-1 2-1 2 3 3 6 5 9h-2l-2-3c-1-2-2-3-3-5z" class="N"></path><path d="M272 453l1 6c1 2 2 3 3 5l2 3v2c-2-1-3-3-4-4 0-3-1-5-2-7 0-1 0-2-1-2v-1l1-2z" class="R"></path><path d="M273 433s0 1 1 1c-1 3-1 6-2 8l1 1s0-1 1-1v-4 6h0l-1-1c0 3 0 5 1 8 0 0-1 1-1 2l2 5c-1 0-1 0-2 1l-1-6-1-2c-1-2-1-4-1-7 0-4 2-7 3-11z" class="G"></path><path d="M271 449c0-2 0-4 1-7l1 1c-2 2 0 4-2 6z" class="R"></path><path d="M273 443s0-1 1-1v-4 6h0l-1-1c0 3 0 5 1 8 0 0-1 1-1 2-1-2-1-3-2-4 2-2 0-4 2-6z" class="J"></path><path d="M362 458h1c1-1 2-1 4-2 1 0 3-2 4-3l1 1c-3 2-5 4-7 5h-1-2c-3 2-8 5-11 8-1 0-1 1-2 1l-1-1c-1 0-3 2-5 2v-1h0l15-9c1 0 2-1 4-1z" class="M"></path><path d="M419 361c1 7 1 14 0 21l-1 4c0-2-1-3-1-5s-1-4-1-6l1-1-1-7h0v-3-2l1 1 1-1v1h1v-2z" class="E"></path><path d="M418 362v1c1 2 1 5 0 7v-1l-1-6 1-1z" class="B"></path><path d="M416 367v-3-2l1 1 1 6v1h-1l-1-3z" class="I"></path><path d="M417 374c1 3 1 5 2 8l-1 4c0-2-1-3-1-5s-1-4-1-6l1-1z" class="M"></path><path d="M550 552c2 2 3 6 4 9 2 6 4 11 5 17 2 6 2 12 3 18 0 2 1 5 1 7-1-2-1-5-2-7l-2-15c-1-4-2-7-4-10l-7-19c1 0 1 1 2 1l3 9c0-1-1-5-1-6-1-1-1-2-2-3v-1z" class="a"></path><path d="M557 505l1-1c5 8 10 17 11 27-1-3-2-7-4-10s-4-6-6-8v-2c-2-2-3-5-4-7 1 0 1 0 2 1z" class="W"></path><path d="M555 504c1 0 1 0 2 1 1 2 4 5 4 7l-2-1c-2-2-3-5-4-7z" class="G"></path><path d="M559 511l2 1c2 3 3 6 4 9-2-3-4-6-6-8v-2z" class="h"></path><path d="M350 485h1c1-1 2-2 4-2 2-1 6-2 8-3 6-2 12-5 18-7v1l-9 5-10 4h1-1l-1 1c-3 1-7 1-11 1z" class="D"></path><path d="M337 673h0c1 1 2 1 2 2h1l1 1c0 1 0 1 1 1l3 3 1 1 1 1c1 0 1 1 2 1l1 1h0 1c-1-1-3-2-5-4v-1c2 2 5 5 7 5 0 0 0-1 1-1 3 2 6 3 9 5h0c1 1 1 1 1 2 1 0 1 1 2 1v1c-6-2-11-4-16-7h0l-2-2c-2-1-3-2-5-3 0-1-1-2-2-2h-1c-1-2-3-3-3-5z" class="K"></path><path d="M363 688h0c1 1 1 1 1 2l-4-2h0 3z" class="G"></path><path d="M354 683c3 2 6 3 9 5h-3 0c-3 0-5-2-7-4 0 0 0-1 1-1z" class="O"></path><path d="M443 679l2-1s1 0 2-1v1h0 0c0 1 0 2-1 2l-1 1h-1l-1 1h-1l-3 1-6 3c-1 0-1 1-2 1v1h-1c0-1 1-1 1-2l1-1c-1 1-2 1-3 1l-1 1h-1c-1 1-1 0-1 1h-1-3l-2 1h-1-1l1-1h1c1 0 1 0 1-1h-1 2c1-1 5-2 7-2 3-1 6-3 9-5h0 2 0c1 0 3-1 3-1z" class="P"></path><path d="M438 680h2l-9 5h-2c3-1 6-3 9-5h0z" class="D"></path><path d="M420 687h2c1-1 5-2 7-2h2l-8 3h-1l-2 1h-1-1l1-1h1c1 0 1 0 1-1h-1z" class="C"></path><path d="M401 706c3 0 7 1 9 0 4 0 7-2 10-2v1c-5 2-11 3-17 4-3 1-7 2-10 2-2-2-2-1-4-1l-5-1h6-3v-1h3-4l15-2z" class="W"></path><path d="M369 475c-4 3-7 5-12 5 4-3 9-5 13-8 1 0 2-1 3-1v-1c1 0 1-1 2-1l1-1c2-2 4-3 6-4 1-1 3-1 4-1 0 1-1 2-2 2-1 1-2 2-3 2h0l-4 3 1 1c-3 1-6 2-9 4z" class="C"></path><path d="M375 470c2-2 3-2 6-3l-4 3h-2z" class="D"></path><path d="M375 470h2l1 1c-3 1-6 2-9 4v-1c1-2 4-3 6-4z" class="H"></path><path d="M295 490l1-1c12 7 25 10 39 10 4 1 9 0 13 1-4 0-10 1-15 0-1-1-2 0-3 0-1-1-2-1-3-1v1h7c2 1 5 1 8 1 1 0 4-1 5 0h-1-8c-3 0-6 0-10-1-1 0-3 0-4-1l-10-1h-2l-8-2c0-1 0-1-1-2 0-1-2-1-2-1l-6-3z" class="F"></path><path d="M303 494c4 1 7 3 11 4h-2l-8-2c0-1 0-1-1-2z" class="B"></path><defs><linearGradient id="D" x1="402.171" y1="427.219" x2="405.298" y2="404.279" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#D)" d="M405 403v3c1-2 0-5 0-7l1-1v2c1 1 1 3 1 5-1 9-2 18-7 26h-1c0-1 1-1 1-2 1-2 1-4 1-6l3-12h0c1-2 1-5 1-8z"></path><path d="M379 674c4 5 8 6 14 8 7 1 16 0 23-1 3-1 6-2 8-3l1 1c-7 3-16 5-24 5-3 0-6 0-8-1-6-1-12-3-15-9h1z" class="B"></path><path d="M464 642c1 1 1 2 1 3-4 6-9 11-14 16-1 2-3 4-5 6-6 5-14 9-21 12l-1-1c3-1 6-3 9-4 13-8 24-18 31-32z" class="C"></path><path d="M347 491l-1-1 28-2c1 0 1 1 2 1 1 1 3 0 4 1h0 1l-23 2h-4-2c-2 1-5 0-7 0 1 0 1 0 2-1z" class="H"></path><path d="M490 514c3 1 6 5 8 7l3 4c1 1 2 2 2 3h1l2 3 3 4 1 1v-1-1c1 1 2 2 2 3h0c-1 1-1 2 0 3l4 9v3l-2-2c-1-3-3-9-6-11 0-1-1-2-2-3-3-5-6-9-10-13h1c3 3 6 6 8 10 2 2 3 4 4 6v-1c0-4-16-20-19-24z" class="D"></path><path d="M216 686c-5-1-10-3-15-3h-1v-1h-5c-2-1-3-1-4-1h-1c-1 0 0 0-1-1h-4c-1-1-2-1-2-1-1 0-1-1-2-1h0-2l-1-1h-2-1c0-1 0-1-1-1h-1l-1-1h0-2v-1h2c1 1 1 1 2 1h1 1c-1 0-1 0-2-1h-1l-1-1h-1c-1 0-1 0-2-1h-1v-1c6 3 11 5 17 6 10 4 21 5 32 7h0-3-2-2 1 2v1h1c1 0 2 0 2 1z" class="F"></path><defs><linearGradient id="E" x1="484.727" y1="506.227" x2="483.284" y2="515.173" xlink:href="#B"><stop offset="0" stop-color="#393a37"></stop><stop offset="1" stop-color="#646166"></stop></linearGradient></defs><path fill="url(#E)" d="M473 503v-1h1l2 1h0 1 0c1 1 3 2 4 3 3 2 6 5 9 8 3 4 19 20 19 24v1c-1-2-2-4-4-6-2-4-5-7-8-10h-1c-5-6-11-11-17-16-2-1-4-4-6-4z"></path><path d="M94 647l1-1c3 4 6 7 9 11l5 5c1 3 5 6 8 8l7 7c3 4 8 7 13 9v1c-4-1-8-4-11-6-12-9-23-22-32-34z" class="J"></path><path d="M517 557v1s0 1 1 1l1 1c1 2 2 4 2 6 2 5 3 10 4 15-1 1-1 1-1 3h0v3 1c0-1-1-2-1-3h0v2l-6-26v-4z" class="G"></path><path d="M346 679c-1-2-3-4-3-6-3-6-3-11 0-16 1-3 3-7 6-9h0c-4 6-8 12-6 21 0 2 1 6 3 7-1-4-5-11-2-16 0 3 0 6 1 8v1c1 2 2 6 4 8s4 4 5 6c-1 0-1 1-1 1-2 0-5-3-7-5z" class="h"></path><path d="M497 512c6 5 13 11 18 17v1c3 3 6 6 8 10h0c0 2 0 3 1 4v4l-2-3c-3-5-6-11-10-16-2-3-4-5-6-7-3-4-7-7-10-10h1z" class="B"></path><path d="M473 599h1c0 7 1 15 0 23 0 4-2 8-3 11l-6 12c0-1 0-2-1-3 1-2 3-4 4-7 3-7 5-15 5-23v-13zm-404-2l1 5h0l1 3c3 7 8 15 13 22 1 2 2 3 4 5 0 1 1 2 2 3l-1 1-1-1v1s-1-1-1-2c-1 0-5-6-5-6h-1 0c-2-2-3-5-4-7-3-5-5-10-7-16h0l-1-1v-1c0-1 0-2-1-3l1-3z" class="K"></path><path d="M421 449l19-4v1h2s1 0 1 1h9c-1 0-2 0-4 1-5 0-11 0-16 1-4 0-9 1-13 2-2 1-5 1-8 3h0c-2 1-4 2-7 2 2-3 3-2 6-3 3-2 8-4 11-4z" class="F"></path><path d="M421 449l19-4v1h2s1 0 1 1c-4 0-8 0-12 1-3 0-5 1-7 1h-2-1z" class="Z"></path><path d="M508 679c2-2 3-4 4-6 3-5 5-9 7-14l1 1c-3 9-9 17-14 24l-9 9c-3 3-6 5-9 7-2 2-5 4-7 5h-1 0c4-3 8-6 11-10l6-6s1-1 2-1c3-1 8-7 9-9z" class="O"></path><path d="M471 684c1-1 2-2 4-3 0 0 1 0 2-1-3 4-6 7-9 10s-7 5-11 7c-3 3-7 5-10 6h-2c9-7 18-12 26-20v1z" class="N"></path><path d="M471 684c1-1 2-2 4-3 0 0 1 0 2-1-3 4-6 7-9 10s-7 5-11 7c1-2 5-5 7-6 1-1 1-2 2-2 2-1 4-3 5-5z" class="g"></path><path d="M391 469l15-4c6-1 13-3 19-4 5-1 11-1 16-2v1l-8 1c-1 0-2 0-3 1s-2 0-2 1c1 0 1 0 2 1-13 1-26 2-38 6l-1-1z" class="F"></path><path d="M405 290v1c7 3 11 9 14 15 0 1 1 2 1 2h-1l-3-5h-1c-3-1-5-6-8-6h-1c-1-1 0 0-1 0h0 1v1c-2-1-5-2-7-2l-2-1 1-1s1-1 2 0v-2h-3c2-1 3-1 4-1h1l1-1c0-1 1 0 2 0z" class="Z"></path><path d="M405 290v1c-1 1-2 0-3 0l1-1c0-1 1 0 2 0z" class="K"></path><path d="M404 293c2 1 4 1 5 2v2l-3-1-2-3z" class="O"></path><path d="M400 292l4 1 2 3 3 1v-2c3 3 5 5 7 8h-1c-3-1-5-6-8-6h-1c-1-1 0 0-1 0h0 1v1c-2-1-5-2-7-2l-2-1 1-1s1-1 2 0v-2z" class="D"></path><path d="M400 292l4 1 2 3c-2 0-4-1-5-1h-1v-1-2zm-94-74c-3-1-6-1-9-2 3 0 7-1 10-1h16 12 2c-3 2-6 3-8 4-1 0 0-1 0-1h1c-1-1-1 0-1 0h-3-3-1c-2-1-2 0-3 0l-2 1v-1l-1-1c-3 0-6 1-9 1h-1z" class="M"></path><path d="M335 215h2c-3 2-6 3-8 4-1 0 0-1 0-1h1c-1-1-1 0-1 0h-3c2-1 4-1 6-2 1 0 2 0 3-1z" class="P"></path><path d="M523 534l4 7c5 8 9 17 11 25 4 11 6 22 7 32 1 9 2 17 1 26v-3h0c0 1-1 2-1 3 0-19-2-38-7-56-2-6-4-11-7-16-2-4-3-9-6-13-1-1-1-3-2-4v-1h0z" class="G"></path><path d="M489 651v3c-1 1-2 3-2 4l-1 2c-7 9-14 17-23 24-10 8-21 15-33 22-2 0-4 2-6 2v-1c4-1 9-4 12-6 9-5 16-10 23-16 1 0 2-1 3-2 6-4 11-10 16-16 4-5 7-10 11-16z" class="E"></path><path d="M573 510l2 3c0-1-1-2 0-2h0 1c1 2 3 5 4 7 4 10 9 22 9 33l-3-9-4-11c-2-4-3-7-5-11-1-2-2-5-4-7h0v-1-2z" class="J"></path><path d="M583 530c0 1 1 3 2 5 0 2 1 4 1 6v1l-4-11 1-1z" class="E"></path><path d="M573 510l2 3 3 6c2 3 3 7 5 11l-1 1c-2-4-3-7-5-11-1-2-2-5-4-7h0v-1-2z" class="K"></path><path d="M399 296c2 0 5 1 7 2l3 3h1c-1 0-1 0-2 1l-1 1c2 0 3 1 4 2h0l-6-3c-12-5-23-1-34 3h1-2c0 1-1 1-1 1h-1-1l3-3c5-3 10-4 14-5 6-1 9 0 15 1v-2h0v-1z" class="Z"></path><path d="M399 296c2 0 5 1 7 2l3 3h1c-1 0-1 0-2 1l-1 1-1-1c-2-1-4-2-7-3v-2h0v-1z" class="D"></path><path d="M407 300c1 0 1 1 2 1h1c-1 0-1 0-2 1l-1 1-1-1c0-1 0-1-1-2h-1 3z" class="B"></path><path d="M399 296c2 0 5 1 7 2l3 3c-1 0-1-1-2-1h-1c-2-1-3-1-5-3h-2 0v-1z" class="k"></path><path d="M43 422c1 1 1 2 1 3 0 3-1 6-2 9l-4 16 1 1-3 11h0c0 1 0 2-1 4 0-1 0-1-1-2l-1 3v-1l3-20 7-24z" class="B"></path><path d="M38 450l1 1-3 11h0c0 1 0 2-1 4 0-1 0-1-1-2v-3l4-11z" class="N"></path><path d="M34 461c1 1 1 2 1 3 0-1 0-1 1-2 0 1 0 2-1 4 0-1 0-1-1-2v-3z" class="G"></path><path d="M285 462c2 2 4 5 7 7l2 1 2 3 2 1 1-1h1 0c1 0 2 1 2 3 6 2 12 3 19 3h3v1c-10 2-20-1-29-5-1-1-4-3-5-3-3-3-7-5-9-9 2 1 4 4 6 5l-2-3v-1-2z" class="S"></path><path d="M300 473h0c1 0 2 1 2 3l-4-2 1-1h1z" class="E"></path><path d="M296 473c-1 0-2-1-3-2-1 0-2-1-4-2s-3-2-4-4l2 1c2 1 3 3 5 3l2 1 2 3z" class="k"></path><path d="M446 483h1c1-1 3 0 4 0l12 3c5 1 9 3 14 5s10 4 15 7c4 2 8 5 12 8 2 2 4 4 6 5l-1 2-6-6c-9-7-19-12-30-16-2-2-6-2-10-3-2 0-4-1-6-1v1c-1 0-3-1-4-1-2-2-5-3-7-4z" class="M"></path><path d="M371 688l4 1c3 1 5 2 8 2 4 0 10 2 14 1 3-1 6-1 9-1 3-1 6-1 8-2h1 0 2 0s1 0 1 1h3l-2 1-2 1c-5 1-9 1-14 1-3 1-7 1-10 1s-6-1-9-1h-5c-2 0-4 0-6-1l1-1h0c-1-1-2-2-3-2v-1z" class="D"></path><path d="M406 691h4 0c-1 1-3 1-4 1h-3c-2 1-3 1-4 1l-2-1c3-1 6-1 9-1z" class="E"></path><path d="M371 688l4 1c3 1 5 2 8 2 4 0 10 2 14 1l2 1c-4 0-8 0-13-1-4 0-8 0-12-1-1-1-2-2-3-2v-1z" class="S"></path><path d="M550 596l2 6 1 8v5h0v-2h-1v4 16c0 1 0 2-1 3v1c-1 3-3 21-5 23h0c4-19 6-41 3-60 0 0 1 0 1-1v-3z" class="J"></path><path d="M551 637v-10-9c0-2 0-4 1-5v4 16c0 1 0 2-1 3v1z" class="R"></path><path d="M389 432h1l1-1v1l2-2v3l-3 4-3 4c-3 3-6 6-9 8l-6 5-1-1h1v-2c0-1 1-3 2-3 3-3 6-6 8-10h0l1 1h1l2-5c0 1 0 1 1 1v-1h1s1-1 1-2z" class="Z"></path><path d="M386 434c0 1 0 1 1 1v-1h1l-3 6-1-1 2-5z" class="D"></path><path d="M374 448h2 1l-5 5v-2c0-1 1-3 2-3z" class="R"></path><path d="M383 439h1l1 1a30.44 30.44 0 0 1-8 8h-1c3-3 5-6 7-9z" class="E"></path><path d="M382 438l1 1c-2 3-4 6-7 9h-2c3-3 6-6 8-10h0z" class="N"></path><path d="M50 388c1-3 3-7 6-11 1-2 3-3 4-5 1-3 2-5 4-8-1 3-1 5-2 7s-3 5-5 8c-2 4-4 8-5 12-1 2-2 5-3 7v2c0 2-1 3-1 5-1 2-1 4-3 6 0-1 0-1 1-2h0-1c-1-1 0-3 0-4v-1-3l5-13z" class="R"></path><path d="M50 388c0 1 1 2 0 3 0 1-1 2-2 4l-3 10v-1-3l5-13z" class="I"></path><path d="M438 486c9 1 18 4 26 7v-2l9 4 7 4 3 1 2 1-2 2 3 2c1 1 3 2 4 3-3-1-4-3-7-4l-7-5c-1 0-2-1-2-1-2-1-3-1-4-2h-1c-1 0-1-1-2-1 0 0-1 0-2-1h0l-4-2h-2c-1 0-1 0-2-1h-2 1l-1 1c-1-1-1-1-2-1 1 1 2 2 3 2h1c1 1 4 2 5 2 0-1-1-1-2-1 0-1-1-1-1-2h0c2 1 3 2 4 3h0 1c2 2 4 2 6 4h1 1v3h0c-2-2-4-3-6-4h-1l-8-4h-1c-1-1-3-2-4-2-2-1-3-1-5-1h1 3 1v-1h1c-2-1-5-1-6-2h-2-1l-1-1h-3-1l-1-1z" class="B"></path><path d="M480 499l3 1-2 2-2-2 1-1z" class="X"></path><path d="M483 500l2 1-2 2-2-1 2-2z" class="G"></path><path d="M464 491l9 4 7 4-1 1-15-7v-2z" class="W"></path><path d="M393 361l3 2c2 0 3 0 5 2h0 1v-1c1 0 1 0 1 1s1 3 2 4l3 6c3 6 5 12 6 19v6c0-1 0-3-1-4 0-3-1-5-1-8-2-5-5-9-7-13l-1 1c-2-3-4-5-5-8l-1 2-3-2-1-2 1-2c-1-1-2-2-2-3z" class="b"></path><path d="M393 361l3 2c3 3 6 7 9 12l-1 1c-2-3-4-5-5-8l-1 2-3-2-1-2 1-2c-1-1-2-2-2-3z" class="F"></path><path d="M395 364c2 1 3 2 4 4l-1 2-3-2-1-2 1-2z" class="W"></path><path d="M376 304c8-4 17-3 25 0 1 0 2 1 2 1h1c4 2 8 6 10 10 2 3 4 5 5 8 2 5 4 10 5 15h-1l-4-12-1 1c-1-3-2-5-3-8-1-2-3-4-4-6l-6-3v-1l-1-1h2c-1-1-3-2-4-2-2-1-3-2-4-1h-1v-1c-1-1-3 0-5 0-5 0-10 0-15 1l-1-1z" class="L"></path><path d="M405 309l-1-1h2c3 2 7 5 8 8s4 6 5 10l-1 1c-1-3-2-5-3-8-1-2-3-4-4-6l-6-3v-1z" class="D"></path><path d="M474 464h1c-1-1-2-1-2-1h-1c-1 0-2 0-2-1h-1-1 2 3c1 1 2 1 2 0h1c5 1 11 2 16 4 1 0 6 2 7 2 2 1 5 2 7 3 1 1 3 2 4 3-1-1-2-1-3-1h0-2 0v1h0l-5-2-2-1c-2 0-3-1-4-2-1 1-2 1-3 1l-13-4c1 0 1-1 1-1l-5-1z" class="a"></path><path d="M498 471v-1l3 1-1 1-2-1z" class="I"></path><path d="M501 471c1 0 2 1 3 1h2 0v-1c1 1 3 2 4 3-1-1-2-1-3-1h0-2 0v1h0l-5-2 1-1z" class="k"></path><path d="M479 465c5 1 10 2 15 4-1 1-2 1-3 1l-13-4c1 0 1-1 1-1z" class="K"></path><path d="M498 510l1 1c2 0 4 2 6 4l10 9c1 1 2 3 3 4 2 1 5 4 5 6h0v1c1 1 1 3 2 4v1h-1-1c-2-4-5-7-8-10v-1c-5-6-12-12-18-17v-2l1 1v-1z" class="R"></path><path d="M518 528c2 1 5 4 5 6h0v1c1 1 1 3 2 4v1h-1l-1-2-1-1v-2h-1v-1c-1-1-1-1-1-2-1-1-1-2-2-3h-1l1-1z" class="C"></path><path d="M498 510l1 1s0 1 1 2l3 3a79.93 79.93 0 0 1 13 13l-1 1v-1c-5-6-12-12-18-17v-2l1 1v-1z" class="h"></path><path d="M516 593c0 1 0 2 1 2h0l1 2v1h1v1 16l-2 14v7c-1 4-2 8-2 12-1-1-1-4-1-5v-3c0-2 1-4 1-6v-7c1-3 1-5 1-8h-1 1v-9-3c0-1-1-1-1-2v-3-9c0 1 0 1 1 1v-1z" class="N"></path><path d="M517 616c1-3-1-9 1-12 0 5 0 10-1 15 0 3-1 6-1 9v6c0 2-1 6-2 9v-3c0-2 1-4 1-6v-7c1-3 1-5 1-8 1-1 1-2 1-3z" class="p"></path><path d="M516 593c0 1 0 2 1 2h0l1 2v1 6c-2 3 0 9-1 12 0 1 0 2-1 3h-1 1v-9-3c0-1-1-1-1-2v-3-9c0 1 0 1 1 1v-1z" class="I"></path><path d="M517 595h0l1 2v1 6c-2 3 0 9-1 12v-21z" class="M"></path><path d="M348 226h2 0-1 1 1v-1h2c0-1 0 0 1 0h1c1-1 2-2 3-4 1 0 2-1 4-1h0c1 0 2-1 3-1l-3 3-3 3h-2l-2 1-2 2h-1c-1 0-2 1-3 1-1 1-1 1-2 1h-1c-1 0-1 0-2 1h-1c-4 2-7 4-11 7v1c-1-1-3-3-3-4v-1-1h1l1-1v1c1 0 3-2 4-2 1-1 2-2 3-2h1c2-2 6-3 8-4v1h1z" class="B"></path><path d="M350 227c1 0 1 0 2-1s2 0 3 0l-2 2h-1v-1h-2zm7-2c0-1 1-2 1-3 1-1 3 0 4 0l-3 3h-2z" class="E"></path><path d="M331 233c1 0 3-2 4-2 1-1 2-2 3-2h1c2-2 6-3 8-4v1h1c-1 1-3 1-5 2s-5 3-8 4h-1c-1 1-2 2-3 2h0v-1z" class="D"></path><path d="M350 227h2v1c-1 0-2 1-3 1-1 1-1 1-2 1h-1c-1 0-1 0-2 1h-1c-4 2-7 4-11 7 1-3 6-4 7-6 3-2 8-4 11-5z" class="H"></path><defs><linearGradient id="F" x1="475.75" y1="442.817" x2="478.766" y2="432.949" xlink:href="#B"><stop offset="0" stop-color="#868685"></stop><stop offset="1" stop-color="#b0aeb1"></stop></linearGradient></defs><path fill="url(#F)" d="M448 435c3-1 7-1 11-1 13 0 27 1 39 4 2 1 4 1 6 2v2c-13-3-26-5-39-4 0-1-1-1-1-1-1 0-1-1-2-1s-5-1-6 0h-2c-1 0-2 0-3-1h-3z"></path><path d="M74 273c-1 3-1 5-2 8l-3 3-6 4c-4 3-23 14-28 14l14-8c5-3 9-7 13-10 4-4 8-7 12-11z" class="S"></path><path d="M534 504h1c1 1 4 4 5 4l2 2 9 12c1 1 2 2 2 3l7 13h0c-1-1-1-1-1-2h-1 0v2h1v2c0 1 4 9 5 11 2 6 2 12 4 18v4c1 1 1 2 1 3h0 0 0c0-1-1-1-1-2l-1-5c0-4-1-8-2-12-3-7-6-14-10-21-3-7-7-14-11-20-3-4-7-8-10-12z" class="L"></path><path d="M540 508l2 2 9 12c1 1 2 2 2 3l-1 1 1 2c-2-1-2-3-3-5-1-1-2-3-3-4-2-4-5-7-7-11z" class="K"></path><path d="M553 528l-1-2 1-1 7 13h0c-1-1-1-1-1-2h-1 0v2h1v2c-1-1-2-4-3-5-1-3-2-5-3-7z" class="P"></path><path d="M374 246l2-1c1 1 2 1 3 1v1c1 1 1 1 1 2v1 3 1l1-1 6-1h0c-1 1-1 1-2 1-1 1-1 1-3 1-1 1-2 1-3 2h-1-3 0c-1 0-1 0-1 1h-1c-2-1-5-1-7-1s-4-1-6 0h0l-1-1c-1 1-2 1-4 1l10-3v-1l1-1 5-1h2v-1h-4 0v-1h4v-1l1-1z" class="E"></path><path d="M378 250l2-1v1c-1 1-1 3-2 5-1 0-2-1-4-1l1-1 1-1v-1l1 1 1-1v-1z" class="V"></path><path d="M368 252h5l-1 1v1h1c-5 0-9 0-14 1-1 1-2 1-4 1l10-3 3-1z" class="o"></path><path d="M368 252h5l-1 1v1h-4v-2z" class="b"></path><path d="M371 250h5v1 1l-1 1-1 1h-1-1v-1l1-1h-5l-3 1v-1l1-1 5-1z" class="J"></path><path d="M376 252l-1 1-1 1h-1-1v-1l1-1h3z" class="c"></path><path d="M374 246l2-1c1 1 2 1 3 1v1c1 1 1 1 1 2l-2 1v1l-1 1-1-1v-1h-5 2v-1h-4 0v-1h4v-1l1-1z" class="b"></path><path d="M374 246l2-1c1 1 2 1 3 1v1c1 1 1 1 1 2l-2 1c0-1 0-1-1-2s-2 0-4 0v-1l1-1z" class="f"></path><path d="M380 436l2-4v3h1l-1 3h0c-2 4-5 7-8 10-1 0-2 2-2 3v2h-1c-1 1-3 3-4 3-2 1-3 1-4 2h-1l3-3h-2l-1-1 2-2c2 0 4-4 6-5l2-3c1-1 2-2 2-3v-1l-1 2-1-1h0c1-2 2-4 3-5h1l1-1v1h2 1z" class="H"></path><path d="M382 435h1l-1 3h0l-1-1 1-2z" class="E"></path><path d="M372 444c0 1 0 2-1 3h1 0c1-1 1-1 2-1-2 2-4 5-6 7l-1-1 1-1c0-1 0-2 1-3h1v-1l2-3z" class="d"></path><path d="M370 447v1h-1c-1 1-1 2-1 3l-1 1 1 1-3 2h-2l-1-1 2-2c2 0 4-4 6-5z" class="Q"></path><path d="M377 435v1h2 1c-1 3-3 5-4 7-1 1-1 2-2 3-1 0-1 0-2 1h0-1c1-1 1-2 1-3 1-1 2-2 2-3v-1l-1 2-1-1h0c1-2 2-4 3-5h1l1-1z" class="b"></path><defs><linearGradient id="G" x1="44.757" y1="421.584" x2="37.683" y2="421.167" xlink:href="#B"><stop offset="0" stop-color="#515152"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#G)" d="M45 401v3 1c0 1-1 3 0 4h1 0c-1 1-1 1-1 2 0 2 0 3-1 5 1-2 2-4 3-5h1l-5 11-7 24v-1-2c0-3 1-7 1-10 0-4 0-8 1-11h1c1-7 3-15 6-21z"></path><path d="M43 411h1v1l-1-1h0z" class="a"></path><defs><linearGradient id="H" x1="69.474" y1="358.547" x2="38.081" y2="367.383" xlink:href="#B"><stop offset="0" stop-color="#4e4e52"></stop><stop offset="1" stop-color="#6f6e6c"></stop></linearGradient></defs><path fill="url(#H)" d="M42 379c5-11 12-21 20-30 1-2 2-3 3-5 1 0 1-1 2-2 0 4 0 8-1 11v-3l-4 4-12 18c-3 3-5 6-7 9h-1v-2z"></path><path d="M393 711h-10c-9 0-18 0-27-3-7-2-16-6-20-13v-1c5 7 11 9 19 11 10 3 21 3 31 3h4-3v1h3-6l5 1c2 0 2-1 4 1z" class="Q"></path><path d="M389 710h-14c3-1 6-1 9-1l5 1z" class="L"></path><path d="M536 502c-1-2-4-4-5-6h0l10 8c3 4 7 8 10 12s5 7 7 11c2 2 3 4 5 7h-1v2h-1c1 1 1 1 1 2v1l-1-1h-1l-7-13c0-1-1-2-2-3l-9-12-2-2c-1 0-4-3-5-4h1 1v-2h-1z" class="W"></path><path d="M536 502c3 2 6 4 6 8l-2-2c-1 0-4-3-5-4h1 1v-2h-1z" class="O"></path><path d="M283 687h2c3-1 5-1 8-2l15-5h3l11-5c1 0 2-2 4-3h3c2 1 2 3 3 4l-1 1c-1-2-2-4-3-4l-1 1c-1 1 2 5 2 7h0 0c-2-2-2-4-3-7h-1v1c-1 1-3 2-4 3-10 5-22 9-33 11l-2 1-1-1h0l-1 1h-3-2c1 0 2 0 3-1v-1l-1 1c-2 0-4 1-6 0l8-2h0z" class="D"></path><path d="M504 547l2-2 1 1c5 11 8 22 10 34h0v2 1 1c1 1 1 2 0 3v-1 1l-1 1v5 1c-1 0-1 0-1-1v-1c-1-4-1-9-1-13-2-8-4-16-7-23-1-3-1-6-3-9z" class="G"></path><path d="M515 592c1-2 1-5 1-7h0v3 5 1c-1 0-1 0-1-1v-1z" class="O"></path><path d="M504 547l2-2 1 1v5c0 1 1 2 0 3 1 1 1 1 0 2-1-3-1-6-3-9z" class="X"></path><path d="M534 541c1 0 1 0 2 1v-1c4 7 7 14 10 22h1 0 0l1-1s1 1 1 2 0 1 1 2v1 2c-1 1 0 2 0 3 1 2 2 7 0 9v-4h-1v2h0v-1l-1 1h0c0-3-1-6-2-9-4-9-8-17-12-25v-1l1 1v-2l-1-2z" class="a"></path><path d="M546 563h1 0 0l1-1s1 1 1 2 0 1 1 2v1 2c-1 1 0 2 0 3 1 2 2 7 0 9v-4c0-1-1-3-1-5l-3-9zm-12-22c1 0 1 0 2 1 3 6 6 13 9 20 0 1 1 3 1 4h0c-1-1-1-2-2-2-1-2-1-4-2-6-2-5-5-10-7-15l-1-2z" class="P"></path><path d="M386 449c1 0 5-2 7-3l1 1-2 2c1 0 2 0 3 1l-6 3-5 2-10 6-9 6c-1 1-2 2-4 2h-1 0c0-2 2-3 3-4l12-9c4-3 7-6 11-7z" class="H"></path><path d="M384 455v-1c1-1 2-2 4-2l1 1-5 2z" class="R"></path><path d="M388 452l4-3c1 0 2 0 3 1l-6 3-1-1z" class="h"></path><path d="M334 219h1 4s1 0 1-1h4c-2 1-4 1-6 2l-3 2 1 1 1-1 1 1h0 1l-1 1h0v1h0c1 0 1 0 2 1-1 0-2 0-3 1-3 1-6 3-9 3h0c0 1 0 1-1 1l1 1h2v1h-1v-1s-1 1-2 0h0c-1 0-1 1-1 1h0c-2 0-3-2-5-3l1-1c-1-1-3-2-4-3v-1s1-1 2-1c2 0 2-1 4-1h2l8-4z" class="P"></path><path d="M333 222l1-1c1-1 2-1 4-1l-3 2h-2z" class="C"></path><path d="M329 226c-1 1-3 2-4 3-1 0-1 0-1-1v-1c1 0 2-1 3-1h2z" class="F"></path><path d="M328 230h-1c0-1 1-1 2-1s2-1 3-1v-1c2 0 4-1 6-2 1 0 1 0 2 1-1 0-2 0-3 1-3 1-6 3-9 3zm5-8h2l1 1c-2 1-4 2-5 2-1 1-2 1-2 1h-2l6-4z" class="a"></path><path d="M320 224c2 0 2-1 4-1h2c-2 1-3 3-5 3v1l5 5-1 1-3-4h0c-1-1-3-2-4-3v-1s1-1 2-1z" class="F"></path><path d="M389 315c4 1 11 2 13 4 3 2 5 4 7 7l1 1v1c2 2 4 6 5 9v3 1 1l-1-3-1 1-5-5c-1-1-2-2-4-3l1-1 1-1h0 1c-1-1-3-3-4-3h0c-1-2-3-4-4-5h-1c-2-2-5-3-7-3v-1h0v-1h1l-2-1c-1 0-1 0-1-1h0z" class="G"></path><path d="M392 317h4l-1 2-4-1v-1h1z" class="D"></path><path d="M396 317c3 1 5 2 7 5l6 4h0l1 1v1c-2-1-3-2-4-4-4-2-6-4-11-5l1-2z" class="C"></path><path d="M389 315c4 1 11 2 13 4 3 2 5 4 7 7h0l-6-4c-2-3-4-4-7-5h-4l-2-1c-1 0-1 0-1-1h0z" class="N"></path><path d="M399 322h1c4 2 8 5 11 10 1 1 2 4 4 5v3 1 1l-1-3-1 1-5-5c-1-1-2-2-4-3l1-1 1-1h0 1c-1-1-3-3-4-3h0c-1-2-3-4-4-5z" class="D"></path><path d="M408 335v-2h0c1 1 2 1 2 2l2 2c0 1 1 1 2 2l-1 1-5-5z" class="B"></path><path d="M397 292h3v2c-1-1-2 0-2 0l-1 1 2 1v1h0v2c-6-1-9-2-15-1-4 1-9 2-14 5l-3 3-1 1v1h0v-3l-3 3 2-3c6-8 12-11 22-12 1-1 5-1 6-1h4z" class="X"></path><path d="M366 307c1-1 0-1 1-1 3-5 11-8 16-9 1 0 1 0 1 1-4 1-9 2-14 5l-3 3-1 1z" class="D"></path><path d="M383 297c5-1 10-2 16 0h0v2c-6-1-9-2-15-1 0-1 0-1-1-1z" class="H"></path><path d="M397 292h3v2c-1-1-2 0-2 0l-1 1c-1 0-3-1-4-1-2-1-5 0-7 0l1-1h0c1-1 5-1 6-1h4z" class="E"></path><path d="M397 292h3v2c-1-1-2 0-2 0l-1-1c-1 0-2-1-4-1h4z" class="H"></path><path d="M365 305c6-8 12-11 22-12h0l-1 1c-3 1-6 1-9 2-4 2-8 6-11 9h0l-3 3 2-3z" class="D"></path><path d="M552 617v-4h1v2h0v3c0 4 2 8 2 12 0 2 0 4 1 6v2h0c-1 10-5 22-11 30 0 1-1 1-1 2l-1-1 1-1h0v-4l2-4h0c2-2 4-20 5-23v-1c1-1 1-2 1-3v-16z" class="I"></path><path d="M551 637v-1c0 6-1 12-2 18-1 3-1 6-2 8l-2 5v1c0 1-1 1-1 2l-1-1 1-1h0v-4l2-4h0c2-2 4-20 5-23z" class="E"></path><path d="M550 569c2 6 1 11 3 17 0 4 1 9 3 13 1 13 2 26 0 39h0 0v-2c-1-2-1-4-1-6 0-4-2-8-2-12v-3-5l-1-8v-5c1-4-1-8-2-12v-4c2-2 1-7 0-9 0-1-1-2 0-3z" class="J"></path><path d="M553 610h1c0 2 0 5-1 8v-3-5z" class="E"></path><path d="M552 597l2 13h-1l-1-8v-5z" class="C"></path><path d="M510 471h2l12 7h0l1-2 12 9c2 2 5 6 7 7 3 3 6 5 8 9l3 3c1 2 2 5 4 7v2l-1-1s-1 0-1-1c-4-3-7-8-11-12-4-5-9-8-13-12s-8-6-12-9l-11-7z" class="k"></path><path d="M551 502l1 1 2 2h0c-1-1-2-3-2-4h0l3 3c1 2 2 5 4 7v2l-1-1s-1 0-1-1c0-2-5-7-6-9z" class="B"></path><path d="M525 476l12 9c2 2 5 6 7 7 3 3 6 5 8 9h0c0 1 1 3 2 4h0l-2-2-1-1c-1 0-2-1-2-2-2-2-3-4-5-6-2-1-4-3-6-5-2-1-4-3-5-4l-9-7h0l1-2z" class="C"></path><defs><linearGradient id="I" x1="385.978" y1="477.467" x2="427.061" y2="446.685" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#I)" d="M401 460c5-2 11-3 17-4 8-2 15-3 23-3h0c-2 1-4 0-6 1h-2v1 1h1 2 0l-15 2c-13 3-26 6-39 11-1 0-2 1-4 2l-1-1 4-3h0c1 0 2-1 3-2 1 0 2-1 2-2l2-1c1 0 1-1 3-1v1l9-3 1 1z"></path><path d="M381 467h0l-1 2h2c-1 0-2 1-4 2l-1-1 4-3z" class="F"></path><path d="M391 461v1l9-3 1 1c-2 1-5 1-7 2-3 1-6 3-10 3 1 0 2-1 2-2l2-1c1 0 1-1 3-1z" class="E"></path><defs><linearGradient id="J" x1="484.659" y1="653.79" x2="492.116" y2="659.617" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#6e6d6d"></stop></linearGradient></defs><path fill="url(#J)" d="M502 626v3 3h0v4c0 1 0 3-1 4v-3l-1 2v-1c-1 2-1 3-2 5l-2 6c-2 5-5 10-7 14-4 6-7 12-12 17-1 1-2 1-2 1-2 1-3 2-4 3v-1c5-6 12-13 15-20 0-1 1-2 1-4l9-17 2-7c0-2 1-4 1-6h1c1-1 2-2 2-3z"></path><path d="M499 629h1l-1 7c-1 1-1 4-2 5v-1l-1 2h0 0l2-7c0-2 1-4 1-6z" class="h"></path><path d="M502 626v3 3h0v4c0 1 0 3-1 4v-3l-1 2v-1h0v-3 1h-1l1-7c1-1 2-2 2-3z" class="F"></path><path d="M502 632h0v4c0 1 0 3-1 4v-3l-1 2v-1h0l2-6z" class="e"></path><path d="M367 671c0-2 0-4 1-6v-1c1-3 3-7 6-9-1 2-2 4-3 5 0 1 0 1-1 2v3l-1 1c1 1 1 2 1 4h0c2 6 5 10 11 12 11 6 28 5 40 2l2 1c-10 3-20 3-29 4-7-1-13-2-19-6-5-3-7-7-8-12z" class="E"></path><defs><linearGradient id="K" x1="38.335" y1="474.397" x2="66.536" y2="493.311" xlink:href="#B"><stop offset="0" stop-color="#20211f"></stop><stop offset="1" stop-color="#383739"></stop></linearGradient></defs><path fill="url(#K)" d="M54 451h0 1c-3 16-5 32-5 47 1 7 2 14 2 20-2 0-2 0-3-2-1-4-1-8-1-12-1-3 1-9-1-11v-3c0-4 1-9 1-14l1-9c1-5 3-11 5-16z"></path><path d="M49 467c1 3 0 7 0 10l-1-1 1-9z" class="C"></path><path d="M48 476l1 1v6c-1 4-1 8-1 12 0 3 1 6 0 9-1-3 1-9-1-11v-3c0-4 1-9 1-14z" class="E"></path><defs><linearGradient id="L" x1="71.153" y1="603.782" x2="49.616" y2="629.234" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#49494a"></stop></linearGradient></defs><path fill="url(#L)" d="M44 580l4 14c5 17 12 34 22 48l5 6 3 4v1l-1 1c-18-19-30-46-34-72l1-2z"></path><defs><linearGradient id="M" x1="303.593" y1="646.25" x2="222.535" y2="703.674" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#M)" d="M316 667h0c1 0 1 0 2-1v2c-2 1-4 2-7 3l-10 4-14 5c-4 2-9 3-13 4-9 2-18 3-26 3h-23l-7-1h-2c0-1-1-1-2-1h-1v-1h-2-1 2 2 3 0c3 0 7 1 9 1 4 0 9 1 13 1 12-1 24-2 35-4 7-2 14-3 21-6 2 0 5-2 7-3l14-6z"></path><defs><linearGradient id="N" x1="564.034" y1="569.9" x2="574.944" y2="565.792" xlink:href="#B"><stop offset="0" stop-color="#6b6c6d"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#N)" d="M562 536v-2h1v2c6 9 10 21 12 32l1-1 2 7c0 1 0 1-1 2v1 1c0 6 0 13-1 20 0 1 0 1-1 2v-5c0-5 0-10-1-15-1-12-5-23-9-34l-1-2-3-6 1 1v-1c0-1 0-1-1-2h1z"></path><path d="M564 542l1 1v3l-1-2v-2z" class="W"></path><path d="M562 536v-2h1v2 3l1 1 1 1v2h0l-1-1c-1-2-1-4-2-6z" class="Q"></path><path d="M561 538l1 1v-1c0-1 0-1-1-2h1c1 2 1 4 2 6v2l-3-6z" class="L"></path><path d="M575 568l1-1 2 7c0 1 0 1-1 2v1 1l-2-10z" class="p"></path><path d="M200 686h0c4 0 8 1 11 2 1 0 2 0 3 1 9 1 19 2 28 2 7 0 14-1 21-1 3-1 7-2 10-2v1h2c2 1 4 0 6 0l1-1v1c-1 1-2 1-3 1h2 3l1-1h0l1 1c-5 1-11 2-15 2-25 3-50 1-74-5v-1h2 1z" class="H"></path><defs><linearGradient id="O" x1="347.849" y1="276.905" x2="341.063" y2="278.501" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#303132"></stop></linearGradient></defs><path fill="url(#O)" d="M344 289v-6c0-2 0-2-1-3v-3-3l-1-2v-2-1l-1-2h0v-2l-3-8c0-2-2-3-2-5h0c2 1 2 2 3 4v1l1 4h1c-1-1 0-2 0-3v-1c1 0 1 0 1-1 1 1 1 2 1 3s1 1 1 2c0 2 0 3 1 4l1 3c1 6 1 12 2 18 0 2 0 4-1 6s-1 5-1 7h1c-1 3-1 6-1 9l1 1h-1l-1 2h-1c0-1-1-1-1-2h0v-3-7-5 1c0-2 0-5 1-6z"></path><path d="M348 286c0 2 0 4-1 6v-2h0c0-1-1-1 0-2l1-2z" class="C"></path><path d="M344 289l1-1c0 4 0 17 1 20l1 1h-1l-1 2h-1c0-1-1-1-1-2h0v-3-7-5 1c0-2 0-5 1-6z" class="V"></path><path d="M343 294v1c0-2 0-5 1-6 0 3 0 16-1 17v-7-5z" class="P"></path><path d="M510 648c2-3 2-6 4-8v3c0 1 0 4 1 5 0 1-1 2-1 3v1s-1 0-1 1v1l-2 2-1 1-1 2h1l-1 3c0 2-2 3-2 5-1 1-2 2-3 4v-1 1c-1 1-1 1-1 2h-1l-1 1c-1 3-3 6-5 9-1 1-2 2-3 4-2 3-6 6-8 8h0l-1-1 3-3h1l1-2c1-1 0-1 1-1l1-1c0-1 1-1 1-1 0-1 0-2 1-2v-1h0l2-2v-1l3-4c0-1 1-2 1-2v-1l1-1v-1l2-3h0l-1-1c0-1 1-1 1-2v-1l1-2c1-2 2-5 3-7v-3-1-1h-1l1-2s1 0 1 1h0l1-1v1l1-2 1 1z" class="D"></path><g class="P"><path d="M509 659h1l-1 3c0 2-2 3-2 5-1 1-2 2-3 4v-1 1c-1 1-1 1-1 2h-1l-1 1c1-1 1-1 1-2 2-1 2-4 3-6 1-1 2-2 2-3v-1c1 0 1 0 1-1 1-1 1-1 1-2z"></path><path d="M510 648c2-3 2-6 4-8v3c0 1 0 4 1 5 0 1-1 2-1 3v1s-1 0-1 1v1l-2 2-1 1h-1c-1 1-2 3-2 5l-1 1v1h-1l-1-1 2-8h0v-3-1-1h-1l1-2s1 0 1 1h0l1-1v1l1-2 1 1z"></path></g><path d="M508 655h1c0-1 0-2 1-3 0-1 1-2 2-4h0 1l-1 2v1c-1 1-1 2-2 3v1s1 0 1 1l-1 1h-1c-1 1-2 3-2 5l-1 1v1h-1l3-9z" class="K"></path><path d="M509 647l1 1c-1 2-2 4-2 7l-3 9-1-1 2-8h0v-3-1-1h-1l1-2s1 0 1 1h0l1-1v1l1-2z" class="N"></path><path d="M506 648s1 0 1 1h0l1-1v1l-2 6h0v-3-1-1h-1l1-2z" class="M"></path><path d="M506 648s1 0 1 1l-1 3v-1-1h-1l1-2z" class="g"></path><path d="M377 305c5-1 10-1 15-1 2 0 4-1 5 0v1h1c1-1 2 0 4 1 1 0 3 1 4 2h-2l1 1v1l6 3c1 2 3 4 4 6l-1 1c-1-1-2-3-3-3-1-1-2-1-3-2-2-1-3-1-4-2s-1-1-2-1l-1-1s-1-1-2-1h-1c-1-1-2-1-2-1h-1-1c-1 0-3-1-4-1h-7-2l-1 1c-2 0-3 1-5 2h-1-2 0l-1-1h0l-1-2c-1 1-2 1-2 2v1l-1-2 2-1-1-2h1s1 0 1-1h2c1-1 3-1 4-1l1 1z" class="Z"></path><path d="M396 306l1-1c1 0 1 0 2 1h1 2c1 0 3 1 4 2h-2l1 1h0l-9-3z" class="H"></path><path d="M370 308c2 0 4 0 6-1s5-1 7-2c2 1 3 1 4 1-1 1-3 1-4 1h-4c-2 1-4 2-7 2l-1 1h0l-1-2z" class="n"></path><path d="M377 305c5-1 10-1 15-1 2 0 4-1 5 0v1h1c1-1 2 0 4 1h-2-1c-1-1-1-1-2-1l-1 1c-1 0-2-1-3-1h-10c-2 1-5 1-7 2s-4 1-6 1c-1 1-2 1-2 2v1l-1-2 2-1-1-2h1s1 0 1-1h2c1-1 3-1 4-1l1 1z" class="F"></path><path d="M372 305c1-1 3-1 4-1l1 1c-1 1-4 2-5 2l-1-1-2 2-1-2h1s1 0 1-1h2z" class="W"></path><defs><linearGradient id="P" x1="419.949" y1="462.726" x2="428.766" y2="429.082" xlink:href="#B"><stop offset="0" stop-color="#464648"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#P)" d="M395 452c10-5 21-8 32-12 7-2 14-4 21-5h3c1 1 2 1 3 1h2c1-1 5 0 6 0s1 1 2 1c0 0 1 0 1 1h-5c-7 1-14 1-20 2-5 1-11 3-16 4-5 2-11 3-16 5s-10 5-15 7h-1l-1-1c2-1 3-1 4-3h0z"></path><path d="M460 438c-1-1-1-1-2-1s-1 0-2-1h0 6c1 0 1 1 2 1 0 0 1 0 1 1h-5z" class="Z"></path><path d="M346 260h1c0 2 0 4 1 6h0c1 3 2 5 2 8 1 3 2 6 2 9h1v1h-1l-1 3c0 2 0 4-1 7h1l-1 4h0c0 2 0 4 1 6v3s0 1-1 1v3h1c1-1 1-1 1-3h0 1v3h1v1l1 1c-1 0-1 1-2 1h0-1c0 2-2 3-2 4-1-1-1-2-1-3h0l-2-1c0-1 0-1-1-2s0-2 0-3h1l-1-1c0-3 0-6 1-9h-1c0-2 0-5 1-7s1-4 1-6c-1-6-1-12-2-18l-1-3v-2-3h1z" class="I"></path><path d="M349 305h-1v-2c1-2 1-4 2-5 0 2 0 4 1 6v3s0 1-1 1l-1-3h0z" class="c"></path><path d="M347 299c1 2 0 8 2 9v-3h0l1 3v3h1c1-1 1-1 1-3h0 1v3h1v1l1 1c-1 0-1 1-2 1h0-1c0 2-2 3-2 4-1-1-1-2-1-3h0l-2-1c0-1 0-1-1-2s0-2 0-3h1l-1-1c0-3 0-6 1-9z" class="f"></path><path d="M354 311v1l1 1c-1 0-1 1-2 1h0-1c0-1 1-1 1-3h1z" class="l"></path><path d="M346 260h1c0 2 0 4 1 6h0c1 3 2 5 2 8 1 3 2 6 2 9h1v1h-1l-1 3c0 2 0 4-1 7l-2 6c0-4 1-8 1-11 0-8-1-15-3-22v1l-1-3v-2-3h1z" class="e"></path><path d="M345 265v-2-3h1v7 1l-1-3z" class="C"></path><path d="M348 266h0c1 3 2 5 2 8 1 3 2 6 2 9h1v1h-1l-1 3c0-7-2-14-3-21z" class="H"></path><defs><linearGradient id="Q" x1="500.618" y1="486.65" x2="560.83" y2="505.245" xlink:href="#B"><stop offset="0" stop-color="#68696a"></stop><stop offset="1" stop-color="#8b8989"></stop></linearGradient></defs><path fill="url(#Q)" d="M505 474h0v-1h0 2 0c1 0 2 0 3 1 5 2 10 6 15 10 1 0 6 5 7 5 10 8 19 17 25 28v3h-2c-1 0-1-2-2-3l-6-8c-2-2-3-4-5-6-2-3-5-5-7-7-10-9-20-15-30-22z"></path><defs><linearGradient id="R" x1="77.327" y1="610.037" x2="59.594" y2="629.955" xlink:href="#B"><stop offset="0" stop-color="#1a1919"></stop><stop offset="1" stop-color="#484849"></stop></linearGradient></defs><path fill="url(#R)" d="M50 585l9 24c6 12 13 24 21 35l9 11c3 3 6 6 8 9 1 1 3 2 3 4l-8-8c-4-4-9-8-13-13-14-16-25-39-30-60l1-2z"></path><path d="M73 471c1 1 1 3 1 4 0 12 2 25 5 37 4 14 11 28 20 40 9 11 20 22 31 31 2 1 3 3 5 4s6 3 7 6l-6-5c-6-4-12-9-17-13-20-18-36-38-42-64-1-3-2-6-2-9-1-4-1-9-2-13v-12c0-1-1-4 0-6 1 2 0 8 0 9 1-2 0-6 0-9z" class="B"></path><path d="M492 565l3-4c2 3 2 6 2 9 3 18 2 37-1 55-1 4-2 8-4 12 0 2-1 4-2 5v-2s0-1 1-1v-1l1-1c-2 1-3 4-4 6l2-5c0-2 1-4 2-5 1-2 1-3 1-4l-1 1v-1l-1 1h0l3-10 1-4 1-5-1-4c1-2 1-7 1-9l-1-2v-1c0-8 0-15-1-22 0-3 0-6-2-8z" class="E"></path><path d="M495 595c1-2 1-3 1-4v2c1 1 0 2 0 3v2l-1-2v-1z" class="M"></path><path d="M496 611v-1h1c0 2-1 4-1 6h-1l1-5z" class="U"></path><path d="M493 629l1-4c0 2 0 4-1 6 0 1-1 2-1 3v1c0 1-1 2-2 3 0-2 1-4 2-5 1-2 1-3 1-4z" class="C"></path><path d="M495 616h1l-1 7c-1 0-1 2-1 2l-1 4-1 1v-1l-1 1h0l3-10 1-4z" class="e"></path><path d="M496 596l1-2v1 1 9 5h-1v1l-1-4c1-2 1-7 1-9v-2z" class="B"></path><defs><linearGradient id="S" x1="541.146" y1="594.874" x2="521.397" y2="602.664" xlink:href="#B"><stop offset="0" stop-color="#686867"></stop><stop offset="1" stop-color="#8c8c8f"></stop></linearGradient></defs><path fill="url(#S)" d="M529 557l2 6c1 1 1 4 2 5v1l4 23c1 2 1 6 1 8v6h0c0 1 1 4 0 5v-5h-1c0 7 0 15-2 23 0 1 0 3-1 4h0 0c-1 0-1 0-1-1 0-2 0-5 1-8v-15c0-15-3-29-6-44 0 0 1-1 1-2h0v-2c0-1 0-2-1-3l1-1z"></path><path d="M529 557l2 6c1 1 1 4 2 5v1l-1 1-2-6-1-1h0v-2c0-1 0-2-1-3l1-1z" class="U"></path><path d="M529 561c1 1 1 2 1 3l-1-1h0v-2z" class="g"></path><path d="M532 570l1-1 4 23c1 2 1 6 1 8v6h0c0 1 1 4 0 5v-5h-1c0-7-1-14-2-21l-3-15z" class="p"></path><path d="M329 469c4-1 9-2 12-2 1 1 2 1 2 1h0c0 1 0 1-1 1-2 1-4 3-6 3v1h0c-2 2-4 3-7 4 0 0-2 1-3 1 1 0 1 1 2 1-1 0-3 1-4 1v-1h-3c-7 0-13-1-19-3 0-2-1-3-2-3v-1-1h2c1 1 2 1 3 1 1 1 2 1 4 1h0 2l1-2 3 1h8l-1-1h-3c-1-1-2-1-3-2h5c3 0 5 1 8 0z" class="D"></path><path d="M312 471l3 1c1 1 2 1 3 2-3 0-5-1-7-1l1-2z" class="L"></path><path d="M332 471c0 1 0 1 1 1h3v1c-1 0-2 1-4 0h-3 0c1 0 2-1 3-2z" class="n"></path><path d="M315 472h8c2 1 3 1 6 1h0 0c-3 1-8 2-11 1-1-1-2-1-3-2z" class="W"></path><defs><linearGradient id="T" x1="334.125" y1="471.183" x2="330.959" y2="467.032" xlink:href="#B"><stop offset="0" stop-color="#837f83"></stop><stop offset="1" stop-color="#8c8f8b"></stop></linearGradient></defs><path fill="url(#T)" d="M329 469c4-1 9-2 12-2 1 1 2 1 2 1h0c0 1 0 1-1 1-2 1-4 3-6 3h-3c-1 0-1 0-1-1-1 1-2 2-3 2h0c-3 0-4 0-6-1l-1-1h-3c-1-1-2-1-3-2h5c3 0 5 1 8 0z"></path><path d="M333 472l3-1h1c1-1 3-2 5-2h0c-2 1-4 3-6 3h-3z" class="W"></path><path d="M316 469h5c2 2 4 1 6 1v1c-1 0-1 0-1 1 2 0 4 0 6-1-1 1-2 2-3 2h0c-3 0-4 0-6-1l-1-1h-3c-1-1-2-1-3-2z" class="S"></path><path d="M300 471h2c1 1 2 1 3 1 1 1 2 1 4 1 1 1 2 2 4 2s3 0 5 1c0-1 1-1 2-1h1-1v1c2 0 4-1 6 0 2 0 3-1 5-1 0 1 0 1-1 1l-1 1s-2 1-3 1c1 0 1 1 2 1-1 0-3 1-4 1v-1h-3c-7 0-13-1-19-3 0-2-1-3-2-3v-1-1z" class="B"></path><path d="M321 479l5-1c1 0 1 1 2 1-1 0-3 1-4 1v-1h-3z" class="O"></path><path d="M469 667h1 0 1l-4 5h1c-2 4-5 7-8 10-6 5-12 9-18 13-7 4-14 7-22 10v-1c-3 0-6 2-10 2-2 1-6 0-9 0 4-1 7-2 11-2 4-1 10-3 14-5 1 0 3-1 5-2 4-2 8-4 12-7v-1l2-1h1l1-1 1-1 1-1c1-1 2-2 3-2 2-1 3-3 5-4-3 1-6 4-9 5l-2 1c-1 1-2 2-3 2h-1c2-1 4-2 6-4l9-6c4-3 8-7 12-10z" class="G"></path><path d="M469 667h1 0 1l-4 5a30.44 30.44 0 0 1-8 8c-1 1-2 3-3 3 2-3 4-5 7-8h0v-1c-3 3-6 5-8 8-4 3-8 6-12 8v-1l2-1h1l1-1 1-1 1-1c1-1 2-2 3-2 2-1 3-3 5-4-3 1-6 4-9 5l-2 1c-1 1-2 2-3 2h-1c2-1 4-2 6-4l9-6c4-3 8-7 12-10z" class="D"></path><path d="M283 410c0 1 0 2-1 3h1c1-1 2-2 2-3 0 2 0 2-1 3s-1 3-2 4l-2 7v1h1 0v2l-1 2v2c-2 5-2 10-1 15 0 2 0 4 1 6l5 10v2 1l2 3c-2-1-4-4-6-5-1-1-2-3-2-5-3-4-4-9-5-14h0v-6 4c-1 0-1 1-1 1l-1-1c1-2 1-5 2-8-1 0-1-1-1-1 1-6 2-13 5-18 1-1 2-3 3-5l1 1 1-1z" class="Z"></path><path d="M277 428h1c0 1-1 2-1 3-1 0-1-1-1-1l1-2z" class="d"></path><path d="M274 438l2-8s0 1 1 1c0 2-1 5-3 7z" class="o"></path><path d="M277 428c0-4 1-7 3-11 0-1 1-3 2-4h1c-2 5-4 10-5 15h-1z" class="c"></path><path d="M280 424v1h1 0v2l-1 2v2c-2 5-2 10-1 15 0 2 0 4 1 6v1l-3-6v-4c-1-1-1-1-1-2 0-3 1-7 2-10 1-2 1-4 2-7z" class="d"></path><path d="M283 410c0 1 0 2-1 3s-2 3-2 4c-2 4-3 7-3 11l-1 2-2 8h0v4c-1 0-1 1-1 1l-1-1c1-2 1-5 2-8-1 0-1-1-1-1 1-6 2-13 5-18 1-1 2-3 3-5l1 1 1-1z" class="H"></path><path d="M278 415c1-1 2-3 3-5l1 1c-5 7-7 15-8 23-1 0-1-1-1-1 1-6 2-13 5-18z" class="b"></path><defs><linearGradient id="U" x1="305.534" y1="460.534" x2="273.626" y2="441.31" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#3c3c3d"></stop></linearGradient></defs><path fill="url(#U)" d="M283 425v-1c1 2 1 3 1 5h0c0 2-1 4-1 6 0 1 1 3 1 4 0 3 0 6 2 8v2c1 3 2 5 3 7l1 2v2s2 1 2 2c2 2 4 4 7 6l2 1 2 1-1 1h-2v1 1h0-1l-1 1-2-1-2-3-2-1c-3-2-5-5-7-7l-5-10c-1-2-1-4-1-6-1-5-1-10 1-15v-2l1-2v-2h2z"></path><path d="M281 427c0 1 0 1 1 2l-1 3c0-2 0-2-1-3l1-2zm6 31l3 2s2 1 2 2h-1 0l-1-1v1c-2-1-2-2-3-4z" class="a"></path><path d="M286 455s1 0 1 1h1 1 0l1 2v2l-3-2-1-3z" class="X"></path><path d="M294 470c2 0 4 1 6 3h-1l-1 1-2-1-2-3z" class="M"></path><path d="M297 468h2l2 1 2 1-1 1h-2l-3-3z" class="C"></path><path d="M290 462v-1l1 1h0 1c2 2 4 4 7 6h-2c-3-2-5-4-7-6z" class="E"></path><path d="M279 446c-1-5-1-10 1-15 0 3 1 8-1 11v2 2z" class="M"></path><path d="M284 439c0 3 0 6 2 8v2c1 3 2 5 3 7h0-1-1c0-1-1-1-1-1-2-3-3-6-4-10 2-1 0-4 2-6z" class="W"></path><path d="M283 425v-1c1 2 1 3 1 5h0c0 2-1 4-1 6 0 1 1 3 1 4-2 2 0 5-2 6h0c-1-4-1-9-1-13l1-3c-1-1-1-1-1-2v-2h2z" class="d"></path><path d="M281 425h2l-1 4c-1-1-1-1-1-2v-2z" class="J"></path><path d="M166 640l-1 1h-1v1h2l1 1h2c1 1 2 1 3 1 1 1 2 0 3 1h3l1 1h1 1 0 1c1 1 2 0 3 1h3l8 2h2 0v1c-16-3-32-7-48-13-5-2-11-4-17-6l-15-9c-7-5-15-10-21-17-7-7-14-15-19-24l-3-6c-1-1-3-3-3-5l2 4c2 4 6 9 9 13 4 6 8 11 14 15 3 3 6 5 9 7 8 7 17 13 27 18 7 4 15 6 23 9l9 3 1 1z" class="R"></path><path d="M399 368c1 3 3 5 5 8 2 4 2 8 4 12v1c1 5 0 10-1 16 0-2 0-4-1-5v-2l-1 1c0 2 1 5 0 7v-3c0 3 0 6-1 8h0l-1-1c-1 1-1 2-2 4v-1-1-6c0-3-1-6-2-9l-2-8-1-2h1c0-1 1-1 1-2h1v-1l1-1-2-5c0 1 1 1 1 2h0v-3h0c0-1-1-2-1-2v-1h0c-2-2-3-4-3-6l3 2 1-2z" class="I"></path><path d="M403 394h2v1 1 5h-1v-3c0-1 0-3-1-4z" class="O"></path><path d="M399 384l1-1 1 1h0 1l3 10h-2c0-3-1-5-2-8-1 0-1-1-1-2l-1 1h0v-1z" class="Z"></path><path d="M399 385l1-1c0 1 0 2 1 2 1 3 2 5 2 8 1 1 1 3 1 4v3h-1v-4l-1-3-1-4-1-2c0-1 0-2-1-3z" class="E"></path><path d="M403 397v4h1v-3 3s0 2 1 2c0 3 0 6-1 8h0l-1-1c-1 1-1 2-2 4v-1-1-6c0 1 0 0 1 0v-1-1c1-3 1-5 1-7z" class="M"></path><path d="M399 385h0c1 1 1 2 1 3l1 2 1 4 1 3c0 2 0 4-1 7v1 1c-1 0-1 1-1 0 0-3-1-6-2-9l-2-8-1-2h1c0-1 1-1 1-2h1z" class="o"></path><path d="M401 390l1 4c-1 2-1 3-1 5 0-1-1-4 0-6v-3z" class="W"></path><path d="M402 394l1 3c0 2 0 4-1 7v1c0-2 0-4-1-6 0-2 0-3 1-5z" class="L"></path><path d="M399 385h0c1 1 1 2 1 3v8l-1 1-2-8-1-2h1c0-1 1-1 1-2h1z" class="d"></path><path d="M399 368c1 3 3 5 5 8 2 4 2 8 4 12v1c0 1 0 2-1 3 0-1 0-2-1-2l-1 1v-1s-1-1-1-2 0-1-1-2v-2h-1-1 0l-1-1-2-5c0 1 1 1 1 2h0v-3h0c0-1-1-2-1-2v-1h0c-2-2-3-4-3-6l3 2 1-2z" class="G"></path><path d="M399 377l2 7h0l-1-1-2-5c0 1 1 1 1 2h0v-3h0zm-4-9l3 2c1 3 3 5 3 8-1-1-1-3-2-4h-1 0c-2-2-3-4-3-6z" class="b"></path><path d="M372 311h0 2 1c2-1 3-2 5-2l1-1h2 7c1 0 3 1 4 1h1 1s1 0 2 1h1c1 0 2 1 2 1l1 1c1 0 1 0 2 1s2 1 4 2c1 1 2 1 3 2 1 0 2 2 3 3l1-1 3 8c2 3 3 7 3 10l-3-7c-1 0-2 0-2-1-1 0-3-3-3-5-1-1-2-2-3-2 0-1-1-1-1-2-2 0-3-2-5-2l-2 1c-2-2-9-3-13-4-2-1-5-1-7-1-3 1-6 2-9 1h0-1 0v-1-3z" class="X"></path><path d="M413 324v-1c-1-1-1-1-1-2 2 3 5 6 6 9-1 0-2 0-2-1-1 0-3-3-3-5z" class="K"></path><path d="M379 312c2-1 4-2 6-2h5c2 1 4 1 6 1 2 1 3 2 4 2l1 1c-5-1-11-2-16-2h-6z" class="N"></path><path d="M379 312h6c5 0 11 1 16 2l2 2c3 0 4 2 7 3 0 0 2 1 2 2s0 1 1 2v1c-1-1-2-2-3-2 0-1-1-1-1-2-2 0-3-2-5-2l-2 1c-2-2-9-3-13-4-2-1-5-1-7-1-3 1-6 2-9 1h0-1c2-2 5-3 7-3z" class="C"></path><path d="M379 312h6c-1 0-3 1-5 1-1 0-2 1-3 1-1 1-2 0-3 0h-1v1h0-1c2-2 5-3 7-3z" class="H"></path><path d="M286 216c9 1 20 6 27 12 5 4 9 8 12 13 4 4 8 10 10 15 2 3 2 6 3 9 1 5 3 10 3 15 1 3 1 7 1 10 0 1 1 3 1 4v5c-1 0-1-1-1-2-1 1-1 2-1 3 1 2 1 3 1 5h-1v-3-1c-2-5-2-10-2-15-1-5-3-10-4-15v-1c-1-4-4-8-6-12l1-1-1-1h0c1 1 2 2 3 4l1 1h0v-1c-1-2-2-4-2-6l-3-6v-1l-7-10c-1-1-2-3-4-4h-1c-3-3-7-5-11-7-2-2-4-3-7-4l-7-3c-1-1-2 0-3-1s-1-1-2-1h0v-1z" class="h"></path><path d="M288 218c8 1 17 5 24 10 2 1 4 3 5 5h-1c-3-3-7-5-11-7-2-2-4-3-7-4l-7-3c-1-1-2 0-3-1z" class="e"></path><path d="M329 258l1-1-1-1h0c1 1 2 2 3 4l1 1h0v-1c-1-2-2-4-2-6l-3-6v-1c3 3 5 7 7 11 1 2 1 5 2 7 0 1 1 3 1 4 1 2 1 4 1 6 2 6 2 12 2 18v7h0c1 2 1 3 1 5h-1v-3-1c-2-5-2-10-2-15-1-5-3-10-4-15v-1c-1-4-4-8-6-12z" class="P"></path><path d="M338 269h-1v-1c-1 0-1-1-1-1v-2h1c0 1 1 3 1 4z" class="e"></path><path d="M373 315c3 1 6 0 9-1 2 0 5 0 7 1h0c0 1 0 1 1 1l2 1h-1v1h0v1c2 0 5 1 7 3h1c1 1 3 3 4 5h0c1 0 3 2 4 3h-1 0l-1 1c-4-1-6-3-10-3h-1c-4 0-8-1-12-1h-7c-1-1-2-1-3-1h-1l1-1h1c0-1 0-1 1-1v-1-1-1l-1-1v-1-4h0z" class="k"></path><path d="M372 326c1 0 2-1 2-1 2 0 4 1 5 1h10c2 0 4 1 6 2h0-1c-4 0-8-1-12-1h-7c-1-1-2-1-3-1z" class="B"></path><path d="M391 322l1-1c2 1 4 1 6 1h1c1 1 3 3 4 5h0c1 0 3 2 4 3h-1 0c-3-2-7-4-11-5h0c-3-1-6-1-10-1s-7 1-11 0v-1c3 1 5 1 8 0h0v-1h7 2z" class="C"></path><path d="M382 322h7 2c2 0 7 1 8 2h0-3c-3-2-10-1-14-1v-1z" class="G"></path><path d="M391 322l1-1c2 1 4 1 6 1h1c1 1 3 3 4 5h0c1 0 3 2 4 3h-1 0c-3-2-7-4-11-5l1-1h3 0c-1-1-6-2-8-2z" class="e"></path><path d="M373 315c3 1 6 0 9-1 2 0 5 0 7 1h0c0 1 0 1 1 1l2 1h-1v1h0v1c2 0 5 1 7 3-2 0-4 0-6-1l-1 1h-2-7v1h0c-3 1-5 1-8 0v-1-1l-1-1v-1-4h0z" class="g"></path><path d="M374 319v-1-1h5 9c-1 1-3 1-4 1h-6l-4 1z" class="C"></path><path d="M390 316l2 1h-1v1h0v1 1c-4 0-7-1-10-1-2 0-2 0-3-1h6c1 0 3 0 4-1l2-1z" class="k"></path><path d="M390 316l2 1h-1v1h0-7c1 0 3 0 4-1l2-1z" class="H"></path><path d="M374 319l4-1c1 1 1 1 3 1-1 1-2 1-2 2v1h3v1h0c-3 1-5 1-8 0v-1-1-1h0 1l-1-1z" class="O"></path><path d="M374 320c2 0 3 1 5 1v1h-5v-1-1z" class="C"></path><path d="M374 319l4-1c1 1 1 1 3 1-1 1-2 1-2 2-2 0-3-1-5-1h0 1l-1-1z" class="J"></path><path d="M381 319c3 0 6 1 10 1v-1c2 0 5 1 7 3-2 0-4 0-6-1l-1 1h-2-7-3v-1c0-1 1-1 2-2z" class="M"></path><defs><linearGradient id="V" x1="451.108" y1="590.909" x2="460.974" y2="676.791" xlink:href="#B"><stop offset="0" stop-color="#222424"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#V)" d="M478 590v-1-1l1 2v-1c4 8 4 19 4 28 0 2-1 4-1 7v1h1 0c-8 20-22 39-40 51-2 1-6 2-8 3h0s-1 0-1 1h-2c3-1 7-3 9-6h1l13-12c1-2 4-4 6-6l6-9 4-6c4-8 9-15 10-24v-1c-1 0-2 1-2 1l-1-1 3-2c1-5 0-12-2-17 0-2-1-4-1-7h0z"></path><path d="M471 641v2h0l-3 5-1-1 4-6z" class="M"></path><path d="M467 647l1 1-8 11h-1l-4 4v-1c1-2 4-4 6-6l6-9z" class="J"></path><defs><linearGradient id="W" x1="464.006" y1="463.359" x2="466.723" y2="447.974" xlink:href="#B"><stop offset="0" stop-color="#5f5f60"></stop><stop offset="1" stop-color="#918f90"></stop></linearGradient></defs><path fill="url(#W)" d="M452 447c3-1 6 0 9 0 3 1 7 0 10 1h3c2 1 5 1 7 2h0c13 3 26 6 39 13h-3c-1 0-4-1-4-1-1 0-1 0-1 1-1 0-2-1-3-2l-4-2c-5-1-10-2-15-4-25-7-51-6-76 0h0c-1-1-2-1-3-1h0c3-2 6-2 8-3 4-1 9-2 13-2 5-1 11-1 16-1 2-1 3-1 4-1z"></path><path d="M452 447c3-1 6 0 9 0 3 1 7 0 10 1h3c2 1 5 1 7 2h0c-3 0-6-1-8-1-1-1-2-1-4-1h-6-15c2-1 3-1 4-1z" class="M"></path><defs><linearGradient id="X" x1="415.395" y1="508.958" x2="428.223" y2="483.429" xlink:href="#B"><stop offset="0" stop-color="#414142"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#X)" d="M374 488c13-1 26-2 39-2l17 3c11 2 21 5 31 10 3 2 6 3 8 5h1c-1 1-1 1-1 0-1 1 0 2 0 3h-1s-1 0-1-1l-1-1h-2c-1-1-2-1-3-1-5-2-9-5-15-6-13-6-28-8-42-9-8 0-16 1-23 1h-1 0c-1-1-3 0-4-1-1 0-1-1-2-1z"></path><defs><linearGradient id="Y" x1="445.128" y1="481.703" x2="447.598" y2="469.92" xlink:href="#B"><stop offset="0" stop-color="#605f60"></stop><stop offset="1" stop-color="#949394"></stop></linearGradient></defs><path fill="url(#Y)" d="M402 470c22-3 45-2 66 4 9 2 17 6 25 10 0 1 0 1-1 2h-1l-1 1c-28-13-58-18-89-13 0-1-1-2-1-3l2-1z"></path><path d="M357 225h2l-7 6c0 1-1 2-1 2h2c-1 1-1 2-2 2-2 2-4 4-5 6 0 0 0 1-1 1v1c-1 0-1 1-2 1l-1 1 1 1h-1 1c1 0 1 1 1 1 1 1 2 0 2 2v1h1v2 1h0 2 2l1 1v1h1v1l-1 1c-1 1-2 1-2 2v4h-1 0 0c0 1 0 1-1 1v2h0c-1-2-1-4-1-6h-1-1v3 2c-1-1-1-2-1-4 0-1-1-1-1-2s0-2-1-3c-1-2-2-5-3-6l-1-1c0-1-1-4-2-5h0l-1-1c-1 0-1-1-2-1v-2l-1-1v-1c4-3 7-5 11-7h1c1-1 1-1 2-1h1c1 0 1 0 2-1 1 0 2-1 3-1h1l2-2 2-1z" class="U"></path><path d="M344 236h0c0 1-1 2-1 2 0 1 0 1 1 1l-3 4-1 2v-2-1c0-1 1-2 2-3v-2l2-1z" class="E"></path><path d="M352 228h1c-2 2-3 4-6 5l1-2c2 0 2-1 3-2l-1 1c-1 0-5 2-5 2-1 0-2 0-2-1h1c1-1 1-1 2-1h1c1 0 1 0 2-1 1 0 2-1 3-1z" class="D"></path><path d="M357 225h2l-7 6c0 1-1 2-1 2h-1c-1 0-2 2-4 2-1 0-1 0-2 1l-2 1c-2 1-4 2-6 4l1-1v-1c1-1 2-2 3-2 2-2 5-3 7-4 3-1 4-3 6-5l2-2 2-1z" class="a"></path><path d="M344 236c1-1 1-1 2-1 2 0 3-2 4-2h1 2c-1 1-1 2-2 2-2 2-4 4-5 6 0 0 0 1-1 1v1c-1 0-1 1-2 1l-1 1 1 1h-1-2v-1l1-2 3-4c-1 0-1 0-1-1 0 0 1-1 1-2h0z" class="i"></path><path d="M341 243h1c1 0 2-1 3-1h0v1c-1 0-1 1-2 1l-1 1 1 1h-1-2v-1l1-2z" class="Q"></path><path d="M344 236c1-1 1-1 2-1 2 0 3-2 4-2l-6 6c-1 0-1 0-1-1 0 0 1-1 1-2h0z" class="M"></path><defs><linearGradient id="Z" x1="492.033" y1="651.708" x2="439.812" y2="721.616" xlink:href="#B"><stop offset="0" stop-color="#676667"></stop><stop offset="1" stop-color="#969596"></stop></linearGradient></defs><path fill="url(#Z)" d="M500 660l1 1 1-2h0v1l1 1h0l-1 1h1l-1 2c-3 5-5 9-8 13-11 17-27 29-46 34-5 1-11 2-16 2l-2-1h-2-1c1 0 2-1 3-1 1-1 2-1 3-1l5-1 10-2c19-5 34-18 45-35 3-3 5-8 7-12z"></path><defs><linearGradient id="a" x1="434.945" y1="447.033" x2="433.975" y2="427.642" xlink:href="#B"><stop offset="0" stop-color="#4e4e51"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#a)" d="M465 426c7-1 14-1 21 0-2 1-5 0-8 0-2 1-5 1-8 1-11 2-22 5-34 8-6 2-13 3-19 6-4 1-7 2-11 4-4 1-7 3-11 5-1-1-2-1-3-1l2-2-1-1c4-2 8-4 13-6l9-4 4-1 7-2 16-5 4-1v2l19-3z"></path><path d="M442 428l4-1v2l-19 5-1-1 16-5z" class="e"></path><path d="M419 435l7-2 1 1c-9 3-18 6-27 10l-6 3-1-1c4-2 8-4 13-6l9-4 4-1z" class="B"></path><path d="M387 284c4 0 9 0 13 3l-2 2c2 0 3 1 5 1l-1 1h-1c-1 0-2 0-4 1h-4c-1 0-5 0-6 1-10 1-16 4-22 12l-2 3c-1 0-3 3-4 3h0-2v-1-2c0-1 1-2 2-3h-1v-1-1c0-1 1-2 2-2 2-3 4-5 7-7v-2c2-1 4-4 7-4l6-3c2 0 4 0 7-1z" class="C"></path><path d="M360 305l6-6 1 1c-3 2-7 6-7 9 0 0 4-4 4-5l1 1-2 3c-1 0-3 3-4 3h0-2v-1-2 1c1-1 3-3 3-4z" class="V"></path><path d="M369 294h2c-4 2-6 4-8 7l-3 3v1c0 1-2 3-3 4v-1c0-1 1-2 2-3h-1v-1c1-2 3-3 4-4 3-3 4-5 7-6z" class="G"></path><path d="M386 288l3 1h0c-3 1-6 1-9 2s-6 3-9 3h-2c5-3 11-5 17-6z" class="N"></path><path d="M367 294v-1c4-2 7-3 10-4 5-2 8-3 13-2v1h-4c-6 1-12 3-17 6-3 1-4 3-7 6-1 1-3 2-4 4v-1c0-1 1-2 2-2 2-3 4-5 7-7z" class="P"></path><path d="M364 304c2-2 6-5 6-7 1-1 2-1 3-2 2-1 5-2 7-3 3 0 9-1 10-2l1-1c3 0 7 1 10 2-1 0-2 0-4 1h-4c-1 0-5 0-6 1-10 1-16 4-22 12l-1-1z" class="X"></path><path d="M387 284c4 0 9 0 13 3l-2 2c-2-1-5-2-8-2-5-1-8 0-13 2-3 1-6 2-10 4v1-2c2-1 4-4 7-4l6-3c2 0 4 0 7-1z" class="G"></path><defs><linearGradient id="b" x1="269.684" y1="648.312" x2="257.812" y2="692.29" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#b)" d="M286 671c2 1 26-9 30-10h1c-2 2-5 3-7 4s-4 3-6 4c-1 1-3 2-5 2-4 2-8 4-13 5-4 1-9 2-13 3-11 2-21 3-31 3-11 0-22 0-33-2h-1l3-1h-2-1c1-1 1-1 2 0h12 17c7 0 15-1 22-2 6-1 13-2 19-4l6-2z"></path><defs><linearGradient id="c" x1="384.144" y1="426.821" x2="400.246" y2="397.145" xlink:href="#B"><stop offset="0" stop-color="#8d8b8c"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#c)" d="M388 374c1 1 2 2 3 4l5 9 1 2 2 8c1 3 2 6 2 9v6h-1v-2 2h0c-1 4-1 9-3 13v3l-2-1-2 3-2 2v-1l-1 1h-1c3-8 4-15 3-24 0-1-1-3-1-5l-1-3v-1h0l1-2h0v-1 1h1v-1-4l1 1v-2l-1-2c-1-3-3-8-6-10 0-1 0-2 1-2h0 0c0-1 0-1-1-2v-1h1 1z"></path><path d="M394 396h1l1 2c0 1 1 3 1 4h0l-1 1h0l-2-7z" class="L"></path><path d="M391 397v-1 1h1v-1l3 10h-1c0-2 0-3-1-4v-1c-1 1 0 2 0 4h0c-1-2-1-5-2-8h0z" class="b"></path><path d="M397 418c1 2 0 5 0 7v3l-2-1 2-9z" class="I"></path><path d="M397 402h1v1l1-1v8l1 2c-1 4-1 9-3 13 0-2 1-5 0-7v-2c0-2 1-3 0-5 0-3 0-5-1-8l1-1h0z" class="G"></path><path d="M397 402h1v1l1-1v8 1c-1-1-1-7-2-9h0z" class="S"></path><path d="M396 390l1-1 2 8c1 3 2 6 2 9v6h-1v-2 2h0l-1-2v-8l-1 1v-1h-1c0-1-1-3-1-4v-1c1 0 1-1 2-1l-2-6z" class="D"></path><path d="M398 396c0 2 1 4 1 6l-1 1v-1h-1c0-1-1-3-1-4v-1c1 0 1-1 2-1z" class="Q"></path><defs><linearGradient id="d" x1="399.241" y1="390.036" x2="384.527" y2="384.238" xlink:href="#B"><stop offset="0" stop-color="#a3a2a3"></stop><stop offset="1" stop-color="#d3d2d1"></stop></linearGradient></defs><path fill="url(#d)" d="M388 374c1 1 2 2 3 4l5 9 1 2-1 1 2 6c-1 0-1 1-2 1v1l-1-2h-1c-1-1-1-4-1-5l-1-2c-1-3-3-8-6-10 0-1 0-2 1-2h0 0c0-1 0-1-1-2v-1h1 1z"></path><path d="M388 374c1 1 2 2 3 4l-1 1c-1-1-2-3-3-5h1z" class="c"></path><path d="M391 378l5 9 1 2-1 1h0c-2-4-4-8-6-11l1-1z" class="L"></path><defs><linearGradient id="e" x1="460.678" y1="653.212" x2="471.471" y2="662.854" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#e)" d="M490 621h1v-2h1v2-2c1-1 1-1 1-2 0 1 0 2 1 3l-3 10h0l1-1v1l1-1c0 1 0 2-1 4-1 1-2 3-2 5l-2 5-9 15c-1 0-1 2-2 3l-6 6h-1 0-1c-4 3-8 7-12 10l-9 6c-2 2-4 3-6 4-1 1-3 2-4 2-2 0-4 1-5 1h1 1c-1 1-2 1-3 1-1-1 0-1 0-2s1-1 1-1l13-6c5-2 9-5 12-9 9-8 17-17 22-27 5-9 7-17 10-25z"></path><path d="M491 630h0l1-1v1l1-1c0 1 0 2-1 4-1 1-2 3-2 5l-2 5-9 15c-1 0-1 2-2 3l-6 6h-1 0-1c3-3 5-6 7-8 3-6 7-11 10-17 1-4 3-9 5-12z" class="K"></path><path d="M541 504l1-1c2 2 3 4 5 6l6 8c1 1 1 3 2 3h2v-3c2 3 5 7 6 10 2 2 3 5 5 8 5 10 8 20 11 30l7 28c2 6 3 13 8 18-1 0-2-1-3-2-1-2-3-5-4-7-4-9-7-18-9-28l-2-7-1 1c-2-11-6-23-12-32v-2c-2-3-3-5-5-7-2-4-4-7-7-11s-7-8-10-12z" class="Z"></path><path d="M557 517c2 3 5 7 6 10h0c-1-1-2-3-3-4v-1c-1 0-2 0-2 1h0c1 2 2 3 3 5h-1l-1-2-4-6h2v-3z" class="W"></path><path d="M541 504l1-1c2 2 3 4 5 6l6 8c1 1 1 3 2 3l4 6 1 2c5 8 8 16 12 24 1 4 3 7 4 11v4l-1 1c-2-11-6-23-12-32v-2c-2-3-3-5-5-7-2-4-4-7-7-11s-7-8-10-12z" class="C"></path><path d="M516 549c2 5 5 10 6 15 2 4 2 8 3 11h1c2 8 4 18 3 27v4h0c0 9-1 20-2 29-2 8-5 16-8 24-2 5-4 9-7 14-1 2-2 4-4 6v-1c1-2 2-3 2-5 4-3 6-8 7-12h0l-1-1h0v-3h-1l2-7 2 1v-3c0-2 1-4 2-6l1 1 1-5 2-16c0-1 0-1-1-2 0-2 1-5 1-8 1-4 0-9 0-14 0-3 0-7-1-10v-1-3h0c0-2 0-2 1-3-1-5-2-10-4-15 0-2-1-4-2-6l-1-1c-1 0-1-1-1-1v-1c-1 0-1-1-1-2l1-1-1-2v-3z" class="B"></path><path d="M525 622l1-4v1h1v5c-1-1-2-1-2-2z" class="L"></path><path d="M525 581l1 7-1 1h0l-1-2v-3h0c0-2 0-2 1-3z" class="X"></path><path d="M517 554c1 2 1 4 2 6l-1-1c-1 0-1-1-1-1v-1c-1 0-1-1-1-2l1-1z" class="g"></path><path d="M520 650l1 1-4 10h0l-1-1 2-4 2-6z" class="N"></path><path d="M523 638l1 1v1l-3 11-1-1c1-3 1-5 2-7l1-5z" class="X"></path><path d="M519 651v-3c0-2 1-4 2-6l1 1c-1 2-1 4-2 7l-2 6h-1c1-1 1-2 1-2 0-1 0-2 1-3z" class="E"></path><path d="M517 650l2 1c-1 1-1 2-1 3 0 0 0 1-1 2h1l-2 4h0v-3h-1l2-7z" class="M"></path><path d="M527 604c1 5 1 10 0 15h-1v-1-11h1v-3z" class="S"></path><path d="M525 589l1-1c1 6 1 11 1 16v3h-1c0-6 0-12-1-18z" class="Q"></path><path d="M525 575h1c2 8 4 18 3 27v4h0c-1-5-1-9-1-14-1-5-2-11-3-17z" class="S"></path><path d="M525 622h0c0 1 1 1 2 2-1 5-1 11-3 16v-1l-1-1 2-16z" class="G"></path><path d="M524 588v-1l1 2h0c1 6 1 12 1 18v11l-1 4h0c0-1 0-1-1-2 0-2 1-5 1-8 1-4 0-9 0-14 0-3 0-7-1-10z" class="C"></path><defs><linearGradient id="f" x1="390.766" y1="495.37" x2="391.844" y2="513.19" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#f)" d="M335 499c6 0 11 1 17 0 4 0 8-1 12-2l15-1c20-1 41-1 61 6 8 2 16 5 23 11-7-2-12-5-19-7-20-6-43-8-64-7l-19 1c-4 1-10 1-14 1-1-1-4 0-5 0-3 0-6 0-8-1h-7v-1c1 0 2 0 3 1 1 0 2-1 3 0 5 1 11 0 15 0-4-1-9 0-13-1z"></path><defs><linearGradient id="g" x1="49.702" y1="504.554" x2="36.855" y2="504.418" xlink:href="#B"><stop offset="0" stop-color="#363637"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#g)" d="M44 451v2h0l6-18c1-4 3-9 5-13v-1h1v1c-1 3-3 6-4 10-2 5-4 11-6 17-3 12-5 26-6 39 0 13 2 26 4 38l6 26-1 2c-2-4-3-8-5-13-5-16-8-33-8-50 0-4 0-9 1-13 0-2 1-5 1-8 0-4 1-8 2-12h1l-1 1c0 1 1 2 1 2l3-10z"></path><path d="M40 458h1l-1 1c0 1 1 2 1 2l-3 24-1-1h0v-6c0-2 1-5 1-8 0-4 1-8 2-12z" class="N"></path><path d="M61 453h0c1-1 1-2 1-3v3 3c-1 0-1 0-1 1v1 1c0 2-1 5-1 8l-1 16v11c0 13 1 26 5 39 1 7 4 13 7 20l4 10c1 1 2 2 2 3-1 1 0 2 0 3l3 6-1 1-2-2c-2-3-3-7-4-10-3-6-6-11-8-18-9-21-11-45-9-67l2-12v-4c1 0 1-1 1-1v-1-2h1v-2-1l1-1v-2h0z" class="C"></path><path d="M80 575c-2-1-3-3-3-4-1-1-1-1 0-2l3 6z" class="K"></path><defs><linearGradient id="h" x1="380.473" y1="516.541" x2="429.308" y2="461.279" xlink:href="#B"><stop offset="0" stop-color="#656562"></stop><stop offset="1" stop-color="#929093"></stop></linearGradient></defs><path fill="url(#h)" d="M388 480c4-2 9-2 13-2 2 0 3 0 5 1h3 11l10 1 16 3c2 1 5 2 7 4 1 0 3 1 4 1l7 3v2c-8-3-17-6-26-7-4-1-8-2-12-2-17-2-33-2-49 0l-15 2c-2 0-5 1-7 1-1 0-3-1-4 0h-4c1-1 4 0 5-1h-4l2-1c4 0 8 0 11-1l1-1h1-1l10-4h0v1l-1 1h0 2 3l12-1z"></path><path d="M388 480c4-2 9-2 13-2 2 0 3 0 5 1h3c-3 0-6 1-9 0h-1-3c-1 0-2 1-4 1h-1-3z" class="O"></path><path d="M361 484l1-1h1-1l10-4h0v1l-1 1h0 2 3c-4 1-10 3-15 3z" class="P"></path><path d="M272 216c5 0 9-1 14 0h0v1h0c1 0 1 0 2 1s2 0 3 1l7 3c3 1 5 2 7 4 4 2 8 4 11 7h1c2 1 3 3 4 4l7 10v1l3 6c0 2 1 4 2 6v1h0l-1-1c-1-2-2-3-3-4h0l1 1-1 1-1-2h-1c-6-9-13-18-23-24-3-1-6-3-8-4-4-1-8-2-11-4-2-2-4-3-6-3l-3-1 1-1-7-1c1-1 4-1 5-1-1 0-2 0-3-1h0z" class="K"></path><path d="M317 233c2 1 3 3 4 4l-1 1v1c1 1 2 1 2 2 1 2 4 4 4 6h0l-4-5c-2-3-5-5-6-8v-1h1z" class="B"></path><path d="M272 216c5 0 9-1 14 0h0v1h0c1 0 1 0 2 1s2 0 3 1l7 3c3 1 5 2 7 4 4 2 8 4 11 7v1l-3-2c-6-4-12-8-18-10-2-1-4-2-7-2-4-2-8-4-13-3-1 0-2 0-3-1h0z" class="M"></path><path d="M277 219c5 1 8 3 12 5 5 3 10 4 15 7 4 3 9 6 12 10 3 3 6 5 8 8 1 3 3 5 4 7h-1c-6-9-13-18-23-24-3-1-6-3-8-4-4-1-8-2-11-4-2-2-4-3-6-3l-3-1 1-1z" class="h"></path><defs><linearGradient id="i" x1="160.42" y1="674.641" x2="154.371" y2="688.471" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#i)" d="M89 636l1-1c3 3 6 7 9 11 7 7 15 15 23 21 3 2 5 3 8 5 2 2 5 4 8 5 9 6 20 11 30 14 16 4 32 7 48 9h-2c1 1 2 1 4 1h3c-2 0-4 0-5 1h0c-5 0-11-1-16-2-23-3-46-9-66-21-3-1-5-3-8-5l-12-9-5-4c0-1-1-1-2-2v1l2 2-5-5c-3-4-6-7-9-11l-1-2v-2c-2-1-3-4-5-6z"></path><path d="M102 653h1l6 5c1 2 5 4 6 5l-1 1h1l-1 1-5-4-7-8z" class="a"></path><path d="M94 642c1 2 5 5 5 6 1 3 1 3 3 5h0l7 8c0-1-1-1-2-2v1l2 2-5-5c-3-4-6-7-9-11l-1-2v-2z" class="C"></path><defs><linearGradient id="j" x1="53.597" y1="428.59" x2="23.49" y2="428.793" xlink:href="#B"><stop offset="0" stop-color="#595758"></stop><stop offset="1" stop-color="#8d8d8e"></stop></linearGradient></defs><path fill="url(#j)" d="M35 466c-1 4-4 9-6 13-3 4-7 8-8 13-1 2-1 5-1 8v-4c-1-6 3-11 5-16l3-12c2-9 3-19 4-28 2-12 3-23 6-34 2-9 5-18 10-26 2-4 5-7 8-11l6-7h1c-2 2-3 4-5 6l-6 9c-6 10-9 21-11 33l-3 12c-1 3-1 7-1 11 0 3-1 7-1 10v2 1l-3 20v1l1-3c1 1 1 1 1 2z"></path><defs><linearGradient id="k" x1="145.884" y1="603.206" x2="135.919" y2="626.628" xlink:href="#B"><stop offset="0" stop-color="#212021"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#k)" d="M80 575l6 9 1-1c4 5 7 9 11 13 24 25 58 38 92 43l7 1h0 12v1h-3l-27-1c-1 1-1 1-2 1-1-1-2-1-2 0l-1-1h-3 0 1 1c0 1 1 1 1 1 1 0 1 0 2 1-4-1-8-2-11-3l-9-3 1-1c-27-8-52-24-70-46-3-4-6-9-8-13l1-1z"></path><path d="M157 635l6 2c5 2 11 2 16 3-1 1-1 1-2 1-1-1-2-1-2 0l-1-1h-3 0 1 1c0 1 1 1 1 1 1 0 1 0 2 1-4-1-8-2-11-3l-9-3 1-1z" class="P"></path><path d="M401 478c3 0 6-1 9-2h16c8 1 17 1 26 2 12 2 23 6 35 10 7 3 14 6 21 11l3 3c2 1 4 2 6 4s5 4 7 7c12 11 20 24 26 39v1c1 1 1 2 2 3 0 1 1 5 1 6l-3-9c-1 0-1-1-2-1h0c-5-10-10-19-17-27-18-23-45-38-74-44-6-1-12-2-18-2h0-9-4 2 1l1 1-10-1h-11-3c-2-1-3-1-5-1z" class="J"></path><path d="M511 502c2 1 4 2 6 4h-1-1l-3-2h0c0-1-1-1-1-1v-1h0z" class="g"></path><path d="M420 479c-1 0-2 0-4-1h1 12c3 0 7 0 10 1h0-9-4 2 1l1 1-10-1z" class="C"></path><defs><linearGradient id="l" x1="69.644" y1="344.324" x2="24.326" y2="347.796" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#919091"></stop></linearGradient></defs><path fill="url(#l)" d="M61 343c-11 13-22 26-36 35-3 2-6 4-8 4 2-2 5-3 7-5 4-4 8-8 11-12 5-6 9-14 14-21 5-10 11-19 18-28 0 0 1 0 1-1h1l-1 8v5l-1 2v2h-1c-1 1-2 2-3 4-1 0-4 3-4 4v1 2 1l1-1h1z"></path><path d="M59 340l-10 11c4-10 13-19 18-29l1 6-1 2v2h-1c-1 1-2 2-3 4-1 0-4 3-4 4z" class="U"></path><path d="M366 305v3h0v-1l1-1h1l1 2-2 1 1 2v-1c0-1 1-1 2-2l1 2h0l1 1v3 1h0 1v4 1l1 1v1 1 1c-1 0-1 0-1 1h-1l-1 1-1 3-3 3-2 2-1 1h0c1 0 1 0 2 1 0 1-2 1-3 1 0 0-1-1-2-1 0 1 0 1-1 1l-1-1v-1h-1l-1-1v-1s-1 0-1 1c-2 1-3 2-5 3v-1c0-1 0-1-1-2v-3h0v-1c-1-6 3-10 6-14h0v-1c1-2 2-3 3-4 1 0 3-3 4-3l3-3z" class="D"></path><path d="M365 313h2c1 2-1 4 1 6l-1 3h-1v-2c-1-2 0-3-1-5v-2z" class="E"></path><path d="M366 305v3h0v-1l1-1h1l1 2-2 1-11 7v-1c1-2 2-3 3-4 1 0 3-3 4-3l3-3z" class="T"></path><path d="M364 315v-1-1h0 1v2c1 2 0 3 1 5-2 4-3 6-6 8h-6 0s-1 0-1-1h0l1-5c0-2 2-3 3-5 1-1 3-2 4-2s1 0 2-1l1 1z" class="P"></path><path d="M364 315v-1-1h0 1v2c1 2 0 3 1 5-2 4-3 6-6 8h-6 0s-1 0-1-1h0l1-5c1 1 1 2 1 3h2c1 0 2 0 2-1 1 0 1-1 1-1l2-1s0-1 1-2c0 0 0-1 1-2v-3z" class="Q"></path><path d="M355 325h2c1 0 2 0 2-1 1 0 1-1 1-1l2-1s0-1 1-2c0 0 0-1 1-2 0 3 0 5-2 7-2 1-4 1-6 2 0-1-1-1-1-2z" class="H"></path><path d="M368 311v-1c0-1 1-1 2-2l1 2h0l1 1v3 1h0 1v4 1l1 1v1 1 1c-1 0-1 0-1 1h-1l-1 1-1 3-3 3-2 2-1 1h0c1 0 1 0 2 1 0 1-2 1-3 1 0 0-1-1-2-1 0 1 0 1-1 1l-1-1v-1h-1l-1-1v-1s-1 0-1 1c-2 1-3 2-5 3v-1c0-1 0-1-1-2v-3h4-1l1-1c-1 0 0-1 0-2h0 6c3-2 4-4 6-8v2h1l1-3v-8h0z" class="j"></path><path d="M373 320l1 1v1 1 1c-1 0-1 0-1 1h-1c0-2 0-3 1-5z" class="h"></path><path d="M350 331h4 1l-1 1h0 0c-1 1-2 1-2 2h-2v-3z" class="V"></path><path d="M368 311v-1c0-1 1-1 2-2l1 2h0l1 1v3h-1l-1 1-1 3v-3l1-1h0v-2c-1-1-1-1-2-1h0z" class="f"></path><path d="M357 334c2 0 3-1 4 0h1c1-1 2-1 2-1l1 1-1 1h0c1 0 1 0 2 1 0 1-2 1-3 1 0 0-1-1-2-1 0 1 0 1-1 1l-1-1v-1h-1l-1-1z" class="n"></path><path d="M369 318l1-3 1-1h1v1 2 2c0 1-1 1-1 2v3l-3 3c0-2 2-6 1-9z" class="i"></path><path d="M366 320v2h1c0 3-1 6-4 7-2 2-7 2-10 2l1-1c-1 0 0-1 0-2h0 6c3-2 4-4 6-8z" class="H"></path><defs><linearGradient id="m" x1="253.204" y1="676.344" x2="251.898" y2="699.922" xlink:href="#B"><stop offset="0" stop-color="#222"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#m)" d="M180 687c17 5 33 8 51 9 25 1 49-1 73-8 7-2 15-5 22-8h1c0-1 1-1 1-1 0 1-1 2-2 3l-6 3c-11 5-23 9-36 12-15 3-31 3-47 3l-7-1c-4 0-8 0-12-1-13-2-27-4-39-8v-1l1-1v-1z"></path><path d="M333 665s0 1 1 1c0 3 1 5 3 7 0 2 2 3 3 5h1c1 0 2 1 2 2 2 1 3 2 5 3l2 2h0c5 3 10 5 16 7v-1h1l1-1 5 2c2 1 4 1 6 1h5c3 0 6 1 9 1s7 0 10-1c5 0 9 0 14-1l2-1 2-1 2-1c1 1 1 1 1 2h1v1l5-2 2-1c0 1-1 1 0 2 1 0 2 0 3-1h-1-1c1 0 3-1 5-1-2 1-4 2-5 3-3 1-6 3-9 3s-5 1-8 2c-2 1-4 1-5 1-10 2-18 2-28 1-8-1-16-2-24-5-2-1-4-2-6-4-5-3-12-7-15-12-2-3-4-7-5-10v-3z" class="k"></path><path d="M425 692l5-2v1c-1 1-3 1-4 1l-1 1h-3-1c1-1 2-1 4-1z" class="G"></path><path d="M340 678h1c1 0 2 1 2 2 2 1 3 2 5 3h-1-2c-1 0-5-4-5-5z" class="a"></path><path d="M350 685c5 3 10 5 16 7h1l-1 1c1 0 2 1 4 1l3 1h-3l2 1h0 0c-7-1-14-4-20-8 0 0-2-1-2-2v-1z" class="p"></path><path d="M350 685c5 3 10 5 16 7h1l-1 1c-6-2-11-4-16-7v-1z" class="L"></path><path d="M421 690l2-1c1 1 1 1 1 2h1v1c-2 0-3 0-4 1-8 2-16 4-25 5h-16c-2-1-6-1-8-2h0 0 0l-2-1h3 3c4 1 9 1 13 1 10 0 19-1 29-3 0-1-1-1-1-1l2-1 2-1z" class="P"></path><path d="M419 691l1 1-2 1c0-1-1-1-1-1l2-1z" class="I"></path><path d="M421 690l2-1c1 1 1 1 1 2-1 0-3 1-4 1l-1-1 2-1z" class="J"></path><path d="M368 690l5 2c2 1 4 1 6 1h5c3 0 6 1 9 1s7 0 10-1c5 0 9 0 14-1 0 0 1 0 1 1-10 2-19 3-29 3-4 0-9 0-13-1h-3l-3-1c-2 0-3-1-4-1l1-1h-1v-1h1l1-1z" class="G"></path><path d="M372 693c1 1 3 1 4 2h-3l-3-1 2-1z" class="h"></path><path d="M366 691h1c2 1 3 2 5 2l-2 1c-2 0-3-1-4-1l1-1h-1v-1z" class="I"></path><defs><linearGradient id="n" x1="489.383" y1="479.563" x2="497.652" y2="459.78" xlink:href="#B"><stop offset="0" stop-color="#706f70"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#n)" d="M441 453c35-3 71 7 98 30 2 2 4 3 5 5 3 2 5 4 7 7h-1v1h0v1c-1-1-2-3-3-3 0-1 0 0-1-1 0-1 0-1-2-1-2-1-5-5-7-7l-12-9-1 2h0l-12-7h0 1 0c-1-1-3-2-5-2l-1-1h-1l-1-1h-1c-1-1-1 0-2-1h-1-1s-1-1-2-1v-1c-4-2-10-3-15-4-10-3-21-4-32-4h-15-2-1v-1-1h2c2-1 4 0 6-1h0z"></path><path d="M498 464l8 3c6 2 13 5 19 9l-1 2h0l-12-7h0 1 0c-1-1-3-2-5-2l-1-1h-1l-1-1h-1c-1-1-1 0-2-1h-1-1s-1-1-2-1v-1z" class="B"></path><defs><linearGradient id="o" x1="550.511" y1="536.312" x2="522.844" y2="557.072" xlink:href="#B"><stop offset="0" stop-color="#585758"></stop><stop offset="1" stop-color="#8d8d8d"></stop></linearGradient></defs><path fill="url(#o)" d="M493 484c15 7 28 16 39 28 3 4 7 7 10 11 8 12 15 25 19 38 3 9 5 19 6 29 2 12 3 25 1 37-1-3 0-6 0-9 0-5-1-11-2-17-5-39-20-80-55-103-1-1-20-11-21-11l1-1h1c1-1 1-1 1-2z"></path><defs><linearGradient id="p" x1="95.762" y1="530.956" x2="72.594" y2="547.403" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#p)" d="M64 484c0 1 0 1 1 1 0-2-1-5 0-7v11c1 3 1 7 2 10l3 20c3 12 9 25 14 36 2 3 3 6 5 9 1 2 3 4 3 6h2l-1-2c2 3 4 7 7 9 2 2 4 4 5 6h-1v1c1 0 1 0 0 1 18 19 40 34 66 41h-2-1l-1-1c-1 0-2 0-3-1-2-1-4-1-6-2h-1s-1 0-1-1h-2c0-1-1-1-2-1l-1-1c-3-1-5-2-7-3-3-2-6-3-9-5-7-5-14-9-19-15l-14-14v1 1c0 1 1 1 1 2h1l1 1c1 0 1 1 1 1l1 1s1 1 1 2h0-1-1l1 1c1 0 1 1 2 2h-1c-6-6-12-14-18-22-4-6-7-12-10-18-6-11-10-23-13-36-1-4-1-8-2-12-1-7-1-15 0-22z"></path><path d="M93 568c2 3 4 7 7 9 2 2 4 4 5 6h-1v1c1 0 1 0 0 1l-12-15h2l-1-2z" class="P"></path><path d="M367 231c1-1 2-1 3-1h3c-1 0-2 1-2 1s0 1 1 1l2-2v1l-4 2v1l2 1h1c0 1-1 1-1 2 1 0 1 0 2-1h3 0c-1 0-1 0-1 1-1 1-1 1-2 1h0v1h0 2 0 0l1 1h-2c-1 1-2 2-3 2v1h1v1l1 1v1l-1 1v1h-4v1h0 4v1h-2l-5 1-1 1v1l-10 3h-2v-1h-1v-1l-1-1h-2-2 0v-1-2h-1v-1c0-2-1-1-2-2 0 0 0-1-1-1h-1 1l-1-1 1-1c1 0 1-1 2-1v-1c1 0 1-1 1-1 1-2 3-4 5-6h4c1-1 3-1 3-2 3-1 5-2 9-2z" class="l"></path><path d="M351 237h2l-1 1h-1v-1zm21 5h0-2v-1l5-1c-1 1-2 2-3 2zm-27 0c1 0 1-1 1-1 0 1 0 2-1 3h1c1-1 1 0 2 0l-5 2-1-1 1-1c1 0 1-1 2-1v-1zm10-2c1 0 2-1 4-1h1c-1 1-1 1-2 1s-2 0-3 2h2 0-1-2-1l1-1 1-1z" class="V"></path><path d="M355 240l-1 1-1 1h1c-2 1-3 1-4 1h-1-2v-1c2-1 5-1 8-2z" class="i"></path><path d="M367 231c1-1 2-1 3-1h3c-1 0-2 1-2 1s0 1 1 1h-1-2c-1 1-1 1-2 1v1h0v1h-3c-2 0-3 1-5 1v-1l3-1h1c1 0 1 0 2-1l1-1h1v-1z" class="V"></path><path d="M357 246v1l-5 3-2 1c-1 0-2 1-3 2v-1-2h-1v-1h0c2 0 5-2 7-2 1-1 3-1 4-1z" class="E"></path><path d="M374 236h3 0c-1 0-1 0-1 1-1 1-1 1-2 1h0v1c-1 0-2 1-3 1s-3 1-4 1h0c-1 0-1 0-2 1h0l-1-1 1-1h-2 0c0-1 1-1 2-1 1-1 1 0 1-1h0 2l1-1h2 1c1 0 1 0 2-1z" class="V"></path><path d="M354 242h2l-2 2c0 1 0 1 1 1h-1c-2 1-6 2-8 4h0c0-2-1-1-2-2 0 0 0-1-1-1h-1 1l5-2 2-1c1 0 2 0 4-1z" class="D"></path><path d="M370 243h2 1v1l1 1v1l-1 1v1h-4v-1h-1l-1 1c-1 0-1 0-2-1h0v-1h-3-1l-1 1h-3v-1l5-1c1 0 3 0 4-1l2-1h2z" class="i"></path><path d="M367 245h4c-2 1-3 1-4 0z" class="T"></path><path d="M370 243h2 1v1 1h-2c0-1-1-1-1-2z" class="Y"></path><path d="M357 247h3l1-1h1 3v1h0c1 1 1 1 2 1l1-1h1v1 1h0 4v1h-2l-5 1v-1h-2v-1c-2 0-3 0-5 1h-1c-1-1-3 0-5 1h-3l2-1 5-3z" class="T"></path><path d="M357 247h3l1-1h1 3v1h0c-3 1-4 1-6 1-2 1-2 0-3 1-1 0-1 1-2 1h-1-1l5-3z" class="f"></path><path d="M350 251h3c2-1 4-2 5-1h1c2-1 3-1 5-1v1h2v1l-1 1v1l-10 3h-2v-1h-1v-1l-1-1h-2-2 0c1-1 2-2 3-2z" class="V"></path><path d="M349 253l4-1v1l-1 1-1-1h-2z" class="C"></path><path d="M357 251h5l-2 2h0-2c0-1-1-1-1-1v-1z" class="J"></path><path d="M353 252c1 0 2 0 4-1v1s1 0 1 1h2-2c-1 0-1 1-1 1l-1-1-4 2v-1l1-1v-1z" class="H"></path><path d="M364 250h2v1l-1 1v1l-10 3h-2v-1h-1l4-2 1 1s0-1 1-1h2 0l2-2 2-1z" class="B"></path><path d="M364 250h2v1l-1 1c-2 0-3 1-5 1h0l2-2 2-1z" class="N"></path><path d="M353 256h2c2 0 3 0 4-1l1 1h0l2 1c1 0 1 0 2 1v1l4-1h6 1c3 1 5 2 8 4 1 0 2 1 4 1 1 2 3 3 4 5 1 1 1 1 1 2l1 1c0 1-1 1-1 1l1 3h-1l-5-2-1-1h0c-1 0-2-1-3-1v-1c-5-1-9-1-13 0 0-1-1-1-1-1-1 0-1-1-1-1h-1c-2 2-6 3-7 5l-4 4c-1 1-2 3-3 5v1h-1c0-3-1-6-2-9 0-3-1-5-2-8v-2c1 0 1 0 1-1h0 0 1v-4c0-1 1-1 2-2l1-1z" class="n"></path><path d="M386 264l2 2h-1l-1 1-2-1 2-2z" class="K"></path><path d="M374 260c4 1 8 2 12 4l-2 2c-1-1-3-2-4-3-2-1-4-2-5-2l-1-1h0zm-15 1h0c2-1 5-2 8-2-1 1-1 2-2 3h-1c-1 1-2 1-3 2l-1-1c-1-1 0-1-1-2z" class="C"></path><path d="M360 263c2 0 2-1 4-2l1 1h-1c-1 1-2 1-3 2l-1-1z" class="K"></path><path d="M372 259l2 1h0 0l1 1c1 0 3 1 5 2 1 1 3 2 4 3l2 1 1-1 1 1-1 2c-1 0-3-1-4-2-2-1-4-1-6-1l-2-1h-3-2c-1 1-2 1-3 1 1-1 2-1 2-1h2l2-2h-1v-1-1h0v-2z" class="M"></path><path d="M375 265c-1-1-1-2-1-2h-1c1-1 1-1 2-1l3 2c-1 0-2 0-2-1-1 1-1 1-1 2z" class="B"></path><path d="M375 265c0-1 0-1 1-2 0 1 1 1 2 1s3 1 4 1c1 1 2 2 4 2h0l1-1 1 1-1 2c-1 0-3-1-4-2-2-1-4-1-6-1l-2-1h0z" class="C"></path><path d="M388 266c1 1 1 2 3 2 1 1 1 1 1 2l1 1c0 1-1 1-1 1l1 3h-1l-5-2-1-1h0c-1 0-2-1-3-1v-1c-5-1-9-1-13 0 0-1-1-1-1-1 1-1 1-1 2-1h2v-1h2l2-1c2 0 4 0 6 1 1 1 3 2 4 2l1-2-1-1h1z" class="N"></path><path d="M383 270c2 1 5 2 7 3h1c-1-1-1-1-1-2h1 2 0c0 1-1 1-1 1l1 3h-1l-5-2-1-1h0c-1 0-2-1-3-1v-1z" class="P"></path><path d="M388 266c1 1 1 2 3 2 1 1 1 1 1 2l1 1h0-2-1-1l-3-1h0 3c0-1-1-1-2-1l1-2-1-1h1z" class="U"></path><path d="M367 259h5v2h0v1 1h1l-2 2h-2s-1 0-2 1c-1 0-2 0-4 1h0c0 1-1 1 0 1-1 1-1 0-2 1h-3 0l-1 1c-1 0-3 2-3 3h-1c1-1 1-2 2-3h1c0-1 1-1 1-2v-1c-1 1-2 1-3 2h0-1v-1c2-1 3-2 4-2h1l3-2c1-1 2-1 3-2h1c1-1 1-2 2-3z" class="C"></path><path d="M364 262h1 1 6v1c-2 0-5 0-7 1h-1c-2 0-4 2-6 3v-1l3-2c1-1 2-1 3-2z" class="g"></path><path d="M367 259h5v2h0v1h-6-1-1 1c1-1 1-2 2-3z" class="D"></path><path d="M367 266c1 0 2 0 3-1h2 3l2 1-2 1h-2v1h-2c-1 0-1 0-2 1-1 0-1-1-1-1h-1c-2 2-6 3-7 5l-4 4c-1 1-2 3-3 5v1h-1c0-3-1-6-2-9 1-1 1-2 1-4h0c1 1 1 2 1 3v1h1v-1h1c0-1 2-3 3-3l1-1h0 3c1-1 1 0 2-1-1 0 0 0 0-1h0c2-1 3-1 4-1z" class="B"></path><path d="M367 266c1 0 2 0 3-1h2 3l2 1-2 1h-2v1h-2c-1 0-1 0-2 1-1 0-1-1-1-1v-1c-1 0-3 1-4 2-2 1-5 2-7 4-1 2-2 3-3 4h0c0-2 1-4 3-5 1-1 2-2 4-3 1-1 1 0 2-1-1 0 0 0 0-1h0c2-1 3-1 4-1z" class="g"></path><path d="M369 266c1 0 1 0 1 1 0 0-1 0-1-1z" class="k"></path><path d="M353 256h2c2 0 3 0 4-1l1 1h0l2 1c1 0 1 0 2 1v1c-2 0-4 0-6 1 0 0-1 1-2 1 0 0-1 0-2 1h0l5-1c1 1 0 1 1 2l1 1-3 2h-1c-1 0-2 1-4 2v1h1 0c1-1 2-1 3-2v1c0 1-1 1-1 2h-1c-1 1-1 2-2 3v1h-1v-1c0-1 0-2-1-3h0c0 2 0 3-1 4 0-3-1-5-2-8v-2c1 0 1 0 1-1h0 0 1v-4c0-1 1-1 2-2l1-1z" class="P"></path><path d="M353 256h2c2 0 3 0 4-1l1 1h0l2 1c1 0 1 0 2 1v1c-2 0-4 0-6 1 0 0-1 1-2 1-1-2 1-3 0-5 0 0-1 0-1 1h-2-1l1-1z" class="B"></path><path d="M353 256h2c2 0 3 0 4-1l1 1h0l2 1h-2c-1 0-1 0-1 1h-1-1v-1l-1-1s-1 0-1 1h-2-1l1-1z" class="D"></path><defs><linearGradient id="q" x1="477.602" y1="491.236" x2="490.179" y2="469.118" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#q)" d="M441 460c6 0 13 0 19 1l14 3 5 1s0 1-1 1l13 4c1 0 2 0 3-1 1 1 2 2 4 2l2 1 5 2c10 7 20 13 30 22 2 2 5 4 7 7l-1 1-10-8h0c1 2 4 4 5 6h1v2h-1-1-1c-1 0-1-1-2-1l-4-5c-13-12-29-21-45-27-17-6-35-8-53-7-1-1-1-1-2-1 0-1 1 0 2-1s2-1 3-1l8-1z"></path><path d="M515 483l13 10h-1c-2-1-4-3-6-4-2-2-5-3-6-5v-1z" class="S"></path><path d="M460 461l14 3 5 1s0 1-1 1l-18-3c0-1-1-1-1-1l1-1z" class="p"></path><path d="M441 460c6 0 13 0 19 1l-1 1s1 0 1 1l-19-2c-4 0-8 1-11 1 1-1 2-1 3-1l8-1z" class="P"></path><defs><linearGradient id="r" x1="515.207" y1="468.419" x2="517.043" y2="504.26" xlink:href="#B"><stop offset="0" stop-color="#232021"></stop><stop offset="1" stop-color="#313534"></stop></linearGradient></defs><path fill="url(#r)" d="M494 469c1 1 2 2 4 2l2 1 5 2c10 7 20 13 30 22 2 2 5 4 7 7l-1 1-10-8-3-3-13-10c-1-1-3-2-5-3-6-4-12-7-19-10 1 0 2 0 3-1z"></path><defs><linearGradient id="s" x1="478.652" y1="486.681" x2="525.498" y2="442.129" xlink:href="#B"><stop offset="0" stop-color="#818282"></stop><stop offset="1" stop-color="#a19fa0"></stop></linearGradient></defs><path fill="url(#s)" d="M440 445c2 0 4 0 6-1h17c28 1 53 7 77 20 10 5 19 12 26 20 3 3 5 6 8 9l-1 2c-13-17-32-28-51-35h-2c-1 0-1-1-2-1s-2 0-3-1h-1c-1-1-2-1-3-1h0c-1 0-1 0-1-1l-1 1-1-1h-1-1c0-1-1-1-1-1l7 3h1c0 1 1 1 2 1 1 1 4 2 5 2 1 1 1 1 1 2h-1c-13-7-26-10-39-13h0c-2-1-5-1-7-2h-3c-3-1-7 0-10-1-3 0-6-1-9 0h-9c0-1-1-1-1-1h-2v-1z"></path><path d="M481 450c14 2 28 5 41 10h-2c-1 0-1-1-2-1s-2 0-3-1h-1c-1-1-2-1-3-1h0c-1 0-1 0-1-1l-1 1-1-1h-1-1c0-1-1-1-1-1l7 3h1c0 1 1 1 2 1 1 1 4 2 5 2 1 1 1 1 1 2h-1c-13-7-26-10-39-13h0z" class="B"></path><path d="M365 219c5-4 13-14 19-14 1 0 2 1 3 1 1 2 1 5 0 6 0 6-3 11-4 17 0 1 0 2-1 3l-1 5c0-1 0-2-1-2 0 0-1 1-1 2-1 1-3 1-3 2h-2 0v-1h0c1 0 1 0 2-1 0-1 0-1 1-1h0-3c-1 1-1 1-2 1 0-1 1-1 1-2h-1l-2-1v-1l4-2v-1l-2 2c-1 0-1-1-1-1s1-1 2-1h-3c-1 0-2 0-3 1-4 0-6 1-9 2 0 1-2 1-3 2h-4c1 0 1-1 2-2h-2s1-1 1-2l7-6 3-3 3-3z" class="T"></path><path d="M374 231h1c1 0 1-1 2-1 1-1 3-3 4-3 0 1-2 3-3 4v1h1 0 1 0c0 1-1 1-2 2h0c-1 1-2 1-2 2h-2c-1 1-1 1-2 1 0-1 1-1 1-2h-1l-2-1v-1l4-2z" class="Y"></path><path d="M365 226v-1c2-2 6-2 8-3 1 0 2-1 3-2h0-3v-1h3c2 0 5-2 5-3 1-1 1-2 1-3s0-3 1-5l1 1c0 2 0 5-1 7 0 1 0 2-1 3 0 2-2 3-3 4h-1c-1 1-2 1-3 2l-1-1 3-2-1-1c-3 3-8 3-11 5z" class="c"></path><path d="M376 221c2-2 4-3 7-4-2 3-3 4-6 5l-1-1z" class="j"></path><path d="M376 221l1 1-3 2c-2 1-4 1-6 2-1 1-2 1-2 2 3-1 7-2 10-3 1 0 3-2 3-2v1c-2 1-2 2-4 3-1 0-2 1-3 1-2 1-3 1-5 1-2 1-4 1-5 2-2 0-3 0-4 1v1c0 1-2 1-3 2h-4c1 0 1-1 2-2 1 0 2-1 3-2s1-2 2-2c2-1 5-2 7-3 3-2 8-2 11-5z" class="V"></path><path d="M512 463c0-1 0-1 1-1 0 0 3 1 4 1h3 1c10 5 18 11 27 18 8 7 17 15 23 24h-1c1 2 3 3 3 5v2 1h0c2 2 3 5 4 7 2 4 3 7 5 11l4 11 3 9v22c-1-3-1-8-2-12-1-7-3-14-6-21-3-8-6-14-10-21-3-4-5-10-8-14l-9-12c-3-2-5-5-8-7-10-9-21-18-34-23z" class="L"></path><path d="M573 513v1h-1l-6-11v-1c3 4 5 8 7 11h0z" class="W"></path><path d="M566 502h0c-1-3-3-5-5-7l-3-2s0-1 1-1c4 4 8 9 11 13 1 2 3 3 3 5v2 1c-2-3-4-7-7-11z" class="B"></path><defs><linearGradient id="t" x1="192.811" y1="638.137" x2="187.552" y2="662.862" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#t)" d="M112 633l-3-3c-1-1-2-1-2-2l3 2c1 1 2 1 3 2l3 2 4 2c16 9 34 16 52 20 17 4 33 6 51 5l53-5c1 0 2 0 3 1-9 2-19 4-28 5s-18 3-28 3c-17 1-33-1-49-4-16-4-31-9-46-17-5-3-11-7-16-11z"></path><path d="M116 634l4 2h0v1l-1-1c-1-1-2-1-2-1l-1-1z" class="C"></path><path d="M371 326h1c1 0 2 0 3 1h7c4 0 8 1 12 1h1c4 0 6 2 10 3l-1 1c2 1 3 2 4 3l5 5 2 3-1 1 2 4-1 1v1l1 3-1 1c1 1 1 1 1 2l-2-3h0l-1-1c-1 0-2-1-3-2s-2-1-3-2c-1-2-2-3-4-5l-1 1c1 0 1 1 2 2h0c-2-1-3-2-5-3s-5-1-8-2v-2l-5-1h-1 0c-1 0-2 0-3-1-1 0-1 0-2 1h-2-1c-2 1-5 1-8 1-2 0-5 1-8 1v-2c-1 0-1 0-2-1h1c1 0 1 0 1-1 1 0 2 1 2 1 1 0 3 0 3-1-1-1-1-1-2-1h0l1-1 2-2 3-3 1-3z" class="k"></path><path d="M387 334c2 0 3 0 4 1l-1 1-6-1c0-1 0-1 1-1h2z" class="C"></path><path d="M361 340l1-1c2 0 4-1 7-1h8c-2 1-5 1-8 1-2 0-5 1-8 1z" class="E"></path><g class="F"><path d="M367 332h3l-1 1h4v1h2l1 1c-3 0-6 0-9 1h-1c-1-1-1-1-2-1h0l1-1 2-2z"></path><path d="M380 338c-4-1-9-1-13-1v-1h6 7c1 0 3 0 5 1h3l-1 1h-1-1 0c-1 0-2 0-3-1-1 0-1 0-2 1z"></path></g><path d="M382 337h0 3 3l-1 1h-1-1 0c-1 0-2 0-3-1z" class="H"></path><path d="M388 337c4 1 8 1 11 4l1 1h0l-1 1c-2-1-5-1-8-2v-2l-5-1h1l1-1z" class="D"></path><path d="M391 339l9 3-1 1c-2-1-5-1-8-2v-2z" class="G"></path><path d="M397 334c5 2 10 5 14 9h0l1 1 1-1 1 1 2 4-1 1v1l1 3-1 1c1 1 1 1 1 2l-2-3h0l-1-1c-2-4-6-8-10-10-2-1-4-2-5-3-2-2-5-3-8-3l1-1c2 0 3 0 5 1 0-1 1-2 1-2z" class="B"></path><path d="M398 337c0-1 1-1 1-1 1 1 4 2 5 3h1l-1 1c-1-1-2-1-2-2v1s1 0 1 1h0c-1 0-1-1-2-1s-2-1-3-2z" class="E"></path><path d="M397 334c5 2 10 5 14 9h0l1 1 1-1 1 1 2 4-1 1v1c-2-4-6-8-10-11h-1c-1-1-4-2-5-3 0 0-1 0-1 1l-2-1c0-1 1-2 1-2z" class="X"></path><path d="M411 343h0l1 1 1-1 1 1 2 4-1 1c-1-2-2-4-4-6z" class="U"></path><path d="M371 326h1c1 0 2 0 3 1h7c4 0 8 1 12 1h1c4 0 6 2 10 3l-1 1c2 1 3 2 4 3l5 5 2 3-1 1-1-1-1 1-1-1h0c-4-4-9-7-14-9 0 0-1 1-1 2-2-1-3-1-5-1-1-1-2-1-4-1h-2c-1 0-1 0-1 1-1 0-3-1-5-1l-3 1-1-1h-2v-1h-4l1-1h-3l3-3 1-3z" class="K"></path><path d="M371 326h1c1 0 2 0 3 1v1h-3l-1 1h-1l1-3z" class="R"></path><path d="M370 329h1 11c3 0 7 1 11 2h4v1h-3-1c-2 1-4 0-6 0l-10-1c-1 0-6 0-7 1h0-3l3-3z" class="H"></path><path d="M382 329c3 0 7 1 11 2h-1-2-6c-1-1-2-1-4-1l2-1z" class="J"></path><path d="M370 329h1 11l-2 1c-2 0-8-1-10 0v1h7c-1 0-6 0-7 1h0-3l3-3z" class="g"></path><path d="M375 327h7c4 0 8 1 12 1h1c4 0 6 2 10 3l-1 1c2 1 3 2 4 3l5 5 2 3-1 1-1-1-1-3c-3-3-7-7-10-8-8-4-19-3-27-4v-1z" class="X"></path><path d="M394 328h1c4 0 6 2 10 3l-1 1c-3-2-6-3-10-4z" class="K"></path><path d="M377 331l10 1c2 0 4 1 6 0 1 1 3 1 4 2 0 0-1 1-1 2-2-1-3-1-5-1-1-1-2-1-4-1h-2c-1 0-1 0-1 1-1 0-3-1-5-1l-3 1-1-1h-2v-1h-4l1-1h0c1-1 6-1 7-1z" class="E"></path><path d="M373 333c2 0 4 0 6 1l-3 1-1-1h-2v-1z" class="a"></path><path d="M377 331l10 1c-1 0-3 0-4 1-5-1-9-1-13-1 1-1 6-1 7-1z" class="X"></path><path d="M387 332c2 0 4 1 6 0 1 1 3 1 4 2 0 0-1 1-1 2-2-1-3-1-5-1-1-1-2-1-4-1l-4-1c1-1 3-1 4-1z" class="N"></path><path d="M373 442l1-2v1c0 1-1 2-2 3l-2 3c-2 1-4 5-6 5l-2 2 1 1h2l-3 3c-2 0-3 1-4 1l-15 9s-1 0-2-1c-3 0-8 1-12 2h0c-1-1-2-1-3-1v1h-1l-1-1h-1 1-1-1c2-1 2 0 3 0h1 2c-1-1-1-1-2-1h-6c-2 0-3 0-5-1h-1v-1h3l2-1 2 1v-1-1h2v-1h1v-1c-4 0-7-2-10-5h-1l-1-1v-2l2 1 1-1v-1h0c1 0 1 0 1-1l2 1h2l-1-2v-1l1-1h1l2 2v-1l2 2v-3l1 1c1 1 2 1 3 2l1 1h1c2 2 5 2 8 3h3c0-1 1-1 2-1 2 0 3-1 5-2l3-1 2 1h1 2l3-2-1 2h0c1 0 2 0 2-1 4-1 6-3 9-6l3-3z" class="M"></path><path d="M312 453l2 1 2 1v1h-2-1l-1-1v-2z" class="I"></path><path d="M315 452h0l2 1h1c1 2 2 2 3 4-3-1-4-2-6-4v-1z" class="N"></path><path d="M316 451l2 1h2l1 1c1 1 1 2 3 3l-1 1 1 1-3-1c-1-2-2-2-3-4h-1l-2-1c1 0 1 0 1-1z" class="G"></path><path d="M318 452h2l1 1c1 1 1 2 3 3l-1 1c-2-1-4-3-5-5z" class="M"></path><path d="M357 452l3-2-1 2h0c1 0 2 0 2-1-3 3-8 7-13 8h-1c-4 2-9 2-13 2l-1-1h1c3 0 6 0 9-1 3 0 7-2 10-4 2 0 3-2 4-3z" class="W"></path><path d="M349 452l3-1 2 1h1 2c-1 1-2 3-4 3-3 2-7 4-10 4 1-1 1-2 3-2 0-1-1-1-1-1h-3v-1h-3 3c0-1 1-1 2-1 2 0 3-1 5-2z" class="F"></path><path d="M349 455h0c2-2 3-2 5-2v1c-1 0-2 1-3 1h-2z" class="I"></path><path d="M355 452h2c-1 1-2 3-4 3h-2c1 0 2-1 3-1v-1l1-1z" class="J"></path><path d="M351 455h2c-3 2-7 4-10 4 1-1 1-2 3-2 0-1-1-1-1-1h-3c1 0 3 0 4-1h1v1h1l1-1h2z" class="D"></path><path d="M321 448l2 2v-1l2 2v-3l1 1c1 1 2 1 3 2l1 1h1c2 2 5 2 8 3h3v1h3s1 0 1 1c-2 0-2 1-3 2-3 1-6 1-9 1h-1-3c-3 0-4 0-6-2l-1-1 1-1c-2-1-2-2-3-3l-1-1-1-2v-1l1-1h1z" class="B"></path><path d="M324 452l3 2c-1 1-1 1-2 1h0l-1-2v-1z" class="X"></path><path d="M325 448l1 1c1 1 2 1 3 2l1 1h-3c-1 0-2-1-2-1v-3z" class="F"></path><path d="M325 448l1 1v2s1 0 1 1c-1 0-2-1-2-1v-3z" class="H"></path><path d="M327 454c2 1 5 2 8 4-2-1-4-1-6-1-1 0-3-1-4-2 1 0 1 0 2-1z" class="O"></path><path d="M330 460c-2 0-2-1-4-2 0 0-1 0-1-1h0l9 3h-1-3z" class="k"></path><path d="M321 453h3l1 2v2h0c0 1 1 1 1 1 2 1 2 2 4 2-3 0-4 0-6-2l-1-1 1-1c-2-1-2-2-3-3z" class="O"></path><path d="M321 448l2 2 1 2v1h-3l-1-1-1-2v-1l1-1h1z" class="L"></path><path d="M349 460h0c1 1 3 0 4-1 2-1 4-2 5-3 2-1 4-3 5-4h1l-2 2 1 1h2l-3 3c-2 0-3 1-4 1l-15 9s-1 0-2-1c-3 0-8 1-12 2h0c-1-1-2-1-3-1v1h-1l-1-1h-1 1-1-1c2-1 2 0 3 0h1 2c-1-1-1-1-2-1h-6c-2 0-3 0-5-1h-1v-1h3l2-1 2 1v-1-1h2v-1h1v-1c2 0 3 1 4 1h5c3 1 8 0 12-1 1 0 2-1 3-1h1z" class="D"></path><path d="M321 465h3c-1 0-3 0-4 1l-3-1 2-1 2 1z" class="E"></path><path d="M340 463l7-2c-1 2-2 2-3 3-1 0-2 1-3 1l-1-1v-1z" class="F"></path><path d="M341 467c4-1 7-3 11-6 3-2 7-4 10-7l1 1h2l-3 3c-2 0-3 1-4 1l-15 9s-1 0-2-1zm-17-6c2 0 3 1 4 1h5c3 1 8 0 12-1 1 0 2-1 3-1h1c-1 1-2 1-2 1l-7 2c-5 1-11 2-16 2h-3v-1-1h2v-1h1v-1z" class="W"></path><path d="M295 408h1 0l-1 3-4 10h0v3s1 1 0 2c0 2 0 3-1 5 1 1 1 2 2 3-1 4 0 9 2 13l1-1 1 2 1 2v2l5 7h0c2 1 3 2 5 2v1h2 1v2 2 1l4 1 1-2c2 1 3 1 5 1h6c1 0 1 0 2 1h-2-1c-1 0-1-1-3 0h1 1-1 1l1 1h1v-1c1 0 2 0 3 1h0c-3 1-5 0-8 0h-5c1 1 2 1 3 2h3l1 1h-8l-3-1-1 2h-2 0c-2 0-3 0-4-1-1 0-2 0-3-1l1-1-2-1-2-1c-3-2-5-4-7-6 0-1-2-2-2-2v-2l-1-2c-1-2-2-4-3-7v-2c-2-2-2-5-2-8 0-1-1-3-1-4 0-2 1-4 1-6h0c0-2 0-3-1-5 1-3 2-7 4-10v-2c1 0 2-1 3-2 0 0 1 0 1 1h1c0-1 2-2 2-3h1z" class="b"></path><path d="M308 471h4l-1 2h-2s0-1-1-2z" class="S"></path><path d="M303 470l5 1c1 1 1 2 1 2h0c-2 0-3 0-4-1-1 0-2 0-3-1l1-1z" class="F"></path><path d="M288 449c0 1 0 2 1 3l3 7h-1l-1-1-1-2c-1-2-2-4-3-7h2z" class="T"></path><path d="M295 446l1 2 1 2v2l5 7c0 1 0 1-1 1l-2-2c-1-2-3-5-4-7l-1-4h0l1-1z" class="j"></path><path d="M290 458l1 1h0c1 1 1 1 2 1s4 3 5 4c0 1 0 1 1 1 0 0 0 1 1 1 1 1 2 1 3 2 0 1-1 0-2 1l-2-1c-3-2-5-4-7-6 0-1-2-2-2-2v-2z" class="Q"></path><path d="M288 433h0c1-1 0-2 1-4v-1l1-1c1 1 0 3 0 4h0c1 1 1 2 2 3-1 4 0 9 2 13h0 0c-3-3-2-8-5-10v-2h0c-1 1-1 2-1 3 1 2 0 3 0 4l1 2v2c1 0 1 1 1 2 0 3 2 5 2 8l-2-2v-2h-1 0c-1-1-1-2-1-3-1-1-1-2-1-3s1-1 1-1v-2c-1-2-1-4-1-6l1-4z" class="c"></path><path d="M316 469l-7-2h-1c-2 0-3-1-5-2-5-3-7-5-10-10h1c1 1 2 3 3 4l2-1 2 2c1 0 1 0 1-1h0c2 1 3 2 5 2v1h2 1v2 2 1l4 1 1-2c2 1 3 1 5 1h6c1 0 1 0 2 1h-2-1c-1 0-1-1-3 0h1 1-1 1l1 1h1v-1c1 0 2 0 3 1h0c-3 1-5 0-8 0h-5z" class="H"></path><path d="M315 466c2 1 3 1 5 1l1 1c-1 0-5 1-6 0h-1l1-2z" class="a"></path><path d="M299 458l2 2c1 0 1 0 1-1h0c2 1 3 2 5 2v1h2 1v2 2 1l-6-3c-1 0-2 0-3-1-2-1-3-3-4-4l2-1z" class="d"></path><path d="M295 408h1 0l-1 3-4 10h0v3s1 1 0 2c0 2 0 3-1 5h0c0-1 1-3 0-4l-1 1v1c-1 2 0 3-1 4h0l-1 4c0 2 0 4 1 6v2s-1 0-1 1 0 2 1 3h-2v-2c-2-2-2-5-2-8 0-1-1-3-1-4 0-2 1-4 1-6h0c0-2 0-3-1-5 1-3 2-7 4-10v-2c1 0 2-1 3-2 0 0 1 0 1 1h1c0-1 2-2 2-3h1z" class="T"></path><path d="M286 447v-3h1v2c0 1 0 2 1 3h-2v-2z" class="j"></path><path d="M287 444c-1-3-1-6-1-9l1 2h0c0 2 0 4 1 6v2s-1 0-1 1v-2z" class="b"></path><path d="M290 420l1 1c-1 1-1 1-1 3h-1c-2 2-1 6-1 9l-1 4h0l-1-2 2-11h0c1-1 2-3 2-4z" class="W"></path><path d="M291 421h0 0v3s1 1 0 2c0 2 0 3-1 5h0c0-1 1-3 0-4l-1 1v1c-1 2 0 3-1 4h0c0-3-1-7 1-9h1c0-2 0-2 1-3zm-4-7c0 1 0 2-1 4s-1 5 0 7h0c0 1 0 1-1 2s-1 1-1 2h0c0-2 0-3-1-5 1-3 2-7 4-10z" class="Y"></path><path d="M295 408h1 0l-1 3-4 10h0l-1-1c0 1-1 3-2 4v-3-1l1-2c0-2 1-5 3-7 0-1 2-2 2-3h1z" class="V"></path><path d="M295 408h1 0l-1 3-4 10h0l-1-1v-1l5-11z" class="c"></path><path d="M524 540h1v-1c3 4 4 9 6 13 3 5 5 10 7 16 5 18 7 37 7 56 0 4-1 9-1 13l-3 9c-1 2-2 5-2 7v1c-1 2-1 4-4 5h-1v-1c0-1 0-1-1-2l1-2h-1l3-14c2-9 3-19 3-29 0-3 0-7-1-11 0-2 0-6-1-8l-4-23v-1c-1-1-1-4-2-5l-2-6-2-6-1 1v-1 1c-1-1-2-3-2-4v-4c-1-1-1-2-1-4h0 1z" class="U"></path><path d="M538 650l1 1-1 1v1h0c-1 1-1 2-2 2l2-5z" class="P"></path><defs><linearGradient id="u" x1="528.03" y1="551.596" x2="532.046" y2="561.591" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#727273"></stop></linearGradient></defs><path fill="url(#u)" d="M524 540h1c1 3 1 6 3 9 0 1 1 2 2 4s1 4 2 6c0 1 1 2 1 3l-2 1-2-6-2-6-1 1v-1 1c-1-1-2-3-2-4v-4c-1-1-1-2-1-4h0 1z"></path><path d="M523 540c2 4 3 8 4 11l-1 1v-1 1c-1-1-2-3-2-4v-4c-1-1-1-2-1-4z" class="U"></path><defs><linearGradient id="v" x1="514.456" y1="601.074" x2="552.649" y2="620.191" xlink:href="#B"><stop offset="0" stop-color="#6f7171"></stop><stop offset="1" stop-color="#939192"></stop></linearGradient></defs><path fill="url(#v)" d="M531 563l2-1 1 4c1 3 2 7 3 10 4 15 6 30 5 45 0 7 0 14-2 21l-1 5-1 3-2 5-2 4v-1c0-1 0-1-1-2l1-2h-1l3-14c2-9 3-19 3-29 0-3 0-7-1-11 0-2 0-6-1-8l-4-23v-1c-1-1-1-4-2-5z"></path><path d="M539 647h-1c0-2 1-5 1-6l1 1h0l-1 5z" class="S"></path><path d="M531 563l2-1 1 4-1 1v-2 3h0c-1-1-1-4-2-5z" class="X"></path><defs><linearGradient id="w" x1="132.051" y1="575.933" x2="102.06" y2="618.05" xlink:href="#B"><stop offset="0" stop-color="#414041"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#w)" d="M49 516c1 2 1 2 3 2 3 22 10 42 20 60 2 3 3 5 4 7a116.82 116.82 0 0 0 35 35c16 11 33 19 52 26 2 0 25 6 25 6v1l7 1c2 1 5 1 6 1-1 1-5 0-6 0s-2 0-3-1c-1 0-2 0-3 1h0c-2 0-4-1-6-1l-14-3c-25-7-50-18-71-35-24-21-40-51-47-81l-3-19h1z"></path><path d="M351 337c2-1 3-2 5-3 0-1 1-1 1-1v1l1 1h1v1l1 1h-1c1 1 1 1 2 1v2c3 0 6-1 8-1 3 0 6 0 8-1h1 2c1-1 1-1 2-1 1 1 2 1 3 1h0 1l5 1v2c3 1 6 1 8 2s3 2 5 3h0c-1-1-1-2-2-2l1-1c2 2 3 3 4 5 1 1 2 1 3 2s2 2 3 2l1 1c0 2 3 7 2 9v2 3h0l1 7-1 1c0 2 1 4 1 6v3h-1-1v-1-2c-1-2-2-4-4-6-1-3-3-6-6-8l-2-2c0-1 0-1-1-1l-1-1c0-1-1-1-2-2-2-2-4-4-7-6v-1-1h0 0v-1h-1c2-1 3-1 4-1h1l-3-2-3-2c-2 2-3 1-5 3 0 0-1-1-2-1-1-1-3-1-5-1v1h-2c-1-1-6 0-7 0h-7c0-1-1-1-2-1h-2c-1 0-1-1-2-1-1-1-2-3-4-4h0c0-1-1-3-1-3v-2-1z" class="L"></path><path d="M411 364l2-1v2l-1 1-1-2z" class="C"></path><path d="M410 361c1 0 1-1 1-1l2 3-2 1-1-3z" class="D"></path><path d="M382 344c3 0 6 0 9 1l-2 1h-1c-2-1-5-1-6-2h0z" class="Y"></path><path d="M391 345c3 2 8 4 10 7l-1 1c0-1-1-1-1-2l-6-3c-1-1-3-1-4-2l2-1z" class="T"></path><path d="M402 349h0c1 0 2 0 2 1 3 3 7 6 7 10 0 0 0 1-1 1h0c-1-1-1-2-1-3-1-3-4-7-7-8v-1z" class="E"></path><path d="M386 341l4 1c1 1 1 2 3 2v1h-1c-2-1-3-2-4-2l-10-1v-1h8zm-27 5c-3 0-5-3-7-5v-1l5 4c1 1 1 1 2 1 2-2 6-1 8-1-2 1-4 1-7 1v1h8c-3 1-6 0-9 0z" class="b"></path><path d="M395 343h0l3 3v1l3 2h1v1 1c-1-1-1-1-2-1 0-1 0 0-1-1-2-1-5-2-7-4h1v-1h1l1-1z" class="m"></path><path d="M395 343h0l3 3v1l-5-2v-1h1l1-1z" class="Q"></path><path d="M395 343c4 0 7 3 9 5l2 1-1 1h-1c0-1-1-1-2-1h0-1l-3-2v-1l-3-3z" class="L"></path><path d="M372 345h1l1-1c2-1 6-1 8 0h0c1 1 4 1 6 2h1c1 1 3 1 4 2v1l-3-2c-4-1-7-2-11-2-2 0-4 1-7 0z" class="o"></path><path d="M351 338c1 0 2 1 2 1l6 6c-1 0-1 0-2-1l-5-4v1c2 2 4 5 7 5 1 2 2 2 4 2 2 1 6 0 9 0 1 0 3-1 5 0h1 0v1h-2c-1-1-6 0-7 0h-7c0-1-1-1-2-1h-2c-1 0-1-1-2-1-1-1-2-3-4-4h0c0-1-1-3-1-3v-2z" class="V"></path><path d="M412 366l1-1c2 3 3 6 3 10 0 2 1 4 1 6v3h-1-1v-1-2c0-4-3-7-4-10h1c1 1 1 3 2 4 0-2-1-4-1-6 0-1-1-2-1-3z" class="K"></path><path d="M372 345c3 1 5 0 7 0 4 0 7 1 11 2-2 2-3 1-5 3 0 0-1-1-2-1-1-1-3-1-5-1h0-1c-2-1-4 0-5 0-3 0-7 1-9 0-2 0-3 0-4-2 3 0 6 1 9 0l4-1z" class="j"></path><path d="M403 356c1 1 2 2 3 4 0 1 1 1 1 2h1c1 1 1 2 1 3 1 0 1 1 1 2l1 1v3c1 3 4 6 4 10-1-2-2-4-4-6-1-3-3-6-6-8l1-1c-1-1-1-1-1-2-1-2-2-4-4-5-1-1-1-1-1-2l1-1v1l2-1z" class="Q"></path><path d="M382 339h0v-1h2 1 1l5 1v2c3 1 6 1 8 2s3 2 5 3l1 1-1 1c-2-2-5-5-9-5h0l-1 1h-1c-2 0-2-1-3-2l-4-1c-3-1-6 0-9 0s-7 0-10 1h-6v-1c3 0 6-1 9-1h7c1-1 1-1 2-1h3 0z" class="M"></path><path d="M390 342c2 0 3 1 5 1l-1 1h-1c-2 0-2-1-3-2z" class="d"></path><path d="M382 339h0v-1h2 1 1l5 1v2l-9-2z" class="S"></path><path d="M396 351c2 1 4 3 7 5l-2 1v-1l-1 1c0 1 0 1 1 2 2 1 3 3 4 5 0 1 0 1 1 2l-1 1-2-2c0-1 0-1-1-1l-1-1c0-1-1-1-2-2-2-2-4-4-7-6v-1-1h0 0v-1h-1c2-1 3-1 4-1h1z" class="c"></path><path d="M392 352c5 2 7 4 9 8l-9-6v-1h0 0v-1z" class="i"></path><path d="M351 337c2-1 3-2 5-3 0-1 1-1 1-1v1l1 1h1v1l1 1h-1c1 1 1 1 2 1v2c3 0 6-1 8-1 3 0 6 0 8-1h1 2c1-1 1-1 2-1 1 1 2 1 3 1h0-1-2v1h0 0-3c-1 0-1 0-2 1h-7c-3 0-6 1-9 1v1l-1 1h-1c-1 0-1-1-2-1-1-1-2-2-4-3 0 0-1-1-2-1v-1z" class="G"></path><path d="M351 337c2-1 3-2 5-3 0-1 1-1 1-1v1l1 1h1v1h-4v1l2 1h0c-2 0-2-1-4-1 1 1 5 5 6 5 0 0 1 0 1 1h-1c-1 0-1-1-2-1-1-1-2-2-4-3 0 0-1-1-2-1v-1z" class="c"></path><path d="M404 346h0c-1-1-1-2-2-2l1-1c2 2 3 3 4 5 1 1 2 1 3 2s2 2 3 2l1 1c0 2 3 7 2 9v2 3h0l1 7-1 1c0-4-1-7-3-10v-2l-2-3c0-4-4-7-7-10h1l1-1-2-1 1-1-1-1z" class="R"></path><path d="M404 350h1c4 1 7 6 8 10 1 2 1 5 3 7l1 7-1 1c0-4-1-7-3-10v-2l-2-3c0-4-4-7-7-10z" class="n"></path><path d="M353 282c1-2 2-4 3-5l4-4c1-2 5-3 7-5h1s0 1 1 1c0 0 1 0 1 1 4-1 8-1 13 0v1c1 0 2 1 3 1h0l1 1 5 2h1l3 3c2 1 5 4 6 6 0 1-1 1-1 1l-1 1 1 1h0-1 0c-4-3-9-3-13-3-3 1-5 1-7 1l-6 3c-3 0-5 3-7 4v2c-3 2-5 4-7 7-1 0-2 1-2 2v1 1l-2 2v3 2l-1 1-1-1v-1h-1v-3h-1 0c0 2 0 2-1 3h-1v-3c1 0 1-1 1-1v-3c-1-2-1-4-1-6h0l1-4h-1c1-3 1-5 1-7l1-3h1v-1-1z" class="D"></path><path d="M356 303h0c1-1 1-2 1-3h0c1-2 2-4 4-5v1c-1 1-3 3-4 5h1c1-1 2-3 3-4l2-2v-1c2-2 2-2 4-2v2c-3 2-5 4-7 7-1 0-2 1-2 2v1 1l-2 2c0-1 1-2 0-4z" class="g"></path><path d="M356 299v-1c0-2 3-6 6-7s5-4 8-5 6-3 10-4v1h1 0c-3 2-7 2-11 4-2 1-5 4-8 5-1 1-5 5-5 6-1 2-1 3-1 5h0c1 2 0 3 0 4v3 2l-1 1-1-1v-1h-1v-3h0c-1-1-1-1-1-2l1-5h1c1 0 1-1 2-2z" class="O"></path><path d="M356 299v1s0 1-1 1l-1 1v4c1 0 1-3 1-4 1 0 1 0 1 1h0c1 2 0 3 0 4v3 2l-1 1-1-1v-1h-1v-3h0c-1-1-1-1-1-2l1-5h1c1 0 1-1 2-2z" class="c"></path><path d="M354 311v-4h1v2l1 1v2l-1 1-1-1v-1z" class="V"></path><path d="M378 280l1-1h5l1 1-3 1v1h-2c-4 1-7 3-10 4s-5 4-8 5-6 5-6 7v1c-1 1-1 2-2 2h-1l-1 5c0-3 0-6 1-9l1-2c1-1 3-3 3-5 1-2 1-3 3-4l6-4v1c-1 0-1 1-2 2 3-1 5-2 7-3l1-1c2 0 4 0 6-1z" class="C"></path><path d="M364 285c3-1 5-2 7-3l-1 1c1 1 1 1 2 1h0c-3 1-7 2-10 4-1 1-5 6-5 6 1-4 4-7 7-9z" class="G"></path><defs><linearGradient id="x" x1="381.309" y1="284.476" x2="373.777" y2="278.422" xlink:href="#B"><stop offset="0" stop-color="#6a6b6f"></stop><stop offset="1" stop-color="#7d7d7a"></stop></linearGradient></defs><path fill="url(#x)" d="M378 280l1-1h5l1 1-3 1-10 3h0c-1 0-1 0-2-1l1-1 1-1c2 0 4 0 6-1z"></path><path d="M366 282v1c-1 0-1 1-2 2-3 2-6 5-7 9-1 0-2 1-3 2h0l-1 3v1 1l-1 5c0-3 0-6 1-9l1-2c1-1 3-3 3-5 1-2 1-3 3-4l6-4z" class="B"></path><path d="M382 273h3 0s1 0 1-1l1 1 5 2h1l3 3c2 1 5 4 6 6 0 1-1 1-1 1l-1 1 1 1h0-1 0c-4-3-9-3-13-3h-4v-1h-2-1v-1h2v-1l3-1-1-1h-5l-1 1c-2 1-4 1-6 1l-1 1c-2 1-4 2-7 3 1-1 1-2 2-2v-1-1h0l-1-1-4 2c1-2 2-3 4-4 1-1 3-2 4-4l1 1h0c4-2 8-2 12-2z" class="G"></path><path d="M387 273l5 2h-1c-2 0-3 0-5-1l1-1z" class="S"></path><path d="M365 280v-1c7-2 13-3 21-2-1 0-3 1-3 1h-5v1 1c-2 1-4 1-6 1l-1 1c-2 1-4 2-7 3 1-1 1-2 2-2v-1-1h0l-1-1z" class="D"></path><path d="M371 280c-1 0-2 2-4 2v-1c2-2 5-3 8-3 2-1 6-1 8 0h-5v1 1c-2 1-4 1-6 1l-1-1z" class="I"></path><path d="M371 280l7-2v1 1c-2 1-4 1-6 1l-1-1z" class="B"></path><path d="M387 277c0-1 0-1 1-1 2 0 3 0 4 1 2 1 2 1 4 1 2 1 5 4 6 6 0 1-1 1-1 1l-1 1 1 1h0-1 0c-4-3-9-3-13-3h-4v-1h-2-1v-1h2v-1l3-1-1-1h-5l-1 1v-1-1h5s2-1 3-1h1z" class="p"></path><path d="M379 279h11c1 0 2 1 3 1l-1 2c-3-1-6-1-10 0v-1l3-1-1-1h-5z" class="O"></path><g class="Z"><path d="M387 277c0-1 0-1 1-1 2 0 3 0 4 1 2 1 2 1 4 1 2 1 5 4 6 6 0 1-1 1-1 1s-1 0-1-1c-3-4-8-6-13-7z"></path><path d="M393 280c1 1 2 1 3 2 1 0 2 0 2 1 1 0 1 1 2 1 0 1 1 1 1 1l-1 1 1 1h0-1 0c-4-3-9-3-13-3h-4v-1h-2-1v-1h2c4-1 7-1 10 0l1-2z"></path></g><path d="M383 283c4 0 9 0 13 1 0-1 1-1 2-1s1 1 2 1c0 1 1 1 1 1l-1 1 1 1h0-1 0c-4-3-9-3-13-3h-4v-1z" class="e"></path><path d="M396 284c0-1 1-1 2-1s1 1 2 1c0 1 1 1 1 1l-1 1-4-2z" class="L"></path><path d="M353 282c1-2 2-4 3-5l4-4c1-2 5-3 7-5h1s0 1 1 1c0 0 1 0 1 1 4-1 8-1 13 0v1c1 0 2 1 3 1h0c0 1-1 1-1 1h0-3c-4 0-8 0-12 2h0l-1-1c-1 2-3 3-4 4-2 1-3 2-4 4l4-2 1 1h0v1l-6 4c-2 1-2 2-3 4 0 2-2 4-3 5l-1 2c-1 3-1 6-1 9 0 1 0 1 1 2h0-1 0c0 2 0 2-1 3h-1v-3c1 0 1-1 1-1v-3c-1-2-1-4-1-6h0l1-4h-1c1-3 1-5 1-7l1-3h1v-1-1z" class="B"></path><path d="M362 274v-1c2-1 4-2 6-2-1 1-1 2-3 2v1h-3z" class="E"></path><path d="M359 280c1 0 1 0 2 1-2 1-3 2-5 4l-1 3-1-1c1-3 3-5 5-7z" class="O"></path><path d="M355 282c-1 3-2 5-3 7 0 2 0 4-1 5h-1c1-3 1-5 1-7l1-3h1v-1-1h2z" class="M"></path><path d="M359 280l6-6 2 1-5 5-1 1c-1-1-1-1-2-1z" class="I"></path><path d="M352 296l1 1c-1 3-1 6-1 9 0 1 0 1 1 2h0-1 0c0 2 0 2-1 3h-1v-3c1 0 1-1 1-1v-3l1-8z" class="T"></path><path d="M365 274l2-1c1 0 1-1 2-1 5-1 9-2 14-1 1 0 2 1 3 1h0c0 1-1 1-1 1h0-3c-3-2-10 0-14 1l-1 1-2-1z" class="O"></path><path d="M353 282c1-2 2-4 3-5l4-4c1-2 5-3 7-5h1s0 1 1 1c0 0 1 0 1 1l-2 1c-2 0-4 1-6 2v1c-4 2-6 4-7 8h-2z" class="G"></path><path d="M367 275l1-1c4-1 11-3 14-1-4 0-8 0-12 2h0l-1-1c-1 2-3 3-4 4-2 1-3 2-4 4l4-2 1 1c-3 1-7 3-9 5l-1-1c2-2 3-3 5-4l1-1 5-5z" class="U"></path><path d="M366 281h0v1l-6 4c-2 1-2 2-3 4 0 2-2 4-3 5l-1 2-1-1c0-3 2-6 2-9l1 1 1-3 1 1c2-2 6-4 9-5z" class="J"></path><path d="M356 285l1 1c0 3-2 6-4 8 1-2 2-4 2-6l1-3z" class="B"></path><path d="M262 217c3 0 6 0 8 1l7 1-1 1 3 1c2 0 4 1 6 3 3 2 7 3 11 4 2 1 5 3 8 4 10 6 17 15 23 24h1l1 2c2 4 5 8 6 12v1h-1c1 2 1 4 2 6 0 3 2 7 2 10h-2l-6-20h-1c4 9 6 18 7 27 0 2 1 4 0 6v-1c0 1 0 1-1 2 0-1 0-1-1-1h-1c0-5-3-10-4-15 0-2-1-3-1-4l-1-2v-2s0-1 1-1c-3-6-6-12-10-18-6-8-15-17-24-22-10-5-21-8-31-10h-1c-1 0-1 0-2-1h1 3 1l1-1h1v-1h-1v-1h-5-3l-1-1-1-1-1-1v-1c2-1 5-1 7-1z" class="P"></path><path d="M267 223c1 1 4 1 5 2h-1c-1 0-3 0-4-1v-1z" class="C"></path><path d="M329 277l7 22c0 1 0 1-1 2 0-1 0-1-1-1-1-7-2-15-5-21v-2z" class="b"></path><path d="M328 276l1 3c3 6 4 14 5 21h-1c0-5-3-10-4-15 0-2-1-3-1-4l-1-2v-2s0-1 1-1z" class="p"></path><path d="M261 225h3c5 1 9 2 13 4 15 5 29 13 39 26 5 6 11 14 14 22h-1v2l-1-3c-3-6-6-12-10-18-6-8-15-17-24-22-10-5-21-8-31-10h-1c-1 0-1 0-2-1h1z" class="g"></path><path d="M262 217c3 0 6 0 8 1l7 1-1 1 3 1-1 1c3 1 5 2 7 3 15 7 30 17 38 31l5 8c0 1 1 2 2 3h-1l-9-13c-6-8-12-14-20-19-7-5-13-8-21-10-4-2-9-2-13-3h-5-3l-1-1-1-1-1-1v-1c2-1 5-1 7-1z" class="I"></path><path d="M262 217c3 0 6 0 8 1l7 1-1 1 3 1-1 1c-7-1-14-1-21-1l-1-1-1-1v-1c2-1 5-1 7-1z" class="P"></path><path d="M262 217c3 0 6 0 8 1l7 1-1 1c-7-2-14-2-21-1v-1c2-1 5-1 7-1z" class="I"></path><path d="M279 221c2 0 4 1 6 3 3 2 7 3 11 4 2 1 5 3 8 4 10 6 17 15 23 24h1l1 2c2 4 5 8 6 12v1h-1c1 2 1 4 2 6 0 3 2 7 2 10h-2l-6-20c-1-1-2-2-2-3l-5-8c-8-14-23-24-38-31-2-1-4-2-7-3l1-1z" class="K"></path><path d="M328 256l1 2c2 4 5 8 6 12v1h-1l-1-3v-1c-1 0-1 0-1-1 0 0 0-1-1-1 0-1 0-2-1-2s0 1 0 2c-2-1-3-5-4-7v-1c1 1 1 1 2 1 0 0 0-1-1-2h1z" class="H"></path><path d="M504 547c2 3 2 6 3 9 3 7 5 15 7 23 0 4 0 9 1 13v1 9 3c0 1 1 1 1 2v3 9h-1 1c0 3 0 5-1 8v7c0 2-1 4-1 6-2 2-2 5-4 8l-1-1-1 2v-1l-1 1h0c0-1-1-1-1-1l-1 2h1v1 1 3c-1 2-2 5-3 7h-1l1-1h0l-1-1v-1h0l-1 2-1-1c-2 4-4 9-7 12-11 17-26 30-45 35l-10 2c0-1 0-1-1-1h0l1-1c3 0 6-2 9-4 3-1 7-3 10-6 4-2 8-4 11-7s6-6 9-10c5-5 8-11 12-17 2-4 5-9 7-14l2-6c1-2 1-3 2-5v1l1-2v3c1-1 1-3 1-4v-4h0v-3-3c1-1 1-3 1-3l2-12v-1l-1-1h0c0-2 1-5 2-6 0-6 0-11-1-16 0-7-1-14-2-20-1-4-3-8-3-12v-1l1-1 3-6z" class="e"></path><path d="M505 650h1v1 1 3c-1 2-2 5-3 7h-1l1-1h0l-1-1v-1h0l-1 2-1-1c1-3 4-7 5-10z" class="O"></path><path d="M515 607v-2c0 1 1 1 1 2v3 9h-1 1c0 3 0 5-1 8v7c0 2-1 4-1 6-2 2-2 5-4 8l-1-1-1 2v-1l-1 1h0c0-1-1-1-1-1 1-6 4-11 5-16s2-11 3-17c0-2 0-5 1-8h0z" class="I"></path><path d="M509 644h1l-1 3-1 2v-1l-1 1c0-2 1-4 2-5z" class="a"></path><path d="M515 607v-2c0 1 1 1 1 2v3 9h-1c0 5-1 10-2 14l-2 7c0 1-1 3-1 4h-1c2-6 3-11 4-17 1-7 3-14 2-20z" class="H"></path><path d="M515 619h1c0 3 0 5-1 8v7c0 2-1 4-1 6-2 2-2 5-4 8l-1-1 1-3c0-1 1-3 1-4l2-7c1-4 2-9 2-14z" class="O"></path><defs><linearGradient id="y" x1="515.711" y1="581.731" x2="494.748" y2="574.872" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#5e5d5f"></stop></linearGradient></defs><path fill="url(#y)" d="M500 554l1-1h1c1 5 2 9 3 13 3 9 4 19 5 28v13l-1-5h0c-1 1-1 2-1 3 0-3-1-7-1-11 0 1 0 1-1 2h0v7h0c0-6 0-11-1-16 0-7-1-14-2-20-1-4-3-8-3-12v-1z"></path><path d="M506 603v-7h0c1-1 1-1 1-2 0 4 1 8 1 11 0-1 0-2 1-3h0l1 5c-1 10-2 20-5 30-1 3-2 6-3 10h-1 0-1c0-3 1-5 1-7 1-1 1-3 1-4v-4h0v-3-3c1-1 1-3 1-3l2-12v-1l-1-1h0c0-2 1-5 2-6h0z" class="R"></path><path d="M507 617v2c0 4-1 7-2 11-1 0-1 0-1-1 0-2 1-4 2-7l1-5z" class="C"></path><path d="M508 605c0-1 0-2 1-3 0 3-1 5-1 8s0 6-1 9v-2l1-12z" class="E"></path><path d="M502 629h1 1c0 1 0 1 1 1 0 3-1 5-2 8v-1-2h-1v1h0v-4h0v-3z" class="D"></path><path d="M506 603v-7h0c1-1 1-1 1-2 0 4 1 8 1 11l-1 12-1 5c-1 3-2 5-2 7h-1-1v-3c1-1 1-3 1-3l2-12v-1l-1-1h0c0-2 1-5 2-6h0z" class="a"></path><path d="M504 609h0c0-2 1-5 2-6h0v8-3c-1 1-1 2-1 3v-1l-1-1z" class="D"></path><path d="M505 611c0-1 0-2 1-3v3l-1 8v3h1c-1 3-2 5-2 7h-1-1v-3c1-1 1-3 1-3l2-12z" class="U"></path><path d="M505 619v3h1c-1 3-2 5-2 7h-1l2-10z" class="R"></path><path d="M500 638v1l1-2v3c0 2-1 4-1 7h1 0 1l-4 11c-1 2-3 4-4 6-4 7-9 15-16 21-1 1-3 3-5 4 0 0-1 1-2 1-1 1-1 2-2 3h-1c-1 1-2 1-3 2v-1c1-1 3-2 4-4h-1c3-3 6-6 9-10 5-5 8-11 12-17 2-4 5-9 7-14l2-6c1-2 1-3 2-5z" class="U"></path><path d="M500 647h1 0 1l-4 11c-1 2-3 4-4 6h-1c1-3 3-6 4-9l3-8z" class="J"></path><path d="M493 664h1c-4 7-9 15-16 21-1 1-3 3-5 4 0-1 3-3 3-4 7-6 12-13 17-21z" class="R"></path><defs><linearGradient id="z" x1="360.104" y1="433.588" x2="319.761" y2="364.416" xlink:href="#B"><stop offset="0" stop-color="#d2d1d2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#z)" d="M351 351l1-1v-2h1v-2-1l3 3s1 1 2 1h0l5 2c1 1 2 1 3 1s1 0 1-1h1-1-2-2c-1 0 0 0-1-1h-1c-1 0-1 0-2-1l1-1c1 0 2 0 2 1h7c1 0 6-1 7 0h2v-1c2 0 4 0 5 1 1 0 2 1 2 1 2-2 3-1 5-3l3 2 3 2h-1c-1 0-2 0-4 1h1v1h0 0v1 1c3 2 5 4 7 6 1 1 2 1 2 2l1 1v1h-1 0c-2-2-3-2-5-2l-3-2c0 1 1 2 2 3l-1 2 1 2c0 2 1 4 3 6h0v1s1 1 1 2h0v3h0c0-1-1-1-1-2l2 5-1 1v1h-1c0 1-1 1-1 2h-1l-5-9c-1-2-2-3-3-4h-1-1v1c1 1 1 1 1 2h0 0c-1 0-1 1-1 2 3 2 5 7 6 10l1 2v2l-1-1v4 1h-1v-1 1h0l-1 2h0v1l1 3c0 2 1 4 1 5 1 9 0 16-3 24 0 1-1 2-1 2h-1v1c-1 0-1 0-1-1l-2 5h-1l-1-1 1-3h-1v-3l-2 4h-1-2v-1l-1 1h-1c-1 1-2 3-3 5h0l1 1-3 3c-3 3-5 5-9 6 0 1-1 1-2 1h0l1-2-3 2h-2-1l-2-1-3 1c-2 1-3 2-5 2-1 0-2 0-2 1h-3c-3-1-6-1-8-3h-1l-1-1c-1-1-2-1-3-2l-1-1v3l-2-2v1l-2-2h-1l-1 1v1l1 2h-2l-2-1c0 1 0 1-1 1h0v1l-1 1-2-1v2l1 1h1c3 3 6 5 10 5v1h-1v1h-2v1 1l-2-1-2 1h-3v1h1l-1 2-4-1v-1-2-2h-1-2v-1c-2 0-3-1-5-2h0l-5-7v-2l-1-2-1-2-1 1c-2-4-3-9-2-13-1-1-1-2-2-3 1-2 1-3 1-5 1-1 0-2 0-2v-3h0l4-10 1-3h0-1-1c0 1-2 2-2 3h-1c0-1-1-1-1-1-1 1-2 2-3 2v2c-2 3-3 7-4 10v1h-2 0-1v-1l2-7c1-1 1-3 2-4s1-1 1-3c0 1-1 2-2 3h-1c1-1 1-2 1-3l-1 1-1-1 5-6h1c2-1 3-3 4-4l3-2h0c0-1 1-1 2-2h-1-1v-1-1l-3 1 1-1h1l1-1c2 0 3-1 4-1 7-4 14-5 21-8 4-2 7-4 11-6 1-1 2-2 4-2 1 0 3-1 4-2h2c1-1 5 1 6 2h6 0-4c-1-1-2-1-3-1-1-1-3-3-4-5s-1-6 0-8 3-3 4-4c2-2 4-4 6-7h0z"></path><path d="M366 404c-1-1 0-3-1-5v-2h1l1 2h0c-1 1-1 4-1 5z" class="i"></path><path d="M375 370c-1-2-3-2-4-4h1l3 2-1 1h1v1z" class="l"></path><path d="M354 422h1c-1 2-1 4-3 5h0v-1c0-1 1-1 1-2h0v-1l1-1h0z" class="T"></path><path d="M314 420v4c0-2 1-1 2-2 0-1-1-1 0-1 0 0 0-1 1-1l-1 1c0 2-1 3-2 4l-1 1h0c0-2 0-4 1-6z" class="V"></path><path d="M375 358c-1 0-2 0-2-1-3 0-6 1-9 0h0c4-1 8-1 11 0v1z" class="l"></path><path d="M369 383c-2-2-4-4-5-6l6 3c1 0 1 1 1 1-1 0-2-1-4-2 0 2 2 3 2 4z" class="V"></path><path d="M372 380c-2 0-2-2-4-3h0c-1-1-2-1-2-2 1 0 4 1 5 2v1c0 1 0 2 1 2z" class="f"></path><path d="M318 420c0 2-1 3 0 4v4h1c0-2-1-3 0-5v5c0 2 0 3-1 4v-1c-1-3-1-7 0-11z" class="Y"></path><path d="M392 364l2 2 1 2c0 2 1 4 3 6h0v1h0-1 0c0-2-1-3-2-4 0-1 0-1-1-1 0-1-1-2-1-2l1-1-1-1c0-1-1-1-1-2h0z" class="T"></path><path d="M354 438l-1-1c1-1 3-3 5-3h0c1 0 2-1 2-2 1-1 2-2 2-3h0l1 1c-2 2-4 5-7 7h-1c-1 0-1 0-1 1z" class="j"></path><path d="M353 404v-5h0v-2l-1-1c0-1-1-2-1-3 2 1 2 3 2 4 1 1 1 1 1 2h0v1l1 4v2c0 1 1 3 0 4-1-1-1-3-1-4v-1s0-1-1-1z" class="l"></path><path d="M366 404c0-1 0-4 1-5l2 10h0v3l-1-1h0l-1 1v-3-1c0-1-1-2-1-4zm10 2v2c1 2 0 5 0 7h-1v-2h-1v-7h1 1z" class="T"></path><path d="M375 357c3 0 5 1 7 1v1c1 0 2 0 2 1h1l1 1c2 1 2 2 3 3v1l-1-1c-1-2-3-4-6-4h0-2c-1-1-2-1-3-1 0 0-1-1-2-1v-1z" class="V"></path><path d="M292 424l1 2h0l1-1v-2c0-2 1-3 2-4v1c0 1 0 1-1 2v1h0v2h-1v1 2 1c-1 1 0 1-1 2h-1v-4h0v-1-2h0z" class="Y"></path><path d="M347 397c1 3 2 7 2 9 1 3 0 6 0 9 0 2 0 4-1 5h0 0c1-3 1-10 1-13l-2-6v-4z" class="c"></path><path d="M318 399l1-2h1c-1 3-3 5-4 8-1 1-1 1-1 2-1 1-1 2-1 3l-1-1v-1c0-1 1-1 1-2 1-3 2-5 4-7zm-23 12l1 1c-1 4-3 8-4 12h0c0-2 1-5 2-7v-1c0 1-1 2-1 3v1h-1 0v3c-1-1-1-1-1-2h0l4-10zm-3 23v2c1 1 0 2 0 3 1 1 1 3 1 4v-1c0-2 0-3-1-5h0c1-1 1-2 1-3 1-1 0-2 1-3v-1c0-1 1-2 2-3h0c-1 1-1 1-1 2-1 2-1 5-2 7 0 3 1 7 2 10l-1 1c-2-4-3-9-2-13z" class="V"></path><path d="M298 408h0c1-1 1-1 1-2s2-3 3-3h0c0 1 0 2-1 2v2c-1 1-1 2-2 3v1l-1 2v2h0l-1 1v2h0c0 1-1 1-1 2v-1l1-6c1-1 1-3 2-4l-1-1z" class="T"></path><path d="M351 426c0 1-1 1-1 2v1h1v1c-1 0-1 1-2 1v1h0c-1 1-4 3-4 5h-3l1-1 1-1c0-1 1-1 1-2 1-1 3-2 4-4 0-1 1-2 2-3z" class="j"></path><path d="M298 408l1 1c-1 1-1 3-2 4l-1 6c-1 1-2 2-2 4v2l-1 1h0l-1-2c1-4 3-8 4-12l2-4z" class="c"></path><path d="M368 398l-3-7v-1c2 2 3 5 3 7 3 6 5 13 4 19l-1 3v1l-1-1c2-8 0-14-2-21z" class="m"></path><path d="M326 428c0-2 0-6 1-7-1-1-1-2 0-2v-2c0-1 1-1 1-2v5h0c1-1 1-2 2-3v1c0 1-1 3-1 4v3 1c-1 0-1 1-2 1l-1 1z" class="V"></path><path d="M349 422c0-1 1-2 1-3 1-2 2-4 2-6h1c0-1 0-1-1-2l1-1v-5-1h0c1 0 1 1 1 1v1c0 1 0 3 1 4-1 2 0 3-1 5h0c-1 0-1 1-1 2h0s0 2-1 2v1s0 1-1 2l-1 3c-1 1-1 1-2 1h0c0-2 1-3 1-4z" class="T"></path><path d="M390 347l3 2 3 2h-1c-1 0-2 0-4 1h1v1h0 0c-2 0-4-1-5-2-4-1-7-1-11-1h-4 0 1c2-1 5 0 8-1 1 0 2 1 2 1 0-1 0-1-1-1h-2c0-1-1 0-2-1 2 0 4 0 5 1 1 0 2 1 2 1 2-2 3-1 5-3z" class="f"></path><path d="M390 347l3 2 3 2h-1c-1 0-2 0-4 1l-6-2c2-2 3-1 5-3z" class="T"></path><path d="M348 426h0c1 0 1 0 2-1l1-3c1-1 1-2 1-2v-1c1 0 1-2 1-2h0c0-1 0-2 1-2h0v4h0c-1 0-1 0-1 1 0 0-2 5-2 6-1 1-2 2-2 3-1 2-3 3-4 4 0 1-1 1-1 2l-1 1-1 1s-1 0-1 1c-1 0-2-1-3-1h1c1-1 3-2 4-4s3-5 5-7zm-33-14c1-2 1-4 2-6h1c-1 5-3 9-4 14-1 2-1 4-1 6s-1 5-1 7l-1-4v-8l1-4 1-4v1l2-2z" class="c"></path><path d="M312 417v2h1c0 1 0 3-1 4l-1 1v-3l1-4z" class="T"></path><path d="M315 412l-2 7h-1v-2l1-4v1l2-2z" class="i"></path><path d="M374 352c4 0 8 1 11 3 1 0 2 1 2 1h1l2 2s3 2 3 3 1 2 2 3l-1 2-2-2h0l-3-2-1-1c0-1-2-2-3-3v-1h1 0c-3-3-8-4-13-4l1-1z" class="j"></path><path d="M390 358s3 2 3 3 1 2 2 3l-1 2-2-2-2-2c0-1 1-1 2-1-1-1-2-2-2-3z" class="m"></path><path d="M325 405v1h0v2h1c0-1 0-1 1-1 0 1-1 2 0 2v-1h0v2 1 2c1-1 0-3 1-3 1 1 1 1 0 2v3c0 1-1 1-1 2v2c-1 0-1 1 0 2-1 1-1 5-1 7v3h0-1v-1-5-2c0-1-1-2-1-3h0c1-2 1-5 1-8h1c0-1 0-1-1-2v1h-1l1-6z" class="l"></path><path d="M336 410c0-3 0-6 1-9 0 2 0 11 1 12h-1v2h0c-1 1 0 3-1 4v1 2c1 0 1 1 1 1 0-1 1-2 1-4 1-1 0-2 2-3v-1h1 0v1 1l-1 4s-1 0-1 1v1l-1 1s0 1-1 2l-2 2c0 1-1 2-2 3l-1-1c0-1 1-2 1-3s1-1 1-2h0v-1l1-1c1-1 0-4 1-5 0-1 0-4 1-5v-1h0 0l-1-2z" class="i"></path><path d="M343 428h0c1-1 1-2 2-3l1-1c0-1 1-2 2-3v1h1c0 1-1 2-1 4-2 2-4 5-5 7s-3 3-4 4h-1c-1 0-5 1-7 1l6-3h0 0c0-1 5-5 6-7z" class="O"></path><path d="M343 428h0c1-1 1-2 2-3l1-1c0-1 1-2 2-3v1c-1 4-7 11-11 13h0c0-1 5-5 6-7z" class="V"></path><path d="M324 420h0c0 1 1 2 1 3v2 5 1h1v2l3 2h0 0l1-1h1c1 0 2-2 3-3v1c0 1 0 1-1 1l-1 2h-2v1c1 1 2 1 1 2-1 0-1 0-2-1h-2v1l2 1c-1 0-1 0-1 1h0c-2-1-3-2-4-4h0v-2c0-1-1-1-1-1 0-1 0-2-1-3 0-3 0-5 1-8 0-1 0-1 1-2z" class="Y"></path><path d="M326 433h0-1c-1-1-1-2-1-3h0 1v1h1v2z" class="c"></path><path d="M336 410l1 2h0 0v1c-1 1-1 4-1 5-1 1 0 4-1 5l-1 1v1h0c0 1-1 1-1 2s-1 2-1 3l1 1-4 3v1h0 0l-3-2v-2h0c1 1 1 1 2 0 0-1 1-1 1-2 2-4 3-8 4-12h1c-1 1-1 2-1 2h1v-3c2-1 2-3 2-6z" class="Y"></path><path d="M329 429h1 0l1-2h1v1c0 1-1 2-2 3h-2c0-1 1-1 1-2z" class="T"></path><path d="M332 428l1-1c0 1-1 2-1 3l1 1-4 3v1h0 0l-3-2v-2h0c1 1 1 1 2 0h2c1-1 2-2 2-3z" class="R"></path><path d="M300 413c0-1 0-2 1-3s2-1 2-3v1-2l1-2s0-1 1-1h1s-1 0-1 1c1-1 2-2 3-2l1-1c1-2 4-4 5-5 1 0 1 1 1 1-1 1-3 2-3 3-1 1-1 2-2 2h0v2c-1 1-1 1-1 2s0 1-1 1v1c0 1 0 1-1 2l-1 2v-1c0-1 0-1 1-2v-2l1-1h-1c-1 1-1 2-1 3l-2 3h-1c-1 1-2 2-3 4v1l-1-1c0-1 0-2 1-3z" class="f"></path><path d="M300 413s1 0 1-1 1-2 1-2c1-1 2-3 3-4h0l-2 6c-1 1-2 2-3 4v1l-1-1c0-1 0-2 1-3z" class="c"></path><path d="M375 370v-1h-1l1-1c1 1 2 1 3 2h1l1 2c2 2 4 4 5 6v1c-1 1 1 1 1 3h-1v-1c-1-1-1-1-1-2h-1 0c-1-1-2-2-4-3l-1 1v-1c0 2 1 2 1 4l-2-2v1l-1-1c0-2-4-4-5-6-1 0-2-1-2-2 1 0 2 0 3 1 1-1 2-1 3-1z" class="f"></path><path d="M379 376l-1-1 1-2c-1-1-1-1-1-2-1 0-1-1-2-1 2 0 3 1 4 2 2 2 4 4 5 6v1c-1 1 1 1 1 3h-1v-1c-1-1-1-1-1-2h-1 0c-1-1-2-2-4-3z" class="T"></path><path d="M330 436c2 0 3-1 5-2 3-1 5-4 8-6-1 2-6 6-6 7h0 0l-6 3c2 0 6-1 7-1s2 1 3 1c0-1 1-1 1-1h3c1-1 2-2 4-3-1 2-2 3-4 4 0 1-1 1-1 2-2 0-3 1-5 1-1 1-2 0-4 2h2 0-3-1v-1h0s-1-1-2-1c0-1-1-1-2-2l-2-1v-1h2c1 1 1 1 2 1 1-1 0-1-1-2z" class="d"></path><path d="M345 437c1-1 2-2 4-3-1 2-2 3-4 4 0 1-1 1-1 2-2 0-3 1-5 1-1 1-2 0-4 2h2 0-3-1v-1h0s-1-1-2-1c0-1-1-1-2-2l-2-1h1c1 0 1 0 2 1h1l1 1h4c1-1 2 0 3-1h1l1-1c0-1 1-1 1-1h3z" class="b"></path><path d="M319 423l6-19h0v1l-1 6h1v-1c1 1 1 1 1 2h-1c0 3 0 6-1 8-1 1-1 1-1 2-1 3-1 5-1 8 1 1 1 2 1 3 0 0 1 0 1 1v2h0v1l-2-2h-1c0-1 0-2-1-3v3l-2 1v-5 1c1-1 1-2 1-4v-5z" class="c"></path><path d="M324 411h1v-1c1 1 1 1 1 2h-1c-1 1-1 2-1 3s0 3-1 4l-1-1 2-7z" class="V"></path><path d="M297 423h1v2l1 1h0c-1 1-1 2-2 3 2 3 1 7 2 10v1l-1 1v3c0 1 1 3 1 3v4l-2 1v-2l-1-2-1-2c-1-3-2-7-2-10 1-2 1-5 2-7 0-1 0-1 1-2h0c-1-1 0-2 0-3l1-1h0z" class="d"></path><path d="M297 446c1 2 1 2 0 4l-1-2 1-2z" class="b"></path><path d="M297 429c2 3 1 7 2 10v1l-1 1v3c-1-1-1-2-1-3 0-4-1-8 0-12z" class="I"></path><path d="M296 427c-1-1 0-2 0-3l1-1v1c0 2-1 3 0 5h0c-1 2-1 4-1 6s0 6 1 9v2l-1 2-1-2c-1-3-2-7-2-10 1-2 1-5 2-7 0-1 0-1 1-2h0z" class="m"></path><defs><linearGradient id="AA" x1="394.089" y1="353.446" x2="381.351" y2="361.366" xlink:href="#B"><stop offset="0" stop-color="#cdcdcd"></stop><stop offset="1" stop-color="#f4f2f2"></stop></linearGradient></defs><path fill="url(#AA)" d="M376 350c4 0 7 0 11 1 1 1 3 2 5 2v1 1c3 2 5 4 7 6 1 1 2 1 2 2l1 1v1h-1 0c-2-2-3-2-5-2l-3-2c0-1-3-3-3-3l-2-2h-1s-1-1-2-1c-3-2-7-3-11-3h-1 0c-1 0-3 0-4-1 1-1 5 0 7 0v-1z"></path><path d="M376 350c4 0 7 0 11 1-1 1-2 1-3 1-3-1-5-1-8-1v-1zm-70 62v1c1 0 1-1 2-2v-1h0c0-1 3-5 3-6 1-1 2-3 3-4 0 1 0 2-1 3s-2 3-2 5h-1c0 2-1 4-1 5 0 2 0 3-1 4 0 0-1 1-1 2l-1-1-1 1-1 3-1 1-1 3c-1 0-1 0-2-1h0-1v1l-1-1v-2h-1c0-2 1-5 2-7l1 1v-1c1-2 2-3 3-4h1l2-3c0-1 0-2 1-3h1l-1 1v2c-1 1-1 1-1 2v1z" class="i"></path><path d="M304 418c1 0 1 1 1 1l-1 3-1 1c0-2 1-3 1-5z" class="c"></path><path d="M304 418c1-1 1-3 2-4h1c1 0 2-5 3-6h0c0 2-1 4-1 5 0 2 0 3-1 4 0 0-1 1-1 2l-1-1-1 1s0-1-1-1z" class="T"></path><path d="M300 416c1-2 2-3 3-4h1 1l-5 13h0-1v1l-1-1v-2h-1c0-2 1-5 2-7l1 1v-1z" class="Q"></path><path d="M300 416c1-2 2-3 3-4h1l-3 6-1-2z" class="V"></path><path d="M300 416l1 2-1 3-1 4v1l-1-1v-2h-1c0-2 1-5 2-7l1 1v-1z" class="b"></path><path d="M299 421h1l-1 4v1l-1-1c0-1 0-3 1-4z" class="Y"></path><path d="M300 416l1 2-1 3h-1l1-4v-1zm79-40c2 1 3 2 4 3h0 1c0 1 0 1 1 2v1h1c0-2-2-2-1-3v-1 1h1 0c3 2 5 7 6 10l1 2v2l-1-1v4 1h-1v-1 1h0l-1 2-1-2c0-1-1-2-1-3l-1-1v1l-2-3-5-8s-2-3-3-4v-1l2 2c0-2-1-2-1-4v1l1-1z" class="j"></path><path d="M389 391c1 1 1 4 2 6h0l-1 2-1-2c0-1-1-2-1-3v-1c1 1 1 1 1 2l1 1c0-1 0-1-1-2v-3z" class="Y"></path><path d="M380 383l1-1c3 4 5 7 7 11v1l-1-1v1l-2-3-5-8z" class="d"></path><path d="M385 378v1h1 0c3 2 5 7 6 10l1 2v2l-1-1v4 1h-1v-1 1c-1-2-1-5-2-6 0-2 0-3-1-4-1-3-3-5-5-8h0 1c0 1 0 1 1 2v1h1c0-2-2-2-1-3v-1z" class="c"></path><path d="M392 389l1 2v2l-1-1-1-2 1-1z" class="j"></path><path d="M385 378v1h1 0c3 2 5 7 6 10l-1 1-6-11v-1z" class="i"></path><path d="M320 435v-3c1 1 1 2 1 3h1l2 2v-1c1 2 2 3 4 4h0c0-1 0-1 1-1 1 1 2 1 2 2 1 0 2 1 2 1h0v1h1 3 0 4c-1 1-3 1-4 1v1l-1 1h-1-3c0-1 0-1-1-1 1 1 2 2 2 4h0v1h0-1v1h1c-1 0-2 1-2 1h-1l-1-1c-1-1-2-1-3-2l-1-1c-1 0-2-2-3-3l-1-2c-2-2-2-5-3-7l2-1z" class="F"></path><path d="M330 450l1-1 1 1v1h1c-1 0-2 1-2 1h-1l-1-1 1-1z" class="N"></path><path d="M320 435c1 2 2 5 3 7l-1 1h-1c-2-2-2-5-3-7l2-1z" class="d"></path><path d="M323 442c2 4 4 6 7 8l-1 1c-1-1-2-1-3-2l-1-1c-1 0-2-2-3-3l-1-2h1l1-1z" class="S"></path><path d="M321 435h1l2 2c0 1 1 2 2 2 0 1 1 2 2 3 0 1 0 2 1 2l2 1c1 1 2 2 2 4h0c-5-4-9-8-12-14z" class="Y"></path><path d="M324 437v-1c1 2 2 3 4 4h0c0-1 0-1 1-1 1 1 2 1 2 2 1 0 2 1 2 1h0v1h1 3 0 4c-1 1-3 1-4 1v1l-1 1h-1-3c0-1 0-1-1-1l-2-1c-1 0-1-1-1-2-1-1-2-2-2-3-1 0-2-1-2-2z" class="d"></path><defs><linearGradient id="AB" x1="323.569" y1="443.602" x2="313.175" y2="429.867" xlink:href="#B"><stop offset="0" stop-color="#444443"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#AB)" d="M317 420v-1c0-1 1-7 2-8 0 3-1 7-1 9-1 4-1 8 0 11v5c1 2 1 5 3 7l1 2c1 1 2 3 3 3v3l-2-2v1l-2-2h-1l-1 1v1l1 2h-2l-2-1v-1-2c-1 0-1-1-1-2v-1h0l-1-3c-1-1-1-3-1-4l-1-5c0-2 1-5 1-7h0l1-1c1-1 2-2 2-4l1-1z"></path><path d="M322 445c1 1 2 3 3 3v3l-2-2c0-1-2-1-2-2l1-2z" class="F"></path><path d="M314 425c1-1 2-2 2-4v4 13l-1-1v-1c-1-2-1-4-1-6v-5z" class="c"></path><path d="M314 425c1-1 2-2 2-4v4l-1 2c0 1-1 2-1 3v-5z" class="T"></path><path d="M317 438c1 4 2 7 4 10h-1l-1 1v1l1 2h-2l-2-1v-1-2c-1 0-1-1-1-2v-1h0c1-1 1-1 1-2 0-2 0-3 1-5z" class="d"></path><path d="M316 448c-1 0-1-1-1-2v-1c1 2 2 4 4 5l1 2h-2l-2-1v-1-2z" class="J"></path><defs><linearGradient id="AC" x1="314.006" y1="442.457" x2="314.081" y2="426.309" xlink:href="#B"><stop offset="0" stop-color="#b4b3b3"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#AC)" d="M313 426h0l1-1v5c0 2 0 4 1 6v1l1 1h1c-1 2-1 3-1 5 0 1 0 1-1 2l-1-3c-1-1-1-3-1-4l-1-5c0-2 1-5 1-7z"></path><path d="M314 442h1v1-1h1 0v1c0 1 0 1-1 2l-1-3z" class="b"></path><path d="M349 434c1-1 4-3 5-5 1-1 1-2 2-4v-1c0-1 1-1 1-1 0-1 0-2 1-2v-1-1-1l1-2v-12l-1-3v-1c1 1 1 1 1 2 1-1 1-1 1-2 0-2-1-5-2-7l-1-1v-1s0-1-1-1v-1h0c-1 0-1-1-1-1l2 1 1 1h0c1 1 1 1 1 2 2 1 4 6 4 9v3c1 3 1 6 1 9-1 1 0 1 0 2-1 1-1 2-1 2v1c-1 1 0 1-1 1v2l-2 2h0c-1 1-1 2-1 3 0 0 0 1-1 1 0 1-1 2-2 3l-4 4h0l-2 1c0 1-1 1-2 2-1 0-2 1-3 1 2-1 3-2 4-4z" class="j"></path><path d="M308 417c0 4-1 8-1 12v5h0c-1 1-1 5-1 7v4c-1 0-1 0-1 1-1 0-1 0-2-1v3 1c1 1 1 2 1 3h0c-3-4-4-7-5-12v-1c-1-3 0-7-2-10 1-1 1-2 2-3h0v-1h1 0c1 1 1 1 2 1l1-3 1-1 1-3 1-1 1 1c0-1 1-2 1-2z" class="o"></path><path d="M305 419l1-1 1 1-3 14c-1-2-1-3-1-5l1-6 1-3z" class="i"></path><path d="M303 423l1-1-1 6c0 2 0 3 1 5v6c1 0 1 1 2 2v4c-1 0-1 0-1 1-1 0-1 0-2-1 0-1-1-2-1-3v-1l1-2c-1 0-1-1-1-2-1 0 0-2 0-3 0-2-1-4 0-6v-1-1l1-3z" class="b"></path><path d="M303 428c0 2 0 3 1 5v6l-1 3h-1v-1l1-2v-11z" class="Y"></path><path d="M304 439c1 0 1 1 2 2v4c-1 0-1 0-1 1-1 0-1 0-2-1 0-1-1-2-1-3h1l1-3z" class="c"></path><path d="M300 425h0c1 1 1 1 2 1v1 1c-1 2 0 4 0 6 0 1-1 3 0 3 0 1 0 2 1 2l-1 2v1c0 1 1 2 1 3v3 1c1 1 1 2 1 3h0c-3-4-4-7-5-12v-1c-1-3 0-7-2-10 1-1 1-2 2-3h0v-1h1z" class="d"></path><path d="M299 426v3 6 4c-1-3 0-7-2-10 1-1 1-2 2-3z" class="n"></path><path d="M300 425h0c1 1 1 1 2 1v1 1c-1 2 0 4 0 6 0 1-1 3 0 3 0 1 0 2 1 2l-1 2-1-1c-2-2-1-8-1-11h-1v-3h0v-1h1z" class="m"></path><path d="M299 425h1v4h-1v-3h0v-1z" class="L"></path><path d="M368 398c2 7 4 13 2 21-1 4-3 9-5 12l-6 6c-2 2-4 5-6 6l-2 1c-1 1-3 2-3 2h-3l-5 1c-2 0-3 0-4-1l1-1v-1c1 0 3 0 4-1h-4-2c2-2 3-1 4-2 2 0 3-1 5-1 0-1 1-1 1-2 1 0 2-1 3-1 1-1 2-1 2-2 0 1 0 2-1 3 0 1 0 1-1 1h1 3c1 0 1 0 2-1 0-1 0-1 1-1h1c3-2 5-5 7-7 0-2 1-3 2-5 1-4 2-8 2-13l1-1h0l1 1v-3h0l-2-10h0l1-1z" class="f"></path><path d="M367 412l1-1h0l1 1-1 7c-2 7-5 12-10 18l-4 4h-1l-2 2v1c-1 1-3 2-3 2h-3-2l-1-1c1 0 3 0 4-1l-1-1h0c2 0 3-1 4-1l2-2 1-1c1 0 1 0 2-1 0-1 0-1 1-1h1c3-2 5-5 7-7 0-2 1-3 2-5 1-4 2-8 2-13z" class="Q"></path><path d="M354 438c0-1 0-1 1-1h1c0 1-1 2-2 3-1 0-1 1-3 1 0 0-1 1-2 1l2-2 1-1c1 0 1 0 2-1z" class="d"></path><path d="M348 437c1-1 2-1 2-2 0 1 0 2-1 3 0 1 0 1-1 1h1 3l-1 1-2 2c-1 0-2 1-4 1h0l1 1c-1 1-3 1-4 1l1 1h2l-5 1c-2 0-3 0-4-1l1-1v-1c1 0 3 0 4-1h-4-2c2-2 3-1 4-2 2 0 3-1 5-1 0-1 1-1 1-2 1 0 2-1 3-1z" class="m"></path><path d="M345 443l1 1c-1 1-3 1-4 1l1 1h2l-5 1c-2 0-3 0-4-1l1-1c3 0 6-1 8-2z" class="S"></path><path d="M348 437c1-1 2-1 2-2 0 1 0 2-1 3 0 1 0 1-1 1h1 3l-1 1-2 2c-1 0-2 1-4 1v-1c1 0 1-1 2-1h-2c0-1 1-1 2-2 0-1 1-1 1-2h0z" class="Y"></path><path d="M347 441v-1h4l-2 2c-1 0-2 1-4 1v-1c1 0 1-1 2-1z" class="c"></path><defs><linearGradient id="AD" x1="397.411" y1="372.837" x2="375.636" y2="373.784" xlink:href="#B"><stop offset="0" stop-color="#bdbdbd"></stop><stop offset="1" stop-color="#f0f0f0"></stop></linearGradient></defs><path fill="url(#AD)" d="M377 359c1 0 2 0 3 1h2 0c3 0 5 2 6 4l1 1v-1c1 1 2 2 2 3l4 5 2 3h0 1 0s1 1 1 2h0v3h0c0-1-1-1-1-2l2 5-1 1v1h-1c0 1-1 1-1 2h-1l-5-9c-1-2-2-3-3-4h-1-1 0c-2-3-4-6-7-8-2 0-3-2-5-3h0 1v-1h1l3-2h0s-1-1-2-1z"></path><path d="M396 378h1v-1l1 1 2 5-1 1c-1-1-3-5-3-6z" class="L"></path><path d="M392 373v-3h-1l2 1c1 2 4 5 4 6v1h-1c-2-1-3-4-4-5z" class="d"></path><path d="M382 360c3 0 5 2 6 4l1 1 4 6-2-1h1v3c-1-2-2-4-3-5-1-2-2-3-3-5-1-1-2-2-4-3z" class="b"></path><path d="M389 364c1 1 2 2 2 3l4 5 2 3h0 1 0s1 1 1 2h0v3h0c0-1-1-1-1-2l-1-1c0-1-3-4-4-6l-4-6v-1z" class="c"></path><path d="M379 366v-1c4 2 7 5 9 9h-1-1 0c-2-3-4-6-7-8z" class="T"></path><path d="M298 444v-3l1-1c1 5 2 8 5 12h0c0-1 0-2-1-3v-1-3c1 1 1 1 2 1 0-1 0-1 1-1 1 4 2 8 6 10l1 1h1c3 3 6 5 10 5v1h-1v1h-2v1 1l-2-1-2 1h-3v1h1l-1 2-4-1v-1-2-2h-1-2v-1c-2 0-3-1-5-2h0l-5-7 2-1v-4s-1-2-1-3z" class="Q"></path><path d="M321 463c-1-1-3-1-4-2h0l6 1v1h-2z" class="L"></path><path d="M305 446c0-1 0-1 1-1 1 4 2 8 6 10l1 1c0 1 0 0-1 1l-3-3-3-3-3-3v-3c1 1 1 1 2 1z" class="Y"></path><path d="M303 445c1 1 1 1 2 1 0 1 1 2 1 4v1l-3-3v-3z" class="b"></path><path d="M312 460c2 1 3 2 5 3 1 1 3 1 4 1v1l-2-1-2 1h-3v1h1l-1 2-4-1v-1-2-2h0l1-1 1 1v-2z" class="H"></path><path d="M312 460c2 1 3 2 5 3-3 0-3 0-5-1v-2z" class="J"></path><path d="M310 462h0l4 2v1 1h1l-1 2-4-1v-1-2-2z" class="Q"></path><path d="M298 444v-3l1-1c1 5 2 8 5 12 1 3 4 5 6 7l2 1v2l-1-1-1 1h0-1-2v-1c-2 0-3-1-5-2h0l-5-7 2-1v-4s-1-2-1-3z" class="R"></path><path d="M310 459l2 1v2l-1-1-1 1h0-1l-2-2 1-1v1c1 0 1 0 2-1z" class="F"></path><path d="M299 447c1 4 4 11 8 13l2 2h-2v-1c-2 0-3-1-5-2h0l-5-7 2-1v-4z" class="S"></path><path d="M314 400c1-1 2 0 2-1h2c-2 2-3 4-4 7 0 1-1 1-1 2v1l1 1c-1 1-1 2-1 3l-1 4-1 4v8l1 4 1 5c0 1 0 3 1 4l1 3h0v1c0 1 0 2 1 2v2 1c0 1 0 1-1 1h0v1l-1 1-2-1v2c-4-2-5-6-6-10v-4c0-2 0-6 1-7h0v-5c0-4 1-8 1-12 1-1 1-2 1-4 0-1 1-3 1-5h1c0-2 1-4 2-5s1-2 1-3z" class="L"></path><path d="M308 438c2-2 0-5 1-7 0 4 0 7 1 10 0 1 0 1-1 1v-2l-1-2z" class="o"></path><path d="M308 428c0-3 1-5 2-8 0 4 0 7-1 11-1 2 1 5-1 7l-1-4h0v-5l1-2v2-1z" class="b"></path><path d="M307 429l1-2v2-1c1 2 1 4 0 6h0 0-1v-5z" class="Y"></path><defs><linearGradient id="AE" x1="315.189" y1="422.146" x2="308.848" y2="401.456" xlink:href="#B"><stop offset="0" stop-color="#d4d1d4"></stop><stop offset="1" stop-color="#f4f6f5"></stop></linearGradient></defs><path fill="url(#AE)" d="M314 400c1-1 2 0 2-1h2c-2 2-3 4-4 7 0 1-1 1-1 2v1c0 1 0 2-1 3l-2 8c-1 3-2 5-2 8v1-2l-1 2c0-4 1-8 1-12 1-1 1-2 1-4 0-1 1-3 1-5h1c0-2 1-4 2-5s1-2 1-3z"></path><defs><linearGradient id="AF" x1="310.525" y1="444.225" x2="306.297" y2="444.412" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#AF)" d="M307 434l1 4 1 2v2c1 0 1 0 1-1l2 2c0-2 0-4 1-5 0 1 0 3 1 4l1 3h0v1c0 1 0 2 1 2v2 1c0 1 0 1-1 1h0v1l-1 1-2-1v2c-4-2-5-6-6-10v-4c0-2 0-6 1-7z"></path><path d="M309 440v2c0 2 1 3 1 4 1 1 2 2 2 3h-2 0c-1-2-2-6-1-9z" class="I"></path><path d="M310 441l2 2 1 6h0-1 0c0-1-1-2-2-3 0-1-1-2-1-4 1 0 1 0 1-1z" class="Q"></path><path d="M313 449l1 1h0l2-2v2 1c0 1 0 1-1 1h0v1l-1 1-2-1c-1-1-1-2-2-4h0 2 0 1 0z" class="J"></path><path d="M313 449l1 1h0l2-2v2 1c0 1 0 1-1 1h0l-3-3h1 0z" class="n"></path><path d="M312 443c0-2 0-4 1-5 0 1 0 3 1 4l1 3h0v1c0 1 0 2 1 2l-2 2h0l-1-1-1-6z" class="G"></path><path d="M334 376c1 0 3-1 4-2h2c1-1 5 1 6 2h6c-2 1-8 1-10 1h-2c-3 0-8 4-10 6-6 3-13 5-19 8h0v1c-1 1-2 1-2 2-1 0-1 0-2 1l2-1 4-1h0v1h0-1s-1 1-1 2c0 0-1 0-1 1-1 0-1 0-2 1l-1 1h-1v1c-1 0-1 1-2 1v1c0 1-1 1-2 2 0 1 0 1-1 1 1 0 1-1 1-2h0c-1 0-3 2-3 3s0 1-1 2h0l-2 4-1-1 1-3h0-1-1c0 1-2 2-2 3h-1c0-1-1-1-1-1-1 1-2 2-3 2v2c-2 3-3 7-4 10v1h-2 0-1v-1l2-7c1-1 1-3 2-4s1-1 1-3c0 1-1 2-2 3h-1c1-1 1-2 1-3l-1 1-1-1 5-6h1c2-1 3-3 4-4l3-2h0c0-1 1-1 2-2h-1-1v-1-1l-3 1 1-1h1l1-1c2 0 3-1 4-1 7-4 14-5 21-8 4-2 7-4 11-6 1-1 2-2 4-2z" class="l"></path><path d="M298 396h1v-1c1-1 3-2 4-2h1l-1 1c-2 0-3 2-5 3v-1z" class="V"></path><path d="M309 394l-4 3c1-2 4-4 6-6v1c-1 1-2 1-2 2-1 0-1 0-2 1l2-1zm-13 9c1 1 1 1 2 0h1c-1 1-1 1-1 2-1 0-1 1-1 2h0l-1 1h0-1-1c0 1-2 2-2 3h-1c0-1-1-1-1-1 2-1 3-3 4-5l2-2z" class="i"></path><path d="M298 396v1c-2 2-4 5-7 7h2c1-2 1-2 3-3 1 0 3-2 5-3h0 0v1c-2 1-4 3-5 4l-2 2c-1 2-2 4-4 5-1 1-2 2-3 2v2c-2 3-3 7-4 10v1h-2 0-1v-1l2-7c1-1 1-3 2-4s1-1 1-3c0 1-1 2-2 3h-1c1-1 1-2 1-3l-1 1-1-1 5-6h1c2-1 3-3 4-4l3-2v1l4-3z" class="T"></path><path d="M291 400l3-2v1l-8 8c-1 1-3 2-3 3l-1 1-1-1 5-6h1c2-1 3-3 4-4z" class="V"></path><path d="M285 410c2-1 3-3 5-4h0c0 1-1 2-2 3h1v1l2-2v-1c1-1 2-2 3-2-1 2-2 4-4 5-1 1-2 2-3 2v2c-2 3-3 7-4 10v1h-2 0-1v-1l2-7c1-1 1-3 2-4s1-1 1-3z" class="c"></path><path d="M281 425c1-5 3-9 5-12l1-1v2c-2 3-3 7-4 10v1h-2 0z" class="I"></path><path d="M372 380c-1 0-1-1-1-2v-1c1 1 4 3 4 5 2 1 3 3 4 5h1v1l1 1c1 1 1 2 2 2v2c1 1 2 3 3 3h1l-1-1v-1c-1-1-1-1-1-2v-1l2 3v-1l1 1c0 1 1 2 1 3l1 2h0v1l1 3c0 2 1 4 1 5 1 9 0 16-3 24 0 1-1 2-1 2h-1v1c-1 0-1 0-1-1l-2 5h-1l-1-1 1-3h-1v-3l-2 4h-1-2v-1c1-2 1-4 2-6l-1-1c1-1 1-4 1-5 2-7 3-15 1-22h0c-1-1-1-2-1-3h-1c-2-6-4-11-9-15 0-1-2-2-2-4 2 1 3 2 4 2h0c2 1 2 3 4 4h0l1 1-1-2h-1c0-1-1-1-1-2s0-1-1-2z" class="j"></path><path d="M380 401h0c1 1 1 3 2 4 0 5 0 9-1 14l-2 10-1-1c1-1 1-4 1-5 2-7 3-15 1-22h0z" class="C"></path><path d="M386 411l1-1v2c1 1 1 3 1 4v1l1-1c0 5-1 9-2 13h-1l1-3-2-2 1-2v-3-3c1-2 0-3 0-5z" class="X"></path><path d="M386 419c1 0 1 0 1 1 1 1 0 3 0 4v2l-2-2 1-2v-3z" class="g"></path><path d="M386 422c0 1 1 1 1 2v2l-2-2 1-2z" class="J"></path><defs><linearGradient id="AG" x1="385.624" y1="433.111" x2="382.31" y2="427.943" xlink:href="#B"><stop offset="0" stop-color="#2e3230"></stop><stop offset="1" stop-color="#464244"></stop></linearGradient></defs><path fill="url(#AG)" d="M385 424l2 2-1 3h1c0 2-1 4-1 5l-2 5h-1l-1-1 1-3h-1v-3l3-8z"></path><path d="M383 435l3-6h1c0 2-1 4-1 5l-2 5h-1l-1-1 1-3z" class="k"></path><path d="M377 386c1 0 1 1 2 2 3 5 7 12 8 18h2v-5h0l1-1 1 3c0 2 1 4 1 5h-2 0l-1 1v7l-1 1v-1c0-1 0-3-1-4v-2l-1 1c0-2 0-4-1-6-1-7-5-14-8-19z" class="L"></path><path d="M389 401l1-1 1 3c0 2 1 4 1 5h-2 0l-1 1v3h-1l-1-6h2v-5h0z" class="d"></path><path d="M389 401l1-1 1 3h-1l-1 3v-5z" class="L"></path><path d="M389 406l1-3h1c0 2 1 4 1 5h-2 0l-1-2z" class="N"></path><path d="M377 386h0c-1-2-2-3-2-4h0c2 1 3 3 4 5h1v1l1 1c1 1 1 2 2 2v2c1 1 2 3 3 3h1l-1-1v-1c-1-1-1-1-1-2v-1l2 3v-1l1 1c0 1 1 2 1 3l1 2h0v1l-1 1h0v5h-2c-1-6-5-13-8-18-1-1-1-2-2-2z" class="Y"></path><path d="M389 400l1-1v1l-1 1h0v-1z" class="W"></path><path d="M387 394v-1l1 1c0 1 1 2 1 3l1 2h0l-1 1-1-2c0-1-1-3-1-4z" class="o"></path><path d="M388 398l1-1 1 2h0l-1 1-1-2z" class="Q"></path><path d="M390 408h0 2c1 9 0 16-3 24 0 1-1 2-1 2h-1v1c-1 0-1 0-1-1s1-3 1-5c1-4 2-8 2-13v-7l1-1z" class="E"></path><path d="M376 406l-1-2v-2c0-1 0-1-1-2v-1-1l1 1c0 2 1 2 2 3v1 2h1c0 1 1 2 2 3h0v-4c-1 0 0-2 0-3 2 7 1 15-1 22 0 1 0 4-1 5l1 1c-1 2-1 4-2 6l-1 1h-1c-1 1-2 3-3 5h0l1 1-3 3c-3 3-5 5-9 6 0 1-1 1-2 1h0l1-2-3 2h-2-1l-2-1-3 1c-2 1-3 2-5 2-1 0-2 0-2 1h-3c-3-1-6-1-8-3 0 0 1-1 2-1h-1v-1h1 0v-1h0c0-2-1-3-2-4 1 0 1 0 1 1h3 1c1 1 2 1 4 1l5-1h3s2-1 3-2l2-1c2-1 4-4 6-6l6-6c2-3 4-8 5-12l1 1v-1c1 1 1 1 1 2l2-7v-1h1v2h1c0-2 1-5 0-7v-2z" class="R"></path><path d="M348 449v1h0 0v2h1c-2 1-3 2-5 2-1 0-2 0-2 1h-3c-3-1-6-1-8-3 0 0 1-1 2-1h-1v-1h1 1c1 1 2 1 3 1 4 0 7 0 11-2z" class="d"></path><path d="M341 453c2-1 4-1 6-2l1 1h1c-2 1-3 2-5 2h-2c-1 0-1-1-1-1z" class="L"></path><path d="M331 452s1-1 2-1l8 2s0 1 1 1h2c-1 0-2 0-2 1h-3c-3-1-6-1-8-3z" class="g"></path><path d="M371 419c1 1 1 1 1 2-1 2-1 4-3 7-2 5-6 10-11 13-1 1-2 2-3 2-1 1-2 2-4 3-1 1-2 2-3 2l-3-1h-2-3l5-1h3s2-1 3-2l2-1c2-1 4-4 6-6l6-6c2-3 4-8 5-12l1 1v-1z" class="h"></path><path d="M371 419c1 1 1 1 1 2-1 2-1 4-3 7-2 5-6 10-11 13-1 1-2 2-3 2h0c4-4 8-8 11-12 2-4 4-7 5-11v-1z" class="j"></path><defs><linearGradient id="AH" x1="356.433" y1="431.535" x2="369.126" y2="437.658" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#d8d6d8"></stop></linearGradient></defs><path fill="url(#AH)" d="M374 413h1v2h1c-1 2 0 5-1 7-1 1 1 4 0 5s-1 2-1 3l-5 8c-1 1-1 2-2 3h1l-3 4-4 4-1 1-3 2h-2-1l-2-1-3 1h-1v-2h0 0v-1c4-2 9-5 12-8 3-2 6-6 8-9 3-5 7-13 6-18v-1z"></path><path d="M367 441h1l-3 4-4 4-1 1-3 2h-2-1l-2-1c5-3 12-6 15-10z" class="a"></path><path d="M354 452v-1c1 0 2-1 2-1h2c1-1 1-1 3-1l-1 1-3 2h-2-1z" class="I"></path><path d="M376 406l-1-2v-2c0-1 0-1-1-2v-1-1l1 1c0 2 1 2 2 3v1 2h1c0 1 1 2 2 3h0v-4c-1 0 0-2 0-3 2 7 1 15-1 22 0 1 0 4-1 5l1 1c-1 2-1 4-2 6l-1 1h-1c-1 1-2 3-3 5h0l1 1-3 3c-3 3-5 5-9 6 0 1-1 1-2 1h0l1-2 1-1 4-4 3-4h-1c1-1 1-2 2-3 2-3 3-5 5-8 0-1 0-2 1-3s-1-4 0-5c1-2 0-5 1-7 0-2 1-5 0-7v-2z" class="Y"></path><path d="M374 430c0 2-4 9-5 11h-1-1c1-1 1-2 2-3 2-3 3-5 5-8z" class="S"></path><path d="M365 445l1 1v-1c3-1 5-5 7-7 1-1 2-2 3-4s1-4 2-6l1 1c-1 2-1 4-2 6l-1 1h-1c-1 1-2 3-3 5h0l1 1-3 3c-3 3-5 5-9 6 0 1-1 1-2 1h0l1-2 1-1 4-4z" class="Q"></path><path d="M225 227c2-4 4-7 6-10s5-5 8-5c6 0 10 3 13 6h3v1l1 1 1 1 1 1h3 5v1h1v1h-1l-1 1h-1-3-1c1 1 1 1 2 1h1c10 2 21 5 31 10 9 5 18 14 24 22 4 6 7 12 10 18-1 0-1 1-1 1v2l1 2c0 1 1 2 1 4 1 5 4 10 4 15h1c1 0 1 0 1 1 1-1 1-1 1-2v1c1-2 0-4 0-6-1-9-3-18-7-27h1l6 20h2c0-3-2-7-2-10-1-2-1-4-2-6h1c1 5 3 10 4 15 0 5 0 10 2 15v1 3h1c0-2 0-3-1-5 0-1 0-2 1-3 0 1 0 2 1 2v7 3h0c0 1 1 1 1 2h1l1-2c0 1-1 2 0 3s1 1 1 2l2 1h0c0 1 0 2 1 3 0-1 2-2 2-4h1 0c1 0 1-1 2-1l1-1v-2-3l2-2h1c-1 1-2 2-2 3v2 1h2 0c-1 1-2 2-3 4v1h0c-3 4-7 8-6 14v1h0v3c1 1 1 1 1 2v1 1 2s1 2 1 3h0c2 1 3 3 4 4 1 0 1 1 2 1h2l-1 1c1 1 1 1 2 1h1c1 1 0 1 1 1h2 2 1-1c0 1 0 1-1 1s-2 0-3-1l-5-2h0c-1 0-2-1-2-1l-3-3v1 2h-1v2l-1 1h0c-2 3-4 5-6 7-1 1-3 2-4 4s-1 6 0 8 3 4 4 5c1 0 2 0 3 1h4 0-6c-1-1-5-3-6-2h-2c-1 1-3 2-4 2-2 0-3 1-4 2-4 2-7 4-11 6-7 3-14 4-21 8-1 0-2 1-4 1l-1 1h-1l-1 1 3-1v1 1h1 1c-1 1-2 1-2 2-1-1-2-1-2-2-3 1-8 3-10 5-1 2-4 4-5 6h0c0-2 3-4 4-6 1-1 2-2 3-2v-1h0-1v-1c1-1 2-1 3-2h0l1-1-1-1h0-3c0 1-1 1-1 2l-3 1h0l-3 1c-2 2-4 3-6 4l-3 3v-1c0-1 1-3 1-4-1 1-2 1-3 1v-1l-2 2-1-1-2 1h-2c-1 1-2 1-2 2-1-1-1-1-1-2h0c-1-1-1-1-1-2l-1 1h-1l1-2 6-4 2-1c0-1 1-1 1-2h-1l1-1c-1-1-2-1-3-1h1-1l1-1v-1l-1 1v-1h-2c-1 0-1 0-2-1v1h-2l1-1v-1h0c1-1 1-1 2-1h0v-1h-2v-1h0 1c1 0 1-1 2-2h-1v-1l5-3c1 0 4-1 5-1h1 2c2-1 3-2 6-2l1-2h1l-1-2 4-1h2c1 0 3-1 4-2 0-1 2-1 2-2l5-2 5-2c4-2 9-5 10-9h1c0-1 1-2 1-2 0-1 0-1-1-1h-1c-1 0-1 1-2 1l-5 1c-1 1-2 2-4 3v-2h-1 1v-1l1-1c-1-1-2-1-4-1l-3-1-1-1h0l1-1v-1c-1-1-3-2-5-3l-3-4c-1-2-2-3-2-4-1-1-1-2-1-3 0-2-1-4 0-5v-1-2c-1 0-1 1-2 1l-1-1c-1-1-3 0-4 0v-1h0c1-1 1-1 2-1v-1-1h-1l2-1h-2l-3-1s-1 0-1-1l1-1h0v-1l-1-1h0c1 0 1 0 1-1 2 0 3 1 5 1h1l-2-1v-1h0l-2-1-1-2c1 0 2 0 3 1v-1-1l1-1c-2 0-4-1-6-1v-2h-1c-8-2-15-3-23-2-3 0-6 1-8 1l-5 1c-9 1-20 5-28 10h0c-1 1-3 2-5 3-6 4-10 8-14 14h-1l1-8v-9c1-7 1-13 2-20 2-1 3-3 4-4l-1-1v-1c-2-1-1-2-2-3 0-1 1-3 1-4 1-1 2-3 3-4v-2h0c2-2 6-4 9-6 4-1 6-3 10-4 1 1 1 1 2 1 1 1 2 1 3 2 0-2 1-4 1-6h0v-4-2h0c1-1 1-3 1-5h0c1-2 1-4 1-6l2-6v-1c1-1 1-3 2-5l1-3v-1z" class="q"></path><path d="M328 309c1 0 1 1 2 1v1l-1-1v1c-1-1-1-1-1-2z" class="i"></path><path d="M312 329c1 0 2 0 3 1-1 0-2 1-2 1l-1-2z" class="f"></path><path d="M331 308v-3h1 0v1 3c-1 0-1 0-1-1z" class="i"></path><path d="M334 310l1 2c0 2 0 4-1 6v-8z" class="l"></path><path d="M275 374h0s1 0 2-1h0 3v1h-2 0-3z" class="f"></path><path d="M315 330c1 1 2 1 2 2 1 1 1 2 2 3l1 1v-1c1 2 1 4 1 6 0 0 0 1-1 1v-3c0-2-2-3-2-4l-1-1c0-1 0-1-1-2 0-1-1-1-1-2z" class="l"></path><path d="M275 373c1-1 2-1 3-1 0 0-1 0-1 1-1 0-1 1-2 1-2 0-2 0-4 1 1 0 1 0 1-1h3 3c-1 1-2 1-3 1-2 0-5 1-7 1 0 0 0-1 1-1 2-1 3-2 6-2z" class="V"></path><path d="M280 374h0 2c1 0 2 0 4-1h0v1s-1 0-1 1h-1-1 0c-1 0-3 0-4 1-1 0-2 0-3 1h-2c-1 1-1 1-2 1h0l-1-1c1 0 3-1 4-2 1 0 2 0 3-1h0 2z" class="l"></path><path d="M341 300c0-1 0-2 1-3 0 1 0 2 1 2v7 3h0c0 1 1 1 1 2h1l-1 1-1-1v1l-1 1c-1-1 0-2-1-3v-5h1c0-2 0-3-1-5z" class="f"></path><path d="M322 305c0 1 1 2 2 3h0c0-2-2-2-2-4h1v2l2-1 1 2c1 1 0 2 0 3 0 2 1 3 1 5h-1c-1-1-1-2-2-3 0-1-1-2-1-3v-1h-1v1c0-1-1-1-1-1h-1l1 1-1 1v-2c0-1 0-1-1-2v-1h2 1z" class="l"></path><path d="M291 395l3-1v1 1h1 1c-1 1-2 1-2 2-1-1-2-1-2-2-3 1-8 3-10 5-1 2-4 4-5 6h0c0-2 3-4 4-6 1-1 2-2 3-2v-1h0-1v-1h1 1v-1h2v1 1h0l1-1v-1h0v-1c1 0 1 1 2 1 0-1 0-1 1-1z" class="j"></path><path d="M303 359c1-1 3-2 4-3 1-2 2-2 4-3 1 0 3-2 3-4 1-1 2-2 4-3 0 0 1 0 2-1-2 5-5 9-10 11h0-1 0c-1 1-1 1-2 1-1 1-2 2-4 2z" class="V"></path><path d="M288 364l5-2c0 1-1 2-2 2s-1 0-2 1c-1 0-1 0-2 1h3v1c-3 0-5 0-7 1l7 1h-3c-1 0-3 1-4 1l-2 1-3 1c-1 0-2 0-3 1l1-2h1l-1-2 4-1h2c1 0 3-1 4-2 0-1 2-1 2-2z" class="f"></path><path d="M276 369l4-1-2 2c2 0 3 0 4-1l1 1-2 1-3 1c-1 0-2 0-3 1l1-2h1l-1-2z" class="V"></path><path d="M277 371l4-1v1l-3 1c-1 0-2 0-3 1l1-2h1z" class="Y"></path><path d="M287 317c1 0 2 0 3 1 0-1 1-1 1-1 1 0 4 1 5 1l1-1h0l1 1h0v-1h2l2 2h-4c-1-1-2 0-2-1-1 0-2 1-2 1h-1c-2 1-4-1-6-1v1c1 0 2 0 3 1 1 0 2-1 4 0h0c1 0 1 1 2 1h0c2 0 2 0 4 1h1l-11-2c-2 0-6-1-8 0h-1-1-1l-1-1c1 0 2 0 2-1h1 2c1-1 2 0 3 1h0l1-2zm11-14h2l1 1 1-1c1 1 4 3 4 4 2 2 3 5 5 6h1l1 2h0l-2-1h0c0 2 1 2 2 3v1c-1 0-3-3-4-3h0l1 2v1l-1-1c-1-1-2-2-3-4-1-1-1-2-2-3h0c-1-2-3-5-5-6-1 0-1 0-1-1z" class="i"></path><path d="M298 303h2l1 1 1-1c1 1 4 3 4 4h-1c1 2 1 2 2 3 0 1 0 1 1 2l-1 1-2-4h-1v1h0c-1-2-3-5-5-6-1 0-1 0-1-1z" class="Q"></path><path d="M271 311c2 0 3 1 5 1 1 1 2 1 2 2l4 2 1-1 3 2h1l-1 2h0c-1-1-2-2-3-1h-2-1c0 1-1 1-2 1l1 1h1 1l-1 2c-1 0-1 1-2 1l-1-1c-1-1-3 0-4 0v-1h0c1-1 1-1 2-1v-1-1h-1l2-1h-2l-3-1s-1 0-1-1l1-1h0v-1l-1-1h0c1 0 1 0 1-1z" class="c"></path><path d="M275 319c2 1 2 0 4 2 0-1 1-1 1-1h1l-1 2c-1 0-1 1-2 1l-1-1c-1-1-3 0-4 0v-1h0c1-1 1-1 2-1v-1z" class="J"></path><path d="M275 319c2 1 2 0 4 2h-6 0c1-1 1-1 2-1v-1z" class="Z"></path><path d="M271 311c2 0 3 1 5 1 1 1 2 1 2 2v1l4 2c-2 0-4-2-6-2h-3c-1 0-1 0-2-1h0v-1l-1-1h0c1 0 1 0 1-1zm76 44c1-1 3-3 3-5v-1l1-1v1 2h0c-2 3-4 5-6 7-1 1-3 2-4 4s-1 6 0 8 3 4 4 5c1 0 2 0 3 1h4 0-6c-1-1-5-3-6-2h-2c-1 1-3 2-4 2l1-1c2-2 4-4 4-6l1-6c-1-2-10-8-12-9-1-1-1-2-2-3l1-1c1 1 4 1 6 1l-1 1h-4 0c0 1 0 1 2 1 1 0 2 1 3 1 1 1 2 1 3 2s2 1 4 3v-1c1-1 3-2 4-3h3z" class="N"></path><path d="M336 356c1 1 2 1 4 3v-1l2 2-1 2h-1l-2-2c1-1 1-1 0-1-1-1-2-1-2-2v-1z" class="b"></path><path d="M344 355h3l-5 5-2-2c1-1 3-2 4-3z" class="o"></path><path d="M328 354c-1-1-1-2-2-3l1-1c1 1 4 1 6 1l-1 1h-4 0c0 1 0 1 2 1 1 0 2 1 3 1 1 1 2 1 3 2v1c0 1 1 1 2 2 1 0 1 0 0 1-2-2-6-4-8-6h-2z" class="m"></path><path d="M272 383h-1v-1h0 1l1-1h0 2c0-1 3 0 4-1l4-1c1 0 2-1 4-1h0v1c-4 1-8 3-12 4-1 0 0 0-1 1 1 0 1 0 2-1h1l1 1c2 0 4-1 6 0 1-1 2-1 4-1-1 0-1 0-1 1 0 0 1 0 1-1l1 1h-1c-2 1-3 2-5 3-3 2-5 2-8 4v1l-2 2h0v1l-6 2v-1l3-2c1-1 2-2 2-4 0 0 2-1 3-2-1 0-1-1-1-2h1l1-1h0c-1-1-2-1-3-1-1 1-3 2-4 2h1l2-2h1 0l-1-1z" class="f"></path><path d="M272 383c1-1 1-1 3-1h0c-1 1-2 1-2 2l-1-1z" class="i"></path><path d="M275 388c2-1 5-2 7-2h1c-1 0-1 1-1 1-2 0-6 2-7 4h0v1l-2 2h0v1l-6 2v-1l3-2c1-1 2-2 2-4 0 0 2-1 3-2z" class="T"></path><path d="M351 348v1h0v-1l-1 1v1c0 2-2 4-3 5h-3c-1 1-3 2-4 3v1c-2-2-3-2-4-3s-2-1-3-2c-1 0-2-1-3-1-2 0-2 0-2-1h0 4l1-1c1 0 2 0 4-1l14-2z" class="V"></path><path d="M351 348v1h0v-1l-1 1v1c0 2-2 4-3 5h-3l1-1c1-1 3-3 3-4h-2-1c-1 0-2 0-3 1h-4c-2 1-4 1-6 1l1-1c1 0 2 0 4-1l14-2z" class="Y"></path><path d="M329 267h1l6 20h2c0-3-2-7-2-10-1-2-1-4-2-6h1c1 5 3 10 4 15 0 5 0 10 2 15v1h-1v7c-1-1-1-4-1-6-1 0-1 1-1 1 0 2 0 4-1 6 0-1 0-4-1-5h0c-1 2-1 4-1 7l-1-2c1-3-1-7-1-10h1c1 0 1 0 1 1 1-1 1-1 1-2v1c1-2 0-4 0-6-1-9-3-18-7-27z" class="d"></path><path d="M336 287h2l1 14c-2-4-2-9-3-14z" class="p"></path><path d="M275 392v1c1-1 2-2 3-2v-1c1 0 1 0 2-1h2v-1h1 4c0 1 0 1-1 2-1 0-2 0-2 1-1 0-2 1-3 1-1 1-2 1-2 1h1 0 1 0c1-1 1-1 2-1h1c0-1 4-2 6-2h0c1-1 1-1 2-1h1c1-1 2-1 3-1v1c-1 0-1 1-2 1s-2 2-3 3-3 1-5 2h0l1-1-1-1h0-3c0 1-1 1-1 2l-3 1h0l-3 1c-2 2-4 3-6 4l-3 3v-1c0-1 1-3 1-4-1 1-2 1-3 1v-1h0l2-2 6-2v-1h0l2-2z" class="V"></path><g class="j"><path d="M283 393c0 1-1 1-1 2l-3 1 1-1c0-1 0-1 1-1 0-1 1-1 2-1zm6-2c1-1 2-1 3-1h1l-2 2c-1-1-1-1-2-1z"></path><path d="M283 393c1-1 1-1 2-1h1c1-1 2-1 3-1-1 1-2 2-3 2h0-3z"></path></g><path d="M289 391c1 0 1 0 2 1-1 0-3 2-4 2l-1-1c1 0 2-1 3-2zm-19 10l1-2c2-1 3-1 5-2-2 2-4 3-6 4z" class="T"></path><path d="M273 394h1c1-1 3-2 5-3l1 1c-1 1-4 2-5 3h-1-1v-1z" class="Y"></path><path d="M273 395h1c-2 2-4 3-6 4-1 1-2 1-3 1v-1h0l2-2 6-2z" class="o"></path><path d="M276 305l4 2c1 1 2 2 3 2h1l2 1 1 1v-1h3 0c3 2 5 4 7 6v1c1 0 0 0 1 1l-1-1h0l-1 1c-1 0-4-1-5-1 0 0-1 0-1 1-1-1-2-1-3-1h-1l-3-2-1 1-4-2c0-1-1-1-2-2h1l-2-1v-1h0l-2-1-1-2c1 0 2 0 3 1v-1-1l1-1z" class="S"></path><path d="M276 305l4 2c1 1 2 2 3 2l2 2c-2-1-4-2-6-2-1-1-2-2-4-2v-1l1-1z" class="M"></path><path d="M277 312c3 0 5 1 7 3 1 0 2 0 2 1h1l-1 1-3-2-1 1-4-2c0-1-1-1-2-2h1z" class="J"></path><path d="M284 309l2 1 1 1v-1h3 0v1l2 2 3 3-8-4s-2 0-2-1l-2-2h1z" class="N"></path><path d="M272 307c1 0 2 0 3 1l17 8h0c-4 0-7-3-10-4-2-1-4-1-7-2h0l-2-1-1-2z" class="M"></path><path d="M272 378c1 0 1 0 2-1h2c1-1 2-1 3-1 1-1 3-1 4-1h0c-4 1-7 3-11 4v1c-2 4-7 6-12 8v-1l-1 1v-1h-2c-1 0-1 0-2-1v1h-2l1-1v-1h0c1-1 1-1 2-1h0v-1h-2v-1h0 1c1 0 1-1 2-2h-1v-1l5-3c1 0 4-1 5-1h1 2c-1 0-1 1-1 1 2 0 5-1 7-1-1 1-3 2-4 2l1 1h0z" class="j"></path><path d="M263 383l4-2-1 1c0 1 1 1 1 1v1c-1-1-3-1-4 0v-1z" class="Y"></path><path d="M272 378l1 1c-2 1-4 1-6 2l-4 2-3 1v-1c0-1 3-2 4-2 2-2 5-1 8-3h0z" class="Q"></path><path d="M266 378c1 0 4-1 5-1l1 1c-3 2-6 1-8 3l-2-1 4-2z" class="Y"></path><path d="M257 380c2 0 3-1 5-1 1-1 3-1 4-1l-4 2 2 1c-1 0-4 1-4 2l-4 1h0v-1h-2v-1h0 1c1 0 1-1 2-2z" class="L"></path><path d="M256 383c1-1 4-3 6-3l2 1c-1 0-4 1-4 2l-4 1h0v-1z" class="b"></path><path d="M260 384l3-1v1c1-1 3-1 4 0-3 1-5 2-7 3l-1 1v-1h-2c-1 0-1 0-2-1v1h-2l1-1v-1h0c1-1 1-1 2-1l4-1v1z" class="m"></path><path d="M256 384l4-1v1c-2 1-3 1-5 2v1h-2l1-1v-1h0c1-1 1-1 2-1z" class="L"></path><path d="M267 375h2c-1 0-1 1-1 1 2 0 5-1 7-1-1 1-3 2-4 2s-4 1-5 1-3 0-4 1c-2 0-3 1-5 1h-1v-1l5-3c1 0 4-1 5-1h1z" class="m"></path><path d="M267 375h2c-1 0-1 1-1 1-1 1-2 1-3 1v-1l1-1h1z" class="T"></path><defs><linearGradient id="AI" x1="260.454" y1="402.668" x2="267.687" y2="384.621" xlink:href="#B"><stop offset="0" stop-color="#bebcbf"></stop><stop offset="1" stop-color="#e2e2e0"></stop></linearGradient></defs><path fill="url(#AI)" d="M269 386c1 0 3-1 4-2 1 0 2 0 3 1h0l-1 1h-1c0 1 0 2 1 2-1 1-3 2-3 2 0 2-1 3-2 4l-3 2v1l-2 2h0l-2 2-1-1-2 1h-2c-1 1-2 1-2 2-1-1-1-1-1-2h0c-1-1-1-1-1-2l-1 1h-1l1-2 6-4 2-1c0-1 1-1 1-2h-1l1-1c-1-1-2-1-3-1h1c1-1 2-1 3-2h1c1-1 0 0 0-1h1c1-1 2 0 3 0h1z"></path><path d="M264 392h0l-1 1h1v1h-2v-1l2-1z" class="i"></path><path d="M257 400h1v1c-1 1-2 1-2 2-1-1-1-1-1-2h0l2-1z" class="X"></path><path d="M272 390c0 2-1 3-2 4l-3 2h-1v-1c1-2 5-4 6-5z" class="o"></path><path d="M262 390c1-1 3-1 4-2 2 0 3-1 4-1l-6 5-2 1h-1c0-1 1-1 1-2h-1l1-1z" class="b"></path><path d="M261 393h1v1h2c-1 1-4 2-6 2-1 1-3 2-4 3l-1 1h-1l1-2 6-4 2-1z" class="T"></path><path d="M269 386c1 0 3-1 4-2 1 0 2 0 3 1h0c-1 1-2 1-3 1-1 1-2 1-3 1s-2 1-4 1c-1 1-3 1-4 2-1-1-2-1-3-1h1c1-1 2-1 3-2h1c1-1 0 0 0-1h1c1-1 2 0 3 0h1z" class="j"></path><path d="M266 395v1h1v1l-2 2h0l-2 2-1-1-2 1h-2v-1h-1c3-3 6-4 9-5z" class="L"></path><path d="M260 401v-1h1c1-2 2-2 3-3 0 2 0 2-2 3h0l-2 1z" class="o"></path><path d="M266 396h1v1l-2 2h0l-2 2-1-1h0c2-1 2-1 2-3l2-1z" class="m"></path><path d="M358 305h1c-1 1-2 2-2 3v2 1h2 0c-1 1-2 2-3 4v1h0c-3 4-7 8-6 14v1h0v3c1 1 1 1 1 2v1 1 2s1 2 1 3h0c2 1 3 3 4 4 1 0 1 1 2 1h2l-1 1c1 1 1 1 2 1h1c1 1 0 1 1 1h2 2 1-1c0 1 0 1-1 1s-2 0-3-1l-5-2h0c-1 0-2-1-2-1l-3-3v1 2h-1v2l-1 1v-2h0v-1-2h-1v-1c-3-3-4-5-6-8h2l1 1s-1 0-1-1h0v-9c0-2 0-4 1-6v-8l2 1h0c0 1 0 2 1 3 0-1 2-2 2-4h1 0c1 0 1-1 2-1l1-1v-2-3l2-2z" class="l"></path><path d="M358 305h1c-1 1-2 2-2 3v2 1h2 0c-1 1-2 2-3 4v1h0-2 0l-4 5v-1h0l-1-1c1 0 0 0 1-1 0-1 2-2 2-4h1 0c1 0 1-1 2-1l1-1v-2-3l2-2z" class="i"></path><path d="M313 331s1-1 2-1h0c0 1 1 1 1 2 1 1 1 1 1 2l1 1c0 1 2 2 2 4v3 3c-1 1-2 1-2 1-2 1-3 2-4 3 0 2-2 4-3 4-2 1-3 1-4 3-1 1-3 2-4 3l-5 1c4-2 9-5 10-9h1c0-1 1-2 1-2 0-1 0-1-1-1h-1c-1 0-1 1-2 1l-5 1c-1 1-2 2-4 3v-2h-1 1v-1l1-1 2-1h0v-1c3 0 5-2 7-2 1 0 1 0 2-1l-1-1h1l2-2c0-1 1-1 1-2l-1-1h4 1l-2-5-1-2z" class="T"></path><path d="M313 331s1-1 2-1h0c0 1 1 1 1 2 1 1 1 1 1 2 0-1-1-1-2-2h0 0s-1 0-1 1l-1-2z" class="V"></path><path d="M316 338v3h1 0s1 2 0 2v1c-1 1-2 1-3 2-1 0-3 2-3 3-1 1-2 1-2 2 0-1 1-2 1-2 0-1 0-1-1-1h-1c-1 0-1 1-2 1l-5 1c-1 1-2 2-4 3v-2h-1 1v-1l1-1 2-1h0v-1c3 0 5-2 7-2 1 0 1 0 2-1l-1-1h1l2-2c0-1 1-1 1-2l-1-1h4 1z" class="m"></path><path d="M311 338h4v2c-1 1-3 1-4 1 0-1 1-1 1-2l-1-1z" class="Y"></path><path d="M300 348c3 0 5-2 8-2 0 1-2 2-2 3l-5 1c-1 1-2 2-4 3v-2h-1 1v-1l1-1 2-1z" class="X"></path><path d="M288 293c2 0 3 1 5 1l2 2-1 1c3 2 5 4 8 6l-1 1-1-1h-2c0 1 0 1 1 1 2 1 4 4 5 6h0c1 1 1 2 2 3-1 0-2-1-2-1h-1c2 2 3 4 4 6h0l-4-5h-2l-1 1h1c2 1 3 3 4 5v1-1l-3-3h0l-1 1 2 2h0-1l-2-2h-2v1h0c-1-1 0-1-1-1v-1c-2-2-4-4-7-6h0c0-1 0-1-1-2-1 0-1-1-1-2l-2-1 1-1h0c2 1 3 2 5 3h1c-1-2-3-3-5-4-1-1-2-2-3-4l6 4c2 1 2 2 3 4 1 1 4 3 5 4 0-2-3-4-5-5h0v-1l1-1c-2-3-5-3-7-6 0-1-1-1-2-2h1l-2-2h0 0l2-1 1 1v-1z" class="L"></path><path d="M288 306c1 1 3 2 4 4 2 2 6 5 6 7v1h0c-1-1 0-1-1-1v-1c-2-2-4-4-7-6h0c0-1 0-1-1-2-1 0-1-1-1-2z" class="R"></path><path d="M287 296h0 2c3 1 7 5 10 6 1 0 1 1 1 1h-2-1c-3-2-5-5-8-6 1 2 4 4 6 6 1 0 1 1 2 2 2 2 4 4 6 7-1-1-2-1-3-2s-3-5-5-4h-1 0v-1l1-1c-2-3-5-3-7-6 0-1-1-1-2-2h1z" class="M"></path><path d="M288 293c2 0 3 1 5 1l2 2-1 1c3 2 5 4 8 6l-1 1-1-1s0-1-1-1c-3-1-7-5-10-6h-2 0l-2-2h0 0l2-1 1 1v-1z" class="G"></path><path d="M288 293c2 0 3 1 5 1l2 2-1 1-6-3v-1z" class="D"></path><path d="M266 291c3 1 7 2 10 3 1 0 3-1 4 0h1l-1-1c2 0 4 1 5 1h0l2 2h-1c1 1 2 1 2 2 2 3 5 3 7 6l-1 1v1h0c2 1 5 3 5 5-1-1-4-3-5-4-1-2-1-3-3-4l-6-4c1 2 2 3 3 4 2 1 4 2 5 4h-1c-2-1-3-2-5-3h0l-1 1 2 1c0 1 0 2 1 2 1 1 1 1 1 2h-3v1l-1-1-2-1h-1c-1 0-2-1-3-2l-4-2c-2 0-4-1-6-1v-2h-1l1-1-1-1c-1 0 0 0 0-1h1 1v-1-1h3 0l-9-3h3c0 1 1 1 2 1-1-1-1-1-2-1s-2 0-2-1h-1 0c1-1 2 0 3 0h1c-1-1-1-1-3-1v-1z" class="H"></path><path d="M269 299h1l6 2-1 1-5-1-1-1c-1 0 0 0 0-1z" class="n"></path><path d="M276 301l5 1c0 1 0 1-1 2l-5-2 1-1z" class="X"></path><path d="M274 297l3 1h0 1c1 1 3 1 5 2 1 0 3 2 4 3-4-2-7-3-12-4h-4v-1-1h3z" class="S"></path><path d="M274 297l3 1h0-1c1 0 1 1 2 1h-3-4v-1-1h3z" class="X"></path><path d="M270 301l5 1 5 2c1-1 1-1 1-2 2 0 3 1 5 1 1 0 0 0 1 1l-1 1 2 1c0 1 0 2 1 2 1 1 1 1 1 2h-3v1l-1-1-2-1h-1c-1 0-2-1-3-2l-4-2c-2 0-4-1-6-1v-2h-1l1-1z" class="E"></path><path d="M270 302c4 1 9 2 11 5 1 0 2 1 3 2h-1c-1 0-2-1-3-2l-4-2c-2 0-4-1-6-1v-2z" class="S"></path><path d="M281 302c2 0 3 1 5 1 1 0 0 0 1 1l-1 1 2 1c0 1 0 2 1 2 1 1 1 1 1 2h-3v1l-1-1-1-2c-2-1-4-2-5-4 1-1 1-1 1-2z" class="J"></path><path d="M286 305l2 1c0 1 0 2 1 2v1c-2-1-3-2-4-4h1z" class="I"></path><path d="M281 302c2 0 3 1 5 1 1 0 0 0 1 1l-1 1h-1c-1-1-2-1-3-2 1 2 2 4 4 5h-1c-2-1-4-2-5-4 1-1 1-1 1-2z" class="k"></path><defs><linearGradient id="AJ" x1="272.588" y1="289.796" x2="281.889" y2="300.073" xlink:href="#B"><stop offset="0" stop-color="#444746"></stop><stop offset="1" stop-color="#736d6f"></stop></linearGradient></defs><path fill="url(#AJ)" d="M266 291c3 1 7 2 10 3 1 0 3-1 4 0h1l-1-1c2 0 4 1 5 1h0l2 2h-1c1 1 2 1 2 2 2 3 5 3 7 6l-1 1c-2-3-6-5-8-7-3 0-5-1-7-2v1l-2 1-3-1h0l-9-3h3c0 1 1 1 2 1-1-1-1-1-2-1s-2 0-2-1h-1 0c1-1 2 0 3 0h1c-1-1-1-1-3-1v-1z"></path><path d="M274 295l5 1v1l-2 1-3-1h0v-2z" class="E"></path><path d="M265 293c3 0 6 2 9 2v2l-9-3h3c0 1 1 1 2 1-1-1-1-1-2-1s-2 0-2-1h-1z" class="U"></path><path d="M280 293c2 0 4 1 5 1h0l2 2h-1l-10-2c1 0 3-1 4 0h1l-1-1z" class="D"></path><path d="M263 283c4 0 8 1 12 2l1-1 6 2 3 1 3 1c1 1 2 1 4 1 1 0 3 2 4 2 2 1 5 2 6 3 2 1 2 3 3 4h1 1c-1-3-3-4-4-6-1 0-1 0-1-1h0c1 0 2 1 2 1v1c1 2 2 3 4 3 1 2 2 4 4 6 1 1 1 2 2 3 2 2 4 3 4 5v1-1h-2v3l-1-1h-1v1 1c0-1 0-1-1-1l-1-1v1h-1c-2-1-3-4-5-6 0-1-3-3-4-4-3-2-5-4-8-6l1-1-2-2c-2 0-3-1-5-1v1l-1-1-2 1h0 0 0c-1 0-3-1-5-1h0l1-1s1 1 2 0h-1v-1c-2 0-3-1-4-1l-12-3h4c2 1 3 0 4 1h1c1 1 2 1 3 1l1-1c1 1 2 0 3 0 0 1 1 1 2 1-2-1-3-2-4-2l-12-3c-2 0-3 0-5-1h0z" class="B"></path><path d="M287 291h2v1c1 0 3 1 4 1v1c-2 0-3-1-5-1l-1-1v-1z" class="F"></path><path d="M276 284l6 2 3 1-1 1-9-3 1-1z" class="J"></path><path d="M293 293c2 1 4 1 6 3l3 3c1 0 2 0 3 1 1 0 1 1 2 2l-1 1c-1-1-1-2-2-2-3-1-3-2-5-3-1-1-3-2-4-2l-2-2v-1z" class="a"></path><path d="M278 290c2-1 3-1 5-1 1 1 2 1 3 1 0 1 0 0 1 1h0v1l1 1v1l-1-1-2 1h0 0 0c-1 0-3-1-5-1h0l1-1s1 1 2 0h-1v-1c-2 0-3-1-4-1z" class="E"></path><path d="M287 292c-1 0-1 0-2-1h0 2 0v1z" class="M"></path><path d="M282 291c1 0 1 1 2 1 1 1 2 1 3 1l-2 1h0 0 0c-1 0-3-1-5-1h0l1-1s1 1 2 0h-1v-1z" class="k"></path><path d="M307 300h1l5 5c2 2 3 2 5 5h-2v3l-1-1h-1v1 1c0-1 0-1-1-1v-1c-1-3-4-7-7-9l1-1 8 8h0c-1-4-5-7-8-10z" class="l"></path><path d="M295 296c1 0 3 1 4 2 2 1 2 2 5 3 1 0 1 1 2 2 3 2 6 6 7 9v1l-1-1v1h-1c-2-1-3-4-5-6 0-1-3-3-4-4-3-2-5-4-8-6l1-1z" class="E"></path><path d="M311 313c-2-4-5-7-8-11 3 1 8 7 9 9l1 1h0v1l-1-1v1h-1z" class="f"></path><path d="M285 287l3 1c1 1 2 1 4 1 1 0 3 2 4 2 2 1 5 2 6 3 2 1 2 3 3 4h1 1c-1-3-3-4-4-6-1 0-1 0-1-1h0c1 0 2 1 2 1v1c1 2 2 3 4 3 1 2 2 4 4 6 1 1 1 2 2 3 2 2 4 3 4 5v1-1c-2-3-3-3-5-5l-5-5h-1-1l-1-1c-2-1-4-3-6-4-5-3-10-5-15-7l1-1z" class="R"></path><defs><linearGradient id="AK" x1="303.157" y1="323.884" x2="296.963" y2="341.439" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#AK)" d="M282 320c2-1 6 0 8 0l11 2c4 2 7 4 11 7l1 2 1 2 2 5h-1-4l1 1c0 1-1 1-1 2l-2 2h-1l1 1c-1 1-1 1-2 1-2 0-4 2-7 2v1h0l-2 1c-1-1-2-1-4-1l-3-1-1-1h0l1-1v-1c-1-1-3-2-5-3l-3-4c-1-2-2-3-2-4-1-1-1-2-1-3 0-2-1-4 0-5v-1-2l1-2h1z"></path><path d="M303 333c0 1 1 1 1 2-1 1 0 2 0 3l-2 1h-1c-1-1-1-2-1-3l3-3z" class="b"></path><path d="M302 342l9-4 1 1c0 1-1 1-1 2l-2 2-1-1v-2l-3 1c-1 1-2 1-3 1z" class="V"></path><path d="M304 329v-1h1c2 1 3 2 4 4v3c-1 1-3 2-4 3h-1c0-1-1-2 0-3 0-1-1-1-1-2 1-1 1-2 1-4z" class="Z"></path><path d="M304 329l1 1v3s1 0 1 1-1 3-1 4h-1c0-1-1-2 0-3 0-1-1-1-1-2 1-1 1-2 1-4zm-17 4v-1c2 0 2-1 3-2h1c2 2 3 5 7 6h2c0 1 0 2 1 3h1c-2 1-4 1-6 1-3-2-6-4-9-7h0z" class="Q"></path><path d="M287 333c1-1 2 0 3 0 1 1 2 1 2 2 1 2 3 2 4 3 1 0 1 0 2 1h0c-1 0-1 0-2 1-3-2-6-4-9-7z" class="d"></path><path d="M282 320c2-1 6 0 8 0 0 1-1 2-1 3s1 3 1 4l1 3h-1c-1 1-1 2-3 2v1c-1-1-1-1-1-2h-2c-1-2-1-4-2-5v-3h-1c0 1 0 1-1 2h0v-1-2l1-2h1z" class="B"></path><path d="M281 320h1v6-3h-1c0 1 0 1-1 2h0v-1-2l1-2z" class="f"></path><path d="M286 329c-1-1-1-1-1-2s0-2 1-4h3c0 1 1 3 1 4l1 3h-1c-1 1-1 2-3 2v1c-1-1-1-1-1-2v-2h0z" class="O"></path><path d="M286 329c-1-1 0-2 0-3 1 0 2 1 3 1 0 1 0 2-1 3l-2-1h0z" class="W"></path><path d="M289 327h1l1 3h-1c-1 1-1 2-3 2v1c-1-1-1-1-1-2v-2l2 1c1-1 1-2 1-3z" class="S"></path><path d="M280 325h0c1-1 1-1 1-2h1v3c1 1 1 3 2 5a19.81 19.81 0 0 0 11 11h7c1 0 2 0 3-1l3-1v2l1 1h-1l1 1c-1 1-1 1-2 1-2 0-4 2-7 2v1h0l-2 1c-1-1-2-1-4-1l-3-1-1-1h0l1-1v-1c-1-1-3-2-5-3l-3-4c-1-2-2-3-2-4-1-1-1-2-1-3 0-2-1-4 0-5z" class="l"></path><path d="M308 342l1 1h-1c-2 1-5 3-7 3h-1c-1 0-3 0-4-1 2 0 5 0 6-1l1-1h2c0-1 0-1 1-2v2h1l1-1z" class="j"></path><path d="M300 346v-1h1v1h-1z" class="V"></path><path d="M308 343l1 1c-1 1-1 1-2 1-2 0-4 2-7 2v1h0l-2 1c-1-1-2-1-4-1l-3-1-1-1h0l1-1v-1c2 0 4 1 5 1 1 1 3 1 4 1h1c2 0 5-2 7-3z" class="d"></path><path d="M269 260l1-1v1c1 0 2 1 3 1l1-2c1 2 2 3 4 4v-1c0-1 1-1 1-2h1 1c4 1 8 3 12 5 3 2 7 4 11 6l2 1 3 2c1 1 3 2 3 4h0c0 1 1 2 2 2l-1 1c0 1 1 2 1 2 2 2 4 5 4 8 0 2 3 6 4 9h0c2 2 4 4 4 7l-1-2-2 1v-2h-1c0 2 2 2 2 4h0c-1-1-2-2-2-3h-1-2v1c1 1 1 1 1 2-1 0-2 0-3-2-2-1-4-4-5-6l-4-4c-2 0-3-1-4-3v-1s-1-1-2-1h0c0 1 0 1 1 1 1 2 3 3 4 6h-1-1c-1-1-1-3-3-4-1-1-4-2-6-3 0-1 0-1 1-2l1 1h1l-2-2c0-1 0-1-1-2 1 0 2 0 2-1-1-1-3-3-5-4-4-2-9-4-14-6-1 0-2-1-4-1v-1-1c-4 0-7-2-11-3h-2-1 0c-1 0-1-1-2-1 1-1 4-1 5-1h0 0v-1c-1 0-1 0-2-1h0c1 0 2 0 3-1v-1l-4-1-4-1v-1c4 1 9 2 12 3l1-1 1 1 1-1c-1 0-2-1-2-2h-1z" class="G"></path><path d="M281 270c1 1 2 1 4 2l-1 1-5-2 2-1z" class="L"></path><path d="M292 278h1c2 0 3 1 4 2h-1v1l-4-3z" class="S"></path><path d="M285 272c2 1 3 2 4 4h-1l-4-3 1-1z" class="Z"></path><path d="M296 281v-1h1c1 0 1 1 2 1s2 0 2 1h1c-2 0-1 0-2 2l-4-3z" class="n"></path><path d="M300 284c1-2 0-2 2-2l3 2 1 2-2 1-4-3z" class="W"></path><path d="M285 270l2 1 3 1 5 3h-1c-3-1-6-2-9-4h0v-1z" class="S"></path><path d="M285 270l2 1h-2 0v-1z" class="Q"></path><path d="M271 265c3 3 6 4 10 5l-2 1-8-3v-1h-2v-1h1l1-1z" class="W"></path><path d="M265 263c2 1 4 1 6 2l-1 1h-1v1h2v1l-7-1h0 0v-1c-1 0-1 0-2-1h0c1 0 2 0 3-1v-1z" class="Q"></path><path d="M265 263c2 1 4 1 6 2l-1 1c-2-1-5-1-8-1h0c1 0 2 0 3-1v-1z" class="Y"></path><path d="M264 267l7 1 8 3 5 2 4 3c1 1 3 2 4 2l4 3 4 3 4 3 2 2c-2-1-2-2-4-3l-6-4c-3-2-6-4-9-5l-12-4v-1c-4 0-7-2-11-3h-2-1 0c-1 0-1-1-2-1 1-1 4-1 5-1z" class="D"></path><path d="M269 260l1-1v1c1 0 2 1 3 1l1-2c1 2 2 3 4 4v-1c0 1 0 1 1 2h1l9 3h0v2h0l1 3-3-1-2-1v1c-1 0-2-1-3-1-1-1-2-1-4-1-3-1-6-4-9-6l1-1 1 1 1-1c-1 0-2-1-2-2h-1z" class="M"></path><path d="M277 266c3 0 6 3 8 4v1c-1 0-2-1-3-1-1-1-2-1-4-1 0-1 0-2-1-3z" class="d"></path><path d="M269 260l1-1v1c1 0 2 1 3 1 2 1 3 2 4 3v1c-2-1-3-2-4-2h-1c1 2 4 2 5 3s1 2 1 3c-3-1-6-4-9-6l1-1 1 1 1-1c-1 0-2-1-2-2h-1z" class="b"></path><path d="M275 273l12 4c3 1 6 3 9 5l6 4c2 1 2 2 4 3l-2-2 2-1-1-2c1 1 2 1 3 3l3 4c4 3 5 8 7 13l1 1v1c1 1 1 1 1 2-1 0-2 0-3-2-2-1-4-4-5-6l-4-4c-2 0-3-1-4-3v-1s-1-1-2-1h0c0 1 0 1 1 1 1 2 3 3 4 6h-1-1c-1-1-1-3-3-4-1-1-4-2-6-3 0-1 0-1 1-2l1 1h1l-2-2c0-1 0-1-1-2 1 0 2 0 2-1-1-1-3-3-5-4-4-2-9-4-14-6-1 0-2-1-4-1v-1z" class="g"></path><path d="M315 303c0-1 1-1 1-2l3 5h0c1 1 1 1 1 2-1 0-2 0-3-2 0-1-1-3-2-3z" class="f"></path><path d="M306 286c3 2 5 6 7 9l-3-3c-1-1-3-3-4-3l-2-2 2-1z" class="Q"></path><path d="M315 303c-2-4-5-7-7-10v-1c1 1 2 2 3 2 1 3 4 5 5 7 0 1-1 1-1 2z" class="j"></path><path d="M298 285l3 2c0 1 1 1 2 1v1c-1 0-2 1-3 1v-1l-1 1h-1 1l-2-2c0-1 0-1-1-2 1 0 2 0 2-1z" class="E"></path><path d="M281 260c4 1 8 3 12 5 3 2 7 4 11 6l2 1 3 2c1 1 3 2 3 4h0c0 1 1 2 2 2l-1 1c0 1 1 2 1 2 2 2 4 5 4 8 0 2 3 6 4 9h0c2 2 4 4 4 7l-1-2-2 1v-2h-1c0 2 2 2 2 4h0c-1-1-2-2-2-3h-1-2l-1-1c-2-5-3-10-7-13l1-2h0c-4-6-11-11-17-14l-5-3-1-3h0v-2h0l-9-3h-1c-1-1-1-1-1-2s1-1 1-2h1 1z" class="J"></path><path d="M304 271l2 1c-1 1 0 1-1 1 0 0-1 0-1-1h-1l1-1z" class="a"></path><path d="M296 272v-1c2 2 5 3 8 5h0l7 7h-1c-1-2-4-4-6-5-3-2-5-4-8-6zm-18-10c0-1 1-1 1-2h1c4 2 9 4 13 6 3 2 5 4 8 6l-12-5h0l-9-3h-1c-1-1-1-1-1-2z" class="B"></path><path d="M289 269c1 0 2 1 3 1 2 0 3 0 4 1v1c3 2 5 4 8 6 2 1 5 3 6 5h1c1 1 2 2 3 4 0 0 1 2 2 3v-1c-1-2-2-4-2-6 2 2 4 5 4 8 0 2 3 6 4 9h0c2 2 4 4 4 7l-1-2-2 1v-2h-1c0 2 2 2 2 4h0c-1-1-2-2-2-3h-1-2l-1-1c-2-5-3-10-7-13l1-2h0c-4-6-11-11-17-14l-5-3-1-3h0z" class="H"></path><path d="M318 304c1-1 1-1 2-1l1 2h-2l-1-1z" class="V"></path><path d="M312 289c3 4 7 9 8 14-1 0-1 0-2 1-2-5-3-10-7-13l1-2z" class="j"></path><path d="M322 305l-3-6c-1-3-3-6-4-8h1c2 2 3 5 5 8l4 6-2 1v-2h-1c0 2 2 2 2 4h0c-1-1-2-2-2-3z" class="Y"></path><path d="M296 272c3 2 5 4 8 6 2 1 5 3 6 5 1 1 2 2 2 3h-1l-8-7c-2-2-3-3-5-4-1-1-2-1-3-2l-1-1h2z" class="a"></path><path d="M263 226c10 2 21 5 31 10 9 5 18 14 24 22 4 6 7 12 10 18-1 0-1 1-1 1v2l1 2c0 1 1 2 1 4h-1c2 7 4 13 4 21v-1h0-1v3l-1 1v1c-1 0-1-1-2-1v-1c0-1-1-3-2-4-1-2-1-3-2-4h-2 0c-1-3-4-7-4-9 0-3-2-6-4-8 0 0-1-1-1-2l1-1c-1 0-2-1-2-2h0c0-2-2-3-3-4l-3-2-2-1c-4-2-8-4-11-6-4-2-8-4-12-5l-2-1h-1l-2-6-5-9h3c0-1 0-1-1-1v-1c-3-1-4-4-5-7l1-1v-1c-1-1-3-1-5-1v1c-1-1-1-2-2-3l-2-3c1 0 2 0 3-1z" class="P"></path><path d="M262 230c1 0 2 1 3 1 4 1 8 1 11 2v1c-2-1-4-1-7-1-1-1-3-1-5-1v1c-1-1-1-2-2-3z" class="R"></path><path d="M322 275l1-1 4 10c0-1-1-3-1-4s-1-3-2-4v-1c1 0 2 3 3 4v1 1 1 1h1v2c2 7 4 13 4 21v-1h0c0-2-1-4-1-6-1-4-3-8-4-12 0-1-2-4-2-5l-1-2-1-2-1-3z" class="p"></path><path d="M315 264l3 3c1 2 2 4 3 5v1c1 1 1 1 1 2h0l1 3 1 2 1 2c0 1 2 4 2 5l-1 2c0-1-1-2-1-4v-1l-1-3h-1v-2c-3-1-3-2-4-4v-1-1h-2l-3-8 1-1z" class="B"></path><path d="M319 273l-3-6v-1c1 1 1 1 1 2 1 1 1 1 1 2l1 1 2 5 2 3c-3-1-3-2-4-4v-1-1z" class="e"></path><path d="M269 233c3 0 5 0 7 1l4 1h0v1h-1 1c2 2 6 3 8 5l12 9c6 4 10 10 15 14l-1 1c-4-5-9-11-15-15-3-3-7-5-10-7-4-3-7-4-11-5-1-1-2-2-4-2-1-1-4-1-5-2v-1z" class="H"></path><path d="M269 233c3 0 5 0 7 1l4 1h0v1h-1c-3-1-6-2-10-2v-1z" class="e"></path><path d="M299 250c6 4 11 10 15 15 1 3 2 5 3 8h2v1 1l-2-2-1 1s-1-2-2-2c-1-2-1-3-2-4l-1-1-1-1v-1l-5-5-1-1c-2-2-4-4-7-6l1-1h0v-1l1-1z" class="U"></path><path d="M269 234c1 1 4 1 5 2 2 0 3 1 4 2 4 1 7 2 11 5 3 2 7 4 10 7l-1 1v1h0l-1 1c3 2 5 4 7 6l-1-1c-3-1-6-3-8-5l-1 1-2-2h-1l-4-2-1-1h0c-1-1-1-1-2-1l-4-2c-1 0-2 0-3-1l-3-1c0-1 0-1-1-1v-1c-3-1-4-4-5-7l1-1z" class="e"></path><path d="M295 253c-2-2-4-3-6-4-1-1-2-2-4-2-2-1-6-2-8-4v-1c6 1 10 5 15 7 2 1 4 3 5 4 3 2 5 4 7 6l-1-1c-3-1-6-3-8-5z" class="D"></path><path d="M298 252c-3-1-5-3-7-5-5-4-12-7-17-9h-1-2s0-1-1-1l1-1 2 1h0l1 1c6 1 13 4 18 8 2 1 4 3 6 5v1h0z" class="B"></path><path d="M311 269l1-1c1 1 1 2 2 4 1 0 2 2 2 2l1-1 2 2c1 2 1 3 4 4v2h1l1 3v1c0 2 1 3 1 4l1-2c1 4 3 8 4 12 0 2 1 4 1 6h-1v3l-1 1v1c-1 0-1-1-2-1v-1c0-1-1-3-2-4-1-2-1-3-2-4h-2 0c-1-3-4-7-4-9 0-3-2-6-4-8 0 0-1-1-1-2l1-1c-1 0-2-1-2-2h0c0-2-2-3-3-4h1l4 3c1 0 1 0 2 1l-1-2v-1c-1-2-2-4-4-6z" class="O"></path><path d="M315 275c1 1 3 3 3 5 1 2 1 4 1 6-1-3-3-5-3-8l-1-2v-1z" class="p"></path><path d="M310 274l4 3 1 2v1c1 2 3 4 5 6 1 3 2 6 5 8 0 1 1 3 1 4l2 5-3-6c0-3-3-4-5-7-1-1-1-3-2-4-2-2-3-4-4-6-1 0-2-1-2-2h0c0-2-2-3-3-4h1z" class="K"></path><path d="M314 283s-1-1-1-2l1-1c1 2 2 4 4 6 1 1 1 3 2 4 2 3 5 4 5 7l-1 1c1 1 2 4 3 5l1 3c0 1 1 2 0 3v-1c0-1-1-3-2-4-1-2-1-3-2-4h-2 0c-1-3-4-7-4-9 0-3-2-6-4-8z" class="S"></path><path d="M316 274l1-1 2 2c1 2 1 3 4 4v2h1l1 3v1c0 2 1 3 1 4l1-2c1 4 3 8 4 12 0 2 1 4 1 6h-1v3l-1 1v1c-1 0-1-1-2-1 1-1 0-2 0-3l-1-3c-1-1-2-4-3-5l1-1 3 6-2-5c0-1-1-3-1-4v-1c-2-2-2-5-4-7 0-1-1-2-1-4-1-1-1-3-1-4-1-1-2-3-3-4z" class="p"></path><path d="M325 293c-1-5-3-9-5-14 1 2 3 4 4 6s1 4 2 5c0 2 2 6 2 8h-1-1c0-1-1-3-1-4v-1z" class="L"></path><path d="M327 287c1 4 3 8 4 12 0 2 1 4 1 6h-1v3l-1 1v1c-1 0-1-1-2-1 1-1 0-2 0-3l-1-3c-1-1-2-4-3-5l1-1 3 6-2-5h1 1c1 2 1 5 2 7 0-5-2-12-4-16l1-2z" class="f"></path><path d="M328 306h2v2 1 1c-1 0-1-1-2-1 1-1 0-2 0-3z" class="l"></path><path d="M271 244h3l3 1c1 1 2 1 3 1l4 2c1 0 1 0 2 1h0l1 1 4 2h1l2 2 1-1c2 2 5 4 8 5l1 1 1 1 5 5v1l1 1 1 1-1 1c2 2 3 4 4 6v1l1 2c-1-1-1-1-2-1l-4-3h-1l-3-2-2-1c-4-2-8-4-11-6-4-2-8-4-12-5l-2-1h-1l-2-6-5-9z" class="B"></path><path d="M287 255c1 1 3 2 4 4h0-1c-1-1-3-2-4-4h1z" class="U"></path><path d="M295 253c2 2 5 4 8 5l-1 1c-3-1-5-3-7-4 0-1 0-1-1-1l1-1z" class="P"></path><path d="M305 264c0-1 0-1 1-2 1 2 3 4 4 6l1 1c2 2 3 4 4 6v1l1 2c-1-1-1-1-2-1l-4-3c-2-2-5-3-6-5l-3-3c3 1 7 5 9 7 0-2-1-4-3-6-1-1-2-1-2-3z" class="E"></path><path d="M305 264c0-1 0-1 1-2 1 2 3 4 4 6l1 1c2 2 3 4 4 6v1c-1-2-3-3-4-5-2-3-3-6-6-7z" class="P"></path><path d="M271 244h3l3 1v1h1c0 1 1 1 1 1l3 1h1l1 1 4 2c1 1 1 1 2 1 0 1 1 1 1 1l7 5 1 2h0c-2-1-4-4-6-4h0c0 2 2 3 3 4-1 0-2-2-4-2v-1h-1c-2-1-4-3-6-4-1 0-1-1-2-1h-1c-1-1-2-1-3-1h0c1 1 2 1 3 1v1c2 0 3 1 5 2h-1c1 2 3 3 4 4v1c1 1 2 2 3 2l1-1c0 1 1 1 1 1 1 0 1 1 2 1h0l-2 2h1-1-2c-4-2-8-4-12-5l-2-1h-1l-2-6-5-9z" class="e"></path><path d="M276 250v2l1 1c1 2 1 3 3 5l-1 1h-1l-2-6v-3z" class="B"></path><path d="M290 260c-2-1-4-4-6-4-1-1-2-1-3-1h0v-1c2 1 3 1 5 1 1 2 3 3 4 4v1zm-19-16h3l3 1v1c-1 0-1-1-2-1h-2c0 2 2 3 3 5h0v3l-5-9z" class="C"></path><path d="M225 227c2-4 4-7 6-10s5-5 8-5c6 0 10 3 13 6h3v1l1 1 1 1 1 1h3 5v1h1v1h-1l-1 1h-1-3-1c1 1 1 1 2 1h1c-1 1-2 1-3 1l2 3c1 1 1 2 2 3v-1c2 0 4 0 5 1v1l-1 1c1 3 2 6 5 7v1c1 0 1 0 1 1h-3l5 9 2 6h1l2 1h-1-1c0 1-1 1-1 2v1c-2-1-3-2-4-4l-1 2c-1 0-2-1-3-1v-1l-1 1h1c0 1 1 2 2 2l-1 1-1-1-1 1c-3-1-8-2-12-3v1l4 1 4 1v1c-1 1-2 1-3 1h-4c-7 1-14 0-20-1-2 0-3-1-4-1h-1-2-2l1 1c-1 0-2 1-3 1h-1c-1 0-1 0-2 1h-1c-2 0-2 0-3-1l-1 1 1 1c-1 1-1 2-1 3v3h-2c-1-1 0-3 0-5v-2c0-2 1-4 1-6h0v-4-2h0c1-1 1-3 1-5h0c1-2 1-4 1-6l2-6v-1c1-1 1-3 2-5l1-3v-1z" class="l"></path><path d="M243 248h2v1h-1c-1 0-1-1-2-1h1z" class="q"></path><path d="M224 242v-2h0 1 8c1 1 2 0 2 1l1-1c1 0 2 1 4 2-3-1-8-2-10-1h0c1 0 2 1 3 1 1-1 1-1 2 0l1 1c-2-1-5-1-7-2-2 0-4 0-5 1z" class="T"></path><path d="M224 242c1-1 3-1 5-1 2 1 5 1 7 2 1 0 2 1 3 1s3 2 5 3l-7-2c-1-1-3-1-4-1-3-1-6-1-9-1l-1-1h1z" class="S"></path><path d="M243 260c1 0 1-1 2 0h1 2c2 0 6 2 8 2 1 1 3 1 4 1s1-1 1-1l4 1v1c-1 1-2 1-3 1h-4v-1l-8-3h-4c-1 0-1-1-2-1 0 1 0 0-1 0h0z" class="i"></path><path d="M236 256c1 0 2 0 3 1-1 1-2 1-4 1-1-1-2-1-3 0h1 1 5c0 1 1 1 2 1h-14c-2 0-4 0-6 1l-1-1 1-1v1c2-1 3 0 4-1h1c3-1 6-1 9-1v-1h1z" class="Y"></path><path d="M233 244c1 0 3 0 4 1l-1 1c1 0 1 1 2 1h0c-1 1-1 0-2 1 1 1 2 1 2 2 0 0-1-1-2-1-1-1-2 0-4 0-1-1-2-1-3-1h-2l-1-2h6 1v-1h-2v-1h2z" class="V"></path><path d="M223 242l1 1c3 0 6 0 9 1h-2v1h2v1h-1-6l1 2h-2c-1 1-2 1-3 1l1-1c0-1-1-1-1-2h1v-2-2z" class="T"></path><path d="M222 249l1-1c0-1-1-1-1-2h1 0 1c2 0 6-1 8 0h-6l1 2h-2c-1 1-2 1-3 1zm5-18v-2h1c1 1 3 1 4 1v-1h0 3 2v1c2 0 3 1 4 1 1 1 2 1 2 2l2 1-1 1v-1h-2l-1-1c-3-2-5-2-8-2 1 0 1 0 2 1h2c2 0 2 2 4 2l1 1c1 2 3 3 4 4h0-1c-1-1-3-2-4-3h0v-1c-1 0-2-1-3-2-2 0-3-1-5-1-2-1-4-1-6-1z" class="i"></path><path d="M266 252c-2-2-4-3-6-5h0c3 1 5 3 7 4l3 3h-1c1 2 2 3 3 4h1l1 1-1 2c-1 0-2-1-3-1v-1l-1 1c0-1-1-1-2-2-1 0-1-1-2-1h-1v-1h1c1 1 2 1 3 1h0c-2-2-5-3-8-5h1l4 2c0-1-1-2-2-2l1-1c0 1 1 1 1 1h0 1z" class="Y"></path><path d="M265 254c0-1-1-2-2-2l1-1c0 1 1 1 1 1h0 1c1 1 2 2 3 4h-1c-1-1-2-2-3-2h0z" class="f"></path><path d="M227 248h2c1 0 2 0 3 1 2 0 3-1 4 0 1 0 2 1 2 1l1 1c1 0 2 1 2 1h0-1-2c-1-1-3 0-4 0h-1-1c1 0 2 1 3 1h-2c-3-1-7-2-9-1h-1-1c0-1-1-1 0-2v-1h0 0c1 0 2 0 3-1h2z" class="T"></path><path d="M227 248h2c1 0 2 0 3 1 2 0 3-1 4 0 1 0 2 1 2 1l1 1h-2c0-1-1-1-2-1h-3c-1-1-3 0-4-1-1 0-3 0-4 1 0-1-1 0-2 0v-1h0 0c1 0 2 0 3-1h2z" class="f"></path><path d="M227 231c2 0 4 0 6 1 2 0 3 1 5 1 1 1 2 2 3 2v1h0-1v1c0 1 1 1 2 1-2 1-3 0-5 0 0-1-1-1-1-1-1 0-2 0-3-1h-3c-2-1-2-1-4-1v-2l1-2z" class="T"></path><path d="M227 231c2 0 4 0 6 1 2 0 3 1 5 1 1 1 2 2 3 2-2 0-4-1-5-1l-1-1c-3 0-6 1-9 0l1-2z" class="Z"></path><path d="M226 235c2 0 2 0 4 1h3c1 1 2 1 3 1 0 0 1 0 1 1 2 0 3 1 5 0l2 2v1c-1 0-1-1-2-1h0l2 2h-1s-1-1-2-1v1l-1 1v-1c-2-1-3-2-4-2l-1 1c0-1-1 0-2-1h-8-1 0l2-5z" class="V"></path><path d="M226 235c2 0 2 0 4 1h-3-1l2 1h0c2 0 4 0 5 2h0c1 0 2 0 3 1l-1 1c0-1-1 0-2-1h-8-1 0l2-5z" class="m"></path><path d="M221 260c2-1 4-1 6-1h14s1 0 2 1h0-1-4c-3 0-5 0-8 1h0c1 0 2 1 3 2h-2-2l1 1c-1 0-2 1-3 1h-1c-1 0-1 0-2 1h-1c-2 0-2 0-3-1v-3l1-2z" class="Y"></path><path d="M223 266c-1 0-2 0-3-1l1-1h1l4-2c1 1 2 1 3 1h0l1 1c-1 0-2 1-3 1h-1c-1 0-1 0-2 1h-1z" class="c"></path><path d="M221 260c2-1 4-1 6-1h14s1 0 2 1h0-1c-5-2-12 0-17 1-1 0-4 1-5 1l1-2z" class="E"></path><path d="M222 252h1 1c2-1 6 0 9 1h2l2 1h3v1c-2 0-3-1-4 1h-1v1c-3 0-6 0-9 1h-1c-1 1-2 0-4 1v-1c0-2 0-3 1-5v-1z" class="L"></path><path d="M222 254c1-1 5-1 7-1h0c1 1 3 0 4 1-3 0-4 0-7 2h-4v-1c1 0 2 0 2-1h0-2 0z" class="Q"></path><path d="M237 254h3v1c-2 0-3-1-4 1h-1-4-5c3-2 4-2 7-2h4z" class="i"></path><path d="M221 258c0-2 0-3 1-5v1h0 2 0c0 1-1 1-2 1v1h4 5 4v1c-3 0-6 0-9 1h-1c-1 1-2 0-4 1v-1z" class="d"></path><path d="M242 260h1c1 0 1 1 1 0 1 0 1 1 2 1h4c2 1 5 2 8 3v1c-7 1-14 0-20-1-2 0-3-1-4-1h-1c-1-1-2-2-3-2h0c3-1 5-1 8-1h4z" class="f"></path><path d="M230 224c0-1 0-2 1-2h7 1c1 0 1 0 1 1h1 1 0c1 0 2 0 2 1h1c1 1 2 1 2 2v1l-2-2-1 1h1l5 8-4-3c0 1 0 1-1 1l-2-2c-2 0-3-1-4-1h-1c0-1-1-1-1-1h-1-5 0v1h1 0v1c-1 0-3 0-4-1l1-3 1-2z" class="T"></path><path d="M238 226l4 1c1 1 1 1 1 2h0c-1 0-1 0-2-1h-3c0-1-1-1-1-1v-1h1z" class="f"></path><path d="M230 224c4 0 8 0 12 3h0l-4-1c-3-1-6-1-9 1v-1l1-2z" class="X"></path><path d="M225 227c2-4 4-7 6-10s5-5 8-5c6 0 10 3 13 6h3v1l1 1 1 1 1 1h3 5v1h1v1h-1l-1 1h-1-3-1c1 1 1 1 2 1h1c-1 1-2 1-3 1l2 3c1 1 1 2 2 3v-1c2 0 4 0 5 1v1l-1 1c1 3 2 6 5 7v1c1 0 1 0 1 1h-3l5 9 2 6h1l2 1h-1-1c0 1-1 1-1 2v1c-2-1-3-2-4-4l-1-1h-1c-1-1-2-2-3-4h1l-3-3h0c0-1 0-1-1-2v-1h0l-1-1c-1-1-2-3-4-4l-4-5c-1-1-3-4-4-5-1-2-4-4-6-6v-1c0-1-1-1-2-2h-1c0-1-1-1-2-1h0-1-1c0-1 0-1-1-1h-1-7c-1 0-1 1-1 2l-1 2-1 3h-1v2l-1 2v2l-2 5v2h-1v2 2h-1c0 1 1 1 1 2l-1 1h0 0v1c-1 1 0 1 0 2v1c-1 2-1 3-1 5l-1 1 1 1-1 2v3l-1 1 1 1c-1 1-1 2-1 3v3h-2c-1-1 0-3 0-5v-2c0-2 1-4 1-6h0v-4-2h0c1-1 1-3 1-5h0c1-2 1-4 1-6l2-6v-1c1-1 1-3 2-5l1-3v-1z" class="l"></path><path d="M252 218h3v1l1 1-2 1-2-3z" class="B"></path><path d="M256 220l1 1 1 1h3c-2 0-3 0-4 1l-3-2 2-1z" class="M"></path><path d="M271 252c1 0 1 1 1 2 1 1 1 2 2 2 0 1-1 1-1 2h-1c-1-1-2-2-3-4h1 1v-2h0z" class="V"></path><path d="M218 254h0c1 2 1 3 0 5 0 2 1 3 0 5v3h0l1 1v1c0-1 0-1 1-2-1 1-1 2-1 3v3h-2c-1-1 0-3 0-5v-2c0-2 1-4 1-6h0v-4-2z" class="f"></path><path d="M264 233v-1c2 0 4 0 5 1v1l-1 1c1 3 2 6 5 7v1c1 0 1 0 1 1h-3l-7-11z" class="B"></path><path d="M261 222h5v1h1v1h-1l-1 1h-1-3-1c1 1 1 1 2 1h1c-1 1-2 1-3 1s-3-3-3-4c1-1 2-1 4-1z" class="E"></path><path d="M261 224l-1-1h1 5 1v1h-1l-1 1h-1-3c-1 0-1 0-1-1h1z" class="R"></path><path d="M261 225c-1 0-1 0-1-1h1 5l-1 1h-1-3z" class="K"></path><path d="M231 222c1-3 3-4 6-5 2-1 6-1 9 1l3 2c2 1 4 4 5 6l8 10 5 8c0 1 1 1 1 2s0 1 1 1c0 1 1 3 2 4v1h0v2h-1l-3-3h0c0-1 0-1-1-2v-1h0l-1-1c-1-1-2-3-4-4l-4-5c-1-1-3-4-4-5-1-2-4-4-6-6v-1c0-1-1-1-2-2h-1c0-1-1-1-2-1h0-1-1c0-1 0-1-1-1h-1-7z" class="f"></path><path d="M257 238l1-1-1-1h1c1 1 2 3 3 4v1c1 1 2 3 3 4s2 1 2 2v1l-1-1c-1-1-2-3-4-4l-4-5z" class="T"></path><path d="M231 222c1-3 3-4 6-5 2-1 6-1 9 1l3 2c2 1 4 4 5 6l8 10 5 8c0 1 1 1 1 2s0 1 1 1c0 1 1 3 2 4h-1l-1-1c0-1 0-1-1-2 0-1-1-2-1-3-1 0-1-1-1-1-1-1-2-3-3-5h-1l-1-3-4-5c-1-1-2-3-3-5h-1c-2-3-5-5-7-7-1-1-2-1-3-1h-5c3 1 7 2 8 5h0c-1-1-3-2-5-3h-2c0 1 0 1 1 1l2 2h0-1-1c0-1 0-1-1-1h-1-7z" class="c"></path><path d="M233 263h1c1 0 2 1 4 1 6 1 13 2 20 1h4 0c1 1 1 1 2 1v1h0 0c-1 0-4 0-5 1 1 0 1 1 2 1h0 1 2c4 1 7 3 11 3v1 1c2 0 3 1 4 1 5 2 10 4 14 6 2 1 4 3 5 4 0 1-1 1-2 1 1 1 1 1 1 2l2 2h-1l-1-1c-1 1-1 1-1 2-1 0-3-2-4-2-2 0-3 0-4-1l-3-1-3-1-6-2-1 1c-4-1-8-2-12-2h0c2 1 3 1 5 1l12 3c1 0 2 1 4 2-1 0-2 0-2-1-1 0-2 1-3 0l-1 1c-1 0-2 0-3-1h-1c-1-1-2 0-4-1h-4l12 3c1 0 2 1 4 1v1h1c-1 1-2 0-2 0l-1 1h0l1 1h-1c-1-1-3 0-4 0-3-1-7-2-10-3v1c2 0 2 0 3 1h-1c-1 0-2-1-3 0h0 1c0 1 1 1 2 1s1 0 2 1c-1 0-2 0-2-1h-3l9 3h0-3v1 1h-1-1c0 1-1 1 0 1l1 1-1 1c-8-2-15-3-23-2-3 0-6 1-8 1l-5 1c-9 1-20 5-28 10h0c-1 1-3 2-5 3-6 4-10 8-14 14h-1l1-8v-9c1-7 1-13 2-20 2-1 3-3 4-4l-1-1v-1c-2-1-1-2-2-3 0-1 1-3 1-4 1-1 2-3 3-4v-2h0c2-2 6-4 9-6 4-1 6-3 10-4 1 1 1 1 2 1 1 1 2 1 3 2v2c0 2-1 4 0 5h2v-3c0-1 0-2 1-3l-1-1 1-1c1 1 1 1 3 1h1c1-1 1-1 2-1h1c1 0 2-1 3-1l-1-1h2 2z" class="e"></path><path d="M235 296h-4c1-1 2-1 3-1l1 1z" class="B"></path><path d="M208 294c2-1 4-3 6-3 0 0-1 1-2 1h1l-1 1-3 2-1-1z" class="K"></path><path d="M225 298l1 1-6 3c-1 0-1 0-1-1l6-3z" class="U"></path><path d="M234 295l5-1c1 1 2 1 3 1-2 1-5 1-7 1l-1-1z" class="D"></path><path d="M245 275c2-1 4-1 6-1l1 1h-1c-2 1-5 1-8 1h3l-1-1z" class="C"></path><path d="M276 280h-2c-1 0-3-1-4-1 2-2 4 0 5-1h1c2 1 3 1 4 1h-3l-1 1z" class="B"></path><path d="M199 280l-1 1c0 1-1 1-2 2 1-1 3-2 4-2h0 0c-3 2-6 4-8 7l-1-1c2-3 5-5 8-7z" class="E"></path><path d="M260 271c3 1 7 1 10 2v1c-4-1-8-1-12-2v-1h2z" class="h"></path><path d="M279 278c3 0 5 2 8 3 2 1 3 1 5 2l2 2c1 0 2 0 2 1 1 1 1 1 1 2l2 2h-1l-1-1c-1 0 0 0-1-1-1 0-1-1-2-2h0v-1h-1c-2-2-4-2-6-3h-1c0-1 0 0-1-1s-3-2-5-2l-1-1zm-58 5c-2 1-4 2-7 3-2 1-3 3-5 3l-2 2-1-1c1 0 1-1 1-1 3-2 6-4 9-5 2-1 3-2 4-2l1 1z" class="B"></path><path d="M260 282h-2c1-1 1 0 2 0 1-1 1-1 3-1l13 3-1 1c-4-1-8-2-12-2l-3-1z" class="a"></path><path d="M270 274v-1l17 7v1c-3-1-5-3-8-3l-9-4z" class="I"></path><path d="M276 280l1-1h3c0 1 1 1 2 1v1h1 1 2v1c1 1 3 1 4 2s3 1 4 2h0 0-1-1c-1-1-6-4-8-3h0l-2-1-6-2z" class="C"></path><path d="M199 280c3-2 7-4 11-6 0 1 1 1 1 2l-11 5h0c-1 0-3 1-4 2 1-1 2-1 2-2l1-1z" class="H"></path><path d="M220 282c8-3 16-6 25-7l1 1h-3c-7 1-15 4-22 7l-1-1z" class="D"></path><path d="M186 321v5c4-3 7-7 11-10l6-3c0-1 1-1 2-1h0c-1 1-3 2-5 3-6 4-10 8-14 14h-1l1-8z" class="C"></path><path d="M237 271c1 0 2 0 3 1h-1c-6 1-11 2-17 4-4 1-8 2-12 4-1 1-3 2-4 3-2 0-3 2-4 2 5-5 12-7 19-10 2-1 6-2 9-3l7-1z" class="I"></path><path d="M232 283c11-2 20-3 31-2-2 0-2 0-3 1-1 0-1-1-2 0h2c-9 0-18 0-27 2l-1-1z" class="F"></path><path d="M284 283c2-1 7 2 8 3h1 1 0c1 1 1 2 2 2 1 1 0 1 1 1-1 1-1 1-1 2-1 0-3-2-4-2-2 0-3 0-4-1l-3-1-3-1c1-1 2-1 2 0h2l-1-1 1-1h-2v-1h0z" class="B"></path><g class="I"><path d="M294 286h0c1 1 1 2 2 2 1 1 0 1 1 1-1 1-1 1-1 2l-3-3 1-2z"></path><path d="M284 283c2-1 7 2 8 3h1 1l-1 2h-1l-2-2s-1-1-2-1l-4-2z"></path></g><path d="M293 286h1l-1 2h-1v-2h1z" class="g"></path><path d="M260 271h-2c0-1-1-1-1-1h-1c-1-1-3-1-3-2h2l1 1c4 2 11 1 15 3l4 2c2 0 3 1 4 1 5 2 10 4 14 6 2 1 4 3 5 4 0 1-1 1-2 1 0-1-1-1-2-1l-2-2c-2-1-3-1-5-2v-1l-17-7c-3-1-7-1-10-2z" class="F"></path><path d="M287 280c1 0 2 1 3 1s1 1 2 1v1c-2-1-3-1-5-2v-1z" class="J"></path><defs><linearGradient id="AL" x1="252.495" y1="300.783" x2="258.115" y2="290.021" xlink:href="#B"><stop offset="0" stop-color="#353335"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#AL)" d="M239 294c6-1 11-1 17 0h9l9 3h0-3v1 1h-1-1 0l-9-2c0-1-1-1-1-1l-7-1h-10c-1 0-2 0-3-1z"></path><path d="M259 296c4 0 7 1 11 2h1v1h-1-1 0l-9-2c0-1-1-1-1-1z" class="D"></path><path d="M246 268c2-1 5 0 7 0 4-1 7-1 11-1h0c-1 0-4 0-5 1 1 0 1 1 2 1h0 1 2c4 1 7 3 11 3v1 1l-4-2c-4-2-11-1-15-3l-1-1h-2c0 1 2 1 3 2h1s1 0 1 1h2-2v1h-19 1c-1-1-2-1-3-1l7-1c1-1 2-1 2-2z" class="R"></path><defs><linearGradient id="AM" x1="251.682" y1="296.086" x2="252.283" y2="300.33" xlink:href="#B"><stop offset="0" stop-color="#4c4a4c"></stop><stop offset="1" stop-color="#646463"></stop></linearGradient></defs><path fill="url(#AM)" d="M252 295l7 1s1 0 1 1l9 2h0c0 1-1 1 0 1l1 1-1 1c-8-2-15-3-23-2-3 0-6 1-8 1l-5 1c1-1 1-1 3-1h0c1 0 3-1 4-1h2 0-4s0-1-1 0h-6l-1-1c3-1 6-1 9-2 3 0 6 0 9-1h5c0-1 0-1-1-1z"></path><path d="M252 295l7 1s1 0 1 1c-4-1-8-1-12-1h5c0-1 0-1-1-1zm-21 5c4-1 8-1 13-1 4 0 9-1 14 0h6c2 1 3 1 5 1l1 1-1 1c-8-2-15-3-23-2-3 0-6 1-8 1l-5 1c1-1 1-1 3-1h0c1 0 3-1 4-1h2 0-4s0-1-1 0h-6z" class="P"></path><defs><linearGradient id="AN" x1="222.376" y1="281.581" x2="248.095" y2="311.225" xlink:href="#B"><stop offset="0" stop-color="#181919"></stop><stop offset="1" stop-color="#3e3c3e"></stop></linearGradient></defs><path fill="url(#AN)" d="M212 294c4 0 9-2 13-4 4 0 8-2 11-2s6 0 8 1h7c3 0 5 1 8 1 2 0 5 1 7 1v1c2 0 2 0 3 1h-1c-1 0-2-1-3 0l-13-2c-12-2-22-1-34 3-4 2-8 3-12 5-2 1-5 3-7 5 1-2 3-4 5-5 1-2 2-4 4-5l1 1 3-2v1z"></path><path d="M212 293v1c-1 1-2 2-3 2v-1h0l3-2z" class="e"></path><path d="M233 263h1c1 0 2 1 4 1 6 1 13 2 20 1h4 0c1 1 1 1 2 1v1h0c-4 0-7 0-11 1-2 0-5-1-7 0 0 1-1 1-2 2l-7 1-7 1h-1c-2-1-6 1-8 2-1 0-2-1-2-1-1 1-2 1-3 1l1-1h2v-3c0-1 0-2 1-3l-1-1 1-1c1 1 1 1 3 1h1c1-1 1-1 2-1h1c1 0 2-1 3-1l-1-1h2 2z" class="m"></path><path d="M230 264h5v1h-2c-2 1-3 1-4 1h-2 0l-1-1h1c1 0 2-1 3-1z" class="Y"></path><path d="M220 267l-1-1 1-1c1 1 1 1 3 1h1c1-1 1-1 2-1l1 1h0c-1 1-2 1-3 1-2 1-4-1-4 2v1h0 1 0c1 0 1 0 2-1s3 0 5-1h0s0 1-1 1h2 0 1 0l-11 4v-3c0-1 0-2 1-3z" class="T"></path><defs><linearGradient id="AO" x1="230.186" y1="276.582" x2="234.538" y2="264.653" xlink:href="#B"><stop offset="0" stop-color="#2c262b"></stop><stop offset="1" stop-color="#393d3a"></stop></linearGradient></defs><path fill="url(#AO)" d="M230 269l16-1c0 1-1 1-2 2l-7 1-7 1h-1c-2-1-6 1-8 2-1 0-2-1-2-1-1 1-2 1-3 1l1-1h2l11-4z"></path><path d="M202 267c4-1 6-3 10-4 1 1 1 1 2 1 1 1 2 1 3 2v2c0 2-1 4 0 5l-1 1c-2 0-3 1-5 2 0-1-1-1-1-2-4 2-8 4-11 6s-6 4-8 7v-1c-2-1-1-2-2-3 0-1 1-3 1-4 1-1 2-3 3-4v-2h0c2-2 6-4 9-6z" class="e"></path><path d="M211 269c2-1 3-1 4-1 0 1 0 1-1 1l-1 1h-1-1v-1z" class="J"></path><path d="M200 275c-2 1-5 2-6 4h-2 0c2-2 4-4 6-5l2 1z" class="D"></path><path d="M193 273c2-2 6-4 9-6l1 2c-4 2-7 3-10 6v-2h0z" class="H"></path><path d="M216 268h1c0 2-1 4 0 5l-1 1c-2 0-3 1-5 2 0-1-1-1-1-2 2 0 4-1 5-2v-2h-2l1-1c1 0 1 0 1-1h1z" class="a"></path><path d="M198 274c4-2 9-4 13-5v1h1c-4 2-8 3-12 5l-2-1z" class="E"></path><path d="M202 267c4-1 6-3 10-4 1 1 1 1 2 1 1 1 2 1 3 2v2h-1c0-1 0-2-1-2-3-1-10 2-12 3l-1-2z" class="F"></path><path d="M233 284c9-2 18-2 27-2l3 1h0c2 1 3 1 5 1l12 3c1 0 2 1 4 2-1 0-2 0-2-1-1 0-2 1-3 0l-1 1c-1 0-2 0-3-1h-1c-1-1-2 0-4-1h-4l12 3c1 0 2 1 4 1v1h1c-1 1-2 0-2 0l-1 1h0l1 1h-1c-1-1-3 0-4 0-3-1-7-2-10-3-2 0-5-1-7-1-3 0-5-1-8-1h-7c-2-1-5-1-8-1s-7 2-11 2c-4 2-9 4-13 4v-1l1-1h-1c1 0 2-1 2-1 2-3 6-4 9-6h2l7-2 1 1z" class="P"></path><path d="M225 285l7-2 1 1-8 2v-1z" class="H"></path><path d="M252 287c-2-1-5 0-8 0h-6c5-1 11-2 16-1h0 2l-1 1h0c-1-1-1-1-1 0h-2z" class="E"></path><path d="M223 285h2v1l-2 1c-1 1-4 2-6 3-1 1-3 2-4 2h-1c1 0 2-1 2-1 2-3 6-4 9-6z" class="C"></path><path d="M223 285h2v1l-2 1h0v-1-1z" class="E"></path><defs><linearGradient id="AP" x1="259.86" y1="294.217" x2="278.342" y2="284.268" xlink:href="#B"><stop offset="0" stop-color="#3c3836"></stop><stop offset="1" stop-color="#6c6e71"></stop></linearGradient></defs><path fill="url(#AP)" d="M254 286l12 1 12 3c1 0 2 1 4 1v1h1c-1 1-2 0-2 0l-1 1c-2-1-4-2-6-2l-22-4h2c0-1 0-1 1 0h0l1-1h-2 0z"></path><path d="M238 301c2 0 5-1 8-1 8-1 15 0 23 2h1v2c2 0 4 1 6 1l-1 1v1 1c-1-1-2-1-3-1l1 2 2 1h0v1l2 1h-1c-2 0-3-1-5-1 0 1 0 1-1 1h0l1 1v1h0l-1 1c0 1 1 1 1 1l3 1h2l-2 1h1v1 1c-1 0-1 0-2 1h0v1c1 0 3-1 4 0l1 1c1 0 1-1 2-1v2 1c-1 1 0 3 0 5 0 1 0 2 1 3 0 1 1 2 2 4l3 4c2 1 4 2 5 3v1l-1 1h0l1 1 3 1c2 0 3 0 4 1l-1 1v1h-1 1v2c2-1 3-2 4-3l5-1c1 0 1-1 2-1h1c1 0 1 0 1 1 0 0-1 1-1 2h-1c-1 4-6 7-10 9l-5 2-5 2c0 1-2 1-2 2-1 1-3 2-4 2h-2l-4 1 1 2h-1l-1 2c-3 0-4 1-6 2h-2-1c-1 0-4 1-5 1l-5 3v1h1c-1 1-1 2-2 2h-1 0v1h2v1h0c-1 0-1 0-2 1h0v1l-1 1h2v-1c1 1 1 1 2 1h2v1l1-1v1l-1 1h1-1c1 0 2 0 3 1l-1 1h1c0 1-1 1-1 2l-2 1-6 4-1 2h1l1-1c0 1 0 1 1 2h0c0 1 0 1 1 2 0-1 1-1 2-2h2l2-1 1 1 2-2v1c1 0 2 0 3-1 0 1-1 3-1 4v1l3-3c2-1 4-2 6-4l3-1h0l3-1c0-1 1-1 1-2h3 0l1 1-1 1h0c-1 1-2 1-3 2v1h1 0v1c-1 0-2 1-3 2-1 2-4 4-4 6h0c1-2 4-4 5-6 2-2 7-4 10-5 0 1 1 1 2 2h0l-3 2c-1 1-2 3-4 4h-1l-5 6c-1 2-2 4-3 5-3 5-4 12-5 18-1 4-3 7-3 11 0 3 0 5 1 7l1 2-1 2v1c1 0 1 1 1 2 1 2 2 4 2 7h-1c2 4 4 7 6 11h-1c4 6 9 10 15 13l2 1 6 3s2 0 2 1c1 1 1 1 1 2l8 2c1 1 2 1 3 1l1 1 1 1c2 0 3 0 5 1 5 1 10 1 15 1h4 21c-3 1-6 2-9 2h0-7v1h4 0-3 0 3c6 0 11-2 17-2 11-1 23 0 35 2s25 4 37 9c3 2 7 3 10 5 4 2 8 6 11 7l-2 3c-3 4-5 8-9 11l-1 2-7 7c-31 28-72 41-114 39-35-1-67-15-91-41-12-14-23-32-29-49-4-9-6-18-8-27-7-27-11-54-13-82-1-17-2-34-2-51l1-10h1c4-6 8-10 14-14 2-1 4-2 5-3h0c8-5 19-9 28-10l5-1z" class="P"></path><path d="M314 511l3 1h-3v-1z" class="C"></path><path d="M314 512l-2-1c-1 0-2 0-2-1h0c1 0 3 0 4 1v1z" class="E"></path><path d="M231 494l1-1h1v4h-1 0c0-2-1-2-1-3z" class="B"></path><path d="M201 393l1-2h1c0 2-1 3-2 4v-2z" class="D"></path><path d="M196 414v1s0 1 1 1c0 1-1 2-1 3s-1 3-1 5v-1h0v-2c1-1 0-3 1-4v-3z" class="K"></path><path d="M341 522l4 1-1 1h-4v-1l1-1z" class="S"></path><path d="M259 335l2 1h2v1h0-9l1-1h4v-1z" class="I"></path><path d="M324 536c-1 0-4 0-5-1-1 0-1 0-2-1 3 0 6 1 8 2h-1z" class="D"></path><path d="M356 525c2 0 5 1 7 2l-2 1v-1h-2c-1-1-2-1-3-1v-1z" class="E"></path><path d="M250 465c0-1 0-4-1-6h0 0 1v-5c-1-1 0-1 0-1 0 4 1 7 1 11l-1 1z" class="B"></path><path d="M315 536l10 2h-4 0c-2 0-3-1-5-2h0-1z" class="L"></path><path d="M242 517c1 1 3 4 3 5h-1v-1 2 1c-1-1-1-2-2-3v-2-2z" class="g"></path><path d="M332 517l9 1c-2 1-4 1-7 1l-2-1v-1z" class="L"></path><path d="M272 336h2v1h-2s1 1 0 1c-2 0-4 0-5-1h1c1-1 2-1 3-1h1z" class="E"></path><path d="M221 453v2h1v3h0c-1 1-1 2-1 3h-1 0 0l1-8z" class="S"></path><path d="M218 419v2 1l-2 5h-1l3-8z" class="M"></path><path d="M242 337c1-1 3-1 5-1h2v1c-2 0-5 1-7 1v-1z" class="N"></path><path d="M295 550c1 1 2 2 4 2l1 1c1 0 4 1 6 1h1 1v1c1 1 1 1 2 1-2 0-5-1-6-1h0c-1-2-3-2-5-2-1-1-3-2-4-3z" class="D"></path><path d="M345 523l11 2v1c-2-1-5-1-6-1l-6-1 1-1z" class="Z"></path><path d="M214 439h-1c1-1 0-2 1-3v-1-2l1-2v-2h-1v2c0 1 0 1-1 2v2c0 1 0 1-1 1l3-9h1v4c-1 3-2 5-2 8z" class="F"></path><path d="M218 371h1v1h0 1l-6 6c-1 0-1 1-2 1h0c0-1 1-2 2-2 1-1 2-2 2-3v1l-1-1 2-1c0-1 1-1 1-2zm16-19v-1h2v-1h-1c1-1 3-1 4-2h1 1 2v1l-9 3z" class="K"></path><path d="M247 336c4-1 8-1 12-1v1h-4l-1 1h-5v-1h-2z" class="O"></path><path d="M225 476l1-3c1 4 1 7 2 11l-1 2-2-10z" class="R"></path><path d="M251 543c2 2 3 3 5 4 2 2 4 3 6 4l-1 1c-1-1-3-2-4-3-2-1-5-3-7-5l1-1z" class="D"></path><path d="M357 543l10 2h0c-2 1-4 0-6 0-1-1-2-1-3-1h-3v-1h2z" class="d"></path><path d="M283 548l2-1c3 2 6 4 9 5l1 1c-4 0-8-2-12-5z" class="I"></path><path d="M185 329h1v3 3c1-1 2-2 4-3h0c-1 1-3 3-4 5 0 0 0 1-1 2v-1l-1 1 1-10z" class="B"></path><path d="M301 558c4 0 10 2 14 1-1 1-2 1-4 1-3 0-8 0-11-1l1-1z" class="O"></path><path d="M257 333h4 4c3 0 5 0 8 1-2 0-3 0-5 1h-1c-3-1-7-1-10-2z" class="D"></path><path d="M257 560c1 0 4 3 6 4h2c0 1 2 2 2 2 1 0 2 1 3 1 1 1 1 1 2 1-1-1-4-3-5-4-1 0-2 0-2-1h0c1 1 3 2 5 3l5 4h0l1 1c-2 0-4-2-5-3-5-3-10-5-14-8z" class="C"></path><path d="M201 393v2c-2 4-6 8-8 13 0 1-1 1-1 2 0-1 1-3 1-4 3-5 5-9 8-13z" class="B"></path><path d="M202 391c1-1 1-2 2-3l6-6 1 1c-2 3-6 5-8 8h-1z" class="E"></path><path d="M213 463v-6c1-4 1-10 3-13h1c-1 3-2 7-2 10l-1 8c0 1 0 3-1 4v-3z" class="M"></path><path d="M217 367c1 0 1-1 2-1s1-1 2-1c0-1 1-1 1-1l1-1c1 0 2-1 3-1h0c0 1-1 1-1 1-2 1-3 2-4 3v1c-1 1-1 2-2 4h0-1v-1c-1 1-4 5-5 6l4-6c1 0 2-1 2-1v-1c0-1 1-1 1-2-1 0 0 1-1 1h-2z" class="C"></path><path d="M337 535c2 0 2 0 4 1h-1-2v1h5c-6 0-13 0-19-1h1 6c2 0 4 0 6-1z" class="J"></path><path d="M240 368c1 1 4-1 6-1h1c-1 1-2 1-3 1-1 1-1 0-2 1-2 1-4 1-5 2-1 0-1 0-1 1h-1c-1 0-2 0-2 1h-1c-1 0-1 1-3 1 2-2 5-2 7-4 1 0 2-1 4-2z" class="H"></path><path d="M285 553l16 5-1 1-16-4c1-1 1-1 1-2z" class="Q"></path><path d="M309 534l6 2h1 0c2 1 3 2 5 2h-1-3v1c-2 0-4-1-5-1h-1c1-1 1-1 2-1v-1c-1 0-2 0-3-1 0 0-1 0-1-1z" class="G"></path><path d="M228 484l3 10c0 1 1 1 1 3h0 0l-1 1v1h0v-1c-1-4-3-8-4-12l1-2z" class="F"></path><path d="M393 539c5 2 9 4 13 7 3 2 5 4 7 6l-1 1c-6-6-12-10-20-13l1-1z" class="d"></path><path d="M250 440h1v1l-1 1c-1 7-2 13-2 20h0v-6h0c-1-1-1-2-1-2v-2l3-12z" class="O"></path><path d="M273 334c2 0 3 0 5 1v1l-2-1c-1 0-3 0-4 1h-1c-1 0-2 0-3 1h-1-4 0v-1h-2 5l1-1h1c2-1 3-1 5-1z" class="M"></path><path d="M273 334c2 0 3 0 5 1v1l-2-1c-3-1-6 1-10 1l1-1h1c2-1 3-1 5-1zm-53 47c0 1-2 2-3 3-3 3-5 5-8 9l-3 3 1-3h0c3-5 8-10 13-13v1z" class="B"></path><path d="M250 465l1-1c1 5 3 11 5 16 1 2 3 4 4 7-1 0-1 0-1 1-5-7-7-15-9-23z" class="R"></path><path d="M273 512c-2-1-3-3-5-5-1-1-2-3-4-4-1-2-3-5-4-7 2 1 2 2 4 4 1 1 2 3 4 4l1 1h1l1 2-1 1c1 1 2 3 4 4h-1z" class="E"></path><path d="M268 504l1 1h1l1 2-1 1c-1-1-2-2-2-4z" class="K"></path><path d="M301 513c-2-1-4-2-7-3-4-2-9-3-13-6l1-1c7 4 15 7 23 9-1 0-3 0-4 1z" class="I"></path><path d="M271 516l1-1c6 6 12 11 20 14h-1-2 0c1 0 1 1 2 2l-1-1c-3-2-7-4-10-6l-3-3c-2-2-4-4-6-5z" class="R"></path><path d="M242 338c-7 2-15 4-22 7-2 1-3 2-5 3-3 1-6 4-9 5 2-3 5-4 8-5 8-5 18-9 28-11v1z" class="G"></path><path d="M215 324h1l1 1c-3 2-7 3-10 5-5 2-9 5-14 8h0l12-9 10-5z" class="O"></path><path d="M247 367h1l2 1-8 4-3 1c-1 1-2 1-3 1 1-2 4-3 6-4v-1c1-1 1 0 2-1 1 0 2 0 3-1z" class="D"></path><path d="M242 370l2-2v1c0 1-2 2-3 3-1 0-1 0-2 1h0c-1 1-2 1-3 1 1-2 4-3 6-4z" class="F"></path><path d="M230 317c1 0 2-1 3-1v1h0-2v1h1l1-1h1 1 1 0c-2 1-5 1-6 2-4 1-8 2-12 4v1h1l-2 1-1-1h-1v-1c2 0 4-1 5-2h1 2c0-1 1-1 1-1h1l-1-1h-1l1-1c2 0 4-1 6-1z" class="B"></path><path d="M216 324l2-1v1h1l-2 1-1-1z" class="N"></path><path d="M224 318c2 0 4-1 6-1-1 1-3 2-5 2-1 0-1 0-1-1z" class="C"></path><path d="M258 495c1 1 2 4 3 6l5 5c2 3 3 7 6 9l-1 1c-1-2-2-3-4-5-2-3-5-6-7-10-1-1-2-3-2-4v-2z" class="D"></path><path d="M261 341h0l8-1h5v1h3 3l5 1c-2 2-6 0-8 0h-1-1-17c1-1 2 0 3-1h0z" class="H"></path><path d="M225 475c0-3-1-6-1-10 0-3 1-6 1-9 1-2 1-5 2-6-1 4-2 8-2 13 0 2 1 5 1 7v3l-1 3v-1z" class="F"></path><path d="M267 561l6 3c1 1 4 1 5 2s2 1 3 1c-1 1-1 1-2 0h-1l-1-1c-1 0-2-1-4-1-1 0-2-2-4-2v1c4 1 7 4 10 6 2 1 5 2 8 3 0 1 1 1 1 1h1s1 1 2 1c2 1 3 1 5 1l1 1h2 2v1h3 0c2 1 3 1 4 1s2 0 2 1h2 3c1 1 1 0 2 1h2 2c-2 1-4 0-6 0-12-2-25-5-36-10-4-3-9-6-13-9h2-1v-1z" class="C"></path><path d="M237 352l1 1v1s-1 1-1 2c-1 1-1 1-2 1l-1-1c-1 1-2 1-3 2h0l-1-1h0v-1c-1 1-1 1-2 1h-1l-5 2-1 1h-1 0c6-4 11-6 17-8z" class="K"></path><path d="M238 353v1s-1 1-1 2c-1 1-1 1-2 1l-1-1c-1 1-2 1-3 2h0l-1-1h0v-1c3-1 5-1 8-3z" class="D"></path><path d="M274 512c8 6 18 11 27 14v1c-10-3-20-8-28-15h1z" class="I"></path><path d="M258 342c-2 0-5 0-7 1-3 0-7 1-10 2s-6 2-9 4c-3 1-7 4-10 4h0c2-2 5-2 6-3 3-2 5-3 7-4 9-3 17-5 26-5h0c-1 1-2 0-3 1z" class="F"></path><path d="M291 541l20 6 4 1s-1 0-1 1l-1-1h-1-2 0l-2-1h-1l-1-1-1 1v-1h-1-1-1v-1c-1 0-1 1-2 0h-1c-1 0-1 0-1-1h-1c-1 0-2-1-3-1 0 0-1 0-2-1 0 1 1 2 2 2s3 1 5 2h1l2 1c1 0 1 0 2 1h1 1s1 0 1 1c1 0-1-1 1 0h1 1c1 0 4 1 5 2h0l-11-3h-1l-10-4c-1 0-2-1-3-2l1-1z" class="B"></path><path d="M276 342h1v1c-1 0-3 0-4 1h0c-5 1-10 1-15 2-4 1-7 2-11 2h-3c10-4 21-3 32-6z" class="R"></path><path d="M239 373l3-1 1 1c-1 1-3 1-4 2-1 0-2 0-3 1l-3 2h-1c-2 1-4 3-6 5l-7 6-1-1c2-2 5-4 8-7 2-1 4-3 6-5 1 0 2-1 3-2h1c1 0 2 0 3-1z" class="J"></path><path d="M371 540h2 1c1 1 2 1 3 1 4 1 8 2 11 4l1-1c-3-1-5-2-8-3l-6-2h-2 0l1-1c5 2 10 3 14 5 3 2 6 3 9 5 1 1 4 3 5 5l1 1c-3-1-5-4-7-5-8-5-17-7-25-9z" class="Q"></path><path d="M289 542h1c1 1 2 2 3 2l10 4h1c-2 1-3 1-4 1l-1 1c-3-2-7-4-11-5l-1-1v-2c1 1 1 0 2 0z" class="O"></path><path d="M289 542h1c1 1 2 2 3 2-2 0-3 0-5 1l-1-1v-2c1 1 1 0 2 0z" class="M"></path><path d="M232 497h1c1 5 4 10 6 14 1 2 3 5 3 6v2 2c-2-2-3-5-4-8-2-5-5-10-6-16h0z" class="J"></path><path d="M252 367h2v1l-3 1v1 1l-5 3v1l-1 1h-3 0-1c0-1-1-1-2-1 1-1 3-1 4-2l-1-1 8-4 2-1z" class="H"></path><path d="M242 376l4-2v1l-1 1h-3z" class="X"></path><path d="M252 367h2v1l-3 1c-3 1-6 2-8 4l-1-1 8-4 2-1z" class="n"></path><path d="M353 550c7 0 14 0 20 1h1l1 2-36-2c3-1 6 0 9-1 2 0 3 1 5 0z" class="W"></path><path d="M242 331h15c2 1 6 1 8 1v1h-4-4c-7 0-15-1-21 2h0l1-1h0c1-2 4-2 5-3z" class="a"></path><path d="M242 331h15c2 1 6 1 8 1v1h-4-4v-1h-4-7-4v-1z" class="F"></path><path d="M248 349v1h-1c-3 0-7 0-10 2-6 2-11 4-17 8h0c-6 2-9 7-15 10h1c0-1 2-2 3-3 7-7 16-12 25-15l9-3h5z" class="h"></path><path d="M350 525c1 0 4 0 6 1 1 0 2 0 3 1h2v1h-2l1 1-11-1c-2 0-4 0-6-1v-1h2c0-1 0 0-1-1 2 0 4 1 6 0z" class="B"></path><path d="M244 348h3c4 0 7-1 11-2 5-1 10-1 15-2v1l-7 2c-4 0-9 1-13 3v-1c-2 0-3 1-5 1v-1h-5v-1h1z" class="C"></path><path d="M248 349c1-1 2-1 3-1h2v1h0c-2 0-3 1-5 1v-1z" class="E"></path><path d="M248 354h2l1 1-9 3v1c-5 1-11 5-15 8-3 3-6 5-9 8-2 2-5 5-7 8l-1-1c1-1 1-2 2-3 1 0 1-1 2-1l6-6c2-2 4-4 6-5 5-4 12-8 18-11l4-2z" class="N"></path><path d="M260 487c3 5 7 10 12 14 6 5 12 8 18 11h0l-1 1c-6-3-13-6-18-11l-7-7c-2-2-4-5-5-7 0-1 0-1 1-1z" class="M"></path><defs><linearGradient id="AQ" x1="243.857" y1="317.232" x2="217.952" y2="323.073" xlink:href="#B"><stop offset="0" stop-color="#5e5e5e"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#AQ)" d="M236 317h1c2 2 5 0 7 1l1 1h3 2-7c-8 1-16 3-24 5h-1v-1c4-2 8-3 12-4 1-1 4-1 6-2z"></path><path d="M341 518c4 0 8 1 12 2l15 3c3 0 6 1 8 2-2 0-5-1-8-1l-14-3c-6 0-12 0-18-1l-2-1c3 0 5 0 7-1z" class="Z"></path><path d="M291 531c-1-1-1-2-2-2h0 2 1l10 3c2 1 5 1 7 2 0 1 1 1 1 1 1 1 2 1 3 1v1c-1 0-1 0-2 1l-2-1h0c-6-2-13-3-18-6z" class="O"></path><path d="M244 524v-1-2 1h1c4 6 10 12 15 17l9 6h-2c-4-1-9-6-12-9l-2-2h-1l-2-2c-2-2-4-5-6-8z" class="k"></path><path d="M374 551c2 1 5 1 7 1h2 3l3 1 1-1c5 3 11 7 16 11 1 1 2 3 3 4v1c-2-2-4-5-7-6-8-6-18-8-27-9l-1-2z" class="Q"></path><path d="M227 337c1 0 4-2 5-3 3-1 6-3 9-3 5-2 11-1 16-1v1h-15c-1 1-4 1-5 3h0l-1 1h0c-1 2-3 2-5 3-1 0-1-1-1-1h-1l-11 6c-2 1-4 3-5 3h0c4-4 9-6 14-9z" class="X"></path><path d="M237 334h0l-1 1h0c-1 2-3 2-5 3-1 0-1-1-1-1h-1c3-2 5-2 8-3z" class="B"></path><path d="M252 534h1l2 2c3 3 8 8 12 9h2c5 4 11 6 16 8 0 1 0 1-1 2-2-1-5-2-8-3-9-4-17-10-24-18z" class="Z"></path><path d="M217 367l-3 3c-6 4-10 10-15 15-1 3-3 5-4 7l-3 6c-1 1-1 2-1 3l-1-1c4-11 11-20 20-28 2-1 3-3 5-4 2-2 5-4 7-6h1l1-1c1 0 1-1 2-1h2c0 1-1 1-1 2s-3 4-4 4l-2 1v-1c1-1 2-2 4-3 0 0 1 0 1-1h0c-1 0-2 1-3 1l-1 1s-1 0-1 1c-1 0-1 1-2 1s-1 1-2 1z" class="D"></path><path d="M301 526c5 1 9 3 14 3 1 0 2 1 3 1h4l4 1c5 2 10 3 15 3 4 1 9 1 13 1h-1c-1 1-2 1-3 1-3-1-6 0-9-1v1c1 0 2 1 4 1h2l3 1c-3 0-5 0-7-1h-5v-1h2 1c-2-1-2-1-4-1l-36-8v-1z" class="N"></path><defs><linearGradient id="AR" x1="329.168" y1="543.508" x2="330.742" y2="552.69" xlink:href="#B"><stop offset="0" stop-color="#79787c"></stop><stop offset="1" stop-color="#9f9e9d"></stop></linearGradient></defs><path fill="url(#AR)" d="M314 545l39 5c-2 1-3 0-5 0-3 1-6 0-9 1-8 0-16-1-24-3l-4-1c2 0 2 0 3-1v-1z"></path><defs><linearGradient id="AS" x1="217.186" y1="365.961" x2="248.977" y2="359.457" xlink:href="#B"><stop offset="0" stop-color="#171617"></stop><stop offset="1" stop-color="#343335"></stop></linearGradient></defs><path fill="url(#AS)" d="M227 362l6-3c2-1 3-1 4-2 1 0 3-1 4-2 2-1 4-1 6-2l1 1-4 2c-6 3-13 7-18 11-2 1-4 3-6 5h-1 0v-1h0c1-2 1-3 2-4l2-1c1 0 4-3 4-4z"></path><path d="M250 319l9 1h3c2 0 3 1 5 1s4 1 6 1c1 0 3-1 4 0l1 1c-1 0-1 1-2 1 0-1-3 0-4-1h-3 0-2-2-1-3-2-4c-2-1-3-1-5-1h-4-5 1c1-1 1 0 1-1h0 1 3c1-1 3 0 4-1h7-4-8-2l-1-1h7z" class="B"></path><path d="M246 322c7-1 17-2 23 1h-2-2-1-3-2-4c-2-1-3-1-5-1h-4z" class="G"></path><path d="M259 323l1-1c2 0 3 0 5 1h-1-3-2z" class="h"></path><path d="M389 512c15 1 31 5 44 10 3 2 19 9 19 12 0 0-2-1-2-2l-6-3c-3-1-5-2-8-3-3-2-6-3-9-4-9-3-18-5-27-7-4-1-8-1-11-3z" class="C"></path><path d="M280 330c0 1 0 2 1 3 0 1 1 2 2 4l3 4c2 1 4 2 5 3v1l-1 1h0l1 1-2-1c-1-2-2-3-4-4l-5-1h-3-3v-1c1 0 2 0 2-1-1 0-2-1-4-1 1 0 0-1 0-1h2v-1h-2c1-1 3-1 4-1l2 1v-1-1h2v-2c-1 0-1 0-2 1l-1-1c1-1 2-1 2-1 1 0 1-1 1-1z" class="L"></path><path d="M280 338c1 1 3 2 4 3l-3-1c-1-1-1-1-1-2z" class="M"></path><path d="M278 335c1 1 2 1 3 2h0-2c0 1 0 1 1 1-1 1-1 1-2 1h-1v-2c-1 0-2-1-3-1h-2c1-1 3-1 4-1l2 1v-1z" class="J"></path><path d="M281 340l3 1h2c2 1 4 2 5 3v1l-1 1h0l1 1-2-1c-1-2-2-3-4-4l-5-1 1-1z" class="S"></path><path d="M274 336c1 0 2 1 3 1v2h1c1 0 1 0 2-1h0c0 1 0 1 1 2l-1 1h-3-3v-1c1 0 2 0 2-1-1 0-2-1-4-1 1 0 0-1 0-1h2v-1z" class="R"></path><path d="M280 338h0c0 1 0 1 1 2l-1 1h-3v-1h1v-1c1 0 1 0 2-1z" class="a"></path><path d="M289 513l1-1c10 4 21 8 32 9 5 1 10 0 15 1h4l-1 1v1h4l6 1c-2 1-4 0-6 0 1 1 1 0 1 1h-2c-3 0-6 0-9-1h-2c-1 0-1 0-1-1h0c-1 0-1-1-2-1-14-1-27-5-40-10z" class="N"></path><path d="M329 523l11 1h4l6 1c-2 1-4 0-6 0 1 1 1 0 1 1h-2c-3 0-6 0-9-1h-2c-1 0-1 0-1-1h0c-1 0-1-1-2-1z" class="e"></path><path d="M332 525c1-1 2-1 4-1l8 1c1 1 1 0 1 1h-2c-3 0-6 0-9-1h-2z" class="H"></path><defs><linearGradient id="AT" x1="307.619" y1="520.998" x2="325.596" y2="509.697" xlink:href="#B"><stop offset="0" stop-color="#5e5f5d"></stop><stop offset="1" stop-color="#7f7c80"></stop></linearGradient></defs><path fill="url(#AT)" d="M305 512c2 0 3 1 5 0l18 4c1 0 3 0 4 1v1l2 1 2 1c-12-1-24-4-35-7 1-1 3-1 4-1z"></path><path d="M328 516c1 0 3 0 4 1v1c-1 0-5-1-6-1 1-1 2-1 2-1z" class="Z"></path><path d="M238 416h0l-3 6c-1 1-3 3-3 4-1 2-1 3-2 5h0c-1 2-2 3-3 4-1 2-1 4-2 6-1 3-1 5-2 8l-1 6h-1v-2c1-9 4-16 8-24v-3 1c0-1 1-1 1-2v-1-1c-1 1-1 2-3 2h0l1-1 3-4 1-1 2-3h2 1 1z" class="W"></path><path d="M236 416h1l-8 13v-3 1c0-1 1-1 1-2v-1-1c-1 1-1 2-3 2h0l1-1 3-4 1-1 2-3h2z" class="F"></path><path d="M234 416h2c-1 1-1 2-3 2l-1 1 2-3z" class="a"></path><path d="M350 538l-3-1h-2c-2 0-3-1-4-1v-1c3 1 6 0 9 1 1 0 2 0 3-1h1l14 2 6 1-1 1h0 2l6 2 8 3-1 1c-3-2-7-3-11-4-1 0-2 0-3-1h-1-2c-2 1-6 0-8-1l-13-1z" class="L"></path><defs><linearGradient id="AU" x1="324.725" y1="548.729" x2="347.4" y2="532.271" xlink:href="#B"><stop offset="0" stop-color="#787e7c"></stop><stop offset="1" stop-color="#a6a0a3"></stop></linearGradient></defs><path fill="url(#AU)" d="M325 538l32 5h-2v1c-6 0-12-1-18-2l-19-3h-1v-1h3 1 0 4z"></path><path d="M244 328h2 2c-1 1-2 1-3 1-3 0-6 2-10 3-2 0-4 1-6 2-1 1-1 2-3 2h0c-1 1-3 2-5 2h-1c-11 5-20 12-29 20v-1c5-4 10-9 15-13 5-3 10-6 15-8 3-2 6-3 10-5l8-2c2 0 3-1 5-1h0z" class="N"></path><path d="M220 338c1-1 3-2 4-3 2-1 3-1 5-1-1 1-1 2-3 2h0c-1 1-3 2-5 2h-1z" class="h"></path><path d="M226 498l3 8 5 10c2 5 4 10 7 15s6 8 10 12l-1 1c-4-5-8-9-11-14-2-2-5-5-6-8 1-2-2-6-3-8-1-4-3-7-4-11 0-1-1-3-1-5h0 1z" class="E"></path><path d="M253 350c4-2 9-3 13-3 1 0 2 0 3 1l-8 1h0 0l-1 1h2l-2 1c-3 0-5 0-8 1l-2 2h0-2l-1-1c-2 1-4 1-6 2-1 1-3 2-4 2-1 1-2 1-4 2l-6 3c0-1 1-1 1-2 2 0 5-2 7-3 1 0 1 0 2-1 0-1 1-2 1-2v-1l-1-1c3-2 7-2 10-2h1c2 0 3-1 5-1v1z" class="G"></path><path d="M247 353l4-1h1l-2 2h0-2l-1-1z" class="D"></path><path d="M251 352c3-2 6-3 10-3h0l-1 1h2l-2 1c-3 0-5 0-8 1h-1zm2-3v1c-4 2-8 3-13 4h0l1-1h0 0c1-1 2-1 3-1 1-1 2-1 3-2h1c2 0 3-1 5-1z" class="K"></path><path d="M237 352c3-2 7-2 10-2-1 1-2 1-3 2-1 0-2 0-3 1h0 0l-1 1h0c-1 1-2 2-3 2 0-1 1-2 1-2v-1l-1-1zm14 3l11-2v1h1c0 1 0 1 1 1l-9 3c-4 1-9 3-12 6v1c-4 2-9 5-13 8-3 1-6 3-8 5l1-2h1l-1-1c0-1 2-2 3-3 0-1 1-1 2-2h0l1-1h0c1-1 2-1 3-2h1c1-1 2-1 3-2s2-1 4-2h0c1 0 1-1 2-1 0 0 1 0 1-1h1l3-2c1 0 2 0 3-1h0c2-1 3-1 4-2h1 1c1-1 2-1 2-1l1-1h1-1-1-1c-1 1 0 1-1 0h-1v1h-1-1c-1 0-1 0-2 1h-1-1c0 1-1 1-1 1-2 0-3 1-4 1v1h-2 0v-1l9-3z" class="B"></path><path d="M262 354h1c0 1 0 1 1 1l-9 3c-4 1-9 3-12 6-2 1-3 1-5 2-3 1-5 3-8 4h0c3-3 7-4 10-6 4-2 7-4 11-6 2 0 3-1 5-2 2 0 4-1 6-2z" class="X"></path><defs><linearGradient id="AV" x1="216.927" y1="480.55" x2="226.655" y2="476.716" xlink:href="#B"><stop offset="0" stop-color="#4c4d4d"></stop><stop offset="1" stop-color="#6d6c6e"></stop></linearGradient></defs><path fill="url(#AV)" d="M220 461h0 0 1c0-1 0-2 1-3h0v11 6c0 8 2 15 4 23h-1 0c0 2 1 4 1 5 1 4 3 7 4 11 1 2 4 6 3 8-7-14-12-28-14-43 0-6-1-12 1-18z"></path><path d="M299 550l1-1c1 0 2 0 4-1l11 3c13 3 26 4 39 5h0l-14 1c-15 0-28-2-41-7z" class="G"></path><path d="M236 423c3 0 3-2 5-3h2l-5 5c-1 3-3 5-4 7s-2 4-3 5v2c-1 1-1 3-2 4 0 2-1 6-2 7s-1 4-2 6c0 3-1 6-1 9 0 4 1 7 1 10 0-1-1-1-1-2h0v-1c0-3-1-7-1-10h0c0-1 0-1 1-2v-1-1c1-1 1-3 1-3 1-1 0-3 1-4v-1-1h0c1-1 1-2 1-2 0-2 1-4 1-6-1 2-2 3-2 5h0c-1 1-1 2-2 3v1h0c0 1 0 1-1 2v2h0v3c-1 1 0 5-1 7v11-6-11-3l1-6c1-3 1-5 2-8 1-2 1-4 2-6 1-1 2-2 3-4h0 0l1-1c2-1 3-3 5-5-1 0-2 1-3 1h0c1-1 1-2 2-2l1-1z" class="D"></path><path d="M225 441h0v3h0c0-1 0-2 1-2 0-1 1-1 1-2l-4 9c1-3 1-5 2-8z" class="N"></path><path d="M231 430h2c-1 2-2 4-3 5-1 2-2 4-3 5 0 1-1 1-1 2-1 0-1 1-1 2h0v-3h0c1-2 1-4 2-6 1-1 2-2 3-4h0 0l1-1z" class="G"></path><path d="M236 423c3 0 3-2 5-3h2l-5 5c-1 3-3 5-4 7s-2 4-3 5v-2c1-2 1-3 2-4s4-3 4-5l-4 4h-2c2-1 3-3 5-5-1 0-2 1-3 1h0c1-1 1-2 2-2l1-1z" class="W"></path><path d="M257 314l10 1 4 2v-1l3 1h2l-2 1h1v1 1c-1 0-1 0-2 1h0v1c-2 0-4-1-6-1s-3-1-5-1h-3l-9-1h-2-3l-1-1c-2-1-5 1-7-1 2 0 5 0 7-1 0 0 1-1 2-1h2 7 3 1c-1-1-1-1-2-1z" class="I"></path><path d="M263 316c1-1 2-1 4-1l4 2h-1l-7-1z" class="e"></path><path d="M262 320v-1h2s2 0 2 1c2 0 4-1 6 0-2 0-4 0-5 1-2 0-3-1-5-1z" class="G"></path><path d="M274 317h2l-2 1h1v1 1c-1 0-1 0-2 1h0v1c-2 0-4-1-6-1 1-1 3-1 5-1h1 0c0-1-1-1-2-2h2l1-1z" class="n"></path><path d="M257 314l10 1c-2 0-3 0-4 1-6-1-13 0-19 0 0 0 1-1 2-1h2 7 3 1c-1-1-1-1-2-1z" class="U"></path><defs><linearGradient id="AW" x1="212.49" y1="329.747" x2="260.285" y2="323.684" xlink:href="#B"><stop offset="0" stop-color="#636364"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#AW)" d="M241 322h5 4c2 0 3 0 5 1h4 2v1h-5-7c-7 1-14 3-20 5-8 2-15 4-22 8-4 3-8 6-13 8 6-6 14-10 22-14 7-5 16-7 25-9z"></path><path d="M271 507c1 1 2 2 3 2 7 5 13 8 21 11 9 3 18 6 27 7 0 1 0 1-1 1h-1 3v2l3 1h0l-4-1h-4c-1 0-2-1-3-1-5 0-9-2-14-3-9-3-19-8-27-14-2-1-3-3-4-4l1-1z" class="C"></path><path d="M323 530c-6-1-12-2-18-4-3-1-7-2-10-4 2-1 6 2 9 2 5 2 10 3 16 4h3v2z" class="Q"></path><defs><linearGradient id="AX" x1="362.031" y1="544.81" x2="361.853" y2="550.867" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#AX)" d="M337 542c6 1 12 2 18 2h3c1 0 2 0 3 1 2 0 4 1 6 0h0c8 1 16 4 23 7l-1 1-3-1h-3-2l-44-7h-2v-1l1-1h0 1v-1z"></path><path d="M336 543c2 0 3 0 4 1h-4l1 1h0-2v-1l1-1z" class="W"></path><path d="M337 542c6 1 12 2 18 2h3v1c-6 0-12-1-18-1-1-1-2-1-4-1h0 1v-1z" class="U"></path><path d="M236 376c1-1 2-1 3-1s2 0 2 1h1 0 3c-2 2-4 3-6 5h0 1 0l-13 11-1-1h-1l-5 4c0 1-1 1-1 2l-1-1c1-1 0-1 0-2l1 1c0-1 2-2 3-3v-1l1-1 2-2h-1v-1l-1-1h0l-4 4v1c-1 0-1 1-1 2-1 0-2 1-3 2-2 4-4 7-7 10h0v-1l6-9c1-2 3-5 4-7l1 1 7-6c2-2 4-4 6-5h1l3-2z" class="D"></path><path d="M236 376c1-1 2-1 3-1-1 1-2 2-2 3-1 1-2 2-3 2h-1 0l3-3v-1z" class="F"></path><path d="M242 376h0 3c-2 2-4 3-6 5-1 0-1-1-2-1 2-1 5-2 7-4l-6 3h0l-1-1c1-1 3-2 5-2z" class="N"></path><path d="M226 388h0c2-2 4-4 6-5l2 1c-2 2-3 3-5 4 0 1-1 1-2 2 0 0-1 0-2 1l-5 4v-1l6-6z" class="J"></path><path d="M226 388c0 1-1 1-1 2h-1v1l3-3h2c0 1-1 1-2 2 0 0-1 0-2 1l-5 4v-1l6-6z" class="R"></path><path d="M232 383c2-1 4-3 5-3s1 1 2 1h0 1 0l-13 11-1-1h-1c1-1 2-1 2-1 1-1 2-1 2-2 2-1 3-2 5-4l-2-1z" class="E"></path><path d="M232 383c2-1 4-3 5-3s1 1 2 1h0l-5 3-2-1z" class="O"></path><path d="M243 380l1 1h-1v1c1-1 2-1 3-1l1 1c-2 1-5 3-6 5l-7 6c-1 1-2 1-3 2-5 3-8 7-12 12-1 1-2 2-3 4l-1 1h0v-2c-1 0-1 1-1 2h-1l1-1v-1c1-1 0-1 0-1-2 4-3 7-6 11 1-4 3-9 5-12 1-1 1-1 1-2 0 0 1 0 1-1 1-1 1-2 2-2 2-4 6-8 10-11l13-11c1 0 2-1 3-1z" class="U"></path><path d="M231 392l2-1c1-1 2-1 3-1-1 0-2 1-2 2v1c-1 1-2 1-3 2-5 3-8 7-12 12-1 1-2 2-3 4v-1c4-6 9-13 15-18z" class="I"></path><path d="M243 380l1 1h-1v1c1-1 2-1 3-1l1 1c-2 1-5 3-6 5l-7 6v-1c0-1 1-2 2-2-1 0-2 0-3 1l-2 1-1-1h0c-1 2-3 3-5 4l-9 9-1 1c1-1 1-2 2-2 2-4 6-8 10-11l13-11c1 0 2-1 3-1z" class="O"></path><path d="M243 381v1l-3 3c-2 2-2 3-4 5-1 0-2 0-3 1l-2 1-1-1c4-3 8-7 13-10z" class="R"></path><path d="M246 381l1 1c-2 1-5 3-6 5l-7 6v-1c0-1 1-2 2-2 2-2 2-3 4-5l3-3c1-1 2-1 3-1z" class="N"></path><path d="M276 352h3 1v1h3s-1 1-2 1c-3 0-6 0-8 1l-5 1v1h0l-1 1h1l-1 1v1h1l-1 1h3l-2 1h0v1l-1 1c-2 0-6 1-8 2h-2c-1 0-2 1-3 1h-2l-2 1-2-1h-1-1c-2 0-5 2-6 1-2 1-3 2-4 2-2 2-5 2-7 4-3 2-6 4-9 7v-1l2-2c2-2 5-4 8-5 4-3 9-6 13-8v-1c3-3 8-5 12-6l9-3c-1 0-1 0-1-1h1l6-2h6z" class="n"></path><path d="M256 361h2 1l-2 2h-2-1-4c2-1 4-1 6-2z" class="D"></path><path d="M250 363h4 1l-9 4c-2 0-5 2-6 1 2-2 6-4 10-5z" class="C"></path><path d="M258 361h0c2-1 4-2 6-1h3 1l-1 1h3l-2 1h0v1l-1 1c-2 0-6 1-8 2h-2c-1 0-2 1-3 1h-2l-2 1-2-1h-1-1l9-4h2l2-2h-1z" class="L"></path><path d="M252 366h1l-1 1-2 1-2-1 4-1z" class="C"></path><path d="M268 363l-1 1c-2 0-6 1-8 2h-2c-1 0-2 1-3 1h-2l1-1h-1 0c4-2 8-2 13-3h3z" class="H"></path><path d="M258 361h0c2-1 4-2 6-1h3 1l-1 1h3l-2 1h0v1h-3-2c-1 0-1-1-1-1-1 0-3 1-5 1l2-2h-1z" class="E"></path><path d="M267 361h3l-2 1h0v1h-3-2c-1 0-1-1-1-1l5-1z" class="W"></path><path d="M276 352h3 1v1h3s-1 1-2 1c-3 0-6 0-8 1l-5 1v1h0l-1 1h1l-1 1v1h-3c-2-1-4 0-6 1h0-2-1 0c-1 0-1-1-1-1-4 1-8 3-11 5v-1c3-3 8-5 12-6l9-3c-1 0-1 0-1-1h1l6-2h6z" class="D"></path><path d="M276 352h3 1v1h-5 0l1-1z" class="W"></path><path d="M270 352h6l-1 1h0c-3 1-5 2-9 2h-2c-1 0-1 0-1-1h1l6-2z" class="X"></path><path d="M263 354h1c1 0 1 0 2 1h-2c-1 0-1 0-1-1z" class="Z"></path><path d="M254 360c4-2 10-4 14-4v1h0l-1 1h1l-1 1v1h-3c-2-1-4 0-6 1h0-2-1 0c-1 0-1-1-1-1z" class="X"></path><path d="M278 323c1 0 1-1 2-1v2 1c-1 1 0 3 0 5 0 0 0 1-1 1 0 0-1 0-2 1l1 1c1-1 1-1 2-1v2h-2v1c-2-1-3-1-5-1-3-1-5-1-8-1v-1c-2 0-6 0-8-1v-1c-5 0-11-1-16 1-3 0-6 2-9 3-1 1-4 3-5 3l-1-1h0c2 0 2-1 3-2 2-1 4-2 6-2 4-1 7-3 10-3 1 0 2 0 3-1h-2-2c2-1 4-1 6-1 0-1 2-1 3-1h5 2 4-1-2 0l-2-1h-1-3-6v-1h7 5v-1h3 1 2 2 0 3c1 1 4 0 4 1 1 0 1-1 2-1z" class="O"></path><path d="M269 323h0l1 1v1c-1-1-2-1-3-2h2z" class="n"></path><path d="M269 323h3c0 1 0 2-1 2h-1v-1l-1-1z" class="G"></path><path d="M278 323c1 0 1-1 2-1v2c-1 0-2 1-3 0h-1c1 0 1-1 2-1zm-29 1h7c2 1 5 1 7 1 4 1 7 2 10 3 2 1 3 1 5 2v1l-9-3c-7-2-15-1-22 0h-1-2c2-1 4-1 6-1 0-1 2-1 3-1h5 2 4-1-2 0l-2-1h-1-3-6v-1z" class="U"></path><path d="M245 329c8 0 15 0 22 1 2 1 5 1 8 2 1 0 2 0 3 1h-4l-1-1-7-1v1h-1c-2 0-6 0-8-1v-1c-5 0-11-1-16 1-3 0-6 2-9 3-1 1-4 3-5 3l-1-1h0c2 0 2-1 3-2 2-1 4-2 6-2 4-1 7-3 10-3z" class="F"></path><path d="M257 330l9 1v1h-1c-2 0-6 0-8-1v-1z" class="k"></path><path d="M448 543l-1-1c-3-1-6-3-9-5l-10-5c-12-6-26-10-39-13-7-2-14-3-21-3-2 0-9 0-10-1h1c5-1 11 1 16-1h4l9 2c16 2 32 7 46 15 5 3 10 7 15 10h0l-1 2z" class="H"></path><defs><linearGradient id="AY" x1="219.136" y1="422.277" x2="229.419" y2="425.868" xlink:href="#B"><stop offset="0" stop-color="#0f0e0f"></stop><stop offset="1" stop-color="#3a3a3b"></stop></linearGradient></defs><path fill="url(#AY)" d="M236 401c1 0 2-1 2-1 0 1 1 1 0 2h1c-4 5-10 9-12 14-1 1-3 4-3 6h0l1 1 1-1h1v1h0c1 0 1 0 1 1l-1 1h0l-1 1c-1 3-3 5-5 8v1c-2 3-4 6-4 9h-1c-2 3-2 9-3 13v6c-1-1 0-4 0-6h-1v-4h1v2-2l1-1c-1-1-1-1-1-2 1-3 0-6 1-9v-2c0-3 1-5 2-8v-4l2-5v-1c2-4 3-7 5-10h1 1 1l6-7 4-3z"></path><path d="M218 422v3 1l-2 5v-4l2-5z" class="E"></path><path d="M219 434l2 1c-2 3-4 6-4 9h-1l3-10z" class="S"></path><defs><linearGradient id="AZ" x1="216.177" y1="419.102" x2="225.299" y2="416.548" xlink:href="#B"><stop offset="0" stop-color="#31302c"></stop><stop offset="1" stop-color="#535358"></stop></linearGradient></defs><path fill="url(#AZ)" d="M223 411h1 1 1c-2 3-4 7-6 10 0 2-1 4-2 5v-1-3-1c2-4 3-7 5-10z"></path><path d="M226 422h1v1h0c1 0 1 0 1 1l-1 1h0l-1 1c-1 3-3 5-5 8v1l-2-1c0-1 5-9 6-11l1-1z" class="W"></path><path d="M224 426c1-1 1-2 2-2l1 1h0l-1 1h-2z" class="S"></path><path d="M224 426h2c-1 3-3 5-5 8l3-8z" class="G"></path><path d="M285 342c2 1 3 2 4 4l2 1 3 1c2 0 3 0 4 1l-1 1v1h-1 1v2h-2l-1-1c-2 1-4 2-6 2h-3-4c1 0 2-1 2-1h-3v-1h-1-3-6l-6 2h-1-1v-1l-11 2-1-1h0l2-2c3-1 5-1 8-1l2-1h-2l1-1h0 0l8-1c-1-1-2-1-3-1l7-2v-1h0c1-1 3-1 4-1v-1c2 0 6 2 8 0z" class="D"></path><path d="M275 348c5-1 7-1 11 0l2 1h-1-2-1c-2-1-4-1-5 0h-1-2l-1-1z" class="G"></path><path d="M273 345h10-3v1h3c-2 0-4 0-5 1-3 0-7 1-9 1-1-1-2-1-3-1l7-2z" class="I"></path><path d="M285 342c2 1 3 2 4 4h-2c-2 0-3-1-4-1h-10v-1h0c1-1 3-1 4-1v-1c2 0 6 2 8 0z" class="E"></path><path d="M285 342c2 1 3 2 4 4h-2c-3-2-7-2-10-3v-1c2 0 6 2 8 0z" class="X"></path><path d="M260 351c5 0 10-1 15-3l1 1h2 1c1-1 3-1 5 0h1l-2 1 1 1h-3l-2 1h-3-6l-6 2h-1-1v-1l-11 2-1-1h0l2-2c3-1 5-1 8-1z" class="O"></path><path d="M276 349h2-1v1c-1 0-1 0-1 1-1 0-3 0-4-1h2c0-1 1-1 2-1z" class="a"></path><path d="M262 353l9-2c0 1 0 1-1 1l-6 2h-1-1v-1z" class="D"></path><path d="M279 349c1-1 3-1 5 0h1l-2 1 1 1h-3l-2 1h-3-6c1 0 1 0 1-1l1-1c1 1 3 1 4 1 0-1 0-1 1-1v-1h1 1z" class="I"></path><path d="M279 349h0c-1 1-1 1-2 1v-1h1 1z" class="M"></path><path d="M283 350l1 1h-3l-2 1h-3-6c1 0 1 0 1-1l1-1c1 1 3 1 4 1 2 0 5 0 7-1z" class="F"></path><path d="M283 345c1 0 2 1 4 1h2l2 1 3 1c2 0 3 0 4 1l-1 1v1h-1 1v2h-2l-1-1c-2 1-4 2-6 2h-3-4c1 0 2-1 2-1h-3v-1h-1l2-1h3l-1-1 2-1h2 1l-2-1c0-1-2-1-3-2h0-3v-1h3z" class="G"></path><path d="M291 349h4c-1 1-4 1-6 2v-1c1 0 1-1 2-1z" class="J"></path><path d="M285 349h2c1 1 1 1 2 1v1h-5l-1-1 2-1z" class="R"></path><path d="M279 352l2-1h6v1c-1 1-2 1-4 1h-3v-1h-1zm15-4c2 0 3 0 4 1l-1 1c-1 0-1 0-2-1h-4l-1-1c1-1 3 1 4 0z" class="Z"></path><path d="M283 345c1 0 2 1 4 1h2l2 1 3 1c-1 1-3-1-4 0l1 1c-1 0-1 1-2 1s-1 0-2-1h1l-2-1c0-1-2-1-3-2h0-3v-1h3z" class="k"></path><path d="M267 364h3l-1 1h0 6 1v1c-1 1-2 1-3 1h2 1l2 1c1 0 2-1 3 0h1-2l-4 1 1 2h-1l-1 2c-3 0-4 1-6 2h-2-1c-1 0-4 1-5 1h-2s-1 0-1-1h-2c-2 1-4 3-6 4l-4 2c-1 0-2 0-3 1v-1h1l-1-1c-1 0-2 1-3 1h0-1 0c2-2 4-3 6-5l1-1v-1l5-3v-1-1l3-1v-1c1 0 2-1 3-1h2c2-1 6-2 8-2z" class="Z"></path><path d="M261 368l8-1h0c-1 1-3 2-4 2s-3 0-4-1z" class="Q"></path><path d="M269 366h0v1l-8 1h-1s1 0 1-1c3-1 6-1 8-1z" class="h"></path><path d="M268 369l1 1-2 1h0c-3 1-5 2-9 2l10-4z" class="b"></path><path d="M260 367l-1 1-8 3v-1-1l3-1c2-1 4-1 6-1z" class="F"></path><path d="M267 364h3l-1 1h0v1c-2 0-5 0-8 1v-1l-1 1c-2 0-4 0-6 1v-1c1 0 2-1 3-1h2c2-1 6-2 8-2z" class="S"></path><path d="M275 367h1l2 1c1 0 2-1 3 0h1-2l-4 1 1 2h-1l-1 2c-3 0-4 1-6 2h-2-1c-1 0-4 1-5 1h-2s-1 0-1-1l7-2 2-2h0l2-1-1-1c1-1 3-2 5-2h2z" class="o"></path><path d="M275 367h1l2 1h-3v-1z" class="Y"></path><path d="M273 370c1 0 2 0 3-1l1 2h-1-3v-1z" class="j"></path><path d="M268 369c1-1 3-2 5-2h2v1c-2 1-1 0-3 0-1 0-3 1-3 2l-1-1z" class="m"></path><path d="M270 373l3-3v1h3l-1 2c-3 0-4 1-6 2h-2c0-1 3-2 3-2z" class="c"></path><path d="M265 373h1c2 0 3-1 4 0 0 0-3 1-3 2h-1c-1 0-4 1-5 1h-2s-1 0-1-1l7-2z" class="S"></path><path d="M246 375c1-1 3-2 5-2 2-1 5-3 8-3v1h0c0 1-1 1-1 1-1 1-1 1-1 2h0s0 1-1 1c-2 1-4 3-6 4l-4 2c-1 0-2 0-3 1v-1h1l-1-1c-1 0-2 1-3 1h0-1 0c2-2 4-3 6-5l1-1z" class="h"></path><path d="M244 378h0c1 0 2-2 3-2l1 1-2 1h-2z" class="O"></path><path d="M244 378h2l1 1c-1 1-2 1-3 2l-1-1 1-2z" class="G"></path><path d="M248 377c3-3 7-4 11-6h0c0 1-1 1-1 1-1 1-1 1-1 2h0c-4 1-7 2-10 5l-1-1 2-1z" class="W"></path><path d="M257 374s0 1-1 1c-2 1-4 3-6 4l-4 2c-1 0-2 0-3 1v-1h1c1-1 2-1 3-2 3-3 6-4 10-5z" class="h"></path><path d="M238 425l1 1c-1 2-2 3-3 4 1 2 0 3 0 5 0 0-1 1-1 2v2c-2 5-2 10-2 16-1 18 2 34 10 50 4 8 9 16 14 22 3 5 7 9 11 12v1c-9-6-16-14-21-23-14-21-21-48-15-73-1-2 0-3-1-5v-2c1-1 2-3 3-5s3-4 4-7z" class="N"></path><path d="M238 425l1 1c-1 2-2 3-3 4-3 5-3 10-4 14-1-2 0-3-1-5v-2c1-1 2-3 3-5s3-4 4-7z" class="U"></path><defs><linearGradient id="Aa" x1="352.447" y1="543.965" x2="363.066" y2="523.679" xlink:href="#B"><stop offset="0" stop-color="#919492"></stop><stop offset="1" stop-color="#aba7a9"></stop></linearGradient></defs><path fill="url(#Aa)" d="M349 528l11 1h0c11 3 23 5 33 10l-1 1c-8-2-15-4-23-4 0 0 0 1-1 1l-14-2c-4 0-9 0-13-1-5 0-10-1-15-3h0l-3-1v-2c1 0 3 0 5 1h0l15 1 1-1c1-1 4 0 5 0h0v-1z"></path><path d="M349 528l11 1h0c-1 1-1 1-2 1h-9-3-3l1-1c1-1 4 0 5 0h0v-1z" class="S"></path><path d="M328 529l15 1h3-4v1c1 0 3-1 4 0h0-1c-3 0-7 0-10-1h-5c-1 0-1-1-2-1z" class="Q"></path><path d="M326 531c6 2 13 2 20 2l23 3s0 1-1 1l-14-2c-4 0-9 0-13-1-5 0-10-1-15-3h0z" class="e"></path><path d="M250 379c2-1 4-3 6-4h2c0 1 1 1 1 1h2l-5 3v1h1c-1 1-1 2-2 2h-1 0v1h2v1h0c-1 0-1 0-2 1h0v1l-1 1h2v-1c1 1 1 1 2 1h2v1l-6 4v-1h0c-1 0-2 1-3 1-1 1-1 1-2 1h-1l-1 1h-1c1-1 2-2 2-3l-1-1c-1 2-3 3-5 4 0 1-1 2-1 3v1c-1 1-3 2-4 3l-4 3-6 7h-1-1-1c-2 3-3 6-5 10v-2c3-6 6-12 10-17l1-2h0l3-3h-1l-1 1h0l2-3h-1c1-1 2-1 3-2l7-6c1-2 4-4 6-5l-1-1 4-2z" class="X"></path><path d="M246 385l5-4c0 1 1 1 1 2h0l-3 1-2 2h-1v-1z" class="L"></path><path d="M254 383h2v1h0c-1 0-1 0-2 1h0c-2 1-3 1-4 2-1 0-1 0-2 1h-1c2-3 4-4 7-5z" class="Q"></path><path d="M248 388h1c-1 1-3 2-3 2-1 2-3 3-5 4-1 1-3 2-4 3l-1-1c3-2 6-5 9-6l2-2h1z" class="J"></path><path d="M254 385v1l-1 1-1 1-5 5-1 1h-1c1-1 2-2 2-3l-1-1s2-1 3-2h-1c1-1 1-1 2-1 1-1 2-1 4-2z" class="Z"></path><path d="M241 387l1 2c-2 1-5 4-6 6-1 1-2 1-3 2l-5 5 1-2h0l3-3h-1l-1 1h0l2-3h-1c1-1 2-1 3-2l7-6z" class="F"></path><path d="M255 387v-1c1 1 1 1 2 1h2v1l-6 4v-1h0c-1 0-2 1-3 1-1 1-1 1-2 1h-1l5-5 1-1h2z" class="o"></path><path d="M253 387h2l-1 1h-2l1-1z" class="Q"></path><path d="M250 379c2-1 4-3 6-4h2c0 1 1 1 1 1h2l-5 3v1h1c-1 1-1 2-2 2h-1 0l-2 1h0c0-1-1-1-1-2l-5 4c-2 1-3 2-4 4l-1-2c1-2 4-4 6-5l-1-1 4-2z" class="R"></path><path d="M246 381l4-2 1 1-4 2-1-1z" class="X"></path><path d="M251 381c2-1 4-2 5-2v1h1c-1 1-1 2-2 2h-1 0l-2 1h0c0-1-1-1-1-2z" class="S"></path><path d="M250 379c2-1 4-3 6-4h2c0 1 1 1 1 1-3 2-6 3-8 4l-1-1z" class="L"></path><path d="M237 397c1-1 3-2 4-3 0 1-1 2-1 3v1c-1 1-3 2-4 3l-4 3-6 7h-1-1-1c4-6 8-10 13-15l1 1z" class="h"></path><path d="M237 397c1-1 3-2 4-3 0 1-1 2-1 3v1c-1 1-3 2-4 3l-4 3h0c0-2 4-6 5-7z" class="N"></path><path d="M255 433c1 2 0 5 2 6v1c0-1 1-1 1-2-1 9-2 18 1 27 1 4 3 8 4 12v1l1-1v-1c2 4 4 8 7 11s6 5 9 8c3 2 7 5 10 5 0 1 0 1 1 2h1c0 1 1 1 1 1 0 1-1 1-1 1-5-1-9-3-13-5-8-5-16-13-20-22-2-4-3-8-4-12-1-3-2-6-2-9v-1h-1v-2h0l-1 1v-1-1-9l-1-1 1-1h1 0c1-1 1-2 1-3 1-2 1-3 2-4v-1z" class="O"></path><path d="M253 456v-3c1 2 2 6 2 9v2 1h0c-1-3-2-6-2-9z" class="N"></path><path d="M251 452c1-1 1-2 1-3 0-3 0-6 2-8-1 5-1 9-1 14h-1v-2h0l-1 1v-1-1z" class="K"></path><path d="M253 438c1-2 1-3 2-4v6l-1 1c-2 2-2 5-2 8 0 1 0 2-1 3v-9l-1-1 1-1h1 0c1-1 1-2 1-3z" class="H"></path><path d="M251 441h1l-1 2-1-1 1-1z" class="U"></path><path d="M264 477c2 5 4 9 7 12 1 1 1 2 1 2h0c-2-1-4-3-5-5h-1c-1-2-1-3-1-4-1-2-1-3-2-4l1-1z" class="J"></path><path d="M264 477v-1c2 4 4 8 7 11s6 5 9 8h-1c-2 0-5-3-7-4 0 0 0-1-1-2-3-3-5-7-7-12z" class="G"></path><defs><linearGradient id="Ab" x1="330.736" y1="529.992" x2="263.936" y2="497.551" xlink:href="#B"><stop offset="0" stop-color="#111011"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#Ab)" d="M264 495l7 7c5 5 12 8 18 11 13 5 26 9 40 10 1 0 1 1 2 1h0c0 1 0 1 1 1h2c3 1 6 1 9 1v1c2 1 4 1 6 1v1h0c-1 0-4-1-5 0l-1 1-15-1h0c-2-1-4-1-5-1h-3 1c1 0 1 0 1-1-9-1-18-4-27-7-8-3-14-6-21-11-1 0-2-1-3-2l-1-2c-1 0-1 0-1-1 0 0 0-1-1-2-1-3-4-3-4-7z"></path><path d="M274 509h1c3 2 7 5 11 6 2 2 5 2 8 3 10 4 21 8 32 8 3 0 6 1 8-1 3 1 6 1 9 1v1c2 1 4 1 6 1v1h0c-1 0-4-1-5 0l-1 1-15-1h0c-2-1-4-1-5-1h-3 1c1 0 1 0 1-1-9-1-18-4-27-7-8-3-14-6-21-11z" class="G"></path><path d="M339 527h4c2 1 4 1 6 1v1h0c-1 0-4-1-5 0l-7-1v-1h2z" class="L"></path><path d="M334 525c3 1 6 1 9 1v1h-4l-13-1c3 0 6 1 8-1z" class="e"></path><path d="M306 349c1 0 1-1 2-1h1c1 0 1 0 1 1 0 0-1 1-1 2h-1c-1 4-6 7-10 9l-5 2-5 2c0 1-2 1-2 2-1 1-3 2-4 2h-1c-1-1-2 0-3 0l-2-1h-1-2c1 0 2 0 3-1v-1h-1-6 0l1-1h-3l1-1v-1h0l2-1h-3l1-1h-1v-1l1-1h-1l1-1h0v-1l5-1c2-1 5-1 8-1h4 3c2 0 4-1 6-2l1 1h2c2-1 3-2 4-3l5-1z" class="Q"></path><path d="M285 361c-1 0-1 0-1 1h-5v-1h6z" class="G"></path><path d="M285 361c2-1 5-1 7-2l1 1c-3 1-7 2-9 2 0-1 0-1 1-1z" class="L"></path><path d="M268 362l11-1v1l-4 1h1v1c-1 0-1 0-1 1h-6 0l1-1h-3l1-1v-1z" class="I"></path><path d="M270 364l5-1h1v1c-1 0-1 0-1 1h-6 0l1-1z" class="d"></path><path d="M279 362h5-1c1 2 3 0 4 2h1c0 1-2 1-2 2-1 1-3 2-4 2h-1c-1-1-2 0-3 0l-2-1h-1-2c1 0 2 0 3-1v-1h-1c0-1 0-1 1-1v-1h-1l4-1z" class="Q"></path><path d="M287 364h1c0 1-2 1-2 2-1 1-3 2-4 2h-1c-1-1-2 0-3 0l-2-1 11-3z" class="T"></path><path d="M296 355c4-2 10-3 13-7 1 0 1 0 1 1 0 0-1 1-1 2h-1c-1 4-6 7-10 9l-5 2-5 2h-1c-1-2-3 0-4-2h1c2 0 6-1 9-2l-1-1c1 0 3-1 4-2-1 0-1 0-1 1l-1-1 2-2z" class="b"></path><defs><linearGradient id="Ac" x1="301.359" y1="357.094" x2="300.709" y2="350.994" xlink:href="#B"><stop offset="0" stop-color="#959395"></stop><stop offset="1" stop-color="#b1b2b0"></stop></linearGradient></defs><path fill="url(#Ac)" d="M296 355c4-2 10-3 13-7 1 0 1 0 1 1 0 0-1 1-1 2h-1-1v1c-1 2-2 1-4 2l-2 2c-3 2-6 3-8 4l-1-1c1 0 3-1 4-2-1 0-1 0-1 1l-1-1 2-2z"></path><path d="M306 349c1 0 1-1 2-1h1c-3 4-9 5-13 7l-2 2h-1c-2 1-3 2-5 2h-5-1l-12 2h-3l1-1h-1v-1l1-1h-1l1-1h0v-1l5-1c2-1 5-1 8-1h4 3c2 0 4-1 6-2l1 1h2c2-1 3-2 4-3l5-1z" class="Z"></path><path d="M277 357h4 3v1c-2 0-6 0-7-1z" class="L"></path><path d="M293 355h3l-2 2h-1c-1 0-3 1-4 0v-1l4-1z" class="W"></path><path d="M267 359h15 0l-12 2h-3l1-1h-1v-1z" class="M"></path><path d="M276 356c4 0 7-1 10-1h6c1-1 1 0 1 0l-4 1c-2 0-5 1-8 1h-4c-3 1-6 0-9 1h-1l1-1h0v-1l5-1c1 0 2 0 3 1h0z" class="F"></path><path d="M268 356l5-1c1 0 2 0 3 1h0c-3 0-6 1-8 1h0v-1z" class="W"></path><path d="M306 349c1 0 1-1 2-1h1c-3 4-9 5-13 7h-3s0-1-1 0h-6c-3 0-6 1-10 1h0c-1-1-2-1-3-1 2-1 5-1 8-1h4 3c2 0 4-1 6-2l1 1h2c2-1 3-2 4-3l5-1z" class="L"></path><path d="M259 388l1-1v1l-1 1h1-1c1 0 2 0 3 1l-1 1h1c0 1-1 1-1 2l-2 1-6 4-1 2h1c-1 2-1 2-1 3-2 1-4 2-5 4h1l-1 1c0 1-1 1-1 3-3 4-7 8-10 12l-1 1c-1 0-1 1-2 2h0c1 0 2-1 3-1-2 2-3 4-5 5l-1 1h0c1-2 1-3 2-5 0-1 2-3 3-4l3-6h0-1-1-2l-2 3-1 1-3 4c0-1 0-1-1-1h0v-1h-1l-1 1-1-1h0c0-2 2-5 3-6 2-5 8-9 12-14h-1c1-1 0-1 0-2 0 0-1 1-2 1 1-1 3-2 4-3v-1c0-1 1-2 1-3 2-1 4-2 5-4l1 1c0 1-1 2-2 3h1l1-1h1c1 0 1 0 2-1 1 0 2-1 3-1h0v1l6-4z" class="d"></path><path d="M246 400h0l-3 3h0 0v-1l3-2z" class="b"></path><path d="M259 388l1-1v1l-1 1h1-1c1 0 2 0 3 1l-1 1h1c0 1-1 1-1 2l-2 1v-1l-3 2-1-1 3-2v-1c-1 0-2 1-3 2-1 0-1 1-3 1h-1l-1 1c-1 0-1 0-1 1-1 0-1 0 0 0-1 0-2 1-3 1v-1c1 0 2-1 3-2 1 0 3-1 4-2l6-4z" class="Y"></path><path d="M261 391h1c0 1-1 1-1 2l-2 1v-1c0-1 1-2 2-2z" class="d"></path><path d="M234 410h0c0 2 0 2-1 3l-1 1h0 1v2l2-2c2-2 2-3 5-4l-6 6-2 3-1 1-3 4c0-1 0-1-1-1h0v-1h-1c2-4 5-9 8-12z" class="Q"></path><path d="M227 422c1-2 2-3 3-4s1-2 2-3l-2 5h1l-3 4c0-1 0-1-1-1h0v-1z" class="L"></path><path d="M259 393v1l-6 4-1 2h1c-1 2-1 2-1 3-2 1-4 2-5 4l-2 1-1-1 2-2h1c0-1 1-1 2-2l-1-1c0 1-2 2-2 2l-1-1c2-1 3-3 5-4 0-2 1-3 2-4 1 0 2 0 3-1h0l1 1 3-2z" class="c"></path><path d="M259 393v1l-6 4-2 1-1-1c1 0 2-1 3-2 1 0 2-1 3-1l3-2z" class="o"></path><path d="M240 410l6-5h0l-2 2 1 1 2-1h1l-1 1c0 1-1 1-1 3-3 4-7 8-10 12l-1 1c-1 0-1 1-2 2h0c1 0 2-1 3-1-2 2-3 4-5 5l-1 1h0c1-2 1-3 2-5 0-1 2-3 3-4l3-6h0-1-1-2l6-6z" class="a"></path><path d="M240 410l6-5h0l-2 2 1 1c-2 3-4 6-7 8h0-1-1-2l6-6z" class="X"></path><path d="M240 410l6-5h0l-2 2c-3 2-5 6-6 9h-1-1-2l6-6z" class="I"></path><path d="M246 390l1 1c0 1-1 2-2 3h1l1-1h1c1 0 1 0 2-1 1 0 2-1 3-1h0v1c-1 1-3 2-4 2-1 1-2 2-3 2v1c1 0 2-1 3-1v1c-1 0-2 1-3 2v1l-3 2c-1 0-3 3-4 3l-5 5c-3 3-6 8-8 12l-1 1-1-1h0c0-2 2-5 3-6 2-5 8-9 12-14h-1c1-1 0-1 0-2 0 0-1 1-2 1 1-1 3-2 4-3v-1c0-1 1-2 1-3 2-1 4-2 5-4z" class="W"></path><path d="M245 394h1c-2 2-4 3-5 5-1 1-2 2-2 3h-1c1-1 0-1 0-2 0 0-1 1-2 1 1-1 3-2 4-3 2-1 4-3 5-4z" class="M"></path><path d="M246 390l1 1c0 1-1 2-2 3s-3 3-5 4v-1c0-1 1-2 1-3 2-1 4-2 5-4z" class="n"></path><path d="M227 416c2 0 3-3 5-5l6-7 1 1-5 5c-3 3-6 8-8 12l-1 1-1-1h0c0-2 2-5 3-6z" class="E"></path><path d="M290 530l1 1c5 3 12 4 18 6h0l2 1h1c1 0 3 1 5 1h1l19 3v1h-1 0l-1 1v1h2l44 7c-2 0-5 0-7-1h-1c-6-1-13-1-20-1l-39-5v1c-1 1-1 1-3 1l-20-6c-4-1-7-3-11-6v-2h0c-1-1 0-1 0-1h1 2l3 1h2 0v-1h0 2 0l-1-1 1-1z" class="K"></path><path d="M288 533c4 1 7 3 10 4 4 0 7 1 11 2v-2l2 1h1c1 0 3 1 5 1h1l-2 1h0 0v2c-10-2-20-4-28-9h0z" class="I"></path><path d="M309 537l2 1h1l-1 1h-2v-2z" class="P"></path><path d="M312 538c1 0 3 1 5 1h1l-2 1h0 0c-2 0-3 0-5-1l1-1z" class="e"></path><path d="M290 530l1 1c5 3 12 4 18 6h0v2c-4-1-7-2-11-2-3-1-6-3-10-4v-1h0 2 0l-1-1 1-1z" class="K"></path><path d="M318 539l19 3v1h-1 0l-1 1v1l-3-1c-2 0-5-1-7-1l-9-1v-2h0 0l2-1z" class="p"></path><path d="M316 540c3 1 5 1 7 2h2v1l-9-1v-2h0z" class="G"></path><path d="M325 542l9 1h2 0l-1 1v1l-3-1c-2 0-5-1-7-1v-1z" class="S"></path><path d="M334 543h2 0l-1 1v1l-3-1 1-1h1z" class="n"></path><defs><linearGradient id="Ad" x1="290.403" y1="527.819" x2="303.919" y2="552.111" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#Ad)" d="M281 532h2l3 1v1h-1-1c1 0 1 1 2 1 1 2 4 2 6 3 7 3 14 6 22 7v1c-1 1-1 1-3 1l-20-6c-4-1-7-3-11-6v-2h0c-1-1 0-1 0-1h1z"></path><path d="M281 532h2l3 1v1h-1-1c1 0 1 1 2 1-2 0-4-2-5-3z" class="e"></path><path d="M290 500c2 1 4 2 6 2h2 0 3c1 1 0 0 1 0s1 1 1 1c2 0 3 0 4 1 0 0 1 0 1-1l12 3c6 1 13 1 20 1 3 0 7 0 10-1 6 0 11-2 17-2 11-1 23 0 35 2s25 4 37 9c3 2 7 3 10 5 4 2 8 6 11 7l-2 3c-2-1-4-3-6-4-4-2-7-4-11-6-7-3-14-5-21-7-6-2-11-2-17-3-13-2-26-3-39-2-14 1-28 3-42 2-6-1-13-1-19-2l-11-4s1 0 1-1c0 0-1 0-1-1h-1c-1-1-1-1-1-2z" class="J"></path><path d="M305 505l5 1h3-1l-1 1h1s1 0 1 1c-3-1-6-2-8-2v-1zm-13-1s1 0 1-1l3 1 1 1c2 1 6 2 8 2l-2 1-11-4z" class="N"></path><path d="M290 500c2 1 4 2 6 2h2 0 3c1 1 0 0 1 0s1 1 1 1h0c0 1 1 1 2 2v1c-2 0-3-1-5-1 0-1 0 0-1-1h0-1 0-2l-3-1s-1 0-1-1h-1c-1-1-1-1-1-2z" class="I"></path><path d="M265 400c1 0 2 0 3-1 0 1-1 3-1 4v1c-2 1-4 2-6 4l-3 3-6 7c-1 1-2 2-3 4v-1h-2c-9 12-14 30-12 45l3 15c1 4 1 7 2 10 3 8 7 16 12 23 0 2 1 2 2 3 1 2 2 2 2 3v1c1 0 2 2 2 3 1 1 2 2 2 3 1 2 4 4 5 5l2 2h0c1 0 1 1 2 2 1 0 3 2 4 2 2 2 3 4 5 5 2 2 5 2 7 4l-2 1c-1 0-3-2-5-2-2-1-3-2-5-3s-3-2-5-3v-1c-4-3-8-7-11-12-5-6-10-14-14-22-8-16-11-32-10-50 0-6 0-11 2-16v-2c0-1 1-2 1-2 0-2 1-3 0-5 1-1 2-2 3-4l-1-1 5-5h-2c-2 1-2 3-5 3 3-4 7-8 10-12 0-2 1-2 1-3l1-1h-1c1-2 3-3 5-4 0-1 0-1 1-3l1-1c0 1 0 1 1 2h0c0 1 0 1 1 2 0-1 1-1 2-2h2l2-1 1 1 2-2v1z" class="B"></path><path d="M269 536c1 0 3 2 4 2 2 2 3 4 5 5 2 2 5 2 7 4l-2 1c-1 0-3-2-5-2-2-1-3-2-5-3s-3-2-5-3v-1l10 5c-3-3-7-5-9-8h0z" class="a"></path><path d="M265 400c1 0 2 0 3-1 0 1-1 3-1 4v1c-2 1-4 2-6 4l-3 3-6 7c-1 1-2 2-3 4v-1h-2l5-8-1 1-5 5c-1 1-1 2-2 3-1 2-3 3-4 5-2 3-4 8-5 12v-2c0-1 1-2 1-2 0-2 1-3 0-5 1-1 2-2 3-4l-1-1 5-5h-2c-2 1-2 3-5 3 3-4 7-8 10-12 0-2 1-2 1-3l1-1h-1c1-2 3-3 5-4 0-1 0-1 1-3l1-1c0 1 0 1 1 2h0c0 1 0 1 1 2 0-1 1-1 2-2h2l2-1 1 1 2-2v1z" class="L"></path><path d="M260 401l2-1 1 1-12 9v-2c1-1 2-1 2-3 0 0 2-2 3-2 0-1 1-1 2-2h2zm5-1c1 0 2 0 3-1 0 1-1 3-1 4v1c-2 1-4 2-6 4l-3 3h-1c0-1 1-1 1-2l-1 1-1-1c1 0 2-1 2-2 1 0 1-1 2-2 1-2 4-3 5-5z" class="b"></path><path d="M254 411c1 0 1-1 2-2l1 1 1-1c0 1-1 1-1 2h1l-6 7c-1 1-2 2-3 4v-1h-2l5-8h0c0-1 1-1 2-2z" class="d"></path><path d="M254 411c-2 3-4 6-5 9v1h-2l5-8h0c0-1 1-1 2-2zm0-12c0 1 0 1 1 2h0c0 1 0 1 1 2-1 0-3 2-3 2 0 2-1 2-2 3v2c-1 1-2 2-3 4l-1 1h0c-1 2-3 3-4 4v1h-2c-2 1-2 3-5 3 3-4 7-8 10-12 0-2 1-2 1-3l1-1h-1c1-2 3-3 5-4 0-1 0-1 1-3l1-1z" class="Q"></path><path d="M254 399c0 1 0 1 1 2h0c0 1 0 1 1 2-1 0-3 2-3 2-1 1-2 1-3 2v1c-1 1-3 2-4 3 0-2 1-2 1-3l1-1h-1c1-2 3-3 5-4 0-1 0-1 1-3l1-1z" class="N"></path><path d="M254 399c0 1 0 1 1 2h0l-3 2c0-1 0-1 1-3l1-1z" class="b"></path><path d="M238 301c2 0 5-1 8-1 8-1 15 0 23 2h1v2c2 0 4 1 6 1l-1 1v1 1c-1-1-2-1-3-1l1 2 2 1h0v1l2 1h-1c-2 0-3-1-5-1 0 1 0 1-1 1h0l1 1v1h0l-1 1c0 1 1 1 1 1v1l-4-2-10-1c-16-2-33-1-47 6-7 4-15 7-20 12h0c-2 1-3 2-4 3v-3-3c4-6 8-10 14-14 2-1 4-2 5-3h0c8-5 19-9 28-10l5-1z" class="e"></path><path d="M260 309l5 1c-1 0-2 0-3 1h-3c0-1 0-1 1-2z" class="k"></path><path d="M254 309h6c-1 1-1 1-1 2-1 0-3-1-5-1v-1h0z" class="g"></path><path d="M265 310l6 1c0 1 0 1-1 1h0c-3 0-5 0-8-1 1-1 2-1 3-1z" class="G"></path><path d="M264 307c3 0 6 1 9 2l2 1h0v1l-14-3v-1h3zm-55 12s0 1 1 1c-7 4-15 7-20 12h0c2-3 6-5 9-7 3-3 6-5 10-6z" class="N"></path><path d="M240 310c-3 0-6 1-9 1v-1c3-1 6-1 8-1 6-1 10-3 15 0h0v1c-2 0-4-1-6-1s-5 0-8 1z" class="R"></path><path d="M240 310c3-1 6-1 8-1s4 1 6 1 4 1 5 1h3c3 1 5 1 8 1l1 1v1c-2-1-5-2-7-2-7-1-13-1-20-1v-1h-4z" class="U"></path><defs><linearGradient id="Ae" x1="248.144" y1="301.461" x2="241.175" y2="308.438" xlink:href="#B"><stop offset="0" stop-color="#5d5c5b"></stop><stop offset="1" stop-color="#747375"></stop></linearGradient></defs><path fill="url(#Ae)" d="M250 304h5-3l1 1h1-1l1 1-2 1c-2-1-5-1-7-1l-7 1v-2l12-1z"></path><path d="M245 306c0-1 5 0 6 0 1-1 2-1 2-1l1 1-2 1c-2-1-5-1-7-1z" class="h"></path><path d="M233 302l5-1v2c-10 1-18 3-26 6-2 1-5 2-7 3h0c8-5 19-9 28-10z" class="O"></path><path d="M238 307c-9 1-18 3-27 7 1-1 2-2 3-2 7-4 16-6 24-7v2z" class="G"></path><path d="M254 305c2-1 5 0 7 0 4 1 8 1 11 2l1 2c-3-1-6-2-9-2h-3v1l-9-1 2-1-1-1h1z" class="C"></path><path d="M254 306c3-1 6 0 10 1h-3v1l-9-1 2-1z" class="k"></path><path d="M268 303c0 1 1 1 2 1 2 0 4 1 6 1l-1 1v1 1c-1-1-2-1-3-1-3-1-7-1-11-2-2 0-5-1-7 0h-1l-1-1h3c4-1 8-1 12 0l1-1z" class="G"></path><path d="M268 303c0 1 1 1 2 1 2 0 4 1 6 1l-1 1c-3-1-6-1-8-2l1-1z" class="H"></path><path d="M238 301c2 0 5-1 8-1 8-1 15 0 23 2h1v2c-1 0-2 0-2-1l-1 1c-4-1-8-1-12 0h-5v-1h6c-1 0-2 0-3-1h-6c-3 0-6 1-9 1v-2z" class="I"></path><path d="M247 302c7-1 14 0 21 1l-1 1c-4-1-8-1-12 0h-5v-1h6c-1 0-2 0-3-1h-6z" class="K"></path><path d="M244 311c7 0 13 0 20 1 2 0 5 1 7 2h0l-1 1c0 1 1 1 1 1v1l-4-2-10-1c-16-2-33-1-47 6-1 0-1-1-1-1l15-6c6-1 13-2 20-2z" class="X"></path><path d="M253 418c1 1 1 1 2 1-1 2-3 3-4 5 0 2-1 4-2 6h0 1l1-1h0l-1 1h0l1 1 1 2-2 7-3 12v2s0 1 1 2h0v6h0c0 3 0 6 1 9 1 8 3 17 9 24v2c0 1 1 3 2 4 2 4 5 7 7 10 2 2 3 3 4 5 2 1 4 3 6 5l3 3c3 2 7 4 10 6l-1 1 1 1h0-2 0v1h0-2l-3-1h-2-1s-1 0 0 1h0v2c4 3 7 5 11 6l-1 1h-1c-1 0-1 1-2 0v2 2c2 2 5 3 8 4 1 1 3 2 4 3 2 0 4 0 5 2l-9-2-1-1c-3-1-6-3-9-5-2-2-5-2-7-4-2-1-3-3-5-5-1 0-3-2-4-2-1-1-1-2-2-2h0l-2-2c-1-1-4-3-5-5 0-1-1-2-2-3 0-1-1-3-2-3v-1c0-1-1-1-2-3-1-1-2-1-2-3-5-7-9-15-12-23-1-3-1-6-2-10l-3-15c-2-15 3-33 12-45h2v1c1-2 2-3 3-4l1 1v-1z" class="C"></path><path d="M247 496c1 3 3 6 4 9l1 1-1 1c-2-3-4-7-5-10l1-1z" class="I"></path><path d="M235 466v-2h1c0 6 1 12 2 17h0l-3-15z" class="S"></path><path d="M238 481c4 9 6 19 11 27 1 1 3 4 3 6-5-7-9-15-12-23-1-3-1-6-2-10h0z" class="G"></path><path d="M239 453c1-6 3-12 5-18 2 0 2 0 3 1l-3 6c0 1 0 2-1 3 0 2-1 2-1 4l-1 5-2-1z" class="L"></path><path d="M253 418c1 1 1 1 2 1-1 2-3 3-4 5 0 2-1 4-2 6h0 1l1-1h0l-1 1h0c-1 1-1 1-1 2-1 0-1 1-2 1v3c-1-1-1-1-3-1 2-5 5-12 9-16v-1z" class="Q"></path><path d="M239 453l2 1c-1 2-1 4-1 5v2l1 11h0c0 7 2 12 4 18 1 2 1 4 2 6l-1 1-6-18c-1-6-2-14-1-20v-6z" class="W"></path><path d="M239 453l2 1c-1 2-1 4-1 5v2c-1 1 0 3 0 4h-1v-6-6z" class="Q"></path><path d="M294 552h0c-1-2-4-3-6-4-6-3-10-7-15-11 0-1-1-1-2-2v-1l7 6h0l3 1h2l4 1v2 2c2 2 5 3 8 4 1 1 3 2 4 3 2 0 4 0 5 2l-9-2-1-1z" class="R"></path><path d="M278 540l3 1h2l4 1v2 2l-9-6h0z" class="e"></path><path d="M281 541h2l4 1v2c-2 0-4-1-6-3z" class="H"></path><path d="M241 472h0c1 1 0 3 1 4v3h1 1 0v-2h1v5l3 6c1 3 3 6 3 9 0 1 1 3 1 4l-1 1 1 2-1 1c-1-3-3-6-4-9-1-2-1-4-2-6-2-6-4-11-4-18z" class="P"></path><path d="M248 488c1 3 3 6 3 9 0 1 1 3 1 4l-1 1c-1-4-3-7-4-10 1-1 1-3 1-4z" class="J"></path><path d="M244 479v-2h1v5l3 6c0 1 0 3-1 4l-3-8c-1-2-1-3-2-5h1 1 0z" class="I"></path><defs><linearGradient id="Af" x1="234.365" y1="442.108" x2="248.703" y2="441.712" xlink:href="#B"><stop offset="0" stop-color="#848587"></stop><stop offset="1" stop-color="#a39f9f"></stop></linearGradient></defs><path fill="url(#Af)" d="M247 421h2v1c-10 12-12 27-13 42h-1v2c-2-15 3-33 12-45z"></path><path d="M252 511c2 0 3 1 4 2s1 2 2 2h0c1 1 4 5 4 5 1 1 3 2 3 3 2 3 4 5 7 7 2 2 4 3 6 5v-1l2 1c4 3 7 5 11 6l-1 1h-1c-1 0-1 1-2 0l-4-1h-2l-3-1 1-1v-2c-3-1-5-4-7-5-4-1-6-4-9-6 0-1-1-1-1-2-2-4-7-7-9-11l-1-2z" class="h"></path><path d="M279 537c1 1 2 1 3 2h-3v-2z" class="B"></path><path d="M279 539h3l1 2h-2l-3-1 1-1z" class="M"></path><path d="M278 535v-1l2 1c4 3 7 5 11 6l-1 1h-1c-4-2-7-4-11-7z" class="U"></path><path d="M252 511c2 0 3 1 4 2s1 2 2 2h0c1 1 4 5 4 5 1 1 3 2 3 3v1c1 2 4 4 6 6l-1 1c-2-2-5-6-7-5 0-1-1-1-1-2-2-4-7-7-9-11l-1-2z" class="H"></path><path d="M247 436v-3c1 0 1-1 2-1 0-1 0-1 1-2l1 1 1 2-2 7-3 12v-1l-1-2v1 1c0 3-1 5-1 7-1 3-1 7-1 9 0 4 1 7 1 10h-1v2h0-1-1v-3c-1-1 0-3-1-4h0 0l-1-11v-2c0-1 0-3 1-5l1-5c0-2 1-2 1-4 1-1 1-2 1-3l3-6z" class="B"></path><path d="M246 451h-1v-2h1v1 1z" class="C"></path><path d="M244 479v-8c0-3-2-9 0-12v2 6h0c0 4 1 7 1 10h-1v2z" class="F"></path><path d="M247 436v-3c1 0 1-1 2-1 0-1 0-1 1-2l1 1c-4 5-6 13-7 18h-1-1c0-2 1-2 1-4 1-1 1-2 1-3l3-6z" class="Z"></path><path d="M242 449h1 1c-1 5-2 11-2 16s1 9 1 14h-1v-3c-1-1 0-3-1-4h0 0l-1-11v-2c0-1 0-3 1-5l1-5z" class="O"></path><path d="M240 459h1 0l1 17c-1-1 0-3-1-4h0 0l-1-11v-2z" class="F"></path><path d="M251 497c2 2 2 3 3 5 2 3 4 7 7 9 0 1 0 1 1 2 0-1 0-1 1 0v-1c3 3 6 6 9 8 2 1 4 4 6 5 3 2 5 3 8 5l3 1 1 1h0-2 0v1h0-2l-3-1h-2-1s-1 0 0 1h0v2l-2-1v1c-2-2-4-3-6-5-3-2-5-4-7-7 0-1-2-2-3-3 0 0-3-4-4-5h0c-1 0-1-1-2-2s-2-2-4-2l-1-4 1-1-1-1 1-1-1-2 1-1c0-1-1-3-1-4z" class="C"></path><path d="M286 530l3 1 1 1h0-2 0v1h0-2l-3-1h2 0l-2-1v-1-1l2 1h1z" class="D"></path><path d="M283 529l2 1 2 2h-2l-2-1v-1-1z" class="K"></path><path d="M252 504l2 2c0 1 0 2 1 3 0 0 0 1 1 1 0 1 1 2 2 3v1 1h0c-1 0-1-1-2-2s-2-2-4-2l-1-4 1-1-1-1 1-1z" class="J"></path><path d="M252 504l2 2c0 1 0 2 1 3 0 0 0 1 1 1 0 1 1 2 2 3v1l-6-8-1-1 1-1z" class="K"></path><path d="M265 521l1-1c3 3 5 6 9 8 1 1 4 3 5 4 0 0-1 0 0 1h0v2l-2-1-13-13z" class="M"></path><path d="M262 520c2 0 2 0 3 1l13 13v1c-2-2-4-3-6-5-3-2-5-4-7-7 0-1-2-2-3-3z" class="B"></path><path d="M251 497c2 2 2 3 3 5 2 3 4 7 7 9 0 1 0 1 1 2 0 2 2 4 4 5-3-1-5-5-7-7-1-2-3-4-5-5l-2-2-1-2 1-1c0-1-1-3-1-4z" class="F"></path><path d="M263 513v-1c3 3 6 6 9 8 2 1 4 4 6 5 3 2 5 3 8 5h-1l-2-1c-4-2-8-4-11-6-4-3-7-7-9-10z" class="M"></path><path d="M246 450v-1l1 2v1 2s0 1 1 2h0v6h0c0 3 0 6 1 9 1 8 3 17 9 24v2c0 1 1 3 2 4 2 4 5 7 7 10 2 2 3 3 4 5 2 1 4 3 6 5l3 3c3 2 7 4 10 6l-1 1-3-1c-3-2-5-3-8-5-2-1-4-4-6-5-3-2-6-5-9-8v1c-1-1-1-1-1 0-1-1-1-1-1-2-3-2-5-6-7-9-1-2-1-3-3-5 0-3-2-6-3-9l-3-6v-5c0-3-1-6-1-10 0-2 0-6 1-9 0-2 1-4 1-7v-1z" class="P"></path><path d="M247 454s0 1 1 2h0v6h0c0 3 0 6 1 9l-1 1-1 1c-1-6-1-13 0-19z" class="N"></path><path d="M249 471c1 8 3 17 9 24v2c-5-7-10-16-11-24l1-1 1-1z" class="g"></path><path d="M283 393h3 0l1 1-1 1h0c-1 1-2 1-3 2v1h1 0v1c-1 0-2 1-3 2-1 2-4 4-4 6h0c1-2 4-4 5-6 2-2 7-4 10-5 0 1 1 1 2 2h0l-3 2c-1 1-2 3-4 4h-1l-5 6c-1 2-2 4-3 5-3 5-4 12-5 18-1 4-3 7-3 11 0 3 0 5 1 7l1 2-1 2v1c1 0 1 1 1 2 1 2 2 4 2 7h-1c2 4 4 7 6 11h-1c4 6 9 10 15 13l2 1 6 3s2 0 2 1c1 1 1 1 1 2l8 2c1 1 2 1 3 1l1 1 1 1c2 0 3 0 5 1 5 1 10 1 15 1h4 21c-3 1-6 2-9 2h0-7v1h4 0-3 0 3c-3 1-7 1-10 1-7 0-14 0-20-1l-12-3c0 1-1 1-1 1-1-1-2-1-4-1 0 0 0-1-1-1s0 1-1 0h-3 0-2c-2 0-4-1-6-2-3 0-7-3-10-5-3-3-6-5-9-8s-5-7-7-11v1l-1 1v-1c-1-4-3-8-4-12-3-9-2-18-1-27 0 1-1 1-1 2v-1c-2-1-1-4-2-6v1c-1 1-1 2-2 4 0 1 0 2-1 3h0-1v-1h-1l2-7-1-2-1-1h0l1-1h0l-1 1h-1 0c1-2 2-4 2-6 1-2 3-3 4-5-1 0-1 0-2-1v1l-1-1 6-7 3-3c2-2 4-3 6-4l3-3c2-1 4-2 6-4l3-1h0l3-1c0-1 1-1 1-2z" class="K"></path><path d="M266 430h1c0 2 0 5-1 7 0 2-1 4 0 6v1c-1 0 0-3-1-4h0c0-1 1-4 1-4h-1-1l2-6h0z" class="B"></path><path d="M271 466c1 1 2 3 4 5 1 2 2 3 3 5h0l-1 1c-1-1-2-1-2-2-1-2-2-3-3-5-1-1-1-3-1-4zm18 22c1 0 2 1 4 1l2 1 6 3s2 0 2 1c1 1 1 1 1 2-3 0-6-2-9-3l-1 1-4-2c-2-1-2-1-2-3h0v-1h1z" class="C"></path><path d="M290 492h1l4 1h0l-1 1-4-2z" class="E"></path><path d="M288 489c-1-1-4-3-5-2v1c-1-1-2-2-3-2-1-1-2-3-3-4s-1-2-2-3c-2-2-3-4-4-6s-2-4-2-6l3 6c1 2 3 5 5 6 4 2 7 7 11 9h1-1v1h0z" class="D"></path><path d="M322 502c5 1 10 1 15 1h4 21c-3 1-6 2-9 2-11 0-22 0-32-2l1-1z" class="F"></path><path d="M294 494l1-1c3 1 6 3 9 3l8 2c1 1 2 1 3 1l1 1 1 1c2 0 3 0 5 1l-1 1c-10-1-19-5-27-9z" class="H"></path><path d="M267 444c0 1 0 3 1 4 0 3 0 5 2 7h0c1 1 1 2 2 3 1 2 2 4 2 7h-1c2 4 4 7 6 11h-1 0c-1-2-2-3-3-5-2-2-3-4-4-5 0-1-1-3-1-4l-3-9v-9z" class="M"></path><path d="M270 455h0c1 1 1 2 2 3 1 2 2 4 2 7h-1c-2-2-2-7-3-10z" class="C"></path><path d="M282 401c2-2 7-4 10-5 0 1 1 1 2 2h0l-3 2c-1 1-2 3-4 4h-1l-5 6c-1 2-2 4-3 5-3 5-4 12-5 18-1 4-3 7-3 11 0 3 0 5 1 7l1 2-1 2v1c1 0 1 1 1 2-1-1-1-2-2-3h0c-2-2-2-4-2-7-1-1-1-3-1-4 0-2 1-5 2-7v-2h0v-1h-1l-2 9c-1-2 0-4 0-6 1-2 1-5 1-7h-1 0c0-1 0-1-1-2 1-3 2-5 4-8 0-1 1-2 2-3 0-1 0-1-1-1l-1 2-1 1h0-1l1-2 3-3c1-1 1-2 2-3l1-1h-1c1-2 2-4 4-6 1-1 2-2 4-3-1 2-4 4-4 6h0c1-2 4-4 5-6z" class="X"></path><path d="M288 401c1 0 2-1 3-1-1 1-2 3-4 4h-1c-2 1-4 5-7 5 3-3 6-6 9-8z" class="T"></path><path d="M277 412h0c0 1-1 2-1 3-3 3-4 8-4 12-1 3-2 6-3 8h0v-1h-1 0c0-2 1-4 2-7 1-5 3-11 7-15z" class="C"></path><path d="M282 401c2-2 7-4 10-5 0 1 1 1 2 2h0l-3 2c-1 0-2 1-3 1-2 0-4 2-5 3-2 1-3 3-5 5v-1c0-1 0-1 1-1 1-2 2-4 4-5l-1-1z" class="c"></path><path d="M282 401l1 1c-2 1-3 3-4 5-1 0-1 0-1 1v1h0v2l-1 1c-4 4-6 10-7 15-1 3-2 5-2 7h0l-2 9c-1-2 0-4 0-6 1-2 1-5 1-7 2-4 3-8 5-12 0-1 0-2 1-3 1-2 2-3 3-4v-1c0-1 1-2 1-3 1-2 4-4 5-6z" class="Z"></path><path d="M270 440c1-6 2-12 4-18 1-2 2-6 4-7-3 5-4 12-5 18-1 4-3 7-3 11 0 3 0 5 1 7l1 2-1 2v1c1 0 1 1 1 2-1-1-1-2-2-3h0c-2-2-2-4-2-7-1-1-1-3-1-4 0-2 1-5 2-7 0 2 0 2 1 3z" class="E"></path><path d="M270 444c0 3 0 5 1 7l1 2-1 2v1c1 0 1 1 1 2-1-1-1-2-2-3v-2c-1-2-1-7 0-9z" class="F"></path><path d="M269 437c0 2 0 2 1 3-1 3-2 5-2 8-1-1-1-3-1-4 0-2 1-5 2-7z" class="J"></path><path d="M277 404c1-1 2-2 4-3-1 2-4 4-4 6h0c0 1-1 2-1 3v1c-1 1-2 2-3 4-1 1-1 2-1 3-2 4-3 8-5 12h-1 0c0-1 0-1-1-2 1-3 2-5 4-8 0-1 1-2 2-3 0-1 0-1-1-1l-1 2-1 1h0-1l1-2 3-3c1-1 1-2 2-3l1-1h-1c1-2 2-4 4-6z" class="S"></path><path d="M276 410v1c-1 1-2 2-3 4-1 1-1 2-1 3-2 4-3 8-5 12h-1c1-3 2-5 3-7 0-2 2-5 2-7 0-1 4-5 5-6z" class="C"></path><path d="M267 419h1 0l1-1 1-2c1 0 1 0 1 1-1 1-2 2-2 3-2 3-3 5-4 8 1 1 1 1 1 2l-2 6v3c-1 3-2 7-2 10 0 11 3 22 9 31 2 2 5 4 6 7 7 5 14 10 22 13 3 2 6 2 9 3 0 1-1 1-1 1-1-1-2-1-4-1 0 0 0-1-1-1s0 1-1 0h-3 0-2c-2 0-4-1-6-2-3 0-7-3-10-5-3-3-6-5-9-8s-5-7-7-11v1l-1 1v-1c-1-4-3-8-4-12-3-9-2-18-1-27 0-3 1-6 2-9h1 0 1 0c0-1 0-2 1-3 1-3 2-5 4-7z" class="g"></path><path d="M277 491c2 0 3 0 4 1s1 1 1 2h1c-3-1-4-2-6-3z" class="J"></path><path d="M260 456c0-2 0-3 1-5v6c1 1 1 4 1 5h-1c0-2-1-4-1-6z" class="I"></path><path d="M265 428c1 1 1 1 1 2l-2 6v3h-1 0c0-2 0-4 1-6 0-2 0-3 1-5z" class="o"></path><path d="M271 480c2 2 5 4 6 7-1 0-2-1-3-1-2-2-3-3-3-6z" class="M"></path><path d="M262 432l1-1h0c0 3-1 4-1 6-1 5-2 10-2 15v4c0 2 1 4 1 6h-1c-2-10-1-20 2-30z" class="G"></path><g class="C"><path d="M264 476v-4c3 8 7 13 13 19 2 1 3 2 6 3 2 2 4 3 7 5 1 1 3 2 5 2 1 0 2 0 3 1h0-2c-2 0-4-1-6-2-3 0-7-3-10-5-3-3-6-5-9-8s-5-7-7-11z"></path><path d="M260 429h1 0 1 0v3c-3 10-4 20-2 30l2 7c1 1 2 2 2 3v4 1l-1 1v-1c-1-4-3-8-4-12-3-9-2-18-1-27 0-3 1-6 2-9z"></path></g><path d="M262 469c1 1 2 2 2 3v4 1l-1 1v-1c1-2 0-4-1-6v-2z" class="F"></path><path d="M283 393h3 0l1 1-1 1h0c-1 1-2 1-3 2v1h1 0v1c-1 0-2 1-3 2-2 1-3 2-4 3-2 2-3 4-4 6h1l-1 1c-1 1-1 2-2 3l-3 3-1 2c-2 2-3 4-4 7-1 1-1 2-1 3h0-1 0-1c-1 3-2 6-2 9 0 1-1 1-1 2v-1c-2-1-1-4-2-6v1c-1 1-1 2-2 4 0 1 0 2-1 3h0-1v-1h-1l2-7-1-2-1-1h0l1-1h0l-1 1h-1 0c1-2 2-4 2-6 1-2 3-3 4-5-1 0-1 0-2-1v1l-1-1 6-7 3-3c2-2 4-3 6-4l3-3c2-1 4-2 6-4l3-1h0l3-1c0-1 1-1 1-2z" class="m"></path><path d="M257 429c1-3 3-6 5-8 1-2 3-5 5-7l6-7v1c-1 2-3 4-5 5-5 7-10 15-11 23v3c-2-1-1-4-2-6h0c-1-2 0-3 1-5l1 1z" class="F"></path><path d="M255 433h0c-1-2 0-3 1-5l1 1h0c0 1 0 1-1 1v5c1 1 0 1 1 1v3c-2-1-1-4-2-6z" class="h"></path><path d="M273 410h1l-1 1c-1 1-1 2-2 3l-3 3-1 2c-2 2-3 4-4 7-1 1-1 2-1 3h0-1 0-1c-1 3-2 6-2 9 0 1-1 1-1 2v-1-3c1-8 6-16 11-23l1 2 4-5z" class="S"></path><path d="M273 410h1l-1 1c-1 1-1 2-2 3l-3 3-1 2c-2 2-3 4-4 7-1 1-1 2-1 3h0-1 0-1c2-5 5-10 9-14l4-5z" class="B"></path><path d="M263 416c1-1 1-3 3-4v1c-4 5-8 10-10 15-1 2-2 3-1 5h0v1c-1 1-1 2-2 4 0 1 0 2-1 3h0-1v-1h-1l2-7-1-2-1-1h0l1-1c1-2 2-2 2-4 1-1 2-2 2-3h0l1-1 5-5h2z" class="W"></path><path d="M253 433v5c0 1 0 2-1 3h0-1v-1c0-2 1-4 2-7z" class="B"></path><path d="M251 429c1-2 2-2 2-4 1-1 2-2 2-3h0l1-1c-1 3-3 6-4 9v3l-1-2-1-1h0l1-1z" class="H"></path><path d="M263 416c1-1 1-3 3-4v1c-4 5-8 10-10 15-1 2-2 3-1 5h0v1c-1 1-1 2-2 4v-5c1-6 6-12 10-17z" class="C"></path><path d="M283 393h3 0l1 1-1 1h0c-1 1-2 1-3 2v1h1 0v1c-1 0-2 1-3 2-2 1-3 2-4 3h-1c1-1 1-2 2-3h-1c-5 2-9 6-12 10l-4 5-5 5-1 1h0c0 1-1 2-2 3 0 2-1 2-2 4h0l-1 1h-1 0c1-2 2-4 2-6 1-2 3-3 4-5-1 0-1 0-2-1v1l-1-1 6-7 3-3c2-2 4-3 6-4l3-3c2-1 4-2 6-4l3-1h0l3-1c0-1 1-1 1-2z" class="d"></path><path d="M283 393h3c-2 2-2 2-4 2 0-1 1-1 1-2z" class="Y"></path><path d="M261 408v1c-1 2-2 3-3 5-2 1-3 3-5 4v1l-1-1 6-7 3-3z" class="Z"></path><path d="M286 395h0c-1 1-2 1-3 2v1h1 0v1c-1 0-2 1-3 2-2 1-3 2-4 3h-1c1-1 1-2 2-3 1-2 3-4 5-5 1 0 2-1 3-1z" class="T"></path><path d="M279 396v1h-1l-3 3c-1 0-2 1-3 1-4 1-5 6-8 7-1 0-2 1-3 1v-1c2-2 4-3 6-4l3-3c2-1 4-2 6-4l3-1z" class="L"></path><path d="M260 413c1-2 3-3 5-4l11-8c1-1 1-1 1 0-5 2-9 6-12 10-3 0-5 5-7 7 0-2 1-3 2-4v-1z" class="O"></path><path d="M260 413v1c-1 1-2 2-2 4 2-2 4-7 7-7l-4 5-5 5-1 1h0c0 1-1 2-2 3 0 2-1 2-2 4h0l-1 1h-1 0c1-2 2-4 2-6 1-2 3-3 4-5s3-4 5-6z" class="h"></path><path d="M74 273l3-15c4-23 12-47 24-69 16-31 41-57 70-77 62-42 145-51 215-26 8 3 17 6 24 10l15 8c4 3 9 6 14 6 6 2 11 0 16-3 8-6 11-14 12-24h38 0v176c-4-1-8 0-12 0h-25c-1-2-1-4-2-6l-3-12c-4-14-9-27-16-40-16-31-44-53-77-63-35-10-73-7-105 10-25 14-47 39-59 66-3 7-5 15-7 23v1h1s1 0 1-1c3-1 5-3 7-4 3-2 6-3 9-4l4-1 1-1h1 2v1l-1 3c-1 2-1 4-2 5v1l-2 6c0 2 0 4-1 6h0c0 2 0 4-1 5h0v2 4h0c0 2-1 4-1 6-1-1-2-1-3-2-1 0-1 0-2-1-4 1-6 3-10 4-3 2-7 4-9 6h0v2c-1 1-2 3-3 4 0 1-1 3-1 4 1 1 0 2 2 3v1l1 1c-1 1-2 3-4 4-1 7-1 13-2 20v9l-1 8-1 10c0 17 1 34 2 51 2 28 6 55 13 82 2 9 4 18 8 27 6 17 17 35 29 49 24 26 56 40 91 41 42 2 83-11 114-39l7-7 1-2c4-3 6-7 9-11l2-3 6-9c1-3 3-6 4-9 0-1 1-2 2-3l1-3c2 0 4 3 6 4 6 5 12 10 17 16 4 4 7 8 10 13 1 1 2 2 2 3l1 1-3 5-2 2-3 6-1 1-5 7-3 4-21 24c-31 29-72 47-114 54a227.21 227.21 0 0 1-52 3c-27 0-54-4-80-12-35-11-68-32-93-59-13-13-24-29-33-45-8-14-14-30-19-46-13-42-15-87-14-131 1-3 1-7 1-11v-1c1-4 0-8 0-11l1-2v-5l1-8 3-34c1-3 1-5 2-8z" class="q"></path><path d="M191 271c1 1 1 2 2 2v2c-1 1-2 3-3 4l1-8z" class="D"></path><path d="M189 283c1 1 0 2 2 3v1l1 1c-1 1-2 3-4 4 0-3 1-6 1-9zm5-20l-1 4c1 1 0 1 1 1v1c-1 1-1 2-2 4h1 0c-1 0-1-1-2-2l1-7h1 0l1-1z" class="B"></path><defs><linearGradient id="Ag" x1="202.87" y1="244.262" x2="209.14" y2="253.676" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#Ag)" d="M199 237v1h1s1 0 1-1c3-1 5-3 7-4 3-2 6-3 9-4l4-1 1-1h1 2v1l-1 3c-1 2-1 4-2 5v1l-2 6c0 2 0 4-1 6h0c0 2 0 4-1 5h0v2 4h0c0 2-1 4-1 6-1-1-2-1-3-2-1 0-1 0-2-1-4 1-6 3-10 4-3 2-7 4-9 6h-1c1-2 1-3 2-4v-1c-1 0 0 0-1-1l1-4-1 1h0-1l3-14 1-4v-2l1-2 2-5z"></path><path d="M198 246l2-1h1c-1 2-3 3-5 5h0-1l1-4 1 1s1 0 1-1z" class="B"></path><path d="M197 255v-1c2-4 8-8 12-10l1 1c-5 2-9 6-13 10z" class="F"></path><path d="M209 244c3-2 6-4 9-5 1-1 2-1 2-2h2l-2 6h-1v-1-2-1l-9 6-1-1z" class="a"></path><path d="M210 245l9-6v1 2 1c-1 0-2 0-2 1-2 2-4 3-6 4h0l1 1-9 6-7 6v-1l-1-1h0c0-2 1-3 2-4 4-4 8-8 13-10z" class="P"></path><path d="M196 260c2-3 6-7 9-9l-4 4h1 1l-7 6v-1z" class="E"></path><path d="M205 251c2-1 4-3 6-3h0l1 1-9 6h-1-1l4-4z" class="H"></path><path d="M219 243h1c0 2 0 4-1 6h0c0 2 0 4-1 5h0v-2c-1 0-1 1-2 1-1 1-4 2-5 3-3 0-6 4-8 4-4 1-7 5-9 8-1 0 0 0-1-1l1-4s2-1 2-2l7-6 9-6-1-1h0c2-1 4-2 6-4 0-1 1-1 2-1z" class="P"></path><path d="M219 243h1c0 2 0 4-1 6l-1-2h1l-1-1c-2 0-4 1-6 3l-1-1h0c2-1 4-2 6-4 0-1 1-1 2-1z" class="g"></path><path d="M203 260c4-3 8-7 13-9 1-1 2-1 3-2 0 2 0 4-1 5h0v-2c-1 0-1 1-2 1-1 1-4 2-5 3-3 0-6 4-8 4z" class="h"></path><path d="M203 260c2 0 5-4 8-4 1-1 4-2 5-3 1 0 1-1 2-1v2 2 4h0c0 2-1 4-1 6-1-1-2-1-3-2-1 0-1 0-2-1-4 1-6 3-10 4-3 2-7 4-9 6h-1c1-2 1-3 2-4v-1c2-3 5-7 9-8z" class="C"></path><path d="M218 256v4c0-1 0-1-1-2-2 0-4 1-6 2v-1c2-1 5-2 7-3z" class="L"></path><path d="M211 260c2-1 4-2 6-2 1 1 1 1 1 2h0c0 2-1 4-1 6-1-1-2-1-3-2-1 0-1 0-2-1-4 1-6 3-10 4-3 2-7 4-9 6h-1c1-2 1-3 2-4l1-1c3-2 6-3 9-5 2 0 4-2 6-2 0-1 0-1 1-1z" class="e"></path><path d="M212 263c1 0 3 0 4-1 1 0 1-1 2-2 0 2-1 4-1 6-1-1-2-1-3-2-1 0-1 0-2-1z" class="g"></path><path d="M199 237v1h1s1 0 1-1c3-1 5-3 7-4 3-2 6-3 9-4l4-1 1-1h1 2v1l-1 3c-1 2-1 4-2 5 0-1 0-1-1-2-3 1-4 2-6 3-5 3-10 5-14 8h-1l-2 1c0 1-1 1-1 1l-1-1v-2l1-2 2-5z" class="P"></path><path d="M196 244c1 2 1 2 2 2 0 1-1 1-1 1l-1-1v-2z" class="K"></path><path d="M200 245c5-4 10-7 15-9 2-2 5-2 6-4 1 0 2-1 3-1-1 2-1 4-2 5 0-1 0-1-1-2-3 1-4 2-6 3-5 3-10 5-14 8h-1z" class="F"></path><path d="M199 237v1h1s1 0 1-1c3-1 5-3 7-4 3-2 6-3 9-4l4-1 1-1h1 2v1h-2-1l-1 1c-2 0-4 1-6 2h0l-4 1-1 1-1 1h-1l-5 4c-2 1-4 3-6 4l2-5z" class="C"></path></svg>