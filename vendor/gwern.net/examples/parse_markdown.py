#!/usr/bin/env python3
"""
Example script demonstrating the Markdown parser.

This script parses a Markdown file and prints the extracted metadata and HTML output.
"""
import sys
import os
import argparse
from pathlib import Path

# Add the parent directory to the path so we can import gwernpy
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from gwernpy.parser.markdown import MarkdownParser, MarkdownParserError


def main():
    """Parse a Markdown file and print the metadata and HTML output."""
    parser = argparse.ArgumentParser(description='Parse a Markdown file.')
    parser.add_argument('file', type=str, help='Path to the Markdown file')
    parser.add_argument('--output', '-o', type=str, help='Path to the output HTML file')
    args = parser.parse_args()
    
    try:
        # Initialize the Markdown parser
        md_parser = MarkdownParser(
            extensions=['tables', 'footnotes', 'code_hilite', 'front_matter']
        )
        
        # Parse the file
        metadata, content = md_parser.parse_file(args.file)
        
        # Print the metadata
        print("Metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        # Render the HTML
        html = md_parser.render_html(content, metadata)
        
        # Print or save the HTML
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(html)
            print(f"\nHTML output saved to {args.output}")
        else:
            print("\nHTML Output:")
            print(html)
        
    except FileNotFoundError:
        print(f"Error: File not found: {args.file}")
        return 1
    except MarkdownParserError as e:
        print(f"Error parsing Markdown: {e}")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
