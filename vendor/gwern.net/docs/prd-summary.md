# Gwern Python: Core Components PRD Summary

This document provides a high-level overview of the four core components of the gwern-python project, each detailed in their individual PRDs.

## Project Overview

Gwern Python is a Python-based reimplementation of the gwern.net website infrastructure, designed to replace the existing Haskell-based system. The project aims to provide a more maintainable and extensible codebase for someone familiar with Python, while preserving the core functionality and concepts of the original system.

## Core Components

### 1. Markdown Parser with Front Matter Support

**Purpose**: Process Markdown content with metadata for site generation.

**Key Features**:
- YAML front matter extraction and validation
- Extended Markdown syntax support (tables, footnotes, etc.)
- Code blocks with syntax highlighting
- Math expressions and custom extensions
- Clean API for integration with the site generator

**Integration Points**:
- Provides parsed content to the site generator
- Extracts metadata for use in templates
- Supports custom syntax extensions

[Full PRD](prd-markdown-parser.md)

### 2. Tufte CSS Templates

**Purpose**: Provide elegant, readable templates following <PERSON>'s design principles.

**Key Features**:
- Sidenotes and margin notes
- Margin figures with captions
- Proper typography with appropriate font choices
- Responsive design for all screen sizes
- Flexible templating system for various content types

**Integration Points**:
- Receives content from the site generator
- Uses metadata for template rendering
- Provides HTML output for the final site

[Full PRD](prd-tufte-css-templates.md)

### 3. Site Generation Pipeline

**Purpose**: Transform source content into a complete static website.

**Key Features**:
- Content discovery and processing
- Incremental builds for efficient updates
- Asset management and optimization
- Maintenance of directory structure
- Generation of index, tag, and feed pages

**Integration Points**:
- Uses the Markdown parser for content processing
- Applies templates to generate HTML
- Manages the overall build process
- Handles file system operations

[Full PRD](prd-site-generation.md)

### 4. Org-Roam Connector

**Purpose**: Integrate with org-roam for knowledge management and content creation.

**Key Features**:
- Connection to org-roam database
- Tag-based selection of notes for publication
- Conversion of org-mode to appropriate formats
- Maintenance of bidirectional links
- Synchronization of changes

**Integration Points**:
- Provides content to the site generator
- Converts org-mode to Markdown or HTML
- Manages links between notes
- Handles privacy and selective publication

[Full PRD](prd-orgroam-connector.md)

## Component Relationships

```
┌─────────────────┐     ┌─────────────────┐
│   Org-Roam      │     │   Markdown      │
│   Connector     │────▶│   Parser        │
└─────────────────┘     └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │   Site          │
                        │   Generator     │
                        └────────┬────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │   Tufte CSS     │
                        │   Templates     │
                        └─────────────────┘
```

## Implementation Priorities

1. **Phase 1: Core Infrastructure**
   - Markdown parser with basic front matter
   - Simple templates with Tufte CSS
   - Basic site generation pipeline
   - Initial org-roam connection

2. **Phase 2: Feature Completion**
   - Extended Markdown syntax
   - Complete Tufte design elements
   - Incremental builds and asset management
   - Advanced org-roam integration

3. **Phase 3: Refinement and Optimization**
   - Custom extensions and advanced features
   - Performance optimizations
   - Comprehensive documentation
   - Full test coverage

## Success Criteria

The implementation will be considered successful when:

1. All components work together seamlessly
2. The system can generate a complete static site with Tufte CSS styling
3. Org-roam notes can be selectively published
4. The directory structure matches the original design
5. All code follows PEP standards with comprehensive type hinting
6. Unit tests provide adequate coverage
7. Bot scraping can be controlled via configuration

## Next Steps

1. Review and finalize individual PRDs
2. Prioritize implementation tasks
3. Begin development of core components
4. Create integration tests
5. Develop example content for testing
