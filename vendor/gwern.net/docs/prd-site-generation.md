# Product Requirements Document: Site Generation Pipeline

## Overview
The Site Generation Pipeline component will provide a comprehensive system for transforming source content into a complete static website. This pipeline will handle content processing, template rendering, asset management, and output generation while maintaining the same directory structure as the original gwern.net site.

## Goals
- Create a flexible and extensible site generation pipeline
- Maintain the same directory structure as the original site
- Support incremental builds for efficient content updates
- Provide a clean CLI interface for site generation
- Generate static output that can be served by any web server

## User Stories
1. As a content creator, I want to generate a complete website from my Markdown and org-mode files so that I can publish my content online.
2. As a developer, I want an extensible pipeline so that I can add custom processing steps for specific content types.
3. As a site maintainer, I want incremental builds so that I can quickly update the site when only a few files change.
4. As a content creator, I want to maintain the same URL structure as my existing site so that links to my content remain valid.

## Requirements

### Functional Requirements
1. **Content Discovery**
   - Recursively scan source directories for content files
   - Support multiple content formats (Markdown, org-mode)
   - Detect and process metadata from front matter
   - Handle file dependencies and relationships

2. **Processing Pipeline**
   - Parse content files into structured data
   - Apply transformations based on content type
   - Generate HTML from processed content
   - Create index pages for directories
   - Generate tag/category pages

3. **Asset Management**
   - Copy static assets to output directory
   - Process and optimize images
   - Bundle and minify CSS and JavaScript
   - Generate appropriate cache headers

4. **Output Generation**
   - Maintain the same directory structure in output
   - Generate clean URLs (e.g., `/page/` instead of `/page.html`)
   - Create redirects for renamed content
   - Generate sitemap and RSS/Atom feeds

5. **Build System**
   - Support incremental builds
   - Track file dependencies
   - Parallel processing for improved performance
   - Clear cache management

### Non-Functional Requirements
1. **Performance**
   - Efficient processing of large content repositories
   - Fast incremental builds
   - Minimal memory footprint

2. **Reliability**
   - Graceful handling of errors in content files
   - Detailed logging of build process
   - Validation of output structure

3. **Maintainability**
   - Modular design with clear separation of concerns
   - Comprehensive documentation
   - Extensive test coverage

## Technical Specifications

### Component Structure
```
gwernpy/generator/
├── __init__.py
├── site.py              # Main site generator
├── pipeline.py          # Processing pipeline
├── assets.py            # Asset management
├── html.py              # HTML generation
├── templates.py         # Template management
├── index.py             # Index page generation
├── feeds.py             # Feed generation
└── utils/
    ├── __init__.py
    ├── urls.py          # URL handling
    ├── fs.py            # File system operations
    └── cache.py         # Cache management
```

### Build Process Flow
1. **Discovery Phase**
   - Scan source directories
   - Identify content files and their types
   - Build dependency graph

2. **Processing Phase**
   - Parse content files
   - Extract and validate metadata
   - Apply content transformations
   - Generate HTML

3. **Assembly Phase**
   - Generate index pages
   - Create tag/category pages
   - Process and copy assets
   - Generate feeds and sitemaps

4. **Output Phase**
   - Write files to output directory
   - Create redirects
   - Generate cache manifests

### Dependencies
- Core parsing modules (Markdown, org-mode)
- Template engine (Jinja2)
- File system utilities
- Asset processing libraries

### API Design
```python
class SiteGenerator:
    def __init__(self, config: Dict[str, Any]):
        """Initialize the site generator with configuration."""
        
    def build(self, source_dir: str, output_dir: str, incremental: bool = True) -> None:
        """Build the site from source to output directory."""
        
    def clean(self, output_dir: str) -> None:
        """Clean the output directory."""
        
    def serve(self, output_dir: str, port: int = 8000) -> None:
        """Serve the output directory for local preview."""
        
    def register_processor(self, file_type: str, processor: Callable) -> None:
        """Register a custom processor for a specific file type."""
```

## Implementation Plan
1. **Phase 1: Core Infrastructure**
   - Implement file discovery and basic pipeline
   - Create content parsing integration
   - Set up template rendering
   - Implement basic output generation

2. **Phase 2: Advanced Features**
   - Add incremental build support
   - Implement index and tag page generation
   - Add asset processing
   - Create feed and sitemap generation

3. **Phase 3: Optimization and Extensions**
   - Implement parallel processing
   - Add caching mechanisms
   - Create plugin system for extensions
   - Add comprehensive logging and error handling

## Success Criteria
- Site structure matches the original gwern.net structure
- All content types are correctly processed
- Incremental builds are significantly faster than full builds
- Generated site passes validation checks
- CLI interface is intuitive and well-documented

## Future Considerations
- Watch mode for automatic rebuilds on file changes
- Integration with version control systems
- Remote deployment capabilities
- Performance profiling and optimization
- Content validation and link checking
