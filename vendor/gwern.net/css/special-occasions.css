/*********************/
/* SPECIAL OCCASIONS */
/*********************/

/*=--------------=*/
/*= April Fool’s =*/
/*=--------------=*/

.popup.april-fools-special-birthdaycat {
	width: 514px !important;
	height: 704px !important;
	max-height: unset !important;
}
.popup.april-fools-special-birthdaycat.offset {
	left: unset !important;
	right: 64px !important;
	top: -800px !important;
}
.popup.april-fools-special-birthdaycat.offset.moved {
	top: 8px !important;
	transition:
		top 5s ease;
}

/*=-----------=*/
/*= Halloween =*/
/*=-----------=*/

body.special-halloween-dark {
    --GW-blood-red: #b00;
    --GW-blood-red-dried: #700;
    --GW-blood-red-arterial: #e00;

    --GW-nav-header-link-color: var(--GW-blood-red-dried);
    --GW-nav-header-link-hover-color: var(--GW-blood-red-arterial);

    --GW-TOC-border-color: var(--GW-blood-red-dried);
    --GW-abstract-border-color: var(--GW-blood-red-dried);

    --GW-pre-element-border-color: var(--GW-blood-red-dried);

    --GW-epigraph-quotation-mark-color: var(--GW-blood-red-dried);

    --GW-H1-border-color: var(--GW-blood-red);
    --GW-H2-border-color: var(--GW-blood-red);

    --GW-dropcaps-yinit-color: var(--GW-blood-red);
    --GW-dropcaps-yinit-text-shadow-color: var(--GW-blood-red);
    --GW-dropcaps-de-zs-color: var(--GW-blood-red);
    --GW-dropcaps-cheshire-color: var(--GW-blood-red);
    --GW-dropcaps-kanzlei-color: var(--GW-blood-red);

    --GW-figure-outline-color: var(--GW-blood-red-dried);
    --GW-figure-caption-outline-color: var(--GW-blood-red);

    --GW-footnotes-section-top-rule-color: var(--GW-blood-red-dried);
    --GW-footnote-border-color: var(--GW-blood-red-arterial);
    --GW-sidenote-highlight-box-shadow-color: var(--GW-blood-red-arterial);
    --GW-footnote-backlink-border-color: var(--GW-blood-red);
    --GW-footnote-backlink-border-hover-color: var(--GW-blood-red-arterial);
    --GW-footnote-highlighted-border-color: var(--GW-blood-red);

    --GW-sidenote-self-link-border-color: var(--GW-blood-red-arterial);
    --GW-sidenote-border-color: var(--GW-blood-red-arterial);

    --GW-math-block-scrollbar-border-color: var(--GW-blood-red);

    --GW-collapse-disclosure-button-text-color: var(--GW-blood-red-dried);
    --GW-collapse-disclosure-button-text-hover-color: var(--GW-blood-red);

    --GW-x-of-the-day-border-color: var(--GW-blood-red-dried);

    --GW-page-toolbar-border-color: var(--GW-blood-red-dried);
    --GW-page-toolbar-control-button-color: var(--GW-blood-red);

    --GW-bottom-ornament-line-color: var(--GW-blood-red-arterial);

    --GW-back-to-top-link-color: var(--GW-blood-red-dried);
    --GW-back-to-top-link-hover-color: var(--GW-blood-red-arterial);

    --GW-floating-header-scroll-indicator-color: var(--GW-blood-red);

    --GW-body-link-hover-color: var(--GW-blood-red-arterial);

    --GW-highlighted-link-outline-color: var(--GW-blood-red);
}

body.special-halloween-dark blockquote {
    --border-color: var(--GW-blood-red-dried);
}

body.special-halloween-dark .heading {
    --GW-body-text-color: var(--GW-blood-red);
    --GW-body-link-color: var(--GW-blood-red);

    color: var(--GW-body-text-color);
}

@media all and (max-width: 649px) {
	body.special-halloween-dark #sidebar a {
		--GW-body-link-color: var(--GW-blood-red-arterial);

		color: var(--GW-body-link-color);
	}
}

body.special-halloween-dark #navigation {
    color: var(--GW-blood-red);
}
body.special-halloween-dark #navigation a:hover {
    color: var(--GW-blood-red-arterial);
}

body.special-halloween-dark #sidebar svg.logo-image {
    visibility: hidden;
}
body.special-halloween-dark #sidebar svg.logo-image.visible {
	visibility: visible;
}
body.special-halloween-dark #sidebar span.logo-image img {
    filter: none;
}
@media all and (max-width: 649px) {
    body.special-halloween-dark #sidebar span.logo-image img {
        width: 133%;
        position: relative;
        left: -15%; top: -15%;
    }
}
@media all and (min-width: 650px) {
    @media all and (max-width: 1179px) {
        body.special-halloween-dark #sidebar span.logo-image img {
            width: 150%;
            position: relative;
            left: -30%; top: -15%;
        }
    }
    @media all and (min-width: 1180px) {
        body.special-halloween-dark #sidebar span.logo-image img {
            width: 150%;
            position: relative;
            left: -30%; top: -10%;
        }
    }
}

body.special-halloween-dark hr::after {
	filter: none;
    opacity: 1;
}
body.special-halloween-dark .horizontal-rule-nth-1 hr::after {
    background-image: url('/static/img/ornament/halloween/sun-verginasun-black.svg');
}
body.special-halloween-dark .horizontal-rule-nth-2 hr::after {
    background-image: url('/static/img/ornament/halloween/japanesecrest-tsukinihoshi-dottedmoon.svg');
}
body.special-halloween-dark .horizontal-rule-nth-3 hr::after {
    background-image: url('/static/img/ornament/halloween/asterism-triplewhitestar.svg');
}

body.special-halloween-dark #x-of-the-day::before,
body.special-halloween-dark #x-of-the-day::after {
    background-image:
        url('/static/img/ornament/halloween/three-wavy-lines-ornament-left.svg'),
        url('/static/img/ornament/halloween/three-wavy-lines-ornament-right.svg');
    filter: none;
    opacity: 0.75;
}
body.special-halloween-dark #x-of-the-day .site-of-the-day blockquote {
    background-image: url('/static/img/ornament/halloween/swissspiralroll.svg#svgView(preserveAspectRatio(none))');
}
body.special-halloween-dark #x-of-the-day .site-of-the-day blockquote::before,
body.special-halloween-dark #x-of-the-day .site-of-the-day blockquote::after {
    background-image: url('/static/img/ornament/halloween/swissspiralroll.svg');
}

/*  Styles for /index
 */
body.page-index.special-halloween-dark #sidebar a:hover {
    color: var(--GW-blood-red);
}

body.page-index.special-halloween-dark #sidebar a.logo {
	pointer-events: auto;
}

/*=-----------=*/
/*= Christmas =*/
/*=-----------=*/

body.special-christmas-light {
    --GW-santa-hat-red: #900;
    --GW-holly-berry-red: #c00;
    --GW-holly-leaf-green: #060;
    --GW-christmas-lights-red: #f00;
    --GW-mistletoe-green: #0c0;
    --GW-pine-needle-green: #050;
    --GW-fir-needle-green: #014421;
    --GW-spruce-needle-green: #4bd24d;
    --GW-christmas-lights-gold: #ff0;
}

body.special-christmas-dark {
    --GW-santa-hat-red: #d00;
    --GW-holly-berry-red: #f00;
    --GW-holly-leaf-green: #0a3;
    --GW-christmas-lights-red: #f00;
    --GW-mistletoe-green: #005519;
    --GW-pine-needle-green: #5eff66;
    --GW-spruce-needle-green: #5ce15b;
    --GW-fir-needle-green: #a7cab6;
    --GW-christmas-lights-gold: #996515;
}

body[class*='special-christmas'] {
    --GW-nav-header-link-color: var(--GW-holly-leaf-green);
    --GW-nav-header-link-hover-color: var(--GW-holly-berry-red);

    --GW-TOC-border-color: var(--GW-mistletoe-green);
    --GW-abstract-border-color: var(--GW-mistletoe-green);

    --GW-pre-element-border-color: var(--GW-holly-leaf-green);

    --GW-epigraph-quotation-mark-color: var(--GW-holly-leaf-green);

    --GW-H1-border-color: var(--GW-holly-leaf-green);
    --GW-H2-border-color: var(--GW-holly-leaf-green);

    --GW-dropcaps-yinit-color: var(--GW-santa-hat-red);
    --GW-dropcaps-yinit-text-shadow-color: var(--GW-santa-hat-red);
    --GW-dropcaps-de-zs-color: var(--GW-santa-hat-red);
    --GW-dropcaps-cheshire-color: var(--GW-santa-hat-red);
    --GW-dropcaps-kanzlei-color: var(--GW-santa-hat-red);

    --GW-figure-outline-color: var(--GW-holly-leaf-green);
    --GW-figure-caption-outline-color: var(--GW-holly-leaf-green);

    --GW-footnotes-section-top-rule-color: var(--GW-holly-leaf-green);
    --GW-footnote-border-color: var(--GW-holly-leaf-green);
    --GW-sidenote-highlight-box-shadow-color: var(--GW-holly-leaf-green);
    --GW-footnote-backlink-border-color: var(--GW-holly-leaf-green);
    --GW-footnote-backlink-border-hover-color: var(--GW-holly-berry-red);
    --GW-footnote-highlighted-border-color: var(--GW-holly-leaf-green);

    --GW-sidenote-self-link-border-color: var(--GW-holly-leaf-green);
    --GW-sidenote-border-color: var(--GW-holly-leaf-green);

    --GW-math-block-scrollbar-border-color: var(--GW-holly-leaf-green);

    --GW-collapse-disclosure-button-text-color: var(--GW-holly-leaf-green);
    --GW-collapse-disclosure-button-text-hover-color: var(--GW-holly-berry-red);

    --GW-x-of-the-day-border-color: var(--GW-holly-leaf-green);

    --GW-page-toolbar-border-color: var(--GW-holly-leaf-green);
    --GW-page-toolbar-control-button-color: var(--GW-holly-leaf-green);

    --GW-bottom-ornament-line-color: var(--GW-holly-leaf-green);

    --GW-back-to-top-link-color: var(--GW-holly-leaf-green);
    --GW-back-to-top-link-hover-color: var(--GW-holly-berry-red);

    --GW-floating-header-scroll-indicator-color: var(--GW-holly-leaf-green);

    --GW-body-link-color: var(--GW-pine-needle-green);
    --GW-body-link-visited-color: var(--GW-fir-needle-green);
    --GW-body-link-hover-color: var(--GW-holly-berry-red);

    --GW-highlighted-link-outline-color: var(--GW-santa-hat-red);
}

body[class*='special-christmas'] blockquote {
    --border-color: var(--GW-holly-leaf-green);
}

body[class*='special-christmas'] blockquote.blockquote-level-1,
body[class*='special-christmas'] blockquote.blockquote-level-3,
body[class*='special-christmas'] blockquote.blockquote-level-5 {
    --GW-body-link-color: var(--GW-holly-berry-red);
    --GW-body-link-visited-color: var(--GW-santa-hat-red);
    --GW-body-link-hover-color: var(--GW-pine-needle-green);
}
body[class*='special-christmas'] blockquote.blockquote-level-2,
body[class*='special-christmas'] blockquote.blockquote-level-4,
body[class*='special-christmas'] blockquote.blockquote-level-6 {
    --GW-body-link-color: var(--GW-pine-needle-green);
    --GW-body-link-visited-color: var(--GW-fir-needle-green);
    --GW-body-link-hover-color: var(--GW-holly-berry-red);
}

body[class*='special-christmas'] .heading {
    --GW-body-text-color: var(--GW-holly-leaf-green);
    --GW-body-link-color: var(--GW-holly-leaf-green);

    color: var(--GW-body-text-color);
}

body[class*='special-christmas'] header h1 {
    --GW-body-text-color: var(--GW-holly-leaf-green);

    color: var(--GW-body-text-color);
}

body[class*='special-christmas'] #navigation {
    color: var(--GW-santa-hat-red);
}
body[class*='special-christmas'] #navigation a:hover {
    color: var(--GW-holly-berry-red);
}

body[class*='special-christmas'] ol > li:nth-of-type(odd)::before {
	color: var(--GW-spruce-needle-green);
}
body[class*='special-christmas'] ol > li:nth-of-type(even)::before {
	color: var(--GW-christmas-lights-red);
}

/********/
/*  Glow.
 */

body.special-christmas-light header h1 {
    text-shadow:
        0 0 2px var(--GW-christmas-lights-gold);
}
body.special-christmas-light .heading a,
body.special-christmas-light p[class*='dropcap-'] span.dropcap,
body.special-christmas-light #navigation a,
body.special-christmas-light #sidebar a {
    text-shadow:
        0 0 1px var(--GW-christmas-lights-gold);
}

body.special-christmas-dark header h1 {
    text-shadow:
        0 0 6px var(--GW-body-background-color),
        0 0 4px var(--GW-christmas-lights-gold);
}
body.special-christmas-dark .heading a,
body.special-christmas-dark p[class*='dropcap-'] span.dropcap,
body.special-christmas-dark #navigation a,
body.special-christmas-dark #sidebar a {
    text-shadow:
        0 0 2px var(--GW-body-background-color),
        0 0 2px var(--GW-christmas-lights-gold);
}

/********/
/*  Logo.
 */

body[class*='special-christmas'] #sidebar svg.logo-image {
    visibility: hidden;
}
body[class*='special-christmas'] #sidebar span.logo-image img {
    filter: none;
}
@media all and (max-width: 649px) {
    body[class*='special-christmas'] #sidebar span.logo-image img {
        width: 133%;
        position: relative;
        left: -15%; top: -15%;
    }
}
@media all and (min-width: 650px) {
    @media all and (max-width: 1179px) {
        body[class*='special-christmas'] #sidebar span.logo-image img {
            width: 150%;
            position: relative;
            left: -30%; top: -15%;
        }
    }
    @media all and (min-width: 1180px) {
        body[class*='special-christmas'] #sidebar span.logo-image img {
            width: 125%;
            position: relative;
            left: -10%; top: -10%;
        }
    }
}

/*  Styles for /index
 */
body.page-index[class*='special-christmas'] #sidebar a:hover {
    color: var(--GW-holly-leaf-green);
}

body.page-index[class*='special-christmas'] #sidebar a.logo {
	pointer-events: auto;
}
