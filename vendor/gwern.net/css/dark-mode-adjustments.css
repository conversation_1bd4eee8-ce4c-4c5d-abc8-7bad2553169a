:root {
    /*  Adjust background color to prevent pixels from turning off (contrary to 
    	popular belief, pixels turning off saves trivial energy, like <1% in 
    	measurements, and causes nasty scrolling/update jank due to delay in 
    	pixels turning back on), and improve contrast with the white.
     */
    --GW-body-background-color: #161616;

    /*  Dial back from pure white to keep contrast from being *too* high & 
    	‘stark’ (#fff bg / #000 text seems to work in light mode, but not in 
    	dark mode, perhaps because of differences in lighting environment?)
     */
    --GW-body-text-color: #f1f1f1;

    --GW-popups-popup-title-bar-pattern: var(--GW-image-pattern-dotted-161616-on-252525-2x-gif);
    --GW-popups-popup-title-bar-pattern-focused: var(--GW-image-pattern-dotted-161616-on-3e3e3e-2x-gif);

    --GW-popins-popin-backdrop-color: rgba(0, 0, 0, 0.6);

    --GW-popins-popin-title-bar-button-color: #bbb;

    --GW-checkerboard-scrollbar-background-image: var(--GW-image-checkerboard-888-000-2x-gif);
    --GW-checkerboard-scrollbar-hover-background-image: var(--GW-image-checkerboard-bfbfbf-000-2x-gif);
}

.dark-mode-invert,
.dark-mode-invert::before,
.dark-mode-invert::after {
	filter: var(--dark-mode-invert-filter, none);
}

/*  Admonition icons.
 */
div.admonition.tip::before {
    filter: invert(1);
}
div.admonition.note::before {
    filter: none;
}
div.admonition.warning::before {
    filter: none;
}
div.admonition.error::before {
    filter: none;
}

/*  SVG icons in the two darker styles of admonitions.
 */
div.admonition.warning a[data-link-icon-type='svg'] .link-icon-hook::after,
div.admonition.error a[data-link-icon-type='svg'] .link-icon-hook::after {
    filter: none;
}

/*  For sortable table column headings, we use dark versions of the up/down/both
    arrow icons.
 */
table th.tablesorter-header {
    background-image: url('/static/img/tablesorter/tablesorter-bg-dark.gif');
}
table th.tablesorter-headerAsc {
    background-image: url('/static/img/tablesorter/tablesorter-asc-dark.gif');
}
table th.tablesorter-headerDesc {
    background-image: url('/static/img/tablesorter/tablesorter-desc-dark.gif');
}

/*  Images that are marked as '.invert' by the server are inverted,
    hue-rotated, and desaturated. Other (non-invertible) images are merely
    desaturated. Hovering over an image restores it to its original state.
    Hierarchy: ‘.invert-not’/‘.invert-not-auto’: no inversion or grayscale;
    ‘.invert’/‘.invert-auto’: inverted (uninverted upon mouse hover);
    none: grayscaled (ungrayscaled on hover).
 */
figure img.invert,
figure img.invert-auto {
    filter: grayscale(50%) invert(100%) brightness(95%) hue-rotate(180deg);
}
figure img:not(.invert):not(.invert-auto) {
    filter: grayscale(50%);
}
figure img,
figure img.invert,
figure img.invert-auto {
    transition: filter 0.25s ease;
}
figure img:not(.drop-filter-on-hover-not):hover,
figure img:not(.drop-filter-on-hover-not).invert:hover,
figure img:not(.drop-filter-on-hover-not).invert-auto:hover,
figure img:not(.drop-filter-on-hover-not):not(.invert):not(.invert-auto):hover {
    filter: none;
    transition: filter 0s ease 0.25s;
}

figure img[src$=".svg"].invert:hover,
figure img[src$=".svg"].invert-auto:hover {
    filter: grayscale(50%) invert(100%) brightness(95%) hue-rotate(180deg);
}

/*  Image alt-text.
 */
figure img.invert::before,
figure img.invert-auto::before {
    filter: invert(1);
}
figure img.invert:hover::before,
figure img.invert-auto:hover::before {
    filter: none;
}
/*  Styling the image alt-text interferes with the transitions in dark mode.
    (We include non-class’d `img` in this selector for consistency.)

    TEMPORARY until we transition to a color-based instead of filter-based
    scheme for this. —SA 2022-07-29
 */
figure img,
figure img:hover,
figure img.invert,
figure img.invert:hover,
figure img.invert-auto,
figure img.invert-auto:hover {
    transition: none;
}

/*  For images which have been marked up (manually or automatically) with 
	‘.invert-not’, we avoid any filtering at all. If they are manually marked up
	(artwork, diagrams with multiple subtly-different colors matched to a 
	legend/caption), the color is important and shouldn’t be faded out by 
	default. (Or invertOrNot has judged the image to be non-invertible, which
	presumably means something like the above also.)
 */
#markdownBody figure img.invert-not,
#markdownBody figure img.invert-not-auto {
    filter: none;
}

/*  The loading spinner for object popups (image, iframe, object) is inverted
    and made more visible in dark mode.
 */
.popframe.loading::before {
    filter: invert(1);
    opacity: 0.4;
}

/*  “Loading failed” messages for object popups.
 */
.popframe.loading-failed::after {
    opacity: 0.4;
}

/*  Masked links key toggle info alert panel.
 */
div#masked-links-key-toggle-info-alert img {
    filter: drop-shadow(0 0 3px var(--GW-reader-mode-masked-links-key-toggle-info-alert-panel-text-shadow-color));
}

/*  Recently-modified icon, manicule
 */
.has-recently-modified-icon .recently-modified-icon-hook::before,
.manicule svg {
    filter: invert(1);
}

