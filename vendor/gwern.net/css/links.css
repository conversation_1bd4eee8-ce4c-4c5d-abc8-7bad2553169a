/************************/
/* POP-FRAME INDICATORS */
/************************/

/*  If a link has an automatic or custom link annotation (eg. a paper abstract,
    or excerpts), subtly mark it with a dog-ear/tag-like icon, so users don’t
    get frustrated hovering over links which have no useful annotation (which
    is an unfortunate number).

    At some point, if enough links get annotated & annotation is the default,
    we might flip this to be a warning for non-annotated links.

    We exclude it on the homepage because every linked page has a
    description/abstract, and the triangles everywhere add a lot of visual
    clutter.
 */
.markdownBody a.has-indicator-hook {
    margin-left: 0.1em;
    position: relative;
    z-index: 1;

/*  This is currently broken in Safari. Since it’s not important enough to
    figure out a way to scope it to only non-Safari browsers, I’m disabling it
    for now. This should be revisited in a year.
        —SA 2023-09-24
 */
/*
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
 */
}

.markdownBody a.has-indicator-hook .indicator-hook {
    position: relative;
    padding-left: 0.3em;
}
.markdownBody a:not(.has-indicator-hook) .indicator-hook {
    display: none;
}

.markdownBody a.has-indicator-hook .indicator-hook::before {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    width: 1px;
    height: 100%;
    color: inherit;
    background-image:
        linear-gradient(var(--link-underline-background-color),
                        var(--link-underline-background-color)),
        linear-gradient(var(--link-underline-background-color),
                        var(--link-underline-background-color)),
        linear-gradient(var(--link-underline-gradient-line-color),
                        var(--link-underline-gradient-line-color));
    background-size:
        1px 0.6em,
        1px 0.1em,
        1px 1em;
    background-position:
        0 0,
        0 100%,
        0 calc(100% - 0.1em);
    background-repeat: no-repeat;
}

/*  Dotted underlines for annotated links.
 */
.markdownBody a.has-annotation,
.markdownBody a.has-annotation-partial {
    background-image: linear-gradient(to right,
                                      var(--link-underline-gradient-line-color) 50%,
                                      var(--link-underline-background-color) 50%);
    background-size: 2px 1px;
}
.markdownBody a.has-annotation.decorate-not {
    background-image: none;
}

/*  Colored (on hover) links.
 */
a[data-link-icon-color]:hover {
    --link-underline-gradient-line-color: var(--link-icon-color-hover);
}


/**************/
/* LINK ICONS */
/**************/

/*******************************************************************************/
/*  The philosophy of link icons is that the hyperlink text & surrounding
    context often leaves much out, which would be impossible to express inline
    conveniently (such as filetypes like PDF: a link being PDF is no longer a
    big deal for desktop readers, whose browser will open them quickly,
    natively, and render accurately, but due to the unfortunate state of mobile
    PDF viewing, mobile readers still care and may prefer not to open a PDF).

    Link icons provide some additional information in a compact iconic form
    which doesn't interrupt the text or require manual annotation, and are
    useful for denoting the logical source, the topic, the file format, or
    anything else one might be able to infer using regexes on a URL.

    To visualize the current set of link icons, see /lorem#link-icons.

    Link icons for domains can be auto-generated by extracting favicons from the
    domain (eg. https://www.pmwiki.org/wiki/Cookbook/ExternalLinksFavicons );
    this would be useful for many sites, particularly user-contributed ones, but
    I don't use this approach on Gwern.net for several reasons:

    1. most favicons look bad on their own (often they're too low-resolution to
       use inline) and aren't even recognizable by a reader anyway;
    2. most favicons need tweaking to fit into Gwern.net to avoid shattering the
       monochrome theme, and to get proper margins/spacing/opacity in both light
       & dark mode;
    3. domains and icons are long-tailed: most domains are linked only 1 or 2
       times, so even if they are high quality and usable as-is, readers would
       not recognize or benefit from them, and if they did, the benefit would be
       astronomically tiny (because they'd see it once or twice, max).

    Because Gwern.net is written just by myself, it is not hard to add new icons
    as the need arises and a domain or filetype becomes common enough that an
    icon might be useful, and the icons need to be manually-edited anyway.
 */
/*  Note: some file extension link types have are selected additionally by
    origin on Gwern.net, because they frequently appear at the ends of ordinary
    web page URLs.
 */

/*=================*/
/*= COMMON STYLES =*/
/*=================*/

.markdownBody a.has-icon {
    margin-right: 0.1em;
}

/*******************/
/*  Graphical icons.
    */
a[data-link-icon-type*='svg'] {
    --link-icon-size-default: 0.55em;
    --link-icon-offset-x-default: 0.20em;
    --link-icon-offset-y-default: 0.15em;
    --link-icon-opacity-default: 0.55;
}
a[data-link-icon-type*='svg'] .link-icon-hook::after {
    --dark-mode-invert-filter: invert(1);

    content: "";
    position: relative;
    bottom: var(--link-icon-offset-y, var(--link-icon-offset-y-default));
    padding: 0 var(--link-icon-size, var(--link-icon-size-default)) 0 0;
    margin: 0 0 0 var(--link-icon-offset-x, var(--link-icon-offset-x-default));
    background-image: var(--link-icon-url);
    background-size: var(--link-icon-size, var(--link-icon-size-default));
    background-position-x: center;
    background-position-y: 1ex;
    background-repeat: no-repeat;
    opacity: var(--link-icon-opacity, var(--link-icon-opacity-default));
}
a[data-link-icon-type*='svg']:hover .link-icon-hook::after {
    opacity: calc(var(--link-icon-opacity, var(--link-icon-opacity-default)) * 0.55);
}

/*  Quad-letter (rendered) SVG icons.
 */
a[data-link-icon-type*='svg'][data-link-icon-type*='quad'] {
    --link-icon-size-default: 0.75em;
    --link-icon-offset-x-default: 0.15em;
    --link-icon-offset-y-default: 0.30em;
    --link-icon-opacity-default: 0.80;
}
a[data-link-icon-type*='svg'][data-link-icon-type*='quad'] .link-icon-hook::after {
    background-position-y: 0.75ex;
}

/*  Quad-letter (rendered) SVG icons in a sans-serif font.
 */
a[data-link-icon-type*='svg'][data-link-icon-type*='quad'][data-link-icon-type*='sans'] {
    --link-icon-size-default: 0.80em;
}

/*  Quad-letter (rendered) SVG icons in a monospaced font.
 */
a[data-link-icon-type*='svg'][data-link-icon-type*='quad'][data-link-icon-type*='mono'] {
    --link-icon-size-default: 0.85em;
}

/*  Colored graphical link icons.
 */
a[data-link-icon-type*='svg'][data-link-icon-color]:hover .link-icon-hook::after {
    --dark-mode-invert-filter: none;

    background-image: var(--link-icon-url-hover, var(--link-icon-url));
    opacity: 1.0;
}

/*  When link colors are reversed relative to the rest of the page (e.g., in
    some admonition types), a graphical link icon must also be inverted.
 */
.markdownBody .colors-invert a[data-link-icon-type*='svg'] .link-icon-hook::after {
    filter: invert(1);
}

/*****************/
/*  Textual icons.
    */
a[data-link-icon-type*='text'] {
    --link-icon-size-default: 0.75em;
    --link-icon-offset-x-default: 0.12em;
    --link-icon-offset-y-default: 0.25em;
    --link-icon-opacity-default: 0.83;
    --link-icon-font-default: Noto Emoji, Quivira, var(--GW-serif-font-stack);
    --link-icon-font-weight-default: 600;
    --link-icon-font-style-default: normal;
    --link-icon-text-decoration-default: none;
}
a[data-link-icon-type*='text'] .link-icon-hook::after {
    content: var(--link-icon);
    font-size: var(--link-icon-size, var(--link-icon-size-default));
    font-weight: var(--link-icon-font-weight, var(--link-icon-font-weight-default));
    font-style: var(--link-icon-font-style, var(--link-icon-font-style-default));
    font-family: var(--link-icon-font, var(--link-icon-font-default));
    text-decoration: var(--link-icon-text-decoration, var(--link-icon-text-decoration-default));
    margin: 0 0 0 var(--link-icon-offset-x, var(--link-icon-offset-x-default));
    vertical-align: baseline;
    position: relative;
    bottom: var(--link-icon-offset-y, var(--link-icon-offset-y-default));
    opacity: var(--link-icon-opacity, var(--link-icon-opacity-default));
    padding: 0;
    background-image: none;
    background-size: unset;
    line-height: 1;
    overflow-wrap: normal;
}

a[data-link-icon-type*='sans'] {
    --link-icon-font-default: Noto Emoji, Quivira, var(--GW-sans-serif-font-stack);
}

a[data-link-icon-type*='mono'] {
    --link-icon-opacity-default: 0.7;
    --link-icon-font-default: Noto Emoji, Quivira, var(--GW-monospaced-font-stack);
}

a[data-link-icon-type*='bold'] {
    --link-icon-font-weight-default: 700;
}

a[data-link-icon-type*='italic'] {
    --link-icon-size-default: 0.80em;
    --link-icon-offset-x-default: 0.10em;
    --link-icon-font-style-default: italic;
}

a[data-link-icon-type*='overline'] {
    --link-icon-offset-y-default: 0.20em;
    --link-icon-text-decoration-default: overline;
}

/*  Triple-letter ‘tri’ initials: too few for quad, but too big for regular
    font size/offset. Common with TLA orgs.
 */
a[data-link-icon-type*='tri'] {
    --link-icon-size-default: 0.65em;
    --link-icon-offset-x-default: 0.20em;
    --link-icon-offset-y-default: 0.40em;
    --link-icon-opacity-default: 0.90;
}

/*  Tri-letter icons in a sans face.
 */
a[data-link-icon-type*='tri'][data-link-icon-type*='mono'] {
    /*  Nothing, for now. */
}

/*  Tri-letter icons in a monospaced face.
 */
a[data-link-icon-type*='tri'][data-link-icon-type*='mono'] {
    --link-icon-opacity-default: 0.7;
}

/*  Quad-letter ‘quad’ square icons.

    NOTE: Currently unused, due to SVG rendering of quad icons.
        —SA 2025-02-25
 */
a[data-link-icon-type*='text'][data-link-icon-type*='quad'] .link-icon-hook::after {
    text-indent: 0;
    overflow-wrap: break-word;
    display: inline-block;
    text-align: center;
    left: 0;
    bottom: 0.3em;
    font-size: 0.52em;
    font-weight: bold;
    letter-spacing: 0.05em;
    line-height: 0.85;
    width: 1.7em;
    opacity: 0.83;
}

/*  Quad-letter square icons in a sans face.

    NOTE: Currently unused, due to SVG rendering of quad icons.
        —SA 2025-02-25
    */
a[data-link-icon-type*='text'][data-link-icon-type*='quad'][data-link-icon-type*='sans'] .link-icon-hook::after {
    /*  Nothing, for now. */
}

/*  Quad-letter square icons in a monospaced face.

    NOTE: Currently unused, due to SVG rendering of quad icons.
        —SA 2025-02-25
    */
a[data-link-icon-type*='text'][data-link-icon-type*='quad'][data-link-icon-type*='mono'] .link-icon-hook::after {
    line-height: 0.90;
}

/******************************/
/*  Colored textual link icons.
 */
a[data-link-icon-type*='text'][data-link-icon-color]:hover .link-icon-hook::after {
    color: var(--link-icon-color-hover, currentColor);
    opacity: 1.0;
}

/*===========================================*/
/*= ICONS FOR CERTAIN LINK TYPES: BY TARGET =*/
/*===========================================*/

/*=----------------------------=*/
/*= Within-page (anchor) links =*/
/*=----------------------------=*/

a[data-link-icon='¶'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.25em;
    --link-icon-offset-y: 0.35em;
    --link-icon-opacity: 0.7;
    --link-icon-font-weight: 400;
}

a[data-link-icon='↑'],
a[data-link-icon='↓'] {
    --link-icon-size: 1em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.20em;
}

/*=------------------------------=*/
/*= Internal (within-site) links =*/
/*=------------------------------=*/

a[data-link-icon='gwern'] {
	--link-icon-size: 0.60em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.6;
}

/*=------------------------------------------=*/
/*= Cross-directory links on tag index pages =*/
/*=------------------------------------------=*/

a[data-link-icon='arrow-up-left'],
a[data-link-icon='arrow-down-right'],
a[data-link-icon='arrow-right'] {
    --link-icon-size: 0.65em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.15em;
    --link-icon-opacity: 0.8;
}

/*=-------------------------------=*/
/*= Textual per-domain link icons =*/
/*=-------------------------------=*/

/******************/
/*  Logotype icons.
    */

a[data-link-icon='𝛘'] { /* Arxiv */
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.60em;
    --link-icon-opacity: 0.7;
}

a[data-link-icon='❐'] { /* Booru */
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.15em;
}

a[data-link-icon='⧖'] { /* Longecity */
    --link-icon-size: 0.90em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.10em;
    --link-icon-opacity: 0.70;
}

a[data-link-icon='♡'] { /* Fandom/Wikia */
    --link-icon-offset-x: 0.05em;
    --link-icon-offset-x: 0.10em;
    --link-icon-opacity: 1.0;
}
a[data-link-icon='♡'] .link-icon-hook::after {
    text-shadow: 0 0 0 currentcolor;
}

a[data-link-icon='MS'] {
    --link-icon-size: 0.65em;
    --link-icon-offset-y: 0.35em;
}

a[data-link-icon='n'] { /* Nature */
    --link-icon-size: 0.90em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.30em;
    --link-icon-opacity: 0.85;
}

a[data-link-icon='s'] {
    --link-icon-size: 1.00em;
}

a[data-link-icon='Ss'] {
    --link-icon-offset-x: 0.15em;
}

a[data-link-icon='Vox'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.10em;
}

a[data-link-icon='ψ'] {
    --link-icon-offset-y: 0.40em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='ℵ'] { /* Unsong ALEF */
    --link-icon-size: 0.85em;
}

a[data-link-icon='▅▇▃'] {
    --link-icon-size: 0.50em;
    --link-icon-offset-x: 0.25em;
    --link-icon-opacity: 0.7;
}
a[data-link-icon='▅▇▃'] .link-icon-hook::after {
    margin-right: 0.15em;
}

a[data-link-icon='▲'] { /* Melting Asphalt (Kevin Simler) */
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='▽'] { /* The Verge */
    --link-icon-size: 0.90em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.15em;
}

a[data-link-icon='∇'] { /* The Gradient.pub */
    --link-icon-size: 0.85em;
    --link-icon-opacity: 0.7;
}

a[data-link-icon='✉'] { /* ENVELOPE / letter / \9993 */
    --link-icon-size: 1.10em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.05em;
    --link-icon-opacity: 0.75;
    --link-icon-font-weight: 700;
}

a[data-link-icon='M𝐑'] {
    --link-icon-opacity: 0.75;
}

a[data-link-icon='𝐁'] {
    --link-icon-opacity: 0.77;
}

a[data-link-icon='𝐌'] {
    --link-icon-opacity: 0.7;
}

a[data-link-icon='𝛌'] {
    --link-icon-size: 1.00em;
    --link-icon-opacity: 0.6;
}

a[data-link-icon='𝛘'] {
    --link-icon-offset-y: 0.40em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='෴'] { /* Moustache / antilop.cc */
    --link-icon-offset-y: 0.50em;
}

a[data-link-icon='🤖'] { /* ROBOT FACE U+1F916 beepb00p.xyz */
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.30em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='GPP'] { /* Game Programming Patterns */
    --link-icon-size: 0.60em;
    --link-icon-offset-y: 0.60em;
}

a[data-link-icon='⛨'] { /* Cambridge */
    --link-icon-size: 1.00em;
    --link-icon-offset-x: 0.10em; /* NOTE: this may be too much for Chrome, but the FF needs it */
    --link-icon-offset-y: 0.05em;
    --link-icon-font-weight: 400;
}

a[data-link-icon='𝒩𝒴'] { /* nymag.com New York Magazine */
    --link-icon-opacity: 0.7;
}

a[data-link-icon='𝕳'] { /* NZ Herald */
    --link-icon-offset-y: 0.40em;
    --link-icon-opacity: 0.7;
}

a[data-link-icon='𝔐'] { /* Daily Mail */
    --link-icon-size: 0.75em;
}

a[data-link-icon='𝛌'] { /* Haskell */
    --link-icon-size: 0.85em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
}

a[data-link-icon='𝓝'] { /* Nintil */
    --link-icon-size: 0.85em;
    --link-icon-opacity: 0.9;
}

a[data-link-icon='𝓥'] { /* Variety */
    --link-icon-size: 0.85em;
    --link-icon-opacity: 0.9;
}

a[data-link-icon='ρ'] { /* Project Rho */
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.50em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='ℰ'] { /* Esquire */
    --link-icon-size: 0.8em;
    --link-icon-opacity: 0.9;
}

a[data-link-icon='𝓡 𝐒'] { /* Rolling Stone */
    --link-icon-size: 0.65em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='≈'] { /* similar-links (ALMOST EQUAL TO) */
    --link-icon-size: 0.95em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='♔'] { /* WBW / Wait But Why */
    --link-icon-size: 1em;
    --link-icon-opacity: 0.75;
    --link-icon-offset-y: 0.20em;
}

a[data-link-icon='☀'] { /* Low Tech Magazine */
    --link-icon-size: 0.90em;
    --link-icon-offset-y: 0.15em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='🖋'] { /* NovelAI */
    --link-icon-size: 0.65em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.35em;
}

a[data-link-icon='𝕆'] { /* Outside Online */
    --link-icon-size: 0.85em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.15em;
}

a[data-link-icon='☯'] { /* Touhou Wiki */
    --link-icon-size: 0.85em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
}

a[data-link-icon='jt'] { /* Japan Times */
    --link-icon-offset-x: 0.20em;
}


/***********************************/
/*  Quad-letter ‘quad’ square icons.
 */

a[data-link-icon='VICE'] {
    --link-icon-offset-x: 0.10em;
}

/*=---------------------------------=*/
/*= Graphical per-domain link icons =*/
/*=---------------------------------=*/

a[data-link-icon='alcor'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='alphabet'] {
    --link-icon-size: 0.60em;
    --link-icon-opacity: 0.6;
}

a[data-link-icon='amazon'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='anthropic'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.10em;
}

a[data-link-icon='apple'] {
    --link-icon-size: 0.60em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='arrows-pointing-inwards-to-dot'] { /* bl backlinks 'implosion' */
    --link-icon-size: 0.85em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='atlas-obscura'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.85;
}

a[data-link-icon='audio-waveform-lines'] { /* Radiolab */
    --link-icon-size: 0.60em;
    --link-icon-offset-y: 0.30em;
}

a[data-link-icon='bibliography'] { /* lb link-bibliography 'table of contents' */
    --link-icon-size: 0.60em;
    --link-icon-offset-y: 0.10em;
}

a[data-link-icon='bitcoin'] {
    --link-icon-size: 0.55em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='cbs'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='chi-dna'] {
    --link-icon-size: 0.58em;
    --link-icon-offset-x: 0.15em;
    --link-icon-opacity: 0.85;
}

a[data-link-icon='chicago-tribune'] { /* fraktur C */
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='cochrane-collaboration'] {
    --link-icon-size: 0.75em;
    --link-icon-opacity: 0.7;
}

a[data-link-icon='connected-papers'] {
    --link-icon-size: 0.60em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.30em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='creative-commons'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.8;
}

a[data-link-icon='deepmind'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.7;
}

a[data-link-icon='deepseek'] {
    --link-icon-size: 0.8em;
}

a[data-link-icon='distillpub'] {
    --link-icon-size: 0.45em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='dropbox'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='econlib'] { /* torch */
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.30em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='eleutherai'] {
    --link-icon-size: 0.85em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.15em;
    --link-icon-opacity: 0.85;
}

a[data-link-icon='emacs'] {
    --link-icon-opacity: 0.65;
}

a[data-link-icon='erowid'] {
    --link-icon-size: 0.95em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.9;
}

a[data-link-icon='facebook'] {
    --link-icon-size: 0.50em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='github'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='google-scholar'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.10em;
    --link-icon-opacity: 0.60;
}

a[data-link-icon='hacker-news'] { /* HN / YC / Y Combinator */
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.60;
}

a[data-link-icon='hoover-institution'] {
    --link-icon-size: 0.50em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.45em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='internet-archive'] {
    --link-icon-size: 0.55em;
    --link-icon-opacity: 0.6;
}

a[data-link-icon='laion'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='maggie-appleton'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='magnifying-glass'] { /* eye glass for Retraction Watch; cf. .icon-magnifying-glass */
    --link-icon-size: 0.75em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='mega'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.50;
}

a[data-link-icon='metaculus'] {
    --link-icon-size: 0.55em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='midjourney'] {
    --link-icon-size: 0.90em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='miri'] {
    --link-icon-size: 1.05em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='nasa'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-y: 0.30em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='nautilus'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='new-york-times'] {
    --link-icon-size: 0.65em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.15em;
    --link-icon-opacity: 0.6;
}

a[data-link-icon='nlm-ncbi'] {
    --link-icon-size: 0.60em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.9;
}

a[data-link-icon='openai'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.15em;
    --link-icon-opacity: 0.7;
}

a[data-link-icon='open-philanthropy'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.95;
}

a[data-link-icon='patreon'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.15em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='plos'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.10em;
    --link-icon-opacity: 0.80;
}

a[data-link-icon='quanta'] {
    --link-icon-size: 0.90em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.9;
}

a[data-link-icon='raven'] { /* LibGen/Sci-Hub */
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.10em;
}

a[data-link-icon='reddit'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='scholarpedia'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
}

a[data-link-icon='springerlink'] { /* WHITE CHESS KNIGHT '♘'-style SVG icon */
    --link-icon-size: 0.85em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.4em;
    --link-icon-opacity: 0.70;
}

a[data-link-icon='stack-exchange'] {
    --link-icon-size: 0.60em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='stat-news'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.30em;
    --link-icon-opacity: 1.0;
}

a[data-link-icon='substack'] {
    --link-icon-size: 0.55em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='tensorflow'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.6;
}

a[data-link-icon='tex'] { /* CTAN etc; TeX logotype (actually uses Source Serif) */
    --link-icon-size: 1.10em;
    --link-icon-offset-x: 0.05em;
    --link-icon-opacity: 0.85;
}

a[data-link-icon='tiktok'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.15em;
}

a[data-link-icon='the-guardian'] {
    --link-icon-size: 0.80em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='the-new-yorker'] {
    --link-icon-size: 0.65em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='the-pirate-bay'] {
    --link-icon-size: 0.90em;
    --link-icon-offset-x: 0.20em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='the-telegraph'] {
    --link-icon-size: 0.60em;
    --link-icon-offset-x: 0.10em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.75;
}

a[data-link-icon='tumblr'] {
    --link-icon-size: 0.47em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.45;
}

a[data-link-icon='twitter'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='upton-tea'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.05em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='video'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.40em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='wandb'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.65;
}

a[data-link-icon='washington-post'] {
    --link-icon-size: 0.85em;
    --link-icon-offset-x: 0.20em;
    --link-icon-offset-y: 0.25em;
    --link-icon-opacity: 0.85;
}

a[data-link-icon='wired'] {
    --link-icon-size: 0.75em;
    --link-icon-offset-x: 0.15em;
    --link-icon-offset-y: 0.20em;
    --link-icon-opacity: 0.55;
}

a[data-link-icon='youtube'] {
    --link-icon-size: 0.88em;
    --link-icon-offset-x: 0.15em;
    --link-icon-opacity: 0.5;
}

a[data-link-icon='yud'] {
    --link-icon-offset-y: 0.25em;
}

a[data-link-icon='deepseek'] {
    --link-icon-size: 1.05em;
    --link-icon-offset-x: 0.10em;
}

a[data-link-icon='✤'] { /* Ganbreeder/Artbreeder */
    --link-icon-opacity: 0.60;
}

a[data-link-icon='☤'] { /* Karolinska */
    --link-icon-opacity: 0.50;
}

a[data-link-icon='sourceforge'] {
    --link-icon-size: 0.8em;
    --link-icon-offset-x: 0.10em;
}

a[data-link-icon='worldcat'] {
    --link-icon-size: 0.70em;
    --link-icon-offset-x: 0.10em;
}

/* Similar to the 'W' Wikipedia icon, we shrink the PDF icon on desktop aggressively because there are *so* many. However, they are then illegible on a physical smartphone screen held normally (even if they look OK in a desktop browser's simulation). So we need to have a media-query conditional here. */
@media not all and (max-width: 649px) {
	a[data-link-icon='pdf'] { /* Adobe Acrobat */
	    --link-icon-size: 0.45em;
	    --link-icon-offset-x: 0.10em;
	    --link-icon-opacity: 0.50;
	}
}
@media all and (max-width: 649px) {
    a[data-link-icon='pdf'] {
        --link-icon-size-default: 0.6em;
        --link-icon-offset-y-default: 0.25em;
    }
}

/*=------------------=*/
/*= Wikipedia links. =*/
/*=------------------=*/

a[data-link-icon='wikipedia'] {
    --link-icon-size: 0.60em;
    --link-icon-offset-x: 0.12em;
    --link-icon-offset-y: -0.05em;
    --link-icon-opacity: 0.65;
}
