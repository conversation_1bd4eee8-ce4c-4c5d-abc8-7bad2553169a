map_hash_bucket_size 128; # https://nginx.org/en/docs/http/ngx_http_map_module.html
map $request_uri $new_uri {
    ## library of custom redirects:
    ### first, manual moves, updates, mistake fixes:
    include /home/<USER>/gwern.net/static/redirect/nginx.conf;
    ### then hide garbage from logs:
    include /home/<USER>/gwern.net/static/redirect/nginx-broken.conf;
}

## Rewrite https://www.gwern.net → https://gwern.net
server{
    #redirect www to non-www, and all other sub-domains as well (I sometimes get spurious traffic to `direct.gwern.net` etc)
    listen 80;
    listen 443 ssl;
    server_name *.gwern.net;

    ssl_certificate /home/<USER>/ssl/cloudflare.cert;
    ssl_certificate_key /home/<USER>/ssl/cloudflare.key;
    return 301 https://gwern.net$request_uri;
}

server {
     listen 80  default_server;
     listen 443 default_server ssl;

     server_name gwern.net;

     # block anyone trying to access by direct IP; this can be useful for debugging, but seems to be badly abused by bots/scanners, clogging the logs with spurious errors. So we kick them out at the start. (All legitimate users should have been redirected to the 'gwern.net' domain by this point.)
     if ( $host != "gwern.net" ){
        return 444; # 444="CONNECTION CLOSED WITHOUT RESPONSE"
    }

     ssl_certificate      /home/<USER>/ssl/cloudflare.cert;
     ssl_certificate_key  /home/<USER>/ssl/cloudflare.key;

     root /home/<USER>/gwern.net;
     index index;

     error_page 404 /404;

     default_type text/html;

     location / {

        limit_except GET { deny all; } # block scanners/botnets from spamming POST and other garbage

        location ~ ^/(confidential|private|secret|metadata/annotation/link-bibliography/%2Fconfidential|metadata/annotation/link-bibliography/%2Fprivate|metadata/annotation/link-bibliography/%2Fsecret)/ {
                 auth_basic "Non-Public Pages";
                 auth_basic_user_file /etc/nginx/.htpasswd; }

        # for linkArchive.sh/LinkArchive.hs: ensure that no crawlers pick up my mirrors, to reduce DMCA risk.
        # Prevent crawlers from indexing specific directories to reduce DMCA risk
        # and repeat robots.txt's major rules, because Google Search appears to ignore it:
        location ~ ^/(doc/link-bibliography/|metadata/|404) {
          add_header X-Robots-Tag "none, noindex, nosnippet, noarchive, nocache"; }

        # support client-side search of annotations by ID or URL; the JS in ref-include.html reads the URL path to look up the ID → URL mapping & load the corresponding annotation
        location /ref/ {
            rewrite ^/ref/.*$ /placeholder break;
        }

        # Specific configuration for /doc/www/: WWW snapshots may accidentally match SSI includes, breaking them badly, so disable SSI just for those:
        location /doc/www/ { ssi off;
          # duplicate the no-scraping header because otherwise the previous regexp match would override this and you'd get SSI xor headers on /doc/www/*:
          add_header X-Robots-Tag "none, noindex, nosnippet, noarchive, nocache"; }

        ## support basic MIME types
        include  /etc/nginx/mime.types;
        ## support non-basic MIME types...
        types {
          text/markdown md;
          application/x-maff maff;
          text/plain conf;
          text/csv csv;
          text/x-adobe-acrobat-drm ebt;
          application/epub+zip epub;
          text/x-haskell hs;
          text/html html5;
          application/msaccess mdb;
          message/rfc822 mht;
          application/vnd.oasis.opendocument.text odt;
          application/vnd.oasis.opendocument.spreadsheet ods;
          application/vnd.oasis.opendocument.presentation odp;
          text/x-opml opml;
          text/x-patch patch;
          text/x-diff  diff;
          text/x-php php;
          text/x-python py;
          text/x-r R;
          text/x-c c;
          text/plain tmpl; # PMWiki template
          application/vnd.rn-realmedia rm;
          text/plain sh;
          text/plain bash;
          application/wasm wasm;
          application/x-tar tar;
          application/font-sfnt ttf;
          image/x-xcf xcf;
          application/x-xz xz;
          audio/wav wav;
          video/mp4 mkv;
          application/font-sfnt otf;
          text/x-gtx gtx; # Gwern.net custom format; see </static/build/GTX.hs>
          application/x-par2 par2;
          application/octet-stream dat elc idx t7;
          application/x-hdf5 h5;
          application/jsonl jsonl;
          application/x-git-packed-objects pack; # 'octet-stream'?
          application/octet-stream mtimes rev;
          image/vnd.adobe.photoshop psd;
          application/vnd.sqlite3 sqlite; # Or 'application/x-sqlite3'
          application/vnd.sqlite3 sqlite3;
          font/woff2 woff2;
          text/x-emacs-lisp el;
          }

        # force text files into UTF-8 to avoid 'mojibake':
        charset utf-8;
        source_charset utf-8;
        charset_types htm text/css text/csv text/markdown text/plain text/x-diff text/x-haskell text/x-opml text/x-patch text/x-php text/x-r text/x-shellscript text/x-gtx;

        ## somewhat aggressive caching:
        add_header Cache-Control "max-age=77760000, public, immutable";

        ## RIP: (see </static/nginx/memoriam.sh> for details on generated file)
        include /etc/nginx/conf.d/memoriam.conf;

        ## allow directory browsing, particularly useful for /doc/*
        autoindex on;

        # server-side includes <https://en.wikipedia.org/wiki/Server_Side_Includes>:
        ## used in templating for the includes to avoid the need for full site rebuilds. Can just rsync up & expire cache.
        ssi on; # http://nginx.org/en/docs/http/ngx_http_ssi_module.html default: applies to all files served with 'text/html' MIME type

        # begin redirect rewrites: for cases where a more powerful regexp match-and-replace is required

        ## stripping query parameters:
        rewrite ^/static/css/fonts\.css.*$ /static/css/fonts-VERSIONED.css last;

        rewrite ^/static/js/head\.js$    /static/js/head-GENERATED.js last;
        rewrite ^/static/js/script\.js$  /static/js/script-GENERATED.js last;
        rewrite ^/static/css/head\.css$  /static/css/head-VERSIONED.css last;
        rewrite ^/static/css/style\.css$ /static/css/style-VERSIONED.css last;

        rewrite ^/(?<old>[a-rt-z][a-su-z][b-z][a-su-z][a-hj-z][a-bd-z].*)\?.+$ /$old permanent; # exclude /static/ because of file versioning
        rewrite ^/(?<original>.*)\?chat$ /$original permanent;
        rewrite ^/(?<original>.*)\?feedback.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?[0-9]$ /$original permanent;
        rewrite ^/(?<original>.*)\?[0-9]$ /$original permanent;
        rewrite ^/(?<original>.*)\?fbclid\=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?utm.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?ref.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?revision=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?amp\;\?amp\;.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?usg=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?fbid.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?fbclid.*$ /$original permanent;
        rewrite ^/(?<original>.*)\?revisi.*$ /$original permanent;
        rewrite ^(?<original>.*)\?.*callback=.*from=.*$ /$original permanent;
        rewrite ^(?<original>.*)\?service=.*$ /$original permanent;
        rewrite ^(?<original>.*)\?relatedposts_hit=.*$ /$original permanent;
        rewrite ^(?<original>.*)\?ref.*$ /$original permanent;
        rewrite ^(?<original>.*)\?code.*$ /$original permanent;
        rewrite ^(?<original>.*)\?backlink.*$ /$original permanent;
        rewrite ^(?<original>.*)\!$ /$original permanent;

        rewrite ^(?<original>.*)ç$ /$original permanent;
        rewrite ^/docs/(?<old>.*)$ /doc/$old permanent;
        rewrite ^/notes/(?<old>.*)$ /note/$old permanent;
        rewrite ^/images/(?<old>.*)$ /image/$old permanent;
        rewrite ^/reviews/(?<old>.*)$ /review/$old permanent;
        rewrite ^/notes/(?<old>.*)$ /note/$old permanent;
        rewrite ^/nootropics/(?<old>.*)$ /nootropic/$old permanent;
        rewrite ^/doc/tominaga-nakamoto/(?<old>.*)$ /doc/japan/history/tominaga-nakamoto/$old permanent;
        rewrite ^/doc/tcs/(?<old>.*)$ /doc/psychology/neuroscience/tcs/$old permanent;
        rewrite ^/doc/ai/nn/transformer/clip/samplse/(?<old>.*)$ /doc/ai/nn/transformer/clip/sample/$old permanent;
        rewrite ^/doc/biology/boogers/(?<old>.*)$ /doc/biology/booger/$old permanent;
        rewrite ^/doc/darknet-markets/dnm-archives/(?<old>.*)$ /doc/darknet-market/dnm-archive/$old permanent;
        rewrite ^/doc/darknet-markets/(?<old>.*)$ /doc/darknet-market/$old permanent;
        rewrite ^/doc/genetics/heritable/rare-variants/(?<old>.*)$ /doc/genetics/heritable/rare/$old permanent;
        rewrite ^/image/design/sidenotes/(?<old>.*)$ /image/design/sidenote/$old permanent;
        rewrite ^/image/nootropics/(?<old>.*)$ /image/nootropic/$old permanent;
        rewrite ^/image/traffic/ads/(?<old>.*)$ /image/traffic/ad/$old permanent;
        rewrite ^/metadata/annotations/(?<old>.*)$ /metadata/annotation/$old permanent;
        rewrite ^/metadata/annotations/backlinks/(?<old>.*)$ /metadata/annotation/backlink/$old permanent;
        rewrite ^/metadata/annotations/similars/(?<old>.*)$ /metadata/annotation/similar/$old permanent;
        rewrite ^/static/img/icons/(?<old>.*)$ /static/img/icon/$old permanent;
        rewrite ^/static/img/scrollbars/(?<old>.*)$ /static/img/scrollbar/$old permanent;
        rewrite ^/static/includes/(?<old>.*)$ /static/include/$old permanent;
        rewrite ^/static/redirects/(?<old>.*)$ /static/redirect/$old permanent;
        rewrite ^/static/templates/(?<old>.*)$ /static/template/$old permanent;

        ## replace all spaces with hyphens:
        rewrite ^(.*)(\s|%20)(.*)$ $1-$3 permanent;
        ## fix a bunch of very obxious crawlers:
        rewrite ^/100-y/ALL/score/1/(?<original>.*) /$original permanent;
        rewrite ^/2017/11/20/(?<original>.*) /$original permanent;
        rewrite ^/BINARY/(?<original>.*) /$original permanent;
        rewrite ^/Prediction-markets/(?<original>.*) /$original permanent;
        rewrite ^/Red/feed/(?<original>.*) /$original permanent;
        rewrite ^/Yoga.*/(?<original>.*) /$original permanent;
        rewrite ^/[a-z]/(?<original>.*) /$original permanent;
        rewrite ^/about/(?<original>.*) /$original permanent;
        rewrite ^/advanced-search/(?<original>.*) /$original permanent;
        rewrite ^/all-essays/(?<original>.*) /$original permanent;
        rewrite ^/alternates/s615b/(?<original>.*) /$original permanent;
        rewrite ^/amp/(?<original>.*) /$original permanent;
        rewrite ^/b/(?<original>.*) /$original permanent;
        rewrite ^/blog/chasing-10x-leveraging-a-poor-memory-in-software-engineering/(?<original>.*) /$original permanent;
        rewrite ^/blog/everything-i-know-strategies-tips-and-tricks-for-spaced-repetition-anki/(?<original>.*) /$original permanent;
        rewrite ^/border-wall/(?<original>.*) /$original permanent;
        rewrite ^/card/(?<original>.*) /$original permanent;
        rewrite ^/cart/(?<original>.*) /$original permanent;
        rewrite ^/choice/(?<original>.*) /$original permanent;
        rewrite ^/component/(?<original>.*) /$original permanent;
        rewrite ^/configure/(?<original>.*) /$original permanent;
        rewrite ^/contests/(?<original>.*) /$original permanent;
        rewrite ^/doc/2010-crc/(?<original>.*) /$original permanent;
        rewrite ^/doc/2020-crc/(?<original>.*) /$original permanent;
        rewrite ^/doc/docs/(?<original>.*) /$original permanent;
        rewrite ^/doc/images/(?<original>.*) /doc/$original permanent;
        rewrite ^/image/images/(?<original>.*) /image/$original permanent;
        rewrite ^/doc/iq/docs/(?<original>.*) /doc/$original permanent;
        rewrite ^/doc/tags/(?<original>.*) /tags/$original permanent;
        rewrite ^/doc/www/au.news.yahoo.com/Chrome/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/au.news.yahoo.com/Safari/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/brnensky.denik.cz/edge/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/play\.google\.com/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/play\.google\.com/\+/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/play\.google\.com/\_/js/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/play\.google\.com/\_/ss/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.adressa.no/Trident/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.belfastlive.co.uk/offline/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.dailymail.co.uk\/android-app\:\/\/com.dailymail.online/dailymail/article/2825778/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.sueddeutsche.de/edge/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.upi.com/YWRzLmxmc3RtZWRpYS5jb20vZ2V0YWQ/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/yuki-onna.livejournal.com/flymeango.com/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/yuki-onna.livejournal.com/www.arte.tv/en/(?<original>.*) /$original permanent;
        rewrite ^/doc/www\/free\.law/OPR/(?<original>.*) /$original permanent;
        rewrite ^/event/(?<original>.*) /$original permanent;
        rewrite ^/feature/(?<original>.*) /$original permanent;
        rewrite ^/find_v2/(?<original>.*) /$original permanent;
        rewrite ^/fonts/(?<original>.*) /$original permanent;
        rewrite ^/foo/(?<original>.*) /$original permanent;
        rewrite ^/forum/(?<original>.*) /$original permanent;
        rewrite ^/help/(?<original>.*) /help permanent;
        rewrite ^/homepage-test/(?<original>.*) /$original permanent;
        rewrite ^/in-depth/(?<original>.*) /$original permanent;
        rewrite ^/initiatives/(?<original>.*) /$original permanent;
        rewrite ^/live-blog/(?<original>.*) /$original permanent;
        rewrite ^/login/(?<original>.*) /$original permanent;
        rewrite ^/m/(?<original>.*) /$original permanent;
        rewrite ^/my-account/(?<original>.*) /$original permanent;
        rewrite ^/opinion/(?<original>.*) /$original permanent;
        rewrite ^/p/(?<original>.*) /$original permanent;
        rewrite ^/partner_content/(?<original>.*) /$original permanent;
        rewrite ^/performer/(?<original>.*) /$original permanent;
        rewrite ^/poisoned-cities/(?<original>.*) /$original permanent;
        rewrite ^/privacy/(?<original>.*) /$original permanent;
        rewrite ^/products/(?<original>.*) /$original permanent;
        rewrite ^/satellites/bible/(?<original>.*) /$original permanent;
        rewrite ^/source/I0.*/(?<original>.*) /$original permanent;
        rewrite ^/source/LP.*/(?<original>.*) /$original permanent;
        rewrite ^/soylent/library/death/suicide/famous/(?<original>.*) /$original permanent;
        rewrite ^/soylent/library/travel/cities/nyc/(?<original>.*) /$original permanent;
        rewrite ^/sponsored/(?<original>.*) /$original permanent;
        rewrite ^/sponsored_sections/(?<original>.*) /$original permanent;
        rewrite ^/static/docs/(?<original>.*) /doc/$original permanent;
        rewrite ^/store/configure/xbox-design-lab/(?<original>.*) /$original permanent;
        rewrite ^/stylesheets/(?<original>.*) /$original permanent;
        rewrite ^/terms/(?<original>.*) /$original permanent;
        rewrite ^/trust/(?<original>.*) /$original permanent;
        rewrite ^/videos/(?<original>.*) /$original permanent;
        rewrite ^/wp-content/(?<original>.*) /$original permanent;

        rewrite ^/locales/(?<original>.*) /$original permanent;
        rewrite ^/merchants/(?<original>.*) /$original permanent;
        rewrite ^/strong-opinions-weakly-held/(?<original>.*) /$original permanent;
        rewrite ^/uris/(?<original>.*) /$original permanent;
        rewrite ^/_uris/(?<original>.*) /$original permanent;
        rewrite ^/instances/(?<original>.*) /$original permanent;
        rewrite ^/_pages/(?<original>.*) /$original permanent;
        rewrite ^/partner/(?<original>.*) /$original permanent;
        rewrite ^/services/(?<original>.*) /$original permanent;
        rewrite ^/v/(?<original>.*) /$original permanent;
        rewrite ^/v2/(?<original>.*) /$original permanent;
        rewrite ^/articles/(?<original>.*) /$original permanent;
        rewrite ^/\/(?<original>.*)$ /$original permanent;
        rewrite ^/doc/sr/\^/live/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/old.reddit.com/\^/live/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/market.android.com/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/market.android.com/Edg/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/market.android.com/_/js/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/market.android.com/_/ss/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/market.android.com/type.googleapis.com/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/market.android.com/\+/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.google.com/\+/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.google.com/Edg/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.lenovo.com/Yoga.*/p/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.supermemo.com/Trident/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.thedenverchannel.com/Trident/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.baltimoresun.com/Trident/(?<original>.*) /$original permanent;
        rewrite ^/articleComments/(?<original>.*) /$original permanent;
        rewrite ^/43010785/wallanews/innerpages/(?<original>.*) /$original permanent;
        rewrite ^/bucket/3067d498bc60d7bdfa033571e782efbcf5b28c5c/(?<original>.*) /$original permanent;
        rewrite ^/connecticut/norwalk/police-fire/norwalk-police-bust-major-marijuana-operation-after-finding-pot-in-mail/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/au.news.yahoo.com/Chromium/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/drugs.globalincidentmap.com/(?<original>.*) /$original permanent;
        rewrite ^/weather/(?<original>.*) /$original permanent;
        rewrite ^/connecticut/norwalk/(?<original>.*) /$original permanent;
        rewrite ^/2017/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/groups.google.ca/+/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/groups.google.*/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/CriOS/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/Edge/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/Firefox/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/IEMobile/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/Silk/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/Chrome/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/Version/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/blogs.msdn.microsoft.com/WebKit/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/bgr.com/Version/(?<original>.*) /$original permanent;
        rewrite ^/community-static/8891523-register/(?<original>.*) /$original permanent;
        rewrite ^/community-static/4805458-metroland-media-group-commenting-guidelines/(?<original>.*) /$original permanent;
        rewrite ^/community-static/2545471-mississauga-about-us/(?<original>.*) /$original permanent;
        rewrite ^/2015/04/21/sane-finds-100-hits-of-lsd-during-petoskey-search/(?<original>.*) /$original permanent;
        rewrite ^/article/dn8317-meditation-builds-up-the-brain/(?<original>.*) /$original permanent;
        rewrite ^/app.php/mentionloc/(?<original>.*) /$original permanent;
        rewrite ^/ark:/67531/metadc.*/(?<original>.*) /$original permanent;
        rewrite ^/article/dn14249-interview-its-a-dogs-life-again/(?<original>.*) /$original permanent;
        rewrite ^/datapreview/(?<original>.*) /$original permanent;
        rewrite ^/display/heraldsun.com.au/(?<original>.*) /$original permanent;
        rewrite ^/quot./(?<original>.*) /$original permanent;
        rewrite ^/quot.(?<original>.*) $original permanent;
        rewrite ^/wcsstore/PetcoStore/(?<original>.*) /$original permanent;
        rewrite ^/transcript/(?<original>.*) /$original permanent;
        rewrite ^/static-assets/(?<original>.*) /$original permanent;
        rewrite ^/source/.*/(?<original>.*) /$original permanent;
        rewrite ^/region/(?<original>.*) /$original permanent;
        rewrite ^/profile/papers/(?<original>.*) /$original permanent;
        rewrite ^/profile/collections/(?<original>.*) /$original permanent;
        rewrite ^/profile/(?<original>.*) /$original permanent;
        rewrite ^/money/(?<original>.*) /$original permanent;
        rewrite ^/reviews/soy-isoflavones_red-clover_black-cohosh_supplements/phytoestrogens/(?<original>.*) /$original permanent;
        rewrite ^/reviews/turmeric-curcumin-supplements-spice-review/turmeric/(?<original>.*) /$original permanent;
        rewrite ^/reviews/Vitamin_A_Retinol_Beta-Carotene_Cod_Liver_Oil/Vitamin_A/(?<original>.*) /$original permanent;
        rewrite ^/journal/revw.*/(?<original>.*) /$original permanent;
        rewrite ^/jfe/(?<original>.*) /$original permanent;
        rewrite ^/explore/partners/TAMS/browse/(?<original>.*) /$original permanent;
        rewrite ^/en/account/management/(?<original>.*) /$original permanent;
        rewrite ^/embed/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.petco.com/images/colors/color1/(?<original>.*) /$original permanent;
        rewrite ^/reviews/Wellbutrin_vs_Generic_Bupropion/Wellbutrin/(?<original>.*) /$original permanent;
        rewrite ^/review/reviews/(?<original>.*) /reviews/$original permanent;
        rewrite ^/reviewsfood-testing/.*/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.instagram.com/android-app.*/(?<original>.*) /$original permanent;
        rewrite ^/client_error/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/support.google.com/\+/(?<original>.*) /$original permanent;
        rewrite ^/article./.*/(?<original>.*) /$original permanent;
        rewrite ^/WebGraphics/(?<original>.*) /$original permanent;
        rewrite ^/subscribe-more/(?<original>.*) /$original permanent;
        rewrite ^/pt/home/<USER>/$original permanent;
        rewrite ^/soundcloud-tpa/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www3.bostonglobe.com/.*/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/www.allinea.com/.*/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/highqualityevidence.blogspot.com/.*/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/newcriterion.com/.*\.com/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/support.google.com/gm/(?<original>.*) /$original permanent;
        rewrite ^/subscribe/(?<original>.*) /$original permanent;
        rewrite ^/story/[0-9]+/(?<original>.*) /$original permanent;
        rewrite ^/static/bundles/(?<original>.*) /$original permanent;
        rewrite ^/pages/feedback/(?<original>.*) /$original permanent;
        rewrite ^/onward/notify/(?<original>.*) /$original permanent;
        rewrite ^/members/exclusives/(?<original>.*) /$original permanent;
        rewrite ^/embedded-video/(?<original>.*) /$original permanent;
        rewrite ^/elections/chatbot/(?<original>.*) /$original permanent;
        rewrite ^/iq-tests/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/external/languages-dist/smart/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/researchnews.osu.edu/player.vimeo.com/video/(?<original>.*) /$original permanent;
        rewrite ^/doc/www/support.google.com/Edg/(?<original>.*) /$original permanent;
        rewrite ^/outdoor-adventure/exploration/the-king-of-the-ferret-leggers/(?<original>.*) /$original permanent;
        rewrite ^/food-testing/laboratories/eurofins-food-integrity-and-innovation/(?<original>.*) /$original permanent;
        rewrite ^/.*/Trident/(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*)\$$ /$original permanent;
        rewrite ^/(?<original>.*)\)$ /$original permanent;
        rewrite ^/(?<original>.*)\)\)$ /$original permanent;
        rewrite ^/(?<original>.*)\($ /$original permanent;
        rewrite ^/(?<original>.*)\.$ /$original permanent;
        rewrite ^/(?<original>.*),$ /$original permanent;
        rewrite ^/(?<original>.*)_$ /$original permanent;
        rewrite ^/(?<original>.*)-$ /$original permanent;
        rewrite ^/(?<original>.*)\;$ /$original permanent;
        rewrite ^/(?<original>.*)\+$ /$original permanent;
        rewrite ^/article/.*(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*)\%26amp\;quot\; /$original permanent;
        rewrite ^/\%26amp\;quot\;(?<original>.*) /$original permanent;
        rewrite ^/.*\%26amp\;quot\;(?<original>.*) /$original permanent;
        rewrite ^/newsletter/2020/%26amp\;quot\;https\:\/\/.*/(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*)\.djvu$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)\.djvu.*$ /$original.pdf permanent; # ignore trailing junk in this one
        rewrite ^/(?<original>.*)/index/$ /$original/index permanent;
        rewrite ^/(?<original>.*)/.html/$ /$original.html permanent;
        rewrite ^/(?<original>.*)/.pdf/$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)/.pdff$ /$original.pdf permanent;

        ## rewrite mistaken newsletter URLs like '/newsletter/2019/7' to '/newsletter/2019/07':
        rewrite ^/newsletter/(?<originalYear>20[0-5][0-9])/(?<originalMonth>[0-9])$ /newsletter/$originalYear/0$originalMonth permanent;
        rewrite ^/.*/%c3%a2%c2%80%c2%98.*/(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*)-ORDER-BY-.*/ /$original permanent;
        rewrite ^/newsletter/2[0-9][0-9][0-9]/docs/www/(?<original>.*) /doc/www/$original permanent;
        rewrite ^/[a-zA-Z].*/static/js/(?<original>.*\.js)$ /static/js/$original permanent;
        rewrite ^/.*\|\.\/\/(?<original>.*) /$original permanent;
        rewrite ^/\%26amp\;quot\;.*/(?<original>.*) /$original permanent;
        rewrite ^/\%26amp\;.*quot\;.*/(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*)\&quot$ /$original permanent;
        rewrite ^/(?<original>.*)\&[a-z][a-z]=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\.pag$ /$original.md permanent;
        rewrite ^/\.\/doc/(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*)\=$ /$original permanent;
        # rewrite ^/(?<original>.*)\%[0-9]C.*$ /$original permanent;
        # rewrite ^/(?<original>.*)\%[0-9]D.*$ /$original permanent;
        # rewrite ^/(?<original>.*)\%[0-9]F.*$ /$original permanent;
        # rewrite ^/(?<original>.*)\%5D$ /$original permanent;
        rewrite ^/(?<original>.*)\[Aa]cessed$ /$original permanent;
        # rewrite ^/(?<original>.*)\%5C.*$ /$original permanent;
        # rewrite ^/(?<original>.*)%5C.*$ /$original permanent;
        # rewrite ^/(?<original>.*)\\$ /$original permanent;
        # rewrite "^(?<original>.*?)[\\%5C]+$" $scheme://$host$original permanent;
        rewrite ^/(?<original>.*)\|$ /$original permanent;
        rewrite ^/\/(?<original>.*)$ /$original permanent;
        rewrite ^/\/\/(?<original>.*)$ /$original permanent;
        rewrite ^/\/\/\/(?<original>.*)$ /$original permanent;
        rewrite ^/.*-https:\/\/www.gwern.net/(?<original>.*)$ /$original permanent;
        rewrite ^/blob/master/(?<original>.*)$ /$original permanent;
        rewrite ^/doc/.*/(?<original>docs/www/.*)$ /$original permanent;
        rewrite ^/does/(?<original>.*)$ /doc/$original permanent;
        rewrite ^/(?<original>doc/.*/[12].*\.pd)$ /$original\f permanent;
        rewrite ^/tag/.*/(?<original>.*)$ /tags/$original permanent;
        rewrite ^/(?<original1>.*)­(?<original2>.*)$ /$original1-$original2 permanent;
        rewrite ^/(?<original1>doc/.*)--(?<original2>.*pdf)$ /$original1-$original2 permanent; # wandb.ai likes double-dashes?
        rewrite ^/(?<original1>.*)—(?<original2>.*)$ /$original1-$original2 permanent;
        rewrite ^/doc/rotten.com/library/bio/crime/killer-moms/darlie-routier/(?<original>.*) /doc/rotten.com/library/bio/crime/criminals/darlie-routier/$original permanent;
        rewrite ^/doc/rotten.com/library/bio/authors/Richard_Scarry/(?<original>.*) /doc/rotten.com/library/bio/authors/richard-scarry/$original permanent;
        rewrite ^/doc/rotten.com/library/bio/crime/serial-killers/Fred_and_Rosemary_West/(?<original>.*) /doc/rotten.com/library/bio/crime/serial-killers/wests/$original permanent;
        rewrite ^/dos/(?<original>.*) /doc/$original permanent;
        rewrite ^/GPT-2-Folk-Music-\%C2\%B7-Gwern\.net_files/(?<original>.*) /doc/ai/music/$original permanent;
        rewrite ^/(?<original1>.*)/%E2%80%8B(?<original2>.*)$ /$original1/$original2 permanent;
        rewrite ^/(?<original1>.*)%E2%80%8B(?<original2>.*)$ /$original1$original2 permanent;
        rewrite ^/index/(?<original>.*)$ /$original permanent;
        rewrite ^/(?<original>.*)\~$ /$original permanent;
        rewrite ^/(?<original>.*)\:$ /$original permanent;
        rewrite ^/(?<original>.*),page$ /$original.md permanent;
        rewrite ^/(?<original>.*)\.markdown$ /$original.md permanent;
        rewrite ^/(?<original>.*)\.source$ /$original.md permanent;
        rewrite ^/(?<original>.*)\<br\>$ $original.md permanent;
        rewrite ^/-(?<original>.*)$ /$original permanent;
        rewrite ^/(?<original>.*)\.tmp$ /$original permanent;
        rewrite ^/(?<original>.*)\.pd\/f$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)https:\/\/www\.gwern\.net\/.*$ /$original permanent;
        rewrite ^/(?<original>.*)&imgrefurl.*$ /$original permanent;
        rewrite ^/(?<original>.*)\]$ /$original permanent;
        rewrite ^/(?<original>.*)\<.*$ /$original permanent;
        rewrite ^/(?<original>.*)\>.*$ /$original permanent;
        rewrite ^/(?<original>doc/.*/)[0-9][0-9][0-9][0-9]$ /$original permanent;
        rewrite ^/(?<original>doc/.*/)[0-9][0-9][0-9][0-9]-$ /$original permanent;
        rewrite ^/doc/www.rotten.com/(?<original>.*)$ /doc/rotten.com/$original permanent;
        rewrite ^/(?<original>doc/.*)\.pd$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)\.p$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)\/pdf.*$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)\.pd$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)\.p$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)\.\.\.$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)…$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*\.pdf)…$ /$original permanent;
        rewrite ^/(?<original>.*)\*\*$ /$original permanent;
        rewrite ^/(?<original>.*)\*$ /$original permanent;
        rewrite ^/(?<original>.*)\&lang=en$ /$original permanent;
        rewrite ^/(?<original>.*)\&sa=.*$ /$original permanent;
        rewrite ^/doc/eva/doc/eva/(?<original>.*)$ /doc/eva/$original permanent;
        rewrite ^/(?<original>.*)\&lang=en$ /$original permanent;
        rewrite ^/image%25252F(?<original>.*)$ /image/$original permanent;
        rewrite ^/image%25252Fthumbnails%25252Fwikipedia%25252F(?<original>.*)$ /image/thumbnails/wikipedia/$original permanent;
        rewrite ^/(?<original>doc/.*\.pdf)The$ /$original permanent;
        rewrite ^/(?<original>.*)\&spec\=ft100x75$ /$original permanent;
        rewrite ^/(?<original>.*)\'A\=0$ /$original permanent;
        rewrite ^/(?<original>.*)\&usg=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&$ /$original permanent;
        rewrite ^/(?<original>.*)\+\&cd\=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&ved\=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&xid\=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&fbid.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&fbclid.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&key=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&id\=.*$ /$original permanent;
        rewrite ^/(?<original>.*)\%E2\%80\%99$ /$original permanent;
        rewrite ^/(?<original>.*)\%60$ /$original permanent;
        rewrite ^/(?<original>.*)// /$original/ permanent;
        # match surprising pervasive errors of appending an entire URL, like '/doc/culture/2012-russell.pdfhttps://www.google.com/amp/s/www.oprahmag.com/life/health/amp27336010/rewatching-old-reruns-is-good-for-your-health-study/index.html'
        rewrite ^/(?<original>doc/.*\.pdf)http.*$ /$original permanent;
        rewrite ^/(?<original>doc/.*\.html)http.*$ /$original permanent;
        rewrite ^/(?<original>.*\.pdf)\.pdf$ /$original permanent;
        rewrite ^/(?<original>.*)/index\.html\?KEY1\[KEY2\]\=VALUE0$ /$original permanent;
        rewrite ^/(?<original>.*)http$ /$original permanent;
        rewrite ^/(?<original>.*)https$ /$original permanent;
        rewrite ^/(?<original>.*)_$ /$original permanent;
        rewrite ^/(?<original>.*)__$ /$original permanent;
        rewrite ^/static/js/(?<original>.*)\.js\.map$ /static/js/$original permanent;
        rewrite ^/doc/(?<original>.*\.pdf)/index.*$ /doc/$original permanent;
        rewrite ^/doc/(?<original>.*\.pdf)/$ /doc/$original permanent;
        rewrite ^/doc/(?<original>.*\.html)/$ /doc/$original permanent;
        rewrite ^/doc/(?<original>.*\.html)/index$ /doc/$original permanent;
        rewrite ^/(?<original>.*)\&hl\=en$ /$original permanent;
        rewrite ^/[A-Z].*(?<original>/tags/.*) /$original permanent;
        rewrite ^/(?<original>doc/.*/index)/index.html /$original permanent;
        rewrite ^/newsletter/(?<original>2[0-9][0-9][0-9]/[01][0-9])/index.html /newsletter/$original permanent;
        rewrite ^/(?<original>.*)\&lt$ /$original permanent;
        rewrite ^/(?<original>.*)\&gt$ /$original permanent;
        rewrite ^/en/about/cookies/(?<original>.*) /$original permanent;
        rewrite ^/en/activities/(?<original>.*) /$original permanent;
        rewrite ^/en/datasets/(?<original>.*) /$original permanent;
        rewrite ^/en/clippings/(?<original>.*) /$original permanent;
        rewrite ^/en/organisations/(?<original>.*) /$original permanent;
        rewrite ^/en/persons/(?<original>.*) /$original permanent;
        rewrite ^/en/prizes/(?<original>.*) /$original permanent;
        rewrite ^/en/publications/(?<original>.*) /$original permanent;
        rewrite ^/en/(?<original>.*) /$original permanent;
        rewrite ^/img/(?<original>.*) /static/img/$original permanent;
        rewrite ^/(?<original>.*)Cache /$original permanent;
        rewrite ^/dics/(?<original>.*) /doc/$original permanent;
        rewrite ^/doc/catnip/(?<original>.*) /doc/cat/$original permanent;
        rewrite ^/doc/sr/pickard/(?<original>.*) /doc/darknet-markets/william-pickard/$original permanent;
        rewrite ^/doc/lwsurvey/hpmor/(?<original>.*) /doc/lesswrong-survey/hpmor/$original permanent;
        rewrite ^/doc/histoy/(?<original>.*) /doc/history/$original permanent;
        rewrite ^/doc/rl/armstrong-controlproblem/ /doc/reinforcement-learning/armstrong-controlproblem/$original permanent;
        rewrite ^/(?<original>doc/.*)/index/index.html /$original/index permanent;
        rewrite ^/(?<original>doc/.*)\.pd/f.$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)\.pd\\\f.*$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*)-pdf$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)https://www.gwern.net.*$ /$original permanent; # eg. '/CO2-Coinhttps://gwern.net/CO2-Coin'
        rewrite ^/(?<original>.*)\{$ /$original permanent;
        rewrite ^/(?<original>.*)\}$ /$original permanent;
        rewrite ^/(?<original>.*)\.$ /$original permanent;
        rewrite ^/(?<original>.*)\;$ /$original permanent;
        rewrite ^/(?<original>.*)\,$ /$original permanent;
        rewrite ^/(?<original>.*)\"$ /$original permanent;
        rewrite ^/(?<original>.*)\'$ /$original permanent;
        rewrite ^/(?<original>.*)\.Pdf$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)\.PDf$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)\.PDF$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)\.pDF$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)\.pdF$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)undefined$ /$original permanent;

        # deal en masse with spam hits from local Reddit archives
        rewrite ^/AskHistorians/(?<original>.*)$ https://old.reddit.com/r/AskHistorians/$original permanent;
        rewrite ^/.*/r/politics/(?<original>.*)$ https://old.reddit.com/r/politics/$original permanent;
        rewrite ^/CasadastraphobiaENG/(?<original>.*)$ https://old.reddit.com/r/CasadastraphobiaENG/$original permanent;
        rewrite ^/fusion/(?<original>.*)$ https://old.reddit.com/r/fusion/$original permanent;
        rewrite ^/gamedev/(?<original>.*)$ https://old.reddit.com/r/gamedev/$original permanent;
        rewrite ^/intradeForum/(?<original>.*)$ https://old.reddit.com/r/intradeForum/$original permanent;
        rewrite ^/keto/(?<original>.*)$ https://old.reddit.com/r/keto/$original permanent;
        rewrite ^/DimensionJumping/(?<original>.*)$ https://old.reddit.com/r/DimensionJumping/$original permanent;
        rewrite ^/AstralProjection/(?<original>.*)$ https://old.reddit.com/r/AstralProjection/$original permanent;
        rewrite ^/shiftingrealities/(?<original>.*)$ https://old.reddit.com/r/shiftingrealities/$original permanent;
        rewrite ^/Phobia/(?<original>.*)$ https://old.reddit.com/r/Phobia/$original permanent;
        rewrite ^/DotA2/(?<original>.*)$ https://old.reddit.com/r/DotA2/$original permanent;
        rewrite ^/place/(?<original>.*)$ https://old.reddit.com/r/place/$original permanent;
        rewrite ^/AIDungeon/(?<original>.*)$ https://old.reddit.com/r/AIDungeon/$original permanent;
        rewrite ^/AnimalsBeingDerps/(?<original>.*)$ https://old.reddit.com/r/AnimalsBeingDerps/$original permanent;
        rewrite ^/Bitcoin/(?<original>.*)$ https://old.reddit.com/r/Bitcoin/$original permanent;
        rewrite ^/CucumbersScaringCats/(?<original>.*)$ https://old.reddit.com/r/CucumbersScaringCats/$original permanent;
        rewrite ^/DIY_eJuice/(?<original>.*)$ https://old.reddit.com/r/DIY_eJuice/$original permanent;
        rewrite ^/DecisionTheory/(?<original>.*)$ https://old.reddit.com/r/DecisionTheory/$original permanent;
        rewrite ^/Futurology/(?<original>.*)$ https://old.reddit.com/r/Futurology/$original permanent;
        rewrite ^/GPT3/(?<original>.*)$ https://old.reddit.com/r/GPT3/$original permanent;
        rewrite ^/HPMOR/(?<original>.*)$ https://old.reddit.com/r/HPMOR/$original permanent;
        rewrite ^/HobbyDrama/(?<original>.*)$ https://old.reddit.com/r/HobbyDrama/$original permanent;
        rewrite ^/IAmA/(?<original>.*)$ https://old.reddit.com/r/IAmA/$original permanent;
        rewrite ^/IncreasinglyVerbose/(?<original>.*)$ https://old.reddit.com/r/IncreasinglyVerbose/$original permanent;
        rewrite ^/LSD/(?<original>.*)$ https://old.reddit.com/r/LSD/$original permanent;
        rewrite ^/LessWrong/(?<original>.*)$ https://old.reddit.com/r/LessWrong/$original permanent;
        rewrite ^/MEMarketplace/(?<original>.*)$ https://old.reddit.com/r/MEMarketplace/$original permanent;
        rewrite ^/MLPtunes/(?<original>.*)$ https://old.reddit.com/r/MLPtunes/$original permanent;
        rewrite ^/MachineLearning/(?<original>.*)$ https://old.reddit.com/r/MachineLearning/$original permanent;
        rewrite ^/MapPorn/(?<original>.*)$ https://old.reddit.com/r/MapPorn/$original permanent;
        rewrite ^/MediaSynthesis/(?<original>.*)$ https://old.reddit.com/r/MediaSynthesis/$original permanent;
        rewrite ^/NavySealCopypasta/(?<original>.*)$ https://old.reddit.com/r/NavySealCopypasta/$original permanent;
        rewrite ^/Parahumans/(?<original>.*)$ https://old.reddit.com/r/Parahumans/$original permanent;
        rewrite ^/Perfectfit/(?<original>.*)$ https://old.reddit.com/r/Perfectfit/$original permanent;
        rewrite ^/Pets/(?<original>.*)$ https://old.reddit.com/r/Pets/$original permanent;
        rewrite ^/Piracy/(?<original>.*)$ https://old.reddit.com/r/Piracy/$original permanent;
        rewrite ^/PrequelMemes/(?<original>.*)$ https://old.reddit.com/r/PrequelMemes/$original permanent;
        rewrite ^/Psychonaut/(?<original>.*)$ https://old.reddit.com/r/Psychonaut/$original permanent;
        rewrite ^/QuantifiedSelf/(?<original>.*)$ https://old.reddit.com/r/QuantifiedSelf/$original permanent;
        rewrite ^/QuantikXanax/(?<original>.*)$ https://old.reddit.com/r/QuantikXanax/$original permanent;
        rewrite ^/Re_Zero/(?<original>.*)$ https://old.reddit.com/r/Re_Zero/$original permanent;
        rewrite ^/SDAM/(?<original>.*)$ https://old.reddit.com/r/SDAM/$original permanent;
        rewrite ^/SampleSize/(?<original>.*)$ https://old.reddit.com/r/SampleSize/$original permanent;
        rewrite ^/Scholar/(?<original>.*)$ https://old.reddit.com/r/Scholar/$original permanent;
        rewrite ^/Scotland/(?<original>.*)$ https://old.reddit.com/r/Scotland/$original permanent;
        rewrite ^/SheepMarketplace/(?<original>.*)$ https://old.reddit.com/r/SheepMarketplace/$original permanent;
        rewrite ^/SilkRoad/(?<original>.*)$ https://old.reddit.com/r/SilkRoad/$original permanent;
        rewrite ^/SpiceandWolf/(?<original>.*)$ https://old.reddit.com/r/SpiceandWolf/$original permanent;
        rewrite ^/StartledCats/(?<original>.*)$ https://old.reddit.com/r/StartledCats/$original permanent;
        rewrite ^/SubSimulatorGPT2/(?<original>.*)$ https://old.reddit.com/r/SubSimulatorGPT2/$original permanent;
        rewrite ^/SubSimulatorGPT2Meta/(?<original>.*)$ https://old.reddit.com/r/SubSimulatorGPT2Meta/$original permanent;
        rewrite ^/Supplements/(?<original>.*)$ https://old.reddit.com/r/Supplements/$original permanent;
        rewrite ^/TOR/(?<original>.*)$ https://old.reddit.com/r/TOR/$original permanent;
        rewrite ^/TOUHOUMUSIC/(?<original>.*)$ https://old.reddit.com/r/TOUHOUMUSIC/$original permanent;
        rewrite ^/TheMotte/(?<original>.*)$ https://old.reddit.com/r/TheMotte/$original permanent;
        rewrite ^/TomMarketplace/(?<original>.*)$ https://old.reddit.com/r/TomMarketplace/$original permanent;
        rewrite ^/Tulpas/(?<original>.*)$ https://old.reddit.com/r/Tulpas/$original permanent;
        rewrite ^/UnresolvedMysteries/(?<original>.*)$ https://old.reddit.com/r/UnresolvedMysteries/$original permanent;
        rewrite ^/WTF/(?<original>.*)$ https://old.reddit.com/r/WTF/$original permanent;
        rewrite ^/WeAreTheMusicMakers/(?<original>.*)$ https://old.reddit.com/r/WeAreTheMusicMakers/$original permanent;
        rewrite ^/atlantis/(?<original>.*)$ https://old.reddit.com/r/atlantis/$original permanent;
        rewrite ^/electronic_cigarettes/(?<original>.*)$ https://old.reddit.com/r/electronic_cigaretttes/$original permanent;
        rewrite ^/estimation/(?<original>.*)$ https://old.reddit.com/r/estimation/$original permanent;
        rewrite ^/fitnesscirclejerk/(?<original>.*)$ https://old.reddit.com/r/fitnesscirclejerk/$original permanent;
        rewrite ^/genewolfe/(?<original>.*)$ https://old.reddit.com/r/genewolfe/$original permanent;
        rewrite ^/grams/(?<original>.*)$ https://old.reddit.com/r/grams/$original permanent;
        rewrite ^/hangovereffect/(?<original>.*)$ https://old.reddit.com/r/hangovereffect/$original permanent;
        rewrite ^/haskell_proposals/(?<original>.*)$ https://old.reddit.com/r/haskell_proposals/$original permanent;
        rewrite ^/havanamarket/(?<original>.*)$ https://old.reddit.com/r/havanamarket/$original permanent;
        rewrite ^/holdmycatnip/(?<original>.*)$ https://old.reddit.com/r/holdmycatnip/$original permanent;
        rewrite ^/interestingasfuck/(?<original>.*)$ https://old.reddit.com/r/interestingasfuck/$original permanent;
        rewrite ^/kratom/(?<original>.*)$ https://old.reddit.com/r/kratom/$original permanent;
        rewrite ^/lowlevelaware/(?<original>.*)$ https://old.reddit.com/r/lowlevelaware/$original permanent;
        rewrite ^/math/(?<original>.*)$ https://old.reddit.com/r/math/$original permanent;
        rewrite ^/medsforbitcoin/(?<original>.*)$ https://old.reddit.com/r/medsforbitcoin/$original permanent;
        rewrite ^/microdosing/(?<original>.*)$ https://old.reddit.com/r/microdosing/$original permanent;
        rewrite ^/minimalism/(?<original>.*)$ https://old.reddit.com/r/minimalism/$original permanent;
        rewrite ^/mlscaling/(?<original>.*)$ https://old.reddit.com/r/mlscaling/$original permanent;
        rewrite ^/modup/(?<original>.*)$ https://old.reddit.com/r/modup/$original permanent;
        rewrite ^/mylittlepony/(?<original>.*)$ https://old.reddit.com/r/mylittlepony/$original permanent;
        rewrite ^/neography/(?<original>.*)$ https://old.reddit.com/r/neography/$original permanent;
        rewrite ^/neuro/(?<original>.*)$ https://old.reddit.com/r/neuro/$original permanent;
        rewrite ^/onions/(?<original>.*)$ https://old.reddit.com/r/onions/$original permanent;
        rewrite ^/philosophy/(?<original>.*)$ https://old.reddit.com/r/philosophy/$original permanent;
        rewrite ^/programming/(?<original>.*)$ https://old.reddit.com/r/programming/$original permanent;
        rewrite ^/r/haskell/(?<original>.*)$ https://old.reddit.com/r/haskell/$original permanent;
        rewrite ^/rational/(?<original>.*)$ https://old.reddit.com/r/rational/$original permanent;
        rewrite ^/reinforcementlearning/(?<original>.*)$ https://old.reddit.com/r/reinforcementlearning/$original permanent;
        rewrite ^/researchchemicals/(?<original>.*)$ https://old.reddit.com/r/researchchemicals/$original permanent;
        rewrite ^/sanitariummarket/(?<original>.*)$ https://old.reddit.com/r/sanitariummarket/$original permanent;
        rewrite ^/science/(?<original>.*)$ https://old.reddit.com/r/science/$original permanent;
        rewrite ^/skeptic/(?<original>.*)$ https://old.reddit.com/r/skeptic/$original permanent;
        rewrite ^/slatestarcodex/(?<original>.*)$ https://old.reddit.com/r/slatestarcodex/$original permanent;
        rewrite ^/startups/(?<original>.*)$ https://old.reddit.com/r/startups/$original permanent;
        rewrite ^/statistics/(?<original>.*)$ https://old.reddit.com/r/statistics/$original permanent;
        rewrite ^/techsupport/(?<original>.*)$ https://old.reddit.com/r/techsupport/$original permanent;
        rewrite ^/themarketplace/(?<original>.*)$ https://old.reddit.com/r/themarketplace/$original permanent;
        rewrite ^/touhou/(?<original>.*)$ https://old.reddit.com/r/touhou/$original permanent;
        rewrite ^/transhumanism/(?<original>.*)$ https://old.reddit.com/r/transhumanism/$original permanent;
        rewrite ^/trees/(?<original>.*)$ https://old.reddit.com/r/trees/$original permanent;
        rewrite ^/unsong/(?<original>.*)$ https://old.reddit.com/r/unsong/$original permanent;
        rewrite ^/wholesomeanimememes/(?<original>.*)$ https://old.reddit.com/r/wholesomeanimememes/$original permanent;
        rewrite ^/wholesomeanimemes/(?<original>.*)$ https://old.reddit.com/r/wholesomeanimemes/$original permanent;
        rewrite ^/wikipedia/(?<original>.*)$ https://old.reddit.com/r/wikipedia/$original permanent;
        rewrite ^/conspiracy/(?<original>.*)$ https://old.reddit.com/r/conspiracy/$original permanent;
        rewrite ^/deepdream/(?<original>.*)$ https://old.reddit.com/r/deepdream/$original permanent;
        rewrite ^/StableDiffusion/(?<original>.*)$ https://old.reddit.com/r/StableDiffusion/$original permanent;
        rewrite ^/typography/(?<original>.*)$ https://old.reddit.com/r/typography/$original permanent;
        rewrite ^/bigsleep/(?<original>.*)$ https://old.reddit.com/r/bigsleep/$original permanent;
        rewrite ^/CrappyDesign/(?<original>.*)$ https://old.reddit.com/r/CrappyDesign/$original permanent;
        rewrite ^/DiscoDiffusion/(?<original>.*)$ https://old.reddit.com/r/DiscoDiffusion/$original permanent;
        rewrite ^/HentaiDiffusion/(?<original>.*)$ https://old.reddit.com/r/HentaiDiffusion/$original permanent;
        rewrite ^/Lettering/(?<original>.*)$ https://old.reddit.com/r/Lettering/$original permanent;
        rewrite ^/Openaijukebox/(?<original>.*)$ https://old.reddit.com/r/Openaijukebox/$original permanent;
        rewrite ^/Inktober/(?<original>.*)$ https://old.reddit.com/r/Inktober/$original permanent;
        rewrite ^/Illustration/(?<original>.*)$ https://old.reddit.com/r/Illustration/$original permanent;
        rewrite ^/logodesign/(?<original>.*)$ https://old.reddit.com/r/logodesign/$original permanent;
        rewrite ^/AskReddit/(?<original>.*)$ https://old.reddit.com/r/AskReddit/$original permanent;

        rewrite ^/(?<original>.*/dp/B.*)$ https://amazon.com/$original permanent; # eg. '/Miracle-Philadelphia-Constitutional-Convention-September/dp/B0007ELQH0'
        rewrite ^/(?<original>doc/www/.*)\.pdf%23(?<anchor>[a-z0-9=]+)$ /$original.pdf\#$anchor permanent;
        rewrite ^/doc/www/(?<original>.*)/\&quot$ https://$original permanent;

        # fix any hits to now-removed external link-bibliographies (long since obsoleted by 'fragment' popups, see /design-graveyard#popup-annotations):
        rewrite ^/(?<original>.*)-link-bibliography /$original#link-bibliography permanent;
        rewrite ^/doc/link-bibliography/(?<original>.*) /$original#link-bibliography permanent;

        # fix older annotations pre-interwiki-canonicalization: [a-z] → [A-Z]: (write out by hand because the recommended nginx way is to shell out to Lua in a separate block, and I don't want to mess with such complexity)
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fa(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FA$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fb(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FB$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fc(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FC$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fd(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FD$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fe(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FE$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Ff(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FF$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fg(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FG$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fh(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FH$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fi(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FI$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fj(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FJ$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fk(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FK$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fl(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FL$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fm(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FM$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fn(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FN$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fo(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FO$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fp(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FP$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fq(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FQ$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fr(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FR$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fs(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FS$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Ft(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FT$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fu(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FU$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fv(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FV$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fw(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FW$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fx(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FX$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fy(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FY$suffix permanent;
        rewrite ^/metadata/annotations/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2Fz(?<suffix>.*)$ /metadata/annotation/https%3A%2F%2Fen.wikipedia.org%2Fwiki%2FZ$suffix permanent;

        # post-WP popup shift to fully-dynamic:
        rewrite ^/image/thumbnails/wikipedia/[0-9][0-9][0-9]px-(?<original>.*) https://en.wikipedia.org/wiki/File:$original permanent;
        rewrite ^/image/thumbnails/wikipedia/(?<original>.*) https://en.wikipedia.org/wiki/File:$original permanent;
        rewrite ^/metadata/annotations/https%253A%252F%252Fen.wikipedia.org%252Fwiki%252F(?<original>.*)\.html$ https://en.wikipedia.org/wiki/$original permanent;
        rewrite ^/(?<original>(Category:|Special:|Skin/|Portal:|Talk:|Template_talk:|User:|User_talk:|Wikipedia:).*)$ https://en.wikipedia.org/wiki/$original permanent;
        rewrite ^/pmwiki/pmwiki.php/(?<original>.*)$ https://tvtropes.org/pmwiki/pmwiki.php/$original permanent;
        rewrite ^/(?<original>doc/.*).Pdf /$original.pdf permanent;
        rewrite ^/(?<original>.*)\&source=gmail /$original permanent;
        rewrite ^/(?<original>emociones/.*) https://competicionmental.appspot.com/$original permanent;
        rewrite ^/(?<original>sound/[a-z]+\.mp3) https://competicionmental.appspot.com/$original permanent;
        rewrite ^/(?<original>letrasImg/.*.png) https://competicionmental.appspot.com/$original permanent;
        rewrite ^/(?<original>letra/.*) https://competicionmental.appspot.com/$original permanent;
        rewrite ^/(?<original>/figuras/.*.png) https://competicionmental.appspot.com/$original permanent;
        rewrite ^/(?<original>.*)\&rut\=.* /$original permanent;
        rewrite ^/(?<original>.*)\&lang\=.* /$original permanent;
        rewrite ^/(?<original>.*)\&keyword\=.* /$original permanent;
        rewrite ^/(?<original>doc/.*/)\&quot.* /$original/index permanent;
        rewrite ^/backlinks/(?<original>.*) /metadata/annotations/backlink/$original permanent;
        rewrite ^/(?<original1>.*)\'.*(?<original2>.*)$ /$original1-$original2 permanent; # ' → -
        rewrite ^/(?<original>.*)\&amp\;.*$ /$original permanent;
        rewrite ^/(?<original>doc/.*/.*\.pdf)-.*$ /$original permanent; # deal with all the erroneous PDF links like '/doc/iq/2018-warne.pdf-en-espanol'
        rewrite ^/(?<original>doc/.*/.*\.pdf)[A-Z][a-z]+$ /$original permanent; # '/doc/psychology/writing/1993-ericsson.pdfEriksson'
        rewrite ^/(?<original>\.page)/.*$ /$original permanent; # '/foo.page/', surprisingly common
        rewrite ^/(?<original>\.md)/.*$ /$original permanent;
        rewrite ^/(?<original>.*)\&tbnid.*$ /$original permanent;
        rewrite ^/(?<original>.*)\]$ /$original permanent;
        rewrite ^/(?<original>.*)\[$ /$original permanent;
        rewrite ^/\.\/(?<original>.*) /$original permanent;
        rewrite ^/(?<original>.*).PAGE$ /$original.md permanent;
        rewrite ^/(?<original>.*)\’ /$original permanent;
        rewrite ^/(?<original>.*)\&title.*$ /$original permanent;
        rewrite ^/(?<original>.*).pagee$ /$original.md permanent;
        rewrite ^/(?<original>.*/)url$ /$original permanent;
        rewrite ^/(?<original>doc/.*\.pdf)/.*$ /$original permanent;
        rewrite ^/(?<original>tags/.*).page.*$ /$original permanent;
        rewrite ^/(?<original>.*).pages$ /$original.md permanent;
        rewrite ^/(?<original>.*).Page$ /$original.md permanent;
        rewrite ^/(?<original>.*)adminer\..*$ /$original permanent;
        rewrite ^/(?<original>doc/.*\.pd)\/(?<ext>f).*$ /$original$ext permanent;
        rewrite ^/(?<original>doc/.*\.pd)%5c(?<ext>f).*$ /$original$ext permanent;
        rewrite ^/(?<original>doc/.*\.)-pdf$ /$original.pdf permanent;
        rewrite ^/(?<original>doc/.*\.pdf)y$ /$original permanent;
        rewrite ^/(?<original>doc/.*\.pdf)y$ /$original permanent;
        rewrite ^/(?<original>.*)；.*$ /$original permanent;
        rewrite ^/(?<original>pubmed/.*)$ https://www.ncbi.nlm.nih.gov/$original permanent;
        rewrite ^/(?<original>pmc/articles/.*)$ https://www.ncbi.nlm.nih.gov/$original permanent;
        rewrite ^/(?<original>.*)”$ /$original permanent;
        rewrite ^/(?<original>.*\.html)[0-46-9]+$ /$original permanent;
        rewrite ^/(?<original>.*/index)/index.html /$original permanent;
        rewrite ^/doc/pyschology/(?<original>.*)$ /doc/psychology/$original permanent;
        rewrite ^/(?<original>static/font/.*.ttf).*-format.*$ /$original permanent;
        rewrite ^/(?<original>doc/.*)/inde$ /$original/index permanent;
        rewrite ^/(?<original>.*[a-z])(?<originalAnchor>fn[0-9]+)$    /$original\#$originalAnchor permanent;
        rewrite ^/(?<original>.*[a-z])(?<originalAnchor>fnref[0-9]+)$ /$original\#$originalAnchor permanent;
        rewrite ^/(?<original>.*[a-z])(?<originalAnchor>sn[0-9]+)$    /$original\#$originalAnchor permanent;
        rewrite ^/(?<original>.*)/[0-9]+px-.*em.*$ /$original permanent;
        rewrite ^/image/(?<original>201[2-5]-[01][1-9]-[1-3][0-9]-tumblr_[mn][a-z0-9_]+_640\.png).*$ /image/hardtruthsfromsoftcats.tumblr.com/$original;
        rewrite ^/(?<original>doc/.*)\]\(http.*$ /$original permanent;
        rewrite ^/metadata/annotations/similar/(?<original>.*)$ /metadata/annotation/similar/$original permanent;
        rewrite ^/doc/psycology/(?<original>.*)$ /doc/psychology/$original permanent;
        rewrite ^/doc/www/pkhungurn\.github\.io/data/videos/(?<original>.*)$ https://pkhungurn.github.io/data/videos/$original permanent;
        rewrite ^/doc/www/semianalysis.com/(?<original>.*)$ /doc/www/www.semianalysis.com/$original permanent;
        rewrite ^/(?<original>doc/.*),pdf$ /$original.pdf permanent;
        rewrite ^/(?<original>.*)%C3%A7$ /$original permanent;
        rewrite ^/(?<original>.*)/index/index$ /$original/index permanent;
        rewrite ^/ttps://www\.gwern\.net/(?<original>.*)$ /$original permanent;

        rewrite ^/doc/silk-road/(?<old>.*)$ /doc/darknet-markets/$old permanent;
        rewrite ^/doc/ai/stylegan/(?<old>.*)$ /doc/ai/gan/stylegan/$old permanent;
        rewrite ^/doc/ai/rnn/(?<old>.*)$ /doc/ai/nn/rnn/$old permanent;
        rewrite ^/doc/ai/fully-connected/(?<old>.*)$ /doc/ai/nn/fully-connected/$old permanent;
        rewrite ^/doc/ai/gpt/(?<old>.*)$ /doc/ai/nn/transformer/gpt/$old permanent;
        rewrite ^/doc/ai/gan/(?<old>.*)$ /doc/ai/nn/gan/$old permanent;
        rewrite ^/doc/reinforcement-learning/alphago/(?<old>.*)$ /doc/reinforcement-learning/model/alphago/$old permanent;
        rewrite ^/doc/reinforcement-learning/muzero/(?<old>.*)$ /doc/reinforcement-learning/model/muzero/$old permanent;
        rewrite ^/doc/reinforcement-learning/oa5/(?<old>.*)$ /doc/reinforcement-learning/model-free/oa5/$old permanent;
        rewrite ^/doc/reinforcement-learning/alphastar/(?<old>.*)$ /doc/reinforcement-learning/model-free/alphastar/$old permanent;
        rewrite ^/doc/eva/(?<old>.*)$ /doc/anime/eva/$old permanent;
        rewrite ^/doc/conscientiousness/(?<old>.*)$ /doc/psychology/personality/conscientiousness/$old permanent;
        rewrite ^/doc/spaced-repetition/(?<old>.*)$ /doc/psychology/spaced-repetition/$old permanent;
        rewrite ^/doc/psychology/bird/(?<old>.*)$ /doc/psychology/animal/bird/$old permanent;
        rewrite ^/doc/prediction/(?<old>.*)$ /doc/statistics/prediction/$old permanent;
        rewrite ^/doc/terrorism/(?<old>.*)$ /doc/crime/terrorism/$old permanent;
        rewrite ^/doc/statistics/crime/terrorism/(?<old>.*)$ /doc/crime/terrorism/$old permanent;
        rewrite ^/doc/genetics/selection/dysgenics/(?<old>.*)$ /doc/genetics/selection/natural/human/dysgenics/$old permanent;
        rewrite ^/doc/genetics/selection/index-selection/(?<old>.*)$ /doc/genetics/selection/artificial/index-selection/$old permanent;
        rewrite ^/doc/genetics/selection/apple/(?<old>.*)$ /doc/genetics/selection/artificial/apple/$old permanent;
        rewrite ^/doc/genetics/correlation/(?<old>.*)$ /doc/genetics/heritable/correlation/$old permanent;
        rewrite ^/doc/linkrot/(?<old>.*)$ /doc/cs/linkrot/$old permanent;
        rewrite ^/doc/advertising/(?<old>.*)$ /doc/economics/advertising/$old permanent;
        rewrite ^/xf/BL_Image/(?<original>.*)$ https://www.bluelight.org/xf/BL_Image/$original permanent;
        rewrite ^/doc/sf/(?<old>.*)$ /doc/fiction/science-fiction/$old permanent;
        rewrite ^/doc/ai/alphafold/(?<old>.*)$ /doc/ai/nn/transformer/alphafold/$old permanent;
        rewrite ^/doc/lithium/(?<old>.*)$ /doc/psychiatry/lithium/$old permanent;
        rewrite ^/(?<before>.*)/%E2%80%8B(?<after>.*)$ /$before/$after permanent;
        rewrite ^/doc/iq/smpy/(?<old>.*)$ /doc/iq/high/smpy/$old permanent;
        rewrite ^/doc/iq/anne-roe/(?<old>.*)$ /doc/iq/high/anne-roe/$old permanent;
        rewrite ^/doc/iq/fullerton/(?<old>.*)$ /doc/iq/high/fullerton/$old permanent;
        rewrite ^/doc/iq/munich/(?<old>.*)$ /doc/iq/high/munich/$old permanent;
        rewrite ^/doc/longevity/john-bjorksten/(?<old>.*)$ /doc/longevity/johan-bjorksten/$old permanent;
        rewrite ^/doc/ai/clip/samples/(?<old>.*)$ /doc/ai/nn/transformer/clip/sample/$old permanent;
        rewrite ^/doc/ai/nn/transformer/clip/samples/(?<old>.*)$ /doc/ai/nn/transformer/clip/sample/$old permanent;
        rewrite ^/doc/ai/clip/(?<old>.*)$ /doc/ai/nn/transformer/clip/$old permanent;
        rewrite ^/doc/ai/sparsity/(?<old>.*)$ /doc/ai/nn/sparsity/$old permanent;
        rewrite ^/doc/ai/retrieval/(?<old>.*)$ /doc/ai/nn/retrieval/$old permanent;
        rewrite ^/doc/ai/adversarial/(?<old>.*)$ /doc/ai/nn/adversarial/$old permanent;
        rewrite ^/doc/ai/diffusion/(?<old>.*)$ /doc/ai/nn/diffusion/$old permanent;
        rewrite ^/image/ai/diffusion/(?<old>.*)$ /image/ai/nn/diffusion/$old permanent;
        rewrite ^/doc/japanese/(?<old>.*)$ /doc/japan/$old permanent;
        rewrite ^/doc/japan/zeami/(?<old>.*)$ /doc/japan/poetry/zeami/$old permanent;
        rewrite ^/doc/music-distraction/(?<old>.*)$ /doc/psychology/music/distraction/$old permanent;
        rewrite ^/doc/nature/(?<old>.*)$ /doc/psychology/nature/$old permanent;
        rewrite ^/doc/humor/(?<old>.*)$ /doc/fiction/humor/$old permanent;
        rewrite ^/doc/statistics/comparison/(?<old>.*)$ /doc/statistics/order/comparison/$old permanent;
        rewrite ^/doc/sunk-cost/(?<old>.*)$ /doc/psychology/cognitive-bias/sunk-cost/$old permanent;
        rewrite ^/doc/psychology/illusion-of-depth/(?<old>.*)$ /doc/psychology/cognitive-bias/illusion-of-depth/$old permanent;
        rewrite ^/doc/ai/scaling/moe/(?<old>.*)$ /doc/ai/scaling/mixture-of-experts/$old permanent;
        rewrite ^/(?<original>doc/.*)/[0-9]$ /$original permanent;
        rewrite ^/(?<original>doc/.*)/[0-9][0-9]$ /$original permanent;
        rewrite ^/(?<original>doc/.*)/[0-9][0-9][0-9]$ /$original permanent;
        rewrite ^/(?<original>doc/.*)/[0-9][0-9][0-9][0-9]$ /$original permanent;
        rewrite ^/(?<first>.*)%E2%80%93(?<second>.*)$ /$first-$second permanent;
        rewrite ^/\.\/(?<all>.*)$ /$all permanent;
        rewrite ^/Doc/(?<old>.*)$ /doc/$old permanent;
        rewrite ^/Doc/History/(?<old>.*)$ /doc/history/$old permanent;
        rewrite ^/doc/cat/tartarian-honeysuckle/(?<old>.*)$ /doc/cat/tatarian-honeysuckle/$old permanent;
        rewrite ^/(?<old>.*)/null$ /$old/index permanent;
        rewrite ^/(?<old>doc/.*)/null.*$ /$old/index permanent;
        rewrite ^/(?<old>.*)%EF%BC%8E.*$ /$old permanent;

        rewrite ^/image/otakutalk/(?<old>.*)$ /image/eva/otaku-talk/$old permanent;
        rewrite ^/image/storyofyourlife/(?<old>.*)$ /image/story-of-your-life/$old permanent;
        rewrite ^/image/notenkimemoirs/blueblazes/(?<old>.*)$ /image/notenki-memoirs/blue-blazes/$old permanent;
        rewrite ^/image/notenkimemoirs/(?<old>.*)$ /image/notenki-memoirs/$old permanent;
        rewrite ^/image/spacedrepetition/(?<old>.*)$ /image/spaced-repetition/$old permanent;
        rewrite ^/image/dnb/(?<old>.*)$ /image/dual-n-back/$old permanent;
        rewrite ^/image/philo/(?<old>.*)$ /image/philosophy/$old permanent;
        rewrite ^/image/ab/(?<old>.*)$ /image/ab-testing/$old permanent;
        rewrite ^/image/coinflip/(?<old>.*)$ /image/coin-flip/$old permanent;
        rewrite ^/image/silkroad/(?<old>.*)$ /image/silk-road/$old permanent;
        rewrite ^/image/littleboy/(?<old>.*)$ /image/eva/little-boy/$old permanent;
        rewrite ^/image/ai/dalle/2/(?<old>.*)$ /image/ai/dall-e/2/$old permanent;
        rewrite ^/image/ai/dalle/1/(?<old>.*)$ /image/ai/dall-e/1/$old permanent;
        rewrite ^/image/ai/dalle/(?<old>.*)$ /image/ai/dall-e/$old permanent;
        rewrite ^/image/orderstatistics/(?<old>.*)$ /image/statistics/order/$old permanent;
        rewrite ^/image/order-statistics/(?<old>.*)$ /image/statistics/order/$old permanent;
        rewrite ^/image/dinosaurcomics/(?<old>.*)$ /image/humor/dinosaur-comics/$old permanent;
        rewrite ^/image/lwsurvey/(?<old>.*)$ /image/lw-survey/$old permanent;
        rewrite ^/image/rl/(?<old>.*)$ /image/reinforcement-learning/$old permanent;
        rewrite ^/static/static/(?<old>.*)$ /static/$old permanent;
        rewrite /doc/(?<first>[a-z0-9-]+)/doc/(?<second>.*)$ /doc/$first/$second permanent;
        rewrite /doc/(?<first>[a-z0-9-]+/[a-z0-9-]+)/doc/(?<second>[a-z0-9-].*)$ /doc/$first/$second permanent;
        rewrite /doc/(?<first>[a-z0-9-]+/[a-z0-9-]+)/doc/.*/doc/(?<second>[a-z0-9-].*)$ /doc/$first/$second permanent; # rewrite repeated-substitution errors like /doc/economics/advertising/doc/economics/advertising/doc/economics/advertising.../2021-freeman.pdf
        rewrite ^/(?<old>.*)%21W$ /$old permanent;
        rewrite ^/(?<old>.*)%3C/p$ /$old permanent;
        rewrite ^/(?<prefix>.*)%23(?<suffix>.*)$ /$prefix permanent; # strip 'foo.pdf#bar' encoded into the URL
        rewrite ^/(?<prefix>.*)\&quot.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)\&embed.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)\&target.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)\?nig.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)\?sourc.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)\?backlinkTarget.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)-https:.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)-http:.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)https:.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)http:.*$ /$prefix permanent;
        rewrite ^/(?<prefix>.*)accessed.*$ /$prefix permanent;
        rewrite ^/tags/(?<first>.*)+(?<second>.*)$ /$first%20$second permanent;
        rewrite ^/newslettter/(?<old>.*)$ /newsletter/$old permanent;
        rewrite ^/newsleter/(?<old>.*)$ /newsletter/$old permanent;
        rewrite ^/(?<issue>20[0-5][0-9]/[01][0-9])$ /newsletter/$issue permanent;
        rewrite ^/(?<old>.*)`(?<old2>.*)$ /$old$old2 permanent;
        rewrite ^/(?<old>.*)\.page/index\.html$ /$old.md permanent;
        rewrite ^/(?<old>.*)\.page/$ /$old.md permanent;
        rewrite ^/(?<old>.*)\.page\.html$ /$old.md.html permanent;
        rewrite ^/(?<old>.*)/N/A$ /$old permanent;
        rewrite ^/(?<first>.*)‐(?<second>.*)$ /$first-$second permanent; # convert HYPHEN (which is bad!) to what everyone thinks HYPHEN is, HYPHEN-MINUS
        rewrite ^/(?<first>.*)\+(?<second>[^0-9][^0-9]*)$ /$first-$second permanent; # WARNING: PAR2 files have '+' required in them, like 'ecc.vol127+73.par2'
        rewrite ^/tatic/(?<old>.*)$ /static/$old permanent;
        rewrite ^/doc/anime/eva/anime/eva/(?<old>.*)$ /doc/anime/eva/$old permanent;
        rewrite ^/(?<old>.*)/index/wp$ /$old/index permanent;
        rewrite ^/doc/(?<dir>.*)/docs?/(?<file>.*)$ /doc/$dir/$file permanent;
        rewrite ^/pmc/(?<old>.*)$ https://www.ncbi.nlm.nih.gov/pmc/$old permanent;

        # rewrite LW links like `/posts/5yFRd3cjLpm3Nd6Di/argument-screens-off-authority` (17 character ID):
        rewrite ^(?<old>/posts/[A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9][A-Za-z0-9]/[a-z-]+$) https://lesswrong.com$old permanent;
        rewrite ^/(?<old>.*)-530px.jpg-530px.jpg$ /$old permanent;
        rewrite ^/(?<old>.*)-530px.jpg$ /$old permanent;
        rewrite ^/(?<old>.*)-768px\.[jp][pn]g$ /$old permanent;

        rewrite ^/image/(?<old>.+)$ /doc/$old permanent;
        rewrite ^/genetics/(?<old>.*)$ /doc/genetics/$old permanent;
        rewrite ^/doc/gan/stylegan/(?<old>.*)$ /doc/ai/nn/gan/stylegan/$old permanent;
        rewrite ^/(?<old>.*)，.*$ /$old permanent;
        rewrite ^/Docs?/(?<old>.*)$ /doc/$old permanent;
        rewrite ^/\.\/(?<old>.*)$ /$old permanent; # eg. '/./Complement'
        rewrite ^/(?<page>.*)@(?<anchor>.*)$ /$page#$anchor permanent; # eg. '/review/movie@all-about-eve' - for some reason, '#' fragments often get translated to '@'. Haven't figured out who or why.
        rewrite ^/(?<page>doc/psychology/okcupid/.*\.html).+$ /$page permanent; # the ingenuity of people coming up with new typos on OKC archives is astounding
        rewrite ^/(?<page>doc/psychology/.*)\.htm$ /$page.html permanent;
        rewrite ^/(?<old>.*\.pdf)(Just|this|it|there|as|you|me|if|on|special|the|in|link|they|og|2|also|for|3|\.html|abstractlike|sorry|please|replydelete|[Hh]e|[Ss]he|Nebula|Social|Since|and|And)$ /$old permanent;
        rewrite ^/(?<hs>static/build/.*)\.hi$ /$hs.hs permanent;
        rewrite ^/(?<hs>static/build/.*)\.o$  /$hs.hs permanent;
        rewrite ^/(?<first>.*)%E2%80%93(?<second>.*)$ /$first-$second permanent;
        rewrite ^/.*ttps\:\/\/gwern\.net/(?<original>.*) /$original permanent;
        rewrite ^/doc/cryonic/(?<original>.*)$ /doc/cryonics/$original permanent;
        rewrite ^/doc/philosophy/frank-p-ramsey/(?<original>.*)$ /doc/philosophy/frank-ramsey/$original permanent;
        rewrite ^/(?<original>doc/.*)/3[Dd]$ /$original/index permanent;
        rewrite ^/(?<original>.*)\)$ /$original permanent;
        rewrite ^/(?<original>.*)\[.*$ /$original permanent;
        rewrite ^/(?<original>.*)\].*$ /$original permanent;
        rewrite ^/\./(?<original>.*)$ /$original permanent;
        rewrite ^/\./(?<original>.*)%7C4$ /$original permanent;
        rewrite ^/doc/psychology/european-journal-of-parapsychology/(?<original>.*)$ /doc/psychology/parapsychology/european-journal-of-parapsychology/$original permanent;
        rewrite ^/doc/www/i.reddit.com/(?<old>.*)$ /doc/www/old.reddit.com/$old permanent;
        rewrite ^/doc/reinforcement-learning/brain-imitation-learning/(?<old>.*)$ /doc/reinforcement-learning/imitation-learning/brain-imitation-learning/$old permanent;
        rewrite ^/doc/www/(?<domain>.*)/.*\.html/favicon.ico$ https://$domain/favicon.ico permanent; # match domain+snapshot examples like `/doc/www/edwardtufte.github.io/e43d8239ed3fa1d513e2b4d071b6a7c0c8a98bff.html/favicon.ico`
        rewrite ^/doc/www/(?<domain>.+\..+)/favicon.ico$ https://$domain/favicon.ico permanent; # match domain examples like `/doc/www/languagelog.ldc.upenn.edu/favicon.ico`
        rewrite ^/(?<original>.*)/wp-admin/css/.*$ /$original permanent;

        # fix en masse all of the doc-prefix-less file hosting errors that keep happening (not least by myself when carelessly linking people to documents), by rewriting for all top-level doc/foo/ paths to date:
        # NOTE: must be careful to skip top-level directories that do in fact exist (eg. 'zeo', 'nootropic', 'fiction')
        rewrite ^/ai/(?<original>.*)$ /doc/ai/$original permanent;
        rewrite ^/algernon/(?<original>.*)$ /doc/algernon/$original permanent;
        rewrite ^/anime/(?<original>.*)$ /doc/anime/$original permanent;
        rewrite ^/biology/(?<original>.*)$ /doc/biology/$original permanent;
        rewrite ^/bitcoin/(?<original>.*)$ /doc/bitcoin/$original permanent;
        rewrite ^/borges/(?<original>.*)$ /doc/borges/$original permanent;
        rewrite ^/cat/(?<original>.*)$ /doc/cat/$original permanent;
        rewrite ^/co2/(?<original>.*)$ /doc/co2/$original permanent;
        rewrite ^/creatine/(?<original>.*)$ /doc/creatine/$original permanent;
        rewrite ^/crime/(?<original>.*)$ /doc/crime/$original permanent;
        rewrite ^/cryonics/(?<original>.*)$ /doc/cryonics/$original permanent;
        rewrite ^/cs/(?<original>.*)$ /doc/cs/$original permanent;
        rewrite ^/culture/(?<original>.*)$ /doc/culture/$original permanent;
        rewrite ^/darknet-market/(?<original>.*)$ /doc/darknet-market/$original permanent;
        rewrite ^/dataset/(?<original>.*)$ /doc/dataset/$original permanent;
        rewrite ^/design/(?<original>.*)$ /doc/design/$original permanent;
        rewrite ^/dog/(?<original>.*)$ /doc/dog/$original permanent;
        rewrite ^/dual-n-back/(?<original>.*)$ /doc/dual-n-back/$original permanent;
        rewrite ^/economics/(?<original>.*)$ /doc/economics/$original permanent;
        rewrite ^/exercise/(?<original>.*)$ /doc/exercise/$original permanent;
        rewrite ^/existential-risk/(?<original>.*)$ /doc/existential-risk/$original permanent;
        rewrite ^/food/(?<original>.*)$ /doc/food/$original permanent;
        rewrite ^/genetics/(?<original>.*)$ /doc/genetics/$original permanent;
        rewrite ^/gwern.net-gitstats/(?<original>.*)$ /doc/gwern.net-gitstats/$original permanent;
        rewrite ^/history/(?<original>.*)$ /doc/history/$original permanent;
        rewrite ^/insight-porn/(?<original>.*)$ /doc/insight-porn/$original permanent;
        rewrite ^/interview/(?<original>.*)$ /doc/interview/$original permanent;
        rewrite ^/iodine/(?<original>.*)$ /doc/iodine/$original permanent;
        rewrite ^/iq/(?<original>.*)$ /doc/iq/$original permanent;
        rewrite ^/japan/(?<original>.*)$ /doc/japan/$original permanent;
        rewrite ^/law/(?<original>.*)$ /doc/law/$original permanent;
        rewrite ^/lesswrong-survey/(?<original>.*)$ /doc/lesswrong-survey/$original permanent;
        rewrite ^/longevity/(?<original>.*)$ /doc/longevity/$original permanent;
        rewrite ^/long-now/(?<original>.*)$ /doc/long-now/$original permanent;
        rewrite ^/marijuana/(?<original>.*)$ /doc/marijuana/$original permanent;
        rewrite ^/math/(?<original>.*)$ /doc/math/$original permanent;
        rewrite ^/melatonin/(?<original>.*)$ /doc/melatonin/$original permanent;
        rewrite ^/meta/(?<original>.*)$ /doc/meta/$original permanent;
        rewrite ^/modafinil/(?<original>.*)$ /doc/modafinil/$original permanent;
        rewrite ^/music/(?<original>.*)$ /doc/music/$original permanent;
        rewrite ^/newest/(?<original>.*)$ /doc/newest/$original permanent;
        rewrite ^/nicotine/(?<original>.*)$ /doc/nicotine/$original permanent;
        rewrite ^/personal/(?<original>.*)$ /doc/personal/$original permanent;
        rewrite ^/philosophy/(?<original>.*)$ /doc/philosophy/$original permanent;
        rewrite ^/politics/(?<original>.*)$ /doc/politics/$original permanent;
        rewrite ^/psychedelic/(?<original>.*)$ /doc/psychedelic/$original permanent;
        rewrite ^/psychiatry/(?<original>.*)$ /doc/psychiatry/$original permanent;
        rewrite ^/psychology/(?<original>.*)$ /doc/psychology/$original permanent;
        rewrite ^/radiance/(?<original>.*)$ /doc/radiance/$original permanent;
        rewrite ^/reinforcement-learning/(?<original>.*)$ /doc/reinforcement-learning/$original permanent;
        rewrite ^/rotten.com/(?<original>.*)$ /doc/rotten.com/$original permanent;
        rewrite ^/science/(?<original>.*)$ /doc/science/$original permanent;
        rewrite ^/sociology/(?<original>.*)$ /doc/sociology/$original permanent;
        rewrite ^/statistics/(?<original>.*)$ /doc/statistics/$original permanent;
        rewrite ^/survey/(?<original>.*)$ /doc/survey/$original permanent;
        rewrite ^/tea/(?<original>.*)$ /doc/tea/$original permanent;
        rewrite ^/technology/(?<original>.*)$ /doc/technology/$original permanent;
        rewrite ^/touhou/(?<original>.*)$ /doc/touhou/$original permanent;
        rewrite ^/traffic/(?<original>.*)$ /doc/traffic/$original permanent;
        rewrite ^/transhumanism/(?<original>.*)$ /doc/transhumanism/$original permanent;
        rewrite ^/tutorial/(?<original>.*)$ /doc/tutorial/$original permanent;
        rewrite ^/vitamin-d/(?<original>.*)$ /doc/vitamin-d/$original permanent;
        rewrite ^/wikipedia/(?<original>.*)$ /doc/wikipedia/$original permanent;
        rewrite ^/wiki/(?<original>[A-Z0-9].*)$ https://en.wikipedia.org/wiki/$original permanent;
        rewrite ^/wiki/(?<original>.*_.*)$ https://en.wikipedia.org/wiki/$original permanent;
        rewrite ^/wiki/(?<original>.*)$ /$original permanent;
        rewrite ^/(?<original>.*)\~$ /$original permanent;
        rewrite ^/(?<original>.*)%7E$ /$original permanent;
        rewrite ^/home/<USER>/wiki/(?<original>.*)$ /$original permanent;
        rewrite ^/www/(?<original>.*)$ /doc/www/$original permanent;
        rewrite ^/doc/www/nitter.moomoo.me/(?<original>.*)$ /doc/www/nitter.net/$original permanent;
        rewrite ^/doc/www/nitter.net/(?<original>.*)$ /doc/www/localhost/$original permanent;
        rewrite ^/doc/(?<original>.*)/out.html$ /$original/index permanent;
        rewrite ^/doc/misc/(?<original>.*)$ /doc/www/misc/$original permanent;
        rewrite ^/misc/(?<original>.*)$ /doc/www/misc/$original permanent;

        rewrite ^/[a-z0-9-]+/[a-z0-9-]+/doc/(?<original>.*)$ /doc/$original permanent;
        rewrite ^/[a-z0-9-]+/[a-z0-9-]+/[a-z0-9-]+/doc/(?<original>.*)$ /doc/$original permanent;
        rewrite ^/[a-z0-9-]+/doc/(?<original>.*)$ /doc/$original permanent;
        rewrite ^/doc/longevity/johan-bjorksten/index/longevity/johan-bjorksten/(?<original>.*)$ /doc/longevity/johan-bjorksten/$original permanent;
        rewrite ^/doc/iq/high/smpy/index/iq/high/smpy/(?<original>.*)$ /doc/iq/high/smpy/$original permanent;
        rewrite ^/doc/genetics/heritable/correlation/genetics/heritable/correlation/(?<original>.*)$ /doc/genetics/heritable/correlation/$original permanent;
        rewrite ^/doc/economics/advertising/economics/advertising/(?<original>.*)$ /doc/economics/advertising/$original permanent;
        rewrite ^/doc/design/typography/tex/index/design/typography/tex/(?<original>.*)$ doc/design/typography/tex/$original permanent;
        rewrite ^/doc/biology/index/(?<original>.*)$ /doc/biology/$original permanent;
        rewrite ^/doc/anime/eva/little-boy/index/anime/eva/little-boy/(?<original>.*)$ /doc/anime/eva/little-boy/$original permanent;
        rewrite ^/doc/anime/eva/index/anime/eva/(?<original>.*)$ /doc/anime/eva/$original permanent;
        rewrite ^/doc/ai/nn/rnn/index/ai/nn/rnn/(?<original>.*)$ /doc/ai/nn/rnn/$original permanent;
        rewrite ^/_src(?<original>.*)$ /static/$original permanent;
        rewrite ^/doc/ai/.*/arxiv.org/(?<original>.*)$ /doc/www/arxiv.org/$original permanent;
        rewrite ^/doc/statistics/bias/animal/index/(?<original>.*)$ /doc/$original permanent;
        rewrite ^/doc/longevity/semaglutide/(?<file>.*)$ /doc/longevity/glp/semaglutide/$file permanent;
        rewrite ^/doc/longevity/tirzepatide/(?<file>.*)$ /doc/longevity/glp/tirzepatide/$file permanent;
        rewrite ^/(?<original>[L-Nl-n]*[h-q][r-t][s-v].[j-m][A-Ca-f][m-z]).*$ /$original permanent;
        rewrite ^/(?<original>.*).shtml$ /$original.html permanent;
        rewrite ^/(?<original>.*)[\:\;\,]$ /$original permanent;
        rewrite ^/(?<original>.*)/1000$ /$original permanent;
        rewrite ^/static/font/drop-cap/(?<original>.*)$ /static/font/dropcap/$original permanent;
        rewrite ^/metadata/annotation/(?<base>.*)/\%\2\f(?<original>.*)$ /metadata/annotation/$base/$2F$original permanent;
        rewrite /protein/(?<protein>.*)$ https://www.ncbi.nlm.nih.gov/protein/$protein permanent;
        rewrite /doc/[a-z]+/www/(?<mirror>.*\..*/[a-z0-9]+\.html)$ /doc/www/$mirror permanent;
        rewrite /(?<original>.*)/index\.xml$ /$original/index permanent;
        rewrite /(?<original>.*)/div$ /$original/index permanent;
        rewrite /h?t?t?p?s?:\/\/w?w?w?\.?gwern\.net\/(?<original>.*)$ /$original permanent;
        rewrite /doc/genetics/selection/artificial/apple/genetics/selection/.*/(?<original>.*.pdf)$ /doc/genetics/selection/artificial/apple/$original permanent;
        rewrite ^/doc/psychology/inner-monologue/(?<old>.*)$ /doc/psychology/inner-voice/$old permanent;
        rewrite ^/doc/www/scribe\.rip/(?<old>.*)$ /doc/www/freedium.cfd/$old permanent;
        rewrite ^/(?<old>.*)~$ /$old permanent;
        rewrite ^/(?<old>.*)\&amp$ /$old permanent;
        rewrite ^/(?<old>.*)/guestbook$ /$old/index permanent;
        rewrite ^/doc/music/music-distraction/(?<old>.*)$ /doc/psychology/music/distraction/$old permanent;
        rewrite ^/doc/cs/scheme/(?<old>.*)$ /doc/cs/lisp/scheme/$old permanent;
        rewrite ^/doc/sociology/preference-falsification/(?<old>.*)$ /doc/sociology/false-preference/$old permanent;
        rewrite ^/(?<before>.*)/1\*(?<after>.*)$ /$before/$after permanent;
        rewrite ^/(?<before>.*)\*(?<after>.*)$   /$before$after  permanent;
        rewrite ^/doc/www/.*madasafish.com/(?<after>.*)$ http://members.madasafish.com/$after permanent;
        rewrite ^/doc/www/members.madasafish.com/Location_Location/(?<after>.*)$ http://members.madasafish.com/Location_Location/$after permanent;
        rewrite ^/doc/www/members.madasafish.com/map_of-Hogwarts/(?<after>.*)$ http://members.madasafish.com/map_of-Hogwarts/$after permanent;
        rewrite ^/(?<before>.*)%E2%80%8B%E2%80%8B(?<after>.*)$ /$before$after permanent;
        rewrite ^/(?<before>.*)%25252520(?<after>.*)$ /$before-$after permanent;

        rewrite ^/metadata/annotations/doc/(?<original>[0-9a-zA-Z].*) /metadata/annotation/%2Fdoc/$original permanent;
        rewrite ^/metadata/annotations/(?<original>[0-9a-ce-gi-z].*) /metadata/annotation/%2F$original permanent;
        rewrite ^//(?<original>.+)$ /$original permanent;
        # rewrite ^/(?<original>[A-Za-z0-9-]+)/$ /$original permanent; # try to deal with the spiders bruteforcing appending '/' to all essays like '/self-decrypting/' ; although this breaks stuff like '/nootropic' by creating an infinite loop...?
        # rewrite ^/(?<original>[A-Za-z0-9-]+/[a-z0-9-]+)/$ /$original permanent; # BAD creates loops!

        # Gitit dot-page → Markdown .md migration:
        rewrite /(?<original>.*)\.page$ /$original.md permanent;

        ## it's very hard to fix broken '#' anchors since it's client-side, the web server isn't support to see it
        ## and I couldn't find any Nginx solution I understood, so we just strip the trailing garbage:
        rewrite ^(?<u>.*)\#(?<v>.*)     $u permanent;

        ## Strip all query-parameters which are meaningless on Gwern.net & only yield 404s
        ## NOTE: '$args' = '?args' in 'foo.com/bar?args'
        set $args '';

        ## when browsing files like `/doc/foo/2024-bar.pdf`, any experienced web user might try to browse by going to the index `/doc/foo/index.html`, but we actually put them at `/doc/foo/index`; instead of trying to create thousands of redirects for every possible error, we tell nginx to check for the /index file existing and if it does, load that instead
        try_files $uri $uri/ @fallback;
     }

     # backslashed URL requests like 'https://gwern.net/dnb-faq%5C' are always broken, but also very hard to match normally, for some reason, and so result in a huge number of spurious errors. Try to catch them all here.
     # (Why are there so many? My guess is this is downstream of bad code, where the authors failed to correctly escape the escaping characters and so wind up spamming backslashes everywhere.)
     # NOTE: the triple-backslash is not an error. That is in fact how you match a single '\' literal, due to Lua being involved: <https://stackoverflow.com/questions/27189195/nginx-rewrite-backslashes-to-forward-slashes/27267428#27267428>. This is hard to find out because '\\' and '\' and '\\\\' all just fail silently or cause subtle bugs.
     if ($uri ~ "^(?<original>.*?)\\\$") {
        return 301 $scheme://$host$original;
    }
     # if ($request_uri ~ "^(?<original>.*?)[\\%5C]+$") {
     #    return 301 $scheme://$host$original;
     # }

     # Enable redirection? TODO: I don't understand why this seems to be necessary in addition to `map`
     if ($new_uri != "" ) {
        rewrite ^(.*)$ $new_uri permanent;
      }

    location @fallback {
        # Handle the specific fallback for missing index.html or index.htm
        rewrite ^(/.+)/index\.html?$ $1/index permanent;
    }

}

