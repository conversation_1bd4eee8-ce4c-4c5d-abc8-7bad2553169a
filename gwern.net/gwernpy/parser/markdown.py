"""
Markdown parser for gwern-python.

This module handles parsing of Markdown content, including metadata extraction
and custom syntax extensions.
"""
from typing import Dict, Any, Optional, List, Tuple, Union, Callable
import re
from pathlib import Path

try:
    import markdown_it
    from markdown_it.extensions.front_matter import front_matter_plugin
    from markdown_it.extensions.footnote import footnote_plugin
    from markdown_it.extensions.table import table_plugin
except ImportError:
    # Fallback to indicate markdown-it-py is not installed
    markdown_it = None

from .frontmatter import FrontMatterParser, FrontMatterError


class MarkdownParserError(Exception):
    """Exception raised for errors in Markdown parsing."""
    pass


class MarkdownParser:
    """Parser for Markdown content with metadata extraction."""
    
    def __init__(self, 
                 extensions: Optional[List[str]] = None,
                 required_fields: Optional[List[str]] = None,
                 default_values: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the Markdown parser.
        
        Args:
            extensions: Optional list of Markdown extensions to enable
            required_fields: Optional list of required front matter fields
            default_values: Optional dictionary of default values for missing fields
        
        Raises:
            ImportError: If markdown-it-py is not installed
        """
        if markdown_it is None:
            raise ImportError(
                "markdown-it-py is required for Markdown parsing. "
                "Install it with 'pip install markdown-it-py'"
            )
        
        self.extensions = extensions or ['tables', 'footnotes', 'code_hilite']
        
        # Initialize front matter parser
        self.frontmatter_parser = FrontMatterParser(
            required_fields=required_fields,
            default_values=default_values
        )
        
        # Initialize markdown-it parser with plugins
        self.md = markdown_it.MarkdownIt()
        
        # Add plugins based on extensions
        if 'front_matter' in self.extensions or 'yaml' in self.extensions:
            self.md.use(front_matter_plugin)
        
        if 'tables' in self.extensions:
            self.md.use(table_plugin)
            
        if 'footnotes' in self.extensions:
            self.md.use(footnote_plugin)
        
        # TODO: Add more plugins based on extensions
    
    def parse(self, content: str) -> Tuple[Dict[str, Any], str]:
        """
        Parse Markdown content and extract metadata.
        
        Args:
            content: Raw Markdown content with optional front matter
            
        Returns:
            Tuple containing (metadata dict, processed markdown content)
            
        Raises:
            MarkdownParserError: If there's an error parsing the content
        """
        try:
            # Extract front matter
            metadata, content_without_frontmatter = self.frontmatter_parser.extract(content)
            
            # Process the Markdown content
            processed_content = content_without_frontmatter
            
            return metadata, processed_content
        except FrontMatterError as e:
            raise MarkdownParserError(f"Front matter error: {str(e)}") from e
        except Exception as e:
            raise MarkdownParserError(f"Error parsing Markdown: {str(e)}") from e
    
    def render_html(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Render Markdown content to HTML.
        
        Args:
            content: Markdown content to render
            metadata: Optional metadata to include in rendering context
            
        Returns:
            Rendered HTML content
            
        Raises:
            MarkdownParserError: If there's an error rendering the content
        """
        try:
            # If metadata is provided, we assume content doesn't have front matter
            if metadata is None:
                metadata, content = self.parse(content)
            
            # Render Markdown to HTML
            html = self.md.render(content)
            
            return html
        except Exception as e:
            raise MarkdownParserError(f"Error rendering Markdown to HTML: {str(e)}") from e
    
    def parse_file(self, file_path: Union[str, Path]) -> Tuple[Dict[str, Any], str]:
        """
        Parse a Markdown file and extract metadata.
        
        Args:
            file_path: Path to the Markdown file
            
        Returns:
            Tuple containing (metadata dict, processed markdown content)
            
        Raises:
            MarkdownParserError: If there's an error parsing the file
            FileNotFoundError: If the file doesn't exist
        """
        try:
            path = Path(file_path)
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.parse(content)
        except FileNotFoundError:
            raise FileNotFoundError(f"Markdown file not found: {file_path}")
        except Exception as e:
            raise MarkdownParserError(f"Error parsing Markdown file: {str(e)}") from e
