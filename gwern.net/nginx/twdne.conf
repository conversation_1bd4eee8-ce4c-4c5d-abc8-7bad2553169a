server {
    listen 80;
#   listen [::]:80 default_server;

    root /home/<USER>/thiswaifudoesnotexist.net;

    # index index.html;

    server_name thiswaifudoesnotexist.net www.thiswaifudoesnotexist.net;

    location / {
        # First attempt to serve request as file, then
        # as directory, then fall back to displaying a 404.
        try_files $uri $uri/ =404;

        ## allow directory browsing, particularly useful for /docs/*
        autoindex on;
    }
}
