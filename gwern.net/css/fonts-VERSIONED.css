@charset "UTF-8";

/******************/
/* SOURCE SERIF 4 */
/******************/

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 200;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-ExtraLight.otf?v=1742261860') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 200;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-ExtraLightItalic.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 300;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Light.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 300;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-LightItalic.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 400;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Regular.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 400;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-RegularItalic.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 600;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Semibold.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 600;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-SemiboldItalic.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 700;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Bold.otf?v=1742261860') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 700;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-BoldItalic.otf?v=1742261860') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 900;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Black.otf?v=1742261860') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 900;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-BlackItalic.otf?v=1742261860') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

/*****************/
/* SOURCE SANS 3 */
/*****************/

@font-face {
	font-family: 'Source Sans 3';
	font-weight: 200;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-ExtraLight.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 200;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-ExtraLightItalic.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 300;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Light.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 300;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-LightItalic.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Regular.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-RegularItalic.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 500;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Medium.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 500;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-MediumItalic.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 600;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Semibold.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 600;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-SemiboldItalic.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 700;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Bold.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 700;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-BoldItalic.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 900;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Black.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 900;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-BlackItalic.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}

/*****************/
/* IBM PLEX MONO */
/*****************/

@font-face {
	font-family: 'IBM Plex Mono';
	font-weight: normal;
	font-style: normal;
	src: url('/static/font/ibm-plex-mono/IBMPlexMono-Regular.otf?v=1606177027') format('opentype');
	font-display: swap;
}
@font-face {
	font-family: 'IBM Plex Mono';
	font-weight: normal;
	font-style: italic;
	src: url('/static/font/ibm-plex-mono/IBMPlexMono-RegularItalic.otf?v=1606177027') format('opentype');
	font-display: swap;
}
@font-face {
	font-family: 'IBM Plex Mono';
	font-weight: bold;
	font-style: normal;
	src: url('/static/font/ibm-plex-mono/IBMPlexMono-Bold.otf?v=1606177026') format('opentype');
	font-display: swap;
}
@font-face {
	font-family: 'IBM Plex Mono';
	font-weight: bold;
	font-style: italic;
	src: url('/static/font/ibm-plex-mono/IBMPlexMono-BoldItalic.otf?v=1606177027') format('opentype');
	font-display: swap;
}

/***********/
/* QUIVIRA */
/***********/

@font-face {
	font-family: 'Quivira';
	font-weight: normal;
	src: url('/static/font/quivira/Quivira-SUBSETTED.ttf?v=1734481577') format('truetype');
	font-display: swap;
}

/**************/
/* NOTO EMOJI */
/**************/

@font-face {
	font-family: 'Noto Emoji';
	font-weight: normal;
	src: url('/static/font/noto-emoji/NotoEmoji-Bold-SUBSETTED.ttf?v=1734481577') format('truetype');
	font-display: swap;
}

/************************/
/* DEUTSCHE ZIERSCHRIFT */
/************************/

@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-A.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0041;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-B.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0042;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-C.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0043;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-D.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0044;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-E.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0045;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-F.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0046;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-G.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0047;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-H.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0048;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-I.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0049;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-J.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004A;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-K.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004B;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-L.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004C;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-M.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004D;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-N.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004E;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-O.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004F;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-P.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0050;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-Q.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0051;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-R.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0052;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-S.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0053;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-T.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0054;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-U.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0055;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-V.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0056;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-W.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0057;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-X.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0058;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-Y.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0059;
}
@font-face {
	font-family: 'Deutsche Zierschrift';
	src: url('/static/font/dropcap/de-zs/DeutscheZierschrift-Z.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+005A;
}

/*********/
/* YINIT */
/*********/

@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-A.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0041;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-B.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0042;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-C.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0043;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-D.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0044;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-E.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0045;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-F.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0046;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-G.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0047;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-H.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0048;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-I.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0049;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-J.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004A;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-K.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004B;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-L.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004C;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-M.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004D;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-N.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004E;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-O.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004F;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-P.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0050;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-Q.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0051;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-R.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0052;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-S.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0053;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-T.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0054;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-U.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0055;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-V.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0056;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-W.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0057;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-X.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0058;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-Y.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0059;
}
@font-face {
	font-family: 'Yinit';
	src: url('/static/font/dropcap/yinit/Yinit-Z.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+005A;
}

/*******************/
/* GOUDY INITIALEN */
/*******************/

@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-A.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0041;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-B.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0042;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-C.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0043;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-D.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0044;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-E.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0045;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-F.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0046;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-G.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0047;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-H.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0048;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-I.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0049;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-J.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+004A;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-K.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+004B;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-L.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+004C;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-M.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+004D;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-N.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+004E;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-O.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+004F;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-P.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0050;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-Q.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0051;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-R.ttf?v=1734481576') format('truetype');
	font-display: swap;
	unicode-range: U+0052;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-S.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0053;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-T.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0054;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-U.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0055;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-V.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0056;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-W.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0057;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-X.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0058;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-Y.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0059;
}
@font-face {
	font-family: 'Goudy Initialen';
	src: url('/static/font/dropcap/goudy/GoudyInitialen-Z.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+005A;
}

/*********************/
/* CHESHIRE INITIALS */
/*********************/

@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-A.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0041;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-B.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0042;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-C.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0043;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-D.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0044;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-E.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0045;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-F.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0046;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-G.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0047;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-H.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0048;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-I.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0049;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-J.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004A;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-K.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004B;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-L.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004C;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-M.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004D;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-N.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004E;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-O.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+004F;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-P.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0050;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-Q.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0051;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-R.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0052;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-S.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0053;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-T.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0054;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-U.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0055;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-V.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0056;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-W.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0057;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-X.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0058;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-Y.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+0059;
}
@font-face {
	font-family: 'Cheshire Initials';
	src: url('/static/font/dropcap/cheshire/Cheshire-Initials-Z.ttf?v=1734481573') format('truetype');
	font-display: swap;
	unicode-range: U+005A;
}

/*********************/
/* KANZLEI INITIALEN */
/*********************/

@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-A.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0041;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-B.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0042;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-C.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0043;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-D.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0044;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-E.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0045;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-F.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0046;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-G.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0047;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-H.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0048;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-I.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0049;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-J.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004A;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-K.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004B;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-L.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004C;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-M.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004D;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-N.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004E;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-O.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+004F;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-P.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0050;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-Q.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0051;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-R.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0052;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-S.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0053;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-T.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0054;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-U.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0055;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-V.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0056;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-W.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0057;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-X.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0058;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-Y.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+0059;
}
@font-face {
	font-family: 'Kanzlei Initialen';
	src: url('/static/font/dropcap/kanzlei/Kanzlei-Initialen-Z.ttf?v=1734481577') format('truetype');
	font-display: swap;
	unicode-range: U+005A;
}

/*******************/
/* BLACKMOOR PLAIN */
/*******************/

@font-face {
	font-family: 'Blackmoor Plain';
	font-weight: normal;
	src: url('/static/font/blackletter/BlackmoorPlain.otf?v=1734481573') format('opentype');
	font-display: swap;
}

/******************/
/* CLOISTER BLACK */
/******************/

@font-face {
	font-family: 'Cloister Black';
	font-weight: normal;
	src: url('/static/font/blackletter/CloisterBlack.ttf?v=1734481573') format('truetype');
	font-display: swap;
}

/********************/
/* DEUTSCHE SCHRIFT */
/********************/

@font-face {
	font-family: 'Deutsche Schrift';
	font-weight: normal;
	src: url('/static/font/blackletter/DeutscheSchrift.ttf?v=1734481573') format('truetype');
	font-display: swap;
}

/*************************/
/* ENGRAVERS OLD ENGLISH */
/*************************/

@font-face {
	font-family: 'Engravers Old English';
	font-weight: normal;
	src: url('/static/font/blackletter/EngraversOldEnglish-Regular.ttf?v=1734481573') format('truetype');
	font-display: swap;
}
@font-face {
	font-family: 'Engravers Old English';
	font-weight: bold;
	src: url('/static/font/blackletter/EngraversOldEnglish-Bold.ttf?v=1734481573') format('truetype');
	font-display: swap;
}

/************************/
/* GREAT PRIMER UNCIALS */
/************************/

@font-face {
	font-family: 'Great Primer Uncials';
	font-weight: normal;
	src: url('/static/font/blackletter/GreatPrimerUncials.otf?v=1734481573') format('opentype');
	font-display: swap;
}

/********************/
/* GUTENBERG GOTHIC */
/********************/

@font-face {
	font-family: 'Gutenberg Gothic';
	font-weight: normal;
	src: url('/static/font/blackletter/GutenbergGothic.ttf?v=1734481573') format('truetype');
	font-display: swap;
}

/****************/
/* HANSA GOTHIC */
/****************/

@font-face {
	font-family: 'Hansa Gothic';
	font-weight: normal;
	src: url('/static/font/blackletter/HansaGothic.ttf?v=1734481573') format('truetype');
	font-display: swap;
}
