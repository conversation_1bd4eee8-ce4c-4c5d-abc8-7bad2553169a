/**********/
/* IMAGES */
/**********/

:root {
	--GW-image-single-black-star-svg: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><path d="M 5,0 C 4,4 4,4 0,5 4,6 4,6 5,10 6,6 6,6 10,5 6,4 6,4 5,0 Z" /></svg>');
	--GW-image-single-white-star-on-black-circle-svg: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><path d="M 5 0 C 6 4 6 4 10 5 A 5 5 0 0 0 5 0 z M 5 0 A 5 5 0 0 0 0 5 C 4 4 4 4 5 0 z M 0 5 A 5 5 0 0 0 5 10 C 4 6 4 6 0 5 z M 5 10 A 5 5 0 0 0 10 5 C 6 6 6 6 5 10 z"/></svg>');
	--GW-image-single-white-star-rotated-svg: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><path d="M 1.25 1.25 C 3.5 5 3.5 5 1.25 8.75 C 5 6.5 5 6.5 8.75 8.75 C 6.5 5 6.5 5 8.75 1.25 C 5 3.5 5 3.5 1.25 1.25 z M 3 3 C 5 4.2 5 4.2 7 3 C 5.8 5 5.8 5 7 7 C 5 5.8 5 5.8 3 7 C 4.2 5 4.2 5 3 3 z"/></svg>');
	--GW-image-single-white-star-svg: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><path d="M 5 0 C 4 4 4 4 0 5 C 4 6 4 6 5 10 C 6 6 6 6 10 5 C 6 4 6 4 5 0 z M 5 2.5 C 5.5 4.5 5.5 4.5 7.5 5 C 5.5 5.5 5.5 5.5 5 7.5 C 4.5 5.5 4.5 5.5 2.5 5 C 4.5 4.5 4.5 4.5 5 2.5 z"/></svg>');
	--GW-image-checkerboard-000-fff-2x-gif: url('data:image/gif;base64,R0lGODlhBAAEAPAAMQAAAP///ywAAAAABAAEAAACBgQShqgJBQA7');
	--GW-image-checkerboard-777-fff-2x-gif: url('data:image/gif;base64,R0lGODlhBAAEAPAAMXd3d////ywAAAAABAAEAAACBgQShqgJBQA7');
	--GW-image-checkerboard-888-000-2x-gif: url('data:image/gif;base64,R0lGODlhBAAEAPAAMQAAAIiIiCwAAAAABAAEAAACBkwAhqgZBQA7');
	--GW-image-checkerboard-bfbfbf-000-2x-gif: url('data:image/gif;base64,R0lGODlhBAAEAPAAMQAAAL+/vywAAAAABAAEAAACBkwAhqgZBQA7');
	--GW-image-pattern-dotted-161616-on-252525-2x-gif: url('data:image/gif;base64,R0lGODlhEAAQAPAAMRYWFiUlJSwAAAAAEAAQAAACHowPcKgb6V6LtFoDXV6b3Q9aHTZ5WhmmJodK0ajGBQA7');
	--GW-image-pattern-dotted-161616-on-3e3e3e-2x-gif: url('data:image/gif;base64,R0lGODlhEAAQAPAAMRYWFj4+PiwAAAAAEAAQAAACHowPcKgb6V6LtFoDXV6b3Q9aHTZ5WhmmJodK0ajGBQA7');
	--GW-image-pattern-dotted-e6e6e6-on-fff-2x-gif: url('data:image/gif;base64,R0lGODlhEAAQAPAAMebm5v///ywAAAAAEAAQAAACHowPcKgb6V6LtFoDXV6b3Q9aHTZ5WhmmJodK0ajGBQA7');
	--GW-image-pattern-dotted-fff-on-e6e6e6-2x-gif: url('data:image/gif;base64,R0lGODlhEAAQAPAAMebm5v///ywAAAAAEAAQAAACHoQfcagL6V6LtFoDXV6b3Q9aHTZ5WhmmJodK0ajGBQA7');
}

/*************/
/* VARIABLES */
/*************/

:root {
    --GW-serif-font-stack: "Source Serif 4", "Apple Garamond", "Baskerville", "Libre Baskerville", "Droid Serif", "Times New Roman", "Times", serif, "Noto Emoji", "Quivira";
    --GW-sans-serif-font-stack: "Source Sans 3", "Lucida Sans Unicode", "Helvetica", "Trebuchet MS", sans-serif, "Noto Emoji", "Quivira";
    --GW-monospaced-font-stack: "IBM Plex Mono", "Liberation Mono", "Consolas", "Courier", monospace, "Noto Emoji", "Quivira";

    --GW-body-text-font-size: 20px;

    --GW-body-max-width: 935px;
    --GW-body-side-padding: 20px;
}
@media all and (max-width: 649px) {
    :root {
        --GW-body-text-font-size: 18px;

        --GW-body-side-padding: 16px;
    }
}


/***********/
/* GENERAL */
/***********/

html,
.shadow-body {
    --base-font-size: var(--GW-body-text-font-size);
    --background-color: var(--GW-body-background-color);

    background-color: var(--background-color);
    color: var(--GW-body-text-color);
    font-weight: 400;
    font-family: var(--GW-serif-font-stack);
    font-size: var(--base-font-size);
}


/****************/
/* ADAPTIVENESS */
/****************/

/*  Utility classes: show something only on mobile (`.desktop-not`)
    or only on desktop (`.mobile-not`).
 */
@media all and (max-width: 649px) {
    .mobile-not {
        display: none !important;
    }
}
@media all and (min-width: 650px) {
    .desktop-not {
        display: none !important;
    }
}



/**********/
/* LAYOUT */
/**********/

html,
body {
    margin: 0;
    padding: 0;
}

main {
    position: relative;
    box-sizing: border-box;
	min-height: 100vh;
    max-width: var(--GW-body-max-width);
    margin: 0 auto;
    padding: 1rem
    		 var(--GW-body-side-padding)
    		 1.25rem
    		 var(--GW-body-side-padding);
	display: flex;
	flex-flow: column;
}
@media all and (max-width: 649px) {
	main {
		overflow: hidden;
		padding: 1rem
				 var(--GW-body-side-padding)
				 4.25rem
				 var(--GW-body-side-padding);
	}
}


/***********/
/* SIDEBAR */
/***********/

#sidebar {
	position: relative;
    display: flex;
}

/*  Sidebar links (including logo).
 */
#sidebar a {
    border: 1px dotted currentColor;
    text-align: center;
    margin: 1px 0 1px 2px;
}

/*  Logo.
 */
#sidebar a.logo {
    display: flex;
    align-items: center;
    margin: 1px 0;
}
#sidebar a.logo .logo-image {
    width: var(--logo-width);
    height: calc(var(--logo-width) * 94/80);
}

/*  Sidebar links (not including logo).
 */
#sidebar .sidebar-links {
    flex: 1 1 100%;
    display: flex;
    flex-flow: row wrap;
}
#sidebar .sidebar-links a {
    flex: 1 1 auto;
    padding: 0.25em 0.75em;
}

@media all and (max-width: 649px) {
	#sidebar {
		margin-right: -1px;
	}

    /*  Logo.
     */
    #sidebar a.logo {
    	margin: 1px 0 1px -1px;
        padding: 0.5em 0.375em;
    }
    #sidebar a.logo .logo-image {
        --logo-width: 2.5em;
    }

    /*  Sidebar links (not including logo).
     */
    #sidebar .sidebar-links a {
        font-variant-caps: small-caps;
    }

    #sidebar .sidebar-links a[href="/about"],
    #sidebar .sidebar-links a.site {
        order: -3;
        flex: 1 1 20%;
    }
    #sidebar .sidebar-links a[href="/me"],
    #sidebar .sidebar-links a.me {
        order: -1;
        flex: 1 1 20%;
    }
    #sidebar .sidebar-links a[href="/changelog"],
    #sidebar .sidebar-links a.new-essays {
        order: 1;
        flex: 1 1 35%;
    }
    #sidebar .sidebar-links a[href="/doc-newest-index"],
    #sidebar .sidebar-links a.new-links {
        order: 2;
        flex: 1 1 35%;
    }
    #sidebar .sidebar-links a.patreon {
        order: -2;
        flex: 1 1 20%;
    }
}

@media all and (min-width: 650px) {
    /*  Sidebar links (including logo).
     */
    #sidebar a {
        color: var(--GW-nav-header-link-color);
    }
    #sidebar a:hover {
        color: var(--GW-nav-header-link-hover-color);
    }

    /*  Sidebar links (not including logo).
     */
    #sidebar .sidebar-links a {
        text-transform: uppercase;
        font-weight: 600;
    }

	@media all and (max-width: 779px) {
		#sidebar .sidebar-links .mobile-not {
			display: none;
		}
	}
    @media all and (max-width: 1179px) {
        /*  Logo.
         */
        #sidebar a.logo {
            padding: 0.25em 0.375em;
        }
        #sidebar a.logo .logo-image {
            --logo-width: 1em;
        }
    }
    @media all and (min-width: 1180px) {
        /*  Logo.
         */
        #sidebar a.logo {
            position: absolute;
            right: 100%;
            margin: 0 0.75em 0 0;
            padding: 0.125em;
            border: none;
            opacity: 0.5;
        }
        #sidebar a.logo:hover,
        #sidebar a.logo.bright {
            opacity: 1.0;
        }
        #sidebar a.logo.fading {
            transition:
                opacity 1s ease;
        }
        #sidebar a.logo .logo-image {
            --logo-width: 4em;
        }

        /*  Sidebar links (not including logo).
         */
        #sidebar .sidebar-links a:first-child {
            margin-left: 0;
        }
    }
}


/*********************/
/* ARTICLE CONTAINER */
/*********************/

article {
    margin-top: 1.75rem;
	flex: 1 1 auto;
}


/***************/
/* PAGE HEADER */
/***************/

@media all and (max-width: 649px) {
    header {
        margin: 0 1rem 1.75rem 1rem;
    }
}
@media all and (min-width: 650px) {
    header {
        margin: 0.375rem 2rem 1.75rem 2rem;
    }
}

header h1 {
    margin: 0;
    text-align: center;
    line-height: 1.15;
    text-transform: none;
    font-variant-caps: small-caps;
    font-size: 2.5em;
    font-weight: 600;
    letter-spacing: -1px;
}
@media all and (max-width: 649px) {
    header h1 {
        font-size: 2em;
    }
}

/*  Source Serif 4 has no italic small caps, sadly.
 */
header h1 em {
    font-variant-caps: normal;
}


/***********************/
/* PAGE METADATA BLOCK */
/***********************/

#page-metadata {
    --text-alignment: center;
    --text-hyphenation: none;
    --text-indent: 0em;

    line-height: 1.5;
}

#page-metadata:not(:last-child) {
    margin-bottom: 2.25rem;
}

#page-metadata > * + * {
    margin-top: 0.75em;
}

/*************/
/*  Page tags.
 */
#page-metadata .link-tags {
    font-family: var(--GW-monospaced-font-stack);
    font-style: italic;
}
#page-metadata .link-tags a {
    white-space: nowrap;
    margin-right: 0.05em;
    background-position: 0% calc(100% - 1px);
}
#page-metadata .link-tags a:nth-child(n+2) {
    margin-left: 0.10em;
}

/********************/
/*  Page description.
 */
/*  Override normal handling of italics: before, we wrapped descriptions in
    `<em>`; however, they compile to HTML which can contain italics of their
    own (eg. book titles). This causes HTML Tidy to warn about nested italics
    (which is valid HTML but *usually* means you’ve made an error - this often
    triggers when I mess up bolding, for example, like on /gpt-3). So we
    removed the wrapper in favor of the above `font-style: italic`. However,
    now the titles don’t unitalicize automatically like they would for nested
    italics! So we force italicized titles back to normal Roman font to match
    the expected typographic convention.
 */
#page-metadata .page-description {
    font-style: italic;
}
#page-metadata .page-description em {
    font-style: normal;
}

/************************/
/*  Page metadata fields.
 */
#page-metadata .page-metadata-fields {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
}
#page-metadata .page-metadata-fields > span {
    white-space: nowrap;
}
/*  Interpunct separators for the metadata fields; looks nicer than semicolons
    or slashes.
 */
#page-metadata .page-metadata-fields > span::after {
    content: "\00B7"; /* interpunct MIDDLE DOT (U+00B7) '·'; TODO: replace with BULLET .separator-inline? */
    margin: 0 0.75em;
}
/*  No separator at line end.
 */
#page-metadata .page-metadata-fields .page-importance::after,
#page-metadata .page-metadata-fields > span:last-child::after {
    display: none;
}
/*	Adjust icon spacing.
 */
#page-metadata .page-metadata-fields .progress-indicator-icon {
	margin: 0 0 0 0.25em;
}
/*	Adjust z-order of text elements.
 */
#page-metadata .page-metadata-fields a + span {
	position: relative;
	z-index: 1;
}

/*  Flex order.
 */
/* date, author, status */
#page-metadata .page-metadata-fields .page-author,
#page-metadata .page-metadata-fields .page-date-range,
#page-metadata .page-metadata-fields .page-status {
    order: 1;
}
/* certainty, importance */
#page-metadata .page-metadata-fields .page-confidence,
#page-metadata .page-metadata-fields .page-importance {
    order: 3;
}
/* backlinks, similar, bibliography */
#page-metadata .page-metadata-fields .page-backlinks,
#page-metadata .page-metadata-fields .page-similars,
#page-metadata .page-metadata-fields .page-link-bibliography {
    order: 5;
}

/*  Author.
 */
#page-metadata .page-metadata-fields .page-author {
    white-space: normal;
}

/*	Importance.
 */
#page-metadata .page-metadata-fields .page-importance {
	font-variant-numeric: tabular-nums;
}

/*=------------=*/
/*= Responsive =*/
/*=------------=*/

@media all and (min-width: 650px) {
    #page-metadata {
        font-size: calc((19/20) * var(--GW-body-text-font-size));
        line-height: calc(1.5 / (19/20));
    }

    /*  Page tags.
     */
    #page-metadata .link-tags {
        font-size: calc((16/20) * var(--GW-body-text-font-size));
    }

    /*  Page metadata fields.
     */
    #page-metadata .page-metadata-fields {
        font-size: calc((18/20) * var(--GW-body-text-font-size));
    }
    /* line break in one place */
    #page-metadata .page-metadata-fields::after {
        content: "";
        width: 100%;
        height: calc(1em * (4/20));
    }
    /* break between certainty/importance and backlinks/similar/bibliography */
    #page-metadata .page-metadata-fields::after {
        order: 4;
    }
}
@media all and (max-width: 649px) {
    #page-metadata {
        font-size: calc((17/18) * var(--GW-body-text-font-size));
    }

    /*  Page tags.
     */
    #page-metadata .link-tags {
        font-size: calc((16/18) * var(--GW-body-text-font-size));
    }

    /*  Page description.
     */
    #page-metadata .page-description {
        line-height: calc(25/17);
    }

    /*  Page metadata fields.
     */
    #page-metadata .page-metadata-fields {
        display: flex;
        flex-flow: row wrap;
        justify-content: center;
        font-size: calc((16/18) * var(--GW-body-text-font-size));
        line-height: calc(25/16);
    }
    /* line breaks in two places */
    #page-metadata .page-metadata-fields::before,
    #page-metadata .page-metadata-fields::after {
        content: "";
        width: 100%;
        height: calc(1em * (3/18));
    }
    /* break between date/status and certainty/importance */
    #page-metadata .page-metadata-fields::before {
        order: 2;
    }
    /* break between certainty/importance and backlinks/similar/bibliography */
    #page-metadata .page-metadata-fields::after {
        order: 4;
    }
    /*  No separator at line end.
     */
    #page-metadata .page-metadata-fields .page-author::after,
    #page-metadata .page-metadata-fields .page-status::after {
        display: none;
    }
    /*  Author (on its own line).
     */
    #page-metadata .page-metadata-fields .page-author {
        flex: 1 1 100%;
        text-align: center;
    }
    #page-metadata .page-metadata-fields .page-author::after {
        content: "";
        display: block;
        height: calc(1em * (3/18));
    }
}


/*********************/
/* TABLE OF CONTENTS */
/*********************/

.TOC:empty {
    display: none;
}

.TOC.hidden {
    display: none;
}

.TOC {
    --text-indent: 0em;
    --text-alignment: left;
    --text-hyphenation: none;

	--background-color: var(--GW-TOC-background-color);
	--border-color: var(--GW-TOC-border-color);

    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    font-family: var(--GW-sans-serif-font-stack);
    line-height: 1.25;
}

/*************************/
/*  TOC for main document.
 */

#TOC {
    padding: 0.625em 0.75em 0.75em 0.75em;
    position: relative;
    z-index: 20;
}
@media all and (min-width: 901px) {
    /*  On desktop, we want the TOC side by side with the content.
     */
    #TOC {
        margin: 0 2.25rem 1rem 0;
        float: left;
        max-width: 285px;
    }
}
@media all and (max-width: 900px) {
    /*  On mobile, we want the TOC to be inline.
     */
    #TOC {
        margin: 2.75rem auto 2.5rem auto;
    }
}

/*********************************/
/*  TOCs on directory index pages.
 */

body[class*='-index'] #TOC {
    margin-top: 2.5rem;
}
@media all and (min-width: 650px) {
    body[class*='-index'] #TOC {
        float: unset;
        max-width: unset;
        margin-right: unset;
    }
}

/*  TOCs with nothing but links (no “Miscellaneous”, no link bibliography).
    (This happens on some directory index pages.)
 */

body[class*='-index'] #TOC.TOC-links-only {
    padding-top: 2.5em;
}

body[class*='-index'] #TOC.TOC-links-only::before {
    content: "Contents";
    position: absolute;
    font-size: 1.125em;
    font-weight: bold;
    top: 0.5em;
    left: 0.75em;
}
@media all and (max-width: 900px) {
    body[class*='-index'] #TOC.TOC-links-only::before {
        left: 0.5em;
    }
}

body[class*='-index'] #TOC.TOC-links-only li {
    font-weight: normal;
    font-size: 0.9em;
}

/******************/
/*  TOC collapsing.
 */

#TOC.collapsed {
    height: 2em;
    padding-top: 0;
    padding-bottom: 0;
}
@media all and (max-width: 900px) {
    #TOC.collapsed {
        margin-top: 2rem;
        background-image: none;
    }
}

#TOC.collapsed + * {
    clear: both;
}

#TOC.collapsed > ul {
    max-height: 0;
    overflow: hidden;
}

/*  Collapsed TOC label.
 */

#TOC.collapsed::before {
    content: "[Contents]";
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    padding: 0 0 0 0.5em;
    display: flex;
    align-items: center;
    z-index: 2;
    pointer-events: none;
    font-size: 1.125em;
    font-weight: bold;
    opacity: 0.5;
}
@media all and (hover: hover) {
    #TOC.collapsed:hover::before {
        opacity: 1.0;
    }
}
@media all and (max-width: 900px) {
    #TOC.collapsed::before {
        padding: 0 0.5em;
        right: 0;
        margin: auto;
        justify-content: center;
    }
}

/*  TOC collapse toggle button.
 */

#TOC .toc-collapse-toggle-button {
    appearance: none;
    border: none;
    background-color: var(--GW-TOC-collapse-button-color);
    position: absolute;
    height: calc(2em - 2px);
    padding: 0.3em 0.4em;
    top: 0;
    right: 0;
    display: flex;
    justify-content: flex-end;
    color: var(--GW-TOC-collapse-button-text-color);
    font-family: var(--GW-sans-serif-font-stack);
    font-weight: inherit;
    font-style: inherit;
    font-size: inherit;
    z-index: 1;
    cursor: pointer;
    visibility: hidden;
}
#TOC.collapsed .toc-collapse-toggle-button {
    width: 100%;
    height: 100%;
    visibility: visible;
}

#TOC .toc-collapse-toggle-button span {
    display: flex;
    width: calc(1.4em - 2px);
    height: 100%;
    justify-content: center;
    align-items: center;
}

#TOC .toc-collapse-toggle-button svg {
    width: 100%;
    height: 100%;
    transform: rotate(-0.25turn);
    transition:
        transform 0.1s ease;
}
#TOC.collapsed .toc-collapse-toggle-button svg {
    transform: rotate(0);
}

@media all and (hover: hover) {
    #TOC .toc-collapse-toggle-button:hover {
        color: var(--GW-TOC-collapse-button-text-hover-color);
        background-color: var(--GW-TOC-collapse-button-hover-color);
        outline: 1px dotted var(--GW-TOC-collapse-button-border-hover-color);
    }
    #TOC:hover .toc-collapse-toggle-button {
        visibility: visible;
    }
}

@media all and (max-width: 649px) {
    /*  In expanded state, place button above.
     */
    #TOC:not(.collapsed) .toc-collapse-toggle-button {
        top: -1.75em;
        right: unset;
        left: -1px;
        justify-content: center;
        border: inherit;
        background-color: inherit;
        margin: auto;
        width: 2em;
        height: 1.75em;
        padding: 3px;
        box-sizing: border-box;
        opacity: 1.0;
    }
    /*  Extended tap area.
     */
    #TOC:not(.collapsed) .toc-collapse-toggle-button::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        left: -100%;
        top: 0;
    }
}

/*=-----------------=*/
/*= TOC list layout =*/
/*=-----------------=*/

.TOC ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

@media all and (max-width: 900px) {
    /*  Divider line between the columns.
     */
    #TOC {
        background-image:
            linear-gradient(var(--GW-TOC-border-color),
                            var(--GW-TOC-border-color));
        background-repeat: no-repeat;
        background-size: 1px 100%;
        background-position: 50% 0;
    }

    /*  Two-column layout.
     */
    #TOC > ul {
        column-count: 2;
        column-gap: 1.25em;
    }
    #TOC ul > li:first-child {
        break-before: avoid-column;
    }

    /*  But not on directory index pages.
     */
    body[class*='-index'] #TOC {
        background-image: none;
    }
    body[class*='-index'] #TOC > ul {
        column-count: 1;
    }
}

.TOC ul ul {
    margin: 0;
    padding-left: 0.7em;
    padding-top: 0.35em;
}

.TOC ul > li {
    font-weight: bold;
    margin: 0 0 0.5em 0;
    padding-left: 1.125em;
    position: relative;
    overflow-wrap: break-word;
}
.TOC ul > li:last-child {
    margin-bottom: 0;
}

@media all and (max-width: 900px) {
    /*  No numbers on mobile.
     */
    #TOC ul > li {
        padding-left: 0;
    }
    #TOC ul > li::before {
        content: none;
    }
}

.TOC ul > li li {
    font-weight: normal;
    font-size: 0.9em;
}

@media all and (max-width: 649px) {
    #TOC ul > li li a {
        padding: 1px 0 0 0;
    }
    #TOC ul > li li li a {
        padding: 2px 0 0 0;
    }
    #TOC ul > li li li li a {
        padding: 3px 0 0 0;
    }
    #TOC ul > li li li li a {
        padding: 4px 0 0 0;
    }
}

/*=------------------=*/
/*= TOC link styling =*/
/*=------------------=*/

.TOC a {
    border: 0;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    padding: 0 0.25rem 0 0;
}
@media all and (hover: hover) {
    .TOC a:hover,
    .markdownBody .TOC a:hover {
        background-color: var(--GW-TOC-link-hover-background-color);
        color: var(--GW-TOC-link-hover-color);
    }
    .TOC a:hover::after,
    .markdownBody .TOC a:hover::after {
        content: "";
        display: inline-block;
        position: absolute;
        left: 100%;
        top: 0;
        background-color: var(--GW-TOC-link-hover-indicator-bar-color);
        width: 0.25em;
        height: 100%;
    }
}
@media all and (hover: none) {
    .TOC a {
        position: relative;
    }
    .TOC a::before {
        content: "";
        position: absolute;
        left: -0.50em;
        top: -0.25em;
        width: calc(100% + 0.75em);
        height: calc(100% + 0.50em);
    }
}

/*  Inline code in TOC links.
 */
.TOC code {
    font-family: inherit;
    font-size: inherit;
    border: none;
    padding: 0;
    background-color: inherit;
}

/*=-------------------------------=*/
/*= Wikipedia-style TOC numbering =*/
/*=-------------------------------=*/

.TOC > ul {
    counter-reset: htoc_1;
}
.TOC > ul > li::before {
    counter-increment: htoc_1;
    content: counter(htoc_1) "\2006  ";
}
.TOC > ul ul {
    counter-reset: htoc_2;
}
.TOC > ul ul > li::before {
    counter-increment: htoc_2;
    content: counter(htoc_1) "." counter(htoc_2) "\2006  ";
}
.TOC > ul ul ul {
    counter-reset: htoc_3;
}
.TOC > ul ul ul > li::before {
    counter-increment: htoc_3;
    content: counter(htoc_1) "." counter(htoc_2) "." counter(htoc_3) "\2006  ";
}
.TOC > ul ul ul ul {
    counter-reset: htoc_4;
}
.TOC > ul ul ul ul li::before {
    counter-increment: htoc_4;
    content: counter(htoc_1) "." counter(htoc_2) "." counter(htoc_3) "." counter(htoc_4) "\2006  ";
}
.TOC > ul ul ul ul ul {
    counter-reset: htoc_5;
}
.TOC > ul ul ul ul ul > li::before {
    counter-increment: htoc_5;
    content: counter(htoc_1) "." counter(htoc_2) "." counter(htoc_3) "." counter(htoc_4) "." counter(htoc_5) "\2006  ";
}
.TOC > ul ul ul ul ul ul {
    counter-reset: htoc_6;
}
.TOC > ul ul ul ul ul ul > li::before {
    counter-increment: htoc_6;
    content: counter(htoc_1) "." counter(htoc_2) "." counter(htoc_3) "." counter(htoc_4) "." counter(htoc_5) "." counter(htoc_6) "\2006  ";
}
.TOC ul > li::before {
    position: absolute;
    right: calc(100% - 1em);
    left: unset;
    top: 0;
    width: 111px;
    height: initial;
    line-height: inherit;
    padding: 0;
    text-align: right;
    font-weight: normal;
    font-variant-numeric: tabular-nums;
    pointer-events: none;
    color: var(--GW-TOC-number-color);
}
.TOC ul > li:hover::before {
    color: var(--GW-TOC-number-hover-color);
}


/****************/
/* MAIN CONTENT */
/****************/

#markdownBody {
	position: relative;
}

.markdownBody {
    --base-block-spacing: 0.25em;

    /*  Try to avoid scrollbars on paragraphs: prevents long unbroken
        un-hyphenatable lines from causing H-scrolling.
     */
    overflow-wrap: break-word;

    /*  Cute ‘old-style’ numerals, look a little nicer inline in text, and
        available natively in the SS fonts:

        https://practicaltypography.com/alternate-figures.html#oldstyle-figures
     */
    font-variant-numeric: oldstyle-nums;

    line-height: var(--line-height);
}

/*  Replaces A/B-test-set average value; the wider the screen, the more
    line-height is necessary, and no one size suits all, so set 4 brackets
    of increasing height.
 */
@media all and (max-width: 649px) {
    .markdownBody {
        --line-height: 1.45;
    }
}
@media all and (max-width: 999px) {
    .markdownBody {
        --line-height: 1.50;
    }
}
@media all and (max-width: 1199px) {
    .markdownBody {
        --line-height: 1.55;
    }
}
@media all and (min-width: 1200px) {
    .markdownBody {
        --line-height: 1.60;
    }
}

/*  `bsm` = “block spacing multiplier”
 */
.markdownBody .block {
    margin-top: calc(var(--base-block-spacing) * var(--bsm));
}


/************/
/* SECTIONS */
/************/

/*  Collapsed sections should not intersect the TOC.
 */
section.collapse.expanded-not {
    display: flow-root;
}

/*  Lazy-load below-fold sections in supported browsers.
    Disabled temporarily pending layout redesign to compensate for layout
    containment. —Obormot 2021-03-21
    */
/*
section.level1:nth-of-type(n+4) {
    content-visibility: auto;
    contain-intrinsic-size: 200vh;
}
 */

/************/
/*  Headings.
 */

.heading {
	display: flow-root;
    margin: 0;
    position: relative;
    line-height: 1;
}
@media all and (min-width: 1180px) {
    .heading {
        margin-left: calc(-1 * var(--base-font-size));
    }
}

/*  H1.
 */

section.level1 > .heading {
    text-align: right;
    font-weight: 600;
    font-variant-caps: small-caps;
    letter-spacing: -0.75px;
    padding: 0 0.05em 0.025em calc(1.25 * var(--base-font-size));
    border-bottom: 1px solid var(--GW-H1-border-color);
}
@media all and (max-width: 1179px) {
    section.level1 > .heading {
        font-size: 1.75em;
    }
}
@media all and (min-width: 1180px) {
    section.level1 > .heading {
        font-size: 2em;
    }
}

section.level1 > .heading em {
    font-variant-caps: normal;
}
section.level1 > .heading em:last-child {
    padding-right: 0.075em;
}

/*  H2.
 */
section.level2 > .heading {
    text-transform: uppercase;
    font-size: 1.4em;
    padding: 0 calc(0.75 * var(--base-font-size)) 0.05em 0;
    border-bottom: 1px dotted var(--GW-H2-border-color);
}

/*  H3.
 */
section.level3 > .heading {
    font-size: 1.35em;
    font-weight: bold;
}

/*  H4.
 */
section.level4 > .heading {
    font-size: 1.2em;
}

/*  H5.
 */
section.level5 > .heading {
    font-size: 1.1em;
}

/*  H6.
 */
section.level6 > .heading {
    font-size: 1.1em;
    font-weight: normal;
    font-style: italic;
}

/*=---------------------------=*/
/*= Misc. styling in headings =*/
/*=---------------------------=*/

/*  No link underlining in headings.
 */
.markdownBody .heading a {
    background: none;
}

/*	No visited link styling in headings.
 */
.markdownBody .heading a:link,
.markdownBody .heading a:visited {
	color: var(--GW-body-link-color);
}
.markdownBody .heading a:hover,
.markdownBody .heading a:visited:hover {
	color: var(--GW-body-link-hover-color);
}

/*  Disable bold within headings.
 */
.heading strong {
    font-weight: inherit;
}

/*  Inline code in headings.
 */
.heading code {
    border: none;
    padding: 0;
    background-color: inherit;
    font-size: inherit;
    font-weight: bold;
    font-variant-caps: normal;
}

/*=------------=*/
/*= /doc/index =*/
/*=------------=*/

body[class*='-index'] section#see-also > .heading a {
    display: none;
}


/**************/
/* PARAGRAPHS */
/**************/

p {
    margin: 0;
}

p.empty-graf {
    height: 0 !important;
    margin: 0 !important;
    visibility: hidden;
}

/*  On wide screens (desktop), use fully-justified text (words get
    broken/hyphenated as necessary to avoid a ‘ragged margin’), overriding
    browser default of ‘flush left, ragged right’ (see
    https://en.wikipedia.org/wiki/Typographic_alignment#Flush_left )

    On mobile/narrow screens, that doesn’t work as well: because browsers do
    greedy layout, justification forces ‘s t r e t c h e d out’ words
    and large spaces, leading to blatant
    https://en.wikipedia.org/wiki/River_(typography) , getting worse the
    narrower the screen (eg. in lists). On wide screens, it isn’t too bad, and
    justification is still better than not, but on mobile it is extremely
    noticeable. Switching to flush left ragged right doesn’t look as nicely
    ‘even’, but it avoids that pathological behavior. (More advanced
    typesetting algorithms like Knuth-Plass
    https://en.wikipedia.org/wiki/Line_wrap_and_word_wrap#Minimum_raggedness
    can handle mobile widths nicely, but no contemporary browser implements
    them natively, and all JS implementations I’ve looked at are buggy.)

    However, do hyphenate at all widths.
*/

.markdownBody p {
    -webkit-hyphens: var(--text-hyphenation);
    hyphens: var(--text-hyphenation);
    text-align: var(--text-alignment);
}

.markdownBody p:not(.first-graf) {
    text-indent: var(--text-indent);
}

/*  Prevent inheritance of indentation property.
 */
.markdownBody p * {
    text-indent: 0em;
}

/*  Specify indentation, alignment, hyphenation.
 */
.markdownBody {
    --text-indent: 1.75em;
    --text-alignment: justify;
    --text-hyphenation: auto;
}
/*  Justification only matters for the wide.
 */
@media all and (max-width: 649px) {
    .markdownBody {
        --text-alignment: left;
    }
}
/*  Larger indentation in main content column on non-phones.
 */
@media all and (min-width: 650px) {
    #markdownBody {
        --text-indent: 2.5em;
    }
}

/**************/
/*  Exceptions.
 */

/*  EPITAPH (eg /LARPing)
 */
.markdownBody p.text-center {
    text-align: center;
    text-indent: 0;
}


/***************/
/* BLOCKQUOTES */
/***************/

blockquote {
    --blockquote-vertical-padding: 0.9em;
    --blockquote-horizontal-padding: 1.25em;
    --blockquote-font-size-scaling-factor: 0.95;

    display: flow-root;
    margin: 0;
    overflow: visible;
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    padding: var(--blockquote-vertical-padding) var(--blockquote-horizontal-padding);
}
@media all and (max-width: 649px) {
    blockquote {
        --blockquote-vertical-padding: 0.7em;
        --blockquote-horizontal-padding: 1em;
    }
}

blockquote > * {
    font-size: calc(var(--blockquote-font-size-scaling-factor) * 1em);
    line-height: calc((var(--line-height) - 0.05) / var(--blockquote-font-size-scaling-factor));
}

/*  Three-level of blockquote emphasis (darkening).
 */
blockquote,
.blockquote-level-1,
.blockquote-level-4 {
    --background-color: var(--GW-blockquote-background-color-level-one);
}
.blockquote-level-2,
.blockquote-level-5 {
    --background-color: var(--GW-blockquote-background-color-level-two);
}
.blockquote-level-3,
.blockquote-level-6 {
    --background-color: var(--GW-blockquote-background-color-level-three);
}

/*  Borders must follow a different cycle.
 */
blockquote,
.blockquote-level-1 {
    --border-color: var(--GW-blockquote-border-color-level-one);
}
.blockquote-level-2,
.blockquote-level-5 {
    --border-color: var(--GW-blockquote-border-color-level-two);
}
.blockquote-level-3,
.blockquote-level-6 {
    --border-color: var(--GW-blockquote-border-color-level-three);
}
.blockquote-level-4 {
    --border-color: var(--GW-blockquote-border-color-level-four);
}

/*  Headings in blockquotes.
 */
@media all and (min-width: 1180px) {
    blockquote .heading {
        margin-left: 0;
    }
}


/*********/
/* LISTS */
/*********/

.list {
    --text-alignment: left;
    --list-left-padding: 2.5em;

    margin: 0;
    padding: 0;
    list-style-type: none;
}
@media all and (max-width: 649px) {
    .list {
        --list-left-padding: 1.75em;
    }
}
@media all and (min-width: 650px) {
    .list.big-list {
        --text-alignment: justify;
    }
}

li {
    position: relative;
    display: flow-root;
    padding: 0 0 0 var(--list-left-padding);
}

/*=-----------------------=*/
/*= Bulleted list markers =*/
/*=-----------------------=*/

ul > li::before {
	--dark-mode-invert-filter: var(--list-bullet-dark-mode-invert-filter);

    content: var(--list-bullet);
    position: absolute;
    z-index: 1;
    display: block;
    left: calc(var(--list-left-padding) - 1.375em);
    top: 0.35em;
    width: 0.75em;
    height: 1em;
    line-height: 1;
    opacity: var(--list-bullet-opacity);
}

ul > li {
    --list-bullet-dark-mode-invert-filter: invert(1);
}

ul,
ul.list-level-1 {
    --list-bullet: var(--GW-image-single-black-star-svg);
    --list-bullet-opacity: 0.65;
}
ul.list-level-2 {
    --list-bullet: var(--GW-image-single-white-star-svg);
    --list-bullet-opacity: 0.85;
}
ul.list-level-3 {
    /*  This rotates into more of a ‘lozenge’ square for variety’s sake.
     */
    --list-bullet: var(--GW-image-single-white-star-rotated-svg);
    --list-bullet-opacity: 0.85;
}

/*=-----------------------=*/
/*= Numbered list markers =*/
/*=-----------------------=*/

/*  This may not be necessary. It definitely causes the `start` HTML list
    property to not work correctly, so if indeed it’s unnecessary otherwise, it
    should be removed. Commenting it out for now until we can confirm that no
    bugs are caused by its absence.
        —SA 2023-07-24
/*
ol {
    counter-reset: list-item;
}
 */

ol > li {
    counter-increment: list-item;
}

ol > li::before {
    position: absolute;
    z-index: 15;
    display: block;
    left: 0;
    top: 0;
    width: calc(var(--list-left-padding) - 0.375em);
    text-align: right;
}

/*  Specific list type overrides. (Used by auto-sequencing, but can also be
    invoked manually from the source.)

    Manual invocation assumes writing plain `<ol>` HTML lists instead of Arabic
    numerals like `<ol type="1">`. Pandoc Markdown defaults to the latter if you
    write '1./2.' etc, but an alternate syntax, '#.' is supported which yields a
    plain `<ol>` which can be more easily styled.
    (See https://pandoc.org/MANUAL.html#extension-startnum .)
*/
ol > li::before,
ol.list-type-decimal > li::before {
    content: counter(list-item, decimal) ".";
}
ol.list-type-lower-alpha > li::before {
    content: counter(list-item, lower-alpha) ".";
}
ol.list-type-upper-alpha > li::before {
    content: counter(list-item, upper-alpha) ".";
}
ol.list-type-lower-roman > li::before {
    content: counter(list-item, lower-roman) ".";
}
ol.list-type-upper-roman > li::before {
    content: counter(list-item, upper-roman) ".";
}
ol.list-type-lower-greek > li::before {
    content: counter(list-item, lower-greek) ".";
}

/*=---------------------------------------------=*/
/*= “Recently modified” black star list markers =*/
/*=---------------------------------------------=*/

.link-modified-recently-list-item {
    --list-bullet: var(--GW-image-single-white-star-on-black-circle-svg);
}
.link-modified-recently-list-item::before {
    opacity: 0.7;
}


/**********/
/* FLOATS */
/**********/
/*  Floating figures (mostly for amusing asides/memes), floating small tables,
    code blocks, blockquotes, etc.
 */

@media all and (min-width: 650px) {
    .markdownBody {
        --float-side-margin: 2em;
    }
    .markdownBody blockquote {
        --float-side-margin: 1.5em;
    }

    div.float,
    figure.float {
        max-width: calc(50% - var(--float-side-margin));
        margin-bottom: calc(var(--float-side-margin) * 0.5);
        position: relative;
        z-index: 1;
    }
    div.float-right,
    figure.float-right {
        float: right;
        margin-right: 0;
        margin-left: var(--float-side-margin);
    }
    div.float-left,
    figure.float-left {
        float: left;
        margin-left: 0;
        margin-right: var(--float-side-margin);
    }
}


/***********/
/* FIGURES */
/***********/

figure {
    display: flow-root;
    margin: 0 auto;
}
@media all and (min-width: 650px) {
    figure {
        max-width: calc(100% - 5em);
    }
}

@media all and (min-width: 650px) {
    /*  Reduce reflow.
     */
    img.float-right,
    img.float-left {
        max-width: calc(50% - var(--float-side-margin));
    }
}

/*******************/
/*  Figure contents.
 */

.figure-outer-wrapper {
    display: flex;
    flex-flow: column;
    margin: auto;
    width: fit-content;
    max-width: 100%;
}

figure img,
figure video,
figure svg {
	position: relative;
    display: block;
    max-height: calc(100vh - 8rem);
    max-width: 100%;
    height: auto;
    width: auto;
    margin: 0 auto;

    /*  This is to prevent overflow of image alt-text in Chrome.
     */
    overflow: hidden;
}

figure img,
figure video {
    outline: 1px solid var(--GW-figure-outline-color);
}

figure.outline-not img,
figure.outline-not video {
    outline: none;
}

figure audio {
    display: block;
    margin: 0 auto;
    height: 54px;
    border-radius: 27px;
    position: relative;
    z-index: 1;
    top: 1px;
    box-sizing: border-box;
    border: 1px solid transparent;
}
/*  Special styles for special browsers.
    */
@supports (-moz-appearance: none) {
    figure audio {
        height: 42px;
        border-radius: 21px;
    }
}
figure audio:hover {
    border-color: var(--GW-figure-outline-color);
    box-shadow:
        0 0 4px 0 var(--GW-figure-outline-color) inset,
        0 0 4px 0 var(--GW-figure-outline-color);
}

/*******************/
/*  Figure captions.
    */

figcaption {
    font-size: 0.9em;
    line-height: 1.50;
    margin: auto;
    padding: 0.5em 0.875em calc(0.5em + 1px) 0.875em;
    width: fit-content;
    background-color: var(--background-color);
}
@media all and (max-width: 649px) {
    figcaption {
        font-size: 0.875em;
    }
}

.markdownBody figcaption {
    --text-alignment: left;
}

figcaption p:first-of-type {
    font-weight: bold;
}

figcaption strong {
    font-weight: inherit;
    font-variant-caps: small-caps;
}

.caption-wrapper {
    display: block;
    outline: 1px solid var(--GW-figure-caption-outline-color);
    margin: 1px auto 0 auto;
    width: 100%;
}

figure.outline-not .caption-wrapper {
    outline: none;
}

/***********************************************************************/
/*  Enable the various `![caption](/doc/foo/bar.jpg){.width-full}` uses;
    analogous to width-full tables
 */
figure.width-full {
    position: relative;
    max-width: unset;
    clear: both;
}

/****************/
/*	Figures misc.
 */

/*  Figure in the page abstract.
 */
.page-abstract .page-thumbnail-figure.float-not {
    max-width: unset;
}


/****************/
/* MARGIN NOTES */
/****************/
/* display inline on mobile/narrow windows; on sufficiently-wide screens, pop out as number-less sidenotes */

.marginnote {
    /* bolding turns out to be too obtrusive for users' liking; italicized both inline & popped-out */
    font-style: italic;
    color: var(--GW-body-text-color);
    font-family: var(--GW-serif-font-stack);
}
.marginnote.inline {
    color: inherit;
    font-variant-caps: initial;
}
#markdownBody .marginnote.sidenote {
    --text-hyphenation: none;
    --background-color: transparent;

    display: flex;
    justify-content: right;
    width: calc(50vw - (var(--GW-body-max-width) / 2 + 96px));
    max-width: var(--GW-sidenotes-max-width);
    right: calc(100% + 64px);
    left: unset;

    /*  We inherit left and right padding from .sidenote class. */
    padding-top: 0.2em;
    padding-bottom: 0.5em;

    font-size: calc(var(--GW-body-text-font-size) * 0.85);
    text-align: left;
    opacity: 0.85;
    z-index: 1;
}
#markdownBody .marginnote.sidenote .marginnote-inner-wrapper {
    display: block;
    width: fit-content;
}

#markdownBody .marginnote.sidenote:hover {
    box-shadow: none;
}
#markdownBody .marginnote.sidenote::after {
    display: none;
}

/*  Margin notes within admonitions need special layout.
    This is based on the width of the admonition icon area.
 */
#markdownBody .admonition .marginnote.sidenote {
    width: calc(50vw - ((var(--GW-body-max-width) + (2.875em / (0.85/0.875)))/2 + 96px));
    right: calc(var(--GW-body-max-width) + (2.875em / (0.85/0.875)) + 64px);
}

/*  Special “icon only” margin notes.
 */
#markdownBody .marginnote.only-icon {
    pointer-events: none;
}
#markdownBody .marginnote.only-icon.inline {
	margin-right: 0.2em;
}
#markdownBody .marginnote.only-icon.sidenote {
	font-size: inherit;
    padding-top: 0.1em;
}

/*******************************************/
/*  Margin note blocks at start of sections.
 */

.markdownBody .margin-notes-block {
    text-align: center;
    hyphens: none;
}

@media all and (min-width: 1497px) {
	#markdownBody .margin-notes-block {
		display: none;
	}
}

.markdownBody .margin-notes-block.hidden {
    display: none;
}

.markdownBody .margin-notes-block .marginnote:nth-last-child(n+2)::after {
    content: "•";
    margin: 0 0.75em;
    opacity: 0.5;
}


/******************/
/* DISPLAY RANDOM */
/******************/

[class*='display-random-'],
[class*='display-random-'] > * {
	display: none;
}
[class*='display-random-'].visible,
[class*='display-random-'].visible > .visible {
	display: initial;
}


/****************/
/* OTHER LAYOUT */
/****************/

.clear-floats {
	clear: both;
}


/*********/
/* LINKS */
/*********/

a {
    text-decoration: none;
    color: var(--GW-body-link-color); /* off-black */
}
/*  Slightly gray out links the reader has already visited, so they know if
    they’ve seen that content before. (A critical affordance in densely
    hyperlinked longform.)
 */
.markdownBody a:hover {
    color: var(--GW-body-link-hover-color);
}
.markdownBody a:visited {
    color: var(--GW-body-link-visited-color);
}
.markdownBody a:visited:hover {
    color: var(--GW-body-link-hover-color);
}

/*=------------------------=*/
/*= Fancy link underlining =*/
/*=------------------------=*/

/*  Tufte CSS for underlining (https://github.com/edwardtufte/tufte-css)
    The advantage of all this CSS linenoise compared to the previous 'text-decoration: none; border-bottom: 1px Solid grey;' solution from http://devhints.wordpress.com/2006/10/24/css-links-with-different-color-underlines/
    is the 'skip-ink': when dealing with characters with 'descenders', like 'y', 'g', 'p', 'q' etc, with regular underlining the bottom stroke overlaps with the line and it's harder to read;
    by adding this text-shadow stuff and backgrounding, a little break is made in the underline to add contrast and keep the descender fully visible and outlined. (The downside is that the overlapping text-shadows can also slightly damage the rendering of slashes & parentheses, which come too close and get partially erased.)

    Unfortunately, we don't want to add underlines to links in the TOC because it clutters it (all the lines are links and are in small font), so we have to avoid styling the TOC, which is difficult.
    I got around this by adding in the Hakyll template an additional <div> just for the body of the Markdown content, excluding the TOC, and changing the Tufte CSS to target *that* instead.

May be able at some point to simplify this using regular link underlining, since CSS4's `text-decoration-skip-ink` by default avoids overlapping with text descenders (but as of Oct 2019, no Edge/IE or Safari support, and only the latest Firefox 70 supports it; maybe in a few years...): https://developer.mozilla.org/en-US/docs/Web/CSS/text-decoration-skip-ink#Browser_Compatibility https://www.caniuse.com/#feat=mdn-css_properties_text-decoration-skip-ink (Right now, Firefox skip-ink looks quite bad: it doesn't skip enough ink, defeating the point, and also positions the underline badly!)
*/

/*  Color-inverted containers.
 */
.markdownBody .colors-invert a {
    color: var(--GW-body-link-inverted-color);
}
.markdownBody .colors-invert a:hover {
    color: var(--GW-body-link-inverted-hover-color);
}
.markdownBody .colors-invert a:visited {
    color: var(--GW-body-link-inverted-visited-color);
}
.markdownBody .colors-invert a:visited:hover {
    color: var(--GW-body-link-inverted-hover-color);
}

.markdownBody a {
    --link-underline-background-color: var(--background-color);
    --link-underline-gradient-line-color: currentColor;

    background-image: linear-gradient(var(--link-underline-gradient-line-color),
                                      var(--link-underline-gradient-line-color));
    background-size: 1px 1px;
    background-repeat: repeat-x;
    background-position: 0% calc(100% - 0.1em);

    /*  Disable oldstyle nums in underlined links because the oldstyle nums are almost subscript-like and overlap */
    font-variant-numeric: lining-nums;
}
.markdownBody a,
.markdownBody a * {
    text-shadow:
         0      0.05em  var(--link-underline-background-color),
         0.05em 0.05em  var(--link-underline-background-color),
        -0.05em 0.05em  var(--link-underline-background-color),
         0.17em 0.05em  var(--link-underline-background-color),
        -0.17em 0.05em  var(--link-underline-background-color),
         0.17em 0       var(--link-underline-background-color),
        -0.17em 0       var(--link-underline-background-color);
}

/*  Links with decoration disabled.
 */
.markdownBody a.decorate-not,
.markdownBody a.decorate-not * {
    background-image: none;
    text-shadow: none;
}

/* eliminate the blurring of headers and links when selecting by overriding the text-shadow: */
::selection {
    text-shadow: none;
    background: var(--GW-text-selection-background-color);
    color: var(--GW-text-selection-color);
}

/*  Prevent code block background color and border from obscuring link
    underlining, for inline <code> elements in links.
 */
a code {
    border-bottom-width: 0;
    word-break: break-all;
    background-color: transparent;
}

/*  Reduce text-shadow overlap partially erasing letters next to smallcaps:
    eg. in `<a href="https://www.microcovid.org/">"microCOVID project"</a>`,
    the ‘o’ in ‘micro’ will be partially erased and look like a ‘c’ without
    some sort of extra spacing.
 */
.markdownBody a .smallcaps {
    margin-left: 0.8px;
}

/*  Like above, but for subscripts and superscripts.
 */
.markdownBody a sub,
.markdownBody a sup {
    padding: 0 0.05em;
}

/*  Prevent text-shadow of italics in links from creating visual glitches
    (also fixes occasional link icon overlaps, eg. a WP link to ‘MGS:V’ where
    the right-tilted ‘V’ would touch the WP ‘W’ link icon).
 */
.markdownBody em {
    margin-right: 0.1em;

    /*  Source Serif 4 has no italic small caps.
     */
    font-variant-caps: normal;
}

/*  Inline SVG icons.
    (These need to be in `initial.css` because some of them are used in the
     opening paragraphs of /index.)
 */
.inline-icon {
    --link-underline-background-color: var(--background-color);

	--icon-opacity: 1.00;
	--icon-width: 1.00em;
	--icon-height: 1.00em;
	--icon-offset-x: 0.00em;
	--icon-offset-y: 0.00em;

    position: relative;
    top: calc(0.05em + var(--icon-offset-y));
    left: var(--icon-offset-x);
    background-color: var(--link-underline-background-color);
	padding-left: calc(var(--icon-width) + 0.2em);
}

.inline-icon::before {
	--dark-mode-invert-filter: invert(1);

    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: var(--icon-opacity);
    background-image: var(--icon-url);
    background-repeat: no-repeat;
    background-position: center;
    background-size: var(--icon-width) var(--icon-height);
}

.icon-moon-solid,
.icon-gear-solid {
	--icon-opacity: 0.70;
    --icon-width: 0.90em;
    --icon-height: 1.00em;
}
.icon-book-open-solid,
.icon-message-slash-solid,
.icon-eye-slash-solid {
	--icon-opacity: 0.70;
    --icon-width: 0.90em;
    --icon-height: 0.75em;
}
.icon-manicule-left,
.icon-manicule-right {
	--icon-opacity: 0.85;
    --icon-width: 1.70em;
    --icon-height: 1.10em;
    --icon-offset-y: -0.05em;
}
.icon-sun-solid {
	--icon-opacity: 0.75;
    --icon-width: 1.00em;
    --icon-height: 1.00em;
}
.icon-magnifying-glass {
	--icon-opacity: 0.90;
    --icon-width: 0.90em;
    --icon-height: 1.00em;
}
.icon-question-solid {
    --icon-width: 0.55em;
}

.icon-single-white-star-on-black-circle {
    --icon-opacity: 0.75;
    --icon-width: 0.65em;
    --icon-height: 1.00em;

	/*	This icon uses a special data-URI variable.
	 */
	--icon-url: var(--GW-image-single-white-star-on-black-circle-svg);
}

.icon-special.progress-indicator-icon {
    --icon-opacity: 0.60;
    --icon-width: 0.80em;
}

/*  Recently-modified link icons.
    (These need to be in `initial.css` because they are used on /index.)
    NOTE: These also need to be inverted in `dark-mode-adjustments.css` (to avoid invisibility).
 */
.has-recently-modified-icon .recently-modified-icon-hook {
    --link-underline-background-color: var(--background-color);

    --icon-opacity: 0.75;
    --icon-width: 0.65em;
    --icon-height: 1em;

    position: relative;
    top: 0.05em;
    background-color: var(--link-underline-background-color);
	padding-left: calc(var(--icon-width) + 0.2em);
}
.has-recently-modified-icon .recently-modified-icon-hook::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: var(--icon-opacity);
    background-image: var(--GW-image-single-white-star-on-black-circle-svg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: var(--icon-width) var(--icon-height);
}
.has-recently-modified-icon .recently-modified-icon-hook::before {
    background-position: center left;
}


/********************/
/* SUB/SUPERSCRIPTS */
/********************/

sub,
sup {
    font-size: 0.7em;
    position: relative;
    line-height: 0.3em;
    font-variant-numeric: lining-nums;
}

sub {
    bottom: 0.1em;
}

/*  SUBSCRIPT+SUPERSCRIPT (necessary for inflation adjuster, and other math)
 */
.subsup {
    margin-left: 0.15em;
    margin-right: 0.1em;
    display: inline-flex;
    flex-flow: column;
    align-items: flex-start;
    vertical-align: text-bottom;
    position: relative;
    bottom: -0.1em;
}
.subsup sup,
.subsup sub {
    position: relative;
    line-height: 1;
    font-size: 0.6em;
}


/********/
/* MISC */
/********/

svg {
    fill: currentColor;
}

/*  Date ranges.

    These should also move as a block, just like inflation adjustments. Also,
    the en-dash should be v-positioned as one normally would be, for clarity.
 */
span.date-range {
    white-space: nowrap;
    font-variant-numeric: oldstyle-nums;
}
/* Unless in a link, for the usual reasons: */
a span.date-range {
    font-variant-numeric: tabular-nums;
}
span.date-range .subsup {
    align-items: center;
    bottom: 0;
    margin: -0.5em 0.05em 0 0.1em;
}
span.date-range .subsup sup {
    font-size: 1em;
    top: 0.45em;
}
span.date-range .subsup sub {
    bottom: -0.25em;
}
span.date-range > sub {
    margin: 0 0.1em;
    bottom: 0;
}

/*  Hide date range metadata on mobile (just display like normal).
 */
@media all and (max-width: 649px) {
    span.date-range sub {
        display: none;
    }
    span.date-range .subsup {
        margin: 0;
    }
    span.date-range .subsup sup {
        top: -0.15em;
    }
}

/*	Small caps blocks.
 */
p.smallcaps {
	font-variant-caps: small-caps;
}

/*  Auto-smallcaps introductory paragraphs of documents or sub-documents.
    (This is also where dropcaps appear.)
 */
p.intro-graf::first-line {
    font-variant-caps: small-caps;
}

.smallcaps-not > p.intro-graf::first-line {
    font-variant-caps: unset;
}

/*  Enable standard Pandoc attribute-based syntax for small-caps like
    ‘[foo]{.smallcaps}’; see https://pandoc.org/MANUAL.html#small-caps
 */
span.smallcaps {
    font-variant-caps: small-caps;
}


/*************/
/* SIDENOTES */
/*************/

/*  Hide sidenote columns on narrow viewports.
    */
@media all and (max-width: 1760px) {
	#sidenote-column-left,
    #sidenote-column-right {
        display: none;
    }
}

/*=--------------=*/
/*= Margin notes =*/
/*=--------------=*/

/*  On wide viewports, hide the margin notes until it is determined which layout
    form it should take (‘sidenote’ or ‘inline’).
 */
@media all and (min-width: 1497px) {
    .marginnote:not(.inline):not(.sidenote) {
        display: none;
    }
}


/*****************/
/* ACCESSIBILITY */
/*****************/
/* Provide a link which skips straight to the content bypassing the sidebar &
   metadata block; this link is positioned off-screen until <Tab>-navigated to,
   which focuses it, highlighting it in a big red block in the upper-left corner.
   Implementation & rationale from https://webaim.org/techniques/skipnav/
   Further background: https://www.w3.org/TR/WCAG20-TECHS/G1.html
 */
#skip-to-content-link  {
    padding: 0.3em 0.75em 0.4em 0.65em;
    position: absolute;
    top: -2.375em;
    left: 0;
    font-family: var(--GW-sans-serif-font-stack);
    color: var(--GW-skip-to-content-text-color);
    background-color: var(--GW-skip-to-content-background-color);
    border-color: var(--GW-skip-to-content-border-color);
    border-style: double;
    border-width: 0 3px 3px 0;
    outline: none;
    z-index: 10001;
    transition:
        top 1s ease-out;
}
#skip-to-content-link:focus {
    top: 0;
    transition:
        top 0.1s ease-in;
}


/***********/
/* CONSOLE */
/***********/

#console.hidden {
    bottom: calc(-1 * var(--GW-console-view-height));
    opacity: 0;
}


/*******************************/
/* PAGE-SPECIFIC MODIFICATIONS */
/*******************************/

/*=--------=*/
/*= /index =*/
/*=--------=*/

/*  On the index page, we hide article-related formatting, to make it
    more compact & classy.
 */
body.page-index header,
body.page-index #TOC {
    display: none;
}

/***********/
/*  Nav bar.
 */

/*  No index self-link.
 */
body.page-index #sidebar a.logo {
    pointer-events: none;
}

/*  Special nav link styling on /index.
 */
@media all and (min-width: 650px) {
    body.page-index #sidebar a {
        color: var(--GW-nav-header-link-hover-color);
    }
    body.page-index #sidebar a:hover {
        color: var(--GW-nav-header-link-color);
    }

	@media all and (min-width: 1180px) {
		body.page-index #sidebar a.logo {
			opacity: 1.0;
		}
	}
}

/*****************/
/*  Column layout.
 */

/*	Suppress bonus section in single/double-column layout.
 */
body.page-index #markdownBody #notable {
    display: none;
}
@media all and (min-width: 961px) {
    body.page-index #markdownBody,
    body.page-404 div#new-popular-notable {
        display: grid;
		grid-template-columns: 1fr 1fr;
		grid-column-gap: 2em;
	}
	body.page-index #markdownBody .abstract,
	body.page-index #markdownBody hr,
	body.page-404 div#new-popular-notable hr {
		grid-column-start: 1;
		grid-column-end: -1;
	}
	@media all and (max-width: 1760px) {
		body.page-404 div#new-popular-notable #notable {
			grid-column-start: 1;
			grid-column-end: -1;
		}
		body.page-404 div#new-popular-notable .columns ul {
			column-gap: 2em;
		}
	}
    /*  On sufficiently-wide screens, we can get 3 columns in nicely; to preserve
        the initial row being ‘meta’ blocks, we leave #notable visible
        (for a newest/popular/notable triplet)
     */
    @media all and (min-width: 1761px) {
        body.page-index #markdownBody,
	    body.page-404 div#new-popular-notable {
            grid-template-columns: 45ch 45ch 45ch;
        }
        body.page-index #markdownBody {
            margin-left: -20ch;
        }
	    body.page-404 div#new-popular-notable {
            margin-left: -22ch;
        }
        body.page-index #markdownBody #notable {
            display: block;
        }
        body.page-index #markdownBody .abstract {
			max-width: calc(var(--GW-body-max-width) - 2 * var(--GW-body-side-padding));
            margin-left: 20ch;
        }
        body.page-index #markdownBody hr {
            margin-left: -10ch;
        }
	    body.page-404 div#new-popular-notable hr {
            margin-left: -9.5ch;
        }
    }
}

/*****************/
/*  Link sections.
 */

/*  Headings.
 */
body.page-index #markdownBody section .heading {
    font-size: 1.5em;
    line-height: 1.125;
    text-align: left;
    font-weight: bold;
    margin-left: 0;
    padding: 0;
    border-bottom: 1px dotted currentColor;
}
body.page-404 div#new-popular-notable section .heading {
	margin-left: 0;
}
/*  No pilcrows.
 */
body.page-index #markdownBody section .heading a::after,
body.page-404 div#new-popular-notable section .heading a::after {
    display: none;
}

/*  Link lists.
 */
/* Index tweak: we start the unordered lists of pages with the second-level list icon (the empty/white star) instead
   of the default first-level list icon (the filled/black star). This is because if we use the normal list markers,
   it looks rather overwhelming due to the sheer number of black stars, distracting from the essay titles and
   wrecking the 'color'. Since there is currently no three-level nesting, we don't need to worry about cycling,
   and just lighten the color of the first/second level markers by overriding them. */
body.page-index #markdownBody section ul,
body.page-404 div#new-popular-notable section ul {
    --list-left-padding: 1.375em;
    --text-alignment: left;
    --list-bullet: var(--GW-image-single-white-star-svg);
    --list-bullet-opacity: 0.85;
}
body.page-index #markdownBody section ul ul,
body.page-404 div#new-popular-notable section ul ul {
    --list-bullet: var(--GW-image-single-white-star-rotated-svg);
    --list-bullet-opacity: 0.85;
}

@media all and (max-width: 649px) {
    body.page-index #markdownBody section ul p,
    body.page-404 div#new-popular-notable section ul p {
        margin-bottom: 0.15em;
        line-height: 1.65;
        overflow-wrap: break-word;
    }
}

/*	“Newest: Blog” section.
 */
body.page-index section#newest-blog li::before {
	top: 0.25em;
}
body.page-index section#newest-blog li p {
	--adjusted-line-height: calc(var(--line-height) - 0.20);
	
	line-height: var(--adjusted-line-height);
	padding-left: 1em;
	text-indent: -1em;
	margin-bottom: 0.25em;
	hyphens: none;
}
@media all and (max-width: 649px) {
	body.page-index section#newest-blog li p {
		margin-bottom: 0.50em;
	}
}
body.page-index section#newest-blog a code {
	word-break: normal;
}
@media all and (max-width: 960px) {
	body.page-index section#newest-blog .columns ul {
		column-width: unset;
	}
	body.page-index section#newest-blog li {
		padding-right: 1em;
	}
	/*	Hide 10th through 29th items.
	 */
	body.page-index section#newest-blog li:nth-of-type(n+10) {
		display: none;
	}
	body.page-index section#newest-blog li:last-of-type {
		display: list-item;
	}
}
/*	This is the width breakpoint when one column becomes two.
 */
@media all and (min-width: 961px) {
	body.page-index section#newest-blog .columns ul {
		column-gap: 2em;
	}
	body.page-index section#newest-blog li {
		padding-right: 3em;
	}
	@media all and (max-width: 1760px) {
		body.page-index section#newest-blog {
			grid-column-end: span 2;
		}
		/*	Hide 20th through 29th items.
		 */
		body.page-index section#newest-blog li:nth-of-type(n+20) {
			display: none;
		}
		body.page-index section#newest-blog li:last-of-type {
			display: list-item;
		}
	}
	/*	This is where two columns become three.
	 */
	@media all and (min-width: 1761px) {
		body.page-index section#newest-blog {
			grid-column-end: span 3;
		}
	}
}

/**********************************/
/*  Miscellaneous things on /index.
 */

/*  For desktop users, we hide the horizontal rulers on the /index page as they
    are unnecessary given the flowing columns; for mobile users, we’ll separate
    sections by rulers because they only get a single very long column to scroll
    through, which lacks a sense of hierarchy/chunking. However, desktop users
    still get the initial `<hr class="horizontal-rule-nth-1">` inserted by sed
    to divide the two blocks of 'New'/'Popular'/'Notable' from all-topics sections
    (which has no class set on it).
 */
@media all and (min-width: 650px) {
	/*  Horizontal ruler on the home page.
     */
    body.page-index section hr {
        display: none;
    }
}

/*  Hide date range metadata on /index.
 */
body.page-index span.date-range sub {
    display: none;
}
body.page-index span.date-range .subsup {
    margin: 0;
}
body.page-index span.date-range .subsup sup {
    top: -0.15em;
}

/*=------=*/
/*= /404 =*/
/*=-------=*/

@media all and (min-width: 650px) {
	body.page-404 #markdownBody {
		margin-top: 3em;
	}
}

body.page-404 figure {
	--float-side-margin: 3em;
}
body.page-404 figure figcaption {
    margin-top: 0.5em;
}

/*=------------=*/
/*= /doc/index =*/
/*=------------=*/

.doc-index-tag-short {
    display: block;
    font-size: 0.875em;
    padding-left: 1.5em;
    font-family: var(--GW-sans-serif-font-stack);
}

body.page-doc-index #see-alsos > ul > li {
    padding: 0;
}
body.page-doc-index #see-alsos > ul > li::before {
    display: none;
}

body.page-doc-index #see-alsos code {
    background: inherit;
    border: none;
    padding: 0;
}


/*****************************/
/* SOURCE CODE FILE PREVIEWS */
/*****************************/
/*  Syntax-highlighted code files, viewed by themselves (not in a popup).
 */

body.file-preview-source-code {
    max-width: unset;
    padding: 0;
}
body.file-preview-source-code pre {
    max-height: unset;
    border: none;
}
body.file-preview-source-code pre code {
    padding: 1rem;
}
/*********************/
/* SPECIAL OCCASIONS */
/*********************/

/*=--------------=*/
/*= April Fool’s =*/
/*=--------------=*/

.popup.april-fools-special-birthdaycat {
	width: 514px !important;
	height: 704px !important;
	max-height: unset !important;
}
.popup.april-fools-special-birthdaycat.offset {
	left: unset !important;
	right: 64px !important;
	top: -800px !important;
}
.popup.april-fools-special-birthdaycat.offset.moved {
	top: 8px !important;
	transition:
		top 5s ease;
}

/*=-----------=*/
/*= Halloween =*/
/*=-----------=*/

body.special-halloween-dark {
    --GW-blood-red: #b00;
    --GW-blood-red-dried: #700;
    --GW-blood-red-arterial: #e00;

    --GW-nav-header-link-color: var(--GW-blood-red-dried);
    --GW-nav-header-link-hover-color: var(--GW-blood-red-arterial);

    --GW-TOC-border-color: var(--GW-blood-red-dried);
    --GW-abstract-border-color: var(--GW-blood-red-dried);

    --GW-pre-element-border-color: var(--GW-blood-red-dried);

    --GW-epigraph-quotation-mark-color: var(--GW-blood-red-dried);

    --GW-H1-border-color: var(--GW-blood-red);
    --GW-H2-border-color: var(--GW-blood-red);

    --GW-dropcaps-yinit-color: var(--GW-blood-red);
    --GW-dropcaps-yinit-text-shadow-color: var(--GW-blood-red);
    --GW-dropcaps-de-zs-color: var(--GW-blood-red);
    --GW-dropcaps-cheshire-color: var(--GW-blood-red);
    --GW-dropcaps-kanzlei-color: var(--GW-blood-red);

    --GW-figure-outline-color: var(--GW-blood-red-dried);
    --GW-figure-caption-outline-color: var(--GW-blood-red);

    --GW-footnotes-section-top-rule-color: var(--GW-blood-red-dried);
    --GW-footnote-border-color: var(--GW-blood-red-arterial);
    --GW-sidenote-highlight-box-shadow-color: var(--GW-blood-red-arterial);
    --GW-footnote-backlink-border-color: var(--GW-blood-red);
    --GW-footnote-backlink-border-hover-color: var(--GW-blood-red-arterial);
    --GW-footnote-highlighted-border-color: var(--GW-blood-red);

    --GW-sidenote-self-link-border-color: var(--GW-blood-red-arterial);
    --GW-sidenote-border-color: var(--GW-blood-red-arterial);

    --GW-math-block-scrollbar-border-color: var(--GW-blood-red);

    --GW-collapse-disclosure-button-text-color: var(--GW-blood-red-dried);
    --GW-collapse-disclosure-button-text-hover-color: var(--GW-blood-red);

    --GW-x-of-the-day-border-color: var(--GW-blood-red-dried);

    --GW-page-toolbar-border-color: var(--GW-blood-red-dried);
    --GW-page-toolbar-control-button-color: var(--GW-blood-red);

    --GW-bottom-ornament-line-color: var(--GW-blood-red-arterial);

    --GW-back-to-top-link-color: var(--GW-blood-red-dried);
    --GW-back-to-top-link-hover-color: var(--GW-blood-red-arterial);

    --GW-floating-header-scroll-indicator-color: var(--GW-blood-red);

    --GW-body-link-hover-color: var(--GW-blood-red-arterial);

    --GW-highlighted-link-outline-color: var(--GW-blood-red);
}

body.special-halloween-dark blockquote {
    --border-color: var(--GW-blood-red-dried);
}

body.special-halloween-dark .heading {
    --GW-body-text-color: var(--GW-blood-red);
    --GW-body-link-color: var(--GW-blood-red);

    color: var(--GW-body-text-color);
}

@media all and (max-width: 649px) {
	body.special-halloween-dark #sidebar a {
		--GW-body-link-color: var(--GW-blood-red-arterial);

		color: var(--GW-body-link-color);
	}
}

body.special-halloween-dark #navigation {
    color: var(--GW-blood-red);
}
body.special-halloween-dark #navigation a:hover {
    color: var(--GW-blood-red-arterial);
}

body.special-halloween-dark #sidebar svg.logo-image {
    visibility: hidden;
}
body.special-halloween-dark #sidebar svg.logo-image.visible {
	visibility: visible;
}
body.special-halloween-dark #sidebar span.logo-image img {
    filter: none;
}
@media all and (max-width: 649px) {
    body.special-halloween-dark #sidebar span.logo-image img {
        width: 133%;
        position: relative;
        left: -15%; top: -15%;
    }
}
@media all and (min-width: 650px) {
    @media all and (max-width: 1179px) {
        body.special-halloween-dark #sidebar span.logo-image img {
            width: 150%;
            position: relative;
            left: -30%; top: -15%;
        }
    }
    @media all and (min-width: 1180px) {
        body.special-halloween-dark #sidebar span.logo-image img {
            width: 150%;
            position: relative;
            left: -30%; top: -10%;
        }
    }
}

body.special-halloween-dark hr::after {
	filter: none;
    opacity: 1;
}
body.special-halloween-dark .horizontal-rule-nth-1 hr::after {
    background-image: url('/static/img/ornament/halloween/sun-verginasun-black.svg');
}
body.special-halloween-dark .horizontal-rule-nth-2 hr::after {
    background-image: url('/static/img/ornament/halloween/japanesecrest-tsukinihoshi-dottedmoon.svg');
}
body.special-halloween-dark .horizontal-rule-nth-3 hr::after {
    background-image: url('/static/img/ornament/halloween/asterism-triplewhitestar.svg');
}

body.special-halloween-dark #x-of-the-day::before,
body.special-halloween-dark #x-of-the-day::after {
    background-image:
        url('/static/img/ornament/halloween/three-wavy-lines-ornament-left.svg'),
        url('/static/img/ornament/halloween/three-wavy-lines-ornament-right.svg');
    filter: none;
    opacity: 0.75;
}
body.special-halloween-dark #x-of-the-day .site-of-the-day blockquote {
    background-image: url('/static/img/ornament/halloween/swissspiralroll.svg#svgView(preserveAspectRatio(none))');
}
body.special-halloween-dark #x-of-the-day .site-of-the-day blockquote::before,
body.special-halloween-dark #x-of-the-day .site-of-the-day blockquote::after {
    background-image: url('/static/img/ornament/halloween/swissspiralroll.svg');
}

/*  Styles for /index
 */
body.page-index.special-halloween-dark #sidebar a:hover {
    color: var(--GW-blood-red);
}

body.page-index.special-halloween-dark #sidebar a.logo {
	pointer-events: auto;
}

/*=-----------=*/
/*= Christmas =*/
/*=-----------=*/

body.special-christmas-light {
    --GW-santa-hat-red: #900;
    --GW-holly-berry-red: #c00;
    --GW-holly-leaf-green: #060;
    --GW-christmas-lights-red: #f00;
    --GW-mistletoe-green: #0c0;
    --GW-pine-needle-green: #050;
    --GW-fir-needle-green: #014421;
    --GW-spruce-needle-green: #4bd24d;
    --GW-christmas-lights-gold: #ff0;
}

body.special-christmas-dark {
    --GW-santa-hat-red: #d00;
    --GW-holly-berry-red: #f00;
    --GW-holly-leaf-green: #0a3;
    --GW-christmas-lights-red: #f00;
    --GW-mistletoe-green: #005519;
    --GW-pine-needle-green: #5eff66;
    --GW-spruce-needle-green: #5ce15b;
    --GW-fir-needle-green: #a7cab6;
    --GW-christmas-lights-gold: #996515;
}

body[class*='special-christmas'] {
    --GW-nav-header-link-color: var(--GW-holly-leaf-green);
    --GW-nav-header-link-hover-color: var(--GW-holly-berry-red);

    --GW-TOC-border-color: var(--GW-mistletoe-green);
    --GW-abstract-border-color: var(--GW-mistletoe-green);

    --GW-pre-element-border-color: var(--GW-holly-leaf-green);

    --GW-epigraph-quotation-mark-color: var(--GW-holly-leaf-green);

    --GW-H1-border-color: var(--GW-holly-leaf-green);
    --GW-H2-border-color: var(--GW-holly-leaf-green);

    --GW-dropcaps-yinit-color: var(--GW-santa-hat-red);
    --GW-dropcaps-yinit-text-shadow-color: var(--GW-santa-hat-red);
    --GW-dropcaps-de-zs-color: var(--GW-santa-hat-red);
    --GW-dropcaps-cheshire-color: var(--GW-santa-hat-red);
    --GW-dropcaps-kanzlei-color: var(--GW-santa-hat-red);

    --GW-figure-outline-color: var(--GW-holly-leaf-green);
    --GW-figure-caption-outline-color: var(--GW-holly-leaf-green);

    --GW-footnotes-section-top-rule-color: var(--GW-holly-leaf-green);
    --GW-footnote-border-color: var(--GW-holly-leaf-green);
    --GW-sidenote-highlight-box-shadow-color: var(--GW-holly-leaf-green);
    --GW-footnote-backlink-border-color: var(--GW-holly-leaf-green);
    --GW-footnote-backlink-border-hover-color: var(--GW-holly-berry-red);
    --GW-footnote-highlighted-border-color: var(--GW-holly-leaf-green);

    --GW-sidenote-self-link-border-color: var(--GW-holly-leaf-green);
    --GW-sidenote-border-color: var(--GW-holly-leaf-green);

    --GW-math-block-scrollbar-border-color: var(--GW-holly-leaf-green);

    --GW-collapse-disclosure-button-text-color: var(--GW-holly-leaf-green);
    --GW-collapse-disclosure-button-text-hover-color: var(--GW-holly-berry-red);

    --GW-x-of-the-day-border-color: var(--GW-holly-leaf-green);

    --GW-page-toolbar-border-color: var(--GW-holly-leaf-green);
    --GW-page-toolbar-control-button-color: var(--GW-holly-leaf-green);

    --GW-bottom-ornament-line-color: var(--GW-holly-leaf-green);

    --GW-back-to-top-link-color: var(--GW-holly-leaf-green);
    --GW-back-to-top-link-hover-color: var(--GW-holly-berry-red);

    --GW-floating-header-scroll-indicator-color: var(--GW-holly-leaf-green);

    --GW-body-link-color: var(--GW-pine-needle-green);
    --GW-body-link-visited-color: var(--GW-fir-needle-green);
    --GW-body-link-hover-color: var(--GW-holly-berry-red);

    --GW-highlighted-link-outline-color: var(--GW-santa-hat-red);
}

body[class*='special-christmas'] blockquote {
    --border-color: var(--GW-holly-leaf-green);
}

body[class*='special-christmas'] blockquote.blockquote-level-1,
body[class*='special-christmas'] blockquote.blockquote-level-3,
body[class*='special-christmas'] blockquote.blockquote-level-5 {
    --GW-body-link-color: var(--GW-holly-berry-red);
    --GW-body-link-visited-color: var(--GW-santa-hat-red);
    --GW-body-link-hover-color: var(--GW-pine-needle-green);
}
body[class*='special-christmas'] blockquote.blockquote-level-2,
body[class*='special-christmas'] blockquote.blockquote-level-4,
body[class*='special-christmas'] blockquote.blockquote-level-6 {
    --GW-body-link-color: var(--GW-pine-needle-green);
    --GW-body-link-visited-color: var(--GW-fir-needle-green);
    --GW-body-link-hover-color: var(--GW-holly-berry-red);
}

body[class*='special-christmas'] .heading {
    --GW-body-text-color: var(--GW-holly-leaf-green);
    --GW-body-link-color: var(--GW-holly-leaf-green);

    color: var(--GW-body-text-color);
}

body[class*='special-christmas'] header h1 {
    --GW-body-text-color: var(--GW-holly-leaf-green);

    color: var(--GW-body-text-color);
}

body[class*='special-christmas'] #navigation {
    color: var(--GW-santa-hat-red);
}
body[class*='special-christmas'] #navigation a:hover {
    color: var(--GW-holly-berry-red);
}

body[class*='special-christmas'] ol > li:nth-of-type(odd)::before {
	color: var(--GW-spruce-needle-green);
}
body[class*='special-christmas'] ol > li:nth-of-type(even)::before {
	color: var(--GW-christmas-lights-red);
}

/********/
/*  Glow.
 */

body.special-christmas-light header h1 {
    text-shadow:
        0 0 2px var(--GW-christmas-lights-gold);
}
body.special-christmas-light .heading a,
body.special-christmas-light p[class*='dropcap-'] span.dropcap,
body.special-christmas-light #navigation a,
body.special-christmas-light #sidebar a {
    text-shadow:
        0 0 1px var(--GW-christmas-lights-gold);
}

body.special-christmas-dark header h1 {
    text-shadow:
        0 0 6px var(--GW-body-background-color),
        0 0 4px var(--GW-christmas-lights-gold);
}
body.special-christmas-dark .heading a,
body.special-christmas-dark p[class*='dropcap-'] span.dropcap,
body.special-christmas-dark #navigation a,
body.special-christmas-dark #sidebar a {
    text-shadow:
        0 0 2px var(--GW-body-background-color),
        0 0 2px var(--GW-christmas-lights-gold);
}

/********/
/*  Logo.
 */

body[class*='special-christmas'] #sidebar svg.logo-image {
    visibility: hidden;
}
body[class*='special-christmas'] #sidebar span.logo-image img {
    filter: none;
}
@media all and (max-width: 649px) {
    body[class*='special-christmas'] #sidebar span.logo-image img {
        width: 133%;
        position: relative;
        left: -15%; top: -15%;
    }
}
@media all and (min-width: 650px) {
    @media all and (max-width: 1179px) {
        body[class*='special-christmas'] #sidebar span.logo-image img {
            width: 150%;
            position: relative;
            left: -30%; top: -15%;
        }
    }
    @media all and (min-width: 1180px) {
        body[class*='special-christmas'] #sidebar span.logo-image img {
            width: 125%;
            position: relative;
            left: -10%; top: -10%;
        }
    }
}

/*  Styles for /index
 */
body.page-index[class*='special-christmas'] #sidebar a:hover {
    color: var(--GW-holly-leaf-green);
}

body.page-index[class*='special-christmas'] #sidebar a.logo {
	pointer-events: auto;
}
@charset "UTF-8";

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 400;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Regular.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 400;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-RegularItalic.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 600;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Semibold.otf?v=1742261861') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 700;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Bold.otf?v=1742261860') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

@font-face {
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Regular.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-RegularItalic.otf?v=1742304942') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}

@font-face {
	font-family: 'Source Sans 3';
	font-weight: 700;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Bold.otf?v=1742304941') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
/*  Elements to be deleted completely.
 */
body.reader-mode-active #sidebar,
body.reader-mode-active #page-metadata .page-status,
body.reader-mode-active #page-metadata .page-confidence,
body.reader-mode-active #page-metadata .page-importance,
body.reader-mode-active #page-metadata .page-backlinks,
body.reader-mode-active #page-metadata .page-link-bibliography,
body.reader-mode-active #page-metadata .page-similars,
body.reader-mode-active #page-metadata .page-date-range::after,
body.reader-mode-active #page-metadata .page-metadata-fields::after,
body.reader-mode-active #TOC ul li::before,
body.reader-mode-active #TOC .toc-collapse-toggle-button,
body.reader-mode-active #sidenote-column-left,
body.reader-mode-active #sidenote-column-right,
body.reader-mode-active #navigation,
body.reader-mode-active #footer,
body.reader-mode-active .inflation-adjusted .subsup,
body.reader-mode-active .has-recently-modified-icon .recently-modified-icon-hook
body.reader-mode-active .date-range sub,
body.reader-mode-active .file-includes {
    display: none;
}

/*  Link icons in reader mode just don’t work well, sadly.
        —SA 2024-11-27
 */
body.reader-mode-active .markdownBody a .link-icon-hook {
    display: none;
}

body.reader-mode-active .markdownBody a .recently-modified-icon-hook {
    display: none;
}

/*  Preserve the positioning of related elements by hiding visibility, instead
    of deleting entirely.
 */
body.reader-mode-active .date-range .subsup sub {
    visibility: hidden;
}

body.reader-mode-active.masked-links-hidden .footnote-ref,
body.reader-mode-active.masked-links-hidden .footnote-back,
body.reader-mode-active.masked-links-hidden .footnote-back-block {
    opacity: 0;
}

body.reader-mode-active #sidebar a.logo {
    border-color: transparent;
}

body.reader-mode-active #TOC ul li {
    padding-left: 0.125em;
}

/*  Links.
 */
body.reader-mode-active .markdownBody a.has-indicator-hook {
    margin: 0;
    position: relative;
    z-index: 111;
}

body.reader-mode-active .markdownBody a .indicator-hook {
    background-image: inherit;
    background-size: inherit;
    background-repeat: inherit;
    background-position: inherit;
    background-color: var(--link-underline-background-color);
    box-shadow: -2px 0 0 0 var(--link-underline-background-color);
    margin-left: -0.3em;
}
body.reader-mode-active.masked-links-hidden .markdownBody a .indicator-hook {
    visibility: hidden;
}

body.reader-mode-active.masked-links-hidden .markdownBody a:not(.popup-open):not(.popin-open),
body.reader-mode-active.masked-links-hidden .markdownBody a:not(.popup-open):not(.popin-open):visited,
body.reader-mode-active.masked-links-hidden .markdownBody a:not(.popup-open):not(.popin-open):hover {
    color: inherit;
    background: none;
    cursor: text;
}

body.reader-mode-active.masked-links-hidden .markdownBody a:link,
body.reader-mode-active.masked-links-hidden .markdownBody a:link * {
    text-shadow: none;
}

body.reader-mode-active.masked-links-hidden .markdownBody a code {
    border-bottom-width: 1px;
}

/*  Footnotes section self-link.
 */
body.reader-mode-active .markdownBody section.footnotes .section-self-link:hover {
    position: absolute !important;
    cursor: pointer !important;
}

/*  Citations.
 */
body.reader-mode-active .markdownBody .cite-joiner {
    display: initial;
}
body.reader-mode-active .markdownBody .cite-date::before {
    content: " ";
}

body.reader-mode-active .markdownBody .cite-date {
    vertical-align: unset;
    font-size: unset;
    line-height: unset;
    font-variant-numeric: unset;
    margin-left: unset;
}
body.reader-mode-active .markdownBody .cite-author-plural::after {
    content: " ";
}

/*  Misc.
 */
body.reader-mode-active .markdownBody p code {
    --background-color: inherit;

    border-color: transparent;
}

body.reader-mode-active .date-range .subsup {
    margin: -0.5em 0 0 0;
}

/*  Anti-style zone.
 */
body.reader-mode-active .reader-mode-style-not * {
    font-weight: inherit;
    font-style: inherit;
    font-variant: inherit;
    color: inherit;
}

/*  Mobile layout adjustments.
 */
@media all and (max-width: 649px) {
    body.reader-mode-active article {
        margin-top: 0;
    }
}
