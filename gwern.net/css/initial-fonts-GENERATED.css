@charset "UTF-8";

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 400;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Regular.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}
@font-face {
	font-family: 'Source Serif 4';
	font-weight: 400;
	font-style: italic;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-RegularItalic.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 600;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Semibold.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

@font-face {
	font-family: 'Source Serif 4';
	font-weight: 700;
	font-style: normal;
	src: url('/static/font/ssf4/SourceSerif4-Text-BASIC-Bold.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-007E, U+00A0-00BF, U+00C6, U+00D0, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02DD, U+0300-030C, U+0391-03A9, U+03B1-03C9, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+25A0-25C9, U+2752;
}

@font-face {
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Regular.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
@font-face {
	font-family: 'Source Sans 3';
	font-weight: 400;
	font-style: italic;
	src: url('/static/font/ss3/SourceSans3-BASIC-RegularItalic.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}

@font-face {
	font-family: 'Source Sans 3';
	font-weight: 700;
	font-style: normal;
	src: url('/static/font/ss3/SourceSans3-BASIC-Bold.otf') format('opentype');
	font-display: swap;
	unicode-range: U+0020-00BF, U+00C6, U+00D7, U+00D8, U+00DE, U+00DF, U+00E6, U+00F0, U+00F7, U+00F8, U+00FE, U+0152, U+0153, U+017F, U+018F, U+0192, U+0259, U+02C6-02CC, U+02D8-02DD, U+0300-030C, U+2002-2049, U+2190-2199, U+2212, U+2215, U+221E, U+2248, U+2260, U+2264-2265, U+2318, U+2325, U+2326, U+232B, U+25A0-25E6, U+2610, U+263C, U+263F-2642, U+2660, U+2663, U+2665, U+2666, U+2752, U+275B-2760, U+2E3A, U+2E3B;
}
