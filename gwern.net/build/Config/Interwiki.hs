{-# LANGUAGE OverloadedStrings #-}
module Config.Interwiki where

import Text.Pandoc (Inline(..), nullAttr)
import qualified Data.Text as T (Text, append, isPrefixOf)

-- testing: `Test` checks key-uniqueness & validity of URL in the `Link` output.
testCases :: [(Inline, Inline)]
testCases = [
  -- !Wikipedia
  (Link nullAttr [Str "Pondicherry"] ("!Wikipedia",""),
    <PERSON> ("", ["link-live"], []) [Str "Pondicherry"] ("https://en.wikipedia.org/wiki/Pondicherry", ""))
  , (<PERSON> nullAttr [Emph [Str "Monty Python's Life of Brian"]] ("!Wikipedia",""),
      <PERSON> ("", ["link-live"], []) [Emph [Str "Monty Python's Life of Brian"]] ("https://en.wikipedia.org/wiki/Monty_Python%27s_Life_of_Brian", ""))
  , (<PERSON> nullAttr [<PERSON> [Str "Monty Python's Life of Brian"]] ("!Wikipedia",""),
      <PERSON> ("", ["link-live"], []) [<PERSON> [Str "Monty Python's Life of Brian"]] ("https://en.wikipedia.org/wiki/Monty_Python%27s_Life_of_Brian", ""))
  , (Link nullAttr [Str "SHA-1#Attacks"] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Str "SHA-1#Attacks"] ("https://en.wikipedia.org/wiki/SHA-1#Attacks", ""))
  , (Link nullAttr [Str "Bayesian search theory"] ("!Wikipedia","USS Scorpion (SSN-589)#Search: 1968"),
      Link ("", ["link-live"], []) [Str "Bayesian search theory"] ("https://en.wikipedia.org/wiki/USS_Scorpion_(SSN-589)#Search:_1968", ""))
  , (Link nullAttr [Str "C++ templates"] ("!Wikipedia","Template (C++)"),
     Link ("", ["link-live"], []) [Str "C++ templates"] ("https://en.wikipedia.org/wiki/Template_(C%2B%2B)", ""))
  , (Link nullAttr [Str "Aaahh!!! Real Monsters"] ("!Wikipedia",""),
    Link ("", ["link-live"], []) [Str "Aaahh!!! Real Monsters"] ("https://en.wikipedia.org/wiki/Aaahh!!!_Real_Monsters", ""))
    -- test whether we remove crud from the URL, although we continue to pass it through in the displayed link text:
  , (Link nullAttr [Str "%3Cstrong%3Aaahh!!! Real Monsters%3C/strong%3E"] ("!Wikipedia",""),
     Link ("", ["link-live"], []) [Str "%3Cstrong%3Aaahh!!! Real Monsters%3C/strong%3E"] ("https://en.wikipedia.org/wiki/Aaahh!!!_Real_Monsters", ""))
  , (Link nullAttr [Str "%3Cem%3Aaahh!!! Real Monsters%3C/em%3E"] ("!Wikipedia",""),
     Link ("", ["link-live"], []) [Str "%3Cem%3Aaahh!!! Real Monsters%3C/em%3E"] ("https://en.wikipedia.org/wiki/Aaahh!!!_Real_Monsters", ""))
  , (Link nullAttr [Str "Senryū"] ("!Wikipedia",""),
    Link ("", ["link-live"], []) [Str "Senryū"] ("https://en.wikipedia.org/wiki/Senry%C5%AB", ""))
  , (Link nullAttr [Str "D&D"] ("!Wikipedia","Dungeons & Dragons"),
    Link ("", ["link-live"], []) [Str "D&D"] ("https://en.wikipedia.org/wiki/Dungeons_%26_Dragons", ""))
  , (Link nullAttr [Str "Arm & Hammer"] ("!Wikipedia",""),
    Link ("", ["link-live"], []) [Str "Arm & Hammer"] ("https://en.wikipedia.org/wiki/Arm_%26_Hammer", ""))
  , (Link nullAttr [Str "Achaea"] ("!Wikipedia","Achaea, Dreams of Divine Lands"),
    Link ("", ["link-live"], []) [Str "Achaea"] ("https://en.wikipedia.org/wiki/Achaea,_Dreams_of_Divine_Lands", ""))
  , (Link nullAttr [Str "Armageddon"] ("!Wikipedia","Armageddon (MUD)"),
    Link ("", ["link-live"], []) [Str "Armageddon"] ("https://en.wikipedia.org/wiki/Armageddon_(MUD)", ""))
  , (Link nullAttr [Str "Special:Pondicherry"] ("!Wikipedia",""),
    Link ("", ["content-transform-not", "link-live-not"], []) [Str "Special:Pondicherry"] ("https://en.wikipedia.org/wiki/Special:Pondicherry", ""))
  , (Link nullAttr [Str "SpecialPondicherry"] ("!Wikipedia",""),
     Link ("", ["link-live"], []) [Str "SpecialPondicherry"] ("https://en.wikipedia.org/wiki/SpecialPondicherry", ""))
  , (Link nullAttr [Str "Category:Pondicherry"] ("!Wikipedia",""),
    Link ("", ["content-transform-not", "link-live"], []) [Str "Category:Pondicherry"] ("https://en.wikipedia.org/wiki/Category:Pondicherry", ""))

  -- !W
  , (Link nullAttr [Str "Jure Robič"] ("!W",""),
      Link ("", ["link-live"], []) [Str "Jure Robič"] ("https://en.wikipedia.org/wiki/Jure_Robi%C4%8D", ""))
  , (Link nullAttr [Str "Pondicherry"] ("!W",""),
    Link ("", ["link-live"], []) [Str "Pondicherry"] ("https://en.wikipedia.org/wiki/Pondicherry", ""))
  , (Link nullAttr [Str "Special:Pondicherry"] ("!W",""),
    Link ("", ["content-transform-not", "link-live-not"], []) [Str "Special:Pondicherry"] ("https://en.wikipedia.org/wiki/Special:Pondicherry", ""))
  , (Link nullAttr [Str "SpecialPondicherry"] ("!W",""),
     Link ("", ["link-live"], []) [Str "SpecialPondicherry"] ("https://en.wikipedia.org/wiki/SpecialPondicherry", ""))
  , (Link nullAttr [Str "Category:Pondicherry"] ("!W",""),
    Link ("", ["content-transform-not", "link-live"], []) [Str "Category:Pondicherry"] ("https://en.wikipedia.org/wiki/Category:Pondicherry", ""))

   -- !W + duplicate classes
  , (Link ("", ["link-live"], []) [Str "Jure Robič"] ("!W",""),
      Link ("", ["link-live"], []) [Str "Jure Robič"] ("https://en.wikipedia.org/wiki/Jure_Robi%C4%8D", ""))

  -- !W + title
  , (Link nullAttr [Str "foo"] ("!W","Pondicherry"),
    Link ("", ["link-live"], []) [Str "foo"] ("https://en.wikipedia.org/wiki/Pondicherry", ""))
  , (Link nullAttr [Str "foo"] ("!W","Special:Pondicherry"),
    Link ("", ["content-transform-not", "link-live-not"], []) [Str "foo"] ("https://en.wikipedia.org/wiki/Special:Pondicherry", ""))
  , (Link nullAttr [Str "foo"] ("!W","SpecialPondicherry"),
     Link ("", ["link-live"], []) [Str "foo"] ("https://en.wikipedia.org/wiki/SpecialPondicherry", ""))
  , (Link nullAttr [Str "foo"] ("!W","Category:Pondicherry"),
    Link ("", ["content-transform-not", "link-live"], []) [Str "foo"] ("https://en.wikipedia.org/wiki/Category:Pondicherry", ""))

  -- !W + possessive special-case rewrite:
  , (Link nullAttr [Emph [Str "George Washington"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "George Washington"]] ("https://en.wikipedia.org/wiki/George_Washington", ""))
  , (Link nullAttr [Emph [Str "George Washington's"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "George Washington's"]] ("https://en.wikipedia.org/wiki/George_Washington", ""))
  , (Link nullAttr [Emph [Str "George Washington’s"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "George Washington’s"]] ("https://en.wikipedia.org/wiki/George_Washington", ""))
  , (Link nullAttr [Emph [Str "George Washington"]] ("!Wikipedia","George Washington's"),
      Link ("", ["link-live"], []) [Emph [Str "George Washington"]] ("https://en.wikipedia.org/wiki/George_Washington%27s", ""))
  , (Link nullAttr [Emph [Str "Antoine's"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "Antoine's"]] ("https://en.wikipedia.org/wiki/Antoine%27s", ""))
  , (Link nullAttr [Emph [Str "Antoine's"]] ("!Wikipedia","Antoine's"),
      Link ("", ["link-live"], []) [Emph [Str "Antoine's"]] ("https://en.wikipedia.org/wiki/Antoine%27s", ""))
  , (Link nullAttr [Emph [Str "famous restaurant"]] ("!Wikipedia","Antoine's"),
      Link ("", ["link-live"], []) [Emph [Str "famous restaurant"]] ("https://en.wikipedia.org/wiki/Antoine%27s", ""))

    -- !W + quotation marks special-case rewrite:
    , (Link nullAttr [Emph [Str "“The Two Cultures”"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "“The Two Cultures”"]] ("https://en.wikipedia.org/wiki/The_Two_Cultures", ""))
    , (Link nullAttr [Emph [Str "”The Two Cultures“"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "”The Two Cultures“"]] ("https://en.wikipedia.org/wiki/The_Two_Cultures", "")) -- must be able to handle cases of smart-quotes going awry
    , (Link nullAttr [Emph [Str "\"The Two Cultures\""]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "\"The Two Cultures\""]] ("https://en.wikipedia.org/wiki/The_Two_Cultures", ""))
    , (Link nullAttr [Emph [Str "'The Two Cultures'"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "'The Two Cultures'"]] ("https://en.wikipedia.org/wiki/The_Two_Cultures", ""))
    , (Link nullAttr [Emph [Str "‘The Two Cultures’"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "‘The Two Cultures’"]] ("https://en.wikipedia.org/wiki/The_Two_Cultures", ""))
    , (Link nullAttr [Emph [Str "’The Two Cultures‘"]] ("!Wikipedia",""),
      Link ("", ["link-live"], []) [Emph [Str "’The Two Cultures‘"]] ("https://en.wikipedia.org/wiki/The_Two_Cultures", ""))

   -- <https://en.wikipedia.org/wiki/$ARTICLE>
  , (Link nullAttr [Str "Pondicherry"] ("https://en.wikipedia.org/wiki/Pondicherry",""),
    Link ("", ["link-live"], []) [Str "Pondicherry"] ("https://en.wikipedia.org/wiki/Pondicherry", ""))
  , (Link nullAttr [Str "Special:Pondicherry"] ("https://en.wikipedia.org/wiki/Special:Pondicherry",""),
    Link ("", ["content-transform-not", "link-live-not"], []) [Str "Special:Pondicherry"] ("https://en.wikipedia.org/wiki/Special:Pondicherry", ""))
  , (Link nullAttr [Str "SpecialPondicherry"] ("https://en.wikipedia.org/wiki/SpecialPondicherry",""),
     Link ("", ["link-live"], []) [Str "SpecialPondicherry"] ("https://en.wikipedia.org/wiki/SpecialPondicherry", ""))
  , (Link nullAttr [Str "Category:Pondicherry"] ("https://en.wikipedia.org/wiki/Category:Pondicherry",""),
    Link ("", ["content-transform-not", "link-live"], []) [Str "Category:Pondicherry"] ("https://en.wikipedia.org/wiki/Category:Pondicherry", ""))

  -- bypass redirects:
  , (Link nullAttr [Str "WP:RS"] ("!W",""),
     Link ("", ["link-live"], []) [Str "WP:RS"] ("https://en.wikipedia.org/wiki/Wikipedia:Reliable_sources", ""))
  , (Link nullAttr [Str "WP:RS#foobar"] ("!W",""),
     Link ("", ["link-live"], []) [Str "WP:RS#foobar"] ("https://en.wikipedia.org/wiki/Wikipedia:Reliable_sources#foobar", ""))
  , (Link nullAttr [Str "Robots exclusion standard"] ("https://en.wikipedia.org/wiki/Robots_exclusion_standard",""),
    Link ("", ["link-live"], []) [Str "Robots exclusion standard"] ("https://en.wikipedia.org/wiki/Robots.txt", ""))

  -- /lorem testcases: Should popup (as an **annotation**):
  , (Link nullAttr [Emph [Str "Liber Figurarum"]] ("https://it.wikipedia.org/wiki/Liber_Figurarum",""),
     Link ("", ["link-live"], []) [Emph [Str "Liber Figurarum"]] ("https://it.wikipedia.org/wiki/Liber_Figurarum", ""))
  , (Link nullAttr [Str "Small caps"] ("!W",""),
    Link ("", ["link-live"], []) [Str "Small caps"] ("https://en.wikipedia.org/wiki/Small_caps", ""))
  , (Link nullAttr [Str "Talk:Small caps"] ("!W",""),
    Link ("", ["link-live"], []) [Str "Talk:Small caps"] ("https://en.wikipedia.org/wiki/Talk:Small_caps", ""))
  , (Link nullAttr [Str "User:Gwern"] ("!W",""),
    Link ("", ["link-live"], []) [Str "User:Gwern"] ("https://en.wikipedia.org/wiki/User:Gwern", ""))
  , (Link nullAttr [Str "User talk:Gwern"] ("!W",""),
    Link ("", ["link-live"], []) [Str "User talk:Gwern"] ("https://en.wikipedia.org/wiki/User_talk:Gwern", ""))
  , (Link nullAttr [Str "Help:Authority control"] ("!W",""),
    Link ("", ["link-live"], []) [Str "Help:Authority control"] ("https://en.wikipedia.org/wiki/Help:Authority_control", ""))
  , (Link nullAttr [Str "Help talk:Authority control"] ("!W",""),
    Link ("", ["link-live"], []) [Str "Help talk:Authority control"] ("https://en.wikipedia.org/wiki/Template_talk:Authority_control", ""))
  , (Link nullAttr [Str "MediaWiki:Citethispage-content"] ("!W",""),
      Link ("", ["link-live"], []) [Str "MediaWiki:Citethispage-content"] ("https://en.wikipedia.org/wiki/MediaWiki:Citethispage-content", ""))

   -- NOTE: we ban all 'Wikipedia:.*Signpost.*' popups due to very strange newsletter HTML structure breaking parsing & popups too weirdly for Achmiz to fix; they can only be live-links:
  , (Link nullAttr [Str "Wikipedia:Wikipedia Signpost"] ("!W",""),
    Link ("", ["content-transform-not", "link-live"], []) [Str "Wikipedia:Wikipedia Signpost"] ("https://en.wikipedia.org/wiki/Wikipedia:Wikipedia_Signpost", ""))
  , (Link nullAttr [Str "Wikipedia talk:Wikipedia Signpost"] ("!W",""),
    Link ("", ["content-transform-not", "link-live"], []) [Str "Wikipedia talk:Wikipedia Signpost"] ("https://en.wikipedia.org/wiki/Wikipedia_talk:Wikipedia_Signpost", ""))

   -- /lorem testcases: should all be annotations, but check that the presence of the slash (these slashes are genuine, and in the true article name, they aren't redirects which can be skipped, so we do need to verify we handle them correctly) doesn't screw up and trigger false negatives on annotation/live status:
  , (Link nullAttr  [Str "Bouba/kiki effect"] ("!W",""),
     Link ("", ["link-live"], []) [Str "Bouba/kiki effect"] ("https://en.wikipedia.org/wiki/Bouba/kiki_effect", ""))
  , (Link nullAttr [Emph [Str "Fate/stay night"]] ("!W",""),
     Link ("", ["link-live"], []) [Emph [Str "Fate/stay night"]] ("https://en.wikipedia.org/wiki/Fate/stay_night", ""))
  , (Link nullAttr [Emph [Str "Fate/stay_night: Unlimited_Blade_Works (film)"]] ("!W",""),
     Link ("", ["link-live"], []) [Emph [Str "Fate/stay_night: Unlimited_Blade_Works (film)"]] ("https://en.wikipedia.org/wiki/Fate/stay_night:_Unlimited_Blade_Works_(film)", ""))

    -- Should popup (as a **live link** but not annotation): [Category:Buddhism and sports](!W)
  , (Link nullAttr [Str "Category:Buddhism and sports"] ("https://en.wikipedia.org/wiki/Category:Buddhism_and_sports",""),
     Link ("", ["content-transform-not", "link-live"], []) [Str "Category:Buddhism and sports"] ("https://en.wikipedia.org/wiki/Category:Buddhism_and_sports", ""))

    -- Should **not** popup at all: [Special:Random](!W)
  , (Link nullAttr [Str "Special:Random"] ("!W",""),
      Link ("", ["content-transform-not", "link-live-not"], []) [Str "Special:Random"] ("https://en.wikipedia.org/wiki/Special:Random", ""))
  , (Link nullAttr [Str "Special:BookSources/0-8054-2836-4"] ("!W",""),
     Link ("", ["content-transform-not", "link-live-not"], []) [Str "Special:BookSources/0-8054-2836-4"] ("https://en.wikipedia.org/wiki/Special:BookSources/0-8054-2836-4", ""))
  , (Link nullAttr [Str "Special:Log/Marudubshinki"] ("!W",""),
     Link ("", ["content-transform-not", "link-live-not"], []) [Str "Special:Log/Marudubshinki"] ("https://en.wikipedia.org/wiki/Special:Log/Marudubshinki", ""))
  , (Link nullAttr [Str "Deletion log"] ("https://en.wikipedia.org/w/index.php?title=Special:Log&type=delete&user=&page=Gernot+Pfl%C3%BCger&year=&month=-1&tagfilter=",""),
      Link ("", ["content-transform-not", "link-live-not"], []) [Str "Deletion log"] ("https://en.wikipedia.org/w/index.php?title=Special:Log&type=delete&user=&page=Gernot+Pfl%C3%BCger&year=&month=-1&tagfilter=", ""))

    -- TODO: historical page versions should be queryable, but the WP popups code doesn't support that yet, so for now we must mark them non-popuable
  , (Link nullAttr [Str "GCTA"] ("https://en.wikipedia.org/w/index.php?title=Genome-wide_complex_trait_analysis&oldid=*********", ""),
     Link ("", ["content-transform-not", "link-live-not"], []) [Str "GCTA"] ("https://en.wikipedia.org/w/index.php?title=Genome-wide_complex_trait_analysis&oldid=*********", ""))
  ]

quoteOverrides :: [T.Text]
quoteOverrides =  ["Antoine's", "Bloomingdale's", "Collier's", "Kinko's", "Mzoli's", "Denny's", "Denny’s",
                    "Security_hacker#Birth_of_subculture_and_entering_mainstream:_1960's-1980's",
                    "Security hacker#Birth of subculture and entering mainstream: 1960's-1980's"]

-- manual override of a particular WP link A with another target B.
-- This is useful for skipping redirects & ensuring all variants globally produce the same WP link (which is helpful for LinkSuggester to avoid false negatives).
-- This can also be used to override links to disambiguation pages: for example, 'Dialysis' is a disambiguation page, even though the other uses are so obscure, so one can force that to point to the always-intended `Kidney_dialysis` instead.
--
-- Testing: this is tested in `Test` for: key-uniqueness, graph cycles, and value valid-as-URL. (We cannot require 'key valid as URI or URL', since sometimes we're correcting Unicode-in-URL.)
redirectDB :: [(T.Text, T.Text)]
redirectDB = let wp u = if "http" `T.isPrefixOf` u then u -- allow overrides of arbitrary URLs (eg. other WMF projects, or for deleted articles, IA)
                        else T.append "https://en.wikipedia.org/wiki/" u in
               map (\(a,b) -> (wp a, wp b)) $ [
          ("WP:RS", "Wikipedia:Reliable_sources")
          , ("%2420", "United_States_twenty-dollar_bill")
          , ("%C3%80_la_recherche_du_temps_perdu", "In_Search_of_Lost_Time")
          , ("%C3%89cole_Gr%C3%A9goire_Ferrandi", "%C3%89cole_Gr%C3%A9goire-Ferrandi")
          , ("%CE%912_receptor", "Alpha-2_adrenergic_receptor")
          , ("%CE%917_nicotinic_receptors", "Alpha-7_nicotinic_receptor")
          , ("%CE%92-galactosidase", "%CE%92-Galactosidase")
          , ("%CE%92-pinene", "%CE%92-Pinene")
          , ("%ce%93-aminobutyric_acid", "%CE%93-Aminobutyric_acid")
          , (".alt_newsgroup", "Alt.*_hierarchy")
          , (".srt", "SubRip")
          , ("/dev/null", "Null_device")
          , ("1%25_rule_(Internet_culture)", "1%25_rule")
          , ("100%2C000_Genomes_Project", "100,000_Genomes_Project")
          , ("1000_Genomes", "1000_Genomes_Project")
          , ("120_Days_of_Sodom", "The_120_Days_of_Sodom")
          , ("15_puzzle", "15_Puzzle")
          , ("16S_ribosomal_DNA", "16S_ribosomal_RNA")
          , ("16mm_film", "16_mm_film")
          , ("1804_Haiti_massacre", "1804_Haitian_massacre")
          , ("1906_San_Francisco_Fire", "1906_San_Francisco_earthquake#Fires")
          , ("1917_Russian_Revolution", "Russian_Revolution")
          , ("1918_flu_pandemic", "Spanish_flu")
          , ("1924_Immigration_Act", "Immigration_Act_of_1924")
          , ("1964_New_York_World's_Fair", "1964_New_York_World%27s_Fair")
          , ("1970_World%27s_Fair", "Expo_%2770")
          , ("1970_World's_Fair", "Expo_%2770")
          , ("1983_Beirut_barracks_bombing", "1983_Beirut_barracks_bombings")
          , ("1992-1993_Jack_in_the_Box_E._coli_outbreak", "1992%E2%80%931993_Jack_in_the_Box_E._coli_outbreak")
          , ("1993_Jack_in_the_Box_E._coli_outbreak", "1992%E2%80%931993_Jack_in_the_Box_E._coli_outbreak")
          , ("2%2C4-Dinitrophenol", "2,4-Dinitrophenol")
          , ("2-butoxyethanol", "2-Butoxyethanol")
          , ("2-ethylhexanol", "2-Ethylhexanol")
          , ("20,000_Leagues_Under_the_Sea", "Twenty_Thousand_Leagues_Under_the_Seas")
          , ("2000_US_presidential_election", "2000_United_States_presidential_election")
          , ("2001:_A_Space_Odyssey_(film)", "2001:_A_Space_Odyssey")
          , ("2007_Glasgow_International_Airport_attack", "2007_Glasgow_Airport_attack")
          , ("2010_(film)", "2010:_The_Year_We_Make_Contact")
          , ("2011_Tohoku_earthquake_and_tsunami", "2011_T%C5%8Dhoku_earthquake_and_tsunami")
          , ("2015_Thalys_attack", "2015_Thalys_train_attack")
          , ("2016_Berlin_attack", "2016_Berlin_truck_attack")
          , ("2016_Nice_attack", "2016_Nice_truck_attack")
          , ("2016_W%C3%BCrzburg_train_attack", "W%C3%BCrzburg_train_attack")
          , ("2017_Congressional_baseball_shooting", "Congressional_baseball_shooting")
          , ("2018_US_midterm_election", "2018_United_States_elections")
          , ("2019%E2%80%9320_coronavirus_outbreak", "COVID-19_pandemic")
          , ("2019_Japan%E2%80%93South_Korea_trade_dispute", "Japan%E2%80%93South_Korea_trade_dispute")
          , ("2019_United_States_outbreak_of_lung_illness_linked_to_vaping_products", "2019%E2%80%932020_vaping_lung_illness_outbreak")
          , ("2020_Twitter_bitcoin_scam", "2020_Twitter_account_hijacking")
          , ("2020_coronavirus_pandemic_in_the_United_States", "COVID-19_pandemic_in_the_United_States")
          , ("2021%E2%80%932022_North_Korean_missile_tests", "2021%E2%80%932023_North_Korean_missile_tests")
          , ("2022_Hunga_Tonga_eruption_and_tsunami", "2022_Hunga_Tonga%E2%80%93Hunga_Ha%CA%BBapai_eruption_and_tsunami")
          , ("2022_Russian_invasion_of_Ukraine", "Russian_invasion_of_Ukraine")
          , ("22_nanometer", "22_nm_process")
          , ("24-bit_color", "Color_depth#True_color_(24-bit)")
          , ("24_Game", "24_(puzzle)")
          , ("3%2C4-Methylenedioxyamphetamine", "3,4-Methylenedioxyamphetamine")
          , ("3,4-Methylenedioxymethamphetamine", "MDMA")
          , ("3,4-methylenedioxymethamphetamine", "MDMA")
          , ("3-SAT", "Boolean_satisfiability_problem#3-satisfiability")
          , ("3-quinuclidinyl_benzilate", "3-Quinuclidinyl_benzilate")
          , ("3D-printed", "3D_printing")
          , ("3D_CAD", "3D_modeling")
          , ("5-HT1A", "5-HT1A_receptor")
          , ("5-HT2A", "5-HT2A_receptor")
          , ("5-methoxy-N,N-dimethyltryptamine", "5-MeO-DMT")
          , ("501(c)", "501(c)_organization")
          , ("501(c)(3)", "501(c)(3)_organization")
          , ("5_Factor_Model_personality_traits", "Big_Five_personality_traits")
          , ("64-bit", "64-bit_computing")
          , ("7_Habits_of_Highly_Effective_People", "The_7_Habits_of_Highly_Effective_People")
          , ("7zip", "7-Zip")
          , ("8-hour_day", "Eight-hour_day")
          , ("8mm_film", "8_mm_film")
          , ("9/11", "September_11_attacks")
          , ("9/11_Truthers", "9/11_truth_movement")
          , ("A%2FB_tests", "A/B_testing")
          , ("A-bomb", "Nuclear_weapon")
          , ("A-ketoglutarate", "%CE%91-Ketoglutaric_acid")
          , ("A-series_and_B-series", "A_series_and_B_series")
          , ("A.E._Housman", "A._E._Housman")
          , ("A.E._Van_Vogt", "A._E._van_Vogt")
          , ("A.E._Vogt", "A._E._van_Vogt")
          , ("A.J._Budrys", "Algis_Budrys")
          , ("A.P._biology", "AP_Biology")
          , ("A.R.I.E.L.", "Ariel_(novel_series)")
          , ("A._E._Van_Vogt", "A._E._van_Vogt")
          , ("A/B_tests", "A/B_testing")
          , ("A1c", "Glycated_hemoglobin")
          , ("ABM_treaty", "Anti-Ballistic_Missile_Treaty")
          , ("ABU_RoboCon", "ABU_Robocon")
          , ("ADHD", "Attention_deficit_hyperactivity_disorder")
          , ("AES-256", "Advanced_Encryption_Standard")
          , ("AGH_University_of_Science_and_Technology", "AGH_University_of_Krakow")
          , ("AIDS", "HIV/AIDS")
          , ("AI_Dungeon_2", "AI_Dungeon")
          , ("AI_Winter", "AI_winter")
          , ("ALCAR", "Acetylcarnitine")
          , ("ANCOVA", "Analysis_of_covariance")
          , ("ANOVA", "Analysis_of_variance")
          , ("AOL_search_data_scandal", "AOL_search_log_release")
          , ("ARIMA", "Autoregressive_integrated_moving_average")
          , ("ARM_architecture", "ARM_architecture_family")
          , ("ASIC", "Application-specific_integrated_circuit")
          , ("AUROC", "Receiver_operating_characteristic")
          , ("AVX512", "AVX-512")
          , ("A_Connecticut_Yankee_in_King_Arthur's_Court", "A_Connecticut_Yankee_in_King_Arthur%27s_Court")
          , ("A_Deepness_In_The_Sky", "A_Deepness_in_the_Sky")
          , ("A_LEGO_Brickumentary", "A_Lego_Brickumentary")
          , ("A_Lesson_is_Learned", "A_Lesson_Is_Learned_but_the_Damage_Is_Irreversible")
          , ("A_Mathematician's_Apology", "A_Mathematician%27s_Apology")
          , ("A_Mathematician's_Miscellany", "A_Mathematician%27s_Miscellany")
          , ("A_Quiet_Place_(film)", "A_Quiet_Place")
          , ("A_State_of_Mind", "A_State_of_Mind_(film)")
          , ("A_Word_A_Day", "Anu_Garg")
          , ("Aaahh%21%21%21_Real_Monsters", "Aaahh!!!_Real_Monsters")
          , ("Aalib", "AAlib")
          , ("Ab_Urbe_Condita_Libri", "History_of_Rome_(Livy)")
          , ("Abd_Allah_ibn_Alawi_al-Haddad", "%27Abdallah_ibn_%27Alawi_al-Haddad")
          , ("Abelard", "Peter_Abelard")
          , ("Aberrant_salience", "Salience_(neuroscience)#Aberrant_salience_hypothesis_of_schizophrenia")
          , ("Aboulia", "Abulia")
          , ("Absorption_spectrum", "Absorption_spectroscopy#Absorption_spectrum")
          , ("Absurdist_humor", "Surreal_humour")
          , ("Abu_Iyad", "Salah_Khalaf")
          , ("Accountancy", "Accounting")
          , ("Accumbal", "Nucleus_accumbens")
          , ("Accumulator_register", "Accumulator_(computing)")
          , ("Ace_o_Nerae", "Aim_for_the_Ace!")
          , ("Acetaminophen", "Paracetamol")
          , ("Acetyl-coenzyme_A", "Acetyl-CoA")
          , ("Achaea_%28ancient_region%29", "Achaea_(ancient_region)")
          , ("Acheta_domesticus", "House_cricket")
          , ("Ackee_fruit", "Blighia_sapida")
          , ("Acronyms", "Acronym")
          , ("Actigraphs", "Actigraphy")
          , ("Actinobacteria", "Actinomycetota")
          , ("Activated_charcoal", "Activated_carbon")
          , ("Active_inference", "Free_energy_principle")
          , ("Active_recall", "Testing_effect")
          , ("Actor%E2%80%93critic_method", "Reinforcement_learning#Direct_policy_search")
          , ("Acts_of_God", "Act_of_God")
          , ("AdSense", "Google_AdSense")
          , ("AdWords", "Google_Ads")
          , ("Ada_programming_language", "Ada_(programming_language)")
          , ("Adam_D'Angelo", "Adam_D%27Angelo")
          , ("Adaptive_clinical_trial", "Adaptive_design_(medicine)")
          , ("Adaptive_immunity", "Adaptive_immune_system")
          , ("Admiral_Nimitz", "Chester_W._Nimitz")
          , ("Advanced_Chess", "Advanced_chess")
          , ("Advanced_Micro_Devices", "AMD")
          , ("Advanced_Strategic_Computing_Initiative", "Advanced_Simulation_and_Computing_Program")
          , ("Adversarial_examples", "Adversarial_machine_learning#Adversarial_examples")
          , ("Adverse_drug_event", "Adverse_drug_reaction")
          , ("Aegean_Seas", "Aegean_Sea")
          , ("Aeropagitica", "Areopagitica")
          , ("Aerosmith's_Rock_n_Roller_Coaster", "Aerosmith%27s_Rock_n_Roller_Coaster")
          , ("Affective_disorders", "Affective_spectrum")
          , ("Affero_GPLv3", "GNU_Affero_General_Public_License")
          , ("African_lion", "Lion")
          , ("Africa–North_Korea_relations", "Africa%E2%80%93North_Korea_relations")
          , ("Against_the_Odds_(novel)", "Elizabeth_Moon")
          , ("Age_heaping", "Whipple%27s_index")
          , ("Agency_problems", "Principal%E2%80%93agent_problem")
          , ("Agile_development", "Agile_software_development")
          , ("Agnatic", "Patrilineality")
          , ("Agricultural_Research_Organization,_Volcani_Center", "Volcani_Center")
          , ("Ahmad_Khan_Rahami", "2016_New_York_and_New_Jersey_bombings#Suspect")
          , ("Aichi_dialect", "T%C5%8Dkai%E2%80%93T%C5%8Dsan_dialect")
          , ("Aim_For_The_Top_2%21", "Diebuster")
          , ("Aim_for_the_Ace", "Aim_for_the_Ace!")
          , ("Air_Force_Space_Command", "Space_Operations_Command")
          , ("Airhammer", "Transformers:_Generation_1")
          , ("Akai_Takami", "Takami_Akai")
          , ("Akashic_Records", "Akashic_records")
          , ("Akihabara_massacre", "2008_Akihabara_massacre")
          , ("Al_Qaeda", "Al-Qaeda")
          , ("Alanine_aminotransferase", "Alanine_transaminase")
          , ("Alaska_malamutes", "Alaskan_Malamute")
          , ("Albert,_Prince_Consort", "Prince_Albert_of_Saxe-Coburg_and_Gotha")
          , ("Albert_Abraham_Michelson", "Albert_A._Michelson")
          , ("Albumen", "Egg_white")
          , ("Alcohol_use_disorder", "Alcoholism")
          , ("Alcohol_use_disorders", "Alcoholism")
          , ("Aleksandr_Kolchak", "Alexander_Kolchak")
          , ("Alexa_Toolbar", "Alexa_Internet#Toolbar")
          , ("Alfred_Deakin_Institute", "Deakin_University#Research")
          , ("Alibaba", "Alibaba_Group")
          , ("Alibaba.com", "Alibaba_Group#E-commerce_and_retail_service_platforms")
          , ("Alice_in_Wonderland", "Alice%27s_Adventures_in_Wonderland")
          , ("Alief_(belief)", "Alief_(mental_state)")
          , ("Alkaloids", "Alkaloid")
          , ("All-cause_mortality", "Mortality_rate")
          , ("All_the_President's_Men", "All_the_President%27s_Men")
          , ("Allerca", "Lifestyle_Pets")
          , ("Alpha-PVP", "Alpha-Pyrrolidinopentiophenone")
          , ("Alpha-amylase", "%CE%91-Amylase")
          , ("Alpha-ketoglutarate", "%CE%91-Ketoglutaric_acid")
          , ("Alpha_(investment)", "Alpha_(finance)")
          , ("Aluminum", "Aluminium")
          , ("Aluminum_in_deodorant", "Deodorant#Aluminum")
          , ("Alvin_Plantinga%27s_free_will_defense", "Alvin_Plantinga%27s_free-will_defense")
          , ("Alvin_Plantinga's_free_will_defense", "Alvin_Plantinga%27s_free-will_defense")
          , ("Alzheimer", "Alzheimer%27s_disease")
          , ("Alzheimer's_disease", "Alzheimer%27s_disease")
          , ("Alzheimer’s_disease", "Alzheimer%27s_disease")
          , ("Amara%27s_law", "Roy_Amara#Amara%27s_law")
          , ("Amara's_law", "Roy_Amara")
          , ("Amateur_Press_Association", "Amateur_press_association")
          , ("Amazing_Science_Fiction_Stories", "Amazing_Stories")
          , ("Amazon's_Mechanical_Turk", "Amazon%27s_Mechanical_Turk")
          , ("Amazon.com", "Amazon_(company)")
          , ("Amazon_Affiliates", "Amazon_(company)#Third-party_sellers")
          , ("Ambien", "Zolpidem")
          , ("Amdahl%27s_Law", "Amdahl%27s_law")
          , ("Amdahl's_Law", "Amdahl%27s_law")
          , ("Amdahl's_law", "Amdahl%27s_law")
          , ("American_Academy_of_Art", "American_Academy_of_Art_College")
          , ("American_Dad", "American_Dad!")
          , ("American_Mathematical_Monthly", "The_American_Mathematical_Monthly")
          , ("American_Way_of_Life", "American_way")
          , ("Amino_acids", "Amino_acid")
          , ("Amniotes", "Amniote")
          , ("Amphetamine_psychosis", "Stimulant_psychosis#Amphetamines")
          , ("Amphetamines", "Substituted_amphetamine")
          , ("Amphipods", "Amphipoda")
          , ("Amur_tigers", "Siberian_tiger")
          , ("Amyloid_plaque", "Amyloid_plaques")
          , ("Amyloid_precursor_protein", "Amyloid-beta_precursor_protein")
          , ("Amyotrophic_lateral_sclerosis", "ALS")
          , ("An_Essay_towards_a_Real_Character_and_a_Philosophical_Language", "An_Essay_Towards_a_Real_Character,_and_a_Philosophical_Language")
          , ("An_Nafud", "Nafud_desert")
          , ("Anagrams", "Anagram")
          , ("Anakin_Skywalker", "Darth_Vader")
          , ("Analog_computers", "Analog_computer")
          , ("Analytic_languages", "Analytic_language")
          , ("Ananteris_balzani", "Ananteris_balzanii")
          , ("Anatomy_of_Melancholy", "The_Anatomy_of_Melancholy")
          , ("AncestryDNA", "Ancestry.com")
          , ("Anchialine_pool", "Anchialine_system")
          , ("Anchor_(cognitive_bias)", "Anchoring_effect")
          , ("Anchoring", "Anchor")
          , ("Anchoring_(cognitive_bias)", "Anchoring_effect")
          , ("Ancient_tea_route", "Tea_Horse_Road")
          , ("Andersen_Consulting", "Accenture")
          , ("Andon_cord", "Andon_(manufacturing)")
          , ("Andrew_Goff", "International_prize_list_of_Diplomacy#WorldDipCon")
          , ("Andrew_Sorkin", "Andrew_Ross_Sorkin")
          , ("Andromeda_galaxy", "Andromeda_Galaxy")
          , ("Angel's_Egg", "Angel%27s_Egg")
          , ("Angel_investment", "Angel_investor")
          , ("Angiotensin_I", "Angiotensin#Angiotensin_I")
          , ("Anglican", "Anglicanism")
          , ("Animal_Crossing%3A_New_Horizons", "Animal_Crossing:_New_Horizons")
          , ("Animal_behavior", "Ethology")
          , ("Animal_health", "Veterinary_medicine")
          , ("Animal_personality", "Personality_in_animals")
          , ("Animal_trials", "Animal_trial")
          , ("Anime-Con", "AnimeCon")
          , ("Anime_music_videos", "Anime_music_video")
          , ("Animeigo", "AnimEigo")
          , ("Anitya", "Impermanence#Hinduism")
          , ("Anki_%28software%29", "Anki_(software)")
          , ("Ann_Arbor", "Ann_Arbor,_Michigan")
          , ("Anne_of_Green_Gables_(anime)", "Anne_of_Green_Gables_(1979_TV_series)")
          , ("AnoHana", "Anohana")
          , ("Anorexia", "Anorexia_nervosa")
          , ("Anorexigenic", "Anorectic")
          , ("Ant_Financial", "Ant_Group")
          , ("Antagonistic_Pleiotropy_Theory", "Pleiotropy#Antagonistic_pleiotropy")
          , ("Antagonistic_pleiotropy", "Antagonistic_pleiotropy_hypothesis")
          , ("Anterior_cingulate", "Anterior_cingulate_cortex")
          , ("Anthocyanins", "Anthocyanin")
          , ("Anthropic_biases", "Anthropic_principle")
          , ("Anti-Federalists", "Anti-Federalism")
          , ("Anti-Revisionism_(Marxism%E2%80%93Leninism)", "Anti-revisionism_(Marxism%E2%80%93Leninism)")
          , ("Anti-revisionism", "Anti-revisionism_(Marxism%E2%80%93Leninism)")
          , ("Anti-satellite_missiles", "Anti-satellite_weapon")
          , ("Anticholinergics", "Anticholinergic")
          , ("Antidepressant_drugs", "Antidepressant")
          , ("Antidepressant_medication", "Antidepressant")
          , ("Antidepressants", "Antidepressant")
          , ("Antidiabetic_biguanides", "Biguanide#Antihyperglycemic_agents")
          , ("Antigens", "Antigen")
          , ("Antigravity", "Anti-gravity")
          , ("Antihypertensive", "Antihypertensive_drug")
          , ("Antipsychotics", "Antipsychotic")
          , ("Antirealism", "Anti-realism")
          , ("Antoine's", "Antoine%27s")
          , ("Antoni_van_Leeuwenhoek", "Antonie_van_Leeuwenhoek")
          , ("Anxi_county", "Anxi_County")
          , ("Anxiety_disorders", "Anxiety_disorder")
          , ("Ao_no_Rokugo", "Blue_Submarine_No._6")
          , ("Aoki_Uru", "Uru_in_Blue")
          , ("Aperiodic_monotile", "Einstein_problem")
          , ("Apocalypse_Meow", "Cat_Shit_One")
          , ("Apolipoprotein%28a%29", "Lipoprotein(a)#Structure")
          , ("App_Store_(iOS/iPadOS)", "App_Store_(Apple)")
          , ("Apple_Computer", "Apple_Inc.")
          , ("Apple_Inc", "Apple_Inc.")
          , ("Apple_T1", "Apple_silicon#Apple_T1")
          , ("Apple_TV+", "Apple_TV%2B")
          , ("Appleseed_%28OVA%29", "Appleseed_(1988_film)")
          , ("Appleseed_%28manga%29", "Appleseed_(manga)")
          , ("Application_programming_interface", "API")
          , ("Applied_microeconomics", "Microeconomics#Applied_microeconomics")
          , ("April_2021_United_States_Capitol_attack", "2021_United_States_Capitol_car_attack")
          , ("Arabia", "Arabian_Peninsula")
          , ("Arakawa_Under_the_Bridge_x_Bridge", "Arakawa_Under_the_Bridge")
          , ("Archimedes'_Law", "Archimedes%27_Law")
          , ("Arctic_hares", "Arctic_hare")
          , ("Arctic_ice", "Arctic_ice_pack")
          , ("Arctocephalus_pusillus_pusillus", "Brown_fur_seal")
          , ("Ariel_%28anime%29", "Ariel_(novel_series)")
          , ("Arm_(company)", "Arm_Holdings")
          , ("Armed_conflict", "War")
          , ("Arnold_Ventures", "Arnold_Ventures_LLC")
          , ("Array_data_structure", "Array_(data_structure)")
          , ("Ars%C3%A8ne_Lupin_III", "Lupin_III_(character)")
          , ("Arsene_Lupin", "Ars%C3%A8ne_Lupin")
          , ("Art_of_the_Third_Reich", "Art_in_Nazi_Germany")
          , ("Arthur_D._Little,_Inc.", "Arthur_D._Little")
          , ("Arthur_L._Norberg", "Arthur_Norberg")
          , ("Articles_of_Incorporation", "Articles_of_association")
          , ("Artificial_Intelligence", "Artificial_intelligence")
          , ("Artificial_selection", "Selective_breeding")
          , ("Arxiv", "ArXiv")
          , ("Ashkenazi", "Ashkenazi_Jews")
          , ("Aspect-oriented", "Aspect-oriented_programming")
          , ("Asperger's_Syndrome", "Asperger_syndrome")
          , ("Asperger's_syndrome", "Asperger_syndrome")
          , ("Assassination_markets", "Assassination_market")
          , ("Associative_learning", "Learning#Associative_learning")
          , ("Asteroid-impact_avoidance", "Asteroid_impact_avoidance")
          , ("Asuka_Souryuu_Langley", "Asuka_Langley_Soryu")
          , ("Atari_Inc", "Atari,_Inc.")
          , ("Atari_games", "Atari_Games")
          , ("Atlanta%2C_Georgia", "Atlanta")
          , ("Atlanta_Campaign", "Atlanta_campaign")
          , ("Atlas.ti", "ATLAS.ti")
          , ("Atlatl", "Spear-thrower")
          , ("Atomic_absorption_spectrophotometer", "Atomic_absorption_spectroscopy")
          , ("Atomic_demolition_munitions", "Atomic_demolition_munition")
          , ("Atomic_isotope_separation_methods", "Isotope_separation")
          , ("Atomists", "Atomism")
          , ("Attack_submarines", "Attack_submarine")
          , ("Attention_deficit_disorder", "Attention_deficit_hyperactivity_disorder")
          , ("Attention_switching", "Attentional_shift")
          , ("Attenuation_bias", "Regression_dilution")
          , ("Attractive_nuisance", "Attractive_nuisance_doctrine")
          , ("Audiologist", "Audiology#Audiologist")
          , ("Auditory_hallucinations", "Auditory_hallucination")
          , ("Auditory_perception", "Hearing")
          , ("Audubon_Society", "Audubon")
          , ("Australopithecines", "Australopithecine")
          , ("Austrian_School", "Austrian_school_of_economics")
          , ("Autism", "Autism_spectrum")
          , ("Autism-Spectrum_Quotient", "Autism-spectrum_quotient")
          , ("Autism_Spectrum", "Autism_spectrum")
          , ("Autism_spectrum_disorder", "Autism_spectrum")
          , ("Autism_spectrum_disorders", "Autism_spectrum")
          , ("Autistic_traits", "Autism_spectrum#Features_and_characteristics")
          , ("Autoencoders", "Autoencoder")
          , ("Autogyros", "Autogyro")
          , ("Autoimmune_diseases", "Autoimmune_disease")
          , ("Autolysis_%28biology%29", "Autolysis_(biology)")
          , ("Automated_program_repair", "Automatic_bug_fixing")
          , ("Automated_summarization", "Automatic_summarization")
          , ("Autonomous_car", "Self-driving_car")
          , ("Autonomous_sensory_meridian_response", "ASMR")
          , ("Autonomous_vehicle", "Vehicular_automation")
          , ("Autopilots", "Autopilot")
          , ("Autoregressive_Modeling", "Autoregressive_model")
          , ("Autosomal_recessive", "Dominance_(genetics)")
          , ("Autotools", "GNU_Autotools")
          , ("Availability_bias", "Availability_heuristic")
          , ("Average_is_Over", "Average_Is_Over")
          , ("Aviation_Week", "Aviation_Week_%26_Space_Technology")
          , ("Avogadro%27s_number", "Avogadro_constant")
          , ("Avogadro's_number", "Avogadro_constant")
          , ("Axiom_of_Choice", "Axiom_of_choice")
          , ("Axiomatic_(story_collection)", "Axiomatic_(book)")
          , ("Axis_I", "Diagnostic_and_Statistical_Manual_of_Mental_Disorders#Multi-axial_system")
          , ("Ayatollah_Ruhollah_Khomeini", "Ruhollah_Khomeini")
          , ("Azande", "Azande_people")
          , ("Azure_cloud_computing_services", "Microsoft_Azure")
          , ("B.F._Skinner", "B._F._Skinner")
          , ("B._anthracis", "Bacillus_anthracis")
          , ("BDFL", "Benevolent_dictator_for_life")
          , ("BETDAQ", "Betdaq")
          , ("BMAL1", "Basic_helix-loop-helix_ARNT-like_protein_1")
          , ("BPTT", "Backpropagation_through_time")
          , ("BSD", "Berkeley_Software_Distribution")
          , ("BSD_Unix", "Berkeley_Software_Distribution")
          , ("Baader-Meinhof_effect", "Frequency_illusion")
          , ("Baboons", "Baboon")
          , ("Baby_Boomers", "Baby_boomers")
          , ("Babylonian_Talmud", "Talmud")
          , ("Bacillus_(bacteria)", "Bacillus")
          , ("Bacillus_marismortui", "Virgibacillus_marismortui")
          , ("Bacillus_phage_SPbeta", "Spbetavirus")
          , ("Bacillus_phages", "Bacillus_phage")
          , ("Bacillus_sphaericus", "Lysinibacillus_sphaericus")
          , ("Back_To_The_Future", "Back_to_the_Future")
          , ("Backdoor_%28computing%29", "Backdoor_(computing)")
          , ("Backfire_effect", "Belief_perseverance")
          , ("Backlinks", "Backlink")
          , ("Backus-Naur_form", "Backus%E2%80%93Naur_form")
          , ("Backwards_digit_span", "Memory_span")
          , ("Backwards_induction", "Backward_induction")
          , ("Bacterial_meningitis", "Meningitis#Bacterial")
          , ("Bacterial_toxins", "Microbial_toxin#Bacterial")
          , ("Bacteroidetes", "Bacteroidota")
          , ("Baddeley's_model_of_working_memory", "Baddeley%27s_model_of_working_memory")
          , ("Bag-of-words", "Bag-of-words_model")
          , ("Bag_of_words", "Bag-of-words_model")
          , ("Baidu_Zhidao", "Baidu_Knows")
          , ("Bail_Bonds", "Bail_bondsman")
          , ("Baja_peninsula", "Baja_California_peninsula")
          , ("Bakemonogatari", "Monogatari_(series)")
          , ("Bakuretsu_Toshi", "Burst_City")
          , ("Balance_books", "Bookkeeping")
          , ("Baldur", "Baldr")
          , ("Ballistic_missile_submarines", "Ballistic_missile_submarine")
          , ("Ban_(information)", "Hartley_(unit)")
          , ("Bang_Ippongi", "Ippongi_Bang")
          , ("Bar_Exam", "Bar_examination")
          , ("Barbegal_aqueduct_and_mill", "Barbegal_aqueduct_and_mills")
          , ("Barcamp", "BarCamp")
          , ("Barnard’s_Star", "Barnard%27s_Star")
          , ("Barnes_and_Noble", "Barnes_%26_Noble")
          , ("Barney_Oliver", "Bernard_M._Oliver")
          , ("Baron_Harkonnen", "Vladimir_Harkonnen")
          , ("Baron_de_Montyon", "Jean_Baptiste_Antoine_Auget_de_Montyon")
          , ("Barrage_balloons", "Barrage_balloon")
          , ("Barrio_Logan", "Barrio_Logan,_San_Diego")
          , ("Bartholomeus_Anglicus", "Bartholomaeus_Anglicus")
          , ("Bartlett's_Familiar_Quotations", "Bartlett%27s_Familiar_Quotations")
          , ("Barts_and_the_London_School_of_Medicine_and_Dentistry", "Barts_and_The_London_School_of_Medicine_and_Dentistry")
          , ("Base-64", "Base64")
          , ("Base-pair", "Base_pair")
          , ("Base-rate", "Base_rate")
          , ("Base-rate_neglect", "Base_rate_fallacy")
          , ("Base_pairs", "Base_pair")
          , ("Base_rates", "Base_rate")
          , ("Bash_%28Unix_shell%29", "Bash_(Unix_shell)")
          , ("Basic_income_guarantee", "Universal_basic_income")
          , ("Basilica_of_San_Marco", "St_Mark%27s_Basilica")
          , ("Basis_functions", "Basis_function")
          , ("Bassoonist", "Bassoon")
          , ("Bat-borne_virus", "Bat_virome")
          , ("Battle_of_Fort_Eben-Emael", "Battle_of_Fort_%C3%89ben-%C3%89mael")
          , ("Baumol%27s_cost_disease", "Baumol_effect")
          , ("Baumol's_cost_disease", "Baumol_effect")
          , ("Bay_Area", "San_Francisco_Bay_Area")
          , ("Bay_Laurel", "Laurus_nobilis")
          , ("Bayes%27_Theorem", "Bayes%27_theorem")
          , ("Bayes%27_rule", "Bayes%27_theorem")
          , ("Bayes%27s_theorem", "Bayes%27_theorem")
          , ("Bayes'_Theorem", "Bayes%27_theorem")
          , ("Bayes'_rule", "Bayes%27_theorem")
          , ("Bayes'_theorem", "Bayes%27_theorem")
          , ("Bayes's_theorem", "Bayes%27_theorem")
          , ("Bayes_factors", "Bayes_factor")
          , ("Bayes_theorem", "Bayes%27_theorem")
          , ("Bayesian_model_comparison", "Bayes_factor")
          , ("Bayesian_neural_network", "Artificial_neural_network#Stochastic_neural_network")
          , ("Bayesian_optimal_experiment_design", "Bayesian_experimental_design")
          , ("Bayesian_spam_filtering", "Naive_Bayes_spam_filtering")
          , ("Bayesian_theory", "Bayesian_probability")
          , ("Bayh-Dole_Act", "Bayh%E2%80%93Dole_Act")
          , ("Bean_machine", "Galton_board")
          , ("Bear_100-miler", "Bear_100_Mile_Endurance_Run")
          , ("Behavior_genetics", "Behavioural_genetics")
          , ("Behavioral_genetics", "Behavioural_genetics")
          , ("Beijing_Genomics_Institute", "BGI_Group")
          , ("Being_sane_in_insane_places", "Rosenhan_experiment")
          , ("Bell's_theorem", "Bell%27s_theorem")
          , ("Bell_Laboratories", "Bell_Labs")
          , ("Beltway_sniper_attacks", "D.C._sniper_attacks")
          , ("Bene_Tleilax", "Organizations_of_the_Dune_universe#Bene_Tleilax")
          , ("Benford's_law", "Benford%27s_law")
          , ("Bengali_Renaissance", "Bengal_Renaissance")
          , ("Benjamini-Hochberg_false_positive_rate_correction_test", "False_discovery_rate#Benjamini%E2%80%93Hochberg_procedure")
          , ("Benjamini-Yekutieli_correction", "False_discovery_rate#Benjamini%E2%80%93Yekutieli_procedure")
          , ("Benzodiazepine_withdrawal", "Benzodiazepine_withdrawal_syndrome")
          , ("Benzodiazepines", "Benzodiazepine")
          , ("Benztropine", "Benzatropine")
          , ("Berkson's_paradox", "Berkson%27s_paradox")
          , ("Berserkers", "Berserker")
          , ("Best_Linear_Unbiased_Prediction", "Best_linear_unbiased_prediction")
          , ("Beta-Alanine", "%CE%92-Alanine")
          , ("Beta-Hydroxybutyric_acid", "%CE%92-Hydroxybutyric_acid")
          , ("Betrayers_of_the_Truth:_Fraud_and_Deceit_in_the_Halls_of_Science", "Betrayers_of_the_Truth")
          , ("Between-group_design", "Between-group_design_experiment")
          , ("Beyond_Good_and_Evil:_Prelude_to_a_Philosophy_of_the_Future", "Beyond_Good_and_Evil")
          , ("Bhujapidasana", "Tittibhasana")
          , ("Bias-variance_tradeoff", "Bias%E2%80%93variance_tradeoff")
          , ("Bicameral_mind_theory", "Bicameral_mentality")
          , ("Bicameralism_(psychology)", "Bicameral_mentality")
          , ("Bicycle_face", "Bicycling_and_feminism")
          , ("BigTable", "Bigtable")
          , ("Big_Five_personality", "Big_Five_personality_traits")
          , ("Big_Five_personality_factors", "Big_Five_personality_traits")
          , ("Big_Five_personality_trait", "Big_Five_personality_traits")
          , ("Big_Hero_6_%28film%29", "Big_Hero_6_(film)")
          , ("Big_Name_Fans", "Big_Name_Fan")
          , ("Big_Thunder_Mountain", "Big_Thunder_Mountain_Railroad")
          , ("Big_West_Advertising", "Macross#Title")
          , ("Bilateral_filters", "Bilateral_filter")
          , ("Bile_salt", "Bile_acid")
          , ("Bilingual_lexicography", "Lexicography")
          , ("Bill_%26_Ted's_Excellent_Adventure", "Bill_%26_Ted%27s_Excellent_Adventure")
          , ("Bill_Clinton-Monica_Lewinsky_affair", "Clinton%E2%80%93Lewinsky_scandal")
          , ("Billiard_ball_computer", "Billiard-ball_computer")
          , ("Bills_of_exchange", "Negotiable_instrument")
          , ("Binaural_beats", "Beat_(acoustics)#Binaural_beats")
          , ("Bing_(search_engine)", "Microsoft_Bing")
          , ("Bing_search_engine", "Microsoft_Bing")
          , ("Binjamin_Wilkomirski", "Fragments:_Memories_of_a_Wartime_Childhood")
          , ("Bio-energy_with_carbon_capture_and_storage", "Bioenergy_with_carbon_capture_and_storage")
          , ("Bioelectricity", "Developmental_bioelectricity")
          , ("Biofilms", "Biofilm")
          , ("Biological_Research_Centre", "Biological_Research_Centre_(Hungarian_Academy_of_Sciences)")
          , ("Biological_clock_(aging)", "Epigenetic_clock")
          , ("Biological_weapon", "Biological_agent")
          , ("Biomarkers", "Biomarker")
          , ("Biphasic_and_polyphasic_sleep", "Polyphasic_sleep")
          , ("Bipolar_Disorder", "Bipolar_disorder")
          , ("Bipolar_affective_disorder", "Bipolar_disorder")
          , ("Bipolar_disorders", "Bipolar_disorder")
          , ("Bipolar_disorders_research", "Bipolar_disorder")
          , ("Bipolar_spectrum", "Bipolar_disorder#Bipolar_spectrum")
          , ("Birch_trees", "Birch")
          , ("Bird_song", "Bird_vocalization")
          , ("Birkbeck_College", "Birkbeck,_University_of_London")
          , ("Birth_defects", "Birth_defect")
          , ("Birthday_paradox", "Birthday_problem")
          , ("Birthers", "Barack_Obama_citizenship_conspiracy_theories")
          , ("Bishojo", "Bish%C5%8Djo")
          , ("Bishoujo", "Bish%C5%8Djo")
          , ("Bit.ly", "Bitly")
          , ("BitInstant", "Charlie_Shrem")
          , ("Bitcoins", "Bitcoin")
          , ("Bitkeeper", "BitKeeper")
          , ("Bittermelon", "Momordica_charantia")
          , ("Bixby_(virtual_assistant)", "Bixby_(software)")
          , ("Black-footed_cats", "Black-footed_cat")
          , ("Black_Rain_%28Japanese_film%29", "Black_Rain_(1989_Japanese_film)")
          , ("Black_September_(group)", "Black_September_Organization")
          , ("Black_swans", "Black_swan")
          , ("Blast_furnaces", "Blast_furnace")
          , ("Blazing_Transfer_Student", "Hon%C5%8D_no_Tenk%C5%8Dsei")
          , ("Blessing_of_dimensionality", "Curse_of_dimensionality#Blessing_of_dimensionality")
          , ("Blind_experiment", "Blinded_experiment")
          , ("Blinder-Oaxaca_decomposition", "Blinder%E2%80%93Oaxaca_decomposition")
          , ("Blindness", "Visual_impairment")
          , ("Blockbuster_LLC", "Blockbuster_(retailer)")
          , ("Blocking_%28statistics%29", "Blocking_(statistics)")
          , ("Blocking_(scheduling)", "Blocking_(computing)")
          , ("Blogger", "Blog")
          , ("Blonde_hair", "Blond")
          , ("Blood_glucose", "Blood_sugar_level")
          , ("Bloodhounds", "Bloodhound")
          , ("Bloom%27s_2_Sigma_Problem", "Bloom%27s_2_sigma_problem")
          , ("Bloom's_2_Sigma_Problem", "Bloom%27s_2_sigma_problem")
          , ("Bloom's_2_sigma_problem", "Bloom%27s_2_sigma_problem")
          , ("Bloom_filters", "Bloom_filter")
          , ("Bloomingdale's", "Bloomingdale%27s")
          , ("Blue_Screen_of_Death", "Blue_screen_of_death")
          , ("Blue_Wizard_Is_About_To_Die%21", "Blue_Wizard_Is_About_To_Die!")
          , ("Blueshift", "Redshift#Blueshift")
          , ("Blum%27s_speed-up_theorem", "Blum%27s_speedup_theorem")
          , ("Blum's_speed-up_theorem", "Blum%27s_speedup_theorem")
          , ("Board_of_selectmen", "Select_board")
          , ("Bob's_Game", "Bob%27s_Game")
          , ("Bob_Axelrod", "Robert_Axelrod")
          , ("Body-mass_index", "Body_mass_index")
          , ("Body_weight", "Human_body_weight")
          , ("BoingBoing", "Boing_Boing")
          , ("Bollywood", "Hindi_cinema")
          , ("Boltzmann_brains", "Boltzmann_brain")
          , ("Bongo_drums", "Bongo_drum")
          , ("Bonn,_Germany", "Bonn")
          , ("Bonobos", "Bonobo")
          , ("Book_of_the_Long_Sun", "The_Book_of_the_Long_Sun")
          , ("Book_of_the_New_Sun", "The_Book_of_the_New_Sun")
          , ("Boots_UK", "Boots_(company)")
          , ("Bootstrapping_%28statistics%29", "Bootstrapping_(statistics)")
          , ("Borderline_disorder", "Borderline_personality_disorder")
          , ("Boreal_forest", "Taiga")
          , ("Borges", "Jorge_Luis_Borges")
          , ("Boring_Company", "The_Boring_Company")
          , ("Born_To_Run", "Born_to_Run")
          , ("Bosc_pears", "Bosc_pear")
          , ("Bose_statistics", "Bose%E2%80%93Einstein_statistics")
          , ("Botnets", "Botnet")
          , ("Bottom_(sex)", "Top,_bottom_and_versatile#Bottom")
          , ("Bouba-Kiki_effect", "Bouba/kiki_effect")
          , ("Bovine_tuberculosis", "Mycobacterium_bovis")
          , ("Bovine_viral_diarrhea_virus", "Bovine_viral_diarrhea")
          , ("Bow_riding", "Cetacean_intelligence#Complex_play")
          , ("Boy_or_Girl_paradox", "Boy_or_girl_paradox")
          , ("Bradley-Terry_model", "Bradley%E2%80%93Terry_model")
          , ("Braess's_paradox", "Braess%27s_paradox")
          , ("Brain-computer_interfaces", "Brain%E2%80%93computer_interface")
          , ("Brain-to-body_mass_ratio", "Brain%E2%80%93body_mass_ratio")
          , ("Brain_aging", "Aging_brain")
          , ("Brain_emulation", "Mind_uploading")
          , ("Brain_function", "Brain")
          , ("Brain_morphology", "Brain_size")
          , ("Brandolini's_law", "Brandolini%27s_law")
          , ("Brave_%282012_film%29", "Brave_(2012_film)")
          , ("Brave_Raideen", "Reideen_The_Brave")
          , ("Brazil_nut_effect", "Granular_convection")
          , ("Brazilian_Jiu-Jitsu", "Brazilian_jiu-jitsu")
          , ("Break-danced", "Breakdancing")
          , ("Brexiteers", "Glossary_of_Brexit_terms#Brexiteer")
          , ("Bricoleur", "Bricolage")
          , ("Brier_scores", "Brier_score")
          , ("Brigham_and_Women's_Hospital", "Brigham_and_Women%27s_Hospital")
          , ("Brink's-Mat_robbery", "Brink%27s-Mat_robbery")
          , ("Bristol_Myers-Squibb", "Bristol_Myers_Squibb")
          , ("British_Broadcasting_Corporation", "BBC")
          , ("British_Caribbean", "British_West_Indies")
          , ("British_Medical_Journal", "The_BMJ")
          , ("British_agricultural_revolution", "British_Agricultural_Revolution")
          , ("Broca's_area", "Broca%27s_area")
          , ("Broca’s_area", "Broca%27s_area")
          , ("Broken_window_fallacy", "Parable_of_the_broken_window")
          , ("Bront%C3%AB_sisters", "Bront%C3%AB_family")
          , ("Brood_parasite", "Brood_parasitism")
          , ("Brood_parasites", "Brood_parasitism")
          , ("Brown_Antechinus", "Brown_antechinus")
          , ("Brown_dwarfs", "Brown_dwarf")
          , ("Brussels_sprouts", "Brussels_sprout")
          , ("Bryan_Cave_Leighton_Paisner", "Bryan_Cave")
          , ("Buenos_Aires,_Argentina", "Buenos_Aires")
          , ("Buffer_overflows", "Buffer_overflow")
          , ("Buffon's_needle_problem", "Buffon%27s_needle_problem")
          , ("Buffon's_noodle", "Buffon%27s_noodle")
          , ("Buffon_needle", "Buffon%27s_needle_problem")
          , ("Buffon_needle_problem", "Buffon%27s_needle_problem")
          , ("Buffy_the_Animated_Series", "Buffy:_The_Animated_Series")
          , ("Buffy_the_Vampire_Slayer_(TV_series)", "Buffy_the_Vampire_Slayer")
          , ("Bufotenine", "Bufotenin")
          , ("Bug_report", "Bug_tracking_system")
          , ("Bulimia", "Bulimia_nervosa")
          , ("Bundesnachrichtendienst", "Federal_Intelligence_Service")
          , ("Burnside%27s_problem", "Burnside_problem")
          , ("Burnside's_problem", "Burnside_problem")
          , ("Burrows-Wheeler_transform", "Burrows%E2%80%93Wheeler_transform")
          , ("Businessweek", "Bloomberg_Businessweek")
          , ("Bussard_ramjets", "Bussard_ramjet")
          , ("Butler_W._Lampson", "Butler_Lampson")
          , ("Butlerian_Jihad", "Dune_(franchise)#The_Butlerian_Jihad")
          , ("Buyout_firm", "Private_equity_firm")
          , ("By-elections", "By-election")
          , ("Byzantine_Generals%27_Problem", "Byzantine_fault")
          , ("Byzantine_Generals'_Problem", "Byzantine_fault")
          , ("C%C3%A9zanne", "Paul_C%C3%A9zanne")
          , ("C++", "C%2B%2B")
          , ("C++_programming_language", "C%2B%2B")
          , ("C-section", "Caesarean_section")
          , ("C-type_natriuretic_peptide", "Natriuretic_peptide_precursor_C")
          , ("C.A.R._Hoare", "Tony_Hoare")
          , ("C.S._Lewis", "C._S._Lewis")
          , ("C._botulinum", "Clostridium_botulinum")
          , ("C._elegans", "Caenorhabditis_elegans")
          , ("CAPTCHAs", "CAPTCHA")
          , ("CC-BY-NC-ND", "Creative_Commons_license#Types_of_licenses")
          , ("CD8%2B_T_cell", "Cytotoxic_T_cell")
          , ("CDDL", "Common_Development_and_Distribution_License")
          , ("CDH2", "Cadherin-2")
          , ("CDKN1C", "Cyclin-dependent_kinase_inhibitor_1C")
          , ("CFIUS", "Committee_on_Foreign_Investment_in_the_United_States")
          , ("CIA_World_Factbook", "The_World_Factbook")
          , ("CIFAR-100", "CIFAR-10")
          , ("CJKV", "CJK_characters")
          , ("CMIP6", "Coupled_Model_Intercomparison_Project#CMIP_Phase_6")
          , ("CNAS", "Center_for_a_New_American_Security")
          , ("CNC", "Numerical_control")
          , ("CNNMoney.com", "CNN_Business")
          , ("COCO_(dataset)", "List_of_datasets_for_machine-learning_research#COCO")
          , ("COMT", "Catechol-O-methyltransferase")
          , ("CPT_invariant", "CPT_symmetry")
          , ("CPU", "Central_processing_unit")
          , ("CRISPR-Cas9", "Cas9")
          , ("CRISPR/Cas9", "Cas9")
          , ("CSET", "California_Subject_Examinations_for_Teachers")
          , ("CUHK-SZ", "Chinese_University_of_Hong_Kong,_Shenzhen")
          , ("CWEB", "Web_(programming_system)#CWEB")
          , ("C_programming_language", "C_(programming_language)")
          , ("Cache_misses", "Cache_(computing)#Operation")
          , ("Cagliostro_no_Shiro", "The_Castle_of_Cagliostro")
          , ("Cahokia_Mounds", "Cahokia")
          , ("Cake_cutting_problem", "Fair_cake-cutting")
          , ("CalPIRG", "Public_Interest_Research_Group")
          , ("Calcifications", "Calcification")
          , ("Calcium_homeostasis", "Calcium_metabolism")
          , ("Calcutta_University", "University_of_Calcutta")
          , ("Calibrated_probability_assessments", "Calibrated_probability_assessment")
          , ("Calibration_%28statistics%29", "Calibration_(statistics)")
          , ("California_Proposition_19_(2010)", "2010_California_Proposition_19")
          , ("Callus_%28cell_biology%29", "Callus_(cell_biology)")
          , ("Caloric_restriction", "Calorie_restriction")
          , ("Caltech", "California_Institute_of_Technology")
          , ("Caltrans", "California_Department_of_Transportation")
          , ("Camera_traps", "Camera_trap")
          , ("Campbell's_law", "Campbell%27s_law")
          , ("Camponotus", "Carpenter_ant")
          , ("Camponotus_gigas", "Dinomyrmex")
          , ("Canada.com", "Postmedia_Network")
          , ("Canada_2006_Census", "2006_Canadian_census")
          , ("Cannabis_%28drug%29", "Cannabis_(drug)")
          , ("Cannabis_use_disorders", "Cannabis_use_disorder")
          , ("Canneto_(Caronia)", "Canneto,_Caronia")
          , ("Cannibalism_(zoology)", "Cannibalism")
          , ("Cantor's_diagonal_argument", "Cantor%27s_diagonal_argument")
          , ("Canute", "Cnut")
          , ("Cape_ground_squirrels", "Cape_ground_squirrel")
          , ("Capital_gains", "Capital_gain")
          , ("Capital_taxes", "Wealth_tax")
          , ("Caption_Contest", "Caption_contest")
          , ("Capture-recapture", "Mark_and_recapture")
          , ("Caracal_caracal", "Caracal")
          , ("Carbon_fixation", "Biological_carbon_fixation")
          , ("Carbon_offset", "Carbon_offsets_and_credits")
          , ("Carborundum", "Silicon_carbide")
          , ("Carboxylase", "Carboxylation")
          , ("Carcinogenicity", "Carcinogen")
          , ("Cardiac_arrhythmias", "Arrhythmia")
          , ("Cardinal_Wolsey", "Thomas_Wolsey")
          , ("Cardinal_scale", "Cardinal_number")
          , ("Cardiomyocyte", "Cardiac_muscle#Microanatomy")
          , ("Cardiovascular_diseases", "Cardiovascular_disease")
          , ("Cardiovascular_system", "Circulatory_system#Structure")
          , ("Caribou", "Reindeer")
          , ("Carl_Von_Clausewitz", "Carl_von_Clausewitz")
          , ("Carmichael's_totient_function_conjecture", "Carmichael%27s_totient_function_conjecture")
          , ("Cartesian_skepticism", "Cartesian_doubt")
          , ("CashApp", "Cash_App")
          , ("Cashback_reward_program", "Credit_card#Cashback_reward_programs")
          , ("Cassette_Tape", "Cassette_tape")
          , ("Cassette_tapes", "Cassette_tape")
          , ("Cat's_Cradle", "Cat%27s_Cradle")
          , ("Cat_Fanciers'_Association", "Cat_Fanciers%27_Association")
          , ("Cat_allergy", "Allergy_to_cats")
          , ("Cat_cafe", "Cat_caf%C3%A9")
          , ("Cat_condo", "Cat_tree")
          , ("Catalan_sequence", "Catalan_number")
          , ("Catastrophic_forgetting", "Catastrophic_interference")
          , ("Catboost", "CatBoost")
          , ("Catenaries", "Catenary")
          , ("Catharine_Cox", "Catharine_Cox_Miles")
          , ("Cathartes_aura", "Turkey_vulture")
          , ("Cattell_Culture_Fair_III", "Cattell_Culture_Fair_Intelligence_Test")
          , ("Cattle_in_religion", "Cattle_in_religion_and_mythology")
          , ("Causal_loop_diagrams", "Causal_loop_diagram")
          , ("Cave_bears", "Cave_bear")
          , ("Cavorite", "The_First_Men_in_the_Moon#Plot_summary")
          , ("Cavum_septum_pellucidum", "Cave_of_septum_pellucidum")
          , ("Cell_(microprocessor)", "Cell_(processor)")
          , ("Cell_lysis", "Lysis")
          , ("Cellular_Automata", "Cellular_automaton")
          , ("Cellular_automata", "Cellular_automaton")
          , ("Cellular_membrane", "Cell_membrane")
          , ("Cellular_reprogramming", "Induced_pluripotent_stem_cell")
          , ("Centenarians", "Centenarian")
          , ("Center_of_our_galaxy", "Galactic_Center")
          , ("Cephalopods", "Cephalopod")
          , ("Cephalosporins", "Cephalosporin")
          , ("Cerebellar_palsy", "Cerebral_palsy")
          , ("Cervids", "Deer")
          , ("Cetirizine_hydrochloride", "Cetirizine")
          , ("Ch%C3%A4oS%3BHEAd", "Chaos;Head")
          , ("Ch%C5%AB%C5%8D-S%C5%8Dbu_Line", "Ch%C5%AB%C5%8D%E2%80%93S%C5%8Dbu_Line")
          , ("Chain-of-thought_prompting", "Prompt_engineering#Chain-of-thought")
          , ("Chaitin's_constant", "Chaitin%27s_constant")
          , ("Chalcidoidea", "Chalcid_wasp")
          , ("Change_of_Command", "Change_of_command")
          , ("Chapterhouse_Dune", "Chapterhouse:_Dune")
          , ("Char_no_Gyakushu", "Mobile_Suit_Gundam:_Char%27s_Counterattack")
          , ("Character.AI", "Character.ai")
          , ("Charles_Bonnet_syndrome", "Visual_release_hallucinations")
          , ("Charles_Murray_%28political_scientist%29", "Charles_Murray_(political_scientist)")
          , ("Charles_Schumer", "Chuck_Schumer")
          , ("Cheese_puff", "Cheese_puffs")
          , ("Cheetahs", "Cheetah")
          , ("Chekhov's_gun", "Chekhov%27s_gun")
          , ("Chemiosmotic_hypothesis", "Chemiosmosis")
          , ("Chemoinformatics", "Cheminformatics")
          , ("Chernobyl_Exclusion_Zone", "Chernobyl_exclusion_zone")
          , ("Cherry_picking_(fallacy)", "Cherry_picking")
          , ("Chess_AI", "Computer_chess")
          , ("Chess_endgames", "Chess_endgame")
          , ("Chi-square_test", "Chi-squared_test")
          , ("Chicken_pox", "Chickenpox")
          , ("Childhood's_End", "Childhood%27s_End")
          , ("Childhood_maltreatment", "Child_abuse")
          , ("Children's_Hospital_of_Philadelphia", "Children%27s_Hospital_of_Philadelphia")
          , ("Chimeric_antigen_receptor_T_cell", "CAR_T_cell")
          , ("Chimpanzees", "Chimpanzee")
          , ("Chimps", "Chimpanzee")
          , ("Chinese_industrialization", "Industrialization_of_China")
          , ("Chip_fabs", "Semiconductor_fabrication_plant")
          , ("Chiroptera", "Bat")
          , ("Chofu", "Ch%C5%8Dfu")
          , ("Chopin", "Fr%C3%A9d%C3%A9ric_Chopin")
          , ("Chris_Langton", "Christopher_Langton")
          , ("Christmas_Pudding", "Christmas_pudding")
          , ("Christmas_crackers", "Christmas_cracker")
          , ("Chromosomes", "Chromosome")
          , ("Chronic_back_pain", "Back_pain")
          , ("Chung-hee_Park", "Park_Chung_Hee")
          , ("Churchill", "Winston_Churchill")
          , ("Cigarette_smoking", "Tobacco_smoking")
          , ("Cilia", "Cilium")
          , ("Circadian_rhythms", "Circadian_rhythm")
          , ("Circuit_no_Okami", "Circuit_no_%C5%8Ckami")
          , ("Cisco_Systems", "Cisco")
          , ("Citations", "Citation")
          , ("Citizens'_Advisory_Council_on_National_Space_Policy", "Citizens%27_Advisory_Council_on_National_Space_Policy")
          , ("Civil_List", "Civil_list")
          , ("Civil_List_Act,_1837", "Civil_List_Act_1837")
          , ("Clan_(video_gaming)", "Clan_(video_games)")
          , ("Clarke's_three_laws", "Clarke%27s_three_laws")
          , ("Class_struggle", "Class_conflict")
          , ("Claude_Levi-Strauss", "Claude_L%C3%A9vi-Strauss")
          , ("Cleanrooms", "Cleanroom")
          , ("Clear_Channel_Communications", "IHeartMedia")
          , ("Cleft_palate", "Cleft_lip_and_cleft_palate#Cleft_palate")
          , ("Clifford_T._Morgan", "Clifford_Morgan")
          , ("Clinical_Global_Impression", "Clinical_global_impression")
          , ("Clinical_depression", "Major_depressive_disorder")
          , ("Clipping_service", "Media_monitoring_service")
          , ("Clonal_expansions", "Somatic_evolution_in_cancer#Clonal_expansions")
          , ("Closure_(computer_science)", "Closure_(computer_programming)")
          , ("CloudFlare", "Cloudflare")
          , ("Cloud_Nine_(tensegrity_sphere)", "Cloud_Nine_(sphere)")
          , ("Cloves", "Clove")
          , ("Cloze_deletion", "Cloze_test")
          , ("Cloze_deletions", "Cloze_test")
          , ("Coase%27s_theorem", "Coase_theorem")
          , ("Coase's_theorem", "Coase_theorem")
          , ("Cochlear_hair_cell", "Hair_cell")
          , ("Cochlear_implants", "Cochlear_implant")
          , ("Cochrane_Collaboration", "Cochrane_(organisation)")
          , ("Cochrane_Database_of_Systematic_Reviews", "Cochrane_Library")
          , ("Cock’s_comb", "Cock%E2%80%99s_comb")
          , ("Codependence", "Codependency")
          , ("Codice_Zanardelli", "Zanardelli_Code")
          , ("Cogito_ergo_sum", "Cogito,_ergo_sum")
          , ("Cognitive-behavioral_therapy", "Cognitive_behavioral_therapy")
          , ("Cognitive_Load", "Cognitive_load")
          , ("Cognitive_Psychology", "Cognitive_psychology#Neisser")
          , ("Cognitive_ability", "Cognitive_skill")
          , ("Cognitive_behavior_therapy", "Cognitive_behavioral_therapy")
          , ("Cognitive_biases", "Cognitive_bias")
          , ("Cognitive_effects_of_multilingualism", "Cognitive_effects_of_bilingualism")
          , ("Cognitive_skills", "Cognitive_skill")
          , ("Colchicine_poisoning", "Colchicine#Toxicity")
          , ("Cold_water_immersion", "Ice_bath")
          , ("Coleman_Report", "James_Samuel_Coleman#Coleman_Report")
          , ("Coleoid", "Coleoidea")
          , ("Colex_order", "Lexicographic_order#Colexicographic_order")
          , ("Collaborative_tagging", "Folksonomy")
          , ("Collectivism", "Social_organization#Collectivism_and_individualism")
          , ("College_degree", "Academic_degree")
          , ("Collier's", "Collier%27s")
          , ("Colon_cancer", "Colorectal_cancer")
          , ("Color-blindness", "Color_blindness")
          , ("Color_image", "Digital_image")
          , ("Color_vision_deficiency", "Color_blindness")
          , ("Colors", "Color")
          , ("Combat%21", "Combat!")
          , ("Combattler", "Ch%C5%8Ddenji_Robo_Combattler_V")
          , ("Combattler_V", "Ch%C5%8Ddenji_Robo_Combattler_V")
          , ("Comdex", "COMDEX")
          , ("Comet_Swift-Tuttle", "Comet_Swift%E2%80%93Tuttle")
          , ("Command_line", "Command-line_interface")
          , ("Commercial_driver's_license", "Commercial_driver%27s_license")
          , ("Commitment_devices", "Commitment_device")
          , ("Common-pool_resources", "Common-pool_resource")
          , ("Common_Sense_(pamphlet)", "Common_Sense")
          , ("Commonwealth_of_Virginia", "Virginia")
          , ("Communication_satellite", "Communications_satellite")
          , ("Communist_Manifesto", "The_Communist_Manifesto")
          , ("Commutativity", "Commutative_property")
          , ("Compact_OED", "Oxford_English_Dictionary#Compact_editions")
          , ("Comparative_fit_index", "Confirmatory_factor_analysis#Comparative_fit_index")
          , ("Comparison_of_notetaking_software", "Comparison_of_note-taking_software")
          , ("Comparison_of_time_tracking_software", "Comparison_of_time-tracking_software")
          , ("Competence_(biology)", "Natural_competence")
          , ("Compiler_backdoor", "Backdoor_(computing)#Compiler_backdoors")
          , ("Compiler_backdoors", "Backdoor_(computing)#Compiler_backdoors")
          , ("Complex_numbers", "Complex_number")
          , ("Complexity_classes", "Complexity_class")
          , ("Composition_of_Der_Ring_des_Nibelungen", "Der_Ring_des_Nibelungen:_composition_of_the_text")
          , ("Compositional_Pattern_Producing_Network", "Compositional_pattern-producing_network")
          , ("Compositionality", "Principle_of_compositionality")
          , ("Compton's_Encyclopedia", "Compton%27s_Encyclopedia")
          , ("Compulsive_hoarding", "Hoarding_disorder")
          , ("Computational_mechanism", "Model_of_computation")
          , ("Computer_Chess_%28film%29", "Computer_Chess_(film)")
          , ("Computer_Science_and_Artificial_Intelligence_Laboratory", "MIT_Computer_Science_and_Artificial_Intelligence_Laboratory")
          , ("Computer_system", "Computer")
          , ("Con_artist", "Scam")
          , ("Concept-mapping", "Concept_map")
          , ("Concept_mapping", "Concept_map")
          , ("Concessions_in_Tianjin", "Foreign_concessions_in_Tianjin")
          , ("Concrete_operational_stage", "Piaget%27s_theory_of_cognitive_development#Concrete_operational_stage")
          , ("Condorcet's_jury_theorem", "Condorcet%27s_jury_theorem")
          , ("Confidence_band", "Confidence_and_prediction_bands")
          , ("Confidence_intervals", "Confidence_interval")
          , ("Confidence_trick", "Scam")
          , ("Confirmatory_factor_analyses", "Confirmatory_factor_analysis")
          , ("Confocal_imaging", "Confocal_microscopy")
          , ("Confounders", "Confounding")
          , ("Congenital_cretinism", "Congenital_iodine_deficiency_syndrome")
          , ("Congenital_pain_insensitivity", "Congenital_insensitivity_to_pain")
          , ("Congestion_control", "Network_congestion#Congestion_control")
          , ("Congestive_heart_failure", "Heart_failure")
          , ("Connectomes", "Connectome")
          , ("Conservatoire_des_arts_et_m%C3%A9tiers", "Conservatoire_national_des_arts_et_m%C3%A9tiers")
          , ("Constant_factors", "Big_O_notation")
          , ("Constraint_%28mathematics%29", "Constraint_(mathematics)")
          , ("Constructal_law", "Adrian_Bejan#Constructal_law")
          , ("Constructed_script", "Constructed_writing_system")
          , ("Consumer_Revolution", "Consumer_revolution")
          , ("Consumer_behavior", "Consumer_behaviour")
          , ("Consumer_surplus", "Economic_surplus#Consumer_surplus")
          , ("Content_recommendation", "Contextual_advertising")
          , ("Continuations", "Continuation")
          , ("Contrast_%28vision%29", "Contrast_(vision)")
          , ("Contrastive_learning", "Self-supervised_learning")
          , ("Conversational_agent", "Dialogue_system")
          , ("Conversion_of_units_of_energy", "Conversion_of_units#Energy")
          , ("Conversion_rate", "Conversion_marketing#Conversion_rate")
          , ("Convex_programming", "Convex_optimization")
          , ("Convolution_(computer_science)", "Zipping_(computer_science)")
          , ("Convolutional_neural_networks", "Convolutional_neural_network")
          , ("Conway's_Game_of_Life", "Conway%27s_Game_of_Life")
          , ("Cooperative_Research_And_Development_Agreements", "Cooperative_research_and_development_agreement")
          , ("Cooperative_Wheat_Research_and_Production_Program", "Green_Revolution#Development_in_Mexico")
          , ("Cooperative_game_theories", "Cooperative_game_theory")
          , ("Coordination_problems", "Coordination_game")
          , ("Coppersmith-Winograd_algorithm", "Matrix_multiplication_algorithm#Sub-cubic_algorithms")
          , ("Coprolites", "Coprolite")
          , ("Copy-number_variants", "Copy_number_variation")
          , ("Copy-number_variation", "Copy_number_variation")
          , ("Copy_number_variants", "Copy_number_variation")
          , ("Coracopsis_vasa", "Greater_vasa_parrot")
          , ("Corel", "Alludo")
          , ("Cornucopian", "Cornucopianism")
          , ("Coronary_Artery_Disease", "Coronary_artery_disease")
          , ("Coronary_angioplasty", "Percutaneous_coronary_intervention")
          , ("Coronary_artery_bypass_grafting", "Coronary_artery_bypass_surgery")
          , ("Coronavirus_pandemic", "COVID-19_pandemic")
          , ("Correction_for_multiple_testing", "Multiple_comparisons_problem#Multiple_testing_correction")
          , ("Correlation_is_not_causation", "Correlation_does_not_imply_causation")
          , ("Cortex-M3", "ARM_Cortex-M#Cortex-M3")
          , ("Cortical_dysplasia", "Focal_cortical_dysplasia")
          , ("Cortical_surface_area", "Cerebral_cortex")
          , ("Corticosteroids", "Corticosteroid")
          , ("Corvids", "Corvidae")
          , ("Cosmere", "Brandon_Sanderson#Cosmere")
          , ("Cosmetic_surgery", "Plastic_surgery#Cosmetic_surgery_procedures")
          , ("Cosmic_radiation", "Cosmic_ray")
          , ("Cosplayer", "Cosplay")
          , ("Cost_of_Capital", "Cost_of_capital")
          , ("Cost_of_reproduction", "Cost_of_reproduction_hypothesis")
          , ("Costs_of_capital", "Cost_of_capital")
          , ("Cotard_delusion", "Cotard%27s_syndrome")
          , ("Counter-Strike%3B_Global_Offensive", "Counter-Strike;_Global_Offensive")
          , ("Counterparty_risk", "Credit_risk#Counterparty_risk")
          , ("Countersignalling", "Countersignaling")
          , ("Coup_d%27Etat:_A_Practical_Handbook", "Coup_d%27%C3%89tat:_A_Practical_Handbook")
          , ("Coup_d'Etat:_A_Practical_Handbook", "Coup_d%27%C3%89tat:_A_Practical_Handbook")
          , ("Coupled_oscillators", "Oscillation#Coupled_oscillations")
          , ("Courtesy_bias", "Response_bias#Courtesy_bias")
          , ("Covid-19_pandemic", "COVID-19_pandemic")
          , ("Cowbells", "Cowbell_(instrument)")
          , ("Cox_proportional_hazards_models", "Proportional_hazards_model#The_Cox_model")
          , ("Cox_regression", "Proportional_hazards_model")
          , ("Coyote_in_mythology", "Coyote_(mythology)")
          , ("Cr%C3%A9dit_Suisse", "Credit_Suisse")
          , ("Cracking_joints", "Joint_cracking")
          , ("Craig_Barrett_%28chief_executive%29", "Craig_Barrett_(chief_executive)")
          , ("Cranial_capacity", "Brain_size#Cranial_capacity")
          , ("Craniofacial", "Craniofacial_surgery")
          , ("Cray_3", "Cray-3")
          , ("Cray_Computers", "Cray")
          , ("Cray_Research", "Cray")
          , ("Crayon_Shin-chan:_Fierceness_That_Invites_Storm%21_The_Adult_Empire_Strikes_Back", "Crayon_Shin-chan:_Fierceness_That_Invites_Storm!_The_Adult_Empire_Strikes_Back")
          , ("Creatine_ethyl_ester", "Creatine")
          , ("Creatine_monohydrate", "Creatine")
          , ("Creativity_and_mental_illness", "Creativity_and_mental_health")
          , ("Credible_intervals", "Credible_interval")
          , ("Credit_assignment_problem", "Reinforcement_learning")
          , ("Creutzfeldt-Jacob_disease", "Creutzfeldt%E2%80%93Jakob_disease")
          , ("Creutzfeldt-Jakob_disease", "Creutzfeldt%E2%80%93Jakob_disease")
          , ("Crewel", "Crewel_embroidery")
          , ("Critical_Assessment_of_Techniques_for_Protein_Structure_Prediction", "CASP")
          , ("Critical_Assessment_of_protein_Structure_Prediction", "CASP")
          , ("Crocheting", "Crochet")
          , ("Crocodiles", "Crocodile")
          , ("Crohn's_disease", "Crohn%27s_disease")
          , ("Cromolyn", "Cromoglicic_acid")
          , ("Cromulent", "Lisa_the_Iconoclast#Embiggen_and_cromulent")
          , ("Cronbach's_alpha", "Cronbach%27s_alpha")
          , ("Cronbach's_alphas", "Cronbach%27s_alphas")
          , ("Cronbach’s_alpha", "Cronbach%27s_alpha")
          , ("Cross-lagged_panel_modeling", "Cross-lagged_panel_model")
          , ("Cross-validated", "Cross-validation_(statistics)")
          , ("Cross-validation_%28statistics%29", "Cross-validation_(statistics)")
          , ("Cross_entropy", "Cross-entropy")
          , ("Crosscompile", "Cross_compiler")
          , ("Crossover_design", "Crossover_study")
          , ("Crozet_Archipelago", "Crozet_Islands")
          , ("Cruise_Automation", "Cruise_(autonomous_vehicle)")
          , ("Crustaceans", "Crustacean")
          , ("Cryobiosis", "Cryptobiosis#Cryobiosis")
          , ("Cryptograms", "Cryptogram")
          , ("Cubic_spline", "Cubic_Hermite_spline")
          , ("Cul-de-sac", "Dead_end_street")
          , ("Cultural_genocide_of_Uyghurs", "Uyghur_genocide")
          , ("Cultural_values", "Value_(ethics_and_social_sciences)")
          , ("Curry-Howard_isomorphism", "Curry%E2%80%93Howard_correspondence")
          , ("Curse_of_expertise", "Curse_of_knowledge")
          , ("Curwen's_Bay_Barb", "Curwen%27s_Bay_Barb")
          , ("Cybertruck", "Tesla_Cybertruck")
          , ("Cyclic_tag_system", "Tag_system#Cyclic_tag_systems")
          , ("Cyclosporin", "Ciclosporin")
          , ("Cyclosporine", "Ciclosporin")
          , ("Cygnus_columbianus", "Tundra_swan")
          , ("Cyphernomicon", "Timothy_C._May")
          , ("Cypherpunks", "Cypherpunk")
          , ("Cypherpunks_mailing_list", "Cypherpunk")
          , ("Cyrillic_alphabet", "Cyrillic_script")
          , ("Cytochrome_oxidase", "Cytochrome_c_oxidase")
          , ("Cytokines", "Cytokine")
          , ("Cytotec", "Misoprostol")
          , ("Czeslaw_Milosz", "Czes%C5%82aw_Mi%C5%82osz")
          , ("D%C3%ADa_de_los_muertos", "Day_of_the_Dead")
          , ("D%C5%8Djin_music", "Doujin_music")
          , ("D%C5%8Djinshi_convention", "Doujinshi_convention")
          , ("D2_receptor", "Dopamine_receptor_D2")
          , ("DAICON", "Nihon_SF_Taikai")
          , ("DAICON_3", "Daicon_III_and_IV_Opening_Animations#DAICON_III_Opening_Animation")
          , ("DAICON_4", "Daicon_III_and_IV_Opening_Animations#DAICON_IV_Opening_Animation")
          , ("DAICON_IV", "Daicon_III_and_IV_Opening_Animations")
          , ("DAICON_IV_Opening_Animation", "Daicon_III_and_IV_Opening_Animations")
          , ("DALY", "Disability-adjusted_life_year")
          , ("DALYs", "Disability-adjusted_life_year")
          , ("DAT1", "Dopamine_transporter")
          , ("DC++", "DC%2B%2B")
          , ("DDoS", "Denial-of-service_attack")
          , ("DDoS_attack", "Denial-of-service_attack")
          , ("DENDRAL", "Dendral")
          , ("DGX-1", "Nvidia_DGX#DGX-1")
          , ("DMAE", "Dimethylethanolamine")
          , ("DMCA", "Digital_Millennium_Copyright_Act")
          , ("DMSO", "Dimethyl_sulfoxide")
          , ("DNA_%28cytosine-5%29-methyltransferase_3A", "DNA_(cytosine-5)-methyltransferase_3A")
          , ("DNA_damage", "DNA_repair#DNA_damage")
          , ("DNA_metabarcoding", "Metabarcoding")
          , ("DPP-IV", "Dipeptidyl_peptidase-4")
          , ("DRD2", "Dopamine_receptor_D2")
          , ("DSM-III", "Diagnostic_and_Statistical_Manual_of_Mental_Disorders#DSM-III_(1980)")
          , ("DSM-III-R", "Diagnostic_and_Statistical_Manual_of_Mental_Disorders#DSM-III-R_.281987.29")
          , ("DSM-IV", "Diagnostic_and_Statistical_Manual_of_Mental_Disorders#DSM-IV_.281994.29")
          , ("DU_PONT", "DuPont")
          , ("Dajuan_Wagner_Jr.", "D._J._Wagner")
          , ("Dallas_Morning_News", "The_Dallas_Morning_News")
          , ("Daly_City", "Daly_City,_California")
          , ("Damerau_edit_distance", "Damerau%E2%80%93Levenshtein_distance")
          , ("Damiana", "Turnera_diffusa")
          , ("Dan_Dennett", "Daniel_Dennett")
          , ("Dandy-Walker_syndrome", "Dandy%E2%80%93Walker_malformation")
          , ("Danielle_Steele", "Danielle_Steel")
          , ("Danish_Demographics", "Demographics_of_Denmark")
          , ("Dark_Matter", "Dark_matter")
          , ("Dark_Web", "Dark_web")
          , ("Dark_net_market_Hydra", "Hydra_Market")
          , ("Dark_patterns", "Dark_pattern")
          , ("Dark_side_(Star_Wars)", "The_Force#Depiction")
          , ("Darknet_Markets", "Darknet_market")
          , ("Darknet_marketplace", "Darknet_market")
          , ("Darn_Tough_socks", "Cabot_Hosiery_Mills")
          , ("Darwin_%28operating_system%29", "Darwin_(operating_system)")
          , ("Das_Boot_(film)", "Das_Boot")
          , ("Data_Monitoring_Committee", "Data_monitoring_committee")
          , ("Data_URIs", "Data_URI_scheme")
          , ("Data_havens", "Data_haven")
          , ("Data_leakage", "Leakage_(machine_learning)")
          , ("Data_poisoning", "Adversarial_machine_learning#Data_poisoning")
          , ("Data_poisoning_attack", "Adversarial_machine_learning#Data_poisoning")
          , ("Data_poisoning_attacks", "Adversarial_machine_learning#Data_poisoning")
          , ("Data_visualization", "Data_and_information_visualization")
          , ("David_Silver_(programmer)", "David_Silver_(computer_scientist)")
          , ("DeVry_Inc.", "Adtalem_Global_Education")
          , ("De_Broglie-Bohm_theory", "De_Broglie%E2%80%93Bohm_theory")
          , ("De_Finetti's_theorem", "De_Finetti%27s_theorem")
          , ("De_institutione_musica", "Boethius#De_institutione_musica")
          , ("De_novo_mutations", "De_novo_mutation")
          , ("Deacon_(Latter_Day_Saints)", "Aaronic_priesthood_(Latter_Day_Saints)#Deacon")
          , ("Dead-drop", "Dead_drop")
          , ("Dead-weight_loss", "Deadweight_loss")
          , ("Dead_Birds_%281963_film%29", "Dead_Birds_(1963_film)")
          , ("Dead_end_(street)", "Dead_end_street")
          , ("Deadline_%28science_fiction_story%29", "Deadline_(science_fiction_story)")
          , ("Deadline_(video_game)", "Deadline_(1982_video_game)")
          , ("DealBook", "Andrew_Ross_Sorkin#DealBook")
          , ("Deaminase", "Deamination")
          , ("Death_Fugue", "Todesfuge")
          , ("Death_squads", "Death_squad")
          , ("Debating_society", "Debate")
          , ("Decapitation_attack", "Decapitation_(military_strategy)")
          , ("Decision_trees", "Decision_tree")
          , ("Declaration_of_Independence", "Declaration_of_independence")
          , ("Declarative_memory", "Explicit_memory")
          , ("Decline_and_Fall_of_the_Roman_Empire", "The_History_of_the_Decline_and_Fall_of_the_Roman_Empire")
          , ("Decompilation", "Decompiler")
          , ("DeepL", "DeepL_Translator")
          , ("DeepMind", "Google_DeepMind")
          , ("Deep_neural_network", "Deep_learning#Deep_neural_networks")
          , ("Deep_structure", "Deep_structure_and_surface_structure")
          , ("Deer-vehicle_collisions", "Deer%E2%80%93vehicle_collisions")
          , ("Degenerate_Art_Exhibition", "Degenerate_Art_exhibition")
          , ("Delay_of_gratification", "Delayed_gratification")
          , ("Deliberate_practice", "Practice_(learning_method)#Deliberate_practice")
          , ("Delicious.com", "Delicious_(website)")
          , ("Delphinidae", "Oceanic_dolphin")
          , ("Delta_robots", "Delta_robot")
          , ("Demand_effect", "Demand_characteristics")
          , ("Demo_(computer_programming)", "Demoscene")
          , ("Demo_scene", "Demoscene")
          , ("Democracy_Index", "The_Economist_Democracy_Index")
          , ("Denial_of_Service", "Denial-of-service_attack")
          , ("Denoising", "Noise_reduction")
          , ("Densetsu_Kyojin_Ideon", "Space_Runaway_Ideon")
          , ("Dental_caries", "Tooth_decay")
          , ("Dentifriced", "Dentifrice")
          , ("Denture", "Dentures")
          , ("Deoxyribonucleic_acid", "DNA")
          , ("Depressive_disorder", "Mood_disorder#Depressive_disorders")
          , ("Depth_estimation", "Depth_perception")
          , ("Derrida", "Jacques_Derrida")
          , ("Desert_Eagles", "Desert_Eagle")
          , ("Desert_kites", "Desert_kite")
          , ("Design_patterns", "Design_pattern")
          , ("Desk_Top_Publishing", "Desktop_publishing")
          , ("Dessert_wines", "Dessert_wine")
          , ("Determination_of_primality", "Primality_test")
          , ("Deuteranomalia", "Color_blindness#Deuteranomaly")
          , ("Devil%27s_proof", "Probatio_diabolica")
          , ("Devil's_proof", "Probatio_diabolica")
          , ("Dewey_decimal_system", "Dewey_Decimal_Classification")
          , ("Dexamphetamine", "Dextroamphetamine")
          , ("DiGiCharat", "Di_Gi_Charat")
          , ("Diabetes_mellitus", "Diabetes")
          , ("Diabetes_mellitus_type_2", "Type_2_diabetes")
          , ("Diagnostic_and_Statistical_Manual_of_Mental_Disorders_(DSM-III)", "Diagnostic_and_Statistical_Manual_of_Mental_Disorders#DSM-III_(1980)")
          , ("Dialectical_behavioral_therapy", "Dialectical_behavior_therapy")
          , ("Diaspora_%28novel%29", "Diaspora_(novel)")
          , ("Diastolic_blood_pressure", "Blood_pressure")
          , ("Diatoms", "Diatom")
          , ("Dick_Bong", "Richard_Bong")
          , ("Dickens", "Charles_Dickens")
          , ("Dictionnaire_de_l'Acad%C3%A9mie_fran%C3%A7aise", "Dictionnaire_de_l%27Acad%C3%A9mie_fran%C3%A7aise")
          , ("Diesel_engines", "Diesel_engine")
          , ("Diesel_submarines", "Submarine#Diesel-electric_transmission")
          , ("Diet_Coke_and_Mentos_eruption", "Soda_geyser")
          , ("Dietitians", "Dietitian")
          , ("Diffeomorphic", "Diffeomorphism")
          , ("Difference-in-differences", "Difference_in_differences")
          , ("Differential_Equations", "Differential_equation")
          , ("Diffusion_model_(machine_learning)", "Diffusion_model")
          , ("Diffusion_models", "Diffusion_model")
          , ("Diffusion_tensor_imaging", "Diffusion_MRI#Diffusion_tensor_imaging")
          , ("Diffusion_weighted_imaging", "Diffusion_MRI")
          , ("Digiko", "Dejiko")
          , ("Digit-span", "Memory_span")
          , ("Digit_span", "Memory_span")
          , ("Digital_Humanities", "Digital_humanities")
          , ("Digital_Signal_Processor", "Digital_signal_processor")
          , ("Digital_age", "Information_Age")
          , ("Digital_breast_tomosynthesis", "Tomosynthesis#Breast")
          , ("Digital_cash", "Digital_currency")
          , ("Digital_comics", "Digital_comic")
          , ("Digital_currency_exchanger", "Cryptocurrency_exchange")
          , ("Dimethyltryptamine", "N,N-Dimethyltryptamine")
          , ("Diminishing_marginal_returns", "Diminishing_returns")
          , ("Dimli", "Zaza_language")
          , ("Dinoflagellates", "Dinoflagellate")
          , ("Dinosaur_intelligence", "Dinosaur")
          , ("Diogenes_La%C3%ABrtius", "Diogenes_Laertius")
          , ("Diopter", "Dioptre")
          , ("Dioxin_Affair", "Dioxin_affair")
          , ("Diphenhydramine_hydrochloride", "Diphenhydramine")
          , ("Dipole%E2%80%93dipole_interaction", "Intermolecular_force#Dipole-dipole_interactions")
          , ("Diptera", "Fly")
          , ("Dirac", "Paul_Dirac")
          , ("Direct-to-consumer_genetic_testing", "Genetic_testing#Direct-to-consumer_genetic_testing")
          , ("Direct_Instruction", "Direct_instruction")
          , ("Direct_mail", "Advertising_mail")
          , ("Directed_acyclic_graphs", "Directed_acyclic_graph")
          , ("Directed_energy_weapons", "Directed-energy_weapon")
          , ("Directory_tree", "Directory_(computing)")
          , ("Dirichlet's_principle", "Dirichlet%27s_principle")
          , ("Discord_(software)", "Discord")
          , ("Discriminant_analysis", "Linear_discriminant_analysis")
          , ("Discriminant_function", "Linear_discriminant_analysis")
          , ("Discriminant_function_analysis", "Linear_discriminant_analysis")
          , ("Display_advertising", "Digital_display_advertising")
          , ("Disposable_Soma_Theory", "Disposable_soma_theory_of_aging")
          , ("Disposable_soma", "Disposable_soma_theory_of_aging")
          , ("Disruptive_behavior_disorder", "DSM-IV_codes#Attention-deficit_and_disruptive_behavior_disorders")
          , ("Dissociation_%28psychology%29", "Dissociation_(psychology)")
          , ("Dissolution_of_the_Monasteries", "Dissolution_of_the_monasteries")
          , ("Distributed_Revision_Control_Systems", "Distributed_version_control")
          , ("Divehi_language", "Maldivian_language")
          , ("Dizygotic", "Twin#Dizygotic")
          , ("Dizygotic_twins", "Twin#Dizygotic")
          , ("DoTA_2", "Dota_2")
          , ("Doc2Vec", "Word2vec#Extensions")
          , ("Docstrings", "Docstring")
          , ("Doctor_Abraham_Van_Helsing", "Abraham_Van_Helsing")
          , ("Doctor_Strangelove", "Dr._Strangelove")
          , ("Dogri", "Dogri_language")
          , ("Dollo%27s_Law", "Dollo%27s_law_of_irreversibility")
          , ("Dollo's_Law", "Dollo%27s_law_of_irreversibility")
          , ("Domain-specific_languages", "Domain-specific_language")
          , ("Domain_randomization", "Domain_adaptation")
          , ("Domestic_cat", "Cat")
          , ("Domestic_cats", "Cat")
          , ("Domestic_dogs", "Dog")
          , ("Domesticated_red_fox", "Domesticated_silver_fox")
          , ("Domestication_of_animals", "Domestication_of_vertebrates")
          , ("Dominance_%28genetics%29", "Dominance_(genetics)")
          , ("Dominance_(ethology)", "Dominance_hierarchy")
          , ("Don%27t_Repeat_Yourself", "Don%27t_repeat_yourself")
          , ("Don't_Repeat_Yourself", "Don%27t_repeat_yourself")
          , ("Don't_repeat_yourself", "Don%27t_repeat_yourself")
          , ("Donald_E._Knuth", "Donald_Knuth")
          , ("Donald_J._Trump", "Donald_Trump")
          , ("Trump", "Donald_Trump")
          , ("Donor_insemination", "Insemination")
          , ("Doogie_mice", "Long-term_potentiation#Spatial_memory")
          , ("Doomsday_Argument", "Doomsday_argument")
          , ("Dorothy_Denning", "Dorothy_E._Denning")
          , ("Dorsal_root_ganglia", "Dorsal_root_ganglion")
          , ("Dose-response_curve", "Dose%E2%80%93response_relationship")
          , ("Dose-response_relationship", "Dose%E2%80%93response_relationship")
          , ("Dot-product", "Dot_product")
          , ("Dotcom_bubble", "Dot-com_bubble")
          , ("Double-entry_bookkeeping_system", "Double-entry_bookkeeping")
          , ("Double-muscling", "Myostatin")
          , ("Doubly_Labelled_Water", "Doubly_labeled_water")
          , ("Doug_Engelbart", "Douglas_Engelbart")
          , ("Douglas_Fir", "Douglas_fir")
          , ("Douyin", "TikTok#Douyin")
          , ("Down's_syndrome", "Down_syndrome")
          , ("Downtown_Chicago", "Chicago_Loop")
          , ("Doxbin", "Doxbin_(darknet)")
          , ("Dr._Seuss'_How_the_Grinch_Stole_Christmas!_%28TV_special%29", "How_the_Grinch_Stole_Christmas!_(TV_special)")
          , ("Dragon's_teeth_(mythology)", "Dragon%27s_teeth_(mythology)")
          , ("Dragon_Ball_Evolution", "Dragonball_Evolution")
          , ("Dragon_Naturally_Speaking", "Dragon_NaturallySpeaking")
          , ("Dragon_Quest_4", "Dragon_Quest_IV")
          , ("Dragon_Quest_Fantasia_Video", "List_of_Dragon_Quest_media#Television_and_film")
          , ("Drank_(soda)", "Drank_(soft_drink)")
          , ("Draughts", "Checkers")
          , ("Dream_(comics)", "Dream_(character)")
          , ("Dried_nasal_mucus", "Mucus")
          , ("Driver_mutations", "Somatic_evolution_in_cancer#Driver_mutation")
          , ("Droit_d%27auteur", "Authors%27_rights")
          , ("Droit_d'auteur", "Authors%27_rights")
          , ("Dromedaries", "Dromedary")
          , ("Drop_cap", "Initial#Drop_cap")
          , ("Drop_caps", "Initial#Types_of_initial")
          , ("Dropout", "Dilution_(neural_networks)")
          , ("Dropout_%28neural_networks%29", "Dilution_(neural_networks)")
          , ("Dropout_(neural_networks)", "Dilution_(neural_networks)")
          , ("Drug_target", "Biological_target")
          , ("Dual_Axis_Radiographic_Hydrodynamic_Test_Facility", "Dual-Axis_Radiographic_Hydrodynamic_Test_Facility")
          , ("Dual_n-back", "N-back#Dual_n-back")
          , ("Dual_use", "Dual-use_technology")
          , ("Dubbed", "Dubbing")
          , ("Dueling_scars", "Dueling_scar")
          , ("Duhem-Quine", "Duhem%E2%80%93Quine_thesis")
          , ("Duhem-Quine_thesis", "Duhem%E2%80%93Quine_thesis")
          , ("Duke_of_Milan", "List_of_dukes_of_Milan")
          , ("Dukkha", "Du%E1%B8%A5kha")
          , ("Dunbar's_number", "Dunbar%27s_number")
          , ("Dunbar_number", "Dunbar%27s_number")
          , ("Dune_universe", "Dune_(franchise)")
          , ("Dungeon_master", "Dungeon_Master")
          , ("Dungeons_&_Dragons", "Dungeons_%26_Dragons")
          , ("Dungeons_&amp;_Dragons", "Dungeons_%26_Dragons")
          , ("Dungeons_&amp;amp%3Bamp%3B_Dragons", "Dungeons_%26_Dragons")
          , ("Dungeons_and_Dragons", "Dungeons_%26_Dragons")
          , ("Dunning-Kruger_Effect", "Dunning%E2%80%93Kruger_effect")
          , ("Duodenal_ulcer_disease", "Peptic_ulcer_disease")
          , ("Dutch_angles", "Dutch_angle")
          , ("Duty-free_shops", "Duty-free_shop")
          , ("Duverger's_law", "Duverger%27s_law")
          , ("Dynamic_systems_theory", "Dynamical_systems_theory")
          , ("Dyson_spheres", "Dyson_sphere")
          , ("Dyspnea", "Shortness_of_breath")
          , ("Dzhokhar_and_Tamerlan_Tsarnaev", "Boston_Marathon_bombing#Identification_and_search_for_suspects")
          , ("E-Gold", "E-gold")
          , ("E-cigarettes", "Electronic_cigarette")
          , ("E-ink", "Electronic_paper")
          , ("E.E._%22Doc%22_Smith", "E._E._Smith")
          , ("E.O._Wilson", "E._O._Wilson")
          , ("E.T._Jaynes", "Edwin_Thompson_Jaynes")
          , ("E._G._Boring", "Edwin_Boring")
          , ("E.e._cummings", "E._E._Cummings")
          , ("ECC_RAM", "ECC_memory")
          , ("ECoG", "Electrocorticography")
          , ("EEG", "Electroencephalography")
          , ("EMACS", "Emacs")
          , ("ERC-20", "Ethereum#ERC20")
          , ("ERIC", "Education_Resources_Information_Center")
          , ("ETAOIN_SHRDLU", "Etaoin_shrdlu")
          , ("EVE_Online", "Eve_Online")
          , ("Ear_mites", "Ear_mite")
          , ("Early_Holocene", "Greenlandian")
          , ("East_German", "East_Germany")
          , ("Eating_one%27s_own_dog_food", "Eating_your_own_dog_food")
          , ("Eating_one's_own_dog_food", "Eating_your_own_dog_food")
          , ("Echolocate", "Human_echolocation")
          , ("Economic_analysis_of_law", "Law_and_economics")
          , ("Economic_experiment", "Experimental_economics")
          , ("Eczema", "Dermatitis")
          , ("Ed_Fredkin", "Edward_Fredkin")
          , ("Edge_Foundation,_Inc", "Edge_Foundation,_Inc.")
          , ("Edgewood_Arsenal", "Aberdeen_Proving_Ground#Edgewood_Arsenal")
          , ("Edison", "Thomas_Edison")
          , ("Edmonton,_Canada", "Edmonton")
          , ("Edmonton_Symptom_Assessment_System", "Palliative_care#ESAS")
          , ("Edna_O'Brien", "Edna_O%27Brien")
          , ("Edsger_Dijkstra", "Edsger_W._Dijkstra")
          , ("Edward_E._David_Junior", "Edward_E._David_Jr.")
          , ("Edward_L._Thorndike", "Edward_Thorndike")
          , ("Effect_of_Reality", "Effect_of_reality")
          , ("Effect_sizes", "Effect_size")
          , ("Effective_Altruism", "Effective_altruism")
          , ("Effective_Altruists", "Effective_altruism")
          , ("Effective_neutron_multiplication_factor", "Nuclear_chain_reaction#Effective_neutron_multiplication_factor")
          , ("Efficient_market", "Efficient-market_hypothesis")
          , ("Efficient_markets", "Efficient-market_hypothesis")
          , ("Effortful_Control", "Temperament#Effortful_control")
          , ("Egger%27s_test", "Matthias_Egger")
          , ("Egyptian_New_Kingdom", "New_Kingdom_of_Egypt")
          , ("Ehrlich's_reagent", "Ehrlich%27s_reagent")
          , ("Ehrlich_test", "Ehrlich%27s_reagent")
          , ("Eiffel_programming_language", "Eiffel_(programming_language)")
          , ("Eigenvalue_decomposition", "Eigendecomposition_of_a_matrix")
          , ("Eight_track_digital_tape", "ADAT")
          , ("Eightman", "8_Man")
          , ("Einstein%E2%80%93Szil%C3%A1rd_letter", "Einstein%E2%80%93Szilard_letter")
          , ("Electoral_College_(United_States)", "United_States_Electoral_College")
          , ("Electric_typewriter", "Typewriter#Electronic_typewriters")
          , ("Electron_beam_lithography", "Electron-beam_lithography")
          , ("Electron_micrographs", "Micrograph")
          , ("Electron_microscopy", "Electron_microscope")
          , ("Electronic_Freedom_Foundation", "Electronic_Frontier_Foundation")
          , ("Elephants", "Elephant")
          , ("Elephas_maximus", "Asian_elephant")
          , ("Elevated_blood_pressure", "Hypertension")
          , ("Elinks", "ELinks")
          , ("Elizabeth_Ann_(ferret)", "Elizabeth_Ann")
          , ("Elvish_languages_(Tolkien)", "Elvish_languages_of_Middle-earth")
          , ("Embryonic_stem-cell", "Embryonic_stem_cell")
          , ("Embryonic_stem_cells", "Embryonic_stem_cell")
          , ("Emergency_Data_Request", "Emergency_data_request")
          , ("Emery_Air_Freight", "Emery_Worldwide_Airlines")
          , ("Emily_Simpson", "The_Real_Housewives_of_Orange_County")
          , ("Emoticons", "Emoticon")
          , ("Emotional_stability", "Equanimity")
          , ("Empathizing-systemizing_theory", "Empathising%E2%80%93systemising_theory")
          , ("Emperor_Palpatine", "Palpatine")
          , ("Empirical_Bayes", "Empirical_Bayes_method")
          , ("Encapsulation_%28computer_programming%29", "Encapsulation_(computer_programming)")
          , ("Encoding_%28memory%29", "Encoding_(memory)")
          , ("Encyclopedia_Britannica", "Encyclop%C3%A6dia_Britannica")
          , ("End_of_Evangelion", "The_End_of_Evangelion")
          , ("Ender's_Game", "Ender%27s_Game")
          , ("Ender's_Shadow", "Ender%27s_Shadow")
          , ("Endocannabinoid", "Cannabinoid#Endocannabinoids")
          , ("Endocrine_disruption", "Endocrine_disruptor")
          , ("Endogeneity_%28econometrics%29", "Endogeneity_(econometrics)")
          , ("Endoplasmic_reticulum_stress", "Endoplasmic_reticulum_stress_in_beta_cells")
          , ("Endosymbiotic_theory", "Symbiogenesis")
          , ("Endothelial", "Endothelium")
          , ("Energy_based_model", "Energy-based_model")
          , ("Energy_metabolism", "Bioenergetics")
          , ("Energy_technology", "Energy_development")
          , ("English_Parliamentary_enclosure_acts", "Inclosure_Acts")
          , ("English_bulldog", "Bulldog")
          , ("Eniwetok", "Enewetak_Atoll")
          , ("Enjo_kosai", "Enjo_k%C5%8Dsai")
          , ("Entailment", "Logical_consequence")
          , ("Entropy_%28information_theory%29", "Entropy_(information_theory)")
          , ("Environment_of_evolutionary_adaptedness", "Evolutionary_psychology#Environment_of_evolutionary_adaptedness")
          , ("Environmental_tobacco_smoke", "Passive_smoking")
          , ("Epigenetic", "Epigenetics")
          , ("Epileptic_seizure", "Seizure")
          , ("Epimenides_sentence", "Liar_paradox")
          , ("Epinephrine", "Adrenaline")
          , ("Epistatic", "Epistasis")
          , ("Epsom_salts", "Epsomite")
          , ("Epstein-Barr", "Epstein%E2%80%93Barr_virus")
          , ("Epstein-Barr_virus", "Epstein%E2%80%93Barr_virus")
          , ("Equal_environments_assumption", "Twin_study#Assumptions")
          , ("Ergot_fungus", "Ergot")
          , ("Eric_Robert_Rudolph", "Eric_Rudolph")
          , ("Erlk%C3%B6nig_(Goethe)", "Erlk%C3%B6nig")
          , ("Ern_Malley", "Ern_Malley_hoax")
          , ("Ero_games", "Eroge")
          , ("Errol_Musk", "Musk_family")
          , ("Error_bars", "Error_bar")
          , ("Errors-in-variables_model", "Errors-in-variables_models")
          , ("Errors_and_residuals_in_statistics", "Errors_and_residuals")
          , ("Erwin_Schrodinger", "Erwin_Schr%C3%B6dinger")
          , ("Escape_the_room", "Escape_room")
          , ("Esophageal_sphincter", "Esophagus#Sphincters")
          , ("Essay_On_Criticism", "An_Essay_on_Criticism")
          , ("Essene", "Essenes")
          , ("Essential_nutrient", "Nutrient#Essential_nutrients")
          , ("Essential_oils", "Essential_oil")
          , ("Esterification", "Ester#Preparation")
          , ("Ethinyl_estradiol", "Ethinylestradiol")
          , ("Ethnic_issues_in_China", "Racism_in_China")
          , ("Ethylene_dibromide", "1,2-Dibromoethane")
          , ("Eucles", "Eucles_of_Marathon")
          , ("Euclid's_Elements", "Euclid%27s_Elements")
          , ("Eugene_Cernan", "Gene_Cernan")
          , ("Eukaryotes", "Eukaryote")
          , ("Euler's_sum_of_powers_conjecture", "Euler%27s_sum_of_powers_conjecture")
          , ("Euler_characteristics", "Euler_characteristic")
          , ("Euler_equations", "List_of_things_named_after_Leonhard_Euler#Equations")
          , ("Euripedes", "Euripides")
          , ("Eusebius_of_Caesarea", "Eusebius")
          , ("Evangelion:_1.0_You_Are_%28Not%29_Alone", "Evangelion:_1.0_You_Are_(Not)_Alone")
          , ("Evangelion:_2.0_You_Can_%28Not%29_Advance", "Evangelion:_2.0_You_Can_(Not)_Advance")
          , ("Evangelion:_3.0%2B1.0", "Evangelion:_3.0%2B1.0_Thrice_Upon_a_Time")
          , ("Evangelion_1.0", "Evangelion:_1.0_You_Are_(Not)_Alone")
          , ("Evangelion_2.0", "Evangelion:_2.0_You_Can_(Not)_Advance")
          , ("Evariste_Galois", "%C3%89variste_Galois")
          , ("Everything_Bad_Is_Good_for_You:_How_Today%27s_Popular_Culture_Is_Actually_Making_Us_Smarter", "Everything_Bad_Is_Good_for_You")
          , ("Everything_Bad_Is_Good_for_You:_How_Today's_Popular_Culture_Is_Actually_Making_Us_Smarter", "Everything_Bad_Is_Good_for_You")
          , ("Evita_Per%C3%B3n", "Eva_Per%C3%B3n")
          , ("Evolution_%28marketplace%29", "Evolution_(marketplace)")
          , ("Evolutionary_algorithms", "Evolutionary_algorithm")
          , ("Excel", "Microsoft_Excel")
          , ("Exchangeability", "Exchangeable_random_variables")
          , ("Excite_%28web_portal%29", "Excite_(web_portal)")
          , ("Executable_space_protection", "Executable-space_protection")
          , ("Executive_function", "Executive_functions")
          , ("Executive_pay", "Executive_compensation")
          , ("Existential_risk", "Global_catastrophic_risk#Defining_existential_risks")
          , ("Existential_risks", "Global_catastrophic_risk")
          , ("Exogenous", "Exogeny")
          , ("Exokernels", "Exokernel")
          , ("Exomes", "Exome")
          , ("Exomind", "Extended_mind_thesis#%22The_Extended_Mind%22")
          , ("Exotic_dancer", "Stripper")
          , ("Expectation_Maximization", "Expectation%E2%80%93maximization_algorithm")
          , ("Expected_Value_of_Perfect_Information", "Expected_value_of_perfect_information")
          , ("Expected_Value_of_Sample_Information", "Expected_value_of_sample_information")
          , ("Expected_utility", "Expected_utility_hypothesis")
          , ("Expelled_From_Paradise", "Expelled_from_Paradise")
          , ("Experience_curve", "Experience_curve_effects")
          , ("Experience_curves", "Experience_curve_effects")
          , ("Experience_sampling", "Experience_sampling_method")
          , ("Experiment_design", "Design_of_experiments")
          , ("Experimental_design", "Design_of_experiments")
          , ("Experimental_neurosis", "Learned_helplessness")
          , ("Expert_Systems", "Expert_system")
          , ("Expert_systems", "Expert_system")
          , ("Exploratory_Factor_Analysis", "Exploratory_factor_analysis")
          , ("Expo_'70", "Expo_%2770")
          , ("Export_of_cryptography_in_the_United_States", "Export_of_cryptography_from_the_United_States")
          , ("Exposure_bias", "Exposure_compensation")
          , ("Expressiveness", "Expressive_suppression")
          , ("Extended_Mind", "Extended_mind_thesis#%22The_Extended_Mind%22")
          , ("External_evidence", "Textual_criticism#External_evidence")
          , ("Externalities", "Externality")
          , ("Externalizing_disorders", "Externalizing_disorder")
          , ("Exteroception", "Sense")
          , ("Extraversion", "Extraversion_and_introversion")
          , ("Exxon_Mobil", "ExxonMobil")
          , ("Eye-tracking", "Eye_tracking")
          , ("Eyetracking", "Eye_tracking")
          , ("F-22", "Lockheed_Martin_F-22_Raptor")
          , ("FAQs", "FAQ")
          , ("FGFR2", "Fibroblast_growth_factor_receptor_2")
          , ("FLOSS", "Free_and_open-source_software")
          , ("FMRI", "Functional_magnetic_resonance_imaging")
          , ("FOGBANK", "Fogbank")
          , ("FOOBAR", "Foobar")
          , ("FORTRAN", "Fortran")
          , ("FPGA", "Field-programmable_gate_array")
          , ("FURIN", "Furin")
          , ("Fable_3", "Fable_III")
          , ("Fabless_semiconductor_company", "Fabless_manufacturing")
          , ("FaceTimed", "FaceTime")
          , ("Face_recognition", "Facial_recognition_system")
          , ("Facebook_Chat", "Messenger_(software)")
          , ("Facial_(sex_act)", "Facial_(sexual_act)")
          , ("Factor_analytic", "Factor_analysis")
          , ("Factorial_invariance", "Measurement_invariance")
          , ("Fail_deadly", "Fail-deadly")
          , ("Falls_and_fractures_in_older_people", "Falls_in_older_adults")
          , ("False_consensus", "False_consensus_effect")
          , ("False_detection_rate", "False_positives_and_false_negatives")
          , ("False_flag_attacks", "False_flag")
          , ("False_memories", "False_memory")
          , ("False_memory_syndromes", "False_memory_syndrome")
          , ("Famicom", "Nintendo_Entertainment_System")
          , ("FanFiction.net", "FanFiction.Net")
          , ("Fansubs", "Fansub")
          , ("Fantasy_prone_personality", "Fantasy-prone_personality")
          , ("Fantasy_sports", "Fantasy_sport")
          , ("Fanzines", "Fanzine")
          , ("Farewell_to_Alms", "A_Farewell_to_Alms")
          , ("Faroese_independence_referendum,_1946", "1946_Faroese_independence_referendum")
          , ("Fast_Fourier_Transform", "Fast_Fourier_transform")
          , ("Fast_motion", "Time-lapse_photography")
          , ("Faster-than-light_neutrino_anomaly", "2011_OPERA_faster-than-light_neutrino_anomaly")
          , ("Fasting_plasma_glucose", "Glucose_test")
          , ("Fat-soluble_vitamin", "Vitamin#In_humans")
          , ("Fat_men's_club", "Fat_men%27s_club")
          , ("Fate/Stay_Night", "Fate/stay_night")
          , ("Fate/stay_night:_Unlimited_Blade_Works_(2010_film)", "Fate/stay_night:_Unlimited_Blade_Works_(film)")
          , ("Father_Goose_%28film%29", "Father_Goose_(film)")
          , ("Fatty_acid_amide_hydrolase", "Fatty-acid_amide_hydrolase_1")
          , ("Fatty_acids", "Fatty_acid")
          , ("Fatty_liver", "Fatty_liver_disease")
          , ("Fa’afafine", "Fa%CA%BBafafine")
          , ("Fear,_uncertainty_and_doubt", "Fear,_uncertainty,_and_doubt")
          , ("February_26_Incident", "February_26_incident")
          , ("Federalist_party", "Federalist_Party")
          , ("Fedex", "FedEx")
          , ("Feeder_of_lice", "Louse-feeder")
          , ("FeiTeng_(processor)", "FeiTeng")
          , ("Fel_d1", "Fel_d_1")
          , ("Fel_d_4", "Major_urinary_proteins#Kairomones")
          , ("Felid_hybrid", "Felid_hybrids")
          , ("Feline_cystitis", "Feline_idiopathic_cystitis")
          , ("Feline_panleukopenia", "Carnivore_protoparvovirus_1")
          , ("Felis_chaus", "Jungle_cat")
          , ("Felis_silvestris", "European_wildcat")
          , ("Felis_silvestris_ornata", "Asiatic_wildcat")
          , ("Female_sexuality", "Human_female_sexuality")
          , ("Femtojoule", "Joule")
          , ("Fen-phen", "Fenfluramine/phentermine")
          , ("Fender_Strat", "Fender_Stratocaster")
          , ("Ferdinandea_Island", "Graham_Island_(Mediterranean_Sea)")
          , ("Fermat%27s_Principle_of_Least_Time", "Fermat%27s_principle")
          , ("Fermat%27s_last_theorem", "Fermat%27s_Last_Theorem")
          , ("Fermat's_Last_Theorem", "Fermat%27s_Last_Theorem")
          , ("Fermat's_Principle_of_Least_Time", "Fermat%27s_principle")
          , ("Fermat's_principle", "Fermat%27s_principle")
          , ("Fermenter", "Industrial_fermentation")
          , ("Fermi", "Enrico_Fermi")
          , ("Fermi_calculation", "Fermi_problem")
          , ("Fermi_estimate", "Fermi_problem")
          , ("Fermi_problems", "Fermi_problem")
          , ("Ferrous_fumarate", "Iron(II)_fumarate")
          , ("Feyd-Rautha_Harkonnen", "Feyd-Rautha")
          , ("Fiat_Automobiles", "Fiat")
          , ("Fibonacci_numbers", "Fibonacci_sequence")
          , ("Fibroblasts", "Fibroblast")
          , ("Field-programmable_gate_arrays", "Field-programmable_gate_array")
          , ("Fifteen_points", "Cribbage_(pool)")
          , ("Fifth_Generation_Project", "Fifth_Generation_Computer_Systems")
          , ("Fifth_Head_of_Cerberus", "The_Fifth_Head_of_Cerberus")
          , ("Fifth_generation_computer", "Fifth_Generation_Computer_Systems")
          , ("Figure%E2%80%93ground_%28perception%29", "Figure%E2%80%93ground_(perception)")
          , ("Figure-ground_(perception)", "Figure%E2%80%93ground_(perception)")
          , ("File:Aa_example1.png", "https://commons.wikimedia.org/wiki/File:Aa_example1.png")
          , ("File:Aa_example2.png", "https://commons.wikimedia.org/wiki/File:Aa_example2.png")
          , ("File:Aa_example3.png", "https://commons.wikimedia.org/wiki/File:Aa_example3.png")
          , ("File:Ascii_art_alphabet.png", "https://commons.wikimedia.org/wiki/File:Ascii_art_alphabet.png")
          , ("File:Basic_by_Isotype.jpg", "https://commons.wikimedia.org/wiki/File:Basic_by_Isotype.jpg")
          , ("File:Design_by_El_Lissitzky_1922.jpg", "https://commons.wikimedia.org/wiki/File:Design_by_El_Lissitzky_1922.jpg")
          , ("File:Diagram_of_Hard_Disk_Drive_Manufacturer_Consolidation.svg", "https://commons.wikimedia.org/wiki/File:Diagram_of_Hard_Disk_Drive_Manufacturer_Consolidation.svg")
          , ("File:Energy_density.svg", "https://commons.wikimedia.org/wiki/File:Energy_density.svg")
          , ("File:EnwikipediagrowthGom.PNG", "https://commons.wikimedia.org/wiki/File:EnwikipediagrowthGom.PNG")
          , ("File:Flag_of_the_NSDAP_(1920%E2%80%931945).svg", "https://commons.wikimedia.org/wiki/File:Flag_of_the_NSDAP_(1920%E2%80%931945).svg")
          , ("File:InkErosion.jpg", "https://commons.wikimedia.org/wiki/File:InkErosion.jpg")
          , ("File:Jenson_De_viris_illustribus.jpg", "https://commons.wikimedia.org/wiki/File:Jenson_De_viris_illustribus.jpg")
          , ("File:Kingdom_of_Denmark_EEZ.PNG", "https://commons.wikimedia.org/wiki/File:Kingdom_of_Denmark_EEZ.PNG")
          , ("File:Labeled_talmud.png", "https://commons.wikimedia.org/wiki/File:Labeled_talmud.png")
          , ("File:Lady_Burton%27s_Edition.jpg", "https://commons.wikimedia.org/wiki/File:Lady_Burton%27s_Edition.jpg")
          , ("File:Lombardic_capitals_Ambraser_Heldenbuch_folio_75v.jpg", "https://commons.wikimedia.org/wiki/File:Lombardic_capitals_Ambraser_Heldenbuch_folio_75v.jpg")
          , ("File:Mah%C4%81vyutpatti_Title_Page.jpg", "https://commons.wikimedia.org/wiki/File:Mah%C4%81vyutpatti_Title_Page.jpg")
          , ("File:Manises_Basin_with_arms_of_Maria_of_Castile_VA_243-1853.jpg", "https://commons.wikimedia.org/wiki/File:Manises_Basin_with_arms_of_Maria_of_Castile_VA_243-1853.jpg")
          , ("File:NASA_Worm_logo.svg", "https://commons.wikimedia.org/wiki/File:NASA_Worm_logo.svg")
          , ("File:Nachf%C3%BClleisengallustinte_Pelikan_0.5_Liter_(G%C3%BCnther_Wagner).JPG", "https://commons.wikimedia.org/wiki/File:Nachf%C3%BClleisengallustinte_Pelikan_0.5_Liter_(G%C3%BCnther_Wagner).JPG")
          , ("File:Palmer_House_Hilton_Chicago.jpg", "https://commons.wikimedia.org/wiki/File:Palmer_House_Hilton_Chicago.jpg")
          , ("File:Sumer_is_icumen_in_-_Summer_Canon_(Reading_Rota)_(mid_13th_C),_f.11v_-_BL_Harley_MS_978.jpg", "https://commons.wikimedia.org/wiki/File:Sumer_is_icumen_in_-_Summer_Canon_(Reading_Rota)_(mid_13th_C),_f.11v_-_BL_Harley_MS_978.jpg")
          , ("File:TitlePage_Burton%27s_Nights.jpg", "https://commons.wikimedia.org/wiki/File:TitlePage_Burton%27s_Nights.jpg")
          , ("File_management", "File_manager")
          , ("Financial_intermediation", "Financial_intermediary")
          , ("Financial_technology", "Fintech")
          , ("Fine-tuned_Universe", "Fine-tuned_universe")
          , ("Fine-tuning_(machine_learning)", "Fine-tuning_(deep_learning)")
          , ("Fine-tuning_argument", "Fine-tuned_universe")
          , ("Fine_structure_constant", "Fine-structure_constant")
          , ("Finfish", "Fish")
          , ("Finger_tapping", "Tapping_rate")
          , ("Finite_state_machines", "Finite-state_machine")
          , ("Firmicutes", "Bacillota")
          , ("First_cousins", "Cousin")
          , ("First_order_logic", "First-order_logic")
          , ("Fischer-Tropsch_process", "Fischer%E2%80%93Tropsch_process")
          , ("Fisher's_exact_test", "Fisher%27s_exact_test")
          , ("Fisher-Tippett-Gnedenko_theorem", "Fisher%E2%80%93Tippett%E2%80%93Gnedenko_theorem")
          , ("Fisher_Information", "Fisher_information")
          , ("Fisking", "Robert_Fisk")
          , ("Fission_bomb", "Nuclear_weapon#Fission_weapons")
          , ("Fitts's_law", "Fitts%27s_law")
          , ("Five_Element_Ninjas", "Five_Elements_Ninjas")
          , ("Fixation_%28population_genetics%29", "Fixation_(population_genetics)")
          , ("Fixed_mindset", "Mindset#Fixed_and_growth_mindset")
          , ("Flamingos", "Flamingo")
          , ("Flannery_O'Connor", "Flannery_O%27Connor")
          , ("Flashback_%28psychology%29", "Flashback_(psychology)")
          , ("Flatting", "Roommate")
          , ("Flaubert", "Gustave_Flaubert")
          , ("Flaubert's_letters", "Flaubert%27s_letters")
          , ("Flavanols", "Flavan-3-ol")
          , ("Flavonoids", "Flavonoid")
          , ("Flehmen", "Flehmen_response")
          , ("Flesch-Kincaid", "Flesch%E2%80%93Kincaid_readability_tests")
          , ("Flipbook", "Flip_book")
          , ("Flipturn", "Tumble_turn")
          , ("Float_tanks", "Isolation_tank")
          , ("Floating_point", "Floating-point_arithmetic")
          , ("Florham_Park", "Florham_Park,_New_Jersey")
          , ("Flower_Travellin'_Band", "Flower_Travellin%27_Band")
          , ("Fluid_intelligence", "Fluid_and_crystallized_intelligence")
          , ("Fluoride_in_toothpaste", "Toothpaste#Fluoride")
          , ("Fluttershy", "List_of_mainline_My_Little_Pony_ponies#Fluttershy")
          , ("Flying_cars", "Flying_car")
          , ("Flynn_Effect", "Flynn_effect")
          , ("Folding%40home", "Folding@home")
          , ("Folding_bicycles", "Folding_bicycle")
          , ("Folk_physics", "Na%C3%AFve_physics")
          , ("Folksonomies", "Folksonomy")
          , ("Food_preference", "Food_choice")
          , ("Foot-binding", "Foot_binding")
          , ("For_A_Few_Dollars_More", "For_a_Few_Dollars_More")
          , ("Forced_disappearance", "Enforced_disappearance")
          , ("Forced_swim_test", "Behavioural_despair_test")
          , ("Fork_%28software_development%29", "Fork_(software_development)")
          , ("Fork_(operating_system)", "Fork_(system_call)")
          , ("Forks", "Fork")
          , ("Forlorn_hopes", "Forlorn_hope")
          , ("Form_(HTML)", "HTML_form")
          , ("Formics", "Ender%27s_Game_(novel_series)#Formics")
          , ("Forward_digit_span", "Memory_span#Digit-span")
          , ("Forward_error_correction", "Error_correction_code")
          , ("FosB", "FOSB")
          , ("Fosbury_Flop", "Fosbury_flop")
          , ("Foster-care", "Foster_care")
          , ("Foucault's_Pendulum", "Foucault%27s_Pendulum")
          , ("Founder_effects", "Founder_effect")
          , ("FourSquare", "Four_square")
          , ("Four_Arts_of_the_Chinese_Scholar", "Four_arts")
          , ("Four_colour_theorem", "Four_color_theorem")
          , ("Fourier_Transform", "Fourier_transform")
          , ("Fourier_multiplier", "Multiplier_(Fourier_analysis)")
          , ("Fractional_reserve_banking", "Fractional-reserve_banking")
          , ("Fractran", "FRACTRAN")
          , ("Framingham_CVD_risk_scores", "Framingham_Risk_Score")
          , ("Francis_Fulford_(television_personality)", "Francis_Fulford_(landowner)")
          , ("Frank_P._Ramsey", "Frank_Ramsey_(mathematician)")
          , ("Franklin_Delano_Roosevelt", "Franklin_D._Roosevelt")
          , ("Frederic_Bastiat", "Fr%C3%A9d%C3%A9ric_Bastiat")
          , ("Frederick_Pohl", "Frederik_Pohl")
          , ("Free_Content", "Free_content")
          , ("Free_Software", "Free_software")
          , ("Free_culture_movement", "Free-culture_movement")
          , ("Free_port", "Free_economic_zone")
          , ("Free_radical_scavenger", "Antioxidant")
          , ("Free_rider_problem", "Free-rider_problem")
          , ("Free_riders", "Free-rider_problem")
          , ("Freemasons", "Freemasonry")
          , ("Freeze_distillation", "Fractional_freezing")
          , ("French_Revolution_of_1789", "French_Revolution")
          , ("French_livres", "French_livre")
          , ("Freud", "Sigmund_Freud")
          , ("Friedman_(unit)", "Friedman_Unit")
          , ("Friendly_Islands", "Tonga")
          , ("From_the_New_World_%28novel%29", "From_the_New_World_(novel)")
          , ("Frontal_cortex", "Frontal_lobe")
          , ("Frontal_lobes", "Frontal_lobe")
          , ("Frontal_pole", "Cerebral_hemisphere#Poles")
          , ("Fréchet_Inception_Distance", "Fr%C3%A9chet_Inception_Distance")
          , ("Fuel_Cells", "Fuel_cell")
          , ("Fujiko_F._Fujio", "Fujiko_Fujio")
          , ("Fujiwara_family", "Fujiwara_clan")
          , ("Fukushima_Daiichi_nuclear_disaster", "Fukushima_nuclear_accident")
          , ("Fukushima_nuclear_disaster", "Fukushima_nuclear_accident")
          , ("Full_Metal_Panic%21", "Full_Metal_Panic!")
          , ("Full_Metal_Panic%21_The_Second_Raid", "Full_Metal_Panic!#The_Second_Raid")
          , ("Full_body_scanners", "Full_body_scanner")
          , ("Full_stops", "Full_stop")
          , ("Fuller's_earth", "Fuller%27s_earth")
          , ("Fully_homomorphic_encryption", "Homomorphic_encryption#Fully_homomorphic_encryption")
          , ("Functional_Magnetic_Resonance_Imaging", "Functional_magnetic_resonance_imaging")
          , ("Functional_connectivity", "Resting_state_fMRI#Functional")
          , ("Functional_programming_languages", "Functional_programming")
          , ("Functional_purity", "Purely_functional_programming")
          , ("Fundamental_Attribution_Error", "Fundamental_attribution_error")
          , ("Fundamental_attribution_bias", "Fundamental_attribution_error")
          , ("Fungiform_papilla", "Lingual_papillae#Fungiform_papilla")
          , ("Furikuri", "FLCL")
          , ("Fushigi_no_Umi_no_Nadia", "Nadia:_The_Secret_of_Blue_Water")
          , ("Fustel_de_Coulanges", "Numa_Denis_Fustel_de_Coulanges")
          , ("Futamura_projection", "Partial_evaluation#Futamura_projections")
          , ("Future_shock", "Future_Shock")
          , ("Fuzz_tester", "Fuzzing")
          , ("Fuzz_testing", "Fuzzing")
          , ("G%C3%B6del's_famous_Incompleteness_Theorem", "G%C3%B6del%27s_famous_Incompleteness_Theorem")
          , ("G%C3%B6del,_Escher,_Bach:_an_Eternal_Golden_Braid", "G%C3%B6del,_Escher,_Bach")
          , ("G%C5%8D_Nagai", "Go_Nagai")
          , ("G.G._Simpson", "George_Gaylord_Simpson")
          , ("G.H._Hardy", "G._H._Hardy")
          , ("G.K._Chesterton", "G._K._Chesterton")
          , ("GABA", "%CE%93-Aminobutyric_acid")
          , ("GAINAX", "Gainax")
          , ("GAO", "Government_Accountability_Office")
          , ("GCTA", "https://en.wikipedia.org/w/index.php?title=Genome-wide_complex_trait_analysis&oldid=*********")
          , ("GCTAs", "https://en.wikipedia.org/w/index.php?title=Genome-wide_complex_trait_analysis&oldid=*********")
          , ("SNP heritable", "https://en.wikipedia.org/w/index.php?title=Genome-wide_complex_trait_analysis&oldid=*********")
          , ("SNP heritability", "https://en.wikipedia.org/w/index.php?title=Genome-wide_complex_trait_analysis&oldid=*********")
          , ("GDB_debugger", "GNU_Debugger")
          , ("GFDL", "GNU_Free_Documentation_License")
          , ("GLP-1", "Glucagon-like_peptide-1")
          , ("GMO_crop", "Genetically_modified_crops")
          , ("GNU_screen", "GNU_Screen")
          , ("GPGPU", "General-purpose_computing_on_graphics_processing_units")
          , ("GPL", "GNU_General_Public_License")
          , ("GPS", "Global_Positioning_System")
          , ("GPU", "Graphics_processing_unit")
          , ("GPUs", "Graphics_processing_unit")
          , ("GRAS", "Generally_recognized_as_safe")
          , ("GRE", "Graduate_Record_Examinations")
          , ("GRE_Advanced_test", "Graduate_Record_Examinations")
          , ("GWAS_Catalog", "GWAS_catalog")
          , ("G_protein%E2%80%93coupled_receptor", "G_protein-coupled_receptor")
          , ("Galileo", "Galileo_Galilei")
          , ("Gallstones", "Gallstone")
          , ("Galton%27s_problem", "Phylogenetic_autocorrelation")
          , ("Galton's_problem", "Phylogenetic_autocorrelation")
          , ("Gambler's_conceit", "Gambler%27s_conceit")
          , ("Gambler's_fallacy", "Gambler%27s_fallacy")
          , ("Gambler's_ruin", "Gambler%27s_ruin")
          , ("Game_master", "Gamemaster")
          , ("Gamera_3", "Gamera_3:_Revenge_of_Iris")
          , ("Gamma-Aminobutyric_acid", "%CE%93-Aminobutyric_acid")
          , ("Gao_Gai_Gar", "The_King_of_Braves_GaoGaiGar")
          , ("Gappa", "Gappa:_The_Triphibian_Monster")
          , ("Garage_kits", "Garage_kit")
          , ("Garbage_collection_%28computer_science%29", "Garbage_collection_(computer_science)")
          , ("Garcia_Ordonez_de_Montalvo", "Garci_Rodr%C3%ADguez_de_Montalvo")
          , ("Garden_of_Sinners", "The_Garden_of_Sinners")
          , ("Garden_path_sentences", "Garden-path_sentence")
          , ("Gas_Chromatograph", "Gas_chromatography")
          , ("Gas_chromatograph", "Gas_chromatography")
          , ("Gas_chromatography-mass_spectrometry", "Gas_chromatography%E2%80%93mass_spectrometry")
          , ("Gas_transmission_pipeline", "Pipeline#Oil_and_natural_gas")
          , ("Gaspar_de_Portola", "Gaspar_de_Portol%C3%A1")
          , ("Gastric_inhibitory_polypeptide", "Glucose-dependent_insulinotropic_polypeptide")
          , ("Gastric_sleeve_surgery", "Sleeve_gastrectomy")
          , ("Gastroesophageal_reflux", "Gastroesophageal_reflux_disease")
          , ("Gates_Foundation", "Bill_%26_Melinda_Gates_Foundation")
          , ("Gaucher's_disease", "Gaucher%27s_disease")
          , ("Gauss's_principle_of_least_constraint", "Gauss%27s_principle_of_least_constraint")
          , ("Gaussian_(normal)_distribution", "Normal_distribution")
          , ("Gaussian_distribution", "Normal_distribution")
          , ("Gaussian_processes", "Gaussian_process")
          , ("Gazelles", "Gazelle")
          , ("Geico", "GEICO")
          , ("Gemini_IV", "Gemini_4")
          , ("Gemini_IX", "Gemini_9A")
          , ("Gender_norm", "Gender_role")
          , ("Gender_stereotype", "Gender_role#Gender_stereotypes")
          , ("Gene-environment_correlation", "Gene%E2%80%93environment_correlation")
          , ("Gene-environment_interaction", "Gene%E2%80%93environment_interaction")
          , ("Gene_cloning", "Molecular_cloning")
          , ("Gene_drives", "Gene_drive")
          , ("Gene_regulatory_networks", "Gene_regulatory_network")
          , ("Geneon_Universal_Entertainment", "NBCUniversal_Entertainment_Japan")
          , ("General_Accounting_Office", "Government_Accountability_Office")
          , ("General_Factor_of_Personality", "Hierarchical_structure_of_the_Big_Five#General_factor_of_personality")
          , ("General_Motors_Research_Center", "General_Motors_Research_Laboratories")
          , ("General_Products", "Pierson%27s_Puppeteers#General_Products")
          , ("General_Semantics", "General_semantics")
          , ("General_Winter", "Russian_Winter")
          , ("General_cognitive_ability", "G_factor_(psychometrics)")
          , ("General_equilibrium_model", "General_equilibrium_theory")
          , ("General_intelligence", "G_factor_(psychometrics)")
          , ("General_intelligence_factor", "G_factor_(psychometrics)")
          , ("General_purpose_technology", "General-purpose_technology")
          , ("Generalised_Anxiety_Disorder", "Generalized_anxiety_disorder")
          , ("Generative_Pre-trained_Transformer", "Generative_pre-trained_transformer")
          , ("Generative_adversarial_networks", "Generative_adversarial_network")
          , ("Genetic_algorithms", "Genetic_algorithm")
          , ("Genetic_correlations", "Genetic_correlation")
          , ("Genetic_evolution", "Evolution")
          , ("Genetic_lottery", "Original_position")
          , ("Genetic_makeup", "Heritability")
          , ("Genetic_studies", "Genetic_analysis")
          , ("Genetics_of_intelligence", "Heritability_of_IQ")
          , ("Genome-wide_Complex_Trait_Analysis", "Genome-wide_complex_trait_analysis")
          , ("Genome_sequencing", "Whole_genome_sequencing")
          , ("Genomic_medicine", "Medical_genetics")
          , ("Genshiken_Nidaime", "Genshiken")
          , ("Gentoo_linux", "Gentoo_Linux")
          , ("Geocities", "GeoCities")
          , ("Geoengineering", "Climate_engineering")
          , ("Geofencing", "Geo-fence")
          , ("Geoff_Hinton", "Geoffrey_Hinton")
          , ("Geoffroy's_cat", "Geoffroy%27s_cat")
          , ("Geoip", "Internet_geolocation")
          , ("George_Dyson_%28science_historian%29", "George_Dyson_(science_historian)")
          , ("George_Edward_Moore", "G._E._Moore")
          , ("George_M._Church", "George_Church_(geneticist)")
          , ("George_Udny_Yule", "Udny_Yule")
          , ("Georgia_Institute_of_Technology", "Georgia_Tech")
          , ("Geospatial_analysis", "Spatial_analysis#Geospatial_and_Hydrospatial_analysis")
          , ("Geoworks", "Berkeley_Softworks")
          , ("German_Shepherds", "German_Shepherd")
          , ("German_Socio-Economic_Panel", "Socio-Economic_Panel")
          , ("German_nuclear_energy_project", "German_nuclear_weapons_program_during_World_War_II")
          , ("German_nuclear_weapons_program", "German_nuclear_program_during_World_War_II")
          , ("Germanic_heroic_epic", "Germanic_heroic_legend")
          , ("Germinal_centers", "Germinal_center")
          , ("Geroscience", "Gerontology")
          , ("Ghanima_Atreides", "List_of_Dune_characters#Ghanima_Atreides")
          , ("Ghola", "List_of_technology_in_the_Dune_universe#Ghola")
          , ("Gianpiero_Palermo", "Gianpiero_D._Palermo")
          , ("Giant_deer", "Irish_elk")
          , ("Gibbs_distribution", "Boltzmann_distribution")
          , ("Gila_monsters", "Gila_monster")
          , ("Gilligan's_Island", "Gilligan%27s_Island")
          , ("Gills", "Gill")
          , ("Gini_Index", "Gini_coefficient")
          , ("Gini_index", "Gini_coefficient")
          , ("Ginkgo_Biloba", "Ginkgo_biloba")
          , ("Girl_Scout_cookies", "Girl_Scout_Cookies")
          , ("Girl_Scouts", "Girl_Guides")
          , ("Girls_Frontline", "Girls%27_Frontline")
          , ("Girolamo_Cardano", "Gerolamo_Cardano")
          , ("Git_%28software%29", "Git")
          , ("Git_(software)", "Git")
          , ("Github", "GitHub")
          , ("Github’s_Copilot", "GitHub_Copilot")
          , ("Gitlab", "GitLab")
          , ("Given_names", "Given_name")
          , ("Givewell", "GiveWell")
          , ("Glass_microspheres", "Glass_microsphere")
          , ("GlaxoSmithKline", "GSK_plc")
          , ("Glial", "Glia")
          , ("Glider_(Conway%27s_Life)", "Glider_(Conway%27s_Game_of_Life)")
          , ("Glissandi", "Glissando")
          , ("GloVe_(machine_learning)", "GloVe")
          , ("Global_Burden_of_Disease", "Disease_burden")
          , ("Global_burden_of_disease", "Disease_burden")
          , ("Global_surveillance_disclosures_(2013%E2%80%93present)", "2010s_global_surveillance_disclosures")
          , ("Global_surveillance_disclosures_(2013-present)", "2010s_global_surveillance_disclosures")
          , ("Global_warming", "Climate_change")
          , ("Glucagon-like_peptide", "Glucagon-like_peptide-1")
          , ("Glucagon-like_peptide-1_receptor_agonist", "GLP-1_receptor_agonist")
          , ("Glucosinolates", "Glucosinolate")
          , ("Glutamate", "Glutamic_acid")
          , ("Glycemic_control", "Diabetes_management#Glycemic_control")
          , ("Glycerine", "Glycerol")
          , ("Glycogen_synthase_kinase_3", "GSK-3")
          , ("Gnostic_gospels", "Nag_Hammadi_library")
          , ("Gnotobiology", "Gnotobiosis")
          , ("Go_to_market", "Go-to-market_strategy")
          , ("Goats", "Goat")
          , ("Gödel%27s_incompleteness_theorems", "G%C3%B6del%27s_incompleteness_theorems")
          , ("Gödel's_incompleteness_theorems", "G%C3%B6del%27s_incompleteness_theorems")
          , ("Godot_%28game_engine%29", "Godot_(game_engine)")
          , ("Goethe's_Faust", "Goethe%27s_Faust")
          , ("Goiter", "Goitre")
          , ("Goiterogens", "Goitrogen")
          , ("Goldbach_conjecture", "Goldbach%27s_conjecture")
          , ("Golden_Path_(Dune)", "Children_of_Dune")
          , ("Golden_Rice", "Golden_rice")
          , ("Golgi's_method", "Golgi%27s_method")
          , ("Gompertz-Makeham_law_of_mortality", "Gompertz%E2%80%93Makeham_law_of_mortality")
          , ("Gompertz_curve", "Gompertz_function")
          , ("Gongfu_tea_ceremony", "Gongfu_tea")
          , ("Gonville_and_Caius_College", "Gonville_and_Caius_College,_Cambridge")
          , ("GoodReads", "Goodreads")
          , ("Good_Clinical_Practice", "Good_clinical_practice")
          , ("Good_genes_hypothesis", "Sexy_son_hypothesis#Good_genes_theory")
          , ("Goodhart%27s_Law", "Goodhart%27s_law")
          , ("Goodhart's_Law", "Goodhart%27s_law")
          , ("Goodhart's_law", "Goodhart%27s_law")
          , ("Google+", "Google%2B")
          , ("Google_Co-op", "Google_Programmable_Search_Engine")
          , ("Google_Code", "Google_Developers#Google_Code")
          , ("Google_Colab", "Project_Jupyter#Industry_adoption")
          , ("Google_Knol", "Knol")
          , ("Google_Ngram", "Google_Ngram_Viewer")
          , ("Google_Storage", "Google_Cloud_Storage")
          , ("Google_Survey", "Google_Surveys")
          , ("Google_Video_Marketplace", "List_of_Google_products#Discontinued_in_2007")
          , ("Google_X", "X_Development")
          , ("Google_search", "Google_Search")
          , ("Goranger", "Himitsu_Sentai_Gorenger")
          , ("Gosenzo-sama_Banbanzai%21", "Gosenzo-sama_Banbanzai!")
          , ("Gotham_font", "Gotham_(typeface)")
          , ("Government_shutdowns", "Government_shutdown")
          , ("Grade_point_average", "Grading_in_education")
          , ("Gradient_Boosting", "Gradient_boosting")
          , ("Gradient_ascent", "Gradient_descent")
          , ("Gradient_boosted_trees", "Gradient_boosting#Gradient_tree_boosting")
          , ("Gradius_(series)", "Gradius")
          , ("Graduate_Record_Examination", "Graduate_Record_Examinations")
          , ("Grandes_%C3%A9coles", "Grande_%C3%A9cole")
          , ("Graph_convolutional_network", "Graph_neural_network#Graph_convolutional_network")
          , ("Grapheme-color_synesthesia", "Grapheme%E2%80%93color_synesthesia")
          , ("Graphic_organizers", "Graphic_organizer")
          , ("Gravitational_lensing", "Gravitational_lens")
          , ("Gravitational_physics", "Gravity")
          , ("Gravity's_Rainbow", "Gravity%27s_Rainbow")
          , ("Gravity_%28film%29", "Gravity_(2013_film)")
          , ("Gray_Lensman", "Grey_Lensman")
          , ("Gray_Riesling", "Trousseau_gris")
          , ("Great_Chain_of_Being", "Great_chain_of_being")
          , ("Great_Depression_of_British_Agriculture", "Great_depression_of_British_agriculture")
          , ("Great_Hanshin_Earthquake", "Great_Hanshin_earthquake")
          , ("Great_Jedi_Purge", "Jedi#Great_Jedi_Purge")
          , ("Great_Man_Theory", "Great_man_theory")
          , ("Great_Pyrenees", "Pyrenean_Mountain_Dog")
          , ("Greatest_element", "Greatest_element_and_least_element")
          , ("Greek_drachma", "Ancient_drachma")
          , ("Green_Sahara", "African_humid_period")
          , ("Green_giant", "Green_Giant")
          , ("Green_peppers", "Bell_pepper")
          , ("Green_space", "Urban_green_space")
          , ("Green_threads", "Green_thread")
          , ("Greenland_sled_dogs", "Greenland_Dog")
          , ("Greenspun%27s_Tenth_Law", "Greenspun%27s_tenth_rule")
          , ("Greenspun's_Tenth_Law", "Greenspun%27s_tenth_rule")
          , ("Grepping", "Grep#Usage_as_a_verb")
          , ("Gresham%27s_Law", "Gresham%27s_law")
          , ("Gresham's_Law", "Gresham%27s_law")
          , ("Grey’s_Anatomy", "Grey%27s_Anatomy")
          , ("Gripping_hand", "The_Gripping_Hand")
          , ("Grit_%28personality_trait%29", "Grit_(personality_trait)")
          , ("Gross_profit_margin", "Gross_margin")
          , ("Group_42_(Emirati_company)", "G42_(company)")
          , ("Growth_hormone-releasing_hormone", "Growth_hormone%E2%80%93releasing_hormone")
          , ("Growth_mindset", "Mindset#Fixed_and_growth_mindset")
          , ("Grue_and_bleen", "New_riddle_of_induction")
          , ("Guangzhou,_China", "Guangzhou")
          , ("Guaran%C3%A1", "Guarana")
          , ("Guayusa", "Ilex_guayusa")
          , ("GuideStar", "Candid_(organization)")
          , ("Guillemets", "Guillemet")
          , ("Guitar_tablature", "Tablature#Guitar_tablature")
          , ("Gujaratis", "Gujarati_people")
          , ("Gulliver's_Travels", "Gulliver%27s_Travels")
          , ("Gundam_RX-78", "Gundam_(fictional_robot)")
          , ("Gundam_X", "After_War_Gundam_X")
          , ("Gunma,_Japan", "Gunma_Prefecture")
          , ("Gunshot_locators", "Gunfire_locator")
          , ("Gustav_III_of_Sweden's_coffee_experiment", "Gustav_III_of_Sweden%27s_coffee_experiment")
          , ("Gustaw_Herling", "Gustaw_Herling-Grudzi%C5%84ski")
          , ("Gut-brain_axis", "Gut%E2%80%93brain_axis")
          , ("Gut_flora", "Gut_microbiota")
          , ("H%C3%A9riman_of_Tournai", "Herman_of_Tournai")
          , ("H%C5%8Dzuki_no_Reitetsu", "Hozuki%27s_Coolheadedness")
          , ("H-bomb", "Thermonuclear_weapon")
          , ("H.264", "Advanced_Video_Coding")
          , ("H.P._Lovecraft", "H._P._Lovecraft")
          , ("H1N1", "Influenza_A_virus_subtype_H1N1")
          , ("H2G2", "H2g2")
          , ("HBM2", "High_Bandwidth_Memory#HBM_2")
          , ("HBM3", "High_Bandwidth_Memory#HBMnext")
          , ("HCl", "Hydrogen_chloride")
          , ("HDL_cholesterol", "High-density_lipoprotein")
          , ("HEXACO", "HEXACO_model_of_personality_structure")
          , ("HIIE", "High-intensity_interval_training")
          , ("HM_(patient)", "Henry_Molaison")
          , ("HP_NewWave", "NewWave")
          , ("HUMINT", "Human_intelligence_(intelligence_gathering)")
          , ("H_to_the_Izzo", "Izzo_(H.O.V.A.)")
          , ("Haber-Bosch_process", "Haber_process")
          , ("Habit_(psychology)", "Habit")
          , ("Hadiths", "Hadith")
          , ("Hadoop", "Apache_Hadoop")
          , ("Hafiz_(Qur%27an)", "Hafiz_(Quran)")
          , ("Hafiz_(Qur'an)", "Hafiz_(Quran)")
          , ("Hair_transplants", "Hair_transplantation")
          , ("Hakata", "Hakata-ku,_Fukuoka")
          , ("Hal_Finney_%28cypherpunk%29", "Hal_Finney_(computer_scientist)")
          , ("Hal_Finney_(cypherpunk)", "Hal_Finney_(computer_scientist)")
          , ("Half_Life_2", "Half-Life_2")
          , ("Halley's_Comet", "Halley%27s_Comet")
          , ("Halting_Problem", "Halting_problem")
          , ("Halting_Theorem", "Halting_problem")
          , ("Hamilton%27s_rule", "Kin_selection#Hamilton%27s_rule")
          , ("Hamilton's_principle", "Hamilton%27s_principle")
          , ("Hamilton's_rule", "Kin_selection#Hamilton%27s_rule")
          , ("Hamlet's_Mill", "Hamlet%27s_Mill")
          , ("Hamming_window", "Window_function#Examples_of_window_functions")
          , ("Hampton_Court", "Hampton_Court_Palace")
          , ("Han-tan", "Handan")
          , ("Han_Dynasty", "Han_dynasty")
          , ("Hand-hooked_rugs", "Rug_hooking")
          , ("Haploid", "Ploidy#Haploid_and_monoploid")
          , ("Haplotypes", "Haplotype")
          , ("Hapsburgs", "House_of_Habsburg")
          , ("Haptophytes", "Haptophyte")
          , ("Harakiri", "Seppuku")
          , ("Hard_cider", "Cider")
          , ("Hardy-Weinberg_principle", "Hardy%E2%80%93Weinberg_principle")
          , ("Hare_Hare_Yukai", "List_of_Haruhi_Suzumiya_albums#Hare_Hare_Yukai")
          , ("Harem_anime", "Harem_(genre)")
          , ("Harper%27s_Monthly", "Harper%27s_Magazine")
          , ("Harper's_Monthly", "Harper%27s_Magazine")
          , ("Harry_B._Hutchins", "Harry_Burns_Hutchins")
          , ("Harry_Potter_and_the_Deathly_Hallows_-_Part_2", "Harry_Potter_and_the_Deathly_Hallows_%E2%80%93_Part_2")
          , ("Harry_Truman", "Harry_S._Truman")
          , ("Hart-Scott-Rodino_Antitrust_Improvements_Act", "Hart%E2%80%93Scott%E2%80%93Rodino_Antitrust_Improvements_Act")
          , ("Hartsfield_Manor,_Betchworth", "Hartsfield_Manor")
          , ("HashCash", "Hashcash")
          , ("Hash_precommitments", "Commitment_scheme")
          , ("Hashtags", "Hashtag")
          , ("Haskell_(programming_language)", "Haskell")
          , ("Hataraku_Maou-sama%21", "The_Devil_Is_a_Part-Timer!")
          , ("Hatsumode", "Hatsum%C5%8Dde")
          , ("Hawthorne_experiments", "Hawthorne_effect")
          , ("Hawthorne_illumination_experiments", "Hawthorne_effect")
          , ("Hazard_rate", "Survival_analysis#Hazard_function_and_cumulative_hazard_function")
          , ("Hazard_ratios", "Hazard_ratio")
          , ("Hazel_R._O'Leary", "Hazel_R._O%27Leary")
          , ("Hazmat_team", "Dangerous_goods")
          , ("HbA1C", "Glycated_hemoglobin")
          , ("HbA1c", "Glycated_hemoglobin")
          , ("Head_Start_%28program%29", "Head_Start_(program)")
          , ("Head_Start_Program", "Head_Start_(program)")
          , ("Headfooters", "Tadpole_person")
          , ("Headgear_(artist_group)", "Patlabor#Headgear")
          , ("Heads-up_Texas_hold'em", "Heads-up_Texas_hold%27em")
          , ("Heads_up_display", "Head-up_display")
          , ("Health_Professional_Shortage_Area", "Health_care")
          , ("Health_care_in_the_United_States", "Healthcare_in_the_United_States")
          , ("Health_effects_of_chocolate", "Chocolate#Health_effects")
          , ("Healthcare", "Health_care")
          , ("Hearing_aids", "Hearing_aid")
          , ("Hearing_voices", "Hearing_Voices_Movement")
          , ("Heart_disease", "Cardiovascular_disease")
          , ("Heartbeat_hypothesis", "Rate-of-living_theory")
          , ("Heavy_rock", "Hard_rock")
          , ("Hebbian_learning", "Hebbian_theory")
          , ("Hebb’s_rule", "Hebb%E2%80%99s_rule")
          , ("Hebrew_University", "Hebrew_University_of_Jerusalem")
          , ("HedgeStreet", "Nadex")
          , ("Hedgehog's_dilemma", "Hedgehog%27s_dilemma")
          , ("Hedges'_g", "Effect_size#Hedges%27_g")
          , ("Hedges_g", "Effect_size#Hedges%27_g")
          , ("Hegelian_dialectics", "Dialectic#Hegelian_dialectic")
          , ("Heidegger", "Martin_Heidegger")
          , ("Heifer_Project", "Heifer_International")
          , ("Heinrich_Kluver", "Heinrich_Kl%C3%BCver")
          , ("Heirs_of_Empire", "David_Weber")
          , ("Heisei_Gamera", "Gamera#Heisei_series")
          , ("Heisenberg", "Werner_Heisenberg")
          , ("Helen_Merrell_Lynd", "Helen_Lynd")
          , ("Hell's_Kitchen,_Manhattan", "Hell%27s_Kitchen,_Manhattan")
          , ("Hellsing_(TV_series)", "Hellsing#Anime")
          , ("Hellsing_Ultimate", "Hellsing#Original_video_animation")
          , ("Hellstrom's_Hive", "Hellstrom%27s_Hive")
          , ("Help:Watching_pages", "Help:Watchlist")
          , ("Help_talk:Authority_control", "Template_talk:Authority_control")
          , ("Hematoxylin", "Haematoxylin")
          , ("Hemiplegia", "Hemiparesis")
          , ("Hempel%27s_raven_paradox", "Raven_paradox")
          , ("Hempel's_raven_paradox", "Raven_paradox")
          , ("Henri_Poincare", "Henri_Poincar%C3%A9")
          , ("Henrick_Ibsen", "Henrik_Ibsen")
          , ("Hepatocytes", "Hepatocyte")
          , ("Herbal_teas", "Herbal_tea")
          , ("Herculaneum_papyrus", "Herculaneum_papyri")
          , ("Herfindahl%E2%80%93Hirschman_Index", "Herfindahl%E2%80%93Hirschman_index")
          , ("Herfindahl-Hirschman_index", "Herfindahl%E2%80%93Hirschman_index")
          , ("Heritabilities", "Heritability")
          , ("Heritability_of_intelligence", "Heritability_of_IQ")
          , ("Heritable", "Heredity")
          , ("Heritable_genome_editing", "Human_germline_engineering")
          , ("Hermanstadt", "Sibiu")
          , ("Heterochromia_Iridis", "Heterochromia_iridum")
          , ("Heterodimers", "Protein_dimer")
          , ("Heterogeneity_(statistics)", "Homogeneity_and_heterogeneity_(statistics)")
          , ("Heteroscedasticity", "Homoscedasticity_and_heteroscedasticity")
          , ("Heteroskedasticity", "Homoscedasticity_and_heteroscedasticity")
          , ("Heterozygosity", "Zygosity#Heterozygous")
          , ("Heuristics", "Heuristic")
          , ("Hibernating", "Hibernation")
          , ("Hidaka_Noriko", "Noriko_Hidaka")
          , ("Hidden_Markov_models", "Hidden_Markov_model")
          , ("High-sensitivity_C-reactive_protein", "C-reactive_protein#High-sensitivity_CRP")
          , ("High-yielding_crop_varieties", "High-yielding_variety")
          , ("High_Frequency_Active_Auroral_Research_Program", "High-frequency_Active_Auroral_Research_Program")
          , ("Higher_Biblical_criticism", "Historical_criticism")
          , ("Hikaru_dorodango", "Dorodango")
          , ("Hilbert's_problems", "Hilbert%27s_problems")
          , ("Himitsu_Sentai_Goranger", "Himitsu_Sentai_Gorenger")
          , ("Hip-hop", "Hip_hop_music")
          , ("His_and_Her_Circumstances", "Kare_Kano")
          , ("Historical_and_Critical_Dictionary", "Dictionnaire_Historique_et_Critique")
          , ("History_of_Cape_Colony_from_1806_to_1870", "History_of_the_Cape_Colony_from_1806_to_1870")
          , ("History_of_Maxwell's_equations", "History_of_Maxwell%27s_equations")
          , ("History_of_early_Christian_thought_on_abortion", "History_of_Christian_thought_on_abortion")
          , ("Hoarding_%28animal_behavior%29", "Hoarding_(animal_behavior)")
          , ("Hojicha", "H%C5%8Djicha")
          , ("Holocaust", "The_Holocaust")
          , ("Holstein_Friesian_cattle", "Holstein_Friesian")
          , ("Holy_Basil", "Ocimum_tenuiflorum")
          , ("Holystones", "Holystone")
          , ("Home_Demonstration_Clubs", "Home_demonstration_clubs")
          , ("Homo_sapiens", "Human")
          , ("Homoiconic", "Homoiconicity")
          , ("Homozygosity", "Zygosity#Homozygous")
          , ("Honest_signaling", "Signalling_theory#Honest_signals")
          , ("Honey_ant", "Honeypot_ant")
          , ("Honeybees", "Honey_bee")
          , ("Honeybush", "Cyclopia_(plant)")
          , ("Hono_no_Tenkosei", "Hon%C5%8D_no_Tenk%C5%8Dsei")
          , ("Honor_Harrington", "Honorverse#Protagonist")
          , ("Hopfield_networks", "Hopfield_network")
          , ("Horizon_(UK_TV_series)", "Horizon_(British_TV_series)")
          , ("Hormone_replacement", "Hormone_replacement_therapy")
          , ("Hormone_replacement_therapy_(menopause)", "Hormone_replacement_therapy")
          , ("Horns_effect", "Horn_effect")
          , ("Horseshoe_crabs", "Horseshoe_crab")
          , ("Horsetails", "Equisetum")
          , ("Hospital_Episode_Statistics", "NHS_Digital#Hospital_Episode_Statistics")
          , ("Hostess_clubs", "Host_and_hostess_clubs")
          , ("Hotbeds", "Hotbed")
          , ("Hotei_Tomoyasu", "Tomoyasu_Hotei")
          , ("Houshin_Exaxxion", "Cannon_God_Exaxxion")
          , ("Howard_S._Liddell", "Howard_Liddell_(psychologist)")
          , ("Howl's_Moving_Castle", "Howl%27s_Moving_Castle_(film)")
          , ("Howl's_Moving_Castle_(film)", "Howl%27s_Moving_Castle_(film)")
          , ("Hroswitha", "Hrotsvitha")
          , ("Huang's_law", "Huang%27s_law")
          , ("Hubble_radius", "Hubble_volume")
          , ("Hubert_L._Dreyfus", "Hubert_Dreyfus")
          , ("Huck_Finn", "Huckleberry_Finn")
          , ("Huffman_encoding", "Huffman_coding")
          , ("HuggingFace", "Hugging_Face")
          , ("Human%E2%80%93machine_interface", "User_interface")
          , ("Human_Genome_Project_-_Write", "Genome_Project-Write")
          , ("Human_language_technology", "Language_technology")
          , ("Human_microbiota", "List_of_human_microbiota")
          , ("Human_population", "World_population")
          , ("Human_pose_estimation", "3D_pose_estimation")
          , ("Humanists", "Humanism")
          , ("Hungarian_People's_Republic", "Hungarian_People%27s_Republic")
          , ("Hunter_Thompson", "Hunter_S._Thompson")
          , ("Hunting_Party_(novel)", "Elizabeth_Moon")
          , ("Huntington's_disease", "Huntington%27s_disease")
          , ("Huntington_disease", "Huntington%27s_disease")
          , ("Huperzine", "Huperzine_A")
          , ("Huperzine-A", "Huperzine_A")
          , ("Hutter_prize", "Hutter_Prize")
          , ("Hyakunin_Issh%C5%AB", "Ogura_Hyakunin_Isshu")
          , ("Hydergine", "Ergoloid")
          , ("Hydrodynamics", "Fluid_dynamics")
          , ("Hydrogen_bombs", "Thermonuclear_weapon")
          , ("Hydrogen_burning_limit", "Glossary_of_astronomy#hydrogen_burning_limit")
          , ("Hydrophobic", "Hydrophobe")
          , ("Hyman_Rickover", "Hyman_G._Rickover")
          , ("HyperTIES", "The_Interactive_Encyclopedia_System#HyperTIES")
          , ("Hyperandrogenemia", "Hyperandrogenism")
          , ("Hypercard", "HyperCard")
          , ("Hypercarnivory", "Hypercarnivore")
          , ("Hyperlinks", "Hyperlink")
          , ("Hyperrealism_%28visual_arts%29", "Hyperrealism_(visual_arts)")
          , ("Hyperreals", "Hyperreal_number")
          , ("Hypersomnolence", "Hypersomnia")
          , ("Hyperspectral", "Hyperspectral_imaging")
          , ("Hyperspheres", "N-sphere")
          , ("Hypervelocity_stars", "Stellar_kinematics#Hypervelocity_stars")
          , ("Hypervitaminosis_D", "Vitamin_D_toxicity")
          , ("Hyphal", "Hypha")
          , ("Hyphenation", "Syllabification")
          , ("Hyphenation_algorithm", "Syllabification#Algorithm")
          , ("Hypnopompic", "Hypnopompia")
          , ("Hypnotics", "Hypnotic")
          , ("Hypoallergenic_cats", "Allergy_to_cats#Hypoallergenic_cats")
          , ("Hypomanic", "Hypomania")
          , ("Hypomanic_episode", "Hypomania")
          , ("Hypostatization", "Reification_(fallacy)")
          , ("Hypoxia_(medical)", "Hypoxia_(medicine)")
          , ("I2p", "I2P")
          , ("IBM-360", "IBM_System/360")
          , ("IBM_7094", "IBM_7090#IBM_7094")
          , ("IBM_Plex_Mono", "IBM_Plex")
          , ("IBM_Roadrunner", "Roadrunner_(supercomputer)")
          , ("IBM_Selectric_typewriter", "IBM_Selectric")
          , ("IBM_Sequoia", "Sequoia_(supercomputer)")
          , ("IBM_System/23", "IBM_System/23_Datamaster")
          , ("ICBM", "Intercontinental_ballistic_missile")
          , ("ICBMs", "Intercontinental_ballistic_missile")
          , ("ICD-9", "International_Classification_of_Diseases#ICD-9")
          , ("IEEE", "Institute_of_Electrical_and_Electronics_Engineers")
          , ("IEEE_Standard_754_floating-point_numbers", "IEEE_754")
          , ("IFIH1", "MDA5")
          , ("IFlyTek", "IFlytek")
          , ("IGF-1", "Insulin-like_growth_factor_1")
          , ("INT8", "Integer_(computer_science)#Common_integral_data_types")
          , ("IOMMU", "Input%E2%80%93output_memory_management_unit")
          , ("IPFS", "InterPlanetary_File_System")
          , ("IPhone_applications", "IPhone#App_Store_and_third-party_apps")
          , ("IQ", "Intelligence_quotient")
          , ("IQR", "Interquartile_range")
          , ("IRC_bots", "IRC_bot")
          , ("ISBNs", "ISBN")
          , ("ITGAM", "Integrin_alpha_M")
          , ("Iatrogenicity", "Iatrogenesis")
          , ("Iblard_Time", "Iblard_Jikan")
          , ("Ice_I", "Ice_Ih")
          , ("Ice_wines", "Ice_wine")
          , ("Icelandic_constitutional_referendum,_1944", "1944_Icelandic_constitutional_referendum")
          , ("Icosahedral", "Icosahedron")
          , ("Identity-by-descent", "Identity_by_descent")
          , ("Idle_games", "Incremental_game")
          , ("Iframe", "HTML_element#Frames")
          , ("IgE", "Immunoglobulin_E")
          , ("IgG", "Immunoglobulin_G")
          , ("IgY", "Immunoglobulin_Y")
          , ("Ikkyu", "Ikky%C5%AB")
          , ("Illumina_(company)", "Illumina,_Inc.")
          , ("Illumina_Inc.", "Illumina,_Inc.")
          , ("Imagawa_Ry%C5%8Dshun", "Imagawa_Sadayo")
          , ("Image_classification", "Computer_vision#Recognition")
          , ("Image_maps", "Image_map")
          , ("Image_processing", "Digital_image_processing")
          , ("Imaginary_friends", "Imaginary_friend")
          , ("Imam_al-Haddad", "%27Abdallah_ibn_%27Alawi_al-Haddad")
          , ("Imitation_learning", "AI_alignment#Learning_human_values_and_preferences")
          , ("Immigration_policy", "Border_control#Immigration_law_and_policy")
          , ("Immunosuppressive_therapy", "Immunosuppression")
          , ("Impulse_control", "Inhibitory_control")
          , ("Imputation_reference_panel", "Imputation_(genetics)")
          , ("In-context_learning", "Prompt_engineering#In-context_learning")
          , ("In_Vitro_Fertilization", "In_vitro_fertilisation")
          , ("In_vitro_fertilization", "In_vitro_fertilisation")
          , ("In_vitro_gametogenesis", "Gametogenesis#In_vitro_gametogenesis")
          , ("In_vitro_oocyte_maturation", "In_vitro_maturation")
          , ("Inattention", "Attention")
          , ("Inbreeding_coefficient", "Coefficient_of_inbreeding")
          , ("Incentive-compatible", "Incentive_compatibility")
          , ("Incentive_compatible", "Incentive_compatibility")
          , ("Income_inequality", "Income_distribution")
          , ("Incremental_cost-effectiveness_ratios", "Incremental_cost-effectiveness_ratio")
          , ("Incubation_effect", "Incubation_(psychology)")
          , ("Independent_software_vendors", "Independent_software_vendor")
          , ("Index_(typography)", "Manicule")
          , ("Indian_Institute_of_Technology", "Indian_Institutes_of_Technology")
          , ("Induced_pluripotent_stem_cells", "Induced_pluripotent_stem_cell")
          , ("Industrial/organizational_psychology", "Industrial_and_organizational_psychology")
          , ("Industrial_robots", "Industrial_robot")
          , ("Inequality_of_arithmetic_and_geometric_means", "AM-GM_Inequality")
          , ("Infarct", "Infarction")
          , ("Influence_diagrams", "Influence_diagram")
          , ("Influenza_A", "Influenza_A_virus")
          , ("Influenza_B", "Influenza_B_virus")
          , ("Influenza_vaccination", "Influenza_vaccine")
          , ("Influenza_vaccines", "Influenza_vaccine")
          , ("Information_entropy", "Entropy_(information_theory)")
          , ("Information_processing", "Information_processing_(psychology)")
          , ("Ingroup", "In-group_and_out-group")
          , ("Ingroup_bias", "In-group_favoritism")
          , ("Initials", "Initial")
          , ("Ink_traps", "Ink_trap")
          , ("Inner_speech", "Intrapersonal_communication")
          , ("Inositol_monophosphatase", "Inositol-phosphate_phosphatase")
          , ("Insensitive_High_Explosive", "Insensitive_munition")
          , ("Insertionsort", "Insertion_sort")
          , ("Insight_problem", "Eureka_effect#Insight_problems")
          , ("Insight_problems", "Eureka_effect#Insight_problems")
          , ("Instagram_Reels", "Instagram#Reels")
          , ("Institut_Mittag-Leffler", "Mittag-Leffler_Institute")
          , ("Institutional_review_boards", "Institutional_review_board")
          , ("Institutionalization", "Institutionalisation")
          , ("Instructional_video", "Educational_film")
          , ("Instrumental_conditioning", "Operant_conditioning")
          , ("Instrumental_variable", "Instrumental_variables_estimation")
          , ("Instrumental_variables", "Instrumental_variables_estimation")
          , ("Integer_factoring", "Integer_factorization")
          , ("Integrals", "Integral")
          , ("Intel_80386", "I386")
          , ("Intel_8051", "MCS-51")
          , ("Intel_MCS-51", "MCS-51")
          , ("Intel_SGX", "Software_Guard_Extensions")
          , ("Intelligence_explosion", "Technological_singularity#Intelligence_explosion")
          , ("Intention-to-treat", "Intention-to-treat_analysis")
          , ("Interchange_fees", "Interchange_fee")
          , ("Intercontinental_ballistic_missiles", "Intercontinental_ballistic_missile")
          , ("Interest_group", "Advocacy_group")
          , ("Intergenerational_mobility", "Social_mobility")
          , ("Interleukin_(IL)-21", "Interleukin_21")
          , ("Intermittent_reinforcement", "Reinforcement#Intermittent_reinforcement_schedules")
          , ("Internal_evidence", "Textual_criticism#Internal_evidence")
          , ("Internal_monologue", "Intrapersonal_communication")
          , ("Internalizing_symptoms", "Internalizing_disorder")
          , ("International_Cryptology_Conference", "International_Association_for_Cryptologic_Research#International_Cryptology_Conference")
          , ("International_Standard_Book_Number", "ISBN")
          , ("Internet_Movie_Database", "IMDb")
          , ("Internet_Service_Provider", "Internet_service_provider")
          , ("Internet_troll", "Troll_(slang)")
          , ("Interrupted_time_series_analysis", "Interrupted_time_series")
          , ("Interspecies_SCNT", "Somatic_cell_nuclear_transfer#Interspecies_nuclear_transfer")
          , ("Intracerebral_bleeding", "Intracerebral_hemorrhage")
          , ("Intracranial_haemorrhage", "Intracranial_hemorrhage")
          , ("Introgressed", "Introgression")
          , ("Intrusive_thoughts", "Intrusive_thought")
          , ("InuYasha", "Inuyasha")
          , ("Inuksuit", "Inuksuk")
          , ("Inventory_of_Henry_VIII_of_England", "Inventory_of_Henry_VIII")
          , ("Inverse_reinforcement_learning", "Reinforcement_learning#Inverse_reinforcement_learning")
          , ("Invertible", "Inverse_element")
          , ("Investment_theory", "Asset_pricing")
          , ("Investors_Business_Daily", "Investor%27s_Business_Daily")
          , ("Invisible_college", "Invisible_College")
          , ("Iproniazide", "Iproniazid")
          , ("Iranian_Jews", "Persian_Jews")
          , ("Iraq_sanctions", "International_sanctions_against_Iraq")
          , ("Iraqi_no-fly_zones", "Iraqi_no-fly_zones_conflict")
          , ("Irish_Supreme_Court", "Supreme_Court_of_Ireland")
          , ("Irish_pork_crisis_of_2008", "2008_Irish_pork_crisis")
          , ("Irish_potato_famine", "Great_Famine_(Ireland)")
          , ("Iron_Law_of_Oligarchy", "Iron_law_of_oligarchy")
          , ("Is_Marriage_for_White_People%3F_How_the_African_American_Marriage_decline_affects_everyone", "Is_Marriage_for_White_People%3F")
          , ("Isaac_Newton%27s_religious_views", "Religious_views_of_Isaac_Newton")
          , ("Isaac_Newton's_occult_studies", "Isaac_Newton%27s_occult_studies")
          , ("Isaac_Newton's_religious_views", "Religious_views_of_Isaac_Newton")
          , ("Isabella_d'Este", "Isabella_d%27Este")
          , ("Ise_no_Taiu", "Ise_no_Taifu")
          , ("Ishihara_Test", "Ishihara_test")
          , ("Ising_spin_glass", "Ising_model")
          , ("Islamic_law", "Sharia")
          , ("Islamization", "Spread_of_Islam")
          , ("Isolation_effect", "Von_Restorff_effect")
          , ("Isomers", "Isomer")
          , ("Isoproterenol", "Isoprenaline")
          , ("Isotope_labeling", "Isotopic_labeling")
          , ("It%27s_A_Wonderful_Life", "It%27s_a_Wonderful_Life")
          , ("It's_a_Long_Way_to_Tipperary", "It%27s_a_Long_Way_to_Tipperary")
          , ("It_Can't_Happen_Here", "It_Can%27t_Happen_Here")
          , ("Item_Response_Theory", "Item_response_theory")
          , ("Ives_Delage", "Yves_Delage")
          , ("J.%E2%80%89H._Prynne", "J._H._Prynne")
          , ("J.B.S._Haldane", "J._B._S._Haldane")
          , ("J.B.S_Haldane", "J._B._S._Haldane")
          , ("J.D._Hooker", "Joseph_Dalton_Hooker")
          , ("J.G._Ballard", "J._G._Ballard")
          , ("J.K._Rowling", "J._K._Rowling")
          , ("J.P._Hogan", "James_P._Hogan_(writer)")
          , ("J.R.R._Tolkien", "J._R._R._Tolkien")
          , ("J.S._Bach", "Johann_Sebastian_Bach")
          , ("J._B._Rhine", "Joseph_Banks_Rhine")
          , ("J._S._Bach", "Johann_Sebastian_Bach")
          , ("JSONL", "JSON_streaming#Line-delimited_JSON")
          , ("Jacobus_Henricus_van%27t_Hoff", "Jacobus_Henricus_van_%27t_Hoff")
          , ("Jacobus_Henricus_van't_Hoff", "Jacobus_Henricus_van_%27t_Hoff")
          , ("Jaguars", "Jaguar")
          , ("James-Stein_estimator", "James%E2%80%93Stein_estimator")
          , ("James_Garfield", "James_A._Garfield")
          , ("James_Gunn_(author)", "James_E._Gunn")
          , ("James_Harris_Simons", "Jim_Simons_(mathematician)")
          , ("James_L._Kugel", "James_Kugel")
          , ("Japan's_Imperial_Conspiracy", "Japan%27s_Imperial_Conspiracy")
          , ("Japan-Korea_disputes", "Japan%E2%80%93Korea_disputes")
          , ("Japan_Sci-Fi_Convention", "Nihon_SF_Taikai")
          , ("Japan_Shipbuilding_Industry_Foundation", "Nippon_Foundation")
          , ("Japan_World_Exposition", "Expo_%2770")
          , ("Japanese_Brazilian", "Japanese_Brazilians")
          , ("Japanese_Cultural_Festival", "Cultural_festival_(Japan)")
          , ("Japanese_Diet", "National_Diet")
          , ("Japanese_Society_for_Rights_of_Authors,_Composers,_and_Publishers", "Japanese_Society_for_Rights_of_Authors,_Composers_and_Publishers")
          , ("Japanese_real_estate_bubble", "Japanese_asset_price_bubble")
          , ("Jar-Jar_Binks", "Jar_Jar_Binks")
          , ("Jason_Hazeley", "Jason_Hazeley_and_Joel_Morris")
          , ("Jason_Matheny", "Jason_Gaverick_Matheny")
          , ("Jason_Scott_Sadofsky", "Jason_Scott")
          , ("Jean-Jacques_Rousseau's_Social_Contract", "Jean-Jacques_Rousseau%27s_Social_Contract")
          , ("Jean_le_Rond_d'Alembert", "Jean_le_Rond_d%27Alembert")
          , ("Jedi_Exile", "Star_Wars_Knights_of_the_Old_Republic_II:_The_Sith_Lords")
          , ("Jeff_Dean_(computer_scientist)", "Jeff_Dean")
          , ("Jennie_Jerome", "Lady_Randolph_Churchill")
          , ("Jensen's_inequality", "Jensen%27s_inequality")
          , ("Jensen_effect", "Spearman%27s_hypothesis#Other_related_hypotheses")
          , ("Jeopardy%21", "Jeopardy!")
          , ("Jerry_Linenger", "Jerry_M._Linenger")
          , ("Jewish_angelic_hierarchy", "Angels_in_Judaism#Angelic_hierarchy")
          , ("Jhana", "Dhyana_in_Buddhism")
          , ("Jiangsu_Province", "Jiangsu")
          , ("Jimbo_Wales", "Jimmy_Wales")
          , ("Jinrui_wa_Suitaishimashita", "Humanity_Has_Declined")
          , ("Job_Partnership_Training_Act", "Job_Training_Partnership_Act_of_1982")
          , ("Job_classification", "Job_analysis")
          , ("Jod-Basedow's_Syndrome", "Jod-Basedow%27s_Syndrome")
          , ("Joe_Armstrong_(programming)", "Joe_Armstrong_(programmer)")
          , ("Joel_On_Software", "Joel_Spolsky")
          , ("Johann_Peter_Gustav_Lejeune_Dirichlet", "Peter_Gustav_Lejeune_Dirichlet")
          , ("John_Desmond_Bernal", "J._D._Bernal")
          , ("John_Forbes_Nash_Junior", "John_Forbes_Nash_Jr.")
          , ("John_H._Gibbon", "John_Heysham_Gibbon")
          , ("John_Larry_Kelly,_Jr.", "John_Larry_Kelly_Jr.")
          , ("John_Locke's_Second_Treatise", "John_Locke%27s_Second_Treatise")
          , ("John_McCarthy_%28computer_scientist%29", "John_McCarthy_(computer_scientist)")
          , ("John_P._A._Ioannidis", "John_Ioannidis")
          , ("John_Rawls's_Theory_of_Justice", "John_Rawls%27s_Theory_of_Justice")
          , ("John_Robb_%28military_theorist%29", "John_Robb_(author)")
          , ("John_Robb_(military_theorist)", "John_Robb_(author)")
          , ("John_Rockefeller_McCormick", "Edith_Rockefeller_McCormick")
          , ("John_Strutt,_3rd_Baron_Rayleigh", "John_William_Strutt,_3rd_Baron_Rayleigh")
          , ("John_W._Tukey", "John_Tukey")
          , ("Johnson_%26_Johnson_COVID-19_vaccine", "Janssen_COVID-19_vaccine")
          , ("Joint_Improvised_Explosive_Device_Defeat_Organization", "Joint_Improvised-Threat_Defeat_Organization")
          , ("Jointly_file", "Filing_status#Married_filing_jointly")
          , ("Josef_Stalin", "Joseph_Stalin")
          , ("Joseph_Louis_Lagrange", "Joseph-Louis_Lagrange")
          , ("Joseph_Ratzinger", "Pope_Benedict_XVI")
          , ("Joseph_Rhine", "Joseph_Banks_Rhine")
          , ("Josephson_junction", "Josephson_effect")
          , ("Josephson_junctions", "Josephson_effect")
          , ("Journal_of_Neuroscience", "The_Journal_of_Neuroscience")
          , ("Journal_of_the_American_Medical_Association", "JAMA")
          , ("Julian_Lincoln_Simon", "Julian_Simon")
          , ("Julius_Africanus", "Sextus_Julius_Africanus")
          , ("June_(manga_magazine)", "June_(magazine)")
          , ("Jungian_analysis", "Analytical_psychology")
          , ("Junipero_Serra", "Jun%C3%ADpero_Serra")
          , ("Junk_DNA", "Non-coding_DNA#Junk_DNA")
          , ("Jupiter_Ammon", "Amun#Greece")
          , ("Juso", "J%C5%ABs%C5%8D")
          , ("Just_world", "Just-world_hypothesis")
          , ("Just_world_beliefs", "Just-world_hypothesis")
          , ("Justification_(typesetting)", "Typographic_alignment#Justified")
          , ("Juusenki_L-Gaim", "Heavy_Metal_L-Gaim")
          , ("K%C3%B3ryos", "*K%C3%B3ryos")
          , ("K%C5%8Dji_Aihara", "Koji_Aihara")
          , ("K'Nex", "K%27Nex")
          , ("K-On%21", "K-On!")
          , ("K-means", "K-means_clustering")
          , ("K-nearest_neighbor", "K-nearest_neighbors_algorithm")
          , ("KDM5B", "JARID1B")
          , ("KLKB1", "Plasma_kallikrein")
          , ("Kabuki-cho", "Kabukich%C5%8D")
          , ("Kadokawa_Pictures", "Kadokawa_Daiei_Studio")
          , ("Kafka", "Franz_Kafka")
          , ("Kahoolawe", "Kaho%CA%BBolawe")
          , ("Kaihogyo", "Kaih%C5%8Dgy%C5%8D")
          , ("Kaiji_%28manga%29", "Kaiji_(manga)")
          , ("Kaiketsu_Notenki", "Kaiketsu_Noutenki")
          , ("Kaldor-Hicks_efficiency", "Kaldor%E2%80%93Hicks_efficiency")
          , ("Kalman_filters", "Kalman_filter")
          , ("Kalpa_(aeon)", "Kalpa_(time)")
          , ("Kamchatka", "Kamchatka_Peninsula")
          , ("Kamichu%21", "Kamichu!")
          , ("Kamille_Bidan", "List_of_Mobile_Suit_Zeta_Gundam_characters#Kamille_Bidan")
          , ("Kanadehon_Chushingura", "Kanadehon_Ch%C5%ABshingura")
          , ("Kancho", "Kanch%C5%8D")
          , ("Kansai", "Kansai_region")
          , ("Kanto_region", "Kant%C5%8D_region")
          , ("Kap_K%C3%B8benhavn_Formation", "Kap_Kobenhavn_Formation")
          , ("Kapitza's_pendulum", "Kapitza%27s_pendulum")
          , ("Kaplan-Meier_estimator", "Kaplan%E2%80%93Meier_estimator")
          , ("Kaposi's_sarcoma", "Kaposi%27s_sarcoma")
          , ("Kara_no_Kyoukai", "The_Garden_of_Sinners")
          , ("Kareshi_Kanojo_no_Jijo", "Kare_Kano")
          , ("Karl_Friedrich_Gauss", "Carl_Friedrich_Gauss")
          , ("Karnofsky_Performance_Status", "Performance_status")
          , ("Kasumigaseki_Subway_Station", "Kasumigaseki_Station_(Tokyo)")
          , ("Kaufman_Brief_Intelligence_Test", "Alan_S._Kaufman#Overview_of_Kaufman%27s_tests")
          , ("Kaze_Tachinu", "The_Wind_Rises")
          , ("Kele_language_(Congo)", "Kele-Foma_language")
          , ("Kelly_Criterion", "Kelly_criterion")
          , ("Kennedy_administration", "Presidency_of_John_F._Kennedy")
          , ("Kennelly-Heaviside_layer", "Kennelly%E2%80%93Heaviside_layer")
          , ("Kenyan_shillings", "Kenyan_shilling")
          , ("Kenzaburo_Oe", "Kenzabur%C5%8D_%C5%8Ce")
          , ("Kepler", "Johannes_Kepler")
          , ("Kerckhoffs%27s_Principle", "Kerckhoffs%27s_principle")
          , ("Kerckhoffs's_Principle", "Kerckhoffs%27s_principle")
          , ("Kerckhoffs's_principle", "Kerckhoffs%27s_principle")
          , ("Kernel_density_estimator", "Kernel_density_estimation")
          , ("Kernel_methods", "Kernel_method")
          , ("Key_frames", "Key_frame")
          , ("Keybindings", "Keyboard_shortcut#Customization")
          , ("Khoesan", "Khoisan")
          , ("Kidney_stones", "Kidney_stone_disease")
          , ("Kido_Senshi_Gundam", "Mobile_Suit_Gundam")
          , ("Kidosenkan_Nadesico", "Martian_Successor_Nadesico")
          , ("Killer_whale", "Orca")
          , ("Kim_Il-Sung", "Kim_Il_Sung")
          , ("Kim_Il-sung", "Kim_Il_Sung")
          , ("Kim_Il-sung_University", "Kim_Il_Sung_University")
          , ("Kim_Jong-Il", "Kim_Jong_Il")
          , ("Kim_Jong-Un", "Kim_Jong_Un")
          , ("Kim_Jong-il", "Kim_Jong_Il")
          , ("Kim_Jong-un", "Kim_Jong_Un")
          , ("Kim_Yo-jong", "Kim_Yo_Jong")
          , ("Kindle_Unlimited", "Kindle_Store#Kindle_Unlimited")
          , ("Kinesin-1", "Kinesin#Kinesin_superfamily_members")
          , ("Kinesthesia", "Proprioception#Proprioception_and_kinesthesia")
          , ("Kinesthesis", "Proprioception")
          , ("King_Joe_(Ultra_monster)", "Ultraman")
          , ("Kingdom_Of_Loathing", "Kingdom_of_Loathing")
          , ("Kinki_University", "Kindai_University")
          , ("Klaiber's_law", "Klaiber%27s_law")
          , ("Klein_bottles", "Klein_bottle")
          , ("Kleine-Levin_syndrome", "Kleine%E2%80%93Levin_syndrome")
          , ("Kleinfelter's_syndrome", "Klinefelter_syndrome")
          , ("Kleptoparasitic", "Kleptoparasitism")
          , ("Knapsack_problems", "Knapsack_problem")
          , ("Knowledge-graph", "Knowledge_graph")
          , ("Knowledge_graphs", "Knowledge_graph")
          , ("Knox%27s_Decalogue", "Golden_Age_of_Detective_Fiction#Description_of_the_genre")
          , ("Knox's_Decalogue", "Golden_Age_of_Detective_Fiction#Description_of_the_genre")
          , ("Knuth-Morris-Pratt_algorithm", "Knuth%E2%80%93Morris%E2%80%93Pratt_algorithm")
          , ("Kobe_Earthquake", "Great_Hanshin_earthquake")
          , ("Koch_brothers", "Koch_family")
          , ("Kohn-Sham_equations", "Kohn%E2%80%93Sham_equations")
          , ("Kolyma_River", "Kolyma_(river)")
          , ("Komparu_Zenchiku", "Konparu_Zenchiku")
          , ("Kongo_Gumi", "Kong%C5%8D_Gumi")
          , ("Koomey's_law", "Koomey%27s_law")
          , ("Korean_wave", "Korean_Wave")
          , ("Kratom", "Mitragyna_speciosa")
          , ("Kryder%27s_Law", "Mark_Kryder#Kryder%27s_law_projection")
          , ("Kryder%27s_law", "Mark_Kryder#Kryder%27s_law_projection")
          , ("Kryder's_Law", "Mark_Kryder#Kryder%27s_law_projection")
          , ("Kryder's_law", "Mark_Kryder#Kryder%27s_law_projection")
          , ("Kuiper_Belt", "Kuiper_belt")
          , ("Kullback-Leibler_divergence", "Kullback%E2%80%93Leibler_divergence")
          , ("Kush_%28Cannabis%29", "Kush_(cannabis)")
          , ("Kush_(Cannabis)", "Kush_(cannabis)")
          , ("Kwisatz_Haderach", "Bene_Gesserit#Breeding_program")
          , ("L%C3%A8se_majest%C3%A9_in_Thailand", "L%C3%A8se-majest%C3%A9_in_Thailand")
          , ("L-alanine", "Alanine")
          , ("L-dopa", "L-DOPA")
          , ("L-theanine", "Theanine")
          , ("LD50", "Median_lethal_dose")
          , ("LDL_cholesterol", "Low-density_lipoprotein")
          , ("LED_lights", "LED_lamp")
          , ("LEGO", "Lego")
          , ("LG_Corporation", "LG")
          , ("LHCb", "LHCb_experiment")
          , ("LIDAR", "Lidar")
          , ("LMNA", "Prelamin-A/C")
          , ("LOESS", "Local_regression")
          , ("LSAT", "Law_School_Admission_Test")
          , ("LSD_and_schizophrenia", "LSD#Mental_disorders")
          , ("LSTM", "Long_short-term_memory")
          , ("LSTMs", "Long_short-term_memory")
          , ("LUKS", "Linux_Unified_Key_Setup")
          , ("La_Roche_College", "La_Roche_University")
          , ("Lab_color_space", "CIELAB_color_space")
          , ("Lab_rats", "Laboratory_rat")
          , ("Labor_productivity", "Workforce_productivity")
          , ("Labor_union", "Trade_union")
          , ("Labrador_retriever", "Labrador_Retriever")
          , ("Lady_Chatterley's_Lover", "Lady_Chatterley%27s_Lover")
          , ("Lady_Jane_%281986_film%29", "Lady_Jane_(1986_film)")
          , ("Lady_Margaret_Hall", "Lady_Margaret_Hall,_Oxford")
          , ("Lady_Margot_Fenring", "Margot_Fenring")
          , ("Lake_Ashino", "Lake_Ashi")
          , ("Lanchester's_laws", "Lanchester%27s_laws")
          , ("Land_Reform_Movement_(China)", "Land_Reform_Movement")
          , ("Landing_slot", "Airport_slot")
          , ("Landraces", "Landrace")
          , ("Langton's_ant", "Langton%27s_ant")
          , ("Langton’s_ant", "Langton%E2%80%99s_ant")
          , ("Laparoscopic", "Laparoscopy")
          , ("Laparoscopic_adjustable_gastric_banding", "Adjustable_gastric_band")
          , ("Laphroaig", "Laphroaig_distillery")
          , ("Laplace%27s_rule_of_succession", "Rule_of_succession")
          , ("Laplace's_rule_of_succession", "Rule_of_succession")
          , ("Laplace’s_demon", "Laplace%E2%80%99s_demon")
          , ("Laplacian_demon", "Laplace%27s_demon")
          , ("Lapland_(region)", "S%C3%A1pmi")
          , ("Large_language_models", "Large_language_model")
          , ("Larry_Summers", "Lawrence_Summers")
          , ("Larus_dominicanus", "Kelp_gull")
          , ("Las_Sergas_de_Esplanadian", "Las_sergas_de_Esplandi%C3%A1n")
          , ("Laser_microphones", "Laser_microphone")
          , ("Laserdisc", "LaserDisc")
          , ("Lasso_%28statistics%29", "Lasso_(statistics)")
          , ("Last_Generation_(activists)", "Last_Generation_(climate_movement)")
          , ("Latent_Dirichlet_Allocation", "Latent_Dirichlet_allocation")
          , ("Latent_variable", "Latent_and_observable_variables")
          , ("Lateral_prefrontal", "Lateral_prefrontal_cortex")
          , ("Lateral_ventricle", "Lateral_ventricles")
          , ("Latex_and_PVC_fetishism", "Rubber_and_PVC_fetishism")
          , ("Latter_Day_Saints", "Latter_Day_Saint_movement")
          , ("Launch_service_provider", "List_of_launch_service_providers")
          , ("Lavender", "Lavandula")
          , ("Lavender_essential_oil", "Lavender_oil")
          , ("Law_And_Order:_SVU", "Law_%26_Order:_Special_Victims_Unit")
          , ("Law_of_Large_Numbers", "Law_of_large_numbers")
          , ("Law_of_reciprocity", "Reciprocity_law")
          , ("Law_of_supply_and_demand", "Supply_and_demand")
          , ("Lawrence_H._Summers", "Lawrence_Summers")
          , ("Lawrence_Radiation_Laboratory", "Lawrence_Berkeley_National_Laboratory")
          , ("Lazar_Markovich_Lissitzky", "El_Lissitzky")
          , ("Le%C3%B3_Szil%C3%A1rd", "Leo_Szilard")
          , ("Le_C%C3%B4t%C3%A9_de_Guermantes", "In_Search_of_Lost_Time#Volume_Three:_The_Guermantes_Way")
          , ("Le_Chatelier's_principle", "Le_Chatelier%27s_principle")
          , ("Leaky_integrate-and-fire", "Biological_neuron_model#Leaky_integrate-and-fire")
          , ("Lean_3", "Lean_(proof_assistant)")
          , ("Learning_rates", "Learning_rate")
          , ("Learning_rules", "Learning_rule")
          , ("Lebanese_Christians", "Christianity_in_Lebanon")
          , ("Lebron_James", "LeBron_James")
          , ("Ledger_%28software%29", "Ledger_(software)")
          , ("Left-to-right", "Writing_system#Directionality")
          , ("Left-wing_authoritarianism", "Right-wing_authoritarian_personality#Left-wing_authoritarians")
          , ("Left_brain", "Lateralization_of_brain_function")
          , ("Legacy_code", "Legacy_system")
          , ("Legendarium", "Tolkien%27s_legendarium")
          , ("Lehman_Brothers_bankruptcy", "Bankruptcy_of_Lehman_Brothers")
          , ("Lelystad,_Netherlands", "Lelystad")
          , ("Lemon_market", "The_Market_for_Lemons")
          , ("Lemongrass", "Cymbopogon")
          , ("Lempel-Ziv-Markov_chain_algorithm", "Lempel%E2%80%93Ziv%E2%80%93Markov_chain_algorithm")
          , ("Lemurs", "Lemur")
          , ("Lenin", "Vladimir_Lenin")
          , ("Lensman", "Lensman_series")
          , ("Lensman_%28anime%29", "Galactic_Patrol_Lensman")
          , ("Lensman_series_%28anime%29", "Lensman_series_(anime)")
          , ("Lenticular_clouds", "Lenticular_cloud")
          , ("Lentiviral", "Lentivirus")
          , ("Leonard_J._Savage", "Leonard_Jimmie_Savage")
          , ("Leopard_print", "Animal_print")
          , ("Leopards", "Leopard")
          , ("Leopardus_pardalis", "Ocelot")
          , ("Leopardus_wiedii", "Margay")
          , ("Leptailurus_serval", "Serval")
          , ("Lesch-Nyhan_syndrome", "Lesch%E2%80%93Nyhan_syndrome")
          , ("Let's_Encrypt", "Let%27s_Encrypt")
          , ("Leto_I_Atreides", "List_of_Dune_secondary_characters#Leto_I_Atreides")
          , ("Letzte_Generation", "Last_Generation_(climate_movement)")
          , ("Level_cap", "Experience_point#Level-based_progression")
          , ("Lewontin%27s_fallacy", "Human_Genetic_Diversity:_Lewontin%27s_Fallacy")
          , ("Lewontin's_fallacy", "Human_Genetic_Diversity:_Lewontin%27s_Fallacy")
          , ("Lewy_body_disease", "Lewy_body_dementias")
          , ("Lexically_scoped", "Scope_(computer_science)#Lexical_scope")
          , ("LiDAR", "Lidar")
          , ("Liability-threshold_model", "Threshold_model#Liability_threshold_model")
          , ("Liability_scale_threshold_model", "Threshold_model#Liability_threshold_model")
          , ("Liability_threshold", "Threshold_model#Liability_threshold_model")
          , ("Liability_threshold_model", "Threshold_model#Liability_threshold_model")
          , ("Liar's_dice", "Liar%27s_dice")
          , ("Liberal_capitalism", "Economic_liberalism")
          , ("Liebeck_v._McDonald's_Restaurants", "Liebeck_v._McDonald%27s_Restaurants")
          , ("Liebig's_law_of_the_minimum", "Liebig%27s_law_of_the_minimum")
          , ("Life_tables", "Life_table")
          , ("Ligand_%28biochemistry%29", "Ligand_(biochemistry)")
          , ("Light_%28novel%29", "Light_(novel)")
          , ("Light_microscopy", "Microscopy#Optical_microscopy")
          , ("Lighthill_Report", "Lighthill_report")
          , ("Lightsaber_combat", "index.php?title=Lightsaber_combat&oldid=220748376")
          , ("Likelihood", "Likelihood_function")
          , ("Likelihood_ratio", "Likelihood_function#Likelihood_ratio")
          , ("Likert-type_scale", "Likert_scale")
          , ("Lila_Gleitman", "Lila_R._Gleitman")
          , ("Limits_to_computation", "Limits_of_computation")
          , ("Lincoln_administration", "Presidency_of_Abraham_Lincoln")
          , ("Line_breaking", "Line_wrap_and_word_wrap")
          , ("Linear_feedback_shift_register", "Linear-feedback_shift_register")
          , ("Linear_functions", "Linear_function")
          , ("Linear_models", "Linear_model")
          , ("Lines_of_code", "Source_lines_of_code")
          , ("Linkbacks", "Linkback")
          , ("Lions", "Lion")
          , ("Lip_read", "Lip_reading")
          , ("Lipid_oxidation", "Lipid_peroxidation")
          , ("Lipopolysaccharides", "Lipopolysaccharide")
          , ("Liquid_chromatography", "Chromatography")
          , ("Liquid_diets", "Liquid_diet")
          , ("Liquidus", "Liquidus_and_solidus")
          , ("Lisdexamphetamine", "Lisdexamfetamine")
          , ("Lisp_Machine", "Lisp_machine")
          , ("Lisp_machines", "Lisp_machine")
          , ("List_experiment", "Preference_falsification")
          , ("List_of_Dune_secondary_characters", "List_of_Dune_characters")
          , ("List_of_SJS_inducing_substances", "List_of_SJS-inducing_substances")
          , ("List_of_converts_to_Christianity_from_atheism", "List_of_converts_to_Christianity_from_nontheism")
          , ("List_of_countries_by_number_of_troops", "List_of_countries_by_number_of_military_and_paramilitary_personnel")
          , ("List_of_disproved_mathematical_ideas", "List_of_conjectures#Disproved_(no_longer_conjectures)")
          , ("List_of_mass_murderers_and_spree_killers_by_number_of_victims", "List_of_rampage_killers")
          , ("List_of_mergers_and_acquisitions_by_Facebook", "List_of_mergers_and_acquisitions_by_Meta_Platforms")
          , ("List_of_mergers_and_acquisitions_by_Google", "List_of_mergers_and_acquisitions_by_Alphabet")
          , ("List_of_oldest_madrasahs_in_continuous_operation", "List_of_Islamic_seminaries#List_of_oldest_Islamic_seminaries")
          , ("List_of_rasa'il_in_the_Encyclopedia_of_the_Brethren_of_Purity", "List_of_rasa%27il_in_the_Encyclopedia_of_the_Brethren_of_Purity")
          , ("List_of_rulers_of_Milan", "List_of_dukes_of_Milan")
          , ("List_of_the_oldest_living_people", "List_of_oldest_living_people")
          , ("Literary_scholarship", "Literary_theory")
          , ("Lithium_deuteride", "Lithium_hydride#Lithium_deuteride")
          , ("Lithium_pharmacology", "Lithium_(medication)")
          , ("Lithoprinted", "Offset_printing")
          , ("Litmus_paper", "Litmus")
          , ("Little's_law", "Little%27s_law")
          , ("Little_Nicky_%28cat%29", "Little_Nicky_(cat)")
          , ("Little_Somalia", "Cedar-Riverside,_Minneapolis")
          , ("Littlewood%27s_Law", "Littlewood%27s_law")
          , ("Littlewood's_Law", "Littlewood%27s_law")
          , ("Littlewood's_law", "Littlewood%27s_law")
          , ("Livejournal", "LiveJournal")
          , ("Livestreaming", "Live_streaming")
          , ("Lloyd's_of_London", "Lloyd%27s_of_London")
          , ("Local_Average_Treatment_Effect", "Local_average_treatment_effect")
          , ("Locality_Sensitive_Hashing", "Locality-sensitive_hashing")
          , ("Locus_Online", "Locus_(magazine)#Locus_Online")
          , ("Log-likelihood", "Likelihood_function#Log-likelihood")
          , ("Log-normal", "Log-normal_distribution")
          , ("Log_normal_distribution", "Log-normal_distribution")
          , ("Log_odds", "Logit")
          , ("Logical_Atomism", "Logical_atomism")
          , ("Logical_validity", "Validity_(logic)")
          , ("Lolita_complex", "Lolicon")
          , ("Lone_wolf_(terrorism)", "Lone_wolf_attack")
          , ("Long-term_effects_of_alcohol_consumption", "Long-term_effects_of_alcohol")
          , ("Long-time_nuclear_waste_warning_messages", "Long-term_nuclear_waste_warning_messages")
          , ("Long_Bets", "Long_Now_Foundation#Long_Bet_Project")
          , ("Long_Now", "Long_Now_Foundation")
          , ("Long_Now_Clock", "Clock_of_the_Long_Now")
          , ("Long_covid", "Long_COVID")
          , ("Long_line_%28topology%29", "Long_line_(topology)")
          , ("Long_short_term_memory", "Long_short-term_memory")
          , ("Longitudinal_analysis", "Longitudinal_study")
          , ("Loopy_belief_propagation", "Belief_propagation#Approximate_algorithm_for_general_graphs")
          , ("Lord's_paradox", "Lord%27s_paradox")
          , ("Lord_Nelson", "Horatio_Nelson,_1st_Viscount_Nelson")
          , ("Lord_of_the_Rings", "The_Lord_of_the_Rings")
          , ("Lorena_Bobbitt", "John_and_Lorena_Bobbitt")
          , ("Los_Alamos_Scientific_Laboratory", "Los_Alamos_National_Laboratory")
          , ("Loss-of-function_mutation", "Mutation#By_effect_on_function")
          , ("Lost_Decade_(Japan)", "Lost_Decades")
          , ("Lotka's_law", "Lotka%27s_law")
          , ("Lotka-Volterra_equation", "Lotka%E2%80%93Volterra_equations")
          , ("Louis_V._Gerstner_Junior", "Lou_Gerstner")
          , ("Louvain", "Leuven")
          , ("Love_&_Pop", "Love_%26_Pop")
          , ("Love_and_Pop", "Love_%26_Pop")
          , ("Loving-kindness_meditation", "Maitr%C4%AB")
          , ("Low-density_lipoprotein_cholesterol", "Low-density_lipoprotein")
          , ("Low_level_laser_therapy", "Low-level_laser_therapy")
          , ("Loxodonta_africana", "African_bush_elephant")
          , ("Loxodonta_cyclotis", "African_forest_elephant")
          , ("Loyalist_%28American_Revolution%29", "Loyalist_(American_Revolution)")
          , ("Luc_Sante", "Lucy_Sante")
          , ("LucasArts", "Lucasfilm_Games")
          , ("Lucid_dreaming", "Lucid_dream")
          , ("Lucid_dreams", "Lucid_dream")
          , ("Luddite_fallacy", "Technological_unemployment#The_Luddite_fallacy")
          , ("Lufthansa_Heist", "Lufthansa_heist")
          , ("Luis_Alvarez", "Luis_Walter_Alvarez")
          , ("Luis_Urrea", "Luis_Alberto_Urrea")
          , ("Luke_(name)", "Luke_(given_name)")
          , ("Lumbar_disc_herniation", "Spinal_disc_herniation#Lumbar_disc_herniation")
          , ("Lunar_Lander_(video_game)", "Lunar_Lander_(video_game_genre)")
          , ("Lupan_Sansei", "Lupin_the_Third")
          , ("Lycaon_pictus", "African_wild_dog")
          , ("Lyon_Playfair", "Lyon_Playfair,_1st_Baron_Playfair")
          , ("Lysergic_acid_diethylamide", "LSD")
          , ("Lysogeny", "Lysogenic_cycle")
          , ("M%C4%81maki", "Pipturus_albidus")
          , ("M%E1%BB%B9_Lai_massacre", "My_Lai_massacre")
          , ("M.C._Escher", "M._C._Escher")
          , ("M.I.T", "Massachusetts_Institute_of_Technology")
          , ("M._abscessus", "Mycobacteroides_abscessus")
          , ("M._mimicus", "Myrmecocystus_mimicus")
          , ("MAML3", "Mastermind-like_3_(drosophila)")
          , ("MANOVA", "Multivariate_analysis_of_variance")
          , ("MATE", "MATE_(desktop_environment)")
          , ("MATE_(software)", "MATE_(desktop_environment)")
          , ("MBTA", "Massachusetts_Bay_Transportation_Authority")
          , ("MC1R", "Melanocortin_1_receptor")
          , ("MENSA", "Mensa_International")
          , ("METAFONT", "Metafont")
          , ("MINIX", "Minix")
          , ("MIRV", "Multiple_independently_targetable_reentry_vehicle")
          , ("MIRVs", "Multiple_independently_targetable_reentry_vehicle")
          , ("MIT", "Massachusetts_Institute_of_Technology")
          , ("MITRE", "Mitre_Corporation")
          , ("MMORPG", "Massively_multiplayer_online_role-playing_game")
          , ("MMORPGs", "Massively_multiplayer_online_role-playing_game")
          , ("MOOs", "MOO")
          , ("MOV_(x86_instruction)", "X86_instruction_listings#Original_8086/8088_instructions")
          , ("MRE11", "MRE11A")
          , ("MRNA-lipid_nanoparticle", "Solid_lipid_nanoparticle#Nucleic_acids")
          , ("MS%20COCO", "MS_COCO")
          , ("MSTN", "Myostatin")
          , ("MTOR_inhibitor", "MTOR_inhibitors")
          , ("MUD", "Multi-user_dungeon")
          , ("MUDs", "Multi-user_dungeon")
          , ("MUJI", "Muji")
          , ("MULTICS", "Multics")
          , ("MYCIN", "Mycin")
          , ("Mac_LC_II", "Macintosh_LC_II")
          , ("Maca_root", "Lepidium_meyenii#Root")
          , ("Macaca_fascicularis", "Crab-eating_macaque")
          , ("Mach_%28kernel%29", "Mach_(kernel)")
          , ("Machiavelli", "Niccol%C3%B2_Machiavelli")
          , ("Machiavellian", "Machiavellianism_(psychology)")
          , ("Machiavellianism_%28psychology%29", "Machiavellianism_(psychology)")
          , ("Macintosh", "Mac_(computer)")
          , ("Macro_language", "Macro_(computer_science)")
          , ("Macrophages", "Macrophage")
          , ("Macross:_Do_You_Remember_Love", "Macross:_Do_You_Remember_Love%3F")
          , ("Macross:_Do_You_Remember_Love?", "Macross:_Do_You_Remember_Love%3F")
          , ("Madder_plant", "Rubia")
          , ("Madeleine_Wickham", "Sophie_Kinsella")
          , ("Madoka_Magika", "Puella_Magi_Madoka_Magica")
          , ("Magic:_the_Gathering", "Magic:_The_Gathering")
          , ("Magic_mushrooms", "Psilocybin_mushroom")
          , ("Magic_tricks", "Magic_(illusion)#Magic_tricks")
          , ("Magma_Taishi", "Ambassador_Magma")
          , ("Magnesium_l-threonate", "Magnesium_L-threonate")
          , ("Magnetic_fields_in_the_sun", "Stellar_magnetic_field")
          , ("Magnetic_resonance_spectroscopy", "Nuclear_magnetic_resonance_spectroscopy")
          , ("Magnus_Carlsen_Chess_Tour", "Magnus_Carlsen_Chess_Tour_2020")
          , ("Mahou_Shoujo_Nanoha", "Magical_Girl_Lyrical_Nanoha_(series)")
          , ("Mahou_shoujo", "Magical_girl")
          , ("Major-General's_Song", "Major-General%27s_Song")
          , ("Major_Depressive_Disorder", "Major_depressive_disorder")
          , ("Major_depression", "Major_depressive_disorder")
          , ("Majority_vote", "Majority#Majority_vote")
          , ("Makefiles", "Make_(software)#Makefile")
          , ("Makyo", "Maky%C5%8D")
          , ("Malagasy_people", "Malagasy_peoples")
          , ("Male_circumcision", "Circumcision")
          , ("Malinois_dog", "Belgian_Shepherd")
          , ("Malloc", "C_dynamic_memory_allocation")
          , ("Malthusian_theory", "Malthusianism")
          , ("Malthusian_trap", "Malthusianism")
          , ("Mammalian_evolution", "Evolution_of_mammals")
          , ("Man'y%C5%8Dsh%C5%AB", "Man%27y%C5%8Dsh%C5%AB")
          , ("Man'yoshu", "Man%27y%C5%8Dsh%C5%AB")
          , ("Man_With_No_Name", "Man_with_No_Name")
          , ("Man_in_the_middle_attack", "Man-in-the-middle_attack")
          , ("Mandela_effect", "False_memory#Mandela_effect")
          , ("Mangaka", "Manga_artist")
          , ("Mann's_Chinese_Theatre", "Grauman%27s_Chinese_Theatre")
          , ("Mann-Whitney_U", "Mann%E2%80%93Whitney_U_test")
          , ("Manual_typewriter", "Typewriter")
          , ("Many-Worlds_interpretation", "Many-worlds_interpretation")
          , ("Many_Worlds_Interpretation", "Many-worlds_interpretation")
          , ("Mapudungun", "Mapuche_language")
          , ("Marchetti's_constant", "Marchetti%27s_constant")
          , ("Marcus_Claudius_Marcellus_(Julio-Claudian_dynasty)", "Marcellus_(nephew_of_Augustus)")
          , ("Mari_Makinami", "Mari_Illustrious_Makinami")
          , ("Marie_Celeste", "Mary_Celeste")
          , ("Marijuana", "Cannabis_(drug)")
          , ("Mark_Jason_Dominus", "Higher-Order_Perl")
          , ("Mark_Ziesing", "Mark_V._Ziesing")
          , ("Markov_Decision_Processes", "Markov_decision_process")
          , ("Markov_chains", "Markov_chain")
          , ("Markov_decision_processes", "Markov_decision_process")
          , ("Markov_process", "Markov_chain")
          , ("Marshall_plan", "Marshall_Plan")
          , ("Marshmallow_test", "Stanford_marshmallow_experiment")
          , ("Martin_A._Nowak", "Martin_Nowak")
          , ("Martingale_%28betting_system%29", "Martingale_(betting_system)")
          , ("Marty_%28film%29", "Marty_(film)")
          , ("Mass_shootings", "Mass_shooting")
          , ("Mass_spectrometer", "Mass_spectrometry")
          , ("Mass_spectroscopy", "Mass_spectrometry")
          , ("Massa_intermedia", "Interthalamic_adhesion")
          , ("Massachusetts_General_Research_Institute", "Massachusetts_General_Hospital#The_Mass_General_Research_Institute_(MGRI)")
          , ("Mastodons", "Mastodon")
          , ("Mat%C3%A9", "Mate_(drink)")
          , ("Match_3", "Tile-matching_video_game")
          , ("Matching_gift", "Matching_funds")
          , ("Material_adverse_effect", "Material_adverse_change")
          , ("Mathematica", "Wolfram_Mathematica")
          , ("Mathematical_Intelligencer", "The_Mathematical_Intelligencer")
          , ("Mathematical_Sciences_Research_Institute", "Simons_Laufer_Mathematical_Sciences_Institute")
          , ("Mathematical_biology", "Mathematical_and_theoretical_biology")
          , ("Mating_(human)", "Human_mating_strategies")
          , ("Matlab", "MATLAB")
          , ("Matrix_factorization", "Matrix_decomposition")
          , ("Matrix_inversion", "Invertible_matrix#Methods_of_matrix_inversion")
          , ("Matrix_multiplications", "Matrix_multiplication")
          , ("Matroid_theory", "Matroid")
          , ("Maude_Flanders", "List_of_recurring_The_Simpsons_characters#Maude_Flanders")
          , ("Mawaru_Penguindrum", "Penguindrum")
          , ("Max_V._Mathews", "Max_Mathews")
          , ("Maximum_likelihood", "Maximum_likelihood_estimation")
          , ("Maxwell%27s_Equations", "Maxwell%27s_equations")
          , ("Maxwell's_Equations", "Maxwell%27s_equations")
          , ("Maxwell's_equations", "Maxwell%27s_equations")
          , ("Mazer_Rackham", "List_of_Ender%27s_Game_characters#Mazer")
          , ("Mazinger-Z", "Mazinger_Z")
          , ("Mazurkas", "Mazurka")
          , ("McDonald's_Premium_line", "McDonald%27s_Premium_line")
          , ("MeV", "Electronvolt")
          , ("Measure_theory", "Measure_(mathematics)")
          , ("Measurement_error", "Observational_error")
          , ("Meat_pies", "Meat_pie")
          , ("Mecha_anime", "Mecha_anime_and_manga")
          , ("Mechanical_keyboard", "Keyboard_technology#Discrete-switch_keyboard")
          , ("Mechanistic_target_of_rapamycin", "MTOR")
          , ("Media_Matters", "Media_Matters_for_America")
          , ("Medial_temporal_lobe", "Temporal_lobe")
          , ("Mediation_%28statistics%29", "Mediation_(statistics)")
          , ("Mediation_analysis", "Mediation_(statistics)")
          , ("Medical_students'_disease", "Medical_students%27_disease")
          , ("Medici_bank", "Medici_Bank")
          , ("Medici_family", "House_of_Medici")
          , ("Mee_krob", "Mi_krop")
          , ("Megaviridae", "Mimiviridae")
          , ("Meguro,_Tokyo", "Meguro")
          , ("Meiji_restoration", "Meiji_Restoration")
          , ("Meituan-Dianping", "Meituan")
          , ("Melanocyte-stimulating_hormones", "Melanocyte-stimulating_hormone")
          , ("Melanogenesis", "Melanocyte#Melanogenesis")
          , ("Melanomas", "Melanoma")
          , ("Mellanox", "Mellanox_Technologies")
          , ("Memes", "Meme")
          , ("Memoize", "Memoization")
          , ("Memory_bound_function", "Memory-bound_function")
          , ("Memory_palace", "Method_of_loci")
          , ("Memory_reconsolidation", "Memory_consolidation")
          , ("Mendelian_Randomization", "Mendelian_randomization")
          , ("Mendelian_genetics", "Mendelian_inheritance")
          , ("Mental_effort", "Cognitive_load")
          , ("Meowing", "Meow")
          , ("Mere_exposure_effect", "Mere-exposure_effect")
          , ("Merkle's_Puzzles", "Merkle%27s_Puzzles")
          , ("Merkle-Hellman_knapsack_cryptosystem", "Merkle%E2%80%93Hellman_knapsack_cryptosystem")
          , ("Merovingian_period", "Merovingian_dynasty")
          , ("Mesenteries", "Mesentery_(zoology)")
          , ("Messenger_RNAs", "Messenger_RNA")
          , ("Meta-circular_interpreter", "Meta-circular_evaluator")
          , ("Meta_learning_(computer_science)", "Meta-learning_(computer_science)")
          , ("Metal_ion", "Metal")
          , ("Metanephros", "Kidney_development")
          , ("Metasploit_Project", "Metasploit")
          , ("Metastable", "Metastability")
          , ("Metazoa", "Animal")
          , ("Metcalfe's_Law", "Metcalfe%27s_law")
          , ("Metcalfe's_law", "Metcalfe%27s_law")
          , ("Metcalfe’s_Law", "Metcalfe%E2%80%99s_Law")
          , ("Metcalfe’s_law", "Metcalfe%E2%80%99s_law")
          , ("Methodist", "Methodism")
          , ("Methuselah_Mouse_Prize", "Methuselah_Foundation#Methuselah_Mouse_Prize")
          , ("Methyl_iodide", "Iodomethane")
          , ("Methylenedioxymethamphetamine", "MDMA")
          , ("Metric_learning", "Similarity_learning#Metric_learning")
          , ("Mhorr_gazelles", "Dama_gazelle")
          , ("Michiko_to_Hatchin", "Michiko_%26_Hatchin")
          , ("Microbiomes", "Microbiome")
          , ("Microbrews", "Craft_beer")
          , ("Microcomputed_tomography", "X-ray_microtomography")
          , ("Microfilm", "Microform")
          , ("Microfluidic", "Microfluidics")
          , ("Microrobotics", "Microbotics")
          , ("Microsoft's_Bing", "Microsoft%27s_Bing")
          , ("Microsoft_Encarta", "Encarta")
          , ("Microtubule_motors", "Motor_protein#Microtubule_motors")
          , ("Microvasculature", "Microcirculation#Microvessels")
          , ("Mighty_Morphin%27_Power_Rangers", "Mighty_Morphin_Power_Rangers")
          , ("Mighty_Morphin'_Power_Rangers", "Mighty_Morphin_Power_Rangers")
          , ("Mild_Seven", "Mevius")
          , ("Miles_Albert_Tinker", "Miles_Tinker")
          , ("Miles_per_gallon", "Fuel_economy_in_automobiles")
          , ("Military_engineer", "Military_engineering")
          , ("Military_intervention", "Interventionism_(politics)")
          , ("Military_use_of_children", "Children_in_the_military")
          , ("Milky_Way_Galaxy", "Milky_Way")
          , ("Millennium_Dome_Raid", "Millennium_Dome_raid")
          , ("Millennium_Pharmaceuticals", "Takeda_Oncology")
          , ("Millennium_Problems", "Millennium_Prize_Problems")
          , ("Mills_College", "Mills_College_at_Northeastern_University")
          , ("Mind-body_practices", "Mind%E2%80%93body_interventions")
          , ("Mini%E2%80%93Mental_State_Examination", "Mini%E2%80%93mental_state_examination")
          , ("Mini-Mental_State_Examination", "Mini%E2%80%93mental_state_examination")
          , ("Miniature_dachshund", "Dachshund")
          , ("Minimal_cell", "Artificial_cell#The_minimal_cell")
          , ("Minimum_spanning_trees", "Minimum_spanning_tree")
          , ("Ministry_of_Health,_Labor,_and_Welfare", "Ministry_of_Health,_Labour_and_Welfare")
          , ("Minnesota_Study_of_Twins_Reared_Apart", "Minnesota_Center_for_Twin_and_Family_Research")
          , ("Minovsky_physics", "Gundam_Universal_Century_technology#Minovsky_physics")
          , ("Miracles", "Miracle")
          , ("Mirai_Nikki", "Future_Diary")
          , ("Mirai_Robo_Daltanious", "Future_Robot_Daltanious")
          , ("Mirai_Shonen_Conan", "Future_Boy_Conan")
          , ("Mirror_box", "Mirror_therapy")
          , ("Mirror_self-recognition", "Mirror_test")
          , ("Mirrored_self-misidentification", "Mirrored-self_misidentification")
          , ("Missing_heritability", "Missing_heritability_problem")
          , ("Mistborn_series", "Mistborn")
          , ("Mitchell_Kapor", "Mitch_Kapor")
          , ("Mitochondria", "Mitochondrion")
          , ("Mitospore", "Conidium")
          , ("Mixed_integer_programming", "Linear_programming#Integer_unknowns")
          , ("Mixed_integer_programs", "Linear_programming#Integer_unknowns")
          , ("Mizuki_Ashiya", "List_of_Hana-Kimi_characters")
          , ("Mobbing_%28animal_behavior%29", "Mobbing_(animal_behavior)")
          , ("Mobile_Police_Patlabor", "Patlabor")
          , ("Mobile_Suit_Gundam:_Char's_Counterattack", "Mobile_Suit_Gundam:_Char%27s_Counterattack")
          , ("Moby_Dick", "Moby-Dick")
          , ("Mod_%28computer_gaming%29", "Video_game_modding")
          , ("Mod_(video_games)", "Video_game_modding")
          , ("Model_3", "Tesla_Model_3")
          , ("Moebius_strip", "M%C3%B6bius_strip")
          , ("Moguera", "The_Mysterians")
          , ("Mohammad_bin_Salman", "Mohammed_bin_Salman")
          , ("Mohammed_Taheri-Azar", "2006_UNC_SUV_attack")
          , ("Mohammed_bin_Zayed_Al_Nahyan", "Mohamed_bin_Zayed_Al_Nahyan")
          , ("MojoNation", "https://web.archive.org/web/**************/https://en.wikipedia.org/wiki/Mnet_(peer-to-peer_network)")
          , ("Molecular_Point_Groups", "Molecular_symmetry")
          , ("Molecular_weight", "Molecular_mass")
          , ("Moleskines", "Moleskine")
          , ("Monaco:_What's_Yours_Is_Mine", "Monaco:_What%27s_Yours_Is_Mine")
          , ("Monkeypox", "Mpox")
          , ("Monoamine_oxidase_inhibitors", "Monoamine_oxidase_inhibitor")
          , ("Monoclonal_antibodies", "Monoclonal_antibody")
          , ("Monoid_factorization", "Monoid_factorisation")
          , ("Monoids", "Monoid")
          , ("Monolith_%28Space_Odyssey%29", "Monolith_(Space_Odyssey)")
          , ("Monoterpenes", "Monoterpene")
          , ("Monotype_Imaging_Holdings_Inc.", "Monotype_Imaging")
          , ("Monozygotic", "Twin#Monozygotic_(identical)_twins")
          , ("Monozygotic_twins", "Twin#Monozygotic_(identical)_twins")
          , ("Monte-Carlo_Tree_Search", "Monte_Carlo_tree_search")
          , ("Montezuma%27s_Revenge", "Travelers%27_diarrhea")
          , ("Montezuma's_Revenge_(video_game)", "Montezuma%27s_Revenge_(video_game)")
          , ("Monty_Hall_paradox", "Monty_Hall_problem")
          , ("Monty_Python's_Life_of_Brian", "Monty_Python%27s_Life_of_Brian")
          , ("Moon_rocks", "Moon_rock")
          , ("Moonie", "Unification_Church_of_the_United_States")
          , ("Moonwalking_With_Einstein", "Moonwalking_with_Einstein")
          , ("Moore%27s_Law", "Moore%27s_law")
          , ("Moore's_Law", "Moore%27s_law")
          , ("Moore's_law", "Moore%27s_law")
          , ("Moore’s_Law", "Moore%27s_law")
          , ("Moore’s_law", "Moore%27s_law")
          , ("Moral_Foundations_Theory", "Moral_foundations_theory")
          , ("Moral_judgment", "Moral_reasoning")
          , ("Moral_rights_(copyright_law)", "Moral_rights")
          , ("Moravec's_paradox", "Moravec%27s_paradox")
          , ("Morlocks", "Morlock")
          , ("Mormon", "Mormons")
          , ("Mormon_(Book_of_Mormon)", "Mormon_(Book_of_Mormon_prophet)")
          , ("Morris_water_maze", "Morris_water_navigation_task")
          , ("Mostly_Mozart", "Mostly_Mozart_Festival")
          , ("Moth_crystals", "Mothball")
          , ("Mother_Hitton's_Littul_Kittons", "Mother_Hitton%27s_Littul_Kittons")
          , ("Mother_Theresa", "Mother_Teresa")
          , ("Mountain_Pass_rare_earth_mine", "Mountain_Pass_mine")
          , ("Mouse_model", "Model_organism")
          , ("Mozart", "Wolfgang_Amadeus_Mozart")
          , ("Mr._Peepers", "Mister_Peepers")
          , ("Mr_Smith_Goes_to_Washington", "Mr._Smith_Goes_to_Washington")
          , ("Ms._Pacman", "Ms._Pac-Man")
          , ("Mt._Hiei", "Mount_Hiei")
          , ("Mt._Mihara", "Mount_Mihara")
          , ("MtGox", "Mt._Gox")
          , ("Mt_Fuji", "Mount_Fuji")
          , ("Mu%E1%B8%A5ammad_ibn_M%C5%ABs%C4%81_al-Khw%C4%81rizm%C4%AB", "Al-Khwarizmi")
          , ("Muawiyah_I", "Mu%27awiya_I")
          , ("Muhammad_ibn_Musa_al-Khwarizmi", "Al-Khwarizmi")
          , ("Multi-armed_bandit_problem", "Multi-armed_bandit")
          , ("Multi-armed_bandits", "Multi-armed_bandit")
          , ("Multi-level_model", "Multilevel_model")
          , ("Multi-level_selection", "Group_selection#Multilevel_selection_theory")
          , ("Multilevel_analyses", "Multilevel_model")
          , ("Multilevel_marketing_scheme", "Multi-level_marketing")
          , ("Multilevel_models", "Multilevel_model")
          , ("Multipartite_viruses", "Multipartite")
          , ("Multiple_comparisons", "Multiple_comparisons_problem")
          , ("Multiple_imputation", "Imputation_(statistics)")
          , ("Multiple_regression", "Regression_analysis#General_linear_model")
          , ("Multiply-accumulate_operation", "Multiply%E2%80%93accumulate_operation")
          , ("Multiply-accumulate_operations", "Multiply%E2%80%93accumulate_operation")
          , ("Multisig", "Cryptocurrency_wallet#Multisignature_wallet")
          , ("Multisignature", "Cryptocurrency_wallet#Multisignature_wallet")
          , ("Multistate_Bar_Exam", "Bar_examination_in_the_United_States#Multistate_Bar_Examination_(MBE)")
          , ("Multivariate_linear_model", "General_linear_model")
          , ("Multivariate_regression", "General_linear_model")
          , ("Multivitamin_supplement", "Multivitamin")
          , ("Multivitamins", "Multivitamin")
          , ("Munchausen_syndrome", "Factitious_disorder_imposed_on_self")
          , ("Munchausen_syndrome_by_proxy", "Factitious_disorder_imposed_on_another")
          , ("Muscular_dystrophies", "Muscular_dystrophy")
          , ("Muscular_hypermyotrophy", "Myostatin-related_muscle_hypertrophy")
          , ("Mushi_Pro", "Mushi_Production")
          , ("Mushroom_body", "Mushroom_bodies")
          , ("Music_streaming", "Music_streaming_service")
          , ("Musical_Offering", "The_Musical_Offering")
          , ("Musk-ox", "Muskox")
          , ("Muslim", "Muslims")
          , ("Musophagiformes", "Turaco")
          , ("Mustard_greens", "Brassica_juncea")
          , ("Mutation_accumulation", "Evolution_of_ageing#Mutation_accumulation")
          , ("Mutation_load", "Genetic_load")
          , ("Mutational_load", "Genetic_load#Mutational_load")
          , ("Mutineers'_Moon", "Mutineers%27_Moon")
          , ("Mutually_Assured_Destruction", "Mutual_assured_destruction")
          , ("Muv-Luv_Alternative", "Muv-Luv")
          , ("My_Bride_Is_A_Mermaid", "My_Bride_Is_a_Mermaid")
          , ("My_Bride_is_a_Mermaid", "My_Bride_Is_a_Mermaid")
          , ("My_Life_As_A_Teenage_Robot", "My_Life_as_a_Teenage_Robot")
          , ("My_Little_Pony:_Friendship_is_Magic", "My_Little_Pony:_Friendship_Is_Magic")
          , ("Mycorrhizal_fungi", "Mycorrhiza")
          , ("Mycosed", "Fungal_infection")
          , ("Myelination", "Myelin")
          , ("Myeloproliferative_neoplasms", "Myeloproliferative_neoplasm")
          , ("Myers-Briggs_Type_Indicator", "Myers%E2%80%93Briggs_Type_Indicator")
          , ("Myocyte", "Muscle_cell")
          , ("Mzoli's", "Mzoli%27s")
          , ("N-acetyl_cysteine", "Acetylcysteine")
          , ("N-gram_language_model", "Word_n-gram_language_model")
          , ("N-grams", "N-gram")
          , ("N-rays", "N-ray")
          , ("NADH", "Nicotinamide_adenine_dinucleotide")
          , ("NADPH", "Nicotinamide_adenine_dinucleotide_phosphate")
          , ("NBA", "National_Basketball_Association")
          , ("NCAM1", "Neural_cell_adhesion_molecule")
          , ("NEJM", "The_New_England_Journal_of_Medicine")
          , ("NEO-PI-R", "Revised_NEO_Personality_Inventory")
          , ("NES_Sound_Format", "Nintendo_Entertainment_System")
          , ("NHANES", "National_Health_and_Nutrition_Examination_Survey")
          , ("NLSY79", "National_Longitudinal_Surveys#NLSY79")
          , ("NLSY97", "National_Longitudinal_Surveys#NLSY97")
          , ("NMDAR", "NMDA_receptor")
          , ("NOS3", "Endothelial_NOS")
          , ("NP-complete", "NP-completeness")
          , ("NP-hard", "NP-hardness")
          , ("NR2B", "GRIN2B")
          , ("NREM", "Non-rapid_eye_movement_sleep")
          , ("NSA", "National_Security_Agency")
          , ("NUNAOIL", "Nunaoil")
          , ("NVIDIA", "Nvidia")
          , ("NVIDIA_RTX_3090", "GeForce_30_series")
          , ("NVIDIA_Titan_X_GPU", "GeForce_900_series")
          , ("NYSE", "New_York_Stock_Exchange")
          , ("NYU", "New_York_University")
          , ("N_ray", "N-ray")
          , ("NaNoWriMo", "National_Novel_Writing_Month")
          , ("NaOH", "Sodium_hydroxide")
          , ("Nadia:_The_Secret_of_Blue_Water_%28video_game%29", "Nadia:_The_Secret_of_Blue_Water_(video_game)")
          , ("Nadia_gekijoban", "Nadia:_The_Secret_of_Blue_Water#Production")
          , ("Nagisa_Oshima", "Nagisa_%C5%8Cshima")
          , ("Nails", "Nail_(anatomy)")
          , ("Nairobi,_Kenya", "Nairobi")
          , ("Naive_Bayes", "Naive_Bayes_classifier")
          , ("Naked_mole_rats", "Naked_mole-rat")
          , ("Named_entity_recognition", "Named-entity_recognition")
          , ("Nancy_C._Andreasen", "Nancy_Coover_Andreasen")
          , ("Napa_Valley", "Napa_Valley_AVA")
          , ("Naphta", "Naphtha")
          , ("Napoleon_Bonaparte", "Napoleon")
          , ("Narcissist", "Narcissism")
          , ("Narcissistic", "Narcissism")
          , ("Narcissistic_rage_and_narcissistic_injury", "Narcissistic_injury")
          , ("Narcissists", "Narcissism")
          , ("Narita_airport", "Narita_International_Airport")
          , ("Narutaru", "Shadow_Star")
          , ("Nasal_dilation_cycle", "Nasal_cycle")
          , ("Nash_equilibriums", "Nash_equilibrium")
          , ("Nat_Turner", "Nat_Turner%27s_slave_rebellion")
          , ("Natalya_Poklonskaya", "Natalia_Poklonskaya")
          , ("National_College_Entrance_Examination", "Gaokao")
          , ("National_Forest_Service", "United_States_Forest_Service")
          , ("National_Health_Insurance_(Taiwan)", "Healthcare_in_Taiwan")
          , ("National_Inquirer", "National_Enquirer")
          , ("National_Longitudinal_Survey_of_Youth", "National_Longitudinal_Surveys")
          , ("National_Security_Advisor", "National_security_advisor")
          , ("National_Security_Commission_on_AI", "National_Security_Commission_on_Artificial_Intelligence")
          , ("National_Spelling_Bee", "Scripps_National_Spelling_Bee")
          , ("Natural_language_inference", "Textual_entailment")
          , ("Natural_language_understanding", "Natural-language_understanding")
          , ("Naturopathic", "Naturopathy")
          , ("Nausica%C3%A4_of_the_Valley_of_the_Wind_%28film%29", "Nausica%C3%A4_of_the_Valley_of_the_Wind_(film)")
          , ("Navier-Stokes_equations", "Navier%E2%80%93Stokes_equations")
          , ("Neal_Bush", "Neil_Bush")
          , ("Neanderthals", "Neanderthal")
          , ("Near-infrared_light", "Infrared#Regions_within_the_infrared")
          , ("Near_(Death_Note)", "List_of_Death_Note_characters#Near")
          , ("Nebraska_%28film%29", "Nebraska_(film)")
          , ("Nebula_Awards", "Nebula_Award")
          , ("Negative-sum_game", "Zero-sum_game")
          , ("Negative_binomial", "Negative_binomial_distribution")
          , ("Negative_externalities", "Externality#Negative")
          , ("Negative_externality", "Externality#Negative")
          , ("Neighborhood", "Neighbourhood")
          , ("Nelson's_Column", "Nelson%27s_Column")
          , ("Nematodes", "Nematode")
          , ("Neolithic_period", "Neolithic")
          , ("Neon_Genesis_Evangelion_%28TV%29", "Neon_Genesis_Evangelion")
          , ("Neon_Genesis_Evangelion_%28anime%29", "Neon_Genesis_Evangelion")
          , ("Neon_Genesis_Evangelion_(TV)", "Neon_Genesis_Evangelion")
          , ("Neon_Genesis_Evangelion_(anime)", "Neon_Genesis_Evangelion")
          , ("Neoplatonic", "Neoplatonism")
          , ("Neoscholasticism", "Neo-scholasticism")
          , ("Nepeta_cataria", "Catnip")
          , ("Nested_sampling", "Nested_sampling_algorithm")
          , ("Netboot", "Network_booting")
          , ("Nethack", "NetHack")
          , ("Netscape_IPO", "Netscape#Initial_public_offering_(IPO)")
          , ("Network_effects", "Network_effect")
          , ("Network_externalities", "Network_effect")
          , ("Network_protocol", "Communication_protocol")
          , ("NeurIPS", "Conference_on_Neural_Information_Processing_Systems")
          , ("Neural_Style_Transfer", "Neural_style_transfer")
          , ("Neural_nets", "Neural_network_(machine_learning)")
          , ("Neural_plasticity", "Neuroplasticity")
          , ("Neurathian_bootstrap", "Neurath%27s_boat")
          , ("Neurites", "Neurite")
          , ("Neurobiology", "Neuroscience")
          , ("Neurodegeneration", "Neurodegenerative_disease")
          , ("Neuroeconomic", "Neuroeconomics")
          , ("Neurofibromatosis_Type_1", "Neurofibromatosis_type_I")
          , ("Neurofilament_light_chain", "Neurofilament_light_polypeptide")
          , ("Neuroglia", "Glia")
          , ("Neuroleptics", "Antipsychotic")
          , ("Neuroprosthetic", "Neuroprosthetics")
          , ("Neutrinos", "Neutrino")
          , ("Neutron_stars", "Neutron_star")
          , ("Nevada_National_Security_Site", "Nevada_Test_Site")
          , ("New_America_Foundation", "New_America_(organization)")
          , ("New_Crobuzon", "Bas-Lag")
          , ("New_Economy", "New_economy")
          , ("New_England_Journal_of_Medicine", "The_New_England_Journal_of_Medicine")
          , ("New_Sincerity", "New_sincerity")
          , ("New_South_Wales%2C_Australia", "New_South_Wales")
          , ("New_World_vultures", "New_World_vulture")
          , ("New_York_City_mayoral_election,_2013", "2013_New_York_City_mayoral_election")
          , ("New_York_Magazine", "New_York_(magazine)")
          , ("New_York_Times", "The_New_York_Times")
          , ("Newcastle-Ottawa_Scale", "Newcastle%E2%80%93Ottawa_scale")
          , ("Newcomb%27s_dilemma", "Newcomb%27s_paradox")
          , ("Newcomb's_dilemma", "Newcomb%27s_paradox")
          , ("Newcomb's_paradox", "Newcomb%27s_paradox")
          , ("Newton's_method", "Newton%27s_method")
          , ("Newtype_(magazine)", "Newtype")
          , ("Nichi_nichi_kore_k%C5%8Dnichi", "Nichinichi_kore_k%C5%8Dnichi")
          , ("Nico_Nico_Douga", "Niconico")
          , ("Nidal_Hassan", "Nidal_Hasan")
          , ("Nidal_Malik_Hasan", "Nidal_Hasan")
          , ("Nielsen_ratings", "Nielsen_Media_Research#Nielsen_TV_ratings")
          , ("Niemann_Pick", "Niemann%E2%80%93Pick_disease")
          , ("Niftyserve", "CompuServe#File_transfers")
          , ("Night_owl_(person)", "Night_owl")
          , ("Nightshade", "Solanaceae")
          , ("Nihon_SF_Taisho", "Nihon_SF_Taisho_Award")
          , ("Nij%C5%ABichidaish%C5%AB", "Chokusen_wakash%C5%AB#Nij%C5%ABichidaish%C5%AB")
          , ("Nike+_FuelBand", "Nike%2B_FuelBand")
          , ("Nikkatsu_Studio", "Nikkatsu")
          , ("Nile_Valley", "Nile")
          , ("Ninin-Baori", "Nininbaori")
          , ("Ninja_Gaiden_Black", "Ninja_Gaiden_(2004_video_game)#Ninja_Gaiden_Black")
          , ("Nintendo_Power_Glove", "Power_Glove")
          , ("Nintendo_of_America", "Nintendo#Nintendo_of_America")
          , ("Nippon_Chinbotsu", "Japan_Sinks")
          , ("Nitrates", "Nitrate")
          , ("Nitrosamines", "Nitrosamine")
          , ("Nixon_Shock", "Nixon_shock")
          , ("No-globes", "List_of_technology_in_the_Dune_universe#No-chamber/No-ship")
          , ("No_Matter_How_I_Look_at_It,_It%27s_You_Guys%27_Fault_I%27m_Not_Popular%21", "No_Matter_How_I_Look_at_It,_It%27s_You_Guys%27_Fault_I%27m_Not_Popular!")
          , ("No_Matter_How_I_Look_at_It,_It's_You_Guys'_Fault_I'm_Not_Popular%21", "No_Matter_How_I_Look_at_It,_It%27s_You_Guys%27_Fault_I%27m_Not_Popular!")
          , ("No_free_lunch", "No_such_thing_as_a_free_lunch")
          , ("No_poo", "Hair_washing_without_commercial_shampoo")
          , ("Noble_Lie", "Noble_lie")
          , ("Non-alcoholic_fatty_liver_disease", "Metabolic_dysfunction%E2%80%93associated_steatotic_liver_disease")
          , ("Non-alcoholic_steatohepatitis", "Metabolic_dysfunction%E2%80%93associated_steatotic_liver_disease")
          , ("Non-cooperative_game", "Non-cooperative_game_theory")
          , ("Non-human_primate", "Primate")
          , ("Non-parametric_statistics", "Nonparametric_statistics")
          , ("Noninferiority", "Equivalence_test")
          , ("Nonsense_syllable", "Pseudoword#Nonsense_syllables")
          , ("Nonshared_environment", "Twin_study#Methods")
          , ("Noopept", "N-Phenylacetyl-L-prolylglycine_ethyl_ester")
          , ("Nootropics", "Nootropic")
          , ("Noradrenergic", "Norepinephrine")
          , ("Norbert_Weiner", "Norbert_Wiener")
          , ("Norm_(social)", "Social_norm")
          , ("Norman_R._F._Maier", "Norman_Maier")
          , ("Norman_conquest_of_England", "Norman_Conquest")
          , ("North_American_Aerospace_Defense_Command", "NORAD")
          , ("Nose-picking", "Nose_picking")
          , ("Not_Invented_Here", "Not_invented_here")
          , ("Noumenal", "Noumenon")
          , ("Nova_laser", "Nova_(laser)")
          , ("Novelty_Seeking", "Novelty_seeking")
          , ("Novocaine", "Procaine")
          , ("Npm_(software)", "Npm")
          , ("Nuclear_density_gauge", "Nuclear_densitometry")
          , ("Nuclear_deterrence", "Deterrence_theory#Nuclear_deterrence_theory")
          , ("Nuclear_energy_in_Denmark", "Nuclear_power_in_Denmark")
          , ("Nucleoside_5%CA%B9-triphosphate", "Nucleoside_triphosphate")
          , ("Nucleotides", "Nucleotide")
          , ("Null_hypothesis_significance_testing", "Statistical_hypothesis_test")
          , ("Null_hypothesis_testing", "Statistical_hypothesis_test")
          , ("Null_terminated_strings", "Null-terminated_string")
          , ("Numenta", "Jeff_Hawkins")
          , ("Numerosity", "Numerical_cognition")
          , ("Nurses'_Health_Study", "Nurses%27_Health_Study")
          , ("Nyctereutes_procyonoides", "Common_raccoon_dog")
          , ("O'Melveny_%26_Myers", "O%27Melveny_%26_Myers")
          , ("O-Ring_theory_of_economic_development", "O-ring_theory_of_economic_development")
          , ("O-ring_theory_of_productivity", "O-ring_theory_of_economic_development")
          , ("O._J._Simpson_murder_case", "Murder_trial_of_O._J._Simpson")
          , ("OBSCN", "Obscurin")
          , ("OCD", "Obsessive%E2%80%93compulsive_disorder")
          , ("OKCupid", "OkCupid")
          , ("OS/360", "OS/360_and_successors")
          , ("OSINT", "Open-source_intelligence")
          , ("Oak_Ridge_National_Lab", "Oak_Ridge_National_Laboratory")
          , ("Obesity_epidemic", "Epidemiology_of_obesity")
          , ("Obfuscated_programming", "Obfuscation_(software)")
          , ("Object_oriented", "Object-oriented_programming")
          , ("Object_recognition", "Outline_of_object_recognition")
          , ("Obligate_carnivore", "Carnivore#Obligate_carnivores")
          , ("Obsessive-compulsive_disorder", "Obsessive%E2%80%93compulsive_disorder")
          , ("Obsessive_compulsive_disorder", "Obsessive%E2%80%93compulsive_disorder")
          , ("Occupational_mobility", "Labor_mobility")
          , ("Ocean's_11", "Ocean%27s_11")
          , ("Ocean_alkalinity_enhancement", "Ocean_acidification#Ocean_alkalinity_enhancement")
          , ("Octopus_bimaculoides", "California_two-spot_octopus")
          , ("Octopus_vulgaris", "Common_octopus")
          , ("Oculus_VR", "Reality_Labs")
          , ("Odonists", "Heathenry_(new_religious_movement)")
          , ("Oe_Kenzaburo", "Kenzabur%C5%8D_%C5%8Ce")
          , ("Oestradiol", "Estradiol")
          , ("Off-label_indications", "Off-label_use")
          , ("Oh_My_Goddess%21", "Oh_My_Goddess!")
          , ("Ohio-class_submarines", "Ohio-class_submarine")
          , ("Oja's_rule", "Oja%27s_rule")
          , ("Okinawa_Battle", "Battle_of_Okinawa")
          , ("Olbers%27_paradox", "Olbers%27s_paradox")
          , ("Old_World_vultures", "Old_World_vulture")
          , ("Oleander", "Nerium")
          , ("Olfaction", "Sense_of_smell")
          , ("Olfactory_bulbs", "Olfactory_bulb")
          , ("Oligodendrocytes", "Oligodendrocyte")
          , ("Oligomerization", "Oligomer")
          , ("Oligoovulation", "Ovulation#Disorders")
          , ("Oligozoospermia", "Oligospermia")
          , ("Olivetti_S.p.A.", "Olivetti")
          , ("Omega-3", "Omega-3_fatty_acid")
          , ("On_Being_Sane_in_Insane_Places", "Rosenhan_experiment")
          , ("On_the_Nature_of_Things", "De_rerum_natura")
          , ("Once_a_Hero_(novel)", "Elizabeth_Moon")
          , ("Oncifelis_geoffroyi", "Geoffroy%27s_cat")
          , ("One-shot_learning", "Prompt_engineering")
          , ("One-time_pads", "One-time_pad")
          , ("OneWeb", "Eutelsat_OneWeb")
          , ("One_Child_policy", "One-child_policy")
          , ("One_country_two_systems", "One_country,_two_systems")
          , ("One_instruction_set_computer", "One-instruction_set_computer")
          , ("Onegai_Teacher", "Please_Teacher!")
          , ("Ong's_Hat", "Ong%27s_Hat")
          , ("Ong's_Hat,_New_Jersey", "Ong%27s_Hat,_New_Jersey")
          , ("Ong_Bak", "Ong-Bak:_Muay_Thai_Warrior")
          , ("Online_dating_service", "Online_dating")
          , ("Online_education", "Distance_education")
          , ("Online_survey", "Survey_data_collection#Online_surveys")
          , ("Only_Fans", "OnlyFans")
          , ("Onychopagia", "Nail_biting")
          , ("Oocytes", "Oocyte")
          , ("Oolong_tea", "Oolong")
          , ("Oort_Cloud", "Oort_cloud")
          , ("Open-label", "Open-label_trial")
          , ("Open-source_model", "Open_source")
          , ("Open_Gaming", "Open_gaming")
          , ("Open_Genera", "Genera_(operating_system)")
          , ("Open_Philanthropy_%28organization%29", "Open_Philanthropy")
          , ("Open_Philanthropy_(organization)", "Open_Philanthropy")
          , ("Open_Science_Framework", "Center_for_Open_Science#Open_Science_Framework")
          , ("Open_Source_movement", "Open-source-software_movement")
          , ("Open_format", "Open_file_format")
          , ("Open_source_intelligence", "Open-source_intelligence")
          , ("Openness_to_Experience", "Openness_to_experience")
          , ("Operating_system-level_virtualization", "OS-level_virtualization")
          , ("Operation_Bojinka", "Bojinka_plot")
          , ("Operation_Entebbe", "Entebbe_raid")
          , ("Operation_Hemorrhage", "2010_transatlantic_aircraft_bomb_plot")
          , ("Operation_Neptune_Spear", "Killing_of_Osama_bin_Laden#Operation_Neptune_Spear")
          , ("Operation_Plowshare", "Project_Plowshare")
          , ("Operator_algebras", "Operator_algebra")
          , ("Opiates", "Opiate")
          , ("Opinion_polls", "Opinion_poll")
          , ("Opium_poppy", "Papaver_somniferum")
          , ("Opium_tea", "Poppy_tea")
          , ("Opportunity_costs", "Opportunity_cost")
          , ("Optimal_design", "Optimal_experimental_design")
          , ("Optimal_transport", "Transportation_theory_(mathematics)")
          , ("Option_value_(cost_benefit_analysis)", "Option_value_(cost%E2%80%93benefit_analysis)")
          , ("Oral_contraceptives", "Oral_contraceptive_pill")
          , ("Orcinus_orca", "Orca")
          , ("Order_statistics", "Order_statistic")
          , ("Ordinal_scale", "Ordinal_data")
          , ("Ordinary_differential_equations", "Ordinary_differential_equation")
          , ("Orexigenic", "Appetite_stimulant")
          , ("Organ_meats", "Offal")
          , ("Organ_transplants", "Organ_transplantation")
          , ("Organizational_psychology", "Industrial_and_organizational_psychology")
          , ("Organoids", "Organoid")
          , ("Origin_of_Species", "On_the_Origin_of_Species")
          , ("Original_animation_video", "Original_video_animation")
          , ("Original_equipment_manufacturers", "Original_equipment_manufacturer")
          , ("Oritsu_Uchugun", "Royal_Space_Force:_The_Wings_of_Honn%C3%AAamise")
          , ("Ornstein-Uhlenbeck_process", "Ornstein%E2%80%93Uhlenbeck_process")
          , ("Orphan_works", "Orphan_work")
          , ("Orpheus_no_Mado", "The_Window_of_Orpheus")
          , ("Ortega_y_Gasset", "Jos%C3%A9_Ortega_y_Gasset")
          , ("Orthographic_ligature", "Ligature_(writing)")
          , ("Orthoherpesviridae", "Herpesviridae")
          , ("Osaka_castle", "Osaka_Castle")
          , ("Oscar_Saint-Just", "List_of_characters_in_the_Honorverse#S")
          , ("Osmolarity", "Osmotic_concentration")
          , ("Otomo_no_Sakanoe_no_Iratsume", "%C5%8Ctomo_no_Sakanoue_no_Iratsume")
          , ("Our_Mr._Wrenn:_The_Romantic_Adventures_of_a_Gentle_Man", "Our_Mr._Wrenn")
          , ("Outgroup_(sociology)", "In-group_and_out-group")
          , ("Outriders_%28video_game%29", "Outriders_(video_game)")
          , ("Oviposition", "Ovipositor")
          , ("Owarimonogatari", "Monogatari_(series)")
          , ("Oxygen_consumption", "Blood#Oxygen_transport")
          , ("Oxygenation_(environmental)", "Hypoxia_(environmental)")
          , ("Oyasumi_Punpun", "Goodnight_Punpun")
          , ("Oz_books", "List_of_Oz_books")
          , ("P%3DNP", "P_versus_NP_problem")
          , ("P%C3%A9rigord_truffles", "Tuber_melanosporum")
          , ("P%C4%81li_Canon", "Pali_Canon")
          , ("P-adic", "P-adic_number")
          , ("P-hard", "P_(complexity)")
          , ("P2P_networks", "Peer-to-peer")
          , ("PARC_%28company%29", "PARC_(company)")
          , ("PARK7", "DJ-1")
          , ("PASAT", "Paced_Auditory_Serial_Addition_Test")
          , ("PC-8001", "PC-8000_series")
          , ("PC-9800_series", "PC-98")
          , ("PC-DOS", "IBM_PC_DOS")
          , ("PCL-R", "Psychopathy_Checklist")
          , ("PCR", "Polymerase_chain_reaction")
          , ("PDFs", "PDF")
          , ("PDPK1", "Phosphoinositide-dependent_kinase-1")
          , ("PDP_eleven", "PDP-11")
          , ("PGP_Corp", "PGP_Corporation")
          , ("PID_controller", "Proportional%E2%80%93integral%E2%80%93derivative_controller")
          , ("PISA", "Programme_for_International_Student_Assessment")
          , ("PKMzeta", "Protein_kinase_C_zeta_type")
          , ("PL%2FI", "PL/I")
          , ("PLO", "Palestine_Liberation_Organization")
          , ("PMSing", "Premenstrual_syndrome")
          , ("POMC", "Proopiomelanocortin")
          , ("POMDP", "Partially_observable_Markov_decision_process")
          , ("PRNP", "Major_prion_protein")
          , ("PS3", "PlayStation_3")
          , ("PS3s", "PlayStation_3")
          , ("PTSD", "Post-traumatic_stress_disorder")
          , ("P_Production", "P_Productions")
          , ("P_factor_(psychopathology)", "Psychopathology#The_p_factor")
          , ("P_vs_NP", "P_versus_NP_problem")
          , ("Pachelbel's_Canon", "Pachelbel%27s_Canon")
          , ("Paid_parental-leave", "Parental_leave")
          , ("Palaeolithic", "Paleolithic")
          , ("Paleography", "Palaeography")
          , ("Palestinian_Black_September_Organization", "Black_September_Organization")
          , ("Pandoraviruses", "Pandoravirus")
          , ("Pangium_edule", "Pangium")
          , ("Panmictic", "Panmixia")
          , ("Panoptic_segmentation", "Image_segmentation#Groups_of_image_segmentation")
          , ("Panthera_leo_spelaea", "Panthera_spelaea")
          , ("Papanicolaou_smear", "Pap_test")
          , ("Parable_of_the_talents_or_minas", "Parable_of_the_Talents")
          , ("Parallel_corpus", "Parallel_text")
          , ("Paranoid_delusions", "Delusion")
          , ("Paraphilias", "Paraphilia")
          , ("Parasite_loads", "Parasite_load")
          , ("Pardus_(browser_game)", "Pardus_(video_game)")
          , ("Parental_age_effect", "Paternal_age_effect")
          , ("Pareto-efficient", "Pareto_efficiency")
          , ("Pareto-improving", "Pareto_efficiency")
          , ("Pareto-optimal", "Pareto_efficiency")
          , ("Pareto_frontier", "Pareto_front")
          , ("Pareto_frontiers", "Pareto_front")
          , ("Pareto_optimal", "Pareto_efficiency")
          , ("Parietal_cortex", "Parietal_lobe")
          , ("Paris_Review", "The_Paris_Review")
          , ("Parish_records", "Parish_register")
          , ("Parkinson%27s_Law", "Parkinson%27s_law")
          , ("Parkinson's_Law", "Parkinson%27s_law")
          , ("Parkinson's_disease", "Parkinson%27s_disease")
          , ("Parkinson's_law", "Parkinson%27s_law")
          , ("Parkinsons_Disease", "Parkinson%27s_disease")
          , ("Parkinsons_disease", "Parkinson%27s_disease")
          , ("Parkinson’s_disease", "Parkinson%27s_disease")
          , ("Parrots", "Parrot")
          , ("Partial_differential_equations", "Partial_differential_equation")
          , ("Partially_Observed_Markov_Decision_Processes", "Partially_observable_Markov_decision_process")
          , ("Particle_filtering", "Particle_filter")
          , ("Partisan_(political)", "Partisan_(politics)")
          , ("Partition_Coefficients", "Partition_coefficient")
          , ("Pascal%27s_Wager", "Pascal%27s_wager")
          , ("Pascal's_Wager", "Pascal%27s_wager")
          , ("Passive_immunization", "Passive_immunity")
          , ("Pasteurized_milk", "Pasteurization#Milk")
          , ("Patent_thickets", "Patent_thicket")
          , ("Path_analysis_%28statistics%29", "Path_analysis_(statistics)")
          , ("Patrick_O'Brian", "Patrick_O%27Brian")
          , ("Paul_Graham_%28computer_programmer%29", "Paul_Graham_(programmer)")
          , ("Paul_Graham_(computer_programmer)", "Paul_Graham_(programmer)")
          , ("Paul_Karl_Feyerabend", "Paul_Feyerabend")
          , ("Pavlovian_conditioning", "Classical_conditioning")
          , ("Peak-end_rule", "Peak%E2%80%93end_rule")
          , ("Peano_arithmetic", "Peano_axioms#Peano_arithmetic_as_first-order_theory")
          , ("Pearson%27s_r", "Pearson_correlation_coefficient")
          , ("Pearson_correlation", "Pearson_correlation_coefficient")
          , ("Pearson_product-moment_correlation_coefficient", "Pearson_correlation_coefficient")
          , ("Pedology_(study_of_children)", "Paedology")
          , ("Pen%C3%ADnsula_Vald%C3%A9s", "Vald%C3%A9s_Peninsula")
          , ("Penetration_testing", "Penetration_test")
          , ("Penicillins", "Penicillin")
          , ("Peninsula_Vald%C3%A9s", "Vald%C3%A9s_Peninsula")
          , ("Penn_Treebank", "Treebank")
          , ("Pennsylvania_Station_(New_York_City)", "New_York_Penn_Station")
          , ("Pentylenetetrazole", "Pentylenetetrazol")
          , ("People%27s_Republic_of_China", "China")
          , ("People's_Daily", "People%27s_Daily")
          , ("People's_Literature", "People%27s_Literature")
          , ("People's_Republic_of_China", "China")
          , ("Peoples%27_Friendship_University_of_Russia", "Patrice_Lumumba_Peoples%27_Friendship_University_of_Russia")
          , ("Peoples'_Friendship_University_of_Russia", "Patrice_Lumumba_Peoples%27_Friendship_University_of_Russia")
          , ("Peptic_ulcer", "Peptic_ulcer_disease")
          , ("Perceptrons", "Perceptron")
          , ("Perceptrons_%28book%29", "Perceptrons_(book)")
          , ("Perceptual_load", "Perceptual_load_theory")
          , ("Perchloroethane", "Hexachloroethane")
          , ("Perikarya", "Soma_(biology)")
          , ("Permissive_Action_Link", "Permissive_action_link")
          , ("Permissive_Action_Links", "Permissive_action_link")
          , ("Permutahedron", "Permutohedron")
          , ("Permutation_groups", "Permutation_group")
          , ("Permuting", "Permutation")
          , ("Personal_information_managers", "Personal_digital_assistant")
          , ("Personal_organizers", "Personal_organizer")
          , ("Personality_cults", "Cult_of_personality")
          , ("Personality_disorders", "Personality_disorder")
          , ("Personality_trait", "Trait_theory")
          , ("Personality_traits", "Trait_theory")
          , ("Personifications", "Personification")
          , ("Perverse_incentives", "Perverse_incentive")
          , ("Pesachim_(tractate)", "Pesachim")
          , ("Pet_cloning", "Commercial_animal_cloning")
          , ("Petaflops", "FLOPS")
          , ("Petards", "Petard")
          , ("Pfizer_Inc.", "Pfizer")
          , ("Phage", "Bacteriophage")
          , ("Pharmacogenetic_testing", "Pharmacogenomics")
          , ("Phase_III_trial", "Phases_of_clinical_research#Phase_III")
          , ("Phase_transitions", "Phase_transition")
          , ("Phenobarbitol", "Phenobarbital")
          , ("Phil_Zimmerman", "Phil_Zimmermann")
          , ("Philadelphia_Convention", "Constitutional_Convention_(United_States)")
          , ("Philip_Tetlock", "Philip_E._Tetlock")
          , ("Philip_Warren_Anderson", "Philip_W._Anderson")
          , ("Philosophy_of_Max_Stirner", "Max_Stirner#Philosophy")
          , ("Phlebotomist", "Phlebotomy")
          , ("Phlebotomists", "Phlebotomy")
          , ("Phone_book", "Telephone_directory")
          , ("Photoshop", "Adobe_Photoshop")
          , ("Phryne_before_the_Areopagus", "Phryne_Before_the_Areopagus")
          , ("Physical_exercise", "Exercise")
          , ("Physically_unclonable_functions", "Physical_unclonable_function")
          , ("Physikalisch-Technische_Reichsanstalt", "Physikalisch-Technische_Bundesanstalt")
          , ("Picasso", "Pablo_Picasso")
          , ("Pick_up_artists", "Pickup_artist#Pickup_artist")
          , ("Pickup_Artist", "Pickup_artist#The_pickup_artist")
          , ("Pied_Piper_of_Hamlin", "Pied_Piper_of_Hamelin")
          , ("Pierre-Simon_de_Laplace", "Pierre-Simon_Laplace")
          , ("Pierson's_Puppeteers", "Pierson%27s_Puppeteers")
          , ("Pigeonhole_Principle", "Pigeonhole_principle")
          , ("Pigeons", "Columbidae")
          , ("Pigovian_tax", "Pigouvian_tax")
          , ("Pilgrim%27s_Progress", "The_Pilgrim%27s_Progress")
          , ("Pilgrim's_Progress", "The_Pilgrim%27s_Progress")
          , ("Pilot_waves", "Pilot_wave_theory")
          , ("Pioneers%21_O_Pioneers%21", "Pioneers!_O_Pioneers!")
          , ("Piotr_Wozniak_(researcher)", "Piotr_Wo%C5%BAniak_(researcher)")
          , ("Pixel_2_XL", "Pixel_2")
          , ("Place_(Reddit)", "R/place")
          , ("Placebo_effect", "Placebo#Effects")
          , ("Placebos", "Placebo")
          , ("Planck", "Max_Planck")
          , ("Planck-second", "Planck_units#Planck_time")
          , ("Planck_density", "Planck_units#Derived_units")
          , ("Planck_length", "Planck_units#Planck_length")
          , ("Planck_temperature", "Planck_units#Planck_temperature")
          , ("Plane_of_the_ecliptic", "Ecliptic")
          , ("Planetary_defense", "Asteroid_impact_avoidance")
          , ("Plasma_glucose", "Blood_sugar_level")
          , ("Plasmids", "Plasmid")
          , ("Plato's_Republic", "Republic_(Plato)")
          , ("PlayStation_1", "PlayStation_(console)")
          , ("Playboy_bunny", "Playboy_Bunny")
          , ("Player's_Handbook", "Player%27s_Handbook")
          , ("Pleiotropic", "Pleiotropy")
          , ("Plexiglas", "Poly(methyl_methacrylate)")
          , ("Pluripotency", "Cell_potency#Pluripotency")
          , ("Pluripotent_stem_cell", "Cell_potency#Pluripotency")
          , ("Plushies", "Stuffed_toy")
          , ("Podofilox", "Podophyllotoxin")
          , ("Poe's_law", "Poe%27s_law")
          , ("Pogona_vitticeps", "Central_bearded_dragon")
          , ("Poikilotherms", "Poikilotherm")
          , ("Poisoning_of_Alexei_Navalny_(2020)", "Poisoning_of_Alexei_Navalny")
          , ("Poisson_process", "Poisson_point_process")
          , ("Pok%C3%A9mon_Detective_Pikachu", "Detective_Pikachu_(film)")
          , ("Pok%C3%A9mon_Yellow", "Pok%C3%A9mon_Red,_Blue,_and_Yellow#Pok%C3%A9mon_Yellow")
          , ("Pokemon_Yellow", "Pok%C3%A9mon_Red,_Blue,_and_Yellow#Pok%C3%A9mon_Yellow")
          , ("Polarization_(politics)", "Political_polarization")
          , ("Police_Foundation", "National_Policing_Institute")
          , ("Poliomyelitis", "Polio")
          , ("Political_advertising", "Campaign_advertising")
          , ("Political_behavior", "Theories_of_political_behavior")
          , ("Political_elite", "Elite")
          , ("Political_extremism", "Extremism")
          , ("Political_knowledge", "Political_philosophy")
          , ("Polychaetes", "Polychaete")
          , ("Polychlorinated_biphenyls", "Polychlorinated_biphenyl")
          , ("Polycyclic_aromatic_hydrocarbons", "Polycyclic_aromatic_hydrocarbon")
          , ("Polygenic_scores", "Polygenic_score")
          , ("Polygraphia", "Polygraphia_(book)")
          , ("Polyhedrons", "Polyhedron")
          , ("Polyphenols", "Polyphenol")
          , ("Polyploid", "Polyploidy")
          , ("Polyresin", "Polyester_resin")
          , ("Polysaccharides", "Polysaccharide")
          , ("Polysulfide_bromide_battery", "Polysulfide%E2%80%93bromide_battery")
          , ("Polytopes", "Polytope")
          , ("Polyunsaturated_fatty_acid", "Polyunsaturated_fat")
          , ("Pompoko", "Pom_Poko")
          , ("Pool_variance", "Pooled_variance")
          , ("Popeye_the_Sailor", "Popeye")
          , ("Poppy_family", "Papaveraceae")
          , ("Population_explosion", "Overpopulation")
          , ("Population_register", "Civil_registration")
          , ("Population_registry", "Civil_registration")
          , ("Port-wine", "Port_wine")
          , ("Portable_Network_Graphics", "PNG")
          , ("Portfolio_allocation", "Portfolio_optimization")
          , ("Portia_%28spider%29", "Portia_(spider)")
          , ("Portobello_mushrooms", "Agaricus_bisporus")
          , ("Portscanning", "Port_scanner")
          , ("Positional_goods", "Positional_good")
          , ("Positive-sum_game", "Win%E2%80%93win_game")
          , ("Positive_externalities", "Externality")
          , ("Positive_predictive_value", "Positive_and_negative_predictive_values")
          , ("Positive_selection", "Directional_selection")
          , ("Post-scarcity_economy", "Post-scarcity")
          , ("Post-translational_modifications", "Post-translational_modification")
          , ("Posterior_distribution", "Posterior_probability")
          , ("Posterior_probabilities", "Posterior_probability")
          , ("Posttraumatic_stress_disorder", "Post-traumatic_stress_disorder")
          , ("Potamogeton_pectinatus", "Stuckenia_pectinata")
          , ("Potatoes", "Potato")
          , ("Poverty_traps", "Cycle_of_poverty")
          , ("PowerPoint", "Microsoft_PowerPoint")
          , ("Power_(statistics)", "Power_of_a_test")
          , ("Power_grid", "Electrical_grid")
          , ("Power_laws", "Power_law")
          , ("Power_spectra", "Spectral_density#Power_spectral_density")
          , ("Powered_Suit", "Powered_exoskeleton#Fictional_powered_exoskeletons")
          , ("Practice_effect", "Between-group_design#Practice_effect")
          , ("Praeparatio_Evangelica", "Praeparatio_evangelica")
          , ("Prandial_insulin", "Insulin_(medication)#Prandial_insulin")
          , ("Prayer_wheels", "Prayer_wheel")
          , ("Pre-Raphaelites", "Pre-Raphaelite_Brotherhood")
          , ("Pre-established_harmony", "Mind%E2%80%93body_problem#Pre-established_harmony")
          , ("Pre-registration", "Preregistration_(science)")
          , ("Pre-socratic", "Pre-Socratic_philosophy")
          , ("Precautionary_Principle", "Precautionary_principle")
          , ("Precision_Medicine_Initiative", "All_of_Us_(initiative)")
          , ("Precision_medicine", "Personalized_medicine")
          , ("Predicate_calculus", "First-order_logic")
          , ("Prediction_markets", "Prediction_market")
          , ("Predictive_processing", "Predictive_coding")
          , ("Predictor-corrector_method", "Predictor%E2%80%93corrector_method")
          , ("Preferred_reporting_items_for_systematic_reviews_and_meta-analyses", "Preferred_Reporting_Items_for_Systematic_Reviews_and_Meta-Analyses")
          , ("Preoperational_stage", "Piaget%27s_theory_of_cognitive_development#Preoperational_stage")
          , ("Preregistration", "Preregistration_(science)")
          , ("Prerenal_azotemia", "Azotemia#Prerenal_azotemia")
          , ("President_Nixon", "Richard_Nixon")
          , ("Pressure_sensor", "Pressure_measurement#Instruments")
          , ("Pretoria,_South_Africa", "Pretoria")
          , ("Prevention_of_Tay-Sachs_disease", "Prevention_of_Tay%E2%80%93Sachs_disease")
          , ("Price%27s_equation", "Price_equation")
          , ("Price%27s_law", "Derek_J._de_Solla_Price#Scientific_contributions")
          , ("Price's_equation", "Price_equation")
          , ("Price's_law", "Derek_J._de_Solla_Price#Scientific_contributions")
          , ("Price_discriminate", "Price_discrimination")
          , ("PricewaterhouseCoopers", "PwC")
          , ("Primality_testing", "Primality_test")
          , ("Primary/secondary_quality_distinction", "Primary%E2%80%93secondary_quality_distinction")
          , ("Primary_follicle", "Germinal_center")
          , ("Primary_visual_cortex", "Visual_cortex#Primary_visual_cortex_(V1)")
          , ("Priming_%28psychology%29", "Priming_(psychology)")
          , ("Primitive_recursion", "Primitive_recursive_function")
          , ("Primitive_recursive", "Primitive_recursive_function")
          , ("Primordial_follicle", "Folliculogenesis#Primordial")
          , ("Primordial_germ_cell", "Germ_cell")
          , ("Primordial_pouch", "Cat_anatomy#Skin")
          , ("Principal-agent_conflicts", "Principal%E2%80%93agent_problem")
          , ("Principal-agent_problem", "Principal%E2%80%93agent_problem")
          , ("Principal-agent_problems", "Principal%E2%80%93agent_problem")
          , ("Principal_Component_Analysis", "Principal_component_analysis")
          , ("Principal_component", "Principal_component_analysis")
          , ("Principal_components", "Principal_component_analysis")
          , ("Principality_of_Transylvania_(1570-1711)", "Principality_of_Transylvania_(1570%E2%80%931711)")
          , ("Principle_of_contradiction", "Law_of_noncontradiction")
          , ("Principle_of_least_action", "Stationary-action_principle")
          , ("Printf_format_string", "Printf")
          , ("Prisoner's_dilemma", "Prisoner%27s_dilemma")
          , ("Prisoners'_Dilemma", "Prisoner%27s_dilemma")
          , ("Privacy_mode", "Private_browsing")
          , ("Private_sperm_donor", "Sperm_donation#Private_or_.22directed.22_donors")
          , ("Private_tutor", "Tutoring#Peer_tutoring")
          , ("Pro-ana", "Promotion_of_anorexia")
          , ("Probabilistic_programming_language", "Probabilistic_programming")
          , ("Probability_distributions", "Probability_distribution")
          , ("Probability_sample", "Sampling_(statistics)#Sampling_frame")
          , ("Problematic_Internet_use", "Internet_addiction_disorder")
          , ("Procavia_capensis", "Rock_hyrax")
          , ("Proctor_%26_Gamble", "Procter_%26_Gamble")
          , ("Procyclical_and_countercyclical", "Procyclical_and_countercyclical_variables")
          , ("Product_distribution", "Distribution_of_the_product_of_two_random_variables")
          , ("Profile_of_Mood_States", "Profile_of_mood_states")
          , ("Profiles_of_Mood_States", "Profile_of_mood_states")
          , ("Progress_bars", "Progress_bar")
          , ("Progressive_Enhancement", "Progressive_enhancement")
          , ("Progressive_Party_(United_States,_1912)", "Progressive_Party_(United_States,_1912%E2%80%931920)")
          , ("Progressive_taxation", "Progressive_tax")
          , ("Projection_neuron", "Projection_fiber")
          , ("Prokaryotes", "Prokaryote")
          , ("Proof-of-Work", "Proof_of_work")
          , ("Proof-of-work_system", "Proof_of_work")
          , ("Proofs_by_contradiction", "Proof_by_contradiction")
          , ("Propaganda_of_the_Spanish-American_War", "American_propaganda_of_the_Spanish%E2%80%93American_War")
          , ("Propensity_scoring", "Propensity_score_matching")
          , ("Proper_scoring", "Scoring_rule#Proper_score_functions")
          , ("Proper_scoring_rule", "Scoring_rule#Proper_scoring_rules")
          , ("Proportional_fonts", "Typeface#Proportion")
          , ("Proportional_hazards_models", "Proportional_hazards_model")
          , ("Proposed_United_States_purchase_of_Greenland", "Proposals_for_the_United_States_to_purchase_Greenland")
          , ("Propositional_logic", "Propositional_calculus")
          , ("Propulsion_transmission", "Transmission_(mechanical_device)")
          , ("Prosospagnosia", "Prosopagnosia")
          , ("Protein-protein_interaction", "Protein%E2%80%93protein_interaction")
          , ("Protein_kinase_G", "CGMP-dependent_protein_kinase")
          , ("Protein_restriction", "Low-protein_diet")
          , ("Proteins", "Protein")
          , ("Proteobacteria", "Pseudomonadota")
          , ("Proton_Decay", "Proton_decay")
          , ("Protonibea_diacanthus", "Blackspotted_croaker")
          , ("Pruning_(neural_networks)", "Pruning_(artificial_neural_network)")
          , ("Prussia's_Army", "Prussian_Army")
          , ("Przewalski's_horse", "Przewalski%27s_horse")
          , ("Pseudo-Dionysius", "Pseudo-Dionysius_the_Areopagite")
          , ("Pseudologia_fantastica", "Pathological_lying")
          , ("Pseudosciences", "Pseudoscience")
          , ("Psi_(parapsychology)", "Parapsychology#Terminology")
          , ("Psilocybin_mushrooms", "Psilocybin_mushroom")
          , ("Psittaciformes", "Parrot")
          , ("Psychedelic_substance", "Psychedelic_drug")
          , ("Psychedelics", "Psychedelic_drug")
          , ("Psychiatric_disorder", "Mental_disorder")
          , ("Psychopath", "Psychopathy")
          , ("Psychopathic", "Psychopathy")
          , ("Psychotic-like_experiences", "Psychosis")
          , ("Psychotropic_medication", "Psychoactive_drug")
          , ("Psyops", "Psychological_warfare")
          , ("Pu%27erh_tea", "Pu%27er_tea")
          , ("Pu'erh_tea", "Pu%27er_tea")
          , ("Public_funding", "Subsidy")
          , ("Public_goods", "Public_good_(economics)")
          , ("Public_key_cryptography", "Public-key_cryptography")
          , ("Pubmed", "PubMed")
          , ("Puccini", "Giacomo_Puccini")
          , ("Pugs", "Pug")
          , ("Pugs_(programming)", "Pugs_(compiler)")
          , ("Pulse_(legume)", "Legume")
          , ("Pupil_dilation", "Pupillary_response")
          , ("Pupil_size", "Pupillary_response")
          , ("Puppy_cat", "Dog-like_cat")
          , ("Purifying_selection", "Negative_selection_(natural_selection)")
          , ("Purple_(cipher_machine)", "Type_B_Cipher_Machine")
          , ("Push_polls", "Push_poll")
          , ("Pyramidal_neurons", "Pyramidal_cell")
          , ("Q%2Abert", "Q*bert")
          , ("Q-Tips", "Cotton_swab")
          , ("QALY", "Quality-adjusted_life_year")
          , ("QALYs", "Quality-adjusted_life_year")
          , ("QIWI", "Qiwi")
          , ("Qelippot", "Qlippoth")
          , ("Qliphoth", "Qlippoth")
          , ("Quaker", "Quakers")
          , ("Quantified_Self", "Quantified_self")
          , ("Quantitative_analyst", "Quantitative_analysis_(finance)")
          , ("Quantitative_trait_loci", "Quantitative_trait_locus")
          , ("Quantum_computer", "Quantum_computing")
          , ("Quantum_immortality", "Quantum_suicide_and_immortality")
          , ("Quantum_simulation", "Quantum_simulator")
          , ("Quantum_suicide", "Quantum_suicide_and_immortality")
          , ("Quantum_uncertainty", "Uncertainty_principle")
          , ("Quasi-experimental", "Quasi-experiment")
          , ("Quaternions", "Quaternion")
          , ("Quechua_languages", "Quechuan_languages")
          , ("Queen's_Gold_Medal", "King%27s_Gold_Medal_for_Poetry")
          , ("Queen_Elizabeth_II", "Elizabeth_II")
          , ("Queen_Marie_Antoinette", "Marie_Antoinette")
          , ("Query_optimizer", "Query_optimization")
          , ("Queue_%28hairstyle%29", "Queue_(hairstyle)")
          , ("Queuing_theory", "Queueing_theory")
          , ("Quidditch_(sport)", "Quidditch_(real-life_sport)")
          , ("Quilts", "Quilt")
          , ("Q✱bert", "Q*bert")
          , ("R.A._Fisher", "Ronald_Fisher")
          , ("R.A._Lafferty", "R._A._Lafferty")
          , ("R.R._Bowker", "R._R._Bowker")
          , ("R._A._Fisher", "Ronald_Fisher")
          , ("R._C._Lewontin", "Richard_Lewontin")
          , ("R7RS", "Scheme_(programming_language)#R7RS")
          , ("RACTER", "Racter")
          , ("RAD-seq", "Restriction_site_associated_DNA_markers")
          , ("RAND", "RAND_Corporation")
          , ("RECAP", "Free_Law_Project#RECAP")
          , ("REM", "Rapid_eye_movement_sleep")
          , ("REM_paralysis", "Rapid_eye_movement_sleep#Muscle")
          , ("REPL", "Read%E2%80%93eval%E2%80%93print_loop")
          , ("RIDGID", "Ridgid")
          , ("RKO_Radio_Pictures", "RKO_Pictures")
          , ("RMSE", "Root-mean-square_deviation")
          , ("RNA-seq", "RNA-Seq")
          , ("RNAi", "RNA_interference")
          , ("RSS_feed", "RSS")
          , ("RX-77_Guncannon", "Mobile_Suit_Gundam")
          , ("Rabbit-duck_illusion", "Rabbit%E2%80%93duck_illusion")
          , ("Race_conditions", "Race_condition")
          , ("Racemic", "Racemic_mixture")
          , ("Races_of_StarCraft", "StarCraft#Story")
          , ("Racial_resentment", "Symbolic_racism")
          , ("Radar_Detector", "Radar_detector")
          , ("Radar_charts", "Radar_chart")
          , ("Radiation_Laboratory", "MIT_Radiation_Laboratory")
          , ("Radiation_effects_from_Fukushima_Daiichi_nuclear_disaster", "Radiation_effects_from_the_Fukushima_Daiichi_nuclear_disaster")
          , ("Radiation_sickness", "Acute_radiation_syndrome")
          , ("Radiation_transport", "Radiative_transfer")
          , ("Radical_%28chemistry%29", "Radical_(chemistry)")
          , ("Radiologists", "Radiology")
          , ("Radium-226", "Isotopes_of_radium#Radium-226")
          , ("Rag-picker", "Rag-and-bone_man")
          , ("Ragdoll_cat", "Ragdoll")
          , ("Ragpickers", "Rag-and-bone_man")
          , ("Rags", "Textile")
          , ("Raid_on_Alexandria_%281941%29", "Raid_on_Alexandria_(1941)")
          , ("Railguns", "Railgun")
          , ("Rainbow_Dash", "List_of_mainline_My_Little_Pony_ponies#Rainbow_Dash")
          , ("Rainbow_tables", "Rainbow_table")
          , ("Raleigh%2C_North_Carolina", "Raleigh,_North_Carolina")
          , ("Ralph_C._Merkle", "Ralph_Merkle")
          , ("Ralph_P._Boas_Junior", "Ralph_P._Boas_Jr.")
          , ("Raman_effect", "Raman_scattering")
          , ("Raman_microscopy", "Raman_microscope")
          , ("Ramanujan", "Srinivasa_Ramanujan")
          , ("Ramjets", "Ramjet")
          , ("Ran_Ito", "Ran_It%C5%8D")
          , ("Rand_Corporation", "RAND_Corporation")
          , ("Random-effects_meta-analysis", "Random_effects_model")
          , ("Random_error", "Observational_error#Random")
          , ("Random_forests", "Random_forest")
          , ("Random_number_generator", "Random_number_generation")
          , ("Randomized_algorithms", "Randomized_algorithm")
          , ("Randomized_block_design", "Blocking_(statistics)")
          , ("Randomized_controlled_experiments", "Randomized_controlled_trial")
          , ("Randomized_experiments", "Randomized_experiment")
          , ("Range_restriction", "Statistical_conclusion_validity#Restriction_of_range")
          , ("Rapamycin", "Sirolimus")
          , ("Rape_of_Nanking", "Nanjing_Massacre")
          , ("Rapid_visual_information_processing", "Rapid_serial_visual_presentation")
          , ("Rare_earths", "Rare-earth_element")
          , ("Rashomon_(film)", "Rashomon")
          , ("Raven%27s_Advanced_Progressive_Matrices", "Raven%27s_Progressive_Matrices")
          , ("Raven%27s_progressive_matrices", "Raven%27s_Progressive_Matrices")
          , ("Raven's_Advanced_Progressive_Matrices", "Raven%27s_Progressive_Matrices")
          , ("Raven's_Matrices", "Raven%27s_Progressive_Matrices")
          , ("Raven's_Progressive_Matrices", "Raven%27s_Progressive_Matrices")
          , ("Raven's_matrices", "Raven%27s_matrices")
          , ("Raven's_progressive_matrices", "Raven%27s_Progressive_Matrices")
          , ("Raven_Progressive_Matrices", "Raven%27s_Progressive_Matrices")
          , ("Rayleigh-Taylor_instability", "Rayleigh%E2%80%93Taylor_instability")
          , ("Re:Zero_%E2%88%92_Starting_Life_in_Another_World", "Re:Zero")
          , ("ReLU", "Rectifier_(neural_networks)")
          , ("React_(JavaScript_library)", "React_(software)")
          , ("Reaction-diffusion_model", "Reaction%E2%80%93diffusion_system")
          , ("Reaction_time", "Mental_chronometry")
          , ("Read-eval-print_loop", "Read%E2%80%93eval%E2%80%93print_loop")
          , ("Reader's_Digest", "Reader%27s_Digest")
          , ("Reading_the_Mind_in_the_Eyes_Test", "Simon_Baron-Cohen#Developmental_social_cognitive_neuroscience")
          , ("Real_Housewives_of_Orange_County", "The_Real_Housewives_of_Orange_County")
          , ("Real_estate_bubble", "Real-estate_bubble")
          , ("Real_option", "Real_options_valuation")
          , ("Reboot_%28fiction%29", "Reboot_(fiction)")
          , ("Recap_(software)", "Free_Law_Project#RECAP")
          , ("Recessive_gene", "Dominance_(genetics)")
          , ("Recoilless_rifles", "Recoilless_rifle")
          , ("Recommender_Systems", "Recommender_system")
          , ("Recommender_systems", "Recommender_system")
          , ("Recovered_memory", "Repressed_memory")
          , ("Recreational_marijuana", "Cannabis_(drug)")
          , ("Recumbent_bicycles", "Recumbent_bicycle")
          , ("Recurrent_neural_networks", "Recurrent_neural_network")
          , ("Red-black_tree", "Red%E2%80%93black_tree")
          , ("Red_%282010_film%29", "Red_(2010_film)")
          , ("Red_Cross_Book", "The_End_of_Evangelion#Red_Cross_Book")
          , ("Red_Queen%27s_Hypothesis", "Red_Queen_hypothesis")
          , ("Red_Queen's_Hypothesis", "Red_Queen_hypothesis")
          , ("Reduce_(parallel_pattern)", "Reduction_operator")
          , ("Reduced_instruction_set_computing", "Reduced_instruction_set_computer")
          , ("Reduction_gears", "Gear_train")
          , ("Reed_Elsevier", "RELX")
          , ("Reed’s_Law", "Reed%E2%80%99s_Law")
          , ("Reed’s_law", "Reed%27s_law")
          , ("Regal_Entertainment_Group", "Regal_Cinemas")
          , ("Regeneron", "Regeneron_Pharmaceuticals")
          , ("Registered_report", "Preregistration_(science)#Registered_reports")
          , ("Regression_discontinuity", "Regression_discontinuity_design")
          , ("Regression_to_the_mean", "Regression_toward_the_mean")
          , ("Regular_expressions", "Regular_expression")
          , ("Reiki_healing", "Reiki")
          , ("Reindeers", "Reindeer")
          , ("Rejection_therapy", "Rejection_Therapy")
          , ("Relation_extraction", "Relationship_extraction")
          , ("Relationship_between_gravity_and_quantum_mechanics", "Quantum_gravity")
          , ("Relative_risks", "Relative_risk")
          , ("Relativistic_kill_vehicle", "Kinetic_energy_weapon")
          , ("Reliability_%28statistics%29", "Reliability_(statistics)")
          , ("Remainers", "Glossary_of_Brexit_terms#Remainer")
          , ("Remorselessness", "Remorse#Psychopathy")
          , ("Removal_of_Sam_Altman", "Removal_of_Sam_Altman_from_OpenAI")
          , ("Ren'Py", "Ren%27Py")
          , ("Rene_Descartes", "Ren%C3%A9_Descartes")
          , ("Renormalized", "Renormalization")
          , ("Rent_control_in_New_York", "Rent_regulation_in_New_York")
          , ("Rent_seeking", "Rent-seeking")
          , ("Repeated-measures_analyses", "Repeated_measures_design")
          , ("Repeated_prisoner%27s_dilemma", "Prisoner%27s_dilemma#The_iterated_prisoner%27s_dilemma")
          , ("Repeated_prisoner's_dilemma", "Prisoner%27s_dilemma#The_iterated_prisoner%27s_dilemma")
          , ("Repl.it", "Replit")
          , ("Replication_Crisis", "Replication_crisis")
          , ("Representation_learning", "Feature_learning")
          , ("Reproductive-Cell_Cycle_Theory", "Reproductive-cell_cycle_theory")
          , ("Republican_Contract_with_America", "Contract_with_America")
          , ("Repugnant_Conclusion", "Mere_addition_paradox")
          , ("Research_chemicals", "Research_chemical")
          , ("Research_on_meditation", "Effects_of_meditation")
          , ("Resistance_training", "Strength_training")
          , ("Restraining_orders", "Restraining_order")
          , ("Restricted_Boltzmann_machines", "Restricted_Boltzmann_machine")
          , ("Result-blind_peer_review", "Scholarly_peer_review#Result-blind_peer_review")
          , ("Retinal_photographs", "Fundus_photography")
          , ("Retrieval-augmented_generation", "Prompt_engineering#Retrieval-augmented_generation")
          , ("Revan", "Characters_of_the_Star_Wars:_Knights_of_the_Old_Republic_series#Revan")
          , ("Revealed_preferences", "Revealed_preference")
          , ("Reversible_jump_MCMC", "Reversible-jump_Markov_chain_Monte_Carlo")
          , ("Reversible_universal_computing", "Reversible_computing")
          , ("Revision_control", "Version_control")
          , ("Revolutionary_France", "French_Revolution")
          , ("Reward_function", "Reinforcement_learning")
          , ("Reye%27s_syndrome", "Reye_syndrome")
          , ("Reye's_syndrome", "Reye_syndrome")
          , ("Reynard", "Reynard_the_Fox")
          , ("Rhesus_monkeys", "Rhesus_macaque")
          , ("Rheumatoid_Arthritis", "Rheumatoid_arthritis")
          , ("Rhyme_as_reason_effect", "Rhyme-as-reason_effect")
          , ("Ricardo%27s_theorem", "Comparative_advantage#Classical_theory_and_Ricardo%27s_formulation")
          , ("Ricardo's_theorem", "Comparative_advantage#Classical_theory_and_Ricardo%27s_formulation")
          , ("Rice%27s_Theorem", "Rice%27s_theorem")
          , ("Rice's_Theorem", "Rice%27s_theorem")
          , ("Rice's_theorem", "Rice%27s_theorem")
          , ("Richard_Karp", "Richard_M._Karp")
          , ("Richard_Nisbett", "Richard_E._Nisbett")
          , ("Richard_W._Hamming", "Richard_Hamming")
          , ("Rielle_Hunter", "John_Edwards_extramarital_affair")
          , ("Rifampin", "Rifampicin")
          , ("Right-handedness", "Handedness")
          , ("Right-wing_authoritarianism", "Right-wing_authoritarian_personality")
          , ("Right_to_fork", "Fork_(software_development)#Forking_of_free_and_open-source_software")
          , ("Ring_world", "Ringworld")
          , ("Ripple_(monetary_system)", "Ripple_(payment_protocol)")
          , ("Rising_Sun_(novel)", "Rising_Sun_(Crichton_novel)")
          , ("Risk-taking", "Risk")
          , ("Risk_averse", "Risk_aversion")
          , ("Risk_homeostasis", "Risk_compensation#Risk_homeostasis")
          , ("Ritsurin_Park", "Ritsurin_Garden")
          , ("Roar_(1981_film)", "Roar_(film)")
          , ("Roasted_barley_tea", "Barley_tea")
          , ("Robert_Bakewell_%28agriculturalist%29", "Robert_Bakewell_(agriculturalist)")
          , ("Robert_Bakewell_(farmer)", "Robert_Bakewell_(agriculturalist)")
          , ("Robert_Burton_(scholar)", "Robert_Burton")
          , ("Robert_C._Tryon", "Robert_Tryon")
          , ("Robert_Heinlein", "Robert_A._Heinlein")
          , ("Robert_Iger", "Bob_Iger")
          , ("Robert_Oppenheimer", "J._Robert_Oppenheimer")
          , ("Robert_Wilhelm_Bunsen", "Robert_Bunsen")
          , ("Robots_exclusion_standard", "Robots.txt")
          , ("Robust_standard_errors", "Robust_measures_of_scale")
          , ("Rocket_equation", "Tsiolkovsky_rocket_equation")
          , ("Rocko's_Modern_Life", "Rocko%27s_Modern_Life")
          , ("Rodion_Romanovich_Raskolnikov", "Rodion_Raskolnikov")
          , ("Rodong-1", "Hwasong-7")
          , ("Roff_(computer_program)", "Roff_(software)")
          , ("Roger_Craig_(Jeopardy%21_contestant)", "Roger_Craig_(Jeopardy!_contestant)")
          , ("Rogue_Male_%28novel%29", "Rogue_Male_(novel)")
          , ("Rogue_One:_A_Star_Wars_Story", "Rogue_One")
          , ("Rohirrim", "Rohan,_Middle-earth")
          , ("Rolling_Stones", "The_Rolling_Stones")
          , ("Roman_emperors", "Roman_emperor")
          , ("Romanian_Revolution", "Romanian_revolution")
          , ("Romanoffs", "House_of_Romanov")
          , ("Romantic_relationship", "Romance_(love)")
          , ("Ronald_A._Fisher", "Ronald_Fisher")
          , ("Room_Key", "Roomkey")
          , ("Root_mean_square_error", "Root-mean-square_deviation")
          , ("Rosetta%40home", "Rosetta@home")
          , ("Rosetta_Disk", "HD-Rosetta")
          , ("Rosetta_disk", "HD-Rosetta")
          , ("Rosmarinus_officinalis", "Rosemary")
          , ("Rotor_kites", "Rotor_kite")
          , ("Round-bottom_flasks", "Round-bottom_flask")
          , ("Roux-en-Y_gastric_bypass", "Gastric_bypass_surgery#Surgical_techniques")
          , ("Rowhammer", "Row_hammer")
          , ("Royal_Christmas_Message", "Royal_Christmas_message")
          , ("Royal_Institution_of_Great_Britain", "Royal_Institution")
          , ("Rubik%27s_cube", "Rubik%27s_Cube")
          , ("Rubik's_cube", "Rubik%27s_Cube")
          , ("Rubik_cube", "Rubik%27s_Cube")
          , ("Ruby_programming_language", "Ruby_(programming_language)")
          , ("Rule_of_three_(medicine)", "Rule_of_three_(statistics)")
          , ("Rules_of_Engagement_(Elizabeth_Moon_novel)", "Elizabeth_Moon")
          , ("Ruminococcaceae", "Oscillospiraceae")
          , ("Runescape", "RuneScape")
          , ("Runge-Kutta_method", "Runge%E2%80%93Kutta_methods")
          , ("Rupees", "Rupee")
          , ("Russell's_teapot", "Russell%27s_teapot")
          , ("Russian_Domesticated_Red_Fox", "Domesticated_silver_fox")
          , ("Russian_Hill", "Russian_Hill,_San_Francisco")
          , ("Russian_Revolution_of_February_1917", "February_Revolution")
          , ("Russian_invasion_of_Ukraine_(2022%E2%80%93present)", "Russian_invasion_of_Ukraine")
          , ("Ryszard_Kapuscinski", "Ryszard_Kapu%C5%9Bci%C5%84ski")
          , ("Ryu_Murakami", "Ry%C5%AB_Murakami")
          , ("S-expressions", "S-expression")
          , ("S._mediterranea", "Schmidtea_mediterranea")
          , ("SALT_I", "Strategic_Arms_Limitation_Talks#SALT_I_Treaty")
          , ("SCI_Foundation", "Unlimit_Health")
          , ("SCN9A", "Nav1.7")
          , ("SGLT2_inhibitors", "SGLT2_inhibitor")
          , ("SHA-256", "SHA-2")
          , ("SIMD", "Single_instruction,_multiple_data")
          , ("SIM_cards", "SIM_card")
          , ("SIR2", "Sirtuin_1#Sir2")
          , ("SLAPP", "Strategic_lawsuit_against_public_participation")
          , ("SLBM", "Submarine-launched_ballistic_missile")
          , ("SLBMs", "Submarine-launched_ballistic_missile")
          , ("SLC12A5", "Chloride_potassium_symporter_5")
          , ("SLC30A8", "Zinc_transporter_8")
          , ("SMILES", "Simplified_molecular-input_line-entry_system")
          , ("SMRT_sequencing", "Single-molecule_real-time_sequencing")
          , ("SNES", "Super_Nintendo_Entertainment_System")
          , ("SOFT_HYPHEN", "Soft_hyphen#Encodings_and_definitions")
          , ("SPECfp95", "SPECfp#SPECfp95")
          , ("SPECint95", "SPECint")
          , ("SS", "Schutzstaffel")
          , ("SSRIs", "Selective_serotonin_reuptake_inhibitor")
          , ("SS_America_(1940)", "SS_America_(1939)")
          , ("STT-RAM", "Spin-transfer_torque#Spin-transfer_torque_memory")
          , ("SUTVA", "Rubin_causal_model#Stable_unit_treatment_value_assumption_(SUTVA)")
          , ("S_Corporation", "S_corporation")
          , ("Sack_lunches", "Packed_lunch")
          , ("SafeGraph", "Auren_Hoffman#Safegraph")
          , ("Saga_of_Tanya_the_Evil", "The_Saga_of_Tanya_the_Evil")
          , ("Saigy%C3%B4", "Saigy%C5%8D")
          , ("Saint_Augustine", "Augustine_of_Hippo")
          , ("Salk_vaccine", "Polio_vaccine")
          , ("Salomon's_House", "Salomon%27s_House")
          , ("Salt_iodization", "Iodised_salt")
          , ("Samaranch", "Juan_Antonio_Samaranch")
          , ("San_Carlos_de_Bariloche", "Bariloche")
          , ("San_Mateo-Hayward_Bridge", "San_Mateo%E2%80%93Hayward_Bridge")
          , ("San_serif", "Sans-serif")
          , ("Sanctions_against_Iraq", "International_sanctions_against_Iraq")
          , ("Sanford_Bernstein", "AllianceBernstein")
          , ("Sapir-Whorf", "Linguistic_relativity")
          , ("Sapir-Whorf_hypothesis", "Linguistic_relativity")
          , ("Sarin_gas", "Sarin")
          , ("Sarin_gas_attack_on_the_Tokyo_subway", "Tokyo_subway_sarin_attack")
          , ("Sarnoff%27s_law", "David_Sarnoff")
          , ("Sarnoff’s_Law", "Sarnoff%E2%80%99s_Law")
          , ("Sarnoff’s_law", "Sarnoff%E2%80%99s_law")
          , ("Sasaki_Kojiro", "Sasaki_Kojir%C5%8D")
          , ("Savikalpa_samadhi", "Samadhi#Hinduism")
          , ("Savoy_dynasty", "House_of_Savoy")
          , ("Say%27s_Law", "Say%27s_law")
          , ("Say's_Law", "Say%27s_law")
          , ("Scalable_Vector_Graphics", "SVG")
          , ("Scalable_vector_graphics", "SVG")
          , ("Scale-free_networks", "Scale-free_network")
          , ("Scatter_plots", "Scatter_plot")
          , ("Scatterplots", "Scatter_plot")
          , ("Schedule_IV", "Controlled_Substances_Act#Schedule_IV_controlled_substances")
          , ("Schistosomiasis_Control_Initiative", "Unlimit_Health")
          , ("Schizophrenia_and_smoking", "Schizophrenia_and_tobacco_smoking")
          , ("Schizotypal", "Schizotypal_personality_disorder")
          , ("School-Live%21", "School-Live!")
          , ("School_attacks_in_China_(2010-12)", "School_attacks_in_China")
          , ("School_day", "Academic_year#School_holiday")
          , ("Sci-fi_conventions", "Science_fiction_convention")
          , ("Science_Daily", "ScienceDaily")
          , ("Science_the_endless_frontier", "Vannevar_Bush#National_Science_Foundation")
          , ("Scientific_computing", "Computational_science")
          , ("Scientific_peer_review", "Scholarly_peer_review")
          , ("Scooby-Doo,_Where_Are_You%21", "Scooby-Doo,_Where_Are_You!")
          , ("Scope_Neglect", "Scope_neglect")
          , ("Score_%28statistics%29", "Informant_(statistics)")
          , ("Score_(statistics)", "Informant_(statistics)")
          , ("Scoring_rules", "Scoring_rule")
          , ("Scotch_broom", "Cytisus_scoparius")
          , ("Scotland_Yard_", "Scotland_Yard")
          , ("Scoville_heat_units", "Scoville_scale")
          , ("Screen_readers", "Screen_reader")
          , ("Scribal_abbreviations", "Scribal_abbreviation")
          , ("Search_and_seizure_law", "Search_and_seizure")
          , ("Search_warrants", "Search_warrant")
          , ("Second-price_auction", "Vickrey_auction")
          , ("Second_moment", "Moment_(mathematics)#Variance")
          , ("Second_moments", "Moment_(mathematics)#Variance")
          , ("Secretaries", "Secretary")
          , ("Secure_multiparty_computation", "Secure_multi-party_computation")
          , ("Seedbanks", "Seed_bank")
          , ("Seeds_in_the_Heart:_Japanese_Literature_from_Earliest_Times_to_the_Late_Sixteenth_Century", "Seeds_in_the_Heart")
          , ("Segmented_sleep", "Polyphasic_sleep")
          , ("Seigenthaler_incident", "Wikipedia_Seigenthaler_biography_incident")
          , ("Seikai_no_Monsho", "Crest_of_the_Stars")
          , ("Selectman", "Select_board")
          , ("Self-adhesive_stamps", "Self-adhesive_stamp")
          , ("Self-enhancement_bias", "Self-enhancement")
          , ("Self-promotion", "Promotion_(marketing)")
          , ("Self-report", "Self-report_study")
          , ("Self-talk", "Intrapersonal_communication")
          , ("Selfing", "Autogamy")
          , ("Seligiline", "Selegiline")
          , ("Semantic_priming", "Priming_(psychology)#Semantic_priming")
          , ("Semantic_segmentation", "Image_segmentation")
          , ("Semi-Supervised_Learning", "Weak_supervision")
          , ("Semi-supervised_learning", "Weak_supervision")
          , ("Semigroups", "Semigroup")
          , ("Senate_Committee_on_Armed_Services", "United_States_Senate_Committee_on_Armed_Services")
          , ("Senate_Subcommittee_on_Space,_Science_and_Competitiveness", "United_States_Senate_Commerce_Subcommittee_on_Space_and_Science")
          , ("Senchas_M%C3%A1r", "Early_Irish_law#Senchas_M%C3%A1r")
          , ("Senescent_cells", "Cellular_senescence")
          , ("Senolytics", "Senolytic")
          , ("Sensitive_Compartmented_Information_Facility", "Sensitive_compartmented_information_facility")
          , ("Sentence_spacing_studies", "Sentence_spacing#Effects_on_readability_and_legibility")
          , ("Separatory_funnels", "Separatory_funnel")
          , ("Sepedi", "Northern_Sotho")
          , ("Sepracor", "Sunovion")
          , ("Septuplets", "Multiple_birth#Very_high-order_multiple_births")
          , ("Sequence_conservation", "Conserved_sequence")
          , ("Serial_killers", "Serial_killer")
          , ("Serial_position_effect", "Serial-position_effect")
          , ("Serotonin_storm", "Serotonin_syndrome")
          , ("Serotonin_uptake_inhibitors", "Selective_serotonin_reuptake_inhibitor")
          , ("Servier", "Laboratoires_Servier")
          , ("Seti%40home", "SETI@home")
          , ("Settlers_of_Catan", "Catan")
          , ("Seven_Basic_Plots", "The_Seven_Basic_Plots")
          , ("Seven_Should-not-plays", "Seven_Should-not-plays_(Chinese_Music)")
          , ("Seven_Sisters_(oil_companies)", "Big_Oil")
          , ("Seven_Years_War", "Seven_Years%27_War")
          , ("Severe_acute_respiratory_syndrome_coronavirus_2", "SARS-CoV-2")
          , ("Severely_Deficient_Autobiographical_Memory", "Autobiographical_memory#Individual_differences")
          , ("Sex_and_gender_distinction", "Sex%E2%80%93gender_distinction")
          , ("Sex_steroid", "Sex_hormone")
          , ("Sexual_coercion", "Rape")
          , ("Shadow_DOM", "Web_Components#Shadow_DOM")
          , ("Shah_Mohammad_Reza_Pahlavi", "Mohammad_Reza_Pahlavi")
          , ("Shaka_Zulu", "Shaka")
          , ("Shakespeare's_sonnets", "Shakespeare%27s_sonnets")
          , ("Shamir%27s_Secret_Sharing", "Shamir%27s_secret_sharing")
          , ("Shamir's_Secret_Sharing", "Shamir%27s_secret_sharing")
          , ("Shandong_Province", "Shandong")
          , ("Shanghai,_China", "Shanghai")
          , ("Shannon%27s_theorem", "Noisy-channel_coding_theorem")
          , ("Shannon's_theorem", "Noisy-channel_coding_theorem")
          , ("Shannon-McMillan-Breiman_theorem", "Asymptotic_equipartition_property#Discrete-time_finite-valued_stationary_ergodic_sources")
          , ("Shannon-Wiener_index", "Diversity_index#Shannon_index")
          , ("Shannon_entropy", "Entropy_(information_theory)")
          , ("Shannon_limit", "Noisy-channel_coding_theorem")
          , ("Sharp-P-complete", "%E2%99%AFP-complete")
          , ("Sharp_wave%E2%80%93ripple_complexes", "Sharp_waves_and_ripples")
          , ("Sharpie_%28marker%29", "Sharpie_(marker)")
          , ("Shays'_Rebellion", "Shays%27_Rebellion")
          , ("She,_the_Ultimate_Weapon", "Saikano")
          , ("Sheldon_Lee_Glashow", "Sheldon_Glashow")
          , ("Shepard_tones", "Shepard_tone")
          , ("Shift_worker", "Shift_work")
          , ("Shift_workers", "Shift_work")
          , ("Shih_tzu", "Shih_Tzu")
          , ("Shiki_(novel_series)", "Shiki_(novel)")
          , ("Shikijitsu", "Shiki-Jitsu")
          , ("Shimane", "Shimane_Prefecture")
          , ("Shin_Kokin_Wakashu", "Shin_Kokin_Wakash%C5%AB")
          , ("Shinigamis", "Shinigami")
          , ("Shinseiki_Evangelion_-_Kotetsu_no_Girlfriend", "Neon_Genesis_Evangelion:_Girlfriend_of_Steel")
          , ("Shinseiki_Evangelion_Gekijoban_-_Shito_Shinsei", "Neon_Genesis_Evangelion:_Death_%26_Rebirth")
          , ("Shinseiki_Evangelion_Gekijoban_-_The_End_of_Evangelion", "The_End_of_Evangelion")
          , ("Ships'_cats", "Ship%27s_cat")
          , ("Shirokuma_Cafe", "Polar_Bear_Caf%C3%A9")
          , ("Shmup", "Shoot_%27em_up")
          , ("Shoegazing", "Shoegaze")
          , ("Shoemaker-Levy", "Comet_Shoemaker%E2%80%93Levy_9")
          , ("Shoji_Kawamori", "Sh%C5%8Dji_Kawamori")
          , ("Shonen_Ace", "Monthly_Sh%C5%8Dnen_Ace")
          , ("Shonen_Jump", "Weekly_Sh%C5%8Dnen_Jump")
          , ("Shonen_Magazine", "Weekly_Sh%C5%8Dnen_Magazine")
          , ("Shonen_Sunday", "Weekly_Sh%C5%8Dnen_Sunday")
          , ("Shooting_skeet", "Skeet_shooting")
          , ("Shor%27s_factorization", "Shor%27s_algorithm")
          , ("Shor's_algorithm", "Shor%27s_algorithm")
          , ("Shor's_factorization", "Shor%27s_algorithm")
          , ("Short-beaked_common_dolphin", "Common_dolphin")
          , ("Short-form_video", "Video_clip#Short-form_video_clips")
          , ("Short_tandem_repeats", "Microsatellite")
          , ("Shortest_path_tree", "Shortest-path_tree")
          , ("Shotetsu", "Sh%C5%8Dtetsu")
          , ("Shoujo_Kakumei_Utena", "Revolutionary_Girl_Utena")
          , ("Showa_period", "Sh%C5%8Dwa_era")
          , ("Shuichi_Ikeda", "Sh%C5%ABichi_Ikeda")
          , ("Shuji_Terayama", "Sh%C5%ABji_Terayama")
          , ("Shunzei%27s_Daughter", "Shunzei%27s_daughter")
          , ("Shunzei's_Daughter", "Shunzei%27s_daughter")
          , ("Sia_(musician)", "Sia")
          , ("Siamese_cats", "Siamese_cat")
          , ("Siberian_(cat)", "Siberian_cat")
          , ("Siberian_husky_breed", "Siberian_Husky")
          , ("Sickle-cell_anemia", "Sickle_cell_disease")
          , ("Side-channel_attacks", "Side-channel_attack")
          , ("Sikh", "Sikhs")
          , ("Silent_Mobius", "Silent_M%C3%B6bius")
          , ("Silexan", "Lavender_oil")
          , ("Silica-coated_iron_oxide_nanoparticles", "Magnetic_nanoparticles#Ferrites_with_a_shell")
          , ("Silk_Road_%28anonymous_marketplace%29", "Silk_Road_(marketplace)")
          , ("Silk_Road_(anonymous_marketplace)", "Silk_Road_(marketplace)")
          , ("Silk_Road_2", "Silk_Road_(marketplace)#Silk_Road_2.0")
          , ("Silmarillion", "The_Silmarillion")
          , ("Silver_Spoon", "Silver_spoon")
          , ("Silvervine", "Actinidia_polygama")
          , ("Silver_vine", "Actinidia_polygama")
          , ("Simcard", "SIM_card")
          , ("Simeon_Denis_Poisson", "Sim%C3%A9on_Denis_Poisson")
          , ("Simpson's_paradox", "Simpson%27s_paradox")
          , ("Simulation_Argument", "Simulation_hypothesis")
          , ("Sina_Weibo", "Weibo")
          , ("Sindh,_Pakistan", "Sindh")
          , ("Single_cell_sequencing", "Single-cell_sequencing")
          , ("Singularity_(William_Sleator_novel)", "Singularity_(Sleator_novel)")
          , ("Sirtuins", "Sirtuin")
          , ("Six_Avoidances", "Six_Avoidances_(Chinese_Music)")
          , ("Sixels", "Sixel")
          , ("Skaldic_poetry", "Skald#Skaldic_poetry")
          , ("Skin_galvanic_response", "Electrodermal_activity")
          , ("Skinner_Boxes", "Operant_conditioning_chamber")
          , ("Skybox_Imaging", "SkySat")
          , ("Slate_Magazine", "Slate_(magazine)")
          , ("Slave_trade", "History_of_slavery")
          , ("Sled_dogs", "Sled_dog")
          , ("Sleds", "Sled")
          , ("Sleep%E2%80%93wake_cycle", "Circadian_rhythm")
          , ("Sleep_disturbance", "Sleep_disorder")
          , ("Slide_projectors", "Slide_projector")
          , ("Slime_mould", "Slime_mold")
          , ("Slow_wave_sleep", "Slow-wave_sleep")
          , ("Small-world_networks", "Small-world_network")
          , ("Small_RNAs", "Small_RNA")
          , ("Small_cell_lung_cancer", "Small-cell_carcinoma")
          , ("Small_multiples", "Small_multiple")
          , ("Small_presses", "Small_press")
          , ("Smallpox_vaccination", "Smallpox_vaccine")
          , ("Smart_cards", "Smart_card")
          , ("Snake_eels", "Ophichthidae")
          , ("Snakes_in_Suits:_When_Psychopaths_Go_to_Work", "Snakes_in_Suits")
          , ("Snappy_(software)", "Snap_(software)")
          , ("Snell's_law", "Snell%27s_law")
          , ("Snow_leopards", "Snow_leopard")
          , ("Social_animal", "Sociality")
          , ("Social_desirability_bias", "Social-desirability_bias")
          , ("Social_dilemma", "Collective_action_problem")
          , ("Social_engineering_%28security%29", "Social_engineering_(security)")
          , ("Social_hierarchy", "Social_stratification")
          , ("Social_homogamy", "Homogamy_(sociology)")
          , ("Social_program", "Welfare")
          , ("Social_psychology_(psychology)", "Social_psychology")
          , ("Social_role_theory", "Role_theory")
          , ("SoftBank", "SoftBank_Group")
          , ("Soft_determinism", "Compatibilism#Criticism")
          , ("Soft_science", "Hard_and_soft_science")
          , ("Softbank", "SoftBank_Group")
          , ("Softmax", "Softmax_function")
          , ("Softool", "Leon_Presser")
          , ("Soil_nematode", "Nematode#Soil_ecosystems")
          , ("Solar_sails", "Solar_sail")
          , ("Solar_thermal_power", "Concentrated_solar_power")
          , ("Solar_water_heaters", "Solar_water_heating")
          , ("Soldiers'_Pay", "Soldiers%27_Pay")
          , ("Solenopsis_invicta", "Red_imported_fire_ant")
          , ("Solitaire_cipher", "Solitaire_(cipher)")
          , ("Solomonoff's_theory_of_inductive_inference", "Solomonoff%27s_theory_of_inductive_inference")
          , ("Solzhenitsyn", "Aleksandr_Solzhenitsyn")
          , ("Somatosensory_cortex", "Somatosensory_system#Somatosensory_cortex")
          , ("Somtow_Sucharitkul", "S._P._Somtow")
          , ("Song_of_Solomon", "Song_of_Songs")
          , ("Sony_Handycam", "Handycam")
          , ("Sony_Music_Entertainment", "Sony_Music")
          , ("Sooam", "Hwang_Woo-suk")
          , ("Sorting_algorithms", "Sorting_algorithm")
          , ("Sorting_networks", "Sorting_network")
          , ("Soul_Eater_(manga)", "Soul_Eater")
          , ("Sound_cards", "Sound_card")
          , ("Soup_Nazi", "The_Soup_Nazi")
          , ("Source_Sans_Pro", "Source_Sans")
          , ("Source_Serif_Pro", "Source_Serif")
          , ("Sourceforge", "SourceForge")
          , ("Sous-vide", "Sous_vide")
          , ("South_China_Seas", "South_China_Sea")
          , ("South_Sea_Bubble", "South_Sea_Company")
          , ("Sox2", "SOX2")
          , ("Soy_lecithin", "Lecithin")
          , ("Space-time", "Spacetime")
          , ("Space-time_tradeoff", "Space%E2%80%93time_tradeoff")
          , ("Space_exposure", "Effect_of_spaceflight_on_the_human_body#Space_environments")
          , ("Space_stations", "Space_station")
          , ("Spaced_practice", "Distributed_practice")
          , ("Spacewar_(video_game)", "Spacewar!")
          , ("Spaghetti_plots", "Spaghetti_plot")
          , ("Spanish-American_War", "Spanish%E2%80%93American_War")
          , ("Spanish_Tragedy", "The_Spanish_Tragedy")
          , ("Spanish_influenza", "Spanish_flu")
          , ("Sparklines", "Sparkline")
          , ("Sparse_Distributed_Memory", "Sparse_distributed_memory")
          , ("Sparse_coding", "Neural_coding#Sparse_coding")
          , ("Spartans", "Sparta")
          , ("Spear_phishing", "Phishing#CITEREFrajput2023")
          , ("Spearman%27s_correction", "Regression_dilution#Correlation_correction")
          , ("Spearman's_correction", "Regression_dilution#Correlation_correction")
          , ("Spearman's_rank_correlation_coefficient", "Spearman%27s_rank_correlation_coefficient")
          , ("Spearman_rank_correlation", "Spearman%27s_rank_correlation_coefficient")
          , ("Spearphishing", "Phishing#Spear_phishing")
          , ("Special-purpose_district", "Special_district_(United_States)")
          , ("Special:Allpages", "Special:AllPages")
          , ("Special:Newpages", "Special:NewPages")
          , ("Specific_gravity", "Relative_density")
          , ("Spectrograms", "Spectrogram")
          , ("Speed-reading", "Speed_reading")
          , ("Speed_chess", "Fast_chess")
          , ("Speedrun", "Speedrunning")
          , ("Sperm_count", "Semen_analysis")
          , ("Spider-Man_(Toei_TV_series)", "Spider-Man_(Japanese_TV_series)")
          , ("Spielberg", "Steven_Spielberg")
          , ("Spike-time-dependent_plasticity", "Spike-timing-dependent_plasticity")
          , ("Spiking_neural_networks", "Spiking_neural_network")
          , ("Sporting_Chance", "Elizabeth_Moon")
          , ("Sporulation", "Spore")
          , ("Spotted_towhees", "Spotted_towhee")
          , ("Sprague_Dawley_rat", "Laboratory_rat#Sprague_Dawley_rat")
          , ("Spreadsheets", "Spreadsheet")
          , ("Spruce_budworm", "Choristoneura")
          , ("Spurious_correlation", "Spurious_relationship")
          , ("Sputnik", "Sputnik_1")
          , ("Square_(financial_services_company)", "Block,_Inc.")
          , ("Square_packing_in_a_square", "Square_packing")
          , ("St._Cyrus", "St_Cyrus")
          , ("St._George%27s_Day", "Saint_George%27s_Day")
          , ("St._George's_Day", "Saint_George%27s_Day")
          , ("St._John%27s_Wort", "Hypericum_perforatum")
          , ("St._John's_Wort", "Hypericum_perforatum")
          , ("St._Louis_Art_Museum", "Saint_Louis_Art_Museum")
          , ("St._Petersburg", "Saint_Petersburg")
          , ("St_John's_College,_Cambridge", "St_John%27s_College,_Cambridge")
          , ("Stable_diffusion", "Stable_Diffusion")
          , ("Stable_time-loop", "Temporal_paradox#Causal_loop")
          , ("StackExchange", "Stack_Exchange")
          , ("Stalin", "Joseph_Stalin")
          , ("Stalin%27s_cult_of_personality", "Joseph_Stalin%27s_cult_of_personality")
          , ("Stalin_era", "History_of_the_Soviet_Union_(1927%E2%80%931953)")
          , ("Stand_On_Zanzibar", "Stand_on_Zanzibar")
          , ("Standard_errors", "Standard_error")
          , ("Stanford-Binet_Intelligence_Scales", "Stanford%E2%80%93Binet_Intelligence_Scales")
          , ("Stanford_Prison_Experiment", "Stanford_prison_experiment")
          , ("Stanislaw_Lem", "Stanis%C5%82aw_Lem")
          , ("Stanislaw_Ulam", "Stanis%C5%82aw_Ulam")
          , ("Star_Trek_%28text_game%29", "Star_Trek_(1971_video_game)")
          , ("Star_Wars:_A_New_Hope", "Star_Wars_(film)")
          , ("Star_Wars:_Knights_of_the_Old_Republic_II_The_Sith_Lords", "Star_Wars_Knights_of_the_Old_Republic_II:_The_Sith_Lords")
          , ("Starblazers", "Star_Blazers")
          , ("Starfruit", "Carambola")
          , ("Starlink_(satellite_constellation)", "Starlink")
          , ("State-Trait_of_Anxiety_Inventory", "State-Trait_Anxiety_Inventory")
          , ("State_machines", "Finite-state_machine")
          , ("State_transition_system", "Transition_system")
          , ("Statens_Pensjonsfond", "Government_Pension_Fund_of_Norway")
          , ("States'_rights", "States%27_rights")
          , ("Statins", "Statin")
          , ("Statistical-significance", "Statistical_significance")
          , ("Statistical_independence", "Independence_(probability_theory)")
          , ("Statistical_power", "Power_of_a_test")
          , ("Statistical_power_calculation", "Power_of_a_test")
          , ("Steady_state_economy", "Steady-state_economy")
          , ("SteamVR", "Steam_(service)#SteamVR")
          , ("Steam_(software)", "Steam_(service)")
          , ("Steam_Machine_(hardware_platform)", "Steam_Machine_(computer)")
          , ("Steepest_descent", "Gradient_descent")
          , ("Stein%27s_paradox", "Stein%27s_example")
          , ("Stein's_paradox", "Stein%27s_example")
          , ("Stem_cells", "Stem_cell")
          , ("Stephen's_Sausage_Roll", "Stephen%27s_Sausage_Roll")
          , ("Stepstone", "NeXT")
          , ("Stereotypes", "Stereotype")
          , ("Steve_Pinker", "Steven_Pinker")
          , ("Steven_Berlin_Johnson", "Steven_Johnson_(author)")
          , ("Stevens-Johnson_Syndrome", "Stevens%E2%80%93Johnson_syndrome")
          , ("Stevens-Johnson_syndrome", "Stevens%E2%80%93Johnson_syndrome")
          , ("Steward_Brand", "Stewart_Brand")
          , ("Stick-breaking", "Dirichlet_process#The_stick-breaking_process")
          , ("Stigler%27s_Law", "Stigler%27s_law_of_eponymy")
          , ("Stigler's_Law", "Stigler%27s_law_of_eponymy")
          , ("Still_Alice_%28novel%29", "Still_Alice_(novel)")
          , ("Stochastic_Petri_nets", "Stochastic_Petri_net")
          , ("Stochastic_differential_equations", "Stochastic_differential_equation")
          , ("Stochastic_processes", "Stochastic_process")
          , ("Stochastic_terrorism", "Lone_wolf_attack#Stochastic_terrorism")
          , ("Stock_pinning", "Pin_risk")
          , ("Stomach_acid", "Gastric_acid")
          , ("Stone_marten", "Beech_marten")
          , ("Strategic_disclosure", "Voluntary_disclosure")
          , ("Strategies_for_Engineered_Negligible_Senescence", "Strategies_for_engineered_negligible_senescence")
          , ("Stratospheric_sulfate_aerosols_(geoengineering)", "Stratospheric_aerosol_injection")
          , ("Stream_of_consciousness_(narrative_mode)", "Stream_of_consciousness")
          , ("Striatal", "Striatum")
          , ("Stripe_%28company%29", "Stripe,_Inc.")
          , ("Stripe_(company)", "Stripe,_Inc.")
          , ("Stromatolites", "Stromatolite")
          , ("Strong_prior", "Prior_probability#Strong_prior")
          , ("Structural_equation_model", "Structural_equation_modeling")
          , ("Struggle_sessions", "Struggle_session")
          , ("Stuart_E._Dreyfus", "Stuart_Dreyfus")
          , ("StubHub.com", "StubHub")
          , ("Student%27s_t", "Student%27s_t-distribution")
          , ("Student's_t", "Student%27s_t-distribution")
          , ("Student's_t-distribution", "Student%27s_t-distribution")
          , ("Student's_t-test", "Student%27s_t-test")
          , ("Students_for_Fair_Admissions_v._President_and_Fellows_of_Harvard_College", "Students_for_Fair_Admissions_v._Harvard")
          , ("Student’s_t-test", "Student%E2%80%99s_t-test")
          , ("Studio Gainax", "Gainax")
          , ("Studio_4%E2%84%83", "Studio_4%C2%B0C")
          , ("Studio_Gainax", "Gainax")
          , ("Studio_Gonzo", "Gonzo_(company)")
          , ("Studio_Khara", "Khara_(studio)")
          , ("Studio_Pierrot", "Pierrot_(company)")
          , ("Stylometrics", "Stylometry")
          , ("Su_Hui_%28poet%29", "Su_Hui_(poet)")
          , ("Submarine-launched_ballistic_missiles", "Submarine-launched_ballistic_missile")
          , ("Subscriber_identity_module", "SIM_card")
          , ("Subsistence_wage", "Living_wage")
          , ("Sudden_infant_death_syndrome", "SIDS")
          , ("Sufficient_statistics", "Sufficient_statistic")
          , ("Suga_Hiroe", "Hiroe_Suga")
          , ("Sugars", "Sugar")
          , ("Sulfonamide_(chemistry)", "Sulfonamide")
          , ("Sulphone", "Sulfone")
          , ("Sumi-e", "Ink_wash_painting")
          , ("Summit_supercomputer", "Summit_(supercomputer)")
          , ("Sumptuary_laws", "Sumptuary_law")
          , ("Sun_acquisition_by_Oracle", "Acquisition_of_Sun_Microsystems_by_Oracle_Corporation")
          , ("Sunk-cost_bias", "Sunk_cost#Fallacy_effect")
          , ("Sunk_cost_fallacy", "Sunk_cost#Fallacy_effect")
          , ("Supanova", "Supanova_Expo")
          , ("Super-Earths", "Super-Earth")
          , ("Super-radiance", "Superradiance")
          , ("Super-twisted_nematic_display", "STN_display")
          , ("Super_massive_black_hole", "Supermassive_black_hole")
          , ("Supercompilation", "Metacompilation")
          , ("Superconducting_logic", "Superconducting_computing")
          , ("Supermax", "Supermax_prison")
          , ("Supersingular_Isogeny_Key_Exchange", "Supersingular_isogeny_key_exchange")
          , ("Superstring", "Superstring_theory")
          , ("Supersweet_corn", "Sweet_corn#Genetics")
          , ("Support-vector_machine", "Support_vector_machine")
          , ("Support_Vector_Machine", "Support_vector_machine")
          , ("Support_Vector_Machines", "Support_vector_machine")
          , ("Surely_You%27re_Joking,_Mr._Feynman%21", "Surely_You%27re_Joking,_Mr._Feynman!")
          , ("Surely_You're_Joking,_Mr._Feynman%21", "Surely_You%27re_Joking,_Mr._Feynman!")
          , ("Survival_graph", "Survival_function")
          , ("Suspicion_%281941_film%29", "Suspicion_(1941_film)")
          , ("Swedish_Cancer_Society", "Cancerfonden")
          , ("Sweet_basil", "Basil")
          , ("Syllogisms", "Syllogism")
          , ("Syllogistic_reasoning", "Syllogism")
          , ("Synthesis_microkernel", "Self-modifying_code#Synthesis")
          , ("Synthetic_Mycoides", "Synthetic_mycoides")
          , ("Synthetic_civet", "Civetone")
          , ("Synthetic_control_group", "Synthetic_control_method")
          , ("System_V", "UNIX_System_V")
          , ("Systematic_error", "Observational_error")
          , ("T-Mobile_Sidekick", "Danger_Hiptop")
          , ("T-SNE", "T-distributed_stochastic_neighbor_embedding")
          , ("T-cell", "T_cell")
          , ("T-test", "Student%27s_t-test")
          , ("T.S._Eliot", "T._S._Eliot")
          , ("T.co", "Twitter#URL_shortener")
          , ("TALENs", "Transcription_activator-like_effector_nuclease")
          , ("TANSTAAFL", "No_such_thing_as_a_free_lunch")
          , ("TAOCP", "The_Art_of_Computer_Programming")
          , ("TCP/IP", "Internet_protocol_suite")
          , ("TI-83", "TI-83_series")
          , ("TOEFL", "Test_of_English_as_a_Foreign_Language")
          , ("TP53", "P53")
          , ("T_cell_receptor", "T-cell_receptor")
          , ("T_cells", "T_cell")
          , ("Tab-completion", "Command-line_completion")
          , ("Tachistoscopes", "Tachistoscope")
          , ("Tachyons", "Tachyon")
          , ("Tactile_sensing", "Somatosensory_system")
          , ("Tahnoun_bin_Zayed_Al_Nahyan", "Tahnoun_bin_Zayed_Al_Nahyan_(national_security_advisor)")
          , ("Tai_Chi", "Tai_chi")
          , ("Taiyou_no_Ouji_Horus_no_Daiboken", "The_Great_Adventure_of_Horus,_Prince_of_the_Sun")
          , ("Takashi_Asahina", "Asahina_Takashi")
          , ("Takedown_request", "Notice_and_take_down")
          , ("Takedown_requests", "Notice_and_take_down")
          , ("Takeshi_Mori_%28director%29", "Takeshi_Mori_(director)")
          , ("Tale_of_Genji", "The_Tale_of_Genji")
          , ("Talk:Folding%40home/Archive_2", "Talk:Folding@home/Archive_2")
          , ("Talk:Kimi_to_Boku_(manga)", "Talk:Kimi_to_Boku")
          , ("Talk:Mt.Gox", "Talk:Mt._Gox")
          , ("Talk:Peace_Maker_%28manga%29", "Talk:Peacemaker_Kurogane")
          , ("Talking_birds", "Talking_bird")
          , ("Talking_drums", "Talking_drum")
          , ("Tamam_Shud_case", "Somerton_Man")
          , ("Tamarin_(JavaScript_engine)", "Tamarin_(software)")
          , ("Tamil_Tigers", "Liberation_Tigers_of_Tamil_Eelam")
          , ("Tammuz_(deity)", "Dumuzid")
          , ("Tanegashima_space_center", "Tanegashima_Space_Center")
          , ("Tang_Dynasty", "Tang_dynasty")
          , ("Tanned_skin", "Sun_tanning")
          , ("Tanner_Lecture", "Tanner_Lectures_on_Human_Values")
          , ("Tanner_lecture", "Tanner_Lectures_on_Human_Values")
          , ("Taqiya", "Taqiyya")
          , ("Tar_(file_format)", "Tar_(computing)")
          , ("Tar_heroin", "Black_tar_heroin")
          , ("Tar_sands", "Oil_sands")
          , ("TarePanda", "Tarepanda")
          , ("Tarento", "Television_personalities_in_Japan")
          , ("Tasmanian_devil_facial_tumour_disease", "Devil_facial_tumour_disease")
          , ("Tatami_Galaxy", "The_Tatami_Galaxy")
          , ("Tatsunoko", "Tatsunoko_Production")
          , ("Tau_oligomers", "Tau_protein")
          , ("Taurine_cattle", "Cattle")
          , ("Tax_Cuts_and_Jobs_Act_of_2017", "Tax_Cuts_and_Jobs_Act")
          , ("Tax_compliance", "Tax")
          , ("Taxa", "Taxon")
          , ("Taxons", "Taxon")
          , ("Tay-Sachs", "Tay%E2%80%93Sachs_disease")
          , ("Tay-Sachs_disease", "Tay%E2%80%93Sachs_disease")
          , ("Tay_(bot)", "Tay_(chatbot)")
          , ("TeX_license", "TeX#License")
          , ("Tea_tree_essential_oil", "Tea_tree_oil")
          , ("Technological_Singularity", "Technological_singularity")
          , ("Technological_progress", "Technical_progress_(economics)")
          , ("Tehran,_Iran", "Tehran")
          , ("Telencephalon", "Cerebrum")
          , ("Television_advertising", "Television_advertisement")
          , ("Telomeres", "Telomere")
          , ("Template_(C++)", "Template_(C%2B%2B)")
          , ("Temporal_difference", "Temporal_difference_learning")
          , ("Temporal_lobes", "Temporal_lobe")
          , ("Tendrils", "Tendril")
          , ("Tenebrio_molitor", "Mealworm")
          , ("Tengen_Toppa_Gurren_Lagann", "Gurren_Lagann")
          , ("Tennis_ladder", "Ladder_tournament")
          , ("Tensor_processing_unit", "Tensor_Processing_Unit")
          , ("Tenure", "Academic_tenure")
          , ("Teosinte", "Zea_(plant)#Origin_of_maize_and_interaction_with_teosintes")
          , ("Teraflops", "FLOPS")
          , ("Teratogenic", "Teratology")
          , ("Teratogenicity", "Teratology")
          , ("Teratogens", "Teratology")
          , ("Term-rewriting", "Rewriting")
          , ("Term_Frequency", "Tf%E2%80%93idf")
          , ("Term_Frequency_Inverse_Document_Frequency", "Tf%E2%80%93idf")
          , ("Terpenes", "Terpene")
          , ("Terrence_Sejnowski", "Terry_Sejnowski")
          , ("Terriers", "Terrier")
          , ("Terry_Allen_Winograd", "Terry_Winograd")
          , ("Terry_stops", "Terry_stop")
          , ("Teru_Mikami", "List_of_Death_Note_characters#Teru_Mikami")
          , ("Tesla_Inc", "Tesla,_Inc.")
          , ("Tesla_Motors", "Tesla,_Inc.")
          , ("Test-retest_reliability", "Repeatability")
          , ("Tetanus_toxin", "Tetanospasmin")
          , ("Tetanus_toxoid", "Tetanus_vaccine")
          , ("Tethered_cord_syndrome", "Tethered_spinal_cord_syndrome")
          , ("Tetrachoric_correlation", "Polychoric_correlation")
          , ("Tetracyclines", "Tetracycline_antibiotics")
          , ("Tetrameric", "Tetramer")
          , ("Tetsujin_28", "Tetsujin_28-go")
          , ("Tetulia_Tea_Garden", "Tea_production_in_Bangladesh")
          , ("Texas_superconducting_supercollider", "Superconducting_Super_Collider")
          , ("Text-to-image", "Text-to-image_model")
          , ("Text-to-image_generation", "Text-to-image_model")
          , ("Text_adventure", "Interactive_fiction")
          , ("Text_adventure_game", "Interactive_fiction")
          , ("Text_classification", "Document_classification")
          , ("Text_justification", "Typographic_alignment#Justified")
          , ("Text_similarity", "String_metric")
          , ("Text_summarization", "Automatic_summarization")
          , ("Thalamocortical_radiation", "Thalamocortical_radiations")
          , ("Thales", "Thales_of_Miletus")
          , ("The_24_Game", "24_(puzzle)")
          , ("The_Ancestor's_Tale", "The_Ancestor%27s_Tale")
          , ("The_Birds_%28play%29", "The_Birds_(play)")
          , ("The_Black_Cat_%281934_film%29", "The_Black_Cat_(1934_film)")
          , ("The_Book_of_Mormon", "Book_of_Mormon")
          , ("The_Cavendish", "Cavendish_Laboratory")
          , ("The_Championships,_Wimbledon", "Wimbledon_Championships")
          , ("The_Clock_of_the_Long_Now", "Clock_of_the_Long_Now")
          , ("The_Clockwork_Rocket", "Orthogonal_(series)")
          , ("The_Collapse_of_Complex_Societies", "Joseph_Tainter")
          , ("The_Creature_from_the_Black_Lagoon", "Creature_from_the_Black_Lagoon")
          , ("The_Darkness_That_Comes_Before", "Prince_of_Nothing")
          , ("The_Decline_and_Fall_of_the_Roman_Empire", "The_History_of_the_Decline_and_Fall_of_the_Roman_Empire")
          , ("The_Devil's_Dictionary", "The_Devil%27s_Dictionary")
          , ("The_Devil_Is_a_Part-Timer%21", "The_Devil_Is_a_Part-Timer!")
          , ("The_Discovery_Of_France", "The_Discovery_of_France")
          , ("The_Dog_of_Flanders", "Dog_of_Flanders_(TV_series)")
          , ("The_Eighteenth_Brumaire_of_Louis_Napoleon", "The_Eighteenth_Brumaire_of_Louis_Bonaparte")
          , ("The_Emperor's_New_Groove", "The_Emperor%27s_New_Groove")
          , ("The_Flame_Alphabet", "Ben_Marcus")
          , ("The_Golden_Half", "Golden_Half")
          , ("The_Good,_The_Bad,_And_The_Ugly", "The_Good,_the_Bad_and_the_Ugly")
          , ("The_Great_Exhibition", "Great_Exhibition")
          , ("The_Great_Gatsby_%282013_film%29", "The_Great_Gatsby_(2013_film)")
          , ("The_History_of_the_Communist_Party_of_the_Soviet_Union_(Bolsheviks)", "History_of_the_Communist_Party_of_the_Soviet_Union_(Bolsheviks)")
          , ("The_Hitchhiker's_Guide_to_the_Galaxy_(novel)", "The_Hitchhiker%27s_Guide_to_the_Galaxy_(novel)")
          , ("The_Hitchhiker's_Guide_to_the_Galaxy_(video_game)", "The_Hitchhiker%27s_Guide_to_the_Galaxy_(video_game)")
          , ("The_Hunt_of_the_Unicorn", "The_Unicorn_Tapestries")
          , ("The_IQ_Controversy,_the_Media_and_Public_Policy_(book)", "The_IQ_Controversy,_the_Media_and_Public_Policy")
          , ("The_Illuminatus%21_Trilogy", "The_Illuminatus!_Trilogy")
          , ("The_Impact_of_Legalized_Abortion_on_Crime", "Legalized_abortion_and_crime_effect#Donohue_and_Levitt_study")
          , ("The_Iowa_Electronic_Markets", "Iowa_Electronic_Markets")
          , ("The_John_Birch_Society", "John_Birch_Society")
          , ("The_King_of_Kong:_A_Fistful_of_Quarters", "The_King_of_Kong")
          , ("The_Knowledge", "Taxis_of_the_United_Kingdom#The_Knowledge")
          , ("The_Last_Airbender", "Avatar:_The_Last_Airbender")
          , ("The_Last_Of_Us_2", "The_Last_of_Us_Part_II")
          , ("The_Long_Tail", "Long_tail")
          , ("The_Magazine_of_Fantasy_and_Science_Fiction", "The_Magazine_of_Fantasy_%26_Science_Fiction")
          , ("The_Martian_%28Weir_novel%29", "The_Martian_(Weir_novel)")
          , ("The_Martian_%28film%29", "The_Martian_(film)")
          , ("The_Matrix_(series)", "The_Matrix_(franchise)")
          , ("The_Melancholy_of_Haruhi_Suzumiya", "Haruhi_Suzumiya#Anime")
          , ("The_Monkey's_Paw", "The_Monkey%27s_Paw")
          , ("The_Narrow_Road_to_Oku", "Oku_no_Hosomichi")
          , ("The_New_Organon", "Novum_Organum")
          , ("The_ODP_Corporation", "Office_Depot")
          , ("The_Omnivore's_Dilemma", "The_Omnivore%27s_Dilemma")
          , ("The_Party:_The_Secret_World_of_China's_Communist_Rulers", "The_Party:_The_Secret_World_of_China%27s_Communist_Rulers")
          , ("The_Phantom_Menace", "Star_Wars:_Episode_I_%E2%80%93_The_Phantom_Menace")
          , ("The_Philosophers'_Magazine", "The_Philosophers%27_Magazine")
          , ("The_Plain_Dealer_(newspaper)", "The_Plain_Dealer")
          , ("The_Politics_of_Heroin", "The_Politics_of_Heroin_in_Southeast_Asia")
          , ("The_Prospect_of_Immortality", "Robert_Ettinger")
          , ("The_Return_of_Ultraman", "Return_of_Ultraman")
          , ("The_Royal_Space_Force", "Royal_Space_Force:_The_Wings_of_Honn%C3%AAamise")
          , ("The_Sandman_%28Vertigo%29", "The_Sandman_(comic_book)")
          , ("The_Sandman_(Vertigo)", "The_Sandman_(comic_book)")
          , ("The_Secret_History_of_the_Mongols", "Secret_History_of_the_Mongols")
          , ("The_Secret_Life_of_Walter_Mitty_%282013_film%29", "The_Secret_Life_of_Walter_Mitty_(2013_film)")
          , ("The_Seven_Samurai", "Seven_Samurai")
          , ("The_Shining_%28film%29", "The_Shining_(film)")
          , ("The_Singularity", "Technological_singularity")
          , ("The_Singularity_is_Near", "The_Singularity_Is_Near")
          , ("The_Sorcerer's_House", "The_Sorcerer%27s_House")
          , ("The_Spirit_of_the_Laws", "The_Spirit_of_Law")
          , ("The_Super_Dimension_Fortress_Macross", "Super_Dimension_Fortress_Macross")
          , ("The_Tale_of_Sir_Thopas", "Sir_Thopas")
          , ("The_Thing_%281982_film%29", "The_Thing_(1982_film)")
          , ("The_Three_Pillars_of_Zen", "Philip_Kapleau")
          , ("The_Tragedy_of_the_Commons", "Tragedy_of_the_commons#Garrett_Hardin%27s_article")
          , ("The_University_of_Queensland", "University_of_Queensland")
          , ("The_Wailers", "Bob_Marley_and_the_Wailers")
          , ("The_War_On_Cancer", "War_on_cancer")
          , ("The_Wasteland", "The_Waste_Land")
          , ("The_Wikipedia_Revolution:_How_a_bunch_of_nobodies_created_the_world%27s_greatest_encyclopedia", "The_Wikipedia_Revolution")
          , ("The_Wikipedia_Revolution:_How_a_bunch_of_nobodies_created_the_world's_greatest_encyclopedia", "The_Wikipedia_Revolution")
          , ("The_Wings_of_Honneamise", "Royal_Space_Force:_The_Wings_of_Honn%C3%AAamise")
          , ("The_iron_law_of_oligarchy", "Iron_law_of_oligarchy")
          , ("The_monkey%27s_paw", "The_Monkey%27s_Paw")
          , ("The_monkey's_paw", "The_Monkey%27s_Paw")
          , ("The_tale_of_Phyllis_and_Aristotle", "Phyllis_and_Aristotle")
          , ("Thellusson_Will_Case", "Thellusson_v_Woodford")
          , ("Theoderic_the_Great", "Theodoric_the_Great")
          , ("Theodore_C._Schneirla", "T._C._Schneirla")
          , ("Theodore_Kaczynski", "Ted_Kaczynski")
          , ("Theodore_Schneirla", "T._C._Schneirla")
          , ("Theorem_proving", "Automated_theorem_proving")
          , ("There_ain%27t_no_such_thing_as_a_free_lunch", "No_such_thing_as_a_free_lunch")
          , ("There_ain't_no_such_thing_as_a_free_lunch", "No_such_thing_as_a_free_lunch")
          , ("Thermogravimetry", "Thermogravimetric_analysis")
          , ("Thiazolidinediones", "Thiazolidinedione")
          , ("Thin_sow_syndrome", "Animal_psychopathology#Thin_sow_syndrome")
          , ("Think-aloud_protocols", "Think_aloud_protocol")
          , ("Third-person_omniscient", "Narration#Third-person,_omniscient")
          , ("Thirty_Minutes_Over_Tokyo", "Thirty_Minutes_over_Tokyo")
          , ("Thomas_A._Edison", "Thomas_Edison")
          , ("Thread_(computer_science)", "Thread_(computing)")
          , ("Threadripper", "Ryzen")
          , ("Three's_Company", "Three%27s_Company")
          , ("Three-letter_acronyms", "Three-letter_acronym")
          , ("Three_Coins_in_the_Fountain_%28film%29", "Three_Coins_in_the_Fountain_(film)")
          , ("Through_the_Looking-Glass,_and_What_Alice_Found_There", "Through_the_Looking-Glass")
          , ("Thucydides'", "Thucydides%27")
          , ("Thule_Air_Base", "Pituffik_Space_Base")
          , ("Thyrotoxicosis", "Hyperthyroidism")
          , ("Thyrotropin", "Thyroid-stimulating_hormone")
          , ("Thyroxin", "Thyroid_hormones")
          , ("Thyroxine", "Levothyroxine")
          , ("Tiananmen_Square_massacre", "1989_Tiananmen_Square_protests_and_massacre")
          , ("Tianhe-1A", "Tianhe-1#Tianhe-1A")
          , ("Tianjin_cloning_center", "Tianjin_animal_cloning_center")
          , ("Tic_disorders", "Tic_disorder")
          , ("Tie-fighter", "TIE_fighter")
          , ("Tiger_teams", "Tiger_team")
          , ("Tikhonov_regularization", "Ridge_regression#Tikhonov_regularization")
          , ("Tim_O'Reilly", "Tim_O%27Reilly")
          , ("Time-shared_computing", "Time-sharing")
          , ("Time_Warner", "WarnerMedia")
          , ("Timing_attacks", "Timing_attack")
          , ("Timothy_D._Wilson", "Timothy_Wilson")
          , ("Timothy_Ferriss", "Tim_Ferriss")
          , ("Tit-for-tat", "Tit_for_tat")
          , ("Title-case", "Title_case")
          , ("Tizard_Committee", "Committee_for_the_Scientific_Survey_of_Air_Defence")
          , ("Toad_venom", "Bufotoxin")
          , ("Tobacco_budworm", "Helicoverpa_armigera")
          , ("Tobler's_first_law_of_geography", "Tobler%27s_first_law_of_geography")
          , ("Token_economies", "Token_economy")
          , ("Tokenization_(lexical_analysis)", "Lexical_analysis#Tokenization")
          , ("Toki_o_Kakeru_Shoujo", "The_Girl_Who_Leapt_Through_Time")
          , ("Tokubetsu_K%C5%8Dt%C5%8D_Keisatsu", "Special_Higher_Police")
          , ("Tokyo_Broadcasting_System", "TBS_Holdings")
          , ("Tokyo_Metropolitan_Museum_of_Photography", "Tokyo_Photographic_Art_Museum")
          , ("Tokyo_Trade_Center", "World_Trade_Center_(Tokyo)")
          , ("Tokyo_University", "University_of_Tokyo")
          , ("Tokyo_subway_system", "Tokyo_subway")
          , ("Tokyu_Hands", "Hands_(store)")
          , ("Tomatoes", "Tomato")
          , ("Ton_618", "TON_618")
          , ("Tonic_immobility", "Apparent_death#Tonic_immobility")
          , ("Tool_use_by_animals", "Tool_use_by_non-humans")
          , ("Top,_bottom_and_versatile", "Gay_sex_roles")
          , ("Top_Gun_Maverick", "Top_Gun:_Maverick")
          , ("Top_o_Nerae!", "Gunbuster")
          , ("Top_o_Nerae%21", "Gunbuster")
          , ("Top_o_Nerae%21_2", "Diebuster")
          , ("TorBrowser", "Tor_(network)#Tor_Browser")
          , ("Tor_%28anonymity_network%29", "Tor_(network)")
          , ("Tor_(anonymity_network)", "Tor_(network)")
          , ("Tora%21_Tora%21_Tora%21", "Tora!_Tora!_Tora!")
          , ("Toradora", "Toradora!")
          , ("Toshiba_EMI", "EMI_Music_Japan")
          , ("Total_energy_expenditure", "Energy_homeostasis#Expenditure")
          , ("Total_ordering", "Total_order")
          , ("Totipotency", "Cell_potency#Totipotency")
          , ("Touch_%28manga%29", "Touch_(manga)")
          , ("Touch_Bar", "MacBook_Pro#Fourth_generation_(Touch_Bar)")
          , ("Touhou", "Touhou_Project")
          , ("Tourette%27s_Syndrome", "Tourette_syndrome")
          , ("Towers_of_Hanoi", "Tower_of_Hanoi")
          , ("Trade_secrets", "Trade_secret")
          , ("Tragedy_of_the_anti-commons", "Tragedy_of_the_anticommons")
          , ("Training,_validation,_and_test_sets", "Training,_validation,_and_test_data_sets")
          , ("Transclusions", "Transclusion")
          , ("Transcranial_alternating_current_stimulation", "Cranial_electrotherapy_stimulation")
          , ("Transcription_%28biology%29", "Transcription_(biology)")
          , ("Transcription_factors", "Transcription_factor")
          , ("Transcriptomics", "Transcriptomics_technologies")
          , ("Transformativeness", "Transformative_use")
          , ("Transformer_(machine_learning)", "Transformer_(deep_learning_architecture)")
          , ("Transformers_(toy_line)", "Transformers:_Generation_1")
          , ("Transhumanist", "Transhumanism")
          , ("Transhumanists", "Transhumanism")
          , ("Transitional_object", "Comfort_object")
          , ("Translation_%28biology%29", "Translation_(biology)")
          , ("Transmissible_cancer", "Clonally_transmissible_cancer")
          , ("Transmissible_spongiform_encephalopathies", "Transmissible_spongiform_encephalopathy")
          , ("Transmission_(mechanics)", "Transmission_(mechanical_device)")
          , ("Transparency_(social)", "Transparency_(behavior)")
          , ("Transplantation", "Organ_transplantation")
          , ("Traveler's_dilemma", "Traveler%27s_dilemma")
          , ("Traveling_Salesman_problem", "Travelling_salesman_problem")
          , ("Traveling_salesman_problem", "Travelling_salesman_problem")
          , ("Treatment-resistant_major_depression", "Treatment-resistant_depression")
          , ("Treble_clef", "Clef#Treble_clef")
          , ("Tricyclic_antidepressants", "Tricyclic_antidepressant")
          , ("Triglycerides", "Triglyceride")
          , ("Trinitrotoluene", "TNT")
          , ("Triplets", "Multiple_birth#Triplets")
          , ("Triploid", "Polyploidy#Types")
          , ("Tristan_and_Isolde", "Tristan_and_Iseult")
          , ("Tristram_Shandy", "The_Life_and_Opinions_of_Tristram_Shandy,_Gentleman")
          , ("Trithemius", "Johannes_Trithemius")
          , ("Trithemius's_cipher", "Trithemius%27s_cipher")
          , ("Tritium_illumination", "Tritium_radioluminescence")
          , ("Trojan_horse_%28computing%29", "Trojan_horse_(computing)")
          , ("Troll_(Internet)", "Troll_(slang)")
          , ("Truncation_%28statistics%29", "Truncation_(statistics)")
          , ("Truth_tables", "Truth_table")
          , ("Tryptamines", "Substituted_tryptamine")
          , ("Tsuburaya", "Eiji_Tsuburaya")
          , ("Tubeworm", "Tube_worm")
          , ("Tueller_drill", "Tueller_Drill")
          , ("Tulpas", "Tulpa")
          , ("Tummy_tuck", "Abdominoplasty")
          , ("Tumour_suppressor_genes", "Tumor_suppressor_gene")
          , ("Turbo-generators", "Turbo_generator")
          , ("Turbocurarine", "Tubocurarine_chloride")
          , ("Turing-complete", "Turing_completeness")
          , ("Turing-completeness", "Turing_completeness")
          , ("Turing_Prize", "Turing_Award")
          , ("Turing_Test", "Turing_test")
          , ("Turing_complete", "Turing_completeness")
          , ("Turner's_syndrome", "Turner_syndrome")
          , ("Turnpike_trusts", "Turnpike_trust")
          , ("Tusken_Raider", "Tusken_Raiders")
          , ("Tuyuca", "Tuyuca_language")
          , ("TweetDeck", "Tweetdeck")
          , ("Twenty_Questions", "Twenty_questions")
          , ("Twin_studies", "Twin_study")
          , ("Two-sided_markets", "Two-sided_market")
          , ("Type-checking", "Type_system#Type_checking")
          , ("Type_1_diabetes_mellitus", "Type_1_diabetes")
          , ("Type_2_diabetes_mellitus", "Type_2_diabetes")
          , ("Type_II_errors", "Type_I_and_type_II_errors#Type_II_error")
          , ("Type_polymorphism", "Polymorphism_(computer_science)")
          , ("Type_two_diabetes", "Type_2_diabetes")
          , ("Tyrannosaurus_rex", "Tyrannosaurus")
          , ("Tyto_alba", "Barn_owl")
          , ("U-net", "U-Net")
          , ("U.C.L.A._Medical_Center", "Ronald_Reagan_UCLA_Medical_Center")
          , ("U.S.A._trilogy", "U.S.A._(trilogy)")
          , ("U.S._Federal_District_Courts", "United_States_district_court")
          , ("U.S._Office_of_Foreign_Assets_Control", "Office_of_Foreign_Assets_Control")
          , ("U.S._State_Courts", "State_court_(United_States)")
          , ("U.S._State_Department_list_of_Foreign_Terrorist_Organizations", "United_States_Department_of_State_list_of_Foreign_Terrorist_Organizations")
          , ("UAE", "United_Arab_Emirates")
          , ("UEFA_Euro_2016_knockout_phase", "UEFA_Euro_2016_knockout_stage")
          , ("UHDTV", "Ultra-high-definition_television")
          , ("UNIX", "Unix")
          , ("URL-encoding", "Percent-encoding")
          , ("URL_encoding", "Percent-encoding")
          , ("USB-2", "USB#2.0")
          , ("USB-3", "USB_3.0")
          , ("USMLE", "United_States_Medical_Licensing_Examination")
          , ("US_Census_Bureau", "United_States_Census_Bureau")
          , ("US_Department_of_State", "United_States_Department_of_State")
          , ("US_Federal_Trade_Commission", "Federal_Trade_Commission")
          , ("US_National_Institutes_of_Health", "National_Institutes_of_Health")
          , ("Ubuntu_Linux", "Ubuntu")
          , ("Uchouten_Kazoku", "The_Eccentric_Family")
          , ("Uchu_Senkan_Yamato", "Space_Battleship_Yamato")
          , ("Udon_noodles", "Udon")
          , ("Ugric", "Ugric_languages")
          , ("Ulam's_game", "Ulam%27s_game")
          , ("Ulam-Teller_bomb", "Thermonuclear_weapon#Basic_principle")
          , ("Ultimatum_Game", "Ultimatum_game")
          , ("Ultra-marathon", "Ultramarathon")
          , ("Ultra-runner", "Ultramarathon")
          , ("Ultra_Series", "Ultraman")
          , ("Ultra_Seven", "Ultraseven")
          , ("Ultraman_Powered", "Ultraman_Powered_(video_game)")
          , ("Ultraviolet_radiation", "Ultraviolet")
          , ("Umineko:_When_They_Cry", "Umineko_When_They_Cry")
          , ("Umineko_no_Naku_Koro_ni", "Umineko_When_They_Cry")
          , ("Ummon", "Yunmen_Wenyan")
          , ("Unabomber", "Ted_Kaczynski")
          , ("Unabomber_Manifesto", "Industrial_Society_and_Its_Future")
          , ("Uncle_Tom's_Cabin", "Uncle_Tom%27s_Cabin")
          , ("Undecidability", "Undecidable_problem")
          , ("Underground_nuclear_testing", "Underground_nuclear_weapons_testing")
          , ("Uniform_CPA_Examination", "Uniform_Certified_Public_Accountant_Examination")
          , ("Uniform_distribution_%28discrete%29", "Discrete_uniform_distribution")
          , ("Uniform_distribution_(discrete)", "Discrete_uniform_distribution")
          , ("Uninterruptable_power_supply", "Uninterruptible_power_supply")
          , ("Uninterruptible_power_supplies", "Uninterruptible_power_supply")
          , ("Unisoc", "UNISOC")
          , ("Unit_tests", "Unit_testing")
          , ("United_Nations_Charter", "Charter_of_the_United_Nations")
          , ("United_Nations_Universal_Declaration_of_Human_Rights", "Universal_Declaration_of_Human_Rights")
          , ("United_States_2008_wireless_spectrum_auction", "2008_United_States_wireless_spectrum_auction")
          , ("United_States_Census", "United_States_census")
          , ("United_States_Constitution", "Constitution_of_the_United_States")
          , ("United_States_House_of_Representatives_elections_in_Florida,_2016", "2016_United_States_House_of_Representatives_elections_in_Florida")
          , ("United_States_Military", "United_States_Armed_Forces")
          , ("United_States_Representatives", "United_States_House_of_Representatives")
          , ("United_States_Senate_election_in_Florida,_2016", "2016_United_States_Senate_election_in_Florida")
          , ("United_States_federal_government_shutdown_of_1995_and_1996", "1995%E2%80%931996_United_States_federal_government_shutdowns")
          , ("United_States_government", "Federal_government_of_the_United_States")
          , ("Unity_%28game_engine%29", "Unity_(game_engine)")
          , ("Unity_engine", "Unity_(game_engine)")
          , ("Universal_influenza_vaccines", "Universal_flu_vaccine")
          , ("Universe_is_a_black_hole", "Black_hole_cosmology")
          , ("Universidad_Nacional_de_R%C3%ADo_Negro", "National_University_of_R%C3%ADo_Negro")
          , ("University_admission", "University_and_college_admission")
          , ("University_of_California_at_Irvine", "University_of_California,_Irvine")
          , ("University_of_California_at_San_Diego", "University_of_California,_San_Diego")
          , ("University_of_Colorado_at_Boulder", "University_of_Colorado_Boulder")
          , ("University_of_Iowa_Writers'_Workshop", "Iowa_Writers%27_Workshop")
          , ("University_of_Massachusetts,_Amherst", "University_of_Massachusetts_Amherst")
          , ("Unix_epoch", "Unix_time")
          , ("Unmanned_combat_air_vehicle", "Unmanned_combat_aerial_vehicle")
          , ("Unpublished_work", "Publication#Unpublished_works")
          , ("Up_%282009_film%29", "Up_(2009_film)")
          , ("Upregulation", "Downregulation_and_upregulation")
          , ("Uranium_235", "Uranium-235")
          , ("Urban_legends", "Urban_legend")
          , ("Urban_legends_and_myths", "Urban_legend")
          , ("Ursula_K._LeGuin", "Ursula_K._Le_Guin")
          , ("Ursula_Le_Guin", "Ursula_K._Le_Guin")
          , ("Urxvt", "Rxvt#Derived_programs")
          , ("Usagi_Tsukino", "Sailor_Moon_(character)")
          , ("Usemod", "UseModWiki")
          , ("User-agent", "User_agent")
          , ("User:Marudubshinki", "User:Gwern")
          , ("User:X%21", "User:X!")
          , ("Ushio_and_Tora", "Ushio_%26_Tora")
          , ("Usufructuary", "Usufruct")
          , ("VC_dimension", "Vapnik%E2%80%93Chervonenkis_dimension")
          , ("VLSI", "Very_Large_Scale_Integration")
          , ("Vaccines", "Vaccine")
          , ("Vacuum_energy_density", "Vacuum_energy")
          , ("Val66Met", "Rs6265")
          , ("Valdes_Peninsula", "Vald%C3%A9s_Peninsula")
          , ("Valeriana_officinalis", "Valerian_(herb)")
          , ("Valium", "Diazepam")
          , ("Valois_dynasty", "House_of_Valois")
          , ("Valproic_acid", "Valproate")
          , ("Value_iteration", "Markov_decision_process#Value_iteration")
          , ("Value_of_Information", "Value_of_information")
          , ("Vandenberg_Air_Force_Base", "Vandenberg_Space_Force_Base")
          , ("Vaping", "Electronic_cigarette")
          , ("Vaquita_porpoise", "Vaquita")
          , ("Varhadi", "Varhadi_dialect")
          , ("Variable_bit_rate", "Variable_bitrate")
          , ("Variable_fighter", "Variable-sweep_wing")
          , ("Variable_fonts", "Variable_font")
          , ("Variational_inference", "Variational_Bayesian_methods")
          , ("Variational_principles", "Variational_principle")
          , ("Varicella-zoster", "Varicella_zoster_virus")
          , ("Varicella_zoster", "Varicella_zoster_virus")
          , ("Varicose_vein", "Varicose_veins")
          , ("Variola_virus", "Smallpox#Cause")
          , ("Vastus_lateralis_muscles", "Vastus_lateralis_muscle")
          , ("Vatican_II", "Second_Vatican_Council")
          , ("Vauban", "S%C3%A9bastien_Le_Prestre_de_Vauban,_Marquis_of_Vauban")
          , ("Vax_Common_Lisp", "VAX_Common_Lisp")
          , ("Ventral_striatum", "Striatum#Structure")
          , ("Venture_capitalist", "Venture_capital#Venture_capitalist")
          , ("Version_control_systems", "Version_control")
          , ("Vervet", "Vervet_monkey")
          , ("Vi", "Vi_(text_editor)")
          , ("ViaGen", "ViaGen_Pets")
          , ("Vibrissae", "Whiskers")
          , ("Video_cards", "Graphics_card")
          , ("Vietcong", "Viet_Cong")
          , ("Vietnam_People%27s_Army", "People%27s_Army_of_Vietnam")
          , ("Vietnam_People's_Army", "People%27s_Army_of_Vietnam")
          , ("Vigenere_ciphers", "Vigen%C3%A8re_cipher")
          , ("Vile_Rat", "Sean_Smith_(diplomat)")
          , ("Villiers_de_l%27Isle-Adam", "Auguste_Villiers_de_l%27Isle-Adam")
          , ("Vinca_alkaloids", "Vinca_alkaloid")
          , ("Vinyl_records", "Phonograph_record")
          , ("Violent_video_game", "Violence_and_video_games")
          , ("Violin_plots", "Violin_plot")
          , ("Vioxx", "Rofecoxib")
          , ("Vipassana", "Samatha-vipassana")
          , ("Virtual_YouTuber", "VTuber")
          , ("Virtual_electrons", "Virtual_particle")
          , ("Virtual_machines", "Virtual_machine")
          , ("Virtual_reality_exposure_therapy", "Virtual_reality_therapy")
          , ("Virtue_signaling", "Virtue_signalling")
          , ("Vision_Fund", "SoftBank_Vision_Fund")
          , ("Visual_display_unit", "Computer_monitor")
          , ("Visual_novels", "Visual_novel")
          , ("Visual_pattern_recognition", "Pattern_recognition_(psychology)")
          , ("Visual_question_answering", "Question_answering")
          , ("Visual_recognition", "Computer_vision#Bababoui")
          , ("Visual_snow", "Visual_snow_syndrome")
          , ("Visuospatial_sketchpad", "Baddeley%27s_model_of_working_memory#Visuospatial_sketchpad")
          , ("Vitrification_in_cryopreservation", "Cryopreservation#methods")
          , ("Vocaloids", "Vocaloid")
          , ("Volatile_organic_compounds", "Volatile_organic_compound")
          , ("Volcanic_Explosivity_Index", "Volcanic_explosivity_index")
          , ("Volcanic_eruptions", "Types_of_volcanic_eruptions")
          , ("Volta_%28microarchitecture%29", "Volta_(microarchitecture)")
          , ("Von_Neumann_probes", "Self-replicating_spacecraft#Von_Neumann_probes")
          , ("Voronin%27s_theorem", "Zeta_function_universality")
          , ("Voronin's_theorem", "Zeta_function_universality")
          , ("Voting_system", "Electoral_system")
          , ("Votoms", "Armored_Trooper_Votoms")
          , ("Voynich_Manuscript", "Voynich_manuscript")
          , ("Vulpes_vulpes", "Red_fox")
          , ("Vultures", "Vulture")
          , ("W.E.H._Lecky", "William_Edward_Hartpole_Lecky")
          , ("WAIS-IV", "Wechsler_Adult_Intelligence_Scale")
          , ("WDR11", "WD_repeat-containing_protein_11")
          , ("WEB", "Web_(programming_system)")
          , ("WGS84", "World_Geodetic_System#WGS84")
          , ("WNBA", "Women%27s_National_Basketball_Association")
          , ("WP:HAIKU", "Wikipedia:Haiku_about_Wikipedia_policy")
          , ("WP:MISSING", "Wikipedia:WikiProject_Missing_encyclopedic_articles")
          , ("Wachowski_brothers", "The_Wachowskis")
          , ("Wage_stickiness", "Nominal_rigidity")
          , ("Wall_Street_Journal", "The_Wall_Street_Journal")
          , ("Walmart_Inc.", "Walmart")
          , ("Walt_Disney_Corporation", "The_Walt_Disney_Company")
          , ("Walter_B._Pillsbury", "Walter_Bowers_Pillsbury")
          , ("Walter_Brattain", "Walter_Houser_Brattain")
          , ("Walter_M._Miller_Junior", "Walter_M._Miller_Jr.")
          , ("Wang_tiles", "Wang_tile")
          , ("War_in_Somalia_(2006-2009)", "War_in_Somalia_(2006%E2%80%932009)")
          , ("War_on_Cancer", "War_on_cancer")
          , ("War_on_Drugs", "War_on_drugs")
          , ("War_on_Poverty", "War_on_poverty")
          , ("Ward%27s_Wiki", "WikiWikiWeb")
          , ("Ward's_Wiki", "WikiWikiWeb")
          , ("Warrant_%28finance%29", "Warrant_(finance)")
          , ("Wash_trading", "Wash_trade")
          , ("Washington,_District_of_Columbia", "Washington,_D.C.")
          , ("Watamote", "No_Matter_How_I_Look_at_It,_It%27s_You_Guys%27_Fault_I%27m_Not_Popular!")
          , ("Waterwheel", "Water_wheel")
          , ("Watson_Institute_for_International_Studies", "Watson_Institute_for_International_and_Public_Affairs")
          , ("Wave-particle_duality", "Wave%E2%80%93particle_duality")
          , ("Waveguides", "Waveguide")
          , ("WebCitation", "WebCite")
          , ("WebCitation.org", "WebCite")
          , ("Web_ARChive", "WARC_(file_format)")
          , ("Web_browsing", "Web_navigation")
          , ("Web_search_engine", "Search_engine")
          , ("Weber-Fechner_law", "Weber%E2%80%93Fechner_law")
          , ("Webfonts", "Web_typography#Web_fonts")
          , ("Webserials", "Web_fiction#Web_serial")
          , ("Wedge%27s_Gamble", "Star_Wars:_X-wing_(book_series)#Wedge%27s_Gamble_(1996)")
          , ("Wedge's_Gamble", "Star_Wars:_X-wing_(book_series)#Wedge%27s_Gamble_(1996)")
          , ("Weekly_Shonen_Jump", "Weekly_Sh%C5%8Dnen_Jump")
          , ("Wegovy", "Semaglutide")
          , ("Weight_decay", "Ridge_regression#Tikhonov_regularization")
          , ("Weighted_blankets", "Weighted_blanket")
          , ("Weird_Al_Yankovic", "%22Weird_Al%22_Yankovic")
          , ("Well-powered", "Power_of_a_test")
          , ("Wellbutrin", "Bupropion")
          , ("Wernicke's_area", "Wernicke%27s_area")
          , ("West_Loop", "Near_West_Side,_Chicago#West_Loop")
          , ("West_Point", "United_States_Military_Academy")
          , ("Western_Hunter-Gatherers", "Western_Hunter-Gatherer")
          , ("What's_Opera,_Doc%3F", "What%27s_Opera,_Doc%3F")
          , ("What.cd", "What.CD")
          , ("What_Would_Jesus_Do", "What_would_Jesus_do%3F")
          , ("Wheeled_Luggage", "Baggage#Wheels")
          , ("Where%27s_Waldo%3F", "Where%27s_Wally%3F")
          , ("Where's_Waldo%3F", "Where%27s_Wally%3F")
          , ("Whiskey", "Whisky")
          , ("Whiskey_rebellion", "Whiskey_Rebellion")
          , ("Whistle-blowers", "Whistleblowing")
          , ("Whistleblower", "Whistleblowing")
          , ("White_Russian", "White_%C3%A9migr%C3%A9")
          , ("White_dwarf_stars", "White_dwarf")
          , ("White_dwarfs", "White_dwarf")
          , ("White_rot", "Wood-decay_fungus#White_rot")
          , ("Whitelists", "Whitelist")
          , ("Who%27s_Who_%28UK%29", "Who%27s_Who_(UK)")
          , ("Whole-brain_emulation", "Mind_uploading")
          , ("Whole_Foods", "Whole_Foods_Market")
          , ("Whole_brain_emulation", "Mind_uploading")
          , ("Whuffie", "Down_and_Out_in_the_Magic_Kingdom")
          , ("Whuffies", "Down_and_Out_in_the_Magic_Kingdom")
          , ("WiFi", "Wi-Fi")
          , ("Wiccans", "Wicca")
          , ("Wigner's_friend", "Wigner%27s_friend")
          , ("WikiMedia_Foundation", "Wikimedia_Foundation")
          , ("Wikileaks", "WikiLeaks")
          , ("Wikipedia:Articles_for_deletion/Mzoli's_Meats", "Wikipedia:Articles_for_deletion/Mzoli%27s_Meats")
          , ("Wikipedia:HAIKU", "Wikipedia:Haiku_about_Wikipedia_policy")
          , ("Wikipedia:MISSING", "Wikipedia:WikiProject_Missing_encyclopedic_articles")
          , ("Wikipedia:RS", "Wikipedia:Reliable_sources")
          , ("Wikipedia_biography_controversy", "List_of_Wikipedia_controversies")
          , ("Wile_E._Coyote_and_Road_Runner", "Wile_E._Coyote_and_the_Road_Runner")
          , ("Will_Provine", "William_B._Provine")
          , ("William_Burroughs", "William_S._Burroughs")
          , ("William_Gass", "William_H._Gass")
          , ("William_Gladstone", "William_Ewart_Gladstone")
          , ("William_H._Taft", "William_Howard_Taft")
          , ("William_Harper_(South_Carolina)", "William_Harper_(South_Carolina_politician)")
          , ("William_IV_of_the_United_Kingdom", "William_IV")
          , ("William_Somerset_Maugham", "W._Somerset_Maugham")
          , ("William_Thomson,_1st_Baron_Kelvin", "Lord_Kelvin")
          , ("Willingness-to-pay", "Willingness_to_pay")
          , ("Willis_Jeep", "Willys_MB")
          , ("Willpower_depletion", "Ego_depletion")
          , ("WilmerHale", "Wilmer_Cutler_Pickering_Hale_and_Dorr")
          , ("Wiltern_Theater", "Pellissier_Building_and_Wiltern_Theatre")
          , ("Windowing_systems", "Windowing_system")
          , ("Wings_of_Honn%C3%AAamise", "Royal_Space_Force:_The_Wings_of_Honn%C3%AAamise")
          , ("Wings_of_Honneamise", "Royal_Space_Force:_The_Wings_of_Honn%C3%AAamise")
          , ("Winn_Feline_Foundation", "EveryCat_Health_Foundation")
          , ("Winner%27s_remorse", "Winner%27s_curse")
          , ("Winner's_curse", "Winner%27s_curse")
          , ("Winner's_remorse", "Winner%27s_curse")
          , ("Winning_Colors_(novel)", "Elizabeth_Moon")
          , ("Winning_streak_(sports)", "Winning_streak")
          , ("Winograd_Schema_Challenge", "Winograd_schema_challenge")
          , ("Winthrop_Niles_Kellogg", "Winthrop_Kellogg")
          , ("Wired_%28magazine%29", "Wired_(magazine)")
          , ("Wired_Magazine", "Wired_(magazine)")
          , ("Wireheading", "Wirehead_(science_fiction)")
          , ("Wirth's_law", "Wirth%27s_law")
          , ("Wisdom_of_crowds", "The_Wisdom_of_Crowds")
          , ("Wislawa_Szymborska", "Wis%C5%82awa_Szymborska")
          , ("Wizard's_First_Rule", "Wizard%27s_First_Rule")
          , ("Wizards_of_the_coast", "Wizards_of_the_Coast")
          , ("Wodin", "Odin")
          , ("Wolfgang_Kohler", "Wolfgang_K%C3%B6hler")
          , ("Woman_in_Gold", "Portrait_of_Adele_Bloch-Bauer_I")
          , ("Women's_Health_Initiative", "Women%27s_Health_Initiative")
          , ("Women_are_wonderful_effect", "Women-are-wonderful_effect")
          , ("Woolly_mammoths", "Woolly_mammoth")
          , ("World_Medical_Mission", "Samaritan%27s_Purse")
          , ("World_model", "Physical_cosmology")
          , ("Worse_is_Better", "Worse_is_better")
          , ("Wright_Field", "Wilbur_Wright_Field")
          , ("Writer's_block", "Writer%27s_block")
          , ("Wrongful_involuntary_commitment", "Involuntary_commitment#Wrongful_involuntary_commitment")
          , ("Wunderkammer", "Cabinet_of_curiosities")
          , ("X-Men_(TV_series)", "X-Men_(disambiguation)#Animated_TV_series")
          , ("X-ray_lasers", "X-ray_laser")
          , ("X-wing", "X-wing_fighter")
          , ("X.org", "X.Org_Server")
          , ("XBox_360", "Xbox_360")
          , ("XCON", "Xcon")
          , ("XKCD", "Xkcd")
          , ("XMonad", "Xmonad")
          , ("XNOR", "XNOR_gate")
          , ("XOR", "Exclusive_or")
          , ("X_(company)", "X_(disambiguation)#Companies")
          , ("Xanax", "Alprazolam")
          , ("Xenobots", "Xenobot")
          , ("Xenopus_laevis", "African_clawed_frog")
          , ("Xerox_PARC", "PARC_(company)")
          , ("Xerox_Palo_Alto_Research_Center", "PARC_(company)")
          , ("Xi_%28letter%29", "Xi_(letter)")
          , ("Xinjiang_re-education_camps", "Xinjiang_internment_camps")
          , ("Xultophy", "Insulin_degludec/liraglutide")
          , ("Y%C5%ABgen", "Japanese_aesthetics#Y%C5%ABgen")
          , ("Y-aminobutyric_acid", "%CE%93-Aminobutyric_acid")
          , ("Y._S._Bakhle", "Y_S_Bakhle")
          , ("Yagi-Uda_antenna", "Yagi%E2%80%93Uda_antenna")
          , ("Yahoo!_GeoCities", "GeoCities")
          , ("Yahoo", "Yahoo!")
          , ("Yahoo%21_Mail", "Yahoo!_Mail")
          , ("Yahoo%21_Video", "Yahoo!_Screen")
          , ("Yakuza_(series)", "Yakuza_(franchise)")
          , ("Yamanaka_Factor", "Shinya_Yamanaka#Yamanaka's_research")
          , ("Yamanaka_Shikanosuke", "Yamanaka_Yukimori")
          , ("Yamata_no_Orochi_no_Gyakushu", "Yamata_no_Orochi_no_Gyakush%C5%AB")
          , ("Yasir_Arafat", "Yasser_Arafat")
          , ("Yasuo_Otsuka", "Yasuo_%C5%8Ctsuka")
          , ("Yasutani", "Hakuun_Yasutani")
          , ("Yavanna", "Valar#Yavanna")
          , ("Yeats", "W._B._Yeats")
          , ("Yebisu", "Sapporo_Breweries#Yebisu")
          , ("Yellow_Pages", "Yellow_pages")
          , ("Yellow_jackets", "Yellowjacket")
          , ("Yerkes-Dodson_law", "Yerkes%E2%80%93Dodson_law")
          , ("Yoji_Enokido", "Y%C5%8Dji_Enokido")
          , ("Yojimbo_(film)", "Yojimbo")
          , ("Yolks", "Yolk")
          , ("Yonago_dialect", "Umpaku_dialect")
          , ("Yongbyon_Nuclear_Scientific_Research_Center", "Nyongbyon_Nuclear_Scientific_Research_Center")
          , ("Yoruba", "Yoruba_people")
          , ("Yosuke_Yamashita", "Y%C5%8Dsuke_Yamashita")
          , ("Yotsuba%26%21", "Yotsuba%26!")
          , ("You_can%27t_always_get_what_you_want", "You_Can%27t_Always_Get_What_You_Want")
          , ("You_can't_always_get_what_you_want", "You_Can%27t_Always_Get_What_You_Want")
          , ("Young_Jeezy", "Jeezy")
          , ("Youth_Risk_Behavior_Survey", "Youth_Risk_Behavior_Surveillance_System")
          , ("Yuichi_Sasamoto", "Y%C5%ABichi_Sasamoto")
          , ("Yuki_Nagato", "List_of_Haruhi_Suzumiya_characters#Yuki_Nagato")
          , ("Yuko_Miyamura", "Y%C5%ABko_Miyamura")
          , ("Yunnan_Province", "Yunnan")
          , ("ZCash", "Zcash")
          , ("Zalgo", "Zalgo_text")
          , ("Zapp_Brannigan", "List_of_Futurama_characters#Zapp_Brannigan")
          , ("Zebra_finches", "Zebra_finch")
          , ("Zen_Buddhism", "Zen")
          , ("Zentrale_Stelle", "Central_Office_of_the_State_Justice_Administrations_for_the_Investigation_of_National_Socialist_Crimes")
          , ("Zeo_Inc.", "Zeo,_Inc.")
          , ("Zero-click_attack", "Exploit_(computer_security)#Zero-click")
          , ("Zero-knowledge_proofs", "Zero-knowledge_proof")
          , ("Zero-sum", "Zero-sum_game")
          , ("Zero_To_One", "Zero_to_One")
          , ("Zero_no_Tsukaima", "The_Familiar_of_Zero")
          , ("Zettel_(Wittgenstein)", "Zettel_(Wittgenstein_book)")
          , ("Zinc-bromine_battery", "Zinc%E2%80%93bromine_battery")
          , ("Zinc_finger_nuclease", "Zinc-finger_nuclease")
          , ("Zipf's_law", "Zipf%27s_law")
          , ("Zooko's_triangle", "Zooko%27s_triangle")
          , ("Zurich%2C_Switzerland", "Z%C3%BCrich")
          , ("_Unguentarium_", "Unguentarium")
          , ("replication_Crisis", "Replication_crisis")
          , ("replication_crisis", "Replication_crisis")
          , ("Dialysis", "Kidney_dialysis")
          , ("Ad-blockers", "Ad_blocking")
          , ("Aminotransferase", "Transaminase")
          , ("Amyloid-beta", "Amyloid_beta")
          , ("Antidiabetic_drug", "Diabetes_medication")
          , ("Aphantasics", "Aphantasia")
          , ("Apple_Retina", "Retina_display")
          , ("ARM_Holdings", "Arm_Holdings")
          , ("Bait_shyness", "Poison_shyness")
          , ("Big_Three_(World_War_II)", "Allies_of_World_War_II#Grand_Alliance")
          , ("Bird_watchers", "Birdwatching")
          , ("Black_Kite", "Black_kite")
          , ("Blood-brain_barrier", "Blood%E2%80%93brain_barrier")
          , ("Brennan_Center", "Brennan_Center_for_Justice")
          , ("Brown_Falcon", "Brown_falcon")
          , ("California_Gold_Rush", "California_gold_rush")
          , ("%CE%B4-opioid_receptor", "%CE%94-opioid_receptor")
          , ("%CE%BA-opioid_receptor", "%CE%9A-opioid_receptor")
          , ("CNTFET", "Carbon_nanotube_field-effect_transistor")
          , ("Cognitive_control", "Executive_functions")
          , ("Columba_livia", "Rock_dove")
          , ("Covid-19", "COVID-19")
          , ("Creepy", "Creepiness")
          , ("Cross_entropy_method", "Cross-entropy_method")
          , ("Darknet_markets", "Darknet_market")
          , ("David_E._Rumelhart", "David_Rumelhart")
          , ("Demographics", "Demography")
          , ("Dermoscopy", "Dermatoscopy")
          , ("Desaturase", "Fatty_acid_desaturase")
          , ("Determinate_growth", "Indeterminate_growth")
          , ("Dropcap", "Initial")
          , ("Dropcaps", "Initial")
          , ("Drug_repurposing", "Drug_repositioning")
          , ("Duke_of_Tuscany", "List_of_grand_dukes_of_Tuscany")
          , ("Dunedin_Study", "Dunedin_Multidisciplinary_Health_and_Development_Study")
          , ("Dutch_raid_on_the_Medway", "Raid_on_the_Medway")
          , ("Edison_bulb", "Edison_light_bulb")
          , ("Elastic_net_regression", "Elastic_net_regularization")
          , ("Electron_cryotomography", "Cryogenic_electron_tomography")
          , ("Elo_rating", "Elo_rating_system")
          , ("End-stage_renal_disease", "Chronic_kidney_disease#Stages")
          , ("Enoch_Root", "The_Baroque_Cycle#Characters")
          , ("ERCC8", "ERCC8_(gene)")
          , ("Estimating_the_number_of_unseen_species", "Unseen_species_problem")
          , ("Evolutionary", "Evolution")
          , ("Executioner_sword", "Executioner%27s_sword")
          , ("Extreme_Programming", "Extreme_programming")
          , ("File:Riddles_of_Aldhelm,_London,_British_Library,_Royal_MA_12_c_xxiii_folio_84r.jpg", "https://commons.wikimedia.org/wiki/File:Riddles_of_Aldhelm,_London,_British_Library,_Royal_MA_12_c_xxiii_folio_84r.jpg")
          , ("Flight_and_expulsion_of_Germans_%281944%E2%80%9350%29", "Flight_and_expulsion_of_Germans_(1944%E2%80%931950)")
          , ("Funeral_directors", "Funeral_director")
          , ("Gazettes", "Gazette")
          , ("Geoguessr", "GeoGuessr")
          , ("Greebles", "Greeble")
          , ("Heavy-tailed", "Heavy-tailed_distribution")
          , ("History_of_lysergic_acid_diethylamide", "History_of_LSD")
          , ("How_Long_Is_the_Coast_of_Britain%3F_Statistical_Self-Similarity_and_Fractional_Dimension", "Coastline_paradox#Measuring_a_coastline")
          , ("Hydrolyzed", "Hydrolysis")
          , ("Incarceration", "Imprisonment")
          , ("L-Cysteine", "Cysteine")
          , ("Lenia_(cellular_automaton)", "Lenia")
          , ("Leveller", "Levellers")
          , ("L-Histidine", "Histidine")
          , ("Linear-quadratic_regulator", "Linear%E2%80%93quadratic_regulator")
          , ("Linoleate", "Linoleic_acid")
          , ("List_of_nuclear_close_calls", "Nuclear_close_calls")
          , ("Litter_(animal)", "Litter_(zoology)")
          , ("L-Leucine", "Leucine")
          , ("L-Methionine", "Methionine")
          , ("L-Phenylalanine", "Phenylalanine")
          , ("L-Serine", "Serine")
          , ("L-Tryptophan", "Tryptophan")
          , ("L-Tyrosine", "Tyrosine")
          , ("Mary,_Crown_Princess_of_Denmark", "Queen_Mary_of_Denmark")
          , ("Medial_prefrontal_cortex", "Prefrontal_cortex#Subdivisions")
          , ("Mental_imagery", "Mental_image")
          , ("MIX", "MIX_(abstract_machine)")
          , ("NaCl", "Sodium_chloride")
          , ("Nail_matrix", "Nail_(anatomy)")
          , ("Near-sightedness", "Myopia")
          , ("Nuclear_reprogramming", "Reprogramming")
          , ("Off-label", "Off-label_use")
          , ("Opregte_Haarlemsche_Courant", "Haarlems_Dagblad")
          , ("Ordered_weighted_averaging_aggregation_operator", "Ordered_weighted_averaging")
          , ("Policing", "Police")
          , ("Polygenic_inheritance", "Quantitative_trait_locus#Quantitative_traits")
          , ("Positron-emission_tomography", "Positron_emission_tomography")
          , ("Preeclampsia", "Pre-eclampsia")
          , ("Purine_nucleotides", "Purine")
          , ("Putney_debates", "Putney_Debates")
          , ("Pyrroline-5-carboxylate_synthase", "Aldehyde_dehydrogenase_18_family,_member_A1")
          , ("Recurrent_networks", "Recurrent_neural_network")
          , ("Recursive_self-improvement", "Technological_singularity#Intelligence_explosion")
          , ("Rhesus_monkey", "Rhesus_macaque")
          , ("Robots.txts", "Robots.txt")
          , ("Sad_music", "Music_and_emotion#Basic_emotions")
          , ("S%C3%A9bastien_Le_Prestre_de_Vauban", "S%C3%A9bastien_Le_Prestre,_Marquis_of_Vauban")
          , ("Second_Anglo-Dutch_war", "Second_Anglo-Dutch_War")
          , ("Sexual_activity", "Human_sexual_activity")
          , ("Ships''_cats", "Ships%27%27_cats")
          , ("Sir_Roger_L%27Estrange", "Roger_L%27Estrange")
          , ("Sodium%E2%80%93glucose_cotransporter_2_inhibitor", "SGLT2_inhibitor")
          , ("Soy", "Soybean")
          , ("Spearman%27s_rho", "Spearman%27s_rank_correlation_coefficient")
          , ("Supervised_classification", "Supervised_learning")
          , ("Taxidermists", "Taxidermy")
          , ("Temporal_pole", "Cerebral_hemisphere")
          , ("ToTok_(app)", "ToTok")
          , ("Uncial", "Uncial_script")
          , ("Venetia_Digby", "Venetia_Stanley")
          , ("Venom_Snake", "Characters_of_the_Metal_Gear_series#Venom_Snake")
          , ("Whistling_Kite", "Whistling_kite")
          , ("White_box_testing", "White-box_testing")
          , ("Wikipedia:Flagged_revisions", "Wikipedia:Pending_changes")
          , ("WRN_protein", "Werner_syndrome_helicase")
          , ("Zoloft", "Sertraline")
          , ("Entrepreneur", "Entrepreneurship")
          , ("ABCB1", "P-glycoprotein")
          , ("Ab_urbe_condita_(Livy)", "History_of_Rome_(Livy)")
          , ("Bad_Boy_Entertainment", "Bad_Boy_Records")
          , ("Cargo_cult_science", "Surely_You%27re_Joking,_Mr._Feynman!#Cargo_cult_science")
          , ("Chronic_fatigue_syndrome", "Myalgic_encephalomyelitis/chronic_fatigue_syndrome")
          , ("Cipriani", "Cipriani_S.A.")
          , ("Cryptocurrencies", "Cryptocurrency")
          , ("Dwight_Eisenhower", "Dwight_D._Eisenhower")
          , ("Gustatory_system", "Taste")
          , ("Hebbian_plasticity", "Hebbian_theory")
          , ("History_of_the_Second_Avenue_Subway", "Unbuilt_plans_for_the_Second_Avenue_Subway")
          , ("Jiangsu_province", "Jiangsu")
          , ("Literary_studies", "Literary_criticism")
          , ("Rebecca_F._Kuang", "R._F._Kuang")
          , ("The_LOX", "The_Lox")
          , ("The_New_York_Times_crossword_puzzle", "The_New_York_Times_Crossword")
          , ("Time-delay_embedding", "Takens%27s_theorem")
          , ("Tor_Browser", "Tor_(network)#Tor_Browser")
          , ("Unicyclist", "Unicycle")
          , ("Wonderlic_Personnel_Test", "Wonderlic_test")
          , ("Yale_Law_Journal", "The_Yale_Law_Journal")
          , ("Yiddish_culture", "Yiddishkeit")
          ] ++ -- disambiguation overrides:
        [ ("$20", "United_States_twenty-dollar_bill")
        , ("A1C", "Glycated_hemoglobin")
        , ("Alcohol", "Alcohol_(drug)")
        , ("Alfred_Loomis", "Alfred_Lee_Loomis")
        , ("Ali_Baba_(disambiguation)", "Alibaba_Group")
        , ("American_quarter", "Quarter_(United_States_coin)")
        , ("Anonymous_web_browsing", "Dark_web")
        , ("APL", "APL_(programming_language)")
        , ("APOE", "Apolipoprotein_E")
        , ("Arthur_Samuel", "Arthur_Samuel_(computer_scientist)")
        , ("Arthur_Young", "Arthur_Young_(agriculturist)")
        , ("ASIP", "Agouti-signaling_protein")
        , ("Assessment", "Educational_assessment")
        , ("Atomic_Energy_Commission", "United_States_Atomic_Energy_Commission")
        , ("Babel_fish", "The_Hitchhiker%27s_Guide_to_the_Galaxy")
        , ("Bantu", "Bantu_languages")
        , ("Big_Five", "Big_Five_personality_traits")
        , ("Bing", "Microsoft_Bing")
        , ("Bipolar", "Bipolar_disorder")
        , ("BM25", "Okapi_BM25")
        , ("Boost_Mobile", "Boost_Mobile_(United_States)")
        , ("Brain_stimulation", "Electrical_brain_stimulation")
        , ("Central_African", "Central_Africa")
        , ("Chain_of_thought", "Prompt_engineering#Chain-of-thought")
        , ("Charles_Brenner", "Charles_Brenner_(mathematician)")
        , ("Charles_Stein", "Charles_M._Stein")
        , ("Charly", "Charly_(1968_film)")
        , ("Cochlear", "Cochlea")
        , ("Cocoa", "Cocoa_solids")
        , ("Colorization", "Film_colorization")
        , ("Congo", "Congo_Basin")
        , ("Coq", "Coq_(software)")
        , ("Cortex", "Cerebral_cortex")
        , ("Deep_Blue", "Deep_Blue_(chess_computer)")
        , ("Delayed_parenthood", "Paternal_age_effect")
        , ("Depression", "Depression_(mood)")
        , ("Dolomite", "Dolomite_(mineral)")
        , ("Drachma", "Ancient_drachma")
        , ("Dub", "Dubbing")
        , ("Echolocation", "Human_echolocation")
        , ("Energy_efficiency", "Electrical_efficiency")
        , ("Enigma", "Enigma_machine")
        , ("Ensign", "Ensign_(flag)#Heraldic_ensigns")
        , ("Ephedra", "Ephedra_(medicine)")
        , ("EQP", "Equational_prover")
        , ("Error_rate", "Bayes_error_rate")
        , ("Expressivity", "Expressive_suppression")
        , ("Few-shot_learning", "Prompt_engineering")
        , ("Fine-tuning", "Fine-tuning_(deep_learning)")
        , ("Finnish", "Finnish_language")
        , ("Forced_choice", "Ipsative")
        , ("Fourth_Amendment", "Fourth_Amendment_to_the_United_States_Constitution")
        , ("FPU", "Floating-point_unit")
        , ("Fran%C3%A7ois_de_La_Rochefoucauld", "Fran%C3%A7ois_de_La_Rochefoucauld_(writer)")
        , ("FTO", "FTO_gene")
        , ("Gaster", "Gaster_(insect_anatomy)")
        , ("Gene_editing", "Genetic_engineering")
        , ("General_Tso", "General_Tso%27s_chicken")
        , ("Genetic_variant", "Mutation")
        , ("Gestalt", "Gestalt_psychology")
        , ("Great_Famine", "Great_Chinese_Famine")
        , ("Green_pepper", "Bell_pepper")
        , ("Greenspace", "Urban_green_space")
        , ("Greylock", "Greylock_Partners")
        , ("H100", "Hopper_(microarchitecture)")
        , ("Hearing_Voices", "Hearing_Voices_Movement")
        , ("Helion", "Helion_Energy")
        , ("Herbert_Simon", "Herbert_A._Simon")
        , ("Here_Comes_Everybody", "Here_Comes_Everybody_(book)")
        , ("Higurashi", "Higurashi_When_They_Cry")
        , ("Howl%27s_Moving_Castle", "Howl%27s_Moving_Castle_(film)")
        , ("Human_performance", "Performance_science")
        , ("Hypoxia", "Hypoxia_(medicine)")
        , ("IFrame", "HTML_element#Frames")
        , ("Inferior", "Inferior_good")
        , ("Information_search", "Information_foraging")
        , ("Insula", "Insular_cortex")
        , ("Intoxication", "Substance_intoxication")
        , ("Jack_Warner", "Jack_L._Warner")
        , ("James_Young", "James_Young_(chemist)")
        , ("Jill_Scott", "Jill_Scott_(singer)")
        , ("John_McCarthy", "John_McCarthy_(computer_scientist)")
        , ("John_Wheeler", "John_Archibald_Wheeler")
        , ("Jon_Bentley", "Jon_Bentley_(computer_scientist)")
        , ("Kate_Atkinson", "Kate_Atkinson_(writer)")
        , ("Kevin_Scott", "Kevin_Scott_(computer_scientist)")
        , ("KHK", "Hepatic_fructokinase")
        , ("Kuru", "Kuru_(disease)")
        , ("Lactate", "Lactic_acid")
        , ("Lamb", "Sheep")
        , ("Lazear", "Edward_Lazear")
        , ("Little_Nell", "Nell_Trent")
        , ("Los_Alamos", "Los_Alamos,_New_Mexico")
        , ("Lucky_Star", "Lucky_Star_(manga)")
        , ("Luis_%C3%81lvarez", "Luis_Walter_Alvarez")
        , ("Machiavellianism", "Machiavellianism_(psychology)")
        , ("Manometry", "Esophageal_motility_study")
        , ("Mate", "MATE_(desktop_environment)")
        , ("MCMC", "Markov_chain_Monte_Carlo")
        , ("MCTS", "Monte_Carlo_tree_search")
        , ("Medicare", "Medicare_(United_States)")
        , ("Medulla", "Medulla_oblongata")
        , ("Merz", "Merz_(art_style)")
        , ("Methone", "Methone_(Messenia)")
        , ("Mujo", "Impermanence")
        , ("Nail", "Nail_(anatomy)")
        , ("Negative_selection", "Negative_selection_(natural_selection)")
        , ("Newton", "Isaac_Newton")
        , ("New_York", "New_York_(state)")
        , ("Oliver_Byrne", "Oliver_Byrne_(mathematician)")
        , ("Parian", "Paros")
        , ("Path_analysis", "Path_analysis_(statistics)")
        , ("Paul_Graham", "Paul_Graham_(programmer)")
        , ("Pausanias", "Pausanias_(geographer)")
        , ("Piecewise_linear", "Piecewise_linear_function")
        , ("Plantain", "Cooking_banana")
        , ("Pleomorphism", "Pleomorphism_(cytology)")
        , ("Population_structure", "Population_structure_(genetics)")
        , ("Project_Orion", "Project_Orion_(nuclear_propulsion)")
        , ("Public-benefit_corporation", "Benefit_corporation")
        , ("Rag", "Prompt_engineering#Retrieval-augmented_generation")
        , ("RCT", "Randomized_controlled_trial")
        , ("Rem", "Rapid_eye_movement_sleep")
        , ("Rick_Cook", "Rick_Cook_(writer)")
        , ("Rising_Sun", "Rising_Sun_(Crichton_novel)")
        , ("Romani", "Romani_people")
        , ("Ropeway", "Cable_transport")
        , ("Sadism", "Sadomasochism")
        , ("Sandhurst", "Royal_Military_Academy_Sandhurst")
        , ("San_Diego_University", "San_Diego_State_University")
        , ("Satiation", "Satiety")
        , ("Scala", "Scala_(programming_language)")
        , ("Schedule_4", "Controlled_Substances_Act#Schedule_IV_controlled_substances")
        , ("SCNT", "Somatic_cell_nuclear_transfer")
        , ("Score_function", "Informant_(statistics)")
        , ("Self-regulation", "Self-control")
        , ("Serotonergic", "Serotonin")
        , ("Sh%C5%8Dnen_Jump", "Weekly_Sh%C5%8Dnen_Jump")
        , ("Snuff", "Snuff_(tobacco)")
        , ("Social_engineering", "Social_engineering_(security)")
        , ("Spoons", "Spoon")
        , ("State_of_Mind", "A_State_of_Mind_(film)")
        , ("Stephen_Schneider", "Stephen_Schneider_(scientist)")
        , ("Summary", "Automatic_summarization")
        , ("Swahili", "Swahili_language")
        , ("Tahnoun_Al_Nahyan", "Tahnoun_bin_Zayed_Al_Nahyan_(national_security_advisor)")
        , ("Tanuki", "Bake-danuki")
        , ("Textfiles", "Textfiles.com")
        , ("The_Late_Show", "The_Late_Show_with_Stephen_Colbert")
        , ("The_Woman_in_Me", "The_Woman_in_Me_(album)")
        , ("Transplant", "Organ_transplantation")
        , ("UKBB", "UK_Biobank")
        , ("Ultra", "Ultra_(cryptography)")
        , ("UMAP", "Nonlinear_dimensionality_reduction#Uniform_manifold_approximation_and_projection")
        , ("Undecidable", "Undecidable_problem")
        , ("Valerian", "Valerian_(herb)")
        , ("VI", "Vi_(text_editor)")
        , ("Weight_Watchers", "Weight_Watchers_(diet)")
        , ("White_Russia", "White_%C3%A9migr%C3%A9")
        , ("X.com", "X_Corp.")
        , ("X.Org", "X.Org_Server")
        , ("Yam", "Yam_(vegetable)")
        , ("Yeezy", "Adidas_Yeezy")
        , ("Bird_seed", "Bird_food")
        , ("Charles_Stein_(statistician)", "Charles_M._Stein")
        , ("Chinatowns_in_Latin_America", "Chinatowns_in_Latin_America_and_the_Caribbean")
        , ("Cold_plasma", "Nonthermal_plasma")
        , ("Cold_rolled_steel", "Cold-formed_steel")
        , ("Computer_algebra_systems", "Computer_algebra_system")
        , ("DDR3", "DDR3_SDRAM")
        , ("DDR4", "DDR4_SDRAM")
        , ("Dengue", "Dengue_fever")
        , ("Determinants", "Determinant")
        , ("Disney+", "Disney%2B")
        , ("Dostoevsky", "Fyodor_Dostoevsky")
        , ("DRAM", "Dynamic_random-access_memory")
        , ("Eurasianist", "Eurasianism")
        , ("General_Certificate_of_Secondary_Education", "GCSE")
        , ("Github_Copilot", "GitHub_Copilot")
        , ("Gumilev_Eurasian_National_University", "L._N._Gumilev_Eurasian_National_University")
        , ("Hot_rolled_steel", "Rolling_(metalworking)#Hot_rolling")
        , ("Human_factors_and_ergonomics", "Ergonomics")
        , ("LPDDR4", "LPDDR#LPDDR4")
        , ("Magnetron", "Cavity_magnetron")
        , ("Matrices", "Matrix")
        , ("Ministry_of_Education_of_the_Russian_Federation", "Ministry_of_Education_(Russia)")
        , ("Nursultan_Nazarbaev", "Nursultan_Nazarbayev")
        , ("Paramount+", "Paramount%2B")
        , ("Passionarity", "Lev_Gumilev#Ideas")
        , ("Poe_(software)", "Quora#Poe")
        , ("Psychological_development", "Developmental_psychology")
        , ("Pushkin", "Alexander_Pushkin")
        , ("Rational_expectations_hypothesis", "Rational_expectations")
        , ("Rosetta_stone", "Rosetta_Stone")
        , ("Russian_idea", "Russian_Idea")
        , ("Social_media_advertising", "Social_media_marketing")
        , ("Superforecasting", "Superforecasting:_The_Art_and_Science_of_Prediction")
        , ("Third_Rome", "Succession_of_the_Roman_Empire")
        , ("Transformer_(machine_learning_model)", "Transformer_(deep_learning_architecture)")
        , ("Trithemius’s_cipher", "Trithemius%E2%80%99s_cipher")
        , ("Uniform_Manifold_Approximation_and_Projection", "Nonlinear_dimensionality_reduction#Uniform_manifold_approximation_and_projection")
        , ("U.S._Department_of_Transportation", "United_States_Department_of_Transportation")
        , ("Intrasexual_competition", "Female_intrasexual_competition")
        , ("Waka", "Waka_(poetry)")
        , ("Mnet", "https://web.archive.org/web/**************/https://en.wikipedia.org/wiki/Mnet_(peer-to-peer_network)")
        , ("1979_Cleveland_Elementary_School_shooting_(San_Diego)", "Cleveland_Elementary_School_shooting_(San_Diego)")
        , ("ANZ_Bank", "ANZ_(bank)")
        , ("Artificial_neural_network", "Neural_network_(machine_learning)")
        , ("Asymmetric_Numeral_Systems", "Asymmetric_numeral_systems")
        , ("Celiac_disease", "Coeliac_disease")
        , ("Chimney_sweeps", "Chimney_sweep")
        , ("Conjuring", "Conjuration")
        , ("Conversation_threading", "Thread_(online_communication)")
        , ("Face-to-face_interaction", "Human_communication")
        , ("FDA_Amendments_Act", "Food_and_Drug_Administration_Amendments_Act_of_2007")
        , ("Fractals", "Fractal")
        , ("Geraniol_synthase", "Geranyl_diphosphate_diphosphatase")
        , ("Go-Hanazono", "Emperor_Go-Hanazono")
        , ("Go-Komatsu", "Emperor_Go-Komatsu")
        , ("Go-Toba", "Emperor_Go-Toba")
        , ("H100s", "Hopper_(microarchitecture)")
        , ("Immortal_time_bias", "Survivorship_bias")
        , ("Interspousal_correlation", "Homogamy_(sociology)")
        , ("Klonopin", "Clonazepam")
        , ("Linear_time", "Time_complexity#Linear_time")
        , ("Merkel", "Angela_Merkel")
        , ("N-Acetylcysteine", "Acetylcysteine")
        , ("NBER", "National_Bureau_of_Economic_Research")
        , ("Neutron_star_mergers", "Neutron_star_merger")
        , ("Nicholas_II_of_Russia", "Nicholas_II")
        , ("Nij%C5%8D_house", "Nij%C5%8D_family")
        , ("Polymorphism_(materials_science)", "Crystal_polymorphism")
        , ("Publication_selection_bias", "Publication_bias")
        , ("Public_education", "State_school")
        , ("Quantile_regressions", "Quantile_regression")
        , ("Quantum_physics", "Quantum_mechanics")
        , ("Random_Forests", "Random_forest")
        , ("Sergei_Lavrov", "Sergey_Lavrov")
        , ("Seroquel", "Quetiapine")
        , ("Shingosh%C5%ABish%C5%AB", "Shingosh%C5%ABi_Wakash%C5%AB")
        , ("Shunzei", "Fujiwara_no_Shunzei")
        , ("S.L.A._Marshall", "S._L._A._Marshall")
        , ("Statistical_hypothesis_testing", "Statistical_hypothesis_test")
        , ("Structural_similarity", "Structural_similarity_index_measure")
        , ("Supernovae", "Supernova")
        , ("Sympatric", "Sympatry")
        , ("Teika", "Fujiwara_no_Teika")
        , ("Values_Scale", "Values_scale")
        , ("Visual_illusion", "Optical_illusion")
        , ("Working_papers", "Working_paper")
        , ("Adobe_Stock", "Adobe_Creative_Cloud#Desktop,_mobile,_and_web_services")
        , ("Antisocial_behavior", "Anti-social_behaviour")
        , ("Arctic_anthropology", "Circumpolar_peoples")
        , ("Breeder%27s_equation", "Heritability#Response_to_selection")
        , ("Broader_autism_phenotype", "Autism_spectrum#Broader_autism_phenotype")
        , ("Cattell-Horn-Carroll_theory", "Cattell%E2%80%93Horn%E2%80%93Carroll_theory")
        , ("Childhood_autistic_disorder", "Classic_autism")
        , ("Computer_programmer", "Programmer")
        , ("Controlled_experiment", "Scientific_control#Controlled_experiments")
        , ("Criterion_validities", "Criterion_validity")
        , ("Einstein", "Albert_Einstein")
        , ("Fat_acceptance", "Fat_acceptance_movement")
        , ("Fibonacci_tilings", "Domino_tiling")
        , ("First_International_Polar_Year", "International_Polar_Year#The_First_International_Polar_Year_(1882%E2%80%931883)")
        , ("Fixation_probability", "Fixation_(population_genetics)#Probability")
        , ("Flash_devices", "Flash_memory")
        , ("Genotype-phenotype_distinction", "Genotype%E2%80%93phenotype_distinction")
        , ("Hard_disk_drives", "Hard_disk_drive")
        , ("Hazard_rates", "Survival_analysis#Hazard_function_and_cumulative_hazard_function")
        , ("Jane%27s_Fighting_Ships", "Janes_Fighting_Ships")
        , ("Kitab_al-ibar", "Kitab_al-Ibar")
        , ("K-Nearest_Neighbors", "K-nearest_neighbors_algorithm")
        , ("Lexemes", "Lexeme")
        , ("Linear_type", "Substructural_type_system#Linear_type_systems")
        , ("List_of_National_Basketball_Association_career_scoring_leaders", "List_of_NBA_career_scoring_leaders")
        , ("Maillard_products", "Maillard_reaction")
        , ("McCarty_Little", "William_McCarty_Little")
        , ("Navier-Stokes", "Navier%E2%80%93Stokes_equations")
        , ("Neural_tangent_kernels", "Neural_tangent_kernel")
        , ("New_York_Times_crossword_puzzle", "The_New_York_Times_Crossword")
        , ("Normalizing_flow", "Flow-based_generative_model")
        , ("Northern_Sami_language", "Northern_S%C3%A1mi")
        , ("Northern_Sotho_language", "Northern_Sotho")
        , ("Optical_data_storage", "Optical_storage")
        , ("Opus_codec", "Opus_(audio_format)")
        , ("Pangenome", "Pan-genome")
        , ("Pareto_distributions", "Pareto_distribution")
        , ("Pass-by-reference", "Evaluation_strategy#Call_by_reference")
        , ("Pass-by-value", "Evaluation_strategy#Call_by_value")
        , ("Pentaploid", "Polyploidy#Polyploid_types")
        , ("Philippines_Campaign_(1944%E2%80%9345)", "Philippines_campaign_(1944%E2%80%931945)")
        , ("Pinwheel_Scheduling_Problem", "Pinwheel_scheduling")
        , ("Power_law_distribution", "Power_law")
        , ("Prompt_injection", "Prompt_engineering#Prompt_injection")
        , ("Quantum_error-correcting_code", "Quantum_error_correction")
        , ("Qubits", "Qubit")
        , ("Rust_programming_language", "Rust_(programming_language)")
        , ("Sacred", "Sacredness")
        , ("Schizophrenic", "Schizophrenia")
        , ("Securities_and_Exchange_Commission", "U.S._Securities_and_Exchange_Commission")
        , ("Signal_(software)", "Signal_(disambiguation)#Computing")
        , ("Solar_geoengineering", "Solar_radiation_modification")
        , ("SRY", "Sex-determining_region_Y_protein")
        , ("Ternary_computation", "Ternary_computer")
        , ("Thematic_apperception_test", "Thematic_Apperception_Test")
        , ("THRB", "Thyroid_hormone_receptor_beta")
        , ("Thyroid_hormone", "Thyroid_hormones")
        , ("Unreal_Engine_5", "Unreal_Engine#Unreal_Engine_5")
        , ("Wargaming", "Wargame")
        , ("Y-axis", "Cartesian_coordinate_system")
        , ("501(c)4", "501(c)_organization#501(c)(4)")
        , ("A16z", "Andreessen_Horowitz")
        , ("Brain_structure", "Brain")
        , ("Business_dynamism", "Economic_dynamism")
        , ("Carbon-fiber-reinforced_polymers", "Carbon-fiber_reinforced_polymer")
        , ("Charlie_Stross", "Charles_Stross")
        , ("Copy-number_variations", "Copy_number_variation")
        , ("Diffusion_(machine_learning)", "Diffusion_model")
        , ("Glucagon_like_peptide-1_receptor_agonists", "GLP-1_receptor_agonist")
        , ("GPT_(language_model)", "Generative_pre-trained_transformer")
        , ("GPT", "Generative_pre-trained_transformer")
        , ("Guy_Steele", "Guy_L._Steele_Jr.")
        , ("Han_purple", "Han_purple_and_Han_blue")
        , ("IAPAC", "Public_Affairs_Alliance_of_Iranian_Americans")
        , ("Language_model_evaluation", "Language_model#Evaluation_and_benchmarks")
        , ("Mike_Gallagher", "Michael_Gallagher")
        , ("Political_connection", "Lobbying")
        , ("Population_stratification", "Population_structure_(genetics)")
        , ("Postmenopausal", "Menopause#Postmenopause")
        , ("Russian_ambassador_to_the_United_States", "List_of_ambassadors_of_Russia_to_the_United_States")
        , ("Sesquiterpenes", "Sesquiterpene")
        , ("Sony_Online_Entertainment", "Daybreak_Game_Company")
        , ("Speaker_diarization", "Speaker_diarisation")
        , ("Spoken-word", "Spoken_word")
        , ("Star-free_languages", "Star-free_language")
        , ("Samuel_Huntington", "Samuel_P._Huntington")
        , ("Inkling", "The_Inklings")
        , ("American_Constitution", "Constitution_of_the_United_States")
        , ("Blood_brain_barrier", "Blood%E2%80%93brain_barrier")
        , ("Bukharin", "Nikolai_Bukharin")
        , ("Buy-and-hold", "Buy_and_hold")
        , ("Car_radio", "Vehicle_audio")
        , ("Copyright_transfer", "Copyright_transfer_agreement")
        , ("C++_(programming_language)", "C%2B%2B")
        , ("Difference_in_difference_design", "Difference_in_differences")
        , ("Dragoneer", "Dragoneer_Investment_Group")
        , ("DSM-IV_codes", "List_of_mental_disorders_in_the_DSM-IV_and_DSM-IV-TR")
        , ("Du_Pont", "Dupont")
        , ("Fitts%27_law", "Fitts%27s_law")
        , ("Fraternal_twin", "Twin#Fraternal")
        , ("Gastropod", "Gastropoda")
        , ("Gladiator_2", "Gladiator_II")
        , ("Horizons_Ventures", "Li_Ka-shing#Internet_and_technology")
        , ("Human_cognition", "Cognition")
        , ("KNN", "k-nearest_neighbors_algorithm")
        , ("kNN", "k-nearest_neighbors_algorithm")
        , ("Bagging", "Bootstrap_aggregating")
        , ("Purina", "Nestl%C3%A9_Purina_PetCare")
        , ("UCT", "Monte_Carlo_tree_search")
        , ("DOI", "Digital_object_identifier")
        , ("Pattern_separation", "Place_cell#Pattern_separation")
        , ("Stockfish", "Stockfish_(chess)")
        , ("Imagen", "Imagen_(Google_Brain)")
        , ("SARSA", "State%E2%80%93action%E2%80%93reward%E2%80%93state%E2%80%93action")
        , ("Bruegel", "Pieter_Bruegel_the_Elder")
        , ("Mr._Natural", "Mr._Natural_(character)")
        , ("Chunking", "Chunking_(psychology)")
        , ("Chunked", "Chunking_(psychology)")
        , ("SVM", "Support_vector_machine")
        , ("PCA", "Principal_component_analysis")
        , ("SMIC", "Semiconductor_Manufacturing_International_Corporation")
        , ("Admixture", "Genetic_admixture")
        , ("LLM",  "Large_language_model")
        , ("LLMs", "Large_language_model")
        , ("Genetic_correlate", "Genetic_correlation")
        , ("Genetic_correlates", "Genetic_correlation")
        , ("Evelyn_Baring,_1<sup>st</sup>_Earl_of_Cromer", "Evelyn_Baring,_First_Earl_of_Cromer")
        , ("Nike", "Nike,_Inc.")
        , ("CSV", "Comma-separated_values")
        , ("WAIS", "Wechsler_Adult_Intelligence_Scale")
        , ("Bluesky", "Bluesky_(social_network)")
        , ("Alice_Bradley_Sheldon", "James_Tiptree_Jr.")
        , ("Pit_bulls", "Pit_bull")
        , ("Third_space", "Third_place")
        , ("Lister", "Joseph_Lister")
        , ("Fleming", "Alexander_Fleming")
        , ("Nearest_neighbor", "Nearest_neighbor_search")
        , ("Huygens", "Christiaan_Huygens")
        , ("Processing_speed", "Mental_chronometry")
        , ("BPP", "BPP_(complexity)")
        , ("Hacker_news", "Hacker_News")
        , ("Taxi_medallions", "Taxi_medallion")
        , ("Scheme", "Scheme_(programming_language)")
        , ("Suno", "Suno_AI")
        , ("Elo", "Elo_rating_system")
        , ("Fermi_Problem", "Fermi_problem")
        , ("Test-retest", "Repeatability")
        , ("☞", "Manicule")
        , ("Piaget", "Jean_Piaget")
        , ("Piagetian", "Piaget%27s_theory_of_cognitive_development")
        , ("HFT", "High-frequency_trading")
        , ("Facet", "Facet_(psychology)")
        , ("Java", "Java_(programming_language)")
        , ("Lean", "Lean_(proof_assistant)")
        , ("Will_Wilkinson", "https://web.archive.org/web/20250207111732/https://en.wikipedia.org/wiki/Will_Wilkinson")
        , ("Hash", "Hash_function")
        , ("BBS", "Bulletin_board_system")
        , ("Feliway", "Cat_pheromone")
        , ("Stephen_A._Ross", "Stephen_Ross_(economist)")
        , ("PAC", "Probably_approximately_correct_learning")
        , ("Coalescence", "https://en.wikipedia.org/wiki/Coalescent_theory")
        , ("Coalescent", "https://en.wikipedia.org/wiki/Coalescent_theory")
        , ("Hanabi", "Hanabi_(card game)")
        ]
