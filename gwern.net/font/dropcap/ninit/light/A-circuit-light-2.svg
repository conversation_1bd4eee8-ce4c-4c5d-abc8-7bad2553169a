<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="84 -18 880 952"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#e2e1e2}.C{fill:#d9d8d9}.D{fill:#d0cfd0}.E{fill:#eeeded}.F{fill:#1e1d1e}.G{fill:#c2c1c2}.H{fill:#373637}.I{fill:#121212}.J{fill:#f2f1f2}.K{fill:#302f30}.L{fill:#a1a0a0}.M{fill:#010101}.N{fill:#242324}.O{fill:#181819}.P{fill:#898889}.Q{fill:#e8e8e8}.R{fill:#b7b6b7}.S{fill:#c9c9c9}.T{fill:#49494a}.U{fill:#2b2a2b}.V{fill:#666}.W{fill:#a7a7a6}.X{fill:#585758}.Y{fill:#bdbcbd}.Z{fill:#737273}.a{fill:#b1b1b2}.b{fill:#070707}.c{fill:#3d3d3d}.d{fill:#acabac}.e{fill:#292828}.f{fill:#939293}.g{fill:#858485}.h{fill:#5f5e5f}.i{fill:#504f50}.j{fill:#0d0d0d}.k{fill:#6f6f6f}.l{fill:#8d8c8c}.m{fill:#424243}.n{fill:#f8f8f7}</style><path d="M735 374h2l5 1c-3 0-5 0-7-1zm40-111l1-1c1 0 5 0 6 1-2 1-5 0-7 0zm-76 41h2v1 1c-1 0-2 0-3-1l1-1z" class="B"></path><path d="M302 154c3 0 6 1 8 2-2 0-3-1-5-1s-2 0-3-1zM92 343c1 0 2-1 3 0 2 0 3 0 5 1-3 0-5-1-8-1z" class="E"></path><path d="M829 231h1v1h-7-2c1-1 6 0 8-1h0z" class="B"></path><path d="M203 927h0c3 3 5 3 8 3-1 1-3 1-4 1-2-1-3-3-4-4z" class="C"></path><path d="M229 192h1c2-1 9-1 11 0h-1-11z" class="D"></path><path d="M903 254c-3 0-9 0-12-1h12v1z" class="B"></path><path d="M295 412h1l1 1-2 3h-1-1c1-2 1-3 2-4z" class="Q"></path><path d="M938 447h2c-1 1-6 1-8 1h-3c2-2 7-1 9-1z" class="E"></path><path d="M222 585l11-1v1l-10 1-1-1z" class="C"></path><path d="M770 180h1v1h-5c-1 1-3 1-5 1-1 0 0 0-1-1h1c3 0 6-1 9-1zm-418 68c-1 0-3 0-4-1-2 0-4 1-6-1h1 2c2 0 4 0 7 1v1z" class="Q"></path><path d="M352 247c3 0 9 0 12 1v1l-12-1v-1z" class="B"></path><path d="M603 105h1c3 0 10 0 11 1l-1 1c-3-2-8 0-11-2zM313 265c1 0 1 0 2 1h3l1 1c-2 0-3 0-4 1-2-2-2-2-2-3z" class="E"></path><path d="M684 237v1c1 2 0 4 0 6h-1c0-2 0-5 1-7z" class="Q"></path><path d="M257 951h1 1v1c2 0 7-1 8 1-2 0-7 0-9-1 0 0-1 0-1-1h0z" class="E"></path><path d="M353 208c4-1 7 0 10 1 2 1 4 2 4 4l-1-1c-4-3-8-4-13-4z" class="Q"></path><path d="M288 70h1c2 1 6 1 9 1 0 0 1-1 2 0h-11c0 2 0 2-1 3v-4z" class="C"></path><path d="M87 381h3c3 1 10 1 12 3-2 0-4 0-5-1l-6-1c-2 0-3 0-4-1z" class="E"></path><path d="M259 542h1c0 2-1 3-2 5-1 0-1 0-2-1l2-4h1z" class="B"></path><path d="M101 354h0c2 2 5 2 7 2v1h-4-2v3h0l-1-1v-5z" class="Q"></path><path d="M193 458c3-1 6 0 9-1h5l-1 1c-3 0-6 1-9 0h-4z" class="E"></path><path d="M896 389h3c0 2-2 5-3 6v-6z" class="B"></path><path d="M846 163h1l1 2c0 1-1 2-1 3-2-1-2-2-3-2 1-1 1-2 2-3z" class="k"></path><path d="M375 953h2v9h-2 1c-1-3 0-7-1-9h-4 0c1-1 3-1 4 0z" class="B"></path><path d="M192 376l4 2h2v1c-1 1-3 1-4 1h-1c1 0-1-3-1-4z" class="C"></path><path d="M888 595l1 1c0 3 0 7-1 9-1-2-1-5-1-7 1-1 1-2 1-3z" class="B"></path><path d="M657 181h2c0 1 1 2 1 3h-1l-3 1-1-1c1-2 1-2 2-3z" class="G"></path><path d="M269 500c1-1 2-1 4 0 0-1 1-1 1-1 2 1 3 1 5 2h-10v-1zm-52-191c-1 1-2 1-3 1h-3c-2 0-9 1-10 0 5-1 10-1 15-1h1z" class="Q"></path><path d="M506 641l4-1c-1 1 0 1-1 2 0 1 0 1-1 2-1 0-1 1-2 0-1-2-1-2 0-3z" class="F"></path><path d="M375 953c0-2 1-6 0-7v-1c0-1 0-1 1-2h0v1c0 1 0 1 1 1h0 5c3 1 5 1 8 1 1 0 3-1 4 0h-18v6c1 0 1 0 1 1h0-2z" class="C"></path><path d="M681 298v1c0 1-1 3-1 4l-1 1c0 3-1 7-1 10v-3l-1-1c1-2 1-5 2-7h0c0-2 1-3 2-5zM215 403h-1 1v-1h-1c1-1 2-2 3-2s1 0 2-1h1l1 1h0c-1 1-4 3-6 3z" class="E"></path><path d="M722 201h0c-1 4-3 8-3 11 0 2 1 3 2 4s1 0 1 2h-2l-1-4c-2-4 1-9 3-13zm-92 702c1 1 0 7 1 8 0 0 1 0 2 1l-1 1c-1 0-1 1-1 1h-1c-1-3-1-8 0-11z" class="B"></path><path d="M792 301c-5 1-9 1-14 1h-5-5 2c2 0 3 0 5-1h7c3-1 6 0 10 0h0zM238 946h1 1c4 1 9 1 13 1v1h-3c-4 0-7-1-10 0v1c-1 1-1 2-2 3v-1c1-1 0-3 0-5z" class="C"></path><path d="M343 225h-30l1-1c3 1 24 0 29 0v1z" class="Q"></path><path d="M771 356c8 0 16 1 24 1h0c-4 2-11 0-15 0h-9v-1z" class="B"></path><path d="M343 224l1-3h1 0c1 1 2 2 3 2v1c-1 1-2 2-4 3l-1-2v-1z" class="i"></path><path d="M912 403v1c-2 3-4 8-7 11v-1c1-4 4-7 7-11z" class="Q"></path><path d="M289 454c1 2 0 4 0 5 1 5 1 10 0 14l-1-1c0-4 1-7 1-10 0-2-2-4-1-5h1v-3z" class="B"></path><path d="M912 413l1 1-1 1 1 1h0c-2 2-3 3-4 6h-1v-2-1h1v-1-1l3-4z" class="E"></path><path d="M336 239c5 1 10 0 16 1l5 1c2 0 4 0 6 2-2 0-6-1-8-2h-3-2c-3-1-6-1-9-1-2 0-4 0-5-1z" class="B"></path><path d="M418 751c1 2 1 4 1 6v12 12l-1 1v-31z" class="D"></path><path d="M678 100c1 0-1 5-2 7-1 4 1 8 2 12v1s1 1 1 2l-2 2h-1l1-2c0-1 1-2 0-3-1-4-3-9-1-13l2-6zM222 586h-15 0c5-1 10-1 15-1l1 1h-1z" class="B"></path><path d="M703 215c0 2-1 4-2 6-1 1-2 1-4 1 1-1 1-3 2-4h1c1-1 1-1 1-2h0l2-1z" class="h"></path><path d="M146 483h-21v-1h5c4 0 8 0 11-1 2 0 3 0 4 1l1 1z" class="Q"></path><path d="M180 627h35 0-35 0z" class="B"></path><path d="M756 328v3l-2 1h-2-1l-1-4h6z" class="G"></path><path d="M752 332l-1-2h2s0 1 1 1v1h-2z" class="S"></path><path d="M284 958c2 1 2 2 3 4-1 1-1 3-2 4-1 0-1 0-1-1-1-1-1-2-2-3h0l1-1c0-1 1-2 1-3z" class="T"></path><path d="M635 207l1-1c1 0 2 0 3 1-1 1-1 3-1 5 1 2 2 3 0 6l-3-11z" class="B"></path><path d="M591 80v2c-1 3-1 5-1 8 2 7 5 13 8 20h-1l-3-6c-1-4-3-8-4-12h0c-1-4-1-8 1-12z" class="C"></path><path d="M817 619c2 0 6 0 8 1l-1 1c0 1-1 1-1 1-3 0-5 0-7-1l1-2z" class="c"></path><path d="M809 286c2-1 5 0 8 0h14c3 0 7 0 10 1-4 1-8 0-12 0-6 0-13 0-20-1z" class="B"></path><path d="M812 560l6-2c1 0 2 1 3 1-1 1-3 1-4 2l-2 1c-2 0-3 0-4 1 0-1 1-2 2-3h-1z" class="c"></path><path d="M833 388c0-2 0-9-1-11h0c0-1 0-1 2-2 2 2 0 9 1 12v1h-2z" class="B"></path><path d="M825 325c2 0 3 1 4-1v2l-2 2h-1v1c-3 1-1 4-5 4l1-1h0l1-1-1-1 1-2c1-2 1-2 2-3z" class="V"></path><path d="M741 274h3l1 2c0 1 0 1-1 3h-1c-1 0-2 0-3-1 0-1 0-2 1-4z" class="G"></path><path d="M575 67c2-1 5 0 8 0h21-1c-3 1-26 1-28 0z" class="B"></path><path d="M829 326v1c1 1 1 2 3 3 1 0 2 1 4 0 1-1 1-2 1-4h1c0 2 0 2-1 4 0 1-1 1-2 2-3 0-5-1-7-2-1-1-1-1-2-1v-1h1l2-2z" class="Z"></path><path d="M628 125h1v1c-3 4-6 11-11 13h-1 0c3-2 5-4 6-6s2-3 3-5v-1l2-2z" class="Q"></path><path d="M835 663l2 1c1 7 0 15 0 22-1-1-2-20-2-23zm-645-49l-18 1c-4 0-7 0-10-1-4-1-7-2-10-3l1-1c3 2 6 3 9 3l12 1h16z" class="R"></path><path d="M787 377h1c-2 4-1 13-1 17l3 10-1 1v-1c-3-8-4-19-2-27z" class="Q"></path><path d="M781 337h15c4 0 8 0 12 2h0c-2 0-4-1-6-1-6-1-13 0-18 0-2 0-4 1-6 1v1h-1-1-1c2-2 4-2 6-3z" class="D"></path><path d="M180 627c-11 0-24 2-34-3v-1c2 1 4 2 6 2h2c8 2 17 2 26 2h0z" class="G"></path><path d="M289 138h1c0 2-1 4 0 6 1-1 2-1 3-2l1 1c-1 0-2 1-3 2l2 4h-1c-1-1-1-2-2-2h0c0 1-1 3-2 4h0c1-4 0-9 1-13zm112 650c0-1 1-2 1-3h5 1c-2 1-1 2-2 4v1h-3l-2-2z" class="E"></path><path d="M430 929h23c1 0 5-1 6 0 1 0 1 0 1 1-2 1-4 0-6 0h-14c-3 0-7-1-10 0v-1z" class="D"></path><path d="M734 114h-20c-2 0-6 1-9 0 1 0 1-1 2-1 0-1 1-2 1-2 0 1 0 1 1 2h25v1z" class="Q"></path><path d="M437 815h1v1c-1 0-2 1-3 1l-8 2h-1c-1 0-2 1-4 1h-1c-1 0-2 0-3 1h-5-1-1l4-1h4l5-1c5-1 9-3 13-4z" class="E"></path><path d="M174 614c12-1 24 0 36-1h1c-1 1-5 1-6 1-5 0-10 1-15 1v-1h-16z" class="C"></path><path d="M837 342c1-1 3-1 4-1 9 0 15 1 23 4v1c-5-2-10-3-15-4h-9c-2 0-4 0-6 1-4 0-7 0-10 1-2 1-2 1-3 1l6-2h2c2-1 5-1 8-1z" class="D"></path><path d="M325 278l-1 2v-1-1h-2c-1 0-11 0-12 1-1 0-1 1-1 2l-1-1v-2l-1-1 1-1h1c3 1 4 2 7 1h5c1-1 3-1 4-1v2z" class="E"></path><path d="M319 168c9-1 18 0 26 2v1l-6-2h-2c-2-1-5-1-7-1h-11v1c-3 0-6 2-9 3-2 0-3 1-4 1h0l13-5zm369 142c2 3 1 14 1 18-3-5-2-13-1-18z" class="C"></path><path d="M288 74h0c-1 1-2 1-2 1l-2-1v-1l-1-1-1-1h1c1-1 2-3 4-3 1 1 1 1 1 2v4z" class="E"></path><path d="M715 418l1-1 1 3c1 0 2 0 2-1 1 3 2 5 3 7-1 1-2 1-3 2l-4-10z" class="i"></path><path d="M244 269c2-1 3 0 4 0h1c7 2 15 1 23 2h4 1l4 1c2 1 3 1 5 2-2 0-3-1-4-1-2-1-4-1-6-1-4-1-8-1-13-1-4 0-10 1-14-1h-1c-1 0-2-1-4-1z" class="Q"></path><path d="M104 744h2c1 1 1 2 2 4-1 1-1 1-3 2h0c-2 0-3-1-3-2 0-2 1-3 2-4z" class="K"></path><path d="M678 220c1-2 2-4 4-5h3 1c-1 1 0 1-2 1h-2v1c0 1 0 1-1 2v7-1c-2-1-3-3-3-5z" class="S"></path><path d="M626 174v-3c1 0 1-1 1-2h0-1c2-4 5-8 9-10l1 2c-5 3-8 8-10 13z" class="C"></path><path d="M835 388c1 1 2 2 2 4l-2 2c-2 0-2 0-4-2 0-2 0-2 2-4h2z" class="Z"></path><path d="M208 505h0c1 0 1 0 2-1l1 1h2 0v3c1 2 3 3 4 5l-4-4c-2 1-5-2-6-2h-1v-1h1l1-1z" class="D"></path><path d="M635 179l1-1c1 1 2 1 3 2 1 0 1 1 1 2l-1 1c-1-1-2-1-3-1h-1c0 1-1 1-1 1-1 1-1 0-2 1h0l-1-1c1-1 2-3 4-4z" class="Q"></path><path d="M850 368c1 0 3 1 4 2l1 2c1 1 2 3 4 4h0c1 2 1 3 2 4v1l-1-1h-1l1 1v3c-3-7-5-11-10-16z" class="c"></path><path d="M121 250h3c1 1 2 1 2 3 0 1-1 2-2 3h-1c-2-1-2-1-3-2 0-2 0-2 1-4z" class="i"></path><path d="M244 138c-1 2-2 3-3 4-2-1-3-2-4-4 1-1 2-2 4-3 1 0 1 1 3 2v1z" class="H"></path><path d="M272 367l1-3v1c0 2 0 4 1 6h1l2 1v1h1 1v1c-2 0-2 0-3 2v5-5c-2-2-4 0-6-2l-1-1c2-1 2-1 3-2 0-1 1-2 0-4z" class="X"></path><path d="M820 326c1-1 1-3 1-4 2 0 1 2 3 3v-2h1v2c-1 1-1 1-2 3l-1 2 1 1-1 1h0l-1 1c-1-1-1-2-1-4v-2-1z" class="k"></path><path d="M920 637c1-1 2-1 2-2 1 0 2 1 2 2v1c0 1 0 1-1 2-1 0-2 0-2-1h-1l-6 1h-1s0 1-1 1-3 0-4-1h0 2c1-1 2-1 3-1s2 0 2-1h4 1v-1z" class="B"></path><path d="M875 568c3 6 3 10 4 16v4l4 4c-2-1-3-2-5-2v-1-2c-1-2-1-6-1-9 0-1 0-3-1-4v-1c0-2 0-3-1-5z" class="R"></path><path d="M223 313h7c6 2 12 2 18 5h0l-5-1h-1l-5-1c-1-1-2-1-3-1h-2c-2-1-3-1-4-1l-1 1c-3 0-8-1-11-1v-1h7z" class="Q"></path><path d="M389 776c0-1-1-2-2-2-1-1-3-1-4-1-2 0-3 2-4 3h0-1 0c0-2 1-4 2-5s3-1 4-1c3 0 4 1 6 3-1 1-1 2-1 3z" class="c"></path><path d="M605 777h1v24c0 2 1 6 0 7-1-5-1-10-1-15v-16z" class="C"></path><path d="M230 279h7v-1c-1-2-1-3 0-5v-1c0-1 1-1 1-2 1 1 1 1 2 1 0 2 0 7-1 8v1 1c-1-1-1-1-2-1h-1-1c-2 0-3-1-5-1z" class="E"></path><path d="M218 483l1 1c1 1 2 1 3 2 2 2 3 3 4 5-1 0-1 1-2 1-2 0-4-4-6-4-1-1-4 0-6 0h-1-2c3-2 6 0 9-1v-1l1 1h1 2l-2-1c-1-1-2-1-3-2l1-1z" class="B"></path><path d="M908 681v-39c1 0 0 3 1 4h2l-2 2c-1 4 0 8 0 13v20c3 0 8-1 12 0h-13z" class="L"></path><path d="M107 914h1v1c1 2 1 5 0 7l-4-2-5-2 7-3 1-1z" class="N"></path><path d="M244 137h21 18c2 0 5 0 7-1v2h-1c-2-1-4-1-6-1l-20 1c-5 0-12-1-17 0-1 1-1 1-2 0v-1zm90 141v2h1c1 0 2-1 3-1s1 1 2 1c2 0 3-1 5 0v1h-5l-1 1c1 0 2 1 3 1l-1 1-3-1h-1c0 1 0 2-1 2h0c0-1-1-1-2-2 0-1-1-1-1-2h0-1c0-1 1-2 2-3z" class="B"></path><path d="M145 459l-36-1 24-1h1 2c2 0 5 0 6 1h3v1z" class="G"></path><path d="M727 954c1 1 2 3 3 4 1 0 0 0 1 1-1 0-1 0-3-1-1 0-1 0-2 2l1 1h1l1 1-2 2-6-4 6-6z" class="O"></path><path d="M923 746l4 5c0 1 0 0-1 1s-2 1-2 3h-1c0-1 0-1-1-1l-3-2v-1c1-2 2-4 4-5z" class="V"></path><path d="M375 962h2c0 1 0 1 1 1 1 2 1 2 1 5-1 1-2 1-3 2-1 0-2-1-3-1 0-1-1-2-1-3 1-2 2-3 3-4z" class="D"></path><path d="M747 131c2 0 10 0 11 1l-65 1c-4 0-8-1-11 0h-1 0l35-1c7-1 15 0 22 0 3 0 7 0 9-1z" class="B"></path><path d="M707 354c1 2 0 4 1 6v1-1l1-1c1-1 1-2 1-3l1-1h0v1l-1 3c0 4 1 8 0 12l-1-3v3l-1 1c0-6-2-12-1-18zM183 677h0l1 1v43 1c0 3-1 5-2 7 0-1 1-3 1-5v-8-27-12z" class="D"></path><path d="M814 453h-3-1c-1-1-2-1-3-3 0-1 0-3 1-4 1-2 2-2 4-2s3 1 4 3h-1c-1 1-1 1-3 1-1 1-2 2-3 2l2 2s2 1 3 1z" class="G"></path><path d="M734 223h1v3h0c1 0 0 0 1 1l-1 1h-2c-1 1-3 1-4 2h-1 0l-1-1-1-1h0c1 0 1-1 2-2h-2c1-1 1-1 2-1h1l2-1h1c0-1 1-1 2-1z" class="E"></path><path d="M840 362h1c1 0 2 1 3 2 2 0 3 1 4 1h0c-1-1-1-2-2-4 2 1 4 2 5 4 1 0 2 2 3 2 2 3 4 6 5 9h0c-2-1-3-3-4-4l-1-2c-1-1-3-2-4-2l-10-6z" class="g"></path><path d="M792 301h40 7c2 1 4 0 6 1 1 0 3 0 4 1 0 0-1 1-1 0-9-1-18-1-27-2h-29 0z" class="S"></path><path d="M181 484h1l1 1v3c0 4-1 8 0 11 0 3-1 7 0 10 1 1 1 0 1 1v3l1 1c0 2 0 2-1 3h-2c-1 0-1-1-1-1 0-1 1-1 1-2h1v-3c-1-1-1-2-1-3v-12c0-1 0-3-1-4v-1-1c1-2 0-5 0-6z" class="Q"></path><path d="M851 913c6-1 14-1 20 0l-3 3h0c-2 0-4 1-6-1h5c-2-2-27 0-32-1h11c2 0 3 0 5-1z" class="J"></path><path d="M834 248h1v1c1 1 1 1 1 2v2h3c4-1 7 0 11 0h12c2 0 5 0 7 1h-1l-14-1h-7-5c-2 1-4 1-5 1-1 1-1 8-1 10h1v1c-3 0-4 0-6-1 1-1 3 0 4-1-1-2 0-9 0-11 0-1-1-3-1-4z" class="Q"></path><path d="M430 930c-4 0-9-1-13-1-7 0-15 1-22 0l-12-1c-2 0-7 1-8 0v-1l20 1c8 1 17 0 26 0 3 0 6 0 9 1v1z" class="B"></path><path d="M410 905c2 0 2 0 4 1 1 1 1 2 1 4-1 1-2 1-3 2-2 0-3 0-4-1s-1-2-1-3c1-2 1-2 3-3z" class="Z"></path><path d="M691 301c1-2 2-5 4-6s5-2 6-4l1 1-1 2c1 1 5 3 7 4-3 0-9-2-12 0-1 1-2 3-3 4l-2-1z" class="L"></path><path d="M800 379h1l1-2c0 2-1 2-1 3v3 2c-1 5 0 9 1 14v2h0c0 2 1 2 1 4l1 1h-1l-2-3h0v-1c-3-6-3-17-1-23z" class="i"></path><path d="M938 463h1c2 1 5 0 7 1v1c-3 2-8 1-11 1h-1c-2 0-4 0-5-1h-5c2 0 3-1 4-1 2-2 7-1 10-1z" class="J"></path><path d="M928 464c2-2 7-1 10-1v1h0-6v1h9l-7 1c-2 0-4 0-5-1h-5c2 0 3-1 4-1z" class="C"></path><path d="M315 252v-3h0l22 1c2 0 3 1 5 2h-27z" class="D"></path><path d="M836 634c-1 1-2 1-3 2h0l-2 2v1h-1c0-2-1-3-1-4 2-2 3-3 5-4h1c1-1 2-1 3-1h7l1 1-1 1h-2c-1-1-3 1-5 2h-2zm22-92c3 0 6 1 10 0 6-1 12-3 17-6l1 1c-1 0-3 1-4 2h1l8-3v1c-5 2-10 4-15 5-6 1-12 2-17 2l-1-2zm-97-192c1 0 2-1 3-2h17 9 5c3 1 5 1 8 2-3 0-8-1-11-1h-19-7l-2 2c-1 1-2 1-3 1v-2z" class="C"></path><path d="M816 447c0 1 1 2 2 2 0-5-1-11 0-16h1c1 0 1 0 2 1h-2v2c1 3 0 8 0 12h0c-1 2-2 3-2 4h-1 0c-1 1-1 1-2 1h0c-1 0-3-1-3-1l-2-2c1 0 2-1 3-2 2 0 2 0 3-1h1z" class="Q"></path><path d="M818 558c9-2 19-2 29 0 6 2 13 5 17 11 0 2 1 3 2 5h-1c-1-2-1-3-2-5-4-5-10-9-16-10l-3-1c-4-1-8-1-12-1-4 1-8 1-11 2-1 0-2-1-3-1z" class="H"></path><path d="M348 294c0 11-3 20-6 30l-1 1c0-2 0-4 1-5l1-3c1-5 2-13 2-18h-2c1-1 2-1 2-2 1-1 2-2 2-3h1z" class="a"></path><path d="M871 913l4-4c0 1 2 5 2 7s-1 5-2 7c-2-3-4-6-7-7l3-3z" class="N"></path><path d="M206 582l-13-1c-1 0-1 0-2 1-4-2-9 0-13-2 1-1 9 1 11 0 0 0 1-3 2-4 2 1 2 2 3 4h6c2 0 3-2 4-3h1l1 2 1 1c-1 0-1 1-1 2z" class="T"></path><path d="M337 277h6c2-1 4-1 7-1l-1 1v1c1 0 1 0 2-1v1l1 2-2 1c1 1 2 2 2 3h0-1l-3-3h-1v-1c1-1 1-1 2-1-1 0-2-1-3-1 0 1 0 1 1 2l-1 1h-1v-1c-2-1-3 0-5 0-1 0-1-1-2-1s-2 1-3 1h-1v-2l3-1z" class="E"></path><path d="M197 565h20c2 0 6 1 8 0h0c1 0 2 0 2 1h-1c-5 1-9 0-14 0h-22c-9 0-19 0-28 1-1 1-1 1-2 0h0s1 0 2-1h5c4-1 8 0 12-1h18z" class="C"></path><path d="M627 185h1c2 3 5 7 8 8 2 0 3 0 4-1s2-2 2-4c1 0 1-2 0-2 0-1-1-2-1-2l1-1c1 2 2 3 2 5-1 2-3 5-4 6-2 1-4 1-6 1h-2 0l1 1-1 1-2-1 1-1-3-3v-2h-1v-5z" class="R"></path><path d="M390 773c1 2 1 3 1 6-2 2-3 3-6 4h0c-3 1-6 0-8-2-3-1-5-5-5-8 0-1 0-2 1-4h0c1-4 3-6 6-7 1-1 5-1 7-1l2 2h0 0c-3-1-6-2-9-1s-5 4-5 7c-1 2-1 5 0 7 1 3 3 5 6 6h6c1-1 2-3 3-5v-1c0-1 0-2 1-3z" class="H"></path><path d="M678 220c0 2 1 4 3 5v1c2 0 4 1 5 1 5 1 8-2 11-5 2 0 3 0 4-1-1 2-2 4-3 5-3 2-6 3-9 3s-6 0-9-2c-2-2-2-4-2-7z" class="V"></path><path d="M869 608v73c-2-4-1-11-1-15v-46c1 0 0-2 0-3 0-3 1-6 1-9z" class="X"></path><path d="M358 149h1v11l-6-2h0c-1-1-3-1-4-2 3-3 7-4 9-7h0z" class="U"></path><path d="M145 458h38c2 0 8 1 10 0h4l-52 1v-1z" class="C"></path><path d="M799 286c-2-1-2-1-3-2 0-2 0-2 1-3h0v1h1c0-3 1-6 2-9 1-1 2-1 3-1 0 3 1 6 2 8h1c0 2 0 2-1 3 0-1-1-2-1-3l-1-1h0l-1 1-1 1v3h-2v2z" class="U"></path><path d="M653 226h0c-1-5 3-14 6-18 4-4 8-7 12-11 1-2 3-5 4-5h1l-5 5c-8 9-15 18-17 30l-1-1z" class="Z"></path><path d="M325 276l1-1c2-3 4-5 6-7h1l2-2h2l-1 2c-1 2-3 3-3 5l-1 4v2l-1-1v-1h0c-2 2-2 5-2 8-1-2 1-5-1-7h-3v-2z" class="B"></path><path d="M160 441h0c-6 0-13 0-18-1-1 0-2 0-2-1v-1h1c1-1 2-1 3-1 2 1 5 0 7 1 1 0 5 0 6 1l4 2h1c1 0 3-1 5 0 1 0 1 0 2 1-1 1-3 0-5 0h0l-4-1z" class="Q"></path><path d="M375 921h-56-16-8c-1 0-2-1-3-1l1-1c4 2 10 1 14 1h39 29v1z" class="G"></path><path d="M160 441c-4 1-19 0-21 1v3h-1v-2c-1-2-3-1-4-3h3l2-2c-1-2-3-1-4-3 2-1 2-1 4-3v1c1 0 1 1 2 2 4 2 9 0 13 2h-1-9c-1 0-2 0-3 1h-1v1c0 1 1 1 2 1 5 1 12 1 18 1h0z" class="U"></path><path d="M248 285l-10-3c-2 0-3-1-4 0h-1v-1l-14-1-2-2h1c4-1 8 0 12 1 2 0 3 1 5 1h1 1c1 0 1 0 2 1l10 2-1 2z" class="P"></path><path d="M329 285c0-3 0-6 2-8h0v1l1 1v-2h5l-3 1c-1 1-2 2-2 3-2 3-1 6 0 9 1 2 5 5 7 5 2 1 4 1 6 0 1-1 1-4 1-6 0-1-1-2-1-2h0c2 2 3 4 3 7h-1c0 1-1 2-2 3 0 1-1 1-2 2l-4-2c-4-2-8-5-9-8l-1-4z" class="l"></path><path d="M337 266h1c5 0 7-1 11 3l3 6c0-3 0-9 1-12v-5h1l1 1-1 1c-1 3 0 10 0 14l3 3c0 1 0 1-1 2-1 0-3 0-4 1h0l-1-2v-1c-1 1-1 1-2 1v-1l1-1s0-1 1-1c-1-2-1-3-2-4-2-2-4-4-6-4-3-1-5 0-7 1l1-2z" class="X"></path><path d="M351 278v-1l2-2 1 1v1c0 1-1 2-2 3h0l-1-2z" class="T"></path><path d="M663 168h1c1 2 1 2 1 4-5 5-8 5-15 5l-2 3c-4 6 0 17 2 24 1 1 1 2 1 4 0 1 0 1-1 2v-1c0-1 0-2-1-3v-4l-2-5c0-2-1-5-1-8-1-1-1-3-1-5 1-2 4-7 6-8 1 0 2 0 3-1 3 0 5-1 7-2 1-2 2-3 2-5z" class="D"></path><path d="M401 788c-1 3-3 6-5 9h-1c3-3 4-6 4-10 0-3-3-1-5-2l1-1c1-1 2-1 3-1 1-1 1-9 1-10 3 3 2 7 6 10h0l2 2h-5c0 1-1 2-1 3z" class="I"></path><path d="M231 574v1c0 2-3 3-5 4l4-1c3 0 3-2 5-3 0 2-1 3-3 4-5 2-10 2-15 2-4 1-8 1-11 1 0-1 0-2 1-2l-1-1 15-1c5 0 7-1 10-4z" class="R"></path><path d="M636 161h1c-6 5-9 9-11 16v2-1c2-3 4-5 6-7v1 1c-3 2-5 5-5 9v3 5h1v2l3 3-1 1c-2-1-4-4-5-6-2-5-1-10 1-16 2-5 5-10 10-13z" class="k"></path><path d="M661 217l1 1 2 5 4-4 1-1v3c-2 3-4 3-5 5-2 2-1 34-1 38h0l1-2v1l-1 2c-1 1-1 1-1 2-1-4 0-8 0-11v-20-9c0-2-1-2-2-3 0-3-1-5 1-7z" class="V"></path><path d="M731 360c1 1 1 6 1 7v43c0 8 0 17 1 25 0 1 1 1 1 2-1 0-1 0-2-1l-1-76z" class="D"></path><path d="M127 451c5 0 10 1 15 1 5 1 11 0 17 1h8c8 1 15 0 23 0 4 0 8 0 12-1h3 1 2c1-1 2 0 3 0h-2c-1 1-1 1-2 1-7 1-14 1-22 1-7 0-14 1-20 0l-11-1h-17c-3 0-7-1-10-2zm666-229c2 1 1 18 2 21v3 1c0 1 0 0-1 1h0c-1 3 1 8-1 10h-1c0 2 0 4-1 6-1 1-1 1-2 0h0v-2c-1 0-1 0-1 1l-1-1v-1c0-1-1-1-1-2h-1c1-1 1-1 2-1 0-1 0-1 1-2 1 1 3 0 4 0 2-1 1-9 1-12h0c1-3 0-18 0-22z" class="B"></path><path d="M772 418l-3-1c2-1 5 0 7 0 6-1 12-1 18 1h1c4 1 10 6 11 10 1 3 0 7-1 9l-3 3 1-1v-1c1-1 1-3 1-4 1-3-1-7-3-9-3-4-6-4-11-5h3v-1c-2 0-3-1-5-1-4-1-7 0-10-1-2 0-4 0-6 1z" class="h"></path><path d="M573 809v-3c0-2-1-2-2-3 0-1 1-2 2-2h1c1-2 0-4 0-6v-12c-2 0-4 0-7-1h-2 3 6v-10-2l2-2v14l12-1-1-12 1-1v14c-3 0-10-1-12 0l-1 20 1 1c0 1-2 2-2 3s0 2-1 3z" class="D"></path><path d="M767 353h23c3 0 7 0 9 1h1c-1 1-1 1-2 1h-11-9c2 1 4 1 6 1h0c-3 0-10-1-13 0-1 1-2 0-3 0l-2-1-2-1 3-1z" class="E"></path><path d="M764 354l3-1 1 3-2-1-2-1z" class="B"></path><path d="M715 418c-3-6-5-12-7-18l-6-14-3-9c1 1 2 3 3 5l3 7 8 17c2 5 5 9 6 13 0 1-1 1-2 1l-1-3-1 1z" class="Z"></path><path d="M211 649h3c1-1 1-1 1-2s1-1 1-2v-1-1l1-3v-2-1c1-3 0-5 1-7v-6c0-4 1-9 1-13v-1h0l1 1s1 1 1 2 0 1-1 2 0 4 0 6c0 3 0 6-1 9-1 2 0 4-1 6v4c-1 2 0 3-1 5v2c0 1 0 1-1 2v2c-1 5-5 9-7 13 0 1 0 2-1 3h0l2-6c1-4 3-7 4-10-1-1-2-2-3-2z" class="Q"></path><path d="M241 251h2 3l6 1c1 0 3 1 4 1 1 1 2 0 3 1h1 2l3 1h3c1 1 1 1 2 1 1 1 2 0 3 1-1 0-2 1-3 0h-3 0c-2-1-7-1-9-1-1-1-3 0-5 0l-7-1c-1-1-3 0-5-1v-3z" class="E"></path><path d="M908 696h1v5 103c-2-4-1-24-1-29v-74-5z" class="R"></path><path d="M635 207l3 11c1 2 2 5 3 7 2 3 3 7 4 11 2 6 5 12 8 18h0v1 5l-14-36c-1-5-4-11-4-17z" class="N"></path><path d="M774 364c5 2 9 6 15 6 2-4 4-7 7-10h0c-2 3-3 6-5 9 0 1-1 3-2 5-4-1-7-1-10-2v-1c-3-1-6-2-7-5l1-1h0l1-1z" class="B"></path><path d="M691 301l2 1c-3 8-4 17-3 25 1 5 2 9 2 14-2-2-4-4-5-6v1c0 1 0 1-1 2 0-2-1-3-1-4-1-1-1-2-1-2-1-1-1-1-1-2l-1-1v-2c-1-1-2-2-2-4h1c1 1 3 6 4 8 0 0 1 1 1 2h1l3 5h1c0-2 0-3-1-4l-1-6c0-4 1-15-1-18 0-3 2-6 3-9z" class="Y"></path><path d="M286 454v-8c0-7-1-14 0-21l2 1 1 1v1c1 2 0 6 0 9v16h0c-1-1-1-1-1-2v-1l-1 1v3l-1 1c0 1-1 1-2 1 1-1 2-1 2-2z" class="B"></path><path d="M219 534l-14-1h-29c-6 0-13 0-19 1-2 0-4-1-5 0h-2-1c-2 1-5 2-6 1h0l1-1c1-1 4 0 6-1 2 0 3 0 5-1h8c4-1 10 0 15 0h25c3 0 7 0 10 1h7l-1 1z" class="C"></path><path d="M810 349l2-1v1l-14 9h0l2-2v-1h-2c1 0 1 0 2-1h-1c-2-1-6-1-9-1h-23l-3 1 2 1h-3v1l-1 1c-1-1-1-2-2-3v-1h0 2 2c1-1 7-1 9-1h22 8c1-1 1-1 2-1 2 0 3-1 5-2z" class="S"></path><path d="M760 353h0c1 1 2 1 3 1h1l2 1h-3v1l-1 1c-1-1-1-2-2-3v-1zm-544-44l1-1s1-1 1-2h0l2 2v-2c3-1 6-1 9-1 14-1 29-2 43 0h1c1 1 2 1 2 1l12 4c1 0 2 1 3 2h0c-1 0-4-1-5-2-4-1-8-3-11-3h0c1 6 0 12 1 17v2h-1c-1-1 0-2 0-3-1-4-1-8-1-12v-5c-9-1-16-1-25-1l-27 1c0 2 0 2-1 3h-3-1z" class="C"></path><path d="M744 378v-17c0-1-1-4 0-6 0-1 2-2 2-3l-1-1h-10v-1c3 0 8 0 11 1h1 2l1 1 1-1c1 1 6 1 8 1l1-1 1-1v2l1 1h-2 0-2c-2 1-5 1-8 0h-2c-1 0-1-1-1-1-2 3-1 5-1 8v17-1c0-1 0-1 1-2-1-1-1-1 0-2v-3-1l1-2c0 2 0 3-1 5v4l-1 4c-1 0-2 0-2-1z" class="B"></path><path d="M145 482l15-1h34c6 0 15-1 21 1h0c1 0 2 1 3 1l-1 1c-2-2-2-1-4-1-3-1-7-1-11-1h-3-7-14-8c-1 0-1 0-1 1-2-1-4-1-5-1-4 1-11-1-14 1v3l-2 1v3h2l-1 1h-2c-1 0-1 0-2-1h0v-1h1 1l-1-2c1 0 0 0 1-1h1 0-1l-2-1h1v-2h0l-1-1z" class="S"></path><path d="M405 783c2-1 2-27 2-31h0c2 5 0 11 0 16v16h11v-2l1-1c0 1-1 2 1 3 3 1 6 0 10 0 2 0 4-1 6 1h0 0l-2 1c-1-1-4-1-5-1h-10v15 5s1 0 1 1h0-4l2-2h0v-7-12h-10-1l-2-2z" class="B"></path><path d="M265 359v-1c1-1 2-3 3-4l4-4v-1h1v2h1c1 3-1 7 0 10s0 5 1 8v2h-1c-1-2-1-4-1-6v-1l-1 3v-5h-1c-2 0-4-1-6-2v-1z" class="Q"></path><path d="M756 328l1-1c2 1 4 0 6 0 6 0 13-1 19 0h1c1 0 3 0 4 1v1c-2 0-5 0-6 1-6 1-22-1-25 1v-3z" class="E"></path><path d="M715 233l2 1h0l1-1c1 0 1 0 2 1l-3 3-1 3v2h-1v5c1 1 2 3 2 4v1c1 1 3 2 4 3l2 1v1l-1 1c-1 1-1 1-2 0h-1c-3-2-5-5-6-8v-1l-1-1c0-2 0-6 1-8 0-3 1-4 2-7z" class="J"></path><path d="M868 739c1-2 1-24 0-27h0l1-1v53 15c0 3 0 7 1 10 0 2 1 4 2 6-4-5-6-11-9-17l3-1 2 8v-41-4-1z" class="X"></path><path d="M172 291c9-6 18-10 29-12 4 0 10 0 14-3h0 1c1 1 1 1 1 3 0 1 0 1-1 2h-3v-1c-8 1-16 2-23 4-6 2-12 5-18 8v-1z" class="L"></path><path d="M118 647h1 1v3l-1 13v74 19 11c1 1 1 1 0 2h-1v-5c1-2 0-6 0-9v-27-79-2z" class="D"></path><path d="M264 485s0-1 1-2l4-7c0-1 1-1 1-2h1c1-3 3-4 5-6h1 0c-1 2-3 3-3 5 1 0 2 0 3-1h2c-1 1-1 1-2 1l-2 1h0c-1 1-3 2-3 3v2l-2 3 1 1c1 0 2 0 3-1l1 1c-1 1-3 1-5 1 0-1-1 0-1 0-3 1-3 4-4 6 0 1 0 3-1 4 0 2-2 4-3 5h0 0c-2-4 2-10 3-13v-1z" class="C"></path><path d="M927 688c1 1 2 1 3 2h2 0l-3 1v1c0 2-2 5-4 7-5 1-9 1-13 2h-3v-5c3-1 8 0 12-1h1c3-1 4-4 5-7z" class="Q"></path><path d="M219 484c7 2 11 8 15 15 2 4 3 7 4 12l1 4-2 1c1 2 0 7 1 9h-1c0-5-1-10-2-15-1-7-5-13-9-19-1-2-2-3-4-5-1-1-2-1-3-2z" class="L"></path><path d="M238 511l1 4-2 1h0c0-3 0-3 1-5z" class="d"></path><path d="M722 426h0c0 3 1 4 2 7l2 5 1-1v-1-1c-1-1-1-2-1-2v-3c3 10 4 20 6 31l-13-33c1-1 2-1 3-2z" class="K"></path><path d="M903 253c1 2 1 8 1 11 2 0 5 0 7 2 0 0 0 1-1 2h-2c-1 1-2 1-2 1-1 2-1 3 0 5l-1 1v-2h-1-1 0-1c1 1 1 2 1 3 1 3 0 8 0 12 0-5 0-11-3-16v-2h-1c2-1 3-1 5-2h0-1-7c-1-1-1-1-1-3 2-1 6-1 8-1v-10-1z" class="X"></path><path d="M375 920v-13c1 4 1 9 1 13h81v-5l1 5c3 0 4-1 6-1h1v1c-1 0-1 0-3 1-1 0-2 0-3 1 0 0 0 1 1 1 0 2 0 3 1 5-2-2-2-4-3-7h-82v5h-1v-5-1z" class="Y"></path><path d="M705 389l2 2c1 0 1 1 2 1 1 1 1 2 2 3v1c1 2 2 3 3 5v1c1 0 1 1 2 1h-1v-2c0-1 0 0-1-1v-2c0-1 0-1-1-1v-1-1h0v-3c3 9 6 19 8 28 0 2 1 4 1 6h0 0c-1-2-2-4-3-7-1-4-4-8-6-13l-8-17z" class="R"></path><path d="M761 352c1 0 2 0 3-1l2-2h7 19c3 0 8 1 11 1l7-1c-2 1-3 2-5 2-1 0-1 0-2 1h-8-22c-2 0-8 0-9 1h-2l-1-1z" class="E"></path><path d="M813 414c4 0 8 3 12 5 3 1 7 1 11 1s9 0 13 1l7-1 5-1h0c-1 1 0 1-1 1-4 0-9 1-12 3 1 0 2 0 4 1h-2c-13 1-27-1-37-10z" class="D"></path><path d="M836 420c4 0 9 0 13 1l-3 1h-2c-3 0-6 0-8-2z" class="B"></path><path d="M354 772c1-1 1-2 1-3 1-1 1-2 1-4l1 2c0 1 0 1 1 2 1 0 2 0 4 1v2c0 1 0 1 1 2v2 1h0l-1-1c0-1-1-2-1-3-1 1-2 2-2 3-1 4-2 9-3 13 0 2 0 3 2 4h-1c-1 0-1 1-1 1h-1c-1-2-1-4-1-6-1-5-1-11 0-16z" class="R"></path><path d="M356 789l-1-7c1-2 2-4 4-6-1 4-2 9-3 13z" class="D"></path><path d="M671 197c0 1 1 1 0 2l-3 3v1-1l3-2c2-1 3-1 4-1-2 1-5 3-7 5-3 3-5 9-7 13-2 2-1 4-1 7l-1-1v-1-3c0 2 0 3-2 5l-1-1c-1 5-1 11-1 16 0 2 0 6-1 8-1-1 0-3 0-5-1-5-1-10 0-15 2-12 9-21 17-30z" class="B"></path><path d="M836 614h1 2v-2l2 2h-2c1 1 2 1 4 1h7l1 1h-1c-1 2-2 2-5 2 0 2 0 1 1 2 1 0 2 0 3 1l1 1h2c2 1 3 2 4 3 0 0 1 0 1 1 2 0 2 1 3 3h0c1 1 1 2 1 3-4-5-9-8-15-10-3 0-5 0-8-1v-1l2-1c-1-1-1 0-1-1-1 0-1-2-1-2h-9c-2-1-3-1-4-1l1-1h10z" class="X"></path><path d="M838 616h12c-1 2-2 2-5 2 0 2 0 1 1 2v1c-2 0-4 0-5-1 1 0 2 0 3-1h0-3-1c-1-1-1 0-1-1-1 0-1-2-1-2z" class="D"></path><path d="M836 614h1 2v-2l2 2h-2c1 1 2 1 4 1h7l1 1h-1-12-9c-2-1-3-1-4-1l1-1h10z" class="O"></path><path d="M763 356v-1h3l2 1c1 0 2 1 3 0v1h-2c0 2 0 3 1 4s2 2 4 3l-1 1h0l-1 1c1 3 4 4 7 5v1c3 1 6 1 10 2l-1 1c-3-1-6-2-9-2 1 1 0 1 1 1s2 1 4 1c0 0 1 0 2 1 1 0 3 0 4 1h-2-1l-5-1c-7-2-12-7-16-13-2-2-3-4-3-7z" class="D"></path><path d="M817 534v-1h3v7 1c0 2 1 5 0 6l1 1c-2 0-4 1-5 1l-6 2h-1-1l-1 1v-1l-2-5v-1l3 3c1 0 2-1 2-2 1 0 2-1 2-2s0-1 1-2l1-1h0 1l-2-1 1-1h3v-1c0-1-1-1-2-1v-1h3v-1l-1-1z" class="C"></path><path d="M810 551c1-1 0-1 1-1l1-1v-1c0-1 0-1 1-1 1-1 2-1 3-2 1-2 0-2 0-3 1 0 2-1 3-1l-1-2 1-1 1 1v1 1c0 2 1 5 0 6l1 1c-2 0-4 1-5 1l-6 2z" class="G"></path><path d="M816 549v-1c2-2 2-3 2-6l2-1c0 2 1 5 0 6l1 1c-2 0-4 1-5 1z" class="a"></path><path d="M775 500v-1-3c1-1 1-3 2-4h3c1 0 1 1 2 2 2 3 0 9-1 12-2 3-4 6-7 7v-6c1-2 0-5 1-7z" class="j"></path><path d="M480 955l1 1c2 1 3 2 5 3 4 2 9 4 13 4 11 2 22 2 32 1h41 28c3 0 8 1 10 0h1c0-2 0-3-1-4 1-1 2-1 3-2 0 1 1 1 1 2s-1 3 1 4h1v-2h0c2 0 2 1 3 2 0 1-1 2-2 3-2 0-1-1-3-2l-1 5-1 1-1-1v-5h-28-51c-11 0-24 1-35-1-6-2-13-5-17-9z" class="T"></path><path d="M406 652c8-1 17 0 25 0 6 0 12-1 18 0l-1 1h-11-1c0 2 0 4 1 5 1 4 4 10 6 13l1 1 1 1h1 1c-2 1-4 1-5 2-3-3-5-6-6-10-2-3-3-6-4-9-1 0-1-1-1-2-1 0 0 0-1-1h-5l1-1c-6 0-13 1-20 0z" class="F"></path><path d="M843 406c0 1-1 1-1 2-4 2-11 3-15 2h-1l-6-3-1-1c-4-2-6-6-8-10v-1c-1-4-1-9 1-13 1-4 4-8 8-10l-1 1c-1 2-3 3-4 5h0c-1 1-1 2-2 3v1c0 1 0 1-1 2v1c0 3 0 5 1 8l1 1c2 5 4 9 9 11l1 1c1 0 2 1 4 1 1 0 3 1 5 1 3 2 7-1 10-2z" class="a"></path><defs><linearGradient id="A" x1="797.152" y1="574.356" x2="807.759" y2="581.669" xlink:href="#B"><stop offset="0" stop-color="#282928"></stop><stop offset="1" stop-color="#4d4c4d"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M812 560h1c-1 1-2 2-2 3-2 2-4 3-6 5-5 5-7 11-8 17 0 6 0 13 4 17l3 6-1-1c-3-1-3-2-4-4v-1h-2v-1c-3-7-4-17-1-25s9-13 16-16z"></path><path d="M782 327c1 0 5 0 6-1 0-1 0-1 1-2h0l2 2h29v1 2h-6l-2 1h-2c-4-1-10 0-14 0h-6c0 1 0 2-1 3h-1c0-2-1-2-1-3h-6c1-1 4-1 6-1v-1c-1-1-3-1-4-1h-1z" class="U"></path><path d="M799 327c7-1 14 0 21 0v2h-6c-1 0-1 0-2-1l-13-1z" class="B"></path><path d="M812 330c-2-3-18 1-21-2h0l8-1 13 1c1 1 1 1 2 1l-2 1z" class="E"></path><path d="M855 523h16-1c-4 1-8 1-12 1 0 6-1 12 0 18l1 2h-15c-1 1-1 1-1 2 1 1 1 1 3 2h2l5 2c1 0 2 1 3 2h0-1c-4-2-8-3-11-4l-5-1v-22h4c0 1 0 1-1 2h-1c0 1 0 2 1 3h1 3c-2 0-2 0-3 1 0 2 0 8-1 10 0 0-1 0-1 1h2c1 1 2 0 4 0l10 1v-19l-2-1z" class="D"></path><path d="M323 331v1l1 1h0v2l1 3v3 1h1v5h1c1-3 1-4 1-6 1-2 1-5 1-6 1 1 1 1 1 3 1 1 0 2 0 4 1 0 1 1 1 2l-1 5c0 1 0 3-1 4v1 2l-1 5c0 2-1 3-1 4-1 2-2 4-2 7 0 0-1 0-1 1l-1-1v-4c-1-2 0-5 0-7v-1h-1 0l1-1c1 1 1 2 1 4l1-1v-2c1-1 0-3 0-5v-8-4c-1-2-1-4-1-6h0c-1-2-1-2-1-3v-3z" class="B"></path><path d="M708 372l1-1v-3l1 3c0 8 3 16 5 23 3 9 7 19 10 28 0 3 1 6 1 8v3s0 1 1 2v1 1l-1 1-2-5c-1-3-2-4-2-7h0c0-2-1-4-1-6-2-9-5-19-8-28l-6-19 1-1z" class="i"></path><path d="M336 268c2-1 4-2 7-1 2 0 4 2 6 4 1 1 1 2 2 4-1 0-1 1-1 1-3 0-5 0-7 1h-6-5l1-4c0-2 2-3 3-5z" class="n"></path><path d="M249 283c2 1 4 2 5 2 0-3 0-5 1-7l1-1c1 2-1 5 0 7 0 0 2 1 3 1 2 1 4 3 7 4h0c-3 0-7-3-10-4l-1 1c2 3 10 4 13 6 2 0 3 2 5 3v-1l-1-1c1 0 1 0 2 1v1c2 1 3 2 5 3v-4c-1-2 0-5-1-7-1-1-1-2-2-2 1 0 2 1 2 2 3 4-1 9 4 13 2-1 3-2 4-3v1l-3 3 10 7c5 5 11 12 15 18h0l-15-15h0v1h-1s0-1-1-1c-1-4-5-6-8-8-2-1-7-2-8-4 1 0 2 1 4 1-1-2-3-2-4-3h-1c0 1 1 3 0 3h-1v-1-2-1c-5-4-11-5-17-8-3-1-5-2-8-3l1-2z" class="Z"></path><path d="M679 261c2-1 4-1 6 0h1c3 1 4 2 6 5v1c-2-3-4-5-8-5-3 0-6 0-8 2s-4 5-4 8c0 2 1 4 3 6 1 2 4 3 6 2 1 0 3-2 4-3 0-1 1-2 0-3 0-1-1-2-2-3h-1-1c-1 0-1 1-2 3h-1v-2c0-1 0-2 1-3s3-1 4-1c2 0 4 1 5 3 1 1 1 3 0 5 0 3-2 5-4 6s-4 1-6 1c-3-1-5-3-7-6-1-3-1-6 0-8 1-5 4-7 8-8z" class="i"></path><path d="M482 802h0l3-2c1 0 1 1 2 1v1c-1 3-1 7-1 9v18 7c-1 1-1 1-2 1 1 0 1 0 2 1l-2 2c-2 1-3 2-4 5h0l-7 8-1 1v1l-1-1v-1c1 0 1-1 0-2 3-4 8-7 10-12 3-5 2-11 2-17v-19l-1-1z" class="D"></path><path d="M190 662c1 0 1 0 2 1 2 0 3 1 4 2 0 2 0 3-1 4v2c0 1 0 1-1 2v1c-1 1-1 3-2 3l-3 1h-3c-1-1-2-2-2-3l-1-2v-4c0-2 0-2 1-3 1-2 3-3 6-4z" class="E"></path><defs><linearGradient id="C" x1="864.965" y1="402.619" x2="850.617" y2="370.938" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#393939"></stop></linearGradient></defs><path fill="url(#C)" d="M856 366c6 5 9 13 9 20 0 8-1 15-6 21-2 2-5 4-7 5 6-7 9-14 8-23v-5-3l-1-1h1l1 1v-1c-1-1-1-2-2-4-1-3-3-6-5-9 1 0 1 1 3 1l-1-1v-1z"></path><path d="M212 497l-2-1h0c1-1 2-1 3-1h3c1 0 1 0 2 1h1c2 1 5 3 7 5v1l1 2c1 1 1 1 1 2v1c1 1 2 3 2 5h0 0-1c0-1-1-3-2-4l-1 1h-3c-1 0-2-1-2-2l-2-2h0-6 0-2l-1-1c-1 1-1 1-2 1h0c0-2 1-4 2-6l2-2z" class="C"></path><path d="M212 497l1 1v1h2c0 1 0 1-1 2h1c2 0 3 0 4 1v1l-1-1h-5l-1 1v1h6c1 0 1 1 1 1h-6 0-2l-1-1c-1 1-1 1-2 1h0c0-2 1-4 2-6l2-2z" class="Y"></path><path d="M871 700c10 0 20 0 30-1h0v1 86 19c0 2 1 5 0 7-2-3-1-16-1-19v-92h-21-8v-1z" class="D"></path><path d="M820 372c1-1 1-1 2-1h0v1c-1 1-2 1-2 3l-1 1c-2 2-2 4-3 6v1c-1 5 0 12 4 17 2 2 5 5 8 5 5 0 10 0 15-2 2-2 4-3 6-4v-1l1 1c-1 1-1 1-2 3-1 1-3 4-5 4-3 1-7 4-10 2-2 0-4-1-5-1-2 0-3-1-4-1l-1-1c-5-2-7-6-9-11l-1-1c-1-3-1-5-1-8v-1c1-1 1-1 1-2v-1c1-1 1-2 2-3h0c1-2 3-3 4-5l1-1z" class="C"></path><path d="M866 574c0 1 1 2 1 4s-1 5-1 8c0 2 1 7 0 9-1 1-1 2-2 2l1 1c0 2-1 2-1 4h-1c0 2-1 4-2 5h-1l-1 1c1 0 1 0 3-1 1 0 0 0 1-1l2-2 1 1h0c0 1 0 1 1 1h0c0-1 0-3 1-4v-17c0-4 0-8 1-11v-1h0c1 1 1 3 0 4 0 4 0 10 1 13v12c0 2 0 5-1 6 0 3-1 6-1 9 0 1 1 3 0 3h0v-12c-1 0-2 0-3 1h-3c-1 1-1 1-2 1l-2 1c-2-1-2-1-4-1-1 0-3-1-4-1 3-1 5-1 8-3 2-1 3-3 4-5 3-7 3-18 0-25 1 0 1 1 2 2 0 1 0 0 1 1v-2h-1c1-1 1-2 1-3h1z" class="S"></path><path d="M888 637h12 0V531l3-3 1 1-2 2c-2 1-1 95-1 106h7v-45h1v45h11v1h-1-4-27v-1z" class="a"></path><path d="M654 227c-1 5-1 10 0 15 0 2-1 4 0 5 1-2 1-6 1-8 0-5 0-11 1-16l1 1c-2 6-1 28 0 35 0 5 2 11 4 15l1-7c0-1 0-1 1-2-2 11 1 20 5 30-4-6-6-12-9-18l-6-17v-5-1h0c0-4-1-8-1-11 0-6 0-11 1-17l1 1z" class="e"></path><path d="M772 441l9 39c-3 1-5 1-8 2h0v-4c-1-2-1-2-1-3v-3c-1-1-1-1-1-2v-4h0 1c-1-2-1-5-1-6h1v2h0v2c1 1 1 2 1 3s0 0 1 1h0v-3l-1-1v-3c0-1 0-2-1-3v-3c-1-1-1-8-1-10 0-1 1-2 0-3 0-1 0 0 1-1z" class="b"></path><path d="M262 339c3-1 6-1 9-1l2 1v10h-1v1l-4 4c-1 1-2 3-3 4v1c0-4 1-6 3-9l1-1c-5 0-12 0-16-2v-1l3-3c2-1 3-2 4-4h2z" class="e"></path><path d="M256 343c2-1 3-2 4-4h2l1 1c0 1 1 1 1 1 1 0 2 0 3 1h0c1 0 2 1 2 2l2-1v1 1l-1-1h-7-1c-1 0-1 0-1-1h-2c-1 0-1 1-1 1l-2-1z" class="H"></path><path d="M863 640h1l-2 36-1 1-2-1h0l2-1h1l-1-1c0-3 1-6 0-9 0 0 1 0 1-1v-1h-1v3h-1c0 3-3 5-5 7v1l1-1h1c0 1 1 0-1 1-1 1-2 2-3 4 1 1 2 4 2 5l2 1 2-1h0l-2 2c-3 0-4-1-5-3-9-9 6-20 9-28 2-4 2-9 2-14z" class="V"></path><path d="M855 683h-1c-1-1-3-3-3-4v-6c0-1 1-2 2-2 2-3 4-5 7-8v1c-1 4-4 6-6 8-1 2-2 4-2 7l1-1c1 1 2 4 2 5z" class="D"></path><path d="M463 934h1c2 2 3 5 5 7v2h0c-1 0-2-1-4-1-11-1-22 0-32 0h-57-79c-13 0-26 0-39 1-1 0-2 1-2 3-1 2 0 3 1 5h0l-1-1c-1-1-2-2-2-4 1-1 1-2 2-3s2-1 3-1c6-1 13 0 19 0l46-1h94 49c-1-1-1-3-2-5l-2-2z" class="a"></path><path d="M271 327l1-1h2 1 3v1h-3c-1 1-1 9-1 11 6 0 12 0 17 3-3-1-5-1-7 0l-2 2c4 2 7 0 10 4-3 2-12 2-16 2l3 4h-1l-3-4h-2v-10l-2-1c-1-2 0-8 0-11z" class="F"></path><path d="M271 327l2-1 1 1v7c0 1 0 3-1 5l-2-1c-1-2 0-8 0-11z" class="C"></path><path d="M136 655l9 3c16 2 32 2 48 2-3 1-10 0-14 1-1 0-1 1-1 1v1c0-1 0-2-1-3h-17v19c2 0 4 0 6 1l-1 1c-1 1-2 1-4 1v1c1 0 3 0 4 1h-1c-1 0-2 0-3 1v5l1 1c-1 0-2 0-2 1v7l-1-8h-1v-1h2v-1h-2v-4c-1 0-1-1-2-1h-1l1-1h3l1-1-6-1v-1l1-1h3c1-2 1-4 1-5v-14c-4-1-9 0-13-2l-9-2h-1v10 127c-1-2 0-5 0-7v-20-111z" class="U"></path><path d="M825 357c4-1 8-1 11-1 8 1 15 4 20 10v1l1 1c-2 0-2-1-3-1s-2-2-3-2c-1-2-3-3-5-4 1 2 1 3 2 4h0c-1 0-2-1-4-1-1-1-2-2-3-2h-1c-1 0-3-1-4-1-9-2-17-1-24 5-6 4-9 10-11 17v2-2-3c0-1 1-1 1-3l-1 2h-1c1-2 2-5 3-7 5-8 13-13 22-15z" class="c"></path><path d="M825 357c4-1 8-1 11-1-2 2-6 2-9 2-1 0-1 0-2-1z" class="H"></path><path d="M836 361v-1c-1 0-2-1-3-1h0c4 0 9 1 13 2 1 2 1 3 2 4h0c-1 0-2-1-4-1-1-1-2-2-3-2h-1c-1 0-3-1-4-1z" class="f"></path><path d="M208 667c0 3-2 5-3 7 0 2-1 3-1 4h-1v1 2l-1 1v2s-1 0-1 1v2l-2 5v2h-1c0 1 0 2-1 3 0 1 0 1-1 2l-1-1c0-1 0-2 1-3v-2l1-2v-2l1-2v-1-2l1-2v-2c1-2 1-5 2-6 1-2 1-1 1-2v-2c1-1 1-1 1-2v-2c1-1 1-1 1-2v-2c1-2 1-2 1-3v-3c1-2 1-2 1-3v-3c1-1 1-2 2-3h0c0 1 0 1 1 2h2c1 0 2 1 3 2-1 3-3 6-4 10l-2 6h0z" class="E"></path><path d="M852 424c2-1 4-2 6-1 1-1 0-1 1-1 3-1 7-3 10-5 2 0 3-2 4-3 6-3 12-9 13-15 0-2 0-4 1-6v1s1 1 0 2c0 7-6 15-12 20-11 8-27 12-41 10-7-1-13-2-19-6s-12-10-14-17h0l2 3h1l-1-1c0-2-1-2-1-4h0c3 5 6 9 11 13 10 9 24 11 37 10h2z" class="X"></path><path d="M657 224c2-2 2-3 2-5v3 1l1 1c1 1 2 1 2 3v9 20c0 3-1 7 0 11l-1 7c-2-4-4-10-4-15-1-7-2-29 0-35z" class="J"></path><path d="M635 159c3-2 7-3 10-5 2-1 4-3 6-4 1 0 2-1 3-1 2-1 3-2 5-3 3-1 8-2 11-3l1 1h2c7-1 14-1 21-1 2 0 5 0 7 1h5c1 0 2 0 4 1 2 0 4 0 6 1 6 1 15 2 20 5v1l-17-5-6-1c-4-1-9-1-13-1-7-1-14-1-21 0h-2-1c-1 1-1 1-2 1h-1c1 0 1 0 3 1 6 0 13 1 19 2l1 1h4c2 1 7 2 9 3-1 1 0 1-1 1-1-1-2-1-3-1l-9-3h-3c-3-1-7 0-9-1s-4 0-5 0c-4 0-9-1-13 0h-4v1c-2 0-3 0-5 1 0 2 1 2 0 3-1-1-4-1-5 0h-1-1c-1 1-2 1-3 2-3 1-7 4-10 5h-1l-1-2z" class="S"></path><path d="M676 147h-10l3-1h3c1-1 3-1 4-1-1 1-1 1-2 1h-1c1 0 1 0 3 1z" class="Q"></path><path d="M876 642v-46-18l-1-1c-1-7-3-12-7-16v-1l4 4c1 1 2 3 3 4 1 2 1 3 1 5v1c1 1 1 3 1 4 0 3 0 7 1 9v2 1c2 0 3 1 5 2 1 0 1 0 2 1h1l2 2c0 1 0 2-1 3 0 2 0 5 1 7l-1 20c0 4 0 8 1 12v1 1c-1 1-1 1-3 0l1-41-8-1v38 13c-1 2 0 5-1 6h-1v-12z" class="W"></path><path d="M878 590c2 0 3 1 5 2 1 0 1 0 2 1h1l2 2c0 1 0 2-1 3l-3-1-6-1v-6z" class="E"></path><defs><linearGradient id="D" x1="686.438" y1="198.491" x2="684.568" y2="210.962" xlink:href="#B"><stop offset="0" stop-color="#343233"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#D)" d="M675 199h1c4-1 8-1 13-1h0c5 1 10 6 12 9 2 2 2 5 2 8l-2 1h0c0 1 0 1-1 2h-1l1-4c-1-1-2-1-3-2-1-2-3-4-5-7-2 0-4-1-6-2-5 1-9 1-14 4-1 1-3 2-4 3s-1 2-3 2c-1 1-3 4-3 6l-1-1c2-4 4-10 7-13 2-2 5-4 7-5z"></path><path d="M682 201c5 0 9 1 12 4h-2c-2 0-4-1-6-2h0c-1-1-3-1-4-2z" class="G"></path><path d="M694 205c3 2 5 5 6 9-1-1-2-1-3-2-1-2-3-4-5-7h2zm-26 5v-2c4-4 9-6 14-7 1 1 3 1 4 2h0c-5 1-9 1-14 4-1 1-3 2-4 3z" class="D"></path><path d="M444 672l-1-1c-2-3-5-9-6-13-1-1-1-3-1-5h1 11c3 5 5 10 8 15l-1 1v1s-1 1-1 2v1l-1 1-1-2c-2 0-1 0-3 1h-2-1-1l-1-1z" class="J"></path><path d="M447 671l8-2v1s-1 1-1 2v1l-1 1-1-2c-2 0-1 0-3 1h-2-1-1l-1-1c1 0 2 0 3-1z" class="N"></path><path d="M444 672l-1-1c-2-3-5-9-6-13-1-1-1-3-1-5h1v1h1c2 3 4 7 5 11 1 2 2 4 4 6-1 1-2 1-3 1z" class="l"></path><path d="M855 523l2 1v19l-10-1c-2 0-3 1-4 0h-2c0-1 1-1 1-1 1-2 1-8 1-10 1-1 1-1 3-1h-3-1c-1-1-1-2-1-3h1c1-1 1-1 1-2 2 0 4 0 6-1l6-1z" class="J"></path><path d="M849 524h0c0 1 0 1 1 2-2 1-6 1-8 1 1-1 1-1 1-2 2 0 4 0 6-1z" class="C"></path><path d="M269 484s1-1 1 0c-1 1-1 3-1 5v11 1c0 8-1 14-3 22 0-2 1-5 1-8v-2c1-2 0-4 1-6v-7h-6v1c1 0 3 0 4 1-1 0-1 0-1 1 1 3 0 8-1 10h0c0-1 0-2 1-4 0-1 0-5-1-6h0c-1 0-1 1-1 2-1 1-1 1-1 3l1 1c0 1 0 5-1 7v2h0c0 2 0 3-1 5v1c0 1 0 1-1 2h-1-1c1 2-1 2-1 4 0 1-1 2-1 2v1c-1-2-1-5-1-8 0-1 0-4-1-6v-4-4h1v2 4h1v-8c-1-1-1-1 0-1v-1l1 1c2-1 2-1 3-2l1-1v-6h0c1-1 3-3 3-5 1-1 1-3 1-4 1-2 1-5 4-6z" class="D"></path><path d="M258 526v-5c-1-3-1-5-1-8l1 1c1-1 1-1 2 0h0c0 4-1 7 0 11 1-2 1-5 2-7 0 2 0 3-1 5v1c0 1 0 1-1 2h-1-1zm11-42l-1 15h-7c1-1 3-3 3-5 1-1 1-3 1-4 1-2 1-5 4-6z" class="E"></path><path d="M254 511h1v2 4h1v-8c-1-1-1-1 0-1v-1l1 1 3 1c-1 1 0 1-2 1l1 1h1v3c-1-1-1-1-2 0l-1-1c0 3 0 5 1 8v5c1 2-1 2-1 4 0 1-1 2-1 2v1c-1-2-1-5-1-8 0-1 0-4-1-6v-4-4z" class="C"></path><path d="M213 505h6 0l2 2c0 1 1 2 2 2h3l1-1c1 1 2 3 2 4h1 0c1 1 1 3 1 5l1 1-1 1c0 1 0 2 1 3l-1 2h0l-1 1v1l-7-2c-1-1-1-3-1-4v-3-2c-1-2-3-4-4-6l-1 1h-1l1 1c2 1 3 2 3 5l1 8c-6 0-11 0-17-1h0v-1-1c2 0 3 1 4 0 1 0 2-1 3-1 0-1 1-1 2-2h1c0-1 1-1 1-2h1c0-1 1-2 1-3-1-2-3-3-4-5v-3z" class="E"></path><path d="M462 931h1l1 3h-1c-2 1-4 0-6 1l-1-1-77-1h-20c-2 0-5-1-7 0h0-1c-10 1-19 0-28 0h-22c-4 0-9 0-13-1v-1h1c5 1 10 1 14 1h26 50 32l38-1c3 0 8-1 12 0h0 1z" class="G"></path><path d="M836 634h2c2-1 4-3 5-2 2 0-1 0 2 0l1-1c3 2 5 3 7 7v2c0 2-1 3-1 5v2h0c-2 2-3 5-6 5-3 1-9-1-12-3h3c1 1 2 1 3 2h1c1 0 1 0 1 1 1 0 2-1 3-1h0l-1-1-1-1h0-1l-1-1h-3c0-1 0-1-1-1-1-1-2-2-2-3 0-3-1-5 1-7v-3z" class="E"></path><path d="M802 280l1-1h0l1 1c0 1 1 2 1 3 1-1 1-1 1-3 1 2 1 3 1 5l-3 1c-1 1 0 5 0 7 2 1 5 1 8 1l14 1c15 1 31 8 44 17 1 1 4 2 5 4h0c-1 0-3-1-4-2-3-2-5-4-8-6-4-2-9-5-14-6-17-7-35-8-53-7-11 0-22 1-33 3-8 2-16 5-23 9-14 7-26 21-31 36-1 4-2 8-2 11-1 6 1 12 1 18l-1 1-1-10c-1-18 6-32 18-45 13-14 35-21 53-23 7-1 15-1 22-1v-8-2h2v-3l1-1z" class="e"></path><path d="M802 280c1 1 1 1 1 3-1 0-1 1-2 1v-3l1-1z" class="H"></path><path d="M172 291v1l-8 6c-3 2-6 4-8 7l5 4h-1l-5-4-12 15-3 6v1c-2 2-2 4-3 6-4 8-5 15-7 24-1 7 0 16 1 23 1 3 2 7 3 10 2 6 6 12 11 17-6-14-7-28-11-42v-4l1-1v2c0 3 1 5 2 8l4 18c1 3 1 6 1 9l1 1c1 8 8 13 14 17 10 6 23 7 35 7 7 0 15-1 22-3 7-1 14-4 20-9 1-2 2-3 4-5v1c-2 3-4 5-7 7-13 9-28 10-43 10-4 0-9 0-13-1l-5-1c-7-1-15-5-20-10-2-1-4-3-5-5h-1c-5-3-11-14-13-20v-1c-1-4-1-8-2-12v-16c1-7 3-14 6-21 1-3 3-6 4-9v-2c2-2 3-4 4-6 2-3 6-7 7-9v-1h0c6-7 15-13 22-18z" class="V"></path><path d="M825 658l-1-1c0-1 1-1 1-1 0-1-1-1-1-2s1-1 1-1c0-1-1-1-1-1 0-1 1-1 1-2v-1h2c2 0 4 1 5 2h0c0 3 0 9 1 11 2-2 0-6 1-8 1 1 1 1 1 2v1 5 1 1 18c0 2 0 6-1 8-1 1-2 1-4 0l-1-1v-3l-1-8c0-1 1-3 0-3 0-1-2-2-2-2l-1-1c1-1 1-8 0-10h0l1-2h-1v-2z" class="S"></path><path d="M832 666h1v1c1 3 1 20 0 22h-1v-23z" class="J"></path><path d="M825 658l-1-1c0-1 1-1 1-1 0-1-1-1-1-2s1-1 1-1c0-1-1-1-1-1 0-1 1-1 1-2v-1h2c2 0 4 1 5 2h0l-1 1v1c-1 1 0 7 0 8h1v3c-1-1-1-1-1-2h-1v8c-2-2 0-5-1-8v-1l-1-1h0v-2h-3z" class="G"></path><path d="M650 922h144v-15 1c1 4 1 9 1 14l29-1h1v-2c1-2 1-4 2-6h24c-2 1-3 1-5 1h-11-8l-1 5c0 1 0 2-1 3s-6 1-8 1h-22c0 1 0 4 1 5 2 0 5 0 8 1h0v1l-1-1h-7-1v1c2 0 7 1 9 0h1c6 1 13-1 19 0-3 1-10 1-14 1-1 0-4-1-4 0-1 0-1 0-1 1s0 1-1 2v-2c-2-1-6 0-9 0 0 3 0 6 1 9v3 2l1 3c0 1 0 2 1 3h0c-2-1-2-2-2-3s0-2-1-3v-2-1l-1-4v-8h-10l-44-2v-1c7 0 15 1 22 1 8 0 16 0 23 1 3 0 6-1 8 0h1c1-2 0-5 0-7l-144-1h0z" class="C"></path><path d="M315 252h27c1 1 2 1 2 3h0c-5-2-12-2-17-2v1c6 1 13 0 19 3 1 1 4 5 6 6h-2l-2-1h-3c-4-1-7 0-11 0-1 1-2 2-4 3l-1 1c-1 1-1 1-2 1h-1v-2-1c0-1-1-2-1-2-3-1-7 1-10-1-1-2-1-4-1-5s0-1 1-2v-1-1z" class="h"></path><path d="M326 267l1-1v-3c2-1 5-1 7-1-1 1-2 2-4 3l-1 1c-1 1-1 1-2 1h-1z" class="C"></path><path d="M345 262l-1-1c-1-1-4-1-6 0h-3c-2 0-5 1-8 0l1-1h5v-2h4l2-1c2 0 5 1 6 1 2 2 4 3 5 5l-2-1h-3z" class="Y"></path><path d="M315 253h10c1 1 0 1 0 2-3 0-8-1-10 1v1l10 1v1h-3l3 1v1c-3 1-7 0-10 0h0c-1-2-1-4-1-5s0-1 1-2v-1z" class="B"></path><path d="M865 609c1-1 2-1 3-1v12h0v46c0 4-1 11 1 15v1c-2 0-2 0-4 1-1 2-3 4-3 7l-3 1v-2l1-1h0v1h0c1 0 1-1 2-2l1-3c0-1 1-2 1-2 1-1 2-1 3-1h0v-1c0-8 1-16 1-24 0-4-1-9-1-13v-4c1-3 1-6 0-8v-5h-1c0 1 0 2-1 2v1c1 2 1 8 0 9v8 1c-1 1 0 6 0 8-2 2 0 5-1 7v2c0 3 0 9-1 12v2l-2-1 1-1 2-36h-1c0-3-1-5-2-8 0-1 0-2-1-3h0c-1-2-1-3-3-3 0-1-1-1-1-1-1-1-2-2-4-3h-2l-1-1 9-1c1 0 2-1 4-1h1c-3-5-4-7-9-9 2 0 2 0 4 1l2-1c1 0 1 0 2-1h3z" class="Q"></path><path d="M865 609c-2 1-3 1-3 3v1c1 1 1 2 1 3-2-2-3-4-5-5l2-1c1 0 1 0 2-1h3z" class="B"></path><path d="M854 610c2 0 2 0 4 1s3 3 5 5c1 3 2 5 2 8l-1 1v-1c0-1 0-3-1-4v3h0c-1-1-1-1-2-1-2-1-2-1-3-2 1 0 2-1 4-1h1c-3-5-4-7-9-9z" class="H"></path><path d="M849 621l9-1c1 1 1 1 3 2h-2v1l2 2 1-1h0l1 1c1 0 1 0 1 1v14h-1c0-3-1-5-2-8 0-1 0-2-1-3h0c-1-2-1-3-3-3 0-1-1-1-1-1-1-1-2-2-4-3h-2l-1-1z" class="m"></path><path d="M184 524h-16-20c-3 1-7 1-10 2-1 1-3 1-4 1l11-3h2c2-1 5 0 8 0h1c0-1 1-5 1-6l8-25c1-2 2-7 3-8v-1l1 2v5c0 2 1 5 1 7l1 15v4 6h13v1z" class="K"></path><path d="M167 501l-1 18v2c-2 1-2 1-4 0 0-5 2-10 3-15 1-2 1-4 2-5z" class="D"></path><path d="M169 486v5c0 2 1 5 1 7l1 15v4c-1 2-1 4-1 5-1 0-3 1-4 0 1-1 1-2 1-3h-1l1-18v-4-1-1c0-1 0-1 1-2v-1c0-1 0-1 1-2s0-3 0-4z" class="F"></path><path d="M202 601c-3 1-6 1-8 0h-12c-6 0-13 1-19 0l-1-1s1-2 1-3v-7h-4c0 2 0 2 1 3l-1 1-1-1c0-2-1-4 0-6l1-1 2 2h-2l1 1h3v-10c5-1 9-1 14 1l1 3c2 4 2 6 1 10h-2l-3 5c2-1 2-1 3-2v2h0l-1 2c4 1 10 0 14 0 1-1 0-1 2-2 0 1 1 1 1 2 3 0 7 0 9 1z" class="i"></path><path d="M174 598c-3 1-6 3-8 2 0-2 0-4 1-6 0-1 2-2 3-3v-3c-1-1-1-1-3-2h0c-1-2-1-4-1-5l1-1c2 0 4 0 6 1s4 3 4 5l1 1v2c0 1 0 3-1 4l-3 5z" class="J"></path><defs><linearGradient id="E" x1="818.266" y1="528.512" x2="828.081" y2="526.932" xlink:href="#B"><stop offset="0" stop-color="#1d1c1e"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient></defs><path fill="url(#E)" d="M809 506h1v7c0 3 1 6 1 9h9l1-11c1-1 3-2 4-4l2 5v2c0 3 1 5 0 8 0 2 0 4-1 7v4c0 2 0 4 1 7v3 2 2h-4l-2 1-1-1c1-1 0-4 0-6v-1-7h-3v1c-1 1-3 1-4 1l-4-2c0-1 0-2 1-4-1-1-1-3-1-5l-3-1c1-1 2-1 4-1 0-6-1-11-1-16z"></path><path d="M827 514c0 3 1 5 0 8 0 2 0 4-1 7v4c-2-2-1-4-1-6v-3-1h-1l-1 4v-3c0-2 2-1 3-2h-1v-2l-1 1-1-1c1-1 2-1 4-2v-4z" class="T"></path><defs><linearGradient id="F" x1="818.527" y1="541.208" x2="830.251" y2="529.041" xlink:href="#B"><stop offset="0" stop-color="#333133"></stop><stop offset="1" stop-color="#4a4a49"></stop></linearGradient></defs><path fill="url(#F)" d="M823 527l1-4h1v1 3c0 2-1 4 1 6 0 2 0 4 1 7v3 2 2h-4c1-6 1-14 0-20z"></path><path d="M812 523h4 1 3v3 7h-3v1c-1 1-3 1-4 1l-4-2c0-1 0-2 1-4 1-1 0-3 0-5l2-1z" class="E"></path><path d="M812 523h4 1 3v3c-1 1-2 1-2 1-1 1-1 1-2 1h-1c0-2 1-2 1-4h-2v1c-1-1-2-1-2-2z" class="D"></path><path d="M808 638l-5-10h0c8 6 18 9 25 16 1 1 3 3 3 4l3 1c3 2 9 4 12 3 3 0 4-3 6-5 0 2-1 4-3 6h-1l-1 1h-1l-5-1-1 3-1-3c-1 0-2-1-3-1h-1 0v-1l-2 1-1-1h0c-1-1-3-2-5-2h-2v1c0 1-1 1-1 2 0 0 1 0 1 1 0 0-1 0-1 1s1 1 1 2c0 0-1 0-1 1l1 1v2h1l-1 2h0c1 2 1 9 0 10l-9-19c-3-4-6-10-8-15z" class="e"></path><path d="M824 646v-1c4 0 5 2 7 3v1c-3 0-5-1-7-3z" class="G"></path><path d="M808 638h2c2 2 3 5 4 8h0v-1c-1-1-1-2-2-2h0c1 3 2 5 3 7v1l1 2c-3-4-6-10-8-15z" class="H"></path><defs><linearGradient id="G" x1="841.519" y1="528.253" x2="829.948" y2="531.634" xlink:href="#B"><stop offset="0" stop-color="#232222"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#G)" d="M833 508l1-2h1c2 2 3 6 4 9v1 6h7l-1 1h-5c-1 1-1 1-1 2v22h-9-3v-2-2-3c-1-3-1-5-1-7v-4c1-3 1-5 1-7 2-1 3-1 4-1 0-3 0-6 1-9l1-4z"></path><path d="M833 518l1 1v1l1-6 1-1v17h0l-2-2v-3c-1-3-1-4-1-7z" class="K"></path><path d="M833 508l1-2h1c2 2 3 6 4 9-1 0-1-1-2-1v-1 2l-1-2-1 1-1 6v-1l-1-1 1-2v-6l-2 2h0l1-4z" class="N"></path><path d="M832 512h0l2-2v6l-1 2c0 3 0 4 1 7v3 12c0 2 0 4-1 6h-1c-1 0-1 0-2 1h-3v-2-2l1-1c0-1 0-1 1-1 0 1 1 1 2 2l1-1c0-1 0-1-1-2h0l1-1v-13c0-2 0-4-1-5 0-3 0-6 1-9z" class="m"></path><path d="M831 521c1 1 1 3 1 5v13l-1 1h0c1 1 1 1 1 2l-1 1c-1-1-2-1-2-2-1 0-1 0-1 1l-1 1v-3c-1-3-1-5-1-7v-4c1-3 1-5 1-7 2-1 3-1 4-1z" class="g"></path><path d="M828 529h0c0-3 0-4 2-6 0 4 0 9-1 13h-1v-7z" class="W"></path><path d="M826 529h2v7 2h0c1 1 1 1 1 2h1v-2c1 1 1 1 1 2h0c1 1 1 1 1 2l-1 1c-1-1-2-1-2-2-1 0-1 0-1 1l-1 1v-3c-1-3-1-5-1-7v-4z" class="Z"></path><path d="M224 458v-1c2-2 4-3 6-4h1c3 0 7-1 10-1l7 1h0c1 0 1 1 2 1h0l13-1c5 1 9 1 14 1h9c0 1-1 1-2 2-1 0-2 1-4 1-1 0-3 0-5 1-1 0-3 1-4 2l-1 1-1-1c1-1 2-1 4-2h1v-1h-2c-3 0-7 1-10 0-1 0-2 1-4 1l-1-1s-1 0-2 1l-1-1c0 1 1 2 1 3v1c-2-2-2-3-5-3-2 1-3 3-5 5h0l-3 5h-1c0 1 0 2-1 3s0 1-1 2l-1-2-2-2-1-1c-1-2-2-4-4-5-2-2-3-3-5-4-1-1-1-1-2-1z" class="S"></path><path d="M241 452l7 1h0c1 0 1 1 2 1h0c2 2 3 2 6 2 4 0 7 0 11 1 1 0 4-1 5 0-3 0-7 1-10 0-1 0-2 1-4 1l-1-1s-1 0-2 1l-1-1c0 1 1 2 1 3v1c-2-2-2-3-5-3-2 0-3-1-4-1h-14c-2 1-4 0-5-1h19 2v-1l-7-3z" class="Z"></path><path d="M250 454l13-1c5 1 9 1 14 1h9c0 1-1 1-2 2-1 0-2 1-4 1-1 0-3 0-5 1-1 0-3 1-4 2l-1 1-1-1c1-1 2-1 4-2h1v-1h-2c-1-1-4 0-5 0-4-1-7-1-11-1-3 0-4 0-6-2z" class="Y"></path><path d="M226 459l2-1s1 0 1 1c1 0 2 1 3 1l1-1c1 1 2 3 4 4h0v-1c-1-1-1-1-1-2 1-1 2 0 4 0 0 0 1 0 1 1 1 2 0-1 1 2h0c1 0 1-1 2-1v-2l1-1 1 1v1c-1 0-1 1-1 2h0l-3 5h-1c0 1 0 2-1 3s0 1-1 2l-1-2-2-2-1-1c-1-2-2-4-4-5-2-2-3-3-5-4zm564-39c5 1 8 1 11 5 2 2 4 6 3 9 0 1 0 3-1 4v1l-1 1c-1 1-1 1-3 2l-2 2 1 3c-1 1-1 2 0 3l1 2-1 1-1-1h-1v-8c-2 0-5 0-7-1-3-1-6-3-7-6s-1-7 0-10c2-4 5-5 8-7z" class="B"></path><path d="M791 440c-2-1-3-2-4-3-2-2-2-5-2-7 1-2 2-4 4-5s5-1 7-1c3 1 4 3 5 6h-1v-1c-1 0-1-1-2-1-2 0-4 0-5 1h-1c-2 2-4 3-5 6 0 2 2 4 4 5z" class="N"></path><path d="M787 435c1-3 3-4 5-6h1c1-1 3-1 5-1 1 0 1 1 2 1v1h1c1 2 1 3 0 5s-3 4-5 5c-1 1-1 1-2 1h-2l-1-1c-2-1-4-3-4-5z" class="Y"></path><path d="M787 435c1-3 3-4 5-6h1c1-1 3-1 5-1 1 0 1 1 2 1v1h1c1 2 1 3 0 5s-3 4-5 5c1-3 4-4 4-8h-1c-2-1-4-1-6-1 0 1-1 2-2 2h-1c-1 1-1 1-2 1l-1 1zm58 195c4 1 7 3 9 6 0 1 0 2 1 4h0v3l1 1c0 1-1 5-1 7v1c-1 4-4 8-7 13-2 4-3 11-3 17 1 3 1 4 3 6l1 1h1c1 1 3 2 5 2v1c-1 0-2 0-3-1h-2l-1-1h-4l-3 1v-3l-1-1v-4h0l-1-1h-1c0 3 1 7 0 10l1 1v1h-1c-1-2 0-3 0-5l-1-14c0-4 1-8 0-12l-1-1v2l-2-1v-1-5-1c0-1 0-1-1-2-1 2 1 6-1 8-1-2-1-8-1-11l1 1 2-1v1h0 1c1 0 2 1 3 1l1 3 1-3 5 1h1l1-1h1c2-2 3-4 3-6h0v-2c0-2 1-3 1-5v-2c-2-4-4-5-7-7l-1-1z" class="L"></path><path d="M848 660s0 1-1 1h-3c-1-2-1-4-1-7h2c0 2 2 2 3 3v3z" class="J"></path><path d="M853 640c1 2 1 3 1 4v1c0 2 0 4-1 6 0 3-1 5-3 8 0 0-1 1-2 1v-3c-1-1-3-1-3-3h1 1l1-1h1c2-2 3-4 3-6h0v-2c0-2 1-3 1-5z" class="B"></path><path d="M846 654h1l1 1 1-1 1 1h0v1 3s-1 1-2 1v-3c-1-1-3-1-3-3h1z" class="Q"></path><path d="M842 662l1 1v11c1-3 1-6 1-10h1c1 1 0 3 0 5 0 5-3 17 3 19h0l1 1h1c1 1 3 2 5 2v1c-1 0-2 0-3-1h-2l-1-1h-4l-3 1v-3c0-2 0-4 1-5-1-2-1-5-1-7v-3h-1c0-4 0-8 1-11z" class="C"></path><path d="M842 688c0-2 0-4 1-5l1 4v2l1 1-3 1v-3z" class="n"></path><path d="M832 651l1 1 2-1v1h0 1c1 0 2 1 3 1l1 3c0 1 0 3-1 4h1v2h2c-1 3-1 7-1 11h1v3c0 2 0 5 1 7-1 1-1 3-1 5l-1-1v-4h0l-1-1h-1c0 3 1 7 0 10l1 1v1h-1c-1-2 0-3 0-5l-1-14c0-4 1-8 0-12l-1-1v2l-2-1v-1-5-1c0-1 0-1-1-2-1 2 1 6-1 8-1-2-1-8-1-11z" class="d"></path><path d="M839 682c-1-2 0-16 0-19l1-1c1 4 0 11 0 15h1v-4h1v3c0 2 0 5 1 7-1 1-1 3-1 5l-1-1v-4h0l-1-1h-1z" class="B"></path><path d="M734 113c0-4 1-7 5-10 2-2 6-3 10-2 3 0 6 2 8 5 1 2 2 5 2 8-1 4-3 7-6 9-2 2-5 2-8 2-4-1-7-3-9-6-1-1-2-3-2-5v-1z" class="e"></path><path d="M745 102c2 0 4 0 6 1s5 4 6 6c1 3 0 6-1 9-2 3-4 4-7 5h-5c-3-1-5-3-7-5-1-2-2-5-1-8h0c1-1 1-2 2-3 1-3 4-4 7-5z" class="n"></path><path d="M783 541l5 1c2 0 5 1 6 4 1 2 3 9 2 12 0 0-1 1-1 2-2 1-3 3-4 5-3 4-4 8-5 12l-1 2c0-1-9-23-10-25 0-3 0-6 1-9 2-2 4-3 7-4z" class="M"></path><path d="M784 559h0c2 0 2 0 3 2 0 1-1 3-2 5h0c-1-2-3-4-3-6l2-1z" class="G"></path><path d="M496 652h71l-2 1c-4 6-7 11-14 14-1 1-2 1-4 2s-4 2-5 3c-1-2-3-4-4-5-1-2-2-2-3-3-2-3-4-5-6-7s-4-4-6-4h-1-10 1-17v-1z" class="F"></path><path d="M522 653h9c3 3 6 5 8 8 3 2 5 6 8 8-2 1-4 2-5 3-1-2-3-4-4-5-1-2-2-2-3-3-2-3-4-5-6-7s-4-4-6-4h-1z" class="S"></path><path d="M551 667l-15-14h29c-4 6-7 11-14 14z" class="E"></path><path d="M234 499h1c0 1 1 1 1 2v1c1 1 2 2 2 4h0c1 1 1 2 1 4v1l1 2v2 1h0v2c1 1 1 2 1 3v2h0v4 4h0c0 2 0 7 1 8 0 1 0 3-1 4-2 3 0 12 0 15v16c0 4-1 9-2 13v3h-1v-2c1-1 1-1 0-2h-3v3l-1-1v-4c2 1 3 1 5 1v-3-1c1-1 1-3 1-5 1-3 0-6 0-9v-11h-3v1h2l-3 2v3c1 1 1 0 1 1 0 3 0 8-1 11l-1 1c-2 1-2 3-5 3l-4 1c2-1 5-2 5-4v-1l1-1c1-2 2-4 2-5 0-2-1-1 0-2h0 1 0v-2-2-1-3h0c1-2 0-6 0-7v-9l-1-7c-1-2-1-3-1-4l-1 3c0 1-1 2-1 3l-1-1 1-2v-7l-9-2v9c-1 1 0 0 0 1v5 1 2h-1v-1l-1-1c-2-1-7 0-9 0h-19c-4 0-8 0-11 1-2 0-3 0-5-1l1-2c3 0 5 1 8 1h36v-6h-2l1-1 1-1v-7l-37-1v-1h20c6 1 11 1 17 1l-1-8c0-3-1-4-3-5l-1-1h1l1-1c1 2 3 4 4 6v2 3c0 1 0 3 1 4l7 2c1 0 2 0 3 1h3l1-1v-1h1c-1-2 0-7-1-9l2-1-1-4c-1-5-2-8-4-12z" class="B"></path><path d="M237 516l2-1c1 7 1 14 2 20 0 3 0 5-1 7v-5c-1-4-2-7-3-11v-1h1c-1-2 0-7-1-9z" class="Y"></path><path d="M236 527l1-1c1 4 2 7 3 11v5 2c-1 3-1 7-1 10-1 0-1 1-2 0l-1-18c-1-3-2-5-1-7 1-1 1-1 1-2zm319 395h16 41v-9h0v9h38 0 0l-38 1c0 1 0 5 1 6h25v1h-7 0-25-8-29-16l1-1h-1v-1l2-5v-1z" class="E"></path><path d="M555 922h16 41v-9h0v9h38 0 0l-38 1c0 1 0 5 1 6h25v1h-7 0-25-8-29-16l1-1c6-1 13 0 19 0h39v-6h-57v-1z" class="Y"></path><path d="M356 794l3 8c-2-2-5-7-5-10h-1c-2-8-1-17 1-25 4-16 12-33 18-49l25-66h9c7 1 14 0 20 0l-1 1c-9 0-18-1-26 1v1 1c2 1 4 1 6 3h1c1 0 2 2 3 3l-1 1-3-3-1 2v1l-2 2c0 3 0 6 1 9v2h0l1 4-10 1h-3l1 1c-3 1-5 1-6 4l-1 1c-3 8-7 16-9 25l1-1v1c-1 1-1 2-1 3h1c0 2 0 4 1 6h-1-4-1l-10 26h0l1 3v1l1-1h-1c1-2 1-2 2-3h1c0 1 0 1-1 2 0 1 0 2-1 2l1 1c0 1 0 2-1 3-1 2-1 5-1 7l-1 1v4l2 2h-2v1c-2-1-3-1-4-1-1-1-1-1-1-2l-1-2c0 2 0 3-1 4 0 1 0 2-1 3-1 5-1 11 0 16 0 2 0 4 1 6h1z" class="O"></path><path d="M376 712l1-1v1c-1 1-1 2-1 3h1c0 2 0 4 1 6h-1-4-1l4-9z" class="G"></path><defs><linearGradient id="H" x1="357.048" y1="750.397" x2="363.051" y2="764.487" xlink:href="#B"><stop offset="0" stop-color="#acaaa9"></stop><stop offset="1" stop-color="#cacbcc"></stop></linearGradient></defs><path fill="url(#H)" d="M362 747l1 3v1l1-1h-1c1-2 1-2 2-3h1c0 1 0 1-1 2 0 1 0 2-1 2l1 1c0 1 0 2-1 3-1 2-1 5-1 7l-1 1v4l2 2h-2v1c-2-1-3-1-4-1-1-1-1-1-1-2l-1-2c0 2 0 3-1 4 0 1 0 2-1 3 1-5 2-9 4-14 1-4 2-7 4-11z"></path><path d="M358 758v1 3c0 2-1 4 0 5h2c1-1 0-2 1-3l1-1v4l2 2h-2v1c-2-1-3-1-4-1-1-1-1-1-1-2l-1-2c0 2 0 3-1 4 0 1 0 2-1 3 1-5 2-9 4-14z" class="W"></path><path d="M389 681c0-1-1-1 0-2 0-3 1-5 2-7 1-4 3-11 6-13 3 0 5 0 8 1l-1 2v1l-2 2c0 3 0 6 1 9v2h0l1 4-10 1h-3-2z" class="G"></path><path d="M389 681s1-1 1-2l2-3c0-1 0-2 1-3h0c1-1 1-1 1-2 1-1 1-1 1-2l1 1c1-1 2-3 3-5l1 1c-1 1-1 1-1 2s0 1-1 2c-1 0-1 1-1 2s-1 3-2 4-1 3-2 4l1 1h-3-2z" class="D"></path><path d="M400 666v1l1 1v-4l1 1c0 3 0 6 1 9v2h0l1 4-10 1-1-1c1-1 1-3 2-4s2-3 2-4 0-2 1-2c1-1 1-1 1-2s0-1 1-2z" class="B"></path><path d="M373 721h4l2 1 4 1-2 1h4c1 3 0 12-1 16v1h-5v1c2 0 4-1 6 1v9 1h0c1-2 0-3 1-3v4c-1 0-2 0-3-1h-2c-1 0-1 1-1 1-1 1-1 1-1 2h-1c-1 0-2 1-3 1h-1-1c0-1 0-2-1-2l-2-1h0-3c-1 1 0 2-1 4 0 1 1 1 0 2h0c-1 1-1 2-1 3h-1-1v4l4 2c0 1 0 1-1 2h0c-1-1-2-1-3-1v1 1 2l1 1v1h0 0c1 2 1 3 2 4s2 3 3 4v1c-1 0-4-3-4-4-1 0-1 0-1-1l-1-3h0v-1-2c-1-1-1-1-1-2v-2-1h2l-2-2v-4l1-1c0-2 0-5 1-7 1-1 1-2 1-3l-1-1c1 0 1-1 1-2 1-1 1-1 1-2h-1c-1 1-1 1-2 3h1l-1 1v-1l-1-3h0l10-26h1z" class="Q"></path><path d="M365 752l5 1h-4c-1 4 0 7-3 9 0-2 0-5 1-7 1-1 1-2 1-3z" class="C"></path><path d="M381 724h4c1 3 0 12-1 16h0c-1 0-2-1-3 0h-1c0-2 0-4 1-6 0-2 0-8-1-9l1-1z" class="E"></path><path d="M373 721h4l2 1 4 1-2 1-1 1h-1v-1c-1 1-2 1-2 2s0 2-1 3v2c-1 2-1 4-3 7 0 1 0 1 1 2 1 2 3 1 4 2-2 1-2 1-4 0-1 1-2 2-3 2-1 1-2 2-3 4v2c1 1 2 1 3 1v1c2 0 4 0 6-1l3-1 1 1h0l-2 1c-1 0-1 1-2 1s-3 0-4 1h-1c-1-1-1-1-2-1h0l-5-1-1-1c1 0 1-1 1-2 1-1 1-1 1-2h-1c-1 1-1 1-2 3h1l-1 1v-1l-1-3h0l10-26h1z" class="W"></path><path d="M373 721h4l2 1 4 1-2 1-1 1h-1v-1c-1 1-2 1-2 2s0 2-1 3v2c-1 2-1 4-3 7 0 1 0 1 1 2 1 2 3 1 4 2-2 1-2 1-4 0h-1l-1 1c-2 0-2 0-4-1 0 1-1 1-2 1h-1c2-4 4-9 6-12 0-1 1-1 1-2v-1c1-1 1-3 1-4-1-1 0-2 0-3z" class="D"></path><path d="M368 742c1-2 2-6 3-7h1c0 2 0 4 1 6l1-1c1 2 3 1 4 2-2 1-2 1-4 0h-1l-1 1c-2 0-2 0-4-1z" class="C"></path><path d="M373 721h4l2 1 4 1-2 1-1 1h-1v-1c-1 1-2 1-2 2l-1-1h0c-2-1-1 0-2-1h-1c-1-1 0-2 0-3zm389-303c2 1 4 2 5 4 3 6 4 13 5 19-1 1-1 0-1 1 1 1 0 2 0 3 0 2 0 9 1 10v3c1 1 1 2 1 3v3l1 1v3h0c-1-1-1 0-1-1s0-2-1-3v-2h0v-2h-1c0 1 0 4 1 6h-1 0v4c0 1 0 1 1 2v3c0 1 0 1 1 3v4h0c-2 2-4 4-4 7-1 3 0 5 1 8h1c1 0 2 0 3 2l1 1c-1 2 0 5-1 7v6c-3 1-6 1-9 1v-4c-1-4-1-9-1-13v-22c-1-1 0-6 0-8v-1c-1-1 0-16 0-19h-1v-2c-1-3-1-8-1-11v-2c-1-2-1-8 0-10v-4z" class="M"></path><path d="M765 432l1 1v2 8 1c1 6 1 12 1 18 0 4-1 7-1 11l1 18c0 2 1 3 1 5v11 7c-2-3 0-17-2-21-1-3 0-7 0-10l-1-25v-26z" class="m"></path><path d="M762 418c2 1 4 2 5 4 3 6 4 13 5 19-1 1-1 0-1 1 1 1 0 2 0 3 0 2 0 9 1 10v3c1 1 1 2 1 3v3l1 1v3h0c-1-1-1 0-1-1s0-2-1-3v-2h0v-2h-1c0 1 0 4 1 6h-1 0 0l-1-1v-6l-1 1v6c1 1 1 0 1 1v2 1 1c1 1 1 3 1 5v4c-2-2-1-3-1-6-1-1-1-1-1-3h0v-2l-1-1v-2c-1-6 1-13 0-20v-1c-1 0-1-1-1-2l-1 1v-1-8-2l-1-1v-1c-1-1-1-2-1-3v1 18h-1v-2c-1-3-1-8-1-11v-2c-1-2-1-8 0-10v-4z" class="F"></path><path d="M221 400h1c-8 6-21 7-31 6-1-1-3-1-5-2h0c-6-2-9-6-11-11-2-6-2-14 0-19v-1c2-6 7-13 12-16 3-1 6-2 9-2 3-1 7 0 10 0 2 0 4 1 5 2 8 2 14 7 19 13 5 7 7 13 9 21s1 15-2 23c-1 0-1 1-1 1-2 7-9 12-14 16h-1c1-1 3-2 4-3 5-4 9-9 11-14-3 2-5 5-9 7-14 8-29 10-44 9-7 0-14 0-20-1-19-3-36-12-47-28h0 0c11 14 27 24 45 27 11 2 25 3 37 1 13-2 29-4 37-16 2-2 3-5 3-7v-1c1-4 0-9 0-13-2 6-5 11-10 14-11 7-22 9-34 9h-8c-1-1-1-1-2 0-3 1-10-1-13-3h0c-3-1-4-2-5-4h0c3 2 5 3 8 4l1 1c3 1 5 0 8 1h11c10-1 19-2 28-6 6-2 13-9 15-15 1-6-2-13-5-19 0 2 1 4 1 6 0 4-1 9-3 13h-1v-1c-1 0-4 0-5 1s-1 2-2 3v-3c-3-1-8 0-11 0h-9c-3 0-6-1-8 0-2 0-2 3-3 4l-2-1v-2-1c-1 0-2 0-2-1l-2-1h0v-1h1c1 1 3 1 4 0 0-3 0-8-1-10h-2v-1h2l3-12v9c0 1 2 4 1 4l-1 1v10h19l1-1v1c2 1 8 0 10 0 1-7 2-15 0-23-1-3-2-6-5-8s-6-3-9-4l-1 1 1 2v-1c-1-1-2-2-3-2h-2-1c-2-1-7 0-9 0h-1c-5 2-11 7-14 11-3 5-5 15-4 21h0c1 5 4 10 8 12 3 3 7 4 11 5h1c6 1 15 1 21-2 2 0 5-2 6-3z" class="H"></path><path d="M220 362c5 3 7 6 10 10 3 7 2 11 0 17v2h-1-5c-1-1 0-8 0-10 0-4 0-7-1-10h0l-3-9z" class="n"></path><path d="M839 682h1l1 1h0v4l1 1v3l3-1h4l1 1h2c1 1 2 1 3 1v-1c-2 0-4-1-5-2h-1c3 1 6 2 10 2l3-1v1 1 2c0 1-1 1 0 2l1 1h0v7l-3-1h-2v1c-2 0-3 1-4 2h-1c-5 4-8 9-9 15-1 3-1 6-2 8-1-1-1-1-1-2-3-7-7-13-11-19l-1-3c-1 0-2-1-2-2h0 1c1-1 0-2 0-3h0c-1-1-3-4-3-5-1-2 0-2-1-3v-1h1l2 2v1h8 1c1-1 1-1 1-2s1-2 2-3c0 2-1 3 0 5h1v-1l-1-1c1-3 0-7 0-10z" class="E"></path><path d="M836 704h3c1 1 3 3 3 4 0 2-1 3-1 5h-1c-2-2-3-6-4-9z" class="l"></path><path d="M849 689c3 1 6 2 10 2l3-1v1 1 2c0 1-1 1 0 2h-6-11c0-3 5-3 7-5 1 1 2 1 3 1v-1c-2 0-4-1-5-2h-1z" class="V"></path><path d="M856 696c-1 0-1 0-2-1-1 0-1 1-1 0l3-2c2 0 4-1 6-1v2c0 1-1 1 0 2h-6z" class="m"></path><path d="M845 690h4l1 1h2c-2 2-7 2-7 5-1 2-2 3-2 5-1 2-1 5-1 7 0-1-2-3-3-4h-3c-1 0-1-1-1-2-2 0-2 0-4-1v-1c1 0 2 1 4 0 3 0 4-3 6-5l1-4 3-1z" class="P"></path><path d="M845 690h4l1 1c-4 0-6 2-9 4l1-4 3-1z" class="W"></path><path d="M233 584h1v4l1 1v-3h3c1 1 1 1 0 2v2h1c0 2-1 4-2 6v1c0 2-1 4-2 6l-1 1-1 2v1s-1 1-1 2c-1 1-2 3-4 4-1 1-1 1-3 2l-1 1v1c1 1 1 2 1 3l-1 1v1c0 2 0 4-1 7v2c-1 1 0 3 0 4l-1 4c0 1 0 2-1 3v3 1h-1 0 0l-1-1c0-1 0-1 1-2v-2h0v-2c1-1 1-2 1-3h0c0-2 0-4 1-5h0l-1-2 1-1v-4c1-1 0-1 0-2v-2h1c0-1 0-1-1-2 0-2 1-3 0-4 0-2-1-3-1-4v-1-1l-1 1c-1 0-2 0-2-1l-1-1h-1l-2-2-4-1c-1-1 0-1-1-1s-1 0-2-1c-2 1-2 1-4 1l-1-2h0c-2-1-6-1-9-1 0-1-1-1-1-2-2 1-1 1-2 2-4 0-10 1-14 0l1-2h0v-2c-1 1-1 1-3 2l3-5h2c2 1 5 1 8 1h18l2-1c4-1 10 0 14 0 1-1 0-3 0-4l1-3h1l10-1v-1z" class="E"></path><path d="M230 603h0c0 2-1 5-2 7h0l-1 1-1-2c2-2 3-4 4-6z" class="C"></path><path d="M202 601v-2h4l1 3c-2 1-2 1-4 1l-1-2z" class="N"></path><path d="M174 598l3-5h2c2 1 5 1 8 1h18l2-1c-3 2-7 1-10 1h-14-4c-1 1-2 3-2 4h0v-2c-1 1-1 1-3 2z" class="X"></path><path d="M212 599h3c1 1 2 0 3 0 2 1 4 0 5 1 0 2 0 2-1 3h-2l-1 2c-2-2-4-3-6-4-2 0-2 0-3-1l1-1h1zm50-142c3 1 7 0 10 0h2v1h-1c-2 1-3 1-4 2l1 1 1-1c1-1 3-2 4-2 2-1 4-1 5-1 0 1-1 2-2 3v1c-1 1-3 2-4 3h1 3l1 1h2l-4 3h-1c-2 2-4 3-5 6h-1c0 1-1 1-1 2l-4 7c-1 1-1 2-1 2v1c-1 3-5 9-3 13h0v6l-1 1c-1 1-1 1-3 2l-1-1v1c-1 0-1 0 0 1v8h-1v-4-2h-1c0-1 0 0-1-1v-3-1l-1-2c0-1 0-2-1-3v-2l-2-7c-1-2-2-3-2-5l-2-3-1-1 1-1h1v-2h0c0-1 0-1 1-2 1 1 0 2 0 4h1c1-1 1-1 1-2l1-1c0-1 1-2 1-2l1-1c0-1 1-2 1-3l1-1 2-2c-1-2-2-2-2-4l-1 1-1-1c1-1 1-1 1-2l2-2v-1-1c0-1-1-2-1-3l1 1c1-1 2-1 2-1l1 1c2 0 3-1 4-1z" class="B"></path><path d="M264 485v1c-1 3-5 9-3 13h0v6l-1 1c-1-2-1-3 0-5v-2c-1-1 0-2 0-4 0-1 0-2 1-4l3-6z" class="a"></path><path d="M255 461v-1c0-1-1-2-1-3l1 1c1-1 2-1 2-1l1 1c2 0 3-1 4-1 0 1-1 2-2 3 0 1-1 1-1 1h-1c-1 1 0 1-1 1v1l2-1v2l1 2h-2c0 1-1 2-1 2 0 1-1 2-1 2-1-2-2-2-2-4l-1 1-1-1c1-1 1-1 1-2l2-2v-1z" class="C"></path><path d="M261 491c-1 0-2 0-3 1h0c-1-1-1-1-1-2l1-3c1-1 1-1 1-2v-1c1-1 1-2 2-3v-1c0-1 1-1 1-2l1-1h-1c-1-1-1-2-2-3v-1c1-1 1-2 2-3l1 1v1c1 1 1 2 1 3l2-3 1-1c1-1 2-3 3-4 2-1 1 0 2-1l2-2h1 3l1 1h2l-4 3h-1c-2 2-4 3-5 6h-1c0 1-1 1-1 2l-4 7c-1 1-1 2-1 2l-3 6z" class="E"></path><path d="M876 672l1-5v13c2 1 5 1 7 1h12 4l1-1c1 1 5 1 7 1h13c2 0 3 1 4 3 1 1 2 3 2 4-1 3-2 6-5 7h-1c-4 1-9 0-12 1h-1v5c-2 0-5 0-7-1v-1h0c-10 1-20 1-30 1v1c0 1-1 2-1 3s0 3-1 4c-2 0-3 0-5-1l-1-3v-7h0l-1-1c-1-1 0-1 0-2v-2-1-1c0-3 2-5 3-7 2-1 2-1 4-1v2c1 2 3 4 2 6 5 0 27 1 29 0v-2 2h8 1c2 1 10 1 12-1v-3c-1 0-1-1-2-1-2 0-9 0-11 1v2l-1-3h-6v1c-3-2-17-1-20-1-2 0-4 1-6 0v-4c1-3 1-6 1-9z" class="I"></path><path d="M869 684c1 2 3 4 2 6l-1 1 1 2c1 1 2 1 3 1h1c-2 0-3 1-5 0v3l1-1c1 0 2 0 3-1h7 19c2 1 5 0 8 1h0c2-1 4 0 5-1h8c-4 1-9 0-12 1h-1c-2 0-6-1-7 1-2-1-6-1-8-1l-21 1c-1 1-1 2-1 3v1c0 1-1 2-1 3v-2c0-1-2-1-2-3l1-1c-1-1-1-1-1-2-1-3 0-8 1-12z" class="U"></path><path d="M871 700c0-1 0-2 1-3l21-1c2 0 6 0 8 1 1-2 5-1 7-1v5c-2 0-5 0-7-1v-1h0c-10 1-20 1-30 1z" class="B"></path><path d="M862 690c0-3 2-5 3-7 2-1 2-1 4-1v2c-1 4-2 9-1 12 0 1 0 1 1 2l-1 1c0 2 2 2 2 3v2c0 1 0 3-1 4-2 0-3 0-5-1l-1-3v-7h0l-1-1c-1-1 0-1 0-2v-2-1-1z" class="j"></path><path d="M760 354c1 1 1 2 2 3v43 11c0 2 0 4-1 6l1 1v4c-1 2-1 8 0 10v2c0 3 0 8 1 11v2h1c0 3-1 18 0 19v1c0 2-1 7 0 8v22c0 4 0 9 1 13v4c-3 0-5-1-8-2v-26l-1-71c0-16-1-33 1-49 0-4 1-8 3-12z" class="I"></path><path d="M650 922l144 1c0 2 1 5 0 7h-1c-2-1-5 0-8 0-7-1-15-1-23-1-7 0-15-1-22-1v1h-67l-35 1v-1h-25c-1-1-1-5-1-6l38-1z" class="J"></path><path d="M638 929c6-1 13 1 20 0 9-1 19-1 28-1h54v1h-67l-35 1v-1z" class="S"></path><path d="M772 418c2-1 4-1 6-1 3 1 6 0 10 1 2 0 3 1 5 1v1h-3c-3 2-6 3-8 7-1 3-1 7 0 10s4 5 7 6c2 1 5 1 7 1v8 84l-1-1c-5-2-11-3-16-1-5 1-8 4-11 9l-8-22c7 1 15 2 22-2 6-4 9-10 10-17 0-3 1-7 0-11-1-3-1-7-1-10l-7-25-1-4c-1-1-1-4-2-6-3-9-4-21-9-28z" class="b"></path><path d="M783 452c3 1 3 2 3 4l1 1-3-1-1-4z" class="I"></path><path d="M784 456l3 1c1 5 3 11 4 16l-1 1c0 4 2 8 3 12v5h-1c-1-3-1-7-1-10l-7-25z" class="F"></path><path d="M871 701h8c-3 1-5 1-7 1l-1 2v1c0 1 0 2-1 2v1l1 1-2 2-1 1h0c1 3 1 25 0 27v1 4 41l-2-8-3 1c-1-1-9-21-10-23-2-4-4-7-5-10l-1-1c-2-4-4-9-5-15 1-2 1-5 2-8 1-6 4-11 9-15h1c1-1 2-2 4-2v-1h2l3 1 1 3c2 1 3 1 5 1 1-1 1-3 1-4s1-2 1-3z" class="J"></path><path d="M868 740v4c-1 0-2 1-3 2h0c-1 1-2 1-3 1h-1c-2-1-3-2-4-3h0c-2-1-2-2-3-3v-1l1 1c1 1 3 2 5 2s4 0 5-2c1 0 2-1 3-1z" class="C"></path><path d="M848 745c1 0 1 1 2 2 1 2 2 3 4 4l7 16c-1-1-2-2-3-4h-1l-2-6c-1-1-1-2-2-3v1c-2-4-4-7-5-10z" class="g"></path><path d="M853 755v-1c1 1 1 2 2 3l2 6h1c1 2 2 3 3 4l5 10-3 1c-1-1-9-21-10-23z" class="k"></path><path d="M858 704v-1h2c-3 5-8 9-10 14-3 7-2 11 1 18l-1 1c-1-3-2-5-2-8-1-1-1-3-1-4 0-2 0-4 1-5v-1l-1-1c-1-1 0-2 0-3 1 0 1 0 1-1l1-1 3-3 2-2c2-1 3-2 4-3z" class="W"></path><path d="M842 729c1-2 1-5 2-8 1-6 4-11 9-15h1c1-1 2-2 4-2-1 1-2 2-4 3l-2 2-3 3-1 1c0 1 0 1-1 1 0 1-1 2 0 3l-3 9c-1 4-1 6 1 10 2 2 3 3 4 5 2 3 4 6 5 10-2-1-3-2-4-4-1-1-1-2-2-2l-1-1c-2-4-4-9-5-15z" class="l"></path><path d="M871 701h8c-3 1-5 1-7 1l-1 2v1c0 1 0 2-1 2v1l1 1-2 2-1 1h0c1 3 1 25 0 27-1-6 0-13 0-20 0-2 0-5-1-7h-2c-1 1-1 1-3 1v1c-1-1-1-2-2-3v-1h3 0c1 1 2 1 4 1 1 0 0 0 1-1v-1h-3c-3-1-1-2-3-4l-1 1v1l-1 1c-5 2-6 9-6 13 0 2 1 3 2 5h1l-1 1c-2-2-3-3-4-5s-1-3-2-5c2-5 7-9 10-14l3 1 1 3c2 1 3 1 5 1 1-1 1-3 1-4s1-2 1-3z" class="B"></path><path d="M250 458c3 0 3 1 5 3v1l-2 2c0 1 0 1-1 2l1 1 1-1c0 2 1 2 2 4l-2 2-1 1c0 1-1 2-1 3l-1 1s-1 1-1 2l-1 1c0 1 0 1-1 2h-1c0-2 1-3 0-4-1 1-1 1-1 2h0v2h-1l-1 1 1 1 2 3c0 2 1 3 2 5l2 7v2c1 1 1 2 1 3l1 2v1 3c1 1 1 0 1 1v4 4c1 2 1 5 1 6 0 3 0 6 1 8 1 1 0 3 0 5v1c0 1 0 5-1 7 0 1 1 3 0 4 0 1 0 1-1 3v2h-1l-1 4c0 1-1 2-2 3 0 1-1 1-1 2l-2-1c0-1-1-2-1-3h0c0-1-1-2-1-2l-2-10v-8c-1-4 0-7-1-10v-1c0-4 0-9-1-13h0c1-1 1-4 1-5-1-2 0-4-1-6v-1l-1-6-1-3c0-1-1-2-1-2v-2l-1-1-1-2v-2c-1-1-1-2-2-2v-1c-1-2-2-3-3-4h1l2 2 1-1c-1-1-1-2-1-2-1-2-2-3-3-5h0c-2-2-7-8-10-9-1 0-1 0-2-1 1-1 1 0 2 0v-1c1-1 2-3 3-4 1 0 1 0 2 1 2 1 3 2 5 4 2 1 3 3 4 5l1 1 2 2 1 2c1-1 0-1 1-2s1-2 1-3h1l3-5h0c2-2 3-4 5-5z" class="E"></path><path d="M245 516c1 1 1 2 1 3h1c0-1-1-3 0-4 2 3 0 5 2 7v16h0v-1c1-1 1-1 1-2 1 1 1 3 1 4-2 7-1 16-1 23 0 1-1 1-1 2l-2-1c1-1 1-1 0-2v-2l-1-5c-1-4-1-7-1-11l-1-1v-8c0-4-1-14 1-18z" class="B"></path><path d="M231 473c1 1 2 1 3 3l1 2c3 4 6 9 7 14l1 1c0 1 0 2 1 3v2c1 2 1 4 2 6v2h0c-1-1-1-3-2-4v1c0 1 1 3 1 5v8c-2 4-1 14-1 18v8l1 1c0 4 0 7 1 11l1 5v2c1 1 1 1 0 2 0-1-1-2-1-3h0c0-1-1-2-1-2l-2-10v-8c-1-4 0-7-1-10v-1c0-4 0-9-1-13h0c1-1 1-4 1-5-1-2 0-4-1-6v-1l-1-6-1-3c0-1-1-2-1-2v-2l-1-1-1-2v-2c-1-1-1-2-2-2v-1c-1-2-2-3-3-4h1l2 2 1-1c-1-1-1-2-1-2-1-2-2-3-3-5z" class="S"></path><path d="M279 353l-3-4c4 0 13 0 16-2-3-4-6-2-10-4l2-2c2-1 4-1 7 0 1 0 3 1 3 2v1l1 1c1 1 0 2 0 4v1h-1c-1 3-6 7-6 9v1c1 0 3 1 3 2h0l2 2c1 1 0 0 0 1 1 1 2 2 3 4l1 2c2 2 5 7 4 10v1c-1 1-1 2-1 2v1c-1 1-1 2-1 3-1 1-1 2-2 3h0c-1 2-2 3-3 4-1 2-2 4-5 5 0 1-1 1-2 2 0 2 1 9 0 11 0 1 0 1 1 2h-1c0 1 0 1-1 2v2h0l1 1c-1 1-1 1-1 2v1 2c-1 7 0 14 0 21v8h-9c-5 0-9 0-14-1h12v-36c1-2 1-7 1-9v-1h-1 0c1-1 1-1 1-2-1-2-1-22 0-24v-5c1-2 1-2 3-2v-1h-1-1v-1l-2-1v-2c-1-3 0-5-1-8s1-7 0-10h-1v-2h2l3 4h1z" class="E"></path><path d="M273 349h2l3 4h1c2 2 3 4 3 7-2 1-4 1-6 1 0 2 0 5-1 7v1c-1-3 0-5-1-8s1-7 0-10h-1v-2z" class="D"></path><path d="M878 648v-13-38l8 1c0 4-1 41-1 41 2 1 2 1 3 0v-1h27c0 1-1 1-2 1s-2 0-3 1h-2 0c1 1 3 1 4 1s1-1 1-1h1c1 2 3 4 3 6-1 1-2 1-4 1h0l-2-1h-2c-1-1 0-4-1-4v39c-2 0-6 0-7-1l-1 1h-4-12c-2 0-5 0-7-1v-13l-1 5c-1-6 0-12 0-18 0-4-1-8 0-12v12h1c1-1 0-4 1-6z" class="E"></path><path d="M876 672c-1-6 0-12 0-18 0-4-1-8 0-12v12h1c1-1 0-4 1-6 1 11 0 22 2 32l4 1c-2 0-5 0-7-1v-13l-1 5z" class="Y"></path><path d="M748 366l-1 2v1 3c-1 1-1 1 0 2-1 1-1 1-1 2v1-17c0-3-1-5 1-8 0 0 0 1 1 1h2c3 1 6 1 8 0h2v1c-2 4-3 8-3 12-2 16-1 33-1 49l1 71v26c-7-4-11-13-14-20 0-1-1-4-1-5l2 5h1c1-3 0-7-1-10 0-1 0-2-1-3h0 0c-1-2-1-2-1-3l1-2c2-2 1-9 0-11s1-4 1-6v-2c1-3 0-6 0-8 0-10-1-20 0-30v-7l-1-1h1v-6-15-10c0 1 1 1 2 1l1-4v-4c1-2 1-3 1-5z" class="E"></path><path d="M751 358h1v149c1 1 2 3 4 3 1-2 1-5 1-7 0-5-1-11 0-17v26c-7-4-11-13-14-20 0-1-1-4-1-5l2 5 6 14V406v-30c0-4 0-9 1-13v-5z" class="P"></path><path d="M748 366v-3c0-1 0-4 1-6l2 1v5c-1 4-1 9-1 13v30 100l-6-14h1c1-3 0-7-1-10 0-1 0-2-1-3h0 0c-1-2-1-2-1-3l1-2c2-2 1-9 0-11s1-4 1-6v-2c1-3 0-6 0-8 0-10-1-20 0-30v-7l-1-1h1v-6-15-10c0 1 1 1 2 1l1-4v-4c1-2 1-3 1-5z" class="Y"></path><path d="M744 388v-1c1-1 1-1 1-2h2v-1h1v21c0 2-1 5 0 7v1c0 4 1 9 0 13v4c-2-3-1-12-1-15-1-1-1-1-2-1 0 1 0 2-1 3v-7l-1-1h1v-6-15z" class="D"></path><path d="M744 417c1-1 1-2 1-3 1 0 1 0 2 1 0 3-1 12 1 15v9 3 23 14 7l-1 1h0c-1-3-1-6-4-8h0c-1-2-1-2-1-3l1-2c2-2 1-9 0-11s1-4 1-6v-2c1-3 0-6 0-8 0-10-1-20 0-30z" class="C"></path><path d="M821 434h2l1 1h12c15 0 31-2 43-11 8-6 14-14 16-25 2-14-3-30-12-41 1 0 3 2 4 3 0-1 0-1 1-2 0 1 1 2 1 3 1 5 4 10 6 15 1 4 1 8 1 12v6l-1 8c-2 10-9 18-17 24l-4 2h25c1-1 1-3 1-4l1 1-1 3h22 16c2 0 5-1 6 0-5 1-11 1-17 1-2 0-5-1-6 0v2 1h1c5 1 10 0 15 0 1 0 3 0 5 1h-21v12c0 2 0 4 1 5 1 0 2-1 3 0s1 2 1 4h0c-2 1-3 1-5 0-3-1-10 0-13 0h-41-9c-2 0-4 0-5-1-3-1-3-4-4-6 1-3 5-6 8-8l-1-1c2-1 2-2 2-3v-1c-5 1-10 1-15 1s-11 0-16-1c-2 0-4 0-6-1z" class="X"></path><path d="M909 434h11v17h-11v-17z" class="J"></path><path d="M921 433c-13 1-27 0-40 0h-9c-2 0-4 1-6 0 0-1 0-1 1-1l3-1c1 0 2-1 3-1 5-1 11 0 16 0 3 0 8-1 10 0 0 1 0 1 1 2v-2h1c2-1 6-1 7 0v1l1 1v-2h11c0 1 0 1 1 2v1z" class="E"></path><path d="M857 440h1l5-5c1-1 3-1 5-1v1c1 1 0 3 0 4v12c-4 0-8-1-13 0h0l55 1 10-1h4c1 1 1 2 1 3-1 1-2 1-4 1-3-1-10 0-13 0h-41-9c-2 0-4 0-5-1-3-1-3-4-4-6 1-3 5-6 8-8zm13-4l1-1 21-1c2 0 6 0 8 1h0c2-1 6 0 8 0v16h-8v-10c-1 3-1 6-1 10h-29v-9-6z" class="J"></path><path d="M870 436h1l9 1h17 1c-2 1-4 1-6 1h-8c-4 0-10-1-13 1 0 1 0 2-1 3v-6z" class="B"></path><path d="M835 487l74-1c5 0 11 0 16 1h1 0l-5 1v13 3c-1-1-1-3-1-4v-12h-11v6 16c0 3-1 8 0 11 2 1 3 0 5-1v1l-5 2h0v2c-1 2 0 4 0 6v14 47h-1v-65c-1-1-2 1-3 0l3-2v-2c-3-1-6 0-9 0h-28-16l-6 1c-2 1-4 1-6 1h-4c0-1 0-1 1-2h5l1-1h-7v-6-1c-1-3-2-7-4-9h-1l-1 2-1 1-1-1-1-8v-1c1 0 2 0 2-1 0 0-1 0-2-1v-8c1-1 1-1 2-1h2 1v-1z" class="P"></path><path d="M835 488c2 0 5 0 7-1h1c1 1 3 1 4 1v33l-1 1h-7v-6-1c-1-3-2-7-4-9h-1l-1 2-1 1-1-1-1-8v-1c1 0 2 0 2-1 0 0-1 0-2-1v-8c1-1 1-1 2-1h2 1z" class="J"></path><path d="M848 488h20v5h-4c-1 0-1 1 0 2 0 1 1 2 2 2h1c0 1 0 1 1 2h0l1-1c0 2-1 3 0 4 2-2 1-3 2-5 1 0 1 0 2-1 1 0 1-1 1-2l-1-1h-3v-5c5 0 28-1 30 0 1-1 6 0 8 0v9 24c-6 2-13 1-19 1h-41v-34z" class="n"></path><path d="M832 557c4 0 8 0 12 1l3 1c6 1 12 5 16 10 1 2 1 3 2 5 0 1 0 2-1 3h1v2c-1-1-1 0-1-1-1-1-1-2-2-2 3 7 3 18 0 25-1 2-2 4-4 5-3 2-5 2-8 3 1 0 3 1 4 1 5 2 6 4 9 9h-1c-2 0-3 1-4 1l-9 1c-1-1-2-1-3-1-1-1-1 0-1-2 3 0 4 0 5-2h1l-1-1h-7c-2 0-3 0-4-1h2l-2-2v2h-2-1-10l-1 1c1 0 2 0 4 1-6 0-14 2-20-1-6-2-10-8-12-13h2v1c1 2 1 3 4 4l1 1-3-6c-4-4-4-11-4-17 1-6 3-12 8-17 2-2 4-3 6-5 1-1 2-1 4-1l2-1c1-1 3-1 4-2 3-1 7-1 11-2z" class="E"></path><path d="M843 612h1 1c1 1 2 1 3 1 0 1 0 1 1 1l1 1h-7v-3z" class="B"></path><path d="M821 559c3-1 7-1 11-2l-2 2h0 1c1 1 5 1 7 1l-3 1h-6l-2-1c-2-1-8 1-10 1 1-1 3-1 4-2z" class="Y"></path><path d="M802 579h0c1-5 3-8 7-11v2c-5 6-6 11-7 18h-1c1-2 0-3 1-4v-1-4z" class="W"></path><path d="M817 561c2 0 8-2 10-1l2 1 1 2v1l-1-1c-1-1-2-1-4-1h0-1c-6 0-7 1-12 5l-2 2-1 1v-2l6-6 2-1z" class="G"></path><path d="M802 579v4 1c-1 1 0 2-1 4h1c0 2 0 5 1 7 1 4 2 7 4 12-2-2-5-7-6-10h0c0-2-1-3-1-4v-4l2-10z" class="a"></path><path d="M797 585l1 1v2s1 1 2 1v4c0 1 1 2 1 4h0c1 3 4 8 6 10l3 4h-1c-2 0-4-2-5-3l-3-6c-4-4-4-11-4-17z" class="P"></path><defs><linearGradient id="I" x1="812.379" y1="571.494" x2="795" y2="581.293" xlink:href="#B"><stop offset="0" stop-color="#737374"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#I)" d="M811 563c1-1 2-1 4-1l-6 6c-4 3-6 6-7 11h0l-2 10c-1 0-2-1-2-1v-2l-1-1c1-6 3-12 8-17 2-2 4-3 6-5z"></path><path d="M833 595c-1-1-1-6 0-7 1 2 0 6 1 8s2 5 2 7c1 2 3 4 4 6l1 1c0 1 1 1 2 2v3c-2 0-3 0-4-1h2l-2-2v2h-2-1-10-1c1-1 2-1 3-2h0l3-3 2-10v-4z" class="G"></path><path d="M833 599v5l1-1c0 2-1 4 1 6h-1-1-2l2-10z" class="Y"></path><path d="M835 609h0v3h1v-4h0c1 1 1 2 1 3s0 1 1 2h-2v1h-10-1c1-1 2-1 3-2h0l3-3h2 1 1z" class="f"></path><path d="M828 612l3-3h2 1v4c-1-1-1-1-1-2h-1c-1 1-2 1-4 1z" class="a"></path><path d="M833 595v4l-2 10-3 3h0c-1 1-2 1-3 2h1l-1 1c1 0 2 0 4 1-6 0-14 2-20-1-6-2-10-8-12-13h2v1c1 2 1 3 4 4l1 1c1 1 3 3 5 3h1c1 1 2 1 4 2 5 0 10-1 14-4s4-9 5-14z" class="N"></path><path d="M809 582v-2c2-6 5-12 10-14 2-1 3-1 5-1l2 2c1 1 1 10 1 13v4 1 2c-1 5 2 15-3 19-1 1-2 1-3 1-4-1-8-5-10-9l-2-3c0-2 0-4-1-6 0-2 0-5 1-7z" class="M"></path><path d="M809 582h0 3 3-1v3 1l-1-1-1 1h-2v1 1h-1c1 1 1 2 2 2v4c-1 0-1 1-2 1 0-2 0-4-1-6 0-2 0-5 1-7z" class="e"></path><path d="M818 586h8c0 2 0 5-1 7h-5c-2-2-2-4-3-6l1-1z" class="C"></path><path d="M842 605c-1-1-1-2-2-4-1-3-3-13-1-16-1-1-1-1-1-2 0-4 0-13 4-16 1-2 3-3 5-3 4 0 8 3 11 5 2 3 3 4 4 7 3 7 3 18 0 25-1 2-2 4-4 5-3 2-5 2-8 3v-1c-4 0-5 0-8-3z" class="M"></path><path d="M842 605c2 0 3 1 5 1h2c0 1 1 2 1 2-4 0-5 0-8-3z" class="F"></path><path d="M843 589v-2c2-1 4 0 6 0 0 0 1 0 1 2 0 1 0 3-1 4s-2 0-3 0-2 0-2-1c-1-1-1-2-1-3z" class="G"></path><path d="M843 589c1-1 2-1 3-1l1 1c0 1-1 2-1 3h-1-1c-1-1-1-2-1-3z" class="a"></path><path d="M858 569c2 3 3 4 4 7 3 7 3 18 0 25-1 2-2 4-4 5l-1-1-1 1h-1l2-2 1-1c3-1 3-6 3-8l1-2c0-2 1-11 0-12l-2-2h1c0-2-1-4-2-5-1-2-1-3-1-5z" class="e"></path><path d="M523 943c1 0 2-1 3-1h2l1-1c0 1 1 0 1 1l-3 1h1c1 0 3 1 4 0l1-1h5c2-1 3-2 5-3h0l1-1c1 0 2-1 2-2h-1c-1 1-3 2-5 3l-3 2v1l-1-1c0-1 0-1 1-2h1c1-1 2-1 4-2-2-1-5 0-7 0h3c2-1 6-1 8-3l4-6h1 0c0 2-1 3-2 5h1c1-2 1-2 3-3h16 29 8 25 0 7l35-1h67l44 2h10v8l1 4-2-1c-5 3-15 2-21 2h-56c-12 0-24 1-35 0-4 0-24-1-26 1l-2 2c-1-1-1 0-2-1 0-1 0-1-1-1l-37-1c0 4 1 10 0 14-2-3-1-10-1-13h-51-25 0c-5 0-8-1-13-2z" class="J"></path><path d="M638 930l35-1c-1 0-2 0-3 1h1l-2 1h0l-1 2c-2 0-2-1-3-2-4-1-7-1-11-1-7 0-15 1-23 0h0 7z" class="B"></path><path d="M650 943h2c1-1-1-2 2-2 0 1 0 2 1 2 1 1 4 0 6 0l20 1h0c-4 0-24-1-26 1l-2 2c-1-1-1 0-2-1 0-1 0-1-1-1v-2z" class="Y"></path><path d="M681 944l69-1h33c3 0 6 0 9-1l1-1v1c-5 3-15 2-21 2h-56c-12 0-24 1-35 0h0z" class="S"></path><path d="M671 930h31c6 0 12 0 18 1l67 1h3v1 1c-2-1-4-1-6-2h-15-20-22l-18-1h-40 0l2-1z" class="G"></path><path d="M523 943c1 0 2-1 3-1h2l1-1c0 1 1 0 1 1l-3 1h1c1 0 3 1 4 0l1-1h5c2-1 3-2 5-3h0l1-1c1 0 2-1 2-2h-1c-1 1-3 2-5 3l-3 2v1l-1-1c0-1 0-1 1-2h1c1-1 2-1 4-2-2-1-5 0-7 0h3c2-1 6-1 8-3l4-6h1 0c0 2-1 3-2 5h1l-1 2c3 1 7 0 11 0h18 90-56-12c-7 0-49 0-52 1-2 1-5 4-7 6-2 1-4 1-5 3-5 0-8-1-13-2z" class="F"></path><path d="M740 929l44 2c1 0 2 0 3 1l-67-1c-6-1-12-1-18-1h-31-1c1-1 2-1 3-1h67zm-140 6h12l1 8h24 13v2l-37-1c0 4 1 10 0 14-2-3-1-10-1-13h-51-25 0c1-2 3-2 5-3 2-2 5-5 7-6 3-1 45-1 52-1z" class="n"></path><defs><linearGradient id="J" x1="566.494" y1="933.339" x2="574.006" y2="953.161" xlink:href="#B"><stop offset="0" stop-color="#94968e"></stop><stop offset="1" stop-color="#c1bbc4"></stop></linearGradient></defs><path fill="url(#J)" d="M600 935h12l1 8h24 13v2l-37-1c0 4 1 10 0 14-2-3-1-10-1-13h-51-25 0c1-2 3-2 5-3 2 1 14 0 18 0l53 1v-7h-12v-1z"></path><path d="M821 463c3-2 7-3 10-5 4-2 8-5 12-8l6-6c2-2 5-3 7-5l1 1c-3 2-7 5-8 8 1 2 1 5 4 6 1 1 3 1 5 1h9 41c3 0 10-1 13 0 2 1 3 1 5 0h0c-1 1-3 1-5 1h-12v7l19 1c-1 0-2 1-4 1h-14c-1 1-1 2-1 4h6c4 0 8 0 11 1h0c-5 1-11 1-17 1v11h11v-9c-1-1-1 0-2-1h0 3l1 1h0v9h0c1 0 0 0 1 1-4 0-10 1-14 0v3l-74 1v1h-1-2c-1 0-1 0-2 1v8c1 1 2 1 2 1 0 1-1 1-2 1v1l1 8 1 1 1-1-1 4c-1 3-1 6-1 9-1 0-2 0-4 1 1-3 0-5 0-8v-2l-2-5c-1 2-3 3-4 4l-1 11h-9c0-3-1-6-1-9v-7h-1c1 0 1-1 1-1-1-2-1-6-1-7v-3c-1-2-1-4-1-5 1-1 1-2 1-3 1-2 0-3 0-4v-1l1-1c-1-1-1-1-1-2l1-1c-1-1-1-4-1-5v-1l-1 1-2-2v-2h2v-1-2s0-1 1-2h0l1-1s1 0 0 1v1h4c1-1 0-1 2-1h2c1-1 2-1 3-1z" class="P"></path><path d="M849 460c3 0 5 1 7 2v1h0 2c-2 0-7 1-9 0v-3h0z" class="B"></path><path d="M900 456h8v7h-8v-7z" class="J"></path><path d="M821 463c3-1 5-2 8-1l2 1 8 3v2h-2-6c-1-1-2-1-3-2s-2-1-3-1h-1c-1-1-1-1-2-1l-1-1z" class="E"></path><path d="M837 458h3 0c1-1 1-2 1-2 1 0 2 1 3 0 0-1 0-1 1-2 1 2 4 4 4 6h0l-1 2v1h-1-6l-1-1h-1c-1-1-2-1-3-1s-1 1-2 1l-2-1 1-1c1 0 3-1 4-2z" class="C"></path><path d="M836 461c2-2 4-1 7-1h1c1 1 2 1 3 3h0-6l-1-1h-1c-1-1-2-1-3-1z" class="B"></path><path d="M808 469v-1-2s0-1 1-2h0l1-1s1 0 0 1v1h4c1-1 0-1 2-1h2c1-1 2-1 3-1h0l1 1c1 0 1 0 2 1h1c1 0 2 0 3 1s2 1 3 2h6l-14 1v1h-9-1-2-2l-1-1z" class="C"></path><defs><linearGradient id="K" x1="845.114" y1="454.533" x2="857.803" y2="473.865" xlink:href="#B"><stop offset="0" stop-color="#e1d9da"></stop><stop offset="1" stop-color="#f0f4f5"></stop></linearGradient></defs><path fill="url(#K)" d="M836 461c1 0 2 0 3 1h1l1 1c3 2 7 1 11 1h16v5l-29-1v-2-1l-5-3c1 0 1-1 2-1z"></path><path d="M837 458c3-2 5-5 8-6 1 1 3 3 5 4v-1h-1c-1-1-2-2-3-4h0c2 1 5 4 7 5h4 11v7h-10-2 0v-1c-2-1-4-2-7-2 0-2-3-4-4-6-1 1-1 1-1 2-1 1-2 0-3 0 0 0 0 1-1 2h0-3zm31 6l40 1v4h-40v-5z" class="E"></path><path d="M848 472h1c6 1 13 0 19 1v9h-9v-8h-1v8h-10v-3-2c1-1 0-4 0-5zm22-16h29v7h-29v-7zm0 14h13l25 1v11h-38c0-3-1-8 0-10v-2z" class="J"></path><path d="M809 506c1 0 1-1 1-1-1-2-1-6-1-7v-3c-1-2-1-4-1-5 1-1 1-2 1-3 1-2 0-3 0-4v-1l1-1c-1-1-1-1-1-2l1-1c-1-1-1-4-1-5v-1l-1 1-2-2v-2h2l1 1h2 2 1 9 3c4-1 9 0 13 0h29v1c-2 1-34-1-39 0l1 1h2 15 1c0 1 1 4 0 5v2 3l-1 1h5 11c4-1 8 0 11 0h20c5 0 11 1 15 0v3l-74 1v1h-1-2c-1 0-1 0-2 1v8c1 1 2 1 2 1 0 1-1 1-2 1v1l1 8 1 1 1-1-1 4c-1 3-1 6-1 9-1 0-2 0-4 1 1-3 0-5 0-8v-2l-2-5c-1 2-3 3-4 4l-1 11h-9c0-3-1-6-1-9v-7h-1z" class="E"></path><path d="M832 472h-8l-1 7v-2h-1v4c2 2 8 0 10 1v1c-5 0-11 1-15-1h0c1-1 3-1 4-1 0-3 0-6 1-9-3 0-9 1-11 0 1-1 5-1 7-1h11l1 1h2z" class="C"></path><path d="M821 511l-1-1v-1c-1-1-1-3-1-4-1-4 0-9 1-13l1 1c0 1 0 3-1 5h1 2v-2l-1-1c0-1 0-4 1-5 1 1 0 2 0 4 1 4 1 9 2 13-1 2-3 3-4 4z" class="B"></path><path d="M827 512l2 1v-1-17l1-7c-3 0-12 1-14 0 1-1 8-1 10-1 1-1 6 0 9 0v1h-1-2c-1 0-1 0-2 1v8c1 1 2 1 2 1 0 1-1 1-2 1v1l1 8 1 1 1-1-1 4c-1 3-1 6-1 9-1 0-2 0-4 1 1-3 0-5 0-8v-2z" class="C"></path><path d="M522 786v65c1-2 1-6 1-9h3l2 1 9 6c12 11 20 24 20 41 1 10 1 22-2 32v1l-2 5v1h1l-1 1c-2 1-2 1-3 3h-1c1-2 2-3 2-5h0-1l-4 6c-2 2-6 2-8 3h-3c2 0 5-1 7 0-2 1-3 1-4 2h-1c-1 1-1 1-1 2l1 1v-1l3-2c2-1 4-2 5-3h1c0 1-1 2-2 2l-1 1h0c-2 1-3 2-5 3h-5l-1 1c-1 1-3 0-4 0h-1l3-1c0-1-1 0-1-1l-1 1h-2c-1 0-2 1-3 1 5 1 8 2 13 2h0 25 51c0 3-1 10 1 13h0c-1 1-2 1-3 2 1 1 1 2 1 4h-1c-2 1-7 0-10 0h-28-41c-10 1-21 1-32-1-4 0-9-2-13-4-2-1-3-2-5-3l-1-1c-1 0-4-3-5-4h0c-1-3-5-4-6-8h0v-2c-2-2-3-5-5-7l-1-3h-1c-1-1-1-1-1-3-1-2-1-3-1-5-1 0-1-1-1-1 1-1 2-1 3-1 2-1 2-1 3-1v-1h-1c-2 0-3 1-6 1l-1-5h-1v-2c-1-2 0-17 0-20 1-1 0-2 0-3 1-2 1-3 1-4v-2c1-2 1-4 1-6v-1-1h1v-1-2-1c1-2 1-4 3-6 0-1 1-2 1-3l1-1 1-2c0-1 1-2 1-2l1-1c0-1 1-1 1-2l1-1c1-1 1-2 2-3 1 1 1 2 0 2v1l1 1v-1l1-1 7-8h0c1-3 2-4 4-5l2-2c-1-1-1-1-2-1 1 0 1 0 2-1v-7-18c0-2 0-6 1-9v-1c-1 0-1-1-2-1l2-1h2c1 0 2-1 2-1 2 0 3-1 5-2h0l3-1 1-1c1 0 2 0 3-1 1 2 1 12 1 15v38c0 8-1 16 0 23l3 3c2 0 3 0 4-1 1-2 2-3 2-5 1-2 0-6 0-8v-17-52c1 0 2 0 3-1 1 0 2-1 3-1s2-1 3-1z" class="J"></path><path d="M495 920c1 0 0 0 1 1 0 6 1 11 2 17 0-1-1-3-1-5h0c-1-4-1-7-1-10 0-1-1-1-2-2l1-1z" class="Q"></path><path d="M485 800l2-1h2c1 2 1 4 1 6v12s-1 1-1 2v17l1 17-2 2-1-1c2-2 2-8 1-11v-1-3c-3 1-6 4-8 6h0c1-3 2-4 4-5l2-2c-1-1-1-1-2-1 1 0 1 0 2-1v-7-18c0-2 0-6 1-9v-1c-1 0-1-1-2-1z" class="Y"></path><path d="M522 851c1-2 1-6 1-9h3l2 1 9 6c12 11 20 24 20 41 1 10 1 22-2 32v1l-2 5v1h1l-1 1c-2 1-2 1-3 3h-1c1-2 2-3 2-5h0-1l-4 6c-2 2-6 2-8 3h-3c2 0 5-1 7 0-2 1-3 1-4 2h-1c-1 1-1 1-1 2l1 1v-1l3-2c2-1 4-2 5-3h1c0 1-1 2-2 2l-1 1h0c-2 1-3 2-5 3h-5l-1 1c-1 1-3 0-4 0h-1l3-1c0-1-1 0-1-1l-1 1h-2c-1 0-2 1-3 1-4-2-7-5-10-9-2-5-3-11-1-16 1-5 3-8 7-10s10-2 15 0c1 2 1 8 0 10-1 1-2 2-3 2-3 0-4-2-5-3-1 0-2 0-3 1-1 0-3 1-3 3-1 2 0 5 1 6 2 2 4 3 6 4 4 0 7-1 10-4 8-7 9-17 10-27 0-14-2-24-10-36-5-5-8-8-15-11v-2z" class="M"></path><path d="M537 864h0c0-1 0-1-1-2l1-1 6 9c3 4 6 14 5 19v-1c-2 3 0 9-1 12 0-14-2-24-10-36z" class="F"></path><path d="M449 652h47v1h17-1 10 1c2 0 4 2 6 4s4 4 6 7c1 1 2 1 3 3 1 1 3 3 4 5 1-1 3-2 5-3s3-1 4-2c7-3 10-8 14-14l2-1h44l6 14c2 4 4 9 5 13l6 15c1 1 2 3 2 5 0 1 1 2 1 3 1 1 2 3 2 5 1 3 3 7 4 11l1 1v2c1 1 1 1 1 2l3 6 2 5c0 1 1 3 1 4l2 5c1 1 2 3 2 4 1 3 2 5 3 7 0 1 1 2 1 3l1 3 1 1c0 1 1 2 1 4v2l1 1v2c0 1 1 7 0 8 0 1 0 1-1 3v2h0c-1 1-1 2-2 3h0c-1 2-5 6-7 6v1c-2 1-3 1-5 2h-1c-2 1-5 0-7 0h-1c-1 0-1 0-2-1h0c-1 0-2 0-2-1h-1c-4-2-9-6-11-10h0c-2-2-2-3-2-6h1v-3-4l-2-2-1 1h-1-2v8l3-2 1 1v-3h1v1h0v3l-1 1c0 1 0 1 1 2l1 4c0 2 0 22-1 24-1 1-1 1-2 1h-2l-2-1v-6-14c0-1 1-4 0-5v-5c-1-1-1-1-1-3l-2 2h0-1c-1 1-1 1-2 1s-1 0-2 1h-2s-1 1-2 1h-3c0 1-2 1-2 1l-1 1c-1-2 0-4 0-5l-1-1c-1-2 0-7 0-9h-9-1-1c-2 0-2 0-3 1l-2 2v2 10h-6-3 2c3 1 5 1 7 1v12c0 2 1 4 0 6h-1c-1 0-2 1-2 2 1 1 2 1 2 3v3 2h-1v2 1l-2 5-1 2c-1 1-2 3-3 5h-1c0 1-1 2-2 3 0 1-1 1-1 2-2 1 0-1-1 1s-5 5-7 6c-1 1-2 2-3 2l-1 1c0 1-1 1-1 2l-3 1c-1 1-4 2-6 3l-2 1-1 1-9-6-2-1h-3c0 3 0 7-1 9v-65c-1 0-2 1-3 1s-2 1-3 1c-1 1-2 1-3 1v52 17c0 2 1 6 0 8 0 2-1 3-2 5-1 1-2 1-4 1l-3-3c-1-7 0-15 0-23v-38c0-3 0-13-1-15-1 1-2 1-3 1l-1 1-3 1h0c-2 1-3 2-5 2 0 0-1 1-2 1h-2l-2 1-3 2h0l-3 2h0c-2 1-3 1-4 2l-9 4c-1 1-2 1-3 2l-12 6-7 4h-1 0c-1-3-3-6-4-8l-1-6h-1v-2c-1-1-1-1-1-2v-3c-1-1-1-2-1-3v-4c-1-1-1-2-1-3-1-2 0-4 0-5l2-1h0 0c-2-2-4-1-6-1-4 0-7 1-10 0-2-1-1-2-1-3v-12-12c0-2 0-4-1-6v-1c-1 0-3 1-4 0h-3c-3 0-5 1-8 1h-1c-2 0-6 1-8 0h-2c-1 0-2-1-3-1-1-1-2 0-3 0s0 1-1 3h0v-1-9c-2-2-4-1-6-1v-1h5v-1c1-4 2-13 1-16h-4l2-1-4-1-2-1h1c-1-2-1-4-1-6h-1c0-1 0-2 1-3v-1l-1 1c2-9 6-17 9-25l1-1c1-3 3-3 6-4l-1-1h3l10-1-1-4h0v-2c-1-3-1-6-1-9l2-2v-1l1-2 3 3 1-1c-1-1-2-3-3-3h-1c-2-2-4-2-6-3v-1c0-1 0 1 0-1 8-2 17-1 26-1h5c1 1 0 1 1 1 0 1 0 2 1 2 1 3 2 6 4 9 1 4 3 7 6 10 1-1 3-1 5-2h2c2-1 1-1 3-1l1 2 1-1v-1c0-1 1-2 1-2v-1l1-1c-3-5-5-10-8-15l1-1z" class="E"></path><path d="M491 718h0c4 1 7 0 10 1h-2c-2 0-6 0-8-1z" class="B"></path><path d="M555 750c1 0 3 2 4 3v1h-2c-2-1-1-2-2-4z" class="D"></path><path d="M562 717l1 1c2 1 6 1 8 1l2-1 1 1c-4 1-9-1-11 1l-1-3zm66 48c-3-2-5-3-8-3-2-1-5-1-7-1h-1l-1 1h0l-1-1 1-1h3c3 1 5 1 8 1l1 1c2 0 3 0 5 2v1z" class="B"></path><path d="M553 674c1-1 1-1 3-1v2c-2 0-4 3-7 5v-3l1-1 3-2z" class="m"></path><path d="M595 735c2 0 5 0 7 1l3-1h0c0 1-1 1-2 1s-1 1-2 1c-2 1-4 1-6 1v-3z" class="H"></path><path d="M580 750c2 0 1-1 2-2-1-1 0-1-1-1h3v2l1-1 1 1-1 1h4l-1 1c-2 0-5 1-7 0h-1v-1z" class="Z"></path><path d="M590 727c1-1 0-6 2-8h2c0 2-1 6 0 7 0 0 1-1 1 1h0c-2-1-3 0-5 0zm-160-26l2-1v4c2 1 3 0 4 1v1h-4c-1 1-1 4-1 6v2c0-3 0-6-1-8l-2-1h1c1-1 1-3 1-4z" class="U"></path><path d="M562 712l1 2c-1 1-1 2-1 3l1 3c-2 0-3 0-4-1l-1-1-1-1h0l2-2h1c0-1 0-1 1-1s1-1 1-2z" class="C"></path><path d="M578 735h12c0 2-1 2 0 4-5-1-8-2-12-4z" class="K"></path><path d="M431 717l30 1v1c-4-2-11-1-16-1-4 0-9-1-13 0-1 1-1 3-1 4 0 0 0-3-1-4h-11 0l12-1h0z" class="R"></path><path d="M531 695c-1-1-3-2-3-3h1 0c1 1 3 1 4 1 6 0 9-1 14-2v1l-6 3c-4 0-7-1-10 0z" class="a"></path><path d="M609 682v3c-2 3-6 5-8 8h-2-1l1-1v-1c2-4 7-6 10-9z" class="C"></path><path d="M527 822l1 21-2-1v-14c0-1-1-3 0-4 0-1 1-2 1-2z" class="Z"></path><path d="M465 777h-2l-1-1c0-1 0-1 1-2s2-1 4-1l2 2v2h-2c-1 5 0 9 0 14 0 1 0 2 1 3-2 1-4 1-6 1h0l4-1c0-5 1-12 0-17h-1z" class="H"></path><path d="M431 684l1 12c1 1 1 1 2 1l2 3h-1c-1 0-1-1-3 0l-2 1c-1-1-1-3-2-4h0l2-1v-11l1-1z" class="F"></path><path d="M593 735h2v3 1c0 2 1 4 0 5-2 1-3 1-4 0 0-1 0-4-1-5-1-2 0-2 0-4h1 2z" class="I"></path><path d="M580 751h-5c-2-1-7-1-9 1 1 0 1 0 1 1h1l1 1 1 1c1 0 1 1 1 1l-1 1c-1-1-1-1-2-1v-1l-1 1-1-1c-1-2-4-4-5-6h10c3 0 6 0 9 1v1z" class="Q"></path><path d="M483 783v3c-4 4-10 7-15 8-1-1-1-2-1-3 6-2 10-4 16-8z" class="a"></path><path d="M538 720h1v4l1 1-1 1 1 13 4-4 1 1c-1 2-4 3-5 4v16 10h-1v-7c-1-7-1-15-1-22v-17z" class="C"></path><path d="M628 764c2-1 4-3 7-3 1-1 1-1 2-1-1 4-2 7-2 11 0 1 0 1-1 1h0c-2-3-6-4-6-7v-1z" class="l"></path><path d="M604 655c2 0 4 0 5 2 1 1 1 2 2 4l4 12 6 14s-1 1-1 2l-1-2c-1-3-4-6-7-7 1 0 2 0 3 1 1 0 1 0 2 1v-1-2c-1-1-1-1-1-2-1-2-1-5-3-7-1-1-2-3-3-4 0-2 0-3-1-4-2-3-3-5-5-7zm-57 37l2-1h1c-1 2-2 2-3 3-2 1-3 2-5 3l-1 1c-2 1-2 1-4 3l-6 2c2-3 4-3 7-5l-1-1c-1 1-2 0-3 0h0c-2 0-2-1-3-1v-1c3-1 6 0 10 0l6-3z" class="C"></path><path d="M611 723l-1-1c1-2 1-4 1-7 2 0 3 3 5 3 2-2 3-3 4-6h0 1v1c0 3-1 5-2 8v3c-1 0-2 0-3-1h-5z" class="T"></path><path d="M616 723c-1-1-1-2-2-3h2 1v1c0 1 0 1-1 2z" class="H"></path><path d="M465 777h1c1 5 0 12 0 17l-4 1h0c-1 1-9 1-11 1 1-1 4-2 6-3s4-3 6-4h1l1-10v-1-1z" class="d"></path><path d="M485 778c1-1 1-3 2-4s1-5 1-6v-2c0-4-1-14 1-17v13c1 5 1 9 1 14 0 2 0 4-1 6l-6 4v-3c1-1 2-3 2-5z" class="W"></path><path d="M575 694h1l1-2 1 2c0 3 1 5 2 8l1-1h0l1-1v3h1c-2 1-3 2-4 4v2c0 2-2 5-3 7 0 1 0 1-1 2v-5-3c0-1 0-2 1-3 0-1-1-2 0-4h1c0-2-2-7-3-9h1z" class="C"></path><path d="M404 683h2c2-1 4-1 5 0v12c-1 2 0 3-1 5-1-1-1-2-1-4h0-2-2 0c1-2 1-7 1-9 0-1-1-1-1-2v-1l-1 1c0 1 0 1-1 2 0-2 0-3 1-4z" class="B"></path><defs><linearGradient id="L" x1="556.497" y1="682.291" x2="558.029" y2="688.82" xlink:href="#B"><stop offset="0" stop-color="#a5a6a0"></stop><stop offset="1" stop-color="#b5b3b9"></stop></linearGradient></defs><path fill="url(#L)" d="M561 679h1c0 1 1 2 2 3-2 2-4 5-7 7-2 1-5 3-7 5-1 0-1 1-2 2-2 2-8 5-10 5h-1c2-2 2-2 4-3l1-1c2-1 3-2 5-3 1-1 2-1 3-3h-1l-2 1v-1l2-1v-2c1 1 1 1 2 1l10-9h1l-1-1z"></path><path d="M484 718c1 1 3 1 4 2v20h-8 0 4c0-5 1-13-1-17 0-1 0-1 1-2h0v-3z" class="G"></path><path d="M590 727c2 0 3-1 5 0 1 1 1 1 3 2h5 2v2c2 0 2 0 4-1h1c-3 3-7 3-11 3-2 1-4 0-6 2h0-2l-1-1v-3l1-1c-1-1-2-2-4-2l3-1z" class="O"></path><path d="M590 727c2 0 3-1 5 0 1 1 1 1 3 2h5c-2 1-3 1-5 1v-1h-3c-1 1-2 1-4 1h0c-1-1-2-2-4-2l3-1z" class="I"></path><path d="M422 682h-2c0 1 1 5 0 6h0v-15c0-5-1-8 1-13 2-3 5-3 8-4h1v1c-1 1-1 2-2 2 0-1 0 0-1-1h-1v2c1 1 1 1 1 3 0 3 4 8 6 11 0 1 0 1 1 2l-2 2c-1-3-8-15-10-16-2 3-1 7-1 11-1 2-1 5-1 7l1 1 1 1z" class="X"></path><path d="M472 750c-1 0-2 0-3-1v-1c1 0 3 1 3 0h1c1 0 1 1 2 1h13 1c-2 3-1 13-1 17v2c0 1 0 5-1 6s-1 3-2 4v-28h-13z" class="a"></path><path d="M402 692c0 1 0 2 1 3h1c-1 1-1 1-3 1s-3-2-5-1h0l-1 1h1 0c1 1 2 1 3 3 0 1 0 2 1 3l-1 1v1l-1 1v-4c-2 0-2 0-3 1h-1c0 1 0 1-1 1v-1c-1 1-2 1-2 3-1 1-1 3-2 4-1 2-1 3-2 4 0-5 2-9 2-14v-1c2-1 2-4 3-5h3c2-1 4-1 7-1zm19-11l-1-1c0-2 0-5 1-7 0-4-1-8 1-11 2 1 9 13 10 16-1 1-3 1-5 2h-1c-2 1-3 1-5 1z" class="B"></path><path d="M488 740c1-2 1-6 1-8v8c0 2 1 3 0 4h-15-7v4c1 1 1 0 1 1h-1c0-2 0-3-1-4v-1l-18-1 1-2h3c4 0 12 1 15-1v-2c0 1 0 1 1 2 2 1 5 1 6 0v-3c0 2 0 2 1 3h5 8z" class="b"></path><defs><linearGradient id="M" x1="471.657" y1="704.825" x2="466.013" y2="712.102" xlink:href="#B"><stop offset="0" stop-color="#080707"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#M)" d="M443 690c3 2 5 5 8 7 7 7 15 12 25 16l12 3 1 1v2h0v21-8c0 2 0 6-1 8v-20c-1-1-3-1-4-2-7-2-13-4-19-7-5-3-8-4-12-8-2-5-8-8-10-13z"></path><defs><linearGradient id="N" x1="410.301" y1="666.145" x2="401.98" y2="677.523" xlink:href="#B"><stop offset="0" stop-color="#cacaca"></stop><stop offset="1" stop-color="#f5f4f4"></stop></linearGradient></defs><path fill="url(#N)" d="M405 660l3 3 1-1c3 7 2 14 2 21-1-1-3-1-5 0h-2l2-2-2-1-1-4h0v-2c-1-3-1-6-1-9l2-2v-1l1-2z"></path><path d="M404 663c0 2 0 1 1 3s-1 6-1 8l-1 2h0v-2c-1-3-1-6-1-9l2-2z" class="D"></path><path d="M408 663l1-1c3 7 2 14 2 21-1-1-3-1-5 0h-2l2-2-2-1h6c0-6 0-12-2-17zm-4 54c1-1 2-1 4-1 0 1 0 1 1 2 0 1 0 1 1 2 0 1 0 1-1 2 3 2 24 1 28 1 2 1 4 0 6 1h-10c-1 1-1 2-1 4 1 1 2 0 3 1-1 1-2 1-4 2 0-1 0-1-1-1l-4-1h4c1-2 1-2 1-4-2-1-4-1-6-1-4 0-9-1-13 0h-2c-1 2 0 4-2 5-2 0-4-1-5-1h1v-2c-1-1-2-1-4-2h-15v12c1 2 0 3 1 5 2 0 2 0 3 2-1 0-2-1-2-1l-1 1c0 2-1 5 0 7-1 0 0 1-1 3h0v-1-9c-2-2-4-1-6-1v-1h5v-1c1-4 2-13 1-16h-4l2-1c4-1 17 1 20 0v-3l1-3z" class="F"></path><path d="M580 653h31l3 9c1 1 1 3 2 4h1c2 4 4 9 5 13l6 15c1 1 2 3 2 5-3-2-5-8-6-11l-1-3-1-3h-1s0 1 1 2v2c1 2 2 3 2 5h0l-3-4-6-14-4-12c-1-2-1-3-2-4-1-2-3-2-5-2l-2-1c-2-1-5 0-8 0l-14-1h0z" class="W"></path><path d="M601 665l1 1c1 2 2 4 2 6 1 2 5 6 7 7l1 1c3 1 6 4 7 7l-4-4c0 1-1 1-1 2h-1c-1-1-1 0-2-1v1 4l-1-2c-4 1-7 5-9 8-1 3-1 6 0 9 2-2 3-4 6-4 1 0 3 1 4 2s1 2 1 4c-1 1-2 2-3 2h0c1-2 1-3 1-5-1-1-1-1-3-2-2 0-3 0-4 2-1 0-1 1-1 2l1 2h0c-1-1-2-2-4-3 0-4 0-7 2-10v-1c2-3 6-5 8-8v-3c0-3-3-6-5-8l-12 11h0s-1 1-1 2l-1-2c0-2 1-4 2-6v-3h1v2c2-2 5-4 7-6h0 2v-1c0-2-2-4-4-5 2 0 2 0 3 1v-2z" class="Z"></path><path d="M611 684v-1l-1-3c2 1 3 2 5 3 0 1-1 1-1 2h-1c-1-1-1 0-2-1z" class="f"></path><path d="M600 672h0c0 2-2 3-4 5h0l6-4v1c-3 3-9 7-10 11 0 0-1 1-1 2l-1-2c0-2 1-4 2-6v-3h1v2c2-2 5-4 7-6z" class="D"></path><path d="M578 735l-1-1c-3-1-5 0-8-1h-3 0c-2-1-2-2-3-2s-1 0-2-1h0 2 0 2l-1-1h-1-1l-2-1c-1 0-1 0-2-1h1c1 0 2 0 4 1h2 5c2-1 4-1 6-1 4 0 8 0 11 1 2 0 3 1 4 2l-1 1v3l1 1h-1-12z" class="C"></path><path d="M590 734c-4-1-9 0-13-2h-3c-1 0-2-1-4-1h1c6 1 13 0 19 0v3z" class="V"></path><path d="M591 687s-1 0-1 1 0 5 1 6c0 1 1 1 2 1-1 3 0 6 0 8s0 5 1 7h0l-1 1-2-2c1-1 0-1 1-1h1c-1-1-1-2 0-3v-1l-3-2c-1-8-1-15-1-23 0-4-1-10 2-13 1-2 2-3 4-3 3 0 4 1 6 2v2c-1-1-1-1-3-1 2 1 4 3 4 5v1h-2 0c-2 2-5 4-7 6v-2h-1v3c-1 2-2 4-2 6l1 2z" class="k"></path><path d="M600 672h-8v-2c0-1 1-2 2-4h4c2 1 4 3 4 5v1h-2 0z" class="B"></path><path d="M522 786c2-2 5-3 8-4h0l-3 2v38s-1 1-1 2c-1 1 0 3 0 4v14h-3c0 3 0 7-1 9v-65z" class="K"></path><path d="M523 653c2 0 4 2 6 4s4 4 6 7c1 1 2 1 3 3 1 1 3 3 4 5-3 2-6 2-10 3h-2c-3 1-4 1-6 2l-1 1v10c-1-3 0-7 0-11v-24z" class="F"></path><path d="M529 659l4 3h-5c0-1 1-2 1-3z" class="L"></path><path d="M529 674c0-1-1-2-2-2v-5c-1-3-1-7 1-9l1 1c0 1-1 2-1 3 1 2 1 4 2 5v1h-2c0 1 0 1 1 2v4z" class="f"></path><path d="M533 662l2 2c1 1 2 1 3 3 1 1 3 3 4 5-3 2-6 2-10 3h-2l-1-1v-4c-1-1-1-1-1-2h2v-1c-1-1-1-3-2-5h5z" class="Y"></path><path d="M530 668l4 4h0-1c-1-1-1-1-2-1v1h0v2l1 1h-2l-1-1v-4c-1-1-1-1-1-2h2z" class="a"></path><defs><linearGradient id="O" x1="516.836" y1="724.985" x2="545.627" y2="752.351" xlink:href="#B"><stop offset="0" stop-color="#211f20"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#O)" d="M522 717c10-2 21-5 30-10l1 1-9 5 4 11h-1l-3-10c-6 2-12 4-17 5v38 17l-5 2v-59z"></path><path d="M569 654c2 0 3 0 5 1 0 0 2 1 3 1 2 1 3 3 4 5s1 5 1 7c1 9 0 19 1 28 0 3 1 10 0 12h0v-3-2h-1v-3l-1 1h0l-1 1c-1-3-2-5-2-8l-1-2-1 2h-1-1c-1-1-1-2-1-3-2-1-3-2-5-2v-1l-1-1 8-11c1-3 3-5 3-8 0-2 0-4-1-6-2-2-5-4-8-4l-1-1 1-3z" class="G"></path><path d="M578 675c0-1 0-1 1-2 1 2 0 17 0 21h-1v-19z" class="B"></path><path d="M576 679l1-4h1v19h0l-1-2c0-2 0-4-1-6h-1v-1l-1-1c1-2 1-3 2-4v-1z" class="W"></path><path d="M574 684c1-2 1-3 2-4v6h-1v-1l-1-1z" class="D"></path><path d="M581 701c-1-3-2-5-1-8 1-5 0-12 0-17 0-2 0-4 1-6v2c1 3 0 7 0 11l1 17-1 1z" class="Q"></path><path d="M575 676c1 1 1 2 1 3v1c-1 1-1 2-2 4l1 1v1h1c1 2 1 4 1 6l-1 2h-1-1c-1-1-1-2-1-3-2-1-3-2-5-2v-1l-1-1 8-11z" class="B"></path><path d="M574 684l1 1v1h1c1 2 1 4 1 6l-1 2h-1v-1-1-3c-1-2-1-3-1-5z" class="G"></path><path d="M621 687l3 4h0c0-2-1-3-2-5v-2c-1-1-1-2-1-2h1l1 3 1 3c1 3 3 9 6 11 0 1 1 2 1 3 1 1 2 3 2 5 1 3 3 7 4 11l1 1v2c1 1 1 1 1 2l3 6 2 5c0 1 1 3 1 4l2 5c1 1 2 3 2 4 1 3 2 5 3 7-1 0-2-1-2-2-1-1-1-2-2-3h-1l1-1c0-2-1-4-2-6l-1-2c0-1-1 0-2-1 0 0-1-2-2-3s-2-2-4-3l-3-1-2-1c-2 0-3-1-4-1v1h-1v-2h-2-3c-1-1-2-1-3-1h-1c1-1 3-1 5-1 0 0 1 0 1 1 1-1 2-1 3-1l1 1v-2c-2-2-5-2-8-1h-2c-1 0-1-1-3-1-1 0-3 0-4-1h5c1 1 2 1 3 1v-3c1-3 2-5 2-8 1 1 1 3 1 4s-1 2-1 2h1c1 1 1 0 2 1 1 0 5 4 6 5v-9-4c-1-1-1-2-1-3 0 0 0-1-1-2l-1-4v-1c-1-2 0 0-1-1l-1-2v-2l-1-2-1-2c-1-1-2-3-3-4 0-1 1-2 1-2z" class="B"></path><path d="M638 721c1 1 1 1 1 2l3 6 2 5c0 1 1 3 1 4l2 5c1 1 2 3 2 4 1 3 2 5 3 7-1 0-2-1-2-2-1-1-1-2-2-3h-1l1-1c0-2-1-4-2-6l-1-2c0-1-1 0-2-1 0 0-1-2-2-3s-2-2-4-3l-3-1-2-1c-2 0-3-1-4-1v-1h2c1 1 2 1 3 1l1-1-3-1v-1l4 1c2 0 3 0 5 1-3-2-5-3-8-4h0l-1-1c2 0 5 2 7 3h1c0-1 0-3-1-4v-1-1z" class="Y"></path><path d="M634 729c1 0 3 0 5 1 0 1 1 2 1 3h-3l-3-1-2-1c-2 0-3-1-4-1v-1h2c1 1 2 1 3 1l1-1z" class="D"></path><path d="M567 652h44l6 14h-1c-1-1-1-3-2-4l-3-9h-31 0l-3 1v2c-1 0-3-1-3-1-2-1-3-1-5-1l-1 3-3 3c-5 5-9 8-15 12 1 1 2 1 3 2l-3 2-1 1v3c-3 2-7 5-11 6-5 3-8 6-15 4v-2-10l1-1c2-1 3-1 6-2h2c4-1 7-1 10-3 1-1 3-2 5-3s3-1 4-2c7-3 10-8 14-14l2-1z" class="b"></path><path d="M569 654v-1c3-1 8 0 11 0h0l-3 1v2c-1 0-3-1-3-1-2-1-3-1-5-1z" class="C"></path><path d="M544 675c0 2 0 3 1 4l-5 3-2-4 6-3z" class="G"></path><path d="M536 684c-2 1-4 2-7 1l-1-1c0-1-1-2 0-4h1c2 2 5 3 7 4h0z" class="g"></path><path d="M545 679l4-2v3c-3 2-7 5-11 6 2-2 5-2 7-5-3 1-5 3-8 3 1-1 2-2 3-2l5-3z" class="H"></path><path d="M550 672c1 1 2 1 3 2l-3 2-1 1-4 2c-1-1-1-2-1-4l3-1 3-2z" class="B"></path><path d="M550 672c1 1 2 1 3 2l-3 2v-1c-1-1-2-1-3-1l3-2z" class="D"></path><path d="M529 680c4 0 6-1 9-2l2 4c-1 0-2 1-3 2h-1 0c-2-1-5-2-7-4z" class="d"></path><path d="M404 680l2 1-2 2c-1 1-1 2-1 4v8c-1-1-1-2-1-3-3 0-5 0-7 1h-3c-1 1-1 4-3 5v1c0 5-2 9-2 14l-2 6 1 1c5-1 11-1 16-1l2-2-1 3v3c-3 1-16-1-20 0l-4-1-2-1h1c-1-2-1-4-1-6h-1c0-1 0-2 1-3v-1l-1 1c2-9 6-17 9-25l1-1c1-3 3-3 6-4l-1-1h3l10-1z" class="L"></path><path d="M386 704c1 2-1 9-1 12l-1 2c0 1 1 1 1 1l1 1h-1c-1 0-3 1-4 0 3-3 4-12 5-16z" class="C"></path><path d="M389 698v-1c0-3 0-4 2-6l1 1 1-1h3c1-1 2-1 3-1h1 2v2c-3 0-5 0-7 1h-3c-1 1-1 4-3 5z" class="D"></path><path d="M386 704c0-2 1-6 2-7v2h1c0 5-2 9-2 14l-2 6s-1 0-1-1l1-2c0-3 2-10 1-12z" class="S"></path><path d="M379 722c2-2 5-1 7-1 6-2 12-2 17-1v3c-3 1-16-1-20 0l-4-1z" class="C"></path><path d="M404 680l2 1-2 2c-1 1-1 2-1 4v-1c-2 0-3 0-4 1h-2-2c-1 1-2 1-2 1-2 1-3 1-5 2v-1l-3 3c0-2 0-3 1-4l-1-1 1-1c1-3 3-3 6-4l-1-1h3l10-1z" class="F"></path><path d="M392 682h4 0l-10 6-1-1 1-1c1-3 3-3 6-4z" class="G"></path><path d="M404 680l2 1-2 2c-1 1-1 2-1 4v-1c-1 0-1-1-2-2l-1 1h-3l-1-3h0-4l-1-1h3l10-1z" class="b"></path><path d="M385 687l1 1c-1 1-1 2-1 4l-1 2h1 1c1 1 0 7 0 9v1c-1 1-1 2-2 3 0 1 0 3-1 4v2c-1 1 0 1-1 2 0 1 0 1-1 2 0 1 0 2-1 3v-1c-1 0-1-1-1-2 0 1-1 3-1 4-1-2-1-4-1-6h-1c0-1 0-2 1-3v-1l-1 1c2-9 6-17 9-25z" class="W"></path><path d="M381 703l2-4h1c0 2 0 4-1 5l-2-1z" class="G"></path><path d="M381 703l2 1c-2 4-3 9-4 13 0 1-1 3-1 4-1-2-1-4-1-6 0-1 1-3 1-3 1-4 3-6 3-9z" class="D"></path><path d="M567 687l1 1v1c2 0 3 1 5 2 0 1 0 2 1 3 1 2 3 7 3 9h-1c-1 2 0 3 0 4-1 1-1 2-1 3l-1-3v2 1c-1-1-1-2-1-3-2 0-2 0-3 1s-3 3-5 3c-1 1-1 2-2 3l-1-2c-1-2-2-4-2-6h-2l-5 2-1-1c-9 5-20 8-30 10v-6c18-5 32-11 45-24z" class="Q"></path><path d="M556 704v-2h1c1 0 2 1 2 1 1 1 1 2 1 3h-2-1l-1-2z" class="U"></path><path d="M552 707h0c1-2 3-2 4-3l1 2h1l-5 2-1-1z" class="K"></path><path d="M568 689c2 0 3 1 5 2 0 1 0 2 1 3 1 2 3 7 3 9h-1c-1-3-2-6-4-9-1 2-3 4-6 6l-7 2c3-2 5-4 8-6h1s0-1 1-1l1-1c1-1 1 0 1-2l-3-3z" class="c"></path><path d="M565 660l3-3 1 1c3 0 6 2 8 4 1 2 1 4 1 6 0 3-2 5-3 8-2 3-5 7-8 11-13 13-27 19-45 24v-6c3 0 6-1 9-2l6-2h1c2 0 8-3 10-5 1-1 1-2 2-2 2-2 5-4 7-5 3-2 5-5 7-7-1-1-2-2-2-3h-1c-1-1-1-1-2-1l-3-3v-2c-2 0-2 0-3 1-1-1-2-1-3-2 6-4 10-7 15-12z" class="O"></path><path d="M574 665c0 2 0 3-1 6 0 1-2 3-3 4v-3c1-1 1-2 2-4 1 0 1-2 2-3z" class="K"></path><path d="M567 674l3-2v3l-6 7c-1-1-2-2-2-3 2 0 4-2 5-5z" class="P"></path><path d="M565 660l3-3 1 1c0 1 0 0 1 1 1 0 2 1 3 3 0 1 1 2 1 3-1 1-1 3-2 3-1 2-1 3-2 4l-3 2c-1 3-3 5-5 5h-1c-1-1-1-1-2-1l-3-3v-2c-2 0-2 0-3 1-1-1-2-1-3-2 6-4 10-7 15-12z" class="a"></path><path d="M565 660l3-3 1 1c0 1 0 0 1 1 1 0 2 1 3 3 0 1 1 2 1 3-1 1-1 3-2 3-1-1-3-2-4-3 1-1 2 1 3 1h0c-1-2-4-3-5-5l-1-1z" class="W"></path><path d="M563 671h0c-1-1-1-2-2-2l1-1h2l-1-1c2 0 2 0 4 1h0l-3-3 1-1c1 0 2 1 3 1 1 1 3 2 4 3-1 2-1 3-2 4l-3 2-4-3z" class="D"></path><path d="M556 673c0-1 2-1 3-2l-1-1 1-1 4 2 4 3c-1 3-3 5-5 5h-1c-1-1-1-1-2-1l-3-3v-2z" class="C"></path><path d="M448 743l18 1v1c1 1 1 2 1 4h1c0-1 0 0-1-1v-4h7 15v4h-1v1h-13c-1 0-1-1-2-1h-1c0 1-2 0-3 0v1c1 1 2 1 3 1v3l-1 3c-1 0-1 1-2 2h0c-1 2-3 4-4 5h-1c-1 1-1 1-2 1v1c-1 0-2 0-3 1h0-2l-1 1c-2 0-5 0-7-1h-1s-1-1-2-1h-1c-1-1-1-2-2-3 0-1-1-1-1-2l-1-1-4 2v1c0 1-1 1-1 1-1-1-1 0-1-1h-3v-2-4c-1-1-2-1-1-3v-1-1-1-1h1c-1-2-1-3 0-5 1 1 0 2 2 3v-1-3h14z" class="n"></path><path d="M449 652h47v1h17-1-22l-1 15c-2 0-6 0-8-2-1-1-1-2-2-3l-1-2c-3 2-5 4-8 7 3 4 6 7 11 10 1 1 8 5 8 6 1 0 0 2 0 3v7c-4-2-9-4-13-6-6-5-12-11-17-17l-5 2v-1c0-1 1-2 1-2v-1l1-1c-3-5-5-10-8-15l1-1z" class="b"></path><path d="M453 655l1 1h1c2 1 5 7 6 9 5 8 13 15 21 21v1c-9-4-16-13-22-20h0l1 1c5 8 13 17 21 21h1-1c-6-3-11-6-15-11-2-2-5-5-6-8-1-2-2-3-3-4-1-2-1-3-2-5s-3-4-3-6z" class="d"></path><path d="M456 653h30c-2 2-5 3-8 5l-15 7-7-12z" class="J"></path><defs><linearGradient id="P" x1="465.758" y1="682.754" x2="453.444" y2="700.477" xlink:href="#B"><stop offset="0" stop-color="#0d0c0d"></stop><stop offset="1" stop-color="#2f2e2e"></stop></linearGradient></defs><path fill="url(#P)" d="M447 673h2c2-1 1-1 3-1l1 2-3 2 1 1c3 1 5 3 7 5l6 6c4 3 10 5 13 9l4 2c2 0 6 2 8 2v2 16h0v-2l-1-1-12-3c-10-4-18-9-25-16-3-2-5-5-8-7 2 5 8 8 10 13-7-6-13-14-20-20h-1l-1 1-1 1v-2c-3 0-5-1-8-1h0l-1-1c2 0 3 0 5-1h1c2-1 4-1 5-2l2-2 1 2c2-1 5-2 7-3 1-1 3-1 5-2z"></path><path d="M481 699c2 0 6 2 8 2v2c-3 0-7-2-10-3 1 0 1-1 2-1z" class="W"></path><path d="M479 700c-12-4-25-13-31-23l1-1 1 1h1c3 1 5 3 7 5l6 6c4 3 10 5 13 9l4 2c-1 0-1 1-2 1z" class="a"></path><path d="M458 682l6 6c4 3 10 5 13 9-1-1-3-1-4-2h0c-6-3-13-7-16-12l1-1z" class="D"></path><path d="M443 690v-1l-6-8c2-1 5-2 7-2 8 11 19 20 32 26 1 1 12 5 12 6v4 1l-12-3c-10-4-18-9-25-16-3-2-5-5-8-7z" class="E"></path><path d="M503 793h0c2-3 1-17 1-21v-58c0-8-1-17 0-25 0-1 0-3 1-4s2-1 3-1c1-1 2-1 3 1 1 1 2 3 2 4 1 9 0 18 0 27v46 27 52 17c0 2 1 6 0 8 0 2-1 3-2 5-1 1-2 1-4 1l-3-3c-1-7 0-15 0-23v-38c0-3 0-13-1-15z" class="I"></path><path d="M508 787v-19-40-17c0-3 0-6 1-9v1 64l1 16c0 2 0 5-1 7h-1v-3z" class="f"></path><path d="M508 787h0l1-33c0 3-1 8 0 12v1l1 16c0 2 0 5-1 7h-1v-3z" class="L"></path><path d="M399 204c6-18 20-40 11-58-4-9-12-14-20-17-15-5-33-4-47 3l-10 7V60h238l24 63 14 37 29 71 34 90 15 36c0 1 2 6 2 7 2 4 4 9 6 14l12 30 27 71 20 51c3 8 7 16 10 25l44 107 30 73 18 44c3 8 7 18 12 26 6 9 14 16 25 21 15 6 31 5 48 5v57H591v-57c22 1 46-1 63-17 10-9 14-21 15-34 0-7-1-13-3-20-3-11-9-22-13-33l-23-57-9-23c-1-2-2-6-4-8h-70l-37 1h0l-4 1v-1h-10-19-86l-30 80-10 24c-3 8-6 17-8 26s-1 19 2 28c4 12 13 23 25 29 14 7 34 9 50 4h1l11-4h1c-5 2-10 4-14 6h-1v55H88l1-57c15 0 30 0 45-6 12-5 23-14 30-25 6-10 10-21 14-32l14-37 34-85 31-80 34-89 27-68 37-95 12-30c2-7 5-14 8-20l17-44 7-16z" class="I"></path><path d="M514 630l2 1 2 3-3 1c0-2-1-3-1-5z" class="H"></path><path d="M621 206c1 0 1 0 2 1v2l-1 1-2-2 1-2z" class="K"></path><path d="M520 100c5 0 10-1 15 0l-15 1v-1z" class="h"></path><path d="M434 86c0-3 1-5 2-8 0 3 0 6 1 8h-3z" class="P"></path><path d="M506 627h7l1 1c-1 1-2 1-3 1v1h0c-1 1-2 1-3 1v-2l-1-1-1-1z" class="F"></path><path d="M800 657c1 0 0 0 1 1h0l1-1 3 6c0 1 0 1-1 1l-1 1c-1-1-1-2-2-3v-1l-1-1v-2-1zm-306-21h-8l-1 1-1-1 1-1c2-1 4-1 6-1h1l1-1h1v2 1z" class="e"></path><path d="M440 100h1c2 0 2 0 4 1 0 2 0 2-1 4h-1c-1 0-2 0-3-1v-4z" class="J"></path><path d="M636 674v-2h0l1 1c0 1 1 1 1 2h0c1 1 1 1 1 2h1v-1l2 1-1 1c0 2 1 3 2 4s0 3 0 5c0-1-1-3-2-4-2-3-2-7-5-9z" class="F"></path><path d="M511 630l2 1 1-1c0 2 1 3 1 5h-3c-2 0-2 0-4-1v-1-2c1 0 2 0 3-1h0z" class="c"></path><path d="M508 631c1 0 2 0 3-1l-1 2 1 1v1l-1-1h-2v-2z" class="K"></path><path d="M493 100h9 18v1h-30l3-1z" class="V"></path><path d="M831 757l2-2 6 15h-3v-1l-1-1v-1c0-1-1-1-1-2l-3-8z" class="R"></path><path d="M185 865c1 0 1 0 2 1-1 1-1 2-2 3h0c-2 1-2 0-4 1h-9 0c2 0 4 0 5-1h2 1v-2h-1-2 0c-2-1-3-1-4-1l1-1c2 1 4 1 6 1h1c1 0 1 0 2 1v-2h1 1z" class="K"></path><path d="M648 688h1l1-1 7 17c-1 0-2-1-2-2v-1h-1c0-1 0-2-1-3v1c-2-3-5-7-5-11z" class="U"></path><path d="M399 204s1 1 1 2-1 2-1 3l-6 15-1-4 7-16zm342 662h-1 0c-1 0-1 0-1 1-1-1-2 0-3 0h0c-2 0-2 0-3-1v-1h0v1h-5v-1-1h5 0 5l1 1v-1c1-1 4 0 6 0-2 1-2 0-4 2z" class="H"></path><path d="M706 418h0c1 2 3 8 3 10l-1 1c3 1 4 4 5 7l1 1h-1 0l-1 1h0l-1-1c-1-1-2-2-2-3-1-1-1-3-2-3l-1-1 1-1c-1-1-2-2-2-3h0l2 2h1l1-1c-1-2-2-4-2-5l-1-1v-3z" class="U"></path><path d="M723 472l3 1c0 2 1 4 2 5l6 14h-1l-2-4v-1c-2-2 0 1-1-1v-1l-1 1h0c-1-1-1-1-1-2l-1-2-1-2c-2-2-2-6-3-8z" class="K"></path><path d="M434 86h3c0 4 0 8 1 11h-6c1-2 0-6 2-8h0 1l-2-2 1-1z" class="R"></path><path d="M432 97h6c0 2 1 4 0 6 0 1 0 1-1 2-2 0-3 0-5-1v-7z" class="J"></path><path d="M430 623c2-1 5-1 7 0h11c1 0 2 1 4 1-1 0-1 1-2 1-8 0-17 1-25 0 2-1 3-1 5-1v-1z" class="Y"></path><path d="M477 640l2-1c2-1 5 0 8 0 0-1 0-1 1-1h12c3 0 5-1 8 0h0c1 1 0 1 1 1-1 0-2 0-3 1h-10-19z" class="U"></path><path d="M143 849c4 0 6 1 8 4 0 1 0 2-1 2-1 1-1 1-3 1-1-2-1-3-3-3h-1c-2 3-4 6-7 8l6-9-1-1v-1l2-1z" class="D"></path><path d="M494 633c2 1 3 1 4 0v-1c1-4 5-1 8-3h-4v-1l4-1 1 1c-1 2-1 3-1 5v2h-2c-2-1-7 0-10 1v-1-2zm19-10l1 1c7 2 14 1 20 1 2 1 2 1 4 1-6 2-15 2-20 1-3-1-4-1-5-4z" class="H"></path><path d="M536 631h2 2c1 1 3 1 4 3v2l2 1h-4l-1-1-5 1-2-2h-2 2v-2l-1-1 1-1h2z" class="O"></path><path d="M536 631h2 2c1 1 3 1 4 3v2c-1 0-2 0-3-1l1-2c-1 0-2-1-3 0h-2c-1-1-1-1-1-2z" class="F"></path><path d="M207 731l9-22 1 2-8 22h-1l-1 1v-3z" class="G"></path><path d="M518 634c1 1 3 0 5 0l9 1h2l2 2h-2-6c-5 0-11 1-16 1h1v-1c2-1 4 0 6 0-1 0-2 0-3-1h-1c-1 0-2 0-3-1h0 3l3-1z" class="e"></path><path d="M305 864h3 1l2 1h1 1l1 1h2c1 0 1 0 2 1 0-1 1-2 2-3h0 2l1 1v1l-2 1-2 1c-1 0-1-1-2-1l-1 2h-2c-1-1-2-1-3-2h0-2c-2-1-3 0-5 1l-1-1v-2l2-1z" class="H"></path><path d="M599 623c2-1 3-1 5-1 2 2 15 1 18 1l1-1 2 2h-44 7l-3-1h14z" class="C"></path><path d="M195 761l12-30v3l1-1h1l-13 30-1-2z" class="R"></path><path d="M390 623h40v1c-2 0-3 0-5 1h-41l1-2c2 1 3 0 5 0z" class="B"></path><path d="M323 432l13-37 2 2-3 10-11 26-1-1z" class="L"></path><defs><linearGradient id="Q" x1="527.845" y1="630.962" x2="521.534" y2="644.712" xlink:href="#B"><stop offset="0" stop-color="#252d29"></stop><stop offset="1" stop-color="#41393e"></stop></linearGradient></defs><path fill="url(#Q)" d="M536 637l5-1 1 1c2 2 3 2 5 2l-37 1h0l-4 1v-1c1-1 2-1 3-1-1 0 0 0-1-1h0 4c5 0 11-1 16-1h6 2z"></path><path d="M537 623h48l3 1h-7c-2 1-5 0-8 0l-15 1-20 1c-2 0-2 0-4-1h0l-2-1h0c2 0 3 0 5-1h0z" class="E"></path><path d="M534 625h24l-20 1c-2 0-2 0-4-1h0z" class="i"></path><path d="M492 625c4-1 8-1 13-2-3 2-6 4-10 5h-1c-3 1-6 1-8 0-3 0-14 0-16-1-3-3-11-2-15-2h37z" class="K"></path><path d="M486 628c2-1 3-1 6-1 1 0 1 1 2 1-3 1-6 1-8 0z" class="F"></path><path d="M665 858l15-1c1 0 3 1 3 0l40 1v1h0c-7-1-15 0-22 0h-49s-3 0-4-1h4c4 0 9 1 13 0z" class="C"></path><path d="M375 88l58-1 2 2h-1 0-59v-1z" class="J"></path><path d="M636 674c3 2 3 6 5 9 1 1 2 3 2 4l1 1c1 1 3 3 2 5v1l1 1c0 1 0 2 1 2l1 1c-1 3 1 7 1 10l1 1c1 3 2 7 4 11l-1 1s-1-1-1-2h-1c-1-3-2-7-3-10-2-7-4-13-7-19-2-4-3-8-4-11l-1-1c-1-2-2-3-1-4z" class="U"></path><path d="M718 462l1 2 1 1v1c1 1 1 2 1 3h0c-1-1-2-1-2-2v-1l-1-2h0c-1-1-1-2-1-3h-1c0-2 0-3 1-4-1-1-2 0-2-2 0-1 0-2-1-2l1-1c1 1 1 2 1 3 1 0 2-1 3-1l-1-1h0l2-1v-1l1 1-1 1c1 0 1 0 2 1v1 2c1 2 1 3 1 5h1c1 2 1 4 2 6 0 1-1 1 0 3 0 1 0 1 1 2v2c0 1 1 1 1 2v1c-1-1-2-3-2-5l-3-1-1-3c-1-1-1-1-1-2-1-2-1-3-1-5h0 0l-2-1v1z" class="e"></path><path d="M718 462c-1-1-1-1-1-2v-1c1 0 1 1 1 2v1z" class="F"></path><path d="M722 463h0c-1-2-2-4-3-5 1-1 1-1 2-1 1 1 1 2 1 3 1 1 0 1 0 2l2 2c-1 0-2 0-2-1z" class="c"></path><path d="M721 467v-3h0l1-1c0 1 1 1 2 1l1 4v1l1 4-3-1-1-3c-1-1-1-1-1-2z" class="K"></path><path d="M722 469c0-1 0-2 1-3 0 1 1 2 2 3l1 4-3-1-1-3zm-436 57h1l-25 63c0-3 1-5 2-9 3-8 6-17 10-25 2-5 3-10 5-15 1-3 3-6 3-8 0-1 0-1 1-2 0 1 0 1 1 1l1-2c0-1 0-2 1-3zm344 109l20 52-1 1h-1c-3-6-5-10-6-16l-1-1c-1-3-2-5-3-7-1-5-3-10-5-14-1-4-2-7-4-11 0-1 0-2 1-4z" class="H"></path><path d="M454 87h78v1h-17c-8 0-74 2-77 0v-1c4-1 11 0 16 0z" class="J"></path><path d="M806 691l27 64-2 2-18-43c-2-6-5-13-7-20 1-1 0-2 0-3z" class="a"></path><path d="M392 220l1 4-11 30c-2 4-4 7-5 10 0 1-1 2-1 4l-1 1v1c-1 1-1 0-1 1s-1 2-1 3l-1 1v1l-1-1v2c0 1-1 1-1 2v1c-1 2-2 3-3 4 2-7 5-14 8-20l17-44z" class="T"></path><path d="M286 526l2-5v-1-1c1-1 0-1 1-2h0c0-1 1-2 1-3s0-1 1-2c0-1 0 0 1-2h0v-1c0-1 0-1 1-2 0 0 0-1 1-1 0-2 0-3 1-4h0 0v-1c1-2 2-3 2-4 0-2 1-3 2-5v-1h-1l4-10v-3l2-2h0l1-1c1-2 0-3 0-5 1-2 2-3 2-5 1-2 2-3 2-5h0l1-2c1-1 1-2 2-4 0-1 0-2 1-2h1 0l2-5v-1l1-1c0-2 1-2 2-3l-32 84h-1z" class="K"></path><path d="M689 779c2 8 3 15 2 24-2 16-9 29-22 40-6 4-12 8-19 11l-9 3 24 1c-4 1-9 0-13 0h-4c1 1 4 1 4 1h-7-17c6-1 12-3 18-5 14-5 27-15 35-28 6-8 9-19 10-28v-6c-1-3-1-6-2-9v-4z" class="G"></path><path d="M836 770h3l10 24c10 22 24 38 46 50-1 1-3 0-4-1l-3-1c-1-2-5-3-7-5-4-2-8-5-11-9-7-6-13-14-17-22-7-11-11-24-17-36z" class="d"></path><path d="M452 613v6h1c2-1 3-1 6 0h1l2 2c1 1 4 0 6 0v-1c-1 0-2 0-3-1h5 0l6 3c3 1 6 1 9 1s4 0 7 2h-37-5c1 0 1-1 2-1-2 0-3-1-4-1h4l-1-2h-2c1 0 1 0 2-1l1-7z" class="R"></path><path d="M470 619h0l6 3c3 1 6 1 9 1l-1 1c-5 0-10 0-15-1h3c-2-1-5-1-8-1 0 0-1-1-2-1h-1 1c1 1 4 0 6 0v-1c-1 0-2 0-3-1h5z" class="L"></path><path d="M452 613v6h1c2-1 3-1 6 0h1l2 2h-1 1c1 0 2 1 2 1 3 0 6 0 8 1h-3-17l-1-2h-2c1 0 1 0 2-1l1-7z" class="O"></path><path d="M452 597h2l2 2c1 3 1 7 3 9 2 4 6 7 9 10l2 1h-5c1 1 2 1 3 1v1c-2 0-5 1-6 0l-2-2h-1c-3-1-4-1-6 0h-1v-6-1-15z" class="M"></path><path d="M603 172c2 1 1 2 2 3l1 1v2c1 1 1 2 2 3 0 1 1 3 1 4v1l1 2v1c1 0 0 0 1 1 0 1 0 1 1 2v2l1 2c0 1 1 3 2 4 0 1 0 1 1 2 0 2-1 0 0 2l1 1c0 1 0 2 1 3 0 0 0 1 1 2v1l1 3c1 3 2 6 4 9v1l1 1c0 1 0 2 1 2v1 1 1h0c1 1 1 1 1 2l1 1v1c0 1 0 1 1 2v2l1 1h0c0 1 0 1 1 2v1h0v1l3 8c1 0 1 1 1 2 1 1 1 2 2 4 0 0 0 1 1 2 0 1 0 2 1 3h0c0 2 0 1 1 2v1l1 1v1c0 1 0 1 1 2 0 1 1 2 1 4h0c0 1 0 0 1 1v2l1 1c0 1 1 3 1 5 0 1 1 1 1 2v1h1v1c0 3 3 6 3 9l1 1v1l1 1v1c0 1 1 2 1 3 0 0 0 1 1 1v2s0 1 1 2v1 1l2 4 1 1c0 2 1 4 2 6v1l1 1h0c0 1 1 2 1 3l1 1v-1c1 1 2 1 3 3v2c0 1 0 2 1 3v1c-1-1-2-1-3-1l-13-34-17-43c-1-6-4-11-6-17l-26-67z" class="K"></path><path d="M150 842c3-1 7-2 10-1v1c1 2 1 4 2 5v6c1 1 1 2 1 4v7h-8c1-3 1-5 1-8-1-5-3-7-6-10-2-1-3-1-4-2 1-1 3-2 4-2z" class="B"></path><path d="M150 842c3-1 7-2 10-1v1c-3 0-3 3-5 4-2 0-3-1-5 0-2-1-3-1-4-2 1-1 3-2 4-2z" class="G"></path><path d="M252 846h6c-1 2-1 4 0 6v1c-1 1 0 2 1 4h0c1 1 1 1 1 2h0-2v1h-52-4-24-5c0-1-1-1-1-1l5-1c7-1 14 0 22 0h48 0c2-1 4 0 5 0v-2-3-7z" class="k"></path><path d="M252 846h6c-1 2-1 4 0 6-1 0-2-1-3-1-1 1-2 0-2 0-1 2-1 3-1 5v-3-7z" class="G"></path><path d="M689 364c2 4 4 9 6 14l12 30 27 71 20 51c3 8 7 16 10 25h-1c0-1 0-1-1-2v-2h-1c0-3-2-6-3-9v-1l-1-1v-2h0c-1-1-1-1-1-2h-1c0-2 0-3-1-4l-1-1c0-1-1-4-1-5v-1h-1v-1-1c-1 0-1-1-1-1-1-2-1-1-1-2 0-2-1-3-2-5h0c-1-2 0 0 0-1l-1-2h0v-1c-1-1-1 0-1-1v-1-1c-1-1-1-2-2-4l-1-1v-1c0-1-1-1-1-2v-1c-1-1-1 0-1-2h0v-1c-1-1-1-1-1-2-1 0-1 1-1 2 0-1-1-1-1-2-1-4-3-10-5-13 0-1-1-2-1-3-1-1-1-2-1-2l-1-1v-1-1s0-1-1-2c0-1-1-2-1-3v-2l-1-1v-2l-1-1h1v-2h-1v-2h0l-1-2h0v-1l-1-1v-2h0l-1-1-1-2v-2c-1-1-1 0-1-2h0l-1-1v-2c-1-2-2-5-3-7l-1-3v-1h-1v-2l-1-1v-1s0-1-1-1v-1c0-1-1-2-1-3l-1-2v-2l-3-6v-1c-1-1-1-1-1-2v-1h-1v-2c-1-1-1-1-1-2v-1h-1v-2h0l-1-1v-3h-1v-1c0-2-2-5-3-7v-2c-2-2-2-5-4-7l-1-3v-1l-1-1v-2c-1-1-1-1-1-2s0-1-1-2h0v-1c-1-2-1-3-1-4z" class="U"></path><path d="M195 761l1 2-8 19c3-3 5-5 8-7 1 0 1 1 1 2-4 5-7 10-10 15-1 1-2 3-2 4-6 10-10 20-17 29-5 7-11 11-18 17-1 0-3 1-4 2-5 1-9 4-14 6h-1c13-6 26-14 35-26 7-8 10-17 14-26l15-37zm282-649c4-1 8-3 12-4 11-2 22-1 34 0 6 0 13 1 19 2v5c-2 0-4 0-6-1l-14-2c-7-1-14-2-21-2-1 2-2 2-4 2-4 0-4 1-8 3-1 0-2 1-4 1h-3c-2 0-3 1-5 1l-2 2-1-1h-1v-1c1-2 2-4 4-5z" class="B"></path><path d="M477 117c7-5 15-6 24-7-1 2-2 2-4 2-4 0-4 1-8 3-1 0-2 1-4 1h-3c-2 0-3 1-5 1z" class="T"></path><path d="M314 777l1-1c-3 17 4 36 13 49 11 17 30 29 50 33 2 1 5 1 8 2H258v-1h2 0 62c2-1 3 0 4-2l-1-2c6 3 11 4 17 4h8c3-1 6 0 10 0h17c-4-2-9-3-13-4-7-3-13-5-19-10-7-5-13-12-18-19-7-9-10-19-12-30-1-5-2-10-1-14v-5z" class="Y"></path><path d="M553 74l-164-1h-25c-2 0-6 0-8-1h208l8 21c0 1 0 1-1 2l-8-22-10 1z" class="C"></path><path d="M668 874h65 68 73c11 0 23-1 35 1h-39-241l39-1z" class="E"></path><path d="M161 874c5-1 213 0 213 0h1c1 0 1 0 2 1h-1-61l-214 1c2-2 9-1 12-1l48-1zm623-14c2-1 5 0 7 0h18l47-1c-1 0-2 1-2 2-2 1-4 1-6 2l-1 1h0-2c-2 0-2 0-3 1h-6c-5 0-10-1-16 0-1 0-3-1-4 0-1 0-1 2-2 1v-1c-1 0 0 0-1 1h-1c-3-1 1 0-1 0-1 0-2-1-3-1h0-1-4v-1h3c-1-1-5 0-6 0s-1 0-1 1v1s1 1 2 0h1l1 1c0-1 2 0 2-1h1c1-1 1 0 1 0l1 1 1-1h0v1 1l-1-1h-2 0c-1 1-3 1-4 1v-1h-1c-1 1-2 0-3 0h-1 0l-1 1c-1-1-2-1-2-2h1v-1c-1 0-1 0-1 1-2 0-2 0-3-1h-1v1 1l-1 1c-1 0-2-1-2 0h-4l-1-1v-1h-1-1c-1 1-1 1-2 1h0-1c-1 0-2 0-2 1h-3l-1-1-1 1h-1c-1-2 0-2 0-3h1v-1c1 0 2-1 3-1l1 1c1-1 3-1 4 0l1-1c1 1 2 1 2 1 2 0 4 0 5-1l-1-1h-8 0l-1-1 2-1c1 1 3 1 4 0h1 0 1z" class="H"></path><path d="M790 862h5v1h-3-3 0l1-1z" class="K"></path><path d="M784 860c2-1 5 0 7 0h18l47-1c-1 0-2 1-2 2-2 1-4 1-6 2-2-1-5 1-8 1h-14c-2 0-5 1-7 0 1-2 5-1 7-1l-1-1h-8c-3 0-5-2-8-1-4 1-8 1-12 1v-1c1 0 1 0 2-1h0-3 0v1l-1 1h-5-5-8 0l-1-1 2-1c1 1 3 1 4 0h1 0 1z" class="m"></path><path d="M323 432l1 1-6 15-2 6-7 17c0 2-1 4-2 6l-25 66h3v2c-1 1-1 2-1 3l-1 1c-2 1-2 1-4 1l-10 24c-2 6-4 11-6 17l-46 120-1-2 46-120 25-63 32-84c1-1 3-9 4-10z" class="S"></path><path d="M279 550l3-7h3v2c-1 1-1 2-1 3l-1 1c-2 1-2 1-4 1z" class="e"></path><path d="M347 370l16-43 30-76 18-45c4-10 8-19 11-30 3-10 5-21 3-31-1-10-4-21-10-29-12-17-29-24-49-28h9v1h0 1c16 3 31 13 40 27 5 6 8 13 10 21 0 3 0 8 2 11l1 2c-2 5-2 10-2 14-1 4-2 7-3 10l-3 10c0 1-1 3-1 3l-3 9c-1 2-2 4-2 6v2h-1c-2 4-4 9-5 13l-11 27-34 86-8 21c-2 5-4 9-5 14l2 2c0 1 1 2 0 3-1-1-2-1-3-1l-2 2-1-1z" class="D"></path><path d="M461 590c1 2 3 7 5 9h0c3 2 8 8 12 9 2 2 4 3 7 4 6 2 12 3 18 4h3v5 1l-1 1c-5 1-9 1-13 2-3-2-4-2-7-2s-6 0-9-1l-6-3h0l-2-1c-3-3-7-6-9-10-2-2-2-6-3-9h1 4 1v-3h0c0-2-1-4-2-5l1-1z" class="C"></path><path d="M456 599h1v1h0c2 3 4 6 6 7l-2 2-1-1h-1c-2-2-2-6-3-9z" class="P"></path><path d="M463 607c2 2 4 3 5 5h-2v1 1c1 1 1 3 2 4-3-3-7-6-9-10h1l1 1 2-2z" class="a"></path><path d="M461 590c1 2 3 7 5 9h0c3 2 8 8 12 9 2 2 4 3 7 4-2 1-4 0-6 1l-1 1c2 1 5 2 7 4h0c-6-2-13-5-17-10-2-3-4-6-7-9h0 1v-3h0c0-2-1-4-2-5l1-1z" class="N"></path><path d="M466 599h0c3 2 8 8 12 9 2 2 4 3 7 4-2 1-4 0-6 1l-1 1c0-1-2-2-3-3-3-3-7-7-9-12z" class="G"></path><path d="M724 484l82 207c0 1 1 2 0 3 2 7 5 14 7 20l18 43c1 3 2 5 3 8 0 1 1 1 1 2v1l1 1v1c6 12 10 25 17 36 4 8 10 16 17 22 3 4 7 7 11 9 2 2 6 3 7 5h-1-1l-4-3-5-4c-1 0-1 0-2-1s1 0-1-1c-1-1-2-2-4-3-1-1-4-4-5-6-1-1-3-2-4-4 0-1-1-1-2-3l-1-1c-1-1-1-2-1-2l-3-3-2-5-1-2c-1-1-2-2-2-3l-3-6c0-1 0-2-1-3 0-1 0-1-1-2l-1-2c0-1 0-2-1-2v-2l-1-1-1-2v-1c0-1-1-1-1-2v-1l-3-6c-2 2-3 4-6 5-1 2 0 19 0 23 3 3 7 7 9 10 1 2 5 5 7 7l13 13 7 7c3 2 5 6 9 8 1 1 1 2 3 2 1 1 1 2 3 3v1c1 0 2 0 2 1l1 1h1l1 1h-1l-1 1c1 1 1 1 3 1l1 2c1 0 2 0 3 1-1 1-9 0-11 0l-23 1h-1l-47 1h-18c-2 0-5-1-7 0h-1 0-1c-1 1-3 1-4 0l-2 1 1 1h0 8l1 1c-1 1-3 1-5 1 0 0-1 0-2-1l-1 1c-1-1-3-1-4 0l-1-1c-1 0-2 1-3 1v1h-1c0 1-1 1 0 3l-2-1h-1c-1 0 0 0-1-1v-1h0l-2 1c-1 0-1 1-2 0h0c-4-1-9-1-12 0h-3v-1c-2 0-3 0-5 1 2-2 2-1 4-2v-1c-2 0-2 0-4-1l-1-1h-4c-2-1-4 0-5 0-1-1-1-1-2-1l-1-1h-5v-1l-40-1c0 1-2 0-3 0l-15 1-24-1 9-3c7-3 13-7 19-11 13-11 20-24 22-40 1-9 0-16-2-24 0-1-1-2-1-2l-2-5v-2c0-1-1-2-1-3l-3-6v-1l-1-1c0-2 0-3-1-5h-1c0-2 0-2-1-3l-14-31c-2-6-7-15-7-21 0-2 1-4 1-5l1-1c0-3 2-6 3-9l6-16c1-3 2-5 3-8v-2l1 1c3-2 6 0 9-2v-1c1-1 3-2 4-1l1 1h1v-1l-1-1v-3c3 2 6 6 7 9l1 1c0 1 0 2 2 2 2-3 1-6 1-10v-13h1c1 0 1-1 2-1 2 0 3 0 4-1 1-5 0-13-1-18l-7-48c2 0 5 0 7 1-1 1-1 2-1 3l2 1h-2v3h1c1 1 2 1 3 2h0c1-1 2-1 3-1l1 1c2 0 3 0 6-1h1v-2-1c1-2 1-4 1-5 3-1 8-1 11 0l1 1h0v-1h2l2-1-1-1 2-2h-8v-1c3-1 8 0 12 0l3-16v-5-11c-2-7-5-13-7-18-3-8-6-17-10-25-1-2-3-4-4-6 1 0 2 1 3 2v1c1 1 1 2 2 3 0-1-1-4-2-5v-4z" class="M"></path><path d="M707 753h0c0 5 1 12 0 16l-1-1v-5c1-3 0-7 1-10z" class="O"></path><path d="M788 790c0-2 0-6 1-8h1c1 3 1 6 1 8h-3z" class="g"></path><path d="M693 840c1-1 0 0 1 0l-1 1h-3c-4 1-9 1-13 1h-2c2-2 5-1 7-1s4 0 6-1h5z" class="O"></path><path d="M744 655c1 1 2 1 2 2 1 2 1 4 1 6-1 1-1 1-3 2 0-2 2-1 2-3h0l-1 1v-1c0-1 0-2-1-3v-1-1h1l-1-1v-1z" class="j"></path><path d="M702 580h1c1 1 2 1 3 2h0c1-1 2-1 3-1l1 1c2 0 3 0 6-1h1c0 1 0 1-1 2-3 2-8 1-12 0-1-1-1-1-2-3z" class="C"></path><path d="M783 727h3c1 0 2 1 3 2-1 2 0 2-2 4h-4v-1c-1-2 0-3 0-5zm12 81l2 1v-1c0 3 0 8 1 9h5v-2h3v1h-1l1 1v1h-9l-2-1h0v-1h0v-8z" class="L"></path><path d="M713 835c1 2 2 3 3 5v12 1c-1-2-1-5-1-7v-3c-1 1-1 2-1 3-1-1-1-1-1-2s0-1-1-2c0-2 0-5 1-7z" class="I"></path><path d="M834 765c0 1 1 1 1 2v1c-1 1-1 1-3 1-1 0-2 1-3 1h-2v1c-1 0-1-4-1-5l8-1z" class="P"></path><path d="M726 604c-1 2-3 4-4 6l-11 14h0l14-23v1c-1 2-2 3-3 5h0l1-1c1-1 1-1 2-1v-1h1z" class="G"></path><path d="M789 759h1c1 0 3 1 4 2s0 3 0 5h-2-4c0-1-1-2-1-3 1-2 1-3 2-4z" class="d"></path><path d="M706 820c0 2-1 5 0 6 2 2 1 5 1 7s0 3 1 5c-1 2-1 3-2 4-2 2-2 4-4 5l4-7v-1l-1-1h-1c1-2 1-5 1-7 1-3 0-7 1-11z" class="K"></path><path d="M789 719c-3 3-9 8-13 8-3-1-5-7-6-9 2 2 3 5 7 5 2 0 6-5 8-6h0c-1 3-5 6-7 7h3 0c1-1 1-1 2-1 2-2-2 1 1-1l1-1h1c0-1 0-2 2-2h1z" class="Y"></path><path d="M788 790h3v10 1c0 1-1 1 0 2 0 3 1 10-1 13l1 1h-1-1-1v-1h2c0-1-1-1-1-1l1-2h0-2l1-1h0l1-1-1-1h0-1c1-1 1-1 2-1-1-1-1-1-1-2h1v-1c-1 0-1 0-2-1h1l1-1c-2 0-1 0-2-1h2c0-1-1-1-2-2h2l-2-1c1-1 1-1 1-2l-1-1h1l-1-1v-6z" class="P"></path><path d="M752 623h1l3 22c0 3 0 5 1 8 1-1 2-1 3 0v3h-3v5c0 2 0 2-1 3v-3c0-3-1-5-1-7 1-3 0-7 0-10l-3-21z" class="f"></path><path d="M835 768l1 1c-2 4-4 5-7 6-1 4 0 8 0 11 0 4-1 8 0 11h-1c-1-5 0-10 0-15 0-4-1-7-1-11v-1h2c1 0 2-1 3-1 2 0 2 0 3-1z" class="d"></path><path d="M830 812l4 2v13l-1 1h-2c0-1 0-2-1-3v-11-2z" class="h"></path><path d="M830 814l1 1c1 4 1 9 2 13h-2c0-1 0-2-1-3v-11z" class="f"></path><path d="M795 808v-25h1 1c1 2 0 5 0 7v18 1l-2-1z" class="d"></path><path d="M730 643l-1-1c2-5-1-12 2-18l1 1 1 1c0 4 1 8 1 12-1 2-3 4-4 6v-1z" class="k"></path><path d="M733 626c0 4 1 8 1 12-1 2-3 4-4 6v-1c0-2 1-3 1-4 2-4 1-9 2-13z" class="f"></path><path d="M744 597v-1c2-2 3-4 4-7 0-2 0-4 1-5v-1c-1-1-1-1-1-2h1v1l1-1c-1-1-1-1-1-3h3c1 2 2 4 1 6l-1-1v1c1 0 1 0 1 2h0l-1 2h1v1h0c-2 1-3 2-4 4s-3 4-4 5l-1-1z" class="R"></path><path d="M747 746h0c-3 0-6 0-8-2-2-1-3-3-3-5 1-3 4-5 6-7h0l1 2h0c-2 1-2 2-3 3v2h1c2 2 2 3 5 3h0 2c0 2 0 2-1 4z" class="G"></path><path d="M744 655l-3-1v-3h1 2c0-5 0-9 1-14v-1-1c0 5-1 10 0 15 2 1 4 2 5 4v11c-1-1-2-1-3-2 0-2 0-4-1-6 0-1-1-1-2-2z" class="a"></path><path d="M780 780h4v7 2 10c0 2 0 5-1 7h0s-1-1-1-2c0-3-1-5-1-8-1-5-1-11-1-16z" class="K"></path><path d="M685 745l1 1c0 10 6 19 9 29 1 3 1 7 2 10v1c0 1 0 2-1 3l-2-1-1-1c-1-1-1-2-1-4l3 1c0-2-1-4-2-6-1-4-3-8-4-13l-2-9c-1-2-1-3-1-4 0-2 0-4-1-6v-1z" class="X"></path><path d="M759 771c7 0 14-1 20 0l12 2c3 0 9-1 11 1-3 0-6 0-8 1h-2c-2-1-3 0-5 0-5-1-12-1-18-1h-1c1-1 1-1 2-1 1-1 2-1 3-1h-1-3c-3-1-6 0-9 0l-1-1z" class="D"></path><path d="M705 831c0 2 0 5-1 7h1c-1 6-6 11-10 14-3 2-7 3-10 5h-2c0 1-2 0-3 0 3-1 5-2 7-3 9-6 14-13 17-23h1z" class="l"></path><defs><linearGradient id="R" x1="730.631" y1="762.399" x2="730.86" y2="749.084" xlink:href="#B"><stop offset="0" stop-color="#c7c5c7"></stop><stop offset="1" stop-color="#e9e8e9"></stop></linearGradient></defs><path fill="url(#R)" d="M723 753c0-2-1-3 0-4s4-1 6 0c4 1 8 5 9 8l1 4 1 1-1 2h-1-2c-1-1-1-3-1-3-1-2-2-5-4-6-2-2-5-2-8-2z"></path><path d="M708 701l1-2c1-1 2-1 4-2 3 0 6-1 9 1 0 2 0 6-1 8h-1l-1-1v-1-4 3c0 1-1 2-1 2h-1v-6h-1c0 2 1 5 0 7h-2v-1c-1 0-1-4-1-5l-1 1c1 1 1 1 0 2v1l1 1h-2c-1 1-1 0-2 0v-3l-1-1z" class="U"></path><path d="M706 826v-7c1-1 1-1 1-2v-1h1c0 1 0 3 1 4h0c1 6 2 10 4 15-1 2-1 5-1 7 0 3 1 10 0 12-2-2-1-14-1-17v-1l-1-1h0c1 2 0 9-1 11-1-3-1-7-1-10v2c-1-2-1-3-1-5s1-5-1-7z" class="N"></path><path d="M710 585h0c1 0 2 0 3 1 0 3 1 8-1 11s-5 5-8 6h-1l-1-1c-1-1-1-3 0-4 1 0 2 0 3-1 6-3 2-8 3-12h2z" class="G"></path><path d="M826 828h-1l-1-1c0-4-1-12 1-15h2c1-3 0-6 1-9h0c2 3 2 6 2 9v2 11c1 1 1 2 1 3h-5z" class="g"></path><path d="M826 828c0-3 0-5 1-8 0-3-1-5 1-8h1l1 2v11c1 1 1 2 1 3h-5z" class="D"></path><path d="M704 831v-22-12c0-6 0-12 1-18 1 2 3 4 3 5v5c0 7 0 13-1 19 0 4-1 8-1 12h0c-1 4 0 8-1 11h-1z" class="V"></path><path d="M802 744c0 1-1 1-1 2h-4c-2-1-2-2-2-3 0-4 3-6 5-9 3-2 6-4 9-3 3 0 7 1 8 4 1 1 1 2 1 3s-1 1-2 2c-2-1-2-4-4-5-1-1-4-1-5-1-2 0-4 1-5 3-1 1 0 2 0 4h1c0 1 0 2-1 3z" class="C"></path><path d="M802 741h1c0 1 0 2-1 3h0c-2-1-3-1-5-2h0 1 0v-1h2 2z" class="D"></path><path d="M766 637c1 4 2 8 5 11 1 2 4 5 5 7v4l-2 2 1 1c3 2 3 6 4 10h-9 0c2 1 5 0 8 1h0l2 1v1c-2-1-3-1-4-1l1 1c-1 1-1 1-3 0v1h-1l-1-2c-1 1-1 1-2 1s-1 0-2-1c0-1 1-1 1-2 0-3 1-6 1-9 1-4 1-10 0-14-2-4-4-8-4-12z" class="E"></path><path d="M739 768l59 1v1c-4 0-9-1-14 0 6 1 12 1 17 1-4 2-10 2-14 1-3-1-5-1-7-1-4-2-10-1-15-1-2 0-5 0-7 1-8 1-16 0-23 0-5 0-9 1-13 1-3 0-8 1-10 0 0-1 1-2 2-3 7-2 17-1 25-1z" class="G"></path><path d="M777 856h29c1 1 1 1 1 2 2 0 8 0 10-1h5c6 0 13-1 19 0 2 1 8 0 11 0h25c3 0 7 1 11 0 1 0 2 0 3 1-1 1-9 0-11 0l-23 1h-1l-47 1h-18c-2 0-5-1-7 0-1-1-2-1-4-1 2-1 4-1 6-1h9c-4-2-10-1-14-1-1 0-3 0-4-1z" class="W"></path><path d="M697 640h1c1 0 1-1 2-1 2 0 3 0 4-1v9c-1 8-4 15-5 23-1 3-1 10-3 12l-1-2c-1-2-1-3-2-5s-1-5-2-7c-1-4-2-7-3-9v-1c0-1-1-1-1-2v-1l-1-1v-3c3 2 6 6 7 9l1 1c0 1 0 2 2 2 2-3 1-6 1-10v-13z" class="B"></path><path d="M686 651c3 2 6 6 7 9-1 0-1 0-2-1l-1-2c-1 2 1 5 1 8l1 1v-1c2 3 1 6 1 9v1c-1-2-1-5-2-7-1-4-2-7-3-9v-1c0-1-1-1-1-2v-1l-1-1v-3zm56-99c1-2 0-4 1-7v-4h0c1 1 1 1 1 2v1 4 2l1-1v-2c0 2 1 5 0 6 0 2-1 2 0 4l1 1c-2 14-6 26-15 38-1 3-3 6-5 8h-1v1c-1 0-1 0-2 1l-1 1h0c1-2 2-3 3-5v-1c5-9 9-18 12-28h-5 0l2-1-1-1 2-2h-8v-1c3-1 8 0 12 0l3-16z" class="D"></path><defs><linearGradient id="S" x1="696.852" y1="722.069" x2="715.357" y2="715.686" xlink:href="#B"><stop offset="0" stop-color="#bfbfbf"></stop><stop offset="1" stop-color="#eeedee"></stop></linearGradient></defs><path fill="url(#S)" d="M708 701l1 1v3c1 0 1 1 2 0h2l-1-1v-1c1-1 1-1 0-2l1-1c0 1 0 5 1 5v1c-5 0-10 2-13 5-2 3-2 7-1 9 0 3 1 5 4 6 1 1 4 1 6 1 2-1 3-2 3-3 1-1 1-1 1-2l-1-1-2 1h-1c-2 0-3 0-4-1s-2-2-2-3c0-2 1-3 2-4s3-2 5-1c2 0 3 1 5 2 2 3 2 5 2 9 0 2-2 4-4 6-2 1-5 2-7 1-4 0-7-2-9-5s-3-7-2-10c1-6 4-10 9-13 0 1-2 2-2 3l2-2h1v-1l1-1v3 1h1v-5z"></path><path d="M709 720l-1-2c2-1 2-1 3 0l-1 2h-1z" class="I"></path><path d="M711 718c2 0 3 0 3 2v2l-1-1-2 1-2-2h1l1-2z" class="F"></path><path d="M688 659c1 2 2 5 3 9 1 2 1 5 2 7s1 3 2 5l-1 2c0 2 0 3-1 5v1c-1 4-3 7-5 10-1 1-3 2-4 4h-1c-2-1-3 0-5-1v-13c0-3 0-6 1-9 2 0 3 0 4 1h1l1 1 1 1 1-1v-1l-1-1c0-1 0-1 1-2 1 1 1 1 1 2h1l1-1h0 1c3-6-3-13-3-19z" class="J"></path><path d="M688 659c1 2 2 5 3 9 1 2 1 5 2 7s1 3 2 5l-1 2c-1 2-1 4-1 6-2 5-6 9-10 13v-2c2-3 3-6 3-10v-1c1-3 0-5-2-7v-1l1 1 1 1 1-1v-1l-1-1c0-1 0-1 1-2 1 1 1 1 1 2h1l1-1h0 1c3-6-3-13-3-19z" class="j"></path><path d="M817 857c0-5 0-11 4-15 3-2 6-3 9-2 3 0 7 1 9 3 3 4 3 9 2 14-6-1-13 0-19 0h-5z" class="J"></path><path d="M728 859h2c-1-1-2 0-3-1l1-1 12 1c4 1 7 1 11 1l1-1h5c0-1 0 0-1-1h-1 4 2 1l1 1c1 0 1 0 2 1v-1l1-1-1-1 1-1v1h8 1l1-1h0l1 1c1 1 3 1 4 1 4 0 10-1 14 1h-9c-2 0-4 0-6 1 2 0 3 0 4 1h-1 0-1c-1 1-3 1-4 0l-2 1 1 1h0 8l1 1c-1 1-3 1-5 1 0 0-1 0-2-1l-1 1c-1-1-3-1-4 0l-1-1c-1 0-2 1-3 1v1h-1c0 1-1 1 0 3l-2-1h-1c-1 0 0 0-1-1v-1h0l-2 1c-1 0-1 1-2 0h0c-4-1-9-1-12 0h-3v-1c-2 0-3 0-5 1 2-2 2-1 4-2v-1c-2 0-2 0-4-1l-1-1h-4c-2-1-4 0-5 0-1-1-1-1-2-1l-1-1z" class="F"></path><path d="M741 862c3 0 10-1 12 1h-8c-2 0-2 0-4-1z" class="b"></path><path d="M722 698h1 2 2c1 0 2 1 3 1v1h2c1 1 2 2 3 2l2 2 1 1 2 1v-1c1 1 1 1 1 2l2 2h0c1 0 1 1 2 1 2 1 4 3 5 5l1-1c1 1 1 2 2 3h1c1 0 1 1 2 1l2 1 2 1h2v1 3h0c0-2 1-2 1-3h1l-2 8c-1-1-1-1-1-2h0c-1-1-1-1-1-3v-1c-1-2-2-2-3-3-1 0-2 0-2-1h-4-2l-1-1c-1 1-2 1-2 2l1 1v1c-3-1-5-1-8 0h0c-1-1-1-1-3 0v-1l-1-2h-2-1v4h-1c0-3 0-5-1-8-1-4-5-7-9-9 1-2 1-6 1-8z" class="N"></path><path d="M740 709c1 0 3 2 4 2h1l4 4c0 1 1 1 2 1 2 1 4 3 6 4-1 0-2 0-2-1h-4-2l-1-1-4-1c-1-1-2-3-2-4l-2-4z" class="K"></path><path d="M737 718h1v-1c0-5-3-11-7-14-2-1-3-3-4-4 5 3 9 5 13 10l2 4c0 1 1 3 2 4l4 1c-1 1-2 1-2 2-2 0-4 0-5-1h-3-1v-1z" class="P"></path><path d="M722 698h1c0 2 0 5-1 8 3 1 6 3 8 6v-1c-1-2-2-3-2-5v-1c2 1 4 4 6 6v2c2 1 3 3 3 5v1h1 3c1 1 3 1 5 1l1 1v1c-3-1-5-1-8 0h0c-1-1-1-1-3 0v-1l-1-2h-2-1v4h-1c0-3 0-5-1-8-1-4-5-7-9-9 1-2 1-6 1-8z" class="d"></path><path d="M748 718l1 1h2 4c0 1 1 1 2 1 1 1 2 1 3 3v1c0 2 0 2 1 3h0c0 1 0 1 1 2l-1 4s-1-1-1 0c-2 3-4 8-6 11l-3-1c-1 1-1 2-3 3h-1c1-2 1-2 1-4h-2 0c-3 0-3-1-5-3h-1v-2c1-1 1-2 3-3h0l-1-2h0l1-1v-1h1l1-1h-2c-2 1-3 1-4 1h-2c-1 1-2 1-3 1v-1c0-2 1-3 2-4v-2c0-1 2-2 3-2h0c3-1 5-1 8 0v-1l-1-1c0-1 1-1 2-2z" class="L"></path><path d="M734 730c1 0 2-1 3-1 0 0 1 0 1-1h1c2-2 6-1 8 0h1c1 1 3 2 4 4-1 0-2 0-3-1h-1 0c-2-1-3-1-5 0v-1h1l1-1h-2c-2 1-3 1-4 1h-2c-1 1-2 1-3 1v-1z" class="G"></path><path d="M748 728c1 0 2 0 3 1l2 3h2c0 2-1 3-1 5-1 0-1 2-1 3l-2 3c-1 1-1 2-3 3h-1c1-2 1-2 1-4l2-2c1-1 2-2 2-4s-2-4-3-5c1 1 2 1 3 1-1-2-3-3-4-4z" class="g"></path><path d="M743 731c2-1 3-1 5 0h0 1c1 1 3 3 3 5s-1 3-2 4l-2 2h-2 0c-3 0-3-1-5-3h-1v-2c1-1 1-2 3-3h0l-1-2h0l1-1z" class="B"></path><path d="M741 739c0-1 1-2 2-3 0 0 0-1 1-1 1 1 2 1 3 2 0 1-1 2-1 2v3h0c-3 0-3-1-5-3z" class="C"></path><path d="M748 718l1 1h2 4c0 1 1 1 2 1 1 1 2 1 3 3v1c0 2 0 2 1 3h0c0 1 0 1 1 2l-1 4s-1-1-1 0c-2 3-4 8-6 11l-3-1 2-3c0-1 0-3 1-3 0-2 1-3 1-5v-1l-1-1v-1c0-2-2-4-4-5h-1-1c-2-2-6-2-8-1-2 0-3 1-4 3h0v-2c0-1 2-2 3-2h0c3-1 5-1 8 0v-1l-1-1c0-1 1-1 2-2z" class="V"></path><path d="M748 718l1 1h2l1 1c1 2 2 3 4 5h1l2 2v2c-1 0-2 0-2-1-2-1-5-3-5-5-2-1-3-1-5-1v-1l-1-1c0-1 1-1 2-2z" class="k"></path><path d="M748 718l1 1 1 2c1 0 1 0 2 1v1c-2-1-3-1-5-1v-1l-1-1c0-1 1-1 2-2z" class="f"></path><path d="M751 719h4c0 1 1 1 2 1 1 1 2 1 3 3v1c0 2 0 2 1 3h0c0 1 0 1 1 2l-1 4s-1-1-1 0c-2 3-4 8-6 11l-3-1 2-3s1 0 1 1h1c2-4 3-7 4-12v-2l-2-2h-1c-2-2-3-3-4-5l-1-1z" class="T"></path><path d="M752 720c2 0 3 1 5 3l-1 2c-2-2-3-3-4-5z" class="h"></path><defs><linearGradient id="T" x1="752.273" y1="626.492" x2="726.834" y2="685.417" xlink:href="#B"><stop offset="0" stop-color="#b7b6b6"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#T)" d="M745 624l3 1c-8 9-14 20-13 33 0 2 2 4 4 6 1 1 3 1 5 1 2-1 2-1 3-2 1 1 2 1 3 2 0 1 1 2 0 3-2 2-5 3-7 3-3-1-6-2-8-4 0 4 0 21-2 22-1 0-2-1-4-1v-27c0-6 0-12 1-17 1-2 3-4 4-6 2-5 7-10 11-14z"></path><path d="M659 693c4 3 7 6 12 7 0-5 1-12 3-16 1-3 2-4 5-5-1 3-1 6-1 9v13c2 1 3 0 5 1h1l-1 4c-1 2-2 5-2 8 1 6 3 10 4 16l2 8c-2-1-6-3-7-5l-1-1c-8-7-8-21-8-31-4-1-10-3-12-7h-1l1-1z" class="C"></path><path d="M676 708c0 4 0 8-1 13v-2s-1 0-1-1v-7c0-1 1-2 2-3z" class="B"></path><path d="M676 708v-3h0l1 2 1-1v-1l-1-1h4v2h-1c-1 1-1 2-1 3 0 2 1 4 2 5 1 6 3 10 4 16l2 8c-2-1-6-3-7-5 0-1 0-2-1-3h0c0-1-1-2-2-3v-1l-1-2c0-1-1-2-1-3 1-5 1-9 1-13z" class="E"></path><path d="M714 769h-1c1-2 0-4 0-5s0-2 1-3c0-3-1-7 0-9 2 2 0 11 1 14v1c2-2 6-1 8-1v-10-3c3 0 6 0 8 2 2 1 3 4 4 6 0 0 0 2 1 3h2 1l1-2h0l2-2c1-1 2-1 2-2l3-4 1 1c0 1 1 1 2 1 3 0 6 1 9 1 5 0 10 0 15 1h3v1h1 2c1 1 3 0 5 1h1 0c-1 2-3 1-5 2-1 0-2-1-3 0-2 1-5 0-7 0 1 1 4 0 5 2-1 1-4 0-5 0h-18c-1 0-4 0-5 1h-3-1c-1 0-2 0-2 1l-1-1c-1 0-2 1-3 0h-3v1c-1 1-5 0-6 1h-1 5c0 1 1 0 1 0 2 0 4 0 5 1-8 0-18-1-25 1z" class="j"></path><path d="M747 754l1 1c0 1 1 1 2 1 3 0 6 1 9 1 5 0 10 0 15 1-9 1-20-1-30 0l3-4z" class="C"></path><path d="M723 756h1c1 2 0 7 0 9h3 3c2-1 3 0 4 0v1h-7c-4 1-8 1-11 1h-1c2-2 6-1 8-1v-10z" class="H"></path><path d="M724 484l82 207c0 1 1 2 0 3l-32-77c-6-1-14 0-19 3-3 1-5 3-7 5l-3-1c9-7 18-9 28-9l-27-69v12l-1-1c-1-2 0-2 0-4 1-1 0-4 0-6v2l-1 1v-2-4-1c0-1 0-1-1-2h0v4c-1 3 0 5-1 7v-5-11c-2-7-5-13-7-18-3-8-6-17-10-25-1-2-3-4-4-6 1 0 2 1 3 2v1c1 1 1 2 2 3 0-1-1-4-2-5v-4z" class="S"></path><path d="M758 771h1l1 1c3 0 6-1 9 0h3 1c-1 0-2 0-3 1-1 0-1 0-2 1h1c6 0 13 0 18 1 2 0 3-1 5 0h2c3 0 6 0 9 1h0 0-1c1 1 0 1 1 1 1 1 2 1 3 2h0c-7 1-15 1-22 1h-4-72c-1 0-1 0-2-1l1-1c2-2 3-3 5-3 0-1 0-1-1-1l1-2c2 1 7 0 10 0 4 0 8-1 13-1 7 0 15 1 23 0z" class="J"></path><path d="M758 771h1l1 1c3 0 6-1 9 0h3 1c-1 0-2 0-3 1-1 0-1 0-2 1h1c-4 1-10 1-14 1h-10-15-2-10c-1 0-3-1-4 0h-2c0-1 0-1-1-1l1-2c2 1 7 0 10 0 4 0 8-1 13-1 7 0 15 1 23 0z" class="C"></path><path d="M764 721h2c3 4 5 9 7 13s5 9 7 13c0 1 1 2 1 2v1h0c1 1 1 0 1 2 0 0-1 1-2 1h0c1 1 3 1 4 1 1 1 2 2 2 3l-9 1h-3c-5-1-10-1-15-1-3 0-6-1-9-1-1 0-2 0-2-1l-1-1 7-10c2-3 4-8 6-11 0-1 1 0 1 0l1-4 2-8z" class="E"></path><path d="M754 744c2-3 4-8 6-11 0-1 1 0 1 0-1 5-3 9-5 13-1 1-5 5-5 6l-3 3-1-1 7-10z" class="L"></path><path d="M751 752c3 1 6 1 9 1 6 0 14-1 20 0h0c1 1 3 1 4 1 1 1 2 2 2 3l-9 1h-3c-5-1-10-1-15-1-3 0-6-1-9-1-1 0-2 0-2-1l3-3z" class="B"></path><path d="M681 656c1-1 3-2 4-1l1 1h1c0 1 1 1 1 2v1c0 6 6 13 3 19h-1 0l-1 1h-1c0-1 0-1-1-2-1 1-1 1-1 2l1 1v1l-1 1-1-1-1-1h-1c-1-1-2-1-4-1-3 1-4 2-5 5-2 4-3 11-3 16-5-1-8-4-12-7 0-3 2-6 3-9l6-16c1-3 2-5 3-8v-2l1 1c3-2 6 0 9-2v-1z" class="M"></path><path d="M681 657l2 2v1c-4-1-8 1-12 0v-2l1 1c3-2 6 0 9-2z" class="l"></path><path d="M681 656c1-1 3-2 4-1l1 1h1c0 1 1 1 1 2v1c0 6 6 13 3 19h-1 0v-2c-1-2-2-3-4-4h-1v-3c-1-3-1-6-2-9v-1l-2-2v-1z" class="G"></path><path d="M681 656c1-1 3-2 4-1l1 1h1c0 1 1 1 1 2l-2-1c-1 2-1 3 0 5v1c0 2 2 3 1 6h0c-1-1-1-2-1-2-1 1-1 1-1 2-1-3-1-6-2-9v-1l-2-2v-1z" class="L"></path><path d="M744 597l1 1c-19 23-35 48-40 78-1 4-1 10-1 14 10-3 18-4 28-1h1c6 4 12 7 17 13 1-8 4-14 10-19 6-4 13-6 20-5 6 1 11 4 15 9 3 5 5 11 4 18-1 5-5 10-10 14h-1c-2 0-2 1-2 2h-1l-1 1c-3 2 1-1-1 1-1 0-1 0-2 1h0-3c2-1 6-4 7-7h0c0-1 2-2 2-2 2-3 4-5 5-8 2-5 2-9 0-13s-6-8-10-9c-5-2-11-1-16 2-4 2-8 5-9 10 0 1 0 2 1 4h0c2 1 2 3 4 4 1 0 2 2 3 2l1 1s1 1 2 1h2l2-1v1l-2 2v1c2-2 4-3 5-5s1-4 0-6c0-1-1-1-2-2-1 0-2 1-3 2-2 0-2-1-3-2 0-1 0-2 1-3s3-2 4-2c2 1 5 2 6 4 2 3 2 6 1 9-1 4-3 7-7 9-3 2-8 2-11 1-7-2-12-8-17-12-12-10-27-15-41-7-6 3-10 8-12 15-1 5-1 11 2 16 1 2 4 5 7 6h1c1 0 3 0 3 1h9v-1c1 0 1 0 2-1h1c1-1 2-2 2-3 1-1 1-2 2-3s0-4 2-6c-1-1-1-2-1-2v-2-3h0l1 1 1 1h1l3 2-1 1v6c1-2 1-4 1-5h1v7c1-2 1-5 1-7h1v7l1-2v-3h1v-4h1 2l1 2v1c2-1 2-1 3 0-1 0-3 1-3 2v2c-1 1-2 2-2 4v1c1 0 2 0 3-1h2c1 0 2 0 4-1h2l-1 1h-1-1l-1 1c-2 1-4 3-6 5-8 7-13 10-23 11-10 0-18-3-25-9-1-8-4-18-2-26 1-9 7-15 14-20l-2-7c0-6 2-13 3-18 3-12 8-23 14-33 9-13 20-24 30-37z" class="E"></path><path d="M755 704c0-3 1-5 2-7 0 1 0 2 1 4h0c0 1-1 1-1 2v1h-2z" class="I"></path><path d="M758 701c2 1 2 3 4 4 1 0 2 2 3 2l1 1s1 1 2 1h2l2-1v1l-2 2v1c-1 1-2 1-4 1-2 1-5-1-6-2-3-2-4-4-5-7h2v-1c0-1 1-1 1-2z" class="F"></path><path d="M731 723h1v-4h1 2l1 2v1c2-1 2-1 3 0-1 0-3 1-3 2v2c-1 1-2 2-2 4v1c1 0 2 0 3-1h2c-2 1-3 2-5 2l-2 2h-1v-1-1l2-2h0c0-1 1-2 1-3v-1h0c-1 0-1 0-2 1v3c-1 2-4 5-5 6s-1 1-2 1l-2 2v-1h-1l-1-1 2-2c4-1 1-12 3-15v6c1-2 1-4 1-5h1v7c1-2 1-5 1-7h1v7l1-2v-3z" class="D"></path><path d="M700 735h1c1 0 3 0 3 1h9v-1c1 0 1 0 2-1h1c1-1 2-2 2-3 1-1 1-2 2-3s0-4 2-6c-1-1-1-2-1-2v-2-3h0l1 1 1 1h1l3 2-1 1c-2 3 1 14-3 15 1-2 1-4 1-7h-1c0 3 0 5-1 7h-1v-2c-1 1-1 1-1 2v2l-1-1-5 2-1-1-1 1c-5 1-8 0-12-3z" class="F"></path><path d="M709 820c1-11 3-20 12-27 8-7 18-11 29-10 10 1 20 5 26 13s9 17 8 27c-1 9-7 17-14 23s-15 8-25 9l-1-1c-5-1-11-1-17-4-4-3-7-6-11-10-1-2-2-3-3-5-2-5-3-9-4-15z" class="J"></path><path d="M721 810c0 1 1 1 1 2v4l-1-2-1 1c0-2 0-3 1-5z" class="e"></path><path d="M725 801l1 1h0v1h0c-1 2-2 4-3 7l-1 2c0-1-1-1-1-2 0-3 2-7 4-9z" class="H"></path><defs><linearGradient id="U" x1="746.239" y1="790.058" x2="725.092" y2="801.228" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#434242"></stop></linearGradient></defs><path fill="url(#U)" d="M725 801c4-5 9-8 14-9 2-1 4-1 5-2l1-1h2c1 2 2 2 4 3s3 1 5 2l-1 1c-2-1-5-2-7-2l-1 1c0 3 0 3 2 6l-3-1c-2 0-4 1-5 1l3-3c1 0 1 0 2-1l-2-1v-1c-4 0-9 1-11 4-3 1-5 3-7 5h0v-1h0l-1-1z"></path><path d="M733 798c2-3 7-4 11-4v1l2 1c-1 1-1 1-2 1l-3 3c-2 1-3 2-4 2-2 3-3 5-5 7l-1 1c-1 1-2 1-3 1s-1 1-2 1c1-3 2-5 4-7v-1h0c0-2 1-3 3-5h0v-1z" class="V"></path><path d="M730 805h1c-1 1-1 1-1 2h1c1-2 3-3 5-4l1-1c-2 3-3 5-5 7l-1 1c-1 1-2 1-3 1s-1 1-2 1c1-3 2-5 4-7z" class="X"></path><path d="M733 798c2-3 7-4 11-4v1l-2 2c-1 0-1 1-2 1l-4 2c-2 1-4 2-6 4h0c0-2 1-3 3-5h0v-1z" class="S"></path><path d="M720 815l1-1 1 2c-1 7 1 13 6 19 4 4 10 7 16 7 1-1 0-1 1-2h1v2c2 1 3 1 4 0-1 1-2 2-3 2v1 1c-2 2-1 6-2 9l-1-1h0c1-1 0-8 0-8-1 0-3-1-4-1-6-2-11-5-15-10-5-6-6-13-5-20z" class="F"></path><path d="M747 789c10 3 18 6 23 15 4 6 5 14 3 20-2 8-6 14-13 18-4 2-9 3-13 4v-1-1c1 0 2-1 3-2 0 0 3 0 4-1 7-2 11-6 15-12 2-6 3-14 1-20-3-7-8-11-15-14l1-1c-2-1-3-1-5-2s-3-1-4-3z" class="T"></path><path d="M733 798v1h0c-2 2-3 3-3 5h0v1c-2 2-3 4-4 7 1 0 1-1 2-1s2 0 3-1c-2 4-3 8-3 12v1c2 2 3 5 4 7l1 4c2 2 5 4 8 4 1 1 3 1 4 2-1 1 0 1-1 2-6 0-12-3-16-7-5-6-7-12-6-19v-4l1-2c1-3 2-5 3-7 2-2 4-4 7-5z" class="C"></path><path d="M728 822v1c2 2 3 5 4 7l1 4c-2-2-4-5-6-8v-2-2l1 1v-1z" class="m"></path><path d="M726 812c1 0 1-1 2-1s2 0 3-1c-2 4-3 8-3 12v1l-1-1v2 2c-2-5-1-9-1-14z" class="T"></path><path d="M747 794l1-1c2 0 5 1 7 2 7 3 12 7 15 14 2 6 1 14-1 20-4 6-8 10-15 12-1 1-4 1-4 1-1 1-2 1-4 0v-2c1-1 4-1 5-1l1-3c1 0 3-1 4-2 2-1 4-3 6-5s1-6 3-9v-3-2h0c-2-2-1-4-3-5-2-3-4-5-6-7-3-2-5-4-7-3-2-3-2-3-2-6z" class="B"></path><path d="M756 834c2-1 4-3 6-5l1 2c-4 4-7 6-12 8l1-3c1 0 3-1 4-2z" class="c"></path><path d="M747 794l1 3h1c4-1 9 3 12 6l3 3c2 3 2 5 3 8s1 6 0 10h-1c0 3-2 5-3 7l-1-2c2-2 1-6 3-9v-3-2h0c-2-2-1-4-3-5-2-3-4-5-6-7-3-2-5-4-7-3-2-3-2-3-2-6z" class="h"></path><path d="M747 794l1 3h1c4-1 9 3 12 6l3 3v1h-1c-2-3-4-6-7-7l-1 1c4 3 7 5 9 10h1v4c-2-2-1-4-3-5-2-3-4-5-6-7-3-2-5-4-7-3-2-3-2-3-2-6z" class="T"></path><path d="M741 800c1 0 3-1 5-1l3 1c2-1 4 1 7 3 2 2 4 4 6 7 2 1 1 3 3 5h0v2 3c-2 3-1 7-3 9s-4 4-6 5c-1 1-3 2-4 2l-1 3c-1 0-4 0-5 1h-1c-1-1-3-1-4-2-3 0-6-2-8-4l-1-4c-1-2-2-5-4-7v-1c0-4 1-8 3-12l1-1c2-2 3-4 5-7 1 0 2-1 4-2z" class="b"></path><path d="M752 820h2l-1 1h0c-2 2-6 3-9 3h-2v-1h1c1 0 2-1 3-1 2 0 4 0 6-2z" class="I"></path><g class="F"><path d="M758 828c1 0 1 0 2 1-2 2-3 3-4 5-1 1-3 2-4 2h-3c2-1 3-2 4-3 2-2 4-3 5-5z"></path><path d="M732 830l6 6h6 5 0 3l-1 3c-1 0-4 0-5 1h-1c-1-1-3-1-4-2-3 0-6-2-8-4l-1-4z"></path></g><path d="M749 836h3l-1 3c-1 0-4 0-5 1h-1c-1-1-3-1-4-2 2 0 3 1 4 1 2-1 3-2 4-3h0z" class="e"></path><path d="M761 824c0-1 1-2 1-3 0-6-2-11-6-16h0c2 1 3 4 5 5h1c2 1 1 3 3 5h0v2 3c-2 3-1 7-3 9s-4 4-6 5c1-2 2-3 4-5-1-1-1-1-2-1 1-1 1-2 1-3 1 0 2 0 2-1z" class="K"></path><path d="M758 828c1-1 1-2 1-3 1 0 2 0 2-1 0 2 0 4-1 5-1-1-1-1-2-1z" class="N"></path><path d="M553 74l10-1 8 22c1-1 1-1 1-2 11 22 17 46 26 68 1 1 5 11 5 11l26 67c2 6 5 11 6 17l17 43 13 34c1 0 2 0 3 1l6 16-1 2 51 132v4c1 1 2 4 2 5-1-1-1-2-2-3v-1c-1-1-2-2-3-2 1 2 3 4 4 6 4 8 7 17 10 25 2 5 5 11 7 18v11 5l-3 16c-4 0-9-1-12 0v1h8l-2 2 1 1-2 1h-2v1h0l-1-1c-3-1-8-1-11 0 0 1 0 3-1 5v1 2h-1c-3 1-4 1-6 1l-1-1c-1 0-2 0-3 1h0c-1-1-2-1-3-2h-1v-3h2l-2-1c0-1 0-2 1-3-2-1-5-1-7-1l7 48c1 5 2 13 1 18-1 1-2 1-4 1-1 0-1 1-2 1h-1v13c0 4 1 7-1 10-2 0-2-1-2-2l-1-1c-1-3-4-7-7-9v3l1 1v1h-1l-1-1c-1-1-3 0-4 1v1c-3 2-6 0-9 2l-1-1v2c-1 3-2 5-3 8l-6 16c-1 3-3 6-3 9l-1 1c0 1-1 3-1 5 0 6 5 15 7 21l14 31c-3-1-19-41-21-47l-7-17-20-52-5-11-2-2c-1-2 0-5 0-8v-13-67-66l-1 1h-1c-1 2-1 3-1 5-2 1-3 0-5 1l-2-1v-5h-1c-2 0-3 0-4-1v-6-38-1l-6 2h-5-2v-3c0-1 0 0-1-1v-3h-4-2-6-4c-1 0-1-1-2-1 0-1-1-3-1-3l-4-12-19-53-8-23 1-1c-1-1 0-3 0-4 3-17 2-33-3-49-6-14-12-28-22-40l-1-2c-1-2-1-3-1-5l2 3 1-1h0l1 1h2l-10-10c-1 0-1 0-2-1h0l-3-2v-1l-2-1-3-3-3-3h1 3l-16-16v-2c-10-8-18-17-26-27l-2-4c0-1 0-1-1-2-2-6-3-15-1-22 0-1 0-3 1-4 1-2 1-3 2-4h-14c3-2 9-1 12-1h3l1-2c2-2 3-4 5-5v-1c-1 0-2 0-3-1s-2-2-2-3c-1-2-1-3 0-4s1-1 1-2c-3 1-6 2-8 4-10 6-19 15-24 26-2 4-3 9-4 13l-1-2c2-10 6-19 12-26 14-16 34-20 53-22l-3 1c-2 0-4 1-6 1l-13 3c2 2 5 4 6 7h0c-2 1-3 3-4 5v1h1l1 1 2-2c2 0 3-1 5-1h3c2 0 3-1 4-1 4-2 4-3 8-3 2 0 3 0 4-2 7 0 14 1 21 2l14 2c2 1 4 1 6 1v-5l1-3V79v-3l1-1h2c1 1 1 1 1 2 1 1 1 1 2 1v-1-1l-1-1v-1h5 0z" class="J"></path><path d="M584 274l2-1c0 1 1 1 1 1 0 2 0 4 1 5v2l-4-7z" class="X"></path><path d="M609 310c2 2 2 3 2 6-2 1-2 1-2 2h-1c-1-3 0-5 1-8z" class="I"></path><path d="M660 341l1 1 1-1c1 0 2 0 3 1v2c-1 2-6 1-9 1l-1-1h3l2-3z" class="O"></path><path d="M610 331c-2-4-4-9-5-13l1-1c1 0 1 1 2 1h1v4 2c1 2 2 5 1 7z" class="F"></path><path d="M588 281v-2c-1-1-1-3-1-5l3 5c0 1 1 1 2 2l2 8-1 2c-2-3-4-7-5-10z" class="V"></path><path d="M617 384l3 18h0-2l-3-16 2-2z" class="e"></path><path d="M637 645c2 1 2 5 3 7s2 3 2 5l1 3h0-1-1c-2-4-4-9-5-14l1-1z" class="F"></path><path d="M581 271c2-2 5-1 7-1h1l1 2c0 3 1 6 2 9-1-1-2-1-2-2l-3-5s-1 0-1-1l-2 1-3-3z" class="i"></path><path d="M588 270h1l1 2c0 3 1 6 2 9-1-1-2-1-2-2-1-2-1-3-1-4l-2-2c1-1 1-2 1-3z" class="X"></path><path d="M665 333c1 0 2 0 3 1l6 16-1 2c-4-5-6-13-8-19z" class="T"></path><path d="M596 317l-5-17h0l10 25c1 2 2 6 2 7h0l-1-1h0v2 4l-1-2c-1-7-2-12-5-18z" class="K"></path><path d="M574 292c5 2 6 10 9 14 1 1 1 2 1 3l3 6c0 2 1 3 2 4v3c0 2 3 5 4 7v1l-2-2c-1-2-2-4-3-5 0-1-1-1-1-3s-2-5-3-8c-1-1-2-4-3-5l-2-2c0-1 0-2-1-3s-1-2-3-4l-1-6h0z" class="B"></path><path d="M489 189c1 1 18 14 18 14 0 2 1 3 2 4v1l-3-3c-1-1-1-1-2-1 0 1 3 3 4 4l-1 1-2-2-16-16v-2z" class="c"></path><path d="M589 270h6c0 3 0 5 1 8v2h-1c1 2 1 5 1 7l-2 2-2-8c-1-3-2-6-2-9l-1-2z" class="O"></path><path d="M589 270h6c0 3 0 5 1 8v2h-1c0-1 0-2-1-3v-1-3h-1v3l1 3-1 1c-2-2-2-5-3-8h0l-1-2z" class="U"></path><path d="M466 139c0 2 0 5 1 8 0 1 1 3 1 4 1 1 1 1 1 2v1c2 2 3 4 4 6 2 3 4 5 6 7 0 1 0 1 1 2l2 1h1c1 2 3 3 4 6-2-2-4-4-6-4-1 0-2-1-2-2-9-7-14-18-15-30l2-1z" class="d"></path><path d="M680 381h1c1 2 8 18 7 20-2 1-4 1-6 0-1-1-1-3-1-5 0-5-1-10-1-15z" class="M"></path><path d="M596 317c3 6 4 11 5 18h0l-1-1c-1 1 0 6 0 8v1c1 3 1 5 3 8h0l2 17h0v9c-1-5-2-10-2-15 0-2-1-4-1-6 0-1 0-3-1-4-1-2-2-4-2-6l-1-5 1-1-1-1c1-2 0-5 0-7h1v-2-2l-1-1 1-1c-1-1-2-2-2-3v-1 1c0 1 1 2 0 3-1-3-1-6-1-9z" class="C"></path><path d="M467 147h1v2l1-2c0 2 1 4 1 6 1-1 1-1 1-2l1-1 3 8h1l5 8c3 3 8 5 10 9h-1 0c-2-1-4-3-7-4v-1h-1l-2-1c-1-1-1-1-1-2-2-2-4-4-6-7-1-2-2-4-4-6v-1c0-1 0-1-1-2 0-1-1-3-1-4z" class="P"></path><path d="M469 137l1-4v-1c0-2 1-4 2-6l1 1-1 4h1 1c0 2-1 4-2 6-1 4 0 9 1 13v1c1 2 2 5 3 7h-1c-1-3-2-5-3-8l-1 1c0 1 0 1-1 2 0-2-1-4-1-6l-1 2v-2h-1c-1-3-1-6-1-8l1-1v3c1-1 1-2 2-4z" class="T"></path><path d="M467 138v3c1-1 1-2 2-4v8h1c1 1 1 3 2 4v1l-1 1c0 1 0 1-1 2 0-2-1-4-1-6l-1 2v-2h-1c-1-3-1-6-1-8l1-1z" class="l"></path><path d="M646 383h3c1 1 2 19 2 23v3h-1l-2-3h0s-1-1-1-2v-3c-2-4-1-12-2-17h0l1-1z" class="b"></path><path d="M663 567h1c2-3-2-32-2-37-1-3-2-6-2-9 0-1 1-3 1-4 0 1 1 2 1 4l1 6 4 26 1 14c-1 0-4 1-5 0z" class="F"></path><path d="M663 495c1 1 1 1 1 2 1 4 2 8 4 12 4 15 8 31 11 47 0 2 1 5 1 8h-1v-1c-1-1-1-1 0-3-1-1-1-2-1-4-1-1-1 0-1-1v-1-1-2-1c-1-1-1-2-1-4-1-1-1-1-1-2v-4c-1-1-1 0-1-1v-1-1-2c-1-1-1-3-1-4l-1-1c1-1 1-1 0-1l-1-4v-2l-1-2v-1-1c-1-1-1-2-1-3v-1c0-1-1-3-1-4v5c1 2 2 6 2 8l1 3 1 6v1l5 26v4l-1 1-11-62-2-8z" class="C"></path><path d="M482 116h3l-5 5c-2 1-7 5-7 6l-1-1c-1 2-2 4-2 6v1l-1 4c-1 2-1 3-2 4v-3l-1 1-2 1c0-2 1-4 1-6 2-6 5-11 10-15l2-2c2 0 3-1 5-1z" class="V"></path><path d="M465 134l1-1c1-2 1-3 2-4h1c-1 3-2 6-2 9l-1 1-2 1c0-2 1-4 1-6z" class="L"></path><path d="M482 116h3l-5 5c-2 1-7 5-7 6l-1-1c2-5 5-7 10-10z" class="H"></path><path d="M483 170v1c3 1 5 3 7 4h0 1c6 3 12 5 18 8h-5l-1 1h1l3 1h1l1 1h-1l-5-1v1h0l1 1c1 0 1 0 2 1 1 0 1 1 2 1l-1 1c-9-5-19-12-26-18 2 0 4 2 6 4-1-3-3-4-4-6z" class="N"></path><path d="M578 302c1 1 1 2 1 3l2 2c1 1 2 4 3 5 1 3 3 6 3 8s1 2 1 3c1 1 2 3 3 5l2 2v-1-1l-1-1v-1c0-1 0-2-1-3v-2h0c-1-1-1-2-1-3 0-4-2-8-2-12 2 3 2 8 3 11 3 8 5 16 7 24l1 5c-3-3-5-8-7-12h0c-1-1-1-2-1-3s-2-1-2-2c-1-2-2-4-3-5-2-3-1-7-4-10-2-3-3-8-4-12z" class="S"></path><path d="M485 161c2 1 4 3 6 5v1c1 1 2 2 4 3 0 0 0 1 1 1 1 1 2 2 4 2 1 1 1 0 2 0l2 2v1l6 1c2 1 4 1 6 1-1 0-3 0-4 1l-1 2h1 0c3 1 6 2 8 4-4 0-8-1-11-2-6-3-12-5-18-8-2-4-7-6-10-9 3 1 5 5 9 5 1 0 0 0 1 1h1 1c-1-1-2-1-2-2v-1c-1 0-1 0-1-1-2-1-4-2-4-4 0-1-1-1-1-2v-1z" class="B"></path><path d="M504 176h0v1c1 2 2 0 4 1 0 1 1 1 1 2h-1l-1-1h-3c-3-2-5-3-8-4h0v-1l2 1c2 1 3 1 6 1v-1 1z" class="Q"></path><path d="M652 304l6 18c2 3 7 13 6 17-1 0-2 1-3 2h-1l-2 3h-3c-1-1-1-3-1-4v-12c0-5 0-13-1-18 0-2-1-4-1-6z" class="b"></path><defs><linearGradient id="V" x1="646.94" y1="428.946" x2="659.515" y2="430.431" xlink:href="#B"><stop offset="0" stop-color="#1c1d1e"></stop><stop offset="1" stop-color="#3f3d3d"></stop></linearGradient></defs><path fill="url(#V)" d="M651 406l10 53h-1l1 1v3h-1l-1-1c-1-5-3-10-4-14l-6-26v-9c-1-1 0-1 0-2-1-2-1-3-1-5l2 3h1v-3z"></path><path d="M677 636c0-2 1-4 1-6 0 2 1 3 2 5l6 14v2 3l1 1v1h-1l-1-1c-1-1-3 0-4 1v1c-3 2-6 0-9 2l-1-1 5-18 1-4z" class="M"></path><path d="M677 636c2 1 3 4 4 6 1 1 3 5 3 7l-1 1c0-2-1-3-2-4 0 1-1 2-1 2h-1v-1c0-2 1-4-1-6v-1-1-1l-2 2 1-4z" class="O"></path><path d="M677 636c0-2 1-4 1-6 0 2 1 3 2 5l6 14v2 3l1 1v1h-1l-1-1c-1-1-3 0-4 1 1-2 1-4 2-6l1-1c0-2-2-6-3-7-1-2-2-5-4-6z" class="f"></path><path d="M682 597l-1-25h6l5 31h-2c0 2 0 4 1 5l-2 2c0 1 0 1 1 2v2 2h-1-1v-1-3c-1-2-1-2-1-3-1-1-1-1-1-2s0-2-1-3l-1-19-1 1v1h-1v10h0z" class="K"></path><path d="M689 610v-2c-1-4 0-8-1-12 0-3-1-5-1-8h1v2c1 5 1 9 2 13 0 2 0 4 1 5l-2 2z" class="I"></path><path d="M684 585l-1-11h1c1 6 1 12 2 19 1 4 1 8 1 12v4l1 1v2c0 1 0 1 1 2v2h-1v-1-3c-1-2-1-2-1-3-1-1-1-1-1-2s0-2-1-3l-1-19z" class="b"></path><path d="M575 298c2 2 2 3 3 4 1 4 2 9 4 12 3 3 2 7 4 10 1 1 2 3 3 5 0 1 2 1 2 2s0 2 1 3h0c2 4 4 9 7 12 0 2 1 4 2 6 1 1 1 3 1 4 0 2 1 4 1 6 0 5 1 10 2 15v-9c2 3 1 5 1 8h-1c-1 0-2 0-3 1v-1l-2 1v-1c-1-6-3-12-5-19 1 0 0-4-1-5 0-3-1-7-3-9v-3c0-1 0-1-1-1-1-1-1-2-2-3 0-1-1-2-1-3s-1-3-2-4c0-2 0-3-1-4 0-1-1-1-1-2l-4-11c0-2-1-4-2-6l-1-2c0-1 0-4-1-6z" class="W"></path><path d="M589 329c0 1 2 1 2 2s0 2 1 3h0c2 4 4 9 7 12 0 2 1 4 2 6 1 1 1 3 1 4 0 2 1 4 1 6h-2c-1-1-1-5-2-6v-1c1-2 0-5-2-7-1-2-3-5-4-7-1-4-3-8-4-12z" class="G"></path><path d="M602 376h-1c0-2 0-4-1-6-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-1-2l1 1h0 0v-4l-1-1 1-1c1 1 1 5 2 6h2c0 5 1 10 2 15v-9c2 3 1 5 1 8h-1c-1 0-2 0-3 1v-1z" class="P"></path><path d="M566 223l-3-3h0c7 5 14 12 18 21 2 4 5 8 7 13 0 1 1 3 1 4 1 1 1 3 2 3 2 1 5 0 7 1l1-1c2 1 5 1 8 1 4 0 10 0 15-1 1 2 1 3 2 5v1 3h-16 0c-2 0-6-1-8 0l-1 1v-1h-4-6-1c-2 0-5-1-7 1v-2h2c-1-1-1-2-1-2v-2h0v-1c1 2 1 3 2 5h0 3c1-2 1-4 1-6 1 0 1 0 2 1v2h-1c0 1-1 1-1 2v1h6v-1c-1-1-1-3 0-5h-2c-1-1-1-1-2-1-3 1-7 1-9 0h-1 9c-4-14-12-30-23-39z" class="B"></path><path d="M663 514v-4c-1-2 0-4 0-5s1-1 2-2l11 62c1 1 1 1 0 2-2 0-4 1-7 0-1-2-1-7-1-9l-3-19c0-4 0-8-1-11 0-2-1-3-1-5 1-1 1-2 1-3-1-1-1-2-1-3v-3z" class="j"></path><path d="M520 213c-1-2-3-3-4-5l-9-7h1c2 1 4 3 6 5l11 9c5 5 10 9 14 14 11 13 20 28 27 44l8 19h0c-1 2-1 2-1 3v3c0 1 0 1 1 2v7c-1-1 0-2-1-3 0 0-1-1-1-2h0c-1-1-1-3-1-4-1-2-1-5-1-7-1-2-2-4-2-6-1-2-1-3-2-5h0-1c-1-1-2-6-3-8-3-6-5-12-9-17-7-12-16-22-26-32l-4-4c0-1-1-2-1-3-1-1-2-1-2-3z" class="F"></path><path d="M522 216c1 0 2 1 3 2l1 1c1 1 1 1 1 3h1l-1 1-4-4c0-1-1-2-1-3z" class="I"></path><path d="M670 468l-5-23c5 1 10 2 15 1 5-2 10-5 12-9 1-2 2-5 3-7 1 0 2-1 4-1 2 3 3 7 4 10l6 15c0 1 0 1-1 1l-2-1c-2-2-4-3-6-5h0c-1-1-2-1-4-1-3-1-6-1-9 1l-2 1h-2c0 1-1 1-1 2l-1 1-2 2c-1 0-1 0-2-1s-1-1-1-3l-1-1v1l-1 1v-2c-1 1-1 2-1 3h-1v-1c0-1 0-2-1-3h-1c0 4 1 11 1 14 0 2 0 2-1 3v2z" class="b"></path><defs><linearGradient id="W" x1="621.018" y1="366.046" x2="591.993" y2="341.953" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#222223"></stop></linearGradient></defs><path fill="url(#W)" d="M602 337v-4-2h0l1 1h0l12 44 1 2h-8-1l1 7v17c1 0 3 0 3 1h-2l-1 1c-1 0-2 1-3 1v1h0v2h0-1l-1-1h0v-1c-1 1-1 1-2 1 1-1 1-3 2-4 0-3 0-6-1-9v-3c-2-3-2-9-2-13-1-1-3 0-5 0-2-1-6-1-9-1v-1h3 1 5 1l4 1 2-1v1c1-1 2-1 3-1h1c0-3 1-5-1-8h0l-2-17h0c-2-3-2-5-3-8v-1c0-2-1-7 0-8l1 1h0l1 2z"></path><path d="M603 351h0c-2-3-2-5-3-8v-1c0-2-1-7 0-8l1 1h0l1 2c2 8 4 17 4 26l-1-1v-2c-1-1 0-2 0-3l-1-1c0-2 0-3-1-5z" class="D"></path><path d="M603 351c1 2 1 3 1 5l1 1c0 1-1 2 0 3v2l1 1h0c1 4 2 10 1 13 3 0 6 1 8 0l1 2h-8-1l1 7v17c1 0 3 0 3 1h-2l-1 1c-1 0-2 1-3 1v1h0v2h0-1l-1-1h0v-1c-1 1-1 1-2 1 1-1 1-3 2-4 0-3 0-6-1-9v-3c-2-3-2-9-2-13-1-1-3 0-5 0-2-1-6-1-9-1v-1h3 1 5 1l4 1 2-1v1c1-1 2-1 3-1h1c0-3 1-5-1-8h0l-2-17z" class="R"></path><path d="M602 391h0v1c1 1 1 1 1 2h1v3c1-1 1 0 1-1 1 2 1 4 1 6l-2 1h0c2 0 3 1 5 0l-1 1c-1 0-2 1-3 1v1h0v2h0-1l-1-1h0v-1c-1 1-1 1-2 1 1-1 1-3 2-4 0-3 0-6-1-9v-3z" class="K"></path><path d="M602 391v-5-1-2c-1-1-1-4-1-5 1-1 1 0 2 0 1 1 1 4 1 6 1 4 1 8 1 12 0 1 0 0-1 1v-3h-1c0-1 0-1-1-2v-1z" class="U"></path><path d="M659 462l1 1h1v-3l-1-1h1l4 14c0 1 1 4 1 5l5 20 6 24c1 5 3 10 3 14l3 12 4 19v1h-3-3c-1-1-1-3-1-4 0-3-1-6-1-8-3-16-7-32-11-47-2-4-3-8-4-12 0-1 0-1-1-2-1 0-1-2-1-3l-3-11-1-8h0c1-1 2-1 2-2l-1-9z" class="m"></path><path d="M666 495l2 9v5c-2-4-3-8-4-12l2-2z" class="g"></path><path d="M666 478l5 20h-1l-1 1c-1-1-1-3-1-4-1-4-2-8-2-12v1-1-5z" class="i"></path><path d="M660 471l6 24-2 2c0-1 0-1-1-2-1 0-1-2-1-3l-3-11-1-8h0c1-1 2-1 2-2z" class="f"></path><path d="M684 568v-6h0l-1-2c-1-2-1-4-1-6-1-6-3-11-4-16 0-2-1-3 0-5l1 1v2h1l3 12 4 19v1h-3z" class="i"></path><path d="M683 587v-1l1-1 1 19c1 1 1 2 1 3s0 1 1 2c0 1 0 1 1 3v3 1h1 1v-2-2c-1-1-1-1-1-2l2-2c-1-1-1-3-1-5h2l5 37v13c0 4 1 7-1 10-2 0-2-1-2-2l-1-1c-1-3-4-7-7-9v-2l-6-14c-1-2-2-3-2-5 1-3 2-7 2-10l2-23h0v-10h1z" class="I"></path><path d="M686 649c1 0 2 1 3 2l1 1c2 2 2 3 4 5v-2c0 1 1 2 1 3s-1 1-1 3h0l-1-1c-1-3-4-7-7-9v-2z" class="F"></path><path d="M686 609v3c2 4 1 9 2 14 0 3 1 8 0 11l-1-1v-6h0-1v-2c0-7-1-13 0-19z" class="O"></path><path d="M683 587v-1l1-1 1 19c1 1 1 2 1 3s0 1 1 2c0 1 0 1 1 3v3 1c1 5 2 10 2 15v14c0 1 0 2 1 3 0 1 0 1-1 2h0c-2-4-2-8-2-13 1-3 0-8 0-11-1-5 0-10-2-14v-3c0-1-1-2-1-2 0-3-1-6-1-9-1-3-1-7-1-11z" class="T"></path><path d="M690 603h2l5 37v13c0 4 1 7-1 10-2 0-2-1-2-2h0c0-2 1-2 1-3s-1-2-1-3l1-1-1 1c-1-1-1-2-2-2-1-2 0-6 0-7l1-3c0-1 0-4-1-5 0 3 0 6-1 9v1c-1-1-1-2-1-3v-14c0-5-1-10-2-15h1 1v-2-2c-1-1-1-1-1-2l2-2c-1-1-1-3-1-5z" class="N"></path><path d="M691 608c0 3 0 4-1 6v-2c-1-1-1-1-1-2l2-2z" class="F"></path><path d="M667 572l1 1v2-1l1-1c1-1 3-1 5-1 1 0 2 0 3 1 1 4 1 9 1 14 0 14-1 28-4 41h0c-1-2-2-4-4-7-7-14-16-31-18-48l2-1h13z" class="M"></path><path d="M668 598c0 2-1 7 1 8 1-1 0-8 1-10 0-2 0-5 1-7 0 3-1 5-1 8v8c0 3-1 4 0 7h0c-1-1-1-1-1-2-2-1-2-2-2-3v-5l1-4z" class="N"></path><path d="M667 572l1 1v2-1c1 8 1 17 0 24l-1 4c-1-7 0-14 0-22 0-2 1-6 0-8z" class="V"></path><path d="M651 480c0-3 0-6-1-9v-4-2 1 1c1 1 1 1 1 2v1c0 1 0 2 1 3v4c0 1 0 1 1 2 0 2 0 3 1 4-1 1-1 1 0 2v2h1 0v4l1 1v2c0 1 0 2 1 3 0 2 0 2 1 3v-1h0c-1-2 0-4 0-6 0 2 0 4 1 5 0-2 0-3-1-4l1-1c0 2 0 3 1 5v1c0 1 1 4 1 4 0 3 1 5 1 7v2l1 2v3c0 1 0 2 1 3 0 1 0 2-1 3 0-2-1-3-1-5 0-1-1-2-1-2l-1-7c-1-2-1-5-2-6l1 4 2 10c0 1-1 3-1 4 0 3 1 6 2 9 0 5 4 34 2 37h-1-12v-5-6-12-16h0v-7c0-1-1-1-2-2l1-11-1-21 1-7h1z" class="M"></path><path d="M650 480h1c0 1 0 3 1 4v7 27c0 3 1 8-1 10h0v-7c0-1-1-1-2-2l1-11-1-21 1-7z" class="N"></path><path d="M650 508v-4l1 1h0v11 5c0-1-1-1-2-2l1-11z" class="j"></path><path d="M595 270h4v1l1-1c2-1 6 0 8 0h0 16c2 12 3 26 2 38h0c-2-2-4-3-6-4-1 0-3 0-4-1l-5 1c-4 2-6 4-8 8-2-3-3-6-4-8s-2-5-2-7c1 1 2 1 4 1 2-2 3-3 4-6 0-1-1-3-2-4-1-2-3-2-5-2h-1l-1-6v-2c-1-3-1-5-1-8z" class="b"></path><path d="M611 304c0-3 0-15-1-17h1v2c1-1 1-1 1-2l1 1v2l1 1v-8h1v1c1 0 0 4 0 6l1 1v7 5l-5 1z" class="O"></path><path d="M687 449c3-2 6-2 9-1 2 0 3 0 4 1h0c2 2 4 3 6 5l2 1c1 0 1 0 1-1l7 20h-5-20c-2 0-3-1-4-1l-1 1c-1-1-1-1-2-1h-2 0-3v9 13c0 3 1 5 0 7h0l-1 1c-3-11-7-23-8-35v-2c1-1 1-1 1-3 0-3-1-10-1-14h1c1 1 1 2 1 3v1h1c0-1 0-2 1-3v2l1-1v-1l1 1c0 2 0 2 1 3s1 1 2 1l2-2 1-1c0-1 1-1 1-2h2l2-1z" class="N"></path><path d="M679 460h1v5c1 2 3 3 4 5-1 0-2-1-3-2v-1h-1l-1 1v-8z" class="F"></path><path d="M687 449v2h3v1h-1c-1 1-1 2-1 2l-1 3c-1 0-1 1-3 1v3c-1 1-1 2-1 3h0c-1-1-1-3-2-5 0-2 0-4 2-6l2-3 2-1z" class="L"></path><path d="M687 451h3v1h-1c-1 1-1 2-1 2l-1 3c-1 0-1 1-3 1 1-1 1-3 1-4l2-3z" class="G"></path><path d="M688 454v5c1 0 1-1 1-2v-1h2c0 1 0 3-1 5h0v2l3 4v2c-1 1-1 1-2 1l-4-1c-2-1-3-2-4-5h0c0-1 0-2 1-3v-3c2 0 2-1 3-1l1-3z" class="L"></path><path d="M687 457v6l-2 1-1-3v-3c2 0 2-1 3-1z" class="d"></path><path d="M684 461l1 3 2 3c1 0 2 1 4 3l-4-1c-2-1-3-2-4-5h0c0-1 0-2 1-3z" class="g"></path><path d="M690 463l3 4v2c-1 1-1 1-2 1-2-2-3-3-4-3l3-4z" class="f"></path><path d="M670 468v-2c1-1 1-1 1-3 0-3-1-10-1-14h1c1 1 1 2 1 3v1h1c0-1 0-2 1-3v2c0 2 0 4 1 6v2l1 1v-4c1 1 3 9 2 11v1c-1 1-2 1-4 1h-2v1l1 1c1-1 4-2 6-1v2 9 13c0 3 1 5 0 7h0l-1 1c-3-11-7-23-8-35z" class="Z"></path><path d="M687 449c3-2 6-2 9-1 2 0 3 0 4 1h0c2 2 4 3 6 5 1 2 2 3 3 5v2c0 2 1 5 1 7 1 1 1 1 1 2-1 1-2 1-3 1h-12c-2-1-7 0-9-2l4 1c1 0 1 0 2-1v-2l-3-4v-2h0c1-2 1-4 1-5h-2v1c0 1 0 2-1 2v-5s0-1 1-2h1v-1h-3v-2z" class="h"></path><path d="M700 449c2 2 4 3 6 5 1 2 2 3 3 5l-3 2c0-2-3-8-4-9s-2-2-2-3z" class="L"></path><path d="M706 461l3-2v2c0 2 1 5 1 7 0 0-1 0-1-1-1 1-1 1-2 1v-1h-1l-1-1v-3l1-1 1 1-1-2z" class="V"></path><path d="M706 461l3-2v2 3h-1-2l-1-1 1-1 1 1-1-2z" class="f"></path><path d="M690 463v-2h0c0 1 1 2 2 4 1 0 1 1 2 1v1h-1l1 1v2h1 1c1-1 2-1 2-1l1-1 1 1h0 3v-1l1-2c1 1 1 1 1 2l1 1v-1-1h1v1c1 0 1 0 2-1 0 1 1 1 1 1 1 1 1 1 1 2-1 1-2 1-3 1h-12c-2-1-7 0-9-2l4 1c1 0 1 0 2-1v-2l-3-4z" class="T"></path><path d="M690 451c1-1 3 0 5 0 2 2 4 4 4 6v3l-2 2-1 1-2 3c-1 0-1-1-2-1-1-2-2-3-2-4 1-2 1-4 1-5h-2v1c0 1 0 2-1 2v-5s0-1 1-2h1v-1z" class="R"></path><path d="M689 456h0v-1c1-2 1-2 4-2v1l-1 1-1 1h-2z" class="S"></path><path d="M692 456c1-1 2-2 3-1 1 0 1 1 2 2v2h-1c-1-1-2-2-4-2h-1l1-1z" class="D"></path><path d="M691 456l1-1v1l-1 1h1c2 0 3 1 4 2h1 1l1 1-2 2-1 1-2 3c-1 0-1-1-2-1-1-2-2-3-2-4 1-2 1-4 1-5z" class="W"></path><path d="M696 463c-3 0-3 0-5-2v-2l1 1 2-1 3 3-1 1z" class="R"></path><path d="M507 190l1-1c-1 0-1-1-2-1-1-1-1-1-2-1l-1-1h0v-1l5 1c3 1 4 1 7 1l1 1h1 0c1 0 2 1 2 1l9 3h-1-3 0l3 2c1 0 2 0 3 1h-1-2 0c1 1 1 1 2 1 1 1 2 2 3 2l-1 1c-1-1-2-1-3-2-2-1-3 0-4-1h-1c-1-1-3-2-4-3l-1 1c1 1 3 3 5 4 1 0 2 1 2 2 1 1 1 1 2 1 2 1 4 3 6 4l5 3c1 1 2 1 3 1 2 2 5 4 7 6 1 1 0 0 1 0l1 1c2 0 3 1 5 2v1c2 1 3 3 6 4l2 3h1c-2-5-6-6-8-9 3 1 6 4 9 5l1 1c11 9 19 25 23 39h-9v-1h-1c0 1-4 1-5 1-1-1-4-6-5-8-11-18-25-34-41-48-7-6-14-11-21-16z" class="j"></path><path d="M608 378h8l1 6-2 2 3 16h-1c2 2 4 0 4 3l2 63-1 1h-1c-1 2-1 3-1 5-2 1-3 0-5 1l-2-1v-5h-1c-2 0-3 0-4-1v-6-38-1-19l1-1h2c0-1-2-1-3-1v-17l-1-7h1z" class="b"></path><path d="M608 378h8l1 6-2 2v-5c-1-1-1-1-1-2v-1c-1 0-4 1-5 0h-1z" class="O"></path><path d="M616 450c1 0 3-1 4 0s1 3 1 4l-1 1h-3c0-1 0-2-1-3v-1-1z" class="E"></path><path d="M613 469h1l1-1v-50-11-4h0v-1h1v1 22 15 5 5 1 1c1 1 1 2 1 3-2 1-2 9-1 11v3h3 2c-1 2-1 3-1 5-2 1-3 0-5 1l-2-1v-5z" class="G"></path><path d="M616 425l1-5h1v4h0v-6h0c1 1 1 0 1 1v1c1 1 0 4 0 5s1 2 1 3c0 2-1 9 0 11v7 1c-1-1-1-1-1-2v-1-2c-1 1-1 2-1 2 0 2 0 3 1 5h0-2c-1-1-1-3-1-4v-5-15z" class="j"></path><path d="M608 385v-4h0c0 2 0 1 1 2v1c1-1 0-2 0-3l-1-1v-1h2 0c0 2 0 5-1 7l1 2h1c0-1-1-2 0-3 1 1 2 4 2 6 0 1-1 2 0 4v52 14 8h-1c-2 0-3 0-4-1v-6-38-1-19l1-1h2c0-1-2-1-3-1v-17z" class="I"></path><path d="M655 355c0-2-1-4 0-6h2c3 0 8-1 11 0 2 2 3 7 4 10 5 13 3 28 3 42-1 1-1 1-2 1l1 1c1 3 0 8 0 11v1c-1 2-2 5-3 7h12c0-2-1-4-2-6 0-1 0-3-1-4 1-2 1-3 2-4v-1l-1-5h8l8 20-3 1c-1 0-4 1-4 1-1 1-1 9-1 10s-1 3-2 4c-2 3-5 5-9 5-5 1-8 0-12-3-5-5-6-12-7-18l-2-19 3-1h-3c-1-8-1-16-2-24h7v-1h-7v-17-5z" class="M"></path><path d="M682 407c0 3 0 7 1 10 1 2 3 3 2 5h-2c0-2-1-4-2-6 0-1 0-3-1-4 1-2 1-3 2-4v-1z" class="C"></path><path d="M671 422h12 2l-1 2-1 1c-4-1-8-1-11-1l-1-2zm-16-67c2 0 3-1 5 0l2-1v1c0 1 0 2 1 3h1v-1c-1-1-1-1-1-3h2 1l1 1-1 2h4l1 1c-2 1-5-1-6 2-1 1-1 5-1 7l1 1v10h-1-1v-1h1v-1c0-2 0-4-1-6v-10h-8v-5z" class="I"></path><path d="M655 355c0-2-1-4 0-6h2c3 0 8-1 11 0 2 2 3 7 4 10-2 1-4 1-7 1 1-3 4-1 6-2l-1-1h-4l1-2-1-1h-1-2c0 2 0 2 1 3v1h-1c-1-1-1-2-1-3v-1l-2 1c-2-1-3 0-5 0z" class="F"></path><path d="M657 349c3 0 8-1 11 0-2 1-3 3-5 3v-2c-2 0-4 0-6-1z" class="U"></path><path d="M509 183c3 1 7 2 11 2 18 4 36 10 53 19l9 6c14 8 26 24 34 38 2 5 3 9 6 13-5 1-11 1-15 1-3 0-6 0-8-1l-1 1c-2-1-5 0-7-1-1 0-1-2-2-3 0-1-1-3-1-4-2-5-5-9-7-13-4-9-11-16-18-21h0l3 3-1-1c-3-1-6-4-9-5 2 3 6 4 8 9h-1l-2-3c-3-1-4-3-6-4v-1c-2-1-3-2-5-2l-1-1c-1 0 0 1-1 0-2-2-5-4-7-6-1 0-2 0-3-1l-5-3c-2-1-4-3-6-4-1 0-1 0-2-1 0-1-1-2-2-2-2-1-4-3-5-4l1-1c1 1 3 2 4 3h1c1 1 2 0 4 1 1 1 2 1 3 2l1-1c-1 0-2-1-3-2-1 0-1 0-2-1h0 2 1c-1-1-2-1-3-1l-3-2h0 3 1l-9-3s-1-1-2-1h0-1l-1-1c-3 0-4 0-7-1h1l-1-1h-1l-3-1h-1l1-1h5z" class="M"></path><path d="M509 183c3 1 7 2 11 2 18 4 36 10 53 19l9 6c-1 0 0 0-1 1 2 1 3 2 4 3l2 2h0l-9-6c1 1 6 5 6 6l-1 1c-3-3-6-7-9-9h-1l-1-1c-1 0-1 0-2-1h-1v1c2 0 2 1 3 2h-1c-1 0-1-1-2-1l-1-1-1-1c-1-1-2-2-4-3l-2-1c-4-2-9-5-14-7l-5-2-2-1h-2c-3-1-5-2-7-2l-1-1h-2-1c-1-1-2-1-3-1h-2c1 2 10 4 13 5h0v1l-6-2h-1l-9-3s-1-1-2-1h0-1l-1-1c-3 0-4 0-7-1h1l-1-1h-1l-3-1h-1l1-1h5z" class="F"></path><path d="M528 192h1l6 2v-1c2 1 2 1 4 1l1 1h1c1 1 2 1 3 2l8 4h0c1 0 2 1 2 1l3 2c1 0 1 0 2 1h1c1 1 2 1 3 2s2 1 2 2l2 1h0l2 2 1 1c1 0 2 2 3 2 2 3 6 6 7 9 1 0 2 1 2 2 1 1 2 1 2 3l1 1c1 0 1 1 2 2h0l4 8 3 5c1 0 1 1 1 2h-1l-1-1c0 1-1 1 0 2 0 1 0 2 1 3l1 5v1 2h0v1c-1-1-2-1-2-2s-1-1-1-2h-1c0 1-1 2-2 2 0-1-1-3-1-4-2-5-5-9-7-13-4-9-11-16-18-21h0l3 3-1-1c-3-1-6-4-9-5 2 3 6 4 8 9h-1l-2-3c-3-1-4-3-6-4v-1c-2-1-3-2-5-2l-1-1c-1 0 0 1-1 0-2-2-5-4-7-6-1 0-2 0-3-1l-5-3c-2-1-4-3-6-4-1 0-1 0-2-1 0-1-1-2-2-2-2-1-4-3-5-4l1-1c1 1 3 2 4 3h1c1 1 2 0 4 1 1 1 2 1 3 2l1-1c-1 0-2-1-3-2-1 0-1 0-2-1h0 2 1c-1-1-2-1-3-1l-3-2h0 3 1z" class="I"></path><path d="M609 310c2-2 3-2 5-2 3 0 5 0 7 2 1 3 1 7 0 9h1c1-1 3-1 4-2 1 4 0 12 0 17l1 46c2 1 3 3 5 4v-6l1-1c-1-3-1-6-1-9v-19l1-3h4 6 5l1 3h0c-1 7-1 14-1 20 1 2 1 7 0 8h-3v1h3c1 1 1 1 0 2s-2 1-3 2l-2-1v1h-2v1h1 1 0 3l-1 1h0c1 5 0 13 2 17v3c0 1 1 2 1 2h0c0 2 0 3 1 5 0 1-1 1 0 2v9l6 26c1 4 3 9 4 14l1 9c0 1-1 1-2 2h0l1 8 3 11c0 1 0 3 1 3l2 8c-1 1-2 1-2 2s-1 3 0 5v4l-1-2v-2c0-2-1-4-1-7 0 0-1-3-1-4v-1c-1-2-1-3-1-5l-1 1c1 1 1 2 1 4-1-1-1-3-1-5 0 2-1 4 0 6h0v1c-1-1-1-1-1-3-1-1-1-2-1-3v-2l-1-1v-4h0-1v-2c-1-1-1-1 0-2-1-1-1-2-1-4-1-1-1-1-1-2v-4c-1-1-1-2-1-3v-1c0-1 0-1-1-2v-1-1 2 4c1 3 1 6 1 9h-1l-1 7-1-15c-7-41-18-81-31-121l-7-20c1-2 0-5-1-7v-2-4c0-1 0-1 2-2 0-3 0-4-2-6z" class="M"></path><path d="M630 390l3 2v9c-1-2-1-5-2-7-1-1-1-2-1-4z" class="Z"></path><path d="M627 380c2 1 3 3 5 4v-6l1-1v15l-3-2c-1-3-2-7-3-10z" class="g"></path><path d="M643 346h5l1 3h-17l1-3h4 6z" class="D"></path><path d="M622 342c1 0 2 0 3 1v6c-1 0-2 0-4 1-1-1-2-1-3-2v-5c1-1 3-1 4-1z" class="J"></path><path d="M609 310c2-2 3-2 5-2l1 2v1h0v4c-1 3-1 6 0 9 0 2 1 9 0 10-1-1-1-1-1-3h0l-1-20v-1c-2 3-1 14 0 17v2l-1-1-1-12c0-3 0-4-2-6z" class="N"></path><path d="M614 308c3 0 5 0 7 2 1 3 1 7 0 9s0 9 0 12l1 1v-1l1 1-1 1h-1c-1-1-1-1-1-3-1-2 0-4-1-6v-1-4l-1 2c0 2 1 4 0 6v-6c-1 2 0 5-1 8h0v-1c-1-2-1-3-1-5v-2-11h-1l-1-2zm29 75h3l-1 1h0c1 5 0 13 2 17v3c0 1 1 2 1 2h0c0 2 0 3 1 5 0 1-1 1 0 2v9h0c-1-1-2-2-2-3l-1 1v-4l-1-1-1 1c1 5 1 9 2 13v2 2c1 2 1 4 1 6l1 1v3c0 1 0 2 1 3h0v3 2l1 1v2c-2-2-2-6-3-8 0 7 2 14 4 20 1 1 1 2 1 4v2c0 1 0 1 1 2v4 2l1 1v1h0c0 2 0 2 1 4v1h-1v-2c-1-1-1-1 0-2-1-1-1-2-1-4-1-1-1-1-1-2v-4c-1-1-1-2-1-3v-1c0-1 0-1-1-2v-1-1 2 4c1 3 1 6 1 9h-1l-1 7-1-15v-2c-1-2 0-3 0-5-1-1-1-2-1-3v-1-2-1l-1-1c0-2 0-4-1-6v-4-5c-1-1-1-3-1-4-1-1 0-3 0-4-1-1-1-3-1-4-1-2 0-3 0-4-1-2-1-2-1-3v-3c-1-1-1-2-1-3v-3c-1-1-1-1-1-2v-1c0-1 0-1-1-2v-1c0-1-1-1-1-2l-1-1c0-4-2-13 1-16 1 1 0 4 0 6 1 1 1 3 1 4h0c0-2 0-4 1-5 1 2 1 4 1 6h1 0c-1-2 0-6 0-7-1-2-1-2-1-3v-1-3-1h0v-1h2v-1h0z" class="j"></path><path d="M645 447c1 2 1 3 2 6v2c0 1 0 2 1 3v2c1 2 1 5 1 7 1 4 1 9 1 13l-1 7-1-15v-2c-1-2 0-3 0-5-1-1-1-2-1-3v-1-2-1l-1-1c0-2 0-4-1-6v-4z" class="U"></path><path d="M643 383h3l-1 1h0c1 5 0 13 2 17v3c0 1 1 2 1 2h0c0 2 0 3 1 5 0 1-1 1 0 2v9h0c-1-1-2-2-2-3-1-3-1-5-2-8 0-2 0-5-1-7 0-2 0-2 1-2 0-1-1-1-1-1l1-3c0-5-1-11-2-15z" class="H"></path><path d="M654 482h0v-1l-1-1v-2-4c-1-1-1-1-1-2v-2c0-2 0-3-1-4-2-6-4-13-4-20 1 2 1 6 3 8v-2l-1-1v-2-3h0c-1-1-1-2-1-3v-3l-1-1c0-2 0-4-1-6v-2-2c-1-4-1-8-2-13l1-1 1 1v4l1-1c0 1 1 2 2 3h0l6 26c1 4 3 9 4 14l1 9c0 1-1 1-2 2h0l1 8 3 11c0 1 0 3 1 3l2 8c-1 1-2 1-2 2s-1 3 0 5v4l-1-2v-2c0-2-1-4-1-7 0 0-1-3-1-4v-1c-1-2-1-3-1-5l-1 1c1 1 1 2 1 4-1-1-1-3-1-5 0 2-1 4 0 6h0v1c-1-1-1-1-1-3-1-1-1-2-1-3v-2l-1-1v-4h0v-1c-1-2-1-2-1-4z" class="U"></path><path d="M661 503h1l-2-11c0-3-2-9-1-11l3 11c0 1 0 3 1 3l2 8c-1 1-2 1-2 2s-1 3 0 5v4l-1-2v-2c0-2-1-4-1-7z" class="O"></path><path d="M654 482h1c0-1 0-2-1-3v-3-1-2c1 2 2 4 3 7 1 2 1 4 1 7 1 2 1 4 1 6l-1 1c1 1 1 2 1 4-1-1-1-3-1-5 0 2-1 4 0 6h0v1c-1-1-1-1-1-3-1-1-1-2-1-3v-2l-1-1v-4h0v-1c-1-2-1-2-1-4z" class="I"></path><path d="M649 422h0l6 26c1 4 3 9 4 14l1 9c0 1-1 1-2 2h0l-9-47v-4z" class="d"></path><path d="M649 519c1 1 2 1 2 2v7h0v16 12 6 5c-1 1 0 3 0 5h1 2l-2 1c2 17 11 34 18 48 2 3 3 5 4 7h0v4c-2 10-4 20-8 29-2 7-5 20-12 23h-1c-4-4-6-10-8-15h0l-4-9h1 1 0l-1-3c0-2-1-3-2-5s-1-6-3-7l-1 1-3-6c-1-3-3-7-3-10-1-5 0-10 0-14 1-18 5-34 9-51l6-22c2-8 3-16 4-24z" class="M"></path><path d="M633 640c1 1 3 1 3 2 1 0 1 1 1 2v1l-1 1-3-6z" class="I"></path><path d="M640 646h-1v-4-1c1 1 1 2 1 3 1 0 1-1 1-2l1 1h0c0 2 0 4 1 5l-1 4v-1c0-1-1-2-1-2v-1l-1-2zm6-4h0c2 5 1 11 1 16h-1-1c0-3 1-6 1-9 0-2-1-5 0-7z" class="j"></path><path d="M640 646l1 2v1s1 1 1 2v1c1 3 2 5 2 8h3 0 1l1 1c-1 1-3 0-5 0 0 2 1 4 2 5 0 1 0 2-1 3h0 0l-4-9h1 1 0l-1-3c0-2-1-3-2-5v-2-4z" class="I"></path><path d="M638 607l-1-3h-4c0-1 0-3 1-4 1-2 2-2 4-2v6h4 6v1h5-1c-1 1-2 1-3 2h0c-4-1-7-1-11 0z" class="H"></path><path d="M651 528h0v16 12 6h-1v3c-2 2-2 4-2 7-1-3-1-7 0-10 0-11 2-22 3-34z" class="Z"></path><path d="M638 598c4 0 9-1 13 1h1l1 1h0-3c1 1 2 2 4 2v1c-2 1-4 1-6 1h-6-4v-6z" class="S"></path><path d="M642 604l-1-1h0l6-1h1c0-1 0-1 1-2h1c1 1 2 2 4 2v1c-2 1-4 1-6 1h-6z" class="G"></path><path d="M658 662v4 2h-1v2l-2 4h-1c-1 0-1-1-1-2h0c-1-1-1-1-1-2v-1c0-2 0-3 1-4v-3h-2l-1-1 1-1h2v-1l1 2h1c0-3 1-5 2-7 1-1 1-1 1-2l1-1v-1l-1 5h0v3c0 1 0 1-1 2v1l-1 1h2z" class="j"></path><path d="M638 607c4-1 7-1 11 0h0l6 1h-1l-5 1h0l1 1h2c-1 1-2 1-3 1l-2 3c-3-1-5 0-8 0v-3h-1c-1 0-1-1-2-1s-1 0-2-1h1c1 1 1 0 2 0h0l-1-1 2-1z" class="C"></path><path d="M639 611h10l-2 3c-3-1-5 0-8 0v-3z" class="N"></path><path d="M641 588c3 0 5 0 7 1s2 4 3 6c-1 1-2 1-3 2-3 0-7 1-9-1-1-2-1-3 0-4 0-2 1-3 2-4z" class="E"></path><path d="M664 615l8 13-1-1h-3l-1 3v2l-1 2v2l-1 2v2 1c0 1-1 2-1 2l-1 1-1 4c0 1-1 2-1 2v2s-1 1-1 2h0c-1 1-1 0-2 1h0l1-5 1-5 2-10c1-1 1-2 1-3-1-1-1-1-1-2h1l-1-1h-1c1-2 2-4 2-6 0-1 1-2 2-3 0-1 0 0-1-1v-4z" class="I"></path><path d="M638 626h5 7 1c0 1 1 1 1 2 0 2 0 7-1 9-3 1-7 3-11 2h0l-1-1c-2 0-3-1-4-2 0-1-1-2-1-3 0-2 0-4 2-6l2-1z" class="S"></path><path d="M643 626h7v2c0 3-1 5-3 7l-1 1c-1 0-1 1-1 1h-1c-2 0-2-1-3-2-1-2-3-5-3-8h5v-1z" class="B"></path><defs><linearGradient id="X" x1="662.287" y1="593.593" x2="652.564" y2="595.28" xlink:href="#B"><stop offset="0" stop-color="#9d9c9d"></stop><stop offset="1" stop-color="#bebdbe"></stop></linearGradient></defs><path fill="url(#X)" d="M650 565v-3h1v5c-1 1 0 3 0 5h1 2l-2 1c2 17 11 34 18 48 2 3 3 5 4 7h0v4l-2-1c-1-1-1-2-1-4l1 1-8-13c-4-10-10-20-13-30-1-2-2-4-2-6l-1-7c0-3 0-5 2-7z"></path><path d="M650 574c1 1 2 5 1 7h-1l1 1v3c-1-2-2-4-2-6 1-2 1-3 1-5z" class="W"></path><path d="M650 565v9c0 2 0 3-1 5l-1-7c0-3 0-5 2-7z" class="f"></path><path d="M639 614c3 0 5-1 8 0 2 0 5 1 7 2 0 2 0 4-1 6l-2 4h-1-7-5c-1 0-2-1-3-2-2-2-3-4-3-8 2-1 5-2 7-2z" class="J"></path><defs><linearGradient id="Y" x1="663.189" y1="642.615" x2="670.651" y2="645.327" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#292929"></stop></linearGradient></defs><path fill="url(#Y)" d="M664 643s1-1 1-2v-1-2l1-2v-2l1-2v-2l1-3h3c0 2 0 3 1 4l2 1c-2 10-4 20-8 29l-2 1c-1 1-1 1-2 1h-1-1-1v-1h-1-2l1-1v-1c1-1 1-1 1-2v-3c1-1 1 0 2-1h0c0-1 1-2 1-2v-2s1-1 1-2l1-4 1-1z"></path><path d="M662 651h1c0-1 0-2 1-2 1-1 2-1 2-2 1-2 0-2 0-3 0 0 1 0 1-1s1-2 2-2v-3c1 2-1 3-1 6-1 2-2 5-3 7 0 3-2 5-2 8h-2v-1-1c0-2 0-2 1-4v-2z" class="j"></path><path d="M658 655c1-1 1 0 2-1h0c0-1 1-2 1-2v-2s1-1 1-2l1-4 1-1c0 2 0 3-1 4v1l-1 3v2c-1 2-1 2-1 4v1 1h2c0 1-1 3-1 4h-1-1-1v-1h-1-2l1-1v-1c1-1 1-1 1-2v-3z" class="F"></path><defs><linearGradient id="Z" x1="533.612" y1="136.019" x2="474.906" y2="127.36" xlink:href="#B"><stop offset="0" stop-color="#8f8e8f"></stop><stop offset="1" stop-color="#f5f4f5"></stop></linearGradient></defs><path fill="url(#Z)" d="M542 110l1-3v17 13 19l-1 16c1 2 0 8 2 10l4 1 1 1-1 1-14-4-8-2-10-1c-2 0-4 0-6-1l-6-1v-1l-2-2c-1 0-1 1-2 0-2 0-3-1-4-2-1 0-1-1-1-1-2-1-3-2-4-3v-1c-2-2-4-4-6-5v1c0 1 1 1 1 2 0 2 2 3 4 4 0 1 0 1 1 1v1c0 1 1 1 2 2h-1-1c-1-1 0-1-1-1-4 0-6-4-9-5l-5-8c-1-2-2-5-3-7v-1c-1-4-2-9-1-13 1-2 2-4 2-6h-1-1l1-4c0-1 5-5 7-6l5-5c2 0 3-1 4-1 4-2 4-3 8-3 2 0 3 0 4-2 7 0 14 1 21 2l14 2c2 1 4 1 6 1v-5z"></path><path d="M525 128c2 0 2 0 3 1v-1l-1-1 1-1c2 2 4 6 6 8 1 1 1 1 1 2v1l-2-2-1-1-2-2h-1l-1-1-3-3z" class="d"></path><path d="M473 127c0-1 5-5 7-6h2c-3 3-6 6-8 10h-1-1l1-4z" class="i"></path><path d="M497 112h3c1 0 1 1 2 1 2 1 4 0 7 0-11 0-18 2-27 8h-2l5-5c2 0 3-1 4-1 4-2 4-3 8-3z" class="h"></path><path d="M496 121h0l2-2h0 2l1 1h-2c-2 1-2 3-2 6h0l-1 1c-1 0-3 0-4 1l-2 1-3 4 1-4c0-2 0-2 2-4v-1l-3 2v-1c2-2 4-4 7-5 1 0 1 1 2 1z" class="D"></path><path d="M496 121h0l2-2h0 2l1 1h-2c-2 1-2 3-2 6h0l-1 1c-1 0-3 0-4 1l-2 1c1-2 2-3 3-5l3-3z" class="G"></path><path d="M537 126c1 1 1 2 2 4h0c2 3 1 5 4 7v19h-1v1c-1-1-1-2-1-3v-6c0-7-3-14-6-20h0l2 2v1-1-3-1z" class="K"></path><path d="M535 136c1 4 3 6 3 10 0 2 0 4 1 6v1c-1 0-3-1-4-2h0c-2-1-3-3-4-4-1 0-2-1-3-2v-1-2h3c2 0 3 0 5 1h0l-1-6v-1z" class="n"></path><path d="M523 129l-1-1v-1c1 0 2 1 3 1h0l3 3 1 1h1l2 2 1 1 2 2 1 6h0c-2-1-3-1-5-1h-3 0c-1-5-3-9-6-12l1-1z" class="G"></path><path d="M523 129l-1-1v-1c1 0 2 1 3 1h0l3 3c0 3 2 4 3 7 1 1 1 2 2 3-1 0-1 0-2 1v-1c0-4-5-9-8-12z" class="P"></path><path d="M528 131l1 1h1l2 2 1 1 2 2 1 6h0c-2-1-3-1-5-1h0c1-1 1-1 2-1-1-1-1-2-2-3-1-3-3-4-3-7z" class="W"></path><path d="M502 123h3c2 0 2 0 3 1h1c2 1 5 2 7 4v-1-1l-1-1c-1 0 0 0-1-1h1v1c2 1 3 2 4 3l3 2c3 3 5 7 6 12h0v2h-1v2c-2 0-4-1-6-2v-1h0l-1-2c0-2 0-3-1-5l-2-2v-4c-3-3-7-5-11-6-1 0-2 0-3-1h-1 0z" class="L"></path><path d="M519 136v-4c1 0 1 0 2 1 0 1 0 3 1 4s1 0 1 2c-1 0-1 0-1 1s-1 2-1 3l-1-2c0-2 0-3-1-5z" class="d"></path><path d="M521 143c0-1 1-2 1-3s0-1 1-1l2 2v-1h1v1c0 1 0 2 1 3v2c-2 0-4-1-6-2v-1h0z" class="J"></path><path d="M472 137c1 1 0 3 1 4 0-2 0-4 1-6v1c1 2 1 4 2 6v1c-1 1 0 2 0 4l1-1c1 1 0 2 0 4 1 2 2 4 4 6l4 5v1c0 1 1 1 1 2 0 2 2 3 4 4 0 1 0 1 1 1v1c0 1 1 1 2 2h-1-1c-1-1 0-1-1-1-4 0-6-4-9-5l-5-8c-1-2-2-5-3-7v-1c-1-4-2-9-1-13z" class="E"></path><defs><linearGradient id="a" x1="523.929" y1="113.775" x2="517.789" y2="122.928" xlink:href="#B"><stop offset="0" stop-color="#201f1f"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#a)" d="M542 110l1-3v17 13c-3-2-2-4-4-7h0c-1-2-1-3-2-4v1 3 1-1l-2-2h0l-2-1c-6-8-14-13-24-14-3 0-5 1-7 0-1 0-1-1-2-1h-3c2 0 3 0 4-2 7 0 14 1 21 2l14 2c2 1 4 1 6 1v-5z"></path><path d="M542 110l1-3v17 13c-3-2-2-4-4-7h0c-1-2-1-3-2-4 0-1-1-2-1-3l-5-5v-1c2 1 2 1 3 2l1 1 1 2c1 2 2 3 4 5l-3-6h0c-1-2-2-2-3-3s0-1-2-2c-1-1-3-1-5-1s-3-2-5-3h0l14 2c2 1 4 1 6 1v-5z" class="c"></path><path d="M542 110l1-3v17c0 1-1 1-1 2l-2-1c0-3-2-5-4-7h0l-1-1h-1c1-1 1-1 2-1v-2c2 1 4 1 6 1v-5z" class="H"></path><path d="M501 120h1c1-1 1-1 2-1v1c-1 1-2 1-2 3h0 1c1 1 2 1 3 1 4 1 8 3 11 6v4l2 2c1 2 1 3 1 5l1 2h0v1c2 1 4 2 6 2v-2h1v1c1 1 2 2 3 2 1 1 2 3 4 4h0c1 1 3 2 4 2l-1 4-4-2h-1c-4-2-9-7-14-8 1 0 1 0 0-1 0-2-1-3-3-4l1-1 1 1h1l-1-1c0-1 0 0-1-1h-1v-2c-1 1-1 0-1 1v1h-4 0c-1-1-3-1-4-1h-4c-2 0-3-1-5-1-1 1-4 0-5 0v-1l-1-2 1-1h0-2v-1-1h1v-1c0-1 0-1 1-1l-1-2c1-1 3-1 4-1l1-1h0c0-3 0-5 2-6h2z" class="h"></path><path d="M500 128c2 0 3 0 5-1h0c1 0 2 1 2 1-1 0-2 0-3 1 1 1 1 1 2 1-1 1-1 1-2 1v1h3v1h-4v-1h-3-2c0-1 1-1 1-2v-1l-1 1-1-1v-1h2 1z" class="Z"></path><path d="M499 128h1v1h2v1c-1 1-1 0-3 0v-1l-1 1-1-1v-1h2z" class="k"></path><path d="M507 139v-1h6l-2-2h0c1-1 0-1 2-1v-1h-1v-1h-1-2c0-1 0-1 1-1-1-1-1-1-2-1v-1h4l2 3c2 2 4 5 4 7v1c0-1 0 0-1-1h-1v-2c-1 1-1 0-1 1v1h-4 0c-1-1-3-1-4-1z" class="i"></path><path d="M528 144v1c1 1 2 2 3 2 1 1 2 3 4 4h0c1 1 3 2 4 2l-1 4-4-2h-1c-2-3-8-5-10-8-2-1-2-1-2-3 2 1 4 2 6 2v-2h1z" class="B"></path><path d="M501 120h1c1-1 1-1 2-1v1c-1 1-2 1-2 3h0 1c1 1 2 1 3 1 4 1 8 3 11 6v4c-1-2-3-3-5-5-1-1-2-1-3-1h-2s-1-1-2-1h0c-2 1-3 1-5 1h-1v-2h-2 0c0-3 0-5 2-6h2z" class="l"></path><path d="M499 126c3 0 10-1 12 2 1 0 1 0 1 1-1-1-2-1-3-1h-2s-1-1-2-1h0c-2 1-3 1-5 1h-1v-2z" class="P"></path><path d="M501 120h1c1-1 1-1 2-1v1c-1 1-2 1-2 3h0c-1 0-1-1-2-1l-1 1c1 1 2 1 3 1v1c-1 0-2 0-3 1h-2 0c0-3 0-5 2-6h2z" class="a"></path><path d="M497 126h2v2h-2v1l1 1 1-1v1c0 1-1 1-1 2h2 3v1h-2v1h5v1h-5v1h2v1c-2 0-3 0-5 1-1 1-4 0-5 0v-1l-1-2 1-1h0-2v-1-1h1v-1c0-1 0-1 1-1l-1-2c1-1 3-1 4-1l1-1z" class="f"></path><path d="M492 128c1-1 3-1 4-1v2h-2c1 1 0 1 1 2l-1 1v1h-1v1h-2v-1-1h1v-1c0-1 0-1 1-1l-1-2z" class="L"></path><path d="M493 134h3v1h0l-1 1h4 2 2v1c-2 0-3 0-5 1-1 1-4 0-5 0v-1l-1-2 1-1z" class="P"></path><defs><linearGradient id="b" x1="495.593" y1="144.224" x2="481.685" y2="140.72" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#b)" d="M487 126l3-2v1c-2 2-2 2-2 4l-1 4 3-4 2-1 1 2c-1 0-1 0-1 1v1h-1v1 1h2 0l-1 1 1 2v1c1 0 4 1 5 0 2 0 3 1 5 1h4c1 0 3 0 4 1h0 4v-1c0-1 0 0 1-1v2h1c1 1 1 0 1 1l1 1h-1l-1-1-1 1c2 1 3 2 3 4 1 1 1 1 0 1 5 1 10 6 14 8h1l4 2 1-4v-1-1h1l1-3v6c0 1 0 2 1 3v-1h1l-1 16c1 2 0 8 2 10l4 1 1 1-1 1-14-4-8-2-10-1c-2 0-4 0-6-1l-6-1v-1l-2-2c-1 0-1 1-2 0-2 0-3-1-4-2-1 0-1-1-1-1-2-1-3-2-4-3v-1c-2-2-4-4-6-5l-4-5c-2-7-3-12-2-19 2-5 4-9 8-12v1z"></path><path d="M487 125v1c-4 5-5 10-5 17-1-2-1-4-1-5-1 0-1-1-2-1 2-5 4-9 8-12z" class="T"></path><path d="M509 169h1l3 3c1 2 3 3 5 4h0c-1-2-3-4-4-5s-3-2-3-3c2 0 7 6 9 8h0c-2-5-8-9-11-12v-1c4 4 9 8 13 13v-1c1-1-1-2-1-3-2-3-7-7-10-9v-1c3 3 6 5 9 8 1 1 2 3 4 5l1-1c0-1-3-3-3-4 1 1 2 2 4 2 1 1 3 2 4 3v3h-1v-1l-1 1c-1 0-1 0-2 1l-10-1c-2 0-4 0-6-1h3c-1-1-2-2-2-3s0 0-1-1c0-1-1-3-1-4z" class="e"></path><path d="M496 157l1-1h0c-1-2-3-3-4-5h1 1 0l-2-2h1l1 1h12 9l2 1v2l-2 2c1 1 2 1 3 2 0 0 1 0 2 1v2c0 2 8 9 9 10-1 1-1 1-2 1v1c-1 0-1 0-2-1v1h0c-2 0-3-1-4-2l-1-1c-2-2-5-5-8-7-1-1-2-2-3-2h-3 0c-2 1-4 2-6 1h-1-1c-1 0-1 0-2-1h2l1 1 1-1h2 1l1-1h4c-1-1-2-1-3-1s-2 1-4 1h0-1-2c-1-1-1-1-2-1l-1-1z" class="K"></path><path d="M496 157l1-1h0c-1-2-3-3-4-5h1 1 0l-2-2h1l1 1c3 2 12 2 16 1h3v2l-1 1h-2 0c-4 1-7 1-11 1 1 1 2 1 3 1h0 6c-1 1-1 1-2 1-2 0-3 0-4 1h-1v1h0-1-2c-1-1-1-1-2-1l-1-1z" class="g"></path><path d="M511 151h3v2l-1 1h-2-5-2c-1-1-3-1-4-1h1 3c2 0 4-1 6-1h1 1l-1-1z" class="H"></path><defs><linearGradient id="c" x1="499.682" y1="177.957" x2="487.434" y2="135.226" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3c3a3a"></stop></linearGradient></defs><path fill="url(#c)" d="M479 137c1 0 1 1 2 1 0 1 0 3 1 5 2 7 4 12 10 16 3 2 9 5 12 4 1-1 1-1 2-1 1 1 2 5 3 7 0 1 1 3 1 4 1 1 1 0 1 1s1 2 2 3h-3l-6-1v-1l-2-2c-1 0-1 1-2 0-2 0-3-1-4-2-1 0-1-1-1-1-2-1-3-2-4-3v-1c-2-2-4-4-6-5l-4-5c-2-7-3-12-2-19z"></path><path d="M490 129l2-1 1 2c-1 0-1 0-1 1v1h-1v1 1h2 0l-1 1 1 2v1c1 0 4 1 5 0 2 0 3 1 5 1h4c1 0 3 0 4 1h0 4v-1c0-1 0 0 1-1v2h1c1 1 1 0 1 1l1 1h-1l-1-1-1 1c2 1 3 2 3 4 1 1 1 1 0 1l-1 4-2-1h-9-12l-1-1h-1l2 2h0-1-1c1 2 3 3 4 5h0l-1 1c-2 0-4-2-6-4h0l-1-1-1-2c-3-6-2-11-1-17l3-4z" class="W"></path><path d="M511 140h0 4v-1c0-1 0 0 1-1v2h1c1 1 1 0 1 1l1 1h-1l-1-1-1 1h-1c-2-1-3-1-5-1-3 0-9 1-12-1h1 12z" class="m"></path><path d="M501 145h3c1 0 1 1 2 1h3 2v1c0 1 0 0 1 1h0c2 0 3 0 5 1v1h-1-9c1 0 2-1 3-1-1-1-3-1-4-1-3-1-6-1-9-1h0v-1c2 0 2 0 4-1z" class="V"></path><path d="M490 129l2-1 1 2c-1 0-1 0-1 1v1h-1v1 1h2 0l-1 1-2 1c0 1 1 3 1 4h-1-1 1v1 1l-1 1h2c-1 1 0 1-1 2v3c1 1 0 2 0 3v2h0l-1-1-1-2c-3-6-2-11-1-17l3-4z" class="R"></path><path d="M510 141c2 0 3 0 5 1h1c2 1 3 2 3 4 1 1 1 1 0 1l-1 4-2-1h1v-1c-2-1-3-1-5-1h0c-1-1-1 0-1-1v-1h-2-3c-1 0-1-1-2-1h-3-6l-1-1c2 0 4 0 5-1v-1h11v-1z" class="h"></path><path d="M516 142c2 1 3 2 3 4 1 1 1 1 0 1l-1 4-2-1h1v-1c-2-1-3-1-5-1h0c-1-1-1 0-1-1v-1h-2v-1l7-1v-1h0-8c2-1 4-1 7-1h1z" class="H"></path><path d="M519 147c5 1 10 6 14 8h1l4 2 1-4v-1-1h1l1-3v6c0 1 0 2 1 3v-1h1l-1 16c1 2 0 8 2 10l4 1 1 1-1 1-14-4-8-2c1-1 1-1 2-1l1-1v1h1v-3c-1-1-3-2-4-3h0v-1c1 1 1 1 2 1v-1c1 0 1 0 2-1-1-1-9-8-9-10v-2c-1-1-2-1-2-1-1-1-2-1-3-2l2-2v-2l1-4z" class="h"></path><path d="M521 160c1 1 1 2 3 2 2 2 4 6 7 7l1 1v-1c-1-2-3-3-5-4 0-1-1-2-1-2-1-2-2-2-2-3 1 0 1 1 2 1l1 1c0 2 5 6 6 6 2 2 5 4 5 6l-1 1h-1v2 2h1v1h-1s-1 0-2 1l-8-2c1-1 1-1 2-1l1-1v1h1v-3c-1-1-3-2-4-3h0v-1c1 1 1 1 2 1v-1c1 0 1 0 2-1-1-1-9-8-9-10z" class="F"></path><path d="M541 154c0 1 0 2 1 3v-1h1l-1 16c1 2 0 8 2 10l4 1 1 1-1 1-14-4c1-1 2-1 2-1h1v-1h-1v-2-2h1l1-1c0-2-3-4-5-6 0-1 0-1 1-1l5 4c0-4 0-7 1-10l1-1v-2c-1-2 0-2 0-4z" class="H"></path><path d="M541 154c0 1 0 2 1 3v-1h1l-1 16-1 1c-1 1-1 0-1 1l-1 1v-1-2h1l1-1v-11-2c-1-2 0-2 0-4z" class="O"></path><path d="M541 173l1-1c1 2 0 8 2 10l4 1 1 1-1 1-14-4c1-1 2-1 2-1h1v-1h-1v-2l2-2c0 1 0 1 1 2v4h1l1-8z" class="b"></path><path d="M519 147c5 1 10 6 14 8h1l3 3-4 8c-4-5-9-10-15-13h0v-2l1-4z" class="J"></path><path d="M679 473h3 0 2c1 0 1 0 2 1l1-1c1 0 2 1 4 1h20 5l1 1c0 2-1 8 0 9 0 1 2 2 3 3h1c1 2 3 4 4 6 4 8 7 17 10 25 2 5 5 11 7 18v11 5l-3 16c-4 0-9-1-12 0h-1l1-1c3-3 5-7 5-12v-5c0-22-9-45-25-59-6-5-13-9-18-14l-1-1h0c4 6 10 10 16 14 13 11 22 23 25 40 2 9 3 19 2 28v1c-1 3-3 6-6 9h-2-2c-3 1-5 1-8 0-6 0-12 1-17 0l-1-1c-1-1-1-4-2-6l-3-14-12-44 1-1h0c1-2 0-4 0-7v-13-9z" class="M"></path><path d="M694 547c0-3-1-11 1-14 0 1 1 2 1 3v4 10h-1c0-1 0-2-1-3z" class="c"></path><path d="M691 541v1c1 1 1 2 1 3l2 2c1 1 1 2 1 3h1v10c0 2 0 5-1 7-1-1-1-4-2-6l-3-14c1-1 1-4 1-6z" class="i"></path><path d="M686 514c5 3 7 4 13 3l-3 3v7h-1l-1 1h2v1c-1 1-1 0-2 1h0-2v1h1c1-1 2 0 3 0v1h-2c1 1 1 1 2 1v1l-1-1c-2 3-1 11-1 14l-2-2c0-1 0-2-1-3v-1-14c-1-2 0-4-1-6v-1c0-1 0-2-1-2-1-1-2-2-2-3l-1-1z" class="X"></path><path d="M715 529l-3 2c-1 1-3 1-5 1-2-1-3-2-4-4s-2-5-1-7 3-3 4-4c3 0 4 0 7 2l1 2c1 2 1 3 1 5v2 1z" class="B"></path><path d="M706 517l2-1c0-1 1-2 2-2l1 1c2 2 6 5 7 8h0c0-2 0-3-1-4-2-1-3-3-4-4h1c-1-1-1-1-2-1l-2-2h0v-2l-2-10c7 6 9 12 11 20 0 3 1 6 0 9v1c0 1 0 1-1 3l1 1-2 2h2v1c-1 2-3 2-5 2-2 1-9 2-11 0h0 1v-1c-2 0-3-1-4-2l2-1-1-1c1-1 2-1 3-1 0 0 1 1 2 1h2c1 0 3-1 4-2 1 0 2-2 3-3v-1-2c0-2 0-3-1-5l-1-2c-3-2-4-2-7-2z" class="L"></path><path d="M686 514l-1-2c-2-3-3-7-2-10s3-5 6-7 5-2 9-1c3 1 6 3 8 6 1 3 1 7 0 10s-4 5-6 6l-1 1c-6 1-8 0-13-3z" class="n"></path><path d="M505 207l2 2 1-1c-1-1-4-3-4-4 1 0 1 0 2 1l3 3v-1c-1-1-2-2-2-4 5 3 9 7 13 10 0 2 1 2 2 3 0 1 1 2 1 3l4 4c10 10 19 20 26 32 4 5 6 11 9 17 1 2 2 7 3 8h1 0c1 2 1 3 2 5 0 2 1 4 2 6 0 2 0 5 1 7 0 1 0 3 1 4h0c0 1 1 2 1 2 1 1 0 2 1 3v-7c-1-1-1-1-1-2v-3c0-1 0-1 1-3l1 6c1 2 1 5 1 6l1 2c1 2 2 4 2 6l4 11c0 1 1 1 1 2 1 1 1 2 1 4 1 1 2 3 2 4s1 2 1 3c1 1 1 2 2 3 1 0 1 0 1 1v3c2 2 3 6 3 9 1 1 2 5 1 5 2 7 4 13 5 19v1l-4-1h-1-5-1-3v1c3 0 7 0 9 1 2 0 4-1 5 0 0 4 0 10 2 13v3c1 3 1 6 1 9-1 1-1 3-2 4 1 0 1 0 2-1v1h0l1 1h1 0v-2h0v-1c1 0 2-1 3-1v19l-6 2h-5-2v-3c0-1 0 0-1-1v-3h-4-2-6-4c-1 0-1-1-2-1 0-1-1-3-1-3l-4-12-19-53-8-23 1-1c-1-1 0-3 0-4 3-17 2-33-3-49-6-14-12-28-22-40l-1-2c-1-2-1-3-1-5l2 3 1-1h0l1 1h2l-10-10c-1 0-1 0-2-1h0l-3-2v-1l-2-1-3-3-3-3h1 3z" class="b"></path><path d="M580 336l2 3-1 6c-1-3-1-6-1-9z" class="N"></path><path d="M589 379c2 1 1 3 1 4s2 2 2 3l-1 1h-2v-8z" class="H"></path><path d="M579 405c-1-2 0-5 0-7 1 1 2 1 3 3h1l1 3c-2 0-3 1-5 1z" class="W"></path><path d="M579 323c1 3 2 5 2 8 0 2 2 5 2 7l-1 1-2-3-1-13z" class="K"></path><path d="M584 404h0v7h-4c0-2 0-5-1-6 2 0 3-1 5-1z" class="g"></path><path d="M549 266c2 5 4 9 5 14 0 1 1 4 0 5l-1 2-1-2-4-14h1v-2c-1-1-1-2-1-3l1 1v-1z" class="W"></path><path d="M553 307c2 4 0 7 2 11 0 4 1 8 0 12v-3l-1 1c0 2 1 6-1 8v-6c0-1 1-2 1-2l-1-1v-2l-1-1v-3c1-4 1-10 1-14z" class="K"></path><path d="M552 285l1 2c1 3 1 7 1 10v-1h-1v11c0 4 0 10-1 14v3 2c0 2 0 5-1 7-1-1-1-3 0-4v-7c1-12 1-25 1-37z" class="k"></path><path d="M505 207l2 2 1-1c-1-1-4-3-4-4 1 0 1 0 2 1l3 3c5 4 11 9 16 14h-1c-2-1-3-2-5-4l-3-3c-2-1-2-2-3-3v1c0 1 1 1 2 2 0 1-1 1 0 2s2 2 4 3c1 1 3 3 3 4-2-2-5-5-8-6-1 0-1 0-2-1h0l-3-2v-1l-2-1-3-3-3-3h1 3z" class="I"></path><path d="M514 218c3 1 6 4 8 6l1 1 9 10c7 9 13 20 17 31v1l-1-1c0 1 0 2 1 3v2h-1c-3-10-7-19-12-27-4-6-8-11-12-16l-10-10z" class="a"></path><path d="M574 292l1 6c1 2 1 5 1 6h-1v16l1 1v23c0 3 0 7-1 9v1 1c-2-1-1-9 0-11v-7c-1-1-1-3-1-5v-9c-1-2 0-7 0-9l-1-4-1-1v-7c0 1 1 2 1 2 1 1 0 2 1 3v-7c-1-1-1-1-1-2v-3c0-1 0-1 1-3z" class="e"></path><path d="M538 255c4 8 7 16 9 24 2 11 1 24 0 35-1 3-1 7-2 10v1c-1-1 0-3 0-4 3-17 2-33-3-49 0-5-1-7-3-12-1-2-1-4-1-5z" class="d"></path><path d="M553 287l1-2c3 13 4 28 3 42l-2 24h-1l1-21c1-4 0-8 0-12-2-4 0-7-2-11v-11h1v1c0-3 0-7-1-10z" class="G"></path><path d="M553 307v-11h1v1c1 5 0 10 1 14 1 2 1 5 0 7-2-4 0-7-2-11z" class="H"></path><path d="M589 379c-1-2-8-1-11-2h0c-2 1-1 3-2 4h-1l1-2v-1l-1-1 1-1h10v1c3 0 7 0 9 1 2 0 4-1 5 0 0 4 0 10 2 13v3c-3-2-5-4-8-5-2-1-4 0-5 0v-2h2l1-1c0-1-2-2-2-3s1-3-1-4z" class="U"></path><path d="M592 386v-2c1 1 1 2 3 4l-1 1c-2-1-4 0-5 0v-2h2l1-1z" class="F"></path><defs><linearGradient id="d" x1="573.775" y1="367.197" x2="590.751" y2="349.303" xlink:href="#B"><stop offset="0" stop-color="#080709"></stop><stop offset="1" stop-color="#2b2c2b"></stop></linearGradient></defs><path fill="url(#d)" d="M582 339l1-1c0 1 0 2 1 3 1 3 0 6 1 9l1 11v8c0 2 1 5 0 7h-10v-3c0 1 0 2 1 2s2 1 2 0h2v-30l1-6z"></path><path d="M518 225l2 3 1-1h0l1 1h2c4 5 8 10 12 16l-2 2c1 2 4 6 4 9 0 1 0 3 1 5 2 5 3 7 3 12-6-14-12-28-22-40l-1-2c-1-2-1-3-1-5z" class="f"></path><path d="M524 228c4 5 8 10 12 16l-2 2c-4-6-7-12-12-18h2z" class="O"></path><path d="M577 306c1 2 2 4 2 6l4 11c0 1 1 1 1 2 1 1 1 2 1 4 1 1 2 3 2 4s1 2 1 3c1 1 1 2 2 3-1 1 0 0 0 2 1 0 0 2 0 3l-1 1 1 2v1h-1c-1-1-1-3-1-5 0 0 0-1-1-1 0 3 0 7 1 11 0 1 0 2 1 4v3l-1 1v3h-1c0 2 0 3-1 5v-8l-1-11c-1-3 0-6-1-9-1-1-1-2-1-3 0-2-2-5-2-7 0-3-1-5-2-8l-1-9c-1-2-1-5-1-8z" class="Z"></path><path d="M578 314l1 4c1 3 3 7 4 10 0 2 0 4 1 6s1 5 2 7c0 2 1 5 0 6 1 5 2 10 2 14v3h-1c0 2 0 3-1 5v-8l-1-11c-1-3 0-6-1-9-1-1-1-2-1-3 0-2-2-5-2-7 0-3-1-5-2-8l-1-9z" class="T"></path><path d="M586 361h1v-5c-1-3-2-6-1-9 1 5 2 10 2 14v3h-1c0 2 0 3-1 5v-8z" class="N"></path><path d="M590 339c1 0 1 0 1 1v3c2 2 3 6 3 9 1 1 2 5 1 5 2 7 4 13 5 19v1l-4-1h-1-5-1-3c1-2 0-5 0-7 1-2 1-3 1-5h1v-3l1-1v-3c-1-2-1-3-1-4-1-4-1-8-1-11 1 0 1 1 1 1 0 2 0 4 1 5h1v-1l-1-2 1-1c0-1 1-3 0-3 0-2-1-1 0-2z" class="k"></path><path d="M596 376c0-5 0-10-1-15v-4h0c2 7 4 13 5 19v1l-4-1z" class="T"></path><path d="M592 366l1 1c1-2 0-2 1-4l1 10v3h-5v-5-3c1-1 1-2 2-2z" class="K"></path><path d="M590 371l1 1v3h1v-3c1 0 2 1 3 1v3h-5v-5z" class="e"></path><path d="M589 348h1v-1l-1-2 1-1 3 14c0 1 1 3 1 5-1 2 0 2-1 4l-1-1v-2c-1-3-1-7-2-10-1-2-2-4-1-6z" class="X"></path><path d="M588 361l1-1v-3c-1-2-1-3-1-4-1-4-1-8-1-11 1 0 1 1 1 1 0 2 0 4 1 5-1 2 0 4 1 6 1 3 1 7 2 10v2c-1 0-1 1-2 2v3 5h-1-3c1-2 0-5 0-7 1-2 1-3 1-5h1v-3z" class="c"></path><path d="M586 369c1-2 1-3 1-5h1c0 2 1 5 1 7v5h0-3c1-2 0-5 0-7z" class="F"></path><path d="M589 376h-1c-1-2 0-3 0-5h1v5z" class="I"></path><path d="M589 389c1 0 3-1 5 0 3 1 5 3 8 5 1 3 1 6 1 9-2 0-4 1-5 1l-1-1v1h-5-2v2l-1-1v-1h-2-3 0l-1-3h-1c-1-2-2-2-3-3 0-1 1-3 2-4 1-3 4-4 8-5z" class="C"></path><path d="M586 395c1-1 3-2 5-1 2 0 4 0 5 2l1 3v4 1h-5-2v2l-1-1v-1h-2-3 0l-1-3c1-3 1-4 3-6z" class="F"></path><path d="M586 395c1-1 3-2 5-1 2 0 4 0 5 2l1 3c-1 1-2 2-4 3h-1c1-1 1-2 0-3 0-1 0-1-1-1h-1l-1 3-1 1h-2v-6-1zm11 8l1 1c1 0 3-1 5-1-1 1-1 3-2 4 1 0 1 0 2-1v1h0l1 1h1 0v-2h0v-1c1 0 2-1 3-1v19l-6 2h-5-2v-3c0-1 0 0-1-1v-3h-4-2v-2c-1-1-1-2-1-3-1-2-1-2-3-2v-7h3 2v1l1 1v-2h2 5v-1z" class="I"></path><path d="M590 418l2-1c0-1 0-1 1-2v1c1 0 2 1 3 2h-2-4z" class="F"></path><path d="M589 405l1 1v-2h2c1 2 0 4 1 6v1 1l-2-1h-2 0v-1-3-2zm-5-1h3l1 2-1 1 1 1v3 1h0l-1 1c-1-2-1-2-3-2v-7z" class="O"></path><path d="M597 403l1 1c1 0 3-1 5-1-1 1-1 3-2 4 1 0 1 0 2-1v1h0l1 1h1 0v8l-6 2c0-2 0-3-1-5l-1 1c-1-2 0-8 0-10v-1z" class="F"></path><path d="M597 403l1 1c1 0 3-1 5-1-1 1-1 3-2 4 1 0 1 0 2-1v1h0l-1 1 1 2c-1 1-1 2-2 2v-2h-1c0 2-1 2-2 3l-1 1c-1-2 0-8 0-10v-1z" class="R"></path><path d="M605 408v-2h0v-1c1 0 2-1 3-1v19l-6 2h-5-2v-3c0-1 0 0-1-1v-3h2 3l6-2v-8z" class="Y"></path><path d="M604 419h2v1l-2 2h-1v-2l1-1z" class="D"></path><path d="M507 203c5 3 9 7 13 10 0 2 1 2 2 3 0 1 1 2 1 3l4 4c10 10 19 20 26 32 4 5 6 11 9 17 1 2 2 7 3 8 1 4 2 7 3 11 2 11 1 23 0 34-1 7-1 14-2 20l-2 11c-1 3-2 6-2 9v-4c1-1 1-2 1-3h-1c0 1-1 2-1 3v1-8-1-2c2-21 3-44-1-64-2-19-11-37-23-52-3-5-8-10-12-13-5-5-11-10-16-14v-1c-1-1-2-2-2-4z" class="P"></path><path d="M507 203c5 3 9 7 13 10 0 2 1 2 2 3 0 1 1 2 1 3l-14-12c-1-1-2-2-2-4z" class="b"></path><path d="M563 298c-1-6-1-12-2-17-2-8-5-15-8-22 8 12 13 23 14 38-1 1-1 2-2 3v5 1l-1-1c0-3-1-5-1-7z" class="H"></path><defs><linearGradient id="e" x1="574.67" y1="342.009" x2="551.83" y2="325.991" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#323031"></stop></linearGradient></defs><path fill="url(#e)" d="M567 297c1 5 0 10 0 16 0 15-2 30-4 45h-1c0 1-1 2-1 3v1-8-1-2h0c2-10 2-19 3-29v-11c-1-4-1-9-1-13 0 2 1 4 1 7l1 1v-1-5c1-1 1-2 2-3z"></path><path d="M553 74l10-1 8 22c1-1 1-1 1-2 11 22 17 46 26 68 1 1 5 11 5 11l26 67c2 6 5 11 6 17l17 43-2 1c1 1 2 2 2 4s1 4 1 6c1 5 1 13 1 18h0-2c0-1 0-3-1-4v2c0 3-1 8-2 10 0 2 0 8-1 9v1h-5-6-4v-22c0-13 0-26-2-39-2-19-9-41-21-57-1 0-2-2-3-3l1-2c-5-3-9-8-13-12-14-13-29-21-47-26l1-1-1-1-4-1c-2-2-1-8-2-10l1-16v-19-13-17-28-3l1-1h2c1 1 1 1 1 2 1 1 1 1 2 1v-1-1l-1-1v-1h5 0z" class="M"></path><path d="M553 116h2c-1 1-1 3-1 4v2c0 2-1 3-1 5-1-1-2-1-2-3l1-1c1-3-1-5 1-7z" class="O"></path><path d="M608 223c2 2 4 3 7 4v1h-2c1 1 0 1 1 1 1 1 2 1 3 1-1 1 0 1-1 1-2 0-4-2-6-3-1 0-2-2-3-3l1-2z" class="D"></path><path d="M615 227l6 1 2 5c-2-1-5-1-7-2 1 0 0 0 1-1-1 0-2 0-3-1-1 0 0 0-1-1h2v-1z" class="L"></path><path d="M573 186v1c-1 1-2 3-4 4-2-1-3-1-4-2 2-1 3-3 4-5h1c1 1 2 2 3 2z" class="R"></path><path d="M640 305c0-2 0-4-1-6 0-3 1-6 0-8v-2-1c1 2 1 5 2 7 0 5 0 9 1 14v2c1 2-1 7 1 9v5 13c0 2 1 5 0 7h0c-2-2-1-7-1-10h-1v5 4 1h0c-3-1 0-34-1-40z" class="N"></path><path d="M650 300h0c1 1 2 2 2 4s1 4 1 6c1 5 1 13 1 18h0-2c0-1 0-3-1-4v2c0 3-1 8-2 10 0 2 0 8-1 9v-30h1v2-1c1-4 1-9 0-14 0-1 1-1 1-2z" class="R"></path><path d="M571 178c-1 2-2 5-4 7v-1c0-1 1-3 2-4v-1l1-3v-2h0c1-4 1-7 1-11v-9c0-1-1-3 0-4 1 0 1 1 2 1v3c0 5-1 9-1 13 1-4 1-10 4-13 0 0 2 1 2 2 0 2 0 6-1 9-1 5-2 10-5 14l-1-1z" class="N"></path><path d="M571 178v-3s1-1 1-2v-3c1 0 1 0 1-1s2-4 4-4c-1 5-2 10-5 14l-1-1z" class="O"></path><path d="M629 239c2 6 5 11 6 17l17 43-2 1h0l-16-42c-3-6-5-12-7-18 1 0 1 1 1 1h1l-1-1 1-1z" class="D"></path><path d="M629 239c2 6 5 11 6 17l-1-1h0c0-1-1-3-1-4-1-1-1-2-2-3 0 2 1 3 1 4s1 2 1 3c1 1 1 2 1 3-3-6-5-12-7-18 1 0 1 1 1 1h1l-1-1 1-1z" class="C"></path><path d="M550 123l1 1c0 2 1 2 2 3v26c0 9 0 18-1 27v2l-1 1h0-1v-2h0c0 1 0 2-1 3l-1-1c2-3 1-11 2-14v-16-30z" class="K"></path><path d="M567 171v1c0 2 0 5-1 7-1-1-1-4-1-5h0l-1 8h-1v-7h-1c0 3 1 6 0 9v3c0 1 0 0-1 1h0c0-5 1-10 0-14-1-2-1-2 0-3-1-2-1-5-1-6 0-5 1-12 0-17v-3c3 2 1 9 2 12 2-1-1-12 1-15l1 1v1 12c1 0 1 1 2 1v1c0 4 0 9 1 13zm70 175v-11c-1-1-1-3-1-5-1-2 0-16 0-19 1-2 0-3 0-4 1-2 1-5 1-6l-1-1c0-3 0-9 1-10 1 1 0 2 0 3 0 2 1 12 1 12 0-2 1-4 0-6v-8c0 1 0 2 1 4s-1 5 0 8h0l1 2h0c1 6-2 39 1 40h0v-1-4-5h1c0 3-1 8 1 10v1h-6z" class="I"></path><path d="M593 182h5c2 1 4 2 5 5 2 2 2 5 1 8s-3 4-6 6h-3c-3-1-6-2-8-5-1-2-1-5-1-7 1-4 4-6 7-7z" class="n"></path><path d="M572 93c11 22 17 46 26 68 1 1 5 11 5 11l26 67-1 1 1 1h-1s0-1-1-1c-4-7-6-15-9-23l-15-39c-2-4-3-8-5-12-1-1-1-2-2-3h-2c-6-2-9-3-14-6 1 0 2 2 2 2 0 2-1 5-1 7-1 7-4 14-8 20-1 0-2-1-3-2l2-5c3-4 4-9 5-14 1-3 1-7 1-9 0-1-2-2-2-2-4-4-8-9-9-14-1-2-1-4-2-6v-2c0-1-1-2-1-3 0 3 1 7 1 10 0 2 1 3 1 5h1v16 11c-1-4-1-9-1-13 0-5 1-12 0-16l-1-1c-1-7-4-15-5-22h0l3 6c0 2 1 3 2 5 1 3 2 7 4 10h0c-1-3-2-7-3-10s-3-6-4-9v-1c4 7 6 15 10 22 1 3 3 8 6 8 1-7-2-16-4-23 4 6 5 18 6 25h0c1-8-2-20-6-27h0c-2-3-5-6-7-8v-1c2 3 5 5 7 8 6 9 7 19 8 30 3 3 9 5 13 6 0-3-2-7-3-9l-21-56c1-1 1-1 1-2z" class="S"></path><path d="M543 79v-3l1-1h2c1 1 1 1 1 2 1 1 1 1 2 1 0 5 1 11 1 16 0 2 1 4 1 6v-3h1c0 2-1 5 0 7h1v5c1 1 2 1 3 1-1 0-3 0-3 1v5c-2 2 0 4-1 7l-1 1-1-1v30 16c-1 3 0 11-2 14l-4-1c-2-2-1-8-2-10l1-16v-19-13-17-28z" class="J"></path><path d="M543 79v-3l1-1h2c1 1 1 1 1 2 1 1 1 1 2 1 0 5 1 11 1 16 0 2 1 4 1 6v-3h1c0 2-1 5 0 7h1v5c1 1 2 1 3 1-1 0-3 0-3 1v5c-2 2 0 4-1 7l-1 1-1-1v30 16c-1-8-1-17-1-25v-38-14c-1-5-1-11-5-14l-1 1z" class="W"></path><path d="M550 94c0 2 1 4 1 6v-3h1c0 2-1 5 0 7h1v5c1 1 2 1 3 1-1 0-3 0-3 1v5c-2 2 0 4-1 7l-1 1-1-1V94z" class="N"></path><path d="M553 74l10-1 8 22 21 56c1 2 3 6 3 9-4-1-10-3-13-6-1-11-2-21-8-30-2-3-5-5-7-8v1c-2-1-9-1-12-1h-2v-5c0-1 2-1 3-1-1 0-2 0-3-1v-5h-1c-1-2 0-5 0-7h-1v3c0-2-1-4-1-6 0-5-1-11-1-16v-1-1l-1-1v-1h5 0z" class="M"></path><path d="M549 78c1 3 2 6 2 9l1 1v9h-1v3c0-2-1-4-1-6 0-5-1-11-1-16z" class="O"></path><path d="M553 104c2-1 7-3 9-2 2 0 3 1 5 1v1h-1v3c1 1 2 1 2 2 0 2 0 5-1 7v1c-2-1-9-1-12-1h-2v-5c0-1 2-1 3-1-1 0-2 0-3-1v-5z" class="J"></path><path d="M429 150c1-4 2-9 4-13 5-11 14-20 24-26 2-2 5-3 8-4 0 1 0 1-1 2s-1 2 0 4c0 1 1 2 2 3s2 1 3 1v1c-2 1-3 3-5 5l-1 2h-3c-3 0-9-1-12 1h14c-1 1-1 2-2 4-1 1-1 3-1 4-2 7-1 16 1 22 1 1 1 1 1 2l2 4c8 10 16 19 26 27v2l16 16h-3-1l3 3 3 3 2 1v1l3 2h0c1 1 1 1 2 1l10 10h-2l-1-1h0l-1 1-2-3c0 2 0 3 1 5l1 2c10 12 16 26 22 40 5 16 6 32 3 49 0 1-1 3 0 4l-1 1 8 23 19 53 4 12s1 2 1 3c1 0 1 1 2 1h4 6 2 4v3c1 1 1 0 1 1v3h2 5l6-2v1 38 6c1 1 2 1 4 1h1v5l2 1c2-1 3 0 5-1 0-2 0-3 1-5h1l1-1v66 67 13c0 3-1 6 0 8l-1 1c-3 0-16 1-18-1-2 0-3 0-5 1h-14-48 0c-2 1-3 1-5 1h0l2 1h0c-6 0-13 1-20-1l-1-1c-2 0-3 0-4-1h-3v-1-5h-3c-6-1-12-2-18-4-3-1-5-2-7-4-4-1-9-7-12-9h0c-2-2-4-7-5-9l-1 1c1 1 2 3 2 5h0v3h-1-4-1l-2-2h-2v15 1l-1 7c-1 1-1 1-2 1h2l1 2h-4-11c-2-1-5-1-7 0h-40c-2 0-3 1-5 0l-1 2c-1 2-2 5-3 8l-5 12c-10 26-17 53-31 77-3 6-6 11-10 16-3 4-7 8-10 12-2 2-3 5-5 8-1 2-1 4-2 6-1 3-2 6-2 9 0 1-1 1-1 2v1l-1 1v5c-1 4 0 9 1 14 2 11 5 21 12 30 5 7 11 14 18 19 6 5 12 7 19 10 4 1 9 2 13 4h-17c-4 0-7-1-10 0h-8c-6 0-11-1-17-4l1 2c-1 2-2 1-4 2h-62c0-1 0-1-1-2h0c-1-2-2-3-1-4v-1c-1-2-1-4 0-6h-6v7 3 2c-1 0-3-1-5 0h0-48c-8 0-15-1-22 0l-5 1-1-1-1 5-7 1c0-5 2-10 2-14 0-1 0-1 1-2h-1l-1 1v6l-1-3v5c0-2 0-3-1-4v-6c-1-1-1-3-2-5v-1c-3-1-7 0-10 1 7-6 13-10 18-17 7-9 11-19 17-29 0-1 1-3 2-4 3-5 6-10 10-15 0-1 0-2-1-2-3 2-5 4-8 7l8-19 13-30 8-22 46-120c2-6 4-11 6-17l10-24c2 0 2 0 4-1l1-1c0-1 0-2 1-3v-2h-3l25-66c1-2 2-4 2-6l7-17 2-6 6-15 11-26 3-10-2-2 11-25 1 1 2-2c1 0 2 0 3 1 1-1 0-2 0-3l-2-2c1-5 3-9 5-14l8-21 34-86 11-27c1-4 3-9 5-13h1v-2c0-2 1-4 2-6l3-9s1-2 1-3l3-10c1-3 2-6 3-10 0-4 0-9 2-14z" class="M"></path><path d="M341 525c2-1 4 0 6 0h-6zm5-15h1c-1 1-3 1-5 1 0-1-1-1-1-1h5z" class="I"></path><path d="M334 528l2 2 1-1h0 1v3c-1 0-2-1-3-1l-1-3z" class="H"></path><path d="M367 411h0l-2 11v1c0-4 0-8 2-12z" class="I"></path><path d="M353 595h7c-2 1-3 0-4 2v2h-1-2v-4z" class="L"></path><path d="M353 599h2v6c-1-1-2-1-3-1l1-5h0z" class="T"></path><path d="M301 753v-6c2-1 2-1 3 0 0 2-1 3-1 5v1c-1-1-1-1-2 0z" class="k"></path><path d="M351 475h-11c-1-2-1-4 0-6v3c2 1 4 1 5 1l1 1c1 1 4 0 5 1z" class="W"></path><path d="M380 294h2c1 1 2 1 3 3h-2c-1 1-3 1-5 1v-1c1-2 1-2 2-3z" class="D"></path><path d="M396 253c2 2 5 2 7 3v-1l1-1h0l3 3h-6c-2 0-3-1-5-2v-2z" class="a"></path><path d="M282 703c1 1 1 0 2 0l1 1-7 9-1-1h0c1-1 2-3 2-4l1 1c1-2 2-4 2-6z" class="L"></path><path d="M250 759l6 1c1 1 0 2 0 3-2 1-6 0-8 0l1-1h5 1 0c0-2-3-1-4-1 0 0-1-1-1-2z" class="l"></path><path d="M313 522c1 3 2 8 1 10l-2-1 1-1h-2v-3h0c1-2 2-3 2-5z" class="c"></path><path d="M340 534l15-2v1h-6c-3 1-4 2-5 5l-1 1-1-1c0-2-1-3-2-4z" class="a"></path><path d="M220 831c2 3 4 14 2 17v1l-1-1-1-17z" class="W"></path><path d="M377 302h0c2-1 4 0 6 1v1l-1 1c-2 1-5 0-6 0-1-2 0-2 1-3z" class="Y"></path><path d="M299 535h1v2h1v-3l1 1v1l1 1h1l1 1 1-1h1v1 2h-1c-1 1-3 0-4-1h-1-1l-1-1c1-1 1-1 0-2v-1z" class="O"></path><path d="M349 689l1-1 5 2v1c-3 2-14 1-16 1h-1c3-1 7 0 10-1l-1-1h2v-1z" class="g"></path><path d="M336 407c1-1 2-1 4-1 1 0 2 1 2 1 1 1 1 1 1 3-2 1-4 1-6 0-1 0-1 0-2-1l1-2z" class="a"></path><path d="M267 728l1-1c1 0 2 1 3 2v4h-1c-3 0-3 0-5-2 0-2 1-2 2-3z" class="L"></path><path d="M196 775c1-2 3-2 5-3 2 0 5 1 7 2-1 1-1 2 0 4l-3-2-2-1-1-1c-2 1-3 2-4 3h-1c0-1 0-2-1-2z" class="W"></path><path d="M404 254h1v-1c3-1 5-1 8 0-1 1-1 3-3 4h-3l-3-3z" class="C"></path><path d="M307 491l-3-1v-3c2-1 3-1 5-1l3 1c0 1-2 1-2 2h1v1c-1 1-2 1-3 2l-1-1z" class="f"></path><path d="M396 253c1-1 1-1 1-2 1-2 2-3 4-4-1 1-1 2-2 3h1l1 1c1 0 2 0 3 1v2l-1 1v1c-2-1-5-1-7-3z" class="G"></path><path d="M185 806c1-1 2-1 3-2l1 2c1 2 1 4 0 7h0 0-3v3 10l-1-20z" class="V"></path><path d="M189 806c1 2 1 4 0 7h0 0-3v-3h1c1-1 1-3 2-4z" class="P"></path><path d="M369 378c3 0 6 0 9-1 1 1 0 2 0 3s0 1-1 2h1v4h-3l1-2c-2-3-5-3-7-6h0z" class="S"></path><path d="M301 753c1-1 1-1 2 0v3 11c0 1 0 2-1 3h-2l1-5v-12z" class="l"></path><path d="M301 765c0-2 0-3 1-5h0l1 7c0 1 0 2-1 3h-2l1-5z" class="R"></path><path d="M384 544c1 1 1 2 2 3-2 2-4 4-5 6v1 1c-1 0-1 0-1 1l-3-3c2-4 4-6 7-9z" class="C"></path><path d="M340 543c3-1 6-1 9-1-1 1-3 1-4 3v2h0 4l2 2h-13c1-1 2-1 4-1v-1c-1-1-1-1-1-2v-2c-1 1-1 1-2 1l1-1z" class="B"></path><path d="M192 825c1 3 0 6 2 9 0 5 0 11-1 16v4c-1-1-1-2-1-3v-9c-1-2-1-5 0-8v-9z" class="R"></path><path d="M329 541v1 7h-6v-7c2-1 4-1 6-1z" class="G"></path><path d="M251 650c0-3-1-9 1-11h4c0 4 0 8-2 10-1 1-2 1-3 1z" class="C"></path><path d="M309 526c1 1 1-2 2-2v3 3h2l-1 1 2 1v1c-1 1-2 1-2 2-2-1-2-1-3-3h0l-1 1c-1 1 0 2-1 3-1-1-1-2-1-2v-8s0-1 1-1v1l1 1v-2h1v1z" class="N"></path><path d="M309 526c1 1 1-2 2-2v3 3h2l-1 1h-2-1v-5z" class="H"></path><path d="M282 703c1-2 2-5 1-7v-2c-1-2-1-5-3-7-3-4-8-8-13-9-3 0-6 1-9 0h3c4-1 10 0 13 2 4 3 8 7 10 12h0v1c2 1 1 4 2 6l1 4-1 2v-1h-1l-1-1c-1 0-1 1-2 0z" class="d"></path><path d="M348 468l1 1v1c1 0 2 0 3 1-1 2-1 2 0 4h-1c-1-1-4 0-5-1l-1-1c-1 0-3 0-5-1v-3c2-1 5 0 8-1z" class="D"></path><path d="M294 701s-1-1-2-1l1-1c3 0 5 3 8 3l2-2c0 4-1 7-4 11h-3l-1 1h-4c1-1 1-1 3-1l3-3h0v-4h0l1-2h-1l-3-1z" class="Y"></path><path d="M329 551c2 1 3 2 5 2 0-1 0-1-1-2 1-1 2-1 3-1v2l-1 1v1 1h0-2v1l3 1c-2 0-4-1-5 0v1l-1 1v10l1 1v2l-1 1-1-9c-2-1-4-1-6-1v-1h6v-3-6-2z" class="F"></path><path d="M366 570h4c1 2 2 3 2 5-1 2-1 2-2 3-2 1-4 0-5-1s-2-2-2-4l3-3z" class="C"></path><path d="M336 395l11-25 1 1-10 26-2-2z" class="d"></path><path d="M323 563c2 0 4 0 6 1l1 9h-7v-10z" class="C"></path><path d="M401 247c3-1 6-1 9 0 2 1 2 2 3 4v2c-3-1-5-1-8 0v1h-1 0v-2c-1-1-2-1-3-1l-1-1h-1c1-1 1-2 2-3z" class="B"></path><path d="M352 604c1 0 2 0 3 1 0 3 1 6 0 8v1h1v1c-2 3-1 9-1 12 0 2-1 2-2 3-1-1-1-1-1-2 0-3 1-12-1-14 2-2 1-8 1-10z" class="h"></path><path d="M301 739h0c-1-1-1-4-2-4l-1 2h-1c-2-1-4-1-6-1v-1h5c1-1 3-2 3-3h-1-1-1l3-1 2-1c0-1 0-1 1-2-1 0-2 0-3-1 1 0 2-1 2-1l1-1h2l1 1c0 1-1 1-1 2 1 1 1 1 0 2v4c-1 1-1 0-1 2v2l-2 1z" class="O"></path><path d="M251 656h4l1 1c0 4 1 9-1 13h-3 0c-2-3-2-12-1-14zm72-94c-1-3 0-8 0-12l6 1v2 6 3h-6z" class="C"></path><path d="M329 559l-2-2c0-2 1-3 2-4v6z" class="Y"></path><path d="M245 778v-15c-2 0-6 1-8 0 0-2 0-2 1-3 4-1 8-1 12-1 0 1 1 2 1 2 1 0 4-1 4 1h0-1-5l-1 1-1 1c0 4 0 9-1 13l-1 1z" class="P"></path><path d="M336 486c-1-3-1-7-1-10l1-17c0-2-1-6 0-8l1-1c1 1 0 5 0 7l1 27h-1c-1 3 0 5-1 7v-5z" class="Z"></path><path d="M330 534h10c1 1 2 2 2 4l1 1h-6l-4 1h-3-9l8-1v-1c0-2 0-3 1-4z" class="L"></path><path d="M329 538v-1c1 0 1-1 1-2h7v1c-1 1 0 2 0 3l-4 1h-3-9l8-1v-1z" class="f"></path><path d="M297 702h1l-1 2h0v4h0l-3 3c-2 0-2 0-3 1h-1l-2-2v5h-1v-9l-1-1 1-2c2 1 2 1 4 1 0-1 2-1 3-1h1c1 0 1 0 2-1z" class="G"></path><path d="M297 702h1l-1 2h0v2h-2c-1 1-3 1-4 0v-2h0c0-1 2-1 3-1h1c1 0 1 0 2-1z" class="D"></path><path d="M186 813h3 0c-1 7-2 16-1 23v3l-1 5-1 5-1 1 1-24v-10-3z" class="a"></path><path d="M186 813h3 0c-1 7-2 16-1 23v3c-2-7 1-16-2-23v-3z" class="d"></path><defs><linearGradient id="f" x1="336.328" y1="597.978" x2="352.314" y2="599.712" xlink:href="#B"><stop offset="0" stop-color="#727071"></stop><stop offset="1" stop-color="#989899"></stop></linearGradient></defs><path fill="url(#f)" d="M353 599c-5 1-10 2-14 4-2 1-5 5-7 5 1-1 1-2 2-2v-1l-1-1 2-3-1-1c2 0 5-1 7-2l12-3v4h0z"></path><path d="M297 528h1c1-1 1-1 1-2l1-1h0l1 2h1 1c1-1 1 0 1-1l1-1v1h1v8l-1 1v-2 1l-1 1h0l-2 1v-1l-1-1v3h-1v-2h-1l-1 1v1l-1-1c1-1 1-1 1-2v-1l-1 1c0 1 0 0-1 1h-1c0 1 0 2-1 2 0-2 2-7 3-9z" class="U"></path><path d="M255 776l-1 9h-1l-2 1c-3 0-5 1-8 1l2-7v-2l1-1v2h1l2-2c2 0 4 0 6-1z" class="E"></path><path d="M246 777v2h1l2-2c0 1 0 2-1 3v4l1 1h0 4l-2 1c-3 0-5 1-8 1l2-7v-2l1-1z" class="C"></path><path d="M330 540h3l1 1h0c2 1 4 1 6 2l-1 1c1 0 1 0 2-1v2c0 1 0 1 1 2v1c-2 0-3 0-4 1-2 1-6 0-8 0h-1v-7-1l1-1z" class="F"></path><path d="M330 540h3l1 1h0v1c0 1 0 0 1 1 0 2 0 3-1 5l-2 1-1-1c0-1 1-1 1-2s-1-1-2-2c0-1-1-1-1-2v-1l1-1z" class="I"></path><path d="M314 533c5 0 11-1 16 1-1 1-1 2-1 4v1l-8 1h-7l-2-5c0-1 1-1 2-2z" class="B"></path><path d="M188 836c0-2 1-5 2-6h1l1 4c-1 3-1 6 0 8v9c0 1 0 2 1 3v1c-3 0-5-1-7 0h0l-1-1v-4l1-1 1-5 1-5v-3z" class="T"></path><path d="M187 844l2-5 1 3h0v-2c1 3-1 8 1 11h-1v-7c-1 1-1 0-1 1 0 3 0 4-2 6 0 1-1 1-1 2l-1 1v-4l1-1 1-5z" class="m"></path><path d="M185 854l1-1c0-1 1-1 1-2 2-2 2-3 2-6 0-1 0 0 1-1v7h1 1c0 1 0 2 1 3v1c-3 0-5-1-7 0h0l-1-1z" class="U"></path><path d="M314 847c1 0 2-1 3-1 1 1 2 1 4 1-1-1-1-2-2-2l1-1 4 4 2 2h2l7 5c1 0 1 0 2 1 2 0 4 2 5 3-6 0-11-1-17-4l-11-8z" class="j"></path><path d="M312 513v4l1 5c0 2-1 3-2 5h0v-3c-1 0-1 3-2 2v-1h-1v2l-1-1v-1c-1 0-1 1-1 1h-1v-1l-1 1c0 1 0 0-1 1h-1-1l-1-2h0l-1 1c0 1 0 1-1 2h-1l4-10c1 0 1-1 2-2v4h0c1 0 2-1 2-3h1v1h1c0-1 1-1 2-2 0 1 0 1 1 1 0-1 1-2 2-4z" class="P"></path><path d="M312 517l1 5c0 2-1 3-2 5h0v-3c-1 0-1 3-2 2v-1h-1v-4h1v2h1c0-2 1-3 2-4v-2z" class="V"></path><path d="M333 575c2 0 9-1 11 1 0 1 1 3 0 5h1v-6l1-1c1 2 1 5 1 7h-2c-1 0-1 1-2 1h-1l-15 1c1-1 1-1 0-1v-1c1 0 1-1 2-1h0c1-2 0-3 0-5h4z" class="f"></path><path d="M329 575h4l3 1c-1 1-1 2-2 4-1 0-2 0-2 1 3 1 8-1 10 1l-15 1c1-1 1-1 0-1v-1c1 0 1-1 2-1h0c1-2 0-3 0-5z" class="W"></path><path d="M349 550c4 0 10 0 15 1l-3 1c2 1 7-1 8 1h-2c-1 0-2 0-2 1h0c2 0 3 0 4 1v1h-2c-3 0-5 0-7 1h-13l-1-1c1 0-1-1-1-2 1-2 2-2 3-3l1-1z" class="J"></path><path d="M314 777h-1v1 1c-2 4-1 11-1 15v5c1 1 1 1 1 2 0 4 1 8 2 12l2 5-1 1c0-1 0-1-1-2l-2-8-1-3-1-7c0-3-1-6-1-9 1-6 2-13 3-20l-1 1c0 1 0 2-1 3v2l-1-1v-2l1-1c0-3 2-6 3-9 1-2 2-4 3-5v1 1h-1l1 1c1-1 1-2 2-3h0l1-1v1c-1 2-1 4-2 6-1 3-2 6-2 9 0 1-1 1-1 2v1l-1 1z" class="N"></path><path d="M163 852v-4c1-1 2-1 4-1 0 1-1 7-1 7 2-3 0-11 3-13h5c0 1 0 1-1 1-2 5-2 11-2 16l-1 5-7 1c0-5 2-10 2-14 0-1 0-1 1-2h-1l-1 1v6l-1-3z" class="B"></path><path d="M284 693l1-20c2 1 3 1 5 3 0 1 1 1 2 2h0c4 2 8 4 12 7-2 0-4-1-6-1v1c1 0 2 0 3 1h0l-4 1c-1 0-2 0-3 1h-2c-2-2-1-3-2-5 0-1-1-2-1-4h-1c-1 3 0 8-1 12 0 2 0 5-1 8-1-2 0-5-2-6z" class="D"></path><defs><linearGradient id="g" x1="328.755" y1="520.922" x2="342.431" y2="508.855" xlink:href="#B"><stop offset="0" stop-color="#332b31"></stop><stop offset="1" stop-color="#454b45"></stop></linearGradient></defs><path fill="url(#g)" d="M336 491c1-2 0-4 1-7h1v32c0 4 1 9 0 13h-1 0l-1 1-2-2c1-2 0-5 0-7 1-6 2-13 1-19-1-3-1-7-1-10v-3l1-1c0-1 0-2 1-2v5z"></path><path d="M336 491c1-2 0-4 1-7h1v32c-1 2 0 4-1 6l-1-31z" class="g"></path><path d="M353 371c2-1 4-1 6-1 2 5 5 7 10 8h0 0c2 3 5 3 7 6l-1 2c-1-1-3 0-5 0-3 0-7-2-9-3-4-3-7-8-8-12z" class="B"></path><defs><linearGradient id="h" x1="307.155" y1="517.434" x2="303.455" y2="493.549" xlink:href="#B"><stop offset="0" stop-color="#bbbaba"></stop><stop offset="1" stop-color="#e1dfe0"></stop></linearGradient></defs><path fill="url(#h)" d="M307 491l1 1 4 21c-1 2-2 3-2 4-1 0-1 0-1-1-1 1-2 1-2 2h-1v-1h-1c0 2-1 3-2 3h0v-4c-1 1-1 2-2 2l6-27z"></path><path d="M391 445v-14h-15c-3 0-7 1-9-2-1-1-2-3-1-4h25l1 13c0 2 0 5-1 7z" class="B"></path><path d="M264 593l2-2h0c0-1 0-2 1-2 0-1 1-2 1-3v-1c1-1 3-1 4-2v3l-1 1 2 1-2 2c1 1 3 0 5 0 3 0 7 0 9 1l2 1c3 1 6 1 9 1h18v1c-4 0-8 0-11 1h-6c-1 1-4 1-5 0l-6-1h-9l1-1h2c1 1 1 0 2 0h0-1-1c-2-1-8-1-10 0-1 1-1 2-1 3 1 0 2 1 3 0 2 0 3 1 4 1h-13l1-4z" class="C"></path><path d="M276 597c-1 0-2-1-4-1-1 1-2 0-3 0 0-1 0-2 1-3 2-1 8-1 10 0h1 1 0c-1 0-1 1-2 0h-2l-1 1h9l6 1c-2 0-3 1-4 0h-3l1 1-1 1h0c3 3 6 5 9 8v1c-4 0-15 1-18-2 0-2 1-3 3-6h-1l-2-1z" class="E"></path><defs><linearGradient id="i" x1="360.095" y1="371.716" x2="370.023" y2="352.902" xlink:href="#B"><stop offset="0" stop-color="#cccbcb"></stop><stop offset="1" stop-color="#f5f4f4"></stop></linearGradient></defs><path fill="url(#i)" d="M353 367c1-5 4-10 8-12 3-2 7-3 11-2 2 1 3 2 4 3 1 2 2 4 1 6s-2 3-4 4h-1v-1c0-1 0-4-1-5-1-2-3-2-4-2-3 0-5 1-7 3-1 1-2 3-2 5l1 4c-2 0-4 0-6 1v-1c1-1 0-2 0-3z"></path><path d="M285 543c4-1 8-1 12-1l18 1h1v1c-1 1-1 1-1 2h-1l-1 1h2v1c-4 1-7 1-11 1h-21l1-1c0-1 0-2 1-3v-2z" class="E"></path><path d="M286 699c1-3 1-6 1-8 1-4 0-9 1-12h1c0 2 1 3 1 4 1 2 0 3 2 5h2c1-1 2-1 3-1 2 0 3 1 5 1v1h-2l2 2c0 1-1 1-2 2l-9-1c3 1 5 2 8 3 1 0 2 0 3 1v1h-1v1h1l1 2-2 2c-3 0-5-3-8-3l-1 1c1 0 2 1 2 1l3 1c-1 1-1 1-2 1h-1c-1 0-3 0-3 1-2 0-2 0-4-1l-1-4z" class="B"></path><path d="M294 701c-2 0-3 0-4-1l1-1v-2h2v-1c3 0 5 0 8 1v1h1l1 2-2 2c-3 0-5-3-8-3l-1 1c1 0 2 1 2 1z" class="D"></path><path d="M322 497c2 0 6 0 8 2h-1-3c1 0 1 1 1 1 1 0 2 0 3 1h0l-2 1c1 1 2 1 2 2l-2 1c1 0 1 0 2 1-1 1-1 1-3 1l3 1v1c-1 0-1 0-2 1h2v1l-2 1c1 0 1 0 2 1-1 1-1 0-2 1 0 0 1 0 2 1v1h-1l1 1c0 1-1 2 0 3v2c-1 0-1 0-2 1h1l1 1-1 1 1 2-1 1h1v3l-1 1h-7v-6l1-1h-1v-1h1l1-1h-2v-1l1-1c-1 0-1 0-1-1v-1h1l-1-1v-1h2c-1-1-2-1-2-1v-1l1-1h-1v-1l4-1h-4v-1l2-1c-1 0-1 0-2-1v-4l-1-1 1-2h0v-2-3z" class="R"></path><path d="M321 671l1-3h2c1 1 1 1 2 1h1 3c1 1 1 1 2 1 1-1 3-1 4 0h1 1l-1 1 1 1c1 2 2 5 4 7 2 4 5 6 8 9l-1 1c-4-1-9-2-13-4l-3-1 1-1c-5-2-8-5-11-9l-2-3z" class="V"></path><path d="M321 671l1-3h2c1 1 1 1 2 1h1 3c1 1 1 1 2 1 1-1 3-1 4 0-2 1-8 2-10 2 3 1 7 0 10 0-1 1 0 1-1 1-2 0-4 0-6 1h0l-2-1h-2c-1 0-1 0-2 1l-2-3z" class="R"></path><path d="M323 674c1-1 1-1 2-1h2l2 1h1 1 7v1c-3 0-6 0-8 1h8v1h-6l-1 1c2 0 7 0 9 1-3 0-6 0-8 1v1l1-1c1-1 5 0 7 0v1h-6c1 2 5 1 7 1v1h-1-2 0-4 0c-5-2-8-5-11-9z" class="W"></path><path d="M305 566l10 1v1h-1c-1 0-1 0-2 1h2v1l-2 1c2 0 2 0 3-1v2c-1 1-4 0-6 1h-8c-6 1-13 1-19 1-3-1-7-1-9 0l-1 1 1-2c1-1 1-1 2-1 1-1 1-1 1-2h-1c0-1 0-2 1-3h6l23-1z" class="J"></path><path d="M272 575l1-2c1-1 1-1 2-1 1-1 1-1 1-2h-1c0-1 0-2 1-3h6c-1 2-1 2-2 5v1c6 0 14-1 21 0-6 1-13 1-19 1-3-1-7-1-9 0l-1 1z" class="B"></path><path d="M349 648c-3 3-5 6-6 11 0 4 2 9 4 12 4 5 9 5 15 5l-3 7c-4-1-8-2-11-4-6-4-8-10-8-17-1-10 2-18 9-25l1 4c-1 0-1 0-2 1v5l1 1zM209 783c0 1 0 2 2 3 1 2 3 1 5 1-4 14-7 28-8 41l-3 27c-3-10 0-21-1-31l5-39v-2z" class="D"></path><path d="M336 550h3 3 7l-1 1c-1 1-2 1-3 3 0 1 2 2 1 2l1 1h13c2-1 4-1 7-1v1h1l2 1c-1 0-2 1-3 1l1 1h2v1l-2 1s1 0 2 1h-1l-30-1-1 1c-1 0-3 0-4-1l1-1-1-2 1-1 1 1v1h1v-1-1-1h-1l-3-1v-1h2 0v-1-1l1-1v-2z" class="B"></path><path d="M347 557h13c2-1 4-1 7-1v1h1l2 1c-1 0-2 1-3 1l1 1-21-2h4c-2-1-3-1-5-1h1z" class="i"></path><path d="M336 557l-3-1v-1h2 0v-1-1l1-1 1 2h1v2h2c1 0 1 1 2 1h4c2 0 3 0 5 1h-4l-7 1v1h1v1h-2v1l-1 1c-1 0-3 0-4-1l1-1-1-2 1-1 1 1v1h1v-1-1-1h-1z" class="H"></path><path d="M336 550h3 3 7l-1 1c-1 1-2 1-3 3 0 1 2 2 1 2l1 1h-1-4c-1 0-1-1-2-1h-2v-2h-1l-1-2v-2z" class="B"></path><path d="M336 550h3 3c0 1 1 2 1 2h-1l-1 1v1l-1 1v1h-2v-2h-1l-1-2v-2z" class="m"></path><path d="M272 583l22 1 22 1-7 1 2 1c-2 1-5 1-8 1 4 1 7 1 11 1v1c-5 0-10 1-15 2h-12l-2-1c-2-1-6-1-9-1-2 0-4 1-5 0l2-2-2-1 1-1v-3z" class="E"></path><path d="M360 595c6 2 11 4 16 8 4 5 6 9 7 15h0 0c1-1 2-2 2-3v-1c1-4 1-8 1-12 0-2-1-4 0-5v-1h4v3c-1 2 0 7-1 10v5 1c-1 2-2 4-4 5h-1c-1 0-1 1-2 1l-1 1 1 1v1l-2 2h0l-1-1v1c-2 2-3 3-4 5 0-2 0-4 1-5l1-1v-1c1-1 1-1 1-2 1-5-1-10-4-14-4-6-11-8-18-9v-2c1-2 2-1 4-2z" class="D"></path><path d="M324 433l11-26h1l-1 2c1 1 1 1 2 1l-1 1h1c0 1 1 1 1 2h0v4l-1 2 1 6c-1 4 0 8-1 11v3l-2 1h-4v-2l-2 1-1-1-2-2c-1 2 0 4 0 6-2 0-4 0-5 2l-2 4h-1l6-15z" class="I"></path><path d="M333 421c0-1 1-2 1-2 1-2 1-5 4-6v4c-3 1-3 2-5 4z" class="N"></path><path d="M333 421c2-2 2-3 5-4l-1 2c-2 2-3 2-4 5l-3 3 3-6z" class="K"></path><path d="M330 427l3-3c1-3 2-3 4-5l1 6-1-2-3 3c0 1-1 2-1 3v1h-1-1c1 2 0 4 0 6v2l-2 1-1-1-2-2c1-4 2-6 4-9z" class="H"></path><path d="M331 436l-2-1v-1c0-2 1-3 2-4 1 2 0 4 0 6z" class="m"></path><path d="M333 429c0-1 1-2 1-3l3-3 1 2c-1 4 0 8-1 11v3l-2 1h-4v-2-2c0-2 1-4 0-6h1 1v-1z" class="V"></path><path d="M333 429c0-1 1-2 1-3l3-3 1 2c-1 4 0 8-1 11v-8l-4 2v-1z" class="h"></path><path d="M331 430h1 1v-1 1l1 2h1l1-1h0v1l-3 1v6c1 0 1 1 2 1h-4v-2-2c0-2 1-4 0-6z" class="X"></path><path d="M349 637l1-2c8-8 17-11 27-11v1l-1 1c-1 1-1 3-1 5 0 1 0 1-1 2v-2c-1 1-1 2-1 3v1c-1-2-1-3-1-5-1 3 0 6-3 8-4 3-9 4-13 6-2 2-4 3-7 4l-1-1v-5c1-1 1-1 2-1l-1-4z" class="C"></path><path d="M273 574c2 1 3 1 5 1 5-1 11-1 16-1l35 1c0 2 1 3 0 5h0c-1 0-1 1-2 1v1c1 0 1 0 0 1h-33l-24-1c0-2 1-5 2-7l1-1z" class="n"></path><path d="M395 578c1 3 0 7 0 10 0 10 0 21-5 30-1 2-3 3-5 4-1 0-1 0-1 1h6c-2 0-3 1-5 0l-1 2c-1 2-2 5-3 8l-5 12c-10 26-17 53-31 77-3 6-6 11-10 16-3 4-7 8-10 12-2 2-3 5-5 8v-1c3-6 6-13 9-19l17-29c1-3 3-8 5-10 0 2-2 5-3 7l-10 17c-3 6-5 11-8 17-1 3-3 5-4 7 23-26 34-61 46-93l6-19c1-2 3-4 4-7v-4-1l-1-1 1-1c1 0 1-1 2-1h1c2-1 3-3 4-5v-1-5c1-3 0-8 1-10v-3-11-5-1-1h3 0 2 0z" class="L"></path><path d="M309 471l7-17c0 1 0 2 1 3l4-1h1c2-1 7-1 9-1 1 1 1 1 1 3 0 1 0 1-1 2l1 1c0 1 0 1-1 2l1 1-1 1v1c0 1 0 3 1 4-1 1-1 1-1 2h-2 2l1 1v1 3 1l-1 1h-1v1c1 1 1 2 1 3-1 1-1 3 0 5h0v2l-1 1v1c-2 1-5 0-8 0h0v-2h0c0-1 1-1 2-2h0l-1-1v-1-1h-1v-1h0v-3c1-1 2-1 3-2h-3l-13-1-2-1c1-2 2-4 2-6z" class="F"></path><path d="M322 456h8v3c-2-1-5-1-7-1l-2-2h1z" class="P"></path><path d="M312 470v-1c0-1 0-2 2-3 1-1 3-1 4 0h0v3c-2 1-4 1-6 1z" class="G"></path><path d="M322 465c3 0 5 0 8 1h0c-1 1-1 1-2 1 1 2 2 0 2 2 0 1 0 1-1 2-1 0-6 0-7-1 1-1 0-3 0-5z" class="R"></path><path d="M322 465c0 2 1 4 0 5v1l-1 2h-10l-2 5-2-1c1-2 2-4 2-6 1 0 2 0 3-1 2 0 4 0 6-1v-3h0c2 0 3 0 4-1z" class="I"></path><path d="M317 457l4-1 2 2c2 0 5 0 7 1 0 1-1 0-2 1 1 0 2 1 3 1v1c-5 1-12 0-17 0 1-2 1-4 3-5h0z" class="Y"></path><path d="M321 473c4 0 7-1 10 0v3 2c-2 1-6 1-9 1l-13-1 2-5h10z" class="E"></path><path d="M377 601c-1-10 1-20 0-29 0-6-1-13 0-19l3 3v1c1 3 3 5 6 7-1 1-1 2-2 2l3 3c1 1 3 3 5 4 1 0 1 0 1 1v1l1 2h0v-1c0-2 0-3 1-3v5h0-2 0-3v1 1 5 11h-4v1c-1 1 0 3 0 5 0 4 0 8-1 12v1c0 1-1 2-2 3h0 0c-1-6-3-10-7-15 0 0 1-1 1-2z" class="U"></path><path d="M377 601c-1-10 1-20 0-29 0-6-1-13 0-19l3 3v1c-1 4 1 7 1 11 1 2 0 4 1 7 1 9 0 19 0 28l-1-1v-5-1h0v-15c0-3-1-5-1-7 1-5-1-10-1-15h-1c0 4 1 9 1 12 0 5-1 9-1 14v7l-1 1h1c-1 1 0 3 0 4v1c-1 1 0 1 0 3h-1z" class="G"></path><path d="M380 557c1 3 3 5 6 7-1 1-1 2-2 2h-1v19 8 1h1 2l-1 1h-2l1 1h2v1c-1 1 0 3 0 5 0 4 0 8-1 12v1c0 1-1 2-2 3h0 0c-1-6-3-10-7-15 0 0 1-1 1-2h1c0 1 1 2 2 3l2 2v-1h0v-2c0-9 1-19 0-28-1-3 0-5-1-7 0-4-2-7-1-11z" class="O"></path><path d="M383 593v-8-19h1l3 3c1 1 3 3 5 4 1 0 1 0 1 1v1l1 2h0v-1c0-2 0-3 1-3v5h0-2 0-3v1 1 5 11h-4-2l-1-1h2l1-1h-2-1v-1z" class="R"></path><path d="M387 581v-10h1c0 1 1 2 2 2v1h-1v1h2v2l2-2 1 2h0v-1c0-2 0-3 1-3v5h0-2 0-3-1c-1 1-1 2-2 3z" class="E"></path><path d="M387 581c1-1 1-2 2-3h1v1 1 5 11h-4-2l-1-1h2l1-1h-2-1v-1l1-1 1 1h1c1-3 1-6 0-9 1-1 1-2 1-3z" class="C"></path><path d="M279 550c2 0 2 0 4-1h21v1h-1 3l9 1 1 1h-2v1h1v1h-3c1 1 2 1 3 1v1l-14 1-2 1h8l8 1v1h-1v1h1v1h-4v1c2 0 4 0 6 1l-18 1v1h-1 7l-23 1h-6c-1 1-1 2-1 3h1c0 1 0 1-1 2-1 0-1 0-2 1l-1 2c-1 2-2 5-2 7l24 1v1l-22-1c-1 1-3 1-4 2v1c0 1-1 2-1 3-1 0-1 1-1 2h0l-2 2-1-2c2-6 4-11 6-17l10-24z" class="H"></path><path d="M282 557h-2l3-6h1v1 3l-2 1v1z" class="S"></path><path d="M283 565h-7l3-5c1-1 2-1 4-2-1 2-1 3-2 4v2h1l1 1z" class="B"></path><path d="M284 551l22-1 9 1 1 1h-2v1h1v1h-3c1 1 2 1 3 1v1l-14 1h-19v-1l2-1v-3-1zm23 7l8 1v1h-1v1h1v1h-4v1c2 0 4 0 6 1l-18 1h-16l-1-1h-1v-2c1-1 1-2 2-4h24z" class="J"></path><path d="M294 606v-1c-3-3-6-5-9-8h0l1-1-1-1h3c1 1 2 0 4 0 1 1 4 1 5 0h17v1h-7c-3 1-5 1-7 1l14 1h0c-4 1-9 1-13 1l13 1v1h-10v1l10 1h0c-3 1-6 0-9 1l9 1-3 1v1h3 0c-1 1-2 1-4 1l1 1h3c1 1 1 1 1 2l-1 1v1 1l-4-1h0l1-1-1-2-2 2c0 2-1 6 0 8 0 1 0 3-1 4h-1v1l-1 1c-1 1-1 3-1 4v15h0c-1-2-1-4-2-6-1 1-1 2-1 4 0 3 0 10-2 12h-1c-2 0-3 1-4 1l-1-4v-30c0-6 0-11 1-16z" class="B"></path><path d="M301 609c-2-1-4-1-6-2v-1c5 1 11-1 16 0v1h3 0c-1 1-2 1-4 1l1 1h3c1 1 1 1 1 2l-1 1v1 1l-4-1h0l1-1-1-2-2 2v-3h-1l-1-1h-2c-1 1-1 0-2 1l-1 1v-1z" class="b"></path><path d="M302 609c1-1 1 0 2-1h2l1 1h1v3c0 2-1 6 0 8 0 1 0 3-1 4h-1v1l-1 1c-1 1-1 3-1 4v15h0c-1-2-1-4-2-6-1 1-1 2-1 4 0 3 0 10-2 12h-1c-2 0-3 1-4 1l-1-4h1 0c1-1 1-1 2-1 1 1 0 1 1 1l1-2v-2c2-3 0-20 2-25 1-5 0-9 1-14h0v1l1-1z" class="C"></path><path d="M301 610l1-1v15c0 5 2 10 0 15-1 1-1 2-1 4v-33z" class="l"></path><path d="M302 609c1-1 1 0 2-1h2l1 1h1v3c0 2-1 6 0 8 0 1 0 3-1 4h-1v1l-1 1c-1 1-1 3-1 4v15h0c-1-2-1-4-2-6 2-5 0-10 0-15v-15z" class="P"></path><path d="M302 609c1-1 1 0 2-1h2l-1 12v6c-1 1-1 3-1 4v-5l-1-15-1-1z" class="g"></path><path d="M302 609c1-1 1 0 2-1v17l-1-15-1-1z" class="k"></path><path d="M306 608l1 1h1v3c0 2-1 6 0 8 0 1 0 3-1 4h-1v1l-1 1v-6l1-12z" class="X"></path><path d="M306 608l1 1v3c0 2 0 5-2 8l1-12z" class="H"></path><path d="M340 440c8-3 17-12 26-8 3 2 6 4 7 7 1 4 1 9-1 13-1 2-4 4-6 5-5 1-9 1-13-2-2-1-5-3-7-4-9-4-18-2-27-3l2-4c1-2 3-2 5-2 0-2-1-4 0-6l2 2 1 1 2-1v2h4l2-1 3 1z" class="n"></path><path d="M326 436l2 2 1 1 2-1v2h4l2-1 3 1-14 2c0-2-1-4 0-6z" class="e"></path><defs><linearGradient id="j" x1="202.287" y1="797.538" x2="180.638" y2="821.491" xlink:href="#B"><stop offset="0" stop-color="#575859"></stop><stop offset="1" stop-color="#858384"></stop></linearGradient></defs><path fill="url(#j)" d="M198 777c1-1 2-2 4-3l1 1 2 1 3 2v1l1 4v2l-5 39-1-1c-3 0-5 0-8-1l-1 12c-2-3-1-6-2-9v9l-1-4h-1c-1 1-2 4-2 6-1-7 0-16 1-23h0c1-3 1-5 0-7l-1-2c-1 1-2 1-3 2v-3l1-1c0-2-1-4-1-6 0-1 1-3 2-4 3-5 6-10 10-15h1z"></path><path d="M186 802c1-5 5-12 8-16 0 1 0 2-1 3s-1 2-2 3v1l2 1c-1 1-2 0-3 0v1c0 2-1 5-1 6l-1 3c-1 1-2 1-3 2v-3l1-1z" class="i"></path><path d="M185 803l2-1h2v-1l-1 3c-1 1-2 1-3 2v-3z" class="h"></path><path d="M198 777c-1 1-1 2-1 3-1 1-3 4-3 6-3 4-7 11-8 16 0-2-1-4-1-6 0-1 1-3 2-4 3-5 6-10 10-15h1z" class="O"></path><path d="M196 790l1-3 1 1v2l-3 29v3l-1 12c-2-3-1-6-2-9l4-35z" class="S"></path><path d="M198 777c1-1 2-2 4-3l1 1 2 1 3 2v1l1 4v2c-4 0-7 2-9 4v1l-1 1v-2l-1 1v-2l-1-1-1 3v-6l1-4c0-1 0-2 1-3z" class="K"></path><path d="M196 784l3-3c0 3-1 6 0 8l-1 1v-2l-1-1-1 3v-6z" class="l"></path><path d="M203 775l2 1 3 2v1l-2 2h-1-2c-1-1-2-1-2-2v-2c1 0 1 0 2 1 1-1 0-2 0-3z" class="H"></path><path d="M205 776l3 2v1l-2 2h-1c1-2 1-3 0-5z" class="T"></path><path d="M198 777c1-1 2-2 4-3l1 1c0 1 1 2 0 3-1-1-1-1-2-1v2c-1-1-1 0-1-1-1 1-1 2-1 3l-3 3 1-4c0-1 0-2 1-3z" class="P"></path><defs><linearGradient id="k" x1="211.59" y1="810.817" x2="194.156" y2="796.967" xlink:href="#B"><stop offset="0" stop-color="#040403"></stop><stop offset="1" stop-color="#262527"></stop></linearGradient></defs><path fill="url(#k)" d="M199 789v2l1-1v-1c2-2 5-4 9-4l-5 39-1-1c-3 0-5 0-8-1v-3l3-29 1-1z"></path><path d="M195 819l1 3h7v1c-3 0-5 0-8-1v-3z" class="V"></path><path d="M254 649c12-2 21-3 31 4 3 1 6 4 9 5l7 7c5 5 11 9 17 12 1 1 2 1 3 2l6 3 6 2 3 1c4 2 9 3 13 4v1h-2l1 1c-3 1-7 0-10 1h-1c-2 0-4-1-6-1s-7-1-9-2c-13-4-25-11-37-19-6-5-15-13-23-16-3-1-8 0-11 0h-1l1-4c1 0 2 0 3-1z" class="J"></path><path d="M331 691c2-1 5 0 7-2-2-1-4-2-6-2l-1 1c-5-4-11-5-17-8l-18-8v-1l22 9c2 1 2 1 5 1l-2-2 6 3 6 2 3 1c4 2 9 3 13 4v1h-2l1 1c-3 1-7 0-10 1h-1c-2 0-4-1-6-1z" class="C"></path><path d="M327 682l6 2 3 1c4 2 9 3 13 4v1h-2l1 1c-3 1-7 0-10 1h-1c1-2 1-2 2-3v-1c-2-1-5-2-7-3h-2l-3-3z" class="G"></path><path d="M220 831v-1h0c-1-3-4-9-7-10-1-1-2-1-3-1 0-6 4-6 7-10 4-5 4-14 7-20 2-4 9-5 13-7l8-2-2 7c3 0 5-1 8-1 0 1-1 2-1 3-2 1-2 2-2 3 0 2 0 2 1 3l-6 3-1 1 2 1-2 2c1-1 2-1 3-2 0 1 1 1 0 1 0 2-3 4-4 5-2 1-4 4-5 6v1c-1-1-1-1-1-2-2 3-2 6-2 10h-2c2 5 5 10 9 14 3 0 6 2 10 3 0 1 0 1 1 2l1-1h0v4 2 1 7 3 2c-1 0-3-1-5 0l-15-6-10-3v-1c2-3 0-14-2-17z" class="J"></path><path d="M222 810h1c-1 4 0 7-1 11-1-3 0-8 0-11z" class="Q"></path><path d="M232 852c4-2 15 0 20 1v3 2c-1 0-3-1-5 0l-15-6z" class="C"></path><path d="M252 845c-6 0-13-1-18-3-2-1-4-1-6-2-1-1-1-6-1-7l1 2c4 5 12 6 17 7l1 1c1 0 3 0 4-1-2-2-4-2-6-3s-3-3-4-4c3 0 6 2 10 3 0 1 0 1 1 2l1-1h0v4 2z" class="a"></path><defs><linearGradient id="l" x1="234.105" y1="802.629" x2="234.895" y2="820.371" xlink:href="#B"><stop offset="0" stop-color="#727273"></stop><stop offset="1" stop-color="#959294"></stop></linearGradient></defs><path fill="url(#l)" d="M241 798h2l-1 1 2 1-2 2c1-1 2-1 3-2 0 1 1 1 0 1 0 2-3 4-4 5-2 1-4 4-5 6v1c-1-1-1-1-1-2-2 3-2 6-2 10h-2v-4c0-8 4-14 10-19z"></path><path d="M242 799l2 1-2 2c1-1 2-1 3-2 0 1 1 1 0 1 0 2-3 4-4 5-2 1-4 4-5 6v1c-1-1-1-1-1-2l2-5c1-3 3-5 5-7z" class="X"></path><path d="M243 787c3 0 5-1 8-1 0 1-1 2-1 3-2 1-2 2-2 3 0 2 0 2 1 3l-6 3h-2v-1c1-2 2-5 2-7-4 1-8 2-12 4l-4 4v-1l1-2v-3c1-1 2-1 3-1l12-4z" class="R"></path><path d="M308 612l2-2 1 2-1 1h0l4 1v-1-1l1-1v5c-1 1-1 1-1 2 4-3 8-6 14-6 4 1 8 3 11 6 4 4 5 8 4 13 0 4-3 7-5 10 1 2 1 4 0 6v1c-1 3 0 7 0 9v1h0v2 1 1 4l-1 2h0c-2 0-5 1-7 1h-3-1c-1 0-1 0-2-1h-2l-1 3 2 3c3 4 6 7 11 9l-1 1-6-2-6-3c-1-1-2-1-3-2-6-3-12-7-17-12l-7-7h1l-1-2c1 0 2-1 4-1h1c2-2 2-9 2-12 0-2 0-3 1-4 1 2 1 4 2 6h0v-15c0-1 0-3 1-4l1-1v-1h1c1-1 1-3 1-4-1-2 0-6 0-8z" class="Q"></path><path d="M337 627l1 1h0c0 2 0 3-1 4l-3 3h-1c0-2 0-2-1-3v-4c2 0 3 2 5 2v-3z" class="Y"></path><path d="M332 632c1 1 1 1 1 3h1l3-3c-2 3-3 5-6 6h-3v1h-1v-2h-1c-1 0-2 0-3-1l-1-1 1-2c1 1 3 2 5 2s3-2 4-3z" class="L"></path><path d="M323 633c-1-1-1-2-1-3-1-2 0-2 0-4 1 0 2-1 3-1 3 0 5 1 7 3v4c-1 1-2 3-4 3s-4-1-5-2z" class="M"></path><path d="M315 633c-1-3 0-5 1-7 1-3 4-6 8-7 4-2 7 0 10 1 3 3 4 5 4 8h0l-1-1c-1-3-2-5-4-6s-6-2-8-1h0-1c-3 1-5 3-7 5-1 2-1 4-2 6 2 6 3 8 7 11l1 1c1 1 1 1 2 1l2 1v1h-2v2h2v1c-2 0-3 0-4-1h-1l-1 1-2-2c0-2-1-3-2-5v-3c-1-1-1-2-2-4v-2z" class="g"></path><path d="M322 642l1 1c1 1 1 1 2 1l2 1v1h-2v2l-2-1c0-1-1-3-1-5z" class="P"></path><path d="M317 639l2 3v1c1 2 3 3 4 4l2 1h2v1c-2 0-3 0-4-1h-1l-1 1-2-2c0-2-1-3-2-5v-3z" class="d"></path><path d="M308 612l2-2 1 2-1 1h0l4 1v-1-1l1-1v5c-1 1-1 1-1 2v1c-2 5-3 8-4 13-1 1-1 2-1 3l-1 1s-1 1-1 2c0-5-1-9 0-14 1-1 1-3 1-4-1-2 0-6 0-8z" class="K"></path><path d="M315 633v2c1 2 1 3 2 4v3c1 2 2 3 2 5l2 2 1-1h1c1 1 2 1 4 1v-1h-2v-2h2v-1l-2-1c-1 0-1 0-2-1h1c5 1 9 1 14-2 1 2 1 4 0 6v1c-1 3 0 7 0 9v1h0v2 1 1 4l-1 2h0c-2 0-5 1-7 1h-3-1c-1 0-1 0-2-1h-2l-1 3c-1-2-2-4-4-6-1-3-2-5-3-8 1-2 0-4 0-7 0-5-1-11 0-17h1z" class="F"></path><path d="M318 658c0 2 1 5 3 6h1c1 1 1 1 3 1 3 1 5 1 9 0h2v1h-1c-1 0-1 0-2 1-2 0-6 1-8 0h0c3 1 6 1 9 0h2l1 1c-2 0-5 1-7 1h-3-1c-1 0-1 0-2-1h-2l-1 3c-1-2-2-4-4-6h1v-7z" class="S"></path><path d="M327 645h4v1h-3v1c2 0 7-1 9 0-2 1-3 1-5 1l1 1h4l-1 1h-2v1h1c1 0 1 1 2 1-1 1-2 2-3 2h-2v-1c-2 0-5 0-6-1h4v-1h-2c-2-1-3-1-5-3 1 1 2 1 4 1v-1h-2v-2h2v-1z" class="Z"></path><path d="M321 649l1-1h1c2 2 3 2 5 3h2v1h-4c1 1 4 1 6 1v1h-4c2 1 5 1 7 1h0 0c-2 1-4 1-6 1v1h6v1c-2 0-5 0-6 1h9l-9 2h0l-1 1v-1h-6v-1c1-1 1 0 1-1l-2-2-1-7 1-1h0z" class="W"></path><path d="M315 633v2c1 2 1 3 2 4v3c1 2 2 3 2 5l2 2h0l-1 1 1 7 2 2c0 1 0 0-1 1v1h6v1l1-1h9l-11 2c2 1 8-1 9 1h-2c-3 1-6 2-9 1h0c-2 0-2 0-3-1h-1c-2-1-3-4-3-6v7h-1l-3-8c1-2 0-4 0-7 0-5-1-11 0-17h1z" class="R"></path><path d="M314 633c1 2 1 3 1 4 0 5 2 7 2 11 1 3 0 7 1 10v7h-1c-1-3-2-5-3-8 1-2 0-4 0-7 0-5-1-11 0-17z" class="D"></path><path d="M305 626l1-1v-1h1c-1 5 0 9 0 14 0-1 1-2 1-2l1-1c0-1 0-2 1-3 1 9 1 17 4 25 1 3 2 5 3 8 2 2 3 4 4 6l2 3c3 4 6 7 11 9l-1 1-6-2-6-3c-1-1-2-1-3-2-6-3-12-7-17-12l-7-7h1l-1-2c1 0 2-1 4-1h1c2-2 2-9 2-12 0-2 0-3 1-4 1 2 1 4 2 6h0v-15c0-1 0-3 1-4z" class="j"></path><path d="M318 677v-3h-1c-1-1-1-2-1-3h0 0c2 3 5 4 5 8-1-1-2-1-3-2z" class="O"></path><path d="M305 626l1-1v-1h1c-1 5 0 9 0 14s0 10 2 15c1 1 2 4 2 5l-3-7v8c0 3 1 5 1 7-1-3-2-6-2-9s0-6-1-8v-1 1 6c-1-1-1-3-1-4-2 3-1 4-2 7v5l-2 2-7-7h1l-1-2c1 0 2-1 4-1h1c2-2 2-9 2-12 0-2 0-3 1-4 1 2 1 4 2 6h0v-15c0-1 0-3 1-4z" class="V"></path><path d="M295 658c1 1 2 2 3 2l2-2c1-1 3-10 4-12 0 2 1 3 1 5-2 3-1 4-2 7v5l-2 2-7-7h1z" class="O"></path><path d="M208 774c1 1 3 3 4 3-2-5 1-11 2-17-1 0-3-1-5-2-2-2-3-4-3-6s1-5 3-6c1-2 3-2 5-2s4 3 6 4c2-1 4-2 5-4 1-4 0-7-1-11l-3-24c-1-3 0-6 2-8 3 3 8 9 9 13 0 3-3 5-2 9 1 2 4 4 6 5v-8c-1-6-1-14-1-20 1-4 1-8 3-12h0l-2 7c-1 6-1 14 0 19 0 3 1 8 1 11 0 1-1 1-1 2 1 5-1 9-2 14l1 1 2-1c2 0 5-2 7-4s4-3 7-4c-5-7-8-15-8-24 1-7 3-14 9-19 3-3 8-5 13-4 5 0 9 2 12 6s3 11 2 16c0 1-1 3-2 4h0l1 1c-6 8-13 13-20 19 7 6 17 12 27 11h1c5-1 10-1 15-4l2-1c11-5 18-17 22-28 1-4 2-7 2-10 1-2 1-5 2-6l2 1v3h1c0 4-1 8-2 11-4 11-9 21-18 29-8 8-19 11-31 11-6-1-13-4-19-7-3-2-5-3-7-5l-2-2-21 14c-5 4-7 11-9 17l-7 21c-2 0-4 1-5-1-2-1-2-2-2-3l-1-4v-1c-1-2-1-3 0-4z" class="E"></path><path d="M264 691h0c4 0 7 2 9 5 3 2 3 5 3 8-1 10-10 16-17 21l-4 4-2-3c-4-6-5-13-4-20h0c0-3 1-6 3-8 3-4 7-6 12-7z" class="M"></path><path d="M302 770v1c2-3 4-8 5-12 3-6 7-13 11-19 2-2 4-5 6-8v2h0c-1 1-2 2-2 3h1c-2 4-5 8-7 12-6 11-11 24-12 37v34c1 4 1 9 2 13 1 1 2 3 3 4 0 1 0 2 1 3 1 3 3 5 4 7 4 3 7 5 11 8l1 2c-1 2-2 1-4 2h-62c0-1 0-1-1-2h0c-1-2-2-3-1-4v-1c-1-2-1-4 0-6h-6v-1-2-4h0l-1 1c-1-1-1-1-1-2-4-1-7-3-10-3-4-4-7-9-9-14h2c0-4 0-7 2-10 0 1 0 1 1 2v-1c1-2 3-5 5-6 1-1 4-3 4-5 1 0 0 0 0-1-1 1-2 1-3 2l2-2-2-1 1-1 6-3c-1-1-1-1-1-3 0-1 0-2 2-3 0-1 1-2 1-3l2-1h1l1-9 1 1h5c4 2 25 8 26 10s2 9 2 11c1 3 1 7 3 10s4 4 7 5v-1c1-1 1-37 1-42h2z" class="B"></path><path d="M258 793c4 1 8 2 11 5-1 0-2-1-3-1l-1-1-1 1c-1 0-2 0-3-1h-5 0l-1-2-1-1h0 4z" class="X"></path><path d="M288 807c1 2 0 4 1 5 3 1 4 3 5 5h0c1 0 2 0 2 1h1c-3 0-6 1-9 1v-12z" class="J"></path><path d="M258 853h7c2 0 5-1 7 0-4 1-9 2-13 4h0c-1-2-2-3-1-4z" class="Y"></path><path d="M250 789h7s-1 1-1 2l2 2h-4l-2 1-3 1c-1-1-1-1-1-3 0-1 0-2 2-3z" class="D"></path><path d="M252 794c1-2 1-3 3-4l1 1 2 2h-4l-2 1z" class="S"></path><path d="M270 835c1 0 1-1 2-1 1-1 2-3 3-4l3-6c0-2 1-4 2-5 0 0 0 2 1 2h2c-1 1-2 1-3 1l1 1c0 1-2 4-2 5-1 2-4 6-6 7h-3z" class="Y"></path><path d="M255 776l1 1v8h2c1 0 2 1 3 2v1l-4 1h-7c0-1 1-2 1-3l2-1h1l1-9z" class="U"></path><path d="M264 797l1-1 1 1c1 0 2 1 3 1 6 3 9 7 10 13 1 3 1 5 1 8-1 1-2 3-2 5l-3 6c-1 1-2 3-3 4-1 0-1 1-2 1-2 2-5 4-8 4h-1c-2 2 0 3-2 5-1-2 1-3-1-5l-1-1 1-1 1 1h0c1-1 2-1 3-2h1 3c3-2 7-6 8-8l3-6h0c0-1 1-2 1-3-2-3 0-6-1-9-1-1-1 0-1-1l1-1-1-1c-1-2-2-3-4-5v-1c-3-2-5-3-8-4z" class="Z"></path><path d="M233 821c0-4 0-7 2-10 0 1 0 1 1 2-1 1-1 2-1 4l1 1v1l-1 1c1 0 1 1 2 1v1l-1 1c1 0 1 0 1 1l1 2h0c1 4 4 6 7 8 2 1 4 1 6 3v1l1-1v-2h1v4h1 1 3c2 2 0 3 1 5 2-2 0-3 2-5h1c0 2 0 2 2 4 6 0 14-2 19-6l2-5c1 1 0 4-1 5 0 1-1 1-2 2-8 3-16 5-24 7h-6v-1-2-4h0l-1 1c-1-1-1-1-1-2-4-1-7-3-10-3-4-4-7-9-9-14h2z" class="f"></path><path d="M233 821c0-4 0-7 2-10 0 1 0 1 1 2-1 1-1 2-1 4l1 1v1l-1 1c1 0 1 1 2 1v1l-1 1c1 0 1 0 1 1l1 2h0c1 4 4 6 7 8h-2c-5-2-8-9-10-13z" class="V"></path><path d="M272 853s3-1 4-1l13-3c1-8-1-16 3-24 2-3 3-5 7-6 1 1 1 3 1 4l2 2v2c0 1 0 5 1 6s2 1 2 2v1c1 1 1 1 3 1l-1-2-2-2v-2c-1-1-1-2-1-2 0-2 0-4-1-5 0-2 1-3 1-4 1 4 1 9 2 13 1 1 2 3 3 4 0 1 0 2 1 3 1 3 3 5 4 7 4 3 7 5 11 8l1 2c-1 2-2 1-4 2h-62c0-1 0-1-1-2 4-2 9-3 13-4z" class="M"></path><path d="M304 820c1 4 1 9 2 13 1 1 2 3 3 4 0 1 0 2 1 3 1 3 3 5 4 7 4 3 7 5 11 8l1 2c-1 2-2 1-4 2-3-4-8-6-11-10-1-2-3-4-4-7-1-2-1-4-4-6h-1c-1-4-1-9-2-13l2 2v2c0 1 0 5 1 6s2 1 2 2v1c1 1 1 1 3 1l-1-2-2-2v-2c-1-1-1-2-1-2 0-2 0-4-1-5 0-2 1-3 1-4z" class="D"></path><path d="M254 793h0l1 1 1 2h0 5c1 1 2 1 3 1 3 1 5 2 8 4v1c2 2 3 3 4 5l1 1-1 1c0 1 0 0 1 1 1 3-1 6 1 9 0 1-1 2-1 3h0l-3 6c-1 2-5 6-8 8h-3-1c-1 1-2 1-3 2h0l-1-1-1 1 1 1h-3-1-1v-4h-1v2l-1 1v-1c-2-2-4-2-6-3-3-2-6-4-7-8h0l-1-2c0-1 0-1-1-1l1-1v-1c-1 0-1-1-2-1l1-1v-1l-1-1c0-2 0-3 1-4v-1c1-2 3-5 5-6 1-1 4-3 4-5 1 0 0 0 0-1-1 1-2 1-3 2l2-2-2-1 1-1 6-3 3-1 2-1z" class="Z"></path><path d="M252 798c7-1 12-1 18 4 2 1 3 2 4 5v1l2 3c-1 1-1 1-1 2-1-2-2-4-2-6-1-2-2-3-4-4v-1c-3-2-6-3-9-3h-2c-1 1-1 2-1 3v3 1h-1l-1-1c1-2 1-4 1-5v-1h-4v1l-1-1 1-1z" class="P"></path><path d="M265 810h1v-5h-1v2l-1-1v-3h3c3 2 4 5 5 9l1 3c-1 0-2 0-3 1v2h-1c0-2-1-3-2-5v-1h-2v-2z" class="X"></path><path d="M272 812l1 3c-1 0-2 0-3 1l-1-2c1-1 1-2 3-2z" class="T"></path><path d="M245 810l2-2h0v1h1c1-1 2-2 3-4v-1l1 1c-1 1-1 1 0 2h2l-2 1v1c-1 1-2 3-3 4-2 2-2 3-2 5h-1l-1 1c0 1 0 1-1 2v-2l-2 2v-6c1-2 2-3 3-5h0z" class="h"></path><path d="M242 815c1-2 2-3 3-5v3l2-2v1c0 2-3 4-3 7l-2 2v-6z" class="N"></path><path d="M254 793h0l1 1 1 2h0 5c1 1 2 1 3 1 3 1 5 2 8 4v1c2 2 3 3 4 5h-2c-1-3-2-4-4-5-6-5-11-5-18-4-2 0-4 1-5 1s-2 0-2 1c-1 1-2 1-3 2l2-2-2-1 1-1 6-3 3-1 2-1z" class="c"></path><path d="M254 793h0v3c-2 0-1 1-2 1h-2c-1 0-5 2-6 2v1l-2-1 1-1 6-3 3-1 2-1z" class="V"></path><path d="M254 807c1 0 1 0 2 1 2 0 6 1 8 0v2l-2 1c0 1 0 1 1 2v9c-1 1-1 1-1 3-1 1-3 2-4 2l-1-1v-1h-3l-1-1c-2-1-3-2-5-3-1-1-1-2-2-3h1c0-2 0-3 2-5 1-1 2-3 3-4v-1l2-1z" class="c"></path><path d="M253 812l2-1 3 3v1l-5-3zm-7 6h1c0-2 0-3 2-5 0 4 0 6 3 9 1 1 3 1 5 1 0 0 1-1 2-1h0c0 1-1 2-2 2s-3 0-3 1l-1-1c-2-1-3-2-5-3-1-1-1-2-2-3z" class="T"></path><path d="M253 812l5 3h0c1 2 0 3 0 4-2 1-3 1-4 1-2-1-2-2-3-3 0-2 1-3 2-5z" class="g"></path><path d="M254 814c1 0 2 0 3 1 0 2 0 2-1 4h-1c-1-1-2-1-2-2s1-1 1-2v-1z" class="T"></path><path d="M254 807c1 0 1 0 2 1 2 0 6 1 8 0v2l-2 1c0 1 0 1 1 2v9c-1 1-1 1-1 3-1 1-3 2-4 2l-1-1v-1c1-1 3-2 4-3 1-2 2-7 1-9-3-3-6-3-10-4v-1l2-1z" class="X"></path><path d="M276 807l1 1-1 1c0 1 0 0 1 1 1 3-1 6 1 9 0 1-1 2-1 3h0l-3 6c-1 2-5 6-8 8h-3-1c-1 1-2 1-3 2h0l-1-1-1-1v-1h1c0-1-1-1-1-2 1-1 2-1 2-1l3-1 1-1 1-1v-1h-1c-1 1-2 1-3 1 1-2 3-3 3-4h-1c0-2 0-2 1-3v-9c-1-1-1-1-1-2l2-1h1v2h2v1c1 2 2 3 2 5h1v-2c1-1 2-1 3-1v1 2h1l1-1v-4c0-1 0-1 1-2l-2-3v-1h2z" class="V"></path><path d="M270 824c2 0 2 0 3 1l-1 1c-1 2-1 3-3 4h-1c1-2 2-3 2-5v-1z" class="T"></path><path d="M259 832l1 1h4 0c1-1 2-2 3-2h1 0l-5 5h-1c-1 1-2 1-3 2h0l-1-1-1-1v-1h1c0-1-1-1-1-2 1-1 2-1 2-1z" class="X"></path><path d="M264 810h1v2h2v1c1 2 2 3 2 5h1v-2c1-1 2-1 3-1v1 2h1l1-1v3h-2c1 2 1 3 0 5-1-1-1-1-3-1v-1-4h-1c0 3 0 6-1 9-2 1-3 3-6 3l1-1 1-1v-1h-1c-1 1-2 1-3 1 1-2 3-3 3-4h-1c0-2 0-2 1-3v-9c-1-1-1-1-1-2l2-1z" class="c"></path><path d="M245 800c0-1 1-1 2-1v1c-1 2-5 5-5 7 1 1 2 2 2 3h1 0c-1 2-2 3-3 5v6l2-2v2c1-1 1-1 1-2l1-1c1 1 1 2 2 3 2 1 3 2 5 3l1 1h3v1l1 1c1 0 3-1 4-2h1c0 1-2 2-3 4 1 0 2 0 3-1h1v1l-1 1-1 1-3 1s-1 0-2 1c0 1 1 1 1 2h-1v1l1 1-1 1 1 1h-3-1-1v-4h-1v2l-1 1v-1c-2-2-4-2-6-3-3-2-6-4-7-8h0l-1-2c0-1 0-1-1-1l1-1v-1c-1 0-1-1-2-1l1-1v-1l-1-1c0-2 0-3 1-4v-1c1-2 3-5 5-6 1-1 4-3 4-5 1 0 0 0 0-1z" class="i"></path><path d="M247 830c-1 0-2 0-3 1h0c-2-2-3-5-5-7v-1c2-1 0-4 1-6 0-1 1-1 2-2v6 3c1 2 4 4 5 6zm-11-17v-1c1-2 3-5 5-6v1l-1 1 1 1-1 3c0 1 0 2 1 3-1 0 0 0-1 1-1 2-1 4-1 6h-1-1v-1c-1 0-1-1-2-1l1-1v-1l-1-1c0-2 0-3 1-4z" class="H"></path><path d="M236 819c1-2 1-3 1-4h0l1-1v8h-1v-1c-1 0-1-1-2-1l1-1z" class="T"></path><path d="M236 813v-1c1-2 3-5 5-6v1l-1 1 1 1-2 2c0 1-1 2-1 3l-1 1h0c0 1 0 2-1 4v-1l-1-1c0-2 0-3 1-4z" class="m"></path><path d="M245 819l1-1c1 1 1 2 2 3 2 1 3 2 5 3l1 1h3v1l1 1c1 0 3-1 4-2h1c0 1-2 2-3 4 1 0 2 0 3-1h1v1l-1 1-1 1-3 1s-1 0-2 1h-4v-2l-1-1 1-1-1-1c-1 1 0 2-1 3-2 0-2 0-4-1-1-2-4-4-5-6v-3l2-2v2c1-1 1-1 1-2z" class="U"></path><path d="M253 824l1 1h3v1c-1 0-3 0-4 1h-3-1l1-2c1 0 2 0 3-1z" class="h"></path><path d="M245 819l3 4c1 2 1 2 2 2l-1 2c0-1-1-1-2-1-1-1-3-3-3-5 1-1 1-1 1-2z" class="V"></path><path d="M245 819l1-1c1 1 1 2 2 3 2 1 3 2 5 3-1 1-2 1-3 1s-1 0-2-2l-3-4z" class="k"></path><path d="M381 327c-2 0-3 0-4-2-1-1 0-2 0-4 2-8 3-16 13-14 1 0 2 1 3 0 1-6-4-7-7-11-1-2-2-4-2-6-1-8 1-15 5-21 5-5 10-8 17-9h1c6-1 10 1 15 3l-3-3c-1-5-3-10-8-13-2-2-7-3-10-2l-2 1c0-2 1-2 2-4 2-1 4-1 6 0 9 1 14 7 19 13 1 3 2 5 2 7 0 1 0 3 1 4v6l1 5c1 1 1 2 1 3 1 7 1 14 1 21-1 4-2 9-1 13-1 2-2 5-3 7s-3 5-3 7 1 5 2 8l-1 9v1c-2 6-3 11-6 16-1 2-3 4-4 5l-2 2c1 2 0 4 0 5l-5 9-1 2c-1 4-2 7-4 11v1c-1 1-2 3-2 5 0 1 0 1-1 2l-1 1 1 1h1v1h2v1c-2 1-5 0-7 1v59h6c1 7 0 14 0 21-2 0-5 0-6 1-3 3-5 8-7 11l-10 17 1 1h0 0-1c-2 1-3 2-4 4l20 25c-2 1-4 2-5 3-1-1-2-1-3-1h0c-1-1-2-2-2-3-1-1-1-2-2-3l-11-17-7 13c-2 3-4 6-7 9h-8l-2-2h-4 0v-2c1-2 3-2 4-3h9l12-20c-4-4-7-10-11-15-1 3-3 7-2 10 0 2 1 2 2 3-3 1-13 0-17 0 3-1 6-1 9-1-1-9-2-16-6-24-2-2-3-4-5-7h0v-5c7-1 14 0 20 0h39v-9l-47 1c-1-2-1-2 0-4-1-1-2-1-3-1v-1l-1-1v-1c-1-2-1-5 1-7h1c3 2 3 5 5 8h3v-4h1v3h1v-3l1 4h24c1 0 5 1 6 0v-23c1-2 1-5 1-7l-1-13v-14-4c1-2 0-5 0-7l1-15c-15 9-27 25-39 38-5 6-9 12-15 17 1-2 3-4 5-7l8-10c7-10 14-20 22-28a57.31 57.31 0 0 1 11-11c3-3 6-5 9-7 7-4 13-7 18-13 7-9 11-19 9-30-2-13-11-21-21-29v38c8-2 13-6 19-11l1 3c0 2 1 4 0 5-1 0-3 0-4 1 0 0-1 2-1 3l-4 8c-3 5-7 10-11 15-6 8-12 14-20 19 4-4 6-7 9-11l12-15 10-17c-4 2-7 3-11 4-1 4-3 8-5 11-4 7-12 15-20 16-2 1-5 1-7-1-1-1-2-1-2-2h3c5-1 8-1 11-4l1-1c2-4 3-7 3-12v-6-1h0c-2 0-7 0-9-2 1-1 3 0 5-1h13c1-5 2-24 0-27h-4c1 2 0 5 0 7v12c-1 1-1 1-2 1-2-1-4-7-5-10z" class="E"></path><path d="M406 265c4 0 7 0 10 2l-1 2-1-1v-1c-2-1-6-1-8-2z" class="I"></path><path d="M383 347h2v6c-1 0-1 0-2 1v-6-1h0z" class="P"></path><path d="M340 488c2 0 3 0 5 1v6c-2-2-3-4-5-7z" class="C"></path><path d="M383 354c1-1 1-1 2-1 1 4-1 9-3 12l-2 1c2-4 3-7 3-12z" class="W"></path><path d="M399 268h0c2-2 4-2 7-3 2 1 6 1 8 2v1l-6-1c-3 1-5 1-8 2l-1-1z" class="j"></path><path d="M381 327h5c1 1 1 4 1 5s-1 1-1 2 1 1 2 2c-1 1-1 1-2 1-2-1-4-7-5-10z" class="P"></path><path d="M416 267c4 2 6 5 7 9 0 2 0 5-1 7-1 1-1 1-2 1-2 0-5 0-6-1v-1h0c1-1 1-1 1-3-1 0-1-1-1-2h-1v-2c-1 0-2-1-3-1h-1c0-1 0-1-1-2v-1-4l6 1 1 1 1-2z" class="D"></path><path d="M416 267c4 2 6 5 7 9 0 2 0 5-1 7-1 1-1 1-2 1-2 0-5 0-6-1v-1c2-1 3-2 4-4l-1-1 1-2v-1l1 3c0 2-1 4-2 5v1h0l1-1 1 1h1c1-2 2-3 1-5 0-4-3-7-6-9l1-2z" class="F"></path><path d="M414 369c1 2 0 4 0 5l-5 9-1 2c-1 4-2 7-4 11v1c-1 1-2 3-2 5 0 1 0 1-1 2l-1 1 1 1h1v1l-5 1v-1-20c0-1-1-4 0-5 0-1 4-3 5-3 5-3 9-6 12-10z" class="I"></path><path d="M404 396l-2-1 1-1v-1h-2v-1h2l1-1c-2 0-3 0-4-1h1c1 0 3 0 4-1h0-1 0c-2 0-3-1-4-1v-1h1c1 1 2 1 3 1s1-1 2-2c-1 0-1 0-2-1 1 0 2 0 3-1h-3c2-1 3-1 5-1l-1 2c-1 4-2 7-4 11z" class="F"></path><path d="M428 284h0v15c0 8-2 16-5 23-3-4-6-8-10-12l-12-9c4 1 7 0 11-2 3-1 6-3 7-7 1-1 1-1 0-3 4 0 7-2 9-5h0zm-28-15c3-1 5-1 8-2v4 1c1 1 1 1 1 2h1c1 0 2 1 3 1v2h1c0 1 0 2 1 2 0 2 0 2-1 3h0v1h-1c0-1-1-2-1-3h-2c-1 1-2 1-2 2v4c2 2 6 3 9 4-2 2-5 4-7 5-3 2-7 2-10 1-1-4-3-6-6-9h0c-1 2 0 6-1 7-1 0-2-1-2-2-3-3-2-9-1-12 1-6 4-10 9-12l1 1z" class="M"></path><path d="M400 269c3-1 5-1 8-2v4 1c1 1 1 1 1 2h1c1 0 2 1 3 1v2c-2-2-4-2-7-1-2 0-3 2-4 3-2 4-2 8-1 11v3h0l-5-9c-1-3-1-4-1-6 0-4 3-6 5-9z" class="B"></path><path d="M348 489h42l-10 17c-2 4-5 8-8 11-4-2-7-8-11-12l-13-16z" class="M"></path><path d="M382 493h3c-2 1-6 0-8 1h-20l-2-1h0 27z" class="N"></path><path d="M449 324l3-6c3-6 7-15 4-22-1-4-1-8 0-11 2-2 4-4 6-5 3-1 6 0 9 1 3 2 5 6 6 10v1l2 1-1 1-4 12v2l-1 4-3 8-17 47h0-2c0 3-1 5-2 7-1 3-2 8-4 11v-1c0 1-1 1-1 2-1 3-2 7-3 10 0 1-1 3-1 4v1c0 1-1 3-1 5 0-1 0-1 1-2v1l-6 16-17 47-14 39-9 23v1h-1c-2 0-3 0-4 2 0 2 2 2 3 3 0 2 0 3 2 4l1 1 1 1c0 2 1 2 2 3 1 2 3 5 4 7l1 1c1 1 0 1 1 2s1 1 2 1c1 2 2 2 2 4v1h1c-1-2 0-2-1-4 1-1 1-1 1-2l-2-2h0c2 0 3 1 3 2v1h2l1 9-1 2 1 18c0 3-1 6 0 9h1l21 1c3 0 8-1 11 1l2-1h1 2l1 2v15 1l-1 7c-1 1-1 1-2 1h2l1 2h-4-11c-2-1-5-1-7 0h-40-6c0-1 0-1 1-1 2-1 4-2 5-4 5-9 5-20 5-30 0-3 1-7 0-10v-5c-1 0-1 1-1 3v1h0l-1-2v-1c0-1 0-1-1-1-2-1-4-3-5-4l-3-3c1 0 1-1 2-2-3-2-5-4-6-7v-1c0-1 0-1 1-1v-1-1c1-2 3-4 5-6 0 1 1 2 2 3h0c1 0 2 0 3 1 1-1 3-2 5-3l-20-25c1-2 2-3 4-4h1 0 0l-1-1 10-17c2-3 4-8 7-11 1-1 4-1 6-1 0-7 1-14 0-21h-6v-59c2-1 5 0 7-1v-1h-2v-1h-1l-1-1 1-1c1-1 1-1 1-2 0-2 1-4 2-5v-1c2-4 3-7 4-11l1-2 5-9c0-1 1-3 0-5l2-2c1-1 3-3 4-5 3-5 4-10 6-16v-1l1-1 2-2h1c1-1 2-1 2-1h1l1 1-2 1 1 1c1-2 2-2 3-4l2 2c-2 4-5 8-8 13h0l10-14 1-1 3-7c1-1 2-2 2-3h1c1-2 2-4 2-6z" class="M"></path><path d="M403 586h3l-1 8h-2l-1-1 1-7z" class="g"></path><path d="M461 314c0-2 1-3 2-5 2-2 3-2 5-3l-9 14h0c0-1 0-2 1-2 0-2 0-2 1-4z" class="C"></path><path d="M472 308h2l-1 4-3 8-1 1h-1l4-13z" class="a"></path><path d="M425 370c2 2 1 9 1 12h0-3v-10s1-2 2-2z" class="T"></path><path d="M446 611c2 1 3 2 4 2h1c0-1 0-1 1-1v1l-1 7c-1 1-1 1-2 1s-1 0-2-1l-1-9z" class="D"></path><path d="M440 400v1c0 1-1 3-1 5 0-1 0-1 1-2v1l-6 16-1-2c2-6 4-13 7-19z" class="P"></path><path d="M415 447c1-1 5-1 6 0v1c-1 1 0 2-1 2-2 1-3 1-5 1-1-1-3-1-4-1l-1-1c1-1 0-2 1-2h4z" class="f"></path><path d="M415 447c1-1 5-1 6 0v1l-9 1-1-1v-1h4z" class="d"></path><path d="M410 439h1 12c0 2 0 5-2 6h-1c-3 0-7 1-9-1v-1-4h-1z" class="D"></path><path d="M410 432h15l-1 5-13 1c0-2-1-4-1-6z" class="C"></path><path d="M394 501h0c1 2 1 6 2 8 0 4 1 8-2 11v2h-1v2 1l1 1c-1 1-2 2-2 4h-1v-2c1-2 1-6 1-8h-1c-1 0-3 0-4-1h0c2 0 3 0 5-1v-6c1-4 1-8 2-11z" class="U"></path><path d="M404 397h2l2-2 1 1c3 1 9 0 11 2v1c-5 3-13 0-16 5h-2l2 1c0 1-1 1-2 1h-1l-1-1 1-1c1-1 1-1 1-2 0-2 1-4 2-5z" class="L"></path><path d="M449 595h2l1 2v15c-1 0-1 0-1 1h-1c-1 0-2-1-4-2v-10c0-3 1-4 3-6z" class="Q"></path><path d="M447 343v13c0 3 0 6-1 9h-4l-1-1c0-4 0-10 1-14 1-2 3-5 5-7z" class="L"></path><path d="M416 467l17-48 1 2-17 47c0-1 0-1-1-1z" class="d"></path><path d="M391 551v1c2 0 4-4 7-1v1c1 1 2 2 2 3l-1 2h0 3c1 1 1 2 2 3-1 0 0 0-1 1h2v1l-2 1h3l-1 1h-1c-2 0-4 0-6 1l-1-1c-2-1-4-3-5-4v-1l2 2c1 0 2 0 3-1l-3-2c0-1-1-2-2-2-1-3-3-4-4-6 1 0 2 0 3 1z" class="C"></path><defs><linearGradient id="m" x1="468.144" y1="348.102" x2="455.356" y2="330.898" xlink:href="#B"><stop offset="0" stop-color="#8d8d8f"></stop><stop offset="1" stop-color="#a9a7a9"></stop></linearGradient></defs><path fill="url(#m)" d="M468 321h1l1-1-17 47h0-2 1l16-46z"></path><path d="M408 487l8-20c1 0 1 0 1 1l-14 39-9 23v1h-1c0-3 3-6 4-9 0-1 0-1 1-2v-1-1c2-10 7-19 9-28 1-1 1-2 1-3z" class="L"></path><defs><linearGradient id="n" x1="433.497" y1="397.918" x2="414.274" y2="409.913" xlink:href="#B"><stop offset="0" stop-color="#444343"></stop><stop offset="1" stop-color="#646567"></stop></linearGradient></defs><path fill="url(#n)" d="M423 382h3 0v20c0 9 1 20-2 28-2-5-1-11-1-16v-32z"></path><path d="M437 374c-1 0-2 0-3-1-1-2-2-4-2-7 5 0 11 0 17 1h3-1c0 3-1 5-2 7-1 0-2 0-3-1-3 1-7 0-9 1z" class="B"></path><path d="M449 367h3-1c0 3-1 5-2 7-1 0-2 0-3-1h1c0-1 0-2 1-3v-1l-1-1c1 0 2 0 2-1h0z" class="H"></path><path d="M437 374c2-1 6 0 9-1 1 1 2 1 3 1-1 3-2 8-4 11v-1c0 1-1 1-1 2l-10 1c0-3 0-7 1-8h2v-1c-1-1-1 0-1-1-1 0-1-1-1-1l2-2z" class="J"></path><path d="M437 374c2-1 6 0 9-1 1 1 2 1 3 1-1 3-2 8-4 11v-1l1-5h-9v-1c-1-1-1 0-1-1-1 0-1-1-1-1l2-2z" class="O"></path><path d="M386 547c0 1 1 2 2 3h0c1 2 3 3 4 6 1 0 2 1 2 2l3 2c-1 1-2 1-3 1l-2-2v1c1 1 3 3 5 4l1 1-4 2h-5l-2 2-3-3c1 0 1-1 2-2-3-2-5-4-6-7v-1c0-1 0-1 1-1v-1-1c1-2 3-4 5-6z" class="S"></path><path d="M386 564l3 3h0l-2 2-3-3c1 0 1-1 2-2z" class="j"></path><path d="M407 490c-1-5 0-11 0-16s-1-10 0-15c1 0 2-1 3-1l-1-1-1 1h-1c-1-6 0-13 0-20v-32c1-2 1-2 2-2l1 1v26 1c0 2 1 4 1 6h-2 1v1h1v4 1c2 2 6 1 9 1l-8 1 3 1h-4c-1 0 0 1-1 2l1 1c1 0 3 0 4 1h-4c0 3 0 6-1 9h-2v1h2v1c-1 0-1 0-2 1h2v1l-1 16c-1 1-2 5-1 6v1c0 1 0 2-1 3z" class="k"></path><defs><linearGradient id="o" x1="417.677" y1="365.968" x2="428.929" y2="371.123" xlink:href="#B"><stop offset="0" stop-color="#807e7e"></stop><stop offset="1" stop-color="#b3b2b3"></stop></linearGradient></defs><path fill="url(#o)" d="M441 340l1 1c5-6 10-13 14-19 1-2 3-5 4-7l1-1c-1 2-1 2-1 4-1 0-1 1-1 2h0l-34 50c-1 0-2 2-2 2-6 8-10 15-14 24h0l-1-1-2 2h-2v-1c2-4 3-7 4-11l1-2 5-9c0-1 1-3 0-5l2-2c1-1 3-3 4-5 3-5 4-10 6-16v-1l1-1 2-2h1c1-1 2-1 2-1h1l1 1-2 1 1 1c1-2 2-2 3-4l2 2c-2 4-5 8-8 13h0l10-14 1-1z"></path><path d="M416 372l-1 3c0 3-4 8-7 10l1-2 5-9 2-2z" class="P"></path><path d="M426 345l1-1 2-2h1c1-1 2-1 2-1h1l1 1-2 1 1 1-1 1 1 1 1-2 1 1c-1 0-1 1-2 2l-17 25-2 2c0-1 1-3 0-5l2-2c1-1 3-3 4-5 3-5 4-10 6-16v-1z" class="Z"></path><path d="M449 324l3-6c3-6 7-15 4-22-1-4-1-8 0-11 2-2 4-4 6-5 3-1 6 0 9 1 3 2 5 6 6 10v1l2 1-1 1-4 12v2h-2c2-4 3-7 3-11-3 3-5 5-7 9-2 1-3 1-5 3-1 2-2 3-2 5l-1 1c-1 2-3 5-4 7-4 6-9 13-14 19l-1-1 3-7c1-1 2-2 2-3h1c1-2 2-4 2-6z" class="n"></path><path d="M406 556c1 2 2 2 2 4v1h1c-1-2 0-2-1-4 1-1 1-1 1-2l-2-2h0c2 0 3 1 3 2v1h2l1 9-1 2 1 18c0 3-1 6 0 9v4c3 10 10 18 19 24-12-1-24 0-36 0 0-1 1-2 2-3l5-10 3-9c3-11 1-22 2-33v-1h-2-1c1-1 1-1 2-1h0c0-3 0-6-1-9z" class="Q"></path><path d="M429 150c1-4 2-9 4-13 5-11 14-20 24-26 2-2 5-3 8-4 0 1 0 1-1 2s-1 2 0 4c0 1 1 2 2 3s2 1 3 1v1c-2 1-3 3-5 5l-1 2h-3c-3 0-9-1-12 1h14c-1 1-1 2-2 4-1 1-1 3-1 4-2 7-1 16 1 22 1 1 1 1 1 2l2 4c8 10 16 19 26 27v2l16 16h-3-1l3 3 3 3 2 1v1l3 2h0c1 1 1 1 2 1l10 10h-2l-1-1h0l-1 1-2-3c0 2 0 3 1 5l1 2v1l-5-6h-1l-1-1v2 1c0 1 0 2 1 3h-1c-1-1-2-3-3-3-1-1-2-2-2-3l-6-10h-1l-1 1-1-2c-3 3-6 29-9 28 0 3-1 4 0 5l-2 10c-3 8-6 17-8 25-1 2-2 6-3 8-1-4-3-8-6-10-3-1-6-2-9-1-2 1-4 3-6 5-1 3-1 7 0 11 3 7-1 16-4 22l-3 6c0 2-1 4-2 6h-1c0 1-1 2-2 3l-3 7-1 1-10 14h0c3-5 6-9 8-13l-2-2c-1 2-2 2-3 4l-1-1 2-1-1-1h-1s-1 0-2 1h-1l-2 2-1 1 1-9c-1-3-2-6-2-8s2-5 3-7 2-5 3-7c-1-4 0-9 1-13 0-7 0-14-1-21 0-1 0-2-1-3l-1-5v-6c-1-1-1-3-1-4 0-2-1-4-2-7h0c-1-2-3-4-4-6-2-3-5-4-7-6l-1-1v-7c0-10 1-21 0-31h1v-2c0-2 1-4 2-6l3-9s1-2 1-3l3-10c1-3 2-6 3-10 0-4 0-9 2-14z" class="M"></path><path d="M473 219c3 9 5 23 2 33l-2 1c1-4 1-9 1-13 0-7 0-14-1-21h0z" class="k"></path><path d="M440 234c1 0 3 1 4 0 3-1-1-13 2-15 1 1-1 11 1 14h3l-1 7-2 1-3 1-2 1-3 1c-1-2-1-4-1-6l1 2h1v-6z" class="I"></path><path d="M432 236l1-14c0-2-1-7 0-8 1 2 1 4 1 5v15 23 7 1c0 4 0 13 3 16h0v6l-2 1h-1l-1-15c-1-5 1-11 0-16-1-3-1-6-1-9v-12z" class="V"></path><path d="M432 227c1 1 0 7 0 9v12c0 3 0 6 1 9 1 5-1 11 0 16l1 15c-1 5 0 11 0 16v6c0 3 0 7-1 9-1-2-2-4-2-5-1-4 0-9 1-13 0-7 0-14-1-21 2-9 1-19 1-28v-25z" class="F"></path><path d="M437 241c0-3 1-5 1-8v-1c0-1 0-2-1-3h-1v-1h2 1v6h1v6h-1l-1-2c0 2 0 4 1 6l-1 4v12l1 13-1 17c0 2 1 5 0 8-1-6 0-13 0-19h-1v2h0c-3-3-3-12-3-16v-1-7c1-2 1-4 1-5 0-5-1-12 0-17v-4l1-1 1 1-1 1 1 2c-1 0 0 1-1 2 0 2 1 3 1 5z" class="X"></path><path d="M437 241c0-3 1-5 1-8v-1c0-1 0-2-1-3h-1v-1h2 1v6h1v6h-1l-1-2c0 2 0 4 1 6l-1 4v12l1 13-1 17c0 2 1 5 0 8-1-6 0-13 0-19h-1v2h0v-40z" class="L"></path><path d="M459 240l1 2c2-2 3-5 4-8h0c1 2 1 5 0 8 0 3 0 5-1 8 0 3-1 5-2 7 0 1-1 3 0 5l-2 1-7 6 3-3v-8l-1-1c-1-1 1-1 0-2s-1-1-2-1v-1h-1l-1-1c0-2-1-2 0-4 3-1 5-1 8-1h1c1-2 0-5 0-7z" class="X"></path><path d="M458 247v1c1 0 1 0 2-1 0 1-1 1-2 2h-1-1-1c-1 1-1 1-2 1l1 1h1v1c-2 0-3 0-4 1l-1-1c0-2-1-2 0-4 3-1 5-1 8-1z" class="Z"></path><path d="M457 253l1-1c1-1 1-2 3-3l1-1v1 2h-1c-1 2 0 4 0 6 0 1-1 3 0 5l-2 1c-1-3 1-7 0-10h-2z" class="h"></path><path d="M457 253h2c1 3-1 7 0 10l-7 6 3-3v-8l-1-1c-1-1 1-1 0-2s-1-1-2-1v-1h0l1 1 4-1z" class="k"></path><path d="M460 156c1 1 1 1 1 2-1 1-1 1-3 2-6 2-13 11-16 17v1l-1 1c-2 7-1 14 3 21 4 6 9 10 15 13l13 3v1c-1 1-3 1-4 1-4-1-9-2-12-4-4-1-9-6-12-10-4-6-7-15-6-22v-1c0-3 1-6 3-8 4-8 11-14 19-17z" class="B"></path><path d="M456 214h1l-1-1s-1 0-2-1l-1-1-3-3c3 2 6 5 9 5l13 3v1c-1 1-3 1-4 1-4-1-9-2-12-4z" class="C"></path><path d="M423 191c0-1-1-2-1-3 1 0 1 0 2 1s1 1 0 2h2c1 2-1 7 1 7h0l-1-1h1 1v-1l1 2 2-2c1 2 1 2 0 4v1h1c-2 3-1 5-2 8 1 4 3 12 1 15l1 3v25c0 9 1 19-1 28 0-1 0-2-1-3l-1-5v-6c-1-1-1-3-1-4 0-2-1-4-2-7h0l1-1c-1-3 0-6 0-9l-1-31v-5c-1-3-1-6-1-9h3l1-1h-3l-1-6-1 2c-1-2-1-3-1-4z" class="I"></path><path d="M430 241c-1-7 0-15 0-22 0-3-1-8 0-10 1 4 3 12 1 15-2 5 1 12-1 17z" class="R"></path><path d="M430 241c2-5-1-12 1-17l1 3v25h-1v-3c0 1 0 2-1 3v1-12z" class="B"></path><path d="M429 266h0l-1-6c-1-6 0-12 0-18v-28c1 7 2 15 1 22v18h1v-1-1c1-1 1-2 1-3v3h1c0 9 1 19-1 28 0-1 0-2-1-3l-1-5v-6z" class="C"></path><path d="M441 179c2 3 19 22 20 23h1c2 4 9 10 10 14l-13-3c-6-3-11-7-15-13-4-7-5-14-3-21z" class="M"></path><path d="M437 281v-2h1c0 6-1 13 0 19v29 10 5l-2-2c-1 2-2 2-3 4l-1-1 2-1-1-1h-1s-1 0-2 1h-1l-2 2-1 1 1-9c-1-3-2-6-2-8s2-5 3-7 2-5 3-7c0 1 1 3 2 5 1-2 1-6 1-9v-6c0-5-1-11 0-16h1l2-1v-6z" class="V"></path><path d="M431 314c0 1 1 3 2 5v1c-1 5-1 10-3 15-1 1-1 2-2 3v2h-1v-4c-1-3-2-6-2-8s2-5 3-7 2-5 3-7z" class="M"></path><path d="M437 281v-2h1c0 6-1 13 0 19v29 10 5l-2-2c-1 2-2 2-3 4l-1-1 2-1-1-1c2-2 1-6 2-9 1-8 0-17 0-25 0 1-1 2-1 3v-6c0-5-1-11 0-16h1l2-1v-6z" class="m"></path><path d="M434 288h1l2-1-1 13c0 2 1 4 0 6h0c-1-4 1-9 0-13-2 3-1 10-1 14 0 1-1 2-1 3v-6c0-5-1-11 0-16z" class="X"></path><path d="M437 281v-2h1c0 6-1 13 0 19v29 10 5l-2-2c1-3 1-7 1-11l-1-23c1-2 0-4 0-6l1-13v-6z" class="G"></path><path d="M450 233c1-3 3-6 5-8 2 4 5 8 4 13 0 0-1 1-2 1 1 1 1 1 2 1 0 2 1 5 0 7h-1c-3 0-5 0-8 1-1 2 0 2 0 4l1 1h1v1c1 0 1 0 2 1s-1 1 0 2l1 1v8l-3 3-7 7v-4l-1 1v-3c-1-2-1-5-1-7 0 0 0-1-1-1v-19l2-1 3-1 2-1 1-7z" class="Q"></path><path d="M444 242l3-1c-3 11-3 21-2 31l-1 1v-3c-1-2-1-5-1-7 0 0 0-1-1-1v-19l2-1z" class="R"></path><path d="M442 243l2-1v7 10c-1 1-1 3-1 4 0 0 0-1-1-1v-19z" class="B"></path><path d="M495 215h4 0c-3 3-6 29-9 28-5 1-11 6-15 9 3-10 1-24-2-33l1-1 6-1 1 1c1-1 5-1 7-2h2c2-1 3-1 5-1z" class="M"></path><path d="M420 187v5c1-1 1-1 2-1h1c0 1 0 2 1 4l1-2 1 6h3l-1 1h-3c0 3 0 6 1 9v5l1 31c0 3-1 6 0 9l-1 1c-1-2-3-4-4-6-2-3-5-4-7-6l-1-1v-7c0-10 1-21 0-31h1v-2c0-2 1-4 2-6l3-9z" class="m"></path><path d="M419 196l2 1v2c-1 1 0 2 0 4-2 0-3-1-5-2h0c1-2 2-3 3-5z" class="i"></path><path d="M420 187v5c1-1 1-1 2-1h1c0 1 0 2 1 4l1-2 1 6h3l-1 1h-3v-1h-1v1h-1v-4l-2-1-2 1c-1 2-2 3-3 5h0l-1 1c0-2 1-4 2-6l3-9z" class="K"></path><path d="M421 236c2-2 1-6 2-9 0-8-1-16-1-24h1v5c1 2 0 5 1 8l1 35h0c1-8 0-16 0-24 0-4 1-8 0-12v-1h1l1 31c0 3-1 6 0 9l-1 1c-1-2-3-4-4-6l1-2c-1 0-1-1-2-1v-5-5z" class="S"></path><path d="M416 201c2 1 3 2 5 2 1 11 1 22 0 33v5 5c1 0 1 1 2 1l-1 2c-2-3-5-4-7-6l-1-1v-7c0-10 1-21 0-31h1v-2l1-1z" class="B"></path><path d="M439 244l3-1v19c1 0 1 1 1 1 0 2 0 5 1 7v3l1-1v4c0 2-1 3-1 5l1 1c1-1 3-3 5-4 1 3 2 4 2 7-1 9 1 18-1 27 0 0-1 0-2-1v3c0 2 0 4 1 5 0 2-1 4-1 5 0 2-1 4-2 6h-1c0 1-1 2-2 3l-3 7-1 1-10 14h0c3-5 6-9 8-13v-5-10-29c1-3 0-6 0-8l1-17-1-13v-12l1-4z" class="I"></path><path d="M442 262c1 0 1 1 1 1 0 2 0 5 1 7v3l1-1v4c0 2-1 3-1 5l1 1h-1-2v-17-3z" class="S"></path><path d="M439 244l3-1v19 3c-2 3-1 8-1 11h-1c0-3 1-11 0-13-2 1-1 8-1 10l-1-13v-12l1-4z" class="N"></path><path d="M438 248l2 1c0 3 1 7 0 10l-2 1v-12z" class="c"></path><path d="M445 282c1-1 3-3 5-4 1 3 2 4 2 7l-1-1-2-1c-2 1-1 0-2 1-1 0-2 1-2 1-2 4 0 9-2 12 0 4-1 9 0 13l-1 12c1 3 0 10 1 12l1-1h0l-3 7-1 1-10 14h0c3-5 6-9 8-13v-5l1-2h0c2-4 1-11 1-16 1-1 0-3 0-5 0-5-1-11 0-16 1-2 1-4 2-6v1h0v-11h2 1z" class="M"></path><path d="M443 297c0 4-1 9 0 13l-1 12c-2-3-1-20-1-25l1 7c0-2 0-5 1-7z" class="N"></path><path d="M445 282c1-1 3-3 5-4 1 3 2 4 2 7l-1-1-2-1c-2 1-1 0-2 1-1 0-2 1-2 1-2 4 0 9-2 12-1 2-1 5-1 7l-1-7c1-1 1-3 1-4h0v-11h2 1z" class="U"></path><path d="M445 285s1-1 2-1c1-1 0 0 2-1l2 1 1 1c-1 9 1 18-1 27 0 0-1 0-2-1v3c0 2 0 4 1 5 0 2-1 4-1 5 0 2-1 4-2 6h-1c0 1-1 2-2 3h0l-1 1c-1-2 0-9-1-12l1-12c-1-4 0-9 0-13 2-3 0-8 2-12z" class="C"></path><path d="M445 285l2 1h0c1-1 1-1 2 0-1 0-2 1-3 1 0 4-1 8-1 12v19c-2-2 0-5-2-8-1-4 0-9 0-13 2-3 0-8 2-12z" class="S"></path><path d="M443 310c2 3 0 6 2 8l1 5h-1c0-2 0-2-1-4h0v12h0v-1l1-1c1-1 3-6 3-8h-1l2-7c0 2 0 4 1 5 0 2-1 4-1 5 0 2-1 4-2 6h-1c0 1-1 2-2 3h0l-1 1c-1-2 0-9-1-12l1-12z" class="D"></path><path d="M445 285s1-1 2-1c1-1 0 0 2-1l2 1 1 1c-1 9 1 18-1 27 0 0-1 0-2-1 1-8 1-17 0-25-1-1-1-1-2 0h0l-2-1z" class="R"></path><path d="M475 252c4-3 10-8 15-9 0 3-1 4 0 5l-2 10c-3 8-6 17-8 25-1 2-2 6-3 8-1-4-3-8-6-10-3-1-6-2-9-1-2 1-4 3-6 5-1 3-1 7 0 11 3 7-1 16-4 22l-3 6c0-1 1-3 1-5-1-1-1-3-1-5v-3c1 1 2 1 2 1 2-9 0-18 1-27 0-3-1-4-2-7-2 1-4 3-5 4l-1-1c0-2 1-3 1-5l7-7 7-6 2-1 12-9 2-1z" class="M"></path><path d="M449 311c1 1 2 1 2 1l-1 7c-1-1-1-3-1-5v-3z" class="G"></path><path d="M475 252c4-3 10-8 15-9 0 3-1 4 0 5l-21 14c-7 5-13 10-19 16-2 1-4 3-5 4l-1-1c0-2 1-3 1-5l7-7 7-6 2-1 12-9 2-1z" class="n"></path><path d="M441 179l1-1v-1c3-6 10-15 16-17 2-1 2-1 3-2l2 4c8 10 16 19 26 27v2l16 16h-3-1l3 3 3 3 2 1v1l3 2h0c1 1 1 1 2 1l10 10h-2l-1-1h0l-1 1-2-3c0 2 0 3 1 5l1 2v1l-5-6h-1l-1-1v2 1c0 1 0 2 1 3h-1c-1-1-2-3-3-3-1-1-2-2-2-3l-6-10h-1l-1 1-1-2h0-4c-2 0-3 0-5 1h-2c-2 1-6 1-7 2l-1-1-6 1-1 1h0c-1-1-4-1-5-1 1 0 3 0 4-1v-1c-1-4-8-10-10-14h-1c-1-1-18-20-20-23z" class="G"></path><path d="M461 195c-2-1-3-4-4-5-1-2-5-4-5-6-1 0-1 0-1-1l1 1c1 0 2 2 3 2 2 1 3-1 5 2v1c0 1 1 1 2 2l-1 1c-1-1 0-1-2-1h0c0 1 1 2 2 4z" class="R"></path><path d="M481 194c-1-1-3-4-4-6-4-3-8-7-10-10v-1a30.44 30.44 0 0 1 8 8c3 3 6 5 9 8h-1c-1-1-1 0-1-1l-1 2z" class="T"></path><path d="M471 177h-1c-2 0-3-2-4-3-3-2-5-5-7-7l1-1c0 1 1 1 2 1h1l2 2 6 8z" class="S"></path><path d="M451 183l-3-3c0-2 1-3 2-4s2-1 3-1c2 0 4 1 4 3v1 1c-1 2-2 3-4 3l-1 1-1-1z" class="E"></path><path d="M453 175c2 0 4 1 4 3v1 1c-2-1-2 0-3 0s-2-1-2-1v-1c0-1 1-2 1-3h0z" class="B"></path><path d="M460 188l3 3 4 4c0 1 0 1 1 2l1 1v2c0 1 3 3 2 4l-1 1h-1l-1-1-3-3-1-1s-1 0-1-1c-1-1-1-3-2-4-1-2-2-3-2-4h0c2 0 1 0 2 1l1-1c-1-1-2-1-2-2v-1z" class="d"></path><path d="M463 191l4 4c0 1 0 1 1 2l1 1v2c0 1 3 3 2 4l-1 1h-1l-1-1c-1-1-1-2-1-3s0-2-1-3-1-1-1-2c0-2-1-3-2-5z" class="P"></path><path d="M484 190c-1 0-1-1-1-2h1 1c1 1 2 2 3 2l1 1 16 16h-3-1c-1 0-3-2-4-3l-13-14z" class="N"></path><defs><linearGradient id="p" x1="472.893" y1="182.325" x2="476.552" y2="171.831" xlink:href="#B"><stop offset="0" stop-color="#343533"></stop><stop offset="1" stop-color="#504c4f"></stop></linearGradient></defs><path fill="url(#p)" d="M463 162c8 10 16 19 26 27v2l-1-1c-1 0-2-1-3-2h-1-1c0 1 0 2 1 2-5-3-9-9-13-13l-6-8v-3l-2-4z"></path><defs><linearGradient id="q" x1="492.663" y1="212.738" x2="495.251" y2="193.887" xlink:href="#B"><stop offset="0" stop-color="#101212"></stop><stop offset="1" stop-color="#494747"></stop></linearGradient></defs><path fill="url(#q)" d="M481 194l1-2c0 1 0 0 1 1h1l11 11c3 2 6 5 9 6l3 3 2 1v1l3 2h0c1 1 1 1 2 1l10 10h-2l-1-1h0l-1 1-2-3c0 2 0 3 1 5l1 2v1l-5-6h-1l-1-1v2 1c0 1 0 2 1 3h-1c-1-1-2-3-3-3-1-1-2-2-2-3l-6-10h-1l-1 1-1-2h0-4l1-1v-1a30.44 30.44 0 0 0-8-8h1l1-1 1 1h1l-11-11z"></path><path d="M507 217c3 0 9 4 11 7v1c0 2 0 3 1 5l1 2v1l-5-6-1-3c-1-2-3-3-5-5l-2-2z" class="Z"></path><path d="M488 205h1l1-1 1 1h1c3 3 6 7 11 9v1h1l-1 1h-1-1l-1 1-1-2h0-4l1-1v-1a30.44 30.44 0 0 0-8-8z" class="P"></path><path d="M504 215h1l2 2 2 2c2 2 4 3 5 5l1 3h-1l-1-1v2 1c0 1 0 2 1 3h-1c-1-1-2-3-3-3-1-1-2-2-2-3l-6-10h1l1-1z" class="T"></path><path d="M513 229c-1-1-3-3-4-5v-2c-1-1-1-1-1-2l-2-2-2-2 1-1 2 2 2 2c2 2 4 3 5 5l1 3h-1l-1-1v2 1z" class="P"></path><defs><linearGradient id="r" x1="485.169" y1="217.545" x2="467.959" y2="179.529" xlink:href="#B"><stop offset="0" stop-color="#929192"></stop><stop offset="1" stop-color="#c4c2c3"></stop></linearGradient></defs><path fill="url(#r)" d="M461 187l-1-1v-1h-1l3-1 1-2c1 0 1 1 2 2l1-1c1 1 1 2 2 3 1 0 2 2 2 2l18 17a30.44 30.44 0 0 1 8 8v1l-1 1c-2 0-3 0-5 1h-2c-2 1-6 1-7 2l-1-1-6 1-1 1h0c-1-1-4-1-5-1 1 0 3 0 4-1v-1c-1-4-8-10-10-14h-1 1c2 1 4 3 5 5l2-2h1l1-1c1-1-2-3-2-4v-2l-1-1c-1-1-1-1-1-2 0-3-4-6-6-8z"></path><path d="M461 187c4 2 6 6 9 8v1c1 3 7 6 7 9l-4-4h0c-1-1-2-3-4-3l-1-1c-1-1-1-1-1-2 0-3-4-6-6-8z" class="L"></path><path d="M470 188l18 17a30.44 30.44 0 0 1 8 8v1c-5-1-10-8-13-12-4-4-10-9-13-14z" class="H"></path><path d="M477 205c0-3-6-6-7-9 5 5 12 10 16 16l2 3h0v1c-2 1-6 1-7 2l-1-1-6 1-1 1h0c-1-1-4-1-5-1 1 0 3 0 4-1v-1c-1-4-8-10-10-14h-1 1c2 1 4 3 5 5l2-2h1l1-1c1-1-2-3-2-4v-2c2 0 3 2 4 3h0l4 4z" class="P"></path><path d="M477 209l1-1v-2l5 6 2 3c-2-1-4-2-6-4l-2-2z" class="V"></path><path d="M469 198c2 0 3 2 4 3h0l4 4 1 1v2l-1 1 2 2-1 1c-1-1-1-2-3-2l-3-3c0-1-1-2-2-2l1-1c1-1-2-3-2-4v-2z" class="g"></path><path d="M473 201l4 4 1 1v2l-1 1-4-4v-4z" class="Z"></path><defs><linearGradient id="s" x1="473.525" y1="216.523" x2="473.469" y2="206.974" xlink:href="#B"><stop offset="0" stop-color="#78787a"></stop><stop offset="1" stop-color="#979796"></stop></linearGradient></defs><path fill="url(#s)" d="M461 202h1c2 1 4 3 5 5l2-2h1c1 0 2 1 2 2l3 3c1 1 2 2 2 3l3 3c-1 0-1 0-2 1h2l-6 1-1 1h0c-1-1-4-1-5-1 1 0 3 0 4-1v-1c-1-4-8-10-10-14h-1z"></path><path d="M490 243c3 1 6-25 9-28l1 2 1-1h1l6 10c0 1 1 2 2 3 1 0 2 2 3 3h1c-1-1-1-2-1-3v-1-2l1 1h1l5 6v-1c10 12 16 26 22 40 5 16 6 32 3 49 0 1-1 3 0 4l-1 1 8 23 19 53 4 12s1 2 1 3c1 0 1 1 2 1h4 6 2 4v3c1 1 1 0 1 1v3h2 5l6-2v1 38 6c1 1 2 1 4 1h1v5l2 1c2-1 3 0 5-1 0-2 0-3 1-5h1l1-1v66 67 13c0 3-1 6 0 8l-1 1c-3 0-16 1-18-1-2 0-3 0-5 1h-14-48 0c-2 1-3 1-5 1h0l2 1h0c-6 0-13 1-20-1l-1-1c-2 0-3 0-4-1h-3v-1-5h-3c-6-1-12-2-18-4-3-1-5-2-7-4-4-1-9-7-12-9h0c-2-2-4-7-5-9l-1 1c1 1 2 3 2 5h0v3h-1-4-1l-2-2h-2l-1-2h-2-1l-2 1c-3-2-8-1-11-1l-21-1h-1c-1-3 0-6 0-9l-1-18 1-2-1-9h-2v-1c0-1-1-2-3-2h0l2 2c0 1 0 1-1 2 1 2 0 2 1 4h-1v-1c0-2-1-2-2-4-1 0-1 0-2-1s0-1-1-2l-1-1c-1-2-3-5-4-7-1-1-2-1-2-3l-1-1-1-1c-2-1-2-2-2-4-1-1-3-1-3-3 1-2 2-2 4-2h1v-1l9-23 14-39 17-47 6-16v-1c-1 1-1 1-1 2 0-2 1-4 1-5v-1c0-1 1-3 1-4 1-3 2-7 3-10 0-1 1-1 1-2v1c2-3 3-8 4-11 1-2 2-4 2-7h2 0l17-47 3-8 1-4v-2l4-12 1-1-2-1v-1c1-2 2-6 3-8 2-8 5-17 8-25l2-10c-1-1 0-2 0-5z" class="I"></path><path d="M576 578l3 3-2 1 1 1h-1l-2-2 1-3z" class="K"></path><path d="M621 469h1v5h-2c0-2 0-3 1-5z" class="R"></path><path d="M572 574c1 1 2 1 3 2l1 2-1 3c-1-2-2-4-3-7z" class="N"></path><path d="M572 409c1 5 4 10 5 15l-2-2h1-1c-1-2-3-6-3-8v-5z" class="F"></path><path d="M478 294c2 4 0 12-3 16l-2 2 1-4v-2l4-12zm141 208h1c1 1 0 2 2 3v6 17h-1v-7c0-4-2-8-1-11 1-1 1-2 1-3-1 0-1 0-2-1v-4z" class="K"></path><path d="M451 367h2l-13 38v-1c-1 1-1 1-1 2 0-2 1-4 1-5v-1c0-1 1-3 1-4 1-3 2-7 3-10 0-1 1-1 1-2v1c2-3 3-8 4-11 1-2 2-4 2-7z" class="L"></path><path d="M488 258h0c0 2-1 4-1 6v-1l1-1v-1l2-5v1c-2 9-5 18-8 27l-3 9-2-1v-1c1-2 2-6 3-8 2-8 5-17 8-25z" class="C"></path><defs><linearGradient id="t" x1="578.186" y1="586.402" x2="587.311" y2="587.138" xlink:href="#B"><stop offset="0" stop-color="#363a3a"></stop><stop offset="1" stop-color="#514d4e"></stop></linearGradient></defs><path fill="url(#t)" d="M579 581v-1c-2-2-3-4-4-7l1-1v1c3 9 11 15 17 21 4 4 7 9 9 14h-1c-2-3-3-7-6-9l-1 1c-1-1-2-3-3-3-4-5-10-9-14-14h1l-1-1 2-1z"></path><path d="M490 243c3 1 6-25 9-28l1 2-2 12-6 22c0 1-1 5-2 6v-1l-2 5v1l-1 1v1c0-2 1-4 1-6h0l2-10c-1-1 0-2 0-5z" class="D"></path><path d="M394 531c15-2 30-1 45-1h13c2 0 4-1 6 0l-27 1c-5 0-11-1-16 1-4 1-8 3-13 5-4 1-6 1-10-1-1-1-3-1-3-3 1-2 2-2 4-2h1z" class="J"></path><path d="M587 564h4 0c1 1 2 1 2 1 2 2 4 3 4 6h1v2l-1 4s0 1-1 2c0 1-1 2-1 4h-1-1c-2-1-3-2-5-3s-4-3-5-5c0-1 0 0-1-2-1-1 0-4 0-6 2-2 2-3 5-3zm18-102c1 2 1 3 2 6v-1c0-2 0-3 1-5v6c1 1 2 1 4 1h1v5l2 1v11c0 2-1 3 0 5v12c0 1 0 3 1 4v19l-2-6c-1-1-1-2-2-3 0-3-1-5-2-8v-1l-1-1c-1-3-2-6-2-10l-1-16c1-2 1-5 1-6h-2c-1-2-1-4-1-6 1 0 1 0 2-1-1-2-1-4-1-6h0z" class="M"></path><path d="M605 462c1 2 1 3 2 6v-1c0-2 0-3 1-5v6c1 1 2 1 4 1h1v5l-5 1v27c0 2 1 3 1 5h0c-1-3-2-6-2-10l-1-16c1-2 1-5 1-6h-2c-1-2-1-4-1-6 1 0 1 0 2-1-1-2-1-4-1-6h0z" class="D"></path><path d="M572 409l-39-111-15-39c-2-7-5-13-7-20v-2l16 42c2 4 5 9 5 13l2 1c1 2 1 4 2 6l1-1c1 2 1 4 1 6 0 1 0 2-1 3l7 19 8 23 19 53 4 12s1 2 1 3c1 0 1 1 2 1h4l2 7h-3l-1-1c0 2 0 3 1 5l10 28c1 5 3 11 6 15h1l1 2h2l4 1h2c0 1 0 4-1 6l1 16-2-1c-1-1-2-3-2-5l-4-10c-1-3-3-5-4-8l-9-25c-3-8-6-17-9-24-1-5-4-10-5-15z" class="l"></path><path d="M578 418h4l2 7h-3l2-1v-1c-1-1-3-3-4-3h-1v-2z" class="L"></path><path d="M532 292l2 1c1 2 1 4 2 6l1-1c1 2 1 4 1 6 0 1 0 2-1 3l-5-15z" class="U"></path><path d="M597 472h1l1 2h2l4 1h2c0 1 0 4-1 6l1 16-2-1c0-2-1-4-2-6l-6-18zm18 69h1 0c1 2 1 41 0 44v3l1 1v17 8 1 1c0 2 0 3-1 4h-1v-3l-1 3h0-1v-29-3c-2 0-3 0-4 1-1 2-1 5-1 7 1 4 1 8 2 12v6 1c1 2 1 4 0 5h-2v-1c1-1 1-3 0-4v-5-3-1c-1-3-1 0-1-2v-1l-1-6-1-5c-1-1-1-2-1-3l-1-1c-2-2-1-8-1-10 1-3 0-11 0-14l1-1c0-1 0-3-1-4h-4 0c1-2 2-2 4-2l1-1h1c1-1 1-1 2-1 1-1 2-1 2-1s0-2 1-2c0-1 0-1 1-1h0c1 4 0 8 1 12v9h0 0v-17-2c0-1 0-2 1-2v-1-1-2l1-1c0-1 0-1 1-1 0-2 0-3 1-4z" class="M"></path><path d="M603 559c2 1-1 12 1 14h4l1-2c0 1 0 1 1 2h2c1 3 1 8 1 12l-1 2c-3 1-6 1-9 0 0-1-1-1-1-2 0-3 0-7 1-10v-16z" class="J"></path><path d="M588 418h2 4v3c1 1 1 0 1 1v3h2 5l6-2v1 38c-1 2-1 3-1 5v1c-1-3-1-4-2-6h0c0 2 0 4 1 6-1 1-1 1-2 1 0 2 0 4 1 6l-4-1h-2l-1-2h-1c-3-4-5-10-6-15l-10-28c-1-2-1-3-1-5l1 1h3l-2-7h6z" class="j"></path><path d="M600 469h4c0 2 0 4 1 6l-4-1h-2l-1-2v-3h2z" class="G"></path><path d="M598 472v-3h2c0 1 0 2 1 3v2h-2l-1-2z" class="d"></path><path d="M585 434c1 2 2 6 3 7 0-2 1-4 0-6v-1h0c-1-2 0-4 0-6h0c2 2 1 6 1 8 0 4 1 9 1 13 1 3 2 5 1 8l-10-28c1 0 2 1 3 0l1 1v4z" class="F"></path><path d="M585 434h-1c-1-1-1-2-1-4h2v4z" class="U"></path><path d="M588 418h2 4v3c1 1 1 0 1 1v3h2-2-11l-2-7h6z" class="a"></path><path d="M597 425h5v1c-1 1 0 3 0 5v14 3c-1-1-1-1-1-2 0 1-1 2-1 3 0 2 1 3 0 5l-1-1v-2c0-2-1-3-1-5-1-1 0-3 0-5 1-2 0-6 1-8h-2v12c0 3-1 6-1 9 1 2 1 3 0 4h0c-1-2-1-5-1-8v-2-19-4h2z" class="e"></path><path d="M597 425h5v1c-1 1 0 3 0 5v14 3c-1-1-1-1-1-2 0 1-1 2-1 3v-14l1-1c0-2 0-6-1-7h0l-1 1v2h0l-1-1h-3v-4h2z" class="I"></path><path d="M608 423v1 38c-1 2-1 3-1 5v1c-1-3-1-4-2-6h0c-1-1-1-4-1-5h-1v3c0 1 0 4-1 5h-1l1-17v-3-14c0-2-1-4 0-5v-1l6-2z" class="C"></path><path d="M608 423v1c-1 1-3 1-3 2-1 3 0 7 0 10v26c-1-1-1-4-1-5h-1v3c0 1 0 4-1 5h-1l1-17v-3-14c0-2-1-4 0-5v-1l6-2z" class="F"></path><path d="M500 217l1-1h1l6 10c0 1 1 2 2 3 1 0 2 2 3 3h1c-1-1-1-2-1-3v-1-2l1 1h1l5 6v-1c10 12 16 26 22 40 5 16 6 32 3 49 0 1-1 3 0 4l-1 1-7-19c1-1 1-2 1-3 0-2 0-4-1-6l-1 1c-1-2-1-4-2-6l-2-1c0-4-3-9-5-13l-16-42-1-1c0-1 0-2-1-2 0-1 0-2-1-3v1 1l-1-1c0-1 0-1-1-2 0-3-2-4-1-7h0l-4-7-3 13h0l2-12z" class="O"></path><path d="M540 286c1 0 1 0 1-1l1-1 1 16v10c-1-2-1-3-2-5 1-5 0-13-1-19z" class="L"></path><path d="M536 263c4 5 7 14 6 21l-1 1c0 1 0 1-1 1 0-3-2-7-2-11h0v-3h0l-1-2v-2h0c-1-2-1-4-1-5z" class="P"></path><path d="M537 289c0 1 0 1 1 2 1-4-1-10-1-14 0-2-1-3 0-4l1 2c0 4 2 8 2 11 1 6 2 14 1 19-1-3-1-7-3-10h0c-1-1-1-4-1-6z" class="Z"></path><path d="M513 229v-1-2l1 1h1l5 6c7 8 12 20 16 30 0 1 0 3 1 5h0v2l1 2h0v3h0l-1-2c-1 1 0 2 0 4 0 4 2 10 1 14-1-1-1-1-1-2-1-13-4-27-11-39v1c-2-2-3-4-4-7-1-1-1-3-2-4h-1l-1-3-3-1c-1-1-2-2-2-4h1c-1-1-1-2-1-3z" class="L"></path><path d="M513 229v-1-2l1 1c3 4 8 10 9 14v1 1c2 2 3 4 3 7v1c-2-2-3-4-4-7-1-1-1-3-2-4h-1l-1-3-3-1c-1-1-2-2-2-4h1c-1-1-1-2-1-3z" class="k"></path><path d="M500 217l1-1h1l6 10c0 1 1 2 2 3 1 0 2 2 3 3 0 2 1 3 2 4l3 1 1 3h1c1 1 1 3 2 4 1 3 2 5 4 7 7 14 9 32 11 47l-1 1c-1-2-1-4-2-6l-2-1c0-4-3-9-5-13l-16-42-1-1c0-1 0-2-1-2 0-1 0-2-1-3v1 1l-1-1c0-1 0-1-1-2 0-3-2-4-1-7h0l-4-7-3 13h0l2-12z" class="M"></path><path d="M510 229c1 0 2 2 3 3 0 2 1 3 2 4l3 1 1 3h1c1 1 1 3 2 4 1 3 2 5 4 7 7 14 9 32 11 47l-1 1c-1-2-1-4-2-6-1-9-4-17-7-26-4-13-10-26-17-38z" class="g"></path><path d="M607 532c2 0 4 0 6 1-2 9-7 17-15 22-5 3-11 5-17 6-2 1-5 1-7 2-2 0-2 1-3 3s0 6 1 8c1 3 2 5 3 7l2 2c4 5 10 9 14 14 1 0 2 2 3 3 6 7 9 13 10 22-2 0-3 0-5 1h-14-48 0c-2 1-3 1-5 1h0l2 1h0c-6 0-13 1-20-1l-1-1c-2 0-3 0-4-1v-2c3-1 4-2 6-4 8-1 15-3 22-7l8-8c3-4 6-10 7-16l1-1c1-8 0-15-1-23l-1-3-2-3c-1-3-2-6-3-8-1-4-5-8-7-11l2-2c7 5 10 11 13 19 1 1 2 4 2 4 1 1 3 1 4 1 6-1 13-1 19-3 4 0 8-1 11-3 9-4 14-11 17-20z" class="E"></path><path d="M517 623c4 0 9 1 13 0 3-1 5-1 7-1v1h0c-2 1-3 1-5 1h0l2 1h0c-6 0-13 1-20-1l3-1z" class="R"></path><path d="M556 596v1c-5 10-14 18-25 21h1c10-6 16-12 22-23v3l1-1s1 0 1-1z" class="I"></path><path d="M537 609c-1 1-1 2-2 2-2 2-7 3-10 4l-4 1c0 1 0 1-1 2s-5 2-7 3c2 0 7 0 8-1h3c1 0 1 0 2-1h3 0l-1 1h-1c-3 1-6 2-9 2-1 0-1 0-1 1l-3 1-1-1c-2 0-3 0-4-1v-2c3-1 4-2 6-4 8-1 15-3 22-7z" class="S"></path><path d="M554 595c4-10 6-22 3-33h1 3c1 2 0 5 0 7 0 4 1 8 3 12 3 4 7 7 11 11 6 5 14 11 19 18 2 4 4 9 5 13h-14-48v-1c4 0 7-2 10-4 10-7 17-18 19-30 0-3-3-6-4-7-1 5-3 10-6 15 0 1-1 1-1 1l-1 1v-3zm-152-58c5-2 9-4 13-5 5-2 11-1 16-1l27-1c3 1 6 1 9 1h18c2 0 6-1 7 0h54 11c2 0 3 1 5 0 1 0 6-1 8 0 1 0 2 1 3 0 2 0 8-1 10 0 3 0 18-1 20 1h4c-3 9-8 16-17 20-3 2-7 3-11 3-6 2-13 2-19 3-1 0-3 0-4-1 0 0-1-3-2-4-3-8-6-14-13-19l-2 2c2 3 6 7 7 11 1 2 2 5 3 8l2 3 1 3c1 8 2 15 1 23l-1 1c-1 6-4 12-7 16l-8 8c-7 4-14 6-22 7-2 2-3 3-6 4v2h-3v-1-5h-3c-6-1-12-2-18-4-3-1-5-2-7-4-4-1-9-7-12-9h0c-2-2-4-7-5-9l-1 1c1 1 2 3 2 5h0v3h-1-4-1l-2-2h-2l-1-2h-2-1l-2 1c-3-2-8-1-11-1l-21-1h-1c-1-3 0-6 0-9l-1-18 1-2-1-9h-2v-1c0-1-1-2-3-2h0l2 2c0 1 0 1-1 2 1 2 0 2 1 4h-1v-1c0-2-1-2-2-4-1 0-1 0-2-1s0-1-1-2l-1-1c-1-2-3-5-4-7-1-1-2-1-2-3l-1-1-1-1c-2-1-2-2-2-4 4 2 6 2 10 1z" class="M"></path><path d="M559 534h1c0 7-1 15 0 22l-1-1c-1-2-1-5 0-8v-13z" class="O"></path><path d="M463 543c3-3 7-6 10-8 0 1 0 0 1 1h0c-1 1-5 6-6 6h0l-3 3c-1-1-1 0-1-1s0-1-1-1z" class="T"></path><path d="M430 554c1-2 2-4 2-6-1-4-2-6-5-8h0 1l4 4c2 4 3 5 3 9l-1 1v-1l-1 1c-1 1-1 1-2 1l-1 1v-2z" class="l"></path><path d="M432 544c1 0 1 0 1-1l-1-1 1-1v1l2-1c1 2 3 4 4 6l-1 1v-1l-2-2c0 1 1 2 1 4v4c-1 0-1 1-2 0 0-4-1-5-3-9z" class="I"></path><path d="M430 554v2c-2 3-5 4-8 7-2 0-2 0-3 1-1 0-1 1-2 1v1 1h-5l1-2c1-1 1-1 1-3 1 0 2-1 3-1 2 0 2-1 3-2 1 0 3 0 4-1 2-1 4-3 6-4z" class="Z"></path><path d="M560 558l1-1c4-4 11-4 16-4 2 0 3-1 4-1 3-1 7-2 9-4h1 0c-1 1-1 2-2 2l-10 4v1c-6 2-13 2-19 3z" class="e"></path><path d="M430 556l1-1c1 0 1 0 2-1l1-1v1c-1 4-2 7-6 9-1 1-3 2-5 3h-2l-4 1v-1-1c1 0 1-1 2-1 1-1 1-1 3-1 3-3 6-4 8-7z" class="R"></path><path d="M429 582c-2-1-5 0-7-1l1-1h1c4-1 9-1 13-1h1l1-1h1 4 1v-9h0v13h-1-15z" class="I"></path><path d="M463 543c1 0 1 0 1 1s0 0 1 1l3-3h0c-1 4-4 6-6 9-1 2-2 6-4 8-2 0-3 1-4 0l-1-2h1c2-5 5-9 8-13l1-1z" class="i"></path><path d="M462 544v3c0 1 0 3-1 4 0 1-1 2-2 4-1 1-2 1-2 2h-3c2-5 5-9 8-13z" class="h"></path><path d="M435 541l-1-1h2c-2-1-3-2-5-3l1-1c4 2 9 6 11 10v5c0 5-1 10-4 14-3 6-8 10-14 11l-2 1h-6c7-1 15-3 20-9 1-2 2-4 2-6l1-3c2-1 1-4 1-6h1l-1-1c0-2-1-3-2-5s-3-4-4-6z" class="g"></path><path d="M421 543l2-1c1 0 3 1 4 2s2 2 2 4c0 1-1 3-2 5-2 1-4 1-7 1l-1-1h2l-1-1-1-1v-1l2 1h0l-3-3v-2c1-2 2-2 3-3z" class="G"></path><path d="M458 531h2c2 0 3 1 4 3 1 1 2 3 1 4 0 2-2 4-4 5h-4c-2-1-3-2-3-4-1-1-1-4 0-6s2-2 4-2z" class="E"></path><path d="M474 536h1c2-1 4 0 5 0h10 37c2 0 6 0 8 1 1 0 2 1 3 2h1l-7-6c3 0 6 0 9 1l-2 2c2 3 6 7 7 11l-2-2-4-4v-1l-1 1c-1 0-3-2-4-3-13-1-27 0-40-1h-17c-1 1-2 1-2 2-1 0-1 0-2-1h0v-2z" class="V"></path><path d="M392 536c4 2 6 2 10 1 0 1 1 2 1 3 1 1 2 2 4 3 1 0 1 1 2 2 1 0 1 1 2 1v1c-1 0 0 0-1 1h0l1 1v1c-1 0-2-1-3-2v-1l-2-1c0 1 1 3 2 4h1c1 0 2 1 3 2v4h-2v-1c0-1-1-2-3-2h0l2 2c0 1 0 1-1 2 1 2 0 2 1 4h-1v-1c0-2-1-2-2-4-1 0-1 0-2-1s0-1-1-2l-1-1c-1-2-3-5-4-7-1-1-2-1-2-3l-1-1-1-1c-2-1-2-2-2-4z" class="b"></path><path d="M437 553v-4c0-2-1-3-1-4l2 2v1l1-1c1 2 2 3 2 5l1 1h-1c0 2 1 5-1 6v1c-1 0-1 1-1 2-1 1-2 3-2 4-2 4-7 6-10 8-3 1-6 1-9 1 1-1 1-1 2-1h2c2-1 3-1 5-2h0-4l-1 1-1-1-1-1h0c1-1 1-1 1-2v-3h2c2-1 4-2 5-3 4-2 5-5 6-9l1-1c1 1 1 0 2 0z" class="Z"></path><path d="M421 566h2v1h0c0 1 1 1 1 2h-1v1c2 0 3 0 5-2v1c-2 1-3 2-5 2v1l-1 1-1-1-1-1h0c1-1 1-1 1-2v-3z" class="b"></path><path d="M435 553c1 1 1 0 2 0v4l-3 6c-2 2-4 3-7 4-1 1-1 2-3 2 0-1-1-1-1-2h0v-1c2-1 4-2 5-3 4-2 5-5 6-9l1-1z" class="O"></path><path d="M574 532c2-1 5-1 6 0 3 0 4 2 6 4 1 2 1 4 1 6-1 4-3 5-7 7h-6c-1 0-3-2-4-3-2-3-2-6-1-9s3-4 5-5z" class="J"></path><path d="M445 569c1-7 1-15-1-21s-8-11-13-14l-4-1h15c3 2 5 5 7 9 2 3 2 7 3 11v4h1l1 2c1 1 2 0 4 0l-1 3h-1c-2 3-2 14-1 17v1c1 1 1 1 2 1 1 1 2 0 3 0v1h-8-7v-13z" class="E"></path><path d="M452 553v4h1l1 2c1 1 2 0 4 0l-1 3h-1c-2 3-2 14-1 17v1c1 1 1 1 2 1 1 1 2 0 3 0v1h-8v-29z" class="F"></path><path d="M445 582h7 8v2h-4-2l6 1c-2 1-3 1-5 1l1 1h3c-1 1 0 1-1 1l2 1 1 1-1 1c1 1 2 3 2 5h0v3h-1-4-1l-2-2h-2l-1-2h-2-1l-2 1c-3-2-8-1-11-1l-21-1h-1c-1-3 0-6 0-9 1 0 1-1 2-1 0-1 1-1 1-1l13-1h15 1z" class="j"></path><path d="M417 590l15-1v2h-7c-2 0-6 0-8-1z" class="L"></path><path d="M432 589c8 0 18-1 26 1-3 2-8 2-11 2l-15-1v-2zm17-3c2 0 5-1 6 0l1 1h3c-1 1 0 1-1 1h-18-21c2-1 6-2 8-2h18 4z" class="R"></path><path d="M427 586h18c-1 1-2 1-4 1h-10c-1 0-2-1-3 0 4 0 8 0 12 1h-21c2-1 6-2 8-2z" class="W"></path><path d="M458 590l2 1c1 1 2 3 2 5h0v3h-1-4-1l-2-2h-2l-1-2h0l1-2c-2-1-3-1-5-1 3 0 8 0 11-2z" class="P"></path><path d="M452 593l3 3c3-1 4-1 7 0h0v3h-1-4-1l-2-2h-2l-1-2h0l1-2z" class="O"></path><path d="M455 596c3-1 4-1 7 0h0-1l-2 1c-1 0-3 0-4-1z" class="H"></path><path d="M419 593c-3-1-3-1-4-3v-1l2 1c2 1 6 1 8 1h7l15 1c2 0 3 0 5 1l-1 2h0-2-1l-2 1c-3-2-8-1-11-1l-21-1c2-1 3-1 5-1z" class="X"></path><path d="M419 593c-3-1-3-1-4-3v-1l2 1c2 1 6 1 8 1 3 1 6 0 9 1 1 1 3 1 4 1h0-19z" class="F"></path><path d="M425 591h7l15 1c2 0 3 0 5 1l-1 2h0-2-1c-2 0-3 0-4-1v-1h-6c-1 0-3 0-4-1-3-1-6 0-9-1z" class="Z"></path><path d="M445 582h7 8v2h-4-2l6 1c-2 1-3 1-5 1-1-1-4 0-6 0h-4-18c-2 0-6 1-8 2h-4v-1h2l1-1h-3v-1c1 0 2 0 3-1-1 0-1 0-2-1l13-1h15 1z" class="k"></path><path d="M443 584c1 0 3 0 4 1h1l1 1h-4-18-6l-1-1c7-2 16-1 23-1z" class="D"></path><path d="M445 582h7 8v2h-4-2l6 1c-2 1-3 1-5 1-1-1-4 0-6 0l-1-1h-1c-1-1-3-1-4-1-2-1-6 0-8-1l9-1h1z" class="L"></path><path d="M448 585l5-1h1l6 1c-2 1-3 1-5 1-1-1-4 0-6 0l-1-1z" class="G"></path><path d="M474 536h0v2h0c1 1 1 1 2 1 0-1 1-1 2-2h17c13 1 27 0 40 1 1 1 3 3 4 3l1-1v1l4 4 2 2c1 2 2 5 3 8l2 3h-5c-2 0-3 0-5 2h-3-7-5-1-1v-2h0v-2l2 1v-1l-1-1c-1-1-1-3-2-3h-2v4l-1 1h-1c-1 0-4-1-5 0l-6-1c-3 1-8 1-11 1h-1-2-3-3 0-2c-2 1-4 0-6 1h-5-1-3c-1 0-1 1-2 2-1-1-6-1-7 0h-1-2v1l-2 1 1-3c2-2 3-6 4-8 2-3 5-5 6-9 1 0 5-5 6-6z" class="j"></path><path d="M474 558v-11l2-2c0 3 1 11-1 13h-1z" class="U"></path><defs><linearGradient id="u" x1="464.942" y1="539.781" x2="467.572" y2="555.301" xlink:href="#B"><stop offset="0" stop-color="#6b6868"></stop><stop offset="1" stop-color="#7a7c7c"></stop></linearGradient></defs><path fill="url(#u)" d="M474 536h0v2h0c1 1 1 1 2 1-6 6-9 12-13 19h0 8c-1 0-1 1-2 2-1-1-6-1-7 0h-1-2v1l-2 1 1-3c2-2 3-6 4-8 2-3 5-5 6-9 1 0 5-5 6-6z"></path><path d="M544 545l2 2c1 2 2 5 3 8l2 3h-5c-2 0-3 0-5 2h-3-7-5-1-1v-2h0v-2l2 1v-1l-1-1c0-1 0-2 1-3 0 1 1 2 1 3h0l1 1v-2l1-1c0 1 0 2 1 3 0 0 0-1 1-2v1c0-1 1-1 1-2 1-2 0-6 1-8h0c2 2 1 0 3 0 1 1 0 1 2 0 0 1 0 1 2 1 1-1 2-1 3-1h1z" class="M"></path><path d="M544 545l2 2c1 2 2 5 3 8l-2 1v-1h0c0-2-1-4-1-5-1-1-1 0-1-1l-2-4h1z" class="U"></path><path d="M543 557h0v-3c1-1 1-1 0-2l1-1c0 1 1 2 1 3l-1 1v1h0l1-1h2v1l2-1 2 3h-5l-3-1z" class="K"></path><path d="M525 555c0-1 0-2 1-3 0 1 1 2 1 3h0l1 1v-2l1-1c0 1 0 2 1 3 0 0 0-1 1-2v1l1 1c1 0 2 0 4 1h7l3 1c-2 0-3 0-5 2h-3-7-5-1-1v-2h0v-2l2 1v-1l-1-1z" class="f"></path><path d="M476 545l-1-1 1-1c2 1 8 0 11 0h34 2l1 1-1 1 1 1c0 2-1 4 0 5 1 0 2 0 3 1l1-1 1 2-1 1v2l-1-1h0c0-1-1-2-1-3-1 1-1 2-1 3-1-1-1-3-2-3h-2v4l-1 1h-1c-1 0-4-1-5 0l-6-1c-3 1-8 1-11 1h-1-2-3-3 0-2c-2 1-4 0-6 1h-5c2-2 1-10 1-13z" class="O"></path><path d="M487 547h3c1 1 2 1 4 1h0c1-1 2-1 2-1h2c1 0 1 1 2 1h8c1-1 2-1 3-1l1 1h-2v1 1l-1 1c1 0 2 0 2 1h3v1 4l-6-1c-3 1-8 1-11 1h-1-2v-7l-1-2v1l-1 1c-1-1-2-1-4-1v-1l-1-1z" class="F"></path><path d="M494 550h1 1v7h-2v-7z" class="e"></path><path d="M497 557l1-6h3v4l1 1v-1h1v1c1-1 1-1 2-1v1c1 0 1-1 2-1l1 1 1-1v1h-1c-3 1-8 1-11 1z" class="H"></path><path d="M476 545l-1-1 1-1c2 1 8 0 11 0h34v2h0c-3-1-13 0-16-1l-4 1v1l-1 1c-1-1-2 0-3-2-1 1-1 0-1 1-1-1-1 0-1-1l-1 1h-1-1l-1-1-1 1c-1-1-1 0-1-2l-1 1v1c-1 0-1 0-1-1l-1 1h-3v1h0 3v1c0 1 0 0 1 1v-2l1 1v1c2 0 3 0 4 1l1-1v-1l1 2v7h-3-3 0-2c-2 1-4 0-6 1h-5c2-2 1-10 1-13z" class="T"></path><path d="M477 549c0-1 0-3 1-5h2c1 2 1 3 2 4v1h-1-4z" class="O"></path><path d="M482 549h1 2c1 2 0 4 0 6h-1v-2-1-1l-1-1v1c0 2 0 3-2 4h0v-6h1z" class="X"></path><path d="M477 549h4v6h0l-1 2c-1 0-1 0-1-1h-1l-1 1v-8z" class="e"></path><path d="M478 556c0-2 0-4 1-6h1c0 1 0 2-1 3v1l1 1h1 0l-1 2c-1 0-1 0-1-1h-1z" class="H"></path><path d="M480 544c8 0 17-1 25 0l-4 1v1l-1 1c-1-1-2 0-3-2-1 1-1 0-1 1-1-1-1 0-1-1l-1 1h-1-1l-1-1-1 1c-1-1-1 0-1-2l-1 1v1c-1 0-1 0-1-1l-1 1h-3v1h0 3v1c0 1 0 0 1 1-2 2 0 6-1 8l-1-2c0-2 1-4 0-6h-2-1v-1c-1-1-1-2-2-4z" class="N"></path><path d="M514 557c1-1 4 0 5 0h1l1-1v-4h2c1 0 1 2 2 3l1 1v1l-2-1v2h0v2h1 1 5 7 3c2-2 3-2 5-2h5l1 3c1 8 2 15 1 23l-1 1c-1 6-4 12-7 16l-8 8c-7 4-14 6-22 7-2 2-3 3-6 4v2h-3v-1-5h-3c-6-1-12-2-18-4-3-1-5-2-7-4-4-1-9-7-12-9h0c-2-2-4-7-5-9l-1-1-2-1c1 0 0 0 1-1h-3l-1-1c2 0 3 0 5-1l-6-1h2 4v-2-1c-1 0-2 1-3 0-1 0-1 0-2-1v-1c-1-3-1-14 1-17h1l2-1v-1h2 1c1-1 6-1 7 0 1-1 1-2 2-2h3 1 5c2-1 4 0 6-1h2 0 3 3 2 1c3 0 8 0 11-1l6 1z" class="H"></path><path d="M493 586v1c-1-1-1-3-1-4v-9h1v6 1l1 3v1l-1 1z" class="m"></path><path d="M489 571v-2-1h1v-6h1c0 2 0 2 1 4 0 1 0 0 1 1 0-1 0-1 1-2l-2-1v-2-1h1c1 0 1 2 2 3l-1 1c1 0 0 0 1 1l-2 2-4 3z" class="h"></path><path d="M489 571l4-3c1 1 1 2 0 4v1c-1 0-1 0-1-1-1 1-1 1-1 2h0c1 4 0 10 0 14h0c-1-1-1-4-1-6l-1-11z" class="T"></path><path d="M475 574h1v-2c1-2 1-8 1-10-1-1-1-1-1-2h1v3c1 2 1 5 1 8l-1 4c0 2 1 5 0 6v4 9l1 1h-1l-1 1v1c-1-2-1-2-1-3 1-2 0-16 0-20z" class="I"></path><path d="M494 584c0-2 0-3 1-5l2 1h1 1l1 1-1 1c-1 1-1 1-2 3v4 3c1 1 1 1 2 1 1-1 4-1 6-1-1 1 0 1-1 1v1c0 2 1 7 0 9h-1c-1-1-3-1-5-1v-1l-1 5h0-3v-10s-1-1 0-2c0-1-1-1-1-1 1-2 1-5 0-7l1-1v-1z" class="K"></path><path d="M494 584c0-2 0-3 1-5l2 1h1 1l1 1-1 1c-1 1-1 1-2 3v-2c-2 2-1 6-1 9v1c-3-2-1-5-2-7v-1-1z" class="e"></path><path d="M497 606c-1-4-1-8-1-12 3 0 5 0 8-1v1c0 2 1 7 0 9h-1c-1-1-3-1-5-1v-1l-1 5h0z" class="b"></path><path d="M478 571c0-1 1-1 1-2 1-2-1-5 1-7v-2h1c0 1 0 4 1 5 1-2 0-2 1-3v-1 1c0 1 0 3 1 5v27h-7v-9-4c1-1 0-4 0-6l1-4z" class="F"></path><path d="M461 560h1c1-1 6-1 7 0 2-1 4 0 6-1v9 6c0 4 1 18 0 20 0 1 0 1 1 3v-1l1-1h1 1c2 1 3 1 4 0h1v1c0 4 0 8-1 12-1-4-1-7-1-12l-1 1c0 3 1 6 0 9l-1 1h6v1c-3 1-5 0-8 0h0c-4-1-9-7-12-9h0c-2-2-4-7-5-9l-1-1-2-1c1 0 0 0 1-1h-3l-1-1c2 0 3 0 5-1l-6-1h2 4v-2-1c-1 0-2 1-3 0-1 0-1 0-2-1v-1c-1-3-1-14 1-17h1l2-1v-1h2z" class="b"></path><path d="M462 561h0l1 1v2c0 1 0 2 1 3s1 2 0 4h0-2c-1-2-1-4-1-6v-1c0-1 0-2 1-3z" class="I"></path><path d="M460 580v-5c0-3 0-7 1-10 0 2 0 4 1 6h2v1l-1 3c0 1-1 2-2 4l-1 1z" class="F"></path><path d="M457 562l2-1v-1h2l1 1c-1 1-1 2-1 3v1c-1 3-1 7-1 10v5l1 1c4-1 5-1 7-4v1c-1 2-3 3-5 5 2 7 6 13 10 19 1 1 2 3 4 4l3 1h6v1c-3 1-5 0-8 0h0c-4-1-9-7-12-9h0c-2-2-4-7-5-9l-1-1-2-1c1 0 0 0 1-1h-3l-1-1c2 0 3 0 5-1l-6-1h2 4v-2-1c-1 0-2 1-3 0-1 0-1 0-2-1v-1c-1-3-1-14 1-17h1z" class="f"></path><path d="M457 562l2-1v-1h2l1 1c-1 1-1 2-1 3-1-1-2-1-3-2l-1 2-1-2h1z" class="g"></path><path d="M460 585h2c0 1 0 1-1 2h1l1 1c-1 1-1 0-3 1l-2-1c1 0 0 0 1-1h-3l-1-1c2 0 3 0 5-1z" class="W"></path><path d="M455 580v-1c-1-3-1-14 1-17l1 2c0 2 0 15-1 15l-1 1z" class="P"></path><path d="M514 557c1-1 4 0 5 0h1l1-1v-4h2c1 0 1 2 2 3l1 1v1l-2-1v2h0v2h1c1 1 1 2 1 3v6l-2 2-2 1v4h-1v5l-1 4v9h-1l-9-1c-1 0-1 0-1-1h-4c-2 0-5 0-6 1-1 0-1 0-2-1v-3-4c1-2 1-2 2-3l1-1-1-1h-1c-1-2-1-3-2-4l-1-2v-1-1c0-3 1-6 3-8 1-1 1-1 3-1l1-1c-2 0-3 0-5 1h-1l2-2h-1s-1 0-1 1h0c-2-1-2-1-3-2v-1h-4c-4 0-10-1-14 0-2 1-4 0-6 1 1-1 1-2 2-2h3 1 5c2-1 4 0 6-1h2 0 3 3 2 1c3 0 8 0 11-1l6 1z" class="h"></path><path d="M511 564v1c-3-3-5-1-8-2v-1c3 0 9-1 12 0h2v1 1l-2-1-1 1h-3z" class="c"></path><path d="M513 574c0 3-1 4-2 6-1 1-1 2-1 2-1 1-2 2-3 1h-1 0-2c-2-1-3-2-4-2l-1-1h1c-1-1-2-2-2-3h1c0 1 1 1 2 2h0l3 2c2-1 4 0 5-1v-1-1l4-4z" class="T"></path><path d="M514 564l1-1 2 1c1 2 2 3 2 5-1 0-1 1-1 1h-2v3l-1 1h-1l1-1c-2-1-2-2-3-3l-1-1c1-1 2-2 3-2l-3-3h3z" class="i"></path><path d="M511 564h3c1 1 3 2 3 4v1c-1 0-2-1-3-2l-3-3z" class="K"></path><path d="M505 579l-2-1c-2 0-3-1-3-2l-1-1v-1c-1-2-1-4 0-6s3-2 5-3h1c2 0 2 1 4 2l2 2 1 1-2 1 2 2h1c1 0 0 0 1 1h-1 0l-4 4v1h-4z" class="H"></path><path d="M505 579v-2l-2-1h1c2-1 2-2 2-4-1 0-2-1-3-1v-2h3 1l-1 2h1c2 0 2 0 3-1v1l2 2h1c1 0 0 0 1 1h-1 0l-4 4v1h-4z" class="K"></path><path d="M514 557c1-1 4 0 5 0h1l1-1v-4h2c1 0 1 2 2 3l1 1v1l-2-1v2h0v2h-4-3-18-2c-2-1-3-1-4-1h-4c-4 0-10-1-14 0-2 1-4 0-6 1 1-1 1-2 2-2h3 1 5c2-1 4 0 6-1h2 0 3 3 2 1c3 0 8 0 11-1l6 1z" class="g"></path><path d="M524 560h1c1 1 1 2 1 3v6l-2 2-2 1v4h-1v5l-1 4v9h-1l-9-1c-1 0-1 0-1-1h-4c-2 0-5 0-6 1-1 0-1 0-2-1v-3-4c1-2 1-2 2-3l1-1c1 0 2 1 4 2h2 0 1c1 1 2 0 3-1 0 0 0-1 1-2 1-2 2-3 2-6h0 1c-1-1 0-1-1-1h-1l-2-2 2-1c1 1 1 2 3 3l-1 1h1l1-1v-3h2s0-1 1-1c0-2-1-3-2-5v-1-1c1-1 2-1 3-2h4z" class="K"></path><path d="M513 588c1-1 1-2 2-3l1 1-1 2h-2z" class="e"></path><path d="M503 585h3c-1 1 0 1-1 1v3h-1c0-2-1-3-1-4h0z" class="m"></path><path d="M518 579v4l-2 2h-1c1-1 1-1 1-2 0-2 1-2 2-4z" class="H"></path><path d="M518 579c1 0 1-3 2-3 1 1 0 3 1 5l-1 4v-2h-2v-4z" class="e"></path><path d="M518 583h2v2 9h-1l-1-2h1c0-2-1-3-1-4h-3l1-2 1-1h-1l2-2z" class="F"></path><path d="M521 576c0-4 0-10-1-13v-2h1l1 3h1c-1-2-1-2-1-3h0c1 1 2 2 2 4v4c-1 1-2 1-2 3v4h-1z" class="h"></path><path d="M500 581c1 0 2 1 4 2h-2 0l1 2h0c0 1 1 2 1 4-1 0-1 0-2-1l-4-1c0 1-1 2-1 2v-4c1-2 1-2 2-3l1-1z" class="H"></path><path d="M497 585c1-2 1-2 2-3 0 1 0 2 1 3v1h0l2 2-4-1c0 1-1 2-1 2v-4z" class="O"></path><path d="M512 570c1 1 1 2 3 3l-1 1h1l1-1 2-1h1l1 1-1 1h-1c-1 1-1 1-1 2s0 1-1 2v1c0 1-1 3-2 4-2 0-2 0-3 1-1 0-1 1-1 2l-1-1 1-3s0-1 1-2c1-2 2-3 2-6h0 1c-1-1 0-1-1-1h-1l-2-2 2-1z" class="c"></path><path d="M497 589s1-1 1-2l4 1c1 1 1 1 2 1h1l8-1h2 3c0 1 1 2 1 4h-1l1 2-9-1c-1 0-1 0-1-1h-4c-2 0-5 0-6 1-1 0-1 0-2-1v-3z" class="b"></path><path d="M509 592h9l1 2-9-1c-1 0-1 0-1-1z" class="H"></path><path d="M541 560c2-2 3-2 5-2h5l1 3c1 8 2 15 1 23l-1 1c-1 6-4 12-7 16l-8 8c-7 4-14 6-22 7-2 2-3 3-6 4v2h-3v-1-5h-3c-6-1-12-2-18-4-3-1-5-2-7-4h0c3 0 5 1 8 0v-1h1c2-1 5 0 7-1h3 0l1-5v1c2 0 4 0 5 1h1c1-2 0-7 0-9v-1c1 0 0 0 1-1h4c0 1 0 1 1 1l9 1h1v-9l1-4v-5h1v-4l2-1 2-2v-6c0-1 0-2-1-3h1 5 7 3z" class="M"></path><path d="M509 620v-4h6c-2 2-3 3-6 4z" class="a"></path><path d="M497 606h8 3s0-1 1-1v1h8 4 0 2 0 4l8 1h2 1c-1 0-1 1-1 1h-3-9-26-13v-1h1c2-1 5 0 7-1h3 0z" class="l"></path><path d="M505 592h4c0 1 0 1 1 1l9 1h1l1 6v6h-4-8v-1c-1 0-1 1-1 1h-3-8l1-5v1c2 0 4 0 5 1h1c1-2 0-7 0-9v-1c1 0 0 0 1-1z" class="N"></path><path d="M498 602c1 1 1 2 2 2h0c2-1 5 1 6 0v-9h1v1c0 1 0 1 1 2 1-1 0-2 0-3l1-1c1 1 0 1 1 2 0 1 0 3 1 4h0v2c1 1 0 2 0 2h1 3l2 2h-8v-1c-1 0-1 1-1 1h-3-8l1-5v1z" class="c"></path><path d="M510 593l9 1h1l1 6-1 4c-1 0-2-1-3-1-2-1-3-2-5-3v-4c-1-1-2-1-2-2v-1z" class="I"></path><defs><linearGradient id="v" x1="547.309" y1="562.323" x2="550.311" y2="596.623" xlink:href="#B"><stop offset="0" stop-color="#1d1c1b"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#v)" d="M541 560c2-2 3-2 5-2h5l1 3c1 8 2 15 1 23l-1 1c-1 6-4 12-7 16-1-2 1-9 2-11v-5c1-1 0-4 1-5 0-2 1-1 1-3-1 0-2 0-3-1v-3-2h0-1l-1-1 1-2c0-1 0-2 1-3s1 0 1-1c-1-1-1-2-2-3l-2-1h-2z"></path><path d="M541 560c2-2 3-2 5-2h5l1 3h-7l-2-1h-2z" class="L"></path><path d="M541 560h2c-1 2-1 4-1 6v6c0 2 0 6-1 8v16c-3 1-7 0-10 0v-1h2c1-2 1-4 0-6 0-2 0-5 1-7v-11c0-2-1-2-2-4s-1-4-1-7h7 3z" class="H"></path><path d="M538 566l-1 13v2 1l1 2h-2 0c0-1 0-1-1-1h0c-1-1-1-1 0-1 0-4-1-8 0-11v-1-1l1-1h1s0-1 1-1v-1z" class="I"></path><path d="M531 560h7v6 1c-1 0-1 1-1 1h-1v-2l-2-2-2 3c-1-2-1-4-1-7z" class="N"></path><path d="M538 584v1c1 2-1 7 0 9 2-2 0-14 2-14v4 11h-5c-2-2-1-9 0-12h0c1 0 1 0 1 1h0 2z" class="b"></path><path d="M532 567l2-3 2 2v2l-1 1v1 1c-1 3 0 7 0 11-1 0-1 0 0 1-1 3-2 10 0 12h5v-11-4h1v16c-3 1-7 0-10 0v-1h2c1-2 1-4 0-6 0-2 0-5 1-7v-11c0-2-1-2-2-4z" class="T"></path><path d="M525 560h1 5c0 3 0 5 1 7s2 2 2 4v11c-1 2-1 5-1 7 1 2 1 4 0 6h-2v1h-1l-1-1v-3h-1v3c-1 4 0 8-1 11h-4 0-2 0v-6l-1-6v-9l1-4v-5h1v-4l2-1 2-2v-6c0-1 0-2-1-3z" class="U"></path><path d="M527 561l1-1 1 1v1c-1 2 0 5 0 7v11h-1l-1-7v-12z" class="m"></path><path d="M525 560h1l1 1v12l1 7-1 4h0-1v-4c0-3 1-7-1-9h-1l2-2v-6c0-1 0-2-1-3z" class="F"></path><path d="M527 573l1 7-1 4h0-1v-4c1-3 0-5 1-7z" class="I"></path><path d="M528 580h1v5h1v4c-1 2 0 5 0 7l-1-1v-3h-1v3l-1-1v-5-5h0l1-4z" class="H"></path><path d="M528 580h1v5c-1 1-1 2-1 3l-1 1v-5h0l1-4z" class="c"></path><path d="M530 585c2 1 1 3 2 4 0-5-2-13 0-18v5c0 1 0 2 1 3v-7l1-1v11c-1 2-1 5-1 7 1 2 1 4 0 6h-2v1h-1c0-2-1-5 0-7v-4z" class="O"></path><path d="M524 571h1c2 2 1 6 1 9v4h1v5 5l1 1c-1 4 0 8-1 11h-4 0-2 0v-6l-1-6v-9l1-4v-5h1v-4l2-1z" class="c"></path><path d="M526 584h1v5 5c-1 4 0 8-1 12h-1v-1-12l1-9z" class="j"></path><path d="M522 578h1v15 2c1 2 1 9 0 11l-1-1c0-3 1-8 0-12v-1-14z" class="I"></path><path d="M521 576h1v2 14 1c1 4 0 9 0 12l1 1h0 0-2 0v-6l-1-6v-9l1-4v-5z" class="X"></path><path d="M524 571h1c2 2 1 6 1 9v4l-1 9v-4c0-1 0 0-1-1v-1-11-1l-1 1v2h-1v-2-4l2-1z" class="U"></path><path d="M503 268c3 5 5 11 7 17l16 44 49 137 17 47h-87-88l86-245z" class="n"></path><path d="M547 425l12 33h0c-2-1-8-22-10-26-1-2-2-2-4-3h0v-3l1 1 1-2h0z" class="U"></path><path d="M446 464h3c-1 2-2 2-2 4l2 2h-2c-1 2-2 4-2 5v1l2 1c-1 1-2 0-3 0h0c2 3 3 2 3 5h0l-1-1c0-1 0-1-1-2h-2v1h1l3 6h0 0c-2-1-3-4-4-5-1 2-4 7-4 9l4 4h-1c-1-1-2-2-4-3h0v2h-1c0 1 0 2-1 2v-4l5-14c1-4 3-9 5-13z" class="c"></path><path d="M446 464h3c-1 2-2 2-2 4l2 2h-2c-1 2-2 4-2 5v1l2 1c-1 1-2 0-3 0-2 1-2 1-2 3l-1 1c0 1 0 1-1 2h0 0c1-2 1-4 1-6 1-4 3-9 5-13z" class="H"></path><path d="M436 495c1 0 1-1 1-2h1v-2h0c2 1 3 2 4 3h1v1c2 1 1 0 2 1s2 1 3 2l2 1c7 1 14 0 21 0h2v1h-41l4-9v4z" class="K"></path><path d="M436 495c1 0 1-1 1-2h1v-2h0c2 1 3 2 4 3h1v1c2 1 1 0 2 1s2 1 3 2l2 1h-3c-3 0-6 0-8-1l-2-1-1-2z" class="C"></path><path d="M487 350c1 1 1 2 1 3h0v-1l1-1c0-1 0-2 1-3l1 1-2 4-6 17c0 1-2 3-2 4l-12 33c-2 5-5 9-6 14-1 2-2 3-2 5l-2-1 28-75z" class="H"></path><defs><linearGradient id="w" x1="489.088" y1="325.64" x2="501.477" y2="335.411" xlink:href="#B"><stop offset="0" stop-color="#221d24"></stop><stop offset="1" stop-color="#2d322b"></stop></linearGradient></defs><path fill="url(#w)" d="M487 350l16-45c2 4 4 9 5 14l13 34 17 45 7 19c0 3 2 5 2 8h0c-1-1-2-4-3-6l-5-16-20-54c-4-11-8-23-13-34l-1-2-1 1h0l-1 1c-1 1-1 3-1 4l-4 11-7 19-1-1c-1 1-1 2-1 3l-1 1v1h0c0-1 0-2-1-3z"></path><path d="M498 330l4-11c0-1 0-3 1-4l1-1h0l1-1 1 2c5 11 9 23 13 34l20 54 5 16c1 2 2 5 3 6l-1 2-1-1v3h0c2 1 3 1 4 3 2 4 8 25 10 26h0c1 1 1 2 1 3 1 4 3 7 4 10 1 1 1 2 1 3v1l1 1c0 2 1 4 2 6 0 1 1 2 1 4 1 1 2 2 2 4 1 2 2 5 3 8l1 1h-1-1l-48 1h-24-28v-1h-2c-7 0-14 1-21 0l-2-1c-1-1-2-1-3-2s0 0-2-1v-1l-4-4c0-2 3-7 4-9 1 1 2 4 4 5h0 0l-3-6h-1v-1h2c1 1 1 1 1 2l1 1h0c0-3-1-2-3-5h0c1 0 2 1 3 0l-2-1v-1c0-1 1-3 2-5h2l-2-2c0-2 1-2 2-4h-3l1-4 9-24 3-11 2 1c0-2 1-3 2-5 1-5 4-9 6-14l12-33c0-1 2-3 2-4l6-17 2-4 7-19z" class="E"></path><path d="M471 459c3 0 6-1 8 1v1 1l-1-1h-2c-1 0 0 0-1-1s-3-1-4-1z" class="D"></path><path d="M515 428h6c-3 3-6 6-8 9-1-3 0-6 2-9z" class="F"></path><path d="M543 429c-2-1-5-2-8-1-8 1-12 5-17 10h0c2-3 6-7 9-9 4-3 13-4 18-3v3h-2z" class="c"></path><path d="M520 447c2-4 5-7 8-9 5-3 8-4 14-3l3 3c-6-2-12-1-17 2-3 2-5 5-7 7h-1z" class="i"></path><path d="M459 425l2 1h6c3 0 6 1 8 2h1c-3 0-6-1-9 0-2 1-5 0-7 2l1 1c-1 1-1 1-2 1l1 1h1c3 0 10 0 13 1h-3-4c-2 0-6 0-8 1-2 0-1 0-2 1h-1l3-11z" class="F"></path><path d="M484 392c1-8 4-15 5-23 0-1 0-1 1-2v-1 2 1c-1 1-1 2-1 3 0 3-1 5-2 8 0 2 0 4-1 6 0 2 0 4-1 6h1c0 2 0 4-1 6-1 7-1 14-3 21 0 2 2 5 3 7h0c-2-2-4-5-5-8h0l1 1 1-1h-1l1-3 1-1c0-2-1-3 1-4l-1-1h-1v1l-1-1h1v-8c1-2 2-6 2-9z" class="C"></path><path d="M459 435c2-1 6-1 8-1-1 0-2 1-3 2 0 1-2 2-2 3v5h-1c-1 1-1 1 0 2v-1c1 1 2 1 3 2l-1 1-1 3c-1-1 0-1-1-1v-1c-1 0-2 0-3-1h0l-1-1-3-1c0-4 2-7 5-11z" class="D"></path><defs><linearGradient id="x" x1="532.997" y1="478.896" x2="552.024" y2="483.517" xlink:href="#B"><stop offset="0" stop-color="#454346"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#x)" d="M520 447h1c-3 6-5 13-3 20v1c2 3 3 6 6 9 5 4 10 6 17 6s10-3 14-8l1 1-1 2c-1 3-3 5-6 6-5 3-12 2-17 0-6-3-12-8-14-15-3-8-2-15 2-22z"></path><path d="M456 436h1c1-1 0-1 2-1-3 4-5 7-5 11l3 1 1 1h0c1 1 2 1 3 1v1c1 0 0 0 1 1-1 1-1 1-1 2l-2 3c1 1 3 1 4 2h-6-2v-2c-1 1-1 0-2 1l-3-1-1 2-1 2h-1l9-24z" class="N"></path><path d="M450 456c1-3 1-4 3-6l1 1c-1 1-2 2-2 3s1 2 1 3l-3-1z" class="Y"></path><path d="M458 448h0c1 1 2 1 3 1v1c1 0 0 0 1 1-1 1-1 1-1 2l-2 3c1 1 3 1 4 2h-6-2v-2c-1 1-1 0-2 1 0-1-1-2-1-3s1-2 2-3c1 0 2-1 3-1l1-2z" class="C"></path><path d="M543 429h2 0c2 1 3 1 4 3 2 4 8 25 10 26h0c1 1 1 2 1 3 1 4 3 7 4 10l-2-1c-1-1-2-3-2-5 0-1-1-1-1-2v-2c-1-1-1-2-2-3h0 0v3c0 1 1 4 2 5v2l-1-1-9-24c-1-2-2-3-3-4-1 0-1 0-1-1l-3-3h1v-6z" class="G"></path><path d="M543 429h2 0c1 2 1 5 2 7 0 2 1 3 2 4v3c-1-2-2-3-3-4-1 0-1 0-1-1l-3-3h1v-6z" class="D"></path><path d="M504 421c1 5 1 10 1 15 0 4 0 8-1 12 0 1 1 6 0 6h-1c-2-1-2-3-2-5-1-3-1-6-2-10-2-3-4-6-6-10h1c5 0 7-5 10-8z" class="I"></path><path d="M503 439h0c0 2 1 7 0 9-1-3 0-6 0-9z" class="b"></path><path d="M449 458l1-2 3 1c1-1 1 0 2-1v2h2 6l8 1c1 0 3 0 4 1s0 1 1 1h2l1 1c-1 0-4 1-5 1l-1 1s-1 1-2 1h0l2 2v2h0c1 1 1 2 1 3-1-1-2-3-3-3-2-1-5-1-6 0-2-1-2 0-3 0s-1-1-2-1l-1 2h-1v-2c-4 0-5 1-8 3 0 1-1 2-1 2l-1-1-1 1 1 2c-1 0-1 0-3 1v-1c0-1 1-3 2-5h2l-2-2c0-2 1-2 2-4h-3l1-4h1l1-2z" class="G"></path><path d="M471 469l-1-2h-12c1-1 3-1 4-1 2-1 4-2 7-2 1 0 1 0 2-1h3l-1 1s-1 1-2 1h0l2 2v2h0c1 1 1 2 1 3-1-1-2-3-3-3z" class="D"></path><path d="M449 458l1-2 3 1c1-1 1 0 2-1v2h2c3 0 6 1 9 3 1 0 3 0 4 1h0c-2 0-4 1-6 1l-2-1-13 2h-3l1-4h1l1-2z" class="l"></path><path d="M449 458l1-2 3 1c1-1 1 0 2-1v2h2c3 0 6 1 9 3h-8c-1-1 1-1-1-1-1 0-2-1-3-1-2-1-3-1-5-1z" class="d"></path><path d="M447 460h1c3 1 6 1 10 2 2 0 3-1 4 0l-13 2h-3l1-4z" class="O"></path><path d="M498 330l4-11c0-1 0-3 1-4l1-1h0l1-1 1 2c-2 3-2 4-3 7l2 8h0c-1-2-2-5-3-7l-13 53-2 11c0 1-1 3-1 5h-1c1-2 1-4 1-6 1-2 1-4 1-6 1-3 2-5 2-8 0-1 0-2 1-3v-1-2 1c-1 1-1 1-1 2-1 8-4 15-5 23l-1-1c0-4 1-11 3-14 1-3 1-6 2-9 0-1 1-2 1-3l-1-1-2 9c-2 2-3 3-3 5h-1 0-1c0-1 1-1 1-2s0-1-1-2c0-1 2-3 2-4l6-17 2-4 7-19z" class="f"></path><path d="M498 330v2h0v2l-3 9c-1 3-1 9-3 12v1h-1c-1-1-1-1-1-2s0 0-1-1l2-4 7-19z" class="l"></path><path d="M445 476c2-1 2-1 3-1l-1-2 1-1 1 1s1-1 1-2c3-2 4-3 8-3v2h1l1-2c1 0 1 1 2 1s1-1 3 0c1-1 4-1 6 0 1 0 2 2 3 3 0 1 0 1-1 3-1 1-2 1-3 2l-1-1h-1c-1 0-1-1-2-2 0-1 0-3-1-4h-5v1 1c1 1 0 2 0 3v1h0-2c-2 1-3 2-3 5 0 1 1 2 1 3 1 0 2 1 4 1 1 0 5-1 6 0-1 1-2 0-3 1-2 1-4 0-6 0v1 1c1 0 2 1 3 1h1 3l1 1c1 0 4-1 5 0-2 2-9 2-12 2 0 0 0-1-1-1h-1c-3-1-7-2-9-5h0l-3-6h-1v-1h2c1 1 1 1 1 2l1 1h0c0-3-1-2-3-5h0c1 0 2 1 3 0l-2-1z" class="B"></path><defs><linearGradient id="y" x1="525.958" y1="456.027" x2="546.427" y2="465.654" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#y)" d="M535 449c3 0 4 0 7 1 3 2 5 4 6 7s1 7-1 10c-2 4-5 5-8 6-3 0-5 0-8-1-3-2-5-5-6-9-1-3-1-6 1-8 2-4 6-5 9-6z"></path><path d="M532 454h1c-1 1-1 2-2 3h1c1-1 2-2 4-3 0 1 0 1 1 2h3 0l4 4c0 3 0 3-2 5-2 3-3 3-6 3h0c-1 1-1 1-2 1-2-1-3-2-4-3h0c-1-2-2-2-2-4v-1h0c0-1 1-3 1-4l1-1c0-1 1-1 2-2z" class="K"></path><path d="M504 421l3-3c2 3 6 7 8 10-2 3-3 6-2 9-3 6-5 12-6 19-1 4-1 8 0 13 3 9 7 18 16 23 12 9 27 7 41 7h9l-48 1h0l1-1h0c-1-1-1-1-1-2-1 0-2 0-2-1-2 0-4-1-5-2h-1l-3-3c-3-2-6-6-7-9l-3-9c0-1-1-3-1-4 0-2-1-4 0-5v-1l-1-1 3 1c1-3 1-5 1-8l-2-1c1 0 0-5 0-6 1-4 1-8 1-12 0-5 0-10-1-15z" class="b"></path><path d="M505 463c0 2 1 5 2 8 0 1 1 2 1 4 1 2 2 5 4 8h0c1 2 5 6 5 6l-1 1-5-5v-1c-1-1-1-2-2-3 0 2 2 4 3 6s3 3 4 4l2 1c2 2 4 3 7 5h0c-1 0-2 0-2-1-2 0-4-1-5-2h-1l-3-3c-3-2-6-6-7-9l-3-9c0-1-1-3-1-4 0-2-1-4 0-5v-1l-1-1 3 1z" class="X"></path><path d="M486 373l2-9 1 1c0 1-1 2-1 3-1 3-1 6-2 9-2 3-3 10-3 14l1 1c0 3-1 7-2 9h-2v2c1 1 0 2 0 3v1c0 3-1 4-3 6h0c0 2-1 4-2 6 0 1-1 3-1 5 0 1 1 2 1 4-2-1-5-2-8-2h-6c0-2 1-3 2-5 1-5 4-9 6-14l12-33c1 1 1 1 1 2s-1 1-1 2h1 0 1c0-2 1-3 3-5z" class="S"></path><path d="M477 402v6h-1c-1-1 0-2 0-3v-3h1z" class="Y"></path><path d="M468 420l2-2c1 1 0 2 1 3l1-1 2-7v7l3-7h0 0c0 2-1 4-2 6 0 1-1 3-1 5 0 1 1 2 1 4-2-1-5-2-8-2l1-6z" class="C"></path><path d="M463 421l2-2c0-1 0-1 1-2 2-2 4-6 6-8l3-8c0 3 0 5-1 7v1c-1 2-2 3-3 5l-3 6-1 6h-6c0-2 1-3 2-5z" class="Y"></path><path d="M481 374c1 1 1 1 1 2s-1 1-1 2h1 0 1c0-2 1-3 3-5-1 1-1 3-2 4 0 1 0 4-1 5l-3 13c0 2 0 4-1 6 0-5 1-8 2-12 0-3 1-6 1-8-1 1-1 1-1 2l-3 12c0 3 0 5-1 7h-1c1-3 1-6 2-9v-1h-1v1c-1 3-1 5-2 8h0c-1 3-2 5-3 8-2 2-4 6-6 8-1 1-1 1-1 2l-2 2c1-5 4-9 6-14l12-33z" class="L"></path><path d="M495 480c2-7 5-16 3-24h0v1c0 2 0 3 1 5h0 3l1 1v1c-1 1 0 3 0 5 0 1 1 3 1 4l3 9c1 3 4 7 7 9l3 3h1c1 1 3 2 5 2 0 1 1 1 2 1 0 1 0 1 1 2h0l-1 1h0-24-28v-1h-2l6-1c9-3 14-9 18-18z" class="F"></path><path d="M499 462h3l1 1v1c-1 1 0 3 0 5 0 1 1 3 1 4v1l-1-2v-1c-1-1-1-2-1-3v-1c-1 0-1 0-1 1h0-1l-1-6z" class="M"></path><path d="M500 483v-8-1c1-1 0-2 1-3 0 6 0 10 2 16 1 3 1 5 4 7l1 2 2 1h0l-6-3c-2-3-3-6-4-11z" class="S"></path><path d="M496 494l1-1-1-2c1-1 1-1 2-1-1-2-1-6-1-8h1l1 4v-3h1c1 5 2 8 4 11l6 3c1 1 2 1 3 2h0l-12 1h-28v-1h26 0c-1-1-2-3-2-5h-1z" class="I"></path><path d="M499 486v-3h1c1 5 2 8 4 11 0 1-1 2-1 2v1c-1 1-1 1-3 1v-3l1-1c0-2-2-6-2-8z" class="N"></path><defs><linearGradient id="z" x1="519.765" y1="485.103" x2="508.109" y2="490.093" xlink:href="#B"><stop offset="0" stop-color="#090b09"></stop><stop offset="1" stop-color="#353436"></stop></linearGradient></defs><path fill="url(#z)" d="M513 499h4 1l-2-1c-1 0-2-1-3-1v-1c-2 0-1 1-2 0 0-1-1-1-1-2h-1c0-1 0-1-1-1 0-1-1-1-1-2l-2-4c-1-1-1-2-1-3l-1-1v-2c-1-1-1-2-1-3-1-2 0-4 0-5l1-1 1 2v-1l3 9c1 3 4 7 7 9l3 3h1c1 1 3 2 5 2 0 1 1 1 2 1 0 1 0 1 1 2h0l-1 1h0-24l12-1z"></path><path d="M495 480c1 2 1 3 1 4l-1 1v3 2c0 1 1 3 1 4h1c0 2 1 4 2 5h0-26-2l6-1c9-3 14-9 18-18z" class="M"></path></svg>