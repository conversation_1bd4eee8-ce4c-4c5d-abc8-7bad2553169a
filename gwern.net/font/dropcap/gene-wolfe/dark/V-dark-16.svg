<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="128 95 770 844"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#c7c5c5}.C{fill:#010101}.D{fill:#fefefe}.E{fill:#f4f4f4}.F{fill:#b9b7b7}.G{fill:#b2b0b0}.H{fill:#828081}.I{fill:#e4e2e2}.J{fill:#949292}.K{fill:#a5a4a4}.L{fill:#5c5b5b}.M{fill:#747273}.N{fill:#8c8b8b}.O{fill:#f0efee}.P{fill:#383737}.Q{fill:#9d9b9c}.R{fill:#1b1b1b}.S{fill:#101010}.T{fill:#6b6a6a}.U{fill:#494849}</style><path d="M532 577c2 0 4 0 5 1 0 1-1 1-2 2h0l-3-3z" class="J"></path><path d="M537 578c1 1 1 4 1 6-1-1-3-2-3-4h0c1-1 2-1 2-2z" class="N"></path><path d="M282 371c1-1 2-1 4-1v4l-2 1c-1-2-2-3-2-4z" class="M"></path><path d="M489 342l5-1 1 1c0 2 0 4-1 5h0v-1c-1-1-1-2-2-2l-2 1c0-1 0-2-1-3z" class="I"></path><path d="M491 259c1 0 2 1 4 1-1 1-1 2-2 3l-4-1c0-1 0 0 1-1l1-1v-1z" class="B"></path><path d="M624 681h3l-1 1 1 1h0c0 1 1 1 1 2-2 0-4 0-5-1 0-1 0-2 1-3z" class="H"></path><path d="M532 280h5v6c-1 0-2-1-4-2 2-1 2-1 2-3h-3v-1z" class="N"></path><path d="M511 387h7l-1 1c1 1 1 1 1 2-2 1-5 0-7-1v-2z" class="R"></path><path d="M619 679c2 1 3 2 5 2-1 1-1 2-1 3-3 0-4-1-6-3l2-2z" class="T"></path><path d="M520 238h8l1 2-10 1 1-3z" class="J"></path><path d="M515 400l3-1c0 2 0 3 1 5h-8c1-1 2-1 3-1l1-3z" class="R"></path><path d="M533 380h4c1 1 1 4 0 5v1h-1c-2-2-2-4-3-6z" class="J"></path><path d="M489 262c-1 0-2 0-3-1v-4c1-1 1-1 3-1l-1 1c1 1 2 1 3 2v1l-1 1c-1 1-1 0-1 1z" class="G"></path><path d="M529 287c0-1 0-1-1-2 0-1 0 0 1-1l1-1 1 2c1 0 1-1 2-1 2 1 3 2 4 2-1 1-5 1-7 1h-1z" class="M"></path><path d="M489 256c2-1 3-1 5 0l1 1h0v3c-2 0-3-1-4-1-1-1-2-1-3-2l1-1z" class="E"></path><path d="M314 457l1 1c1 0 2 1 3 2 2 1 6 0 7 2v1c-3 0-5 0-7-1s-3-3-4-5z" class="F"></path><path d="M568 283h3c2 0 4 0 6-1v2c-5 1-9 2-14 1 2-1 3-1 5-2z" class="L"></path><path d="M534 237h3c1 1 1 1 1 2-2 2-7 1-9 1l-1-2c2 0 4 0 6-1z" class="M"></path><path d="M651 581h2c1 0 3 0 4 1-2 0-5 1-7 2l-4 1h-1l1-1c1-2 3-2 5-3z" class="Q"></path><path d="M488 426l1-2c0-1 0-1 1-2h4v3 1h-6z" class="B"></path><path d="M501 236c1-1 1-3 1-4l1-9 2 3c-1 3-1 8-2 10 0 0-1 1-2 0zm116 411l5 2c2 1 3 2 4 4h0c0 3 0 4-1 6l-1 1v-1-4c-1-5-3-6-7-8z" class="J"></path><path d="M373 291v-1-4h0c1 0 3 2 3 3 1 1 1 6 1 7l-1 1-3-6z" class="E"></path><path d="M523 368h4c0 2-1 4 0 6l-8 1c1-2 3-2 5-2 1-1 0-3 0-4l-1-1z" class="K"></path><path d="M473 375c1-3 2-5 2-8l1-1c1 2 1 4 0 6 0 2 0 4-1 6h-2c1-1 0-2 0-3z" class="I"></path><path d="M532 518h2c1-1 2 0 3 0h0v2h-6c-3 0-6 1-9 0h4v-2h6z" class="H"></path><path d="M489 507h0c-2 0-2-1-3-2 0-2 0-2 1-4 2-1 4-1 6-1l-3 3v2l-1 2z" class="G"></path><path d="M592 724c4 1 3 7 5 8 2 0 3 0 5-1v1l-2 2c-3 0-4 0-6-1-2-2-1-6-2-9z" class="Q"></path><path d="M493 500l2 1h-1v2c1 1 1 2 1 3l-1 1c-2 0-3 1-5 0l1-2v-2l3-3z" class="I"></path><path d="M503 223l2-16c1 2 0 3 1 5s0 11-1 14h0l-2-3z" class="F"></path><path d="M527 460l3 1c3 0 5 0 7 1v1c-2 1-8 1-11 1l1-4z" class="H"></path><path d="M491 324c-2 0-4-1-5-3-1-1-1-2 0-4 1 0 3 0 4-1v3 2 1 1c1 1 1 0 1 1z" class="K"></path><path d="M523 590h7c1 2 1 4 0 6v1h-7l3-1c1-2 1-3 1-6h-3-1z" class="H"></path><path d="M645 647c-2-3-5-5-8-7 0 0 0 1-1 0h2c3 0 6 0 8 2 0 2 1 2 1 4-1 0-1 0-2 1z" class="J"></path><path d="M490 316h4c1 2 0 5 0 7-1 1-2 1-3 1 0-1 0 0-1-1v-1-1-2-3z" class="E"></path><path d="M598 424c-1 4-4 7-8 10l-2 1-3-3c6-2 9-5 13-8z" class="N"></path><path d="M617 681c-3-2-5-4-6-8 0-1-1-3 0-4 1 1 1 1 1 2 2 3 4 6 7 8h0l-2 2z" class="U"></path><path d="M511 275l1 1h6v1c0 1 0 2 1 3-3-1-6 0-8 0h-2c1-1 1-3 2-4v-1z" class="R"></path><path d="M512 276h6v1 1h-2c-2 0-3-1-4-2z" class="P"></path><path d="M519 521l-1 4h-7-1c0-1 1-3 1-4h8zm-7-184h6v4l-8 1v-1c0-1 1-2 1-4h1z" class="S"></path><path d="M511 452h1 6c0 1 0 3 1 4h-1-6-2c1-1 1-3 0-4h1z" class="R"></path><path d="M519 367l4 1 1 1c0 1 1 3 0 4-2 0-4 0-5 2h-1c0-2 0-5 1-8z" class="B"></path><path d="M474 538h1c0-1 0-1 1-1 1-2 3-3 4-4 0 5 0 9 1 14l-2-1c0-1-1-2-1-3v-1c0-1-1-2 0-2v-1c-1-1-1-1-1-2 0 0-1 1-1 2l-1 1c-1-1-1-1-1-2z" class="P"></path><path d="M467 313v-1c0-7 3-11 7-15-1 4-2 8-2 13v-1c-1-1-1-2-1-3h-1c-1 1-1 2-2 3 0 1 0 3-1 4z" class="O"></path><path d="M537 244l1 1v5l-14 1c1-1 1-1 2-1 1-1 1-1 2-1 1-1 0-1 1-2l1 1h3c2-1 2-2 4-3v-1z" class="L"></path><path d="M410 694l1-1c4-3 4-6 5-11 1 2 2 4 2 6-1 2-1 4-3 6h0c-1 1-2 1-3 1s-1-1-2-1z" class="H"></path><path d="M652 626h1c2-2 4-5 4-8v-1h1c1 1 2 2 1 4 0 4-2 6-4 8l-1 1-2-4z" class="G"></path><path d="M642 625c3 2 4 3 7 3l3-2 2 4h-2l-3 1c-4 0-6-1-9-4l2-2z" class="M"></path><path d="M652 626l2 4h-2-3c-1 0-2-1-2-2h2l3-2zm-84-330l2 1c1-1 2-2 4-2l-6 7h0c-2 0-2 1-3 1-1 1-2 1-2 1l-1 1h-1l1-3 6-6z" class="Q"></path><path d="M511 440c2 0 6 0 9-1 0 2-1 3 1 5h-2-9c1-2 0-3 0-4l1-1v1z" class="S"></path><path d="M529 317h9c0 2 0 5-1 7h-5v-4c0-1-1-2-3-3z" class="N"></path><path d="M534 319h1l1 1-1 2-2-1v-1l1-1z" class="J"></path><path d="M528 577h4l3 3c0 2 2 3 3 4-2 1-7 0-9 0v-3l2-2v-1l-3-1z" class="M"></path><path d="M520 250l1 1c-1 1-2 1-1 3v1h-2-4-3c-1-1-1-3-1-4 3 0 7 0 9-1h1z" class="S"></path><path d="M505 262c-3 0-7 1-9-1v-1h1l-2-1c1-1 1-1 1-2 1-2 5-1 7-1 0 1 0 1 1 2h-2c-1 0-1 0-2 1 0 1 0 1 2 2 1 1 2 1 3 1h0z" class="K"></path><path d="M525 316l4 1c2 1 3 2 3 3v4h-6v-6l1-1-2-1z" class="H"></path><path d="M512 547c3 0 5 0 7-1l1 1c-1 1-2 2-2 3v1l-7 1v-1l-1-4h-1 3z" class="S"></path><path d="M512 547c3 0 5 0 7-1l1 1c-1 1-2 2-2 3h-3c-1-2-2-3-5-3h-1 3z" class="P"></path><path d="M282 192c2-1 3-1 5 1l2 2h1l2 4-3 3-7-10z" class="I"></path><path d="M518 508h3c-1 1-2 2-2 3l1 1-1 1-8-1c0-1-1-3-1-4h4 4z" class="S"></path><path d="M488 426l-1-1-1-1v-5c3-2 5-1 8-1 1 1 1 2 0 4h-4c-1 1-1 1-1 2l-1 2z" class="E"></path><path d="M519 280h4l2 1c0 3-1 4-2 6l-5 1 1-8z" class="F"></path><path d="M469 836h0l3-3c1-2 2-4 2-6h1c1 2 1 4 0 6-1 3-3 6-6 7-1 1-2 1-3 1l1-1c1-1 2-2 2-3v-1z" class="L"></path><path d="M527 457h10c1 1 1 3 0 5-2-1-4-1-7-1l-3-1-1-1h0l1-2z" class="J"></path><path d="M466 841c-2 1-5 1-7 0-4-1-6-4-8-7 2 1 3 3 5 4 5 1 9 1 12-1l1-1v1c0 1-1 2-2 3l-1 1z" class="I"></path><path d="M494 347c0 2 0 2-1 3-2-1-4-1-6-1 0-2-1-4-1-6 1-1 2-1 3-1 1 1 1 2 1 3l2-1c1 0 1 1 2 2v1h0z" class="B"></path><path d="M490 345l2-1c1 0 1 1 2 2v1h-5l1-2z" class="E"></path><path d="M645 647c1-1 1-1 2-1l1 2c0 3 1 5 1 8s-1 6-1 9c-1 1-2 2-4 3 3-7 4-15 1-21z" class="K"></path><path d="M530 292h8l-1 7h-7v-7z" class="J"></path><path d="M510 171l1-13 2 12-1 8h0l-1 1-1 4-1-5 1-7z" class="B"></path><path d="M510 171c1 1 1 1 1 3 1 1 1 2 1 4h0l-1 1-1 4-1-5 1-7z" class="I"></path><path d="M520 392h6c0 2 0 4-1 6l-2 1h-4c0-1-1-5 0-5 0-1 0-1 1-2zm-1 146h6l1 1v1c0 2-1 4 0 5l-1 1-5 1-1-1c0-2 1-5 0-8z" class="F"></path><path d="M529 380h4c1 2 1 4 3 6h1c-3 1-8 0-12 1 2-2 2-2 2-4 1-1 1-2 2-3z" class="H"></path><path d="M530 444h8v6c-1 1-2 1-3 1h-1-5c1-2 1-4 1-7z" class="N"></path><path d="M534 451c-1-1-1-2-1-3 1-1 1-1 2-1 1 1 1 1 1 2s-1 1-1 2h-1z" class="K"></path><path d="M519 469h7 1l-1 7-7 1v-4c1-1 1-2 0-4z" class="B"></path><path d="M526 469h1l-1 7-7 1v-4c3 0 4 1 7 0v-4z" class="G"></path><path d="M479 546l2 1c1 6 3 13 2 19-1-1-1-5-3-6v4 1l-1-6 1-1v-4c-1-1-1 0-1-1 1-2 0-5 0-7z" class="U"></path><path d="M644 668c2-1 3-2 4-3-1 3-2 6-4 8-1 2-3 3-3 5h1c0 1-1 2-2 2-1 1-3 2-4 1v-3c4-2 6-6 8-10z" class="F"></path><path d="M523 280h9v1h3c0 2 0 2-2 3-1 0-1 1-2 1l-1-2-1 1c-1 1-1 0-1 1 1 1 1 1 1 2h-3-3c1-2 2-3 2-6l-2-1z" class="J"></path><path d="M526 287c0-1 0-2 1-4h3l-1 1c-1 1-1 0-1 1 1 1 1 1 1 2h-3z" class="H"></path><path d="M580 283c1-1 2-2 4-2l-10 14c-2 0-3 1-4 2l-2-1 12-13zm-60 306l3 1h1 3c0 3 0 4-1 6l-3 1h-4c-1-2 1-5 1-8z" class="G"></path><path d="M532 590h7l-1 7h-7-1v-1c1-2 1-4 0-6h2z" class="M"></path><path d="M530 590h2l1 2v1 2 1s-1 0-2 1h-1v-1c1-2 1-4 0-6z" class="N"></path><path d="M584 274c2-2 5-5 5-8 1-2 1-4 2-5 0 0 0 5-1 6v1 2c-2 4-3 8-6 11-2 0-3 1-4 2l1-3c1-2 3-4 3-6z" class="J"></path><path d="M518 500l8 1c0 1-1 5 0 6l-5 1h-3 0c1-3 1-5 0-8z" class="F"></path><path d="M511 280c2 0 5-1 8 0l-1 8h-5l-2-8z" class="D"></path><path d="M487 244h8c0 2 0 4-1 6-1 1-3 0-5 0s-2-1-3-2c0-2 0-2 1-4z" class="F"></path><path d="M527 513c2 0 9-1 11 1-1 1 0 3-1 4h0c-1 0-2-1-3 0h-2-6l1-3-1-1 1-1z" class="J"></path><path d="M527 515c1 0 2 1 3 2l2 1h-6l1-3z" class="N"></path><path d="M519 456l8 1-1 2h0l1 1-1 4h-7v-8z" class="F"></path><path d="M526 459c0 2 0 1-1 2-1 0-1 0-2-1l1-1h2 0z" class="B"></path><path d="M519 564h6l2 1c0 1-1 1-1 2v5c-3 0-5 0-7 1v-3-6z" class="G"></path><path d="M518 342h7 2l-2 7c-2 0-4 0-7 1l1-3v-1c0-2 0-3-1-4z" class="B"></path><path d="M525 342h2l-2 7c-2 0-4 0-7 1l1-3h5c1-2 1-3 1-5z" class="G"></path><path d="M487 292h7c0 2 1 5 0 7h-3c-2 0-3-1-5-2 0-2 0-3 1-5z" class="E"></path><path d="M519 304h7v7h-4c-1 1-2 1-3 1l-1-1 1-7z" class="F"></path><path d="M487 444h7v4 3h-1c-2 1-5 0-6-1-1-2-1-3-1-5l1-1z" class="E"></path><path d="M491 380c1 0 2-1 4 0 0 2 0 5-1 7-1 0-3 0-5-1-1 0-2-1-3-1v-1h1v-1l-1 1v-1-2c1-1 4-1 5-1zm-2-100c2 0 4 0 6 1 0 2 0 4-1 6h-2c-2 0-3 0-5-1-1-1-1-2-1-4 1-1 2-1 3-2z" class="I"></path><path d="M510 367h9c-1 3-1 6-1 8h-1-5l-2-8z" class="D"></path><path d="M495 395c0 1 0 2-1 3 0 1 0 1-1 1-2 0-5 0-6-1s-1-2-1-3 0-2 1-3h7c0 1 0 2 1 3z" class="O"></path><path d="M520 379l9 1c-1 1-1 2-2 3 0 2 0 2-2 4h-6l1-8z" class="G"></path><path d="M519 489h3l4 1c1 2 0 4 0 5-5 1-11 2-16 1 3 0 6 0 8-1 1-1 1-4 1-6z" class="F"></path><path d="M519 525l7 1v7h-1l-6 1h-1l1-4v-5z" class="B"></path><path d="M519 534c0-3 2-4 3-5h1l2 4-6 1z" class="G"></path><path d="M519 316h6l2 1-1 1v6l-8 1c1-3 1-6 1-9z" class="F"></path><path d="M643 604l1 1h-1c-1 1-1 2-2 3v2c-1 1-1 1 0 2-2 3-2 7-1 10l2 3-2 2c-1-1-2-3-2-5-2-4-1-10 1-14h0c2-1 3-3 4-4z" class="L"></path><path d="M491 513h3l1 1c0 2 0 5-1 6h-3c-2 0-3 0-5-2v-4c1-1 3-1 5-1z" class="E"></path><path d="M519 329l8 1h0c-1 2-1 4-1 7h-8l1-8z" class="F"></path><path d="M487 304h8c0 2 0 5-1 7h-1c-2 0-4 1-6-1-1 0-1-2-1-3 0-2 0-2 1-3z" class="O"></path><path d="M487 368c2-1 6-1 8 0v1 2h0c-1 1-1 3-1 4h-5c-2-1-2-1-3-2 0-2 0-4 1-5z" class="E"></path><path d="M487 620c0-2 0-4-1-6 2 0 6-1 8 1 0 1 0 3 1 5h-1c-2 2-4 1-6 2 1 1 1 1 1 2h-1l-1-1c-1-1-1-1 0-3z" class="F"></path><path d="M494 615c0 1 0 3 1 5h-1-1c-1-1-3-2-4-3l1-1 1-1h3z" class="I"></path><path d="M511 392c3-1 6-1 9 0-1 1-1 1-1 2-1 0 0 4 0 5h-1l-3 1-2-1h-1 0v-1-1l-2-1v-1c1-1 1-2 1-3z" class="D"></path><path d="M491 404c1 0 2 0 4 1 0 2 0 5-2 7h-3l-4-2c0-2-1-4 0-5l5-1z" class="E"></path><path d="M493 525c1 0 1 0 1 1 1 2 1 5 0 7-2 0-4 0-7-1-1 0-1-1-1-1 0-2-1-3 0-4 2-2 5-2 7-2z" class="O"></path><path d="M491 538c1 0 2 0 3 1h0c1 2 1 6 0 7h-5 0c-1 0-2-1-2-2l1-1h-1c-1-1-1-2-1-3 2-2 3-2 5-2z" class="D"></path><path d="M495 462c-1 2 0 1-1 2-2 0-4 0-6-1-1 0-2 0-2-1-1-2-1-3 0-4 2-2 4-1 7-1h1l1 1h0c-1 1 0 3 0 4z" class="F"></path><path d="M493 457h1l1 1h0c-1 1 0 3 0 4-2 0-2 0-3-1-2-1-3-2-4-3v-1h5z" class="O"></path><path d="M510 500h8c1 3 1 5 0 8h0-4c-1-2-3-2-5-3 1-1 1-3 1-4v-1z" class="D"></path><path d="M594 390h4c2 2 3 4 4 7h1v-2c1 5-1 11-4 16 1-9 1-14-5-21z" class="M"></path><path d="M487 432c3-1 5 0 7 0l1 1v2c0 2 0 3-1 4-3 0-6 1-8-1v-5l1-1z" class="F"></path><path d="M487 432c3-1 5 0 7 0l1 1v2h-2v1c1 0 0 0 1 1l-1 1v-1h-2c-1 0-1-1-1-2-2-1-2-1-3-3z" class="E"></path><path d="M488 552c2-1 4 0 7 0v7h-2-7v-5c0-1 1-1 2-2z" class="B"></path><path d="M510 329c3 0 6-1 9 0l-1 8h0-6l-1-3c-1-2-1-3-1-5z" class="D"></path><path d="M603 687h2c2 0 4 1 6 3 3 4 4 8 3 13 0 4-2 8-5 10-2 2-6 2-9 2l-1-1h0 3c3 0 7-3 9-6s2-6 2-10v-1c-1-1-1-3-2-4-2-4-4-5-8-6z" class="I"></path><path d="M512 456h6 1v8h-6 0-1v-2l-1-1-1-1c0-1-1-2 0-4h2z" class="D"></path><path d="M510 456h2c0 3 0 3 2 6h4v-6h1v8h-6 0-1v-2l-1-1-1-1c0-1-1-2 0-4z" class="E"></path><path d="M511 255h3l-3 1v2c0 1 0 1-1 2l1 1c1 0 1 1 1 2-2 0-5 0-7-1h0c-1 0-2 0-3-1-2-1-2-1-2-2 1-1 1-1 2-1h2c-1-1-1-1-1-2l8-1z" class="F"></path><path d="M486 577c2-1 5 0 8 0 1 2 1 4 0 6v2c-2 0-6 0-8-1v-7z" class="B"></path><path d="M562 302l-1 3h1l1-1s1 0 2-1c1 0 1-1 3-1h0l-2 5h-1l-1-1v1h-1c0-1 0-1-1-1-3 2-5 4-8 7-2 1-4 2-5 4l-1 1c-2 2-5 2-7 2 8-5 15-11 21-18z" class="J"></path><path d="M663 560h1l-3 6c3 1 5 3 7 5 1 3 2 5 1 8 0 2-1 3-3 4s-3 0-5 0v-1h1c2-1 3-2 5-4l-1-1c-1 1-1 2-3 2 1-1 2-3 3-4 0-2 0-4-1-5-1-2-3-3-4-3h-1v-1c1-1 0-2 0-2 1-2 2-3 3-4z" class="O"></path><path d="M512 291l7 1v8h-6l-3-3c0-2-1-3 0-5l2-1z" class="D"></path><path d="M527 368h11l-1 6h-10c-1-2 0-4 0-6z" class="H"></path><path d="M509 469c3 0 7-1 10 0 1 2 1 3 0 4v4h-7c-1-3-3-5-3-8zm9-128v1c1 1 1 2 1 4v1l-1 3c-2 0-5 0-8-1 2-4 0-3-1-7h1l8-1z" class="D"></path><path d="M527 330h10c1 1 0 4 0 5-1 1-4 1-6 1h-1c-1 1-2 0-4 1 0-3 0-5 1-7h0z" class="N"></path><defs><linearGradient id="A" x1="532.512" y1="494.869" x2="530.815" y2="489.036" xlink:href="#B"><stop offset="0" stop-color="#828182"></stop><stop offset="1" stop-color="#9c9a9b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M522 489c3 0 14-1 16 1 0 2 0 3-1 5h-11c0-1 1-3 0-5l-4-1z"></path><path d="M486 238c2 0 4-1 5 0h5 13c4 1 7 1 11 0l-1 3-14-1h-8-3c-2 0-7 1-8 0v-2z" class="E"></path><path d="M509 589h11c0 3-2 6-1 8-2 1-5 0-7 0-1 0-1 0-1-1 0-2-1-4-2-7z" class="D"></path><defs><linearGradient id="C" x1="531.821" y1="410.933" x2="534.972" y2="404.958" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#9a9898"></stop></linearGradient></defs><path fill="url(#C)" d="M528 404l10 1-1 7c-2 0-6 0-8-1l-2 1v-1c1-2 1-5 1-7z"></path><path d="M527 469h11c0 2 0 5-1 7h-11l1-7z" class="H"></path><path d="M527 342h10v7c-2 0-5 0-6-1h-1c-1 1-3 1-5 1l2-7z" class="J"></path><path d="M511 525h7 1v5l-1 4h-5l-2-2-1-2v-4l1-1z" class="D"></path><path d="M526 256h5 6v6h-7l-4 1v-7z" class="M"></path><path d="M526 256h5c0 2 1 3 2 5l-1 1c-1-1-1-1-1-2h-1v1 1l-4 1v-7zm0 245h12c0 1 0 5-1 6h-11c-1-1 0-5 0-6zm-13-331c1 7 1 14 2 21 0 3 1 7 1 10 0 1 0 3-1 5 0 0 0-1-1-1h-1l-1-27h0l1-8z" class="H"></path><path d="M525 552h13c0 2 0 5-1 7-4 0-9 0-13 1 1-1 1-1 2-1 1-2 1-4 0-6l-1-1z" class="N"></path><path d="M510 418h10c-1 2-2 7-1 9h-7c-2-3-2-6-2-9zm1-102h8c0 3 0 6-1 9-2 0-5 0-7-1v-1h0l-1-1h0c-1-2-1-3-1-5l2-1z" class="D"></path><path d="M456 353l1 2c0 1 1 1 1 2 4 7 12 6 19 4 2 0 4-1 6-1l-1 1c-4 3-8 5-13 5-4 0-7-1-9-4-3-2-4-5-4-9z" class="O"></path><path d="M526 392h12c0 2 0 4-1 6-2 1-8 1-10 1h-4l2-1c1-2 1-4 1-6z" class="N"></path><path d="M527 399c0-2 1-3 2-3h1l1 2h2c0-1-1-2-1-2l1-1c2 2 2 3 4 3-2 1-8 1-10 1zm-209 63c-2 0-3 0-5-2-4-3-7-9-8-14 0-7 3-12 7-16-2 3-3 6-4 9-1 8 2 12 6 18 1 2 2 4 4 5zm202-218h7 10v1c-2 1-2 2-4 3h-3l-1-1c-1 1 0 1-1 2-1 0-1 0-2 1-1 0-1 0-2 1h-3l-1-1h-1c1-1 1-4 1-6h0z" class="M"></path><path d="M520 244h7l1 1c-2 2-2 3-4 4-1 0-3 1-4 1h-1c1-1 1-4 1-6h0z" class="G"></path><path d="M525 564c2 0 10 0 12 1 1 2 1 4 0 6-1 1-2 1-4 1h-7v-5c0-1 1-1 1-2l-2-1z" class="M"></path><path d="M533 572l-2-4s-1-1-1-2h0v-1h7c1 2 1 4 0 6-1 1-2 1-4 1z" class="N"></path><path d="M639 608c1-4 2-7 4-10 3-4 7-7 12-8 1 3 1 4-1 6-2 1-3 1-5 2-3 1-4 2-7 5l1 1h0c-1 1-2 3-4 4h0zM523 432h15v7h-12v-6c-1-1-2-1-3-1z" class="H"></path><path d="M526 304h12l-1 7h-15 4v-7z" class="J"></path><path d="M519 292h9 2v7h-3c-3 0-6 0-8 1v-8z" class="F"></path><path d="M528 292h2v7h-3c0-2 0-5 1-7z" class="M"></path><defs><linearGradient id="D" x1="664.301" y1="650.607" x2="663.635" y2="668.062" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#D)" d="M662 665c0-6 0-10-2-16h1c4 2 5 7 6 11 1 2 1 5 1 7-2 0-4 2-6 1v-2-1z"></path><path d="M525 538l13 1-1 6c-1 1-1 1-2 1h-10l1-1c-1-1 0-3 0-5v-1l-1-1z" class="N"></path><path d="M526 545h0c1-2 1-3 1-5 1 0 1 0 2-1 1 2 0 6 2 6 1 1 3 0 4 1h-10l1-1z" class="H"></path><path d="M516 201l3 24c1 4 2 8 2 11l-1 1h-2c-1-2-1-4-1-6l-1-12c0-5-1-9-1-13 1-2 1-4 1-5z" class="T"></path><path d="M519 225c1 4 2 8 2 11l-1 1c-1-1-2-2-2-4s0-6 1-8z" class="L"></path><path d="M526 526c3 0 9-1 12 0v7h-12v-7z" class="H"></path><path d="M519 577h4 5l3 1v1l-2 2v3l-3 1h-8c-1 1-2 1-4 0h5v-8z" class="K"></path><path d="M523 577h5l3 1v1l-2 2v3l-3 1 1-7-4-1z" class="H"></path><path d="M470 528l4 10c0 1 0 1 1 2l1-1c0-1 1-2 1-2 0 1 0 1 1 2v1c-1 0 0 1 0 2v1c0 1 1 2 1 3 0 2 1 5 0 7 0 1 0 0 1 1v4l-1 1-2-6-1-4-2-7c-1-2-1-3-2-4-1 0-2-4-2-5v-5z" class="L"></path><path d="M512 477c-4-1-12 0-16-2-1-2 0-2 0-5 2-1 10-1 13-1 0 3 2 5 3 8zm-2 31c-3-1-7-1-10-1-2 0-4 0-5-1 1 0 2 0 2-1 0 0-1 0-1-1l1-1v-1-1c4-1 9 0 13-1v1c0 1 0 3-1 4 2 1 4 1 5 3h-4z" class="G"></path><path d="M519 267l3 1c-1 3 0 5-3 7l-1 1h0-6l-1-1c0-1-2-2-2-4 0-1-1-2-1-3l11-1z" class="D"></path><path d="M519 267l3 1c-1 3 0 5-3 7l-1 1c1-3 2-6 1-9z" class="B"></path><path d="M506 444h4c0 1-1 4 0 5l1 3h-1l-14-1v-1h1 0c-1-1-1-2-1-3l1-1v-1c1-2 7-1 9-1z" class="F"></path><path d="M497 450h0c-1-1-1-2-1-3l1-1v-1c1-2 7-1 9-1 0 0-1 1-1 2s2 1 1 2c-2 0-2-1-3-1l-2 2 1 1c-2 1-4 0-5 0z" class="G"></path><path d="M511 276c-3-1-14 0-15-2v-2-1l1-1-1-1v-1h12c0 1 1 2 1 3 0 2 2 3 2 4v1z" class="Q"></path><path d="M513 464l-14-1v1l-3-3v-4h3l11-1c-1 2 0 3 0 4l1 1 1 1v2h1 0z" class="B"></path><path d="M510 341v1h-1c1 4 3 3 1 7-3 0-11 0-14-1v-2l-1-1c1-1 1-2 2-3l13-1z" class="G"></path><path d="M494 392h1v2l1-1c0-1 13-1 15-1 0 1 0 2-1 3v1l2 1v1 1h0 1c-2 0-14-1-16-1-1-1-1-2-2-3h0c-1-1-1-2-1-3z" class="B"></path><path d="M468 309c1-1 1-2 2-3h1c0 1 0 2 1 3v1c1 0 0 1 1 2 0 8 5 10 8 17l2 4h0c-3-4-4-7-9-9h0c-2-1-3-1-4-2-2-3-3-6-3-9 1-1 1-3 1-4z" class="L"></path><path d="M468 309c1 2 0 4 1 5 1 3 5 7 5 10-2-1-3-1-4-2-2-3-3-6-3-9 1-1 1-3 1-4z" class="N"></path><path d="M512 597l-17-1 1-1-1-1c0-1 1-3 1-4 4-1 9 0 13-1 1 3 2 5 2 7 0 1 0 1 1 1z" class="K"></path><path d="M509 280h2l2 8-10-1c-1 0-3 0-5-1l1-1h-2c-1-1-1-1-1-2v-2l13-1z" class="B"></path><path d="M513 300l-12-1c-2 0-4 0-5-1v-3h0v-1-1s0-1 1-1h3l12-1-2 1c-1 2 0 3 0 5l3 3z" class="G"></path><path d="M499 324l-3-1v-6c1-1 3 0 4 0l11-1-2 1c0 2 0 3 1 5h0l1 1h0v1h-12z" class="K"></path><path d="M499 324l3-1c1-1 1-1 1-2 1-1 1-1 1-3h1c1 2 1 3 2 4h3l1 1h0v1h-12z" class="Q"></path><path d="M511 337c-4 0-12 0-15-2h2c-1-1-1-1-2-1-1-1 0-2-1-3l1-1 14-1c0 2 0 3 1 5l1 3h-1z" class="G"></path><path d="M517 375c-4 1-7 0-10 0-4-1-7 0-10-1v-1h0l-1-1v-4c5-1 10 0 14-1l2 8h5z" class="B"></path><path d="M481 596c2-2 1-3 1-5 1-1 2-1 2-2v-1l1 2c1 0 1 0 2-1 1 1 1 1 1 2l2 1c1-1 1-3 3-2l1 1h-2l1 1 1 1c0 1 1 2 0 3s-1 1-3 1h0c-2 0-4 0-4-1-1-1-1-2-2-2s-1 1-2 1c0 3-1 11 0 14 0 1 0 1 1 2v3c-2-2-3-6-4-9 0-3 0-6 1-9z" class="R"></path><path d="M510 525h1l-1 1v4l1 2 2 2-11-1c-2 0-4 0-6-1v-2-4c3-1 10 0 13-1h1zM393 644c1-1 2-1 4-1-7 9-12 19-10 31 1 7 4 14 10 19h0-1c-1-1-1-1-2-1h-1c-3-4-5-8-8-13-2-8 0-20 4-28 1-2 3-4 4-7h0z" class="K"></path><path d="M502 432l9-1c0 3 0 6 1 8l-1 1v-1l-1 1c-4-1-9 0-13-2-1-2-1-4-1-6h6z" class="F"></path><path d="M502 432l9-1c0 3 0 6 1 8l-1 1v-1l-2-2-3-3-1 1-3-3z" class="B"></path><path d="M512 427c-3 0-14-1-16-2l1-1c-1-2-1-4-1-5 1-1 3-1 4-1h10c0 3 0 6 2 9z" class="F"></path><path d="M502 419h1l1 1c0 1-1 1 0 2 0 1 0 1-1 2-1 0-1-1-2-2v-2l1-1z" class="B"></path><defs><linearGradient id="E" x1="466.053" y1="512.498" x2="458.746" y2="510.96" xlink:href="#B"><stop offset="0" stop-color="#6c6d6b"></stop><stop offset="1" stop-color="#8a8689"></stop></linearGradient></defs><path fill="url(#E)" d="M460 501c4 8 7 18 10 27v5l-4-9c0-2 0-2-2-4v1h-1l-3-9h-1c-1-1-2-4-2-6l2-4 1-1z"></path><path d="M459 502c1 6 3 12 5 18v1h-1l-3-9h-1c-1-1-2-4-2-6l2-4z" class="F"></path><path d="M518 255h2l6 1v7h-8c-2 1-4 1-6 0 0-1 0-2-1-2l-1-1c1-1 1-1 1-2v-2l3-1h4z" class="K"></path><path d="M514 255h4v7c-1 1-4 1-6 1 0-1 0-2-1-2l-1-1c1-1 1-1 1-2v-2l3-1zM363 642h0l1-1c2 0 4 0 5-1 8-4 11-10 13-18 0 4 0 7-1 10l-1 1c-2 1-3 4-4 6-5 5-12 7-19 7h-3c-3-1-6-3-8-4 3-2 6 2 10 1 1-1 2-1 3-1h4z" class="D"></path><path d="M511 431h8l4 1c1 0 2 0 3 1v6h-6c-3 1-7 1-9 1l1-1c-1-2-1-5-1-8z" class="F"></path><path d="M511 431h8l-1 8h-6c-1-2-1-5-1-8z" class="D"></path><defs><linearGradient id="F" x1="466.404" y1="443.372" x2="454.231" y2="460.723" xlink:href="#B"><stop offset="0" stop-color="#686667"></stop><stop offset="1" stop-color="#868484"></stop></linearGradient></defs><path fill="url(#F)" d="M473 447h4 0c-3 5-8 10-15 11-5 1-12 1-16-2-2-1-4-3-5-5 5 2 9 5 14 5 8 0 13-4 18-9z"></path><path d="M522 268h3 13l-1 7h-13-5c3-2 2-4 3-7z" class="H"></path><path d="M522 268h3 3c0 1-1 1-1 2l-2 4-1 1h-5c3-2 2-4 3-7z" class="G"></path><defs><linearGradient id="G" x1="292.667" y1="419.246" x2="281.13" y2="407.399" xlink:href="#B"><stop offset="0" stop-color="#4a4949"></stop><stop offset="1" stop-color="#737373"></stop></linearGradient></defs><path fill="url(#G)" d="M277 401c3 6 6 11 12 13 5 2 9 2 13 2-1 1-3 3-5 4v-1c-2 0-3 1-5 0-4 1-7-1-10-2 0-1 0-2-1-2-1-2-4-6-3-9h0l-1-1v-4z"></path><path d="M292 419h0v-1c2 0 6-1 7-1l-2 2c-2 0-3 1-5 0z" class="P"></path><defs><linearGradient id="H" x1="659.004" y1="667.774" x2="660.501" y2="682.277" xlink:href="#B"><stop offset="0" stop-color="#bebcbc"></stop><stop offset="1" stop-color="#f2f1f1"></stop></linearGradient></defs><path fill="url(#H)" d="M652 683c6-5 8-11 10-18v1 2c2 1 4-1 6-1-1 5-2 13-6 17h-1v-1l1-1c-3 0-5 0-8 1h-2z"></path><path d="M744 348c4-3 10-5 15-6l-1 1v1c0 1-1 4-1 5-5 2-9 4-13 7h-1l-2 2v1c0 1-1 2-2 2h0c1-4 2-9 5-13z" class="Q"></path><path d="M739 361c1-4 2-9 5-13v1h7c-1 1-4 3-5 4-2 1-2 2-3 3l-2 2v1c0 1-1 2-2 2h0z" class="H"></path><path d="M510 496c-2 0-6 0-8-2-2 0-3 0-5-1 0-2 0-2 1-4h21c0 2 0 5-1 6-2 1-5 1-8 1z" class="D"></path><path d="M563 307h1v-1l1 1h1c-3 8-7 17-8 25-1 3 0 6 0 10 1 5 0 10-2 14-2 3-5 6-8 7h-4l2-1c1 0 2 0 2-1 12-6 7-24 9-34 1-7 4-14 6-20z" class="N"></path><path d="M520 354l2 1h16l-1 7h-7c-1-1-2 0-4 0l-7 1c0-3 0-6 1-9z" class="J"></path><path d="M520 354l2 1h4v1c-1 2-1 4 0 6l-7 1c0-3 0-6 1-9z" class="F"></path><path d="M354 646h3c7 0 14-2 19-7 1-2 2-5 4-6l1-1c-3 8-7 12-14 16-6 3-14 3-20 1-5-1-9-5-12-8 1 0 3 1 4 2h0c1 0 2 1 3 1s2 1 3 1c3 0 6 2 9 1z" class="K"></path><path d="M520 418h3l15 1-1 6c-3 0-8 0-12 1-2 0-4 0-6 1-1-2 0-7 1-9z" class="J"></path><path d="M520 418h3l4 1c0 2 0 5-2 7-2 0-4 0-6 1-1-2 0-7 1-9z" class="F"></path><path d="M567 421c4 4 7 8 13 8 5 1 10-1 14-5l6-6c-1 2-1 4-2 6-4 3-7 6-13 8l-3 1c-6 0-12-1-16-5-2-1-3-3-3-5l1 1h0 2 2s-1-1-1-2c-1-1-1 0 0-1z" class="D"></path><path d="M510 444h9 2 0 9c0 3 0 5-1 7l-5 1h-6-6-1l-1-3c-1-1 0-4 0-5z" class="G"></path><path d="M521 444h9c0 3 0 5-1 7l-5 1c1-1 2-1 3-2v-5c-2 0-4-1-6-1z" class="H"></path><path d="M510 444h9v7c-2 0-5 0-7 1h-1l-1-3c-1-1 0-4 0-5z" class="D"></path><path d="M510 251l-13-1v-6c7 0 16-1 23 0h0c0 2 0 5-1 6-2 1-6 1-9 1z" class="K"></path><path d="M519 250c-2 0-6 1-7 0-1 0-1-1-2-1v-5h10c0 2 0 5-1 6z" class="D"></path><path d="M552 550c2 2 4 1 4 5l-8 16-3 6c-1 4 0 9-2 12v1c0-4 1-8 0-12-1 1 0 1-1 2 0 0 0 1-1 1h-1l1-1c0-3 1-4 1-6l3-6 2-6 5-12z" class="P"></path><path d="M511 387c-5 0-10 0-14-1l-1-1h2v-1c-1 0-1 0-2-1l1-1-1-1 1-1 23-1-1 8h-1-7z" class="D"></path><defs><linearGradient id="I" x1="656.192" y1="598.012" x2="656.284" y2="607.872" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#I)" d="M643 604c2-2 6-5 9-5 8-3 15-1 21 2-1 1-2 2-2 3-1 1 0 2 0 3-6-4-11-5-18-4-5 1-9 5-12 9-1-1-1-1 0-2v-2c1-1 1-2 2-3h1l-1-1z"></path><path d="M516 610l-18-1-1-7 23-1v2c-1 2-1 4-1 6h-3v1zm-2-25c-3 0-7-1-10-1-2 0-4 1-7 0l1-1v-1c-1-1-1-2-1-3h-1v-1c2-1 6-1 8-1 5 0 10-1 15 0v8h-5zm5-222c-3 1-9 0-12-1h-9l-1-1h7v-2l-1 1c-2 0-5 0-6-1l-1-4 24-1c-1 3-1 6-1 9z" class="D"></path><path d="M512 564h7v6 3l-22-2v-6l15-1z" class="O"></path><path d="M512 564h7v6h-6c0-2-1-4-1-6z" class="D"></path><path d="M512 303l7 1-1 7 1 1c-5 1-10-1-15-1h-7v-1l-1-1v-1c0-1 0-3 1-4l15-1z" class="B"></path><path d="M512 303l7 1-1 7h-1c-2 0-4 0-5-2-1-1-1-3-1-4s1-1 1-2z" class="D"></path><defs><linearGradient id="J" x1="475.24" y1="538.242" x2="462.47" y2="535.701" xlink:href="#B"><stop offset="0" stop-color="#656563"></stop><stop offset="1" stop-color="#7c7a7d"></stop></linearGradient></defs><path fill="url(#J)" d="M459 512h1l3 9h1v-1c2 2 2 2 2 4l4 9c0 1 1 5 2 5 1 1 1 2 2 4l2 7 1 4 2 6 1 6v1l-1 9v1c-2-3 0-8-2-11v-3c-2-4-3-8-4-11l-7-18c-2-7-4-14-7-20v-1z"></path><path d="M472 538c1 1 1 2 2 4l2 7 1 4 2 6 1 6v1l-1 9v1c-2-3 0-8-2-11v-3-5l-5-19z" class="S"></path><defs><linearGradient id="K" x1="384.623" y1="679.088" x2="365.546" y2="670.984" xlink:href="#B"><stop offset="0" stop-color="#8d8a8b"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#K)" d="M376 650c-3 9-5 16-4 25 1 8 5 17 12 22v1h0l-2-1-5-3c-6-5-9-11-10-19-1-9 3-17 9-25z"></path><defs><linearGradient id="L" x1="440.564" y1="413.276" x2="454.231" y2="434.763" xlink:href="#B"><stop offset="0" stop-color="#bebcbc"></stop><stop offset="1" stop-color="#f7f6f7"></stop></linearGradient></defs><path fill="url(#L)" d="M458 410c-2 1-4 1-6 1-4 1-8 5-10 9-2 2-2 6-1 9s3 5 5 6c4 2 7 0 11-1l-4 3c-2 2-4 2-7 2s-5-2-6-3c-3-4-4-9-3-13 1-5 3-10 8-13 4-3 10-5 16-4-2 1-4 0-6 1h0c-2 0-3 1-4 1l1 2h2 4z"></path><defs><linearGradient id="M" x1="348.902" y1="596.3" x2="343.798" y2="611.6" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#M)" d="M336 615c2-9 5-14 13-19 1 1 3 2 5 3 1 0 2 1 3 1-6 2-10 6-12 11-1 3-2 6-2 9h0c0-1 0-1-1-2-1 1-2 1-4 1-1-1-1-2-2-4z"></path><path d="M509 547l-13-1v-7l15-1h8c1 3 0 6 0 8-2 1-4 1-7 1h-3z" class="G"></path><path d="M512 547c0-1-1-2-2-3v-5l9-1c1 3 0 6 0 8-2 1-4 1-7 1z" class="D"></path><defs><linearGradient id="N" x1="274.15" y1="356.23" x2="265.007" y2="366.997" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#767576"></stop></linearGradient></defs><path fill="url(#N)" d="M254 354c5-1 9-1 14 0h1c6 2 11 6 15 10h1c1 2 0 4 1 6-2 0-3 0-4 1-3-5-5-8-10-10-7-3-13-2-19 0l1-1 1-1-1-5z"></path><path d="M349 596c7-4 14-5 21-2 2 1 4 1 5 2s3 3 4 3c2 3 3 8 3 12h-1c-3-6-6-10-13-12-4-1-8-1-11 1-1 0-2-1-3-1-2-1-4-2-5-3z" class="P"></path><path d="M505 207c0-3 1-7 2-10l2-19 1 5v1c-1 8 0 17-1 26v27c-6 0-13 1-19 0 4-1 8 0 11-1 1 1 2 0 2 0 1-2 1-7 2-10h0c1-3 2-12 1-14s0-3-1-5z" class="B"></path><defs><linearGradient id="O" x1="459.792" y1="553.669" x2="469.257" y2="518.981" xlink:href="#B"><stop offset="0" stop-color="#cccccd"></stop><stop offset="1" stop-color="#f4f2f2"></stop></linearGradient></defs><path fill="url(#O)" d="M452 507l1 1c1 1 2 3 2 4l1-1c0 1 1 6 3 6h1l-1-1v-3c3 6 5 13 7 20l7 18c1 3 2 7 4 11v3 7l-7-17-18-48z"></path><path d="M470 555l7 17v-7c2 3 0 8 2 11v-1l1-9v-1-1-4c2 1 2 5 3 6l1 1-1 1c0 3 1 5 1 8v4h1v-1c0-1 0-1 1-2v7l-1-1c-1 1-1 3-1 5v1c0 1-1 1-2 2 0 2 1 3-1 5 0-1-1-2-1-3 0-2 0-1-1-2s-1-2-1-3h-1v-1c0-2-1-3-2-3-1-1-1-1-1-2 1 0 2 1 3 2v-1c-1-4-2-9-3-12-1-5-3-10-4-14v-2z" class="P"></path><path d="M481 574c1 1 1 4 1 6l1 1c0 2 0 3-1 5-1-1-1-2-1-3h0l-1-1v-4c1-1 1-2 1-4z" class="R"></path><path d="M511 552l7-1v1h2 5l1 1c1 2 1 4 0 6-1 0-1 0-2 1-1-1-4 0-5 0-2 1-6 1-8 0-4-1-10-1-14-1-1-3-1-5 0-7 5 0 9 0 14-1v1z" class="F"></path><path d="M518 552h2c0 2 1 4 1 5-1 1-2 1-3 1v-6z" class="B"></path><path d="M511 552l7-1v1 6c-2 1-5 0-7 0-1-1-1-1-1-2l1-1v-3z" class="D"></path><path d="M511 551v1 3l-1 1c0 1 0 1 1 2h-2c-3-1-6 0-9-1h-1c-1 0-1-1-2-1v-4c5 0 9 0 14-1z" class="B"></path><path d="M511 512l8 1 1-1 7 1-1 1 1 1-1 3v2h-4-2l-1 1h-8l-14-1v-1h1v-1c-1 0-1 0-2-1v-3c1-2 8-1 11-1l4-1z" class="F"></path><path d="M526 515v-1l1 1-1 3v2h-4-2c0-1 1-2 2-4v1l2-2h2z" class="G"></path><path d="M522 517l2-2h2v3c-2 0-2 0-4-1z" class="B"></path><path d="M511 512l8 1c1 2-1 5-1 7-1 0-4 1-6 0 0-1 0-1-1-2 0-2-3-3-4-5l4-1z" class="D"></path><defs><linearGradient id="P" x1="640.948" y1="681.298" x2="654.46" y2="691.5" xlink:href="#B"><stop offset="0" stop-color="#cecccc"></stop><stop offset="1" stop-color="#fafaf9"></stop></linearGradient></defs><path fill="url(#P)" d="M652 683h2c3-1 5-1 8-1l-1 1v1 1c-3 5-10 8-15 9-8 2-16 1-23-3v-1c-3-1-4-3-6-5l6 3h0c11 4 20 1 29-5z"></path><path d="M380 656v-1c3-6 7-9 13-11h0c-1 3-3 5-4 7-4 8-6 20-4 28 1 2 1 3 1 4 0 3 1 6 1 8-4-3-6-7-7-13-1-1-1-2-1-4h-1v-7c1-1 1-1 1-2s0-2 1-3v-1c0-1 0-1 1-2l1-3v-1l-1 1h-1z" class="D"></path><defs><linearGradient id="Q" x1="410.327" y1="698.542" x2="388.682" y2="692.814" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#Q)" d="M380 656h1l1-1v1l-1 3c-1 1-1 1-1 2v1c-1 1-1 2-1 3s0 1-1 2v7h1c0 2 0 3 1 4 1 6 3 10 7 13 0-2-1-5-1-8 0-1 0-2-1-4 3 5 5 9 8 13h1c1 0 1 0 2 1h1c5 2 8 2 13 1 1 0 1 1 2 1s2 0 3-1h0c-2 3-6 5-9 6-6 1-12-1-17-4-6-5-10-12-12-20-1-6-1-14 3-20z"></path><path d="M539 601l1 2-1 2-1 1c0 1 0 2-1 3l2 1c-1 1-2 3-2 4h-14-3-1c-2-1-4 0-6-1 1-1 1-2 3-3v-1h3c0-2 0-4 1-6v-2l17 1c1 0 2-1 2-1z" class="G"></path><path d="M522 609v-4-1l2-2c1 2 1 3 1 5 0 1 0 1-1 2h-2z" class="Q"></path><path d="M528 609l-2-1c1-2 1-3 2-4s2-1 3-1v3l-1 3h-2z" class="T"></path><path d="M539 601l1 2-1 2-1 1c0 1 0 2-1 3h-2-5l1-3v-3h4c2 0 2 0 2-1 1 0 2-1 2-1z" class="M"></path><path d="M539 601l1 2-1 2-1 1c0 1 0 2-1 3h-2l1-3c-1-1-1-2-1-3 2 0 2 0 2-1 1 0 2-1 2-1z" class="L"></path><path d="M535 609h2l2 1c-1 1-2 3-2 4h-14-3-1c-2-1-4 0-6-1 1-1 1-2 3-3v-1l1 1 5-1h2 4 2 5z" class="C"></path><path d="M516 609l1 1c1 1 1 2 1 3l2 1h-1c-2-1-4 0-6-1 1-1 1-2 3-3v-1z" class="P"></path><path d="M511 404h8 4 5c0 2 0 5-1 7v1l-9 1h-1c-4 0-7-1-10-1l-11-1c0-2-1-4 0-6 2-1 3-1 5-1h9 1z" class="B"></path><path d="M523 404h5c0 2 0 5-1 7v1l-9 1 2-3h6c0-2 0-4-1-5l-2-1z" class="K"></path><path d="M511 404h8v3l1 1c-1 0-1 1-1 2-1 1-1 1-1 2h-3l-4-4h-1c-1-2-1-2 0-4h1zm248-62c10 1 18 2 25 10v5c1 2 4 5 2 7-2-4-6-7-11-9-5-4-13-5-19-4h-1c-5 1-10 5-14 8v-1l2-2h1c4-3 8-5 13-7 0-1 1-4 1-5v-1l1-1z" class="D"></path><defs><linearGradient id="R" x1="549.417" y1="278.234" x2="563.172" y2="250.539" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#737171"></stop></linearGradient></defs><path fill="url(#R)" d="M563 285h-1c-7-2-14-7-17-13-3-5-5-12-3-17 2-6 6-13 12-16-2 2-4 5-5 7-2 6-2 14 0 20 2 7 8 13 14 16l5 1c-2 1-3 1-5 2z"></path><path d="M554 239c2-2 4-3 6-4-6 8-9 14-8 23 1 7 4 13 9 17 4 3 9 4 14 4 4-1 6-2 9-5 0 2-2 4-3 6l-4 2c-2 1-4 1-6 1h-3l-5-1c-6-3-12-9-14-16-2-6-2-14 0-20 1-2 3-5 5-7z" class="O"></path><path d="M432 356l1-1v-1c3-8 8-12 16-14 7-3 13-4 21-2 1 1 3 1 5 2h0 1c3 2 5 6 7 10-7-6-15-10-24-9-6 1-12 4-16 9-3 5-4 9-5 14l-2-1c1 0 0 0 1-1-2 0-4-1-6-1 1-2 1-3 1-5z" class="F"></path><defs><linearGradient id="S" x1="751.202" y1="353.401" x2="759.102" y2="369.531" xlink:href="#B"><stop offset="0" stop-color="#363535"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#S)" d="M741 359c4-3 9-7 14-8h1c6-1 14 0 19 4h1c0 1 1 2 2 2-1 2-1 4-2 5 1 2 2 3 3 4v1c-5-5-10-9-17-9-6-1-12 2-16 5-2 3-4 5-5 8-1 1-2 3-3 5h-1c-2-5 0-11 2-15h0c1 0 2-1 2-2z"></path><path d="M510 183l1-4 1-1 1 27h1c1 0 1 1 1 1 0 4 1 8 1 13l1 12c0 2 0 4 1 6h2l1-1 13 1c-2 1-4 1-6 1h-8c-4 1-7 1-11 0h-13-5c-1-1-3 0-5 0 1-1 3-1 4-1 6 1 13 0 19 0v-27c1-9 0-18 1-26v-1z" class="D"></path><path d="M514 218c1 2 0 4 1 6 2 3 1 9 1 13-1 0-1 0-3-1l1-18z" class="K"></path><path d="M513 205h1c1 0 1 1 1 1 0 4 1 8 1 13l1 12c0 2 0 4 1 6h-2c0-4 1-10-1-13-1-2 0-4-1-6l-1-13z" class="J"></path><defs><linearGradient id="T" x1="632.298" y1="662.45" x2="654.57" y2="669.813" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#T)" d="M646 642c5 2 9 9 11 14 1 5 0 11-2 16-4 6-10 10-17 12-3 1-7 2-10 1 0-1-1-1-1-2h0l-1-1 1-1h1 0c3-1 5-2 8-3v3c1 1 3 0 4-1 1 0 2-1 2-2h-1c0-2 2-3 3-5 2-2 3-5 4-8 0-3 1-6 1-9s-1-5-1-8l-1-2c0-2-1-2-1-4z"></path><path d="M648 648c3 5 5 9 4 14-2 7-5 12-10 16h-1c0-2 2-3 3-5 2-2 3-5 4-8 0-3 1-6 1-9s-1-5-1-8z" class="I"></path><path d="M436 337c7-5 16-7 26-5 5 1 11 4 14 8h-1 0c-2-1-4-1-5-2-8-2-14-1-21 2-8 2-13 6-16 14v1l-1 1c-1 2-1 3-2 5v1-1c-1-2-1-5-2-8v-7c0-2 6-6 7-8l1-1z" class="E"></path><path d="M238 355c7-7 14-11 24-11 5 0 13 3 17 6 0 1 0 1 1 1l-1 1c-3-3-7-6-11-5h0 1c4 1 8 4 12 6 1 4 3 7 4 11h-1c-4-4-9-8-15-10h-1c-5-1-9-1-14 0-7 3-12 6-17 12 0-4 3-6 3-9l-2-2z" class="D"></path><path d="M461 406c5 1 10 4 13 8 6 6 7 13 7 20 0 5-2 9-4 13h0-4c-5 5-10 9-18 9-5 0-9-3-14-5l-2-2h0 2c1 1 3 2 5 3 6 2 12 1 18-2 5-3 9-8 10-14 2-6 1-12-3-17-3-5-7-7-13-9h-4-2l-1-2c1 0 2-1 4-1h0c2-1 4 0 6-1z" class="O"></path><path d="M473 447c4-5 7-11 6-17h0 1c0 1 0 2 1 4h0c0 5-2 9-4 13h0-4z" class="L"></path><path d="M655 590c11 0 19 1 27 8 5 5 9 12 10 19 0 3 0 7-1 10h-1v1h0c-6-4-5-10-7-16l-1-1c-1-4-5-7-9-10-6-3-13-5-21-2-3 0-7 3-9 5h0l-1-1c3-3 4-4 7-5 2-1 3-1 5-2 2-2 2-3 1-6z" class="E"></path><defs><linearGradient id="U" x1="772.13" y1="359.073" x2="782.612" y2="394.856" xlink:href="#B"><stop offset="0" stop-color="#81807f"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#U)" d="M784 352l4 4c1 2 2 3 3 5 3 8 4 16 0 24-2 5-6 10-10 12-6 2-11 1-16-1 5-1 10 0 14-5 3-4 4-8 4-13-1-4-3-8-4-11v-1c-1-1-2-2-3-4 1-1 1-3 2-5-1 0-2-1-2-2h-1c5 2 9 5 11 9 2-2-1-5-2-7v-5z"></path><path d="M784 352l4 4c1 2 2 3 3 5v18l-3-10-2-5c2-2-1-5-2-7v-5z" class="B"></path><path d="M784 352l4 4c-1 2-1 4 0 6 0 1 1 2 1 3v2l-1 2-2-5c2-2-1-5-2-7v-5zM327 609l2-5c3-8 10-14 18-17 6-3 14-3 21-1 5 3 9 7 11 13-1 0-3-2-4-3s-3-1-5-2c-7-3-14-2-21 2-8 5-11 10-13 19-3-1-4-4-7-6l-1 1-1-1z" class="E"></path><defs><linearGradient id="V" x1="262.582" y1="369.559" x2="232.692" y2="388.205" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#c0bebf"></stop></linearGradient></defs><path fill="url(#V)" d="M237 366c5-6 10-9 17-12l1 5-1 1-1 1c-6 4-11 9-12 16-2 5-1 10 2 15 1 3 4 5 8 6h4c-1 1-3 2-4 2-5 0-10-1-13-4-5-4-8-11-9-17 0-3 0-6 1-10v-1h1c2-5 4-9 7-13l2 2c0 3-3 5-3 9z"></path><path d="M238 355l2 2c0 3-3 5-3 9-2 3-3 6-4 11v-1c0-1 0-2 1-3v-1-1-2c-1 2-1 4-2 6 0-3 0-5-1-7 2-5 4-9 7-13z" class="E"></path><path d="M327 609l1 1 1-1c3 2 4 5 7 6 1 2 1 3 2 4 2 0 3 0 4-1 1 1 1 1 1 2 0 6 1 11 5 16 5 4 8 5 15 6h-4c-1 0-2 0-3 1-4 1-7-3-10-1 2 1 5 3 8 4-3 1-6-1-9-1-1 0-2-1-3-1s-2-1-3-1h0c-1-1-3-2-4-2-2-2-4-4-5-6-4-8-5-18-3-26z" class="B"></path><path d="M346 642c-4-2-6-6-8-9 1 0 5 2 6 4 2 2 3 3 7 4l1-1c-1 0-1-1-1-1l-3-2v-1c5 4 8 5 15 6h-4c-1 0-2 0-3 1-4 1-7-3-10-1z" class="I"></path><defs><linearGradient id="W" x1="665.704" y1="627.363" x2="676.431" y2="645.057" xlink:href="#B"><stop offset="0" stop-color="#bbb9ba"></stop><stop offset="1" stop-color="#e1e0e0"></stop></linearGradient></defs><path fill="url(#W)" d="M673 601c4 3 8 6 9 10l1 1c2 6 1 12 7 16h0v-1h1c-1 6-5 13-10 17-1 0-2 1-3 2-6 2-11 4-17 2-5-2-9-4-13-7-3-2-5-3-6-5v-1c3 3 5 4 9 6 6 1 13 3 20 0 6-4 7-9 8-15h1c-1-9-3-13-9-19 0-1-1-2 0-3 0-1 1-2 2-3z"></path><path d="M661 648l1-1c3-1 5-1 8-2 2 0 6-2 9-1-1 0-1 0-1 1v1c-6 2-11 4-17 2z" class="O"></path><defs><linearGradient id="X" x1="674.033" y1="609.552" x2="682.227" y2="612.193" xlink:href="#B"><stop offset="0" stop-color="#888786"></stop><stop offset="1" stop-color="#b0aeae"></stop></linearGradient></defs><path fill="url(#X)" d="M673 601c4 3 8 6 9 10 0 4 1 10 0 13v-1-2l-1-1h0l-1-1v1 6c-1-9-3-13-9-19 0-1-1-2 0-3 0-1 1-2 2-3z"></path><defs><linearGradient id="Y" x1="587.151" y1="432.876" x2="544.558" y2="408.218" xlink:href="#B"><stop offset="0" stop-color="#989796"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#Y)" d="M552 387v1c-1 3-2 4-3 7-1 2-1 5-2 7v2l1-1c2 0 5 0 7 2 1 0 1 0 1 1 0-1 1-2 1-3v4h2v1l2-1c1-1 1-2 2-2 0 1 0 1 1 2 0 5 1 9 3 14-1 1-1 0 0 1 0 1 1 2 1 2h-2-2 0l-1-1c0 2 1 4 3 5 4 4 10 5 16 5l3-1 3 3c-2 0-4 1-6 2-8 1-16 0-22-5-7-4-12-12-13-20-2-8 0-18 5-25z"></path><path d="M297 420c-4 2-8 3-13 3-5-1-11-4-14-8-5-6-7-13-6-19s4-12 9-16c4-3 10-5 16-4 1 0 2 0 3 1l-2 1c0 1 0 1-1 2-5 2-9 5-11 10l-1 3v5 3 4l1 1h0c-1 3 2 7 3 9 1 0 1 1 1 2 3 1 6 3 10 2 2 1 3 0 5 0v1z" class="E"></path><defs><linearGradient id="Z" x1="276.577" y1="390.361" x2="270.923" y2="394.139" xlink:href="#B"><stop offset="0" stop-color="#817e7f"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#Z)" d="M272 402v-1c-1-6 1-12 4-16 1 2 1 3 0 5l-1 1c-1 3-1 6-1 10l-2-2v3z"></path><defs><linearGradient id="a" x1="285.243" y1="383.687" x2="276.949" y2="384.234" xlink:href="#B"><stop offset="0" stop-color="#545556"></stop><stop offset="1" stop-color="#6d6b6a"></stop></linearGradient></defs><path fill="url(#a)" d="M276 385l1-2c4-3 8-5 13-5 0 1 0 1-1 2-5 2-9 5-11 10l-1 3-2-2 1-1c1-2 1-3 0-5z"></path><path d="M274 401c0-4 0-7 1-10l2 2v5 3 4l1 1h0c-1 3 2 7 3 9 1 0 1 1 1 2-2-1-3-2-5-4-3-3-5-7-5-11v-3l2 2z" class="H"></path><path d="M272 402v-3l2 2c1 4 3 8 3 12-3-3-5-7-5-11z" class="Q"></path><path d="M738 376c7 1 13 3 17 9 4 5 5 11 4 18-1 6-5 12-10 16-4 3-10 4-16 3l-4-1c1-1 4-1 6-2 1-2 2-3 2-6 4-3 8-7 9-13 1-5 0-10-4-15 1 0 1-1 1-2 1-1 0-2-1-2-2-2-5-2-8-3-1 0-2 0-3-1l1-1h5 1z" class="D"></path><defs><linearGradient id="b" x1="739.085" y1="395.995" x2="751.317" y2="400.151" xlink:href="#B"><stop offset="0" stop-color="#797877"></stop><stop offset="1" stop-color="#b5b3b3"></stop></linearGradient></defs><path fill="url(#b)" d="M742 381c5 3 9 8 10 14s-1 12-5 17c-3 4-7 6-12 7 1-2 2-3 2-6 4-3 8-7 9-13 1-5 0-10-4-15 1 0 1-1 1-2 1-1 0-2-1-2z"></path><path d="M513 613c2 1 4 0 6 1h1 3 14c1 3 0 5-1 8h1l-1 4h-11-5l3 2c0 2 0 3-2 5h-19c1-1 1-2 1-3h-1-1l-2 1c-2 0-3 0-5-1v-4c-1-1-2-1-3-1-1 1-1 3-2 4h-1 0c0-2-1-4-1-6l1 1h1c0-1 0-1-1-2 2-1 4 0 6-2h1 2c-1-1 0-1-1-2h1l-1-1c0-1 0-1 1-2 2-1 5-1 8-1l8-1z" class="C"></path><path d="M523 614h14c1 3 0 5-1 8-4-2-10-1-15 0v-1c0-3 1-4 2-7z" class="T"></path><path d="M509 626c4 0 7-1 11 0h0l3 2c0 2 0 3-2 5h-19c1-1 1-2 1-3h-1-1l-2 1c-2 0-3 0-5-1v-4h10 5z" class="J"></path><path d="M494 626h10l-5 1-1 2c1 0 2 1 3 1h0l-2 1c-2 0-3 0-5-1v-4z" class="M"></path><path d="M509 626c4 0 7-1 11 0v2c-1 1-1 2-2 2-2 1-2 1-4 1-2-1-4-3-5-5z" class="I"></path><path d="M513 613c2 1 4 0 6 1h1 3c-1 3-2 4-2 7v1h-3 0c-4 0-8-1-12-1s-8 0-12-1h1 2c-1-1 0-1-1-2h1l-1-1c0-1 0-1 1-2 2-1 5-1 8-1l8-1z" class="B"></path><path d="M519 614h1 3c-1 3-2 4-2 7v1h-3c-1-1-4 0-6-1 0-2-1-4-1-6l1-1h7z" class="E"></path><defs><linearGradient id="c" x1="216.635" y1="184.045" x2="145.573" y2="203.977" xlink:href="#B"><stop offset="0" stop-color="#828080"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#c)" d="M228 229c-5-6-9-13-14-19-10-10-21-20-34-27-10-6-22-9-33-13l-23-8c5-1 13 0 18 0l-11 1c14 3 28 5 42 9 22 7 39 24 51 43l6 9-2 5z"></path><path d="M734 378c3 1 6 1 8 3 1 0 2 1 1 2 0 1 0 2-1 2 4 5 5 10 4 15-1 6-5 10-9 13 0 3-1 4-2 6-2 1-5 1-6 2h-2c-4-2-8-6-10-10l2-1-1-1c-1-3 9-25 11-30h1c1 0 2-1 4-1z" class="C"></path><defs><linearGradient id="d" x1="737.648" y1="377.823" x2="738.537" y2="384.587" xlink:href="#B"><stop offset="0" stop-color="#626061"></stop><stop offset="1" stop-color="#7a7a78"></stop></linearGradient></defs><path fill="url(#d)" d="M734 378c3 1 6 1 8 3 1 0 2 1 1 2 0 1 0 2-1 2-4-3-7-5-13-6h1c1 0 2-1 4-1z"></path><path d="M719 410c3 3 5 6 10 6 2-1 5-2 8-3 0 3-1 4-2 6-2 1-5 1-6 2h-2c-4-2-8-6-10-10l2-1z" class="P"></path><defs><linearGradient id="e" x1="455.828" y1="394.178" x2="427.267" y2="347.337" xlink:href="#B"><stop offset="0" stop-color="#9d9b9b"></stop><stop offset="1" stop-color="#e7e6e5"></stop></linearGradient></defs><path fill="url(#e)" d="M436 337l-1 1c-1 2-7 6-7 8v7c1 3 1 6 2 8v1-1c1-2 1-3 2-5 0 2 0 3-1 5 2 0 4 1 6 1-1 1 0 1-1 1l2 1c0 6 2 11 6 15s8 5 13 5c6 0 10-2 13-6l2-3h1c0 1 1 2 0 3h2c-2 5-5 8-9 11-6 5-14 7-21 5-7-1-14-5-18-11-5-7-7-17-5-25s6-16 14-21z"></path><path d="M431 361c2 0 4 1 6 1-1 1 0 1-1 1l2 1c0 6 2 11 6 15s8 5 13 5c6 0 10-2 13-6l2-3h1c0 1 1 2 0 3-2 4-6 8-11 9-5 2-12 2-17 0-7-4-12-10-12-17-1-3 0-5-1-7h0c0 1 0 1-1 2v-4zm121 26c3-6 10-10 16-13 7-2 15-2 21 1s11 8 13 14l1 6v2h-1c-1-3-2-5-4-7h-4c-5-3-9-4-14-3s-10 5-12 9c-3 4-3 7-4 11-1-1-1-1-1-2-1 0-1 1-2 2l-2 1v-1h-2v-4c0 1-1 2-1 3 0-1 0-1-1-1-2-2-5-2-7-2l-1 1v-2c1-2 1-5 2-7 1-3 2-4 3-7v-1z" class="E"></path><defs><linearGradient id="f" x1="574.785" y1="382.675" x2="577.485" y2="393.711" xlink:href="#B"><stop offset="0" stop-color="#515150"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#f)" d="M557 403c2-8 4-14 11-18 4-3 10-4 16-3h0c6 1 10 4 14 8h-4c-5-3-9-4-14-3s-10 5-12 9c-3 4-3 7-4 11-1-1-1-1-1-2-1 0-1 1-2 2l-2 1v-1h-2v-4z"></path><path d="M879 162c6 0 12-1 17 0l-29 10c-10 3-19 7-28 11-19 11-37 29-48 49l-54 127v-1h0c1-2 1-2 1-3 1-1 1-2 1-3-1-2 1-4 1-6-1 0-1 1-2 1-1 1-2 1-2 2h-1v-1c-2-4 1-5 2-8l2-4c1-3-1-5-2-7 2 0 3 1 5 1h0c1-2 2-5 4-7v-1c2-4 3-8 5-12 1-3 2-6 4-9l5-10 9-25 17-38c1 0 7-12 8-13 4-7 11-14 17-21 8-7 17-14 27-19 16-7 33-9 50-12l-9-1z" class="K"></path><path d="M751 310c0 3 0 4-1 7h0c-1 3-2 5-4 6v-1c2-4 3-8 5-12z" class="G"></path><path d="M737 329c2 0 3 1 5 1h0c-2 6-4 12-7 18-2-4 1-5 2-8l2-4c1-3-1-5-2-7z" class="E"></path><path d="M357 249c1 6 3 11 6 16l10 26 3 6 63 152h0l21 52-1 1-2 4c0 2 1 5 2 6v1 3l1 1h-1c-2 0-3-5-3-6l-1 1c0-1-1-3-2-4l-1-1c-6-10-10-22-15-33-1-4-3-9-5-13-3-9-7-18-11-26 0-2-1-4-2-6-1-3-2-5-3-8l-1-2-1-2c-1-2-1-3-2-4 0-1 0-2-1-3h0v-1l-2-4c0-2-1-3-2-4v-2c-1 0-1-1-1-2 0 0 0-1-1-1l-1-1c0-2-1-5-2-6 0-2-1-3-2-4 0-1 0-2-1-2v-2c-1-1-1-1-1-2l-1-2c0-1 0-2-1-3-2-6-5-12-7-18l-12-27c0-2-2-5-2-7l-9-21c0-2-3-7-3-8l-8-17c-1-3-2-6-5-9 0-1-1-2-1-4 0-1-2-6-3-8 4-2 8-4 11-6z" class="D"></path><path d="M650 181l-52-19h7 13 171 5l-9 12v-1-3c-1 0-1 0-2 1l-23 13c-1 2-1 2-3 3h0c-5 0-11 4-15 6-2 1-4 2-5 3s-1 1-1 2l-1-1c-1 1-2 2-3 4h-3-1l-1 1-1 1c0 1-1 1-2 2v1c0 1-1 1-2 2-3 2-7 4-10 5 4-6 8-11 13-16l-2-1h-1c-1 1-2 2-3 4v-1h-2v-1c-2 1-5 2-7 4h0c-3 2-9 6-10 10-3 3-5 6-7 10l-1 1c-1 1-1 2-1 3v7c1 2 1 6 0 8 0 3 1 5-1 7l-4 9c-2-3-6-2-10-4h0c3-10 3-22 0-33-2-11-7-23-16-31-3-3-7-5-10-8z" class="E"></path><path d="M697 193h-3v-1c2-2 3-3 6-4 1 0 4 0 5 1-2 2-5 2-8 4z" class="H"></path><path d="M705 189c1 1 1 2 0 4-3 6-10 13-16 16h-1c0-2 0-3 1-4 1-2 2-6 4-6h0c2-1 4-3 5-5l-1-1c3-2 6-2 8-4z" class="B"></path><path d="M729 189l-4 8h0l-2-1h-1c-1 1-2 2-3 4v-1h-2v-1c-2 1-5 2-7 4h0c-3 2-9 6-10 10-3 3-5 6-7 10l-1 1 1-4c5-16 22-23 36-30z" class="P"></path><path d="M650 181l-52-19h7 13l-8 1h-3c8 1 16 3 23 5 14 4 26 8 38 17-1 0-1 1-2 1s-1-1-2 0l-4 1c-2-1-4-3-6-4-1 0 0 0-1-1h-1c-1 0-1-1-2-1z" class="K"></path><path d="M789 162h5l-9 12v-1-3c-1 0-1 0-2 1l-23 13c-1 2-1 2-3 3h0c-5 0-11 4-15 6-2 1-4 2-5 3s-1 1-1 2l-1-1c-1 1-2 2-3 4h-3-1l-1 1-1 1c0 1-1 1-2 2v1c0 1-1 1-2 2-3 2-7 4-10 5 4-6 8-11 13-16h0l4-8c2-2 5-3 7-4 5-3 9-5 14-7 7-3 14-7 22-9 5-2 10-3 15-5 1 0 2-1 2-2z" class="B"></path><defs><linearGradient id="g" x1="696.927" y1="240.594" x2="649.415" y2="199.841" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#959494"></stop></linearGradient></defs><path fill="url(#g)" d="M650 181c1 0 1 1 2 1h1c1 1 0 1 1 1 2 1 4 3 6 4l4-1c1-1 1 0 2 0s1-1 2-1c13 11 21 28 22 45l1 11c0 3 1 5-1 7l-4 9c-2-3-6-2-10-4h0c3-10 3-22 0-33-2-11-7-23-16-31-3-3-7-5-10-8z"></path><path d="M719 200c1-2 2-3 3-4h1l2 1c-5 5-9 10-13 16 3-1 7-3 10-5 1-1 2-1 2-2v-1c1-1 2-1 2-2l1-1 1-1h1 3c1-2 2-3 3-4l1 1v2c-3 4-5 9-7 14h1c-6 9-9 17-7 27 0 3 2 9 5 11 0 0 1 1 2 1l-12 30-34 77-21 50-32 75-16 38c-3 8-7 16-9 23h-1l-1 3h-1l1-3c1-6-3-14-4-19-2-5-2-12-5-16-1 1-1 2-1 3s-1 2-2 3c-2 1-3 1-4 0-3-1-5-3-7-5l-10 24c-2 4-3 8-5 12-1 0-1 0-2 1l-2 5c-3-4-3-6-8-9l7-15c0-2 2-6 3-8l11-28 37-89 22-51c1-4 3-8 5-12l8-20 29-69h0c4 2 8 1 10 4l4-9c2-2 1-4 1-7 1-2 1-6 0-8v-7c0-1 0-2 1-3l1-1c2-4 4-7 7-10 1-4 7-8 10-10h0c2-2 5-3 7-4v1h2v1z" class="O"></path><path d="M605 487l1-2c0-2 1-3 3-3-1 1-1 3-1 5h-3zm61-135l-1-1c-1-1-3-4-3-6h0c0-3 0-5 3-8v1 7c0 3 0 4 1 7z" class="B"></path><path d="M632 438c3 2 6 4 9 3 2 0 4-2 6-3-1 2-2 4-4 5-1 1-6 1-7 0-3-1-3-2-4-5z" class="I"></path><path d="M666 309c1-1 1-2 2-3 0 2 0 5 2 6 2 3 6 4 9 6l-1 1c-1 0-3 1-5 0-4-2-6-6-7-10z" class="J"></path><path d="M670 306l9 7v5c-3-2-7-3-9-6-2-1-2-4-2-6h2z" class="H"></path><path d="M638 393c4 3 5 7 6 12v2c1 3 0 8 2 11 0 3 2 5 2 7v1h0c0-2-1-4-2-5-1 2 1 5 1 6-1 0-2 0-3-1l-2-1c1 0 1-1 2-2l1 1c0-3-1-6-1-9l-1-10c-1-4-4-7-6-9l1-3z" class="S"></path><path d="M690 276h0c1 1 2 1 3 3 0 1 0 3-1 5-1 1-2 1-4 2-2 0-3-1-4-2s-2-2-2-3 0-1 1-2 2-1 4-1v1h2v-1h2 0l-1-2z" class="C"></path><path d="M639 391c1-2 0-5 1-7 4 6 6 12 5 20 0 1 0 2-1 3v-2c-1-5-2-9-6-12l1-2zm-48 98c6 7 8 14 8 23v4c-1-4-3-7-5-10 1-3-2-6-2-9 0-1 0-2-1-2 0-2-1-3 0-6z" class="H"></path><defs><linearGradient id="h" x1="615.384" y1="497.156" x2="611.264" y2="486.048" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#cccbcc"></stop></linearGradient></defs><path fill="url(#h)" d="M605 487h3c0 2 1 5 3 6s5 1 7 1c2-1 3-2 4-4 1-1 1-2 1-3l1 2c0 3-1 5-2 7-1 1-3 2-5 2-2 1-6 0-8-1-3-3-4-6-4-10z"></path><path d="M682 335c2 3 3 7 2 11s-3 6-6 8h0c-1 1-2 1-4 1s-5-1-8-3c-1-3-1-4-1-7 2 3 3 4 6 5s7 0 9-2h1v-1c1-2 1-4 1-6v-1-5z" class="K"></path><path d="M665 345c2 3 3 4 6 5s7 0 9-2c0 1 0 1-1 2-2 2-5 2-8 2h-1l1 1c2 1 5 1 7 1-1 1-2 1-4 1s-5-1-8-3c-1-3-1-4-1-7z" class="G"></path><path d="M705 282h3c2 1 3 2 4 4 0 2 0 3-1 5s-1 2-3 3h-2c-2 0-3-2-4-3-2-1-2-3-1-5 0-1 1-2 2-3h0l2-1zM586 500v-2c2-1 3-1 6-1 0 3 3 6 2 9 2 3 4 6 5 10s1 8 3 11c1 6 4 13 3 19l-1 3h-1l1-3c1-6-3-14-4-19-2-5-2-12-5-16l-3-3c-2-2-5-4-6-6v-2z" class="C"></path><path d="M586 500v-2c2-1 3-1 6-1 0 3 3 6 2 9-2-2-5-4-8-6z" class="I"></path><path d="M645 353l13-30 1 1-23 55c1 1 3 3 4 5-1 2 0 5-1 7l-1 2c-2 0-4-2-5-3l-2-2 14-35z" class="J"></path><path d="M633 390c1-2 1-2 3-2 1 0 2 0 2 1 1 1 1 2 1 2l-1 2c-2 0-4-2-5-3z" class="O"></path><path d="M698 239h3c1 1 2 1 2 2-1-1-2-1-3-1-2 0-3 1-4 2v1c2 0 3 1 4 1l1-2h1c1 0 3 1 4 3 1 1 1 3 1 4-1 2-2 3-3 4-2 1-4 1-5 1-3-1-5-2-6-4s-1-4-1-6c1-2 4-4 6-5zm-87 241c2-1 4-2 6-1 2 0 4 2 5 4 1 1 1 2 1 4 0 1 0 2-1 3-1 2-2 3-4 4-2 0-5 0-7-1s-3-4-3-6 0-4 1-5l2-2z" class="C"></path><path d="M611 480h1c2-1 3-1 5 0 3 0 4 2 5 4h0l-3-2-3-2v1c0 1 0 2-1 3h-3l-2-2 1-2z" class="E"></path><path d="M631 388l2 2c1 1 3 3 5 3l-1 3c2 2 5 5 6 9h0c-3 1-5 2-8 0-3-1-5-3-7-6l-20 47c-1 1-2 3-2 4l-9 23h-1l24-57 6-16c2-3 2-8 5-10v-2z" class="H"></path><path d="M631 388l2 2c1 1 3 3 5 3l-1 3c-2-2-5-3-6-6v-2z" class="R"></path><path d="M665 337c1-1 2-3 4-4 3-2 5-2 9-1l1 2c1 1 1 2 2 4v3h1c0 2 0 4-1 6v1h-1c-2 2-6 3-9 2s-4-2-6-5v-7-1z" class="C"></path><path d="M665 337c1-1 2-3 4-4 3-2 5-2 9-1l1 2v7c-1-1 0-4-2-5v1c0 1-1 2-2 3s-6 1-7 1c-1-1-2-1-3-3v-1z" class="G"></path><defs><linearGradient id="i" x1="682.948" y1="307.617" x2="676.129" y2="302.441" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#7e7c7d"></stop></linearGradient></defs><path fill="url(#i)" d="M671 297c0-1 1-3 2-4v-1l1-4c1-1 1-2 0-3l3 3c5 7 6 12 5 20-1 3-1 6-2 8v11c0 2-1 4 0 6l2 2v5 1h-1v-3c-1-2-1-3-2-4l-1-2c1-3 0-10 0-13l1-1v-5l-9-7c-1-2-1-4-1-6 1 0 1-2 2-3z"></path><path d="M669 300l5 3 1 2 2 2v-6c1 2 1 6 1 8 0 1 1 3 1 4l-9-7c-1-2-1-4-1-6z" class="C"></path><path d="M671 297c0-1 1-3 2-4v-1l1-4c1-1 1-2 0-3l3 3v9c-1 2 0 6-2 8l-1-2-5-3c1 0 1-2 2-3z" class="K"></path><path d="M671 297h2l1 1v5l-5-3c1 0 1-2 2-3z" class="I"></path><path d="M719 200c1-2 2-3 3-4h1l2 1c-5 5-9 10-13 16l-12 14-1 1h-1c-1-4 2-11 3-15l-1-1c1-4 7-8 10-10h0c2-2 5-3 7-4v1h2v1z" class="H"></path><path d="M710 202l1 1-10 10-1-1c1-4 7-8 10-10z" class="B"></path><path d="M719 200c1-2 2-3 3-4h1l2 1c-5 5-9 10-13 16l-12 14v-1l19-26z" class="S"></path><path d="M647 427c0-1-2-4-1-6 1 1 2 3 2 5h0v-1c2 4 0 9-1 13-2 1-4 3-6 3-3 1-6-1-9-3-1-1-2-3-2-5 0-3 0-7 3-10 1-1 2-1 4-1l2 2c1 0 2 0 3-1h2c-1 1-1 2-2 2l2 1c1 1 2 1 3 1z" class="F"></path><path d="M647 427c0 2 0 5-1 7-2 1-3 3-5 3s-5 0-7-2c-1-1-2-3-2-5s0-3 1-4h3 1 0c1 0 4 0 5-1l2 1c1 1 2 1 3 1z" class="C"></path><path d="M608 446l1-1 2-2c2-1 4-2 6-2h1l3 3c1 1 2 2 3 4 2 1 2 1 2 4s-1 5-3 7-4 3-6 3c-3 0-6-1-8-4-2-2-1-6-3-8 0-1 1-3 2-4z" class="G"></path><path d="M621 444c1 1 2 2 3 4 0 2 0 4-1 6s-2 3-5 4c-2 0-4 0-5-1-2-2-3-4-4-6 0-1 0-2 1-4 1-1 3-2 5-2-1 1-1 1-1 2h2s0-1 1-1v1 1l3-1c1-1 1-1 1-3zm45-69h1c1 3 0 5-1 7l-2 4c-3 2-5 3-9 3-2-1-3-2-4-4-2-2-4-6-3-9s3-6 5-7c1-1 4-1 6-1h0c4 1 5 3 7 6v1z" class="C"></path><path d="M666 375h1c1 3 0 5-1 7l-2 4c-3 2-5 3-9 3-2-1-3-2-4-4-2-2-4-6-3-9s3-6 5-7c1-1 4-1 6-1h0c1 1 2 2 2 3 0 0 0 2-1 2-1 1-3 0-4 0h2v-1h-3v1h0-4c-1 2-1 3-1 5 1 2 1 4 3 5s5 1 7 1c2-1 4-2 5-4v-5h1 0z" class="F"></path><defs><linearGradient id="j" x1="691.811" y1="264.592" x2="640.956" y2="313.685" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#b5b2b2"></stop></linearGradient></defs><path fill="url(#j)" d="M676 253h0c4 2 8 1 10 4l-12 28c1 1 1 2 0 3l-1 4v1c-1 1-2 3-2 4-1 1-1 3-2 3 0 2 0 4 1 6h-2c-1 1-1 2-2 3h0l-7 15-1-1-13 30v-1c0-2 1-3 2-5 0-1 0-1 1-1v-1-1h1c0-1 0-1 1-2h0v-2c1-1 1-1 1-2s0-1 1-1v-1l2-5s0-1 1-2c0-1 0-1 1-2v-2h0c-3 0-5-2-8-3-2 5-11 30-14 32 1-4 3-8 5-12l8-20 29-69z"></path><defs><linearGradient id="k" x1="672.982" y1="423.113" x2="540.647" y2="446.018" xlink:href="#B"><stop offset="0" stop-color="#c9cecc"></stop><stop offset="1" stop-color="#efe9ea"></stop></linearGradient></defs><path fill="url(#k)" d="M634 354c3-2 12-27 14-32 3 1 5 3 8 3h0v2c-1 1-1 1-1 2-1 1-1 2-1 2l-2 5v1c-1 0-1 0-1 1s0 1-1 2v2h0c-1 1-1 1-1 2h-1v1 1c-1 0-1 0-1 1-1 2-2 3-2 5v1l-14 35v2c-3 2-3 7-5 10l-6 16-24 57h1c-2 5-6 11-7 16h1c-1 3 0 4 0 6 1 0 1 1 1 2-3 0-4 0-6 1v2 2c1 2 4 4 6 6l3 3c-1 1-1 2-1 3s-1 2-2 3c-2 1-3 1-4 0-3-1-5-3-7-5l-10 24c-2 4-3 8-5 12-1 0-1 0-2 1l-2 5c-3-4-3-6-8-9l7-15c0-2 2-6 3-8l11-28 37-89 22-51z"></path><path d="M561 530v1c0 2-3 7-4 10h1c3 2 5 3 6 6v1c1-2 1-3 2-4l2-5v-1c0-1 1-2 2-2h1c-2 4-3 8-5 12-1 0-1 0-2 1l-2 5c-3-4-3-6-8-9l7-15z" class="G"></path><path d="M570 536l26-63h1c-2 5-6 11-7 16h1c-1 3 0 4 0 6 1 0 1 1 1 2-3 0-4 0-6 1v2 2c1 2 4 4 6 6l3 3c-1 1-1 2-1 3s-1 2-2 3c-2 1-3 1-4 0-3-1-5-3-7-5l-10 24h-1z" class="J"></path><path d="M788 173h0c3-3 7-5 10-7 1-1 3-3 5-4h5 71l9 1c-17 3-34 5-50 12-10 5-19 12-27 19-6 7-13 14-17 21-1 1-7 13-8 13l-17 38-9 25-5 10c-2 3-3 6-4 9-2 4-3 8-5 12v1c-2 2-3 5-4 7h0c-2 0-3-1-5-1 1 2 3 4 2 7l-2 4c-1 3-4 4-2 8v1h1c0-1 1-1 2-2 1 0 1-1 2-1 0 2-2 4-1 6 0 1 0 2-1 3 0 1 0 1-1 3h0v1c-2 5-4 11-6 16l1 1-1 1c1 1 2 1 3 1-2 0-3 1-4 1h-1c-2 5-12 27-11 30l1 1-2 1v-1c-3 4-5 10-7 15l-11 25-36 88-16 37-4 9-5 12-29 71-15 35c-3 7-6 12-8 19v1h4l-1 1h-3-1c-2 4-4 9-6 14l-9 23-24 60c-1 3-6 16-7 17-2 9-6 17-10 26l-19 49c-1-2-2-4-2-6l-4-10-30-75-37-91-12-28c-3-7-6-14-8-22l-29-70-8-20-67-162-29-71-8-17-27-64c-6-15-13-32-21-47l2-5-6-9c-12-19-29-36-51-43-14-4-28-6-42-9l11-1h57 11 5c2 1 5 4 7 6l14 11c5 4 10 9 16 11l7-9-1 10c1 1 1 1 2 1 4 1 9 4 14 6s11 4 16 7h1 0l-2-3 3-3-2-4h-1l-2-2c-2-2-3-2-5-1-14-13-34-19-50-28-2 0-4-1-5-2h194 17l-28 10c-19 6-36 14-46 32-7 14-8 30-7 45-3 2-7 4-11 6 1 2 3 7 3 8 0 2 1 3 1 4 3 3 4 6 5 9l8 17c0 1 3 6 3 8l9 21c0 2 2 5 2 7l12 27c2 6 5 12 7 18 1 1 1 2 1 3l1 2c0 1 0 1 1 2v2c1 0 1 1 1 2 1 1 2 2 2 4 1 1 2 4 2 6l1 1c1 0 1 1 1 1 0 1 0 2 1 2v2c1 1 2 2 2 4l2 4v1h0c1 1 1 2 1 3 1 1 1 2 2 4l1 2 1 2c1 3 2 5 3 8 1 2 2 4 2 6 4 8 8 17 11 26 2 4 4 9 5 13 5 11 9 23 15 33l18 48v2c1 4 3 9 4 14 1 3 2 8 3 12v1c-1-1-2-2-3-2 0 1 0 1 1 2 1 0 2 1 2 3v1h1c0 1 0 2 1 3s1 0 1 2c0 1 1 2 1 3-1 3-1 6-1 9 1 3 2 7 4 9l3 6c-1 2-1 2 0 3 0 2 1 4 1 6h0 1c1-1 1-3 2-4 1 0 2 0 3 1v4c2 1 3 1 5 1l2-1h1 1c0 1 0 2-1 3h19c2-2 2-3 2-5l-3-2h5 11l1-4h-1c1-3 2-5 1-8 0-1 1-3 2-4l-2-1c1-1 1-2 1-3l1-1 1-2-1-2c1-4 2-8 4-11v-1c2-3 1-8 2-12l3-6 8-16c0-4-2-3-4-5l1-3 1-2c5 3 5 5 8 9l2-5c1-1 1-1 2-1 2-4 3-8 5-12l10-24c2 2 4 4 7 5 1 1 2 1 4 0 1-1 2-2 2-3s0-2 1-3c3 4 3 11 5 16 1 5 5 13 4 19l-1 3h1l1-3h1c2-7 6-15 9-23l16-38 32-75 21-50 34-77 12-30c-1 0-2-1-2-1-3-2-5-8-5-11-2-10 1-18 7-27h-1c2-5 4-10 7-14v-2c0-1 0-1 1-2s3-2 5-3c4-2 10-6 15-6h0c2-1 2-1 3-3l23-13c1-1 1-1 2-1v3 1 1l3-2z" class="O"></path><path d="M671 493h1 1c-1 1-1 2-2 3-1 0-1 0-2-1v-1l2-1z" class="I"></path><path d="M600 641c2-1 6-1 8 0 1 1-1 2 0 3-2 0-3-1-5-2-1 0-2 0-3-1z" class="E"></path><path d="M584 682c2-1 4 1 6 2 1 0 2 1 2 2v1h-1c-2 0-6-3-7-5z" class="R"></path><path d="M404 604h0v2c0 1-1 2-1 3-1 2-2 5-4 6v-2c-1-2 4-7 5-9z" class="U"></path><path d="M749 307c-2-1-4-2-6-4v-1c2 0 4 0 6 1s2 2 2 3c-1 1-1 1-2 1zm-52 103c2 0 5 0 7 1 1 0 1 1 2 2v2h-1c-2-1-7-3-8-5z" class="C"></path><path d="M672 485l-8-8c0-1 0-1 1-2h3l1 1s-1 2 0 3c0 2 3 3 3 6z" class="E"></path><path d="M678 454c4 0 6 1 10 3-1 1-1 2-2 2-3-1-6-3-8-5zM558 739c3 0 5 1 8 2h1 0l1 2c-1 0-1 1-1 2-3 0-4-1-6-3l-3-3zm110-264c3 0 5 1 8 2h1l2 3c-1 1-1 1-3 1l-7-5-1-1z" class="C"></path><path d="M609 630h0c-2-3-3-4-3-7 1-1 1-1 2-1v1c2 1 4 3 6 4l-1 2-2-2c-1 1-2 2-2 3z" class="G"></path><path d="M623 585h3c3 0 5 1 7 3v1l-1 1c-4 0-6-3-9-5z" class="C"></path><path d="M561 742c2 2 3 3 6 3-3 1-3 3-4 6l-4-5c0-2 1-3 2-4z" class="Q"></path><path d="M627 604c0-1-1-4 0-5 0-1 2-2 2-4 1-3 2-3 4-5l-6 14z" class="E"></path><path d="M731 324c4 0 7 0 10 3 1 1 1 1 1 3-2 0-3-1-5-1l-6-5zM608 622c3 0 6 1 9 3 0 2 0 2-1 3l-2-1c-2-1-4-3-6-4v-1z" class="C"></path><path d="M559 746c-2-1-4-3-5-4v-3h4l3 3c-1 1-2 2-2 4z" class="E"></path><path d="M565 730c-1-1-3-3-4-5-1-1-1-1-1-2l3-1 1 1 2 2-1 5z" class="I"></path><path d="M564 723c4 0 6 0 10 3v1c0 1 0 2-2 2h0c-3-1-4-3-6-4l-2-2z" class="C"></path><path d="M566 725c2 1 3 3 6 4l-2 1c0 1 1 2 1 3v1c-2 0-4-3-6-4l1-5z" class="G"></path><path d="M592 662c3 1 6 2 8 4 0 2 0 1-1 3l-3-1c-1-1-5-3-5-5l1-1zm115-275l-2-2c3 0 7 0 9 2 1 1 2 1 2 2l-1 2-1 1c-3-1-4-5-7-5zm15-43h3c4 1 7 2 8 5v2l-1 1c-2-1-4-2-5-4s-3-3-5-4z" class="C"></path><path d="M575 701v-1c3 0 8 3 10 6-1 0-1 1-1 1l-3 1c-2-2-4-5-6-7z" class="R"></path><path d="M703 420c-1-1-4-3-5-4l-2-4v-1l1-1c1 2 6 4 8 5h1c-2 1-2 3-3 5z" class="F"></path><path d="M707 387c3 0 4 4 7 5-1 1-2 3-2 5-1 0-1 0-1-1-1 0-1-1-1-2l-4-4c0-1 0-3 1-3z" class="B"></path><path d="M669 476l7 5c-1 1-1 1 0 2 0 1-1 3-1 4h0c-2 0-2-1-3-2 0-3-3-4-3-6-1-1 0-3 0-3z" class="G"></path><path d="M602 659v-1-2c-1-5-3-8-6-11 0-2 0-1 1-2l3 3c2 2 3 4 5 6l-3 7z" class="E"></path><path d="M394 598c0-3 1-6 2-8l4-16c2 5 1 10 1 15-1-2-1-4 0-6l-1 1c0 1-1 5-2 6l-1-3v2c0 1-1 2-1 2v1c0 1 0 2-1 3v1l-1 2z" class="I"></path><path d="M575 701c2 2 4 5 6 7l-1 4c-2-2-8-7-9-10h3l1-1z" class="F"></path><path d="M592 687c-1 1-2 1-3 1l1 2c-1 1-1 1-3 2l-6-7v-2l3-1c1 2 5 5 7 5h1z" class="G"></path><path d="M439 708c1-1 1-1 2-1 1 1 3 0 4 0v2c-1 4 0 9 1 13h-1l-1-2-5-12z" class="E"></path><path d="M492 837c2-1 3-2 5-2 1 1 1 3 1 4 0 2-2 4-4 6-2-2-2-4-3-6l1-2z" class="L"></path><path d="M682 466v-1-3s-1-1-2-1c-1-2-5-4-6-6 2-1 2-1 4-1 2 2 5 4 8 5-2 2-3 5-4 7z" class="F"></path><path d="M334 204c1 1 1 2 1 4l-2 1c0 1 0 1 1 2 1 2 0 5 0 8h-1c-1-1-2-2-2-4-1-3-1-5-2-8v-1l1 1c2-1 3-2 4-3z" class="I"></path><path d="M600 641c1 1 2 1 3 1 2 1 3 2 5 2v3h-2l-1 1v4c-2-2-3-4-5-6l-3-3 3-2z" class="B"></path><path d="M596 668c0 1 1 2 1 3 0 2 0 2-1 4h-1c-3-4-5-5-6-10l1-2c0-1 0 0 1-1h1l-1 1c0 2 4 4 5 5z" class="K"></path><path d="M335 432h1c0 2-1 3-2 5s-2 3-4 4h-1l-3-5c3-2 5-3 9-4z" class="H"></path><path d="M652 524l5 4-4 8c0 2-2 4-2 6-2-1-4-2-7-2 2-1 5-1 6-3 1-1 3-5 3-7 0-1-1-4-1-6z" class="I"></path><path d="M290 326c1 0 1 0 2 1v1c-1 3-3 5-6 7h0c-2-1-2-3-2-4 1-3 3-4 6-5zm81 199l1-1c1 1 2 2 2 4s-1 5-2 8c0 3 0 7-1 11 0 1 0 2-1 3-1-1-1-1-2-3 3-4 1-11 3-16l1-2c0-1 0-3-1-4z" class="M"></path><path d="M358 511l-1-2c1-2 4-4 6-4h1c1 2 1 4 0 6 0 1-3 4-4 4l-2-4z" class="L"></path><path d="M718 367h2c1 1 3 2 3 4h0-2 0l-1 1 1 1c0 2 0 3-1 5h-1c-2-2-2-4-3-5s-1-2-1-3c0 0 1-1 2-1 1-1 1-1 1-2z" class="I"></path><path d="M467 772c1-2 3-3 5-4h1c1 2 2 3 2 5-2 2-4 3-7 5-1-2-2-4-1-6z" class="L"></path><path d="M242 231h-2l-10-15c-1-2-1-3 0-5 1 0 1 0 3 1l-1 1v1c3 4 5 9 8 13 0 1 2 2 2 4z" class="H"></path><path d="M754 298l-1-1c0-2 1-4 2-6 2-6 3-11 7-16 1-2 1-4 2-5l1-1c0 2-2 6-2 8l-9 21z" class="I"></path><path d="M609 630c0-1 1-2 2-3l2 2 1-2 2 1 1 1-6 15-1-1c-1-3 1-6 2-9l-3-4z" class="E"></path><path d="M609 630c0-1 1-2 2-3l2 2 1 1c0 2-1 3-2 4l-3-4z" class="K"></path><path d="M324 411h1v1c-1 3-1 5-1 8 1 1 1 5 0 6-1-1-1 0-1-1-1-1-1-1-2-1s-1 0-2 1h-1v-1c1-1 0-3 0-4 1-3 5-5 6-9z" class="I"></path><path d="M358 511l2 4c0 4 3 9 5 13 0 2 1 4 2 5v3h-1c-3-1-7-14-8-17s-1-5 0-8z" class="D"></path><path d="M431 689c1-1 1-1 1-2 1-2 5-5 8-5h1c1 2-3 6-4 8s-2 3-3 4l-3-5zm-64-156c1-3 2-6 4-8 1 1 1 3 1 4l-1 2c-2 5 0 12-3 16-1-3-1-7-1-11v-3z" class="L"></path><path d="M281 308c2 0 2-1 3 0 0 4-4 8-6 11l-4-8c2-1 5-2 7-3z" class="H"></path><path d="M430 664h3c-1 5-4 8-7 11-1-2-3-4-3-6 2-3 4-4 7-5z" class="L"></path><path d="M501 859c2-2 4-4 7-4 1-1 2 0 3 1 0 2-1 3-2 4-1 2-3 4-5 5-2-2-2-3-3-6z" class="T"></path><path d="M450 728l1-1c3-2 6-2 8-2-1 3-4 9-8 10h-1s-1-2-2-2c0-2 1-3 2-5z" class="L"></path><path d="M638 575l-1 1c-1 2 0 1 0 1-1-1-1-1-1-2l-3-3c-1-2-1-3-1-5 1-1 1-2 2-2 2 1 4 1 6 2 1 3-1 6-2 8z" class="E"></path><path d="M485 814c2-2 4-3 7-4v8c-1 1-3 3-4 3h-1-2-1c0-2-1-3-1-4s1-2 2-3z" class="L"></path><path d="M616 603s1-1 2-1c1 1 1 1 2 1h2v1c1 0 2 1 3 1l-1 1-1 4-2 4v1c-2-3-3-6-5-9-1-1-1-1-1-2l1-1z" class="B"></path><path d="M417 636c1 0 2-1 3 0l1 1c0 4-5 8-7 10h0c-2-2-2-4-2-6 1-3 3-4 5-5z" class="M"></path><path d="M385 555h3c-1 5-3 8-6 11 0 1 0 0-1 0l-3-7c2-2 4-3 7-4z" class="H"></path><path d="M329 206c0-2 2-4 2-6 0-3-4-6-6-9-1-1-1-1 0-2s1-1 2 0c3 1 5 2 6 4l-2 2h0l3 9c-1 1-2 2-4 3l-1-1z" class="B"></path><path d="M309 396h0l5 10h1 0c1 2 0 4 1 7 1 0 1 2 1 3-1 1-1 2-1 3h-1l-1-2-1-2v-1l-1-2v-1l-4-15h1z" class="E"></path><path d="M242 231c0-2-2-3-2-4-3-4-5-9-8-13v-1l1-1c5 3 8 9 10 15l1 6h0l-2-2z" class="P"></path><path d="M652 524l-3-3c0-1 0-2 1-3s3 0 5 0c1 0 5 1 6 3v2h-3c0 1 0 1-1 2v3l-5-4z" class="G"></path><path d="M655 518c1 0 5 1 6 3v2h-3c-2-1-4-3-6-5h3z" class="C"></path><path d="M668 503c-1 0-2 3-3 4l-9-9 3-1h5c2 1 4 2 5 3 0 1 0 1-1 3z" class="G"></path><path d="M664 497c2 1 4 2 5 3 0 1 0 1-1 3l-1-1-2-1c-1-1-3-2-4-3l3-1z" class="C"></path><path d="M737 340l-1-2c1-1 1 0 1-1-1 0-2 0-3-1v-2c-1-3-2-4-4-6-1-1-2-2-2-3s1-1 3-2v1l6 5c1 2 3 4 2 7l-2 4z" class="I"></path><path d="M690 449v-4c1-1 2-4 2-5 0-2-7-6-8-8 1-1 2-1 3-2 3 1 6 1 9 3l1 2s0 1-1 2h-2c-1 2-1 4-2 5 0 3-1 5-2 7z" class="B"></path><path d="M687 430c3 1 6 1 9 3l1 2s0 1-1 2c-3-1-7-4-9-7z" class="C"></path><path d="M533 828l-3 4c-2-2-3-3-5-4s-4-2-5-3c1-2 2-2 3-2v-1c2-1 6 0 7 0 2 1 3 2 4 3v2l-1 1z" class="B"></path><path d="M533 828c-3-2-6-3-9-5h1l5-1c2 1 3 2 4 3v2l-1 1z" class="C"></path><path d="M646 554h0c-1-5-4-7-6-11v-3l1-1h2l1 1c3 0 5 1 7 2v2l-2 2c-1 2-2 5-3 8z" class="B"></path><path d="M643 539l1 1c3 0 5 1 7 2v2l-2 2c-2-2-5-4-7-6l1-1z" class="C"></path><path d="M545 796h-1c-2-5-4-8-8-12 3-1 5-2 8-2 3 1 4 2 6 4v1 1h-3l-2 8z" class="B"></path><path d="M544 782c3 1 4 2 6 4v1 1h-3c-2-2-4-3-5-5l2-1z" class="C"></path><path d="M554 773h0c-1-4-4-6-6-9-1-1-2-2-1-3s1-1 3-1c3 0 5 0 8 1v1l1 1c0 1 0 2-1 3-2 1-3 5-4 7z" class="B"></path><path d="M550 760c3 0 5 0 8 1v1l1 1c0 1 0 2-1 3-2-1-3-2-5-3-1-2-2-2-3-3z" class="C"></path><path d="M537 817l-5-7c-2-2-4-3-4-5 3-2 6-2 9-1h0c2 1 3 1 4 4-1 1-1 1-2 1-1 2-1 3-1 5l-1 3z" class="B"></path><path d="M535 806l4 3c-1 2-1 3-1 5-2-1-4-4-5-6l2-2z" class="G"></path><path d="M537 804h0c2 1 3 1 4 4-1 1-1 1-2 1l-4-3-2-2h4z" class="C"></path><path d="M793 207c-1 1-3 3-4 3-2 0-3 0-4-1 0-3 1-4 2-7 1-1 3-2 4-2l1 1 1-1c2-1 4-2 6-2l1 1-1 2c-1 2-3 4-6 6z" class="L"></path><path d="M793 207l-1-2c0-1 2-3 3-4s2-1 4 0c-1 2-3 4-6 6z" class="U"></path><path d="M260 259h1s1 1 1 2c2 2 1 5 2 8 0 6 0 13-2 19-2-4-2-8-3-12-1-5 0-12 1-17z" class="L"></path><path d="M317 382l3-1c0 3-2 5-3 7a30.44 30.44 0 0 1-8 8v-1c-1-3-3-5-5-8 5-3 8-4 13-5z" class="H"></path><path d="M313 385h3c-2 0-3 2-4 4h0c0 1-1 1-2 2h-1-1c-1-1-1-1-1-3l6-3z" class="M"></path><defs><linearGradient id="l" x1="335.962" y1="450.67" x2="343.984" y2="454.886" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#959393"></stop></linearGradient></defs><path fill="url(#l)" d="M348 447h1c-1 6-7 12-10 17 0-1-1-2-1-2-1-2-4-9-4-10 5-3 9-4 14-5z"></path><path d="M658 523h3c-1 8-6 15-9 23l-11 26c-2 4-3 9-5 13v-2-1c0-2 2-4 2-7 1-2 3-5 2-8 0-1 0-1 1-2 0-1 1-1 2-2 0 0 0-1 1-2s1-2 1-4l1-3c1-3 2-6 3-8l2-2v-2c0-2 2-4 2-6l4-8v-3c1-1 1-1 1-2zm48-108h0l-19 48c-2 5-4 12-7 16l-1 1-2-3c1-1 2-2 2-3 1-2 2-3 2-5 1-1 1-2 1-3 1-2 2-5 4-7 1 0 1-1 2-2 0-3 1-5 2-8 1-2 2-4 2-7 1-1 1-3 2-5h2c1-1 1-2 1-2l-1-2c0-1 0-1 1-2h2c2-2 3-8 4-11 1-2 1-4 3-5zm59-146l2-1c1-1 0-1 0-2l3-8h0c0 1 0 2-1 3 0 1-1 2-1 3v1 1 1l1-1s-1 0 0-1c0-1 1-1 1-2 1-1 1-2 1-3l2-3v-1l1-1c0-1 0-2 1-3l3-6v-1c0-1 1-2 1-3s2-3 2-4c1-2 0-1 1-2 1-2 1-4 2-6l1-2h1 0l-17 38-9 25-5 10c-2 3-3 6-4 9-2 4-3 8-5 12-1-3 1-6 2-8 0-3 0-5 1-7 1 0 1 0 2-1l1-1-1-1c2-1 2-4 3-6l9-21c0-2 2-6 2-8z" class="D"></path><path d="M266 199c-3-1-6-3-8-4v-4c1 1 1 1 2 1 4 1 9 4 14 6s11 4 16 7h1 0c3 2 5 6 7 9h0c-4-3-10-5-15-8-2 0-4-1-5-2-4-1-8-3-12-5z" class="N"></path><path d="M266 199l1-1 4 2c0 1 0 0 1 1h1s1 1 2 1l1-1c2 1 4 2 5 3h-1l1 1s1 0 2 1h0c-2 0-4-1-5-2-4-1-8-3-12-5z" class="J"></path><path d="M584 707c-1 3-2 4-2 6-2 5-3 10-6 14-3 3-4 8-5 13-1 2-3 4-4 7l-12 30c-1 2-2 9-5 10v-1c1-4 3-8 4-13 1-2 2-6 4-7 1-1 1-2 1-3l-1-1 1-2c2-3 3-6 4-9s1-5 4-6c0-1 0-2 1-2l-1-2c0-3 3-4 4-7v-1c0-1-1-2-1-3l2-1h0c2 0 2-1 2-2v-1-2c1-1 2-1 2-2 1-4 3-7 4-10l1-4 3-1z" class="D"></path><defs><linearGradient id="m" x1="389.212" y1="576.769" x2="399.987" y2="587.454" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#m)" d="M394 598h-1v-1c-2-5-5-10-6-15-1-1-1-2-1-3 3-3 11-8 14-7v2l-4 16c-1 2-2 5-2 8z"></path><defs><linearGradient id="n" x1="267.437" y1="168.042" x2="252.838" y2="182.267" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#b1b1b0"></stop></linearGradient></defs><path fill="url(#n)" d="M232 164c5-1 12 2 17 4 15 6 30 12 44 20l-1 1c-2 0-3-1-5-1v5c-2-2-3-2-5-1-14-13-34-19-50-28z"></path><path d="M438 710c-1-1-1-1-1-2l-1-1c-1-3-3-7-4-11v-1c-1-2-2-3-1-4v-1-2 1l3 5c0 1 0 1 1 2 1 3 3 8 4 12h0l5 12v1c-1-1-1-1-1-2l-2-4v-1c0-1-1-2-2-3 0 2 1 3 2 5 0 1 1 3 1 4 1 4 3 7 5 10v1h1c0-1 0-2 1-3h1c-1 2-2 3-2 5 1 0 2 2 2 2l1 1 1 1c2 3 2 7 4 10 0 2 1 4 2 5v2c3 5 4 13 8 17l1 1c-1 2 0 4 1 6 1 3 2 6 4 9 1 3 5 7 4 10h0c3 5 3 12 7 15l2 2c-1 1-2 2-2 3v1c-4-4-6-11-8-17l-8-20-7-14-9-23c-1-3-2-7-3-10s-3-6-5-9l-5-15z" class="D"></path><defs><linearGradient id="o" x1="309.774" y1="197.131" x2="300.888" y2="205.335" xlink:href="#B"><stop offset="0" stop-color="#686666"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#o)" d="M293 188c8 3 17 9 23 15 2 2 4 3 5 6v5h-1l-3-1c-10-3-19-5-25-14l-2-4h-1l-2-2v-5c2 0 3 1 5 1l1-1z"></path><path d="M290 195c5 0 23 12 27 17v1c-10-3-19-5-25-14l-2-4z" class="C"></path><path d="M417 680c2 2 2 3 3 5 0 1 1 2 1 4 1 2 2 3 2 6l1 2c1 0 1 1 1 1v1c1 1 1 1 1 2 1 0 1 1 1 2l1 2v1c1 1 0 0 1 2h0l1 1c0 2 1 5 2 6v1l1 1h0v1c1 1 1 3 2 4v1l1 1v2s1 1 1 2c0 0 0 1 1 2v1c1 0 1 1 1 2s1 2 2 3v1 1c1 1 1 1 1 3 1 0 1 1 1 2v1h1v1c0 1 0 0 1 2v1l1 1v1 1l1 1h0 1 1 0 5 0l-1-1v-2l-1-2-1-1c0-2-1-3-1-4l-1-2v-1c0-1-1-2-1-3-1-3-4-6-5-9 0-2-1-4-2-5 0-2-1-3-1-4-1-2-1-3-2-4l-1-3 1-1 5 15c2 3 4 6 5 9s2 7 3 10l9 23 7 14 8 20c2 6 4 13 8 17v-1c0 1 1 2 1 4h1 2 1c-1 3 0 4 0 6l4 10-1 2c1 2 1 4 3 6h0c2 4 4 11 7 14 1 3 1 4 3 6l4 11 2 4 2-5 1-4 6-12c1-1 2-2 3-4v1l11-25v1l-3 9c0 1-1 3-1 4v2c1 2 1 3 3 4 1 0 1 0 2-2v-1l1-1c0-1 0-2 1-3 0-1 0-1 1-2v-2l1-2 1-1h0c-2 9-6 17-10 26l-19 49c-1-2-2-4-2-6l-4-10-30-75-37-91-12-28c-3-7-6-14-8-22z" class="F"></path><path d="M485 821h2 1c-1 3 0 4 0 6l4 10-1 2c-3-4-7-13-6-18zm37 35l-7 19c-2 5-3 10-6 14l-8-24c-3-6-6-12-7-18-1-1 0-1 0-2 2 4 4 11 7 14 1 3 1 4 3 6l4 11 2 4 2-5 1-4 6-12c1-1 2-2 3-4v1z" class="D"></path><path d="M788 173h0c3-3 7-5 10-7 1-1 3-3 5-4h5v2c-2 2-6 5-8 7-12 10-25 20-39 29-1 0 0 0-1 1-1 0-1 0-2 1h-1l-6 6c0 1 0 2-1 3h1c3-1 4-3 6-5 5-4 11-7 16-10h0c-1 1-1 1-2 1l-2 2h-1l-8 5-6 6h2c1-1 1-1 2-1h1l1 1v-1l1 1 1-1v1l1-1 1 1h1v1h1l1 1v1c1 0 1 1 1 2h0v2 1c0 1 0 1-1 2h0c0 1 0 2-1 3h0c0 1-1 1-1 2h-1c0 1 0 2-1 3s-1 2-1 3l1 1v1h0v1l1 1v1h0c1 1 1 1 0 2v4c0 1 0 0-1 1l-2 7c0 2-1 4-2 6-1-5-1-10-2-16l-2-3h0c-2 1-3 1-4 2l-1 1-1-1-4-1h-3l-4 4v1c-2 1-3 3-4 5h-2c-1 2-1 4-2 5-1 0-2-1-2-1-3-2-5-8-5-11-2-10 1-18 7-27h-1c2-5 4-10 7-14v-2c0-1 0-1 1-2s3-2 5-3c4-2 10-6 15-6h0c2-1 2-1 3-3l23-13c1-1 1-1 2-1v3 1 1l3-2z" class="F"></path><path d="M732 248c2-5 5-9 9-13 3 1 5 2 8 4l-4-1h-3l-4 4v1c-2 1-3 3-4 5h-2z" class="N"></path><path d="M746 220v-2c0-2 0-3 2-4 2 0 3 0 5 1 1 0 1 1 2 2 5 8 8 16 8 26l-2 7c0 2-1 4-2 6-1-5-1-10-2-16l-2-3c-2-6-8-10-9-17z" class="L"></path><path d="M755 217c5 8 8 16 8 26l-2 7v-1c2-9-1-17-4-25-1-2-3-4-2-7z" class="P"></path><path d="M746 220c0-1 0-2 1-3 2 1 3 1 4 3 6 5 6 12 6 19v1l-2-3c-2-6-8-10-9-17z" class="U"></path><path d="M757 187h0c2-1 2-1 3-3l23-13c1-1 1-1 2-1v3 1 1l3-2 1 1c-7 6-15 11-23 16-4 3-9 7-14 10-8 5-16 8-22 14h-1c2-5 4-10 7-14v-2c0-1 0-1 1-2s3-2 5-3c4-2 10-6 15-6z" class="D"></path><path d="M736 198c0-1 0-1 1-2s3-2 5-3c4-2 10-6 15-6-3 3-6 5-10 7s-8 4-11 6v-2z" class="S"></path><path d="M421 162h17l-28 10c-19 6-36 14-46 32-7 14-8 30-7 45-3 2-7 4-11 6 1 2 3 7 3 8v4l-1-1-2-2-1-2c-5-5-18-10-25-10l-2-2c0 1 0 0 1 1h-2l3 6c-3 1-8 2-11 1l-1-1c1-1 2-3 4-4h0c-1-1-4 0-6-1h1c1-1 1-1 1-2h0c-3-2-11-2-14-1v1c1 1 1 2 1 4h0l2 5h-1v3c1 0 1 1 1 2l1 2v4c-2-6-4-9-9-14l-16-15 1 7v-1h-1c-1 0-3-4-4-4l-1-1c0-1-1-2-1-3l-3-5v-2h0-1-1v-1h0-1l-1-1h-1c-1-1-3-2-4-3h0c-1 0-1 0-2-1v-1h-1l-1-1-1-1v-1h0l-1-1v-1-1-1-2l1-4 1-1v-2c1-1 0-1 1-2 0 0 0-1 1-1v-1h0c1-1 1-1 1-2h1c-1-1-1-1-2-1v-1l-6-2c-1-1-2-1-3-2-1 0-2-1-3-2h0v-1c10 7 23 12 35 17l30 14c4 2 11 4 15 7l3 1 4 1c1 0 1 0 2 1l-1 1c1 1 2 2 4 3h1 2 1 3c3 0 9-1 12 1h0 1v-1c1 1 2 1 2 1l1 1v-5-1-2h0c1-2 0-4 1-7v-2h0v-1-1c1-3 1 0 1-2v-1-1s0-1 1-1v-1-2l1-1v-1c0-1 0-1 1-2l-1-2-1-1v-4c-1-2-1-5-1-6h1v-2l1-1c-1 0-1 1-2 1v1c-1 1-2 1-2 2-2 5-5 9-7 13l-1 5c-1 2-1 3-1 4s0 1-1 2v4c-1 1-1 1-1 2v2l-2 2h-1-1c1-1 2-1 3-2 0 0 1-3 1-4 0-3 1-6 2-9 2-5 3-11 6-15 8-14 22-24 37-29 12-4 26-7 38-10h-6l-1-1z" class="F"></path><path d="M312 254h1c2 0 2 1 4 2v1h-2c-1 0-2 1-2 0-2 0-2 0-3-1l2-2z" class="D"></path><path d="M283 240c1 0 2 1 3 1h1c3 4 5 9 8 13l2 5h-1c-1-4-5-8-7-11l-6-8z" class="R"></path><path d="M289 248c2 3 6 7 7 11v3c1 0 1 1 1 2l1 2v4c-2-6-4-9-9-14h1c1-2-1-6-1-8z" class="J"></path><path d="M283 240l-1-1c-3-3-8-9-13-10-1 0-2 0-2 1-1 0-1 1-1 2h-1c-1-2-2-3-1-5 1-1 3-1 5-1 7 3 14 9 18 15h-1c-1 0-2-1-3-1z" class="C"></path><path d="M298 228h1c4 0 6 3 9 6 2 2 7 9 7 13 0-1-1-2-2-2-1-1-2-1-2-2-3-2-13-11-13-15zm23 4l3 1c3 2 6 5 9 8 0 1 3 3 3 5-1 1 0 1 0 3-4-1-7-6-10-9l-7-6v-1l2-1z" class="P"></path><defs><linearGradient id="p" x1="673.071" y1="607.929" x2="594.526" y2="587.695" xlink:href="#B"><stop offset="0" stop-color="#7a7c7c"></stop><stop offset="1" stop-color="#a7a3a3"></stop></linearGradient></defs><path fill="url(#p)" d="M736 349c0-1 1-1 2-2 1 0 1-1 2-1 0 2-2 4-1 6 0 1 0 2-1 3 0 1 0 1-1 3h0v1c-2 5-4 11-6 16l1 1-1 1c1 1 2 1 3 1-2 0-3 1-4 1h-1c-2 5-12 27-11 30l1 1-2 1v-1c-3 4-5 10-7 15l-11 25-36 88-16 37-4 9-5 12-29 71-15 35c-3 7-6 12-8 19v1h4l-1 1h-3-1c-2 4-4 9-6 14l-9 23-24 60c-1 3-6 16-7 17h0l-1 1-1 2v2c-1 1-1 1-1 2-1 1-1 2-1 3l-1 1v1c-1 2-1 2-2 2-2-1-2-2-3-4v-2c0-1 1-3 1-4l3-9v-1c1-2 1-3 1-4v-2h0c0-2 0-3 1-5 1-1 1-2 2-3h0l1-3c0-2 0-3 1-5 1 0 1 0 2-1v-1c0-3 3-7 4-11l2-8h3v-1c3-1 4-8 5-10l12-30c1-3 3-5 4-7 1-5 2-10 5-13 3-4 4-9 6-14 0-2 1-3 2-6 0 0 0-1 1-1 1-1 1-3 2-3 2-5 3-11 5-16 2-2 3-5 4-7 2-4 2-8 4-11l2-4c-1-2 0-3 0-6l3-7v-4l1-1h2 1c1 0 1-2 2-3l6-15 10-25 6-14 3-5c2-4 3-9 5-13l11-26c3-8 8-15 9-23 2-2 2-4 3-6l5-12c1-3 3-6 4-9 3-6 4-11 7-17 3-4 5-11 7-16l19-48c2-2 4-5 4-8v-3l2-7c0-2 1-4 2-5l1-1 1 1 1-2c3-6 5-12 8-18 1-3 3-6 4-9 2-5 3-10 6-14h1z"></path><path d="M609 648c0 3 0 5-1 7l-1 3c-1 2-5 10-7 11h0l2-4c3-5 5-11 7-17z" class="J"></path><path d="M605 652v-4l1-1h2 1v1c-2 6-4 12-7 17-1-2 0-3 0-6l3-7zm-58 136h3c0 2-1 4-2 6-1 3-2 7-4 10-1 2-2 4-3 7l-6 15h0l-1-1c0-2 0-3 1-5 1-1 1-2 2-3h0l1-3c0-2 0-3 1-5 1 0 1 0 2-1v-1c0-3 3-7 4-11l2-8z" class="D"></path><path d="M735 349h1l-3 7h1c0 5-3 11-5 15l-3 6c-2 4-3 9-6 11h-1l-2 2c3-6 5-12 8-18 1-3 3-6 4-9 2-5 3-10 6-14z" class="J"></path><path d="M715 391l1 1c0 1-1 2-1 3 0 2 0 3-1 5v1s1-1 1-2l1 1h-1l-3 8c0 1 0 2-1 2 0 1-1 1-2 2l-2 5v1 2s-1 1-1 2v1c-1 1-1 1-1 2l-1 4-1 1c0 1-1 2-1 3l-2 1c-1 2-3 5-3 8h0l-6 17c-1 0-1 0-1-1-1 2-2 4-3 5l19-48c2-2 4-5 4-8v-3l2-7c0-2 1-4 2-5l1-1z" class="N"></path><path d="M715 391l1 1c0 1-1 2-1 3l-5 12v-3l2-7c0-2 1-4 2-5l1-1z" class="D"></path><path d="M732 248h2c1-2 2-4 4-5v-1l4-4h3l4 1 1 1c1 4-1 11-3 15-1 6-5 12-7 17l-16 36-57 135-62 147-31 71-38 91-25 59c-6-11-10-23-14-34l-39-91-121-290-50-118-9-21c-1-3-3-6-4-9l-1-7 16 15c5 5 7 8 9 14 4 9 8 19 13 29l24 57 60 144 19 46 72 170 16 39 6 14c1 2 1 4 2 5 0 1 0 2 1 2 4-6 6-15 9-22l26-60 57-135 2-5v-4l1-4c2-7 6-15 9-23l16-38 32-75 21-50 34-77 12-30c1-1 1-3 2-5z" class="C"></path><path d="M732 248h2c1-2 2-4 4-5v-1l4-4h3l4 1 1 1c1 4-1 11-3 15l-2-12c-4 3-6 7-9 11-3 3-6 6-8 10l-9 21v1l-2 2 1-2v-3l12-30c1-1 1-3 2-5z" class="Q"></path><path d="M718 283v3l-1 2 2-2v-1L605 554v-4l1-4c2-7 6-15 9-23l16-38 32-75 21-50 34-77z" class="J"></path><path d="M295 254h0c0-2 0-3-1-4v-1c3-1 11-1 14 1h0c0 1 0 1-1 2h-1c2 1 5 0 6 1h0c-2 1-3 3-4 4l1 1c3 1 8 0 11-1l-3-6h2c-1-1-1 0-1-1l2 2c7 0 20 5 25 10l1 2 2 2 1 1v-4c0 2 1 3 1 4 3 3 4 6 5 9l8 17c0 1 3 6 3 8l9 21c0 2 2 5 2 7l12 27c2 6 5 12 7 18 1 1 1 2 1 3l1 2c0 1 0 1 1 2v2c1 0 1 1 1 2 1 1 2 2 2 4 1 1 2 4 2 6l1 1c1 0 1 1 1 1 0 1 0 2 1 2v2c1 1 2 2 2 4l2 4v1h0c1 1 1 2 1 3 1 1 1 2 2 4l1 2 1 2c1 3 2 5 3 8 1 2 2 4 2 6 4 8 8 17 11 26 2 4 4 9 5 13 5 11 9 23 15 33l18 48v2c1 4 3 9 4 14 1 3 2 8 3 12v1c-1-1-2-2-3-2 0 1 0 1 1 2 1 0 2 1 2 3v1h1c0 1 0 2 1 3s1 0 1 2c0 1 1 2 1 3-1 3-1 6-1 9 1 3 2 7 4 9l3 6c-1 2-1 2 0 3 0 2 1 4 1 6h0 1c1-1 1-3 2-4 1 0 2 0 3 1v4c2 1 3 1 5 1l2-1h1 1c0 1 0 2-1 3h19c2-2 2-3 2-5l-3-2h5 11l1-4h-1c1-3 2-5 1-8 0-1 1-3 2-4l-2-1c1-1 1-2 1-3l1-1 1-2-1-2c1-4 2-8 4-11v-1c2-3 1-8 2-12l3-6 8-16c0-4-2-3-4-5l1-3 1-2c5 3 5 5 8 9l2-5c1-1 1-1 2-1 2-4 3-8 5-12l10-24c2 2 4 4 7 5 1 1 2 1 4 0 1-1 2-2 2-3s0-2 1-3c3 4 3 11 5 16 1 5 5 13 4 19l-1 3h1l1-3h1l-1 4v4l-2 5-57 135-26 60c-3 7-5 16-9 22-1 0-1-1-1-2-1-1-1-3-2-5l-6-14-16-39-72-170-19-46-60-144-24-57c-5-10-9-20-13-29v-4l-1-2c0-1 0-2-1-2v-3h1l-2-5z" class="O"></path><path d="M379 367h2 4l1-1h1v1l-2 1-3 3-1-2c0-1-1-1-2-2z" class="I"></path><path d="M324 311l-1-1v-3c0-3 0-3 2-5 0 3 0 5 1 7-1 1-2 1-2 2z" class="E"></path><path d="M360 304c2-1 2-1 4 0 1 1 2 1 2 3l-1-1-1 1c-1 1 0 1-2 1v-1c-1-1-2-2-2-3z" class="D"></path><path d="M382 371l3-3 1 1c0 1-1 4-2 5l-3 2v-1l1-4zm-3-4c1 1 2 1 2 2 0 2-1 3-2 4h-1c-1 0-1 0-2 1h0l-1 1c0-1 1-2 2-3 0-1 0-2 1-2 0-1 0-1 1-2v-1z" class="B"></path><path d="M409 443c-1 0-3 1-4 1 0-2 1-3 2-4h5c1 0 2 1 3 1l-1 1h1v1c-2-1-4-1-6 0z" class="D"></path><path d="M338 265c-2-2-2-2-4-3h1c1-1 1-1 2 0 1-1 2-1 3-1 2 1 4 2 5 4v1l-1-1c-2-1-4-1-6 0z" class="E"></path><path d="M423 533c1-2 2-3 3-4 3 0 5 0 8 2l-1 2c-2-1-3-2-5-1-1 0-2 0-3 1s-1 2-1 3c0-2 0-2-1-3z" class="J"></path><path d="M362 389c1-1 2-3 5-2 2 0 4 1 5 2l1 2v1c-1-1-3-3-5-3h-1c-1 0-2 1-3 1s-2-1-2-1z" class="Q"></path><path d="M596 548l2 2 1 2c0 1-1 3-2 4 0 1-1 2-2 3v-1c-1 1-2 1-2 1h-1l1-1c3-3 3-6 3-10zM381 369l1 2-1 4v1l3-2c-1 1-2 3-4 3h-6v-1l1-1 1-1h0c1-1 1-1 2-1h1c1-1 2-2 2-4z" class="E"></path><path d="M441 492c-1 2-2 5-4 6-2 2-5 3-7 2-1 0-2 0-3-1 2 0 2-1 4-1h2l1-1 5-3h0c0-1 1-2 2-2z" class="K"></path><path d="M364 359l1-2c1-1 2-2 4-2s3 0 5 1v2c0 1 0 1 1 1l-1 1c-1-1-2-1-3-2-1 1-2 1-3 1h0c-1-1-2 1-3 1l-1-1zm-1 6l1-2c2 2 2 3 5 4h1c1 0 2-1 4-1l2 1-3 3c-2 1-4 0-6-1s-3-2-4-4z" class="G"></path><path d="M518 741c0 1 0 2-1 4v1c-2 2-4 2-6 2-1 0-3 0-5-1l-1-6c0 1 1 1 1 2 2 1 3 2 5 2 3 0 6-3 7-4z" class="I"></path><path d="M366 309c2-1-1-5 0-7v-1l9 21c0 2 2 5 2 7v1l-1-3-3-7-3-6c0-2-1-3-2-4h-2v-1z" class="E"></path><path d="M393 447l-3 3c-2-1-6-1-8-3s-4-7-4-9l1-1 1 1c1-1 1-2 1-4h0c1 2 1 4 1 5-1 2-1 3 0 5s2 3 4 3c2 1 5 1 7 0z" class="I"></path><path d="M340 261c2 0 3 1 4 2l2 3 2 2 1-1h0 0v-4c0 2 1 3 1 4 0 2 1 8 0 10-1 1-3 3-3 4h-1c0-1 0-3 1-4l1-1-1-1v-1c1 0 1-1 1-2 0-2-1-3-2-4l-1-2v-1c-1-2-3-3-5-4z" class="B"></path><path d="M420 515c-1-2-1-3-1-4-1-3 0-6 0-9 0-7 0-13 5-19 0 3-1 5-2 8-1 1-1 3-1 5s-2 4-1 6c0 1 1 2 1 2h0c0 3 0 6-1 9v2z" class="E"></path><path d="M374 356c2 2 3 3 3 6 0 1 0 3-1 5l-2-1c-2 0-3 1-4 1h-1c-3-1-3-2-5-4l-1 2v-1c0-2 0-3 1-5l1 1c0 2 0 4 2 5 1 1 2 1 4 1l3-3v-3l1-1c-1 0-1 0-1-1v-2z" class="B"></path><path d="M439 580h1c1 2 2 2 4 2 3-1 5-1 7-4v-2-3c0-2 0-3 2-4 0 1 0 3 1 5 0 2-1 4-2 6-2 2-5 3-9 3h-1-1c-1-1-2-1-3-2s-1-2-1-3l2 2z" class="I"></path><path d="M365 360c1 0 2-2 3-1h0c1 0 2 0 3-1 1 1 2 1 3 2v3l-3 3c-2 0-3 0-4-1-2-1-2-3-2-5z" class="C"></path><path d="M399 398v-1c-1-1-1-1-2-1l-1-1h-1c-1 1-1 0-2 0 0 0-1 1-1 2-2 0-4 0-5-1-1 0-2-1-2-2 1-2 1-2 3-2 1-1 3-1 4-1h1c1 1 1 1 2 1h1c0 1 0 1 1 1 0 1 1 1 1 2h1v4-1z" class="D"></path><path d="M490 653h-1c-3 3-6 3-9 3-2-2-4-4-5-7v-1c0-2 0-5 1-6s1-1 2-1c0 2-1 4 0 6l1 2h1 0v2h0c0 1 0 2 1 2 3 1 6 0 9 0z" class="B"></path><path d="M543 648h0 4c4 0 4-2 6-4l1-1v1l1-1c0 3-1 5-3 7s-4 3-6 3l-6-3c-1-1-1-3-2-4 2 0 3 1 4 2h1z" class="F"></path><path d="M382 432c2-2 4-3 7-3 2-1 4 0 6 1 2 3 2 5 3 9l-2 1v-2c0-2 0-3-1-4 0-1-1-2-3-3 0 1-1 1-1 2-2 1-3 1-5 1s-2 0-4-2z" class="J"></path><path d="M320 257l-3-6h2c-1-1-1 0-1-1l2 2c0 1 0 1-1 2l2 2v-1l1-1c1 0 1 0 2 1s2 1 4 1v1h-4l-2-2v1c3 3 3 5 2 9l-1 3c0 2-3 5-4 6-2 1-5 1-7 0-1 0-2-1-2-2 1 1 3 1 5 1 3 0 4-1 6-3 2-3 2-4 1-7v-3c-1-1-2-2-2-3zm206 432l1-1c1 2 3 4 3 6l1 2c1 2 1 5 0 6v1c0 4-1 7-3 10v1l-3 3c0-1 2-2 1-3-2 1-2 2-4 3h-2c3-2 5-4 7-6 2-5 3-10 2-15v-1l-3-6z" class="B"></path><path d="M581 547c0-1 1-2 0-3v-1s1-1 2-1c3-1 4-1 7 0 2 1 4 3 5 5v2l-1 1-5-2c-1 0-1 0-2-1v-2l-2-1c-1 1-3 3-4 3z" class="G"></path><path d="M590 542c2 1 4 3 5 5v2l-1 1-5-2c-1 0-1 0-2-1v-2c1 0 2 1 3 2h1l1-2c-1-2-1-1-3-2l1-1z" class="Q"></path><path d="M489 639h1c2 2 4 3 4 6s-2 5-4 8c-3 0-6 1-9 0-1 0-1-1-1-2h0v-2 1c2 0 4 1 7 0 1 0 2-2 3-3 1-3 0-6-1-8zm-93-201v2l2-1c-1 1-1 2-1 4 0 1-1 2-2 3l-1 1h-1c-2 1-5 1-7 0-2 0-3-1-4-3s-1-3 0-5c1 2 2 3 3 3 2 1 5 2 7 1s3-3 4-5zm-86-166c-2-1-3-3-4-5 0-3 0-6 1-8 0 2 1 3 1 4 1 2 1 5 4 6 2 0 5 0 6-1 2 0 3-2 4-4v-1c1 3 1 4-1 7-2 2-3 3-6 3-2 0-4 0-5-1z" class="G"></path><path d="M431 483h2l1 2c-1 1-3 2-4 3-1 3-1 6-1 9-1 1-1 1-2 1h-1v-2h-1c0 1-1 2-1 4h-1c-1 6-1 15 0 21v3h-1c-2-2-1-5-2-8v-1-2c1-3 1-6 1-9l1-2 1-4c1-3 2-6 4-8 1-3 3-5 4-7z" class="H"></path><path d="M377 330v-1l12 27c2 6 5 12 7 18-1-1-2-2-2-3-1-2-1-3-2-4-1 1-1 1-1 2-1 5-2 7-5 11l-1-1 2-1v-2h1c1-2 1-3 1-4 0 1-1 2-3 3h0c1-1 1-2 2-3s0-2 0-3l1-1-1-1h-1v-1l1-1c1-7-2-11-4-17l-7-18z" class="B"></path><path d="M570 621c-1 2-2 3-3 5-3 0-5 0-8-1-2-2-5-4-5-7-1-4 0-7 3-10 0 0 0 1 1 1v1c-1 0-1 0-2 2 0 1 0 3 1 5 0 1 2 3 3 3 2 1 4 0 7 0 1 0 2 1 3 1z" class="F"></path><path d="M411 479h1c2 2 2 5 2 8 0 1-1 4-2 5-1 2-4 3-6 3s-4-2-6-3c-2-2-3-5-3-8 1-3 2-5 5-8v1c0 1-1 1-1 3h0l-2 2c0 2 0 3 1 5 0 1 2 2 4 3 1 0 4 0 6-1 1 0 2-2 2-3 1-2 1-4 0-6h-1v-1z" class="K"></path><path d="M478 641l1-1 1 1h1c1 0 3 0 4-1h1 1 0l-1-2h0c1 0 1 1 2 1h1c1 2 2 5 1 8-1 1-2 3-3 3-3 1-5 0-7 0v-1h0-1l-1-2c-1-2 0-4 0-6z" class="C"></path><defs><linearGradient id="q" x1="588.367" y1="563.781" x2="577.827" y2="549.306" xlink:href="#B"><stop offset="0" stop-color="#a7a4a4"></stop><stop offset="1" stop-color="#c5c5c5"></stop></linearGradient></defs><path fill="url(#q)" d="M595 559c0 1-1 2-2 3-2 2-7 3-10 3-2 0-3-2-4-3-3-3-5-7-4-11 0-1 0-2 1-3s1-1 1-2h1c1 1 2 1 3 1-1 0-1 1-2 0h-1v1c0 4-1 7 2 9 2 2 4 3 7 3 1 0 4-1 5-1h1s1 0 2-1v1z"></path><path d="M438 484h1c1 3 2 5 2 8-1 0-2 1-2 2h0l-5 3-1 1h-2c-2 0-2 1-4 1v-1c1 0 1 0 2-1 0-3 0-6 1-9 1-1 3-2 4-3 2 1 3 0 4-1z" class="J"></path><path d="M401 480h1 1 1l1 1c1 0 2-1 3-1l-1-1 1-1c1 1 0 1 1 1l-1-2c2 0 2 1 3 2v1h1c1 2 1 4 0 6 0 1-1 3-2 3-2 1-5 1-6 1-2-1-4-2-4-3-1-2-1-3-1-5l2-2zm-19-48c2 2 2 2 4 2s3 0 5-1c0-1 1-1 1-2 2 1 3 2 3 3 1 1 1 2 1 4-1 2-2 4-4 5s-5 0-7-1c-1 0-2-1-3-3 0-1 0-3-1-5l1-2z" class="C"></path><path d="M435 473c-3-2-3-4-4-6l-5-15h0v-1c-1-1-1-2-1-3l-1-1v-1l-1 1c-1-4-3-7-5-11s-3-9-4-12c-1-2-2-4-2-5v-2-4c1 1 1 2 2 4l1 2 1 2c1 3 2 5 3 8 1 2 2 4 2 6h0l4 10 2 6c0 1 1 2 1 3l3 7 1 2v2c1 1 1 0 1 2 1 2 2 4 2 6z" class="I"></path><path d="M320 257c0 1 1 2 2 3v3 1c-1 2-2 4-4 4-1 1-4 1-6 1-3-1-3-4-4-6 0-1-1-2-1-4l1-2 1 1c3 1 8 0 11-1z" class="C"></path><path d="M320 257c0 1 1 2 2 3-4 0-8 0-11-1-1 1-2 1-3 2v2c0-1-1-2-1-4l1-2 1 1c3 1 8 0 11-1z" class="U"></path><path d="M399 395c1 2 2 5 2 7 0 1-1 2-1 2v-2h0c-2 2-3 4-5 5h0c-2 1-5 1-7 1-1 0-3-2-4-3-1-2-1-4 0-6 1-1 1-1 2-1h13v1-4z" class="C"></path><path d="M553 581v-1c1-1 2-2 2-4h0l2-1 3 2c3 2 6 4 7 7l1 1v1-1c0 1 1 2 1 2 0 7 1 14-1 20l-1-1 1-2c0-3 1-15-1-17l-3 3c-1-1-3-1-5-2-2-2-4-4-5-7h-1z" class="L"></path><path d="M553 581v-1c1-1 2-2 2-4h0l2-1 3 2-2 2-1-1h-2v1c0 1 0 1-1 2h-1z" class="H"></path><path d="M434 531c2 2 3 3 3 6 0 2-1 4-3 5-1 2-3 3-6 3-1 0-3-1-4-2-1-3-2-7-1-9v-1c1 1 1 1 1 3 0-1 0-2 1-3s2-1 3-1c2-1 3 0 5 1l1-2z" class="F"></path><path d="M424 536c0-1 0-2 1-3s2-1 3-1c2-1 3 0 5 1 1 2 1 3 0 5-1 1-2 2-4 2-1 0-3-1-3-1-1-1-1-2-2-3h0zm32 83v-5c1-1 3-3 5-3 2-1 5 0 7 1 2 3 3 4 3 7-1 2-2 4-4 5-1 1-4 1-6 1-2-1-4-3-5-6z" class="C"></path><path d="M456 619v-5c1-1 3-3 5-3 2-1 5 0 7 1v2c-1 1 0-1-1 1h-1l-2-1h0c-1-1-3-1-4 0-1 0-1 1-2 1l-2 4z" class="K"></path><path d="M542 638l2-3c1-1 2 0 3 0s1 0 2 1l1-1c2 2 3 3 3 5l1 3-1 1c-2 2-2 4-6 4h-4 0-1c-1-1-2-2-4-2v-1c-2-2-1-5 0-8h2l1-1v2h1z" class="C"></path><path d="M542 638l2-3c1-1 2 0 3 0s1 0 2 1c0 1 0 1-1 3-2 0-4 0-6-1z" class="E"></path><path d="M538 646v-1c-2-2-1-5 0-8h2c-1 2-1 5 0 7s2 3 3 4h-1c-1-1-2-2-4-2z" class="I"></path><path d="M361 398c-1-1-1-2-1-3-1-2 0-4 2-6 0 0 1 1 2 1s2-1 3-1h1c2 0 4 2 5 3v-1c2 2 2 2 2 5v1 1c0 1-2 5-3 5-2 2-5 2-7 1-1 0-1-1-2-1v-1c-1-1-2-3-2-4z" class="B"></path><path d="M373 391c2 2 2 2 2 5v1 1c0 1-2 5-3 5-2 2-5 2-7 1-1 0-1-1-2-1v-1c-1-1-2-3-2-4h0l1 1c2 1 3 3 6 3 2 0 3 0 5-2 0-1 1-3 1-4l-3 2c-1 1-3 1-4 1h-1l-3-3v-1c1 2 2 2 4 3 2 0 3-1 5-2 1-1 1-2 1-4v-1z" class="I"></path><path d="M364 390c1 0 2-1 3-1h1c2 0 4 2 5 3 0 2 0 3-1 4-2 1-3 2-5 2-2-1-3-1-4-3 0-2 0-3 1-5zm217 157c1 0 3-2 4-3l2 1v2c1 1 1 1 2 1l5 2 1-1v-2l1 1c0 4 0 7-3 10l-1 1c-1 0-4 1-5 1-3 0-5-1-7-3-3-2-2-5-2-9v-1h1c1 1 1 0 2 0z" class="C"></path><path d="M581 547c1 0 3-2 4-3l2 1v2c1 1 1 1 2 1l-2 1c-2 1-2 1-3 2l-2-1h0c-1 0-2-1-3-1v-1h-1v-1h1c1 1 1 0 2 0z" class="N"></path><path d="M557 608c1-1 1-2 3-2h0c1-1 2-1 4-1l3 1 1 1c2 2 3 5 4 8 0 2 0 4-2 6-1 0-2-1-3-1-3 0-5 1-7 0-1 0-3-2-3-3-1-2-1-4-1-5 1-2 1-2 2-2v-1c-1 0-1-1-1-1z" class="C"></path><path d="M567 620c0-1 2-2 2-3 1-1 1-3 0-5v-2c1 1 2 3 3 5h0c0 2 0 4-2 6-1 0-2-1-3-1z" class="B"></path><path d="M557 608c1-1 1-2 3-2h0c1-1 2-1 4-1l2 2c-1 1-1 2-2 3v-1h-1l1 1c-1 1-2 1-3 1v-1h1-4v-1c-1 0-1-1-1-1z" class="I"></path><path d="M506 730c2-1 3-2 5-2s4 0 6 2 2 4 2 7v3l-1 1c-1 1-4 4-7 4-2 0-3-1-5-2 0-1-1-1-1-2h0v-1c-1-3-1-6 0-9l1-1z" class="K"></path><path d="M506 730c1 2 0 2 2 3 2 0 3 0 5-2l1-1c1 1 3 2 3 4 1 1 1 2 0 4 0 1-2 2-3 3-3 0-5 0-7-1-2-2-2-5-2-7v-2l1-1z" class="C"></path><path d="M409 443c2-1 4-1 6 0 0 1 1 2 2 3v2c1 0 1 0 2 1 0 2-1 3-1 5-1 0-1 1-1 1v1c-2 1-3 2-4 3-3 1-5 0-7-1-1-1-2-2-2-3l-1-1v-3c-1-2-1-5 1-7 2 2 3 1 5-1z" class="B"></path><path d="M411 446c1 0 1 1 2 1l1-1 2 2v4c-1 1-2 2-4 3h-3c-1 0-2-1-3-2-1-2-1-4-1-6l1 1c1-1 1-1 2-1s2-1 3-1z" class="C"></path><path d="M435 473c0-2-1-4-2-6 0-2 0-1-1-2v-2l-1-2-3-7c0-1-1-2-1-3l-2-6-4-10h0c4 8 8 17 11 26 2 4 4 9 5 13 0 2 2 7 3 9l-1 1h-1c-1 1-2 2-4 1l-1-2h-2c-1 2-3 4-4 7-2 2-3 5-4 8l-1 4-1 2h0s-1-1-1-2c-1-2 1-4 1-6s0-4 1-5c1-3 2-5 2-8 3-4 6-7 11-10z" class="F"></path><path d="M431 483h0c1-1 1-2 3-3 1-1 1-2 2-3h1v1 2c1 2 1 3 1 4-1 1-2 2-4 1l-1-2h-2z" class="E"></path><path d="M441 565l1-1c1 0 2 0 3 1h2 1 1 1v1h1l2 3c-2 1-2 2-2 4v3 2c-2 3-4 3-7 4-2 0-3 0-4-2h-1l-2-2-1-1v-3c-1-2 0-5 2-7 1-1 2-1 3-2z" class="C"></path><path d="M437 578l-1-1v-3c-1-2 0-5 2-7 0 3 0 6 2 8 1 1 3 2 4 2s2-1 3-1h0 1 1l1 1-3 3h-1 0c-1 1-2 1-4 1v-1h-3l-2-2z" class="B"></path><path d="M441 565l1-1c1 0 2 0 3 1h2 1 1 1v1h1l2 3c-2 1-2 2-2 4v3 2c-2 3-4 3-7 4-2 0-3 0-4-2h-1 3v1c2 0 3 0 4-1h0 1l3-3-1-1h-1-1c1-1 1-2 2-3 0-2 0-3-1-5h0l-1 1h-3c-1 0-1-1-1-1-1 0-2 2-4 1 0-2 0-2 2-4z" class="E"></path><path d="M563 555c3 2 5 3 6 6 4 9 1 18 0 26 0 0-1-1-1-2v1-1l-1-1c-1-3-4-5-7-7l-3-2v-2h1c1-3 2-5 3-8 0-3 1-7 2-10z" class="H"></path><path d="M562 565v-4h1c1 4 2 8 2 13h-2l-1-9z" class="M"></path><path d="M561 565h1l1 9v1l-5-2 3-8z" class="E"></path><path d="M557 575v-2h1l5 2v-1h2c1 3 3 6 3 10v1h0v1-1l-1-1c-1-3-4-5-7-7l-3-2z" class="C"></path><path d="M325 301c0-1 1-2 2-3 2-1 3-1 5-1 3 1 5 2 7 4 2 3 1 8 1 11-2 2-3 3-6 4h-1c-1 1-3 1-4 0-3-1-4-3-5-5 0-1 1-1 2-2-1-2-1-4-1-7v-1z" class="F"></path><path d="M325 301l2 1c1-1 1-1 2-1l1 1s2 1 3 0c0-1 1-1 2-2 1 1 3 2 3 4s0 4-1 5c-1 2-3 3-5 3s-4-1-5-2l-1-1c-1-2-1-4-1-7v-1z" class="C"></path><path d="M542 606h0l3-7c1 4-1 5-1 8-1 2-1 4-2 6v4 6 6h2c1 1 2 1 3 1v1h-1-1c-3 1-5 2-7 3-3 0-9-1-11 1-1 1-1 1-1 2s0 0-1 1h0l1-6c-2-1-3 0-5 1 2-2 2-3 2-5l-3-2h5 11l1-4h-1c1-3 2-5 1-8 0-1 1-3 2-4l-2-1c1-1 1-2 1-3l1-1 1 2 1-1h1 0z" class="E"></path><path d="M537 609c1-1 1-2 1-3l1-1 1 2 1-1h1 0l-3 9-2 7h-1c1-3 2-5 1-8 0-1 1-3 2-4l-2-1z" class="P"></path><path d="M539 615c0 1 0 2 1 3 0 2-1 8-2 10-2 3-8 3-12 4h0c-2-1-3 0-5 1 2-2 2-3 2-5l-3-2h5 11l1-4 2-7z" class="H"></path><path d="M525 626h11l-1 1c-2 1-6 3-9 2l-1-3z" class="M"></path><path d="M357 357c-1 1-3 2-4 3-2 0-4 0-6-1-3-2-5-4-5-8-1-3 0-7 2-10 2-1 3-2 5-2s4 1 6 2l3 3c1 2 1 3 1 5 0 3-1 5-2 8z" class="B"></path><path d="M359 349c0 3-1 5-2 8-3 1-5 1-8 1-2-1-3-1-4-3l1-1 2 1c2 1 3 1 5 0 3-1 4-3 6-5v-1z" class="K"></path><path d="M355 341l3 3v2c0 3-1 5-3 7-1 1-4 1-6 1s-4-2-5-3c0-1-1-3 0-5 0-3 2-2 5-3h0v2c1 0 2 0 3-1v-1h-1l-1-1c2-1 3 0 4 1h1v-2h0z" class="C"></path><path d="M338 265c2-1 4-1 6 0l1 1 1 2c1 1 2 2 2 4 0 1 0 2-1 2v1l1 1-1 1c-1 1-1 3-1 4-3 2-4 3-7 3s-5-1-7-3-3-6-3-9l2-1v-2c1-2 4-3 7-4z" class="G"></path><path d="M346 268c1 1 2 2 2 4 0 1 0 2-1 2v1-1l-1-1-2 1c0 1 1 1 1 3-1 1-1 2-3 2-2 1-4 1-7 0-1-1-2-2-3-4 0-1-1-3 0-5h1c3 1 9 0 11-1 1 0 1-1 2-1z" class="C"></path><path d="M367 313c0 1 0 2-1 4 0 1-1 1-1 2v1c-2 2-4 4-7 4-3 1-6 0-8-2-2-1-3-3-3-6h0v-5c2-3 2-5 4-7 3-2 5-1 9-1v1c0 1 1 2 2 3v1c2 0 1 0 2-1l1-1 1 2v1 1l1 3z" class="G"></path><path d="M367 313c-1 2-4 5-6 6s-5 2-7 1-4-2-4-4c-2-3-2-5-1-7l1 1c2 0 5 0 6-1 1 0 2 0 3-1 1 1 2 1 3 2s1 1 2 1c1-1 1-2 2-3v1 1l1 3z" class="C"></path><path d="M522 637c0-1 0-1 2-2 0 1 0 2 1 3h0l-2 6v3h5c2 1 3 1 5 3 1 2 1 4 1 6 1-1 1-3 1-5h0c2 2 1 6 0 8 0 2-2 5-4 6-3 1-7 1-10 3-2 1-3 4-4 6l-2 4c-1 0-1 0-2 1l3-13c0-3 2-6 2-10 0-3 2-6 3-9v-1-2c1-2 1-6 1-7z" class="G"></path><path d="M520 653c0 3 0 6 2 7 1 1 2 2 4 2-1 0-2 1-2 1h-4c0 1 0 2-1 2-1-1 0-6 0-7 0-2 0-3 1-5z" class="J"></path><path d="M528 647c2 1 3 1 5 3 1 2 1 4 1 6v1c-2 2-2 4-5 5h-3c-2 0-3-1-4-2-2-1-2-4-2-7 1-2 2-3 3-5l5-1z" class="C"></path><path d="M465 545l5 12c1 4 3 9 4 14 1 3 2 8 3 12v1c-1-1-2-2-3-2 0 1 0 1 1 2 1 0 2 1 2 3v1h1c0 1 0 2 1 3s1 0 1 2c0 1 1 2 1 3-1 3-1 6-1 9 1 3 2 7 4 9l3 6c-1 2-1 2 0 3 0 2 1 4 1 6-4-4-4-10-6-14-2-5-4-13-9-15-2 0-5-1-6-2-2-1-3-3-3-5v-1-4c1-3 2-4 5-5h0c1-2 2-3 2-5 0-3 1-6 0-9 0-8-5-16-6-24z" class="B"></path><path d="M478 588c0 1 0 2-1 2 0 2-2 4-4 4-1 1-3 1-5 0-1-1-2-3-2-4 0-2 0-3 2-4h1l1-1v1 1h1 1 1l1-1c1 0 2 1 3 2h1z" class="C"></path><path d="M554 545c5 3 5 5 8 9l2-5c1-1 1-1 2-1l-3 7c-1 3-2 7-2 10-1 3-2 5-3 8h-1v2l-2 1h0c0 2-1 3-2 4v1l-2 5-4 10c-1 1-2 2-2 3l-3 7h0 0-1l-1 1-1-2 1-2-1-2c1-4 2-8 4-11v-1c2-3 1-8 2-12l3-6 8-16c0-4-2-3-4-5l1-3 1-2z" class="M"></path><path d="M554 545c5 3 5 5 8 9l-2 7-2-1c-1-1 0-6 0-8-1-2-3-3-5-5l1-2z" class="H"></path><path d="M543 589l1-1c1 0 1 0 2-1 1 0 2 0 3-1v1c-1 2-2 5-3 7-1 3-3 8-5 11-1-1-1-1-1-2l-1-2c1-4 2-8 4-11v-1z" class="B"></path><path d="M562 554l2-5c1-1 1-1 2-1l-3 7c-1 3-2 7-2 10-1 3-2 5-3 8h-1v2l-2 1h0c0 2-1 3-2 4v1l-2 5c0-4 0-8 2-11v-1c2-5 4-9 7-13l2-7z" class="L"></path><defs><linearGradient id="r" x1="554.834" y1="598.029" x2="594.701" y2="643.441" xlink:href="#B"><stop offset="0" stop-color="#908c86"></stop><stop offset="1" stop-color="#adaeb4"></stop></linearGradient></defs><path fill="url(#r)" d="M605 546h1l-1 4v4l-2 5-57 135v-3c0-1 1-2 1-2 0-1 0-2 1-2v-1l1-1-1-1-2 1v1l33-79c8-19 18-39 24-58h1l1-3z"></path><path d="M605 546h1l-1 4v4l-2 5h-1c0-3 1-7 2-10l1-3z" class="Q"></path><path d="M440 483c-1-2-3-7-3-9 5 11 9 23 15 33l18 48v2l-5-12-6-15v6c-2 2-4 6-6 7h-7c0 7 3 13 6 19 1 2 2 4 2 6 1 2 1 5 0 6-1-2-1-4-1-5l-2-3h-1v-1h-1-1v-1c-3-7-6-16-5-23 0-3 0-5 1-8 0-3 0-6 1-9s3-6 6-9h0v-4c-1 0-1-2-1-2-1-2-1-2 0-4h0l-10-22z" class="T"></path><path d="M451 515h0v-4c-1 0-1-2-1-2-1-2-1-2 0-4 3 6 4 14 6 21l-7 4 1-10 1-1v-4z" class="I"></path><path d="M451 515v4l-1 1c-2 2-1 3-2 6 0 1 0 2-1 4v2c-1 2-2 4-2 6-1 2-1 5 0 7v5l2 6c1 3 3 6 4 10h-1v-1h-1-1v-1c-3-7-6-16-5-23 0-3 0-5 1-8 0-3 0-6 1-9s3-6 6-9z" class="B"></path><path d="M446 542v-4-1c1 0 1-1 1-1v-1c1-2 3-3 4-4l4-3c1 0 2 0 2-1 1-1 0-1 1-2 1 1 1 3 1 5v6c-2 2-4 6-6 7h-7v-1z" class="H"></path><path d="M446 542c3-3 6-7 10-9 1 0 1-1 2-1l1 4c-2 2-4 6-6 7h-7v-1z" class="J"></path><defs><linearGradient id="s" x1="516.153" y1="640.283" x2="495.329" y2="669.346" xlink:href="#B"><stop offset="0" stop-color="#acabac"></stop><stop offset="1" stop-color="#d9d7d6"></stop></linearGradient></defs><path fill="url(#s)" d="M494 630c2 1 3 1 5 1l2-1h1 1c0 1 0 2-1 3h19c2-1 3-2 5-1l-1 6c-1-1-1-2-1-3-2 1-2 1-2 2s0 5-1 7v2 1c-1 3-3 6-3 9 0 4-2 7-2 10l-3 13c1-1 1-1 2-1v2h2c4 1 7 4 10 8h0l-1 1c-4-4-8-7-13-7-5-1-9 1-13 4-2 2-3 5-4 7h-1 0-1l1-2c0-1 1-1 1-2h1c0-2 1-3 3-4l1-1s1-1 2-1c1-1 2-1 3-2 1 0 2 0 3-1l-1-1c-4-7-4-17-6-25-1-3-3-6-4-10-1-2-2-5-2-8-1-2-1-4-2-6z"></path><path d="M494 630c2 1 3 1 5 1l2-1h1l-2 2c0 1 0 1-1 2-1 2-1 2-3 2-1-2-1-4-2-6z" class="K"></path><path d="M521 633c2-1 3-2 5-1l-1 6c-1-1-1-2-1-3-2 1-2 1-2 2s0 5-1 7v2 1c-1 3-3 6-3 9-1 3-2 5-4 8h-1c0 2-1 4-1 6-2-6-3-13-4-20-1-3-3-5-4-7-1-3-2-7-2-10h19z" class="C"></path><path d="M522 637c0 1 0 5-1 7v2 1c-1 3-3 6-3 9-1 3-2 5-4 8h-1l3-14c2-4 3-10 6-13z" class="B"></path><path d="M496 693c1-2 2-5 4-7 4-3 8-5 13-4 5 0 9 3 13 7l3 6v1c1 5 0 10-2 15-2 2-4 4-7 6-4 1-7 2-11 1-5-1-8-4-11-8-3-5-4-10-2-17z" class="C"></path><defs><linearGradient id="t" x1="401.414" y1="519.458" x2="407.271" y2="517.056" xlink:href="#B"><stop offset="0" stop-color="#8a8b8a"></stop><stop offset="1" stop-color="#afaaac"></stop></linearGradient></defs><path fill="url(#t)" d="M297 259l31 74 25 58 7 17 77 186 21 48 26 64 13 29c2 5 5 10 7 15 2 7 7 17 6 23v1c-1-1-1-3-2-5l-6-14-16-39-72-170-19-46-60-144-24-57c-5-10-9-20-13-29v-4l-1-2c0-1 0-2-1-2v-3h1z"></path></svg>