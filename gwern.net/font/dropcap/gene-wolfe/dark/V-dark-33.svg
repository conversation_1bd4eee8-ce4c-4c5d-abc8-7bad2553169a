<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="144 36 724 924"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#7f0c0c}.C{fill:#5b0808}.D{fill:#470606}.E{fill:#980f0e}.F{fill:#b11515}.G{fill:#cec4be}.H{fill:#e22624}.I{fill:#410707}.J{fill:#edece9}.K{fill:#e7e4e0}.L{fill:#d0c6be}.M{fill:#a51111}.N{fill:#8c0b0b}.O{fill:#b2a195}.P{fill:#660807}.Q{fill:#550606}.R{fill:#840e0d}.S{fill:#4d0606}.T{fill:#e92722}.U{fill:#6a0706}.V{fill:#710b0c}.W{fill:#d8d0ca}.X{fill:#dc221d}.Y{fill:#2e0303}.Z{fill:#d8d2cd}.a{fill:#470808}.b{fill:#c6b9b0}.c{fill:#e32421}.d{fill:#ddd7d1}.e{fill:#e7201d}.f{fill:#7a0c0b}.g{fill:#c0b2a8}.h{fill:#bcaba0}.i{fill:#e1ddd8}.j{fill:#c51311}.k{fill:#9b8678}.l{fill:#dd1d1b}.m{fill:#3b0404}.n{fill:#d0c3ba}.o{fill:#f0eeec}.p{fill:#a99689}.q{fill:#ba1615}.r{fill:#f35954}.s{fill:#1f2126}.t{fill:#b31313}.u{fill:#a41110}.v{fill:#340404}.w{fill:#2c2d33}.x{fill:#190f12}.y{fill:#1d1f24}.z{fill:#ac1817}.AA{fill:#8c1010}.AB{fill:#7d0e0e}.AC{fill:#f64b49}.AD{fill:#f93534}.AE{fill:#2d3036}.AF{fill:#f85e59}.AG{fill:#ef352f}.AH{fill:#ca1d1b}.AI{fill:#9d887d}.AJ{fill:#877065}.AK{fill:#260202}.AL{fill:#7b0c0d}.AM{fill:#f62a28}.AN{fill:#1a1b20}.AO{fill:#a89489}.AP{fill:#f4f3f2}.AQ{fill:#101114}.AR{fill:#c21d1e}.AS{fill:#f94540}.AT{fill:#9c8477}.AU{fill:#303237}.AV{fill:#f25a5a}.AW{fill:#c91817}.AX{fill:#2a0303}.AY{fill:#1e0202}.AZ{fill:#36383e}.Aa{fill:#08090b}.Ab{fill:#796157}.Ac{fill:#63676e}.Ad{fill:#755c51}.Ae{fill:#575b61}.Af{fill:#eb7b7b}.Ag{fill:#f57977}.Ah{fill:#7e8085}.Ai{fill:#44474d}.Aj{fill:#f78787}.Ak{fill:#eea7a6}.Al{fill:#ee9897}.Am{fill:#71757b}.An{fill:#e0b7b4}</style><path d="M527 918l-1-2c1-1 5-3 7-4-2 3-3 4-6 6z" class="AQ"></path><path d="M612 834l2 1-1 2h1v2l-1 1-2-1v-2c1-1 1-2 1-3z" class="AN"></path><path d="M415 687c1-1 2-2 4-3l2-2h1c0 1-3 5-4 6l-3-1z" class="s"></path><path d="M415 687l3 1-4 4-1-1h-2v-1c2-1 3-2 4-3z" class="w"></path><path d="M438 761v-1c0-2 1-3 2-5v-1c1-1 2-1 3-2-1 4-2 7-4 10l-1-1z" class="AN"></path><path d="M557 146h1v2l-3 8c0-1 0-2-1-4l3-6z" class="AJ"></path><path d="M361 639c0 1 0 1 1 2 2-1 4-3 5-5l1 1c-2 2-4 4-6 5l-1 1-1 1c-1 0 0 0-1-1v-1l2-3z" class="D"></path><path d="M598 838h2c-5 3-7 5-10 10l-1-1c0-2 1-3 2-3 2-2 5-4 6-6h1z" class="s"></path><path d="M630 849h0c3 1 8 2 10 4l-1 1h0-1c-2 0-7-2-9-3l1-2z" class="AE"></path><path d="M339 579h1l3 3 1 2-1 1c-2-1-5-3-8-3 2-1 2-1 4-1v-2z" class="e"></path><path d="M290 624c-3 1-7 0-10 0 3-2 6-2 9-4h1l-1 3h2l-1 1z" class="AF"></path><path d="M433 692c1 3 2 6 2 9 0 1 0 1-1 2l-1-1v-1c-1-3-2-6 0-9z" class="AN"></path><path d="M830 187c-1-1-2-3-2-4 0-3 0-3 2-5h1v2 6l-1 1z" class="L"></path><path d="M411 818l1 3c-2 3-3 6-3 10v-1c-1-1-2-3-2-4l1-4c1-1 2-3 3-4z" class="AE"></path><path d="M294 614v6l-3 3h-2l1-3c1-1 2-3 2-4h2v-2z" class="Ag"></path><path d="M421 760c1-1 1-2 1-3 1-1 1 0 1-2 0 0 0-1 1-1l1-1c0-1 0-2 1-3v1 2 3c-1 1-1 1-1 2v3l-1-1h0-3z" class="AE"></path><path d="M748 510c-3-3-6-7-10-10-2-2-4-3-6-6 1 0 2 1 3 2 2 2 7 7 10 8 2 1 3 3 3 6z" class="x"></path><path d="M789 143h1 1c1 1 2 0 4 0l1 1c-2 1-5 2-7 4-2 0-4 2-6 2 1-1 3-2 4-3 1-2 2-3 2-4z" class="k"></path><path d="M407 840c2 0 3 1 4 1h1c2 1 4 3 5 4 0 2 0 3 1 4v1 2l-2-4c-3-4-5-5-8-7l-1-1z" class="w"></path><path d="M455 799h0c-3 4-6 6-10 8l-1-1 2-2c2-1 3-4 6-5h0 3z" class="y"></path><path d="M561 139c1 1 1 1 2 1l-1 2 1 1-5 5v-2h-1v-4c1 0 3-2 4-3z" class="k"></path><path d="M662 651l1 1c2 1 3 1 5 1 4-1 8-4 11-6-3 4-7 7-11 9-1-1-2-1-3-1v-1h-1c-1-1-1-1-2-3h0z" class="AM"></path><path d="M478 879h-2c-2 0-5-1-7-3h-1c-1-1-2-1-3-2l1-1 1 1h1 1l3 1h0c1 0 2 0 3 1 1 0 1 1 2 1h1c1 1-1 1 1 1h1c-1 0-2 1-2 1z" class="x"></path><path d="M594 814c0 2-1 4-2 5l-9 1c-3 0-5 0-7-1l-3-3h1c3 2 7 3 11 3 5 0 6-2 9-5z" class="AZ"></path><path d="M568 864c2-4 7-9 11-11h1l1 1c-1 0-1 1-2 1-4 3-7 8-11 11v-2z" class="y"></path><path d="M500 923c1 0 2 0 3 1s2 2 2 3 1 2 1 2c0 1 0 3 1 4v2c0 1 1 4 0 5v2h0v1l-1 2h-1l1-4v-6-1c0-2-2-4-3-6v-1l-3-4z" class="AN"></path><path d="M162 345c-2 1-3 2-4 4v1l-2 2c-2-2-5-2-8-4h1l4-1c2 1 6-1 9-2z" class="AS"></path><path d="M631 846l15 3v1h-1v1c-4 0-9-1-13-3-1 0-1-1-1-2z" class="AE"></path><path d="M189 280c-3 1-6 2-9 2 3-2 6-4 11-5 3 2 7 0 11 2-4 0-9 0-13 1z" class="AF"></path><path d="M552 154l2-2c1 2 1 3 1 4-1 5 0 9 1 15l-2-2c0-2-1-4-1-5-1-3-1-7-1-10z" class="k"></path><path d="M367 612c1-1 2-1 3-2h1v1 2h-1c0 1-1 2-2 3h0l-1 1c1 0 1 0 2-1h1c1 1 1 1 1 2l1 1-1 1-1-2c-2 0-4 2-5 2v-1l2-5v-1-1z" class="v"></path><path d="M437 845h0c0 2 0 2 2 2l-1 1 2 1-1 1h-1-1-2c-3 4-5 8-7 13-1 2-2 5-4 7h0l8-18v-1c2-2 3-4 5-6z" class="AN"></path><path d="M470 173h2l-2 3v1c-2 1-5 2-8 2-2 0-3-1-4-2-1-2 0-2 0-3h1c2 0 2 2 4 3 3-1 5-2 7-4h0z" class="G"></path><path d="M372 599h1l15 13v1l-18-13c2 0 2 0 2-1z" class="M"></path><path d="M421 803h0c-3 4-6 8-9 13l-1 2c-1 1-2 3-3 4l-1-2v-2-1l1-1c1 0 1 0 2-1 4-4 7-9 11-12z" class="Ah"></path><path d="M658 659l1-1h0 2l3 4c0 3 1 5 1 8l1 1c0 3-1 5-2 8v-2c0-2 1-4 0-6 0-5-3-10-6-12z" class="X"></path><path d="M282 557h4c2 3 5 6 4 9 0 1-1 1-1 2l-1 1c-1-5-3-8-6-12z" class="q"></path><path d="M678 796l5-4c3-2 7-5 8-9 0-3-2-5-4-7 2 0 4 1 6 2v2c-2 8-8 13-14 17h-1v-1z" class="Ac"></path><path d="M666 882h1 1c1 0 1-1 2-1 2 1 2 1 3 2 0 3-3 7-4 10-1 1-2 2-3 2 1-2 2-3 2-5s1-4 1-6h-1l-2-2z" class="AU"></path><path d="M366 703h3c0 1-3 4-3 5-1 2-1 4-1 6l1 1h0 0l2 9c-2-3-4-6-5-10 1-1 0-3 0-4 0-2 1-3 1-4 1-1 2-1 2-2v-1z" class="s"></path><path d="M611 727v4c0 7 1 17 7 22v1l-1 1c-1 0-1 0-2 1-4-7-4-13-5-20 0-3 0-7 1-9z" class="AE"></path><path d="M619 787h0c6 3 10 2 15 2-2 2-3 2-4 5l-2 1-9-8h0z" class="Ae"></path><path d="M579 226c1-1 1-1 3-1 1 0 3 1 4 2s2 2 4 3l3 3v3l-3-4h-3c-2-3-5-4-8-6z" class="c"></path><path d="M586 227c1 1 2 2 4 3l3 3v3l-3-4h0c-2-1-3-2-5-3l1-2z" class="AS"></path><path d="M572 739c1-1 2-2 3-4v3c-1 1-1 1-1 3s-1 3-2 5h0c-1 3-1 5-1 8 1 1 1 2 0 4h0v1-1c-3-6-2-13 1-19z" class="AN"></path><path d="M462 218h3c1 1 5 4 6 5 1 2 2 3 3 4l1 1c1 2 2 7 1 9-1-1-1 0-1-1v-2c-1-4-3-7-7-9h0c-1-2-1-3-2-5-1 0-2-1-3-1l-1-1z" class="e"></path><path d="M395 687l6-3 7-7c-2 3-3 5-5 6l-2 2h1 0c0 1-1 2-1 2v1h0c1 1 1 1 2 1-1 1-1 2-3 2l-2 1h-2v-1c1-1 1-1 1-2h-3l1-2z" class="AU"></path><path d="M716 590v-1c1 2 2 3 3 5 4 5 4 9 4 15-1-1-1-3-2-4-1-5-3-8-6-12v-3h1z" class="n"></path><path d="M646 849h0c4 1 9 1 13 1h6c2 1 5 3 6 5 3 2 6 4 9 7h0l-13-9c-2-2-18-2-22-2v-1h1v-1z" class="Ae"></path><path d="M437 845l4-4c1 1 1 1 2 1l1-2c2 1 3 1 4 1h0c2 1 4 2 5 3 0 0-1 0-1 1h1l2 1h0-3 0c-1 0-2-1-2-1-2 0-3-1-4-1h-1c0-1 0 0-1-1h-2l-1 2c1 0 1 0 2 1h0c-3 0-3-1-6-1h0z" class="x"></path><path d="M750 783l6-3v3c0 2-1 3-1 5h-1-1c-3-1-5-1-8-1 0 0 1 0 1-1 1-1 2-1 3-2l1-1z" class="AU"></path><path d="M750 783l6-3v3h-1c-1 1-2 1-3 1l-1-1h-1z" class="y"></path><path d="M614 839c6 2 11 5 17 7 0 1 0 2 1 2-3-1-6-2-8-3l6 4-1 2-16-11 1-1z" class="AU"></path><path d="M338 875c1 0 2 5 3 7 3 1 6 0 9 0 1 2 3 5 3 6 1 4 3 6 6 9-3 0-8-8-10-10 0-1-1-3-1-3-2-1-5 0-7-2-2-1-3-5-3-7z" class="AZ"></path><path d="M860 351c2 4 0 12-1 16-2-2-2-8-3-10l1-2c0-1 0-2-1-2v-1l1-1h3z" class="AS"></path><path d="M788 258c4 0 6-1 9-4 0 4-1 7 0 11v1 1h0c-1-1-2-3-2-5 0-1 0 0-1-1l-2 1-2 1c-2 0-3-1-5-2h3c3 1-1 0 2 1l1-1v-1l-4-2h1z" class="e"></path><path d="M706 651h0l1 1c1 0 1-1 1-1 3 0 6-2 8-4h1c1 0 1 0 2 1-1 2-4 3-7 4-6 2-12 2-17 1 3-1 7-1 10-2h1 0z" class="T"></path><path d="M554 169l2 2h0c2 2 4 5 7 6 2 0 2 0 4-1 1-1 1-2 3-2 1 1 1 1 0 2-1 2-3 3-6 3s-7-2-9-5h-1c1-2 0-4 0-5z" class="G"></path><path d="M605 195h1c2 0 0 0 2-1h3v1 1c-1 1-3 4-4 5-3 0-3 1-5 1-1 1-2 0-3 0l4-4 2-3z" class="X"></path><path d="M605 195h1c2 0 0 0 2-1h3v1 1l-1-1c-1 1-2 1-3 2-1 0-3 1-4 1l2-3z" class="H"></path><path d="M853 352c1 1 2 3 3 5s1 8 3 10c0 4-1 8-2 12-1-2 0-5 0-7 0-5-2-9-4-13 1-3 1-4 0-7z" class="E"></path><path d="M411 691h2l1 1-1 1c-1 0-2 1-3 1-7 4-14 6-21 8h-2c-1 0-1-1-2-1l1-1 1 1v-1c6-1 12-2 16-4l1-1c3-1 5-2 7-4z" class="AE"></path><path d="M608 188h3l2-1 1 1c1-1 1-1 2-1-1 2-1 3-2 4-1 2-2 3-3 4v-1h-3c-2 1 0 1-2 1h-1v-1l3-6z" class="AR"></path><path d="M605 194c2-1 3-1 5-3h0 3 1c-1 2-2 3-3 4v-1h-3c-2 1 0 1-2 1h-1v-1z" class="c"></path><path d="M647 705v-2c2-1 4-3 7-4-2 2-3 4-3 6-1 3 0 17-2 18h0c0-6-1-13-2-18z" class="Ai"></path><path d="M389 817c4-2 11-3 16-4 1 1 3 2 4 2h1c-1 1-1 1-2 1l-1 1v1 2l-3-3c-2-1-6 0-8 0h-6-1z" class="Ac"></path><path d="M631 688c5-1 7-1 11 1 3-2 5-4 8-6-2 3-3 5-5 7l-1 2c2 1 4 3 6 3s4-2 6-3v1c-1 1-2 3-4 3h-4l-6-4c-3-4-6-4-11-4z" class="Ah"></path><path d="M164 349c-1 3-2 3-4 5v3l-1 1c-2 3-3 6-3 9v1c-1 4 0 9 1 13-2-4-5-12-4-16 2-2 3-4 4-6 1-4 4-7 7-10z" class="T"></path><path d="M646 864h4l1 1v1c1 1 3 2 4 3l-2 1c-3 1-5 1-8 3h0-1c2-2 4-3 6-4-2-4-10-2-14-3-2 0-4-1-6-2l4 1c2 0 10 1 12-1z" class="AE"></path><path d="M262 774l-2-1 1-1 1 1c4 2 8 5 12 6l13 7c1 0 4 1 5 2v1l-2-1h-1l-5-2v-1c-6-2-12-4-17-8-2-1-3-2-5-3z" class="AN"></path><path d="M616 187h2v1l1-1v1h0l-3 5-4 6-2 2-2 2s-1 0-1 1l-1-2 1-1c1-1 3-4 4-5v-1c1-1 2-2 3-4 1-1 1-2 2-4h0z" class="I"></path><path d="M319 782h0c6-2 8-5 13-8-2 3-6 7-5 11s6 7 10 9l1 1h-1c-4-2-8-5-11-9-1-1-1-3-3-4l-2 1-2-1z" class="Ae"></path><path d="M443 846l2-1h1c1 0 1 0 2 1h1 0 1l1 1h1l2 1 9 3h1v1c2 0 3 1 4 1h0l-2 1c1 0 2 1 4 1l-1 1-26-10h0z" class="AE"></path><path d="M226 434l1-1c2 4 2 6 1 10v1h2c0 1 0 2 1 3-1 1-1 2-1 3h-1c-1 2-3 3-5 4h0l1-1-1-1v1c-1 0-1 1-2 1 1-2 3-4 4-6 2-5 2-9 0-14z" class="Aj"></path><path d="M228 443v1h2c0 1 0 2 1 3-1 1-1 2-1 3h-1 0v-2l-1-1v-4z" class="Ag"></path><path d="M438 761l1 1c-4 10-10 18-16 27l-1-2c3-6 8-12 12-18 0-1 1-1 1-2l3-6z" class="s"></path><path d="M162 347v1l1-1c1-1 2-1 4-2 0 1-1 2-2 2h-1v1 1h0c-3 3-6 6-7 10-1 2-2 4-4 6 0-4 2-9 3-13l2-2c1 0 4-3 4-3z" class="Al"></path><path d="M811 483l2 2c2 2 3 5 4 8 3 8 4 16 3 25 0 0-1 1-1 2v1h0v-15h0c-1-7-4-15-8-20v-3z" class="AG"></path><path d="M480 878c2-1 5-1 7-1h1l3-3v-1l1 1c-1 1-1 2-2 3v1c-5 1-9 4-14 5-1 0-2 1-4 1 0-2 0-2-2-3l8-2s1-1 2-1z" class="s"></path><path d="M761 516l2 1c1-1 1-1 2-1l-1 1c2 6 5 10 9 15 3 5 6 11 9 16h-1c-1-1-2-2-2-4-1 0-1-1-2-2l-3-5c-1-3-3-6-5-8-1-2-2-3-4-5s-4-4-5-7l1-1zM409 722c3 5 5 10 7 15 1 3 1 7 1 10 1-1 2-1 3-2h0c-1 2-2 3-4 4l-1 1c-1 0-1 1-2 1l-2 2-1-1h0l5-4c0-7-2-14-6-21h1c0-2-1-3-1-4v-1z" class="AZ"></path><path d="M371 618l4-1v-1c1 0 1 0 2-1h4c0 1 1 2 1 3s-1 1-1 2h-1c-2 0-3 1-4 2h-1l-1 1s-1 1-2 1l-1 1c-1 1-1 1-2 1-1 1-1 1-2 1 0 1-1 1-1 2-1 2-3 3-5 5h0c-1 1-1 2-2 2v1l-1 1c0 1 0 2-1 3v-3c2-5 6-8 9-12h1s1 0 2-1l11-7h-2-3-1l-2 1-1-1z" class="AX"></path><path d="M773 170h2v1h-1c-4 2-7 4-9 8-2 5-2 12 1 17l3 5c-1 0-1 0-2 1-1-1-3-6-4-8v-1c-1-4-1-10 1-14 0-1 1-3 2-5 2-3 4-3 7-4z" class="S"></path><path d="M569 845c3-5 6-14 5-20-2-6-5-12-5-18h0v1c1 3 2 5 4 7l1 1h-1c-1 2 0 3 1 4 1 4 2 7 2 10v1c-1 1 0 3-1 4 0 2-1 3-1 4h0c-1 2-2 4-4 6h-1z" class="w"></path><path d="M433 702l1 1 3 15c0 1 1 5 0 6 0 1-2 3-2 3-2 2-3 4-4 6l-1 1c0 1 0 1-1 2h-1l1-1c1-2-1 0 1-2 0-1 1-2 2-3v-2c3-8 2-17 1-26z" class="AE"></path><path d="M393 811v-1c1-2 5-3 7-4 1 0 2 0 3-1h0 1c-1 1-2 1-3 2h0 2 0l1-1h1c1-1 2-1 2-2l3-1 1-1 1-1c2-1 3-1 4-2l2-2h1c1-1 5-5 5-7 2-2 5-5 7-6-1 3-3 5-5 7-7 8-18 16-28 19-2 0-3 1-5 1z" class="s"></path><path d="M369 700h18v1l-1-1-1 1c1 0 1 1 2 1h-7l-1 1h-1c-3 0-6-1-9 1-2 3-3 6-3 9v2h0l-1-1c0-2 0-4 1-6 0-1 3-4 3-5h-3c1-1 2-1 3-2v-1z" class="AZ"></path><path d="M417 845l2 2c1 0 2 2 2 4 1 1 1 3 1 5v-2c1 3 1 6 2 8-1 3-2 7-2 11-1 1-1-1-1 1 0 1-1 2-1 3v-2-2c1-4 2-10 1-14v-1l-3-6v-2-1c-1-1-1-2-1-4z" class="x"></path><path d="M417 845l2 2h0l2 5v1l-1-1c0-1-1-1-1-2h-1v-1c-1-1-1-2-1-4z" class="y"></path><path d="M659 653l1 1c1 0 1-2 2-3 1 2 1 2 2 3h1v1c1 0 2 0 3 1l-2 2v3c0 3 1 6 0 10l-1-1c0-3-1-5-1-8l-3-4h-2 0v-5z" class="AC"></path><path d="M659 653l1 1c1 0 1-2 2-3 1 2 1 2 2 3h1v1l-2 1 1 6-3-4h-2 0v-5z" class="T"></path><path d="M568 864v2c-2 3-6 8-6 12-1 1-2 3-2 4-1 3-2 6-2 9 1 1 2 1 3 2l-1 1v2c-3-1-4-4-7-4 0-1 1-1 1-1l6-13c2-5 5-9 8-14z" class="AE"></path><path d="M342 537h0 1c1-3 3-5 5-7l7-6h1c-5 6-10 12-14 19h-3-1l-1 1c0-2 3-5 5-7z" class="l"></path><path d="M290 781c10 2 19 3 29 1l2 1c-2 1-5 2-7 2h-1c-4 1-12 2-16 0h2c-1-1-1-1-2-1-3 0-5-1-7-3z" class="Ac"></path><path d="M350 652c1-1 2-1 4 0v5l-2 7v11c-1-3-2-7-3-10 0-2 1-6 0-8l-1-1h-3c1-1 4-2 5-4h0z" class="AF"></path><path d="M169 185l1 1c3 0 6 1 9 0 2-1 3-2 3-4s0-2 1-4c1 0 1 0 2 2 0 2 0 4-2 6s-4 3-7 3c-4 0-9 0-13-3h2c1 1 0 0 2 1-2-1-3-3-4-4 2 2 4 2 6 2z" class="o"></path><path d="M427 267c0-2-1-6 0-8 0-2 1-3 1-4 0-2 1-2 2-3l1-1c-1 8-1 17 1 25h0c-1 0-2 0-2-1-1-3-2-5-3-8z" class="AG"></path><path d="M204 351l14 12h0c4 6 11 10 11 17-3-3-6-12-10-13l-5-5c-4-4-8-7-14-10l4-1z" class="M"></path><path d="M245 175l1-1 2 4 3 9v1c0 4 0 6-1 10v2c1 2-3 7-4 9l-2 1h0c-1 1-1 1-2 1v-2l3-4c1-1 1-1 1-3h0c1-2 3-5 3-8 1-6-1-14-4-19z" class="D"></path><path d="M757 520c0-2 0-3-1-5-1-1-1-2-2-3l1-1c0 1 0 1 1 2 1 0 2 1 3 2 0 1 0 0 1 1v1c1 3 3 5 5 7s3 3 4 5c2 2 4 5 5 8h-1c-1 0-1-1-2-2l-1-1-4-5v-1c-1-1-2-1-2-2 0-2-1-2-2-3h-1l-1-1c-1 1-1 1-3 1 0-2 1-2 1-3v-1l-1 1z" class="x"></path><path d="M363 714c-1-4-2-10-4-13l-2-4v-1c2 0 3 1 4 2s3 1 4 0h1l1 1 3-1c1-1 1-1 2-1l-1 1-2 2h0v1c-1 1-2 1-3 2v1c0 1-1 1-2 2 0 1-1 2-1 4 0 1 1 3 0 4z" class="w"></path><path d="M852 175l1 2c-2 2-3 4-4 6h0 0c-2 2-3 3-6 4l1 1-2 1c-4 1-8 0-12-2l1-1v-6c1 2 2 3 3 5 3 1 6 1 8 0 5-1 7-6 10-10z" class="O"></path><path d="M767 202c1-1 1-1 2-1 4 6 9 10 17 11 7 0 13 0 20 5h0c-7-3-12-4-19-2h-1c-2-1-3-1-5-2h-1c-3-2-4-4-7-4-2-2-4-4-6-7z" class="E"></path><path d="M847 147c2 0 3 2 4 3l1 2c1 0 0 0 1-1h1c2 4 2 8 2 12v5l-1 1c0 3-1 5-2 8l-1-2c0-1 1-3 1-5 1-9-1-16-6-23z" class="AO"></path><path d="M533 912c3-2 5-3 7-5h0v1c-1 1-4 4-4 6-1 1 0 3 0 4l-2-2c-5 1-8 3-11 7-4 7-7 15-5 22v2c1 0 1 1 1 1-2 0-2-1-2-2-2-5-1-10 0-14v-2c1-4 7-9 10-12 3-2 4-3 6-6z" class="AU"></path><path d="M278 513c4 0 7 2 11 3 2 1 4 1 7 1-1 1-2 1-3 2v1c2 1 3 2 5 3h1c3 2 7 2 11 2l-2 1c-2 0-6 1-7 0-2-1-2-1-4-1-2-1-4-1-6-3v-1l-1 1v1l-2-1c0-1 0-2-2-2 0-2-1-3-3-4h0c-2-1-2-1-3-1s-2-1-2-2z" class="P"></path><path d="M508 312c3-6 5-13 7-19l3 14-1 3-1-2h-1v6l-2 1 1-10-3 6c-1 0-2 0-3 1z" class="AF"></path><path d="M334 587c0 1 1 2 2 2 3-1 5 0 8 0-1 1-1 1-3 1-1-1-1 0-2 0h-1-2l1 2c1 1 1 2 1 3 1 1 1 0 2 1s1 1 1 2c-1 1 0 1-1 1v2-1c1 0 2 0 3 1h1l4 2h-7c-2-1-4 0-5-1v-2h-1c-1-2-1-3-2-4 1-1 0-2 0-3s0-1 1-2h1c0-1-1-3-1-4z" class="D"></path><path d="M334 591h1v1c0 1 0 2 1 3h0c0 2 1 3 1 4v1h-1-1c-1-2-1-3-2-4 1-1 0-2 0-3s0-1 1-2z" class="a"></path><path d="M372 619l2-1h1 3 2l-11 7c-1 1-2 1-2 1h-1c-2 0-2 1-4 3l-1-1v-2c1-2 2-5 4-7v1c1 0 3-2 5-2l1 2 1-1z" class="I"></path><path d="M449 759v2c1 1-1 4-1 5-2 3-3 6-4 9l-8 14a30.44 30.44 0 0 1-8 8c-2 2-4 5-7 6h0c1-2 4-4 5-5 1-4 5-7 7-10 5-6 10-12 13-20l3-9z" class="AE"></path><path d="M230 417v1c0 2-4 6-5 8h-2l-1 1c1 1 3 2 5 3 1 1 2 7 3 9h1v-3-2l1-1v3h1l1-3h0v5h0c-1 2-2 4-4 6h-2v-1c1-4 1-6-1-10l-1 1c-3-4-6-6-10-8 6-2 9-2 13-8l1-1z" class="AF"></path><path d="M423 816c8-3 26-5 32-12l1 1c-8 8-16 13-27 15 1-1 1-2 2-2s2-1 3-1h1c1-1-1 0 1-1s4-1 6-2c-3 0-5-1-8 0-3 0-8 4-11 2z" class="s"></path><path d="M262 774c2 1 3 2 5 3 5 4 11 6 17 8v1l5 2h1l-3 1-9-4c-3-1-6-3-9-3 2 1 4 3 7 4v2c-3-1-5-4-8-6-2-1-4-1-6-2l-2 2h-1l1-3c1-1 1-2 0-2h0s0 1-1 1c0-1 0-2 1-3 0 1 0 0 1 1h1v-2z" class="y"></path><path d="M343 582c1 0 2 1 3 2 2 1 4 2 6 2l4 2 9 6 7 5c0 1 0 1-2 1l-5-3-22-12 1-1-1-2z" class="H"></path><path d="M365 594l7 5c0 1 0 1-2 1l-5-3 1-1 1 1s1 0 2 1v-1c-2-1-3-1-4-3h0z" class="t"></path><path d="M628 795l2-1c6 6 17 7 25 7s16-2 23-5v1c-3 2-6 3-8 4-11 2-23 4-33 0l-9-6z" class="Ah"></path><path d="M183 346h9c4 0 8 3 12 5l-4 1c-10-4-21-5-31-1v-1l2-2c2-1 4-2 6-2h6z" class="N"></path><path d="M368 565h2c1 1 2 2 3 4l2 2c1 1 0 1 1 2 0 1 1 1 2 2 1 0 1 2 2 3 0 0 1 1 1 2l1 1c0-1 0-2 1-2l1-1 1 2-2 2h1l-2 1h-1v2c0 1-1 0 0 1s0 1 1 1c-2-1-3-3-5-5-2-3-5-6-7-9 1-1 1-2 1-3-1-2-2-3-3-4v-1z" class="Q"></path><path d="M586 777c-1-2-3-4-3-6 1 1 2 4 4 6 3 6 8 10 13 14 1 1 3 3 5 4 1 1 3 2 5 3l3 3h-1c-6-3-13-7-17-12l-4 1c-2-2-4-7-6-10l-1-3h2z" class="Ah"></path><path d="M586 777l9 12-4 1c-2-2-4-7-6-10l-1-3h2z" class="s"></path><path d="M338 795c13 5 26 4 39 3-1 1-2 1-3 2l1 1h0c-6 1-13 1-19 0-4 0-7-1-10-1h-1c-2-3-5-4-8-5h1z" class="Ac"></path><path d="M242 544c1-1 2-3 3-4l4-6h2l-10 14c-7 12-12 26-16 40h0c0-2 1-4 1-5v-1l1-1 1-3v-1-1c-1 1-1 2-1 3l-2 6-1-1c5-14 10-28 18-40zm348 310c0 2 1 5-1 7v1 1c3-3 4-8 5-12v-2l3-3c2 5 1 12 2 18 1 3 0 7 2 11h0v1h-1c0-2-1-3-1-5-1-3-1-7-1-11l-2-13c-1 5-3 11-6 16l-1 2c-1 0-6-2-7-1-3 0-5 1-7 2-1 1-1 1-3 1 5-3 9-5 14-4h1c1-1 2-2 3-4l-1-4c-1-3-3-6-7-7v-1-1l1 1h3s0 1 1 2c0 2 1 3 3 5z" class="AN"></path><path d="M423 816c3 2 8-2 11-2 3-1 5 0 8 0-2 1-4 1-6 2s0 0-1 1h-1c-1 0-2 1-3 1s-1 1-2 2c-1 0-1 1-2 1l-3 1-9 3h-2l1-1 2-2c0-1-1 0 0-1 1-2 4-4 6-5h1z" class="y"></path><path d="M424 195l-3-6c-1-2-2-4-2-7 5 10 8 21 19 25l3 1c-2 1-5 1-8 1-1-1-1-1-3-2h-1l-2-2c-2-2-4-4-5-7-1 0-2-3-3-4h4l1 1z" class="l"></path><path d="M419 194h4l1 1s1 1 1 2h0c1 1 1 1 0 2l-2-1h-1c-1 0-2-3-3-4z" class="T"></path><path d="M422 198h1l2 1v1h1c1 2 2 4 3 5l1 1-1 1-2-2c-2-2-4-4-5-7z" class="c"></path><path d="M208 476c2 0 4-2 5-4h2c-3 4-6 7-8 11h-1c-9 11-12 22-11 37h-1v-3c-3-12 3-27 10-37h1c1-1 2-3 3-4z" class="r"></path><path d="M358 641c0-1 0-1 1-2 0-1 1-1 2-1 0-2 0-1 1-2l2-2v-1l2-2s0-1 1-1l2-2c1 1 1 1 2 1l1-1s1-1 2-1c1-4-2 2 0-2h1c1-1 1-1 2-1h1c1 0 3 0 5 1h-1-2c-1 1-3 1-4 1v1 3c-1 1-5 4-6 5l-2 2h0l-1-1c-1 2-3 4-5 5-1-1-1-1-1-2l-2 3-1-1z" class="v"></path><path d="M361 639c3-4 9-8 14-11-2 4-5 6-8 8-1 2-3 4-5 5-1-1-1-1-1-2z" class="Q"></path><path d="M724 620c-3 6-1 12-3 19-1 3-2 6-2 9-1-1-1-1-2-1h-1c-2 2-5 4-8 4 0 0 0 1-1 1l-1-1h0l2-1 4-2c3-2 5-7 6-10 2-4 1-9 1-12v-4c1-1 1-1 3-1 0 0 1-1 2-1z" class="AC"></path><path d="M374 512l10 33c0 1-2 2-2 3v1c-1 1-1 2-1 3 0 2-2 6 0 9h0c1 2 3 4 3 6h-1l-11-8 1-2c1 3 3 3 5 4v1-1-3c1-1 0-3 1-5v-1-2-1c1-1 1-2 1-3v-2h0c1-2 2-2 2-3l-1-1v-1-1-1c-1-1-1 0-1-1v-2l-1-1c0-1 0-3-1-4h0c0-1 0-2-1-3h0v-1c-1-1-1-2-1-3s-1-1-1-2v-1l-1-3-1-3 1-1z" class="AK"></path><path d="M609 184c0-4-1-9-3-12v-2-1l1 1c2 2 3 2 6 3-1 0-1 0-2 1h1c2 0 1 0 2 1v1l-1 2h1v2l1 1v1 3h1c1-1 2-2 2-3 1-1 1-2 2-3h0l-4 8h0c-1 0-1 0-2 1l-1-1-2 1h-3l1-4z" class="E"></path><path d="M607 170c2 2 3 2 6 3-1 0-1 0-2 1h1c2 0 1 0 2 1v1l-1 2c0 1 0 1-1 2-1-4-4-5-5-10z" class="Q"></path><path d="M618 182c1-1 1-2 2-3h0l-4 8h0c-1 0-1 0-2 1l-1-1-2 1h-3l1-4v-1-4h1c1 1 1 2 1 3s0 2 1 2v1l3-3v3h1c1-1 2-2 2-3z" class="z"></path><path d="M827 109c4 1 8 3 11 4 8 4 14 11 18 19l1 1-1 1-3-3c-8-11-17-16-30-20 2 0 3 0 4-2z" class="o"></path><path d="M377 798c7-1 13-3 19-7 9-7 15-18 17-30 1-2 1-5 1-7s1-2 2-3c0 2-1 5-1 8-2 9-5 19-11 27-3 3-6 6-10 8l-2 1c-6 3-11 5-17 6h0l-1-1c1-1 2-1 3-2z" class="Am"></path><path d="M292 624l3-2h0 0c-1 2-1 4-1 6 1 3 0 8 1 11v1c1 1 2 3 2 4h1c1 3 5 6 8 7h4c2 1 4 1 7 2h0c-2 1-4 1-5 1-5 0-10-1-14-5-7-6-6-16-6-25z" class="AS"></path><path d="M311 800l15 6c3 1 6 1 9 2 4 2 11 5 16 5 3 0 7 1 10 1 2 0 9-1 11 0l2 1c-17 4-35-3-51-9-4-2-8-3-13-5l1-1z" class="w"></path><path d="M448 209c7-2 11-3 16-9-2 4-4 8-7 12 6 4 15 7 17 15-1-1-2-2-3-4-1-1-5-4-6-5h-3l-6-3c-1-1-2-3-4-3-3 0-4-1-7-3h3z" class="Aj"></path><defs><linearGradient id="A" x1="345.883" y1="552.073" x2="354.24" y2="563.322" xlink:href="#B"><stop offset="0" stop-color="#3d0100"></stop><stop offset="1" stop-color="#690d0c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M343 555h1c1-1 1-3 2-4 0-1 1-2 2-3 1 0 1 1 2 1 2 3 3 6 5 9l-1 1c0 1 1 2 1 3 1 1 1 2 1 4l-1 1h0c-1-1-1-2-2-2 0 1 0 2-1 3v1-2-1c0-1-1-2-1-3v-2l-1-1v1 1 1l-1-2-2 1v1h-1v-5-1l-1-1c-1 0-1 0-2-1z"></path><path d="M346 558l2 2-1 2v1h-1v-5z" class="a"></path><defs><linearGradient id="C" x1="571.256" y1="220.031" x2="567.743" y2="227.42" xlink:href="#B"><stop offset="0" stop-color="#d02222"></stop><stop offset="1" stop-color="#ee3a34"></stop></linearGradient></defs><path fill="url(#C)" d="M557 223h2l1-1c3-2 6-4 9-5l3 1c2 1 4 3 6 4s3 2 4 3c-2 0-2 0-3 1-4-2-10-4-14-2-8 1-10 5-14 11 1-5 3-8 6-12z"></path><defs><linearGradient id="D" x1="385.082" y1="717.781" x2="404.418" y2="710.719" xlink:href="#B"><stop offset="0" stop-color="#1f2328"></stop><stop offset="1" stop-color="#3a3c41"></stop></linearGradient></defs><path fill="url(#D)" d="M387 702h2l-1 1h4l1 1c1 0 1 0 2 1h3l1 1h2-2c-2 0-4-1-6-2l-1 1c6 5 13 9 17 17v1c0 1 1 2 1 4h-1c-5-8-12-16-21-20-3-2-6-3-10-4h1l1-1h7z"></path><path d="M594 257l1 1c0 3 0 6-1 10l-2 12-3 12c-1 2-2 4-3 7 0-4 1-6-1-9 1-5 3-11 4-17 0-4-1-7-1-11h1v5c1 3 1 6 2 9 2-6 3-12 3-19z" class="T"></path><path d="M343 555c1 1 1 1 2 1l1 1v1 5h1l-1 3v4h-2l1 2c-1 2-2 3-3 4l1 2-3 1h-1l-2-3c-2-6-2-11-1-17 0 3-1 10 1 12 0 1 0 2 1 3h1v-1c0-1 1-2 1-2v-3h0l1-1v-4h1v-1-1c0-2 1-4 1-6z" class="Q"></path><path d="M342 576h0l-1-1v-2l3-3 1 2c-1 2-2 3-3 4z" class="U"></path><path d="M343 555c1 1 1 1 2 1l1 1v1 5h0c-1 2-1 3-1 6h0c-1-1-2-3-2-4-1 1-1 2-2 4-1 1 0 3-2 4 0-1 1-2 1-2v-3h0l1-1v-4h1v-1-1c0-2 1-4 1-6z" class="D"></path><path d="M467 894c-2 0-4 1-6 2-1 2 0 6 0 9l-2-5c-1-2-2-4-4-4-2-1-4-1-6 0l-7 5 4-6c0-2-1-3-2-4h-1l1-1 1 1 2-2c1-2 1-5 0-8 0-2-1-4-1-6l3 8v4l1 1v2c-1 1-1 0 0 1 0 1 1 1 1 1l1 1v-1h2c2 0 3-1 4-2v3 1c2 1 3 0 5 0 2-1 3-1 4-1v1z" class="s"></path><path d="M622 163c2 1 2 3 2 5v3c0 1-1 2-1 3-1 2-1 4-3 5h0c-1 1-1 2-2 3 0 1-1 2-2 3h-1v-3-1l-1-1v-2h-1l1-2v-1c-1-1 0-1-2-1h-1c1-1 1-1 2-1 4-1 5-3 7-6 2-1 2-2 2-4z" class="P"></path><path d="M619 173l1 1c1 0 2-1 2-2l1-1h1c0 1-1 2-1 3h-1c-1 1-1 3-3 4l-1-1c0-1 1-2 1-4z" class="a"></path><path d="M614 176h1v3l2-2h0 0v4l1 1c0 1-1 2-2 3h-1v-3-1l-1-1v-2h-1l1-2z" class="AL"></path><path d="M622 163c2 1 2 3 2 5v3h-1l-1 1c0 1-1 2-2 2l-1-1c1-2 2-4 1-6 2-1 2-2 2-4z" class="m"></path><path d="M599 781c2 4 6 8 9 12v1s-1 0-1-1c-3-2-6-5-9-9-8-10-13-21-17-34-2-4-3-8-4-12h1c0 1 0 2 1 3 1 5 2 9 4 13 1 3 3 5 4 7 1 3 1 6 3 8h0c2 2 3 4 5 6h0c1 1 1 2 2 2v1l2 3z" class="AN"></path><path d="M519 948c-2 1-3 2-4 4-3 3-3 7-3 11-1-3-1-8-3-12-1-2-3-3-4-5-1 0-2-1-2-1 0-1 2-4 2-5v-4c-1-6-4-10-8-13v-1h1l2 1 3 4v1c1 2 3 4 3 6v1 6l-1 4h1c1 3 3 5 5 7 0 1 1 2 2 3 0-3 0-6-1-9l-1-2c-2-3-2-8-2-12 2 3 3 5 4 7l2 5c0 1 0 1 2 2 0 1 0 2 2 2zm220-158h1c2-3 5-5 9-6-1 1-2 1-3 2 0 1-1 1-1 1-1 2-3 3-4 5-3 4-3 8-3 12s3 7 6 10c4 2 7 4 11 3 2 0 3-1 4-2v1c-3 3-6 2-9 5-2 1-3 4-3 5 0 2-1 5-2 6-1-1 1-5 1-7 1-3 1-4 3-6h2-1c-2 0-3-1-5-2l-1-1c-3-2-6-5-7-8v-1c-2-3-1-7-1-10h0c1-3 2-5 3-7z" class="w"></path><path d="M196 316v1c-7 5-12 10-18 15-4 4-8 7-12 10-1 1-3 2-4 3-3 1-7 3-9 2 10-4 16-12 25-19 5-4 12-8 18-12z" class="An"></path><path d="M563 140c3-3 10-4 14-4h5 1c7 1 19 3 25 8l-6-2c-3-1-4-1-6 0-2 0-5-1-7-2-10-2-17-2-26 3l-1-1 1-2z" class="O"></path><path d="M413 179c-1-2-1-4-1-7-1 0-1-1-1-2l1-1 2 6c2-3 4-5 6-7 0 4-2 10-1 14 0 3 1 5 2 7l3 6-1-1h-4c-3-4-4-9-6-14v-1z" class="C"></path><path d="M413 180c2 1 2 2 4 4 2 3 4 7 6 10h-4c-3-4-4-9-6-14z" class="AH"></path><path d="M450 213c2 1 4 1 6 2h0l6 3 1 1c1 0 2 1 3 1 1 2 1 3 2 5-5-2-8-2-13-1l-7 3h-2v-2c1-2 3-3 4-4s2-1 3-3h0c1 0 1 0 2-1l-1-1c-1 0-2-1-3-2l-1-1z" class="t"></path><path d="M466 220c1 2 1 3 2 5-5-2-8-2-13-1v-1c3-1 8-1 11-3z" class="H"></path><path d="M450 213c2 1 4 1 6 2h0l6 3 1 1-1 1c-3 0-5-1-8-2h-1c1 0 1 0 2-1l-1-1c-1 0-2-1-3-2l-1-1z" class="N"></path><path d="M366 698h1c2-2 4-3 6-5-2-1-5-4-7-6l-2-3c1 0 2 1 3 2 6 5 13 3 20 3l8-2-1 2h3c0 1 0 1-1 2v1c-1 0-4 1-4 1-1 0-3 0-5 1h0v-1l-2-1c-1 0-3 0-4-1l-1 1c-1 1-2 1-3 1l-5 4c-1 0-1 0-2 1l-3 1-1-1z" class="Ai"></path><path d="M395 687l-1 2h3c0 1 0 1-1 2v1c-1 0-4 1-4 1-1 0-3 0-5 1h0v-1l-2-1h3c1-1 3-1 4-2-2-1-3-1-5-1l8-2z" class="AE"></path><path d="M276 817c2-2 4-3 5-5 2-4 2-8 1-12h-1c-2-7-7-13-13-16-2-1-3-2-5-3l-6 4c-3 0-5 0-8-1h-1c-2-1-3-2-4-4-2-2-5-7-4-10h0l3 6v1l2 2c1 1 3 3 5 3h0c3 0 4 0 6-1 2-2 1-3 2-5v-1c0-1-1-2-2-3h0v-1c2 1 3 3 4 4-1 1-1 2-1 3 1 0 1-1 1-1h0c1 0 1 1 0 2l-1 3h1l2-2c2 1 4 1 6 2 3 2 5 5 8 6 3 4 6 8 7 12v1c1 1 1 6 0 7v1 1c0 2-1 3-2 4l-2 2h-1v1h-2 0z" class="w"></path><path d="M289 784c-6-2-13-5-19-8-9-6-18-13-25-21-13-15-21-36-26-55l1-1c2 14 8 28 15 40 12 21 31 36 55 42 2 2 4 3 7 3 1 0 1 0 2 1h-2l-8-1z" class="Am"></path><path d="M335 550l2-2c1-1 1-1 2-1l-3 12c-1 6-1 11 1 17l2 3v2c-2 0-2 0-4 1l-2 1c0 1 0 1-1 1-1-2-1-3 0-6h0v-5-4-1c-1-4 0-8 1-12 1-2 1-4 2-6z" class="AD"></path><path d="M332 573c0 2 1 4 2 6 1 1 2 1 4 1v-1c-1-1-1-2-1-3l2 3v2c-2 0-2 0-4 1l-2 1c0 1 0 1-1 1-1-2-1-3 0-6h0v-5z" class="AM"></path><path d="M246 202c0 2 0 2-1 3l-3 4v2c1 0 1 0 2-1l1 1c-1 1-5 3-5 4l1 1c-2 0-5 0-8-1l1 2-1 1-1-1-1 1c-3-2-5-3-8-4h0c-4-1-8-1-12 1-1 1-2 1-4 2 2-1 3-3 5-4 4-2 11-1 15-1 9-1 14-4 19-10z" class="N"></path><path d="M233 215h-2v-1c5 0 7-2 11-5v2c1 0 1 0 2-1l1 1c-1 1-5 3-5 4l1 1c-2 0-5 0-8-1z" class="C"></path><path d="M431 784c5-5 7-11 11-17v1c0 4-3 8-6 11-6 10-14 19-23 26-3 2-7 4-10 5-2 1-4 1-5 1l-24 4-2-1 21-3c2 0 3-1 5-1 10-3 21-11 28-19 2-2 4-4 5-7z" class="Ac"></path><path d="M596 142c2-1 3-1 6 0l6 2 7 5 5 5c2 3 6 10 9 12h1c1 2 0 2 0 4h0c1 0 2 1 2 2h0c-2-2-5-3-8-4 0-2 0-4-2-5-7-10-15-16-26-21z" class="G"></path><path d="M795 458c-2-1-5-5-5-6-2-3-4-4-3-7 7 11 16 20 22 32 2 2 3 5 4 8l-2-2v3c-3-3-6-6-9-8l1-1 1-1c0-1-1-3-1-4-1-1-2-2-2-3-3-3-6-6-9-8l1-2c0 1 1 1 2 1h1l-1-2z" class="AF"></path><path d="M803 472c3 3 6 7 8 11v3c-3-3-6-6-9-8l1-1 1-1c0-1-1-3-1-4z" class="B"></path><path d="M518 307c1 1 1 3 3 4 3 4 7 6 7 11h0l-2-2c-1 1-2 5-3 6-1 0-2 0-2 1-2 0-2 0-3-1-2-1-2-2-2-3v-1-3l-1-5v-6h1l1 2 1-3z" class="AM"></path><path d="M515 314v-6h1l1 2c0 2 1 3 2 5 2 3 4 4 3 9h-1c-1-1-1-3-2-3v2h-1c-1-2-1-3-2-4l-1-5z" class="o"></path><path d="M630 663v-2c1-1 2-3 3-4s2-2 3-2c1-1 3-2 4-3 2-1 4-2 6-2l1-1c1 0 2 1 2 1 1 1 2 1 3 1v5c-5 2-9 3-13 6h0c-1 1-3 3-4 3l-6 6c-3 5-6 9-7 15h0l-1 1v-1c1-6 4-11 7-16l6-6 1-1h1l1-1 3-3c1 0 1-1 2-1v-1h1v-1-1h-3c0 1-1 1-2 1 0 1-1 1-1 1-3 1-5 3-7 6z" class="I"></path><path d="M219 367c4 1 7 10 10 13l3 9c1 3 1 9 3 12l1 7c-1 2-1 4-2 6-2 4-3 8-6 11l-1 2c1 1 2 1 3 2h1c0 2-1 4 0 5v2 3h-1c-1-2-2-8-3-9-2-1-4-2-5-3l1-1h2c1-2 5-6 5-8v-1h0l1-2c0-1 1-3 2-5 3-15-6-32-14-43z" class="c"></path><path d="M582 785c-2-2-4-4-6-5-4-3-8-3-10-7l1-1c3 2 7 3 10 5 1-6-2-10-2-15v-7h0c1 5 4 9 5 14l4 8 1 3-1 1 3 7h-2l-3-3z" class="s"></path><path d="M580 769l4 8 1 3-1 1 3 7h-2l-3-3c1 0 2 1 3 1v-1c-1-1-1-1-1-2-2-3-5-10-4-14z" class="AU"></path><path d="M398 692l1 1c1 0 2-1 3-2l1-1c1 0 1-1 2-2v1c0 2-2 3-4 4v1l-2 1c-1 0-2 1-3 1h0c-1 1-2 1-3 1-1 1-3 1-4 1l-1-1c-1-1-2 0-3 0h-1-2-2-1c-1 1-3 1-4 1-2 0-2 1-3 1l-1 1c6-2 11 0 17-1 1-1 1 0 3 0v-1c3 0 5-1 7-1l1-1h1l2-1h1 1l1-1 1-1h1 0v-1h-2l1-1h3c1 0 1-1 2-1v1c-2 2-4 3-7 4l-1 1c-4 2-10 3-16 4h-18 0l2-2 1-1 5-4c1 0 2 0 3-1l1-1c1 1 3 1 4 1l2 1v1h0c2-1 4-1 5-1 0 0 3-1 4-1h2z" class="s"></path><path d="M372 697l5-4c0 2 0 2-1 3s-3 2-5 2l1-1z" class="y"></path><path d="M398 692l1 1c1 0 2-1 3-2l1-1c1 0 1-1 2-2v1c0 2-2 3-4 4v1l-2 1c-1 0-2 1-3 1h-1c-2 0-4 1-6 1v-1h-9-1c3-1 6-1 9-1v-1c1 0 2 0 3-1h1s3-1 4-1h2z" class="w"></path><path d="M430 207c2 1 2 1 3 2 3 0 6 0 8-1l7 1h-3c3 2 4 3 7 3 2 0 3 2 4 3h0c-2-1-4-1-6-2h-3c-6-1-12-1-18 0l-3 1h-5-4l-1-1-1-1v-1c-2 0-4 0-7 1v-1h0c2 0 3-1 5-1h2c2-1 4-1 7-1 2-1 6 0 8-2z" class="Af"></path><path d="M416 213l7-2v1h1c2 0 3 1 5 1l-3 1h-5-4l-1-1z" class="AV"></path><path d="M430 207c2 1 2 1 3 2l-10 2-7 2-1-1v-1c-2 0-4 0-7 1v-1h0c2 0 3-1 5-1h2c2-1 4-1 7-1 2-1 6 0 8-2z" class="T"></path><path d="M245 459c1 2 3 4 5 4l4 2c1 1 2 1 3 1s2 0 3 1h-1c-2 1-3 1-5 1 0 1 1 0 2 1h-11c-14-1-26 3-37 13l-1 1c2-4 5-7 8-11l2-2c0 1 1 1 2 2l2-1c7-5 17-6 27-6l-4-5 1-1z" class="B"></path><path d="M564 850c4-1 8-3 11-5l2-1c1 0 1 0 1-1h2l4-2 2-1 3-2c0-1 1-1 2-2s3-2 5-3h0 1l1 1-1 1c1 0 2 0 3 1h0s-1 1-2 1v1h-1c-1 2-4 4-6 6-1 0-2 1-2 3l1 1v6c-2-2-3-3-3-5-1-1-1-2-1-2h-3l-1-1h0-2c-2 1-5 2-7 2h-1c-1 1-1 1-2 1l-12 6v-1c1-1 1-1 3-1l3-3z" class="x"></path><path d="M425 758l3-9c0 3-1 8-2 11v2h1c-1 1-3 1-4 2-2 2-3 7-4 10-5 13-14 25-27 32l-1-2c5-2 8-5 11-8 1-2 3-3 4-5s3-4 4-7c1-1 1-2 2-4 0-2 2-6 3-9h1c0-2 1-3 2-5 1-1 1-2 2-3s1-2 1-3h3 0l1 1v-3z" class="Ac"></path><path d="M421 760h3 0l1 1-4 4-1-2c1-1 1-2 1-3z" class="AU"></path><path d="M412 780h2c0 3-4 8-6 10-1 1-1 1-2 1 1-2 3-4 4-7 1-1 1-2 2-4z" class="AZ"></path><path d="M412 780c0-2 2-6 3-9h1c0-2 1-3 2-5 1-1 1-2 2-3l1 2c-2 5-5 10-7 15h-2z" class="s"></path><path d="M640 853v1c2 2 4 5 6 7 5 4 22 10 28 10 4 0 6-1 9-3 0-2-1-2-1-4h0c1 1 2 2 2 3 2 0 4 0 6-1l1 1c1 1 0 1 0 3h-1-1c1 1 3 9 3 9-1 2-2 4-3 5-2 2-3 4-4 5h-1c4-4 6-8 6-13 0-2-1-5-3-7-1 0-1-1-2-1-5 3-9 4-15 5-2 0-4 0-6 1s-2 2-3 4c0 1 0 2 1 3l1 1-4-2c0-3 1-5 2-8l-1-1c-2 0-4-1-7-1l2-1c-1-1-3-2-4-3v-1l-1-1h-4c-2-4-4-7-7-10l1-1z" class="Ai"></path><path d="M659 868c4 1 8 2 11 4-2 0-4-1-6 0h-3 0l-1-1 1-1-2-2z" class="AQ"></path><path d="M651 865c2 1 5 2 8 3l2 2-1 1c-2 0-4-1-7-1l2-1c-1-1-3-2-4-3v-1z" class="s"></path><path d="M422 854c2-5-5-13-9-16l-1-1h-1c1-1 1-1 2-1 3 1 8 6 10 9l1 1c0 1 1 1 1 3 2-1 5-4 6-6 6-5 11-11 15-18 3-6 5-14 10-19l1 1c-1 1-1 2-2 3h0c0 1 0 1-1 2-1 2 0 0-1 1v1 1l-1 1v2l-1 1c0 1-1 2-1 3-4 5-7 10-10 16-4 4-9 8-12 12-2 4-3 8-4 12-1-2-1-5-2-8z" class="w"></path><path d="M548 877c1 0 1 0 3 1v-1h4 0 1c-1 2-2 2-3 2v3c0-1 1-1 1-2l1-1v1 2l2-2h1l1-3 1 1-6 13s-1 0-1 1h0c-3 1-6 3-9 3l-2 1c-3 1-6 1-9 2h-3c2-2 4-2 7-3 4-1 6-3 9-7 0-1 1-4 0-5v-1c1-3 5-1 6-3h-4v-2z" class="AQ"></path><path d="M546 888c2-1 4-3 5-5v-2l1 1c-1 3-4 7-5 10 1 0 3-1 5-1l1 1c-3 1-6 3-9 3l-2 1c-3 1-6 1-9 2h-3c2-2 4-2 7-3 4-1 6-3 9-7z" class="s"></path><path d="M569 217l9-3v1h1c4 1 7 3 10 5 3 3 6 6 8 10v1c1 1 1 2 1 4l-5-5v1c-1-1-2-1-3-1-2-1-3-2-4-3s-3-2-4-2c-1-1-2-2-4-3s-4-3-6-4l-3-1z" class="t"></path><path d="M260 467c2 1 5 1 7 1l-1-1 1-1 15 9 9 6c2 2 3 4 5 5l2 3c2 3 5 4 5 7-3-3-6-7-10-10l-3-1c-10-8-22-12-34-16-1-1-2 0-2-1 2 0 3 0 5-1h1z" class="c"></path><path d="M436 230l4-4 1 1c-2 3-3 5-4 8l1 1v-1h1c-3 5-5 9-7 15l-1 1-1 1c-1 1-2 1-2 3 0 1-1 2-1 4-1 2 0 6 0 8h-2 1v-1-4h-1c0 1 0 2-1 2h0c-1-7 1-15 3-22 2-3 4-6 6-8l3-4z" class="u"></path><path d="M239 158s4 2 4 3c3 2 7 6 9 8-1 2-1 3-1 5l-1-1v1c3 6 4 13 2 20 0 2-1 4-2 6v-2c1-4 1-6 1-10v-1l-3-9-2-4-1 1c-2-2-4-3-6-4v-1-2-6c-1-1 0-3 0-4z" class="x"></path><path d="M239 168l1-3c1 0 1 0 1 1 1 1 1 2 2 3-1 1-2 1-4 1v-2z" class="AK"></path><path d="M239 170c2 0 3 0 4-1 1 2 2 3 3 5l-1 1c-2-2-4-3-6-4v-1z" class="v"></path><path d="M243 161c3 2 7 6 9 8-1 2-1 3-1 5l-1-1v1c-2-3-4-7-5-10-1-1-1-2-2-3z" class="Y"></path><path d="M410 694c1 0 2-1 3-1h0c2 2 6 4 8 7v4c1 0 1 0 1 1 2-3 2-7 4-10v1h1c0 1 0 2-1 3v3h-1v2h0l-1 3v1c-1 2-1 1-1 2v1h-1v1 2h-1l-1 3v1h0l1-1v2c-1 1 0 1-1 2l-1 5v2l-1 2h0l-1-1h0l-1-2c-1-2 0-4 0-6h0v-1c0-1 1-2 1-3v-1l1-1h0c0-1 0-1 1-2v-2l1-1v-1c-1 0-1 0-1 1h-1c0-1 1-1 1-1 0-1-2-3-2-3l-1-1c0-1 0-1-1-1v-1l-1-1c-2-2-3-4-4-8z" class="y"></path><path d="M775 256l1-5c0-2 0-3 2-4l3 5c1 2 3 3 5 5l1 1h1-1l4 2v1l-1 1c-3-1 1 0-2-1h-3c2 1 3 2 5 2 0 1 0 2-1 3l-3 2c-1 1-2 2-2 4h-1c-2-2-3-3-4-5-1-1-1-2-2-2 1-2 1-3 0-5l-2-4z" class="H"></path><path d="M789 266c-2 0-3-1-4-1 0-2-1-2-2-4h-1l1-1 2 1c2 1 3 2 5 2 0 1 0 2-1 3z" class="AD"></path><path d="M779 262c2 3 3 4 7 5v1c-1 1-2 2-2 4h-1c-2-2-3-3-4-5v-5z" class="z"></path><path d="M775 256l1-5c0-2 0-3 2-4l3 5c-2 2-3 4-3 7 0 1 1 2 1 3v5c-1-1-1-2-2-2 1-2 1-3 0-5l-2-4z" class="M"></path><path d="M511 311l3-6-1 10 2-1 1 5v3c-1 1-3 2-3 4-1 1-1 0-1 1h-1-1c-2 1-2 1-3 1-2-1-2-2-3-3-1-2-3-1-4-3 0-2 1-3 1-4l1-2c2-1 4-3 6-4 1-1 2-1 3-1z" class="o"></path><path d="M508 312c1-1 2-1 3-1l-3 6-1-1c-1 2-2 4-3 5 0-2 0-3-2-5 2-1 4-3 6-4z" class="AC"></path><path d="M513 315l2-1 1 5v3c-1 1-3 2-3 4-1 1-1 0-1 1h-1-1c-2 1-2 1-3 1-2-1-2-2-3-3-1-2-3-1-4-3 0-2 1-3 1-4l1-2c2 2 2 3 2 5l1 2c0 1 0 1 1 1h2l3-3v-1c2-2 2-3 2-5z" class="l"></path><path d="M501 318l1-2c2 2 2 3 2 5l1 2c-1-1-1-1-1-2h-1c-1-1-1-2-2-3z" class="e"></path><path d="M354 660v-1c2-3 10-8 14-9 1-1 2-1 4 0l1 1c1 1 2 1 3 1 2 1 3 3 5 4v1c1 0 2 2 3 2-2 1-4 2-4 4 2 2 5 5 7 8h0c-7-7-15-13-26-14-2 0-5 1-7 3z" class="q"></path><path d="M374 659c-4-2-9-3-13-4 1 0 5-1 6-2h1l3 1h2c1 1 2 2 3 2l-2 2v1z" class="Y"></path><path d="M368 653c1-1 2-2 4-3l1 1c1 1 2 1 3 1 2 1 3 3 5 4v1c1 0 2 2 3 2-2 1-4 2-4 4l-2-2-4-2v-1l2-2c-1 0-2-1-3-2h-2l-3-1z" class="AY"></path><path d="M381 657c1 0 2 2 3 2-2 1-4 2-4 4l-2-2-1-2h2l2 1v-1-1-1z" class="x"></path><path d="M817 318l14 11c10 7 22 13 29 22h-3l-1 1v1c1 0 1 1 1 2l-1 2c-1-2-2-4-3-5h0c-5-7-14-11-22-14l3-1c1 0 2 1 3 0h1l-11-8-5-5c-2-1-4-3-5-6z" class="AF"></path><path d="M585 788h2l-3-7 1-1c2 3 4 8 6 10 3 5 6 10 10 14l-1 1 2 2v2 1c1 1 2 3 3 4 1 2 3 3 3 5l-1 1-11-11c0-2-3-4-4-5l-4-7-2-2 1-1v-1-1l1-1-3-3z" class="y"></path><path d="M595 802l4 4c1 2 1 3 2 4v1c-3-2-5-6-7-9h1z" class="x"></path><path d="M588 791l4 4c0 3 1 5 3 7h-1l-6-6v1l-2-2 1-1v-1-1l1-1z" class="AQ"></path><path d="M592 795c2 2 4 4 5 6 1 1 2 3 3 4l2 2v2 1c1 1 2 3 3 4l-1 1c-1-1-2-2-3-4v-1c-1-1-1-2-2-4l-4-4c-2-2-3-4-3-7z" class="s"></path><path d="M585 788h2l-3-7 1-1c2 3 4 8 6 10 3 5 6 10 10 14l-1 1c-1-1-2-3-3-4-1-2-3-4-5-6l-4-4-3-3z" class="AE"></path><path d="M745 504l-11-11v-1c0-1-1-2-2-3s-3-2-4-3h0c7 3 13 9 18 15 2 2 5 6 8 8 1 1 3 1 5 2s9 1 12 0c0 0 0-1 1-1l3-3 1 1c-2 3-7 6-11 7h-3l-1 1-1 1v-1c-1-1-1 0-1-1-1-1-2-2-3-2-1-1-1-1-1-2l-1 1c1 1 1 2 2 3 1 2 1 3 1 5-1 2-2 3-3 3-1 1-2 1-3 1 1-1 2-3 2-5 1-4-2-7-5-9 0-3-1-5-3-6z" class="w"></path><path d="M436 277l2-8 2-4c0 1-1 3-1 4-1 6-2 10 0 15 1 1 2 3 3 3 1 1 3 2 4 3s1 5 2 7c0 3 1 5 2 7h0c-1 1-1 2-1 2l-2 1-1-2-1 1c0 2 0 2 1 3h-1l-3-3c-2-3-3-5-5-8l2-1c0-3-2-7-2-10-1-3-1-7-1-10z" class="j"></path><path d="M439 288h1l2 1c1 0 2 1 2 2h0v3h0c-1 0-2 0-3-1-2-2-2-3-2-5z" class="u"></path><path d="M439 847c1 1 3 2 5 2 5 3 11 5 17 7l17 6c2 1 5 1 7 2h0c-11 0-21-5-31-7-1 0-3 0-3 1-3 2-4 7-4 10v1c-1-3-1-5-2-7-1-6-3-9-8-12h1 1l1-1-2-1 1-1z" class="s"></path><path d="M447 855c1 0 3 0 4 1l-2 1c0 1 0 2-1 2l-1-3v-1z" class="AQ"></path><path d="M347 562l2-1 1 2v-1-1-1l1 1v2c0 1 1 2 1 3v1 2c-1 1-2 1-1 3 1 4 3 7 5 11l1 3v1h-1v1l-4-2c-2 0-4-1-6-2-1-1-2-2-3-2l-3-3 3-1-1-2c1-1 2-2 3-4l-1-2h2v-4l1-3v-1z" class="P"></path><path d="M351 583l2-1v1l3 4v1l-4-2c0-1-1-2-1-3z" class="V"></path><path d="M349 580c0 1 1 2 2 3 0 1 1 2 1 3-2 0-4-1-6-2l1-1h1l1-3z" class="R"></path><path d="M345 572c0 2 1 3 2 5l1 1 1 2-1 3h-1l-1 1c-1-1-2-2-3-2l-3-3 3-1-1-2c1-1 2-2 3-4z" class="AB"></path><path d="M348 578l1 2-1 3h-1c-1-1-2-3-3-4h1 1l2-1z" class="E"></path><path d="M343 578l1 1c1 1 2 3 3 4l-1 1c-1-1-2-2-3-2l-3-3 3-1z" class="C"></path><path d="M290 485l3 1-1 1c-2 3-5 5-8 8-6 5-14 11-18 18h0c-1 2-2 3-2 5h-1c0 2 1 4 1 5-1 0-1-1-2-2 0-3 1-6 3-9-1 0-2 1-3 1-1 1-1 1-2 0v-1h-1c-2 1-2 1-3 1-2 1-5 0-7 0-1 0-1 0-2-1 1-1 0-1 1-1 4 0 9 1 13-1 1-2 3-3 4-4a57.31 57.31 0 0 0 11-11c5-3 9-8 14-10z" class="s"></path><path d="M217 470c6-7 13-13 23-16 1 2 3 4 4 6l4 5c-10 0-20 1-27 6l-2 1c-1-1-2-1-2-2z" class="m"></path><path d="M534 899l3-1c1 0 3 0 4-1 4 0 13-3 16-2l1 2h-1c-6 1-10 4-15 7-2 1-4 3-7 5l-16 12-1-1c3-3 7-6 10-9 4-3 8-5 11-8 4-3 10-3 14-6-2 0-5 1-7 1l-14 2s-2 0-3 1c0 0-1 2-2 2l-6 10-3 5-1 2c-1 1 0 1-1 3 0 0-2 2-2 3-1-3 1-6 1-9 2-4 4-9 6-13 2-3 5-5 7-8h0c1 2 1 2 0 4h1l1-1h4z" class="y"></path><path d="M357 631c1-2 2-4 4-5v2l1 1c2-2 2-3 4-3-3 4-7 7-9 12v3 1l1-1 1 1v1c1 1 0 1 1 1 0 1 0 0 1 1 2-2 4-5 6-5 2-1 2-3 3-5l1 1 2-2 3-1v1c-1 1-3 4-5 4l-17 19v-5c-2-1-3-1-4 0 1-4 1-6-1-10h1c0 1 1 2 1 3 1-2 1-4 2-6l2-7 1-2 1 1z" class="r"></path><path d="M357 631c1-2 2-4 4-5v2l1 1c-3 3-5 6-7 10 0-3 1-5 2-8z" class="B"></path><path d="M373 634l3-1v1c-1 1-3 4-5 4h-1c-5 4-10 10-14 15-1 0-1 1-1 1 0-5 0-11 2-16v3 1l1-1 1 1v1c1 1 0 1 1 1 0 1 0 0 1 1 2-2 4-5 6-5 2-1 2-3 3-5l1 1 2-2z" class="a"></path><path d="M351 822c-3-2-7-2-9-3s-4-2-6-2l-2-1c-1 0-2-1-3-1v-1l-3-1v-1c15 6 30 12 46 10 3 0 5-1 8-2 2-1 4-2 7-3h1 6c-2 1-5 2-6 3s-2 1-3 2h-1 0l3 3h0c-2 1-5 1-7 3-2 1-6 0-8 0h-2c-2 0-4-1-6-1l-5-1v-1-1c-3-1-8-1-11-2h1z" class="AU"></path><path d="M382 820c2-1 4-2 7-3h1 6c-2 1-5 2-6 3-2 0-2 0-4 1-1 0-3 0-4-1z" class="AZ"></path><path d="M351 822c11 2 24 4 35 0l3 3h0c-2 1-5 1-7 3-2 1-6 0-8 0h-2c-2 0-4-1-6-1l-5-1v-1-1c-3-1-8-1-11-2h1z" class="x"></path><path d="M366 827c3-1 6-1 9 0h1-1c-1 0-2 0-3 1-2 0-4-1-6-1z" class="AQ"></path><path d="M689 621h8c5 1 8 3 12 7l1 2c2 5 2 8 2 12l-1 1c0-6 0-11-5-16-4-3-9-5-13-5-10 1-20 6-27 13-1 2-3 4-4 6v1c-1 2-1 5-1 7 1 1 1 2 1 2h0c-1 1-1 3-2 3l-1-1v-1l-2-17h2l-1-4h1l1 2 3-3 5-2-2-1 6-2 14-3 3-1z" class="e"></path><path d="M658 631h1l1 2c1 2 1 4 2 6l-2 4-1-8-1-4z" class="m"></path><path d="M668 628l8-3c-5 3-8 6-12 10l-2 4c-1-2-1-4-2-6l3-3 5-2z" class="Y"></path><path d="M663 630c-1 2-1 2-1 4 1 1 1 1 2 1l-2 4c-1-2-1-4-2-6l3-3z" class="I"></path><path d="M321 783l2-1c2 1 2 3 3 4v1c1 2 2 3 4 5v1c-2 0-4-2-7-3v1h0-6c-2 1-2 3-2 4h-1l-3-2v1c-2 0-4-1-5-2l-14-4c-1-1-4-2-5-2v-1h-1c0-1-1-1-2-2 2 0 2 0 3 1h2l8 1c4 2 12 1 16 0h1c2 0 5-1 7-2z" class="AQ"></path><path d="M311 793l3-3c3-1 7-1 9 0v1h0-6c-2 1-2 3-2 4h-1l-3-2z" class="Ah"></path><path d="M772 211c-1-1-2-2-2-3 1 1 2 1 3 1h0c3 0 4 2 7 4h1c2 1 3 1 5 2h1l-6 3c-4 4-5 7-6 12l1 11 2 6c-2 1-2 2-2 4l-1 5-1-2c-1-2-2-5-3-7-1-4-1-8-1-12 0-2 0-3 1-4 0-4 1-7 2-10 1-2 1-4 2-5h1l-1-1v-1c-2-1-2-2-3-3h0z" class="C"></path><path d="M772 211c-1-1-2-2-2-3 1 1 2 1 3 1h0c3 0 4 2 7 4h1c2 1 3 1 5 2h1l-6 3c-1-1-1-1-1-2 1-1 2-1 2-1v-1h-3c-3 0-5-2-7-3z" class="U"></path><path d="M775 230l1 11 2 6c-2 1-2 2-2 4l-1 5-1-2c-1-2-2-5-3-7 1-3 1-6 2-9 1-2 1-5 2-8z" class="R"></path><path d="M776 241l2 6c-2 1-2 2-2 4l-1 5-1-2c1-4 0-10 2-13z" class="B"></path><path d="M252 455l2 2v-1l1-1 2 1h1c4 2 6 5 11 7l1 1 2 1 3 1 5 3 5 4h1c1 2 2 2 3 3h-4c-1-1-2-1-3-1l-15-9-1 1 1 1c-2 0-5 0-7-1-1-1-2-1-3-1s-2 0-3-1l-4-2c-2 0-4-2-5-4l-1-3c2 1 3 1 4 1 2 0 3-1 4-2z" class="I"></path><path d="M244 456c2 1 3 1 4 1h0c6 2 14 5 19 9l-1 1 1 1c-2 0-5 0-7-1-1-1-2-1-3-1s-2 0-3-1l-4-2c-2 0-4-2-5-4l-1-3z" class="N"></path><path d="M251 460c1 0 3 1 4 2l1 1-2 2-4-2c1-1 1-2 1-3z" class="P"></path><path d="M244 456c2 1 3 1 4 1h0v1c1 1 2 1 3 2 0 1 0 2-1 3-2 0-4-2-5-4l-1-3z" class="Q"></path><path d="M358 553c-1 0-2-1-2-1-2-1-1 0-2-1l-1-1-2-1-1-1c-2-1-4-3-5-4 0-1 0-1 1-2v-1c1 0 2-1 3-1l1 1 1 1c1 0 2 2 3 3h1c1 1 3 2 4 3 2 2 5 4 8 5 1 0 1-1 1-1v-2c1 0 1-1 1-1 1-2 1-3 2-5l-1 1v-3l-1-2h0c1 0 1 1 2 2v1c1-4 2-9 5-11 2 2 2 1 2 3l-1 1c0 2-1 2-1 4h0c-1 1-1 2-1 2v1 1h-1v1 1c0 1 0 1-1 2h0v1c-1 1-1 2-1 4l-1 1v1h1v-1c0-1 0-2 1-2h0v2c1-1 0-2 1-3v-1-1c0 1 1 2 1 3v1h0c0 2-1 3-2 4l-1 2c4 3 7 5 11 8l-1 1c-2-1-3-3-5-4-1-1-3-2-5-3 0-1-2-2-3-2l-11-7v1z" class="Y"></path><path d="M351 548h0c-1-1-3-2-4-4 1-1 2-1 3-2 2 1 3 3 5 4l-4 2z" class="U"></path><path d="M355 546l17 13c4 3 7 5 11 8l-1 1c-2-1-3-3-5-4-1-1-3-2-5-3 0-1-2-2-3-2l-11-7-7-4 4-2z" class="u"></path><path d="M723 810h2 5c-2 0-4 0-6 1-5 4-7 11-9 18-1 3-1 7 0 10v1c0 1 1 2 1 3v1l1 2v2h0c-1-1-2-2-2-4 0 0 0-1-1-1-1-2 0-1 0-3h-1v-2l-1-1v-2 2 1c0 1-1 2-1 3v1h0v2l-1 1c1 1 1 2 1 3l-11 5c-5 1-9-3-12-5h0c2-1 5 3 8 4h3l4-3c1 0 2-1 3-1 1-1 3-3 3-4 1-1 1-6 1-8s0-5 1-7c-3 4-5 7-8 10h-1l4-4v-1c2-2 3-5 4-7-2 3-4 5-6 7l-2 1-3 2-1 1c-1 1-3 1-4 2 6-6 10-12 16-18l10-10h1l2-2z" class="y"></path><path d="M276 786c2 1 5 4 7 5 2 2 4 4 6 5v1h-1c1 1 1 2 2 3l1-1h1c2 0 3 1 4 2h1c0 1 1 1 1 2-2 0-4 0-6 2l-2 3c-2 5-7 11-5 16l1 1 1 5h0c-1-1-2-3-2-4-1-2-2-5-3-7-4-1-11 3-14-1 1 0 3 0 5-1h1 2 0 2v-1h1l2-2c1-1 2-2 2-4v-1-1c1-1 1-6 0-7v-1c-1-4-4-8-7-12v-2z" class="AN"></path><path d="M292 799c2 0 3 1 4 2h1c0 1 1 1 1 2-2 0-4 0-6 2l-2 3h0c0-2 1-4 2-5l1-1h0-3c1-1 1-2 2-3z" class="w"></path><path d="M468 897l-1-1h1c1 0 2 0 3-1 6 2 13 4 18 9 6 3 11 8 16 12v1 1l1 1h1l-1-2h1l1 3c-3 0-6-2-9-3-2 0-4-1-5-2v2c0 2 0 3-1 5l-2 5h0v-1c1-3 1-5 1-8-5-8-15-17-24-21z" class="s"></path><path d="M468 897l-1-1h1c1 0 2 0 3-1 6 2 13 4 18 9h-1c4 4 11 6 13 11-2-1-5-4-7-5l-9-6c-3-1-5-3-8-4-2-1-5-2-7-4l-2 1z" class="x"></path><path d="M373 634c1-2 3-3 5-4l1-1h0l1 2c1-1 2-1 2-1l1-1v1c-1 1-1 1-2 1-2 2-4 3-5 5-2 2-5 3-5 6 1 1 2 1 3 3 1 1 2 2 3 4 1 0 2 1 3 2h0v-1-2h0l1-1v-1s0-1 1-2c0-1 1-3 1-4v-1h1c1-1 1-1 3-2v-1l6-3v1l2-1v1c-1 1-1 1-2 1h-1 2c1 0 4 0 5 1h-1-1c-2-1-3 0-5 0l-1 1c-3 1-6 5-7 8s-1 8-1 10l3 5v1c-1 0-2-1-2-2-1 0-2-2-3-2v-1c-2-1-3-3-5-4-1 0-2 0-3-1l-1-1c-2-1-3-1-4 0-4 1-12 6-14 9v1c0 2-1 2-2 4l2-7 17-19c2 0 4-3 5-4v-1l-3 1z" class="AK"></path><path d="M430 275c0 1 1 1 2 1h0 2c1 1 1 1 2 1 0 3 0 7 1 10 0 3 2 7 2 10l-2 1c2 3 3 5 5 8l3 3c1 2 1 3 3 4l-1 1-2-2h0c1 2 1 3 1 5l-5-6v2c-2-2-3-5-4-6l-1 1v6l-1 1-1-2-2-6c0-1 0-1-1-2 0-1 1-2 1-2h1c-1-2-1-2 0-4v-1l-1-2c-1-7-2-14-2-21z" class="H"></path><path d="M441 311l-1-2c-2-3-5-7-5-10h1l1 1h0c0 1 1 2 1 2 1 2 3 4 4 6l1 1c0 1 1 2 2 3h0c1 2 1 3 1 5l-5-6z" class="l"></path><path d="M433 298c1 3 2 6 4 9l-1 1v6l-1 1-1-2-2-6c0-1 0-1-1-2 0-1 1-2 1-2h1c-1-2-1-2 0-4v-1z" class="R"></path><path d="M432 276h2c1 1 1 1 2 1 0 3 0 7 1 10 0 3 2 7 2 10l-2 1c-3-7-6-15-5-22h0z" class="x"></path><path d="M183 346v-1l1-1h3c1-1 3-1 4-1 1 1 2 1 4 1 1 0 2 0 3 1h1 0c1 1 2 1 4 2l2 1c1 1 3 2 4 2l12 9c3 4 8 7 11 12h0v1c1 2 1 3 1 5 1 4 1 8 1 12h-2l-3-9c0-7-7-11-11-17h0l-14-12c-4-2-8-5-12-5h-9z" class="m"></path><path d="M221 359c3 4 8 7 11 12h0v1c1 2 1 3 1 5 1 4 1 8 1 12h-2l-3-9c0-7-7-11-11-17h2c0 1 1 1 2 2 2 2 5 4 8 6-1-2-2-4-4-5-2-2-4-4-5-6v-1z" class="V"></path><path d="M221 359c3 4 8 7 11 12h-1c1 2 2 5 2 7-2-2-4-4-6-7-3-3-5-4-7-8 0 1 1 1 2 2 2 2 5 4 8 6-1-2-2-4-4-5-2-2-4-4-5-6v-1z" class="S"></path><path d="M764 468l2 1c-21 2-43 15-57 31l-5 7h0l-3-3v-1c1-2 2-3 4-5 4-4 8-9 13-13v-1c3-2 7-5 10-7s7-5 10-6v1c-2 2-9 4-10 7 1-1 1-1 2-1h1l1-1c1-1 1-1 2-1h1 0l17-6 12-2z" class="e"></path><path d="M705 498l1 2h0c-1 1-1 2-2 3v1 3l-3-3v-1c1-2 2-3 4-5z" class="B"></path><path d="M718 484c3-2 7-5 10-7s7-5 10-6v1c-2 2-9 4-10 7 1-1 1-1 2-1h1l1-1c1-1 1-1 2-1h1 0c-4 3-9 5-13 8-3 2-5 5-8 8-3 2-5 5-8 8l-1-2c4-4 8-9 13-13v-1z" class="AA"></path><path d="M358 552l11 7c1 0 3 1 3 2h-1l-2-1v2c2 0 3 1 5 3s4 3 5 5c6 6 12 13 15 20l1 1v1c1 1 1 2 2 2 0 1 1 2 1 3l1 2h0c1 1 1 2 1 3s1 2 1 3 0 1 1 2v2h1c1 2 1 3 1 4l-1 1v-1c-1-2-1-4-3-6v-2l-1-1-1-3v-1c-1-2 0 0-1-1 0-2 0-2-1-3s-1-2-1-2l-1-1c0-1-1-2-1-3s-1-2-1-2c-1-1-2-2-2-3-1-1-1-2-1-2l-1-1c0-1-1-1-1-2v3h-1l-1-2v-1l1-1c-1-1-2-1-2-3h-1c-1 0 0 0-1-1l-1-1c-1-1-1-1-1-2-1-1-2-2-3-2v-1h-1l-1-1c-1-1-3-2-4-3v-1l-1 1h-2v1c1 1 2 2 3 4 0 1 0 2-1 3 2 3 5 6 7 9 2 2 3 4 5 5l2 2c0 2 0 2 1 3h1l1 2 2 2v1l1 1 1 1c0 1 1 2 2 3s1 0 2 1l2 1s0-1-1-2l-1-2c-1-2-2-3-2-4h1c1 1 1 2 2 3l1 1v1c1 2 1 4 2 6 1 0 0 0 1 1 0 2 1 3 2 5v1l1 1 1 1v2l1 1v2l-1-2c-1-2-2-5-4-7l-2-2v-1c-1-1-1-1-1-2v-1 2l-1-1c0-1 0-2-1-2-1-2-4-2-4-4l-3-3c-1-1 0-1-1-1-1-2-2-3-3-5h-1c0-1-1-1-1-2-2-3 1 1-1-1l-2-2v-2c-1-1-2-1-2-2-1-2-3-2-3-4-2-2 0 1-1-1l-1-1c-1-1-3-4-4-6-1-1-1-1-1-2-2-2-4-5-4-7 0 0-1-1-1-2s0-1-1-2h-1c0-2 0-1-1-2 0-1-1-2-1-3v-1z" class="a"></path><path d="M370 573c-3-5-6-10-8-15l1-1c2 1 2 3 3 5 1-1 1-1 2-1-1 1 0 2 0 3v1 1c1 1 2 2 3 4 0 1 0 2-1 3z" class="P"></path><path d="M761 523h1c1 1 2 1 2 3 0 1 1 1 2 2v1l4 5 1 1c1 1 1 2 2 2h1l3 5c1 1 1 2 2 2 0 2 1 3 2 4h1c0 1 1 2 1 3 2 4 5 8 6 12l1 4c5 13 10 26 12 40l1 3c0 2 0 4 1 6l1 8v4c-2-2-1-5-2-8v-2c-1-2-1-4-1-5v-2l-1-6c-1-1-1 0-1-1v-3c-1-1-1-3-1-5l-1-2v-2c-1 0-1-1-1-1v-1c-1-1-1-1-1-2-1-1-1-1-1-2l-1-1v-2-1l-1-2c-1-2 0 0-1-1v-1h0l-2-5-2-5v-1l-3-6 1 2h-1l-1-1v1l1 1v2l1 1 1 2h-1c-3-8-7-17-12-25-3-4-6-8-8-12l-5-9z" class="s"></path><path d="M449 759v-4h1v1 7c1 2 0 4 0 5-1 3-1 6-2 8-2 6-6 11-9 16h0c-3 5-8 10-12 14-4 3-7 6-10 10-2 1-4 5-5 5l-1-3 1-2c3-5 6-9 9-13 3-1 5-4 7-6a30.44 30.44 0 0 0 8-8l8-14c1-3 2-6 4-9 0-1 2-4 1-5v-2z" class="AQ"></path><path d="M427 806c0-2 1-2 2-3v-2c0-2 4-6 6-7 1 0 2-1 4-2-3 5-8 10-12 14z" class="AN"></path><path d="M781 457h4 0c2 1 4 2 6 4h1c3 2 6 5 9 8 0 1 1 2 2 3 0 1 1 3 1 4l-1 1-1 1c-10-7-19-10-32-9h-4 0l-2-1c1-1 15 1 18-1-1-1-5-2-7-3h-1l2-1-2-2c2-2 4-3 7-4z" class="U"></path><path d="M785 457c2 1 4 2 6 4l1 2v1l-16-2c2 0 4-1 6 0 2 0 3 0 5-1 0-2-1-3-2-4z" class="m"></path><path d="M781 457h4 0c1 1 2 2 2 4-2 1-3 1-5 1-2-1-4 0-6 0v1l-2-2c2-2 4-3 7-4z" class="AK"></path><path d="M792 262l2-1c1 1 1 0 1 1 0 2 1 4 2 5h0c5 6 9 9 18 10 8 1 14 1 21 6-5 0-9-3-13-3h-14l1-1c-5 0-9 0-14 1-1 0-2 1-4 0h-3l-1 2c-1-2-2-4-2-5v-1c0-6 3-9 6-13v-1z" class="X"></path><path d="M792 263c1 1 2 1 1 2 0 4-5 8-7 11 0-6 3-9 6-13z" class="f"></path><path d="M296 618c0 3 0 3 2 6-1 1-2 2-2 4l1-1s1-2 2-2v1l3-3c2-1 5-2 7-3 4 0 8 0 12 1l-1 1h-4c-1 1-1 2-2 3h0c-2 1-3 2-5 3h0c-2 2-4 4-6 7h0c-1 3-1 7 0 10 2 3 4 4 7 6h-4c-3-1-7-4-8-7h-1c0-1-1-3-2-4v-1c-1-3 0-8-1-11 0-2 0-4 1-6h0 0l-3 2h-2l1-1 3-3 2-2z" class="B"></path><path d="M309 620c4 0 8 0 12 1l-1 1h-4c-1 1-1 2-2 3h0c-2 1-3 2-5 3h0-1-1l-1 1c-1 0-1 0-2 1-3 3-5 8-5 12v1h0c-2-2-2-4-2-6 0-4 1-7 4-10l2-3-1-1c2-1 5-2 7-3z" class="Y"></path><path d="M309 620c4 0 8 0 12 1l-1 1h-4c-5 0-8 0-13 2l-1-1c2-1 5-2 7-3z" class="c"></path><path d="M458 876l-1-1-1-2c0-2-1-3-1-4h0 1c3 4 8 8 12 12h0c1 0 1-1 2-1v1c2 1 2 1 2 3v1c2 2 4 4 7 6h0c3 2 4 2 7 2v1h-1c0 2 1 1 2 2 3 1 10 2 12 4h-2l-12-3h-2l-7-2-3-1h-6v-1c-1 0-2 0-4 1-2 0-3 1-5 0v-1-3-1c1-3 1-5 0-8 0-1-1-1-1-2l-1-2 1-1v1l1-1z" class="AN"></path><path d="M458 881c0-1-1-1-1-2l-1-2 1-1v1l1-1c1 2 3 3 4 4 3 4 4 8 8 10 2 1 5 2 6 3h-5-4c-1 0-2 0-4 1-2 0-3 1-5 0v-1-3-1c1-3 1-5 0-8z" class="AQ"></path><path d="M458 881c1 1 2 1 2 2l4 5-6 5v-3-1c1-3 1-5 0-8z" class="AN"></path><path d="M539 870l2-2 1 2c-2 1-4 3-5 5 1 1 1 1 2 1l9 1v2h4c-1 2-5 0-6 3v1c1 1 0 4 0 5-3 4-5 6-9 7-3 1-5 1-7 3h3l1 1h-4l-1 1h-1c1-2 1-2 0-4h0v-7c0-7 7-14 11-19z" class="Aa"></path><path d="M539 870l2-2 1 2c-2 1-4 3-5 5 1 1 1 1 2 1l9 1v2l-10-2v5c1 2 1 3 1 5v1c-1-1-1-3-2-5 0-1 0-3-1-5h-1c-2 1-3 5-4 7h-1c-1 2 0 4 0 6s0 2-1 3v3 2l1-1h3l1 1h-4l-1 1h-1c1-2 1-2 0-4h0v-7c0-7 7-14 11-19z" class="AN"></path><path d="M516 322v1c0 1 0 2 2 3 1 1 1 1 3 1 0-1 1-1 2-1 1-1 2-5 3-6l2 2h0c1 3 0 6-2 9l-3 3c-2 0-3 1-5 1h0v2l2 1-2 1h-3s-1 0-2-1c-1 1-1 2-2 3l-1-1-3 2c-2-2-2-2-5-3h-3s0-1-1-1c1-1 1-1 1-2h0c1-1 2-1 3-2v-1-1c-2-4-3-6-2-10 1 2 3 1 4 3 1 1 1 2 3 3 1 0 1 0 3-1h1 1c0-1 0 0 1-1 0-2 2-3 3-4z" class="j"></path><path d="M510 335c1-1 2-2 3-2l1 1c1-1 2-3 3-3v3l1 1h0l-2 1c-3 0-4-1-6-1z" class="E"></path><path d="M502 332v-1c0-1 0-2-1-3v-5c1 3 3 5 4 7 1 1 1 1 3 1 1 1 1 2 2 3l-2 1c-2-1-4-2-6-2v-1z" class="B"></path><path d="M502 333c2 0 4 1 6 2l2-1v1c2 0 3 1 6 1l2-1v2l2 1-2 1h-3s-1 0-2-1c-1 1-1 2-2 3l-1-1-3 2c-2-2-2-2-5-3h-3s0-1-1-1c1-1 1-1 1-2h0c1-1 2-1 3-2v-1z" class="X"></path><path d="M502 333c2 0 4 1 6 2h1c0 1 1 1 2 1v1h-1v1c-3-1-6-2-8-4v-1z" class="a"></path><path d="M510 334v1c2 0 3 1 6 1l2-1v2l2 1-2 1h-3s-1 0-2-1h-3v-1h1v-1c-1 0-2 0-2-1h-1l2-1z" class="C"></path><path d="M450 304c7 14 16 27 32 32l11 2 3 2h-3l-1 1h0c0 1 0 2-1 3v-1l-1-1 1-1c-1-1-1-1-3-1h-2c-2-1-2-1-3-1h-1-2l-2-1h-2l-7-3-2-1-3-1-2-2c-2-1-3-2-5-3-3-2-5-5-8-8l-3-3c0-2 0-3-1-5h0l2 2 1-1c-2-1-2-2-3-4h1c-1-1-1-1-1-3l1-1 1 2 2-1s0-1 1-2h0z" class="T"></path><path d="M445 312l2 2c2 1 3 2 4 4l1 1-3 1-3-3c0-2 0-3-1-5h0z" class="X"></path><path d="M459 326c2 1 5 3 7 5 1 1 2 3 3 4h0l-2-1-3-1-2-2v-1c-1-1-2-2-3-4z" class="c"></path><path d="M449 320l3-1c2 2 4 5 7 7 1 2 2 3 3 4v1c-2-1-3-2-5-3-3-2-5-5-8-8z" class="H"></path><path d="M384 567c0-2-2-4-3-6h0c-2-3 0-7 0-9 0-1 0-2 1-3v-1c0-1 2-2 2-3l4 12c1 2 2 4 2 5 3 7 5 15 7 22l9 26c3 10 7 21 10 32-1-2-2-3-3-5 0-1 0-2-1-3 0-1 0-3-1-5-1-1-1-3-1-4l-3-9h-1l-2-9c0-1-1-1-1-2l-3-9v-1h-1v-1c0-1 0-2-1-3h0v1c-1 0-1-1-1-2l-2-3v-1c-1-1-2-1-3-3l-1-1v-1c-1 0-1-1-1-1-1-1-1-2-2-3v-1c-1 0-1 0-1-1l-2-2-6-6-1-1-2-2c-1 0-2 0-2-1l-1-1c-2 0-1 1-2 0v-1h1c2 1 4 2 5 3 2 1 3 3 5 4l1-1h1z" class="Y"></path><path d="M384 567c0-2-2-4-3-6h0c-2-3 0-7 0-9 0-1 0-2 1-3v-1c0-1 2-2 2-3l4 12c1 2 2 4 2 5v4c0 2 1 3 1 5 1 2 1 5 2 7-3-3-7-7-9-10v-1z" class="x"></path><path d="M356 583c3 1 5 3 7 4l11 9v-2-1l2-1c1 1 1 2 2 3h0c-1-4-4-6-6-9h1c1 0 1 0 1 1l3 3c2 2 4 6 7 6 4 3 16 19 16 23h1c0 1 1 2 1 3h0-1 0c1 2 3 4 4 6-2-1-17-14-17-15v-1l-15-13h-1l-7-5-9-6v-1h1v-1l-1-3z" class="Q"></path><path d="M356 583c3 1 5 3 7 4l11 9 1 1h1l3 3 1 1c0 1 1 1 2 1 0-1 0 0-1-1v-1l-1-1-1-2h1 1 1l-1 2c0 1 1 2 2 3l-1 1c-3-1-5-3-8-5l-1 1h-1l-7-5-9-6v-1h1v-1l-1-3z" class="U"></path><path d="M587 232h3l3 4 4 5c1 2 4 6 4 8-1 1-1 3-1 5l1 5v10c0 3-1 7-2 10l-1 3-1 8-2 4c-1 1-1 1-1 2l-1 2c0 1-1 2-2 3 0-1 0-2-1-3 0-1 1-3 2-4h-2l-1-2 3-12 2-12c1-4 1-7 1-10l-1-1c1-3 0-5 2-7-2-5-3-9-6-13-1 0-3-4-3-5z" class="c"></path><path d="M592 280l2 1 2-2-4 15h-2l-1-2 3-12z" class="m"></path><path d="M587 232h3l3 4 4 5c1 2 4 6 4 8-1 1-1 3-1 5-1-4-2-7-4-10-1-2-3-6-6-7-1 0-3-4-3-5z" class="I"></path><path d="M596 250c2 10 1 19 0 29l-2 2-2-1 2-12c1-4 1-7 1-10l-1-1c1-3 0-5 2-7z" class="x"></path><path d="M276 786c-3-1-5-3-7-4 3 0 6 2 9 3l9 4c3 1 5 2 8 4 2 1 6 3 8 5 1 1 1 2 3 2 7 5 14 8 22 12v1l3 1v1c1 0 2 1 3 1l2 1c2 0 4 1 6 2s6 1 9 3h-1c3 1 8 1 11 2v1 1l-17-4c-4 0-8-1-11-3l-1 1c-4-1-8-5-12-6v1l6 4c-1 0-2-1-2-1l-1 1 1 1v2c-2-2-3-5-5-6l-1-1v-1l-1-1-1 1c-3-4-7-7-11-10s-10-7-15-9l-1 1c-2-1-4-3-6-5-2-1-5-4-7-5z" class="w"></path><path d="M330 817c3 1 7 1 9 3 2 1 3 1 5 2-4 0-8-1-11-3l-3-2z" class="x"></path><defs><linearGradient id="E" x1="312.258" y1="800.564" x2="304.104" y2="809.539" xlink:href="#B"><stop offset="0" stop-color="#000206"></stop><stop offset="1" stop-color="#212327"></stop></linearGradient></defs><path fill="url(#E)" d="M283 791c2 0 3 0 5 1 1 1 1 1 2 1 6 1 11 6 17 9l23 15 3 2-1 1c-4-1-8-5-12-6v1l6 4c-1 0-2-1-2-1l-1 1 1 1v2c-2-2-3-5-5-6l-1-1v-1l-1-1-1 1c-3-4-7-7-11-10s-10-7-15-9l-1 1c-2-1-4-3-6-5z"></path><path d="M249 513c2 0 5 1 7 0 1 0 1 0 3-1h1v1c1 1 1 1 2 0 1 0 2-1 3-1-2 3-3 6-3 9 1 1 1 2 2 2 1 1 1 2 2 3-1 0-1 0-2-1-1 1-2 1-3 1s-5 9-6 11c0-2 1-3 2-5 1-3 2-5 2-8l1-1v-2h-1v1c-1 1-2 2-3 4s-3 5-5 8h-2l-4 6c-1 1-2 3-3 4 1-2 1-3 2-4s1-1 0-2c1-1 1-1 1-2h-1l-1 2-2 4-1 1c-1 2 0 1-1 2-1 2-2 4-3 7l-4 7-5 12v1l-1 1c0 2 0 3-1 4h0l-1 2c0 1 0 2-1 3v1 1s-1 1-1 2l-1 4c0 1-1 2-1 2 0 1 0 3-1 4v2c-1 1-1 1-1 2v3c-1 0-1 0-1 2v2c-1 1-1 1-1 3v3c-1 1-1 1-1 2-1 2 0 3-1 4h-1v-2c1-1 1-3 1-4v-1c0-4 2-8 3-12v-3c1-1 1-3 1-4 1 0 1-1 1-2s0-2 1-3c0-1 0-3 1-4l1-4c0-1 1-2 1-3 1-1 1-2 1-3l1-1v-1-1c1-1 1-2 1-3h1l1-3v-2l1-1 3-6 1-2v-2c1-1 1 0 1-1l5-8 1-3c1-2 2-3 2-4l7-9c0-1 1-2 1-2 1-1 2-2 2-3v-2h1c-1-3-2-4-4-6v-1z" class="y"></path><path d="M352 569v-1c1-1 1-2 1-3 1 0 1 1 2 2h0l1-1c0-2 0-3-1-4 0-1-1-2-1-3l1-1 4 7 25 31c-3 0-5-4-7-6l-3-3c0-1 0-1-1-1h-1c2 3 5 5 6 9h0c-1-1-1-2-2-3l-2 1v1 2l-11-9c-2-1-4-3-7-4-2-4-4-7-5-11-1-2 0-2 1-3z" class="AA"></path><path d="M352 569v-1c1-1 1-2 1-3 1 0 1 1 2 2h0l1-1c0-2 0-3-1-4 0-1-1-2-1-3l1-1 4 7-1 1c2 2 5 5 4 7l-1 1c-2-1-4-2-6-4l-1 2c2 4 3 8 6 12l3 3c-2-1-4-3-7-4-2-4-4-7-5-11-1-2 0-2 1-3z" class="f"></path><path d="M693 780l4 1c-2 2-3 4-4 7-3 4-6 7-10 11-1 1-2 3-4 3v2l2-2 2 1c0 1 0 1-1 2h2v1c-1 0-2 1-3 1h-4c-1 1-3 1-4 1-3 0-5 1-8 2h-1-9-1-2l-4-1c-1 0-2-1-2-1-2 0-4-1-6-2v-1h-1c-1-1-2-1-2-2-1 0-1 0-1-1h-1l2-1c10 4 22 2 33 0 2-1 5-2 8-4h1c6-4 12-9 14-17z" class="x"></path><path d="M665 808c1-1 2-2 4-3 2 0 4-2 6-2 1 0 2-1 3-1h0 1v2c-1 0-1 1-2 1h0c-1 1-2 1-3 1-2 0-4 1-5 1h-1c-1 1-2 1-3 1z" class="AE"></path><path d="M681 802l2 1c0 1 0 1-1 2h2v1c-1 0-2 1-3 1h-4c-1 1-3 1-4 1-3 0-5 1-8 2h-1-9-1-2l-4-1c-1 0-2-1-2-1-2 0-4-1-6-2v-1l3 1c1 1 2 1 3 1h0 1c2 0 1 0 2 1h4c4 1 9 1 12 0 1 0 2 0 3-1h1c1 0 3-1 5-1 1 0 2 0 3-1h0c1 0 1-1 2-1l2-2z" class="AU"></path><path d="M806 135h1c1 0 1-1 1-2 5-2 16-1 20 0l2 1h1c8 2 16 7 20 14 1 1 1 2 2 3-1 1 0 1-1 1l-1-2c-1-1-2-3-4-3l-2-1c-1-1-3-2-4-3-14-7-31-4-45 1l-1-1c-2 0-3 1-4 0h-1-1l1-1c0-2 0-4 1-6l5-2h1c3 0 6-1 9-1h0v2z" class="AI"></path><path d="M797 134c3 0 6-1 9-1h0v2l-1 1c1 1 3 0 4 0l2-1h2 0c2 0 3 0 5-1l2 1c3 0 7 0 9 1h1c-1 1-2 1-3 1v-1c-2 0-4 1-5 0-2-1-3 0-4 0-2 0-4 0-6 1h-5c-4 1-7 2-11 3-2 1-3 2-5 3h-1-1l1-1c0-2 0-4 1-6l5-2h1z" class="AJ"></path><path d="M796 134h1 1 2l1 1c-2 1-3 1-4 2-3 0-5 3-7 5 0-2 0-4 1-6l5-2z" class="k"></path><path d="M531 864l2-2v-4l1 3c1 1 1 2 1 3v1h2c1 0 2-2 3-2l1 1c1 0 1 1 2 1-1 1-2 2-2 3l-2 2c-4 5-11 12-11 19v7c-2 3-5 5-7 8-2 4-4 9-6 13h0l-1 1v1l-1 1v1-4c0-2-1-4 0-7 0-1 1-2 0-3 0-4 3-6 3-9 0-2 1-3 2-4 2-5 5-10 7-15l3-6c0-3 2-5 2-7 1-1 1-1 1-2z" class="AP"></path><path d="M534 861c1 1 1 2 1 3v1c-2 1-2 3-3 5h-1 0v-1-1l2-2v-1-4h1 0z" class="J"></path><path d="M528 889c-1 0-1-1-1-2-1-1-1-3 0-4l1-2c0-2 1-3 2-5h0c1-1 1-2 2-3 0 1 0 1 1 1 2-2 3-3 6-4-4 5-11 12-11 19z" class="o"></path><path d="M396 817c2 0 6-1 8 0l3 3 1 2-1 4c0 1 1 3 2 4v1l-2 8v1l1 1c-2 1-2 2-2 3l-3 8-3 5h0c-1 2-3 5-3 7 1 0 1 2 1 3 1 1 1 2 2 3h0v1c-2-2-2-3-3-4h-1 0c-1 1-1 1-2 1h-1v-1c4-7 7-16 8-24-3 0-8-1-11 0-1 1-4 3-5 5-1 0-1 1-2 2l-3 3h0v-1c1-1 1-1 1-2v-1l1-2h0-1c-1 0-1-1-1-1h1c3-1 7-3 8-5 1-1 3-1 4-2 1 0 1 1 2 1h0c2 0 3 0 4 1h1 1v-3c0-5 0-9-4-13-4 0-10 2-15 3 2-2 5-2 7-3h0l-3-3h0 1c1-1 2-1 3-2s4-2 6-3z" class="s"></path><path d="M406 832v6l1 1v1l1 1c-2 1-2 2-2 3-1-1-2-2-1-4 0-2 0-5 1-8z" class="AE"></path><path d="M407 826c0 1 1 3 2 4v1l-2 8-1-1v-6c0-2 1-4 1-6z" class="AZ"></path><path d="M557 223c4-4 10-7 15-10l-7-9c3 2 6 4 10 5 6 2 12 1 18-2l2 1-1 2h2 5c2 1 5 2 7 2v1 2l-8-2c2 1 3 1 4 3l1 13v2h0l-2-1v2c0 1 0 2 1 4l-2-2s-1-1-2-1l-3-3c-2-4-5-7-8-10-3-2-6-4-10-5h-1v-1l-9 3c-3 1-6 3-9 5l-1 1h-2z" class="r"></path><path d="M578 214c6-1 14-2 20-1v1h-3c-1-1-3 0-5 0l1 1c1 0 1 0 2 1h-2c-1-1-2-1-3-1l-1 1h0c2 1 3 2 3 4h-1c-3-2-6-4-10-5h-1v-1z" class="S"></path><path d="M598 213h2c2 1 3 1 4 3l1 13v2h0l-2-1v2c0 1 0 2 1 4l-2-2s-1-1-2-1l-3-3c-2-4-5-7-8-10h1c0-2-1-3-3-4h0l1-1c1 0 2 0 3 1h2c-1-1-1-1-2-1l-1-1c2 0 4-1 5 0h3v-1z" class="AB"></path><path d="M591 216h2c5 3 8 8 11 13h1v2h0l-2-1v2c-4-5-8-12-13-16h1z" class="E"></path><path d="M598 213h2c2 1 3 1 4 3l1 13h-1c-3-5-6-10-11-13-1-1-1-1-2-1l-1-1c2 0 4-1 5 0h3v-1z" class="Y"></path><path d="M779 372c2-3 3-6 4-9l1 1v1l2 1c-3 7-3 14-4 22l1 2c-2 7-3 17 0 24 1 4 4 8 8 10 2 1 3 1 5 0 1 0 0 0 1 1 0 1-1 1-3 2-3 2-6 4-8 7s1 11 1 11c-1 3 1 4 3 7 0 1 3 5 5 6l1 2h-1c-1 0-2 0-2-1l-1 2h-1c-2-2-4-3-6-4h0-4c-1-1-1-1-2-1-1-1-2-1-3-2v-4l-1-2 2 1 1-1h0c0-1 0-1 1-1v-1l-1-1h1c1 1 3 1 4 3 1 0 1 0 2-1l-1-4c-2-5-1-9-2-13h0c1-6-1-10-2-15-1-3-1-7-1-10 0-5 1-10 1-15 1-6 1-13-1-18z" class="e"></path><path d="M778 445h1c1 1 3 1 4 3 1 0 1 0 2-1 1 2 3 4 2 7l7 5c1 0 0 0 1-1l1 2h-1c-1 0-2 0-2-1l-1 2h-1c-2-2-4-3-6-4h0-4c-1-1-1-1-2-1-1-1-2-1-3-2v-4l-1-2 2 1 1-1h0c0-1 0-1 1-1v-1l-1-1z" class="AV"></path><path d="M778 445h1c1 1 3 1 4 3 1 0 1 0 2-1 1 2 3 4 2 7-3-2-5-5-8-8l-1-1z" class="j"></path><path d="M775 448l2 1c2 2 5 4 6 7h1l1 1h-4c-1-1-1-1-2-1-1-1-2-1-3-2v-4l-1-2z" class="AM"></path><path d="M231 218l1-1 1 1 1-1h2c2 0 4 1 6 1 0 3 5 5 7 7 0 1 1 2 1 3h0c1 2 0 6-1 8l1 2h1 0 1v2c1-1 1-1 2-1-1 3-2 6-4 8l-2 3h-2c-2 2-3 5-5 6l-1-2-5 6-2-2c2-2 3-3 4-5 0-1-1-2 0-3 0 0 0-1-1-1 2-4 3-8 3-12 0-7-3-14-8-19z" class="U"></path><path d="M249 236l1 2h1 0l-7 11h-1v-2c0-1 5-9 6-11z" class="I"></path><path d="M234 217h2c5 6 7 11 7 19 0 2 0 4-1 6h-1v-2-3c0-6-3-14-8-19l1-1z" class="D"></path><path d="M251 238h1v2c1-1 1-1 2-1-1 3-2 6-4 8l-2 3h-2c-2 2-3 5-5 6l-1-2 1-1 3-4 7-11z" class="T"></path><path d="M252 240c1-1 1-1 2-1-1 3-2 6-4 8l-2 3h-2c1-1 2-2 2-4 1-2 2-4 4-6z" class="B"></path><path d="M231 218l1-1 1 1c5 5 8 13 8 19v3 1l-1 12h1l-1 1-5 6-2-2c2-2 3-3 4-5 0-1-1-2 0-3 0 0 0-1-1-1 2-4 3-8 3-12 0-7-3-14-8-19z" class="z"></path><path d="M237 253c2-2 3-8 4-12l-1 12h1l-1 1-5 6-2-2c2-2 3-3 4-5z" class="E"></path><path d="M292 788l14 4c1 1 3 2 5 2v-1l3 2h1l1 1h1c4 4 10 5 15 6l26 7v-1h11 6c2-1 5-1 7-2h1c0-1 1-1 2-1h0 2c1 1 3 0 4-1l1 2c-7 3-13 5-21 6-6 1-12 1-19-1l-1 2c-5 0-12-3-16-5-3-1-6-1-9-2l-15-6-1 1c-1 0-3-1-4-1-2 0-2-1-3-2-2-2-6-4-8-5-3-2-5-3-8-4l3-1 2 1v-1z" class="x"></path><path d="M303 798c3 0 5 0 8 2l-1 1c-1 0-3-1-4-1-2 0-2-1-3-2z" class="s"></path><path d="M352 811c-5 0-11-1-16-3v-1c5 0 11 2 16 3l1-1 18 3c-6 1-12 1-19-1z" class="AE"></path><path d="M292 788l14 4c1 1 3 2 5 2v-1l3 2h1l1 1h1c4 4 10 5 15 6l1 2-41-15v-1z" class="Am"></path><path d="M332 802l26 7v-1h11 6c2-1 5-1 7-2h1c0-1 1-1 2-1h0 2c1 1 3 0 4-1l1 2c-7 3-13 5-21 6l-18-3-20-5-1-2z" class="Ae"></path><path d="M358 808h11 6c2-1 5-1 7-2h1c0-1 1-1 2-1h0 2c-10 4-19 6-29 4v-1z" class="s"></path><path d="M429 213c6-1 12-1 18 0h3l1 1c1 1 2 2 3 2l1 1c-1 1-1 1-2 1h0c-1 2-2 2-3 3s-3 2-4 4v2h2c-3 3-6 5-9 8h-1v1l-1-1c1-3 2-5 4-8l-1-1-4 4-3 4c-2 2-4 5-6 8h-1l-1 3c-1-2-1-8-1-11v-3-12l1-3h0c1 0 1-2 1-2l3-1z" class="E"></path><path d="M424 219c1 3 0 3 2 5 1 0 1 0 2-1v1l-1 1c-1 3-1 5-1 9l-1 2h0c0-2 0-3-1-5v-12z" class="C"></path><path d="M432 229l1 1v4c-2 2-4 5-6 8h-1v-2c2-4 3-8 6-11z" class="AH"></path><path d="M435 218c1 0 1 1 2 1 0 1-1 1-1 2l1 1-4 5h-1l2-5c-2 2-3 4-4 7l-2 2c0-2 1-4 1-6l-1-1v-1l1-1h1v-2c2-1 3-1 5-2z" class="AL"></path><path d="M435 218c1-1 3-1 5-1l3 1c2-1 4-1 7-1-1 1-2 2-3 2l-1-1c-3 2-5 4-6 7v1l-4 4-3 4v-4l-1-1v-1l1-1 4-5-1-1c0-1 1-1 1-2-1 0-1-1-2-1z" class="F"></path><path d="M432 228l1 1 1 1h1 1l-3 4v-4l-1-1v-1z" class="q"></path><path d="M435 218c1-1 3-1 5-1l3 1c-1 1-2 2-3 2-2 0-2 1-3 2l-1-1c0-1 1-1 1-2-1 0-1-1-2-1z" class="P"></path><path d="M450 217l3 1c-1 2-2 2-3 3s-3 2-4 4v2h2c-3 3-6 5-9 8h-1v1l-1-1c1-3 2-5 4-8l-1-1v-1c1-3 3-5 6-7l1 1c1 0 2-1 3-2z" class="q"></path><path d="M440 226v-1c1-3 3-5 6-7l1 1c0 1 0 3-1 3 0 1-3 3-3 4-1 0-2 1-2 1l-1-1z" class="B"></path><path d="M429 213c6-1 12-1 18 0h3l1 1c1 1 2 2 3 2l1 1c-1 1-1 1-2 1h0l-3-1c-3 0-5 0-7 1l-3-1c-2 0-4 0-5 1-2 1-3 1-5 2v2h-1l-1 1c-1 1-1 1-2 1-2-2-1-2-2-5l1-3h0c1 0 1-2 1-2l3-1z" class="v"></path><path d="M425 216v3l2 1c1 0 2-1 3-2 2 0 4-1 6-1 2-1 4-1 7-2h1l-4 2c-2 0-4 0-5 1-2 1-3 1-5 2v2h-1l-1 1c-1 1-1 1-2 1-2-2-1-2-2-5l1-3h0z" class="D"></path><path d="M447 213h3l1 1c1 1 2 2 3 2l1 1c-1 1-1 1-2 1h0l-3-1c-3 0-5 0-7 1l-3-1 4-2c1 1 3 0 4-1-1 0-1-1-1-1z" class="V"></path><defs><linearGradient id="F" x1="799.5" y1="283.826" x2="798.5" y2="297.174" xlink:href="#B"><stop offset="0" stop-color="#570100"></stop><stop offset="1" stop-color="#800a0a"></stop></linearGradient></defs><path fill="url(#F)" d="M788 282l1-2h3c2 1 3 0 4 0 5-1 9-1 14-1l-1 1h14c-2 1-4 0-6 2-1 0-2 1-3 1-4 2-9 5-11 9-1 1-1 3-1 4l1 4h-1c-2 0-3 0-4-1h-1c0 1-1 1-2 1h-1l-1 1h0c-1 0-2 1-2 1h-1l3-5-7 2-7 7-6 6h-1l-1-1 1-4c1-1 1-1 2-3v-2c1-2 2-3 2-5h1l4-4c1-2 2-3 3-4 1-2 1-3 2-4 2 0 3-1 4-3h-1-1z"></path><path d="M794 300l6-3c1-1 1-1 2-1l1 4h-1c-2 0-3 0-4-1h-1c0 1-1 1-2 1h-1z" class="B"></path><path d="M790 292l2-2c1 0 1 0 2-1 2-1 5-2 7-2-2 2-4 2-6 4-2 1-4 3-5 4h-3l3-3z" class="Q"></path><path d="M809 280h14c-2 1-4 0-6 2-1 0-2 1-3 1-2-1-8 1-10 2-1 1-2 1-3 2h0c-2 0-5 1-7 2-1 1-1 1-2 1l-2 2-1-1h0l3-3c2-2 5-4 7-5 3-2 7-2 10-3z" class="m"></path><path d="M788 282l1-2h3c2 1 3 0 4 0 5-1 9-1 14-1l-1 1c-3 1-7 1-10 3-2 1-5 3-7 5l-3 3h0l1 1-3 3h3l-4 4-7 7-6 6h-1l-1-1 1-4c1-1 1-1 2-3v-2c1-2 2-3 2-5h1l4-4c1-2 2-3 3-4 1-2 1-3 2-4 2 0 3-1 4-3h-1-1z" class="D"></path><path d="M787 295h3l-4 4-7 7c0-2 1-3 2-4l6-7z" class="C"></path><path d="M788 282l1-2h3c2 1 3 0 4 0 5-1 9-1 14-1l-1 1c-3 1-7 1-10 3-11 4-18 13-25 21v-2c1-2 2-3 2-5h1l4-4c1-2 2-3 3-4 1-2 1-3 2-4 2 0 3-1 4-3h-1-1z" class="H"></path><path d="M324 611l8-1c1 0 4 1 6 1 5 4 10 8 13 13 1 2 2 3 3 5l1 3-2 7c-1 2-1 4-2 6 0-1-1-2-1-3h-1c-1 0-5-7-6-8-3-4-7-7-11-9-6-3-12-2-18 0h0c1-1 1-2 2-3h4l1-1c1 0 3 1 5 0-1-1-5-2-7-3-1 1-2 0-3 0v-1c1 0 1-1 2-1l4-2c0-1 1-2 2-3z" class="V"></path><path d="M324 611l8-1c1 0 4 1 6 1-2 0-5 0-7 1-3 0-5 3-9 2h0c0-1 1-2 2-3zm25 27c0-1 0-1 1-2l2 2 1-1v-2c0-2 0-1 1-2h-1c-1 1-2 0-3 0v-2c-1-1-1-1-1-2-1-1 0-1 0-2 0 1 1 2 2 2 1 1 2 1 3 0l1 3-2 7c-1 2-1 4-2 6 0-1-1-2-1-3s0-3-1-4z" class="U"></path><path d="M319 618c2-1 7 1 9 1 7 2 17 11 20 17l1 2c1 1 1 3 1 4h-1c-1 0-5-7-6-8-3-4-7-7-11-9-6-3-12-2-18 0h0c1-1 1-2 2-3h4l1-1c1 0 3 1 5 0-1-1-5-2-7-3z" class="H"></path><path d="M247 430c1-2 3-3 5-4l1 2-3 3h1l2-1v3h2v5h0l-1 1v1c1 2 1 3 1 4h-2c-1 1-2 2-4 3l1 2c-2 2-4 3-6 4v3l1 3-1 1c-1-2-3-4-4-6-10 3-17 9-23 16l-2 2h-2c-1 2-3 4-5 4-1 1-2 3-3 4h-1c3-6 7-12 10-17 2-3 5-7 8-9 1 0 1-1 2-1v-1l1 1-1 1h0c2-1 4-2 5-4h1l6-6 8-8h0v-3c1-1 2-3 3-3z" class="e"></path><path d="M245 444c0-1 1-4 2-5l3-3c0-1 0 0 1-1v1c0 2-1 3-1 4l-2 2-3 2z" class="l"></path><path d="M253 433h2v5h0l-1 1v1c1 2 1 3 1 4h-2l1-1c-1 0 0 0-1-1-1 0-1-1-2-2h0l-2 2h-1l2-2c0-1 1-2 1-4l2-3z" class="X"></path><path d="M249 442l2-2h0c1 1 1 2 2 2 1 1 0 1 1 1l-1 1c-1 1-2 2-4 3l1 2c-2 2-4 3-6 4v-1c1-1 0-2 0-3-1-1-2 0-3 0l4-5 3-2h1z" class="e"></path><path d="M248 442h1c-1 1-4 4-4 6l1 1c1 0 2-2 3-2l1 2c-2 2-4 3-6 4v-1c1-1 0-2 0-3-1-1-2 0-3 0l4-5 3-2z" class="AV"></path><path d="M241 449c1 0 2-1 3 0 0 1 1 2 0 3v1 3l1 3-1 1c-1-2-3-4-4-6-10 3-17 9-23 16l-2 2h-2c6-9 18-17 28-23z" class="Ag"></path><path d="M247 430c1-2 3-3 5-4l1 2-3 3h1c-2 4-6 6-9 9-8 8-17 16-24 24-4 4-7 8-10 12-1 1-2 3-3 4h-1c3-6 7-12 10-17 2-3 5-7 8-9 1 0 1-1 2-1v-1l1 1-1 1h0c2-1 4-2 5-4h1l6-6 8-8h0v-3c1-1 2-3 3-3z" class="Al"></path><path d="M247 430c1-2 3-3 5-4l1 2-3 3-6 5h0v-3c1-1 2-3 3-3z" class="AH"></path><path d="M299 563c1 0 3 1 4 2 1 0 1 1 2 2l1-1h1l2 1 3-3c1 1 2 2 2 3l-1 1c-1 4-2 5-4 7h-1l-1-1c-2 1-1 1-2 2v1 2 4c0 2 0 4-1 6v2c1 0 2-1 3 0v1c-1 0-1 1-1 2l-3 6c0-1-1-1-1-1-2 0-2 0-4-1l-4 5v1h0c-1 3-1 4-1 7l1 3v2h-2c0-5-1-9-3-13l1-1h2c2 0 5-4 6-6l-11-8 4-12c-1-2-1-3-3-5v-2l1-1 5-3 1-1c1 0 2 0 4-1z" class="B"></path><path d="M297 564h2c1 1 2 3 2 5l-1 1h-1l-1 2-3-4c0-1 1-2 2-4z" class="U"></path><path d="M300 570l1-1c0 1 1 2 1 3l1 6v1c-1 2-1 2-3 4l-1-1v-6l-1-4 1-2h1z" class="f"></path><path d="M298 572l1-2h1l1 6h-1-1l-1-4z" class="a"></path><path d="M301 576c0 2 0 2 2 3-1 2-1 2-3 4l-1-1v-6h1 1z" class="P"></path><path d="M303 578c0 6 0 10-3 16 0-6-6-8-7-14l1-1v2c1 2 1 3 2 4l1 1c2-1 2-2 2-3v-1l1 1c2-2 2-2 3-4v-1z" class="N"></path><path d="M289 585c1-1 2-3 3-4v-1h1c1 6 7 8 7 14 0 1 0 0-1 1-4-2-7-5-11-7 0-2 1-2 1-3z" class="x"></path><path d="M299 563c1 0 3 1 4 2 1 0 1 1 2 2l1-1h1l2 1 3-3c1 1 2 2 2 3l-1 1c-1 4-2 5-4 7h-1l-1-1c-2 1-1 1-2 2v1 2l-2-6-1-1c0-1-1-2-1-3 0-2-1-4-2-5h-2c-4 2-6 4-9 7v-2l1-1 5-3 1-1c1 0 2 0 4-1z" class="AC"></path><path d="M312 564c1 1 2 2 2 3l-1 1c-2 2-3 3-6 4l-1 1c-1 0-1 0-2-1v-2c-1-1-1 0-1-2h4c1 0 1 0 2-1l3-3z" class="Af"></path><path d="M302 572l1 1 2 6v4c0 2 0 4-1 6v2c1 0 2-1 3 0v1c-1 0-1 1-1 2l-3 6c0-1-1-1-1-1-2 0-2 0-4-1l-4 5v1h0c-1 3-1 4-1 7l1 3v2h-2c0-5-1-9-3-13l1-1h2c2 0 5-4 6-6l-11-8 4-12c0 4-1 5-2 9 0 1-1 1-1 3 4 2 7 5 11 7 1-1 1 0 1-1 3-6 3-10 3-16l-1-6z" class="Aj"></path><path d="M307 592c-1 0-1 1-1 2l-3 6c0-1-1-1-1-1-2 0-2 0-4-1l9-6z" class="c"></path><path d="M203 334c5-1 11 0 16 1s11 2 16 1v1c-4 0-11 1-15 0-2-1-4-1-5-1l2 2c-1 3-1 3-3 5l-4 4 1 1 1 1c1 0 2 0 3-1h5l1 1-1 1c3 2 3 3 3 6-1 1-1 1-2 1-3-2-8-7-12-7-1 0-3-1-4-2l-2-1c-2-1-3-1-4-2h0-1c-1-1-2-1-3-1-2 0-3 0-4-1-1 0-3 0-4 1h-3l-1 1v1h-6c-2 0-4 1-6 2l-2 2v1c-4 2-6 4-9 6v-3c2-2 3-2 4-5h0v-1-1h1c1 0 2-1 2-2-2 1-3 1-4 2l-1 1v-1h0c12-10 26-13 41-13z" class="m"></path><path d="M177 342v2c2 0 4-1 5-1l1-1h4c1-1 4 0 6 0l1 1h1v1c-2 0-3 0-4-1-1 0-3 0-4 1h-3l-1 1v1h-6c-2 0-4 1-6 2h-1v-1c1-1 2-3 4-3l3-2z" class="S"></path><path d="M195 343h3l3 2h2l3 1c0 1 1 1 2 1h1l-2-2c-2-1-1 0-2-1 0 0-1-1-2-1-1-1 0-1-1-1l-1-1h-2c-1 0-1 0-2-1v-1h5c1 1 3 0 4 1h1c2 0 3 0 4 1s2 1 3 2h0l-4 4 1 1 1 1c1 0 2 0 3-1h5l1 1-1 1c3 2 3 3 3 6-1 1-1 1-2 1-3-2-8-7-12-7-1 0-3-1-4-2l-2-1c-2-1-3-1-4-2h0-1c-1-1-2-1-3-1v-1z" class="C"></path><path d="M203 334c5-1 11 0 16 1s11 2 16 1v1c-4 0-11 1-15 0-2-1-4-1-5-1-5-1-9-1-13-1-5 3-11 3-16 4-4 1-6 2-9 3l-3 2c-2 0-3 2-4 3v1h1l-2 2v1c-4 2-6 4-9 6v-3c2-2 3-2 4-5h0v-1-1h1c1 0 2-1 2-2-2 1-3 1-4 2l-1 1v-1h0c12-10 26-13 41-13z" class="Ak"></path><path d="M164 349c11-9 24-13 38-14-5 3-11 3-16 4-4 1-6 2-9 3l-3 2c-2 0-3 2-4 3v1h1l-2 2v1c-4 2-6 4-9 6v-3c2-2 3-2 4-5h0z" class="AL"></path><path d="M289 796l1-1c5 2 11 6 15 9s8 6 11 10c5 7 8 15 12 22 2 2 3 4 5 5 4 3 9 5 13 6 3 0 5 0 8 1h0c-3 1-11-1-13-1l-2-1c1 2 3 5 3 8h1c1 2 0 3-1 5h0c-1-1-1-1 0-2 0-1 0-2-1-3v-2c0-2-3-3-4-4-2 0-4 0-6 1h-4c-2 1-5 1-7 1h-4c-2 3-3 5-3 8v1 1c-2-2-1-4-1-6h-1c-1 1-2 2-3 2h-1c2-2 4-3 6-6h0c1-1 2-3 2-3l2-6c1-9-3-19-8-26h0c-3-4-6-8-9-10-1-1-2-1-2-2h0c0-1-1-1-1-2h-1c-1-1-2-2-4-2h-1l-1 1c-1-1-1-2-2-3h1v-1z" class="AN"></path><path d="M304 805l1-1c4 3 8 6 11 10 5 7 8 15 12 22-3-2-4-6-6-9-1-2-2-3-3-5s-2-5-4-8c-3-3-7-6-11-9z" class="w"></path><path d="M321 834c1 1 1 2 2 3 2 3 5 7 9 8l2 1-1 1c-2 1-3 0-5 1h-1c-1 1-4 0-5-1-1 0-2-2-2-3 0-2 0-8 1-10z" class="Aa"></path><path d="M289 796l1-1c5 2 11 6 15 9l-1 1h0c-2 0-1 0-2-1 1 4 4 5 6 8l3 3 2 3h0c1 1 1 1 1 2l1 2c1 2 2 4 3 7v2c1 2 1 8 0 10-1 3 0 4-3 6l2-6c1-9-3-19-8-26h0c-3-4-6-8-9-10-1-1-2-1-2-2h0c0-1-1-1-1-2h-1c-1-1-2-2-4-2h-1l-1 1c-1-1-1-2-2-3h1v-1z" class="y"></path><path d="M725 802s1 0 1 1c-1 2-4 4-6 5-12 9-22 21-35 28-14 8-30 10-46 8-4 0-8 0-12-2l-3-3c1-1 5 0 6 0 16 0 32-3 47-8l2-1h0c1 0 1 0 2-1h2c1-1 4-2 5-3h-2c1 0 1-1 2-1h2 1c1-1 2-2 4-2l2-2c1 0 2-1 3-2h1l2-2c1 0 1 0 1-1l21-14z" class="x"></path><path d="M732 521l1-3c1-2 3-3 6-4h0c2 0 3 0 4-1h1c1 0 3 1 4 2-6 1-10-1-14 5-1 2-2 6-2 8 2 8 12 15 19 19 2 1 4 1 5 3-8-2-14-2-21 3-5 4-9 11-9 17-1 5 1 10 4 15l-1 1c-2-1-3-2-4-2-3 3-5 6-6 10-1-2-2-3-3-5v1-1-2c-1-2 0-6 1-8 0-2 1-5 2-7 0-1 1-2 1-3l5-10c1-1 0-1 1-2s2-1 2-3v-1c1-2 2-5 3-7l1-1c-1-2-1-2-2-3v-1c-5-3-11-5-18-7l1-1c2 0 5 1 7 1 3 1 4 1 7 2h0c1 0 2 1 3 2h1c-1-1-1-2-2-3-2-2-3-3-5-4v-1c-1-1-1-2 0-3-1-1-1-1-1-2 1 0 3-1 4-2 1-2 3-2 5-2z" class="f"></path><path d="M724 527c-1-1-1-1-1-2 1 0 3-1 4-2 1-2 3-2 5-2l-2 6-1 1c-1 0-2-1-3-1h0-2z" class="C"></path><path d="M730 541c2 1 5 2 7 3 2 0 7 1 8 2-1 1-2 2-4 2l-5-2h-3l-1 1-1-1 1-1c-1-2-1-2-2-3v-1z" class="Y"></path><path d="M724 527h2l3 4v2c1 1 3 2 4 4l5 5-8-3-10-5 7 2h0c1 0 2 1 3 2h1c-1-1-1-2-2-3-2-2-3-3-5-4v-1c-1-1-1-2 0-3z" class="S"></path><path d="M731 546l1 1 1-1h3l5 2c-6 2-10 5-13 11l-1 1-1-1h-1c1-1 0-1 1-2s2-1 2-3v-1c1-2 2-5 3-7z" class="C"></path><path d="M725 559h1l1 1c-2 3-2 6-3 8 0 3 0 5-1 8v5l-2 4c-1 2-2 3-2 5-1 0-2 0-3-1v1-1-2c-1-2 0-6 1-8 0-2 1-5 2-7 0-1 1-2 1-3l5-10z" class="D"></path><path d="M723 581c0-1-1-1-1-2-2 1-1 2-1 3-1 0-2 1-2 2l-1-1c1-3 1-5 2-7 2-3 2-5 4-8 0 3 0 5-1 8v5z" class="S"></path><path d="M405 788s0-1 1-1l1-1c1-1 1-1 3-2-1 3-3 5-4 7s-3 3-4 5c-3 3-6 6-11 8-1 1-3 2-4 1h-2 0c-1 0-2 0-2 1h-1c-2 1-5 1-7 2h-6-11v1l-26-7c-5-1-11-2-15-6h-1l-1-1c0-1 0-3 2-4h6 0v-1c3 1 5 3 7 3v-1c-2-2-3-3-4-5v-1c3 4 7 7 11 9 3 1 6 2 8 5h1c3 0 6 1 10 1 6 1 13 1 19 0s11-3 17-6l2-1c4-2 7-5 10-8h0c0 1-1 2-1 3h0l2-1z" class="x"></path><path d="M323 791h0c3 2 6 3 9 4l-1 1c-2 0-9-1-11-2h0c-2-1-2-1-3-2h0l6-1z" class="AQ"></path><path d="M326 786c3 4 7 7 11 9 3 1 6 2 8 5-3 0-6-1-8-2-2 0-2-1-3-1-1-1-1-1-2-1v-1c-3-1-6-2-9-4v-1c3 1 5 3 7 3v-1c-2-2-3-3-4-5v-1z" class="AE"></path><path d="M405 788s0-1 1-1l1-1c1-1 1-1 3-2-1 3-3 5-4 7s-3 3-4 5c-3 3-6 6-11 8-1 1-3 2-4 1h-2 0c-1 0-2 0-2 1h-1c-2 1-5 1-7 2h-6-11-2c-2 0-3-1-4-1 0-1 0-1 1-1h7c4 0 9 0 13-1 2 1 4 1 7 1h1c1-1 1-1 3-2s5-3 7-4l3-3c1-1 1 0 1-1v-1c-1 1-2 1-3 0l2-1c4-2 7-5 10-8h0c0 1-1 2-1 3h0l2-1z" class="y"></path><path d="M405 788s0-1 1-1l1-1c1-1 1-1 3-2-1 3-3 5-4 7s-3 3-4 5h-2-1-2 1c2-2 6-5 7-8zm187 16c1 1 4 3 4 5l11 11c2 3 3 6 4 10v7 2c-2-1-6-2-8-1h-2-1-2v-1c1 0 2-1 2-1h0c-1-1-2-1-3-1l1-1-1-1h-1 0c-2 1-4 2-5 3s-2 1-2 2l-3 2-2 1-4 2h-2c0 1 0 1-1 1l-2 1c-3 2-7 4-11 5h0c1-2 4-4 5-5h1c2-2 3-4 4-6h0c0-1 1-2 1-4 1-1 0-3 1-4v-1c0-3-1-6-2-10-1-1-2-2-1-4l3 3c2 1 4 1 7 1l9-1c1-1 2-3 2-5h0c0-1 1-3 0-4 0-1-2-4-2-6h0z" class="AQ"></path><path d="M576 819c2 1 4 1 7 1 1 1 2 1 3 1h2l8 1v1h0c-5 0-9-1-14-1 1 1 2 2 2 3-1 0-1 0-2-1l-1-1c-2-1-3-3-5-4z" class="w"></path><path d="M592 804c1 1 4 3 4 5v1c0 1 1 1 1 1v3c0 1 1 1 1 2-1 2-1 2-3 3 2 1 4 1 5 1 2 0 5 1 6 2-3 1-7 1-10 1h0v-1l-8-1h-2c-1 0-2 0-3-1l9-1c1-1 2-3 2-5h0c0-1 1-3 0-4 0-1-2-4-2-6h0z" class="AN"></path><path d="M574 820c-1-1-2-2-1-4l3 3c2 1 3 3 5 4l1 1c1 1 1 1 2 1 1 1 1 1 1 2 1 1 1 2 2 3h0v2 1c-1 2-3 2-5 4l-1 1v-2c-1-1 0-1 0-3l-1-1c0-2-1-4-2-6 0-3-1-4-3-6h-1z" class="y"></path><defs><linearGradient id="G" x1="314.223" y1="585.735" x2="319.448" y2="603.917" xlink:href="#B"><stop offset="0" stop-color="#230000"></stop><stop offset="1" stop-color="#450706"></stop></linearGradient></defs><path fill="url(#G)" d="M323 582c2 0 4-1 6-1h1c0-2 0-2 1-3h1c-1 3-1 4 0 6 1 0 1 0 1-1l1 4c0 1 1 3 1 4h-1c-1 1-1 1-1 2s1 2 0 3c-1 0-2 0-3 1-2 1-3 3-5 3l-1-1-6 5h0l-1 1h1 0l1 1c-1 0-3 1-4 2h-1c-1 2-2 3-4 4l3-1h1c-2 2-5 4-8 6-2 3-5 5-7 8-1 0-2 2-2 2l-1 1c0-2 1-3 2-4-2-3-2-3-2-6l-2 2v-6l-1-3c0-3 0-4 1-7h0v-1l4-5c2 1 2 1 4 1 0 0 1 0 1 1l3-6c0-1 0-2 1-2v-1l3-3c2 0 3-1 4-2h2c3-2 5-3 7-4z"></path><path d="M323 582c2 0 4-1 6-1h1c0-2 0-2 1-3h1c-1 3-1 4 0 6 1 0 1 0 1-1l1 4c0 1 1 3 1 4h-1c0-2-2-5-4-7s-4-2-7-2z" class="l"></path><path d="M315 595l4-4c3 0 6-1 9 0 1 1 1 2 3 2 0 1 1 1 2 0 0 1 1 2 0 3-1 0-2 0-3 1-2 1-3 3-5 3l-1-1 1-1 1-1c1 0 1-1 1-2v-1h-1c-2-1 0-1-2-1h-4c-2 1-3 2-5 2h0z" class="I"></path><path d="M315 595h0c2 0 3-1 5-2h4c2 0 0 0 2 1h1v1c0 1 0 2-1 2l-1 1-1-1-4 2-2-2c-1 1-3 2-4 3h0c-1 1-2 1-3 1 0-2 2-4 4-6z" class="AL"></path><path d="M314 600h0l1-3h2c1 0 1-1 3-1 1 0 3 1 5 0h1v1l-1 1-1-1-4 2-2-2c-1 1-3 2-4 3h0z" class="AB"></path><path d="M314 586h2c-3 4-7 8-10 13-1 3-3 7-4 10l-1 1h-1c-1 1-2 3-3 5 1-7 3-10 6-15l3-6c0-1 0-2 1-2v-1l3-3c2 0 3-1 4-2z" class="e"></path><path d="M298 598c2 1 2 1 4 1 0 0 1 0 1 1-3 5-5 8-6 15 1-2 2-4 3-5h1c-1 3-3 5-4 7l-1 1-2 2v-6l-1-3c0-3 0-4 1-7h0v-1l4-5z" class="AD"></path><path d="M320 599l4-2 1 1-1 1-6 5h0l-1 1h1 0l1 1c-1 0-3 1-4 2l-6 3-2 1v-2h-1v-1c1-2 2-5 5-8 1 0 2 0 3-1h0c1-1 3-2 4-3l2 2z" class="N"></path><path d="M318 604h0l-1 1h1 0l1 1c-1 0-3 1-4 2l-6 3-2 1v-2l5-4 6-2z" class="I"></path><path d="M320 599l4-2 1 1-1 1-6 5-6 2h-1l1-1h0c1-2 2-3 3-5h0 1l-1 1v1l1 1c1-2 2-3 4-4z" class="u"></path><path d="M315 608h-1c-1 2-2 3-4 4l3-1h1c-2 2-5 4-8 6-2 3-5 5-7 8-1 0-2 2-2 2l-1 1c0-2 1-3 2-4-2-3-2-3-2-6l1-1c1-2 3-4 4-7l1-1 1 2c1 0 2-1 2-2h1v1h1v2l2-1 6-3z" class="U"></path><path d="M315 608h-1c-1 2-2 3-4 4l3-1h1c-2 2-5 4-8 6-2 3-5 5-7 8-1 0-2 2-2 2l-1 1c0-2 1-3 2-4l11-13 6-3z" class="j"></path><defs><linearGradient id="H" x1="745.653" y1="183.238" x2="782.956" y2="214.048" xlink:href="#B"><stop offset="0" stop-color="#140000"></stop><stop offset="1" stop-color="#300101"></stop></linearGradient></defs><path fill="url(#H)" d="M767 160c2 2 3 3 4 6 0 2 0 3 2 4-3 1-5 1-7 4-1 2-2 4-2 5-2 4-2 10-1 14v1c1 2 3 7 4 8 2 3 4 5 6 7h0c-1 0-2 0-3-1 0 1 1 2 2 3h0c1 1 1 2 3 3v1l1 1h-1c-1 1-1 3-2 5-1 3-2 6-2 10-1 1-1 2-1 4l-1-6-1 4-1-1h-1l-1 1s0 1-1 2v2c-1-1-1-3-2-4h-1l-2-6-3-9v-2h0v-2c-1-1-1-1-1-2v-3c-1-2-1-3-1-4-1-5-1-11-1-16 1-1 1-5 1-7h0l1-1v-2l2-3-1-2c0-1 0-2 1-3h1l-1-1c2-1 2-2 2-3l1-1 7-6z"></path><path d="M759 227h1 1v-3h0c0 2 0 3 1 4 1 2 1 2 0 4v1h-1l-2-6z" class="Y"></path><path d="M769 229c-1-1-2-4-2-5v-1l1-1c1-1 1-1 1-2h0l1-1c2 4 0 8 1 12-1 1-1 2-1 4l-1-6z" class="Q"></path><path d="M756 218l3 9 2 6h1c1 1 1 3 2 4v-2c1-1 1-2 1-2l1-1h1l1 1 1-4 1 6c0 4 0 8 1 12 1 2 2 5 3 7l1 2 2 4c1 2 1 3 0 5 1 0 1 1 2 2 1 2 2 3 4 5h1c0-2 1-3 2-4l3-2c1-1 1-2 1-3l2-1v1c-3 4-6 7-6 13v1c0 1 1 3 2 5h1 1c-1 2-2 3-4 3-1 1-1 2-2 4-1 1-2 2-3 4l-4 4h-1c-2 1-3 3-4 5l-2 2v-2l2-3c-1 0-1 0-2 1s-1 1-2 1h-1l1-5v-1-3l-1-1h0c0-3 1-5 1-8 1-8 0-15-2-23-3-7-5-14-9-21l2-1c-2-2-3-5-5-8v-1l1-1h0c1-1 1-2 1-3v-2-2l-1-1c0-1 0-1 1-2z" class="q"></path><path d="M756 225l3 7c-2-1-3-1-5-3l1-1h0c1-1 1-2 1-3z" class="v"></path><path d="M768 283l3-1v2 2l-3 9v-3l-1-1h0c0-3 1-5 1-8z" class="T"></path><path d="M766 260l1-1c3 4 4 13 4 17v6l-3 1c1-8 0-15-2-23z" class="H"></path><path d="M754 229c2 2 3 2 5 3l10 23c-4-5-7-11-10-17-2-2-3-5-5-8v-1z" class="Y"></path><path d="M769 229l1 6c0 4 0 8 1 12 1 2 2 5 3 7l1 2 2 4c1 2 1 3 0 5-7-9-12-22-16-32h1c1 1 1 3 2 4v-2c1-1 1-2 1-2l1-1h1l1 1 1-4z" class="I"></path><path d="M766 233h1c1 3 1 5 1 8h-1c0-2-1-2-2-4 0-2 1-3 1-4z" class="C"></path><path d="M790 263l2-1v1c-3 4-6 7-6 13v1c0 1 1 3 2 5h1 1c-1 2-2 3-4 3-1 1-1 2-2 4-1 1-2 2-3 4l-4 4h-1c-2 1-3 3-4 5l-2 2v-2l2-3c5-7 8-13 10-21 0-2 0-4 1-6h1c0-2 1-3 2-4l3-2c1-1 1-2 1-3z" class="AV"></path><path d="M790 263l2-1v1c-3 4-6 7-6 13v1h0c-1 2-2 4-2 6-1 3-2 5-4 7l-3 6-1-1c3-5 6-10 7-16h0l-1-1c0-2 0-4 1-6h1c0-2 1-3 2-4l3-2c1-1 1-2 1-3z" class="AG"></path><path d="M325 600c2 0 3-2 5-3 1-1 2-1 3-1 1 1 1 2 2 4h1v2c1 1 3 0 5 1h7l-4-2h2 1 1c0 1 1 1 2 1h1c1 1 2 0 3 0v1c2 0 4-1 5 0 1 0 2 0 3 1 1 0 2 0 3 1 1-1 1-1 2 0 1 0 2-1 3 0h1c1 1 1 1 2 1 1 1 2 1 3 1v1c1 0 1 0 2 1l-1 2c-1-1-2-1-3-1l-1 1c-1 1-1 1-2 1v1-2-1h-1c-1 1-2 1-3 2v1 1l-2 5c-2 2-3 5-4 7-2 1-3 3-4 5l-1-1-1 2-1-3c-1-2-2-3-3-5-3-5-8-9-13-13-2 0-5-1-6-1l-8 1c-1 1-2 2-2 3l-4 2c-1 0-1 1-2 1v1c1 0 2 1 3 0 2 1 6 2 7 3-2 1-4 0-5 0-4-1-8-1-12-1-2 1-5 2-7 3l-3 3v-1c2-3 5-5 7-8 3-2 6-4 8-6h-1l-3 1c2-1 3-2 4-4h1c1-1 3-2 4-2l-1-1h0-1l1-1h0l6-5 1 1z" class="AK"></path><path d="M342 610c1 0 3 1 5 1s10 0 12 1v1c0 1 1 1 0 2h0c-1-1-2-1-2-2h-1c-3-1-4-1-7 0h-5c-2-1-2-2-2-3z" class="D"></path><path d="M349 613c3-1 4-1 7 0h1c0 1 1 1 2 2h0l-3 9c-1-3-1-5 0-8 0-1-1-1-1-2h-3l-1 1-2-2z" class="Q"></path><path d="M367 612v1 1l-2 5c-2 2-3 5-4 7-2 1-3 3-4 5l-1-1c0-2 1-4 2-5 3-5 5-9 9-13z" class="E"></path><path d="M344 613h5l2 2 1-1h3c0 1 1 1 1 2-1 3-1 5 0 8l-1 1c-1 0-2-1-3-1-1-4-6-8-8-11z" class="P"></path><path d="M306 617l2 2c1-1 2-1 3-2l2-2 1-1c1-1 2-1 4-2 1-1 1-1 2-1 1-1 2-1 3-1 1-1 2 0 3 0v-1h2l1 1 3-1 1-1h2c1 2 2 2 4 3-1-1-1-1 0-2 1 0 1 0 3 1 0 1 0 2 2 3 2 3 7 7 8 11h-1c-3-5-8-9-13-13-2 0-5-1-6-1l-8 1c-1 1-2 2-2 3l-4 2c-1 0-1 1-2 1v1c1 0 2 1 3 0 2 1 6 2 7 3-2 1-4 0-5 0-4-1-8-1-12-1-2 1-5 2-7 3l-3 3v-1c2-3 5-5 7-8z" class="Y"></path><path d="M309 620c4-4 10-7 15-9-1 1-2 2-2 3l-4 2c-1 0-1 1-2 1v1c1 0 2 1 3 0 2 1 6 2 7 3-2 1-4 0-5 0-4-1-8-1-12-1z" class="P"></path><path d="M325 600c2 0 3-2 5-3 1-1 2-1 3-1 1 1 1 2 2 4h1v2c1 1 3 0 5 1h7l16 2v1l-4-1c-16-1-31-1-46 6h-1l-3 1c2-1 3-2 4-4h1c1-1 3-2 4-2l-1-1h0-1l1-1h0l6-5 1 1z" class="X"></path><path d="M325 600c2 0 3-2 5-3 1-1 2-1 3-1 1 1 1 2 2 4h1v2c1 1 3 0 5 1-8 0-15 1-22 3l-1-1h0-1l1-1h0l6-5 1 1z" class="S"></path><path d="M325 600c2 0 3-2 5-3 1-1 2-1 3-1 1 1 1 2 2 4l-1 1-11 1h0l2-2z" class="M"></path><path d="M236 249c1 0 1 1 1 1-1 1 0 2 0 3-1 2-2 3-4 5l2 2 5-6 1 2-1 2c-2 1-3 4-5 6l-3 6h1c1 4-1 8 1 11v4l4 6-1 3 3 5c1 1 3 3 3 4 0 2 0 3 1 4l-1 1c0-1-1-2-1-3l-2-2c-1-1-2-2-4-2l3 3h0c1 1 1 1 1 2l-1 1 1 1h-1 0 0c-1 0-2 0-3-1-3-2-5-4-8-8-4-3-7-9-12-11-2-2-6-2-8-3l4 5-3-1c-3-2-6-5-10-7h-2l-8-2c4-1 9-1 13-1-4-2-8 0-11-2 7-2 15-1 21-7 4-3 5-6 6-11l-1-1v-1c2 0 3 0 4 1 2 0 4 0 6-1 4-1 7-5 9-8z" class="P"></path><path d="M199 282c3-1 6 1 8 2l11 3c2 1 3 2 4 3 1 3 3 4 5 6v1l1 2c-4-3-7-9-12-11-2-2-6-2-8-3l4 5-3-1c-3-2-6-5-10-7z" class="N"></path><path d="M218 259h1c1 3 0 5-1 8h-1c-2 4-4 5-8 7 2 1 6 0 8 0-2 2-6 2-9 2 1 1 0 1 1 2 6 2 10 6 15 10 3 2 7 5 8 8l4 4v1l3 3h0c1 1 1 1 1 2l-1 1c-6-7-11-15-18-21-6-4-12-6-19-7-4-2-8 0-11-2 7-2 15-1 21-7 4-3 5-6 6-11z" class="AV"></path><path d="M209 274c2 1 6 0 8 0-2 2-6 2-9 2 1 1 0 1 1 2-2 0-5-1-7-1l7-3h0z" class="AC"></path><path d="M236 249c1 0 1 1 1 1-1 1 0 2 0 3-1 2-2 3-4 5l2 2 5-6 1 2-1 2c-2 1-3 4-5 6-3 1-4 5-8 6 0 0-1 1-2 1s-3 1-5 2l-3 1h0c-2 0-6 1-8 0 4-2 6-3 8-7h1c1-3 2-5 1-8h-1l-1-1v-1c2 0 3 0 4 1 2 0 4 0 6-1 4-1 7-5 9-8z" class="X"></path><path d="M240 254l1 2-1 2c-2 1-3 4-5 6-3 1-4 5-8 6 0 0-1 1-2 1l1-1c4-3 6-6 9-10l5-6z" class="AG"></path><path d="M236 249c1 0 1 1 1 1-1 1 0 2 0 3-1 2-2 3-4 5-2 1-5 5-8 7 1-2 4-5 4-7h-2l-1 1c-2 0-3 0-5-1 2 0 4 0 6-1 4-1 7-5 9-8z" class="e"></path><path d="M235 264l-3 6h1c1 4-1 8 1 11v4l4 6-1 3 3 5c1 1 3 3 3 4 0 2 0 3 1 4l-1 1c0-1-1-2-1-3l-2-2c-1-1-2-2-4-2v-1l-4-4c-1-3-5-6-8-8-5-4-9-8-15-10-1-1 0-1-1-2 3 0 7 0 9-2h0l3-1c2-1 4-2 5-2s2-1 2-1c4-1 5-5 8-6z" class="AD"></path><path d="M231 282l1 1h1 0c1 1 1 1 1 2l4 6-1 3-6-12z" class="E"></path><path d="M232 270h1c1 4-1 8 1 11v4c0-1 0-1-1-2h0-1l-1-1c-2-4-1-8 1-12z" class="B"></path><path d="M431 305c1 1 1 1 1 2l2 6 1 2 1-1v-6l1-1c1 1 2 4 4 6v-2l5 6 3 3c3 3 5 6 8 8 2 1 3 2 5 3l2 2 3 1 2 1 7 3h2l2 1h2 1c1 0 1 0 3 1h2c2 0 2 0 3 1l-1 1 1 1v1c1-1 1-2 1-3h0l1-1h3c2 0 4 1 6 2h2v3h-3c0 1 0 1 1 1l-1 1v4l-1 1-1-2v-3h-2 0c-5 2-16 3-21 2l-6-2-8-2h0c-6-1-12-4-17-7-2-1-4-2-7-3-1-1-2-2-4-3-1 0-2-1-3-2l-2-2-1-2v-1l-3-10c1-3 4-5 5-8 1-1 0-1 1-2z" class="AM"></path><path d="M459 338h3 2c2 1 4 2 6 2l-3 1c1 0 1 1 3 0l-1 1-10-3v-1z" class="AS"></path><path d="M455 336l-1-1-3-3 1-1 12 7h-2-3c-2 0-3-1-4-2z" class="r"></path><path d="M437 307c1 1 2 4 4 6h0l2 2 1 1 1 2s1 1 1 2c3 4 8 8 10 12-2-2-4-3-6-4-1-1-2-2-4-2 0-1-1-2-2-3h0l2-1c-1-2-1-3-2-4l-3-3-5-7 1-1z" class="l"></path><path d="M462 345v-1c-1-1-3-2-4-2l-7-6 4 2c1 0 2 1 3 1h1l10 3 6 3c4 1 8 1 12 2l4-1c1 0 1 0 1-1l-1-1c1-1 1-2 1-3h0l1-1h3c2 0 4 1 6 2h2v3h-3c0 1 0 1 1 1l-1 1v4l-1 1-1-2v-3h-2 0c-5 2-16 3-21 2l-6-2-8-2h0z" class="X"></path><path d="M491 344c1-1 1-2 1-3h0l1-1h0v1c1 1 1 2 2 3 0 1 1 2 0 3-2 1-6 0-8 0l4-1c1 0 1 0 1-1l-1-1z" class="e"></path><path d="M496 340c2 0 4 1 6 2h2v3h-3c0 1 0 1 1 1l-1 1v4c0-2 0-3-1-4 0-1-1-2-1-3-1-1-5-3-6-4h0 3z" class="u"></path><path d="M431 305c1 1 1 1 1 2l2 6 1 2 1-1v-6l5 7 3 3c1 1 1 2 2 4l-2 1h0c1 1 2 2 2 3h0c1 1 6 5 6 5l-1 1 3 3 1 1c1 1 2 2 4 2v1h-1c-1 0-2-1-3-1l-4-2 7 6c1 0 3 1 4 2v1c-6-1-12-4-17-7-2-1-4-2-7-3-1-1-2-2-4-3-1 0-2-1-3-2l-2-2-1-2v-1l-3-10c1-3 4-5 5-8 1-1 0-1 1-2z" class="e"></path><g class="H"><path d="M444 318c1 1 1 2 2 4l-2 1-2-2h0v4l-1 2v1l-1-2h0c1-1 0-3-1-4 1-1 1-2 2-3 1 0 1 1 2 1l1-2z"></path><path d="M450 334c-3-1-4-3-6-5 0-2-2-4-1-6h1c1 1 2 2 2 3h0l-1 1c0 2 3 5 5 7z"></path></g><path d="M450 334c-2-2-5-5-5-7l1-1c1 1 6 5 6 5l-1 1 3 3 1 1c-2-1-4-1-5-2z" class="AS"></path><path d="M436 308l5 7 3 3-1 2c-1 0-1-1-2-1-1 1-1 2-2 3 1 1 2 3 1 4-2-3-4-7-5-11l1-1v-6z" class="M"></path><path d="M441 315l3 3-1 2c-1 0-1-1-2-1-1 1-1 2-2 3v-5h0l1-1 1-1z" class="AW"></path><path d="M432 322c0-2-1-4-1-5l-1-1h0c1-2 0-7 2-9l2 6-2-2c0 1-1 2 0 3l1 1-1 1c0 2 1 6 2 8h0c1 2 2 3 3 4v3 1 1l-2-1h-1c-1 0-2-1-3-2l-2-2-1-2c1 0 2 0 3 1l1-1-1-3 1-1z" class="l"></path><path d="M432 322l1 1c0 1-1 2 0 3l1 1h1c1 1 1 2 1 3-1 0-1 1-1 1-1 0-2-1-3-1h-1l-2-2-1-2c1 0 2 0 3 1l1-1-1-3 1-1z" class="H"></path><path d="M431 305c1 1 1 1 1 2-2 2-1 7-2 9h0l1 1c0 1 1 3 1 5l-1 1 1 3-1 1c-1-1-2-1-3-1v-1l-3-10c1-3 4-5 5-8 1-1 0-1 1-2z" class="F"></path><path d="M209 289l3 1 3 3c2 1 3 3 4 5 0 0 1 2 2 2 2 1 3 4 3 6 1 2 2 5 2 7 1 4 2 8 1 11-1 2-1 4-3 6h0c-1 2-1 4-2 5h-3c-5-1-11-2-16-1-15 0-29 3-41 13h0s-3 3-4 3v-1c1-2 2-3 4-4 1-1 3-2 4-3 4-3 8-6 12-10 6-5 11-10 18-15v-1c4-3 9-7 13-11l2-4 1-3c0-4-1-6-3-9z" class="AD"></path><path d="M209 305v1l-4 4c-3 5-7 9-9 15 0 1 0 3-1 4l-1-1c1-3 1-5 3-7 1-2 2-4 2-6-1 0-2 1-3 2v-1c4-3 9-7 13-11z" class="Ak"></path><path d="M211 301l1 1 1-1c1 0 2 0 4 1l1 5c1 1 1 3 1 5s1 4 1 7c1 3 1 4-1 7v1h-1c-1-1-2-1-3-2-1 0-1 1-2 1 0 1-1 2-2 3s-1 0-2 0c1-1 1-1 1-2 1-2 0-5 0-7 1-1 1-4 1-5v-4c1-1 1-1 1-2v-2-1l-1 1-2-1v-1l2-4z" class="AM"></path><path d="M213 301c1 0 2 0 4 1l1 5c1 1 1 3 1 5s1 4 1 7c-1-3-1-5-2-8h-1c-2-1-1-5-1-6s-1-1-1-1h1v-1c-1 0-2-1-3-2z" class="T"></path><path d="M209 289l3 1 3 3c2 1 3 3 4 5 0 0 1 2 2 2 2 1 3 4 3 6 1 2 2 5 2 7 1 4 2 8 1 11-1 2-1 4-3 6h0c-1 2-1 4-2 5h-3c-5-1-11-2-16-1v-1l-1-1h4l3-1h2c2 0 3 0 4-1l3-3h1v-1c2-3 2-4 1-7 0-3-1-5-1-7s0-4-1-5l-1-5c-2-1-3-1-4-1l-1 1-1-1 1-3c0-4-1-6-3-9z" class="X"></path><path d="M218 307l3 2 1 1c0 3 1 7 1 10h-1v4c0 2-1 4-1 5-2-1-2-1-3-1-2 2-3 3-5 4h-1c-1 1-4 0-6 0l3-1h2c2 0 3 0 4-1l3-3h1v-1c2-3 2-4 1-7 0-3-1-5-1-7s0-4-1-5z" class="e"></path><path d="M221 300c2 1 3 4 3 6 1 2 2 5 2 7 1 4 2 8 1 11-1 2-1 4-3 6h0-1c1-3 1-8 0-10 0-3-1-7-1-10l-1-1v-3l2-1-2-5z" class="AF"></path><path d="M223 305c0 2 1 4 1 6-1 0-2 0-2-1l-1-1v-3l2-1z" class="AB"></path><path d="M222 310c0 1 1 1 2 1 2 6 2 13 0 19h0-1c1-3 1-8 0-10 0-3-1-7-1-10z" class="F"></path><path d="M209 289l3 1 3 3c2 1 3 3 4 5 0 0 1 2 2 2l2 5-2 1v3l-3-2-1-5c-2-1-3-1-4-1l-1 1-1-1 1-3c0-4-1-6-3-9z" class="B"></path><path d="M212 298v-4c2 1 4 4 6 7 1 1 2 3 3 5v3l-3-2-1-5c-2-1-3-1-4-1l-1 1-1-1 1-3z" class="H"></path><path d="M324 822v-2l-1-1 1-1s1 1 2 1l-6-4v-1c4 1 8 5 12 6l1-1c3 2 7 3 11 3l17 4 5 1c2 0 4 1 6 1h2c2 0 6 1 8 0 5-1 11-3 15-3 4 4 4 8 4 13v3h-1-1c-1-1-2-1-4-1h0c-1 0-1-1-2-1-1 1-3 1-4 2-1 2-5 4-8 5h-1s0 1 1 1h1 0l-1 2v1c-5 0-10 0-14-1-1-1-3-1-5-1-1 1-2 0-3 1h-1-2c1-1 2-1 3-1l2-1 8-1 1-1 7-2c-6-1-12 0-18-1-13-2-28-10-35-20z" class="x"></path><path d="M373 847s-2 0-2-1l11-3c0-1 2-1 3-1h1l-8 5h-5z" class="Aa"></path><path d="M389 841c-1 2-5 4-8 5h-1s0 1 1 1h1 0l-1 2v1c-5 0-10 0-14-1v-1c2 0 3 0 5 1h6v-1c-2 0-3 0-4-1h-1 5l8-5c1 0 3-1 3-1z" class="w"></path><path d="M349 828c4 0 9 2 13 4 5 1 10 1 14 1 5 1 9 1 14 1l2 1-2 1c-10 1-19-1-29-4-4 0-8-1-12-3v-1z" class="AZ"></path><path d="M333 819c3 2 7 3 11 3l17 4 5 1c2 0 4 1 6 1h2c2 0 6 1 8 0 5-1 11-3 15-3 4 4 4 8 4 13v3h-1-1c-1-1-2-1-4-1l2-1 1-2c-1 0-2 0-3-1l-1 1v-1h0l1-2h0c-1 0-2 1-3 1h0l-2-1c-5 0-9 0-14-1-4 0-9 0-14-1-4-2-9-4-13-4l-2-1-4-1c-1-1-3-1-4-2l-7-4 1-1z" class="Aa"></path><path d="M395 836c2-1 1-2 3-3h0c1 2 1 2 0 4h0c-1 0-2 0-3-1z" class="s"></path><path d="M347 827h4 0c2 0 3 0 5 1 4 1 7 1 11 2h12c1 0 4 0 5 1h3l3 3c-5 0-9 0-14-1-4 0-9 0-14-1-4-2-9-4-13-4l-2-1z" class="AQ"></path><path d="M232 371c1 1 3 2 4 3 5 4 10 9 13 14-1 1-2 1-3 2h1c0 1 1 2 2 3 0 1 0 0 1 2l3 3 1 3c2 3 4 5 5 8 1 1 2 3 3 5-1 1-1 2-3 3h0l-1 3 1 1-2 2-4 5-1-2c-2 1-4 2-5 4-1 0-2 2-3 3v3h0l-8 8-6 6c0-1 0-2 1-3-1-1-1-2-1-3 2-2 3-4 4-6h0v-5h0l-1 3h-1v-3l-1 1c-1-1 0-3 0-5h-1c-1-1-2-1-3-2l1-2c3-3 4-7 6-11 1-2 1-4 2-6l-1-7c-2-3-2-9-3-12h2c0-4 0-8-1-12 0-2 0-3-1-5v-1z" class="I"></path><path d="M239 406l2-2c0-2 1-4 2-6v5 1c-1 2-2 5-3 8v1 1c-2 0-2 0-3 2h-1c2-3 2-7 3-10z" class="B"></path><path d="M228 425c3-3 4-7 6-11v3c0 2-1 2-1 4 1-1 1-1 1-2s1-2 2-3h0 1c0 3-3 5-3 7-1 2-2 4-3 5-2 0-2-1-3-3z" class="E"></path><path d="M240 394c0-3 1-6 1-9l5 5h1c0 1 1 2 2 3 0 1 0 0 1 2l-1-1c-1 1-3 1-5 0h0-1c-1 1-2 2-3 4v-4z" class="P"></path><path d="M240 394l1-4c1 0 2 1 3 1 1 1 4 2 5 3-1 1-3 1-5 0h0-1c-1 1-2 2-3 4v-4z" class="V"></path><path d="M243 394h1 0c2 1 4 1 5 0l1 1 3 3 1 3c-1-1-2-3-4-3h-3-2l-1-1-1 1c-1 2-2 4-2 6l-2 2c0-3 1-6 1-8 1-2 2-3 3-4z" class="AA"></path><path d="M243 394h1 0c2 1 4 1 5 0l1 1 3 3 1 3c-1-1-2-3-4-3l-1-1h-1c-2 0-2 0-3-1s0-1-1-2h-1z" class="f"></path><path d="M232 371c1 1 3 2 4 3-1 3-1 5-1 8 0 8 3 18 1 26l-1-7c-2-3-2-9-3-12h2c0-4 0-8-1-12 0-2 0-3-1-5v-1z" class="u"></path><path d="M243 398l1-1 1 1h2 3c2 0 3 2 4 3 2 3 4 5 5 8l-2-2-1-1c-1 0-2 0-3 1h-1l-1-2c0-1-1-1-2-3l-2 1h0c-1-1-2-1-3-1l-1 2v-1-5z" class="B"></path><path d="M243 398l1-1 1 1v1c1 1 1 1 1 2l1 1c1-1 1-1 1-2h1s0 1 1 1c2 1-1 0 2 1h1c0 2 1 2 3 4-1 0-2 0-3 1h-1l-1-2c0-1-1-1-2-3l-2 1h0c-1-1-2-1-3-1l-1 2v-1-5z" class="t"></path><path d="M243 404l1-2c1 0 2 0 3 1h0l2-1c1 2 2 2 2 3l1 2h1c1-1 2-1 3-1l1 1v1h-2l-1 2h0l-4 3c1 1 1 2 1 3l-2-3-1 1h-1c-1-1-3 0-4-1 0 1 0 0-1 1-1-1 0-1-2-1v-1l3-8z" class="c"></path><path d="M251 405l1 2h1c1-1 2-1 3-1l1 1v1h-2l-1 2h0l-4 3v-1l1-1v-1l-1-1c0-1 1-2 1-3v-1z" class="j"></path><path d="M243 404l1-2c1 0 2 0 3 1h0l2-1c1 2 2 2 2 3-1 1-2 3-2 5l-1-1c0-1 0-3 1-4h-1c-1 1-2 3-3 5l-1 1h-1v2c0 1 0 0-1 1-1-1 0-1-2-1v-1l3-8z" class="q"></path><path d="M257 407l2 2c1 1 2 3 3 5-1 1-1 2-3 3h0l-1 3 1 1-2 2-4 5-1-2c-2 1-4 2-5 4 0-1 0-1 1-2 0-1 0-2 1-3 0-1 1-1 1-2 1-1 0-2 1-3v-4c0-1 0-2-1-3l4-3h0l1-2h2v-1z" class="c"></path><path d="M253 424c2-2 4-5 6-7l-1 3 1 1-2 2-4 5-1-2 1-2z" class="F"></path><path d="M257 407l2 2c1 1 2 3 3 5-1 1-1 2-3 3h0v-2s-1-1-1-2c0-2 0-3-1-5v-1z" class="u"></path><path d="M254 410h1l1 1s0 1 1 2v4h-1l-1 2c-1 1-2 4-2 5l-1 2c-2 1-4 2-5 4 0-1 0-1 1-2 0-1 0-2 1-3 0-1 1-1 1-2 1-1 0-2 1-3v-4c0-1 0-2-1-3l4-3z" class="e"></path><path d="M240 413c2 0 1 0 2 1 1-1 1 0 1-1 1 1 3 0 4 1h1l1-1 2 3v4c-1 1 0 2-1 3 0 1-1 1-1 2-1 1-1 2-1 3-1 1-1 1-1 2-1 0-2 2-3 3v3h0l-8 8-6 6c0-1 0-2 1-3-1-1-1-2-1-3 2-2 3-4 4-6h0v-5h0l-1 3h-1v-3l-1 1c-1-1 0-3 0-5h-1c-1-1-2-1-3-2l1-2c1 2 1 3 3 3 1-1 2-3 3-5 0-2 3-4 3-7 1-2 1-2 3-2v-1z" class="T"></path><path d="M234 438l3-6c1 2 0 3-1 5v1c-2 4-3 6-5 9-1-1-1-2-1-3 2-2 3-4 4-6h0z" class="Af"></path><path d="M234 423l2 1c0-1 0-2 1-3v-1l1 1h2v1 2c0 1-1 2-2 3h0v-2-1l-1 1c-1 0-1 0-2 1h0c0 2-2 3-2 4h-1v-1-1l-1 1h-1c-1-1-2-1-3-2l1-2c1 2 1 3 3 3 1-1 2-3 3-5z" class="l"></path><path d="M240 413c2 0 1 0 2 1 1-1 1 0 1-1 1 1 3 0 4 1h1l1-1 2 3v4c-1-1-2-3-3-4s-2-1-3-1c-2 1-4 7-5 9v-2-1h-2l-1-1v1c-1 1-1 2-1 3l-2-1c0-2 3-4 3-7 1-2 1-2 3-2v-1z" class="j"></path><path d="M212 290l-4-5c2 1 6 1 8 3 5 2 8 8 12 11 3 4 5 6 8 8 1 1 2 1 3 1h0 0 1l-1-1 1-1c0-1 0-1-1-2h0l-3-3c2 0 3 1 4 2l2 2c0 1 1 2 1 3l1-1c-1-1-1-2-1-4 1 2 1 3 3 5l3 5 5 7c1 5 6 11 8 15 1 2 2 3 2 5 0 1 1 1 1 2 1 2 2 3 3 5h1c1 5 4 5 7 8h-3c1 1 2 3 3 4 1 2 2 3 2 5l-8-8-1-1-1-2c-3 0-5-2-7-3-4-2-7-7-10-10-3-2-6-2-9-3-2-1-4-1-7-1-5 1-11 0-16-1h3c1-1 1-3 2-5h0c2-2 2-4 3-6 1-3 0-7-1-11 0-2-1-5-2-7 0-2-1-5-3-6-1 0-2-2-2-2-1-2-2-4-4-5l-3-3z" class="u"></path><path d="M236 307c1 1 2 1 3 1h0 0 1c3 3 5 6 7 9s4 6 6 10l-1-1c-1 0 0 0-1-1 0 1 0 2-1 2s-13-17-14-20z" class="V"></path><path d="M215 293h2c3 2 5 3 7 5 2 3 4 5 6 6v1c1 2 2 3 3 4 2 2 4 5 7 7 2 3 4 4 5 7l1 1c5 4 6 12 12 15l1 2c-1 0-1-1-2-1v-1c-3-1-4-3-5-5l-3-3c0-1-1-2-2-3s-1-3-2-4c0-1-1-1-1-2h-1c0-1-1-2-2-2-1-1-2 0-3-1v-1c-1 0-2-1-2-2-2-2-3-4-4-6h-1l1 2c-1 0-2 1-2 2s0 0-1 1v-1l-3-1c0-2-1-5-2-7 0-2-1-5-3-6-1 0-2-2-2-2-1-2-2-4-4-5z" class="AW"></path><path d="M215 293h2c3 2 5 3 7 5 0 2 0 3 1 4 1 2 1 4 2 7 1 2 2 3 2 5l-3-1c0-2-1-5-2-7 0-2-1-5-3-6-1 0-2-2-2-2-1-2-2-4-4-5z" class="AR"></path><path d="M236 301c2 0 3 1 4 2l2 2c0 1 1 2 1 3l1-1c-1-1-1-2-1-4 1 2 1 3 3 5l3 5 5 7c1 5 6 11 8 15 1 2 2 3 2 5 0 1 1 1 1 2 1 2 2 3 3 5h1c1 5 4 5 7 8h-3l-7-9c-1-1-1-2-2-2-3-1-3-1-5-3l-1-2c-2-4-6-8-8-12 1 0 1-1 1-2 1 1 0 1 1 1l1 1c-2-4-4-7-6-10s-4-6-7-9l-1-1 1-1c0-1 0-1-1-2h0l-3-3z" class="AG"></path><path d="M250 327c1 0 1-1 1-2 1 1 0 1 1 1l1 1c1 1 2 3 3 4 2 5 5 9 8 13-3-1-3-1-5-3l-1-2c-2-4-6-8-8-12z" class="B"></path><path d="M236 301c2 0 3 1 4 2l2 2c0 1 1 2 1 3l1-1c-1-1-1-2-1-4 1 2 1 3 3 5l3 5 1 4c1 3 2 4 3 6s2 3 2 4l1 1v3c-1-1-2-3-3-4-2-4-4-7-6-10s-4-6-7-9l-1-1 1-1c0-1 0-1-1-2h0l-3-3z" class="AF"></path><path d="M236 316c0 1 1 2 2 2v1c1 1 2 0 3 1 1 0 2 1 2 2h1c0 1 1 1 1 2 1 1 1 3 2 4s2 2 2 3l3 3c1 2 2 4 5 5v1c1 0 1 1 2 1 2 2 2 2 5 3 1 0 1 1 2 2l7 9c1 1 2 3 3 4 1 2 2 3 2 5l-8-8-1-1-1-2c-3 0-5-2-7-3-4-2-7-7-10-10-1-1-2-2-2-3-2-4-5-6-7-10v-1c-3-3-5-6-6-10z" class="l"></path><path d="M249 337c1 0 1 0 1 1l3 3 1-1c2 2 3 3 5 4 1 1 1 1 2 1v1h0c3 0 6 4 7 6 1 1 1 2 1 3l-1-2c-3 0-5-2-7-3-4-2-7-7-10-10-1-1-2-2-2-3z" class="T"></path><path d="M229 314v1c1-1 1 0 1-1s1-2 2-2l-1-2h1c1 2 2 4 4 6 1 4 3 7 6 10v1c2 4 5 6 7 10 0 1 1 2 2 3-3-2-6-2-9-3-2-1-4-1-7-1-5 1-11 0-16-1h3c1-1 1-3 2-5h0c2-2 2-4 3-6 1-3 0-7-1-11l3 1z" class="AM"></path><path d="M307 502l1-1 3 5c2 1 2 2 3 3l1 1c3 5 5 11 6 16l1 1h0c0 2 0 3 1 4v-3h0c1 2 1 3 0 5h0c1 3 0 6 1 9h1c1 1 1 1 1 2v-2h1c2 1 0 2 2 3 1-5 1-9 1-14 2 4 1 11 1 15 1 0 1 1 2 1v2l2-2 1-2h1v1c-1 2-1 3-2 4-1 2-1 4-2 6-1 4-2 8-1 12v1 4 5h0-1c-1 1-1 1-1 3h-1c-2 0-4 1-6 1-2 1-4 2-7 4h-2c-1 1-2 2-4 2l-3 3c-1-1-2 0-3 0v-2c1-2 1-4 1-6v-4-2-1c1-1 0-1 2-2l1 1h1c2-2 3-3 4-7l1-1c0-1-1-2-2-3l-3 3-2-1h-1v-1c0-1 1-2 1-3-1-3 0-6 1-9h0l1-3c1-3 1-4 1-7l3-5h0c1-2 3-3 3-6l-1-1h2c0-3 0-5-1-7-2-8-4-15-9-22z" class="H"></path><path d="M315 556c1 0 2 0 2 1 0 3-1 7-3 10 0-1-1-2-2-3l2-4 1-4z" class="Ag"></path><path d="M321 567l2 2v1c0 1-1 1-1 2 0 3-2 5-3 7l-3 2h-1c2-4 5-9 6-14z" class="M"></path><path d="M305 583c0 1 1 2 2 3l1-1c1-1 3-2 4-4h1 1l-1 2h0l2 1h0l-1 2c-1 1-2 2-4 2l-3 3c-1-1-2 0-3 0v-2c1-2 1-4 1-6z" class="AV"></path><path d="M313 583h0l2 1h0l-1 2c-1 1-2 2-4 2 0-2 1-3 3-5z" class="H"></path><path d="M325 564l2 4v3 2 1l-5 5h-3c1-2 3-4 3-7 0-1 1-1 1-2v-1l1-1 1-4z" class="X"></path><path d="M325 564l2 4c-1 1-1 2-2 2h-1v-2l1-4z" class="c"></path><path d="M307 502l1-1 3 5 1 1c0 1 1 2 1 2 1 1 1 2 1 3 2 6 3 11 5 17v6l2 11h-1c-1-1 0-1-1-2v-1-8h0c0 2 0 5-1 7v4l1 1c-2 3-1 7-2 10 0-1-1-1-2-1 2-8 3-17 2-25 0-3 0-5-1-7-2-8-4-15-9-22z" class="AC"></path><path d="M327 574l1-1c1-1 1-3 1-5h1v2c1-1 1-1 2-1v4 5h0-1c-1 1-1 1-1 3h-1c-2 0-4 1-6 1-2 1-4 2-7 4h-2l1-2h0l-2-1h0l1-2h1 1l3-2h3l5-5z" class="T"></path><path d="M327 574l1-1c1-1 1-3 1-5h1v2c0 3 0 6-2 8s-3 2-5 3h-2l-1-1 2-1 5-5z" class="AD"></path><path d="M321 526l1 1h0c0 2 0 3 1 4v-3h0c1 2 1 3 0 5h0c1 3 0 6 1 9h1c1 1 1 1 1 2v-2h1c2 1 0 2 2 3v7c-1 1-2 2-3 2 0 3 0 7-1 10l-1 4-1 1-2-2c0-1 1-4 1-6 1-4 1-7 1-11 1-8-1-16-2-24z" class="j"></path><path d="M327 542c2 1 0 2 2 3v7c-1 1-2 2-3 2 0-3 0-5 1-8v-4z" class="H"></path><path d="M330 531c2 4 1 11 1 15 1 0 1 1 2 1v2l2-2 1-2h1v1c-1 2-1 3-2 4-1 2-1 4-2 6-1 4-2 8-1 12v1c-1 0-1 0-2 1v-2h-1c0 2 0 4-1 5l-1 1v-1-2-3l-2-4c1-3 1-7 1-10 1 0 2-1 3-2v-7c1-5 1-9 1-14z" class="e"></path><path d="M330 531c2 4 1 11 1 15v4l-2 9-1-1v-2l1-4v-7c1-5 1-9 1-14z" class="AG"></path><path d="M317 531c1 8 0 17-2 25l-1 4-2 4-3 3-2-1h-1v-1c0-1 1-2 1-3-1-3 0-6 1-9h0l1-3c1-3 1-4 1-7l3-5h0c1-2 3-3 3-6l-1-1h2z" class="D"></path><path d="M313 538h1c1 2 0 4 0 6 0 4-1 8-2 12h0-1c-1-2-1-4-2-5v-1c1-3 1-4 1-7l3-5z" class="P"></path><path d="M309 550v1c1 1 1 3 2 5h1 0v1l-1 5v1c0-1 1-1 1-2l2-1-2 4-3 3-2-1h-1v-1c0-1 1-2 1-3-1-3 0-6 1-9h0l1-3z" class="U"></path><path d="M309 562l-1-1 2-3h1v-1h1l-1 5h-2z" class="V"></path><path d="M311 562v1c0-1 1-1 1-2l2-1-2 4-3 3-2-1c1-1 1-2 2-4h2z" class="C"></path><path d="M305 463c1 0 1-1 2-1h2v-1l2 1v-2h2c1 2 2 3 2 5l1 1 3 5 2 5 1 3c0 1 1 4 3 5h-1c0 4-4 7-4 10 0 1-1 1-1 2l-3 3 1 1v1h1c1 1 1 1 2 1h0c1 1 1 1 2 1h1 0c1 1 1 1 2 1s1 0 1 1 0 1-1 2c0 1-1 2 0 3l1 4c1 0 1 1 1 1 0 2 0 2 1 3v3c1 1 1 3 1 4v2c0 1 1 1 1 2v2c0 5 0 9-1 14-2-1 0-2-2-3h-1v2c0-1 0-1-1-2h-1c-1-3 0-6-1-9h0c1-2 1-3 0-5h0v3c-1-1-1-2-1-4h0l-1-1c-1-5-3-11-6-16l-1-1c-1-1-1-2-3-3l-3-5-1 1-4-6c0-3-3-4-5-7l-2-3c-2-1-3-3-5-5l-9-6c1 0 2 0 3 1h4c-1-1-2-1-3-3h-1l8 4c-2-2-2-5-4-6l-1-1c1 0 3-2 4-2s1 1 3 0c0-1-1-1-1-2h0c4 0 9 3 13 5l1-1c0-1 0-2-1-3v-1-1l-2-2z" class="q"></path><path d="M312 469h1 0c-1-1-1-2-2-3l1-2c1 1 2 1 3 1h0l1 1 3 5-1-1c-2 0-2 0-3-1l-1-1c1 1 1 2 0 3-1 0-1-1-2-2zm-12 13c2 1 4 1 5 4v1c-1 0-3 0-4-1-1 0-1 0-1-1-1 0-2-1-3-1l-1 1h1l-1 1c-2-1-3-3-5-5 1 1 2 1 3 1 2 1 4 1 6 1v-1z" class="M"></path><path d="M305 463c1 0 1-1 2-1h2v-1l2 1v-2h2c1 2 2 3 2 5h0c-1 0-2 0-3-1l-1 2c1 1 1 2 2 3h0-1l-5-4-2-2z" class="E"></path><path d="M285 473l8 4c2 1 5 3 7 5v1c-2 0-4 0-6-1-1 0-2 0-3-1l-9-6c1 0 2 0 3 1h4c-1-1-2-1-3-3h-1z" class="R"></path><path d="M820 334h4l1 1c3 0 6 1 9 2l-3 1c8 3 17 7 22 14h0c1 3 1 4 0 7-5-7-10-10-19-10-11-1-22 4-30 11-7 6-13 13-17 21-1 3-2 7-4 9l-1-2c1-8 1-15 4-22l-2-1v-1l-1-1 1-4 1-6v-4l-1-1c-1-2-1-3-1-4h0v-2c-3-1-6 0-9 0h0l8-2c3 0 6-1 9-2l7-1c7 0 13-1 19 0l3-1h0-3l3-1h0v-1z" class="U"></path><path d="M820 334h4l1 1c3 0 6 1 9 2l-3 1c-3-1-7-1-11-2h0-3l3-1h0v-1z" class="r"></path><path d="M820 334h4l1 1c-2 1-3 0-5 1h-3l3-1h0v-1z" class="Af"></path><path d="M803 348c0 1-2 2-3 3-2 2-5 4-7 6-2 3-5 6-7 9l-2-1v-1l6-6 13-10z" class="T"></path><defs><linearGradient id="I" x1="800.228" y1="337.033" x2="803.248" y2="352.741" xlink:href="#B"><stop offset="0" stop-color="#300201"></stop><stop offset="1" stop-color="#520c0b"></stop></linearGradient></defs><path fill="url(#I)" d="M798 337c7 0 13-1 19 0h4l1 1-1 1 1 1c-2 0-2-1-3 0-4 1-8 3-11 5-2 0-4 1-5 3l-13 10-6 6-1-1 1-4 1-6v-4l-1-1c-1-2-1-3-1-4h0v-2c-3-1-6 0-9 0h0l8-2c3 0 6-1 9-2l7-1z"></path><path d="M783 344h1l1 1 1 2h3c1-1 2-1 3-1h0c1-1 2-1 3-2l2 1c1 0 2 1 2 1v1h0c-3 2-4 5-7 6 1-1 1-2 2-3s1-1 1-2l-4 2h-1-1c-1-1-2-1-4-1l-1-1c-1-2-1-3-1-4z" class="Q"></path><path d="M790 350h1l4-2c0 1 0 1-1 2s-1 2-2 3h0c-1 1-1 2-2 3l1 1h-1v1l-6 6-1-1 1-4 1-6v-4c2 0 3 0 4 1h1z" class="V"></path><path d="M785 349c2 0 3 0 4 1h1c-1 2-2 4-3 5l-2-2v-4z" class="B"></path><path d="M784 359c3-1 5-4 8-6-1 1-1 2-2 3l1 1h-1v1l-6 6-1-1 1-4z" class="C"></path><path d="M279 517c-1 0-7-2-8-3 2-1 4-1 7-1 0 1 1 2 2 2s1 0 3 1h0c2 1 3 2 3 4 2 0 2 1 2 2l2 1v-1l1-1v1c2 2 4 2 6 3 2 0 2 0 4 1 1 1 5 0 7 0l2-1c2 0 4 0 6-1 1 2 1 4 1 7h-2l1 1c0 3-2 4-3 6h0l-3 5c0 3 0 4-1 7l-1 3h0c-1 3-2 6-1 9 0 1-1 2-1 3v1l-1 1c-1-1-1-2-2-2-1-1-3-2-4-2-2 1-3 1-4 1l-1 1-5 3c0-1 1-1 1-2 1-3-2-6-4-9h-4c-2-2-4-3-6-5-3-1-7-3-11-3l-4-1c2-1 4-1 6-2 4-1 8-4 11-7 3-4 6-10 6-15-1-4-3-5-5-7z" class="t"></path><path d="M275 544c4-1 7-4 11-4 0 1 0 2-1 4l-6-1-1 2-3-1z" class="S"></path><path d="M285 544l2 7h-1c-3-3-4-5-8-6l1-2 6 1z" class="V"></path><path d="M286 551h1c3 4 5 9 7 14l-5 3c0-1 1-1 1-2 1-3-2-6-4-9l-1-1c2-2 1-3 1-5zm-7-34c-1 0-7-2-8-3 2-1 4-1 7-1 0 1 1 2 2 2s1 0 3 1h0c2 1 3 2 3 4 2 0 2 1 2 2l-2-1c0 3-1 9 0 12-1 2-4 2-3 5 0 1-1 1-2 1h-1l3-5c2-3 3-8 2-11 0-1 0-3-1-3-2-2-3-3-5-3z" class="C"></path><path d="M273 547h-2l-1-1 5-2 3 1c4 1 5 3 8 6 0 2 1 3-1 5l1 1h-4c-2-2-4-3-6-5l1-1v-1c-1 0-2-1-2-1-1 0-2-1-2-2z" class="I"></path><path d="M273 547c5 1 9 5 12 9l1 1h-4c-2-2-4-3-6-5l1-1v-1c-1 0-2-1-2-1-1 0-2-1-2-2z" class="M"></path><path d="M288 522l2 1v-1l1-1v1c2 2 4 2 6 3 2 0 2 0 4 1 1 1 5 0 7 0l2-1c2 0 4 0 6-1 1 2 1 4 1 7h-2c-1 1-5 2-6 1h-2-3c-1 1-2 1-4 1l-9 2-8 3c-1-3 2-3 3-5-1-3 0-9 0-12l2 1z" class="Q"></path><path d="M296 531v-1l2-1h3c1 1-1 0 1 1h1l-7 1z" class="a"></path><path d="M303 530c1 0 2-1 3-1v1c-2 1-5 1-6 3l-9 2c1-1 1-2 2-3h2l1-1 7-1z" class="C"></path><path d="M315 531l1 1c0 3-2 4-3 6h0l-3 5c0 3 0 4-1 7l-1 3h0c-1 3-2 6-1 9 0 1-1 2-1 3v1l-1 1c-1-1-1-2-2-2-1-1-3-2-4-2-2 0-2 0-3-1-3-3-5-6-6-9l-1-1c-1-2-2-6-2-8s1-4 3-6c6-3 12-5 19-6 1 1 5 0 6-1z" class="Q"></path><path d="M299 553c1 1 1 2 1 4h-1c-1-1-1-1-1-3l1-1z" class="D"></path><path d="M292 553c1 1 1 2 1 3 2 1 3 1 4 3v3h-1c-3-3-5-6-6-9 1 0 1 1 2 2v1-2-1z" class="I"></path><path d="M290 538v3h1c1-1 1-2 3-2l2-1h1l1 1h-2c-2 0-5 3-6 5 1 3 2 6 2 9v1 2-1c-1-1-1-2-2-2l-1-1c-1-2-2-6-2-8s1-4 3-6z" class="v"></path><path d="M302 549l1-2h1v1 4l1 1c1-1 1-1 2-1l1 1c-1 3-2 6-1 9 0 1-1 2-1 3-1 0-1-1-2-2h-1-1c0-1-1-2-1-3v-1c0-3 1-6 1-10z" class="AK"></path><path d="M315 531l1 1c0 3-2 4-3 6h0l-3 5c0 3 0 4-1 7l-1 3h0l-1-1c-1 0-1 0-2 1l-1-1v-4-1h-1l-1 2h-1v2l-1 1v-1l-1 2-1 1-3-3h0c0-1-1-1-1-2l2-1c1 1 1 1 1 2l1 1c1-2 2-3 1-4v-1-2-1h0c1-1 1-2 1-3l-1-1 1 1 1 2c0-1 0-2-1-4l-2 1-1-1h-1l-2 1c-2 0-2 1-3 2h-1v-3c6-3 12-5 19-6 1 1 5 0 6-1z" class="a"></path><path d="M309 539h1l3-1h0l-3 5-2 3h-1v-1c1-2 1-4 2-6z" class="AX"></path><path d="M304 547c1-1 2-3 2-4 1 0 1 1 1 2v1h1l2-3c0 3 0 4-1 7l-1 3h0l-1-1c-1 0-1 0-2 1l-1-1v-4-1z" class="Q"></path><path d="M315 531l1 1c0 3-2 4-3 6l-3 1h-1c0-2 1-4 0-5l-2 1c-3 1-4 4-4 7h0-1v-4c-2-1-3 0-5 0h-1l-2 1c-2 0-2 1-3 2h-1v-3c6-3 12-5 19-6 1 1 5 0 6-1zm445-365l-1 1c0 1 0 2-2 3l1 1h-1c-1 1-1 2-1 3l1 2-2 3v2l-1 1h0c0 2 0 6-1 7 0 5 0 11 1 16 0 1 0 2 1 4v3c0 1 0 1 1 2v2h0v2c-1 1-1 1-1 2l1 1v2 2c0 1 0 2-1 3h0l-1 1v1c2 3 3 6 5 8l-2 1c4 7 6 14 9 21 2 8 3 15 2 23 0 3-1 5-1 8-2 1-2 2-2 3h0l-3 6v-1c-1-1-1-3-1-4l1-3c-1-2-1-4-1-6 0-1 0-2 1-3v-1l-1-1c1-2 0-4 0-5 0-2-1-4-1-6 0-3-1-7-2-10l-2-4v-1c-1-1-2-3-3-4h-1l1-1c-2-1-3-1-5-1-2 2 0 4-1 7h0 0c-1-1-1-2-2-3 0-6 2-11 1-17v-1c-1-3-2-5-3-8 0-3-2-4-1-8-1-1-1-3-2-4-1-2-2-2-3-2-1-1-1 0-2-1h0c-1-3 0-5 0-8 3-10 8-19 15-27l2-1 4-7c1-1 2-2 4-3z" class="AK"></path><path d="M745 225c1 2 3 6 3 9h0-1c0-1-1-3-2-4v-1-4z" class="D"></path><path d="M748 224c-1-2-3-4-4-6l-2-5 1-1 1 1v-2h1c-1-1-1-2-1-3l1 1c1 2 2 5 3 7-1 1-1 2-1 2 1 2 1 4 1 6z" class="Y"></path><path d="M748 224c0-2 0-4-1-6 0 0 0-1 1-2 1 3 1 6 3 8 0 2 2 4 3 6 2 3 3 6 5 8l-2 1h0c-3-5-7-9-9-15z" class="C"></path><path d="M742 219l3 6v4 1c1 1 2 3 2 4 1 4 1 7 0 11v1h1c2 1 3 1 5 1v1l1-1c1 0 1 0 1 1 2 1 3 1 4 2-1 1-4 0-5-1-2 0-3-1-5-1l-2-1v9c-1-1-1-2-2-3 0-6 2-11 1-17v-1c-1-3-2-5-3-8 0-3-2-4-1-8z" class="S"></path><path d="M760 270c1 1 1 2 2 4v2c0-1 1-3 0-4 0-2-2-3-1-5 1 1 1 2 1 4h1v-5l-1-2v-1l-1-2v-2c1 1 1 3 2 4 1 6 3 13 3 19 0 2-2 4-2 7-1 3-1 6-2 9v1c-1-1-1-3-1-4l1-3c-1-2-1-4-1-6 0-1 0-2 1-3v-1l-1-1c1-2 0-4 0-5 0-2-1-4-1-6z" class="D"></path><path d="M761 276c3 5 3 11 1 16h0c-1-2-1-4-1-6 0-1 0-2 1-3v-1l-1-1c1-2 0-4 0-5z" class="P"></path><path d="M755 228c-3-4-5-13-6-19v1l2 7c0 1 0 1 1 2v1h0 1 0c0-1 0-2-1-4h0v-1l-1-1v-1-1c0-2-1-7 0-8l1-1v-3c0-2-1-7 0-9 0 0 1-1 1-2 0 5 0 11 1 16 0 1 0 2 1 4v3c0 1 0 1 1 2v2h0v2c-1 1-1 1-1 2l1 1v2 2c0 1 0 2-1 3h0z" class="m"></path><defs><linearGradient id="J" x1="789.189" y1="387.866" x2="768.384" y2="405.01" xlink:href="#B"><stop offset="0" stop-color="#1b0000"></stop><stop offset="1" stop-color="#490807"></stop></linearGradient></defs><path fill="url(#J)" d="M779 372c2 5 2 12 1 18 0 5-1 10-1 15 0 3 0 7 1 10 1 5 3 9 2 15h0c1 4 0 8 2 13l1 4c-1 1-1 1-2 1-1-2-3-2-4-3h-1c-1-1-4-5-5-6l-15-13-1-1c-2 0-3-2-5-3 1-1 1-1 1-3v-7-5-4h1c1-1 1-4 2-6v-1l1-1c1-1 2-3 3-4 2-3 4-5 7-8 4-4 8-7 12-11z"></path><path d="M761 395v-1l2-2c2-1 4-3 6-3 0 1 0 2-1 3l-1 1h-3-1l-2 2z" class="P"></path><path d="M767 399c1-1 1-2 2-4 2 1 3 3 4 4h0v1 1l1 1h1v5l-1-2c0-1-1-2-1-3h-1l-1-1c0-1 1-1 1-2l-2-1c-1 1-2 1-3 1h0z" class="a"></path><path d="M760 391v1c0 1-1 1-1 2v2h1l1-1 2-2h1 3c-1 3-2 5-4 7 0-1-1-2-2-2s-1 1-2 2c0 1-1 1-2 2h-1c0-2 1-4 1-6v-1c1-1 2-3 3-4z" class="R"></path><path d="M757 395v1c0 2-1 4-1 6h1c1-1 2-1 2-2 1-1 1-2 2-2s2 1 2 2l-3 9-1 1v-2h-1c-1 1-1 3-1 4h-1-1c0-2 0-4 1-6v-1c-1-1-1-2-1-3 1-2 1-4 1-6l1-1z" class="M"></path><path d="M756 396c0 2 0 4-1 6 0 1 0 2 1 3v1c-1 2-1 4-1 6h1 1c0-1 0-3 1-4h1v2l1-1c0 1 0 1 1 2v1l-1 4v2l1 8c-1 0-2-1-3-1v1l-1-1c-2 0-3-2-5-3 1-1 1-1 1-3v-7-5-4h1c1-1 1-4 2-6v-1z" class="AR"></path><path d="M757 425l-1-2c0-1 0-2 1-3l2 4-1 1v1l-1-1z" class="H"></path><path d="M759 410l1-1c0 1 0 1 1 2v1l-1 4v2l1 8c-1 0-2-1-3-1l1-1c-1-4-1-7-1-11l1-3z" class="V"></path><path d="M759 410l1-1c0 1 0 1 1 2v1l-1 4v2c-1-1-1-4-2-5l1-3z" class="B"></path><path d="M756 396c0 2 0 4-1 6 0 1 0 2 1 3v1c-1 2-1 4-1 6v7 2l-2-1c0-1 0-3 1-5v-1c0-2 0-5-1-7v-4h1c1-1 1-4 2-6v-1z" class="AW"></path><path d="M767 399h0c1 0 2 0 3-1l2 1c0 1-1 1-1 2l1 1h1c0 1 1 2 1 3l1 2c1 1 1 1 1 2 0 2 0 3 1 4 1 2 1 4 3 6v-4c1 5 3 9 2 15-1-2-1-4-3-5v-2c-1-1-1-2-2-3s-2-3-3-4h-2v-1c0-2-1-2-2-3l-1 1 1 1v1c-2-1-2-2-3-3v-3c-1 0-1 0-2 1v-1l-1-1c-1 2-1 3-3 4h0v-1-1c0-1 0-1 1-2v-1c1-1 1-2 2-3l1-3c0-1 0-1 2-2z" class="Q"></path><path d="M765 401c1 0 3-1 5-2 1 2 0 4 1 6 0 0 1 0 1 1h-4c0-2 0-3-1-5l-3 3 1-3z" class="C"></path><path d="M768 406h4c1 2 3 3 3 5 1 4 5 8 4 12-1-1-1-2-2-3 0-2 0-3-1-4-2-2-3-5-5-7v-1-1c-1-1-2-1-3-1z" class="V"></path><path d="M764 404l3-3c1 2 1 3 1 5 1 0 2 0 3 1v1 1c2 2 3 5 5 7 1 1 1 2 1 4-1-1-2-3-3-4h-2v-1c0-2-1-2-2-3l-1 1 1 1v1c-2-1-2-2-3-3v-3c-1 0-1 0-2 1v-1l-1-1c-1 2-1 3-3 4h0v-1-1c0-1 0-1 1-2v-1c1-1 1-2 2-3z" class="N"></path><path d="M764 404l3-3c1 2 1 3 1 5 1 0 2 0 3 1v1 1c-1 0-3 1-3 0-1-2-1-2-3-4-1 1-2 3-3 4l-1 1c0-1 0-1 1-2v-1c1-1 1-2 2-3z" class="AL"></path><path d="M761 412h0c2-1 2-2 3-4l1 1v1c1-1 1-1 2-1v3c1 1 1 2 3 3v-1l-1-1 1-1c1 1 2 1 2 3v1h2c1 1 2 3 3 4s1 2 2 3v2c2 1 2 3 3 5h0c1 4 0 8 2 13l1 4c-1 1-1 1-2 1-1-2-3-2-4-3h-1c-1-1-4-5-5-6l-15-13v-1c1 0 2 1 3 1l-1-8v-2l1-4z" class="F"></path><path d="M774 420l2 2c0 1-1 2 0 4v1l-1-1v1 1c0-2-1-3-2-4h-1c0-2 1-3 2-4z" class="AH"></path><path d="M772 416h2c1 1 2 3 3 4s1 2 2 3v2c-1-2-2-3-3-3l-2-2c-1-2-2-3-2-4h0z" class="M"></path><path d="M761 412h0c2-1 2-2 3-4l1 1v1c1-1 1-1 2-1v3c1 1 1 2 3 3v-1l-1-1 1-1c1 1 2 1 2 3v1h0c-2 1-2 2-2 4l-2 1v1c0 1 1 2 1 3s0 2-1 3l2 2c-1 0-1-1-2-1h-1c1 1 2 1 2 3-1-1-2-1-3-2-2-1-3-3-5-4h0l-1-8v-2l1-4z" class="AR"></path><path d="M767 429v-1c0-1 0-1-1-2h-1v-3h1c-1 0-2-1-3-1v-2c1-1 2-4 3-5l2 6v1c0 1 1 2 1 3s0 2-1 3l2 2c-1 0-1-1-2-1h-1z" class="H"></path><path d="M761 412h0c2-1 2-2 3-4l1 1v1c1-1 1-1 2-1v3c1 1 1 2 3 3v-1l-1-1 1-1c1 1 2 1 2 3v1h0c-2 1-2 2-2 4l-2 1-2-6c1-1 0-2 0-3-1 0-2 1-3 2h0v-2c1-1 1 0 2-1l-1-1c-1 1-2 2-2 3s0 2-1 2l-1 1 1-4z" class="t"></path><path d="M535 837h0l2-12c1 2 0 4 1 6 0 2-2 7-1 9v3c-2 4-2 11-1 16 0 1 1 3 1 5h-1 0-1c0-1 0-2-1-3l-1-3v4l-2 2c0 1 0 1-1 2 0 2-2 4-2 7l-3 6c-2 5-5 10-7 15-1 1-2 2-2 4 0 3-3 5-3 9 1 1 0 2 0 3-1 3 0 5 0 7v4-1l1-1v-1l1-1h0c0 3-2 6-1 9l-2 3v2h-1c-1-3-2-7-3-11l-1-3-6-23c-1-3-4-4-7-5v-1c0-4-1-7-4-10v-1c1-1 1-2 2-3l1-3v4h-1v1h0c-1 1-1 1-1 2h1c1 2 2 3 2 5h1c0 2-1 4 0 5h2 1l-1-1c-2-4-4-8-2-13 0 0 1-3 1-4v-1c1 1 1 3 1 5 0-2 0-3 1-5v-2c0-1 0-2 1-3h0v-6-1-1c1 1 2 1 3 1 0-1-1-2-1-2-1-1-1-1-1-2h1 1 4v-1h2c2-1 4-3 4-5h1c1 4 0 10-1 14-1 3-1 5 0 7 0-1 1-1 1-2s0-2 1-4c2-1 2-3 5-3v1 1h1v-2l3-2h1c0-1 0-1 1-2h0c0-1 0-2 1-2l1-1 1-4 2-1-2 7c0 3-1 3 0 6 2-7 3-13 4-20v-1c1-1 1-2 1-3 1 0 0 0 1-1v-1h0v-1l1-2h0v3 1 2h0z" class="K"></path><path d="M496 870v-1c1 1 1 3 1 5v5 4-1h-1c-1-3-1-5-1-8 0 0 1-3 1-4z" class="k"></path><path d="M497 879v-1c0-1 1-3 1-4 1-1 1-2 1-3h3c0 2 0 2-1 4-1 1-1 4 0 6h0c0 1 0 2 1 3v2 1l1 1v2h0-1c-1-1-1-2-2-2-2-2-3-3-3-5h0v-4z" class="d"></path><path d="M512 929l-1-1c2-4-1-9-1-13l-4-14v-1c0-1 1-2 1-2 1-1 1-2 2-3 1-2 2-4 5-5-1 2-3 4-5 7-1 2-2 3-1 6v1l1 3v2l1-1c1-3 4-9 6-10 0 3-3 5-3 9 1 1 0 2 0 3-1 3 0 5 0 7v4-1l1-1v-1l1-1h0c0 3-2 6-1 9l-2 3z" class="Z"></path><path d="M520 859l3-2h1c0-1 0-1 1-2h0c0-1 0-2 1-2l1-1-3 9c0 1 0 1-1 2v3h0c0 2-1 2-1 4-2 4-4 8-7 12v-1-1c3-4 5-8 7-13l-6 6c-2 2-3 5-5 8-1 2-1 4-2 6l5-5v1c-1 1-2 3-4 5-1-1-1 0-1-1-1-4 0-8 0-11 1-2 1-3 1-5l2-3c0-1 1-1 1-2s0-2 1-4c2-1 2-3 5-3v1 1h1v-2z" class="g"></path><path d="M520 859l3-2h1c0-1 0-1 1-2h0c0-1 0-2 1-2l1-1-3 9c0 1 0 1-1 2l-2 2-1-1h1l-1-1v1c-2 1-4 3-5 4-2 2-4 5-6 8 1-2 1-3 1-5l2-3c0-1 1-1 1-2s0-2 1-4c2-1 2-3 5-3v1 1h1v-2z" class="L"></path><path d="M512 847h1c1 4 0 10-1 14-1 3-1 5 0 7l-2 3v-6c-2 4-3 7-4 11v2c-1-1-1-2-1-4l-1 1v5 2l-1 1-1-1-1-1h0c-1-2-1-5 0-6 1-2 1-2 1-4h-3c0 1 0 2-1 3 0 1-1 3-1 4v1-5c0-2 0-3 1-5v-2c0-1 0-2 1-3h0v-6-1-1c1 1 2 1 3 1 0-1-1-2-1-2-1-1-1-1-1-2h1 1 4v-1h2c2-1 4-3 4-5z" class="Z"></path><path d="M504 880c-1-1-2-7-1-9s3-5 4-7v-1l1-1v2h1v-1h1v2c-2 4-3 7-4 11v2c-1-1-1-2-1-4l-1 1v5z" class="L"></path><path d="M499 857v-1c1 1 2 1 3 1 0-1-1-2-1-2-1-1-1-1-1-2h1 1l1 1c2 1 5 0 8 0l-5 6c-1 2-3 4-5 6h1c0 1-1 2-1 2-1 1-1 2-2 3 0 1 0 2-1 3 0 1-1 3-1 4v1-5c0-2 0-3 1-5v-2c0-1 0-2 1-3h0v-6-1z" class="b"></path><defs><linearGradient id="K" x1="517.774" y1="866.538" x2="527.919" y2="871.247" xlink:href="#B"><stop offset="0" stop-color="#dcd8d5"></stop><stop offset="1" stop-color="#fefefe"></stop></linearGradient></defs><path fill="url(#K)" d="M535 837h0l2-12c1 2 0 4 1 6 0 2-2 7-1 9v3c-2 4-2 11-1 16 0 1 1 3 1 5h-1 0-1c0-1 0-2-1-3l-1-3v4l-2 2c0 1 0 1-1 2 0 2-2 4-2 7l-3 6c-2 5-5 10-7 15-1 1-2 2-2 4-2 1-5 7-6 10l-1 1v-2l-1-3v-1c-1-3 0-4 1-6 2-3 4-5 5-7 0-3 2-5 3-7l6-14c1-1 2-3 1-4l-1 1h0v-3c1-1 1-1 1-2l3-9 1-4 2-1-2 7c0 3-1 3 0 6 2-7 3-13 4-20v-1c1-1 1-2 1-3 1 0 0 0 1-1v-1h0v-1l1-2h0v3 1 2h0z"></path><path d="M522 876c1 0 1 0 2-1v1c-1 2-2 5-4 8h-1c-2 3-3 6-5 9l-1 1-1 1v1c-1 1-1 2-2 3h0l-1-2c2-3 4-5 5-7 1-1 1-3 2-4 2-3 4-6 6-10z" class="AP"></path><path d="M535 837h0l2-12c1 2 0 4 1 6 0 2-2 7-1 9v3c-2 4-2 11-1 16 0 1 1 3 1 5h-1 0-1c0-1 0-2-1-3l-1-3v4l-2 2 3-21c1-2 1-4 1-6z" class="W"></path><path d="M530 847l-2 7c0 3-1 3 0 6l-6 16c-2 4-4 7-6 10-1 1-1 3-2 4 0-3 2-5 3-7l6-14c1-1 2-3 1-4l-1 1h0v-3c1-1 1-1 1-2l3-9 1-4 2-1z" class="O"></path><defs><linearGradient id="L" x1="550.795" y1="331.056" x2="554.879" y2="339.423" xlink:href="#B"><stop offset="0" stop-color="#c90703"></stop><stop offset="1" stop-color="#ed1d19"></stop></linearGradient></defs><path fill="url(#L)" d="M597 290h0v6l1 1-1 3h0c1 2 1 3 1 5l3-5 1 1c0 2 0 2 1 3s0 3 0 5l-2 9-2 5h-1l-3 3c-2 1-2 1-3 1l-3-1-1-1c-2 1-1 1-2 2 0 0-2 1-3 2l-8 6c-3 2-5 3-8 4l-6 4c-5 3-11 8-17 10-1 0-7 2-7 2-4 0-7-1-10-3-1-1-1-2-1-4h0v-4c-2-1-2-1-2-2l1-3c1 1 1 2 2 2 1-1 2-1 3-2l2 1c2-1 4-1 6-2 6-1 10-3 15-6 4-2 8-5 11-9 7-6 11-13 16-21l1-3 4-9c2 3 1 5 1 9 1-3 2-5 3-7l1 2h2c-1 1-2 3-2 4 1 1 1 2 1 3 1-1 2-2 2-3l1-2c0-1 0-1 1-2l2-4z"></path><path d="M583 312c1 1 1 2 1 3-1 2-3 4-4 6l-2-1c0-2 4-6 5-8z" class="Ak"></path><path d="M578 320l2 1-2 3-7 7-2-2 9-9z" class="An"></path><path d="M585 290c2 3 1 5 1 9h0c-2 4-4 7-6 11 0-4 2-7 1-11l4-9z" class="X"></path><path d="M581 299c1 4-1 7-1 11-2 4-6 8-9 12v-2-1c0-1 0-2 1-3l1-1 1-1v-1c1 0 1-1 2-2s1-2 2-3c0-1 1-2 1-2 1-2 1-2 1-4l1-3z" class="AS"></path><path d="M529 348c5 1 8 0 13-1h1c10-4 18-11 26-18l2 2-2 2-12 8c-9 5-17 10-27 9 0 0-1 0-1-2z" class="n"></path><path d="M557 341l1 1c1-1 2-1 3-2 0 0 1-1 2-1h0c-1 1-1 1-2 1v1h0c1 0 1 0 2-1h1l2-2h1v1l-6 4c-5 3-11 8-17 10-1 0-7 2-7 2-4 0-7-1-10-3-1-1-1-2-1-4h3c0 2 1 2 1 2 10 1 18-4 27-9z" class="AD"></path><path d="M578 324h1c3 0 6-4 7-6 1 1 1 1 3 1l2-1 1 1 1 1c2 1 2 1 3 3l-1 1v1 1c-2 1-2 1-3 1l-3-1-1-1c-2 1-1 1-2 2 0 0-2 1-3 2l-8 6c-3 2-5 3-8 4v-1h-1l-2 2h-1c-1 1-1 1-2 1h0v-1c1 0 1 0 2-1h0c-1 0-2 1-2 1-1 1-2 1-3 2l-1-1 12-8 2-2 7-7z" class="T"></path><path d="M578 324h1c3 0 6-4 7-6 1 1 1 1 3 1l2-1 1 1 1 1c2 1 2 1 3 3l-1 1v1 1c-2 1-2 1-3 1 0-1 1-2 0-3s-1-2-3-2v-1c-2 0-3 2-4 3-4 3-7 6-11 8l-1 1h-4l2-2 7-7z" class="H"></path><path d="M580 302c0 2 0 2-1 4 0 0-1 1-1 2-1 1-1 2-2 3s-1 2-2 2v1l-1 1-1 1c-1 1-1 2-1 3v1 2c-4 4-9 9-14 12-4 4-9 6-14 8l-6 3h-3 0-3c0 1-1 1-2 1l-2-2h-1c-2-1-2-1-2-2l1-3c1 1 1 2 2 2 1-1 2-1 3-2l2 1c2-1 4-1 6-2 6-1 10-3 15-6 4-2 8-5 11-9 7-6 11-13 16-21z" class="AV"></path><path d="M553 332c1 0 2 0 3-1s2-2 4-2c-1 2-3 3-5 4l-3 3-2 1c-3 2-6 3-8 4l-8 3c-1 0-2 0-3 1 0 1-1 1-2 1l-2-2h-1c-2-1-2-1-2-2l1-3c1 1 1 2 2 2 1-1 2-1 3-2l2 1c2-1 4-1 6-2 6-1 10-3 15-6z" class="AG"></path><path d="M531 341h1 3l1-1h0l-2 4c-1 0-2 0-3 1 0 1-1 1-2 1l-2-2c1 0 2 0 3-1v-1l1-1z" class="e"></path><path d="M530 339l2 1-1 1-1 1v1c-1 1-2 1-3 1h-1c-2-1-2-1-2-2l1-3c1 1 1 2 2 2 1-1 2-1 3-2z" class="j"></path><path d="M597 290h0v6l1 1-1 3h0c1 2 1 3 1 5l3-5 1 1c0 2 0 2 1 3s0 3 0 5l-2 9-2 5h-1l-3 3v-1-1l1-1c-1-2-1-2-3-3l-1-1-1-1-2 1c-2 0-2 0-3-1-1 2-4 6-7 6h-1l2-3c1-2 3-4 4-6 0-1 0-2-1-3l2-4-1-3c1-2 1-4 2-6h0c1-3 2-5 3-7l1 2h2c-1 1-2 3-2 4 1 1 1 2 1 3 1-1 2-2 2-3l1-2c0-1 0-1 1-2l2-4z" class="e"></path><path d="M597 290h0v6l1 1-1 3-2 3h0c-1-3 1-6 0-9l2-4z" class="X"></path><path d="M590 298c1 1 1 2 1 3-2 5-4 9-7 14 0-1 0-2-1-3l2-4 5-10z" class="Al"></path><path d="M586 299c1-3 2-5 3-7l1 2h2c-1 1-2 3-2 4l-5 10-1-3c1-2 1-4 2-6h0z" class="C"></path><path d="M597 300h0c1 2 1 3 1 5l3-5 1 1c0 2 0 2 1 3s0 3 0 5l-2 9-2 5h-1l-3 3v-1-1l1-1c-1-2-1-2-3-3l-1-1-1-1-2 1c-2 0-2 0-3-1 1-1 2-1 2-2 3-4 5-8 7-13h0l2-3z" class="N"></path><path d="M595 303h0l1 1h0c0 2-1 2 0 5h1c0-1 0-2 1-2 2 1 1 2 1 4 0 0-1-1-2-1v1c-1 1-2 2-4 3h0c-1 1-2 3-4 3l-1-1c3-4 5-8 7-13z" class="B"></path><path d="M593 314h0c2-1 3-2 4-3v-1c1 0 2 1 2 1 1 1 1 1 1 2s1 1 1 2v3l-2 5h-1l-3 3v-1-1l1-1c-1-2-1-2-3-3l-1-1-1-1-2 1c-2 0-2 0-3-1 1-1 2-1 2-2l1 1c2 0 3-2 4-3z" class="q"></path><path d="M593 314h0c2-1 3-2 4-3v-1c1 0 2 1 2 1 1 1 1 1 1 2s1 1 1 2v3l-2 5-1-4v-4l-2 1-3-2z" class="M"></path><path d="M714 518c9 0 18-8 27-6l3 1h-1c-1 1-2 1-4 1h0c-3 1-5 2-6 4l-1 3c-2 0-4 0-5 2-1 1-3 2-4 2 0 1 0 1 1 2-1 1-1 2 0 3v1c2 1 3 2 5 4 1 1 1 2 2 3h-1c-1-1-2-2-3-2h0c-3-1-4-1-7-2-2 0-5-1-7-1l-1 1c7 2 13 4 18 7v1c1 1 1 1 2 3l-1 1c-1 2-2 5-3 7v1c0 2-1 2-2 3s0 1-1 2l-5 10c0 1-1 2-1 3-1 2-2 5-2 7-1 2-2 6-1 8v2 1h-1v3l-1 1c-1-3-3-6-4-9s-1-7-3-9c1-1 2-1 2-2 0-2 0-2 1-3v-1l-1-2c-2 0-3 0-4-1-3-2-4-5-5-9-1-2-2-6-2-9h0c-1-2-1-4-2-5 1-1 0-2 0-3l1-9 3-15 1 1v-1c2-1 5-1 7 0 1 0 5 0 6 1z" class="AK"></path><path d="M720 547c1 1 1 2 2 4h0c-1 2-2 4-3 5l-1-1v-1c0-1 0-3 1-4s1-2 1-3zm-12-30c1 0 5 0 6 1l1 1c1 0 2 1 4 1-3 1-5 0-8 0-1-1-3-1-5-2l2-1z" class="m"></path><path d="M712 534h0-3l-4-1h-3l-1-1c2-1 4-1 6-1l1 1h3 2c1 0 1-1 2-1h2c1 1 2 1 3 2 0 1 1 1 2 1l1-1c1 1 4 3 6 2 1 1 1 2 2 3h-1c-1-1-2-2-3-2h0l-7-2c-2 0-5-1-7-1l-1 1z" class="v"></path><path d="M741 512l3 1h-1c-1 1-2 1-4 1h0c-3 1-5 2-6 4l-1 3c-2 0-4 0-5 2-1 1-3 2-4 2 0 1 0 1 1 2-1 1-1 2 0 3v1l-1-1c-1-1-1-2-1-3h0-1l-1 1v-3c2-2 5-3 8-5l5-4v-1c1-1 4-2 5-2 2 0 2 0 3-1z" class="D"></path><path d="M714 518c9 0 18-8 27-6-1 1-1 1-3 1-1 0-4 1-5 2-2 1-13 4-14 5-2 0-3-1-4-1l-1-1z" class="N"></path><path d="M730 542c1 1 1 1 2 3l-1 1c-1 2-2 5-3 7v1c0 2-1 2-2 3s0 1-1 2l-5 10c0 1-1 2-1 3-1 2-2 5-2 7h-1c-1-1-1-1-1-2 0-3 0-3 2-4l-1-1v-3l2-7c1-1 2-3 3-4 3-4 6-9 7-13 1-1 1-2 2-3z" class="U"></path><path d="M716 569c3-1 2-5 4-6h1c0 1-1 2-1 4h-1l1 2c0 1-1 2-1 3-1 2-2 5-2 7h-1c-1-1-1-1-1-2 0-3 0-3 2-4l-1-1v-3z" class="P"></path><path d="M700 558c1 1 1 1 2 3 1 3 3 4 6 6v-1c1-1 1-3 1-4s0-1 1-2c0-2 2-6 3-7s2-2 2-3 0-1 1-2c0-1 1-2 1-4h2l1 3c0 1 0 2-1 3s-1 3-1 4v1l1 1-5 6 1 1-1 1h1v1l-6 3h0c-2 0-3 0-4-1-3-2-4-5-5-9z" class="I"></path><path d="M718 555l-1-1h0l-1-1c0-2 0-3 1-5h1v6 1z" class="S"></path><path d="M715 564l3-2-2 7v3l1 1c-2 1-2 1-2 4 0 1 0 1 1 2h1c-1 2-2 6-1 8v2 1h-1v3l-1 1c-1-3-3-6-4-9s-1-7-3-9c1-1 2-1 2-2 0-2 0-2 1-3v-1l-1-2h0l6-3v-1z" class="AC"></path><path d="M715 564l3-2-2 7v3l1 1c-2 1-2 1-2 4 0 1 0 1 1 2h1c-1 2-2 6-1 8v2c-1-2-2-5-3-7v-1c0-6 1-11 2-16v-1z" class="C"></path><path d="M438 335c3 1 5 2 7 3 5 3 11 6 17 7h0l8 2 6 2c5 1 16 0 21-2h0 2v3l1 2 2 15c-1 3-1 7-1 10v11l-1 2h-1 0-2l-1 1-1 1-1-1h-1-6l-1-1c-3 0-5-2-7-4l-4 1c-2-2-4-3-6-5l-1-1c-5-4-9-9-14-13v-1l-2 2-1-1c-1-3-5-7-7-11h0c0-2 0-2-1-4v-1l3-2c1-1 1-1 2-3l-1-1 1-1c-1-1-1-2-1-3l-9-7z" class="H"></path><path d="M451 354l1 1c2 0 4 0 7 1v1h-1c0 1 0 2 1 3h0-2 0c-1 0-3-1-4-2v-2l-2 1-1-1 1-2z" class="c"></path><path d="M483 364c0 1 0 1-1 2-2 0-3-1-4-2 0 0-1-1-2-1s-3-1-4-2c-2-1-5-2-7-3-2 0-4-1-5-2v-1c2 1 4 1 6 2 2 0 3 2 5 2l11 4 1 1z" class="AC"></path><path d="M452 354c0-2 0-3 2-4 5 0 10 1 15 3 1 1 2 1 3 1l3 1c3 2 7 3 10 6 2 1 3 3 4 4 2 2 3 3 4 5s2 4 2 7c-1-1-2-2-2-3h-1v-3l-1-1v-1c-1-2-6-5-8-5l-1-1c-1-1-3-2-4-3l-1-1c-3-1-5-2-8-3s-6-3-9-3h-6v-1l-2 2z" class="c"></path><path d="M438 335c3 1 5 2 7 3 5 3 11 6 17 7h0l8 2v3l9 3h-1l2 2h-5l-3-1c-1 0-2 0-3-1-5-2-10-3-15-3-2 1-2 2-2 4h-1l-1 2 1 1v1l-4 1-1-2h-2 0c0-2 0-2-1-4v-1l3-2c1-1 1-1 2-3l-1-1 1-1c-1-1-1-2-1-3l-9-7z" class="F"></path><path d="M472 354l-1-1-1-1 2-1 6 2 2 2h-5l-3-1z" class="R"></path><path d="M462 345l8 2v3l-9-2 1-1v-2z" class="Aj"></path><path d="M446 350l1 1v4l1 1h0 2l1 1v1l-4 1-1-2h-2 0c0-2 0-2-1-4v-1l3-2z" class="U"></path><path d="M438 335c3 1 5 2 7 3 5 3 11 6 17 7h0v2l-1 1-8-2c-1 3-3 5-5 7l3-7c0-2-3-3-4-4l-9-7z" class="Ak"></path><path d="M497 347h2v3l1 2 2 15c-1 3-1 7-1 10v11l-1 2h-1l1-1v-3l-4-6c-1 0-1 0-1-1l1-1-1-1c0-3-1-5-2-7s-2-3-4-5c-1-1-2-3-4-4-3-3-7-4-10-6h5l-2-2h1l-9-3v-3l6 2c5 1 16 0 21-2h0z" class="Af"></path><path d="M494 360c1 1 2 1 3 2 0-1 1-2 1-2l3 4-1 1c0 3 1 5 0 8-1-3-1-5-2-7l-3-5-1-1z" class="r"></path><path d="M494 360c1 1 2 1 3 2 0-1 1-2 1-2l3 4-1 1h-1c-1-1-3-2-4-4l-1-1z" class="H"></path><path d="M499 350l1 2 2 15c-1 3-1 7-1 10 0-1-1-3-1-4 1-3 0-5 0-8l1-1-3-4 1-1c0-2-1-4-1-6 0-1 1-2 1-3z" class="l"></path><path d="M498 370c1 2 1 4 2 6h0v10l-4-6c1-1 1-2 1-3-1-1-1-2-1-3 1-1 1-3 2-4z" class="B"></path><path d="M485 356c5 3 10 8 13 14-1 1-1 3-2 4 0 1 0 2 1 3 0 1 0 2-1 3-1 0-1 0-1-1l1-1-1-1c0-3-1-5-2-7 1 1 1 1 2 1 0-3-1-5-3-7-2-3-4-5-6-6-1-1-1-1-1-2z" class="E"></path><path d="M479 353c2 1 4 1 6 3 0 1 0 1 1 2 2 1 4 3 6 6 2 2 3 4 3 7-1 0-1 0-2-1-1-2-2-3-4-5-1-1-2-3-4-4-3-3-7-4-10-6h5l-2-2h1z" class="N"></path><path d="M497 347h2v3c0 1-1 2-1 3 0 2 1 4 1 6l-1 1s-1 1-1 2c-1-1-2-1-3-2-5-4-9-6-15-9 6 0 12-1 17-2l1-1v-1z" class="AM"></path><path d="M497 347h2v3c0 1-1 2-1 3 0 2 1 4 1 6-1-1-3-2-3-4v-5h2l-1-2v-1z" class="H"></path><path d="M451 357l2-1v2c1 1 3 2 4 2l1 1c3 2 6 5 10 6 2 1 5 2 8 3l1 1 7 3 2 1v1l3 1 2 1c1 1 4 3 5 5 1 1 2 2 2 3l1 1s1 1 1 2l-1 1h0-2l-1 1-1 1-1-1h-1-6l-1-1c-3 0-5-2-7-4l-4 1c-2-2-4-3-6-5l-1-1c-5-4-9-9-14-13v-1l-2 2-1-1c-1-3-5-7-7-11h2l1 2 4-1v-1z" class="AH"></path><path d="M454 367c-1 0-1-1-2-1h0v-1-2h0l3 1c1 0 3 1 5 1l-1 1v2l-1 1-3-2-1 1v-1z" class="T"></path><path d="M451 357l2-1v2c1 1 3 2 4 2l1 1c0 1 0 2-1 3-2-2-5-4-7-5l-1 1-2-1 4-1v-1z" class="z"></path><path d="M476 376l-2-2h-1c-1 0-2-2-2-3l1-1 1 1c2 1 5 2 7 4 3 2 7 3 11 6 1 0 2 1 3 2h2c1 1 2 2 2 3l1 1s1 1 1 2l-1 1h0-2l-1 1-1-2-9-6-10-7z" class="F"></path><path d="M480 375c3 2 7 3 11 6v3c-1-1-3-2-4-3-2-1-6-3-8-5l1-1z" class="N"></path><path d="M491 381c1 0 2 1 3 2h2c1 1 2 2 2 3l1 1s1 1 1 2l-1 1h0-2l-1 1-1-2c-1-3-2-4-4-5v-3z" class="R"></path><path d="M460 365l1 2 3 2c3 1 4 3 6 4l6 3 10 7 9 6 1 2-1 1-1-1h-1-6l-1-1c-3 0-5-2-7-4l-4 1c-2-2-4-3-6-5l-1-1c-5-4-9-9-14-13l1-1 3 2 1-1v-2l1-1z" class="AS"></path><path d="M460 365l1 2-1 1 3 3h-2c-2 0-3-1-3-2l1-1v-2l1-1z" class="AG"></path><path d="M463 371l-3-3 1-1 3 2c3 1 4 3 6 4l6 3 10 7h-1c-2 0-5-2-7-3 0-1-1-1-1-1l-6-3c-3-2-5-4-8-5z" class="r"></path><path d="M477 379s1 0 1 1c2 1 5 3 7 3h1l9 6 1 2-1 1-1-1h-1-6l-1-1c-3 0-5-2-7-4-1 0-1 0-2-1 1-1 2 0 3 0l1-1c-1-2-2-2-3-2v-1h0l-1-1v-1z" class="AC"></path><path d="M605 229c1 3 1 6 2 9h2l1 5c2 9 6 17 7 26 1 6 0 12 1 18v5 5c-1 4-1 7-2 11 0 1 0 2 1 3l-1 1c0 1-1 2-1 3 1 1 1 1 2 1l3-1 1-1 5-7 1 3c-2 3-6 6-6 10v2c0-1 1-2 2-2h1 0c-1 1-1 1-1 2l-8 11-8 8-4 4-3 3h-2l-1 1c0-1-1-1-1-1 1-2 2-3 3-4-1-2-1-3-1-4 2-2 2-4 3-5 0-1-1-1-2-1 1-2 1-3 1-4l-5 9-2-2h0-1c-2 2-3 3-5 3h-1l2-2c-1-1-2-1-3-2l-1 1h-1v-3h0l1-2c-1-1-1-2-1-3 1-1 3-2 3-2 1-1 0-1 2-2l1 1 3 1c1 0 1 0 3-1l3-3h1l2-5 2-9c0-2 1-4 0-5s-1-1-1-3l-1-1-3 5c0-2 0-3-1-5h0l1-3-1-1v-6h0l1-8 1-3c1-3 2-7 2-10v-10l-1-5c0-2 0-4 1-5 0-2-3-6-4-8l-4-5v-3l-3-3c1 0 2 0 3 1v-1l5 5c0-2 0-3-1-4v-1l3 3c1 0 2 1 2 1l2 2c-1-2-1-3-1-4v-2l2 1h0v-2z" class="e"></path><path d="M593 233c2 1 3 2 5 4h-1-1v1c1 1 1 1 1 2v1l-4-5v-3z" class="AG"></path><path d="M590 230c1 0 2 0 3 1v-1l5 5 1 1h1c1 0 1 4 1 5l-3-4c-2-2-3-3-5-4l-3-3z" class="j"></path><path d="M605 303c0 2 1 4 0 7v-1l1-1h0c0 3-3 18-6 20v2l-5 9-2-2 2-3c0-2 1-4 2-6l1-5h1l2-5 2-9c1-2 1-4 2-6z" class="C"></path><path d="M597 328v5c1-2 2-3 3-5h0v2l-5 9-2-2 2-3c0-2 1-4 2-6z" class="B"></path><path d="M595 326l3-3-1 5c-1 2-2 4-2 6l-2 3h0-1c-2 2-3 3-5 3h-1l2-2c-1-1-2-1-3-2l-1 1h-1v-3h0l1-2c-1-1-1-2-1-3 1-1 3-2 3-2 1-1 0-1 2-2l1 1 3 1c1 0 1 0 3-1z" class="F"></path><path d="M589 330l-2-1c0-2 1-2 2-3l3 1h0c1 1 1 1 2 1l-2 2v-1l-1-1-2 2z" class="R"></path><path d="M587 335l4-3v1h1v1l-4 4c-1-1-2-1-3-2l2-1z" class="AH"></path><path d="M588 331c0 2-1 2-1 4l-2 1-1 1h-1v-3h0l1-2 4-1z" class="j"></path><path d="M583 329c1-1 3-2 3-2 1-1 0-1 2-2l1 1c-1 1-2 1-2 3l2 1-1 1-4 1c-1-1-1-2-1-3z" class="E"></path><path d="M601 249c2 4 3 8 4 12 3 12 4 24 3 36 0 4-1 8-2 11h0l-1 1v1c1-3 0-5 0-7-1 2-1 4-2 6 0-2 1-4 0-5s-1-1-1-3l-1-1-3 5c0-2 0-3-1-5h0l1-3-1-1v-6h0l1-8 1-3c1-3 2-7 2-10v-10l-1-5c0-2 0-4 1-5z" class="I"></path><path d="M601 249c2 4 3 8 4 12l-1-1s0-1-1-2l-1-3v3c1 1 1 1 1 3l-1 1-1-3-1-5c0-2 0-4 1-5z" class="m"></path><path d="M601 259l1 3c1 6 2 14 0 19l-1-1c0 1 0 2-1 2v1h-1v-4c1-3 2-7 2-10v-10z" class="E"></path><path d="M599 279v4h1v-1c1 0 1-1 1-2l1 1h0c1 1 1 1 2 3l1 19c-1 2-1 4-2 6 0-2 1-4 0-5s-1-1-1-3l-1-1-3 5c0-2 0-3-1-5h0l1-3-1-1v-6h0l1-8 1-3z" class="U"></path><path d="M599 279v4h1v-1c1 0 1-1 1-2l1 1h0l-2 10c-1-2-1-2-1-4 0-1 0-3-1-5l1-3z" class="F"></path><path d="M598 282c1 2 1 4 1 5 0 2 0 2 1 4l-2 6-1-1v-6h0l1-8z" class="j"></path><defs><linearGradient id="M" x1="614.575" y1="304.93" x2="606.877" y2="303.427" xlink:href="#B"><stop offset="0" stop-color="#400404"></stop><stop offset="1" stop-color="#651111"></stop></linearGradient></defs><path fill="url(#M)" d="M605 229c1 3 1 6 2 9h2l1 5c2 9 6 17 7 26 1 6 0 12 1 18v5 5c-1 4-1 7-2 11 0 1 0 2 1 3l-1 1c0 1-1 2-1 3 1 1 1 1 2 1l3-1 1-1 5-7 1 3c-2 3-6 6-6 10v2c0-1 1-2 2-2h1 0c-1 1-1 1-1 2l-8 11-8 8-4 4-3 3h-2l-1 1c0-1-1-1-1-1 1-2 2-3 3-4-1-2-1-3-1-4 2-2 2-4 3-5s2-3 2-4c2-3 4-7 5-10l1-1c1-4 2-8 2-12 2-11 3-25 0-37 0-4-1-10-3-14-1-6-4-11-7-16 0-1 0-5-1-5h-1l-1-1c0-2 0-3-1-4v-1l3 3c1 0 2 1 2 1l2 2c-1-2-1-3-1-4v-2l2 1h0v-2z"></path><path d="M620 315c0 2-2 3-3 5v1-2c-4 2-6 7-9 10l5-11h2l2-2 3-1z" class="M"></path><path d="M626 307l1 3c-2 3-6 6-6 10v2c0-1 1-2 2-2h1 0c-1 1-1 1-1 2l-8 11-8 8-4 4-3 3h-2l-1 1c0-1-1-1-1-1 1-2 2-3 3-4l18-23h0v-1c1-2 3-3 3-5l1-1 5-7z" class="e"></path><path d="M626 307l1 3c-2 3-6 6-6 10v2c-2 1-7 6-8 6 0-1 4-4 4-6v-1h0v-1c1-2 3-3 3-5l1-1 5-7z" class="j"></path><path d="M605 229c1 3 1 6 2 9h2l1 5c2 9 6 17 7 26 1 6 0 12 1 18v5 5c-1 4-1 7-2 11 0 1 0 2 1 3l-1 1c0 1-1 2-1 3 1 1 1 1 2 1l-2 2h-2c3-9 2-18 3-27 0-9 0-18-2-26-1-4-2-7-3-10l-1-3h0c0 3 1 6 1 9 1 4 1 7 0 10 0-4-1-10-3-14-1-6-4-11-7-16 0-1 0-5-1-5h-1l-1-1c0-2 0-3-1-4v-1l3 3c1 0 2 1 2 1l2 2c-1-2-1-3-1-4v-2l2 1h0v-2z" class="F"></path><path d="M605 229c1 3 1 6 2 9 1 1 1 3 1 5v1l-8-11c1 0 2 1 2 1l2 2c-1-2-1-3-1-4v-2l2 1h0v-2z" class="M"></path><path d="M616 291c2 3 1 7 0 10v6 1c1-2 0-4 0-6 1-2 0-5 1-8v-2h1v5c-1 4-1 7-2 11 0 1 0 2 1 3l-1 1c0 1-1 2-1 3 1 1 1 1 2 1l-2 2h-2c3-9 2-18 3-27z" class="N"></path><path d="M391 163c7-8 14-17 24-20 2 1 3 4 5 6-1 0-2 1-4 2l-7 5c-1 5 2 8 3 13l-1 1c0 1 0 2 1 2 0 3 0 5 1 7v1c2 5 3 10 6 14 1 1 2 4 3 4 1 3 3 5 5 7l2 2h1c-2 2-6 1-8 2-3 0-5 0-7 1h-2c-2 0-3 1-5 1h0v1c3-1 5-1 7-1v1l1 1 1 1h4l-4 1c-2 1-4 1-6 2l-7 2-9 4-1 1-2 1c-2 2-3 3-6 4-1-1-2-2-2-3l-1-2c-2-5-2-10-1-14 1 0 3-3 3-4l-2-4c0-2 0-3-1-3v-1c-1-5-1-9 0-15 1-8 5-14 9-20z" class="AK"></path><path d="M394 197l-7 7c1-3 4-5 4-8v-1c2-1 3-3 5-4 0 2-1 2-2 4v2zm13-12h1c-1-2-1-3-1-4 1 0 1 3 2 4l1-1c1 3 2 6 2 8l-1 2c0-1-1-1-1-2-1-1-1-2-2-3-2 2-4 4-7 6l2-2c1-1 3-4 5-5-3 1-5 3-8 4h0c2-2 5-4 7-5v-2z" class="a"></path><path d="M384 226v-1c0-1 0-2 1-3 1-2 5-3 7-4v1l-3 3c0 1 2 2 3 3-2 2-3 3-6 4-1-1-2-2-2-3z" class="P"></path><path d="M396 191l6-6h1c1-1 1-1 2-1l2 1v2c-2 1-5 3-7 5l-6 5v-2c1-2 2-2 2-4z" class="Q"></path><path d="M396 217h1c1 0 2 0 3-1s2-1 4 0h-1v3h1l-9 4-1 1-2 1c-1-1-3-2-3-3l3-3v-1c1 0 3-1 4-1h0z" class="AL"></path><path d="M392 218c1 0 3-1 4-1-2 2-2 3-4 4l-1 1h1 1v1c1 1 1 0 1 1l-2 1c-1-1-3-2-3-3l3-3v-1z" class="V"></path><path d="M395 223h0c-1-2 0-2 1-4 0 0 1 0 2-1h2c1-1 2-2 3-2v3h1l-9 4z" class="f"></path><path d="M408 212c3-1 5-1 7-1v1l1 1 1 1h4l-4 1c-2 1-4 1-6 2l-7 2h-1v-3h1c-2-1-3-1-4 0s-2 1-3 1h-1c2-1 3-2 4-3h0l4-2h4z" class="r"></path><path d="M408 212c3-1 5-1 7-1v1l-11 4c-2-1-3-1-4 0s-2 1-3 1h-1c2-1 3-2 4-3h0l4-2h4z" class="P"></path><path d="M393 203c2-3 4-5 7-7-2 2-2 4-3 6v1l1 1-2 2 2 1v2l1 3h-2l-3 2c-1 0-3 1-4 2h0c-1 1-3 2-4 2l-1-1 3-1-1-1h0c0-2 0-4 1-5 2-3 2-6 5-7z" class="D"></path><path d="M393 203c2-3 4-5 7-7-2 2-2 4-3 6v1l1 1-2 2 2 1v2l1 3h-2l-3 2-2-2 1-1c0-1 0-2 1-3 0 0 1-1 1-2h-1c0-1 1-1 1-1v-2-1l1 1v-1c1-1 1-1 1-2-1 1-2 2-4 3h0z" class="Q"></path><path d="M401 195c3-2 5-4 7-6 1 1 1 2 2 3l1 6v1l2 4c-1 1-1 2-1 3l1 1-1 1c-1-1-1-1-2-1 0 0-1 1-2 1l1 1c-1 1 0 1-1 1s-2 0-2 1h-1-2l1 1-4 2h0l-10 2c1-1 3-2 4-2l3-2h2l-1-3v-2l-2-1 2-2-1-1v-1c1-2 1-4 3-6l1-1z" class="V"></path><path d="M405 200v-2l1-1c0-1 0-2 1-3 1 0 1 1 2 1-1 2-2 3-4 5z" class="AB"></path><path d="M398 207l-2-1 2-2-1-1v-1l1 1c2-1 2 0 3 0 0 2-2 3-3 4z" class="C"></path><path d="M405 211c0-1-3-2-3-3s1-2 1-2v-2l1-1c1 1 1 2 2 3v1l2 1h0l1 1c-1 1 0 1-1 1s-2 0-2 1h-1z" class="R"></path><path d="M405 200c2-2 3-3 4-5l2 3v1l2 4c-1 1-1 2-1 3l1 1-1 1c-1-1-1-1-2-1 0 0-1 1-2 1h0l-2-1v-1c-1-1-1-2-2-3 0-1 1-2 1-3z" class="AA"></path><path d="M406 207l1-1v-3l1-1c1 0 1 1 2 1h1l-1-2 1-2 2 4c-1 1-1 2-1 3l1 1-1 1c-1-1-1-1-2-1 0 0-1 1-2 1h0l-2-1z" class="F"></path><path d="M408 208v-2-1l1-1h3l-1 2h1l1 1-1 1c-1-1-1-1-2-1 0 0-1 1-2 1h0z" class="M"></path><path d="M409 179c-1-3-2-5-1-7v-1c1 1 1 2 1 4l1 1v1c1 1 1 2 2 3l1-1v1c2 5 3 10 6 14 1 1 2 4 3 4 1 3 3 5 5 7l2 2h1c-2 2-6 1-8 2-3 0-5 0-7 1h-2c-2 0-3 1-5 1h0v1h-4l-1-1h2 1c0-1 1-1 2-1s0 0 1-1l-1-1c1 0 2-1 2-1 1 0 1 0 2 1l1-1-1-1c0-1 0-2 1-3l-2-4v-1l-1-6c0 1 1 1 1 2l1-2c0-2-1-5-2-8l-1-5z" class="u"></path><path d="M410 192c0 1 1 1 1 2l1-2c0 3 1 8 2 10v1h-1l-2-4v-1l-1-6z" class="I"></path><path d="M414 202v-2c1 1 2 1 2 2v1h2v-1 2c1 1 1 2 2 3l1-1c0 1 1 2 1 3-3 0-5 0-7 1h-2 0l2-1 1-2c0-1-1-3-2-4v-1z" class="F"></path><path d="M414 202v-2c1 1 2 1 2 2v1c0 1 1 2 1 3l-1 1c0-1-1-3-2-4v-1z" class="t"></path><path d="M413 203h1c1 1 2 3 2 4l-1 2-2 1h0c-2 0-3 1-5 1h0v1h-4l-1-1h2 1c0-1 1-1 2-1s0 0 1-1l-1-1c1 0 2-1 2-1 1 0 1 0 2 1l1-1-1-1c0-1 0-2 1-3z" class="AA"></path><path d="M409 179c-1-3-2-5-1-7v-1c1 1 1 2 1 4l1 1v1c1 1 1 2 2 3l1-1v1c2 5 3 10 6 14 1 1 2 4 3 4 1 3 3 5 5 7l2 2h1c-2 2-6 1-8 2 0-1-1-2-1-3-1 0-1-1-1-1-1-2-2-3-1-5-2-3-4-6-5-9-1-4-2-7-3-10l-1-2h0-1z" class="D"></path><path d="M419 200c2 2 3 4 6 6h1l1-1 2 2h1c-2 2-6 1-8 2 0-1-1-2-1-3-1 0-1-1-1-1-1-2-2-3-1-5z" class="t"></path><defs><linearGradient id="N" x1="398.514" y1="150.202" x2="396.064" y2="196.222" xlink:href="#B"><stop offset="0" stop-color="#9c8677"></stop><stop offset="1" stop-color="#af9e98"></stop></linearGradient></defs><path fill="url(#N)" d="M391 163c7-8 14-17 24-20 2 1 3 4 5 6-1 0-2 1-4 2l-7 5c-12 9-23 24-25 40l-1 6c0-2 0-3-1-3v-1c-1-5-1-9 0-15 1-8 5-14 9-20z"></path><path d="M391 163c7-8 14-17 24-20 2 1 3 4 5 6-1 0-2 1-4 2-1-1-3-1-5-1-2 1-4 4-6 4-2 1-3 2-4 3-6 5-10 12-14 19-1 2-3 6-5 8v-1c1-8 5-14 9-20z" class="Ad"></path><path d="M567 339c3-1 5-2 8-4l8-6c0 1 0 2 1 3l-1 2h0v3h1l1-1c1 1 2 1 3 2l-2 2h1c2 0 3-1 5-3h1 0l2 2 5-9c0 1 0 2-1 4 1 0 2 0 2 1-1 1-1 3-3 5 0 1 0 2 1 4-1 1-2 2-3 4 0 0 1 0 1 1l1-1h2c-3 2-6 5-9 8-2 2-4 5-6 7l-6 6 2 1c-2 1-3 2-3 4-1 2-3 3-4 5v2h-1v-1s-1 1-2 1c0-1-1-1-2-2s-2-1-4-1h0-1c-4-1-8 4-11 6l-1 1c-1 1-3 2-4 3-2 0-2 0-3-1-2 1-4 3-6 4-1 1-1 0-2 0 0-1 0-1 1-2l-1-1c-1 0-3 0-4 1l-2-2v-1l-1-1h0c0-2 0-3 1-5h-1c0-1-1-1-1-2 0-2 1-4 3-6h0v-3l-2-1c1-1 2-2 2-3h-1l1-5c2-2 4-3 5-5 0 0 6-2 7-2 6-2 12-7 17-10l6-4z" class="H"></path><path d="M574 355h2 1l3-3v1l-3 3-2 2c-5 5-10 10-16 14l-6 3h-1l3-3c1 0 2-1 2-1 1-1 2-2 3-2 1-1 2-3 2-4h1l6-5c1-2 3-3 5-5z" class="AV"></path><path d="M574 355h2 1l3-3v1l-3 3c-3 1-5 4-8 4 1-2 3-3 5-5z" class="AC"></path><path d="M571 368c1 1 1 1 1 3l1 3 1 5v2h-1v-1s-1 1-2 1c0-1-1-1-2-2s-2-1-4-1h0-1c-4-1-8 4-11 6l-1 1c-1 1-3 2-4 3-2 0-2 0-3-1-2 1-4 3-6 4 3-4 8-6 12-9l10-7c3-3 7-5 10-7z" class="G"></path><path d="M564 374v4c-4-1-8 4-11 6l-1 1c-1 1-3 2-4 3-2 0-2 0-3-1l19-13z" class="AH"></path><path d="M564 374l5-2h1l3 8s-1 1-2 1c0-1-1-1-2-2s-2-1-4-1h0-1v-4z" class="z"></path><path d="M564 374l5-2h1l-1 1-2 1v1c0 1-1 2-2 3h0-1v-4z" class="j"></path><path d="M552 368l3-1 1 1 2-1c2-1 3-2 5-2h-1c0 1-1 3-2 4-1 0-2 1-3 2 0 0-1 1-2 1l-3 3h1l-19 12h-1-2v-1l-1-1h0c0-2 0-3 1-5h-1c0-1-1-1-1-2 0-2 1-4 3-6l-1 3h1c1-1 2-1 2-2h2c1-2 6-4 8-5v3l-1 1c0 2-2 2-2 3l1-1 1 1 1-1h0l1-1c1 0 1 0 2-1 1 0 1-1 2-1l1-1c1 0 1-1 2-2z" class="AC"></path><path d="M552 368l3-1 1 1 2-1c2-1 3-2 5-2h-1l-7 5-6 2 1-2c1 0 1-1 2-2z" class="AS"></path><path d="M543 372c0 2-2 2-2 3l1-1 1 1 1-1h0l1-1c1 0 1 0 2-1 1 0 1-1 2-1l1-1-1 2c-4 2-7 5-9 8-1 1-1 2-2 3h-1c-1 1-2 1-2 1l-2 1h-1l-1 1-1-1h0c1-2 1-3 3-5 3-2 6-6 10-8z" class="AD"></path><path d="M544 368v3l-1 1c-4 2-7 6-10 8-2 2-2 3-3 5h0 0c0-2 0-3 1-5h-1c0-1-1-1-1-2 0-2 1-4 3-6l-1 3h1c1-1 2-1 2-2h2c1-2 6-4 8-5z" class="l"></path><path d="M530 380c0-1-1-1-1-2 0-2 1-4 3-6l-1 3h1c1-1 2-1 2-2h2 0v2c-1 2-4 3-5 5h-1z" class="B"></path><path d="M586 340h1c2 0 3-1 5-3h1c-1 2-2 4-3 4l-2 3-7 9h-1v-1l-3 3h-1-2c-2 2-4 3-5 5l-6 5c-2 0-3 1-5 2l-2 1-1-1-3 1c-1 1-1 2-2 2l-1 1c-1 0-1 1-2 1-1 1-1 1-2 1l-1 1h0l-1 1-1-1-1 1c0-1 2-1 2-3l1-1v-3s3-2 4-2l14-9 14-10 4-2h0l6-5z" class="e"></path><path d="M552 368c3-4 7-4 11-7 2-2 3-3 6-5h0v-1c0-1 1-2 2-3s2-2 4-2v1h-1l1 1-1 1v2c-2 2-4 3-5 5l-6 5c-2 0-3 1-5 2l-2 1-1-1-3 1z" class="H"></path><path d="M586 340h1c2 0 3-1 5-3h1c-1 2-2 4-3 4l-2 3-7 9h-1v-1l-3 3h-1-2v-2l1-1-1-1h1c2 0 3-2 4-3h1 1c0-1 1-1 2-2v-1h-2l-5 2 4-2h0l6-5z" class="AG"></path><path d="M586 340h1c2 0 3-1 5-3h1c-1 2-2 4-3 4-3 1-4 4-6 5l-2 2c-1 1-1 2-2 2h0l1-2h0c0-1 1-1 2-2v-1h-2l-5 2 4-2h0l6-5z" class="AR"></path><path d="M600 330c0 1 0 2-1 4 1 0 2 0 2 1-1 1-1 3-3 5 0 1 0 2 1 4-1 1-2 2-3 4 0 0 1 0 1 1l1-1h2c-3 2-6 5-9 8-2 2-4 5-6 7l-6 6 2 1c-2 1-3 2-3 4-1 2-3 3-4 5l-1-5-1-3c0-2 0-2-1-3l1-2 2-2c1-1 1-3 2-3l1-1-2-2 2-2 3-3h1l7-9 2-3c1 0 2-2 3-4h0l2 2 5-9z" class="l"></path><path d="M572 366c1 0 2 0 3 1l-3 4c0-2 0-2-1-3l1-2z" class="n"></path><path d="M581 357l1 3-7 7c-1-1-2-1-3-1l2-2 7-7z" class="b"></path><path d="M580 353h1v4h0l-7 7c1-1 1-3 2-3l1-1-2-2 2-2 3-3z" class="z"></path><path d="M573 374v-2l3-3h3l2 1c-2 1-3 2-3 4-1 2-3 3-4 5l-1-5z" class="j"></path><path d="M588 344c0 2 0 2 1 4h0c1 1 1 1 1 2l-5 7-3 3-1-3h0v-4l7-9z" class="F"></path><path d="M589 348c1 1 1 1 1 2l-5 7-3 3-1-3h0l8-9z" class="r"></path><path d="M600 330c0 1 0 2-1 4 1 0 2 0 2 1-1 1-1 3-3 5 0 1 0 2 1 4-1 1-2 2-3 4 0 0 1 0 1 1l1-1h2c-3 2-6 5-9 8-2 2-4 5-6 7l-1-1c0-1 0 0 1-1 0-1 1-2 1-3l-1-1 5-7c0-1 0-1-1-2h0c-1-2-1-2-1-4l2-3c1 0 2-2 3-4h0l2 2 5-9z" class="M"></path><path d="M596 348s1 0 1 1l1-1h2c-3 2-6 5-9 8-2 2-4 5-6 7l-1-1c0-1 0 0 1-1 0-1 1-2 1-3 1-2 3-3 5-5 2-1 3-3 5-5z" class="c"></path><path d="M600 330c0 1 0 2-1 4 1 0 2 0 2 1-1 1-1 3-3 5l-2 2c-1 3-4 6-6 8 0-1 0-1-1-2h0c-1-2-1-2-1-4l2-3c1 0 2-2 3-4h0l2 2 5-9z" class="AF"></path><path d="M599 334c1 0 2 0 2 1-1 1-1 3-3 5l-2 2c0-3 2-6 3-8z" class="T"></path><path d="M593 337h0l2 2-6 9c-1-2-1-2-1-4l2-3c1 0 2-2 3-4z" class="AB"></path><path d="M567 339c3-1 5-2 8-4l8-6c0 1 0 2 1 3l-1 2h0v3h1l1-1c1 1 2 1 3 2l-2 2-6 5h0l-4 2-14 10-14 9c-1 0-4 2-4 2-2 1-7 3-8 5h-2c0 1-1 1-2 2h-1l1-3h0v-3l-2-1c1-1 2-2 2-3h-1l1-5c2-2 4-3 5-5l7-2c6-2 12-7 17-10l6-4z" class="AM"></path><path d="M557 357c3-3 7-4 11-6 1-1 2-1 3-2 3-2 5-4 9-4l-4 2-14 10v-1h0c-2 0-3 1-5 1z" class="r"></path><path d="M583 334h0v3h1l1-1c1 1 2 1 3 2l-2 2c-3-2-4 1-6 2h-1c-1 0-2 1-3 1v-1c0-1 1-3 2-3 1-1 2-1 2-2h0c-2 1-4 2-6 2 3-2 6-4 9-5z" class="X"></path><path d="M557 357c2 0 3-1 5-1h0v1l-14 9c-1 0-4 2-4 2-2 1-7 3-8 5h-2c0-1 1-1 1-2l1-1c3-5 10-7 15-9 2-1 4-3 6-4z" class="Ag"></path><path d="M537 355l7-2-3 3 4-1 3-2v2c-5 3-13 10-15 15 0 1-1 1-1 2v-3l-2-1c1-1 2-2 2-3h-1l1-5c2-2 4-3 5-5z" class="N"></path><path d="M537 355l7-2-3 3-2 2c-3 2-5 5-7 7h-1l1-5c2-2 4-3 5-5z" class="l"></path><path d="M567 339c3-1 5-2 8-4l8-6c0 1 0 2 1 3l-1 2c-3 1-6 3-9 5s-7 4-10 6l-11 6c-1 1-3 3-5 4v-2l-3 2-4 1 3-3c6-2 12-7 17-10l6-4z" class="t"></path><defs><linearGradient id="O" x1="724.694" y1="264.145" x2="748.235" y2="278.227" xlink:href="#B"><stop offset="0" stop-color="#230101"></stop><stop offset="1" stop-color="#480503"></stop></linearGradient></defs><path fill="url(#O)" d="M740 224c1 2 2 3 2 4h0l1-1 3 8v1c1 6-1 11-1 17 1 1 1 2 2 3h0 0c1-3-1-5 1-7 2 0 3 0 5 1l-1 1h1c1 1 2 3 3 4v1l2 4c1 3 2 7 2 10 0 2 1 4 1 6 0 1 1 3 0 5l1 1v1c-1 1-1 2-1 3 0 2 0 4 1 6l-1 3c0 2 0 3-1 5l-3 7-3 3c-1 3-3 6-4 9 0 1 0 2 1 3l-4 6c-1 2-3 4-4 5-1 2-4 4-5 6v-2c2-7 2-16 1-24l-1-5c-2-1-2-2-4-2h-1c-2-4-4-8-6-13l-2-3h0c-1 0-1 0-2-1l-1-2-3-6c-1-3-2-4-4-6v-1c-1 0-1 0-2-1l1-2c-2-1-4-2-5-4h0 7 3 2 0c2 0 4-1 6-1 6-2 10-8 13-14 3-8 2-19 0-28z"></path><path d="M745 253c1 1 1 2 2 3h0v10c-1 0-1-1-1-1h-1c-1-3 0-8 0-12z" class="N"></path><path d="M745 265h1s0 1 1 1v4c0 3 0 7-1 10v-2-1h-2c0 1-1 2-1 3l2-15z" class="M"></path><path d="M740 224c1 2 2 3 2 4h0c1 4 2 8 2 12 0 5-1 11-1 17-1 2 0 3-1 5l-1 2v-2c1-2 1-3 1-5h-1c1-1 1-3 1-3l-1-1v-1h-1c3-8 2-19 0-28z" class="a"></path><path d="M731 287c1 1 5 5 5 7v1c1 2 2 3 2 5v1c2 2 3 8 2 11 0 1 0 1-1 1l-1-5c-2-1-2-2-4-2h-1c-2-4-4-8-6-13l2-1 1 1c1 0 1 1 2 1s1-1 2-1l-1-2c-1-1-2-3-2-4z" class="AG"></path><path d="M731 287c1 1 5 5 5 7v1l-1 1c1 1 1 3 2 5v1c-2-2-3-5-5-6-1 0-2-2-2-3 1 0 1 1 2 1s1-1 2-1l-1-2c-1-1-2-3-2-4z" class="F"></path><path d="M729 292l1 1c0 1 1 3 2 3l6 12c-2-1-2-2-4-2h-1c-2-4-4-8-6-13l2-1z" class="B"></path><path d="M715 274c2 0 3 1 4 2l1 1c3 2 9 5 11 10 0 1 1 3 2 4l1 2c-1 0-1 1-2 1s-1-1-2-1l-1-1-2 1-2-3h0c-1 0-1 0-2-1l-1-2-3-6c-1-3-2-4-4-6v-1z" class="P"></path><path d="M725 286l1-1c3 2 4 4 7 7v-1l1 2c-1 0-1 1-2 1s-1-1-2-1l-1-1-4-6z" class="E"></path><path d="M715 274c2 0 3 1 4 2l1 1h-1l6 9h0l4 6-2 1-2-3h0c-1 0-1 0-2-1l-1-2-3-6c-1-3-2-4-4-6v-1z" class="D"></path><path d="M740 252h1v1l1 1s0 2-1 3h1c0 2 0 3-1 5v2c0 2-1 5-4 7l-1 3-3 3h0-4c-3-1-6-2-10-3l-5-3c-2-1-4-2-5-4h0 7 3 2 0c2 0 4-1 6-1 6-2 10-8 13-14z" class="S"></path><path d="M733 268h1 0v2c-2 0-2 0-3 1-2 1-4 0-5 0s-1 0-2-1l2-1v1c2 0 3 0 5-1 0-1 1-1 2-1z" class="P"></path><path d="M709 267h7 3 2l-2 2h1l3 3c-1 1-2 1-3 1h0l-1 1-5-3c-2-1-4-2-5-4h0z" class="D"></path><path d="M747 270v5c1 2 0-1 1 1v4-2h0l1 4v7l-3 20c0 4-1 8-1 12v4c1 0 1 1 2 2v1c-1 2-3 4-4 5-1 2-4 4-5 6v-2c2-7 2-16 1-24 1 0 1 0 1-1 1-3 0-9-2-11 1-2 2-5 2-8l3-13c0-1 1-2 1-3h2v1 2c1-3 1-7 1-10z" class="H"></path><path d="M745 303h-1c-1-3 0-7 1-9h1l-1 9z" class="X"></path><path d="M743 333c-1 0-1-1-1-2 0-2 1-5 1-7l2-16 1 1c0 4-1 8-1 12v4c1 0 1 1 2 2v1c-1 2-3 4-4 5z" class="R"></path><path d="M747 270v5c1 2 0-1 1 1v4-2h0l1 4v7l-3 20-1-1v-5l1-9c1-4 0-10 0-14 1-3 1-7 1-10z" class="B"></path><path d="M749 274c3 1 3 1 5 4 0 1 0 2 1 3l1 4c0 2 1 3 1 5 1 2 2 3 2 5s0 3 1 5l-3 7-3 3c-1 3-3 6-4 9 0 1 0 2 1 3l-4 6v-1c-1-1-1-2-2-2v-4c0-4 1-8 1-12l3-20v-7l-1-4 1-4z" class="t"></path><path d="M749 318l1 1c0 1 0 2 1 3l-4 6v-1-2h0-1c0-2 1-3 2-4 0-1 0-1 1-2v-1z" class="B"></path><path d="M749 318h0l1-1v-1h-1c-1 1-1 1-2 1l1-2h1c0-1 0-2 1-3 1-2 1-4 1-7 1-2 1-4 1-6 1 3 0 5 2 7v4c-1 3-3 6-4 9l-1-1z" class="u"></path><path d="M749 274c3 1 3 1 5 4 0 1 0 2 1 3l1 4c0 2 1 3 1 5 1 2 2 3 2 5s0 3 1 5l-3 7-3 3v-4c1-2 0-5 0-7l2-1v-2c0-2-1-3-1-4s0-2-1-3h-1v-2c-1-1-1-2-1-3l-2-2h-1v7h0v-7l-1-4 1-4z" class="M"></path><path d="M749 274c3 1 3 1 5 4 0 1 0 2 1 3-1 2-1 4-1 5h-1v-5c-1-2-1-2-3-4l-1 1v4l-1-4 1-4z" class="AA"></path><path d="M747 256h0c1-3-1-5 1-7 2 0 3 0 5 1l-1 1h1c1 1 2 3 3 4v1l2 4c1 3 2 7 2 10 0 2 1 4 1 6 0 1 1 3 0 5l1 1v1c-1 1-1 2-1 3 0 2 0 4 1 6l-1 3c0 2 0 3-1 5-1-2-1-3-1-5s-1-3-2-5c0-2-1-3-1-5l-1-4c-1-1-1-2-1-3-2-3-2-3-5-4l-1 4h0v2-4c-1-2 0 1-1-1v-5-4-10z" class="I"></path><path d="M752 268c0-2 0-4 1-7h1l1 3-1 1c1 2 2 4 3 5l-3-3-1-1-1 2z" class="U"></path><path d="M755 264h1c0-1 0-1 1-2 0 1 1 2 1 3 0 3 1 7 0 9v1l-1-5c-1-1-2-3-3-5l1-1z" class="Q"></path><path d="M749 274c0-2 0-4 1-6h1c0 3 1 5 2 7l1-1c0 1 1 3 1 4h-1c-2-3-2-3-5-4z" class="AB"></path><path d="M758 275v-1c1-2 0-6 0-9 0 1 1 3 1 4l1 1c0 2 1 4 1 6 0 1 1 3 0 5l1 1v1c-1 1-1 2-1 3v-2c-1-1-1-2-2-3s-1-5-1-6z" class="U"></path><path d="M752 268l1-2 1 1 3 3 1 5c0 1 0 5 1 6s1 2 2 3v2c0 2 0 4 1 6l-1 3c0 2 0 3-1 5-1-2-1-3-1-5s-1-3-2-5c0-2-1-3-1-5l-1-4c-1-1-1-2-1-3h1c0-1-1-3-1-4l-2-6z" class="R"></path><path d="M747 256h0c1-3-1-5 1-7 2 0 3 0 5 1l-1 1h1c1 1 2 3 3 4v1l2 4-2-2h-1v1l-1-1h-2c-2 2 0 7-2 8-2 2-2 3-2 6-1 2 0 4 0 6v2-4c-1-2 0 1-1-1v-5-4-10z" class="Y"></path><path d="M779 116c10-3 20-5 30-5 5 0 10-1 14 0 13 4 22 9 30 20l3 3 1-1c5 8 8 21 6 30l-2 8c-2 6-7 13-14 16l-3 1-1-1c3-1 4-2 6-4h0 0c1-2 2-4 4-6 1-3 2-5 2-8l1-1v-5c0-4 0-8-2-12h-1c-1-1-1-2-2-3-4-7-12-12-20-14h-1l-2-1c-4-1-15-2-20 0 0 1 0 2-1 2h-1v-2h0c-3 0-6 1-9 1h-1l-5 2c-1 2-1 4-1 6l-1 1c0 1-1 2-2 4-1 1-3 2-4 3l-5 3c-1 1-1 1-3 2l-8 5-7 6c-2 1-3 2-4 3-1 0-1-2-1-3l3-3c1-2 1-3 1-5l-1-2c0-1 1-1 2-2l-1-2c1-1 3-3 5-4 0-1 0-2-1-4h0c-2 0-2-1-3-2 1-1-1-1-1-2v-2c0-1 1-1 1-2h-2l1-3c1 0 2-1 3-2h-1l3-1 5-4h-3v-1l13-9z" class="d"></path><path d="M819 117l1-1h2c1-1 2-1 3-1 3 1 6 2 9 4-2-1-3-1-5-1h-2l-8-1z" class="n"></path><path d="M792 124c2-1 2-1 3 0h0c2 1 2 1 4 1v1l3 3 2 1h0-3c-2-1-3-3-5-4-1 0-3-1-4-2z" class="W"></path><path d="M800 124c1 0 2 0 3 1l1-1c2 2 0 4 2 5-1 0-1 1-2 1h0l-2-1-3-3v-1c-2 0-2 0-4-1h1 4z" class="g"></path><path d="M818 123v-1c-1-2-2-4-4-5v-1h0 1c2 0 3 2 4 3v1h1c3 0 5 0 7 1h1l-1 1c-2 1-3 1-6 1v1h1c-1 1-2 1-3 0l-1-1h0z" class="Z"></path><path d="M808 120l7 6s2 1 2 2c4 1 9 0 13 1v1c-4-1-9-1-13-1h-7c0-3-1-4-1-6l-2-2 1-1z" class="n"></path><path d="M784 117h3 0l-1 1c2 1 4 1 6 2 3 1 5 2 8 4h-4-1 0c-1-1-1-1-3 0-2-1-5-3-8-3 0 0-1 1-2 1-3 0-6 1-9 2l-2 1h0c1-1 2-2 3-2v-1c3-1 5-1 7-2l1-1c0-1 1-1 2-2z" class="O"></path><path d="M784 117h3 0l-1 1c2 1 4 1 6 2-1 1-2 1-3 0-3 0-5-1-8 0l1-1c0-1 1-1 2-2z" class="AI"></path><path d="M797 116c4 2 8 4 9 8 1 2 1 4 0 5-2-1 0-3-2-5l-1 1c-1-1-2-1-3-1-3-2-5-3-8-4-2-1-4-1-6-2l1-1 2 1c2-1 4-1 7-1h0l1-1z" class="k"></path><path d="M787 117l2 1c2 0 5 1 7 2h2c2 1 5 2 6 4h0l-1 1c-1-1-2-1-3-1-3-2-5-3-8-4-2-1-4-1-6-2l1-1z" class="L"></path><path d="M802 117v-1l-1-1-1-1c2 0 5 1 7 0h15l3 1c-1 0-2 0-3 1h-2l-1 1c0 1 1 2 1 3h-1v-1c-1-1-2-3-4-3h-1 0v1c2 1 3 3 4 5v1c-5-3-9-7-15-8l5 5-1 1c-1-2-3-2-5-4z" class="b"></path><path d="M854 151c-1-2-2-4-2-6-1-3-4-7-6-10h0c5 3 10 11 11 18 1 4 1 10 0 15h0c-1 6-4 12-8 15h0 0c1-2 2-4 4-6 1-3 2-5 2-8l1-1v-5c0-4 0-8-2-12z" class="Ad"></path><path d="M853 131l3 3 1-1c5 8 8 21 6 30l-2 8v-2c-1-1 0-4 0-6 1-2 1-5 0-7-1-9-4-17-9-25h1z" class="AO"></path><path d="M779 116c10-3 20-5 30-5 5 0 10-1 14 0 13 4 22 9 30 20h-1c-3-3-6-7-10-9-1 0-1 0-2-1h-1c-1 0-1 0-2-1l-3-1c-3-2-6-3-9-4l-3-1h-15c-2 1-5 0-7 0l1 1 1 1v1c-1-1-3-1-4-2h-5l4 1-1 1h0c-3 0-5 0-7 1l-2-1h0-3c-1 1-2 1-2 2l-1 1c-2 1-4 1-7 2v1c-1 0-2 1-3 2l-2 1h-3v-1l13-9z" class="AT"></path><path d="M784 117c1-1 2-1 3-2h2 1 3l4 1-1 1h0c-3 0-5 0-7 1l-2-1h0-3z" class="AJ"></path><path d="M781 124c1-1 2 0 3 0h1c1 1 2 1 2 2v3l2 1c1 0 1 0 2-1v3c2 0 3 0 4-1v1h-1c-1 0-2 1-3 1-2 1-4 1-5 2h-3l-1 1 1 1 1-1c1 0 2 0 3 1 2-1 2-4 5-3l1 1 1-1h2l-5 2c-1 2-1 4-1 6l-1 1c0 1-1 2-2 4-1 1-3 2-4 3l-5 3c-1 1-1 1-3 2l-8 5-7 6c-2 1-3 2-4 3-1 0-1-2-1-3l3-3c1-2 1-3 1-5l-1-2c0-1 1-1 2-2l-1-2c1-1 3-3 5-4 0-1 0-2-1-4h0c-2 0-2-1-3-2 1-1-1-1-1-2v-2c0-1 1-1 1-2h-2l1-3c1 0 2-1 3-2h-1l3-1 5-4 2-1h0v1c3 0 7-2 10-2z" class="K"></path><path d="M783 131h1c0-1 1-1 2-2v1 3l-2 1-1-3z" class="W"></path><path d="M759 138h1l6 6h-3 0c-2 0-2-1-3-2 1-1-1-1-1-2v-2z" class="p"></path><path d="M762 131c4 0 6 1 10 4 1 1 2 2 2 3s-1 1-2 1h0c-2-1-4-2-6-2 1 1 2 2 3 2h1l1 1-1 1h-1l-5-3c-1-1-1-1-2-1s-2 1-2 1h-1c0-1 1-1 1-2h-2l1-3c1 0 2-1 3-2z" class="G"></path><path d="M762 131c4 0 6 1 10 4h-3c-3-1-6-1-9 1h-2l1-3c1 0 2-1 3-2z" class="k"></path><path d="M781 124c1-1 2 0 3 0h1c1 1 2 1 2 2v3l-1 1v-1c-1 1-2 1-2 2h-1l1 3h-1c-3 1-5 3-6 5h-1l1-2v-2c-2-1-2-1-4-1-1-2-2-3-4-3s-3-1-4-1h-1l5-4 2-1h0v1c3 0 7-2 10-2z" class="Z"></path><path d="M782 131h1l1 3h-1-3l-1-1c1-1 1-2 3-2z" class="n"></path><path d="M781 124h0v1c-1 0-1 0-2 1v1h0c-3-1-5 1-8 1-2 1-4 1-6 2h-1l5-4 2-1h0v1c3 0 7-2 10-2z" class="b"></path><path d="M781 124c1-1 2 0 3 0h1c1 1 2 1 2 2v3l-1 1v-1c-1 1-2 1-2 2h-1-1l-1-1c-2 0-3 0-4 1l-2-1-1-1c3 0 3 0 5-2h0v-1c1-1 1-1 2-1v-1h0z" class="o"></path><path d="M784 124h1l1 2v1h-3v-1h0l1-2z" class="G"></path><path d="M781 124c1-1 2 0 3 0l-1 2h0v1h-4v-1c1-1 1-1 2-1v-1h0z" class="n"></path><path d="M787 137h1c1-1 1-1 3-1-1 2-1 4-1 6l-1 1c0 1-1 2-2 4-1 1-3 2-4 3l-5 3c-1 1-1 1-3 2l-8 5-7 6c-2 1-3 2-4 3-1 0-1-2-1-3l3-3c1-2 1-3 1-5l-1-2c0-1 1-1 2-2l-1-2c1-1 3-3 5-4l5-3 8-4c3-1 7-2 10-4z" class="Ab"></path><path d="M761 157h0l4-4 2 1-3 3-2 2h-1v-2z" class="Ad"></path><path d="M769 145l8-4v1c0 1 0 1-1 2h1v5c-1 0-1 1-1 2l1 1c-1 1-2 1-2 1l-2 1v1l-4 3h-1l2-2h-1c-1-1-2 0-3 0v1h-2l3-3c2-1 4-4 6-5 1 0 0-2 1-3l-1-1c-1 1-2 0-4 0z" class="AO"></path><path d="M773 155l2-1v1l-8 5-7 6c-2 1-3 2-4 3-1 0-1-2-1-3l3-3c1-2 1-3 1-5l-1-2c0-1 1-1 2-2l1 3v2h1l2-2h2v-1c1 0 2-1 3 0h1l-2 2h1l4-3z" class="p"></path><path d="M760 154l1 3v2 1h1v1l-3 3-1-1c1-2 1-3 1-5l-1-2c0-1 1-1 2-2z" class="Z"></path><path d="M787 137h1c1-1 1-1 3-1-1 2-1 4-1 6l-1 1c0 1-1 2-2 4-1 1-3 2-4 3l-5 3c-1 1-1 1-3 2v-1l-2 1v-1l2-1s1 0 2-1l-1-1c0-1 0-2 1-2v-5h-1c1-1 1-1 1-2v-1c3-1 7-2 10-4z" class="AJ"></path><path d="M787 137h1c1-1 1-1 3-1-1 2-1 4-1 6l-1 1c0 1-1 2-2 4l-1-2c1-3 1-5 1-8z" class="G"></path><path d="M786 145l1 2c-1 1-3 2-4 3l-5 3c-1 1-1 1-3 2v-1l-2 1v-1l2-1s1 0 2-1l-1-1c0-1 0-2 1-2l3-4v1 1l1 1c2-1 3-2 5-3z" class="AI"></path><path d="M786 145l1 2c-1 1-3 2-4 3l-5 3v-1c2-1 2-2 3-4 2-1 3-2 5-3z" class="p"></path><defs><linearGradient id="P" x1="358.693" y1="474.819" x2="333.925" y2="495.771" xlink:href="#B"><stop offset="0" stop-color="#150000"></stop><stop offset="1" stop-color="#4f0506"></stop></linearGradient></defs><path fill="url(#P)" d="M334 456l2 1h2c5 4 11 7 15 11l6 6c1 2 2 3 4 5l11 33-1 1c0-2-1-2-1-3v-2l-1-1v-2c-1 2 0 1 0 2l-3 3c-1 0-3 1-4 2-1 0-1 1-2 2v1h0c-1 1-1 1-1 2 1 0 0 0 1-1h1c0-1 0-1 1-1h1l1-1c1-2 2-2 3-4 1 0 2 0 2 1 1 2 0 2 0 4l-1 1-1-1c-1 0-2 1-3 2h-1l-9 7h-1l-7 6c-2 2-4 4-5 7h-1 0c-2 2-5 5-5 7l1-1h1 3l-3 4h0c-1 0-1 0-2 1l-2 2c1-1 1-2 2-4v-1h-1l-1 2-2 2v-2c-1 0-1-1-2-1 0-4 1-11-1-15v-2c0-1-1-1-1-2v-2c0-1 0-3-1-4v-3c-1-1-1-1-1-3 0 0 0-1-1-1l-1-4c-1-1 0-2 0-3 1-1 1-1 1-2s0-1-1-1-1 0-2-1h0-1c-1 0-1 0-2-1h0c-1 0-1 0-2-1h-1v-1l-1-1 3-3c0-1 1-1 1-2 0-3 4-6 4-10h1v-1c1 0 1-1 2-2v-2c2-2 4-6 5-8l-1-1v-3h0c-1-2-2-1-4-2h2l-2-4 1-1 1 1c1-1 1-2 2-3h0l1-1 1 1 1-2z"></path><path d="M352 505v-7-1c1-1 1-1 1-3 1 0 1 1 2 1 1-1 1 0 1-1h-1l1-1c1 1 1 3 2 5v1c0 2 0 5 1 7l-1 2v1c0 1-1 2-2 3 0-3-1-5-1-7 0-1 1-2 1-3l-1-1-3 4z" class="Q"></path><path d="M352 505l3-4 1 1c0 1-1 2-1 3 0 2 1 4 1 7l-1 2v1h1l-6 6-1-1c0-2 0-5 1-6 2-3 2-6 2-9z" class="U"></path><path d="M348 483c0-1 0-3-1-4h1c0 1 0 3 1 4 1 9 0 21-1 29-1 4-1 7-3 11-1 1-1 2-2 2l1-4 1-3c1-4 1-8 2-12 1-7 2-15 1-23z" class="B"></path><path d="M357 515c2-2 5-4 7-5h0v1c-2 1-5 3-6 5 0 3-3 6-5 8 3-2 9-7 11-7h1l-9 7h-1l-7 6c-2 2-4 4-5 7h-1 0c4-8 9-15 15-22h0z" class="E"></path><path d="M327 465h2l-2-4 1-1 1 1c1-1 1-2 2-3h0l1-1 1 1v2l1 1c1 1 1 1 1 2 0 2 3 4 4 6v4 4l-3-2c0-1 0-2-1-3l-1-1v-1l-1-1-1-3h0l-1 1h0c-1-2-2-1-4-2z" class="m"></path><path d="M334 461c1 1 1 1 1 2 0 2 3 4 4 6v4c-1 0-2 0-3-1s-1-3-1-5l-1-6z" class="Y"></path><path d="M357 515h0c-6 7-11 14-15 22-2 2-5 5-5 7l1-1h1 3l-3 4h0c-1 0-1 0-2 1l-2 2c1-1 1-2 2-4v-1h-1l-1 2-2 2v-2c1-2 2-5 3-7l1-1 4-7 6-8 3-3 6-6h1z" class="AM"></path><path d="M339 477c1 1 2 3 2 4l2 4c1 2 1 4 1 7h1c1 2 1 3 1 5 1-2 0-5 0-7s1-4 2-7c1 8 0 16-1 23-1 4-1 8-2 12l-1 3-1-1v-3h0l-1-2v-1c-1-2 0-4-1-6h-1l1-1 1-1-1-2 2 1v-2c0-1 0-5-1-6l-1-1v-1-2-2h0v-5c-1-2-1-4-1-6-1-1-1-2-1-3z" class="C"></path><path d="M341 486l2 6c0 1 0 0 1 1 0 2 0 4 1 5 0 2 0 3-1 5 1 1 1 3 1 4l-1 1c0 1-1 3-1 4l-1 2c-1-2 0-4-1-6h-1l1-1 1-1-1-2 2 1v-2c0-1 0-5-1-6l-1-1v-1-2-2h0v-5z" class="V"></path><path d="M341 504l2 1v7l-1 2c-1-2 0-4-1-6h-1l1-1 1-1-1-2z" class="N"></path><path d="M331 467l1-1h0l1 3 1 1v1l1 1c1 1 1 2 1 3l3 2h0c0 1 0 2 1 3 0 2 0 4 1 6v5h0v2 2 1l1 1c1 1 1 5 1 6v2l-2-1 1 2-1 1-1 1h1c1 2 0 4 1 6v1l1 2h0v3l1 1-1 4c1 0 1-1 2-2l2 1-6 8-4 7-1 1c-1 2-2 5-3 7-1 0-1-1-2-1 0-4 1-11-1-15v-2c0-1-1-1-1-2v-2c0-1 0-3-1-4v-3c-1-1-1-1-1-3 0 0 0-1-1-1l-1-4c-1-1 0-2 0-3 1-1 1-1 1-2s0-1-1-1-1 0-2-1h0-1c-1 0-1 0-2-1h0c-1 0-1 0-2-1h-1v-1l-1-1 3-3c0-1 1-1 1-2 0-3 4-6 4-10h1v-1c1 0 1-1 2-2v-2c2-2 4-6 5-8l-1-1v-3z" class="z"></path><path d="M331 509l-1-2v-1h1l2 2v1h-1-1z" class="M"></path><path d="M331 509h1v3c-1 0-1 1-1 2h0l2-1v1c0 1 0 2-1 3v-1l-2-1h0-1c-1 0-2 0-2-1h0v-1c1 0 2 0 3-1l1-3zm-1-22l2 1c2-1 0-3 2-4 1 0 1 0 1 2h1l2 2c0 1 0 2-1 3l1 1v2l2 2c0 1 0 1-1 2h1l1 1-1 1c1 1 1 3 1 4l1 2-1 1c-1-1-1-2-1-3-1-1 0-2-1-4-1-1-1-1-1-3l1-1-2-1v-3l-1-1v-2h-1l1-1v-1l-2-1v-1 3c-1 1-1 1-1 2 1 2 0 4 1 6v3h-1l-1 1v-2l-2-6 1-1v-2l-1-2z" class="F"></path><path d="M331 489l1 1c0 3 1 5 1 8h-1l-2-6 1-1v-2z" class="E"></path><path d="M341 508c1 2 0 4 1 6v1l1 2h0v3l1 1-1 4c1 0 1-1 2-2l2 1-6 8-4 7-1 1h-1 0v-2l1-5c1-2 1-3 1-4s-1-2-1-3c1-1 2-2 2-3v-1-1c1-3 1-5 1-8v-1h0 1c1-1 1-2 1-4z" class="u"></path><path d="M341 508c1 2 0 4 1 6v1c0 2 0 4-1 6v-3c-1-3-1-3-1-6 1-1 1-2 1-4z" class="B"></path><path d="M337 539l-1-1c-1-2 2-6 3-8 0 1 1 2 2 2l-4 7z" class="R"></path><path d="M342 515l1 2h0v3l1 1-1 4c-1 1-2 3-3 4v-1c0-3 1-4 1-7 1-2 1-4 1-6z" class="P"></path><path d="M345 523l2 1-6 8c-1 0-2-1-2-2l1-2v1c1-1 2-3 3-4 1 0 1-1 2-2z" class="V"></path><path d="M331 467l1-1h0l1 3 1 1v1l1 1c1 1 1 2 1 3l3 2h0c0 1 0 2 1 3 0 2 0 4 1 6v5h0v2 2 1l1 1c1 1 1 5 1 6v2l-2-1c0-1 0-3-1-4l1-1-1-1h-1c1-1 1-1 1-2l-2-2v-2l-1-1c1-1 1-2 1-3l-2-2h-1c0-2 0-2-1-2-2 1 0 3-2 4l-2-1 1 2v2l-1 1c-1-2-2-4-2-5v-1c0-2 0-2-1-2l-2 1v-2c1 0 1-1 2-2v-2c2-2 4-6 5-8l-1-1v-3z" class="AL"></path><path d="M330 487v-5h0c1-2 0-2 0-3v-1c1-1 1-2 1-3l1-1c1 0 1 1 2 2v3l2 2c1 1 1 2 2 4 0 1 1 2 2 3l1 3h0v2 2 1l1 1c1 1 1 5 1 6v2l-2-1c0-1 0-3-1-4l1-1-1-1h-1c1-1 1-1 1-2l-2-2v-2l-1-1c1-1 1-2 1-3l-2-2h-1c0-2 0-2-1-2-2 1 0 3-2 4l-2-1z" class="AA"></path><path d="M762 299v1l3-6h0c0-1 0-2 2-3h0l1 1v3 1l-1 5h1c1 0 1 0 2-1s1-1 2-1l-2 3v2l2-2c1-2 2-4 4-5 0 2-1 3-2 5v2c-1 2-1 2-2 3l-1 4 1 1h1l6-6 7-7 7-2-3 5h1s1-1 2-1h0l1-1h1c1 0 2 0 2-1h1c1 1 2 1 4 1h1v2l1 2 1 2 5 6 7 6c1 3 3 5 5 6l5 5 11 8h-1c-1 1-2 0-3 0-3-1-6-2-9-2l-1-1h-4v1h0l-3 1h3 0l-3 1c-6-1-12 0-19 0l-7 1c-2 0-4-1-7-1h-4c2 0 3 0 4-1l-1-1-8 1-14 4-16 5c-2 1-3 1-5 2l-8 7-10 7 1 1-3 3 1 1c-3 2-6 4-8 7l-2-1-1-2v-2h0c0-2 1-2 2-3l-2-1c1-1 2-1 3-2l9-7 2-1 8-8 6-7c1-2 4-4 5-6 1-1 3-3 4-5l4-6c-1-1-1-2-1-3 1-3 3-6 4-9l3-3 3-7c1-2 1-3 1-5 0 1 0 3 1 4z" class="AM"></path><path d="M788 316l1-1c1-1 1-1 3 0 2 2 5 5 8 7 1 1 3 2 4 3h4c1 3 5 2 7 5l-1 1-5-1-5-1c-7-2-12-7-16-13z" class="M"></path><path d="M781 313l3-2c1 0 2 1 2 2v1h-1c-2 0-2 1-3 2l-1 1h-1 0l-1 1h1 0l1 1v2 2s1 0 2 1c2 0 3 1 5 2v-1c1 0 2 0 3 1l2 1c1 0 2 1 4 1h0 2l1 1c1 0 3 1 4 1v-1l5 1-1 1h-2-5-3-3c-1-1-2-1-4-1h-12v-1c1 0 3 0 4-1 2-1 3 0 5-1-1-1-4-1-5-1-2-1-4-1-5-1l-2-1h-3l-1-1 3-3 5-6 1-1z" class="l"></path><path d="M759 336v-1c0-1 2-3 3-4l4-5v-1l3-3c4-3 8-7 11-8l-5 6-3 3 1 1h3l2 1c1 0 3 0 5 1 1 0 4 0 5 1-2 1-3 0-5 1-1 1-3 1-4 1v1c2 1 4 1 6 0v1c-2 0-5 0-7 1l-3 1 3 1-3 2-14 4-4-2c2 0 2 0 3-1l-1-1h0z" class="z"></path><path d="M772 327c-1 1-3 1-4 2-2 0-3 1-4 2 1-2 2-4 4-5l1 1h0 3z" class="F"></path><path d="M775 333l3 1-3 2-14 4-4-2c2 0 2 0 3-1l-1-1h0c5-2 11-1 16-3h0z" class="R"></path><path d="M768 326l1-2 1-1c2-2 3-2 5-3l-3 3 1 1h3l2 1c1 0 3 0 5 1 1 0 4 0 5 1-2 1-3 0-5 1-1 1-3 1-4 1v1c2 1 4 1 6 0v1c-2 0-5 0-7 1l-3 1h0c-1 0-2 0-3-1s-1-1-2-1h-1v-1c2 0 4-1 6-3h-3-3 0l-1-1z" class="AR"></path><path d="M772 323l1 1h3l2 1c1 0 3 0 5 1h-10-1v-3z" class="H"></path><defs><linearGradient id="Q" x1="801.357" y1="335.468" x2="801.8" y2="328.588" xlink:href="#B"><stop offset="0" stop-color="#b50e0d"></stop><stop offset="1" stop-color="#e11a19"></stop></linearGradient></defs><path fill="url(#Q)" d="M814 331h2v-1l1 1 3-1c2 0 4 1 5 2l1 1-2 1h-4v1h0l-3 1h3 0l-3 1c-6-1-12 0-19 0l-7 1c-2 0-4-1-7-1h-4c2 0 3 0 4-1l-1-1-8 1 3-2-3-1 3-1c2-1 5-1 7-1v-1c-2 1-4 1-6 0h12c2 0 3 0 4 1h3 3 5 2l1-1 5 1z"></path><path d="M795 333c8-1 17 0 25 1v1h0l-3 1c-5 0-12 1-17-1v-1l-5-1z" class="Ag"></path><path d="M783 335c4-1 8-1 12-2l5 1v1c5 2 12 1 17 1h3 0l-3 1c-6-1-12 0-19 0l-7 1c-2 0-4-1-7-1h-4c2 0 3 0 4-1l-1-1z" class="AF"></path><path d="M784 337l7-1c2 1 5 1 7 1l-7 1c-2 0-4-1-7-1z" class="r"></path><path d="M783 335c4-1 8-1 12-2l5 1v1c-3 0-6 0-9 1l-7 1h-4c2 0 3 0 4-1l-1-1z" class="Ak"></path><path d="M786 299l7-2-3 5h1s1-1 2-1l-1 2h-1v3h0l1 1v1l1 1c0 1 1 1 1 1l-2 3v2c-2-1-2-1-3 0l-1 1-2-2v-1c0-1-1-2-2-2l-3 2-1 1c-3 1-7 5-11 8l-3 3v1l-4 5c-1 1-3 3-3 4v1h0c-3 1-5 2-8 1h0c1-1 2-1 3-2l7-8c2-1 3-3 5-5v-2c0-1 0-2 2-3l3-3 2-2 6-6 7-7z" class="N"></path><path d="M782 311l-1-1c-1 0-3 0-4-1h1c0-1 1-2 2-2h1c1 0 1-1 2-1v1l1 1c1 0 1-1 1-1 2-1 4-3 6-4v3h0l-4 1-1 2h1v2h0 0l-1 1c-1-2-1-2-2-3-2 0-2 1-2 2z" class="E"></path><path d="M791 306l1 1v1l1 1c0 1 1 1 1 1l-2 3v2c-2-1-2-1-3 0l-1 1-2-2v-1c0-1-1-2-2-2l-3 2 1-2c0-1 0-2 2-2 1 1 1 1 2 3l1-1h0 0v-2h-1l1-2 4-1z" class="u"></path><path d="M786 313c1 0 2-1 3-1 1 1 2 1 3 1v2c-2-1-2-1-3 0l-1 1-2-2v-1z" class="q"></path><path d="M791 306l1 1c0 2 0 3-1 5h-1l-1-2-2 1h0v-2h-1l1-2 4-1z" class="B"></path><path d="M768 317l3-3 2 1 1-1c0 1-1 2-1 3l-1 1v1h-1 0l-1 1c-3 4-7 6-9 10-1 2-5 5-7 5l7-8c2-1 3-3 5-5v-2c0-1 0-2 2-3z" class="AL"></path><path d="M768 317l3-3 2 1c-2 1-3 2-4 3l-1-1z" class="R"></path><path d="M794 300h1c1 0 2 0 2-1h1c1 1 2 1 4 1h1v2l1 2 1 2 5 6 7 6c1 3 3 5 5 6l5 5 11 8h-1c-1 1-2 0-3 0-3-1-6-2-9-2l-1-1 2-1-1-1c-1-1-3-2-5-2l-3 1-1-1v1h-2l1-1c-2-3-6-2-7-5h-4c-1-1-3-2-4-3-3-2-6-5-8-7v-2l2-3s-1 0-1-1l-1-1v-1l-1-1h0v-3h1l1-2h0l1-1z" class="T"></path><path d="M804 316l1-2c2 5 7 13 12 15l1 1h1 1l-3 1-1-1v1h-2l1-1c-2-3-6-2-7-5h-4c-1-1-3-2-4-3 1-3 2-4 4-6z" class="X"></path><path d="M804 316c1 2 3 5 4 8v1h-4c-1-1-3-2-4-3 1-3 2-4 4-6z" class="T"></path><path d="M794 300h1c1 0 2 0 2-1h1c1 1 2 1 4 1h1v2l1 2 1 2 5 6h0c-1-1-2-1-3-2l-2-2-1 1 1 3v2l-1 2c-2 2-3 3-4 6-3-2-6-5-8-7v-2l2-3s-1 0-1-1l-1-1v-1l-1-1h0v-3h1l1-2h0l1-1z" class="e"></path><path d="M794 300h1c1 0 2 0 2-1h1c1 1 2 1 4 1h1v2l1 2 1 2c-1 1-2 1-3 2-1-1-1-1-3-1l-2 1h-1v-2h-1v1c0 2 0 2-1 3 0 0-1 0-1-1l-1-1v-1l-1-1h0v-3h1l1-2h0l1-1z" class="j"></path><path d="M794 300h1c1 0 2 0 2-1h1c1 1 2 1 4 1h1v2l1 2-2 3-1-1c0-1 0-2-1-3h-8l1-2h0l1-1z" class="t"></path><path d="M794 300h1c1 0 2 0 2-1h1c1 1 2 1 4 1h1v2c-4 0-6-1-10-1l1-1z" class="E"></path><path d="M762 299v1l3-6h0c0-1 0-2 2-3h0l1 1v3 1l-1 5h1c1 0 1 0 2-1s1-1 2-1l-2 3v2l2-2c1-2 2-4 4-5 0 2-1 3-2 5v2c-1 2-1 2-2 3l-1 4 1 1h1l-2 2-3 3c-2 1-2 2-2 3v2c-2 2-3 4-5 5l-7 8c-1 1-2 1-3 2h0c3 1 5 0 8-1l1 1c-1 1-1 1-3 1l4 2-16 5c-2 1-3 1-5 2l-8 7-10 7 1 1-3 3 1 1c-3 2-6 4-8 7l-2-1-1-2v-2h0c0-2 1-2 2-3l-2-1c1-1 2-1 3-2l9-7 2-1 8-8 6-7c1-2 4-4 5-6 1-1 3-3 4-5l4-6c-1-1-1-2-1-3 1-3 3-6 4-9l3-3 3-7c1-2 1-3 1-5 0 1 0 3 1 4z" class="AD"></path><path d="M712 365h0c1 0 1 0 2-1l9-7c2-1 4-3 5-4 4-2 7-5 10-8l2 2-8 7-10 7 1 1-3 3 1 1c-3 2-6 4-8 7l-2-1-1-2v-2h0c0-2 1-2 2-3z" class="c"></path><path d="M720 365c-3 2-5 3-7 5h-1l1-2c0-1 1-1 1-2 3-2 5-4 8-5l1 1-3 3z" class="AV"></path><path d="M762 299v1l3-6h0c0-1 0-2 2-3h0l1 1v3 1l-1 5h1c1 0 1 0 2-1s1-1 2-1l-2 3-10 13h-1v-1c-1 0-2 0-2 2 0 1 0 0-1 2h0c-1 2-2 3-3 5h0c-1 1-2 2-3 4h0 0c-1 1-1 1-1 2l-1 1-1 1c-2 1-1 2-2 3-1 2-3 4-5 5l-1 1-1 1-1 1-4 4h-1l6-7c1-2 4-4 5-6 1-1 3-3 4-5l4-6c-1-1-1-2-1-3 1-3 3-6 4-9l3-3 3-7c1-2 1-3 1-5 0 1 0 3 1 4z" class="X"></path><path d="M762 299v1l3-6h0c0-1 0-2 2-3h0c-3 7-5 13-9 19l-1-3 3-7c1-2 1-3 1-5 0 1 0 3 1 4z" class="C"></path><path d="M757 307l1 3-7 12c-1-1-1-2-1-3 1-3 3-6 4-9l3-3z" class="V"></path><path d="M768 296l-1 5h1c1 0 1 0 2-1s1-1 2-1l-2 3-10 13h-1v-1c4-6 6-12 9-18z" class="l"></path><path d="M770 304l2-2c1-2 2-4 4-5 0 2-1 3-2 5v2c-1 2-1 2-2 3l-1 4 1 1h1l-2 2-3 3c-2 1-2 2-2 3v2c-2 2-3 4-5 5l-7 8c-1 1-2 1-3 2h0c3 1 5 0 8-1l1 1c-1 1-1 1-3 1l4 2-16 5c-2 1-3 1-5 2l-2-2 3-2 6-7 8-9-1-1c0-2 1-3 2-4s1-2 2-3l2-4 10-13v2z" class="C"></path><path d="M747 336c1 1 2 2 3 2 1 1 2-1 3 1l-2 2s1 0 1-1c2-1 3-1 5-2l4 2-16 5c-2 1-3 1-5 2l-2-2 3-2 6-7z" class="M"></path><path d="M747 336c1 1 2 2 3 2 1 1 2-1 3 1l-2 2h-1c-1 0-2 1-3 1-1 1-3 2-5 2l-1-1 6-7z" class="AX"></path><path d="M770 304l2-2c1-2 2-4 4-5 0 2-1 3-2 5v2c-1 2-1 2-2 3l-1 4 1 1h1l-2 2-3 3c-2 1-2 2-2 3v2c-2 2-3 4-5 5 0-1 1-1 1-2l1-1v-1c1 0 1-1 2-1l-1-1c1-1 1-2 1-3-2 2-4 5-6 6h0c-1 1-2 1-2 1l-1 1-1 1-1-1c0-2 1-3 2-4s1-2 2-3l2-4 10-13v2z" class="AF"></path><path d="M770 304l2-2c1-2 2-4 4-5 0 2-1 3-2 5v2c-1 2-1 2-2 3l-6 7-3 3c0-2 1-4 2-6l1-2 1-1c2-1 3-2 3-4z" class="X"></path><path d="M763 317l3-3 6-7-1 4 1 1h1l-2 2-3 3c-2 1-2 2-2 3v2c-2 2-3 4-5 5 0-1 1-1 1-2l1-1v-1c1 0 1-1 2-1l-1-1c1-1 1-2 1-3-2 2-4 5-6 6h0c-1 1-2 1-2 1l-1 1 7-9z" class="P"></path><defs><linearGradient id="R" x1="288.38" y1="440.412" x2="288.577" y2="464.822" xlink:href="#B"><stop offset="0" stop-color="#260101"></stop><stop offset="1" stop-color="#500a0a"></stop></linearGradient></defs><path fill="url(#R)" d="M272 420h0v4c2 2 6 2 8 2h0l20 7 8 3-2 2 3 1c4 1 7 2 10 3h1l2-1v-1l6 4-1 1c1 2 5 5 5 7v1s1 1 2 1c0 0 2-3 3-3h2c-2 1-4 3-5 5l-1 2-1-1-1 1h0c-1 1-1 2-2 3l-1-1-1 1 2 4h-2c2 1 3 0 4 2h0v3l1 1c-1 2-3 6-5 8v2c-1 1-1 2-2 2v1c-2-1-3-4-3-5l-1-3-2-5-3-5-1-1c0-2-1-3-2-5h-2v2l-2-1v1h-2c-1 0-1 1-2 1l2 2v1 1c1 1 1 2 1 3l-1 1c-4-2-9-5-13-5h0c0 1 1 1 1 2-2 1-2 0-3 0s-3 2-4 2l1 1c2 1 2 4 4 6l-8-4-5-4-5-3-3-1-2-1-1-1c-5-2-7-5-11-7h-1l-2-1-1 1v1l-2-2c-1 1-2 2-4 2-1 0-2 0-4-1v-3c2-1 4-2 6-4l-1-2c2-1 3-2 4-3h2c0-1 0-2-1-4v-1l1-1h0v-5h-2v-3l-2 1h-1l3-3 4-5h4l-1 2c1 0 2 0 2 1 3-2 5-4 8-5l2-1z"></path><path d="M313 460h1c1 1 1 3 2 4v-2-1c0-1-1-1-1-2h0v-1-2l1-1 3 3c1 0 1 0 2 1 0 1 1 3 2 4v1h0l-2-2c-1-1-1-2-2-3-1 1-2 1-2 1v6h-1l-1-1c0-2-1-3-2-5h0z" class="C"></path><path d="M253 444h2c0-1 0-2-1-4v-1l1-1 1 4 3 3c-3 2-5 3-9 4l-1-2c2-1 3-2 4-3z" class="AF"></path><path d="M316 466h1v-6s1 0 2-1c1 1 1 2 2 3l2 2h0c1 2 2 3 2 5-2-1-4-4-6-4-1 1-1 2 0 3l2 8h0l-2-5-3-5z" class="f"></path><path d="M323 463h2l2 2c2 1 3 0 4 2h0v3c-1 2-1 3-2 4l-1-1c-1-1-1-2-2-4h-1c0-2-1-3-2-5v-1z" class="a"></path><path d="M319 468c-1-1-1-2 0-3 2 0 4 3 6 4h1c1 2 1 3 2 4l1 1c1-1 1-2 2-4l1 1c-1 2-3 6-5 8-2-2-2-6-4-8-1-2-2-3-4-3z" class="AA"></path><path d="M308 453c-2 0-3 1-4 2h-3l1-1h2l-1-1-6-3h-2c-2 1-2 2-4 1v-1h0c1 0 2 0 2-1-1-1-3-1-4-1h-2-2v-1h1 2c1 0 4 1 5 1 3 1 6 0 8 1 1 0 3 0 4 1l1-1c1 1 2 3 2 4h0z" class="S"></path><path d="M319 468c2 0 3 1 4 3 2 2 2 6 4 8v2c-1 1-1 2-2 2v1c-2-1-3-4-3-5l-1-3h0l-2-8z" class="M"></path><path d="M322 479l3 1 1-1v1l1 1c-1 1-1 2-2 2v1c-2-1-3-4-3-5z" class="B"></path><defs><linearGradient id="S" x1="258.499" y1="450.805" x2="280.476" y2="459.722" xlink:href="#B"><stop offset="0" stop-color="#460605"></stop><stop offset="1" stop-color="#6e0d0e"></stop></linearGradient></defs><path fill="url(#S)" d="M252 455c1 0 2-1 3-1h2v-1l4-1c8-2 18-1 26 2-1 0-2 1-2 1-2 1-3-1-4 1v1c-1 1-2 1-3 2-2 0-4 0-5 1s-1 2-1 3v2l-2-1-1-1c-5-2-7-5-11-7h-1l-2-1-1 1v1l-2-2z"></path><path d="M270 464c-1-2-2-4-1-6h4 2c1 0 2 1 3 1-2 0-4 0-5 1s-1 2-1 3v2l-2-1z" class="U"></path><path d="M292 436c5 0 10 0 14 2l3 1c4 1 7 2 10 3h1l2-1v-1l6 4-1 1c1 2 5 5 5 7v1s1 1 2 1c0 0 2-3 3-3h2c-2 1-4 3-5 5l-1 2-1-1 1-1v-1c-2-2-6-3-8-4-4-3-9-6-15-8-5-2-12-2-17-3-9-1-18 0-25 2-4 1-6 1-9 3l-3-3c4 0 9-3 14-4 7-2 14-2 22-2z" class="AH"></path><path d="M292 436c5 0 10 0 14 2l3 1c4 1 7 2 10 3l1 1c1 1 2 1 2 2l-1 1-1-1c-2-2-7-4-10-4-1 0-3 0-5-1-3 0-5-1-8-1h2v-1l-7-2z" class="z"></path><path d="M322 440l6 4-1 1c1 2 5 5 5 7v1c-4-2-8-5-11-7-4-2-7-3-11-5 3 0 8 2 10 4l1 1 1-1c0-1-1-1-2-2l-1-1h1l2-1v-1z" class="M"></path><path d="M322 440l6 4-1 1c-2 0-3 0-5-1 0-1-1-1-2-2l2-1v-1z" class="E"></path><path d="M308 453c2 2 3 5 5 7h0-2v2l-2-1v1h-2c-1 0-1 1-2 1l2 2v1 1c1 1 1 2 1 3l-1 1c-4-2-9-5-13-5h0c0 1 1 1 1 2-2 1-2 0-3 0s-3 2-4 2l1 1c2 1 2 4 4 6l-8-4-5-4-5-3-3-1v-2c0-1 0-2 1-3s3-1 5-1c1-1 2-1 3-2v-1c1-2 2 0 4-1 0 0 1-1 2-1l5 1c1 1 1 1 2 1h2l1 1c0-1 1-1 2-1v1h2c1 0 2 1 3 1v-2h1 2c1-1 1-1 1-3h0z" class="u"></path><path d="M275 466l-1-3c1-1 1-1 2-1 2-3 5-2 8-2h4v-1c-1 0-2-1-3-2l1-1 4 2 2-1c2 0 6 2 8 3-1 0-3-1-4-1l-1 2c1 1 3 2 5 3-4-1-8-2-11-2s-7 0-10-1l-2 1h0l3 1v1c-1 1-1 1-1 2s0 2 1 3l-5-3z" class="AA"></path><path d="M308 453c2 2 3 5 5 7h0-2v2l-2-1v1h-2c-1 0-1 1-2 1-2-1-3-2-5-3s-6-3-8-3l-2 1-4-2-1 1c1 1 2 2 3 2v1h-4c-3 0-6-1-8 2-1 0-1 0-2 1l1 3-3-1v-2c0-1 0-2 1-3s3-1 5-1c1-1 2-1 3-2v-1c1-2 2 0 4-1 0 0 1-1 2-1l5 1c1 1 1 1 2 1h2l1 1c0-1 1-1 2-1v1h2c1 0 2 1 3 1v-2h1 2c1-1 1-1 1-3h0z" class="AL"></path><path d="M272 420h0v4c2 2 6 2 8 2h0l20 7 8 3-2 2c-4-2-9-2-14-2-8 0-15 0-22 2-5 1-10 4-14 4l-1-4h0v-5h-2v-3l-2 1h-1l3-3 4-5h4l-1 2c1 0 2 0 2 1 3-2 5-4 8-5l2-1z" class="v"></path><path d="M265 431c0-1 2-3 3-4l1 1c1-1 2-1 3 0l1 1c2-1 4 0 5 0 2 0 3 1 5 1-4 0-9-1-12 0l-2 1c-2 1-3 1-4 0z" class="I"></path><path d="M257 423h4l-1 2c1 0 2 0 2 1-2 2-5 4-6 7h-1-2v-3l-2 1h-1l3-3 4-5z" class="H"></path><path d="M271 430c3-1 8 0 12 0 4 2 9 4 13 5h4 1c1 1 2 1 3 1l-4-3 8 3-2 2c-4-2-9-2-14-2-8 0-15 0-22 2-5 1-10 4-14 4l-1-4h0v-5h1c0 1 0 1 1 3l4-2c1 0 2-1 4-3 1 1 2 1 4 0l2-1z" class="a"></path><path d="M271 430c1 1 2 1 2 1 0 1-1 2-2 2s-2-1-2-2l2-1z" class="P"></path><path d="M265 431c1 1 2 1 4 0 0 1 1 2 2 2-1 0-2 1-3 1-3 0-4 1-6 1l-1-1c1 0 2-1 4-3z" class="C"></path><defs><linearGradient id="T" x1="244.228" y1="336.741" x2="256.129" y2="387.04" xlink:href="#B"><stop offset="0" stop-color="#200100"></stop><stop offset="1" stop-color="#420506"></stop></linearGradient></defs><path fill="url(#T)" d="M215 336c1 0 3 0 5 1 4 1 11 0 15 0v-1c3 0 5 0 7 1 3 1 6 1 9 3 3 3 6 8 10 10 2 1 4 3 7 3l1 2 1 1 8 8 1 1v1l3 3-2 1c1 1 10 9 10 10l-1 1v2h-1c0 4-1 7-2 11h0c-1-1-1-1-1-2l-1-1c-1 0-1-1-2-2h0-1l-2 6c-2 4-5 8-8 12l-5 6-1 2v1l-3-1v-1c-1-2-2-4-3-5-1-3-3-5-5-8l-1-3-3-3c-1-2-1-1-1-2-1-1-2-2-2-3h-1c1-1 2-1 3-2-3-5-8-10-13-14-1-1-3-2-4-3h0c-3-5-8-8-11-12l-12-9c4 0 9 5 12 7 1 0 1 0 2-1 0-3 0-4-3-6l1-1-1-1h-5c-1 1-2 1-3 1l-1-1-1-1 4-4c2-2 2-2 3-5l-2-2z"></path><path d="M217 338c0 1 0 1 1 1 2 0 2 0 3 1l1 1c-4 1-9 3-11 7l-1-1 4-4c2-2 2-2 3-5z" class="X"></path><path d="M221 340h11 1c0 1 1 1 1 1 3 1 7 2 9 1 2 2 2 2 3 4-8-2-16-5-24-5l-1-1z" class="AF"></path><path d="M282 383v-5c1 0 4 3 5 4l1 1c0 4-1 7-2 11h0c-1-1-1-1-1-2l-1-1c-1 0-1-1-2-2h0-1-1c1-2 1-4 2-6z" class="q"></path><path d="M250 348h3c3 1 5 3 7 3 1 0 1 0 1-1 2 1 4 3 7 3l1 2 1 1 8 8 1 1v1l3 3-2 1c-8-8-19-16-30-22z" class="AD"></path><path d="M279 366h0l-12-10v-1l3 1 8 8 1 1v1z" class="AS"></path><path d="M255 363s1 1 2 1l2 1h1l3-3c-4-3-7-5-11-7v-1c5 2 7 5 11 7 2 1 4 2 6 4l6 6c1 1 2 1 2 2 0 2 0 2 1 3 1 2 1 5 2 7h2c-1 2-1 4-2 6h1l-2 6-1-1c0-1-1-2 0-3 0-2 0-4-1-6v-1c0-2-1-4-2-6v-1c-2-4-4-7-7-9-2-2-3-4-6-4l-1 2h-3-1l-2-3h0z" class="Q"></path><path d="M280 383h2c-1 2-1 4-2 6-1-3-1-4 0-6z" class="D"></path><path d="M215 336c1 0 3 0 5 1 4 1 11 0 15 0v-1c3 0 5 0 7 1 3 1 6 1 9 3 3 3 6 8 10 10 0 1 0 1-1 1-2 0-4-2-7-3h-3l-4-2c-1-2-1-2-3-4-2 1-6 0-9-1 0 0-1 0-1-1h-1-11c-1-1-1-1-3-1-1 0-1 0-1-1l-2-2z" class="AD"></path><path d="M235 336c3 0 5 0 7 1 3 1 6 1 9 3 3 3 6 8 10 10 0 1 0 1-1 1-2 0-4-2-7-3h-3l-4-2c-1-2-1-2-3-4 3 1 5 3 8 3v-2c0-1-1-1-2-2h0l-1-1h-1c-2-1-4-1-6-2h-2c-1 0-2 0-3-1h-1v-1z" class="AC"></path><path d="M232 358v-1c-1-2-3-3-4-4l-5-4c-1 0-2-1-3-2h1 1v-1h1c10 1 20 3 29 8v1c4 2 7 4 11 7l-3 3h-1l-2-1c-1 0-2-1-2-1h0l2 3h1v1c-1 1-1 1-2 1l-1-1h-3c-2 1-2 3-3 5v1l1 1-2 1 2 2h-4l-3-2-2-3-3-6-1-1-5-7z" class="D"></path><path d="M245 357h0c-2-1-5-2-6-3-2 0-2 0-3-1l1-1 1 1h1l-1-1v-1c4 2 9 4 12 7l1 1c-1 0-2-1-2 0l-4-2z" class="I"></path><path d="M243 359c1 0 2 0 2-1h-1l1-1 4 2 1 1c1 0 2 1 3 2l2 1h0l-3 1h-2v-1l-2-1-2-2h0-2l-1-1z" class="B"></path><path d="M248 362l1-1c1 1 3 1 4 1l2 1h0l-3 1h-2v-1l-2-1z" class="V"></path><defs><linearGradient id="U" x1="235.069" y1="361.983" x2="249.577" y2="367.943" xlink:href="#B"><stop offset="0" stop-color="#570505"></stop><stop offset="1" stop-color="#840d0e"></stop></linearGradient></defs><path fill="url(#U)" d="M237 365h1c0-2-1-4-2-6v-1h5 0c1 0 2 1 2 1l1 1h2 0l2 2 2 1v1h2l3-1 2 3h1v1c-1 1-1 1-2 1l-1-1h-3c-2 1-2 3-3 5v1l1 1-2 1 2 2h-4l-3-2-2-3-3-6-1-1z"></path><path d="M241 372c1-1 1-2 1-2h1l5 5 2 2h-4l-3-2-2-3z" class="M"></path><path d="M255 363l2 3h1v1c-1 1-1 1-2 1l-1-1h-3c-2 1-2 3-3 5v1h0c-1-2-2-3-3-5v-3c1 0 2-1 3-1l1 1s2 0 2-1h0l3-1z" class="R"></path><path d="M255 363l2 3c-2 0-3 0-5-2h0l3-1z" class="AB"></path><path d="M246 368c1-1 1-2 2-3 2 0 2 1 4 2-2 1-2 3-3 5v1h0c-1-2-2-3-3-5z" class="AA"></path><path d="M258 366h3l1-2c3 0 4 2 6 4 3 2 5 5 7 9v1c1 2 2 4 2 6v1c1 2 1 4 1 6-1 1 0 2 0 3l1 1c-2 4-5 8-8 12v-2c-1 1-1 1-3 2-1-2-2-5-3-7-1-3-2-6-3-8-3-5-5-9-8-14-2-1-3-2-4-4l-1-1v-1c1-2 1-4 3-5h3l1 1c1 0 1 0 2-1v-1z" class="q"></path><path d="M269 382c1 1 2 1 3 2v1l-2 3c0-2-1-4-1-6z" class="z"></path><path d="M252 367h3l1 1c1 0 1 0 2-1 1 1 3 3 4 5 1 0 3 1 4 2 2 3 4 6 6 10-1-1-2-1-3-2-2-3-5-5-7-8v-1h-1c-1 1-3 0-4-1v-1l-1 2 1 1c-1 1-2 1-3 2v2c-2-1-3-2-4-4l-1-1v-1c1-2 1-4 3-5z" class="M"></path><path d="M258 366h3l1-2c3 0 4 2 6 4 3 2 5 5 7 9v1c1 2 2 4 2 6v1c1 2 1 4 1 6-1 1 0 2 0 3l1 1c-2 4-5 8-8 12v-2c1-2 1-4 2-7 0-1 1-3 1-4 1-1-1-3-1-4 0-2 0-3-1-5v-1c-2-4-4-7-6-10-1-1-3-2-4-2-1-2-3-4-4-5v-1z" class="AB"></path><path d="M221 357c1 0 1 0 2-1 0-3 0-4-3-6l1-1c2 1 5 3 6 4l5 5 5 7 1 1 3 6 2 3 3 2h4l-2-2 2-1c1 2 2 3 4 4 3 5 5 9 8 14 1 2 2 5 3 8 1 2 2 5 3 7 2-1 2-1 3-2v2l-5 6-1 2v1l-3-1v-1c-1-2-2-4-3-5-1-3-3-5-5-8l-1-3-3-3c-1-2-1-1-1-2-1-1-2-2-2-3h-1c1-1 2-1 3-2-3-5-8-10-13-14-1-1-3-2-4-3h0c-3-5-8-8-11-12l-12-9c4 0 9 5 12 7z" class="c"></path><path d="M225 361h2l1-1c2 0 2 0 3 2h0 0c0 1 1 2 1 3l-1 1c-2 0-5-3-6-5z" class="F"></path><path d="M249 388l2 3 4 6-2 1-3-3c-1-2-1-1-1-2-1-1-2-2-2-3h-1c1-1 2-1 3-2z" class="C"></path><path d="M249 388l2 3c-1 0-1 1-1 1l-3-2h-1c1-1 2-1 3-2z" class="D"></path><path d="M243 375l3 2h4l1 1h-3c1 0 1 1 2 2l1 1v1h-1l1 1-1 1-1-1h-1l-1-1-2-2c-2-2-2-3-2-5z" class="l"></path><path d="M225 361l2-1v-1c1 0 1 0 2-1 0-2-2-2-2-5l5 5 5 7 1 1c-3 0-5-2-7-4h0c-1-2-1-2-3-2l-1 1h-2 0z" class="B"></path><path d="M255 397l10 18v1l-3-1v-1c-1-2-2-4-3-5-1-3-3-5-5-8l-1-3 2-1z" class="V"></path><path d="M221 357c1 0 1 0 2-1 0-3 0-4-3-6l1-1c2 1 5 3 6 4 0 3 2 3 2 5-1 1-1 1-2 1v1l-2 1-4-4z" class="AL"></path><path d="M248 375l2-1c1 2 2 3 4 4 3 5 5 9 8 14 1 2 2 5 3 8 1 2 2 5 3 7 2-1 2-1 3-2v2l-5 6c-1-2 0-4 0-6s-1-5-2-7c-3-8-7-15-13-22l-1-1-2-2z" class="AA"></path><defs><linearGradient id="V" x1="323.356" y1="408.949" x2="306.61" y2="440.567" xlink:href="#B"><stop offset="0" stop-color="#190000"></stop><stop offset="1" stop-color="#4f0807"></stop></linearGradient></defs><path fill="url(#V)" d="M288 383h1v4l1 1 2-1c1 1 2 1 3 2 2 1 4 2 6 4 3 3 8 5 12 7h0 4 1v1l2-1 6 2c3 2 5 2 8 2l1-1-1-2c1 0 2-1 3-1l3 9 3 10 8 25 1 4 1 4h0c-1-1-2-2-2-3v-1h-2v1c0 2-1 2-2 4-1 1-1 1 0 2v1h0-4-1c-2 0-2 0-4 1h-2l-2-1c1-2 3-4 5-5h-2c-1 0-3 3-3 3-1 0-2-1-2-1v-1c0-2-4-5-5-7l1-1-6-4v1l-2 1h-1c-3-1-6-2-10-3l-3-1 2-2-8-3-20-7h0c-2 0-6 0-8-2v-4h0l-2 1c-3 1-5 3-8 5 0-1-1-1-2-1l1-2h-4l2-2-1-1 1-3h0c2-1 2-2 3-3v1l3 1v-1l1-2 5-6c3-4 6-8 8-12l2-6h1 0c1 1 1 2 2 2l1 1c0 1 0 1 1 2h0c1-4 2-7 2-11z"></path><path d="M317 433c2-1 3-2 5-2 1 0 2 1 3 1 2-1 4-1 6-1v1l-1 1-1-1h1c-2-1-4 1-5 1h-8z" class="I"></path><path d="M314 429c3-2 6-5 10-6-3 3-8 6-9 10h0v-1h-1v2h-1v-2l1-2v-1z" class="E"></path><path d="M328 444l10 6s2-1 3 0h0l-2 1h-2c-1 0-3 3-3 3-1 0-2-1-2-1v-1c0-2-4-5-5-7l1-1z" class="F"></path><path d="M351 444l1 4 1 4h0c-1-1-2-2-2-3v-1h-2v1c-1 0-3 1-4 2-1 0-1 1-2 2h0l-1-2h-1v-1h0c1-2 3-2 5-3s3-2 5-3z" class="D"></path><path d="M303 409h8c8 1 17 2 25 5v1c-12-3-23-4-35-4 0-1 1-1 1-1h1v-1z" class="T"></path><path d="M315 433h2 8v1 1h5l1-1c0 1-1 1-1 2 0 2 2 2 0 4l-1 1c0-1 0-1-1-1-2-1-3-1-6-2v-1c-2-1-3-2-5-2-1 0-1-1-2-2h0zm26 17v1h1l1 2h0c1-1 1-2 2-2 1-1 3-2 4-2 0 2-1 2-2 4-1 1-1 1 0 2v1h0-4-1c-2 0-2 0-4 1h-2l-2-1c1-2 3-4 5-5l2-1z" class="Q"></path><path d="M336 457c0-2 0-2 1-3h2c2 0 3 0 4 1v1h-1c-2 0-2 0-4 1h-2z" class="N"></path><path d="M272 420c6-5 15-7 23-8h2l-5 1v1h-1 0c-1-1-3 0-4 1h0c-2 2-6 3-8 4h-3l-3 3v1h2l1-1h2l1-1c2 0 4-1 5-2s1-1 2-1h1c1-1 1-1 2-1h2 0c1-1 2-1 3-1h1 6c-1 1-5 1-7 1-1 0-2 0-4 1-5 1-9 4-13 5h-1c-1 1-2 1-4 1v-4z" class="Y"></path><path d="M325 434c1 0 1 1 2 0h1c2 0 3-2 5-1l1 2c1 0 1-1 2-1l2 1h0c2 2 3 3 4 5l-1 1c-1 0-1 0-1 1v2h0l-3-1h-1c-2 0-3-1-4-1-1-1-2-1-2-1h-1l1-1c2-2 0-2 0-4 0-1 1-1 1-2l-1 1h-5v-1z" class="D"></path><path d="M331 408c1 1 2 1 3 0h1l2 1 2 1 1-1 3 10c-1-1-2-1-3-2s-3-1-4-2v-1c-8-3-17-4-25-5 1-1 3-1 5-1l5 1h3c1 1 2 1 4 1l1 1c1-1 3 0 3-1l-1-2z" class="P"></path><path d="M331 408c1 1 2 1 3 0h1l2 1c1 2 1 2 1 4l-2-1c-2-1-5-1-7-1 1-1 3 0 3-1l-1-2z" class="B"></path><path d="M304 402h4l3 1h4c1 1 3 2 6 2v1c1 1 2 1 3 1 1 1 3 1 4 3-2 0-3 0-4-1h-3l-5-1c-2 0-4 0-5 1h-8v-1l-10 1-2-2-1 1c0-1 1-3 2-3h1v-1c2 0 5-1 6 0h1l1-1h3v-1z" class="AW"></path><path d="M291 407c5-1 9-1 14-1h0c-2 1-5 1-7 1 2 1 3 1 5 1l-10 1-2-2z" class="M"></path><path d="M311 403h4c1 1 3 2 6 2v1c1 1 2 1 3 1 1 1 3 1 4 3-2 0-3 0-4-1h-3l-5-1c-2 0-4 0-5 1h-8v-1c-2 0-3 0-5-1 2 0 5 0 7-1h0 2l1-1c2 1 4 1 6 1l1-1h-4v-2z" class="j"></path><path d="M305 406c3 0 6 0 8 1l3 1c-2 0-4 0-5 1h-8v-1c-2 0-3 0-5-1 2 0 5 0 7-1z" class="E"></path><path d="M280 426h1 0c2 0 4 0 7 1l17 3c1 0 4 1 5 0 1 0 3-1 4-1v1l-1 2v2h1v-2h1v1c1 1 1 2 2 2 2 0 3 1 5 2v1 2 1l-2 1h-1c-3-1-6-2-10-3l-3-1 2-2-8-3-20-7z" class="u"></path><path d="M308 436l4 1c-1 1-2 2-3 2l-3-1 2-2z" class="B"></path><path d="M317 435c2 0 3 1 5 2v1 2c-2-1-4-3-5-5z" class="I"></path><path d="M312 437c3 2 5 3 8 4h2l-2 1h-1c-3-1-6-2-10-3 1 0 2-1 3-2z" class="AL"></path><path d="M299 394l2-1c3 3 8 5 12 7h0 4 1v1l2-1 6 2c3 2 5 2 8 2l1-1-1-2c1 0 2-1 3-1l3 9-1 1-2-1-2-1h-1c-1 1-2 1-3 0l1 2c0 1-2 0-3 1l-1-1c-1-2-3-2-4-3-1 0-2 0-3-1v-1c-3 0-5-1-6-2h-4l-3-1c-1-1-2-1-3-1l-2-2-1 1-1-1c0-1 0-3-1-4l-1-1z" class="V"></path><path d="M315 403c4 0 11 2 15 5h1l1 2c0 1-2 0-3 1l-1-1c-1-2-3-2-4-3-1 0-2 0-3-1v-1c-3 0-5-1-6-2z" class="E"></path><path d="M337 400l3 9-1 1-2-1-2-1c-4-1-7-3-11-5-2 0-4-1-6-1-2-1-4-1-5-2h0 4 1v1l2-1 6 2c3 2 5 2 8 2l1-1-1-2c1 0 2-1 3-1z" class="D"></path><path d="M288 383h1v4l1 1 2-1c1 1 2 1 3 2 2 1 4 2 6 4l-2 1 1 1c1 1 1 3 1 4l1 1 1-1 2 2c1 0 2 0 3 1h-4v1h-3l-1 1h-1c-1-1-4 0-6 0v1h-1c-1 0-2 2-2 3l1-1 2 2 10-1v1 1h-1s-1 0-1 1l-6 1c-8 1-17 3-23 8h0l-2 1c-3 1-5 3-8 5 0-1-1-1-2-1l1-2h-4l2-2-1-1 1-3h0c2-1 2-2 3-3v1l3 1v-1l1-2 5-6c3-4 6-8 8-12l2-6h1 0c1 1 1 2 2 2l1 1c0 1 0 1 1 2h0c1-4 2-7 2-11z" class="e"></path><path d="M290 408l1-1 2 2c-2 0-4 1-5 1l-9 2c3-1 7-4 11-4z" class="j"></path><path d="M262 414v1l3 1-6 5-1-1 1-3h0c2-1 2-2 3-3z" class="R"></path><path d="M263 421c2-3 7-5 10-8l1 1-2 2h-1c0 1 0 1-1 2l2 2-2 1c-3 1-5 3-8 5 0-1-1-1-2-1l1-2c0-1 1-1 2-2z" class="r"></path><path d="M263 421l1 1c1 0 2-1 3-1l1-2s1 0 1 1h0c1 1 1 0 1 1-3 1-5 3-8 5 0-1-1-1-2-1l1-2c0-1 1-1 2-2z" class="AD"></path><path d="M288 383h1v4l1 1 2-1c1 1 2 1 3 2 2 1 4 2 6 4l-2 1 1 1c1 1 1 3 1 4l1 1 1-1 2 2c1 0 2 0 3 1h-4v1h-3l-1 1h-1c-1-1-4 0-6 0s-3 1-6 1c-2 0-7 3-10 5l5-7c2-3 3-6 4-9h0c1-4 2-7 2-11z" class="N"></path><path d="M287 405c5-3 11-3 17-3h0v1h-3l-1 1h-1c-1-1-4 0-6 0s-3 1-6 1z" class="X"></path><path d="M288 397c0-1 2-4 3-4h3 3l2 1 1 1c1 1 1 3 1 4l1 1 1-1 2 2c1 0 2 0 3 1h-4 0c-1-1-2-1-3-1h-1c0-5-2-3-3-5-1-1 0-1-1-2h-3c-2 2-3 2-5 3z" class="R"></path><path d="M288 383h1v4l1 1 2-1c1 1 2 1 3 2 2 1 4 2 6 4l-2 1-2-1h-3-3c-1 0-3 3-3 4-2 2-3 5-6 6 2-3 3-6 4-9h0c1-4 2-7 2-11z" class="D"></path><path d="M286 394h1c2 0 3-2 4-3 2-1 2-1 4 0 1 0 2 1 2 2h-3-3c-1 0-3 3-3 4-2 2-3 5-6 6 2-3 3-6 4-9h0z" class="U"></path><defs><linearGradient id="W" x1="750.827" y1="442.77" x2="742.756" y2="472.417" xlink:href="#B"><stop offset="0" stop-color="#230101"></stop><stop offset="1" stop-color="#520c0b"></stop></linearGradient></defs><path fill="url(#W)" d="M722 440c13 0 26-1 38 5l7 4c3 1 7 4 7 7 1-1 1-2 1-3l1-3v4c1 1 2 1 3 2 1 0 1 0 2 1-3 1-5 2-7 4l2 2-2 1h1c2 1 6 2 7 3-3 2-17 0-18 1l-12 2-17 6h0-1c-1 0-1 0-2 1l-1 1h-1c-1 0-1 0-2 1 1-3 8-5 10-7v-1c-3 1-7 4-10 6s-7 5-10 7v1c-5 4-9 9-13 13-2 2-3 3-4 5v1l-5-4v-1c0-1 0-2 1-3l2-2c1-2 2-3 1-5h0v1h-1c-2 1-3 3-3 5h-2c-1-1-1-2-2-3s-3-3-4-3c-2 0-2 0-3-1v1h-1v-3-4-2h1c-2-2-4-4-5-7h0v-4h1c1-2 1-4 1-6l-3-11c3-1 7-1 10-3h0 1c1-1 1-1 2-1v-1l1-1c0-1 2-1 3-2s1-1 2-1 1-1 1-1h1c2-1 4-1 6-1h0 2l1 1s1-1 2-1l1 1 3-1 7-1z"></path><path d="M776 450v4c1 1 2 1 3 2 1 0 1 0 2 1-3 1-5 2-7 4l-2 2v-1l2-6c1-1 1-2 1-3l1-3z" class="c"></path><path d="M758 453l1 1h4 0l7 4-1 5c-1 1-2 1-3 2l1-5c-1 0-1-1-1-2h0l-2-1c-2-2-4-2-7-2l1-2z" class="I"></path><defs><linearGradient id="X" x1="739.657" y1="446.467" x2="736.163" y2="457.978" xlink:href="#B"><stop offset="0" stop-color="#400705"></stop><stop offset="1" stop-color="#660c0d"></stop></linearGradient></defs><path fill="url(#X)" d="M723 452c2-2 3-3 6-4h3 0l-1 1c1 0 2 0 3-1 1 0 2 1 3 0h1c1-1 0-1 2-1h1l1 1c1 0 2 1 3 0l8 2c0 1-1 1-1 2 2 1 4 0 6 1l-1 2c-3-1-5 0-8 0s-4-1-7 1l-1 1h-1l-2-1-2 1h-2c-1 0-2 0-3 1h-1c-2-2-4-1-6-4v-2h-1z"></path><path d="M742 456c3-2 4-1 7-1s5-1 8 0c3 0 5 0 7 2l2 1h0c0 1 0 2 1 2l-1 5-12 2c-1 0-1 1-2 1h4c3-1 6-2 9-2v1h2c-2 0-4 0-6 1h-1-1-1c-2 0 0 0-2 1h-2-2c-1 0 0 0-1 1h1l-17 6h0-1c-1 0-1 0-2 1l-1 1h-1c-1 0-1 0-2 1 1-3 8-5 10-7v-1h1l5-5c0-1-1-1-2-1h-2l-1-2c-1 1-2 1-4 2h0c-1 0-1-1-2-1h-1v-3h-1-1c1-1 5-3 6-3 2 0 3 0 4-1h1l1-1z" class="P"></path><path d="M742 456c4 0 5-1 8 1h2 1c1 1 1 1 1 2h2c0 1 0 1-1 2l3 2h-1 0c-1 1-3 2-3 3h-1v1h-2c-1 1-3 1-4 1 2-1 3-3 5-4-2-2-1 0-2-1 0 0-1-1-2-1h-2c-1-1-2-1-3-1h-1c-2-1 0-1-2-1h0v-2h1v-1l1-1z" class="f"></path><path d="M741 457v1h-1v2h0c2 0 0 0 2 1h1c1 0 2 0 3 1h2c1 0 2 1 2 1 1 1 0-1 2 1-2 1-3 3-5 4-1 1-2 1-4 1l-1 1c-1 0-2 1-3 1l5-5c0-1-1-1-2-1h-2l-1-2c-1 1-2 1-4 2h0c-1 0-1-1-2-1h-1v-3h-1-1c1-1 5-3 6-3 2 0 3 0 4-1h1z" class="AA"></path><path d="M723 452h1v2c2 3 4 2 6 4h1c1-1 2-1 3-1h2l2-1 2 1c-1 1-2 1-4 1-1 0-5 2-6 3h1 1v3h1c1 0 1 1 2 1h0c2-1 3-1 4-2l1 2h2c1 0 2 0 2 1l-5 5h-1c-3 1-7 4-10 6s-7 5-10 7c-2-3-4-7-6-11-1-1-1-3-2-4 1-3 2-7 4-9 0-2 4-5 6-7 0 0 2-1 3-1z" class="z"></path><path d="M723 452h1v2c2 3 4 2 6 4h1c1-1 2-1 3-1h2l2-1 2 1c-1 1-2 1-4 1-1 0-5 2-6 3h1c-4 2-8 4-11 7-1 0-1 1-2 1h-1c0 1-1 1-2 1h-1c-1 1-2 2-2 3-1-1-1-3-2-4 1-3 2-7 4-9 0-2 4-5 6-7 0 0 2-1 3-1z" class="f"></path><path d="M714 460c0-2 4-5 6-7l1 2c0 1-2 2-3 4v5c1 0 1 0 2-1l1-1c0-1 2-2 3-2h1c1 0 2 0 4-1h0l-2 2h2 0c-2 1-2 1-4 1-3 1-6 4-9 3 0-1 0-3 1-4 0-2 0-2-1-3l-1 2h-1z" class="R"></path><path d="M714 460h1l1-2c1 1 1 1 1 3-1 1-1 3-1 4 3 1 6-2 9-3 2 0 2 0 4-1h0 1 1c-4 2-8 4-11 7-1 0-1 1-2 1h-1c0 1-1 1-2 1h-1c-1 1-2 2-2 3-1-1-1-3-2-4 1-3 2-7 4-9z" class="M"></path><defs><linearGradient id="Y" x1="696.329" y1="462.341" x2="704.738" y2="468.291" xlink:href="#B"><stop offset="0" stop-color="#2c0100"></stop><stop offset="1" stop-color="#560808"></stop></linearGradient></defs><path fill="url(#Y)" d="M700 442c2-1 4-1 6-1h0 2l1 1s1-1 2-1l1 1 3-1-6 21h0c0 2 0 4-1 5 1 1 1 2 2 2 1 1 1 3 2 4 2 4 4 8 6 11v1c-5 4-9 9-13 13-2 2-3 3-4 5v1l-5-4v-1c0-1 0-2 1-3l2-2c1-2 2-3 1-5h0v1h-1c-2 1-3 3-3 5h-2c-1-1-1-2-2-3s-3-3-4-3c-2 0-2 0-3-1v1h-1v-3-4-2h1c-2-2-4-4-5-7h0v-4h1c1-2 1-4 1-6l-3-11c3-1 7-1 10-3h0 1c1-1 1-1 2-1v-1l1-1c0-1 2-1 3-2s1-1 2-1 1-1 1-1h1z"></path><path d="M694 467h0l2-1-5 12c-1 2-2 4-2 7l-1 1c1 2 3 4 4 5l3 3 1 1h-2c-1-1-1-2-2-3s-3-3-4-3c-2 0-2 0-3-1v1h-1v-3-4c1 0 2 0 3-1l7-14z" class="c"></path><path d="M700 442c2-1 4-1 6-1h0 2l1 1s1-1 2-1l1 1-1 1c-6 3-9 10-11 15l-4 8-2 1h0l1-5c1 0 2-4 2-5l1-3c-1-2-1-3-2-5-1-1 0-2 0-3h-3c0-1 2-1 3-2s1-1 2-1 1-1 1-1h1z" class="r"></path><path d="M697 457v1 4c0-1 0 0 1-1 0-2 1-2 2-3l-4 8-2 1h0l1-5c1 0 2-4 2-5z" class="T"></path><path d="M705 444h0c-1 1-2 2-3 2-2 2-3 6-4 8-1-2-1-3-2-5-1-1 0-2 0-3h3c0-1 1-1 2-1l4-1z" class="AG"></path><path d="M700 442c2-1 4-1 6-1h0 2l1 1-4 2-4 1c-1 0-2 0-2 1h-3-3c0-1 2-1 3-2s1-1 2-1 1-1 1-1h1z" class="e"></path><path d="M712 442l3-1-6 21h0 0c-4 6-7 15-10 22-1 2-3 4-3 6-1 1-1 1-1 2v2l-3-3c1-8 4-15 7-22 2-7 8-15 13-21 0-2 1-3 1-5h-2l1-1z" class="E"></path><path d="M710 455c-1 3-1 6-4 8l-2 1c1-2 1-4 3-5 1-1 2-3 3-4z" class="AL"></path><path d="M712 448c0 2-1 5-2 7-1 1-2 3-3 4-2 1-2 3-3 5l-1 2h0l-1-1-1 2c-1 1-1 2-2 2 2-7 8-15 13-21z" class="V"></path><path d="M693 446h3c0 1-1 2 0 3 1 2 1 3 2 5l-1 3c0 1-1 5-2 5l-1 5-7 14c-1 1-2 1-3 1v-2h1c-2-2-4-4-5-7h0v-4h1c1-2 1-4 1-6l-3-11c3-1 7-1 10-3h0 1c1-1 1-1 2-1v-1l1-1z" class="Y"></path><path d="M693 446h3c0 1-1 2 0 3h-7 1c1-1 1-1 2-1v-1l1-1z" class="X"></path><path d="M685 459l2-1h1c0 3 1 4 4 5v3 1h0 0c1-1 1-2 1-2l1-1c0-1 1-1 1-2l-1 5-7 14c-1 1-2 1-3 1v-2h1c-2-2-4-4-5-7h0v-4h1c1-2 1-4 1-6h1l2-4z" class="Q"></path><path d="M685 459h2c0 3-1 6-1 8 0 1-1 1-1 2-1 1-1 1-1 2l1 1v2c1 2 0 2 0 4l1 1-1 1c-2-2-4-4-5-7h0v-4h1c1-2 1-4 1-6h1l2-4z" class="R"></path><path d="M695 494v-2c0-1 0-1 1-2 0-2 2-4 3-6 3-7 6-16 10-22h0c0 2 0 4-1 5 1 1 1 2 2 2 1 1 1 3 2 4 2 4 4 8 6 11v1c-5 4-9 9-13 13-2 2-3 3-4 5v1l-5-4v-1c0-1 0-2 1-3l2-2c1-2 2-3 1-5h0v1h-1c-2 1-3 3-3 5l-1-1z" class="t"></path><path d="M708 467c1 1 1 2 2 2 1 1 1 3 2 4 2 4 4 8 6 11v1c-1 0-1 0-1-1-5-3-8-12-9-17z" class="E"></path><path d="M617 200c0-1 1-1 2-1h0c1 0 1 1 2 0h4c0 1 1 2 1 2 1 0 1-1 2 1v2c1 0 2 1 2 2 0 2 0 3-1 4h-1 1c0 1 1 2 1 3h1l2-1h1c0 2-1 4 1 6 3 3 6 5 8 10l-1 1c2 3 2 7 3 10 2 15 0 31-1 45-1 4-2 9-2 12-2 11-3 23-6 33l-1 3h-1c-2 2-7 7-10 8h-1l-7 6-4 3c0-1 0-1-1-2l-6 3-3 3c-1 0-2 0-4 1-1 1-1 1-2 1h0l-5 1c3-3 6-6 9-8l3-3 4-4 8-8 8-11c0-1 0-1 1-2h0-1c-1 0-2 1-2 2v-2c0-4 4-7 6-10l-1-3-5 7-1 1-3 1c-1 0-1 0-2-1 0-1 1-2 1-3l1-1c-1-1-1-2-1-3 1-4 1-7 2-11v-5-5c-1-6 0-12-1-18-1-9-5-17-7-26l-1-5h-2c-1-3-1-6-2-9l-1-13c-1-2-2-2-4-3l8 2v-2-1c-2 0-5-1-7-2h-5-2l1-2-2-1c2-1 4-3 6-5 1 0 2 1 3 0 2 0 2-1 5-1l-1 1 1 2c0-1 1-1 1-1l2-2 2-2 3 2 2-1z" class="Y"></path><path d="M634 246v-4-1 1c1 1 1 2 2 3 2 4 3 7 3 10v1 1l-1-1c-1-2-1-3-1-4l-2-4-1-2z" class="m"></path><path d="M600 213l8 2h-1v1 1 12c1 3 1 6 2 9h-2c-1-3-1-6-2-9l-1-13c-1-2-2-2-4-3z" class="t"></path><path d="M601 210h6l1 1c1 1 3 1 5 2s4 2 6 2c1 1 1 0 1 0 3 2 8 3 10 6v1c-8-3-15-5-23-7h1v-2-1c-2 0-5-1-7-2z" class="AV"></path><path d="M628 210h0 1c0 1 1 2 1 3h1l2-1h1c0 2-1 4 1 6 3 3 6 5 8 10l-1 1c-3-2-5-3-8-5-2-1-3-1-4-2v-1c-2-3-7-4-10-6 3-1 4 0 6 1 1 0 2 0 3 1l1-1c-1-1-1-3-2-4l-3-1v-1h3z" class="V"></path><path d="M628 210h0 1c0 1 1 2 1 3h1c1 2 1 4 1 7l3 3h1l-1 1h-1 0c-2-1-3-1-4-2v-1c-2-3-7-4-10-6 3-1 4 0 6 1 1 0 2 0 3 1l1-1c-1-1-1-3-2-4l-3-1v-1h3z" class="f"></path><path d="M636 289c0-3 2-7 4-10 0 2-2 3-1 5 1-1 1 0 1-1l1 1h-1c-1 2-1 4-1 7 0 1-1 3-1 5l1-1 1-1v2c-1 1-1 2-3 3-1 2-1 3-2 4 0 1-1 3-2 3-3 6-6 11-10 16 0-1 0-1 1-2h0-1c-1 0-2 1-2 2v-2c0-4 4-7 6-10 1-1 2-2 2-4 1-2 1-3 0-5 0-1 2-4 3-5 1-2 2-5 4-7z" class="I"></path><path d="M636 289h1c0 3-1 4-2 6v1c-1 2-3 5-4 8 0 1-1 1-2 2 1-2 1-3 0-5 0-1 2-4 3-5 1-2 2-5 4-7z" class="E"></path><path d="M621 320c2-3 4-5 6-7s3-6 6-7c-3 6-6 11-10 16 0-1 0-1 1-2h0-1c-1 0-2 1-2 2v-2z" class="X"></path><path d="M618 338h2l1-1 1-1c0-1 1-1 2-2h1v-1c2-1 4-5 7-4l1 1c1 0 2-1 3-1l-1 3h-1c-2 2-7 7-10 8h-1l-7 6-4 3c0-1 0-1-1-2l-6 3-3 3c-1 0-2 0-4 1-1 1-1 1-2 1h0l-5 1c3-3 6-6 9-8l3-3 4-4 8-8h0c-1 2-2 3-3 5v1c1 0 0 0 1-1 2-1 3-2 5-3l1 2c0 1 0 1-1 1z" class="C"></path><path d="M611 347c1-1 2-3 4-3 2-1 4-3 6-4h2l-7 6-4 3c0-1 0-1-1-2z" class="j"></path><path d="M618 338h2l1-1 1-1c0-1 1-1 2-2h1v-1c2-1 4-5 7-4l1 1c1 0 2-1 3-1l-1 3h-1l-1-1c-2 1-3 2-4 3s-2 1-2 2c-2 0-2 1-3 1h-1c-1 0-4 2-5 3v-2z" class="D"></path><path d="M607 341l-3 8-2 2c1 0 2 0 2-1h1l-3 3c-1 0-2 0-4 1-1 1-1 1-2 1h0l-5 1c3-3 6-6 9-8l3-3 4-4z" class="P"></path><path d="M600 348l3-3c0 1-1 4-2 5-1 2-3 3-5 5h0l-5 1c3-3 6-6 9-8z" class="B"></path><path d="M617 200c0-1 1-1 2-1h0c1 0 1 1 2 0h4c0 1 1 2 1 2 1 0 1-1 2 1v2c1 0 2 1 2 2 0 2 0 3-1 4h-1 0-3v1l3 1c1 1 1 3 2 4l-1 1c-1-1-2-1-3-1-2-1-3-2-6-1 0 0 0 1-1 0-2 0-4-1-6-2s-4-1-5-2l-1-1h-6-5-2l1-2-2-1c2-1 4-3 6-5 1 0 2 1 3 0 2 0 2-1 5-1l-1 1 1 2c0-1 1-1 1-1l2-2 2-2 3 2 2-1z" class="AH"></path><path d="M618 209c1 0 3 0 4 1h-1l-1 1v2c-1 0-3 0-4-1v-1c0-1 1-1 2-2z" class="X"></path><path d="M617 200c1 0 1 0 1 1h3c0 1 0 2-1 4h2c-2 1-3 1-5 1l1-2-2-2c-1 0-1 0-2 1h-1v-1l-3-1 2-2 3 2 2-1z" class="F"></path><path d="M607 210l1-1c1-1 5 0 6 0h4c-1 1-2 1-2 2v1c1 1 3 1 4 1 2 0 3 0 4 1 2 0 2 0 3 1l-1 1c-2-1-3-2-6-1 0 0 0 1-1 0-2 0-4-1-6-2s-4-1-5-2l-1-1z" class="e"></path><path d="M617 200c0-1 1-1 2-1h0c1 0 1 1 2 0h4c0 1 1 2 1 2 1 0 1-1 2 1v2c1 0 2 1 2 2 0 2 0 3-1 4h-1 0-3v1l3 1c1 1 1 3 2 4l-1 1c-1-1-2-1-3-1l1-1h1l-1-1c-1 0-2-1-3-1l-1-1c-1 0-1 0-2-1 1-1 2-1 4-2h-2l-1-1h0c1 0 2 0 3-1l-1-1-2-1h-2c1-2 1-3 1-4h-3c0-1 0-1-1-1z" class="M"></path><path d="M625 199c0 1 1 2 1 2 1 0 1-1 2 1v2c1 0 2 1 2 2 0 2 0 3-1 4h-1 0v-2l-1-1v-2c-1 0-1-1-2-1h-1l1-2c-1 0 0 0-1-1h1v-2z" class="N"></path><path d="M599 202c1 0 2 1 3 0 2 0 2-1 5-1l-1 1 1 2c0-1 1-1 1-1h3c0 1 0 1-1 2l-1 1v1c1 0 2-1 3-2h2v1h-1l-1 1h2 3 0l-3 2c-1 0-5-1-6 0l-1 1h-6-5-2l1-2-2-1c2-1 4-3 6-5z" class="H"></path><path d="M599 202c1 0 2 1 3 0 2 0 2-1 5-1l-1 1c-4 3-7 5-10 7v1h-2l1-2-2-1c2-1 4-3 6-5z" class="AF"></path><defs><linearGradient id="Z" x1="640.334" y1="259.334" x2="602.529" y2="279.377" xlink:href="#B"><stop offset="0" stop-color="#450705"></stop><stop offset="1" stop-color="#a21416"></stop></linearGradient></defs><path fill="url(#Z)" d="M610 243l1-1v-1-1l1 1c1-2 1-4 1-5h3c1-1 1-2 1-3l1-1c1 1 2 2 3 2 2 0 4 0 6 1 0 1 1 2 2 3 0 0 0 1 1 1 0 2 2 3 3 5v1l1 1 1 2 2 4c0 1 0 2 1 4l1 1c1 10 0 18-4 28l-2 4-2 6 1 1c-1 1-3 4-3 5 1 2 1 3 0 5 0 2-1 3-2 4l-1-3-5 7-1 1-3 1c-1 0-1 0-2-1 0-1 1-2 1-3l1-1c-1-1-1-2-1-3 1-4 1-7 2-11v-5-5c-1-6 0-12-1-18-1-9-5-17-7-26z"></path><path d="M628 296v-1h1v-1-1-5s-1-1-1-2l1-1c0-1 1-2 1-3v-2c0-1 1-1 2-2v1c0 1 1 3 0 4v1c0 1 1 1 2 2v-1h1l-2 4c-1 2-2 3-3 5 0 1-1 2-2 3v-1z" class="U"></path><path d="M618 287v1c1 1 1 2 1 3v1c1 3 0 5 1 8 0 2 0 3-1 4v6-1c1-1 1-2 2-3 1 0 1 0 1-1l2-3 1-3v-4c1 1 1 2 1 3v-1c1 0 1 0 2-1v1c1-1 2-2 2-3 1-2 2-3 3-5l-2 6 1 1c-1 1-3 4-3 5 1 2 1 3 0 5 0 2-1 3-2 4l-1-3-5 7-1 1-3 1c-1 0-1 0-2-1 0-1 1-2 1-3l1-1c-1-1-1-2-1-3 1-4 1-7 2-11v-5-5z" class="AB"></path><path d="M628 297c1-1 2-2 2-3 1-2 2-3 3-5l-2 6 1 1c-1 1-3 4-3 5 1 2 1 3 0 5 0 2-1 3-2 4l-1-3-5 7-1-1c0-3 3-7 5-10 1-1 1-2 2-4 0-1 0-1 1-2z" class="a"></path><path d="M629 301c1 2 1 3 0 5 0 2-1 3-2 4l-1-3 3-6z" class="q"></path><path d="M779 744h0l1-1 2-3v1h1l1 1c1 0 1-1 2-2l1-1v1l-2 5-2 3c-2 4-5 7-8 11l-3 3c-3 3-5 6-8 8-2 2-2 5-2 8 3 2 6 3 10 2h1c2-1 4-3 5-4h1c-1 1-4 5-4 5l1 1h0c-1 1-1 1-2 1h-1c-2 1-5 1-7 0h-1c-3-1-5-3-7-3-1 0-1 1-2 0l-6 3-1 1c-4 1-7 3-9 6h-1c-1 0-1 0-2 1s-2 2-4 3l-4 3c-5 3-9 9-15 12-16 9-31 19-48 23-9 2-18 3-27 4-7 0-15 1-22 0h-1-1l-1 1h-1l1-2-2-1c0 1 0 2-1 3v-7c-1-4-2-7-4-10l1-1c0-2-2-3-3-5-1-1-2-3-3-4v-1-2l-2-2 1-1c-4-4-7-9-10-14l4-1c4 5 11 9 17 12 5 4 12 7 18 9l2-2c13 6 28 5 41 0 1 0 3 0 4-1h4c1 0 2-1 3-1v-1h-2c1-1 1-1 1-2l-2-1-2 2v-2c2 0 3-2 4-3 4-4 7-7 10-11 1-3 2-5 4-7l-4-1v-2c2 1 4 1 6 0h1c2 0 4 1 6 2 5 1 10 1 15 1h1c4-1 9-2 13-3 13-4 24-11 34-21 3-4 6-9 9-12l1-1z" class="AQ"></path><path d="M626 824c3-1 4-2 6-1s7 1 8 0l8 1h-5c-1 1-2 1-2 1h1-5-3l-2 1c-5-1-9-1-14 2h0c2-3 5-4 8-4z" class="AZ"></path><path d="M611 815c6 2 9 3 14 0 2 0 4-2 6-4-2 3-4 5-7 8l7 3h-1c-2 0-3-1-5 0h-4l1-2c-3 0-4-1-6-2s-3-1-5-3h0z" class="Ae"></path><path d="M612 819c3 0 4 2 6 4 3 0 6 0 8 1-3 0-6 1-8 4h0c-2 3-3 5-3 8l-1 1h-1l1-2v-5c0-2 1-6 0-8l-2-3z" class="w"></path><path d="M673 821l8-3c3 0 5-2 8-2-2 2-4 3-6 4-3 2-4 3-7 3-3 1-7 2-10 3-2 1-4 0-7 0 5-2 10-2 14-5z" class="s"></path><path d="M673 808c1 0 3 0 4-1h4c-13 5-26 7-40 5-4 0-7-1-10-2h-1l2-2c13 6 28 5 41 0z" class="Ac"></path><path d="M600 805l1-1c2 3 5 6 8 9 1 0 2 1 2 2h0c2 2 3 2 5 3s3 2 6 2l-1 2h4c2-1 3 0 5 0h1l9 1c-1 1-6 1-8 0s-3 0-6 1c-2-1-5-1-8-1-2-2-3-4-6-4l-3-3v-1l-4-4v-1l-3-2v-1l-2-2z" class="Ai"></path><path d="M673 821c-4 3-9 3-14 5-8 1-18 0-27 0l2-1h3 5-1s1 0 2-1h5c0-1 1-1 2-1 8 0 15 0 23-2z" class="w"></path><path d="M602 807v1l3 2v1l4 4v1l3 3 2 3c1 2 0 6 0 8v5l-2-1c0 1 0 2-1 3v-7c-1-4-2-7-4-10l1-1c0-2-2-3-3-5-1-1-2-3-3-4v-1-2z" class="AU"></path><path d="M609 816l3 3 2 3c1 2 0 6 0 8v5l-2-1c1-3 1-8 0-11l-3-7h0z" class="x"></path><path d="M616 836l1-1h1 1 12c2 0 5 0 7-1-2-2-7 1-11-1 1-1 8 0 10-1h10c5 0 11-1 16-2 18-4 35-12 51-22v1c-16 9-31 19-48 23-9 2-18 3-27 4-7 0-15 1-22 0h-1z" class="w"></path><path d="M698 793l1 1 29-10 1 1-6 3-9 6c-7 3-13 5-20 8-3 2-7 3-10 4v-1h-2c1-1 1-1 1-2l-2-1 9-6c2-1 3-3 5-3h3z" class="y"></path><path d="M693 778c2 1 4 1 6 0h1c2 0 4 1 6 2 5 1 10 1 15 1h1c4-1 9-2 13-3 13-4 24-11 34-21 3-4 6-9 9-12l1-1c-1 3-2 4-3 6-5 6-10 13-17 18s-16 9-25 12c-10 3-20 3-30 8-2 1-4 2-5 3 0 1 0 2-1 2h-3c-2 0-3 2-5 3l-9 6-2 2v-2c2 0 3-2 4-3 4-4 7-7 10-11 1-3 2-5 4-7l-4-1v-2z" class="Ai"></path><path d="M698 788c2 0 4-1 6-1h-2c-1 1-3 2-4 3v1h1c0 1 0 2-1 2h-3c-2 0-3 2-5 3 3-3 5-6 8-8z" class="w"></path><path d="M697 781l17 2c-4 1-13 2-16 5h0c-3 2-5 5-8 8l-9 6-2 2v-2c2 0 3-2 4-3 4-4 7-7 10-11 1-3 2-5 4-7z" class="Aa"></path><defs><linearGradient id="a" x1="378.232" y1="245.871" x2="421.87" y2="271.136" xlink:href="#B"><stop offset="0" stop-color="#1c0000"></stop><stop offset="1" stop-color="#6b0a0a"></stop></linearGradient></defs><path fill="url(#a)" d="M421 214h5s0 2-1 2h0l-1 3v12 3c0 3 0 9 1 11l1-3h1c-2 7-4 15-3 22h0c1 0 1-1 1-2h1v4 1h-1 2c1 3 2 5 3 8 0 7 1 14 2 21l1 2v1c-1 2-1 2 0 4h-1s-1 1-1 2c-1 1 0 1-1 2-1 3-4 5-5 8l3 10v1l1 2 2 2c1 1 2 2 3 2 2 1 3 2 4 3l9 7c0 1 0 2 1 3l-1 1 1 1c-1 2-1 2-2 3l-3 2v1c1 2 1 2 1 4-2-2-3-4-5-6-1-1-1-3-2-5l-1-1h0-2c-1 0-1-1-1-2-1-1-1-1-2-1l-1 1-3-3-6-9v-1l-3-5-1 1c1 3 1 5 3 7l-2 2-5-6-6-9h-1c-1-2-3-4-5-6-3-4-6-7-7-12h-1v1c0 1 0 0-1 1v-1c-1-3-1-5-3-6-2-5-3-10-4-15-3-13-5-26-3-38 1-5 2-8 5-11 1-1 1-1 1-2l-2-2c3-1 4-2 6-4l2-1 1-1 9-4 7-2c2-1 4-1 6-2l4-1z"></path><path d="M410 240c1-1 2-2 3-2l4-2c0 1 1 1 1 2v3-2l1 1c1 1 0 2 0 4 0 0-1 1-1 2v2 3 3c-1 2 0 3-1 6l-1 2v-4l1-1v-13c0-2 0-4-1-5l-2-1-1 1c-1 0-2 1-3 1z" class="S"></path><path d="M411 217c2-1 4-1 6-2 1 1 2 1 3 2l-4 1-1 1c3 3 4 6 5 10 1 2 1 5 3 7v8l-2 18h-1v-5c-1-1 0-3 0-4 1-2 0-3 0-5l1-1v-3l1-2c-2-2-1-5-1-7v-1c0-1 0-1-1-2 0-1 0-2-1-3v-2c-1-1-1-3-2-5-2-1-2-2-3-3l2-1-2-1h-3z" class="v"></path><path d="M394 302h1l2 3c1 2 2 3 4 5 0-4-2-7-2-10v-1c-3-4-4-8-6-11l1-2c1 4 4 8 6 12-1 3 2 7 4 10 0 1 1 1 1 3v1h1l1 1c-1 1-1 1 0 2 0 2 1 3 2 5v2l-2-2h-1c-1-2-3-4-5-6-3-4-6-7-7-12z" class="N"></path><path d="M416 263v-5 4 1 3c0 2-1 4-1 6 1-1 1-1 1-2 1-2 2-3 3-4v-1c0-1 0 0 1-2v-1h1l-4 14c-2 4-3 8-4 12-2 3-3 7-6 10v-1-1l3-3 1-3h0c0-1-1-2-1-2l-1 1 1 1c-1 1-1 2-1 3-2 0-2 1-4 2l1-2c0-1 0-1 1-2 2-3 4-8 4-11 3-6 4-12 5-17z" class="Q"></path><path d="M421 214h5s0 2-1 2h0l-1 3v12 3c-2 3-1 7-1 10v-8c-2-2-2-5-3-7-1-4-2-7-5-10l1-1 4-1c-1-1-2-1-3-2l4-1z" class="C"></path><path d="M423 217l2-1-1 3v12 3c-2 3-1 7-1 10v-8c0-7-1-13 0-19z" class="z"></path><path d="M421 214h5s0 2-1 2h0l-2 1c-1 0-2 0-3 1h1c1 3 1 6 1 9h-1c-1-3-3-5-3-8l-2-1 4-1c-1-1-2-1-3-2l4-1z" class="I"></path><path d="M410 240c1 0 2-1 3-1l1-1 2 1c1 1 1 3 1 5v13c-1-2 0-5-1-7v-6l-1-1h0c-1 2-2 3-4 5l-1-1c-1 0-2 1-3 2-2 2-6 19-5 22v1 3c1 2 1 2 1 4h-1c-2-3 0-5-2-7v-4c0-1 1-2 1-3v-1c1-1 0-1 0-2l1-1v-2c0-1 0-2 1-2 0-2 1-4 1-5v-1c0-1 0-1 1-2h0c1-1 0-2 1-3 1-2 3-4 4-6z" class="Q"></path><path d="M416 244v6c1 2 0 5 1 7l-1 1v5c-1 5-2 11-5 17 0-1 0-1 1-2v-1h-1c0 1-1 1-1 2-2-3-1-11-1-14v-1h-1v-1c1-1 1-2 1-3 1-2 0-4 0-5 1-2 1-2 1-3h0c1-2 2-2 4-3h0c1-2 1-1 1-2 1-1 1-2 1-3z" class="N"></path><path d="M416 244v6c1 2 0 5 1 7l-1 1v5c-1-3-1-5-1-7-1-2-1-3-1-5v-1l-2 1h-1c-2 3-1 10-2 13h-1v-1c1-1 1-2 1-3 1-2 0-4 0-5 1-2 1-2 1-3h0c1-2 2-2 4-3h0c1-2 1-1 1-2 1-1 1-2 1-3z" class="f"></path><path d="M424 234c0 3 0 9 1 11l1-3h1c-2 7-4 15-3 22h0c1 0 1-1 1-2h1v4 1l-3-1c-2 3-2 7-2 11l-1 12c-1 2-1 4-1 7-1-1 0-3 0-4l-1-1c0 2 0 4 1 6v2-1 1 1l-1 1v-1c0-2 0-3-1-4-1-2-1-5-1-8 2-3 1-8 1-12l4-14 2-18c0-3-1-7 1-10z" class="c"></path><path d="M407 298c3-3 4-7 6-10 1-4 2-8 4-12 0 4 1 9-1 12 0 3 0 6 1 8 1 1 1 2 1 4v1 5c0 1 0 1 1 2h0v2c1 1 1 3 2 4v1 7c1 1 1 3 1 4h-1l-3-8v-1c-3-4-3-8-5-12-1-1-2-2-2-3-1-1 0-2 0-2v-1l-1-1-4 4c0-1 1-3 1-4z" class="j"></path><path d="M414 302v-1l2-4c1 2 1 3 1 6l1 1c0 1-1 3 0 4 0 1 0 1 1 1 0 1 0 1-1 2v2c-1-2-1-6-2-9-1-1-1-2-2-2z" class="AR"></path><path d="M410 298l1-1h1c1 2 1 4 2 5 1 0 1 1 2 2 1 3 1 7 2 9v5-1c-3-4-3-8-5-12-1-1-2-2-2-3-1-1 0-2 0-2v-1l-1-1z" class="B"></path><path d="M394 286l-1-1-1-2 1-1c1 2 1 3 2 5s1 3 2 4 1 2 1 3l1 1c1 1 1 2 2 3s1 3 3 5l1-1h-1v-2-1c-2-2-3-4-4-6-1-3-3-6-4-9-1-2-2-3-2-5v-1c-1-1-1-1-1-2s-1-2 0-2c0 1 1 2 1 3h0c1 1 1 1 1 2l2 2v2c0 2 3 5 4 7s1 4 2 6c0 1 2 2 3 3l1-2v1c0 1-1 3-1 4l4-4 1 1v1s-1 1 0 2c0 1 1 2 2 3 2 4 2 8 5 12v1l3 8c0 1 1 2 1 3l-1 1-3-5-1 1c1 3 1 5 3 7l-2 2-5-6-6-9 2 2v-2c-1-2-2-3-2-5-1-1-1-1 0-2l-1-1h-1v-1c0-2-1-2-1-3-2-3-5-7-4-10-2-4-5-8-6-12z" class="D"></path><path d="M400 298c3 6 7 12 11 18l5 6c0 1 1 2 2 3l-1 1c1 3 1 5 3 7l-2 2-5-6-6-9 2 2v-2c-1-2-2-3-2-5-1-1-1-1 0-2l-1-1h-1v-1c0-2-1-2-1-3-2-3-5-7-4-10z" class="AH"></path><path d="M407 315c5 4 7 8 9 14-3-2-6-4-7-7v-2c-1-2-2-3-2-5z" class="F"></path><path d="M418 301l1-1v-1-1 1-2c-1-2-1-4-1-6l1 1c0 1-1 3 0 4 0-3 0-5 1-7 0 7 0 14 1 22 1 1 2 1 2 2h0c0-1 0-1 1-2l1 4 3 10v1l1 2 2 2c1 1 2 2 3 2 2 1 3 2 4 3l9 7c0 1 0 2 1 3l-1 1 1 1c-1 2-1 2-2 3l-3 2v1c1 2 1 2 1 4-2-2-3-4-5-6-1-1-1-3-2-5l-1-1h0-2c-1 0-1-1-1-2-1-1-1-1-2-1l-1 1-3-3-6-9v-1l1-1c0-1-1-2-1-3h1c0-1 0-3-1-4v-7-1c-1-1-1-3-2-4v-2h0c-1-1-1-1-1-2v-5z" class="T"></path><path d="M426 326l2-1v1l1 2c1 2 2 4 4 5v1c-1 1-1 1-1 2l-1 1c-2-4-4-8-5-11z" class="S"></path><path d="M421 311c1 1 2 1 2 2h0c0-1 0-1 1-2l1 4 3 10-2 1c-2-5-4-10-5-15z" class="D"></path><path d="M433 333c2 1 7 4 7 6h-4-1v1 2c1 1 3 3 2 4l-1-1h0c-1-3-4-6-5-8l1-1c0-1 0-1 1-2v-1z" class="N"></path><path d="M421 326h1l1 4c1 2 2 3 4 4 1 3 3 5 4 8l-1 1-3-3-6-9v-1l1-1c0-1-1-2-1-3z" class="M"></path><path d="M429 328l2 2c1 1 2 2 3 2 2 1 3 2 4 3l9 7c0 1 0 2 1 3l-1 1c0-1-2-3-3-3-4-2-5-1-8-4h4c0-2-5-5-7-6s-3-3-4-5z" class="B"></path><path d="M437 346c1-1-1-3-2-4v-2-1h1c3 3 4 2 8 4 1 0 3 2 3 3l1 1c-1 2-1 2-2 3l-3 2v1c1 2 1 2 1 4-2-2-3-4-5-6-1-1-1-3-2-5z" class="j"></path><path d="M439 351c1-1 1-1 1-2 2-1 3-3 4-5l4 3c-1 2-1 2-2 3l-3 2v1c1 2 1 2 1 4-2-2-3-4-5-6z" class="l"></path><path d="M421 277c0-4 0-8 2-11l3 1h-1 2c1 3 2 5 3 8 0 7 1 14 2 21l1 2v1c-1 2-1 2 0 4h-1s-1 1-1 2c-1 1 0 1-1 2-1 3-4 5-5 8l-1-4c-1 1-1 1-1 2h0c0-1-1-1-2-2-1-8-1-15-1-22l1-12z" class="Q"></path><path d="M421 277c0-4 0-8 2-11l3 1h-1l-1 1 1 5v7c-1 0-1-1-1-2l-2 2v-1l1-1v-1c-1-2-1-4-1-6 0 2 0 3-1 5v1z" class="B"></path><path d="M424 278c-1-2-1-5 0-8v1l1 2v7c-1 0-1-1-1-2z" class="V"></path><path d="M432 296l1 2v1c-1 2-1 2 0 4h-1s-1 1-1 2c-1 1 0 1-1 2-1 3-4 5-5 8l-1-4v-1c-1-2 0-5 0-7h1 1l2-3h2v1l2-5z" class="f"></path><path d="M424 310c0-2 0-3 1-4h2l2-2 1-1h0l1 2c-1 1 0 1-1 2-1 3-4 5-5 8l-1-4v-1z" class="R"></path><path d="M421 277v-1c1-2 1-3 1-5 0 2 0 4 1 6v1l-1 1v1c1 2 1 3 1 5s0 10 2 12h0v2l2 1c-1 1-1 1-1 2h-2v1c0 2-1 5 0 7v1c-1 1-1 1-1 2h0c0-1-1-1-2-2-1-8-1-15-1-22l1-12z" class="a"></path><defs><linearGradient id="b" x1="645.16" y1="600.104" x2="681.5" y2="642.113" xlink:href="#B"><stop offset="0" stop-color="#170101"></stop><stop offset="1" stop-color="#3c0403"></stop></linearGradient></defs><path fill="url(#b)" d="M687 559c1 1 3 1 4 2l1 1h1 2v-1c1 2 2 3 4 5 0 1 0 2-1 3l1 1 4 2 4 4c2 2 2 6 3 9s3 6 4 9l1-1c3 4 5 7 6 12 1 1 1 3 2 4h2c2-1 4-3 6-4-1 4-2 6-4 9l-3 6c-1 0-2 1-2 1-2 0-2 0-3 1v4c0 3 1 8-1 12-1 3-3 8-6 10l-4 2c1-2 2-3 2-5 0 0 0-1 1-2l1-1c0-4 0-7-2-12l-1-2c-4-4-7-6-12-7h-8l-3 1-14 3-6 2 2 1-5 2-3 3-1-2h-1l1 4h-2l2 17v1 5l-1 1c-3-1-6-2-9-1s-7 2-10 4h0c4-3 8-4 13-6v-5c-1 0-2 0-3-1 0 0-1-1-2-1l-1 1c-2 0-4 1-6 2-1 1-3 2-4 3-1 0-2 1-3 2s-2 3-3 4v2l-1 1c-2 3-6 9-9 11 2-3 5-8 6-12 2-4 2-12 2-17 0-1-1-2-2-4-2-1-4-1-7-1v-1c-1-1-1-2-2-3h-4-1l1-1h-2v-1l8-11 5-6 7-6-1-1 1-3c3-1 6-4 8-6 4-3 7-6 11-10l2-2v-2l2-2c2-1 4-3 5-3l2 1 6-5 5-4h0l5-5h1l3-7 2-3h1l3-1z"></path><path d="M633 615l1 1h0c1 1 1 0 1 1h-4c1-2 1-2 2-2z" class="AK"></path><path d="M636 641h0 2c1 0 1-1 2-1 2 1 4 2 5 4l-1 1v-1c-2-2-3-1-5-1-1 0-1-1-2-1v2h0 1 0c-1 1-3 2-3 4 0-1 1 0 2 0h0c-1 0-2 1-3 0h0c1-3 2-4 2-7h0z" class="v"></path><path d="M638 610l3 1v1h-1c-2 0-3 1-4 2l1 1v1h3v1h1v1c-2 0-4-1-6-1 0-1 0 0-1-1h0l-1-1 5-5z" class="Y"></path><path d="M652 604c8-2 14-4 22-3l-8 1c1 1 3 0 5 1h3c-6 0-12 0-17 1-2 0-3 1-5 0z" class="U"></path><path d="M678 586l4-1h7 4c1 0 3 2 4 3h-1l-2-1c-1-1-1-1-2-1-4 0-8 2-12 3h-4c-1 1-2 2-5 2 2-2 3-3 5-3l2-2z" class="Y"></path><path d="M676 588c3 1 7 0 10-1 2-1 3-1 6-1-4 0-8 2-12 3h-4c-1 1-2 2-5 2 2-2 3-3 5-3z" class="m"></path><path d="M662 600l-1-1c0-1-1-1-1-2 1-3 6-6 8-8 2-1 3-2 5-2s2 0 4-1h1l-2 2c-2 0-3 1-5 3h0-1c-1 1-1 2-2 3v1l-6 3v1 1z" class="AK"></path><path d="M613 636c3-1 9-1 12 2 3 1 4 4 5 6l-1 6h-1v-4c0-1-1-2-2-4-2-1-4-1-7-1v-1c-1-1-1-2-2-3h-4-1l1-1z" class="S"></path><path d="M617 637c4 0 6 1 8 4l1 1c-2-1-4-1-7-1v-1c-1-1-1-2-2-3z" class="AY"></path><path d="M671 591c3 0 4-1 5-2l1 1h1 0c4 0 9-1 13 0l3 1h1 2l1 2c-1-1-3-1-5-1-5-2-9-1-14-1-1 0-3 0-4 1v5c1 1 4 1 6 2h2 1v1l-8 1c1-1 2-1 2-1l1-1c-3-1-7 0-10 0-2 1-5 1-7 1v-1-1l6-3v-1c1-1 1-2 2-3h1 0z" class="I"></path><path d="M662 600v-1-1l6-3v-1c1-1 1-2 2-3h1l-1 1 1 1c-2 3-5 4-8 5l6 1c-2 1-5 1-7 1z" class="v"></path><path d="M667 579h3c1 1 0 2 1 3-15 8-28 19-40 30l-1-1 1-3c3-1 6-4 8-6 4-3 7-6 11-10l2-2v-2l2-2c2-1 4-3 5-3l2 1 6-5z" class="M"></path><path d="M654 586c2-1 4-3 5-3l2 1-9 6v-2l2-2z" class="B"></path><path d="M672 575l5-5h1l-2 3h1l8-3h0c3 0 4 0 6 2 2 0 2 0 3 1s2 2 2 3c6 4 9 8 13 14 1 4 3 8 4 12 1 3 2 6 2 9-3-9-5-17-11-25-1-2-4-5-6-7-7-4-14-2-21 1l-6 2c-1-1 0-2-1-3h-3l5-4h0z" class="V"></path><path d="M672 575c1 3 2 3 5 5l-6 2c-1-1 0-2-1-3h-3l5-4z" class="R"></path><path d="M676 573h1l8-3h0c3 0 4 0 6 2 2 0 2 0 3 1s2 2 2 3l-2-1v-1c-3-1-10-1-13 0h-1-5l1-1z" class="Q"></path><path d="M687 559c1 1 3 1 4 2l1 1h1 2v-1c1 2 2 3 4 5 0 1 0 2-1 3l1 1c5 5 8 11 10 18v2c-4-6-7-10-13-14 0-1-1-2-2-3s-1-1-3-1c-2-2-3-2-6-2h0c-3 1-5 2-8 3h-1l2-3 3-7 2-3h1l3-1z" class="v"></path><path d="M687 559c1 1 3 1 4 2l1 1h1 2v-1c1 2 2 3 4 5 0 1 0 2-1 3-4-4-8-7-14-9l3-1z" class="r"></path><path d="M684 600v-1h-1-2c-2-1-5-1-6-2v-5c1-1 3-1 4-1 5 0 9-1 14 1 2 0 4 0 5 1v-1c2 1 3 3 4 4 2 1 3 4 5 5 2 2 2 5 3 7s1 2 1 3c1 2 2 3 3 5 1 1 1 3 1 4-2-1-3-5-5-7-1-1-2-2-3-4-3-3-5-5-9-6-1 0-2-1-4-2-2 0-4-1-6 0l-3-1h-1z" class="C"></path><path d="M693 592c2 0 4 0 5 1v-1c2 1 3 3 4 4 2 1 3 4 5 5 2 2 2 5 3 7s1 2 1 3c-2-2-3-4-4-6-2-2-4-3-6-5l1-1c-1-1-1-2-2-4-1 0-2 0-3-1h-1c-1-1-2-1-3-2z" class="S"></path><path d="M699 570l4 2 4 4c2 2 2 6 3 9s3 6 4 9l1-1c3 4 5 7 6 12 1 1 1 3 2 4h2c2-1 4-3 6-4-1 4-2 6-4 9l-3 6c-1 0-2 1-2 1-2 0-2 0-3 1v4c0-2 0-4-1-5 0 4 0 9-1 14-1-4-1-7-2-11h0c0-2 1-2 0-4 0-1 0-3-1-4l1-2c0-1-1-2 0-3 0-3-1-6-2-9-1-4-3-8-4-12v-2c-2-7-5-13-10-18z" class="H"></path><path d="M714 594l1-1c3 4 5 7 6 12v1h-2 0c-1-2-1-4-2-5s0-1-1-2h0l-1-1v-2c-1-1-1-1-1-2z" class="AD"></path><path d="M721 605c1 1 1 3 2 4h2c2-1 4-3 6-4-1 4-2 6-4 9-1 1-2 1-3 2l-3 4c-1-5-1-9-2-14h0 2v-1z" class="AS"></path><path d="M709 588h0c2 4 3 7 5 10 3 7 5 15 4 23 0 4 0 9-1 14-1-4-1-7-2-11h0c0-2 1-2 0-4 0-1 0-3-1-4l1-2c0-1-1-2 0-3 0-3-1-6-2-9-1-4-3-8-4-12v-2z" class="m"></path><path d="M684 600h1l3 1c2-1 4 0 6 0 2 1 3 2 4 2 4 1 6 3 9 6 1 2 2 3 3 4 2 2 3 6 5 7 1 2 0 2 0 4h0c1 4 1 7 2 11 1-5 1-10 1-14 1 1 1 3 1 5 0 3 1 8-1 12-1 3-3 8-6 10l-4 2c1-2 2-3 2-5 0 0 0-1 1-2l1-1c0-4 0-7-2-12l-1-2v-1h1v-1c-1 0-2-2-3-2h-1l1-1s3 2 4 2v-1c0-1-1-2-2-3l-1-1-1-3c-8-7-16-10-27-13-2 0-4 0-6-1h-3c-2-1-4 0-5-1l8-1h2l8-1z" class="q"></path><path d="M693 604c2 0 7 2 8 4v1h-1c-3 0-8-3-11-4h3l1-1z" class="F"></path><path d="M707 617c3 3 6 7 8 12v7c-1-2-2-3-2-5l-3-1-1-2v-1h1v-1c-1 0-2-2-3-2h-1l1-1s3 2 4 2v-1c0-1-1-2-2-3l-1-1-1-3z" class="S"></path><path d="M713 631l-2-1 1-1v-1l2 2 1-1v7c-1-2-2-3-2-5z" class="C"></path><path d="M710 630l3 1c0 2 1 3 2 5 0 4 0 6-2 10l-1 2-4 2c1-2 2-3 2-5 0 0 0-1 1-2l1-1c0-4 0-7-2-12z" class="D"></path><path d="M688 601c2-1 4 0 6 0 2 1 3 2 4 2 4 1 6 3 9 6 1 2 2 3 3 4 2 2 3 6 5 7 1 2 0 2 0 4l-2-3c-5-11-14-16-25-20z" class="I"></path><path d="M684 600h1c0 1 1 2 2 2l6 2-1 1h-3l-2-1c-2 0-5-1-7 0-2 0-4 0-6-1h-3c-2-1-4 0-5-1l8-1h2l8-1z" class="R"></path><defs><linearGradient id="c" x1="652.086" y1="623.276" x2="649.438" y2="644.035" xlink:href="#B"><stop offset="0" stop-color="#230504"></stop><stop offset="1" stop-color="#5a0a0c"></stop></linearGradient></defs><path fill="url(#c)" d="M639 623h3 2l1-1-1-1h1c3 1 7 4 9 7 1 2 2 5 3 7l2 17v1 5l-1 1c-3-1-6-2-9-1s-7 2-10 4h0c4-3 8-4 13-6v-5c0-1-1-3-1-4-1-3-5-8-9-10h0c-3-1-6-2-10-4v1c-1 0-4 0-5-1h0l3-1v-1l-2-1h0c0-2 0-2 1-3s1-1 1-2c1-1 2-1 3-2v-1c2 0 4-1 6 1z"></path><path d="M648 631h3c0 1 1 2 1 3 1 1 2 4 3 6 0 2 0 4 1 5 1 2 1 4 1 7 0 1 0 1-1 1h-1l1-2c0-1 0-2-1-3 0-2-1-3-2-4 0-2-1-3-2-5-2-1-4-3-5-5l1-2h0l1-1z" class="AB"></path><path d="M648 631h3c0 1 1 2 1 3-2 0-3-1-5-2h0l1-1z" class="U"></path><path d="M644 625c1 0 5 3 6 4 0 1 1 1 1 2h-3l-1 1h0l-1 2c1 2 3 4 5 5 1 2 2 3 2 5-1-1-1-1-2-1-3-4-6-6-10-8v-2c-1-1-1-2-2-2v-2l-1-1v-1h2l1-1c1 0 1 0 2-1h1z" class="C"></path><path d="M646 634c-2-1-2-2-3-3v-1l4 2h0l-1 2z" class="P"></path><path d="M644 625c1 0 5 3 6 4 0 1 1 1 1 2h-3 0c-1-1-2-1-3-2-2 0-3 0-4-1h-1l-1 1-1-1v-1h2l1-1c1 0 1 0 2-1h1z" class="S"></path><path d="M632 633c3 0 6 1 9 2 4 2 7 4 10 8 1 0 1 0 2 1s2 2 2 4c1 1 1 2 1 3l-1 2h1l1 1 1 1 1-3v1 5l-1 1c-3-1-6-2-9-1s-7 2-10 4h0c4-3 8-4 13-6v-5c0-1-1-3-1-4-1-3-5-8-9-10h0c-3-1-6-2-10-4z" class="j"></path><path d="M651 643c1 0 1 0 2 1s2 2 2 4c1 1 1 2 1 3l-1 2-1 1h0c-1-4-2-7-3-11z" class="B"></path><defs><linearGradient id="d" x1="630.141" y1="625.32" x2="641.255" y2="630.051" xlink:href="#B"><stop offset="0" stop-color="#2c0201"></stop><stop offset="1" stop-color="#4a0504"></stop></linearGradient></defs><path fill="url(#d)" d="M632 633v1c-1 0-4 0-5-1h0l3-1v-1l-2-1h0c0-2 0-2 1-3s1-1 1-2c1-1 2-1 3-2v-1c2 0 4-1 6 1 2 1 2 2 5 2h-1c-1 1-1 1-2 1l-1 1h-2v1l1 1v2c1 0 1 1 2 2v2c-3-1-6-2-9-2z"></path><path d="M652 604c2 1 3 0 5 0 5-1 11-1 17-1 2 1 4 1 6 1 11 3 19 6 27 13l1 3 1 1c1 1 2 2 2 3v1c-1 0-4-2-4-2l-1 1h1c1 0 2 2 3 2v1h-1v1c-4-4-7-6-12-7h-8l-3 1-14 3-6 2 2 1-5 2-3 3-1-2h-1l1 4h-2c-1-2-2-5-3-7-2-3-6-6-9-7l-2-2c-1 0-1 0-2-1v-1h-1v-1h-3v-1l-1-1c1-1 2-2 4-2h1v-1l-3-1c3-2 5-3 8-4 1 0 1-1 2-1 2 0 3-1 4-1z" class="AK"></path><path d="M687 610c2 0 4 0 7 1-2 1-2 1-4 1-1 0-2 0-3 1v-1-1-1h0z" class="f"></path><path d="M694 611c2 1 3 2 5 3h-2-7-2s0-1-1-1c1-1 2-1 3-1 2 0 2 0 4-1z" class="a"></path><path d="M656 621l-2-3c-1-2-1-3-1-5 1-2 3-2 5-3 1-1 2-1 3-1h1c2-1 3-1 5-1-3 1-4 1-6 3l-1 1c-2 1-4 2-4 4v5z" class="m"></path><path d="M690 614h7 2c3 3 5 6 8 9l-1 1h1c1 0 2 2 3 2v1h-1v1c-4-4-7-6-12-7l-1-2h3l-1-2-1-1h-2c0-1-1-1-2-1l-3-1z" class="Q"></path><path d="M641 617h3 0l1-1-3-3c1-1 0-1 2-2 3 0 5 3 7 5 1 1 0 0 1 2l3 4 3 9 1 4h-2c-1-2-2-5-3-7-2-3-6-6-9-7l-2-2c-1 0-1 0-2-1v-1z" class="a"></path><path d="M652 618l3 4 3 9 1 4h-2c-1-2-2-5-3-7-2-3-6-6-9-7l-2-2c4 0 9 5 12 7l-4-7 1-1z" class="q"></path><path d="M681 608c1-1 2-1 3-1l3 3h0v1 1 1c1 0 1 1 1 1h2l3 1c1 0 2 0 2 1h2l1 1 1 2h-3l1 2h-8l-3 1-14 3-6 2 2 1-5 2-3 3-1-2h-1c-1-2-2-6-3-9l1-1h0v-5c0-2 2-3 4-4l1-1c2-2 3-2 6-3h14z" class="Y"></path><path d="M668 623l2 1h-1l3 1-6 2 2 1-5 2-3 3-1-2v-1c1-3 3-5 5-7l1 2 3-2z" class="B"></path><path d="M668 623l2 1h-1l3 1-6 2-3 1-1-1 3-2 3-2z" class="P"></path><path d="M664 623h0c6-6 14-11 23-13v1 1c-2 1-4 1-6 2-1 0-1 1-2 1-1 1-3 1-4 2v1l-2 2h0l-1 1c0 1-1 1-1 2l-1 1-2-1-3 2-1-2z" class="N"></path><path d="M668 623h0c1-1 1-2 1-3l6-3v1l-2 2h0l-1 1c0 1-1 1-1 2l-1 1-2-1z" class="AB"></path><path d="M681 608c-1 1-3 2-4 3-1 0-2 1-2 1l-2 1c-4 3-8 6-12 10l-2 2c-1 0-1 0-2-1l-1-3h0v-5c0-2 2-3 4-4l1-1c2-2 3-2 6-3h14z" class="C"></path><path d="M673 613h-3v-1c0-1 3-1 4-1l1 1-2 1z" class="U"></path><path d="M657 624c1-2 1-3 3-5 1 2 0 1 0 3l1 1-2 2c-1 0-1 0-2-1z" class="B"></path><path d="M687 612v1c1 0 1 1 1 1h2l3 1c1 0 2 0 2 1h2l1 1 1 2h-3l1 2h-8l-3 1-14 3-3-1h1l1-1c0-1 1-1 1-2l1-1h0l2-2v-1c1-1 3-1 4-2 1 0 1-1 2-1 2-1 4-1 6-2z" class="f"></path><path d="M688 619h8l1 2h-8l-1-1v-1z" class="I"></path><path d="M687 612v1c1 0 1 1 1 1 0 1-1 1-1 1-2 0-4 1-5 1-2 1-5 1-6 2h-1v-1c1-1 3-1 4-2 1 0 1-1 2-1 2-1 4-1 6-2zm-1 8l2-1v1l1 1-3 1-14 3-3-1h1l1-1 10-2 5-1z" class="C"></path><path d="M686 620l2-1v1l1 1-3 1-1-1 1-1z" class="S"></path><path d="M702 270l6-6c1 1 1 2 1 3h0c1 2 3 3 5 4l-1 2c1 1 1 1 2 1v1c2 2 3 3 4 6l3 6 1 2c1 1 1 1 2 1h0l2 3c2 5 4 9 6 13h1c2 0 2 1 4 2l1 5c1 8 1 17-1 24v2l-6 7-8 8-2 1-9 7c-1 1-2 1-3 2l2 1c-1 1-2 1-2 3h0v2l1 2 2 1-2 1c-2 1-3 3-4 4l-7 7c-5 5-10 9-15 13l-5 4v1l-1 1 1 1c0 1 0 1 1 2 2 1 4 2 6 2-4 0-8 0-12 1-5 1-13 2-17 5l-2 1 3-10 1-2 3-13c3-13 7-27 11-40l3-6c1-1 3-4 4-5v-7c1-3 1-7 2-10 1-2 3-5 4-7v-1l3-15 1-5s1-6 2-6c2-7 5-13 9-19z" class="AY"></path><path d="M700 340v1c0 1-1 2-1 4 1 1 2 1 3 3l-1 1v-1s-1 1-1 2h0v-1c0-2-1-2-2-3h-2 0l1-2v-1c0-1 1-2 3-3z" class="Y"></path><path d="M703 325c3 0 5 1 6 3l3 6c0 2-1 3-1 5 0 3-2 6-3 8l-2 2c0 1-1 2-1 3l-2 3-1 1c2-6 7-11 8-17 0-3 0-8-2-10s-3-2-6-3l-1-1h2z" class="Q"></path><path d="M708 347h1l1-1v2h0l-2 4h0c2 0 3-3 4-4-1 1-1 2-1 4 3-1 5-4 7-5h1c-3 3-5 5-9 8-4 2-7 4-12 4 2-1 3-1 4-3l1-1 2-3c0-1 1-2 1-3l2-2z" class="m"></path><path d="M660 404c13-5 22-10 31-21 3-4 8-11 8-16-1-3-2-4-4-6-1 0-1 0-1-1h4l-1 1c2 2 2 2 3 5l-1 1h0c1 1 1 0 1 1s0 1 1 2l1-1c0-1 1-3 3-4-1 4-2 5-4 8-1 2-3 5-4 7-2 3-5 5-7 8h-1c-3 3-5 7-9 8l-6 3c-4 3-10 4-13 7h-2l1-2z" class="r"></path><path d="M689 343c0-1 0-1 1-2h2c-2 6-4 12-5 18l-3 15c0 5-1 10-4 13-1 2-2 3-3 4h-1l3-5c1-1 1-3 1-4 1-1 1-1 1-2 1-1 1-3 1-5l-1-1-1 1v-1c1-2 2-4 2-6s2-5 2-7c0 0 0-1-1-1l2-5c-1-1-1-1-2-1 1-3 1-5 3-7l3-4z" class="D"></path><path d="M686 347l3-4-1 4c0 2 0 4-1 6-1 3 0 5-3 8 0 0 0-1-1-1l2-5c-1-1-1-1-2-1 1-3 1-5 3-7z" class="v"></path><path d="M705 365v-1c2 2 2 4 4 5l1 3h1l2 1-2 1c-2 1-3 3-4 4l-7 7c-5 5-10 9-15 13l-5 4v1l-1 1 1 1c0 1 0 1 1 2 2 1 4 2 6 2-4 0-8 0-12 1-5 1-13 2-17 5l-2 1 3-10h2c3-3 9-4 13-7l6-3c4-1 6-5 9-8h1c2-3 5-5 7-8 1-2 3-5 4-7 2-3 3-4 4-8z" class="e"></path><path d="M675 401c1-1 3-2 3-2l3-2s1-1 2-1c-2 2-4 2-5 4 1 1 1 1 2 1s1-1 2-1l2-2h1l-5 4v1l-1 1 1 1c0 1 0 1 1 2h-6c-1-1 1-1 2-3h-2l2-2v-1h-2z" class="q"></path><path d="M675 401h2v1l-2 2h2c-1 2-3 2-2 3h6c2 1 4 2 6 2-4 0-8 0-12 1l-1-2h-6 0c0-1 1-2 1-3h-1l7-4z" class="B"></path><path d="M705 365v-1c2 2 2 4 4 5-1 2-3 3-4 5-2 2-4 5-6 6 0 0-1 0-1 1-1 0-1 1-1 2l-4 4h-1l-2 1h0c2-3 5-5 7-8 1-2 3-5 4-7 2-3 3-4 4-8z" class="X"></path><path d="M661 406c2 0 4-1 7-1h1c0 1-1 2-1 3h0 6l1 2c-5 1-13 2-17 5l-2 1 3-10h2z" class="U"></path><path d="M681 333c1-3 1-7 2-10 1-2 3-5 4-7v-1c1 1 2 1 3 1 2 2 5 2 7 2 3 1 5 2 7 3l2 1-3 3h-2l1 1c-5 4-8 10-10 15h-2c-1 1-1 1-1 2l-3 4c-2 2-2 4-3 7 1 0 1 0 2 1l-2 5c-1 3-3 7-4 10-2 3-4 5-5 8h0c-1 1-1 2-2 3-1 3-5 6-7 9-1 1-1 1-2 1 3-13 7-27 11-40l3-6c1-1 3-4 4-5v-7z" class="AK"></path><path d="M690 341h0l3-6c2-4 4-9 8-10l1 1c-5 4-8 10-10 15h-2z" class="S"></path><path d="M680 355l-2 3c0-1 1-3 1-4 2-5 5-10 7-15l1-1v1l1-2v1l-3 7 1 2c-2 2-2 4-3 7l-1 2h0l-2-1z" class="m"></path><path d="M685 345l1 2c-2 2-2 4-3 7l-1 2h0l-2-1c1-3 4-7 5-10z" class="AX"></path><path d="M683 354c1 0 1 0 2 1l-2 5c-1 3-3 7-4 10-2 3-4 5-5 8 0-1-1-1-1-2l5-9c1-3 3-7 4-11h0l1-2z" class="I"></path><path d="M702 270l6-6c1 1 1 2 1 3h0c1 2 3 3 5 4l-1 2c1 1 1 1 2 1v1c2 2 3 3 4 6l3 6 1 2c1 1 1 1 2 1h0l2 3c2 5 4 9 6 13h1c2 0 2 1 4 2l1 5c1 8 1 17-1 24v2l-6 7-8 8-2 1-9 7c-1 1-2 1-3 2l2 1c-1 1-2 1-2 3h0v2l1 2h-1l-1-3c-2-1-2-3-4-5v1c-2 1-3 3-3 4l-1 1c-1-1-1-1-1-2s0 0-1-1h0l1-1c-1-3-1-3-3-5l1-1v-1c5 0 8-2 12-4 4-3 6-5 9-8h-1c-2 1-4 4-7 5 0-2 0-3 1-4-1 1-2 4-4 4h0l2-4h0v-2l-1 1h-1c1-2 3-5 3-8 0-2 1-3 1-5l-3-6c-1-2-3-3-6-3l3-3-2-1c-2-1-4-2-7-3-2 0-5 0-7-2-1 0-2 0-3-1l3-15 1-5s1-6 2-6c2-7 5-13 9-19z" class="AY"></path><path d="M706 317l9-8v7c-1 2 0 3-2 4-1 0-2 0-3-1v-1h-1c-1 0-2 0-3-1z" class="v"></path><path d="M700 316h2l1 2c1 0 1 0 3-1 1 1 2 1 3 1h1v1l1 1h0c1 1 1 0 2 1h2c1 2 2 2 1 4v1 4-2c1-2 1-4 3-6l-3 12c0-2 0-6-1-8-1-1-2-3-2-4-1 0-2 0-3-1-2-1-9-2-11-4l1-1z" class="Y"></path><path d="M700 316h0l-1 1c2 2 9 3 11 4 1 1 2 1 3 1 0 1 1 3 2 4v9h-1v-6c-1-1-1-2-2-3 0 0 0-1-1-1 0-1-1-2-2-2-2-1-3-2-5-2-2-1-4-2-7-3-2 0-5 0-7-2 2 0 4 0 6 1 1 0 3 0 4-1z" class="f"></path><path d="M719 322v-1c0-1 0-3 1-5h1c0 3-1 5-1 7-2 8-3 19-8 25-1 1-2 4-4 4h0l2-4v-1c4-6 4-11 4-18v6h1v-9c1 2 1 6 1 8l3-12z" class="P"></path><path d="M704 321c2 0 3 1 5 2 1 0 2 1 2 2 1 0 1 1 1 1 1 1 1 2 2 3 0 7 0 12-4 18v1h0v-2l-1 1h-1c1-2 3-5 3-8 0-2 1-3 1-5l-3-6c-1-2-3-3-6-3l3-3-2-1z" class="Y"></path><path d="M719 286c2 1 3 5 4 7l2-2 2 5c1 3 2 7 3 11 0 1-1 3 0 5h-1c0 2 0 5 1 7 0 1 0 5-1 7 0 1-1 2-2 3 1-5 1-10 0-15-2-1-2-1-4 0 0-2 2-4 3-5 0-5-2-9-3-13 0-1-1-2-1-4l-3-6z" class="X"></path><path d="M725 291l2 5c1 3 2 7 3 11 0 1-1 3 0 5h-1v-2c-2-6-4-11-6-17l2-2z" class="I"></path><path d="M725 290h0l2 3c2 5 4 9 6 13 0 2 1 4 1 5 1 9-1 16-3 24-1 1-2 3-3 4v-2l1-1v-1-2-1l1-1c0-2 0-4-1-5 1-2 1-6 1-7-1-2-1-5-1-7h1c-1-2 0-4 0-5-1-4-2-8-3-11l-2-5v-1z" class="q"></path><path d="M725 290h0l2 3c2 5 4 9 6 13 0 2 1 4 1 5 0 2 0 4-1 5v-1-1c-1-2-1-5-2-6h0c0 1 0 1-1 2v-3c-1-4-2-8-3-11l-2-5v-1z" class="AW"></path><path d="M733 306h1c2 0 2 1 4 2l1 5c1 8 1 17-1 24v2l-6 7-8 8-1-3c0-1 0-2 1-3l3-3v-2-1l1-3c1-1 2-3 3-4 2-8 4-15 3-24 0-1-1-3-1-5z" class="a"></path><path d="M728 339c1-1 2-3 3-4 1 1 1 2 1 4-1 2-2 3-3 4l-1 1-1 1v-2-1l1-3z" class="U"></path><path d="M727 329c1-1 2-2 2-3 1 1 1 3 1 5l-1 1v1 2 1l-1 1v2l-1 3v1 2l-3 3c-1 1-1 2-1 3l1 3-2 1-9 7c-1 1-2 1-3 2l2 1c-1 1-2 1-2 3h0v2l1 2h-1l-1-3c-2-1-2-3-4-5v1c-2 1-3 3-3 4l-1 1c-1-1-1-1-1-2s0 0-1-1h0l1-1c-1-3-1-3-3-5l1-1v-1c5 0 8-2 12-4 4-3 6-5 9-8l2-3c1-3 3-6 5-10l1-5z" class="l"></path><path d="M721 344c0 2 0 3-1 5-1 3-4 7-7 8h0v-2h-3c4-3 6-5 9-8l2-3z" class="AC"></path><path d="M727 329c1-1 2-2 2-3 1 1 1 3 1 5l-1 1v1 2 1l-1 1v2l-1 3v1 2l-3 3c-1 1-1 2-1 3l1 3-2 1-1-1-1-1 1-1h0c1-2 2-2 2-4 0-1 1-3 2-4h0l1-4c1-2 1-4 0-6l1-5z" class="AW"></path><path d="M710 355h3v2l-6 6c1 1 1 2 2 2l1-1 2 1c-1 1-2 1-2 3h0v2l1 2h-1l-1-3c-2-1-2-3-4-5v1c-2 1-3 3-3 4l-1 1c-1-1-1-1-1-2s0 0-1-1h0l1-1c-1-3-1-3-3-5l1-1v-1c5 0 8-2 12-4z" class="AV"></path><defs><linearGradient id="e" x1="689.852" y1="278.942" x2="710.729" y2="306.351" xlink:href="#B"><stop offset="0" stop-color="#130101"></stop><stop offset="1" stop-color="#230000"></stop></linearGradient></defs><path fill="url(#e)" d="M702 270l6-6c1 1 1 2 1 3h0c1 2 3 3 5 4l-1 2c1 1 1 1 2 1v1c2 2 3 3 4 6l3 6 1 2c1 1 1 1 2 1v1l-2 2c-1-2-2-6-4-7l3 6c0 2 1 3 1 4-7 6-13 14-21 20h0-2 0c-1 1-3 1-4 1-2-1-4-1-6-1-1 0-2 0-3-1l3-15 1-5s1-6 2-6c2-7 5-13 9-19z"></path><path d="M715 275c2 2 3 3 4 6l3 6 1 2c1 1 1 1 2 1v1l-2 2c-1-2-2-6-4-7h-1c-1-2-3-4-5-6-1 0-2-1-2-1l1-1 2 2c1 1 2 1 3 0h0c-1-2-2-3-2-5z" class="AK"></path><path d="M722 292c0 2 1 3 1 4-7 6-13 14-21 20h0-2 0c2-1 3-2 5-4 3-2 5-6 8-9s6-7 9-11z" class="N"></path><defs><linearGradient id="f" x1="753.895" y1="345.408" x2="758.786" y2="380.586" xlink:href="#B"><stop offset="0" stop-color="#130000"></stop><stop offset="1" stop-color="#650c0c"></stop></linearGradient></defs><path fill="url(#f)" d="M775 336l8-1 1 1c-1 1-2 1-4 1h4c3 0 5 1 7 1-3 1-6 2-9 2l-8 2h0c3 0 6-1 9 0v2h0c0 1 0 2 1 4l1 1v4l-1 6-1 4c-1 3-2 6-4 9-4 4-8 7-12 11-3 3-5 5-7 8-1 1-2 3-3 4l-1 1v1c-1 2-1 5-2 6h-1v4 5 7c0 2 0 2-1 3 2 1 3 3 5 3l1 1 15 13c1 1 4 5 5 6l1 1v1c-1 0-1 0-1 1h0l-1 1-2-1 1 2-1 3c0 1 0 2-1 3 0-3-4-6-7-7l-7-4c-12-6-25-5-38-5l-7 1-3 1-1-1c-1 0-2 1-2 1l-1-1h-2 0c-2 0-4 0-6 1v-1h-3-1c2-2 5-6 5-8 0-1 0-1 1-2h0l-1-1v-2l1-3v-4l-1-4v-1c-2-4-4-5-7-6l-4-1h-3c-2 0-4-1-6-2-1-1-1-1-1-2l-1-1 1-1v-1l5-4c5-4 10-8 15-13l7-7c1-1 2-3 4-4l2-1c2-3 5-5 8-7l-1-1 3-3-1-1 10-7 8-7c2-1 3-1 5-2l16-5 14-4z"></path><path d="M738 369s1-1 1-2h1c2 0 3-2 4-4h0c2 0 4-1 6-2 1 1 1 1 2 1s1 1 1 1h-1-1c-2 0-6 2-8 3-1 1-1 1-1 2-1 1-2 2-3 4l-1-3z" class="S"></path><path d="M742 368c2 0 3-1 4-2v-1l1 1-2 2v1l1 1 3-2 1 2h1l1-1 1-1c0-1 1-1 1-1 2 0 2 0 2 1h2c0-1 0 0 1-1h1c1-1 1-1 2-1l1 1c-1 0-1 0-2 1-1 0-2 0-3 1-1 0-2 1-3 2-2 0-3-1-5 0l-2 2h0c-1-2-2-2-4-3l-1 2h-4 0c1-2 2-3 3-4z" class="U"></path><path d="M784 348l1 1v4l-1 6-1 4c-1 3-2 6-4 9-4 4-8 7-12 11h-4v-1l12-16h1 0s1-1 1-2l1-2c1-1 1-1 1-3 0-1 0-2 1-3 0-2 1-3 3-4 1-1 1-3 1-4z" class="H"></path><path d="M776 366h0s1-1 1-2l1-2c1-1 1-1 1-3 0-1 0-2 1-3 0-2 1-3 3-4-2 11-6 18-13 26 1-4 4-8 6-12z" class="AX"></path><path d="M739 372h4l1-2c2 1 3 1 4 3h0l2-2c2-1 3 0 5 0 1-1 2-2 3-2 1-1 2-1 3-1 1-1 1-1 2-1 1 1 1 1 2 3l-1 1v2c-1 3-4 6-3 8 1 1 1 1 2 1v1l-3 4c-1 1-2 1-3 2v-3l1-2c1-1 1-2 1-3l-1-1v-1c0-1 0-2-1-3-1 0-2 0-4 1h0c-1 1-1 2-2 3h0c-1-1-2-1-2-2h-2c-2 2-2 2-3 4v1c-2 0-2 1-3 1h-1c1-2 1-3 1-5-1 1-1 1-2 1s-1 0-1 1l-1-2c-1 0-1 1-2 1l1-2 3-6z" class="f"></path><path d="M737 379c1-2 2-3 4-4 1 0 1 1 2 3h0l2-1v1c2-1 2-2 4-4h0c2 1 5-1 6-1 2 0 2-1 3-2 2 1 2 0 3 2 0 2-1 4-2 6l1 1-1 1-1-1v-1c0-1 0-2-1-3-1 0-2 0-4 1h0c-1 1-1 2-2 3h0c-1-1-2-1-2-2h-2c-2 2-2 2-3 4v1c-2 0-2 1-3 1h-1c1-2 1-3 1-5-1 1-1 1-2 1s-1 0-1 1l-1-2z" class="AA"></path><path d="M775 336l8-1 1 1c-1 1-2 1-4 1h4c3 0 5 1 7 1-3 1-6 2-9 2l-8 2c-9 2-18 5-26 9-3 2-7 3-10 6-1 1-1 1-1 3h-3c-3 0-4 1-7 2-2 1-4 2-6 4l-1-1 3-3-1-1 10-7 8-7c2-1 3-1 5-2l16-5 14-4z" class="AC"></path><path d="M732 354h2c-2 4-8 5-11 8l-1-1 10-7z" class="r"></path><path d="M780 337h4c3 0 5 1 7 1-3 1-6 2-9 2h-5 0-7l10-3z" class="AF"></path><path d="M745 345h2c1 0 2 1 3 1-5 3-11 4-16 8h-2l8-7c2-1 3-1 5-2z" class="Aj"></path><path d="M775 336l8-1 1 1c-1 1-2 1-4 1l-10 3-20 6c-1 0-2-1-3-1h-2l16-5 14-4z" class="n"></path><path d="M734 372c1-1 2-1 3-2v-1h1l1 3h0l-3 6-1 2c1 0 1-1 2-1l1 2c0-1 0-1 1-1s1 0 2-1c0 2 0 3-1 5h1c1 0 1-1 3-1v-1c1-2 1-2 3-4h2c0 1 1 1 2 2h0c1-1 1-2 2-3h0c2-1 3-1 4-1 1 1 1 2 1 3v1l1 1c0 1 0 2-1 3l-1 2v3c1-1 2-1 3-2l3-4h4c-3 3-5 5-7 8-1 1-2 3-3 4l-1 1v1c-1 2-1 5-2 6h-1v4 5 7c0 2 0 2-1 3l-8-5c-1-1-2-2-4-3h0c-4-3-7-8-9-13-2-6-4-13-3-19 0-1 1-2 1-3v-1h1c1-2 2-5 4-6z" class="q"></path><path d="M740 384h1c1 0 1-1 3-1l-2 2h0l-2 4h0v-4-1z" class="F"></path><path d="M744 383v-1c1-2 1-2 3-4h2c0 1 1 1 2 2h0 0c-1 2-2 5-3 6 0-2-1-3 0-5l-1-1c-1 1-1 2-2 3l-2 2-1 1v-1h0l2-2z" class="u"></path><path d="M754 384l1-2c1-2 2-3 2-5h0v-1c1 1 1 2 1 3v1l1 1c0 1 0 2-1 3l-1 2c-1 1-2 1-2 2 0-2-1-2-1-4z" class="E"></path><path d="M745 400h-1l4-12h1v1c0 1 1 2 1 3h0l-1 4-2 4h-2z" class="AH"></path><path d="M754 384c0 2 1 2 1 4 0-1 1-1 2-2v3c1-1 2-1 3-2-2 3-4 8-6 10s-5 3-7 5v-2l2-4 1-4c1-1 1-3 2-5l2-3z" class="R"></path><path d="M754 384c0 2 1 2 1 4-1 3-4 6-6 8l1-4c1-1 1-3 2-5l2-3z" class="M"></path><path d="M763 383h4c-3 3-5 5-7 8-1 1-2 3-3 4l-1 1v1c-1 2-1 5-2 6h-1c-1 3-3 3-5 4-1 0-1 0-2-1l-1-1 1-2 1-1c2-2 5-3 7-5s4-7 6-10l3-4z" class="AG"></path><path d="M746 403l6-3 1 1v2c-1 3-3 3-5 4-1 0-1 0-2-1l-1-1 1-2z" class="S"></path><path d="M747 400v2l-1 1-1 2 1 1c1 1 1 1 2 1 2-1 4-1 5-4v4 5 7c0 2 0 2-1 3l-8-5c-1-1-2-2-4-3h0l1-2c0-1-1-2-2-2v-2c1-2 2-3 4-4l1-1c1-1 1-2 1-3h2z" class="u"></path><path d="M748 413l2-2v1h1c1-1 1-1 2 0v7h0c-1-3-3-4-5-6z" class="N"></path><path d="M743 407c2 2 6-1 7 4l-2 2-1-2c-2 0-3-1-5-2v-1l1-1z" class="B"></path><path d="M753 403v4 5c-1-1-1-1-2 0h-1v-1c-1-5-5-2-7-4 1-1 1-2 2-2l1 1c1 1 1 1 2 1 2-1 4-1 5-4z" class="V"></path><path d="M734 372c1-1 2-1 3-2v-1h1l1 3h0l-3 6-1 2c1 0 1-1 2-1l1 2c0-1 0-1 1-1s1 0 2-1c0 2 0 3-1 5v1c-2 0-2 1-3 2-2 3 0 6-1 8-1 3 1 8 0 11 1 1 2 3 3 4 1 0 2 1 2 2l-1 2c-4-3-7-8-9-13-2-6-4-13-3-19 0-1 1-2 1-3v-1h1c1-2 2-5 4-6z" class="C"></path><path d="M734 372c1-1 2-1 3-2v-1h1l1 3h0l-3 6c-1 1-2 2-2 3-1 1-1 1-1 2-1 0-1 1-1 1v1h-1v-1l-1 3v-2c1-2-1-3 0-5v-1-1c1-2 2-5 4-6z" class="a"></path><path d="M735 380c1 0 1-1 2-1l1 2c0-1 0-1 1-1s1 0 2-1c0 2 0 3-1 5v1c-2 0-2 1-3 2-2 3 0 6-1 8-1 3 1 8 0 11v-1c-3-2-4-7-4-11h0c0-5 1-9 3-14z" class="R"></path><path d="M738 381c0-1 0-1 1-1s1 0 2-1c0 2 0 3-1 5v1c-2 0-2 1-3 2-2 3 0 6-1 8-1 3 1 8 0 11v-1c-1-2-3-6-3-7 0-2 2-4 2-6v-3l1-1c-1-1-1-2-1-3h1 1l1-4z" class="u"></path><path d="M727 362h3c0 1-1 1-2 2h2c0 3 0 6-1 8v4c1 0 1-1 2-2l1-1 4-5v1l-2 3c-2 1-3 4-4 6h-1v1c0 1-1 2-1 3-1 6 1 13 3 19 2 5 5 10 9 13h0v1 1l-2-2c-1 1-1 1-1 2 4 3 11 6 13 10h-2 0c-3-2-6-1-9-1v-1-1c-2-4-5-5-8-7l-15-4c-3-1-7-1-10-1l-2 2c0 1 0 1 1 2l-1 1h0-2l-1 1v-1c-2-4-4-5-7-6l-4-1h-3c-2 0-4-1-6-2-1-1-1-1-1-2l-1-1 1-1v-1l5-4c5-4 10-8 15-13l7-7c1-1 2-3 4-4l2-1c2-3 5-5 8-7 2-2 4-3 6-4z" class="Y"></path><path d="M727 372h0l1-5v15c-1 6 1 13 3 19l-1 1-1-2-1-1-1 2v-3c-1-6-1-12-1-18v-2c1-2 1-4 1-6z" class="T"></path><path d="M705 386c1 0 2-1 2-2 1-2 2-2 3-3 2-2 5-4 6-6h1 3v2h5 0c0-2 0-3 1-4l1-1c0 2 0 4-1 6v2 2l-1 2-1 3-2-1c0-1-1-2-1-3h-1l-1-1-1-1h-5c-2 1-4 3-6 4-1 1-1 1-2 1z" class="D"></path><path d="M713 381c1 0 3-1 3-1l3-3c0 1 1 2 2 3l2-2v2h1l2-2v2 2l-1 2-1 3-2-1c0-1-1-2-1-3h-1l-1-1-1-1h-5z" class="B"></path><path d="M726 378v2 2c-1 1-2 2-3 4h0c-1-1-1-1-1-3-1-1-1-2-1-3l2-2v2h1l2-2z" class="C"></path><path d="M726 382v-2c0 6 0 12 1 18-1-1-2-1-2-2h0c-1 1-1 0-2 1 0-1-1-2-1-3-1 0-2-1-2-1-1 0-1 1-1 2l-1 1-1 1-1 1c-1-1-1-2-1-2-1-2-1-2 0-3l-1-3v-2c-1-1-1-1-2-1h-1-3l1-1 5-3h1 0 2 1v-2l1 1 1 1h1c0 1 1 2 1 3l2 1 1-3 1-2z" class="N"></path><path d="M718 381l1 1 1 1h1c0 1 1 2 1 3l2 1 1-3c0 2 1 4 0 5-1 2-1 3-1 4h-1v-1c0-2-1-3-2-4 0-1 0-2-1-4l-1 1c-1 0-1 1-2 0v-1l-2 1s0-1-1-1v-1h1 0 2 1v-2z" class="f"></path><path d="M714 383v1c1 0 1 1 1 1l2-1v1c1 1 1 0 2 0l1-1c1 2 1 3 1 4l-1-1h0c-1 1-1 1-2 1l1 3 1 1v1c-1 0-1 1-1 2l-1 1-1 1-1 1c-1-1-1-2-1-2-1-2-1-2 0-3l-1-3v-2c-1-1-1-1-2-1h-1-3l1-1 5-3z" class="R"></path><path d="M715 393c0-1 0-1 1-1 1-1 1-3 1-5h0 1v1l1 3 1 1v1c-1 0-1 1-1 2l-1 1-1 1-1 1c-1-1-1-2-1-2-1-2-1-2 0-3z" class="z"></path><path d="M705 386c1 0 1 0 2-1 2-1 4-3 6-4h5v2h-1-2 0-1l-5 3-1 1h3 1c1 0 1 0 2 1v2c-1 0-2 1-3 1-2 0-3 3-5 4-1 0-2 0-2 1h0c1 1 1 1 1 2h-4-1c-1 1-1 2-2 2v1h-2c-1 1-2 1-3 1s-1-1-2-2c3-3 6-5 9-8h0l5-6z" class="N"></path><path d="M705 386c1 0 1 0 2-1 2-1 4-3 6-4h5v2h-1-2 0-1l-5 3-1 1c-1 1-2 1-3 2s-1 0-2 1l-2 2h-1 0l5-6z" class="U"></path><path d="M698 400c0-2 2-6 4-7 1-1 3-1 4-3h1 1c1 0 2-1 2-2v-1h2c1 0 1 0 2 1v2c-1 0-2 1-3 1-2 0-3 3-5 4-1 0-2 0-2 1h0c1 1 1 1 1 2h-4-1c-1 1-1 2-2 2z" class="u"></path><path d="M714 390l1 3c-1 1-1 1 0 3 0 0 0 1 1 2l1-1 1-1 1-1c0-1 0-2 1-2 0 0 1 1 2 1 0 1 1 2 1 3 1-1 1 0 2-1h0c0 1 1 1 2 2v3l1-2 1 1 1 2 1-1c2 5 5 10 9 13h0v1 1l-2-2c-1 1-1 1-1 2 4 3 11 6 13 10h-2 0c-3-2-6-1-9-1v-1-1c-2-4-5-5-8-7l-15-4c-3-1-7-1-10-1l-2 2c0 1 0 1 1 2l-1 1h0-2l-1 1v-1c-2-4-4-5-7-6l-4-1h-3c-2 0-4-1-6-2-1-1-1-1-1-2l-1-1 1-1h6 4v-1c0-1 1-1 1-2 1 1 1 2 2 2s2 0 3-1h2v-1c1 0 1-1 2-2h1 4c0-1 0-1-1-2h0c0-1 1-1 2-1 2-1 3-4 5-4 1 0 2-1 3-1z" class="e"></path><path d="M690 403v-1c0-1 1-1 1-2 1 1 1 2 2 2s2 0 3-1h2l3 1v1l-2 1-9-1z" class="R"></path><path d="M680 403h6c-1 0-2 1-3 1 1 1 1 1 2 1 2 0 4 1 6 2 0 1 0 1-1 2h-3c-2 0-4-1-6-2-1-1-1-1-1-2l-1-1 1-1z" class="j"></path><path d="M698 400c1 0 1-1 2-2h1 4l2 3 1 2 1 2-7-1h-3l2-1v-1l-3-1v-1z" class="B"></path><path d="M707 401l1 2 1 2-7-1v-1c2-2 3-1 5-2z" class="u"></path><path d="M717 397l1-1 1-1c0-1 0-2 1-2 0 0 1 1 2 1 0 1 1 2 1 3 1-1 1 0 2-1h0c0 1 1 1 2 2v3l1-2 1 1 1 2c0 1 0 1 1 2 0 1 1 2 1 2v1c1 1 1 1 1 2-2 0-3 0-5-1l-9-2c1 0 0 0 1-1 0-1-1-3-1-4 0-2-1-3-2-4z" class="l"></path><path d="M717 397l1-1 1-1c0-1 0-2 1-2 0 0 1 1 2 1 0 1 1 2 1 3 1-1 1 0 2-1h0c0 1 1 1 2 2v3c-2 0-3-2-5-3 0 2 1 3 2 5h0-2c-1-1-1-2-3-2 0-2-1-3-2-4z" class="AR"></path><path d="M714 390l1 3c-1 1-1 1 0 3 0 0 0 1 1 2l1-1c1 1 2 2 2 4 0 1 1 3 1 4-1 1 0 1-1 1h-3l-7-1-1-2-1-2-2-3c0-1 0-1-1-2h0c0-1 1-1 2-1 2-1 3-4 5-4 1 0 2-1 3-1z" class="q"></path><path d="M705 398c0-1 0-1-1-2h0c0-1 1-1 2-1v2s0 1 1 1 2 0 2 1v2l-1 2-1-2-2-3z" class="z"></path><path d="M709 401l1 1 1-1 1 1 2-1 2 1c-1 2-1 2 0 4l-7-1-1-2 1-2z" class="F"></path><path d="M711 401c-1-1-1-2-1-3 1-2 2-2 4-2 1 1 1 3 2 4v2l-2-1-2 1-1-1zm-17 9c2-2 3-2 6-2h6c2 1 5 0 7 1 1 0 1 1 2 0h1c1 1 2 1 4 1 4 1 8 2 12 4 2 1 3 1 5 2 4 3 11 6 13 10h-2 0c-3-2-6-1-9-1v-1-1c-2-4-5-5-8-7l-15-4c-3-1-7-1-10-1l-2 2c0 1 0 1 1 2l-1 1h0-2l-1 1v-1c-2-4-4-5-7-6z" class="H"></path><path d="M704 416l-1-2-3-2v-1c1-1 2-1 3 0h1l1-1 1 1-2 2c0 1 0 1 1 2l-1 1h0z" class="AC"></path><path d="M716 412c4-2 12 2 16 2 2 1 3 1 5 2 4 3 11 6 13 10h-2 0c-3-2-6-1-9-1v-1-1c-2-4-5-5-8-7l-15-4z" class="AV"></path><path d="M706 411c3 0 7 0 10 1l15 4c3 2 6 3 8 7v1 1c3 0 6-1 9 1h0 2c-2-4-9-7-13-10 0-1 0-1 1-2l2 2v-1-1c2 1 3 2 4 3l8 5c2 1 3 3 5 3l1 1 15 13c1 1 4 5 5 6l1 1v1c-1 0-1 0-1 1h0l-1 1-2-1 1 2-1 3c0 1 0 2-1 3 0-3-4-6-7-7l-7-4c-12-6-25-5-38-5l-7 1-3 1-1-1c-1 0-2 1-2 1l-1-1h-2 0c-2 0-4 0-6 1v-1h-3-1c2-2 5-6 5-8 0-1 0-1 1-2h0l-1-1v-2l1-3v-4l-1-4 1-1h2 0l1-1c-1-1-1-1-1-2l2-2z" class="c"></path><path d="M706 416c2 1 4 0 5 1s1 2 1 2v2l-1 1-5-6z" class="I"></path><path d="M722 434l1 1 7-3 11 1c2 0 4 2 6 2l1 2-5-1h-5c-2-1-8 0-10 0h-6-2v-1l2-1z" class="j"></path><path d="M747 435c2 0 3 1 4 2 2 1 5 2 7 3 1 1 1 1 0 3l-7-2h-1c-2-1-5-1-7-2l-2-2c-1 0-2-1-3-1h5l5 1-1-2z" class="t"></path><path d="M743 436l5 1c3 0 4 0 6 2-1 1 0 1-2 1l-1 1h-1l-1-3c-2 0-4-1-6-2z" class="AH"></path><path d="M738 436h5c2 1 4 2 6 2l1 3c-2-1-5-1-7-2l-2-2c-1 0-2-1-3-1z" class="l"></path><path d="M719 437c3 0 7 0 11 1 3 1 6 0 9 1h4c2 1 5 1 7 2h1l7 2c1 1 2 1 2 2-12-6-25-5-38-5-2-1-2-1-3-3z" class="X"></path><path d="M734 428s0 1 1 2c2 1 4-1 7 0 1 0 1 0 3 1h2c1 0 1 0 3 1h0c3 1 4 2 6 4 8 4 15 9 18 17h1c0 1 0 2-1 3 0-3-4-6-7-7l-7-4c0-1-1-1-2-2 1-2 1-2 0-3-2-1-5-2-7-3-1-1-2-2-4-2s-4-2-6-2l-11-1-7 3-1-1c3-2 8-5 12-6z" class="AA"></path><path d="M758 440c2 0 4 1 5 3 1 1 2 2 3 4 1 0 1 1 1 2l-7-4c0-1-1-1-2-2 1-2 1-2 0-3z" class="M"></path><path d="M734 428s0 1 1 2c2 1 4-1 7 0 1 0 1 0 3 1h2c1 0 1 0 3 1h0c3 1 4 2 6 4-4-2-9-4-13-4-5-1-8-1-13 0l-7 3-1-1c3-2 8-5 12-6z" class="I"></path><path d="M704 416h0c4 4 8 9 11 13 1 1 2 3 2 4v3h2v1c1 2 1 2 3 3l-7 1-3 1-1-1c-1 0-2 1-2 1l-1-1h-2 0c-2 0-4 0-6 1v-1h-3-1c2-2 5-6 5-8 0-1 0-1 1-2h0l-1-1v-2l1-3v-4l-1-4 1-1h2z" class="H"></path><path d="M709 435h4c-3 2-6 3-9 4l-1-1c0-1 1-1 2-1 1-2 3-2 4-2z" class="F"></path><path d="M715 429c1 1 2 3 2 4l-4 2h-4l1-1c1-1 2-1 3-1h1v-1l-1-1 2-2z" class="E"></path><path d="M719 436v1c1 2 1 2 3 3l-7 1-3 1-1-1c2-1 4-3 6-4 1-1 1-1 2-1z" class="AC"></path><path d="M702 425c1 1 1 1 1 2v4h0l3-3h0l5 1 2 2 1 1v1h-1c-1 0-2 0-3 1l-1 1c-1 0-3 0-4 2-1 0-2 0-2 1l1 1-4 2h-3-1c2-2 5-6 5-8 0-1 0-1 1-2h0l-1-1v-2l1-3z" class="M"></path><path d="M703 438c-1 0-2 0-2 1l-1-1c1-2 1-2 3-3 0-1 0-1 1-2l1 1h5 0l-1 1c-1 0-3 0-4 2-1 0-2 0-2 1z" class="B"></path><path d="M704 416h0c4 4 8 9 11 13l-2 2-2-2-5-1h0l-3 3h0v-4c0-1 0-1-1-2v-4l-1-4 1-1h2z" class="AK"></path><path d="M701 417l1-1c1 2 1 7 3 9l1-1c1 1 2 1 3 3 0 1 1 1 2 2l-5-1h0l-3 3h0v-4c0-1 0-1-1-2v-4l-1-4z" class="AA"></path><path d="M706 411c3 0 7 0 10 1l15 4c3 2 6 3 8 7v1c-4 2-10 4-14 7-1 1-4 2-5 2v-2h-1v1l-8-10 1-1v-2s0-1-1-2-3 0-5-1l-1-1c-1-1-1-1-1-2l2-2z" class="B"></path><path d="M711 417h6c4 0 7 0 10 1 1 0 1 1 2 1 2 0 2 1 4 1l2 2 1-1h0c2 1 1 0 2 2l-2 1c-1-1-1 0-2-1l-1 1h0c-1-1-2-1-2-2-1-1-1 0-2 0s-1-2-2-2-2-1-3-1-4-1-5-1l-1 1c-1 1-2 1-3 1l-2-1h-1s0-1-1-2z" class="Q"></path><path d="M706 411c3 0 7 0 10 1l15 4c1 2 3 4 5 5l-1 1-2-2c-2 0-2-1-4-1-1 0-1-1-2-1-3-1-6-1-10-1h-6c-1-1-3 0-5-1l-1-1c-1-1-1-1-1-2l2-2z" class="AX"></path><path d="M737 416c0-1 0-1 1-2l2 2v-1-1c2 1 3 2 4 3l8 5c2 1 3 3 5 3l1 1 15 13c1 1 4 5 5 6l1 1v1c-1 0-1 0-1 1h0l-1 1-2-1 1 2-1 3h-1c-3-8-10-13-18-17-2-2-3-3-6-4h0c-2-1-2-1-3-1h-2c-2-1-2-1-3-1-3-1-5 1-7 0-1-1-1-2-1-2l5-3c3 0 6-1 9 1h0 2c-2-4-9-7-13-10z" class="a"></path><path d="M737 416c0-1 0-1 1-2l2 2v-1-1c2 1 3 2 4 3l8 5c2 1 3 3 5 3l1 1 15 13c1 1 4 5 5 6l1 1v1c-1 0-1 0-1 1h0l-1 1-2-1-5-8c-6-5-13-10-20-14-2-4-9-7-13-10z" class="AG"></path><path d="M770 440l-1-1c-1-3-4-5-6-7-4-4-9-7-13-10l-5-4h-1v-1l8 5c2 1 3 3 5 3l1 1 15 13c1 1 4 5 5 6l1 1v1c-1 0-1 0-1 1h0l-1 1-2-1c-2-3-3-5-5-8z" class="r"></path><defs><linearGradient id="g" x1="288.785" y1="240.74" x2="248.272" y2="238.503" xlink:href="#B"><stop offset="0" stop-color="#000100"></stop><stop offset="1" stop-color="#2f0202"></stop></linearGradient></defs><path fill="url(#g)" d="M251 174c0-2 0-3 1-5 10 10 15 25 19 37 0 1 2 7 2 7 1 4 1 8 0 12 2 1 2 1 2 3s0 3-1 5l2 3v1c0 3-1 5-2 8v2h1 0c1-1 2-2 4-2 1 1 2 1 3 2 1 0 3 3 3 3 1 3 2 6 1 8l-1 1v2l1 1h2c3 1 4 2 6 5 2 2 3 5 4 7 0 1 0 2-1 3l1 2c0 1-1 2-1 4s2 3 2 5c1 4 2 8 3 11l1 2h0v2 2l3-3c1 2 4 13 5 14 2 7 5 13 6 20l20 64c-1 0-2 1-3 1l1 2-1 1c-3 0-5 0-8-2l-6-2-2 1v-1h-1-4 0c-4-2-9-4-12-7-2-2-4-3-6-4-1-1-2-1-3-2l-2 1-1-1v-4-2l1-1c0-1-9-9-10-10l2-1-3-3v-1l-1-1c0-2-1-3-2-5-1-1-2-3-3-4h3c-3-3-6-3-7-8h-1c-1-2-2-3-3-5 0-1-1-1-1-2 0-2-1-3-2-5-2-4-7-10-8-15l-5-7-3-5c-2-2-2-3-3-5 0-1-2-3-3-4l-3-5 1-3-4-6v-4c-2-3 0-7-1-11h-1l3-6c2-2 3-5 5-6l1-2c2-1 3-4 5-6h2l2-3c2-2 3-5 4-8-1 0-1 0-2 1v-2h-1 0-1l-1-2c1-2 2-6 1-8h0c0-1-1-2-1-3-2-2-7-4-7-7-2 0-4-1-6-1h-2l-1-2c3 1 6 1 8 1l-1-1c0-1 4-3 5-4l-1-1h0l2-1c1-2 5-7 4-9 1-2 2-4 2-6 2-7 1-14-2-20v-1l1 1z"></path><path d="M248 217h0l1-1 2 2h2l1 1h0 0c0 1 0 2-1 3l-5-5z" class="I"></path><path d="M273 225c2 1 2 1 2 3s0 3-1 5l-2-4c-1-2 0-3 1-4z" class="K"></path><path d="M262 202v-3-2h1c1 2 2 4 2 7-1 1-1 2 0 4 0 2-1 5 0 6v1 2l-1-2v-3c0-3 0-7-1-10h-1z" class="m"></path><path d="M259 251v6c0 2 0 5-1 6s-2 1-2 2c0 2-1 3-2 4 0-5 3-13 5-18z" class="I"></path><path d="M250 174v-1l1 1c4 6 4 13 2 20h-1c2-7 1-14-2-20z" class="C"></path><path d="M262 202h1c1 3 1 7 1 10v3l1 2-1 1h-1c-1 1-1 1-2 0 0-4 1-7 2-11h-1c-1-1 0-4 0-5z" class="S"></path><path d="M260 248c0-2 0-4 1-5h0v2-1l1-2v-1c1 2 0 4 1 6 1 1 1 2 1 3 1 2 1 5 2 7 2 2 4 4 5 6v2l-3-3c-1-2-1-2-2-3-2-2-3-4-3-6s1-4 0-5h-1l-1 2v-2h-1z" class="v"></path><path d="M252 213l3-4c0 3-3 5-5 6l-1 1-1 1h0v3h-1c-2-1-4-1-5-2-2 0-4-1-6-1h-2l-1-2c3 1 6 1 8 1 5 0 8-1 11-3z" class="B"></path><path d="M262 207h1c-1 4-2 7-2 11 1 1 1 1 2 0h1c0 2-1 3-2 5 0 1 0 2-1 3s-2 2-4 2c0-2 0-3 1-4h-1l2-7v-1c0-2 2-7 3-9z" class="P"></path><path d="M259 216l1 1c0 1 0 2-1 4v3h2v-2l1 1c0 1 0 2-1 3s-2 2-4 2c0-2 0-3 1-4h-1l2-7v-1z" class="f"></path><path d="M257 224h1c-1 1-1 2-1 4 2 0 3-1 4-2 0 2-1 5-2 6 0 2-2 5-2 7l-1-1c-1 1-1 0-2 1-1 0-1 0-2 1v-2h-1 0c2-3 3-6 4-10 1-1 1-3 2-4z" class="u"></path><path d="M257 224h1c-1 1-1 2-1 4 2 0 3-1 4-2 0 2-1 5-2 6v-1l-3 2c-1-1 0-2 0-3s-1-1-1-2c1-1 1-3 2-4z" class="B"></path><path d="M252 194h1v1c0 5-3 9-5 13h1c1-1 2-1 3-2h0c0 1 0 2-1 3 0 1 1 3 1 4-3 2-6 3-11 3l-1-1c0-1 4-3 5-4l-1-1h0l2-1c1-2 5-7 4-9 1-2 2-4 2-6z" class="a"></path><path d="M248 217l5 5c1-1 1-2 1-3 1 1 1 1 1 2v4l1-2h0v-1l1-1c0-2 0-2 1-4h1l-2 7c-1 1-1 3-2 4-1 4-2 7-4 10h-1l-1-2c1-2 2-6 1-8h0c0-1-1-2-1-3-2-2-7-4-7-7 1 1 3 1 5 2h1v-3z" class="m"></path><path d="M248 217l5 5 1 3v2c-1 0-1 0-2-1-1-2-3-4-5-5v-1h1v-3z" class="f"></path><path d="M254 239c1-1 1 0 2-1l1 1c-4 16-9 33-7 50v7c1 6 3 11 5 16 0 1-1 1-1 1 0 1 2 2 1 3l-2-3-1-1 1-1h-1c-1 0-2 1-2 2h0c1 2 4 5 4 7l-5-7-3-5c-2-2-2-3-3-5 0-1-2-3-3-4l-3-5 1-3-4-6v-4c-2-3 0-7-1-11h-1l3-6c2-2 3-5 5-6l1-2c2-1 3-4 5-6h2l2-3c2-2 3-5 4-8z" class="j"></path><path d="M238 291l3 5-1 3-3-5 1-3z" class="B"></path><path d="M241 296c2 2 3 4 5 6l3 3c1 1 1 3 1 5v1h-1v-1c-1-1-2-2-3-2-2-2-2-3-3-5 0-1-2-3-3-4l1-3z" class="u"></path><path d="M246 250h2l2-3v2l-1 3c-1 2-3 3-4 4-2 2-3 5-5 7-1 2-4 4-5 6-1 0-1 2-1 2v10c-2-3 0-7-1-11h-1l3-6c2-2 3-5 5-6l1-2c2-1 3-4 5-6z" class="M"></path><path d="M246 250h2l2-3v2h0c-2 1-2 2-2 4-1 0-2 1-2 1-2 2-3 4-4 6l-1-1h1c0-1 0-2 1-2h-1l-1 1h-1 0l1-2c2-1 3-4 5-6z" class="u"></path><path d="M259 257h2c0 3 0 6-1 9 0 11 1 20 3 31 0 4 1 8 2 12 1 5 2 9 3 13l2 7h1l1-2c0 1 0 3 1 4 0 2 1 5 2 7 1 4 2 9 5 12v1c1 1 3 2 4 2 1 1 1 2 2 3v1c-2 0-3 0-4-1-2-1-3-2-5-3 1 2 2 3 4 4h0l-2 1c-1-1-3-2-3-3-3-3-6-3-7-8h-1c-1-2-2-3-3-5 0-1-1-1-1-2 0-2-1-3-2-5-2-4-7-10-8-15 0-2-3-5-4-7h0c0-1 1-2 2-2h1l-1 1 1 1 2 3c1-1-1-2-1-3 0 0 1 0 1-1-2-5-4-10-5-16 1 1 1 1 2 3v4c1 1 1 2 2 4v-4h0v-1c0-2 0-1-1-2v-1-1-1-2-3-7c0-5 0-10 1-16 1-1 2-2 2-4 0-1 1-1 2-2s1-4 1-6z" class="R"></path><path d="M272 327c0 1 0 3 1 4 0 2 1 5 2 7-1 0-1 0-2-1l-2-4h0l-1-4h1l1-2z" class="C"></path><path d="M254 277l2-2v-2 1c1 0 1 0 1 1h1v-2c0-1 1-2 1-3 0 3 1 11-1 14h0c-1-2-1-6-1-8-1 3-1 7-1 10-1-1-1-1-1-2v-1-1-6l-1 1z" class="B"></path><path d="M253 292h1c0 7 1 13 3 20 1 2 2 5 2 7h-1l-3-7c-2-5-4-10-5-16 1 1 1 1 2 3v4c1 1 1 2 2 4v-4h0v-1c0-2 0-1-1-2v-1-1-1-2-3z" class="S"></path><path d="M267 336v-1c-1-1 0-1-1-2h1c1 1 1 1 1 2 1 2 1 2 1 3l1 2h1v-1l-1-1-1-4c0-1-1-2-1-2l1-1c0 1 0 2 1 2v3c1 0 1 1 1 1h1 1c1 1 1 1 2 1 1 4 2 9 5 12v1c-6-4-9-9-13-15z" class="B"></path><path d="M259 257h2c0 3 0 6-1 9v-3h-1v7c0 1-1 2-1 3v2h-1c0-1 0-1-1-1v-1 2l-2 2v15h-1v-7c0-5 0-10 1-16 1-1 2-2 2-4 0-1 1-1 2-2s1-4 1-6z" class="Q"></path><path d="M261 325l6 11c4 6 7 11 13 15 1 1 3 2 4 2 1 1 1 2 2 3v1c-2 0-3 0-4-1-2-1-3-2-5-3 1 2 2 3 4 4h0l-2 1c-1-1-3-2-3-3-3-3-6-3-7-8 1 0 1 0 2 1h1 0l-1-3h0c-2-4-4-7-6-10-2-2-3-5-4-7l-1-2 1-1z" class="AC"></path><path d="M271 345c2 2 4 6 6 8 1 2 2 3 4 4h0l-2 1c-1-1-3-2-3-3-3-3-6-3-7-8 1 0 1 0 2 1h1 0l-1-3h0z" class="e"></path><path d="M254 320c0-2-3-5-4-7h0c0-1 1-2 2-2h1l-1 1 1 1 2 3c1-1-1-2-1-3 0 0 1 0 1-1l3 7 3 6-1 1 1 2c1 2 2 5 4 7 2 3 4 6 6 10h0l1 3h0-1c-1-1-1-1-2-1h-1c-1-2-2-3-3-5 0-1-1-1-1-2 0-2-1-3-2-5-2-4-7-10-8-15z" class="l"></path><defs><linearGradient id="h" x1="253.124" y1="279.971" x2="276.376" y2="286.529" xlink:href="#B"><stop offset="0" stop-color="#1e0000"></stop><stop offset="1" stop-color="#3b0e0f"></stop></linearGradient></defs><path fill="url(#h)" d="M261 250l1-2h1c1 1 0 3 0 5s1 4 3 6c1 1 1 1 2 3l3 3v-2c3 2 5 4 7 5l10-3v-3c3 1 4 2 6 5 2 2 3 5 4 7 0 1 0 2-1 3l1 2c0 1-1 2-1 4s2 3 2 5c1 4 2 8 3 11l1 2h0v2c-3 2-6 5-9 8l-1 1h0l-1 1c-1 1-3 1-5 2v1c-2 0-2 1-3 1l-3-2v1l-2-1c-1-1-2-1-4-2 0 3 1 6 0 9h0v-8h-1v4c0 3 0 6 1 9v4 1l-1-1h-1c-1-1-1-3-1-4l-1 2h-1l-2-7c-1-4-2-8-3-13-1-4-2-8-2-12-2-11-3-20-3-31 1-3 1-6 1-9h-2v-6l1-3h1v2z"></path><path d="M260 248h1v2 7h-2v-6l1-3z" class="S"></path><path d="M272 302c0-2 0-3 1-4 1-2 1-1 3-2-1 4-2 8-2 12v6 4c0 3 0 6 1 9v4 1l-1-1h-1c-1-1-1-3-1-4l-1 2h-1l-2-7h1v-3c-1-8-1-14-1-22l1 4 1 1c0 1-1 4 0 5 1-2 1-4 2-5z" class="F"></path><path d="M271 317c1 1 1 1 2 1h1c0 3 0 6 1 9l-2-3c-1-2-2-4-2-7z" class="N"></path><path d="M271 312s1 1 2 1c1-1 0-3 1-5v6 4h-1c-1 0-1 0-2-1v-5z" class="B"></path><path d="M269 319l3 8-1 2h-1l-2-7h1v-3z" class="a"></path><path d="M272 302c0-2 0-3 1-4 1-2 1-1 3-2-1 4-2 8-2 12-1 2 0 4-1 5-1 0-2-1-2-1 0-3 2-8 1-10z" class="P"></path><path d="M266 259c1 1 1 1 2 3l3 3c1 1 2 2 3 4 0 1 1 1 2 2 2 2 2 4 1 7h0l-1 2c-1 3-3 5-4 7-2 3-3 6-3 9-2-3-3-5-3-8l-1-2v-1c0-2 0-4 1-5v-1-1-1l-1-2c1-1 0-2 0-4h0v-6c0-2 1-3 1-5v-1z" class="Y"></path><path d="M270 269c1 2 2 3 3 4 0 3 1 8-1 11h0c1-2 2-3 4-4-1 3-3 5-4 7l-1-1h1l-1-1c0-1 0-2 1-3l-1-1v-4c0-1 0-1-1-2h0c-1-2-1-3-1-5l1-1z" class="D"></path><path d="M265 271l1-1 1 1 2-2h1l-1 1c0 2 0 3 1 5h0c1 1 1 1 1 2v4l1 1c-1 1-1 2-1 3l1 1h-1l1 1c-2 3-3 6-3 9-2-3-3-5-3-8l-1-2v-1c0-2 0-4 1-5v-1-1-1l-1-2c1-1 0-2 0-4z" class="m"></path><path d="M270 275h0c1 1 1 1 1 2-1 1-2 2-1 4v2c-1 0-2 1-3 2 0-1 0-1 1-2l-2-1c1-2 0-3 1-4l3-3z" class="S"></path><path d="M271 277v4l1 1c-1 1-1 2-1 3l1 1h-1l1 1c-2 3-3 6-3 9-2-3-3-5-3-8 0-1 1-2 1-3 1-1 2-2 3-2v-2c-1-2 0-3 1-4z" class="a"></path><path d="M288 262c3 1 4 2 6 5 2 2 3 5 4 7 0 1 0 2-1 3l1 2c-1-1-2-3-2-4-1-1-3 0-4 0-3 0-5 2-7 5l-3 3c-3 5-5 9-6 13-2 1-2 0-3 2-1 1-1 2-1 4-1 1-1 3-2 5-1-1 0-4 0-5l-1-1-1-4 1-1c0-3 1-6 3-9 1-2 3-4 4-7l1-2h0c1-3 1-5-1-7-1-1-2-1-2-2-1-2-2-3-3-4v-2c3 2 5 4 7 5l10-3v-3z" class="AX"></path><path d="M271 263c3 2 5 4 7 5l10-3 1 1c-1 2-3 2-5 3h1l1 2v-1l3-1 1 1c-1 1-1 1-1 3h-3l-1-1c-1 1-1 2-2 3h-1c0-1 0-3 1-4h0v-1c-1 0-2 1-2 1l-1 1c0-1-1-2-2-2s-1 0-2 1c-1-1-2-1-2-2-1-2-2-3-3-4v-2z" class="D"></path><path d="M276 271c1-1 1-1 2-1s2 1 2 2l1-1s1-1 2-1v1h0c-1 1-1 3-1 4h1c1-1 1-2 2-3l1 1c-2 2-3 3-4 5l3 2-3 3h-1l-2-1c-2 3-3 6-5 8 0-1 0-2 1-3 0 0 0-1 1-2 0-2 0-3 1-4h1c0-1 0-2-1-3h0c1-3 1-5-1-7z" class="P"></path><path d="M279 282c1-1 2-3 3-4l3 2-3 3h-1l-2-1z" class="Y"></path><path d="M277 278c1 1 1 2 1 3h-1c-1 1-1 2-1 4-1 1-1 2-1 2-1 1-1 2-1 3 2-2 3-5 5-8l2 1h1c-3 5-5 9-6 13-2 1-2 0-3 2-1 1-1 2-1 4-1 1-1 3-2 5-1-1 0-4 0-5l-1-1-1-4 1-1c0-3 1-6 3-9 1-2 3-4 4-7l1-2z" class="I"></path><path d="M277 278c1 1 1 2 1 3h-1c-1 1-1 2-1 4-1 1-1 2-1 2-1 1-1 2-1 3-2 4-3 8-4 12l-1-1-1-4 1-1c0-3 1-6 3-9 1-2 3-4 4-7l1-2z" class="t"></path><path d="M292 275c1 0 3-1 4 0 0 1 1 3 2 4 0 1-1 2-1 4s2 3 2 5c1 4 2 8 3 11l1 2h0v2c-3 2-6 5-9 8l-1 1h0l-1 1c-1 1-3 1-5 2v1c-2 0-2 1-3 1l-3-2v1l-2-1c-1-1-2-1-4-2 0 3 1 6 0 9h0v-8h-1v-6c0-4 1-8 2-12s3-8 6-13l3-3c2-3 4-5 7-5z" class="AY"></path><path d="M278 301c1-1 1-2 1-4 1 1 2 1 3 2s3 2 5 2v2h-1c-1-1-2-1-3-2-1 0-2-1-3 0v1h0v6l-1-1c-1-2-1-3-1-5v-1z" class="S"></path><path d="M287 301h0v-1c-1-2-1-1 0-2v-1c1 1 2 2 2 3v1c1 1 1 1 3 1l1-1v-4c2 1 3 5 4 6v1l1 1h0l1-2h0v2l4-4v2c-3 2-6 5-9 8l-1 1v-3c-1-1-1-2-2-3-2-1-3-2-4-3v-2z" class="Y"></path><path d="M285 280c2-3 4-5 7-5-8 7-12 14-15 23 0 2 1 2 1 3v1c0 2 0 3 1 5l1 1c1 2-2 3-1 5 0 1 1 1 2 2h0v1l-2-1c-1-1-2-1-4-2 0 3 1 6 0 9h0v-8h-1v-6c0-4 1-8 2-12s3-8 6-13l3-3z" class="F"></path><path d="M276 307l1-9c0 2 1 2 1 3v1c0 2 0 3 1 5l1 1c1 2-2 3-1 5 0 1 1 1 2 2h0v1l-2-1c-1-1-2-1-4-2l1-6z" class="m"></path><path d="M276 307l1-9c0 2 1 2 1 3v1 1c0 2 0 4-1 6h0l-1-2z" class="v"></path><path d="M280 302h0v-1c1-1 2 0 3 0 1 1 2 1 3 2h1c1 1 2 2 4 3 1 1 1 2 2 3v3h0l-1 1c-1 1-3 1-5 2v1c-2 0-2 1-3 1l-3-2h0c-1-1-2-1-2-2-1-2 2-3 1-5v-6z" class="AY"></path><path d="M280 302c2 1 3 2 4 3v1c0 1 0 1 1 3v3l1 1v2h1v1c-2 0-2 1-3 1l-3-2h0c-1-1-2-1-2-2-1-2 2-3 1-5v-6z" class="AK"></path><defs><linearGradient id="i" x1="298.504" y1="365.71" x2="322.797" y2="357.04" xlink:href="#B"><stop offset="0" stop-color="#120001"></stop><stop offset="1" stop-color="#320201"></stop></linearGradient></defs><path fill="url(#i)" d="M303 305l3-3c1 2 4 13 5 14 2 7 5 13 6 20l20 64c-1 0-2 1-3 1l1 2-1 1c-3 0-5 0-8-2l-6-2-2 1v-1h-1-4 0c-4-2-9-4-12-7-2-2-4-3-6-4-1-1-2-1-3-2l-2 1-1-1v-4-2l1-1c0-1-9-9-10-10l2-1-3-3v-1l-1-1c0-2-1-3-2-5-1-1-2-3-3-4h3c0 1 2 2 3 3l2-1h0c-2-1-3-2-4-4 2 1 3 2 5 3 1 1 2 1 4 1v-1c-1-1-1-2-2-3-1 0-3-1-4-2v-1c-3-3-4-8-5-12-1-2-2-5-2-7h1l1 1v-1-4c-1-3-1-6-1-9v-4h1v8h0c1-3 0-6 0-9 2 1 3 1 4 2l2 1v-1l3 2c1 0 1-1 3-1v-1c2-1 4-1 5-2l1-1h0l1-1c3-3 6-6 9-8v2z"></path><path d="M299 369l3 6-2 4-3-6c1-1 1-2 2-4z" class="AC"></path><path d="M299 338h1c1 1 1 2 1 3 1 1 1 2 2 3 2 1 2 4 3 6 2 2 3 4 3 7-5-2-4-5-8-8v-1c0-1 0-1-1-2 0-1-1-3-1-4s1-2 0-3v-1z" class="Y"></path><path d="M302 375c6 8 13 13 20 18v1c-1-1-1 0-2-1s-3-1-4-2c-2-1-3-1-4-2-5-3-9-6-12-10l2-4z" class="r"></path><path d="M300 322c0-1 1-2 2-3 1 1 0 1 1 2l1 1c1 1 1 2 1 3l1 1c3 3 3 7 6 10 1 1 1 1 1 2 1 2 1 3 2 4h1v1c-1-1-2-1-3-2l-1-1c-1-2-5-6-7-7h0c0-1-1-1-1-2-1-1-1-1-1-2-1-2-1-3-2-4l-1-3z" class="AY"></path><path d="M303 303v2l-1 1c-1 1-5 4-5 5v5c0 1 1 2 2 3s1 2 1 3l1 3c1 1 1 2 2 4 0 1 0 1 1 2 0 1 1 1 1 2h-1c-2-1-4-3-6-5-2-1-4-1-5-3s-2-3-4-4c-1 0-2-1-3-2h0l3-3h-2v-1c2-1 4-1 5-2l1-1h0l1-1c3-3 6-6 9-8z" class="S"></path><path d="M293 322c2-2 2-2 4-3l1 2-1 1h-4z" class="m"></path><path d="M289 316h0c1 0 1 0 2-1 1 0 2-1 3-2h1v1c-1 2-2 3-3 6v2h-1l-2-1c-1 0-2-1-3-2h0l3-3z" class="AK"></path><path d="M292 320l1 2h4v2c2 0 2 0 4 2v-1c1 1 1 2 2 4 0 1 0 1 1 2 0 1 1 1 1 2h-1c-2-1-4-3-6-5-2-1-4-1-5-3s-2-3-4-4l2 1h1v-2z" class="v"></path><path d="M303 305l3-3c1 2 4 13 5 14 2 7 5 13 6 20v3 1s0 1 1 1v2c-2-2-4-5-5-7h-1c-3-3-3-7-6-10l-1-1c0-1 0-2-1-3l-1-1c-1-1 0-1-1-2-1 1-2 2-2 3 0-1 0-2-1-3s-2-2-2-3v-5c0-1 4-4 5-5l1-1z" class="Y"></path><path d="M306 320l-2-1 1-2-2-2v-1c1 1 2 1 3 2-1 2-1 2 0 4z" class="v"></path><path d="M306 316c1 2 2 2 2 4h-1-1c-1-2-1-2 0-4z" class="m"></path><path d="M274 314h1v8h0c1-3 0-6 0-9 2 1 3 1 4 2l2 1v-1l3 2c1 0 1-1 3-1h2l-3 3h0c1 1 2 2 3 2 2 1 3 2 4 4h0c-2-1-4-2-5-3 0 6 1 12 2 18 2 3 3 8 6 11 1 2 3 4 4 6l2 1c-7 0-11-1-16-5h-2c-1 0-3-1-4-2v-1c-3-3-4-8-5-12-1-2-2-5-2-7h1l1 1v-1-4c-1-3-1-6-1-9v-4z" class="AY"></path><path d="M274 314h1v8h0c2 11 3 22 11 31h-2c-1 0-3-1-4-2v-1c-3-3-4-8-5-12-1-2-2-5-2-7h1l1 1v-1-4c-1-3-1-6-1-9v-4z" class="j"></path><path d="M281 315l3 2c1 0 1-1 3-1h2l-3 3h0c1 1 2 2 3 2 2 1 3 2 4 4h0c-2-1-4-2-5-3 0 6 1 12 2 18 2 3 3 8 6 11 1 2 3 4 4 6-3-1-9-7-11-10-4-4-8-9-8-14l1-1 2 2c1 2 2 4 4 6l3 4c-1-2-2-5-3-8-1-5-1-10-2-15l-3-3-2-2v-1z" class="C"></path><path d="M284 353h2c5 4 9 5 16 5l1 1c0 1 0 0-1 1-2 1-3 2-4 5l1 4c-1 2-1 3-2 4l3 6c3 4 7 7 12 10 1 1 2 1 4 2 1 1 3 1 4 2s1 0 2 1v-1l8 5c1 1 3 1 4 3h0l1 2-1 1c-3 0-5 0-8-2l-6-2-2 1v-1h-1-4 0c-4-2-9-4-12-7-2-2-4-3-6-4-1-1-2-1-3-2l-2 1-1-1v-4-2l1-1c0-1-9-9-10-10l2-1-3-3v-1l-1-1c0-2-1-3-2-5-1-1-2-3-3-4h3c0 1 2 2 3 3l2-1h0c-2-1-3-2-4-4 2 1 3 2 5 3 1 1 2 1 4 1v-1c-1-1-1-2-2-3z" class="X"></path><path d="M273 355h3c0 1 2 2 3 3 2 2 3 5 5 8 3 7 10 10 15 16h-1c-1-1-2-1-3-2l-5-5c-1-1-2 0-2-1-2 0-4-3-6-5l-3-3v-1l-1-1c0-2-1-3-2-5-1-1-2-3-3-4z" class="r"></path><path d="M280 370l2-1c2 2 4 5 6 5 0 1 1 0 2 1l5 5c1 1 2 1 3 2h1c1 0 1 0 2 1h1c0 1 1 1 1 2 1 1 1 2 2 2l-1 1 3 3c2 1 4 2 4 4-7-4-14-10-21-15 0-1-9-9-10-10z" class="c"></path><path d="M290 380c7 5 14 11 21 15 2 1 4 3 6 3 1 1 2 1 3 2l-2 1v-1h-1-4 0c-4-2-9-4-12-7-2-2-4-3-6-4-1-1-2-1-3-2l-2 1-1-1v-4-2l1-1z" class="Y"></path><path d="M303 385c2 0 2 1 4 2l1 1h1c1 1 1 1 3 1 1 1 2 1 4 2 1 1 3 1 4 2s1 0 2 1v-1l8 5c1 1 3 1 4 3h0l1 2-1 1c-3 0-5 0-8-2l-6-2c-1-1-2-1-3-2-2 0-4-2-6-3 0-2-2-3-4-4l-3-3 1-1c-1 0-1-1-2-2z" class="T"></path><path d="M311 395c0-2-2-3-4-4l-3-3 1-1c1 1 2 2 3 2 2 2 4 3 7 4 1 1 0 1 1 1l-1 1 1 1c0 1 1 1 1 2-2 0-4-2-6-3z" class="j"></path><path d="M316 394c3 2 10 6 13 4h1c1 1 3 1 4 3h0l1 2-1 1c-3 0-5 0-8-2l-6-2c-1-1-2-1-3-2 0-1-1-1-1-2l-1-1 1-1z" class="q"></path><path d="M326 402c0-1 0-1-1-2l1-1c2 1 4 1 6 2l1 1 1-1h0l1 2-1 1c-3 0-5 0-8-2z" class="E"></path><path d="M284 353h2c5 4 9 5 16 5l1 1c0 1 0 0-1 1-2 1-3 2-4 5l1 4c-1 2-1 3-2 4 0 0-1-1-1-2-2-2-2-5-2-8l-1-1h-1c-1 1-2 2-4 2h-2-1c0 1 0 1-1 2-2-3-3-6-5-8l2-1h0c-2-1-3-2-4-4 2 1 3 2 5 3 1 1 2 1 4 1v-1c-1-1-1-2-2-3z" class="AD"></path><path d="M298 365l1 4c-1 2-1 3-2 4 0 0-1-1-1-2 0-3 0-4 2-6z" class="r"></path><path d="M281 357c3 2 7 3 11 5-1 1-2 2-4 2h-2-1c0 1 0 1-1 2-2-3-3-6-5-8l2-1z" class="D"></path><path d="M687 409h3l4 1c3 1 5 2 7 6v1l1 4v4l-1 3v2l1 1h0c-1 1-1 1-1 2 0 2-3 6-5 8h1 3v1h-1s0 1-1 1-1 0-2 1-3 1-3 2l-1 1v1c-1 0-1 0-2 1h-1 0c-3 2-7 2-10 3l3 11c0 2 0 4-1 6h-1v4h0c1 3 3 5 5 7h-1v2 4 3h1v-1c1 1 1 1 3 1 1 0 3 2 4 3s1 2 2 3h2c0-2 1-4 3-5h1v-1h0c1 2 0 3-1 5l-2 2c-1 1-1 2-1 3v1l5 4 3 3h0l3 2v1l-3-2c-2 2-3 6-4 9l-3 15-1 9c0 1 1 2 0 3 1 1 1 3 2 5h0c0 3 1 7 2 9 1 4 2 7 5 9 1 1 2 1 4 1l1 2v1c-1 1-1 1-1 3 0 1-1 1-2 2l-4-4-4-2-1-1c1-1 1-2 1-3-2-2-3-3-4-5v1h-2-1l-1-1c-1-1-3-1-4-2l-3 1h-1l-2 3-3 7h-1l-5 5h0l-5 4-6 5-2-1c-1 0-3 2-5 3l-2 2v2l-2 2c-4 4-7 7-11 10-2 2-5 5-8 6l-1 3 1 1-7 6-5 6-8 11v1h2l-1 1h1 4c1 1 1 2 2 3v1c-5 0-8 2-11 6-3 3-4 9-5 13 0 5-1 10-1 14 3 6 8 10 14 13 4 1 10 1 14 1h1c5 0 8 0 11 4l6 4h4c2 0 3-2 4-3 0 2-1 4-2 6-3 1-5 3-7 4v2c-1-1-1-1-2-1 1-1 1-1 2-1l-1-1c-1 0-3 1-5 1h-7c-9 2-16 6-21 12-1 1-2 2-2 3-3 5-7 10-8 15-1 3-1 5-1 8v1c-1 3-1 4 0 7l-2-2c-1 11 4 26 11 34l2 2c3 0 4 2 6 4h0 0l9 8 9 6-2 1h1c0 1 0 1 1 1 0 1 1 1 2 2h1v1c2 1 4 2 6 2 0 0 1 1 2 1l4 1h2 1 9 1c3-1 5-2 8-2-13 5-28 6-41 0l-2 2c-6-2-13-5-18-9h1l-3-3c-2-1-4-2-5-3h1 0 1c1 1 1 1 2 1v-1l2 1v-1l-9-11c-1-1 0-1-1-2h-1v-1h-1l-2-3v-1c-1 0-1-1-2-2h0c-2-2-3-4-5-6h0c-2-2-2-5-3-8-1-2-3-4-4-7-2-4-3-8-4-13h0v-2c-1-1 0-1-1-2h-1 0v-1h1v-1l-3 3v-3l4-5-4-9c0-4 0-8 1-11 2-9 5-17 7-25l11-39 24-84 9-33c1-6 3-14 5-20l5-20 5-19c0-2 1-4 2-5l1-6 11-43 2-1c4-3 12-4 17-5 4-1 8-1 12-1z" class="x"></path><path d="M664 576v-1l1-1v-1-1l1-1c0-2 0-2 1-4h0v-1-2h-2l1-1c1 0 2 1 2 2h1c0-1 0-1 1-2s0-1 1-2h-2l1-1h1c1 1 1 1 1 2l-2 2v1 2h2c-2 1-3 2-4 5l-1 1h0l-3 3z" class="I"></path><path d="M618 605c0-1-1-2-1-3v-1l-1-3c-1-1-1-2 0-3 0 1 1 2 1 3v2l1 1c1 1 1 3 3 4h0c0-1 0-1 1-2v-1c0 1 0 2 1 3h1c1 0 2-1 3-2s2-3 4-4h0c-1 1-2 2-3 4l1 1h0l-5 6c-2-2-4-3-5-5h-1z" class="a"></path><path d="M672 567h0c1-1 1-2 1-3v-1c1 2 0 4-1 6l-1 1v1c-2 3-4 6-6 8 1-1 1 0 2-1 1-2 3-3 5-3h0l-5 4-6 5-2-1c3-3 8-6 9-10h-1 0l1-1c1-3 2-4 4-5z" class="D"></path><path d="M640 557c2-5 10-19 14-21 0 2-2 4-3 5l-6 12c-1 0-1 1-2 2 0 1 0 1-1 1h-1l-1 1z" class="U"></path><path d="M610 798c2 1 3 1 5 2 5 3 12 5 17 8l-2 2c-6-2-13-5-18-9h1l-3-3z" class="Ae"></path><path d="M602 741c-1 0-2 0-4 1h0l-1-1c1-1 1-2 2-3s1-1 1-2c1-2 1-1 1-2h0v-1l1-1v-1h0v-1l1-1v-2l-3 6h0c1-6 5-10 7-16h0v2l1-1h3c-3 5-7 10-8 15-1 3-1 5-1 8z" class="y"></path><path d="M618 606c2 2 3 4 5 6h2c2-4 5-7 9-9v1c-1 2-2 3-3 4l-1 3 1 1-7 6c0-1-1-2-1-3v-1l-1-1c-3 1-12 11-15 14 3-6 8-11 13-15v-2c-1-2-2-3-2-4z" class="D"></path><path d="M623 615c3-2 5-5 8-7l-1 3 1 1-7 6c0-1-1-2-1-3z" class="P"></path><path d="M642 692c-1 0-1 0-2-1s-2-1-3-2c-2-1-5 0-7 1h7l2 1-1 1c0-1-1-1-2-1h-1-6v-1h-8c-3 0-6-2-8-3v2c-3-2-6-4-8-7s-4-6-5-10l1-1 1 3c3 6 8 10 14 13 4 1 10 1 14 1h1c5 0 8 0 11 4z" class="w"></path><path d="M593 687h1c2 1 3 2 5 3 11 6 22 9 34 8 5 0 10-2 15-2h4c-1 1-2 2-4 2-7 0-14 2-21 3h-1c-9-1-17-4-25-7l-4 3h-1l3-4-6-6z" class="Ac"></path><path d="M631 535h0c2 2 4 2 6 3 5-4 9-8 14-11 2-1 4-3 6-2l1 1c-11 10-25 18-35 30 0-4 6-9 9-12 1-2 2-3 4-4-2-2-4-4-7-5-1 0-1 0-1-1 1 0 2 0 3 1zm30 28l6-13c0 3-1 6 0 9l-2 3v1c0 2-3 4-3 7v2l1-1h0c1 1 0 1 0 2v2l1 1 3-3h1c-1 4-6 7-9 10-1 0-3 2-5 3h0c-1 0-2 1-3 1h-1-1 0l2-5c4-5 7-8 10-14 1-1 2-2 2-4h0c1-1 0-2 0-3 0 1-1 1-2 2z" class="U"></path><path d="M623 614v1c0 1 1 2 1 3l-5 6-8 11v1h2l-1 1h1 4c1 1 1 2 2 3v1c-5 0-8 2-11 6-3 3-4 9-5 13v-2h-1c1-1 1-3 2-5l1-5v-2c-1-3 3-5 5-7-1-2-2-1-2-3l1-2 1-1c0-1 1-2 2-3v-1h0v-1c1-1 1-2 1-3l2-2 2-2 6-7z" class="Y"></path><path d="M617 621l-1 5h1l2-2-8 11-1 1c0-3 3-6 4-10 0-1 1-2 1-3l2-2z" class="S"></path><path d="M623 614v1c0 1 1 2 1 3l-5 6-2 2h-1l1-5 6-7z" class="C"></path><path d="M651 582l-2 5h0 1 1c1 0 2-1 3-1h0l-2 2v2l-2 2c-4 4-7 7-11 10-2 2-5 5-8 6 1-1 2-2 3-4v-1c-4 2-7 5-9 9h-2c-2-2-3-4-5-6v-1h1c1 2 3 3 5 5l5-6 5-6c3 0 5-4 7-5 1-1 2-1 3-3l2-2c1 0 2-1 2-2l3-3v-1z" class="P"></path><path d="M652 588v2l-2 2c-1 0-1 0-1-1l3-3z" class="U"></path><path d="M634 603c2-2 3-4 6-4v1l-1 2c-2 2-5 5-8 6 1-1 2-2 3-4v-1z" class="C"></path><path d="M627 529v1l-1 4h1 0c2-1 2 0 3 0l1 1c-1-1-2-1-3-1 0 1 0 1 1 1 3 1 5 3 7 5-2 1-3 2-4 4-3 3-9 8-9 12 0 1 0 1-1 2s1-1-1 1c0 1-1 1-1 2-1 1-1 1-2 1l9-33z" class="AY"></path><path d="M640 806h0c-2 0-3-1-5-2l-7-5-1-1c-6-4-12-10-17-16-4-6-9-13-11-21-1-5-1-11-1-16 1-1 2-2 4-3-1 3-1 4 0 7l-2-2c-1 11 4 26 11 34l2 2c3 0 4 2 6 4h0 0l9 8 9 6-2 1h1c0 1 0 1 1 1 0 1 1 1 2 2h1v1z" class="Ai"></path><defs><linearGradient id="j" x1="624.71" y1="796.903" x2="630.79" y2="793.597" xlink:href="#B"><stop offset="0" stop-color="#151922"></stop><stop offset="1" stop-color="#2d2f2f"></stop></linearGradient></defs><path fill="url(#j)" d="M613 783c3 0 4 2 6 4h0 0l9 8 9 6-2 1h1c0 1 0 1 1 1l-10-5c-3-3-6-6-9-8 0-1-2-3-3-4 0-1-1-2-2-3z"></path><path d="M656 693c0 2-1 4-2 6-3 1-5 3-7 4v2c-1-1-1-1-2-1 1-1 1-1 2-1l-1-1c-1 0-3 1-5 1h-7c-9 2-16 6-21 12-1 1-2 2-2 3h-3c1-1 1-1 1-2l6-6c-1 1-2 1-3 2v-1l2-2c2-1 2-1 4 0l5-4c-2 0-3 1-4 1h-1l-1-1c3 0 4-1 7-1v1c1-1 3-2 5-2h0v-1h-2c-1 0-1 0-2 1h0-10c-1 0-2 0-3-1h-1c-2 0-4 0-5 1s-1 2-1 3l2-2v1l1-1v1c-3 4-5 8-8 13h-1l2-4 2-4h0-2c0-2 1-3 1-3 1-1 1-2 2-2 0-1 1-2 2-3h0l-3-1c7-1 17 4 23 0h1c7-1 14-3 21-3 2 0 3-1 4-2 2 0 3-2 4-3z" class="s"></path><path d="M627 701c7-1 14-3 21-3-2 3-7 3-11 3-1 1-2 1-3 1-2 0-5 0-7-1z" class="w"></path><path d="M603 701c-1 1-1 0-2 0s-3 0-4 1c-2 2-4 5-5 8v1c0 3 2 11 0 13v-2c-1-2-2-5-3-8 0-1-1-4 0-5v-2s1-1 1-2v-3h0v-1l1-1c1-2 2-2 3-3l4-4c-2-2-10-9-10-11 1 1 2 3 4 4l1 1 6 6-3 4h1l4-3c8 3 16 6 25 7-6 4-16-1-23 0z" class="Aa"></path><path d="M660 547c-1-1-1-1 0-1 0-1 1-1 2-1 0 1 0 1 1 2v1c0 2 0 3-1 5-2 4-4 9-6 13-2 2-2 5-4 7-5 7-11 13-16 21 1 0 0 0 1-1l5-6 1-1c2-2 0 1 1-1l1-2 2-2h0l1-1 1-1c0-1 1-1 1-2l3-3v-1l2-3c1-2 3-4 5-6l1-1c1-1 2-1 2-2 0 1 1 2 0 3h0c0 2-1 3-2 4-3 6-6 9-10 14v1l-3 3c0 1-1 2-2 2l-2 2c-1 2-2 2-3 3-2 1-4 5-7 5l-5 6h0l-1-1c1-2 2-3 3-4l8-11c7-9 14-18 19-28 1-1 1-3 1-4l1-1-1-1c-1-1-1-1-1-2l-2 1h-1l1-1c1 0 1-1 2-1l1 1s0 1 1 2v-1-4-2h0z" class="v"></path><path d="M661 563c1-1 2-1 2-2 0 1 1 2 0 3h0c0 2-1 3-2 4-3 6-6 9-10 14v1l-3 3c0 1-1 2-2 2l-2 2c-1 2-2 2-3 3-2 1-4 5-7 5 9-11 19-21 26-34l1-1z" class="M"></path><path d="M632 509l5-20c2 1 2 2 3 4 0 1 1 1 1 2l3 3 5 4 2-2c2 2 4 3 6 5l1 2 4 4-1 2 5 6c2 2 6 5 7 7 0 1 0 1 1 2 2 3 6 10 5 14v3c0 1 0 1-1 2h1c0 1 0 2 1 3 0 1 1 2 1 4l-1 1v4h0v-2c-1-3-1-7-3-9-1-1-1-4-2-6-1-3-3-6-5-9l-8-10-5-4c-3-1-6-4-9-6s-6-4-10-6h0c-1 0-2 0-3-1-1 1-1 1-1 2-1 0-1 1-2 1z" class="c"></path><path d="M662 523v-1c2 0 4 3 6 4 0-1 0-2-1-3l3 3 2 3h-4l2 2v2l-8-10z" class="j"></path><path d="M651 500c2 2 4 3 6 5l1 2 4 4-1 2-12-11 2-2z" class="Y"></path><path d="M657 512c4 3 7 7 10 11 1 1 1 2 1 3-2-1-4-4-6-4v1l-5-4v-1l1-1 2 1h0l-3-6z" class="E"></path><path d="M632 509l5-20c2 1 2 2 3 4 0 1 1 1 1 2l3 3c0 2 3 4 5 6 3 2 5 6 8 8l3 6h0l-2-1-1 1v1c-3-1-6-4-9-6s-6-4-10-6h0c-1 0-2 0-3-1-1 1-1 1-1 2-1 0-1 1-2 1z" class="AX"></path><path d="M638 507c3 0 4 0 6 2 2 1 5 3 8 4 2 1 5 3 6 4l-1 1v1c-3-1-6-4-9-6s-6-4-10-6z" class="N"></path><path d="M660 547h0v2 4 1c-1-1-1-2-1-2l-1-1c-1 0-1 1-2 1l-1 1h1l2-1c0 1 0 1 1 2l1 1-1 1c0 1 0 3-1 4-5 10-12 19-19 28l-8 11h0c-2 1-3 3-4 4s-2 2-3 2h-1c-1-1-1-2-1-3 2-3 2-8 3-12 4-12 9-23 15-33l1-1h1c1 0 1 0 1-1 1-1 1-2 2-2h1c2-1 3-2 3-3l1-1h1l2 2c2-1 3-2 5-2l2-2z" class="B"></path><path d="M684 486v3h1v-1c1 1 1 1 3 1 1 0 3 2 4 3s1 2 2 3h2c0-2 1-4 3-5h1v-1h0c1 2 0 3-1 5l-2 2c-1 1-1 2-1 3v1l5 4 3 3h0l3 2v1l-3-2c-2 2-3 6-4 9l-3 15-1 9c0 1 1 2 0 3 1 1 1 3 2 5h0c0 3 1 7 2 9 1 4 2 7 5 9 1 1 2 1 4 1l1 2v1c-1 1-1 1-1 3 0 1-1 1-2 2l-4-4-4-2-1-1c1-1 1-2 1-3-2-2-3-3-4-5v1h-2-1l-1-1c-1-1-3-1-4-2l-3 1h-1l-2 3-3 7h-1c3-6 2-15 0-22 2 2 2 6 3 9v2h0v-4l1-1c0-2-1-3-1-4-1-1-1-2-1-3h-1c1-1 1-1 1-2v-3c1-4-3-11-5-14-1-1-1-1-1-2 2 2 3 4 5 5v-4c-1-5 0-10 1-14-1 0-1 1-1 1h-1c0-2 0-3-1-5l2-4h0 1v-2h1c0-2 0-2 2-4l1-7 1-6z" class="T"></path><path d="M684 519c0-3 0-5 1-8v5c0 1 0 1 1 2v2 3l-1 1c-1-1-1-4-1-5z" class="AB"></path><path d="M681 563v-1c0-3 0-5 1-8 0-2-1-4 0-6h1l1 4v6c1 0 2 1 3 1l-3 1h-1l-2 3z" class="An"></path><path d="M688 489c1 0 3 2 4 3s1 2 2 3v1l-4-1c-2 7-3 15-4 23-1-1-1-1-1-2v-5c0-7 1-15 3-22z" class="V"></path><path d="M696 495c0-2 1-4 3-5h1v-1h0c1 2 0 3-1 5l-2 2c-1 1-1 2-1 3v1c0 2 2 3 4 5h-1l-1 1-1 1c-1 0-1-1-2-1 0-1 0-2-1-3-1 1-1 2-1 3-2 2-4 3-4 6h0c-1 3-1 5-3 8v-2c1-8 2-16 4-23l4 1v-1h2z" class="AH"></path><path d="M684 486v3h1v4 1h0c0 3-1 5-1 7v4l-2 8c0 3 0 7-1 11v1l-1 1c-1-2 0-3 0-5-1 2-1 3-2 4v2c-1-5 0-10 1-14-1 0-1 1-1 1h-1c0-2 0-3-1-5l2-4h0 1v-2h1c0-2 0-2 2-4l1-7 1-6z" class="H"></path><path d="M678 505h0 1v-2h1c0-2 0-2 2-4l-3 14c-1 0-1 1-1 1h-1c0-2 0-3-1-5l2-4z" class="B"></path><path d="M689 512h0c0-3 2-4 4-6 0-1 0-2 1-3 1 1 1 2 1 3 1 0 1 1 2 1l1-1 1-1h1l1 2c0 1 0 1-1 2-2 4-3 9-5 13 0 2-1 4-1 5l-4 12-2 10c0 2 0 5-2 6-2 0-1-2-2-3v-9c-1-8-1-17 0-24 0 1 0 4 1 5l1-1v-3c2-3 2-5 3-8z" class="AA"></path><path d="M689 512h0c0-3 2-4 4-6 0-1 0-2 1-3 1 1 1 2 1 3 1 0 1 1 2 1v1c0 1-1 1-1 2-1 2-2 6-2 8l-4 14c0 1-2 7-3 8v-5c-1-4 0-8-1-12v-3c2-3 2-5 3-8z" class="F"></path><path d="M689 512c1 1 1 3 1 4l-1 2c0 1-1 3 0 5v-1l1-2c1 1 1 2 1 3-1 3-2 6-1 9 0 1-2 7-3 8v-5c-1-4 0-8-1-12v-3c2-3 2-5 3-8z" class="E"></path><path d="M696 500l5 4 3 3h0l3 2v1l-3-2c-2 2-3 6-4 9l-3 15-1 9c0 1 1 2 0 3 1 1 1 3 2 5h0c0 3 1 7 2 9 1 4 2 7 5 9 1 1 2 1 4 1l1 2v1c-1 1-1 1-1 3 0 1-1 1-2 2l-4-4-4-2-1-1c1-1 1-2 1-3-2-2-3-3-4-5v1h-2-1l-1-1c-1-1-3-1-4-2-1 0-2-1-3-1v-6c1 1 0 3 2 3 2-1 2-4 2-6l2-10 4-12c0-1 1-3 1-5 2-4 3-9 5-13 1-1 1-1 1-2l-1-2c-2-2-4-3-4-5z" class="H"></path><path d="M694 527c0 3 0 6-1 9l-2 10s-1 1-1 2c-1 2-2 4-2 7h-2c2-1 2-4 2-6l2-10 4-12z" class="T"></path><path d="M700 562c-1-1-1-2-2-3l-1-6-1-1v-3l-1-1c0-2 0-3 1-4 1 1 1 3 2 5h0c0 3 1 7 2 9 1 4 2 7 5 9 1 1 2 1 4 1l1 2v1c-1 1-1 1-1 3 0 1-1 1-2 2l-4-4-4-2-1-1c1-1 1-2 1-3v-1c0-1 0-2 1-3z" class="AF"></path><path d="M700 562c0 2 1 3 1 4s0 2 1 3c0 1 0 1 1 3l-4-2-1-1c1-1 1-2 1-3v-1c0-1 0-2 1-3z" class="AD"></path><path d="M687 409h3l4 1c3 1 5 2 7 6v1l1 4v4l-1 3v2l1 1h0c-1 1-1 1-1 2 0 2-3 6-5 8h1 3v1h-1s0 1-1 1-1 0-2 1-3 1-3 2l-1 1v1c-1 0-1 0-2 1h-1 0c-3 2-7 2-10 3l3 11c0 2 0 4-1 6h-1v4h0c1 3 3 5 5 7h-1v2 4l-1 6-1 7c-2 2-2 2-2 4h-1v2h-1 0l-2 4c1 2 1 3 1 5h1s0-1 1-1c-1 4-2 9-1 14v4c-2-1-3-3-5-5-1-2-5-5-7-7l-5-6 1-2-4-4-1-2c-2-2-4-3-6-5l-2 2-5-4-3-3c0-1-1-1-1-2-1-2-1-3-3-4l5-19c0-2 1-4 2-5l1-6 11-43 2-1c4-3 12-4 17-5 4-1 8-1 12-1z" class="AK"></path><path d="M672 470c1-3 1-6 3-8l2 2c-1 0-1 2-2 3l-2 2-1 1z" class="D"></path><path d="M679 458l2 5c-2 1-2 1-4 1l-2-2c2-1 3-2 4-4z" class="I"></path><path d="M657 460c2-1 7-3 10-3h1l-15 7v-1c1-1 2-2 4-3z" class="P"></path><path d="M661 496c1 3 1 5 1 9 0 2 1 4 1 6v-3-1-2c-1-3-1-5-1-7 1-1 1-5 1-7h0c0 6 0 12 1 18 0 3 0 5 2 7l1 2-1 1-5-6 1-2v-1-4l-1-10z" class="a"></path><path d="M663 491c1-11 3-21 7-30h0c0 5-2 11-3 16-3 13-2 25 0 39h-1c-2-2-2-4-2-7-1-6-1-12-1-18z" class="E"></path><path d="M652 459l3-1c1-1 1-1 2-1h1l-1 3c-2 1-3 2-4 3v1l-7 4c-1 1-2 3-3 4v1c0-2 0-2-1-3 0-2 1-4 2-5l1-6 1 1c1 0 3-1 4-2l2 1z" class="I"></path><path d="M645 459l1 1c1 0 3-1 4-2l2 1c-3 2-5 4-8 6l1-6zm6 41l-2-2v-5c1 1 0 2 0 3 1 2 1 2 2 3v-1c0-1 1-2 1-3h0v-1-3c-1-2 0-1 1-2v-2l-1-1h1c1-3 3-5 4-7h1l1 2h1l1 1-1 7 1 7 1 10v4 1l-4-4-1-2c-2-2-4-3-6-5z" class="Q"></path><path d="M657 505v-8h-1v-1c-1-2-1-4-1-6 1 1 1 0 2 1l1-4h1l1 2 1 7 1 10v4 1l-4-4-1-2z" class="P"></path><path d="M658 507c1-1 1-2 2-2l1-1s0 1 1 2v4 1l-4-4z" class="f"></path><path d="M681 463l-1 6v4h0c1 3 3 5 5 7h-1v2 4l-1 6-1 7c-2 2-2 2-2 4h-1v2h-1 0l-2 4c1 2 1 3 1 5h1s0-1 1-1c-1 4-2 9-1 14v4c-2-1-3-3-5-5-1-2-5-5-7-7l1-1-1-2h1c1 0 2 1 2 2l1 1c1-2 0-7-1-10-2-6-2-13-1-19l1-9c1-4 2-8 3-11l1-1 2-2c1-1 1-3 2-3 2 0 2 0 4-1z" class="V"></path><path d="M681 463l-1 6v4h0c1 3 3 5 5 7h-1l-2-1h0c0-1-1-2-1-3-1-2-2-3-2-4l-1-2c-1 2 0 2-1 3 0 1-1 2-1 3h-1-1l1-1-1-2c-1 1-2 4-2 5v1 3c0-1-1-2 0-3-1-2 0-3 0-4 0 1-1 2-1 3s-1 2-2 3c1-4 2-8 3-11l1-1 2-2c1-1 1-3 2-3 2 0 2 0 4-1z" class="B"></path><path d="M676 509v1c0 2-1 3-2 4-1-1-3-7-3-9v-3h-1c1-3 0-5 1-8 1-2 2-5 2-7 0-3 2-5 3-8 0-1 0-2 1-3v-1c1-1 1-2 2-3 0 1 1 2 2 4 0 1 1 2 1 3h0l2 1v2 4l-1 6-1 7c-2 2-2 2-2 4h-1v2h-1 0l-2 4z" class="AA"></path><path d="M682 479l2 1v2 4l-1 6h-1 0-1v-2l1-11z" class="I"></path><path d="M678 505c0-5 1-10 3-15v2h1 0 1l-1 7c-2 2-2 2-2 4h-1v2h-1 0z" class="C"></path><defs><linearGradient id="k" x1="658.237" y1="415.738" x2="676.964" y2="442.392" xlink:href="#B"><stop offset="0" stop-color="#190101"></stop><stop offset="1" stop-color="#420504"></stop></linearGradient></defs><path fill="url(#k)" d="M687 409h3l4 1c3 1 5 2 7 6v1l1 4v4l-1 3v2l1 1h0c-1 1-1 1-1 2 0 2-3 6-5 8h1 3v1h-1s0 1-1 1-1 0-2 1-3 1-3 2l-1 1v1c-1 0-1 0-2 1h-1 0c-3 2-7 2-10 3l3 11c0 2 0 4-1 6h-1l1-6-2-5-1-2c-3-1-7 0-10 1h-1c-3 0-8 2-10 3l1-3h-1c-1 0-1 0-2 1l-3 1-2-1c-1 1-3 2-4 2l-1-1 11-43 2-1c4-3 12-4 17-5 4-1 8-1 12-1z"></path><g class="Q"><path d="M668 446c0-1-2-2-2-3v-1c-1-2-1-3-1-5 1-2 3-6 5-6h1l-1-2h0c2-1 2-1 4-1v-1h1c1 0 1-1 2-1l1 1c1-1 2-1 3-2h3c-2 1-4 1-5 3-1 1-2 2-3 2-3 1-4 3-5 6v2 1c-1 1-1 2-1 4l2 3h-4z"></path><path d="M685 425c0-1 1-1 0-2-2-1-2 0-3 0v2l-1-1c0-1-1 0-2 0-2 1-6 2-8 2 1-1 3-1 4-2l2-1c2 0-1 1 1 0s4-1 5-2h1 1l1-1c1 0 1-1 2-1l1-1c1 1 0 1 1 1h3c1 1 2 1 3 1l2 3h2l2-2v4l-1 3h-1l-1 2-1-3v-1l-1-1h-7-4-1z"></path></g><path d="M686 425v-1c2-1 3-1 4-1 0 1 1 1 0 2h-4z" class="a"></path><path d="M702 421v4l-1 3h-1c-1-1-1-1-1-2s0-2-1-3h2l2-2z" class="I"></path><path d="M690 425h7l1 1v1l1 3h-1-1-1c-2-1-3-1-4-3-1 1-3 2-4 2s-2 1-3 1h-1c-1 0-2 1-2 1h-1c-1 0-2 1-4 2h0v1l-1 1c-3 2 1-1-1 1s-3 2-3 4v2c1 0 1 0 2 1 1 0 2 1 4 1-2 1-4 1-6 2l-2-3c0-2 0-3 1-4v-1-2c1-3 2-5 5-6 1 0 2-1 3-2 1-2 3-2 5-3h1 1 4z" class="U"></path><path d="M692 427h2 1 3l1 3h-1-1-1c-2-1-3-1-4-3zm-35 28l9-3v-1l-6-2c-2-2-4-4-4-7v-2-9-1c1-1 1-2 2-3l-1 6c0 5 2 9 5 12l1 2c1 2 3 2 5 3l9 3 1 3c-3-1-7 0-10 1h-1c-3 0-8 2-10 3l1-3h-1c-1 0-1 0-2 1l-3 1-2-1c3-1 4-2 7-3z" class="AL"></path><path d="M657 455c1 1 2 1 3 0l6-1c1 0 2 0 3-1v1l-2 3c-3 0-8 2-10 3l1-3h-1c-1 0-1 0-2 1l-3 1-2-1c3-1 4-2 7-3z" class="C"></path><path d="M681 431h1s1-1 2-1h1c1 0 2-1 3-1s3-1 4-2c1 2 2 2 4 3h1 1l-1 4c0 1-1 1-1 2-2 1-3 3-6 4-2 3-9 4-12 4-2 0-3-1-4-1-1-1-1-1-2-1v-2c0-2 1-2 3-4s-2 1 1-1l1-1v-1h0c2-1 3-2 4-2z" class="R"></path><path d="M682 438c0-1-1-2-1-3 1-1 3-2 5-2h5v1l-1 1-8 3z" class="F"></path><path d="M674 443c-1-1-1-1-2-1v-2c0-2 1-2 3-4s-2 1 1-1l1-1v-1h0c2-1 3-2 4-2l-2 3h-1l-1 2h1-1c-1 1-1 2-2 4l1 1 2-1h3c-1 0-1 1-3 1h0c-2 0-3 1-4 2z" class="f"></path><path d="M686 433c1 0 1-1 2-1 1-1 1-2 2-2h1l-1-1h2 0c2 1 3 1 5 1h1l-1 4c0 1-1 1-1 2-2 1-3 3-6 4h-1c1-1 2-1 3-1 0-1 1-2 1-2-1 0-2 1-3 1l2-2c1 0 1 0 1-1h-3l1-1v-1h-5z" class="E"></path><path d="M690 435h3c0 1 0 1-1 1l-2 2c1 0 2-1 3-1 0 0-1 1-1 2-1 0-2 0-3 1h1c-2 3-9 4-12 4-2 0-3-1-4-1 1-1 2-2 4-2h0c2 0 2-1 3-1 0-1 1-1 1-2l8-3z" class="AL"></path><path d="M700 428h1v2l1 1h0c-1 1-1 1-1 2 0 2-3 6-5 8h1 3v1h-1s0 1-1 1-1 0-2 1-3 1-3 2l-1 1v1c-1 0-1 0-2 1h-1 0c-3 2-7 2-10 3l3 11c0 2 0 4-1 6h-1l1-6-2-5-1-2-1-3-9-3c-2-1-4-1-5-3l4 1 2-1-1-1h4c2-1 4-1 6-2 3 0 10-1 12-4 3-1 4-3 6-4 0-1 1-1 1-2l1-4h1l1-2z" class="AG"></path><path d="M700 428h1v2c-1 2-2 5-4 8l-1-2c0-1 1-1 1-2l1-4h1l1-2z" class="D"></path><path d="M690 440c3-1 4-3 6-4l1 2c-4 6-15 9-23 10-2 1-5 1-7 0l2-1-1-1h4c2-1 4-1 6-2 3 0 10-1 12-4z" class="a"></path><path d="M163 186c-1 0-2-1-3-2-6-5-10-12-10-20-2-17 5-38 20-48 5-4 11-6 16-11v-1l248-1c1 2 3 5 5 6 11 9 28 11 36 24 2 2 3 4 4 6 3 7 4 14 2 22-2 6-5 13-11 16v-1l2-3h-2c1-2 2-5 3-7 1-6 0-13-3-17-5-7-11-9-18-10-12 0-21 3-31 9l-1 1c-2-2-3-5-5-6-10 3-17 12-24 20-1 1-2 1-3 3l-1 1c-1 2-2 4-4 6 0 2-1 3-2 4v1h-1c-2 3-2 6-3 9l-1 1-5 5h0c-2 1-2 2-3 2v1h-1l-3 2c-1 0-1 1-2 1h-1l1-2c1-4 4-6 6-9 1-4 3-9 4-12l1-4-1-2-2 6-4-12c-1-1-2-3-3-5l-1-1c-6-10-18-14-28-18l-2-1h-1l-4-1h-3 0l-1 1 1 1-1 1h1 0c0 1 1 2 1 4-1 0-1 0-2 1h-2v2 1c-1 0-2 1-3 1-3 2-5 5-7 9-1 4-1 5-1 9l1-1h0c-1 2-1 8-3 9v1h2c0 1 1 1 1 2v3h-1v2c-1-1-1-1-1-3h-1v3c0 1 0 1-1 1l-1 1c0 1 0 0 1 1s4 3 5 4v1l1 1 3 5c1 1 1 1 1 2s1 2 2 3h0l3 4c3 2 4 3 4 7v2l-1 1v2c-1 2-1 4-1 6 1 0 1-1 1-1h1c-1 2-3 4-5 6-3 5-6 11-7 17v-3-1h0v-1c-1 1-1 2-2 2 0 1 0 2-1 2-1 7-9 13-4 21l3 3 1 1c2 1 3 2 5 4v1c-1 0-2 0-3-1l-1 1c0 2 0 2 1 4l-2 3v2h-1c-1-2-1-4-1-6-1-4-4-8-6-11s-3-7-5-10c0 0-1-2-2-2-1-1-2 0-3-1-3-2-5-4-8-5 0 0-2-3-3-3-1-1-2-1-3-2-2 0-3 1-4 2h0-1v-2c1-3 2-5 2-8v-1l-2-3c1-2 1-3 1-5s0-2-2-3c1-4 1-8 0-12 0 0-2-6-2-7-4-12-9-27-19-37-2-2-6-6-9-8 0-1-4-3-4-3l-2-2c-7-4-13-7-20-10-2 0-4-2-6-2l-5 4 3-5c-11-4-24-5-36 0-6 4-10 7-13 14-2 7-2 15 2 21 2 3 4 5 7 7-2 0-4 0-6-2 1 1 2 3 4 4-2-1-1 0-2-1h-2z" class="AP"></path><path d="M365 110l2-1h2c2-1 8-1 10 1h-11-3z" class="J"></path><path d="M249 147c-1-1-2-2-3-4 0-1 0-3 1-4 1-2 7-4 9-5h0c-1 1-3 2-4 2-1 1-3 2-4 3 0 3 1 5 2 8h0-1z" class="g"></path><path d="M185 122v1c1 0 2-1 3-1h0l1 2h0c3 1 4 1 6 3-3 0-7 0-10-2-1 0-1-1-1-1l1-2z" class="O"></path><path d="M185 122v1c1 0 2-1 3-1h0l1 2h0c-1 1-2 1-3 1h-1c-1 0-1-1-1-1l1-2z" class="b"></path><path d="M209 117c4-2 7-4 10-5l2 1c-3 1-5 1-7 3l-9 8-2-1s-1 1-2 1v-2c1-1 3-1 4-2s3-2 4-3zm170-7c2 0 13-1 15 0l2 1c-4 0-10-1-14 1-4-1-11 0-14-2h11z" class="W"></path><path d="M260 109c5-1 9 0 13 0v1c-1 0 0 0-1 1l1 1-10 1-3-1 2-2h-2v-1z" class="J"></path><path d="M194 124l1-1c2 0 4-1 6-1v2c1 0 2-1 2-1l2 1-3 2c-2 1-4 1-6 1h-1c-2-2-3-2-6-3h0 5z" class="h"></path><path d="M194 124l1-1c2 0 4-1 6-1v2c1 0 2-1 2-1l2 1-3 2-1-1c-1 1-1 1-3 1 1-1 1-1 1-2h-5z" class="G"></path><path d="M318 120c-3-3-7-5-11-7 2 0 3 1 5 1h15v1c-1 0-2 1-3 1-1 1-1 1-2 1h-1c-1-1-3 0-4 0l1 2v1z" class="d"></path><path d="M276 212c1 1 1 3 1 4 0 2 0 4-1 7v2s0 1 1 1c0 3 0 8-1 10l-2-3c1-2 1-3 1-5s0-2-2-3c1-4 1-8 0-12 1 1 1 2 2 2 0 2-1 3 1 5h0c1-2 0-3 0-5v-2-1z" class="Z"></path><path d="M241 153c2 0 3 1 4 2 2 1 4 5 8 5l1 6c-1-1-5-3-7-4 0-1-1-2-1-3h0c-1-1-1-2-2-3 0-1-2-1-3-3z" class="W"></path><path d="M282 223h1c2-3 3-7 4-10h1c1 1 1 2 1 4l-1 3c-3 3-9 9-9 13h0c0-3 2-8 3-10h0z" class="b"></path><path d="M203 113l1 1-6 4c-4 2-6 3-10 4h0c-1 0-2 1-3 1v-1c4-4 12-7 18-9z" class="p"></path><path d="M282 247c1 0 2-1 3 0 3 3 7 5 12 5h1l5-2c0 2 0 2-1 2-2 1-2 3-3 4h0l-1-2c-1 0-2 1-2 1-2 0-2-1-3 0-3-2-5-4-8-5 0 0-2-3-3-3z" class="h"></path><path d="M292 201l1 2c-1 5-2 9-4 14 0-2 0-3-1-4h-1c-1 3-2 7-4 10h-1l6-15h0l1-2c2-2 2-3 3-5z" class="O"></path><path d="M260 109v1h-6c-4 1-8 0-13 0-7 0-13 1-20 3l-2-1-1-1c1 0 1 0 2-1h-2c1-1 3 0 4-1 3 0 7 1 10 1 10-2 19 1 28-1z" class="L"></path><defs><linearGradient id="l" x1="191.561" y1="118.118" x2="204.338" y2="122.668" xlink:href="#B"><stop offset="0" stop-color="#cac3c3"></stop><stop offset="1" stop-color="#e9e8e1"></stop></linearGradient></defs><path fill="url(#l)" d="M209 116v1c-1 1-3 2-4 3s-3 1-4 2c-2 0-4 1-6 1l-1 1h-5l-1-2c4-1 6-2 10-4h0c2 1 3 0 5-1l1 1c2 0 3-1 5-2z"></path><path d="M299 184c1-1 1-2 3-3s5-2 7-1c1 0 1 2 1 2v2c-1-1-1-1-1-3h-1v3c0 1 0 1-1 1l-1 1c0 1 0 0 1 1 0 1-1 1 0 2l2 4c-2-1-2-2-3-3h-1-1l-3-6-1 1-1-1z" class="AJ"></path><path d="M181 113c1-1 2-2 3-2l1-1h2c1-1 4-1 6-1 1-1 3 0 5 0h0v-1h2 14 6c4 2 8-1 12 2-3 0-7-1-10-1-1 1-3 0-4 1h-6-19c-3 0-6 0-9 1l-3 2z" class="J"></path><path d="M394 110l24-1c4 0 10-2 14-2 1 0 1 0 2 1v2c-2 1-4 0-6 0h-14l-18 1-2-1z" class="L"></path><path d="M193 110h19c-3 2-9 2-9 2-4 0-8 0-12 2-6 2-12 6-18 9 3-4 10-6 13-9 3-2 3-3 7-4z" class="h"></path><path d="M212 110h6 2c-1 1-1 1-2 1l1 1c-3 1-6 3-10 5v-1c-2 1-3 2-5 2l-1-1c-2 1-3 2-5 1h0l6-4-1-1c1 0 1 0 2-1h-2s6 0 9-2z" class="G"></path><path d="M218 111l1 1c-3 1-6 3-10 5v-1c-2 1-3 2-5 2l-1-1c1 0 1-1 2-2 2-2 5-2 7-3l6-1z" class="K"></path><path d="M205 115h1c1 0 3-1 4 0l-1 1c-2 1-3 2-5 2l-1-1c1 0 1-1 2-2z" class="o"></path><path d="M314 148l1 1c1-1 1-1 3-1 0 0 1-1 2-1l1-1v2 1c-1 0-2 1-3 1-3 2-5 5-7 9-1 4-1 5-1 9l1-1h0c-1 2-1 8-3 9v1l-2-1c-1-3 0-5 1-8 1-7 4-14 7-20z" class="AT"></path><path d="M307 168l2-2v3l-1 2v5 1l-2-1c-1-3 0-5 1-8z" class="p"></path><path d="M213 128l-6-1 4-4c1-1 2-2 2-1 2 0 4-1 5-2 3-1 6-2 9-2 2 1 4 0 5 1-3 2-11 3-13 5l-2 2-3 1-1 1z" class="k"></path><path d="M214 127c-1 0-2-1-3-2l1-1h4 0l1 2-3 1z" class="Ab"></path><defs><linearGradient id="m" x1="359.571" y1="108.642" x2="374.313" y2="115.538" xlink:href="#B"><stop offset="0" stop-color="#d0c1be"></stop><stop offset="1" stop-color="#e6e6e1"></stop></linearGradient></defs><path fill="url(#m)" d="M365 110h3c3 2 10 1 14 2-2 0-4 0-5 2h0-3-1l-1 1c-7-1-14-1-20 0-1 0-1 0-1-1-1 0-3 0-4-1l18-3z"></path><path d="M260 112l3 1c-1 0-2 0-3 1-2 1-5-1-7 0 1 1 1 1 2 1 4 1 8 1 12 3l7 4 2 2c-1 0-2-1-2-1-3-2-7-5-10-5 0 0 0 1 1 1 2 1 4 2 5 3v1c-9-6-17-8-29-8-2-1-5-1-8-1l-1 1c-1 0-1 0-2-1 1 0 1 0 2-1h1c4-1 8-1 12-1 2 1 5 1 7 1 3-1 6-1 8-1z" class="K"></path><path d="M282 131l1-2c2-1 2-2 3-3 0 2-1 5 0 7s2 3 3 4c2 2 6 4 8 5 2 3 4 6 7 8v1 1 3c1-1 1 0 1-1l2-1h0l-3 5-3-5c-2 0-2-1-3-2v-1l1-1c-1-2-3-3-4-5l-1-1c-3-3-6-5-9-8-1-1-3-3-3-4z" class="n"></path><path d="M295 144c3 1 5 3 7 6 1 1 0 2 0 3h-1c-2 0-2-1-3-2v-1l1-1c-1-2-3-3-4-5z" class="i"></path><path d="M325 126l3-1c-1 2-3 4-3 6h1c-2 2-3 4-3 6l-1 1h2l-1 1 1 1-1 1h1 0c0 1 1 2 1 4-1 0-1 0-2 1h-2l-1 1c-1 0-2 1-2 1-2 0-2 0-3 1l-1-1 1-2c3-7 6-14 10-20z" class="p"></path><path d="M318 142h0v-1c1-3 2-3 4-3h2l-1 1 1 1-1 1h1 0c-2 1-4 1-6 1z" class="o"></path><path d="M318 142c2 0 4 0 6-1 0 1 1 2 1 4-1 0-1 0-2 1h-2l-1 1c-1 0-2 1-2 1-2 0-2 0-3 1l-1-1 1-2 3-2v-2z" class="k"></path><path d="M372 115l1-1h1 3l4 1c5 0 9 0 14 1 1 2 1 2 0 4l1 1c-1 1-1 2-2 3l-1 1c-1-1-2-3-3-4-2 0-4-1-5-1h-1l1 1v1s-3-3-4-3c-3-2-6-3-9-4z" class="W"></path><path d="M381 115c5 0 9 0 14 1 1 2 1 2 0 4l-1-1h-2c-1-1-4-1-5 0-2 0-5-1-6-3v-1z" class="AJ"></path><path d="M318 119c1 1 1 2 2 3l1 1c1-2 3-4 5-5l1-1 2-1 1-1c4-2 9-3 13-5-11 1-25 1-35 0-3-1-5 0-7-1-2 0-9 1-10 0 2-1 5-1 8-1l26 1h13c6 0 12-1 17 0h1c-3 0-6 0-9 1-10 2-19 7-27 15v-1c-1-1-1-3-2-4v-1z" class="i"></path><path d="M163 183c-4-4-7-8-8-14-2-9 1-21 6-29 2-2 6-7 9-8-7 7-11 14-13 24-1 2-1 5-1 7h1c0 2 0 5 1 6 1-4-1-8 2-12h0c-2 7-2 15 2 21 2 3 4 5 7 7-2 0-4 0-6-2z" class="k"></path><path d="M181 113l3-2c3-1 6-1 9-1-4 1-4 2-7 4-3 3-10 5-13 9h0c-2 1-4 3-5 4-4 2-6 4-9 8h0 0c-1-1 3-5 4-7h0c0-1 0-1 1-1 0-2 1-3 2-4 5-5 10-7 15-10z" class="Z"></path><path d="M215 138c2-2 4-2 7-2 8 2 18 5 23 12h-1c-2 0-4-1-6-3-1 0-2-1-3-1-2 0-4-1-6-2h-12l-5-1 3-3z" class="k"></path><path d="M215 138c2 0 5 0 8 1 1 0 3 0 4 1l3 1c-3 0-5-1-7 0-2 0-4 0-6 1l-5-1 3-3z" class="Ad"></path><path d="M258 159c2 2 4 3 7 3l2 1h3v1c-2-1-3-1-5-1 1 2 6 2 8 3 1 0 1 1 2 1v1l-10-2c1 4 4 6 7 8l-1 1-2-3-1 1 1 2v1h-1l-1-3v1c0 1 1 3 1 5 1 2 2 6 3 8 1 4 4 9 4 12 1 6 4 12 3 18 0-1 0 0-1-1 0-1 0-3-1-4-1-6-2-11-4-17-4-12-8-25-14-36z" class="p"></path><path d="M268 179l-3-6c0-3-2-5-2-8 1 0 1 0 2 1 1 4 4 6 7 8l-1 1-2-3-1 1 1 2v1h-1l-1-3v1c0 1 1 3 1 5z" class="g"></path><path d="M273 112c1 0 2 0 3 1 2-1 3 0 4 0 1 1 2 1 3 2l-1 1h-1-1c-1 3-3 6-3 8h-1l-2-2-7-4c-4-2-8-2-12-3-1 0-1 0-2-1 2-1 5 1 7 0 1-1 2-1 3-1l10-1z" class="W"></path><path d="M255 115c8-1 16 0 23-1 1 0 2 0 3 1l1 1h-1c-3 0-11 0-13 1l-1 1c-4-2-8-2-12-3z" class="AO"></path><path d="M267 118l1-1c2-1 10-1 13-1h-1c-1 3-3 6-3 8h-1l-2-2-7-4z" class="g"></path><path d="M274 122l1-1c1-1 0-2-1-3 2-2 4-1 6-2-1 3-3 6-3 8h-1l-2-2z" class="G"></path><path d="M282 122c2-3 4-4 8-6h0c6-2 15 0 20 3 3 1 5 4 6 8 1 3 0 6-1 10-1-2 0-5 0-7 0-1-3-4-4-5s-2-2-3-2c-1-1-3-1-4-2-4-1-8-2-13-2-3 1-6 4-9 3zm14 82h0c0 2-1 7 0 8 1-1 1-1 1-3 1 1 1 1 2 1 1-1 1-1 2-1 0 0 1 1 2 1-2 1-3 2-4 3v6c-1 3-2 6-4 9l-1 1c-2 2-6 5-6 8-1 1 0 3 0 4l1 4h0c1 1 3 2 4 3-3 0-6-3-8-6-1-2-1-5-1-7v-1c1-4 5-6 7-9 2-1 4-3 4-5 1-1 0-3 0-5l1-11z" class="k"></path><defs><linearGradient id="n" x1="247.91" y1="114.862" x2="249.074" y2="125.001" xlink:href="#B"><stop offset="0" stop-color="#826c61"></stop><stop offset="1" stop-color="#a28978"></stop></linearGradient></defs><path fill="url(#n)" d="M241 115c12 0 20 2 29 8 3 3 5 5 5 9 0 2 0 3-1 4h1 3l-6 1 1-3v-1c0-2-1-2-2-4s-3-4-6-6h-1c-3-2-6-2-9-3-7-3-15-1-23-1-1-1-3 0-5-1 5-2 10-2 14-3z"></path><path d="M271 187l1 1 2 2 3 8 3 5v2h0l1-1c2-1 4-2 6-2v-1h1l-1-1c1-3 1-4 2-6l1 2c0-2 1-3 1-5l3 1-1 8v3l-1-2c-1 2-1 3-3 5l-1 2h0 0v-1-2h0l-1 1-1 1c-1 2-3 4-4 6h-1c0 2-1 3-2 5s-1 5-2 8c-1 0-1-1-1-1v-2c1-3 1-5 1-7 1 1 1 0 1 1 1-6-2-12-3-18 0-3-3-8-4-12z" class="K"></path><path d="M289 200l1-1c1 0 1-1 1-2l1-1v5c-1 2-1 3-3 5v-3c-1 1-1 1-2 1v-1c1-1 1-2 2-3z" class="d"></path><path d="M291 191l3 1-1 8v3l-1-2v-5l-1 1c0 1 0 2-1 2l-1 1 1-4c0-2 1-3 1-5z" class="h"></path><path d="M275 199c1 2 1 3 2 4l3 5v-1h2c1 0 2-1 3-2l1 1c-1 2-5 5-5 7s-1 3-2 5-1 5-2 8c-1 0-1-1-1-1v-2c1-3 1-5 1-7 1 1 1 0 1 1 1-6-2-12-3-18zm3-63c7 1 13 7 17 12 2 3 4 7 4 10 3 7 0 19-3 25v1l-2 8-3-1v-1-1c1-1 2-2 2-3s-1-1 0-2c2-3 2-6 2-9 2-3 2-10 2-14 0-3-2-9-5-11l-3-3-2-2c-2-1-3-2-6-3h-3c-1-1-3-1-5-1v3l-2-2c0-1 1-2 1-3v-2l6-1z" class="n"></path><path d="M291 190c2-1 2-3 2-4 1-2 1-2 3-2l-2 8-3-1v-1z" class="g"></path><path d="M278 136c7 1 13 7 17 12 2 3 4 7 4 10v1c0-2-1-4-2-5-2-4-3-6-6-8-5-3-6-6-12-7-3 0-4-1-7 0v-2l6-1z" class="AO"></path><path d="M211 144l1-3 5 1h12c2 1 4 2 6 2 1 0 2 1 3 1 2 2 4 3 6 3h1c3 2 4 5 6 8 1 1 1 2 2 4-4 0-6-4-8-5-1-1-2-2-4-2l-2-1c-1 0-2 0-2-1l-1 1c-2 0-2 0-3-1-2-1-3-1-5-1 1 0 2 1 3 2h1c2 1 4 2 5 4h0c-7-4-13-7-20-10-2 0-4-2-6-2z" class="G"></path><path d="M211 144l1-3 5 1h12c2 1 4 2 6 2 1 0 2 1 3 1 2 2 4 3 6 3h1c3 2 4 5 6 8-3 0-7-6-10-7-4-3-9-4-14-5-2 0-6-1-8 0-1 0-2 1-2 2-2 0-4-2-6-2z" class="O"></path><path d="M275 168v-1c-1 0-1-1-2-1-2-1-7-1-8-3 2 0 3 0 5 1h2c1 1 3 1 5 2l1-1c2 1 4 2 6 4h1c2 1 3 3 5 4v1c1 1 1 1 1 2 1 1 1 2 1 3l-1 1-1 3h-1v2h-1c-2-3-6-4-9-6-2-2-4-3-6-4l-1-1c-3-2-6-4-7-8l10 2z" class="J"></path><path d="M288 179l3 1-1 3h-1c-1-2-1-2-1-4z" class="Z"></path><path d="M275 168v-1c-1 0-1-1-2-1-2-1-7-1-8-3 2 0 3 0 5 1h2c1 1 3 1 5 2l1-1c2 1 4 2 6 4h1c2 1 3 3 5 4v1c1 1 1 1 1 2 1 1 1 2 1 3l-1 1-3-1v-2c-2-4-7-7-10-8-1 0-2 0-2-1h-1z" class="i"></path><path d="M269 175l-1-2 1-1 2 3 1-1 1 1c2 1 4 2 6 4 3 2 7 3 9 6h1v-2h1c0 2 0 4 1 6v1 1c0 2-1 3-1 5l-1-2c-1 2-1 3-2 6l1 1h-1v1c-2 0-4 1-6 2l-1 1h0v-2l-3-5c-1-3-2-5-3-8l-2-2-1-1c-1-2-2-6-3-8 0-2-1-4-1-5v-1l1 3h1v-1z" class="J"></path><path d="M268 179c0-2-1-4-1-5v-1l1 3h1v-1c2 4 4 10 6 14l3 6c0 2 2 6 2 8h0l-3-5c-1-3-2-5-3-8l-2-2-1-1c-1-2-2-6-3-8z" class="b"></path><path d="M286 188l1-1 1 1 2-1v1 4l-1 2c-1 2-1 3-2 6l1 1h-1v1c-2 0-4 1-6 2l-1 1h0v-2h0c0-2-2-6-2-8 1 2 3 3 3 5 0 1 1 1 1 2h1c1-1 1-2 1-3h1c0-1 0-2 1-3 0-3 1-5 0-8z" class="W"></path><path d="M272 174l1 1c2 1 4 2 6 4 3 2 7 3 9 6h1v-2h1c0 2 0 4 1 6v1 1c0 2-1 3-1 5l-1-2 1-2v-4-1l-2 1-1-1-1 1-1-1c-4-5-9-8-14-12l1-1z" class="d"></path><path d="M170 137h1c6-5 14-6 22-6 5 0 13 1 18 4 0 2-1 4-1 6l-1 2c-11-4-24-5-36 0-6 4-10 7-13 14h0c-3 4-1 8-2 12-1-1-1-4-1-6v-3c1-8 7-18 13-23z" class="Ad"></path><path d="M170 137h1c6-5 14-6 22-6-2 1-3 1-5 1h3 0c-6 1-10 2-16 4-2 1-3 1-5 1z" class="AJ"></path><defs><linearGradient id="o" x1="185.82" y1="148.636" x2="197.557" y2="133.027" xlink:href="#B"><stop offset="0" stop-color="#908679"></stop><stop offset="1" stop-color="#baa39b"></stop></linearGradient></defs><path fill="url(#o)" d="M173 143v-1c1 0 0-1 1-2 6-3 12-4 18-4 4 1 9 1 13 2 1 0 2 1 3 1 0 0 1 1 1 2h1l-1 2c-11-4-24-5-36 0z"></path><path d="M273 144v-3c2 0 4 0 5 1h3c3 1 4 2 6 3l2 2 3 3c3 2 5 8 5 11 0 4 0 11-2 14 0 3 0 6-2 9-1 1 0 1 0 2s-1 2-2 3c-1-2-1-4-1-6l1-3 1-1c0-1 0-2-1-3 0-1 0-1-1-2v-1-1c-2-2-3-3-4-5-3-4-5-10-8-14-2-3-4-6-5-9z" class="K"></path><defs><linearGradient id="p" x1="295.888" y1="120.227" x2="307.791" y2="147.092" xlink:href="#B"><stop offset="0" stop-color="#c6bab1"></stop><stop offset="1" stop-color="#ebebe9"></stop></linearGradient></defs><path fill="url(#p)" d="M282 122c3 1 6-2 9-3 5 0 9 1 13 2 1 1 3 1 4 2 1 0 2 1 3 2s4 4 4 5c0 2-1 5 0 7-1 5-5 11-8 16h0l-2 1c0 1 0 0-1 1v-3-1-1c-3-2-5-5-7-8-2-1-6-3-8-5-1-1-2-2-3-4s0-5 0-7c-1 1-1 2-3 3l-1 2c0 1 2 3 3 4-2-1-4-2-4-5-1-2-1-6 1-8z"></path><path d="M282 122c3 1 6-2 9-3 5 0 9 1 13 2-5 1-17 0-20 5-1 2-2 3-2 5 0 1 2 3 3 4-2-1-4-2-4-5-1-2-1-6 1-8z" class="O"></path><path d="M311 125c1 1 4 4 4 5 0 2-1 5 0 7-1 5-5 11-8 16h0l-2 1c0 1 0 0-1 1v-3-1c1 0 1-1 1-1 2-3 2-8 2-12h0c1 3 2 5 1 8v1l1-1c0-1-1-3 0-4s2-2 2-4v-2c1-2 1-4 1-6v-1c-1 0-1-1-1-1v-1c-1-1-1-1 0-2z" class="L"></path><path d="M286 126c5-1 8 0 12 3l3 2c2 2 4 4 6 7 0 4 0 9-2 12 0 0 0 1-1 1v-1c-3-2-5-5-7-8-2-1-6-3-8-5-1-1-2-2-3-4s0-5 0-7z" class="K"></path><path d="M297 142c2 0 3 0 4 2 2 2 3 3 3 6-3-2-5-5-7-8z" class="o"></path><path d="M232 119c8 0 16-2 23 1 3 1 6 1 9 3h1c3 2 5 4 6 6s2 2 2 4v1c-1-1-2-2-4-2s-4 0-6 1l-5 1-1-1h1l4-1c1-1 3-1 4-1v-1c-2 0-2 0-3 1-2 0-3 0-4 1h-3-2l-3 2c-1 0-2 0-2 1h-3v1c-1 1-3 1-4 2 0 1-1 1-1 1h-1c-4-1-8-4-12-5l-15-6 1-1 3-1 2-2c2-2 10-3 13-5z" class="W"></path><path d="M243 123c3-1 11 0 14 1h1 1v1c-2 1-4 1-7 1l-7-1c2 0 4 0 5-1-2-1-5 0-7-1z" class="g"></path><path d="M232 119c8 0 16-2 23 1l-11 1c-4 0-9 0-14 1-4 2-7 4-12 4l1-2c2-2 10-3 13-5z" class="O"></path><path d="M255 120c3 1 6 1 9 3h1c3 2 5 4 6 6s2 2 2 4v1c-1-1-2-2-4-2 0-2-1-2-3-3l-1-1-2-1c-4-1-7-1-11-1 3 0 5 0 7-1v-1-1c-5-2-10-2-15-2l11-1z" class="b"></path><path d="M266 129h5c1 2 2 2 2 4v1c-1-1-2-2-4-2 0-2-1-2-3-3z" class="G"></path><path d="M218 126v2h-1c1 1 2 1 4 2 2 0 7-3 9-4 4-1 8-2 13-3 2 1 5 0 7 1-1 1-3 1-5 1-1 1-6 1-7 2l-3 2c-1 1-2 2-3 2v1h4v1h-1l-2 2 2 1c1 0 2 1 3 1l2 2h0c-4-1-8-4-12-5l-15-6 1-1 3-1 2-2-1 2z" class="AO"></path><path d="M233 135c-2-1-5-3-8-3v-1c4-1 9-3 13-4l-3 2c-1 1-2 2-3 2v1h4v1h-1l-2 2z" class="G"></path><path d="M245 125l7 1c4 0 7 0 11 1l2 1 1 1c2 1 3 1 3 3-2 0-4 0-6 1l-5 1-1-1h1l4-1c1-1 3-1 4-1v-1c-2 0-2 0-3 1-2 0-3 0-4 1h-3-2l-3 2c-1 0-2 0-2 1h-3v1c-1 1-3 1-4 2 0 1-1 1-1 1h-1 0l-2-2c-1 0-2-1-3-1l-2-1 2-2h1v-1h-4v-1c1 0 2-1 3-2l3-2c1-1 6-1 7-2z" class="Z"></path><path d="M245 125l7 1c4 0 7 0 11 1l2 1h-7-6c-6 0-9 0-15 2l1 1h3v1c-1 2-4 2-6 4l-2-1 2-2h1v-1h-4v-1c1 0 2-1 3-2l3-2c1-1 6-1 7-2z" class="L"></path><path d="M269 132c2 0 3 1 4 2l-1 3v2c0 1-1 2-1 3l2 2c1 3 3 6 5 9 3 4 5 10 8 14 1 2 2 3 4 5v1c-2-1-3-3-5-4h-1c-2-2-4-3-6-4l-1 1c-2-1-4-1-5-2h-2v-1h-3l-2-1c-3 0-5-1-7-3-2-1-3-3-4-4l-5-8h1 0c-1-3-2-5-2-8 1-1 3-2 4-3 1 0 3-1 4-2h0l1-1 1 1 5-1c2-1 4-1 6-1z" class="J"></path><path d="M250 147c1-1 3-1 5-1 1 4 3 9 7 11s8 3 11 5l1 1v1h2c1 1 1 1 2 1l-1 1c-2-1-4-1-5-2h-2v-1h-3l-2-1c-3 0-5-1-7-3-2-1-3-3-4-4l-5-8h1z" class="Z"></path><path d="M269 132c2 0 3 1 4 2l-1 3v2c0 1-1 2-1 3-4-1-11 2-16 4-2 0-4 0-5 1h0c-1-3-2-5-2-8 1-1 3-2 4-3 1 0 3-1 4-2h0l1-1 1 1 5-1c2-1 4-1 6-1z" class="K"></path><path d="M296 204c1-5 0-10 1-14l2-6 1 1 1-1 3 6h1 1c1 1 1 2 3 3l-2-4c-1-1 0-1 0-2 1 1 4 3 5 4v1l1 1 3 5c1 1 1 1 1 2s1 2 2 3h0l3 4c3 2 4 3 4 7v2l-1 1v2c-1 2-1 4-1 6 1 0 1-1 1-1h1c-1 2-3 4-5 6-3 5-6 11-7 17v-3-1h0v-1c-1 1-1 2-2 2 0 1 0 2-1 2-1 7-9 13-4 21l3 3 1 1c2 1 3 2 5 4v1c-1 0-2 0-3-1l-1 1c0 2 0 2 1 4l-2 3v2h-1c-1-2-1-4-1-6-1-4-4-8-6-11s-3-7-5-10c0 0-1-2-2-2-1-1-2 0-3-1 1-1 1 0 3 0 0 0 1-1 2-1l1 2h0c1-1 1-3 3-4 1 0 1 0 1-2l-5 2v-1c-1-1-2-1-3-1s-1-1-2-2-3-2-4-3h0l-1-4c0-1-1-3 0-4 0-3 4-6 6-8l1-1c2-3 3-6 4-9v-6c1-1 2-2 4-3-1 0-2-1-2-1-1 0-1 0-2 1-1 0-1 0-2-1 0 2 0 2-1 3-1-1 0-6 0-8h0z" class="AP"></path><path d="M305 229c1-2 1-4 2-6 1 2 1 6 2 8l-2 2h-1-1c-1-1 0-3 0-4z" class="i"></path><path d="M310 243c-1 1-2 3-3 4s-3 2-4 3l-5 2v-1c-1-1-2-1-3-1s-1-1-2-2h2l1 1h7l4-4 3-2z" class="J"></path><path d="M305 216l2-2c2 3 2 7 3 9 0 2 1 3 2 4 0 2-1 5-2 7 0 2-1 3-2 4h-1 0l1-1c1-1 1-3 1-4v-1-2 1c-1-2-1-6-2-8v-1c-1-2-1-4-1-5l-1-1zm10 18l1-5c1-2 1-4 1-7h-1v5c-1-4-1-7-2-10-2-6-5-11-7-17 6 6 9 11 11 20 0 5 0 9-2 14h-1z" class="n"></path><path d="M303 210c1 1 2 3 3 4l-1 2 1 1c0 1 0 3 1 5v1c-1 2-1 4-2 6v-1c-1 1-1 3-1 4h-1v-2l1-1-1-1v-1c0-3 0-7-1-9v1c0 2 0 3-1 4s-1 1-1 2c-1 1-1 2-2 3 0-2 1-3 1-4v-1c-2 4-2 5-5 6l1-1c2-3 3-6 4-9v-6c1-1 2-2 4-3z" class="G"></path><path d="M303 210c1 1 2 3 3 4h-1c-2 0-3 0-4-1-1 2-1 4-2 6v-6c1-1 2-2 4-3z" class="b"></path><path d="M296 204c1-5 0-10 1-14l2-6 1 1 1-1 3 6h1c0 2 0 3-1 6-1 1 0 1 0 3 0 0-1 2-1 3h0c1 2 1 3 2 4 0 1 0 1 1 2 3 5 6 13 6 19-1-1-2-2-2-4-1-2-1-6-3-9l-2 2 1-2c-1-1-2-3-3-4-1 0-2-1-2-1-1 0-1 0-2 1-1 0-1 0-2-1 0 2 0 2-1 3-1-1 0-6 0-8h0z" class="AO"></path><path d="M304 190h1c0 2 0 3-1 6-1 1 0 1 0 3 0 0-1 2-1 3h0c1 2 1 3 2 4l-3-2c-1 0-2-1-2-2l1-1c0-3 1-6 3-8v-1-2z" class="K"></path><path d="M307 187c1 1 4 3 5 4v1l1 1 3 5c1 1 1 1 1 2s1 2 2 3h0l3 4c3 2 4 3 4 7v2l-1 1v2c-1 2-1 4-1 6 1 0 1-1 1-1h1c-1 2-3 4-5 6-3 5-6 11-7 17v-3-1h0v-1c-1 1-1 2-2 2 0 1 0 2-1 2-1 7-9 13-4 21l3 3 1 1c2 1 3 2 5 4v1c-1 0-2 0-3-1l-1 1c0 2 0 2 1 4l-2 3v2h-1c-1-2-1-4-1-6-1-4-4-8-6-11s-3-7-5-10c0 0-1-2-2-2-1-1-2 0-3-1 1-1 1 0 3 0 0 0 1-1 2-1l1 2h0c1-1 1-3 3-4 1 0 1 0 1-2 1-1 3-2 4-3s2-3 3-4l1-2h0l4-7h1c2-5 2-9 2-14 1 1 1 4 1 6s0 3-1 5c0 2-1 4-1 6 1-1 1-3 2-4v-1h0c0-1 0-1 1-2 0-1 1-1 1-2v-1c1-1 1-1 1-3v-4c1-2 1-5 1-6v-1c-1-2-2-5-4-7v-1l-1-1-1-2-3-3c-1-1 0-1-1-2l-2-3s-1-1-1-2l-1-2h-1l-1-1c-1-1 0-1 0-2z" class="i"></path><path d="M304 267c1 3 2 6 4 9l2 2h0l1-7c2 1 3 2 5 4v1c-1 0-2 0-3-1l-1 1c0 2 0 2 1 4l-2 3v2h-1c-1-2-1-4-1-6-1-4-4-8-6-11l1-1z" class="d"></path><path d="M311 241l1 1c-2 3-3 5-5 6-1 1-1 2-2 3 0 1 0 1-1 2s-1 3-2 5v1c-1 3 0 6 2 8l-1 1c-2-3-3-7-5-10 0 0-1-2-2-2-1-1-2 0-3-1 1-1 1 0 3 0 0 0 1-1 2-1l1 2h0c1-1 1-3 3-4 1 0 1 0 1-2 1-1 3-2 4-3s2-3 3-4l1-2z" class="L"></path><defs><linearGradient id="q" x1="356.224" y1="138.733" x2="366.892" y2="128.796" xlink:href="#B"><stop offset="0" stop-color="#cbbfb9"></stop><stop offset="1" stop-color="#ebe9e7"></stop></linearGradient></defs><path fill="url(#q)" d="M325 126c3-4 10-9 15-10 2-1 4-2 7-3 1 1 3 1 4 1 0 1 0 1 1 1 6-1 13-1 20 0 3 1 6 2 9 4 1 0 4 3 4 3 6 5 12 12 14 19-1 0-2 0-3-1v1h-1c-1 2-1 5-2 7-2 2-2 5-4 7 0 1-2 2-3 4l-2 3c-1 1-2 3-3 5h-1 0-1l-3 3-1-1v-1c-1 1-1 3-2 4l-1-2-2 6-4-12c-1-1-2-3-3-5l-1-1c-6-10-18-14-28-18l-2-1h-1l-4-1h-3 0-2l1-1c0-2 1-4 3-6h-1c0-2 2-4 3-6l-3 1z"></path><path d="M341 125c10 0 19 3 26 9-3 0-5-2-8-4-4-1-14-4-18-2-3 0-5 0-8 2l-1-1v-1l6-2c2 0 2 0 3-1z" class="AO"></path><path d="M341 128c4-2 14 1 18 2 3 2 5 4 8 4 6 3 9 9 11 15v4h-1c0-4-1-8-4-11-3 0-6-5-9-6-4-3-10-5-15-6l-8-2z" class="g"></path><defs><linearGradient id="r" x1="358.487" y1="152.048" x2="374.488" y2="146.871" xlink:href="#B"><stop offset="0" stop-color="#d9d2ca"></stop><stop offset="1" stop-color="#f5f5f5"></stop></linearGradient></defs><path fill="url(#r)" d="M349 130c5 1 11 3 15 6 3 1 6 6 9 6 3 3 4 7 4 11 0 5 0 10-2 15-1 1-1 3-2 4l-1-2c1-5 2-9 1-13 0-2 0-4-1-6-5-11-14-15-24-20l1-1z"></path><path d="M345 122h-1l1-1c3 0 5-1 7-1 3 2 13 1 14 3 5 1 9 2 12 5l2 3c-1-1-2-1-3-1 0 1 2 2 2 3s0 1 1 2c1 2 3 5 3 8h-1v4c-1-1-2-1-3-3-1-3-2-5-4-8l-4-4c-6-5-13-9-20-10h-6z" class="p"></path><path d="M373 128h-1c-1-1-3-1-5-1l-3-3h2c3 0 5 2 7 4z" class="AO"></path><path d="M375 136c3 0 3 1 5 3 1 1 1 2 2 4v4c-1-1-2-1-3-3-1-3-2-5-4-8z" class="b"></path><path d="M366 123c5 1 9 2 12 5l2 3c-1-1-2-1-3-1-2-1-2-2-4-2-2-2-4-4-7-4v-1z" class="L"></path><path d="M378 128c5 2 7 4 11 7 1 3 1 4 0 6 0 4 0 7-1 11l-2 6h0v1l-2 3c-1 1-2 3-3 5h-1 0-1l-3 3-1-1v-1c2-5 2-10 2-15h1v-4c1-2 1-3 1-5 1 2 2 2 3 3v-4h1c0-3-2-6-3-8-1-1-1-1-1-2s-2-2-2-3c1 0 2 0 3 1l-2-3z" class="o"></path><path d="M382 143h1c1 9 0 15-3 24h-1c1-2 1-4 2-6 1-5 1-9 1-14v-4z" class="O"></path><path d="M378 128c5 2 7 4 11 7 1 3 1 4 0 6 0 4 0 7-1 11 0-5-1-10-3-14-1-3-3-5-5-7l-2-3z" class="g"></path><path d="M379 144c1 2 2 2 3 3 0 5 0 9-1 14-1 2-1 4-2 6l-3 3-1-1v-1c2-5 2-10 2-15h1v-4c1-2 1-3 1-5z" class="Z"></path><path d="M378 153c0 2 1 4 1 6-1 3-3 7-4 10v-1c2-5 2-10 2-15h1z" class="b"></path><defs><linearGradient id="s" x1="353.521" y1="109.02" x2="382.763" y2="148.639" xlink:href="#B"><stop offset="0" stop-color="#785f55"></stop><stop offset="1" stop-color="#ad9a8b"></stop></linearGradient></defs><path fill="url(#s)" d="M325 126c3-4 10-9 15-10 2-1 4-2 7-3 1 1 3 1 4 1 0 1 0 1 1 1 6-1 13-1 20 0 3 1 6 2 9 4 1 0 4 3 4 3 6 5 12 12 14 19-1 0-2 0-3-1v1h-1c-1 2-1 5-2 7-2 2-2 5-4 7 0 1-2 2-3 4v-1h0l2-6c1-4 1-7 1-11 1-2 1-3 0-6-4-3-6-5-11-7-3-3-7-4-12-5-1-2-11-1-14-3-2 0-4 1-7 1l-1 1h1l-2 1-2 1v1c-1 1-1 1-3 1l-6 2v1l1 1c-2 2-5 4-7 6h-1c-1 1-2 1-2 1 0-2 1-4 3-6h-1c0-2 2-4 3-6l-3 1z"></path><path d="M343 120c1 0 3 0 4-1 3 0 7-1 10-2 4 0 8 0 12 1l-3 2h-4-10c-2 0-4 1-7 1l-1 1h1l-2 1-2-1 2-2z" class="O"></path><path d="M328 125c4-3 9-6 14-7-2 1-5 3-7 5h0 1c1-1 2-1 3-1v-1c2-1 3-1 4-1l-2 2c-2 0-4 2-7 3s-6 4-8 6h-1c0-2 2-4 3-6z" class="AJ"></path><path d="M325 126c3-4 10-9 15-10 2-1 4-2 7-3 1 1 3 1 4 1 0 1 0 1 1 1-3 1-7 1-10 3-5 1-10 4-14 7l-3 1z" class="b"></path><path d="M326 131c2-2 5-5 8-6s5-3 7-3l2 1-2 1v1c-1 1-1 1-3 1l-6 2v1l1 1c-2 2-5 4-7 6h-1c-1 1-2 1-2 1 0-2 1-4 3-6z" class="h"></path><path d="M369 118c10 2 19 8 24 17 1 2 1 4 2 6-1 2-1 5-2 7-2 2-2 5-4 7 0 1-2 2-3 4v-1h0l2-6c1-4 1-7 1-11 1-2 1-3 0-6-4-3-6-5-11-7-3-3-7-4-12-5-1-2-11-1-14-3h10 4l3-2z" class="W"></path><path d="M390 135c1 3 2 5 2 8-1 4-3 12-6 15l2-6c1-4 1-7 1-11 1-2 1-3 0-6h1z" class="O"></path><defs><linearGradient id="t" x1="363.859" y1="119.652" x2="381.214" y2="126.995" xlink:href="#B"><stop offset="0" stop-color="#8c786b"></stop><stop offset="1" stop-color="#a69082"></stop></linearGradient></defs><path fill="url(#t)" d="M352 120h10 4c9 2 14 4 20 11l4 4h-1c-4-3-6-5-11-7-3-3-7-4-12-5-1-2-11-1-14-3z"></path><path d="M333 130c3-2 5-2 8-2l8 2-1 1c10 5 19 9 24 20 1 2 1 4 1 6 1 4 0 8-1 13l-2 6-4-12c-1-1-2-3-3-5l-1-1c-6-10-18-14-28-18l-2-1h-1l-4-1h-3 0-2l1-1s1 0 2-1h1c2-2 5-4 7-6z" class="Z"></path><path d="M362 152c-3-3-6-4-8-6 2 1 5 1 7 3 1 1 1 0 3 0 1 1 3 6 4 7v1l-1-2-1 1c-1-1-1 0-1-1-1-2-2-3-3-3z" class="J"></path><path d="M362 152c1 0 2 1 3 3 0 1 0 0 1 1l1-1 1 2v-1c0 1 1 2 2 2v13c-1-3-3-5-3-7v-1c0-2-2-6-4-8h0v-1l-1-2z" class="o"></path><path d="M335 138c1-1 2-1 3-1l1-1c3 0 6 1 9 2 2 1 4 2 6 2 1 1 2 1 3 3v1c-2-1-4-1-5-1h-1l-1-1c-1 0-3-1-4-1s0 0-1-1h-2c-2-1-4-2-6-2h-2z" class="K"></path><path d="M360 142c1 0 3 0 4 1 3 1 4 4 6 6v1c1 1 1 2 1 3l1 1v4h1v-1c1 4 0 8-1 13l-2 6-4-12h1c0 2 2 4 3 7v-13c-1 0-2-1-2-2-1-1-3-6-4-7-2 0-2 1-3 0 1 0 1 0 2-1v-1l1-1-1-1c-1 0-2-1-3-3z" class="O"></path><path d="M364 146c2 2 4 5 5 9h0l1 3c-1 0-2-1-2-2-1-1-3-6-4-7-2 0-2 1-3 0 1 0 1 0 2-1v-1l1-1z" class="d"></path><path d="M333 130c3-2 5-2 8-2l8 2-1 1c10 5 19 9 24 20 1 2 1 4 1 6v1h-1v-4l-1-1c0-1 0-2-1-3v-1c-2-2-3-5-6-6-1-1-3-1-4-1-2-1-2-1-3-3-1-1-4-2-5-2l2 3c-2 0-4-1-6-2-3-1-6-2-9-2l-1 1c-1 0-2 0-3 1h-3l-1 1-4-1h-3 0-2l1-1s1 0 2-1h1c2-2 5-4 7-6z" class="p"></path><path d="M332 137c1-1 2-3 4-3h4 3l9 3 2 3c-2 0-4-1-6-2-3-1-6-2-9-2l-1 1c-1 0-2 0-3 1h-3v-1z" class="i"></path><path d="M333 130c3-2 5-2 8-2l8 2-1 1c-4-1-8-1-12 0h-1c-2 1-6 4-8 6 1 0 2 1 3 1 1-1 2-1 2-1v1l-1 1-4-1h-3 0-2l1-1s1 0 2-1h1c2-2 5-4 7-6z" class="L"></path><defs><linearGradient id="u" x1="461.504" y1="192.903" x2="372.502" y2="138.21" xlink:href="#B"><stop offset="0" stop-color="#ddd7d3"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#u)" d="M404 116c7-2 15-2 23-1 3 0 5 0 8 1 4 1 8 3 12 5 2 1 5 1 8 2 5 2 12 5 15 10h1l5 5h0c0-2 0-3-1-5 2 2 3 4 4 6 3 7 4 14 2 22-2 6-5 13-11 16v-1l2-3h-2c1-2 2-5 3-7 1-6 0-13-3-17-5-7-11-9-18-10-12 0-21 3-31 9l-1 1c-2-2-3-5-5-6-10 3-17 12-24 20-1 1-2 1-3 3l-1 1c-1 2-2 4-4 6 0 2-1 3-2 4v1h-1c-2 3-2 6-3 9l-1 1-5 5h0c-2 1-2 2-3 2v1h-1l-3 2c-1 0-1 1-2 1h-1l1-2c1-4 4-6 6-9 1-4 3-9 4-12l1-4c1-1 1-3 2-4v1l1 1 3-3h1 0 1c1-2 2-4 3-5l2-3c1-2 3-3 3-4 2-2 2-5 4-7 1-2 1-5 2-7h1v-1c1 1 2 1 3 1-2-7-8-14-14-19v-1l-1-1h1c1 0 3 1 5 1 1 1 2 3 3 4l1-1c1-1 1-2 2-3l-1-1c1-2 1-2 0-4 2 0 3 1 5 0h4z"></path><path d="M390 156c-1 3-2 4-4 6-1 1-2 3-3 4l-3 6-3 6c-1 2-3 5-4 7v-4l6-9c2-3 4-6 6-10 1-2 3-4 5-6z" class="J"></path><path d="M479 139c3 7 4 14 2 22-2-3-1-4-2-7 0-3 0-5-1-8h-1l1-1h0l1-1v-2c-1-1 0-2 0-3z" class="G"></path><path d="M395 116c2 0 3 1 5 0h4v1c3 0 5-1 7-1l-1 1h-3c-1 0-2 1-2 1h-3c1 0 2 1 3 0 2 0 4-1 6 0 0 1 1 1 2 1-6 0-11 0-17 2l-1-1c1-2 1-2 0-4z" class="o"></path><path d="M395 141h1v-1c1 1 2 1 3 1 0 3 0 4-1 5l-8 10c-2 2-4 4-5 6h-1l2-3c1-2 3-3 3-4 2-2 2-5 4-7 1-2 1-5 2-7z" class="g"></path><path d="M395 141h1v-1c1 1 2 1 3 1 0 3 0 4-1 5h-1v-2h-1v1c-1 1-1 2-2 4l-1-1c1-2 1-5 2-7z" class="O"></path><path d="M404 116c7-2 15-2 23-1 3 0 5 0 8 1 4 1 8 3 12 5 2 1 5 1 8 2 5 2 12 5 15 10 0 0-1-1-2-1-1-1-2-1-3-2s-3-2-5-3h-2c-2-1-4-1-6-2-6-3-14-8-21-9s-14-1-20 0c-2 0-4 1-7 1v-1z" class="n"></path><path d="M380 167h0 1c1-2 2-4 3-5h1c-2 4-4 7-6 10l-6 9v4c-1 2-3 4-5 5-2 3-4 5-6 7 1-4 4-6 6-9 1-4 3-9 4-12l1-4c1-1 1-3 2-4v1l1 1 3-3h1z" class="W"></path><path d="M368 190l2-4c1-2 2-4 3-5v4c-1 2-3 4-5 5z" class="K"></path><path d="M437 123c1 1 2 1 3 3h0c-4 1-8 2-11 3l-21 9c-1 1-4 2-5 1h-1c1-1 2-1 3-1 2 0 5-2 8-2l6-3h1c0-1 1-1 2-2h0v-1c-2-1-4-3-6-5h0c1-1 2 0 4 0 1 1 0 0 1 0s2 1 2 1c1 1 1 1 2 1v-1l-1-1c1-1 0-1 1-2 4 1 9 2 12 0z" class="h"></path><path d="M437 123c1 1 2 1 3 3h0-3c-2-1-4 0-5-1-3 0-5 1-7 2v-1l-1-1c1-1 0-1 1-2 4 1 9 2 12 0z" class="O"></path><path d="M413 119c8 0 18 0 24 4-3 2-8 1-12 0-1 1 0 1-1 2l1 1v1c-1 0-1 0-2-1 0 0-1-1-2-1s0 1-1 0c-2 0-3-1-4 0-4-2-9-1-14-1-1 0-1 0-2 1l-2 2-1 3c1 1 1 1 1 3l-3-5-2-3 1-1c1-1 1-2 2-3 6-2 11-2 17-2z" class="AJ"></path><path d="M395 128c1 0 1 0 1-1 1-2 1-2 3-3 1-1 3-2 5-2 7-1 14 1 21 1-1 1 0 1-1 2l1 1v1c-1 0-1 0-2-1 0 0-1-1-2-1s0 1-1 0c-2 0-3-1-4 0-4-2-9-1-14-1-1 0-1 0-2 1l-2 2-1 3c1 1 1 1 1 3l-3-5z" class="AI"></path><defs><linearGradient id="v" x1="469.416" y1="168.69" x2="462.033" y2="130.339" xlink:href="#B"><stop offset="0" stop-color="#7c685f"></stop><stop offset="1" stop-color="#aa9589"></stop></linearGradient></defs><path fill="url(#v)" d="M454 130c8 2 14 5 18 11 2 3 3 5 4 8v1c2 7 1 14-3 21l-1 1v1h-2c1-2 2-5 3-7 1-6 0-13-3-17-5-7-11-9-18-10l1-1h8c3 1 6 3 9 4-2-2-4-4-7-5-2 0-4-2-6-2l-10-2 1-1c2-1 4 0 6-1v-1z"></path><path d="M398 127l2-2c1-1 1-1 2-1 5 0 10-1 14 1h0c2 2 4 4 6 5v1h0c-1 1-2 1-2 2h-1l-6 3c-3 0-6 2-8 2-1 0-2 0-3 1-2-2-4-5-4-6 0-2 0-2-1-3l1-3z" class="W"></path><path d="M398 127l2-2c1-1 1-1 2-1 5 0 10-1 14 1h0v2l3 3-1 1-3-3c-1-1-1-2-2-2-2 0-2 0-3 1-1 0-1-2-3-1v-1h0c-3 0-4 0-6 1l-3 1z" class="L"></path><path d="M421 145v-4c1-3 3-4 5-6 9-6 18-7 28-5v1c-2 1-4 0-6 1l-1 1 10 2c2 0 4 2 6 2 3 1 5 3 7 5-3-1-6-3-9-4h-8l-1 1c-12 0-21 3-31 9-1-1 0-2 0-3z" class="Ab"></path><path d="M455 136h0c-12-3-23-1-32 5h-1c2-2 5-4 8-5 5-3 11-4 17-3l10 2-2 1z" class="G"></path><path d="M457 135c2 0 4 2 6 2 3 1 5 3 7 5-3-1-6-3-9-4h-8l-1 1c-12 0-21 3-31 9-1-1 0-2 0-3 4-5 10-7 16-8s12 0 17-1h1l2-1z" class="h"></path><path d="M554 174c-4-4-6-10-7-15-1-7 0-14 3-20 3-9 9-15 17-19 5-3 11-5 17-8 3-2 5-5 8-8h229c1 2 3 4 5 5h1c-1 2-2 2-4 2-4-1-9 0-14 0-10 0-20 2-30 5l-13 9v1h3l-5 4-3 1h1c-1 1-2 2-3 2l-1 3h2c0 1-1 1-1 2v2c0 1 2 1 1 2 1 1 1 2 3 2h0c1 2 1 3 1 4-2 1-4 3-5 4l1 2c-1 1-2 1-2 2l1 2c0 2 0 3-1 5l-3 3c0 1 0 3 1 3l-4 7-2 1c-7 8-12 17-15 27 0 3-1 5 0 8h0c1 1 1 0 2 1 1 0 2 0 3 2 1 1 1 3 2 4-1 4 1 5 1 8l-1 1h0c0-1-1-2-2-4 2 9 3 20 0 28-3 6-7 12-13 14-2 0-4 1-6 1h0-2-3-7c0-1 0-2-1-3l-6 6c-4 6-7 12-9 19-1 0-2 6-2 6l-1 5-3 15v1c-1 2-3 5-4 7-1 3-1 7-2 10v7c-1 1-3 4-4 5l-3 6c-4 13-8 27-11 40l-3 13-1 2-3 10-11 43-1 6c-1 1-2 3-2 5l-5 19-5 20c-2 6-4 14-5 20l-9 33-24 84-11 39c-2 8-5 16-7 25-1 3-1 7-1 11l4 9-4 5c-1 2-2 3-3 4-3 6-4 13-1 19v1c0 1 1 1 0 2l-3 2c-3 3-5 9-6 13v5c0 1 1 3 1 5h-1c-1 3-5 5-6 8-6 9-8 22-10 32 0 4-1 8-2 11-1 4-3 8-4 12 0 3 0 5 1 7 0 3 3 7 2 9-1 0-1-1-2-1l-1-1c-1 0-2 2-3 2h-2v-1h1 0 1c0-2-1-4-1-5-1-5-1-12 1-16v-3c-1-2 1-7 1-9-1-2 0-4-1-6l-2 12h0 0v-2-1-3h0l-1 2v1h0v1c-1 1 0 1-1 1 0 1 0 2-1 3v1c-1 7-2 13-4 20-1-3 0-3 0-6l2-7-2 1-1 4-1 1c-1 0-1 1-1 2h0c-1 1-1 1-1 2h-1l-3 2v2h-1v-1-1c-3 0-3 2-5 3-1 2-1 3-1 4s-1 1-1 2c-1-2-1-4 0-7 1-4 2-10 1-14h-1c0 2-2 4-4 5h-2v1h-4-1-1c0 1 0 1 1 2 0 0 1 1 1 2-1 0-2 0-3-1v1 1 6h0c-1 1-1 2-1 3v2c-1 2-1 3-1 5 0-2 0-4-1-5v1c0 1-1 4-1 4-2 5 0 9 2 13l1 1h-1-2c-1-1 0-3 0-5h-1c0-2-1-3-2-5h-1c0-1 0-1 1-2h0v-1h1v-4l-1-6c-1-4-3-5-6-7 1-3 2-4 2-7-1-7-6-14-9-20-2-6-4-13-8-18-2-2-5-4-8-6l1-13c-2-5-3-10-6-14 0-1-4-5-4-5 0-1 0-3 1-4v-7c-1-5-2-10-4-15l-7-22c-3-9-5-18-9-26 0-3-1-6-2-9-2-3-3-8-4-12-5-13-8-26-13-38-3-11-7-22-10-32l-9-26c-2-7-4-15-7-22 0-1-1-3-2-5l-4-12-10-33-11-33c-2-2-3-3-4-5l-6-6c-4-4-10-7-15-11 2-1 2-1 4-1h1 4 0v-1c-1-1-1-1 0-2 1-2 2-2 2-4v-1h2v1c0 1 1 2 2 3h0l-1-4-1-4-8-25-3-10-3-9-20-64c-1-7-4-13-6-20-1-1-4-12-5-14l-3 3v-2-2h0l-1-2c-1-3-2-7-3-11 0-2-2-3-2-5s1-3 1-4l-1-2c1-1 1-2 1-3-1-2-2-5-4-7-2-3-3-4-6-5h-2l-1-1v-2l1-1c1-2 0-5-1-8 3 1 5 3 8 5 1 1 2 0 3 1 1 0 2 2 2 2 2 3 3 7 5 10s5 7 6 11c0 2 0 4 1 6h1v-2l2-3c-1-2-1-2-1-4l1-1c1 1 2 1 3 1v-1c-2-2-3-3-5-4l-1-1-3-3c-5-8 3-14 4-21 1 0 1-1 1-2 1 0 1-1 2-2v1h0v1 3c1-6 4-12 7-17 2-2 4-4 5-6h-1s0 1-1 1c0-2 0-4 1-6v-2l1-1v-2c0-4-1-5-4-7l-3-4h0c-1-1-2-2-2-3s0-1-1-2l-3-5-1-1v-1c-1-1-4-3-5-4s-1 0-1-1l1-1c1 0 1 0 1-1v-3h1c0 2 0 2 1 3v-2h1v-3c0-1-1-1-1-2h-2v-1c2-1 2-7 3-9h0l-1 1c0-4 0-5 1-9 2-4 4-7 7-9 1 0 2-1 3-1v-1-2h2c1-1 1-1 2-1 0-2-1-3-1-4h0-1l1-1-1-1 1-1h0 3l4 1h1l2 1c10 4 22 8 28 18l1 1c1 2 2 4 3 5l4 12 2-6 1 2-1 4c-1 3-3 8-4 12-2 3-5 5-6 9l-1 2h1c1 0 1-1 2-1l3-2h1v-1c1 0 1-1 3-2h0l5-5 1-1c1-3 1-6 3-9h1v-1c1-1 2-2 2-4 2-2 3-4 4-6l1-1c1-2 2-2 3-3-4 6-8 12-9 20-1 6-1 10 0 15v1c1 0 1 1 1 3l2 4c0 1-2 4-3 4-1 4-1 9 1 14l1 2c0 1 1 2 2 3l2 2c0 1 0 1-1 2-3 3-4 6-5 11-2 12 0 25 3 38 1 5 2 10 4 15 2 1 2 3 3 6v1c1-1 1 0 1-1v-1h1c1 5 4 8 7 12 2 2 4 4 5 6h1l6 9 5 6 2-2c-2-2-2-4-3-7l1-1 3 5v1l6 9 3 3 1-1c1 0 1 0 2 1 0 1 0 2 1 2h2 0l1 1c1 2 1 4 2 5 2 2 3 4 5 6h0c2 4 6 8 7 11l1 1 2-2v1c5 4 9 9 14 13l1 1c2 2 4 3 6 5l4-1c2 2 4 4 7 4l1 1h6 1l1 1 1-1 1-1h2 0 1l1-2v-11c0-3 0-7 1-10l-2-15 1-1v-4l1-1c-1 0-1 0-1-1h3v-3h-2c-2-1-4-2-6-2l-3-2 6-2h0c0 1 0 1-1 2 1 0 1 1 1 1h3c3 1 3 1 5 3l3-2 1 1c1-1 1-2 2-3 1 1 2 1 2 1h3l2-1-2-1v-2h0c2 0 3-1 5-1l1 2c2 0 3 0 5 2 0 0 1 0 1 1-1 1-2 1-3 2-1 0-1-1-2-2l-1 3c0 1 0 1 2 2v4h0c0 2 0 3 1 4 3 2 6 3 10 3-1 2-3 3-5 5l-1 5h1c0 1-1 2-2 3l2 1v3h0c-2 2-3 4-3 6 0 1 1 1 1 2h1c-1 2-1 3-1 5h0l1 1v1l2 2c1-1 3-1 4-1l1 1c-1 1-1 1-1 2 1 0 1 1 2 0 2-1 4-3 6-4 1 1 1 1 3 1 1-1 3-2 4-3l1-1c3-2 7-7 11-6h1 0c2 0 3 0 4 1s2 1 2 2c1 0 2-1 2-1v1h1v-2c1-2 3-3 4-5 0-2 1-3 3-4l-2-1 6-6c2-2 4-5 6-7l5-1h0c1 0 1 0 2-1 2-1 3-1 4-1l3-3 6-3c1 1 1 1 1 2l4-3 7-6h1c3-1 8-6 10-8h1l1-3c3-10 4-22 6-33 0-3 1-8 2-12 1-14 3-30 1-45-1-3-1-7-3-10l1-1c-2-5-5-7-8-10-2-2-1-4-1-6h-1l-2 1h-1c0-1-1-2-1-3h-1 1c1-1 1-2 1-4 0-1-1-2-2-2v-2c-1-2-1-1-2-1 0 0-1-1-1-2h-4c-1 1-1 0-2 0h0c-1 0-2 0-2 1l-2 1-3-2 4-6 3-5h0v-1l-1 1v-1h-2l4-8c2-1 2-3 3-5 0-1 1-2 1-3v-3c3 1 6 2 8 4h0c0-1-1-2-2-2h0c0-2 1-2 0-4h-1c-3-2-7-9-9-12l-5-5-7-5c-6-5-18-7-25-8h-1-5c-4 0-11 1-14 4-1 0-1 0-2-1-1 1-3 3-4 3v4l-3 6-2 2c0 3 0 7 1 10 0 1 1 3 1 5 0 1 1 3 0 5z" class="AP"></path><path d="M380 434h2v4c-1 2-1 4-1 6v-5l-1-1v-4z" class="K"></path><path d="M584 125h1c0-1 1-1 2-2 0 2 1 4 2 5-2 0-4-1-5-3h0z" class="O"></path><path d="M551 783h2v1c-2 1-4 2-5 3l-1 1v-1c1-2 2-3 4-4z" class="J"></path><path d="M364 250c0 1 1 2 2 3h1l1 4-1 2-4-9h1z" class="d"></path><path d="M350 399c1 2 3 4 4 5l1 2-1-1-1 1h-1c-1-2-2-4-2-7z" class="i"></path><path d="M624 138h1l1 1c1 2 1 3 1 5h-2v-1c-1-2-1-3-1-5z" class="K"></path><path d="M677 302l4-1-1 2c-1 2-2 3-3 5h-1l1-6z" class="AT"></path><path d="M454 506l3 2h0s2 2 2 3 0 2 1 3l-3-3-1 3-2-8z" class="S"></path><path d="M752 159v-1-3c-1-2 0-4 1-6v-1c1 3 1 3 0 5 1 1 1 1 2 1-1 2-2 3-3 5z" class="J"></path><path d="M560 632c1 1 1 2 2 3l-2 7-2-2h0c1-2 1-3 1-5v-2h1v-1z" class="y"></path><path d="M424 619c-1-3-3-7-3-10l1 1c1 3 3 5 6 7v1h-1c-2 0-2 0-3 1z" class="h"></path><path d="M422 610c2 2 4 3 6 4s3 1 5 1c-2 1-3 2-5 2-3-2-5-4-6-7z" class="L"></path><path d="M557 142v4l-3 6-2 2c1-4 2-7 3-10l2-2z" class="AI"></path><path d="M456 514l1-3 3 3c1 2 2 3 2 5h-1-2c0-1 0 0-1-1v-1l-2-3z" class="v"></path><path d="M455 573c1 4 1 10 0 14-2 0-1 0-2-1 1-4 1-9 1-13h0v1l1-1z" class="b"></path><path d="M331 326c1 2 2 3 2 5 1 3 4 6 6 8l-1 1c-1-1-3-4-5-5-2-2-2-6-2-9z" class="J"></path><path d="M350 399c0-2-3-7-4-9 2 2 4 4 5 6 0 1 0 2 1 2 1 2 2 4 2 6-1-1-3-3-4-5z" class="W"></path><path d="M450 557c0 1 1 1 2 1 1 2 2 6 3 8h0-1v3l1 4-1 1v-1h0c-1-3-1-7-2-10-1-2-2-4-2-6z" class="g"></path><path d="M531 774l2 1c0-1 1-1 1-2 0-3-1-8 0-11 1 4 0 8 1 12 0 2 1 4 1 6l-1 1-2-2v-1c-1-1-1-3-2-4z" class="W"></path><path d="M654 207v-6c1-1 1-2 1-3-1-2-2-2-2-3v-1l4 4c0 2 2 2 2 4h-1c-1 1-3 3-4 5z" class="d"></path><path d="M356 446c-1-5-5-12-4-16 1 6 3 10 7 15h1c0 1-1 2 0 3 1 2 2 3 2 5-2-3-3-5-6-7z" class="b"></path><path d="M437 616l2-2c0 2-1 2-2 3v1c-1 1-1 2-2 3l-1 1c-1 3-3 5-4 8h0c-1-1-2-2-3-4h3c3-1 5-7 7-10z" class="i"></path><path d="M428 618h3c-2 3-2 3-2 6 0 1 0 1-1 1 0 0-1 0-1-1-2-1-2-3-3-5 1-1 1-1 3-1h1z" class="b"></path><path d="M746 168c1 1 2 3 2 4l1 3c-1 1-2 1-3 1l-2-2h-3c2-2 3-4 5-6z" class="Ad"></path><path d="M428 614c2 0 2 0 3-1 4-2 7-5 11-7-2 4-5 7-9 9-2 0-3 0-5-1z" class="o"></path><path d="M352 448c2 1 3 2 4 4v4h3l-1 1 1 1c1 1 1 2 2 4 0 0 1 1 1 2h-1l-1-2c0-1-1-1-1-2v-1h-1c0-1-1-2-2-2l1 2c1 2 1 2 1 4v1l-1-3-2-5-2-4-1-4z" class="J"></path><path d="M748 120c4 1 10 2 14 1l-1 2-5 1c-2 0-4 1-6 0-1-1-1-2-2-4z" class="p"></path><path d="M658 334h1 1v1h1c-3 3-7 6-10 7h-2l9-8z" class="d"></path><path d="M370 466c0 1 1 2 1 3 1 1 2 3 3 5 3 3 3 10 4 14l-4-11c-2-4-4-7-4-11z" class="n"></path><path d="M561 675v-1c1-2 2-5 3-7 1-1 3-2 4-2h0c0 1 0 1-1 2l1 1c-2 2-3 4-5 7h-2z" class="k"></path><path d="M366 430h1c1 1 1 1 2 1 1 1 1 1 2 1v-1h1c2 1 4 3 6 5v1 1l-1-1h-2 0c-4-1-7-4-9-7z" class="K"></path><path d="M382 244c-2-3-2-7-2-11 3 0 5-1 7 0-3 3-4 6-5 11zm77 339c1 2 2 4 2 6 0 3 0 6 1 10l-1 1 1 1-1 1c-1 0-1 0-2 1l-1-1c1-2 1-3 1-4 1-1 0-3 0-4-1-1 0-3 0-4v-7z" class="o"></path><path d="M384 346l1-1v2l3 6h0v-2h1l-1-1v-2c-1 0-1-1-1-2v-1l4 11c1 2 2 4 2 7v1c-3-6-7-11-9-18z" class="L"></path><path d="M431 618c2-1 4-1 6-2-2 3-4 9-7 10h-3v-2c0 1 1 1 1 1 1 0 1 0 1-1 0-3 0-3 2-6z" class="n"></path><path d="M378 346l-1 1c-4 0-6-1-9-3l-2-1h-1c-2-2-4-6-5-8 2 1 3 2 4 4 4 3 7 6 12 7h1 1z" class="J"></path><path d="M761 128v-1c5-5 11-11 18-11l-13 9c-2 2-3 2-5 3z" class="O"></path><path d="M494 645c-1-2-3-7-1-10 1-2 2-3 4-3-1 2-1 5 0 8-1 1-2 3-3 5z" class="g"></path><path d="M482 817c-1-1-1-2-1-3l-1-2v-4c0 1 0 2 1 3v2h1v-1-3c-1-1-1-1-1-2 1-1 0-2 1-3 1 7 1 12 6 17l-1 3c-2-2-3-4-4-7h0-1z" class="i"></path><path d="M453 586c1 1 0 1 2 1 0 1 0 1-1 3v1l-1 3c0 1 0 2-1 4h0c-1 1-2 1-2 2l-1-1v1l-1-1v1l-1-1c3-4 4-8 6-13z" class="L"></path><path d="M493 842l2 1c1 2 2 5 3 7l-1 5v-2c-2 1-2 2-3 3v3c0-4 0-7-1-11 1-2 1-4 0-6z" class="O"></path><path d="M761 123c-4 3-7 6-9 11-1-2-1-7-2-10 2 1 4 0 6 0l5-1z" class="L"></path><path d="M657 198c2 2 5 3 8 4l3 2-1 1c0 2 0 2 1 4 0 1 1 1 1 3l-3-4h-1c-1 0-3-4-7-6h1c0-2-2-2-2-4z" class="h"></path><path d="M658 202c4 2 6 6 7 6l1 3v-1h-1c-2-2-3-3-6-2-1 0-2 1-3 2l-2-1v-2c1-2 3-4 4-5z" class="Ab"></path><path d="M761 128c2-1 3-1 5-3v1h3l-5 4-3 1h1c-1 1-2 2-3 2l-1 3c-1 1-1 2-1 4h-1l-1-1c1-5 2-8 6-11z" class="AO"></path><path d="M474 808c-2-8-4-17-3-26h0c1-3 1-4 3-7v4l-1 2c0 3 1 7 0 10-2 3 0 10 1 13v4z" class="g"></path><path d="M743 135c0 1 0 2 1 4 0 1 0 3 1 4l-1 1-4 14c-2 4-3 8-5 11h-1v-2l1-1c3-5 5-9 6-15 1-4 0-7 1-11 1-1 1-3 1-5z" class="O"></path><path d="M321 321l-10-23c2 3 3 4 6 6l4 3h0l-2 1c-1 4 0 6 2 10v3z" class="W"></path><path d="M417 608c2 4 3 8 5 13s4 11 5 16h0l-1-1c0-1-1-2-2-3h-1c0-1-1-3-1-4-1-2-1-5-1-7l-3-9c-1-2-1-3-1-5z" class="L"></path><path d="M497 661l2 2v1h0l3 6c1 2 2 6 3 8l-1 1c-2-2-3-5-5-7h0v-1c-2-1-4-5-4-6v-1l5 6-3-9z" class="K"></path><path d="M689 284c1 2-1 6-2 8h0l-4 5-2 4-4 1c2-7 9-12 12-18z" class="AJ"></path><path d="M643 210h1v-1c1 1 1 0 1 1 0 2 1 3 2 5l1-1h0c1 7 3 15 4 22-2-4-3-9-4-14-2-4-4-7-5-12z" class="b"></path><path d="M362 464c0-1-1-2-1-2-1-2-1-3-2-4l-1-1 1-1 2 3c0 1 1 2 2 3l3 6 5 15c-4-5-7-13-9-19z" class="L"></path><path d="M341 350l1 1-2 1c0 3-1 8 0 11 0 1 0 2 1 2 1 2 0 4 1 6 0 0-1 1-1 0-1-3-6-19-5-21h5z" class="h"></path><path d="M321 307c1 0 1 0 2 1 1 0 1 0 2 1h1-1c-2 3-3 6-3 10l1 4c-1 0-1 0-2-2v-3c-2-4-3-6-2-10l2-1h0z" class="J"></path><path d="M335 210h2v-2h0l1-1c1 0 2 1 2 2 1 1 0 0 1 2 1 1 2 3 3 4l1-1c1 2 2 6 3 9-4-5-10-8-13-13z" class="G"></path><path d="M458 780l2-2c2 1 3 3 4 5 2 4 2 9 2 14h0-1v-2h0v-1h-1c-2-5-3-10-6-14z" class="Z"></path><path d="M563 734l2 2c-1 3-2 5-2 8-1 5-3 9-2 14v6l-1-1c-1-1 0-3 0-5h-1v-2l1-2v-2l1-2v-3l1-1v-1l-3 3v1c-1 1-2 3-1 4v1 2 1c1 2 0 4 1 7l-2 2c1-5 1-9 0-13 0-4 2-6 4-9 2-4 2-6 2-10z" class="J"></path><path d="M380 178c1 2 1 3 0 4v2c-1 1-2 3-1 4l1 1c0 1-1 5 0 6v1c-2 4 1 8-3 12v-21c1-3 1-6 3-9z" class="d"></path><path d="M713 259c1 0 2 0 3 1-1 0-2 0-4 1l-4 3-6 6-1-1 2-3c-1 0-1-1-2-1-2 3-4 4-6 7 0 1 0 3-2 3l2-4c4-8 10-9 17-11l1-1z" class="Ad"></path><path d="M358 437c-1 0-1-1-1-2-2-3-10-28-9-31l6 19c1 1 1 2 1 3l1 1c2 3 3 4 4 8l1 2v2c-1-1-2-2-3-2z" class="L"></path><path d="M354 423c1 1 1 2 1 3l1 1c2 3 3 4 4 8-2-1-3-2-3-3l-3-9z" class="i"></path><path d="M494 859v-3c1-1 1-2 3-3v2c1 0 1 1 2 2v1 6h0c-1 1-1 2-1 3v2c-1 2-1 3-1 5 0-2 0-4-1-5v1c0-4-1-7-2-11z" class="p"></path><path d="M498 869l-1-5c-1-3 0-4 0-7h1v2l1-1v6h0c-1 1-1 2-1 3v2z" class="O"></path><path d="M353 460l4 1 1 3 5 15c-2-2-3-3-4-5l-6-6h2l-1-2h1c1-2-1-4-2-6z" class="D"></path><path d="M349 449v-1h2v1c0 1 1 2 2 3h0l2 4 2 5-4-1c-2-2-4-3-6-4h0v-1c-1-1-1-1 0-2 1-2 2-2 2-4z" class="m"></path><path d="M347 455l8 1 2 5-4-1c-2-2-4-3-6-4h0v-1z" class="a"></path><path d="M473 791v2c0 2 0 5 1 8 0-1 1-2 1-2 1 0 1 0 1-1h1c0 2 0 3-1 4v1l2 2c1-1 1-2 1-3v4 2l-1 1c-1 1-1 2-1 3v3l-1 2c0-2-3-7-2-9v-4c-1-3-3-10-1-13z" class="i"></path><path d="M477 798c0 2 0 3-1 4v1 3h-1l-1-5c0-1 1-2 1-2 1 0 1 0 1-1h1z" class="g"></path><path d="M454 569v-3h1v1c1 0 1 0 1 1 2 1 4 9 5 12 1 6 3 13 1 19-1-4-1-7-1-10 0-2-1-4-2-6 0-4-1-7-2-10l-1-1c0-1-1-2-2-3z" class="Z"></path><path d="M584 648c0 2-4 12-4 12l-3 3c-2 2-6 5-9 5l-1-1c1-1 1-1 1-2h0c8-5 12-9 16-17z" class="O"></path><path d="M414 457c2 3 4 4 7 5 4 6 6 13 8 19h0c-2-1-1-2-2-4-2-3 0 2-1-2h0l-1 1v-2c-1-2-1-2-3-4-2-4-5-7-8-10v-3z" class="b"></path><path d="M638 161l2-2 1-3v7 1c1 2 6 6 8 7h1c0 2 1 3 2 4 1 0 2-1 2-1l-2 6c-6-5-10-12-14-19z" class="k"></path><path d="M644 202l1-1v-1h1v2s0 1-1 1v1 3 1h0 4c0 2-1 4-1 6h0l-1 1c-1-2-2-3-2-5 0-1 0 0-1-1v1h-1-2c-1 1-2 2-3 4 0-2-1-2 0-4v-2c1-3 3-2 5-3 0-1 1-3 1-3z" class="O"></path><path d="M638 208h1s1 0 2-1c1 1 2 1 2 3h-2c-1 1-2 2-3 4 0-2-1-2 0-4v-2z" class="W"></path><path d="M383 173c2-2 3-4 4-6l1-1c1-2 2-2 3-3-4 6-8 12-9 20-1 6-1 10 0 15v1c1 0 1 1 1 3l2 4c0 1-2 4-3 4-1-4-1-8-1-12-1 0-1-1-1-2 1 0 1-2 0-2v-1-5-3c1-1 1-2 1-2v-2c1-1 1-2 1-3s0-1 1-2v-3z" class="K"></path><defs><linearGradient id="w" x1="539.736" y1="827.199" x2="546.478" y2="837.849" xlink:href="#B"><stop offset="0" stop-color="#a79791"></stop><stop offset="1" stop-color="#c5b4a8"></stop></linearGradient></defs><path fill="url(#w)" d="M544 823v1c1 0 1 0 1-1 1 1 1 2 1 3 0 4-1 8-2 11-1 4-3 8-4 12-1 0-1-1-2-2 3-7 4-17 6-24z"></path><path d="M541 776c-1 5 5 6 5 11 0 1 0 1-1 2h-1c0-5-4-8-5-11-1-2-1-4-1-5l-1-1v-1-1-1c0-2-1-5 0-7v-2c0-1 0-2 1-2v-2-1c1-1 0-4 0-5 1-2 1-2 1-3s0-1 1-2h0v-1-2c0-2 1-4 3-6l-1 5c-2 4-3 8-3 12-1 5-2 11-1 16 0 3 1 5 3 7z" class="K"></path><path d="M757 140c0-2 0-3 1-4h2c0 1-1 1-1 2v2c0 1 2 1 1 2 1 1 1 2 3 2h0c1 2 1 3 1 4-2 1-4 3-5 4v-1c-2-1-1-3-2-4l-1-2v-1l-1-2v-3l1 1h1z" class="J"></path><path d="M756 140h1v1c1 2 2 3 3 4h1v1c-1 1-1 1-3 1 0-1-1-1-1-2v-2c-1-1-1-2-1-3z" class="AP"></path><path d="M757 140c0-2 0-3 1-4h2c0 1-1 1-1 2v2c0 1 2 1 1 2-1 0-2 0-3-1v-1z" class="AT"></path><path d="M505 654l4 20c-2-2-2-4-2-7l-1-1h-1v1c1 1 1 1 1 2v1 1c1 2 0 2 0 4h0c-2-5-4-10-5-15h-1c1-1 1-2 2-2 2-1 2-2 3-4z" class="W"></path><path d="M358 437c1 0 2 1 3 2v-2c3 2 6 2 9 5h1c-1-1-1-2-2-3l1-1c2 1 3 2 5 1h1 0c2 0 3 1 4 1l-2 1-8 2c-2 1-3 1-5 1-3-2-5-3-7-7z" class="G"></path><path d="M370 438c2 1 3 2 5 1h1v1l-5 2c-1-1-1-2-2-3l1-1z" class="J"></path><path d="M361 437c3 2 6 2 9 5-1 1-2 1-4 1s-4-3-5-4v-2z" class="i"></path><path d="M482 817h1 0c1 3 2 5 4 7l1 2v3c2 4 3 9 5 13 1 2 1 4 0 6-4-10-8-20-11-31z" class="b"></path><path d="M347 285c2 2 4 4 8 4 3 0 5-1 7-3 1 0 1-1 2-1h1 0l1-1v2l-1 2c-1 1-3 2-5 3h-1c-2 1-5 1-7 1h-1l1 1c0 3-1 5-2 8l-1 3v-4c1-3 1-5 1-8v-1-1l-1 1h0c-1-2-2-4-2-6z" class="W"></path><path d="M537 843c-1 2 0 5-1 7v2 1 2l1-1c0-1-1-1 0-2s0-3 1-5c1 1 1 2 2 2 0 3 0 5 1 7 0 3 3 7 2 9-1 0-1-1-2-1l-1-1c-1 0-2 2-3 2h-2v-1h1 0 1c0-2-1-4-1-5-1-5-1-12 1-16z" class="o"></path><path d="M397 471c0 1 0 2-1 3l-2 1c-3 2-4 4-7 4l-2 1h0c-1 2-2 3-2 4-1 1 0 6-2 7-1-4-1-9 0-13 1-3 7-3 10-4l6-3z" class="k"></path><path d="M360 445l2 2c1 0 2 0 3 2v2h0v2c-1 6 1 10 1 15l-3-6c-1-1-2-2-2-3v-1c0-1-1-3-1-4-2-3-3-5-4-8 3 2 4 4 6 7 0-2-1-3-2-5-1-1 0-2 0-3z" class="o"></path><path d="M360 445l2 2s2 1 2 2v6h0l-2-2c0-2-1-3-2-5-1-1 0-2 0-3zm86 111h1l-2-5v-1c2 3 3 5 5 7 0 2 1 4 2 6l-3-3h0v2c0 1 0 1 1 2v7c0 7-1 13-3 20v-1c0-1 0-2 1-3 1-5 1-10 0-15l-1-7h0c1-3-1-6-1-9z" class="L"></path><path d="M584 125c-4 0-9 1-13 1-1 1-1 1-2 0h2c1-1 2-1 3-2l6-4c3-1 5-3 7-6l2 1v1c1 0 4-1 5-1-2 2-4 2-7 4v1c-1 1-3 1-3 3v2h0z" class="K"></path><defs><linearGradient id="x" x1="434.812" y1="606.215" x2="445.912" y2="608.838" xlink:href="#B"><stop offset="0" stop-color="#c7b4a5"></stop><stop offset="1" stop-color="#d8d1cb"></stop></linearGradient></defs><path fill="url(#x)" d="M447 599l1 1v-1l1 1v-1l1 1c0-1 1-1 2-2-3 6-8 11-13 16l-2 2c-2 1-4 1-6 2h-3v-1c2 0 3-1 5-2 4-2 7-5 9-9l5-7z"></path><path d="M341 350h0c2-1 5-1 7-1 3-1 5-1 8-1 1 1 3 1 5 1-7 0-13 2-17 7-2 2-3 5-4 7-1-3 0-8 0-11l2-1-1-1z" class="o"></path><defs><linearGradient id="y" x1="594.603" y1="109.312" x2="602.464" y2="116.777" xlink:href="#B"><stop offset="0" stop-color="#bbaba7"></stop><stop offset="1" stop-color="#d0c9c0"></stop></linearGradient></defs><path fill="url(#y)" d="M587 114l5-8c0 1 0 3 1 4 7 1 14 2 21 1h0c-4 2-8 3-12 3-3 0-5 0-8 1-1 0-4 1-5 1v-1l-2-1z"></path><path d="M557 651v1h1c0 3-1 8-1 11h0c0 1-1 2-1 3v2l-1 1v2l-1 1c0 1 0 2-1 3v1h0c0 1 0 2-1 3v2c1-1 0-2 2-3l-1-1 1-1v-1l1-5 1-1v-1c0-1 1-2 1-2v-2c1-1 0-1 1-2 0-1 0-2 1-3-3 12-6 23-11 34h-1l3-10 2-5c1-5 3-8 1-13l2-6 2-8z" class="o"></path><path d="M575 637c1-3 2-8 5-11 0-1 1-3 2-3l-8 24c-1 4-2 8-4 11 0 1-1 3-2 3-1 1-2 1-2 1h-1v-1c1 0 2 0 2-1l2-8c1-3 2-7 4-9 0-2 1-4 2-6z" class="g"></path><path d="M343 456h4c2 1 4 2 6 4 1 2 3 4 2 6h-1l1 2h-2c-4-4-10-7-15-11 2-1 2-1 4-1h1z" class="C"></path><path d="M343 456h4c2 1 4 2 6 4 1 2 3 4 2 6h-1l-4-5c-1 0-2 0-3-1-2-1-4-2-5-4h1z" class="Y"></path><defs><linearGradient id="z" x1="648.515" y1="128.924" x2="637.308" y2="125.015" xlink:href="#B"><stop offset="0" stop-color="#80665a"></stop><stop offset="1" stop-color="#a48f83"></stop></linearGradient></defs><path fill="url(#z)" d="M632 146c-1-6 1-12 4-17 4-6 9-11 16-12l1 1h-1l-1 1-1 1v1l-2 1c-5 2-8 6-11 11-1 2-4 5-4 8v5h-1z"></path><path d="M640 194c1 0 1 0 1 1l3 3v4s-1 2-1 3c-2 1-4 0-5 3v2c-1 2 0 2 0 4v1l-1 1c-1 0-2 1-2 2-2-2-1-4-1-6 1-2 0-4 0-6 1-2 4-3 5-5 2-2 1-5 1-7z" class="i"></path><path d="M528 764c-4-10-8-20-11-31-1-5-2-12-3-17-1-2-1-2-1-3 2 3 2 6 3 10l6 19 1 2c0 2 1 4 1 6v1c2 3 5 7 5 11l-1 2z" class="h"></path><path d="M423 633h1c1 1 2 2 2 3l1 1h0c1 3 2 5 2 8l4 20 2 9c1 2 2 5 2 8l-11-35c1-4-2-10-3-14z" class="W"></path><path d="M403 571l1-1c2 4 4 10 5 15 0 1 1 2 1 4 1 3 3 7 5 10 0 2 3 7 2 9 0 2 0 3 1 5l3 9c0 2 0 5 1 7 0 1 1 3 1 4 1 4 4 10 3 14l-23-76z" class="b"></path><defs><linearGradient id="AA" x1="337.204" y1="214.847" x2="340.005" y2="228.711" xlink:href="#B"><stop offset="0" stop-color="#917b6d"></stop><stop offset="1" stop-color="#b29c8f"></stop></linearGradient></defs><path fill="url(#AA)" d="M329 218c0-1 0-3 2-3 8 1 14 7 19 14l2 4c-1-1-2-2-2-3-1 0-1 0-1-1-1 0-2-1-2-1 0 1 0 2 1 3v1l-3-3c-1-2-4-3-6-5h1v-1c-1-2-2-2-4-3-2 0-2 0-4 1-2 0 0 0-1-1l-2-2z"></path><path d="M685 123c0-2 0-4 1-5 2-2 4-2 6-2 3 0 7 1 9 4 1 1 1 2 0 3l-1 6v3h-1c0-3 0-6-1-8-2-3-6-3-9-3-1 1-2 2-3 2h-1z" class="AJ"></path><path d="M416 425l-3-14-9-24c-1-4-2-9-3-13l-2-11c0-1-1-2-1-3l20 69h0l-2-4z" class="L"></path><path d="M369 213c1-3 1-4 3-5l2-1c0 2 1 5 0 6l-1 1c-1 3 0 5 0 9 2 7 4 14 4 22v1c-1 1-1 3-1 4 0-5-2-9-3-14v-8l-4-15z" class="K"></path><path d="M485 647l3 1c1 2 1 3 3 4v-3h-1l2-2 5 14 3 9-5-6c-1-3-4-8-7-10s-5-3-8-3v-1h3 1 0v-1h-1v-1h2v-1zm200-524h1c1 0 2-1 3-2 3 0 7 0 9 3 1 2 1 5 1 8h1v-3l1 4h0l1-1v1l1 1c-1 0-4 0-5-1-4-1-6-6-10-6l-1-1v1c-2 0-2 0-3-1 1-1 1-2 1-3z" class="h"></path><defs><linearGradient id="AB" x1="733.735" y1="178.554" x2="741.877" y2="195.156" xlink:href="#B"><stop offset="0" stop-color="#61463d"></stop><stop offset="1" stop-color="#8c7565"></stop></linearGradient></defs><path fill="url(#AB)" d="M741 174h3l2 2c-7 8-10 19-14 28 0 2-1 4-2 5h0c1-5 1-10 3-14 1-8 4-14 8-21z"></path><path d="M601 118c1-1 2 0 4 0-1 1-2 2-2 3-1 3 1 6 2 9v1c1 1 2 2 4 3l1-1c-1 0-1-1-2-1h0c0-2 0-2 1-3h4 3l1 1c2 0 2 0 4 1h1v1l-1-1c-4-1-7-1-10-1v1c1 0 1 1 1 2s0 1-1 2l-3-1c1 1 0 1 1 1l2 2h0c-2 0-3-1-4-1-2-1-4-2-5-3v-1c-2-1-3-2-4-3h1c1 0 1 1 2 1 1 1 1 1 2 1h1c-1-1-1-2-2-2-3-2-2-6-4-8l-1-1c-3 0-4 0-6-1 3-1 7-1 10-1z" class="d"></path><path d="M591 119c3-1 7-1 10-1 1 4 0 7 1 11-3-2-2-6-4-8l-1-1c-3 0-4 0-6-1z" class="AJ"></path><defs><linearGradient id="AC" x1="736.839" y1="181.789" x2="745.045" y2="197.488" xlink:href="#B"><stop offset="0" stop-color="#a89789"></stop><stop offset="1" stop-color="#c5b5b3"></stop></linearGradient></defs><path fill="url(#AC)" d="M749 175l1 2c-7 8-12 17-15 27h-1-2c4-9 7-20 14-28 1 0 2 0 3-1z"></path><path d="M658 190h1c0-1 0-2-1-3 0-1 0-2 1-3v2h1c0-3 0-10 1-13l1 2c0 3 0 6 1 9 0 2 1 4 1 6 1 2 5 6 5 7 1 1 4 2 4 3l-3-1c-8-2-15-8-18-15 2 1 3 4 5 6h1z" class="O"></path><path d="M647 188c1 3 3 6 4 9 0 4-1 7-2 11h-4 0v-1-3-1c1 0 1-1 1-1v-2h-1v1l-1 1v-4-4l-3-3v-1l2-1h1 0l2-1h1z" class="p"></path><path d="M647 188c1 3 3 6 4 9l-3 3c-1-2-2-4-4-5v-1l-3-3v-1l2-1h1 0l2-1h1z" class="AJ"></path><path d="M643 189h1 2c1 2 1 5 2 7-2-2-5-4-5-7z" class="Ab"></path><path d="M487 640l2-1 3 8-2 2h1v3c-2-1-2-2-3-4l-3-1v1h-2v1h1v1h0-1-3v1c-2 0-3 0-4-1v-3l1-3c3-1 7-2 10-4z" class="p"></path><path d="M484 645c2 0 3 0 4 2v1l-3-1s0-1-1-2zm3-5l2-1 3 8-2 2c0-3 0-4-2-6-1-1-1-2-1-3z" class="AO"></path><path d="M476 647l1-1v-1h1l1 1c2 1 4 0 5-1 1 1 1 2 1 2v1h-2v1h1v1h0-1-3v1c-2 0-3 0-4-1v-3z" class="g"></path><defs><linearGradient id="AD" x1="464.79" y1="758.031" x2="484.315" y2="759.047" xlink:href="#B"><stop offset="0" stop-color="#bfab9e"></stop><stop offset="1" stop-color="#d7d2cb"></stop></linearGradient></defs><path fill="url(#AD)" d="M459 742v-1l1 1h0v-1l1-1c2 7 3 11 8 16 2 2 2 2 4 3h0 5v1l1-1c1-1 1-2 2-2h1c1-1 1-2 3-2v2l-1 1c0 1-1 2-3 3-2 2-6 3-9 2-7-2-10-15-13-21z"></path><path d="M433 517c0 4 1 10 4 13s8 4 13 3l-7-27h0l10 32-1 1c-1-2-1-2-3-3-6 0-9-1-14-4-3-4-6-10-5-15l2-1 1 1z" class="g"></path><defs><linearGradient id="AE" x1="542.83" y1="807.211" x2="551.67" y2="818.789" xlink:href="#B"><stop offset="0" stop-color="#826c62"></stop><stop offset="1" stop-color="#a5968e"></stop></linearGradient></defs><path fill="url(#AE)" d="M544 823c2-10 3-23 11-32 1-2 5-5 8-5h-1c-1 3-5 5-6 8-6 9-8 22-10 32 0-1 0-2-1-3 0 1 0 1-1 1v-1z"></path><path d="M702 224v-2-2-1h1 1c0 1 0 2 1 2v1l1 3v1c0 2-1 4 1 6 0 3-1 4-2 7h0c-1 3-3 5-4 7-1 0-3 0-4-1-2-1-3-3-4-5l1-1c2 1 4 2 6 1s2-3 2-4v-5-7z" class="W"></path><path d="M702 224v-2-2-1h1 1c0 1 0 2 1 2v1l1 3c-1 6 0 11-4 16h0v-1c1-1 1-2 1-4h-1v-5-7z" class="K"></path><path d="M723 240c1 2 2 3 3 4v1h-1c-3 1-6 2-7 5s-1 6-1 9v1h-1c-1-1-2-1-3-1h1c1 0 1 0 2-1-3-3-1-3-1-6l-1-1c-1 1-2 2-4 3h-1c-2 1-4 1-5 1h-1-1-6c3-1 7-2 11-4s8-5 12-7l4-4z" class="o"></path><path d="M587 120l4-1c2 1 3 1 6 1l1 1c2 2 1 6 4 8 1 0 1 1 2 2h-1c-1 0-1 0-2-1-1 0-1-1-2-1h-1c1 1 2 2 4 3v1l-8-5c-1 0-1-1-2-1h-1c-1 0-1 1-2 1-1-1-2-3-2-5-1 1-2 1-2 2h-1v-2c0-2 2-2 3-3z" class="W"></path><path d="M587 120l4-1c2 1 3 1 6 1l1 1h-3c-3 1-6 1-8 2-1 1-2 1-2 2h-1v-2c0-2 2-2 3-3z" class="k"></path><path d="M619 147c9 8 11 18 17 27 3 5 7 9 11 14h-1l-3-3c-1-2-3-3-5-5-1-1-2-3-4-4l-2-4h0c0-1-1-2-2-2h0c0-2 1-2 0-4h-1c-3-2-7-9-9-12l-5-5h0c2-1 3 0 4-2z" class="h"></path><path d="M563 744l1-1c-1 4-1 6 0 10l1 2 2 4c1 1 2 2 4 2l-3 2c-3 3-5 9-6 13v5c-1-1 0-1-1-2v-3c-2-3-1-9 0-12v-6c-1-5 1-9 2-14z" class="G"></path><path d="M567 759c-1 1-2 2-2 4h0 0c-1-2-1-6 0-8l2 4z" class="h"></path><defs><linearGradient id="AF" x1="377.964" y1="202.635" x2="371.757" y2="209.616" xlink:href="#B"><stop offset="0" stop-color="#b29e94"></stop><stop offset="1" stop-color="#ccc5b7"></stop></linearGradient></defs><path fill="url(#AF)" d="M377 187v21 17c-1-4-1-7-3-11v-1c1-1 0-4 0-6l-2 1c-2 1-2 2-3 5 0-2 1-5 1-7l3-7-1-5v-1h-1l5-5 1-1z"></path><path d="M376 188c0 2 0 3-1 5v2l-2 4-1-5v-1h-1l5-5z" class="Ab"></path><path d="M693 275c2 0 2-2 2-3 2-3 4-4 6-7 1 0 1 1 2 1l-2 3 1 1c-4 6-7 12-9 19-1 0-2 6-2 6-2 0-3 2-4 3h-1c1-2 2-3 2-5l-1-1h0c1-2 3-6 2-8 0-1 4-7 4-9z" class="AT"></path><path d="M755 154l3 2 1 2c0 2 0 3-1 5l-3 3c0 1 0 3 1 3l-4 7-2 1-1-2-1-3c0-1-1-3-2-4 0-1 1-1 1-2 1-1 2-1 2-2l3-5c1-2 2-3 3-5z" class="Ad"></path><path d="M753 166c-1-1-1-1-1-2s0-2 1-4h1l1 2-2 4z" class="Ab"></path><path d="M746 168c0-1 1-1 1-2 1-1 2-1 2-2l2 8h-3c0-1-1-3-2-4z" class="i"></path><path d="M759 158c0 2 0 3-1 5l-3 3c-1 1-1 1-1 2l-1-2 2-4 4-4z" class="AJ"></path><path d="M754 168c0-1 0-1 1-2 0 1 0 3 1 3l-4 7-2 1-1-2-1-3h3l1 2 1-1v-4l1-1z" class="AO"></path><path d="M748 172h3l1 2h-1l1 2-2 1-1-2-1-3z" class="K"></path><path d="M522 742l2 2h1v1l3 6c1 1 2 2 2 4 1 1 1 1 1 3l1 1v1h0v1h1v-1c0-2 0-3 1-4-1-1-1-1 0-2 0-1 0-2 1-2v-3l2-5h0c0 6-2 12-3 18-1 3 0 8 0 11 0 1-1 1-1 2l-2-1v-1l-3-9 1-2c0-4-3-8-5-11v-1c0-2-1-4-1-6l-1-2z" class="L"></path><path d="M523 744v1l1 1c2 3 2 5 4 8 1 2 2 4 2 7 1 1 2 3 3 4 0 2 0 5-1 7l-1 1-3-9 1-2c0-4-3-8-5-11v-1c0-2-1-4-1-6z" class="n"></path><defs><linearGradient id="AG" x1="590.704" y1="132.878" x2="593.689" y2="139.494" xlink:href="#B"><stop offset="0" stop-color="#7a635d"></stop><stop offset="1" stop-color="#907b6b"></stop></linearGradient></defs><path fill="url(#AG)" d="M568 132c2-1 4-2 7-2h0c2 0 5-1 7 0 3 0 6 2 9 3 8 3 17 6 24 12l4 2c-1 2-2 1-4 2h0l-7-5c-6-5-18-7-25-8 2-1 4 0 6-1h2v-1c-4-3-13-4-18-3-1 0-3 1-5 1h0z"></path><path d="M665 202c4 0 6 1 10 2s11 1 16-1h1 0c-2 2-3 2-6 2-1 0-1 1-2 1h-4v2c1 0 2 1 3 1-1 1-1 1-1 2l1 1v2l1 1h0c-1 3-1 8 0 11v1l1 2c0 1 0 1 1 2v6c-2-7-3-12-3-19v-2l-2-1-2-2c-2-1-3-2-5-2v1c-2 0-3-1-4-2 0-1-2-4-3-5l1-1-3-2z" class="K"></path><path d="M668 204l3 1c4 2 9 3 11 7l1 4-2-1-2-2c-2-1-3-2-5-2v1c-2 0-3-1-4-2 0-1-2-4-3-5l1-1z" class="k"></path><path d="M668 204l3 1-1 1c0 1 0 1 1 2h2v1c1 1 2 1 3 1 2 1 2 2 4 2l1 3-2-2c-2-1-3-2-5-2v1c-2 0-3-1-4-2 0-1-2-4-3-5l1-1zm-10-14c0-1-1-1-1-2-2-8 0-19 5-26 2-3 5-7 10-8v1c-1 1-1 2 0 4-2 1-3 2-4 4-1 0-1 0-2 1-2 2-3 5-4 8 0 0 0 1-1 1-1 3-1 10-1 13h-1v-2c-1 1-1 2-1 3 1 1 1 2 1 3h-1z" class="p"></path><defs><linearGradient id="AH" x1="649.471" y1="213.776" x2="662.13" y2="227.184" xlink:href="#B"><stop offset="0" stop-color="#957b72"></stop><stop offset="1" stop-color="#b4a799"></stop></linearGradient></defs><path fill="url(#AH)" d="M654 209l2 1c1-1 2-2 3-2 3-1 4 0 6 2h1v1c2 1 3 4 4 6h-1c-2-1-3-2-5-3l-1-1c-1 0-2 0-3-1-1 1-1 1-1 2l-1 1c-1 2-1 4 0 7-1 3 0 5 1 9h-1c-1 2-1 3-2 4-1-9-5-18-2-26z"></path><path d="M652 117h1l9-3c5-2 14-1 18 2 2 1 3 2 4 4v1l-1 1c-1-1-2 0-3-1s-1-1-3-1v-1c-3-2-8-2-11-1h-4c-4 0-9 3-14 4l2-1v-1l1-1 1-1h1l-1-1z" class="AJ"></path><path d="M568 132h0c2 0 4-1 5-1 5-1 14 0 18 3v1h-2c-2 1-4 0-6 1h-1-5c-4 0-11 1-14 4-1 0-1 0-2-1-1 1-3 3-4 3l-2 2v-2c1-3 6-7 9-8 1-1 2-2 4-2z" class="W"></path><path d="M561 139c1-1 2-2 4-3 7-4 17-4 24-1-2 1-4 0-6 1h-1-5c-4 0-11 1-14 4-1 0-1 0-2-1z" class="Ab"></path><path d="M559 659c1-1 1-2 2-3-2 9-5 18-8 27-2 6-4 11-6 17l-6 20c-1 4-2 9-4 13 0 2-1 3-2 5 0-2 0-1 1-2v-2-1c1-1 1-2 2-4h-1s-1 0-1 1 0 1-1 2l-2 8c0 1-1 1-1 2l-1 1v-2h0l10-31c2-2 2-3 3-5l3-12h1c5-11 8-22 11-34z" class="W"></path><path d="M404 333v-1l1-1c2 3 2 6 3 10l4 23c1 5 2 11 4 16 0 1-1 2-1 2l-6-18-3-10-6-20v1c2 1 2 2 2 4 1 1 1 3 2 4 0-1-1-1 0-2v-3c-1-1-1-2-1-3l1-2z" class="N"></path><path d="M406 345c2 6 2 12 3 17v2l-3-10v-9z" class="C"></path><path d="M404 333l2 12v9l-6-20v1c2 1 2 2 2 4 1 1 1 3 2 4 0-1-1-1 0-2v-3c-1-1-1-2-1-3l1-2z" class="S"></path><defs><linearGradient id="AI" x1="419.878" y1="412.793" x2="428.362" y2="395.276" xlink:href="#B"><stop offset="0" stop-color="#340100"></stop><stop offset="1" stop-color="#520905"></stop></linearGradient></defs><path fill="url(#AI)" d="M416 380h0l3 9c2 4 4 8 6 13 1 2 3 4 4 6l1 2c2 1 3 2 5 3v-1l4 4 1 1c-1 0-1 0-2-1-2-1-2-1-3-1l-1 2h1-1-2-1-1c-2 0-3-1-4-1l-11-34s1-1 1-2z"></path><path d="M430 410c2 1 3 2 5 3v-1l4 4 1 1c-1 0-1 0-2-1-2-1-2-1-3-1-2 0-3-1-4-2-1-2-1-2-1-3z" class="D"></path><path d="M658 222c-1-3-1-5 0-7l1-1c0-1 0-1 1-2 1 1 2 1 3 1l1 1c2 1 3 2 5 3h1c1 3 2 6 1 9-1 1-1 1-2 1s-3 0-4-1h0c-1 1-1 1-2 0l-1 1-1 1 1 1-4 2h1c-1-4-2-6-1-9z" class="g"></path><path d="M660 220l1-1h0c1 0 2 1 3 0v1c1 1 2 1 3 1 1 1 1 1 1 2l-2 1c-1-1-3-1-4-2s0-1-2-2z" class="L"></path><path d="M660 220c2 1 1 1 2 2s3 1 4 2l2-1c0 2 0 3 1 4-1 0-3 0-4-1h0c-1 1-1 1-2 0l-1 1-1 1 1 1-4 2h1c0-4 1-7 1-11z" class="G"></path><path d="M658 222c-1-3-1-5 0-7l1-1c0-1 0-1 1-2 1 1 2 1 3 1l1 1c2 1 3 2 5 3h1c1 3 2 6 1 9h-1v-1-3c-2-2-4-4-6-4h-1c-2-1-3-1-5-1v5z" class="O"></path><path d="M658 217c1-1 1-1 3-2h3c2 2 5 3 6 7-2-2-4-4-6-4h-1c-2-1-3-1-5-1z" class="h"></path><defs><linearGradient id="AJ" x1="520.312" y1="733.343" x2="509.78" y2="751.419" xlink:href="#B"><stop offset="0" stop-color="#bdac9c"></stop><stop offset="1" stop-color="#dcd9d7"></stop></linearGradient></defs><path fill="url(#AJ)" d="M520 760l-10-13c-1 0-1-1-2-1 0-2-3-6-3-8 0-1 5-6 5-8 1-1 2-3 2-5l4 19-1 1h0l1 3c1 4 4 7 4 12z"></path><path d="M369 416c3 3 6 7 10 10 14 11 31 21 42 36-3-1-5-2-7-5l-4-4 2 6h-1l-4-7c-1-1-1-1-1-2-2-3-9-10-13-11-2 0-3-1-4-2l-2-2c-1 0-1 0-2-1-1 0-2-1-3-1l-4-4c-2-5-6-8-10-12l1-1z" class="O"></path><path d="M491 667l1-1c9 14 14 29 17 45 1 4 2 10 1 14s-6 8-9 10v3h0c-1-1-1-1-1-2 0-2-1-2-1-3 1-3 3-4 5-6s3-5 3-8l-1-1-1-1h1v-1-7-5c-1-8-5-15-7-21-3-6-5-11-8-16z" class="h"></path><path d="M352 406h1c0 2 1 3 2 4 3 7 5 15 11 20 2 3 5 6 9 7h0l1 2h0-1c-2 1-3 0-5-1l-1 1c1 1 1 2 2 3h-1c-3-3-6-3-9-5l-1-2c-1-4-2-5-4-8l2 1c-1-1-2-2-2-3l1-1c-1-1-1-1-1-2l-1-1h0v-3l-3-12z" class="b"></path><path d="M358 425c3 3 5 6 8 8l9 6c-2 1-3 0-5-1-4-1-9-6-11-10-1-1-1-1-1-3z" class="i"></path><path d="M355 418c0 1 3 6 3 7 0 2 0 2 1 3 2 4 7 9 11 10l-1 1c1 1 1 2 2 3h-1c-3-3-6-3-9-5l-1-2c-1-4-2-5-4-8l2 1c-1-1-2-2-2-3l1-1c-1-1-1-1-1-2l-1-1h0v-3z" class="K"></path><path d="M361 349c9 3 23 6 28 15l1 2c-1 0-1 0-2-1h-2l-4-4c-2-1-3-1-4-1-2-1-3-2-4-2-3-2-5-3-7-4-6 0-14-1-19 2-2 3-5 5-6 8l-1 1c-1 0-1-1-1-2 1-2 2-5 4-7 4-5 10-7 17-7z" class="p"></path><path d="M367 354c7 1 10 2 15 7-2-1-3-1-4-1-2-1-3-2-4-2-3-2-5-3-7-4z" class="h"></path><path d="M702 197c1-1 4-4 5-4l4-2c1 2 0 3-1 5-2 4-7 7-9 11-1 0-1 0-1-1l-1 1c-1 2-3 4-5 6v1c-2 0-3 0-4 1-2 2-3 3-4 5v1h0v-2-1c1-4-1-6-1-10 3-2 6-3 10-4l7-7z" class="b"></path><path d="M702 197c0 2-1 3-2 4-3 3-7 6-9 9-2 2-2 4-4 6v-1-1c2-2 0-6 5-8h1c1-1 1-2 2-2l7-7z" class="O"></path><path d="M520 760c0-5-3-8-4-12l-1-3h0l1-1 9 21c1 3 2 5 3 8 1 1 1 2 2 3v1c1 1 1 0 1 2 0 1 1 2 1 3l8 12c1 2 3 4 4 5 0 1 0 6-1 8v1c0-3 0-6-1-8-1-3-4-4-5-7-1-1-2-2-3-4h0c-1-2-2-5-3-7-1 1 0 2 0 3h-1l-1 1c-2-1-2-1-3-3v-1-3h0c0-3-2-6-4-8l-6-11 2 1v1c1 1 2 3 3 4 0 1 1 1 2 2v-1c-1-1-1-1-1-2-1-1-2-2-1-4l-1-1z" class="n"></path><path d="M516 760l2 1v1c1 1 2 3 3 4 0 1 1 1 2 2v-1c-1-1-1-1-1-2-1-1-2-2-1-4 3 5 6 12 6 17l-1 1c0-3-2-6-4-8l-6-11z" class="i"></path><path d="M371 229c1 3 0 7 0 11s1 8 1 12c0 5 0 10 1 14l3 16c2 2 2 6 3 9 0 3 2 7 3 10v5 4c-2-3-3-7-4-10-4-13-6-28-11-41l1-2s0 1 1 1c0-2-1-4-1-7l1 2c0 2 1 3 1 5v1-4c2-6-1-14 0-21v-1c1-1 1-3 1-4z" class="W"></path><defs><linearGradient id="AK" x1="472.369" y1="658.418" x2="448.189" y2="674.384" xlink:href="#B"><stop offset="0" stop-color="#dbcec8"></stop><stop offset="1" stop-color="#e5e6e3"></stop></linearGradient></defs><path fill="url(#AK)" d="M460 643c1 0 2 1 3 1l-1 1c2 0 3 0 4 2 0 1-1 2-2 3h0v2c-4 3-6 5-7 10-2 10 1 22 4 32h-1 0c-5-9-8-24-7-35h0c1-2 1-4 2-6v-1c1-3 2-6 5-9z"></path><path d="M436 661h1l1 1v-3h-1l1-1 5 23c1 5 3 9 3 13l1 1c1 3 2 7 3 10l8 26 3 9-1 1v1h0l-1-1v1c-2-3-3-7-4-10l-5-20-14-51z" class="g"></path><path d="M370 206c0 2-1 5-1 7l4 15v8c1 5 3 9 3 14 0 13 1 25 4 37 0-1 0-1-1-2v-2l-2-6c0 2 0 3-1 5l-3-16c-1-4-1-9-1-14 0-4-1-8-1-12s1-8 0-11c0 1 0 3-1 4 0-2-1-4-1-6h1c0-2-1-4-1-5-1-3-2-9-1-12h0c0-2 1-3 2-4z" class="K"></path><path d="M370 206c0 2-1 5-1 7l4 15v8c1 5 3 9 3 14 0 13 1 25 4 37 0-1 0-1-1-2v-2l-2-6c0-4-1-8-1-12-1-9 0-16-3-24-1-4-1-8-1-12v-1c-1-1-1-2-1-3v4c0 1 0 3-1 4 0-2-1-4-1-6h1c0-2-1-4-1-5-1-3-2-9-1-12h0c0-2 1-3 2-4z" class="L"></path><path d="M722 217v4 3 2l-1 1c-1 6-7 13-12 16-3 1-5 3-8 3 1-2 3-4 4-7h0c1-3 2-4 2-7 0-1 1-3 2-4 2-2 5-4 7-5h1c1-2 3-4 5-6z" class="J"></path><defs><linearGradient id="AL" x1="647.451" y1="250.994" x2="647.214" y2="272.519" xlink:href="#B"><stop offset="0" stop-color="#b1a19a"></stop><stop offset="1" stop-color="#cfc6c0"></stop></linearGradient></defs><path fill="url(#AL)" d="M643 228c3 7 5 14 6 22 1 9 1 17 1 26v8c-1 3-1 6-1 8l-1 1v4l-1 1h-1c0-2 0-7 1-8v-4h0c1-2 0-3 0-4v-2l1-1v-4-3l-1 1c-1 0 0 0-1 1v3c-1 1-1 0-1 2v2 1c-1 1 0 3 0 4-1 2 0 3-1 5 0 2-1 3-1 5 0 0-1 1-1 0 0-3 1-8 2-12 1-14 3-30 1-45-1-3-1-7-3-10l1-1z"></path><defs><linearGradient id="AM" x1="432.038" y1="455.895" x2="418.916" y2="457.109" xlink:href="#B"><stop offset="0" stop-color="#d0c6be"></stop><stop offset="1" stop-color="#e2e3e2"></stop></linearGradient></defs><path fill="url(#AM)" d="M416 425l2 4h0c1 2 2 5 3 7l4 14 13 41c0 1 1 2 1 3-1-1-1-2-1-3h-1l1-1h-1c0-1 0-2-1-2v-2-2h-2v1l1 1v2c1 2 0 1 0 3-2-8-5-15-8-22-2-7-6-13-8-20-1-2-2-4-2-7h1v-1c0-6-2-11-2-16z"></path><path d="M478 655l2-1 1 1c3 2 5 3 7 5 1 1 0 1 1 1l3 5-1 1c3 5 5 10 8 16 2 6 6 13 7 21v5 7 1l-1-6c-1 2-2 4-2 6 1 0 1 0 1 1h-1c-1-1-1-1-1-2l-1 3c-1 2-3 5-5 6l-1 1c3-5 5-7 7-12v-1-5c-3-18-10-39-22-52l-2-1z" class="o"></path><path d="M478 655l2-1 1 1c3 2 5 3 7 5 1 1 0 1 1 1l3 5-1 1c-3-3-7-11-11-11l-2-1z" class="G"></path><path d="M285 250c3 1 5 3 8 5 1 1 2 0 3 1 1 0 2 2 2 2h0c-2 0-3 0-4-1h-1c0 3 5 5 6 8 0 2-2 5 0 7 0 1 1 2 1 3l1 1c1 0 1 1 1 2v1c1 1 1 4 1 5v1l-1-1c0 1 0 2 1 3 0 1-1 2 0 4 0 1 0 0 1 2h0c0 1 0 1 1 2v1h0c1 1 0 1 1 2v1c1 1 1 2 1 3h0l3 9c1 1 1 2 1 2v1 2h0c-1-1-4-12-5-14l-3 3v-2-2h0l-1-2c-1-3-2-7-3-11 0-2-2-3-2-5s1-3 1-4l-1-2c1-1 1-2 1-3-1-2-2-5-4-7-2-3-3-4-6-5h-2l-1-1v-2l1-1c1-2 0-5-1-8z" class="K"></path><path d="M285 259c2 0 4-1 6 0 0 0 1 1 1 2-1 0-4 1-5 1 0-1 0-1-1 0l-1-1v-2z" class="AP"></path><path d="M298 274l8 28-3 3v-2-2h0l-1-2c-1-3-2-7-3-11 0-2-2-3-2-5s1-3 1-4l-1-2c1-1 1-2 1-3z" class="I"></path><path d="M603 121v1c1-1 1-1 2-1h1c1-1 2-1 4-2l-1-2c4 0 8 1 12-1v1 1h2l-1 1 3 3h1c2 2 2 5 3 8 0 1 0 2 1 3l-2 4c0-2-2-5-3-6s-2-1-2-2l-3-3h0-1l-2-1h-1c-1-1-2-1-3-2-1 0-1 0-2-1l-1 1v1h1c1 1 1 1 3 1 0 1 2 0 3 1-1 0-2 0-2 1l-1 1h-1v1h-4c-1 1-1 1-1 3h0c1 0 1 1 2 1l-1 1c-2-1-3-2-4-3v-1c-1-3-3-6-2-9z" class="G"></path><path d="M617 122v-1l-5-3c2 0 4-1 6 0l4 1 3 3h1c2 2 2 5 3 8-2-1-2-4-4-5 0-1 0-1-1-1h0-1v2s1 1 1 2h0c-2-2-5-4-7-6z" class="b"></path><path d="M603 121v1c1-1 1-1 2-1h1c1-1 2-1 4-2l-1-2c4 0 8 1 12-1v1 1h2l-1 1-4-1c-2-1-4 0-6 0l5 3v1c-2-1-4-1-6-1h-3c-1 1-1 1 0 3l1 2c-1 1-3 0-4 3v1c-1-3-3-6-2-9z" class="p"></path><path d="M449 536c2 1 2 1 3 3l1-1 4 13c2 3 4 6 5 10l11 34 4 12c1 1 2 2 2 4v3h0c0 1 0 2-1 3v1c-1-1-1-1-1-2l-2-2-2-9s0-1-1-2l-1-3c-2-5-3-12-4-17-2-10-6-18-11-27l-1-3v-1c-1-2-1-3-2-4v-1c-2-2-3-4-5-6-1 0-1 0-1-1h3c0-2-1-3-1-4z" class="G"></path><path d="M467 583c2 2 2 3 3 6 1 4 3 7 1 11-2-5-3-12-4-17z" class="W"></path><path d="M473 605c3 2 4 6 6 9h0c0 1 0 2-1 3v1c-1-1-1-1-1-2l-2-2-2-9z" class="AO"></path><path d="M616 460c-1 7-2 13-4 20l-9 36c-2 7-2 15-4 22-1 3-2 7-1 10 0 1-1 3-1 4v2h-1l-1 4-2 8c-1 3-2 7-4 9-2 4-2 8-3 12l-3 9c-1 5-2 9-3 13h-1l37-149z" class="h"></path><path d="M589 575l7-30 3-7c-1 3-2 7-1 10 0 1-1 3-1 4v2h-1l-1 4-2 8c-1 3-2 7-4 9z" class="W"></path><path d="M389 297c2 1 2 3 3 6v1c1-1 1 0 1-1v-1h1c1 5 4 8 7 12 2 2 4 4 5 6h1l6 9h-2-1c0-1-1-1-1-1l-3-1-1 1h-2c0 1 2 3 2 3l-1 1v1l-1 2c0 1 0 2 1 3v3c-1 1 0 1 0 2-1-1-1-3-2-4 0-2 0-3-2-4v-1l-11-37z" class="v"></path><path d="M403 335v-1c-1-1-1-2-1-3v-1h0l1-1c-1-1-1-2-2-2l1-1-1-1c-1-3-2-3-2-6l1-1 2 2 1 3 1 1 2 2v1l-1 1h-2c0 1 2 3 2 3l-1 1v1l-1 2z" class="D"></path><path d="M382 433c1 0 2 1 3 1 1 1 1 1 2 1l2 2c1 1 2 2 4 2 4 1 11 8 13 11 0 1 0 1 1 2h-1c1 2 2 3 3 5s1 5 2 7c5 11 9 23 11 35 1 5-2 8-2 12h-1v-2c1-4 2-7 1-12-1-10-5-21-9-30-2-4-4-7-6-11 0-1 0-2-1-4l-1 1c-1 0-1 0-1-1l-1 1v1c0 2 1 7 0 10l-1-1c1-5 0-8-2-13-1-3-5-6-8-7l-5-1c-2 0-2 5-3 7h-1v-1-4c0-2 0-4 1-6v-4-1zm312-219l2 1c2 1 3 3 5 5 1 1 1 2 1 4v7c-2 2-3 4-5 4-1 1-3 1-4 0h-1c0-1-1-1-1-1-3-2-5-9-5-13h0v-1c1-2 2-3 4-5 1-1 2-1 4-1z" class="G"></path><path d="M700 220h0c0 1 0 2 1 3v4l-1 1c-1-3-1-5-3-7l3-1z" class="Z"></path><path d="M694 214l2 1c2 1 3 3 5 5h-1 0c-2-1-4-2-6-2-1 0-3 2-4 3s-2 3-2 5l1 4c1 1 2 3 3 4v1c0-1-1-1-1-1-3-2-5-9-5-13h0v-1c1-2 2-3 4-5 1-1 2-1 4-1z" class="k"></path><path d="M694 214l2 1c-3 1-3 2-6 2v-2c1-1 2-1 4-1z" class="AJ"></path><path d="M700 228c-1 1-1 2-2 3l-1 1c-2 0-3 0-5-1 0-2 0-3-1-4v-1-3-1c1 0 1-1 2-2 2-1 2 0 4 1 2 2 2 4 3 7z" class="i"></path><path d="M351 396l18 20-1 1c4 4 8 7 10 12l4 4v1h-2c-1-1-1-2-2-2-2-1-7-7-9-8v4l3 3h-1v1c-1 0-1 0-2-1-1 0-1 0-2-1h-1c-6-5-8-13-11-20-1-1-2-2-2-4l1-1 1 1-1-2c0-2-1-4-2-6-1 0-1-1-1-2z" class="n"></path><path d="M353 406l1-1 1 1c1 2 3 3 4 6-1-1-2-2-3-2h-1c-1-1-2-2-2-4z" class="K"></path><path d="M368 428v-1c-1-3-2-7-3-10l4 4c2 3 6 6 9 8l4 4v1h-2c-1-1-1-2-2-2-2-1-7-7-9-8v4l3 3h-1l-3-3z" class="d"></path><path d="M355 410h1c1 0 2 1 3 2 0 0 0 1 1 2 3 4 5 10 8 14l3 3v1c-1 0-1 0-2-1-1 0-1 0-2-1h-1c-6-5-8-13-11-20z" class="AP"></path><path d="M679 213l2 2 2 1v2c0 7 1 12 3 19s4 14 5 22c1 6 0 17-4 22v1c-1-1-1-2-1-3v-2l-1-1s2-7 2-8c0-6-1-11-3-16 0-5-1-9-2-14h-1c-1-2-6-17-5-20 1-2 2-2 3-3v-2z" class="b"></path><path d="M679 213l2 2 2 1v2c0 2-1 3-1 4-1-1-1-2-1-3l-2-4v-2z" class="g"></path><path d="M679 215l2 4c0 2 0 3-1 5v1c0-2-1-4-1-5-1-1-2-2-3-2 1-2 2-2 3-3z" class="L"></path><path d="M676 218c1 0 2 1 3 2 0 1 1 3 1 5 0 3 0 7 2 11 0 0 1 1 1 2h-1-1c-1-2-6-17-5-20z" class="Z"></path><path d="M682 238h1c1 3 1 6 3 9v-1l-1-2v-2-1-1l1 2v1c0 1 0 2 1 3v2c0 1 0 2 1 4 1 4 2 8 2 12 1-2 1-3 1-5 1 6 0 17-4 22v1c-1-1-1-2-1-3v-2l-1-1s2-7 2-8c0-6-1-11-3-16 0-5-1-9-2-14z" class="K"></path><path d="M648 122c5-1 10-4 14-4h4c3-1 8-1 11 1v1c2 0 2 0 3 1s2 0 3 1v1h-4c-1-1-3-1-5-1l1-1-1-1c-3 0-6 0-9 1-4 2-7 4-11 6-1 1-2 2-4 2l-1 1c-4 2-8 8-10 12 0 1-1 2-1 3l-1 1v2 3l-1-1h-1c-2-1-2-2-3-4h1v-5c0-3 3-6 4-8 3-5 6-9 11-11z" class="g"></path><path d="M633 141c0-3 3-6 4-8v3l-3 6s0-1-1-1z" class="k"></path><path d="M662 120v1c-2 2-3 2-6 3-3 2-7 4-10 7s-5 7-8 10c0-1 0-3-1-5 2-2 4-5 6-6 2-2 4-4 6-5 1-1 4-1 5-2 1 0 2 0 3-1l5-2z" class="d"></path><path d="M648 122c5-1 10-4 14-4h4c3-1 8-1 11 1v1c2 0 2 0 3 1s2 0 3 1v1h-4c-1-1-3-1-5-1l1-1-1-1c-3 0-6 0-9 1l-1-1c-1-1-1 0-2 0l-5 2c-1 1-2 1-3 1-1 1-4 1-5 2-2 1-4 3-6 5-2 1-4 4-6 6h0v-3c3-5 6-9 11-11z" class="O"></path><path d="M349 291l1-1v1 1c0 3 0 5-1 8v4h0c-1 3-4 9-3 12l2 2h-1-1 0l1 1v1l1 1h0c-1 0-2-1-2-1-2-1-4-3-6-4-1-1-2-1-3-2h-2 0l-1-1v-3c-2 0-4-1-6 1h0v1c-1 1-2 0-3-1v-2h1c0-1 1-1 2-1 2-2 5-4 5-7 1-3 0-6-2-9v-1c2 2 3 5 5 6l1 1v1c2 0 5-1 7-2h3 1 0c1-2 1-4 1-6z" class="K"></path><path d="M331 292v-1c2 2 3 5 5 6l1 1v1c2 0 5-1 7-2h3 1l-6 3c0 1 0 3-1 4s-1 1-2 1l-1 1c-1 1-2 2-4 2 1 1 5 3 5 3-2 0-3 0-5-1-2 0-4-1-6 1h0v1c-1 1-2 0-3-1v-2h1c0-1 1-1 2-1 2-2 5-4 5-7 1-3 0-6-2-9z" class="h"></path><path d="M338 306v-4c2-1 3-2 4-2 0 1 0 3-1 4s-1 1-2 1l-1 1z" class="d"></path><path d="M325 309v2c1 1 2 2 3 1v-1h0c2-2 4-1 6-1v3l1 1h0 2c1 1 2 1 3 2 2 1 4 3 6 4 4 5 5 9 7 15 0 2 0 5-1 7h0l-3 3-1-1c3-2 3-2 4-5 0-6-2-11-6-15 0-1-2-3-3-4l-1 1-1-1c-2-1-6-2-8-2-2 1-2 2-2 4 0 1-1 3 0 4 0 3 0 7 2 9 1 3 3 5 3 8l-2 2h-1c-4-4-6-10-8-15-1-2-2-5-2-7l-1-4c0-4 1-7 3-10z" class="Z"></path><path d="M325 309v2c1 1 2 2 3 1 2 1 4 1 6 2h-2-6 0c-3 3-1 8-3 9l-1-4c0-4 1-7 3-10z" class="AT"></path><path d="M323 323c2-1 0-6 3-9 1 4 0 8 1 11 2 4 2 7 4 11 1 3 2 5 3 8v1h-1c-4-4-6-10-8-15-1-2-2-5-2-7z" class="G"></path><path d="M420 478c-2-6-6-12-6-18h0c3 3 6 6 8 10 2 2 2 2 3 4v2l1-1h0c1 4-1-1 1 2 1 2 0 3 2 4h0c2 6 3 11 5 16 1 6 2 12 2 18-1 0-2 1-3 2l-1-1-2 1 1-1 2-1 1-1c-2 0-3-1-4-1l-1-2v-1c-1 0-1-1-1-2v-1l-1-2c0-2 0-5-1-7 0-2-2-5-1-7v-2c-2-4-3-8-5-11z" class="J"></path><path d="M422 470c2 2 2 2 3 4v2l1-1h0c1 4-1-1 1 2 1 2 0 3 2 4h0c2 6 3 11 5 16 1 6 2 12 2 18-1 0-2 1-3 2l-1-1-2 1 1-1 2-1 1-1c-2 0-3-1-4-1l-1-2v-1c-1 0-1-1-1-2v-1l-1-2c0-2 0-5-1-7 0-2-2-5-1-7l5 19h1c0 2 1 2 2 3v-1l-1-1v-2-2c0-12-5-26-10-37z" class="G"></path><path d="M313 280l2-2c2 0 4 2 5 3 3 2 5 4 7 6 1 2 2 4 3 5 1 3 1 5 0 9 0 0-1 1-1 2-3 2-5 2-8 1s-5-3-7-6v-1c-1-2-2-3-2-4l-1-8v-2l2-3z" class="AP"></path><path d="M312 293l1-1v-4c0-2 1-3 3-5 1 0 2 0 3 1h-2c-2 3-3 6-2 9 1 2 3 6 5 7l2 2c1 0 2 1 3 1 2-1 3-2 4-3v-1l1 2s-1 1-1 2c-3 2-5 2-8 1s-5-3-7-6v-1c-1-2-2-3-2-4z" class="d"></path><defs><linearGradient id="AN" x1="323.03" y1="283.719" x2="330.364" y2="297.542" xlink:href="#B"><stop offset="0" stop-color="#c0aea0"></stop><stop offset="1" stop-color="#d2cac4"></stop></linearGradient></defs><path fill="url(#AN)" d="M313 280l2-2c2 0 4 2 5 3 3 2 5 4 7 6 1 2 2 4 3 5 1 3 1 5 0 9l-1-2c-1-5-3-11-8-14l-2-1c-1-1-2-1-3-1-2 2-3 3-3 5v4l-1 1-1-8v-2l2-3z"></path><path d="M313 280l2-2c2 0 4 2 5 3-2 0-3 0-5 1h0c-1 0-2 1-3 1h-1l2-3z" class="p"></path><path d="M655 287c2-2 2-6 3-9l1 1v-3l1 1c0-3 0-6 1-9 0 8-1 16-3 23l-1 3c0 4-1 8-1 12-1 2 1 5 1 7 2 8-2 14-6 20-3 4-4 8-6 12h1c-3 4-4 7-7 10-2 5-4 10-5 14l-1 1c0-2 0-4 1-5 2-8 6-15 8-22l7-21 4-19c1-5 1-11 2-16z" class="d"></path><path d="M639 355v-1c1-4 3-7 4-10h1c0-1 0-1 1-2 0-1 1-1 1-2 1-3 2-5 4-7h1c-3 4-4 8-6 12h1c-3 4-4 7-7 10z" class="K"></path><path d="M655 287c2-2 2-6 3-9l1 1v-3l1 1c0-3 0-6 1-9 0 8-1 16-3 23l-1 3-1 3v1c-2 3-1 6-1 9h0v-1c-1-1-1-1-1-2l-1 2h1v3l-1 2v1l-1 1v1c0 2-1 7-2 8h-1l4-19c1-5 1-11 2-16z" class="L"></path><path d="M711 191c1 0 2 1 3 1 1 1 1 2 1 3 3 5 2 13 1 17l-1 2h0c-1 1-1 1-1 2-1 2-4 5-6 6h-1v-1l-2 1v-1c-1 0-1-1-1-2h-1-1v1 2 2c0-2 0-3-1-4-2-2-3-4-5-5l-2-1v-1c2-2 4-4 5-6l1-1c0 1 0 1 1 1 2-4 7-7 9-11 1-2 2-3 1-5z" class="J"></path><path d="M694 213c2 1 4 2 6 2h1v-1c1 0 1 0 2 1s2 1 3 1v1c0 2 0 2 1 4h0l-2 1v-1c-1 0-1-1-1-2h-1-1v1 2 2c0-2 0-3-1-4-2-2-3-4-5-5l-2-1v-1z" class="Z"></path><path d="M711 191c1 0 2 1 3 1 1 1 1 2 1 3 3 5 2 13 1 17l-1 2h0c-1 1-1 1-1 2-1 2-4 5-6 6h-1v-1h0c3-1 4-4 5-6 4-4 5-12 4-17-1-2-1-2-2-3-2 2-1 5-3 7-3 3-7 9-12 9v1l-1 1 1 1 1 1c-2 0-4-1-6-2 2-2 4-4 5-6l1-1c0 1 0 1 1 1 2-4 7-7 9-11 1-2 2-3 1-5z" class="G"></path><path d="M459 603c1-1 1-1 2-1 5 0 7 0 10 3l2 2-3 2h0c-1 0-1 0-1 1h-1c-4 0-8-1-11 1-2 1-4 1-5 1-1 1-2 1-2 2-4 0-6 4-8 7-4 6-5 12-6 18 0 6 1 11 2 16h0v3l-1 1h1v3l-1-1h-1c-2-9-5-21-2-30 0-1 1-2 1-3 1-6 4-9 7-14 1-1 2-1 2-2v-1c2-2 5-5 8-6 1 0 3-1 4-2h1 2z" class="AO"></path><path d="M452 612c3-2 6-3 9-3l2-1h5 1l1 1h0c-1 0-1 0-1 1h-1c-4 0-8-1-11 1-2 1-4 1-5 1z" class="h"></path><path d="M459 603c1-1 1-1 2-1h1c1 1 2 1 3 1v2c1 0 1 0 2 1-10 1-17 2-25 8 1-1 2-1 2-2v-1c2-2 5-5 8-6 1 0 3-1 4-2h1 2z" class="i"></path><path d="M434 631l1 1 1-4c1-2 1-4 2-6h1v1c-2 5-4 11-4 16 1 1 1 1 1 0 0 6 1 11 2 16h0v3l-1 1h1v3l-1-1h-1c-2-9-5-21-2-30z" class="h"></path><path d="M684 252h0c2 5 3 10 3 16 0 1-2 8-2 8l-1 3c0 2-2 4-3 6l-5 10c0 2 0 2-1 4l-1 1v2l-1 1c0 1-1 1-1 2v-4l1-1h0v-6c1-1 1-2 1-3v-4c1-3 2-6 1-9 0-1 0-1-1-2h0v-2l-1-1v-3-4c0-4 4-6 7-8 2-2 3-3 4-6z" class="L"></path><path d="M684 252h0c2 5 3 10 3 16 0 1-2 8-2 8l-1 3v-2l1-3c1-2 1-5 1-7v-1l-1-1h-1l-1-1c0-1 0-1-1-2 0 1-1 1-1 2-1 1-1 1-2 1-2 1-3 1-5 1 0 2 0 3-1 4v-4c0-4 4-6 7-8 2-2 3-3 4-6z" class="AI"></path><path d="M674 266c1-2 4-3 6-4s4-2 5-4c0 3 1 6 1 9v-1l-1-1h-1l-1-1c0-1 0-1-1-2 0 1-1 1-1 2-1 1-1 1-2 1-2 1-3 1-5 1z" class="g"></path><path d="M674 274c0-2 0-3 1-4 3 2 4 4 5 7v1c1 1 0 3-1 5l1 2h-1v3l-1 1-3 6s1 1 1 0c0 2 0 2-1 4l-1 1v2l-1 1c0 1-1 1-1 2v-4l1-1h0v-6c1-1 1-2 1-3v-4c1-3 2-6 1-9 0-1 0-1-1-2h0v-2z" class="J"></path><defs><linearGradient id="AO" x1="407.214" y1="535.434" x2="387.347" y2="546.26" xlink:href="#B"><stop offset="0" stop-color="#bfad9e"></stop><stop offset="1" stop-color="#dadbde"></stop></linearGradient></defs><path fill="url(#AO)" d="M381 491c2-1 1-6 2-7 0-1 1-2 2-4h0l2-1c-1 2-1 4-1 5l1 2h-1 0-1c0 2 1 5 1 7 0 6 2 13 3 19l11 37c1 5 2 10 4 15l1 1h0l1 1 5 14 1 4c0 1 1 2 1 3v2c0 1 0 1-1 2 1 2 3 5 3 7v1c-2-3-4-7-5-10 0-2-1-3-1-4-1-5-3-11-5-15l-1 1c-2-6-4-12-5-18l-13-44c-1-6-3-12-4-18z"></path><path d="M341 365l1-1c1-3 4-5 6-8 5-3 13-2 19-2 2 1 4 2 7 4l-5-1h-6c-4 1-8 2-12 4-1 1-3 3-4 5v3l1 6c2 9 8 16 13 24 2 2 4 5 6 8 1 1 2 1 2 3l4 4v1 1c1 1 3 2 4 4l8 6h0c-10-6-18-14-25-22-2-3-5-5-7-8-5-7-10-16-12-25 0 1 1 0 1 0-1-2 0-4-1-6z" class="G"></path><path d="M347 378l-1-1c-3-6-2-11 0-17 1-1 2-2 4-2l1-1c3-1 7-2 11-1 2 0 5 0 7 1h-6c-4 1-8 2-12 4-1 1-3 3-4 5v3c-1 4-1 6 0 9z" class="Z"></path><path d="M347 369l1 6c2 9 8 16 13 24 2 2 4 5 6 8 1 1 2 1 2 3h0l-4-4c-3-4-10-9-11-13h0c-1-2-3-4-4-6l-3-9c-1-3-1-5 0-9z" class="i"></path><defs><linearGradient id="AP" x1="334.545" y1="283.234" x2="399.648" y2="297.2" xlink:href="#B"><stop offset="0" stop-color="#abab9d"></stop><stop offset="1" stop-color="#f0dbd8"></stop></linearGradient></defs><path fill="url(#AP)" d="M348 232v-1c-1-1-1-2-1-3 0 0 1 1 2 1 0 1 0 1 1 1 0 1 1 2 2 3 3 4 5 10 7 15 8 20 11 41 17 62l11 35v1c0 1 0 2 1 2v2l1 1h-1v2h0l-3-6v-2l-1 1c0-1-1-3-1-4v-1c-1 3-2 4-5 5h-1c2-1 3-2 4-3 0-6-2-11-3-17l-7-19-5-21v-2l-1 1h0c1-5-1-10-3-14-3-11-6-22-11-32 0-3-2-5-3-7z"></path><path d="M506 718l1 1c0 3-1 6-3 8s-4 3-5 6c0 1 1 1 1 3 0 1 0 1 1 2h0c2 3 4 6 3 10l12 12 6 11c0 1 0 1-1 0h0c-1-2-2-2-4-3-3-1-5-2-8-4l-1 3c-1 0-2-1-3-1l-1 1-1-2c-1-1-3-4-4-5l-4-6h0c-2-5-3-9-2-14v-2c-1-7 11-13 13-19v-1z" class="Z"></path><path d="M499 733c0 1 1 1 1 3-1 0-1 0-1 1-1 0-2 0-3 1v1l-1-2 3-3 1-1z" class="n"></path><path d="M501 748c1 2 1 4 1 6v1l-1 1c0-1-1-2-1-3h-1c0 1 0 0 1 1v1h-1c-1-2-1-3-2-5h1c1 0 1 0 2 1v1h1v-4z" class="L"></path><path d="M498 738h1 1c0 3-1 5-1 8 0 1 0 1-1 2h-1c-1-2-1-2-1-4 1-1 1-2 1-3l1-3z" class="i"></path><path d="M502 755c3 3 5 7 7 9l-1 3c-1 0-2-1-3-1 1-2-1-2-1-4l1 1 1-1h-1c-1-1-3-5-4-6l1-1z" class="G"></path><path d="M501 738h0c2 3 4 6 3 10-1 2-1 2-1 4 2 3 5 6 7 9 1 0 2 2 3 2 1 2 3 3 4 5-3-1-5-2-8-4-2-2-4-6-7-9v-1c0-2 0-4-1-6l1-1c0-1 0-2-1-3 1-2 1-4 0-6z" class="O"></path><path d="M504 748l12 12 6 11c0 1 0 1-1 0h0c-1-2-2-2-4-3-1-2-3-3-4-5-1 0-2-2-3-2-2-3-5-6-7-9 0-2 0-2 1-4z" class="k"></path><path d="M509 764c3 2 5 3 8 4 2 1 3 1 4 3h0c1 1 1 1 1 0 2 2 4 5 4 8h0v3 1c-1-3-2-5-4-7v1l-2-1v1c1 2 1 3 0 6l-3-3c-1 1-1 1-2 1h-1l-2 4v2h-1v-1-1c-2 2-2 6-4 6l3-6-2-2h0-4-5c-3 2-5 4-7 7l-1-3h0l2-3-1-1c-1 1-2 1-3 2l-3 4h-1l1-2c1-1 1-1 1-2l1-1c-1 0-2 1-3 2v1h0v1c-2 0-2 2-2 3l-2 2c1-3 2-6 4-8l5-7 3-3 2-1 5-5c1 0 1 1 2 1 1-1 1-2 3-2v2c0-1 0-1 1-1l3-1-1-1 1-3z" class="o"></path><path d="M514 781c2-2 4-3 6-4 1 2 1 3 0 6l-3-3c-1 1-1 1-2 1h-1z" class="AI"></path><path d="M505 775c2-1 5-1 7-1h2c-2 2-3 3-6 3-1 0-1 0-2-1l-1 1v-2z" class="L"></path><path d="M499 774l6-3-1 3 1 1v2h-1-1c0-1 0 0-1-1l-1 1c-2 1-2 0-2 1v1h0l-1 1h-1v-1c0-2 1-3 2-5z" class="O"></path><path d="M499 779c5-1 11-1 16-2l-5 8-2-2h0-4-5c1-1 1-1 1-3l-1-1h0z" class="AO"></path><path d="M508 783l-1-2 1-1c1 1 1 1 1 2v1h-1 0z" class="AT"></path><path d="M509 764c3 2 5 3 8 4 2 1 3 1 4 3h0c1 1 1 1 1 0 2 2 4 5 4 8h0v3 1c-1-3-2-5-4-7v1l-2-1v-2c-1 0-2 0-2 1h-1c-1-1-2-1-3-1h-2c-2 0-5 0-7 1l-1-1 1-3 3-1 6-1c-1-1-3-1-5-1l-1-1 1-3z" class="g"></path><path d="M514 769h3s1 1 2 1v1l-2 2-3-1h-2-3l-1-2 6-1z" class="AT"></path><path d="M505 771l3-1 1 2h3 2l-2 2c-2 0-5 0-7 1l-1-1 1-3z" class="AI"></path><path d="M509 768c2 0 4 0 5 1l-6 1-3 1-6 3c-1 2-2 3-2 5v1h1l1-1 1 1c0 2 0 2-1 3-3 2-5 4-7 7l-1-3h0l2-3-1-1c-1 1-2 1-3 2l-3 4h-1l1-2c1-1 1-1 1-2l1-1c-1 0-2 1-3 2v1h0v1c-2 0-2 2-2 3l-2 2c1-3 2-6 4-8l5-7 3-3 2-1 5-5c1 0 1 1 2 1 1-1 1-2 3-2v2c0-1 0-1 1-1l3-1z" class="o"></path><path d="M499 774c-1 2-2 3-2 5v1h1l1-1 1 1c0 2 0 2-1 3-3 2-5 4-7 7l-1-3h0l2-3-1-1c-1 1-2 1-3 2 2-4 6-8 10-11z" class="AI"></path><path d="M616 460c0-3 2-7 3-10l1 3c-2 6-5 17-1 23h1l4 1c2 1 4 1 5 2l1 4 1 2-2 1c0-3-1-5-4-6l-1-1h-1c-2 0-5 0-7 1-1 1-2 2-2 4-3 8-4 17-6 26-2 6-4 13-4 19-1 2 0 3 0 5 2 1 4 1 6 2h2c1-1 2-3 2-4 3-4 5-8 6-13 1 1 1 1 3 1 1 0 1-1 2-1v1l-2 3-2 4h0c0 1 0 1-1 2-2 4-3 9-4 13-1 3-2 5-3 8-1 4-1 8-2 11h-1v-3l2-13c1-1 1-2 1-3 0-2-1-3-2-3-1-1-3-2-5-2h0-2c-4 2-5 8-6 11-1-3 0-7 1-10 2-7 2-15 4-22l9-36c2-7 3-13 4-20z" class="J"></path><path d="M522 777v-1c2 2 3 4 4 7 1 2 1 2 3 3l1-1h1c0-1-1-2 0-3 1 2 2 5 3 7h0c1 2 2 3 3 4 1 3 4 4 5 7 1 2 1 5 1 8-1 6-1 13-3 19 0 4-1 9-3 13-1-2 1-7 1-9-1-2 0-4-1-6l-2 12h0 0v-2-1-3h0l-1 2v1h0v1c-1 1 0 1-1 1 0 1 0 2-1 3v1-4-16c-1-9-2-19-5-28-1-6-3-11-5-15z" class="i"></path><path d="M532 820c0 1 1 2 1 4v1c1 1 1 3 1 5l-1 1c0 1 0 3-1 5v-16z" class="Z"></path><path d="M529 786l1-1h1c0-1-1-2 0-3 1 2 2 5 3 7h0l-1 2h1v1l1 2v1c0 1 0 1 1 3 1 1 1 5 1 7-1 1-1 2-1 3s0 1-1 1c-1-8-3-15-6-23z" class="L"></path><path d="M534 789c1 2 2 3 3 4 1 3 4 4 5 7 1 2 1 5 1 8-1 6-1 13-3 19 0 4-1 9-3 13-1-2 1-7 1-9-1-2 0-4-1-6v-19-1c0-2 0-6-1-7-1-2-1-2-1-3v-1l-1-2v-1h-1l1-2z" class="g"></path><path d="M537 806c0 1 1 2 1 4 0 0 0 1 1 1v11c0 2 0 4 1 5h0c0 4-1 9-3 13-1-2 1-7 1-9-1-2 0-4-1-6v-19z" class="n"></path><path d="M614 111c2-1 6-1 8-1 10 1 20-1 30 1 4 1 8 0 11 1h0c-2 1-5 0-7 1s-3 0-5 1v1s0 1-1 1h-1c-2 2-5 2-7 3-4 2-9 7-11 11 0 1-1 2-1 3-1-1-1-2-1-3-1-3-1-6-3-8h-1l-3-3 1-1h-2v-1-1c-4 2-8 1-12 1l1 2c-2 1-3 1-4 2h-1c-1 0-1 0-2 1v-1c0-1 1-2 2-3-2 0-3-1-4 0-3 0-7 0-10 1l-4 1v-1c3-2 5-2 7-4 3-1 5-1 8-1 4 0 8-1 12-3h0z" class="J"></path><path d="M605 118c2-2 5-3 8-3 9-3 19-3 28-3 3 0 8 0 10 3 0 0 0 1-1 1l-3-1h-19l-7 1c-4 2-8 1-12 1l1 2c-2 1-3 1-4 2h-1c-1 0-1 0-2 1v-1c0-1 1-2 2-3z" class="AT"></path><defs><linearGradient id="AQ" x1="635.237" y1="114.479" x2="631.862" y2="130.599" xlink:href="#B"><stop offset="0" stop-color="#c0ada1"></stop><stop offset="1" stop-color="#d7d0c8"></stop></linearGradient></defs><path fill="url(#AQ)" d="M628 115h19l3 1h-1c-2 2-5 2-7 3-4 2-9 7-11 11 0 1-1 2-1 3-1-1-1-2-1-3-1-3-1-6-3-8h-1l-3-3 1-1h-2v-1-1l7-1z"></path><path d="M621 116l7-1-2 3-1-1v1c0 1 0 2 1 4h-1l-3-3 1-1h-2v-1-1z" class="O"></path><path d="M726 114c1-1 2-1 3-1h3c1-1 2-1 3 0h3l4-1h1 1c-1 1-2 2-4 2 0 0-1 0-1 1 2 2 6 2 8 1h2 2 0c-1 1-2 1-3 1 0 2-1 2 0 3 1 2 1 3 2 4 1 3 1 8 2 10 0 2-1 4-2 6h0c-1 2-1 6-1 8-1 3-2 7-3 10-2 4-5 9-7 12h-1l1-3c1-1 2-3 3-4l1-2v-1l3-6c0-2 0-3 1-5h0c0-2 0-4 1-6v-5l-1 1h-1v-3l-1 1v6c-1-1-1-3-1-4-1-2-1-3-1-4 0 2 0 4-1 5 0-1 0-3-1-4-1-3-2-5-4-7l-1-2c-2 0-2 0-3-1-2-1-4-1-5-3l1-1v-1h-3v2c-1-1-3-2-4-3h-1c-2-2-6-2-9-1-2 0-3 1-5 1l-1-1c6-3 13-4 20-5z" class="K"></path><path d="M725 116c3-1 6-1 8-1l1 1c1 0 3 1 4 1h-8c-1 1-3 1-4 2 0 1-1 1 0 2v2c-1-1-3-2-4-3l1-1 2-2 3-1h-3z" class="n"></path><path d="M726 114h0c-2 1-4 1-6 2h0 5 3l-3 1-2 2-1 1h-1c-2-2-6-2-9-1-2 0-3 1-5 1l-1-1c6-3 13-4 20-5z" class="AI"></path><path d="M730 117h8c3 1 6 2 7 5 1 1 1 2 2 3v1c1 4 1 8 0 13h-1v-3l-1 1v6c-1-1-1-3-1-4-1-2-1-3-1-4 0 2 0 4-1 5 0-1 0-3-1-4-1-3-2-5-4-7l-1-2c-2 0-2 0-3-1-2-1-4-1-5-3l1-1v-1h-3c-1-1 0-1 0-2 1-1 3-1 4-2z" class="k"></path><path d="M726 121c-1-1 0-1 0-2 1-1 3-1 4-2 1 0 3 1 4 1h3c3 1 5 2 7 5h-1 0c-2-1-3-1-5-1s-6-1-9-1h-3z" class="Ab"></path><path d="M729 121c3 0 7 1 9 1 2 2 4 3 5 5s3 7 3 9l-1 1v6c-1-1-1-3-1-4-1-2-1-3-1-4 0 2 0 4-1 5 0-1 0-3-1-4-1-3-2-5-4-7l-1-2c-2 0-2 0-3-1-2-1-4-1-5-3l1-1v-1z" class="G"></path><path d="M739 127c2 2 3 5 4 8 0 2 0 4-1 5 0-1 0-3-1-4-1-3-2-5-4-7l-1-2h3z" class="AT"></path><path d="M729 121c3 0 7 1 9 1 2 2 4 3 5 5l-3-2v1l-1-1-1 1 1 1h-3c-2 0-2 0-3-1-2-1-4-1-5-3l1-1v-1z" class="g"></path><path d="M729 122c4 1 6 2 9 4l1 1h-3c-2 0-2 0-3-1-2-1-4-1-5-3l1-1z" class="AJ"></path><path d="M400 463l1 1c1-3 0-8 0-10v-1l1-1c0 1 0 1 1 1l1-1c1 2 1 3 1 4-1 1-1 1-1 3 0 6-2 11-5 17-2 6-4 12-4 18v5c0 1 2 3 3 5h-1l-2-4v2l1 1 1 2c-1 0-1-1-2-2-2 3 3 31 4 36l4 13 2 9c0 1 1 3 1 5l-1-1h0l-1-1c-2-5-3-10-4-15l-11-37c-1-6-3-13-3-19 0-2-1-5-1-7h1 0 1l-1-2c0-1 0-3 1-5 3 0 4-2 7-4l2-1c1-1 1-2 1-3v-1c1-1 2-2 3-4v-3z" class="d"></path><path d="M398 473l-2 11c-1 3-1 9-3 12l-2-3c-2-3-3-5-4-8 1-2 2-1 3-2 2-2 3-5 5-7l3-3z" class="K"></path><path d="M386 493c0-2 0-4 1-5v1h0c1 1 2 2 2 3 3 4 3 7 4 11 1 6 1 13 2 19l4 19c1 5 3 11 4 16 1 2 2 5 1 7-2-5-3-10-4-15l-11-37c-1-6-3-13-3-19z" class="J"></path><path d="M665 208h1l3 4c0-2-1-2-1-3-1-2-1-2-1-4 1 1 3 4 3 5 1 1 2 2 4 2v-1c2 0 3 1 5 2v2c-1 1-2 1-3 3-1 3 4 18 5 20h1c1 5 2 9 2 14h0c-1 3-2 4-4 6-3 2-7 4-7 8v4 3l1 1v2h0l-5 19c-1-1-1-1-1-2l1-1v-1h0v-1-1-1l-1 2h-1v2h-1c1-2 1-5 1-7-1-3 0-7 1-10l2-13v-13h0l1 3v-1c0-1 1-2 1-3 0 3-1 7 0 10 1-2 1-5 2-6l1-1c2-4 1-10 0-14l-5-5h0l-1-2h-1c-1 1-2 1-3 1-2 1-4 2-5 4-1 0-1 0-2 1 0 1 0 3-1 4h-1v-5c1-1 1-2 2-4l4-2-1-1 1-1 1-1c1 1 1 1 2 0h0c1 1 3 1 4 1s1 0 2-1c1-3 0-6-1-9-1-2-2-5-4-6l-1-3z" class="o"></path><path d="M670 210c1 1 2 2 4 2v-1c2 0 3 1 5 2v2c-1 1-2 1-3 3-1 3 4 18 5 20h1c1 5 2 9 2 14h0c-1-3-2-7-3-11-1-6-4-11-5-16 0-4-4-11-6-15z" class="b"></path><path d="M662 229c4-1 7-1 11 2 3 5 6 11 4 18l-1 2c-1 2-2 3-3 5-2 6-1 13-2 19 0 1-1 3-1 3 0-2 0-5 1-8 0-4 0-9 1-12 1-2 1-5 2-6l1-1c2-4 1-10 0-14l-5-5h0l-1-2h-1c-1 1-2 1-3 1-2 1-4 2-5 4-1 0-1 0-2 1 0 1 0 3-1 4h-1v-5c1-1 1-2 2-4l4-2z" class="k"></path><path d="M665 208h1l3 4c0-2-1-2-1-3-1-2-1-2-1-4 1 1 3 4 3 5 2 4 6 11 6 15-1 2-2 3-1 6 1 1 2 3 3 4v7 5c0 2 0 2-2 4l1-2c2-7-1-13-4-18-4-3-7-3-11-2l-1-1 1-1 1-1c1 1 1 1 2 0h0c1 1 3 1 4 1s1 0 2-1c1-3 0-6-1-9-1-2-2-5-4-6l-1-3z" class="d"></path><path d="M656 137h4s0-1 1-1 7 1 9 2c-1 2-1 4 0 6l1 4-2 2c-1 2-3 3-5 3l-4 6c-2 3-4 7-5 11 0 1-1 3-1 4 0 0-1 1-2 1-1-1-2-2-2-4h-1c-2-1-7-5-8-7v-1-7c1-4 2-8 3-11l6-5c1 0 2 0 3-1s1-2 3-2z" class="b"></path><path d="M641 164h3v-1c-1-4-1-7 1-11 1-1 1-3 2-4 1-2 2-3 2-5l2-2h1l-1 2c-4 8-5 15-6 23l3 2 3 2-1 1h-1c-2-1-7-5-8-7z" class="G"></path><path d="M651 143l1 1-1 2h1l-1 3 1 1c-1 1-1 1 0 2l-3 6v8h-1l-1 1 2 1h-1l-3-2c1-8 2-15 6-23z" class="k"></path><path d="M665 146v-1l-1-1c-1 0-2 0-3-1 0 0-1-1-2-1h0-1l-2-2c1-2 1-2 3-2 3 2 6 4 8 7-1 3-2 5-3 8l-4 6v-1-1l2-3v-2-2c0-2-2-2-3-3v-3c1 0 1 1 2 1 1 1 3 1 4 1z" class="h"></path><path d="M659 147v-3c1 0 1 1 2 1 1 1 3 1 4 1 0 3-1 4-2 7-1 0-1 1-1 1v-2-2c0-2-2-2-3-3z" class="W"></path><path d="M656 137h4s0-1 1-1 7 1 9 2c-1 2-1 4 0 6l1 4-2 2c-1 2-3 3-5 3l3-8c-2-3-5-5-8-7-1 0-2 0-3-1z" class="J"></path><path d="M652 144h3v-1h1l3 4c1 1 3 1 3 3v2 2l-2 3-1-2c-1 1-2-2-3-3h-2-2c-1-1-1-1 0-2l-1-1 1-3h-1l1-2z" class="G"></path><path d="M652 146c1 2 3 4 5 6 1 1 2 2 2 3-1 1-2-2-3-3h-2-2c-1-1-1-1 0-2l-1-1 1-3z" class="O"></path><path d="M657 152c0-3-2-6-1-9l3 4c1 1 3 1 3 3v2 2l-2 3-1-2c0-1-1-2-2-3z" class="i"></path><path d="M652 152h2 2c1 1 2 4 3 3l1 2v1 1c-2 3-4 7-5 11 0 1-1 3-1 4 0 0-1 1-2 1-1-1-2-2-2-4l1-1-3-2h1l-2-1 1-1h1v-8l3-6z" class="W"></path><path d="M655 170c-1-2-2-3-2-5s-1-4 0-6c2-1 2-1 4-1 0 1 1 1 1 1l2-1v1c-2 3-4 7-5 11z" class="n"></path><path d="M652 152h2v1c0 2-1 3-2 5-1 1-2 3-2 6 2 2 4 4 4 8h-1l-2-2-3-2h1l-2-1 1-1h1v-8l3-6z" class="g"></path><path d="M624 168c3 1 6 2 8 4l2 4c2 1 3 3 4 4 2 2 4 3 5 5l3 3-2 1h0-1l-2 1v1l3 3v4l-3-3c0-1 0-1-1-1 0 2 1 5-1 7-1 2-4 3-5 5 0 2 1 4 0 6h-1l-2 1h-1c0-1-1-2-1-3h-1 1c1-1 1-2 1-4 0-1-1-2-2-2v-2c-1-2-1-1-2-1 0 0-1-1-1-2h-4c-1 1-1 0-2 0h0c-1 0-2 0-2 1l-2 1-3-2 4-6 3-5h0v-1l-1 1v-1h-2l4-8c2-1 2-3 3-5 0-1 1-2 1-3v-3z" class="AX"></path><path d="M634 176c2 1 3 3 4 4 2 2 4 3 5 5l3 3-2 1h0-1l-2 1v1l3 3v4l-3-3c0-1 0-1-1-1l-1-2c-1-2-3-4-5-5v-1c1-4 1-6 0-10z" class="G"></path><path d="M641 190l-1-1c-1 0-2-1-2-2l2-2 3 3c1 0 1 0 1 1h0-1l-2 1z" class="J"></path><path d="M638 180c2 2 4 3 5 5l3 3-2 1c0-1 0-1-1-1l-3-3c-1-2-2-3-2-5z" class="L"></path><path d="M619 187l2-2c2 0 3 1 5 1l1 2c1 0 1 0 2 1 1 0 1 1 2 1 0 1-1 2-1 3 1 1 2 1 1 3l-1-1c0 1 1 2 1 3 0 2 0 1 1 2v4h1 0c-1-1-1-2-1-2v-3c0-1 1-1 2-1 0 1-1 4 0 5 1-1 2-3 3-3l1-1c1 0 1 0 2-1l-1-1v-5l1 2c0 2 1 5-1 7-1 2-4 3-5 5 0 2 1 4 0 6h-1l-2 1h-1c0-1-1-2-1-3h-1 1c1-1 1-2 1-4 0-1-1-2-2-2v-2c-1-2-1-1-2-1 0 0-1-1-1-2h-4c-1 1-1 0-2 0h0c-1 0-2 0-2 1l-2 1-3-2 4-6 3-5h0v-1z" class="C"></path><path d="M631 205l2 1h0c-1 2 0 4 0 6l-2 1h-1c0-1-1-2-1-3h-1 1c1 0 2-1 3-1 0-1 0-3-1-4z" class="U"></path><path d="M619 188l2-2 1 1c1 1 1 2 2 3s2 1 3 1v1l-1 1h1c0 1 1 1 2 2v1c1 3 2 5 2 9h0c1 1 1 3 1 4-1 0-2 1-3 1 1-1 1-2 1-4 0-1-1-2-2-2 0-3 1-3-1-5l1-1c-1 0-2-1-2-1-1-1-1-3-2-4 0 0-3-1-4-2 1-1 1-1 1-2l-2-1h0z" class="V"></path><path d="M628 198c1 2 2 3 2 5l1 1v1h0c1 1 1 3 1 4-1 0-2 1-3 1 1-1 1-2 1-4 0-1-1-2-2-2 0-3 1-3-1-5l1-1z" class="P"></path><path d="M619 188l2 1c0 1 0 1-1 2 1 1 4 2 4 2 1 1 1 3 2 4 0 0 1 1 2 1l-1 1c2 2 1 2 1 5v-2c-1-2-1-1-2-1 0 0-1-1-1-2h-4c-1 1-1 0-2 0h0c-1 0-2 0-2 1l-2 1-3-2 4-6 3-5z" class="R"></path><path d="M621 195v-1c1 0 1-1 2 0v1h0 1c0 1 1 1 1 2v2h0-4-1c0-2 0-2 1-4z" class="AA"></path><path d="M616 193c2 1 2 3 4 3l1-1c-1 2-1 2-1 4h1c-1 1-1 0-2 0h0c-1 0-2 0-2 1l-2 1-3-2 4-6z" class="u"></path><path d="M719 155c1 1 2 1 2 3l3 2c3 8 3 17 3 26 0 5 0 10-2 14h1c1 7-1 11-4 17-2 2-4 4-5 6h-1c-2 1-5 3-7 5-1 1-2 3-2 4-2-2-1-4-1-6v-1l-1-3 2-1v1h1c2-1 5-4 6-6 0-1 0-1 1-2h0l1-2c1-4 2-12-1-17 0-1 0-2-1-3 0-1 0-1-1-2-1-5 0-12 1-16 1-7 4-13 5-19z" class="K"></path><path d="M715 214l3-3c0 2-2 6-4 8-1 1-3 2-4 5 1 0 2-1 3-1-2 1-3 2-5 4v-2h1l-1-1h0l-1-1v-1h1c2-1 5-4 6-6 0-1 0-1 1-2h0z" class="Z"></path><path d="M719 155c1 1 2 1 2 3 0 1 1 2 1 3l-2 2c0 1 0-1 0 1l-2 8v1c-1 2-1 4 0 6v5 6h0c-1-2-1-5-2-7-1-1-1-2-1-4v-2-1-5c0 1-1 2-1 4v-1c1-7 4-13 5-19z" class="d"></path><path d="M721 158l3 2c3 8 3 17 3 26 0 5 0 10-2 14v2h-1v-1h-1v-2c2-9 3-22 1-31-1-2-2-4-2-6v-1c0-1-1-2-1-3z" class="Z"></path><path d="M713 223c2-2 3-3 5-6v-1c1-2 1-4 1-7 1-4 1-8 1-12v-1l1 1v9l-2 12 1-2c2-5 2-10 3-15h1v1h1v-2h1c1 7-1 11-4 17-2 2-4 4-5 6h-1c-2 1-5 3-7 5-1 1-2 3-2 4-2-2-1-4-1-6v-1l-1-3 2-1v1 1l1 1h0l1 1h-1v2c2-2 3-3 5-4z" class="L"></path><path d="M505 717l1 1v1c-2 6-14 12-13 19v2c-1 5 0 9 2 14h0l4 6c1 1 3 4 4 5l1 2 1-1c1 0 2 1 3 1l1 1-3 1c-1 0-1 0-1 1v-2c-2 0-2 1-3 2-1 0-1-1-2-1l-5 5-2 1-3 3-5 7c-2 2-3 5-4 8-1 2-2 6-2 9 0 1 0 2-1 3l-2-2v-1c1-1 1-2 1-4h-1c0 1 0 1-1 1 0 0-1 1-1 2-1-3-1-6-1-8v-2c1-3 0-7 0-10l1-2v-4c3-8 12-11 15-19h0c2-7 0-15 3-21 1-4 4-7 6-10 3-2 6-4 7-8z" class="O"></path><path d="M489 762h-2c1-3 3-7 6-9l1 1h1 0l4 6c1 1 3 4 4 5l1 2 1-1c1 0 2 1 3 1l1 1-3 1c-1 0-1 0-1 1v-2c-2 0-2 1-3 2-1 0-1-1-2-1l-5 5-2 1h0c2-3 3-5 6-6l-1-1-1 1c-1 0-1-1-2-1l-1 2-1 1c-1 0-1 0-2 1l1-2-1-1 1-2c1-1 1-2 2-3l-5-2z" class="L"></path><path d="M489 762l1-1 2 1c1 0 2 1 3 0-1-2-3-3-3-6h1c1 1 2 3 3 4h1l1 1c0 1 1 2 2 3 0 1 2 2 3 3h0c-4 1-6-1-9-3l-5-2z" class="o"></path><path d="M483 770l9-3-1 2 1 1-1 2c1-1 1-1 2-1l1-1 1-2c1 0 1 1 2 1l1-1 1 1c-3 1-4 3-6 6h0l-3 3-5 7-1-1-1 2c-1 0-1 0-2-1l-2-3v-4c0-3 1-6 4-8z" class="G"></path><path d="M494 770l1-2c1 0 1 1 2 1l1-1 1 1c-3 1-4 3-6 6h0l-3 3c-2 0-2 2-4 2h0c1-1 1-1 1-2 1 0 2-1 3-2 0-1 1-2 1-3 1 0 1 0 2-1l1-2z" class="Z"></path><path d="M483 770l9-3-1 2c-1 1-3 2-4 3l-3 6c-1 1-1 3-2 5-1-1-1-1-1-2v1h-1-1v-4c0-3 1-6 4-8z" class="g"></path><defs><linearGradient id="AR" x1="476.075" y1="773.549" x2="485.764" y2="783.755" xlink:href="#B"><stop offset="0" stop-color="#836c60"></stop><stop offset="1" stop-color="#a89285"></stop></linearGradient></defs><path fill="url(#AR)" d="M474 779c2-4 4-9 8-12 1-1 3-2 5-3h3v1c-3 1-5 3-7 5-3 2-4 5-4 8v4l2 3c1 1 1 1 2 1l1-2 1 1c-2 2-3 5-4 8-1 2-2 6-2 9 0 1 0 2-1 3l-2-2v-1c1-1 1-2 1-4h-1c0 1 0 1-1 1 0 0-1 1-1 2-1-3-1-6-1-8v-2c1-3 0-7 0-10l1-2z"></path><path d="M473 781l1 4 1 1v4l-2 3v-2c1-3 0-7 0-10z" class="AI"></path><path d="M475 790c0 3 0 6 1 8 0-2 1-4 1-6s1-5 1-8l-1-2h1c1 2 1 3 2 4h1v1 2l-2 2-1-1v4c0 2-1 3-1 4h-1c0 1 0 1-1 1 0 0-1 1-1 2-1-3-1-6-1-8l2-3z" class="h"></path><defs><linearGradient id="AS" x1="476.115" y1="793.724" x2="481.036" y2="798.033" xlink:href="#B"><stop offset="0" stop-color="#beaba4"></stop><stop offset="1" stop-color="#cec6b8"></stop></linearGradient></defs><path fill="url(#AS)" d="M481 785c1 1 1 1 2 1l1-2 1 1c-2 2-3 5-4 8-1 2-2 6-2 9 0 1 0 2-1 3l-2-2v-1c1-1 1-2 1-4 0-1 1-2 1-4v-4l1 1 2-2v-2-1-1z"></path><path d="M620 519c-1 5-3 9-6 13 0 1-1 3-2 4h-2c-2-1-4-1-6-2 0-2-1-3 0-5 0-6 2-13 4-19 2-9 3-18 6-26 0-2 1-3 2-4 2-1 5-1 7-1h1l1 1c3 1 4 3 4 6-2 12-5 22-9 33z" class="p"></path><path d="M615 496l-2 2c-1-1-1 0-1-1 0-3 1-5 2-7v-3c1-1 3-2 4-3l2-2c3 1 4 1 6 3 1 3 1 7 0 10-1 6-3 12-5 19l-1-5c2-4 4-10 4-14 0-3 0-4-1-5 1-2 1-3 1-4-2 4-6 8-9 10z" class="d"></path><path d="M615 496l-1-1c1-1 1-2 1-3h0l1-1v-2-1c1-1 3-2 4-3 1 0 3-1 4 1-2 4-6 8-9 10zm8-6c1 1 1 2 1 5 0 4-2 10-4 14l1 5-6 15c-1 2-2 4-3 5-1 0-2 0-2-1-2 0-2-2-3-3 0-3 0-8 1-11 2-4 1-9 2-14 1-1 1-2 2-3l2-3c3-4 6-6 9-9z" class="J"></path><path d="M610 533c0-1 2-1 2-2 2-2 4-5 4-7h-1c1-2 2-3 2-5h0c1-3 1-5 2-7 0-1 1-2 1-3l1 5-6 15c-1 2-2 4-3 5-1 0-2 0-2-1z" class="K"></path><path d="M607 530c0-3 0-8 1-11 2-4 1-9 2-14 1-1 1-2 2-3 0 3-1 7 1 10v1h0c1-1 3-2 4-3h1l-5 5c-1 1-2 3-3 5 0 1 0 1-1 1v2 2c-1 1-1 2-1 2l-1 3z" class="o"></path><path d="M555 726h3l4 5v1 2h1c0 4 0 6-2 10-2 3-4 5-4 9 1 4 1 8 0 13-1 4-2 7-6 10-3 1-5 2-8 1-1 0-1-1-2-1-2-2-3-4-3-7-1-5 0-11 1-16 0-4 1-8 3-12 2-2 3-4 4-6 3-5 4-7 9-9z" class="O"></path><path d="M541 749c1 0 1-1 2-1h1c0-1 0-2 1-2 0-1 0-2 1-3 0-1 0-1 1-2-1 4-2 7-4 11 0 1 0 2-1 3-1 4-3 6-3 10l-1 4c-1-5 0-11 1-16 1-2 2-3 2-4z" class="p"></path><path d="M555 726h3l4 5v1 2 1h-1c-1-1-2-1-4-1v1l-2 2-2 1v-1c1-1 2-2 2-3l-2-1c-2 2-4 5-6 8-1 1-1 1-1 2-1 1-1 2-1 3-1 0-1 1-1 2h-1c-1 0-1 1-2 1 0 1-1 2-2 4 0-4 1-8 3-12 2-2 3-4 4-6 3-5 4-7 9-9z" class="AI"></path><path d="M553 733c3-2 1-3 4-3v4h0v1l-2 2-2 1v-1c1-1 2-2 2-3l-2-1z" class="AO"></path><path d="M542 741c2-2 3-4 4-6h1v1c-2 4-4 8-6 13 0 1-1 2-2 4 0-4 1-8 3-12z" class="h"></path><path d="M555 726h3l4 5v1c-2-2-3-4-5-4-4 1-7 4-10 6v1h-1c3-5 4-7 9-9z" class="d"></path><path d="M562 734h1c0 4 0 6-2 10-2 3-4 5-4 9-1 2-1 2-1 4-1 2-1 7-2 9 0 1-1 2-2 3 0 1-2 2-3 3-1-1-2-2-3-2h-1v-1h0 1c-1-2 0-5 1-7 0-2 0-5-1-8l1-1 6-15 2-1 2-2v-1c2 0 3 0 4 1h1v-1z" class="G"></path><path d="M562 734h1c0 4 0 6-2 10-2 3-4 5-4 9-1 2-1 2-1 4-1 2-1 7-2 9v-5c-1-4-2-9 0-12 1-2 2-5 3-7v-2c1-1 1-1 1-3h-1-1-1l2-2v-1c2 0 3 0 4 1h1v-1z" class="b"></path><path d="M562 734h1c0 4 0 6-2 10 0-1-2-1-2-2-1-1 0-4 0-5s-2-2-2-2v-1c2 0 3 0 4 1h1v-1z" class="O"></path><path d="M665 121c3-1 6-1 9-1l1 1-1 1c2 0 4 0 5 1h4v-1l1-1-1 4 1 1c1 1 1 1 3 1v-1l1 1c4 0 6 5 10 6 1 1 4 1 5 1h1c-3 1-8 0-11 1 2 0 4 1 6 0l2 2c-5-1-10-1-15 0v2l-3 3-5 2h-3l-1 2h0c-2 0-3-1-4-2-1-2-1-4 0-6-2-1-8-2-9-2s-1 1-1 1h-4c-2 0-2 1-3 2s-2 1-3 1l-6 5c-1 3-2 7-3 11l-1 3-2 2c-1-2-3-6-2-7 0-1 0-2 1-3v-3-2l1-1c0-1 1-2 1-3 2-4 6-10 10-12l1-1c2 0 3-1 4-2 4-2 7-4 11-6z" class="Z"></path><path d="M658 127l2-2c4-3 7-4 12-4-2 2-3 2-5 2l-1 1h-2v1l1 1-7 5-1-1h1v-3z" class="AJ"></path><path d="M643 138c2-3 5-5 8-8l3 5h-2c-3 3-6 6-8 10-1 3-2 7-3 11l-1 3-2 2c-1-2-3-6-2-7 0-1 0-2 1-3v-3c2-3 4-7 6-10z" class="AI"></path><path d="M643 138c2-3 5-5 8-8l3 5h-2c-1 1-2 1-3 2l-1 1c-2 1-3 3-5 5l-1 1h0l3-6h0-2z" class="AT"></path><path d="M683 122l1-1-1 4 1 1c1 1 1 1 3 1v-1l1 1c2 2 3 4 5 5-1 0-2 0-2-1h-7c-2 1-3 1-5 1h-1l-21 1-2-1v-1c1-2 2-3 3-4v3h-1l1 1 7-5-1-1v-1h2l1-1c2 0 3 0 5-2l2 1c2 0 4 0 5 1h4v-1z" class="p"></path><path d="M668 130v-1c2-1 3 0 5 0v1h-5z" class="AI"></path><path d="M681 127c1-1 2-1 2-2l1 1c1 1 1 1 3 1v-1l1 1c2 2 3 4 5 5-1 0-2 0-2-1l-3-2-1 1-3-1c-1 0-2-1-3-2z" class="AT"></path><path d="M673 130l9-1 2 2c-2 1-3 1-5 1h-1l-21 1c0-1 0-1 1-1 1-1 3-1 4-1 2 0 4 0 6-1h5z" class="O"></path><defs><linearGradient id="AT" x1="666.853" y1="129.102" x2="678.881" y2="120.735" xlink:href="#B"><stop offset="0" stop-color="#775e59"></stop><stop offset="1" stop-color="#8b7567"></stop></linearGradient></defs><path fill="url(#AT)" d="M683 122l1-1-1 4c0 1-1 1-2 2-5-2-11-2-16-1l-1-1v-1h2l1-1c2 0 3 0 5-2l2 1c2 0 4 0 5 1h4v-1z"></path><path d="M688 127c4 0 6 5 10 6 1 1 4 1 5 1h1c-3 1-8 0-11 1 2 0 4 1 6 0l2 2c-5-1-10-1-15 0v2l-3 3-5 2h-3l-1 2h0c-2 0-3-1-4-2-1-2-1-4 0-6-2-1-8-2-9-2s-1 1-1 1h-4c-2 0-2 1-3 2s-2 1-3 1l-6 5c2-4 5-7 8-10h2c4 1 7-1 11-1 3 0 9 0 13-2h1c2 0 3 0 5-1h7c0 1 1 1 2 1-2-1-3-3-5-5z" class="h"></path><path d="M686 135h-5-2v-2h9v1h0l-1 1h-1z" class="O"></path><path d="M688 127c4 0 6 5 10 6 1 1 4 1 5 1h1c-3 1-8 0-11 1 2 0 4 1 6 0l2 2c-5-1-10-1-15 0v2l-3 3-5 2h-3l-1 2h0c0-1-1-2-2-3v-1c2-2 6-5 9-5l5-2h1c2-1 5 0 8-1v-1l-2-1c-2-1-3-3-5-5z" class="i"></path><path d="M675 142c2-3 7-4 11-5v2l-3 3-5 2h-3-1l1-2z" class="k"></path><path d="M675 142l1 1 1-1c2-2 4-1 6 0l-5 2h-3-1l1-2z" class="h"></path><path d="M499 783h5v1h1s1 2 0 2c0 2-1 4-1 6h0c-2 9 2 19-1 28 0 2-2 5-4 7l-1 3c-1 0-2 1-2 2-1 4 1 12 2 16v2c-1-2-2-5-3-7l-2-1c-2-4-3-9-5-13v-3l-1-2 1-3c-5-5-5-10-6-17 1-5 2-10 4-15l3-4c1-1 2-1 3-2l1 1-2 3h0l1 3c2-3 4-5 7-7z" class="Z"></path><path d="M501 801h-1v-10l1-1v-2h1c0-2 0-2 1-3 1 0 1 0 2 1 0 2-1 4-1 6h0l-1-4c-3 3-2 10-2 13z" class="i"></path><path d="M492 819l2 2h3c2 0 3-1 4-3 0 3 0 5-3 6-1 1-2 1-3 1l-2-2c0-1 0-2-1-3v-1z" class="L"></path><path d="M499 783h5v1h0-2v1c-1 1-1 1-1 2-1 0-2 0-3 2-1 0-1 1-1 1v3h-3c-1 1-1 3-2 5 0-1 1-3 1-5-1 1-3 4-3 6h0c0 3-1 6-1 9h0l-1-2c0-3 1-6 2-10 1-1 2-3 2-5v-1c2-3 4-5 7-7z" class="G"></path><defs><linearGradient id="AU" x1="482.151" y1="798.905" x2="496.625" y2="805.534" xlink:href="#B"><stop offset="0" stop-color="#927a6c"></stop><stop offset="1" stop-color="#afa096"></stop></linearGradient></defs><path fill="url(#AU)" d="M489 785c1-1 2-1 3-2l1 1-2 3h0l1 3v1c0 2-1 4-2 5-1 4-2 7-2 10l1 2v8c1 1 2 3 3 3v1c1 1 1 2 1 3l2 2c-3 0-5-2-7-4-5-5-5-10-6-17 1-5 2-10 4-15l3-4z"></path><path d="M486 805l1 1s0 1 1 2v-1-1l1 2v8c-1-1-2-2-2-3v-1c-1-2-1-5-1-7z" class="O"></path><path d="M491 787l1 3v1c0 2-1 4-2 5-1 4-2 7-2 10v1 1c-1-1-1-2-1-2l-1-1c1-7 2-12 5-18z" class="p"></path><path d="M501 801c0-3-1-10 2-13l1 4c-2 9 2 19-1 28 0 2-2 5-4 7l-1 3c-1 0-2 1-2 2-1 4 1 12 2 16v2c-1-2-2-5-3-7l-2-1c-2-4-3-9-5-13v-3l-1-2 1-3c2 2 4 4 7 4 1 0 2 0 3-1 3-1 3-3 3-6 2-1 0-14 0-17z" class="J"></path><path d="M488 826c3 2 4 3 5 6v5c-2-4-4-7-5-11z" class="n"></path><path d="M488 826h0c1 4 3 7 5 11l2 6-2-1c-2-4-3-9-5-13v-3z" class="h"></path><path d="M472 603c1 1 1 2 1 2l2 9 2 2c0 1 0 1 1 2v-1c1-1 1-2 1-3l4 7c1 3 3 5 3 8 0 1 1 2 1 3 1 2 0 4 1 6l1 1-2 1c-3 2-7 3-10 4l-1 3v3c1 1 2 1 4 1 3 0 5 1 8 3s6 7 7 10v1c-1-2-2-2-3-3-1-2-1-3-2-4-2-1-4-4-6-5-1 0-2 0-2-1-2-1-3 0-4 0h-1l3 1c0 1 1 0 2 0 0 0 1 2 2 2 1 1 3 2 4 4v1c1 1 0-1 1 1h0c-1 0 0 0-1-1-2-2-4-3-7-5l-1-1-2 1-4-1v3c-4-1-4-6-7-8-1 0-2 0-3 1h0c1-1 2-2 2-3-1-2-2-2-4-2l1-1c-1 0-2-1-3-1h-3c2-1 4-2 7-2v-1h-1l1-1c0-2 0-4-1-6v-4l-1-1c0-1-1-2-2-3v-1l2-2c1-1 2-1 4-2l2-2v-1-1h-1l-1 1h-2c-1 1-1 2-3 3l-4 4-5 8c-1 1-1 1-3 2h0c2-6 8-12 13-16h-2-1c-1 1-3 2-4 2 1-3 5-6 8-9-2 1-5 2-6 3-1 0-1-1-2-1 0 0-2 1-2 2l-3-1c0-1 1-1 2-2 1 0 3 0 5-1 3-2 7-1 11-1h1c0-1 0-1 1-1h0l3-2-2-2 1-2z" class="d"></path><path d="M486 629c0 1 1 2 1 3 1 2 0 4 1 6l1 1-2 1c-3 2-7 3-10 4-3 0-4-1-6-2-2 0-3-1-4-2l-1-1c-1-2-1-4-1-6h1c0 2 0 4 1 5 2 0 3 1 4 2 5 1 8 1 13-2 2-2 2-6 2-9z" class="J"></path><path d="M460 643h-3c2-1 4-2 7-2 1 0 2 1 3 1 2 1 5 2 6 5v2h0c1 2 1 4 1 5v3c-4-1-4-6-7-8-1 0-2 0-3 1h0c1-1 2-2 2-3-1-2-2-2-4-2l1-1c-1 0-2-1-3-1z" class="n"></path><defs><linearGradient id="AV" x1="470.311" y1="609.533" x2="455.536" y2="620.05" xlink:href="#B"><stop offset="0" stop-color="#baaaa0"></stop><stop offset="1" stop-color="#dbd5cf"></stop></linearGradient></defs><path fill="url(#AV)" d="M473 607c1 4 1 7-1 11-1 0-1 1-1 1l-3 2c-2 0-3 2-4 4v2c-1-1-2-1-3-2 1-2 4-3 5-4 3-2 4-6 4-9-3 1-6 4-8 6h0-2-1c-1 1-3 2-4 2 1-3 5-6 8-9-2 1-5 2-6 3-1 0-1-1-2-1 0 0-2 1-2 2l-3-1c0-1 1-1 2-2 1 0 3 0 5-1 3-2 7-1 11-1h1c0-1 0-1 1-1h0l3-2z"></path><path d="M479 614l4 7c1 3 3 5 3 8s0 7-2 9c-5 3-8 3-13 2-2-2-3-5-4-8v-3c0-3 3-5 5-7l2-2c1-1 1-4 1-6l2 2c0 1 0 1 1 2v-1c1-1 1-2 1-3z" class="J"></path><path d="M470 629c1 1 1 2 2 3 0 2 1 3 2 4v1l-3-1-1 1c0-1-1-2-1-2-1-2 0-4 1-6z" class="i"></path><path d="M473 625c2-2 3-2 5-2h0c0 1 0 1-1 2h2c-3 1-4 2-6 4l-1 3c-1-1-1-2-2-3 0-2 2-3 3-4z" class="W"></path><path d="M483 621c1 3 3 5 3 8s0 7-2 9c-5 3-8 3-13 2-2-2-3-5-4-8 1-1 1-2 1-3l1-1v-1c1-2 3-2 4-2-1 1-3 2-3 4-1 2-2 4-1 6 0 0 1 1 1 2 1 0 2 1 3 2 2 1 6 1 8 0l3-3c1-3 0-5-1-8l-1-3 1-4z" class="b"></path><path d="M479 614l4 7-1 4 1 3c-2-1-3-2-4-3h-2c1-1 1-1 1-2h0c-2 0-3 0-5 2-1 0-3 0-4 2v1l-1 1c0 1 0 2-1 3v-3c0-3 3-5 5-7l2-2c1-1 1-4 1-6l2 2c0 1 0 1 1 2v-1c1-1 1-2 1-3z" class="h"></path><path d="M478 623v-1h-1-2c1-1 0-1 1-1s2-1 2-2c2 2 3 4 4 6l1 3c-2-1-3-2-4-3h-2c1-1 1-1 1-2h0z" class="L"></path><path d="M420 511c0-4 3-7 2-12-2-12-6-24-11-35-1-2-1-5-2-7s-2-3-3-5h1l4 7h1l-2-6 4 4v3h0c0 6 4 12 6 18 2 3 3 7 5 11v2c-1 2 1 5 1 7 1 2 1 5 1 7l1 2v1c0 1 0 2 1 2v1l1 2c1 0 2 1 4 1l-1 1-2 1-1 1c-1 5 2 11 5 15 5 3 8 4 14 4 0 1 1 2 1 4h-3c0 1 0 1 1 1 2 2 3 4 5 6v1c1 1 1 2 2 4v1l1 3c5 9 9 17 11 27 1 5 2 12 4 17l1 3-1 2c-3-3-5-3-10-3l1-1-1-1 1-1c2-6 0-13-1-19-1-3-3-11-5-12 0-1 0-1-1-1v-1h0c-1-2-2-6-3-8-1 0-2 0-2-1-2-2-3-4-5-7v1l2 5h-1c-4-8-10-14-15-22-3-4-5-9-7-14-1-3-3-5-3-7 0-1-1-1-1-2z" class="AP"></path><path d="M448 546c3 2 5 4 7 7h0l1 3c-1-1-3-2-3-4-2-1-2-1-3-2l-2-4z" class="J"></path><path d="M411 459h1l-2-6 4 4v3h0c0 6 4 12 6 18v2c-3-8-7-14-9-21z" class="G"></path><path d="M420 478c2 3 3 7 5 11v2c-1 2 1 5 1 7 1 2 1 5 1 7l1 2v1c0 1 0 2 1 2v1l1 2-3-1c-1-1-2-3-2-4-1-3-1-6-1-9-1-6-3-13-4-19v-2z" class="W"></path><path d="M435 532c5 3 8 4 14 4 0 1 1 2 1 4h-3c0 1 0 1 1 1 2 2 3 4 5 6v1c1 1 1 2 2 4v1h0c-2-3-4-5-7-7-5-4-10-8-13-14z" class="Z"></path><path d="M446 556c-4-8-10-14-15-22-3-4-5-9-7-14-1-3-3-5-3-7 4 7 6 14 11 21s12 11 17 18c8 8 12 20 14 31 1 5 2 9 0 14 0 1 0 2-1 4l-1-1 1-1c2-6 0-13-1-19-1-3-3-11-5-12 0-1 0-1-1-1v-1h0c-1-2-2-6-3-8-1 0-2 0-2-1-2-2-3-4-5-7v1l2 5h-1z" class="O"></path><path d="M732 204h2 1c0 3-1 5 0 8h0c1 1 1 0 2 1 1 0 2 0 3 2 1 1 1 3 2 4-1 4 1 5 1 8l-1 1h0c0-1-1-2-2-4 2 9 3 20 0 28-3 6-7 12-13 14-2 0-4 1-6 1h0-2-3-7c0-1 0-2-1-3l4-3c2-1 3-1 4-1h1v-1c0-3 0-6 1-9s4-4 7-5h1v-1c-1-1-2-2-3-4 0-1 0-3 1-5 0-4 5-6 7-8 0-1-1-2-2-3-1-3-2-4-1-7 0-3 0-6 2-8h0c1-1 2-3 2-5z" class="Aa"></path><path d="M717 259l1-1v-2c0-2 0-5 1-6l1-1h0c1 4 1 8 0 12v1l-3-2v-1z" class="AT"></path><path d="M735 212l-1 1c-2 3-4 6-3 10 0 2 2 3 3 5h-1l-2-1c0-1-1-2-2-3-1-3-2-4-1-7l1-1 2-2c1-1 2-2 4-2z" class="Z"></path><path d="M732 204h2 1c0 3-1 5 0 8-2 0-3 1-4 2l-2 2-1 1c0-3 0-6 2-8h0c1-1 2-3 2-5z" class="L"></path><path d="M735 212h0c1 1 1 0 2 1 1 0 2 0 3 2 1 1 1 3 2 4-1 4 1 5 1 8l-1 1h0c0-1-1-2-2-4-2-3-2-8-6-11l1-1z" class="Y"></path><path d="M731 227l2 1c-4 2-4 4-5 8s-1 7 1 10l1 2c-2-1-4-2-5-3h1v-1c-1-1-2-2-3-4 0-1 0-3 1-5 0-4 5-6 7-8z" class="h"></path><path d="M598 548c1-3 2-9 6-11h2 0c2 0 4 1 5 2 1 0 2 1 2 3 0 1 0 2-1 3l-2 13-1 3c0 1 0 2-1 4v2l-1 1v1l-1 4v1l-2 6-1 3h-1v-1l1-2c0-1 0-3 1-4v-3s0-1 1-1v-2c0-1 0-3 1-4v-1c-1 0-1-1-1-1v-5l-3 8c-1 2-2 5-2 8 0 1 0 1-1 2v2 1s0 1-1 1v2c-1 3-3 6-4 9l-1 1c-1 6-3 12-6 17-2 4-3 9-5 13-1 0-2 2-2 3-3 3-4 8-5 11-1 2-2 4-2 6-2 2-3 6-4 9l-2 8c0 1-1 1-2 1 0-4 2-10 4-14-1-2 1-7 2-9 2-10 5-20 8-29h1c1-4 2-8 3-13l3-9c1-4 1-8 3-12 2-2 3-6 4-9l2-8 1-4h1v-2c0-1 1-3 1-4z" class="L"></path><path d="M587 593c0-2 0-4 1-6 1-8 4-16 7-24 0 3-1 7 0 9v3c-2 2-2 4-3 6l-1 6c-2 2-3 3-3 4l-1 2z" class="d"></path><path d="M569 647h0c3-6 4-13 6-20 0-2 1-5 2-7v1l-1 6c-1 2 0 2 0 3 0 2-1 3-1 4s-1 2-1 3h1c-1 2-2 4-2 6-2 2-3 6-4 9l-2 8c0 1-1 1-2 1 0-4 2-10 4-14z" class="W"></path><path d="M605 559l2-8 2-5c1 0 1 0 1-1s0-1 1-2h0 0l-1 7v1 1c-1 1-1 2-1 3l-1 5h0l1 1c0 1 0 2-1 4v2l-1 1v1l-1 4v1l-2 6-1 3h-1v-1l1-2c0-1 0-3 1-4v-3s0-1 1-1v-2c0-1 0-3 1-4v-1c-1 0-1-1-1-1v-5z" class="d"></path><path d="M587 593l1-2c0-1 1-2 3-4 1 5-1 8-2 12l-3 9c0 1 0 1 1 2-2 4-3 9-5 13-1 0-2 2-2 3-3 3-4 8-5 11h-1c0-1 1-2 1-3s1-2 1-4l11-37z" class="i"></path><path d="M595 572c1-6 3-12 5-18 1-4 3-7 5-11h1c1 1 1 2 1 3 0 6-3 13-5 18-1 3-3 7-2 11 0 1 0 1-1 2v2 1s0 1-1 1v2c-1 3-3 6-4 9l-1 1c-1 6-3 12-6 17-1-1-1-1-1-2l3-9c1-4 3-7 2-12l1-6c1-2 1-4 3-6v-3z" class="J"></path><path d="M595 575c0 3 0 6-1 9l-1 5h-1c0-3 1-5 1-7v-1h-1c1-2 1-4 3-6z" class="K"></path><path d="M584 648c0-1 1-2 2-3-1 2-1 2-1 3s0 1-1 2v1l-1 2c0 1 0 2-1 3v3l-13 45v-2c0-1 0-4 1-6h0c-1 1-1 1-1 2s0 1-1 2c-1 3-1 6-1 9-1 3-2 5-2 8-1 4-1 9-1 13h0 0c0 1 0 2 1 3h-1l-2-2-4-5h-3c-5 2-6 4-9 9-1 2-2 4-4 6l1-5c1-4 3-7 4-11 2-8 3-17 6-25 0-2 1-6 2-8l6-17h2c2-3 3-5 5-7 3 0 7-3 9-5l3-3s4-10 4-12z" class="J"></path><path d="M558 726l1-1h-2l-2-1v-2c1 1 2 1 3 2l1-1c2 2 4 4 5 7h0c0 1 0 2 1 3h-1l-2-2-4-5z" class="G"></path><g class="i"><path d="M557 708c1 4 1 7 1 11l2 1v2s-1 0-1 1l-1 1c-1-1-2-1-3-2h1 1l-1-2c0-4 0-8 1-12z"></path><path d="M572 675h0l-3 12c-1 0-1 0-2 1 0-2 1-4 1-6h1l-1-1c-4 4-7 12-8 18-1 3-1 6-3 9v-3c1-7 2-14 6-20h0c2-4 5-7 9-10z"></path></g><path d="M568 668c3 0 7-3 9-5l3-3c-1 3-1 4-3 6-1 2-2 3-3 5l-6 22-3 18c-1 2-1 5-2 7v-1c0-4 1-9 2-13 1-6 2-12 4-17l3-12h0l-1-1c-2 1-3 3-4 3l-1-1c-2 2-3 5-5 7 1-1 1-3 1-4l1-4c2-3 3-5 5-7z" class="Z"></path><path d="M563 675l-1 4c0 1 0 3-1 4 2-2 3-5 5-7l1 1c1 0 2-2 4-3l1 1c-4 3-7 6-9 10h0c-4 6-5 13-6 20v3h0c-1 4-1 8-1 12l1 2h-1-1v2l2 1h2l-1 1h-3c-5 2-6 4-9 9-1 2-2 4-4 6l1-5c1-4 3-7 4-11 2-8 3-17 6-25 0-2 1-6 2-8l6-17h2z" class="h"></path><path d="M556 722c-1 0-2-2-2-2v-4c-1-3 0-6 1-8l2-3v3h0c-1 4-1 8-1 12l1 2h-1z" class="L"></path><path d="M553 700c0 2 0 8-1 9v1c-1 1-1 3-1 5l-1 2h1s0-1 1-2c0-1 0-1 1-2 0 2-1 4-1 5 0 2-1 3-1 4l1 1v-2h1c0 2 0 2-1 3l-1 1-1 1 1 1v-1l3-1 1 1c-5 2-6 4-9 9-1 2-2 4-4 6l1-5c1-4 3-7 4-11 2-8 3-17 6-25z" class="p"></path><path d="M458 517v1c1 1 1 0 1 1h2 1c2 5 5 10 9 13 3 3 6 6 8 10h1c4 6 10 11 13 18 1 3 2 6 2 9 1-1 2-1 3-1v3l2 2c0 3 0 4-2 6v1 7c1-1 2-2 2-3 0-2 0-4 1-5 0 5-1 9-1 13 0 2-1 3-1 4-1 3-1 6 0 9l-1 1-1-1c-5-1-8-4-11-6-1-2-1-2-3-3 0-1-1-3-1-3l-3-10-21-66z" class="AX"></path><path d="M497 605c-1-1-1-2-2-3h0v-2c-6-11-13-23-17-35 0-1 0-2-1-3 0-2 0-4-1-5v-1-1l1 1v2c1 0 1 1 1 2l1 3 3 7c3 7 5 14 9 20l1 1c1 2 3 7 5 9v2l1 1v-16c1-1 2-2 2-3 0-2 0-4 1-5 0 5-1 9-1 13 0 2-1 3-1 4-1 3-1 6 0 9l-1 1-1-1z" class="C"></path><defs><linearGradient id="AW" x1="494.607" y1="565.629" x2="483.347" y2="570.845" xlink:href="#B"><stop offset="0" stop-color="#310101"></stop><stop offset="1" stop-color="#1f0203"></stop></linearGradient></defs><path fill="url(#AW)" d="M479 542h1c4 6 10 11 13 18 1 3 2 6 2 9 1-1 2-1 3-1v3l2 2c0 3 0 4-2 6v1 7 16l-1-1v-2c-2-2-4-7-5-9l1-1-4-8-2-4-1-3-2-5-1-4c-1-2-2-4-2-7l-2-4v-1c-1-2-4-9-3-11v-1l1 1c1-1 1-1 2-1z"></path><path d="M498 580v7 16l-1-1v-2l-1-18 2-2z" class="E"></path><path d="M495 569c1-1 2-1 3-1v3l2 2c0 3 0 4-2 6v1l-2 2-1-13z" class="F"></path><path d="M498 571l2 2c0 3 0 4-2 6v-8z" class="R"></path><path d="M479 555v-1c-1-2-4-9-3-11l9 13c1 1 2 2 2 3-1-1-2-1-4-1-1 0-1 0-2-1 0-1 0-2-1-3l-1 1z" class="S"></path><path d="M479 555l1-1c1 1 1 2 1 3 1 1 1 1 2 1 2 0 3 0 4 1 2 3 4 7 4 11l1 1h0c1 3 0 7 1 10-1 3 1 7 0 9l-4-8-2-4-1-3-2-5-1-4c-1-2-2-4-2-7l-2-4z" class="a"></path><path d="M483 558c2 0 3 0 4 1 2 3 4 7 4 11h-1c1 1 1 3 1 5v1l-1-1v-2c-2-2-4-6-4-9v-1s-1-1-1-2l-1-1c0-1 0-1-1-2z" class="Q"></path><path d="M707 120c2 0 3-1 5-1 3-1 7-1 9 1h1c1 1 3 2 4 3v-2h3v1l-1 1c1 2 3 2 5 3 1 1 1 1 3 1l1 2c2 2 3 4 4 7 1 1 1 3 1 4-1 4 0 7-1 11-1 6-3 10-6 15l-1 1v2h1l-4 14c0 1 0 2-1 3v2 1c0-2-1-3-1-4h1c0-2 0-1-1-2h0v3c0 2 0 3-1 5l-2 9h-1c2-4 2-9 2-14 0-9 0-18-3-26-3-6-7-13-13-16h0c-1-1-3-2-4-3-2 0-3-1-4 0h-1c-2-2-4-2-6-2-1 0-3-1-4-1-2 1-4 1-6 1v-2c5-1 10-1 15 0l-2-2c-2 1-4 0-6 0 3-1 8 0 11-1h-1l-1-1v-1-5c1-3 1-5 4-8l1 1z" class="J"></path><path d="M721 140c5 5 8 13 10 20l2 4h1v1h1v1l-1 1v2h1l-4 14c-1-2 0-4 0-7 1-2 0-4 0-7-1-7-4-13-8-20 2-3-2-6-2-9z" class="n"></path><path d="M731 160l2 4h1v1h1v1l-1 1v2l-1 3h0-1l-1-12z" class="W"></path><path d="M686 137c5-1 10-1 15 0 10 3 17 9 22 18 3 6 4 13 5 20 1 5 0 10 0 16l-2 9h-1c2-4 2-9 2-14 0-9 0-18-3-26-3-6-7-13-13-16h0c-1-1-3-2-4-3-2 0-3-1-4 0h-1c-2-2-4-2-6-2-1 0-3-1-4-1-2 1-4 1-6 1v-2z" class="AI"></path><defs><linearGradient id="AX" x1="707.75" y1="142.73" x2="719.54" y2="137.58" xlink:href="#B"><stop offset="0" stop-color="#a5938a"></stop><stop offset="1" stop-color="#d0c4bb"></stop></linearGradient></defs><path fill="url(#AX)" d="M702 133c1 0 2-1 3-1 0-2 0-4 1-5 1-2 2-2 5-3v2c1 0 2 0 3 1 2 0 4 1 5 1 2 2 7 1 10 2-2 0-4 0-6 1h-2c-1 1 0 0 0 1l-1 1-2-1v2l3 2-1 3 1 1c0 3 4 6 2 9-5-7-11-12-19-15h-1l-1-1z"></path><path d="M711 126c1 0 2 0 3 1 2 0 4 1 5 1 2 2 7 1 10 2-2 0-4 0-6 1h-2c-1 1 0 0 0 1l-1 1-2-1v2l3 2-1 3c-3-2-7-6-10-9 0-1 0-1-1-2v-1l2-1z" class="L"></path><path d="M714 127c2 0 4 1 5 1 2 2 7 1 10 2-2 0-4 0-6 1h-2c-1 1 0 0 0 1l-1 1-2-1-6-5h2z" class="W"></path><path d="M707 120c2 0 3-1 5-1 3-1 7-1 9 1h1c1 1 3 2 4 3v-2h3v1l-1 1c1 2 3 2 5 3 1 1 1 1 3 1l1 2s-2 0-2 1l-1 1c0 1-5-1-5-1-3-1-8 0-10-2-1 0-3-1-5-1-1-1-2-1-3-1v-2c-3 1-4 1-5 3-1 1-1 3-1 5-1 0-2 1-3 1v-1-5c1-3 1-5 4-8l1 1z" class="AI"></path><path d="M702 127c1-3 1-5 4-8l1 1c-1 1-1 2-2 3 2 0 3 0 5-1-2 1-3 2-4 3s-2 1-3 2h-1z" class="AJ"></path><path d="M711 124h8l1 1c-1 2 1 2-1 3-1 0-3-1-5-1-1-1-2-1-3-1v-2z" class="b"></path><path d="M721 120h1c1 1 3 2 4 3l-1 1h-3c-1-1-3-1-4-1-2-1-4-1-7-1 3-1 8-1 10-2z" class="Ab"></path><path d="M707 120c2 0 3-1 5-1 3-1 7-1 9 1-2 1-7 1-10 2h-1c-2 1-3 1-5 1 1-1 1-2 2-3z" class="Ad"></path><path d="M720 125c4 2 9 2 13 1 1 1 1 1 3 1l1 2s-2 0-2 1l-1 1c0 1-5-1-5-1-3-1-8 0-10-2 2-1 0-1 1-3z" class="L"></path><path d="M737 129c2 2 3 4 4 7 1 1 1 3 1 4-1 4 0 7-1 11-1 6-3 10-6 15v-1h-1v-1h-1l-2-4c-2-7-5-15-10-20l-1-1 1-3-3-2v-2l2 1 1-1c0-1-1 0 0-1h2c2-1 4-1 6-1 0 0 5 2 5 1l1-1c0-1 2-1 2-1z" class="K"></path><path d="M737 129c2 2 3 4 4 7v-1l-3-3c-1 0-2-1-3 0h-1l1 2h0c-1 0-2-1-2-1-2-1-5-1-7-1h-5l5 3c1 1 2 1 4 1l2 3-12-6 1-1c0-1-1 0 0-1h2c2-1 4-1 6-1 0 0 5 2 5 1l1-1c0-1 2-1 2-1z" class="d"></path><path d="M718 132l2 1 12 6c2 2 4 5 4 9s0 9-1 13v4h-1v-1-3c1-5 2-13 0-18-1-4-9-5-13-7h0l-3-2v-2z" class="G"></path><path d="M721 136h0c4 2 12 3 13 7 2 5 1 13 0 18v3h-1l-2-4c-2-7-5-15-10-20l-1-1 1-3z" class="J"></path><defs><linearGradient id="AY" x1="635.307" y1="271.643" x2="678.921" y2="307.136" xlink:href="#B"><stop offset="0" stop-color="#ded9d3"></stop><stop offset="1" stop-color="#fdfdfd"></stop></linearGradient></defs><path fill="url(#AY)" d="M660 235c1-2 3-3 5-4 1 0 2 0 3-1h1l1 2h0l5 5c1 4 2 10 0 14l-1 1c-1 1-1 4-2 6-1-3 0-7 0-10 0 1-1 2-1 3v1l-1-3h0v13l-2 13c-1 3-2 7-1 10 0 2 0 5-1 7h1v-2h1l1-2v1 1 1h0v1l-1 1c0 1 0 1 1 2l5-19c1 1 1 1 1 2 1 3 0 6-1 9v4c0 1 0 2-1 3v6h0l-1 1v4c-1 2-1 2-1 4v1c0 2 1 2 2 4l-4 5 2 2-4 6c-1 0-1 1-2 2h0l-4 6h-1v-1h-1-1l-9 8c-1 0-1 0-2 1h1l-2 2h-1c2-4 3-8 6-12 4-6 8-12 6-20 0-2-2-5-1-7 0-4 1-8 1-12l1-3c2-7 3-15 3-23-1 3-1 6-1 9l-1-1v3l-1-1c-1 3-1 7-3 9 0-3 1-6 1-9l1-20-1-18h1c1-1 1-3 1-4 1-1 1-1 2-1z"></path><defs><linearGradient id="AZ" x1="670.757" y1="323.183" x2="658.743" y2="329.817" xlink:href="#B"><stop offset="0" stop-color="#bdaa99"></stop><stop offset="1" stop-color="#d3c9c0"></stop></linearGradient></defs><path fill="url(#AZ)" d="M669 319l2 2-4 6c-1 0-1 1-2 2h0l-4 6h-1v-1h-1-1c5-4 7-10 11-15z"></path><path d="M660 235c1-2 3-3 5-4 1 0 2 0 3-1h1l1 2h0l5 5c1 4 2 10 0 14l-1 1c-1 1-1 4-2 6-1-3 0-7 0-10v-10h0c-1-2 0-1-1-2 0-1-1-1-1-2-2 1-2 1-3 2v2h0c-1-1-1-2-1-4h-1v1c-1 1-1 1-2 1-2 11-2 22-2 32-1 3-1 6-1 9l-1-1v3l-1-1c-1 3-1 7-3 9 0-3 1-6 1-9l1-20-1-18h1c1-1 1-3 1-4 1-1 1-1 2-1z" class="G"></path><path d="M660 235c1-2 3-3 5-4 1 0 2 0 3-1h1l1 2c-2 0-3 0-5 1-1 1-3 1-4 2-1 2-1 5-2 7 0 5 1 11 1 17-1 3-1 6-1 9-1 2-1 4-2 7v4l-1-1 1-20-1-18h1c1-1 1-3 1-4 1-1 1-1 2-1z" class="g"></path><path d="M656 240h1c1-1 1-3 1-4 1-1 1-1 2-1-1 4-2 8-2 13 0 3 0 7-1 10l-1-18z" class="p"></path><path d="M453 615c0-1 2-2 2-2 1 0 1 1 2 1 1-1 4-2 6-3-3 3-7 6-8 9 1 0 3-1 4-2h1 2c-5 4-11 10-13 16-9 17-5 38 0 56l8 21c4 7 7 16 15 18 5 2 10 5 13 10h1c1 2 1 3 1 4v1c1 3 1 6 0 9-1 2-1 3-3 5l1-1v-2c-2 0-2 1-3 2h-1c-1 0-1 1-2 2l-1 1v-1h-5 0c-2-1-2-1-4-3-5-5-6-9-8-16l-3-9-8-26c-1-3-2-7-3-10l-1-1c0-4-2-8-3-13l-5-23v-3h0c-1-5-2-10-2-16 1-6 2-12 6-18 2-3 4-7 8-7l3 1z" class="o"></path><path d="M450 614l3 1c-2 1-4 3-6 5-1 0-2 2-3 3 1-4 3-5 6-9h0z" class="Z"></path><path d="M441 654c-1-7 2-15 6-22-1 4-2 8-3 11-1 4-1 8-3 11zm-5-15c1-6 2-12 6-18 2-3 4-7 8-7h0c-3 4-5 5-6 9-2 3-3 5-4 9-1 2-1 4-1 6l1 1c-1 4-1 8-2 12v4h0c-1-5-2-10-2-16z" class="W"></path><path d="M455 620c1 0 3-1 4-2h1 2c-5 4-11 10-13 16-9 17-5 38 0 56l8 21h-1c-4-8-7-17-10-26v-3c-1-2-1-6-2-8v-2c0-1-1-3-1-4v-3c-1-4-1-7-2-11 2-3 2-7 3-11 1-3 2-7 3-11 2-4 5-8 8-12z" class="G"></path><path d="M457 711c4 7 7 16 15 18 5 2 10 5 13 10h1c1 2 1 3 1 4v1c1 3 1 6 0 9-1 2-1 3-3 5l1-1v-2c-2 0-2 1-3 2h-1c-1 0-1 1-2 2l-1 1v-1h-5 0c-2-1-2-1-4-3l1 1c1 0 2 0 3 1h1 2 2 1c1-1 2-2 3-4 2-3 2-6 2-9v-1c-1-3-2-5-3-7-3-3-7-5-12-5-2 2-4 3-5 5l-1 1-1-2v-2c0-2 4-4 5-5-4-6-8-11-11-18h1zm63 65l2 1c2 4 4 9 5 15 3 9 4 19 5 28v16 4c-1 7-2 13-4 20-1-3 0-3 0-6l2-7-2 1-1 4-1 1c-1 0-1 1-1 2h0c-1 1-1 1-1 2h-1l-3 2v2h-1v-1-1c-3 0-3 2-5 3-1 2-1 3-1 4s-1 1-1 2c-1-2-1-4 0-7 1-4 2-10 1-14h-1c0 2-2 4-4 5h-2v1h-4-1-1c0 1 0 1 1 2 0 0 1 1 1 2-1 0-2 0-3-1v1c-1-1-1-2-2-2l1-5v-2c-1-4-3-12-2-16 0-1 1-2 2-2l1-3c2-2 4-5 4-7 3-9-1-19 1-28h0c0-2 1-4 1-6 1 0 0-2 0-2h-1v-1h4 0l2 2-3 6c2 0 2-4 4-6v1 1h1v-2l2-4h1c1 0 1 0 2-1l3 3c1-3 1-4 0-6v-1z" class="W"></path><path d="M509 816c0 4-1 7 0 11l-2 4c-1-1-2-1-3-2v-1c3-3 3-8 5-12z" class="L"></path><path d="M504 829c1 1 2 1 3 2 5 4 10 10 13 16 1 2 1 2 1 3-4-3-6-8-9-11-2-2-3-4-6-5v-1c0-1-1-2-2-4z" class="J"></path><path d="M529 817c0 1 1 2 2 3 1 2 0 7 0 9 1 1 0 1 0 2 0 0 1 1 0 1 0 3 1 7 0 10l-1 5-2 1c1-3 1-6 1-9v-22z" class="g"></path><path d="M527 792c3 9 4 19 5 28v16 4c-1 7-2 13-4 20-1-3 0-3 0-6l2-7 1-5c1-3 0-7 0-10 1 0 0-1 0-1 0-1 1-1 0-2 0-2 1-7 0-9-1-1-2-2-2-3-1-6-2-12-4-19h1l-1-1c0-2 0-2 1-3 0 1 0 2 1 3v-1-4z" class="k"></path><path d="M520 776l2 1c2 4 4 9 5 15v4 1c-1-1-1-2-1-3-1 1-1 1-1 3l1 1h-1c0-1-1-3-2-4-1-2-4-5-6-6-2 0-3-2-5-3l2-4h1c1 0 1 0 2-1l3 3c1-3 1-4 0-6v-1z" class="AT"></path><path d="M512 785l2-4h1c1 0 1 0 2-1l3 3c1 1 1 1 1 2-1 0-2-1-2-1h-2l1 1v1c2 1 3 2 4 4 1 1 1 2 1 4-1-2-4-5-6-6-2 0-3-2-5-3z" class="k"></path><path d="M512 847c-1-1-2-5-2-6s-1-2-1-4c1 1 1 2 3 2 3 3 5 8 9 11h0c0 2 0 3 1 4v2c0 1-2 1-2 3v2h-1v-1-1c-3 0-3 2-5 3-1 2-1 3-1 4s-1 1-1 2c-1-2-1-4 0-7 1-4 2-10 1-14h-1z" class="n"></path><defs><linearGradient id="Aa" x1="496.401" y1="819.642" x2="510.362" y2="811.298" xlink:href="#B"><stop offset="0" stop-color="#a49183"></stop><stop offset="1" stop-color="#c4b4ab"></stop></linearGradient></defs><path fill="url(#Aa)" d="M508 783h0l2 2-3 6c-2 5 0 10 0 14 1 4 2 7 2 11-2 4-2 9-5 12l-5-1c2-2 4-5 4-7 3-9-1-19 1-28h0c0-2 1-4 1-6 1 0 0-2 0-2h-1v-1h4z"></path><path d="M508 783h0l2 2-3 6c-2 5 0 10 0 14h0c-2-3-2-5-2-8 0-2 0-4-1-5 0-2 1-4 1-6 1 0 0-2 0-2h-1v-1h4zm-9 44l5 1v1c1 2 2 3 2 4v1c3 1 4 3 6 5-2 0-2-1-3-2 0 2 1 3 1 4s1 5 2 6c0 2-2 4-4 5h-2v1h-4-1-1c0 1 0 1 1 2 0 0 1 1 1 2-1 0-2 0-3-1v1c-1-1-1-2-2-2l1-5v-2c-1-4-3-12-2-16 0-1 1-2 2-2l1-3z" class="g"></path><path d="M499 827l5 1v1c1 2 2 3 2 4v1l1 3c-1 0-2-1-3-2-1 0 0 0-1 1h-1l-1-1c-1 0-1 1-2 2l-1 1v5c1 2 1 3 1 5h-1c-1-4-3-12-2-16 0-1 1-2 2-2l1-3z" class="p"></path><path d="M498 830c2 0 3 0 5 1h-1c-1 1-2 1-3 2 0 0-1 0-2 1h-1v-2c0-1 1-2 2-2z" class="AI"></path><path d="M499 827l5 1v1c1 2 2 3 2 4l-3-2c-2-1-3-1-5-1l1-3z" class="d"></path><path d="M502 836h1c1 2 2 3 4 5 1-1 0-1 1-1 2 3 2 5 3 8-1 1-2 2-4 3h0c-2 1-4 1-6 1 0-2-1-4 0-5s0-3 0-4v-1-3h0c1-1 1-2 1-3z" class="n"></path><path d="M464 650c1-1 2-1 3-1 3 2 3 7 7 8v-3l4 1 2 1c12 13 19 34 22 52v5 1c-2 5-4 7-7 12-3 3-5 6-7 10l-4-4s-1-2-2-2c-2-2-4-4-5-6-8-9-13-19-17-30h1c-3-10-6-22-4-32 1-5 3-7 7-10v-2z" class="J"></path><path d="M464 650c1-1 2-1 3-1 3 2 3 7 7 8v-3l4 1 2 1c12 13 19 34 22 52v5 1c-2 5-4 7-7 12-3 3-5 6-7 10l-4-4s-1-2-2-2c-2-2-4-4-5-6-8-9-13-19-17-30h1c4 12 10 24 20 33 1 2 4 4 6 5 1 0 2-1 2-2l2-2c1-2-1 0 1-2l1-1 1-1c0-1 1-1 2-2v-1h1c0-3 0-3-2-5 0-1 0-1 1-3h2v-3c1-1 1-1 1-2v-1-4l-1-1v-3c-1-1-1-2-1-2v-1h-1c-1 0-1 0-1-2 0 0 0-1-1-2v-1-1l-1-2-1-3-3-6c0-1-1-2-2-3v-1l-6-9c-2-2-3-4-5-6l3 8c0 1-1 1-1 2-2-4-4-8-6-11 0-1-1-1-1-2l-3-3c-1-2-2-2-4-2v-2z" class="d"></path><path d="M502 708v5 1c-2 5-4 7-7 12-3 3-5 6-7 10l-4-4h1c1 0 1 1 3 1 3-4 8-9 10-14-1-2 0-4 1-5 1-2 1-4 2-7v1h1z" class="L"></path><path d="M474 654l4 1 2 1c12 13 19 34 22 52h-1v-1c-1-5-2-10-4-15l-1-1-1-1-11-23-1-1c0-1-1-2-2-3h-1c-2-3-4-3-6-6v-3z" class="AI"></path><path d="M474 657h3c3 1 7 7 9 10 4 4 6 11 8 17 1 3 3 5 3 8l-1-1-1-1-11-23-1-1c0-1-1-2-2-3h-1c-2-3-4-3-6-6z" class="G"></path><path d="M686 139c2 0 4 0 6-1 1 0 3 1 4 1 2 0 4 0 6 2h1c1-1 2 0 4 0 1 1 3 2 4 3h0c6 3 10 10 13 16l-3-2c0-2-1-2-2-3-1 6-4 12-5 19-1 4-2 11-1 16h-1-1-3c-4 2-7 5-11 6-3 1-4 2-7 3h-1c-5 2-10 2-15 1h-1c0-1-3-2-4-3 0-1-4-5-5-7 0-2-1-4-1-6-1-3-1-6-1-9l-1-2c1 0 1-1 1-1 1-3 2-6 4-8 1-1 1-1 2-1 1-2 2-3 4-4-1-2-1-3 0-4v-1l1-1-2-5-1-4c1 1 2 2 4 2h0l1-2h3l5-2 3-3z" class="K"></path><path d="M672 154l1 1v2h1c2-1 2-1 4-1 2 1 4 1 5 2l1 1c-2 0-4-1-6 0h-1c-2 1-5 4-7 6l-1 1c-2 1-3 4-4 6v1c-1 3-1 6-1 9h0c-1-2-1-5-2-7l-1-2c1 0 1-1 1-1 1-3 2-6 4-8 1-1 1-1 2-1 1-2 2-3 4-4-1-2-1-3 0-4v-1z" class="h"></path><path d="M696 170h0c1 0 1 1 1 2 1 6 3 9 0 15h1c1-3 2-5 1-8 0-1 0-2-1-3v-6c1 2 1 4 2 6v1 4c0 1 0 4-1 5v1h0c-1 1-1 1-1 2-1 1-1 1-1 2-1 0-1 1-2 2h-1c0 1-1 3 0 4h1l2-1c-3 1-4 2-7 3h-1c-5 2-10 2-15 1h-1c0-1-3-2-4-3 0-1-4-5-5-7 0-2-1-4-1-6-1-3-1-6-1-9 1 2 1 5 2 7h0c1 0 1 0 1 1 2 4 3 6 7 9 1 1 3 2 4 3 3 1 8 1 10 0h1c4-2 7-5 9-8 3-6 1-11 0-17z" class="L"></path><path d="M678 144h3c-1 1-3 1-4 1 1 3 5 4 7 5 5 2 9 7 11 12 2 4 5 10 5 15v-1c-1-2-1-4-2-6v6c1 1 1 2 1 3 1 3 0 5-1 8h-1c3-6 1-9 0-15 0-1 0-2-1-2h0c-2-2-4-5-5-8-2 0-3-1-5-2-1 0-1 0-2-1l-1-1c-1-1-3-1-5-2-2 0-2 0-4 1h-1v-2l-1-1 1-1-2-5-1-4c1 1 2 2 4 2h0l1-2h3z" class="d"></path><path d="M678 144h3c-1 1-3 1-4 1 1 3 5 4 7 5h-1-2-5l-1 1v1 1l-1 1h2 3l1 1c4 1 9 4 11 7-2 0-3-1-5-2-1 0-1 0-2-1l-1-1c-1-1-3-1-5-2-2 0-2 0-4 1h-1v-2l-1-1 1-1-2-5-1-4c1 1 2 2 4 2h0l1-2h3z" class="G"></path><path d="M670 144c1 1 2 2 4 2 2 2 2 2 5 2v1h-4v1c-1 0 0 0-1 1 0 1 0 1-1 2l-2-5-1-4z" class="p"></path><path d="M686 139c2 0 4 0 6-1 1 0 3 1 4 1 2 0 4 0 6 2h1c1-1 2 0 4 0 1 1 3 2 4 3h0c6 3 10 10 13 16l-3-2c0-2-1-2-2-3-1 6-4 12-5 19-1 4-2 11-1 16h-1-1-3c-4 2-7 5-11 6l-2 1h-1c-1-1 0-3 0-4h1c1-1 1-2 2-2 0-1 0-1 1-2 0-1 0-1 1-2h0v-1c1-1 1-4 1-5v-4c0-5-3-11-5-15-2-5-6-10-11-12-2-1-6-2-7-5 1 0 3 0 4-1h-3l5-2 3-3z" class="J"></path><path d="M701 150h1c5 8 7 14 6 22v1l-1 6c0-7 1-14-1-20-2-3-4-6-5-9z" class="i"></path><path d="M686 139c2 0 4 0 6-1 1 0 3 1 4 1l-1 2h-1l-2-1-1 2 1 1h1c2 0 3 1 4 2h0c-5-2-11-2-16-1h-3l5-2 3-3z" class="b"></path><path d="M707 179l1-6v-1l1 8c-1 2 0 8-1 10-4 2-7 5-11 6l-2 1h-1c-1-1 0-3 0-4h1c1-1 1-2 2-2 0-1 0-1 1-2 0-1 0-1 1-2h0v-1c1-1 1-4 1-5v-4 1c1 3 1 7-1 10-1 2-2 3-2 6 1 0 2 0 2-1 2-2 4-4 5-6l2-2 1-1v-5z" class="d"></path><defs><linearGradient id="Ab" x1="686.701" y1="153.837" x2="721.467" y2="175.815" xlink:href="#B"><stop offset="0" stop-color="#d7d1cf"></stop><stop offset="1" stop-color="#f8f8f5"></stop></linearGradient></defs><path fill="url(#Ab)" d="M696 139c2 0 4 0 6 2h1c1-1 2 0 4 0 1 1 3 2 4 3h0c6 3 10 10 13 16l-3-2c0-2-1-2-2-3-1 6-4 12-5 19-1 4-2 11-1 16h-1-1-3c1-2 0-8 1-10l-1-8c1-8-1-14-6-22h-1c-1-2-3-3-4-5h0c-1-1-2-2-4-2h-1l-1-1 1-2 2 1h1l1-2z"></path><path d="M709 180c1 3 1 5 1 8 1 1 1 1 1 2h-3c1-2 0-8 1-10z" class="K"></path><path d="M709 146h-1l-2-2h0c1 0 1 0 2 1h1c1 0 1 1 2 1l5 4c1 0 1 1 2 2h0c-2 2-2 5-2 8-1-3-1-6-2-8s-4-4-5-6z" class="W"></path><path d="M696 139c2 0 4 0 6 2h1c1-1 2 0 4 0 1 1 3 2 4 3h0c6 3 10 10 13 16l-3-2c0-2-1-2-2-3l-1-3h0c-1-1-1-2-2-2l-5-4c-1 0-1-1-2-1h-1c-1-1-1-1-2-1h0l2 2h1c-3-1-10-2-12-4v-1c-2 0-2 0-4 2h-1l-1-1 1-2 2 1h1l1-2z" class="G"></path><path d="M329 218l2 2c1 1-1 1 1 1 2-1 2-1 4-1 2 1 3 1 4 3v1h-1c2 2 5 3 6 5l3 3c1 2 3 4 3 7 5 10 8 21 11 32 2 4 4 9 3 14h-1c-1 0-1 1-2 1-2 2-4 3-7 3-4 0-6-2-8-4 0 2 1 4 2 6h0c0 2 0 4-1 6h0-1-3c-2 1-5 2-7 2v-1l-1-1c-2-1-3-4-5-6v1h-1c-1-1-2-3-3-5-2-2-4-4-7-6-1-1-3-3-5-3l-2 2c-1-2-1-2-1-4l1-1c1 1 2 1 3 1v-1c-2-2-3-3-5-4l-1-1-3-3c-5-8 3-14 4-21 1 0 1-1 1-2 1 0 1-1 2-2v1h0v1 3c1-6 4-12 7-17 2-2 4-4 5-6l3-6z" class="AP"></path><path d="M314 259c5 2 13 6 17 10l-1 1c-1-1-2-2-4-3-3-2-8-2-10-4v-1c-1-1-2-1-2-3z" class="k"></path><path d="M312 268c1-1 1-2 2-2s1 0 2-1l-2-2h2c2 2 7 2 10 4-3 0-8 0-11 1h-1v1h-1l-1-1z" class="b"></path><path d="M310 261c2-2 2-2 4-2 0 2 1 2 2 3v1h-2l2 2c-1 1-1 1-2 1s-1 1-2 2l-1-1-1-2c-1-1 0-3 0-4z" class="p"></path><path d="M310 261h1c0 2 1 2 2 3v1l-2 2-1-2c-1-1 0-3 0-4z" class="k"></path><path d="M333 227c0-1 1-3 2-4 1 0 3 1 4 1 2 2 5 3 6 5l3 3c1 2 3 4 3 7l-2-3c-1 0-1 0-2-1 0-1-1-2-2-3-2-1-1-2-2-3-1-2-5-4-6-4-2 1 0 3-1 5h-1v-2l-1-1c-1 2-2 4-2 6 1 0 1 1 1 1 1 2 2 3 3 3l1 1h-1c-2-1-4-2-5-4h1l-2-2c-3 1-5 3-7 4l-1-1c1-3 4-5 6-7h3l1 1h0l1-2z" class="Z"></path><path d="M314 269h1l6 7h1c2 1 3 2 4 4h0 1c0 1 1 2 3 2l2 1 1-1c0-1 0-2-1-3 1-1 1-3 1-4 0-2-1-3-3-5l1-1 4 4c1 1 3 2 4 3l1 3c-1 2 0 3-1 5l-1-1-1 1c-1 1-2 2-4 2-6 1-14-12-19-16l-1-1h1z" class="n"></path><path d="M335 273c1 1 3 2 4 3l1 3c-1 2 0 3-1 5l-1-1-1 1c1-4-1-7-2-10v-1zm-2 2c1 2 2 4 2 6-1 1-1 2-2 3-2 1-3 1-4 0a30.44 30.44 0 0 1-8-8h1c2 1 3 2 4 4h0 1c0 1 1 2 3 2l2 1 1-1c0-1 0-2-1-3 1-1 1-3 1-4z" class="K"></path><path d="M329 218l2 2c1 1-1 1 1 1 2-1 2-1 4-1 2 1 3 1 4 3v1h-1c-1 0-3-1-4-1-1 1-2 3-2 4l-1 2h0l-1-1h-3c-2 2-5 4-6 7l-1 1v-1c-2 1-4 5-5 7v1c0 2-1 5-1 7 0 1 1 2 1 4-2-3-2-4-2-7 1-6 4-12 7-17 2-2 4-4 5-6l3-6z" class="AI"></path><path d="M328 228c1-1 1-2 2-4l1-1c2 1 0 3 2 4l-1 2h0l-1-1h-3z" class="W"></path><path d="M326 267c2 1 3 2 4 3 2 2 3 3 3 5 0 1 0 3-1 4 1 1 1 2 1 3l-1 1-2-1c-2 0-3-1-3-2h-1 0c-1-2-2-3-4-4h-1l-6-7h-1v-1h1c3-1 8-1 11-1z" class="J"></path><path d="M326 267c2 1 3 2 4 3 2 2 3 3 3 5 0 1 0 3-1 4-1-3-3-7-6-8h-1c-3-2-6-2-10-2h-1v-1h1c3-1 8-1 11-1z" class="i"></path><path d="M322 235l1 1c2-1 4-3 7-4l2 2h-1l-4 1c-2 1-3 3-5 4 0 1 0 2-1 3l1 1c0 2 0 5-1 6 1 5 3 9 6 13 2 1 3 3 5 4 3 3 9 6 10 9-2-1-3-3-4-4-3-2-6-3-8-5-2-1-3-3-5-4-3-2-6-4-8-7l-1-1c0-2-1-3-1-4 0-2 1-5 1-7v-1c1-2 3-6 5-7v1l1-1z" class="b"></path><path d="M316 243c1 1 1 1 1 2-1 1-1 3-1 4h1l2-6v7c-1 2-2 3-2 5l-1-1c0-2-1-3-1-4 0-2 1-5 1-7z" class="Z"></path><path d="M319 250c0 2 1 5 2 7s3 3 4 5h0c-3-2-6-4-8-7 0-2 1-3 2-5z" class="o"></path><path d="M339 276l8 9c0 2 1 4 2 6h0c0 2 0 4-1 6h0-1-3c-2 1-5 2-7 2v-1l-1-1c-2-1-3-4-5-6v1h-1c-1-1-2-3-3-5 1 1 2 1 3 1h1c4 0 5-1 8-4 1-2 0-3 1-5l-1-3z" class="i"></path><path d="M340 279c2 3 5 5 6 8-1-1-3-2-4-3l-1-1-1 1v1c-1 1-1 2-2 3s-2 1-3 1h-3l-1-1c4 0 5-1 8-4 1-2 0-3 1-5z" class="G"></path><path d="M342 284c1 1 3 2 4 3l1 3-3 1s-1 0-1 1c-2 1-4 2-6 2-1 0-1-1-2-1h0l-2-1v-1c1-1 2-1 3-1s1 0 2-1c1 0 2-1 3-1s1 0 2-1c0-1-1-2-1-3z" class="Z"></path><path d="M339 276l8 9c0 2 1 4 2 6h0c0 2 0 4-1 6h0-1-3c-2 1-5 2-7 2v-1l-1-1-1-2c-1 0 0-1-1-1l1-1c1 0 1 1 2 1 2 0 4-1 6-2 0-1 1-1 1-1l3-1-1-3c-1-3-4-5-6-8l-1-3z" class="O"></path><path d="M343 292c0-1 1-1 1-1l3-1c1 1 1 3 1 5h-2v-1c-1-2-2-2-3-2z" class="i"></path><path d="M343 292c1 0 2 0 3 2v1l-9 3-1-1-1-2c-1 0 0-1-1-1l1-1c1 0 1 1 2 1 2 0 4-1 6-2z" class="J"></path><path d="M369 357l5 1c1 0 2 1 4 2 1 0 2 0 4 1l4 4h2c1 1 1 1 2 1 2 3 2 5 3 8h0v2 1 1 1l-1 1c0 1 0 2 1 3h0c0-1 0-1 1-2v-3-3c1-1 1-2 1-4-1-2-1-5-2-7v-1c2 6 4 11 5 17 1 3 2 9 3 11 1 3 3 6 3 10 4 7 8 17 7 25 1 3-1 5-2 7-1 3-4 4-7 5-6-3-12-7-17-12h0l-8-6c-1-2-3-3-4-4v-1-1l-4-4c0-2-1-2-2-3-2-3-4-6-6-8-5-8-11-15-13-24l-1-6v-3c1-2 3-4 4-5 4-2 8-3 12-4h6z" class="K"></path><path d="M374 392h4v1c-1 0-1 1-2 1l-2-2z" class="AP"></path><path d="M370 401c1-2 0-3 2-5l2 2-1 1v1c0 2-1 3-1 5v1h-1c0-2-1-3-1-5z" class="W"></path><path d="M397 413l2 2v-1h0c-1-1-1-2-1-2 1 0 1 1 2 2 2 2 3 4 5 7h-1v3c-2-4-4-7-7-11zm-23-15c1 2 5 8 6 8l1 1 7 8c-2 0-5-4-8-6h0l-1-1c-2-1-3-3-4-5 0-1-1-2-1-2l-1-1v-1l1-1z" class="d"></path><path d="M370 401c0-3 0-5 1-8-1-1-3-2-3-3 1 1 6 3 6 4 1 0 2 2 2 3 1 3 3 6 4 9-1 0-5-6-6-8l-2-2c-2 2-1 3-2 5z" class="O"></path><path d="M380 400h-1c-1-2-1-3-1-6 2-1 5-2 7-2 5-1 9 0 13 2-2 1-5 0-8 0v-1l-1 1h-3 0c-2 1-5 2-5 3h0c-1 1-1 2-1 3z" class="k"></path><path d="M393 363c2 6 4 11 5 17 1 3 2 9 3 11v1l-1 1c-2-1-3-2-4-3-2 0-4-1-5-1h-1c-2 0-3 1-5 1h0l5-3c2-1 3-3 4-5 1-3 1-7 1-11-1-2-1-5-2-7v-1z" class="AO"></path><path d="M394 382l1 1 1-1c0 2-1 3-1 4-2 1-3 1-5 1 2-1 3-3 4-5z" class="O"></path><path d="M396 390c0-3 1-6 2-9h0c1 4 2 7 3 11l-1 1c-2-1-3-2-4-3z" class="AP"></path><path d="M378 360c1 0 2 0 4 1l4 4h2c1 1 1 1 2 1 2 3 2 5 3 8h0c-1 5-2 9-7 13-1 1-2 1-3 2h-3c-1 1-1 1-2 1-1 1-2 1-3 1 1-1 1-1 2-1s1 0 3-1h0l5-4 2-4v-1c1-5-2-12-5-16l-4-4z" class="g"></path><path d="M382 364c3 1 4 2 5 4l1 3h0c1 3 1 7 2 10-2 2-3 3-5 4l2-4v-1c1-5-2-12-5-16z" class="d"></path><path d="M381 407h1c0-2 0-3-1-4l1-1 2 2v-1c2 0 3 1 5 2 2 2 5 7 8 8 3 4 5 7 7 11h0c1 1 1 3 1 4l-1 1-1-1-1-1c0-1-2-2-3-2l-10-10h-1l-7-8z" class="AP"></path><path d="M380 400c0-1 0-2 1-3h0c0-1 3-2 5-3h0 3l1-1v1c3 0 6 1 8 0 3 2 4 4 6 7 4 7 8 17 7 25 1 3-1 5-2 7-1 3-4 4-7 5-6-3-12-7-17-12h0c6 3 10 8 15 10 2 1 4 1 6 0l2-2v-1h1v-1c0-1 0-1 1-2-1-2 0-5-1-7l-1-1v-1c0-1-1-3-2-5s-4-5-4-7c0-1 0-1-1-2v-1h1 0l-1-1c-1-1-3-4-5-5l-1 1c1 1 2 3 3 5v1c-2-5-5-6-9-9-2-1-3-1-6-1 0 1-1 1-1 3h-1c-1-1-1-1-1 0z" class="d"></path><path d="M380 400c0-1 0-2 1-3h0c0-1 3-2 5-3h0 3l1-1v1c3 0 6 1 8 0 3 2 4 4 6 7 4 7 8 17 7 25h0c-1-2-1-5-2-8l-2-4c-1-4-6-15-10-16-1-1 0-1-2-1v-1c-4 0-8-1-12 1 0 1-1 1-1 3h-1c-1-1-1-1-1 0z" class="AT"></path><path d="M483 596c2 1 2 1 3 3 3 2 6 5 11 6l1 1c2 0 4 1 6 1h9l-1 1c0 1 0 1-1 2l2 1c0 1 1 1 3 1 2 1 4-1 6-2h1l-3 3 1 1 1 1h-1c-1 2 1 5 1 8 0 4 2 7 4 11 1 2 1 4 3 6h1 0c2 1 4 8 5 10 0 1 1 2 1 2l1 1c1 1 1 2 2 3h1c1 0 1 0 1 1 1 2 2 4 2 6 0 1 1 2 2 3 0 5 2 10 1 14l1 4v-1h1l1-3c1 1 1 1 1 3l-3 10-3 12c-1 2-1 3-3 5l-10 31h0l-22-67-4-20c-2-2-4-4-5-6-2-1-2-1-3-1-1 1-1 0-2 1 0-1-1-2-1-3 1-2 2-4 3-5-1-3-1-6 0-8 1 0 2 0 3-1-1-2-5-6-7-8l-1-1c-4-8-6-18-9-26z" class="AQ"></path><path d="M497 640l3 8c-2-1-2-1-3-1-1 1-1 0-2 1 0-1-1-2-1-3 1-2 2-4 3-5z" class="K"></path><path d="M535 663h1l1 3c-3 2-5 4-7 6v-1l4-4h-2l-5 6h-1l6-6c1-1 3-2 3-4z" class="w"></path><path d="M533 683l1 1 1 3c0 2-1 6-1 8 0 5-1 11-2 16l-1 3v1l-1 3c-1 2-2 4-2 6v1l-1 1h0c-1-2 0-4 1-6 5-12 5-24 5-37z" class="y"></path><path d="M522 637l1 1c-1 2-2 5-2 6v2c-2 4-5 7-6 11-2 6-2 12-2 19-1-4-1-7-2-11 1-1 1-1 1-2h0c0-1 0-2 1-3 0-1 0-3 1-4l1-2v-1l1-2c1-1 1-2 2-3l2-2h-1l1-1c-1-1-1-2-1-4l3-4z" class="AE"></path><path d="M546 680l1 4v-1h1l1-3c1 1 1 1 1 3l-3 10-3 12c-1 2-1 3-3 5l4-17c-1-2-1-4-1-6 2-2 2-4 2-7z" class="g"></path><path d="M546 680l1 4v-1h1l-3 10c-1-2-1-4-1-6 2-2 2-4 2-7z" class="AE"></path><path d="M540 656c1 0 1 0 1 1 1 2 2 4 2 6 0 1 1 2 2 3 0 5 2 10 1 14 0 3 0 5-2 7 0-9-1-19-5-27v-4h1z" class="w"></path><path d="M527 685c1 1 1 2 1 2l1 1c1 4 1 10 0 14-1 0-1 1-1 2v1 2l-1-1-3-6c0-1-1-1-1-3h0c0-3 1-5 2-7 0-2 1-3 2-5z" class="y"></path><path d="M502 611h3l5 2c4 2 6 2 10 1v-1l1 1 1 1h-1c-1 2 1 5 1 8 0 4 2 7 4 11 1 2 1 4 3 6h0c-1 1-2 1-3 1-2 2-4 3-5 5v-2c0-1 1-4 2-6l-1-1c-4-7-7-14-13-19-2-2-3-1-6-1 0-1 1-2 1-3h1l1-1h-2l-2-2z" class="AU"></path><path d="M483 596c2 1 2 1 3 3 3 2 6 5 11 6l1 1c2 0 4 1 6 1h9l-1 1c0 1 0 1-1 2l2 1c0 1 1 1 3 1 2 1 4-1 6-2h1l-3 3v1c-4 1-6 1-10-1l-5-2h-3l2 2h2l-1 1h-1c0 1-1 2-1 3s-1 1-2 1c-2 1-4 3-7 4l-1 1-1-1c-4-8-6-18-9-26z" class="Aa"></path><path d="M504 613h2l-1 1h-1c0 1-1 2-1 3s-1 1-2 1c-2 1-4 3-7 4l-1 1-1-1c2-1 3-3 4-3l1-1 2-1v-1c1-1 4-2 5-3z" class="y"></path><path d="M486 599c3 2 6 5 11 6l1 1c2 0 4 1 6 1h9l-1 1c0 1 0 1-1 2l2 1c0 1 1 1 3 1 2 1 4-1 6-2h1l-3 3v1c-4 1-6 1-10-1l-5-2h-3c-3-1-8-3-10-6l-1-1c-2-1-4-3-5-5z" class="Ah"></path><path d="M492 605c2 0 3 1 5 1l8 5h-3c-3-1-8-3-10-6z" class="Ac"></path><defs><linearGradient id="Ac" x1="405.867" y1="368.519" x2="458.094" y2="366.083" xlink:href="#B"><stop offset="0" stop-color="#170000"></stop><stop offset="1" stop-color="#590707"></stop></linearGradient></defs><path fill="url(#Ac)" d="M418 335l2-2c-2-2-2-4-3-7l1-1 3 5v1l6 9 3 3 1-1c1 0 1 0 2 1 0 1 0 2 1 2h2 0l1 1c1 2 1 4 2 5 2 2 3 4 5 6h0c2 4 6 8 7 11l1 1 2-2v1c5 4 9 9 14 13l1 1c2 2 4 3 6 5l4-1c2 2 4 4 7 4l1 1h6 1l1 1-2 1v2l1 1h0-2l-2 2c-1 1-2 1-3 1l2 2-4 2-1 1-7 4h-1c-1-2-1-2-2-3h-1l2 3c0 1-1 2-2 2l-12 8-6 3c-1 1-2 1-2 2h0 1v3h0c-1 0-2 0-2 1v1h-1-1-2c-2-1-2-1-3 0h0-7-1l-1-1h-1l-1 1h0c-1-2-3-4-4-5h-2l-1-3-1-4c1 0 2 1 4 1h1 1 2 1-1l1-2c1 0 1 0 3 1 1 1 1 1 2 1l-1-1-4-4v1c-2-1-3-2-5-3l-1-2c-1-2-3-4-4-6-2-5-4-9-6-13l-3-9h0c-2-5-3-11-4-16l-4-23c-1-4-1-7-3-10 0 0-2-2-2-3h2l1-1 3 1s1 0 1 1h1 2l5 6z"></path><path d="M444 380c2 0 3 0 5 1h2l3 3-1 2-6-4h0l-3-2z" class="AB"></path><path d="M427 374c2-1 5-1 7-1l8 5h-6l-1 1c-2 0-6-4-8-5z" class="C"></path><path d="M435 379l1-1h6c0 1 1 2 2 2l3 2h0l-1 1 2 2v1h-1l-12-7z" class="B"></path><path d="M421 370c-2-1-3-2-4-3v-1h1 1l2-1 3 3c0 1 1 1 2 1s1 1 2 1h3v1l3 2h0c-2 0-5 0-7 1h0l-6-4z" class="m"></path><path d="M421 370c1 0 2-1 3 0s2 1 3 2c1 0 1 1 2 1h1l1-1h1l-1-1h0l3 2h0c-2 0-5 0-7 1h0l-6-4z" class="D"></path><path d="M452 375l2 2c3 5 8 10 13 13l2 2v3c-2-1-4-3-7-5 1 2 1 2 2 3-1 1-1 0-3 0-3-2-6-4-8-7l1-2 6 4c1 1 1 1 2 1-1-5-8-8-10-13v-1z" class="B"></path><path d="M447 382l6 4c2 3 5 5 8 7 2 2 5 4 6 6v2c1 1 2 2 4 2 0 1 1 2 2 2l2 3c0 1-1 2-2 2-3 0-6-4-8-6l-3-2-5-5c-3-1-6-3-9-5h1 2c0-1 0-1-1-2s-2-2-2-3l-1-1h1v-1l-2-2 1-1z" class="X"></path><path d="M454 391l9 6v2c1 2 4 2 4 4l-2 1-3-2-5-5 1-1c0-1-1-2-2-3l-2-2h0z" class="B"></path><path d="M448 386l6 5h0 0l2 2c1 1 2 2 2 3l-1 1c-3-1-6-3-9-5h1 2c0-1 0-1-1-2s-2-2-2-3l-1-1h1z" class="R"></path><path d="M463 397c2 1 3 2 4 4 1 1 2 2 4 2 0 1 1 2 2 2l2 3c0 1-1 2-2 2-3 0-6-4-8-6l2-1c0-2-3-2-4-4v-2z" class="z"></path><path d="M432 408c-3-4-5-9-6-13 0-1 1-1 1-2-1 0-2-2-2-3l1-1c0 1 0 2 1 2 0 0 0-1 1-1 0-1-1-1-1-2 2-1 1-3 2-5 1 1 2 1 3 1v-2c2 0 4 2 6 2s3 1 5 2l-1 1c-1-1-1-1-2-1l1 1c1 0 1 0 1 1 1 0 2 1 3 1 1 1 3 2 4 3h-1 0c-2-2-4-3-6-3-1 1-2 2-2 3s1 3 2 4l-2 1-1-2h-1l-1-1v1l-1 1v-1c-2 0-4-3-6-3l-1 1c1 1 1 3 2 4 0 1 1 2 1 3-1 1-1 3 0 4v4h0z" class="Q"></path><path d="M437 395h-1l-1-2c-1-1-2-1-3-2 1-1 2-3 2-4 1 0 1 0 2-1v-1l1 1c2 0 4 1 5 3-1 1-2 2-2 3s1 3 2 4l-2 1-1-2h-1l-1-1v1z" class="B"></path><path d="M418 335l2-2c-2-2-2-4-3-7l1-1 3 5v1l6 9c0 1 0 2-1 3l1 3c1 1 3 5 5 6 1 2 3 4 4 6h0c-3-1-8-4-10-7 0-1 0-1-1-2h-1c0-2 0-3-1-4-2-3-3-6-7-8v1c0 1 1 2 1 3s1 1 2 2c0 1 1 1 1 2s1 1 2 2h0v1c-1-1-3-2-4-3s-1-3-2-4c-1-2-2-3-3-5 0-1-1-4-1-5-1-1-5-2-6-3h-1l1-1 3 1s1 0 1 1h1 2l5 6z" class="S"></path><path d="M418 335l2-2c-2-2-2-4-3-7l1-1 3 5v1l6 9c0 1 0 2-1 3l1 3c-1-1-2-2-2-3-1-1-2-1-3-2v-1l-1-1c-1-2-1-2-3-4z" class="c"></path><path d="M421 331l6 9c0 1 0 2-1 3-1-1-2-3-3-5 0-3-2-3-2-7z" class="r"></path><path d="M427 340l3 3 1-1c1 0 1 0 2 1 0 1 0 2 1 2h2 0l1 1c1 2 1 4 2 5 2 2 3 4 5 6h0c2 4 6 8 7 11v2l2 3c2 2 4 4 4 6-1 0-1-1-2-2h-1l-2-2c-1-1-14-15-16-17-1-2-3-4-4-6-2-1-4-5-5-6l-1-3c1-1 1-2 1-3z" class="AF"></path><path d="M434 345h2 0c-1 2 0 3 1 5h0l3 4-2 2-3-5c1-2 0-4-1-6z" class="c"></path><path d="M431 342c1 0 1 0 2 1 0 1 0 2 1 2 1 2 2 4 1 6l-2-2s-3-5-3-6l1-1z" class="H"></path><path d="M440 354c1 2 3 4 4 6l1 2c2 2 3 3 4 6h0c-3-4-8-7-11-12l2-2z" class="T"></path><path d="M427 340l3 3c0 1 3 6 3 6-1 1-1 2-1 3-2-1-4-5-5-6l-1-3c1-1 1-2 1-3z" class="AS"></path><path d="M442 389c2 0 4 1 6 3h0c3 2 6 4 9 5l5 5 3 2c2 2 5 6 8 6l-12 8c-1-1-1-1-1-2l-5-5v-1c-2-1-5-4-6-5l-1-1c-3-1-3-2-4-4v-1-1-1l-1-1h-1c-1-1-2-3-2-4s1-2 2-3z" class="c"></path><path d="M444 400c1 0 4 2 5 2l1-2c2 1 3 2 4 3 2 0 3 1 4 2 3 2 5 4 8 6l1 2h0-1-1-2c-2-2-4-4-6-5-1 1-2 1-2 2-2-1-5-4-6-5l-1-1c-3-1-3-2-4-4z" class="AW"></path><path d="M444 400c1 0 4 2 5 2l1-2c2 1 3 2 4 3h-1l-1 1v1c-2 0-2-1-3 0l-1-1c-3-1-3-2-4-4z" class="z"></path><defs><linearGradient id="Ad" x1="444.049" y1="394.045" x2="455.505" y2="398.988" xlink:href="#B"><stop offset="0" stop-color="#9d0f0f"></stop><stop offset="1" stop-color="#c71918"></stop></linearGradient></defs><path fill="url(#Ad)" d="M442 389c2 0 4 1 6 3h0c3 2 6 4 9 5l5 5h0c-1-1-2-1-3-2h-2c0 1 0 1 1 2h1c-1 1-2 0-3 0l2 3c-1-1-2-2-4-2-1-1-2-2-4-3l-1 2c-1 0-4-2-5-2v-1-1-1l-1-1h-1c-1-1-2-3-2-4s1-2 2-3z"></path><path d="M442 389c2 0 4 1 6 3h-1-2-2c1 1 1 1 1 2 1 1 2 2 2 3v2c1 0 2 1 4 1l-1 2c-1 0-4-2-5-2v-1-1-1l-1-1h-1c-1-1-2-3-2-4s1-2 2-3z" class="N"></path><path d="M454 367v1c5 4 9 9 14 13l1 1c2 2 4 3 6 5l4-1c2 2 4 4 7 4l1 1h6 1l1 1-2 1v2l1 1h0-2l-2 2c-1 1-2 1-3 1l2 2-4 2-1 1-7 4h-1c-1-2-1-2-2-3h-1c-1 0-2-1-2-2-2 0-3-1-4-2v-2c-1-2-4-4-6-6 2 0 2 1 3 0-1-1-1-1-2-3 3 2 5 4 7 5v-3l-2-2c-5-3-10-8-13-13h1c1 1 1 2 2 2 0-2-2-4-4-6l-2-3v-2l1 1 2-2z" class="H"></path><path d="M469 388l5 4c-2 1-2 1-3 1l-2-1-2-2 2-2z" class="Af"></path><path d="M474 392c2 2 6 4 7 7v1h1l3 3-1 1c-2-1-4-3-6-5s-5-4-7-6c1 0 1 0 3-1z" class="Al"></path><path d="M479 386c2 2 4 4 7 4l1 1h6 1l1 1-2 1v2l1 1h0-2l-3-1c-3-1-5-2-7-3l-1-1-3-3c-1 0-2-1-3-1l4-1z" class="e"></path><path d="M487 391h6 1l1 1-2 1v2l1 1h0-2l-3-1c-3-1-5-2-7-3 2 0 4 0 5 1l3 1h1l-4-3z" class="T"></path><path d="M454 377h1c1 1 1 2 2 2 0-2-2-4-4-6l-2-3v-2l1 1c4 7 11 13 17 19l-2 2c-5-3-10-8-13-13z" class="r"></path><path d="M464 393c-1-1-1-1-2-3 3 2 5 4 7 5v-3l2 1c2 2 5 4 7 6s4 4 6 5l-7 4h-1c-1-2-1-2-2-3h-1c-1 0-2-1-2-2-2 0-3-1-4-2v-2c-1-2-4-4-6-6 2 0 2 1 3 0z" class="T"></path><path d="M464 393c4 2 7 5 10 8l-4-2-1 1 2 1h-1c-1 0-2-1-3-2-1-2-4-4-6-6 2 0 2 1 3 0z" class="z"></path><defs><linearGradient id="Ae" x1="429.12" y1="405.767" x2="456.18" y2="407.632" xlink:href="#B"><stop offset="0" stop-color="#5f0707"></stop><stop offset="1" stop-color="#bc191a"></stop></linearGradient></defs><path fill="url(#Ae)" d="M432 404c-1-1-1-3 0-4 0-1-1-2-1-3-1-1-1-3-2-4l1-1c2 0 4 3 6 3v1l1-1v-1l1 1h1l1 2 2-1h1l1 1v1 1 1c1 2 1 3 4 4l1 1c1 1 4 4 6 5v1l5 5c0 1 0 1 1 2l-6 3c-1 1-2 1-2 2h0 1v3h0c-1 0-2 0-2 1v1h-1-1-2c-2-1-2-1-3 0h0-7-1l-1-1h-1l-1 1h0c-1-2-3-4-4-5h-2l-1-3-1-4c1 0 2 1 4 1h1 1 2 1-1l1-2c1 0 1 0 3 1 1 1 1 1 2 1l-1-1-4-4v1c-2-1-3-2-5-3l-1-2h3 0v-4z"></path><path d="M435 407c1 2 2 3 2 4 1 1 1 1 1 2 1 1 1 1 1 3l-4-4h0v-5z" class="V"></path><path d="M442 418h1l1-1h1v-2-1c0 1 0 2 1 3 0 0 1 1 1 2v2c-2 0-4-2-5-3z" class="E"></path><path d="M432 404c0 1 2 3 3 3v5h0v1c-2-1-3-2-5-3l-1-2h3 0v-4z" class="Y"></path><path d="M432 404c0 1 2 3 3 3v5c-1-1-2-3-3-4v-4z" class="U"></path><path d="M453 423h0 1v3h0c-1 0-2 0-2 1v1h-1-1-2c-2-1-2-1-3 0h0-7v-1l4-1c0 1 0 1 1 1h1l6-3c2 0 2 0 3-1z" class="B"></path><path d="M455 421c-1 0-2 0-3-1l1-1c1 0 2 0 3-2 0-1 0-1-1-2-1-2-1-3-2-5 1 1 1 1 2 1l5 5c0 1 0 1 1 2l-6 3z" class="AW"></path><path d="M435 415c1 0 1 0 3 1 1 1 1 1 2 1s2 1 2 1c1 1 3 3 5 3 1 0 2 1 3 2h0c-1 1-2 1-2 1-1 0-2 1-3 1v-1l-1-1c-1-1-1-1-2-1l-1-1c-2-2-5-2-7-4h1-1l1-2z" class="I"></path><path d="M426 416c1 0 2 1 4 1h1 1 2c2 2 5 2 7 4l1 1c1 0 1 0 2 1l1 1v1c-1 0-3 1-3 1l-4 1v1h-1l-1-1h-1l-1 1h0c-1-2-3-4-4-5h-2l-1-3-1-4z" class="C"></path><path d="M426 416c1 0 2 1 4 1h1 1 2c2 2 5 2 7 4l1 1c1 0 1 0 2 1-1 1-1 0-1 1h-1-1c-3 0-4-1-7-2l-4-4c-1 0-2 1-3 2l-1-4z" class="Q"></path><path d="M324 138h3l4 1h1l2 1c10 4 22 8 28 18l1 1c1 2 2 4 3 5l4 12 2-6 1 2-1 4c-1 3-3 8-4 12-2 3-5 5-6 9l-1 2h1c1 0 1-1 2-1l3-2h1v-1c1 0 1-1 3-2h0 1v1l1 5-3 7c-1 1-2 2-2 4h0c-1 3 0 9 1 12 0 1 1 3 1 5h-1c0 2 1 4 1 6v1c-1 7 2 15 0 21v4-1c0-2-1-3-1-5l-1-2c0 3 1 5 1 7-1 0-1-1-1-1l-1-4h-1c-1-1-2-2-2-3h-1c-3-8-7-19-13-25l-2-2c-1-3-2-7-3-9l-1 1c-1-1-2-3-3-4-1-2 0-1-1-2 0-1-1-2-2-2l-1 1h0v2h-2l-5-4-2-2h-1c-1 0-2-1-3-2-4-4-8-8-11-13 0 0-1-1-1-2l-1 1h0l-4-3c1 0 1 0 1-1v-3h1c0 2 0 2 1 3v-2h1v-3c0-1-1-1-1-2h-2v-1c2-1 2-7 3-9h0l-1 1c0-4 0-5 1-9 2-4 4-7 7-9 1 0 2-1 3-1v-1-2h2c1-1 1-1 2-1 0-2-1-3-1-4h0-1l1-1-1-1 1-1h0z" class="AP"></path><path d="M324 138h0l1 2c2 0 3 0 5 1v1h0c1 1 0 1 1 1l-7-2h-1l1-1-1-1 1-1z" class="J"></path><path d="M351 206c3 0 7 0 9 1 0 1-1 2-1 3h-1c-2 0-3 0-5 1v-1-1l-1-1s-1 0-1-1c-1-1 0-1 0-1z" class="O"></path><path d="M318 178c-1-3-1-7 0-11 0-2 0-5 2-7l1 1 1-1v1h1l-2 6-2 2v7 2h0-1z" class="L"></path><path d="M345 205l6 1s-1 0 0 1c0 1 1 1 1 1l1 1v1 1c-1 0-2 1-4 2h-1c0-2-1-3-1-4l-1-1c-1-2-3-2-5-2v-1h4z" class="k"></path><path d="M347 209l6 1v1c-1 0-2 1-4 2h-1c0-2-1-3-1-4z" class="g"></path><path d="M311 179c2-3 1-7 3-10h0c0 7 0 13 3 20h-1c-2-1-3-5-5-7v-3zm46-20h0c-1-1-1-1-1-3 6 7 10 15 10 23 0 5-3 10-5 14-1-2 1-4 2-6 1-3 1-5 1-8v-4c0-6-3-11-7-16z" class="p"></path><path d="M369 227l-2-10c-2-9 0-16 5-23l1 5-3 7c-1 1-2 2-2 4h0c-1 3 0 9 1 12 0 1 1 3 1 5h-1z" class="k"></path><path d="M360 207l1 2c1 0 1 1 2 2v4h-1c-2-2-3-2-5-2s-4 0-6 2c-1 1-1 0-1 1-1 2-1 3-1 5l1 2v2l-2-2-3-9v-2c1 1 2 2 2 4v1h1c0-1 0-2 1-3v-1c2-1 3-2 4-2 2-1 3-1 5-1h1c0-1 1-2 1-3z" class="J"></path><path d="M330 206h6c1 0 2 0 3 1 0-1 1-1 2-1 2 0 4 0 5 2l1 1c0 1 1 2 1 4h1v1c-1 1-1 2-1 3h-1v-1c0-2-1-3-2-4v2l-1 1c-1-1-2-3-3-4-1-2 0-1-1-2 0-1-1-2-2-2l-1 1h0v2h-2l-5-4z" class="b"></path><path d="M311 167c1-3 3-7 5-9l2-3c2-2 5-4 8-4-2 1-6 3-6 5-3 4-4 8-6 13h0c-2 3-1 7-3 10 0-1-1-1-1-2h-2v-1c2-1 2-7 3-9z" class="O"></path><path d="M318 178h1 0v-2-7l2-2v1c0 6 0 10 1 15 1 2 2 5 4 7s3 3 4 5l3 3 1 1h0-1 0c-3 0-4-2-6-4l-2-1-1-1c-4-3-5-10-6-15z" class="Z"></path><path d="M325 194l-2-3c-2-4-2-6-2-9 0 0-1 0-1-1h1v2c1 4 6 10 9 12h0l3 3 1 1h0-1 0c-3 0-4-2-6-4l-2-1z" class="o"></path><path d="M350 216c0-1 0 0 1-1 2-2 4-2 6-2s3 0 5 2h1c1 6 3 13 3 20h-1c0-3-1-5-3-7l-2-6-2-4-1-1h-6l-1-1z" class="p"></path><path d="M350 216c0-1 0 0 1-1 2-2 4-2 6-2s3 0 5 2h-2c0 1 1 1 1 2 1 1 1 3 1 4-2 0-3-2-4-3l-1-1h-6l-1-1z" class="AI"></path><path d="M307 185c1 0 1 0 1-1v-3h1c0 2 0 2 1 3v-2h1c2 2 3 6 5 7h1c6 8 11 13 21 15 2 0 5 0 7 1h-4v1c-1 0-2 0-2 1-1-1-2-1-3-1h-6l-2-2h-1c-1 0-2-1-3-2-4-4-8-8-11-13 0 0-1-1-1-2l-1 1h0l-4-3z" class="g"></path><path d="M324 141l7 2c1 1 2 1 4 2l5 5 1 1-1 1-2-3-2 1c0-1-1-1-2-1-2 0-7 0-8 2-3 0-6 2-8 4l-2 3c-2 2-4 6-5 9h0l-1 1c0-4 0-5 1-9 2-4 4-7 7-9 1 0 2-1 3-1v-1-2h2c1-1 1-1 2-1 0-2-1-3-1-4h0z" class="k"></path><path d="M324 141l7 2c1 1 2 1 4 2-3 1-5 0-8 1-1 1-2 1-3 1h-1l-2 1v-2h2c1-1 1-1 2-1 0-2-1-3-1-4h0z" class="p"></path><path d="M326 151c1-2 6-2 8-2 1 0 2 0 2 1l2-1 2 3 3 3 1 2v1h-1c-3-1-4-1-6 1v-2l-2 1c0-1 0-1-1-2v-1c-2 0-4 1-6 1-4 4-5 6-6 10 0 1 0 2-1 2v-1l2-6h-1v-1l-1 1-1-1c0-1 0-2 1-3l-1-1c0-2 4-4 6-5z" class="b"></path><path d="M330 152c1-1 2-1 4 0 1 0 2 1 2 1v1h-4l-2-2z" class="G"></path><path d="M330 152l2 2h4l-1 2h1 2c1-1 3-1 5-1l1 2v1h-1c-3-1-4-1-6 1v-2l-2 1c0-1 0-1-1-2v-1c-2 0-4 1-6 1-4 4-5 6-6 10 0 1 0 2-1 2v-1l2-6c1-2 2-4 4-6 1-2 1-2 3-3z" class="o"></path><path d="M330 152l2 2c-2 1-3 1-5 1 1-2 1-2 3-3z" class="Z"></path><path d="M350 216l1 1h6l1 1 2 4 2 6c2 2 3 4 3 7h1c2 5 1 11 2 16 0 3 1 5 1 7-1 0-1-1-1-1l-1-4h-1c-1-1-2-2-2-3h-1c-3-8-7-19-13-25v-2l-1-2c0-2 0-3 1-5z" class="K"></path><path d="M362 228c2 2 3 4 3 7h1c2 5 1 11 2 16 0 3 1 5 1 7-1 0-1-1-1-1l-1-4-4-17 1-1c-1-2-1-4-2-7z" class="h"></path><path d="M350 216l1 1h6l1 1 2 4 2 6c1 3 1 5 2 7l-1 1c0-1-1-5-2-6-1-2-4-9-6-10l-1 2c0 2 1 4 2 6s2 5 3 8v1l5 13h-1c-3-8-7-19-13-25v-2l-1-2c0-2 0-3 1-5z" class="Z"></path><path d="M350 216l1 1h6l1 1 2 4-3-3c-1 0-1-1-3-1h0c-1 1-3 1-4 1 1 1 2 3 3 5v1c1 4 4 9 6 12l5 13h-1c-3-8-7-19-13-25v-2l-1-2c0-2 0-3 1-5z" class="L"></path><g class="J"><path d="M321 168c1 0 1-1 1-2 1-4 2-6 6-10 2 0 4-1 6-1v1c1 1 1 1 1 2l2-1v2c-3 2-5 5-7 9h0c1-1 3-5 5-5 0 2-1 4-2 6l-2 6c-2 3-4 9-3 13 0 0 1 0 1 1v1c1 2 3 5 5 7l-1 1-3-3c-1-2-2-3-4-5s-3-5-4-7c-1-5-1-9-1-15z"></path><path d="M340 150c0-1 0-1 1-2h1c5 1 11 5 14 8h0c0 2 0 2 1 3h0c4 5 7 10 7 16v4c0 3 0 5-1 8-1 2-3 4-2 6-2 3-4 5-7 6l-2 1h-1c-1 0-3 1-4 1-4 0-10-2-13-4-2-2-4-5-5-7v-1c0-1-1-1-1-1-1-4 1-10 3-13l2-6c1-2 2-4 2-6-2 0-4 4-5 5h0c2-4 4-7 7-9 2-2 3-2 6-1h1v-1l-1-2-3-3 1-1-1-1z"></path></g><path d="M341 151c2 3 4 6 5 10h-2c-1-1-3-1-4-3h3 1v-1l-1-2-3-3 1-1z" class="h"></path><path d="M337 159c2-2 3-2 6-1h-3c1 2 3 2 4 3h2c2 3 4 10 2 13h0 0c0-4-1-6-2-9-1-1-2-2-4-2l-3 2-1-1 2-3v-1c-1 0-2 0-3-1h0z" class="b"></path><path d="M342 148c5 1 11 5 14 8h0c0 2 0 2 1 3h0l1 3c1 2 2 6 3 9 0 2 1 4 1 6l-1 1c0-2 0-5-1-7s-3-8-5-10l-3-2-2 2v1h-1c0-1-1-2-1-4-2-3-5-6-7-9l1-1z" class="g"></path><g class="G"><path d="M337 159h0c1 1 2 1 3 1v1l-2 3 1 1h0c-1 3-3 5-5 7-1 2-2 4-2 6v1c-1 4-2 8 0 12v1c1 2 3 5 6 6 1 1 2 1 3 1h3v1c2 0 4-1 6-1l2 1h-1c-1 0-3 1-4 1-4 0-10-2-13-4-2-2-4-5-5-7v-1c0-1-1-1-1-1-1-4 1-10 3-13l2-6c1-2 2-4 2-6-2 0-4 4-5 5h0c2-4 4-7 7-9z"></path><path d="M357 159c4 5 7 10 7 16v4c0 3 0 5-1 8-1 2-3 4-2 6-2 3-4 5-7 6l-2 1-2-1h1v-1h-1c4-3 8-8 9-13h1c0-2 1-6 0-8v-2-4c1 2 1 5 1 7l1-1c0-2-1-4-1-6-1-3-2-7-3-9l-1-3z"></path></g><path d="M351 199c3-1 4-2 6-3 0-2 1-5 3-6 1-2 2-4 2-6s1-4 2-5c0 3 0 5-1 8-1 2-3 4-2 6-2 3-4 5-7 6l-2 1-2-1h1z" class="n"></path><path d="M406 566c0-2-1-4-1-5l-2-9-4-13c-1-5-6-33-4-36 1 1 1 2 2 2l-1-2-1-1v-2l2 4h1c-1-2-3-4-3-5v-5c0-6 2-12 4-18 3-6 5-11 5-17 0-2 0-2 1-3 2 4 4 7 6 11 4 9 8 20 9 30 1 5 0 8-1 12v2h1c0 1 1 1 1 2 0 2 2 4 3 7 2 5 4 10 7 14 5 8 11 14 15 22 0 3 2 6 1 9h0l1 7c1 5 1 10 0 15-1 1-1 2-1 3v1c-1 4-4 9-7 12h0c-4 2-9 7-14 6l-1-1c-5-2-7-5-10-10 0-2-2-5-3-7 1-1 1-1 1-2v-2c0-1-1-2-1-3l-1-4-5-14z" class="AP"></path><path d="M411 580h0c5 8 10 22 19 26l5-2h0c1 0 3-1 4-1v-1l1 1c-4 2-9 7-14 6l-1-1c-5-2-7-5-10-10 0-2-2-5-3-7 1-1 1-1 1-2v-2c0-1-1-2-1-3l-1-4z" class="i"></path><path d="M415 598c0-2-2-5-3-7 1-1 1-1 1-2 1 3 2 6 4 10h1c0 1 1 2 2 3 2 1 3 4 5 6h0c-5-2-7-5-10-10z" class="W"></path><path d="M419 509v2h1c0 1 1 1 1 2 0 2 2 4 3 7 2 5 4 10 7 14 5 8 11 14 15 22 0 3 2 6 1 9-2-3-3-6-4-8-2-6-7-10-10-15l-4-5-1-1c-1-2-2-3-3-5 0-1-1-3-1-4h-1c0-1-1-2-1-3h1c0 1 0 1 1 1v-1c0-1-1-1-1-2h0l-1-2v-1l-1-1c0-3-3-5-2-9z" class="d"></path><path d="M435 604h-3c-4-1-7-6-9-9-6-11-9-24-12-36-1-7-3-15-1-22l1-1c1-4 2-6 5-9-4-3-7-6-10-9-1-2-2-3-2-4 0 0 1 1 2 1l1 3c2 2 5 6 8 8h1c0 1 0 1 1 1l4 4 3 4 4 4c1 1 2 3 3 4v1l-12-14c-2 3-5 7-6 11v1c-2 8-1 17 1 25 2 10 7 22 14 30 2 1 4 3 5 4h3c4-1 8-7 11-11v1c-1 4-4 9-7 12h0l-1-1v1c-1 0-3 1-4 1h0z" class="K"></path><defs><linearGradient id="Af" x1="582.235" y1="579.555" x2="532.043" y2="571.977" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#212328"></stop></linearGradient></defs><path fill="url(#Af)" d="M576 526c2-2 4-4 5-6 0-1 1-1 1-1h3c2 1 4 1 6 1h1v1l-30 114c-1-1-1-2-2-3v1h-1v2c0 2 0 3-1 5h0l2 2-2 6-1 3-2 8-2 6c2 5 0 8-1 13l-2 5c0-2 0-2-1-3l-1 3h-1v1l-1-4c1-4-1-9-1-14-1-1-2-2-2-3 0-2-1-4-2-6 0-1 0-1-1-1h-1c-1-1-1-2-2-3l-1-1s-1-1-1-2c-1-2-3-9-5-10h0-1c-2-2-2-4-3-6-2-4-4-7-4-11 0-3-2-6-1-8h1l-1-1-1-1 3-3h-1c1-1 5-4 6-5l1 1 1-1c1-2 3-6 6-8 2-3 4-5 4-8 1-1 1-2 2-3 1-2 2-3 3-5 1-1 1-1 1-2l2-4h-1v-9-1c-1-1-1-1-1-3l-1-1h1l5-5 10-12 5-8c1-1 3-6 4-7h0l3-1h0c0-2 1-3 2-5 1 1 1 2 1 3z"></path><path d="M536 597h0c2-1 4-3 6-5h0c-3 4-8 10-12 13 1-2 3-6 6-8z" class="AZ"></path><path d="M538 613c-2 2-4 2-6 2h0c-2 1-3 1-3 2v2h-1c-1-1-1-3-1-4 1-1 1-2 1-2 1-1 1-2 2-3 0-1 1-2 1-2 2-2 2-2 4-3l-3 3c0 1-1 1-1 2v1c-1 1-1 1-1 2 3 1 5 1 8 0z" class="AN"></path><path d="M576 526c2-2 4-4 5-6 0-1 1-1 1-1h3c-2 1-3 2-5 4-4 6-6 11-9 18h-1l-1 1c0 1-1 2-2 3h-1l2-3c2-2 4-11 5-14h0c0-2 1-3 2-5 1 1 1 2 1 3z" class="m"></path><path d="M573 528c0-2 1-3 2-5 1 1 1 2 1 3s-1 2-1 3l-1 1c-1-1-1-1-1-2z" class="a"></path><path d="M570 529l3-1c-1 3-3 12-5 14h-1c-2 1-3 3-4 4-2 0-2-1-2-2l5-8c1-1 3-6 4-7h0z" class="D"></path><path d="M561 571v-1c0-1 0-1 1-2h0c1 2-1 3 0 5v1c0 2-1 6-2 9h0c-2 4-2 8-3 12-1 5-2 10-2 15v1l-1-1v-3-6c-1-2-1-3-1-5 0-4 2-9 3-13v-2c1-2 1-2 1-3l-2 1c2-3 3-6 6-8z" class="AQ"></path><path d="M561 571c-1 3-3 9-5 12v-2c1-2 1-2 1-3l-2 1c2-3 3-6 6-8zm-26 34h0c4-3 5-5 8-9l3-6v2c1 3-2 12-4 16-1 3-2 4-4 5-3 1-5 1-8 0 0-1 0-1 1-2v-1c0-1 1-1 1-2l3-3z" class="y"></path><path d="M560 583c-1 2-3 9-2 11s0 5 0 6h1c1 1 1 3 1 5l-1 1c0 2 0 3-1 5h0l1 1v-2c1-1 0-3 1-5 0-2 0-5 1-6v-4c0-2 0-3 1-4v-3c0-1 0-2 1-3v-3c1-1 1-2 1-3h0c1 1-2 9-2 11-1 8-2 17-2 25-1 6 0 12 0 17v1h-1c0-1 0-2-1-3-2-6-2-14-3-20 0-5 1-10 2-15 1-4 1-8 3-12z" class="AZ"></path><path d="M522 610c1-1 5-4 6-5l1 1c-2 3-5 5-4 9 1 7 4 13 8 19l1 1 2 4v2l1 1v1 3c1 1 1 3 1 5l1 1-2 1-1-1s-1-1-1-2c-1-2-3-9-5-10h0-1c-2-2-2-4-3-6-2-4-4-7-4-11 0-3-2-6-1-8h1l-1-1-1-1 3-3h-1z" class="Ae"></path><path d="M529 640c-2-2-2-4-3-6-2-4-4-7-4-11 0-3-2-6-1-8h1l3 9c3 8 8 15 11 24 0 1 1 3 1 5l-1-1s-1-1-1-2c-1-2-3-9-5-10h0-1z" class="s"></path><defs><linearGradient id="Ag" x1="543.03" y1="583.511" x2="557.887" y2="616.803" xlink:href="#B"><stop offset="0" stop-color="#292e34"></stop><stop offset="1" stop-color="#626268"></stop></linearGradient></defs><path fill="url(#Ag)" d="M555 579l2-1c0 1 0 1-1 3v2c-1 4-3 9-3 13l-1 5c-1 3-1 5-2 8 0 2 0 5-1 6s-2 3-2 4c2 3 1 10 1 13-1 2-1 5-1 8v2 1c0 1 0 3 1 5v3l-1-1h0c0-1-1-2-2-3l1 3c-5-6-6-11-6-18-1 1-3 3-5 4v-1c0-1 1-1 2-2 3-3 5-5 6-9 3-15 6-30 12-45z"></path><path d="M545 647c-1-1-1-3-2-5-1-5-2-12 1-17v4c0 3-1 9 0 12 2 1 2 1 3 2v-1 1c0 1 0 3 1 5v3l-1-1h0c0-1-1-2-2-3z" class="y"></path><path d="M547 619c2 3 1 10 1 13-1 2-1 5-1 8v2 1c-1-1-1-1-3-2-1-3 0-9 0-12v-4l3-6z" class="Aa"></path><defs><linearGradient id="Ah" x1="551.071" y1="661.861" x2="541.554" y2="663.164" xlink:href="#B"><stop offset="0" stop-color="#030303"></stop><stop offset="1" stop-color="#222429"></stop></linearGradient></defs><path fill="url(#Ah)" d="M535 635v1c2-1 4-3 5-4 0 7 1 12 6 18 0 0 0 1 1 2h0c1 1 1 1 1 2l-1 1c2 4 5 6 6 10h0c2 5 0 8-1 13l-2 5c0-2 0-2-1-3l-1 3h-1v1l-1-4c1-4-1-9-1-14-1-1-2-2-2-3 0-2-1-4-2-6 0-1 0-1-1-1h-1c-1-1-1-2-2-3l2-1-1-1c0-2 0-4-1-5v-3-1l-1-1v-2l-2-4h1z"></path><path d="M550 674l2 4-2 5c0-2 0-2-1-3 0-1 1-5 1-6z" class="L"></path><path d="M540 648c1 2 0 3 1 5s1 4 2 6c0 2 1 5 2 7-1-1-2-2-2-3 0-2-1-4-2-6 0-1 0-1-1-1v-8z" class="y"></path><path d="M553 665h0c2 5 0 8-1 13l-2-4 3-9z" class="G"></path><path d="M535 635v1c2-1 4-3 5-4 0 7 1 12 6 18 0 0 0 1 1 2h0c1 1 1 1 1 2l-1 1c0-1-1-3-2-3 0-1-1-1-2-3s-3-4-3-6l-2-2c0 2 1 4 1 6l1 1v8h-1c-1-1-1-2-2-3l2-1-1-1c0-2 0-4-1-5v-3-1l-1-1v-2l-2-4h1z" class="AU"></path><path d="M566 545h1c1-1 2-2 2-3l1-1h1c-1 5-5 9-7 14l-4 7c-6 8-9 19-16 27 0 1-1 2-2 3h0c-2 2-4 4-6 5h0c2-3 4-5 4-8 1-1 1-2 2-3 1-2 2-3 3-5 1-1 1-1 1-2l2-4h-1v-9-1c-1-1-1-1-1-3l-1-1h1l5-5 10-12c0 1 0 2 2 2 1-1 2-3 4-4h1l-2 3z" class="s"></path><path d="M547 566h1l1-1h1c3-1 8-7 10-9v1c-3 5-6 10-9 14l-1 1c0 1-1 2-2 3h-1v-9z" class="AU"></path><path d="M563 546c1-1 2-3 4-4h1l-2 3c-2 4-4 7-6 11-2 2-7 8-10 9h-1l-1 1h-1v-1c-1-1-1-1-1-3l-1-1h1l5-5 10-12c0 1 0 2 2 2z" class="q"></path><path d="M551 556c1 1 2 0 4 0l-5 5c-1 1-1 2-2 3l-1 1c-1-1-1-1-1-3l-1-1h1l5-5z" class="B"></path><path d="M551 556l10-12c0 1 0 2 2 2-3 4-5 7-8 10-2 0-3 1-4 0z" class="C"></path><path d="M553 596c0 2 0 3 1 5v6 3l1 1v-1c1 6 1 14 3 20 1 1 1 2 1 3v2c0 2 0 3-1 5h0l2 2-2 6-1 3-2 8-2 6h0c-1-4-4-6-6-10l1-1c0-1 0-1-1-2h0c-1-1-1-2-1-2l-1-3c1 1 2 2 2 3h0l1 1v-3c-1-2-1-4-1-5v-1-2c0-3 0-6 1-8 0-3 1-10-1-13 0-1 1-3 2-4s1-4 1-6c1-3 1-5 2-8l1-5z" class="Aa"></path><path d="M558 640l2 2-2 6c-1-2-1-4-1-6l1-2z" class="w"></path><path d="M547 643h1c1 2 1 4 1 6v1c1 2 2 4 3 7l1 1s1 1 2 1l-2 6h0c-1-4-4-6-6-10l1-1c0-1 0-1-1-2h0c-1-1-1-2-1-2l-1-3c1 1 2 2 2 3h0l1 1v-3c-1-2-1-4-1-5z" class="AE"></path><path d="M553 596c0 2 0 3 1 5v6 3l1 1v-1c1 6 1 14 3 20 1 1 1 2 1 3v2c0 2 0 3-1 5h0l-1 2c-2-6-5-12-6-18v-1c-1-2-1-3-1-5-1 1 0 4-1 6v8c0 4-1 8-1 11h-1v-1-2c0-3 0-6 1-8 0-3 1-10-1-13 0-1 1-3 2-4s1-4 1-6c1-3 1-5 2-8l1-5z" class="AU"></path><path d="M556 630l1-1v1c0 2 1 3 2 5 0 2 0 3-1 5l-2-10z" class="AQ"></path><path d="M553 596c0 2 0 3 1 5v6 3l1 1v-1c1 6 1 14 3 20 1 1 1 2 1 3v2c-1-2-2-3-2-5v-1l-1 1c-1-1-1-3-1-4-2-4-2-8-3-13 0-4 2-9 0-12l1-5z" class="x"></path><path d="M687 292l1 1c0 2-1 3-2 5h1c1-1 2-3 4-3l-1 5-3 15v1c-1 2-3 5-4 7-1 3-1 7-2 10v7c-1 1-3 4-4 5l-3 6c-4 13-8 27-11 40l-3 13-1 2-3 10-11 43-1 6c-1 1-2 3-2 5l-5 19-5 20c-2 6-4 14-5 20l-9 33-24 84-11 39c-2 8-5 16-7 25-1 3-1 7-1 11l4 9-4 5c-1 2-2 3-3 4-3 6-4 13-1 19v1c0 1 1 1 0 2-2 0-3-1-4-2l-2-4-1-2c-1-4-1-6 0-10l-1 1c0-3 1-5 2-8l-2-2h-1v-2-1l2 2h1c-1-1-1-2-1-3h0 0c0-4 0-9 1-13 0-3 1-5 2-8 0-3 0-6 1-9 1-1 1-1 1-2s0-1 1-2h0c-1 2-1 5-1 6v2l13-45v-3c1-1 1-2 1-3l1-2v-1c1-1 1-1 1-2s0-1 1-3l16-62h1l1-3 2-6v-1l1-4v-1l1-1v-2c1-2 1-3 1-4l1-3v3h1c1-3 1-7 2-11 1-3 2-5 3-8 1-4 2-9 4-13 1-1 1-1 1-2h0l2-4 2-3v-1c-1 0-1 1-2 1-2 0-2 0-3-1 4-11 7-21 9-33l2-1-1-2-1-4c-1-1-3-1-5-2l-4-1h-1c-4-6-1-17 1-23l-1-3c1-2 1-5 2-8l4-17 10-42 4-17c2-5 3-11 8-14l1-1c0-1 2-1 3-2l7-4 2-1c1-1 2-4 2-5 1-3 2-5 3-8h1l-1-2h0c1-1 1-2 2-2l4-6-2-2 4-5 3-6h1c1-2 2-3 3-5l1-2 2-4 4-5z" class="o"></path><path d="M680 303c0 1 1 1 1 2l-1 3-2 6v-4l-2 3c0-2 1-3 1-5 1-2 2-3 3-5z" class="Z"></path><path d="M673 314l3-6h1c0 2-1 3-1 5l-6 15v-2c0-2 2-4 1-6v1l-2-2 4-5z" class="h"></path><path d="M575 721l4 9-4 5c-1 2-2 3-3 4l-1-1c1-2 3-4 5-6h1c-1-1-2-1-3-2 1-3 1-6 1-9z" class="J"></path><path d="M570 723l2-7v1l1-1c1 1-1 8-2 10-1 5-4 9-5 15l-2 2-1 1c0-3 1-5 2-8l-2-2h-1v-2-1l2 2h1c-1-1-1-2-1-3h0 0l1 2 1-1v2h1l1-2c0-1-1-1 0-1 1-1 0-1 1-2 1-2 1-3 1-5z" class="i"></path><path d="M571 738l1 1c-3 6-4 13-1 19v1c0 1 1 1 0 2-2 0-3-1-4-2l-2-4-1-2c-1-4-1-6 0-10l2-2-1 11c1 2 2 6 4 7h1 0l-1-2c-1-3-1-7-1-11v3c0 1-1 2-1 4h-1c1-5 2-10 4-14l1-1z" class="K"></path><path d="M632 464l3-3c0-1 0-1 1-1-1 3-1 7-2 11-2 4-3 8-4 12l-1-4c-1-1-3-1-5-2l-4-1 1-1 4 1h2c2-4 4-7 5-12zm39-143v-1c1 2-1 4-1 6v2c-1 5-4 10-6 16-3 6-4 13-5 20h-1c0 1 0 1-1 2 0-3 0-5-1-8h0c-1-2-1-3-2-4l-2-2h-5l1-1c0-1 2-1 3-2l7-4 2-1c1-1 2-4 2-5 1-3 2-5 3-8h1l-1-2h0c1-1 1-2 2-2l4-6z" class="n"></path><path d="M659 346l2-2v1c0 4-1 10-2 14v-1c-1-5-1-7 0-12z" class="Z"></path><path d="M659 346c-1 5-1 7 0 12v1l-1 5c0 1 0 1-1 2 0-3 0-5-1-8h0c-1-2-1-3-2-4l-2-2h-5l1-1c1 0 2 0 3-1 2-2 5-3 8-4z" class="J"></path><path d="M602 583h1l-6 24c0 2-1 3-1 5l-28 104c1 3 0 5 1 9l1-2c0 2 0 3-1 5-1 1 0 1-1 2-1 0 0 0 0 1l-1 2h-1v-2l-1 1-1-2c0-4 0-9 1-13 0-3 1-5 2-8 0-3 0-6 1-9 1-1 1-1 1-2s0-1 1-2h0c-1 2-1 5-1 6v2l13-45v-3c1-1 1-2 1-3l1-2v-1c1-1 1-1 1-2s0-1 1-3l16-62z" class="b"></path><path d="M568 716c1 3 0 5 1 9l1-2c0 2 0 3-1 5-1 1 0 1-1 2-1 0 0 0 0 1l-1 2h-1v-2-8l2-7zm119-424l1 1c0 2-1 3-2 5h1c1-1 2-3 4-3l-1 5-3 15v1c-1 2-3 5-4 7-1 3-1 7-2 10l-2 1c-1 0-2 0-3 1 0 1-1 1-1 2l-6 3c1-2 3-4 6-6v-1h0c0-2 1-5 1-7l2-12 2-6 1-3c0-1-1-1-1-2l1-2 2-4 4-5z" class="K"></path><defs><linearGradient id="Ai" x1="677.632" y1="309.143" x2="680.368" y2="321.357" xlink:href="#B"><stop offset="0" stop-color="#b5a296"></stop><stop offset="1" stop-color="#cfc3b5"></stop></linearGradient></defs><path fill="url(#Ai)" d="M680 308h3 0c0 2-1 2-1 4-1 2-1 4-2 6l-3 8h0-1l2-12 2-6z"></path><path d="M687 292l1 1c0 2-1 3-2 5h1c1-1 2-3 4-3l-1 5c-1 0-2 0-3 1-2 2-3 4-4 7h0-3l1-3c0-1-1-1-1-2l1-2 2-4 4-5z" class="p"></path><path d="M683 297c1 2 0 4 0 6l-1 2h-1c0-1-1-1-1-2l1-2 2-4z" class="b"></path><path d="M647 352h5l2 2c1 1 1 2 2 4h0c1 3 1 5 1 8 1-1 1-1 1-2h1c-1 8-3 15-5 23l-12 49-4 18c-1 1-1 5-2 6-1 0-1 0-1 1l-3 3c-1 5-3 8-5 12h-2l-4-1-1 1h-1c-4-6-1-17 1-23l-1-3c1-2 1-5 2-8l4-17 10-42 4-17c2-5 3-11 8-14z" class="AP"></path><path d="M625 458c1-4 3-10 5-12-2 5-4 10-5 16 0 4 1 9 1 13h-2v-2-2-3c1-2 0-7 1-9h0v-1z" class="G"></path><path d="M638 441c1-1 1-3 1-4 1-2 1-4 1-7 1-2 1-5 2-8 0-2 1-4 1-5l1-1v-1h1v-1l1-1v-2c1-1 1-1 1-2v-3c1-1 1-1 1-2v-1c0-1 0-1 1-2v-1-1h0c1-2 0-1 1-2v-2l1-2c0-1 0-2 1-3 0 1 0 2-1 3v1 2c-1 3-2 6-2 9l-6 25c-1 3-2 7-5 11z" class="K"></path><path d="M623 444v-2c1-2 2-8 4-9-1 3-1 7-3 10 0 5-1 11 1 15v1h0c-1 2 0 7-1 9v3 2 2h2l1 1h-2l-4-1-1 1h-1c-4-6-1-17 1-23l3-9z" class="d"></path><path d="M619 470l1-2c2 1 2 1 2 2h0c1 1 1 2 2 3v2h2l1 1h-2l-4-1c-1-1-2-3-2-5z" class="K"></path><path d="M623 444v-2c1-2 2-8 4-9-1 3-1 7-3 10 0 3-1 6-2 9 0 3 0 10-2 12l-1-1v7c0 2 1 4 2 5l-1 1h-1c-4-6-1-17 1-23l3-9z" class="b"></path><path d="M643 366c0-2 1-3 2-4 0-2 1-3 3-4 1-2 2-3 4-4h2c1 1 1 2 2 4h0c1 3 1 5 1 8 1-1 1-1 1-2h1c-1 8-3 15-5 23l-12 49-4 18c-1 1-1 5-2 6-1 0-1 0-1 1l-3 3 6-23c3-4 4-8 5-11l6-25c0-3 1-6 2-9v-2-1c1-1 1-2 1-3v-1h0v-1c1-2 1-2 1-3v-3c1-1 1-2 1-3v-2c1-1 1-2 1-3v-1h0c-1 2-1 4-2 6-1 4-1 8-3 12h0c1-7 3-13 4-20 1-2 0-4 1-6v-1c-1-1 0-2 0-4h-1l1-1-1-1v-1c-1 0-1-1-1-1-1 1-2 0-3 1 0 2-2 3-3 4-1 2-2 3-3 5h-1z" class="d"></path><path d="M647 352h5l2 2h-2c-2 1-3 2-4 4-2 1-3 2-3 4-1 1-2 2-2 4l-2 7c-1 5 0 11-2 16l-6 23-4 14c0 2-1 5-2 7-2 1-3 7-4 9v2l-3 9-1-3c1-2 1-5 2-8l4-17 10-42 4-17c2-5 3-11 8-14z" class="n"></path><path d="M633 405l6-21v5l-6 23c-1-3 0-5 0-7z" class="i"></path><path d="M633 405c0 2-1 4 0 7l-4 14c0 2-1 5-2 7-2 1-3 7-4 9v2l-1-2 1-2c0-2 1-5 1-8 1-1 1-2 1-3 1-1 1-3 1-4 1-1 1-1 1-2v-3l1-1v-2l1-3c1-1 1-2 1-3h1v-1-1-1l1-1c0-1 0-1 1-2z" class="W"></path><path d="M634 332h1l-5 25-2 8v1c-1 3-4 6-5 8 1 1 2 2 2 4 1 2 0 5-1 7 0 1-1 2-3 3v3c3 7 1 15-1 22h0c-1 4-9 36-10 37l-6 24-12 47v-1h-1c-2 0-4 0-6-1h-3s-1 0-1 1c-1 2-3 4-5 6 0-1 0-2-1-3-1 2-2 3-2 5h0l-3 1h0c-1 1-3 6-4 7l-5 8-10 12-5 5h-1l1 1c0 2 0 2 1 3v1 9h1l-2 4c0 1 0 1-1 2-1 2-2 3-3 5-1 1-1 2-2 3 0 3-2 5-4 8-3 2-5 6-6 8l-1 1-1-1c-1 1-5 4-6 5-2 1-4 3-6 2-2 0-3 0-3-1l-2-1c1-1 1-1 1-2l1-1h-9c-2 0-4-1-6-1l1-1c-1-3-1-6 0-9 0-1 1-2 1-4 0-4 1-8 1-13-1 1-1 3-1 5 0 1-1 2-2 3v-7-1c2-2 2-3 2-6l-2-2v-3c-1 0-2 0-3 1 0-3-1-6-2-9-3-7-9-12-13-18h-1c-2-4-5-7-8-10-4-3-7-8-9-13 0-2-1-3-2-5-1-1-1-2-1-3s-2-3-2-3h0l-3-2c-1 0-1-4-2-5-3-9-6-18-8-27-1-1-11-34-12-39-1 0-1-2-1-2l-3-10h2c1 1 3 3 4 5h0l1-1h1l1 1h1 7 0c1-1 1-1 3 0h2 1 1v-1c0-1 1-1 2-1h0v-3h-1 0c0-1 1-1 2-2l6-3 12-8c1 0 2-1 2-2l-2-3h1c1 1 1 1 2 3h1l7-4 1-1 4-2-2-2c1 0 2 0 3-1l2-2h2 0l-1-1v-2l2-1 1-1 1-1h2 0 1l1-2v-11c0-3 0-7 1-10l-2-15 1-1v-4l1-1c-1 0-1 0-1-1h3v-3h-2c-2-1-4-2-6-2l-3-2 6-2h0c0 1 0 1-1 2 1 0 1 1 1 1h3c3 1 3 1 5 3l3-2 1 1c1-1 1-2 2-3 1 1 2 1 2 1h3l2-1-2-1v-2h0c2 0 3-1 5-1l1 2c2 0 3 0 5 2 0 0 1 0 1 1-1 1-2 1-3 2-1 0-1-1-2-2l-1 3c0 1 0 1 2 2v4h0c0 2 0 3 1 4 3 2 6 3 10 3-1 2-3 3-5 5l-1 5h1c0 1-1 2-2 3l2 1v3h0c-2 2-3 4-3 6 0 1 1 1 1 2h1c-1 2-1 3-1 5h0l1 1v1l2 2c1-1 3-1 4-1l1 1c-1 1-1 1-1 2 1 0 1 1 2 0 2-1 4-3 6-4 1 1 1 1 3 1 1-1 3-2 4-3l1-1c3-2 7-7 11-6h1 0c2 0 3 0 4 1s2 1 2 2c1 0 2-1 2-1v1h1v-2c1-2 3-3 4-5 0-2 1-3 3-4l-2-1 6-6c2-2 4-5 6-7l5-1h0c1 0 1 0 2-1 2-1 3-1 4-1l3-3 6-3c1 1 1 1 1 2l4-3 7-6h1c3-1 8-6 10-8z" class="X"></path><path d="M506 537c-1-4 0-9 0-13 0-5-1-12 1-17 0 2 0 4 1 6v-1l1 1v9c-1 1-2 3-2 4l1 2c0 1-1 1-1 2v6l-1 1z" class="c"></path><path d="M509 464l1-1v34 18 8c0-3 0-8-1-10l-1-1v-38l1-10z" class="e"></path><path d="M505 439c0 1 0 2 1 3l1-1v6l1 1 1-5h1c-1 3-1 7 0 11-1 3-1 6 0 9l-1 1-1 10c-1 5 0 11 0 15-2-2 0-8-1-11h-1v6h-1v-45z" class="r"></path><path d="M509 443h1c-1 3-1 7 0 11-1 3-1 6 0 9l-1 1-1-16 1-5z" class="AM"></path><path d="M504 366v3 26 94-9-16-23c-1-2 0-4 0-6-1-1-2-2-3-4 0-2 1-4 1-6v-17h-1l-1 2h0l1 2h-1v-1c-2 1-2 2-3 2h-2l-1-1c-1-1-2-2-1-3 0 1 1 1 2 1h0c0-1-1-2-2-3v1c-1-1-1-2-2-3h0v-1c2 0 2 0 3-1l2-2 1 2h3l1-1v-7l1-1-1-2-1-2 1-2v-11c0-3 0-7 1-10v4c1-1 2-3 2-5z" class="q"></path><path d="M491 405h0v-1c2 0 2 0 3-1l2-2 1 2h3l1 1c0 2 0 3-1 4s-2 1-4 1c-1-1-1-1-1-2-1-1-1 0-1-1-2 0-2 0-3-1z" class="AB"></path><path d="M502 408h0c2 2 1 6 1 9 0 1 1 3 1 4v20c-1-2 0-4 0-6-1-1-2-2-3-4 0-2 1-4 1-6v-17z" class="E"></path><path d="M502 425c2 3 2 7 2 10-1-1-2-2-3-4 0-2 1-4 1-6z" class="f"></path><path d="M504 366v3c-1 3 1 8-1 11h0c0 2-1 5 0 7v4 1c0-1 0-1-1-2 0-1-1-1-1-2v-11c0-3 0-7 1-10v4c1-1 2-3 2-5z" class="t"></path><path d="M499 390h1l1 2 1 2-1 1v7l-1 1h-3l-1-2-2 2c-1 1-1 1-3 1v1h0c1 1 1 2 2 3v-1c1 1 2 2 2 3h0c-1 0-2 0-2-1-1 1 0 2 1 3l1 1h2c1 0 1-1 3-2v1h1l-1-2h0l1-2h1v17c0 2-1 4-1 6 0 3 0 6 1 8l-1 2c0-2 0-3-1-5h0 1v-8c0-2 0-4-1-6h0v-3c-2-1-3-1-4-1-2-4-8-6-11-9l-1-1c-2 0-2 0-4 1l-1-1-8 5-2 1-10 6-6 3h0c0-1 1-1 2-2l6-3 12-8c1 0 2-1 2-2l-2-3h1c1 1 1 1 2 3h1l7-4 1-1 4-2-2-2c1 0 2 0 3-1l2-2h2 0l-1-1v-2l2-1 1-1 1-1h2 0z" class="AR"></path><path d="M499 390h1l1 2-1 8-1 1-2-2c-1 0-9 4-10 5l-1 1c1 1 1 2 3 3l1 1h-1l-5-3-5 2-8 5-2 1-10 6-6 3h0c0-1 1-1 2-2l6-3 12-8c1 0 2-1 2-2l-2-3h1c1 1 1 1 2 3h1l7-4 1-1 4-2-2-2c1 0 2 0 3-1l2-2h2 0l-1-1v-2l2-1 1-1 1-1h2 0z" class="An"></path><path d="M496 391l1-1h2c-1 5-4 8-8 10-1 1 0 0-2 1l-2-2c1 0 2 0 3-1l2-2h2 0l-1-1v-2l2-1 1-1z" class="AH"></path><path d="M501 431c1 2 2 3 3 4 0 2-1 4 0 6v23 16 9l1 26c0 3-1 8 0 10v2c-1 1-2 2-2 3v2 3h0c-2 3-1 7-1 11h-1l-1-27h-1v-3 1h-1v2s-1-7-1-8c-1-5-2-9-4-14l1-1c-1-2-2-6-3-9l1-1 2 4h1c2-3 1-9 1-13h1l2-8c0-2 1-5 1-7 0-1 1-2 1-3v-3-1-10l-1-1v-3h1l1-2c-1-2-1-5-1-8z" class="C"></path><path d="M501 431c1 2 2 3 3 4 0 2-1 4 0 6v23c0-3 0-5-1-7v-2-5c-1-1-1-1-1-2h1v-8c0 2 0 4-1 6 0 3 1 8-1 10v-1-10l-1-1v-3h1l1-2c-1-2-1-5-1-8zm-1 31c0-1 1-2 1-3l-1 60h-1v-3 1h-1v2l-1-8c-1-5-2-9-4-14l1-1c-1-2-2-6-3-9l1-1 2 4h1c2-3 1-9 1-13h1l2-8c0-2 1-5 1-7z" class="B"></path><path d="M500 462c0 5 0 10-1 15-1 1-1 2-1 3v1 7 18c-1-3-2-7-4-10-1-2-2-6-3-9l1-1 2 4h1c2-3 1-9 1-13h1l2-8c0-2 1-5 1-7z" class="u"></path><path d="M499 336h0c0 1 0 1-1 2 1 0 1 1 1 1h3c3 1 3 1 5 3l3-2 1 1v1 3 1 17 35 20 7h-1v-2h0c-1 3-1 5-1 8l1 1c1 4 0 7 0 11h-1l-1 5-1-1v-6l-1 1c-1-1-1-2-1-3v-1c-1-10 0-22 0-33 0-3 0-8-1-10v-26-3c0 2-1 4-2 5v-4l-2-15 1-1v-4l1-1c-1 0-1 0-1-1h3v-3h-2c-2-1-4-2-6-2l-3-2 6-2z" class="r"></path><path d="M507 342l3-2 1 1v1 3 1c-2 3-1 9-1 13v27c0 2 0 1-1 2v-21h0v-14c-1-4 0-8-2-11z" class="AD"></path><path d="M501 351v-4l1-1c-1 0-1 0-1-1h3c0 7-1 15 0 21 0 2-1 4-2 5v-4l-2-15 1-1z" class="R"></path><path d="M499 336h0c0 1 0 1-1 2 1 0 1 1 1 1h3c3 1 3 1 5 3 2 3 1 7 2 11v14h0l-1 1h0v22c0 2 1 3 0 4 0 3 0 5-1 7-1-2 0-3 0-5-2-8-1-18-1-26v-16-8-2l-2-2h0-2c-2-1-4-2-6-2l-3-2 6-2z" class="AF"></path><path d="M505 438l1-1c1-1 0-4 0-5v-18-6c1-1 1-4 1-5v-1h1c1 1 0 1 1 2 1-2 2-4 2-6v20 7h-1v-2h0c-1 3-1 5-1 8l1 1c1 4 0 7 0 11h-1l-1 5-1-1v-6l-1 1c-1-1-1-2-1-3v-1z" class="AV"></path><path d="M507 441c1-5-1-11 0-16h1c0 2 0 4 1 6l1 1c1 4 0 7 0 11h-1l-1 5-1-1v-6z" class="AC"></path><path d="M510 432c1 4 0 7 0 11h-1l1-11z" class="H"></path><path d="M516 438v-2l1-1v-2c1-4 0-7 0-11l1-11 1-1c0 2 1 5 0 7v3h1l1 10-1 4h1c0 2 0 2 2 4v2h0v10 15c0 2-1 5-1 7h-1v13h-1c0 2-1 15-1 16 1 1 0 2 0 2l1 1-3 2v5l1 22-1 31v13 2c1 3 1 8-2 12v4 3h-1v1c1 2 0 4 0 6v1h-1c0-1 1-2 1-2-1-1 0-2 0-3l-1-1h0l-1 5v1l1 1h-9c-2 0-4-1-6-1l1-1c-1-3-1-6 0-9 0-1 1-2 1-4 0-4 1-8 1-13-1 1-1 3-1 5 0 1-1 2-2 3v-7-1c2-2 2-3 2-6l-2-2v-3h0l1-3c0-1 0-2 1-3 0-2 1-3 1-5v-11h1c0-4-1-8 1-11h0v-3-2c0-1 1-2 2-3v-2 13c0 1 0 3 1 4v-5l1-1v-6c0-1 1-1 1-2l-1-2c0-1 1-3 2-4v-9c1 2 1 7 1 10v-8-18-34c-1-3-1-6 0-9-1-4-1-8 0-11 0-4 1-7 0-11l-1-1c0-3 0-5 1-8h0v2h1v-7 6l2 1v6c0 1 1 1 0 3l1 1c0-2 0-3 1-4v8l1-1z" class="c"></path><path d="M516 538c0 7-2 37 1 41 1 3 1 8-2 12v-20l1-33z" class="E"></path><path d="M512 502c1-2 1-4 1-5 0-5-1-10 0-14 0-1 0-1 1-2l1 3 1 20v24c0 2 0 5-1 7h0v-12c-1-1-1-2-2-2-1 1-1 5-1 7v-26z" class="AB"></path><path d="M512 528c0-2 0-6 1-7 1 0 1 1 2 2v12h0l1 3-1 33h0c-1-3 1-6-1-9-1 0-1 1-1 1-1 1 0 3 0 4 0 2-1 4-1 7v2c-1-1 0-4 0-6v-15-27z" class="B"></path><path d="M511 483h1v19 26 27 15c0 2-1 5 0 6v3c-1 0-1 1-2 2v-14c-1-2-1-4-2-6v-10c-1-2-1-5-1-8l-1-1v-5l1-1v-6c0-1 1-1 1-2l-1-2c0-1 1-3 2-4v-9c1 2 1 7 1 10v-8-18c1-3 1-6 1-9h0v8-13z" class="T"></path><path d="M509 513c1 2 1 7 1 10v44c-1-2-1-4-2-6v-10c-1-2-1-5-1-8l-1-1v-5l1-1v-6c0-1 1-1 1-2l-1-2c0-1 1-3 2-4v-9z" class="t"></path><path d="M506 537l1-1v-6c0-1 1-1 1-2l-1-2c0-1 1-3 2-4l-1 29c-1-2-1-5-1-8l-1-1v-5z" class="T"></path><path d="M511 418v6l2 1v6c0 1 1 1 0 3l1 1c0-2 0-3 1-4v8 45l-1-3c-1 1-1 1-1 2-1 4 0 9 0 14 0 1 0 3-1 5v-19h-1v13-8h0c0 3 0 6-1 9v-34c-1-3-1-6 0-9-1-4-1-8 0-11 0-4 1-7 0-11l-1-1c0-3 0-5 1-8h0v2h1v-7z" class="j"></path><path d="M511 418v6 25c0 2 0 3-1 5-1-4-1-8 0-11 0-4 1-7 0-11l-1-1c0-3 0-5 1-8h0v2h1v-7z" class="r"></path><path d="M511 449v34 13-8h0c0 3 0 6-1 9v-34c-1-3-1-6 0-9 1-2 1-3 1-5z" class="X"></path><path d="M514 435c0-2 0-3 1-4v8 45l-1-3c-1 1-1 1-1 2-1 4 0 9 0 14 0 1 0 3-1 5v-19c0-7-1-14 1-20h0v-3c0-4 0-9-1-13l1-16c0 1 1 1 0 3l1 1z" class="E"></path><path d="M513 431c0 1 1 1 0 3l1 1v6l-1 17c0 2 0 4 1 6v4l-1-1v-4h0v-3c0-4 0-9-1-13l1-16z" class="AR"></path><path d="M516 438v-2l1-1v-2c1-4 0-7 0-11l1-11 1-1c0 2 1 5 0 7v3h1l1 10-1 4h1c0 2 0 2 2 4v2h0v10 15c0 2-1 5-1 7h-1v13h-1c0 2-1 15-1 16 1 1 0 2 0 2l1 1-3 2v5 15h-1v-16-6l-1-20v-45l1-1z" class="r"></path><path d="M518 456c0-1 0-4 1-6v18h1v-2h0v19c0 2-1 15-1 16 1 1 0 2 0 2l1 1-3 2 1-50z" class="l"></path><path d="M516 438v-2l1-1v-2c1-4 0-7 0-11l1-11 1-1c0 2 1 5 0 7v3h1l1 10-1 4h1c0 2 0 2 2 4v2h0v10 15c0 2-1 5-1 7h-1v13h-1v-19h0v2h-1v-18c-1 2-1 5-1 6 0-3 1-18 0-20l-1 1v22h-1v-21z" class="AG"></path><path d="M520 434h1c0 2 0 2 2 4v2h0l-3 2v-8z" class="r"></path><path d="M523 440v10 15c0 2-1 5-1 7h-1l-1-30 3-2z" class="AF"></path><path d="M505 525v13c0 1 0 3 1 4l1 1c0 3 0 6 1 8v10c1 2 1 4 2 6v14c1-1 1-2 2-2v-3-2c0-3 1-5 1-7 0-1-1-3 0-4 0 0 0-1 1-1 2 3 0 6 1 9h0v20 4 3h-1v1c1 2 0 4 0 6v1h-1c0-1 1-2 1-2-1-1 0-2 0-3l-1-1h0l-1 5v1l1 1h-9c-2 0-4-1-6-1l1-1c-1-3-1-6 0-9 0-1 1-2 1-4 0-4 1-8 1-13-1 1-1 3-1 5 0 1-1 2-2 3v-7-1c2-2 2-3 2-6l-2-2v-3h0l1-3c0-1 0-2 1-3 0-2 1-3 1-5v-11h1c0-4-1-8 1-11h0v-3-2c0-1 1-2 2-3v-2z" class="v"></path><path d="M504 586l2-1 1 2v2c0 3 0 6-1 10v1l-2-1c1-4 0-9 0-13z" class="F"></path><path d="M499 605c1-1 1-1 1-2 0-2 0-1 1-2h1l1-1c1 1 1 2 1 3l1 3-1 1c-2 0-4-1-6-1l1-1z" class="AY"></path><path d="M505 562v2h1v-6h1v7c-1 2-1 4-1 6v14l-2 1 1-24z" class="X"></path><path d="M507 589l2 1c0 2 0 4 1 6v1l2-1c0 3-1 6 0 9v1l1 1h-9l1-1-1-3v-4l2 1v-1c1-4 1-7 1-10z" class="C"></path><path d="M509 600c0 2 0 5 1 6h2l1 1h-9l1-1h1c1-1 1-2 1-3h1l1-3z" class="v"></path><path d="M510 597l2-1c0 3-1 6 0 9v1h-2c-1-1-1-4-1-6 1-1 0 0 0-1 0-2 0-1 1-2z" class="U"></path><path d="M507 565h1v-4c1 2 1 4 2 6v14c1-1 1-2 2-2v17l-2 1v-1c-1-2-1-4-1-6l-2-1v-2l-1-2v-14c0-2 0-4 1-6z" class="z"></path><path d="M507 587h0c2-4 2-9 2-13 1 4 0 8 1 13v9c-1-2-1-4-1-6l-2-1v-2z" class="V"></path><path d="M512 576v-2c0-3 1-5 1-7 0-1-1-3 0-4 0 0 0-1 1-1 2 3 0 6 1 9h0v20 4 3h-1v1c1 2 0 4 0 6v1h-1c0-1 1-2 1-2-1-1 0-2 0-3l-1-1h0l-1 5c-1-3 0-6 0-9v-17-3z" class="I"></path><path d="M505 525v13c0 1 0 3 1 4l1 1c0 3 0 6 1 8v10 4h-1v-7h-1v6h-1v-2l-1-1h0v5h0l-1-1c0 1 0 3 1 4l-1 1-2 6v3c-1 1-1 3-1 5 0 1-1 2-2 3v-7-1c2-2 2-3 2-6l-2-2v-3h0l1-3c0-1 0-2 1-3 0-2 1-3 1-5v-11h1c0-4-1-8 1-11h0v-3-2c0-1 1-2 2-3v-2z" class="D"></path><path d="M503 535v14 12 2l-2 2c1-3 1-3 0-5 0-2 1-4 1-5 0-3-1-6 0-9 0-4-1-8 1-11z" class="Q"></path><path d="M501 546h1c-1 3 0 6 0 9 0 1-1 3-1 5 1 2 1 2 0 5v11 3c-1 1-1 3-1 5 0 1-1 2-2 3v-7-1c2-2 2-3 2-6l-2-2v-3h0l1-3c0-1 0-2 1-3 0-2 1-3 1-5v-11z" class="f"></path><path d="M499 565c0-1 0-2 1-3 0 2 0 3 1 5 0 2-1 4-1 6l-2-2v-3h0l1-3z" class="N"></path><path d="M505 525v13c0 1 0 3 1 4l1 1c0 3 0 6 1 8v10 4h-1v-7h-1v6h-1v-2l-1-1c1-4 1-8 1-11v-23-2z" class="AR"></path><path d="M518 335c2 0 3-1 5-1l1 2c2 0 3 0 5 2 0 0 1 0 1 1-1 1-2 1-3 2-1 0-1-1-2-2l-1 3c0 1 0 1 2 2v4h0c0 2 0 3 1 4 3 2 6 3 10 3-1 2-3 3-5 5l-1 5h1c0 1-1 2-2 3l2 1v3h0c-2 2-3 4-3 6 0 1 1 1 1 2h1c-1 2-1 3-1 5h0l1 1v1l2 2c1-1 3-1 4-1l1 1c-1 1-1 1-1 2 1 0 1 1 2 0 2-1 4-3 6-4 1 1 1 1 3 1 1-1 3-2 4-3l1-1c3-2 7-7 11-6h1 0c2 0 3 0 4 1 0 1 0 1-1 2h0c-1 1-2 2-4 3-3 3-8 7-9 11l-1 1v4 2 2 1c1 0 1-1 2-1h1l1 1-2 1v1l5-1v1c-2 2-6 4-8 6v1h1c1-1 1-1 2-1s1 0 2-1c1 0 3-1 5-1l-4 2 1 1c-1 1-2 1-3 1l-2 1-6 2 1 1c1 0 3-1 4 0-1 1-2 2-4 3 0-1-1-2-2-2h-2-1c0-2 1-2 1-4-2 2-3 3-3 5h0l1 2c0 1 1 1 2 2v1l-1 1c-3-2-5-3-7-5 1 2 1 2 3 4 1 0 3 1 4 2l-1 1h-4c-1-1-1-1-2-1h-1l1 1h-4v1c1 0 1 0 2 1-4 0-6 0-10 3h0v1l-2-2h0c0 2 0 5-1 7v-2c-2-2-2-2-2-4h-1l1-4-1-10h-1v-3c1-2 0-5 0-7l-1 1-1 11c0 4 1 7 0 11v2l-1 1v2l-1 1v-8c-1 1-1 2-1 4l-1-1c1-2 0-2 0-3v-6l-2-1v-6-20-35-17-1-3-1c1-1 1-2 2-3 1 1 2 1 2 1h3l2-1-2-1v-2h0z" class="Af"></path><path d="M524 342c0 1 0 1 2 2v4c-1 1-2 2-3 2 0-2 0-6 1-8z" class="AH"></path><path d="M523 388c1 3 1 5 2 7l1 1h1c0-1 0-1 1-1 1-1 3-1 4-1l-3 3c-1 0-2 1-3 2h-1l-2-1v-10z" class="z"></path><path d="M523 415l1-1h1 0v1 5c0 1 0 1 1 2 0 0 0 1 1 1-1 1-1 2-1 4-1 1-1 2-1 4l1 3h0v1l-2-2h0c0 2 0 5-1 7v-2-23z" class="V"></path><path d="M523 406l2-2c1 1 1 0 1 1l3 3c2 1 3 2 5 2h1 0l3 1-2 2h-2-1c-3 2-3 3-4 6-1 1-2 2-2 4h0c-1 0-1-1-1-1-1-1-1-1-1-2v-5-1h0-1l-1 1v-9z" class="f"></path><path d="M526 422c0-3 0-5 1-7 2-1 4-1 6-2-3 2-3 3-4 6-1 1-2 2-2 4h0c-1 0-1-1-1-1z" class="B"></path><path d="M540 399c2-1 4-2 5-3 2 0 2 1 3 2-1 1-2 1-3 2h0c-1 2-1 2-3 3l-1 1c0 1 0 1 1 1v1c1 0 3 0 4 1-1 0-2 1-3 1h-2l-2 1h-2c-1 0-1 1-2 1h0-1c-2 0-3-1-5-2l-3-3c0-1 0 0-1-1l-2 2v-8l2 1h1l2 1v1c0 1 1 2 2 3 1 0 1 0 2-1l1 1 1-1c3-1 4-3 6-4z" class="F"></path><path d="M540 399c2-1 4-2 5-3 2 0 2 1 3 2-1 1-2 1-3 2h0c-2 2-6 2-7 4h-1 0c1-1 2-3 3-5z" class="X"></path><path d="M552 385h3v1s-1 2-2 2c-2 2-3 2-5 5l-1 1-2 2c-1 1-3 2-5 3s-3 3-6 4l-1 1-1-1c-1 1-1 1-2 1-1-1-2-2-2-3v-1l-2-1c1-1 2-2 3-2l3-3h0c1 0 2-1 3-1l2-2c1 0 1 1 2 0 2-1 4-3 6-4 1 1 1 1 3 1 1-1 3-2 4-3z" class="N"></path><path d="M528 401v-1h2 0c1-1 1-1 2-1l2-2c1-1 3-1 4-1v2c-1 1-3 2-3 4l-1 1-1 1-1-1c-1 1-1 1-2 1-1-1-2-2-2-3z" class="V"></path><path d="M552 385h3v1s-1 2-2 2c0-1-1-1-1-1-3 1 0 0-2 1-2 0-3 1-5 2-1 0-1 1-2 2l-5 1c-1 0-2 1-2 1-2 1-4 2-5 3-2 1-3 2-3 3l-2-1c1-1 2-2 3-2l3-3h0c1 0 2-1 3-1l2-2c1 0 1 1 2 0 2-1 4-3 6-4 1 1 1 1 3 1 1-1 3-2 4-3z" class="q"></path><path d="M543 392c1-1 1-2 2-2 2-1 3-2 5-2 2-1-1 0 2-1 0 0 1 0 1 1-2 2-3 2-5 5l-1 1-2 2c-1 1-3 2-5 3s-3 3-6 4l1-1c0-2 2-3 3-4l5-3c1-1 0-2 0-3z" class="M"></path><path d="M552 385l1-1c3-2 7-7 11-6h1 0c2 0 3 0 4 1 0 1 0 1-1 2h0c-1 1-2 2-4 3-3 3-8 7-9 11l-1 1v4 2c-2 2-3 4-6 4v-1l-2 2c-1-1-3-1-4-1v-1c-1 0-1 0-1-1l1-1c2-1 2-1 3-3h0c1-1 2-1 3-2-1-1-1-2-3-2l2-2 1-1c2-3 3-3 5-5 1 0 2-2 2-2v-1h-3z" class="AG"></path><path d="M545 396l2-2 3 2c0 1 0 1-1 1v2h-1v-1c-1-1-1-2-3-2z" class="H"></path><path d="M542 405c2-1 3-2 5-3l2-1c0 1 1 1 1 2s-1 2-2 2l-2 2c-1-1-3-1-4-1v-1z" class="X"></path><path d="M552 385l1-1c3-2 7-7 11-6h1 0c2 0 3 0 4 1 0 1 0 1-1 2h0c-1 0-2 0-3 1-2 1-5 6-7 6v-1c-1 0-1 0-2 1-2 2-5 4-8 5 2-3 3-3 5-5 1 0 2-2 2-2v-1h-3z" class="AW"></path><path d="M526 348h0c0 2 0 3 1 4 3 2 6 3 10 3-1 2-3 3-5 5l-1 5h1c0 1-1 2-2 3l2 1v3h0c-2 2-3 4-3 6 0 1 1 1 1 2h1c-1 2-1 3-1 5h0l1 1v1l2 2c1-1 3-1 4-1l1 1c-1 1-1 1-1 2l-2 2c-1 0-2 1-3 1h0c-1 0-3 0-4 1-1 0-1 0-1 1h-1l-1-1c-1-2-1-4-2-7v-30-8c1 0 2-1 3-2z" class="AL"></path><path d="M527 357l1 2v2l1 1 2-1-1 1v4l-1 1c0 1 0 1-1 2s-1 1-2 0c0-4 0-8 1-12z" class="t"></path><path d="M526 348h0c0 2 0 3 1 4 3 2 6 3 10 3-1 2-3 3-5 5l-1 1-2 1-1-1v-2l-1-2v-2c0-1-1-2-1-2h-1c-1 2-1 3-2 5h0v-8c1 0 2-1 3-2z" class="E"></path><path d="M532 360l-1 5h1c0 1-1 2-2 3l2 1v3h0c-2 2-3 4-3 6 0 1 1 1 1 2-1 1-1 2-1 4h-2v1c-1-2-1-5-1-7v-9c1 1 1 1 2 0s1-1 1-2l1-1v-4l1-1 1-1z" class="AW"></path><path d="M530 368l2 1v3h0c-2 2-3 4-3 6 0 1 1 1 1 2-1 1-1 2-1 4h-2c0-5 0-9 2-13l1-3z" class="AB"></path><path d="M525 395c3-2-1-13 1-17 0 2 0 5 1 7v-1h2c0-2 0-3 1-4h1c-1 2-1 3-1 5h0l1 1v1l2 2c1-1 3-1 4-1l1 1c-1 1-1 1-1 2l-2 2c-1 0-2 1-3 1h0c-1 0-3 0-4 1-1 0-1 0-1 1h-1l-1-1z" class="E"></path><path d="M527 384h2 0v3c2 2 3 2 4 4 0 1-1 2-2 3-1 0-2 0-3-1s-1-3-1-4v-4-1z" class="B"></path><path d="M554 402v2 1c1 0 1-1 2-1h1l1 1-2 1v1l5-1v1c-2 2-6 4-8 6v1h1c1-1 1-1 2-1s1 0 2-1c1 0 3-1 5-1l-4 2 1 1c-1 1-2 1-3 1l-2 1-6 2 1 1c1 0 3-1 4 0-1 1-2 2-4 3 0-1-1-2-2-2h-2-1c0-2 1-2 1-4-2 2-3 3-3 5h0l1 2c0 1 1 1 2 2v1l-1 1c-3-2-5-3-7-5 1 2 1 2 3 4 1 0 3 1 4 2l-1 1h-4c-1-1-1-1-2-1h-1l1 1h-4v1c1 0 1 0 2 1-4 0-6 0-10 3l-1-3c0-2 0-3 1-4 0-2 0-3 1-4h0c0-2 1-3 2-4 1-3 1-4 4-6h1 2l2-2-3-1c1 0 1-1 2-1h2l2-1h2c1 0 2-1 3-1l2-2v1c3 0 4-2 6-4z" class="l"></path><path d="M546 407l2-2v1c-1 1-2 1-2 3l-2 1h-1c-1 1-3 2-4 3-3 2-6 3-9 6-1 1-1 3-3 4 0-2 1-3 2-4 1-3 1-4 4-6h1 2l2-2-3-1c1 0 1-1 2-1h2l2-1h2c1 0 2-1 3-1z" class="M"></path><path d="M538 422v-4c1-1 2-1 3-2 1 0 2 0 2-1 2-3 5-3 8-4-1 2-3 4-5 5-2 2-3 3-3 5h0l1 2c0 1 1 1 2 2v1l-1 1c-3-2-5-3-7-5z" class="D"></path><path d="M526 427v-1c1 0 1-1 2-2s1-2 3-2c2-1 4-3 6-5-1 2-1 3-2 5 1 2 1 2 2 3 0 1 1 1 1 2 2-1 3 0 4 1 1 0 2 0 2 1h-4c-1-1-1-1-2-1h-1l1 1h-4v1c1 0 1 0 2 1-4 0-6 0-10 3l-1-3c0-2 0-3 1-4z" class="R"></path><path d="M538 427c2-1 3 0 4 1 1 0 2 0 2 1h-4c-1-1-1-1-2-1h-1l1 1h-4v1c1 0 1 0 2 1-4 0-6 0-10 3l-1-3c1-2 3-2 4-3h1c2-1 3-1 4-1h3 1z" class="c"></path><path d="M518 335c2 0 3-1 5-1l1 2c2 0 3 0 5 2 0 0 1 0 1 1-1 1-2 1-3 2-1 0-1-1-2-2h0 0l-3 1c0 1-1 3-1 5v28 23 38h-1l1-4-1-10h-1v-3c1-2 0-5 0-7l-1 1-1 11c0 4 1 7 0 11v2l-1 1v2l-1 1v-8c-1 1-1 2-1 4l-1-1c1-2 0-2 0-3v-6l-2-1v-6-20-35-17-1-3-1c1-1 1-2 2-3 1 1 2 1 2 1h3l2-1-2-1v-2h0z" class="AC"></path><path d="M521 396h-2 0c1-5 0-10 0-14 0-2 1-4 0-6 0-1 0-2 1-3h1v23z" class="AS"></path><path d="M517 346h2c1-1 2-2 2-4-1-1-1 0-1-2h2c0 1-1 3-1 5 0 4 1 10-1 14h-1v-3c0-1-2-1-2-2v-8z" class="H"></path><path d="M518 335c2 0 3-1 5-1l1 2c2 0 3 0 5 2 0 0 1 0 1 1-1 1-2 1-3 2-1 0-1-1-2-2h0 0l-3 1h-2c0 2 0 1 1 2 0 2-1 3-2 4h-2c0-2 0-3-1-5l2-2 2-1-2-1v-2h0z" class="e"></path><path d="M518 335c2 0 3-1 5-1l1 2-4 2-2-1v-2h0z" class="S"></path><path d="M513 338c1 1 2 1 2 1h3l-2 2-1 2v52 36c-1 1-1 2-1 4l-1-1c1-2 0-2 0-3v-6l-2-1v-6-20-35-17-1-3-1c1-1 1-2 2-3z" class="q"></path><path d="M513 338c1 1 2 1 2 1h3l-2 2-1 2c0 2-1 4-1 6h1v1l-1 1v-1c0-1 0-1-1-2h0-1v1c1 1 0 2 0 3v-1c0-1 0-5-1-6v-3-1c1-1 1-2 2-3z" class="R"></path><path d="M513 338c1 1 2 1 2 1v1c-1 0-1 1-1 2l-1 1-2-1v-1c1-1 1-2 2-3z" class="V"></path><path d="M511 363c2 2 0 27 1 33 1 2 1 5 1 8h0c1 2 1 6 0 8h0v1h1c0-1 0-2 1-4h-1v-9c1-2 0-3 1-5h0v36c-1 1-1 2-1 4l-1-1c1-2 0-2 0-3v-6l-2-1v-6-20-35z" class="H"></path><path d="M480 409c2-1 2-1 4-1l1 1c3 3 9 5 11 9 1 0 2 0 4 1v3h0c1 2 1 4 1 6v8h-1 0c1 2 1 3 1 5h-1v3l1 1v10 1 3c0 1-1 2-1 3 0 2-1 5-1 7l-2 8h-1c0 4 1 10-1 13h-1l-2-4c-1-2-2-1-2-4v-2l-1 1-4-2h-1l-2-1c-4-3-9-6-13-10l-2-2-1 1-3-4c-1-1-2-1-3-3l-6-6c-1-1-3-2-3-4-1-2-3-2-3-4-1-1-2-1-3-3l1-1-4-4 2-2v-3h-1c-1 0-2-1-3-2-1 0-1-1-2-1-1-1-1-1-1-2h1 7 0c1-1 1-1 3 0h2 1 1v-1c0-1 1-1 2-1h0v-3h-1l6-3 10-6 2-1 8-5 1 1z" class="v"></path><path d="M475 466h1v-1-2-1l2 2h0l-1 1c1 1 2 3 2 3v2s1 2 2 2l2 4c0 1 0 2-1 2-4-3-9-6-13-10h3c3 2 4 7 8 7 0-2-2-4-3-5 0-2-1-3-2-4z" class="f"></path><path d="M463 447c3 0 5 0 8-1l3-1h1c2 2 5 3 7 4 1 0 2 1 3 2l-1 2c-2-2-3-3-5-4-4-2-11-1-15 1l-1-1h1l1-1h1-3-1l1-1z" class="c"></path><path d="M478 464c1 1 3 3 5 3v-1l-1-1 1-1h0 1l-3-4h1l6 6-1 1v1c1 1 1 2 1 3l-1 1-1-1c-2-1-3-1-5-1h-1l1 2c-1 0-2-2-2-2v-2s-1-2-2-3l1-1z" class="R"></path><path d="M475 445l2-1c3-1 6-2 9-2l1 1v6c0 2 2 4 2 5v2c-1-1-2-1-3-2l-2-1 1-2c-1-1-2-2-3-2-2-1-5-2-7-4z" class="T"></path><path d="M454 454c-1-1-3-2-3-4-1-2-3-2-3-4-1-1-2-1-3-3l1-1 2 3 2 2 3 4c3-1 7-3 9-3h1 3-1l-1 1h-1l1 1-8 4c4 4 8 8 11 12l-1 1-3-4c-1-1-2-1-3-3l-6-6z" class="e"></path><path d="M442 438l2-2c1 1 1 2 2 4h1l3 2c2 2 3 3 5 3 1 0 3 1 4 1l4 1h0l-1 1c-2 0-6 2-9 3l-3-4-2-2-2-3-4-4z" class="I"></path><path d="M448 445v-2h2l4 3v1c-2 1-2 0-4 0l-2-2z" class="a"></path><path d="M442 438l2-2c1 1 1 2 2 4h1l3 2v1h-2v2l-2-3-4-4z" class="m"></path><path d="M481 472l-1-2h1c2 0 3 0 5 1l1 1 1-1c0-1 0-2-1-3v-1l1-1c2 3 3 6 4 10v1c0 2-1 3-2 5v-2l-1 1-4-2h-1l-2-1c1 0 1-1 1-2l-2-4z" class="E"></path><path d="M484 479l1-1v-2c0-1 1-1 1-2 1 1 1 1 2 1 2 1 3 1 4 2 0 2-1 3-2 5v-2l-1 1-4-2h-1z" class="z"></path><path d="M459 439l-1-1 1-1h0l4 1c1 1 2 2 4 2 1 0 3 1 4 2s3 2 4 3h-1l-3 1c-3 1-5 1-8 1h0l-4-1c-1 0-3-1-4-1l1-1h1v-1h-1l1-1h1c1-1 1-2 1-3h0z" class="M"></path><path d="M459 439c2 0 2 0 3 1v1c-1 0-1 1-1 2-2 0-2 0-3-1 1-1 1-2 1-3h0z" class="E"></path><path d="M459 446l1-1c3 0 6-3 9-2h1l1-1c1 1 3 2 4 3h-1l-3 1c-3 1-5 1-8 1h0l-4-1z" class="q"></path><defs><linearGradient id="Aj" x1="469.474" y1="454.848" x2="481.203" y2="464.76" xlink:href="#B"><stop offset="0" stop-color="#460000"></stop><stop offset="1" stop-color="#710b0a"></stop></linearGradient></defs><path fill="url(#Aj)" d="M477 470h0v1h-2v-3l-2-2c-1-2-3-4-5-5-1-2-1-4 0-5 1-2 3-2 6-3l8 7h-1l3 4h-1 0l-1 1 1 1v1c-2 0-4-2-5-3h0l-2-2v1 2 1h-1c1 1 2 2 2 4z"></path><path d="M475 466c-1-1-2-3-2-4l1-1c1 0 1 0 2 1v1 2 1h-1z" class="U"></path><path d="M492 430v1l-3 1v1l-2 1-1 2h3l-2 3v4l-1-1c-3 0-6 1-9 2l-2 1h-1 1c-1-1-3-2-4-3s-3-2-4-2c-2 0-3-1-4-2l-4-1c1-1 1-1 2-1h0v-1l-1-1 1-1c1 0 2 1 3 1h3c2 1 9 1 12 1 1-1 1-1 2-1h1s1-1 2-1 2 0 4-1v-1l1 1 1-2h2z" class="c"></path><path d="M486 436h3l-2 3v4l-1-1c-3 0-6 1-9 2l-2 1h-1 1c-1-1-3-2-4-3s-3-2-4-2c-2 0-3-1-4-2 2 0 4 0 6 1h3c1 1 3 0 4 0 1 1 4 1 6 1 1-1 1-1 2-1l1-1c0-1 0-1 1-2h0z" class="l"></path><path d="M463 438c2 0 4 0 6 1h0c2 1 4 1 6 2l1 1c1 0 1 0 1 1s-1 1-2 2c-1-1-3-2-4-3s-3-2-4-2c-2 0-3-1-4-2z" class="AW"></path><path d="M454 423l1 1c2 2 4 4 7 6v2l-1 1h0l-1 1 1 1v1h0c-1 0-1 0-2 1h0l-1 1 1 1h0c0 1 0 2-1 3h-1l-1 1h1v1h-1l-1 1c-2 0-3-1-5-3l-3-2h-1c-1-2-1-3-2-4v-3h-1c-1 0-2-1-3-2-1 0-1-1-2-1-1-1-1-1-1-2h1 7 0c1-1 1-1 3 0h2 1 1v-1c0-1 1-1 2-1h0v-3z" class="f"></path><path d="M457 438l2 1c0 1 0 2-1 3h-1-2l2-4z" class="B"></path><path d="M444 433l3 1 1 1c0 1 1 1 2 2l1 1-2 1c-1 0-1 0-2 1h-1c-1-2-1-3-2-4v-3z" class="D"></path><path d="M447 440c1-1 1-1 2-1 2 2 3 2 6 3h2l-1 1h1v1h-1l-1 1c-2 0-3-1-5-3l-3-2z" class="E"></path><path d="M451 431c1 1 2 1 3 2 1 2 3 3 5 4l-1 1 1 1h0l-2-1c-4-1-6-3-10-4l-3-1h-1l1-1c2 0 5 1 7 2l1-1-1-2z" class="F"></path><path d="M445 428h0c1-1 1-1 3 0l-1 1c1 1 2 1 4 2l1 2-1 1c-2-1-5-2-7-2l-1 1c-1 0-2-1-3-2-1 0-1-1-2-1-1-1-1-1-1-2h1 7z" class="AA"></path><path d="M438 428h7l1 2c-2 1-3 1-4 1h-2c-1 0-1-1-2-1-1-1-1-1-1-2h1z" class="D"></path><path d="M454 423l1 1c2 2 4 4 7 6v2l-1 1h0l-1 1 1 1v1h0c-1 0-1 0-2 1h0c-2-1-4-2-5-4-1-1-2-1-3-2-2-1-3-1-4-2l1-1h2 1 1v-1c0-1 1-1 2-1h0v-3z" class="R"></path><g class="AH"><path d="M448 428h2c3 2 7 4 11 5h0l-1 1 1 1v1h0c-1 0-1 0-2 1h0c-2-1-4-2-5-4-1-1-2-1-3-2-2-1-3-1-4-2l1-1z"></path><path d="M469 414c1 1 1 2 2 2 3 2 5 3 7 6 1 0 1 1 2 2l4 5h1 1v1h1 3l-1 2-1-1v1c-2 1-3 1-4 1s-2 1-2 1h-1c-1 0-1 0-2 1-3 0-10 0-12-1h-3c-1 0-2-1-3-1h0l1-1v-2c-3-2-5-4-7-6l-1-1h-1l6-3 10-6z"></path></g><path d="M462 430h2l4 3v1h-4c-1 0-2-1-3-1h0l1-1v-2z" class="E"></path><path d="M467 426c4 2 7 5 12 5l1 1c-2 1-3 1-5 1l-2-1c-3 0-3 0-5-1-1-1-1-2-1-3 1 0 0-1 0-1v-1z" class="z"></path><path d="M467 428l6 4c-3 0-3 0-5-1-1-1-1-2-1-3z" class="H"></path><path d="M453 423l6-3h1l7 6v1s1 1 0 1c0 1 0 2 1 3l-3-2-1 1h-2c-3-2-5-4-7-6l-1-1h-1z" class="F"></path><path d="M455 424c0-1 1-2 2-2s1 1 2 2 2 1 3 2h0l3 3-1 1h-2c-3-2-5-4-7-6z" class="H"></path><path d="M469 414c1 1 1 2 2 2 3 2 5 3 7 6 1 0 1 1 2 2l4 5c-1 0-2 1-3 2h-2 0c-5 0-8-3-12-5l-7-6h-1l10-6z" class="AM"></path><path d="M480 424l4 5c-1 0-2 1-3 2l-2-1v-1l-1-1 2-2-1-1 1-1z" class="AD"></path><path d="M469 414c1 1 1 2 2 2-1 0-1 0-2 1-2 1-4 1-6 2l-3 1h-1l10-6z" class="l"></path><path d="M489 436c1-1 5-3 7-3 1 0 3 2 4 3h0c1 2 1 3 1 5h-1v3l1 1v10 1 3c0 1-1 2-1 3 0 2-1 5-1 7l-2 8h-1v-4c-1-5-2-9-5-13-2-2-4-4-5-6 1 1 2 1 3 2v-2c0-1-2-3-2-5v-6-4l2-3z" class="j"></path><path d="M496 473v-1l1-1v-4h0v-1c1-1 1-2 1-3 2-2 2-5 3-8v1 3c0 1-1 2-1 3 0 2-1 5-1 7l-2 8h-1v-4z" class="t"></path><path d="M500 436c1 2 1 3 1 5h-1v3l-1 10c-1 2-2 3-4 4h0c-1-3-1-6-1-9l1-1v-2c1-1 1-2 2-3v-1-3l3-3z" class="S"></path><path d="M495 448v-2c1-1 1-2 2-3 0 3-1 8 0 11h2c-1 2-2 3-4 4h0c-1-3-1-6-1-9l1-1z" class="R"></path><path d="M487 439h1c1 2 1 3 1 5l1-1 1-1c0 3 0 5 1 7l1-1 1 1c0 3 0 6 1 9l1 1h-1v1c-3-1-3-2-4-3s-1-1-2-1v-2c0-1-2-3-2-5v-6-4z" class="AH"></path><path d="M487 439h1c1 2 1 3 1 5l1-1 1-1c0 3 0 5 1 7v3l-2-1c-1-1-2-1-3-2v-6-4z" class="z"></path><path d="M489 436c1-1 5-3 7-3 1 0 3 2 4 3h0l-3 3v3 1c-1 1-1 2-2 3v2l-1 1-1-1-1 1c-1-2-1-4-1-7l-1 1-1 1c0-2 0-3-1-5h-1l2-3z" class="M"></path><path d="M490 439c2-1 3-2 5-2 2 2 2 3 2 5v1c-1 1-1 2-2 3v2c0-2 0-7-1-9h-4z" class="N"></path><path d="M489 436c1-1 5-3 7-3 1 0 3 2 4 3h0l-3 3v3c0-2 0-3-2-5-2 0-3 1-5 2v3 1l-1 1c0-2 0-3-1-5h-1l2-3z" class="Q"></path><path d="M480 409c2-1 2-1 4-1l1 1c3 3 9 5 11 9 1 0 2 0 4 1v3h0c1 2 1 4 1 6v8h-1c-1-1-3-3-4-3-2 0-6 2-7 3h-3l1-2 2-1v-1l3-1v-1h-2-3-1v-1h-1-1l-4-5c-1-1-1-2-2-2-2-3-4-4-7-6-1 0-1-1-2-2l2-1 8-5 1 1z" class="e"></path><path d="M496 418c1 0 2 0 4 1v3h0c-1 2-2 3-3 4-1 0-1 1-2 0s-1-1-1-2l2 1c1-1 1-1 1-2l-1-5z" class="R"></path><path d="M482 411h1c1 1 1 1 3 2 1 0 2 1 2 2v1h0l-1-1c-1 2 3 4 3 6h0c-2-1-3-2-4-4h0-2c-2-1-2-3-2-5v-1z" class="X"></path><path d="M471 413l8-5 1 1 2 2v1c-2 1-3 1-4 2-1-1-2-1-4-1h-3z" class="H"></path><path d="M469 414l2-1h3c5 4 11 7 15 11-3 0-4-1-6-3-1 0-1-1-2-1v-1c-1 0-2 0-2-1-1 0-1 0-2 1 1 1 1 1 1 3-2-3-4-4-7-6-1 0-1-1-2-2z" class="t"></path><path d="M480 409c2-1 2-1 4-1l1 1c3 3 9 5 11 9l1 5c0 1 0 1-1 2l-2-1c0-3-4-5-6-8h0v-1c0-1-1-2-2-2-2-1-2-1-3-2h-1l-2-2z" class="C"></path><path d="M478 422c0-2 0-2-1-3 1-1 1-1 2-1 0 1 1 1 2 1v1c1 0 1 1 2 1 2 2 3 3 6 3l4 4c0 1 0 1-1 2h-2-3-1v-1h-1-1l-4-5c-1-1-1-2-2-2z" class="M"></path><path d="M487 426c1 0 2 0 3 1 0 2-2 2-3 3h-1c0-2 0-3 1-4z" class="N"></path><path d="M478 422c0-2 0-2-1-3 1-1 1-1 2-1 0 1 1 1 2 1v1c1 0 1 1 2 1-1 1-2 1-3 1 2 2 3 2 5 3 1 0 2 0 2 1-1 1-1 2-1 4v-1h-1-1l-4-5c-1-1-1-2-2-2z" class="z"></path><path d="M428 423h2c1 1 3 3 4 5h0l1-1h1l1 1c0 1 0 1 1 2 1 0 1 1 2 1 1 1 2 2 3 2h1v3l-2 2 4 4-1 1c1 2 2 2 3 3 0 2 2 2 3 4 0 2 2 3 3 4l6 6c1 2 2 2 3 3l3 4 1-1 2 2c4 4 9 7 13 10l2 1h1l4 2 1-1v2c0 3 1 2 2 4l-1 1c1 3 2 7 3 9l-1 1c2 5 3 9 4 14 0 1 1 8 1 8v-2h1v-1 3h1l1 27v11c0 2-1 3-1 5-1 1-1 2-1 3l-1 3h0c-1 0-2 0-3 1 0-3-1-6-2-9-3-7-9-12-13-18h-1c-2-4-5-7-8-10-4-3-7-8-9-13 0-2-1-3-2-5-1-1-1-2-1-3s-2-3-2-3h0l-3-2c-1 0-1-4-2-5-3-9-6-18-8-27-1-1-11-34-12-39-1 0-1-2-1-2l-3-10z" class="m"></path><path d="M457 508l1-1 3 3v1c3 4 5 7 9 10l2 2c-1 1-1 2-2 2l-11-14c0-1-2-3-2-3z" class="a"></path><path d="M459 511l11 14 1 2c0 2 1 3 2 4l-2 1c-4-3-7-8-9-13 0-2-1-3-2-5-1-1-1-2-1-3z" class="U"></path><path d="M432 435h0c4 2 4 4 6 7s4 6 6 8l8 10 3 2-1 1-7-8-4-4c0-2-4-5-5-6l-1-1c0 2 2 5 2 7 1 1 1 2 0 3v2c1 2 1 3 1 4l1 1c0 2 2 5 2 8 0 1 0 1 1 2v2 1c-1-1-11-34-12-39z" class="Y"></path><path d="M454 454l6 6c1 2 2 2 3 3l3 4 1-1 2 2-1 1c0 1 1 1 2 2h0v1c1 1 1 0 1 1 0 2 2 3 3 4s2 3 2 4l1 2v2l-3-3-1 1 2 5-1 1c-4-10-11-20-19-27l-3-2c4-2 5 3 8 4v-1c-2-2-4-5-6-9z" class="l"></path><path d="M473 483l-2-4c-1-1-2-3-3-5 4 2 6 5 8 7l1 2v2l-3-3-1 1z" class="F"></path><path d="M476 481c0-1-1-3-2-4s-3-2-3-4c0-1 0 0-1-1v-1h0c-1-1-2-1-2-2l1-1c4 4 9 7 13 10l2 1h1l4 2 1-1v2c0 3 1 2 2 4l-1 1c0-1-1-2-2-3l-1 1-1-1c-1 1-1 1-1 3-1 0-2 1-2 2l-2 2v2c0 3-2 6-3 8l-1 5-1 1v-6l-3-12 1-1-2-5 1-1 3 3v-2l-1-2z" class="AM"></path><path d="M473 483l1-1 3 3v-2c1 0 2 1 2 1 1 2 1 8 2 10-1 2-2 4-2 6v1l-1 5-1 1v-6l-3-12 1-1-2-5z" class="AR"></path><path d="M473 483l1-1 3 3c1 4 1 7 1 11h-1l-2-8-2-5z" class="u"></path><path d="M428 423h2c1 1 3 3 4 5h0l1-1h1l1 1c0 1 0 1 1 2 1 0 1 1 2 1 1 1 2 2 3 2h1v3l-2 2 4 4-1 1c1 2 2 2 3 3 0 2 2 2 3 4 0 2 2 3 3 4 2 4 4 7 6 9v1c-3-1-4-6-8-4l-8-10c-2-2-4-5-6-8s-2-5-6-7h0c-1 0-1-2-1-2l-3-10z" class="M"></path><path d="M434 428l1-1h1l1 1c0 1 0 1 1 2 1 0 1 1 2 1 1 1 2 2 3 2h1v3l-2 2-8-10z" class="Y"></path><path d="M428 423h2c1 1 3 3 4 5l-1 1c0 2 2 3 3 5l4 6c2 4 5 5 4 10-2-2-4-5-6-8s-2-5-6-7h0c-1 0-1-2-1-2l-3-10z" class="P"></path><path d="M482 493v-2l2-2c0-1 1-2 2-2 0-2 0-2 1-3l1 1 1-1c1 1 2 2 2 3l3 9-1 1c2 5 3 9 4 14 0 1 1 8 1 8v-2h1v-1 3h1l1 27v11c0 2-1 3-1 5-1 1-1 2-1 3l-1 3h0c-1 0-2 0-3 1 0-3-1-6-2-9-3-7-9-12-13-18h-1c-2-4-5-7-8-10l2-1c-1-1-2-2-2-4l-1-2c1 0 1-1 2-2l-2-2 1-1v-1l1-1v-2h1c0-3 1-5 2-7 0-1 0-1 1-2h0v-2c0-1 0-2 1-4v6l1-1 1-5c1-2 3-5 3-8z" class="M"></path><path d="M477 529v-2c-1-2-1-3-1-5 0-1 0-1 1-2 2 1 1 0 3-1-1 2-1 3-2 4v5 1c1 1 1 2 1 3-1-1-1-2-2-3zm9-17h1l1-1c2-1 2-1 4-1l1 2c1 5 2 11 2 16h0c0 3 0 5-1 7 0 3 1 6 0 9v3h0l-2-2c1 0 1 0 1-1l-1-2c1 0 1-1 1-2 0-2 1-5 1-7-1-1-1-2 0-3h0c-2-4 0-8-1-11-1-1-1 0-1-1v-1l-2-2c-1-1-2-1-4-1v-1-1z" class="AA"></path><path d="M497 524c0 4 1 32-1 34h0c-3-11-14-19-22-28l1-2h1l1 1c1 1 1 2 2 3 1 2 4 4 5 6 3 2 6 5 8 7l2 2h0v-3c1-3 0-6 0-9 1-2 1-4 1-7l1 1c1-2 0-3 1-5z" class="C"></path><path d="M471 527l3 3c8 9 19 17 22 28v1h0l-1-1-2 2c-3-7-9-12-13-18h-1c-2-4-5-7-8-10l2-1c-1-1-2-2-2-4z" class="X"></path><path d="M473 531c3 2 5 5 8 8l-1 3h-1c-2-4-5-7-8-10l2-1z" class="M"></path><path d="M486 514c1 1 1 2 2 3 1 2 2 3 4 5l-1 2c1 2 1 3 1 5 1 2 1 3 1 5-1 0-2 1-2 3h0l-1 1-2-1h-1c0-4-1-3-3-4l1-2c-1-1-1-1-2-1l1-2-3-2c1-1 1-3 1-5l1 2 1-1c0-1 0-1 1-2v-1-2c0-2-1-3 0-5l1 1v1z" class="F"></path><path d="M477 501v6l1-1v1c1 0 2 1 3 0v2c1-1 1-2 2-2l1 1-1 2h0c-1 2-1 3-1 5-1 2-1 3-2 4-2 1-1 2-3 1-1 1-1 1-1 2 0 2 0 3 1 5v2l-1-1h-1l-1 2-3-3-1-2c1 0 1-1 2-2l-2-2 1-1v-1l1-1v-2h1c0-3 1-5 2-7 0-1 0-1 1-2h0v-2c0-1 0-2 1-4z" class="V"></path><path d="M472 523c1 2 2 3 3 5l-1 2-3-3-1-2c1 0 1-1 2-2z" class="D"></path><path d="M481 509c1-1 1-2 2-2l1 1-1 2h0c-1 2-1 3-1 5-1 2-1 3-2 4-2 1-1 2-3 1-1 1-1 1-1 2 0 2 0 3 1 5v2l-1-1c-1-2-1-3-1-5 0-1-1-2 0-3 0-2 1-5 3-7 0 1 0 1 1 1v-2h2v-3z" class="R"></path><path d="M497 511c0 1 1 8 1 8v-2h1v-1 3h1l1 27v11c0 2-1 3-1 5-1 1-1 2-1 3l-1 3h0c-1 0-2 0-3 1 0-3-1-6-2-9l2-2 1 1h0v-1h0c2-2 1-30 1-34l-1-13h1z" class="j"></path><path d="M493 560l2-2 1 1v1l1 1c0 2 0 4 1 6v1h0c-1 0-2 0-3 1 0-3-1-6-2-9z" class="q"></path><path d="M498 519v-2h1v-1 3h1l1 27v11c0 2-1 3-1 5-1 1-1 2-1 3l-1-46z" class="F"></path><path d="M482 493v-2l2-2c0-1 1-2 2-2 0-2 0-2 1-3l1 1 1-1c1 1 2 2 2 3l3 9-1 1c2 5 3 9 4 14h-1l1 13c-1 2 0 3-1 5l-1-1h0c0-5-1-11-2-16l-1-2c-2 0-2 0-4 1l-1 1h-1l-2-4-1-1c-1 0-1 1-2 2v-2c-1 1-2 0-3 0v-1l1-5c1-2 3-5 3-8z" class="T"></path><path d="M488 485l1-1c1 1 2 2 2 3l3 9-1 1a30.44 30.44 0 0 0-8-8l3-4z" class="AX"></path><path d="M485 492c5 6 10 11 10 19h1l1 13c-1 2 0 3-1 5l-1-1h0c0-5-1-11-2-16l-1-2c0-3-2-6-4-8-1-2-3-4-4-6v-1c-1-1-1-1-1-2l2-1z" class="a"></path><path d="M495 511h1l1 13c-1 2 0 3-1 5l-1-1h0c0-5-1-11-2-16 1 3 2 5 2 7l1-1c-2-2-1-5-1-7z" class="S"></path><path d="M482 493l2-2 1 1-2 1c0 1 0 1 1 2v1c1 2 3 4 4 6 2 2 4 5 4 8-2 0-2 0-4 1l-1 1h-1l-2-4-1-1c-1 0-1 1-2 2v-2c-1 1-2 0-3 0v-1l1-5c1-2 3-5 3-8z" class="Q"></path><path d="M481 507l2-2h1l1 1h1c1-1 1-2 1-3s0-1 1-1c2 2 4 5 4 8-2 0-2 0-4 1l-1 1h-1l-2-4-1-1c-1 0-1 1-2 2v-2z" class="f"></path><defs><linearGradient id="Ak" x1="515.399" y1="521.907" x2="532.493" y2="520.156" xlink:href="#B"><stop offset="0" stop-color="#2d0004"></stop><stop offset="1" stop-color="#4b0c06"></stop></linearGradient></defs><path fill="url(#Ak)" d="M523 440c1-2 1-5 1-7h0l2 2c-1 1-1 2-1 4h1c-1 2-1 2 0 3h1v2l1 3c1 1 2 2 2 3 0 2 2 3 3 3 0 1 1 1 2 2v1l-1 1c-1 1 0 2 0 4h0c-1 1-1 2-1 3 0 2-1 4-2 6 0 2-1 4-1 6l1 4c0 2 0 2 1 2h-1c0 2 0 3 1 4l1 1-1 3 2-1c1 1 1 2 1 3-1 1-2 3-2 5-1 2-1 4-1 6 1 1 2 1 3 2 0 1-1 4-1 5h1v-1 1c0 1 0 1 1 2h0v3l1 1c1 1 2 1 4 1l1-1 2-1c1 1 1 2 1 3l4-3c0 1-1 2-2 3h1 2 0 1c2-1 4-3 6-5 0 1-1 1-1 2-1 1-1 1-1 2 0-1 1-1 2-1 2 0 5-3 6-5 1 1 2 1 2 2v4h1c0 1 0 0 1 1h0l1-1v3c-1 1-1 2-1 3 2-2 2-4 2-7 1-1 1-2 1-2 2 3 1 7 0 10l-1 2 1 1v2c-1 1-3 6-4 7l-5 8-10 12-5 5h-1l1 1c0 2 0 2 1 3v1 9h1l-2 4c0 1 0 1-1 2-1 2-2 3-3 5-1 1-1 2-2 3 0 3-2 5-4 8-3 2-5 6-6 8l-1 1-1-1c-1 1-5 4-6 5-2 1-4 3-6 2-2 0-3 0-3-1l-2-1c1-1 1-1 1-2l1-1-1-1v-1l1-5h0l1 1c0 1-1 2 0 3 0 0-1 1-1 2h1v-1c0-2 1-4 0-6v-1h1v-3-4c3-4 3-9 2-12v-2-13l1-31-1-22v-5l3-2-1-1s1-1 0-2c0-1 1-14 1-16h1v-13h1c0-2 1-5 1-7v-15-10h0z"></path><path d="M523 440c1-2 1-5 1-7h0l2 2c-1 1-1 2-1 4h1c-1 2-1 2 0 3-1 1 0 2-1 3 0-2-1-3-1-4v7l-1 2v-10h0z" class="S"></path><path d="M527 574c0 2 1 3 3 4v2l-1 12v5l-1 1h-1l-1-18 1-6z" class="AL"></path><path d="M527 574c0 2 1 3 3 4v2l-1 2c-2 0-2-1-3-2l1-6z" class="B"></path><path d="M530 575c2 3 1 5 2 8 0 3-1 8 1 11h1c0-1 0-1 1-1l-8 8-1-2 1-1h1l1-1v-5l1-12v-2-3z" class="Y"></path><path d="M528 545l1 6h1c0 2 1 2 1 4 0 1-1 2-1 3v4l-1 4c0 2 0 3 1 4l1 1c0 1-1 3-1 4v3c-2-1-3-2-3-4v-23l1 1v-7z" class="q"></path><path d="M528 552l1 6h1v4l-1 4c-2-2-1-10-1-14z" class="F"></path><path d="M528 545l1 6h1c0 2 1 2 1 4 0 1-1 2-1 3h-1l-1-6h0v-7z" class="V"></path><path d="M521 530c1 3 1 5 1 7h0v-19c0-5 0-9 1-13v81l-1 12v-1h0c-1-4-1-9-1-13v-23c1-2 0-26 0-31z" class="l"></path><path d="M521 472h1c0-2 1-5 1-7v40c-1 4-1 8-1 13v19h0c0-2 0-4-1-7v-22c0-1-1-2-1-3v-1l-1-1s1-1 0-2c0-1 1-14 1-16h1v-13z" class="r"></path><path d="M520 485h1v23c0-1-1-2-1-3v-1l-1-1s1-1 0-2c0-1 1-14 1-16z" class="T"></path><path d="M517 577v-1c1-2 1-5 1-7v-2h1l-1 1 1 1v1 2c2 3 1 8 2 12 0 4 0 9 1 13h0v1l1-12v15 2l-1 1c-2 0-5 1-7 1v-7-3-4c3-4 3-9 2-12v-2z" class="f"></path><path d="M515 595c1 2 0 4 2 5h2 2v-1-1h0c1 1 1 2 1 2l1 1v2l-1 1c-2 0-5 1-7 1v-7-3z" class="S"></path><path d="M520 504v1c0 1 1 2 1 3v22c0 5 1 29 0 31v23c-1-4 0-9-2-12v-2-1l-1-1 1-1h-1v2c0 2 0 5-1 7v1-13l1-31-1-22v-5l3-2z" class="AR"></path><path d="M518 533c2 3 1 9 1 12s1 5 1 7c1 4-1 10 0 15v2 1h1l-1-4c0-2 0-4 1-5v23c-1-4 0-9-2-12v-2-1l-1-1 1-1h-1v2c0 2 0 5-1 7v1-13l1-31z" class="z"></path><path d="M527 452l-1-7h0l1-1 1 3c1 1 2 2 2 3 0 2 2 3 3 3 0 1 1 1 2 2v1l-1 1c-1 1 0 2 0 4h0c-1 1-1 2-1 3 0 2-1 4-2 6 0 2-1 4-1 6l1 4c0 2 0 2 1 2h-1c0 2 0 3 1 4l1 1-1 3-1 3-3 10-1-1v-1c1-8 0-17 0-24v-25z" class="E"></path><path d="M531 493c-1-1-1-1-1-2 0-2-1-4 0-6h1v-3c0 2 0 3 1 4l1 1-1 3-1 3z" class="t"></path><path d="M527 452l-1-7h0l1-1 1 3c1 1 2 2 2 3 0 2 2 3 3 3 0 1 1 1 2 2v1l-1 1c-1 1 0 2 0 4h0c-1 1-1 2-1 3 0 2-1 4-2 6 0 2-1 4-1 6h0v3c-2-2 0-5-1-7s0-7-1-10-1-6-1-10z" class="M"></path><defs><linearGradient id="Al" x1="521.75" y1="612.064" x2="523.76" y2="598.364" xlink:href="#B"><stop offset="0" stop-color="#323136"></stop><stop offset="1" stop-color="#4b5057"></stop></linearGradient></defs><path fill="url(#Al)" d="M547 575h1l-2 4c0 1 0 1-1 2-1 2-2 3-3 5-1 1-1 2-2 3 0 3-2 5-4 8-3 2-5 6-6 8l-1 1-1-1c-1 1-5 4-6 5-2 1-4 3-6 2-2 0-3 0-3-1l-2-1c1-1 1-1 1-2l1-1c6-1 10-2 14-6l8-8c-1 0-1 0-1 1h-1c-2-3-1-8-1-11l1-5c0 1 0 2 1 3 1-1 1-3 2-5v1c1 1 2 1 2 2s0 2 1 3h1c1-2 3-3 4-5l2-1h0l1-1z"></path><path d="M547 575h1l-2 4c0 1 0 1-1 2-1 2-2 3-3 5-1 1-1 2-2 3l-1 1c-4 5-8 10-14 14-1 1-3 2-5 3-2 2-4 3-7 3v1l-2-1c1-1 1-1 1-2l1-1c6-1 10-2 14-6l8-8c5-5 9-10 11-17l1-1z" class="Am"></path><path d="M536 576v1c1 1 2 1 2 2s0 2 1 3h1c1-2 3-3 4-5l2-1h0c-2 7-6 12-11 17-1 0-1 0-1 1h-1c-2-3-1-8-1-11l1-5c0 1 0 2 1 3 1-1 1-3 2-5z" class="Q"></path><path d="M532 490l2-1c1 1 1 2 1 3-1 1-2 3-2 5-1 2-1 4-1 6 1 1 2 1 3 2 0 1-1 4-1 5h1v-1 1c0 1 0 1 1 2h0v3l1 1c1 1 2 1 4 1l1-1 2-1c1 1 1 2 1 3-4 4-8 8-10 14h-1l-1 4h0c0 2 0 3 1 4v1 4c0 1-1 2-1 2l1 1h0c-1 1-2 3-4 3h0-1l-1-6v7l-1-1v-18-10c0-2-1-3-1-5l1-1c0-1 0-2-1-3v-3c0-3-1-7 1-10v1l1 1 3-10 1-3z" class="a"></path><path d="M533 536c0 2 0 3 1 4v1 4c0 1-1 2-1 2l1 1h0c-1 1-2 3-4 3 1-5 1-10 3-15z" class="H"></path><path d="M527 533c0-4 1-6 4-8l-3 20v7l-1-1v-18z" class="F"></path><path d="M528 515v4l1 1c0-3 1-5 2-7h1v2h1c0 2-1 9-2 10-3 2-4 4-4 8v-10c0-3 0-5 1-8z" class="M"></path><path d="M533 497c-1 2-1 4-1 6 1 1 2 1 3 2 0 1-1 4-1 5l-1 5h-1v-2h-1c-1 2-2 4-2 7l-1-1v-4c0-6 2-13 5-18z" class="j"></path><path d="M532 490l2-1c1 1 1 2 1 3-1 1-2 3-2 5-3 5-5 12-5 18-1 3-1 5-1 8 0-2-1-3-1-5l1-1c0-1 0-2-1-3v-3c0-3-1-7 1-10v1l1 1 3-10 1-3z" class="Q"></path><path d="M535 517c0-2 0-3 1-5v3l1 1c1 1 2 1 4 1l1-1 2-1c1 1 1 2 1 3-4 4-8 8-10 14h-1l-1 4c-1-2-1-4 0-6l1-1 1-12z" class="AB"></path><path d="M535 517c0-2 0-3 1-5v3l1 1c1 1 2 1 4 1l1-1 2-1c1 1 1 2 1 3-4 4-8 8-10 14h-1v-3c1-2 3-4 4-5 0-2 0-2 1-3l2-2-1-1c-2-1-4-1-5-1z" class="AA"></path><defs><linearGradient id="Am" x1="546.866" y1="560.611" x2="540.373" y2="554.129" xlink:href="#B"><stop offset="0" stop-color="#490709"></stop><stop offset="1" stop-color="#7a0c0e"></stop></linearGradient></defs><path fill="url(#Am)" d="M563 511c1 1 2 1 2 2v4h1c0 1 0 0 1 1h0l1-1v3c-1 1-1 2-1 3 2-2 2-4 2-7 1-1 1-2 1-2 2 3 1 7 0 10l-1 2 1 1v2c-1 1-3 6-4 7l-5 8-10 12-5 5h-1l1 1c0 2 0 2 1 3v1 9l-1 1h0l-2 1c-1 2-3 3-4 5h-1c-1-1-1-2-1-3s-1-1-2-2v-1c-1 2-1 4-2 5-1-1-1-2-1-3l-1 5c-1-3 0-5-2-8 0-1 1-3 1-4l-1-1c-1-1-1-2-1-4l1-4v-4c0-1 1-2 1-3 0-2-1-2-1-4h0c2 0 3-2 4-3h0l-1-1s1-1 1-2v-4-1c-1-1-1-2-1-4h0l1-4h1c2-6 6-10 10-14l4-3c0 1-1 2-2 3h1 2 0 1c2-1 4-3 6-5 0 1-1 1-1 2-1 1-1 1-1 2 0-1 1-1 2-1 2 0 5-3 6-5z"></path><path d="M533 578c1-2 0-3 1-5 0-2 2-7 3-8 3-3 5-6 8-8l1-1c1-2 2-3 3-4l-3 5c0 1-1 2 0 4h-1c-2 2-3 4-5 6l-1 3c0 1 0 2-2 4l-1 1v1c-1 2-1 4-2 5-1-1-1-2-1-3z" class="V"></path><path d="M540 567l-1-1v-1c2-4 5-6 7-8 0 1-1 2 0 4h-1c-2 2-3 4-5 6z" class="N"></path><path d="M564 534l2 2-5 8-10 12-5 5c-1-2 0-3 0-4l3-5c2-1 3-3 5-5 3-3 6-7 8-11l2-2z" class="q"></path><path d="M569 516c1-1 1-2 1-2 2 3 1 7 0 10l-1 2 1 1v2c-1 1-3 6-4 7l-2-2-2 2-3 3v1l-1-1c-1-1 1-3 2-4v-3c3-2 5-6 7-9 2-2 2-4 2-7z" class="I"></path><path d="M569 526l1 1v2c-1 1-3 6-4 7l-2-2c1-2 4-6 5-8z" class="N"></path><path d="M545 561l1 1c0 2 0 2 1 3v1 9l-1 1h0l-2 1c-1 2-3 3-4 5h-1c-1-1-1-2-1-3s-1-1-2-2v-1-1l1-1c2-2 2-3 2-4l1-3c2-2 3-4 5-6z" class="U"></path><path d="M544 545c3-2 10-9 13-9-10 10-22 20-26 35l-1-1c-1-1-1-2-1-4l1-4v-4c0-1 1-2 1-3 0-2-1-2-1-4h0c2 0 3-2 4-3l1 1s0 1 1 2c1-1 2-2 4-2 1-2 3-3 4-4z" class="T"></path><path d="M534 548l1 1s0 1 1 2c1-1 2-2 4-2-2 3-6 6-8 10l-2 3v-4c0-1 1-2 1-3 0-2-1-2-1-4h0c2 0 3-2 4-3z" class="AH"></path><path d="M534 548l1 1s0 1 1 2l-5 4c0-2-1-2-1-4h0c2 0 3-2 4-3z" class="E"></path><path d="M563 511c1 1 2 1 2 2v4h1c0 1 0 0 1 1h0l1-1v3c-1 1-1 2-1 3-2 3-4 7-7 9 0 1-2 3-3 4-3 0-10 7-13 9-1 1-3 2-4 4-2 0-3 1-4 2-1-1-1-2-1-2l-1-1h0l-1-1s1-1 1-2v-4-1c-1-1-1-2-1-4h0l1-4h1c2-6 6-10 10-14l4-3c0 1-1 2-2 3h1 2 0 1c2-1 4-3 6-5 0 1-1 1-1 2-1 1-1 1-1 2 0-1 1-1 2-1 2 0 5-3 6-5z" class="F"></path><path d="M539 543s2 0 2-1c1-1 2-1 3-2l2 1v-1c1-1 2-1 3-2 0 3-4 3-5 7-1 1-3 2-4 4-2 0-3 1-4 2-1-1-1-2-1-2l-1-1h0c2-2 3-3 4-5h1z" class="t"></path><path d="M563 511c1 1 2 1 2 2-2 1-3 2-3 4h-1-1v1 2l-2 1v1h0c-1 1-1 1-2 1 1 2 1 2 1 4l-1 2c-1 1-1 0-1 1-1 1-1 2-2 3h-2-1l-1 2h0c-2 1-3 1-4 1h-1l2-2h-1l-1-1v-1l2-1c1-1 3-2 4-4 0-1 1-2 0-4 1-1 1-3 1-5 2-1 4-3 6-5 0 1-1 1-1 2-1 1-1 1-1 2 0-1 1-1 2-1 2 0 5-3 6-5z" class="AH"></path><path d="M551 533l-1-1h1l-1-1-2 3-1-1 2-2 1-1c1-3 3-5 5-7h1c1 2 1 2 1 4l-1 2c-1 1-1 0-1 1-1 1-1 2-2 3h-2z" class="AR"></path><path d="M549 515c0 1-1 2-2 3h1 2 0 1c0 2 0 4-1 5 1 2 0 3 0 4-1 2-3 3-4 4l-2 1v1c-1 2-1 3-2 4l-1 2c-1 1-2 3-2 4h-1c-1 2-2 3-4 5l-1-1s1-1 1-2v-4-1c-1-1-1-2-1-4h0l1-4h1c2-6 6-10 10-14l4-3z" class="l"></path><path d="M547 518h1 2 0l-6 5 1-1c0-2 1-3 2-4z" class="c"></path><path d="M549 515c0 1-1 2-2 3s-2 2-2 4l-1 1c-1 2-2 2-3 3l-1 1c-1 2-3 5-5 6v-1c2-6 6-10 10-14l4-3z" class="T"></path><path d="M534 541c1 0 1-1 2-2h0c0-1 1-2 2-3 0 0 1 0 2-1 0-1 0-1 1-1 0 1 1 2 1 3l-1 2c-1 1-2 3-2 4h-1c-1 2-2 3-4 5l-1-1s1-1 1-2v-4z" class="c"></path><path d="M538 543l-1-1c0-2 0-2 1-3h3c-1 1-2 3-2 4h-1z" class="l"></path><defs><linearGradient id="An" x1="591.127" y1="463.396" x2="608.706" y2="469.603" xlink:href="#B"><stop offset="0" stop-color="#1a0000"></stop><stop offset="1" stop-color="#2d0704"></stop></linearGradient></defs><path fill="url(#An)" d="M634 332h1l-5 25-2 8v1c-1 3-4 6-5 8 1 1 2 2 2 4 1 2 0 5-1 7 0 1-1 2-3 3v3c3 7 1 15-1 22h0c-1 4-9 36-10 37l-6 24-12 47v-1h-1c-2 0-4 0-6-1h-3s-1 0-1 1c-1 2-3 4-5 6 0-1 0-2-1-3-1 2-2 3-2 5h0l-3 1h0v-2l-1-1 1-2c1-3 2-7 0-10 0 0 0 1-1 2 0 3 0 5-2 7 0-1 0-2 1-3v-3l-1 1h0c-1-1-1 0-1-1h-1v-4c0-1-1-1-2-2-1 2-4 5-6 5-1 0-2 0-2 1 0-1 0-1 1-2 0-1 1-1 1-2-2 2-4 4-6 5h-1 0-2-1c1-1 2-2 2-3l-4 3c0-1 0-2-1-3l-2 1-1 1c-2 0-3 0-4-1l-1-1v-3h0c-1-1-1-1-1-2v-1 1h-1c0-1 1-4 1-5-1-1-2-1-3-2 0-2 0-4 1-6 0-2 1-4 2-5 0-1 0-2-1-3l-2 1 1-3-1-1c-1-1-1-2-1-4h1c-1 0-1 0-1-2l-1-4c0-2 1-4 1-6 1-2 2-4 2-6 0-1 0-2 1-3h0c0-2-1-3 0-4l1-1v-1c-1-1-2-1-2-2-1 0-3-1-3-3 0-1-1-2-2-3l-1-3v-2h-1c-1-1-1-1 0-3h-1c0-2 0-3 1-4v-1h0c4-3 6-3 10-3-1-1-1-1-2-1v-1h4l-1-1h1c1 0 1 0 2 1h4l1-1c-1-1-3-2-4-2-2-2-2-2-3-4 2 2 4 3 7 5l1-1v-1c-1-1-2-1-2-2l-1-2h0c0-2 1-3 3-5 0 2-1 2-1 4h1 2c1 0 2 1 2 2 2-1 3-2 4-3-1-1-3 0-4 0l-1-1 6-2 2-1c1 0 2 0 3-1l-1-1 4-2c-2 0-4 1-5 1-1 1-1 1-2 1s-1 0-2 1h-1v-1c2-2 6-4 8-6v-1l-5 1v-1l2-1-1-1h-1c-1 0-1 1-2 1v-1-2-2-4l1-1c1-4 6-8 9-11 2-1 3-2 4-3h0c1-1 1-1 1-2 1 1 2 1 2 2 1 0 2-1 2-1v1h1v-2c1-2 3-3 4-5 0-2 1-3 3-4l-2-1 6-6c2-2 4-5 6-7l5-1h0c1 0 1 0 2-1 2-1 3-1 4-1l3-3 6-3c1 1 1 1 1 2l4-3 7-6h1c3-1 8-6 10-8z"></path><path d="M602 458c1 3 1 9 0 12h-1l-1-11c2 0 2 0 2-1z" class="P"></path><path d="M604 474v-4c1-1 1-1 1-2v-1h-1v-2h1v-4c1-3 1-6 3-8h1c0-1 0-2 1-3l-6 24z" class="AY"></path><path d="M600 446c1-6 3-11 6-15l1 1c-2 2-3 5-4 8-2 5-1 12-1 18 0 1 0 1-2 1 0-2 0-5-1-7 1-2 1-4 1-6h0z" class="f"></path><path d="M588 465l3-3v-1c0-2 2-4 4-5l-1 1c-1 5-4 10-6 15-4 8-5 17-6 26-1-2-2-4-2-5 0-4 1-8 1-12v-1l1-1c1-3 1-5 2-7l4-7z" class="U"></path><path d="M581 481c0 4-1 8-1 12 0 1 1 3 2 5-1 6 1 12 5 17 2 2 4 3 5 5h-1c-2 0-4 0-6-1h-3s-1 0-1 1c-1 2-3 4-5 6 0-1 0-2-1-3-1 2-2 3-2 5h0l-3 1h0v-2l-1-1 1-2c1-3 2-7 0-10v-1-3h1 0l1-1c2-6 5-10 6-16l3-12z" class="P"></path><path d="M578 515v-1c0-2 0-5 1-6h2c0 1 1 4 1 5h0c-1 1-1 2 0 3h-1c-1 0-1-1-2-1h-1z" class="D"></path><path d="M573 509c0 2 0 3-1 4v3c0 1 1 1 2 1-1 2-1 4-2 6l-2 6h0v-2l-1-1 1-2c1-3 2-7 0-10v-1c2-1 2-2 3-4z" class="f"></path><path d="M582 513l5 5h0c1 1 3 2 4 2-2 0-4 0-6-1h-3s-1 0-1 1c-1 2-3 4-5 6 0-1 0-2-1-3l2-2v-1c0-2 1-3 1-5h1c1 0 1 1 2 1h1c-1-1-1-2 0-3h0z" class="AY"></path><path d="M581 481c0 4-1 8-1 12 0 1 1 3 2 5-1 6 1 12 5 17 2 2 4 3 5 5h-1c-1 0-3-1-4-2h0l-5-5c0-1-1-4-1-5h0c-1-4-2-9-1-12-2 1-3 6-4 9h0c-1 2-3 3-3 4-1 2-1 3-3 4v-3h1 0l1-1c2-6 5-10 6-16l3-12z" class="B"></path><path d="M596 440c1-1 1-2 2-2l-1 2c0 1-1 2-1 3l1 2h1v5c-2 2-2 4-3 6-2 1-4 3-4 5v1l-3 3-4 7c-1 2-1 4-2 7l-1 1v1l-3 12c-1 6-4 10-6 16l-1 1h0-1v3 1s0 1-1 2c0 3 0 5-2 7 0-1 0-2 1-3v-3l-1 1h0c-1-1-1 0-1-1h-1v-4c0-1-1-1-2-2-1 2-4 5-6 5-1 0-2 0-2 1 0-1 0-1 1-2 0-1 1-1 1-2 2-2 4-4 7-6-1-1-1-2-2-2 0-1 4-5 5-6h-1l-1-2 7-8 3-6v-1l1-2v-3l-1-4c-3 0-3-3-5-4h0l1-2-2-1-1-1 4-2 1-1v-1c0-1-1-1-2-2-1 0-1-1-2-1l-3-1c-1-1-1-2-2-3 0 0 0-1 1-1 0-1 0 0 1-1h0c-2-1-5-3-8-3h-1-8l1-1 2-3v-1c1-1 1-1 1-2l3-3c4 1 9 1 14 2h4l2 1c3 1 10 1 13 0 2 0 4-2 7-3v1z" class="c"></path><path d="M575 482l1-2c1 3 1 5 0 8v2c0-1 0-1 1-2 0 0 0-2 1-3l-1-2 2-2c-1 8-3 17-7 24l-1-1v-3l2-4c3-4 2-9 2-14v-1z" class="S"></path><path d="M581 480c0-5 0-13 3-18 1-1 2-3 3-5v3c0 1-1 2-1 3 1 0 2-1 3-3h0l1-1v3c-1 1-2 2-2 3l-4 7c-1 2-1 4-2 7l-1 1z" class="R"></path><path d="M596 443l1 2h1v5c-2 2-2 4-3 6-2 1-4 3-4 5v1l-3 3c0-1 1-2 2-3v-3l-1 1h0c-1 2-2 3-3 3 0-1 1-2 1-3v-3l4-4 3-6 2-4z" class="f"></path><path d="M591 453c0 1-1 2-1 3h0 1l1-1c-1 2-2 4-5 5v-3l4-4z" class="AB"></path><path d="M596 443l1 2v2c0 4-4 5-5 8l-1 1h-1 0c0-1 1-2 1-3l3-6 2-4z" class="C"></path><path d="M596 443l1 2v2c-1 1-1 1-2 1l-1-1 2-4z" class="S"></path><path d="M575 483c0 5 1 10-2 14l-2 4-1 2c-2 1-4 2-5 4h-1c-1-1-1-2-2-2 0-1 4-5 5-6h-1l-1-2 7-8 3-6z" class="AL"></path><path d="M567 499l5-5h1v3l-2 4-1 2c-2 1-4 2-5 4h-1c-1-1-1-2-2-2 0-1 4-5 5-6z" class="j"></path><path d="M571 501v3l1 1c-1 1-1 3-2 5v3 1s0 1-1 2c0 3 0 5-2 7 0-1 0-2 1-3v-3l-1 1h0c-1-1-1 0-1-1h-1v-4c0-1-1-1-2-2-1 2-4 5-6 5-1 0-2 0-2 1 0-1 0-1 1-2 0-1 1-1 1-2 2-2 4-4 7-6h1c1-2 3-3 5-4l1-2z" class="t"></path><path d="M563 511l3-2h0c1 1 0 2-1 4h0c0-1-1-1-2-2z" class="F"></path><path d="M571 501v3l1 1c-1 1-1 3-2 5v3 1s0 1-1 2h0c-1-5-1-8 1-13l1-2z" class="C"></path><path d="M566 457c-1-1-1-2-2-3 0 0 0-1 1-1 0-1 0 0 1-1h0c1 1 6 3 6 4 5 4 6 8 7 14v11l-2 2 1 2c-1 1-1 3-1 3-1 1-1 1-1 2v-2c1-3 1-5 0-8v-3l-1-4c-3 0-3-3-5-4h0l1-2-2-1-1-1 4-2 1-1v-1c0-1-1-1-2-2-1 0-1-1-2-1l-3-1z" class="I"></path><path d="M566 457c-1-1-1-2-2-3 0 0 0-1 1-1 0-1 0 0 1-1h0c1 1 6 3 6 4s1 2 1 3c-1 0 0 0-1-1h-3l-3-1z" class="m"></path><path d="M571 467l3-1h1c1 4 1 7 1 11l-1-4c-3 0-3-3-5-4h0l1-2z" class="AL"></path><path d="M589 442c2 0 4-2 7-3v1c-2 5-5 11-8 15l-4 5c-1 1-2 2-3 2s-2-2-2-3c-3-2-6-5-10-7l-4-2-2-1c-1-1-3-1-5-2h-6c-1 0-1 1-2 1l2-3v-1c1-1 1-1 1-2l3-3c4 1 9 1 14 2h4l2 1c3 1 10 1 13 0z" class="t"></path><path d="M582 454l1 2c0 1-1 1-1 3 0 0 1 1 2 1-1 1-2 2-3 2s-2-2-2-3v-1h0v-3h2v1l1-2z" class="j"></path><path d="M552 445h1 1 6 1c1 1 2 1 3 1h1 1v2c2 0 2 0 3-1 1 1 2 1 2 2-2-1-5 0-8 0-1-1-3-1-5-2h-6c-1 0-1 1-2 1l2-3z" class="F"></path><path d="M563 449c3 0 6-1 8 0 2 0 5 0 6 1 0 1 1 1 1 2 1 0 3 1 4 2l-1 2v-1h-2v3h0v1c-3-2-6-5-10-7l-4-2-2-1z" class="H"></path><path d="M569 452l2-1h3l1 1 2 3h-1l1 1c0 1 1 1 2 2v1c-3-2-6-5-10-7z" class="F"></path><path d="M589 442c2 0 4-2 7-3v1c-2 5-5 11-8 15l-2-1v-2c-1 0-2-1-2-2-3-2-5-3-8-4l-14-3c-3 0-5 0-8 2h0-1-1v-1c1-1 1-1 1-2l3-3c4 1 9 1 14 2h4l2 1c3 1 10 1 13 0z" class="D"></path><path d="M556 439c4 1 9 1 14 2h4l-10 1 6 2c2 0 4 1 6 1 1 0 2 1 3 1s1 0 2 1h2l1 1c1 0 2 0 3 1v3h-1c-1 0-2-1-2-2-3-2-5-3-8-4l-14-3c-3 0-5 0-8 2h0-1-1v-1c1-1 1-1 1-2l3-3z" class="C"></path><path d="M556 439c4 1 9 1 14 2-4 0-7 1-11 0h-1v1l4 1c-3 0-5 0-8 2h0-1-1v-1c1-1 1-1 1-2l3-3z" class="V"></path><path d="M568 465l1 1 2 1-1 2h0c2 1 2 4 5 4l1 4v3l-1 2v1l-3 6-7 8 1 2h1c-1 1-5 5-5 6 1 0 1 1 2 2-3 2-5 4-7 6s-4 4-6 5h-1 0-2-1c1-1 2-2 2-3l-4 3c0-1 0-2-1-3l-2 1-1 1c-2 0-3 0-4-1l-1-1v-3h0c-1-1-1-1-1-2v-1 1h-1c0-1 1-4 1-5-1-1-2-1-3-2 0-2 0-4 1-6 0-2 1-4 2-5l2-3 4-6c4-5 10-11 16-14h0c2 0 3 0 4-1h1c-1 1-1 1-1 2h0l7-5h0z" class="u"></path><path d="M556 486c1-2 1-2 3-3s4-6 6-4c1 2-2 3-2 5-1 1-1 2-2 3h0l-2 1v1c0-1 1-2 1-2 0-1 1-2 1-3l-1-1c-1 2-2 2-4 3z" class="AR"></path><path d="M563 495c3-4 5-8 9-11h0v1 2 2l-7 8-1 2v-2c0-1 0-2-1-2z" class="q"></path><path d="M557 477h1c1 0 2-1 2-1 1-1 2-1 3-2s2-1 4-1h0c1 0 1 0 2 1h2l-3 6 1 2-2-1h0v-2-3c-1-1-1-1-3 0-1 0-2 1-4 2-2 2-4 4-6 5v1c-2 2-3 5-5 8l1-2 1-2v-1-1l-2 2c0 1 0 2-1 3 0 1 0 0-1 1 1-2 1-3 2-5 0-2 6-8 8-10z" class="E"></path><path d="M556 486c2-1 3-1 4-3l1 1c0 1-1 2-1 3 0 0-1 1-1 2l-9 17-1 1h-1c-1-1-1-2-1-3 2-3 3-6 4-8s3-5 4-7v-1c1 0 1-1 1-2z" class="T"></path><path d="M570 469c2 1 2 4 5 4l1 4v3l-1 2v-1c-2-1-2 1-4 1 0-1 0 0 1-1v-1h-1l-1 2h-1 0l-1-2 3-6h-2c-1-1-1-1-2-1h0c-2 0-3 0-4 1s-2 1-3 2c0 0-1 1-2 1h-1 0c2-2 4-3 6-4 3-2 4-3 7-4z" class="N"></path><path d="M554 484h1v1 3 1c-1 2-3 5-4 7s-2 5-4 8c0 1 0 2 1 3 0 2-2 3-3 4h-1l-3 3 1 2-1 1c-2 0-3 0-4-1l-1-1v-3h0c2-7 4-13 8-18 1-3 2-5 5-7-1 2-1 3-2 5 1-1 1 0 1-1 1-1 1-2 1-3l2-2v1 1l-1 2-1 2c2-3 3-6 5-8z" class="AR"></path><path d="M549 487c-1 2-1 3-2 5 1-1 1 0 1-1 1-1 1-2 1-3l2-2v1 1l-1 2-1 2c-2 3-4 8-5 12l-2 6c-1 2-2 3-3 4v1l-1-1c-1 1-1 0-1 2l-1-1v-3h0c2-7 4-13 8-18 1-3 2-5 5-7z" class="B"></path><path d="M549 507v1h0c2-1 3-2 4-4s3-3 4-5l9-12h0c1 2-5 8-6 10v1c-1 1-1 1-1 2h1c0-2 2-4 3-5 1 0 1 1 1 2v2l1-2 1 2h1c-1 1-5 5-5 6 1 0 1 1 2 2-3 2-5 4-7 6s-4 4-6 5h-1 0-2-1c1-1 2-2 2-3l-4 3c0-1 0-2-1-3l-2 1-1-2 3-3h1c1-1 3-2 3-4h1z" class="AW"></path><path d="M544 515c1-1 2-2 3-2 2-2 4-3 6-4l1 2-5 4-4 3c0-1 0-2-1-3z" class="F"></path><path d="M563 495c1 0 1 1 1 2v2l1-2 1 2h1c-1 1-5 5-5 6l-8 6-1-2 4-3c-1-2 2-3 3-5v-1c0-2 2-4 3-5z" class="R"></path><path d="M563 495c1 0 1 1 1 2v2l-7 7c-1-2 2-3 3-5v-1c0-2 2-4 3-5z" class="H"></path><path d="M562 505c1 0 1 1 2 2-3 2-5 4-7 6s-4 4-6 5h-1 0-2-1c1-1 2-2 2-3l5-4 8-6z" class="e"></path><path d="M568 465l1 1 2 1-1 2h0c-3 1-4 2-7 4-2 1-4 2-6 4h0c-2 2-8 8-8 10-3 2-4 4-5 7-4 5-6 11-8 18-1-1-1-1-1-2v-1 1h-1c0-1 1-4 1-5-1-1-2-1-3-2 0-2 0-4 1-6 0-2 1-4 2-5l2-3 4-6c4-5 10-11 16-14h0c2 0 3 0 4-1h1c-1 1-1 1-1 2h0l7-5h0z" class="Q"></path><path d="M537 489c0 1-1 3 0 4 0 1 1 2 2 3l-4 9c-1-1-2-1-3-2 0-2 0-4 1-6 0-2 1-4 2-5l2-3z" class="l"></path><path d="M557 469h0c2 0 3 0 4-1h1c-1 1-1 1-1 2h0c-9 7-17 15-22 26-1-1-2-2-2-3-1-1 0-3 0-4l4-6c4-5 10-11 16-14z" class="T"></path><path d="M536 431c-1-1-1-1-2-1v-1h4l-1-1h1c1 0 1 0 2 1l1 1h1 0c1 1 2 2 3 2 1 1 2 3 4 3v1h2l2 2c1 1 2 1 3 1l-3 3c0 1 0 1-1 2v1l-2 3-1 1h8 1c3 0 6 2 8 3h0c-1 1-1 0-1 1-1 0-1 1-1 1 1 1 1 2 2 3l3 1c1 0 1 1 2 1 1 1 2 1 2 2v1l-1 1-4 2h0l-7 5h0c0-1 0-1 1-2h-1c-1 1-2 1-4 1h0c-6 3-12 9-16 14l-4 6-2 3c0-1 0-2-1-3l-2 1 1-3-1-1c-1-1-1-2-1-4h1c-1 0-1 0-1-2l-1-4c0-2 1-4 1-6 1-2 2-4 2-6 0-1 0-2 1-3h0c0-2-1-3 0-4l1-1v-1c-1-1-2-1-2-2-1 0-3-1-3-3 0-1-1-2-2-3l-1-3v-2h-1c-1-1-1-1 0-3h-1c0-2 0-3 1-4v-1h0c4-3 6-3 10-3z" class="R"></path><path d="M537 473c2-1 3-3 4-4 1 1 1 2 2 3-1 1-3 2-4 3h0-1l-1-2z" class="t"></path><path d="M543 459h4l1-1h2v1c-1 0-2 1-3 1-3 1-6 3-8 4 0-2 2-3 4-5z" class="V"></path><path d="M541 469c3-2 5-4 8-5 2-1 2-2 4-2 2-1 5-2 6-1l-6 3c-3 3-6 5-10 8-1-1-1-2-2-3z" class="F"></path><path d="M561 460v2h1c-8 5-15 9-22 16-1-1 0-1-1-3 1-1 3-2 4-3 4-3 7-5 10-8l6-3 2-1z" class="V"></path><path d="M531 470h2c0 2 0 1-1 2v2 2h2l2-1c-1-1-1-1 0-1s1 0 1-1l1 2h1 0c1 2 0 2 1 3-2 2-3 4-6 4h-2c-1 0-1 0-1-2l-1-4c0-2 1-4 1-6z" class="f"></path><path d="M531 470h2c0 2 0 1-1 2v2 2c1 2 1 2 0 4v1l-1-1-1-4c0-2 1-4 1-6z" class="S"></path><path d="M537 473l1 2h1c-1 2-3 3-5 5l-1-1c0-1 0-2 1-3l2-1c-1-1-1-1 0-1s1 0 1-1z" class="F"></path><path d="M543 459c2-2 4-3 6-3s1 0 2-1h2c1-1 2-1 4 0l1 1 4 1c1-1 3-1 4 0l3 1c1 0 1 1 2 1 1 1 2 1 2 2h-1c-1 0-1 0-2-1-3-1-4-1-7 1h-1l-1 1v-2h1c1 0 2-1 3-2h-1c-1 0-10 0-11 1v1l-3-1v-1h-2l-1 1h-4z" class="U"></path><path d="M561 462l1-1h1c3-2 4-2 7-1 1 1 1 1 2 1h1v1l-1 1-4 2h0l-7 5h0c0-1 0-1 1-2h-1c-1 1-2 1-4 1h0c-6 3-12 9-16 14l-4 6-2 3c0-1 0-2-1-3l-2 1 1-3-1-1c-1-1-1-2-1-4h1 2c3 0 4-2 6-4 7-7 14-11 22-16h-1z" class="e"></path><path d="M539 482l2 1-4 6-2 3c0-1 0-2-1-3l-2 1 1-3 6-5z" class="P"></path><path d="M561 462l1-1h1c3-2 4-2 7-1 1 1 1 1 2 1h1v1l-1 1-4 2h0l-7 5h0c0-1 0-1 1-2h-1c-1 1-2 1-4 1h0c-6 3-12 9-16 14l-2-1c3-2 5-4 7-6 4-4 8-8 13-11 1 0 2-1 3-2v-1h-1z" class="N"></path><path d="M557 469c1-1 5-4 7-4 1-1 2 0 3 0h1l-7 5h0c0-1 0-1 1-2h-1c-1 1-2 1-4 1h0z" class="X"></path><path d="M546 444v-1h0c1 1 1 2 1 3l1 1c1-1 3-3 4-3v1l-2 3-1 1h8 1c3 0 6 2 8 3h0c-1 1-1 0-1 1-1 0-1 1-1 1 1 1 1 2 2 3-1-1-3-1-4 0l-4-1-1-1c-2-1-3-1-4 0h-2c-1 1 0 1-2 1s-4 1-6 3-4 3-4 5l-1 1-6 9v-2c1-1 1 0 1-2h-2c1-2 2-4 2-6 0-1 0-2 1-3h0c0-2-1-3 0-4l1-1v-1c-1-1-2-1-2-2-1 0-3-1-3-3 1 1 2 2 3 2h1c3 1 6-1 8-3h1c1-1 2-2 2-4l1-1z" class="D"></path><path d="M545 452v1c-2 3-6 5-8 8-1 1-1 2-1 3l-1 2h0l3-1-6 9v-2c1-1 1 0 1-2h-2c1-2 2-4 2-6l1-2c3-4 7-8 11-10z" class="P"></path><path d="M546 444v-1h0c1 1 1 2 1 3l1 1c1-1 3-3 4-3v1l-2 3-1 1-4 3c-4 2-8 6-11 10l-1 2c0-1 0-2 1-3h0c0-2-1-3 0-4l1-1v-1c-1-1-2-1-2-2-1 0-3-1-3-3 1 1 2 2 3 2h1c3 1 6-1 8-3h1c1-1 2-2 2-4l1-1z" class="AV"></path><path d="M536 431c-1-1-1-1-2-1v-1h4l-1-1h1c1 0 1 0 2 1l1 1h1 0c1 1 2 2 3 2 1 1 2 3 4 3v1h2l2 2c1 1 2 1 3 1l-3 3c0 1 0 1-1 2-1 0-3 2-4 3l-1-1c0-1 0-2-1-3h0v1l-1 1c0 2-1 3-2 4h-1c-2 2-5 4-8 3h-1c-1 0-2-1-3-2 0-1-1-2-2-3l-1-3v-2h-1c-1-1-1-1 0-3h-1c0-2 0-3 1-4v-1h0c4-3 6-3 10-3z" class="u"></path><path d="M529 443v-1-2c1-2 2-3 3-4v1 4c1 1 1 1 0 2-1 0-1-1-3 0z" class="f"></path><path d="M530 445l1 1v2h2v1c0 1 1 2 0 3-1 0-2-1-3-2 0-1-1-2-2-3l2-2z" class="AA"></path><path d="M533 448l-1-2v-1h2v-1-1h1 2v3 1c-1 1-1 2-2 2h-2v-1z" class="F"></path><path d="M545 438c0 2 1 4 1 6l-1 1-1 1c-1 0-1 1-2 1-1 1-1 1-3 0v-1c1 0 1 0 2-1s1-1 1-3c1 1 1 0 2 0l1-4z" class="E"></path><path d="M539 432c3 1 5 3 6 6l-1 4c-1 0-1 1-2 0-2-5-3-3-7-4 0-1-1-2-2-2l2-1c2 1 4 1 6 1v-1c0-1-1-2-1-2l-1-1z" class="C"></path><path d="M526 434c4-3 6-3 10-3l3 1 1 1s1 1 1 2v1c-2 0-4 0-6-1l-2 1h-1c-1 1-2 2-3 4v2 1c0 1 1 2 1 2l-2 2-1-3v-2h-1c-1-1-1-1 0-3h-1c0-2 0-3 1-4v-1h0z" class="a"></path><path d="M526 434h2c1 1 1 3 1 5-1 0-1 1-1 2h0-1c0-1 0-1-1-2h-1c0-2 0-3 1-4v-1z" class="I"></path><path d="M536 431c-1-1-1-1-2-1v-1h4l-1-1h1c1 0 1 0 2 1l1 1h1 0c1 1 2 2 3 2 1 1 2 3 4 3v1h2l2 2c1 1 2 1 3 1l-3 3c0 1 0 1-1 2-1 0-3 2-4 3l-1-1c0-1 0-2-1-3h0v1c0-2-1-4-1-6-1-3-3-5-6-6l-3-1z" class="AG"></path><path d="M622 376c2 0 2 1 3 2 1 2 0 5-1 7 0 1-1 2-3 3v3c3 7 1 15-1 22h0l-2 2h-1c0 1-1 2-2 2l-2 2c0 1-1 2-1 3 0 2-1 3-1 4-1 1-3 6-4 6l-1-1c-3 4-5 9-6 15h0c0 2 0 4-1 6l-1-2v-5h-1l-1-2c0-1 1-2 1-3l1-2c-1 0-1 1-2 2v-1c-3 1-5 3-7 3-3 1-10 1-13 0l-2-1h-4c-5-1-10-1-14-2-1 0-2 0-3-1l-2-2h-2v-1c-2 0-3-2-4-3-1 0-2-1-3-2h0-1l-1-1h4l1-1c-1-1-3-2-4-2-2-2-2-2-3-4 2 2 4 3 7 5l1-1v-1c-1-1-2-1-2-2l-1-2h0c0-2 1-3 3-5 0 2-1 2-1 4h1 2c1 0 2 1 2 2 2-1 3-2 4-3-1-1-3 0-4 0l-1-1 6-2 2-1c1 0 2 0 3-1l-1-1 4-2c1-1 3-1 5-1l10-4v2l2-1c2-1 4-2 5-2 1-1 3-2 4-2 3 0 6-1 8-2l1-1c-1 0-2-1-3-1l-1-1 8-4 5-2h5c1-1 3-2 3-2 1-1 0-2 1-2 2-1 3-2 5-3 0-2 0-3 1-4-1-1 0-4 0-5z" class="AM"></path><path d="M571 427c3 0 6-1 10-2l-2 2c-1 0-2-1-2 1 0 0 0 1 1 2-2 0-5 1-7-1v-2z" class="c"></path><path d="M581 425h0l5-1c0 1-1 3-1 5l-6 1h0-1c-1-1-1-2-1-2 0-2 1-1 2-1l2-2z" class="AW"></path><path d="M554 422l5-2c1 0 2 0 2-2l2-1v1c1 1 0 0 2 1 0 0 1 1 1 2s0 2-1 3-2 1-3 0c-3 0-6 0-8-2z" class="AS"></path><path d="M555 416l1 1 5-1c1-1 0-1 1-1h2l2-1h1c3-1 7-3 10-3l1 1c-2 0-8 2-9 3-2 2-4 1-6 2l-2 1c0 2-1 2-2 2l-5 2h-1l4-3h-3c-1-1-3 0-4 0l-1-1 6-2z" class="T"></path><path d="M569 415c-2 2-4 1-6 2l-2 1c0 2-1 2-2 2l-5 2h-1l4-3c3-2 8-3 12-4z" class="AD"></path><path d="M563 411c1-1 3-1 5-1l10-4v2l2-1 1 1s0 1-1 1c-1 1-2 1-3 2-3 0-7 2-10 3h-1l-2 1h-2c-1 0 0 0-1 1l-5 1-1-1 2-1c1 0 2 0 3-1l-1-1 4-2z" class="AM"></path><path d="M563 411c1-1 3-1 5-1l10-4v2c-6 2-12 5-18 6l-1-1 4-2z" class="X"></path><path d="M597 401h3v1c-2 1-5 1-6 3 1 0 1 0 2-1h3l1-1h3v1l-1 1v2h2l1 1-1 1 1 1h0c-1 1-1 2-1 2l-1 1-3 2h-1c-2 1-4 4-6 5l1-2h0c2-1 2-2 3-3-1 1-1 1-2 1l1-2c0-1 0-1-1-2s0-1 0-2c-1-1-1-1-2-1h0v-2c-3 0-6 2-9 3l-6 2-1-1c1-1 2-1 3-2 1 0 1-1 1-1l-1-1c2-1 4-2 5-2 1-1 3-2 4-2 3 0 6-1 8-2z" class="H"></path><path d="M585 405c2 0 3 0 5-1h2c-2 1-2 1-3 2-2 0-4 2-6 3l1 1-6 2-1-1c1-1 2-1 3-2 1 0 1-1 1-1l-1-1c2-1 4-2 5-2z" class="c"></path><path d="M600 403h3v1l-1 1v2h2l1 1-1 1 1 1h0c-1 1-1 2-1 2l-1 1-3 2v-2l1-1v-1h-1l1-2v-1c0-1 0-1-1-2h-2l-1-1h2l1-2z" class="F"></path><path d="M607 392h5c1 1 1 1 2 1 1 1 2 0 4 0v2-1c-2 0-4 1-5 1v1 1h0l-2 1c0 1 1 2 1 2 1 3 1 3 0 5l-1 1-3 3-1 1-3 2s0-1 1-2h0l-1-1 1-1-1-1h-2v-2l1-1v-1h-3l-1 1h-3c-1 1-1 1-2 1 1-2 4-2 6-3v-1h-3l1-1c-1 0-2-1-3-1l-1-1 8-4 5-2z" class="M"></path><path d="M609 402c1 0 1 0 2 1s0 1 0 3l-3 3-1 1c0-3 0-3 1-5v-1h-1l2-2z" class="N"></path><g class="AB"><path d="M613 396v1h0l-2 1c0 1 1 2 1 2 1 3 1 3 0 5l-1 1c0-2 1-2 0-3s-1-1-2-1l-2-1-1-1h1c1-1 2-1 3-2l-1-1c1-1 2-1 4-1z"></path><path d="M607 392h5c1 1 1 1 2 1 1 1 2 0 4 0v2-1c-2 0-4 1-5 1v1c-2 0-3 0-4 1h0c-3 2-5 2-9 2 0 0-1 0-2 1-1 0-2-1-3-1l-1-1 8-4 5-2z"></path></g><path d="M607 392h5c1 1 1 1 2 1 1 1 2 0 4 0v2-1c-2 0-4 1-5 1v1c-2 0-3 0-4 1h0c-1 0-2-1-2-1-2 0-3 0-5 1v-1c1 0 2 0 3-1s1-2 2-3z" class="C"></path><path d="M607 410l1-1v7l-2 1c1 1 1 2 1 3 2-1 3-2 4-2v1c-2 2-4 3-6 5-1 2-3 3-5 4-1 0-1 0-2 1-2 0-3 1-5 1v-3c-2 0-4 0-5 1l-3 1c0-2 1-4 1-5 3-1 5-2 7-4 2-1 4-4 6-5h1l3-2 1-1 3-2z" class="AG"></path><path d="M606 417c1 1 1 2 1 3-1 1-3 2-5 3l-1-1 3-4 2-1z" class="D"></path><path d="M599 420c2 0 3-1 5-2l-3 4 1 1c-3 1-6 2-9 4-1 0-2 0-2-1 0 0-1-1 0-1 1-2 6-4 8-5z" class="AB"></path><path d="M607 410l1-1v7l-2 1-2 1c-2 1-3 2-5 2l1-1v-2h1l-1-1-1-1h1l3-2 1-1 3-2z" class="AA"></path><path d="M600 415l3-2 1 1v1c-1 1-2 3-4 4v-2h1l-1-1-1-1h1z" class="t"></path><path d="M607 410l1-1v7l-2 1-2 1v-1c2-1 2-3 3-5h1l-1-1v-1z" class="f"></path><path d="M593 420c2-1 4-4 6-5l1 1 1 1h-1v2l-1 1c-2 1-7 3-8 5-1 0 0 1 0 1 0 1 1 1 2 1h0c-2 0-4 0-5 1l-3 1c0-2 1-4 1-5 3-1 5-2 7-4z" class="F"></path><path d="M622 376c2 0 2 1 3 2 1 2 0 5-1 7 0 1-1 2-3 3v3c3 7 1 15-1 22h0l-2 2h-1c0 1-1 2-2 2l-2 2c0 1-1 2-1 3 0 2-1 3-1 4l-2-2c0-1 1-1 1-2s0-2 1-3v-1c-1 0-2 1-4 2 0-1 0-2-1-3l2-1v-7l3-3 1-1c1-2 1-2 0-5 0 0-1-1-1-2l2-1h0v-1-1c1 0 3-1 5-1v1-2c-2 0-3 1-4 0-1 0-1 0-2-1 1-1 3-2 3-2 1-1 0-2 1-2 2-1 3-2 5-3 0-2 0-3 1-4-1-1 0-4 0-5z" class="I"></path><path d="M618 403l1-1c0 2-1 3-1 5-1 1-1 3 0 5h0v-2l1 1-1 4h-1c0 1-1 2-2 2l-2 2c0 1-1 2-1 3 0 2-1 3-1 4l-2-2c0-1 1-1 1-2s0-2 1-3v-1c-1 0-2 1-4 2 0-1 0-2-1-3l2-1 2-1h1c1-1 3-2 3-4 1 0 1-1 1-2h1s1-1 1-2l1-4z" class="AK"></path><path d="M617 407c0 5 0 6-4 10 0 0-1 1-2 1s-2 1-4 2c0-1 0-2-1-3l2-1 2-1h1c1-1 3-2 3-4 1 0 1-1 1-2h1s1-1 1-2z" class="m"></path><path d="M613 396v-1c1 0 3-1 5-1v1c1 2 0 5 0 8l-1 4c0 1-1 2-1 2h-1c0 1 0 2-1 2 0 2-2 3-3 4h-1l-2 1v-7l3-3 1-1c1-2 1-2 0-5 0 0-1-1-1-2l2-1h0v-1z" class="C"></path><path d="M618 394v1c1 2 0 5 0 8l-1 4c0 1-1 2-1 2h0c-2-1-1 0-2-1v-2c1-1 1-2 1-3l2-3v-2c1-2 1-2 1-4z" class="a"></path><path d="M613 396v-1c1 0 3-1 5-1l-5 3v3h0c1 1 1 1 1 2 0 2-1 4-3 5 0 1 1 1 1 2 1 1-2 3-2 4v2l-2 1v-7l3-3 1-1c1-2 1-2 0-5 0 0-1-1-1-2l2-1h0v-1z" class="V"></path><path d="M545 427l1-1v-1c-1-1-2-1-2-2l-1-2h0c0-2 1-3 3-5 0 2-1 2-1 4h1 2c1 0 2 1 2 2v1c1 2 3 3 6 4h6 9v2c2 2 5 1 7 1h1 0l6-1 3-1c1-1 3-1 5-1v3c2 0 3-1 5-1 1-1 1-1 2-1 2-1 4-2 5-4 2-2 4-3 6-5-1 1-1 2-1 3s-1 1-1 2l2 2c-1 1-3 6-4 6l-1-1c-3 4-5 9-6 15h0c0 2 0 4-1 6l-1-2v-5h-1l-1-2c0-1 1-2 1-3l1-2c-1 0-1 1-2 2v-1c-3 1-5 3-7 3-3 1-10 1-13 0l-2-1h-4c-5-1-10-1-14-2-1 0-2 0-3-1l-2-2h-2v-1c-2 0-3-2-4-3-1 0-2-1-3-2h0-1l-1-1h4l1-1c-1-1-3-2-4-2-2-2-2-2-3-4 2 2 4 3 7 5z" class="r"></path><path d="M565 434l7 1c3 2 3 2 6 2h5c-2 0-3 0-4 1h0c-8 0-15-1-22-2l-1-1c3-1 7-1 9-1z" class="AM"></path><path d="M538 422c2 2 4 3 7 5 8 5 17 3 25 4h2 0l1 1c-2 1-4 0-5 1h-6l3 1c-2 0-6 0-9 1l1 1c-3 0-4 0-6-2l-3-2-2-1s-1 0-2-1h0-2 0-1l-1-1h4l1-1c-1-1-3-2-4-2-2-2-2-2-3-4z" class="AS"></path><path d="M585 429l3-1c1-1 3-1 5-1v3c-6 5-13 5-21 5l-7-1-3-1h6c1-1 3 0 5-1l-1-1h0-2c2-1 6-1 9-1h0l6-1z" class="AD"></path><defs><linearGradient id="Ao" x1="558.017" y1="432.954" x2="566.255" y2="423.675" xlink:href="#B"><stop offset="0" stop-color="#c00602"></stop><stop offset="1" stop-color="#ec1a1d"></stop></linearGradient></defs><path fill="url(#Ao)" d="M545 427l1-1v-1c-1-1-2-1-2-2l-1-2h0c0-2 1-3 3-5 0 2-1 2-1 4h1 2c1 0 2 1 2 2v1c1 2 3 3 6 4h6 9v2c2 2 5 1 7 1h1c-3 0-7 0-9 1-8-1-17 1-25-4z"></path><path d="M611 419c-1 1-1 2-1 3s-1 1-1 2l2 2c-1 1-3 6-4 6l-1-1c-3 4-5 9-6 15h0c0 2 0 4-1 6l-1-2v-5h-1l-1-2c0-1 1-2 1-3l1-2c-1 0-1 1-2 2v-1c-3 1-5 3-7 3-3 1-10 1-13 0l-1-2-1-1c1 0 3 0 5-1h0 0c1-1 2-1 4-1h-5c-3 0-3 0-6-2 8 0 15 0 21-5 2 0 3-1 5-1 1-1 1-1 2-1 2-1 4-2 5-4 2-2 4-3 6-5z" class="I"></path><path d="M597 440c1 1 1 3 2 4 0 1 0 1 1 2h0c0 2 0 4-1 6l-1-2v-5h-1l-1-2c0-1 1-2 1-3z" class="Q"></path><path d="M593 435c2 0 3-1 4-1v1h3v1l-2 2h0c-1 0-1 1-2 2v-1l-1-1h-4-1l-1-1 4-2z" class="C"></path><path d="M600 428v1l6-3c-3 3-6 6-9 8-1 0-2 1-4 1-3-1-7 1-10 2h-5c-3 0-3 0-6-2 8 0 15 0 21-5 2 0 3-1 5-1 1-1 1-1 2-1z" class="H"></path><path d="M583 437c3-1 7-3 10-2l-4 2 1 1h1 4l1 1c-3 1-5 3-7 3-3 1-10 1-13 0l-1-2-1-1c1 0 3 0 5-1h0 0c1-1 2-1 4-1z" class="AG"></path><path d="M589 442c-2-1-3 0-5-2-1 0-1 0-2-1v-1l7-1 1 1h1 4l1 1c-3 1-5 3-7 3z" class="AH"></path><path d="M634 332h1l-5 25-2 8v1c-1 3-4 6-5 8 1 1 2 2 2 4-1-1-1-2-3-2 0 1-1 4 0 5-1 1-1 2-1 4-2 1-3 2-5 3-1 0 0 1-1 2 0 0-2 1-3 2h-5l-5 2-8 4 1 1c1 0 2 1 3 1l-1 1c-2 1-5 2-8 2-1 0-3 1-4 2-1 0-3 1-5 2l-2 1v-2l-10 4c-2 0-4 0-5 1-2 0-4 1-5 1-1 1-1 1-2 1s-1 0-2 1h-1v-1c2-2 6-4 8-6v-1l-5 1v-1l2-1-1-1h-1c-1 0-1 1-2 1v-1-2-2-4l1-1c1-4 6-8 9-11 2-1 3-2 4-3h0c1-1 1-1 1-2 1 1 2 1 2 2 1 0 2-1 2-1v1h1v-2c1-2 3-3 4-5 0-2 1-3 3-4l-2-1 6-6c2-2 4-5 6-7l5-1h0c1 0 1 0 2-1 2-1 3-1 4-1l3-3 6-3c1 1 1 1 1 2l4-3 7-6h1c3-1 8-6 10-8z" class="l"></path><path d="M599 385h1v2h1v1c-2 0-3 1-4 2l-5 3c0-3 5-6 7-8z" class="t"></path><path d="M604 366h0l8-5h-1c0 2-1 3-3 4v1c1 1 1 1 2 1v1l-12 6h-1l1-2c1-1 1-2 2-2l4-4z" class="B"></path><path d="M589 381c3-2 5-5 8-6h0l-3 3h1c2 0 4-2 6-3l1 1-1 1v1s-1 1-2 1c-2 2-3 5-5 7-8 8-18 15-29 18h-1v-1c1-1 1-2 1-2 1-1 2-2 3-2s7-6 8-7c2-2 4-3 6-5 2-1 5-4 7-6z" class="AD"></path><path d="M624 361l1 1v1l3 2v1c-1 3-4 6-5 8 1 1 2 2 2 4-1-1-1-2-3-2 0 1-1 4 0 5-1 1-1 2-1 4-2 1-3 2-5 3-1 0 0 1-1 2 0 0-2 1-3 2h-5l-5 2v-4l-1-2v-1h-1v-2h-1c1-2 4-4 5-5-1-1-1-1-2-1 1-3 4-3 6-5 1-1 1-1 1-3h-3c1-1 3-1 4-2h1l-1-1v-1l4-1 3-2h0c2-1 3-2 6-1h0l1-1v-1z" class="q"></path><path d="M606 381h1l-1 1s-1 1-1 2h0l1 1-5 2h-1v-2l6-4z" class="E"></path><path d="M617 364l1 1v1c-2 1-2 1-3 3v1 1 1c0 1 0 2-1 3v1h0c0 2-1 2-2 3v1h1v1l-7 4-1-1h0c0-1 1-2 1-2l1-1h-1c1-2 2-3 4-3v-3-1c0-1 1-2 2-3v-1c0-1 0-1 1-2 0 0 1-1 2-1l-1-1 3-2z" class="M"></path><path d="M615 370v1 1c0 1 0 2-1 3-2 1-1 2-3 3l-4 3h-1c1-2 2-3 4-3 1-1 2-2 2-3v-2c0-2 1-2 3-3z" class="N"></path><path d="M607 381l4-3c2-1 1-2 3-3v1h0c0 2-1 2-2 3v1h1v1l-7 4-1-1h0c0-1 1-2 1-2l1-1z" class="AL"></path><path d="M624 361l1 1v1l3 2v1c-1 3-4 6-5 8 1 1 2 2 2 4-1-1-1-2-3-2l-1-1v2h-1c-3 0-5 2-7 3h-1v-1c1-1 2-1 2-3h0v-1c1-1 1-2 1-3v-1-1-1c1-2 1-2 3-3v-1l-1-1h0c2-1 3-2 6-1h0l1-1v-1z" class="Q"></path><path d="M622 370l-1 1s-1 0-2 1v1h1l-1 1c-1 0 0 0-1 1 1 0 2-1 3-1l-1 1h1v2h-1c-3 0-5 2-7 3h-1v-1c1-1 2-1 2-3h0l5-5c1 0 2-1 3-1z" class="C"></path><path d="M617 364h0c2-1 3-2 6-1l-3 2v1c1 1 1 2 2 3v1c-1 0-2 1-3 1l-5 5v-1c1-1 1-2 1-3v-1-1-1c1-2 1-2 3-3v-1l-1-1z" class="AB"></path><path d="M619 371v-1h-2v-1l2-1v-2l1-1v1c1 1 1 2 2 3v1c-1 0-2 1-3 1z" class="U"></path><path d="M621 375l1 1c0 1-1 4 0 5-1 1-1 2-1 4-2 1-3 2-5 3-1 0 0 1-1 2 0 0-2 1-3 2h-5l-5 2v-4l-1-2v-1l5-2 7-4v-1c2-1 4-3 7-3h1v-2z" class="P"></path><path d="M613 380c2-1 4-3 7-3l-1 1c-1 0-1 1-2 1-1 1 0 1-1 2 1 1 3 0 4 1-2 3-5 4-7 6h0c-1-1 0-1-1-2l-1 1c-2 0-2 0-4-1 1-1 2-2 3-2 1-1 2-1 2-2l1-1v-1z" class="F"></path><path d="M606 385l7-4-1 1c0 1-1 1-2 2-1 0-2 1-3 2 2 1 2 1 4 1l1-1c1 1 0 1 1 2l-6 4-5 2v-4l-1-2v-1l5-2z" class="H"></path><path d="M634 332h1l-5 25-2 8-3-2v-1l-1-1v1l-1 1h0c-3-1-4 0-6 1h0l-3 2-4 1c-1 0-1 0-2-1v-1c2-1 3-2 3-4h1l-8 5h0c0-1-1-1-2-2l3-2c-1 0-1 0-2-1 1-1 2-1 2-2h1c2-1 4-2 5-3h-1v-1c-1-1 0-2 0-3l2-2v-1h0l4-3 7-6h1c3-1 8-6 10-8z" class="AX"></path><path d="M616 346v1l-3 4v1h4c-2 1-3 2-5 3 0 0-1 0-1 1h-1v-1c-1-1 0-2 0-3l2-2v-1h0l4-3z" class="D"></path><path d="M618 351c1-2 3-4 6-5 1 0 1 0 2-1h1v1l-1 1v2l1 1c0 1-1 2-2 3h-1 0c-1 1-2 1-3 1-2 1-3 1-4 2l-1-1c0-1 2-2 3-4h-1z" class="v"></path><path d="M618 351h1c-1 2-3 3-3 4l1 1c1-1 2-1 4-2l-1 1-5 3-3 3-8 5h0c0-1-1-1-2-2l3-2c-1 0-1 0-2-1 1-1 2-1 2-2h1c2-1 4-2 5-3 0-1 1-1 1-1 2-1 3-2 5-3l1-1z" class="P"></path><path d="M617 356c1-1 2-1 4-2l-1 1-5 3-3 3-8 5h0c0-1-1-1-2-2l3-2 12-6z" class="H"></path><path d="M615 358l5-3c0 2 0 2 1 3 2 1 6-1 9-1l-2 8-3-2v-1l-1-1v1l-1 1h0c-3-1-4 0-6 1h0l-3 2-4 1c-1 0-1 0-2-1v-1c2-1 3-2 3-4h1l3-3z" class="I"></path><path d="M612 361l3-3c-1 2-1 2-1 4v1h1 0c2-1 7-2 9-2v1l-1 1h0c-3-1-4 0-6 1h0l-3 2-4 1c-1 0-1 0-2-1v-1c2-1 3-2 3-4h1z" class="C"></path><path d="M611 347c1 1 1 1 1 2h0v1l-2 2c0 1-1 2 0 3v1h1c-1 1-3 2-5 3h-1c0 1-1 1-2 2 1 1 1 1 2 1l-3 2c-1 1-3 2-5 3l-4 3c2-1 4-1 5-1 0 1-2 2-3 3l-2 2-6 5h2v1c-3 1-5 2-7 4h0c-1 0-2 1-3 1l-1 1c-2 0-3 1-4 2 0-3 0-5-1-7h1v-2c1-2 3-3 4-5 0-2 1-3 3-4l-2-1 6-6c2-2 4-5 6-7l5-1h0c1 0 1 0 2-1 2-1 3-1 4-1l3-3 6-3z" class="V"></path><path d="M578 374c4-2 8-6 11-9-1 2-2 4-3 5-1 2-2 3-4 4h0c-2 1-4 3-5 5v-1h2c1-1 1-1 2-1 1-2 2-4 4-4 3 0 7-5 10-6h2l-4 3c-5 3-13 8-17 12h-1l-1-1v-2c1-2 3-3 4-5z" class="N"></path><path d="M593 370c2-1 4-1 5-1 0 1-2 2-3 3l-2 2-6 5h2v1c-3 1-5 2-7 4h0c-1 0-2 1-3 1l-1 1c-2 0-3 1-4 2 0-3 0-5-1-7h1l1 1h1c4-4 12-9 17-12z" class="T"></path><path d="M611 347c1 1 1 1 1 2h0v1l-2 2c0 1-1 2 0 3v1h1c-1 1-3 2-5 3h-1c-2-1-2-1-3-3l-13 9c-3 3-7 7-11 9 0-2 1-3 3-4l-2-1 6-6c2-2 4-5 6-7l5-1h0c1 0 1 0 2-1 2-1 3-1 4-1l3-3 6-3z" class="X"></path><path d="M596 355c1 0 1 0 2-1 2-1 3-1 4-1-2 2-4 4-7 5l-1-1 2-2h0z" class="f"></path><path d="M602 356l10-7v1l-2 2c0 1-1 2 0 3v1h1c-1 1-3 2-5 3h-1c-2-1-2-1-3-3z" class="Q"></path><path d="M591 356l5-1-2 2 1 1-14 12-2-1 6-6c2-2 4-5 6-7z" class="R"></path><path d="M569 379c1 1 2 1 2 2 1 0 2-1 2-1v1c1 2 1 4 1 7 1-1 2-2 4-2l1-1c1 0 2-1 3-1h0c2-2 4-3 7-4v1c-2 2-5 5-7 6-2 2-4 3-6 5-1 1-7 7-8 7s-2 1-3 2c0 0 0 1-1 2v1h1 0c-1 1-2 1-2 2-1 0-1 0-1 1 2 0 3-1 4-1l2-1c1-1 2-1 3-2l9-4 4-2 1-1c2-1 4-2 7-3l5-3c1-1 2-2 4-2l1 2v4l-8 4 1 1c1 0 2 1 3 1l-1 1c-2 1-5 2-8 2-1 0-3 1-4 2-1 0-3 1-5 2l-2 1v-2l-10 4c-2 0-4 0-5 1-2 0-4 1-5 1-1 1-1 1-2 1s-1 0-2 1h-1v-1c2-2 6-4 8-6v-1l-5 1v-1l2-1-1-1h-1c-1 0-1 1-2 1v-1-2-2-4l1-1c1-4 6-8 9-11 2-1 3-2 4-3h0c1-1 1-1 1-2z" class="AM"></path><path d="M578 406c5-2 11-5 16-8l1 1c1 0 2 1 3 1l-1 1c-2 1-5 2-8 2-1 0-3 1-4 2-1 0-3 1-5 2l-2 1v-2z" class="N"></path><path d="M574 388c1-1 2-2 4-2l1-1c1 0 2-1 3-1-1 1-2 1-3 2l-1 1c0 1-1 1-2 1h0c0 1-1 2-2 2-2 2-3 3-5 4v1c-2 1-4 2-6 2-1 1-2 1-3 2-1 0-2 2-3 3h-1v-2c2-2 3-3 4-5l1-1 2-2c3-2 5-5 8-5h2v1h1z" class="AD"></path><path d="M563 392h3c2-1 3-4 6-4v1l-2 2c-2 1-6 4-7 6-1 1-2 1-3 2-1 0-2 2-3 3h-1v-2c2-2 3-3 4-5l1-1 2-2z" class="AC"></path><path d="M569 379c1 1 2 1 2 2 1 0 2-1 2-1v1c1 2 1 4 1 7h-1v-1h-2c-3 0-5 3-8 5l-2 2c-2 1-4 1-4 3v1h-1c0 1 0 0-1 1 0 1 0 1-1 2v-1-4l1-1c1-4 6-8 9-11 2-1 3-2 4-3h0c1-1 1-1 1-2z" class="F"></path><path d="M573 380v1c1 2 1 4 1 7h-1v-1c0-1 0-2-1-3h-1l-1 1c-1 0-2 0-2 1-1 0-1 1-1 1-1 1-1 1-2 1 0 1-1 1-2 2-1 0-3 2-4 2 2-2 3-4 5-5s5-4 7-6c1 0 2-1 2-1z" class="E"></path></svg>