<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="119 109 789 812"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#a4a2a2}.C{fill:#7d7c7c}.D{fill:#b4b2b3}.E{fill:#8e8d8e}.F{fill:#cfcecd}.G{fill:#e6e5e5}.H{fill:#efeeee}.I{fill:#c6c5c5}.J{fill:#b5b4b3}.K{fill:#d9d8d7}.L{fill:#919090}.M{fill:#353535}.N{fill:#575656}.O{fill:#4c4c4c}.P{fill:#616161}.Q{fill:#666566}.R{fill:#1c1c1c}.S{fill:#737272}.T{fill:#363636}.U{fill:#6e6d6e}</style><path d="M760 167l3-1c1 1 1 2 2 3v2c-1-1-2 0-3 0 0-2-1-3-2-4zm-544-10c1 2 1 2 0 3v1l-1-1c-2-1-3-1-5-2 2-1 3-1 5 0 1 0 1 0 1-1z" class="J"></path><path d="M351 546l2 1c-1 2-2 3-2 6l-3-1 1-3 2-3z" class="M"></path><path d="M353 543h1l-3 3-2 3v-5c1-1 3-1 4-1z" class="C"></path><path d="M361 560c3 0 5 0 8 2v1c-2 0-3 0-4 1-1-2-3-3-4-4z" class="O"></path><path d="M340 590c2 1 3 1 5 1l2 1v3h-2v-1l-6-3 1-1zm270 288h3l2 4c-1 1-1 2-2 3l-3-7z" class="D"></path><path d="M338 621c1 0 3 0 4 1 0 2-3 3-4 5h-1v1h-2c2-2 3-4 4-6l-1-1z" class="N"></path><path d="M263 175h1 4 4s1 0 1 1c-2 1-4 2-7 2-1-1-2-1-3-3z" class="G"></path><path d="M642 843l1 3h0l3 9c-2-2-5-6-6-8 0-1 1-3 2-4z" class="L"></path><path d="M353 543c3-1 6 0 9 1v1h-1c-3 0-6 0-8 2l-2-1 3-3h-1z" class="O"></path><path d="M669 602l5-1-8 8c1-1 1-2 1-3v-2l-1-1 3-1z" class="J"></path><path d="M437 848l-15 3c5-3 10-5 15-6v1c-2 1-2 1-4 1v1h3v-1l1 1z" class="I"></path><path d="M387 844h1c1 0 1 0 2 2-1 3-3 5-6 7 0-2 1-4 2-5l1-4z" class="B"></path><path d="M805 288l13-3c-4 3-7 4-11 6l-1-3h-1z" class="I"></path><path d="M593 116c3 4 4 7 3 12-1-2-1-2-1-4h0v-1c0 2 0 2-2 4 1-4 1-6-1-9l1-2z" class="B"></path><path d="M193 307c1 1 3 1 5 0-2 2-4 3-6 5-1 1-2 2-4 3 1-3 3-6 5-8z" class="G"></path><path d="M812 271c1 0 2 1 3 1l-4 7c-1-1-2-1-3-2 1-2 3-4 4-6z" class="L"></path><path d="M421 737l2 4c0 4 0 7-1 11l-2 1c1-3 1-6 1-9v-5-2z" class="U"></path><path d="M594 719c2-4 3-9 5-14 0 5-1 9-2 13 0 1-1 1-1 2l-1 2-1-3z" class="E"></path><path d="M575 568l1 1h3l-1 2h-2c-4 1-8 1-11 1l10-4z" class="G"></path><path d="M426 926h0l-1-1h1 1 1c5 3 10 5 16 5-1 1-6 0-8 0-3 0-8-1-10-4z" class="L"></path><path d="M197 270c1-2 2-2 4-2 1 3 1 4 3 7-1 0-2 1-3 1h-1l-3-6zm149 285l2-3 3 1v3c-1 2-1 4-2 6-1-2-2-3-3-5v-2z" class="U"></path><path d="M346 555l2-3 3 1v3c-2 0-3 0-5-1z" class="N"></path><path d="M633 165c5 2 9 4 12 7l1 2c-6-4-11-6-17-7l1-1c2 0 2 0 3-1z" class="E"></path><path d="M632 370l-1-1c1-2 2-2 4-3l-3 18c0-4 1-10 0-14z" class="K"></path><path d="M153 258c2 1 3 2 5 3-3 1-6 3-9 3h-1c1-3 3-5 5-6z" class="J"></path><path d="M597 718l-1 15c-2 1-2 2-4 3l3-14 1-2c0-1 1-1 1-2z" class="B"></path><path d="M592 118c-4-6-9-9-15-11 7 1 12 3 16 9l-1 2z" class="D"></path><path d="M634 762c3 3 7 6 12 8 2 1 7 1 8 2-7 0-17-3-22-8 1 0 2 0 2 1h1 0c0-1-1-1-1-2v-1z" class="B"></path><path d="M304 557l1 1c2 0 4-2 6-2h3c-4 3-10 6-15 6h0l-1-1c2-1 4-2 6-4z" class="I"></path><path d="M607 717l1 1c0 1 0 2 1 4-1 5-1 10-1 15l-2 2 1-22z" class="Q"></path><path d="M579 472c-4-2-6-5-9-8 2 0 3 0 5 1 2 3 5 5 9 6-2 0-4 0-5 1z" class="D"></path><path d="M762 171c1 0 2-1 3 0 0 1 0 2-1 3-2 2-5 3-7 3s-3-1-4-1c1-1 2-1 3-1 2-1 4-3 6-4z" class="I"></path><path d="M589 547v4 1c-4 3-9 4-13 3h-1c5-3 10-3 14-8z" class="H"></path><path d="M420 753l2-1c-1 8-3 17-8 24l-1-2c5-6 6-14 7-21z" class="C"></path><path d="M587 558c0 3-2 6-4 9-2 1-3 2-4 2h-3l-1-1c4-2 7-4 10-8l2-2z" class="H"></path><path d="M445 763h2l2 1h1c1 1 2 2 3 4 0 2 1 3 2 5s4 6 3 9c-2-4-3-7-6-11-2-3-5-5-7-8z" class="S"></path><path d="M585 319h0c1 1 2 1 3 2-8 1-14 4-21 8 1-2 1-3 2-4l6-3c4-2 7-2 10-3z" class="M"></path><path d="M711 556c3-1 5 1 8 2h1l2 1h2 1c1 0 2 1 2 2-1 1-1 1-2 1-5 0-11-3-14-6z" class="B"></path><path d="M606 739l2-2v5c-1 5 0 8 1 13 1 3 2 6 2 9-5-8-5-17-5-25z" class="U"></path><path d="M387 844l-1 4c-1 1-2 3-2 5l-2 4c-1-3-2-6-1-9v-1c2-2 3-2 6-3z" class="L"></path><path d="M359 583c6 7 14 12 18 20-2-1-3-4-5-5s-4-3-6-5v-1c-2-1-5-3-6-5s-1-3-1-4z" class="U"></path><path d="M673 258c-7-5-13-6-22-7l2-1c8-1 16 1 23 6-2-1-2-1-3-1l-1-1-2-1h-2v-1h-2l-1 1 3 1c1 0 1 0 2 1 0 0 1 0 2 1l1 1v1z" class="D"></path><path d="M551 329c0-1 0-2 1-2l1-1c5-5 11-9 18-11-2 2-4 2-6 3l1 1c-2 1-3 2-5 4-4 2-7 4-10 6z" class="M"></path><path d="M547 874c-1-1-3-2-4-3 0-1-1-2 0-4 3 3 5 5 9 6h1c3 0 5 0 7-1l1 1h-2v1h0c-3 1-7 1-10 1l-2-1z" class="D"></path><path d="M310 549c0 1 1 3 3 3 1 0 3 0 5 1h0c-2 1-3 2-4 3h-3c-2 0-4 2-6 2l-1-1c3-2 5-4 6-8z" class="K"></path><path d="M373 781h2c4 2 7 2 12 1h0l-2 1c-2 2-6 4-9 3-1 0-2-1-3-1v-4z" class="L"></path><path d="M658 780h0l1 1v2c0 2-1 2-2 3-3 0-6-1-9-2-1-1-2-1-2-2 4 0 7 0 12-2z" class="E"></path><path d="M324 473c3 6 4 12 5 18 1 8 3 16 2 24-1-4-1-8-2-12l-2-11h1v1l1-1c-1-1-1-1-1-2v-1c-1-2-1-5-2-7 0-3-2-6-2-9z" class="O"></path><path d="M763 146c4-2 8-2 11-1h0c-2 1-3 1-5 1h3c1 0 1 1 2 1l6 3v2c-2-1-5-2-7-3l-4-1h-3c-2-1-3-1-5-2h2z" class="F"></path><path d="M761 146h2c4 2 6-1 10 3l-4-1h-3c-2-1-3-1-5-2z" class="D"></path><path d="M547 847h3c0 1 1 3 3 3-1 1-1 2-2 2-4 1-7 2-9 6-1 2-1 4-1 6v-9l4-4h1v-2h1l1-1-1-1z" class="M"></path><path d="M676 550c-1-1-1-2-2-3-3-2-7-1-11-1 0-1 1-2 3-2 2-2 5-2 8-1 2 2 4 4 5 7l-1 1-1-1h-1z" class="P"></path><path d="M630 198h1c1 0 2 0 3 1 1 2 2 4 1 6 0 3-2 5-5 7-1 0-1 1-2 1 2-3 5-6 4-10-1-2-2-3-2-5z" class="G"></path><path d="M638 342c0-1 1-1 2-2 1 2 3 4 5 6 1 1 3 2 3 3s2 3 3 4c1 2 1 3 2 4h-1c-4-6-10-10-14-15z" class="B"></path><path d="M617 224h14c3 0 5-1 8-1 0 0 1 0 2 1-7 2-18 3-26 1h-1 0c1-1 2 0 2-1h1z" class="C"></path><path d="M204 275c3 3 5 6 8 8v1h-1l-3-3c-1 0-2 0-2 1l1 1h0v2h0c1 0 2 1 2 1v1c-4-2-8-4-12-7l9 3-5-7c1 0 2-1 3-1z" class="J"></path><path d="M607 786c1 1 3 1 5 2 2 2 5 7 7 10h0l-8-4c-1 0-2-1-3-2v-2c-1-1-1-2-1-4z" class="I"></path><path d="M666 564h0c-3-2-6-2-8-4v-1c1-1 1-2 3-2s3 0 5 1v1l4 4-1 2c-1 0-2-1-3-2v1z" class="S"></path><path d="M639 622h0c-3 2-5 5-7 8 1-3 2-5 3-8 3-5 6-10 9-14 0 1 0 2-1 3-1 2-1 2 0 4-1 1-1 3-1 4l-3 3z" class="N"></path><path d="M364 623c2-1 4-2 6-2l4 4 2 2v2c-4-2-7-2-11-2 0-1-1-1 0-2v-1l-1-1z" class="S"></path><path d="M420 711c1 9 4 20 3 30l-2-4v-3l-2-8v-1c2-4 0-7 0-11 0-1 1-2 1-3z" class="N"></path><defs><linearGradient id="A" x1="314.075" y1="613.641" x2="320.249" y2="602.263" xlink:href="#B"><stop offset="0" stop-color="#9a9a99"></stop><stop offset="1" stop-color="#c2c0c2"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M315 607l1-5c0-1 1-4 3-5h0c2-2 2-2 4-2-3 2-5 5-5 10-1 5 0 9 3 13h-1v-1h-2c-2-3-3-6-3-10z"></path><path d="M637 348c4 7 9 14 12 22-4-4-7-7-9-12h-1 0c0-1-1-2-1-3-1-3-1-4-1-7z" class="D"></path><path d="M440 746l2 4 8 14h-1l-2-1h-2c-3-4-5-7-6-10l1-1v-6z" class="J"></path><path d="M631 306c1 0 2 0 3 1h2c3 0 9 5 11 7l1 1c0 2 1 3 1 5l-18-14z" class="B"></path><path d="M594 177c4 1 14 5 16 9h0c-11-5-20-8-32-5 3-4 12-2 16-4z" class="C"></path><path d="M722 203h13c-1 0-2 0-3 1h0c5 2 10 1 15 2l2 1h-15-7c-3-2-5-2-8-3v-1h3z" class="D"></path><path d="M719 203h3c1 0 1 1 2 1 1 1 7 0 7 2h1c1 0 1 0 2 1h-7c-3-2-5-2-8-3v-1z" class="B"></path><path d="M582 857h2c1 0 2 1 3 2s4 3 5 4v1l3 3c2 1 4 3 6 4 3 2 6 3 9 5v2c-3-2-6-3-9-5-7-3-14-10-19-16z" class="Q"></path><path d="M464 796c2 2 3 7 4 9 1 1 1 3 1 5 0 4 0 10-1 15v-2-1c-1-2-1-3-3-5 1-7 1-13-1-21z" class="C"></path><defs><linearGradient id="C" x1="574.998" y1="556.447" x2="588.1" y2="555.902" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#b8b7b6"></stop></linearGradient></defs><path fill="url(#C)" d="M589 552h1c0 2-2 4-3 6l-2 2c-3 0-8-1-10-2-1-1-1-1-1-2l1-1h1c4 1 9 0 13-3z"></path><path d="M364 623l1 1v1c-1 1 0 1 0 2-3 1-5 3-7 5-5 5-5 10-5 17h0c-2-3-1-10 0-13 2-6 6-10 11-13z" class="F"></path><path d="M571 860c1 1 1 2 1 3v1 1c-1 1-2 2-2 3l-1 1c-1 1-3 4-5 5h0c-2 1-3 1-5 2-4 1-7 0-10-1 3 0 7 0 10-1h0v-1h2l-1-1c1-1 2-1 3-1 4-3 7-7 8-11z" class="J"></path><path d="M342 605c1 3 2 6 0 8-1 2-2 3-3 4-1 0-2 0-2-1-1-1-1-1-1-3 0-1 0-5 1-6s3-2 5-2zm251-138c1-1 3-3 4-3 1 2 1 3 0 5 0 2-2 4-4 5-4 1-8 1-11-1l-3-1c1-1 3-1 5-1h5c2 0 3-2 4-4z" class="K"></path><path d="M575 868l1-1c2 1 3 3 5 5 1 2 3 3 4 5h0c-1 5-1 11-1 16-2-8-4-15-7-22l-1-2c0-1-1-1-1-1z" class="C"></path><path d="M575 868l1-1c2 1 3 3 5 5 1 2 3 3 4 5h-1v1 4c-2-4-3-9-7-11l-1-2c0-1-1-1-1-1z" class="E"></path><path d="M592 864c3 0 4 1 6 3 5 3 9 5 15 7 3 2 6 2 9 3h0l2 1c-3 0-8-1-11 0h-3 0v-2c-3-2-6-3-9-5-2-1-4-3-6-4l-3-3z" class="C"></path><path d="M330 609v-2c0-2 0-2 1-3 1-2 3-2 5-2 3 0 4 1 6 3-2 0-4 1-5 2s-1 5-1 6v-1h-3c-2 0-2-1-3-3z" class="B"></path><path d="M330 609h5c0 1 0 2 1 3h-3c-2 0-2-1-3-3z" class="O"></path><path d="M572 496c3-1 7-2 10-2h9l-13 6 10-2c-8 5-18 5-27 2h-1 4c0 1 1 0 2 0h-1v-1h0c2 0 7 1 9 0h1c3 0 5-1 8-2 1-1 2-1 4-2h-3c-1 0-2-1-2 0h-4c0 1-1 1-2 1h-4z" class="I"></path><path d="M635 628c0 5-7 12-6 17l-9 16h-1c4-12 9-23 16-33z" class="B"></path><path d="M313 603s1-1 2-1c0 1 0 0-1 1h1v4c0 4 1 7 3 10 1 3 4 6 6 9v1c-4-2-7-4-9-8-3-5-3-10-2-16z" class="H"></path><path d="M452 143c-3-1-6-1-9-2-2 0-4-1-5-2 4 0 7 1 11 2-5-3-10-5-14-11-2-3-1-6 0-9 2-6 6-12 12-15v1c-5 3-8 7-11 13-1 3-2 6 0 10s9 6 14 8l-1 2c0 1 1 1 1 1 1 1 2 1 2 2z" class="J"></path><path d="M666 603l1 1v2c0 1 0 2-1 3-1 2-6 7-8 8-2-1-3 0-4 1-1 0-2 0-2 1v-1-1c1-2 4-4 5-6 2-3 7-6 9-8z" class="O"></path><path d="M595 437v-5c-1-2-2-5-4-6-4-3-8-1-11 0l-2-2c0-2 1-3 2-4 6 0 10 0 14 5 1 1 3 4 2 7l-1 1c0 1 1 2 0 4z" class="B"></path><path d="M581 762v2c-2 4-5 7-7 11l-4 8c0 1-1 3-2 3h0c-1 1-1 1-2 1v-2c0-1 1-3 1-4 2-5 5-9 9-14 1-2 2-4 5-5z" class="C"></path><path d="M574 844c3 2 6 6 9 8 2 2 4 4 7 6l11 8-3 1c-2-2-3-3-6-3v-1c-1-1-4-3-5-4s-2-2-3-2h-2l-6-8h0c-1 0-2-4-2-5z" class="J"></path><path d="M388 792c11-7 17-16 21-28v1c1 4-4 12-6 15l1-1s1 0 1-1c2 0 3-2 4-3l1-1 2-3 1-1c-1 3-4 6-6 9-1 2-4 4-5 6-2 3-6 5-9 7h0c-1 1-2 1-3 1v-1c2 0 2-1 3-2-2 0-2 2-3 2h-2z" class="I"></path><path d="M538 849c2-3 4-5 6-6h6c2 2 3 3 3 5v2c-2 0-3-2-3-3h-3l1 1-1 1h-1c-2 0-3 1-5 1h0c-2 2-2 4-3 7v2l-1-1c1-2 0-4 1-6v-3h0z" class="P"></path><path d="M541 850c0-1 0-1 1-2 2-2 3-2 5-1l1 1-1 1h-1c-2 0-3 1-5 1z" class="R"></path><path d="M556 441c1 1 3 2 5 2h1l6 6c3-2 5-4 7-6h2c0 1 1 1 1 2 0 3-3 5-5 7h0c-3 1-7-2-10-3l-1-2c-4-1-5-3-6-6z" class="I"></path><path d="M575 868s1 0 1 1c-3 5-7 8-13 10-4 1-8 1-12-1-3-1-5-2-8-4h4l2 1c3 1 6 2 10 1h6c4-2 6-5 9-7l1-1z" class="P"></path><path d="M543 874h4l2 1c3 1 6 2 10 1h6c-5 2-9 2-14 2-3-1-5-2-8-4z" class="C"></path><path d="M666 558c3 2 7 5 9 9v1 1 2c-1 2-2 3-3 5v1l-2 3c-2 2-3 4-5 6 0-1 1-2 1-3 1-3 3-6 3-8 1-5 0-8-3-11v-1c1 1 2 2 3 2l1-2-4-4v-1z" class="I"></path><path d="M675 568v1 2c-1 2-2 3-3 5v1c0-2 0-6 1-8 1-1 1-1 2-1z" class="K"></path><defs><linearGradient id="D" x1="397.198" y1="646.191" x2="388.97" y2="624.744" xlink:href="#B"><stop offset="0" stop-color="#585658"></stop><stop offset="1" stop-color="#807f80"></stop></linearGradient></defs><path fill="url(#D)" d="M382 619c3 3 7 6 9 10 5 5 8 12 10 18 1 3 3 5 3 8-2-3-4-6-6-10-3-5-7-11-10-16-2-3-5-7-6-10z"></path><path d="M632 175c1 0 1-1 2-1 2 1 3 2 4 3 2 2 2 6 2 9-1 2-2 3-3 4-2 0-2-2-3-3-1-4-3-6-6-9h1c1 0 2 1 3 2l1 1c0-1 0-2-1-3v-3z" class="H"></path><path d="M278 555c2-1 7-1 10-1 3 1 6 3 7 6 1 1 1 2 1 4h-1c-6-3-9-6-16-5l-2-1h0c0-1 0-1 1-2v-1z" class="C"></path><path d="M575 465h1c4 0 8-1 13-2l4 4c-1 2-2 4-4 4h-5c-4-1-7-3-9-6z" class="L"></path><path d="M491 858h0c1 4 1 7 1 10-4 6-8 10-14 11-6 2-11 1-16-2-1 0-1-1-2-1h3c2 1 4 1 7 2h0c2 0 5 0 7-1h0c4-1 7-3 10-6 1-1 1-2 1-2 1-2 2-4 2-6s0-3 1-5z" class="Q"></path><path d="M594 719l1 3c-1 4-2 9-3 14-2 7-4 16-9 22v-3c0-2 2-5 3-8s2-7 2-10l6-18z" class="O"></path><defs><linearGradient id="E" x1="365.772" y1="569.242" x2="353.619" y2="574.64" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#E)" d="M355 565c1-2 4-3 6-5 1 1 3 2 4 4-4 1-6 3-7 7-2 3 0 8 1 12 0 1 0 2 1 4 1 1 1 3 1 4l-3-3c0-3-2-5-3-8v-8c0-3 1-5 0-7z"></path><path d="M538 849h0v3c-1 2 0 4-1 6l1 1v-2c1-3 1-5 3-7h0c2 0 3-1 5-1v2h-1l-4 4v9c1 1 2 3 2 3-1 2 0 3 0 4 1 1 3 2 4 3h-4c-3-3-5-6-6-9-1-6-1-12 1-16z" class="E"></path><path d="M590 317c2 0 4 0 6 1h1c5 1 9 2 13 5 2 1 3 3 5 3 1 2 2 3 3 4 1 2 2 4 2 6-1-2-3-4-4-6-3-4-9-7-14-8l-1-1c-4-1-9 0-13 0-1-1-2-1-3-2h0l-1-1 6-1z" class="T"></path><path d="M590 317c2 0 4 0 6 1h1c-3 0-5 0-8 1h-4 0l-1-1 6-1z" class="N"></path><defs><linearGradient id="F" x1="210.389" y1="252.381" x2="195.977" y2="265.873" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#F)" d="M210 250l3 2h-1c-1 2-3 3-5 4-4 3-5 7-6 12-2 0-3 0-4 2 0-4 0-8 2-11s5-8 9-8c1-1 1 0 1-1h1z"></path><path d="M639 358l9 44c-2-4-4-8-5-12-3-8-5-16-7-24 1-1 0-1 1-1 1-3 2-3 2-7z" class="K"></path><path d="M453 768c2 2 4 4 5 7l1 1c3 5 5 11 7 16l3 8c0 1 1 3 1 3-1 3 0 4-1 7 0-2 0-4-1-5-1-2-2-7-4-9-2-4-4-10-6-14 1-3-2-7-3-9s-2-3-2-5z" class="N"></path><path d="M360 587c1 2 4 4 6 5v1c2 2 4 4 6 5s3 4 5 5l11 17h0c-1-1-2-2-3-2h0l-2-1-2-2v-1c-1-2-3-3-4-5-2-2-3-5-4-7-2-2-6-5-8-7l-4-4c0-1 0-3-1-4z" class="S"></path><path d="M597 313c2 1 4 1 5 2 7 1 12 5 15 11 1 1 1 2 1 4-1-1-2-2-3-4-2 0-3-2-5-3-4-3-8-4-13-5h-1c-2-1-4-1-6-1l3-1 1-1h-2c2-1 3-1 5-2z" class="C"></path><path d="M602 315c7 1 12 5 15 11 1 1 1 2 1 4-1-1-2-2-3-4-1-1-2-3-3-4l-1-1c-4-3-8-3-12-5v-1h3 0z" class="E"></path><path d="M420 746l1-2c0 3 0 6-1 9-1 7-2 15-7 21-1 2-3 4-4 6-2 4-8 9-11 11h-1c-1 0-1 1-2 1h-2 0c3-2 7-4 9-7 1-2 4-4 5-6 2-3 5-6 6-9 4-8 4-16 7-24z" class="D"></path><defs><linearGradient id="G" x1="575.389" y1="487.911" x2="560.569" y2="475.418" xlink:href="#B"><stop offset="0" stop-color="#747474"></stop><stop offset="1" stop-color="#989797"></stop></linearGradient></defs><path fill="url(#G)" d="M557 475c2 1 3 1 5 1 4 3 9 7 14 9 1 0 3 1 4 1v1c2 1 7 0 9 1 2 0 3 0 4 1h-6c-12 1-22-4-31-12l1-2z"></path><path d="M601 866c4 2 8 3 12 5 6 3 13 3 20 1l1 2c1 0 1 1 2 0h3c-3 1-5 2-8 3l-7 1-2-1h0c-3-1-6-1-9-3-6-2-10-4-15-7l3-1z" class="B"></path><path d="M613 874c3 0 5 1 7 1 3 0 5 0 7 1l4 1-7 1-2-1h0c-3-1-6-1-9-3z" class="L"></path><path d="M661 588c0 1 0 1-1 2-2 2-3 5-3 8h1c0 1 0 1-1 2-5 4-9 10-14 15-1-2-1-2 0-4 1-1 1-2 1-3 5-8 10-14 17-20z" class="B"></path><defs><linearGradient id="H" x1="806.204" y1="248.972" x2="815.055" y2="267.941" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#H)" d="M805 249c4 1 9 5 11 10 2 3 2 6 1 10l-2 3c-1 0-2-1-3-1l1-1c0-2 1-4 0-6 0-6-7-10-12-13l4-2z"></path><path d="M813 270c1-1 1-2 2-2s1 0 2 1l-2 3c-1 0-2-1-3-1l1-1z" class="E"></path><path d="M655 621c7 1 12 4 16 10 3 4 5 10 4 15-1 2-1 3-1 4 0-7 0-12-6-17-2-3-7-6-11-7l-4 1-2-4c2-1 3-1 4-2z" class="K"></path><path d="M153 258c4-3 9-3 14-2 3 1 6 3 8 6 2 4 2 7 2 11l-1-1h0l-1-1c0-4-2-7-5-9s-8-2-12-1c-2-1-3-2-5-3z" class="E"></path><defs><linearGradient id="I" x1="683.551" y1="205.256" x2="720.949" y2="203.744" xlink:href="#B"><stop offset="0" stop-color="#6d6d6d"></stop><stop offset="1" stop-color="#989798"></stop></linearGradient></defs><path fill="url(#I)" d="M727 207l-51-3c3-1 7-1 11-1h32v1c3 1 5 1 8 3z"></path><path d="M306 431c2 3 4 6 5 9 5 9 8 17 11 26 1 2 2 5 2 7 0 3 2 6 2 9 1 2 1 5 2 7v1c0 1 0 1 1 2l-1 1v-1h-1c0-1-1-3-1-4l-4-15c-2-7-5-14-7-20-1-2-3-4-3-6-1-3-2-5-2-8l-4-8z" class="N"></path><defs><linearGradient id="J" x1="343.837" y1="207.599" x2="310.163" y2="201.401" xlink:href="#B"><stop offset="0" stop-color="#5a5b5a"></stop><stop offset="1" stop-color="#727172"></stop></linearGradient></defs><path fill="url(#J)" d="M299 203l55 1c-3 1-7 1-11 1l-23 1c-5 0-11 1-16 0 0-1 1-1 1-2l-6-1z"></path><defs><linearGradient id="K" x1="336.865" y1="619.673" x2="320.45" y2="624.15" xlink:href="#B"><stop offset="0" stop-color="#696869"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#K)" d="M318 617h2v1h1c3 3 6 4 10 4l7-1 1 1c-1 2-2 4-4 6h2c3-1 5-1 7-3h1c-2 2-4 3-6 3v1h-3c-4 1-9 0-12-2v-1c-2-3-5-6-6-9z"></path><path d="M818 155c4 0 7 1 10 4 2 2 3 5 3 9-1 4-3 9-6 12v-1c2-3 2-7 2-11-1-4-4-9-9-9-2 0-4 1-5 0v-2c1-1 3-2 5-2z" class="G"></path><path d="M198 307c9-3 19-2 29 2 5 2 11 6 15 10 0 0-1 0-1 1-3-1-6-4-8-6-9-5-21-8-30-5-5 1-8 2-10 7l-1-4c2-2 4-3 6-5z" class="E"></path><path d="M188 315c2-1 3-2 4-3l1 4-1 3c0 2 1 2 2 4 2 0 4 0 6-1 1-2 1-4 1-5l1-1 1 1c2 1 2 2 2 4s0 4-2 5c-1 1-4 2-6 2-3 0-5-1-7-3-2-3-2-7-2-10z" class="F"></path><path d="M329 503c1 4 1 8 2 12 1 8 1 18 0 27-1 4-2 9-4 14-1 2-1 5-3 6l-1 2c1-6 3-11 5-16 3-13 1-28 0-41l1-4z" class="P"></path><path d="M528 601c1 0 2 0 2 1h1c3 11 8 24 15 33 2 3 6 6 9 8-7-2-13-6-18-11h2l-1-2v-1c0-1 0-2-1-4v-1l-2-5-1-1-6-17z" class="F"></path><path d="M571 852c2 2 3 3 3 6 2 4 5 11 9 13l1 1 1-1c2 4 3 8 5 13l-5-7h0c-1-2-3-3-4-5-2-2-3-4-5-5l-1 1-1 1c-3 2-5 5-9 7h-6c2-1 3-1 5-2h0c2-1 4-4 5-5l1-1c0-1 1-2 2-3v-1-1c0-1 0-2-1-3h0c1-2 1-5 0-8z" class="B"></path><path d="M210 158h-3c-2 1-4 4-5 7-3 5 0 11 2 16-4-3-7-7-7-13 0-4 1-7 4-10 2-3 5-4 9-4 2 0 4 1 6 3 0 1 0 1-1 1-2-1-3-1-5 0z" class="G"></path><path d="M557 516c-1 0-1-1-1-2 1-1 1-1 2-1 0 0 1 0 1 1h2c1 1 0 1 1 1v-2c6 4 14 7 22 9 2 0 6 0 8 1s6 2 9 3c-9 0-16-1-24-4-2-1-6-3-8-3-1 1-1 1 0 2l-1 1c-4-2-8-3-11-6z" class="J"></path><path d="M751 562c-1-1-5-3-6-3-2-1-5-1-6 0-3 1-6 5-8 4 0-3 1-5 3-6 3-3 8-4 12-3 1 0 1 0 2 1h1c4 2 7 6 8 10 0 1 1 4 1 6-1-1-1-1-2 0v-4c-1-2-3-4-5-5z" class="E"></path><path d="M448 870c1 1 4-1 7-1l2-2c1 0 1 0 1 1 4 5 7 7 13 8 2 1 4 1 6 1h0c-2 1-5 1-7 1h0c-3-1-5-1-7-2h-3c-2-1-4-4-5-5h-2c-3 6-5 13-7 20 0-5 0-10 1-14v-1c-2 1-4 3-5 5 1-4 3-8 6-11z" class="C"></path><path d="M455 871l1-1v-1h1v1c2 3 4 4 6 6h-3c-2-1-4-4-5-5z" class="S"></path><defs><linearGradient id="L" x1="381.078" y1="803.205" x2="371.422" y2="791.795" xlink:href="#B"><stop offset="0" stop-color="#908e90"></stop><stop offset="1" stop-color="#b2b3b0"></stop></linearGradient></defs><path fill="url(#L)" d="M398 791c3-2 9-7 11-11 1-2 3-4 4-6l1 2c-7 11-19 21-32 24-6 1-11 1-16 0h-1c-4-1-8-2-11-5 2 1 3 1 5 2h0 1c1 1 2 1 3 1h-1l1-1 6 1c6 2 12 0 19-2 3-1 7-3 10-5z"></path><path d="M433 731h-1c0-1 0-3-1-4l-1-7c-1-5-3-10-4-15h0c2 3 3 6 5 9 4 10 8 21 11 31 1 3 1 3 0 5l-2-4-7-15z" class="T"></path><path d="M578 319c2 0 4-1 6-1l1 1c-3 1-6 1-10 3l-6 3c-1 1-1 2-2 4-9 6-16 14-23 23 1-4 5-7 6-12 1-3 4-4 5-7 7-6 15-11 23-14z" class="O"></path><path d="M685 602h1c2-1 5 1 7 2 1 1 2 3 1 5 0 3-2 5-4 7-2 1-3 1-5 1-1-3-1-5-2-7s-1-4-2-5c1-2 2-2 4-3z" class="B"></path><path d="M687 604c2 0 3 0 5 1 1 1 2 2 2 4l-1 1c-1 1-1 2-2 3l-4 2h-1c0-2-1-3-1-4 1-3 1-5 2-7z" class="G"></path><path d="M270 563c2-4 4-6 8-8v1c-1 1-1 1-1 2h0l2 1c-2 1-4 2-4 3 1 1 4 0 6 1-3 4-7 6-6 12 1 3 4 6 7 7 4 2 8 3 13 1 2-1 5-2 7-3 0 1 0 2-1 2-4 3-11 5-16 4h0l-1-1h-1-1c-3-1-7-5-8-8v-1c-2-4-2-7-2-12l-2-1z" class="D"></path><defs><linearGradient id="M" x1="558.01" y1="791.755" x2="572.032" y2="815.315" xlink:href="#B"><stop offset="0" stop-color="#7d7d7d"></stop><stop offset="1" stop-color="#bab7b8"></stop></linearGradient></defs><path fill="url(#M)" d="M562 797c1-4 2-8 4-12v2c1 0 1 0 2-1h0c-4 12-5 22-1 34l1 3 2 4s-2-1-2-2l-1-1c0 3 2 6 2 9v3c-3-7-6-14-7-20-1-7-1-13 0-19z"></path><path d="M568 823h-3l1-3h1l1 3z" class="D"></path><path d="M584 312c2-1 4-1 6-1l2 1 5 1c-2 1-3 1-5 2h2l-1 1-3 1-6 1c-2 0-4 1-6 1l-1-1-16 5c2-2 3-3 5-4l-1-1c2-1 4-1 6-3 4-2 9-3 13-3z" class="P"></path><path d="M592 312l5 1c-2 1-3 1-5 2h-5c1 0 1-1 2-1l3-2z" class="U"></path><path d="M587 315h5 2l-1 1-3 1-6 1c-2 0-4 1-6 1l-1-1c3-1 7-2 10-3z" class="E"></path><path d="M571 315c4-2 9-3 13-3h4c-1 1-3 0-5 1-1 1-1 1-2 1-5 1-10 4-15 5l-1-1c2-1 4-1 6-3z" class="O"></path><path d="M821 309c2 1 4 4 6 4h0c2 3 1 7 0 10-1 2-3 4-5 5s-5 0-7-1-3-2-4-4c0-1 0-3 1-4 1-2 2-3 4-3h1c0 2 0 4 1 6 2 1 2 0 4 0 1 0 1-1 2-2 0-4-1-6-3-8v-1-2z" class="I"></path><defs><linearGradient id="N" x1="608.157" y1="410.457" x2="618.744" y2="393.609" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#c2c0c0"></stop></linearGradient></defs><path fill="url(#N)" d="M617 384c0-1 3-4 4-5 2-2 3-4 5-6h0v1c-2 6-8 11-12 16l1 2c4-4 9-11 12-16l1-1h1c-3 6-8 11-11 15-3 3-5 5-7 8-1 2-1 3-1 5 1 4 5 7 6 11-1 1-2 1-3 1-3-2-5-5-6-9-1-5 1-10 4-14 1-2 6-6 6-8z"></path><defs><linearGradient id="O" x1="688.669" y1="618.369" x2="702.331" y2="623.631" xlink:href="#B"><stop offset="0" stop-color="#727071"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#O)" d="M707 610c1 1 1 1 1 2-1 4-4 8-3 12h0c-2 2-4 3-7 4-4 2-6 0-10-1s-6-3-9-5c5 1 10 2 16 0 3-1 5-2 6-5 2-2 2-3 4-4 1-1 2-2 2-3z"></path><path d="M701 617c2-2 2-3 4-4 1 2 1 2 1 4-1 0-2 1-3 1-1-1-1-1-2-1z" class="D"></path><path d="M688 627l1-1h4 0c2 0 4 1 5 2-4 2-6 0-10-1z" class="C"></path><path d="M385 869l-1-1c-4-4-8-9-8-15 0-3 1-7 3-9s3-3 6-3h1l-3 3c-2 1-2 2-2 4-1 3 0 6 1 9 3 5 6 8 11 11 2 3 4 2 7 3-1 0-2 1-3 0-2 0-3 0-5-1h-1-2c-1-1-2-1-4-1zm-59-282c3-2 8-1 12-1 2 1 4 2 7 3h0v2c-2 0-3 0-5-1l-1 1c-2 1-5 1-8 2-1 0-6 2-8 2s-2 0-4 2h0c-2 1-3 4-3 5l-1 5v-4h-1c1-1 1 0 1-1-1 0-2 1-2 1l1-2c2-5 7-12 12-14z" class="K"></path><path d="M330 589c3 0 7-1 9 0-1 1-2 1-3 1s-2 1-2 1c-2 0-4 0-5-1l1-1z" class="F"></path><path d="M326 587c3-2 8-1 12-1 2 1 4 2 7 3h0v2c-2 0-3 0-5-1h0l-1-1c-2-1-6 0-9 0h0l3-1h4c-1 0-2-1-3-1h0c-3 0-5-1-7 0l-1 1v-1z" class="G"></path><path d="M270 154l2 2 2-1c3 4 4 9 3 14 0 3-2 5-4 7 0-1-1-1-1-1h-4-4-1c-1-1-1-2-1-3 1-3 5-4 8-5 1-1 2-2 4-3-1 0-1-3-2-4-1-2-2-3-3-4l1-2z" class="I"></path><path d="M270 154l2 2 1 1c0 1 0 2 1 3s2 2 1 3v3l-1-2c-1 0-1-3-2-4-1-2-2-3-3-4l1-2z" class="J"></path><path d="M262 172c1-3 5-4 8-5 1-1 2-2 4-3l1 2c0 3-1 5-3 7-1 0-2 1-3 0-2 0-5-1-7-1z" class="L"></path><defs><linearGradient id="P" x1="761.223" y1="170.766" x2="751.195" y2="151.806" xlink:href="#B"><stop offset="0" stop-color="#9c9b9b"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#P)" d="M753 176c-3-1-5-4-6-7-1-4-1-8 1-11 3-6 7-10 13-12 2 1 3 1 5 2h3c-6 0-10 1-15 6-3 3-5 7-5 12 1 0 1 1 2 2 0 1 2 1 3 1s5-1 6-2c1 1 2 2 2 4-2 1-4 3-6 4-1 0-2 0-3 1z"></path><path d="M488 858c1 1 2 3 2 5s-1 4-2 6c0 0 0 1-1 2-3 3-6 5-10 6-2 0-4 0-6-1-6-1-9-3-13-8 0-1 0-1-1-1l-2 2c-3 0-6 2-7 1l4-8c0 1 0 2-1 4h2c3-1 4-4 6-7 0 3 0 4 2 6 1 2 4 4 6 6 4 2 7 3 12 2 3-1 7-3 9-6 1-3 1-6 0-9z" class="B"></path><defs><linearGradient id="Q" x1="847.735" y1="256.619" x2="853.648" y2="269.812" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#Q)" d="M841 278c-2-3-2-6-2-10s2-7 5-10c3-2 8-3 12-2 3 0 7 3 10 6 1 2 1 2 0 4-1 0-5-3-7-4s-3-1-6-1-6 1-8 4c-4 3-4 7-3 12l-1 1z"></path><defs><linearGradient id="R" x1="577.279" y1="391.708" x2="577.011" y2="401.459" xlink:href="#B"><stop offset="0" stop-color="#2f2f2e"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#R)" d="M548 387c7 5 14 9 23 11 14 3 24 0 36-6 0 0 0 1-1 2-4 5-11 7-17 8-12 2-26-1-36-8-2-2-4-3-5-5v-2z"></path><path d="M439 851c2 0 4 0 7-1h1v1c-2 1-3 2-5 3-5 3-9 7-15 10-1 0 0 0-1 1-6 0-12 1-19 1-2 0-5-1-7-2 4-1 8-1 13-2 10-1 17-5 26-11z" class="K"></path><path d="M691 506c0-31 10-63 28-89 0 2 0 2-1 3s-2 4-2 6c-1 0-1 1-2 2v1c-1 1-1 1-1 2-2 5-6 11-7 16-1 1-1 3-2 4-1 2-2 5-2 7-4 12-8 24-9 36-2 4 0 8-2 12z" class="S"></path><defs><linearGradient id="S" x1="476.046" y1="831.383" x2="453.867" y2="836.759" xlink:href="#B"><stop offset="0" stop-color="#2f2f2e"></stop><stop offset="1" stop-color="#4e4e4f"></stop></linearGradient></defs><path fill="url(#S)" d="M470 803c4 15-1 31-5 45-2 5-4 11-4 17-2-2-2-3-2-6l2-10c1-3 3-9 1-12v-1c3-2 5-8 6-11 1-5 1-11 1-15 1-3 0-4 1-7z"></path><path d="M410 749l1-1c1 0 2 1 3 0 1 0 1-1 2-1s2 0 4-1c-3 8-3 16-7 24l-1 1-2 3-1 1c-1 1-2 3-4 3 0 1-1 1-1 1l-1 1c2-3 7-11 6-15v-1l1-15z" class="F"></path><path d="M488 844h1c1 1 1 2 1 3l1 1c0 1 1 2 1 3h1c1 1 1 2 1 3 0 0 0 1 1 2l-1 2c-1 4 0 7-2 10 0-3 0-6-1-10h0c-1 2-1 3-1 5 0-2-1-4-2-5s-2-3-4-4-6-1-8-3c-1-1-1-1-1-2 1-2 2-2 4-3 2-2 6-2 9-2z" class="C"></path><path d="M488 848h1l2 1c-1 3-1 5 0 7v2h0c-1-3-3-4-5-7l2-2v-1z" class="N"></path><path d="M488 844h1c1 1 1 2 1 3l1 1c0 1 1 2 1 3h1c1 1 1 2 1 3 0 0 0 1 1 2l-1 2c-1-3-1-6-3-9l-2-1h-1c-3 1-5 1-8 2h0v-1c2-2 6-2 9-3h0l-1-2z" class="R"></path><path d="M491 849c2 3 2 6 3 9-1 4 0 7-2 10 0-3 0-6-1-10v-2c-1-2-1-4 0-7z" class="M"></path><path d="M569 836v-3c0-3-2-6-2-9l1 1c0 1 2 2 2 2 6 9 13 15 23 19l9 3h-1c-5-1-9-1-14-2-2 0-5-2-7-3 0 1 0 2 1 3 0 2 2 3 2 5-3-2-6-6-9-8-1-1-3-7-4-8h-2 1z" class="G"></path><path d="M570 836v-1l1 1c2 3 7 5 9 8 0 1 0 2 1 3 0 2 2 3 2 5-3-2-6-6-9-8-1-1-3-7-4-8z" class="F"></path><defs><linearGradient id="T" x1="437.414" y1="868.772" x2="429.836" y2="862.438" xlink:href="#B"><stop offset="0" stop-color="#545455"></stop><stop offset="1" stop-color="#6d6c6b"></stop></linearGradient></defs><path fill="url(#T)" d="M454 849c-5 10-14 20-24 25-4 2-8 3-11 5 0-1-1-1-2-2-3-1-8 0-11 0l9-1h1c2 0 5-3 7-4s5-2 7-4l14-12c4-1 6-6 10-7z"></path><path d="M568 851l-1-1c-1-2-2-6-3-8-6-17-8-34-3-50v5h1c-1 6-1 12 0 19 1 6 4 13 7 20h-1c-1 1-1 1-1 3l-2-1c0 4 3 8 3 13z" class="M"></path><path d="M562 816c1 6 4 13 7 20h-1c-1 1-1 1-1 3l-2-1c-1-6-3-10-4-16 0-1-1-3 0-4l1-2z" class="E"></path><defs><linearGradient id="U" x1="580.778" y1="871.814" x2="570.041" y2="835.89" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#U)" d="M567 839c0-2 0-2 1-3h2c1 1 3 7 4 8 0 1 1 5 2 5 2 8 6 15 9 22l-1 1-1-1c-4-2-7-9-9-13 0-3-1-4-3-6 1 3 1 6 0 8l-3-9c0-5-3-9-3-13l2 1z"></path><path d="M565 838l2 1 4 13c1 3 1 6 0 8l-3-9c0-5-3-9-3-13z" class="O"></path><path d="M589 547c-1-2-1-4-3-6-2-1-7-2-9-1l-6 2c4-3 8-5 13-5 4 1 6 2 8 5 3 4 4 9 3 13-1 5-4 9-8 12-3 2-6 3-9 4l1-2c1 0 2-1 4-2 2-3 4-6 4-9 1-2 3-4 3-6h-1v-1-4z" class="D"></path><defs><linearGradient id="V" x1="652.988" y1="801.268" x2="660.978" y2="782.019" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#ccccca"></stop></linearGradient></defs><path fill="url(#V)" d="M623 784c2 0 2 1 4 2 0 1 0 1 1 1v-1c4 2 6 5 10 6 3 2 5 3 9 4v1c3 1 6 1 9 2h6c-2-1-3-1-5-1h1l2-2c9-1 14-3 20-9l1 1-2 1c1 1 2 2 3 2-4 4-9 8-15 9h-1c-5 1-9 1-14 1-10-2-22-9-29-17z"></path><path d="M631 642c7-11 15-23 26-31-1 2-4 4-5 6v1 1c0-1 1-1 2-1 1-1 2-2 4-1l-4 4h0 1c-1 1-2 1-4 2l2 4c-4 2-7 4-10 9-2 0-3 1-5 2-1 1 0 1-1 1-1 2-4 6-5 7h-1l1-1c-1-1-1-2-1-3z" class="T"></path><path d="M652 619c0-1 1-1 2-1 1-1 2-2 4-1l-4 4h0 1c-1 1-2 1-4 2s-4 3-6 4c1-3 4-6 7-8z" class="P"></path><path d="M645 627c2-1 4-3 6-4l2 4c-4 2-7 4-10 9-2 0-3 1-5 2-1 1 0 1-1 1 2-4 5-8 8-12z" class="L"></path><path d="M424 794v-1c1 0 3-1 3-1 2 0 2 1 3 0 0 2 0 4-1 6-1 1-2 3-1 4-5 8-14 15-23 17-4 1-9 0-13 0 11-3 23-7 29-18 2-2 3-5 3-7z" class="D"></path><defs><linearGradient id="W" x1="615.037" y1="797.323" x2="585.292" y2="793.717" xlink:href="#B"><stop offset="0" stop-color="#acaeab"></stop><stop offset="1" stop-color="#d2d1d1"></stop></linearGradient></defs><path fill="url(#W)" d="M589 762l1 3h0 0c1-1 1-2 1-2 1 1 1 1 0 2h0v1c-1 2-1 5-1 7-2 11-2 23 3 34 4 8 11 15 19 19l9 3c-3 0-6 0-9-1-6-2-12-6-16-11-3-3-4-8-7-11l2 7h-1c-1-3-2-7-3-11-2-13 0-27 2-40z"></path><path d="M438 758v1c3 8 7 15 8 24 1 7 1 15-1 22-1 2-1 5-3 7 0-2 1-4 1-5-7 10-12 17-25 22h-7c1 0 3 0 3-1 3-1 5-2 8-3 4-2 8-5 10-8 10-11 13-25 10-39-2-7-4-13-4-19v-1z" class="I"></path><defs><linearGradient id="X" x1="604.438" y1="904.806" x2="620.708" y2="903.95" xlink:href="#B"><stop offset="0" stop-color="#a6a5a6"></stop><stop offset="1" stop-color="#cdcbcb"></stop></linearGradient></defs><path fill="url(#X)" d="M613 885c1-1 1-2 2-3 3 4 4 8 6 13 2 10 0 18-5 27 0-3 1-8 2-11-5 8-10 17-20 19-5 2-10 2-14 0h-3c-1 0 0 0-1-1 9 1 15 3 23-4 6-5 11-13 13-20v-1c1-3 0-4-1-7 0-4 0-8-2-12z"></path><path d="M157 175h1v1h-1c-3 2-6 5-7 8s0 7 2 10c6 13 21 15 34 19l-11-1c-10 0-20-1-28-8l8 1c-3-3-8-7-9-11v-1c0-3 0-7 2-10 2-4 5-6 9-8z" class="H"></path><defs><linearGradient id="Y" x1="667.12" y1="773.137" x2="688.087" y2="773.821" xlink:href="#B"><stop offset="0" stop-color="#a5a3a3"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#Y)" d="M680 787c3-6 5-11 4-17-1-3-3-5-5-6s-4-2-6-1c-3 1-3 3-5 5-1-1-1-2-1-3v-3-1c0-1 2-3 3-4 3-1 6-1 9 0 4 1 7 4 9 8 2 6 2 12-1 17-1 4-3 7-5 9-1 0-2-1-3-2l2-1-1-1z"></path><defs><linearGradient id="Z" x1="424.114" y1="901.438" x2="408.444" y2="901.706" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#Z)" d="M417 877c1 1 2 1 2 2-4 8-7 19-5 28 3 7 8 13 14 18h-1-1-1l1 1h0c-4 0-10-8-13-11l1 8c-3-3-5-9-5-13-3-13 1-22 8-33z"></path><defs><linearGradient id="a" x1="568.153" y1="454.176" x2="597.376" y2="425.941" xlink:href="#B"><stop offset="0" stop-color="#9a9a9a"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#a)" d="M580 420c3-2 6-3 9-2 4 1 7 5 9 8 2 4 2 11 0 16s-6 11-11 13c-6 2-13 3-18 0-3-1-6-3-8-5h2v-1c3 1 7 4 10 3h0c1 0 1 1 2 1 3 1 8 0 11-2 5-3 8-8 9-14 1-2 0-3 0-4l1-1c1-3-1-6-2-7-4-5-8-5-14-5z"></path><path d="M342 560v-3c1-6 2-10 7-13v5l-1 3-2 3v2c1 2 2 3 3 5v4c1 2 1 5 3 7 0-3 1-5 3-8 1 2 0 4 0 7v8c1 3 3 5 3 8l-5-5c-5-4-8-9-12-14l1-9z" class="D"></path><path d="M342 560h0v4h1l3 8c1 3 4 5 5 7 1 1 2 2 2 4-5-4-8-9-12-14l1-9z" class="J"></path><path d="M346 558v-1c1 2 2 3 3 5v4c1 2 1 5 3 7 0 2 1 3 0 5 0-1-1-1-1-2-1-1-1-2-2-2-3-5-4-11-3-16z" class="L"></path><path d="M346 558v-1c1 2 2 3 3 5v4c-2-2-3-5-3-8z" class="C"></path><defs><linearGradient id="b" x1="740.253" y1="355.142" x2="751.125" y2="357.437" xlink:href="#B"><stop offset="0" stop-color="#545453"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#b)" d="M757 323h0l1-1 1 1-1 1h1c-1 3-6 11-6 14-1 4-2 7-4 11l-6 18c0 2-1 5-1 8-2 7-7 16-12 22 0-1 1-3 1-4l4-10c5-17 9-34 16-50 2-3 3-7 6-10z"></path><defs><linearGradient id="c" x1="633.875" y1="858.548" x2="653.885" y2="857.711" xlink:href="#B"><stop offset="0" stop-color="#a0a09f"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#c)" d="M643 842c1-1 3-1 5 0 2 0 5 2 6 4 2 3 1 9 0 12-3 7-8 12-14 15l-1 1h-3c-1 1-1 0-2 0l-1-2c-7 2-14 2-20-1 4 0 7 1 11 1 2 0 4-1 6-1 1 0 1-1 2-1 3-2 7-3 11-5 1-2 4-3 5-5 1-4-1-10-3-13l-2-1h0l-1-3 1-1z"></path><path d="M642 843l1-1v1h2c2 1 4 2 5 4h0-1c-1-1-2-2-3-2l-1 2-2-1h0l-1-3z" class="M"></path><path d="M632 870c3-2 7-3 11-5v1l-3 2c1 0 2-1 3-1h1c-3 2-8 5-11 5-7 2-14 2-20-1 4 0 7 1 11 1 2 0 4-1 6-1 1 0 1-1 2-1z" class="D"></path><defs><linearGradient id="d" x1="610.469" y1="649.06" x2="628.799" y2="711.156" xlink:href="#B"><stop offset="0" stop-color="#323130"></stop><stop offset="1" stop-color="#4f5052"></stop></linearGradient></defs><path fill="url(#d)" d="M629 645l2-3c0 1 0 2 1 3l-1 1h1c-9 16-16 32-19 50-1 9-2 17-4 26-1-2-1-3-1-4l-1-1c-1-8 7-50 12-56h1l9-16z"></path><path d="M209 287v-1s-1-1-2-1h0v-2h0l-1-1c0-1 1-1 2-1l3 3h1c6 3 12 6 18 10 8 5 16 11 21 19-1 0-3 0-5-1-9-8-21-15-32-19-6-2-12-4-17-7 4 1 8 2 12 2v-1z" class="K"></path><defs><linearGradient id="e" x1="272.579" y1="351.419" x2="262.563" y2="354.537" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#7e7d7f"></stop></linearGradient></defs><path fill="url(#e)" d="M246 312c2 1 4 1 5 1 4 4 6 9 9 14 8 15 14 30 20 46l8 26c-2-4-4-7-6-11-1-1-1-2-1-2 0-5-15-40-18-46l-9-15c0-3-6-9-8-13z"></path><path d="M667 596c2 1 5 1 7 1h0-1l-2 1c-1 1-2 3-2 4l-3 1c-2 2-7 5-9 8-11 8-19 20-26 31l-2 3c-1-5 6-12 6-17 1-2 3-4 4-6l3-3c0-1 0-3 1-4 5-5 9-11 14-15 3-2 7-3 10-4h0z" class="G"></path><path fill="#1f1f1f" d="M667 596c2 1 5 1 7 1h0-1l-2 1c-12 3-22 11-29 21 0-1 0-3 1-4 5-5 9-11 14-15 3-2 7-3 10-4h0z"></path><path d="M442 854c1 0 2 0 4-1h0c3-2 5-4 8-5v1c-4 1-6 6-10 7l-14 12c-2 2-5 3-7 4s-5 4-7 4h-1l-9 1c-3-1-6-1-9-2-2-1-4-1-7-3-1 0-3-2-5-3 2 0 3 0 4 1h2 1c2 1 3 1 5 1 1 1 2 0 3 0-3-1-5 0-7-3 5 2 8 3 13 3 5-1 17-3 20-6 1-1 0-1 1-1 6-3 10-7 15-10z" class="F"></path><path d="M390 872h5c6 2 12 0 18 0l-3 2h-3c-1 1-4 0-5 0-2 0-3 1-5 1-2-1-4-1-7-3z" class="B"></path><path d="M413 872l5-1h1c1 0 3-1 4-1 1-1 1-1 2-1h2c1-1 1-1 3-1-2 2-5 3-7 4s-5 4-7 4h-1l-9 1c-3-1-6-1-9-2 2 0 3-1 5-1 1 0 4 1 5 0h3l3-2z" class="J"></path><defs><linearGradient id="f" x1="611.119" y1="849.957" x2="596.634" y2="864.281" xlink:href="#B"><stop offset="0" stop-color="#c1c1c1"></stop><stop offset="1" stop-color="#e9e8e7"></stop></linearGradient></defs><path fill="url(#f)" d="M581 847c4 2 7 3 11 5 13 7 25 11 40 12-7 2-14 2-21 3 7 3 13 4 21 3-1 0-1 1-2 1-2 0-4 1-6 1-4 0-7-1-11-1-4-2-8-3-12-5l-11-8c-3-2-5-4-7-6 0-2-2-3-2-5z"></path><path d="M808 277c1 1 2 1 3 2l-1 2c1 0 4-2 6-3-4 4-8 6-12 9v1h1 1l1 3c-10 4-20 8-29 14-4 3-9 6-13 11-1-1-3-1-4-1 4-5 7-9 12-13 4-3 9-6 13-10 3-2 4-5 7-6 0 1-1 2-1 3 6-2 11-7 16-12z" class="G"></path><path d="M338 586c-6-1-12-3-17-6l25 4c0-1-5-4-5-5-4-4-7-8-6-14 0-1 0-3 1-4l5 8c4 5 7 10 12 14l5 5 3 3 4 4-1 1h-5c-1 1-2 0-3 0l-1 1c-3 0-6-1-8-2v-3l-2-1v-2h0c-3-1-5-2-7-3z" class="H"></path><path d="M347 592l9 4h0l-1 1c-3 0-6-1-8-2v-3z" class="F"></path><path d="M539 375l2-6c2-3 4-5 7-6s6-1 8 1l1 1c-1 1-1 2-3 2-5 4-7 8-9 14-3 23 4 44 17 62h-1c-2 0-4-1-5-2 0-1-3-4-3-4-3-5-5-9-7-14-2-7-4-14-5-20 0-4-1-8-1-11 0-5 1-13-1-17z" class="H"></path><path d="M556 364l1 1c-1 1-1 2-3 2-5 4-7 8-9 14v-2c0-3 1-10 4-12 1-2 3-3 5-3h2 0z" class="E"></path><path d="M789 198h1c5-1 9-3 13-5 10-5 20-11 31-11 4 0 8 1 11 4 2 1 4 3 5 6-2-1-3-3-5-4-6-3-14-2-20 0-7 3-14 7-21 10-14 7-29 9-44 9h-11l-2-1c-5-1-10 0-15-2 1 0 5-1 7 0 2 0 3 1 6 0 5 0 10 1 15 0 5 0 10-1 15-2 4 0 9-1 12-3l1-1h1z" class="G"></path><defs><linearGradient id="g" x1="384.257" y1="770.037" x2="345.569" y2="786.012" xlink:href="#B"><stop offset="0" stop-color="#9c9b9b"></stop><stop offset="1" stop-color="#d5d5d4"></stop></linearGradient></defs><path fill="url(#g)" d="M354 795c-1 0-1-1-2-2-6-5-11-12-11-20 0-4 1-9 5-12 2-3 6-5 10-5 3 0 5 1 7 3 1 1 1 3 1 5s-1 3-2 4c-1-2-1-4-3-5s-4-1-6 0-4 3-6 6c-1 3-1 8 1 11 2 7 8 11 14 14 9 3 18 1 26-2h2c1 0 1-2 3-2-1 1-1 2-3 2v1c1 0 2 0 3-1h2c1 0 1-1 2-1h1c-3 2-7 4-10 5-7 2-13 4-19 2l-6-1-1 1h1c-1 0-2 0-3-1h-1 0c-2-1-3-1-5-2z"></path><defs><linearGradient id="h" x1="466.034" y1="854.324" x2="448.103" y2="833.539" xlink:href="#B"><stop offset="0" stop-color="#c2c1c0"></stop><stop offset="1" stop-color="#e7e6e6"></stop></linearGradient></defs><path fill="url(#h)" d="M465 817c2 2 2 3 3 5v1 2c-1 3-3 9-6 11v1c2 3 0 9-1 12l-2 10c-2 3-3 6-6 7h-2c1-2 1-3 1-4l2-5c1-3 3-9 2-12 0 1-1 2-2 3-3 1-5 3-8 5h0c-2 1-3 1-4 1 2-1 3-2 5-3v-1h-1c-3 1-5 1-7 1l-1-2-1-1-1-1v1h-3v-1c2 0 2 0 4-1v-1c2 0 4-1 6-2 4-2 6-4 9-7 6-5 11-11 13-19z"></path><path d="M438 849c6-1 9-3 14-5l-6 6c-3 1-5 1-7 1l-1-2z" class="M"></path><path d="M433 731l7 15v6l-1 1c-1-2-2-4-4-6 2 15 1 30-2 45-1 1-1 4-2 5l-1 1c0-2 1-4 1-6h-1c-1 1-1 0-3 0 0 0-2 1-3 1v1c-6 3-13 5-20 6 1-2 2-4 4-5 9-8 20-11 23-24l1-1c1-8 1-16 1-24v-15z" class="G"></path><path d="M359 608c-5-4-10-9-16-11-3-1-7-1-10-1 4-1 8-3 12-1h2c2 1 5 2 8 2l1-1c1 0 2 1 3 0h5l1-1c2 2 6 5 8 7 1 2 2 5 4 7 1 2 3 3 4 5v1l2 2-1 1v1c1 3 4 7 6 10h-1c-1 0-6-8-8-10v1c0 1 1 2 2 3l1 1h0c-2 0-2 0-3-1h-2l-1 1h-1l-16-16z" class="H"></path><path d="M359 608c8 1 11 11 18 15l-1 1h-1l-16-16z" class="L"></path><defs><linearGradient id="i" x1="373.937" y1="603.8" x2="370.255" y2="614.239" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#373737"></stop></linearGradient></defs><path fill="url(#i)" d="M347 595c2 1 5 2 8 2l1-1c1 0 2 1 3 0h5l1-1c2 2 6 5 8 7 1 2 2 5 4 7 1 2 3 3 4 5v1l2 2-1 1v1c1 3 4 7 6 10h-1c-1 0-6-8-8-10h0c-2-4-9-13-13-14-2-1-2-1-3 0h-1l-1-2 1-1c-4-3-10-4-15-7h0z"></path><path d="M365 595c2 2 6 5 8 7 1 2 2 5 4 7 1 2 3 3 4 5v1l-11-12c-2-3-7-5-11-5-1 0-2 0-3-1h-1l1-1c1 0 2 1 3 0h5l1-1z" class="I"></path><defs><linearGradient id="j" x1="626.658" y1="787.22" x2="640.543" y2="754.486" xlink:href="#B"><stop offset="0" stop-color="#bab8b8"></stop><stop offset="1" stop-color="#dddcdc"></stop></linearGradient></defs><path fill="url(#j)" d="M608 742h0c3 2 6 2 9 2h0 1c0 15 7 32 17 41 7 7 15 10 25 11l-2 2h-1c2 0 3 0 5 1h-6c-3-1-6-1-9-2v-1c-4-1-6-2-9-4-4-1-6-4-10-6v1c-1 0-1 0-1-1-2-1-2-2-4-2-3-2-5-6-6-9-3-3-5-7-6-11 0-3-1-6-2-9-1-5-2-8-1-13z"></path><path d="M617 775c1 1 2 2 2 3l1-1c1 0 1 1 3 1h0 0c4 6 10 9 15 14-4-1-6-4-10-6v1c-1 0-1 0-1-1-2-1-2-2-4-2-3-2-5-6-6-9z" class="B"></path><defs><linearGradient id="k" x1="663.986" y1="737.095" x2="633.571" y2="705.503" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#e4e3e2"></stop></linearGradient></defs><path fill="url(#k)" d="M618 741c1-4 1-8 2-11 2-8 5-14 8-21 3-4 4-9 7-13l1 1c-3 4-5 8-6 13 11-13 21-25 39-27 3 0 8 0 11 1-3 0-7 0-10 1-13 3-23 11-30 21-8 9-17 25-15 38 1 7 4 12 9 18v1c0 1 1 1 1 2h0-1c0-1-1-1-2-1-5-4-9-8-11-13-1-2-1-5-3-7h-1c1-1 1-2 1-3z"></path><path d="M508 155l-2-1c-2-1-4-3-4-6-1-5 3-8 5-12 3-4 3-6 2-10 1-2 4-4 6-5 2 2 4 3 7 4v4c0 6 7 11 6 18-1 2-2 6-5 7-1 1-2 1-4 1l-6 1-5-1z" class="H"></path><path d="M514 154c1-3 3-3 6-4 1 2 0 3 3 4-1 1-2 1-4 1l-1-1v-1h0l-3 2-1-1h0z" class="B"></path><path d="M508 155c-1-2-1-2 0-4h1 3c1 1 1 2 2 3h0l1 1 3-2h0v1l1 1-6 1-5-1z" class="D"></path><path d="M508 155c-1-2-1-2 0-4h1l4 5-5-1z" class="E"></path><path d="M522 125v4c0 6 7 11 6 18 0-1 0-1-1-2h0-2 0-1l-5-12c0-2-1-4-1-5 1 0 1 0 2-1l2-2z" class="D"></path><path d="M774 145c6 1 12 4 16 9 4 6 6 13 5 20l-3 16c3-2 6-5 9-7-1 2-4 5-5 7 3-2 5-4 7-6-4 7-7 10-14 14h-1l-1 1c-3 2-8 3-12 3-5 1-10 2-15 2-5 1-10 0-15 0-3 1-4 0-6 0-2-1-6 0-7 0h0c1-1 2-1 3-1l16-2c13-1 27-6 35-17 4-5 6-10 5-16-1-7-5-12-11-16v-2l-6-3c-1 0-1-1-2-1h-3c2 0 3 0 5-1h0z" class="H"></path><defs><linearGradient id="l" x1="566.44" y1="430.192" x2="530.537" y2="424.569" xlink:href="#B"><stop offset="0" stop-color="#999997"></stop><stop offset="1" stop-color="#b7b6b7"></stop></linearGradient></defs><path fill="url(#l)" d="M535 383v1l1 1c2-2 2-7 3-10 2 4 1 12 1 17 0 3 1 7 1 11 1 6 3 13 5 20 2 5 4 9 7 14 0 0 3 3 3 4 1 3 2 5 6 6l1 2v1h-2c-6-5-12-11-17-18 3 9 6 18 11 26l5 8c-1 1-2 2-1 4 0 2 2 4 3 6-2 0-3 0-5-1-1-3-3-5-4-8-3-5-6-11-8-17-4-8-6-18-7-27-2-12-1-25-2-37h-1v5c-1 0-1-1-1-2 0-2 0-4 1-6z"></path><path d="M193 307c7-6 18-8 27-7 14 1 24 13 32 22l2 3 9 15c3 6 18 41 18 46l-11-22c-6-12-13-29-25-34-6-3-12-3-18-1a30.44 30.44 0 0 0-8 8v1-2c2-4 5-7 8-9 8-4 16-1 23 2l-9-9c0-1 1-1 1-1-4-4-10-8-15-10-10-4-20-5-29-2-2 1-4 1-5 0z" class="H"></path><defs><linearGradient id="m" x1="392.376" y1="687.174" x2="413.926" y2="660.57" xlink:href="#B"><stop offset="0" stop-color="#828382"></stop><stop offset="1" stop-color="#b4b2b2"></stop></linearGradient></defs><path fill="url(#m)" d="M377 623h2c1 1 1 1 3 1h0l-1-1c-1-1-2-2-2-3v-1c2 2 7 10 8 10 4 8 10 16 13 24l8 17c4 9 7 20 9 30 1 3 2 8 3 11 0 1-1 2-1 3 0 4 2 7 0 11v1c-8-29-16-58-33-84-1-1-1-2-3-2 0-1-1-2-1-3-1-4-3-6-6-8v-2l-2-2 1-1h1l1-1z"></path><path d="M408 670c4 9 7 20 9 30 0 2 1 6 0 7h0l-1-4v-2c-1-4-2-8-4-11-2-5-2-10-4-15v-5z" class="C"></path><defs><linearGradient id="n" x1="388.519" y1="641.162" x2="381.011" y2="624.377" xlink:href="#B"><stop offset="0" stop-color="#b5b3b3"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#n)" d="M377 623h2c1 1 1 1 3 1h0l-1-1c-1-1-2-2-2-3v-1c2 2 7 10 8 10 4 8 10 16 13 24-2-1-2-2-3-4l-3-4c0-1 0 0-1-1l-1-2c-1-1-1-1-2-1l-2-2c-1-2-3-4-5-6l-7-9 1-1z"></path><path d="M375 624h1l7 9c0 2 1 4 2 6 7 9 14 21 18 32 2 4 3 8 4 12l5 16 2 5c1 2 2 10 4 11 1-2-1-6-1-8 1-1 0-5 0-7 1 3 2 8 3 11 0 1-1 2-1 3 0 4 2 7 0 11v1c-8-29-16-58-33-84-1-1-1-2-3-2 0-1-1-2-1-3-1-4-3-6-6-8v-2l-2-2 1-1z" class="P"></path><path d="M560 466c5 8 14 13 23 15 8 2 19 0 26-5 6-4 11-11 11-18 1-4 1-7-2-10-2-3-7-7-11-7-1 1-2 1-3 2-2 2-2 3-2 5h-1c-2-1-2-2-2-5 0-2 1-4 3-5 1-2 4-3 7-2 5 0 10 3 13 8 4 5 5 13 4 19-1 7-6 14-12 18l-6 3c-2 3-6 4-9 4-2 1-4 1-6 1-1-1-2-1-4-1-2-1-7 0-9-1v-1c-1 0-3-1-4-1-5-2-10-6-14-9-1-2-3-4-3-6-1-2 0-3 1-4z" class="G"></path><path d="M576 485c3-1 9-1 11 0v1l2 1c-3 0-6 0-9-1-1 0-3-1-4-1z" class="F"></path><path d="M589 487c7 0 13-1 19-3-2 3-6 4-9 4-2 1-4 1-6 1-1-1-2-1-4-1-2-1-7 0-9-1v-1c3 1 6 1 9 1z" class="N"></path><path d="M637 639c1 0 0 0 1-1 2-1 3-2 5-2-8 18-17 35-22 55-2 9-3 18-4 27 0 8-1 15 0 22l1 1c0 1 0 2-1 3h0c-3 0-6 0-9-2h0v-5c0-5 0-10 1-15 2-9 3-17 4-26 3-18 10-34 19-50 1-1 4-5 5-7z" class="K"></path><path d="M608 742c1 0 1-1 2-1 2-2 5-1 7-1l1 1c0 1 0 2-1 3h0c-3 0-6 0-9-2z" class="R"></path><defs><linearGradient id="o" x1="623.755" y1="774.42" x2="587.565" y2="780.437" xlink:href="#B"><stop offset="0" stop-color="#b5b5b5"></stop><stop offset="1" stop-color="#f0eeee"></stop></linearGradient></defs><path fill="url(#o)" d="M592 736c2-1 2-2 4-3v19c0 8 1 20 7 27 3 2 5 3 8 4 1 1 4 1 5 3-2 0-3 1-5 1l-6-3v1l2 1c0 2 0 3 1 4v2l-2-1c-1 3 2 7 3 9 5 7 12 13 20 16 2 1 5 2 7 3-3 0-6 1-9 0-5 0-9-2-13-5-9-6-17-15-19-26-3-7-4-15-4-23h0c1-1 1-1 0-2 0 0 0 1-1 2h0 0l-1-3c1-3 1-6 2-9l-8 15c0-3 1-6 1-8l-3 4v-2l2-4c5-6 7-15 9-22z"></path><path d="M607 806c3 1 5 3 7 5 4 3 10 4 13 8-5 0-9-2-13-5l-2-4c-2 0-4-3-5-4z" class="B"></path><path d="M595 788h0v-3c1 1 1 1 1 2 3 7 6 13 11 19 1 1 3 4 5 4l2 4c-9-6-17-15-19-26z" class="D"></path><path d="M780 305c10-5 21-7 32-4 5 2 11 5 14 10 0 0 1 1 1 2-2 0-4-3-6-4v2c-8-3-16-4-24-2-10 2-17 6-24 12a30.44 30.44 0 0 0-8 8c6-3 14-5 20-3 5 2 9 4 10 9 1 1 1 2 1 3v-1c-2-3-4-6-8-8h-1-1c-5-2-12-1-17 2-15 7-21 29-27 44 0-3 1-6 1-8l6-18c2-4 3-7 4-11 0-3 5-11 6-14h-1l1-1-1-1-1 1h0c0-3 3-6 4-8 1 0 3 0 4 1 4-5 9-8 13-11h2z" class="H"></path><path d="M773 321l-1-1c4-6 12-10 18-12 11-3 21-4 31 1v2c-8-3-16-4-24-2-10 2-17 6-24 12z" class="E"></path><path d="M765 316c4-5 9-8 13-11h2c-10 8-19 20-26 31l-1 2c0-3 5-11 6-14h-1l1-1-1-1-1 1h0c0-3 3-6 4-8 1 0 3 0 4 1z" class="M"></path><path d="M757 323c0-3 3-6 4-8 1 0 3 0 4 1l-6 8h-1l1-1-1-1-1 1h0z" class="E"></path><path d="M674 543c4 1 7 3 9 7 2 3 2 8 4 10 1 0 2-1 4-2 0 6 0 14-5 19-3 3-6 5-9 9 10-3 19-6 30-6-6 4-14 6-21 7 3 0 9 0 12 1s6 3 8 5c4 5 7 10 7 17 0 5-2 9-6 12 0 1-1 2-2 2h0c-1-4 2-8 3-12 0-1 0-1-1-2v-3c0-5-2-8-6-11-5-2-12-1-18-2-2 3-6 5-9 7l-5 1c0-1 1-3 2-4l2-1h1 0c-2 0-5 0-7-1h0c-3 1-7 2-10 4 1-1 1-1 1-2h-1c0-3 1-6 3-8 1-1 1-1 1-2l5-5c0 1-1 2-1 3 2-2 3-4 5-6l2-3v-1c1-2 2-3 3-5v-2c1-1 1-2 1-3 2-6 3-10 0-16h1l1 1 1-1c-1-3-3-5-5-7z" class="G"></path><path d="M665 586c2-2 3-4 5-6v1h2 0c-3 3-6 7-9 10 0-3 0-3 2-5z" class="K"></path><path d="M666 583c0 1-1 2-1 3-2 2-2 2-2 5-2 2-3 4-4 6 2 0 3-1 6-1h2c-3 1-7 2-10 4 1-1 1-1 1-2h-1c0-3 1-6 3-8 1-1 1-1 1-2l5-5z" class="D"></path><path d="M701 596v-1c0-1-2-3-3-4 2 1 4 1 6 3 3 5 6 11 5 17l-1 1c0-1 0-1-1-2v-3c0-5-2-8-6-11z" class="J"></path><path d="M667 596h1c1-1 1-1 2-1h1 1 3v1h2 1l1-1h1 1v-1h-1v-1c1 1 2 1 3 1-2 3-6 5-9 7l-5 1c0-1 1-3 2-4l2-1h1 0c-2 0-5 0-7-1z" class="F"></path><defs><linearGradient id="p" x1="674.116" y1="564.134" x2="680.682" y2="571.151" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#p)" d="M679 550c3 4 4 14 3 18-1 5-6 10-10 12h1c2-3 2-5 2-9v-2c1-1 1-2 1-3 2-6 3-10 0-16h1l1 1 1-1z"></path><defs><linearGradient id="q" x1="377.599" y1="758.594" x2="409.779" y2="652.03" xlink:href="#B"><stop offset="0" stop-color="#c7c6c5"></stop><stop offset="1" stop-color="#ebeaea"></stop></linearGradient></defs><path fill="url(#q)" d="M383 640c2 0 2 1 3 2 17 26 25 55 33 84l2 8v3 2 5l-1 2c-2 1-3 1-4 1s-1 1-2 1c-1 1-2 0-3 0l-1 1v-2l-3 6c-2 4-6 8-9 11-6 5-13 9-20 8 5-1 9-3 14-6 6-4 11-12 12-20 2-12-5-28-12-37-9-12-22-23-38-25h0v-1h5c19 2 30 16 41 29-1-1-4-9-3-10l13 29c0-8-1-16-3-24-2-12-7-25-12-36l-12-31z"></path><path d="M413 727c0-1 0-1 1-2h0c1 2 2 3 2 5h-1s-2-2-2-3z" class="F"></path><path d="M413 727c0 1 2 3 2 3l-1 2c0 1 0 3 1 5 0 1 2 1 3 1h-7c1-3 2-7 2-11z" class="T"></path><path d="M411 738h7l1 1c0 1 0 2-1 2-2 1-5 1-7 2-1-2 0-4 0-5z" class="M"></path><path d="M414 732c3 2 4 2 7 2v3 2h-2l-1-1c-1 0-3 0-3-1-1-2-1-4-1-5z" class="D"></path><path d="M419 739h2v5l-1 2c-2 1-3 1-4 1s-1 1-2 1c-1 1-2 0-3 0l-1 1v-2c0-1 1-2 1-4 2-1 5-1 7-2 1 0 1-1 1-2zM304 206c-25 1-52 4-76-6-10-4-19-11-29-14-8-2-13-1-20 3 1-2 3-4 5-5 13-6 32 5 43 11 4 1 7 3 10 4-6-5-11-7-13-15h1l8 6c-3-8-4-19-2-27 1-6 5-11 10-14 6-4 14-4 21-2 5 1 8 4 12 8l-2 1-2-2-1 2c-5-4-10-6-17-5-6 1-11 4-15 9-3 6-3 10-1 16 3 9 10 16 19 20 12 6 30 7 44 7l6 1c0 1-1 1-1 2z" class="H"></path><path d="M237 160v-2c3-4 6-7 10-8l1-1h2c7-2 14 1 20 5l-1 2c-5-4-10-6-17-5-6 1-11 4-15 9z" class="L"></path><path d="M590 311l3-1h0l-1-1c1-2 2-1 4-2 9 0 25 3 32 10 6 6 9 15 14 21 3 3 6 7 10 10 3 3 7 5 9 9s2 8 3 13c0 2 1 4 2 7h0c-2 0-3 0-5-1-1-2-3-5-3-7 0-1 1-1 1-2 1-2 1-5 0-6-2-5-7-9-11-12 0-1-2-2-3-3-2-2-4-4-5-6-1 1-2 1-2 2l-3-2 2 8c0 3 0 4 1 7 0 1 1 2 1 3h0c0 4-1 4-2 7-1 0 0 0-1 1l-1-1v1c-2 1-3 1-4 3l1 1c-1 2-2 4-3 5h-1l-1 1c-3 5-8 12-12 16l-1-2c4-5 10-10 12-16v-1h0c-2 2-3 4-5 6-1 1-4 4-4 5-3 2-5 4-7 6-1 0-3 2-3 2-12 6-22 9-36 6-9-2-16-6-23-11 2 0 11 3 13 4 8 2 14 3 21-2 4-2 8-7 9-12 2-7-1-17-5-23-1-1-1-1-1-2l1 1c5 4 8 12 8 19 2-9 2-18-1-26-2-7-6-9-12-11-2-1-4-1-6-2 3-1 11 2 14 3-2-3-4-5-7-7 6 2 11 6 14 12 5 9 6 22 3 32s-9 16-18 21c5-1 9-1 14-3 11-4 22-14 26-24 5-11 3-21-1-31 0-2-1-4-2-6 0-2 0-3-1-4-3-6-8-10-15-11-1-1-3-1-5-2l-5-1-2-1z" class="G"></path><path d="M635 340c-1-1-1-2-1-3v-1c2 0 3 0 4 2 1 1 2 1 2 2-1 1-2 1-2 2l-3-2z" class="K"></path><path d="M623 329l-2-11c5 3 6 6 8 11-1-1-2-1-3-1s-2 1-3 1z" class="R"></path><path d="M626 328c1 0 2 0 3 1s1 2 2 4c-1 2-1 3-2 5v3 1l-1 2h0c-3-5-4-11-5-15 1 0 2-1 3-1z" class="T"></path><path d="M626 328c1 0 2 0 3 1s1 2 2 4c-1 2-1 3-2 5v3 1l-3-14z" class="F"></path><path d="M310 549c0-5-1-12 1-17h1c1 0 1 1 2 2l2 3c-2-15-9-28-17-40-3-5-6-8-8-13l14 17c-3-6-7-11-11-16l3 2c16 15 23 35 25 56 2-8 2-18 1-26 0-14-5-28-10-41-7-19-16-37-32-51-6-5-13-9-22-10-4-1-9-1-13 0l6-3c12-3 23 2 33 8-2-4-13-8-17-10-2-1-8-2-10-4 1-1 1-1 1-2h0c-2 0-5 1-6 1-10 3-18 9-26 14h0c0-2 2-4 4-6 4-5 7-11 13-15 8-5 17-5 27-3 9 1 15 6 20 14 3 3 15 18 15 22l4 8c0 3 1 5 2 8 0 2 2 4 3 6 2 6 5 13 7 20l4 15c0 1 1 3 1 4l2 11-1 4c1 13 3 28 0 41-2 5-4 10-5 16-4 9-13 19-22 24-6 2-14 3-19 1-6-3-10-7-12-12s-1-9 0-14l2 1c0 5 0 8 2 12v1c1 3 5 7 8 8h1 1l1 1h0c5 1 12-1 16-4 1 0 1-1 1-2 9-8 13-16 16-27h0c-2-1-4-1-5-1-2 0-3-2-3-3z" class="H"></path><path d="M278 406l-7-4c7 2 12 6 19 8l1-1c3 3 15 18 15 22l4 8c0 3 1 5 2 8 0 2 2 4 3 6 2 6 5 13 7 20l4 15c0 1 1 3 1 4l2 11-1 4c-2-13-5-25-10-38l-5-15h0c-3-4-5-9-7-14-7-13-16-23-28-33v-1z" class="Q"></path><path d="M278 406c2 0 3 1 5 2 3 2 7 5 9 8 1 1 2 3 3 4 3 4 6 8 8 12 3 5 5 9 7 14l2 3v1c1 1 1 2 1 4-3-4-5-9-7-14-7-13-16-23-28-33v-1z" class="C"></path><path d="M721 414c5-9 14-17 24-19 10-3 20-1 28 4l2 2h0 0-2c1 2 3 3 5 4 2 3 5 5 8 8-4-1-7-4-11-6l10 11h0c-6-5-13-10-20-13-2-1-4-2-6-2-1 1-1 1-2 1h1v2c-3 2-8 3-12 5-5 2-8 4-12 7 6-2 12-5 18-7h1 10l1 1c4 0 7 2 11 4-12-3-23-2-33 4-23 13-34 44-40 68-4 15-4 32-1 47 0 5 1 10 4 15v-4c-2-22 7-42 21-58 1-1 2-2 3-2-4 4-7 9-9 14l8-9c1-2 2-4 4-5-1 3-3 5-5 8-6 11-13 22-15 35-1 2-2 5-1 7l1-1c1-1 1-2 2-2l1 1c2 6 2 13 2 19 2 2 5 4 7 6h-2l-2-1h-1c-3-1-5-3-8-2l-1-1c0 8 7 18 13 23l4 4c2 0 4 1 6 1h1c6 2 10-1 15-4l1-1c1-1 2-3 2-4 0-5-5-8-8-11l1-1c2-1 4 0 6 0 2 1 4 3 5 5v4c1-1 1-1 2 0-1 2-1 5-2 7-2 5-7 9-12 11-7 2-15 1-21-2-14-8-22-25-27-39-3-8-4-15-5-23-1-6 0-13 0-19 2-4 0-8 2-12 1-12 5-24 9-36 0-2 1-5 2-7 1-1 1-3 2-4 1-5 5-11 7-16 0-1 0-1 1-2v-1c1-1 1-2 2-2 0-2 1-5 2-6s1-1 1-3c0-1 1-2 2-3z" class="H"></path><path d="M715 534c2 6 2 13 2 19l-2-2c-1-3-2-10-1-14l1-3z" class="G"></path><path d="M727 582c2 0 4 1 6 1h1c6 2 10-1 15-4 0 3-2 4-5 5-3 2-7 2-11 1-2 0-5-2-6-3z" class="C"></path><defs><linearGradient id="r" x1="721.947" y1="408.555" x2="736.053" y2="413.445" xlink:href="#B"><stop offset="0" stop-color="#7b7877"></stop><stop offset="1" stop-color="#8f9090"></stop></linearGradient></defs><path fill="url(#r)" d="M721 414h0c3-1 5-3 7-4 3-2 6-5 9-6 3-2 6-2 9-3-6 4-12 8-17 13-3 0-3 1-5 3h-1l-3 3c-2 4-4 8-7 11 0-1 0-1 1-2v-1c1-1 1-2 2-2 0-2 1-5 2-6s1-1 1-3c0-1 1-2 2-3z"></path><path d="M713 431c3-3 5-7 7-11l3-3h1c2-2 2-3 5-3-4 4-8 9-11 13-5 9-9 18-13 27-9 25-13 49-11 76 0 6 2 12 2 18-3-8-4-15-5-23-1-6 0-13 0-19 2-4 0-8 2-12 1-12 5-24 9-36 0-2 1-5 2-7 1-1 1-3 2-4 1-5 5-11 7-16z" class="E"></path><path d="M691 506c2-4 0-8 2-12-1 3-1 5-1 8 0 1 1 2 1 4v11 3c-1 0-1 1-1 1-1 1 0 3-1 4-1-6 0-13 0-19z" class="C"></path><path d="M593 127c2-2 2-2 2-4v1h0c0 2 0 2 1 4-1 2-1 4-2 5-1 2-3 3-3 5-4 3-8 5-12 7 3-1 7-1 10-2 2-1 5-2 6-2-6 4-14 6-21 9-17 6-31 20-36 38 15-19 33-35 57-38 14-2 27 1 39 7 5 2 9 4 13 7 9 7 16 17 17 28 1 8-1 14-4 21v-5c-1 1-1 1-1 2-3 5-8 9-13 12-2 1-4 1-5 2-1-1-2-1-2-1-3 0-5 1-8 1h-14l-6-1c-9-1-17-4-22-11h1c7 7 16 8 25 8 0-1-2-1-3-2 1-1 1-1 2-1 0-1 1-1 2-1 5 1 10 4 15 4 8 0 14-3 19-8 4-4 7-10 7-16-1-9-5-16-11-22l-1-2c-3-3-7-5-12-7-1 1-1 1-3 1l-1 1c-11-2-21 0-32 2l1 1v1c-5 0-12-2-17 0h0c11 1 26 4 33 13 4 4 6 9 6 14s-3 8-6 11c2-5 4-9 2-15-2-3-4-5-6-8h0c-2-4-12-8-16-9-4 2-13 0-16 4-8 2-16 6-23 11l-6 6c-2 1-3 3-4 5 4-2 7-5 11-6 5-3 10-3 16-3l-1 1c-13 3-23 9-29 21-6 10-12 23-15 35-2 12-2 26-1 38 0 3 0 6 1 9 1-1 2-3 3-4 1-3 5-10 8-12 2 2 2 5 3 7 2-5 4-11 6-16 2-3 5-7 7-10s3-6 5-9c4-6 12-12 18-14-4 3-7 6-11 10h1v1c9-8 19-16 32-15h1c-15 4-29 14-38 27 8-4 16-8 25-9l-7 5c-1 2-2 4-4 5-5 5-13 8-17 14 6-3 12-7 18-10l14-5c7-3 14-7 21-12-1 2-3 4-5 5-8 8-19 13-29 18-4 3-8 5-12 8-6 5-12 11-16 18l16-9c14-7 27-8 42-6-1-2-6-2-7-4h0c2-1 5-1 7-1 13 0 25 5 38 9 7 2 15-1 20-4s11-10 11-15c1-5-1-8-4-12v-1l-1-1c-1-1-2-1-2-1-1-1-1-1-2-1l-3-1 1-1h2v1h2l2 1 1 1c1 0 1 0 3 1 3 3 6 6 6 11 0 8-5 14-10 20 2-1 4-2 6-1-1 0-1 0-2 1-5 1-10 3-15 5 4 0 8-1 11-1 3-1 6-2 8-3-7 7-17 8-27 7l-16-3c14 9 32 15 36 34l1 2c-2-2-3-4-4-6-2-3-5-5-8-7s-5-5-8-7c2 3 6 6 7 10-8-8-15-15-25-19 4 4 9 8 13 12s7 9 10 14l-11-10-1-1c-2-2-8-7-11-7h-2c-1-1-2-1-3-1-3-1-6-3-9-4-14-6-31-6-46-1-13 4-22 13-30 24l-10 17v1h1c4-5 9-9 14-14 3-2 6-4 10-6l16-5 1 1c-8 3-16 8-23 14-1 3-4 4-5 7-1 5-5 8-6 12l-6 12c0 2-1 4-1 6l-2 13c-1 2-1 4-1 6 0 1 0 2 1 2v-5h1c1 12 0 25 2 37 1 9 3 19 7 27 2 6 5 12 8 17 1 3 3 5 4 8l-1 2c-8-9-16-20-20-31-9-21-10-47-9-69l2-24c-13 33-7 82 6 113 5 11 11 23 22 28 5 1 10 2 15 2h4c1 0 2 0 2-1h4c0-1 1 0 2 0h3c-2 1-3 1-4 2-3 1-5 2-8 2h-1c-2 1-7 0-9 0h0v1h1c-1 0-2 1-2 0h-4-1c-8-3-13-9-18-15 1 3 2 5 4 7 8 11 18 16 30 21l24 9-7 1c-2-1-6-1-8-1-8-2-16-5-22-9v2c-1 0 0 0-1-1h-2c0-1-1-1-1-1-1 0-1 0-2 1 0 1 0 2 1 2-3-2-6-4-9-7-12-7-18-16-24-28 4 21 12 43 30 55 5 3 11 6 16 7-1 1-3 0-5 0-15-5-23-13-32-26 3 9 9 18 14 25 2 2 4 4 5 6-3-2-5-5-7-7-8-9-13-20-17-31l-4-14c0 30 5 57 27 79 5 5 10 8 17 12-12-3-24-8-32-17-3-4-6-7-9-10 7 17 20 35 38 43 2 2 5 3 8 3v1c-12 1-22-4-31-11-3-3-6-6-8-9-2-1-4-5-6-5 4 10 9 19 15 28 2 4 5 7 7 11-8-7-14-15-20-23l1 4h-1c0-1-1-1-2-1l6 17 1 1 2 5v1c1 2 1 3 1 4v1l1 2h-2c-3-3-6-6-8-9v5c1 4 5 6 7 10 7 10 12 23 23 29 6-7 9-18 13-27l15-37 66-164 35-87 17-40c3-9 4-18 5-27 0-14-2-30-12-41-7-8-16-11-27-11v-20h108 36c10 0 20 1 30-1 8-1 15-4 21-8s11-11 13-18c-1-4-3-7-7-10h-1v-1c3 1 4 2 6 4 3 3 4 7 4 11-1 8-8 13-13 17 2 0 5-1 7-2 1-1 2-1 4-2-2 2-4 4-6 5-6 4-15 5-22 5-4 0-8 0-11 1 9 2 18 2 26 5 13 6 24 17 30 30 4 12 5 23 0 34 0-7-1-13-2-20l-2 6c-1 7-6 14-12 17-8 5-17 5-25 3-6-2-11-5-14-11h0l1-1h0c2 4 4 6 8 6 6 2 14 1 19-3 6-4 10-10 11-17 1-6 0-12-3-17-5-6-12-10-19-11h-1c-18-3-38 4-52 14l-4 2c-18 12-34 32-45 50-4 8-8 16-11 25l-18 41-46 112-134 327-19 51-7 17c-1 4-2 8-5 12-4-5-6-12-9-18l-10-24-29-64-143-324-46-105-15-35c-3-7-6-14-10-21-10-16-25-33-41-43l-3-2c-15-10-34-18-52-15-7 1-15 5-19 11-3 5-4 11-3 16v1c1 6 5 12 10 16l6 3h1c5 1 12 1 16-2 3-1 6-4 7-7v-1h0l1 1c-1 2-1 5-2 7-3 5-9 8-14 9-9 2-19 1-26-3-6-4-10-10-11-16h0c-1-4-1-7-1-11l-3 17c-1-3-2-5-2-7-1-12 3-25 11-34 8-12 22-20 37-21 6 0 13 0 19-1h13 31 89c19 0 37 1 56 0l-1-2c-2-3-3-5-4-9 0 3 0 6-1 8h0c-2-2-3-5-3-8-2-9 2-20 7-27 11-16 26-22 43-26 7-1 13-1 20 0 19 2 36 13 49 27 4 4 7 9 11 14l-8-16c-10-18-26-24-44-29l-1-1 14 1-4-2c0-1-1-1-2-2 0 0-1 0-1-1l1-2c20 6 35 20 45 39 2 5 5 11 7 16 1 3 1 6 2 9 0-15-6-29-15-40-2-3-6-6-8-10 17 12 24 29 28 49 1 5 2 11 4 16l-1-15c-1-6-7-38-4-42 1-1 4-2 6-2 3 0 7 1 8 3l1 1-4 19c-1 7-3 14-3 21-1 7 0 14 0 21 4-19 7-43 19-59 3-3 9-10 13-10l-5 4c-10 11-15 26-18 40-1 5-2 10-2 15 4-15 8-30 16-43 5-8 13-16 21-21 12-7 26-9 33-22z" class="H"></path><path d="M519 494c0 4 0 8-1 11 0-2-1-5 0-7l1-4z" class="K"></path><path d="M572 264c2-2 5-4 8-5-1 2-2 4-4 5-1 0-2 0-3 1h0l-1-1zm-59-102h0l-1 1 1 2c-1 1-1 2-2 3l-2-1c0-1-1-1-1-2l1-1c1 0 2-1 3-2h1z" class="J"></path><path d="M449 174c2 0 6 1 7 3l1 1h-1c-1-1-3-1-5-1-1 0-2-1-3-1 0-1 1-2 1-2z" class="B"></path><path d="M743 228h1c0 2 1 2 2 2h0c2 0 4 1 6 1s4-1 6-1c-1 1-2 2-4 3s-5 0-8-1c-2-1-2-2-3-4z" class="I"></path><path d="M401 299l10-7c-2 4-5 8-8 12 0-2 0-2-1-4h1l-2-1zm11-76c7 1 13-1 19-3-1 1-2 2-4 2l-4 1 2 1v1c-1 1-1 1-2 1s-2 0-2-1h0c-4 0-8 0-11-1l2-1z" class="M"></path><path d="M756 226c1 1 2 1 4 1-1 1-1 2-2 3-2 0-4 1-6 1s-4-1-6-1h0c-1 0-2 0-2-2 2 1 5 1 7 0 1 0 4 0 5-2z" class="B"></path><path d="M451 177c2 0 4 0 5 1h1c4 2 10 4 14 8-4 0-6-2-9-3-4-2-8-3-11-5v-1z" class="E"></path><path d="M194 228l-16-6 10 1h1c3 1 8 0 11 0-2 1-5-1-6 3l-1 1h2l-1 1z" class="N"></path><path d="M521 466c2 1 3 1 5 3 1 3 2 6 4 10l6 9h-1c-1-2-3-3-4-5-2-3-5-9-8-10v-1-1c-1-2-2-3-2-5zm8 162c1 4 5 6 7 10-1 1-3 2-4 3h0c-2-1-3-3-3-5-1-2-1-6 0-8z" class="K"></path><path d="M489 347c7 8 10 17 10 27l-1-2h0c0-2 0-4-1-5v2c-1-3-3-7-4-10 0-2 0-2-1-4l-2-2c-1-2-1-3-1-6z" class="F"></path><path d="M446 178c1-1 1-1 2-1 1 1 2 1 3 1h0c3 2 7 3 11 5 3 1 5 3 9 3 2 2 4 3 6 6h0c-2-1-3-3-6-3h0c-5-2-10-4-15-7-4-1-6-4-10-4z" class="C"></path><path d="M538 693c-5-1-10-2-14-5v-3c2 0 6 2 8 3 2 0 4 1 6 1v4z" class="B"></path><path d="M504 326c2 5 4 10 5 15 1 4 1 8 2 12 0 3 1 6 1 9l-7-25h0 1c0-2 0-3-1-4v-1h0c-1-2-1-4-1-6z" class="D"></path><path d="M502 474c4-9 7-18 9-28l1-11h0c1 12-2 26-7 38 0 1-1 2-1 3l-1-1s1-1 1-2l1-1v-2l1-1-1-1v1c-1 2-1 4-3 5zm20-313l1 1-4 19h0v-2c-1-2-2-6-1-9 0-2-1-4 0-6l3-3h1z" class="F"></path><path d="M419 161c8 1 19 2 25 7-4 0-8 0-11 2l-2-2v-2c-3-2-8-4-12-5z" class="B"></path><path d="M508 258v-3h0c3 2 2 7 4 10l1 1c0 4 1 8 1 11 0 2-1 5 0 7l1 1-1 1v3l-6-31z" class="I"></path><path d="M745 222c3 0 7 0 10 1 0 1 0 2 1 3-1 2-4 2-5 2-2 1-5 1-7 0h-1-1c2-2 2-4 3-6z" class="G"></path><path d="M548 509c0-1 0-4-1-5-1-2-2-3-3-4v-1c3 3 6 6 9 8l7 5s1 1 2 1v2c-1 0 0 0-1-1h-2c0-1-1-1-1-1-1 0-1 0-2 1 0 1 0 2 1 2-3-2-6-4-9-7z" class="I"></path><path d="M517 414c1 10 1 21 4 31 0 4 2 7 3 11 2 5 4 11 7 16l-1-1c-4-8-9-17-11-26-2-10-2-20-2-31z" class="F"></path><path d="M536 342c0-2 0-3 1-4 0-1 1-2 1-3h-1c0-1 1-3 1-3l1-1c0-1-1-2 0-3 2-4 6-9 10-11-2 3-3 4-3 8l-10 17z" class="G"></path><path d="M518 498l1-39 2 7c0 2 1 3 2 5v1 1h-1c-2 5-2 11-3 16v5l-1 4z" class="I"></path><path d="M519 489c-1-7 1-13 1-19l1-1 1 2s0 1 1 1v1h-1c-2 5-2 11-3 16z" class="F"></path><path d="M451 259c2 0 3 1 4 1 9 4 15 9 21 16l-1 1c-1-1-2-3-4-4s-3-2-4-3l-2 1c-5-4-10-7-14-12z" class="K"></path><path d="M378 217c3 0 6 3 10 4 8 3 15 3 24 2l-2 1c3 1 7 1 11 1h0c-5 2-11 2-16 2-9-1-21-3-27-10z" class="L"></path><path d="M534 389l-1-2c-2-3-1-7-1-10 1-7 1-13 4-19 1 3 1 3 0 6h2c0 2-1 4-1 6l-2 13c-1 2-1 4-1 6z" class="F"></path><path d="M537 370l-1 1h-1c0-2 0-5 1-7h2c0 2-1 4-1 6z" class="D"></path><defs><linearGradient id="s" x1="613.182" y1="161.81" x2="616.921" y2="171.456" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#989799"></stop></linearGradient></defs><path fill="url(#s)" d="M598 170c-2 0-3-1-4-1h0c13-6 26-7 39-4-1 1-1 1-3 1l-1 1c-11-2-21 0-32 2l1 1z"></path><defs><linearGradient id="t" x1="572.074" y1="269.534" x2="552.5" y2="274.745" xlink:href="#B"><stop offset="0" stop-color="#b7b6b7"></stop><stop offset="1" stop-color="#dddcdb"></stop></linearGradient></defs><path fill="url(#t)" d="M572 264l1 1h0c1-1 2-1 3-1-5 5-13 8-17 14-1 1-2 2-3 2h0v-1c-2 0-2 1-3 0v-1c5-5 11-12 19-14z"></path><defs><linearGradient id="u" x1="554.545" y1="286.493" x2="558.455" y2="293.507" xlink:href="#B"><stop offset="0" stop-color="#949190"></stop><stop offset="1" stop-color="#bcbdbf"></stop></linearGradient></defs><path fill="url(#u)" d="M550 300v1h-2c0-3 1-5 3-7 5-8 12-13 20-17 1-1 3-3 5-3h2c-4 3-8 5-12 8-6 5-12 11-16 18z"></path><path d="M489 437h1v3l-1 1v2 1c-1 0-1 1-1 2-1 1-1 2-2 4h0c1 1 1 2 1 3l-1 1v2l-1 1c0 2-1 4-2 5v2-1c1 0 2-1 2-1-3 5-6 10-11 13 0 0-1-1 0-1l1-2c2-3 4-6 5-9s2-5 3-9c3-5 5-11 6-17z" class="F"></path><path d="M541 318v1c-3 3-6 7-8 11s-4 8-5 11c-4 10-8 19-10 29v-1c-1-3 0-7 1-10 3-15 11-29 22-41z" class="T"></path><defs><linearGradient id="v" x1="339.283" y1="233.812" x2="348.19" y2="249.641" xlink:href="#B"><stop offset="0" stop-color="#605f5e"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#v)" d="M349 227h1 1c1 1 2 1 3 1-11 9-15 17-17 30-1-1-2-2-2-4 2-9 5-19 12-25 1 0 1-1 2-1v-1z"></path><defs><linearGradient id="w" x1="432.654" y1="175.739" x2="436.005" y2="182.062" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#w)" d="M417 189l-1-1c2-3 4-5 7-7 7-6 17-8 26-7 0 0-1 1-1 2 1 0 2 1 3 1v1h0c-1 0-2 0-3-1-1 0-1 0-2 1-12 0-21 3-29 11z"></path><path d="M383 174v-1c4-6 14-10 20-11l1-1c5-1 10-1 15 0 4 1 9 3 12 5v2c-4-2-8-4-13-5h-1c-10-1-24 3-32 10l-2 1z" class="C"></path><path d="M598 157c3 0 7 0 9 1-12 1-24 3-34 9-5 4-9 7-14 9-3 2-5 3-7 5l1-1c3-4 7-7 11-10 11-8 21-12 34-13z" class="M"></path><path d="M536 358c1 0 4-8 4-10 4-6 10-11 15-15-1 3-4 4-5 7-1 5-5 8-6 12l-6 12h-2c1-3 1-3 0-6z" class="L"></path><path d="M549 198v-2c3-4 5-8 9-10 11-7 24-11 36-9-4 2-13 0-16 4-8 2-16 6-23 11l-6 6z" class="E"></path><path d="M536 638c7 10 12 23 23 29l-2 3c-2-2-5-3-7-4l1-1c-3-4-7-8-10-11-4-4-6-9-9-13 1-1 3-2 4-3z" class="C"></path><path d="M475 277l1-1 7 7c1 1 2 3 3 4 6 8 10 18 14 28 1 3 3 7 4 11h0c0 2 0 4 1 6h0v1c1 1 1 2 1 4h-1 0c-1-1-1-1-1-2-2-5-4-10-5-14-6-17-12-31-24-44z" class="F"></path><path d="M549 317c9-11 20-18 34-20 19-4 37-1 53 10h-2c-1-1-2-1-3-1-3-1-6-3-9-4-14-6-31-6-46-1-13 4-22 13-30 24 0-4 1-5 3-8zm-151-3l12-12v-1c2 0 4-1 6-2h1v-1l9-3c5-1 11-1 16 0 14 1 27 6 36 17-1 0-1-1-2-2l-2-1c-1-1 1 0-1-1l-2-2c-2-1-3-2-5-1-7-4-13-7-21-8h-1c-10-1-19 0-28 5-5 3-9 7-13 12l-2-1c-1 0-2 1-3 1h0z" class="L"></path><path d="M528 601c-1-6-2-11-4-17l-3-15c-1-4-1-8-1-12-2-8-4-17-5-25-1-10 0-22 1-32 1 7-1 15 0 22 2 12 6 24 11 35l-1 1-4-10h0v1 1c2 8 2 17 3 25 0 3 0 6 2 8v2h0c1 1 1 0 1 1v5 1c1 2 2 4 2 6l1 4h-1c0-1-1-1-2-1z" class="K"></path><path d="M508 258l-3-9-1-3-1-7-2-4v-2-2l-2-5v-2l-2-7c-1-7-5-15-9-22-1 0-3-3-3-4v-1c2 2 3 4 5 5 13 18 18 40 21 61 0 3 1 6 1 9-2-3-1-8-4-10h0v3z" class="J"></path><path d="M755 223h57 16c3 0 6-1 9 0l-14 4c-3-2-5 1-8 0h-8 0-47c-2 0-3 0-4-1s-1-2-1-3z" class="N"></path><defs><linearGradient id="x" x1="474.801" y1="509.838" x2="507.573" y2="469.596" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#dbdad9"></stop></linearGradient></defs><path fill="url(#x)" d="M502 474c2-1 2-3 3-5v-1l1 1-1 1v2l-1 1c0 1-1 2-1 2l1 1c-2 6-5 11-9 16-4 6-9 11-15 16-1 1-1 2-1 2-1 1-1 2-2 2l1 1c-2 1-3 2-5 2l-4 3c0-1 0-2-1-3-1-3-4-2-6-2l-1-1 6-3 4-2 8-6c3-3 8-8 10-12 0-1 1-2 2-3l1 1-1 1h1c3-3 7-9 10-14z"></path><path d="M479 501v3l-2 1c-2 1-4 2-6 2h0l8-6z" class="B"></path><path d="M467 509h1c2 1 4 0 6-1 1 1 1 2 1 3s-2 3-2 4h0l-4 3c0-1 0-2-1-3-1-3-4-2-6-2l-1-1 6-3z" class="L"></path><path d="M271 223l61-1c10 0 22 0 33 1-4 2-8 3-11 5-1 0-2 0-3-1h-1-1-2-73c-1-1-2-3-3-4z" class="N"></path><path d="M492 355c1 2 1 2 1 4 1 3 3 7 4 10v-2c1 1 1 3 1 5h0l1 2c0 10 0 20-1 29 0 4-1 7-2 10 0 3-1 5-1 8h-1v1 1l-1 1v1c-1 1 0 1-1 2h0v4l2-2-1-1 1-2v-1c0-1 0-2 1-2v1c1 0 1-1 1-2v-1h0-1l2-3h0c0 5-3 9-3 13h0c-2 1-2 2-3 3v2l-1 1h-1v-2-2l1-5v-2c0-1-1-1-1-2l-1 1s-1 1-1 2l-1 2-1 1v1c-1 1-1 2-1 3-1 2-2 3-3 5-5 3-9 10-16 11l3-2h1c4-3 12-12 13-17 3-4 5-9 7-14 3-10 5-18 5-29h0c1-2 0-5 0-7 0-8-1-15-4-22l2 2h1 0l-1-6z" class="I"></path><path d="M551 182h0c-1 2-3 5-4 7 0 1 0 1-1 3-3 5-7 9-9 15-3 6-4 13-6 20-3 9-6 17-7 27-2 10-3 20-4 31l-4 34-1 16c-1 2 0 5-1 7-1 5-1 12 0 17 0 4 0 7 1 10v4l-1-1v-1h0c-1-5 0-11 0-17v-36l1-18v4 1h0v6c-1 4 0 7 0 11 0 2-1 3 0 5v-1-4-1c0-1 0-3 1-4v-2-2-2c0-2 1-5 1-7v-1-2c0-1 0-1 1-2v-2-2-1c0-1 0-2 1-3v-2-1c1-1 1-3 1-4 1-2 0-5 0-7h-1l-2-2h0v6 5c-1 1 0 2 0 4 0 3 0 7-1 10l-1-1c1-2 1-6 1-9 0-7-1-15 0-23 1-9 2-17 5-25 2-9 5-17 8-25 2-5 4-10 7-15 4-7 10-14 15-20z" class="J"></path><path d="M307 269v-6l86 200h0-1-1c-3-5-5-10-7-15l-19-44-27-62-17-40c-4-9-8-19-12-29 0-2-1-3-2-4z" class="Q"></path><defs><linearGradient id="y" x1="228.517" y1="220.842" x2="235.377" y2="307.325" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#y)" d="M188 223l73-1c0 2-1 4-2 5h-57c8 4 16 8 23 14 22 16 40 39 48 65 1 3 2 6 2 8-5-8-8-18-13-26-10-19-26-36-44-47-7-6-15-9-24-13l1-1h-2l1-1c1-4 4-2 6-3-3 0-8 1-11 0h-1z"></path><defs><linearGradient id="z" x1="415.609" y1="436.961" x2="406.738" y2="440.885" xlink:href="#B"><stop offset="0" stop-color="#9e9c9c"></stop><stop offset="1" stop-color="#cecdcd"></stop></linearGradient></defs><path fill="url(#z)" d="M335 254c0 2 1 3 2 4 0 9 1 16 4 25 3 8 7 17 11 25l30 69 60 138 51 119c-4-7-8-14-11-22l-16-37-24-54-10-24-64-145-19-44c-4-8-8-16-11-25-3-10-4-18-3-29z"></path><defs><linearGradient id="AA" x1="791.601" y1="266.924" x2="772.703" y2="254.27" xlink:href="#B"><stop offset="0" stop-color="#5f5f60"></stop><stop offset="1" stop-color="#787776"></stop></linearGradient></defs><path fill="url(#AA)" d="M807 227h0 8c3 1 5-2 8 0-31 10-55 35-70 64-7 13-12 28-17 42l-34 81-110 272-22 48-28 60c-5 9-9 20-12 30-1-4 3-11 4-14l23-51 24-51 42-103 96-237 20-48 10-26c4-11 10-21 17-31 10-11 21-22 34-29 2-2 4-3 7-4 1-1 3-1 5-2-2-1-3-1-5-1z"></path><path d="M485 462c23-36 24-81 14-121 4 6 6 14 7 22 5 28 3 57-5 84-2 7-4 15-7 22-6 12-17 22-28 29l-11 4c4 0 8 1 12 0l1-1c9-1 14-7 21-12-2 4-7 9-10 12l-8 6-4 2-6 3-7 4-1 1-13-29-3-8h8 3c2 0 4-1 6-1h1v2 1c1 1 0 1 1 1 2 0 3 0 5 1 5-3 9-5 13-9 5-3 8-8 11-13z" class="R"></path><defs><linearGradient id="AB" x1="441.094" y1="486.877" x2="445.296" y2="481.632" xlink:href="#B"><stop offset="0" stop-color="#2d2c2d"></stop><stop offset="1" stop-color="#464645"></stop></linearGradient></defs><path fill="url(#AB)" d="M448 480c2 0 4-1 6-1h1v2 1c1 1 0 1 1 1 2 0 3 0 5 1l-5 2-4 1h-10-1l-1 1-3-8h8 3z"></path><path d="M448 480c2 0 4-1 6-1h1v2 1c1 1 0 1 1 1 2 0 3 0 5 1l-5 2-4 1c-2-2-3-3-5-4s-2-1-2-3h3z" class="Q"></path><path d="M445 480h3c1 3 6 3 8 6l-4 1c-2-2-3-3-5-4s-2-1-2-3z" class="O"></path><path d="M538 689c2 0 5 0 7-1 10-2 18-9 24-17 4-7 7-14 10-22l12-30 42-102 55-137 16-41c5-10 9-20 12-31 4-14 5-31 2-46-3-13-10-26-22-34-3-2-7-3-10-5l58-1h1c-1 2-1 4-3 6-4-1-10-1-14-1h-26c1 2 5 5 7 7s4 5 5 8c14 23 12 54 2 79l-25 62-65 160-32 80-12 29c-2 6-4 13-8 18-5 9-12 16-21 20-5 2-10 3-15 3v-4z" class="P"></path><path d="M478 513v-1c13-7 24-20 30-34-1 9-3 18-7 26-2 4-4 7-5 10-5 9-10 19-18 26 2 0 4-3 5-4 8-9 14-20 20-31l-12 31h0c5-7 9-16 12-24 2-4 3-10 5-14l-3 31c-1 8-1 18-4 25-2 6-7 11-13 15l-2 1c12-2 17-6 25-15l-2 35c0 9-1 18 0 27l5-35c3 16 2 33 5 49l2-9c2 8 3 15 7 22 3 7 10 13 17 18l5 4c2 1 5 2 7 4-2 2-5 4-8 5-5 3-11 3-17 1-6-3-10-8-14-13-5-8-8-16-11-23l-14-31-37-86h0l-3-6 1-1 7-4 1 1c2 0 5-1 6 2 1 1 1 2 1 3l4-3c2 0 3-1 5-2z" class="R"></path><path d="M454 516l7-4 1 1c2 0 5-1 6 2 1 1 1 2 1 3l-6 3-5 1c-1 1-2 1-2 1h0l-3-6 1-1z" class="N"></path><path d="M453 517l1-1c1 0 1 1 2 1 1-1 2-1 4 0 1 1 2 2 3 4l-5 1c-1 1-2 1-2 1h0l-3-6z" class="M"></path><path d="M307 269c1 1 2 2 2 4l12 29 17 40 27 62 19 44c2 5 4 10 7 15h1 1 0l28 64 39 88 17 39c7 15 13 30 23 43a107.02 107.02 0 0 0 26 26c5 4 10 6 15 9l12 4c-14 2-28 2-40-7l-3-3c6 19 11 37 13 57l2 22c-1-3-1-6-1-9l-2-8c-2-12-5-23-10-34-2-5-4-10-7-14l-11-15c-3-5-6-11-9-17l-27-63-44-101-26-60-5-12c-1-1-2-3-2-4-2-1-4-2-5-3-5-3-10-7-14-12-4-6-6-14-5-22 4 3 9 6 13 7 2 0 3 0 5-1l-2-7c-2-3-3-8-5-11l-13-30-28-65c-5-12-12-24-16-37-2-6-4-12-4-18z" class="B"></path><path d="M393 480l1-1c4 9 8 18 13 27 2 5 3 9 5 14l5 11 3 9 6 12c1 2 1 4 2 7 2 3 3 6 5 9 1 3 2 6 3 8s2 3 3 6l8 17v1c-1-1-1-2-2-3s-1-2-1-3l-2-2c-1 0-2-3-2-4l-1-1h1c-2-5-6-10-8-15l-23-57-16-35z" class="L"></path><path d="M373 430h0c1 3 2 6 4 8 2 3 3 5 4 7 4 8 6 16 9 25 2 3 3 6 4 9l-1 1c-3-8-8-16-13-23s-13-13-18-20l1-1c0 1 1 1 2 2l-1-1h1 1c3 2 6 3 9 2v-2l-2-7z" class="E"></path><defs><linearGradient id="AC" x1="446.27" y1="602.105" x2="478.39" y2="589.528" xlink:href="#B"><stop offset="0" stop-color="#636263"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#AC)" d="M391 463h1 1 0l28 64 39 88 17 39c7 15 13 30 23 43a107.02 107.02 0 0 0 26 26c5 4 10 6 15 9-2 1-4 0-5-1-2 0-3-1-5-1-4-2-7-5-11-8-10-7-18-16-25-27-10-16-18-34-26-52l-34-79-44-101z"></path><path d="M261 222c3 0 7 0 10 1 1 1 2 3 3 4h73 0c-4 1-8 0-11 2-2 0-4 1-5 1-9 3-16 7-20 15-2 4-3 9-4 13-1 2 0 3 0 5v6c0 6 2 12 4 18 4 13 11 25 16 37l28 65 13 30c2 3 3 8 5 11l2 7c-2 1-3 1-5 1-4-1-9-4-13-7-1 8 1 16 5 22 4 5 9 9 14 12 1 1 3 2 5 3 0 1 1 3 2 4l5 12 26 60 44 101 27 63c3 6 6 12 9 17l11 15c3 4 5 9 7 14 5 11 8 22 10 34l2 8c0 3 0 6 1 9 1 4 0 22 2 23h1 0v1c0 2-1 3-2 4v2l-1 2c0 2-1 3-2 4l-2 9-4 11h-1c0-4-3-8-4-12l-8-17-35-80-155-351-31-69-6-12c0-2-2-4-2-6s-1-5-2-8c-8-26-26-49-48-65-7-6-15-10-23-14h57c1-1 2-3 2-5z" class="K"></path><path d="M383 174l2-1c8-7 22-11 32-10h1c5 1 9 3 13 5l2 2c-10 4-17 8-21 19-2 4-3 9-1 13 1 2 3 5 5 7-2-6-4-10-1-16 0-2 1-3 2-4 8-8 17-11 29-11 4 0 6 3 10 4 5 3 10 5 15 7h0c5 3 8 5 11 10-4-2-7-4-11-5-6-1-13 0-18 0 5 1 10 2 14 4 9 3 16 10 21 17 9 12 11 26 13 41s2 30 2 45c-3-8-13-34-21-37-1-1-1-1-2 0v4c-9-9-16-23-28-28 3 3 6 6 8 9-3-1-5-4-7-6-6-5-15-7-22-9 6 4 13 7 19 11 5 3 9 8 13 13-7-1-12-4-19-4l7 5c4 5 9 8 14 12h-1l-45-18 17 11c8 4 17 7 24 13 8 7 15 14 21 23-6-4-11-9-17-13-11-7-24-10-38-8-6 1-11 2-17 4-5 1-11 5-17 5s-11-3-16-6h0c-2-1-3-2-4-4-1-1-2-2-2-3v1 1c1 0 1 2 1 3h0c1 1 2 2 2 4l1 1c-1 1-1 0-2 0v2c3 1 7 3 10 4-6 0-11-1-17-1 2 1 4 3 7 3 12 4 23-1 35-3-7 5-14 8-20 15l-1 1v1l-1 1h1c1-1 2 0 3-1 4-2 8-6 12-8h0l2 1h-1c1 2 1 2 1 4-4 5-8 9-12 15l7-5h0c1 0 2-1 3-1l2 1c4-5 8-9 13-12 9-5 18-6 28-5h1c8 1 14 4 21 8 2-1 3 0 5 1l2 2c2 1 0 0 1 1l2 1c1 1 1 2 2 2 5 5 8 10 10 16 1 2 2 4 2 5v3c0 2 1 4 2 6-1 0-2-1-3-1h0c-2 0-2 0-3-1h-1c-1 0-1-1-2-1 1 1 1 1 1 2 2 1 3 3 3 5 1 0 2 1 2 1 0 3 0 4 1 6l2 2 1 6h0-1l-2-2c3 7 4 14 4 22 0 2 1 5 0 7h0c0 11-2 19-5 29-2 5-4 10-7 14-1 5-9 14-13 17h-1l-3 2c7-1 11-8 16-11 1-2 2-3 3-5 0-1 0-2 1-3v-1l1-1 1-2c0-1 1-2 1-2l1-1c0 1 1 1 1 2v2l-1 5v2 2c-1 6-3 12-6 17-1 4-2 6-3 9s-3 6-5 9l-1 2c-1 0 0 1 0 1-4 4-8 6-13 9-2-1-3-1-5-1-1 0 0 0-1-1v-1-2h-1c-2 0-4 1-6 1h-3-8l-20-44-34-79-21-47c-5-13-12-25-14-39-2-10-1-19 5-28 6-6 12-9 21-10v-15c0-1 0-3 1-4l3 3c6 7 18 9 27 10 5 0 11 0 16-2 0 1 1 1 2 1s1 0 2-1v-1l-2-1 4-1c2 0 3-1 4-2h0c4-3 7-5 10-8h0l-4 3c-8 5-16 7-25 5h0c1-1 2-1 3-2h-1c-5-1-10 1-15 1-7 1-14-3-18-7-5-4-8-11-8-17 0-8 4-15 10-21z" class="R"></path><path d="M458 461l9-1c-1 2-1 4-3 6 0-2 0-2 1-3-2 0-3-1-5-1-1 0-1 0-2-1z" class="P"></path><path d="M398 314c1 0 2-1 3-1l2 1-11 10c2-3 4-7 6-10z" class="E"></path><path d="M400 174c2 0 6 1 8 1h0l-3 3h-1c-2 0-4-1-6-2 1 0 1-1 2-2z" class="O"></path><defs><linearGradient id="AD" x1="398.99" y1="373.689" x2="400.51" y2="366.311" xlink:href="#B"><stop offset="0" stop-color="#666767"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#AD)" d="M399 360l2 5 1 4c-1 4 1 10 1 14l-3-7c-1-3-1-7-2-10 1-2 1-4 1-6z"></path><path d="M436 426c1-2 3-5 5-6 3-1 6-1 9 0-3 2-5 2-7 4-2 0-3 2-5 2h-2z" class="F"></path><path d="M393 191c1-1 1-1 1-2s0-3 1-5 3-4 2-7l1-1c2 1 4 2 6 2h1c-3 1-5 3-7 5-1 2-2 7-4 8h-1z" class="C"></path><path d="M466 322l2-1c1 0 2 1 3 1h0c4 4 10 8 12 13-2-1-2-1-4-3s-5-4-7-6c-1 0-1 0-2-1l-4-3z" class="O"></path><path d="M397 198c1 0 0 0 1 1l-1 4c0 3 1 6 4 9 0 1 0 1 1 2l-2-1c-2-1-4-2-5-4-1-1-2-4-2-6 1-3 2-4 4-5z" class="H"></path><path d="M466 305c2-1 3 0 5 1l2 2c2 1 0 0 1 1l2 1c1 1 1 2 2 2 5 5 8 10 10 16 1 2 2 4 2 5v3c-2-3-3-6-4-9-5-9-12-16-20-22z" class="C"></path><path d="M400 174c-1 1-1 2-2 2l-1 1c1 3-1 5-2 7s-1 4-1 5 0 1-1 2c-1-1-2-1-3-2-1 0-1-1-1-2-1-2-1-4 0-6 2-4 7-6 11-7z" class="H"></path><path d="M450 420c0 1 2 1 2 3v2l-1 1h-1c-3-1-5-1-8 0-1 1-3 3-3 5v6 3l-3-4-1-5-1-1h0l2-4h2c2 0 3-2 5-2 2-2 4-2 7-4z" class="J"></path><path d="M387 305l-1 1v1l-1 1h1c1-1 2 0 3-1 4-2 8-6 12-8h0l2 1h-1c-3 3-7 5-10 8-4 3-7 6-10 9 1-1 2-3 2-5-2 0-4 2-6 4v1l-2 2c2-6 6-11 11-14z" class="I"></path><path d="M478 454c1 3 0 3-1 6-4 8-8 17-17 20-2 0-3 0-5-1h-1c11-5 18-15 24-25z" class="G"></path><defs><linearGradient id="AE" x1="461.813" y1="470.465" x2="475.28" y2="473.053" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#acacab"></stop></linearGradient></defs><path fill="url(#AE)" d="M477 460c1 1 1 0 1 2h0c2-2 3-5 4-7l1-1c-1 4-2 6-3 9s-3 6-5 9l-1 2c-1 0 0 1 0 1-4 4-8 6-13 9-2-1-3-1-5-1-1 0 0 0-1-1v-1-2c2 1 3 1 5 1 9-3 13-12 17-20z"></path><defs><linearGradient id="AF" x1="393.748" y1="351.556" x2="401.587" y2="348.073" xlink:href="#B"><stop offset="0" stop-color="#7a7a7a"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#AF)" d="M397 334c2-1 4-4 6-5l-2 6c0 2 0 5 1 7l1-1v3l-1 1-1-1c-1 0-1 1-2 1 0 2 0 3 1 4v1c0 2 1 7 1 8-1 3-1 4 0 7l-2-5c0 2 0 4-1 6-1-7-3-14-3-21 0-4 0-8 2-11z"></path><path d="M399 345c0-4 0-6 2-10 0 2 0 5 1 7l1-1v3l-1 1-1-1c-1 0-1 1-2 1z" class="Q"></path><path d="M399 360v-2c-1-2-1-6 0-8h1c0 2 1 7 1 8-1 3-1 4 0 7l-2-5z" class="E"></path><path d="M484 434c0-1 0-2 1-3v-1l1-1 1-2c0-1 1-2 1-2l1-1c0 1 1 1 1 2v2l-1 5v2 2c-1 6-3 12-6 17l-1 1c-1 2-2 5-4 7h0c0-2 0-1-1-2 1-3 2-3 1-6 3-6 6-14 8-20l-1-1-1 1z" class="B"></path><defs><linearGradient id="AG" x1="370.792" y1="254.385" x2="376.216" y2="263.034" xlink:href="#B"><stop offset="0" stop-color="#8b8a8b"></stop><stop offset="1" stop-color="#b3b2b3"></stop></linearGradient></defs><path fill="url(#AG)" d="M368 256c6-5 11-7 18-7h3c-7 2-15 5-19 11-2 4-2 9 0 12 1 4 4 7 6 10h0c-2-1-3-2-4-4-1-1-2-2-2-3v1 1c1 0 1 2 1 3h0c1 1 2 2 2 4v-1c-4-3-8-8-8-13-1 0-1-1-1-2 0-5 1-8 4-12z"></path><path d="M368 256c0 3-1 6-2 9v1c0 2 0 4 1 7v1c-1-1-2-2-2-4h0c-1 0-1-1-1-2 0-5 1-8 4-12z" class="C"></path><defs><linearGradient id="AH" x1="440.964" y1="463.377" x2="455.669" y2="451.791" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#AH)" d="M464 466c-3 5-10 8-15 11l1-2c1-1 2-2 1-4 0-4-9-10-13-13h0c-3-2-6-5-7-7l1-1c1 0 3 2 4 3 3 3 8 8 11 9s8 0 11-1c1 1 1 1 2 1 2 0 3 1 5 1-1 1-1 1-1 3z"></path><path d="M455 321l1-1 5 3c0-1 1-1 1-1l-6-3 1-1 9 4 4 3c1 1 1 1 2 1 2 2 5 4 7 6s2 2 4 3c2 2 5 4 6 6h0c-2 0-2 0-3-1h-1c-1 0-1-1-2-1 1 1 1 1 1 2 2 1 3 3 3 5 1 0 2 1 2 1 0 3 0 4 1 6l2 2 1 6h0-1l-2-2c0-1-1-2-1-3l-3-6c-6-10-14-18-23-24-2-2-5-4-8-5z" class="C"></path><path d="M487 346l-11-13v-1h3c2 2 2 2 4 3 2 2 5 4 6 6h0c-2 0-2 0-3-1h-1c-1 0-1-1-2-1 1 1 1 1 1 2 2 1 3 3 3 5z" class="F"></path><path d="M434 430h0l1 1 1 5 3 4v-3l3 6 4 4c3 2 7 4 11 3h1 0c3-1 5 0 7 0 7-1 11-8 16-11-2 3-5 7-8 9l-1 1c-5 5-10 7-17 7-6-1-11-4-15-9-5-4-7-10-6-17z" class="C"></path><path d="M436 436l3 4v-3l3 6 4 4v2c2 1 6 2 8 4-2 0-4-1-6-2h-1l-1-1c-5-2-8-8-10-14z" class="B"></path><path d="M465 450c7-1 11-8 16-11-2 3-5 7-8 9h0c-3 1-4 3-7 4-3 2-8 2-12 1-2-2-6-3-8-4v-2c3 2 7 4 11 3h1 0c3-1 5 0 7 0z" class="J"></path><path d="M403 341v-2l1 1v1l2 1h-2v5c1 8 1 17 5 25l4 7c1 3 5 9 8 11 3 5 7 9 6 16 0 2-1 6-4 7-1 2-2 1-4 1l-1-1c1-3 4-6 5-9 1-2 1-4 0-6-2-3-5-6-8-9-5-7-10-13-13-20l-1-4c-1-3-1-4 0-7 0-1-1-6-1-8v-1c-1-1-1-2-1-4 1 0 1-1 2-1l1 1 1-1v-3z" class="B"></path><path d="M403 341v-2l1 1v1l2 1h-2v5s-1 1-1 2v4c0 3-1 7 0 10v3c-1-3-1-5-2-8 0-1-1-6-1-8v-1c-1-1-1-2-1-4 1 0 1-1 2-1l1 1 1-1v-3z" class="C"></path><defs><linearGradient id="AI" x1="441.261" y1="312.43" x2="440.89" y2="329.582" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#AI)" d="M438 311c12 0 24 3 33 11h0c-1 0-2-1-3-1l-2 1-9-4-1 1 6 3s-1 0-1 1l-5-3-1 1-7-2h-1c-9-2-19 0-26 6-5 3-7 7-10 12 0-4 2-9 4-12 5-8 14-11 22-13v-1h1z"></path><path d="M464 447s1-1 2-1c3-2 5-5 6-7 7-9 11-18 14-29 1-7 2-14 2-22 0-6 0-12-3-18-3-3-5-6-9-6h-1-1v-1c1 0 2-1 4-1 3-1 8 1 10 3 6 4 5 16 5 22l1 1c0 11-2 19-5 29-2 5-4 10-7 14-1 5-9 14-13 17h-1l-3 2c-2 0-4-1-7 0-1-2-1-3-2-5l2-2c2 1 4 2 6 4z" class="H"></path><path d="M464 447l-1 1h2c3 0 5-2 7-4s4-4 5-6c2-2 3-4 5-7h0c-1 5-9 14-13 17h-1l-3 2c-2 0-4-1-7 0-1-2-1-3-2-5l2-2c2 1 4 2 6 4z" class="K"></path><path d="M397 334c2-5 4-10 8-14 4-5 11-9 17-11 5-3 11-4 16-2l1 1c0 1-1 2-1 3h-1v1c-8 2-17 5-22 13-2 3-4 8-4 12-4 12-3 22 3 33 7 12 18 20 32 24 3 0 6 1 9 1-10-5-16-10-20-21-3-9-4-23 1-32 3-6 8-11 14-14l-7 8c5-2 9-3 15-3-1 1-2 1-4 1-4 1-11 4-14 8-1 2-2 4-2 7-2 7-1 15 0 23l1 2c1-9 2-17 9-23-2 2-3 5-4 7-2 6-3 15-1 21 1 5 7 10 12 12 9 4 22 0 30-4h1 0v1c0 1-1 3-1 4-2 3-8 6-11 7-11 5-24 7-35 3-3-1-6-3-8-5-3-2-7-6-10-7-3-2-7-8-8-11l-4-7c-4-8-4-17-5-25v-5h2l-2-1v-1l-1-1v2l-1 1c-1-2-1-5-1-7l2-6c-2 1-4 4-6 5z" class="H"></path><path d="M437 311c-1 0-1 0-2-1 2-1 2-2 4-2 0 1-1 2-1 3h-1z" class="M"></path><path fill="#1f1f1f" d="M403 329c2-3 3-6 7-7v1l-2 10c-1 3-1 6-2 9l-2-1v-1l-1-1v2l-1 1c-1-2-1-5-1-7l2-6z"></path><defs><linearGradient id="AJ" x1="457.022" y1="384.128" x2="452.757" y2="404.085" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#AJ)" d="M413 379c10 12 24 20 40 21 11 0 23-6 33-12 0 1-1 3-1 4-2 3-8 6-11 7-11 5-24 7-35 3-3-1-6-3-8-5-3-2-7-6-10-7-3-2-7-8-8-11z"></path></svg>