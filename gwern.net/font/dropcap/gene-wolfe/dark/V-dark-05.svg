<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="67 52 564 616"><!--oldViewBox="0 0 688 752"--><path d="M596 119c2-3 5-6 7-9l10-14 10-14c2-3 4-7 7-10-1 2-2 4-3 7l-11 18c-3 1-6 6-7 8-2 2-10 14-12 15l-1-1zM77 94l-8-14c-1-2-3-5-4-7l35 47v1s0 1 0 0c-2 0-4-4-5-5l-6-8-7-9c-1-2-3-4-5-5z" fill="#e5e5e5"></path><path d="M596 119l1 1c2-1 10-13 12-15 1-2 4-7 7-8l-51 92-19-15 50-55zM77 94c2 1 4 3 5 5l7 9 6 8c1 1 3 5 5 5 0 1 0 0 0 0v-1c3 3 23 27 23 27l11 12 12 11 6 6-6 6-1 1h0c-1 1-2 1-3 1h-1c0 1-1 0-2 0h0c-1 0-2 1-3 1l-6 1c-2-1-3-3-3-5l-10-18-27-47c-4-7-9-14-13-22z" fill="#b9b8b8"></path><path fill="#e5e5e5" d="M123 147l11 12-1 1v-1c-3-1-6-5-8-8l-3-3 1-1z"></path><path fill="#4a4a4a" d="M188 393h2 1 1 1 0l1 1h0 0c1 1 1 0 1 1l2 2h1v1c1 1 2 1 2 2l2 2c0 1 1 1 1 2 0 0 1 0 1 1s1 1 1 1c1 1 1 2 2 2 0 1 0 2 1 2l3 6h0l1 1c0 1 1 1 1 1 0 1 0 1 1 2v1l2 3 1 2 1 1v1l2 5 1 1v1s1 0 1 1v1l1 1v1c0 1 1 1 1 1 0 1 1 2 1 3l1 3 1 1c0 1 0 2 1 2v2h0l1 1c1 2 1 3 2 5 0 1 0 1 1 2v2h0l1 2h0c0 1 0 1 1 2v1 1c1 1 1 1 1 2v1l1 1v2 1 1l1 1h-1v3 1 1c-1 1-2 1-3 2-1 0-1 0-2-1h-1 0-1v-1h-1v-1h-1l-3-3-5-5c-2-1-3-3-4-5-1 0-1 0-1-1h-1v-1l-1-1c-1-1-2-2-3-4l-1-1c-1-1-2-2-2-3l-1-1s0-1-1-1l-6-10c0-1 0-1-1-2h0v-1h-1v-1-1-1c-1 0-1-1-2-1v-1c0-1 0-1-1-2h0l-1-2v-1c0-1 0-1-1-1v-2h0l-1-3-1-2v-1c-1-1-1-2-1-4h0l-1-1v-1-1s0-1-1-2v-2h0c-1-1 0-1 0-2-1-1-1-2-1-3-1-1 0-6 0-7v-1-2h1c0-2 1-2 2-3z"></path><path fill="#fdfdfd" d="M266 385c8 1 16 3 24 5 1 0 3 1 4 1 2 3 4 6 5 10l10 18c3 6 8 13 10 20 1 2 1 5 1 7 1 4 2 9 3 13v-3l-3-15c2 3 4 8 6 12 3 7 7 14 11 21-2-1-4-2-7-2-4-1-11 0-13-3-4-3-6-7-9-10-2-3-4-5-6-8l-12-16c-6-7-11-14-16-22l-14-25v-2h0 3 3v-1z"></path><path d="M146 182l6-5c20 17 41 33 64 46 8 4 16 7 24 11-14-1-28-2-41 2-4 1-7 3-11 5-8 3-15 6-23 10l-21-39-10-19c-2-2-3-4-4-6 2-2 7-2 9-3 1 0 2 1 2 0h1c1 0 2 0 3-1h0l1-1zm252 218c1-3 4-8 6-11 4-1 8-1 12-1 7-1 15-1 22 0-9-5-19-9-28-12l29-57c14-3 27 0 41 2 4 1 8 1 12 2-1 3-2 6-4 8l-9 17-29 57-2-5h-1c1 2 2 7 1 8v1l-17 32-5 11c-1 1-2 3-2 3-1 1-2 1-2 1-1 1-1 2-2 3s-1 2-1 3v1c1 2-5 12-7 15l9-1c-2 1-6 3-8 2h-1 0l-1 1c-1 4-3 7-5 10l-6 12c-1 2-3 5-4 7l-8 1h-5v1h0l-2-1c4-4 9-8 12-13 4-8 4-18 1-27-1-2-3-7-3-10v-1-1c-1-1-1-4 0-5 0-2 1-3 2-5h0c3-4 6-7 10-9 4-4 9-7 12-12l2-2v-1a24.56 24.56 0 0 0 5-15c0-2-2-3-3-5-2-1-4-1-7 0-4 1-11 1-15-1h-1l1-1 1-2z" fill="#e5e5e5"></path><path fill="#4a4a4a" d="M419 463l-5-3 8-4c-1 1-1 2-2 3s-1 2-1 3v1z"></path><path fill="#858586" d="M448 409l-1-1-1 2h0l-2-19c1 3 3 6 4 9h-1c1 2 2 7 1 8v1z"></path><defs><linearGradient id="A" x1="397.147" y1="499.132" x2="393.82" y2="481.594" xlink:href="#B"><stop offset="0" stop-color="#80807f"></stop><stop offset="1" stop-color="#9b9a9b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M394 470l1 1c3 8 6 17 1 27-3 6-8 9-14 12h6-5v1h0l-2-1c4-4 9-8 12-13 4-8 4-18 1-27z"></path><path fill="#858586" d="M398 400c4 1 7 1 11 1 3 0 6-1 10-1 1 0 2 1 3 2v1c2 3 2 8 1 11h0c-1 4-3 8-5 11l-3 4c-2 3-4 5-6 6-5 4-10 8-14 12l-3 3c0 1 0 2-1 3 0-2 1-3 2-5h0c3-4 6-7 10-9 4-4 9-7 12-12l2-2v-1a24.56 24.56 0 0 0 5-15c0-2-2-3-3-5-2-1-4-1-7 0-4 1-11 1-15-1h-1l1-1 1-2z"></path><path fill="#4a4a4a" d="M398 400c4 1 7 1 11 1 3 0 6-1 10-1 1 0 2 1 3 2v1h0 0c-1 0-2-1-2-1h-4-8c-4 0-8-1-11 0l1-2z"></path><defs><linearGradient id="C" x1="177.984" y1="244.349" x2="293.015" y2="369.578" xlink:href="#B"><stop offset="0" stop-color="#d6d5d5"></stop><stop offset="1" stop-color="#fcfcfb"></stop></linearGradient></defs><path fill="url(#C)" d="M244 353c-2-2-3-4-5-5-5-4-13-5-20-6-1-1-4-1-6-2 0 0-7-13-8-14l26-9-12-1c-7-1-14-1-21-4l-22-40c-3-6-6-13-10-19 2-3 6-4 9-5l13-6c7-3 14-7 21-7 5 0 11 1 16 3 10 36 28 71 45 104l4 10 5 9c3 5 5 11 8 16l-19 6c-2 0-4 1-6 2h4v1h-3-3 0v2l-3-6c-2-5-4-9-6-14-1-4-3-7-4-10-1-2-2-4-3-5z"></path><defs><linearGradient id="D" x1="536.39" y1="170.688" x2="450.786" y2="321.74" xlink:href="#B"><stop offset="0" stop-color="#bdbdbd"></stop><stop offset="1" stop-color="#eeeeed"></stop></linearGradient></defs><path fill="url(#D)" d="M545 174l19 16-65 120c-3 2-8 3-12 4-16 3-31 2-47 3l14-30 12-32c2-6 4-11 5-17v-1c6-1 13-1 19-2-11-1-23-3-33-1 3-2 7-4 11-6l13-6 15-9c3-2 5-5 8-6 8 2 17 6 23 11h1c-6-6-15-9-23-12l40-32z"></path><path fill="#fdfdfd" d="M215 345v-1l1-1c4 0 9 1 13 2 3 1 6 2 8 3 2 2 4 4 6 5h1c1 1 2 3 3 5 1 3 3 6 4 10 2 5 4 9 6 14l3 6 14 25c5 8 10 15 16 22l12 16c2 3 4 5 6 8h-1c3 4 7 8 10 11s6 4 9 6c4 1 10 6 14 6h1l3 6c2 2 3 5 5 9 1-4 4-8 6-12h1 1c3-1 5-2 7-4s5-3 7-5 4-4 7-6h0-2c-4 1-8 1-12 2-1 0-3 1-4 2 6-9 10-19 15-29l13-25c3-5 5-11 8-17h0 1c4 2 11 2 15 1 3-1 5-1 7 0 1 2 3 3 3 5a24.56 24.56 0 0 1-5 15v1l-2 2c-3 5-8 8-12 12-4 2-7 5-10 9h0c-1 2-2 3-2 5-1 1-1 4 0 5v1 1c0 3 2 8 3 10 3 9 3 19-1 27-3 5-8 9-12 13l2 1c3 3 6 5 8 8 0 2-4 7-5 9l-8 17-20 38c-2 5-5 10-7 14v5 10l-2 32c0 7 1 15-1 22 0-5 0-10-1-16l-1-40v-8-6c-1-2-3-4-4-7v-1l-36-68 10-7h1l-15-3h0l-1 1c-3 0-9-3-12-4l12 2-16-29-12-2c1-1 10 0 12 0l-9-17c4 0 9-1 13-2-4 0-12 1-16-1-1-4-3-8-5-11l-15-30-25-48c-4-8-8-16-13-24z"></path><defs><linearGradient id="E" x1="228.348" y1="350.012" x2="231.73" y2="345.351" xlink:href="#B"><stop offset="0" stop-color="#848082"></stop><stop offset="1" stop-color="#a1a2a2"></stop></linearGradient></defs><path fill="url(#E)" d="M215 345v-1l1-1c4 0 9 1 13 2 3 1 6 2 8 3 2 2 4 4 6 5h1c1 1 2 3 3 5 1 3 3 6 4 10l-1 1h0c0-1 0-1-1-1 0-3-3-7-4-10l-1-1c0-1-2-2-2-2-8-6-17-9-26-11l-1 1z"></path><defs><linearGradient id="F" x1="262.507" y1="387.982" x2="256.173" y2="388.627" xlink:href="#B"><stop offset="0" stop-color="#7f7e7b"></stop><stop offset="1" stop-color="#959499"></stop></linearGradient></defs><path fill="url(#F)" d="M251 368c2 5 4 9 6 14l3 6 14 25c5 8 10 15 16 22l12 16c2 3 4 5 6 8h-1c-9-12-19-24-28-36l-17-27-7-12c-1-3-3-7-4-10v-1c-1-2-1-3-2-5h0c1 0 1 0 1 1h0l1-1z"></path><path fill="#e5e5e5" d="M334 527c1 0 1 1 2 1 0 1 0 1 1 2h-1c0 3-1 6-1 9 1 1 2 2 4 3l1 1 1 1c1 0 2 1 3 1 1 1 3 1 4 3 0 11-2 22-4 32 0 2 0 7-2 9v-1-4c1-3 0-6 1-10l2-21c-3-2-6-4-8-7-1-2-3-4-3-6 0 0 0-1-1-1 0-2-1-3-1-5 1-2 1-5 2-7h0z"></path><path fill="#4a4a4a" d="M317 501c-1-4-2-8-3-11s-2-5-3-8c1 2 3 4 4 6l8 12 9 15c2 2 3 4 4 6s2 3 1 5v4c-1-1-1-1-1-2-1 0-1-1-2-1-5-6-10-12-14-18-1-3-2-6-3-8z"></path><path fill="#5d5c5d" d="M317 501c1 1 2 1 2 3l2-1c2 2 3 6 5 8l9 11v2h0c1 2 1 3 1 4-1 0-1-1-2-1-5-6-10-12-14-18-1-3-2-6-3-8z"></path><path fill="#4a4a4a" d="M340 166h1 1 1 0c1-1 1-1 2-1l1 1h0 1 1 2 3 1 0 1 4l1 1v-1c1 1 1 1 2 1h0 1 0 1 2 0 1v1l1-1c0 1 1 1 2 1s2 0 3 1h0 1 2 1 1l1 1v-1c0 1 1 1 1 1h1l1 1v-1l1 1h0 0c1 0 2 0 3 1v-1c1 1 1 1 2 1h1 0c1 1 1 1 2 1s1 0 2 1v-1c1 1 1 1 2 1h1l1 1h1 1l2 1h1 0l1 1h1c1 0 1 0 2 1h0 2v1-1l1 1h1c1 1 2 1 2 1h1 0l1 1c1 0 1 0 1 1h2s1 1 2 1h1v1-1l1 1c1 1 1 1 2 1h0 0 1c1 2-1-1 1 1h0 0c1 0 1 1 1 1h1l1 1 1 1h0 1 0 1c0 1 0 1 1 1 0 1 0 1 1 1l1 1h0l3 3h0l2 2h0c0 1 1 1 1 2v1c1 1 1 1 1 2v6h0c0 1 0 2-1 2v1 2h0v1h-1v2 1h0c-1 1 0 2 0 3-1 1-1 1-1 2v4c-1 2-1 7-1 9v14c-1 1 0 3 0 4-1 1-1 3-1 4v3l-1 5h0l-1 1v4c-1 1-1 0-1 1v1l-1 6s-1 1-1 2h0v2c-1 0-1 0-1 1l-1 3v1l-1 1v2l-2 6c0 1-1 2-1 3h0c0 1 0 1-1 2 0 1-1 4-2 5 0 2-1 4-2 6-1 1-1 3-2 5h0l-1 2-4 11h-1v2l-3 6h0l-2 4c0 1-1 3-1 4l-1 2c-1 0-1 1-1 1-1 3-2 5-3 7 0 1-1 1-1 2l-1 2c0 1 0 1-1 2l-2 4-2 6-2 3-2 6-1 1v1c-1 1 0 1-1 1l-1 3-1 3-9 17c0 1-1 2-1 2 0 1-1 2-1 3l-1 2c0 1-1 1-1 2l-1 3-2 3-1 1-2 4-2 4-2 2v1l-2 2c0 1-1 2-2 3-1 0-1 1-2 2h-1-1l-1 1h-2c-1 1-4 1-5 1l-1-1h-2-1c-1 0-2-1-3-1l-1-1c-1 0-1-1-2-1-1-1-2-2-2-3h-1v-1l-3-3c0-1-1-2-1-3-2-2-3-4-4-7-1-1-1-2-2-2l-3-8c-1 0-1-1-1-2l-2-3c0-1-1-1-1-2l-4-8-2-4c0-1 0-1-1-2h0l-1-2v-1s-1-1-1-2l-1-2-2-5-2-3-1-4c-1-1-1-2-2-2l-2-5-1-1v-1c0-1 0-1-1-1 0-2-1-4-1-5l-2-3-3-6v-2c-1-1-1-2-2-3l-1-3-2-5c-1-1-1-2-2-3l-1-2v-2l-1-1-1-2-3-8v-1c0-1-1-1-1-2s-1-2-1-3c0 0-1-1-1-2v-1-1c-1-1-1-2-2-3h0v-2c-1 0-1-1-1-1 0-1 0-1-1-2v-1-1s0-1-1-1v-1-2l-1-3c-1 0-1-1-1-2h0l-1-1v-2l-1-3v-1c-1-1-1-2-1-3-1-1-1-1-1-2v-1-1c-1-1-1-1-1-2v-1c-1-1 0-1 0-2-1-1-1-1-1-2v-2c-1 0-1 0-1-1v-2c-1-1 0-2 0-3v-1h-1v-3c-1-1 0-3 0-4-1-1-1-4-1-6h0v-12c-1-1 0-4 0-6h-1v-5-2c-1-1-1-1-1-2v-2c-1-1 0-2 0-2-1-1-1-1-1-2v-2-2c-1-1-1-5-1-7l1-1v-2h1v-2h1c1-1 1-3 3-4 0 0 1-1 2-1l1-1 1-1 7-4 1-1h1 1l1-1h0l4-2c1 0 1-1 2-1h0 0 1l2-1s1 0 1-1v1l1-1c1 0 2 0 2-1 1 0 2 0 3-1h2l1-1v1l1-1c1 0 2 0 3-1h0 1 1v-1h1 2c0-1 1-1 2-1h0c2 0 2-1 4-1h0 1v-1h3 0 1v-1h1 2v-1h1 1 0 2c0-1 1 0 2 0v-1h1 3 0 1 1v-1l1 1c1-1 2 0 3-1h1 1 0c1-1 4 0 5 0z"></path><defs><linearGradient id="G" x1="348.605" y1="213.601" x2="344.128" y2="430.638" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#868586"></stop></linearGradient></defs><path fill="url(#G)" d="M337 214h0 1 0 1 1 0 0 9 1 0 1 0 1 0 1 0 1c0 1 2 0 3 0v1h3 2 0 0 1 0c1 0 1 0 2 1 0 0 1-1 1 0h2c1 1 1 0 2 0 1 1 1 1 2 1l1 1v-1h0l1 1h2 0 1c1 1 0 0 1 0 1 1 1 1 2 1l2 1c1 1 1 0 1 0 1 1 2 1 3 2h0 0 1c1 1 2 1 4 1 0 1 1 1 2 2h1l1 1c1 0 2 0 3 1l5 2 1 1c1 0 2 1 3 1 1 1 2 2 4 2l1 1 1 1c1 0 1 1 2 2 2 1 3 2 4 4h1l2 3v1 1l1 1 1 2v1 2c1 0 1 1 1 2v7c-1 1 0 2 0 3-1 1-1 2-1 3v3c-1 0-1 0-1 1v2 2c-1 1-1 1-1 2v2c-1 0 0 1 0 1-1 1-1 1-1 2h0v2c-1 1-1 1-1 2v1s0 1-1 1c0 1 0 3-1 4v2l-1 1v1c0 1-1 1-1 2s0 1-1 2v1h0v1h0l-1 1v1 1c-1 2-2 5-3 7l-1 2v1h0v1c-1 1-1 1-1 2h0c-1 3-3 5-4 8h0v1 1l-1 1v1c0 1 0 1-1 1 0 1-1 3-1 4v1l-1 1v1c-1 1-1 1-1 2h0c-1 1-1 3-2 4l-1 3-1 2-1 2-2 4-1 3v1l-4 8-2 4-1 4c-1 0-1 1-2 2l-2 4-1 4-1 1v1h0l-3 6-2 5-2 4-1 2-4 7-2 4-3 5-1 2c0 1-1 1-1 1-1 1-2 2-2 3l-1 1-1 1c-1 0-2 1-3 2h0-2c-1 1-2 1-3 1h0c-1-1-2 0-3-1h-1c-2 0-3-1-5-2 0-1-1-1-1-2l-2-2h0l-2-4c-1-1-3-3-3-4l-1-2-2-2-2-4v-1c-1-1-1-1-1-2h0l-1-2s-1 0-1-1v-1l-2-3-1-3-2-5c-1 0-1-1-1-2l-5-9v-2l-2-3-2-5c-1 0-1-1-1-2l-2-3v-1c-1-2-1-3-2-4l-2-4c0-2-1-3-2-5h0l-1-4-2-4-1-2-1-3-1-2-1-2c0-1 0-1-1-2v-1c0-1-1-1-1-2v-1l-1-1v-1l-3-6v-1l-1-1v-1c0-1 0-2-1-2v-1-1l-1-1c0-1-1-2-1-4 0-1 0-2-1-3v-1h-1v-2-1h0c-1-1-1 0-1-1h0v-1c0-1 0-1-1-2v-1l-1-3c0-1 0-1-1-2v-2c0-1-1-2-1-3v-2c-1 0-1 0-1-1v-2-2c-1 0-1-1-1-2v-2c-1 0 0-1 0-2-1 0-1-1-1-2v-3h-1c1-1 0-2 0-3v-2-2h0v-2-2h0v-1-1h0v-1c1-1 1-2 1-3v-1l1-1v-1s0-1 1-1v-1c0-1 1-2 1-2v-1h0l1-1c1-2 5-4 6-6l1-1c1 0 1 0 2-1 1 0 1-1 2-1h0l2-1c1-1 2-2 3-2l11-6 3-1h1l1-1h1 0c1-1 2-1 3-1 1-1 1-1 2-1h1v-1h1 1 1 1c0-1 0-1 1-1h2l1-1h2 2c0-1 1-1 2-1h0 0 3c1-1 1 0 2 0l1-1h1 1z"></path><defs><linearGradient id="H" x1="347.103" y1="284.174" x2="346.448" y2="411.246" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#H)" d="M340 284h4 9c1 1 3 0 4 0v1h1 2 1 0c1 1 1 1 2 1h0l2 1h1l2 1h1 0c1 1 2 1 3 2 1 0 1 0 2 1 1 0 2 1 3 1l1 1 2 2c1 0 1 1 2 1l3 3h0c0 1 1 1 1 2l3 3v2c1 0 1 1 1 2h0l1 1v1 2c1 1 1 6 1 8-1 1-1 3-1 3v1c0 2-1 3-1 5v1l-1 2h0v2l-1 1c0 2-1 4-2 6v1c-2 6-5 11-7 17-1 1-1 2-2 3v2l-3 6h0v2c-1 1-1 2-1 2l-1 2c-1 1-1 2-1 2l-1 2c-1 1-1 2-1 2l-9 18-1 1v1l-1 1c0 1 0 1-1 1 0 1-1 1-1 2l-3 3-2 2h-1-2 0-1-2-1c-1-1-1-1-1-2-1 0-2 0-3-1l-1-2c0-1-2-2-2-3l-1-1v-1l-3-5v-1h-1l-2-4-1-3-3-6-4-7v-2c0-1-1-1-1-2l-1-2s0-1-1-2v-1l-2-3c0-1 0-2-1-2v-2l-1-1c-1-2-2-4-2-6l-6-14v-1h0c0-1 0-1-1-2v-1-1h0l-1-3v-2c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-3c-1 0 0-5 0-6v-2-1s0-1 1-2h0v-2h1c0-1 0-2 1-3h0c1-1 1-2 2-3l1-1c0-1 2-3 3-4 1 0 2-1 2-1l2-2h1l2-1c1-1 1-1 2-1v-1h1s1 0 1-1h3c0-1 0-1 1-1s1 0 2-1h2l1-1h1 2 1 0c1-1 3-1 4-1z"></path></svg>