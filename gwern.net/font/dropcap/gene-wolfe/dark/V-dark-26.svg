<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="108 36 850 912"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#151413}.C{fill:#b7b5b3}.D{fill:#b7b6b4}.E{fill:#242322}.F{fill:#383737}.G{fill:#9a9997}.H{fill:#dbdad6}.I{fill:#e5e4df}.J{fill:#1b1b1a}.K{fill:#deded9}.L{fill:#757473}.M{fill:#696867}.N{fill:#d0cfcc}.O{fill:#8f8e8c}.P{fill:#0b0b0a}.Q{fill:#4e4d4d}.R{fill:#5d5d5a}.S{fill:#a5a4a2}.T{fill:#ebecdf}.U{fill:#c0c0bb}.V{fill:#c8c7c6}.W{fill:#7e7d7c}.X{fill:#858482}.Y{fill:#434241}.Z{fill:#555453}</style><path d="M565 156c2 1 5-1 7-2 4-1 7-2 11-3 25-5 51 8 63 29 7 12 10 24 9 38 0 2 1 5 0 8v-11c-1-10-4-22-9-31-7-14-20-25-35-30-18-5-35-1-51 7 2-1 3-3 5-5z" class="G"></path><path d="M513 918c1-1 2-2 2-3l10-21 9-21 30-64 8-19 14-29 3-6 43-94 1-4 31-68c2-3 4-7 5-10l7-15 9-21c5-11 11-22 16-34l12-27c1-3 3-6 4-9l10-23 8-17 2-4 3-6c1-2 2-5 3-8l5-11 20-44 26-56 10-22 6-14 7-15 3-6 4-9 6-13 2-6c2-5 5-9 7-14 5-13 12-25 21-35 5-5 11-9 17-12 14-8 28-9 44-6 4 1 7 2 11 3l-3-6a41.54 41.54 0 0 1 0-31l1-2c1-3 3-6 6-8-5 7-7 13-8 21-1 10 2 20 8 28 1 1 2 3 3 4-16-9-32-12-50-7-18 6-32 19-41 36L515 920c-1 0-1-1-2-2z" class="C"></path><path d="M513 918l-6-14-13-31-34-75-112-252-97-217-39-89c-7-17-14-34-23-50-2-4-4-8-7-12-2-3-4-5-6-8h0c-3 0-8-6-10-8-6-3-12-7-18-8-6-2-12-3-18-3-12 1-22 5-32 10l6-7c6-8 8-19 6-28v-1c-2-7-5-14-11-20h0 379c-4 4-7 9-9 14v1l-1 5v1l-1 3c-1 8 1 17 5 23 2 3 4 6 7 9-6-3-11-6-17-8-13-2-25-2-37 3-14 6-28 19-33 35l-1 3-1 4-1 1v2l-1 2c0 2 0 4-1 6-1 8-1 16 1 25 1 11 5 22 10 32s9 21 13 32l28 64 75 174c1 2 2 5 3 8 0 1 0 2 1 3l3-9 12-27 69-152 30-67c6-13 13-27 18-40 3-9 4-18 5-26 1-3 0-6 0-8 1-14-2-26-9-38-12-21-38-34-63-29-4 1-7 2-11 3-2 1-5 3-7 2 4-6 7-14 7-22 1-11-4-21-11-29h378l-3 3c-3 2-5 5-6 8l-1 2a41.54 41.54 0 0 0 0 31l3 6c-4-1-7-2-11-3-16-3-30-2-44 6-6 3-12 7-17 12-9 10-16 22-21 35-2 5-5 9-7 14l-2 6-6 13-4 9-3 6-7 15-6 14-10 22-26 56-20 44-5 11c-1 3-2 6-3 8l-3 6-2 4-8 17-10 23c-1 3-3 6-4 9l-12 27c-5 12-11 23-16 34l-9 21-7 15c-1 3-3 7-5 10l-31 68-1 4-43 94-3 6-14 29-8 19-30 64-9 21-10 21c0 1-1 2-2 3z" class="T"></path><path d="M391 275h0c1 1 1 1 1 3l-1 1-2-2c1-2 1-2 2-2z" class="P"></path><path d="M538 522h3c1 2 0 2 0 3h-1c-1 0-1 0-2-1v-2z" class="E"></path><path d="M571 453h1c1 0 1 0 2 2l-1 1c-1 1-1 0-3 0 0-2 0-2 1-3z" class="J"></path><path d="M607 372c1 0 1 1 2 1 0 2 0 2-1 3h-1c-2-1-2-1-2-3l2-1z" class="F"></path><path d="M494 522h3c1 2 1 2 0 3l-1 1h-1c-1-2-1-2-1-4z" class="P"></path><path d="M371 222h1c1 0 2 0 3 1v2l-1 1h-3v-4z" class="J"></path><path d="M397 291h2v2c0 1 0 1-1 3l-2-1c-1-1-1-1-1-3l2-1zm268-88h3c1 2 1 2 0 4h-1c-1 0-1 0-2-1s0-1 0-3z" class="E"></path><path d="M446 407h1c2 0 1 1 2 2l-2 2h-1c-1-2-1-2-1-3l1-1z"></path><path d="M425 357h1l2 1c0 1 0 1-1 3h-2c-1-1-1-1-1-3l1-1z" class="B"></path><path d="M374 203c2 0 2 0 3 1 0 2 0 2-1 3s-2 1-3 0c0-2 0-2 1-4z" class="E"></path><path d="M649 275c2 0 2 0 3 1 1 2 0 2 0 3l-1 1c-1 0-2-1-2-1-1-2-1-2 0-4z" class="B"></path><path d="M376 237c1 0 1 0 2 1s1 1 1 3l-2 1h-1c-1-2-1-2-1-4l1-1z" class="E"></path><path d="M480 488h2c1 0 1 0 2 1 0 2 0 2-1 3-1 0-1 0-2 1l-1-1c-1-2-1-2 0-4z"></path><path d="M526 546c2 0 2 0 4 1 0 2 0 2-1 4-2 0-2-1-3-2v-3z" class="B"></path><path d="M438 389h2l1 1c0 2 0 2-1 3h-2c-1-1-1-1-1-3l1-1z" class="P"></path><path d="M642 291h1c2 0 2 1 3 1 0 2 0 2-1 4h-1c-1 0-2-1-3-1 0-2 0-2 1-4z" class="E"></path><path d="M666 222l1-1c2 1 2 1 3 2-1 2-1 2-2 3-1 0-2 0-3-1 0-2 0-2 1-3z" class="B"></path><path d="M507 547h3v1c0 2 0 2-2 3h-1l-2-2c0-1 1-1 2-2z" class="J"></path><path d="M466 454h1c1 0 2 1 2 2 0 2 0 2-2 3-1 0-1 0-2-1 0-1-1-1-1-2s1-2 2-2z" class="E"></path><path d="M432 373h3v4l-2 1-1-1c-1-1-2-1-2-2 1-2 1-2 2-2z" class="B"></path><path d="M404 307h1c2 0 2 0 2 2s0 2-1 3h-2l-2-1v-3l2-1z"></path><path d="M635 307h2c2 1 2 1 2 3 0 1 0 2-1 3h-2c-1 0-2-1-3-2 0-2 1-2 2-4z" class="J"></path><path d="M120 118h338c-3 7-4 14-2 22h0c-6-1-12-1-18 0-11 1-22 6-31 12-5 4-11 9-15 14s-6 11-10 16c-6 9-13 14-14 25-2 16 2 33 7 48 3 8 7 17 11 26l22 52 77 178 26 61 4 9c1 2 1 4 3 6l102-226 30-66c7-16 14-31 20-48 4-13 7-28 4-42 0-3-2-6-3-9-3-5-7-9-11-13-4-7-8-14-14-20-4-4-8-8-13-12-16-10-32-14-51-10 2-7 2-15-1-22v-1h337c-2 8-3 14-2 22h0-9c-22-1-41 10-57 25-7 7-12 15-16 23l-26 57-42 91-63 139-36 81-96 209-33 73-23 52-35-78-35-79-128-288-75-169-26-58c-5-11-10-23-16-34-7-13-17-24-30-32-14-10-32-15-49-11 2-8 1-16-1-23z" class="B"></path><path d="M135 121h318c-1 5-1 10-1 15-5 0-10 0-14 1h-1c-4 1-9 2-13 3-14 5-27 14-35 26-4 5-7 11-10 16-4 5-9 10-11 15-7 15-3 36 2 51 2 9 6 18 10 26l22 54 71 164 24 54 20 49c2-2 3-5 4-7l5-12 20-45 78-172 25-54c8-18 17-36 23-55 4-13 7-29 5-42 0-5-1-8-3-12-3-6-8-11-12-16s-7-11-11-17c-3-3-6-6-10-9-16-13-34-20-55-17 1-6 0-11-1-16h329c-2 5-2 10-2 16h-8c-23 0-44 13-59 29-11 13-18 28-25 43l-16 36-41 92-107 234-61 134-55 120-26 56-17-41-37-81-112-251-92-208-30-67c-7-16-14-33-22-49-5-10-12-19-21-26-16-14-37-23-58-21 0-6 0-11-1-16h11z" class="T"></path><path d="M686 368c-1-2-1-2-1-4h1l1 2c0 1 1 2 0 3l-1-1z" class="K"></path><path d="M173 125h2v3h-3c0-1 0-2 1-3zm435 533c1-1 2-1 3 0 0 1 0 2-1 3h-2c-1-2 0-2 0-3z"></path><path d="M470 766h2c0 2 1 2 0 3l-1 1h0l-2-2 1-2z" class="E"></path><path d="M271 319h3c0 2 0 2-1 3h-1l-2-1 1-2z"></path><path d="M830 176c1 0 1 0 2 1-1 1-1 2-2 3l-2-2c0-1 0-1 2-2z" class="J"></path><path d="M220 205l2-1c1 1 1 1 1 3l-1 1c-1 0-2 0-3-1v-1l1-1z" class="B"></path><path d="M782 277h3c0 2 0 2-1 3h-1l-2-2 1-1zM662 173h0c1 0 1 0 3 1 0 1 0 1-1 2h-1l-2-1 1-2z"></path><path d="M816 200c1 0 2 0 3 1 0 1 0 1-1 3-1 0-1 0-2-1l-1-1 1-2zM542 802c1 0 1 0 2 1s0 1 0 3c-2 0-2 0-3-1 0-1 0-1 1-3zM198 163l1-1c1 1 1 1 1 3l-1 1h-1c-1 0-1 0-1-1l1-2z" class="P"></path><path d="M317 421c1 0 1 0 2 1v2l-2 1c-1 0-1-1-2-2v-1l2-1z" class="E"></path><path d="M822 188c1 0 2 0 3 1 0 1 0 1-1 3h-1c-1 0-1-1-2-2l1-2zm30-36h2c0 2 0 2-1 3l-1 1c-1-1-1-1-1-3l1-1z" class="P"></path><path d="M755 339h2v2c0 1 0 1-2 2l-2-2 2-2z"></path><path d="M429 671c1 0 1 0 2 1v2l-1 1c-1 0-2 0-3-1v-1l2-2z" class="J"></path><path d="M334 459h1c1 1 1 1 1 2l-2 2-2-2c0-1 1-1 2-2z"></path><path d="M410 137h1c1 0 1 1 2 3l-1 1c-2 0-2 0-3-1 0-2 0-2 1-3z" class="B"></path><path d="M562 760c2 0 2 0 3 1 0 2 0 2-1 3-2 0-2 0-3-1 0-1 0-2 1-3z" class="E"></path><path d="M466 756h0c2 1 2 1 2 3l-1 1c-2 0-2-1-3-1v-2l2-1z" class="B"></path><path d="M748 352c1 0 1 0 3 1 0 1 0 1-1 3h-1l-2-1c0-1 0-1 1-3zM371 544c1 0 1 0 3 1 0 1 0 1-1 3h-1l-2-2 1-2z" class="E"></path><path d="M762 322c2 0 2 0 3 1v2l-1 1h-3v-2l1-2z" class="B"></path><path d="M496 822h2v3l-2 1c-1 0-1-1-2-2v-1l2-1z"></path><path d="M712 429h1c1-1 2 0 3 0 0 2 0 2-1 3h-1c-2 0-2 0-2-1v-2z" class="P"></path><path d="M548 790h2c1 0 1 0 1 2l-1 2h-1c-1-1-1-1-2-3l1-1z" class="E"></path><path d="M307 403l3-1 1 2c0 1-1 1-2 2h-1c-1-1-2-1-2-2l1-1z" class="B"></path><path d="M377 172c2 0 2 1 3 1 0 2 0 2-1 4h-1c-1 0-1-1-2-2 0-1 0-2 1-3z" class="E"></path><path d="M237 237l1 1c1 1 1 1 1 3h-2-2c-1-1 0-1-1-2 1-1 2-1 3-2z" class="J"></path><path d="M692 474l2-1c1 1 1 1 1 2l-1 2v1c-1 0-2-1-3-1 0-2 0-2 1-3zM571 741c1 0 1 0 2 1 0 2 0 2-1 4l-2-2c-1 0-1-1-1-1l2-2z" class="B"></path><path d="M615 642l1-1c2 1 2 1 2 2v2h-1c-1 1-1 0-2 0-1-1-1-2 0-3z" class="J"></path><path d="M580 720c2 0 2 0 3 1s0 2 0 3c-2 0-2 0-3-1s-1-1 0-3z" class="E"></path><path d="M674 513h2l2 2-1 2h-2l-1-1c-1-1 0-1 0-3z" class="J"></path><path d="M391 590h2l1 1c0 1 1 2 0 3h-2l-2-2 1-2zm-97-216c2 0 2 0 4 1 0 1 0 1-1 3h-1c-2-1-2-1-2-2v-2z"></path><path d="M423 657c1 0 1 0 2 1l-1 2-2 2c-1-1-1-2-2-2 1-2 1-2 3-3z" class="E"></path><path d="M322 432h1 1v2l-2 1c0 1-1 1-2 0l-1-1c1-1 1-1 3-2z" class="J"></path><path d="M589 699c2 0 2 0 3 1 0 2 0 2-1 3-1 0-1 0-3-1 0-1 0-1 1-3z"></path><path d="M436 687s1 0 1 1c1 1 1 1 1 3l-1 1c-2-1-2-1-3-2v-1l2-2z" class="P"></path><path d="M725 402h0c1-1 2-1 3 0 1 2 0 2 0 3h-1c-1 1-2 0-3 0 0-2 0-2 1-3z" class="J"></path><path d="M185 151h2l1 1c0 2 0 2-1 4-1 0-1 0-1-1-1-1-2-1-2-2l1-2z"></path><path d="M313 411c1 0 1 0 1 1 1 0 1 1 1 2-1 1-2 1-3 2l-2-2c1-2 2-2 3-3z" class="E"></path><path d="M706 443c1 0 2 0 3 1 0 2 0 2-1 3h0c-1 0-2 0-3-1 0-2 0-2 1-3zm-379 1c1 0 2 0 2 1 1 1 1 1 1 3l-2 1-1-1c-1-1-1-1-1-3l1-1z" class="B"></path><path d="M277 332h2c1 1 1 1 1 3l-1 1h-1l-2-1c0-1 0-1 1-3z"></path><path d="M897 125c1-1 1-1 3 0v4h-2l-1-1c-1-1-1-2 0-3z" class="J"></path><path d="M266 305c1 0 1 0 2 1 0 1 1 1 0 3h-2-2l-1-1c1-2 2-2 3-3zM139 125c2 0 2 0 3 1 0 2 0 2-1 4h-2c-1-2-1-2-1-4l1-1z" class="E"></path><path d="M837 164h3l1 1c0 2-1 2-1 3l-1 1c-1 0-1-1-2-1-1-2-1-2 0-4zM647 573c1 0 2 0 3 1s0 1 0 3h-1c-2 0-2 0-3-1 0-1 0-1 1-3z" class="J"></path><path d="M156 124h2c1 2 1 2 1 3l-1 2c-1 0-2 0-3-1v-1c0-2 0-2 1-3z"></path><path d="M366 269l4 7-2 1c-2-1-3-1-3-3 0-1 1-1 1-2h0v-2-1zm333 188c2 0 2 0 4 1 0 1 0 2-1 3l-1 1c-1 0-1 0-2-1-1-2-1-2 0-4z" class="F"></path><path d="M289 124h0c2-1 2 0 3 1 0 2 0 2-1 3h-1c-2 0-2 0-3-1 0-2 1-2 2-3zm80-1h3c1 2 1 2 1 4l-1 1c-2 0-2 0-3-1v-4z" class="B"></path><path d="M213 187h1c1 0 1 1 2 2 0 1-1 2-2 3h-1l-2-2v-1l2-2zm242 545h2c1 2 1 2 1 4l-2 1h-1c-1 0-1-1-2-2 0-2 1-2 2-3z"></path><path d="M406 618h1c1 2 1 1 1 3-1 2-1 2-2 2h-1c-1-1-1-1-2-3 1-1 1-1 3-2zm327-234h0c2 0 2 0 3 1v3c-2 1-2 1-3 1l-1-1c0-2 0-2 1-4z" class="B"></path><path d="M601 672c2 0 2 0 4 1 0 2 0 2-1 4h-1c-1 0-2 0-3-1 0-2 0-2 1-4z"></path><path d="M514 860l1-1c1 0 2 1 3 2 0 1 0 2-1 3h-1c-1 1-2 0-3-1 0-2 0-2 1-3z" class="J"></path><path d="M417 645h2l1 1c0 2 0 2-1 3l-1 1c-2-1-2-1-3-3l2-2z"></path><path d="M290 360h1l2 2c0 2-1 2-2 3h-2c-1-1-1-1-1-3l2-2z" class="B"></path><path d="M621 628h1c1 0 2 0 3 1 0 1-1 2-1 3l-2 1-2-2c0-2 0-2 1-3zm-235-52c1 0 1 0 2 1s1 2 1 3l-2 1h-2c-1-2-1-2-1-4l2-1z"></path><path d="M632 603h1c2 0 2 1 3 2 0 2-1 2-2 3h-1c-1 0-2 0-2-1 0-2 0-2 1-4zM250 269h2c1 1 1 1 1 3 0 1 0 1-1 2s-2 0-3 0c-1-1-1-2-1-3l2-2zm351-143l2-1c2 1 2 1 3 2 0 2 0 2-1 3s-2 1-3 1l-2-2c0-1 1-2 1-3z" class="J"></path><path d="M337 394h0l1-2 1 2h0v3c0 3 0 7-1 10 0-3 0-5-2-8 0-1 0-3 1-5z" class="L"></path><path d="M229 221h2c1 2 1 2 1 4l-2 2h-2c-1-1-1-1-2-3 1-2 1-2 3-3z" class="B"></path><path d="M639 350c1-2 3-5 5-7h5l-5 1v1c-1 2-3 4-2 6l1 2-2 2h-1l-1-5z" class="Z"></path><path d="M405 123h2c1 2 1 2 1 4 0 1-1 2-2 3-1 0-2-1-3-1-1-1-1-2-1-3 1-2 2-2 3-3z"></path><path d="M644 345c2-1 2-1 4-1v1l-2 3c0 2-2 3-2 5h-1l-1-2c-1-2 1-4 2-6z" class="B"></path><path d="M689 366c2 2 3 5 4 8h-1l-3-2-4-3-1 1-1-2h1 1 1l1 1c1-1 0-2 0-3h2z" class="I"></path><path d="M570 503l2 1c1 1 1 2 0 4l-2 2c-1 0-2 1-3 3l-1-1c1-3 2-7 4-9z" class="E"></path><path d="M670 355c2 1 3 3 5 5l1 3c-1 1-2 2-3 4l-2-1c0-2 1-7-1-9v-2z" class="K"></path><path d="M439 124h2c1 1 2 2 2 4-1 1-1 2-3 2-1 1-2 0-3 0-1-1-1-2-1-3 1-2 1-2 3-3z"></path><path d="M395 144h1c1-1 2-1 3 0s1 3 1 4-1 2-2 3c-1 0-2 0-3-1s-1-1-2-3c0-2 1-2 2-3z" class="P"></path><path d="M644 143c1 0 1 0 2 1s2 1 2 3c-1 2-1 3-3 4h0c-2 0-3-1-4-2s0-1 0-3c1-1 1-2 3-3z"></path><path d="M684 370l1-1 4 3c1 1 1 2 1 3 1 3 2 4 3 7v1h0c1 1 1 1 1 2v1 1h-2l-2-5c-1-4-4-9-6-12z" class="U"></path><path d="M649 343c2 1 4 1 5 2 1 2 1 4 0 6l-1-1c-1 0-1 0-2 1h-2c-2-1-2-2-3-3l2-3v-1c-2 0-2 0-4 1v-1l5-1z" class="E"></path><path d="M461 513c4-1 7-2 10-1l1 3h-7c-2 0-4 1-7 2h-2c-2 0-4 0-5-1 3-2 7-2 10-3z" class="B"></path><path d="M351 263c4 0 8 0 12 1l3 4c-2 1-4 0-5 0h-3c-2-1-3-1-5-1-1-1-2-2-3-2h-1l1-1 1-1z"></path><path d="M386 320l1-1c4 7 8 15 10 22h-1c-4-1-8-1-12 0v-1c3-2 6-1 9-2l-1-1c0-2 0-3-1-4v-1c0-1-1-1-2-2l-3-10z" class="E"></path><path d="M334 417l1-4c1 2 0 3 1 5 0 2 0 4 2 6 0 4 0 9 1 14v1 1c1 0 1 1 1 2 1 2 0 3 1 4l-1 2-5-9 1-1c1 1 0 1 1 2-1-8-3-16-3-23z" class="F"></path><path d="M392 337l1 1c-3 1-6 0-9 2v1c-2 1-3 1-5 2-4 1-7 4-11 5-2 1-5 3-7 4v-1c2-1 3-3 6-4 4-4 10-6 15-8v-1h0l10-1z" class="O"></path><path d="M334 417c-1-4 0-10 0-14 0-3 0-5 1-8v8c1 0 1-3 1-4 2 3 2 5 2 8v17c-2-2-2-4-2-6-1-2 0-3-1-5l-1 4z" class="R"></path><path d="M280 133c9 0 17 0 26 2 3 0 6 1 9 2h-3c-8-2-17-3-25-1l-11 3c-1 0-2-1-3 0l-1-1v-2h2c2-1 4-1 6-2v-1z" class="B"></path><defs><linearGradient id="A" x1="335.067" y1="386.965" x2="345.433" y2="380.535" xlink:href="#B"><stop offset="0" stop-color="#868581"></stop><stop offset="1" stop-color="#a2a39f"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M346 367c4-5 9-12 15-16v1l-3 3c-4 2-8 8-10 11-1 2-1 4-2 6-4 8-5 16-7 25v-3h0l-1-2-1 2h0c-1-1 0-3 0-5 1-5 3-11 5-16l4-6z"></path><path d="M681 355c-1-1-2-2-4-3l-1-1c-2-1-3-2-4-3l-4-2c-1 0-2-1-3-1-2-1-5-1-6-2-2-1-1-1-2-1h-2c-2-1-5-1-6-1l-1 1c-2 0-3 0-4-1h0 1v-1-2c1 1 1 1 2 1 7-1 16 0 22 2l3 2c-2 0-4 0-5-1h-2c2 1 3 1 5 3l1 1 2 1 4 3c1 1 3 3 4 5z" class="U"></path><path d="M761 133c5-1 12 0 18 0h48c-5 1-10 1-15 1-11 0-22-1-33 0h0l-1 1c1 1 2 2 3 2l-3 1c-1 1-1 2-1 4-4-3-7-4-11-6-1 0-2-1-4-1h0l-1-2z" class="Q"></path><path d="M342 368c-1 2-1 3 0 5-2 5-4 11-5 16 0 2-1 4 0 5-1 2-1 4-1 5s0 4-1 4v-8c-1 3-1 5-1 8 0 4-1 10 0 14 0 7 2 15 3 23-1-1 0-1-1-2-1-5-2-11-2-17l-1-24v-1c1-2 1-4 2-6 0-2 0-4 1-7 0-3 2-7 3-10l3-5z" class="N"></path><path d="M382 338v1c-5 2-11 4-15 8-3 1-4 3-6 4-6 4-11 11-15 16l-4 6c-1-2-1-3 0-5l1-5 2-3h0c1-2 3-4 4-5 6-7 16-11 25-15l8-2z" class="H"></path><path d="M343 363l2-3v5 2h1l-4 6c-1-2-1-3 0-5l1-5z" class="I"></path><defs><linearGradient id="C" x1="649.213" y1="349.402" x2="643.104" y2="362.786" xlink:href="#B"><stop offset="0" stop-color="#53524f"></stop><stop offset="1" stop-color="#6b6a68"></stop></linearGradient></defs><path fill="url(#C)" d="M654 345l9 4-2 1h-1c-1 3-5 6-7 9-3-1-6 1-9 1v1h-8l-1-1c1-3 3-7 4-10l1 5h1l2-2h1c0-2 2-3 2-5 1 1 1 2 3 3h2c1-1 1-1 2-1l1 1c1-2 1-4 0-6z"></path><path d="M644 353c0-2 2-3 2-5 1 1 1 2 3 3h2c1-1 1-1 2-1l1 1c-2 1-3 4-5 4s-4-1-5-2h0z" class="J"></path><path d="M735 134h7l19-1 1 2h0c2 0 3 1 4 1 4 2 7 3 11 6 1 1 3 2 3 4v1l-10-6c1 2 2 2 3 3-2 1-4 0-6 0h-2l-15-7c-4-1-8-1-13-1l-3-1 1-1z" class="W"></path><path d="M735 134h7l19-1 1 2h0c2 0 3 1 4 1 4 2 7 3 11 6 1 1 3 2 3 4v1l-10-6c-2-2-8-4-11-4-2-1-4-1-6-2-4-1-12-1-16 1l-3-1 1-1z" class="B"></path><path d="M681 355c-1-2-3-4-4-5l-4-3-2-1-1-1c-2-2-3-2-5-3h2c1 1 3 1 5 1 4 2 7 4 11 7l3 3h1c3 5 6 10 9 14-1 2-1 2 0 3v4c1 1 2 3 2 5 0 3 1 5 1 7 0 1-1 2-1 3l-2-6-3-9c-1-3-2-6-4-8l-4-7-1-1c0-1 0 0-1-1l-2-2z" class="I"></path><path d="M685 359c4 3 8 10 11 15 1 1 2 3 2 5 0 3 1 5 1 7 0 1-1 2-1 3l-2-6-3-9c-1-3-2-6-4-8l-4-7z" class="L"></path><path d="M663 349c2 2 5 4 7 6v2c2 2 1 7 1 9-5-1-9-2-13-2-2 0-3 0-5 1l-3-1c-1 0-1 0-1-1 1-2 2-3 4-4 2-3 6-6 7-9h1l2-1z" class="G"></path><path d="M689 372l3 2h1l3 9 2 6c3 10 2 23 1 34 1 5-1 8-2 13 0 2 0 6-2 7v1l1-22c1 2 1 4 1 6v-13c-2-9-1-19-5-28h2v-1-1c0-1 0-1-1-2h0v-1c-1-3-2-4-3-7 0-1 0-2-1-3z" class="M"></path><path d="M689 372l3 2h1l3 9 2 6c3 10 2 23 1 34h0c0-6 0-12-1-18s-2-12-4-18v-1-1c0-1 0-1-1-2h0v-1c-1-3-2-4-3-7 0-1 0-2-1-3z" class="N"></path><path d="M689 372l3 2h1l3 9c-1 0-1-1-2-2-1-2-2-4-4-6 0-1 0-2-1-3z" class="H"></path><defs><linearGradient id="D" x1="758.69" y1="138.738" x2="761.392" y2="148.779" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#545453"></stop></linearGradient></defs><path fill="url(#D)" d="M754 143h-1c0-1-1-2-2-3h-1 0c-1 0-1 0-2-1h-1-2c-2-1-4 0-6-1 1-1 2-1 3-1 1 1 2 1 3 1h1c1 0 2 0 3-1h1l15 7h2c2 0 4 1 6 0-1-1-2-1-3-3l10 6 4 3-2 1v2h-1c0 1 0 1 1 2v1l-3-2-1 1 4 4c-1 0-2 0-3-1 0-1 0-1-1-1-3-2-5-5-7-7-1-1-2-1-3-2h0-5 0c-3 1-5 2-7 3l-2-2-1-1 1-1c1-1 1-2 0-3v-1z"></path><path d="M770 141l10 6 4 3-2 1v2h-1c0 1 0 1 1 2v1l-3-2-1 1-7-7-6-4h2c2 0 4 1 6 0-1-1-2-1-3-3z" class="C"></path><path d="M773 144l3 2c1 1 2 1 3 3l1 1-1 1c-3-1-4-3-7-3h-1l-6-4h2c2 0 4 1 6 0z" class="G"></path><path d="M773 144l3 2c0 1 0 1-1 2-3-1-5-2-8-4 2 0 4 1 6 0z" class="O"></path><path d="M635 360l1 1h8v-1c3 0 6-2 9-1-2 1-3 2-4 4 0 1 0 1 1 1l3 1h-2c2 1 4 1 6 2v2c1 1 3 2 4 3l-2-1-1 1c3 2 6 3 8 5-1 0-1 1-2 0h-1c-1 0-1 0-2 1-2-1-6-3-6-5l-1-1-4-1c-3-1-7-1-9-1-1 2-2 2-3 3l-2 1c-2 1-5 2-7 3v1h-2l4-9 1-2 3-6v-1z" class="M"></path><path d="M641 370c-3 1-6 1-9 2h0c1-1 2-1 4-2h0c7-3 16-1 22 2h0c3 2 6 3 8 5-1 0-1 1-2 0h-1c-1 0-1 0-2 1-2-1-6-3-6-5l-1-1-4-1c-3-1-7-1-9-1z" class="K"></path><path d="M636 366h3c3-2 9-1 12-1 2 1 4 1 6 2v2c1 1 3 2 4 3l-2-1c-7-3-15-5-22-3-2 1-4 1-6 1l1-2c1 0 3-1 4-1z" class="C"></path><path d="M635 360l1 1h8v-1c3 0 6-2 9-1-2 1-3 2-4 4 0 1 0 1 1 1l3 1h-2c-3 0-9-1-12 1h-3c-1 0-3 1-4 1l3-6v-1z" class="L"></path><path d="M632 367l3-6c2 0 6 0 8 2-1 0-2 0-3 1h-1c-2 0-3-1-5 1 1 1 1 1 2 1-1 0-3 1-4 1z" class="X"></path><defs><linearGradient id="E" x1="331.86" y1="429.066" x2="351.461" y2="418.53" xlink:href="#B"><stop offset="0" stop-color="#141417"></stop><stop offset="1" stop-color="#3d3b37"></stop></linearGradient></defs><path fill="url(#E)" d="M342 396c2 4 1 10 3 15l1-6v6 1l1-2h0v4c-1 2-1 5-1 7l-2 8c0 8 0 17 1 25v2l1 3c1 1 1 1 1 2v1l-1 1c-2-4-4-10-6-15l1-2c-1-1 0-2-1-4 0-1 0-2-1-2v-13c0-10 1-20 3-31z"></path><path d="M345 411l1-6v6 1l1-2h0v4c-1 2-1 5-1 7l-2 8c0 8 0 17 1 25v2l-1-1c-1-5-1-10-1-15 0-10 0-20 2-29z" class="V"></path><defs><linearGradient id="F" x1="721.794" y1="134.377" x2="739.68" y2="154.147" xlink:href="#B"><stop offset="0" stop-color="#080707"></stop><stop offset="1" stop-color="#353635"></stop></linearGradient></defs><path fill="url(#F)" d="M708 146c6-5 18-10 26-11l3 1c5 0 9 0 13 1h-1c-1 1-2 1-3 1h-1c-1 0-2 0-3-1-1 0-2 0-3 1 2 1 4 0 6 1h2 1c1 1 1 1 2 1h0 1c1 1 2 2 2 3h1v1c1 1 1 2 0 3l-1 1c-1 1-1 2-2 3l-3-2-4-2-7-2c-9-2-17-1-25 2l-1-1h0c-2 1-2 0-3 0z"></path><path d="M754 143v1c-2 0-3 1-5 2-1 1-1 2-1 3l-4-2c1-1 2-2 3-2h1c1 0 2-1 3-1h0c1-1 2-1 3-1z" class="B"></path><path d="M748 149c0-1 0-2 1-3 2-1 3-2 5-2 1 1 1 2 0 3l-1 1c-1 1-1 2-2 3l-3-2z" class="P"></path><defs><linearGradient id="G" x1="697.277" y1="419.301" x2="684.474" y2="417.73" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#G)" d="M687 396c1-3 0-6-1-8 1-2 1-2 2-3 5 12 8 25 8 37l-1 22c-1 0-1 0-1 1h0c1 1 1 2 1 2l-3 6-1-1c-1-2 0-7 0-10 0-9 0-18-1-26s-4-16-8-23h1l1-4 1 1c1 2 1 4 2 6z"></path><path d="M684 389l1 1c1 2 1 4 2 6 4 13 5 26 5 41 0 5 1 10-1 15-1-2 0-7 0-10 0-9 0-18-1-26s-4-16-8-23h1l1-4z" class="H"></path><path d="M358 392l2-3h1v3c0 3-3 7-4 10l-1 2c-1 3-2 6-2 8l-2 9v4c-1 0-1 0-1 1l-2 9c-1 5-1 9-2 14 0 2-1 4 0 6-1 1-1 2-1 4l-1-3v-2c-1-8-1-17-1-25l2-8c0-2 0-5 1-7v-4h0l-1 2v-1-6c0-1 1-3 2-5 1-1 2-3 2-4v1 2l2-5c1-1 2-3 3-4v4l-1 2 1 2 3-6z" class="Z"></path><path d="M353 402l1 1c0 2 0 3-1 4-1 2-1 5-2 7-1 4-1 9-2 13l-2-3v1c-1 1-1 3-3 4l2-8c0-2 0-5 1-7 1 1 1 1 2 3 0-3 1-5 2-7l2-8z" class="F"></path><path d="M347 414c1 1 1 1 2 3l-2 5c0 1 0 1-1 2v-3c0-2 0-5 1-7z" class="Q"></path><defs><linearGradient id="H" x1="351.823" y1="442.79" x2="340.677" y2="436.21" xlink:href="#B"><stop offset="0" stop-color="#13150d"></stop><stop offset="1" stop-color="#28272c"></stop></linearGradient></defs><path fill="url(#H)" d="M344 429c2-1 2-3 3-4v-1l2 3c-2 7-2 14-2 22 0 2-1 4 0 6-1 1-1 2-1 4l-1-3v-2c-1-8-1-17-1-25z"></path><defs><linearGradient id="I" x1="351.003" y1="410.474" x2="347.242" y2="405.828" xlink:href="#B"><stop offset="0" stop-color="#595958"></stop><stop offset="1" stop-color="#706e6d"></stop></linearGradient></defs><path fill="url(#I)" d="M355 390v4l-1 2 1 2-2 4-2 8c-1 2-2 4-2 7-1-2-1-2-2-3v-4h0l-1 2v-1-6c0-1 1-3 2-5 1-1 2-3 2-4v1 2l2-5c1-1 2-3 3-4z"></path><path d="M355 390v4l-1 2 1 2-2 4h-1c0-3 1-5 0-8 1-1 2-3 3-4z" class="X"></path><path d="M348 400c1-1 2-3 2-4v1 2l-3 11h0l-1 2v-1-6c0-1 1-3 2-5z" class="K"></path><defs><linearGradient id="J" x1="674.601" y1="386.736" x2="667.673" y2="362.262" xlink:href="#B"><stop offset="0" stop-color="#6b6b6a"></stop><stop offset="1" stop-color="#898785"></stop></linearGradient></defs><path fill="url(#J)" d="M653 365c2-1 3-1 5-1 4 0 8 1 13 2l2 1c6 1 9 4 11 9l2 3c0 2 2 4 2 6-1 1-1 1-2 3 1 2 2 5 1 8-1-2-1-4-2-6l-1-1-1 4h-1c-4-7-10-16-17-19-2-1-2-1-3-2h-1c-1-1-3-2-4-3v-2c-2-1-4-1-6-2h2z"></path><path d="M686 379c0 2 2 4 2 6-1 1-1 1-2 3 1 2 2 5 1 8-1-2-1-4-2-6 0-3-2-5-2-8 0-2 2-2 3-3z" class="Q"></path><path d="M657 367c11 3 20 9 25 19 1 1 2 2 2 3l-1 4h-1c-4-7-10-16-17-19-2-1-2-1-3-2h-1c-1-1-3-2-4-3v-2z" class="I"></path><defs><linearGradient id="K" x1="342.79" y1="461.482" x2="360.21" y2="450.518" xlink:href="#B"><stop offset="0" stop-color="#040301"></stop><stop offset="1" stop-color="#272726"></stop></linearGradient></defs><path fill="url(#K)" d="M356 404l2-1c0 2 0 4-1 5v3h1l2-4c0 2-1 4-2 6-1 3-2 6-2 9-2 5-3 12-3 18h1v-2h1c1 2 0 3 1 5l-1 11-1 6c0 7 0 14 1 21 0 1 1 3 1 4-4-7-7-14-10-22l1-1v-1c0-1 0-1-1-2 0-2 0-3 1-4-1-2 0-4 0-6 1-5 1-9 2-14l2-9c0-1 0-1 1-1v-4l2-9c0-2 1-5 2-8z"></path><path d="M356 404l2-1c0 2 0 4-1 5v3h1l2-4c0 2-1 4-2 6-1 3-2 6-2 9-2 5-3 12-3 18h1v-2h1c1 2 0 3 1 5l-1 11-1 6c-1-3-1-5-1-7l-1-1v6 5 3-1c-1-4-1-8-1-11 0-10-1-20 1-29v-4l2-9c0-2 1-5 2-8z" class="N"></path><path d="M360 407c0 2-1 4-2 6-1 3-2 6-2 9-2 5-3 12-3 18h1v-2h1c1 2 0 3 1 5l-1 11-1 6c-1-3-1-5-1-7l-1-1v6-14c0-12 1-22 6-33l2-4z" class="F"></path><path d="M641 370c2 0 6 0 9 1l4 1 1 1c0 2 4 4 6 5s5 3 7 5v1c-1 0-1 1-1 2 1 1 2 2 3 4-1 1 0 1-1 1-1-1-3-2-4-2h-1c-8-6-17-6-27-6-5 0-10 1-14 3l2-3c1-2 3-2 5-2l-1-1h-2v-1-1h2v-1c2-1 5-2 7-3l2-1c1-1 2-1 3-3z" class="H"></path><defs><linearGradient id="L" x1="631.932" y1="374.914" x2="652.746" y2="379.226" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#a2a19e"></stop></linearGradient></defs><path fill="url(#L)" d="M627 378h2v-1c2-1 5-2 7-3l1 2c2 0 3-1 5 0h5c2 0 3 1 6 1l1 2v1c-7 0-14-2-21-1l-4 1h-2v-1-1z"></path><defs><linearGradient id="M" x1="625.427" y1="382.31" x2="648.931" y2="380.873" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#3b3b38"></stop></linearGradient></defs><path fill="url(#M)" d="M630 381c9-2 19-1 28 3 2 2 5 3 7 5h-1c-8-6-17-6-27-6-5 0-10 1-14 3l2-3c1-2 3-2 5-2z"></path><defs><linearGradient id="N" x1="647.17" y1="384.609" x2="657.337" y2="374.38" xlink:href="#B"><stop offset="0" stop-color="#807f7c"></stop><stop offset="1" stop-color="#b9b8b4"></stop></linearGradient></defs><path fill="url(#N)" d="M641 370c2 0 6 0 9 1l4 1 1 1c0 2 4 4 6 5s5 3 7 5v1c-1 0-1 1-1 2-2-1-4-3-6-4s-5-2-7-2v-1l-1-2c-3 0-4-1-6-1h-5c-2-1-3 0-5 0l-1-2 2-1c1-1 2-1 3-3z"></path><path d="M388 344c3-1 6 0 10 1 2 3 4 7 6 11l3 8-8-1h-2c-2-2-3-1-4-2-2 0-6 0-8 1h-2l-1 1h-1-2 0c-4 2-8 2-12 2l-2-2c0-1-1-2 0-4 1-4 7-8 10-10 4-2 8-4 13-5z" class="Q"></path><path d="M391 347v-1c1-1 1 0 2 0 1 1 2 2 2 4 0 1-1 2-2 3l-2 1h0c-2 0-4 0-6-1s-2-1-2-3c1-1 2-2 4-3-1 1-1 1-1 2l1 1 2-1 1 1c1-1 1-1 1-3z" class="P"></path><defs><linearGradient id="O" x1="391.789" y1="349.686" x2="402.728" y2="353.294" xlink:href="#B"><stop offset="0" stop-color="#474745"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#O)" d="M388 344c3-1 6 0 10 1 2 3 4 7 6 11-4 2-8 2-12 2v-1c-1-1-1-1-1-2l-1 1-1-1h2v-1h0l2-1c1-1 2-2 2-3 0-2-1-3-2-4-1 0-1-1-2 0v1l-1-1v-1c-1 0-1 0-2-1h0z"></path><defs><linearGradient id="P" x1="394.797" y1="370.882" x2="374.396" y2="349.783" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#aaa9a5"></stop></linearGradient></defs><path fill="url(#P)" d="M367 365l-2-2c0-1-1-2 0-4 1-4 7-8 10-10 0 2 0 4 2 5 1 1 2 1 3 2h0c4 2 8 3 12 2 4 0 8 0 12-2l3 8-8-1h-2c-2-2-3-1-4-2-2 0-6 0-8 1h-2l-1 1h-1-2 0c-4 2-8 2-12 2z"></path><defs><linearGradient id="Q" x1="365.773" y1="391.859" x2="356.573" y2="367.029" xlink:href="#B"><stop offset="0" stop-color="#5b5959"></stop><stop offset="1" stop-color="#898987"></stop></linearGradient></defs><path fill="url(#Q)" d="M367 365c4 0 8 0 12-2h0 2 1l1-1h2c2-1 6-1 8-1 1 1 2 0 4 2h2l-4 1v1h-8c-1 0-3 1-4 1h2l-1 1c-3 2-6 2-9 5-2 1-4 3-6 4s-3 2-5 3c-3 3-8 7-9 11-1 1-2 3-3 4l-2 5v-2-1c0 1-1 3-2 4-1 2-2 4-2 5l-1 6c-2-5-1-11-3-15 1-9 5-19 11-26 1-1 3-4 5-4 2-1 7-1 9-1z"></path><path d="M397 363h2l-4 1v1h-8v-1c3-1 6-1 10-1z" class="Y"></path><path d="M383 366h2l-1 1c-3 2-6 2-9 5-2 1-4 3-6 4s-3 2-5 3c-3 3-8 7-9 11-1 1-2 3-3 4l-2 5v-2-1c0 1-1 3-2 4 1-5 3-10 6-14 7-12 16-17 29-20z" class="T"></path><path d="M399 363l8 1c1 2 2 4 2 5l5 11c-1 0-3-1-4-1-6-2-12-2-18 0l-4 1c-3 1-6 1-9 3-1 1-3 2-4 4l-3 3c-2 0-2 0-3 1l-4 1c-3 4-5 7-7 11l-2 1 1-2c1-3 4-7 4-10v-3h-1l-2 3-3 6-1-2 1-2v-4c1-4 6-8 9-11 2-1 3-2 5-3s4-3 6-4c3-3 6-3 9-5l1-1h-2c1 0 3-1 4-1h8v-1l4-1z" class="O"></path><path d="M391 369h3c3-1 6 0 9 0 2 0 4 1 6 2h0-3c-5-1-10-1-15-1-3 1-5 2-8 3-5 1-8 5-13 7-3 1-6 4-8 7l-1-1c6-6 12-11 20-14 2-1 4-2 6-2 1-1 3-1 4-1z" class="I"></path><path d="M399 363l8 1c1 2 2 4 2 5v2c-2-1-4-2-6-2-3 0-6-1-9 0h-3v-1l-5 1c-3 0-5 1-7 2s-2 1-4 1c3-3 6-3 9-5l1-1h-2c1 0 3-1 4-1h8v-1l4-1z" class="D"></path><path d="M399 363l8 1c1 2 2 4 2 5v2c-2-1-4-2-6-2l-3-1h0 7c-3-2-8-2-12-3v-1l4-1z" class="F"></path><defs><linearGradient id="R" x1="401.85" y1="370.623" x2="388.978" y2="380.168" xlink:href="#B"><stop offset="0" stop-color="#63635f"></stop><stop offset="1" stop-color="#989694"></stop></linearGradient></defs><path fill="url(#R)" d="M409 369l5 11c-1 0-3-1-4-1-6-2-12-2-18 0l-4 1c-2-1-3-1-5 0l-1-2h2c1-1 2-2 2-3v-1h-3v-1c3-1 5-2 8-3 5 0 10 0 15 1h3 0v-2z"></path><path d="M383 373l8-3c0 2 0 5-2 6-1 1-4 1-5 2 1-1 2-2 2-3v-1h-3v-1z" class="S"></path><path d="M361 386l1 1c2-3 5-6 8-7 5-2 8-6 13-7v1h3v1c0 1-1 2-2 3h-2l1 2c2-1 3-1 5 0-3 1-6 1-9 3-1 1-3 2-4 4l-3 3c-2 0-2 0-3 1l-4 1c-3 4-5 7-7 11l-2 1 1-2c1-3 4-7 4-10v-3h-1l-2 3h-1c0-2 3-5 4-6z" class="C"></path><path d="M365 392c4-4 8-8 14-9-1 1-3 2-4 4l-3 3c-2 0-2 0-3 1l-4 1zm1-6c1-2 2-3 4-4 2-2 10-7 14-7v1l-2 2c-2 1-5 2-7 4-3 2-6 6-10 8h0l1-4z" class="N"></path><path d="M231 164c3 2 8 8 12 9l2-3c2 3 1 6 3 10v1c-6 12-8 25-8 39l-1-1c0 1 0 1-1 2 0-1-1-1-1-2l-15-35v-2c1-6 3-9 5-14l4-4z"></path><path d="M245 170c2 3 1 6 3 10v1c-6 12-8 25-8 39l-1-1c0 1 0 1-1 2 0-1-1-1-1-2v-2c-1-5-1-9 0-14 0-10 2-20 6-30l2-3z" class="C"></path><defs><linearGradient id="S" x1="355.236" y1="323.459" x2="355.826" y2="342.43" xlink:href="#B"><stop offset="0" stop-color="#383837"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#S)" d="M362 322c7-2 17-4 24-2l3 10c1 1 2 1 2 2v1c1 1 1 2 1 4l-10 1h0l-8 2c-9 4-19 8-25 15-1 1-3 3-4 5h0l-2 3c0-1 0-3-1-4h0l-3 3c0-3 1-6 2-10 0 0 1-4 1-5l-2 2c-2 0-2-1-3-3l2-3h0-1-5c4-5 9-9 13-13 4-3 8-5 12-7 1 0 3-1 4-1z"></path><defs><linearGradient id="T" x1="388.728" y1="326.702" x2="354.537" y2="327.978" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#T)" d="M362 322c7-2 17-4 24-2l3 10c1 1 2 1 2 2v1c1 1 1 2 1 4l-10 1c-4-3-7-2-10-4 1-1 1-1 2-1-2-1-3-2-4-2-1-1-2-2-2-3s1-2 2-2h1c2-1 4-2 6-2 2-1 5-1 6-2h0c-5 1-10 0-15 1h0c-2 1-4 1-6 2-2 0-3 1-5 2v1c-2 0-1 0-2-1v-1l3-3c1 0 3-1 4-1z"></path><path d="M382 338c-4-3-7-2-10-4 1-1 1-1 2-1-2-1-3-2-4-2-1-1-2-2-2-3s1-2 2-2h1c-1 1-2 1-2 2 3 4 15 8 20 7h1v-1h0c-1-1-1-1-1-2h0v-2c1 1 2 1 2 2v1c1 1 1 2 1 4l-10 1z" class="F"></path><defs><linearGradient id="U" x1="347.126" y1="339.723" x2="347.958" y2="359.815" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#a1a19c"></stop></linearGradient></defs><path fill="url(#U)" d="M349 336l1-1h0c0 1 1 2 1 2l1 1h2c3 1 5 2 7 2 4 1 9-1 13 0-9 4-19 8-25 15-1 1-3 3-4 5h0l-2 3c0-1 0-3-1-4h0l-3 3c0-3 1-6 2-10 0 0 1-4 1-5l-2 2c-2 0-2-1-3-3l2-3c3-3 6-5 10-7z"></path><path d="M349 336c1 1 1 1 1 3-3 2-5 5-8 8l-2 2c-2 0-2-1-3-3l2-3c3-3 6-5 10-7z" class="I"></path><path d="M309 134h14 49 20c3 0 7-1 10 0v1c-3 3-7 5-11 8-10 8-19 19-25 31-4-7-9-13-15-18h-2l-4-2c-8-8-20-14-30-17-3-1-6-2-9-2l3-1z" class="B"></path><path d="M306 135l3-1c16 3 31 10 42 22h-2l-4-2c-8-8-20-14-30-17-3-1-6-2-9-2z" class="I"></path><path d="M369 139c1 0 1 0 2 1 1 0 2 0 2 1 2 0 3 3 5 4v-2h0c-2-1-2-2-3-3h0c2 1 3 2 4 4l-1 2v1h-1c-1 1-1 2-2 3l-4 2v1l1 1s-2 0-3 1l-1-1c-1 0-1 1-2 1v-1h-2c0-2-2-3-2-4-1-1-1-2-2-3-1 0-1 0-1-1v-1-1l1-2c1 0 2-1 3-2h0 1c1 0 1 0 2 1h0c1-2 1-2 3-2z" class="J"></path><path d="M732 134c2-1 2-1 3 0l-1 1c-8 1-20 6-26 11-12 6-22 16-29 26l-1-1s-1 1-1 2c-4-5-7-10-11-15-5-6-11-12-18-17l-10-7c10-1 20 0 29 0h65z" class="P"></path><path d="M679 138h4c1-1 2 0 3 0 1 2 2 3 4 5h-1l-2-1v2h1 0c0 1 1 1 2 1 0 2-1 2-1 4v1l-1 2c-1 0-1 0-1-1-1-1-3-3-4-3v-3l-1-1-1-1c-2-2-2-2-2-5z" class="B"></path><path d="M732 134c2-1 2-1 3 0l-1 1c-8 1-20 6-26 11-12 6-22 16-29 26l-1-1v-1c1-2 4-5 5-7 10-12 23-20 37-26 4-1 8-1 12-3h0z" class="I"></path><path d="M683 145l-2 1 1 1c1 2 2 3 4 4l-1 1c-2-1-2-2-4-3l1 2h0-1l-1 1h2c0 1 1 1 1 2h-1c-1 0-1-1-2-1-1 1-1 0-1 1s0 1-1 2v1l-1-1v-2h-1v3c0 1-1 1-1 2l-1-1-2 1-1-1c1 0 1-1 2-1h2v-2l-1-1v2h-1v-1l-2 2c-1-1-2-1-3-2l1-1c-1-1-1-1-2-3-1 1 0 1-1 1h-1v-3l-1 1-4-2c1-2 1-3 2-4l-1-2 1-1c1 0 1 0 2 1 1-1 2-1 3-2s1-2 2-2h0 3c1-1 5 1 6 0h1c0 3 0 3 2 5l1 1 1 1z" class="J"></path><path d="M392 379c6-2 12-2 18 0 1 0 3 1 4 1 0 1 1 2 1 2l3 6v3l3 6 2 4-2 1c-2 0-3-1-4-2h0 0l-4-1c-7 0-16 1-23 5-5 3-10 6-15 7l-3 3-6 12-2 4-2 6c-1 1-2 1-3 1l-3 18-1-1 1-11c-1-2 0-3-1-5h-1v2h-1c0-6 1-13 3-18 0-3 1-6 2-9 1-2 2-4 2-6l-2 4h-1v-3c1-1 1-3 1-5 2-4 4-7 7-11l4-1c1-1 1-1 3-1l3-3c1-2 3-3 4-4 3-2 6-2 9-3l4-1z" class="C"></path><path d="M366 402s0-1 1-2c3-6 10-6 16-8 1 1 1 1 2 1h0v2l-2 1c0-1 0-1 1-2h-1c-2 1-3 1-5 2v-1l1-1c-2 0-3 1-4 2l-1 1h-1c-2 1-5 4-7 5z" class="D"></path><path d="M380 388c7-3 18-5 25-4 3 1 6 2 8 2h1c1 1 2 1 2 2-3 0-6-1-10-1s-8 1-12 1c-2 1-5 2-7 1 1-1 4-1 5-2h2 1v-1l-2 1c-3 0-5 0-8 1-2 0-3 1-5 0z" class="L"></path><path d="M397 393c-2-1-3-1-5-1h0 0l1-1c8-4 17-3 25 0l3 6h-1c-6-4-16-4-23-4z"></path><path d="M366 402c2-1 5-4 7-5h1l1-1c1-1 2-2 4-2l-1 1v1c2-1 3-1 5-2h1c-1 1-1 1-1 2-1 1-2 1-2 2h0c-1 1-3 1-4 2-3 2-5 5-7 7-1 1-3 5-4 5-3 3-5 7-7 11-1 1-1 1-2 3l-3 12v2h-1c0-6 1-13 3-18 1-1 1-1 1-2 2-6 5-13 9-18z" class="N"></path><defs><linearGradient id="V" x1="409.635" y1="378.063" x2="396.556" y2="388.78" xlink:href="#B"><stop offset="0" stop-color="#030100"></stop><stop offset="1" stop-color="#353536"></stop></linearGradient></defs><path fill="url(#V)" d="M392 379c6-2 12-2 18 0 1 0 3 1 4 1 0 1 1 2 1 2l3 6h-2c0-1-1-1-2-2h-1c-2 0-5-1-8-2-7-1-18 1-25 4l-3 2c-5 2-9 7-13 11l-4 4v2l-2 4h-1v-3c1-1 1-3 1-5 2-4 4-7 7-11l4-1c1-1 1-1 3-1l3-3c1-2 3-3 4-4 3-2 6-2 9-3l4-1z"></path><path d="M392 379l1 2c-6 1-12 3-18 6 1-2 3-3 4-4 3-2 6-2 9-3l4-1z" class="H"></path><path d="M392 379c6-2 12-2 18 0 1 0 3 1 4 1 0 1 1 2 1 2-7-2-15-3-22-1l-1-2z" class="D"></path><path d="M372 390l-2 1c-3 2-7 5-8 10l1-2c3-4 11-10 16-12l5-2c9-3 21-3 30 1h-1c-2 0-5-1-8-2-7-1-18 1-25 4l-3 2c-5 2-9 7-13 11l-4 4v2l-2 4h-1v-3c1-1 1-3 1-5 2-4 4-7 7-11l4-1c1-1 1-1 3-1zm25 3c7 0 17 0 23 4h1l2 4-2 1c-2 0-3-1-4-2h0 0l-4-1c-7 0-16 1-23 5-5 3-10 6-15 7l-3 3-6 12-2 4-2 6c-1 1-2 1-3 1l-3 18-1-1 1-11c-1-2 0-3-1-5h-1l3-12c1-2 1-2 2-3 2-4 4-8 7-11 1 0 3-4 4-5 2-2 4-5 7-7 1-1 3-1 4-2h0c3-1 6-3 9-3l3-1c1 0 2-1 4-1z" class="I"></path><path d="M364 421c0 3-1 5 0 9l-2 6c-1 1-2 1-3 1 1-6 3-11 5-16z" class="Y"></path><path d="M367 415l5-1-6 12-2 4c-1-4 0-6 0-9 0-2 2-4 3-6z" class="R"></path><path d="M354 438l3-12c1-2 1-2 2-3 2-4 4-8 7-11-6 10-8 20-10 31-1-2 0-3-1-5h-1zm34-40c7-3 17-4 24-2 2 0 6 2 8 1h1l2 4-2 1c-2 0-3-1-4-2h0 0l-4-1c-2-1-4 0-5-1-4 0-7 0-10 1-3-2-4-2-7-1h-3z" class="Q"></path><path d="M388 398h3c3-1 4-1 7 1 3-1 6-1 10-1 1 1 3 0 5 1-7 0-16 1-23 5-5 3-10 6-15 7l-3 3-5 1c4-8 12-14 21-17z" class="M"></path><path d="M398 399c3-1 6-1 10-1 1 1 3 0 5 1-7 0-16 1-23 5-5 3-10 6-15 7 0-1 1-3 3-5 3-2 8-3 12-4 2-1 5-3 8-3z" class="X"></path><path d="M782 153v-2l2-1c3 4 7 8 10 13 1 1 3 8 4 8h0 2c2-1 7-6 8-8 0-1-1-3-2-4 2 2 3 4 5 6 2 3 3 6 4 10 1 1 1 4 2 4v3c-1 4-4 8-6 11l-9 20c-1 2-1 4-3 5h0c-2 7-4 13-7 18-2 5-4 10-7 14 0 1 0 1-1 2v-1-1h0v1h-1c-1-4 0-10 0-14 0-6-1-12-1-18l1-1c0-1 0-2-1-3v-3c-1-1-1-4-1-5l-3-12 1-1c2 3 4 5 5 8h1c-1-6-3-12-5-17 1-1 1 1 3 1 2-2 8-7 9-9-1-1-1-2-1-3v-1c-3-5-6-9-9-14l-4-4 1-1 3 2v-1c-1-1-1-1-1-2h1z"></path><path d="M795 182l1-1v-2c1-1 0-2 0-3-1-1 0-1 0-3l2 4c0 1 1 2 2 3 2 8 3 18 3 27 0 2-1 4-1 6-1 2-1 4-3 5h0c2-12 0-25-4-36z" class="D"></path><path d="M782 153v-2l2-1c3 4 7 8 10 13 1 1 3 8 4 8l2 9c-1-1-2-2-2-3l-2-4c0 2-1 2 0 3 0 1 1 2 0 3v2l-1 1c-1-3-3-6-4-9-3-5-6-9-9-14l-4-4 1-1 3 2v-1c-1-1-1-1-1-2h1z" class="H"></path><defs><linearGradient id="W" x1="786.198" y1="163.142" x2="792.99" y2="160.793" xlink:href="#B"><stop offset="0" stop-color="#c2c1bd"></stop><stop offset="1" stop-color="#e9e9dc"></stop></linearGradient></defs><path fill="url(#W)" d="M782 153v-2l2-1c3 4 7 8 10 13 1 1 3 8 4 8l2 9c-1-1-2-2-2-3l-2-4c-1-2-2-4-4-6-1-2-3-5-4-8h0l-1 1-1-2c-1-2-3-4-4-5z"></path><path d="M778 195l1-1c2 3 4 5 5 8h1c2 9 2 18 2 27 0 6 1 13-2 19v2c0 1 0 1-1 2v-1-1h0v1h-1c-1-4 0-10 0-14 0-6-1-12-1-18l1-1c0-1 0-2-1-3v-3c-1-1-1-4-1-5l-3-12z" class="W"></path><path d="M806 159c2 2 3 4 5 6 2 3 3 6 4 10 1 1 1 4 2 4v3c-1 4-4 8-6 11l-9 20c0-2 1-4 1-6 0-9-1-19-3-27l-2-9h0 2c2-1 7-6 8-8 0-1-1-3-2-4z"></path><path d="M827 133l63-1c-6 3-12 3-18 5-16 5-31 12-42 26-4 5-8 10-11 16l-1 1-1-1h0c-1 0-1-3-2-4-1-4-2-7-4-10-2-2-3-4-5-6 1 1 2 3 2 4-1 2-6 7-8 8h-2 0c-1 0-3-7-4-8-3-5-7-9-10-13l-4-3v-1c0-2-2-3-3-4 0-2 0-3 1-4l3-1c-1 0-2-1-3-2l1-1h0c11-1 22 0 33 0 5 0 10 0 15-1z" class="P"></path><path d="M834 145v2c-3 3-6 5-9 7-2 2-4 4-7 5 0-1 1-1 1-2s3-3 4-4c3-1 5-4 7-6l4-2z" class="E"></path><path d="M811 165c1-1 1-4 1-5l1-1 1 1c2 6 4 12 5 18v1l-1 1-1-1h0c-1 0-1-3-2-4-1-4-2-7-4-10z" class="G"></path><path d="M789 137c1-1 2-1 4-1h0 1 6 21c4 0 11-1 14 0v5c1 2 2 3 2 4-1 0-2-1-3-1l-1-1c-2-1-3-1-4-2v-3h0l1-1h-35l-2 1-4-1z" class="E"></path><path d="M793 138l2-1h35l-1 1h0v3c1 1 2 1 4 2l1 1v1l-4 2 2-2h1l-1-1h-3c-3-1-5-1-8-1-2 0-3-1-5-1-2 1-3 2-5 2-3 0-5 0-8 1-2-2-4-3-6-5-1-1-3-1-4-2z" class="F"></path><defs><linearGradient id="X" x1="828.705" y1="138.536" x2="809.563" y2="151.832" xlink:href="#B"><stop offset="0" stop-color="#424241"></stop><stop offset="1" stop-color="#686867"></stop></linearGradient></defs><path fill="url(#X)" d="M803 145c3-1 5-1 8-1 2 0 3-1 5-2 2 0 3 1 5 1 3 0 5 0 8 1h3l1 1h-1l-2 2c-2 2-4 5-7 6-1 1-4 3-4 4l-5 3-1-1c-2-5-6-10-10-14z"></path><defs><linearGradient id="Y" x1="781.027" y1="135.862" x2="802.959" y2="166.91" xlink:href="#B"><stop offset="0" stop-color="#4a4949"></stop><stop offset="1" stop-color="#7b7a79"></stop></linearGradient></defs><path fill="url(#Y)" d="M779 134l10 3 4 1c1 1 3 1 4 2 2 2 4 3 6 5 4 4 8 9 10 14l-1 1c0 1 0 4-1 5-2-2-3-4-5-6 1 1 2 3 2 4-1 2-6 7-8 8h-2 0c-1 0-3-7-4-8-3-5-7-9-10-13l-4-3v-1c0-2-2-3-3-4 0-2 0-3 1-4l3-1c-1 0-2-1-3-2l1-1z"></path><path d="M779 134l10 3 4 1c1 1 3 1 4 2l-1 1 1 2-1 1h-3l-12-7c-1 0-2-1-3-2l1-1z" class="D"></path><path d="M793 144h3l1-1-1-2 1-1c2 2 4 3 6 5 4 4 8 9 10 14l-1 1c0 1 0 4-1 5-2-2-3-4-5-6 0-2-11-13-13-15z" class="K"></path><defs><linearGradient id="Z" x1="759.519" y1="170.837" x2="766.974" y2="165.081" xlink:href="#B"><stop offset="0" stop-color="#a8a8a5"></stop><stop offset="1" stop-color="#dedfd6"></stop></linearGradient></defs><path fill="url(#Z)" d="M712 147c8-3 16-4 25-2l7 2 4 2 3 2c1-1 1-2 2-3l1 1 2 2c2-1 4-2 7-3h0 5 0c1 1 2 1 3 2 2 2 4 5 7 7 1 0 1 0 1 1 1 1 2 1 3 1 3 5 6 9 9 14v1c0 1 0 2 1 3-1 2-7 7-9 9-2 0-2-2-3-1 2 5 4 11 5 17h-1c-1-3-3-5-5-8l-1 1 3 12c0 1 0 4 1 5v3c1 1 1 2 1 3l-1 1c-2-10-4-19-9-29h0c-3-4-5-10-9-14h0l-3-4c-2-3-5-5-7-7 0-1-3-4-4-4-4-3-8-6-13-8-1 0-3 1-4 1-5 4-8 8-9 15v1c1 2 1 4 2 5l2 2v1l-3-1-2-1c-8-1-16-1-25-1-3 0-6 1-8 2l-5 1h-3l-1 3h-1c0-2-1-3-2-5 0-2 0-2 1-4 7-10 17-20 29-26 1 0 1 1 3 0h0l1 1z"></path><path d="M758 160c1 0 1 0 1 1 1 0 2 2 3 4-2-1-3-2-4-4v-1z" class="U"></path><path d="M748 153l1-1c1 0 1 0 1 1 1 1 2 1 2 2 2 1 3 2 4 4 0 1 2 3 1 3-3-2-6-6-9-9z" class="C"></path><path d="M779 185h1c2 5 4 11 5 17h-1c-1-3-3-5-5-8l-1 1c0-1-2-5-2-7h1 0v-2h0l2 1c1 2 0 4 2 5h0v-1l-1-3c-1-1-1-1-1-2v-1z" class="S"></path><path d="M731 147l12 4 3 1 1 1h1c3 3 6 7 9 9 0 2 1 2 2 3l1 1c-2 0-3-1-6-1 0-1-3-4-4-4-4-3-8-6-13-8-7-3-13-4-21-4l2-1c4 0 8 0 13 1h1l-1-2z" class="U"></path><defs><linearGradient id="a" x1="764.926" y1="183.77" x2="770.083" y2="179.368" xlink:href="#B"><stop offset="0" stop-color="#c3c1b8"></stop><stop offset="1" stop-color="#efeeed"></stop></linearGradient></defs><path fill="url(#a)" d="M754 165c3 0 4 1 6 1v1c2 2 5 4 6 6l10 15c0 2 2 6 2 7l3 12c0 1 0 4 1 5v3c1 1 1 2 1 3l-1 1c-2-10-4-19-9-29h0c-3-4-5-10-9-14h0l-3-4c-2-3-5-5-7-7z"></path><defs><linearGradient id="b" x1="754.614" y1="167.657" x2="788.186" y2="164.589" xlink:href="#B"><stop offset="0" stop-color="#535151"></stop><stop offset="1" stop-color="#8d8d8b"></stop></linearGradient></defs><path fill="url(#b)" d="M753 148l1 1 2 2c2-1 4-2 7-3h0 5 0c1 1 2 1 3 2 2 2 4 5 7 7 1 0 1 0 1 1 1 1 2 1 3 1 3 5 6 9 9 14v1c0 1 0 2 1 3-1 2-7 7-9 9-2 0-2-2-3-1h-1c-4-12-10-20-20-28-2-2-5-4-8-6 1-1 1-2 2-3z"></path><path d="M712 147c8-3 16-4 25-2l-1 1c-3 0-10-2-12 0h0c3 0 5 0 7 1l1 2h-1c-5-1-9-1-13-1l-2 1c8 0 14 1 21 4-1 0-3 1-4 1-5 4-8 8-9 15v1c1 2 1 4 2 5l2 2v1l-3-1-2-1c-8-1-16-1-25-1-3 0-6 1-8 2l-5 1h-3l-1 3h-1c0-2-1-3-2-5 0-2 0-2 1-4 7-10 17-20 29-26 1 0 1 1 3 0h0l1 1z"></path><path d="M712 147c8-3 16-4 25-2l-1 1c-3 0-10-2-12 0h0c3 0 5 0 7 1l1 2h-1c-5-1-9-1-13-1l-2 1c-10 1-16 5-23 13-3 5-5 11-8 15v1h-3l-1 3h-1c0-2-1-3-2-5 0-2 0-2 1-4 7-10 17-20 29-26 1 0 1 1 3 0h0l1 1z" class="G"></path><path d="M708 146c1 0 1 1 3 0h0l1 1c-12 5-22 14-28 26l-2 5-1 3h-1c0-2-1-3-2-5 0-2 0-2 1-4 7-10 17-20 29-26z"></path><defs><linearGradient id="c" x1="712.021" y1="157.751" x2="719.796" y2="176.265" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#c)" d="M698 175c1-2 2-3 3-5 2-3 5-7 8-10 7-7 15-6 24-6-5 4-8 8-9 15v1c1 2 1 4 2 5l2 2v1l-3-1-2-1c-8-1-16-1-25-1z"></path><path d="M220 182l-9-14c-13-20-32-30-55-35h103c7 0 15-1 21 0v1c-2 1-4 1-6 2h-2v2l1 1c1-1 2 0 3 0-6 3-13 7-18 12-2 2-4 4-6 7l-7 12-2 3c-4-1-9-7-12-9l-4 4c-2 5-4 8-5 14v2l-2-2z" class="P"></path><path d="M225 160l1-1 1 1h2v2c-1 2-1 3-2 5v1c-2 5-4 8-5 14v2l-2-2c0-5 1-8 2-12l3-10z" class="G"></path><path d="M225 160l1-1 1 1h2v2c-2 1-3 2-4 4s-1 3-3 4l3-10z" class="X"></path><path d="M210 148c-1-1-4-3-4-5 1-2 2-2 3-3h3c1-1 1-1 2-1h0v-1l4-1c7-1 15 0 22 0 2 0 5-1 8 0 0 0 1 0 1 1l-2 1c-5 2-10 4-14 8-3 4-5 7-7 12l-1 1-15-12z" class="F"></path><defs><linearGradient id="d" x1="216.717" y1="140.615" x2="225.721" y2="157.343" xlink:href="#B"><stop offset="0" stop-color="#4d4c4b"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#d)" d="M210 148v-2c-1-1-1 0 0-1 1 0 3-1 5-2 2 0 3 0 5-1l1 1c-1 1-1 1 0 2h1s1-1 2-1c3-1 7 2 9 3-3 4-5 7-7 12l-1 1-15-12z"></path><defs><linearGradient id="e" x1="269.794" y1="136.877" x2="247.199" y2="151.575" xlink:href="#B"><stop offset="0" stop-color="#353434"></stop><stop offset="1" stop-color="#5a5a59"></stop></linearGradient></defs><path fill="url(#e)" d="M259 133c7 0 15-1 21 0v1c-2 1-4 1-6 2h-2v2l1 1c1-1 2 0 3 0-6 3-13 7-18 12-2 2-4 4-6 7l-7 12-2 3c-4-1-9-7-12-9l-4 4v-1c1-2 1-3 2-5v-2h-2l-1-1c2-5 4-8 7-12 4-4 9-6 14-8l2-1 16-3h4v-1h2c1 0 2 1 3 0h0l1 1c1-1 1-1 2-1-2 0-6 1-7 0-3-1-7-1-11-1z"></path><path d="M265 135l2 1c-7 3-13 5-19 9 0-1 1-1 2-2l-1-1c-1-1 1-2 2-3h-4l2-1 16-3z" class="D"></path><defs><linearGradient id="f" x1="243.596" y1="149.771" x2="236.377" y2="144.01" xlink:href="#B"><stop offset="0" stop-color="#b0aea9"></stop><stop offset="1" stop-color="#dfdfdb"></stop></linearGradient></defs><path fill="url(#f)" d="M233 147c4-4 9-6 14-8h4c-1 1-3 2-2 3l1 1c-1 1-2 1-2 2l-3 3-10 10-3 4-1 2-4 4v-1c1-2 1-3 2-5v-2h-2l-1-1c2-5 4-8 7-12z"></path><path d="M232 162c-1-1-1-2 0-3 1-4 7-10 10-11 1-1 2-1 3 0l-10 10-3 4z" class="I"></path><defs><linearGradient id="g" x1="243.202" y1="150.826" x2="242.524" y2="171.534" xlink:href="#B"><stop offset="0" stop-color="#6b6a69"></stop><stop offset="1" stop-color="#93948f"></stop></linearGradient></defs><path fill="url(#g)" d="M235 158c3-1 4-4 6-5h4v-1h2l-1-1c3 0 5 1 7 2h-1-1 0 2l1 1-3 4h1l-7 12-2 3c-4-1-9-7-12-9l1-2 3-4z"></path><path d="M737 153c5 2 9 5 13 8 1 0 4 3 4 4 2 2 5 4 7 7l3 4h0c4 4 6 10 9 14h0c5 10 7 19 9 29 0 6 1 12 1 18 0 4-1 10 0 14 0 5-2 10-4 15-3 6-6 12-10 18 0 2-1 2-2 3 2-21 1-45-7-65l1-1c-1-4-4-8-6-12-2-2-3-4-5-6l1-2 3-3h1c0-1 0-1 1-1 1-1 1-1 1-2l-2-2-6-5h-1c-5-5-13-8-20-10v-1l-2-2c-1-1-1-3-2-5v-1c1-7 4-11 9-15 1 0 3-1 4-1z"></path><path d="M757 195c1 1 1 1 1 2 1 7 6 14 8 22h-1c-2-5-4-11-7-16-1-1-2-2-2-3l-1-2c0-1 0-1 1-1 1-1 1-1 1-2z" class="D"></path><defs><linearGradient id="h" x1="762.361" y1="199.687" x2="760.639" y2="182.813" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#8a8987"></stop></linearGradient></defs><path fill="url(#h)" d="M764 176c4 4 6 10 9 14h0c-1 3 2 8 3 11-6-3-10-7-16-9l-25-13c2 0 2 0 4 1 1 0 3 1 5 2h1l1 1c1 1 2 1 3 0h1c0 1 3 1 5 1v-1h1l-3-1c-1-1-2-1-3-2 3 0 6 1 9 1l6-1v-1c-1 0-1-1-1-2v-1z"></path><defs><linearGradient id="i" x1="768.512" y1="217.803" x2="750.412" y2="217.073" xlink:href="#B"><stop offset="0" stop-color="#898786"></stop><stop offset="1" stop-color="#acacab"></stop></linearGradient></defs><path fill="url(#i)" d="M755 198l1 2c0 1 1 2 2 3 3 5 5 11 7 16h1c2 5 3 10 4 15 0 2 1 4 1 6 1 11 1 22 0 32 0 3-2 9-2 12 0 2-1 2-2 3 2-21 1-45-7-65l1-1c-1-4-4-8-6-12-2-2-3-4-5-6l1-2 3-3h1z"></path><path d="M765 231c2 1 2 2 2 4v1l1 1v-1c0 1 0 2 1 3v3c1 1 0 3 0 4 0-1 0-1-1-2s-1-3-1-5c0-3-1-5-2-8z" class="G"></path><path d="M761 221c1 1 2 3 2 5l2 5c1 3 2 5 2 8 0 2 0 4 1 5s1 1 1 2v24 4h0c1-1 1-1 1-2l1-14c1-1 0-5 0-6-1-2 0-5-1-7h0c0-2 0-3 1-5 1 11 1 22 0 32 0 3-2 9-2 12 0 2-1 2-2 3 2-21 1-45-7-65l1-1z" class="D"></path><defs><linearGradient id="j" x1="743.687" y1="162.54" x2="745.066" y2="184.103" xlink:href="#B"><stop offset="0" stop-color="#777675"></stop><stop offset="1" stop-color="#b1b0ac"></stop></linearGradient></defs><path fill="url(#j)" d="M737 153c5 2 9 5 13 8 1 0 4 3 4 4 2 2 5 4 7 7l3 4h0v1c0 1 0 2 1 2v1l-6 1c-3 0-6-1-9-1 1 1 2 1 3 2l3 1h-1v1c-2 0-5 0-5-1h-1c-1 1-2 1-3 0l-1-1h-1c-2-1-4-2-5-2-2-1-2-1-4-1l-7-2-2-2c-1-1-1-3-2-5v-1c1-7 4-11 9-15 1 0 3-1 4-1z"></path><defs><linearGradient id="k" x1="736.172" y1="154.199" x2="737.26" y2="163.843" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#717170"></stop></linearGradient></defs><path fill="url(#k)" d="M737 153c5 2 9 5 13 8 1 0 4 3 4 4 2 2 5 4 7 7h-1c-1 0-2-2-3-3 0 0-2 0-3-1 0-1-3-3-4-5-4 0-10-1-14 0-1 1 0 1-1 1-3 0-5 1-7 1-2 1-2 1-2 2v1l-2 2v-1c1-7 4-11 9-15 1 0 3-1 4-1z"></path><path d="M413 399l4 1h0 0c1 1 2 2 4 2l2-1 7 17v2l1 1 2 5 1 2 3 6c-8-1-14-1-22 2-7 3-13 7-19 11-2 2-2 2-3 5l-6 7c-2 2-3 4-4 6 0 4-3 9-3 13-1 3-2 7-3 10-2 8-3 16-3 23 0 5 0 10 1 15h-1 0l-2-4-2-5c0-1-1-2-1-3-2-3-4-7-5-10-1-2-1-3-2-4l-6-15h0c0-1-1-3-1-4-1-7-1-14-1-21l1-6 1 1 3-18c1 0 2 0 3-1l2-6 2-4 6-12 3-3c5-1 10-4 15-7 7-4 16-5 23-5z" class="I"></path><path d="M381 436c0-2 2-3 3-4l6-1v1c0 1-1 2-1 3-2-1-2-1-4-1-1 2-5 7-7 8l3-5v-1z" class="H"></path><path d="M378 442c2-1 6-6 7-8 2 0 2 0 4 1-3 3-6 7-9 10h-1l-2 2v-2-1c1-1 1-1 1-2z" class="U"></path><path d="M400 418h0 2 0c-4 2-6 7-9 9l-1 1c-3 1-4 1-7 0 3-3 5-5 8-7l7-3z" class="T"></path><path d="M381 436v1l-3 5c0 1 0 1-1 2v1 2l2-2h1c-2 4-4 8-5 12l-1 1c0 2 0 4-1 6-2 1-1 3-3 4 0-3 2-6 1-10h0c-1 2-1 3-2 4v1l1-9 3-6 1-3 1 1 6-10z" class="D"></path><defs><linearGradient id="l" x1="372.381" y1="439.755" x2="381.5" y2="426.159" xlink:href="#B"><stop offset="0" stop-color="#494748"></stop><stop offset="1" stop-color="#787676"></stop></linearGradient></defs><path fill="url(#l)" d="M392 420h1v1c-3 2-5 4-8 7-4 5-9 11-11 17l-1 3c-1-1-1-1-1-3 1-2 0-3 0-5l-2-1c2-5 5-11 8-15 0 2-1 3-1 5 1-1 1-1 3-2s4-2 6-4h1c2-1 4-2 5-3z"></path><path d="M423 401l7 17v2c-6-1-13 1-19 3v-1-1c-2-1-3-1-4-2v-1c3-3 7-2 11-4h1 0c1-1 2-1 4-1v-1c0-2-1-3-1-5h0c1-2-1-4-1-5l2-1z" class="L"></path><path d="M423 401l7 17h-1c-2-2-4 0-7-2h6v-1c-1 0-2-1-2-1-1-1-2-1-3-1v-1c0-2-1-3-1-5h0c1-2-1-4-1-5l2-1z" class="B"></path><defs><linearGradient id="m" x1="362.102" y1="475.925" x2="372.841" y2="475.117" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#222221"></stop></linearGradient></defs><path fill="url(#m)" d="M370 439l2 1c0 2 1 3 0 5 0 2 0 2 1 3l-3 6-1 9v-1c1-1 1-2 2-4h0c1 4-1 7-1 10 2-1 1-3 3-4-1 4-2 8-2 11 0 2-1 3-1 4v2 2 34c0-1-1-2-1-3-2-3-4-7-5-10 0-4-1-8-1-11-1-9-1-18 0-27 0-6 2-13 4-19l3-8z"></path><path d="M370 439l2 1c0 2 1 3 0 5 0 2 0 2 1 3l-3 6v-5c1-1 1-1 1-3h-1l1-1-1-1h-1c0 1 0 2-1 3h-1l3-8z" class="F"></path><path d="M369 463v-1c1-1 1-2 2-4h0c1 4-1 7-1 10 2-1 1-3 3-4-1 4-2 8-2 11 0 2-1 3-1 4v2 2 34c0-1-1-2-1-3v-1c-2-13-2-26-2-38 1-4 1-9 2-12z" class="G"></path><path d="M382 448c2 0 3-1 4-2 0 2-2 5 0 7h1l1 1c-1 2-2 2-1 5h0c-2 2-3 4-4 6 0 4-3 9-3 13-1 3-2 7-3 10-2 8-3 16-3 23 0 5 0 10 1 15h-1 0l-2-4-2-5v-34c2-9 3-17 7-26l3-6 2-3z"></path><path d="M382 448c2 0 3-1 4-2 0 2-2 5 0 7h1l1 1c-1 2-2 2-1 5h0c-2 2-3 4-4 6h-2c1-2 2-4 2-6h-2v-3l-1-1-3 2 3-6 2-3z" class="F"></path><path d="M382 448c2 0 3-1 4-2 0 2-2 5 0 7l-2 2h-1c1-1 1-2 1-3l-1-1h-3l2-3z" class="Y"></path><path d="M381 465h2c0 4-3 9-3 13-1 3-2 7-3 10-2 8-3 16-3 23 0 5 0 10 1 15h-1 0l-2-4v-3-19c1-7 2-14 4-21 1-4 4-8 5-13v-1z" class="G"></path><path d="M413 399l4 1h0 0c1 1 2 2 4 2 0 1 2 3 1 5h0c0 2 1 3 1 5v1c-2 0-3 0-4 1h0c-7 0-13 1-19 4l-7 3v-1h-1c-1 1-3 2-5 3h-1c-2 2-4 3-6 4s-2 1-3 2c0-2 1-3 1-5 0 0 1-1 1-2-2 0-1-1-3 0 1-3 4-6 6-8v-2l1-1h1c2-2 6-5 6-7 7-4 16-5 23-5z" class="R"></path><path d="M399 403c5-2 11-3 18-3 1 1 2 2 4 2 0 1 2 3 1 5-1-2-1-2-2-3l-1 1h-1v-1c-3-1-6 0-10 0-2 1-4 1-6 1 1-1 4-2 6-3h-1-4l-1 1h-1-2z" class="J"></path><path d="M419 411h-6c-1 0-2-1-3-2v-3c3-2 5-1 8-1h1c1 1 2 3 2 5v1h-2z"></path><path d="M413 399l4 1h0 0c-7 0-13 1-18 3-4 1-8 4-10 6h0c2-1 6-3 9-4l-11 8h-1 0c-1 0-2 1-2 1h-2v-2l1-1h1c2-2 6-5 6-7 7-4 16-5 23-5z" class="K"></path><path d="M419 405l1-1c1 1 1 1 2 3h0c0 2 1 3 1 5v1c-2 0-3 0-4 1h0c-7 0-13 1-19 4l-7 3v-1c4-2 7-4 11-6v-1c-2-2-4-3-8-3l3-2c1-1 2-1 4-1 1 1 3 4 5 5h4 3c2-1 3 0 4-1h2v-1c0-2-1-4-2-5z" class="M"></path><path d="M396 410c4 0 6 1 8 3v1c-4 2-7 4-11 6h-1c-1 1-3 2-5 3h-1c-2 2-4 3-6 4s-2 1-3 2c0-2 1-3 1-5 0 0 1-1 1-2-2 0-1-1-3 0 1-3 4-6 6-8h2s1-1 2-1h0 1 1l8-3z" class="D"></path><path d="M386 413l-7 9c-2 0-1-1-3 0 1-3 4-6 6-8h2s1-1 2-1h0z" class="T"></path><path d="M386 423c-1 0-1 0-2-1l1-1c1-3 4-5 6-6h1 3l1-1c0 1 1 1 1 2l-1 1h0l-1 1c-2 1-2 1-3 2s-3 2-5 3h-1z" class="K"></path><path d="M403 428c4-3 10-5 16-6 4-1 8-1 12-1l2 5 1 2 3 6c-8-1-14-1-22 2-7 3-13 7-19 11-2 2-2 2-3 5l-6 7h0c-1-3 0-3 1-5l-1-1h-1c-2-2 0-5 0-7-1 1-2 2-4 2 5-8 13-15 21-20z" class="Q"></path><path d="M407 432c3 0 5 0 8-1h1l-5 3c-8 3-15 8-21 14l-2 2h-1c5-8 12-14 20-18z" class="H"></path><defs><linearGradient id="n" x1="429.317" y1="420.378" x2="415.683" y2="426.122" xlink:href="#B"><stop offset="0" stop-color="#090706"></stop><stop offset="1" stop-color="#252627"></stop></linearGradient></defs><path fill="url(#n)" d="M403 428c4-3 10-5 16-6 4-1 8-1 12-1l2 5c-2 1-4 0-6 1l-10 1c2-2 3-1 4-2 1 0 2 1 4 0 1 0 3 0 4-1-1-1-5-1-6-1l-1 1h-3c-4 1-7 1-10 3-1 1-2 1-3 1 1-1 3-2 4-3-2 0-3 2-5 2-1 1-2 0-2 0z"></path><defs><linearGradient id="o" x1="405.678" y1="445.124" x2="395.822" y2="439.376" xlink:href="#B"><stop offset="0" stop-color="#333637"></stop><stop offset="1" stop-color="#6b6765"></stop></linearGradient></defs><path fill="url(#o)" d="M390 448c6-6 13-11 21-14v1l4 1c-7 3-13 7-19 11-2 2-2 2-3 5l-6 7h0c-1-3 0-3 1-5l4-4c-1 0-1-1-2-2z"></path><defs><linearGradient id="p" x1="431.765" y1="426.991" x2="414.547" y2="439.777" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#282828"></stop></linearGradient></defs><path fill="url(#p)" d="M433 426l1 2 3 6c-8-1-14-1-22 2l-4-1v-1l5-3h-1c-3 1-5 1-8 1 4-2 7-3 10-4l10-1c2-1 4 0 6-1z"></path><path d="M433 426l1 2c-6 0-12 1-18 3h-1c-3 1-5 1-8 1 4-2 7-3 10-4l10-1c2-1 4 0 6-1z" class="D"></path><path d="M375 411c5-1 10-4 15-7 0 2-4 5-6 7h-1l-1 1v2c-2 2-5 5-6 8 2-1 1 0 3 0 0 1-1 2-1 2-3 4-6 10-8 15l-3 8c-2 6-4 13-4 19-1 9-1 18 0 27 0 3 1 7 1 11-1-2-1-3-2-4l-6-15h0c0-1-1-3-1-4-1-7-1-14-1-21l1-6 1 1 3-18c1 0 2 0 3-1l2-6 2-4 6-12 3-3z" class="U"></path><defs><linearGradient id="q" x1="361.389" y1="447.086" x2="366.887" y2="432.331" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#a9a8a4"></stop></linearGradient></defs><path fill="url(#q)" d="M361 471c0-10-1-34 6-41v1l1-1v5c-2 4-3 9-4 13v5c-1 4-2 9-2 13 0 2 0 4-1 5z"></path><path d="M375 411c5-1 10-4 15-7 0 2-4 5-6 7h-1l-1 1-11 11c-1 1-3 3-4 3h-1l6-12 3-3z" class="T"></path><defs><linearGradient id="r" x1="375.588" y1="442.506" x2="367.088" y2="426.898" xlink:href="#B"><stop offset="0" stop-color="#d5d0d4"></stop><stop offset="1" stop-color="#f9fbf4"></stop></linearGradient></defs><path fill="url(#r)" d="M376 422c2-1 1 0 3 0 0 1-1 2-1 2-3 4-6 10-8 15l-3 8c-2 6-4 13-4 19h-1c0-4 1-9 2-13v-5c1-4 2-9 4-13 2-5 4-9 8-13z"></path><defs><linearGradient id="s" x1="352.128" y1="465.964" x2="367.151" y2="461.961" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#363633"></stop></linearGradient></defs><path fill="url(#s)" d="M359 437c1 0 2 0 3-1-2 8-2 16-2 24v13c1-1 1-1 1-2 1-1 1-3 1-5h1c-1 9-1 18 0 27 0 3 1 7 1 11-1-2-1-3-2-4l-6-15h0c0-1-1-3-1-4-1-7-1-14-1-21l1-6 1 1 3-18z"></path><path d="M355 454l1 1c0 7-1 13 0 20 0 2 2 8 1 10h-1 0c0-1-1-3-1-4-1-7-1-14-1-21l1-6z" class="O"></path><path d="M360 460v13c1-1 1-1 1-2 1-1 1-3 1-5h1c-1 9-1 18 0 27 0 3 1 7 1 11-1-2-1-3-2-4 0-4-1-8-2-12-1-8-1-19 0-28z" class="G"></path><path d="M698 175c9 0 17 0 25 1l2 1 3 1c7 2 15 5 20 10h1l6 5 2 2c0 1 0 1-1 2-1 0-1 0-1 1h-1l-3 3-1 2c2 2 3 4 5 6 2 4 5 8 6 12l-1 1c8 20 9 44 7 65-3 9-6 18-11 27l2-9-1-1c-1-4 0-9 0-13 0-12-2-24-6-35l-2-2c-4-9-10-17-17-23-2-2-5-4-8-6-6-3-11-7-18-9-3-1-6-1-9-2h-9v-3c0-1-1-3-1-5l-2-10v-2c6-1 11 0 16 1h1l-3-2h0-2-2c-2-1-2-1-4-1-1 1-4 1-5 1h-1c-1-1-1-2-2-4v-1s-1-4-1-5c1-1 2-1 4-2h-3v-1c1 0 1 0 2-1h2 1 1l1-2c2-1 5-2 8-2z"></path><path d="M735 216l1-1c2 1 3 2 5 3v-2c4 5 6 10 8 15 9 21 13 45 10 67 0 2 0 6-1 7l-1-1c-1-4 0-9 0-13 0-12-2-24-6-35v-1c3 3 4 11 5 14h0c0-5-2-9-3-14l1-1c1 1 1 3 2 4v-1l-1-3c0-2 0-3-1-4v-3h0l-1-3c-1-1-1-2-1-3l-1-1-2-5v-1h-1l-1-1v-1c-1-1-1-2-2-2-1-2-2-3-3-5 0-1-1-1-1-2-2-3-4-5-6-7z" class="D"></path><defs><linearGradient id="t" x1="698.39" y1="170.24" x2="716.394" y2="187.387" xlink:href="#B"><stop offset="0" stop-color="#92918e"></stop><stop offset="1" stop-color="#c9c9c8"></stop></linearGradient></defs><path fill="url(#t)" d="M698 175c9 0 17 0 25 1l2 1c-1 2-1 4 0 6-1 0-2 0-2-1-2 2-4 1-6 1l1 1h1 1c1 1 2 1 3 1l2 1v1c-9-4-18-6-28-6-4 0-7 1-10 0h-1-3v-1c1 0 1 0 2-1h2 1 1l1-2c2-1 5-2 8-2z"></path><path d="M683 188c4 0 7-1 11 0 12 0 24 6 33 13 5 4 11 10 14 15v2c-2-1-3-2-5-3l-1 1c-1-1-2-2-2-3-2-2-5-4-7-6-2-3-5-4-8-6h0c-3-2-7-4-11-3l-6-3h1l-3-2h0-2-2c-2-1-2-1-4-1-1 1-4 1-5 1h-1c-1-1-1-2-2-4v-1z" class="V"></path><path d="M683 189h1 2c3-1 9 0 12 0h1l6 2v1h1c1 1 1 0 2 1v1h-1c-2-1-4 0-7-1h-1 0-2-2c-2-1-2-1-4-1-1 1-4 1-5 1h-1c-1-1-1-2-2-4z" class="D"></path><path d="M705 191c4 1 12 4 15 8 0 1 0 0-1 1h0l-1 1h0c-3-2-7-4-11-3l-6-3h1l-3-2h1c3 1 5 0 7 1h1v-1c-1-1-1 0-2-1h-1v-1z" class="S"></path><defs><linearGradient id="u" x1="740.768" y1="204.217" x2="730.562" y2="175.522" xlink:href="#B"><stop offset="0" stop-color="#cbccc8"></stop><stop offset="1" stop-color="#fafbee"></stop></linearGradient></defs><path fill="url(#u)" d="M725 177l3 1c7 2 15 5 20 10h1l6 5 2 2c0 1 0 1-1 2-1 0-1 0-1 1h-1l-3 3-1 2c2 2 3 4 5 6 2 4 5 8 6 12l-1 1c-8-16-20-26-35-35v-1l-2-1c-1 0-2 0-3-1h-1-1l-1-1c2 0 4 1 6-1 0 1 1 1 2 1-1-2-1-4 0-6z"></path><path d="M749 188l6 5 2 2c0 1 0 1-1 2-1 0-1 0-1 1h-1l-3 3-1 2h0c-1-1-2-1-2-3h1c2-1 2-4 3-5s1-1 1-2c-1-1-3-2-4-4v-1z" class="U"></path><path d="M755 193l2 2c0 1 0 1-1 2-1 0-1 0-1 1h-1l-1-1 2-4z" class="H"></path><defs><linearGradient id="v" x1="704.253" y1="220.683" x2="721.36" y2="205.825" xlink:href="#B"><stop offset="0" stop-color="#494949"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#v)" d="M685 196v-2c6-1 11 0 16 1l6 3c4-1 8 1 11 3h0c3 2 6 3 8 6 2 2 5 4 7 6 0 1 1 2 2 3 2 2 4 4 6 7 0 1 1 1 1 2 1 2 2 3 3 5 1 0 1 1 2 2v1l1 1h1v1l2 5 1 1c0 1 0 2 1 3l1 3h0v3c1 1 1 2 1 4l1 3v1c-1-1-1-3-2-4l-1 1c1 5 3 9 3 14h0c-1-3-2-11-5-14v1l-2-2c-4-9-10-17-17-23-2-2-5-4-8-6-6-3-11-7-18-9-3-1-6-1-9-2h-9v-3c0-1-1-3-1-5l-2-10z"></path><path d="M685 196c2-1 5 0 7 0 1 1 1 2 2 3 0 2 0 3-1 5-2 2-3 2-6 2l-2-10z"></path><path d="M688 211c5-1 11 0 16 0l-11-2h7 1c2 0 8 1 9 2v1c4 2 9 4 13 6 1 1 3 2 3 3-2-1-5-3-7-4l-3-1c-2-1-7-2-9-1 0 0-1 0-1 1-3-1-6-1-9-2h-9v-3z" class="D"></path><defs><linearGradient id="w" x1="731.732" y1="237.484" x2="731.768" y2="201.015" xlink:href="#B"><stop offset="0" stop-color="#adb1ac"></stop><stop offset="1" stop-color="#efe9e7"></stop></linearGradient></defs><path fill="url(#w)" d="M707 198c4-1 8 1 11 3h0c3 2 6 3 8 6 2 2 5 4 7 6 0 1 1 2 2 3 2 2 4 4 6 7 0 1 1 1 1 2 1 2 2 3 3 5 1 0 1 1 2 2v1l1 1h1v1l2 5 1 1c0 1 0 2 1 3l1 3h0v3h-2l-4-8v-2c-1-2-3-5-4-7-9-14-21-28-37-35z"></path><defs><linearGradient id="x" x1="745.524" y1="247.357" x2="711.802" y2="211.459" xlink:href="#B"><stop offset="0" stop-color="#a6a5a2"></stop><stop offset="1" stop-color="#ebebe4"></stop></linearGradient></defs><path fill="url(#x)" d="M710 211c12 3 25 13 32 24l5 8c2 2 3 6 5 8v-1h2c1 1 1 2 1 4l1 3v1c-1-1-1-3-2-4l-1 1c1 5 3 9 3 14h0c-1-3-2-11-5-14v1l-2-2c-4-9-10-17-17-23-2-2-5-4-8-6-6-3-11-7-18-9 0-1 1-1 1-1 2-1 7 0 9 1l3 1c2 1 5 3 7 4 0-1-2-2-3-3-4-2-9-4-13-6v-1z"></path><path d="M659 371l2 1h1c1 1 1 1 3 2 7 3 13 12 17 19s7 15 8 23 1 17 1 26c0 3-1 8 0 10l1 1-15 36c-2 2-3 8-6 9v-8c1-10 1-20 0-30l-2-9c-1 0-1-1-2-2v-2h-1l1-1c-2 0-3 1-4 2l2 5h-1 0c-1-4-5-5-6-9h0c-1-4-4-7-7-9l-1 1-3-3c-3-3-7-5-10-8v-1l-3-3c-1 0-1 0-2-1-2-2-4-3-7-3l-3-1c-1 0-1 1-2 1h-3c-3 0-5 1-8 1 0-1 1-2 1-3l5-11c1-2 2-3 3-4v-3l3-6c1-1 1-2 2-4v-1c4-2 9-3 14-3 10 0 19 0 27 6h1c1 0 3 1 4 2 1 0 0 0 1-1-1-2-2-3-3-4 0-1 0-2 1-2v-1c-2-2-5-4-7-5 1-1 1-1 2-1h1c1 1 1 0 2 0-2-2-5-3-8-5l1-1z" class="P"></path><path d="M679 447c1 1 2 3 3 4v11c1 1 0 3 1 5h0c0 1 0 1-1 2-1-1 0-10 0-12-1 1-2 3-2 5l-1 4v-19z" class="F"></path><path d="M659 371l2 1h1c1 1 1 1 3 2 7 3 13 12 17 19s7 15 8 23h-1c0 1 0 2 1 4v3c0 2 0 7-1 9h0c1 5 1 11 0 16 0 2 0 4-1 5v-1c2-7 0-15-1-21-1-14-4-27-10-39l-3-4c-1-2-4-3-6-4v-1c-2-2-5-4-7-5 1-1 1-1 2-1h1c1 1 1 0 2 0-2-2-5-3-8-5l1-1z" class="L"></path><path d="M661 378c1-1 1-1 2-1h1c1 1 1 0 2 0 4 4 8 7 12 12 6 10 8 21 9 32l1 7c0 1 0 3 1 4 1 5 1 11 0 16 0 2 0 4-1 5v-1c2-7 0-15-1-21-1-14-4-27-10-39l-3-4c-1-2-4-3-6-4v-1c-2-2-5-4-7-5z" class="I"></path><defs><linearGradient id="y" x1="685.701" y1="409.702" x2="673.385" y2="395.225" xlink:href="#B"><stop offset="0" stop-color="#3e3c3a"></stop><stop offset="1" stop-color="#797976"></stop></linearGradient></defs><path fill="url(#y)" d="M667 386c0-1 0-2 1-2 2 1 5 2 6 4l3 4c6 12 9 25 10 39l-1-1h0c1-2 0-3 0-5v-2l-1-1h0c1-1 1-1 0-2-1 1-1 1-1 2h0c0 2 0 3 1 4 0 2-1 6-2 8-2-12-5-23-11-33-2-5-5-8-8-12h1c1 0 3 1 4 2 1 0 0 0 1-1-1-2-2-3-3-4z"></path><path d="M667 386c0-1 0-2 1-2 2 1 5 2 6 4l3 4c-1 1-2 2-2 3 2 4 3 8 4 12l-10-16c1 0 0 0 1-1-1-2-2-3-3-4z" class="D"></path><path d="M674 388l3 4c-1 1-2 2-2 3-1 0-1-1-2-2 0-1 0-4 1-5z" class="L"></path><path d="M637 383c10 0 19 0 27 6 3 4 6 7 8 12 6 10 9 21 11 33v33c-1-2 0-4-1-5v-11c-1-1-2-3-3-4-1-5 0-11-2-17 0 0-1-2-1-3l-3-10c-2-2-3-6-4-8l2-1v-1l-3-6c0-2-1-3-3-5-3-3-6-7-10-8l-3-1-5-1c-4-2-7-2-10-2v-1z" class="H"></path><path d="M669 409l2-1c2 3 4 7 5 11-1-1-2-2-3-2-2-2-3-6-4-8z" class="G"></path><path d="M673 417c1 0 2 1 3 2 1 2 4 7 3 11h-2s-1-2-1-3l-3-10z" class="W"></path><path d="M677 430h2c2 6 3 14 3 21-1-1-2-3-3-4-1-5 0-11-2-17z" class="Y"></path><defs><linearGradient id="z" x1="678.638" y1="456.536" x2="665.418" y2="455.577" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2c2c"></stop></linearGradient></defs><path fill="url(#z)" d="M668 409h1c1 2 2 6 4 8l3 10c0 1 1 3 1 3 2 6 1 12 2 17v19c0 5 0 11-1 16l-1-1c0 2 0 2-1 4h1v4c-2 2-3 8-6 9v-8c1-10 1-20 0-30v-2l-1-11v-3h1v-1-2l1-1c-1-8-3-15-6-23-1-2-2-4-2-6 1-1 1-1 2-1h1l2 2h0v-2l-1-1z"></path><path d="M672 440l2 15-1 4-1-1h-1l-1-11v-3h1v-1-2l1-1z" class="D"></path><path d="M670 447v-3h1c2 4 1 9 1 14h-1l-1-11z" class="O"></path><defs><linearGradient id="AA" x1="676.23" y1="463.791" x2="668.555" y2="470.462" xlink:href="#B"><stop offset="0" stop-color="#969694"></stop><stop offset="1" stop-color="#b2b1b0"></stop></linearGradient></defs><path fill="url(#AA)" d="M674 455v21c-1 7-1 15-3 22v-8c1-10 1-20 0-30v-2h1l1 1 1-4z"></path><defs><linearGradient id="AB" x1="680.4" y1="430.299" x2="667.1" y2="421.201" xlink:href="#B"><stop offset="0" stop-color="#b0acb0"></stop><stop offset="1" stop-color="#d5d7ce"></stop></linearGradient></defs><path fill="url(#AB)" d="M668 409h1c1 2 2 6 4 8l3 10c0 1 1 3 1 3 2 6 1 12 2 17v19c0 5 0 11-1 16l-1-1c2-19 2-38-4-57l-2-2v-1l-1-1c-1 0-2 0-2-1l-1-1-1-1c-1-2-2-4-2-6 1-1 1-1 2-1h1l2 2h0v-2l-1-1z"></path><path d="M664 411c1-1 1-1 2-1h1c2 4 4 9 6 14l-2-2v-1l-1-1c-1 0-2 0-2-1l-1-1-1-1c-1-2-2-4-2-6z" class="F"></path><path d="M623 386c4-2 9-3 14-3v1c3 0 6 0 10 2l5 1 3 1c4 1 7 5 10 8 2 2 3 3 3 5l3 6v1l-2 1h-1l1 1v2h0l-2-2h-1c-1 0-1 0-2 1h0l-2 1h-1c-2 0-4-2-5-3l-9-5c-3-2-5-3-8-4-1 0-2 1-3 2l-2-1-3-1c0-1 0-2 1-3h-4c-3-1-4 0-7-1l-2 2-1-1 3-6c1-1 1-2 2-4v-1z"></path><path d="M624 395c5-3 12-3 18-2l5 1h0c-2 0-2 0-3 1v1l1 1c3 0 6 1 8 3-3 0-7-1-10-1-3-1-5-1-8-1l-3-1h-4c-3-1-4 0-7-1 1 0 2-1 3-1z" class="R"></path><path d="M642 393l5 1h0c-2 0-2 0-3 1v1l1 1-8-2c1-2 2-2 5-2z" class="V"></path><path d="M624 395c5-3 12-3 18-2-3 0-4 0-5 2-2 0-4-1-6 0h-7z" class="C"></path><path d="M623 386c4-2 9-3 14-3v1c3 0 6 0 10 2l5 1 3 1c-1 2-1 2-3 3l-2-1c-1 1-2 1-2 2v1l-1 1v-1c-1-2-4-3-6-4-7-2-14-1-20 2 1-1 1-2 2-4v-1z" class="W"></path><path d="M623 386c4-2 9-3 14-3v1c3 0 6 0 10 2-3 0-6-1-9 0-2 0-6 0-7-1l-3 1c-2 0-2 1-4 1h-1v-1z" class="X"></path><path d="M652 387l3 1c-1 2-1 2-3 3l-2-1c-1 1-2 1-2 2v1l-1 1v-1c-1-2-4-3-6-4-7-2-14-1-20 2 1-1 1-2 2-4l1 1c4-1 9-1 14-1 3 1 8 3 12 2l1-1h1v-1zm-20 10l3 1c3 0 5 0 8 1 3 0 7 1 10 1 4 1 11 6 13 10-1 0-1 0-2 1h0l-2 1h-1c-2 0-4-2-5-3l-9-5c-3-2-5-3-8-4-1 0-2 1-3 2l-2-1-3-1c0-1 0-2 1-3z" class="G"></path><path d="M632 397l3 1 4 2c-1 0-2 1-3 2l-2-1-3-1c0-1 0-2 1-3z" class="C"></path><path d="M643 399c3 0 7 1 10 1 4 1 11 6 13 10-1 0-1 0-2 1h0l-1-2h0l-1 1h-1v-2c-1-1-2-1-3-2-2-1-4-3-6-3-3-2-6-2-9-4h0z" class="M"></path><path d="M655 388c4 1 7 5 10 8 2 2 3 3 3 5l3 6v1l-2 1h-1l1 1v2h0l-2-2h-1c-2-4-9-9-13-10-2-2-5-3-8-3l-1-1v-1c1-1 1-1 3-1h0l1-1v-1c0-1 1-1 2-2l2 1c2-1 2-1 3-3z" class="D"></path><path d="M665 404l1-1c2 1 3 2 5 4v1l-2 1h-1c0-2-2-3-3-5z" class="U"></path><defs><linearGradient id="AC" x1="655.694" y1="388.265" x2="660.999" y2="397.689" xlink:href="#B"><stop offset="0" stop-color="#a09f9a"></stop><stop offset="1" stop-color="#c0c1bf"></stop></linearGradient></defs><path fill="url(#AC)" d="M655 388c4 1 7 5 10 8l-1 1c-3-1-7-4-10-5-2 0-2 1-3 2l1 1v1c-1-1-3-2-5-2h0l1-1v-1c0-1 1-1 2-2l2 1c2-1 2-1 3-3z"></path><path d="M647 394c2 0 4 1 5 2 5 3 9 4 13 8 1 2 3 3 3 5l1 1v2h0l-2-2h-1c-2-4-9-9-13-10-2-2-5-3-8-3l-1-1v-1c1-1 1-1 3-1z" class="I"></path><path d="M621 396c3 1 4 0 7 1h4c-1 1-1 2-1 3l3 1 2 1c1-1 2-2 3-2 3 1 5 2 8 4l9 5c1 1 3 3 5 3h1l2-1h0c0 2 1 4 2 6 3 8 5 15 6 23l-1 1v2 1h-1v3l1 11v2l-2-9c-1 0-1-1-2-2v-2h-1l1-1c-2 0-3 1-4 2l2 5h-1 0c-1-4-5-5-6-9h0c-1-4-4-7-7-9l-1 1-3-3c-3-3-7-5-10-8v-1l-3-3c-1 0-1 0-2-1-2-2-4-3-7-3l-3-1c-1 0-1 1-2 1h-3c-3 0-5 1-8 1 0-1 1-2 1-3l5-11c1-2 2-3 3-4v-3l1 1 2-2z" class="M"></path><path d="M636 402c1-1 2-2 3-2 3 1 5 2 8 4-1 1-1 1-1 3l4 3v3l-4-4c-2-3-6-5-10-7z" class="H"></path><path d="M615 404c3-1 5-2 7-2-2 2-4 5-5 8h2c-1 1-2 1-4 2h0l-2 2c-1 0-2 1-2 2l-1-1 5-11z" class="E"></path><path d="M619 410c1-3 2-6 5-7 1-1 4-1 6-1v1 1 3c-2 1-3 3-6 3h-5z" class="P"></path><path d="M615 412c1 0 3 0 4-1h3l1 1c-2 1-4 1-6 1 3 0 7 0 8 1v3l-3-1c-1 0-1 1-2 1h-3c-3 0-5 1-8 1 0-1 1-2 1-3l1 1c0-1 1-2 2-2l2-2z" class="R"></path><path d="M617 413c3 0 7 0 8 1v3l-3-1c-3-1-5-2-8-1h0v-1l3-1zm4-17c3 1 4 0 7 1h4c-1 1-1 2-1 3l3 1v1c-4 0-8-2-12 0h0c-2 0-4 1-7 2 1-2 2-3 3-4v-3l1 1 2-2z" class="G"></path><path d="M618 400h13l3 1v1c-4 0-8-2-12 0h0c-2 0-4 1-7 2 1-2 2-3 3-4z" class="F"></path><path d="M648 422c-5-3-10-7-16-8v-1c2 0 3 1 5 1h7 1c2 1 4 3 7 4 1 1 2 3 3 4 0 2-1 2-2 3 0 0 2 2 2 3h-1l-6-6z" class="T"></path><path d="M648 422l6 6c1 2 2 4 4 5h0c0-2-1-2-1-3-1-1-1-2-1-3 2 2 2 4 6 5 3 6 5 13 7 19-1 0-1-1-2-2v-2h-1l1-1c-2 0-3 1-4 2l2 5h-1 0c-1-4-5-5-6-9h0c-1-4-4-7-7-9 0-1-2-3-3-4l3 2c2-1 2-3 2-4-2-3-3-4-6-6l1-1z" class="F"></path><path d="M653 429c4 5 7 12 10 19l2 5h-1 0c-1-4-5-5-6-9h0c-1-4-4-7-7-9 0-1-2-3-3-4l3 2c2-1 2-3 2-4z" class="N"></path><path d="M625 414c5 1 11 2 16 5 2 1 4 2 6 4 3 2 4 3 6 6 0 1 0 3-2 4l-3-2c1 1 3 3 3 4l-1 1-3-3c-3-3-7-5-10-8v-1l-3-3c-1 0-1 0-2-1-2-2-4-3-7-3v-3z" class="I"></path><path d="M625 414c5 1 11 2 16 5-2 0-5 0-6-1l-1 1c1 2 1 3 3 3l1 1c1 1 1 1 2 1 2 1 6 5 8 7h0c1 1 3 3 3 4l-1 1-3-3c-3-3-7-5-10-8v-1l-3-3c-1 0-1 0-2-1-2-2-4-3-7-3v-3z" class="V"></path><defs><linearGradient id="AD" x1="657.514" y1="440.806" x2="666.486" y2="422.694" xlink:href="#B"><stop offset="0" stop-color="#cccdbe"></stop><stop offset="1" stop-color="#f4f3f6"></stop></linearGradient></defs><path fill="url(#AD)" d="M647 404l9 5c1 1 3 3 5 3h1l2-1h0c0 2 1 4 2 6 3 8 5 15 6 23l-1 1v2 1h-1v3l1 11v2l-2-9c-2-6-4-13-7-19-2-4-5-7-7-11-1-1-1-2-1-2l-1-2c-1-2-2-3-3-4v-3l-4-3c0-2 0-2 1-3z"></path><path d="M657 417c0-1-1-1-1-2h2c2 1 4 3 5 4v1c-1 0-1 1-2 1l1 1v3c-2-2-3-5-5-8z" class="K"></path><path d="M650 410l8 5h-2c0 1 1 1 1 2l-2-1h-1l2 3h0c-1 1-1 1-1 2-1-1-1-2-1-2l-1-2c-1-2-2-3-3-4v-3z" class="I"></path><defs><linearGradient id="AE" x1="666.243" y1="431.909" x2="662.201" y2="421.112" xlink:href="#B"><stop offset="0" stop-color="#b2b1ad"></stop><stop offset="1" stop-color="#cfd0c7"></stop></linearGradient></defs><path fill="url(#AE)" d="M662 425v-3l-1-1c1 0 1-1 2-1v-1c1 2 2 4 2 6l3 11c1 2 1 5 3 7v1h-1v3c-2-8-5-15-8-22z"></path><defs><linearGradient id="AF" x1="654.659" y1="428.081" x2="661.841" y2="410.919" xlink:href="#B"><stop offset="0" stop-color="#bab9b9"></stop><stop offset="1" stop-color="#fafaf3"></stop></linearGradient></defs><path fill="url(#AF)" d="M647 404l9 5c1 1 3 3 5 3h1l2-1h0c0 2 1 4 2 6 3 8 5 15 6 23l-1 1v2c-2-2-2-5-3-7l-3-11c0-2-1-4-2-6-1-1-3-3-5-4l-8-5-4-3c0-2 0-2 1-3z"></path><path d="M287 136c8-2 17-1 25 1h3c10 3 22 9 30 17l4 2h2c6 5 11 11 15 18-1 0-1 1-2 2-1 2-1 5-3 6h-1l-6-2c-4-1-7-1-11-1l1 1c-22 2-42 13-56 30-14 16-18 39-18 60v17 2h-1c1 1 1 2 1 3l-16-36c-1-1-1-2-2-3-4-11-11-21-12-33 0-14 2-27 8-39v-1c-2-4-1-7-3-10l7-12c2-3 4-5 6-7 5-5 12-9 18-12l11-3z"></path><path d="M302 152l2-1c2 0 5 0 6 1l-6 2-2 1c-2-1-3 0-5 0l5-3z" class="V"></path><path d="M351 156c6 5 11 11 15 18-1 0-1 1-2 2-5-7-9-13-15-20h2z" class="K"></path><path d="M273 184h0c0 2-1 4-2 6 2 1 4 2 5 4 2-1 3-2 4-2l1-1h2l-13 9c-1-2-3-4-3-6 1-4 4-7 6-10z" class="M"></path><path d="M254 209l3 3c1 0 1-1 2-1-1 7-3 13-4 20l-1 19c-1 2 0 4 0 6-1-1-1-2-2-3 1-3-1-6-1-9 0-12 1-24 3-35z" class="O"></path><path d="M302 155l2-1h1 4c3 0 7 0 10 1h1c7 1 12 6 16 12l4 6-10 1c1-1 3-1 4-2-7-6-16-11-25-14-2-1-4 0-6-1h-1-2c0 1-1 1-2 1h-1l5-3z" class="Q"></path><path d="M295 148c13-4 28-5 40 1 4 2 7 4 9 6 5 5 8 10 11 16 2 2 3 5 4 8-6-9-10-21-20-26-9-5-19-4-29-1-1-1-4-1-6-1l-2 1c-2 0-5 1-7 0 1-2 2-3 3-4-2 1-3 1-5 2l-1-1 3-1z" class="D"></path><path d="M298 148c8-2 19-4 27-2 1 1 3 1 4 2-2 1-5-1-6-1h-9l1 1h5v1c-6 0-11 0-16 2l-2 1c-2 0-5 1-7 0 1-2 2-3 3-4z" class="G"></path><defs><linearGradient id="AG" x1="322.365" y1="136.767" x2="309.89" y2="152.033" xlink:href="#B"><stop offset="0" stop-color="#0a0909"></stop><stop offset="1" stop-color="#2f2f2e"></stop></linearGradient></defs><path fill="url(#AG)" d="M312 137h3c10 3 22 9 30 17l-1 1c-2-2-5-4-9-6-12-6-27-5-40-1l-3 1c-1-1-2-1-2-2h-1v-1c0-2 2-3 3-4h0c2-1 3-1 5-2v-1l2-1h2c1-1 2-1 4-1s4 1 7 0z"></path><path d="M292 142h4 0v1c-1 1 0 3-1 5l-3 1c-1-1-2-1-2-2h-1v-1c0-2 2-3 3-4h0z" class="P"></path><path d="M251 175v1c0 2-2 4-1 6 3 3 6 5 8 8l-4 19c-2 11-3 23-3 35 0 3 2 6 1 9-4-11-11-21-12-33 0-14 2-27 8-39l3-6z"></path><defs><linearGradient id="AH" x1="298.813" y1="160.032" x2="264.629" y2="170.784" xlink:href="#B"><stop offset="0" stop-color="#a9a9a4"></stop><stop offset="1" stop-color="#d8d8d0"></stop></linearGradient></defs><path fill="url(#AH)" d="M290 147c0 1 1 1 2 2l1 1c2-1 3-1 5-2-1 1-2 2-3 4 2 1 5 0 7 0l-5 3c2 0 3-1 5 0l-5 3-9 7-3 3-9 11c-1 1-3 3-3 5-2 3-5 6-6 10l-4 7-3 8s0 2-1 2-1 1-2 1l-3-3 4-19c4-6 6-13 10-19 5-8 14-15 21-21v-2l1-1z"></path><path d="M297 155c2 0 3-1 5 0l-5 3-9 7c-1-1-2 0-3 0 4-4 8-7 12-10z" class="H"></path><defs><linearGradient id="AI" x1="276.703" y1="181.987" x2="272.564" y2="177.776" xlink:href="#B"><stop offset="0" stop-color="#d3cfcf"></stop><stop offset="1" stop-color="#f7f8f0"></stop></linearGradient></defs><path fill="url(#AI)" d="M285 165c1 0 2-1 3 0l-3 3-9 11c-1 1-3 3-3 5-2 3-5 6-6 10l-4 7c-1-3 0-7 1-9 1-1 3-2 4-4s2-5 3-7c4-6 9-11 14-16z"></path><defs><linearGradient id="AJ" x1="265.583" y1="209.484" x2="256.441" y2="178.303" xlink:href="#B"><stop offset="0" stop-color="#8e8f8e"></stop><stop offset="1" stop-color="#c3c0ba"></stop></linearGradient></defs><path fill="url(#AJ)" d="M268 171c0 2 0 3-1 4h1v1c0 5-2 11-4 16-1 2-2 6-1 9l-3 8s0 2-1 2-1 1-2 1l-3-3 4-19c4-6 6-13 10-19z"></path><path d="M297 158h1c1 0 2 0 2-1h2 1c2 1 4 0 6 1 9 3 18 8 25 14-1 1-3 1-4 2l-10 1c-10 3-18 7-27 11-4 1-7 3-10 5h-2l-1 1c-1 0-2 1-4 2-1-2-3-3-5-4 1-2 2-4 2-6h0c0-2 2-4 3-5l9-11 3-3 9-7z" class="O"></path><defs><linearGradient id="AK" x1="316.527" y1="160.793" x2="312.373" y2="171.406" xlink:href="#B"><stop offset="0" stop-color="#525151"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#AK)" d="M297 158h1c1 0 2 0 2-1h2 1c2 1 4 0 6 1 9 3 18 8 25 14-1 1-3 1-4 2l-10 1c-1-1-3-2-5-2 1 0 2-1 2-1h3 1l1-1h0c-6-3-12-6-18-7h-1-4c-2-1-4-1-6-1-1 1-2 2-2 4l1 1h0-7l3-3 9-7z"></path><defs><linearGradient id="AL" x1="309.92" y1="200.717" x2="298.434" y2="185.516" xlink:href="#B"><stop offset="0" stop-color="#cfcdcf"></stop><stop offset="1" stop-color="#eaecdf"></stop></linearGradient></defs><path fill="url(#AL)" d="M295 188h0c8-6 17-10 27-12 12-3 28-3 39 4v2h-1l-6-2c-4-1-7-1-11-1l1 1c-22 2-42 13-56 30-14 16-18 39-18 60v17 2h-1c-1-2-1-4-1-7-2-10-1-21-1-31 0-6 1-11 2-17 2-13 7-25 15-36 0-2 1-2 2-3 2-3 6-6 9-7z"></path><path d="M343 179c-5 0-10-1-16 0-2 1-5 1-7 1l13-3c4-1 10-2 14-1l1 1h1c1 1 2 1 3 1l3 1 3 1c1 0 1 0 2 1v1l-6-2c-4-1-7-1-11-1z" class="X"></path><defs><linearGradient id="AM" x1="283.883" y1="230.259" x2="270.617" y2="205.741" xlink:href="#B"><stop offset="0" stop-color="#878986"></stop><stop offset="1" stop-color="#c3bec0"></stop></linearGradient></defs><path fill="url(#AM)" d="M286 195c2-3 6-6 9-7l-1 1c0 1-1 1-2 2v1l-1 1h0l-1 1c1 2 3 2 4 5-5 4-8 8-12 13-1 3-3 6-5 9-3 8-4 18-7 27-1 6-1 13-1 19v9c-1-4-1-7-1-10 0-5 0-10 1-15l-1-1-1 1h0c0-6 1-11 2-17 2-13 7-25 15-36 0-2 1-2 2-3z"></path><path d="M284 198c0-2 1-2 2-3 1 1 3 1 4 3 0 2 0 3-2 4h-1-2v-4h-1z" class="G"></path><defs><linearGradient id="AN" x1="280.468" y1="170.482" x2="257.019" y2="162.26" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#80807e"></stop></linearGradient></defs><path fill="url(#AN)" d="M287 136c8-2 17-1 25 1-3 1-5 0-7 0s-3 0-4 1h-2l-2 1v1c-2 1-3 1-5 2h0c-1 1-3 2-3 4v1h1l-1 1v2c-7 6-16 13-21 21-4 6-6 13-10 19-2-3-5-5-8-8-1-2 1-4 1-6v-1l-3 6v-1c-2-4-1-7-3-10l7-12c2-3 4-5 6-7 5-5 12-9 18-12l11-3z"></path><path d="M289 138h10l-2 1v1c-2 1-3 1-5 2h0c-1 1-3 2-3 4v1h-5c-1 0 0 0-2-1-1 0-3 1-5 1-2 1-4 4-6 5-4 1-7 6-10 9-1 1-1 2-3 2l5-6 8-7c3-3 6-6 10-8 3-1 6-3 8-4z" class="F"></path><path d="M287 136c8-2 17-1 25 1-3 1-5 0-7 0s-3 0-4 1h-2-10c-2 1-5 3-8 4-4 2-7 5-10 8l-8 7-5 6c-2 4-6 8-7 12l-3 6v-1c-2-4-1-7-3-10l7-12c2-3 4-5 6-7 5-5 12-9 18-12l11-3z" class="I"></path><path d="M276 139l11-3h1l-1 1h-1c-2 1-4 1-7 2-3 2-8 5-11 7-1 1-3 3-3 4h6l-8 7h-1c-1-1-1-1-2-1l-5 5c1-4 4-6 7-9h0c0-2 3-4 4-5h-1c-1 0-1 1-2 1-1 1-2 2-4 3h-1c5-5 12-9 18-12z" class="C"></path><path d="M287 136c8-2 17-1 25 1-3 1-5 0-7 0s-3 0-4 1h-2-10c-2 1-5 3-8 4-4 2-7 5-10 8h-6c0-1 2-3 3-4 3-2 8-5 11-7 3-1 5-1 7-2h1l1-1h-1z" class="L"></path><path d="M289 138h1c2-2 7-2 10-2 2 0 3 0 5 1-2 0-3 0-4 1h-2-10z" class="Q"></path><path d="M688 214h9c3 1 6 1 9 2 7 2 12 6 18 9 3 2 6 4 8 6 7 6 13 14 17 23l2 2c4 11 6 23 6 35 0 4-1 9 0 13l-3 15c-5 14-13 29-19 43l-40 85s0-1-1-2h0c0-1 0-1 1-1v-1c2-1 2-5 2-7 1-5 3-8 2-13 1-11 2-24-1-34 0-1 1-2 1-3 0-2-1-4-1-7 0-2-1-4-2-5v-4c-1-1-1-1 0-3-3-4-6-9-9-14h-1l-3-3c-4-3-7-5-11-7l-3-2c-6-2-15-3-22-2-1 0-1 0-2-1l6-13 2-4c0-1 0-2 1-3l2-4 3-8 1-2c2-3 3-7 5-10 1-3 3-7 4-10l2-4 3-8 2-3 1-2 2-3c1-5 3-11 6-15 4 0 8 0 12 1 8 2 15 8 20 14h1c0-1-1-3-1-5-3-3-6-6-10-8-3-1-7-3-10-4s-5-1-7-2h-5v-2l1-5 1-2 1-6c0-1 0-1 1-1l-1-2v-4-1-2-3-3z"></path><path d="M719 295l1-1v-1h1l1 3h1c2 4 2 8 2 12 1 9 2 19 1 29h-1c0-6-1-12-2-18h1v-2h1c0-8-3-15-6-22z" class="G"></path><path d="M710 355c0 1 1 1 1 2l1 4c3 11 4 24 2 36 0 1 0 0-1 1 0-14-1-28-4-41l1-2z" class="L"></path><path d="M696 367c1 2 2 4 3 5 4 10 5 21 6 32v8 4c-1 2 0 4-1 6v-9c0-12-2-23-6-34 0-2-1-4-2-5v-4c-1-1-1-1 0-3z" class="D"></path><path d="M706 361c2 13 2 26 2 39 0 4 0 9-1 14v-3c1-3 0-7 0-10 0-7 0-14-1-20-1-7-3-13-4-20h4z" class="S"></path><path d="M713 305v-2-1l1 1v-1c-1-1-1-3-2-4l2-1c1 2 1 3 2 5 0 1 1 1 2 2-1-2-1-3-1-5l3 10h0c0 2 0 2 1 3 1 4 0 9 0 12v8c1 2 1 6 0 7v1c-2-3 0-5-1-8v-4h-1l-1-2 1-1c-2-6-4-12-8-17h0c0-1 0-1-1-2s-2-2-2-3c1 0 2-1 3-2l2 4z" class="B"></path><path d="M713 305v-2-1l1 1v-1c-1-1-1-3-2-4l2-1c1 2 1 3 2 5 0 1 1 1 2 2v2 1c-1-1-1-2-2-3h0v6l-1 1-2-6z" class="F"></path><path d="M711 301l2 4 2 6c1 4 4 10 4 14-2-6-4-12-8-17h0c0-1 0-1-1-2s-2-2-2-3c1 0 2-1 3-2z" class="V"></path><path d="M687 353c1 1 3 2 5 3h0c1 2 2 3 4 5h4 2 0c1 7 3 13 4 20 1 6 1 13 1 20 0 3 1 7 0 10 0-2 0-3-1-5-1 2 0 4-1 6v-8c-1-11-2-22-6-32-1-1-2-3-3-5-3-4-6-9-9-14z" class="J"></path><path d="M687 353c1 1 3 2 5 3h0c1 2 2 3 4 5h4c0 1-1 2-1 3s1 4 1 5l3 10c-1 0-1 0-1-1l-2-5v-1h-1c-1-1-2-3-3-5-3-4-6-9-9-14z" class="M"></path><path d="M680 302v-1c3-1 7 0 10 1 3-1 6-2 9 0 2 1 3 4 5 6l2-2c1 1 1 2 2 3l1 1h0c1-1 1-2 1-4 1 1 1 1 1 2h0c2 5 6 12 6 17-1 0-1 0-1-1-1-3-1-5-3-8 0 1 4 18 5 20v-6h1c0 7 0 14-1 20v1h-1c0-4 0-7-1-11l-1 2-2-3c0-1-1-3-1-4l-6-12c-2-2-5-5-6-8v-1c-1-2-3-3-4-4v-1h0c-4-4-11-6-16-7z" class="U"></path><path d="M700 307c1 1 4 3 5 6 0 1 0 2-1 3l-7-6h2l1 1 1-1-1-3z" class="I"></path><path d="M680 302v-1c3-1 7 0 10 1 4 1 6 2 10 5l1 3-1 1-1-1h-2l-1-1h0c-4-4-11-6-16-7z" class="T"></path><path d="M696 309l1 1 7 6c5 6 12 16 12 24h0l-1 2-2-3c0-1-1-3-1-4l-6-12c-2-2-5-5-6-8v-1c-1-2-3-3-4-4v-1z" class="Q"></path><path d="M679 264c1-5 3-11 6-15 1 1 2 1 4 2 7 0 15 9 19 14s9 12 11 18c2 4 3 9 4 13h-1l-1-3h-1v1l-1 1c-3-6-6-12-10-18-5-7-11-11-19-14h-3c-2 0-5 0-8 1z" class="H"></path><path d="M709 277v-1c0-1 0-2-1-3s-1-2-1-3c2 2 5 4 7 6 1 2 1 3 2 5l1 1c1 0 1 1 2 1 2 4 3 9 4 13h-1l-1-3h-1v1l-1 1c-3-6-6-12-10-18z" class="S"></path><path d="M679 264c1-5 3-11 6-15 1 1 2 1 4 2h-1l1 1c3 1 4 3 7 5 3 3 5 4 7 8l-3-2c-2-1-6-1-9 0h-1-3c-2 0-5 0-8 1z" class="C"></path><defs><linearGradient id="AO" x1="680.828" y1="287.522" x2="686.971" y2="297.816" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#828180"></stop></linearGradient></defs><path fill="url(#AO)" d="M671 280h6c2 1 3 1 4 2h1l7 1 3 1v1 1h1l2 2c1 2 3 3 5 5 1 2 4 5 5 6s1 0 1 1l1 2 1 1c0 1 1 2 2 3 0 2 0 3-1 4h0l-1-1c-1-1-1-2-2-3l-2 2c-2-2-3-5-5-6-3-2-6-1-9 0-3-1-7-2-10-1v1c5 1 12 3 16 7h0v1c-6-4-11-7-18-7-4-2-12 0-17 1h-1c2-3 3-7 5-10 1-3 3-7 4-10l2-4z"></path><path d="M675 293c2 1 4 3 6 5h-7c-1 0-3-1-5-1h3c2-2 2-2 3-4z" class="Q"></path><defs><linearGradient id="AP" x1="679.45" y1="285.589" x2="684.31" y2="290.661" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#6d6d6a"></stop></linearGradient></defs><path fill="url(#AP)" d="M676 285c1 0 4 1 5 1 2 1 6 2 7 4 0 1 1 1 2 2h0v1c-2-1-3-1-5-2-1-1-2-2-4-2-3-1-3-1-5-4z"></path><path d="M669 284l7 1c2 3 2 3 5 4-3 1-6 0-8 1-2 0-3 0-5-1l-2 4-1 1h0c1-3 3-7 4-10z" class="Q"></path><path d="M665 294h0l1-1 2-4c2 1 3 1 5 1v1l2 2c-1 2-1 2-3 4h-3c2 0 4 1 5 1h0c-1 1-1 1-2 1l-3 1v1c1 1 9 1 11 1 5 1 12 3 16 7h0v1c-6-4-11-7-18-7-4-2-12 0-17 1h-1c2-3 3-7 5-10z" class="R"></path><path d="M669 297h-2 0c0-2 0-5 2-6s2 0 4 0l2 2c-1 2-1 2-3 4h-3z"></path><path d="M671 280h6c2 1 3 1 4 2h1l7 1 3 1v1 1h1l2 2c1 2 3 3 5 5 1 2 4 5 5 6s1 0 1 1l1 2 1 1c0 1 1 2 2 3 0 2 0 3-1 4h0l-1-1c-1-1-1-2-2-3 0-1-1-2-2-2-3-5-10-11-16-14-1-2-5-3-7-4-1 0-4-1-5-1l-7-1 2-4z" class="I"></path><path d="M671 280h6c2 1 3 1 4 2h1l7 1 3 1v1 1h1l2 2c1 2 3 3 5 5-3-2-8-6-11-6s-5-1-8-2v1c-1 0-4-1-5-1l-7-1 2-4z" class="O"></path><defs><linearGradient id="AQ" x1="692.071" y1="261.48" x2="710.153" y2="290.691" xlink:href="#B"><stop offset="0" stop-color="#060505"></stop><stop offset="1" stop-color="#363734"></stop></linearGradient></defs><path fill="url(#AQ)" d="M679 264c3-1 6-1 8-1h3c8 3 14 7 19 14 4 6 7 12 10 18 3 7 6 14 6 22h-1v2h-1c0-4-1-7-3-10l-3-10c0 2 0 3 1 5-1-1-2-1-2-2-1-2-1-3-2-5l-2 1c1 1 1 3 2 4v1l-1-1v1 2l-2-4c-1 1-2 2-3 2l-1-1-1-2c0-1 0 0-1-1s-4-4-5-6c-2-2-4-3-5-5l-2-2h-1v-1-1l-3-1-7-1h-1c-1-1-2-1-4-2h-6l3-8 2-3 1-2 2-3z"></path><path d="M679 264c3-1 6-1 8-1l3 3v1c-2 1-4 1-5 1l-8-1 2-3z"></path><path d="M676 269l1-2 8 1c4 1 9 2 13 5 8 4 14 12 18 19 4 8 8 16 8 25v2h-1c0-4-1-7-3-10l-3-10h0c-1-3-2-6-4-9h0l-1-1c0-1-3-3-4-4-3-4-6-9-11-12l-1 2h-1c0-1 0 0-1 0 0 1 1 1 2 2h-1l-6-3c-1-1-2-2-4-3-1 0-2-1-3-2h-6z" class="D"></path><path d="M682 269c5 1 10 2 15 4l-1 2h-1c0-1 0 0-1 0 0 1 1 1 2 2h-1l-6-3c-1-1-2-2-4-3-1 0-2-1-3-2z" class="L"></path><path d="M676 269h6c1 1 2 2 3 2 2 1 3 2 4 3l6 3-1 1h-1l-1 3-3 2-7-1h-1c-1-1-2-1-4-2h-6l3-8 2-3z" class="Q"></path><path d="M674 272c3 0 5 0 7 1l1 1c-1 2-1 4-3 6h-2-6l3-8z"></path><path d="M697 273c5 3 8 8 11 12 1 1 4 3 4 4l1 1h0c2 3 3 6 4 9h0c0 2 0 3 1 5-1-1-2-1-2-2-1-2-1-3-2-5l-2 1c1 1 1 3 2 4v1l-1-1v1 2l-2-4c-1 1-2 2-3 2l-1-1-1-2c0-1 0 0-1-1s-4-4-5-6c-2-2-4-3-5-5l-2-2h-1v-1-1l-3-1 3-2 1-3h1l1-1h1c-1-1-2-1-2-2 1 0 1-1 1 0h1l1-2z" class="G"></path><path d="M703 284c2 2 3 5 5 7-3 0-5-2-6-3h-1c2-1 2-3 2-4z" class="R"></path><defs><linearGradient id="AR" x1="713.332" y1="297.676" x2="705.145" y2="293.813" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#6e6e6a"></stop></linearGradient></defs><path fill="url(#AR)" d="M702 288c1 1 3 3 6 3 1 0 0 0 1 1h3c0 2 2 4 2 5l-2 1c1 1 1 3 2 4v1l-1-1v1 2l-2-4c-3-6-6-9-11-12l2-1z"></path><path d="M692 285c2 1 5 2 7 3l1 1c5 3 8 6 11 12-1 1-2 2-3 2l-1-1-1-2c0-1 0 0-1-1s-4-4-5-6c-2-2-4-3-5-5l-2-2h-1v-1z" class="K"></path><path d="M695 277h1l6 5 1 2c0 1 0 3-2 4h1l-2 1-1-1c-2-1-5-2-7-3v-1l-3-1 3-2 1-3h1l1-1z" class="M"></path><path d="M692 284h1 2 2c1 1 1 2 2 4-2-1-5-2-7-3v-1z" class="L"></path><path d="M701 288l-1-1c-1 0-1-1-1-2s1-2 3-3l1 2c0 1 0 3-2 4z" class="Q"></path><path d="M688 227l7 2c1 0 2 0 4-1 2 1 4 1 6 2h1c6 3 13 10 17 15 3 3 6 7 8 10l1 2c1 2 2 4 2 7 2 2 3 4 3 7-1 2-1 5-1 8l-1 2c1 4 2 9 2 14 1 14 2 29-2 44 0-1 1-3 1-4v-10c1-18-3-35-12-50-2-4-4-7-6-11 0-1-1-3-1-5-3-3-6-6-10-8-3-1-7-3-10-4s-5-1-7-2h-5v-2l1-5 1-2 1-6c0-1 0-1 1-1l-1-2z" class="C"></path><path d="M727 259l1-1 2 1 2-2c1 2 2 4 2 7 2 2 3 4 3 7-1 2-1 5-1 8l-1 2-2-11c-1-1-2-1-2-2l-4-9z" class="J"></path><path d="M727 259l1-1 2 1 2-2c1 2 2 4 2 7v3c0 1 0 1 1 1-2 0-3-3-4-3 1 2 1 3 2 5-1-1-2-1-2-2l-4-9z" class="Q"></path><path d="M690 235l8 3 2 2c3 2 4 3 6 5l-11-1c-3 0-6-1-9 0l-1-1 1-5 1-2c1 0 2-1 3-1z" class="X"></path><path d="M690 235l8 3 2 2c-4 2-9-1-14-2l1-2c1 0 2-1 3-1z" class="R"></path><defs><linearGradient id="AS" x1="696.443" y1="233.538" x2="700.807" y2="237.723" xlink:href="#B"><stop offset="0" stop-color="#82827e"></stop><stop offset="1" stop-color="#9a9a94"></stop></linearGradient></defs><path fill="url(#AS)" d="M688 230c1 0 1-1 3 0l1 1c9 4 16 9 23 16v1h1c1 3 1 5 0 8-1-1-2-1-3-2h0c0-1-1-1-1-1h-1l-3-3 1-2c-1-2-1-2-2-3h0l-1-1v-1-1l-1-1-1-1h-1c-1-1-3-2-4-3l-1 1-8-3c-1 0-2 1-3 1l1-6z"></path><path d="M688 230c1 0 1-1 3 0l1 1 2 2c-2 2-3 0-4 2-1 0-2 1-3 1l1-6z" class="L"></path><path d="M707 245l1-1c3 1 4 2 6 3 1 2 0 4 0 6l-1 1c0-1-1-1-1-1h-1l-3-3 1-2c-1-2-1-2-2-3z" class="H"></path><defs><linearGradient id="AT" x1="707.694" y1="242.103" x2="710.334" y2="239.056" xlink:href="#B"><stop offset="0" stop-color="#cdccca"></stop><stop offset="1" stop-color="#f2f2ee"></stop></linearGradient></defs><path fill="url(#AT)" d="M688 227l7 2c8 4 17 9 23 16l6 9v1c1 1 1 2 1 3s1 1 1 2v1h0-1l2 4c-1-1-4-3-5-5v-1h1v-2h-1v-2c-2-2-4-6-6-7h-1v-1c-7-7-14-12-23-16l-1-1c-2-1-2 0-3 0 0-1 0-1 1-1l-1-2z"></path><defs><linearGradient id="AU" x1="700.53" y1="228.761" x2="714.13" y2="238.516" xlink:href="#B"><stop offset="0" stop-color="#363533"></stop><stop offset="1" stop-color="#5f5e5c"></stop></linearGradient></defs><path fill="url(#AU)" d="M695 229c1 0 2 0 4-1 2 1 4 1 6 2h1c6 3 13 10 17 15 3 3 6 7 8 10l1 2-2 2-2-1-1 1c0-1-1-2-1-3l-2-2-6-9c-6-7-15-12-23-16z"></path><defs><linearGradient id="AV" x1="728.003" y1="250.261" x2="719.326" y2="248.667" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#7c7b7a"></stop></linearGradient></defs><path fill="url(#AV)" d="M718 245h1l-4-4c1-1 5 4 6 4h2c3 3 6 7 8 10l1 2-2 2-2-1-1 1c0-1-1-2-1-3l-2-2-6-9z"></path><path d="M726 256l1-1v-1c1 0 2 1 4 1l1 2-2 2-2-1-1 1c0-1-1-2-1-3z" class="Z"></path><path d="M688 214h9c3 1 6 1 9 2 7 2 12 6 18 9 3 2 6 4 8 6l-1-1c-3-1-5-3-8-4 0 1 0 1 1 2h1l3 3c2 1 3 3 4 5h0c9 12 14 24 16 39 1 9 0 18-1 27 0 4 0 7-1 10-1 1-1 1-2 0-2-3-1-8-1-11 0-4-1-8-1-12-1-2-1-5-2-7 0-4-1-7-3-11 0-3-1-5-3-7 0-3-1-5-2-7l-1-2c-2-3-5-7-8-10-4-5-11-12-17-15h-1c-2-1-4-1-6-2-2 1-3 1-4 1l-7-2v-4-1-2-3-3z" class="V"></path><defs><linearGradient id="AW" x1="689.963" y1="219.407" x2="702.489" y2="224.707" xlink:href="#B"><stop offset="0" stop-color="#161515"></stop><stop offset="1" stop-color="#2f2f2d"></stop></linearGradient></defs><path fill="url(#AW)" d="M688 220c6-1 13 1 18 4 0 1 0 1-1 2v1c-4-2-9-4-13-4l-4-1v-2z"></path><defs><linearGradient id="AX" x1="717.176" y1="238.491" x2="720.955" y2="234.949" xlink:href="#B"><stop offset="0" stop-color="#2b2a27"></stop><stop offset="1" stop-color="#53534e"></stop></linearGradient></defs><path fill="url(#AX)" d="M706 224c9 5 16 10 22 18 2 1 4 3 5 5v1c0 1 1 2 1 3l-1 1c-7-11-16-19-28-25v-1c1-1 1-1 1-2z"></path><path d="M736 252v-3c-2-3-3-4-3-7h0l-1-1-2-3 1-1c3 4 6 8 8 12l2 3 2 6h0c1 1 1 2 1 3 1 3 2 6 2 10l1 1-1 1c-1-2 0-3-1-4v-2c-1 0-2-1-3-2l-6-13z" class="D"></path><defs><linearGradient id="AY" x1="743.371" y1="265.538" x2="735.185" y2="265.987" xlink:href="#B"><stop offset="0" stop-color="#1a1817"></stop><stop offset="1" stop-color="#353636"></stop></linearGradient></defs><path fill="url(#AY)" d="M733 252l1-1c0-1-1-2-1-3v-1l3 5 6 13c4 13 5 23 4 36 0 3 0 7-1 10 0-7 1-14 0-20 0-3-1-7-2-10-2-10-5-20-10-29z"></path><defs><linearGradient id="AZ" x1="700.217" y1="226.094" x2="711.033" y2="214.642" xlink:href="#B"><stop offset="0" stop-color="#101111"></stop><stop offset="1" stop-color="#393833"></stop></linearGradient></defs><path fill="url(#AZ)" d="M688 214h9c3 1 6 1 9 2 7 2 12 6 18 9 3 2 6 4 8 6l-1-1c-3-1-5-3-8-4 0 1 0 1 1 2h1l3 3c2 1 3 3 4 5h0c-8-8-19-16-31-18-2 0-4-1-6-1s-5 1-7 0v-3z"></path><defs><linearGradient id="Aa" x1="727.525" y1="288.08" x2="728.975" y2="232.92" xlink:href="#B"><stop offset="0" stop-color="#a1a3a3"></stop><stop offset="1" stop-color="#d3d0cc"></stop></linearGradient></defs><path fill="url(#Aa)" d="M688 222l4 1c4 0 9 2 13 4 12 6 21 14 28 25 5 9 8 19 10 29 1 3 2 7 2 10-1 1-1 2-1 3-1-1-1-3-1-4v-1h-1c-1-2-1-5-2-7 0-4-1-7-3-11 0-3-1-5-3-7 0-3-1-5-2-7l-1-2c-2-3-5-7-8-10-4-5-11-12-17-15h-1c-2-1-4-1-6-2-2 1-3 1-4 1l-7-2v-4-1z"></path><path d="M688 223c7 1 12 3 18 7h-1c-2-1-4-1-6-2-2 1-3 1-4 1l-7-2v-4z" class="E"></path><path d="M660 304h1c5-1 13-3 17-1 7 0 12 3 18 7 1 1 3 2 4 4v1c1 3 4 6 6 8l6 12c0 1 1 3 1 4l2 3 1-2c1 4 1 7 1 11h1c0 3-1 5-3 6l-1 1-1-1v-1c-1 2-1 3-1 5l-1-4c0-1-1-1-1-2l-1 2-2-5h-1l-1-2-2-4c-1-1-1-2-1-2l-1-1-3-3c0 2 1 3 2 5 2 2 3 5 4 7 0 1 1 1 1 2v1 3l1 3h-4 0-2-4c-2-2-3-3-4-5h0c-2-1-4-2-5-3h-1l-3-3c-4-3-7-5-11-7l-3-2c-6-2-15-3-22-2-1 0-1 0-2-1l6-13 2-4c0-1 0-2 1-3l2-4 3-8 1-2z" class="F"></path><path d="M678 320h2l1-1h2l1 1h1 1l1-1c2 1 4 3 7 3l3 3 4 9h0c-1-1-2-1-2-2l-1 1 1 2c1 1 0 3 1 3l-1 1c-5-6-11-11-18-15-1-1-1-3-3-4z" class="H"></path><path d="M687 319c2 1 4 3 7 3l3 3 4 9h0c-1-1-2-1-2-2h0c-3-2-4-4-6-6-3-2-5-3-7-6l1-1z" class="I"></path><defs><linearGradient id="Ab" x1="666.418" y1="315.705" x2="679.374" y2="319.889" xlink:href="#B"><stop offset="0" stop-color="#b5b5b4"></stop><stop offset="1" stop-color="#d5d5d0"></stop></linearGradient></defs><path fill="url(#Ab)" d="M654 318c6-2 13-2 20-2 4 1 8 2 13 3l-1 1h-1-1l-1-1h-2l-1 1h-2c2 1 2 3 3 4l-6-2c-3 2-7 2-10 1h-1-4-6c-1 1-2 2-3 2l2-4c0-1 0-2 1-3z"></path><path d="M666 320c2-1 3-1 5-1l7 1c2 1 2 3 3 4l-6-2c-3-1-6-1-9-2z" class="C"></path><path d="M653 321c4-1 8-1 13-1 3 1 6 1 9 2-3 2-7 2-10 1h-1-4-6c-1 1-2 2-3 2l2-4z" class="E"></path><path d="M698 325c3 1 5 3 7 6h1c0-2-2-3-3-6l6 7 3 6c0 1 0 1 1 2v-1l2 3 1-2c1 4 1 7 1 11h1c0 3-1 5-3 6l-1 1-1-1v-1c-1 2-1 3-1 5l-1-4c0-1-1-1-1-2l-1 2-2-5c0-4-6-10-8-13l1-1c-1 0 0-2-1-3l-1-2 1-1c0 1 1 1 2 2h0l-4-9h1z" class="Z"></path><path d="M699 339l1-1c-1 0 0-2-1-3l-1-2 1-1c0 1 1 1 2 2h0c5 6 6 14 9 21l-1 2-2-5c0-4-6-10-8-13z" class="S"></path><path d="M705 331h1c0-2-2-3-3-6l6 7 3 6c0 1 0 1 1 2v-1l2 3 1-2c1 4 1 7 1 11h1c0 3-1 5-3 6l-1 1-1-1v-1c-1 2-1 3-1 5l-1-4v-8c-1-1-1-3-1-4-1-2-2-3-2-5l1-1c-2-3-3-5-4-8z" class="M"></path><path d="M711 357v-8c-1-1-1-3-1-4-1-2-2-3-2-5l1-1c1 3 3 6 4 9 0 3 0 6 1 9 1-5 1-10 1-15l1-2c1 4 1 7 1 11h1c0 3-1 5-3 6l-1 1-1-1v-1c-1 2-1 3-1 5l-1-4z" class="F"></path><path d="M678 303c7 0 12 3 18 7 1 1 3 2 4 4v1c1 3 4 6 6 8l6 12c0 1 1 3 1 4v1c-1-1-1-1-1-2l-3-6-6-7c1 3 3 4 3 6h-1c-2-3-4-5-7-6h-1l-3-3c-3 0-5-2-7-3-5-1-9-2-13-3v-1h1c2-1 5 0 7 0 0-1 1-2 1-2l2-2v-1c1 0 1 0 2-1h-1l-1-1v-1c-1-2-4-2-6-3l-1-1z" class="H"></path><path d="M674 316v-1h1c2-1 5 0 7 0l5 2c4 1 7 4 11 7v1h-1l-3-3c-3 0-5-2-7-3-5-1-9-2-13-3z" class="L"></path><path d="M698 321h0c-1 0-2-2-2-3s-3-3-4-4l1-1v-1l2 2c0 1 0 0 1 1 1 2 1 3 3 5l1-1h0c-1-2-2-3-3-4s-1-1-1-2l3 1h1v1c1 3 4 6 6 8l6 12c0 1 1 3 1 4v1c-1-1-1-1-1-2l-3-6-6-7c1 3 3 4 3 6h-1c-2-3-4-5-7-6v-1c1 0 1 0 1-1l-1-2z" class="V"></path><path d="M698 321h1c2 2 4 4 7 5 2 1 2 4 3 6l-6-7c1 3 3 4 3 6h-1c-2-3-4-5-7-6v-1c1 0 1 0 1-1l-1-2z" class="G"></path><defs><linearGradient id="Ac" x1="662.031" y1="309.121" x2="677.693" y2="313.577" xlink:href="#B"><stop offset="0" stop-color="#4b4949"></stop><stop offset="1" stop-color="#7e7e7d"></stop></linearGradient></defs><path fill="url(#Ac)" d="M660 304h1c5-1 13-3 17-1l1 1c2 1 5 1 6 3v1l1 1h1c-1 1-1 1-2 1v1l-2 2s-1 1-1 2c-2 0-5-1-7 0h-1v1c-7 0-14 0-20 2l2-4 3-8 1-2z"></path><path d="M659 306l4-1 1 1c0 2-2 6-3 7s-2 1-3 2l-2-1 3-8z"></path><path d="M660 304h1c5-1 13-3 17-1l1 1c2 1 5 1 6 3v1l1 1h1c-1 1-1 1-2 1v1l-2 2c0-2 1-4 0-5-1 0-1 0-1-1-1 0-2-1-4-2-3-1-12-1-15 0l-4 1 1-2z" class="D"></path><defs><linearGradient id="Ad" x1="666.1" y1="329.35" x2="672.967" y2="343.911" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#Ad)" d="M651 325c1 0 2-1 3-2h6l14 4v1h0l1 1 6 1h3c2 1 5 4 5 6l7 6c4 4 7 8 8 13l1 3 1 3h-4 0-2-4c-2-2-3-3-4-5h0c-2-1-4-2-5-3h-1l-3-3c-4-3-7-5-11-7l-3-2c-6-2-15-3-22-2-1 0-1 0-2-1l6-13z"></path><path d="M675 329l6 1 1 2-1 1c-1 0-1 1-2 0-2-1-3-3-4-4z" class="N"></path><path d="M651 325c1 0 2-1 3-2h6l14 4v1h0c-6 1-12 3-17 6-4 1-7 2-10 5-1 0-1 0-2-1l6-13z"></path><path d="M689 336l7 6c4 4 7 8 8 13l1 3 1 3h-4 0-2-4c-2-2-3-3-4-5h0c-2-1-4-2-5-3h-1l-3-3c-4-3-7-5-11-7l-3-2c5-1 12 0 17 1 2 0 4 1 5 1l-4-3c0-2 1-3 2-4z" class="G"></path><path d="M683 350l-1-1-3-4c3 1 6 2 9 4 2 1 4 5 6 6l2 1h-4 0c-2-1-4-2-5-3h-1l-3-3z" class="S"></path><path d="M689 336l7 6c0 2 1 3 1 5 1 1 1 3 2 4v3l-1 1c-1-5-3-9-7-12l-4-3c0-2 1-3 2-4z" class="K"></path><path d="M696 342c4 4 7 8 8 13l1 3 1 3h-4 0-2-4c-2-2-3-3-4-5h4 2v-1l1-1v-3c-1-1-1-3-2-4 0-2-1-3-1-5z" class="V"></path><path d="M704 355l1 3 1 3h-4 0l2-6z" class="C"></path><defs><linearGradient id="Ae" x1="289.047" y1="336.023" x2="378.595" y2="312.073" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232221"></stop></linearGradient></defs><path fill="url(#Ae)" d="M344 180c6 0 11 1 17 3 0 2-1 4-2 5l-2 5v2l-2 10-1 7v1 3 3 2l-1 1v1h1v4l-2 1h1l1 1 1 1v3l1 7 1 2 1 6v1c2 4 3 9 4 13-4 0-8-1-11 1h0l-1 1-1 1h1c1 0 2 1 3 2 2 0 3 0 5 1h3c1 0 3 1 5 0v1 1 2h0c0 1-1 1-1 2 0 2 1 2 3 3l2-1c0 2 1 4 2 6l1 3c3 4 4 9 6 14l3 7 5 11v2l-1 1c-7-2-17 0-24 2-1 0-3 1-4 1-4 2-8 4-12 7-4 4-9 8-13 13h5 1 0l-2 3c1 2 1 3 3 3l2-2c0 1-1 5-1 5-1 4-2 7-2 10l3-3h0c1 1 1 3 1 4l-1 5-3 5c-1 3-3 7-3 10-1 3-1 5-1 7-1 2-1 4-2 6v1l1 24c0 6 1 12 2 17l-1 1-49-112c-1-2-2-3-3-5-5-11-10-21-13-33v-2-17c0-21 4-44 18-60 14-17 34-28 56-30z"></path><path d="M319 328h1v2c2-1 1-4 2-5 1-4 3-8 4-12 1-2 3-3 4-5l2 2-1 2c-2 1-3 2-4 4 0 2-2 2-2 4-2 4-2 8-4 11s-2 6-2 9l-1 5c0 2 0 5-1 8 0-3 1-6 1-8l1-17z" class="C"></path><path d="M338 333c3 0 7-5 10-6 0 1-2 2-3 3h1c-4 4-9 8-13 13-3 6-7 12-9 18l-3 9c0-2 1-4 1-6 1-3 2-6 3-8 2-3 4-10 6-13l3-3c0-3 2-5 4-7z" class="D"></path><path d="M303 284l1 2c0 1 1 3 0 4s-1 2-1 3v5c1-1 1-2 1-2v2c1 2 0 4 0 5-1 3-1 7-2 10 0 8 1 15 1 23-2-4-2-9-2-14-1-13 0-25 2-38zm-5-11h1c0 2-1 4-1 6h0c2-4 3-9 5-13l1 1v1c-2 2-2 5-3 8-4 9-6 18-6 27-1 1 0 1-1 1-1-2 0-3-1-5-1-3 0-5 0-9h0c1-6 3-12 5-17z" class="S"></path><path d="M332 303v1l-2 4c-1 2-3 3-4 5-1 4-3 8-4 12-1 1 0 4-2 5v-2h-1c1-7 3-14 5-21 1-1 1-2 2-3h1 1l-1 2h1l4-3zm-51-30h1c0 2-1 2 0 4v1c0 10-1 20 0 30 1 6 3 12 4 19-1-2-2-3-3-5 1-4-2-9-3-13 0-5-1-10-1-14 1-2 1-4 1-6v-10l1-6z" class="G"></path><path d="M357 242l1 6c-8 1-14 2-21 5l-2-1v1h-1l-1 1c0-1 0-1 1-1 1-1 2-1 2-2l1-1 4-3c5-2 10-4 16-5z" class="N"></path><path d="M317 292h1c0-1 1-2 1-3 1-2 2-3 2-4 0-2 1-3 2-4l1 1c-2 5-5 10-6 14l1 2c-2 7-4 14-5 22-1 3-1 7-1 10v1l1 2v9c-3-10-2-19-1-29 0-7 1-15 4-21z" class="S"></path><path d="M339 362l3-3h0c1 1 1 3 1 4l-1 5-3 5c-1 3-3 7-3 10-1 3-1 5-1 7-1 2-1 4-2 6v1c0-8 0-15 2-23 0-3 2-7 1-11h3v-1z" class="M"></path><path d="M339 362l3-3h0c1 1 1 3 1 4l-1 5-3 5c0-2 0-4 1-6 0-2 1-2 0-4h-1v-1z" class="O"></path><path d="M291 269c1 1 1 1 1 3l-1 2 1 1c1-1 1-2 1-3l1-1v1c-1 6-1 12-1 18h0c0 4-1 6 0 9 1 2 0 3 1 5 1 0 0 0 1-1 0 3 1 9 0 11-2 0-2 0-3-1-2-14-4-30-1-44z" class="G"></path><defs><linearGradient id="Af" x1="317.383" y1="227.983" x2="314.635" y2="219.535" xlink:href="#B"><stop offset="0" stop-color="#c2bfbf"></stop><stop offset="1" stop-color="#deded8"></stop></linearGradient></defs><path fill="url(#Af)" d="M309 225c1 0 4-3 5-3 4-3 8-5 12-7 3 0 4 0 6 2-16 6-29 16-39 30l-1-1h1 0c1-2 3-4 4-6 1-1 3-3 3-4 0-2 1-3 2-4l7-7z"></path><path d="M302 228c0 1 0 1 1 1 2-2 3-3 6-4l-7 7c-1 1-2 2-2 4 0 1-2 3-3 4-1 2-3 4-4 6h0-1l1 1c-3 5-5 10-7 15l-4 15c-1-2 0-2 0-4h-1v-1-2-1c1-1 1-2 1-3 1-2 1-3 2-5l3-6c1-4 3-7 5-10h0c1-1 1-1 1-2l-2 1c1-3 3-5 4-8s4-6 7-8z" class="S"></path><path d="M337 250l-1 1c0 1-1 1-2 2-1 0-1 0-1 1l1-1h1v-1l2 1c-17 11-28 25-33 45v-2s0 1-1 2v-5c0-1 0-2 1-3s0-3 0-4l1-3c0-2 1-4 2-5 0-1 1-2 2-2h1c3-6 6-11 10-16 1 0 1 0 1 1h1c1-3 3-5 7-7 2 0 5-3 8-4z" class="D"></path><path d="M304 286l1-3c0-2 1-4 2-5 0-1 1-2 2-2h1l-6 20s0 1-1 2v-5c0-1 0-2 1-3s0-3 0-4z" class="X"></path><path d="M333 210c3-1 6-1 9-1-1 1-2 1-4 1l1 1 4-1c2 2 2 2 5 2h6v1c-7 0-15 1-22 4-2-2-3-2-6-2-4 2-8 4-12 7-1 0-4 3-5 3-3 1-4 2-6 4-1 0-1 0-1-1v-1c2-2 5-5 8-6 7-6 14-10 23-11z" class="C"></path><defs><linearGradient id="Ag" x1="330.471" y1="371.971" x2="336.356" y2="347.346" xlink:href="#B"><stop offset="0" stop-color="#aaa9a6"></stop><stop offset="1" stop-color="#dddbd9"></stop></linearGradient></defs><path fill="url(#Ag)" d="M333 365c0 1-1 7-2 8-2 7-1 15-2 22l-1 1-1-1c-2-17 0-35 10-49 1 2 1 3 3 3l2-2c0 1-1 5-1 5-1 4-2 7-2 10v1h-3-2l-1 2z"></path><path d="M340 349l2-2c0 1-1 5-1 5l-2 2c0-1 0-1-1-1v-1s1-1 1-2l1-1z" class="N"></path><path d="M341 352c-1 4-2 7-2 10v1h-3-2l-1 2h-1c0-2 0-3 1-4 1-2 1-2 3-2 2-2 2-2 3-5l2-2z" class="I"></path><defs><linearGradient id="Ah" x1="309.134" y1="275.891" x2="317.757" y2="250.41" xlink:href="#B"><stop offset="0" stop-color="#939391"></stop><stop offset="1" stop-color="#b9b7b4"></stop></linearGradient></defs><path fill="url(#Ah)" d="M317 248h1v1c1 0 1 0 2-1h1 0v4h2v5l-2 2-1 1c-4 5-7 10-10 16h-1c-1 0-2 1-2 2-1 1-2 3-2 5l-1 3-1-2 4-12c-1-1-1-1-1-2l1-1v-1l1-1v-2h1v-1h0c0-2 0-2-1-3-1 2-2 5-3 7h-1v-1c2-2 3-6 4-8 3-4 6-8 9-11z"></path><defs><linearGradient id="Ai" x1="312.774" y1="262.75" x2="320.726" y2="259.75" xlink:href="#B"><stop offset="0" stop-color="#868482"></stop><stop offset="1" stop-color="#9d9e9b"></stop></linearGradient></defs><path fill="url(#Ai)" d="M309 276c2-6 7-18 13-23v1l-1 5-1 1c-4 5-7 10-10 16h-1z"></path><defs><linearGradient id="Aj" x1="308.195" y1="260.749" x2="318.283" y2="250.231" xlink:href="#B"><stop offset="0" stop-color="#2c2b2a"></stop><stop offset="1" stop-color="#646463"></stop></linearGradient></defs><path fill="url(#Aj)" d="M317 248h1v1c1 0 1 0 2-1h1c-6 8-11 15-14 24-1-1-1-1-1-2l1-1v-1l1-1v-2h1v-1h0c0-2 0-2-1-3-1 2-2 5-3 7h-1v-1c2-2 3-6 4-8 3-4 6-8 9-11z"></path><path d="M352 193c1 0 4-1 5 0v2l-2 10-1 7h-6c-3 0-3 0-5-2l-4 1-1-1c2 0 3 0 4-1-3 0-6 0-9 1 0-3-1-3-3-5h-3c0-1 0-2 1-3s0-1 1-2h0l10-5c3-1 6-2 9-2h4z" class="Q"></path><path d="M352 193c1 0 4-1 5 0v2l-2 10h-2c-1 0-5 0-5-1-2-1-2-4-2-6-1-2-5-1-7-3 3-1 6-2 9-2h4z"></path><path d="M352 193c1 0 4-1 5 0v2l-2 10h-2c0-3 1-6 2-8-1-1-4-1-6-1s-2 1-3 2c-1-2-5-1-7-3 3-1 6-2 9-2h4z" class="Y"></path><path d="M352 193c1 0 4-1 5 0v2h-1-8c0-1 0-1-1-1h-1 1l1-1h4z" class="B"></path><defs><linearGradient id="Ak" x1="317.368" y1="250.776" x2="309.409" y2="229.271" xlink:href="#B"><stop offset="0" stop-color="#9e9ea2"></stop><stop offset="1" stop-color="#bdbbb5"></stop></linearGradient></defs><path fill="url(#Ak)" d="M354 216v3c-2 0-4 0-7 1-1 0-3 1-5 1-17 7-34 19-43 37-2 5-3 9-5 14v-1l-1 1c0 1 0 2-1 3l-1-1 1-2c0-2 0-2-1-3 1-8 4-16 9-23 9-13 21-24 38-27 4-2 11-2 16-3z"></path><path d="M354 216v3c-2 0-4 0-7 1-1 0-3 1-5 1-2-1-5 1-8 1 0 0-1 0-1 1h-2l-1-1c2-1 5-1 8-2v-1c4-2 11-2 16-3z" class="S"></path><defs><linearGradient id="Al" x1="343.305" y1="263.057" x2="333.173" y2="299.521" xlink:href="#B"><stop offset="0" stop-color="#080607"></stop><stop offset="1" stop-color="#3e413c"></stop></linearGradient></defs><path fill="url(#Al)" d="M319 298c2-7 6-13 10-19 5-7 12-14 21-15l-1 1h1c1 0 2 1 3 2 2 0 3 0 5 1h3c1 0 3 1 5 0v1 1c-10-1-19 2-27 8v1c-3 2-7 6-9 10-2 3-5 7-7 11 0 0-1 2-1 3-4 7-6 14-7 23 0 2 0 5-1 7l-1-2v-1c0-3 0-7 1-10 1-8 3-15 5-22z"></path><path d="M366 268v1 1c-10-1-19 2-27 8v1c-3 2-7 6-9 10-2 3-5 7-7 11 0 0-1 2-1 3-4 7-6 14-7 23h-1c1-15 6-30 15-41 8-10 19-15 32-17 1 0 3 1 5 0z" class="D"></path><defs><linearGradient id="Am" x1="356.32" y1="272.216" x2="328.593" y2="262.323" xlink:href="#B"><stop offset="0" stop-color="#878785"></stop><stop offset="1" stop-color="#bfbdbc"></stop></linearGradient></defs><path fill="url(#Am)" d="M358 249c2 4 3 9 4 13-4 0-8-1-11 1h0l-1 1c-9 1-16 8-21 15-4 6-8 12-10 19l-1-2c1-4 4-9 6-14l-1-1c-1 1-2 2-2 4 0 1-1 2-2 4 0 1-1 2-1 3h-1c2-11 11-25 19-32l1-1c5-6 12-9 20-9l1-1z"></path><path d="M336 260l1-1c5-6 12-9 20-9l1 1c-2 1-6 2-8 2-2 1-3 1-4 2h-1l-6 6c0-2 3-3 2-4-2 1-3 2-4 3h-1z" class="C"></path><defs><linearGradient id="An" x1="323.44" y1="255.764" x2="311.229" y2="241.518" xlink:href="#B"><stop offset="0" stop-color="#0a0b0a"></stop><stop offset="1" stop-color="#2f2e2c"></stop></linearGradient></defs><path fill="url(#An)" d="M342 221c2 0 4-1 5-1 3-1 5-1 7-1v2l-1 1v1c-10 3-19 7-26 15-3 2-7 6-10 10-3 3-6 7-9 11-1 2-2 6-4 8l-1-1c-2 4-3 9-5 13h0c0-2 1-4 1-6h-1c-2 5-4 11-5 17 0-6 0-12 1-18 2-5 3-9 5-14 9-18 26-30 43-37z"></path><defs><linearGradient id="Ao" x1="320.918" y1="246.931" x2="316.248" y2="242.171" xlink:href="#B"><stop offset="0" stop-color="#aaaaa9"></stop><stop offset="1" stop-color="#cbcac7"></stop></linearGradient></defs><path fill="url(#Ao)" d="M298 273c2-9 7-20 13-26 9-11 19-18 33-22 2-1 5-3 9-3v1c-10 3-19 7-26 15-3 2-7 6-10 10-3 3-6 7-9 11-1 2-2 6-4 8l-1-1c-2 4-3 9-5 13h0c0-2 1-4 1-6h-1z"></path><path d="M353 223h1v4l-2 1h1l1 1 1 1v3l1 7 1 2c-6 1-11 3-16 5l-4 3c-3 1-6 4-8 4-4 2-6 4-7 7h-1c0-1 0-1-1-1l1-1 2-2v-5h-2v-4h0-1c-1 1-1 1-2 1v-1h-1c3-4 7-8 10-10 7-8 16-12 26-15z" class="K"></path><path d="M332 238c1-1 3-3 5-4v1c-1 1-2 3-4 3v1c-3 4-7 6-9 10v1c1 2 0 5-1 7v-5h-2v-4c3-4 7-7 11-10z" class="N"></path><path d="M327 238h1c1-1 2-1 4-2h0v1 1c-4 3-8 6-11 10h0-1c-1 1-1 1-2 1v-1h-1c3-4 7-8 10-10z" class="L"></path><defs><linearGradient id="Ap" x1="345.17" y1="222.046" x2="341.612" y2="235.396" xlink:href="#B"><stop offset="0" stop-color="#0a0608"></stop><stop offset="1" stop-color="#333633"></stop></linearGradient></defs><path fill="url(#Ap)" d="M353 223h1v4l-2 1c-2 0-4 2-6 2-3 1-6 3-9 5v-1c-2 1-4 3-5 4v-1-1h0c-2 1-3 1-4 2h-1c7-8 16-12 26-15z"></path><defs><linearGradient id="Aq" x1="353.297" y1="237.924" x2="338.837" y2="235.331" xlink:href="#B"><stop offset="0" stop-color="#646362"></stop><stop offset="1" stop-color="#858582"></stop></linearGradient></defs><path fill="url(#Aq)" d="M329 254c2-2 6-5 8-8v-1c-4-2-4 1-6 1 5-7 16-14 24-16v3l1 7 1 2c-6 1-11 3-16 5l-4 3c-3 1-6 4-8 4z"></path><path d="M355 233l1 7-3 1v-2l1-1h-1-4v-1c1-1 2-1 3-1 2-1 2-1 3-2v-1z" class="M"></path><path d="M356 240l1 2c-6 1-11 3-16 5 1-1 1-2 2-2l6-3h0c2 0 3-1 4-1l3-1z" class="Q"></path><defs><linearGradient id="Ar" x1="356.954" y1="286.111" x2="333.944" y2="278.989" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#7c7b79"></stop></linearGradient></defs><path fill="url(#Ar)" d="M339 279v-1c8-6 17-9 27-8v2h0c0 1-1 1-1 2 0 2 1 2 3 3l2-1c0 2 1 4 2 6l1 3c3 4 4 9 6 14h-1c-2 0-6 0-8-1h-4l-2-2c0-2 0-2 1-3-1-2-3-2-4-3-2-1-4 0-5 0-2 0-3 1-5 1h0c-8 5-13 12-19 19l-2-2 2-4v-1l-4 3h-1l1-2h-1-1c-1 1-1 2-2 3 0-1 0-3-2-4 0-1 1-3 1-3 2-4 5-8 7-11 2-4 6-8 9-10z"></path><path d="M339 279v-1c8-6 17-9 27-8v2h0c0 1-1 1-1 2 0 2 1 2 3 3l2-1c0 2 1 4 2 6l-2-1-13 1 3-2v-1l-5-7h0l-9 3c-2 1-4 4-6 4h-1z" class="B"></path><path d="M368 277l2-1c0 2 1 4 2 6l-2-1h-3c0-2-2-3-4-4 2 1 3 1 5 0z" class="J"></path><path d="M363 277c-1 0-3-1-4-2s-1-1-1-3c3-2 5-1 8 0h0c0 1-1 1-1 2 0 2 1 2 3 3-2 1-3 1-5 0z"></path><path d="M372 282l1 3c3 4 4 9 6 14h-1c-2 0-6 0-8-1h-4l-2-2c0-2 0-2 1-3-1-2-3-2-4-3-2-1-4 0-5 0-2 0-3 1-5 1h0l4-2-1-1c-2 1-4 2-6 1 0-2 1-2 1-4v-1c2 0 5-1 8-2l13-1 2 1z" class="Q"></path><path d="M365 293c2-2 3-2 5-2 2 1 2 2 2 4v2l-2 1h-4l-2-2c0-2 0-2 1-3z"></path><path d="M372 282l1 3c-4 0-7 0-10 1s-5 2-8 3l-1-1c-2 1-4 2-6 1 0-2 1-2 1-4v-1c2 0 5-1 8-2l13-1 2 1z" class="O"></path><path d="M330 289h1c1 0 1-1 2-2s3-4 5-4v-1h1c3 0 2 3 4 4 1 0 4-2 6-2v1c0 2-1 2-1 4 2 1 4 0 6-1l1 1-4 2c-8 5-13 12-19 19l-2-2 2-4v-1l-4 3h-1l1-2h-1-1c-1 1-1 2-2 3 0-1 0-3-2-4 0-1 1-3 1-3 2-4 5-8 7-11z" class="M"></path><path d="M340 289l2 1 1 1c-2 3-6 6-9 9-1 1-2 2-2 3l-4 3h-1l1-2h-1-1c4-6 8-11 14-15z" class="I"></path><path d="M349 285c0 2-1 2-1 4 2 1 4 0 6-1l1 1-4 2c-8 5-13 12-19 19l-2-2 2-4v-1c0-1 1-2 2-3 3-3 7-6 9-9l-1-1-2-1c1-1 7-4 9-4z" class="K"></path><defs><linearGradient id="As" x1="317.99" y1="334.533" x2="342.51" y2="323.967" xlink:href="#B"><stop offset="0" stop-color="#676564"></stop><stop offset="1" stop-color="#989895"></stop></linearGradient></defs><path fill="url(#As)" d="M351 291h0c2 0 3-1 5-1 1 0 3-1 5 0 1 1 3 1 4 3-1 1-1 1-1 3l2 2h4c2 1 6 1 8 1h1l3 7 5 11v2l-1 1c-7-2-17 0-24 2-1 0-3 1-4 1-4 2-8 4-12 7h-1c1-1 3-2 3-3-3 1-7 6-10 6-2 2-4 4-4 7l-3 3c-2 3-4 10-6 13h-3l-1-2v-5-5c-1 0 0 2-1 3v-1-1c0-2 0-3-1-5 0-3 0-6 2-9s2-7 4-11c0-2 2-2 2-4 1-2 2-3 4-4l1-2c6-7 11-14 19-19z"></path><path d="M334 313l1-2c1 2 0 3 0 4l-3 3c-2 2-3 4-5 6-2 3-3 6-4 9l-3 6v-1c0-2 1-5 2-7 3-6 7-13 12-18z" class="D"></path><path d="M330 337h2l1 1c-1 2-2 3-2 5-2 3-4 10-6 13h-3l1-1c0-3 1-7 2-10 1-2 3-4 4-6v-1l1-1z" class="Y"></path><path d="M344 304c2-1 5-2 7-3 1 1 1 2 2 2l2 1c-2 0-3 1-4 1l6-1 1 1-1 2-7 6c-3 1-4 2-5 5-3 1-5 3-7 5-1 0-2 0-3 1h-2c1-1 1-1 2-3s3-6 5-6c1-4 5-5 6-8-1 1-2 2-4 3l-2-1c0-2 3-3 4-5z" class="I"></path><path d="M346 307l2-1 1 1c0 2-1 2-2 4-2 1-4 3-7 4 1-4 5-5 6-8z" class="K"></path><defs><linearGradient id="At" x1="364.637" y1="298.322" x2="331.558" y2="301.655" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#989793"></stop></linearGradient></defs><path fill="url(#At)" d="M351 291h0c2 0 3-1 5-1 1 0 3-1 5 0 1 1 3 1 4 3-1 1-1 1-1 3l2 2c-2 1-3 1-5 2h1v1c-3 0-7-1-11 0-2 1-5 2-7 3-1 2-4 3-4 5l2 1-7 5c0-1 1-2 0-4l-1 2-3-1 1-2c6-7 11-14 19-19z"></path><path d="M335 311h0c2-3 5-6 9-7-1 2-4 3-4 5l2 1-7 5c0-1 1-2 0-4z" class="N"></path><path d="M350 313c3 0 6 0 9 2h5v1h18 0l5 1v2l-1 1c-7-2-17 0-24 2-1 0-3 1-4 1-4 2-8 4-12 7h-1c1-1 3-2 3-3-3 1-7 6-10 6-2 2-4 4-4 7l-3 3c0-2 1-3 2-5l-1-1h-2l-1 1h-1c3-8 11-14 17-19v-1c1-3 2-4 5-5z" class="U"></path><path d="M359 315h5v1c-6 1-10 2-14 6l-4 3-3-2c5-3 10-6 16-8z" class="M"></path><path d="M343 323l3 2a30.44 30.44 0 0 0-8 8c-2 2-4 4-4 7l-3 3c0-2 1-3 2-5l-1-1h-2c3-5 8-10 13-14z" class="L"></path><path d="M364 316h18 0l5 1v2l-1 1c-7-2-17 0-24 2-1 0-3 1-4 1-4 2-8 4-12 7h-1c1-1 3-2 3-3-3 1-7 6-10 6a30.44 30.44 0 0 1 8-8l4-3c4-4 8-5 14-6z" class="C"></path><path d="M350 322h1c1 0 3-1 4-1h1c-2 3-6 3-8 6-3 1-7 6-10 6a30.44 30.44 0 0 1 8-8l4-3z" class="N"></path><path d="M382 316l5 1v2l-1 1c-7-2-17 0-24 2l-2-1c-1 1-2 1-3 1 2-2 12-5 15-5s8 1 10-1h0z" class="G"></path><defs><linearGradient id="Au" x1="373.478" y1="307.677" x2="361.539" y2="313.04" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#7d7c7b"></stop></linearGradient></defs><path fill="url(#Au)" d="M366 298h4c2 1 6 1 8 1h1l3 7 5 11-5-1h0-18v-1h-5c-3-2-6-2-9-2l7-6 1-2-1-1-6 1c1 0 2-1 4-1l-2-1c-1 0-1-1-2-2 4-1 8 0 11 0v-1h-1c2-1 3-1 5-2z"></path><path d="M378 306h4l5 11-5-1c0-1 0-1-1-2-1 0-1 0-2-1-2-1-3-2-3-5h1l-1-1v-1h2z"></path><path d="M366 298h4c2 1 6 1 8 1h1l3 7h-4-2c0-1-1-1-1-1l-1-1-17 3 1-2-1-1-6 1c1 0 2-1 4-1l-2-1c-1 0-1-1-2-2 4-1 8 0 11 0v-1h-1c2-1 3-1 5-2z" class="R"></path><path d="M351 301c4-1 8 0 11 0h17v1c-6 0-12-1-17 0-3 1-5 2-7 2l-2-1c-1 0-1-1-2-2z" class="C"></path><path d="M357 304c2 0 3 0 5-1h13c2 0 4 0 5 1h-6l-17 3 1-2-1-1z" class="G"></path><path d="M344 180c6 0 11 1 17 3 0 2-1 4-2 5l-2 5c-1-1-4 0-5 0h-4c-3 0-6 1-9 2l-10 5h0c-1 1 0 1-1 2s-1 2-1 3h3c2 2 3 2 3 5-9 1-16 5-23 11-3 1-6 4-8 6v1c-3 2-6 5-7 8s-3 5-4 8l2-1c0 1 0 1-1 2h0c-2 3-4 6-5 10l-3 6c-1 2-1 3-2 5 0 1 0 2-1 3v1 2 1l-1 6v10c0 2 0 4-1 6 0 4 1 9 1 14 1 4 4 9 3 13-5-11-10-21-13-33v-2-17c0-21 4-44 18-60 14-17 34-28 56-30z"></path><defs><linearGradient id="Av" x1="283.446" y1="236.255" x2="307.554" y2="219.245" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#acacaa"></stop></linearGradient></defs><path fill="url(#Av)" d="M295 221c3 0 8-8 10-10 1-1 2-1 4-1-7 8-15 17-18 27h2l-4 9h1l1-2 2-1c0 1 0 1-1 2h0c-2 3-4 6-5 10l-3 6c-1 2-1 3-2 5 0 1 0 2-1 3v1 2 1l-1 6v10c0 2 0 4-1 6-1-17 1-33 5-49 2-6 4-11 6-16 2-3 3-7 5-9z"></path><path d="M291 237h2l-4 9h1l1-2 2-1c0 1 0 1-1 2h0c-2 3-4 6-5 10l-3 6c-1 2-1 3-2 5 0 1 0 2-1 3v1 2 1l-1 6v10c0 2 0 4-1 6-1-17 1-33 5-49h1c0 4-2 8-3 12v2l-1 1v2 3c1 0 1 0 1-1 1-6 3-13 5-18 1-4 3-7 4-10zm4-16c4-8 11-16 18-21 14-10 29-15 46-12l-2 5c-1-1-4 0-5 0h-4c-3 0-6 1-9 2l-10 5-1-1-2-1c-7 3-11 8-17 12h0c-2 0-3 0-4 1-2 2-7 10-10 10z" class="C"></path><path d="M326 198c1-1 3-2 4-2l4-2h1c1 0 2 0 3-1 2 0 5 0 7-1h7v1h-4c-3 0-6 1-9 2l-10 5-1-1-2-1z" class="D"></path><defs><linearGradient id="Aw" x1="321.347" y1="219.619" x2="310.371" y2="212.064" xlink:href="#B"><stop offset="0" stop-color="#464648"></stop><stop offset="1" stop-color="#6a6866"></stop></linearGradient></defs><path fill="url(#Aw)" d="M326 198l2 1 1 1h0c-1 1 0 1-1 2s-1 2-1 3h3c2 2 3 2 3 5-9 1-16 5-23 11-3 1-6 4-8 6v1c-3 2-6 5-7 8s-3 5-4 8l-1 2h-1l4-9h-2c3-10 11-19 18-27h0c6-4 10-9 17-12z"></path><defs><linearGradient id="Ax" x1="313.114" y1="223.721" x2="305.163" y2="210.585" xlink:href="#B"><stop offset="0" stop-color="#b8b6b6"></stop><stop offset="1" stop-color="#e1e0dc"></stop></linearGradient></defs><path fill="url(#Ax)" d="M326 198l2 1 1 1h0c-5 2-8 5-12 8-10 9-18 18-24 29h-2c3-10 11-19 18-27h0c6-4 10-9 17-12z"></path><path d="M622 416l3 1c3 0 5 1 7 3 1 1 1 1 2 1l3 3v1c3 3 7 5 10 8l3 3 1-1c3 2 6 5 7 9h0c1 4 5 5 6 9h0 1l-2-5c1-1 2-2 4-2l-1 1h1v2c1 1 1 2 2 2l2 9c1 10 1 20 0 30v8l-4 12c0 1-1 1-2 2l-4 12h-1l-5 11v1c-3 7-7 15-10 23l-28 60-60 133-42 93-14-31-20-44-78-177-15-34-7-18c-3-4-5-10-6-15s-1-10-1-15c0-7 1-15 3-23 1-3 2-7 3-10 0-4 3-9 3-13 1-2 2-4 4-6l6-7c1-3 1-3 3-5 6-4 12-8 19-11 8-3 14-3 22-2l1 4 2 3 1 2 3 7c1 2 2 3 2 5l1 1v1l4 9 1 3 2 3 1 3 1 2 1 3 5 11 1 2 2 5 6 14c-3-1-6 0-10 1-3 1-7 1-10 3 1 1 3 1 5 1h2l1 1c3-2 8-2 12-2h2c1 2 2 4 2 5l7 15c0 1 1 2 1 4-1-1-2-1-4-1v1c2 0 3 1 5 2 2 4 6 10 6 15 2 2 5 8 5 10l5 11c1 2 2 4 2 6l3 6c4 10 7 20 9 30l2 5h2 2c1-3 1-6 2-8l1-1c1-7 4-15 7-22l1-1c5-1 10 0 15 2v-1c-1 0-2-1-3-1v-1c-3 0-7-1-10 0h-2-1c0-1 1-2 1-3l1-2 2-5 1-1c0-2 1-2 1-3 2-4 4-13 7-15h1v-1c-1-1 0-2 1-3v-1h0c1-4 3-7 5-10 1-3 1-6 4-7-1-1-1-1-1-2l1-1 8-18c2-4 3-4 7-6h0-4l1-2 1 1c1-2 2-3 3-3l2-2c1-2 1-3 0-4l-2-1v-2h1c0-1 0-2 1-3 1-2 1-3 3-4v-1-1l2-4 6-12 1-2 1-3 1-3 6-12c0-1 0-2 1-3l2-4 3-6c0-1 1-2 1-3l1-3c1-1 1-2 2-3l3-6v-1l3-5c0-1 0-3 1-4 3 0 5-1 8-1h3c1 0 1-1 2-1z"></path><path d="M400 545c-1 0-2-1-2-2-1-2-1-9 0-11l2 2v11zm148 203c0-1 0-1 1-2 0-1 0 0 1-2 0-1 1-1 1-2l1 1-1 1 1 1 1-1c1 1 0 2 0 4l-1 2v-1c-1 1-2 1-3 2v-2l-1 1h0v-1-1z" class="B"></path><path d="M400 530c1 2 1 4 2 6l-1 6c0 3 1 8-1 10v-7-11-4z" class="V"></path><path d="M588 597l2 5 1 6c-1 1-1 1-2 1 0 3 1 5 0 7l-2-13v-1-1c0-2 0-2 1-4z" class="X"></path><path d="M561 608l4 4c0 1 1 2 2 3 1 2 4 5 4 8v1h-1c-1-2-2-3-3-4l-4-3-1-1s1 0 1-1c1-3-1-5-2-7z" class="M"></path><path d="M581 586l3 1h1 0l3 10c-1 2-1 2-1 4v1 1l-6-17z" class="C"></path><path d="M450 625l1-1v3h0l1-1v2l-3 21v1c0-8-1-18 1-25z" class="W"></path><path d="M425 537h3 1c-1 2-2 4-2 7-1 2-2 6-3 9v-3c1-1 0-1 0-2l1-1v-1-1-1-2h-1s-1 1-1 2h0c0 1 0 1-1 2v2l-1 1c0 2 0 2-1 3l-1 1c1-6 4-11 6-16z" class="B"></path><path d="M551 596l11 9 8 8v1c-1 0-1 0-2-1 0-1 0-1-1-1h-1 0c1 1 1 1 1 2h0v1c-1-1-2-2-2-3l-4-4-1-1c0-1-1-2-2-2-1-1 0-2-1-3-2-2-5-3-6-6z" class="Y"></path><path d="M641 511v-1c1-1 1-1 1-2 1 1 1 2 1 3 1 0 1 1 1 0h0v25c-1-3-1-7-1-10l-2-15z" class="O"></path><path d="M579 600c2 7 3 14 3 21l-4-6c-1-1-2-3-2-5 0 0 0-1-1-1v-1l1-1c1 1 1 2 2 3 1-1-1-5 0-7 0-1 0-2 1-3z" class="C"></path><path d="M397 503c1-1 2-1 2-3l3-3h1c-3 8-3 16-4 24-1-1-1-2-1-3 1-1 0-3 0-4 1-1 1-3 1-4 1-1 0-2 0-3 1-1 1-2 1-3l-2 1h-1v1 2c-1 0-1 0-1 2v3h-1v2 2c0 2 0 3-1 4l-1 1c0-7 2-14 4-19z" class="E"></path><path d="M605 557l2 12c1 9 2 21 0 31v9l-1 1v-1c1-3 0-6 1-9v-10c0-9-1-18-4-27 2-2 2-3 2-6zm45-54v1c1 9 3 33-1 41 0-2 0-3 1-4v-13c0-8-1-16-3-24 1-1 2-1 3-1z" class="D"></path><path d="M455 636c1 3-1 7 0 10 1-2 1-7 3-9h0l-5 31c-1-11-1-21 2-32z" class="L"></path><path d="M439 564v-1c1-2 2-4 4-6l1 1s1-1 2-1c-3 4-6 8-7 12-3 4-5 12-5 16-2 8-1 19-1 27v6c-1-2 0-4 0-6l-1-12 1-10c1-3 0-7 1-10 1-6 3-11 5-16z" class="H"></path><path d="M563 638v-1c0-2-1-3-2-5 2 2 5 4 6 6s1 4 3 5c0 1 0 4 1 5 1 5 3 11 3 17-1-3-2-7-3-10-1-6-5-12-8-17z" class="O"></path><path d="M400 552c2-2 1-7 1-10 2 3 2 7 2 10v13 4l2 1v12c-1-1-1-4-2-6-1-8-3-16-3-24z" class="S"></path><path d="M591 608h0c0 5 2 36 0 39v1l-2-32c1-2 0-4 0-7 1 0 1 0 2-1z" class="L"></path><path d="M520 625h1c4 1 7 1 11 2 10 2 20 9 27 17h-1c-8-8-16-14-28-17-3 0-6-1-9-1h-1l-2 6c-1-1-1-5-3-6-5 1-10 1-16 3h0c5-2 12-4 17-4h2 2z" class="N"></path><path d="M436 584h2l1-2h1l1 2-2 1h3c1-1 1-2 2-3 0 4 0 6-1 10v5c0 3-1 6-1 9-1-1-1-3-1-4-1-1 0-2 0-3 1-1 0-2 0-3 1-1 1-2 1-3-1-1-2-2-4-2-1 0 0 0-1-1 0-1-1-3 0-4l1-1c-1 0-1 0-2-1h0z" class="B"></path><path d="M439 585h3c1-1 1-2 2-3 0 4 0 6-1 10-1-2-3-1-5-2-1-1 0-3 0-4l1-1z" class="E"></path><defs><linearGradient id="Ay" x1="454.681" y1="616.896" x2="453.692" y2="605.659" xlink:href="#B"><stop offset="0" stop-color="#898988"></stop><stop offset="1" stop-color="#a5a2a1"></stop></linearGradient></defs><path fill="url(#Ay)" d="M450 625c0-8 4-17 6-24l1 1 1 1c-1 2-1 2-1 4l1 2v1c-1 2-2 5-3 7l-3 9-1 1h0v-3l-1 1z"></path><path d="M617 562c1-1 1-3 0-5v-7-1l-1-1h0l1-1c1 2 1 3 2 5s1 5 1 7c1 6 0 11 0 17v9h-1l-2-23z" class="O"></path><path d="M403 497c1-2 2-3 4-5v1c-2 5-6 10-4 16-1 7-3 15-3 21v4l-2-2 1-11c1-8 1-16 4-24z" class="Z"></path><path d="M424 553l1 1c0 1 0 3-1 4 0 2-1 4-1 6h0c1-3 3-6 4-9 1 0 1 0 1 1-1 3-3 7-4 11-3 10-3 21-4 31-1-5 0-9 0-14 0-11 1-20 4-31z" class="C"></path><path d="M573 613l4 6c1 1 1 3 2 4 3 10 5 24 5 35 0 2 1 5 0 7 0-4-1-9-1-13-1-6-2-12-4-18-1-7-3-14-9-20h0v-1h3z" class="O"></path><path d="M439 569c1 0 1 0 2-2h1 1v1c0 1-1 2-2 3h0l2 2v1 1h-3c-1 1-1 2-1 4 0 1 0 1 1 1h2c1-1 1-2 2-3v-2-1h2c0 2-1 3-1 6 0 0-1 1-1 2-1 1-1 2-2 3h-3l2-1-1-2h-1l-1 2h-2v-1c0-1 0-1 1-2h-1 0c0 2-1 3-2 4 0-4 2-12 5-16z" class="J"></path><path d="M612 544c0-3 1-5 0-7l-1-4v-1l3 3c2 2 2 5 3 7v1l1 1h1v8c-1-2-1-3-2-5l-1 1h0l1 1v1 7c1 2 1 4 0 5-1-6-3-12-5-18h0z" class="G"></path><path d="M634 527l2-2-1 1c0 1 1 2 2 4 0 9 0 19-1 29 0 5 0 10-1 15v-11l-2-35 1-1z" class="C"></path><defs><linearGradient id="Az" x1="391.357" y1="482.689" x2="400.558" y2="479.513" xlink:href="#B"><stop offset="0" stop-color="#2c2928"></stop><stop offset="1" stop-color="#484d48"></stop></linearGradient></defs><path fill="url(#Az)" d="M406 466c1 0 1 1 1 2-8 7-13 19-15 29-1 4-1 8-1 11-1 0-1-2-1-2v-1h0v-1c0-2 0-2 1-3v-2h0c1-1 0-2 0-3 1-1 1-1 1-2v-2c1-1 1-1 1-2h0v-1l1-1-1-1c-1 1-1 2-2 3v4h-1v1 1l-1 2v1 1 2l-1 1h0-1c2-8 4-16 8-23 3-6 7-10 11-14z"></path><path d="M622 509l2 5c1-2 2-3 2-4l3 13c1 1 1 2 1 3l-1 6-1-1-1-2v-2c-1-1-1-1-1-2s0-2-1-3v-1h-2l1 1v2h0c0 1 0 2 1 3v2c0 1 0 1 1 2v1h-1 0l-5-22c1 0 1 0 2-1z" class="B"></path><path d="M626 510l3 13c1 1 1 2 1 3l-1 6-1-1c-1-6-2-12-4-17 1-2 2-3 2-4z" class="V"></path><path d="M392 497l1 1 1-2c1-2 3-4 4-5l3-6h0l-1 3v5c-1 1-2 3-3 5-1 3-1 5-2 8v1c-1 2-1 4-2 6 0 1 0 2-1 3v-1l-1 16c-2-7-1-16 0-23 0-3 0-7 1-11z" class="C"></path><path d="M601 557l1-2 1 8c3 9 4 18 4 27-2 3-1 5-1 8 0-3 0-7-2-10h0-1c0-4 0-9-2-13l-1 1c0-1-1-3-1-3l-2-8c2 1 2 2 3 4l1 3 1-1h0 0l1-1h0v1h1v-1c0-1 0-4-1-5l-2-8z" class="J"></path><path d="M597 565c2 1 2 2 3 4l1 3 1-1h0 0l1-1h0v1c0 2 1 3 1 5v12h0-1c0-4 0-9-2-13l-1 1c0-1-1-3-1-3l-2-8z" class="M"></path><defs><linearGradient id="BA" x1="456.506" y1="605.98" x2="474.411" y2="592.731" xlink:href="#B"><stop offset="0" stop-color="#b8b7b4"></stop><stop offset="1" stop-color="#e1e0dc"></stop></linearGradient></defs><path fill="url(#BA)" d="M457 602h0 3c2-4 6-7 9-10 2-2 5-2 8-2-6 6-16 13-19 22-2 5-4 10-6 16v-2l3-9c1-2 2-5 3-7v-1l-1-2c0-2 0-2 1-4l-1-1z"></path><path d="M619 526h2c1 0 1 1 2 0 0 4 1 9 1 13 1 0 1 1 2 2 1 4 1 9 1 13l-1 21v-6l-1-11c-1-6-3-11-4-17 0-1-1-2-1-3v-1h0 1c0-2 0-4-1-7l-1-4z" class="G"></path><path d="M619 526h2c1 0 1 1 2 0 0 4 1 9 1 13h-1v1 1c-1-1-1-3-1-5 0 2 1 4-1 5 0-1-1-2-1-3v-1h0 1c0-2 0-4-1-7l-1-4z" class="S"></path><path d="M380 528c1-3 0-7 0-10 0-12 0-26 4-38l1 1v2h1v-1 3 1c-1 2-1 4-2 6h2c-2 6-2 12-3 18-1 2-1 4-2 7 0 3 1 7 0 10l-1 1z" class="D"></path><defs><linearGradient id="BB" x1="460.06" y1="634.857" x2="459.44" y2="615.143" xlink:href="#B"><stop offset="0" stop-color="#707271"></stop><stop offset="1" stop-color="#8f8c8a"></stop></linearGradient></defs><path fill="url(#BB)" d="M463 611l1 1c2-1 3-2 5-3-1 2-3 4-4 6 0 2-1 4-1 6-1 2-3 4-2 6l-4 10h0c-2 2-2 7-3 9-1-3 1-7 0-10 0-7 4-15 6-21l2-4z"></path><defs><linearGradient id="BC" x1="437.822" y1="572.813" x2="431.364" y2="566.859" xlink:href="#B"><stop offset="0" stop-color="#53544f"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#BC)" d="M434 562h0c2-1 2-1 3-2l1-1 1 1c-1 2-4 7-4 10 1-1 2-2 2-4l2-2h0c-2 5-4 10-5 16-1 3 0 7-1 10l-1 10-2 2v5-1-3-1-1l-1-1 1-14 1-12 3-12z"></path><path d="M430 586c1-1 2-2 2-3v-2h1c0 1 0 2-1 3v3 1l1 2-1 10-2 2v5-1-3-1-1l-1-1 1-14z" class="E"></path><defs><linearGradient id="BD" x1="601.266" y1="567.638" x2="593.703" y2="551.641" xlink:href="#B"><stop offset="0" stop-color="#7b7978"></stop><stop offset="1" stop-color="#a2a19f"></stop></linearGradient></defs><path fill="url(#BD)" d="M594 547h0c2 1 2 2 3 4h1v-1c2 2 2 5 3 7l2 8c1 1 1 4 1 5v1h-1v-1h0l-1 1h0 0l-1 1-1-3c-1-2-1-3-3-4l-3-5-2-3-2-3 1-1-1-1 2-3 2-2z"></path><path d="M594 547h0c2 1 2 2 3 4h1v-1c2 2 2 5 3 7l2 8h-1c-1-1-2-3-2-5-1-1-3-3-3-4s1-1 1-1c0-1-1-3-1-3-1 0-1-1-2-1h-1 0l-2 2c1 1 1 2 1 3l-2-3-1-1 2-3 2-2z" class="C"></path><path d="M489 643c10-7 23-11 35-9 13 2 24 9 31 20 3 5 6 10 7 15-1 1-1 0-1 1v-1h-1v-1c-1-1-2-2-3-4l-4-7v-1-1-1h0c-1-3-6-7-8-9h-1c-2-2-4-3-6-4s-4-2-5-2c-3-1-6-2-8-3-2 0-4 0-5-1-2 0-6 0-8 1h-1c-6 1-11 2-16 4-2 1-3 3-5 3h-1z" class="D"></path><defs><linearGradient id="BE" x1="639.832" y1="503.007" x2="639.673" y2="487.483" xlink:href="#B"><stop offset="0" stop-color="#9d9c99"></stop><stop offset="1" stop-color="#bcbcb9"></stop></linearGradient></defs><path fill="url(#BE)" d="M634 490v-1c0-1-1-2-1-3l-3-6h0v-1h0l3 4 1 1c1 1 1 2 2 3 0-2 0-3-1-5h1 0 1v-3c4 9 6 17 7 26v6c0 1 0 0-1 0 0-1 0-2-1-3 0 1 0 1-1 2v1c-2-7-4-12-6-19 0 0-1-1-1-2z"></path><path d="M517 733h1v68h0v-36c-1-1 0-4-1-5v4 10l1 1c-1 1-1 1-1 2l-1 1h-1v14h1c0 4 0 8-1 11l-1-21c0-16 0-33 3-49z" class="B"></path><path d="M430 560h1c0 1 1 2 0 3 0 2-1 9 0 10v1l-1 12-1 14v29c-1-8-1-18-1-26 0-10-1-21 0-30 1-2 1-3 1-4-1 1-1 0-1 1v1c-2 10-2 19-3 29 0 3 0 6 1 8v5l-1-5c-2-9 0-20 1-30 0-4 1-8 2-11v-1l1-2c1-1 1-3 1-4z" class="C"></path><path d="M552 708l2-2c2-3 4-7 5-10l2-1c-4 13-12 23-24 29-10 6-23 7-35 3-3-1-6-2-8-4l1-1c8 4 18 6 27 5 12-2 23-9 30-19z" class="K"></path><path d="M499 751c3 1 5 6 8 8 1 0 1 2 3 2v-7h0v-1-1l1 1v2 1h0l1-3c1 1 0 4 0 6-1 0 0 1 0 2h1v37c0 5 0 9-2 14 0-5-1-10-1-15 0-7 2-26-2-32l-1-2-2-4-2-2-3-3-1-3z" class="J"></path><path d="M548 724h0v1 1l1 1h1l-1 2 1-1c1 1 0 4 1 6 0 1-1 2-1 3s0 2-1 3h-1v2c-1 1-1 2-2 3 0 2-1 2-2 3v1c-1 1-4 5-6 5v-1c1 0 0 0 1-1h-1v-1l1-1h1l-1-2-1 1h0-1v-1h-1c1-1 2-2 3-4l1-2c0-1 0-1 1-1v-2c1-1 1-2 1-3l3-6v-1c1-1 1-2 1-3v-1h1l1-1z" class="P"></path><defs><linearGradient id="BF" x1="467.114" y1="638.395" x2="476.532" y2="625.63" xlink:href="#B"><stop offset="0" stop-color="#9a9999"></stop><stop offset="1" stop-color="#bebdb9"></stop></linearGradient></defs><path fill="url(#BF)" d="M471 622l1 1c2 0 3-1 5-1 1 2 2 6 3 8l1 1c-9 11-16 27-18 40 0 1-1 2-1 3v8h-1c1-3 0-7 1-10 1-2 1-5 1-7 2-5 11-27 9-31-2-2-5-3-7-4 2-3 4-5 6-8z"></path><path d="M607 508l5 1v-1c0-2-2-3-3-4l1-1-4-4c-1 0-2-1-3-2v-1l2 1c4 3 8 6 11 10h1l2 1c0 1 1 1 1 2l5 22h0l1 9c-1-1-1-2-2-2 0-4-1-9-1-13-1 1-1 0-2 0h-2c-3-6-7-12-12-18z" class="U"></path><path d="M623 526c-1-4-4-8-6-12-1-2-4-4-4-6h1 5c0 1 1 1 1 2l5 22h0l1 9c-1-1-1-2-2-2 0-4-1-9-1-13z" class="T"></path><path d="M600 576l1-1c2 4 2 9 2 13h1 0c2 3 2 7 2 10-1 3-1 6-1 9l-1-1-1 4c0 11-1 22-4 33-1-2 0-5 0-7l1-12v-18-4-11c0-3 0-5 2-8l-2-7z" class="W"></path><path d="M602 583v5c0 4 0 10-2 14v-11c0-3 0-5 2-8z" class="S"></path><path d="M600 576l1-1c2 4 2 9 2 13h1 0c2 3 2 7 2 10-1 3-1 6-1 9l-1-1-1 4c0-7 0-15-1-22v-5l-2-7z" class="F"></path><path d="M563 617l4 3c1 1 2 2 3 4h1c1 2 2 4 2 6-1 1-1 0-2 2-2 3-2 7-1 11-2-1-2-3-3-5s-4-4-6-6c1 2 2 3 2 5v1l-5-8-1-1-1-3 1 1h2c2-3 3-7 4-10z" class="D"></path><path d="M570 627h1l-2 3c1 1 1 2 0 4l-1-1v-2c0-2 1-3 2-4z" class="C"></path><path d="M563 617l4 3c1 1 2 2 3 4-1 2-1 1-3 2s-2 4-4 5c-1 0-2 0-3-1h-2l-1-1-1-3 1 1h2c2-3 3-7 4-10z" class="I"></path><path d="M563 617l4 3c-2 2-5 8-7 9-1-1-2-1-3-2h0 2c2-3 3-7 4-10z" class="T"></path><path d="M551 612l5 1v1c2 1 4 1 6 2l1 1c-1 3-2 7-4 10h-2l-1-1 1 3c-2-1-4-3-5-4-7-5-15-9-23-9-2 0-4 1-7 1l1-1c2-1 4-1 7-1 5 0 9 1 14 3 1 1 2 1 3 2 1-1 1-1 1-2s-1-2-2-3v-1c2-1 3-1 5-2z" class="U"></path><path d="M556 614c2 1 4 1 6 2l1 1c-1 3-2 7-4 10h-2l-1-1c0-1-1-2-2-3-1-3-2-5-3-7-1-1-1 0-1-1 2-1 4-1 6-1z" class="H"></path><path d="M548 748v1 1h0l1-1v2c1-1 2-1 3-2v1c0 2-1 4-2 6l-1 2c-2 1-3 3-4 4l-1 1c-1 1-1 2-2 3l-2 2-2 2v-1c-2 1-4 3-5 4s-2 1-2 2c-1 1-2 1-3 1h0v-1l-1 1-1-1 1-1c1 0 0 0 1-1l1-1c1-1 1 0 1-1l-3 1h0-1c1-2 3-3 4-5s3-3 5-5h0v1c-1 1-3 2-4 4h1l5-4c1-2 3-4 4-5 1-2-1 0 1-2l1-1 1-1c0-1 1-2 1-2 1-2 2-3 3-4z" class="E"></path><path d="M590 580l4 2h1v3h1c0-1 1-1 1-2h1l2 8v11 4c-1-1-1-2-1-3h0c-1-3 0-8 0-11l-1 1v2h0v1 3 5l-1 1v3l-1 1c-2-1-2-1-3-2l1-2h-1c0 1 0 2-1 3v-1l-1 1h0l-1-6c1-2 1-4 1-7-1-5 0-10-2-15h1z" class="P"></path><path d="M595 585h1c0-1 1-1 1-2h1l2 8v11 4c-1-1-1-2-1-3h0c-1-3 0-8 0-11l-1 1v2h0v1 3h-4c-1-1-1-1-1-3l1-1c1-1 2-1 3 0 0-1 0-2 1-4h-1-1l-2-1c-1 0-1-1-2-2 0-1 0-1 1-2l1-1h1z" class="B"></path><path d="M606 539c3 3 3 7 4 10 3 13 4 25 4 38 0 5 0 10-1 15-1-4 0-9-1-14 0-5 0-11-1-16-1-1-1-4-1-4-1-1-1-1-2-3h-1l1 1-1 3-2-12-3-11h1l1 2h1c0-1 0-1-1-2v-2c1 0 1 1 2 2v1l1-1c-1-2-1-4-1-6v-1z" class="O"></path><path d="M602 546h1l1 2c1 7 5 13 6 20-1-1-1-1-2-3h-1l1 1-1 3-2-12-3-11z" class="B"></path><path d="M631 513l3 14-1 1 2 35-1 1h-1-1c0 5 1 13-1 17l-2-49 1-6c0-1 0-2-1-3 0-3 1-5 1-8h1v-2z" class="C"></path><path d="M631 513l3 14-1 1v-1l-1 18c-1-4 1-9-1-13 0-2 0-4-1-6 0-1 0-2-1-3 0-3 1-5 1-8h1v-2z" class="W"></path><path d="M632 545l1-18v1l2 35-1 1h-1-1v-19z" class="B"></path><defs><linearGradient id="BG" x1="514.444" y1="629.336" x2="480.844" y2="671.81" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#b0b0ae"></stop></linearGradient></defs><path fill="url(#BG)" d="M489 643h1c2 0 3-2 5-3 5-2 10-3 16-4h1c2-1 6-1 8-1 1 1 3 1 5 1-1 0-2 2-2 2h-7-1c-1 0-2 1-3 1l-12 3c-2 1-5 1-6 2s-1 1-1 2c-1 0-1 1-2 1l-2 2c-3 3-7 6-10 10-2 2-2 6-5 7h0l-1-1-1 2c2-10 9-19 17-24z"></path><path d="M441 541c0 1-1 1 0 2 2 0 4-2 5-4v3c1 3 3 3 1 6v3l8-8v2h0c-4 4-10 7-12 12-2 2-3 4-4 6v1h0l-2 2c0 2-1 3-2 4 0-3 3-8 4-10l-1-1-1 1c-1 1-1 1-3 2h0l-3 12v-1c-1-1 0-8 0-10 1-1 0-2 0-3l1-3h0l1-2c0-2 0-3 1-5s3-5 4-7c2-1 2-1 3-2z" class="I"></path><path d="M447 551l8-8v2h0c-4 4-10 7-12 12-2 2-3 4-4 6v1h0l-2 2c0 2-1 3-2 4 0-3 3-8 4-10l-1-1-1 1c-1 1-1 1-3 2h0c1-5 3-10 6-14h1c1 1 2 2 2 4 0 1-1 2-1 3l-1 1h1c1-2 3-3 5-5z" class="O"></path><path d="M529 720c2 0 4-1 6-2 1-1 3-2 5-2h1l6-6v-1l1 1c-1 2-1 2-3 4v1l4-4v-1c1-1 1-1 3-2-7 10-18 17-30 19-9 1-19-1-27-5-1-2-6-5-8-7 2 1 2 1 3 2h1l-3-3c-1-2-2-3-3-4 1 1 4 3 5 3h1c3 1 7 4 10 6 1 0 3 1 4 1l1 1c7 2 16 1 23-1z" class="G"></path><path d="M488 714c-1-2-2-3-3-4 1 1 4 3 5 3h1c3 1 7 4 10 6 1 0 3 1 4 1l1 1h-2c-7 0-11-3-16-7z" class="D"></path><defs><linearGradient id="BH" x1="547.029" y1="599.114" x2="556.794" y2="608.221" xlink:href="#B"><stop offset="0" stop-color="#636161"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#BH)" d="M531 589c4 0 8 0 12 1h1c3 0 7 2 9 3l2 1c2 1 5 3 5 5 1 0 1 0 1 1v1c1 1 2 1 3 1 0 1 1 1 1 2-1 0-1-1-2-1 0 1 0 1-1 2l-11-9c1 3 4 4 6 6 1 1 0 2 1 3 1 0 2 1 2 2l1 1c1 2 3 4 2 7 0 1-1 1-1 1-2-1-4-1-6-2v-1l-5-1h0c-1-1-2-1-2-1-1-2-3-2-5-2 2-4 4-6 4-11 0-1 0-2-2-3v-1c-1 0-2-1-3-1v-1c-3 0-7-1-10 0h-2-1c0-1 1-2 1-3z"></path><path d="M560 607l1 1c1 2 3 4 2 7 0 1-1 1-1 1-2-1-4-1-6-2v-1-1h0l-1-1c2-2 2-2 4-2l1-2z" class="G"></path><path d="M551 596h0c-4-3-7-4-11-6 6-1 12 4 17 6l4 4v1c1 1 2 1 3 1 0 1 1 1 1 2-1 0-1-1-2-1 0 1 0 1-1 2l-11-9z" class="K"></path><path d="M450 566c0 1 0 2 1 2 5-3 12-7 17-7-2 2-5 4-7 6l-1 1c-6 6-9 11-12 19-1 2-2 6-1 8l1 2c-1 1-1 2-2 3-1 4-1 8-2 12v3l1 1c-1 5-3 10-3 15 0 9 2 18 1 26 0-7-2-14-2-22 0-10 0-19 1-29 0-3 1-6 1-9v-5c1-4 1-6 1-10 0-1 1-2 1-2 0-3 1-4 1-6h1l1-1v-4c0-1 1-2 2-3z" class="X"></path><path d="M446 600c-1-2-1-6 0-8 0-2 0-4 1-6 0-2 1-4 2-6 0-2 1-4 2-5l1-1c2-3 5-6 8-6-6 6-9 11-12 19-1 2-2 6-1 8l1 2c-1 1-1 2-2 3z" class="G"></path><path d="M549 758c0 1 0 1-1 2l-1 1 1 1-1 1 1 1c0 2-2 2-2 5-1 2-2 4-4 6-1 1-1 1-1 2v1l-1 2-2 2 1 2c-1 1-2 1-2 2l1 2c-1 1-2 2-3 2h-1v1h1v1c-1 1-2 2-4 2l-1 1h-4v-1l5-1 1-1h-1c-1 0-2 0-3 1h-2 0c1-2 3-2 4-2v-1c-1 0-2 1-3 1h-1l-1-1 2-1h-1v-1c-1-1-1-1-1-2v-1h0c-1 2 0 10-1 10 0-5-1-11 0-17h1c1-1 2-1 3-2h0 0c1 0 2 0 3-1 0-1 1-1 2-2s3-3 5-4v1l2-2 2-2c1-1 1-2 2-3l1-1c1-1 2-3 4-4z" class="J"></path><path d="M534 582c5-1 8-1 13 0h1c4 2 8 4 11 7l2 1c4 6 10 11 13 18l-3 1 2 4h-3l-8-8c1-1 1-1 1-2 1 0 1 1 2 1 0-1-1-1-1-2-1 0-2 0-3-1v-1c0-1 0-1-1-1 0-2-3-4-5-5l-2-1c-2-1-6-3-9-3h-1c-4-1-8-1-12-1l1-2 2-5z" class="H"></path><path d="M534 582c5-1 8-1 13 0h1c1 2 1 2 1 4h-1c0 1 0 2-1 3-2 0-4-1-7-1-2 0-6 1-8-1l2-5z"></path><defs><linearGradient id="BI" x1="546.938" y1="594.594" x2="573.45" y2="596.854" xlink:href="#B"><stop offset="0" stop-color="#000100"></stop><stop offset="1" stop-color="#464443"></stop></linearGradient></defs><path fill="url(#BI)" d="M548 582c4 2 8 4 11 7l2 1c4 6 10 11 13 18l-3 1c-5-10-13-17-24-20 1-1 1-2 1-3h1c0-2 0-2-1-4z"></path><defs><linearGradient id="BJ" x1="412" y1="477.524" x2="401.915" y2="464.837" xlink:href="#B"><stop offset="0" stop-color="#b3b1b1"></stop><stop offset="1" stop-color="#dedcd9"></stop></linearGradient></defs><path fill="url(#BJ)" d="M416 454c9-4 18-6 27-3-1 1-4 0-6 0-11 1-23 5-30 14l-1 1c-4 4-8 8-11 14-4 7-6 15-8 23-2 14-2 27-1 41h-1c-1-8-1-17-1-26-1 1-1 2-1 3 0 6-1 11-1 17-1-3-2-6-2-10l1-1c1-3 0-7 0-10 1-3 1-5 2-7 1-6 1-12 3-18 1-6 3-11 5-17v5c2-4 4-7 6-10 5-7 12-12 19-16z"></path><path d="M391 475v5c-2 7-5 15-6 22v12c0 1 0 3-1 4s-1 2-1 3c0 6-1 11-1 17-1-3-2-6-2-10l1-1c1-3 0-7 0-10 1-3 1-5 2-7 1-6 1-12 3-18 1-6 3-11 5-17z" class="E"></path><path d="M523 616c1-7 4-15 7-22l1-1c5-1 10 0 15 2 2 1 2 2 2 3 0 5-2 7-4 11 2 0 4 0 5 2 0 0 1 0 2 1h0c-2 1-3 1-5 2v1c1 1 2 2 2 3s0 1-1 2c-1-1-2-1-3-2-5-2-9-3-14-3-3 0-5 0-7 1z"></path><path d="M530 615c0-1 0-1 1-2 1 0 2 0 3-1-1 0-3 0-5 1-1 0-2 1-4 1 1-1 2-1 2-2 6-2 16-3 22-1 0 0 1 0 2 1h0c-2 1-3 1-5 2v1c1 1 2 2 2 3s0 1-1 2c-1-1-2-1-3-2-5-2-9-3-14-3z" class="O"></path><path d="M544 618v-1c0-2-3-3-4-5h8c1-1 2-1 3 0h0c-2 1-3 1-5 2v1c1 1 2 2 2 3s0 1-1 2c-1-1-2-1-3-2z" class="C"></path><path d="M489 584h1c4-1 7-1 10-1l2 1 3 6-1 1-1 1c-3 0-5-1-9 0l-1 1v1c4-1 8-1 12-1v1h0c-6-1-10 0-15 2-4 2-6 4-9 8l-7 6c-1 1-3 2-4 4-4 4-6 8-8 13-1-2 1-4 2-6 0-2 1-4 1-6 1-2 3-4 4-6-2 1-3 2-5 3l-1-1-2 4c0-2 0-3 1-4h0v-1l2-2-1-2c-1 2-3 4-3 5l-1 1h-1c3-9 13-16 19-22l5-3 7-3z" class="H"></path><path d="M485 592c7-2 13-2 19-1l-1 1c-3 0-5-1-9 0l-1 1v1c-1 0-2 1-3 1h-1 0l1-1v-1c-2 1-3 2-5 2-1 1-1 1-2 1-2 1-3 1-4 2-1 0-3 1-4 2h0l-1-1c3-3 7-5 11-7z" class="D"></path><path d="M477 590l5-3h0c-1 3-5 6-8 8 4-1 7-3 11-3-4 2-8 4-11 7-4 4-8 7-11 12l-2 4c0-2 0-3 1-4h0v-1l2-2-1-2c-1 2-3 4-3 5l-1 1h-1c3-9 13-16 19-22z" class="F"></path><path d="M489 584h1c4-1 7-1 10-1l2 1 3 6-1 1c-6-1-12-1-19 1-4 0-7 2-11 3 3-2 7-5 8-8h0l7-3z"></path><defs><linearGradient id="BK" x1="492.105" y1="716.646" x2="477.94" y2="652.494" xlink:href="#B"><stop offset="0" stop-color="#9f9f9e"></stop><stop offset="1" stop-color="#bfbebc"></stop></linearGradient></defs><path fill="url(#BK)" d="M489 649l13-5c2-1 7-2 9-1-12 3-21 8-27 18-6 9-7 21-4 31v2 3c1 3 3 6 5 10 1 2 4 4 6 6h-1c-1 0-4-2-5-3 1 1 2 2 3 4l3 3h-1c-1-1-1-1-3-2 2 2 7 5 8 7l-1 1c-9-4-16-13-20-21-5-11-6-24-2-35l1-2 1 1h0c3-1 3-5 5-7 3-4 7-7 10-10z"></path><path d="M525 636c2 1 5 2 8 3 1 0 3 1 5 2s4 2 6 4h1c2 2 7 6 8 9h0v1 1 1l4 7c1 2 2 3 3 4v1h1v1c0-1 0 0 1-1 2 8 2 19-1 26h0l-2 1c-1 3-3 7-5 10l-2 2c-2 1-2 1-3 2v1l-4 4v-1c2-2 2-2 3-4l-1-1v1l-6 6h-1c-2 0-4 1-5 2-2 1-4 2-6 2 4-2 7-4 10-6s7-5 7-9c1-2 2-3 4-6 3-7 4-14 4-22-1-10-6-19-13-26-9-7-19-9-30-8-2-1-7 0-9 1l-13 5 2-2c1 0 1-1 2-1 0-1 0-1 1-2s4-1 6-2l12-3c1 0 2-1 3-1h1 7s1-2 2-2z" class="S"></path><path d="M525 636c2 1 5 2 8 3-1 0-1 0-2 1-2 0-5-1-7-1h-12c1 0 2-1 3-1h1 7s1-2 2-2z" class="O"></path><path d="M505 590c4 10 7 20 9 30-1-1-2-1-3-2-10-1-17 0-24 7l-6 6-1-1c-1-2-2-6-3-8-2 0-3 1-5 1l-1-1h0c0-2 1-4 0-6l-1-2c1-2 3-3 4-4l7-6c3-4 5-6 9-8 5-2 9-3 15-2h0v-1c-4 0-8 0-12 1v-1l1-1c4-1 6 0 9 0l1-1 1-1z" class="K"></path><path d="M505 590c4 10 7 20 9 30-1-1-2-1-3-2s-2-1-3-1c-3 1-6-1-9 0h-3c1-1 3 0 4-1h5v-1c-4-1-8 0-12 1h-2 0v-2h-5c-1 0-1 0-2-1 2-1 5-2 8-2s5 1 8 0c3 1 6 2 10 3h0l1-1-6-18v-1h0v-1c-4 0-8 0-12 1v-1l1-1c4-1 6 0 9 0l1-1 1-1z" class="E"></path><path d="M500 611c3 1 6 2 10 3l1 1c-2 0-4-1-7-1-1-1-3-1-4 0-2 1-6-1-7 1v1h-2 0v-2h-5c-1 0-1 0-2-1 2-1 5-2 8-2s5 1 8 0z" class="N"></path><path d="M490 596c5-2 9-3 15-2v1l6 18-1 1h0c-4-1-7-2-10-3s-5-2-7-3v-3-5h0c-1-2-2-3-3-4z"></path><defs><linearGradient id="BL" x1="486.351" y1="614.53" x2="479.565" y2="605.32" xlink:href="#B"><stop offset="0" stop-color="#504f4e"></stop><stop offset="1" stop-color="#6f6e6c"></stop></linearGradient></defs><path fill="url(#BL)" d="M481 604c3-4 5-6 9-8 1 1 2 2 3 4h0v5 3c2 1 4 2 7 3-3 1-5 0-8 0s-6 1-8 2c-3 2-5 3-8 5-1 1-3 3-5 4 0-2 1-4 0-6l-1-2c1-2 3-3 4-4l7-6z"></path><path d="M481 604c3-4 5-6 9-8 1 1 2 2 3 4h0v5s-1-1-1-2h-1c0-2 1-2 1-3-3 0-8 4-11 4z" class="F"></path><path d="M464 534c4-1 11-2 15 0 1 0 1 1 1 1l2 1c0 1 1 2 1 4-1-1-2-1-4-1v1c2 0 3 1 5 2 2 4 6 10 6 15v1l-3-1h-10c-3 2-6 3-9 4-5 0-12 4-17 7-1 0-1-1-1-2-1 1-2 2-2 3v4l-1 1h-1-2v1 2c-1 1-1 2-2 3h-2c-1 0-1 0-1-1 0-2 0-3 1-4h3v-1-1l-2-2h0c1-1 2-2 2-3v-1h-1-1c-1 2-1 2-2 2 1-4 4-8 7-12-1 0-2 1-2 1l-1-1c2-5 8-8 12-12h0v-2l2-2-2-1 2-2 7-4z" class="B"></path><path d="M446 561v-1c0-1 1-1 2-1h2 1l-1 2h-4zm13-15l6-3 1 1c-1 1 0 1-1 1-2 1-2 2-3 2l-2 1-1-2z" class="J"></path><path d="M450 559c0-2 1-3 2-4 2 0 2 1 3 2v1c-1 1-2 2-3 2l-1-1h-1z" class="K"></path><path d="M446 562c2 0 2 1 3 2 0 2-1 3-2 4-2 0-2 0-4-1v-1c1-2 1-3 3-4z" class="H"></path><path d="M446 557l3-3c0 2 0 3-1 5-1 0-2 0-2 1v1 1c-2 1-2 2-3 4v1h-1-1c-1 2-1 2-2 2 1-4 4-8 7-12z" class="F"></path><path d="M450 566c2-3 4-4 6-6 0-2 1-3 2-4l1-1c-1-1-2-2-2-3 1-1 2-2 3-2l1 1v1c1 1 1 1 0 2s-2 2-3 4h1c3 1 7 1 10 0 3 0 5-2 8-1-3 2-6 3-9 4-5 0-12 4-17 7-1 0-1-1-1-2z" class="M"></path><path d="M479 540c2 0 3 1 5 2 2 4 6 10 6 15v1l-3-1-9-7c-4-4-7-6-12-6l-1-1c4-2 9-3 14-3z"></path><defs><linearGradient id="BM" x1="474.019" y1="533.683" x2="471.024" y2="541.415" xlink:href="#B"><stop offset="0" stop-color="#181816"></stop><stop offset="1" stop-color="#333431"></stop></linearGradient></defs><path fill="url(#BM)" d="M464 534c4-1 11-2 15 0 1 0 1 1 1 1l2 1c0 1 1 2 1 4-1-1-2-1-4-1v1c-5 0-10 1-14 3l-6 3c-3 2-7 5-10 8l-3 3c-1 0-2 1-2 1l-1-1c2-5 8-8 12-12h0v-2l2-2-2-1 2-2 7-4z"></path><path d="M464 534c4-1 11-2 15 0 1 0 1 1 1 1-9-2-15 2-23 6l-2-1 2-2 7-4z" class="C"></path><defs><linearGradient id="BN" x1="469.594" y1="552.609" x2="448.131" y2="544.083" xlink:href="#B"><stop offset="0" stop-color="#aea6af"></stop><stop offset="1" stop-color="#dfe1d8"></stop></linearGradient></defs><path fill="url(#BN)" d="M455 545c5-3 10-6 16-6v-1c3 0 5 0 8 1v1c-5 0-10 1-14 3l-6 3c-3 2-7 5-10 8l-3 3c-1 0-2 1-2 1l-1-1c2-5 8-8 12-12h0z"></path><path d="M454 494c3-1 6-1 9-1l2 5 6 14c-3-1-6 0-10 1-3 1-7 1-10 3-3 1-6 2-8 4l-1 1v1c-2 0-4 0-5 1-3 1-5 4-7 6h0l-2 3c-1 1-2 3-3 5-2 5-5 10-6 16l-3 11-1 11-1-1c0-9 0-17 2-26h-1 0v-2c1-1 1-1 1-2 1-1 1-3 1-4h0v-1-1c1-1 1-1 1-2 1-1 0-3 0-4-1 1-1 3-2 4h-1c5-15 16-28 28-37 3-2 7-4 11-5z" class="N"></path><path d="M416 564v-4l1-4v-2h1l1-6c1-1 0-1 1-2v-2-1c1-1 1 0 1-1v-2c1-4 2-6 6-9l1 1c-1 1-2 3-3 5-2 5-5 10-6 16l-3 11z" class="H"></path><path d="M430 529h-1l-1-2c1-1 1-2 1-3 2-2 5-3 7-5 2-1 4-3 6-5h1c-1 3-1 4-1 6v1h0v1c-2 0-4 0-5 1-3 1-5 4-7 6z" class="I"></path><defs><linearGradient id="BO" x1="451.468" y1="506.482" x2="451.162" y2="515.622" xlink:href="#B"><stop offset="0" stop-color="#514f4f"></stop><stop offset="1" stop-color="#696867"></stop></linearGradient></defs><path fill="url(#BO)" d="M454 504l2 1c-1 1-1 1-2 1v1 1c1 0 1 1 2 2h0c2 2 4 1 5 3-3 1-7 1-10 3-3 1-6 2-8 4l-1 1h0v-1c0-2 0-3 1-6h0c1-2 1-2 1-4 2-2 4-4 7-5l3-1z"></path><path d="M465 498l6 14c-3-1-6 0-10 1-1-2-3-1-5-3h0c-1-1-1-2-2-2v-1-1c1 0 1 0 2-1l-2-1c-2 0-4 0-6 1 4-3 11-5 16-5h1v-1-1z" class="Q"></path><path d="M456 505c1-2 4-3 7-3 1 0 1 1 2 2s2 4 2 6h-11 0c-1-1-1-2-2-2v-1-1c1 0 1 0 2-1z"></path><defs><linearGradient id="BP" x1="429.498" y1="518.183" x2="426.332" y2="516.015" xlink:href="#B"><stop offset="0" stop-color="#343534"></stop><stop offset="1" stop-color="#4f4e4c"></stop></linearGradient></defs><path fill="url(#BP)" d="M454 494c3-1 6-1 9-1l2 5v1h-6c-3 1-9 4-12 4h-1c-11 7-20 14-25 27-2 6-5 18-5 18h-1 0v-2c1-1 1-1 1-2 1-1 1-3 1-4h0v-1-1c1-1 1-1 1-2 1-1 0-3 0-4-1 1-1 3-2 4h-1c5-15 16-28 28-37 3-2 7-4 11-5z"></path><path d="M454 494c3-1 6-1 9-1l2 5v1h-6c-3 1-9 4-12 4h-1c-2-1-4 0-6 1 2-2 3-3 5-4 3-1 7-2 9-6z" class="P"></path><defs><linearGradient id="BQ" x1="414.291" y1="465.588" x2="402.022" y2="454.021" xlink:href="#B"><stop offset="0" stop-color="#9d9a9d"></stop><stop offset="1" stop-color="#e1e1db"></stop></linearGradient></defs><path fill="url(#BQ)" d="M415 436c8-3 14-3 22-2l1 4 2 3 1 2 3 7-1 1c-9-3-18-1-27 3-7 4-14 9-19 16-2 3-4 6-6 10v-5c-2 6-4 11-5 17h-2c1-2 1-4 2-6v-1-3 1h-1v-2l-1-1 1-4c0-1 0-2 1-3h0v-1l2-3c0-1 0-1 1-1 0-2-1 0 0-2h1l-1-1-2 2h0l-1-1-6 12c0-4 3-9 3-13 1-2 2-4 4-6l6-7c1-3 1-3 3-5 6-4 12-8 19-11z"></path><defs><linearGradient id="BR" x1="409.806" y1="462.104" x2="403.481" y2="457.6" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#6f6e6c"></stop></linearGradient></defs><path fill="url(#BR)" d="M391 475v-1c6-11 18-24 30-28v1c-2 1-3 2-4 3l-2 2h1 2l-1 1-1 1c-7 4-14 9-19 16-2 3-4 6-6 10v-5z"></path><defs><linearGradient id="BS" x1="440.322" y1="444.099" x2="418.318" y2="454.754" xlink:href="#B"><stop offset="0" stop-color="#191817"></stop><stop offset="1" stop-color="#3b3a39"></stop></linearGradient></defs><path fill="url(#BS)" d="M421 446c7-2 13-4 20-3l3 7-1 1c-9-3-18-1-27 3l1-1 1-1h-2-1l2-2c1-1 2-2 4-3v-1z"></path><defs><linearGradient id="BT" x1="417.762" y1="456.75" x2="407.769" y2="445.026" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#4a4a48"></stop></linearGradient></defs><path fill="url(#BT)" d="M437 437l1 1 2 3c-12-2-23 2-33 9-7 5-13 10-17 17-2 3-3 6-5 9 0-1 0-2 1-3h0v-1l2-3c0-1 0-1 1-1 0-2-1 0 0-2h1l-1-1-2 2h0l-1-1c2-3 5-7 8-9 12-13 26-20 43-20z"></path><path d="M415 436c8-3 14-3 22-2l1 4-1-1c-17 0-31 7-43 20-3 2-6 6-8 9l-6 12c0-4 3-9 3-13 1-2 2-4 4-6l6-7c1-3 1-3 3-5 6-4 12-8 19-11z" class="I"></path><path d="M415 436c8-3 14-3 22-2l1 4-1-1c-2-1-6-1-8-1l-1 1h-1-2c-1 0-1 0-2 1-2 0-3 0-4 1h-2l-6 3c-1 1-2 1-3 2h-1c0-1 1-1 1-2-6 2-10 7-15 10 1-3 1-3 3-5 6-4 12-8 19-11z" class="D"></path><path d="M451 516c1 1 3 1 5 1h2l1 1c3-2 8-2 12-2h2c1 2 2 4 2 5l7 15-2-1s0-1-1-1c-4-2-11-1-15 0l-7 4-2 2 2 1-2 2-8 8v-3c2-3 0-3-1-6v-3c-1 2-3 4-5 4-1-1 0-1 0-2-1 1-1 1-3 2-1 2-3 5-4 7s-1 3-1 5l-1 2h0l-1 3h-1v-2c0-2 0-3 1-4h0-1l-2 2c0-1 0-1-1-1-1 3-3 6-4 9h0c0-2 1-4 1-6 1-1 1-3 1-4l-1-1h0c1-3 2-7 3-9 0-3 1-5 2-7h-1-3c1-2 2-4 3-5l2-3h0c2-2 4-5 7-6 1-1 3-1 5-1v-1l1-1c2-2 5-3 8-4z" class="G"></path><path d="M434 535c2 0 3-2 5-2-3 4-6 8-9 13l-2 4h0c-1-2 1-3 1-5 2-3 3-6 5-10z" class="H"></path><path d="M458 520c3 0 5-2 7-1-4 2-9 4-13 6-1 0-2 0-3 1s-2 2-3 2c-2 1-6 5-7 5-2 0-3 2-5 2 1-2 3-3 5-5l7-5c4-1 8-3 12-5z" class="N"></path><path d="M471 516h2c1 2 2 4 2 5v3c-1 1-2 3-2 4-1-1-1-1-1-3h1l-1-2c-1-1-2-1-3 0-5 0-12 4-17 6l1-1 2-1c2-1 3-2 4-4h-1l-2 2h-2-2c4-2 9-4 13-6-2-1-4 1-7 1l-1-2h2c3-2 8-2 12-2z" class="E"></path><path d="M459 518c3-2 8-2 12-2l-5 1v1h2v1h-2-1c-2-1-4 1-7 1l-1-2h2z" class="F"></path><path d="M452 529c5-2 12-6 17-6 1-1 2-1 3 0-2 0-4 1-6 1-3 1-4 1-6 3l-4 2c-1 2-1 3 0 5 0 1 1 1 2 1v1l-1 2-2 2 2 1-2 2-8 8v-3c2-3 0-3-1-6v-3c-1 2-3 4-5 4-1-1 0-1 0-2-1 1-1 1-3 2 3-6 9-9 14-13v-1z" class="N"></path><path d="M441 541c2-1 3-3 5-5l1 1-1 2c-1 2-3 4-5 4-1-1 0-1 0-2z" class="T"></path><path d="M455 540c-2 2-4 3-6 4v-9c2-2 4-4 7-6-1 2-1 3 0 5 0 1 1 1 2 1v1l-1 2-2 2z" class="L"></path><defs><linearGradient id="BU" x1="464.086" y1="529" x2="456.12" y2="530.557" xlink:href="#B"><stop offset="0" stop-color="#484746"></stop><stop offset="1" stop-color="#696965"></stop></linearGradient></defs><path fill="url(#BU)" d="M475 521l7 15-2-1s0-1-1-1c-4-2-11-1-15 0l-7 4 1-2v-1c-1 0-2 0-2-1-1-2-1-3 0-5l4-2c2-2 3-2 6-3 2 0 4-1 6-1l1 2h-1c0 2 0 2 1 3 0-1 1-3 2-4v-3z"></path><path d="M475 521l7 15-2-1s0-1-1-1c-4-2-11-1-15 0h-1-1c0-1 1-2 2-3h1c0-2-1-2-1-3l2-2 1 1c0 1 0 2 1 4 1 0 1 0 2-1v-1h2l1-1c0-1 1-3 2-4v-3z" class="F"></path><path d="M475 524c0 2 1 4 1 6-1 1-1 1-2 1h-2c-1 0-1 0-2-1v-1h2l1-1c0-1 1-3 2-4z" class="B"></path><path d="M451 516c1 1 3 1 5 1h2l1 1h-2l1 2c-4 2-8 4-12 5l-7 5c-1 0-2 1-3 1h-1c-1 0-2 2-3 2l1 1-6 10c0-3 1-5 2-7h-1-3c1-2 2-4 3-5l2-3h0c2-2 4-5 7-6 1-1 3-1 5-1v-1l1-1c2-2 5-3 8-4z" class="M"></path><path d="M451 516c1 1 3 1 5 1-4 1-8 2-11 4-1 0-1-1-2-1 2-2 5-3 8-4z" class="F"></path><path d="M428 532l2-3 1 2h1c0 3-2 4-3 6h-1-3c1-2 2-4 3-5z" class="Z"></path><path d="M446 525h0l2-2c0-2 3-3 5-3v-1l4-1 1 2c-4 2-8 4-12 5z" class="Y"></path><defs><linearGradient id="BV" x1="589.462" y1="518.486" x2="602.984" y2="508.44" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#464542"></stop></linearGradient></defs><path fill="url(#BV)" d="M579 492l3-1c4 1 8 0 12 1l6 2 3 2v1c1 1 2 2 3 2l4 4-1 1c1 1 3 2 3 4v1l-5-1c5 6 9 12 12 18l1 4c1 3 1 5 1 7h-1 0v1c-1-1-1-1-1-2s-1-2-1-3c-1 1-1 0-1 1v2l1 1v1h0c0 1 0 1 1 2v4h-1l-1-1v-1c-1-2-1-5-3-7l-3-3v1l1 4c1 2 0 4 0 7-2-5-5-9-9-14h1c0-2-1-3-2-5l-5-5-1 2c-3-2-6-4-9-5-2-1-4-2-7-3s-8 0-11 0h0-4l1-2 1 1c1-2 2-3 3-3l2-2c1-2 1-3 0-4l-2-1v-2h1c0-1 0-2 1-3 1-2 1-3 3-4v-1l4-1z"></path><path d="M604 513c7 8 13 19 14 31l-1-1v-1c-1-2-1-5-3-7l-3-3h0l-2-3v-2c-1-5-5-10-9-14h4z" class="N"></path><path d="M601 506c2-1 3-1 4 0l2 2c5 6 9 12 12 18l1 4c1 3 1 5 1 7h-1 0c-4-12-10-22-19-31z" class="K"></path><path d="M579 492l3-1c4 3 9 1 13 4l5 4v1c1 2 1 1 1 2 2 1 3 2 4 4-1-1-2-1-4 0-3-3-5-5-8-7-6-4-12-5-18-5v-1l4-1z" class="C"></path><path d="M579 492l3-1c4 3 9 1 13 4l5 4v1c-1-1-2-1-4-1-3-2-7-4-11-5-2-1-4-1-6-2z" class="R"></path><path d="M582 491c4 1 8 0 12 1l6 2 3 2v1c1 1 2 2 3 2l4 4-1 1c1 1 3 2 3 4v1l-5-1-2-2c-1-2-2-3-4-4 0-1 0 0-1-2v-1l-5-4c-4-3-9-1-13-4z" class="D"></path><path d="M602 525c0-1 0-3-1-4-2-3-3-5-4-8 0-1-1-2-1-3h0l3 1 2-1 3 3h-4c4 4 8 9 9 14v2l2 3h0v1l1 4c1 2 0 4 0 7-2-5-5-9-9-14h1c0-2-1-3-2-5z" class="V"></path><path d="M609 529c-2-2-4-4-5-6l-1-1h0c-1-3-4-6-4-9h1c4 4 8 9 9 14v2z" class="T"></path><path d="M571 501c0-1 0-2 1-3h10c7 2 13 7 18 12h1l-2 1-3-1h0c0 1 1 2 1 3 1 3 2 5 4 8 1 1 1 3 1 4l-5-5-1 2c-3-2-6-4-9-5-2-1-4-2-7-3s-8 0-11 0h0-4l1-2 1 1c1-2 2-3 3-3l2-2c1-2 1-3 0-4l-2-1v-2h1z" class="X"></path><path d="M586 504c2 0 3 1 4 2v3h-1l-2-2h-4c1 0 3 0 4-1l-1-2z" class="L"></path><path d="M574 500v1c2 0 5 0 7 1 1 1 3 2 5 2l1 2c-1 1-3 1-4 1-3 0-6 1-8 1-1-1-1-2-2-4h0c0-1-1-2-2-2l-1-1h1c1-1 2-1 3-1z" class="B"></path><path d="M574 501c2 0 5 0 7 1l2 3h-1c-1 1-3 1-5 0-1-1-3-1-4-2l1-2z" class="P"></path><path d="M571 501c0-1 0-2 1-3h10c7 2 13 7 18 12h1l-2 1-3-1h0c0 1 1 2 1 3 1 3 2 5 4 8 1 1 1 3 1 4l-5-5h0v-2c-1-2-2-5-2-7s-2-3-3-4v-1h1c-1-3-4-3-6-5h-2l-2-1c-1-1-1-1-2-1-2-1-5 0-6 0l-1 1c-1 0-2 0-3 1z" class="C"></path><path d="M570 510h1c1 0 2-1 3-1h0c1 1 1 1 2 0 5 0 8 0 12 2h1c1 1 2 1 3 2 0 1 0 2-1 3l6 4h0l-1 2c-3-2-6-4-9-5-2-1-4-2-7-3s-8 0-11 0h0-4l1-2 1 1c1-2 2-3 3-3z" class="Y"></path><path d="M569 514c7-2 15-1 22 2l6 4h0l-1 2c-3-2-6-4-9-5-2-1-4-2-7-3s-8 0-11 0h0z" class="H"></path><defs><linearGradient id="BW" x1="550.213" y1="564.01" x2="558.773" y2="574.923" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#BW)" d="M545 558h8c1 1 2 1 3 1h2c11 4 19 12 24 23l3 5h-1l-3-1v-2c0 3 0 4 1 6s1 4 1 6v1l-1-1-3-5h-2c0 2 0 1 1 2 1 2 1 4 1 7h0c-1 1-1 2-1 3-1 2 1 6 0 7-1-1-1-2-2-3l-1 1v1c1 0 1 1 1 1 0 2 1 4 2 5 1 2 2 3 1 5l-1-2-1 1-4-6-2-4 3-1c-3-7-9-12-13-18l-2-1c-3-3-7-5-11-7h-1c-5-1-8-1-13 0l1-1c0-2 1-2 1-3 2-4 4-13 7-15h1v-1c-1-1 0-2 1-3v-1z"></path><path d="M545 559c6 0 10 0 15 4-1 0-2 0-3-1h-1c-2-1-1-1-3 0h-3c-2 1-4 1-6 1v-1c-1-1 0-2 1-3z" class="F"></path><path d="M560 563c1 0 1 0 3 1h2v1h2c1 2 1 2 1 3 1 2 3 4 5 5 0 1 1 2 1 2l2 2v2l3 3v1h-1l-2-1-3-2h-2c-1 0-3 1-4 0-1-2 1-4 0-6h0c0-2-1-4-2-5v-1c-1-2-3-4-5-5z" class="T"></path><path d="M548 576c7 1 17 4 22 9 1-1 1-1 1-2 3 1 6 3 8 6l1 1h2c1 2 1 4 1 6v1l-1-1-3-5h-2c0 2 0 1 1 2 1 2 1 4 1 7-2-3-3-6-5-9-1-1-2-3-3-4-6-6-15-9-22-10l-1-1z" class="E"></path><path d="M536 578c2-4 4-13 7-15h5c1 1 2 1 2 3 1 3-1 6-3 8h-5v1c2 0 4 0 6 1l1 1c-4-1-8-2-11-1v1h5c-2 1-4 0-6 1h0-1z"></path><path d="M545 558h8c1 1 2 1 3 1h2c11 4 19 12 24 23l3 5h-1l-3-1v-2l-1-1-1-1-3-3v-2l-2-2s-1-1-1-2c-2-1-4-3-5-5 0-1 0-1-1-3h-2v-1h-2c-2-1-2-1-3-1h0c-5-4-9-4-15-4v-1z" class="K"></path><path d="M543 577h-5v-1c3-1 7 0 11 1 7 1 16 4 22 10h-3c-2-2-4-3-6-4 1 3 4 5 5 8h0c-1 0-2-1-3-1h0c-1-1-1-1-2-1s-1 1-1 1l-2-1c-3-3-7-5-11-7h-1c-5-1-8-1-13 0l1-1c0-2 1-2 1-3h1 0c2-1 4 0 6-1z" class="H"></path><path d="M543 577h2 0c2 1 5 1 6 2 4 1 7 3 10 6-5 1-11-3-15-4l-1-1c-3 0-8 0-10 1 0-2 1-2 1-3h1 0c2-1 4 0 6-1z" class="W"></path><defs><linearGradient id="BX" x1="570.584" y1="604.673" x2="564.635" y2="587.724" xlink:href="#B"><stop offset="0" stop-color="#c7c6c3"></stop><stop offset="1" stop-color="#e6e7df"></stop></linearGradient></defs><path fill="url(#BX)" d="M567 591c-1-3-4-5-5-8 2 1 4 2 6 4h3c1 1 2 3 3 4 2 3 3 6 5 9h0c-1 1-1 2-1 3-1 2 1 6 0 7-1-1-1-2-2-3l-1 1v1c1 0 1 1 1 1 0 2 1 4 2 5 1 2 2 3 1 5l-1-2-1 1-4-6-2-4 3-1c-3-7-9-12-13-18 0 0 0-1 1-1s1 0 2 1h0c1 0 2 1 3 1h0z"></path><path d="M574 608c1 3 3 7 4 10l-1 1-4-6-2-4 3-1z" class="B"></path><path d="M567 591c-1-3-4-5-5-8 2 1 4 2 6 4h3c1 1 2 3 3 4v3c0 2 2 4 2 6v1 2c1 1 1 2 1 3-1-4-4-11-7-14-1 0-1-1-3-1z" class="K"></path><defs><linearGradient id="BY" x1="559.601" y1="555.552" x2="597.654" y2="580.068" xlink:href="#B"><stop offset="0" stop-color="#333334"></stop><stop offset="1" stop-color="#898886"></stop></linearGradient></defs><path fill="url(#BY)" d="M554 541c5-1 9-1 15 1 1 0 3 1 5 2 5 3 10 8 14 13h2l1 1 1-1 2 3 3 5 2 8s1 2 1 3l2 7c-2 3-2 5-2 8l-2-8h-1c0 1-1 1-1 2h-1v-3h-1l-4-2h-1c2 5 1 10 2 15 0 3 0 5-1 7l-2-5-3-10h0l-3-5c-5-11-13-19-24-23h-2c-1 0-2 0-3-1h-8 0c1-4 3-7 5-10 1-3 1-6 4-7z"></path><path d="M582 556c-1 1-3 1-5 0h-1c1 0 1 0 2-1 0-1 0-2-1-2l-1-2h4l1 1h0l2 2c0 1 0 0-1 2z" class="B"></path><path d="M589 577v3c2 5 1 10 2 15 0 3 0 5-1 7l-2-5-3-10v-1c1 2 1 3 2 5 1 1 1 1 1 3h1v-1c1-1 0-3-1-4v-1h-1v-3c0-1 1-2 2-3-1-1-1-3 0-5z" class="R"></path><path d="M574 544c5 3 10 8 14 13l2 4c1 2 3 4 4 7h0c-1 1-1 1 0 2-2 0-4 0-5-1l-2-1v-3c1 0 0 0 0-1h-1v-2c-2 0-2 0-3-1h0l2-2-1-1v-1c0-1-1-1-2-1 1-2 1-1 1-2l-2-2h0 1c-1-1-2-3-4-4-1-1-2-2-4-2v-2z" class="F"></path><path d="M586 562l2-1 3 6v1l-2 1-2-1v-3c1 0 0 0 0-1h-1v-2z" class="H"></path><path d="M588 571h0c-1-1-1-1-1-3l2 1c1 1 3 1 5 1-1-1-1-1 0-2h0c2 4 4 10 4 15h-1c0 1-1 1-1 2h-1v-3h-1l-4-2h-1v-3l-1-6z" class="E"></path><path d="M588 571h5c1 2 1 2 1 4l-1-1c1 3 2 5 1 8l-4-2h-1v-3l-1-6z" class="B"></path><path d="M590 580v-4c1-1 2-1 3-2 1 3 2 5 1 8l-4-2z" class="D"></path><defs><linearGradient id="BZ" x1="600.973" y1="583.963" x2="592.686" y2="561.25" xlink:href="#B"><stop offset="0" stop-color="#b1b0b0"></stop><stop offset="1" stop-color="#dfdedb"></stop></linearGradient></defs><path fill="url(#BZ)" d="M588 557h2l1 1 1-1 2 3 3 5 2 8s1 2 1 3l2 7c-2 3-2 5-2 8l-2-8c0-5-2-11-4-15-1-3-3-5-4-7l-2-4z"></path><path d="M588 557h2l1 1 1-1 2 3v1h-4l-2-4z" class="I"></path><path d="M569 542c1 0 3 1 5 2v2l-1 1-3-1c-1-1-3 0-4 0-2 2-3 4-4 7-1 1-3 3-3 5v1h-1-2c-1 0-2 0-3-1h0l1-1c-1 0-1 0-2-1 1-3 6-7 8-9s3-3 6-3l3-2z" class="B"></path><path d="M569 542c1 0 3 1 5 2v2c-3-1-5-1-8-2l3-2z" class="E"></path><path d="M554 541c5-1 9-1 15 1l-3 2c-3 0-4 1-6 3s-7 6-8 9c1 1 1 1 2 1l-1 1h0-8 0c1-4 3-7 5-10 1-3 1-6 4-7z"></path><path d="M569 514c3 0 8-1 11 0s5 2 7 3c3 1 6 3 9 5l1-2 5 5c1 2 2 3 2 5h-1c4 5 7 9 9 14h0l-2 5c-1-3-1-7-4-10v1c0 2 0 4 1 6l-1 1v-1c-1-1-1-2-2-2v2c1 1 1 1 1 2h-1l-1-2h-1l3 11c0 3 0 4-2 6l-1-8-1 2c-1-2-1-5-3-7v1h-1c-1-2-1-3-3-4h0l-2 2-2 3 1 1-1 1 2 3-1 1-1-1h-2c-4-5-9-10-14-13-2-1-4-2-5-2-6-2-10-2-15-1-1-1-1-1-1-2l1-1 8-18c2-4 3-4 7-6z" class="R"></path><path d="M573 528l1-1c8 3 15 8 19 15-3 0-4-1-6-3l-6-6-8-5z" class="H"></path><defs><linearGradient id="Ba" x1="594.242" y1="550.024" x2="601.162" y2="543.601" xlink:href="#B"><stop offset="0" stop-color="#b4b3af"></stop><stop offset="1" stop-color="#e2e1df"></stop></linearGradient></defs><path fill="url(#Ba)" d="M575 525l-3-1c-2 0-2-1-4 0-1 2-1 3-1 5l-1 1-1-1c1-1 2-3 1-5-1 0-2 0-3-1h1c1-1 1-1 3-1 2 1 3 1 4 1 3 0 5 1 7 2l3 1c3 1 6 3 8 5 3 2 6 4 9 8 1 1 3 7 4 7l3 11c0 3 0 4-2 6l-1-8c-1-4-3-9-6-13-4-7-12-15-21-17z"></path><path d="M574 518c2 0 4 0 7 1l-1 1h-2c1 1 1 2 2 2v1l-2 2c-2-1-4-2-7-2-1 0-2 0-4-1-2 0-2 0-3 1h-1c1 1 2 1 3 1 1 2 0 4-1 5l1 1 1-1c0-2 0-3 1-5 2-1 2 0 4 0l3 1c-1 0-1 1-2 1-2-1-2-1-4-1l-1 1 2 1c-1 1-1 2-2 2v1c1 0 1 1 2 1v1l2 1c-2 0-2-1-3 0h-1-2-3l-6 3c-1 1-2 1-3 2h0l8-18h2c1 0 2-1 3-1 3 0 5-1 7-1z" class="Y"></path><path d="M574 518c2 0 4 0 7 1l-1 1h-2c1 1 1 2 2 2v1l-2 2c-2-1-4-2-7-2-1 0-2 0-4-1h1 2c2-1 4-1 6-1l1-1c-1 0-2-1-2-1l-1-1z" class="R"></path><path d="M570 527l2 1h1l8 5 6 6c2 2 3 3 6 3 3 2 4 5 5 8v1h-1c-1-2-1-3-3-4h0l-2 2-2 3c-1-1-2-3-2-4l-2-2c-2-5-7-8-11-9-4-2-10-4-14-2-1 1-2 1-4 1h0l6-3h3 2 1c1-1 1 0 3 0l-2-1v-1c-1 0-1-1-2-1v-1c1 0 1-1 2-2z" class="N"></path><path d="M588 548c-1-3-1-4 0-6h1c0 1 1 2 2 2l1 1 2 2-2 2-2 3c-1-1-2-3-2-4z" class="K"></path><path d="M592 545l2 2-2 2c-1 0-2-1-3-1 1-2 1-2 3-3z" class="T"></path><path d="M581 533l6 6-2 1c-3 0-8-4-11-5 2-1 2-1 4-1l2 1 1-2h0z" class="W"></path><path d="M570 527l2 1h1l8 5h0l-1 2-2-1c-2 0-2 0-4 1h0c-4-1-7-2-11-2h3 2 1c1-1 1 0 3 0l-2-1v-1c-1 0-1-1-2-1v-1c1 0 1-1 2-2z" class="Z"></path><path d="M570 527l2 1c1 1 2 2 2 4-1 0-2 0-4-1-1 0-1-1-2-1v-1c1 0 1-1 2-2z" class="F"></path><defs><linearGradient id="Bb" x1="567.691" y1="532.154" x2="577.247" y2="546.227" xlink:href="#B"><stop offset="0" stop-color="#585756"></stop><stop offset="1" stop-color="#878781"></stop></linearGradient></defs><path fill="url(#Bb)" d="M557 536h0c2 0 3 0 4-1 4-2 10 0 14 2 4 1 9 4 11 9l2 2c0 1 1 3 2 4l1 1-1 1 2 3-1 1-1-1h-2c-4-5-9-10-14-13-2-1-4-2-5-2-6-2-10-2-15-1-1-1-1-1-1-2l1-1h0c1-1 2-1 3-2z"></path><defs><linearGradient id="Bc" x1="561.786" y1="551.244" x2="581.616" y2="541.064" xlink:href="#B"><stop offset="0" stop-color="#c2c0c4"></stop><stop offset="1" stop-color="#ebeae2"></stop></linearGradient></defs><path fill="url(#Bc)" d="M554 538c9-2 16 0 24 5 4 3 8 7 12 11l2 3-1 1-1-1h-2c-4-5-9-10-14-13-2-1-4-2-5-2-6-2-10-2-15-1-1-1-1-1-1-2l1-1h0z"></path><path d="M569 514c3 0 8-1 11 0s5 2 7 3c3 1 6 3 9 5l1-2 5 5c1 2 2 3 2 5h-1c4 5 7 9 9 14h0l-2 5c-1-3-1-7-4-10v1c0 2 0 4 1 6l-1 1v-1c-1-1-1-2-2-2v2c1 1 1 1 1 2h-1l-1-2h-1c-1 0-3-6-4-7-3-4-6-6-9-8-2-2-5-4-8-5l-3-1 2-2v-1c-1 0-1-1-2-2h2l1-1c-3-1-5-1-7-1s-4 1-7 1c-1 0-2 1-3 1h-2c2-4 3-4 7-6z" class="Q"></path><path d="M597 520l5 5c1 2 2 3 2 5h-1l-7-8 1-2z" class="K"></path><path d="M577 517l6 1c4 2 7 4 11 7 4 2 7 4 9 8l-1-1c-3-1-4-4-7-5l-1 1v2c-3-3-6-7-10-9l-3-2c-3-1-5-1-7-1s-4 1-7 1l4-1c2-1 4-1 6-1z" class="U"></path><path d="M569 514c3 0 8-1 11 0s5 2 7 3l-2 1h-2 0l-6-1c-2 0-4 0-6 1l-4 1c-1 0-2 1-3 1h-2c2-4 3-4 7-6z" class="B"></path><path d="M580 514c3 1 5 2 7 3l-2 1h-2 0l-6-1 3-1h2 0l-1-1-1-1z" class="Y"></path><path d="M594 530v-2l1-1c3 1 4 4 7 5l1 1 3 6v1c0 2 0 4 1 6l-1 1v-1c-1-1-1-2-2-2v2c1 1 1 1 1 2h-1l-1-2v-1c-1-3-3-5-5-8-1-2-2-4-4-7z" class="K"></path><path d="M581 519l3 2c4 2 7 6 10 9 2 3 3 5 4 7 2 3 4 5 5 8v1h-1c-1 0-3-6-4-7-3-4-6-6-9-8-2-2-5-4-8-5l-3-1 2-2v-1c-1 0-1-1-2-2h2l1-1z" class="O"></path><path d="M581 519l3 2-2 1h1c2 1 4 3 5 4v1l-1-1c-2-1-3-1-5 0h-1l-3-1 2-2v-1c-1 0-1-1-2-2h2l1-1z" class="M"></path><defs><linearGradient id="Bd" x1="453.446" y1="617.26" x2="478.893" y2="563.476" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#525150"></stop></linearGradient></defs><path fill="url(#Bd)" d="M487 557l3 1v-1c2 2 5 8 5 10l5 11c1 2 2 4 2 6l-2-1c-3 0-6 0-10 1h-1l-7 3-5 3c-3 0-6 0-8 2-3 3-7 6-9 10h-3 0l-1-1 1-2c-4 6-9 13-11 20 0 1 0 3-1 4v-1-1c0-2 1-3 1-5h-1l-1-1v-3c1-4 1-8 2-12 1-1 1-2 2-3l-1-2c-1-2 0-6 1-8 3-8 6-13 12-19l1-1c2-2 5-4 7-6 3-1 6-2 9-4h10z"></path><path d="M489 577h3v1h-1c-4 0-7 1-11 2-2 1-4 2-6 4l-1 1-2-1c-2 1-3 2-6 3l1-1c6-6 15-8 23-9z" class="N"></path><path d="M446 619v-1c0-2 1-4 2-6 1-4 2-7 3-10 4-6 8-11 13-16h2l-1 1c-1 2-4 5-5 7 0 2 0 2-1 3l-1-1c0 1-1 2-1 3-4 6-9 13-11 20z" class="V"></path><path d="M492 577c3 0 5 1 8 1 1 2 2 4 2 6l-2-1c-3 0-6 0-10 1h-1c-3-1-5 1-8 2h0c-3-1-5-1-7-2 2-2 4-3 6-4 4-1 7-2 11-2h1v-1z" class="M"></path><path d="M474 584c2-2 4-3 6-4l2 1h0c2-1 3-1 5 0 4 1 9 1 13 1v1c-3 0-6 0-10 1h-1c-3-1-5 1-8 2h0c-3-1-5-1-7-2z" class="X"></path><path d="M473 585l1-1c2 1 4 1 7 2h0c3-1 5-3 8-2l-7 3-5 3c-3 0-6 0-8 2-3 3-7 6-9 10h-3 0l-1-1 1-2c0-1 1-2 1-3l1 1c1-1 1-1 1-3 1-2 4-5 5-7 3-1 4-2 6-3l2 1z" class="U"></path><path d="M465 587c3-1 4-2 6-3l2 1-13 12h-1c1-1 1-1 1-3 1-2 4-5 5-7z" class="T"></path><path d="M487 557l3 1v-1c2 2 5 8 5 10l5 11c-3 0-5-1-8-1h-3l-1-1h8c-5-2-11 0-15-5-1-1-1-2-1-4s2-3 3-4h1v-1c-2 1-4 1-6 1h-1l4-2h-2c-1 0-1 0-2-1-4 0-7 3-10 5-1 0-4 1-6 2 2-2 5-4 7-6 3-1 6-2 9-4h10z"></path><path d="M490 557c2 2 5 8 5 10v3h1c0 1 0 2-1 2h-2c0-1 1-2 1-4-1-2-2-3-2-5v-1c-2-1-4 0-5 0-2-1-4-1-5-1h-1-2c-1 0-1 0-2-1 4-1 8-2 13-2h0v-1z" class="F"></path><path d="M487 557l3 1h0c-5 0-9 1-13 2-4 0-7 3-10 5-1 0-4 1-6 2 2-2 5-4 7-6 3-1 6-2 9-4h10z" class="K"></path><path d="M467 565c3-2 6-5 10-5 1 1 1 1 2 1h2l-4 2c-3 2-6 6-7 10 0 2 0 3 1 5-1 0-2 0-3 1-2-1-2 0-4 1s-5 4-7 6c-7 9-10 18-13 29v-3c1-4 1-8 2-12 1-1 1-2 2-3l-1-2c-1-2 0-6 1-8 3-8 6-13 12-19l1-1c2-1 5-2 6-2z" class="V"></path><path d="M457 586v-2c0-3 2-5 4-7 1 0 1 0 2 1h1 1 0l-1-1h1l2 1c1-1 1-2 2-2-1-1-2 0-4 0h0l-1-1h1c2-1 3 0 4 0v1l-1 3c-2-1-2 0-4 1s-5 4-7 6z" class="K"></path><path d="M461 567c2-1 5-2 6-2-4 4-7 9-11 14-2 3-5 5-6 8-1 0-1 1-2 0 3-8 6-13 12-19l1-1zm-17-117c1 2 2 3 2 5l1 1v1l4 9 1 3 2 3 1 3 1 2h-1c-4-2-11-1-15 0l-1-1c-1 0-4 0-5 1h-3c-7 4-14 10-19 17l-2-1c-3 5-5 11-7 16-2-6 2-11 4-16v-1c-2 2-3 3-4 5h-1l-3 3c0 2-1 2-2 3-2 5-4 12-4 19-1 10 0 21 0 31h0c-1-5-1-10-1-15-1-7 0-15 0-22 1-1 1-2 1-3 1-2 1-4 2-6v-1c1-3 1-5 2-8 1-2 2-4 3-5v-5l1-3h0l-3 6c-1 1-3 3-4 5l-1 2-1-1c2-10 7-22 15-29 0-1 0-2-1-2l1-1c7-9 19-13 30-14 2 0 5 1 6 0l1-1z" class="H"></path><path d="M444 450c1 2 2 3 2 5h-7s-1-1-2-1v-3c2 0 5 1 6 0l1-1z" class="F"></path><defs><linearGradient id="Be" x1="402.414" y1="492.091" x2="412.341" y2="484.442" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#5f5e5c"></stop></linearGradient></defs><path fill="url(#Be)" d="M397 503c1-4 2-6 3-9 2-4 3-8 6-11h0c-1 2-3 6-3 8l1-1c2-3 4-5 6-7h4c0 2-5 7-7 9h0c-2 2-3 3-4 5h-1l-3 3c0 2-1 2-2 3z"></path><path d="M430 463c5 0 9-2 14-1h-4c3 2 7 3 11 4l1 3h-1v-1c-2-1-4-1-6-1-5-1-10-3-15-2l-3 1c-9 3-15 9-21 17h0c3-5 7-10 11-13s8-6 13-7z" class="Z"></path><path d="M406 483c6-8 12-14 21-17 1 2 1 2 0 4l-8 5v1-1h4c-4 2-9 4-13 8-2 2-4 4-6 7l-1 1c0-2 2-6 3-8z" class="V"></path><defs><linearGradient id="Bf" x1="435.804" y1="450.791" x2="408.193" y2="466.702" xlink:href="#B"><stop offset="0" stop-color="#403f3e"></stop><stop offset="1" stop-color="#737270"></stop></linearGradient></defs><path fill="url(#Bf)" d="M406 466l1-1c7-9 19-13 30-14v3c1 0 2 1 2 1-11 1-22 5-32 13 0-1 0-2-1-2z"></path><defs><linearGradient id="Bg" x1="435.31" y1="454.985" x2="428.433" y2="463.707" xlink:href="#B"><stop offset="0" stop-color="#70716f"></stop><stop offset="1" stop-color="#8b8888"></stop></linearGradient></defs><path fill="url(#Bg)" d="M430 463c-2-1-5 0-7 1 0 0-1 1-2 1-2 1-5 4-7 6h-1c1-1 1-2 1-3 5-6 15-10 22-11 4-1 7-1 11-1v1l4 9c-4-1-8-2-11-4h4c-5-1-9 1-14 1z"></path><path d="M447 457l4 9c-4-1-8-2-11-4h4c1 0 2 1 3 0l-1-1c-2-1-5-1-7-1h-1 0c3-1 5-1 8-1v-1c-1 0 0 0-1-1h2z" class="Y"></path><path d="M427 466l3-1c5-1 10 1 15 2 2 0 4 0 6 1v1h1l2 3c-9-1-18 0-27 4l1-1-1-1-3 1h0-1-4v1-1l8-5c1-2 1-2 0-4z" class="C"></path><path d="M424 475c8-5 17-7 27-6h1l2 3c-9-1-18 0-27 4l1-1-1-1-3 1h0z" class="B"></path><defs><linearGradient id="Bh" x1="431.951" y1="486.331" x2="416.367" y2="479.591" xlink:href="#B"><stop offset="0" stop-color="#bcbabe"></stop><stop offset="1" stop-color="#e8e6e2"></stop></linearGradient></defs><path fill="url(#Bh)" d="M427 476c9-4 18-5 27-4l1 3 1 2h-1c-4-2-11-1-15 0l-1-1c-1 0-4 0-5 1h-3c-7 4-14 10-19 17l-2-1c-3 5-5 11-7 16-2-6 2-11 4-16v-1h0c2-2 7-7 7-9h-4c4-4 9-6 13-8h1 0l3-1 1 1-1 1z"></path><path d="M431 477c6-3 13-4 20-3l4 1 1 2h-1c-4-2-11-1-15 0l-1-1c-1 0-4 0-5 1h-3z" class="Q"></path><path d="M424 475h0l3-1 1 1-1 1-3 2c-6 3-11 9-14 15-3 5-5 11-7 16-2-6 2-11 4-16v-1h0c2-2 7-7 7-9h-4c4-4 9-6 13-8h1z" class="M"></path><defs><linearGradient id="Bi" x1="423.619" y1="505.736" x2="414.114" y2="500.838" xlink:href="#B"><stop offset="0" stop-color="#c1bfbf"></stop><stop offset="1" stop-color="#f3f2ef"></stop></linearGradient></defs><path fill="url(#Bi)" d="M431 477h3c1-1 4-1 5-1l1 1c4-1 11-2 15 0h1l1 3 5 11 1 2c-3 0-6 0-9 1-4 1-8 3-11 5-12 9-23 22-28 37l-5 22c-1 8-1 16 0 24-1-2-1-4-1-5-1-5-1-9-1-14v2c0 2-1 2-1 3h-1v-2c-1-1-1-2 0-3-1-1-1-2-1-4v11l-2-1v-4-13c0-3 0-7-2-10l1-6c-1-2-1-4-2-6 0-6 2-14 3-21 2-5 4-11 7-16l2 1c5-7 12-13 19-17z"></path><path d="M414 507c3-7 10-14 17-17-1 3-2 3-4 5-2 1-3 3-5 5s-5 6-8 7z" class="W"></path><path d="M403 532h0c1 1 1 2 1 3s0 1 1 2c-1 5-1 11-1 16 0 3 1 8 0 10l-1 2h0v-13c0-3 0-7-2-10l1-6 1-4z" class="M"></path><defs><linearGradient id="Bj" x1="404.832" y1="538.122" x2="415.642" y2="537.854" xlink:href="#B"><stop offset="0" stop-color="#030302"></stop><stop offset="1" stop-color="#2e2e2d"></stop></linearGradient></defs><path fill="url(#Bj)" d="M414 508l1 1c0 1 1 2 2 4l-3 8c-5 13-6 28-6 42v2c0 2-1 2-1 3h-1v-2c-1-1-1-2 0-3-1-1-1-2-1-4 1-17 1-34 9-51z"></path><defs><linearGradient id="Bk" x1="448.857" y1="486.945" x2="438.269" y2="494.727" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#353634"></stop></linearGradient></defs><path fill="url(#Bk)" d="M451 487l2 2h4c0 1 1 1 1 1-1 1-1 1-2 1h0-3l-4 1h-1l-2 1h-1c-2 1-3 1-4 3-1 0-2 0-3 1-1 0-2 1-2 1-3 2-5 3-6 5-2 3-4 6-7 7-1 2-2 3-3 4-2 3-3 6-6 7l3-8c-1-2-2-3-2-4l-1-1v-1c3-1 6-5 8-7s3-4 5-5c2-2 3-2 4-5 3-1 6-1 9-2h8 2c1 0 1-1 1-1z"></path><path d="M422 500l1 1c2 0 3-2 5-3h0c-1 2-2 2-2 4l-5 4-1 1c-2 2-3 2-5 2l-1-1v-1c3-1 6-5 8-7z" class="M"></path><path d="M426 502c3-3 7-6 11-8 6-2 11-2 17-4 1 0 1 0 2 1h-3l-4 1h-1l-2 1h-1c-2 1-3 1-4 3-1 0-2 0-3 1-1 0-2 1-2 1-3 2-5 3-6 5-2 3-4 6-7 7-1 2-2 3-3 4-2 3-3 6-6 7l3-8c-1-2-2-3-2-4 2 0 3 0 5-2l1-1 5-4z" class="N"></path><path d="M421 506h1c3 0 5-3 8-5v1h0c-3 2-6 5-7 8h0c-1 2-2 3-3 4-2 3-3 6-6 7l3-8c-1-2-2-3-2-4 2 0 3 0 5-2l1-1z" class="T"></path><path d="M420 507l3 1c-1 1-1 1-1 2l-1 1c0 1-2 1-3 2h-1c-1-2-2-3-2-4 2 0 3 0 5-2z" class="H"></path><defs><linearGradient id="Bl" x1="431.181" y1="515.79" x2="424.131" y2="512.087" xlink:href="#B"><stop offset="0" stop-color="#a09d9f"></stop><stop offset="1" stop-color="#dfdfdb"></stop></linearGradient></defs><path fill="url(#Bl)" d="M462 491l1 2c-3 0-6 0-9 1-4 1-8 3-11 5-12 9-23 22-28 37l-5 22c-1 8-1 16 0 24-1-2-1-4-1-5-1-5-1-9-1-14 0-14 1-29 6-42 3-1 4-4 6-7 1-1 2-2 3-4 3-1 5-4 7-7 1-2 3-3 6-5 0 0 1-1 2-1 1-1 2-1 3-1 1-2 2-2 4-3h1l2-1h1l4-1v1h2c2-1 5 0 7-1z"></path><path d="M462 491l1 2c-3 0-6 0-9 1-4 1-8 3-11 5v-1l2-2c-1-1-1-1 0-1l1-1h1l5-2h1 2c2-1 5 0 7-1z" class="G"></path><path d="M423 510c3-1 5-4 7-7 1-2 3-3 6-5 0 0 1-1 2-1 0 1 0 1-1 2l-9 9c-5 6-11 15-13 22 0 3-1 7-2 10l-1 2-1 2v3 1 1c0 2-2 3-2 5h0v3l1 1c-1 8-1 16 0 24-1-2-1-4-1-5-1-5-1-9-1-14 0-14 1-29 6-42 3-1 4-4 6-7 1-1 2-2 3-4z" class="C"></path><defs><linearGradient id="Bm" x1="412.246" y1="509.458" x2="406.302" y2="506.727" xlink:href="#B"><stop offset="0" stop-color="#787776"></stop><stop offset="1" stop-color="#8d8d8a"></stop></linearGradient></defs><path fill="url(#Bm)" d="M431 477h3c1-1 4-1 5-1l1 1c4-1 11-2 15 0h1l1 3 5 11c-2 1-5 0-7 1h-2v-1h3 0c1 0 1 0 2-1 0 0-1 0-1-1h-4l-2-2-5-1-16 2c-10 4-16 10-19 19-4 9-6 20-6 30-1-1-1-1-1-2s0-2-1-3h0l-1 4c-1-2-1-4-2-6 0-6 2-14 3-21 2-5 4-11 7-16l2 1c5-7 12-13 19-17z"></path><defs><linearGradient id="Bn" x1="412.91" y1="518.222" x2="399.039" y2="506.736" xlink:href="#B"><stop offset="0" stop-color="#bbbebe"></stop><stop offset="1" stop-color="#eeeae9"></stop></linearGradient></defs><path fill="url(#Bn)" d="M410 493l2 1c-6 12-9 24-9 38l-1 4c-1-2-1-4-2-6 0-6 2-14 3-21 2-5 4-11 7-16z"></path><path d="M440 477c4-1 11-2 15 0h1l1 3 5 11c-2 1-5 0-7 1h-2v-1h3 0c1 0 1 0 2-1 0 0-1 0-1-1h-4l-2-2-5-1-16 2c-2 0-3-1-4 0l-4 1c2-2 5-6 7-7s3-1 4-3c1-1 5-2 7-2z" class="B"></path><path d="M445 483c-3 1-5 3-8 2v-1c0-1 0-1 1-2 2-2 7-3 10-3 2 1 4 0 6 0h1 0c-3 1-5 2-8 3 0 0-1 1-2 1z" class="C"></path><path d="M445 483c1 0 2-1 2-1l8-3 2 1 5 11c-2 1-5 0-7 1h-2v-1h3 0c1 0 1 0 2-1 0 0-1 0-1-1h-4l-2-2-5-1-1-3z"></path><defs><linearGradient id="Bo" x1="614.03" y1="499.738" x2="625.699" y2="490.864" xlink:href="#B"><stop offset="0" stop-color="#b9b8b6"></stop><stop offset="1" stop-color="#ecebe8"></stop></linearGradient></defs><path fill="url(#Bo)" d="M592 456c4 0 8 1 11 1v4h1c3 1 5 1 8 3h0c5 3 9 6 13 10h0l-3-4c0-2 0-3-2-4 0-2 1-2 1-3 3 1 6 3 9 6h1l3 4 3 6v3h-1 0-1c1 2 1 3 1 5-1-1-1-2-2-3l-1-1-3-4h0v1h0l3 6c0 1 1 2 1 3v1c0 1 1 2 1 2-1 2-1 2-1 3s1 2 1 3c-1 0-2-1-2-2-1 2 1 5 1 7 1 3 2 8 2 12l-1 3c1 4 2 8 2 12-1-2-2-3-2-4l1-1-2 2-3-14v2h-1c0 3-1 5-1 8l-3-13c0 1-1 2-2 4l-2-5c-1 1-1 1-2 1 0-1-1-1-1-2l-2-1h-1c-3-4-7-7-11-10l-2-1-3-2-6-2c-4-1-8 0-12-1l-3 1-4 1v-1l2-4 6-12 1-2 1-3 1-3 6-12z"></path><path d="M613 474c5 2 12 7 14 11v1c0 2 0 3 1 4l1 1v4c-3-4-4-8-7-11-2-2-5-5-8-7v-1h-1v-2z" class="M"></path><defs><linearGradient id="Bp" x1="600.197" y1="466.324" x2="606.803" y2="478.176" xlink:href="#B"><stop offset="0" stop-color="#2f2f2a"></stop><stop offset="1" stop-color="#626263"></stop></linearGradient></defs><path fill="url(#Bp)" d="M588 469c4-1 8-1 12 0s10 2 13 5v2h1v1c-7-4-18-8-27-6h-2l1-3 1 1h1z"></path><path d="M626 480c3 3 6 6 8 10 0 1 1 2 1 2-1 2-1 2-1 3s1 2 1 3c-1 0-2-1-2-2-1 2 1 5 1 7 1 3 2 8 2 12l-1 3c-2-8-3-16-6-23v-4l-1-1c-1-1-1-2-1-4 1 2 2 3 4 5-1-4-3-7-5-10h0v-1z" class="F"></path><path d="M606 464c2 0 3 1 5 2h-3c1 2 4 3 6 4 3 3 7 5 10 7 0 1 2 2 2 3v1h0c2 3 4 6 5 10-2-2-3-3-4-5v-1c-2-4-9-9-14-11-3-3-9-4-13-5l1-1h3 1l-2-1c1-2 2-2 3-3z" class="S"></path><defs><linearGradient id="Bq" x1="592.4" y1="456.537" x2="598.788" y2="462.059" xlink:href="#B"><stop offset="0" stop-color="#434241"></stop><stop offset="1" stop-color="#5b5b58"></stop></linearGradient></defs><path fill="url(#Bq)" d="M592 456c4 0 8 1 11 1v4h1c3 1 5 1 8 3h0c1 3 4 3 6 5h0c-2 0-7-2-7-3-2-1-3-2-5-2-1 1-2 1-3 3l2 1h-1-3l-1 1c-4-1-8-1-12 0h-1l-1-1 6-12z"></path><path d="M593 467l2-2h1 0l1-1c-2 0-4 1-6 1h0v-1c3-1 7-1 11-1v-1h-2c-1 0-2-1-3-1h-1 7 1c3 1 5 1 8 3h0c1 3 4 3 6 5h0c-2 0-7-2-7-3-2-1-3-2-5-2-1 1-2 1-3 3l2 1h-1-3l-1 1c-4-1-8-1-12 0v-1c1 0 3-1 5-1z" class="O"></path><path d="M593 467h5c1-1 2-1 4-2 1 0 1 0 1-1h3c-1 1-2 1-3 3l2 1h-1-3l-1 1c-4-1-8-1-12 0v-1c1 0 3-1 5-1z" class="G"></path><path d="M621 463c3 1 6 3 9 6h1l3 4 3 6v3h-1 0-1c1 2 1 3 1 5-1-1-1-2-2-3l-1-1-3-4h0v1h0l3 6c0 1 1 2 1 3v1c-2-4-5-7-8-10 0-1-2-2-2-3-3-2-7-4-10-7-2-1-5-2-6-4h3c0 1 5 3 7 3h0c-2-2-5-2-6-5 5 3 9 6 13 10h0l-3-4c0-2 0-3-2-4 0-2 1-2 1-3z" class="I"></path><path d="M634 473l3 6v3h-1 0-1 0c-2-3-2-6-1-9z" class="N"></path><path d="M609 478c11 6 18 17 21 29 0 1 1 4 1 6v2h-1c0 3-1 5-1 8l-3-13c0 1-1 2-2 4l-2-5c-2-7-7-12-13-17l1-2v-1h5l1 1 1-1c-1-2-4-5-6-6-1-2-1-3-2-5z" class="O"></path><path d="M610 490c9 4 13 11 16 20 0 1-1 2-2 4l-2-5c-2-7-7-12-13-17l1-2z" class="I"></path><path d="M584 474c7-3 14-1 21 2l4 2c1 2 1 3 2 5 2 1 5 4 6 6l-1 1-1-1h-5v1l-1 2c-2-1-4-4-6-5-3-1-6-1-9-3v-1c-3-2-5-3-9-2-2 0-2 1-3 2l-2 4-3 1 6-12 1-2z" class="F"></path><path d="M584 474c7-3 14-1 21 2l4 2c1 2 1 3 2 5-3-2-6-5-9-6-5-1-10-3-15-2l-4 1 1-2z" class="Z"></path><path d="M582 483c-1-1-1-1 0-2 0-1 2-2 4-3 4-1 10 1 13 3 4 3 6 6 11 8v1l-1 2c-2-1-4-4-6-5-3-1-6-1-9-3v-1c-3-2-5-3-9-2-2 0-2 1-3 2z" class="C"></path><path d="M585 481c4-1 6 0 9 2v1c3 2 6 2 9 3 2 1 4 4 6 5 6 5 11 10 13 17-1 1-1 1-2 1 0-1-1-1-1-2l-2-1h-1c-3-4-7-7-11-10l-2-1-3-2-6-2c-4-1-8 0-12-1l-3 1-4 1v-1l2-4 3-1 2-4c1-1 1-2 3-2z" class="L"></path><path d="M598 490c3 1 5 1 8 2l4 4h-2c-2-1-5-2-8-2l-6-2c1-1 2-1 4-1h0v-1z" class="R"></path><path d="M600 494c3 0 6 1 8 2h2c2 2 5 3 5 6 2 2 2 2 2 5h-1c-3-4-7-7-11-10l-2-1-3-2z" class="W"></path><path d="M600 494c3 0 6 1 8 2h2c2 2 5 3 5 6-3-2-5-4-8-6l-2 1-2-1-3-2z" class="M"></path><path d="M580 487v1c2 1 5 0 7 1 1-1 1-1 2-1v1 1h9 0v1h0c-2 0-3 0-4 1-4-1-8 0-12-1l-3 1-4 1v-1l2-4 3-1z" class="E"></path><path d="M585 481c4-1 6 0 9 2v1c0 1-1 2-1 3h1c1 0 2 1 3 1h0-1-4-3c-1 0-1 0-2 1-2-1-5 0-7-1v-1l2-4c1-1 1-2 3-2z" class="P"></path><path d="M582 483c1-1 1-2 3-2 1 2-1 4 0 6l7 1h-3c-1 0-1 0-2 1-2-1-5 0-7-1v-1l2-4z" class="B"></path><defs><linearGradient id="Br" x1="628.244" y1="490.676" x2="648.915" y2="467.894" xlink:href="#B"><stop offset="0" stop-color="#aeaea8"></stop><stop offset="1" stop-color="#d0cecf"></stop></linearGradient></defs><path fill="url(#Br)" d="M622 416l3 1c3 0 5 1 7 3 1 1 1 1 2 1l3 3v1c3 3 7 5 10 8l3 3 1-1c3 2 6 5 7 9h0c1 4 5 5 6 9h0 1l-2-5c1-1 2-2 4-2l-1 1h1v2c1 1 1 2 2 2l2 9c1 10 1 20 0 30v8l-4 12c0 1-1 1-2 2l-4 12h-1l-5 11v1c-1-1-1-1-1-2 1-10 0-20-2-30h-2v-1c-1 0-2 0-3 1v-1h-2c0 1-1 1-1 2-1-9-3-17-7-26l-3-6-3-4h-1c-3-3-6-5-9-6 0 1-1 1-1 3 2 1 2 2 2 4l3 4h0c-4-4-8-7-13-10h0c-3-2-5-2-8-3h-1v-4c-3 0-7-1-11-1 0-1 0-2 1-3l2-4 3-6c0-1 1-2 1-3l1-3c1-1 1-2 2-3l3-6v-1l3-5c0-1 0-3 1-4 3 0 5-1 8-1h3c1 0 1-1 2-1z"></path><path d="M633 459c3 3 6 6 8 10 0 0 1 1 1 2s-1 2-1 2l-5-7c1-1 1-1 1-2l-4-4v-1z" class="Q"></path><path d="M642 471c3 5 6 11 7 18v-1c-1-1-1 0-1-1l-1-1v-1-1h0c0-1-1-2-1-3 0 3 2 6 2 10-2-6-4-12-7-18 0 0 1-1 1-2z" class="Y"></path><path d="M648 491c0-4-2-7-2-10 0 1 1 2 1 3h0v1 1l1 1c0 1 0 0 1 1v1l3 15h-2v-1l-2-12z" class="E"></path><path d="M626 452c2 2 5 4 7 7v1l4 4c0 1 0 1-1 2-4-5-8-7-13-11h0c1-1 2-1 3-3z" class="R"></path><path d="M654 490h1c0 1 0 1 1 2l1-1c1 0 1 1 1 2l2 12c-1-1-2-1-4-1v2l-2-16z" class="B"></path><path d="M656 506v-2c2 0 3 0 4 1l-1 19h1l-5 11v-3c2-8 1-17 1-26z"></path><defs><linearGradient id="Bs" x1="607.945" y1="441.909" x2="616.698" y2="456.326" xlink:href="#B"><stop offset="0" stop-color="#161413"></stop><stop offset="1" stop-color="#474846"></stop></linearGradient></defs><path fill="url(#Bs)" d="M598 443h7c8 2 15 4 21 9-1 2-2 2-3 3h0c-4-2-7-3-11-5s-10-2-14-1c-1 0-2 1-3 1v-1l3-6z"></path><path d="M598 443h7c-2 3-5 3-7 6-1 0-2 1-3 1v-1l3-6z" class="B"></path><defs><linearGradient id="Bt" x1="627.53" y1="465.487" x2="638.595" y2="454.022" xlink:href="#B"><stop offset="0" stop-color="#090907"></stop><stop offset="1" stop-color="#51504d"></stop></linearGradient></defs><path fill="url(#Bt)" d="M604 437c7 0 13 3 20 5h1 2c2-1 2-1 5-1 4 4 9 7 13 12 2 2 3 5 5 7 3 5 7 12 8 18-2 3 2 8 1 11v-1c-1-2-2-6-3-8l-1-1c1 5 3 9 3 14 0-1 0-2-1-2l-1 1c-1-1-1-1-1-2h-1c-4-14-8-26-18-36-9-7-23-15-35-14h-1-1l1-3h4z"></path><defs><linearGradient id="Bu" x1="637.738" y1="463.573" x2="645.925" y2="453.351" xlink:href="#B"><stop offset="0" stop-color="#9b9a98"></stop><stop offset="1" stop-color="#dcdbd8"></stop></linearGradient></defs><path fill="url(#Bu)" d="M624 442h1 2c2-1 2-1 5-1 4 4 9 7 13 12 2 2 3 5 5 7 3 5 7 12 8 18-2 3 2 8 1 11v-1c-1-2-2-6-3-8l-1-1c-7-17-15-28-31-37z"></path><defs><linearGradient id="Bv" x1="626.236" y1="485.363" x2="639.552" y2="468.93" xlink:href="#B"><stop offset="0" stop-color="#a7a8a0"></stop><stop offset="1" stop-color="#dedcdc"></stop></linearGradient></defs><path fill="url(#Bv)" d="M598 449c4-1 10-1 14 1s7 3 11 5c5 4 9 6 13 11l5 7c3 6 5 12 7 18l2 12c-1 0-2 0-3 1v-1h-2c0 1-1 1-1 2-1-9-3-17-7-26l-3-6-3-4h-1c-3-3-6-5-9-6 0 1-1 1-1 3 2 1 2 2 2 4l3 4h0c-4-4-8-7-13-10h0c-3-2-5-2-8-3h-1v-4c-3 0-7-1-11-1 0-1 0-2 1-3l2-4v1c1 0 2-1 3-1z"></path><path d="M620 458c4 3 7 6 11 9v2h-1c-3-3-6-5-9-6-2-2-4-3-6-4l5-1z" class="R"></path><path d="M598 449c4-1 10-1 14 1v1l2 2h0c-3-1-8-3-11-2h0-8v-1c1 0 2-1 3-1z" class="S"></path><defs><linearGradient id="Bw" x1="597.777" y1="456.472" x2="615.863" y2="453.33" xlink:href="#B"><stop offset="0" stop-color="#3b393b"></stop><stop offset="1" stop-color="#5e5f59"></stop></linearGradient></defs><path fill="url(#Bw)" d="M593 453l2-4v1 1h8c6 2 12 3 17 7l-5 1c-5-2-11-5-16-5-2-1-4 0-6-1z"></path><defs><linearGradient id="Bx" x1="639.841" y1="501.546" x2="644.02" y2="481.13" xlink:href="#B"><stop offset="0" stop-color="#090a0a"></stop><stop offset="1" stop-color="#3e3b38"></stop></linearGradient></defs><path fill="url(#Bx)" d="M631 467c10 11 13 22 16 36h-2c0 1-1 1-1 2-1-9-3-17-7-26l-3-6-3-4v-2z"></path><defs><linearGradient id="By" x1="605.578" y1="451.737" x2="609.642" y2="466.565" xlink:href="#B"><stop offset="0" stop-color="#b5b3b5"></stop><stop offset="1" stop-color="#d1d2c6"></stop></linearGradient></defs><path fill="url(#By)" d="M592 456c0-1 0-2 1-3 2 1 4 0 6 1 5 0 11 3 16 5 2 1 4 2 6 4 0 1-1 1-1 3 2 1 2 2 2 4l3 4h0c-4-4-8-7-13-10h0c-3-2-5-2-8-3h-1v-4c-3 0-7-1-11-1z"></path><path d="M603 457c6 3 13 6 17 11 1 0 2 1 2 2l3 4h0c-4-4-8-7-13-10h0c-3-2-5-2-8-3h-1v-4z" class="L"></path><path d="M622 416l3 1c3 0 5 1 7 3 1 1 1 1 2 1l3 3v1c3 3 7 5 10 8l3 3 1-1c3 2 6 5 7 9h0c1 4 5 5 6 9h0 1l-2-5c1-1 2-2 4-2l-1 1h1v2c1 1 1 2 2 2l2 9c1 10 1 20 0 30v8l-4 12c0 1-1 1-2 2l-4 12h-1-1l1-19-2-12c0-5-2-9-3-14l1 1c1 2 2 6 3 8v1c1-3-3-8-1-11-1-6-5-13-8-18-2-2-3-5-5-7-4-5-9-8-13-12-3 0-3 0-5 1h-2-1c-7-2-13-5-20-5h-4c1-1 1-2 2-3l3-6v-1l3-5c0-1 0-3 1-4 3 0 5-1 8-1h3c1 0 1-1 2-1z" class="I"></path><path d="M651 435c3 2 6 5 7 9h0l-1 2c-2-3-4-7-7-10l1-1z" class="U"></path><path d="M639 430c4 4 9 7 12 12h-2c0-1-2-2-3-3-2-2-6-4-7-7v-2z" class="W"></path><path d="M637 428l2 2v2c1 3 5 5 7 7l-2 2v-1h-2l-3-3c0-1 0-2-1-3l-4-3h2c0-1 0-2 1-3z" class="L"></path><path d="M628 424h0c3 1 6 3 9 4-1 1-1 2-1 3h-2c-3-2-7-4-12-6h5l1-1z" class="R"></path><defs><linearGradient id="Bz" x1="654.128" y1="456.086" x2="646.81" y2="438.941" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#6e6d6c"></stop></linearGradient></defs><path fill="url(#Bz)" d="M646 439c1 1 3 2 3 3h2c5 7 9 16 11 25-1-1-1-2-1-3-1-2-1-3-2-5h-1l1-1-1-1h-1v2 2c0 2 1 2 1 3l-3-3c-1-2-2-3-2-5 0-4-8-13-11-16h2v1l2-2z"></path><path d="M657 446l1-2c1 4 5 5 6 9h0 1l-2-5c1-1 2-2 4-2l-1 1h1v2c1 1 1 2 2 2l2 9c1 10 1 20 0 30v8l-4 12c0 1-1 1-2 2l1-29v-1-3l-1-6c-1-4-3-9-4-14-1-3-1-6-2-9-1-1-1-3-2-4z" class="D"></path><path d="M669 494c1-2 0-2 2-4v8l-4 12c0-3 1-6 1-8 1-3 1-5 1-8z"></path><path d="M659 450v-1l4 9c1 1 1 1 1 2 3 4 2 9 3 14v4c0 1 0 2 1 3v2h-1l-1-1v-3l-1-6c-1-4-3-9-4-14-1-3-1-6-2-9z" class="G"></path><defs><linearGradient id="CA" x1="670.455" y1="467.202" x2="664.378" y2="467.321" xlink:href="#B"><stop offset="0" stop-color="#010100"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#CA)" d="M663 448c1-1 2-2 4-2l-1 1h1v2c1 1 1 2 2 2l2 9c1 10 1 20 0 30-2 2-1 2-2 4 0-9 0-19-2-28 0-5-1-9-2-13l-2-5z"></path><path d="M650 453l3 3c0 2 1 3 2 5l3 3c0-1-1-1-1-3v-2-2h1l1 1-1 1h1c1 2 1 3 2 5 0 1 0 2 1 3l4 16-1 29-4 12h-1-1l1-19-2-12c0-5-2-9-3-14l1 1c1 2 2 6 3 8v1c1-3-3-8-1-11-1-6-5-13-8-18 0-3-1-4 0-7z"></path><path d="M650 453l3 3c0 2 1 3 2 5l-2-2c0 1 0 1 1 2 0 1 1 2 1 4h-1c1 1 2 3 3 4v-1c2 3 2 5 2 8v2h-1c-1-6-5-13-8-18 0-3-1-4 0-7z" class="B"></path><path d="M658 478h1v1c0 4 2 8 2 12 1 4 1 9 1 13 0 6 0 12-1 18v2h-1-1l1-19-2-12c0-5-2-9-3-14l1 1c1 2 2 6 3 8v1c1-3-3-8-1-11z" class="G"></path><path d="M622 416l3 1c3 0 5 1 7 3 1 1 1 1 2 1l3 3v1l-4-2c-2 0-3 0-5 1h0l-1 1h-5c5 2 9 4 12 6l4 3c1 1 1 2 1 3l3 3c3 3 11 12 11 16l-3-3c-1 3 0 4 0 7-2-2-3-5-5-7-4-5-9-8-13-12-3 0-3 0-5 1h-2-1c-7-2-13-5-20-5h-4c1-1 1-2 2-3l3-6v-1l3-5c0-1 0-3 1-4 3 0 5-1 8-1h3c1 0 1-1 2-1z" class="H"></path><path d="M627 434l5 3c-1 1-2 1-4 1l-3-1-1-1h1c0-1 1-2 2-2z" class="F"></path><path d="M632 437c6 3 12 8 16 13 1 1 2 2 2 3-1 3 0 4 0 7-2-2-3-5-5-7-4-5-9-8-13-12l-4-3c2 0 3 0 4-1z" class="R"></path><path d="M605 427l2-1c4-2 11-2 15-1 5 2 9 4 12 6l4 3c1 1 1 2 1 3l-10-6-1-1c-2-1-5-1-7-1s-4-1-6 0c-1 0-3-1-4-1h-6v-1z" class="X"></path><defs><linearGradient id="CB" x1="620.381" y1="415.166" x2="624.727" y2="424.952" xlink:href="#B"><stop offset="0" stop-color="#878585"></stop><stop offset="1" stop-color="#a3a3a0"></stop></linearGradient></defs><path fill="url(#CB)" d="M622 416l3 1c3 0 5 1 7 3 1 1 1 1 2 1l3 3v1l-4-2c-2 0-3 0-5 1h0l-1 1h-5c-4-1-11-1-15 1l-2 1 3-5c0-1 0-3 1-4 3 0 5-1 8-1h3c1 0 1-1 2-1z"></path><path d="M608 422c6-3 13 0 20 2l-1 1h-5c-4-1-11-1-15 1l-2 1 3-5z" class="B"></path><path d="M605 428h6c1 0 3 1 4 1 4 1 8 2 12 5-1 0-2 1-2 2h-1l1 1 3 1 4 3c-3 0-3 0-5 1h-2-1c-7-2-13-5-20-5h-4c1-1 1-2 2-3l3-6z" class="S"></path><path d="M602 434h0c5-2 6-2 11-1-1 0-2 1-4 1v1c2 1 4 1 6 2h-1c-3-1-6-1-10-1v1h-4c1-1 1-2 2-3z" class="O"></path><path d="M604 437v-1c4 0 7 0 10 1 2 1 7 3 8 2s2-1 2-2h1l3 1 4 3c-3 0-3 0-5 1h-2-1c-7-2-13-5-20-5z" class="C"></path><defs><linearGradient id="CC" x1="607.647" y1="426.114" x2="620.89" y2="438.422" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2e2a"></stop></linearGradient></defs><path fill="url(#CC)" d="M605 428h6c1 0 3 1 4 1 4 1 8 2 12 5-1 0-2 1-2 2h-1l-11-3c-5-1-6-1-11 1h0l3-6z"></path><path d="M511 643c11-1 21 1 30 8 7 7 12 16 13 26 0 8-1 15-4 22-2 3-3 4-4 6 0 4-4 7-7 9s-6 4-10 6c-7 2-16 3-23 1l-1-1c-1 0-3-1-4-1-3-2-7-5-10-6-2-2-5-4-6-6-2-4-4-7-5-10v-3-2c-3-10-2-22 4-31 6-10 15-15 27-18z"></path><path d="M534 693s0-1 1-1h2c1 1 1 2 2 3 0 1 1 2 1 3s-1 4-1 5c-1 1-2 1-3 2l1-3c1-3 1-4-1-7 0-1-1-1-2-2z" class="B"></path><path d="M520 693h-1c-1-2-2-5-1-7 1-3 2-5 4-6 1-1 2-1 3 0 2 0 4 2 5 3 1 3 1 5 0 7l-1-1v-2-1h-1c0-2-1-3-3-4h-2c-1 0-2 1-3 3v8z" class="L"></path><path d="M491 713c-2-2-5-4-6-6-2-4-4-7-5-10v-3c5 11 12 19 23 23l2 1-1 1 1 1c-1 0-3-1-4-1-3-2-7-5-10-6z" class="U"></path><path d="M546 705c0 4-4 7-7 9s-6 4-10 6c-7 2-16 3-23 1l-1-1-1-1 1-1c4 1 7 2 11 2 13-1 21-6 30-15z" class="N"></path><path d="M520 693v-8c1-2 2-3 3-3h2c2 1 3 2 3 4h1v1 2l1 1c-2 2-3 4-6 4-1 1-3 0-4-1h0z" class="W"></path><defs><linearGradient id="CD" x1="500.76" y1="685.345" x2="484.545" y2="685.874" xlink:href="#B"><stop offset="0" stop-color="#a9a8a5"></stop><stop offset="1" stop-color="#cccbc8"></stop></linearGradient></defs><path fill="url(#CD)" d="M492 659c2 1 3 0 5 2h1c-6 7-10 15-9 25 1 7 5 13 10 18 4 3 9 3 13 4 3-1 5-3 8-3 1 2 1 5 2 6s2 1 2 1l-3 1c-6 1-11 1-16-1-3-1-6-3-8-5-8-5-12-14-13-23s2-17 8-25z"></path><path d="M505 712c4-1 8-2 13-4 1 2 2 4 3 5-6 1-11 1-16-1z" class="L"></path><defs><linearGradient id="CE" x1="518.202" y1="684.918" x2="489.338" y2="683.146" xlink:href="#B"><stop offset="0" stop-color="#cacac1"></stop><stop offset="1" stop-color="#f7f8e6"></stop></linearGradient></defs><path fill="url(#CE)" d="M498 661c3 3 6 6 8 10-2 3-3 5-3 8h2c-2 6-1 13 2 18 2 3 6 6 10 7 3 0 5-1 7-1 3-1 5-3 7-5l3-5c1 1 2 1 2 2 2 3 2 4 1 7l-1 3c-3 3-7 6-12 7 0 0-1 0-2-1s-1-4-2-6c-3 0-5 2-8 3-4-1-9-1-13-4-5-5-9-11-10-18-1-10 3-18 9-25z"></path><path d="M531 698c1 1 2 1 2 2 0 2 0 4-2 6-1 2-3 4-5 4h-1c-1-2-1-5-1-7 3-1 5-3 7-5z" class="H"></path><defs><linearGradient id="CF" x1="527.994" y1="651.14" x2="517.885" y2="667.476" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#6c6c6b"></stop></linearGradient></defs><path fill="url(#CF)" d="M492 659c7-7 15-11 25-11 8 0 16 4 21 10 3 3 7 8 7 12 0 2-1 3-2 4-2 0-4 0-5-1-3 0-5-1-8-2-1-1-4-3-5-3s-1 1-2 1c-3-1-5-1-8 0-4 1-7 3-9 7l-1 3h-2c0-3 1-5 3-8-2-4-5-7-8-10h-1c-2-2-3-1-5-2z"></path><path d="M512 657c2 0 4 1 5 2l1 1c1 1 1 2 2 3v1l-1-1-1 1c0 1 0 1-1 2h-1c1 1 0 1 1 0h1 0 3c-1 1-3 0-4 1-1 0-2 1-2 2-4 1-7 3-9 7l-1 3h-2c0-3 1-5 3-8 4-3 7-4 12-6-2-4-3-6-6-8z" class="L"></path><defs><linearGradient id="CG" x1="512.085" y1="658.231" x2="502.819" y2="665.647" xlink:href="#B"><stop offset="0" stop-color="#8a8985"></stop><stop offset="1" stop-color="#aaa9a5"></stop></linearGradient></defs><path fill="url(#CG)" d="M497 661c2-1 4-3 6-4 3-1 7-1 9 0 3 2 4 4 6 8-5 2-8 3-12 6-2-4-5-7-8-10h-1z"></path></svg>