<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="149 111 759 787"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#c1bfc0}.C{fill:#d7d6d6}.D{fill:#cdcccc}.E{fill:#a8a7a7}.F{fill:#a4a3a3}.G{fill:#deddde}.H{fill:#e5e4e4}.I{fill:#8f8e8e}.J{fill:#929191}.K{fill:#e4e4e4}.L{fill:#686868}.M{fill:#b1b0b0}.N{fill:#abaaaa}.O{fill:#c7c6c6}.P{fill:#2f2f2f}.Q{fill:#f0f0f0}.R{fill:#969595}.S{fill:#706f70}.T{fill:#7e7c7d}.U{fill:#807f7f}.V{fill:#131313}.W{fill:#f7f6f6}.X{fill:#464546}.Y{fill:#4e4e4e}.Z{fill:#5e5d5e}.a{fill:#3d3c3d}.b{fill:#565555}.c{fill:#f5f4f4}</style><path d="M582 772h1c-1 2-1 2-2 3l-2-1c0-1 2-1 3-2z" class="V"></path><path d="M434 749l3 1 1 3c-1 0-2 1-3 1l-1-5z" class="J"></path><path d="M816 459c2-1 4-2 5-3 1 1 2 1 2 2l-5 2h-1l-1-1z" class="E"></path><path d="M812 464v-1c1-2 2-3 4-4l1 1h1c-2 2-4 3-6 5v-1z" class="F"></path><path d="M433 745v-8l1-1c1 1 2 1 3 2v7c0-2 0-3-1-5h-2l-1 5z" class="X"></path><path d="M233 317l1 1-1 1c1-1 2-2 3-2l-1 2-6 6h-1c1-3 3-6 5-8z" class="B"></path><path d="M488 811c1 2 2 3 2 5v1c-3-2-6-3-9-5h6l1-1z" class="P"></path><path d="M745 291c5 1 10 1 15 3h-1c-2 0-4 0-7-1v2c-3-1-5-2-7-4zM479 806l4 2c-2 2-3 2-5 2h-1c-1 0-2-1-4-2 2 0 4 0 6-2z" class="b"></path><path d="M467 279l8 1h0-3v2l3 2h-3c-1-1-1-2-2-2l-3 1v-4z" class="E"></path><path d="M223 532c2-1 2 0 4 0 2 1 4 3 5 4l1 2c-4-1-7-4-10-6z" class="F"></path><path d="M467 803h0c2 1 3 1 4 2h2 1l2-1 3 2c-2 2-4 2-6 2-2-2-4-3-6-5z" class="L"></path><path d="M483 808c2 1 3 2 5 3l-1 1h-6c-1 0-2-1-4-2h1c2 0 3 0 5-2z" class="X"></path><path d="M787 542c0 1 0 2 1 3 1-1 1-1 2-1l2-1c-3 3-6 5-10 6v-2l5-5z" class="S"></path><path d="M813 474c-1 0-2-1-2-2 0-2 0-3 1-4 1 2 3 3 5 5l-1 2c-1 0-2 0-3-1z" class="L"></path><path d="M200 522s-1 0-1-1h0c2-1 6-1 8-1l3 2-2 1c-2-1-5-1-8-1z" class="B"></path><path d="M200 522c3 0 6 0 8 1 3 1 4 2 5 4-4-1-8-2-13-5z" class="U"></path><path d="M220 469v2c0 1-1 2-2 3h-1c-1 2-2 3-5 3h-1c3-3 6-6 9-8z" class="L"></path><path d="M403 682c3 0 6 1 9 2 2 1 3 1 5 3l-17-4 3-1z" class="R"></path><path d="M593 747l1-4h3v2l-1 10v-3l-3-3v-2z" class="T"></path><path d="M815 598v-1c1-2 1-5 0-8-2-3-6-5-9-5l-3-1c4 0 8 0 11 4 3 2 4 4 4 7 0 2-1 3-1 5l-2-1z" class="K"></path><path d="M207 536c-1-4-2-7-2-10l5 2 1 5h-3c-1 1-1 2-1 3z" class="R"></path><defs><linearGradient id="A" x1="434.605" y1="740.975" x2="434.832" y2="746.486" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M433 745l1-5h2c1 2 1 3 1 5v5l-3-1-1-4z"></path><path d="M593 747v-9c1-2 2-2 4-2v1 8-2h-3l-1 4z" class="X"></path><path d="M817 473c2 2 3 4 5 7-2 0-3 1-4 2l-5-8c1 1 2 1 3 1l1-2z" class="J"></path><path d="M464 279c0 1 3 0 3 0v4 1c-1 1-3 1-4 1-2-1-2-1-2-3v-1l3-2z" class="M"></path><path d="M461 282v-1l3-2c0 1 1 1 2 2 0 1 0 1-1 2-2 0-3-1-4-1z" class="E"></path><path d="M456 276c4 1 7-2 11 1-3 1-7 4-10 4h-1v-3-2z" class="D"></path><path d="M427 270c-2-1-4-3-5-5-2-4-2-7-1-10 1-2 2-5 4-6-1 4-4 6-3 11 1 3 3 6 6 8h1 0c-2 0-3 0-4-1l2 2v1z" class="c"></path><path d="M509 447v1c2 2 1 4 2 7v-1-2-2l2 17v1l-1-1-1 1v-2l-2-19z" class="B"></path><path d="M241 309c2-2 3-3 5-4 0 1 0 2 1 2l-2 1-3 3-6 6h0c-1 0-2 1-3 2l1-1-1-1c2-3 6-6 8-8z" class="H"></path><path d="M241 309c2-2 3-3 5-4 0 1 0 2 1 2l-2 1-3 3c0-1 1-1 1-2 1-1 1 0 1-1-1 0-1 1-2 1h-1z" class="F"></path><path d="M631 714l14-8c3-1 6-3 9-4l-20 13s0-1 1-1v-1c-1 1-2 1-4 1h0z" class="W"></path><path d="M624 682h2c2 0 3 1 4 1l-2 1c-5 1-10 2-15 4-2 1-4 2-7 3 1-2 2-2 4-4 0 0 2-1 3-1 4-1 7-3 11-4z" class="T"></path><path d="M624 682h2c2 0 3 1 4 1l-2 1-4-2z" class="U"></path><defs><linearGradient id="C" x1="554.09" y1="808.814" x2="555.069" y2="802.691" xlink:href="#B"><stop offset="0" stop-color="#5e5d5d"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#C)" d="M560 800c0 2-2 2-2 4l4 1-8 5c0-1 0-1-1-2-1 0-2 1-4 1l-1-1 12-8z"></path><path d="M821 456c5-1 9-2 13 0 3 1 7 5 8 8 1 2 0 5 0 8-2-6-1-10-7-14-3-2-8-1-12 0 0-1-1-1-2-2z" class="D"></path><defs><linearGradient id="D" x1="822.854" y1="524.701" x2="830.437" y2="519.304" xlink:href="#B"><stop offset="0" stop-color="#858485"></stop><stop offset="1" stop-color="#a8a6a7"></stop></linearGradient></defs><path fill="url(#D)" d="M823 520c4 0 8 0 12 1l-12 6c-1-2-1-3-1-4s0-1 1-2v-1z"></path><path d="M463 253c2-2 4-3 8-4l1 1 1 3c-1 1-2 1-3 1-2 1-4 3-6 5 0-2-1-4-1-6z" class="S"></path><path d="M429 268l3 1c3 0 6-1 8-2 2-2 4-5 4-8v-3h1c1 3 1 6-1 9s-5 5-9 6c-2 1-5 0-8-1v-1l-2-2c1 1 2 1 4 1h0z" class="W"></path><path d="M466 821c-1-3-4-7-6-10-2-4-3-8-4-12 4 7 7 12 12 17 0 2 1 4 0 5h-1-1z" class="C"></path><path d="M587 256s0 6 1 7c0 2 1 4 4 5 2 1 4 2 7 1 3-2 5-5 7-9h0 0v2c-1 3-2 6-5 8-2 1-5 2-7 1h-1-1c-3-1-4-4-5-6-2-3-1-6 0-9z" class="Q"></path><path d="M339 765l-3 3c-2 3-5 4-9 4-3 0-6-1-8-4s-3-6-3-10h1c0 2 1 5 2 7 1 3 4 4 6 5 2 0 5 0 7-1 2-2 3-5 4-9v1c0 2-1 5-1 7h0l1-1c0-1 2-2 3-2z" class="W"></path><path d="M513 467c2 3 1 6 2 9 0 1 0 2-1 3h0v6l1 5v-1-6c0-1 0-1 1-2v-2c0-2 0-2 1-3 0 7-1 15-2 22l-4-30 1-1 1 1v-1z" class="D"></path><defs><linearGradient id="E" x1="545.027" y1="816.777" x2="548.973" y2="806.723" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#E)" d="M548 808l1 1c2 0 3-1 4-1 1 1 1 1 1 2-5 3-10 5-15 8 0-2 2-6 3-7s4-2 6-3z"></path><path d="M529 175l1 1c0 1 1 1 1 2 3 4 9 7 13 10h-1-4c-2-1-3-2-5-3-1 0-3-2-4-2v-1c-1-1-2-2-2-4l1-3z" class="D"></path><path d="M610 685c6-3 12-4 18-6 2 0 3 1 4 1h1l-2 1h0 3l1 2h-5c-1 0-2-1-4-1h-2c-4 1-7 3-11 4-1 0-3 1-3 1-1 0-2 0-3-1l3-1z" class="H"></path><path d="M626 682l8-1 1 2h-5c-1 0-2-1-4-1z" class="I"></path><path d="M817 599c-1 3-3 5-6 7-3 1-6 1-8 0-4-1-5-5-7-8-1-3-2-7-1-10v5c2 5 5 9 8 11l1-2h1 0l1 2h0 3c2 0 5-3 6-6h0l2 1z" class="C"></path><path d="M228 526l4 4 3 3c1 3 2 4 4 6 2 1 2 3 3 4l-9-5-1-2c-1-1-3-3-5-4l-2-1 2-1 2 2v-2c0-1-1-2-1-3v-1z" class="R"></path><path d="M542 357l2 1c1 2 1 4 2 6 0 2 0 5-1 6l-3-2c-2-3-5-5-5-8 1-2 3-2 5-3z" class="W"></path><defs><linearGradient id="F" x1="210.877" y1="523.185" x2="222.32" y2="530.771" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#F)" d="M210 522c2 0 4 2 6 3h3v-1-1l8 7-2 1 2 1c-2 0-2-1-4 0 0 0-2-1-3-1-2-2-4-3-7-4-1-2-2-3-5-4l2-1z"></path><path d="M219 523l8 7-2 1c-2-3-6-4-9-6h3v-1-1z" class="S"></path><defs><linearGradient id="G" x1="469.739" y1="269.247" x2="462.511" y2="276.5" xlink:href="#B"><stop offset="0" stop-color="#939192"></stop><stop offset="1" stop-color="#b2b1b2"></stop></linearGradient></defs><path fill="url(#G)" d="M456 276c1-1 2-3 4-3 4-2 9-2 14-3v4c-2 2-5 2-7 3-4-3-7 0-11-1z"></path><path d="M530 182v1c1 0 3 2 4 2 2 1 3 2 5 3h4c-2 2-5 3-8 4l-1 1c-1-1-3-2-3-4-2-2-2-4-4-6 1 0 2 0 3-1z" class="C"></path><path d="M534 185c2 1 3 2 5 3h4c-2 2-5 3-8 4l-3-6h0c2 0 2 2 4 3h1v-1l-3-3z" class="E"></path><path d="M686 756c1-1 1-2 1-3v4c1 2 2 5 4 7l1 1 1 1c0 1 2 2 3 2-1 0-1-1-1-2h-1v-3c0-1-1-2-1-2 0-3-1-7 0-10 0 0 0-1 1-2h0 0c0 6-1 15 3 19 1 2 3 2 5 2 3 0 6-1 7-3 2-2 3-5 4-7v-1c1 4-1 7-3 9-1 2-4 4-6 4-3 0-6 0-9-2-4-4-8-9-9-14z" class="W"></path><path d="M468 816l4 3c0 5-1 7-4 10l-12-6c3 0 8 0 10-1v-1h1 1c1-1 0-3 0-5z" class="B"></path><path d="M216 463c-5-3-12-7-17-6-3 1-6 2-8 5-3 4-2 10-1 14l-2-3c-1-3-1-7 0-10 2-4 5-6 9-7 4-2 9-1 13 1 2 1 4 2 5 4l1 1v1z" class="N"></path><path d="M462 221c2 0 3 1 5 2s3 1 4 1v1c-1 0-2 0-3-1v2c5 9 6 20 8 30l-1 1-1 2h0c1 1 1 2 0 3l-1-7v-6c-2-10-4-20-11-28z" class="L"></path><path d="M505 176c5 1 11 1 16 0 3 0 6-1 8-1l-1 3-2 1-3 1h-7-2-1c-2 0-4-1-7 0h-1-1l-2 2-1-1 3-3c0-1 0-1 1-2z" class="Q"></path><path d="M687 668c6-3 20-14 26-14 0 2-3 4-4 5-4 2-8 5-12 7-2 2-4 3-6 4l-4-2z" class="P"></path><defs><linearGradient id="H" x1="856.539" y1="183.244" x2="868.969" y2="177.272" xlink:href="#B"><stop offset="0" stop-color="#c9c7c7"></stop><stop offset="1" stop-color="#ebebeb"></stop></linearGradient></defs><path fill="url(#H)" d="M848 180c6-2 14-5 20-2 2 1 3 2 5 4-2 1-3 0-4 2-3 1-5 2-7 4l3-5s-1 0-1-1 1-1 2-2h0-4c-2 1-4 1-6 1s-6 0-8-1z"></path><path d="M242 311l3-3c0 2 0 4 1 7v1c-1 1 0 2 0 3v2 1c-1 0-1 0-2 1l-5 2v-1c-1-1-1-2-1-3 0-2 0-2-1-4l-2 2 1-2h0l6-6z" class="J"></path><path d="M487 356c2 1 5 2 5 5 0 4-3 6-6 9l-3 3h-1c0-7 2-11 5-17z" class="c"></path><path d="M339 670c-2-1-4-2-6-4l-12-8c-2-1-4-2-5-3h1c4-1 22 10 26 13l-4 2z" class="P"></path><defs><linearGradient id="I" x1="802.144" y1="570.364" x2="810.99" y2="549.977" xlink:href="#B"><stop offset="0" stop-color="#616162"></stop><stop offset="1" stop-color="#959393"></stop></linearGradient></defs><path fill="url(#I)" d="M815 549v1l-1 1c1 1 1 1 3 1l1-2h1c-5 7-12 15-20 20 0-1-1-2-2-3 7-6 12-11 18-18z"></path><path d="M490 854c-1 0-2-1-3-1v-4c-2-2-4-2-6-2l1-5c1-2 1-6 4-8 3 4 3 10 6 13-1 1-4 0-3 3 0 1 1 2 1 4z" class="E"></path><defs><linearGradient id="J" x1="775.037" y1="577.015" x2="799.214" y2="571.824" xlink:href="#B"><stop offset="0" stop-color="#373636"></stop><stop offset="1" stop-color="#666667"></stop></linearGradient></defs><path fill="url(#J)" d="M797 567c1 1 2 2 2 3-7 6-14 10-23 13l-1-1h-1l1-3h2c6-5 14-8 20-12z"></path><path d="M815 549c3-7 5-13 6-21l5-1c-1 7-2 13-6 20 0 0-1 2-1 3h-1l-1 2c-2 0-2 0-3-1l1-1v-1z" class="J"></path><defs><linearGradient id="K" x1="209.787" y1="523.665" x2="212.224" y2="507.761" xlink:href="#B"><stop offset="0" stop-color="#656466"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#K)" d="M207 520c0-3-2-5-3-7-1-3-2-5-1-8 1 0 1 1 2 2l1-1 1 1c1 3 2 5 4 7 2 3 5 6 8 9v1 1h-3c-2-1-4-3-6-3l-3-2z"></path><defs><linearGradient id="L" x1="224.843" y1="346.039" x2="203.352" y2="336.84" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#L)" d="M204 342l-2-2h4c2 1 4 1 6 1h0 16c0 1-1 1-1 2h-1c-2 1-3 3-5 5 0 1-1 2-2 3-3-5-10-7-15-9z"></path><path d="M211 477h1c3 0 4-1 5-3h1c-4 6-7 12-7 19-1 2-1 6-2 8l-1-1h-1l-1-2v-4h0c0-7 2-12 5-17z" class="E"></path><path d="M206 494v-1c1 1 2 2 2 3 1 1 0 3 0 4h-1l-1-2v-4z" class="O"></path><path d="M526 179l2-1c0 2 1 3 2 4-1 1-2 1-3 1 2 2 2 4 4 6 0 2 2 3 3 4l-10 2-3-9v-1l-1-3v-1c-1-1-2-1-4-1h7l3-1z" class="B"></path><path d="M526 179l2-1c0 2 1 3 2 4-1 1-2 1-3 1 0-1-1-2-1-4z" class="K"></path><path d="M516 180h7c0 2 0 2 2 4 0 0-1 1-2 1l-2 1v-1l-1-3v-1c-1-1-2-1-4-1z" class="C"></path><defs><linearGradient id="M" x1="248.186" y1="582.715" x2="235.689" y2="562.058" xlink:href="#B"><stop offset="0" stop-color="#414241"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#M)" d="M222 559c4 1 5 6 10 7v-2c4 3 9 6 14 9 3 1 6 2 8 4h1c0 1 1 2 1 3l-1 1c-3-1-6-2-8-3-9-5-19-11-25-19z"></path><defs><linearGradient id="N" x1="818.145" y1="527.833" x2="825.145" y2="505.601" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#9f9e9d"></stop></linearGradient></defs><path fill="url(#N)" d="M824 508c2-1 3-2 5-3l1 1c-2 5-6 9-7 14v1c-1 1-1 1-1 2s0 2 1 4h-3c-1 0-3 1-4 2 0-3 1-5 1-8h-2l2-2 4-7c1-1 2-3 3-4z"></path><path d="M558 820c7-6 11-12 16-20-2 8-7 15-11 22l9 1c-3 2-7 5-11 7h-3-1v-4c1-2 1-4 1-6z" class="B"></path><defs><linearGradient id="O" x1="223.832" y1="546.769" x2="214.11" y2="550.973" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#ababab"></stop></linearGradient></defs><path fill="url(#O)" d="M211 533c3 12 11 23 21 31v2c-5-1-6-6-10-7h0c-7-7-13-14-15-23 0-1 0-2 1-3h3z"></path><path d="M551 203h1c1 1 3 4 5 5s8 2 11 2l3-1c1 0 0 0 2-1h0c1 0 2-1 3-2l-1 2c1 1 0 1 2 1 3-1 8 0 11 2v1h-1c-5 0-10 1-15 3-5 0-9-1-14-3-2-1-4-3-5-5l-2-4z" class="G"></path><defs><linearGradient id="P" x1="535.752" y1="838.639" x2="548.171" y2="844.603" xlink:href="#B"><stop offset="0" stop-color="#838282"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#P)" d="M541 833c2 0 2 0 4 1 2 4 3 8 4 13-1 0-5 1-6 2 0 2 0 4-2 4l-1 1c-1-1-1-1-1-3 1-1 0-4 0-6v-1-1c-1-1-1-1-1-2v-1c-1-1-1-2-1-3v-2-1c1 0 2-1 4-1z"></path><defs><linearGradient id="Q" x1="611.318" y1="743.67" x2="633.008" y2="757.538" xlink:href="#B"><stop offset="0" stop-color="#7a7a79"></stop><stop offset="1" stop-color="#b9b6b9"></stop></linearGradient></defs><path fill="url(#Q)" d="M648 733v1c0 1 0 2-1 3-7 13-33 24-46 28l-7 2h0c2-3 5-4 8-5l12-4c13-6 24-13 34-25z"></path><path d="M212 219h1c1-2 2-4 4-5-1 2-2 4-2 7 2 1 2 1 2 3-3 5-8 15-6 22l1 2-1 1h-1c-2 0-3-1-4-2-1-3-1-8 0-11l6-17z" class="G"></path><path d="M537 834v1 2c0 1 0 2 1 3v1c0 1 0 1 1 2v1 1c0 2 1 5 0 6 0 2 0 2 1 3h-1c-2 5-1 13 0 19-4-6-6-13-8-19-1-2-1-4-1-6s2-2 3-3v-1c0-1 0-1 1-2v-1-3l1-3 2-1z" class="U"></path><defs><linearGradient id="R" x1="488.363" y1="824.503" x2="469.631" y2="830.478" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#R)" d="M472 819c6 4 13 4 19 7l1 6c-1 1-2 1-3 1-7-1-15-1-21-4 3-3 4-5 4-10z"></path><path d="M798 247l3-3c2-4-1-15-2-19 4 5 7 10 10 16 2 2 3 5 5 6s2 0 4 0 4-1 6-1c-1 2-3 4-4 6h0-1c-4 3-13 1-18 1h0c2 0 5 0 7-1-2-3-3-6-6-9 0 2-1 3-3 4h-1z" class="H"></path><defs><linearGradient id="S" x1="224.708" y1="509.871" x2="202.292" y2="512.629" xlink:href="#B"><stop offset="0" stop-color="#b0aeaf"></stop><stop offset="1" stop-color="#d4d4d4"></stop></linearGradient></defs><path fill="url(#S)" d="M206 506c-2-5-6-10-9-16 3 2 7 5 9 8l1 2h1l1 1c0 1 0 2 1 2 1 4 4 7 6 10h3v1l2 2s1 1 1 2l2 3 1 1h1l2 3v1 1c0 1 1 2 1 3v2l-2-2-8-7c-3-3-6-6-8-9-2-2-3-4-4-7l-1-1z"></path><path d="M216 513h3v1l2 2s1 1 1 2l2 3c-1-1-1-1-2-1h-1 0c-2-1-3-3-4-5l-1-2z" class="F"></path><path d="M216 513h3v1l2 2-4-1-1-2z" class="E"></path><path d="M514 180h2c2 0 3 0 4 1v1l1 3v1l3 9 10-2 1-1c3-1 6-2 8-4h1l2 2c-6 4-17 8-24 8h-4v-2l-2-2 1-1c-2-1-2-1-3-2-1 0-2 1-2 2l-3-3 2-3c1-2 2-4 3-7h0z" class="L"></path><path d="M514 191h3c1 1 4 3 5 3h0v-2-2c1 2 1 3 1 5-2 0-4 0-6-1v-1c-2-1-2-1-3-2z" class="F"></path><path d="M514 180h2c2 0 3 0 4 1v1l1 3c-1 2 0 4 1 5v2 2h0c-1 0-4-2-5-3h-3c-1 0-2 1-2 2l-3-3 2-3c1-2 2-4 3-7h0z" class="H"></path><path d="M514 180l2 1-2 1c1 3 1 6 3 9h-3c-1 0-2 1-2 2l-3-3 2-3c1-2 2-4 3-7z" class="R"></path><path d="M514 191c0-1-1-2-1-2-1-3 0-5 1-7 1 3 1 6 3 9h-3z" class="N"></path><path d="M755 638c2-1 7-2 10-2-7 4-15 7-22 9 7 3 13 4 21 4-8 2-16 5-25 5h-2c-2-1-4-1-5 0l-1-1c1-1 1-2 2-2 1-3 2-5 4-7l1-1c2-1 3-1 5-1 0 1 3 0 4 0 3-2 5-2 8-4z" class="D"></path><path d="M737 644l1-1c2-1 3-1 5-1l-5 3-1-1z" class="B"></path><path d="M268 638l1-1c3 0 6 1 9 1l1 1c4 2 11 2 14 6 2 2 3 6 5 8h0-5v1h-4c-8-1-15-3-23-6 8 1 13 0 21-3-7-2-13-4-19-7z" class="C"></path><path d="M221 471c1 4 1 10-1 14s-4 8-5 11c-1 5 2 13 4 18v-1h-3c-2-3-5-6-6-10-1 0-1-1-1-2 1-2 1-6 2-8 0-7 3-13 7-19 1-1 2-2 2-3h1z"></path><path d="M211 493l1 6c2 5 4 10 7 14h-3c-2-3-5-6-6-10-1 0-1-1-1-2 1-2 1-6 2-8z" class="R"></path><path d="M211 493l1 6 1 6h-1 0c-1-1-2-1-2-2-1 0-1-1-1-2 1-2 1-6 2-8z" class="N"></path><defs><linearGradient id="T" x1="458.412" y1="777.492" x2="448.089" y2="783.45" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#T)" d="M435 754c1 0 2-1 3-1 5 20 19 40 37 51h1l-2 1h-1-2c-1-1-2-1-4-2h0-1c-3-2-5-5-8-8-12-12-19-24-23-41z"></path><defs><linearGradient id="U" x1="569.773" y1="774.301" x2="585.201" y2="780.899" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#U)" d="M593 749l3 3v3c-4 15-11 28-22 39-3 3-6 6-10 9l-2 2-4-1c0-2 2-2 2-4 3-2 6-4 8-7 13-12 22-26 25-44z"></path><path d="M558 804h4c0-1 1-1 2-1h0l-2 2-4-1z" class="U"></path><defs><linearGradient id="V" x1="534.827" y1="825.091" x2="556.705" y2="827.914" xlink:href="#B"><stop offset="0" stop-color="#5e5d5d"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#V)" d="M558 820c0 2 0 4-1 6v4h1 3c-6 3-15 2-21 3h1c-2 0-3 1-4 1l-2 1-1 3v3 1c-1 1-1 1-1 2v1c-1 1-3 1-3 3s0 4 1 6c-1-1-1-2-2-3v-1-1c-1-1-1-1-1-2l-1-1c0-1 1-2 1-3h0c0-3 2-8 4-11v1c4-10 18-9 26-13z"></path><path d="M532 832v1c0 1-1 2-1 4-1 1-1 3-1 5l3 3c-1 1-3 1-3 3s0 4 1 6c-1-1-1-2-2-3v-1-1c-1-1-1-1-1-2l-1-1c0-1 1-2 1-3h0c0-3 2-8 4-11z" class="V"></path><path d="M463 253c0 2 1 4 1 6-5 6-7 12-8 19v3c0 4 1 7 4 9 2 2 4 3 7 2 3 0 5-2 8-3l2 2h0c-1 1-1 1-1 2l-3 3c-4 1-7 3-11 2-2-1-4-2-6-4-3-3-6-8-6-13 0-3 1-6 2-9 3-7 5-13 11-19z" class="Q"></path><path d="M491 826c3 1 4 3 6 5h1c0 1 1 3 1 4v2 3c0 1 0 1 1 2v2 2c1 2 0 6 0 8l-5 11c-1 3-2 5-4 8l2-10c0-3 0-8-1-10l-2 1c0-2-1-3-1-4-1-3 2-2 3-3-3-3-3-9-6-13h3v-1c1 0 2 0 3-1l-1-6z" class="Z"></path><path d="M486 834h3c4 2 2 10 6 12v1c-1 0-1 0-2 1 0 2 0 4 1 6 1 3 0 8 1 11-1 3-2 5-4 8l2-10c0-3 0-8-1-10l-2 1c0-2-1-3-1-4-1-3 2-2 3-3-3-3-3-9-6-13zm-84-157l1-1c3-1 7 1 11 2 1 0 3 2 5 2 4 2 7 4 10 6 2 1 2 1 3 2l3 4v3l2 2 6 6v1h0c3 3 5 5 8 7v3c-3-2-6-3-9-4-2-1-3-3-5-3-2-1-3-5-4-7-1 0-2-5-3-6l-13-7c-2-2-3-2-5-3v-2l-11-4 1-1z" class="U"></path><path d="M402 677l1-1c3-1 7 1 11 2 1 0 3 2 5 2 4 2 7 4 10 6-1 1-1 1-3 0-8-5-15-7-24-9z" class="J"></path><path d="M412 682l15 7h2 0c2 0 2 0 3-1l3 4v3l2 2 6 6v1c-3-4-8-8-13-10l-13-7c-2-2-3-2-5-3v-2z" class="F"></path><path d="M432 688l3 4v3l-2-1c-2-2-4-3-6-5h2 0c2 0 2 0 3-1z" class="S"></path><defs><linearGradient id="W" x1="439.484" y1="709.876" x2="440.516" y2="696.624" xlink:href="#B"><stop offset="0" stop-color="#121012"></stop><stop offset="1" stop-color="#323232"></stop></linearGradient></defs><path fill="url(#W)" d="M430 694c5 2 10 6 13 10h0c3 3 5 5 8 7v3c-3-2-6-3-9-4-2-1-3-3-5-3-2-1-3-5-4-7-1 0-2-5-3-6z"></path><path d="M433 700c2 0 4 2 5 3 1 0 1 0 1 1l3 6c-2-1-3-3-5-3-2-1-3-5-4-7z" class="Y"></path><defs><linearGradient id="X" x1="586.562" y1="748.18" x2="624.408" y2="703.939" xlink:href="#B"><stop offset="0" stop-color="#d6d5d5"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#X)" d="M631 714h0c2 0 3 0 4-1v1c-1 0-1 1-1 1-13 8-26 15-40 20l-20 7v-2c1-2 4-4 6-5h-1 0c2-2 4-3 4-5l7-3 1-1 3-1c2 1 3 1 4 3l33-14z"></path><path d="M591 726l3-1c2 1 3 1 4 3l-8 3v-4l1-1z" class="S"></path><path d="M583 730l7-3v4l-10 4h-1 0c2-2 4-3 4-5z" class="T"></path><defs><linearGradient id="Y" x1="221.415" y1="319.626" x2="224.466" y2="337.731" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#Y)" d="M235 319l2-2c1 2 1 2 1 4 0 1 0 2 1 3v1l5-2c1-1 1-1 2-1-3 6-6 12-13 14l-7 3c1 1 2 1 2 1h1 0l-1 1h-16 0c-2 0-4 0-6-1h-4l2 2-6-2 8-1c5-1 13-4 17-7 2-2 3-5 5-7h1l6-6z"></path><path d="M244 323c1-1 1-1 2-1-3 6-6 12-13 14l-7 3c1 1 2 1 2 1h1 0l-1 1h-16c3-2 6-2 9-3 6-2 11-5 16-9v-1h1l1-1c1-1 1 0 2-1l1-1c-3 1-6 3-9 4v-1l6-3 5-2z" class="E"></path><path d="M244 323c1-1 1-1 2-1-3 6-6 12-13 14l-7 3c1 1 2 1 2 1h-4 0l1-1c-1 0-1 0-1-1 3 1 8-4 11-5 1-1 3-2 4-4 2-1 4-4 5-6z" class="F"></path><defs><linearGradient id="Z" x1="233.422" y1="355.09" x2="199.965" y2="363.682" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#Z)" d="M226 343l1 2c1 0 1 0 2 1 1 0 1 1 1 2 1 1 2 3 3 4l1-1v1c-1 3-3 7-6 9h2 1l-2 2c-6 6-14 11-21 13-4 1-8 1-11 2 2-2 5-3 8-5 5-3 9-10 11-16l3-6c1-1 2-2 2-3 2-2 3-4 5-5z"></path><defs><linearGradient id="a" x1="431.623" y1="747.232" x2="413.877" y2="702.768" xlink:href="#B"><stop offset="0" stop-color="#d6d9d9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#a)" d="M457 743c-22-8-43-16-63-29-6-3-12-7-18-12 8 4 16 8 23 12 11 6 22 10 34 14 0-2 0-3 1-4 4 2 11 4 15 7h1v1c3 1 5 4 5 6 1 2 2 3 3 4l-1 1z"></path><path d="M434 724c4 2 11 4 15 7h1v1c3 1 5 4 5 6-7-4-15-7-22-10 0-2 0-3 1-4z" class="I"></path><path d="M484 188c5-3 11-7 14-12l1-1c2 0 4 1 6 1-1 1-1 1-1 2l-3 3 1 1 2-2h1 1c3-1 5 0 7 0h1 0c-1 3-2 5-3 7l-2 3 3 3c0-1 1-2 2-2 1 1 1 1 3 2l-1 1h0c-1-1-3-1-4 0s-2 2-2 4l-1 1h0l-19-5c-3-1-6-2-8-4l2-2z" class="S"></path><path d="M509 190l3 3c-1 1-2 2-3 2h-2c1-1 2-3 2-5z" class="U"></path><path d="M506 192c1-2 3-4 5-5l-2 3c0 2-1 4-2 5-2-1-6-1-8-2 3 0 5 0 7-1z" class="M"></path><path d="M506 180c3-1 5 0 7 0h1 0c-1 3-2 5-3 7-2 1-4 3-5 5-2 1-4 1-7 1-1 0-2-1-4-1 3-4 9-8 10-12h1z" class="H"></path><path d="M506 180c3-1 5 0 7 0h1 0c-1 3-2 5-3 7-2 1-4 3-5 5h-4v-1l2-2 2-4c1-1 3-4 2-5h-2z" class="D"></path><path d="M513 180h1 0c-1 3-2 5-3 7-2 1-4 3-5 5h-4v-1l2-2c0 1 0 0-1 1v1h2s1 0 1-1v-1l2-2c1-3 3-5 5-7z" class="O"></path><path d="M484 188c5-3 11-7 14-12l1-1c2 0 4 1 6 1-1 1-1 1-1 2l-3 3 1 1 2-2h1c-1 4-7 8-10 12-1-1-1-1-2-1-1-2 1-4 2-5-1 0-2 2-3 3h-2-1c-2 1-3 0-5-1z" class="G"></path><path d="M599 708c11-3 22-4 32-9l-18 15c-6 4-13 7-19 11l-3 1c-1 0-1-1-2-1-1-4-1-7 0-11h0c2-2 3-4 3-8 0-1 1-2 2-2v-1l1-1c1-1 2-2 4-2 0 3-1 5 0 8z" class="Q"></path><path d="M594 704v-1l1-1c1-1 2-2 4-2 0 3-1 5 0 8-2 0-3 0-5 1-1-2-1-4 0-5z" class="P"></path><path d="M253 628c-5-3-13-9-17-14 16 8 30 14 48 15h1 3l1 1c1 3 6 3 9 5 1 1 2 2 3 4-2-1-13-2-14-2-1 1-1 1-3 1h-3v1c-1-1-1-1-2-1h-1c-3 0-6-1-9-1l-1 1c-2 0-3-1-4-2-4-2-7-5-11-8z" class="G"></path><path d="M253 628c3 0 5 1 7 2l11 4 16 3c-1 1-1 1-3 1h-3v1c-1-1-1-1-2-1h-1c-3 0-6-1-9-1l-1 1c-2 0-3-1-4-2-4-2-7-5-11-8z" class="E"></path><path d="M264 636l1-1c2 0 4 1 7 1 2 0 5 0 7 1h2c1 0 1 0 2 1h1-3v1c-1-1-1-1-2-1h-1c-3 0-6-1-9-1l-1 1c-2 0-3-1-4-2z" class="B"></path><path d="M434 724c-13-7-25-15-35-26 8 5 16 7 25 8 4 1 9 2 14 3 1 1 1 0 1 1 3 6 6 11 8 16 1 2 2 3 2 5-4-3-11-5-15-7z" class="Q"></path><defs><linearGradient id="b" x1="287.921" y1="295.402" x2="243.009" y2="315.001" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#b)" d="M288 290l1 3-2 1c-1 1-2 1-4 1v1h-3c-3 3-7 6-11 9-1 1-3 4-5 5v-1l2-2h0l1-1-1-1c-1 0 0 0-1 1-1 0-1 1-3 1h0c-4 4-7 8-11 12l-2 2c-1 1-2 1-2 2h0l-1-2v-2c0-1-1-2 0-3v-1c-1-3-1-5-1-7l2-1c-1 0-1-1-1-2l2-2h0c11-6 27-12 40-13z"></path><path d="M246 305l2-2 1 2c-1 1-1 2-2 2s-1-1-1-2z" class="J"></path><path d="M262 307c2-2 5-5 8-7s7-4 10-4c-3 3-7 6-11 9-1 1-3 4-5 5v-1l2-2h0l1-1-1-1c-1 0 0 0-1 1-1 0-1 1-3 1h0z" class="B"></path><path d="M746 629c11-2 22-3 32-8 6-2 11-5 16-8-5 6-12 11-19 16-2 2-7 6-10 7-3 0-8 1-10 2-5 1-10 1-14 2-2 1-4 1-6 1v-1h2c-1-1-2-1-2-2h-1v-2c0-1 0-1 1-2h1l1-1s1-1 2-1l2-2h0c2 0 3-1 5-1z" class="C"></path><path d="M741 630s1 2 0 2l-1 1c-1 1-4 3-5 5 4 0 7-1 10-1l20-5c3-1 7-3 10-3-2 2-7 6-10 7-3 0-8 1-10 2-5 1-10 1-14 2-2 1-4 1-6 1v-1h2c-1-1-2-1-2-2h-1v-2c0-1 0-1 1-2h1l1-1s1-1 2-1l2-2z" class="M"></path><defs><linearGradient id="c" x1="715.912" y1="638.451" x2="731.651" y2="651.625" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#c)" d="M738 630v-1c3-1 5-1 8 0-2 0-3 1-5 1h0l-2 2c-1 0-2 1-2 1l-1 1h-1c-1 1-1 1-1 2v2h1c0 1 1 1 2 2h-2v1c2 0 4 0 6-1 4-1 9-1 14-2-3 2-5 2-8 4-1 0-4 1-4 0-2 0-3 0-5 1l-1 1c-2 2-3 4-4 7-1 0-1 1-2 2l1 1c1-1 3-1 5 0h2c-8 1-16-1-24-2-3-1-7-2-10-3-1 0-2 0-2-1h0v-1l18-8c0-1 13-9 15-10l2 1z"></path><path d="M721 639c0-1 13-9 15-10l2 1c-4 4-11 7-16 10l-1-1z" class="a"></path><path d="M703 647l18-8 1 1-17 9c-1 0-2 0-2-1h0v-1z" class="P"></path><defs><linearGradient id="d" x1="311.577" y1="639.13" x2="298.837" y2="653.303" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#c1c1c1"></stop></linearGradient></defs><path fill="url(#d)" d="M293 628l12 9 7 4s2 0 2 1l10 4-1 2 3 1c-3 1-7 2-10 2-9 2-18 4-27 3h4v-1h5 0c-2-2-3-6-5-8-3-4-10-4-14-6l-1-1h1c1 0 1 0 2 1v-1h3c2 0 2 0 3-1 1 0 12 1 14 2-1-2-2-3-3-4-3-2-8-2-9-5l-1-1h-3-1 7c1-1 1-1 2-1z"></path><path d="M293 628l12 9 7 4s2 0 2 1l10 4-1 2c-3-1-5-2-7-3-9-4-18-11-25-16 1-1 1-1 2-1z" class="a"></path><path d="M316 645c-1-1-2-2-3-2h-1l-2-2c1 0 2 0 3 1h1l10 4-1 2c-3-1-5-2-7-3z" class="P"></path><path d="M546 190l1-1s1-1 1-2c1-2 2-3 4-4h1c1 7 1 16 6 22 2 1 5 2 7 2 3 0 6-1 8-3 1-2 2-4 2-7-1-1-1-3-2-3-2-1-3-2-5-2-1 0-1 1-2 2s-1 2 0 3 1 1 3 1l1-1-1-3c1 0 2 0 3 1 0 1 1 2 0 3 0 1-1 2-2 3h-5c-2-2-3-3-3-6-1-2 0-3 1-5l6-3c2 0 4 1 6 3s3 5 4 8c0 3-2 5-3 8h-1c-1 1-2 2-3 2h0c-2 1-1 1-2 1l-3 1c-3 0-9-1-11-2s-4-4-5-5h-1l-3-9h-1v1c-6 5-20 7-28 8v-4l3-1c7 0 18-4 24-8z" class="W"></path><path d="M585 706l3-3c3-4 8-6 12-8l-1 5c-2 0-3 1-4 2l-1 1v1c-1 0-2 1-2 2 0 4-1 6-3 8h0c-1 4-1 7 0 11 1 0 1 1 2 1l-1 1-7 3c0 2-2 3-4 5h0 1c-2 1-5 3-6 5v2l-7 3c2-3 3-6 4-10h0c0-1-1-2-1-3l8-21 2-3h0c2-1 2-2 3-3h1l1 1z" class="J"></path><path d="M579 735c-1 0-1 1-2 0l1-6c0-2 1-3 2-5l3-6 1-1c1-1 1-2 2-3l-8 20 1 1h0z" class="F"></path><path d="M592 706c0 4-1 6-3 8h0c-1 4-1 7 0 11 1 0 1 1 2 1l-1 1-7 3c0 2-2 3-4 5l-1-1 8-20c2-3 5-3 6-7v-1z" class="N"></path><path d="M583 730h0c0-2 1-8 2-10h0c1-2 2-4 4-6-1 4-1 7 0 11 1 0 1 1 2 1l-1 1-7 3z" class="O"></path><defs><linearGradient id="e" x1="586.163" y1="714.887" x2="591.898" y2="694.631" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#393838"></stop></linearGradient></defs><path fill="url(#e)" d="M585 706l3-3c3-4 8-6 12-8l-1 5c-2 0-3 1-4 2l-1 1v1c-1 0-2 1-2 2v1c-3 2-5 3-7 5-1 1-4 1-5 2 0 1-1 3-1 3-3 6-6 12-8 18 0-1-1-2-1-3l8-21 2-3h0c2-1 2-2 3-3h1l1 1z"></path><path d="M583 705h1l1 1c-2 2-3 3-7 5h0l2-3h0c2-1 2-2 3-3z" class="X"></path><path d="M227 582c3 0 5 0 7 1s3 3 4 5c1 4-1 9-3 12-3 5-7 6-12 7-1 0-4 0-5-1-3-2-5-5-5-8-1-3 0-6 2-9 3-5 7-6 12-7z" class="H"></path><path d="M227 583c3 0 5 0 7 1 1 1 3 2 3 4 1 4-1 9-4 12-1 2-4 4-6 4v-1-1l-1-1-3 4c-1-1-3-1-4-2-2-1-4-3-4-5-1-3-1-6 1-9 2-4 7-5 11-6z"></path><defs><linearGradient id="f" x1="214.937" y1="218.135" x2="194.72" y2="233.854" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#f)" d="M215 196c4 1 8 3 12 5-1 0-3 2-4 2l-3 6c-1 1-2 4-3 5-2 1-3 3-4 5h-1l-6 17c-1 3-1 8 0 11 1 1 2 2 4 2h1 0c-1 0-1 0-2 1l1 1 1 1h0l-2-1c-8-3-15-12-18-19v-7c1 2 2 4 2 6 1 1 1 1 2 1l1-2c1-5 5-10 8-12l6-7 4-5 3-4c0-1 1-1 1-2v-1-1c-1 0-2 0-3-1v-1z"></path><path d="M210 214c-1 1-1 2-2 3v1l-1 1c-4 4-6 8-10 11h-1c1-5 5-10 8-12 2 0 4-3 6-4z" class="I"></path><path d="M215 196c4 1 8 3 12 5-1 0-3 2-4 2s-2-1-3 0c-3 0-4 2-5 4l-5 7c-2 1-4 4-6 4l6-7 4-5 3-4c0-1 1-1 1-2v-1-1c-1 0-2 0-3-1v-1z" class="X"></path><defs><linearGradient id="g" x1="215.209" y1="220.898" x2="201.05" y2="245.749" xlink:href="#B"><stop offset="0" stop-color="#817f7f"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#g)" d="M193 231c0 2 2 4 3 6 1 1 3 2 4 2 0-3 2-7 4-9 1-3 4-10 8-12v1l-6 17c-1 3-1 8 0 11 1 1 2 2 4 2h1 0c-1 0-1 0-2 1l1 1 1 1h0l-2-1c-8-3-15-12-18-19v-7c1 2 2 4 2 6z"></path><defs><linearGradient id="h" x1="795.756" y1="360.152" x2="824.744" y2="357.348" xlink:href="#B"><stop offset="0" stop-color="#b8b7b7"></stop><stop offset="1" stop-color="#e1e1e1"></stop></linearGradient></defs><path fill="url(#h)" d="M818 342c2-1 5-1 7 0-5 3-11 5-14 10 3 6 7 16 12 21 2 1 5 2 8 4-9 0-19-4-27-10v-1c-1-3-3-6-4-10l-7-14c1-1 1-1 1 0 4 1 7 0 10 0 5-1 9 0 14 0z"></path><path d="M822 480c2 5 3 10 4 16l8-7c-2 4-5 7-7 11-1 3-2 5-3 8-1 1-2 3-3 4l-4 7-2 2h2c0 3-1 5-1 8l-6 3-14 9c-1 1-3 2-4 2l-2 1c-1 0-1 0-2 1-1-1-1-2-1-3l3-3c2-1 6-4 7-7 1-1 2-1 2-2 2-3 5-5 7-7s4-4 6-7c2-4 5-8 6-12v-1l1-4c1-3 1-7 1-11-1-2-1-4-2-6 1-1 2-2 4-2z" class="B"></path><path d="M820 488v1c1 1 1 1 3 2 0 2-1 7-2 10-1 1-2 1-3 2l1-4c1-3 1-7 1-11z" class="M"></path><path d="M802 531c2-2 5-4 7-6 1-1 4-5 5-6h3l-2 2h2c0 3-1 5-1 8l-6 3c0-2 1-3 2-5h0c-2 0-6 2-8 4-1 0-1 1-2 1v-1z" class="U"></path><defs><linearGradient id="i" x1="790.83" y1="531.991" x2="807.293" y2="538.467" xlink:href="#B"><stop offset="0" stop-color="#797775"></stop><stop offset="1" stop-color="#acacaf"></stop></linearGradient></defs><path fill="url(#i)" d="M806 523c-1 2-3 6-5 8h1v1c1 0 1-1 2-1 2-2 6-4 8-4h0c-1 2-2 3-2 5l-14 9c-1 1-3 2-4 2l-2 1c-1 0-1 0-2 1-1-1-1-2-1-3l3-3c2-1 6-4 7-7 1-1 2-1 2-2 2-3 5-5 7-7z"></path><path d="M565 252c8 6 10 14 13 23 0 1 0 4 1 5a19.81 19.81 0 0 1 11 11c2 5 2 10 0 15-2 4-6 7-10 9-4 1-9 1-13-1v-1c-1-1-1-2-1-3 5 0 9 0 13-2 3-2 6-5 6-8 2-7-2-12-6-17 0 2-1 4-1 5-1 3-3 4-4 6l-8 1c-3 0-4 0-6-2v-2l3 1c2 1 4 0 6-1 3-2 4-5 4-8 1-2 1-5 0-7l-3-8c-2-5-6-12-11-14 1-1 3 0 4-1 1 0 1-1 2-1z" class="K"></path><path d="M476 188v4h0c0-1 1-3 0-4v-4h1c1 1 3 2 3 4 1 1 1 3 1 4h0v-1c1 1 1 2 1 3v1c-1 1-1 3-1 4-1 7-6 13-12 16-2 0-3 0-5 1h-8-11c-1 1 0 1-1 0v-2c2-3 6-4 9-5-1-1-2-3-3-5-2-4-2-8 0-12 1-2 4-4 6-4 3-1 5 0 7 1s3 3 3 5 0 3-1 5c-2 2-3 2-5 2s-2 0-3-1c-1-2-1-3-1-4 1-1 1-2 3-2v1c0 1-1 1-1 2l1 1c1 0 1-1 2-2 0-2 0-2-1-3s-3-1-4 0c-1 0-3 2-3 3 0 3 1 7 2 9 2 2 5 3 8 3s6-1 8-4c5-4 4-10 5-16z" class="Q"></path><path d="M453 209c2 1 4 1 5 2 4 0 8 0 11-1h0c-1 1-3 2-4 2-4 0-5 1-8-1-2 0-5-1-7 0s-3 2-5 4h1 1 1 0 3 4v-1h4 1l1 1h1c1 0 1 0 2 1h-8-11c-1 1 0 1-1 0v-2c2-3 6-4 9-5z" class="C"></path><path d="M554 260c0-2 0-3 1-4l1-2h3c5 2 9 9 11 14l3 8c1 2 1 5 0 7 0 3-1 6-4 8-2 1-4 2-6 1l-3-1c-1 0-4-2-4-2-1-1-2-1-2-2 1-2 3-3 5-4l1-2-1-1c-1-1-3 0-4 0-1-2 0-3 0-5 0-1 0-3-1-5v-1-8-1z"></path><path d="M554 270h4c1 0 3 1 5 1h2l-2 4h0c-1 1-1 1-1 2h-2c-2-1-3-1-5-2 0-1 0-3-1-5z" class="S"></path><path d="M554 270h4l-2 1v1c1 2 2 3 4 5-2-1-3-1-5-2 0-1 0-3-1-5z" class="L"></path><path d="M563 271h2l-2 4h0c-1 0-2-1-3-1-1-1-1-2 0-3h3z" class="U"></path><path d="M565 271l3 1c2 1 3 2 5 4 1 2 1 5 0 7l-3-3-4-2-4-1c0-1 0-1 1-2h0l2-4z" class="E"></path><path d="M565 271l3 1v2h0c1 1 1 2 2 3-2 1-3 0-4 1l-4-1c0-1 0-1 1-2h0l2-4z" class="F"></path><path d="M555 275c2 1 3 1 5 2h2l4 1 4 2-1 1-1 1c-1 2-3 3-5 3-3 0-1-2-3-4l-1-1c-1-1-3 0-4 0-1-2 0-3 0-5z" class="I"></path><path d="M555 275c2 1 3 1 5 2h2l4 1 4 2-1 1h0c-2-1-5-2-7-2-1 0-1 0-3 1-1-1-3 0-4 0-1-2 0-3 0-5z" class="V"></path><path d="M570 280l3 3c0 3-1 6-4 8-2 1-4 2-6 1l-3-1c-1 0-4-2-4-2-1-1-2-1-2-2 1-2 3-3 5-4l1-2c2 2 0 4 3 4 2 0 4-1 5-3l1-1 1-1z"></path><path d="M574 294c1-2 3-3 4-6 0-1 1-3 1-5 4 5 8 10 6 17 0 3-3 6-6 8-4 2-8 2-13 2l-3-3c-1-2-4-7-6-8s-3-1-5 0l-1 2v2l-1-1h-2v-6l-1-2h0l-1-1c-2 1-5 1-7 2h-3-1c2-1 3-1 4-2l6-2c2-1 4-1 6-1s3 0 5-1c0 0 3 2 4 2v2c2 2 3 2 6 2l8-1z"></path><path d="M556 289s3 2 4 2v2c2 2 3 2 6 2l8-1c-4 3-7 5-12 5-4-1-9-4-13-2 0 1 0 1 1 2 0 1 1 1 1 2h0v2l-1-1h-2v-6l-1-2h0l-1-1c-2 1-5 1-7 2h-3-1c2-1 3-1 4-2l6-2c2-1 4-1 6-1s3 0 5-1z" class="O"></path><defs><linearGradient id="j" x1="510.602" y1="402.961" x2="501.877" y2="403.93" xlink:href="#B"><stop offset="0" stop-color="#bab9b8"></stop><stop offset="1" stop-color="#dddcdd"></stop></linearGradient></defs><path fill="url(#j)" d="M496 361c0-2 1-3 1-5l6-3c1-1 2-1 4-1-1 11 0 24 1 36l3 53c0 1 1 8 0 9v2 2 1c-1-3 0-5-2-7v-1l-13-86z"></path><path d="M777 301c1 1 3 2 4 3s3 2 5 3c1 1 6 4 6 5 4 3 6 6 8 10 2 3 4 8 7 11 1 1 3 1 4 2 4 1 8 3 12 4 3 0 5 0 8 1-2 0-4 1-6 2-2-1-5-1-7 0-5 0-9-1-14 0-3 0-6 1-10 0 0-1 0-1-1 0l-4-10-5-11-1-1-5-12c0-3-1-4-1-7z" class="B"></path><defs><linearGradient id="k" x1="797.806" y1="344.601" x2="808.961" y2="334.09" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#k)" d="M789 332l6 4c2 1 2 1 4 1s4 1 6 2c3 0 7 0 10 1h1c0 1 1 1 2 2-5 0-9-1-14 0-3 0-6 1-10 0 0-1 0-1-1 0l-4-10z"></path><defs><linearGradient id="l" x1="782.91" y1="318.097" x2="789.674" y2="313.676" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#l)" d="M777 301c1 1 3 2 4 3s3 2 5 3c1 1 6 4 6 5h-1l-1-1c0 1 0 1 1 2s1 2 2 4 3 4 4 6c0 2 1 2 0 4-1-1-2-1-3-2h0c-3-2-7-4-10-4l-1-1-5-12c0-3-1-4-1-7z"></path><path d="M781 304c1 1 3 2 5 3v4l-3-3-3-4h1zm13 21c0-1 0-2-1-3v-1h1l3 2c0 2 1 2 0 4-1-1-2-1-3-2h0z" class="F"></path><path d="M786 307c1 1 6 4 6 5h-1l-1-1c0 1 0 1 1 2s1 2 2 4c-2-2-4-5-7-6v-4z" class="N"></path><path d="M194 193c5 0 10 1 15 2 1 0 4 1 6 1v1c1 1 2 1 3 1v1 1c0 1-1 1-1 2l-3 4-4 5-6 7c-3 2-7 7-8 12l-1 2c-1 0-1 0-2-1 0-2-1-4-2-6-2-5-4-11-4-17 0-5 2-9 5-12h0l2-2v-1z" class="B"></path><path d="M192 208s1 1 2 1v-2c1 2 1 3 2 4h0v1h0-1v1c-1 0 0 0-1 1-1-2-2-3-2-6z" class="X"></path><path d="M194 201c1-1 3-2 5-2 2 1 3 3 4 4h-1c-1 0-1 0-2-1h-3l-1 1-1 1-1 3v2c-1 0-2-1-2-1 0-3 0-4 2-7z" class="a"></path><path d="M204 204l1-1c3 0 5 1 8 3h1l-4 5v-2c-1 0-1-1-2-1l-1 1c-2 3-3 6-6 8h-3l-2-1c2-1 4-1 5-2 3-3 3-6 3-10z" class="N"></path><path d="M196 203l1-1h3c1 1 1 1 2 1h1l1 1h0c0 4 0 7-3 10-1 1-3 1-5 2l-2-2c1-1 0-1 1-1v-1h1 0v-1h0c-1-1-1-2-2-4l1-3 1-1z" class="L"></path><path d="M196 203l1 1c2 0 3 0 5 2 0 1 0 2-1 3 0 1-2 2-3 3h-2v-1h0c-1-1-1-2-2-4l1-3 1-1z" class="U"></path><path d="M196 203l1 1 2 2c-1 2-1 3-3 5h0c-1-1-1-2-2-4l1-3 1-1z" class="I"></path><path d="M195 204c1 2 2 5 1 7-1-1-1-2-2-4l1-3z" class="N"></path><path d="M194 193c5 0 10 1 15 2 1 0 4 1 6 1v1c1 1 2 1 3 1v1 1c0 1-1 1-1 2l-3 4h-1c-3-2-5-3-8-3l-1 1h0l-1-1c-1-1-2-3-4-4-2 0-4 1-5 2v-3l-2-2h0l2-2v-1z" class="S"></path><path d="M194 193c5 0 10 1 15 2 1 0 4 1 6 1v1c1 1 2 1 3 1v1 1c0 1-1 1-1 2-3-1-5-3-8-4s-9-1-12-3c-1 0-2-1-3-1v-1z" class="b"></path><path d="M633 678c11-3 21-4 32-4l13 5c7 6 8 16 9 24 0 3 0 5-2 8v1l-1 1c-1 1-1 2-2 3-1 0-1 1-2 1l-1 1-1 1c-1 0-2 1-3 1l-1 1c-1 0-2 1-3 1-3-1-6 0-9-1-4 0-7-1-10-2 7-1 16-5 21-10 4-4 6-9 6-14 0-4-2-7-5-10-6-5-17-4-24-3l-15 1-1-2h-3 0l2-1h-1c-1 0-2-1-4-1l5-1z" class="K"></path><path d="M628 679l5-1c5 3 12 0 17 4l-15 1-1-2h-3 0l2-1h-1c-1 0-2-1-4-1z" class="B"></path><path d="M367 673c10 1 19 2 29 4l5 1 11 4v2c-3-1-6-2-9-2l-3 1h-3l-14-1c-9-1-21-3-28 4-3 2-4 6-4 10 0 5 2 9 5 12 6 7 14 9 22 10-6 3-12 4-19 3h0-3l-3-2c-2-1-4-4-6-5-2-2-3-5-3-8-1-9 1-20 8-27 0 0 7-4 8-5 2 0 4 0 7-1z" class="K"></path><path d="M396 677l5 1 11 4v2c-3-1-6-2-9-2l-3 1h-3l-14-1c1 0 2-1 3-1h0c2 1 4 0 6 0-1 0-3-1-5-1 3 0 8 1 10 0h3c1 1 2 1 3 1h1c-2-2-5-2-8-3v-1z" class="E"></path><path d="M397 683v-2c2 0 4 1 6 1l-3 1h-3z" class="F"></path><path d="M476 188l-1-4 1-1c3 1 4 3 5 5l1 1v1c2 2 5 3 8 4l19 5h0l1-1c0-2 1-3 2-4s3-1 4 0h0l2 2v2c-1 4-1 10-1 14s1 20 0 21c-2 0-4 0-5 1v1h-1c-2-2-1-5-1-7v-6l-33-1-16-1v-1h-1l-1-2-3-1h8c2-1 3-1 5-1 6-3 11-9 12-16 0-1 0-3 1-4v-1c0-1 0-2-1-3v1h0c0-1 0-3-1-4 0-2-2-3-3-4h-1v4c1 1 0 3 0 4h0v-4z" class="S"></path><path d="M497 214h-1c-1-2 0-6 1-8h2c3 0 5 1 8 1l1 1c1 2 1 4 0 6-2 1-4 1-5 0s-1-3-2-3c0 1 0 2-1 4h-3v-1z" class="E"></path><path d="M508 214h-3c1-2 1-4 2-6h1c1 2 1 4 0 6z" class="C"></path><path d="M497 214h-1c-1-2 0-6 1-8h2c1 2 2 5 1 8h-3z" class="K"></path><path d="M483 214c0-2 2-4 3-6s1-4 2-6l1 1c2 0 4 1 5 1 2 0 3 0 4 1h5c2 2 3 1 4 2-3 0-5-1-8-1h-2c-1 2-2 6-1 8h1v1c-3 0-12 0-14-1z" class="R"></path><path d="M489 203c2 0 4 1 5 1 0 2 1 7-1 9-1 1-3 1-4 1l4-7c-1-2-2-3-4-4z" class="F"></path><path d="M483 214c0-2 2-4 3-6s1-4 2-6l1 1c2 1 3 2 4 4l-4 7h-6z" class="K"></path><path d="M489 205l1 1 1 1c-1 2-2 5-4 6h-2c2-3 3-6 4-8z" class="B"></path><path d="M461 219c2-1 8 0 11-1h37l1 1v3l-33-1-16-1v-1zm15-31l-1-4 1-1c3 1 4 3 5 5l1 1v1c2 2 5 3 8 4l19 5h0l1-1c0-2 1-3 2-4s3-1 4 0h0l2 2v2c-1 4-1 10-1 14s1 20 0 21c-2 0-4 0-5 1l-1-29v-2c0-1-6-1-7-1-6-1-17-4-21-8h-1c0-1 0-2-1-3v1h0c0-1 0-3-1-4 0-2-2-3-3-4h-1v4c1 1 0 3 0 4h0v-4z" class="W"></path><defs><linearGradient id="m" x1="495.645" y1="216.267" x2="470.886" y2="269.312" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#m)" d="M461 220l16 1 33 1v6c0 2-1 5 1 7-3 1-5 2-7 4-4 4-5 7-5 12 0 3 0 6 1 9 0 5 4 10 7 14h-1c0 1 0 2 1 3l-1 1v1h-8c-1 0-2-1-2-2h-1l-1-1h-3-1v-1 3c-1 0 0 0-1-1l-2-1-2 2c0 1 0 1-1 2h0-9 0v-7c0-4 0-8-1-11 1-1 1-2 0-3h0l1-2 1-1c-2-10-3-21-8-30v-2c1 1 2 1 3 1v-1c-1 0-2 0-4-1s-3-2-5-2l-1-1z"></path><path d="M482 278c0-1 0-1 1-2 1 0 1 1 2 2 0 1 0 1-1 2l-1-1-1-1z" class="L"></path><path d="M472 226h3v7l-3-7z" class="I"></path><path d="M461 220l16 1h1c1 1 2 1 3 1v1c-4 0-10-1-14 0-2-1-3-2-5-2l-1-1z" class="T"></path><path d="M487 276c-2-2-7-44-7-48 1 0 1 0 1-1 1 0 2 0 3 1 3 12 3 25 5 37 0 3 1 10 0 12l-2-1z" class="C"></path><path d="M468 226h4l3 7c2 10 4 19 5 29 1 4 2 11 1 14-1 0-1 1-2 0l1 2h2l1 1 1 1h0-9 0v-7c0-4 0-8-1-11 1-1 1-2 0-3h0l1-2 1-1c-2-10-3-21-8-30z" class="O"></path><path d="M476 256c1 7 1 14 3 20l1 2h2l1 1 1 1h0-9 0v-7c0-4 0-8-1-11 1-1 1-2 0-3h0l1-2 1-1z" class="U"></path><defs><linearGradient id="n" x1="511.776" y1="261.671" x2="487.3" y2="234.414" xlink:href="#B"><stop offset="0" stop-color="#7a7b7b"></stop><stop offset="1" stop-color="#a2a0a0"></stop></linearGradient></defs><path fill="url(#n)" d="M491 227c3 0 5 0 8 1 2 0 5-1 7 0l1 1v6c-1 1-3 2-4 4h1c-4 4-5 7-5 12 0 3 0 6 1 9 0 5 4 10 7 14h-1c0 1 0 2 1 3l-1 1v1h-8c-1 0-2-1-2-2h-1c-3-13-4-27-5-39 0-4-1-7-1-11h2z"></path><path d="M497 264c1 0 2 1 2 2 1 1 1 2 2 4l5 8v1h-8c-1 0-2-1-2-2h2c1-2 0-4 0-5l-1-8z" class="I"></path><path d="M506 228l1 1v6c-1 1-3 2-4 4l-2 2-1-1v-11l-1-1h7z" class="C"></path><path d="M495 277c-3-13-4-27-5-39 0-4-1-7-1-11h2l3 1c1 2 1 5 1 8 1 8 1 16 2 23v5l1 8c0 1 1 3 0 5h-2-1z" class="G"></path><path d="M512 350h2v1c1 0 3-1 4 0 2 0 5 0 5 2 2 1 2 1 4 1l2 1h3v1c1 7-1 14-2 20l-4 28-5 36c0 6-1 12-2 18 0 5 0 12-2 18-1 1-1 1-1 3v2c-1 1-1 1-1 2v6 1l-1-5v-6h0c1-1 1-2 1-3-1-3 0-6-2-9l-2-17c1-1 0-8 0-9l-3-53c-1-12-2-25-1-36 2 0 4-1 5-2z" class="Q"></path><path d="M512 350h2v1c1 0 3-1 4 0-1 1-1 2-1 3v32l-2 60c-1 7 0 13-1 20-1-11 0-22 0-33l1-24c0-5-1-10-1-15l-1-25v-13-5l-1-1z" class="B"></path><path d="M523 353c2 1 2 1 4 1l2 1h3v1c1 7-1 14-2 20l-4 28-5 36c0 6-1 12-2 18v-4c-1-5 0-9 0-13l2-37 2-51z" class="D"></path><defs><linearGradient id="o" x1="218.898" y1="204.204" x2="165.575" y2="232.565" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#o)" d="M212 248l2-1c4-2 6-6 9-10 2-4 4-8 7-12l-1 6c-1 4-3 11 0 14 1 1 1 2 3 2 1-1 2-1 3-2 3-3 5-5 5-9v-1l1 1v5c-1 5-6 11-10 14-7 4-19 5-27 3-6-2-11-5-15-10-8-9-11-20-14-32-1-5-2-10-5-15-2-4-5-9-10-10-8-2-14 2-20 6 2-4 7-11 11-13h5c2-2 3-5 6-6 6-2 16 1 22 4h0 1l1 1 4 6c1 1 2 3 3 4h1v1l-2 2h0c-3 3-5 7-5 12 0 6 2 12 4 17v7c3 7 10 16 18 19l2 1h0l-1-1-1-1c1-1 1-1 2-1h0l1-1z"></path><path d="M176 202c-2-2-3-5-4-7-2-3-5-6-7-9-1-1-2-3-2-4 2-1 3-1 5-1 0 1 1 2 1 3l-1 1c1 3 4 5 5 8s3 5 3 9z" class="M"></path><path d="M169 184l2 2c2 4 5 7 6 11 1 3 3 6 4 9 1 4 1 8 1 11 0 4 1 8 1 11l-7-26c0-4-2-6-3-9s-4-5-5-8l1-1z" class="F"></path><defs><linearGradient id="p" x1="191.787" y1="204.562" x2="179.513" y2="207.123" xlink:href="#B"><stop offset="0" stop-color="#676667"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#p)" d="M185 182l1 1 4 6c1 1 2 3 3 4h1v1l-2 2h0c-3 3-5 7-5 12 0 6 2 12 4 17v7c-4-9-6-18-9-27l-1 1c-1-3-3-6-4-9-1-4-4-7-6-11l-2-2c0-1-1-2-1-3l4 1c2 0 4 1 7 0h4 1 0 1z"></path><path d="M184 182h0c1 1 1 2 1 3-1 1-4 0-6-1l4-2h1z" class="R"></path><path d="M186 183l4 6c1 1 2 3 3 4h1v1l-2 2c-2-2-4-4-5-7-1-1-1-2 0-3-1-1-1-2-1-3z" class="Y"></path><path d="M168 181l4 1c2 0 4 1 7 0h4l-4 2h-6v1c3 5 6 11 8 17 0 0 1 2 1 3l-1 1c-1-3-3-6-4-9-1-4-4-7-6-11l-2-2c0-1-1-2-1-3z" class="N"></path><path d="M168 181l4 1c0 1-1 2-1 3v1l-2-2c0-1-1-2-1-3z" class="B"></path><defs><linearGradient id="q" x1="815.474" y1="203.653" x2="857.833" y2="232.345" xlink:href="#B"><stop offset="0" stop-color="#bcbabb"></stop><stop offset="1" stop-color="#f9f9f9"></stop></linearGradient></defs><path fill="url(#q)" d="M862 180h4 0c-1 1-2 1-2 2s1 1 1 1l-3 5c2-2 4-3 7-4 1-2 2-1 4-2h0c2 1 4 1 6 2 5 3 9 8 12 13-6-4-11-8-18-7-4 1-8 3-11 7-5 8-7 18-9 27-3 10-8 19-15 26-4 4-9 7-15 8-8 2-19 0-26-4-3-2-8-8-9-12 0-2 0-5 1-7h0c0 5 2 8 5 10 1 1 3 2 4 2h1c2-1 3-2 3-4 3 3 4 6 6 9-2 1-5 1-7 1h0c5 0 14 2 18-1h1 0c1-2 3-4 4-6-2 0-4 1-6 1 0-1 1-3 1-4 0-6-3-15-6-19l-2-2 2-2c0-1-1-4-1-5l-1-3-1-3h0c2-1 6 0 8 0l3 5v-2h0c-2-2-3-7-4-9 0-1 1-1 2-2 1 0 2-1 2-2 1-1 1-1 1-2h1v-1h16v-3-1c-1 1-2 1-2 1h-1c2-2 4-4 6-7 1-1 2-3 3-5l1 1 1-2h1c2 1 6 1 8 1s4 0 6-1z"></path><path d="M850 214c1-2 2-3 2-5 0 7-2 14-5 20h0c0-2 1-3 1-5 1-3 1-7 2-10z" class="B"></path><path d="M852 202l1-1c0-2 1-3 2-5l1-1c1-2 2-2 3-2-3 5-5 11-7 16 0 2-1 3-2 5l2-12z" class="E"></path><path d="M862 180h4 0c-1 1-2 1-2 2s1 1 1 1l-3 5c2-2 4-3 7-4-3 3-7 5-10 9h0c-1 0-2 0-3 2l-1 1c-1 2-2 3-2 5l-1 1-1-1c1-3 2-7 3-10v-1c1 0 1-1 2-2v-1l6-6v-1z" class="H"></path><defs><linearGradient id="r" x1="841.862" y1="194.227" x2="849.463" y2="194.612" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#7a7979"></stop></linearGradient></defs><path fill="url(#r)" d="M847 180h1c2 1 6 1 8 1s4 0 6-1v1l-6 6v1c-1 1-1 2-2 2v1c-1 3-2 7-3 10-1 2-2 4-3 7l-1 2c-1-2-1-4-1-7l-1 1-1 5v-2c-1 1-1 2-1 3v-5l-1-2-1-2-1-4-1-1v-3-1c-1 1-2 1-2 1h-1c2-2 4-4 6-7 1-1 2-3 3-5l1 1 1-2z"></path><path d="M842 203l1-1v-2h0c1 3 1 5 1 7-1 1-1 2-1 3v-5l-1-2z" class="L"></path><path d="M842 186c1-1 2-3 3-5l1 1c-2 3-4 6-4 10-1 2 0 4-1 6v-1h-1l-1-1v-3-1c-1 1-2 1-2 1h-1c2-2 4-4 6-7z" class="X"></path><path d="M847 180h1c2 1 6 1 8 1s4 0 6-1v1l-6 6v1c-1 1-1 2-2 2v1c-1 3-2 7-3 10-1 2-2 4-3 7 0-3 0-5 1-7 0-3-1-8 0-11h0c1-2 1-3 2-4l1-1c0-1 1-1 2-2h0c-1-2-4-1-6-1l-1-2z" class="N"></path><path d="M849 201v-3c0-2 1-4 1-5l1 1c0 2 0 3-1 5v1l1 1c-1 2-2 4-3 7 0-3 0-5 1-7z" class="E"></path><defs><linearGradient id="s" x1="845.425" y1="226.095" x2="818.253" y2="228.295" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#979697"></stop></linearGradient></defs><path fill="url(#s)" d="M819 201c1 0 2-1 2-2 1-1 1-1 1-2h1v-1h16l1 1 1 4 1 2 1 2v5c0-1 0-2 1-3v2l1-5 1-1c0 3 0 5 1 7-3 12-9 24-16 33l-7 7-4 2h0c1-2 3-4 4-6-2 0-4 1-6 1 0-1 1-3 1-4 0-6-3-15-6-19l-2-2 2-2c0-1-1-4-1-5l-1-3-1-3h0c2-1 6 0 8 0l3 5v-2h0c-2-2-3-7-4-9 0-1 1-1 2-2z"></path><path d="M810 209h0c2-1 6 0 8 0l3 5h0l-1 1c1 2 2 3 3 5 3 4 5 9 6 14 0 1 0 0-1 1-3-4-5-9-7-14-2-3-3-6-6-8-2-1-3-1-4-1l-1-3z" class="U"></path><path d="M821 212c2 2 4 5 7 5h1c0 3 2 5 2 9 1 1 1 1 1 3 0 0 0 1 1 2v3l1 1s-2 2-2 3l-2 2c0-2 0-4-1-5v-1c-1-5-3-10-6-14-1-2-2-3-3-5l1-1h0v-2z" class="J"></path><path d="M821 212c2 2 4 5 7 5h1c0 3 2 5 2 9 1 1 1 1 1 3l-11-15h0v-2z" class="Z"></path><path d="M812 215c5 3 11 14 12 21l1 2v1c0 3 0 5-1 7-2 0-4 1-6 1 0-1 1-3 1-4 0-6-3-15-6-19l-2-2 2-2c0-1-1-4-1-5z" class="G"></path><defs><linearGradient id="t" x1="831.152" y1="213.874" x2="840.877" y2="219.073" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#t)" d="M838 213v-1c1-3 0-7-1-10h0v-1c2-1 3 0 4 0l1 2 1 2c-1 11-4 21-9 30l-1-1v-3c-1-1-1-2-1-2 0-2 0-2-1-3 0-4-2-6-2-9l3-2c3-2 4-5 4-9 2 2 2 3 2 6v1z"></path><path d="M836 206c2 2 2 3 2 6v1c-3 2-5 4-6 8 0 3 0 6 1 10-1-1-1-2-1-2 0-2 0-2-1-3 0-4-2-6-2-9l3-2c3-2 4-5 4-9z" class="U"></path><defs><linearGradient id="u" x1="827.49" y1="196.738" x2="838.202" y2="203.633" xlink:href="#B"><stop offset="0" stop-color="#5f5d5e"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#u)" d="M819 201c1 0 2-1 2-2 1-1 1-1 1-2h1v-1h16l1 1 1 4c-1 0-2-1-4 0v1h0c1 3 2 7 1 10v1-1c0-3 0-4-2-6 0 4-1 7-4 9l-3 2h-1c-3 0-5-3-7-5h0c-2-2-3-7-4-9 0-1 1-1 2-2z"></path><path d="M819 201c4-1 8-3 12-1 1 1 2 2 3 4 0 0-1 1-2 1h-6l-3 3c-1 1-2 2-2 3v1c-2-2-3-7-4-9 0-1 1-1 2-2z" class="I"></path><path d="M834 204l2 2h0c0 4-1 7-4 9l-3 2h-1c-3 0-5-3-7-5h0v-1c0-1 1-2 2-3l3-3h6c1 0 2-1 2-1z" class="Q"></path><path d="M522 198l-3 1v4c8-1 22-3 28-8v-1h1l3 9 2 4c1 2 3 4 5 5 5 2 9 3 14 3l-4 3-1 2c-5 4-7 8-8 14-1 5-2 9-2 14 3 1 6 2 8 4-1 0-1 1-2 1-1 1-3 0-4 1h-3l-1 2c-1 1-1 2-1 4v1 8 1c1 2 1 4 1 5 0 2-1 3 0 5h-3-1l-28 1 1-1v-1-2l-1 1c-1 2-2 3-3 5h-1l-1 1s-1 1-2 1h-1l3-3c1-3 4-6 7-8l-1-1c-3 3-6 8-9 10l-8-9c-3-4-7-9-7-14-1-3-1-6-1-9 0-5 1-8 5-12 2-2 4-3 7-4h1v-1c1-1 3-1 5-1 1-1 0-17 0-21s0-10 1-14h4z" class="U"></path><path d="M531 244c-1-1-1-2-2-2 1-2 1-3 0-4-1-3 0-4 1-6 0-1 0-2 1-4h2c1-1 4-1 5 0-2 0-4 0-5 1-1 5 0 10-2 15z" class="F"></path><path d="M548 236v5c-1 4 0 9-1 13-1 2 0 4 0 6 0-1 0-2 1-3v-2-1c1 2 1 5 0 7v1c-1 4 1 10-2 14h-2c1-3 1-8 1-12l3-28z" class="I"></path><path d="M517 212l1 13c-1 3-1 6 0 9h2c1-2-1-4 1-6h1l5 1c1 1 1 3 1 5 1 1 0 4 0 5l-2 2h-1 0c-2-3-4-5-7-6h0-6v-1c1-1 3-1 5-1 1-1 0-17 0-21z" class="R"></path><path d="M522 228l5 1c1 1 1 3 1 5v6c-1-2-2-3-3-4-1 0-2-1-3-2v-6z" class="K"></path><path d="M520 218c3-1 7 0 11 0h28c2 0 7-1 9 0l-1 2h-1-2c-4 1-43 2-44 1v-3z" class="W"></path><path d="M544 276c-1 0-2 1-3 0-1-14 0-28 3-43 0-1 0-5 1-6h3c1 1 0 7 0 9l-3 28c0 4 0 9-1 12z" class="G"></path><path d="M533 229c1-1 3-1 5-1l-2 48c-2 1-3 1-4 1-1-2-1-1-1-2 2-7 2-15 1-22 0-3 0-6-1-9 2-5 1-10 2-15z" class="C"></path><path d="M533 229v4 1c1 3 1 6 0 8-1 4 1 7-1 11 0-3 0-6-1-9 2-5 1-10 2-15z" class="D"></path><path d="M551 280l-1-1c-1-5 0-10 0-15 0-13 2-25 6-38 1 0 2-1 3-1l-3 12 3-3c-1 5-2 9-2 14 3 1 6 2 8 4-1 0-1 1-2 1-1 1-3 0-4 1h-3l-1 2c-1 1-1 2-1 4v1 8 1c1 2 1 4 1 5 0 2-1 3 0 5h-3-1z" class="C"></path><path d="M552 280c2-6 1-12 2-19v8 1c1 2 1 4 1 5 0 2-1 3 0 5h-3zm4-43l3-3c-1 5-2 9-2 14 3 1 6 2 8 4-1 0-1 1-2 1-1 1-3 0-4 1h-3l-1 2c-1 1-1 2-1 4 0-8 1-16 2-23z" class="a"></path><defs><linearGradient id="v" x1="556.46" y1="200.337" x2="515.619" y2="229.123" xlink:href="#B"><stop offset="0" stop-color="#606060"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#v)" d="M522 198l-3 1v4c8-1 22-3 28-8v-1h1l3 9 2 4c1 2 3 4 5 5 5 2 9 3 14 3l-4 3c-2-1-7 0-9 0h-28c-4 0-8-1-11 0-1 2-1 4-1 6l1 1c0 2-2 5 0 7l-1 1h-1v-8h0l-1-13c0-4 0-10 1-14h4z"></path><path d="M537 205c2-1 4-2 5-2 1 2 1 3 2 4h-1c-3-1-3-1-6 0v-2z" class="C"></path><path d="M558 212c-1 0-1 1-2 1-2-1-5-5-6-7l1-1 2 2c1 2 3 4 5 5z" class="L"></path><path d="M537 207c3-1 3-1 6 0h1l2 4 1 2v1h-4 0c-3-1-3-2-5-4h0l-1-3z" class="D"></path><path d="M543 214c-1-1-1-2-2-4h1c1 0 2 1 3 1h1l1 2v1h-4z" class="G"></path><path d="M523 207h4l1 6 7 1c1-1 2 0 3-1v-3c2 2 2 3 5 4h-6c-1 0-2 1-3 1-2-1-2-1-4 0-3-1-6-1-9-1v-2c-1-1-1-4 0-5h2z" class="E"></path><path d="M521 212c-1-1-1-4 0-5h2l1 1 1-1h1v5c-1 1-3 1-4 1s-1 0-1-1z" class="B"></path><path d="M534 207h0c2-1 2-1 3-2v2l1 3h0v3c-1 1-2 0-3 1l-7-1-1-6 6-1 1 1z" class="C"></path><path d="M534 207h0c2-1 2-1 3-2v2l1 3h0v3c-1 1-2 0-3 1-1-3-1-4-1-7z" class="M"></path><path d="M512 235h6 0c3 1 5 3 7 6h0 1c0 2 1 3 1 5 1 1 1 2 1 4 1 7 1 16-3 22l-1 1c-3 3-6 8-9 10l-8-9c-3-4-7-9-7-14-1-3-1-6-1-9 0-5 1-8 5-12 2-2 4-3 7-4h1z" class="c"></path><path d="M525 244l2 2c1 1 1 2 1 4v6h-1-1c0-4 0-8-1-12z" class="G"></path><path d="M512 235h6 0c3 1 5 3 7 6h0 1c0 2 1 3 1 5l-2-2h-1c-4-4-5-6-11-5-3 0-6 3-8 5h-1c-1 1-1 2-2 3 0 2-1 4-1 7v1c0 1 0 3-1 5-1-3-1-6-1-9 0-5 1-8 5-12 2-2 4-3 7-4h1z" class="K"></path><path d="M500 260c1-2 1-4 1-5v-1c0-3 1-5 1-7 1-1 1-2 2-3-1 5 0 12 1 17 1 3 2 6 4 9 2 2 4 5 7 5h1v-1c5-3 8-9 10-14-1-1 0-3 0-4h1v-6c1 7 1 16-3 22l-1 1c-3 3-6 8-9 10l-8-9c-3-4-7-9-7-14z" class="O"></path><path d="M508 272l-1-1c-2-3-3-6-3-10h1c1 3 2 6 4 9l-1 2z" class="M"></path><path d="M509 270c2 2 4 5 7 5h1 0l-1 2-1 1c-2-1-5-4-7-6l1-2z" class="F"></path><path d="M527 260v4c-1 1-1 3-1 5-1 2-6 7-8 8h-2l1-2h0v-1c5-3 8-9 10-14z" class="M"></path><path d="M514 272h0c-3-3-5-5-6-10-1-3-2-13 0-16 0-1 1-1 2-1l-2 2v5l3 6 1-1v-3c1 0 3 0 4 1l1 4h0l1-1c2-4 4-9 2-13-3-3-5-2-8-2l1-1c1 0 4 0 6 1s4 4 5 6l1 2v5h1 1c0 1-1 3 0 4-2 5-5 11-10 14v-1c-1 0-2-1-3-1z" class="D"></path><path d="M525 251v5h1 1c0 1-1 3 0 4-2 5-5 11-10 14v-1c-1 0-2-1-3-1h3c2 0 3-2 4-4h1c3-6 3-11 3-17z" class="H"></path><path d="M524 273l1 1c-3 2-6 5-7 8l-3 3h1c1 0 2-1 2-1l1-1h1c1-2 2-3 3-5l1-1v2 1l-1 1 28-1h1 3c1 0 3-1 4 0l1 1-1 2c-2 1-4 2-5 4 0 1 1 1 2 2-2 1-3 1-5 1s-4 0-6 1l-6 2c-1 1-2 1-4 2h1 3c2-1 5-1 7-2l1 1h0l1 2v6h2l1 1v-2l1-2c2-1 3-1 5 0s5 6 6 8l3 3c0 1 0 2 1 3v1c1 8 0 15-6 22-5 6-14 9-22 11l2 9v1c-2 0-3 0-5-1-1-4-1-7-4-9v9-1h-3l-2-1c-2 0-2 0-4-1 0-2-3-2-5-2-1-1-3 0-4 0v-1h-2c-1 1-3 2-5 2s-3 0-4 1l-6 3c0 2-1 3-1 5-1-4 0-10 1-14-2 1-3 8-4 11-2-1-3-2-5-3l2-8c-9-2-18-4-24-12-5-6-5-13-4-21h-1c-2 1-4 1-7 1-5-1-10-3-13-7-3-5-4-10-3-15 1-6 6-10 11-13h1c1 5-4 7-6 11-1 4-1 8 1 11 1 4 4 6 7 8 3 0 8 1 11 0h0c1-3 1-5 1-7 1-1 1 0 2 1 1-3-2-3-3-4 0-1 0-1-1-2 4 1 7-1 11-2l3-3c0-1 0-1 1-2h0l-2-2h0l1-1h0c-1-2-2-3-4-4h3l-3-2v-2h3 9 0c1-1 1-1 1-2l2-2 2 1c1 1 0 1 1 1v-3 1h1 3l1 1h1c0 1 1 2 2 2h8v-1l1-1c-1-1-1-2-1-3h1l8 9c3-2 6-7 9-10z" class="c"></path><path d="M535 295h1 3c2-1 5-1 7-2l1 1h0l1 2c-2 1-5 2-7 2 1-1 1-1 3-2h-4-2c-2 1-4 3-5 4h0l-1 1v-1c1-1 2-3 2-4l1-1z" class="G"></path><path d="M548 296v6l3 5 7 5h-1l-4-2-5-5c-1-2-2-3-4-4h-3-2l2-3c2 0 5-1 7-2z" class="V"></path><path d="M551 301l1-2c2-1 3-1 5 0s5 6 6 8h-2l-1-1c-2-1-7-1-9 1l-3-5h2l1 1v-2z" class="C"></path><path d="M501 308l6-3c-1 8-1 16-1 24 0 2 0 7 1 9h2l-9 4c2-2 4-3 4-6 2-7 1-21-3-28z" class="J"></path><defs><linearGradient id="w" x1="541.716" y1="303.333" x2="547.566" y2="310.885" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#w)" d="M539 301h2 3c2 1 3 2 4 4l5 5h-3l-2 2-11-4h-2l4-7z"></path><path d="M544 301c2 1 3 2 4 4h-3c-1-1-2-1-2-2l1-2z" class="Y"></path><path d="M537 308l2-1 4 1c2 0 5 1 7 2h0l-2 2-11-4z" class="Z"></path><path d="M473 296c3 0 6-1 10 0v3 2l-2 2c-1 0-2 1-4 0v-1l-1-1v-1l-2 1c0 2 0 3-1 4l-1 1h-1-1-2v-1c-2 1-3 3-5 5 1-3 1-5 1-7 1-1 1 0 2 1 1-3-2-3-3-4 0-1 0-1-1-2 4 1 7-1 11-2z" class="P"></path><path d="M468 305c0-2 0-3 1-5h5v1c0 2 0 3-1 4l-1 1h-1-1-2v-1z" class="Q"></path><path d="M483 299l2-2h3 0c4 4 5 9 7 13h-2c-6 1-11 3-17 4-1 0-2 0-3-1l-1-1c1-1 1-1 1-2h0l1-1 3-1c2-2 3-3 4-5l2-2v-2z" class="I"></path><path d="M474 309c1 1 1 1 2 0h2c0 1 0 1-1 2 0 1-1 2-1 3-1 0-2 0-3-1l-1-1c1-1 1-1 1-2h0l1-1z" class="B"></path><path d="M483 299l2-2h3 0c4 4 5 9 7 13h-2c-1-2-2-2-3-3-1-2-2-4-4-6-4 0-5 6-9 7 2-2 3-3 4-5l2-2v-2z" class="V"></path><path d="M511 301l2 2v4c0 7 1 15 1 22v8c-2 1-3 1-5 1h-2c-1-2-1-7-1-9 0-8 0-16 1-24 1-1 3-2 4-4z" class="Q"></path><path d="M497 347h1l9-3c4-1 8-3 12-2s9 3 13 5v9-1h-3l-2-1c-2 0-2 0-4-1 0-2-3-2-5-2-1-1-3 0-4 0v-1h-2c-1 1-3 2-5 2s-3 0-4 1l-6 3c0 2-1 3-1 5-1-4 0-10 1-14z" class="J"></path><path d="M511 301c1-1 2-1 3-2 1 0 2 1 3 1 2 0 3 1 5 2l5 5c-1 2-2 5-1 7-1 6-2 16 0 22l1 1c0 1 1 2 2 3l1 1-3-1c-3-1-5-2-8-3h-5v-8c0-7-1-15-1-22v-4l-2-2z" class="Q"></path><path d="M524 338v-2c-2-4-2-9-1-14 0-3-1-6 0-9 2 7 1 14 1 21 1 2 1 3 0 4z" class="K"></path><path d="M513 307h1v-2l1 1c1 2 0 5 1 8v-6l1 28c2 1 3 1 5 1l2 1c1-1 1-2 0-4l3 6-8-3h-5v-8c0-7-1-15-1-22z" class="H"></path><path d="M511 301c1-1 2-1 3-2 1 0 2 1 3 1 2 0 3 1 5 2l5 5c-1 2-2 5-1 7-1 6-2 16 0 22l1 1c0 1 1 2 2 3l1 1-3-1-3-6c0-7 1-14-1-21 1-2 1-4 0-6-1-3-4-4-6-5l-1 6v6c-1-3 0-6-1-8l-1-1v2h-1v-4l-2-2z" class="J"></path><path d="M535 308h2l11 4 2-2h3l4 2h1c2 1 3 2 3 4 2 4 1 10-1 14-4 5-9 9-15 11-4 1-7 1-10 1-5-7-5-14-4-22l1-5 3-7z"></path><path d="M550 310h3l4 2 1 1v1c-2 1-7-1-10-2l2-2z" class="S"></path><path d="M532 315c2 0 7 2 8 4 0 1 0 2-1 3-3 0-5-1-7-2h-1l1-5z" class="J"></path><path d="M476 314c6-1 11-3 17-4h2l2 4 2 6c0 5 1 10 0 14-1 2-2 5-3 6-5 2-8 2-14 0-5-1-10-4-13-10-2-4-3-9-2-13 1-3 3-6 6-7h0c0 1 0 1-1 2l1 1c1 1 2 1 3 1z"></path><path d="M490 318c2 1 4 2 6 2v1c-1 0-4 1-6 0v-3z" class="T"></path><path d="M497 314l2 6c-1 0-2 0-3 1v-1c-2 0-4-1-6-2v-1l7-3z" class="L"></path><defs><linearGradient id="x" x1="536.663" y1="279.67" x2="533.837" y2="290.33" xlink:href="#B"><stop offset="0" stop-color="#666464"></stop><stop offset="1" stop-color="#7a7a7a"></stop></linearGradient></defs><path fill="url(#x)" d="M524 273l1 1c-3 2-6 5-7 8l-3 3h1c1 0 2-1 2-1l1-1h1c1-2 2-3 3-5l1-1v2 1l-1 1 28-1h1 3c1 0 3-1 4 0l1 1-1 2c-2 1-4 2-5 4 0 1 1 1 2 2-2 1-3 1-5 1s-4 0-6 1l-6 2c-1 1-2 1-4 2l-1 1c0 1-1 3-2 4v1l-3 4-3 9c-1-2 0-5 1-7l-5-5c-2-1-3-2-5-2-1 0-2-1-3-1-1 1-2 1-3 2-1 2-3 3-4 4l-6 3-3-8-2-3h0c-2 0-5-1-7-1l-1 1h0-3l-2 2v-3c-4-1-7 0-10 0l3-3c0-1 0-1 1-2h0l-2-2h0l1-1h0c-1-2-2-3-4-4h3l-3-2v-2h3 9 0c1-1 1-1 1-2l2-2 2 1c1 1 0 1 1 1v-3 1h1 3l1 1h1c0 1 1 2 2 2h8v-1l1-1c-1-1-1-2-1-3h1l8 9c3-2 6-7 9-10z"></path><path d="M510 291l-2-1h0c-2 0-1 0-2-1h0l2-1h1 0c2-1 5-1 7 0h3c3-1 6-1 9 0l7 1h-19c-2 0-4 1-6 2z" class="J"></path><path d="M555 280c1 0 3-1 4 0l1 1-1 2h-11l-27 1v-1c0-1 0-2 2-2l28-1h1 3z" class="H"></path><path d="M475 289h4c0 1 1 1 1 2h1 0 6c4 1 8 3 12 2l1 1c1 1 3 2 4 3h-8 0c-2 0-5-1-7-1l-1 1h0-3l-2 2v-3c-4-1-7 0-10 0l3-3c0-1 0-1 1-2h0l-2-2h0z" class="F"></path><path d="M483 296c-1-1-1-2 0-3h3 0c2 1 3 0 5 1h1c1 0 2 1 3 2l1 1c-2 0-5-1-7-1l-1 1h0-3l-2 2v-3z" class="B"></path><defs><linearGradient id="y" x1="530.081" y1="294.007" x2="527.9" y2="287.031" xlink:href="#B"><stop offset="0" stop-color="#848383"></stop><stop offset="1" stop-color="#a7a5a5"></stop></linearGradient></defs><path fill="url(#y)" d="M535 289c2 0 3 0 5 1h0v1c-1 0-2 0-2 1l1 1c-1 1-2 1-4 2l-1 1c0 1-1 3-2 4h-2c-1 1-2 0-3 0l-2-1h1c-1 0-2-1-2-1 1 0 2-1 3-2l-1-2-1-1h0c-1-1-3-1-4-1-2-1-3-2-5-3h19z"></path><path d="M525 293h3c2 1 5 0 7 1l-1 1v1c0 1-1 3-2 4h-2c-1 1-2 0-3 0l-2-1h1c-1 0-2-1-2-1 1 0 2-1 3-2l-1-2-1-1h0z" class="M"></path><path d="M510 291c2-1 4-2 6-2 2 1 3 2 5 3 1 0 3 0 4 1h0l1 1 1 2c-1 1-2 2-3 2 0 0 1 1 2 1h-1-1c-3-1-6-4-9-6l-11 8h-1c-2 0-4 0-5-1l-2-3h8c-1-1-3-2-4-3l-1-1h0c-1 0-2-1-3-2 1 0 3 1 4 1 0 0 0-1 1-1h3 6z" class="B"></path><path d="M526 294c0 1-1 2-1 2-1 1-2 1-3 0v-1c1 0 2-1 3-2l1 1z" class="D"></path><path d="M504 301l11-8c3 2 6 5 9 6h1l2 1c1 0 2 1 3 0h2v1l-3 4-3 9c-1-2 0-5 1-7l-5-5c-2-1-3-2-5-2-1 0-2-1-3-1-1 1-2 1-3 2-1 2-3 3-4 4l-6 3-3-8c1 1 3 1 5 1h1z" class="L"></path><path d="M503 301h1l1 1-3 3-1-1c0-2 0-2 2-3z" class="b"></path><path d="M527 300c1 0 2 1 3 0h2v1l-3 4c-1-1-2-3-3-4l1-1z" class="Z"></path><path d="M524 273l1 1c-3 2-6 5-7 8l-3 3h1c1 0 2-1 2-1l-2 2c-1 0-1 0-2-1l-1 1c-1 0-2 1-3 0h-2l-1 1h-1c-2 0-4 0-6 1h-5c-2 0 0 0-2 1h-2v1l2 1h0c-4 0-9-3-12 0h0-1c0-1-1-1-1-2h-4l1-1h0c-1-2-2-3-4-4h3l-3-2v-2h3 9 0c1-1 1-1 1-2l2-2 2 1c1 1 0 1 1 1v-3 1h1 3l1 1h1c0 1 1 2 2 2h8v-1l1-1c-1-1-1-2-1-3h1l8 9c3-2 6-7 9-10z" class="S"></path><path d="M484 280h15c3 0 7 0 10 1l2 2h0-25c-3 0-7 0-11 1l-3-2v-2h3 9z" class="H"></path><path d="M509 84c12-2 24 2 33 9 10 7 17 19 18 31 2 12-1 25-9 34-7 10-19 16-31 18-12 1-24-2-33-9-10-6-17-17-19-29-2-11 1-24 7-34 9-12 20-18 34-20z" class="W"></path><path d="M527 91h2c9 4 18 14 21 23 4 11 3 23-2 33-4 9-11 17-21 20-8 3-18 4-26 0-12-7-19-18-23-30-2-7-2-13 0-20v1c1 11 4 22 14 30 7 6 17 8 26 6 9-1 18-6 23-13s6-16 5-24c-2-11-10-20-19-26z" class="E"></path><path d="M262 307h0c2 0 2-1 3-1 1-1 0-1 1-1l1 1-1 1h0l-2 2v1 1c1 0 2-1 3 0 2 0 3 2 5 2 1 0 2 0 4 1l2 3c0 3 0 6-1 9v5 6c-1 4 0 9-1 12l1 4v4c-2 1-2 2-2 3s0 3-1 4c0 2 0 3-1 4l-2 1-8 5-7 6-3 4c1 1 2 2 4 2 1 1 2 1 3 3-1 2-5 7-4 9h1c-1 1-2 1-3 2 0 1 0 1 1 1l4 4c1 1 2 2 2 3l1 1h0c0 2-2 5-2 7h0c2 0 3 1 5 1l5-4h0l-1 1c-2 3-6 5-9 8h2l23-18v1l-1 1v2l-3 6-2 7v1c0 2-1 3-2 5-1 1-1 1-1 2l1-1s1 0 2-1c1 0 1-1 1-1h2c-3 3-5 2-5 6h-1l-3 1c-1-1-2-1-3-1-1-1-2-1-2-1-1 2-1 3-1 5v1h4c3-1 5-1 8-1 4 1 6 3 9 5 4 4 5 9 5 14-1 4-3 8-4 12-3 7-5 13-9 18-6 9-13 16-18 25-1-4-5-8-7-11l-8-12h-5c-3-4-6-8-10-11-4 5-8 9-10 14-1-1-1-1-1-2s-1-2-1-3c2-4 2-10 1-14h-1v-2c-1-1-1-3-2-4 0-1-1-2-2-2v-1l-1-1c1-1 1 0 1-1 0-3 0-4 2-5-1-2-1-4-2-6-1-6-4-10-5-16-4-18 3-38 11-54 3-6 5-11 8-15l-1-1 2-2h-1-2c3-2 5-6 6-9v-1l-1 1c-1-1-2-3-3-4 0-1 0-2-1-2-1-1-1-1-2-1l-1-2h1c0-1 1-1 1-2l1-1h0-1s-1 0-2-1l7-3c7-2 10-8 13-14v-1l1 2h0c0-1 1-1 2-2l2-2c4-4 7-8 11-12z" class="c"></path><path d="M216 462c2 1 2 1 3 0 1 3 2 6 2 9h-1v-2c-1-1-1-3-2-4 0-1-1-2-2-2v-1z" class="P"></path><path d="M215 461c1-1 1 0 1-1 0-3 0-4 2-5l1 7c-1 1-1 1-3 0l-1-1z" class="V"></path><path d="M224 443h1c2 4 3 8 4 12h-1c-1-1-3-1-4-3s-1-6 0-9z"></path><path d="M249 321l2-2c-3 5-5 10-8 16-3 8-5 17-10 25-1 1-2 3-3 4l-1-1 2-2h-1-2c3-2 5-6 6-9 0-1 1-2 1-3 3-3 5-9 6-13 2-5 5-10 8-15z" class="L"></path><path d="M219 401v4 1l1-1 2 1 4 1c1 2 0 6 0 9-1 3-1 6-1 9h0v-1c-1-3 0-5-1-7v-1h1v-3-1c1-1 0-2 1-4h0-3v3l-1 1v2l-2 6c0 1 0 1-1 2h-2s-2 1-2 2v6c-1-10 1-19 4-29z" class="D"></path><path d="M219 406l1-1 2 1v1c0 5-2 9-3 13-1-5 0-9 0-14z" class="M"></path><path d="M246 322v-1l1 2h0c0-1 1-1 2-2-3 5-6 10-8 15-1 4-3 10-6 13 0 1-1 2-1 3v-1l-1 1c-1-1-2-3-3-4 0-1 0-2-1-2-1-1-1-1-2-1l-1-2h1c0-1 1-1 1-2l1-1h0-1s-1 0-2-1l7-3c7-2 10-8 13-14z" class="R"></path><path d="M229 340c2-1 6-1 8-1l1 1-1 1v3c-1 1-1 2-1 3-1 1-1 0-1 2 0 1-1 2-1 3v-1l-1 1c-1-1-2-3-3-4 0-1 0-2-1-2-1-1-1-1-2-1l-1-2h1c0-1 1-1 1-2l1-1h0z" class="E"></path><path d="M229 340c2-1 6-1 8-1v1h-1c-1 0-1 0-2 1-2 1-5 2-7 2 0-1 1-1 1-2l1-1h0z" class="N"></path><path d="M288 441c4 4 5 9 5 14-1 4-3 8-4 12-3 7-5 13-9 18-6 9-13 16-18 25-1-4-5-8-7-11l-8-12-2-3c0-2-1-4 0-6 2-1 7 3 9 3 5 2 12 2 17-1 9-6 13-17 16-27-3 4-5 8-9 12v-1l1-5c1-2 1-3 2-4l5-9v-1-1c1-2 1-2 2-3z"></path><defs><linearGradient id="z" x1="227.473" y1="367.875" x2="256.751" y2="395.535" xlink:href="#B"><stop offset="0" stop-color="#c0bfbf"></stop><stop offset="1" stop-color="#efefef"></stop></linearGradient></defs><path fill="url(#z)" d="M264 311c1 0 2-1 3 0 2 0 3 2 5 2 1 0 2 0 4 1l2 3c0 3 0 6-1 9v5 6c-1 4 0 9-1 12l1 4v4c-2 1-2 2-2 3s0 3-1 4c0 2 0 3-1 4l-2 1-8 5-7 6-3 4-1 1c-3 3-5 7-8 11-3 5-8 11-7 18 0 3 2 7 4 10-3-2-5-5-7-7v-1c-3-6-2-14-2-20 0-3 0-6-1-8-2 0-2 0-3 1-4 4-5 11-8 16l-1 1v-1-4h0c1-7 4-16 8-23l12-17c7-18 12-35 25-50z"></path><path d="M245 370l2 1v4c-1-1-1-3-2-5z" class="O"></path><defs><linearGradient id="AA" x1="266.962" y1="310.489" x2="250.296" y2="370.689" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#AA)" d="M264 311c1 0 2-1 3 0 2 0 3 2 5 2 1 0 2 0 4 1l2 3c0 3 0 6-1 9v-1l-1-3c-1-4-3-6-7-8-1 0-2-1-3 0-2 2-4 4-5 6s-4 5-3 7l-2 3c-3 9 0 20 0 29h-2l-3-20c-1 4-3 8-3 12-1 7-1 13-1 20l-2-1v-2c-1-3-1-6-1-10 0 1 0 3-1 4v3c-1 1-1 4-1 6 1 3 1 10 0 13-1-6 0-11-1-16-1-3-1-5-1-7 1-1 1-2 1-2v-1c-1 1-1 2-2 3h0c7-18 12-35 25-50z"></path><defs><linearGradient id="AB" x1="270.677" y1="335.45" x2="253.767" y2="367.375" xlink:href="#B"><stop offset="0" stop-color="#b9b8b8"></stop><stop offset="1" stop-color="#e2e1e1"></stop></linearGradient></defs><path fill="url(#AB)" d="M258 327l1 1v1c5 5 6 13 8 20h0l4 12h1c1 1 1 2 2 3 0 2 0 3-1 4l-2 1-8 5-7 6s0-1 1-2l1-2c0-1 1-3 0-4v-1-2l-1-2h-1v-1-2c-1-1 0 0-1 0v7l-1-12h2c0-9-3-20 0-29l2-3z"></path><path d="M267 349l4 12h1c1 1 1 2 2 3 0 2 0 3-1 4l-2 1v-1c-1-2-1-4-2-6-1-5-2-8-2-13z" class="O"></path><path d="M271 361h1c1 1 1 2 2 3 0 2 0 3-1 4-1-2-1-4-2-7z" class="F"></path><path d="M258 327l1 1v1h-1c-1 2-2 3-2 5v4c0 1 1 2 1 3 2-2-1-6 1-8v2c-1 5 0 11 0 16 0 2 2 5 2 8 1 4 1 11 3 15l-7 6s0-1 1-2l1-2c0-1 1-3 0-4v-1-2l-1-2h-1v-1-2c-1-1 0 0-1 0v7l-1-12h2c0-9-3-20 0-29l2-3z" class="H"></path><path d="M258 327l1 1v1h-1c-1 2-2 3-2 5v4c-1 10 3 20 3 30-1-1-2-2-2-3l-1-1c-1-1 0 0-1 0v7l-1-12h2c0-9-3-20 0-29l2-3z" class="N"></path><defs><linearGradient id="AC" x1="281.02" y1="333.325" x2="262.98" y2="353.675" xlink:href="#B"><stop offset="0" stop-color="#afadaf"></stop><stop offset="1" stop-color="#cececd"></stop></linearGradient></defs><path fill="url(#AC)" d="M258 327c-1-2 2-5 3-7s3-4 5-6c1-1 2 0 3 0 4 2 6 4 7 8l1 3v1 5 6c-1 4 0 9-1 12l1 4v4c-2 1-2 2-2 3s0 3-1 4c-1-1-1-2-2-3h-1l-4-12h0c-2-7-3-15-8-20v-1l-1-1z"></path><path d="M275 360c0-3 1-7 0-10-1-4-1-12 0-16 1 5 1 10 1 15h0l1 4v4c-2 1-2 2-2 3z" class="E"></path><defs><linearGradient id="AD" x1="276.412" y1="319.297" x2="269.152" y2="330.603" xlink:href="#B"><stop offset="0" stop-color="#7d7d7d"></stop><stop offset="1" stop-color="#9b9a99"></stop></linearGradient></defs><path fill="url(#AD)" d="M269 314c4 2 6 4 7 8l1 3v1 5 6c-1 4 0 9-1 12h0c0-5 0-10-1-15h0c0-3-1-7-3-10l-3-3c1-2 0-5 0-7z"></path><defs><linearGradient id="AE" x1="264.57" y1="314.06" x2="265.725" y2="360.643" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#a7a6a6"></stop></linearGradient></defs><path fill="url(#AE)" d="M258 327c-1-2 2-5 3-7s3-4 5-6c1-1 2 0 3 0 0 2 1 5 0 7-1 7 0 17 1 24 1 3 3 14 2 16h-1l-4-12h0c-2-7-3-15-8-20v-1l-1-1z"></path><path d="M253 384c1 1 2 2 4 2 1 1 2 1 3 3-1 2-5 7-4 9h1c-1 1-2 1-3 2 0 1 0 1 1 1l4 4c1 1 2 2 2 3l1 1h0c0 2-2 5-2 7h0c2 0 3 1 5 1l5-4h0l-1 1c-2 3-6 5-9 8h2l23-18v1l-1 1v2l-3 6-2 7v1c0 2-1 3-2 5-1 1-1 1-1 2l1-1s1 0 2-1c1 0 1-1 1-1h2c-3 3-5 2-5 6h-1l-3 1c-1-1-2-1-3-1-1-1-2-1-2-1-1 2-1 3-1 5v1h4c3-1 5-1 8-1 4 1 6 3 9 5-1 1-1 1-2 3v1 1l-5 9c-1 1-1 2-2 4l-1 5v1c-2 1-3 4-5 5-2 2-4 5-7 6-7 2-10-1-15-4-2-2-4-3-5-4-5-5-9-10-16-13h-1c5 0 9 1 14 2-3-2-6-5-8-7-6-7-9-16-10-25 0-3 0-6 1-9 0-3 1-7 0-9l-4-1-2-1c3-5 4-12 8-16 1-1 1-1 3-1 1 2 1 5 1 8 0 6-1 14 2 20v1c2 2 4 5 7 7-2-3-4-7-4-10-1-7 4-13 7-18 3-4 5-8 8-11l1-1z"></path><path d="M243 450c1 0 2 1 3 1l-1 14c-1-5-2-10-2-15z" class="J"></path><path d="M256 464l1-1c1 1 1 2 2 3v1l7-2c1 0 2-1 2-2 1 1 2 2 1 3-1 2-3 4-4 5h-2-1c-1 0-2 1-4 0v-2c-1-2 1-4-2-5z" class="T"></path><path d="M259 459c1-9 2-20 7-27 0 2 0 5-1 7l-3 14c0 2-1 4-1 5l1 1h-3z" class="I"></path><path d="M259 459h3c3 0 9 1 12 4v1-1l1-1h2l1 2v1c-2 1-3 4-5 5-1-3-2-7-4-8l-1 1c0 1-1 2-2 2l-7 2v-1c-1-1-1-2-2-3l-1 1c-2 2-2 5-3 7h0v-3c1-4 3-6 6-9z" class="D"></path><path d="M267 440c-1-1-1-3 0-4v1h4c3-1 5-1 8-1 4 1 6 3 9 5-1 1-1 1-2 3v1 1l-5 9c-1 1-1 2-2 4l-1 5-1-2h-2l-1 1v1-1-1c0-2 0-3-1-5l-6-17z" class="R"></path><path d="M278 454l1-1h1c0 1 0 1 1 2-1 1-1 2-2 4l-1-5z" class="X"></path><path d="M267 440c-1-1-1-3 0-4v1l1 1h1c3 3 4 9 5 12-2-2-2-5-4-7l-3-3z" class="J"></path><path d="M267 440l3 3c2 2 2 5 4 7 1 2 2 4 1 5l-2 2-6-17z" class="U"></path><defs><linearGradient id="AF" x1="282.343" y1="449.726" x2="273.688" y2="443.975" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#646263"></stop></linearGradient></defs><path fill="url(#AF)" d="M273 441c1 0 3 0 3 1 2 2 4 3 7 4 1-1 1-1 3 0l-5 9c-1-1-1-1-1-2h-1l-1 1h0l-5-13z"></path><path d="M271 437c3-1 5-1 8-1 4 1 6 3 9 5-1 1-1 1-2 3v1 1c-2-1-2-1-3 0-3-1-5-2-7-4 0-1-2-1-3-1 0-2-1-3-2-4z" class="P"></path><path d="M271 437c3-1 5-1 8-1-1 1-3 2-4 2 0 1 1 2 2 3 2 1 8 1 9 4v1c-2-1-2-1-3 0-3-1-5-2-7-4 0-1-2-1-3-1 0-2-1-3-2-4z" class="X"></path><defs><linearGradient id="AG" x1="270.922" y1="431.867" x2="255.779" y2="420.98" xlink:href="#B"><stop offset="0" stop-color="#b4b3b4"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#AG)" d="M270 413l-1 1c-2 3-6 5-9 8h2l23-18v1l-1 1v2l-3 6-2 7v1c0 2-1 3-2 5-1 1-1 1-1 2l1-1s1 0 2-1c1 0 1-1 1-1h2c-3 3-5 2-5 6h-1l-3 1c-1-1-2-1-3-1-1-1-2-1-2-1 0-2 3-4 5-5l1-1c0-1 0-1-1-2s-2-1-4-1c-7 2-14 7-18 13-3 5-4 10-5 16-1 0-2-1-3-1 1-6 2-11 5-16 3-4 5-8 8-12 2-4 4-9 6-13h0c0 2-2 5-2 7h0c2 0 3 1 5 1l5-4h0z"></path><path d="M273 426v1c-1 1-1 1-1 3 1 1 2 1 4 2l-3 1c-1-1-2-1-3-1-1-1-2-1-2-1 0-2 3-4 5-5z" class="B"></path><defs><linearGradient id="AH" x1="382.419" y1="585.981" x2="275.451" y2="628.866" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#f3f2f2"></stop></linearGradient></defs><path fill="url(#AH)" d="M295 406v-1l3-3c6 3 12 7 17 11 8 7 13 16 17 26l-1 1c2 2 2 6 2 9l-1 1c0 1 0 2 1 3h0c0 5-2 9-1 14h1c1-2 2-2 3-3 4-1 8-4 12-5l2-1 2-1 3 10c-1 2-2 3-1 5l-1 1v1l2 1-1 4h2v1c0 1 0 2 1 3v1c-1 2-1 3-1 4 0 3-1 4 0 7h1c2 0 3-1 4-1 1-1 3-2 4-2h1 1l2 6 4 9 48 125 7 15 6 17c1 1 1 3 1 5s1 3 2 5l1 1 6 14c-1 2-2 3-1 4 1 3 3 7 2 10h-1s0 1-1 1h0v-1l-6-6-2-2v-3l-3-4c-1-1-1-1-3-2-3-2-6-4-10-6-2 0-4-2-5-2-4-1-8-3-11-2l-1 1-1 1-5-1c-10-2-19-3-29-4-3 1-5 1-7 1-1 1-8 5-8 5-7 7-9 18-8 27 0 3 1 6 3 8 2 1 4 4 6 5l3 2h3 0v1c1 3 3 5 5 8 2 4 4 8 6 13 1 1 2 3 2 4-5-3-9-6-15-7l4 12-6 1c1 8 6 20 12 25 3 3 8 4 12 4 5-1 9-3 12-7 2-3 3-6 5-9-1 7-3 13-9 17-5 3-12 4-17 2-13-3-20-19-26-29h-1c-1 4-2 6-4 9-1 0-3 1-3 2l-1 1h0c0-2 1-5 1-7v-1c-1-1 0-3 0-4 0-4 0-7-1-10 0-6-1-11-3-16-6 3-12 4-20 4 4-2 10-5 11-10 1-4 0-9 2-13 2-7 6-12 10-18l-20 3c1-3 4-5 7-8 5-6 10-13 17-18l4-2c6-2 12-4 18-4 5-1 9-1 13-2l1-1c-2-2-6-3-9-4-9-4-19-5-29-7-5-1-9-2-13-4l-10-4c0-1-2-1-2-1l-7-4-12-9c-9-9-18-18-26-28-4-6-8-13-11-20 0-1-1-2-1-3-3-10-5-19-6-29l-2-2-5-3c-1-1-1-3-3-4-2-2-3-3-4-6l-3-3-4-4v-1l-2-3h-1l-1-1-2-3c0-1-1-2-1-2l-2-2c-2-5-5-13-4-18 1-3 3-7 5-11 0 1 1 2 1 3s0 1 1 2c2-5 6-9 10-14 4 3 7 7 10 11h5l8 12c2 3 6 7 7 11 5-9 12-16 18-25 4-5 6-11 9-18 1-4 3-8 4-12 0-5-1-10-5-14-3-2-5-4-9-5-3 0-5 0-8 1h-4v-1c0-2 0-3 1-5 0 0 1 0 2 1 1 0 2 0 3 1l3-1h1c0-4 2-3 5-6l6-3c3-3 6-7 7-11 1-2 1-3 3-4l-3-2z"></path><path d="M415 665c1 1 3 2 4 3v1c2 4 3 6 4 11-1-1-3-1-4-1l-8-4c2 1 4 1 6 2h2l-1-1v-1c-2-2-3-6-3-9v-1z" class="F"></path><path d="M304 605l10 11c-1 1 0 0-2-1h-2-10c-4 0-8-1-11-2h-1c1-1 2-1 3-1h2 1c1 1 1 1 3 1 1-1 4-1 6-1h2c1 1 2 0 3 0l-2-2c1-1-2-3-2-5z" class="B"></path><path d="M390 653c5 2 10 4 15 5l1-1c2 1 2 2 4 3 3 2 7 4 10 7l-1 1h0c-1-1-3-2-4-3-3-2-7-3-10-4h-1l-15-4c2-1 3 0 5 0 0 1 1 0 2 0-2-1-7-2-9-3h-1 0 3l1-1z" class="D"></path><path d="M390 653c-2 0-5-1-7-1-3-1-5-2-7-2v-1h2c-2-1-3 0-4-1h-8c-1-1-3 0-4-1h-7l4-1c9 1 19 2 28 4 5 1 10 3 15 5l4 2-1 1c-5-1-10-3-15-5z" class="K"></path><path d="M306 610h0c-3-4-5-8-8-12-2-3-4-7-6-10-2-2-5-4-7-5s-3-2-4-3c-4-4-8-6-9-11v-1c-1-1-1-1-1-2h0c0-1-1-2-1-3h1l1-1c1 5 4 11 8 15 3 3 8 5 12 8l1 2c0 1 1 2 2 2 1 2 2 5 4 7 1 3 3 6 5 9 0 2 3 4 2 5z" class="C"></path><path d="M349 680l3-1c-7 7-9 18-8 27 0 3 1 6 3 8-2-1-3-3-4-5v8l1 1 3 7h-1c-1-2-3-5-3-8h-1c-1-4 0-8-2-11 2-2 1-4 2-6l1 1v-3l1-1c-1-1-1-1-1-2l1-1v-2l1-2v-2-1h1v-1h0c1-1 1-2 1-3h1c0-1 0-1 1-2h-1l-4 4c-3 4-5 10-7 15l-1 2 1 1v3h1c-4 4-5 8-8 12l4-13c3-9 6-19 15-25z" class="O"></path><path d="M270 519c1-7 5-13 8-18 1-3 6-8 6-11 0-1 1-2 2-3 0-2 2-4 2-5l2-4v-1c1 0 1-1 1-1v-1l1-1v-1-2h1v-1-1l2-2c-1 1-1 3-1 4-2 3-3 7-4 10h0l2-3c0 2-1 3-1 4h0l2-3h0c-1 3-3 7-4 10l-1 1c-2 3-4 6-6 10-4 5-7 11-9 16l-3 3z" class="C"></path><path d="M272 523l4-8c0 2 0 3-1 5 0 4 0 9 1 13l1 2c2 7 3 13 6 20 1-6 1-10 3-15l1 1c-2 7-2 15 0 23l8 25c-1 0-2-1-2-2l-1-2c-3-11-9-20-13-30s-5-21-6-31l-1-1h0z" class="M"></path><path d="M270 519l3-3v1c-1 2-1 4-2 5l1 1h0c-2 6-4 11-5 17-2 12-2 22 4 33 1 4 4 8 5 12 1 2 2 3 3 5h0v1c-2-1-3-4-5-5-9-12-13-25-11-40 0-5 1-10 3-15 1-4 3-8 4-12h0z" class="F"></path><defs><linearGradient id="AI" x1="381.404" y1="661.107" x2="364.096" y2="692.893" xlink:href="#B"><stop offset="0" stop-color="#908e8e"></stop><stop offset="1" stop-color="#cfcfcf"></stop></linearGradient></defs><path fill="url(#AI)" d="M348 678l-12 4c6-5 12-10 21-12l1-1c5-1 11-1 16-1 13 1 25 3 37 7l8 4v1c-2 0-4-2-5-2-4-1-8-3-11-2l-1 1-1 1-5-1c-10-2-19-3-29-4-3 1-5 1-7 1-1 1-8 5-8 5l-3 1-1-1v-1h0z"></path><path d="M351 676v-1l1-1h1c2-2 7-2 9-2v1h5c-3 1-5 1-7 1-3 1-6 1-9 2z" class="B"></path><path d="M351 676c3-1 6-1 9-2-1 1-8 5-8 5l-3 1-1-1v-1h0c0-1 3-2 3-2z" class="C"></path><defs><linearGradient id="AJ" x1="445.4" y1="683.489" x2="420.439" y2="682.977" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M402 655c2 0 3 0 5 1h0c1 0 2 1 2 1h1c1 1 2 1 2 1h4l7 4c4 0 4 2 7 3l5 4c0 2 1 3 2 5l1 1 6 14c-1 2-2 3-1 4 1 3 3 7 2 10h-1s0 1-1 1h0v-1l-6-6-2-2v-3l-3-4c-1-1-1-1-3-2-3-2-6-4-10-6v-1c1 0 3 0 4 1-1-5-2-7-4-11v-1h0l1-1c-3-3-7-5-10-7-2-1-2-2-4-3l-4-2z"></path><path d="M435 692c1 1 2 3 2 5l-2-2v-3z" class="L"></path><path d="M419 668h0c1 1 2 1 3 2h0v1c2 2 4 4 4 7-1 1-1 2-2 2h-1c-1-5-2-7-4-11v-1z" class="I"></path><path d="M423 662c4 0 4 2 7 3l5 4c0 2 1 3 2 5l1 1c-6-2-10-9-15-12v-1z" class="E"></path><path d="M402 655c2 0 3 0 5 1h0c1 0 2 1 2 1h1c1 1 2 1 2 1h4l7 4v1h-1c0 1 0 1-1 1 1 1 1 1 1 2 4 5 10 8 13 13-1 0-4-3-5-4-3-2-5-4-8-5h0c-1-1-2-1-3-2l1-1c-3-3-7-5-10-7-2-1-2-2-4-3l-4-2z" class="F"></path><path d="M416 658l7 4v1h-1c0 1 0 1-1 1 1 1 1 1 1 2-1-1-2-2-4-3 0-1-1-1-2-2s-2-2-4-3h4z" class="J"></path><defs><linearGradient id="AK" x1="366.804" y1="655.81" x2="367.793" y2="635.171" xlink:href="#B"><stop offset="0" stop-color="#888687"></stop><stop offset="1" stop-color="#c2c1c0"></stop></linearGradient></defs><path fill="url(#AK)" d="M321 638v-1-1l2-3h2l4 2c3 0 4 2 6 3 1 0 3 0 5 1l4 1h3c1 0 3 0 5 1v-1c1 0 2 0 3-1 2-1 7 0 10 0 2 0 3 0 5-1l35 13 11 7h-4s-1 0-2-1h-1s-1-1-2-1h0c-2-1-3-1-5-1-5-2-10-4-15-5-9-2-19-3-28-4-6 0-12 0-17-1-8-2-15-4-21-7z"></path><path d="M335 638c1 0 3 0 5 1l4 1h1c1 0 1 1 2 2h-5c-1-1-3-1-4-1l2-1h0l-6-1 1-1z" class="M"></path><path d="M222 490c2-5 6-9 10-14 4 3 7 7 10 11h5l8 12c2 3 6 7 7 11 0 1-1 3-1 3-2 3-3 6-4 9-2-6-4-15-9-18-1 0-1-1-2-1l-1 1c1 9 4 19 4 29v15l-2-2-5-3c-1-1-1-3-3-4-2-2-3-3-4-6l-3-3-4-4v-1l-2-3h-1l-1-1-2-3c0-1-1-2-1-2l-2-2c-2-5-5-13-4-18 1-3 3-7 5-11 0 1 1 2 1 3s0 1 1 2z"></path><path d="M243 490l1 1c0 3-2 5-3 8-2 3-3 6-5 9h0l-1-1c1-3 2-6 4-9 1-3 2-6 4-8z" class="Y"></path><path d="M225 514c1-4 2-11 4-13 2 2 1 8 1 11v2c-2 1-2 2-4 2l-1-2z" class="N"></path><path d="M230 512c1 2 1 4 2 7v1l1 4v2h0c0 1 1 2 1 3-1 1-1 1-2 1l-4-4v-1h1c0-2-3-4-3-6l-2-5h1l1 2c2 0 2-1 4-2v-2z" class="I"></path><path d="M247 487l8 12c2 3 6 7 7 11 0 1-1 3-1 3-3-1-5-5-7-7-3-5-8-10-10-15l-1-1-1-3h5z" class="T"></path><path d="M220 485c0 1 1 2 1 3s0 1 1 2c-3 9 0 16 2 24l2 5c0 2 3 4 3 6h-1l-2-3h-1l-1-1-2-3c0-1-1-2-1-2l-2-2c-2-5-5-13-4-18 1-3 3-7 5-11z" class="M"></path><defs><linearGradient id="AL" x1="248.394" y1="533.408" x2="233.096" y2="531.163" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#AL)" d="M232 520c4 5 8 10 12 14l4 4 1-5v15l-2-2-5-3c-1-1-1-3-3-4-2-2-3-3-4-6l-3-3c1 0 1 0 2-1 0-1-1-2-1-3h0v-2l-1-4z"></path><path d="M235 533c3 2 5 4 8 6l4 4v3h0l-5-3c-1-1-1-3-3-4-2-2-3-3-4-6z" class="U"></path><defs><linearGradient id="AM" x1="392.138" y1="591.636" x2="285.942" y2="538.639" xlink:href="#B"><stop offset="0" stop-color="#b6b5b4"></stop><stop offset="1" stop-color="#e4e4e4"></stop></linearGradient></defs><path fill="url(#AM)" d="M354 480l-2 1c-1 1-2 1-3 2-2 0-5 2-7 3-7 4-14 7-20 12 1 0 2 0 3 1-3 2-6 5-9 8-10 9-22 21-24 35-1 3-1 5 1 8 3 4 6 7 9 11v1 1c1 3 3 6 3 9l2 6c1 2 3 7 2 9 0 2 2 3 2 5 1 2 3 5 4 7l2 2 3 2v1l4 5c0 1 0 1 1 1v2c5 4 10 7 15 10 10 6 19 12 30 16-2 1-3 1-5 1-3 0-8-1-10 0-1 1-2 1-3 1v1c-2-1-4-1-5-1h-3l-4-1c-2-1-4-1-5-1-2-1-3-3-6-3l-4-2h-2l-2 3v1 1c-12-3-20-13-29-20h0 0v-1c1 1 1 1 2 1h1c1 1 3 0 5 0 1 1 3 1 5 1l1-1h3l1-1h3c1 1 1 1 2 0l-1-1-10-11c-2-3-4-6-5-9-2-2-3-5-4-7l-8-25c-2-8-2-16 0-23 4-11 7-23 15-33 2-3 6-6 9-9h0c1 0 2-1 2-1h1l2-2c1 0 2-1 3-1l3-2c2 0 4-2 6-3 9-4 17-8 26-10z"></path><path d="M325 612h0c-3 0-7-5-9-7v-1l3 2h0v-1l1-1 4 5c0 1 0 1 1 1v2z" class="C"></path><path d="M322 498c1 0 2 0 3 1-3 2-6 5-9 8-10 9-22 21-24 35-1 3-1 5 1 8 3 4 6 7 9 11v1 1c-2-2-4-6-7-7-1 0-4-4-5-6-1-4-1-9 0-14 5-18 18-27 32-38z" class="F"></path><path d="M354 479h2v1c0 1 0 2 1 3v1c-1 2-1 3-1 4 0 3-1 4 0 7h1c2 0 3-1 4-1 1-1 3-2 4-2h1 1l2 6 4 9c-2 1-4 2-6 4 0 0-1 1-1 2l1 3-1 4h0c-1 0-1 1-2 1v1l-1-1h0l-3-3c-8 1-14 6-21 10v1c-5 2-9 5-13 9-1 0-2 1-3 3-7 5-12 13-15 21l-1-2v2l-2-3 1 9c0 1 0 2 1 4 0 1 0 3 1 4l-1 2-2-6c0-3-2-6-3-9v-1-1c-3-4-6-7-9-11-2-3-2-5-1-8 2-14 14-26 24-35 3-3 6-6 9-8-1-1-2-1-3-1 6-5 13-8 20-12 2-1 5-3 7-3 1-1 2-1 3-2l2-1v-1z" class="G"></path><path d="M338 506l-9 3c1-2 4-5 7-5l2 2z" class="B"></path><path d="M302 561h1v-3c0-1-1-5 0-6 1 1 1 5 1 6l1 1 1 9c0 1 0 2 1 4 0 1 0 3 1 4l-1 2-2-6c0-3-2-6-3-9v-1-1z" class="N"></path><path d="M349 483c1-1 2-1 3-2 2 1 2 1 3 3v1c-3 4-9 7-14 11-7 5-14 10-21 16-5 5-10 8-12 15 0-3 2-7 4-9v-1h0v-1c0-1 1-2 2-3l9-9c1-2 3-3 5-5v-1l-3 1c-1-1-2-1-3-1 6-5 13-8 20-12 2-1 5-3 7-3z" class="R"></path><path d="M314 513c3 0 8-6 11-8l1 1-14 12v-1h0v-1c0-1 1-2 2-3z" class="D"></path><path d="M328 499c1 0 3-2 3-3 1 0 2-1 3-1-2 3-4 8-6 10h-1l-1 1-1-1c-3 2-8 8-11 8l9-9c1-2 3-3 5-5z" class="N"></path><path d="M322 498c6-5 13-8 20-12 2-1 5-3 7-3s3 0 5 1v1c-3 2-5 3-8 5l-12 5c-1 0-2 1-3 1 0 1-2 3-3 3v-1l-3 1c-1-1-2-1-3-1z" class="H"></path><defs><linearGradient id="AN" x1="360.452" y1="495.481" x2="342.798" y2="511.067" xlink:href="#B"><stop offset="0" stop-color="#8f8d8e"></stop><stop offset="1" stop-color="#c2c1c1"></stop></linearGradient></defs><path fill="url(#AN)" d="M354 479h2v1c0 1 0 2 1 3v1c-1 2-1 3-1 4 0 3-1 4 0 7h1c2 0 3-1 4-1 1-1 3-2 4-2h1 1l2 6 4 9c-2 1-4 2-6 4 0 0-1 1-1 2l1 3-1 4h0c-1 0-1 1-2 1v1l-1-1h0l-3-3-1-2 1-1c0-2-1-4-2-6s-5-4-7-5h-1c-4-1-8 0-12 2l-2-2c1-1 2-2 3-2l1-1 1-1s1-1 2-1c4-3 9-9 12-13v-1-1c-1-2-1-2-3-3l2-1v-1z"></path><path d="M367 492l2 6h-5c-2 0-4-1-6-1l8-5h1z" class="V"></path><path d="M361 512c0-1-1-1-1-2l1-1c1 0 2 1 2 2l1 1c1 0 1 0 2 1l1 3-1 4h0c-1 0-1 1-2 1v1l-1-1h0l-3-3-1-2 1-1 1 1h1l-1-4z" class="E"></path><path d="M361 512h1c1 1 2 3 2 5v2l-1 2-3-3-1-2 1-1 1 1h1l-1-4z" class="B"></path><defs><linearGradient id="AO" x1="370.443" y1="502.506" x2="364.722" y2="507.87" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#AO)" d="M364 498h5l4 9c-2 1-4 2-6 4-1-2-2-2-4-2-1-1-2-2-2-3l1-1c1-2 3-3 5-5l-1-1-2-1z"></path><path d="M307 560c0-10 0-19 4-28s12-18 22-21c5-2 7 0 10 4 1 1 1 2 3 3l3-6c4 0 7 1 10 4l1 2c-8 1-14 6-21 10v1c-5 2-9 5-13 9-1 0-2 1-3 3-7 5-12 13-15 21l-1-2z"></path><path d="M295 406v-1l3-3c6 3 12 7 17 11 8 7 13 16 17 26l-1 1c2 2 2 6 2 9l-1 1c0 1 0 2 1 3h0c0 5-2 9-1 14h1c1-2 2-2 3-3 4-1 8-4 12-5l2-1 2-1 3 10c-1 2-2 3-1 5l-1 1v1l2 1-1 4v1c-9 2-17 6-26 10-2 1-4 3-6 3l-3 2c-1 0-2 1-3 1l-2 2h-1s-1 1-2 1h0c-3 3-7 6-9 9-8 10-11 22-15 33l-1-1c-2 5-2 9-3 15-3-7-4-13-6-20l-1-2c-1-4-1-9-1-13 1-2 1-3 1-5l-4 8-1-1c1-1 1-3 2-5v-1c2-5 5-11 9-16 2-4 4-7 6-10l1-1c1-3 3-7 4-10h0l-2 3h0c0-1 1-2 1-4l-2 3h0c1-3 2-7 4-10 0-1 0-3 1-4h0c1-4 2-8 2-13 1-3 1-7 0-10v-3h0c-2-4-5-5-8-6-1-1-2-1-2-1h-6s-1 1-2 0h-2c-1 0-1 1-2 0l-1-1h-1 0l3-1h1c0-4 2-3 5-6l6-3c3-3 6-7 7-11 1-2 1-3 3-4l-3-2z" class="C"></path><path d="M286 510c2-1 3-4 4-6l1 1c0 1-1 2-1 3v1l-2 2c-2 1-3 4-5 5l3-6h0z" class="O"></path><path d="M286 540l5-17c0-2 1-5 3-7 0-1 1-2 2-3 3-4 10-12 15-14-3 3-7 6-9 9-8 10-11 22-15 33l-1-1z" class="F"></path><defs><linearGradient id="AP" x1="354.581" y1="475.341" x2="338.472" y2="479.147" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#a4a3a2"></stop></linearGradient></defs><path fill="url(#AP)" d="M350 458l2-1 3 10c-1 2-2 3-1 5l-1 1v1l2 1-1 4v1c-9 2-17 6-26 10-2 1-4 3-6 3v-1l1-1c0-1 0-1 1-1 1-1 3-2 4-3h1c1-1 3-2 3-4h1v-1l-1-1c3 0 6-2 8-4l11-8h1l1-1c1-1 0-7-1-8 0-1 0-2-2-2z"></path><path d="M286 510c3-8 9-18 14-24 3-2 9-6 10-8-1 0-3 1-4 2-4 2-7 5-10 7h-1c2-3 8-6 11-8s5-4 7-5c1-2 1-4 3-6h0c0 3-1 6-1 9-1 2 0 4-1 6l-1 1h0l1 1 2-3c0 2 0 2-1 3v1c-5 3-8 7-12 10-4 4-9 8-13 13v-1c0-1 1-2 1-3l-1-1c-1 2-2 5-4 6z" class="B"></path><path d="M304 463c1-3 2-7 2-11 1-7-2-11-6-15 3 1 5 3 7 5 2 5 2 9 2 14-1 1-1 2-1 3l1 1v-1c4-1 8-2 11-3l-1 7c-1 2-2 4-2 6h2l-1 1h0c-1 3-2 9-2 12l-2 3-1-1h0l1-1c1-2 0-4 1-6 0-3 1-6 1-9l3-10c-2 0-4 0-6 1h-1c-4 1-7 6-9 10-4 6-6 12-9 18-6 9-13 18-18 28l-4 8-1-1c1-1 1-3 2-5v-1c2-5 5-11 9-16 2-4 4-7 6-10l1-1c1-3 3-7 4-10l2-2v-1l6-11h1c0-1 1-2 2-2z" class="J"></path><path d="M288 490v3h0c-1 3-4 5-6 7 2-4 4-7 6-10z" class="F"></path><path d="M293 479l2-2v-1l6-11h1c0-1 1-2 2-2l-4 9c-3 6-6 13-11 17 1-3 3-7 4-10z" class="B"></path><defs><linearGradient id="AQ" x1="346.083" y1="473.708" x2="316.514" y2="471.34" xlink:href="#B"><stop offset="0" stop-color="#999798"></stop><stop offset="1" stop-color="#c5c3c3"></stop></linearGradient></defs><path fill="url(#AQ)" d="M331 450h1c0 1 0 2 1 3h0c0 5-2 9-1 14h1c1-2 2-2 3-3 4-1 8-4 12-5l2-1c2 0 2 1 2 2 1 1 2 7 1 8l-1 1h-1-3l-33 17v-1c1-1 1-1 1-3 0-3 1-9 2-12h0l1-1h-2c0-2 1-4 2-6 1 0 2-1 3-2 2-1 3-2 4-4 0-2 1-3 2-5l1-2v1h1l1-1z"></path><path d="M333 453h0c0 5-2 9-1 14v5c-1 1-1 2-2 3-1-4 0-9-1-13v-1h0c1-2 1-3 2-5 1-1 1-2 2-3z" class="B"></path><defs><linearGradient id="AR" x1="351.348" y1="463.099" x2="343.834" y2="465.477" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#989797"></stop></linearGradient></defs><path fill="url(#AR)" d="M350 458c2 0 2 1 2 2 1 1 2 7 1 8l-1 1h-1-3-1c-1-2-2-2-3-3h-1c2-3 5-3 5-7h0l2-1z"></path><path d="M331 450h1c0 1 0 2 1 3-1 1-1 2-2 3-1 2-1 3-2 5h0l-4 4c-2 1-5 2-6 4h-2c0-2 1-4 2-6 1 0 2-1 3-2 2-1 3-2 4-4 0-2 1-3 2-5l1-2v1h1l1-1z" class="K"></path><defs><linearGradient id="AS" x1="306.734" y1="478.635" x2="296.282" y2="428.21" xlink:href="#B"><stop offset="0" stop-color="#bebdbd"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#AS)" d="M295 406v-1l3-3c6 3 12 7 17 11 8 7 13 16 17 26l-1 1c2 2 2 6 2 9l-1 1h-1l-1 1h-1v-1l-1 2c-1 2-2 3-2 5-1 2-2 3-4 4-1 1-2 2-3 2l1-7c-3 1-7 2-11 3v1l-1-1c0-1 0-2 1-3 0-5 0-9-2-14-2-2-4-4-7-5 4 4 7 8 6 15 0 4-1 8-2 11-1 0-2 1-2 2h-1l-6 11v1l-2 2h0l-2 3h0c0-1 1-2 1-4l-2 3h0c1-3 2-7 4-10 0-1 0-3 1-4h0c1-4 2-8 2-13 1-3 1-7 0-10v-3h0c-2-4-5-5-8-6-1-1-2-1-2-1h-6s-1 1-2 0h-2c-1 0-1 1-2 0l-1-1h-1 0l3-1h1c0-4 2-3 5-6l6-3c3-3 6-7 7-11 1-2 1-3 3-4l-3-2z"></path><path d="M307 442c2 2 4 3 4 6 1 2 0 3-1 5h0c0 1-1 2-1 3h0c0-5 0-9-2-14z" class="D"></path><path d="M297 441c2 4 3 8 2 13-1 2 0 4-1 7 0 1 0 2-1 3 0 2-1 3-1 4h-1v-1c1-4 2-8 2-13 1-3 1-7 0-10v-3h0z" class="O"></path><path d="M308 423c0-3 0-6-1-8v-1-1h1c1 1 1 3 1 4v1l1 1v3c2 0 3 2 4 3 1 3 1 6 1 9 1 1 1 1 0 1l1 2c1 0 2 1 3 2l1 1-1 2-1 1c-1-2-1-3-2-4-9-9-22-9-34-8l1-1c4-2 20-1 24 0h1v-1-6z" class="a"></path><path d="M310 422c2 0 3 2 4 3 1 3 1 6 1 9 1 1 1 1 0 1l-5-3v-10z" class="L"></path><path d="M298 408c4 3 7 6 8 11 1 1 2 2 2 4v6 1h-1c-4-1-20-2-24 0l-1 1h-4l-1 1c0-4 2-3 5-6l6-3c3-3 6-7 7-11 1-2 1-3 3-4z" class="J"></path><path d="M306 419c1 1 2 2 2 4v6 1h-1c-4-1-20-2-24 0l-1 1h-4c0-1 0-1 2-2 5-3 12-3 18-3 3 0 5 1 8 0 1-2 1-5 0-7z" class="L"></path><defs><linearGradient id="AT" x1="322.478" y1="431.292" x2="299.599" y2="401.761" xlink:href="#B"><stop offset="0" stop-color="#2a2a2b"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#AT)" d="M295 406v-1l3-3c6 3 12 7 17 11 8 7 13 16 17 26l-1 1c2 2 2 6 2 9l-1 1h-1l-1 1h-1v-1l-1 2c-1 2-2 3-2 5-1 2-2 3-4 4-1 1-2 2-3 2l1-7 1-1v-4-3c0-1 1-4 1-6-1-1-1-2-2-2h0l-1-1c-1-1-2-2-3-2l-1-2c1 0 1 0 0-1 0-3 0-6-1-9-1-1-2-3-4-3v-3l-1-1v-1c0-1 0-3-1-4h-1v1 1c1 2 1 5 1 8 0-2-1-3-2-4-1-5-4-8-8-11l-3-2z"></path><path d="M319 427c1-2 0-3 1-5v3 1c1 2 1 6 3 7v1c-1 1-2 2-2 3l-1-1c-1-3-1-6-1-9z" class="P"></path><path d="M310 419v-1c2 0 2 1 3 1l2 3h2c1 1 1 3 2 5 0 3 0 6 1 9l1 1-1 3h0l-1-1c-1-1-2-2-3-2l-1-2c1 0 1 0 0-1 0-3 0-6-1-9-1-1-2-3-4-3v-3z" class="Y"></path><path d="M310 419v-1c2 0 2 1 3 1 1 3 3 7 4 10v7l-1 1-1-2c1 0 1 0 0-1 0-3 0-6-1-9-1-1-2-3-4-3v-3z" class="b"></path><path d="M323 433c0-2 1-2 2-4l4 8c0 2 1 2 2 3h0c2 2 2 6 2 9l-1 1h-1c0-1-1-1-2-2h-1l-5-14v-1z" class="V"></path><path d="M321 437c0-1 1-2 2-3l5 14h1c1 1 2 1 2 2l-1 1h-1v-1l-1 2c-1 2-2 3-2 5-1 2-2 3-4 4-1 1-2 2-3 2l1-7 1-1v-4-3c0-1 1-4 1-6-1-1-1-2-2-2l1-3z" class="C"></path><path d="M322 461v-1l2-4 1 1h1c-1 2-2 3-4 4z" class="O"></path><path d="M321 437c0-1 1-2 2-3l5 14h1c1 1 2 1 2 2l-1 1h-1v-1l-1 2c-1 2-2 3-2 5h-1l-1-1c1-5 1-13-1-18-1-1-1-1-2-1z" class="H"></path><defs><linearGradient id="AU" x1="353.645" y1="632.795" x2="399.587" y2="573.61" xlink:href="#B"><stop offset="0" stop-color="#d4d3d3"></stop><stop offset="1" stop-color="#f8f8f8"></stop></linearGradient></defs><path fill="url(#AU)" d="M373 507l48 125 7 15 6 17c1 1 1 3 1 5l-5-4c-3-1-3-3-7-3l-7-4-11-7-35-13c-11-4-20-10-30-16-5-3-10-6-15-10v-2c-1 0-1 0-1-1l-4-5v-1l-3-2-2-2c-1-2-3-5-4-7 0-2-2-3-2-5 1-2-1-7-2-9l1-2c-1-1-1-3-1-4-1-2-1-3-1-4l-1-9 2 3v-2l1 2c3-8 8-16 15-21 1-2 2-3 3-3 4-4 8-7 13-9v-1c7-4 13-9 21-10l3 3h0l1 1v-1c1 0 1-1 2-1h0l1-4-1-3c0-1 1-2 1-2 2-2 4-3 6-4z"></path><path d="M320 603c1 1 2 1 3 2s3 4 4 5c-2-1-2-1-3-2h0v1c1 1 0 1 1 1-1 0-1 0-1-1l-4-5v-1z" class="G"></path><path d="M422 656c2 1 5 3 7 4 1 1 3 4 5 4 1 1 1 3 1 5l-5-4h0 2c-1-2-4-4-6-5-2-2-3-2-4-4zm-115-96l1 2c3-8 8-16 15-21-1 3-4 5-6 7-4 5-7 10-9 16 0 1-1 1-1 2l-1 2h0l-1-9 2 3v-2z" class="C"></path><path d="M405 651c2 0 3 0 5 1v1l1 1h1v-1c1 0 2 1 3 1 2 1 5 2 6 3 2 2 3 3 5 4l4 4h0c-3-1-3-3-7-3l-7-4-11-7z" class="B"></path><path d="M306 568h0c1 2 2 4 2 6 0 4 2 7 4 10 0 0 1 1 1 2h1c0-1 0-2-1-3v-2-2-7h1l-1-1c0-1 0-2 1-3v8c0 10 3 17 8 25h0c-2 0-4-3-5-5l-1-1c-1-2-4-6-4-8v1c0 1 1 3 2 4v3h0l-5-8c1-2-1-7-2-9l1-2c-1-1-1-3-1-4-1-2-1-3-1-4z" class="H"></path><path d="M324 595l26 15c17 7 32 15 51 18 3 0 6 3 9 4h0 3 1 0 1l-1 2h1 3c1 1 1 1 3 1-1-1-1-1-1-2v-1h1 0l7 15 6 17c-2 0-4-3-5-4-2-1-5-3-7-4 0-1-8-5-9-6-27-15-62-19-84-45-2-2-4-7-5-10z"></path><path d="M398 637s1-1 2-1c2 0 7 1 9 2l2 1c1 0 3 1 3 1l1 1h0c1 1 1 1 1 2l-18-6z" class="P"></path><defs><linearGradient id="AV" x1="326.015" y1="601.944" x2="344.72" y2="606.615" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#727272"></stop></linearGradient></defs><path fill="url(#AV)" d="M324 595l26 15h-1c-1 0-3-1-4-2v1l-1 2c1 1 2 1 3 2 0 0 1 0 2 1 2 1 6 2 8 4-5-1-9-4-14-6-4-2-9-4-12-7l-1-1-1 1c-2-2-4-7-5-10z"></path><defs><linearGradient id="AW" x1="420.265" y1="643.273" x2="424.418" y2="636.217" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#403e3f"></stop></linearGradient></defs><path fill="url(#AW)" d="M421 632l7 15c-4-1-9-2-12-4 0-1 0-1-1-2h0l-1-1s-2-1-3-1l-2-1c-2-1-7-2-9-2-1 0-2 1-2 1l-2-1c1-1 1-1 2-1h5 1 1c2 1 1 0 2-1 1 0 3 1 4 1l1-1-2-2h3 1 0 1l-1 2h1 3c1 1 1 1 3 1-1-1-1-1-1-2v-1h1 0z"></path><path d="M420 638l3 3v2c-1-1-1 0-2-1-2-1-4-1-7-2 0 0-2-1-3-1h2c2-1 3 0 5 0h1l1-1z" class="X"></path><path d="M412 634c3 1 5 3 8 4l-1 1h-1c-2 0-3-1-5 0h-2l-2-1c-2-1-7-2-9-2-1 0-2 1-2 1l-2-1c1-1 1-1 2-1h5 1 1c2 1 1 0 2-1 1 0 3 1 4 1l1-1z" class="Z"></path><defs><linearGradient id="AX" x1="379.2" y1="617.95" x2="375.248" y2="626.263" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#959595"></stop></linearGradient></defs><path fill="url(#AX)" d="M357 618c-2-2-6-3-8-4-1-1-2-1-2-1-1-1-2-1-3-2l1-2v-1c1 1 3 2 4 2h1c17 7 32 15 51 18 3 0 6 3 9 4h0l2 2-1 1c-1 0-3-1-4-1-1 1 0 2-2 1h-1-1-5c-1 0-1 0-2 1-3-1-6-2-9-4l-30-14z"></path><path d="M387 632c4-1 8 1 11 3h0c-1 0-1 0-2 1-3-1-6-2-9-4z" class="L"></path><path d="M327 577l1-2c2 2 23 13 23 15l53 33 11 5c1 1 4 2 5 4v1c0 1 0 1 1 2-2 0-2 0-3-1h-3-1l1-2h-1 0-1-3 0c-3-1-6-4-9-4-19-3-34-11-51-18l-26-15c-2-6 0-12 3-18z"></path><defs><linearGradient id="AY" x1="329.913" y1="575.744" x2="340.836" y2="588.035" xlink:href="#B"><stop offset="0" stop-color="#3d3d3e"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#AY)" d="M327 577l1-2c2 2 23 13 23 15h-1l-2-1-3-2-2 1 1 1c1 0 1 0 2 1 0 0 1 1 2 1 1 1 2 2 4 3h0l2 2c-4-2-8-5-12-7-4-3-13-7-15-12z"></path><defs><linearGradient id="AZ" x1="388.23" y1="604.291" x2="376.116" y2="617.767" xlink:href="#B"><stop offset="0" stop-color="#484747"></stop><stop offset="1" stop-color="#9c9c9c"></stop></linearGradient></defs><path fill="url(#AZ)" d="M354 596l-2-2h0c-2-1-3-2-4-3-1 0-2-1-2-1-1-1-1-1-2-1l-1-1 2-1 3 2 2 1h1l53 33 11 5c1 1 4 2 5 4v1c0 1 0 1 1 2-2 0-2 0-3-1h-3-1l1-2h-1 0-1-3 0c-3-1-6-4-9-4-3-3-7-5-10-8l-37-24z"></path><path d="M391 620c1 1 2 1 3 2l3 2h1l-1-1c1 0 2 0 3 1s1 1 2 1h0c2 2 4 2 6 4 1 0 2 1 3 2h-1v1c-3-1-6-4-9-4-3-3-7-5-10-8z" class="L"></path><path d="M373 507l48 125h0-1c-1-2-4-3-5-4l-11-5-53-33c0-2-21-13-23-15 3-5 9-9 15-11l4-1c-4-1-8-2-13-3l25-22v-2c-8 5-20 7-29 9 1-2 2-4 3-5 1-4 5-7 6-11v-1c7-4 13-9 21-10l3 3h0l1 1v-1c1 0 1-1 2-1h0l1-4-1-3c0-1 1-2 1-2 2-2 4-3 6-4z"></path><path d="M376 523h1v3c-1 1-3 2-4 3h-2c1-1 1 0 1-1 2-2 2-3 3-5h1z" class="V"></path><path d="M364 533c1 1 0 1 0 3v2l-2 1c-2 0-2 0-3-1v-2c1-1 4-3 5-3z" class="P"></path><path d="M367 516l2 12h0l-5-6v-1c1 0 1-1 2-1h0l1-4z" class="N"></path><path d="M364 533c2-1 4-1 5-2l1-1v2c-1 2-1 5 0 7h-8l2-1v-2c0-2 1-2 0-3z" class="V"></path><defs><linearGradient id="Aa" x1="360.656" y1="520.919" x2="342.453" y2="527.683" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#Aa)" d="M360 518l3 3h0c-2 2-3 2-5 3-9 4-17 10-25 16 1-4 5-7 6-11v-1c7-4 13-9 21-10z"></path><path d="M739 300l4-10h1l1 1c2 2 4 3 7 4v-2c3 1 5 1 7 1h1c4 1 11 3 14 6l3 1c0 3 1 4 1 7l5 12 1 1 5 11 4 10 7 14c1 4 3 7 4 10v1l-1 1c12 20 23 45 17 69-2 4-4 8-5 12-2 5-3 10-4 15h1v1h0c0 1 0 1-1 2 1 1 0 1 1 1-1 1-1 2-1 4 0 1 1 2 2 2l5 8c1 2 1 4 2 6 0 4 0 8-1 11l-1 4v1c-1 4-4 8-6 12-2 3-4 5-6 7s-5 4-7 7c0 1-1 1-2 2-1 3-5 6-7 7l-3 3-5 5v2c-1 11-3 20-7 30l-1 3c-8 20-22 34-38 47-2 1-15 9-15 10l-18 8h-1c-4 1-9 2-13 3-8 2-30 4-34 11v1c2 1 6 1 8 1 4 0 8 1 11 1 5 1 9 3 13 4l4 2c9 8 18 17 25 26l-20-3c3 5 7 10 9 16 2 3 2 7 2 10 1 2 1 5 2 7 1 3 3 5 6 7l2 1c-7 0-13-2-19-5l-4 20h0 0 0c-1 1-1 2-1 2-1 3 0 7 0 10 0 0 1 1 1 2v3h1c0 1 0 2 1 2-1 0-3-1-3-2l-1-1-1-1c-2-2-3-5-4-7v-4c0 1 0 2-1 3v-1l-4 8c-5 9-11 20-22 22-6 2-13 1-17-2-7-4-9-10-10-17l4 8c3 4 8 7 13 8 4 0 9-1 13-3 7-6 10-18 12-26l-5-2 3-11c-6 2-10 4-16 8 5-9 9-18 14-26 1 0 2-1 3-1l1-1c1 0 2-1 3-1l1-1 1-1c1 0 1-1 2-1 1-1 1-2 2-3l1-1v-1c2-3 2-5 2-8-1-8-2-18-9-24l-13-5c-11 0-21 1-32 4l-5 1c-6 2-12 3-18 6-2-2-3-2-6-2-1 1-1 1-2 1v-1c1-1 1-1 2-1 1-1 3-2 4-2l1-1h1l1-1v-1c-2 1-3 2-4 2h-1l-1 1c-1 0-1-1-2-1l-1-1 1-1-2-1-7 6c0-2 0-2 1-4h-2v-1l3-9-1-1 9-23 5-13 36-92c1-2 1-3 2-4l10-27 4-9 1-3 2-4 4-13 2-4 7-18c1 0 1 0 2 1 2 1 4 1 6 3l8 4-1-7 2 4c0 2 0 3 2 5 1-4 1-9 1-13-4-6-2-14-3-21 2-6 2-12 4-18 3-2 4-4 6-6 0-2 2-3 3-4 2-3 4-9 5-13l-2-1 6-4-5-4v-1c-2-1-3-1-4-2l14-34v1l1-2v1h4l2 2h1v-1c-1 0-1 0-1-1s-1-1-1-3h1c1 1 1 1 2 1 2 1 2 1 4 0-3-4-3-11-7-14l12-30z" class="Q"></path><path d="M756 586c-3 3-6 7-10 10v-1c1-1 3-2 3-3-1-1-1 0-2 0l1-2 1-1c1-2 2-3 3-4l1 1h3z" class="B"></path><path d="M692 716h0c1 4 0 9-1 13 0 3 0 7-2 10v-1-3c1-5 1-9 1-13v-1c0 1 0 2 1 2v-2c1-1 1-3 1-5z" class="C"></path><path d="M632 654c1 1 1 1 3 1 5-1 10-5 15-5v1c-3 1-8 2-10 3h2c1 0 2-1 3-1h3c1-1 4-2 6-2-2 2-5 2-8 3h-1c-1 0-1 0-2 1-3 1-4 0-6 2-7 3-13 4-20 7-5 3-9 6-14 9l13-11c5-3 10-6 16-8z" class="G"></path><path d="M765 533c2 10 4 21 2 32h-1c-1 8-5 15-10 21h-3l-1-1c4-4 7-8 9-14 3-5 3-12 4-18 0-1 0-1 1-2 0-2 1-7 0-9v-2-1c-1-2-1-4-1-6z" class="E"></path><path d="M678 679h1c3 2 6 4 8 7 4 4 5 8 7 13 2 6 5 11 6 17h0l-4-9s-1-1-1-2v-1h-1c0-1-1-2-1-3-2 0-3 1-4 2s-1 4-1 5l1 1v2l1 1-3 6c1-2 2-6 1-9h-1 0c-1 2-2 3-3 4l1-1v-1c2-3 2-5 2-8-1-8-2-18-9-24z" class="H"></path><path d="M743 583l2-1v2 1c-2 2-2 4-4 6-5 4-8 11-13 16v-1l1-2c1 0 1-1 2-2h-1c-1 2-3 4-4 6-2 2-5 4-6 7h6v-1h-1 0 1 2 0 2c1-1 2-1 3-1h0 1c1-1 1-1 2-1l2-1h0l2-1c3-1 6-4 10-5 0-1 1-2 2-2l7-6v1c-6 6-13 10-21 15-1 0-2 0-2 1h-1c-3 1-5 1-8 2s-7-1-10 1h1c2 1 0 0 1 1h5 3l1 1c3 0 6 0 9-1 1 0 1 0 2 1-2 0-3 1-4 1h-8c-2-1-5 0-6 0l-2-1h-1c-2 0-3-1-5-2 5-3 9-6 13-11 1-2 3-5 5-7l4-6 3-10h2l1 1 2-1z" class="O"></path><path d="M738 583h2l1 1 2-1-3 6h0c-2 4-5 8-8 11l-1-1 4-6 3-10z" class="E"></path><path d="M738 583h2l1 1c-2 3-4 7-6 9l3-10zm-27-130c0 1 0 3 1 5h9c-1-3-2-8-1-12 2-3 7-8 10-9-2 3-4 5-5 9-1 2-1 6-1 8l1 1c0 1 0 1 1 2v1c0 2 2 4 3 6 3 6 5 12 9 18 0 1 0 2 1 3v1c1 1 2 3 3 4s1 3 2 4 1 2 2 3h-1l-5-7-16-31c-4 0-9 2-12 0h0c0 2 1 5 2 7l-1 1v1c-3-1-7-4-10-7v-1c1-1 2-1 4 0v-1-2l1-1h0c0-2 0-3-1-5-1 1-2 1-2 3l-1-2v-1l5-12 2 2 2 3c0 1 0 2-1 3v1c0 2 0 3-1 5z" class="B"></path><path d="M711 441l2 3c0 1 0 2-1 3v1c0 2 0 3-1 5-1-4-2-8 0-12z" class="C"></path><path d="M707 459v-2l1-1 3 6 2 5v1c-3-1-7-4-10-7v-1c1-1 2-1 4 0v-1z" class="H"></path><path d="M707 459v-2l1-1 3 6c-1 0-1 0-1-1-2 0-2-2-2-3l-1 1z" class="G"></path><path d="M738 482c1 2 3 6 5 7l-2-3-2-8v-1c0-1-1-3-1-4-1-1-1-1-1-3v1c2 5 4 7 7 12 2 3 4 6 5 9 1 1 2 3 3 5 7 11 11 23 13 36 0 2 0 4 1 6v1 2c1 2 0 7 0 9-1 1-1 1-1 2 0-7-2-12-3-19-1-3-2-7-3-11-1 9-4 18-7 27l-3 9h-1l1-2v-4c2-11 7-20 7-31 0-2 0-4-1-5l-10-20h1c-1-1-1-2-2-3s-1-3-2-4-2-3-3-4v-1c-1-1-1-2-1-3z" class="F"></path><path d="M738 482c1 2 3 6 5 7l-2-3-2-8v-1c0-1-1-3-1-4-1-1-1-1-1-3v1c2 5 4 7 7 12 2 3 4 6 5 9 1 1 2 3 3 5-1 3 3 8 5 11 2 6 4 12 5 18l-6-10c0-1 0-2-1-3l-9-16c-1-1-1-2-2-3s-1-3-2-4-2-3-3-4v-1c-1-1-1-2-1-3z" class="G"></path><defs><linearGradient id="Ab" x1="607.553" y1="670.352" x2="689.027" y2="686.798" xlink:href="#B"><stop offset="0" stop-color="#838283"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#Ab)" d="M646 669c8-1 16-2 23 0h1c10 2 18 7 25 13-5-1-10-3-15-5-1 1-1 1-1 2h-1l-13-5c-11 0-21 1-32 4l-5 1c-6 2-12 3-18 6-2-2-3-2-6-2-1 1-1 1-2 1v-1c1-1 1-1 2-1 3 0 6-2 8-3 1 0 1 0 2-1 0 0 1 0 2-1h0 2l5-2 23-6z"></path><path d="M665 674c5-2 11 1 15 3h0c-1 1-1 1-1 2h-1l-13-5z" class="D"></path><defs><linearGradient id="Ac" x1="600.887" y1="667.845" x2="650.66" y2="667.194" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#Ac)" d="M637 657l15-3h2c0-1 1-1 2-1s2-1 2-1h1 1 1c-2 1-8 2-10 3-1 1 0 2-2 2l-2 2-2 1c-2 1-4 2-6 4h-1l-3 3c-2 1-4 3-6 5 3 0 5-1 8-2 1 0 2 0 3-1h2 1 1c1-1 1 0 2 0l-23 6-5 2h-2 0c-1 1-2 1-2 1-1 1-1 1-2 1-2 1-5 3-8 3 1-1 3-2 4-2l1-1h1l1-1v-1c-2 1-3 2-4 2h-1l-1 1c-1 0-1-1-2-1l-1-1 1-1-2-1-7 6c0-2 0-2 1-4 3 0 5-5 8-5 5-3 9-6 14-9 7-3 13-4 20-7z"></path><defs><linearGradient id="Ad" x1="704.103" y1="425.25" x2="719.934" y2="429.26" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#Ad)" d="M732 401h1v3c-1 3-4 6-5 8-2 4-3 11-3 16l5-1 1-1h11c1 1 1 0 2 0s1 1 3 0c2 1 7 3 8 5v1h0l-5-1c-11-1-24-1-33 7-2 1-3 3-4 6l-2-3-2-2-5 12v1l-1-1c-1-1 0-1-1-1-2-3-2-4-3-7l2-9c5-10 14-20 23-27l8-6z"></path><path d="M703 451c1-6 1-12 4-17h1c0 2 1 3 1 5l-5 12v1l-1-1z" class="N"></path><defs><linearGradient id="Ae" x1="717.504" y1="419.447" x2="745.755" y2="433.108" xlink:href="#B"><stop offset="0" stop-color="#403f40"></stop><stop offset="1" stop-color="#6c6a6b"></stop></linearGradient></defs><path fill="url(#Ae)" d="M732 401h1v3c-1 3-4 6-5 8-2 4-3 11-3 16l5-1 1-1h11c1 1 1 0 2 0s1 1 3 0c2 1 7 3 8 5v1h0l-5-1c-11-1-24-1-33 7-2 1-3 3-4 6l-2-3-2-2c0-2-1-3-1-5v-2l1 1v3-2c0-1 0-2 1-2l-1-1v-3-1c1-1 1-1 1-2l2 9v-1h1v3h2c2-1 5-3 6-5 1-3 1-9 2-13 0-2 1-5 1-8v-3l8-6z"></path><path d="M712 434v-1h1v3h2c0 1-1 1-2 2-2-2-1-3-1-4z" class="Y"></path><path d="M730 427l1-1h11c1 1 1 0 2 0s1 1 3 0c2 1 7 3 8 5v1h0l-5-1 1-1c-1 0-3-1-4-2h-1c-5-2-11-1-16-1z" class="T"></path><path d="M716 387l3-1c1 0 1 0 2 1 1 4-4 12-5 15 3-1 5-2 8-3h6l2 1v1l-8 6c-9 7-18 17-23 27l-2 9c1 3 1 4 3 7 1 0 0 0 1 1l1 1 1 2c0-2 1-2 2-3 1 2 1 3 1 5h0l-1 1v2 1c-2-1-3-1-4 0v1c-3-1-4-4-6-7-4-6-2-14-3-21 2-6 2-12 4-18 3-2 4-4 6-6 0-2 2-3 3-4 2-3 4-9 5-13l-2-1 6-4z" class="c"></path><defs><linearGradient id="Af" x1="690.519" y1="421.474" x2="713.084" y2="453.511" xlink:href="#B"><stop offset="0" stop-color="#b3b2b3"></stop><stop offset="1" stop-color="#dfdedd"></stop></linearGradient></defs><path fill="url(#Af)" d="M698 415c3-2 4-4 6-6 0-2 2-3 3-4-2 5-4 9-4 14 1-2 3-7 5-8 0 1-1 2-1 3l-4 8-3 9v1c0 1 0 2-1 3 0 2-1 5-1 7 1-1 1-1 1-2s0-1 1-2v-1c0-1 0-2 1-3h0l-2 9c1 3 1 4 3 7 1 0 0 0 1 1l1 1 1 2c0-2 1-2 2-3 1 2 1 3 1 5h0l-1 1v2 1c-2-1-3-1-4 0v1c-3-1-4-4-6-7-4-6-2-14-3-21 2-6 2-12 4-18z"></path><path d="M708 456l-1-2-1 1c-2 0-2-1-3-3h-1 0l-1 1-2-3v-1-6c1 3 1 4 3 7 1 0 0 0 1 1l1 1 1 2c0-2 1-2 2-3 1 2 1 3 1 5z" class="D"></path><path d="M741 443c3-3 5-5 9-6 3-1 6-1 9 1h1c-3 5-4 9-5 14-1 1-1 2-1 4 0 1-1 3-1 5l-1 4c-1 0-1 0-1-1-3-3-6-6-8-10 2 10 6 20 15 25 5 2 11 4 17 3 3-1 8-6 11-4 1 2 0 3 0 5l-1 1c2 1 3 2 5 2 3-3 5-6 8-8 1 0 8 8 10 10 1 2 2 4 2 7 2-1 3-3 4-4 0 2 1 4 2 5l3 3-1 4v1c-1 4-4 8-6 12-2 3-4 5-6 7s-5 4-7 7c0 1-1 1-2 2-1 3-5 6-7 7l-3 3-5 5 1-9 2-23c0-2 1-7 0-9 0-1 0-1-1-1s-2 0-3 1c-4 2-6 14-6 19-2-5-4-10-7-15-10-17-23-31-29-51-1-6-1-10 2-16z"></path><path d="M801 499v-1c2-3 2-7 2-11 0 2 0 5 1 6 0 3 0 6-1 8l-2-2z" class="V"></path><path d="M801 499l2 2c0 4 2 8 3 11-1 3-1 4-2 7v1c-2 0-3-2-4-4 1-6-1-12 1-17z" class="M"></path><path d="M785 484c2 1 3 2 5 2l-14 20c-2 2-3 6-5 7-1-1-2-2-2-3v-1c0-2 3-5 4-6l12-19z" class="I"></path><defs><linearGradient id="Ag" x1="744.661" y1="439.839" x2="756.822" y2="442.793" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#Ag)" d="M741 443c3-3 5-5 9-6 3-1 6-1 9 1h1c-3 5-4 9-5 14-1 1-1 2-1 4 0 1-1 3-1 5h0c-3-2-3-3-4-5 0-3-1-8-3-10-1-2-3-2-5-3z"></path><path d="M749 456v-1l1-1c0-2 0-3 1-4 1-2 1-4 2-5s1-2 2-2h1l-1 4c-1 2-1 3 0 5-1 1-1 2-1 4 0 1-1 3-1 5h0c-3-2-3-3-4-5z" class="S"></path><defs><linearGradient id="Ah" x1="793.294" y1="540.032" x2="791.638" y2="523.277" xlink:href="#B"><stop offset="0" stop-color="#545255"></stop><stop offset="1" stop-color="#8d8c8b"></stop></linearGradient></defs><path fill="url(#Ah)" d="M810 495c2-1 3-3 4-4 0 2 1 4 2 5l3 3-1 4v1c-1 4-4 8-6 12-2 3-4 5-6 7s-5 4-7 7c0 1-1 1-2 2-1 3-5 6-7 7l-3 3-5 5 1-9c7-5 12-12 16-19 0-1 1-2 1-3 1 2 2 4 4 4v-1c1-3 1-4 2-7v3c3-7 4-12 4-20z"></path><path d="M799 519c0-1 1-2 1-3 1 2 2 4 4 4-1 2-2 2-3 3-2 0-1-1-1-2s-1-2-1-2z" class="J"></path><path d="M810 495c2-1 3-3 4-4 0 2 1 4 2 5l3 3-1 4v1c-1 4-4 8-6 12h-1l-8 8c1-2 3-5 3-8v-1c3-7 4-12 4-20z" class="B"></path><path d="M816 496l3 3-1 4v1h-3c1-3 1-5 1-8z" class="P"></path><path d="M811 516c2-4 3-8 4-12h3c-1 4-4 8-6 12h-1z" class="Y"></path><path d="M739 300l4-10h1l1 1c2 2 4 3 7 4v-2c3 1 5 1 7 1h1c4 1 11 3 14 6l3 1c0 3 1 4 1 7l5 12 1 1 5 11 4 10 7 14c1 4 3 7 4 10v1l-1 1c12 20 23 45 17 69-2 4-4 8-5 12-2 5-3 10-4 15h1v1h0c0 1 0 1-1 2 1 1 0 1 1 1-1 1-1 2-1 4 0 1 1 2 2 2l5 8c1 2 1 4 2 6 0 4 0 8-1 11l-3-3c-1-1-2-3-2-5-1 1-2 3-4 4 0-3-1-5-2-7-2-2-9-10-10-10-3 2-5 5-8 8-2 0-3-1-5-2l1-1c0-2 1-3 0-5-3-2-8 3-11 4-6 1-12-1-17-3-9-5-13-15-15-25 2 4 5 7 8 10 0 1 0 1 1 1l1-4c0-2 1-4 1-5 0-2 0-3 1-4 1-5 2-9 5-14h-1l1-2-1-1c-1-1-3-2-4-3h0v-1c-1-2-6-4-8-5-2 1-2 0-3 0s-1 1-2 0h-11l-1 1-5 1c0-5 1-12 3-16 1-2 4-5 5-8v-3h-1v-1l-2-1h-6c-3 1-5 2-8 3 1-3 6-11 5-15-1-1-1-1-2-1l-3 1-5-4v-1c-2-1-3-1-4-2l14-34v1l1-2v1h4l2 2h1v-1c-1 0-1 0-1-1s-1-1-1-3h1c1 1 1 1 2 1 2 1 2 1 4 0-3-4-3-11-7-14l12-30z"></path><path d="M761 431c1 2 2 3 2 6h0c-1 0-2 0-3-1l-1-1 2-4zm24 5c3 5 4 12 3 18 0 1-1 2-2 3 0-3 1-5 1-8l-1-1c0-3-2-6-3-10 2 2 3 4 4 7v-1l-1-1v-2c-1-2-1-3-1-5z" class="B"></path><path d="M758 461c2 0 4-1 6-1v1c0 2-1 3-2 4 2 2 3 2 5 2 0 0 1 0 2-1h6l1 1h-6l-2 1c-1 0-1 1-2 1 0 1-1 1-2 1-2-1-2-2-3-4l-2 1-1-1c0-2 0-2 1-4h-1v-1z" class="S"></path><path d="M759 462c1 1 2 2 2 3v1l-2 1-1-1c0-2 0-2 1-4z" class="L"></path><path d="M806 420l-4-16c1 1 2 4 3 5l3-1c0 1 1 3 0 4l1 7v1l-1 1c0 2 0 2-2 4v-5z" class="B"></path><path d="M806 420v-1l1-1v-4l1-2 1 7v1l-1 1c0 2 0 2-2 4v-5z" class="C"></path><path d="M764 460c3-1 6-1 9-1 2 3 4 4 4 8h0 0-1l-1-1h-6c-1 1-2 1-2 1-2 0-3 0-5-2 1-1 2-2 2-4v-1z" class="H"></path><path d="M760 436c1 1 2 1 3 1v4c0 2 0 5-1 7-1 3-3 7-5 11v1c-1-2-2-3-3-4 0-2 0-3 1-4 1-5 2-9 5-14h-1l1-2z" class="T"></path><path d="M760 436c1 1 2 1 3 1v4h0l-3-3h-1l1-2z" class="C"></path><defs><linearGradient id="Ai" x1="737.318" y1="399.16" x2="795.682" y2="421.34" xlink:href="#B"><stop offset="0" stop-color="#b5b4b4"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#Ai)" d="M747 379v-3h2c2 3 4 5 6 8 1 1 2 3 3 4 0 0 1 0 1 1l1 1c3 3 3 5 6 7 2 3 2 6 3 10l-1 1c2 4 3 9 6 13 3 5 9 10 11 15 0 2 0 3 1 5v2l1 1v1c-1-3-2-5-4-7l-4-4c-4-3-20-17-23-17-1 1 0 4 0 6h-1c-2-1-4-8-5-10v-1l2 3c0-2 0-3 1-5 1 0 3-1 5-2 3 4 6 9 10 13h0v-2c0-4-5-7-5-11 1 1 0 1 1 1-2-5-5-10-7-14s-4-7-7-11c0-1-1-2-2-3l-1-2z"></path><path d="M758 388s1 0 1 1l1 1c3 3 3 5 6 7 2 3 2 6 3 10l-1 1-10-20z" class="Z"></path><defs><linearGradient id="Aj" x1="728.075" y1="389.605" x2="765.056" y2="405.3" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#c0bebe"></stop></linearGradient></defs><path fill="url(#Aj)" d="M746 406c-1-2-2-3-2-4v-1l-1-2c1-1 3-1 4-1h2c-4-3-8-6-11-10l-1-1-3-5c-1-1-2-3-1-4 3-1 7 0 10 2l1 1c1 0 2 1 3 2l1-2c1 1 2 2 2 3 3 4 5 7 7 11s5 9 7 14c-1 0 0 0-1-1 0 4 5 7 5 11v2h0c-4-4-7-9-10-13-2 1-4 2-5 2-1 2-1 3-1 5l-2-3v1c-1 0-2-3-2-4l-1-1c0-1 0-1-1-2z"></path><path d="M748 381c1 1 2 2 2 3v1l-3-2 1-2z" class="R"></path><path d="M758 408h0l2 2c1 1 1 2 2 3 0-1-1-2-1-3-1-1-1-2-1-3h0l8 12v2h0c-4-4-7-9-10-13z" class="M"></path><defs><linearGradient id="Ak" x1="750.681" y1="386.37" x2="771.759" y2="381.859" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#Ak)" d="M728 346c0-1-1-1-1-3h1c1 1 1 1 2 1 2 1 2 1 4 0l10 10c1 3 3 5 5 7l2-2 2-1 1 1 2 2c0 2 0 7 2 8v1l3 1h0l8 4 8 7 1 1-2 1c0 1-2 2-2 2-2 0-3 1-3 2 1 4 3 7 6 10-1 1-3 1-3 2 0 2 1 4 0 5-2 1-3 2-5 2-1-4-1-7-3-10-3-2-3-4-6-7l-1-1c0-1-1-1-1-1-1-1-2-3-3-4-2-3-4-5-6-8l-6-7c-3-4-7-9-10-11-1-1-1-1-2-1 0-2-2-3-2-5-2-1-3-4-3-6l2 2h1v-1c-1 0-1 0-1-1z"></path><path d="M753 358l1 1 2 2c0 2 0 7 2 8v1l3 1h0l8 4 8 7 1 1-2 1c0 1-2 2-2 2v-1c-2-2-4-2-6-4-4-2-8-4-11-7l-4-4-4-5-1-2c1-1 1-1 1-2l2-2 2-1z" class="Z"></path><path d="M749 361l2-2 1 3-2 1-1 1v1l-1-2c1-1 1-1 1-2z" class="b"></path><path d="M753 358l1 1c0 1 0 3-1 4l-1-1-1-3 2-1z" class="a"></path><path d="M761 371l8 4 8 7 1 1-2 1-4-3h-1l-1-1c-2-3-5-3-7-5-2-1-2-2-2-4z" class="Y"></path><defs><linearGradient id="Al" x1="744.915" y1="351.022" x2="734.915" y2="368.524" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#Al)" d="M728 346c0-1-1-1-1-3h1c1 1 1 1 2 1 2 1 2 1 4 0l10 10c1 3 3 5 5 7 0 1 0 1-1 2l1 2 4 5-1-1c-2-1-3-3-5-4v-1l-1 1h1c0 1 1 2 1 3v1c2 1 3 3 3 5 5 8 12 14 15 23-3-2-3-4-6-7l-1-1c0-1-1-1-1-1-1-1-2-3-3-4-2-3-4-5-6-8l-6-7c-3-4-7-9-10-11-1-1-1-1-2-1 0-2-2-3-2-5-2-1-3-4-3-6l2 2h1v-1c-1 0-1 0-1-1z"></path><path d="M728 346c2 2 3 6 4 8l-1 1v-1c-1-1-2-1-2-2-2-1-3-4-3-6l2 2h1v-1c-1 0-1 0-1-1z" class="a"></path><defs><linearGradient id="Am" x1="733.415" y1="356.024" x2="746.982" y2="358.953" xlink:href="#B"><stop offset="0" stop-color="#514f50"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#Am)" d="M751 374c-1-1-2-3-3-4l-9-11c-2-1-3-3-4-4-1-2-2-4-2-7 3 2 5 5 7 7l8 8 1 2 4 5-1-1c-2-1-3-3-5-4v-1l-1 1h1c0 1 1 2 1 3v1c2 1 3 3 3 5z"></path><defs><linearGradient id="An" x1="800.448" y1="352.999" x2="772.052" y2="404.001" xlink:href="#B"><stop offset="0" stop-color="#cecdcd"></stop><stop offset="1" stop-color="#f2f1f2"></stop></linearGradient></defs><path fill="url(#An)" d="M779 329c3 5 5 10 6 16l4 10c2 3 3 6 5 9 3 4 7 8 9 13h-1l5 12h-1c0 1 0 2-1 3 0-1-1-2-1-3-2-1-2-1-3-1-2 3-1 10-1 14 0 9-3 16-10 22 2-3 4-6 4-10 1-9-5-15-10-21-2-3-3-7-6-10h0l-1-1-8-7 1-2v1c1 0 3 1 4 1 0-1-1-2-1-3v-1-6l5-28c-1 0 0-1 0-2l1 1h0v-1h-1c0-2 0-5 1-6z"></path><path d="M773 371c1 1 1 0 1 1s1 1 1 2c1 1 1 3 1 4 1 2 1 3 1 4l-8-7 1-2v1c1 0 3 1 4 1 0-1-1-2-1-3v-1z" class="D"></path><path d="M792 371h0c1 1 2 2 2 3 1 2 2 3 3 5l-1 1c0 5 0 10 1 15 0 2 1 2 0 4v1c-2-6-3-13-4-20-1-3 0-6-1-9z" class="C"></path><path d="M783 354h1l-1-2c0-1 0-6 1-7v2 1h1v-1-2l4 10h0l-3-5 1 31v-2l-4-25z" class="E"></path><path d="M779 329c3 5 5 10 6 16v2 1h-1v-1-2c-1 1-1 6-1 7l1 2h-1c-1-5-2-12-5-17-1 0 0-1 0-2l1 1h0v-1h-1c0-2 0-5 1-6z" class="J"></path><defs><linearGradient id="Ao" x1="707.806" y1="379.555" x2="757.362" y2="396.905" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#a7a7a6"></stop></linearGradient></defs><path fill="url(#Ao)" d="M721 346v1l1-2v1h4c0 2 1 5 3 6 0 2 2 3 2 5 1 0 1 0 2 1 3 2 7 7 10 11l6 7h-2v3l1 2-1 2c-1-1-2-2-3-2l-1-1c-3-2-7-3-10-2-1 1 0 3 1 4l3 5 1 1c3 4 7 7 11 10h-2c-1 0-3 0-4 1l1 2v1c0 1 1 2 2 4 1 1 1 1 1 2l1 1c0 1 1 4 2 4 1 2 3 9 5 10h1l1 3c0 2 2 4 4 5l-2 4c-1-1-3-2-4-3h0v-1c-1-2-6-4-8-5-2 1-2 0-3 0s-1 1-2 0h-11l-1 1-5 1c0-5 1-12 3-16 1-2 4-5 5-8v-3h-1v-1l-2-1h-6c-3 1-5 2-8 3 1-3 6-11 5-15-1-1-1-1-2-1l-3 1-5-4v-1c-2-1-3-1-4-2l14-34z"></path><path d="M724 399v-1h-3v-1c2-1 2-2 4-2v1 1c1 1 3 0 4 1l1 1h-6z" class="S"></path><path d="M743 369l6 7h-2v3c-2-2-4-6-5-9l1-1z" class="I"></path><path d="M747 426c-1 0-1 0-1-1-1 0-1-1-2-1l-1-1c-5-4-6-7-6-13l1-2v-1l1 3h0c0-1 1-2 1-3s-2-2-1-4c0-1 1-3 2-3l1 1c0 1 0 1 1 1l1 2c0 1 0 1 1 1l1 1c1 1 1 1 1 2l1 1c0 1 1 4 2 4 1 2 3 9 5 10h1l1 3c0 2 2 4 4 5l-2 4c-1-1-3-2-4-3h0v-1c-1-2-6-4-8-5z" class="F"></path><defs><linearGradient id="Ap" x1="741.708" y1="316.545" x2="783.581" y2="350.248" xlink:href="#B"><stop offset="0" stop-color="#5e5d5d"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#Ap)" d="M739 300l4-10h1l1 1c2 2 4 3 7 4 1 0 2 1 4 2h0l-1 1c5 4 9 9 13 13 4 6 9 12 11 18-1 1-1 4-1 6h1v1h0l-1-1c0 1-1 2 0 2l-5 28v6 1c0 1 1 2 1 3-1 0-3-1-4-1v-1l-1 2-8-4h0l-3-1v-1c-2-1-2-6-2-8l-2-2-1-1-2 1-2 2c-2-2-4-4-5-7l-10-10c-3-4-3-11-7-14l12-30z"></path><path d="M754 339c-1-6 1-13 3-18v6l-1 5v-3c-1 2-1 2-1 3 0 2 0 5-1 7z" class="T"></path><path d="M751 316l1 3c1 2 0 5 0 8 0 2 0 5-1 7 0-1 0-2-1-3 1-4 0-7 0-11 1-1 1-3 1-4z" class="Z"></path><path d="M757 321c1-3 2-5 5-7l1 1h0v6-3h-1c-1 0-1 0-1 1-2 3-3 5-4 8v-6z" class="I"></path><path d="M771 361c0 2-1 5 0 7v-1-2l1 1v-1h1v6 1c0 1 1 2 1 3-1 0-3-1-4-1v-1c-1-3 0-8 1-12z" class="E"></path><path d="M752 327h1v6 6 15h-1l-2-23c1 1 1 2 1 3 1-2 1-5 1-7z" class="S"></path><path d="M754 339c1-2 1-5 1-7 0-1 0-1 1-3v3c1 6 0 11 0 17v12l-2-2-1-1h1v-1h-1c-1-1-1-1-1-3h1v-15h1z" class="J"></path><defs><linearGradient id="Aq" x1="740.684" y1="302.915" x2="759.047" y2="299.24" xlink:href="#B"><stop offset="0" stop-color="#363637"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#Aq)" d="M739 300l4-10h1l1 1c2 2 4 3 7 4 1 0 2 1 4 2h0l-1 1-3-1v1l9 9v1c-3-2-7-3-10-4l-1 4h0c0-2-1-3-1-4-3-2-6-3-10-4z"></path><path d="M744 290l1 1c2 2 4 3 7 4 1 0 2 1 4 2h0l-1 1-3-1c-3-2-6-2-8-4v-3z" class="Z"></path><defs><linearGradient id="Ar" x1="773.911" y1="323.764" x2="750.794" y2="351.49" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#acacab"></stop></linearGradient></defs><path fill="url(#Ar)" d="M763 315c3 1 6 4 7 8 0 1-1 2 0 4l1-1v-1h1c0 1 1 1 1 2l1 5c1 2 0 5 0 7-1 7-3 15-3 22-1 4-2 9-1 12l-1 2-8-4h0l-3-1v-1c-2-1-2-6-2-8v-12c0-6 1-11 0-17l1-5c1-3 2-5 4-8 0-1 0-1 1-1h1v3-6h0z"></path><defs><linearGradient id="As" x1="748.346" y1="330.311" x2="770.654" y2="357.689" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#As)" d="M761 319l1 1v26l-2 12c0 2 0 5-1 7v3s0 1-1 2v-1c-2-1-2-6-2-8v-12c0-6 1-11 0-17l1-5c1-3 2-5 4-8z"></path><path d="M761 371c-1-6 1-12 3-17l3-15 3-10c0-1 1-2 2-2h1l1 5c1 2 0 5 0 7-1 7-3 15-3 22-1 4-2 9-1 12l-1 2-8-4h0z" class="C"></path><defs><linearGradient id="At" x1="730.895" y1="321.5" x2="748.876" y2="328.125" xlink:href="#B"><stop offset="0" stop-color="#454444"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#At)" d="M727 330l12-30c4 1 7 2 10 4 0 1 1 2 1 4h0l1 8c0 1 0 3-1 4 0 4 1 7 0 11l2 23c0 2 0 2 1 3h1v1h-1l-2 1-2 2c-2-2-4-4-5-7l-10-10c-3-4-3-11-7-14z"></path><path d="M750 308h0l1 8c0 1 0 3-1 4v-1c0-4-1-8 0-11z" class="Y"></path><defs><linearGradient id="Au" x1="740.447" y1="335.71" x2="757.406" y2="342.729" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#Au)" d="M744 354v-3c-2-6-2-16-1-22h0c1-2 1-4 2-6 2-2 3-4 5-4v1c0 4 1 7 0 11l2 23c0 2 0 2 1 3h1v1h-1l-2 1-2 2c-2-2-4-4-5-7z"></path><path d="M752 295v-2c3 1 5 1 7 1h1c4 1 11 3 14 6l3 1c0 3 1 4 1 7l5 12 1 1 5 11 4 10 7 14c1 4 3 7 4 10v1l-1 1c12 20 23 45 17 69-2 4-4 8-5 12-2 5-3 10-4 15h1v1h0c0 1 0 1-1 2 1 1 0 1 1 1-1 1-1 2-1 4 0 1 1 2 2 2l5 8c1 2 1 4 2 6 0 4 0 8-1 11l-3-3c-1-1-2-3-2-5-1 1-2 3-4 4 0-3-1-5-2-7-2-2-9-10-10-10-3 2-5 5-8 8-2 0-3-1-5-2l1-1c0-2 1-3 0-5-3-2-8 3-11 4-6 1-12-1-17-3-9-5-13-15-15-25 2 4 5 7 8 10 0 1 0 1 1 1l1-4c0-2 1-4 1-5 1 1 2 2 3 4v-1l1 2v1h1c-1 2-1 2-1 4l1 1-2 1 2 3c2 3 5 5 8 5 10 2 20-10 26-16 3-2 6-4 9-7-5 2-10 3-15 5l9-9c5-7 9-15 10-24 2-2 2-2 2-4l1-1v-1l-1-7c1-1 0-3 0-4l1-3c-1-4-2-9-4-13 1-1 1-2 1-3h1l-5-12h1c-2-5-6-9-9-13-2-3-3-6-5-9l-4-10c-1-6-3-11-6-16-2-6-7-12-11-18-4-4-8-9-13-13l1-1h0c-2-1-3-2-4-2z" class="W"></path><path d="M757 459l1 2v1h1c-1 2-1 2-1 4l1 1-2 1c0-3-1-5 0-8v-1z" class="b"></path><path d="M811 484l3 7c-1 1-2 3-4 4 0-3-1-5-2-7h1c1 2 1 2 2 3v-3-4z" class="D"></path><path d="M806 441c1 0 0 0 1 1 1 3 1 6 0 8 0 2-1 3-3 4-1 0-1 0-1-1 0-4 1-8 3-12zm5 23h1v1h0c0 1 0 1-1 2 1 1 0 1 1 1-1 1-1 2-1 4 0 1 1 2 2 2l5 8c1 2 1 4 2 6 0 4 0 8-1 11l-3-3c-1-1-2-3-2-5l-3-7c0-1-1-3-1-5-1-5 0-10 1-15z" class="V"></path><path d="M805 392c1-1 1-2 1-3h1l-5-12h1c10 18 14 37 13 58v-6c-2-1-4-1-6-1v3h0l-1-11v-1l-1-7c1-1 0-3 0-4l1-3c-1-4-2-9-4-13z" class="O"></path><path d="M809 405c0 1 0 2 1 2h1c1 2 0 8-1 10 0 1 0 1-1 2l-1-7c1-1 0-3 0-4l1-3z" class="E"></path><defs><linearGradient id="Av" x1="766.153" y1="332.233" x2="792.048" y2="325.6" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#Av)" d="M752 295v-2c3 1 5 1 7 1h1c4 1 11 3 14 6l3 1c0 3 1 4 1 7l5 12 1 1 5 11 4 10 7 14c1 4 3 7 4 10v1l-1 1c0-1-1-2-2-3-7-11-10-24-16-36-3-7-7-13-12-19h-1c-1 0-2-1-3-2 0-1-1-1-2-2 0 2 1 3 1 4v1c-4-4-8-9-13-13l1-1h0c-2-1-3-2-4-2z"></path><path d="M774 300l3 1c0 3 1 4 1 7l5 12c-3-3-4-6-5-9-1-2-2-3-3-5 0-2-2-4-1-6z" class="T"></path><path d="M755 298l1-1c6 4 13 8 17 13h-1c-1 0-2-1-3-2 0-1-1-1-2-2 0 2 1 3 1 4v1c-4-4-8-9-13-13z" class="D"></path><path d="M697 454c2 3 3 6 6 7 3 3 7 6 10 7v-1l1-1c-1-2-2-5-2-7h0c3 2 8 0 12 0l16 31 5 7 10 20c1 1 1 3 1 5 0 11-5 20-7 31v4l-1 2h1l-9 24h-2l-3 10-4 6c-2 2-4 5-5 7-4 5-8 8-13 11 2 1 3 2 5 2h1l2 1c1 0 4-1 6 0h8c-7 7-16 12-24 17-2 0-3 1-4 2l-5 2h-2 0c-6 2-11 3-17 4l-32 3c-2 1-6 1-9 2s-7 2-10 4c-6 2-11 5-16 8l-13 11c-3 0-5 5-8 5h-2v-1l3-9-1-1 9-23 5-13 36-92c1-2 1-3 2-4l10-27 4-9 1-3 2-4 4-13 2-4 7-18c1 0 1 0 2 1 2 1 4 1 6 3l8 4-1-7 2 4c0 2 0 3 2 5 1-4 1-9 1-13z"></path><path d="M662 521c1 0 3 1 4 1l-5 8c0-3 1-6 1-8v-1z" class="J"></path><path d="M655 521c2 0 3 0 5 1v1h-1-2-1v1h0l2 3h0c-2-1-3-2-5-3l1-2 1-1zm-8 14h1c1-1 1-2 3-2s4 1 5 3c0 1 0 1-2 2s-5 1-8 1h-1 0c1-2 1-3 2-4zm18-7l6 8c0 2-2 2-3 2-2 1-4 1-5 1-1-3 2-8 2-11z" class="V"></path><defs><linearGradient id="Aw" x1="699.542" y1="575.199" x2="687.65" y2="588.526" xlink:href="#B"><stop offset="0" stop-color="#282726"></stop><stop offset="1" stop-color="#484849"></stop></linearGradient></defs><path fill="url(#Aw)" d="M703 576h0v3c-1 1-4 2-5 3l-11 7-3-3 11-7c2-1 5-3 8-3z"></path><path d="M667 521l3-4 6 3c5 2 10 4 15 7v1c-1 1 0 2 1 4s3 4 4 6c-3-1-5-3-7-5-3-2-6-4-9-5-4-3-9-5-13-7z" class="a"></path><path d="M691 528c7 4 14 9 20 15 0 2-1 3 0 5 0 3 2 7 3 10s1 6 2 9v6c1 3 0 6 0 8 0 1 0 2-1 4h0c0-1 0-1 1-3 0-1-1-3 0-4v-3l-1-2-1-1-1 1c-1 1-4 1-5 2s-2 3-2 4v1c0 1 0 2 1 3v1c1 3 0 8-1 11h-1c2-7 2-12-2-19h0c-6-8-11-11-20-13 5-1 8-2 13-2l-23-23c10 4 19 5 29 8l-6-8c-1-2-3-4-4-6s-2-3-1-4z" class="Q"></path><path d="M703 576c4 7 4 12 2 19l-1 2c-1 3-3 6-6 8h-1c-3 1-6 4-8 5-5 3-10 5-15 7l-22 10c-4 2-7 4-10 6-3 1-7 2-10 3-3 0-6 2-10 3 0 0-2 1-3 1l-4 2-9 3h-1l-1-1 5-13h1c3-3 8-5 12-7l12-8h0l41-25 6-3 3-2 3 3 11-7c1-1 4-2 5-3v-3z"></path><defs><linearGradient id="Ax" x1="701.468" y1="604.626" x2="686.859" y2="603.359" xlink:href="#B"><stop offset="0" stop-color="#363637"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#Ax)" d="M678 610l26-13c-1 3-3 6-6 8h-1c-3 1-6 4-8 5-5 3-10 5-15 7 1-1 1-1 3-2 1-1 2-1 3-2h1s1-1 2-1h0l2-1c1-1 1-2 2-4-3 1-6 4-8 3h-1z"></path><defs><linearGradient id="Ay" x1="635.894" y1="607.461" x2="669.229" y2="632.147" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#Ay)" d="M681 588l3-2 3 3-3 1c-1 1-4 4-5 4l-24 15c-8 5-17 11-24 18 11-2 20-5 30-9 6-3 11-6 17-8h1c2 1 5-2 8-3-1 2-1 3-2 4l-2 1h0c-1 0-2 1-2 1h-1c-1 1-2 1-3 2-2 1-2 1-3 2l-22 10c-4 2-7 4-10 6-3 1-7 2-10 3-3 0-6 2-10 3 0 0-2 1-3 1l-4 2-9 3h-1l-1-1 5-13h1c3-3 8-5 12-7l12-8h0l41-25 6-3z"></path><path d="M681 588l3-2 3 3-3 1c-1 1-4 4-5 4v-1l1-1 2-1h0c-1-1-1-1-2-1h0c-2 1-3 1-4 1h-1l6-3z" class="L"></path><path d="M681 588l3-2 3 3-3 1c-1-1-2-2-3-2z" class="b"></path><defs><linearGradient id="Az" x1="609.352" y1="632.246" x2="629.254" y2="621.958" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#Az)" d="M634 616c-2 3-4 3-5 7h0l2-2v1 1h0c-3 1-5 3-7 4l-1 1c-2 2-4 4-7 6v1 1l-2 3c0 1-1 1-1 2h1 1c0-1 0-1 1-1h1 2l-4 2-9 3h-1l-1-1 5-13h1c3-3 8-5 12-7l12-8z"></path><path d="M605 645c1-2 2-4 2-5l7-7c2 0 3-1 4-2 1-2 3-3 6-4l-1 1c-2 2-4 4-7 6v1 1l-2 3c0 1-1 1-1 2h1 1c0-1 0-1 1-1h1 2l-4 2-9 3h-1z" class="L"></path><defs><linearGradient id="BA" x1="649.321" y1="686.378" x2="716.711" y2="540.941" xlink:href="#B"><stop offset="0" stop-color="#a6a4a4"></stop><stop offset="1" stop-color="#e6e5e5"></stop></linearGradient></defs><path fill="url(#BA)" d="M711 543c5 7 9 12 11 20v1l1 1v1c0 1-1 1 0 1v4h1c1-1 3-6 3-8 2-3 7-6 10-9h0c1-1 2-1 3-2v5c0 1 0 3-1 5h0v2c-1 1-1 2-1 3v1h1c0 2-1 2-2 5l-2 9c0 3-3 5-4 8 1 1 2 1 3 1v1c0 1-1 1-1 2h-1v1c-1 1-1 1-1 2v1l-2 2c0 1 0 1-1 2l-1 1v1l-2 1v1c-3 3-6 7-10 9-2 1-2 2-4 3-1 0 0 0-1 1h-1v1c2-1 3-2 4-3 2 1 3 2 5 2h1l2 1c1 0 4-1 6 0h8c-7 7-16 12-24 17-2 0-3 1-4 2l-5 2h-2 0c-6 2-11 3-17 4l-32 3c-2 1-6 1-9 2s-7 2-10 4c-6 2-11 5-16 8l-13 11c-3 0-5 5-8 5h-2v-1l3-9-1-1 9-23 1 1h1l9-3 4-2c1 0 3-1 3-1 4-1 7-3 10-3 3-1 7-2 10-3 3-2 6-4 10-6l22-10c5-2 10-4 15-7 2-1 5-4 8-5h1c3-2 5-5 6-8l1-2h1c1-3 2-8 1-11v-1c-1-1-1-2-1-3v-1c0-1 1-3 2-4s4-1 5-2l1-1 1 1 1 2v3c-1 1 0 3 0 4-1 2-1 2-1 3h0c1-2 1-3 1-4 0-2 1-5 0-8v-6c-1-3-1-6-2-9s-3-7-3-10c-1-2 0-3 0-5z"></path><path d="M651 648h0v-1c1 0 2 1 3 0h4 2c1-1 1-1 2-1l1 1c1-1 1-1 2-1s1 0 2-1c1 1 1 1 2 0h0 4 2c3-1 7 0 9-1s4-1 6-1h0c-5 0-10-1-15 0-4 0-8 0-13 1h-3c12-3 25-1 37-3 1-1 2 0 4 0-6 2-11 3-17 4l-32 3z" class="R"></path><path d="M684 618c-1 2-5 4-7 6-8 4-15 8-24 12-6 2-12 4-18 7 1 0 1 0 2-1h1 1 2l-1 1-4 1v1c1 0 2-1 3-1l9-3 8-2c1-1 1-1 2-1l1-1 3-1c1-1 2-1 3-1l9-5c1-1 3-2 4-3h2 0c-7 5-16 9-24 12-13 5-26 8-38 16h-2l-1 1h0-1-2l-1-1c2-1 3-1 4-3h0c1-2 4-3 6-4 14-7 29-12 44-19 6-3 13-7 19-11z" class="K"></path><defs><linearGradient id="BB" x1="602.149" y1="674.315" x2="605.858" y2="654.146" xlink:href="#B"><stop offset="0" stop-color="#9f9ea0"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#BB)" d="M596 668h0c3-8 12-13 19-16h0c-1 2-2 2-4 3l1 1h2 1 0l1-1h2l-6 4h0c-1 2-4 3-4 5-1 0 0 3 0 4l3-3c0-1 0-1 1-2s3-1 4-1l-13 11c-3 0-5 5-8 5h-2v-1l3-9z"></path><defs><linearGradient id="BC" x1="593.231" y1="675.597" x2="608.346" y2="665.518" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#989797"></stop></linearGradient></defs><path fill="url(#BC)" d="M612 659h0c-1 2-4 3-4 5-1 0 0 3 0 4l3-3c0-1 0-1 1-2s3-1 4-1l-13 11c-3 0-5 5-8 5h-2c5-8 12-14 19-19z"></path><path d="M674 617c5-2 10-4 15-7 2-1 5-4 8-5h1c-1 2-3 4-5 6l-7 5-2 2c-6 4-13 8-19 11-15 7-30 12-44 19-2 1-5 2-6 4-7 3-16 8-19 16h0l-1-1 9-23 1 1h1l9-3 4-2c1 0 3-1 3-1 4-1 7-3 10-3 3-1 7-2 10-3 3-2 6-4 10-6l22-10z"></path><path d="M619 640c1 0 3-1 3-1 4-1 7-3 10-3-2 1-3 2-5 3l-11 4s-1 1-2 1h0v-1c1-1 0-1 1-1l4-2z" class="V"></path><path d="M697 454c2 3 3 6 6 7 3 3 7 6 10 7v-1l1-1c-1-2-2-5-2-7h0c3 2 8 0 12 0l16 31 5 7 10 20c1 1 1 3 1 5 0 11-5 20-7 31v4l-1 2h1l-9 24h-2l-3 10-4 6c-2 2-4 5-5 7-4 5-8 8-13 11-1 1-2 2-4 3v-1h1c1-1 0-1 1-1 2-1 2-2 4-3 4-2 7-6 10-9v-1l2-1v-1l1-1c1-1 1-1 1-2l2-2v-1c0-1 0-1 1-2v-1h1c0-1 1-1 1-2v-1c-1 0-2 0-3-1 1-3 4-5 4-8l2-9c1-3 2-3 2-5h-1v-1c0-1 0-2 1-3 0-2 0 1 0-2h0c1-2 1-4 1-5v-5c-1 1-2 1-3 2h0c-3 3-8 6-10 9 0 2-2 7-3 8h-1v-4c-1 0 0 0 0-1v-1l-1-1v-1c-2-8-6-13-11-20-6-6-13-11-20-15v-1c-5-3-10-5-15-7l-6-3-3 4-1 1c-1 0-3-1-4-1v-1l1-9c-2-2-3-2-6-3l4-9 1-3 2-4 4-13 2-4 7-18c1 0 1 0 2 1 2 1 4 1 6 3l8 4-1-7 2 4c0 2 0 3 2 5 1-4 1-9 1-13z" class="G"></path><path d="M725 501c3 4 6 8 7 13 0-1-1-1-2-2-3-3-5-6-5-11z" class="D"></path><path d="M724 477h0c2 0 3 2 4 3 3 2 6 5 8 9v1h-1c0-4-9-9-11-12v-1z" class="C"></path><path d="M747 549v2l1 2h1v4l-1 2h-1l-2 3-1-1c1-4 1-8 1-11h1l1-1z" class="J"></path><path d="M747 551l1 2h1v4l-1 2h-1v-8z" class="C"></path><path d="M744 561l1 1 2-3h1 1l-9 24h-2l6-22z" class="N"></path><path d="M687 473c2 0 3 1 4 2 3 2 7 3 9 6l5 5c1 1 1 1 2 3l5 5h-2v1c-1-1-2-1-3-2v-2c-3-4-7-6-11-8l-4-3c-1-3-4-4-5-7z" class="C"></path><path d="M713 468c1 0 2 0 2 1 2 2 2 5 4 8-2 1-1 4-1 6 0 1 0 2 1 3h1v-1c0 1 0 0 1 2 0 1 2 3 3 4 2 1 3 3 4 5-1 0-2-2-3-3l-10-9-1-1v-6c-1-2 0-5-1-7-1-1 0-1 0-2z" class="B"></path><path d="M725 501l-3-3h1c5 6 10 12 14 19 5 10 8 21 10 32l-1 1h-1c-1-10-4-19-8-28-1-3-3-5-4-8h-1c-1-5-4-9-7-13z" class="I"></path><defs><linearGradient id="BD" x1="671.641" y1="471.972" x2="687.419" y2="476.175" xlink:href="#B"><stop offset="0" stop-color="#525151"></stop><stop offset="1" stop-color="#878685"></stop></linearGradient></defs><path fill="url(#BD)" d="M677 457c1 0 1 0 2 1 2 1 4 1 6 3-2 1-2 2-2 4l-1 2h-1 0 0l1 2h-1l-1 1c1 1 1 2 2 2 3 3 3 6 7 8l1 1c1 1 2 1 3 1s2 1 3 1c4 2 8 4 11 8v2c-3-2-6-4-9-5-6-3-12-6-19-7l-2-1h-3-1c-2 0-3 0-5-1l2-4 7-18z"></path><path d="M668 479l2-4v1c2 1 3 1 5 1 0 1 0 1-1 1v2h-1c-2 0-3 0-5-1z" class="Y"></path><path d="M679 458c2 1 4 1 6 3-2 1-2 2-2 4l-1 2h-1 0 0c-2 0-3-1-4-2 1-3 1-5 2-7z" class="I"></path><defs><linearGradient id="BE" x1="685.976" y1="458.674" x2="713.43" y2="481.993" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#BE)" d="M697 454c2 3 3 6 6 7 3 3 7 6 10 7h0c0 1-1 1 0 2 1 2 0 5 1 7v6l1 1-31-15c0 2 2 3 3 4 1 3 4 4 5 7l4 3c-1 0-2-1-3-1s-2 0-3-1l-1-1c-4-2-4-5-7-8-1 0-1-1-2-2l1-1h1l-1-2h0 0 1l1-2c0-2 0-3 2-4l8 4-1-7 2 4c0 2 0 3 2 5 1-4 1-9 1-13z"></path><path d="M697 454c2 3 3 6 6 7 0 2 0 2-1 3 0 3 1 7 0 9-2-2-1-6-1-8-1 1-1 1-1 2-2 1-2 1-3 1l-1-1h0c1-4 1-9 1-13z" class="M"></path><path d="M668 479c2 1 3 1 5 1h1 3l2 1 2 2c2 0 3 1 5 2 1 1 3 2 5 2 4 2 8 4 11 7 4 2 8 5 12 8 7 5 14 11 19 19 1 2 2 3 3 5 3 6 6 16 5 22-1 3-3 4-4 6-3 3-8 6-10 9 0 2-2 7-3 8h-1v-4c-1 0 0 0 0-1v-1l-1-1v-1c-2-8-6-13-11-20-6-6-13-11-20-15v-1c-5-3-10-5-15-7l-6-3-3 4-1 1c-1 0-3-1-4-1v-1l1-9c-2-2-3-2-6-3l4-9 1-3 2-4 4-13z" class="G"></path><path d="M662 520h1c3-1 2-5 3-8 2-3 3-4 6-6v1c-1 5-5 8-7 12l1 1 2-2c0-1 0 0 1-1l1-2c0-1 1-1 2-1l-2 3-3 4-1 1c-1 0-3-1-4-1v-1z" class="E"></path><path d="M672 497h1l-1 1v1c0 1-1 2-1 3l-1 2-1 1v1c1 0 1 0 2-1h3l-2 1c-3 2-4 3-6 6-1 3 0 7-3 8h-1l1-9h1c1-3 6-8 6-10l-1-1h0l2-3h1z" class="J"></path><path d="M706 501c8 5 16 11 22 18h0c1 0 1 1 2 1l3 6h0l-1-1c-1-2-2-4-4-4l1 1c1 1 2 2 2 3-2-1-4-3-5-4v-1c-1 0-2-1-2-1-3-4-7-7-11-10v-1c-1-1-2-2-4-3-1-1-2-2-3-4z" class="D"></path><path d="M675 496c2-1 3-2 6-2l1 4c1-1 2-1 2-2 1 1 2 1 2 2l4 2c-1 0-2 1-3 0s-2-2-3-1c-2 1-5 3-5 5-1 0-4 2-5 1h-3c-1 1-1 1-2 1v-1l1-1 1-2c0-1 1-2 1-3v-1l1-1 2-1z" class="M"></path><path d="M735 528l1-2c3 6 6 16 5 22-1 3-3 4-4 6-3 3-8 6-10 9 0 2-2 7-3 8h-1v-4c1-6 4-13 4-20 0-4-1-7-1-10 1 1 1 1 1 2v1c1 7 1 14 0 20 4-4 9-7 12-12 2-2 1-6 0-9v-1c-1-3-2-6-4-10z" class="E"></path><path d="M679 504c0-2 3-4 5-5 1-1 2 0 3 1s2 0 3 0 1 0 1 1l6 3 1-2c3 2 5 4 8 6l2 2-1 1-7-3c-4-2-8-1-12-2-3-1-6-2-9-2z" class="B"></path><path d="M698 502c3 2 5 4 8 6h-3c-1-1-2-1-3-1l-3-3 1-2z" class="D"></path><defs><linearGradient id="BF" x1="707.119" y1="502.348" x2="703.672" y2="512.254" xlink:href="#B"><stop offset="0" stop-color="#a7a6a5"></stop><stop offset="1" stop-color="#c6c5c6"></stop></linearGradient></defs><path fill="url(#BF)" d="M691 494c2 0 3 1 4 2h1c6 4 15 12 19 19 2 4 6 7 8 12h-1c0-1 0-1-1-1-1-1-2-1-2-2-1-2-3-3-4-4-3-3-4-6-8-9l1-1-2-2c-3-2-5-4-8-6-1-2-2-3-4-4h1l-4-4z"></path><defs><linearGradient id="BG" x1="659.434" y1="504.838" x2="666.554" y2="504.662" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#7d7c7c"></stop></linearGradient></defs><path fill="url(#BG)" d="M664 492l4 3c1 0 3 1 4 2h-1l-2 3h0l1 1c0 2-5 7-6 10h-1c-2-2-3-2-6-3l4-9 1-3 2-4z"></path><path d="M664 492l4 3c1 0 3 1 4 2h-1c-3 1-6 1-9 2h-1l1-3 2-4z" class="P"></path><path d="M664 492l4 3c-2 1-4 2-6 2v-1l2-4z" class="V"></path><path d="M701 497h1-1c0-1-1-1-1-2h-2c0-1-1-1-2-2h0 1l2 1h3c4 2 8 5 12 8 7 5 14 11 19 19 1 2 2 3 3 5l-1 2-2-2-3-6c-1 0-1-1-2-1h0c-6-7-14-13-22-18l-5-4z" class="M"></path><defs><linearGradient id="BH" x1="674.903" y1="488.065" x2="696.922" y2="499.764" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#cdcdcd"></stop></linearGradient></defs><path fill="url(#BH)" d="M668 479c2 1 3 1 5 1h1 3l2 1 2 2c2 0 3 1 5 2 1 1 3 2 5 2 4 2 8 4 11 7h-3l-2-1h-1 0c1 1 2 1 2 2h2c0 1 1 1 1 2h1-1l-4-2-1 1h-1c-1-1-2-2-4-2l4 4h-1c2 1 3 2 4 4l-1 2-6-3c0-1 0-1-1-1l-4-2c0-1-1-1-2-2 0 1-1 1-2 2l-1-4c-3 0-4 1-6 2l-2 1h-1c-1-1-3-2-4-2l-4-3 4-13z"></path><path d="M680 491c1 2 3 4 4 5 0 1-1 1-2 2l-1-4h0l-1-2v-1z" class="E"></path><path d="M674 480h3l-3 1h0l1 1v1c0 1 0 1 1 2h-1l-1-1-1 1v-1c-1-1 0-2 0-4h1z" class="I"></path><path d="M674 486c2 1 4 3 6 5v1l1 2h0c-3 0-4 1-6 2 1-1 2-2 2-3s-1-2-2-2c0-2-1-4-1-5z" class="R"></path><path d="M677 480l2 1 2 2c2 0 3 1 5 2 1 1 3 2 5 2 4 2 8 4 11 7h-3l-2-1h-1 0c1 1 2 1 2 2h2c0 1 1 1 1 2h1-1l-4-2-1 1h-1c-1-1-2-2-4-2l-4-3h0c-1-1-3-2-4-2h0-2l-3-3h0 2-2l-1-2h1l-3-2-1-1h0l3-1z" class="N"></path><defs><linearGradient id="BI" x1="665.769" y1="483.396" x2="675.699" y2="492.759" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#BI)" d="M668 479c2 1 3 1 5 1 0 2-1 3 0 4v1l1 1c0 1 1 3 1 5 1 0 2 1 2 2s-1 2-2 3l-2 1h-1c-1-1-3-2-4-2l-4-3 4-13z"></path><path d="M672 514c3-1 7-3 10-2 2 2 2 5 3 7 2-3 5-8 8-8 4-1 9 2 12 4 10 7 16 18 18 29 1 7 0 12-1 19-2-8-6-13-11-20-6-6-13-11-20-15v-1c-5-3-10-5-15-7l-6-3 2-3z"></path><path d="M185 182l-6-12h238l14 33c-6 7-12 14-16 23-5 11-7 23-5 35 3 15 9 29 15 43l23 62 76 198c2-5 3-10 5-14l13-32 34-90 35-91 17-43c4-10 9-21 10-32 3-21-8-44-21-60l12-33 222 1c-1 4-4 7-6 11-1 2-2 4-3 5-2 3-4 5-6 7h1s1 0 2-1v1 3h-16v1h-1c0 1 0 1-1 2 0 1-1 2-2 2-1 1-2 1-2 2 1 2 2 7 4 9h0v2l-3-5c-2 0-6-1-8 0h0c-2-3-4-6-6-8-10 4-17 10-25 16-4 3-8 6-11 11l-25 62-4 10-12 30c4 3 4 10 7 14-2 1-2 1-4 0-1 0-1 0-2-1h-1c0 2 1 2 1 3s0 1 1 1v1h-1l-2-2h-4v-1l-1 2v-1l-14 34c1 1 2 1 4 2v1l5 4-6 4 2 1c-1 4-3 10-5 13-1 1-3 2-3 4-2 2-3 4-6 6-2 6-2 12-4 18 1 7-1 15 3 21 0 4 0 9-1 13-2-2-2-3-2-5l-2-4 1 7-8-4c-2-2-4-2-6-3-1-1-1-1-2-1l-7 18-2 4-4 13-2 4-1 3-4 9-10 27c-1 1-1 2-2 4l-36 92-5 13-9 23 1 1-3 9v1h2c-1 2-1 2-1 4l7-6 2 1-1 1 1 1c1 0 1 1 2 1l1-1h1c1 0 2-1 4-2v1l-1 1h-1l-1 1c-1 0-3 1-4 2-1 0-1 0-2 1v1c1 0 1 0 2-1 3 0 4 0 6 2l-3 1c1 1 2 1 3 1-2 2-3 2-4 4l-6 4c-4 2-9 4-12 8l-3 3-1-1h-1c-1 1-1 2-3 3h0l-2 3-8 21-38 100c-2 3-4 8-4 11-2 3-3 6-4 10-3 7-5 15-8 22-3-3-4-8-5-12l-12-28c0-1-1-3-1-4l-32-84-2-2-7-2 1-1c-1-1-2-2-3-4 0-2-2-5-5-6v-1h-1c0-2-1-3-2-5-2-5-5-10-8-16 0-1 0 0-1-1l-1-2c2 0 3 2 5 3 3 1 6 2 9 4v-3c-3-2-5-4-8-7 1 0 1-1 1-1h1c1-3-1-7-2-10-1-1 0-2 1-4l-6-14-1-1c-1-2-2-3-2-5s0-4-1-5l-6-17-7-15-48-125-4-9-2-6h-1-1c-1 0-3 1-4 2-1 0-2 1-4 1h-1c-1-3 0-4 0-7 0-1 0-2 1-4v-1c-1-1-1-2-1-3v-1h-2l1-4-2-1v-1l1-1c-1-2 0-3 1-5l-3-10-2 1-2 1c-4 1-8 4-12 5-1 1-2 1-3 3h-1c-1-5 1-9 1-14h0c-1-1-1-2-1-3l1-1c0-3 0-7-2-9l1-1c-4-10-9-19-17-26-5-4-11-8-17-11l-3 3v1l3 2c-2 1-2 2-3 4-1 4-4 8-7 11l-6 3h-2s0 1-1 1c-1 1-2 1-2 1l-1 1c0-1 0-1 1-2 1-2 2-3 2-5v-1l2-7 3-6v-2l1-1v-1l-23 18h-2c3-3 7-5 9-8l1-1h0l-5 4c-2 0-3-1-5-1h0c0-2 2-5 2-7h0l-1-1c0-1-1-2-2-3l-4-4c-1 0-1 0-1-1 1-1 2-1 3-2h-1c-1-2 3-7 4-9-1-2-2-2-3-3-2 0-3-1-4-2l3-4 7-6 8-5 2-1c1-1 1-2 1-4 1-1 1-3 1-4s0-2 2-3v-4l-1-4c1-3 0-8 1-12v-6-5c1-3 1-6 1-9l-2-3c-2-1-3-1-4-1-2 0-3-2-5-2-1-1-2 0-3 0v-1c2-1 4-4 5-5 4-3 8-6 11-9h3v-1c2 0 3 0 4-1l2-1-1-3-25-62c-2-5-6-8-11-11-8-6-16-12-25-16-4-2-8-4-12-5-2 0-5-1-6-1-5-1-10-2-15-2h-1c-1-1-2-3-3-4l-4-6-1-1z" class="W"></path><path d="M410 386l1 1-2 3h-1l-1-1 3-3z" class="Q"></path><path d="M650 376c2-1 3 0 5-1 1 0 1 0 2 1h1l1 1c-2 1-4 0-6 0-1 0-2 0-3-1z" class="H"></path><path d="M658 176c3-2 9-1 13 0 6 0 13-1 19 0h-23c-2 0-7 1-8 1 0-1-1-1-1-1z" class="Q"></path><path d="M601 463c1-1 1 0 2 0h0c-4 1-8 1-11 3l1-1-2-1 1-1h1 1c1 1 2 0 3 0 1-1 2 0 4 0z" class="K"></path><path d="M421 418h0v1c1-1 0 0 2 0-3 1-5 2-7 3h-1c-2 1-4 1-6 2h-3c6-2 10-3 15-6z" class="H"></path><path d="M608 427l2-2v6l1 3-1 4c-1 3-1 4-3 6v-1c1-1 1-3 2-4v-1-10l-1-1z" class="D"></path><path d="M432 460c3-1 8 1 11 2s5 1 8 1c1 0 0 0 1 1h-1l1 1h0l-17-4c-1 0-2 0-3-1z" class="G"></path><path d="M653 360c2 0 5 1 7 2h2c2 1 4 1 6 1h1 0c-2 1-4 2-7 2-4 0-6-2-9-5z" class="H"></path><path d="M325 351c1 1 2 1 3 2 0 1 1 1 2 1l7 10-1 1c-1-1-3-3-4-5-1-1-1-3-2-4l-1 1c1 2 2 5 2 7v1c-1-1-1-3-2-4l-4-10z" class="R"></path><path d="M693 415c1 1 1 1 1 2-1 2-1 3-1 5-1 3-2 7-3 10-1-1-1-2-2-3l5-14z" class="a"></path><path d="M767 181c3-1 8-1 11-1 8 0 16 0 25 1h-6c-2 1-4 1-5 1h-11c-2 0-4-1-6 0h-1c-1 0-4 1-6 0 2-1 4 0 6-1h-7z" class="H"></path><path d="M474 533l-1-4v-1h-1l-1 2c0-2 0-2 1-3l1-5c0-1 0 0 1-1v1c1 2 0 0 1 1v2c0 1 1 2 1 3 1 1 1 2 1 3l-1 1c0-1-1-3-1-4h-1c0 2 1 5 2 7s1 3 2 4c-1 0-2-1-2-2l-1-1-1-3z" class="D"></path><path d="M395 445c-1 0 0 0-1-1h-1c0 1 0 0-1 1l-3-4c-1-1-5-6-5-7l1-1c2 1 2 1 4 3 1 0 2 0 4 1-1 1-1 0-1 1-1 1 0 1 0 2 1 2 3 3 3 5z" class="K"></path><path d="M585 693c1 1 1 1 1 2l2 2c-1 1-2 3-3 4l1 1h-1l-2 3h0c-1 1-1 2-3 3h0l5-15z" class="a"></path><path d="M352 410c-1 0-1-1-3-1-1 0-1 0-1-1l-1-1c0-2-1-3-2-5l-2-5 1-1c0 1 1 2 2 3 0 1 1 1 1 2a30.44 30.44 0 0 0 8 8l1 1h-4 0z" class="G"></path><path d="M641 317l1 1c-1 1-1 2-2 3v2c-1 1-1 2-1 3l2-1h0c0 1-1 2-1 3l-1 1c-1 2-1 3-2 4l-2 4c0 1-1 2-1 2h-1v-2l1-1c0-5 4-11 6-15l1-4z" class="K"></path><path d="M707 380c1 1 2 1 4 2v1l-2-1c-2 2-2 7-2 9v1c-2 2-4 3-6 5h0l6-17z" class="P"></path><path d="M377 308l6 6c5 6 12 10 16 17-3-1-4-3-6-5-3-2-5-4-7-6s-5-4-6-6l-1-1c-1-2-1-3-2-5z" class="G"></path><path d="M312 317h0l5 11c1 4 3 9 6 13 2 5 4 9 7 13-1 0-2 0-2-1-1-1-2-1-3-2l-13-34z" class="I"></path><path d="M707 392v-1c0-2 0-7 2-9l2 1 5 4-6 4-3 1z" class="V"></path><path d="M500 620v-2c1 3 2 7 1 10 0 4-1 9-5 11-1 1-3 2-4 1-3 0-4-1-6-3l-1-2v-4 1 1c1 1 1 3 2 4s2 2 4 2 3-1 4-2c5-6 5-10 5-17z" class="R"></path><path d="M622 453c-4 2-10 3-14 4h-1c-1 0-4 1-5 0v-1l5-1c1-1 0-1 1-1 2 0 3 0 4-1h1c2-1 3-1 4-2 2-1 3-1 5-2v4z" class="K"></path><path d="M451 711h1l14 36-2-2h-1c0-3-1-5-2-7l-7-17-3-6v-1-3z" class="P"></path><path d="M656 341v-1c-1-2 0-4 0-5l1-1v-2h1v-1c1-2 5-4 7-4l1-1h3l1 1c-1 2-2 2-4 3-1 0-2 1-3 1-1 1-3 3-4 5-2 1-2 2-3 4v1z" class="Q"></path><path d="M489 635c1 1 2 1 3 1l3-3v-1l1-1c0-3 2-5 3-7 0-1 0-2 1-3v-1c0 7 0 11-5 17-1 1-2 2-4 2s-3-1-4-2l2-2z" class="D"></path><path d="M617 380l1 2h0c-3 8-8 17-8 25-1 6-1 12 0 18l-2 2c0-5-1-10 0-15 0-11 5-22 9-32z" class="B"></path><path d="M622 449v-1c1-1 1-3 1-4h-1 0l1-1c1 0 1-1 2-2 1 3-2 7-1 10 3 0 6-3 8-5 4-3 7-6 11-9-3 6-6 10-12 13l-6 3c-1 0 0 0-1 1-1 0-1 0-2-1v-4z" class="R"></path><path d="M444 689l8 22h-1c-3-2-5-4-8-7 1 0 1-1 1-1h1c1-3-1-7-2-10-1-1 0-2 1-4z" class="X"></path><path d="M385 320h-1c-5-4-9-11-11-17v-1c-1-3 0-7 2-10l3-3c2 0 4 1 6 2-1 0-2 0-3-1-2 1-3 2-4 4-3 4-1 9 0 14 1 2 1 3 2 5l1 1c1 2 4 4 6 6h-1z" class="F"></path><path d="M699 401v1 1c-2 4 0 8-1 12-2 6-2 12-4 18-1-3 0-8 0-11h-1c0-2 0-3 1-5 0-1 0-1-1-2 1-4 4-10 6-14z" class="Y"></path><path d="M451 443c0-1 0-1-1-1 0 1 0 2 1 3l1 9h0s-1 1-1 0c0 0 0-3-1-4 0-5-1-10-3-14 0-2-1-4-2-5l-1 2c-1-4 0-8 1-12l7 20-1 2z" class="N"></path><path d="M489 635c3-3 7-9 8-14 0-2 0-5 1-7h0c0-2-1-4-1-6-1-1-1-2-1-4v-1c2 5 4 10 4 15v2 1c-1 1-1 2-1 3-1 2-3 4-3 7l-1 1v1l-3 3c-1 0-2 0-3-1zm146-109h1 0c1-2 1-3 2-5l1 1-11 26h-1 0c0 1-1 2-1 2h-2l8-19c1-1 1-3 2-4l1-1z" class="O"></path><path d="M352 410h0 4c6 3 11 6 16 10a30.44 30.44 0 0 1 8 8h0c-2 0-3-5-5-3h0c-1-1-2-2-4-2h0l-4-4h0c-3-2-7-3-10-5 0-1 0 0-1-1h0l-2-1c-1 0-1-1-2-2z" class="D"></path><path d="M416 351l1 5c2 6 0 13 1 19 0 4 1 8 1 12 0 2-1 4-1 5v1c-1 3-3 6-5 9v-1c2-3 3-6 4-10 1-3 0-6 0-9 0-4-2-8-1-12v-1l-2 3-3 5h0c2-3 3-7 4-10h0c1-4 2-8 1-11l-1-5h1z" class="B"></path><path d="M449 665v-1l1 1h0v2 1c1 0 1 0 1 1 1 1 1 1 1 2l3 8v1l3 6c0 1 0 1 1 2v1c0 1 1 2 2 3 2-6 2-13 3-18l1-4c0 4-1 9-1 13v1c-1 2-1 5-1 7-1 1-1 6-1 8 1 1 1 2 2 4 1 1 1 2 1 3h-1l-3-9-12-32z" class="I"></path><path d="M385 320h1c2 2 4 4 7 6 2 2 3 4 6 5l1 1c2 4 5 9 6 13v-1l-1 1v2c1 8-3 15-8 21h-1c3-4 5-7 6-10v-1c1-3 2-7 2-10 0-2-1-5-2-6-4-9-11-14-17-21z" class="J"></path><path d="M650 376c-5-2-9-7-11-11s-3-8-2-12h0v4c1 3 0-1 0 2l1 1h0c0 2 1 4 2 5l1 1 1 2 1 1c1 1 2 2 3 4v-1l-2-2-2-3-1-1c0-1 0-1-1-2h0v-1l-1-1c0-1 0-1-1-3h0v-2l-1-1c0-2 0-4 1-6 1-1 1-2 1-3l1 1h0c0 3-1 8 0 10v1c0 1 0 2 1 2v1c1 1 1 2 2 3v1c2 3 6 6 8 8 2 0 3 1 4 1-2 1-3 0-5 1z" class="K"></path><path d="M659 336h0c-1 2-1 3-1 4 3 2 4 1 7 1 3-1 6-3 8-6 1-2 2-5 3-7v-1h1c1 1 1 4 0 6 0 2-1 3-2 4v1c-1 1-2 1-2 2h-1c-2 2-4 2-5 3s-1 1-2 1h-2-4l-3-3v-1c1-2 1-3 3-4z" class="H"></path><path d="M486 631v1l1 1c0 1 0 1 1 2l1-1v-1c1-1 3-2 3-4l2-3h0c0-1 1-1 1-2-1 0-2 1-3 2v2h-1c0-1 1-2 1-3 1-1 2-2 2-3v-4h1c0-3-1-6 0-8v-4l-1-4c-1-1-1-1-1-2l-1-2c1-1 1-2 1-2l2 4c0 1 1 2 1 3v1c0 2 0 3 1 4 0 2 1 4 1 6h0c-1 2-1 5-1 7-1 5-5 11-8 14l-2 2c-1-1-1-3-2-4v-1-1c-1-1-1-2 0-3v-1h0c1 1 1 2 1 4z" class="G"></path><path d="M451 443l1-2 21 58v5c-2-2-3-7-4-10 0-2-2-6-3-8-2-6-3-12-5-18-3-8-7-17-10-25z" class="R"></path><path d="M648 190l-1-1 1-1 1-1h40 0c-2 1-4 0-5 1h3 1 6 17 24c4 0 9 0 13 1h-12-50-25c-4 0-10-1-13 1z" class="D"></path><path d="M710 336h1l-12 31-1-1c2-5 5-11 7-17l-3 3v1c-5 7-16 13-24 16-2 0-5 1-7 1h-1c-2 1-6 1-7 0 1-1 5-1 7-1 6-1 13-3 18-6s8-6 11-9l1-1c1 0 2-3 3-4 2-4 5-9 7-13z" class="I"></path><path d="M727 330c4 3 4 10 7 14-2 1-2 1-4 0-1 0-1 0-2-1h-1c0 2 1 2 1 3s0 1 1 1v1h-1l-2-2h-4v-1l-1 2v-1l6-16z" class="P"></path><path d="M341 331h1v1l1 2v1c1 1 1 0 3 1s2 3 5 4h1c2 1 5 1 8 0h1c5-1 8-4 12-7 1 1 1 1 1 2-1 2-2 2-3 3l-2 2c-1 1-2 1-4 2h-1-1l-1 1h-3c-1 1-2 1-4 1v-1h-4v-1h-2l-1-1h-1c-2-1-5-5-5-7l-1-3z" class="G"></path><path d="M303 291c4 6 6 14 9 21s6 13 9 20c1 3 2 6 2 9-3-4-5-9-6-13l-5-11h0c-2-3-3-7-5-10-2-5-4-10-5-15l1-1z" class="D"></path><path d="M423 200h1c1 2-2 4-2 6-9 12-17 27-18 42l-1 1c0-3 0-6 1-9 0-6 1-12 4-17 3-6 6-12 10-17 1-2 3-3 4-5 1 0 1-1 1-1z" class="C"></path><path d="M686 189h50c-3 2-7 1-11 1v1h2v1h-7 1c2 3 3 4 3 7 1 1 1 1 1 2-1-1-2-3-2-4l-2 1v1 1c-1-1-1-1-1-2-1-1-2-3-3-4-1 0 0 0-1-1l-3-3h-6-21 0v-1z" class="F"></path><path d="M720 198v-1c-1-1-1-2-2-3v-1h-1v-1c1 0 2 0 3 1h0c1 1 2 2 2 3l1 1-2 1v1 1c-1-1-1-1-1-2z" class="D"></path><path d="M467 714h1c1 1 1 2 1 3 2 3 4 7 6 9 2 3 3 7 5 10 1 2 3 5 4 7s3 3 3 5v1l-4-6c-1 2 0 5 1 8 1 0 1 1 1 1 1 2 2 5 2 7-2-2-3-6-5-9l-1 1-14-37z" class="J"></path><path d="M482 750l-3-8c-1-2-1-4-2-6h1l5 7c-1 2 0 5 1 8 1 0 1 1 1 1 1 2 2 5 2 7-2-2-3-6-5-9zm3-115l-1 1h0c-1-2-1-3-2-4s-1-2-1-3v-1-1-2l1-1v-1l3-5 1-2 1-1s0-1 1-2l1-1v-3c1-1 1-2 2-3v-1h0v1 1l1-1h0v3 1l1 1c0 1 0 3-1 4v1c-1 2-1 4-2 6-1 1-3 3-3 5v1c-1 1-1 2-1 3 0-2 0-3-1-4h0v1c-1 1-1 2 0 3v4z" class="H"></path><path d="M702 306v1c-2 4-5 7-6 11l-6 12c-4 6-9 12-16 15v-1h0v-1c1 0 2-2 3-2a30.44 30.44 0 0 0 8-8c3-5 5-13 8-19 2-3 5-9 10-10l-1 2z" class="K"></path><path d="M384 258h1c3 20 9 39 17 58 5 11 11 23 14 35h-1c-3-12-9-24-14-35-3-6-5-13-7-19-4-10-7-19-9-29-2 3-5 7-8 10h-1 0c3-3 6-7 8-11 1-1 1-1 0-2-2 3-5 9-8 10h0c1-1 2-3 3-4 3-4 5-8 5-13z" class="F"></path><path d="M417 382c0 3 1 6 0 9-1 4-2 7-4 10v1h0c-2 4-7 8-10 10-5 3-12 6-18 5-3 0-5-1-8-2h6c6-1 11-3 17-6 4-2 7-5 10-8v-1c3-3 4-6 6-9v-1-2c1-2 1-3 1-6z" class="K"></path><path d="M707 392l3-1 2 1c-1 4-3 10-5 13-1 1-3 2-3 4-2 2-3 4-6 6 1-4-1-8 1-12v-1-1c0-1 1-3 2-4h0c2-2 4-3 6-5z" class="E"></path><path d="M322 376l8 21h-3c-2-1-4-3-6-4-2-3-7-5-10-6v-1c5-2 7-5 11-10z" class="P"></path><path d="M365 180l9-1v1h-9c-4 1-8 1-12 1-1 0-3-1-4 0h-8c-2 0-5 0-6 1h14 4 13v1c-3-1-7-1-10-1h-16l-83-1h-31c-4 0-9 0-12-1h9 24 73c15 0 30 1 45 0z" class="C"></path><path d="M524 640l1-1c0-4-1-8-1-12h1c0 2 0 5 1 7v1c1 2 2 5 4 7v3c-1 1-1 1-1 2l1 1c-1 1 0 2 0 3 0 0-1 0-1 1s0 2-1 3v1c-1 3-4 6-7 7h-1c-1 1-5 1-7 1l-1-1c-1 0-1 0-2-1-3-1-5-4-6-6s0-1 0-2c-1-1-1-1-1-2l-1-1c0-2 0-3 1-5-1 1-1 0-1 1-1 1-3 3-5 3v1h-2l-1 1h-1c-1 0-2-1-3-1-2 0-3-1-4-2-2 0-2 0-3-1 1-1 1 0 1-1l-1-2v-1c1 2 3 3 4 4l1 1h3v1h3l1-1h1c3-1 5-3 6-6h1c1-4 1-6 2-10h1v3c1 1 1 1 1 3 0-1 1-1 2-2h0l2-1v1c-3 2-5 3-7 7 0 1-1 3-1 5 1 4 2 6 4 9 1 2 2 3 4 4l3 1c2 0 5 0 8-2s6-5 6-9c1-2 1-5 0-7-1-1-2-3-3-4l-1-1z" class="D"></path><path d="M700 353v-1-1h0l1-1h0c1-2 2-4 3-5l1-2 1-1v-3l1-1 1-4c0-3 2-6 4-9 2-4 4-9 6-14v-1c-2 2-5 5-8 5-1 1 0 1-1 0 6-4 11-11 15-17 1-3 2-7 5-9l-9 22-6 16c-1 3-3 6-4 9-2 4-5 9-7 13-1 1-2 4-3 4z" class="G"></path><path d="M291 264l2 3c2 7 8 13 14 16 8 4 18 5 26 2l1 2c-9 2-20 3-28-2-4-2-6-5-9-8 0 2 2 4 3 5 4 4 11 8 16 10 1 1 2 2 4 3v1c-1-1-2-1-3-1 7 8 12 16 15 27h0c-3-9-9-19-16-26-5-5-11-9-17-13 1 3 2 5 4 8l-1 1c-3-9-8-19-11-28z" class="T"></path><defs><linearGradient id="BJ" x1="521.83" y1="660.35" x2="518.152" y2="651.298" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#888787"></stop></linearGradient></defs><path fill="url(#BJ)" d="M524 644v-1c1 0 1-1 1-2 1 1 2 3 3 4 1 2 1 5 0 7 0 4-3 7-6 9s-6 2-8 2l-3-1c-2-1-3-2-4-4-2-3-3-5-4-9l3-2c0 3-1 5 0 7 1 1 2 2 4 3h5c2-1 4-2 7-4 1-1 1-2 2-4s0-3 0-5z"></path><path d="M507 658c1 0 2 0 3 1h0s1 0 1 1h0v2c-2-1-3-2-4-4z" class="T"></path><path d="M524 644v-1c1 0 1-1 1-2 1 1 2 3 3 4h-1v1 2c-2 3-2 6-5 7-2 1-5 2-7 2 2-1 4-2 7-4 1-1 1-2 2-4s0-3 0-5z" class="I"></path><defs><linearGradient id="BK" x1="363.284" y1="484.141" x2="355.432" y2="486.442" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#BK)" d="M353 456l7 19c2 5 5 11 7 17h-1-1c-1 0-3 1-4 2-1 0-2 1-4 1h-1c-1-3 0-4 0-7 0-1 0-2 1-4v-1c-1-1-1-2-1-3v-1h-2l1-4-2-1v-1l1-1c-1-2 0-3 1-5l-3-10 1-1z"></path><path d="M354 472c2 2 2 2 2 5 0 1 1 1 1 2 2 3 3 9 1 12v1c-1-1-1-3-2-4 0-1 0-2 1-4v-1c-1-1-1-2-1-3v-1h-2l1-4-2-1v-1l1-1z" class="S"></path><path d="M662 445c1 0 2 0 3-1 2-1 2-7 4-9 1 0 1 0 2-1 0-2 1-4 2-6h0l1-2c0-1 0-2 1-2 0-3 1-3 3-5l-25 64c0 1 0 1-1 2 0-1 0-2 1-3l1-2v-4c0-1-1-1-2-1-2-1-5-1-6-2l1-1 2 1h2c2 1 2 1 4 0-1-1-1-1-1-2h-1v-1h2l1-1 4-14v-1c1-1 1-2 1-3v1l-1-1c2-1 4-3 5-5-1-1-2-1-3-1z" class="C"></path><defs><linearGradient id="BL" x1="649.973" y1="376.513" x2="641.766" y2="384.751" xlink:href="#B"><stop offset="0" stop-color="#848080"></stop><stop offset="1" stop-color="#9d9d9d"></stop></linearGradient></defs><path fill="url(#BL)" d="M617 380c1-4 4-9 6-13l9-26 1 1c-2 4-4 8-5 12v1c-1 2 0 4 0 6s0 3 1 5c2 7 9 14 15 17a41.54 41.54 0 0 0 31 0c8-3 19-8 23-17l1 1c-2 3-3 7-5 10-1 3-2 6-3 8v-2-1c2-3 4-7 5-11h0c-8 9-24 16-36 17h-8-1c-8-2-14-5-19-12-4-5-4-11-6-17-2 5-2 9 0 14s5 10 10 12c1 1 2 2 3 2l-1 1c-6-3-11-9-14-16l-1-4-5 12v2l-1-2z"></path><path d="M508 644c0-1 1-1 1-2 2-2 4-2 7-2 4 0 5 2 8 4 0 2 1 3 0 5s-1 3-2 4c-3 2-5 3-7 4h-5c-2-1-3-2-4-3-1-2 0-4 0-7l2-3z" class="W"></path><path d="M508 644v5c2 1 7 1 10 1h1l5-1c-1 2-1 3-2 4l-1-2c-3 0-5-1-7 0s-4 1-5 1h-1v1c-1 0-1 1-2 1-1-2 0-4 0-7l2-3z" class="K"></path><defs><linearGradient id="BM" x1="517.323" y1="654.878" x2="512.067" y2="650.185" xlink:href="#B"><stop offset="0" stop-color="#bdbcbd"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#BM)" d="M506 654c1 0 1-1 2-1v-1h1c1 0 3 0 5-1s4 0 7 0l1 2c-3 2-5 3-7 4h-5c-2-1-3-2-4-3z"></path><path d="M371 423c2 0 3 1 4 2h0c4 7 6 15 8 22 4 9 11 15 19 19 21 9 51 9 67 28 1 3 2 8 4 10-1 5-3 10-6 13 1-3 3-6 3-9 1-4 0-8-3-12s-8-7-13-10c-9-4-19-8-28-11-6-2-12-4-18-5-1-2-4-2-6-3-5-2-11-5-14-10-3-3-5-7-6-11-1-2-2-5-3-8h-1l1-1c-2-5-5-10-8-14z" class="T"></path><path d="M530 792c2-3 3-6 5-8 0-1 1-2 1-2 0-1 1-2 1-3l2-4c0-1 0-2 1-3l1-2c0-1 0-3 1-4s1-1 2-1l-29 74-12-32 1-1 1 3c0 1 1 2 1 3s1 3 1 4c1 1 1 2 2 3 0 1 0 2 1 2 1 2 3 3 4 5v1h1c2-2 3-6 4-8 3-7 7-14 9-21 1-2 2-4 2-6z" class="I"></path><path d="M482 750c2 3 3 7 5 9l6 12h-1c-1-1-2-4-3-5 0 2 2 5 3 8 2 4 3 9 5 12 4 6 6 13 9 19 1 3 3 6 4 10 1 3 3 6 4 10h1c1-1 1-2 1-3 1-4 3-8 4-11 1-2 2-4 2-5 2-3 4-6 6-8-2 7-6 14-9 21-1 2-2 6-4 8h-1v-1c-1-2-3-3-4-5-1 0-1-1-1-2-1-1-1-2-2-3 0-1-1-3-1-4s-1-2-1-3l-1-3-1 1c-4-7-6-16-9-23-4-11-9-22-13-33l1-1z" class="F"></path><path d="M372 470c-1-1 0-1-1-2l-5-12-6-15-3-9-2-5-1-3h0v-1l1 1 1 1c1 1 3 2 4 3s2 1 3 2h-1c-2 0-2 0-4-1 0 1 1 2 1 3v1c1 1 1 1 1 2 1 2 2 3 2 5v1l1-3c1 1 2 4 3 6l1 1h-1c0 1 1 2 1 3h1v2c1 1 0 1 1 2v1h0c0 1 0 1 1 2l2 4c1 2 1 4 2 6 1 1 1 2 2 3 0 1 1 2 1 2 3 0 6-1 9-1h1c3-1 6-1 9-1 2 0 4 0 6-1 2 1 5 1 6 3-4 0-9 0-13 1-3 1-5 0-8 1-2 1-4 1-6 1-1 1-2 1-3 1h0l-1-1h-2l-1-1-2-2h0z" class="G"></path><defs><linearGradient id="BN" x1="452.56" y1="723.166" x2="447.408" y2="725.095" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#BN)" d="M437 707c2 0 3 2 5 3 3 1 6 2 9 4v1l3 6 7 17c1 2 2 4 2 7h1l-7-2 1-1c-1-1-2-2-3-4 0-2-2-5-5-6v-1h-1c0-2-1-3-2-5-2-5-5-10-8-16 0-1 0 0-1-1l-1-2z"></path><path d="M569 688c0 2 0 3 1 4h0l1-1c0-1 0-2 1-2h1l-2 4-6 18-16 41-4 10c0 1-1 2-1 3-1 0-1 0-2 1s-1 3-1 4l-1 2c-1 1-1 2-1 3l-2 4c0 1-1 2-1 3 0 0-1 1-1 2-2 2-3 5-5 8l9-23c2-4 4-8 5-13l4-10 1-1c1-1 2-4 2-5 1-5 3-9 4-14l5-15c2-2 4-5 5-8l2-8c1-1 1-1 1-3 0-1 0-2 1-3v-1z" class="F"></path><path d="M618 382l1 2-1 1c0 1 0 5 1 7h-1 0 0-1c0 2-1 5 1 7 0 1 1 3 1 5l-4-4-1-1v1l-1 1v3l1 1-1 1 1 1h0c0 4 5 11 9 13 5 3 16 1 22 0 2 0 5-1 7-1-3 2-6 2-9 2-10 2-20 4-29 3-2 0-3-1-3 1 0 1 0 1 1 2 0 1 0 3-1 4v3l-1-3v-6c-1-6-1-12 0-18 0-8 5-17 8-25z" class="K"></path><path d="M610 407c0 5 0 10 1 15 0 1 0 1-1 3 0 2 1 4 0 6v-6c-1-6-1-12 0-18z" class="C"></path><path d="M419 194h1c1 1 2 2 2 4h-1l-10 13c-7 10-10 22-13 34h0 0c-1-5-2-10-1-16s2-11 4-17c2-5 10-14 15-15 1-1 2-1 3-2v-1z" class="G"></path><path d="M376 275h0c3-1 6-7 8-10 1 1 1 1 0 2-2 4-5 8-8 11h0c-6 6-14 12-21 15-4 1-9 2-14 3s-12 2-18 1c-1 0-2 0-3-1v-1c-2-1-3-2-4-3h2l5 1c17 1 37-6 50-18h3z" class="C"></path><path d="M320 295c2 0 5 1 7 1v1h-4c-1 0-2 0-3-1v-1z" class="E"></path><path d="M332 295c5-1 9-3 14-1-1 0-3 1-5 1h-9z" class="D"></path><path d="M332 295h9v1c-5 1-12 2-18 1h4v-1c2 0 4 0 5-1z" class="N"></path><path d="M376 278h0c-6 6-14 12-21 15-4 1-9 2-14 3v-1c2 0 4-1 5-1 8-3 16-7 22-11 3-1 5-3 7-5h1z" class="J"></path><path d="M647 443c0-1 0-3 1-4l1 1 2-2c1 0 1-1 2-1l4-2 2-1c1 0 1-1 2-1 0-1 1-2 2-2 1-1 4-3 5-5l3-3c1-2-1 0 0-1l2-2 2-2c1-2 2-3 2-4 1-2 2-3 2-5h-3l1-1c2-1 4-3 5-5l2-2h0l1 1-1 1v2c0 1-1 2-1 2-1 2-2 4-2 5-1 3-2 5-3 7-2 2-3 2-3 5-1 0-1 1-1 2l-1 2h0c-1 2-2 4-2 6-1 1-1 1-2 1-2 2-2 8-4 9-1 1-2 1-3 1l-5-1c-3-1-7-1-10-1z" class="K"></path><path d="M392 440h1 0c2-2 4-2 7-2l1 1h2c1 1 3 2 4 3s1 0 1 2h1c1 1 1 3 1 4l-1 1h1l1-1 1-1v1c1 0 2 0 2 1h2l14 5c1 0 2 1 3 1 2 0 3 1 4 2l12 4c1 1 2 1 2 2-3 0-5 0-8-1s-8-3-11-2l-2-1c-1-1-2-1-3-1s-2 0-3-1h-1l-2-1h-2l-2-1h-1l-2-1h-4c-1-1 0-1-1-1-2 0-1-1-3-1-1 1-2 2-3 2h-1c0-1 0-2 1-2v-1c1 0 1-1 2-2-1-1-2-1-3-2h-1c-1-1-2-1-3-2h-1-1-1c0-2-2-3-3-5z" class="Q"></path><path d="M241 189h156v1c0 1-1 2-2 3l-1-1h-1-3-11l1 1-1 1-2-2c-1 0-2-1-3-2h-4c-4-1-8 0-11 0h-20 0l1 2c-1 0-1-1-2-1-1-1-3-1-4-1h-13-1c-6-1-13 0-20 0h-4v1l-1 3c1 0 1 0 0 1v2c0 2 0 2-1 4v-4c1-1 0-3 1-5-5-1-11 0-15 0-1 1-1 2-2 2l-1-1c-1-1-5-1-7-1h-12c-5 0-8 0-12-2h-1c-1 0-2 0-4-1z" class="M"></path><path d="M246 190h33v1c0 1 0 1 1 1-1 1-1 2-2 2l-1-1c-1-1-5-1-7-1h-12c-5 0-8 0-12-2z" class="B"></path><path d="M337 364c4 4 9 8 15 9h1c0-1 0-1-1-1-2-2-4-3-5-5h0c1-1 6 4 9 5 2 1 5 1 7 1 8 1 16 0 22-6 4-4 6-10 6-16v-3l-1-1c-1-7-5-12-9-18 7 5 12 14 13 22 0 3 0 5-1 8v1c-1 5-5 9-9 12-7 4-15 5-23 4l-3-1-1 1h-5c-6-2-11-5-15-10l2 4c2 3 4 5 6 7h0c-3-2-6-4-8-7h0v2h0c-1-2-3-3-3-6h-1c0 2 0 3 1 4 0 1 1 2 1 3 2 7 5 14 10 19v1c-2-1-3-3-4-3-3-5-4-11-6-16-2-3-3-6-4-9v-1c0-2-1-5-2-7l1-1c1 1 1 3 2 4 1 2 3 4 4 5l1-1z" class="F"></path><defs><linearGradient id="BO" x1="587.06" y1="685.911" x2="608.228" y2="687.708" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#858485"></stop></linearGradient></defs><path fill="url(#BO)" d="M585 693c0-2 9-22 10-26l1 1-3 9v1h2c-1 2-1 2-1 4l7-6 2 1-1 1 1 1c1 0 1 1 2 1l1-1h1c1 0 2-1 4-2v1l-1 1h-1l-1 1c-1 0-3 1-4 2-1 0-1 0-2 1v1c1 0 1 0 2-1 3 0 4 0 6 2l-3 1c1 1 2 1 3 1-2 2-3 2-4 4l-6 4c-4 2-9 4-12 8l-3 3-1-1h-1 0l2-3h1l-1-1c1-1 2-3 3-4l-2-2c0-1 0-1-1-2z"></path><path d="M593 677v1h2c-1 2-1 2-1 4h0c-1 2-3 3-5 5l4-10z" class="L"></path><defs><linearGradient id="BP" x1="599.67" y1="698.436" x2="600.33" y2="686.064" xlink:href="#B"><stop offset="0" stop-color="#a1a0a3"></stop><stop offset="1" stop-color="#bebcbb"></stop></linearGradient></defs><path fill="url(#BP)" d="M607 686c1 1 2 1 3 1-2 2-3 2-4 4l-6 4c-4 2-9 4-12 8l-3 3-1-1h-1 0l2-3h1l-1-1c1-1 2-3 3-4s1-2 2-3c2 0 4-1 6-1l11-7z"></path><path d="M588 697c1-1 1-2 2-3 2 0 4-1 6-1l-12 12h-1 0l2-3h1l-1-1c1-1 2-3 3-4z" class="P"></path><path d="M190 189c3-1 7-1 10 0 17 1 31 8 45 17l19 16c0 2 0 4-1 6-2-5-6-8-11-11-8-6-16-12-25-16-4-2-8-4-12-5-2 0-5-1-6-1-5-1-10-2-15-2h-1c-1-1-2-3-3-4z" class="F"></path><path d="M648 190c3-2 9-1 13-1h25v1h0l-2 2v1c0 1-1 1-1 2v1c-1 0-1 1-1 1 0 1 0 2-1 2h-1l1-1v-3-1c-4 0-7 1-10 2l-1 6c0 1 1 3 0 5l-1-6v-1l-1-2v-1h-1c-1 1-2 1-3 2-2 0-4 0-5 1l-1 2 4 12c1 2 4 8 3 9-2-1-3-6-4-9 0 0-1-1-1-2v1 4 2c1 1 2 5 1 7h0c-1-1-1-3-1-4l-3-12-1-4c-1-1-1-2-2-3l-2-6-4-7z" class="M"></path><path d="M686 190h0l-2 2v1c0 1-1 1-1 2v1c-1 0-1 1-1 1 0 1 0 2-1 2h-1l1-1v-3-1c-4 0-7 1-10 2l-1 6c0-2-1-6 0-8v-4h16z" class="D"></path><path d="M658 202l-2-9 13-1v8l-1-2v-1h-1c-1 1-2 1-3 2-2 0-4 0-5 1l-1 2zm-33-9l13 17c6 11 10 23 10 35v8l-2 1c-2-1-2-2-2-4-3-13-6-24-12-36-3-5-6-9-9-14l2-7z" class="O"></path><path d="M693 422h1c0 3-1 8 0 11 1 7-1 15 3 21 0 4 0 9-1 13-2-2-2-3-2-5l-2-4 1 7-8-4c-2-2-4-2-6-3-1-1-1-1-2-1l11-28c1 1 1 2 2 3 1-3 2-7 3-10z" class="V"></path><path d="M693 422h1c0 3-1 8 0 11 1 7-1 15 3 21 0 4 0 9-1 13-2-2-2-3-2-5l-2-4h0c-2-8-3-17-2-26 1-3 2-7 3-10z" class="L"></path><path d="M515 796c1 4 0 9 1 13v-2c1-1 1 0 1-1 2-4 3-9 5-13s5-7 7-11c2-3 3-7 5-10l10-16c-1 5-3 9-5 13l-9 23c0 2-1 4-2 6-2 2-4 5-6 8 0 1-1 3-2 5-1 3-3 7-4 11 0 1 0 2-1 3h-1c-1-4-3-7-4-10-1-4-3-7-4-10-3-6-5-13-9-19-2-3-3-8-5-12-1-3-3-6-3-8 1 1 2 4 3 5h1v1c7 10 12 20 16 30 1 3 3 6 3 9 0 1 0 1 1 2v-2c0-2 0-2 1-3 0-4-1-9 0-12h1z" class="E"></path><path d="M755 217h1v8c-1 4-3 7-4 10l-6 15-35 86h-1c1-3 3-6 4-9l6-16 9-22 4-10a120.94 120.94 0 0 1-16 16c-5 4-10 6-15 11l1-2c2-2 4-4 7-6 1-1 7-4 8-6l-3 2-1 1c-3 1-4 2-6 4-6 4-16 3-23 1h-1c-3-1-7-2-9-5-2-1-3-2-5-3s-6-5-7-7l-3-3v-2c-1-2-1-3-1-4l6 4c2 2 5 4 7 6 1 2 3 4 5 5h0l4 2 1 1c1 0 1 0 2 1 12 2 23 0 34-4l-1-1h-1 0l2-1s1 0 1-1h1c1-1 1-1 3-2s5-5 7-8l-1-1 6-6h1c2-2 4-9 5-12 1-2 3-5 3-8l1-1-1-1c1-2 2-3 3-6v-1c2-4 5-7 6-11v-1l1-2h0v-3l1-1v-7z" class="N"></path><path d="M729 277l6-6h1l-5 9c-2 4-8 9-12 11h-1l-1-1h-1 0l2-1s1 0 1-1h1c1-1 1-1 3-2s5-5 7-8l-1-1z" class="Q"></path><path d="M659 276l6 4c2 2 5 4 7 6 1 2 3 4 5 5h0l4 2 1 1c1 0 1 0 2 1h0c2 1 3 1 5 2h1c5 0 14 1 18-1l2-1c-1 2-2 2-3 3-4 2-10 3-15 2-1 0-3 0-4-1-3 0-5-1-8-2-1 0-3-1-5-2s-3-2-5-3-6-5-7-7l-3-3v-2c-1-2-1-3-1-4z" class="H"></path><path d="M245 194l-1-1c-1-1-1 0-2-1s-3-2-4-3h3c2 1 3 1 4 1h1c4 2 7 2 12 2h12c2 0 6 0 7 1l1 1c-1 3-3 9-1 12v1 4l3 13h1l1 3 1 5h1l1 4s0 1 1 1v2l1 1h-1 0l-1-2c-1-1-1-1-1-2-1-1-1-2-2-2 0 2 1 3 2 5s1 4 1 5c3 7 5 13 9 19 0 1 2 3 1 4v-1l-1-1c0-1-1-1-1-2-1-1-1-2-1-2l-1-2-2-3c0 4 3 7 4 11l-2-3h0c-3-5-5-10-7-15-4-10-8-19-9-30-1-6-11-11-16-15l-3-2c0-1-1-1-2-2-1 0 0 0-1-1-1 0-2-1-2-1l-2-2c-1-1-2-1-4-2z" class="G"></path><path d="M245 194l-1-1c-1-1-1 0-2-1s-3-2-4-3h3c2 1 3 1 4 1h1c4 2 7 2 12 2h12c2 0 6 0 7 1h-6c-2 1-4 0-6 0h-12c-2-1-4-1-6-1h-1v1c0 1 0 0-1 1z" class="C"></path><defs><linearGradient id="BQ" x1="799.966" y1="195.887" x2="805.357" y2="211.192" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#BQ)" d="M768 228c-1-2-1-5-2-7 17-15 35-29 58-33 6-1 12-1 18-2-2 3-4 5-6 7h1s1 0 2-1v1 3h-16v1h-1c0 1 0 1-1 2 0 1-1 2-2 2-1 1-2 1-2 2 1 2 2 7 4 9h0v2l-3-5c-2 0-6-1-8 0h0c-2-3-4-6-6-8-10 4-17 10-25 16-4 3-8 6-11 11z"></path><path d="M813 197c6-2 16-7 23-4h1s1 0 2-1v1 3h-16v1h-1c0 1 0 1-1 2 0 1-1 2-2 2-1 1-2 1-2 2-1-1-1-1-1-2-1-2-1-2-3-4h0z" class="Y"></path><defs><linearGradient id="BR" x1="809.143" y1="197.745" x2="809.808" y2="205.516" xlink:href="#B"><stop offset="0" stop-color="#424243"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#BR)" d="M813 197h0c2 2 2 2 3 4 0 1 0 1 1 2 1 2 2 7 4 9h0v2l-3-5c-2 0-6-1-8 0h0c-2-3-4-6-6-8h0l9-4z"></path><path d="M813 197c2 2 2 2 3 4 0 1 0 1 1 2 1 2 2 7 4 9h0v2l-3-5c-1-2-2-5-3-7-1-1-2-1-2-3v-2z" class="a"></path><path d="M546 739l9-13c-1 5-3 9-4 14 0 1-1 4-2 5l-1 1-4 10-10 16c-2 3-3 7-5 10-2 4-5 7-7 11s-3 9-5 13c0 1 0 0-1 1v2c-1-4 0-9-1-13h-1c0-3 0-6-1-8v-1l-2-5c1 0 1 1 2 2 0 1 1 2 1 3 1 0 1-1 2-2-1-1-2-3-2-5h1v1c1 0 1-1 1-2s0-2 1-4c1-5 5-10 9-14 2-3 4-5 6-8l1-2h1c-1 2-2 3-2 5l1-1 10-14h1c0-1 2-1 2-2z" class="G"></path><path d="M537 756c0-2 2-4 3-6 0-1 1-3 3-4h1c-2 4-4 7-7 10z" class="B"></path><path d="M516 785c0-1 0-3 1-4v2h1c0-2 0-1 1-2-3 5-4 9-4 15h-1c0-3 0-6-1-8v-1l-2-5c1 0 1 1 2 2 0 1 1 2 1 3 1 0 1-1 2-2zm30-46l9-13c-1 5-3 9-4 14 0 1-1 4-2 5l-1 1c0-2 0-3 1-4v-1h0c1-1 0-1 1-2h0v-1l1-1h0-1l-3 6h0c-2 1-2 2-3 3h-1l3-7z" class="M"></path><path d="M546 739l-3 7c-2 1-3 3-3 4-1 2-3 4-3 6-1 4-4 6-6 9-4 5-7 11-12 16h0c-1 1-1 0-1 2h-1v-2c-1 1-1 3-1 4-1-1-2-3-2-5h1v1c1 0 1-1 1-2s0-2 1-4c1-5 5-10 9-14 2-3 4-5 6-8l1-2h1c-1 2-2 3-2 5l1-1 10-14h1c0-1 2-1 2-2z" class="O"></path><path d="M387 212l1-5c1-2 1-3 3-4-1 1-1 3-1 4l-4 13c-1 1-1 2-1 3 1 1 0 4 0 6-2 9-1 20 0 29h-1c0 5-2 9-5 13-1 1-2 3-3 4h-3c-13 12-33 19-50 18l-5-1h-2c-5-2-12-6-16-10-1-1-3-3-3-5 3 3 5 6 9 8 8 5 19 4 28 2l-1-2c5-1 10-5 15-9 2 0 3-2 6-3h1c3-2 6-5 9-7 7-9 13-20 17-31l4-16 1-4c0-1 0-2 1-3z" class="W"></path><path d="M378 269l1 2c-1 1-2 3-3 4h-3l5-6z" class="K"></path><path d="M370 266h1 0l-3 3c-4 4-7 8-12 11h-2c5-5 11-10 16-14z" class="H"></path><path d="M381 254h0l2-5 1 9c0 5-2 9-5 13l-1-2 3-5c1-1 1-2 1-3v-5s0-1-1-2z" class="Q"></path><path d="M387 212l1-5c1-2 1-3 3-4-1 1-1 3-1 4l-4 13c-1 1-1 2-1 3 1 1 0 4 0 6-2 9-1 20 0 29h-1l-1-9-2 5h0c-1 3-3 5-5 7-1 1-3 4-4 4 3-3 6-6 8-9 3-6 2-14 3-20 0-3 0-6 1-8v-1h-1v2 1c-1 2-1 3-2 5h0l4-16 1-4c0-1 0-2 1-3z" class="B"></path><path d="M381 235h0c-3 12-10 24-18 33-3 3-6 5-9 7-4 3-7 6-11 8-3 2-6 3-9 4l-1-2c5-1 10-5 15-9 2 0 3-2 6-3h1c3-2 6-5 9-7 7-9 13-20 17-31z" class="J"></path><defs><linearGradient id="BS" x1="449.027" y1="652.874" x2="463.734" y2="648.222" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#ecebec"></stop></linearGradient></defs><path fill="url(#BS)" d="M446 658l1-1-1-1c0-1-1-3-2-4l-3-10-2-4c0-1-1-2-1-3l-1-3c-1-2-2-3-2-5-1-2-3-5-3-8 1 1 1 0 2 1v-2l-1-1 1-1 1 1c4 2 6 4 8 6 0-2-2-4-4-6h2c2 1 4 2 7 3 0 1 1 1 2 2 1 0 2 1 2 1l2 1v1c1 1 2 1 2 2h0l4 5c0 2 0 2 1 4 3 12 3 24 3 38-1 5-1 12-3 18-1-1-2-2-2-3v-1c-1-1-1-1-1-2l-3-6v-1c-1-3-2-5-3-8 0-1 0-1-1-2 0-1 0-1-1-1v-1-2h0l-1-1v1c-2-2-3-5-3-7z"></path><path d="M457 651c1 1 1 3 2 5l-1-1c0 1 0 2-1 3v-2h0v-1l-1-1c0-1 0-2 1-3z" class="C"></path><path d="M457 651v-2h0v-1-1c-1-1-1-2-2-2-1-2-1-3-1-4 4 5 6 12 7 18h0 0c-1-1-1-2-1-2-1 0 0-1-1 0v-1c-1-2-1-4-2-5z" class="O"></path><path d="M446 658l1-1-1-1c0-1-1-3-2-4l-3-10-2-4c0-1-1-2-1-3l-1-3c-1-2-2-3-2-5-1-2-3-5-3-8 1 1 1 0 2 1v-2l-1-1 1-1 1 1c4 2 6 4 8 6 1 1 1 1 1 2l2 2c0 1 1 2 1 2v1c1 2 2 4 2 5 1 1 1 2 1 2 0 1 0 2 1 3 0 1 0 2 1 3h1v3c0 1 0 1 1 2h0c0 3 0 5 1 7v6c0 2-1 5 0 7v1c0 2 0 4 1 6v2 1 1l-1 1v-1l-3-8c0-1 0-1-1-2 0-1 0-1-1-1v-1-2h0l-1-1v1c-2-2-3-5-3-7z" class="E"></path><path d="M453 668c-2-3-3-7-4-10 0-1-1-3-2-5l-8-21 4 5v2h0v1c1 2 1 3 2 5l3 6c2 3 2 5 3 8s2 5 2 9z" class="D"></path><path d="M443 637c3 2 5 4 7 7h0l1-1h0c1 2 1 3 1 5 1 1 1 3 1 4v16 1-1c0-4-1-6-2-9s-1-5-3-8l-3-6c-1-2-1-3-2-5v-1h0v-2z" class="C"></path><path d="M451 643c-3-3-5-6-7-9-1-2-4-3-4-5-1-3-3-4-4-6-1-1 0-2 0-3v-1l-1-1v-1c4 2 6 4 8 6 1 1 1 1 1 2l2 2c0 1 1 2 1 2v1c1 2 2 4 2 5 1 1 1 2 1 2 0 1 0 2 1 3 0 1 0 2 1 3h-1 0z" class="G"></path><path d="M311 387c3 1 8 3 10 6 2 1 4 3 6 4h3l10 26 13 33-1 1-2 1-2 1c-4 1-8 4-12 5-1 1-2 1-3 3h-1c-1-5 1-9 1-14h0c-1-1-1-2-1-3l1-1c0-3 0-7-2-9l1-1c-4-10-9-19-17-26-5-4-11-8-17-11l2-3h1c5-2 10 2 15 5-3-5-5-10-6-16l1-1z" class="E"></path><path d="M334 437c1 4 2 10 0 15l-1 1h0c-1-1-1-2-1-3l1-1c0-3 0-7-2-9l1-1h0c1 1 1 1 1 2 0 2 0 3 1 4v-3c-1-1 0-4 0-5z" class="D"></path><path d="M336 464h-2c-1-2 0-7 1-9 1-3 1-5 1-8h1v4l1 1c0 3-3 10-2 12z" class="J"></path><path d="M330 397l10 26c-1 1-1 2-1 3s0 1-1 2v5c1 1 1 2 1 3h0-1c-1-1-1-4-1-5l-5-21c0-2 0-3-1-5-1-3-2-5-4-8h3z" class="b"></path><defs><linearGradient id="BT" x1="346.729" y1="457.954" x2="332.458" y2="430.408" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#3d3d3e"></stop></linearGradient></defs><path fill="url(#BT)" d="M339 426c0-1 0-2 1-3l13 33-1 1-2 1-2 1c-4 1-8 4-12 5h0c-1-2 2-9 2-12 1-5 1-11 0-16h1 0c0-1 0-2-1-3v-5c1-1 1-1 1-2z"></path><path d="M342 441c2 2 3 2 3 5h1v3l1 2c-1 1-1 1-2 1s-2 1-3 2c0-4 1-8 0-13z" class="P"></path><path d="M339 436h0c0-1 0-2-1-3v-5c1-1 1-1 1-2 1 5 3 11 3 15 1 5 0 9 0 13-2 3-3 7-5 10 0-3 1-7 2-11 1-5 1-11 0-17z" class="L"></path><path d="M311 387c3 1 8 3 10 6l13 44c0 1-1 4 0 5v3c-1-1-1-2-1-4 0-1 0-1-1-2h0c-4-10-9-19-17-26-5-4-11-8-17-11l2-3h1c5-2 10 2 15 5-3-5-5-10-6-16l1-1z" class="W"></path><defs><linearGradient id="BU" x1="315.598" y1="369.169" x2="267.985" y2="421.16" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#BU)" d="M308 342l14 34c-4 5-6 8-11 10v1l-1 1c1 6 3 11 6 16-5-3-10-7-15-5h-1l-2 3-3 3v1l3 2c-2 1-2 2-3 4-1 4-4 8-7 11l-6 3h-2s0 1-1 1c-1 1-2 1-2 1l-1 1c0-1 0-1 1-2 1-2 2-3 2-5v-1l2-7 3-6v-2l1-1v-1l-23 18h-2c3-3 7-5 9-8l1-1c2-2 3-4 5-6 3-3 7-6 10-9 4-4 7-10 11-14 2-2 4-3 5-5v-1c1-2 1-4 1-5v-3c-1-1-1-1-1-2 1-1 1-2 1-3h-1c1-3 2-5 1-8v-2h0c-1 0-2-1-3-2l1-1 1-1c1-2 3-5 5-7l2-2z"></path><path d="M295 406l3 2c-2 1-2 2-3 4v-6z" class="I"></path><path d="M308 342l14 34c-4 5-6 8-11 10v1l-1 1c1 6 3 11 6 16-5-3-10-7-15-5h-1-1c3-2 6-2 9-1 1 0 2 1 3 0h0c-2-4-2-10-1-13h2v-1c1-1 1-3 0-4v-1c0-2-2-3-3-4l-1 1c0-2 2-8 4-11-1-1-7-5-8-6 0-1 0-3-1-5h-1c0-1-1-2-1-3 1-2 3-5 5-7l2-2z" class="b"></path><defs><linearGradient id="BV" x1="299.733" y1="311.226" x2="280.142" y2="342.44" xlink:href="#B"><stop offset="0" stop-color="#464545"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#BV)" d="M280 296h3v-1c2 0 3 0 4-1l2-1 5 14c1 0 2 3 2 3l5 14c0 1 1 2 1 3l3 7 3 8-2 2c-2 2-4 5-5 7l-1 1-3-1c-3 2-5 5-7 8l-1-3h-2c-1 2-3 3-4 5h-1l-1-1c0-1 1-2 1-3h0-3-2v-4l-1-4c1-3 0-8 1-12v-6-5c1-3 1-6 1-9l-2-3c-2-1-3-1-4-1-2 0-3-2-5-2-1-1-2 0-3 0v-1c2-1 4-4 5-5 4-3 8-6 11-9z"></path><path d="M287 349c1-2 1-4 1-5 1-3 1-5 1-7l1-1c-1 4-1 8-2 11v3h1v2l4-3c-2 2-5 4-6 7-1 2-3 3-4 5h-1l-1-1c0-1 1-2 1-3h3c1-2 2-5 2-8z" class="X"></path><defs><linearGradient id="BW" x1="290.264" y1="333.448" x2="284.736" y2="335.552" xlink:href="#B"><stop offset="0" stop-color="#646364"></stop><stop offset="1" stop-color="#777876"></stop></linearGradient></defs><path fill="url(#BW)" d="M288 323v3h1v-4l1 1c0 2-1 6 0 8 0 1 1 1 1 1 0 1-1 4-1 4l-1 1c0 2 0 4-1 7 0 1 0 3-1 5-1-2-1-4-1-6 1-6 1-13 2-20z"></path><path d="M302 327l3 7c-3 5-5 9-9 12l-3 3-4 3v-2c4-4 9-10 11-15 1-2 1-5 2-7v-1z" class="P"></path><defs><linearGradient id="BX" x1="307.366" y1="343.358" x2="286.717" y2="349.616" xlink:href="#B"><stop offset="0" stop-color="#353435"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#BX)" d="M305 334l3 8-2 2c-2 2-4 5-5 7l-1 1-3-1c-3 2-5 5-7 8l-1-3h-2c1-3 4-5 6-7l3-3c4-3 6-7 9-12z"></path><path d="M297 351c3-2 5-7 8-8l1 1c-2 2-4 5-5 7l-1 1-3-1z" class="P"></path><defs><linearGradient id="BY" x1="293.041" y1="321.08" x2="273.284" y2="351.457" xlink:href="#B"><stop offset="0" stop-color="#787878"></stop><stop offset="1" stop-color="#aba9a9"></stop></linearGradient></defs><path fill="url(#BY)" d="M279 354c3-11 2-23 4-35 0-2 1-2 3-3h2v7c-1 7-1 14-2 20 0 2 0 4 1 6 0 3-1 6-2 8h-3 0-3v-3z"></path><path d="M282 357c0-1 1-1 1-2 0 0 1-1 1-2l2-10c0 2 0 4 1 6 0 3-1 6-2 8h-3 0z" class="I"></path><defs><linearGradient id="BZ" x1="270.466" y1="316.499" x2="292.053" y2="336.193" xlink:href="#B"><stop offset="0" stop-color="#5a5a59"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#BZ)" d="M278 317c0-2 0-8 1-9v1h1v-3c1-2 2-2 4-3 1 0 1 0 2 1l1 3c0 2 1 6 1 9h-2c-2 1-3 1-3 3-2 12-1 24-4 35v3h-2v-4l-1-4c1-3 0-8 1-12v-6-5c1-3 1-6 1-9z"></path><path d="M276 349c1-3 0-8 1-12v-6l1 15c1 2 0 6 1 8v3h-2v-4l-1-4z" class="I"></path><defs><linearGradient id="Ba" x1="288.378" y1="293.734" x2="274.761" y2="315.733" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#Ba)" d="M280 296h3v-1c2 0 3 0 4-1l2-1 5 14c1 0 2 3 2 3h-1c0-1-1-2-2-3-1 1-2 2-2 3v1h-1l-3-4-1-3c-1-1-1-1-2-1-2 1-3 1-4 3v3h-1v-1c-1 1-1 7-1 9l-2-3c-2-1-3-1-4-1-2 0-3-2-5-2-1-1-2 0-3 0v-1c2-1 4-4 5-5 4-3 8-6 11-9z"></path><path d="M464 683h0c1-1 1-4 1-5 1-3 1-5 1-7l1 1v2 1c1 2 0 4 1 6v1c1 1 1 1 1 2v2c0 1 0 1 1 2v2c0 1 1 2 1 2v1l1 3c0 1 0 1 1 2l1 3c0 1 0 1 1 2l1 2v1s0 1 1 1c0 2 1 3 1 4l1 4 1 1c2 4 5 8 8 12 2 4 4 10 6 15l1-1c0-1 0-1-1-2v-2h1v-2c1 1 1 2 2 4v2l1 4 1 1c1 4 3 9 6 13 3 6 7 10 8 17l1 3c0 2 1 4 2 5-1 1-1 2-2 2 0-1-1-2-1-3-1-1-1-2-2-2l2 5v1c1 2 1 5 1 8-1 3 0 8 0 12-1 1-1 1-1 3v2c-1-1-1-1-1-2 0-3-2-6-3-9-4-10-9-20-16-30v-1l-6-12c0-2-1-5-2-7 0 0 0-1-1-1-1-3-2-6-1-8l4 6v-1c0-2-2-3-3-5s-3-5-4-7c-2-3-3-7-5-10-2-2-4-6-6-9 0-1 0-2-1-3h-1c-1-3-2-5-3-8h1c0-1 0-2-1-3-1-2-1-3-2-4 0-2 0-7 1-8 0-2 0-5 1-7v-1z" class="C"></path><path d="M483 743l4 6c-2-1-2-2-3-3 0 1 0 1 1 2v4s0-1-1-1c-1-3-2-6-1-8z" class="O"></path><path d="M495 736c1 1 1 2 2 4v2l1 4 1 1c1 4 3 9 6 13 3 6 7 10 8 17l1 3c0 2 1 4 2 5-1 1-1 2-2 2 0-1-1-2-1-3-1-1-1-2-2-2h0l-8-12 1-1 1 2c3 2 5 7 7 10h0c0-3-1-6-3-8 0-1-1-2-2-4 0-2-2-4-2-6-2-4-5-9-6-14-2-3-2-6-3-9l-1-2v-2z" class="B"></path><path d="M513 784c1-1 0-5 0-7h0l1 3c0 2 1 4 2 5-1 1-1 2-2 2 0-1-1-2-1-3z" class="E"></path><path d="M473 720c1 1 1 2 2 3 1 2 3 5 5 6 1 1 2 2 2 3 2 2 4 4 5 6s2 3 3 5l1 2c3 6 7 12 9 18 0 1 1 2 2 3l1 2h-1l-15-19v-1c0-2-2-3-3-5s-3-5-4-7c-2-3-3-7-5-10l-2-6z" class="H"></path><path d="M482 732c2 2 4 4 5 6s2 3 3 5l1 2-2-2c-3-2-6-7-7-11z" class="O"></path><path d="M464 684c0 2 0 3 1 4 0 1 0 1 1 2v2l1 2c0 3 1 5 2 7l9 20c-1-1-2-3-3-4h-1c1 4 3 8 6 12-2-1-4-4-5-6-1-1-1-2-2-3l2 6c-2-2-4-6-6-9 0-1 0-2-1-3h-1c-1-3-2-5-3-8h1c0-1 0-2-1-3-1-2-1-3-2-4 0-2 0-7 1-8 0-2 0-5 1-7z" class="B"></path><path d="M463 691l10 29 2 6c-2-2-4-6-6-9 0-1 0-2-1-3h-1c-1-3-2-5-3-8h1c0-1 0-2-1-3-1-2-1-3-2-4 0-2 0-7 1-8z" class="F"></path><defs><linearGradient id="Bb" x1="304.004" y1="382.326" x2="255.061" y2="391.304" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#e0dede"></stop></linearGradient></defs><path fill="url(#Bb)" d="M290 359c2-3 4-6 7-8l3 1-1 1c1 1 2 2 3 2h0v2c1 3 0 5-1 8h1c0 1 0 2-1 3 0 1 0 1 1 2v3c0 1 0 3-1 5v1c-1 2-3 3-5 5-4 4-7 10-11 14-3 3-7 6-10 9-2 2-3 4-5 6h0l-5 4c-2 0-3-1-5-1h0c0-2 2-5 2-7h0l-1-1c0-1-1-2-2-3l-4-4c-1 0-1 0-1-1 1-1 2-1 3-2h-1c-1-2 3-7 4-9-1-2-2-2-3-3-2 0-3-1-4-2l3-4 7-6 8-5 2-1c1-1 1-2 1-4 1-1 1-3 1-4s0-2 2-3h2 3 0c0 1-1 2-1 3l1 1h1c1-2 3-3 4-5h2l1 3z"></path><path d="M263 400c2-2 2-2 3-4v-1c1 0 1-1 1-1v-1l1-1h0 2v-1c-3 6-5 12-9 17 0-1-1-2-2-3 3-1 3-3 4-5zm36-47c1 1 2 2 3 2h0v2c1 3 0 5-1 8h1c0 1 0 2-1 3 0 1 0 1 1 2v3h-4-1c0-2 0-2 1-4h0c0-2 1-3 1-3l1-2c0-2 0-4-1-6-1 0-1-1-2-2l2-3z" class="S"></path><defs><linearGradient id="Bc" x1="286.531" y1="373.21" x2="280.06" y2="369.559" xlink:href="#B"><stop offset="0" stop-color="#424243"></stop><stop offset="1" stop-color="#706e6f"></stop></linearGradient></defs><path fill="url(#Bc)" d="M290 359c2-3 4-6 7-8l3 1-1 1-2 3c-10 11-19 23-27 35v1h-2 0l-1 1v1s0 1-1 1v1c-1 2-1 2-3 4 5-12 13-23 21-33l1-5-2-1c1-2 3-3 4-5h2l1 3z"></path><path d="M287 356h2l1 3c-2 3-4 5-6 8l1-5-2-1c1-2 3-3 4-5z" class="L"></path><defs><linearGradient id="Bd" x1="288.319" y1="373.058" x2="251.142" y2="390.942" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#b0afaf"></stop></linearGradient></defs><path fill="url(#Bd)" d="M279 357h3 0c0 1-1 2-1 3l1 1h1l2 1-1 5c-8 10-16 21-21 33-1 2-1 4-4 5l-4-4c-1 0-1 0-1-1 1-1 2-1 3-2h-1c-1-2 3-7 4-9-1-2-2-2-3-3-2 0-3-1-4-2l3-4 7-6 8-5 2-1c1-1 1-2 1-4 1-1 1-3 1-4s0-2 2-3h2z"></path><path d="M279 357h3 0c0 1-1 2-1 3l1 1c-1 1-2 3-2 5-1 2-2 3-4 5-3 3-9 5-12 8-3 2-5 6-7 7-2 0-3-1-4-2l3-4 7-6 8-5 2-1c1-1 1-2 1-4 1-1 1-3 1-4s0-2 2-3h2z" class="Z"></path><path d="M456 627c6 5 9 11 13 17 5 8 13 16 21 21 3 2 7 5 10 6 2 0 3 1 5 1l3 1-1 1c-2 0-5-1-7-2-3-1-6-3-8-4 6 5 13 9 19 15l-1 1c-6-6-12-10-19-14-9-7-16-15-22-24-1 2 0 5 0 7 0 10 1 21 4 31 4 12 11 23 17 35 6 13 11 26 19 37 1 3 3 5 5 7 4-4 7-8 10-13 5-7 9-15 14-23 5-11 11-22 14-34l1 1c1-1 1-1 1-2 2-1 3-3 3-5 2-5 4-9 6-14 1 2 0 8 1 11v8l-4 19-5 15-9 13c0 1-2 1-2 2h-1l-10 14-1 1c0-2 1-3 2-5h-1l-1 2c-2 3-4 5-6 8-4 4-8 9-9 14-1 2-1 3-1 4s0 2-1 2v-1h-1l-1-3c-1-7-5-11-8-17-3-4-5-9-6-13l-1-1-1-4v-2c-1-2-1-3-2-4v2h-1v2c1 1 1 1 1 2l-1 1c-2-5-4-11-6-15-3-4-6-8-8-12l-1-1-1-4c0-1-1-2-1-4-1 0-1-1-1-1v-1l-1-2c-1-1-1-1-1-2l-1-3c-1-1-1-1-1-2l-1-3v-1s-1-1-1-2v-2c-1-1-1-1-1-2v-2c0-1 0-1-1-2v-1c-1-2 0-4-1-6v-1-2l-1-1c0 2 0 4-1 7 0 1 0 4-1 5h0c0-4 1-9 1-13l-1 4c0-14 0-26-3-38-1-2-1-2-1-4l-4-5z" class="S"></path><path d="M460 632c1 2 4 5 4 7 2 3 3 4 3 8 1 5 0 10 1 15v5 1c0 1 0 1 1 2l2 8v2c-1 1-1 4-1 6l1 1h0v2c1 1 1 3 1 4 1 2 1 2 0 3l-1-3v-1s-1-1-1-2v-2c-1-1-1-1-1-2v-2c0-1 0-1-1-2v-1c-1-2 0-4-1-6v-1-2l-1-1c0 2 0 4-1 7 0 1 0 4-1 5h0c0-4 1-9 1-13l-1 4c0-14 0-26-3-38-1-2-1-2-1-4z" class="B"></path><path d="M460 632c1 2 4 5 4 7v2c1 2 1 3 1 4s0 2-1 3v-1c0-2 0-3-1-4v-2h0v-1-2c-1-2-1-2-2-2-1-2-1-2-1-4z" class="D"></path><path d="M467 647c1 5 0 10 1 15v5 1c0 1 0 1 1 2l2 8v2c-1 1-1 4-1 6l1 1h0v2c1 1 1 3 1 4 1 2 1 2 0 3l-1-3v-1s-1-1-1-2v-2c-1-1-1-1-1-2v-2c0-1 0-1-1-2v-8l-2-8c0-3 1-5 1-7v-12z" class="F"></path><path d="M471 680c3 12 10 24 16 35 1 3 2 6 4 9 0 1 1 3 2 5 0 1 1 1 1 2v3c1 0 1 1 1 2v2h-1v2c1 1 1 1 1 2l-1 1c-2-5-4-11-6-15-3-4-6-8-8-12l-1-1-1-4c0-1-1-2-1-4-1 0-1-1-1-1v-1l-1-2c-1-1-1-1-1-2l-1-3c-1-1-1-1-1-2 1-1 1-1 0-3 0-1 0-3-1-4v-2h0l-1-1c0-2 0-5 1-6z" class="D"></path><path d="M494 731v1c4 5 14 29 19 31 2 0 3 0 4-1 7-6 12-15 16-23 2 2 1 6 0 9v3l-1 2c-2 3-4 5-6 8-4 4-8 9-9 14-1 2-1 3-1 4s0 2-1 2v-1h-1l-1-3c-1-7-5-11-8-17-3-4-5-9-6-13l-1-1-1-4v-2c-1-2-1-3-2-4 0-1 0-2-1-2v-3z" class="G"></path><path d="M563 673c1 2 0 8 1 11v8l-4 19-5 15-9 13c0 1-2 1-2 2h-1l-10 14-1 1c0-2 1-3 2-5h-1v-3c1-3 2-7 0-9 1-4 4-7 5-10 1-1 1-3 3-4v1 1 1c1-3 2-6 3-8 1-4 4-8 5-13 2-4 3-9 4-13 1-1 1-1 1-2 2-1 3-3 3-5 2-5 4-9 6-14z" class="C"></path><path d="M555 702c1-3 1-5 1-8 1 0 2 2 2 3s-2 5-3 5z" class="T"></path><path d="M558 697c2 5-1 7-2 11-1 2-2 5-3 6l-1 1h0l3-12-4 8c-1-3 2-7 4-9h0c1 0 3-4 3-5zm-25 42c1-4 4-7 5-10 1-1 1-3 3-4v1 1 1 3c-2 7-4 14-7 20h-1v-3c1-3 2-7 0-9z" class="M"></path><path d="M661 226h0c1-2 0-6-1-7v-2-4-1c0 1 1 2 1 2 1 3 2 8 4 9 1 2 2 3 2 5 0 1 0 1 1 1l3 5h1c1 1 1 1 1 2l3 3c1 0 2 1 3 2 0 1 1 1 2 2l1 1 1 1h0c2 6 7 11 12 13 5 3 11 5 17 6 4 1 8 0 12-1 2 0 4 0 5-1 6-2 11-6 15-11 0 3-2 6-3 8-1 3-3 10-5 12h-1l-6 6 1 1c-2 3-5 7-7 8s-2 1-3 2h-1c0 1-1 1-1 1l-2 1h0 1l1 1c-11 4-22 6-34 4-1-1-1-1-2-1l-1-1-4-2h0c-2-1-4-3-5-5-2-2-5-4-7-6l-6-4c0 1 0 2 1 4v2c-1-2-3-3-2-5 0-1 0-2 1-3v-5c2-10 3-20 3-31l-1-1v-6-5z" class="W"></path><path d="M729 277l-1 1c-5 4-16 8-22 7 1 0 2 0 3-1h-5c2 0 5 0 7-1h1c4-1 8-3 12-5s11-7 12-12c0-1 0-1 1-2l1-1c1-2 2-3 3-4-1 3-3 10-5 12h-1l-6 6z" class="C"></path><path d="M661 226h0c1-2 0-6-1-7v-2-4-1c0 1 1 2 1 2 1 3 2 8 4 9 1 2 2 3 2 5 0 1 0 1 1 1l3 5h1c1 1 1 1 1 2l3 3c1 0 2 1 3 2-1 1-1 1-1 2l-1 1v2c0 1 0 1-1 2v3c0 1 0 1-1 2 0 2 0 9 1 11v3c0 1 0 1 1 3v1c0 1 1 2 1 3h1c1 3 4 5 5 8 1 1 2 1 3 2l2 2h0c1 0 2 1 3 1s2 1 3 2h1 2l1 1h2 3l2 1c-1 1-3 1-4 1l-1-1h0-3c-1 0-2 0-3-1h-2c-1 0 0 0-1-1h-3l-2-1-7-2h-3c-1 0-1 0-3-1-1-1-2-1-3-1h0v1l1 1c-2-2-5-4-7-6l-6-4c0 1 0 2 1 4v2c-1-2-3-3-2-5 0-1 0-2 1-3v-5c2-10 3-20 3-31l-1-1v-6-5z" class="Q"></path><path d="M661 226h0c1-2 0-6-1-7v-2-4-1c0 1 1 2 1 2 1 3 2 8 4 9 1 2 2 3 2 5v-1c-2-1-2-1-3-3h0c-1-2-1-3-2-5 0-1 0-1-1-2 0 1 0 2 1 4l1 3h0c1 1 1 2 2 4h0v6c0 4 0 7-1 11 0 3 0 8 1 11s3 6 4 8h0l-3-3-2-3c0 6 0 10 4 15v1h0l-5-6-1 1v3l-2 1c0 1-1 2-1 3s0 2 1 4v2c-1-2-3-3-2-5 0-1 0-2 1-3v-5c2-10 3-20 3-31l-1-1v-6-5z" class="C"></path><path d="M660 273v-1-2c1-1 1-2 1-3 1 0 1 0 2 1l-1 1v3l-2 1zM552 655c1-1 2-3 2-4 4-5 8-13 14-17-2 2-5 5-5 7v4 10 6 3c0 1 0 2-1 3v1 5h1c-2 5-4 9-6 14 0 2-1 4-3 5 0 1 0 1-1 2l-1-1c-3 12-9 23-14 34-5 8-9 16-14 23-3 5-6 9-10 13-2-2-4-4-5-7-8-11-13-24-19-37-6-12-13-23-17-35-3-10-4-21-4-31 0-2-1-5 0-7 6 9 13 17 22 24 7 4 13 8 19 14l1-1c-6-6-13-10-19-15 2 1 5 3 8 4 2 1 5 2 7 2l1-1-3-1c-2 0-3-1-5-1-3-1-7-4-10-6h0l1-1 1-1c1 0 2 1 3 2 2 0 4 1 6 2 3 1 5 1 7 2h5 12l1-1h3c3-1 5-2 9-3l2-2c1-1 1-2 3-2l3-3v-1h1l1 1c1-2 2-3 4-3z" class="K"></path><path d="M532 673h1 0l-1 2h0 1l2-2v1c-7 5-13 11-18 19-2-4-4-7-7-9l1-1c2 2 4 4 6 7 4-7 9-12 15-17z" class="U"></path><path d="M552 693c2-8 7-16 9-25h1v5h1c-2 5-4 9-6 14 0 2-1 4-3 5 0 1 0 1-1 2l-1-1z" class="I"></path><path d="M552 655c1-1 2-3 2-4 4-5 8-13 14-17-2 2-5 5-5 7v4 10 6 3c0 1 0 2-1 3v1h-1c2-8 0-17 0-25-1 2-2 5-3 7-5 10-14 19-23 24v-1l-2 2h-1 0l1-2h0-1c-4 0-7 1-10 1-4 1-10 1-15 0l1-1h17l8-2c2 0 3-1 5-2 5-4 10-9 14-14z" class="T"></path><path d="M511 683c-6-6-13-10-19-15 2 1 5 3 8 4 2 1 5 2 7 2 5 1 11 1 15 0 3 0 6-1 10-1-6 5-11 10-15 17-2-3-4-5-6-7z" class="C"></path><defs><linearGradient id="Be" x1="521.637" y1="663.228" x2="545.863" y2="666.772" xlink:href="#B"><stop offset="0" stop-color="#b3b0b3"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#Be)" d="M513 669h12l1-1h3c3-1 5-2 9-3l2-2c1-1 1-2 3-2l3-3v-1h1l1 1c1-2 2-3 4-3-4 5-9 10-14 14-2 1-3 2-5 2l-8 2h-17l-3-1c-2 0-3-1-5-1-3-1-7-4-10-6h0l1-1 1-1c1 0 2 1 3 2 2 0 4 1 6 2 3 1 5 1 7 2h5z"></path><path d="M502 668c3 1 6 2 9 2-2 1-3 1-5 1l-1 1c-2 0-3-1-5-1h3 0v-1l-1-2z" class="B"></path><path d="M511 670c4 0 8 2 13 2l1 1h-17l-3-1 1-1c2 0 3 0 5-1z" class="H"></path><path d="M490 665h0l1-1 1-1c1 0 2 1 3 2s5 3 7 3l1 2v1h0-3c-3-1-7-4-10-6z" class="C"></path><path d="M484 680c11 10 19 24 28 37h1c1 1 0 2 1 3 0 1 1 1 2 1s1 0 2-1c0-2 0-2-1-4h1 0l1-2c10-9 18-21 26-31-3 7-6 13-10 20-3 5-5 10-7 15-3 8-7 27-13 32-2-1-3-2-3-3-2-4-3-8-4-12l-8-21c-5-11-12-22-16-34z" class="D"></path><path d="M570 549c-1-1-1-3-1-4s-1-2-1-3l-1-1h1 1c1-2 2-3 2-5l1-1h0v-1c0-1 0 0 1-1v-1h1v-1h1v-2l-1-1-1-2-2-5-1-1h-1v-3c-1 0-1-1-2-2v-2c-1-1-1-1-1-2-1-1-1-1-1-2-1-1-1-1-1-2-1-1-2-3-2-4h-1v-1-1s-1 0-1-1l-1-2v-1c1-4 3-8 5-13l7-16 18-42 10-26v13c1 1 1 2 1 3v3c1 1 1 6 1 9h0c0 2 1 3 0 4l-1-1v-3l-1-1c0 1 0 2-1 4 0 0 0 1-1 2l-1 4c-1 1-1 1-1 2-1 3-2 5-1 7v1h0l-1-1h0c-1 1-2 3-2 5h-1v2c0 1-1 2-1 3l-1 1v1c0 2-1 3-1 4h-1v2c0 1-1 3-2 4 0 1-1 3-1 4-1 3-2 5-3 8l-1 1v1c0 1 0 2-1 2v1l-1 2c0 1-1 2-1 3h0c-1 1-1 1-1 3h1l3-3c1-1-1 1 1-1 1-1 1-2 2-2 1-1 2-2 3-2l1-1 4-2c2-1 5-3 8-4l2-1c1-1 3-2 5-2 1 0 2-1 3-1h1l2-1c1 0 2-1 4-1 3-1 8-3 11-5l4-2c1 0 1-1 2-1h1c0-1 1-1 2-2s-1 1 1-1c1-1 2-1 3-2h0c0-2 0-1 1-2l1-1 1-1c0-1 1-1 1-2h0c1-2 1-3 2-4 0-1 0-2 1-3 0-1 0-2 1-3 3 0 7 0 10 1l5 1c1 0 2 0 3 1-1 2-3 4-5 5l1 1v-1c0 1 0 2-1 3v1l-4 14-1 1h-2v1h1c0 1 0 1 1 2-2 1-2 1-4 0h-2l-2-1-1 1c1 1 4 1 6 2 1 0 2 0 2 1v4l-1 2c-1 1-1 2-1 3s0 2-1 4l-2 7c-2 4-4 8-5 12l-1 3v1c-1 1-1 2-1 3-1 1-1 1-1 2l-2 5-1-1c-1 2-1 3-2 5h0-1l-1 1c0-4-1-9-3-13-1-3-3-5-6-6-2-1-3-2-5-3h-1c-5-1-9 0-13 3-2 1-5 4-6 6v1l3 12c1 5 1 10 4 14 2 2 4 6 4 9-3 2-6 1-9 1-5-1-13-3-17 0l-1 1 2 3-1 1-2-2c-3 1-7-1-10-2-1-1-2-1-3-3z" class="H"></path><path d="M577 505c1-3 4-7 7-9h1c0 3-3 5-4 8-1 1-2 3-2 5h-1 0c0-2 1-4 2-5v-1h0l-1-1-2 3h0z" class="M"></path><path d="M577 505h0l2-3 1 1h0v1c-1 1-2 3-2 5h0 1c0 2 1 8 0 9 0 1-1 1-1 3 0-1-1-2-1-2-1-4-2-10 0-14z" class="B"></path><path d="M600 514v1c-1 6 2 14 3 21l-3-3-3-3c2 1 2 2 4 2l1-1c-2-2-2-4-3-6h0v-2c-1-2-1-4-2-6l3-3z" class="C"></path><path d="M580 529v1c-3 2-5 5-7 8h0l4-7c-3-7-7-12-10-19h1c3 5 6 13 12 17h0 0z" class="Z"></path><path d="M597 517c1-4 2-6 5-9 4-4 10-6 15-6 5 1 10 4 13 8 2 2 3 6 4 9l1 7-1 1c0-4-1-9-3-13-1-3-3-5-6-6-2-1-3-2-5-3h-1c-5-1-9 0-13 3-2 1-5 4-6 6l-3 3z" class="S"></path><path d="M600 515l3 12c1 5 1 10 4 14 2 2 4 6 4 9-3 2-6 1-9 1-5-1-13-3-17 0l-1 1 2 3-1 1-2-2c-1-1-1-3-2-4l2 1h0c2-1 3-2 5-2 1-1 3-1 4-1l1-1 1 1 1-1c2 1 4 1 6 2 1 0 4 1 5 0 0 0 1 0 1-1h1c-1-2-1-4-2-6l-3-6c-1-7-4-15-3-21z" class="T"></path><path d="M579 518c1 4 2 7 4 11 4-7 6-13 8-20 1 7 2 15 6 21l3 3-5-2c-4-5-4-10-4-16-3 5-5 10-7 15 2 4 5 6 8 9 1 2 1 5 1 7h0c-1-1-1-3-2-4l-1-1v-1c-1-2-3-4-5-6-2 4-4 9-4 13h0c-1-4 2-9 2-14 0-2-1-2-3-4h0c1-3-1-6-2-8 0-2 1-2 1-3z" class="U"></path><path d="M634 519c1 2 2 3 2 5h0v-5c2-4 4-9 4-13 0-2-1-4-1-6l-1-2v-1-1c-3-5-8-10-13-11h-1c-4-1-7-1-11-1l1-1h2 4 1v-1h-1 2v-1-1h-8c6-1 11 0 17 3l1 1 1 1c3 1 5 5 7 8v1c1 0 1 1 1 3l1 1v2c1 0 1 0 1 2v3c1 1 1 1 1 3l-1 3v1c-1 1-1 2-1 3-1 1-1 1-1 2l-2 5-1-1c-1 2-1 3-2 5h0-1l-1-7z" class="G"></path><defs><linearGradient id="Bf" x1="705.203" y1="192.676" x2="708.56" y2="260.369" xlink:href="#B"><stop offset="0" stop-color="#d9d8d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Bf)" d="M748 189h47l-11 6-12 9c-3 2-6 5-9 7-1 1-2 1-3 2l-4 4h-1v7l-1 1v3h0l-1 2v1c-1 4-4 7-6 11v1c-1 3-2 4-3 6l1 1-1 1c-4 5-9 9-15 11-1 1-3 1-5 1-4 1-8 2-12 1-6-1-12-3-17-6-5-2-10-7-12-13h0l-1-1-1-1c-1-1-2-1-2-2-1-1-2-2-3-2l-3-3c0-1 0-1-1-2h-1l-3-5c-1 0-1 0-1-1 0-2-1-3-2-5 1-1-2-7-3-9l-4-12 1-2c1-1 3-1 5-1 1-1 2-1 3-2h1v1l1 2v1l1 6c1-2 0-4 0-5l1-6c3-1 6-2 10-2v1 3l-1 1h1c1 0 1-1 1-2 0 0 0-1 1-1v-1c0-1 1-1 1-2v-1l2-2h21 6l3 3c1 1 0 1 1 1 1 1 2 3 3 4 0 1 0 1 1 2v-1-1l2-1c0 1 1 3 2 4 0-1 0-1-1-2 0-3-1-4-3-7h-1 7v-1h-2v-1c4 0 8 1 11-1h12z"></path><path d="M724 199c0-3-1-4-3-7h-1 7c3 3 8 7 9 12h-1v1h0s-1-2-2-3h-2c-2-1-4-4-6-6l-1 3z" class="D"></path><path d="M748 189h47l-11 6c0-1 1-2 1-3h-12-6-9c0 1 1 1 1 2v1c-3-3-5-3-9-4-2-1-3-1-5-1l4 4c0 2 1 4 2 6v1 1h0 0c-1 0-1-1-1-1-1-2-1-3-2-4v-1s-1-1-1-2h0l-2-2-1 1c1 1 2 2 2 3l1 1v1l1 1c0 1 0 1 1 2 0 1 1 3 2 4v1h0c1 1 1 8 0 10v1c0 3-1 5-2 7v-1-1-2c1-2 1-4 1-6v-4c-1-2 0-3 0-4s-2-2-2-2c0-2-1-5-2-7-1-1-1-2-2-3h0c-1-1-1-2-2-2-5 0-10 0-14-1 4 4 9 10 10 15 2 7-1 14-4 20-1-1 0-2 0-2 3-7 4-13 2-20h0c-1-5-6-9-9-12v-1h-2v-1c4 0 8 1 11-1h12z" class="M"></path><path d="M683 245c2 1 3 4 4 5 4 5 9 8 15 10-3-3-6-6-8-10-1-3-2-8 0-11 1-2 3-5 6-6 1 0 3 0 4 1s1 2 2 4c-1 2-2 2-4 3 0 1-1 1-2 1s-3-1-4-2h1l-1-1h0-1c-1 4-1 9 1 12 2 5 6 8 11 9 8 2 17 0 24-4 6-4 12-9 16-14v1c-1 3-2 4-3 6l1 1-1 1c-4 5-9 9-15 11-1 1-3 1-5 1-4 1-8 2-12 1-6-1-12-3-17-6-5-2-10-7-12-13z" class="J"></path><path d="M695 239c1-2 3-4 5-5 2 0 3 0 4 1 1 2 0 2 0 4l-2 2c0 1-1 1-2 1s-3-1-4-2h1l-1-1h0-1z" class="c"></path><path d="M747 243c-1 3-2 4-3 6l-4 4c-5 6-17 10-24 10 0-1 7-3 9-3 6-2 15-7 18-13 1-1 3-2 4-4z" class="D"></path><path d="M749 194l-4-4c2 0 3 0 5 1 4 1 6 1 9 4v-1c0-1-1-1-1-2h9 6 12c0 1-1 2-1 3l-12 9c-3 2-6 5-9 7-1 1-2 1-3 2l-4 4h-1-1v-1-2l-1-4v-2c-1 0-1-1-1-1v-1l1-1c-1-1-1-2-2-3h0v-1-1c-1-2-2-4-2-6z" class="C"></path><path d="M767 192h6v3c-1 1-2 2-3 2v-1-1l-3-3z" class="O"></path><path d="M749 194l-1-1h1c2 0 4 1 6 3l2-1c1 2 2 6 4 7 1 0 1 0 1 1 1 0 1 1 1 1 0 1 0 2 1 3h0v1c0 1-1 2-1 3-1 1-2 1-3 2l-4 4h-1-1v-1-2l-1-4v-2c-1 0-1-1-1-1v-1l1-1c-1-1-1-2-2-3h0v-1-1c-1-2-2-4-2-6zm-63-4h21 6l3 3c1 1 0 1 1 1 1 1 2 3 3 4 0 1 0 1 1 2 1 2 2 3 2 5v1c1 1 1 0 1 2v2c1 1 1 2 1 3l1 1v1c-1 1-1 1-1 3h0c0 1 0 1-1 2l-1 4-2 2c0 1-1 1-2 2 0 1-1 2-2 3l-1 1-2 2-1-2 2-1-1-1h1-1-2-1c-1 1-3 1-4 1h-1c-2 0-5 0-6-1h-1-1l-1-2c-1-1-3-2-4-3v1h0l-6-6c-1-1-1-2-2-3h0l-1-1 1-1-2-2v-2c-1-2-1-1-1-2v-2c-1-2-1-6-1-8 1 0 1-1 1-2 0 0 0-1 1-1v-1c0-1 1-1 1-2v-1l2-2z" class="K"></path><path d="M705 205c1 2 2 4 2 6-1 2-3 3-4 4-2 0-3 0-5-1-1 0-2-1-3-3v-2c0 1 1 1 1 1h0l1 1c1 2 2 3 4 3 1-1 1-1 2-1 0-1 0 0 1-2 1 0 0-2 1-3v-3z" class="I"></path><path d="M695 209v-1c0-1 0-2 1-3s2-2 3-2c3 0 4 0 6 2v3c-1-1-3-2-4-2s-3 0-4 1c0 1-1 2-1 3 0 0-1 0-1-1z" class="E"></path><path d="M696 210c0-1 1-2 1-3 1-1 3-1 4-1s3 1 4 2c-1 1 0 3-1 3-1 2-1 1-1 2-1 0-1 0-2 1-2 0-3-1-4-3l-1-1h0z" class="c"></path><path d="M295 194l1-3v-1h4c7 0 14-1 20 0h1 13c1 0 3 0 4 1 1 0 1 1 2 1l-1-2h0 20c3 0 7-1 11 0h4c1 1 2 2 3 2l2 2 1-1-1-1h11 3 1l1 1-4 10h0c-2 1-2 2-3 4l-1 5c-1 1-1 2-1 3l-1 4-4 16c-4 11-10 22-17 31-3 2-6 5-9 7h-1c-3 1-4 3-6 3-5 4-10 8-15 9-8 3-18 2-26-2-6-3-12-9-14-16-1-4-4-7-4-11l2 3 1 2s0 1 1 2c0 1 1 1 1 2l1 1v1c1-1-1-3-1-4-4-6-6-12-9-19 0-1 0-3-1-5s-2-3-2-5c1 0 1 1 2 2 0 1 0 1 1 2l1 2h0 1l-1-1v-2c-1 0-1-1-1-1l-1-4h-1l-1-5-1-3h-1l-3-13v-4-1c-2-3 0-9 1-12 1 0 1-1 2-2 4 0 10-1 15 0-1 2 0 4-1 5v4c1-2 1-2 1-4v-2c1-1 1-1 0-1z" class="Q"></path><path d="M347 218c3 1 5 3 7 6-1 0-2-1-3-1-1-1-2-1-3-1l-4-3c1 0 3 0 4 1l-1-2z" class="I"></path><path d="M344 219c-1 0-3-1-5-2-3-1-6-2-10-4 6 1 14 1 18 5l1 2c-1-1-3-1-4-1z" class="H"></path><path d="M358 260l2-1v-2h1c0 6-3 12-7 16-3 1-4 3-6 3 5-4 7-9 10-16z" class="T"></path><path d="M295 194l1-3v-1h4c7 0 14-1 20 0h-4v2c-1 2-1 7 0 9v2l1 1h-1c-1-2-1-4-2-6 0-1 0-3-1-5-6 0-12-2-18 1z" class="B"></path><defs><linearGradient id="Bg" x1="359.899" y1="239.345" x2="354.858" y2="240.838" xlink:href="#B"><stop offset="0" stop-color="#676566"></stop><stop offset="1" stop-color="#8c8b8c"></stop></linearGradient></defs><path fill="url(#Bg)" d="M348 222c1 0 2 0 3 1 1 0 2 1 3 1 7 10 9 21 7 33h0-1v2l-2 1c1-3 2-7 2-10 0-11-4-20-12-28z"></path><path d="M280 192c4 0 10-1 15 0-1 2 0 4-1 5v4c1 2 1 6 1 8v1c-1-2-2-5-2-6 0-2 1-4 0-5h0c0-2 0-4 1-5v-1h-9 0c-1 1-1 1-2 1l-2 2v4c-2 6 1 12-1 18 0 1 0 2 1 3v3h-1l-3-13v-4-1c-2-3 0-9 1-12 1 0 1-1 2-2z" class="D"></path><path d="M308 210h0c1-1 2-1 4 0h0l2 2 1 1 1 1v1c1 1 1 2 2 3l1 3c-1 0-2 1-3 1l-1 1h-1-1c-1 1-2 1-3 1l-1-1h-1-1v-1c-2 0-3-2-3-4h0l-1-1c0-1 0-2 1-3v-1-1c1-1 3-2 4-2z" class="H"></path><path d="M340 192l-1-2h0 20c3 0 7-1 11 0h4c1 1 2 2 3 2l2 2 1-1-1-1h11 3 1l1 1-4 10h0c-2 1-2 2-3 4l-1 5-1-2v-1c-1-3-7-8-11-10-3-2-6-5-10-6h0c-3-1-6 0-8 0-1 0-2-1-3-1h-9v-1h-1l-1 1 2 1 1 1c2 1 3 3 5 4 1 1 1 0 1 1 1 1 2 2 4 2h0v2l-6-4-2-1-2-2-2-1c-1-1-3-2-4-3z" class="D"></path><path d="M372 470h0l2 2 1 1h2l1 1h0c1 0 2 0 3-1 2 0 4 0 6-1 3-1 5 0 8-1 4-1 9-1 13-1 6 1 12 3 18 5 9 3 19 7 28 11 5 3 10 6 13 10s4 8 3 12c0 3-2 6-3 9 3-3 5-8 6-13v-5c0-1 0-2 1-3 1-3 0 0 1-2v-1l3-4s0-1 1-1v-1l1-1 1-1h1c0-1-1-2-1-4l-1-1v-1c3 6 6 11 8 17v1c0 1 0 2-1 3v1l-1 1c-1 1-4 6-5 6l-3 4c0 1-1 2-1 2l-2 4c0 1 0 2-1 3h0c-1 1-1 0-1 1l-1 5c-1 1-1 1-1 3l1-2h1v1l1 4 1 3 1 1c0 1 1 2 2 2 1 1 1 2 2 3l1 2c0 1 1 1 1 2s1 2 1 3h0c0 1 1 2 1 3 0 6 0 14-1 20 0 3 0 6-2 9 0 1-1 2-1 4l-1 1c0 2-1 3-2 4s-1 2-2 3c0 1-2 3-3 4 0 1-1 2-2 2l-1 1c0 2-3 4-5 5-1 1-1 2-3 2 0 1-1 2-2 2l-1 1c-1 1-2 2-3 2-3 2-6 3-10 3-1 0-2 1-3 1l-1 1h-2c2 2 4 4 4 6-2-2-4-4-8-6l-1-1-1 1 1 1v2c-1-1-1 0-2-1 0 3 2 6 3 8 0 2 1 3 2 5l1 3c0 1 1 2 1 3l2 4 3 10c1 1 2 3 2 4l1 1-1 1c-4-8-7-17-10-25l-15-37-13-34c-1-4-4-9-5-13h0l-7-18-2-4-2-5-1-3-1-4c-1-1-1-1-1-2l-6-15-9-24-1-1-1-3z" class="H"></path><path d="M428 527c1-1 2-1 3-1l3 1v1h-1c-1 0-2 0-2 1h-2l-2 2c0-1 0-3 1-4z" class="T"></path><path d="M474 533l1 3 1 1c0 1 1 2 2 2 1 1 1 2 2 3l1 2c-1 0-2 0-2 1h0c-1-2-3-3-4-4-1-2-2-6-1-8z" class="G"></path><path d="M427 526l1 1c-1 1-1 3-1 4s-2 3-2 4c-2 3-3 6-5 9 0-6 3-14 7-18zm13-16l2-7c3 4 1 9 2 13 0 4 2 7 3 10s3 6 2 8-2 3-2 3h-1c1-2 2-2 2-4 0-1 0-1-1-2v-2c-1-4-3-7-4-10-1-2-1-5-2-7l-1-2z" class="F"></path><path d="M396 485h0l-1 1h1c1-1 2-1 3-2v1c-4 2-11 7-12 12 0 1 0 3 1 4v3l3-4 1 1-2 3c0 1-1 3-1 4 0 2 1 3 0 5l-6-15v-1c1 1 1 1 1 2h1c0-1 1-2 1-3h0c2-5 5-8 10-11z" class="B"></path><path d="M481 544c0 1 1 1 1 2s1 2 1 3h0c0 1 1 2 1 3 0 6 0 14-1 20 0 3 0 6-2 9l-1-1 1-1v-2l1-3c0-2 0-4 1-6v-11c-2 1-3 4-4 5h-1l-1 1-1-1 2-2v-1c0-1 1-1 1-2v-1c1-1 1 0 1-1 0-3 0-7-1-10 0-1 1-1 2-1zm-109-74h0l2 2 1 1h2l1 1h0l-1 1c4 6 7 13 8 20l1 1h0c0 1-1 2-1 3h-1c0-1 0-1-1-2v1l-9-24-1-1-1-3z" class="C"></path><path d="M434 527l1-1c1-2 2-7 1-10-2-4-5-8-9-10h-1c-4-2-10-2-13-1s-6 1-8 3h-1l-7 7v1-1-1c3-4 9-9 14-10 1 0 1 0 2-1h7 0c2 0 3 0 4 1 6 2 11 5 13 10l1 1h0l2-5 1 2c-2 6-2 12-4 17-2 0-4 1-6 0 0-1 1-1 2-1h1v-1z" class="I"></path><path d="M427 526c0-1 2-2 1-3 0-3-1-6-3-8-2-1-2-1-4 0-4 3-6 8-8 12-1-1-1-1-1-2h-1c-2 0-4 3-4 4-1 3-1 6 0 8v1c2 6 9 13 16 16 5 3 13 3 19 1 3-1 6-4 9-6h1c-1 2-8 6-9 7s-2 1-3 1h-1c-4 2-10 1-15-1-7-2-14-10-17-17-2-2-3-6-2-9 2-4 6-9 10-12 2-2 4-3 6-6 1 0 2 0 4 1 4 4 4 9 6 13-1 0-2 0-3 1l-1-1z" class="J"></path><path d="M396 485c4-4 10-6 16-7 4 0 9 0 13 1h1c7 1 16 3 22 9-7-2-13-4-20-5-11-1-19-1-29 2v-1c-1 1-2 1-3 2h-1l1-1h0z" class="G"></path><path d="M411 565l2 2 4 4 2 1 2 2h1c2 1 4 1 6 2h5 1c2 1 3 0 4 1s2 1 3 1h2c3 1 8 1 11 0l5-2c-7 5-17 7-25 5h-3l-1-1c-3 0-6-1-8-2-2 0-3-2-5-2h0v3c-3-3-4-7-5-10-1-2-1-3-1-4z" class="C"></path><path d="M390 504h2c2-1 4-3 6-4-3 4-5 9-4 14l1 1v2h1 0 1c-1 2-2 3-1 5v1l2 6 1 1c1 1 1 2 1 4h0l-1-1c0-1 0-2-1-2 1 3 2 7 4 10l1 2h1c1 1 1 3 2 4v2h0-1c0 1 1 2 1 3h1v1h1c0 2 1 4 1 6l-3-6c-1-2-2-3-3-4h0l-7-18-2-4-2-5-1-3-1-4c-1-1-1-1-1-2 1-2 0-3 0-5 0-1 1-3 1-4z" class="K"></path><path d="M403 549c1 1 2 2 3 4l3 6c2 2 3 4 6 5h0c1 1 2 1 3 1 3 2 6 2 9 3h0c-1 1-5 0-6 0l-2-1c-2 0-2 0-3-1-2-1-4-2-5-3l-1 1 1 1c0 1 0 2 1 4 1 3 2 7 5 10l3 3h0c-1-1-2-2-3-2-1 1 0 3 1 5l2 5 2 4c1 3 3 5 6 6 5 2 12 3 17 1l1 1c-4 1-10 1-14 0-3-1-5-2-8-2 0 1 1 3 2 5 1 3 3 6 6 7 2 1 4 2 6 2 3 1 8 0 11 0 2-1 4-2 6-2-3 2-6 3-10 3-1 0-2 1-3 1l-1 1h-2c2 2 4 4 4 6-2-2-4-4-8-6l-1-1-1 1 1 1v2c-1-1-1 0-2-1 0 3 2 6 3 8 0 2 1 3 2 5l1 3c0 1 1 2 1 3l2 4 3 10c1 1 2 3 2 4l1 1-1 1c-4-8-7-17-10-25l-15-37-13-34c-1-4-4-9-5-13z" class="R"></path><path d="M439 617c-2 0-3-1-5-2-1 0-2-1-3-1h0 4c3 1 7 1 10 1-1 0-2 1-3 1l-1 1h-2z" class="G"></path><path d="M600 514c1-2 4-5 6-6 4-3 8-4 13-3h1c2 1 3 2 5 3 3 1 5 3 6 6 2 4 3 9 3 13-1 1-1 3-2 4l-8 19h2s1-1 1-2h0 1l-5 12c0 2-1 5-2 7l-47 118c0 1 0 2-1 4h-1c-1 0-1 1-1 2l-1 1h0c-1-1-1-2-1-4v1c-1 1-1 2-1 3 0 2 0 2-1 3l-2 8c-1 3-3 6-5 8l4-19v-8c-1-3 0-9-1-11h-1v-5-1c1-1 1-2 1-3v-3-6-10-4c0-2 3-5 5-7-6 4-10 12-14 17 0 1-1 3-2 4-2 0-3 1-4 3l-1-1h-1v1l-3 3c-2 0-2 1-3 2l-2 2c-4 1-6 2-9 3h-3l-1 1h-12 0c0-1 1-1 2-1 3 0 7 1 9 0h3 1l1-1h2l2-1 1-1c1 0 2 0 3-1h1l1-1 4-4c1 0 1-1 2-1v-1c2-2 3-5 4-7l2-5h1v-1l-2 2-1 1v1c-1 1-4 5-6 5h-1c-2 1-3 0-5 0l-1-1c-1 0-3-2-3-4l-2-3v-1l-1-1v-1h0c-2-2-3-5-4-7v-1c-1-2-1-5-1-7h-1c0 4 1 8 1 12l-1 1 1 1c0 1 0 2-1 2v1c-3-2-4-4-8-4-3 0-5 0-7 2 0 1-1 1-1 2l-2 3-3 2c0-2 1-4 1-5 2-4 4-5 7-7 2 0 3 0 4-1-1-4-1-9-2-14 0-2-2-5-2-7v-2c0-2-1-3-1-5-2-4-4-7-5-11v-1l-1-1s0-1-1-1c0-1 0-2-1-3l-4-15c-1-1-1-2-2-3l-3-10c0-1-1-2-1-3l-2-4v-2l-3-8v-2-1h-1c0-1 0-2-1-3v-1c-1-2-1-1-1-2s-1-1-1-2v-1-2h2 2l3 1h1c1 1 2 3 2 5 0 0 1 1 1 2v3h1v2l1 3c0 1 1 2 1 2l1 3v2l5 15c0 1 1 2 1 3l2 5c0 1 0 2 1 2v1l1 1 1 1v1 1h0l1 1v1 2l2 4v1s1 1 1 2c0 0 1 1 1 2s0 1 1 2v1l1 3 1 1v1c0 1 0 0 1 2l1-3 3-5v-1l6-11c0-1 0-2 1-3v-1c1-1 2-3 2-4v-1l2-2c0-1 0-2 1-3 1-2 2-5 3-7v-1h0c1-1 1-1 1-2h0c1-2 1-2 1-3h0v-2l1-1v3h0c0-1 0-2 1-4h1v3-1l1-2h0l1-2v-1c1 0 1-2 1-2 1-3-1 1 1-2v-2c1-2 1-4 2-5l1-1v-3c1-1 1-2 1-3v-1c1-2 1-3 2-5h0c1-2 2-3 3-5h2c0 1 0 1-1 2v1l-1 1v1l-1 2v1c-1 0-1 1-1 2 0 0 0 1-1 2v1c-1 2 0-1 0 1v1h-1v1c0 1 0 2-1 4 0 1-1 2-1 3v1c-1 1-1 3-2 5h0v1l-1 1s-1 1-1 2v1c-1 1-1 2-1 4-1 1 0 1 0 3v-2l2-5h1v-2c0-1 1-3 2-4v-2h0v-1l1-1 2-4v-2c1-2 0 0 1-1 0-1 0-2 1-2v-1c0-2 0-2 1-3 0-2 1-2 0-4 0-1 0 0 1-1h1c0 1 0 3 1 5 1 1 1 1 1 3 1 1 2 1 2 2l3 4 4 7v1l1 2h0c1 1 1 2 2 3v1-4-3c-1-1-1-1-1-3h0c1 2 2 2 3 3 3 1 7 3 10 2l2 2 1-1-2-3 1-1c4-3 12-1 17 0 3 0 6 1 9-1 0-3-2-7-4-9-3-4-3-9-4-14l-3-12v-1z" class="K"></path><path d="M549 569h0c0-3-1-7 1-10v1 9h-1z" class="B"></path><path d="M542 599c1 0 0 0 1 1 0 3 0 6-1 9v-2h-1v1l-1-1c1-3 1-5 2-8zm26 22h0c-2 3-5 5-8 7-1 2-2 3-3 4h-1c2-5 8-9 12-11zm57-113c3 1 5 3 6 6 2 4 3 9 3 13-1 1-1 3-2 4-1-4 0-14-3-18h0c-1-2-2-4-4-5z" class="C"></path><path d="M611 515c0-2 0-4 1-6h0 1v2c1 2 2 4 4 6 3 4 11 12 8 18-1 3-4 6-6 9 1-2 1-3 2-4s3-4 3-5c1-2 1-5 0-6-2-3-7-11-10-12-1 0-2-1-3-2z" class="U"></path><path d="M611 515c1 1 2 2 3 2 3 1 8 9 10 12 1 1 1 4 0 6 0 1-2 4-3 5-1 0-2-1-3-1l-1 1-1-1c3-1 4-2 6-3l-1-1 1-1v1l1 1v-1l1-1c0-5-4-9-7-13-1-1-1 0-2 0l-2-2c-1 2 0 2 0 4 1 0 2 0 2 1 1 1 3 4 3 6-1-1-2-2-2-3s-1-2-2-2l-1-1c-1-3-2-6-2-9z" class="D"></path><path d="M549 569h1c1 13 8 25 19 34l4 4v1h-1c-8-5-17-15-20-24-2-5-3-10-3-15z" class="J"></path><path d="M624 550h2s1-1 1-2h0 1l-5 12c0 2-1 5-2 7h-1v-3l-3 3c-1 0 0 0-1 1-3 2-6 3-9 4h-1s-1 1-2 1h-1 0-1c0-1 1-1 2-1l1-1h1c4-1 6-3 10-6 1 0 1 0 2-1v-1c3-3 4-9 6-13z" class="H"></path><path d="M535 609l-1 2v3c-1 7 0 17 4 22 2 1 3 2 5 1 2-3 3-4 2-7 0-1-1-2-1-3 2 1 2 2 3 4v5c0 2-1 3-3 5h-3c-3-1-5-4-6-6-5-8-3-17 0-26z" class="I"></path><path d="M546 556s-1 1-1 2v1c-1 1-1 2-1 4-1 1 0 1 0 3v-2l2-5h1v-2c0-1 1-3 2-4v-2h0v-1l1-1 2-4v-2c1-2 0 0 1-1 0-1 0-2 1-2v-1c0-2 0-2 1-3 0-2 1-2 0-4 0-1 0 0 1-1h1c0 1 0 3 1 5 1 1 1 1 1 3v-1c-1 0-1-1-2-1v-1c-1 1-1 2-1 3-1 2-2 4-2 6l-5 12h0c0 2-1 2-1 4l-3 5-1 3c0 2-1 3-2 4l-1 4v1l-2 3c0 1 0 1-1 2 0 1 0 2-1 2 0 1 0 2-1 3 0 1 0 2-1 2h0-1l1-1h0c0-3 1-4 1-6l1-1v-2l14-45c1 1 1 1 1 2l-4 13-1 1c0 1-1 2-1 3v1 1z" class="D"></path><path d="M516 621v1c1 1 1 1 1 2l1 1c1 0 1-1 1-2 1-1 1-2 2-3l1-3c1-1 0-1 1-2l3-7c1-2 2-4 3-7 1-2 3-7 5-9v1c-1 3-3 7-5 11-1 3-3 7-5 10-1 3-3 6-4 9v2c1 5-1 9 2 13l2 2 1 1c0 1 0 2-1 2v1c-3-2-4-4-8-4-3 0-5 0-7 2 0 1-1 1-1 2l-2 3-3 2c0-2 1-4 1-5 2-4 4-5 7-7 2 0 3 0 4-1-1-4-1-9-2-14 0-2-2-5-2-7v-2c0-2-1-3-1-5-2-4-4-7-5-11 1 0 1 0 2 1v2c2 3 3 6 4 10l4 10 1 1z" class="N"></path><path d="M520 625c1 5-1 9 2 13-2-1-3-1-4-2 0-4 0-7 2-11z" class="C"></path><path d="M505 597c1 0 1 0 2 1v2c2 3 3 6 4 10l4 10 1 1-1 1h0c1 1 1 2 1 3s0 0 1 1c1 2 0 8 0 10v1c-1-1-1-5-1-7 0-1-1-2-1-3 0-2 0-3-1-5h-1c0-2-2-5-2-7v-2c0-2-1-3-1-5-2-4-4-7-5-11z" class="O"></path><path d="M620 567h1l-47 118h0v-2l2-4v-2c2-5 0-12 0-16 1-6 1-13 3-19l3-6c-3 11-5 20-4 32 2-3 3-7 4-10l10-27c1-3 2-5 3-8 1-2 3-5 5-7-3 0-8 3-11 5-3 3-5 6-8 9 0-2 3-4 4-6 4-4 7-6 11-8-2 0-5 0-8-1-5-1-10-4-15-7v-1l7 4c7 3 14 4 21 1l6-15-6 3c3-2 7-5 8-8l1-1v-1c1-4 3-7 4-11v1l-3 3h-1c-4 4-12 7-18 8h0c4-3 10-3 14-6 2-1 4-3 5-5 2-2 4-4 5-6 2-2 3-5 4-7z" class="I"></path><path d="M607 541c1 0 2 1 3 1s4-2 6-3h0l1 1 1-1c1 0 2 1 3 1-1 1-1 2-2 4-1 3-5 7-8 10-7 7-14 11-25 11-5 0-9-2-13-4-1 1-1 1-1 2v1h0c-1-1-1-1-1-2-1-3-2-4-2-7 1 1 1 2 2 3v1-4-3c-1-1-1-1-1-3h0c1 2 2 2 3 3 3 1 7 3 10 2l2 2 1-1-2-3 1-1c4-3 12-1 17 0 3 0 6 1 9-1 0-3-2-7-4-9z" class="G"></path><path d="M581 630c3-3 5-6 8-9 3-2 8-5 11-5-2 2-4 5-5 7-1 3-2 5-3 8l-10 27c-1 3-2 7-4 10-1-12 1-21 4-32l-3 6c-2 6-2 13-3 19 0 4 2 11 0 16v2l-2 4v2h0c0 1 0 2-1 4h-1c-1 0-1 1-1 2l-1 1h0c-1-1-1-2-1-4v1c-1 1-1 2-1 3 0 2 0 2-1 3l-2 8c-1 3-3 6-5 8l4-19v-8c-1-3 0-9-1-11h-1v-5-1c1-1 1-2 1-3v-3-6-10-4c0-2 3-5 5-7 1-2 3-3 4-4 2-1 5-3 7-4 2 0 4-2 6-2-1 2-4 4-4 6z" class="C"></path><path d="M579 626c2 0 4-2 6-2-1 2-4 4-4 6-2 2-3 4-5 6l-1 2h-1l-1-1c-1 1-2 3-3 4h0c1-2 1-4 2-6 2-3 6-5 7-9z" class="O"></path><path d="M579 642c-2 6-2 13-3 19 0 4 2 11 0 16v2l-2 4v2h0c0 1 0 2-1 4h-1c-1 0-1 1-1 2l-1 1h0c-1-1-1-2-1-4h0c0-2-1-3-1-5 1 0 1 1 1 2s0 1 1 2v-2l1-1c1-4 3-7 4-11l1-2c1-4 0-8 0-12l-1-1c0-3 0-7 1-10 1-1 1-2 1-3 1-1 1-2 2-3z" class="B"></path><path d="M566 658c0-2 0-3 1-5 0-1 0-2 1-3v-1c1-2 1-3 2-5l1 1c-4 10-5 20-4 30 0 2 0 3 1 5v1 2c0 2 1 3 1 5h0v1c-1 1-1 2-1 3 0 2 0 2-1 3l-2 8c-1 3-3 6-5 8l4-19v-8c-1-3 0-9-1-11h-1v-5-1c1-1 1-2 1-3v-3-6l3 3z" class="N"></path><path d="M563 655l3 3c-1 2-1 4-1 6-1 2 0 3-1 5-1 1-1 2-2 4v-5-1c1-1 1-2 1-3v-3-6z" class="B"></path><path d="M563 661c1 2 1 5 1 8-1 1-1 2-2 4v-5-1c1-1 1-2 1-3v-3z" class="R"></path></svg>