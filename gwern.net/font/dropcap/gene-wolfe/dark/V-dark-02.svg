<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="68 46 492 624"><!--oldViewBox="0 0 596 752"--><style>.B{fill:#f4f4f4}.C{fill:#fefefe}.D{fill:#666565}.E{fill:#050505}.F{fill:#464646}.G{fill:#676667}.H{fill:#242425}.I{fill:#323132}.J{fill:#403f3f}.K{fill:#3a393a}.L{fill:#515051}.M{fill:#959394}.N{fill:#cbcaca}.O{fill:#818080}.P{fill:#bdbcbc}.Q{fill:#dededf}.R{fill:#adacac}.S{fill:#121211}.T{fill:#a3a2a2}.U{fill:#5e5e5e}</style><path d="M309 609h0c1 1 1 1 1 2l-1 1h-1c-1-1-1-1-1-2 1-1 1-1 2-1zm128-74h0c2 1 2 2 2 3v1c-1 0-1-1-2-1v-3z" class="B"></path><path d="M103 424c1 0 1 0 2 1v1l-1 1c-1-1-1-1-2-1v-1l1-1z" class="C"></path><path d="M270 555c1-1 1-1 2 0l1 1-2 1v1l-1-1v-2z" class="B"></path><path d="M86 398h1l1 1c-1 1-1 2-2 3l-2-2 1-1 1-1z" class="C"></path><path d="M113 384h2v1l-2 2h-2v-1l2-2zM306 33h1c1 0 1 0 1 1s0 2-1 3c-1 0-1 0-1-1v-3z" class="B"></path><path d="M214 545h1v3l-1 1-1-1v-1c0-1 0-1 1-2z" class="C"></path><path d="M343 579h1v1c1 1 0 2 0 3h-1-1c0-2 0-3 1-4z" class="B"></path><path d="M168 575h1c1 1 1 1 1 2l-1 1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="C"></path><path d="M563 197h2v1l-1 1c-1 0-1 0-2-1v-1h1z" class="B"></path><path d="M375 552h2v2l-2 1h-1l-1-1 2-2zm-29 50c2 0 2 0 2 1 1 1 1 2 0 3h-1c0-1-1-1-1-2v-2zM156 480h1c0 1 0 2-1 2 0 1-1 2-2 2h0v-1c0-2 1-2 2-3z" class="C"></path><path d="M443 479h2c1 1 2 1 2 2l-1 1h-1l-2-2v-1zm-55-14v2h-1c-1 0-2-1-3-2h0 4z" class="B"></path><path d="M392 575h1s0 1 1 1c0 1 0 1-1 2h-1s-1 0-1-1 0-1 1-2zm48-3c1 0 1 0 2 1h0c-1 2-1 2-2 2 0 0 0 1-1 0l-1-1 2-2z" class="C"></path><path d="M115 292c0-1 0-4 1-5v3h0 2l1 1s-1 1-1 2c-1-1-2-1-3-1z" class="P"></path><path d="M145 460h1c1 0 1 0 1 1s-1 1-2 2c-1 0-1 0-2-1l2-2z" class="C"></path><path d="M370 492h1c0 3 0 3-2 5h0l-1-1c1-2 1-3 2-4zm-220-31v1c1 1 0 3-1 5h-1v-3c0-1 1-2 2-3zm247 14c2 1 2 2 3 3 0 1-1 1-1 2h-2c0-2 1-3 0-5z" class="B"></path><path d="M352 550h2c1 1 2 1 2 2l-1 1h-2c-1-1-2-1-2-2l1-1z" class="C"></path><path d="M524 78l1 1c1 1 1 4 0 6h-1c-1-2-1-5 0-7z" class="Q"></path><path d="M290 527h0c0 2-1 4-1 5h-2v-2l3-3z" class="B"></path><path d="M247 590v-1 1c0 2 0 3-2 5 0 0 0-1-1-1v-2c0-1 1-2 3-2z" class="C"></path><path d="M543 144h1c2 1 2 5 3 7l-2-2c-1-1-3-3-2-5zm-1 147h0c1 0 2 1 2 2v3h-2c-1-2-1-4 0-5zm-28 49h0l3 5v1c0 1-1 1-1 1l-1-3c-1-1-1-2-1-4z" class="B"></path><path d="M248 503h1c0 2-1 3-2 4h-3 0c-1-1-1-2 0-3h1 1c1 0 1 0 2-1z" class="C"></path><path d="M455 403c1 3 2 6 4 8l-1 1h-1c-2-1-3-6-4-8 1 0 1 0 2-1zM109 57h1v3c1 2 2 5 1 7l-1-1c-1-2-2-4-2-7 0-1 0-1 1-2z" class="B"></path><path d="M79 414h0l1 1c0 2-2 3-3 5-1 0-1 0-2-1v-1c0-2 2-3 4-4z" class="C"></path><path d="M527 214c1 1 1 2 2 3l3 2c0 1-1 2-2 2l-2-2h0l-3-3 2-2zM400 382c1 0 1 1 1 1 1 2 2 3 3 5v2l-1 1c-2-1-2-5-3-6v-3z" class="B"></path><path d="M459 411v1c-1 2 1 5 1 7h-2c-2-1-2-3-2-5 0 0 1-1 1-2h1l1-1zM97 385h1c0 2 0 2-1 3s-2 1-2 1l-3 3c-1 0-1-1-1-1 1-2 4-4 6-6z" class="C"></path><path d="M57 215c-1 3-1 5-2 7-1 1-1 1-2 1s-1-1-1-1c0-3 3-5 5-7zm492-61c0 1 0 1 1 1 1 1 2 2 3 4l-1 1h-1c0 1-1 0-2 0-1-1 0-4 0-6z" class="B"></path><path d="M216 510h0c1 1-1 5-1 6l-1 1c0 1 0 1-1 2h-1c0-3 2-7 4-9zm4 64h0c0 2-1 4-1 5-1 1-2 2-4 3v-1c0-2 3-5 5-7z" class="C"></path><path d="M119 333h1c-1 2-2 3-2 4-1 2-1 3-2 4h-2 0c-1-3 3-4 4-6 0-1 0-2 1-2z" class="B"></path><path d="M531 209c0 1 0 1 1 2h0c-1 1-1 1-1 2 1 0 2 0 3-1l1-3v3l-2 2h0c-1-1-2 0-3-1h-1c0 2 0 2 1 3 1-1 2-1 2 0 1 1 0 2 0 3l-3-2v-2-2c0-1 1-1 1-1 1-1 1-2 1-3zm-7 39h1c2-1 1-1 2-2 0 3 1 7 2 10h-1v-2c0 1-1 1 0 2v2c-1-1-1-3-3-5h0s1 0 1-1-1-3-2-4z" class="H"></path><path d="M290 584v1c1 1 0 3 0 5 0 3 0 6-1 8h-1-1v-3c2-3 2-7 3-11zM100 332h2c1 0 2 1 2 2s-1 2-2 3h-1c-1-1-2-2-2-3s0-1 1-2z" class="C"></path><path d="M497 98l1 5h-1-4v1h0-1v1h-4l9-7zM147 473h5c1 1 3 2 4 3l-1 1h-1c-2 0-6-2-7-3v-1z" class="B"></path><path d="M85 402h1v1c-1 4-2 6-5 8h-1c0-3 4-7 5-9zm430-86l4 1s1 1 1 2v2s-1 0-1 1c-1 0-2 0-3-1 0-2-1-4-1-5z" class="C"></path><path d="M455 493v1c1 2 2 2 4 4v1c0 1 0 1-1 1h-1c-2-2-3-3-5-4l-1-1h0 0 2l2-2z" class="B"></path><path d="M94 255l-1-1v-1c1-1 2-1 2-1 1-1 1-1 2-1 0-1 1-1 1-2v-3-1h0-2l1 1c0 1-1 1-2 2h0-1l-1-1 1-1c0-1 3-2 4-2 0-1 0-1 1-1v-1l1-1v1h0l1 1c-1 0-1 1-2 1v1c0 1-1 2 0 4-1 1-2 3-4 4 0 0-1 0-1 1v1z" class="I"></path><path d="M240 53c1-1 2-2 3-4h1c-1 1-2 4-2 5 2 1 4-1 5 0h2c-1 1-1 1-1 2v1h-1c-1-1-2-1-4-2l-1 1h-1c-1-1-1-1-1-3z" class="F"></path><path d="M525 253c2 2 2 4 3 5 0 2 1 4 2 5 0 1-1 2-2 2h0c-1-2-3-4-3-6v-6z" class="C"></path><path d="M454 418h0l1 1c2 1 3 1 5 2l2 2-1 1-1 1c-1 0-2 0-3-1-1-2-3-4-3-6zm-36 78v2c1 2 2 3 3 5l2 3-1 1h-1c0 2 0 6-1 7-1-1 0-3 0-4 0-5-1-8-3-13h0c0-1 0-1 1-1z" class="B"></path><path d="M509 64c2-1 2-2 3-3 1 0 2-1 3-1h1v2s1 1 2 1 2 0 2 1h0v3c1 0 1 0 1 1h0 0 1v1h-2c0-1-1-1-1-2h0v-1h0v-2c-1 0-1 0-2-1l-2 2h-1c-1 0-1-1-2-1 1-1 1-1 1-2h0c-2 0-2 1-2 2h-1-1z" class="H"></path><path d="M452 425v-1c3 3 5 7 8 11l1 1h-3c-1-1-4-6-5-8 0-1 0-2-1-3z" class="B"></path><path d="M119 322h5 1v1h-4v-1c0 1-1 1-1 1h-1c0 1-1 2-1 3s0 1-1 1v1h0-1 0c0-1-1-1-1-1-1 0-1 1-2 2 0 0 0 1-1 1h0v-2c0-2 2-3 3-4 2-1 3-1 4-2z" class="I"></path><path d="M150 42l1 1v5 8c0 1 0 2-1 3v-1c0-1 0-3-1-4 0-4-1-8 1-12z" class="B"></path><path d="M383 490h0l3 9c1 0 2 1 2 2 1 1 0 2 0 3h-1c-1 1-1 1-2 0s-1-3-1-4c0-4-1-7-1-10z" class="C"></path><path d="M85 200l-1 1h0c1-2 1-4 1-6h0c1-1 0-1 0-2h0c-1 0-1-1-1-2l-2-2v-1c-1-2-3-3-5-5h0c-1 1-1 1-2 1v-1-1c1 0 2-1 2-1 1-1 2-1 3-1 0-1-1-1 0-1v1h1l2-3-1 3h0c-1 1-1 1-2 1 0 1-1 1-2 2l4 4s1 1 1 2l1 1v1c1 1 2 1 2 2 1 1 0 4 0 5s-1 2-1 2z" class="H"></path><path d="M524 235h1 0c1 3 6 6 8 7 1 1 2 1 2 3 0 0-1 1-2 1-3-1-5-3-6-6l-3-4v-1zm1 32h1c1 4 1 7 3 11 1 0 1 1 1 2-1 1-1 1-2 1-1 1-2 0-3 0-2-3 1-7 1-11-1-1-1-2-1-3z" class="B"></path><path d="M213 55h3c2-1 3-2 5-2 0 1-1 2-1 4h1 2c0 1 0 1 1 1h0l1-1 1-1c1 0 2-1 2-1h2 0c1 0 1 0 1-1h3c0-1 0-1 1-1h2v1c-2 0-3 0-4 1h-1l-1 1h-2c-1-1-4 2-4 3h-2 0c0-1 0-1-1-1h0c-1 0-2 1-3 0s-5-1-5-2l-1-1z" class="H"></path><path d="M166 381l1 1c-1 1-1 3-2 5 0 2 0 6-2 8h-1c-1 0-1-1-1-2 0-2 1-4 2-5 0-2 0-3 1-4 0-1 1-2 2-3z" class="C"></path><path d="M527 246v-1h1c2 3 4 5 4 10 0 1 0 1-1 1h-2c-1-3-2-7-2-10z" class="B"></path><path d="M134 69c-1-1-1-1-1 0h-2 0c0 1 0 0-1 1h0c0 1-1 1-1 2s-1 1-2 2h0v-1h0v-1h1c1-1 1-3 3-4 1-1 2 0 3-1h2v-1c1 0 1 0 2 1v1l-2 2h3c1 0 1 0 2 1 1-1 1-1 2-1v-1c1-1 1-2 2-2 1 1 1 2 1 3-1 0-1 1-2 2 0 1 1 2 2 2h1 1c1 0 1-1 2-1h0 3l-1 1c-2 0-3 0-5 1h-1c-1-1-2-1-3-2l1-2h-1-3-2-1c-1 0-1 0-1-1l1-2v-1c-1 1-2 1-3 2z" class="H"></path><path d="M329 59c-1 1-2 1-3 2 0-1-1-1-2-2-2-1-2-1-4-1 0 0-1 0-1-1h-1-2-2v-1h3v-2c1-1 1-1 2-1 1 1 2 1 2 2h0l-1-1v1c1 1 2 1 3 2h3-1-2l-1-2h0c1 0 2 1 2 1 1-1 2-1 2 0h1l2 3z" class="J"></path><path d="M534 122c1 0 1 0 2 1l1 1 2 1 1-1s0 1 1 2c0 0 1 1 1 2h-1v1h0-1l-1 1c0 1 0 1 1 1 0 1 0 2-1 2l1 1h0 0v3s0-1-1-2l-1-3h0l-2-3-1-3c0-2-1-3-1-4h0z" class="H"></path><path d="M535 126c2 0 3 1 4 3l-1 3h0l-2-3-1-3z" class="L"></path><path d="M88 369v1c-1 1-1 2-2 4l-7 7c-1 2-2 5-4 7h-2l-1-1c0-1 0-2 1-2 1-2 3-3 4-4l7-7c1-2 2-3 4-5z" class="C"></path><path d="M134 69c1-1 2-1 3-2v1l-1 2c0 1 0 1 1 1h1 2 3 1l-1 2c1 1 2 1 3 2h1s-1 0-1 1l-1-2-2 1h-3-1-1c-1-1-2-1-2-1-1-1-2-1-2-1 1-1 1-2 1-3s1-1 1-2h0c-1 1-4 3-4 5h-1c0 1 0 1-1 1h0-1s1 0 1-1c1 0 1-1 2-2 0 0 0-1 1-1 0 0 0-1 1-1zM85 200s1-1 1-2 1-4 0-5c0-1-1-1-2-2v-1l-1-1c0-1-1-2-1-2l-4-4c1-1 2-1 2-2 1 0 1 0 2-1 0 3 0 4 1 5l2 2v1c0 1 1 1 2 1l-1 1c1 1 1 2 2 2v6h-1l-1 1v1h2c1 0 2 1 2 2v2h-1c0-1 0-3-1-4h-1c0 1-1 0-2 0z" class="K"></path><path d="M516 238h1 1l1-1c-1 0-2-1-2-2s1-2 2-3h2 0c0 1-1 1-1 1v3h2s1 0 1 1h0c-1 0-1 1-2 1v1c1 1 1 1 1 2s1 1 1 2 1 2 1 3v2c1 1 2 3 2 4s-1 1-1 1-1-1-1-2v-1l-1-1-1-5s0-1-1-1v-1c0-1-1-1-1-2-1-1-1-1-2-1v1h0v1l1 1h0v1 1s-1 0-1 1v-1c0-1-1-4-2-6z" class="I"></path><path d="M520 358h1c0 4-2 8-2 12-1 2-1 3-2 5-1 1-1 1-2 1 0-1-1-1-1-1-1-3 3-9 4-11s1-4 2-6z" class="C"></path><path d="M88 165s1 0 1 1h2l-1 1c2 1 2 0 3 1-1 1-1 3-2 4l-2 1h0-1v-3c1-1 1-1 1-2v-1h0c-1 1-1 2-2 2 0 1-1 3-1 4l-1-1c1 0 1-1 1-2l-1 1c-1 2-1 1-3 2l-1 2-2 2-1-1c1-2 5-5 6-7 1-1 2-1 2-2s1-2 2-2z" class="F"></path><path d="M95 108l-2 2v1l-1-1c1-1 1-1 1-2s1-2 0-3v1l-1-1c0-1 0-1-1-1v-1c1 0 1-1 2-2v-3-1c0-1-1-3 0-4h1v3h0 0l1-2s0-1 1-1c0-1 0-1 1-2v-1-1c1 0 1 0 2-1s2 0 4-1h0c1 0 2 0 3-1h1l2-2c1 0 1-1 2-1h1c-1 1-1 1-1 2l-1 1c-1 0-1 0-2 1 0 0-1 1-2 1s-2 1-2 1v-1c-2 1-3 1-4 2l-1-1h-1s-1 1-1 2c0 2-2 3-2 5-2 2 0 4-2 6l1 1c0 2 1 4 1 5z" class="I"></path><path d="M90 202c0-1-1-2-2-2h-2v-1l1-1 1 1h2 0c1 1 1 1 2 1 1 1 1 1 1 2l1 1s3 2 4 3v2c-2 3-5 6-7 8 0-2 1-2 2-3s2-2 2-3-1-2-2-3h0 0 0c-1 1-1 1-1 2v1h-1c0-2 1-3 1-4l-2-2v-2zM336 61h2 0v1h1 1c1 1 2 1 2 2h1c1 0 1 1 2 1l1 1v-1-1h2 1v2c1-1 1-1 2-1s2 1 3 2 2 1 2 2h-1v1h-2-2c-1 0-1 0-2 1h-2v-1c-1-2-2-3-4-4-1 0-1-1-2-1h0c-1-1-4-2-5-3v-1z" class="J"></path><path d="M422 508h1c1 2 2 4 2 6l-1 1c1 2 1 6 2 9l3 9-1 1h-1c-1 0-2-1-2-2-1-1 0-4 0-6-1-2-1-5-2-7v-4c-1-2-1-5-1-7z" class="B"></path><path d="M103 129c2 0 2 0 2 1v1c-2 1-4 2-5 4-2 0-2 1-4 2 0 0-1 0-1 1l1 1h-1c-2 1-4 2-5 3l-4 2c-1 1-1 1-2 1l-1 1h-1v-1l2-2c1 0 3-1 4-2h1s0-1 1-1 3-1 4-2v-1c0-1 1-2 1-3h0c-1 0-2 1-4 1-1 1-3 3-5 3h-1c1-2 2-2 4-3l6-3 1-1h1 0l1 1c1 1 1 1 2 1 1-1 2-3 3-4z" class="G"></path><path d="M386 60h0v4l1 2c1 1 3 2 3 4l-1 1v1h2c1-1 2-1 2-2h1s1 0 1-1c1 0 2-1 2-1l1-1c1 0 2 1 3 0 0-1 1-1 2-1h1l1 1 1-1h1l1 1c1 0 1-1 2-1h0l1 1v-1h2l1 1v-1l1 1 1-1h0c0 1 0 2-1 2h0c-1 1-1 1-2 1l-2 1c-2 0-4-1-5 1-1-1-1-2-2-2h-1-3-1-1 0c-1 1-1 0-2 1-2 0-3 2-5 4h-2c-1-1-1-2-1-3 1-1 1-1 1-2-1-1-2-2-3-2h0v-2-5z" class="K"></path><path d="M471 59l1-2c1 1 0 1 1 1h0c1-1 2-1 2-1h1 2 0 0c1 0 4 1 5 0h1 0 3v-1h3s0 1 1 1h1s1 0 2 1h0 1c1 0 1 1 2 0 1 0 2 1 3 1h1 1c1 1 2 0 3 1 0 1 0 1 1 1 0 1 1 2 2 2l1 1h1 1c0-1 0-2 2-2h0c0 1 0 1-1 2 1 0 1 1 2 1h1c0 1-1 1-2 2v-2h-2 0-2c-1-1-2-1-3-1-1-1-3-2-5-3h-6v-2c-2 0-4 0-6-1-3 0-8-1-11 0-2 1-3 1-5 2-1-1-2-1-2-1z" class="I"></path><path d="M102 235l4 26-3-3h0l-1-1c-4 0-6 4-9 6h-1l3-3v-1h1 0c1-1 2-2 2-3 0 0 1 0 1-1v-1-1-1l-1 1h-1c0 1 0 1-1 1h-2v1-1c0-1 1-1 1-1 2-1 3-3 4-4-1-2 0-3 0-4v-1 2h2c1 1 0 1 1 2 0 1-2 3-3 4h1v3-1h1l2-2h1s0-1-1-2c0-1 1-1 0-2h0v-2h0v-2c-1-1-1-1-1-3h0v-1-2-1-2z" class="J"></path><path d="M158 373c1-3 5-5 7-8 3-4 5-8 7-12l1 3c1 0 1 2 0 3h0c0 2-1 4-1 6-1 5-2 10-4 14 0 1-1 2-1 3l-1-1c1-2 2-5 2-8 2-5 3-9 3-14-1 3-4 7-7 9-2 2-4 3-6 6v-1z" class="B"></path><path d="M91 172h1c-1 1-1 2-2 2l-2 2v1h1l3 3c0 1 0 1-1 2h-2c-1 2-1 2-3 2 0 2 1 3 1 5-1 0-2 0-2-1v-1l-2-2c-1-1-1-2-1-5h0l1-3h0v-1c1-1 1-2 2-2h2l1-1h1 0l2-1z" class="U"></path><path d="M86 181c1 0 1-1 2-1l1 1v1l-1 1h-1v-1c-1-1 0 0-2 0l1-1z" class="D"></path><path d="M88 173h1 0c-1 1-1 2-3 3v2c-1 1-3 1-4 2l1-3h0v-1c1-1 1-2 2-2h2l1-1z" class="K"></path><defs><linearGradient id="A" x1="157.851" y1="76.113" x2="158.022" y2="81.57" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#595858"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M150 73l1-1h0c1 1 2 1 3 1h0l1 1-1 1v2h1 0 5s0-1 1-1h0-1c-1 0-1 0-2-1h1 0 1c1 0 1 0 3-1h-1v2h0v2h1c0 1-1 2-1 3l-2 1v1s0 1-1 1c-1 1-3 1-4 1-1-1-2-1-2-2-1 1-1 2-1 3h-1c-1-1 0-4 0-6s0-4 1-6h0l1-1h-3z"></path><path d="M152 74l1-1c1 3 0 8 0 10-1 1-1 2-1 3h-1c-1-1 0-4 0-6s0-4 1-6h0z" class="B"></path><path d="M99 112l1 1v1l1-1c1 1 1 2 2 4v1c-1 2-1 4-1 6-1 2-3 4-4 6h0l-1 1h-1v-1-2h-1c1-1 1-2 1-2v-1c1-1 0-2 0-3l-1-1c-1-1 0-2 0-3l3-4 1-2z" class="I"></path><path d="M99 112l1 1v1l-1 3c-1 2-2 3-3 5l-1-1c-1-1 0-2 0-3l3-4 1-2z" class="B"></path><path d="M101 113c1 1 1 2 2 4v1c-1 2-1 4-1 6-1 2-3 4-4 6h0v-2h1c1-2 2-6 1-8h0c0-2 0-2-1-3l1-3 1-1z" class="G"></path><path d="M439 476h1l19 43c0 1 0 2-1 4-1 1-1 1-2 0-1 0-2-1-2-2-1-1-1-3-1-5l-3-11c-2-10-6-19-11-29z" class="C"></path><defs><linearGradient id="C" x1="95.459" y1="104.979" x2="99.976" y2="102.646" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#C)" d="M95 108c0-1-1-3-1-5l-1-1c2-2 0-4 2-6v1l3 1h1l-1 1h-2 0v3c1 0 2-1 2-1h1c0-1 1-1 2-2v2c1 0 2 0 2 1v5l-3 3c0 1 0 2-1 2l-1 2-3 4v-2h0v-2-1h-1v-2h1v-3z"></path><path d="M99 101c0-1 1-1 2-2v2c1 0 2 0 2 1v5l-3 3v-2l1-1-1-1s1 0 1-1v-3c0-1-2-1-2-1z" class="D"></path><path d="M95 108c0-1-1-3-1-5l-1-1c2-2 0-4 2-6v1c0 3-1 8 1 10v1c1 0 1 1 1 2h0c1 1 1 1 0 2h0c0 1 1 1 1 2l-3 4v-2h0v-2-1h-1v-2h1v-3z" class="K"></path><path d="M237 53h2 1c0 2 0 2 1 3h1l1-1c2 1 3 1 4 2h1v-1c0-1 0-1 1-2l2 2 2 1h-1c-1 1-2 1-4 1h0c-2-1-3-1-5-2 0 1-1 1-1 1l-3-3v1h-2-1c-1 2-2 2-2 4h2 1v1h4v-1h2c1 1 1 2 2 1h1 2 0c1 1 1 0 1 1 1 0 1 1 2 1s2 1 3 0h1 1 1 1c0-1 0-1 1-1s2-2 2-2h2v1h1s0 1 1 1c1 1 2 0 3 1 1-1 1-1 2-1l1 1c1 0 0 0 1-1l-1-1h0c1 0 1 0 2 1v1h0 0v-1c1-1 1-1 2-1h1v1c-1 0-1 0-2 1h1 0c0-1 1-1 1-1h1v2c-1 0-2 1-3 1-3-1-4-2-6-1-2 0-3-1-4-1-2 0-3 0-5 1-1 1-1 1-2 1l-1-1c-1 0-1 0-2 1h-3l-2-2-1-1h0-3v1c-2-1-2-1-3-2h-1l-1 1h0c-1 1-3 1-4 2-2 0-3 2-5 2 0-1 1-1 2-2h0c0-2-1-3-1-5h1l-1-3h1c1-1 2-1 4-1v-1z" class="I"></path><path d="M249 54l2 2-1 1h-2v-1c0-1 0-1 1-2z" class="L"></path><path d="M237 54h1 0c-2 0-2 0-3 2-1 1-1 2-1 3s1 2 2 3v-1h4c-1 1-3 1-4 2-2 0-3 2-5 2 0-1 1-1 2-2h0c0-2-1-3-1-5h1l-1-3h1c1-1 2-1 4-1z" class="F"></path><path d="M211 52l1-1c1 1 1 1 1 2v2l1 1c0 1 4 1 5 2s2 0 3 0h0c1 0 1 0 1 1h0 2c0-1 3-4 4-3h2l1-1h1-1l1 3h-1c0 2 1 3 1 5h0c-1 1-2 1-2 2h-2-1c-1 0-2 1-3 0-4-2-9-3-13-4-1-1 0-2-1-4h-1v-1h1v-4z" class="J"></path><path d="M214 56c0 1 4 1 5 2s2 0 3 0h0c1 0 1 0 1 1h0 2c0-1 3-4 4-3h2l1-1h1-1l1 3h-1c-1-1-1-1-2-1h0v1h1 1l-1 1c0 1-1 2-1 3h0c-1 1-1 2-2 2s-2 0-3-1h0s0-2-1-2c0-1-4-1-5-2-2 0-4-1-5-2v-1z" class="I"></path><path d="M416 66h-2v-1c1 0 2-1 3-1h0c1-1 1-1 2-1l1 1v1h0c1 0 1-1 2-1h0c1-1 0-1 1-1v-2h-1l-1-1-2 1-1-1h0c-1 0-1 1-2 2l-1 1h0 0c-1 0-1 0-2 1h0 0s0-1 1-1c1-1 2-3 4-4 0 0 1 1 2 1 0-1 1-1 1-1l1-1h2c0 1-1 3 0 4v1 1h0 3c2 1 4 1 5-1 1 0 2-1 4-1 0-1 1 0 2 0s3 0 4-1l-2 2c-2 2-4 2-7 3-2 2-3 3-6 3l-1 1c-3 1-5 1-7 2-1 0-1-1-2-1l-6 1v1c-2 1-2 0-3 0h0c2-2 3-2 5-3 1 0 1-1 2-1s1 0 1-1c1 1 2 1 3 1l-1-1-1-2h-1 0z" class="F"></path><path d="M419 69h0c1 0 2 1 3 0 3 0 7-3 10-4 2-1 5-2 8-2-2 2-4 2-7 3-2 2-3 3-6 3l-1 1c-3 1-5 1-7 2-1 0-1-1-2-1l-6 1v1c-2 1-2 0-3 0h0c2-2 3-2 5-3 1 0 1-1 2-1s1 0 1-1c1 1 2 1 3 1z" class="G"></path><path d="M163 70c-1 0-1 0-2 1v1 1h-1v-3h1c0-1-1-1-1-1v-1-1-1h3c1-1 3 0 4 0 0-1 1-2 2-2l1-1h0c0-1-1-2-1-2h-1 3 1v-1l1 1h1 0l1-1h1l2-1c1 1 2 1 3 1v-1-1h0c0-2 0-3 1-4h1c0 1-1 2-1 4h0c0 1 0 1 1 1v1h2c1 0 1 0 3-1v1c2 0 2-2 4-2 1 0 3 0 4 1l1 1h0 1c1 0 1 1 2 0 1 0 1-1 2-2 0-1 0-1 1-1 3-2 6-1 8-2v-3h0v4h-1v1c-3 1-5 3-7 4-1 1-3 3-4 3-3 1-8 1-11 1-1 0-3-1-3-2-1-1-1-1-1-2l-1 1c-2-1-4-1-6 0-1 0-2 1-3 1-2 0-6 2-7 3s-2 2-3 2-1-1-2 0h-1 1c0 1 1 1 1 2z" class="I"></path><path d="M210 57h1c1 2 0 3 1 4 4 1 9 2 13 4 1 1 2 0 3 0h1v1c-6 0-11-3-17-2-1 0-3 1-4 2-1 0-2 1-2 1h-4c-1 0-2 1-4 1-1 1-3 1-4 1-1-1 0-2-2-2-1 0-1 0-1 1l-1 2c-1 1-5 1-6 1 0 1 1 2 0 3h-1c-1-2-1-3-1-4v-5-2l1-1h0l1-1c0 1 0 1 1 2 0 1 2 2 3 2 3 0 8 0 11-1 1 0 3-2 4-3 2-1 4-3 7-4z" class="M"></path><path d="M183 62c1 0 1 0 1 1 1 3 0 5 0 8 0 1 1 2 0 3h-1c-1-2-1-3-1-4v-5-2l1-1z" class="Q"></path><defs><linearGradient id="D" x1="167.856" y1="67.239" x2="176.034" y2="74.326" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#D)" d="M163 70c0-1-1-1-1-2h-1 1c1-1 1 0 2 0s2-1 3-2 5-3 7-3c1 0 2-1 3-1 2-1 4-1 6 0h0l-1 1v2c-1 2 0 3-1 5-1 1-1 0-3 0-1 1-3 2-3 3-1 0-2 0-2 1-1 2-2 2-3 3l-6 6h-1v-1h-1l-2 1v-1l2-1c0-1 1-2 1-3h-1v-2h0v-2h1c-1-1 0-3 0-4z"></path><path d="M163 70c0-1-1-1-1-2h-1 1c1-1 1 0 2 0s2-1 3-2 5-3 7-3c1 0 2-1 3-1 2-1 4-1 6 0h0l-1 1c-4 0-9 0-12 4l-2 1h0c-1 0-1 1-2 1v2s0 1 1 2c-1 0-2 1-2 1-1 1-1 1-2 1 0 1 1 2 0 3h-1v-2h0v-2h1c-1-1 0-3 0-4z" class="F"></path><path d="M277 63h3c0-1 1-1 1-2h-1-1 0 0v-1-1c1 1 2 2 3 2h1-1c0 1-1 1-1 2h1c1-1 1-1 2-1h0s1 0 1 1c1-1 1-1 1-2 1 0 2 0 3-1v-1l1-1v-1h0 1 0c-1-1-1-1-1-2l-1 1h-1c1-1 1-1 2-1l2 2h0l1 1h0-2s0 1-1 2h2l1 1v-1l1 1 1-1s0-1 1-1h1v-1h1v1h0 2l1 1s1 0 1-1c0 1 1 1 1 1l1-1c1 0 0 1 1 0 0-1 2 0 3-1h1 1v-1 1c1 0 2-1 3-1v1c-2 1-4 1-6 2-1 1-3 1-4 1-2 1-5 0-7 0-1 0-2 1-4 1h-1l-2 1c-2 2-1 2-1 4-3 1-6 0-9 0-2 0-3 1-5 1s-4 0-5-1h-1c-1-1-4-1-6-1-3 1-8-1-11-1-3-2-6-1-8-2-1-1-1-1-1-2h-2l1-1h1c1 1 1 1 3 2v-1h3 0l1 1 2 2h3c1-1 1-1 2-1l1 1c1 0 1 0 2-1 2-1 3-1 5-1 1 0 2 1 4 1 2-1 3 0 6 1 1 0 2-1 3-1z" class="J"></path><path d="M532 157v-1c0-1 0-2 1-3 0-4 2-7 3-10 1-1 1-2 1-3h2l1 1h0c0 1-1 1-1 2h-1l-1 1v2 1h-1 0v1c-1 1-1 1-1 3h1s-1 0-1 1l-1 1h0v1 1 1s-1 1-2 1h0v2h-1c0 1 0 1-1 1 1 1 1 1 1 2h1l-1 1v1 1l2-2h0c0 1 0 3-1 4v1 1h1 1c0 1 0 1-1 2l-1 1c0 1-1 2-1 3h0v1c-1 0-1 0-1 1v1 1l1 1 1 2c1 0 2 1 2 2h1c1 1 2 1 2 1v1c-1 2 1 4 1 6v4l1 1c0 1-1 1-2 2v3h0l-1 1-1 1v1c0 1-1 1-1 1h-1c-1 1-1 2-1 3h0-1c0 1 0 2-1 3 0 0-1 0-1 1v2 2c-1-1-1-2-2-3l-1-1 1-1 1-3c0-1 0-1-1-2h0l1-2h0v-1c1-1 2-2 3-2l2-2h-1c-1-3 2-4 3-6v-4c-1-1-1-2-2-3 0 0-1 0-2-1v-1-2h0c0-2-1-1-1-2-1-1-1-3-2-4v-1-5h0c1-1 2-2 2-3l-2 1v-1-2c-1-2-1-3 0-5 0 0 0-1 1-1 0-1 0-1 1-2h1l1-1z" class="H"></path><path d="M531 183c1 1 1 3 3 3h0c2 2 3 6 3 8-1 2-1 3-2 5v1h1v1h0c-1 1-2 4-2 4-1 0-1 0-2 1v3h-1c0 1 0 2-1 3 0 0-1 0-1 1v2 2c-1-1-1-2-2-3l-1-1 1-1 1-3c0-1 0-1-1-2h0l1-2h0v-1c1-1 2-2 3-2l2-2h-1c-1-3 2-4 3-6v-4c-1-1-1-2-2-3 0 0-1 0-2-1v-1-2z" class="F"></path><path d="M531 185c0 1 1 1 1 2h2c1 1 2 5 2 6v1c-1 2-2 7-4 9-1 0-2 0-3 1l-1 1v-1c1-1 2-2 3-2l2-2h-1c-1-3 2-4 3-6v-4c-1-1-1-2-2-3 0 0-1 0-2-1v-1z" class="D"></path><path d="M527 212c2-1 3-1 3-3 0 0 0-1-1-2h0v-1h2l1-2c1 0 2 0 2-1l2-2c-1 1-2 4-2 4-1 0-1 0-2 1v3h-1c0 1 0 2-1 3 0 0-1 0-1 1v2 2c-1-1-1-2-2-3l-1-1 1-1z" class="J"></path><defs><linearGradient id="E" x1="101.848" y1="120.454" x2="117.935" y2="118.316" xlink:href="#B"><stop offset="0" stop-color="#8e8c8c"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#E)" d="M108 109v2l1 1v-2h3 0l3 1 6 1h-2l-1 1v2c0 1 1 1 1 2s-1 2-1 3c-1 2-2 3-3 4l-2 2h-1c-1 2-3 4-3 6-1-1-1-2-1-3l-3 1c0-1 0-1-2-1-1 1-2 3-3 4-1 0-1 0-2-1l-1-1h0l1-1h0c1-2 3-4 4-6 2-2 2-6 3-8l3-7z"></path><path d="M110 121h1v1c-1 1-2 1-3 2h-1c1-1 1-2 1-2 1-1 1-1 2-1z" class="N"></path><path d="M103 129l3-3c1 1 1 1 2 1v2l-3 1c0-1 0-1-2-1z" class="D"></path><path d="M108 129c2-2 2-4 4-5 1-1 1-1 1-2l1 1-2 3c-1 2-3 4-3 6-1-1-1-2-1-3z" class="R"></path><path d="M109 110h3 0l3 1c-1 1-1 1-2 1l-1 1h1c0 1 0 2-1 2v1h-1-1c1-1 1-2 0-3h-1v-1-2z" class="B"></path><path d="M114 123c2-3 3-7 4-10v2c0 1 1 1 1 2s-1 2-1 3c-1 2-2 3-3 4l-2 2h-1l2-3z" class="N"></path><path d="M106 119c0-3 2-6 2-8l1 1v1h1c1 1 1 2 0 3h1v1c-1 0-4 1-5 2z" class="R"></path><path d="M108 109v2c0 2-2 5-2 8-2 4-3 10-7 12l-1-1c1-2 3-4 4-6 2-2 2-6 3-8l3-7z" class="N"></path><defs><linearGradient id="F" x1="371.265" y1="63.603" x2="372.322" y2="76.942" xlink:href="#B"><stop offset="0" stop-color="#3a393a"></stop><stop offset="1" stop-color="#5f5f60"></stop></linearGradient></defs><path fill="url(#F)" d="M354 67l2-2c1 0 1 0 2-1 1 1 1 1 2 1l1 1h1l1-1h0 0c1-1 2-1 2-2l1 1h-1l-1 1c0 1-1 1-1 2l1-1h1 3l1-1 1 1c1 0 1-1 2 0h1 1v-1c1-1 1-1 2-1h0c1 0 2 1 3 0h0c1 0 2-1 3-1l1-1c-1 0-1-1-1-2 1-2 1-3 1-5h-1c1-1 1-2 1-3h0c1-1 1-1 2-1h1c0 3-1 6 0 9v5 2h0c1 0 2 1 3 2 0 1 0 1-1 2 0 1 0 2 1 3-2 0-2 0-3-2h-1c1 1 1 1 1 2 0 0 1 0 1 1h-1c0 1 0 1-1 1 2 0 2 0 3-1 1 0 1 0 2 1h3 1c-3 1-4 0-6 0s-2 1-4 1c-1-1-1-1-2-1 0-1 0-1-1-1h-2l-2-2c-1 0-1-1-2-1s-1-1-2-1c-1-1-3-1-4-1l-2-1-1-1h0 0c-3 0-6 1-8 3-1 0-2-1-3-1v-1h1c0-1-1-1-2-2z"></path><path d="M385 51h1c0 3-1 6 0 9v5c-1 0-1 0-2-1-1-4-1-9 1-13z" class="Q"></path><path d="M473 60c2-1 3-1 5-2 3-1 8 0 11 0 2 1 4 1 6 1v2h6c2 1 4 2 5 3 1 0 2 0 3 1h2 0 2v2c1-1 2-1 2-2l1 1v1c-2 1-2 1-3 2 0 2-1 5-1 6l1 1v1h-1l-2 2c0 1-1 2-2 2l-1 1v-1c-2-1-2-3-2-4s-1-2-1-3c1 0 1-1 2-1v-2c-1 0-2 0-2-1l-1-1c-1-4-6-2-9-4 0 0-1-1-1 0h-1-2-1c-3-3-9-3-13-4h-1v-1h-2z" class="J"></path><path d="M510 79v-1h-1 0c0-1-1-1-1-1l-1-1v-3l1-1v-3 1h3v3c-1 2-3 2-1 4h2l-2 2zm-15-16c3 0 8 0 10 1 1 1 3 1 4 2 1 0 1 1 2 2h0c-1 1-1 1-2 1l-1-1c-2-1-3-3-5-3-3-1-6 0-8-2z" class="L"></path><path d="M475 60c3-1 7 0 11 0 1 0 3 0 5 1 1 0 2 1 4 1v1c2 2 5 1 8 2 2 0 3 2 5 3l1 1h-1v3l-1 1v3l1 1s1 0 1 1h0 1v1c0 1-1 2-2 2l-1 1v-1c-2-1-2-3-2-4s-1-2-1-3c1 0 1-1 2-1v-2c-1 0-2 0-2-1l-1-1c-1-4-6-2-9-4 0 0-1-1-1 0h-1-2-1c-3-3-9-3-13-4h-1v-1z" class="U"></path><path d="M506 73h0v1c1 1 1 1 1 2v1 3h1 0v1l-1 1v-1c-2-1-2-3-2-4s-1-2-1-3c1 0 1-1 2-1zm-31-12c4-1 10 0 13 1 1 0 2 1 2 2h4c3 0 10 2 12 4l-1 1-1 1s1 0 1 1h1c-1 0-2 0-2-1l-1-1c-1-4-6-2-9-4 0 0-1-1-1 0h-1-2-1c-3-3-9-3-13-4h-1z" class="G"></path><defs><linearGradient id="G" x1="399.971" y1="68.867" x2="403.425" y2="81.824" xlink:href="#B"><stop offset="0" stop-color="#4c4b4c"></stop><stop offset="1" stop-color="#7e7e7e"></stop></linearGradient></defs><path fill="url(#G)" d="M416 66h1l1 2 1 1c-1 0-2 0-3-1 0 1 0 1-1 1s-1 1-2 1c-2 1-3 1-5 3h0c1 0 1 1 3 0v-1l6-1c1 0 1 1 2 1 2-1 4-1 7-2-2 2-4 3-6 5h0l-2 2h0c-1 1-1 2-1 3l-2 2c-2 1-7 2-9 1h-1l-3-2c0-2-1-2-2-3v-1h-1-1l-1-1h-1-2-1-3c-1-1-1-1-2-1-1 1-1 1-3 1 1 0 1 0 1-1h1c0-1-1-1-1-1 0-1 0-1-1-2h1c1 2 1 2 3 2h2c2-2 3-4 5-4 1-1 1 0 2-1h0 1 1 3 1c1 0 1 1 2 2 1-2 3-1 5-1l2-1c1 0 1 0 2-1h0c1 0 1-1 1-2z"></path><defs><linearGradient id="H" x1="401.071" y1="73.659" x2="418.671" y2="78.566" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#H)" d="M411 72l6-1c1 0 1 1 2 1 2-1 4-1 7-2-2 2-4 3-6 5h0l-2 2h0c-1 1-1 2-1 3l-2 2c-2 1-7 2-9 1h-1l-3-2c0-2-1-2-2-3 3 0 6 1 9 3l4-2h1l-2-2h0c-1 0-1 0-2 1v-2c0-1 1-1 1-2v-2z"></path><path d="M476 61c4 1 10 1 13 4h1 2 1c0-1 1 0 1 0 3 2 8 0 9 4l1 1c0 1 1 1 2 1v2c-1 0-1 1-2 1 0 1 1 2 1 3s0 3 2 4v1l-3 3-2 1-2 2c1-1 1-3 1-4v-4c-1-1-3-1-5-2-1 1-2 0-3 0h0v1h0l-1 1c-2-2-6-4-7-8l-2-2v-2c0-1-1-1-2-1-1-2-2-3-3-5-1 0-1-1-2-1z" class="T"></path><path d="M476 61c4 1 10 1 13 4h1 3c0 1 1 1 1 2 1 1 2 1 3 1l1 1c-1 1-2 1-3 1 0 1 1 2 1 3l-2-2c0-1 1-1 1-3l-2-2h-1-1-2 0c-2 0-3-1-4-2h-2c-1-1-1-1-2-1s-2 0-3-1c-1 0-1-1-2-1z" class="M"></path><path d="M481 64c3 1 5 1 7 3h2c1 1 1 2 2 2-1 1-1 2-1 3 0 2 3 5 5 6h0c-1 1-2 0-3 0h0v1h0c-2-3-5-5-5-9h0-1v-2c-2-2-4-3-6-4z" class="P"></path><path d="M478 62c1 1 2 1 3 1v1c2 1 4 2 6 4v2h1 0c0 4 3 6 5 9l-1 1c-2-2-6-4-7-8l-2-2v-2c0-1-1-1-2-1-1-2-2-3-3-5z" class="B"></path><defs><linearGradient id="I" x1="503.123" y1="69.723" x2="496.869" y2="74.751" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#I)" d="M490 65h2 1c0-1 1 0 1 0 3 2 8 0 9 4l1 1c0 1 1 1 2 1v2c-1 0-1 1-2 1 0 1 1 2 1 3s0 3 2 4v1l-3 3-2 1v-1h0v-1-1c0-1 0-2 1-4l-3-2h-3c0-2 0-3-1-4 0-1-1-2-1-3 1 0 2 0 3-1l-1-1c-1 0-2 0-3-1 0-1-1-1-1-2h-3z"></path><path d="M504 85v-6c0-2-1-3-1-5h1c0 1 1 2 1 3s0 3 2 4v1l-3 3z" class="O"></path><path d="M130 74h0c1 0 1 0 1-1h1c0-2 3-4 4-5h0c0 1-1 1-1 2s0 2-1 3c0 0 1 0 2 1 0 0 1 0 2 1h1 1 3l2-1 1 2c0-1 1-1 1-1 2-1 3-1 5-1h0l-2 1-1 2c-1 1-1 1-3 2h-3v1c-2 0-2-1-4-1v1c-2-1-5-1-7 0-1 0-2 1-3 1s-2 0-2 1l-1 1-3 3h0l-1 1s-1 0-1 1c-1 1-2 4-4 5h-1l-1-1h1v-3l-1 1h-4c-4 1-7 4-10 8h-1-1-1l-3-1v-1c0-2 2-3 2-5 0-1 1-2 1-2h1l1 1c1-1 2-1 4-2v1s1-1 2-1 2-1 2-1c1-1 1-1 2-1l1-1 2-2 1-1c1 0 2-1 3-1 1-1 1-1 2-1h1c1 0 1-1 1-1v-2l1 1h0v1c1-1 2-1 2-2h0 1 0 1s1 0 1-1h1c1 0 1-1 2-2z" class="J"></path><path d="M138 76c2 0 5-1 7-1v2h1v-1c2-1 2-1 4-1l-1 2c-1 0-3 1-4 1s-2 0-3-1h0c-2 0-3 0-4-1z" class="F"></path><path d="M95 96c1 0 1 0 2 1h0c1-1 2-1 3-2v1-1c3-3 5-4 8-6 1 0 2-1 3-1v-1c2 0 3-1 4-2v-1l3-1-1 1c-1 1-1 1-2 1 0 1 1 2 1 2v1c-1 1-1 1-2 1-4-1-8 2-11 5l-2 2v2h-1-1-1l-3-1v-1zm28-16c0-1 1-1 2-2v1c1 0 2-1 3-1s1 0 2-1h1l2-2c2 0 4 0 5 1s2 1 4 1h0c1 1 2 1 3 1s3-1 4-1c-1 1-1 1-3 2h-3v1c-2 0-2-1-4-1-2-1-3 0-5-2h-1-1c-1 1-2 1-2 1-1 1-2 1-3 1h-2c-1 0-2 1-2 1z" class="L"></path><path d="M134 77c2 0 4 0 6 2 0-1 1-1 1-1h1c1 0 1 1 1 1h0v1c-2 0-2-1-4-1-2-1-3 0-5-2z" class="U"></path><path d="M123 80s1-1 2-1h2c1 0 2 0 3-1 0 0 1 0 2-1-1 1-1 3-2 3h-1c-1-1-3 0-4 0-2 1-4 3-5 5-1 0-2 1-2 2l-1 1-1 1h0l-1 1h-4c-4 1-7 4-10 8v-2l2-2c3-3 7-6 11-5 1 0 1 0 2-1v-1s-1-1-1-2c1 0 1 0 2-1l1-1c0-1 0-1 1-1h1v1c2-1 2-1 3-3z" class="D"></path><path d="M132 77h1 1c2 2 3 1 5 2v1c-2-1-5-1-7 0-1 0-2 1-3 1s-2 0-2 1l-1 1-3 3h0l-1 1s-1 0-1 1c-1 1-2 4-4 5h-1l-1-1h1v-3h0l1-1 1-1c0-1 1-2 2-2 1-2 3-4 5-5 1 0 3-1 4 0h1c1 0 1-2 2-3z" class="G"></path><path fill="#9b9a9a" d="M126 83c1 0 1 1 1 2h-1c0 1-1 3-2 4 0 1 0 2-1 3 0 1-2 4-1 4-1 1-1 2-2 2 0 1-2 1-2 2v1s-2 1-3 2v3c-1 1-1 1-1 3h2v-1c2 1 2 0 3 2-2-1-5-1-7 0h0-3v2l-1-1v-2l-3 7c-1 2-1 6-3 8 0-2 0-4 1-6v-1c-1-2-1-3-2-4l-1 1v-1l-1-1c1 0 1-1 1-2l3-3v-5c0-1-1-1-2-1v-2c-1 1-2 1-2 2h-1s-1 1-2 1v-3h0 2l1-1h1 1c3-4 6-7 10-8h4l1-1v3h-1l1 1h1c2-1 3-4 4-5 0-1 1-1 1-1l1-1h0l3-3z"></path><path d="M103 102h3c-1 1-1 2-2 2l-1 3v-5zm6 4h-3v-1-1h2l1-1 1-1c1 1 1 2 1 3l-2 1zm-7 5l1-2h1 0c0 1 0 1 1 2v5c-1 2-1 6-3 8 0-2 0-4 1-6v-1c-1-2-1-3-2-4 0-1 0-2 1-2z" class="M"></path><path d="M102 111c1 0 2 1 2 1v1c0 1 0 4-1 5v-1c-1-2-1-3-2-4 0-1 0-2 1-2zm-1-12h1c3-1 3-4 6-6 1-1 2-1 4-2 0 1 1 1 2 1h0l-1 1c-1 0-1 0-2-1-1 0-2 1-3 2v3l-2 1 1 2v2h-1-3c0-1-1-1-2-1v-2z" class="O"></path><path d="M101 98c3-4 6-7 10-8h4l-1 2h0c-1 0-2 0-2-1-2 1-3 1-4 2-3 2-3 5-6 6h-1c-1 1-2 1-2 2h-1s-1 1-2 1v-3h0 2l1-1h1 1z" class="G"></path><path d="M111 105c2-3 3-7 7-9 2-1 4-3 5-5 0-1 0-1 1-2 0 1 0 2-1 3 0 1-2 4-1 4-4 2-6 3-9 6v1c0 2-1 3-2 5l-2 2v2l-1-1v-2c0-2 1-2 1-3l2-1z" class="T"></path><path d="M122 96c-1 1-1 2-2 2 0 1-2 1-2 2v1s-2 1-3 2v3c-1 1-1 1-1 3h2v-1c2 1 2 0 3 2-2-1-5-1-7 0h0-3l2-2c1-2 2-3 2-5v-1c3-3 5-4 9-6z" class="P"></path><path d="M314 531v2c1 6-1 12-1 18l1 33v8 4h-1c-1 1-2 1-3 0l-1-1v1h-1s-1 0-1-1c-1-1-1-2-1-3v-2h0c1 0 1 1 1 1l1 1h0c1-8 0-15 0-22l-1-17-2-17c2 1 3 2 6 1 2-2 2-3 3-6z" class="C"></path><path d="M515 65l2-2c1 1 1 1 2 1v2h0v1h0c0 1 1 1 1 2h2v1c-1 1-3 1-4 2-2 0-2 0-3 2h0c1 0 1 0 1-1h1v1c0 1 2 2 2 3l-1 1v1 1h0c1 1 1 1 1 2h0l1 1h-1v1c1 1 1 2 1 3h0c1 0 1 0 0 1h0v1-1c0 1 1 1 1 1h0 1s0-1 1-1v-1 2c-1 1 0 1-1 2l1 1c1 0 1 1 2 2 0 1 1 1 1 1h0c-1 0-1 1-1 1v1c0 1 0 1 1 2 1 0 1 0 1 1h1c0 2 0 2-2 3v1c1 0 1 0 2-1-1 1-1 1-1 2l-1-1c0 2 1 2-1 4h-1c-1 0-1 1-1 1h0v6 2l1 1h0l1 2c1 0 1 1 1 2h1 3c1-1 1-1 3-2v1l-1 1v2c-1 1-1 1-2 1v-1-1h0l-3 1h0-1-1l1 1h-1 0-3c-1-1-2-2-2-4l-2-7c0-3 0-5-1-8 0-2-1-4-1-6 1-2 0-3 0-5l-1-6 1-1c-2-2-3-5-5-7l1-1 1-2v-1-1l-1-1c0-1 1-4 1-6 1-1 1-1 3-2v-1l-1-1z" class="I"></path><path d="M513 78c1-1 1-2 2-3 1 1 1 1 1 2 1 2 0 3 1 4 0 1 2 3 2 5v3c1 2 3 4 4 5 0 2-1 3-1 5h1c1 1 2 0 2 1s1 3 0 4l-1 1-3 6v5h1 0c0 1 1 2 1 2 1 2 1 5 4 6h0 0-1-1 0c-1 0-2-1-2-2-1-1-1-3-2-4v-1h0c-1-3-1-6-1-8v-1c1-2 2-4 3-5-1-2-1-3-3-3v-1-1s1-1 1-2l-1-1c-1 0-1 0-1-1v-1c-1-1-1-1-1-2s0-1-1-1v-1-1c0-3-3-6-5-8l1-2z" class="K"></path><path d="M512 80c2 2 5 5 5 8v1 1c1 0 1 0 1 1s0 1 1 2v1c0 1 0 1 1 1l1 1c0 1-1 2-1 2v1 1c2 0 2 1 3 3-1 1-2 3-3 5v1c0 2 0 5 1 8h0v1c1 1 1 3 2 4 0 1 1 2 2 2h0l1 1h-1 0-3c-1-1-2-2-2-4l-2-7c0-3 0-5-1-8 0-2-1-4-1-6 1-2 0-3 0-5l-1-6 1-1c-2-2-3-5-5-7l1-1z" class="D"></path><path d="M516 95l-1-6 1-1c0 3 1 5 2 8 0 1 1 1 1 1v1 2c-2 3 1 6 0 8-1 1 0 2 0 3 0 4 2 7 2 12 2 1 2 2 4 2h0-3c-1-1-2-2-2-4l-2-7c0-3 0-5-1-8 0-2-1-4-1-6 1-2 0-3 0-5z" class="G"></path><defs><linearGradient id="J" x1="514.17" y1="94.292" x2="499.667" y2="95.619" xlink:href="#B"><stop offset="0" stop-color="#807f80"></stop><stop offset="1" stop-color="#9d9b9c"></stop></linearGradient></defs><path fill="url(#J)" d="M511 81c2 2 3 5 5 7l-1 1 1 6c0 2 1 3 0 5 0 2 1 4 1 6 1 3 1 5 1 8l2 7c0 2 1 3 2 4h3 0 1 1v1h-2v1h2l1 3-3 1c-1-1-3-2-3-3-1-2-2-3-3-4h0l-1-1v-1l-1-3-3-4v-2c-1 0-2-1-3-2-1 0-2-1-2-2v-1c-2-4-7-4-10-5v-1c0 1-1 1-1 1l-1-5 1-2 9-9v-1l2-2 2-3z"></path><path d="M514 94v-1c0-1 0-1-1-2h1c0 1 1 1 1 2s1 2 1 2c0 2 1 3 0 5 0 2 1 4 1 6 1 3 1 5 1 8-2-4-1-9-4-11v-1c0-2 0-3 1-4-1-2-1-3-1-4z" class="D"></path><defs><linearGradient id="K" x1="514.898" y1="85.793" x2="507.515" y2="91.726" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#8b898a"></stop></linearGradient></defs><path fill="url(#K)" d="M511 81c2 2 3 5 5 7l-1 1 1 6s-1-1-1-2-1-1-1-2h-1c1 1 1 1 1 2v1s-1 1-1 2c-1 0 0 0-1 1v-1c-1 0-2-1-2-2h0c-1 0-2-1-2-1-1-1-1-4 0-5h0l-1-1v-1l2-2 2-3z"></path><path d="M509 84c1 1 1 1 1 3l-1 1c-1-1 0-2-2-2l2-2z" class="O"></path><path d="M498 96l1 1c1 2 2 3 4 3 1 1 2 1 3 1 2 0 4 2 5 3 1 2 1 3 2 4s2 2 2 3c2 2 2 4 3 7 0 2 1 4 1 6l-1-1v-1l-1-3-3-4v-2c-1 0-2-1-3-2-1 0-2-1-2-2v-1c-2-4-7-4-10-5v-1c0 1-1 1-1 1l-1-5 1-2z" class="R"></path><path d="M498 96l1 1v5c0 1-1 1-1 1l-1-5 1-2z" class="N"></path><path d="M527 124l3-1h0v1 1c1 0 1 0 2-1v-2l1-1 1 1h0c0 1 1 2 1 4l1 3 2 3h0l1 3c1 1 1 2 1 2 1 2 0 2 0 4l-1-1h-2c0 1 0 2-1 3-1 3-3 6-3 10-1 1-1 2-1 3v1l-1 1h-1c-1 1-1 1-1 2-1 0-1 1-1 1-1 2-1 3 0 5v2 1l2-1c0 1-1 2-2 3h0v5 1c1 1 1 3 2 4 0 1 1 0 1 2h0v2 1c1 1 2 1 2 1 1 1 1 2 2 3v4c-1 2-4 3-3 6h1l-2 2c-1 0-2 1-3 2v1h0c0-1-1-1-2-2 1-1 1-1 1-2v-1l2-2c2-2 4-4 4-6s0-3-2-5c-1-1-1-2-1-3v-1c-1-1-1 0-2 0-1-3-1-4-3-5l-1-1c-1-5-2-11-1-16 1-1 2-2 2-4 0-1-1-3 0-5 1-3 5-6 5-8 1-3 1-7-1-9-1-1-1-2-1-4v-1l-1-3h-2v-1h2v-1h-1l-1-1h1 1 0z" class="G"></path><path d="M527 124l3-1h0v1 1c1 0 1 0 2-1v-2l1-1 1 1h0c0 1 1 2 1 4l1 3 2 3h0l1 3c1 1 1 2 1 2 1 2 0 2 0 4l-1-1h-2c0 1 0 2-1 3-1 3-3 6-3 10-1 1-1 2-1 3v1l-1-1h-2l-1-1c0-3 2-4 4-7 1-2 2-7 2-9 0-1 0-1-1-2-2-1-1-3-1-5 0-1-1-1-1-2v-2c-1-1-2-1-3-2h0c0-1 0-1-1-1l-1-1h1 0z" class="D"></path><path d="M527 124l3-1h0v1 1c1 0 1 0 2-1v-2l1-1 1 1h0l-1 1v2 1c0 1 0 1-1 1l-4-1v-2h-1 0zm4 32c-1-1-1-1-1-2s1-3 2-4c0-1 0-2 1-3 0-2 2-5 3-7 0-1-1-3-1-4s-1-1-2-3v-1c1-1 2-1 3 0l1 1v1 1l1 2 1 2v1h-2c0 1 0 2-1 3-1 3-3 6-3 10-1 1-1 2-1 3v1l-1-1z" class="F"></path><path d="M108 129c0 1 0 2 1 3 0 1 0 1-1 2-2 3-2 9-3 13 0 2-2 4-2 6-1 3-1 5-1 7-1 3-1 6-2 9-1 0-2 1-3 1l-2 2h-1c0 1 0 1-1 1-1 1-2 1-3 2v1h-2l2-2c1 0 1-1 2-2h-1c1-1 1-3 2-4-1-1-1 0-3-1l1-1h-2c0-1-1-1-1-1 0-2 0-2 1-2l3-4v-2-2h-1c-1-1-1-2-2-3l1-1v-1l2-2h0v-1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 0-1 1-1 0-1 1-1 1-2l-1-2h1l-1-1c0-1 1-1 1-1 2-1 2-2 4-2 1-2 3-3 5-4v-1l3-1z" class="T"></path><path d="M95 150c0 1 1 2 2 3l-5 6v-2-2h-1v-1c2-1 2-2 4-3v-1z" class="G"></path><path d="M95 157h1c0-2 2-3 3-4h0v10 4s-1 1-1 2l-1 1-2 2v-2c1-1 3-2 3-3 0-3-1-5-1-8v-1c-1 0-1 0-2-1z" class="O"></path><path d="M95 157c1 1 1 1 2 1v1c0 3 1 5 1 8 0 1-2 2-3 3v2h-1c0 1 0 1-1 1-1 1-2 1-3 2v1h-2l2-2c1 0 1-1 2-2h-1c1-1 1-3 2-4-1-1-1 0-3-1l1-1h-2c0-1-1-1-1-1 0-2 0-2 1-2h1l3-3 2-3z" class="D"></path><path d="M95 157c1 1 1 1 2 1 0 1-1 2-1 3v4 2h0c-2 2-1 4-4 5 1-1 1-2 2-3h0c0-1 0-2 1-3h0v-4c0-1-1-1-2-2l2-3z" class="G"></path><path d="M93 160c1 1 2 1 2 2v4h0c-1 1-1 2-1 3h0c-1 1-1 2-2 3h0-1c1-1 1-3 2-4-1-1-1 0-3-1l1-1h-2c0-1-1-1-1-1 0-2 0-2 1-2h1l3-3z" class="L"></path><path d="M88 165c0-2 0-2 1-2h1c1 1 1 1 1 2v1h-2c0-1-1-1-1-1z" class="K"></path><path d="M96 139l-1-1c0-1 1-1 1-1 2-1 2-2 4-2 1-2 3-3 5-4h1s0 1 1 1c0 1-1 1-1 2-3 6-1 13-7 17-1 1-2 1-2 2-1-1-2-2-2-3v1c-2 1-2 2-4 3v1c-1-1-1-2-2-3l1-1v-1l2-2h0v-1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 0-1 1-1 0-1 1-1 1-2l-1-2h1z" class="O"></path><path d="M100 136l1-1h1c1 1 0 4 0 5 0 3-2 6-3 9l-1 2c-1 0-2-1-3-2l3-5v-1l-1 2v-3c0-1 1-1 2-1v-2c0-1 0-1-1-2l2-1z" class="G"></path><path d="M100 136h1c1 2 0 4-1 6v-2l-1 1h0v-2c0-1 0-1-1-2l2-1z" class="D"></path><path d="M98 137c1 1 1 1 1 2v2c-1 0-2 0-2 1v3l1-2v1l-3 5v1 1c-2 1-2 2-4 3v1c-1-1-1-2-2-3l1-1v-1l2-2h0v-1c0-1 1-1 1-2 1 0 1-1 1-1 1-1 0-1 1-1 0-1 1-1 1-2l-1-2h1l2-2z" class="F"></path><path d="M97 145l1-2v1l-3 5v1 1c-2 1-2 2-4 3v1c-1-1-1-2-2-3l1-1c3-2 5-4 7-6z" class="C"></path><path d="M181 70c1-2 0-3 1-5v5c0 1 0 2 1 4h1v2s-1 1-1 2v-1c-1 1-1 2-1 3-1 1-3 4-4 5-1 0-1 1-1 2-2 1-5 4-7 5h-1c0-1 0 0-1-1l-1 1h0c-1 0-3 1-4 2-6 1-13 1-19 0h-1v-1h1l-1-1h1l-1-1c-1-2-3-5-5-6 0 0-1 0-2-1h-2c-1 0-2 1-2 1-1 1-2 1-3 1-1-1-2-1-3-1h1c0-1 0-2-1-2l1-1c0-1 1-1 2-1s2-1 3-1c2-1 5-1 7 0v-1c2 0 2 1 4 1v-1h3c2-1 2-1 3-2l1-2 2-1c-1 2-1 4-1 6s-1 5 0 6h1c0-1 0-2 1-3 0 1 1 1 2 2 1 0 3 0 4-1 1 0 1-1 1-1l2-1h1v1h1l6-6c1-1 2-1 3-3 0-1 1-1 2-1 0-1 2-2 3-3 2 0 2 1 3 0z" class="P"></path><path d="M182 70c0 1 0 2 1 4h0c-2 4-2 8-7 9l-2 1h-1c-1 0-1 0-2 1-1 0-1 1-1 2h-1c-1 0-2 2-3 2-2 3-8 5-12 4h-2 0l-1-1c3-1 6-1 9-2h0l3-1c1-1 2-1 2-2 2-2 4-4 6-5s6-3 7-5c2-3 3-4 4-7z" class="R"></path><path d="M181 70c1-2 0-3 1-5v5c-1 3-2 4-4 7-1 2-5 4-7 5s-4 3-6 5c0 1-1 1-2 2l-3 1h-2c0-1 0-1-1-1h0 2-3c1-1 0-1 1-1h3 0v-1h-1 0l1-1c2 0 3-2 3-3h1l6-6c1-1 2-1 3-3 0-1 1-1 2-1 0-1 2-2 3-3 2 0 2 1 3 0z" class="M"></path><path d="M163 83h1l6-6c1-1 2-1 3-3 0-1 1-1 2-1 0-1 2-2 3-3 2 0 2 1 3 0 0 1 0 1-1 1-2 1-4 3-5 5s-5 4-6 5c-3 2-6 5-9 7v-1h-1 0l1-1c2 0 3-2 3-3z" class="O"></path><defs><linearGradient id="L" x1="151.266" y1="85.06" x2="149.63" y2="92.371" xlink:href="#B"><stop offset="0" stop-color="#818080"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#L)" d="M150 75l2-1c-1 2-1 4-1 6s-1 5 0 6h1c0-1 0-2 1-3 0 1 1 1 2 2 1 0 3 0 4-1 1 0 1-1 1-1l2-1h1v1c0 1-1 3-3 3l-1 1h0 1v1h0-3c-1 0 0 0-1 1h3-2 0c1 0 1 0 1 1h2 0c-3 1-6 1-9 2l1 1h0 2c-4 1-7 1-10 0l-1-1h1l-1-1c-1-2-3-5-5-6 0 0-1 0-2-1h-2c-1 0-2 1-2 1-1 1-2 1-3 1-1-1-2-1-3-1h1c0-1 0-2-1-2l1-1c0-1 1-1 2-1s2-1 3-1c2-1 5-1 7 0v-1c2 0 2 1 4 1v-1h3c2-1 2-1 3-2l1-2z"></path><defs><linearGradient id="M" x1="146.15" y1="75.378" x2="145.683" y2="83.214" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#M)" d="M150 75l2-1c-1 2-1 4-1 6h-1v4l-1 1c-2 0-5-1-6-2h0c0-1-3-3-4-3h0v-1c2 0 2 1 4 1v-1h3c2-1 2-1 3-2l1-2z"></path><path d="M289 465h1l7 29c1 4 3 9 3 13v6l-1 17-2 42-1 21-1 13c0 1 0 3-1 4h-1l-1-1-5-134c-1 0-1-1-2-1h0c0-1 1-1 2-1 1-2 2-3 2-5-1 1-2 1-2 2l-1 1v-1c1-1 2-2 3-4v-1z" class="C"></path><defs><linearGradient id="N" x1="325.397" y1="58.001" x2="321.555" y2="77.506" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#9c9c9c"></stop></linearGradient></defs><path fill="url(#N)" d="M313 57v-1h0l1-1c1 1 1 1 2 1 0-1 0-1 1-2 0 0 0-1 1-1h0l1-1c1-1 1 0 1-1h1v1c-1 0-1 0-2 1-1 0-1 0-2 1v2h-3v1h2 2 1c0 1 1 1 1 1 2 0 2 0 4 1 1 1 2 1 2 2 1-1 2-1 3-2h0c1 0 2-1 3-2l1 1h0l1 1c0-1-1-2-1-2-1-1-2-1-3-1l-2-2h-1c0 1 0 1-1 1l-1-1c1-1 2 0 3-1l2 2c1 1 2 0 3 1 1 0 1 1 3 1h0c1 0 2-1 3-1h0 3v-1h0c2 1 4 0 5 1h1 1c-1 1-2 1-2 1l-1-1h-1c-1 1-2 1-2 0h-1c-1 1-2 1-3 1h-1 0l-1 1c-1 0-1 1-2 1 1 1 1 1 1 2h0v1c1 1 4 2 5 3h0c1 0 1 1 2 1 2 1 3 2 4 4v1h2c1-1 1-1 2-1h2 2c1 0 2 1 3 1 2-2 5-3 8-3h0 0l1 1 2 1c-3 0-12 2-14 3l-5 1h0s2 1 3 1h2c0 1 1 1 2 1-3 0-9 0-10 2h-2c-1 1-3 2-5 2-1-1-3 0-5-1h-3-3 0l-1 1 1-2h0l-3 1h0v-1h0c-1-1-3 0-5-1h-3c-1 0-2 0-3-1-2 0-6 0-8-2-1-1-2-2-4-3h-3 0 3v-1h-2-2-2c-1 1 0 1-1 1 0 0-2 0-2 1l-10 2c-2 0-3 0-5-1v-1 1h-1c0 1 0 1-1 1-2 1-5 1-6 1l-1-1c-2 0-4 0-7-1h0-1 0c-1 0-2 0-2-1h-1c-1 0-2-1-3-1h0c-1-1-2-3-3-3h-1c-2-2-5-1-7-2h0-1-1c-1-2-5-1-7-1h-1l-1 1-1-1-1 1h-3v-1h2c2 0 3-2 5-2 1-1 3-1 4-2h0 2c0 1 0 1 1 2 2 1 5 0 8 2 3 0 8 2 11 1 2 0 5 0 6 1h1c1 1 3 1 5 1s3-1 5-1c3 0 6 1 9 0 0-2-1-2 1-4l2-1h1c2 0 3-1 4-1 2 0 5 1 7 0 1 0 3 0 4-1 2-1 4-1 6-2v-1z"></path><path d="M332 59c1 0 2 0 3 1 0 0 0 1 1 2-1-1-2-1-3-1h0l-1 1c-1-1-2 0-3-1v-1c1 0 2 0 3-1h0z" class="J"></path><path d="M313 58c0 1 0 2-1 3-2 0-3 0-4 1-2 1-5 1-7 2-3 1-5 0-7 1-1 0-2 1-3 1 0-2 0-2-1-2 0-1 0-1-1-1l2-1h1c2 0 3-1 4-1 2 0 5 1 7 0 1 0 3 0 4-1 2-1 4-1 6-2z" class="F"></path><path d="M184 71c1 0 5 0 6-1l1-2c0-1 0-1 1-1 2 0 1 1 2 2 1 0 3 0 4-1 2 0 3-1 4-1h4s1-1 2-1c1-1 3-2 4-2 6-1 11 2 17 2h3l1-1 1 1 1-1h1c2 0 6-1 7 1h1 1 0c2 1 5 0 7 2h1c1 0 2 2 3 3h0c1 0 2 1 3 1h1c0 1 1 1 2 1h0 1 0c3 1 5 1 7 1l1 1c1 0 4 0 6-1 1 0 1 0 1-1h1v-1 1c2 1 3 1 5 1l10-2c0-1 2-1 2-1 1 0 0 0 1-1h2 2 2v1h-3 0 3c2 1 3 2 4 3 2 2 6 2 8 2 1 1 2 1 3 1h3c2 1 4 0 5 1h0v1h0c-4 4-9 8-14 11-2 2-5 4-8 5-9 6-18 11-29 14s-23 4-34 4l-37 2c-23 0-46-2-69-4l-15-1h-1c-1-2-1-1-3-2v1h-2c0-2 0-2 1-3v-3c1-1 3-2 3-2v-1c0-1 2-1 2-2 1 0 1-1 2-2-1 0 1-3 1-4 1-1 1-2 1-3 1-1 2-3 2-4 1 0 2 0 3 1 1 0 2 0 3-1 0 0 1-1 2-1h2c1 1 2 1 2 1 2 1 4 4 5 6l1 1h-1l1 1h-1v1h1c6 1 13 1 19 0 1-1 3-2 4-2h0l1-1c1 1 1 0 1 1h1c2-1 5-4 7-5 0-1 0-2 1-2 1-1 3-4 4-5 0-1 0-2 1-3v1c0-1 1-2 1-2v-2c1-1 0-2 0-3z" class="B"></path><path d="M184 71c1 0 5 0 6-1l1-2c0-1 0-1 1-1 2 0 1 1 2 2 1 0 3 0 4-1 2 0 3-1 4-1h4s1-1 2-1c1-1 3-2 4-2 6-1 11 2 17 2h3l1-1 1 1 1-1h1c2 0 6-1 7 1h1 1 0c2 1 5 0 7 2h1c1 0 2 2 3 3h0c1 0 2 1 3 1h1c0 1 1 1 2 1h0 1 0c3 1 5 1 7 1l1 1c-2 0-6 0-7-1-3 0-7-1-10-2l-3-3c-3 0-6 0-8-1-2 0-4-1-6-2-2 0-5 1-7 1h-5c-3 0-5-1-8-1s-4 2-6 2h0v-1h0-1c-2 2-4 4-8 5h-6c-1 1-1 0-2 0h-1l-1 6c0-4 1-7 0-11-1 3-1 7-2 10v-3l-1 1h-1c0-1-1-1-1-1h-1 0c-1 3-2 7-4 9v1h-1l-2 1 1 1-3 3h-1c1-1 1-1 1-2s0-2 1-2c1-1 3-4 4-5 0-1 0-2 1-3v1c0-1 1-2 1-2v-2c1-1 0-2 0-3z" class="N"></path><defs><linearGradient id="O" x1="127.9" y1="84.821" x2="129.916" y2="100.629" xlink:href="#B"><stop offset="0" stop-color="#aaa8a8"></stop><stop offset="1" stop-color="#cfcfcf"></stop></linearGradient></defs><path fill="url(#O)" d="M126 85c1 0 2 0 3 1 1 0 2 0 3-1 0 0 1-1 2-1h2c1 1 2 1 2 1 2 1 4 4 5 6l1 1h-1l1 1h-1v1h1c6 1 13 1 19 0 1-1 3-2 4-2h0l1-1c1 1 1 0 1 1h1 1 0v1c-1 0-3 0-4 1h1 0c-1 1-2 1-3 1l-1 1h1-1c-1 1-4 1-5 2h0c-2-1-3 0-5 0h-1-5c-1-1-3 0-4-1s-2 0-3-1v-1l-2-1h-5c-2 0-4 1-6 2 0 1-1 1-2 2v1h1v1c-1 0-1 1-2 2l-4 4h-1 0 0c0-1 0-1 1-2s2-1 2-3c-3 0-4 0-6 1h0l-2 1c1-1 3-2 3-2v-1c0-1 2-1 2-2 1 0 1-1 2-2-1 0 1-3 1-4 1-1 1-2 1-3 1-1 2-3 2-4z"></path><defs><linearGradient id="P" x1="310.777" y1="72.642" x2="307.455" y2="82.42" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#cbcbcb"></stop></linearGradient></defs><path fill="url(#P)" d="M251 69l3 3c3 1 7 2 10 2 1 1 5 1 7 1 1 0 4 0 6-1 1 0 1 0 1-1h1v-1 1c2 1 3 1 5 1l10-2c0-1 2-1 2-1 1 0 0 0 1-1h2 2 2v1h-3 0 3c2 1 3 2 4 3 2 2 6 2 8 2 1 1 2 1 3 1h3c2 1 4 0 5 1h0v1h0c-4 4-9 8-14 11 1-2 2-2 3-3l1-1h-1c-1 0-1 0-2-1h0l-1 1-1-1c1 0 1 0 2-1 0 0-1 0-2-1-1 1-3 0-4-1-4 0-8-3-12-5v1h-3 0l2-1h-2-1-1c-1-1-2 0-3 0-1-1-2-1-3-1-1 1-2 1-3 1v1 1c-2-2-1-2-2-4-1 1-1 2-1 3l-1-1h-1 0-1-4-1 0c-2 0-4 0-5-1h-1c-2 0-3-1-4-1s-1 0-2-1h-1c-1 0-1 0-2-1l-3-1c-1-1-1-2-2-2l1-1z"></path><path d="M279 73c2 1 3 1 5 1h1l1 1h1c1 0 3 0 4 1h-2c-3-1-6 0-9-1l-1-2z" class="N"></path><path d="M471 59s1 0 2 1h2v1h1c1 0 1 1 2 1 1 2 2 3 3 5 1 0 2 0 2 1v2l2 2c1 4 5 6 7 8l1-1h0v-1h0c1 0 2 1 3 0 2 1 4 1 5 2v4c0 1 0 3-1 4-4 4-8 8-13 10-1 1-2 2-3 2-4 3-9 5-14 7-8 4-16 6-25 7-7 1-15 2-22 2-11 0-22-2-33-4l-39-5-16-2c-4-1-9-2-13-1 2 1 5 1 7 1l18 3c15 3 32 7 46 13 5 2 11 6 13 11v5c1 9-2 18-5 27-3 11-7 22-11 33l-19 56-17 52-14 47c-2 7-4 15-7 21-1 2-2 3-5 4 0 0-1 0-2-1-1 0-1-1-2-1-1-4-2-8-4-12l-8-19-15-37-41-112-10-30c-2-6-4-12-5-18 0-2-1-5-1-7 1-4 4-9 6-12s5-7 8-8c4-2 8-3 12-4 6-1 11-3 16-5 4-1 7-3 10-4 3-2 5-3 8-4 0 1 1 1 1 2v6 15 11c0 2 0 3 1 5h1 0l2-2c0-2-2-4-2-6v-12-13c0-3 1-6 2-9 2-3 5-4 7-6l6-5 8-8 1-1h1l1-1h0 3 3c2 1 4 0 5 1 2 0 4-1 5-2h2c1-2 7-2 10-2-1 0-2 0-2-1h-2c-1 0-3-1-3-1h0l5-1c2-1 11-3 14-3 1 0 3 0 4 1 1 0 1 1 2 1s1 1 2 1l2 2h2c1 0 1 0 1 1 1 0 1 0 2 1 2 0 2-1 4-1s3 1 6 0h2 1l1 1h1 1v1c1 1 2 1 2 3l3 2h1c2 1 7 0 9-1l2-2c0-1 0-2 1-3h0l2-2h0c2-2 4-3 6-5l1-1c3 0 4-1 6-3 3-1 5-1 7-3l2-2 1-1v2c1 1 2 1 3 1l1 1c3-1 5 0 7 0 4 0 8-2 12-4 1 0 3-1 5-1z" class="C"></path><defs><linearGradient id="Q" x1="500.542" y1="86.046" x2="490.922" y2="85.279" xlink:href="#B"><stop offset="0" stop-color="#a7a6a7"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#Q)" d="M487 98l1-1c0-1 1-1 1-1v-1h1v-2h1v-1c-1-1-1-1 0-2 0-2 1-4 1-6 0-1 0-3-1-4h-1l-1-1c-1 0-1-1-2-2s-3-2-3-4h-1v-1-2l2 2c1 4 5 6 7 8l1-1h0v-1h0c1 0 2 1 3 0 2 1 4 1 5 2v4c0 1 0 3-1 4-4 4-8 8-13 10z"></path><path d="M436 67l1-1c0 1 0 2-1 2 0 1 0 1-1 2v1h0c-1 2-1 3-1 4h0 0l-4 1h0-2c-1 0 0-1-2-1v1c-1 1-1 2-1 4 1 1 2 1 3 1h1l3-1c-1 1-3 3-3 4v1c0 1-1 1-1 2h0v3h0c0-1 0-2-1-3-1 0-1 0-1 1v3c-1-1-1-1-2-1h-1-2v-1l-1 1h1v1l-1-1-1 1h-3 0-1 0-3 2v1h-4-1 1 0c-2 0-4 1-6 0-1 0-2 1-4 0h0c-1 0-1 0-1-1-1 1-1 1-2 0h-1c-1-1-2 0-3-1h1l-1-1c-1 0-1 1-1 1v-1h-1c-1 0-1 0-1-1l-1 1c0-1 0-1-1-2h-1c-5-2-10-1-14-2v-1-1c4 2 11 2 15 2l2-2h1l-1 1v1c1 0 1 1 2 1h1 3 1c1 0 2-1 3 0h-1 0c1 1 1 1 2 1 0 0 1 0 1 1h1 7 3c0-1 1 0 1-1h4c1 0 2-1 3 0h3 0c1-1 1 0 2-1l2-2-5-5v-2-1h1c0-1 1-1 1-2l1-1c1 0 2 0 2 1h2l2-2v-1c1-2 3-3 4-4h0z" class="N"></path><path d="M442 61l1-1v2c1 1 2 1 3 1l1 1c-2-1-6-1-9 1v1h-1 0l-1 1h0c-1 1-3 2-4 4v1l-2 2h-2c0-1-1-1-2-1l-1 1c0 1-1 1-1 2h-1v1 2l5 5-2 2c-1 1-1 0-2 1h0-3c-1-1-2 0-3 0h-4c0 1-1 0-1 1h-3-7-1c0-1-1-1-1-1-1 0-1 0-2-1h0 1c-1-1-2 0-3 0h-1-3-1c-1 0-1-1-2-1v-1l1-1h-1l-2 2c-4 0-11 0-15-2h0c-1 0-2-1-2-3h1 0 1c1 1 3 1 4 2h2c1 1 3 0 5 1 1 0 3-1 4-1l1-1 1 1c3 0 6 0 10 1 2 0 4 2 7 2h5c1-1 4-2 5-2h1v-1s1 0 1-1c1-1 0-2 0-4l1-2c2-2 4-3 6-5l1-1c3 0 4-1 6-3 3-1 5-1 7-3l2-2z" class="P"></path><path d="M442 61l1-1v2c1 1 2 1 3 1l1 1c-2-1-6-1-9 1v1h-1 0l-1 1c-3 1-4 2-5 5h-2s-1 0-2-1c-2 0-3 2-5 4-1 1-1 2-1 4v2l-1 1s-1 1-1 2c-4 1-9 3-13 2-4 0-6-2-9-2-2 1-4 0-5 0l-2-2c3 0 6 0 10 1 2 0 4 2 7 2h5c1-1 4-2 5-2h1v-1s1 0 1-1c1-1 0-2 0-4l1-2c2-2 4-3 6-5l1-1c3 0 4-1 6-3 3-1 5-1 7-3l2-2z" class="T"></path><defs><linearGradient id="R" x1="356.521" y1="79.072" x2="357.18" y2="86.972" xlink:href="#B"><stop offset="0" stop-color="#b2b2b2"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#R)" d="M346 79l1-1 1 1c1 1 8 0 10 0 1 0 2-1 3-1 1-1 2-1 3-2h0 3l3 2 1 1 1 1h-1c0 2 1 3 2 3h0v1 1c4 1 9 0 14 2h-3v1h-1c-3-1-7 0-9-1v-1h-7-6c-2 1-4 1-6 1s-5 1-7 1-6-1-7 0c-1 0-2 1-2 1-1 0-1 0-2-1v1l-1-1c-1 1-1 0-2 0h-2v-1c-2 0-3 0-5-1-1-1-2 0-4 0 0 0-1 0-1 1-1 1-2 1-4 2l8-8 1-1c1 1 3 0 5 1h2 1 0 7 0c2 0 3-1 4-2h0z"></path><path d="M346 79h1c1 3 1 5 0 7-1-2-1-4-1-6v-1h0z" class="B"></path><path d="M327 80c1 1 3 0 5 1h2 1 0 7 0c2 0 3-1 4-2v1c-2 2-3 3-5 3h-7c-1-1-2-1-4-1-2-1-3 0-4-1l1-1z" class="R"></path><path fill="#9b9a9a" d="M369 70c1 0 3 0 4 1 1 0 1 1 2 1s1 1 2 1l2 2h2c1 0 1 0 1 1 1 0 1 0 2 1 2 0 2-1 4-1s3 1 6 0h2 1l1 1h1 1v1c1 1 2 1 2 3l3 2h1c2 1 7 0 9-1l2-2c0-1 0-2 1-3h0l2-2h0l-1 2c0 2 1 3 0 4 0 1-1 1-1 1v1h-1c-1 0-4 1-5 2h-5c-3 0-5-2-7-2-4-1-7-1-10-1l-1-1-1 1c-1 0-3 1-4 1-2-1-4 0-5-1h-2c-1-1-3-1-4-2h-1 0l-1-1-1-1-3-2h-3 0c-1 1-2 1-3 2-1 0-2 1-3 1-2 0-9 1-10 0l-1-1-1 1h0c-1 1-2 2-4 2h0-7 0-1-2c-2-1-4 0-5-1h1l1-1h0 3 3c2 1 4 0 5 1 2 0 4-1 5-2h2c1-2 7-2 10-2-1 0-2 0-2-1h-2c-1 0-3-1-3-1h0l5-1c2-1 11-3 14-3z"></path><path d="M369 70c1 0 3 0 4 1 1 0 1 1 2 1s1 1 2 1l2 2h2c1 0 1 0 1 1 1 0 1 0 2 1 2 0 2-1 4-1s3 1 6 0h2 1l1 1h1 1v1c1 1 2 1 2 3-2-1-5-1-7-1h-3-3c-3-1-5 0-7 0-5-1-9-5-15-6-3 0-7 1-10 2h0c-1 0-2 0-2-1h-2c-1 0-3-1-3-1h0l5-1c2-1 11-3 14-3z" class="D"></path><path d="M369 70c1 0 3 0 4 1 1 0 1 1 2 1s1 1 2 1l2 2h2c1 0 1 0 1 1 1 0 1 0 2 1 2 0 2-1 4-1s3 1 6 0h2 1l1 1h-2c-2 0-13 1-14 0-5-3-12-6-18-5-3 1-6 1-9 1h0c2-1 11-3 14-3z" class="G"></path><path d="M121 112l4 1c14 4 31 8 41 19 3 4 6 8 8 12 4 7 9 15 10 22v11c0 2 1 4 2 5 0 2 0 4 1 6 0 3 0 5 1 7 0 0 0 1 1 1 0 2-1 4 0 6v1c-1 1-1 1 0 3h0v-1-2h0s1-1 1-2h0c1-3 0-11-1-15-1-2 0-6 0-8 2 2 4 7 5 10l8 21c2 4 4 8 5 12 1 2 0 5 1 8 0 1 0 10 1 11v2 6h0c0-3 1-4 1-7v4h0c1-3 0-8 1-10h1c2 2 2 6 3 8l6 17 11 32 7 21 9 26 10 32 11 30 7 22 4 11 4 13c2 6 4 11 5 17v1c-2 2-4 4-5 8l1 1h-1c-1 3 0 21 0 25v26 9c0 1 1 3 1 4 0 4-1 8-1 11l-3 17v12c0 11-1 21-2 32-1 2-1 6-3 8-1 0-1-1-2-1 0-3 2-6 3-9 0-4-1-8-1-12l1-16c0-9-1-17 0-25 0-3 0-5 1-7 1-3 0-7 0-11 0-12-2-25-2-38-1-6 0-28-2-31 0-1-1-2-2-2h-3v1c-1 0 0 2 0 3v15 7 4 1h0c-1 2-3 6-2 8 2 0 2-1 4 1 0 1 0 1-1 2s-2 1-3 1c-1 2-1 5-1 7l-1 12c0 4 0 8 1 13 0 5 0 10 2 15 1 4 2 7 0 11-1 2-2 3-4 4-2 0-4 0-7-1-1 0-2 2-2 4-1 3-3 9-6 12l-1-1c0-3 3-5 4-8l3-6c0-1 1-2 1-2 0-1-3-3-3-4-2-3-3-5-3-9-1-4 0-8 0-12l2-22c0-2 0-4-1-6s-5-4-3-7c1-1 3-1 3-2 1-1 1-3 1-4 1-7 2-14 2-22l-1-10c0-2 0-3-1-5-1-1-2-3-4-4l-1-1c-2 0-2 0-3 1-2 2-3 4-4 6-1 1-1 2-2 3-1 2 0 11 1 14v4l4-11c0-1 1-5 2-6 0 3-1 5-1 8-1 1 0 3-1 5s-2 3-2 5c-1 2 0 3-1 5-1 4 0 9-1 13l-1 15c0 9 0 42-8 46-1 1-3 1-5 0-1 0-2-1-2-2-1-2-1-5-1-7l1-10c0-4 0-9-1-13 0-3 0-5-1-8 0-4 1-7 0-11 0-3-2-5-2-8v-7l1-21c0-4-1-7-1-11-4 3-9 5-12 8v1c-2 1-1 5-1 7 0 11 0 21-2 32 0 7-3 14-3 21-1 5 0 9 1 14 1 9 3 19 3 29 0 4-2 9-2 14s4 12 1 16c-1 1-2 2-3 2s-2 0-3-1l-3-6c-1-4-2-9-2-13 0-5 1-10 0-16 0-5-2-11-2-16-1-7 0-14-1-21 0-4-3-7-3-11-1-7 0-14 0-20-1-7-3-12-2-18 1-9 4-17 5-26 1-2 2-5 1-8h0l-2 1-4-1-1-1c2-4 5-5 8-8 2-2 3-12 3-15-4 4-8 7-11 12l-2 6c-1 3-1 9-1 12 2-1 4-3 6-4l1 1h0c-1 1-2 3-2 5 0 1-1 2-2 3v1h0c0 2-1 3-2 4l1 3c1 1 0 2 0 4l-1 8c0 8 1 16 0 25-1 6-2 12-2 19 0 3 0 6 1 8 1 4 4 8 2 12-1 0-2 1-3 1s-2-1-2-2c-2-2-3-6-3-9-1-5-1-11-1-16 0-2 1-4 0-7 0-3-1-6-1-9 0-7 1-13 1-20-1 1-1 2-2 3l-4 7c0 1-1 2-1 3-1 3-3 5-4 7-1 3-2 6-4 8 0 1-1 1-2 1l-1-1c0-1 0-1 1-2 1-2 2-3 3-5l10-17c1-2 3-4 4-6s1-6 0-8c0-1-2-3-2-4l1-6c-2 5-6 8-9 12l-21 29c-2 2-5 5-5 7v1c-1 3-2 4-4 6-2 3-3 10-7 12-2 0-2 0-3-1s-1-2 0-4c3-6 9-11 13-16l27-35 7-9s2-2 2-3c1-1 0-4 0-6l1-17c0-6 1-11 1-17-1-4-2-8-2-12s1-8 1-12c0 2-2 5-2 7-1 2-1 4-1 6-1 1-2 2-3 4-1 3 0 8-2 11h-3c-1-1-1-2-1-3 0-2 3-5 4-7 2-4 4-9 5-13l3-6c1-1 0-4 0-6v-20-6l-1-15h0c1-1 1-3 0-3l-1-3c-2 4-4 8-7 12-2 3-6 5-7 8v1l-1 2v2c1 1 1 3 1 4v8c-1 7-1 13-2 19 0 3 0 7-2 9l-2 2c-6-5-6-25-7-33 0-2-1-3-1-5v-3l-2-2h0c1-2 1-2 2-3 1-11 3-21 1-32-1 0-2-1-3-1v-2-2h-1c-1 3-2 7-4 11-1 4-4 8-5 12v10l-3 22v17l-1 18c0 3 1 22 0 24 0 0-1 0-2 1v-1c-1 0 0-3 0-3v-12c0-3 0-7-1-9 0-2-2-3-3-4v-1c-1-2 0-3 1-4h1c1-1 0-3 0-4v-15c0-2 0-4 1-6-1 2-2 4-4 5h-1v-1s3-4 3-5l2-2c1-6 1-13 0-19 0 2-1 5-2 6h-3c-1-4 4-8 5-12 1-2 0-4 1-6v-2c0-1-1-12-2-13-1 3-3 4-4 7-1 1-1 3-2 5-2 3-4 5-5 8-2 3-3 7-6 11 0 1-1 2-2 2s-1-1-2-2c-1-4 6-9 8-13 0-1 1-2 1-3 1-2 1-3 3-4h1c0-5 5-10 8-13l-1-1v-1c3-3 3-21 3-26 0-2 1-6 0-8 0-1-1 0-2-1 0-1-1-2-1-3l3-3c2-9 2-18 1-28v-14c-1 2-2 5-2 7v1 1l-4 4c-1 1-2 3-3 5l-2-1h0v13c-1 1-1 4-1 5v8c0 2 1 5 0 7 0 2-1 3-2 4h-1c-2-5-2-10-2-15s0-10-1-15c0-2 0-5-1-6v-1l-1 1c-1 0-1 0-2-1h0c0-1 1-2 2-3h1c0-2-1-5-2-7l-5 6c-1 1-2 2-4 3 0 1 0 1-1 1l-1-1c1-2 3-3 4-5 2-1 4-3 5-4 2-2 2-2 2-3l-4-26-1-5-1 4c-1 2-3 5-5 5-1 0-1-1-1-2 1-1 2-3 3-4h-1c0-1 1-1 1-1h0c0-1 1-1 1-2 0 0 2-1 2-2v-4h0v-1h0c-1-1-3-3-4-3v-1-1c0-1 1-1 1-2 1-1 0-1 1-2h-1c-1 1-1 1-2 1 0 0 0 1-1 1h-1c-1 2-3 5-6 5 0 1-2 3-2 3v4 9 29c-1 12-2 25-5 36 0 2 0 5-1 7-2 0-2 0-3-1 0-4 2-8 3-11 0-8 1-15 1-23 0-3-1-7 0-10 0-3 1-6 1-9l-1-23c-2 2-4 5-6 8l-3 3c0 1-1 1-2 1 0 0-1 0-1-1-1 0-1-1 0-2 0 0 1-2 2-2l4-5c2-2 3-3 4-5l1-1c1-2 1-5 1-7 0-3 1-7 2-10l3 12c2-2 3-5 6-7 2-2 5-5 7-8v-2c-1-1-4-3-4-3l-1-1c0-1 0-1-1-2-1 0-1 0-2-1h0-2l-1-1h1v-6c-1 0-1-1-2-2l1-1c0-2-1-3-1-5 2 0 2 0 3-2h2c1-1 1-1 1-2l-3-3h-1v-1h2v-1c1-1 2-1 3-2 1 0 1 0 1-1h1l2-2c1 0 2-1 3-1 1-3 1-6 2-9 0-2 0-4 1-7 0-2 2-4 2-6 1-4 1-10 3-13 1-1 1-1 1-2 0-2 2-4 3-6h1l2-2c1-1 2-2 3-4 0-1 1-2 1-3s-1-1-1-2v-2l1-1h2z" class="C"></path><path d="M189 225c0 3 1 7 0 10 0-2 1-4 0-7h-1v1l-1-2 2-2z" class="S"></path><path d="M139 226c0-2 1-3 2-5 1 0 2 1 3 2v2c-2-1-3 0-5 1zm-18-114l4 1c-1 1-2 2-2 3v-3h-2-1v2 1c0-1 1-1 1-2l1 1-1 1c-1 1-2 3-3 4 0-1 1-2 1-3s-1-1-1-2v-2l1-1h2z" class="B"></path><path d="M157 261v-7c0-2 2-4 3-6l1-1v2c-1 1-1 2-1 3-1 1 0 1-1 2v2c0 1-1 1-1 2s0 2-1 3z" class="F"></path><path d="M117 201l2 4 2 10c-1 0-2-2-3-2 0-1 1-3 0-4l-1-2c1-2 0-4 0-6z" class="O"></path><path d="M139 226c2-1 3-2 5-1 1 2 1 3 1 5l-3-2h-2-1v5-7z" class="P"></path><path d="M112 188c0-2 0-5 2-7h1c1 4 3 8 3 12-1-2-2-5-4-6-1 0-1 0-2 1z" class="B"></path><path d="M112 208c2 1 3 0 5-1l1 2c1 1 0 3 0 4 1 0 2 2 3 2v1c-1-1-2-1-3-2v-3-2c-2 0-5 3-6 4-1 0-1 1-1 1 0-2 0-4 1-6z" class="D"></path><path d="M112 213c1-1 4-4 6-4v2 3c1 1 2 1 3 2l2 6-2-2c-1-2-1-3-2-4-1 0-2 0-2-1 0 0-1-1-1-2h-4z" class="U"></path><path d="M184 471c-1 1-1 2-2 3-1-2-1-5-1-7 2-2 5-4 7-5h0c-1 1-2 3-2 5 0 1-1 2-2 3v1h0z" class="E"></path><path d="M112 188c1-1 1-1 2-1 2 1 3 4 4 6l1 12-2-4c-1-2-1-5-2-8v-2c-1-1-1-1-1-2h-2v-1z" class="M"></path><path d="M139 243l5-3v1l1-1v1 3l-4 3h0v1c-1 0-1 0-1 1h-1 0v-5-1z" class="D"></path><path d="M139 243l5-3v1c-2 1-2 3-5 3v-1z" class="N"></path><path d="M188 229v-1h1c1 3 0 5 0 7s-1 5 0 6v3c0 1-1 1-2 2h0c-1-1-1-1-1-2 0-3 1-5 1-8 1-2 0-5 1-7z" class="E"></path><path d="M265 394h1c1 3 1 6 1 8 1 6 3 14 2 20h-1c-3-2 0-17-2-21-1-2-1-5-1-7z" class="S"></path><path d="M157 261c1-1 1-2 1-3s1-1 1-2v-2c1-1 0-1 1-2 0-1 0-2 1-3 1 3 2 7 2 10-1 1-2 2-3 2v1h-1c0 1-1 1-2 1v-2z" class="D"></path><path d="M185 308l1-1v1c2 2 1 11 0 13 0 1 0 1-1 2-1-3-1-5-1-8-1-1-1-3-1-4 0-2 1-2 2-3z" class="E"></path><path d="M87 221c3-2 5-4 8-7 1-2 3-3 4-5 1 2 1 7 1 8h-1v-1l-3 3c1 0 3 2 4 3 0 0 0 2 1 3 0 2 0 3-2 5 0 1-2 3-2 3h-1c0-1 1-1 1-1h0c0-1 1-1 1-2 0 0 2-1 2-2v-4h0v-1h0c-1-1-3-3-4-3v-1-1c0-1 1-1 1-2 1-1 0-1 1-2h-1c-1 1-1 1-2 1 0 0 0 1-1 1h-1c-1 2-3 5-6 5z" class="J"></path><path d="M272 448h1c1 1 1 3 1 4 1 4 1 5-2 8-1 1-1 3-1 4l-1 1s-1 0-2-1c0-2 1-6 2-9 0-1 1-1 1-2 1-2 1-3 1-5z" class="E"></path><defs><linearGradient id="S" x1="114.782" y1="207.816" x2="114.419" y2="189.809" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#S)" d="M112 189h2c0 1 0 1 1 2v2c1 3 1 6 2 8 0 2 1 4 0 6-2 1-3 2-5 1 1-2 2-4 2-6 0-4-2-8-2-12v-1z"></path><path d="M141 337v-1c1-1 1-2 2-3 0-2 1-4 1-6 1-2 0-4 0-6 1-2 1-5 2-8v25l-1 1c-1-1-1-1-2-1h0l-1 1v-2h-1zm-25-107h0c1 0 2-1 2-1h1l2 1v2c-2 3-4 7-7 10l-1-11s1 0 2-1h1z" class="I"></path><path d="M188 195s0 1 1 1c0 2-1 4 0 6v1c-1 1-1 1 0 3h0v-1-2h0s1-1 1-2v5c1 2 0 3 0 5s1 3 1 5-1 3-1 5 0 3-1 4l-2 2 1-32z" class="E"></path><path d="M117 258l1 1v-1c2-2 4-3 6-4h1c-1 1-1 2-2 4 0 1 0 2-1 4-1 1-2 3-3 5 0 0 1 1 1 2l-4 4v-6l1-1c1-2 4-3 4-5v-1c-3 1-4 3-5 6v-9l1 1z" class="F"></path><path d="M142 266h0c0-1 0-1 1-1l2 9c-4 3-6 6-8 11l1-9v-5c1-2 3-3 4-5z" class="L"></path><path d="M160 261l1 1 3-2 4 10h-6c-2 1-4-1-5 0-1-3 1-5 2-8h1v-1z" class="G"></path><path d="M139 233v-5h1 2l3 2h0v7 4-1l-1 1v-1l-5 3v-10z" class="M"></path><path d="M145 230v7c-1 0-2-1-3-1-1-1-1-1-1-2 1 0 2 0 4-1v-3z" class="R"></path><path d="M249 385h1v9 9 5l3 3c0 4 0 8-2 12v1l-1-1v-1c-1-1-1-4-1-5-1-6 0-11 0-17v-10c0-2-1-4 0-5z" class="E"></path><path d="M98 174l3-3h1c1 1 1 2 2 3 1 3 0 4-1 6-2 3-7 2-9 7-1 0-1 2-1 3 0 0 0 2 1 3 0 1 2 1 2 2 1 1 1 3 1 4-1-1-2-1-2-2-3-2-4-7-4-10 1-2 1-2 2-3 1 0 1-1 1-2h1 0l1-2h1c0-1 1-1 1-1v-1c1 0 2-1 3-2-1-1-2-1-3-2z" class="P"></path><path d="M101 176l1 2c-1 1-2 2-4 2 0 1 0 1-1 1 0 0-1 0-2 1l1-2h1c0-1 1-1 1-1v-1c1 0 2-1 3-2z" class="T"></path><path d="M141 247l4-2-2 20c-1 0-1 0-1 1h0l-4 3c0-2 1-5 1-8v-8-3l2-2v-1z" class="F"></path><path d="M157 306c2-4 5-9 5-13v-7h0v2h2l1 1v-1c1 0 2-1 3-1v-1c1 3 1 7 0 10h-1c0 1-1 1-1 2h0c-1 1-1 2-1 3v2h0l-1 2c-1 0-2 0-3 1h0c0 1-1 1-2 2-1 0-1-1-2-1v-1z" class="J"></path><path d="M112 126h1l2-2c-2 5-5 10-6 15-1 2-1 3 0 5 0 3-1 7-4 10 1 2 2 4 2 7-2 2-4 6-7 8 1-3 1-6 2-9 0-2 0-4 1-7 0-2 2-4 2-6 1-4 1-10 3-13 1-1 1-1 1-2 0-2 2-4 3-6z" class="B"></path><path d="M216 407c2 4 2 10 2 14-2 4-5 6-8 9l-1 1-1-1v-11c0-1 2-3 2-4 2-3 4-6 6-8z" class="S"></path><path d="M145 275v4c1 4 0 8 0 12 0 3 0 6-1 8s-1 4-3 5c-1 2-3 4-4 4-2-3-1-8 0-11 0 0 1-1 1-2l2-4 1-1v-2s1-1 1-2c1-1 2-4 3-6h-1c-1 1-1 1-1 3-1 1-1 1-1 2-1 1-1 2-2 3v1c-1 1-1 4-3 5l-1-1c0-1 0-2 1-3 1-2 3-4 4-6 1-1 2-3 2-5 0-1 1-3 2-4z" class="H"></path><defs><linearGradient id="T" x1="112.665" y1="229.689" x2="118.456" y2="214.783" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#T)" d="M112 213h4c0 1 1 2 1 2 0 1 1 1 2 1 1 1 1 2 2 4l2 2c0 1 1 2 1 3-2 2-1 5-3 7v-2l-2-1h-1s-1 1-2 1h0-1c-1 1-2 1-2 1l-2-17s0-1 1-1z"></path><path d="M121 220l2 2c0 1 1 2 1 3-2 2-1 5-3 7v-2l-2-1h-1s-1 1-2 1h0c0-1 1-2 2-2s2-1 3-2 1-1 1-2c-1-2-1-2-1-4h0z" class="J"></path><path d="M157 270c1-1 3 1 5 0h6v2 2c0 2 1 4 0 5v1c0 1 1 5 0 6h0v1c-1 0-2 1-3 1v1l-1-1h-2v-2l1-1v-4h0c-1 2-1 3-1 5-1-4-3-7-4-11l-1-2v-3z" class="K"></path><path d="M168 274c0 2 1 4 0 5v-1l-4 1 2-4h2v-1z" class="D"></path><path d="M164 288c1 0 1-1 1-1h0c1-2 2-3 2-5v-1l1-1c0 1 1 5 0 6h0v1c-1 0-2 1-3 1v1l-1-1zm-7-18c1-1 3 1 5 0h6v2 2 1h-2v-2h-2-2 0l-2 1c-1 0-1 0-2 1l-1-2v-3z" class="L"></path><path d="M157 270c1-1 3 1 5 0h6v2h-5c-1-1-3-2-5-1 0 0 0 1-1 2v-3z" class="D"></path><defs><linearGradient id="U" x1="219.122" y1="277.589" x2="199.51" y2="286.778" xlink:href="#B"><stop offset="0" stop-color="#090906"></stop><stop offset="1" stop-color="#232024"></stop></linearGradient></defs><path fill="url(#U)" d="M209 240v2 6h0c0-3 1-4 1-7v4 10c0 2 1 4 1 7l-1 19v11c0 6 2 11 2 17v6c-1-1-1-3-1-4s-1-1-2-1c0-3-1-6-1-9v-11-10-12c0-1 1-2 0-3v-4c-1-1 0-3 0-3 1-4 0-8 0-11s0-5 1-7z"></path><path d="M90 176v-1c1-1 2-1 3-2 1 0 1 0 1-1 1 2 1 2 1 3 1 0 2-1 3-1 1 1 2 1 3 2-1 1-2 2-3 2v1s-1 0-1 1h-1l-1 2h0-1c0 1 0 2-1 2-1 1-1 1-2 3 0 3 1 8 4 10 0 1 1 1 2 2l1 2c1 1 2 3 1 5v1l-1 1v-2c-1-1-4-3-4-3l-1-1c0-1 0-1-1-2-1 0-1 0-2-1h0-2l-1-1h1v-6c-1 0-1-1-2-2l1-1c0-2-1-3-1-5 2 0 2 0 3-2h2c1-1 1-1 1-2l-3-3h-1v-1h2z" class="O"></path><path d="M89 182h2v1h-2c0 1-1 3-1 4l1 6c0 1 1 3 0 4l-1 1v-6c-1 0-1-1-2-2l1-1c0-2-1-3-1-5 2 0 2 0 3-2z" class="G"></path><path d="M89 193c2 3 3 7 6 9 1 0 2 1 2 2h1c0 1 0 1 1 2v1l-1 1v-2c-1-1-4-3-4-3l-1-1c0-1 0-1-1-2-1 0-1 0-2-1h0-2l-1-1h1l1-1c1-1 0-3 0-4z" class="D"></path><path d="M90 176v-1c1-1 2-1 3-2 1 0 1 0 1-1 1 2 1 2 1 3 1 0 2-1 3-1 1 1 2 1 3 2-1 1-2 2-3 2v1s-1 0-1 1h-1l-1 2h0-1c0 1 0 2-1 2-1 1-1 1-2 3 0 3 1 8 4 10 0 1 1 1 2 2l1 2c-1-1-2-1-3-2h0l-1-1c-2-2-3-3-3-5v-1c-1 0-1-1-1-1v-3h0v-1h-1c0-1 0-2 1-2 0-1 1-1 1-1 1 0 3-2 3-2l1-1-1-1c1-1 2 0 3-2-2-1-3 0-5 0-1 0-1-1-2-1v-1z" class="M"></path><defs><linearGradient id="V" x1="117.635" y1="256.934" x2="123.145" y2="231.928" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#V)" d="M116 257l-2-12c1-2 4-5 5-7s2-4 4-6c0-1 1-1 2-1 0 2 0 5-1 7v3c0 1 1 2 2 3v3c0 1 1 4 0 5l-1 2h0-1c-2 1-4 2-6 4v1l-1-1-1-1z"></path><path d="M126 247c0 1 1 4 0 5l-1 2h0-1c-2 1-4 2-6 4v1l-1-1c2-1 3-4 4-5 2-2 4-4 5-6z" class="D"></path><defs><linearGradient id="W" x1="154.201" y1="320.97" x2="171.755" y2="302.624" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#W)" d="M157 306v1c1 0 1 1 2 1 1-1 2-1 2-2h0c1-1 2-1 3-1l1-2h0v-2c0-1 0-2 1-3h0c0-1 1-1 1-2 3 6 4 12 4 18-2 3-3 5-6 6h-3s-1 0-1 1c0 0 0 1-1 1l-1-1c0 1-1 1-2 2h0-1v-8c1-1 1-1 1-2h-1v1h-1c0-3 1-5 2-8z"></path><defs><linearGradient id="X" x1="146.888" y1="345.077" x2="129.213" y2="315.156" xlink:href="#B"><stop offset="0" stop-color="#151616"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#X)" d="M144 306h1c1 0 1 1 1 2 1 1 1 5 0 6-3 3-3 8-4 12-1 3-3 7-4 10l1 1c-1 3-2 7-3 10-1 2-3 9-5 10v-2-11-3c0-1 2-2 2-2 2-2 2-7 2-9v-1c-1 0-2 2-2 3h0c0-4 1-8 3-12 1 0 2-1 2-2 1-2 1-3 2-5s3-5 4-7z"></path><path d="M236 395l1-1c1 1 2 1 3 2s1 3 1 4c0 4 0 9-1 12 0 0-1 0-1 1-1 1-2 3-2 5 1 1 2 0 3 1v3c0 3-2 6-2 9-1 5 0 10-2 15h-1c-2-2-2-7-2-9-1-8-2-16-1-24 1-1 2-2 2-3l-1-1c0-1 2-4 3-6 0 0 0-1-1-2 1-1 1-2 0-3l1-3h0z" class="E"></path><path d="M236 395h0c2 1 3 2 4 3 0 1-1 1-1 2 0 0-2 3-3 3 0 0 0-1-1-2 1-1 1-2 0-3l1-3z" class="B"></path><path d="M235 401l-2 5c0-2-1-4-1-5 0-2 1-5 1-6-1-4-1-11 0-14 0-4 1-7 2-10 0-4 0-8 1-12s4-8 3-12c0-1-1-1-1-2v-6h1l-1-1v-11c0-2 0-5 1-7h1c1 4 1 8 1 13l-1 9c1 3 2 5 2 8 1 8 0 16-1 24 0 2 0 5-1 7 0 1-1 2-2 2h0c0-3 1-6 1-9-1 2-1 4-2 6s-3 4-2 7h0c0 1 1 3 1 4-1 1-1 3 0 4h0l-1 3c1 1 1 2 0 3z" class="E"></path><path d="M165 320c3-1 4-3 6-6v1l1 5v1c1 1 1 2 1 4v2c0 2 1 6 0 8-2 1-2 4-4 4h-3c-1 1-2 2-2 3h-1c0-1-1-2 0-3v-1-1c-1 1-2 1-3 1 1-2 2-4 2-6l-6 8v-15c-1-3-1-7 0-10v8h1 0c1-1 2-1 2-2l1 1c1 0 1-1 1-1 0-1 1-1 1-1h3z" class="H"></path><path d="M164 328c1-2 2-4 4-5l1 2c-1 0-1 1-2 1v1h1v1c-1 1-2 0-4 0z" class="F"></path><path d="M163 339c1-1 2-2 3-2h1v2h-1c-1 1-2 2-2 3h-1c0-1-1-2 0-3z" class="Q"></path><path d="M173 327c0 2 1 6 0 8-2 1-2 4-4 4-1-1 1-3 1-4s-1-1-1-1h-2v-1c0-2 2-3 3-5v5h1c1 0 1 0 2-1v-5z" class="B"></path><path d="M165 320c3-1 4-3 6-6v1l1 5v1c1 1 1 2 1 4l-1-1c-1 0-2 0-3 1l-1-2c-2 1-3 3-4 5-1 1-1 2-2 3l-1-1c-1-3 5-4 5-7 0-1 0-2-1-3z" class="Q"></path><path d="M172 321c1 1 1 2 1 4l-1-1c-1 0-2 0-3 1l-1-2c2 0 3-1 4-2z" class="K"></path><path d="M172 320h-1c-1 1-3 2-4 3l4-8 1 5z" class="H"></path><path d="M169 339c2 0 2-3 4-4v10c0 3 1 5-1 8-2 4-4 8-7 12-2 3-6 5-7 8l-1 1-1-1c-1-3 0-7-1-10l1-21 4-4c1 0 2 0 3-1v1 1c-1 1 0 2 0 3h1c0-1 1-2 2-3h3zm49 85h0c0 1 1 3 1 4-1 1-1 2-2 2-1 2-2 3-2 6h1c1 0 1-1 2-2v-1l1 1-1 1-3 3c-1 1-1 2-1 3 2-1 3-3 4-4-1 11 0 22 1 33-4 3-7 4-11 6l-3 1 1-16c0-2 0-5 1-6l2-1v-1c1-2 0-3-1-5 3-2 5-5 6-8l-6 7 1-15s1-1 2-1h1c1 0 0-1 1-2 1-2 3-3 4-5h1zm-9-114c1 0 2 0 2 1s0 3 1 4c0 2-1 3-1 5-1 1-1 3-1 5v1c1 1 2 2 3 4s1 7 1 10v7c-1 2 0 4-1 7 0 1-1 2-1 3 0 3 2 7 2 10 1 5 1 10 1 16 0 3 1 6 1 8v2h0c1 1 1 2 1 3v8c-1 2-2 3-3 4l-6 10c1-6 0-12 0-18 2-3 5-5 7-7h0l-7 6v-1c-1-2 0-6 2-8h-1c-1-2 0-4-1-6-1-11-3-21-2-32 0-2 1-4 1-5h0l-1 2c-1-1 0-3 0-4-1-2-1-4-1-6v-10c0-2 0-4 1-6h0l-1-1v-2c0-2 1-3 3-5h0c1-2 1-3 1-5zm-26 135l-1 2h0c0-3 1-6 1-10l1-19c0-3 1-6 1-9 0-2-3-4-3-7v-29-26c1-1 2-2 2-3s0-2 1-3l2-2v-2c0-3 1-5 1-8 0-1-1-1-1-1l1-6 6 105v1c0 2 1 4 0 6l1 1c-4 4-8 7-11 12l-1-2z" class="S"></path><path d="M183 445c1-2 3-5 4-7 2-3 4-8 7-11v1c0 2 1 4 0 6l1 1c-4 4-8 7-11 12l-1-2z" class="B"></path><path d="M194 428c0 2 1 4 0 6l-3 3-3 3 4-8c1-1 1-3 2-4z" class="E"></path><path d="M498 103s1 0 1-1v1c3 1 8 1 10 5v1c0 1 1 2 2 2 1 1 2 2 3 2v2l3 4 1 3v1l1 1h0c1 1 2 2 3 4 0 1 2 2 3 3l3-1v1c0 2 0 3 1 4 2 2 2 6 1 9 0 2-4 5-5 8-1 2 0 4 0 5 0 2-1 3-2 4-1 5 0 11 1 16l1 1c2 1 2 2 3 5 1 0 1-1 2 0v1c0 1 0 2 1 3 2 2 2 3 2 5s-2 4-4 6l-2 2v1c0 1 0 1-1 2 1 1 2 1 2 2l-1 2h0c1 1 1 1 1 2l-1 3-1 1 1 1-2 2 3 3h0v5 1c0 2 1 4 1 7v1c1 1 3 2 4 3 0 2-1 3-2 4l-1-1c-1-2-3-3-5-4h-1v1l-1 1c0-1-1-1-1-1h-2v-3s1 0 1-1h0-2c-1 1-2 2-2 3s1 2 2 2l-1 1h-1-1c1 2 2 5 2 6v1c-2 0-2 2-4 2-1 6 0 11 0 16l-1 20c0 3 0 6-1 9 0 3-1 7-1 11 0 2 0 5 1 7 1 3 0 5 0 8 0 1 1 3 1 4h0c-1 2-1 7 0 8s1 1 1 2l1-1v-5c1 2 3 4 3 5s0 2-1 3h-1l-1 1c3 4 5 9 9 12 0 1 0 1-1 2h0c-2-2-4-5-6-8-1-1-1-3-2-5l-1-1 1-1v-1l-2 1c-1 2 0 6 0 9-1 12-2 23-1 35l1 16c0 3 1 6 1 8 0 4-1 8-2 12v21c-1 5-1 10-1 16 1 4 3 8 4 12s3 11-1 14c-1 0-2 0-3-1-4-4-4-22-4-28 0-5-2-9-1-14 0-5 1-10 1-15v-1c-1 0-1-1-1-1-1-1-2-2-2-3-1 0-1-1-1-2v-1h1v-1c-1-3 0-8 0-12v-7s0-1 1-1c0-2-2-5-2-7h0 2c0-1 0-2-1-3v-1-10-7l1-1v-2c0 1 1 1 2 2h0c0-1-1-2-1-3-2-3 0-9 1-12 0-1 0-1 1-2l-2-16-1-1v-1c0-1 0-3-1-4v4h1l-1 1h-2c-1-1-2-1-2-1l-2 37-2 14c0 3 1 8 0 10-1 0-1 1-2 0-2-2-2-7-2-9s-1-4-1-5 0-1-1-1h0c-3-2-3-6-4-9-1-2-2-4-2-7h-1c-1 5-2 10-1 15 0 2 1 5 1 7-1 6-2 11-3 16s0 10 0 15c-1 2-1 5-1 7 0 1 0 3 1 4l1 2c-1 1-1 0-2 1 1 1 1 3 1 4 0 2-1 5-1 7-1 5-2 11-1 16 0 6 3 11 5 16-1 1-2 1-3 3-1 4-1 8-1 13l-2 17v7c0 4 2 8 1 13 0 3-1 10-5 12 0 1-1 1-2 1-2-1-2-2-3-4 0-4 2-7 2-10l-3-9c-2-13-3-26-2-39v-1c-1 0-1 0-2-1v-3c1-3-2-8-3-11 0-1-1-2-1-3h1l8 17c1-4 1-8 1-12 0-3 0-5-1-7s-2-4-3-5-4-3-4-5c0-1 0-5 1-7 0 3 1 6 2 8s3 4 5 5c0-4 1-8 0-11 0-2 0-3-1-5 0-1-1-2-1-3 1 0 1 0 1-1 1-3 0-7-1-10 0-4 1-7 1-11 0-5-1-9-1-14-1-5-1-10-2-14s-3-7-5-11c-1-3-1-6-1-10-1-2-2-4-2-7-1-1-1-3-1-4h0c-1 1-2 4-2 6-1 5 0 11 0 16 1 5 1 10 1 15 3 2 6 2 8 4v-1h-1c-1 0-2-2-3-3h0v-1c2-1 2-2 3-3l2 3c0 1-1 2-1 2v1c1 0 2 1 2 2l1 1h-1c0 1 0 2-1 3v1c-1 1-1 1-2 0-2-1-5-4-7-7-1 4-3 9-3 13 0 2 2 5 3 7-1 1-1 1-2 1v-1 4 10c0 2 0 5-1 7v1l-3 6 5 4c1 1 2 1 2 2 1 1 1 3 2 5h0l-2 3-2-2c-2-3-2-6-4-9 0-1-1-1-1-2-2 0-3 1-4 1-3-4-3-8-4-12v1h-3l-1-1 1-1c0-1 0-1-1-2 0-1 0-1-1-1v1h1l-1 1c-1 6 0 11 0 17l1 14c0 6 1 11-1 16 0 1 1 1 1 1v1h0-1c-1 0-2 2-3 2h-2c-3 0-7-3-9 0-1 1-2 4-3 5-1 2-2 3-2 5-1 1-1 2 0 4v7c0 2 0 3 1 5-1 0-1 0-1 1-1 3 0 6-1 9v11c0 5 1 10 1 15-1 7-3 14-3 22 0 3 2 6 2 8s-1 5-1 7l-2 14c-1 5 0 11 0 16 1 7 2 13 3 19-3 6-2 11-2 18 0 2-1 4 0 6 0 4 2 14 0 17-1 0-1 1-2 1 0 0-1 0-1-1-3-2-2-12-2-15v-8l-2-23-1-29c0-4-1-7-1-11 0-5 2-10 2-16 1-3 1-7 1-10v-14c0-6-1-13-2-19 0-2-1-5-1-7s1-4 1-5c1-2 1-4 1-7l-1 1c-1 0-1 0-2-1v-2h1l1 1h0l1 1v-1c-1-4-2-7-4-10v-1c0-1 1-4 1-4 0-1 1-1 1-1 2-3 2-9 1-12-1-4-2-7-2-11 1-3 4-15 3-17h0c1-3 1-6 2-9 1-8 1-17 0-25 0-3-1-6-1-10-1-4 0-9-1-13-1-2-2-3-5-4-1-4-2-7-3-11 1 1 2 2 2 4 2 1 3 3 4 4 1-3 1-7 2-10v-3l-2-2c-1-2-3-3-5-6l1 1c0 1 0 1 1 1 2 1 3 3 5 5l1-12c-2-1-4-2-5-3 0-1 1 0 1 0h3c-1-1-1-1-2-1l-1-1h0c1 0 2-1 3 0h0c1-1 0-3 0-4 0 0-1 1-2 1v-1c1-1 1-1 2-1 1-3 1-7 0-10-2-1-4-3-6-4l-2 2c0 1 1 3 1 3-1 2-3 3-3 5-1 2-1 7-1 10 1 2 1 3 3 4l-1 1v22c0 3-1 6-1 9 1 0 2 1 2 1v1l-1-1-1 1v35c0 3 0 6 1 9 0 3-1 6 0 9v1c1 1 3 1 4 3l-1 1c0 1-1 1-2 2l-1-2h0v7c-1 7-1 13-1 20 0 2-1 6 0 8s0 3 0 5c1 5 1 11 0 16v5c1 1 1 1 2 1v2c-1 1-1 0-2 0-1 2-1 5-1 7 0 6 0 11 2 17 0 2 2 4 2 7h0 1v-2l1 1v1c-1 1-1 1-2 1 0 1-1 0-1 0-2 1-2 5-2 7v13c0 4 1 10-1 14l-2 2c-4-3-4-36-4-42 0-3-1-5-1-8s1-6 1-9v-11-16c0-3-1-5-1-7 0-4 0-8-1-11v-1-2c1-5 0-10-1-14 0-6-1-12-1-18h0c-1 3 0 6 0 8-1 1-2 2-2 4v3c1 1 0 2 0 3-1 1 0 4 0 6s-2 4-2 6v9 12c1 2 0 4 0 6 0 7 0 14-1 21 0 3-1 6-3 9l-2 2c-2-2-2-6-3-8-1-6 0-36-1-38-1-1-1-2-2-3-1-2-1-4-2-6 0-1 1-1 1-2l1 1h0c0 1 0 2 1 3h2c0-5 1-12-3-16l-1-1h-2c-1 1-2 3-2 5 1 2 1 3 1 6h0c0 2-1 4-2 6-1 1 0 2 0 4 1 3 1 6 2 9 0 2-1 4-1 5v7c2 13 5 27 6 41 1 5 1 10-1 14-1 1-2 3-2 4-1 1 0 2 0 4 0 1 1 1 1 3l-2 1c0 3 4 8 1 11 0 1-2 2-3 2s-2-1-3-2c-2-2-2-5-3-7-1-7-1-14-2-20l-3-14c-1-9-1-19-3-28v2l-1-2v-3c-1-1 0-2-1-3v3h0c0 1 0 1-1 2v3h0l-1 1c1 1 0 1 0 2h0v2 1h-1 0 0c-1 4 0 9-1 14s-5 9-5 14c-1 4 0 7 0 11l-1 21v10l-3 25v10c0 2 0 4-1 5s-1 1-2 1c-1-1-1-2-2-4v-20c1-14 2-29 1-44 0-3 0-6-1-9s-2-5-3-8v-10l-1-25c-1-2-1-5-2-8v7l1 11v26c1 7 1 14 1 21l-1 11-1 25v27l-1 38-1 13v7c0 2-1 2-2 3h0c-1-1-2-2-2-3-1-3 0-6 0-9-1-7-2-15-1-23 0-2 1-4 1-7v-9-23c0-7 0-14-1-21 0-2 1-4 0-6l-1-23v-37c0-6-1-14 0-20 3-18 9-35 13-52l47-144 10-27c1-5 2-10 5-14 1 1 2 3 2 5 0 1 0 2 1 3v13c0 2-1 7 0 9 0 1 1 1 2 2 1-1 1-1 1-2v-4-3c0-1-1-3-1-4l1-12c0-9-2-18 0-26 1-4 3-8 4-12l6-16c1 2 2 4 2 7 1 3 0 6 0 10l-1 13v8c0 2 0 2 1 3h1l1-1v-5-15c0-5 0-12 1-17 0-2 1-3 1-5s-1-4-1-6c-1-1-1-2-1-3 1-4 3-8 5-12 2-5 3-11 6-16 1 2 2 5 2 7-1 4 0 9 0 13 0 3 1 5 0 7v12 13c0 3 1 5 2 8 1-1 2-2 2-3 0-2-1-6-1-7v-10c0-2-1-5-1-7v-19c0-4 2-7 2-10 0-7-1-14 0-20 1-5 4-11 6-16 4-10 8-19 18-24 4-2 8-3 12-4 8-4 16-8 23-14h4v-1h1 0v-1h4 1z" class="C"></path><path d="M370 470h2c1 1 1 3 1 4h-1c-1-2-2-2-2-4zm50-127c0-1 0-2 1-4h1 1v5l-3-1z" class="E"></path><path d="M483 298c0-2-1-7 0-9l1 1c1 2 1 5 1 7l-2 1z" class="H"></path><path d="M436 406h0l2 7-1 1h0l-1 2c-1-1 0-7 0-10z" class="K"></path><path d="M514 247l1-9c1 1 1 5 2 6h1v1c-2 0-2 2-4 2z" class="L"></path><path d="M508 344v10 5h0c-2-3-1-10-1-13 0-1 0-1 1-2z" class="E"></path><path d="M486 346l1-2h1s1 1 2 1h0l1 7h-1c-1-2-3-4-4-6z" class="S"></path><path d="M420 343l3 1h-2 0l2 1v3c0 2 0 3-1 4h-1c-1-2-1-6-1-9z" class="H"></path><path d="M380 331c1 0 1 0 1 1 2 2 2 8 1 11-1 0-1 0-1-1-1-2-2-9-1-11zm-34 139h1c1 3 1 7 1 10h-1-1-1c0-4 0-7 1-10z" class="E"></path><path d="M484 340c1 0 2 0 2-1h1 3v6h0c-1 0-2-1-2-1h-1l-1 2-1-2c-1-1-1-2-1-3v-1z" class="I"></path><path d="M468 332l1 1c1 0 2-1 3-2v13 7l-1-9-2-2-1 4v-4c1-3 0-5 0-8z" class="L"></path><path d="M499 297h1c0 2-1 1-1 3v1c2 3 1 6 3 8h1v-2-1c1 2 0 4 0 5v4h0c0-1-1-1-1-2-2-1-2-2-2-4-1-1-1-2-2-3-1-3 0-7 1-9z" class="E"></path><path d="M516 238v-2c-1-1 0-3 1-5 1-1 2-1 3-2 2 0 2 1 3 2 1 0 2 2 2 3l-1 1v1l-1 1c0-1-1-1-1-1h-2v-3s1 0 1-1h0-2c-1 1-2 2-2 3s1 2 2 2l-1 1h-1-1z" class="O"></path><path d="M523 231c1 0 2 2 2 3l-1 1v1l-1 1c0-1-1-1-1-1v-4c0-1 1-1 1-1zm-55 113l1-4 2 2 1 9v1c0 1-1 3-2 4h-1c-1-4-1-9-1-12z" class="H"></path><path d="M505 390c1 4 0 8 0 12 1 2 1 5 2 7v7 1c-1 0-1-1-2-1 0-2 0-3-1-5v-1c-1-3 0-8 0-12v-7s0-1 1-1zm0-7c0-1 0-2-1-3v-1-10-7l1-1c1 5 1 11 1 16 0 4 1 9 0 13 0-2-1-5-1-7zM346 511c0-4 1-7 2-11v-2c0-2-1-3-1-5-2-3-2-8-2-12h1 2c1 2 0 7 1 10 0 4 2 8 2 12v2l-1-2v-3c-1-1 0-2-1-3v3h0c0 1 0 1-1 2v3h0l-1 1c1 1 0 1 0 2h0v2 1h-1 0z" class="E"></path><path d="M468 332c0-4 0-11 2-15v-3-1h2 0v9 1c-1-1-1-2-1-3h-1c-1 3 2 3 2 5 1 2 0 5 0 6-1 1-2 2-3 2l-1-1z" class="F"></path><path d="M500 326c-1-6-2-12-2-18l2 4c1 3-1 4 2 6 1-1 1-1 2-1 1 3 2 8 2 11l-1-1v-1c0-1 0-3-1-4v4h1l-1 1h-2c-1-1-2-1-2-1z" class="I"></path><path d="M524 207l2-1 1 1h0 0c1 1 1 1 1 2l-1 3-1 1 1 1-2 2c0-2-1-3-2-4 0-1 0-1-1-2 0 2-1 9 0 10-1 2-1 3-2 4v1h0l-1-1c-1-2 0-3 0-4 1-3 0-6 0-9 0-1 1-3 2-3l2 1c1-1 0-1 1-2h0z" class="N"></path><path d="M527 207h0 0c1 1 1 1 1 2l-1 3-1 1h-1c0-2 2-5 2-6h0z" class="M"></path><path d="M368 359h1c1 1 2 3 2 4 0 2 1 8 0 9-1-1-1-1-2-1l-2 1c0 1-1 1-2 1 0 1 0 0-1 0v-3c0-3 2-9 4-11z" class="S"></path><defs><linearGradient id="Y" x1="479.848" y1="301.673" x2="491.264" y2="312.816" xlink:href="#B"><stop offset="0" stop-color="#212023"></stop><stop offset="1" stop-color="#3d3b3b"></stop></linearGradient></defs><path fill="url(#Y)" d="M485 297c1 1 2 3 2 4 1 1 0 3 0 4 0 5 1 10 1 14-1 0-1 0-2-1 0-1-1-2-2-3h0c-1-2-1-4-1-6 1 0 1 1 2 1-1-3-3-6-2-8v-2-2l2-1z"></path><path d="M520 225v-1c1-1 1-2 2-4-1-1 0-8 0-10 1 1 1 1 1 2 1 1 2 2 2 4l3 3h0v5 1c0 2 1 4 1 7-1 0-1 0-2-1l-4-4c-1 0-3-2-3-2z" class="M"></path><path d="M523 227h0c1-1 1-2 1-4 1-1 1-4 1-6 2 5 2 9 3 14h-1l-4-4z" class="K"></path><path d="M484 315h0v8h0l2-2h2c1 2 1 5 1 7l1 11h-3-1c0 1-1 1-2 1v1c-1-2-1-5-1-7v-13c0-2 0-4 1-6z" class="J"></path><path d="M483 334h0c1 1 1 1 2 1 0 1 1 1 2 2 0 1-1 1-2 2h-1v1 1c-1-2-1-5-1-7z" class="G"></path><path d="M509 115c1 0 2 0 2 1 2 2 3 6 4 9l4 7c-1 6 0 10 0 16v15l1 15h0c1 1 1 1 1 2v1l1 2s1 3 2 4h1 1v1c1 0 1 1 2 1v1 1c0 1 0 1-1 2-1 0-1 0-2 1 0 0-1 0-1 1-1 0-2 1-3 2v1c-1 2 0 3 1 4v1c1 0 1 2 1 3-1-1-1-1-1-2-1-2-1-2-2-3s-1-3-1-5h1v-1c1-3 4-3 7-4 0-1-2-2-3-3h0c-1 0-2-2-3-2 0-1 1 0 0-1s-1-1-1-2c0-2-1-3-1-5h0 0v-1h0v-2c1 0 0-1 0-2h0v1h-1v-5-1-1-1h-1c-1-4 1-9 1-14 0-2-1-5-1-7v-10l1-1s0-1-1-1v-2-1l-1-1h0l-1-2v1l-1-1v-3h0c0-1-1-2-1-4-1-1-1-2-2-4-1 0-1 0-2-1h0z" class="B"></path><path d="M485 344l1 2c1 2 3 4 4 6h1v10 7c1 1 1 2 0 3v1c0-1 0-1-1-1h0c-3-2-3-6-4-9-1-2-2-4-2-7v1l1-3c0-1-1-2-2-3-1-2 0-4 0-6h0 1 0 0l1-1z" class="E"></path><path d="M485 344l1 2c1 2 3 4 4 6-1 1-1 1-2 1s-1 0-2-1c-1-2-3-5-3-7h1 0 0l1-1zm0 10v1c1 1 3 3 3 5l-1 1c0 3 2 6 3 9h1v-1c1 1 1 2 0 3v1c0-1 0-1-1-1h0c-3-2-3-6-4-9-1-2-2-4-2-7v1l1-3z" class="B"></path><path d="M441 345v1c1 2 1 6 1 8 0 6 0 11-1 16 0 4-1 8-1 12 0 3 1 5 1 7 1 3 1 7 1 10v7c0 1 0 3-1 4v-1c-2-7-4-13-4-21 0-2-1-4 0-6 0-6 2-12 3-18 0-6 0-13 1-19zm-60 0c1-1 0 0 1 0 1 2 1 5 1 8v20 21c1 0 1 0 2 1 1 2 0 7 0 10 1 3 3 5 3 8 0 5-2 9-3 14v1h-1c-1-3-1-8-1-11 0-2 0-4-1-6 0-4 1-8 1-12l-1-30-1-10c0-2 1-3 1-5-1-3-1-6-1-9z" class="E"></path><path d="M498 103s1 0 1-1v1c3 1 8 1 10 5v1c0 1 1 2 2 2 1 1 2 2 3 2v2l3 4 1 3v1 2c0 1 0 4 1 4l1 1c0 1 0 1 1 2 0 1 2 2 3 3s0 6 0 8h0v1h0 1 0l-1 1-1 1h-1c0 1-1 3-1 4-1 4-2 8-2 12 0 1 1 2 1 3v3c1 4 1 7 2 11 0 1 2 6 3 7 2 1 4 2 5 4v3c-1 1-2 2-3 2-4 3-4 4-3 8v4h0l-1-1c0-1 0-3-1-3v-1c-1-1-2-2-1-4v-1c1-1 2-2 3-2 0-1 1-1 1-1 1-1 1-1 2-1 1-1 1-1 1-2v-1-1c-1 0-1-1-2-1v-1h-1-1c-1-1-2-4-2-4l-1-2v-1c0-1 0-1-1-2h0l-1-15v-15c0-6-1-10 0-16l-4-7c-1-3-2-7-4-9 0-1-1-1-2-1l-1-1h1l-2-2h0 1c-1-1-1-2-1-3h0c-1 0-1 0-2-1v-1h-1v1l-1-2h-2-1c0-1-1-1-1-2-1 0-1-1-2-1h1z" class="Q"></path><path d="M518 122c0 1 0 1-1 2h-1c0-2-1-3-1-5h2l1 3z" class="B"></path><path d="M420 356h1c1 2 1 6 1 8 0 5 2 10 2 15 1 4 1 9 0 13l-1 1h0c3 3 1 10 2 14 1 3 0 6 0 8v11c0 4 0 8-2 11l-1 1h0c-1-3-1-5-2-8-1-7-1-14 0-20v-14l-1-15v-7c0-3 1-6 1-9 1-2-1-6 0-9z" class="E"></path><path d="M518 123l1 1h0c1 1 2 2 3 4 0 1 2 2 3 3l3-1v1c0 2 0 3 1 4 2 2 2 6 1 9 0 2-4 5-5 8-1 2 0 4 0 5 0 2-1 3-2 4-1 5 0 11 1 16l1 1c2 1 2 2 3 5 1 0 1-1 2 0v1c0 1 0 2 1 3 2 2 2 3 2 5s-2 4-4 6l-2 2v1c0 1 0 1-1 2 1 1 2 1 2 2l-1 2h0l-1-1-2 1v-4c-1-4-1-5 3-8 1 0 2-1 3-2v-3c-1-2-3-3-5-4-1-1-3-6-3-7-1-4-1-7-2-11v-3c0-1-1-2-1-3 0-4 1-8 2-12 0-1 1-3 1-4h1l1-1 1-1h0-1 0v-1h0c0-2 1-7 0-8s-3-2-3-3c-1-1-1-1-1-2l-1-1c-1 0-1-3-1-4v-2z" class="P"></path><path d="M365 377c1 0 1 0 2 1 0-1 1-1 2-2 1 1 2 1 3 2 2 8 1 16 1 24-2-4-4-6-7-9l5 9c2 3 2 8 2 11v4c-3 0-3-2-5-3h0c0 2 3 4 5 5v9 20l-1 1c0-1 0-1-1-1 0-1-1-3-1-4-2-7-2-14-3-21 0-4-1-7-1-11 0-2 1-5 1-7-1-1-1-2-2-4 0-3-1-8 0-11 0-2 0-3 1-4s2-2 2-4c0-1-2-2-3-3v-2z" class="E"></path></svg>