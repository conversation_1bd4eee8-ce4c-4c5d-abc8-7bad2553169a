<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="168 90 694 800"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#deddda}.C{fill:#d3d2d0}.D{fill:#e2e0df}.E{fill:#b5b3b2}.F{fill:#c8c6c5}.G{fill:#acaaa9}.H{fill:#979695}.I{fill:#e9e8e4}.J{fill:#c0bfbe}.K{fill:#727171}.L{fill:#2c2b2c}.M{fill:#393838}.N{fill:#9e9d9c}.O{fill:#bcbaba}.P{fill:#1c1b1b}.Q{fill:#e3e2de}.R{fill:#242323}.S{fill:#8f8d8d}.T{fill:#7b7a79}.U{fill:#868585}.V{fill:#60605f}.W{fill:#4a4949}.X{fill:#a5a3a3}.Y{fill:#555454}.Z{fill:#696868}.a{fill:#424141}.b{fill:#090908}</style><path d="M815 370l1 1c0 2 0 2-1 3h-2v-1c0-1 0-2 2-3z" class="B"></path><path d="M267 690h1l2 2-1 1c-1 0-1 1-2 0l-1-1 1-2z" class="U"></path><path d="M380 741l7 1-7 2c-1-1 0-2 0-3z" class="X"></path><path d="M421 828h0c1 3 2 5 1 7h-1 0c-1-2-1-5 0-7z" class="K"></path><path d="M195 371h1c1 2 1 2 1 4-1 0-1 0-3-1 0-2 0-2 1-3z" class="C"></path><path d="M303 436h1c1 2 1 3 1 4l-1 1h-2l-1-1 2-4z" class="U"></path><path d="M835 271l-1 8-2 2v-3c1-2 2-5 3-7z" class="F"></path><path d="M457 776l8 2c-3 1-7 0-10 1v-1l2-2z" class="M"></path><path d="M657 629l-1-3c-1-2-2-4-3-7 2 1 3 3 5 4 0 1-1 3-1 4v2z" class="C"></path><path d="M509 381v1 1h1l1 1h-7v-2h1l1-1 1 1c1-1 1-1 2-1z" class="P"></path><path d="M460 769c2 0 5 1 6 2s1 1 1 2c-1 1-5-1-6-1l-1-3z" class="V"></path><path d="M650 734c3-1 6-2 9-2h0v2h-2v1c-2 0-5 0-7-1z" class="U"></path><path d="M607 284c1-1 0-1 1-1l4-1v-1c2 0 3 0 4 1-1 1-4 3-6 3l-3-1z" class="G"></path><path d="M504 466l1-2h1v10c-1 0-2 1-2 2l-1 2c0-4 1-8 1-12z" class="L"></path><path d="M348 713c-3-1-7-2-10-4-1-1-2-2-2-4h1c4 3 7 6 12 7v1h-1z" class="W"></path><path d="M741 669c2 1 2 2 2 4 1 1 0 1-1 2s-2 1-4 1v-1c1-1 1-2 1-3 2-1 2-2 2-3z" class="B"></path><path d="M681 462h2v1c-1 2-2 4-3 7 0 0-1 0-1 1l-1-1c0-3 1-6 3-8z" class="D"></path><path d="M574 765l-14 4h-3c1 0 3-1 4-2v-1l-3 1h-1 1l8-3 2 2h1c1-1 3-1 5-1z" class="V"></path><path d="M642 737c1 0 1 0 2-1 1 0 1-1 2-1s1 0 2-1h2c2 1 5 1 7 1l2 1-1 1c-6-1-11 0-16 0z" class="M"></path><path d="M320 461h0c2 0 3 0 5 1 2 2 2 5 2 7v1h-1c-1-1-2-3-2-4-2-2-3-3-4-5z" class="D"></path><path d="M699 651l1 1h2l-12 10c3-3 5-8 9-11z" class="Y"></path><path d="M750 443c2 2 2 3 2 6 0 1 0 2-1 4 0 2 0 4-2 5l-2 1c2-6 3-10 3-16z" class="T"></path><path d="M527 476c-1-4 0-10 1-14 2 4 2 8 2 12l-3 2z" class="N"></path><path d="M474 462v-1l1-1v-1c1-4-1-9 0-13h1l1 1v3 12h-3z" class="Z"></path><path d="M320 680c-2-2-4-4-7-5-2-1-6-2-9-3 7 0 12 1 17 5h-2-1c1 1 2 1 2 3z" class="O"></path><path d="M627 546h-2c0-1 1-2 1-3s0-1 1-2v-2-1h2l4 2c-2 0-2 1-3 2s-1 2 0 3l1 1h-1-3z" class="P"></path><path d="M602 802h3c0 1 1 2 1 3 1 3-1 7-2 9-1 1-1 1-2 1l2-4c0-3-1-4-2-7h0v-2z" class="D"></path><path d="M724 468c0-2 0-4 1-6 0-4 1-8 2-11l1 12h-1l-3 5z" class="V"></path><path d="M746 425h1l1-1c0 1 0 2-1 3 1 1 1 1 1 3-1 1-1 0-3 0h-1l-5-5h7z" class="B"></path><path d="M238 230c5 2 11 5 15 10-1-1-2-1-3-2s0 0-1 0v1h-1-1c-2-2-4-4-7-5-1-1-2-2-2-4z" class="V"></path><path d="M504 284c2 3 4 5 6 8v2c-1 1-1 2-2 3l-4-7-1-2c1-1 1-3 1-4z" class="B"></path><path d="M505 346l3 3h1c1 2 1 4 2 6 0 2 0 5-1 7h-1l-4-16z" class="W"></path><path d="M260 425c3-2 5-3 9-3v1c-2 2-5 4-7 7-2 0-2 0-4-1 0-2 1-2 2-4z" class="B"></path><path d="M719 506h1l4-3v2c-1 1-2 2-2 4l-5 3v-3c-3 1-8 4-11 4 1-2 5-3 7-4l6-3z" class="a"></path><path d="M269 540c1-1 2-1 3 0v1s-4 4-5 4c-1 2-2 4-2 6l-1 1h0c-1-2-1-5 0-7s3-4 5-5z" class="D"></path><path d="M740 293c1-1 1-1 1-2 1-1 2-2 3-4l1 3c-1 1-1 2-1 3l3 3h1l1-2-1 2c-1 2-7 1-9 1 1-2 1-3 1-4z" class="W"></path><path d="M432 775c2-2 5-3 8-3h1c-1 1-2 1-3 1v1h1l-3 3c-3 1-4 4-6 6v-1h-1c0-2 2-3 3-5v-2z" class="N"></path><path d="M743 540c1-1 2-1 3 0 2 1 4 3 5 5 1 3 1 5 0 8-1-1-1-5-2-6-1-2-5-4-6-6v-1z" class="D"></path><defs><linearGradient id="A" x1="202.407" y1="203.51" x2="194.613" y2="205.088" xlink:href="#B"><stop offset="0" stop-color="#3d3e3e"></stop><stop offset="1" stop-color="#5a5958"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M206 198l-2 4-2 4c-5 1-9 4-13 5v-1s2-2 3-2c5-3 9-6 14-10z"></path><path d="M447 383c-2-3-4-6-6-8 3 1 5 1 8 3l-1 1h1c1 1 1 2 2 4s1 4 1 6c-1-3-3-4-5-6z" class="K"></path><path d="M313 504l11 14-2 1-12-14 3-1z" class="B"></path><path d="M503 288c-1-2-3-5-5-6-4-4-7-5-12-5v-1c2 0 3-1 4-1 6 1 11 5 14 9 0 1 0 3-1 4z" class="Q"></path><path d="M453 762v-1c-2-1-5-1-7-2l-2-1c2 1 4 1 6 1 2 1 3 1 5 0h1 1 2l1 1c2 0 3 1 4 2l1 1-1 1c-3 0-8 0-11-2z" class="Y"></path><path d="M796 337c4-2 6-6 10-8 1 0 1 0 3 1 1 2 0 4 0 6h-3l-1-1 1-2c-2 0-2 0-3 1s-2 2-3 2-3 1-4 1z" class="F"></path><path d="M852 236c3 4 4 8 4 12 0 1 0 1-1 2l-2-1c0-1 0-3-1-4-1-3-2-5-3-7 1-1 1-1 1-2l1 1 1-1z" class="B"></path><path d="M708 681c1-1 3-1 4 0h2 3v1c2 0 3 1 3 3h0-2c1 1 1 1 1 2h-1c-1 0-1 0-2-1 0 0-1 0-2-1-1 0-2 0-3-1-2 0-3-1-4-1l1-1h2l-2-1z" class="P"></path><path d="M439 803c5 4 10 7 17 6h4c-3 2-6 3-9 4-5-4-10-3-12-10z" class="K"></path><path d="M796 337c1 0 3-1 4-1 2 3 2 7 2 11 0 1 0 1-1 2h0c-1-1-1-1-2-1v-3c-1-3-2-4-4-6l-1-1c1-1 1-1 2-1z" class="N"></path><path d="M722 534h2c-7 4-18 8-25 7-1 0-2 0-3-1h1c8 0 17-4 25-6z" class="M"></path><path d="M447 383c2 2 4 3 5 6l2 2h-1l-4-4c0 2 2 3 3 6-2 1-5 1-7 1 0-4 0-8 2-11z" class="Z"></path><path d="M206 198c1-2 3-4 4-6 2 1 2 1 3 2 0 3-3 6-6 8-2 2-4 3-5 4l2-4 2-4z" class="L"></path><path d="M422 282c-1 0-4-2-5-3 3-2 9-3 13-4 2 1 4 1 6 3-3 0-6 1-9 1h-1c-1 1-3 2-4 3z" class="K"></path><path d="M520 351v18l1 2h1 0v1c1 2 0 4 0 7h2c-1 0-1 0-2 1-1 0-1-1-2-1 0-3-1-4-1-6v-2c-1-1-1 0-1-1v-1c-1-1 0-5 0-6 0-2 0-2 1-3s1-4 0-5c0-2 0-2 1-4z" class="L"></path><path d="M763 276c1 0 3 1 4 0s2-1 3-1l1-1-2 2c-2 2-5 6-7 7-1 0-1 0-2 1-2 2-3 3-5 3 2-4 5-8 8-11z" class="E"></path><path d="M278 465c-1-5-1-10 1-15 1 6 2 11 4 16l-2 1c-1 2-1 5-2 7l-1-1h1l-1-8z" class="U"></path><path d="M278 465v-6l1 1v4 2c1 0 1 1 2 1-1 2-1 5-2 7l-1-1h1l-1-8z" class="H"></path><path d="M373 741h7c0 1-1 2 0 3-4 1-6 3-9 5v-2l-2 3-1-1v-3-1c2-1 3-2 5-3 1-1 1 0 0-1z" class="E"></path><path d="M368 745c2-1 3-2 5-3h1 1l1 1c-1 2-3 3-5 4l-2 3-1-1v-3-1z" class="D"></path><path d="M660 729c1 1 1 0 2 1l1 1c-7 0-13 1-20 2l-8 2c-1 0-2 1-3 1l-2-1c3-1 6-2 9-4 2 1 5 0 7-1h4c3 0 7 0 10-1z" class="U"></path><path d="M743 276v3h0v5l2-3 6-6v1l-1 2c-3 3-4 6-6 9-1 2-2 3-3 4 0 1 0 1-1 2 0-7 0-11 3-17z" class="M"></path><path d="M159 250c-1 0-2-1-3-1 0-3 1-5 2-7 1-4 2-8 6-10l3 3h-2-1 1c-3 3-5 8-6 12v3z" class="C"></path><path d="M758 356c1 1 1 1 2 1 2 4 4 9 8 11l2 2-1 2-1 1c-2-1-2-1-3-2-4-3-6-9-7-15z" class="K"></path><path d="M600 705h1c2 1 3 2 4 3 2 3 3 6 2 9-1 6-4 11-9 15h-1-1c4-4 8-8 9-14 1-5-2-9-5-13z" class="D"></path><path d="M249 239v-1c1 0 0-1 1 0s2 1 3 2c6 4 11 13 14 21h-1c-2-1-3-1-4-3l1-1c-2-4-3-7-7-10-2-3-4-5-6-8h-1z" class="Y"></path><path d="M436 278l6 4c-6 0-11-1-16 3-2-1-3-2-4-3 1-1 3-2 4-3h1c3 0 6-1 9-1z" class="J"></path><path d="M434 799l3 2 2 2c2 7 7 6 12 10-5 0-10 0-14-2-1-2-3-3-4-5l1-1c2 3 3 5 7 5h0c-3-3-6-6-7-10v-1z" class="H"></path><path d="M646 574h0c3-2 5-5 6-8v-1-1c1-1 1-4 0-6h-1v-1c2 1 3 3 3 5 1 4 0 8-3 11s-6 5-10 5c-3 0-5-1-8-2h1 2c4 1 7 1 10-2zM227 228c4 0 7 1 11 2 0 2 1 3 2 4 3 1 5 3 7 5h-1c-1 0-1 0-2-1h-1v1c-5-2-8-6-13-8l-3-1v-2h0z" class="K"></path><path d="M227 392c1-2 0-4 2-6-1 3-1 3 0 5 1 5 3 10 4 15 2 4 4 8 4 13l-1-1h0c-4-8-7-17-9-26z" class="X"></path><path d="M321 563c0-1 1-1 2-1l2 2v1c-1 1-2 2-3 2-4 4-6 7-8 12 0 1-1 2-1 3h0c-1-2 0-4 0-6 1-5 3-11 8-13z" class="D"></path><path d="M697 517l4-4c1 0 2 0 3 1-4 3-10 7-12 11-1 1-1 2-3 2 0 1 0 1-1 3 1 0 1 0 2 1-3 2-7 4-11 5 2-3 6-6 9-9l9-10zm-54 55c3-2 4-3 5-6s0-5 0-8c0-1 1-1 2-2l1 1v1h1c1 2 1 5 0 6v1 1c-1 3-3 6-6 8h0-4 0l1-2z" class="P"></path><path d="M783 319l5 4h0v-1h1v2l1 2c1 2 5 7 3 9h-1l1-2-1-2h-1 0c0 1 0 2-1 3-2-4-2-6-5-9h-1c1 2 1 2 1 5 0-1-1-1-1-2l-1-1c0-1-1-2-1-2v-1c-1-2-3-3-4-4v-2c1 2 3 3 4 3l1-2z" class="a"></path><path d="M442 765l18 4 1 3c-5-1-10-1-14-2-5-1-9-3-13-5h4c0 1 1 1 1 1 2 0 2 0 3-1z" class="T"></path><path d="M463 212c2 1 2 3 3 5v1c0 3 0 6-2 9s-5 4-8 5c-2 0-4 0-6-1 3 0 6 0 8-1h1l3-3h0l-2 2h-1c-1 1-2 1-3 1-1-1-1-1-2-1h-1c-1 0-2-1-3-1h0l1-1h2c1 1 3 1 5 1l1-1c1 0 1-1 2-2h0c1-2 3-4 2-6 0-2-1-4 0-7h0z" class="R"></path><defs><linearGradient id="C" x1="211.451" y1="314.112" x2="199.329" y2="311.216" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#646463"></stop></linearGradient></defs><path fill="url(#C)" d="M195 300c2 1 2 1 3 3 1 1 5 3 5 5 0 1 1 2 2 3l3 3h1c2 1 4 1 5 3l1 1 1 1c-7-1-13-4-18-8l3-3-1-1c-3-2-4-4-5-7z"></path><path d="M659 732c5 0 12 1 16 5 1 1 2 3 2 4-1 0-2 0-3-1-3-2-8-3-12-4h-3l-2-1v-1h2v-2h0z" class="G"></path><path d="M452 389c0-2 0-4-1-6s-1-3-2-4h-1l1-1 3 2v1c2 2 3 5 5 7 2 4 6 5 7 9-4-2-8-3-12-4-1-3-3-4-3-6l4 4h1l-2-2z" class="N"></path><path d="M590 312c3 0 7 0 10 1 9 2 13 8 17 15l-6-5c-6-5-12-9-20-10-4 0-8 1-12 1 4-2 7-2 11-2zm-18 497c4 2 7 0 11-1l-1 1-1 1c2 1 3 2 4 3-2 1-4 1-6 1-7 1-13-2-18-6 4 1 7 2 11 1h0z" class="B"></path><path d="M541 440l-2-2v-4c2 5 5 12 10 14 4 2 6 2 10 0 2 0 3-2 4-3 1 1 1 1 1 2l-5 7-1-1-1 1v1l-1-1s-1-1-2-1h-1l1 1h-1c-1-1-2-1-4-2l-1-1c-2-4-5-7-7-11z" class="O"></path><path d="M745 273l3-6 1 1c0 1 0 0 1 1l1 1v2l2-2c2-1 2 0 4 0-2 1-4 5-6 6v-1l-6 6-2 3v-5h0v-3c1-1 1-2 2-3z" class="a"></path><path d="M745 273l3-6 1 1c0 1 0 0 1 1l1 1v2h0c-2 1-3 2-4 3h-1l1-2h-1-1z" class="Y"></path><path d="M627 546h3 1l-1-1c-1-1-1-2 0-3s1-2 3-2c3 0 5 1 8 1h-4l1 1-1 1c0 2 1 1 0 3l1 1c-1 0-3 0-3 1v1c-1 0-2 0-3 1h0l1 1c1 0 1 0 1 1l1 1c-2 0-4 0-5-1l1-1-1-1h-2c0-1-1-1-1-2 2 0 2 1 4 1-2-1-3-2-4-3z" class="M"></path><defs><linearGradient id="D" x1="635.563" y1="740.003" x2="646.521" y2="746.863" xlink:href="#B"><stop offset="0" stop-color="#5e5d5e"></stop><stop offset="1" stop-color="#848381"></stop></linearGradient></defs><path fill="url(#D)" d="M648 741c1 0 2 1 3 2-1 1-1 0-2 2v2c-1-1-2-1-3-2l-1 2c-7-3-12-2-19-1h1 1v-2-1c3-1 8-1 12-1 2-1 5 0 8-1z"></path><path d="M213 228c5-1 9-1 14 0h0v2l3 1c5 2 8 6 13 8 4 2 7 5 10 8h0c-1-1-2-1-3-2-3-2-6-5-10-6h-1s-2-2-3-2c-3-2-7-4-11-6-2-1-4-1-6-1s-5 0-6-2z" class="H"></path><defs><linearGradient id="E" x1="226.956" y1="364.61" x2="229.033" y2="384.285" xlink:href="#B"><stop offset="0" stop-color="#4f4f4e"></stop><stop offset="1" stop-color="#7d7d7e"></stop></linearGradient></defs><path fill="url(#E)" d="M227 392c-2-8-2-18 0-26 0-2 1-4 2-5 1 1 1 1 1 3 1 3-1 8-1 12v15c-1-2-1-2 0-5-2 2-1 4-2 6z"></path><path d="M212 432c5-1 6 6 9 9 2 2 5 4 7 6h0l-1-1c-5-1-11-3-14-6-2-2-3-3-3-6 0-1 1-1 2-2z" class="Q"></path><path d="M695 677h0l3-3c3-3 7-5 10-7 2-1 3-2 5-3h3c-5 4-10 8-14 12-2 2-5 6-8 7h0l1-2c1-1 1 0 0-1l-2 1h0c1-1 1-2 3-3l-1-1z" class="B"></path><defs><linearGradient id="F" x1="453.858" y1="777.83" x2="436.93" y2="782.382" xlink:href="#B"><stop offset="0" stop-color="#4f4e4e"></stop><stop offset="1" stop-color="#656564"></stop></linearGradient></defs><path fill="url(#F)" d="M437 781c2-1 4-4 7-4l-1 1h0 0c5-1 8-2 14-2l-2 2v1c-4 0-7 1-11 3-1 1-3 2-4 3-2 1-3 2-5 4v-1c0-1 0-1 1-2 0-1 0-2 1-3h0l-1-1v1l-1-1 2-1z"></path><path d="M630 708c4-2 6-6 9-10l-3 6c-1 3-4 5-4 8-2 3-7 7-8 11-2-1-2-2-3-3 2-4 6-8 9-12z" class="T"></path><path d="M446 244h2c-2 3-4 5-5 8-1 9 5 17 10 23h-2c-3-1-6-6-7-9-3-4-5-11-3-16 1-3 2-4 5-6z" class="D"></path><path d="M180 232h2v1h0c-3 5-6 9-10 13l-1-1c0-1 0 0 1-1v-1c-1 0-1 0-1-1-1 0-2-1-3-1-2-2-2-4-3-6h-1 1 2l-3-3h1c3 1 3 4 5 5 4 1 7-3 10-5z" class="B"></path><path d="M613 619c0 1 0 1-1 1v-2c1-2 0-4 0-6-1 0-2 0-3-1v-2-1c1-1 1-2 1-3h1v-1-3c0-1 0-2 1-3l1-3c1-2 4-5 5-6 2-1 1 0 2-1 2-1 4-3 6-4h1 3c1-1 1-1 2-1 2 0 3 0 4-1h4 4v1c-10-1-20 2-27 8l-2 2 1 1c1 0 1-1 2 0-1 2-2 4-4 5 3-2 6-5 9-5-6 3-11 9-13 16v1l3-1c1 2 0 7 0 9h0z" class="b"></path><path d="M785 330c0-3 0-3-1-5h1c3 3 3 5 5 9l1 5c-1-1-2-1-3-1 0 1 1 2 1 3s0 1-1 2c0 3 1 6 0 9 0 2 0 3-1 5v-8c-1-6-2-11-4-16 1 1 1 2 2 2v-5z" class="U"></path><path d="M275 482c2 1 5 5 6 5 2 0 2 1 3 2 3 2 5 4 7 5l12 13c2 2 2 3 2 5-2-2-4-5-6-7-8-8-15-15-26-21l2-2z" class="S"></path><path d="M423 798c-2 1-4 2-5 5-4 7-1 15 1 22-3-4-6-10-6-16 0-4 0-8 3-11 2-2 4-3 7-3 1 1 1 1 1 3h-1z" class="D"></path><path d="M523 350h1c2-1 2-9 3-11l4-11v1c0 1 0 2-1 4l-5 16c-2 7-2 15-3 22h0-1l-1-2v-18-7l1 2c1 2 1 3 1 4h1 0z" class="W"></path><path d="M521 346c1 2 1 3 1 4h1c0 3-1 6-2 9-1-3 0-9 0-13z" class="Y"></path><path d="M243 239v-1h1c1 1 1 1 2 1h1 1 1 1c2 3 4 5 6 8 4 3 5 6 7 10l-1 1-3-3h0l-1-1-1-1-2-2-2-2c-1-1-3-2-5-4l-9-6h1c4 1 7 4 10 6 1 1 2 1 3 2h0c-3-3-6-6-10-8z" class="Z"></path><path d="M255 251c2 0 2 2 4 2 0-2-4-4-5-6v-1c1 1 1 1 2 1h0c4 3 5 6 7 10l-1 1-3-3h0l-1-1-1-1-2-2z" class="V"></path><path d="M199 217l1 1v1l-2 2h1l2-1 1 1c-7 3-13 7-18 13l-2-1h0v-1h-2c2-2 5-4 7-6 1-1 4-3 5-4l-1-1c1-2 1-2 3-2h1c2-1 3-1 4-2z" class="O"></path><path d="M265 461l13 12 1 1c1-2 1-5 2-7l2-1 1 5v-1c-1-1-2-1-2-2l-1 1c0 1 0 2 1 3v4c1 3 3 6 3 9-3-4-6-9-11-12v-1l-3-2c-2-3-5-4-6-9z" class="C"></path><path d="M452 380c1-1 2-2 4-3v1c2 4 3 8 5 12 2 3 5 6 7 9 5 6 8 12 9 19v1c1 2 0 4 0 7v-1c0-3 0-6-1-9v-1c-1-7-7-14-12-18-1-4-5-5-7-9-2-2-3-5-5-7v-1z" class="O"></path><path d="M631 570c0-2 0-3 1-4s2-1 4-1h1c0 2 1 5 3 7h3l-1 2h0 4c-3 3-6 3-10 2h-2-1l-2-2c-1-1-1-3 0-4z" class="a"></path><path d="M631 570c0-2 0-3 1-4s2-1 4-1h1c0 2 1 5 3 7h3l-1 2c-1-1-2-1-3-1l-1-1c-2 0-3-1-3-2-1-2 0-3 1-4l-1-1c-1 1-3 2-3 4-1 0-1 0-1 1h0z" class="H"></path><defs><linearGradient id="G" x1="746.218" y1="293.716" x2="752.713" y2="275.85" xlink:href="#B"><stop offset="0" stop-color="#605f60"></stop><stop offset="1" stop-color="#9c9b9a"></stop></linearGradient></defs><path fill="url(#G)" d="M751 279c0-1 1-2 2-3l1 1c1-1 2-2 3-2h0c-1 2-1 2-1 3-3 5-7 10-7 16l-1 2h-1l-3-3c0-1 0-2 1-3l-1-3c2-3 3-6 6-9l1 1z"></path><path d="M750 278l1 1c-2 3-4 8-6 11l-1-3c2-3 3-6 6-9z" class="V"></path><path d="M837 192l1 1c1 2 1 5 1 8-1 3-4 7-7 8-7 4-14 2-21 0-1 0-2-1-2-2h0l11 2c5 0 9-1 13-5 4-3 4-7 4-12z" class="D"></path><path d="M296 633c1 2 1 2 2 3h2c1 2 3 4 5 5 1 1 1 2 2 3 0 1 1 1 2 2h0l1 1c1 0 2 1 3 1h0c3 2 5 5 8 8 1 1 3 2 4 4h0c-11-11-23-18-37-22l8-5z" class="C"></path><defs><linearGradient id="H" x1="442.559" y1="356.735" x2="458.835" y2="355.433" xlink:href="#B"><stop offset="0" stop-color="#adacad"></stop><stop offset="1" stop-color="#dfdddb"></stop></linearGradient></defs><path fill="url(#H)" d="M465 361c-11-5-24-4-36-2 2-2 4-2 6-3 8-3 16-2 25-2-1-1-3-4-2-5 2-2 2-2 4-2l-3 2c0 2 1 3 2 5l-1 1v1c2 1 2 1 3 3l2 2z"></path><path d="M237 325c3-2 6-3 10-3 4-1 7 0 11-1l1 1-3 2c5 4 11 11 11 18h0v-1c-2-6-6-12-12-16v1c-3 1-6 1-10 2-3 1-7 3-10 5l-1-1c2-1 3-2 5-3l14-5c-4 0-7 1-10 2-2-1-5 0-6-1z" class="O"></path><path d="M159 250v-3c1-4 3-9 6-12 1 2 1 4 3 6-1 2-1 5-3 7h0l-3 6c-2 6-4 13-9 18h-1c5-7 8-12 9-21v-1h-2z" class="G"></path><path d="M800 336c1 0 2-1 3-2s1-1 3-1l-1 2 1 1h3c-3 8-4 16-10 22v-10c1 0 1 0 2 1h0c1-1 1-1 1-2 0-4 0-8-2-11z" class="E"></path><path d="M800 336c1 0 2-1 3-2s1-1 3-1l-1 2c-2 4-2 8-3 12 0-4 0-8-2-11z" class="L"></path><defs><linearGradient id="I" x1="416.069" y1="763.045" x2="438.033" y2="755.241" xlink:href="#B"><stop offset="0" stop-color="#444645"></stop><stop offset="1" stop-color="#817c7d"></stop></linearGradient></defs><path fill="url(#I)" d="M434 765c-4-1-8-3-12-5-8-4-17-10-23-17 5 3 10 6 14 9 10 6 19 9 29 13-1 1-1 1-3 1 0 0-1 0-1-1h-4z"></path><path d="M491 457l2 1 1-1c1-1 1-2 3-3h0l-2 3c-3 5-9 10-14 11-2 1-5 1-7 0-1-1-2-2-2-3v-2h2 0c1 0 2 1 3 1 5 0 10-4 14-7z" class="H"></path><path d="M685 561c0-1 1 0 2 0s2 1 3 2c2 2 3 5 4 8 3 10 4 18 3 28-1-9-2-19-7-28-1-2-4-4-5-7-1-1-1-2 0-3z" class="D"></path><path d="M712 657l3-2 1-1c1-1 2-2 4-2l1-1c1 0 2-1 4-1l-1 2c1 0 2 0 3 1l4 1h5c-7 1-11 2-17 5-1 0-2 0-3 1-5-1-8 5-13 5 1-1 2-2 4-3l5-5z" class="F"></path><path d="M817 300l2 1c3-2 5-3 9-3l-3 4-4 5-5 4c-2-1-4-1-5-1-2 1-3 2-5 2l1-2 1-1 4-3 1-1v-1l4-4z" class="V"></path><path d="M813 304l2 1h1c1 0 3-1 4-2s4-1 5-1l-4 5h-4-1c-1 0-2 0-4 1 0 0-1 1-2 1 1-1 3-2 3-4h0v-1z" class="K"></path><path d="M817 300l2 1c3-2 5-3 9-3l-3 4c-1 0-4 0-5 1s-3 2-4 2h-1l-2-1 4-4z" class="U"></path><path d="M641 541h3l-3 3 2 2h2l-1 1 1 1h0c1 0 2 1 3 2h1 3l1 1-5 1c-4 1-9 1-13 1l-1-1c0-1 0-1-1-1l-1-1h0c1-1 2-1 3-1v-1c0-1 2-1 3-1l-1-1c1-2 0-1 0-3l1-1-1-1h4z" class="E"></path><path d="M645 548h0c1 0 2 1 3 2h1 3l1 1-5 1c-1-1-2-2-3-2h-1c1-1 1-1 1-2h0z" class="O"></path><path d="M719 659h1c2 0 5-1 8-2l-12 7h-3c-2 1-3 2-5 3-3 2-7 4-10 7l-3 3h0c-1 1-2 1-2 2l-1-1 7-6-1-1v-1l5-5c5 0 8-6 13-5 1-1 2-1 3-1z" class="X"></path><path d="M703 665c5 0 8-6 13-5-1 1-3 2-4 3l-13 9-1-1v-1l5-5z" class="J"></path><path d="M766 452c3-1 6-3 9-5 4-1 8-2 10-5 3-3 5-10 9-10 2 0 2 1 3 2 0 2 0 3-1 5-3 5-14 7-19 9-4 2-7 3-11 5v-1z" class="I"></path><path d="M225 488c5-1 11 0 16 1 1-2 0-3 2-5 7 1 12 6 19 8l-1 1c-1 0-2-1-3-1-5 0-9-2-13-1-2 0-2 1-3 1h-1l-2-2c-4 0-10 1-13 0-1-1-1-1-1-2z" class="B"></path><path d="M770 239v2 1h1c0-1 0-1 1-1 2 0 4-1 6-2 1 0 1 0 2-1 1 1 0 2 0 3l-6 3c-2 1-4 3-5 4-2 1-4 2-6 4-1 1-1 1-3 1l-1 2v-1c1-2 3-4 4-5h1l-1-1 3-3c-2 1-2 1-3 3h-1-2l-5 4c3-5 7-8 12-11l3-2z" class="G"></path><path d="M642 737c5 0 10-1 16 0 2 0 5 1 8 2-3 1-6 0-9 0-2 0-5 0-7-1 0 1 0 1 1 1v1l2 2v1c0 2 2 3 4 5 1 2 4 6 3 9h0c0 2 0 3-1 4l-1-5c0-2-1-3-2-4-2-1-3-3-4-5l-2 1h0l-1-1v-2c1-2 1-1 2-2-1-1-2-2-3-2-3-1-4-3-7-3h-1l2-1z" class="S"></path><path d="M236 337c2 0 4-1 5 0v2h0v1h-4c-1 1-2 2-2 3-1 1-1 1-1 2h-3l-7 7c0 1-2 2-3 3h0c0-1-1-1-1-2 1-4 5-10 7-12 2-3 6-4 9-4z" class="J"></path><path d="M204 330c1 0 2-1 2 0 4 1 7 5 9 8-2-1-3-3-5-4 1 2 0 6 0 8-1 4-1 8 0 12 1 2 2 4 2 5-5-3-6-8-6-14 0-1 1-3 0-4 0-3-2-6-3-9 0-1 0-1 1-2z" class="F"></path><path d="M590 283c3-2 7-5 11-6h6c2 1 4 1 6 2 2 0 4 0 5 1 0 1-1 1-2 2-1-1-2-1-4-1v1l-4 1c-1 0 0 0-1 1-2-1-4 0-6 0h-13l2-1z" class="H"></path><path d="M590 283c3-2 7-5 11-6h6l-2 2c-1 0-1 0-2 1s-4 2-5 2-1 0-2 1-4 0-6 0h0z" class="F"></path><path d="M427 152h19v23l-10-16 11-7h-20z" class="H"></path><defs><linearGradient id="J" x1="155.889" y1="275.908" x2="175.094" y2="260.07" xlink:href="#B"><stop offset="0" stop-color="#333232"></stop><stop offset="1" stop-color="#666"></stop></linearGradient></defs><path fill="url(#J)" d="M162 263l1-1c0-2 0-3 2-3 3-2 4-4 7-6-2 8-8 31-14 35 2-6 3-12 4-19 1-1 1-4 1-6h-1z"></path><defs><linearGradient id="K" x1="254.365" y1="452.905" x2="265.627" y2="441.591" xlink:href="#B"><stop offset="0" stop-color="#969594"></stop><stop offset="1" stop-color="#b6b5b5"></stop></linearGradient></defs><path fill="url(#K)" d="M254 433c1 1 2 1 3 2 1-2 1-3 3-4h1l-3 8h0c-1 9 2 16 7 22h0c1 5 4 6 6 9-3-1-8-7-10-10-1-2-3-4-5-6v-1c0-3-2-6-1-8 0-1 0-2-1-3v-9z"></path><path d="M256 454c2 2 4 4 5 6 2 3 7 9 10 10l3 2v1c5 3 8 8 11 12 2 3 6 6 6 9-2-1-4-3-7-5-1-1-1-2-3-2l-7-8c3-1 1 1 3 1l1-1c-2-2-5-5-7-6l-3-3c-2-1-2-2-3-3l-1-2c-2-2-2-2-4-2-2-3-4-4-4-7v-2z" class="X"></path><path d="M209 208h1c1 1 1 1 1 2l-1 1c-1 0-2 0-3 1 2 1 5 0 7 0v2h-2l-1 2h1v1l-5 2-5 2-1-1-2 1h-1l2-2v-1l-1-1h0c1-1 2-2 3-2l-1-1h-2l2-1v-1l-3 1-1-2 6-2 6-1z" class="Z"></path><path d="M209 208h1c1 1 1 1 1 2l-1 1v-1h-2l1-2z" class="V"></path><path d="M197 211l6-2-1 2h1 4c-1 1-2 1-3 2h2v1h0c-2 0-3 0-4 1l-1-1h-2l2-1v-1l-3 1-1-2z" class="S"></path><path d="M207 215c1-1 3-1 5-1l-1 2h1v1l-5 2-5 2-1-1-2 1h-1l2-2v-1l-1-1h0l8-2z" class="X"></path><path d="M207 215c1-1 3-1 5-1l-1 2h1v1l-5 2c-1-1-1-1-2-1h0v-1c1 0 2 0 3-1l-1-1z" class="U"></path><path d="M795 316s11-5 12-6l-1 2c2 0 3-1 5-2 1 0 3 0 5 1-3 2-7 4-10 6-4 2-9 4-14 5v-1l-3 1h-1c-1-1-2-1-2-2l1-1c3-1 5-2 8-3z" class="W"></path><path d="M795 316c0 2-1 2-2 3h-2-1v1c2 0 3 0 4-1h1c1-1 2-1 3-2 2 0 5-1 8 0-4 2-9 4-14 5v-1l-3 1h-1c-1-1-2-1-2-2l1-1c3-1 5-2 8-3z" class="R"></path><path d="M827 286l5-8v3l2-2c0 4-1 8-2 11s-3 6-4 8c-4 0-6 1-9 3l-2-1 5-5 1-3c1 0 4-5 4-6z" class="J"></path><path d="M827 286l5-8v3c-1 4-1 5-5 8v-1-2z" class="C"></path><path d="M832 290c-1 3-3 6-4 8-4 0-6 1-9 3l-2-1 5-5v1c5 0 7-3 10-6z" class="H"></path><path d="M259 255h0l3 3c1 2 2 2 4 3h1c0 1 1 3 1 4l1 1c0 3 0 7 1 10 1 4 0 10-1 14v1l-1-1-1-2v-1c1-1 1-2 0-3h0c1-1 1-1 1-2 0-3 0-4-1-6-2-3-3-5-4-7-1-1-1-2-1-2 0-2-2-4-3-4 0-3-1-5-1-7l1 1 1-1-1-1z" class="M"></path><path d="M207 308l5 2h0c1-1 2-1 2-1l12 6 2 1-4 5c-2 0-6-1-8-2l-1-1-1-1c-1-2-3-2-5-3h-1l-3-3c0-1 1-1 2-1l-1-1 1-1z" class="L"></path><path d="M207 308l5 2h0c1-1 2-1 2-1l12 6-1 1c-1 0-1 0-2-1-2-1-6-1-8-2l-1-1c-2 1-3 0-5 2h-1l-3-3c0-1 1-1 2-1l-1-1 1-1z" class="a"></path><path d="M767 241v-1c0-2 1-2 2-3 1 0 2 0 2-1l1-2c0-1 2-3 3-3h1l-1-1c5-3 16-3 22-2 2 0 4 1 7 2-3 1-7 0-10 1-5 0-10 2-15 4l-9 4-3 2z" class="T"></path><path d="M779 235l-1-1h-1l-1 1-2 1h-1c4-4 13-5 18-7 2-1 4-1 6-1s4 1 7 2c-3 1-7 0-10 1-5 0-10 2-15 4z" class="G"></path><path d="M576 782c-4-1-9-3-14-3l-8 1c7-5 16-5 24-5-1-1-1-1-3-1v-1c4-1 7-1 11 0l-1 2c-2 0-2 0-3 1l4 4c-1 0-1 0-1-1-1-1-3-2-5-2h0l1 1v1c1 0 1 1 2 1l-1 1h-2 0-1l-3 1z" class="V"></path><defs><linearGradient id="L" x1="752.576" y1="273.264" x2="763.67" y2="270.095" xlink:href="#B"><stop offset="0" stop-color="#9d9b9a"></stop><stop offset="1" stop-color="#c1c1bf"></stop></linearGradient></defs><path fill="url(#L)" d="M765 262c1-1 4-3 5-3h0c-1 2-1 4-3 5 1 1 2 1 3 1l1-1h1 6l-3 3h0l-1 1h0c-1 0-5 1-6 0-5 3-8 6-12 10 0-1 0-1 1-3h0c-1 0-2 1-3 2l-1-1c-1 1-2 2-2 3l-1-1 1-2c2-1 4-5 6-6l8-8z"></path><path d="M765 262c1-1 4-3 5-3h0c-1 2-1 4-3 5 1 1 2 1 3 1l1-1h1 6l-3 3h0l-1 1h0c-1 0-5 1-6 0h-2c-1 1-2 1-3 0 0 0 1-1 1-2 1-1 1-2 1-3v-1z" class="C"></path><path d="M652 722c3-2 8-4 12-3 0 1 0 2-1 3h0v1l-2 1-4 2c0 1-2 2-2 3h5c-3 1-7 1-10 1h-4c-2 1-5 2-7 1l2-1c1-3 5-4 7-6 1-1 2-2 4-2z" class="B"></path><path d="M641 730c4-1 8-3 12-5 1 0 3-1 4-1h1c1-1 3-1 5-2v1l-2 1-4 2c0 1-2 2-2 3h5c-3 1-7 1-10 1h-4c-2 1-5 2-7 1l2-1z" class="N"></path><path d="M273 497c1 1 1 2 3 2h1c0-2-1-3-2-4h-1l2-1c5 4 9 9 14 14l14 13c3 2 8 5 9 7v1l3 4c-2-2-4-4-7-4l-1-1h1 1c-4-4-7-7-12-9l-1 1c-1-1-3-2-5-3 0 0-2-2-3-2l1-1h2 1l-6-6c-2-1-3-2-5-3l-10-7 1-1z" class="Y"></path><path d="M517 308c2 1 2 2 2 4 0 1 0 0-1 1h0c0 2 1 5 0 6v4h0v1 13c0 2 1 5 0 6v1c0 2-1 4 0 6 1 0 1 0 0 1v2h0c0 5-1 12 0 17v1c-1 1-1 1-1 3-1 4-1 9-1 13h0v-3c-1 1 0 4 0 5-2-2-2-9-2-11l1-1-1-8c0-1-1-3-1-4v-2-3-1 1l-1-1v-1c1 0 1-1 1-2h1v3h1l2-13v-27-5-6z" class="R"></path><path d="M514 356v3h1l2-13v23 2l-1 1v4l-1 1-1-8c0-1-1-3-1-4v-2-3-1 1l-1-1v-1c1 0 1-1 1-2h1z" class="M"></path><path d="M514 369l1-1v-3h1v1c0 2 0 2 1 3v2l-1 1v4l-1 1-1-8z" class="L"></path><defs><linearGradient id="M" x1="265.538" y1="219.314" x2="224.099" y2="222.176" xlink:href="#B"><stop offset="0" stop-color="#2b2a29"></stop><stop offset="1" stop-color="#515152"></stop></linearGradient></defs><path fill="url(#M)" d="M231 213l4 1c9 2 17 6 25 9 1 1 2 1 3 1h0l-1-2c1 0 2 1 3 1 2 2 2 5 3 7-14-5-26-13-41-14h-1l-2-2h2c2 0 3 0 5-1h0z"></path><path d="M626 651c3-1 5 0 7 1 3 3 4 10 4 14l1 1c1 8 0 17-4 25 0 0 2-12 2-15 0-5-1-10-3-14-1-1-1-2-2-2-1-1-3-2-5-3-1-2-1-4-1-6l1-1z" class="Q"></path><path d="M292 517c2 1 4 2 5 3l1-1c5 2 8 5 12 9h-1-1l1 1c3 0 5 2 7 4l-3-4v-1c6 5 12 11 18 16-10-3-19-10-28-16-2-2-5-4-7-7-2-1-3-2-4-4z" class="T"></path><path d="M292 517c2 1 4 2 5 3l1-1c5 2 8 5 12 9h-1-1l1 1 6 5c-4-1-14-10-17-13h-1-1c-2-1-3-2-4-4zm471-211h3c0 1 0 2 1 3h0c1 2 2 2 3 4l2 1v-1c2 2 4 4 6 5v2c1 1 3 2 4 4v1s1 1 1 2l1 1c0 1 1 1 1 2v5c-1 0-1-1-2-2s-1-1-1-2c-2-3-4-5-7-8-1-1-3-2-4-4-2-3-6-6-8-8v-1c1 0 1 1 2 1-1-1-1-2-2-2h-1c0-2 0-2 1-3z" class="M"></path><path d="M306 624l2 1 1 2c2 2 3 4 4 6s2 2 3 3l3 2v-1c-1-1-1-2-1-4 3 6 6 12 10 17 6 11 13 21 22 31h-2l-3-4c-1-3-29-37-33-43 0-2-5-7-6-10z" class="E"></path><defs><linearGradient id="N" x1="285.006" y1="517.416" x2="268.225" y2="500.299" xlink:href="#B"><stop offset="0" stop-color="#5d5e5d"></stop><stop offset="1" stop-color="#9f9d9c"></stop></linearGradient></defs><path fill="url(#N)" d="M264 499c2 1 4 2 7 2h0c1 0 3 1 4 1l5 3h1 1c2 1 3 2 5 3l6 6h-1-2l-1 1c-9-5-17-8-27-9l2-1h0l2-2 1-2c-1 0-2-1-3-2z"></path><path d="M271 501c1 0 3 1 4 1l5 3h1 1c2 1 3 2 5 3l6 6h-1-2c-1-1-1-1-2-1l-3-3-1-1c-1-1-2-2-3-2h0l-10-6z" class="K"></path><path d="M189 210v1l-2 1v1l3-1 7-1 1 2 3-1v1l-2 1h2l1 1c-1 0-2 1-3 2h0c-1 1-2 1-4 2h-1c-2 0-2 0-3 2l1 1c-1 1-4 3-5 4l-9 5 2-2c2-4 5-7 9-10-2-1-5-3-5-5-1 0 0-1 0-2 1-1 3-1 5-2z" class="Q"></path><path d="M190 212l7-1 1 2 3-1v1l-2 1h2l1 1c-1 0-2 1-3 2h0c-1 1-2 1-4 2h-1c1-1 3-2 4-3l-4 1h-1l2-1v-1c-3 0-3-1-5-3z" class="N"></path><path d="M249 280l2 2c1 1 2 2 3 4 2 1 2 3 3 6h0v4h1c-1 1 0 1-1 1-2 1-4-1-7 1h-2v-1l-2-2h-1l-1-1c-1 0-2-1-2-2h1 1l-2-2 3 1h0l-1-2c1 0 1 0 2-1l1-1 1-1v-1l-1-1 2-3v-1z" class="Y"></path><path d="M249 280l2 2c1 1 2 2 3 4l-2 3c1 1 1 2 1 4h-2l-2-2 1-1h0l-1-2v-2h0v-5-1z" class="W"></path><path d="M251 282c1 1 2 2 3 4l-2 3h0c-1-2-2-2-2-4h0v-3l1 1v-1z" class="M"></path><path d="M254 286c2 1 2 3 3 6h0v4h1c-1 1 0 1-1 1-2 1-4-1-7 1h-2v-1l1-1-4-4 5 3v-1h1v-1h2c0-2 0-3-1-4l2-3z" class="L"></path><path d="M845 240c0-1 2-2 3-3l1 1c1 2 2 4 3 7 1 1 1 3 1 4l2 1c-2 0-4 0-6 1l4 16c-5-7-8-14-11-22 0-1-1-1-1-2 1-1 2-1 3-1l1-2z" class="E"></path><path d="M845 240l3-1c1 1 1 3 1 5-1-1-2-2-3-2h-2l1-2z" class="F"></path><defs><linearGradient id="O" x1="219.061" y1="207.899" x2="211.513" y2="212.619" xlink:href="#B"><stop offset="0" stop-color="#444544"></stop><stop offset="1" stop-color="#615e60"></stop></linearGradient></defs><path fill="url(#O)" d="M214 207h8c2 1 2 1 3 1l6 3c1 0 1 1 2 1l2 2-4-1h0c-2 1-3 1-5 1h-2l2 2h1c-5 0-10 0-15 1v-1h-1l1-2h2v-2c-2 0-5 1-7 0 1-1 2-1 3-1l1-1c0-1 0-1-1-2 1 0 3-1 4-1z"></path><path d="M214 207h8l1 1c0 1 0 1 1 2l1 1v1c-2-1-2-1-3-1-2-2-4-2-6-3-1 0-1-1-2-1z" class="M"></path><defs><linearGradient id="P" x1="221.29" y1="212.035" x2="216.281" y2="217.882" xlink:href="#B"><stop offset="0" stop-color="#535252"></stop><stop offset="1" stop-color="#717172"></stop></linearGradient></defs><path fill="url(#P)" d="M214 212c3-1 7 0 10 1 3 0 5-1 7 0h0c-2 1-3 1-5 1h-2l2 2h1c-5 0-10 0-15 1v-1h-1l1-2h2v-2z"></path><defs><linearGradient id="Q" x1="265.333" y1="506.37" x2="245.628" y2="500.255" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#e6e4e3"></stop></linearGradient></defs><path fill="url(#Q)" d="M259 498c2 0 3 1 5 1 1 1 2 2 3 2l-1 2-2 2h0l-2 1c-7-1-14 1-20 1 3-4 6-7 10-10l7 1z"></path><path d="M432 775v2c-1 2-3 3-3 5h1v1c-1 2-1 4-2 7 0 6 1 10 4 15l1 1c1 2 3 3 4 5-5-1-8-3-10-7-1-2-2-4-3-5l-1-1h1c0-2 0-2-1-3 1-2 0-5 1-7 0-6 3-10 8-13z" class="O"></path><path d="M783 358c-5-7-9-14-15-20-2-2-4-4-6-5-3-2-8-2-11-4-1-1-2-2-2-3l2-2c2-1 3-1 5-1-1 1-2 1-3 2v2h4c2 1 5 2 6 3 3 1 5 3 7 5 6 4 9 12 12 18 0 1 1 3 1 4v1z" class="H"></path><defs><linearGradient id="R" x1="173.577" y1="251.844" x2="170.198" y2="246.585" xlink:href="#B"><stop offset="0" stop-color="#6b6b6b"></stop><stop offset="1" stop-color="#81807f"></stop></linearGradient></defs><path fill="url(#R)" d="M182 233l2 1c-3 5-7 9-10 15l-2 4c-3 2-4 4-7 6-2 0-2 1-2 3l-1 1c0 1-1 2-2 3h0v-1c1-3 3-6 3-9l1-1 1-7c2-2 2-5 3-7 1 0 2 1 3 1 0 1 0 1 1 1v1c-1 1-1 0-1 1l1 1c4-4 7-8 10-13z"></path><path d="M167 252v1l1 1c0 2 0 2-1 2-2 1-3 1-4 0l1-1h0 1c0-1 0-2 1-3h1z" class="K"></path><path d="M168 241c1 0 2 1 3 1 0 1 0 1 1 1v1c-1 1-1 0-1 1l1 1c-1 2-3 3-4 5 0 0-1 0-1 1h-1c-1 1-1 2-1 3h-1 0l1-7c2-2 2-5 3-7z" class="O"></path><path d="M763 301l2 1 3 1 1 1 4 3h1c2 2 3 4 5 6 1 0 1-1 2-1l4-1 1-1c0 2 0 2-1 4l-3 1v1c1 1 1 1 2 1l4-1v1l-1 2-1 1c0 1 1 1 2 2v1h0l-5-4-1 2c-1 0-3-1-4-3-2-1-4-3-6-5v1l-2-1c-1-2-2-2-3-4h0c-1-1-1-2-1-3h-3c1-2 1-3 0-5z" class="N"></path><path d="M763 301l2 1 3 1 1 1 4 3h-1c-1 0-2-1-3-1v1l-3-1h-3c1-2 1-3 0-5z" class="C"></path><path d="M766 306l3 1c5 4 10 7 14 12l-1 2c-1 0-3-1-4-3-2-1-4-3-6-5v1l-2-1c-1-2-2-2-3-4h0c-1-1-1-2-1-3z" class="L"></path><path d="M264 490c0-2 1-2 2-3 3 1 7 4 10 7l-2 1h1c1 1 2 2 2 4h-1c-2 0-2-1-3-2l-1 1 10 7h-1-1l-5-3c-1 0-3-1-4-1h0c-3 0-5-1-7-2-2 0-3-1-5-1l-1-1v-1-1h8l-1-1-4-1 1-1 2-2z" class="N"></path><path d="M264 490c2 1 2 1 3 2v1l-2 1-4-1 1-1 2-2z" class="J"></path><path d="M264 499l1-1 5 1h1c-2-1-2-2-4-2-1 0-1 0-2-1 1 0 2 0 3-1-1 0-1 0-2-1h2c2 1 3 1 5 3l-1 1 10 7h-1-1l-5-3c-1 0-3-1-4-1h0c-3 0-5-1-7-2z" class="H"></path><defs><linearGradient id="S" x1="553.721" y1="395.597" x2="542.779" y2="383.403" xlink:href="#B"><stop offset="0" stop-color="#bab8b7"></stop><stop offset="1" stop-color="#e8e7e6"></stop></linearGradient></defs><path fill="url(#S)" d="M560 371l1-2h-1c1-1 1-1 2-1l4 4c-4 3-8 4-11 7-9 5-15 16-17 27l-1 6h-1v-7l-1-3c1-9 8-17 15-23 3-3 7-5 9-8h1z"></path><path d="M765 464c4 0 7 2 10 4-1 2-2 4-2 5l7 5c-9-1-16-1-24 0l-1-1-1 1c-1-1 0-3 0-4l6-1s1 0 1-1c2 0 4-2 4-3 1-3 1-3 0-5z" class="Q"></path><path d="M608 732c-2 0-4 2-6 3-3 2-14 8-17 7-2 0-4 1-6 1 1 0 1 0 2-1 4-2 8-4 13-7s11-5 17-10l7-7h1l1 3c0 2-1 3-2 4l-1 1-3 3c-2 1-4 3-6 3z" class="B"></path><defs><linearGradient id="T" x1="587.385" y1="803.481" x2="577.356" y2="789.149" xlink:href="#B"><stop offset="0" stop-color="#80807f"></stop><stop offset="1" stop-color="#9a9998"></stop></linearGradient></defs><path fill="url(#T)" d="M581 778c2 1 4 4 6 6s3 4 4 7h0c0 3 0 7-1 9v1c-1 3-4 5-7 7-4 1-7 3-11 1 2 0 4-1 6-2 3-1 7-5 8-9 1-3 0-6-1-9-3-3-5-5-9-7l3-1h1 0 2l1-1c-1 0-1-1-2-1v-1z"></path><path d="M581 778c2 1 4 4 6 6v-1c-1 0-2 0-2-1l-1 1h-2c1 2 3 3 3 5v1c-3-3-5-5-9-7l3-1h1 0 2l1-1c-1 0-1-1-2-1v-1z" class="K"></path><path d="M766 213h1c5-1 11-1 16-1 1 0 2 0 3 1h1-4c-1 0-2 0-3 1-3 1-5 0-8 1l-4 1h-3-2c-1 1-2 1-3 1h-1c-8 2-16 5-24 9-1 1-4 2-6 3h-2l-3-3 1-1c1 1 2 1 3 0h1c5 0 8-3 13-4h1c0-1 0-1 1-2 2-3 10-4 14-5h2 1c0-1 0-1 2-1h3z" class="P"></path><path d="M509 853l1 1 7 9c1 1 2 1 2 2h1l-1 2c-1 1-14 0-16 0l-3-9c3-2 6-4 9-5zm77-470c2-2 3-4 5-6 1 1 2 2 2 3v4 3l5 4-5 1c-11 3-14 8-19 17 1-5 4-10 6-15 1-2 2-5 3-7s2-3 3-4z" class="D"></path><path d="M586 383c2-2 3-4 5-6 1 1 2 2 2 3v4 3h-1c-1 1-2 1-3 2h-1-1l2-1v-1l1-1v-1h-1v-1c0-1 0-2 1-2v-2l-3 3h-1z" class="J"></path><defs><linearGradient id="U" x1="595.693" y1="754.393" x2="596.622" y2="756.985" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#999897"></stop></linearGradient></defs><path fill="url(#U)" d="M566 764l62-21v1 2h-1-1l-52 19c-2 0-4 0-5 1h-1l-2-2z"></path><path d="M310 505l-4-4c-5-6-11-14-13-22v-1-6c0-2 0-4 2-5 1 1 1 3 1 5 1 3 2 6 3 8 4 9 9 17 14 24l-3 1z" class="Q"></path><path d="M615 247c1 2 2 3 2 5 0 1 0 0 1 1v7l-1 1v1c0 1-1 1-2 2v1l-5 5c-1 1-2 1-2 2h-1c-1 1-4 2-6 3-1-1-2-1-3-1 8-8 13-17 17-27z" class="P"></path><path d="M430 783c2-2 3-5 6-6l1 1h2v1l-2 2-2 1 1 1v-1l1 1h0c-1 1-1 2-1 3-1 1-1 1-1 2v1c2-2 3-3 5-4-3 3-5 6-6 10l3 6-3-2v1c1 4 4 7 7 10h0c-4 0-5-2-7-5l-1 1-1-1c-3-5-4-9-4-15 1-3 1-5 2-7z" class="G"></path><path d="M430 783c2-2 3-5 6-6l1 1h2v1l-2 2-2 1 1 1v-1l1 1h0c-1 1-1 2-1 3-1 1-1 1-1 2v1c2-2 3-3 5-4-3 3-5 6-6 10l3 6-3-2h-1c0-4-1-7 1-11h0c-1-1 0-2 0-3v-1l1-2-1-1c-2 2-3 3-4 6-2 5-1 11 2 16 0 1 1 2 2 2l-1 1-1-1c-3-5-4-9-4-15 1-3 1-5 2-7z" class="T"></path><defs><linearGradient id="V" x1="722.266" y1="499.94" x2="740.445" y2="496.52" xlink:href="#B"><stop offset="0" stop-color="#4f4c4c"></stop><stop offset="1" stop-color="#626364"></stop></linearGradient></defs><path fill="url(#V)" d="M735 491h0c2 0 3-2 5-2 1 1 0 1 1 2-1 2-2 3-3 5 1 0 1 0 2 1l1 1 1 1c-2 0-3 1-4 2h1c2-1 6-2 7-2-4 2-8 3-13 5l-11 5c0-2 1-3 2-4v-2l-4 3h-1 0l16-15z"></path><path d="M738 496c1 0 1 0 2 1l1 1 1 1c-2 0-3 1-4 2h1c2-1 6-2 7-2-4 2-8 3-13 5-2 0-4 1-6 1v-1c1-1 3-1 4-2 3-2 4-5 7-6z" class="V"></path><path d="M756 323l2 1c2 1 4 1 6 1 3 1 5 3 7 4s3 3 4 5l4 5 3 5v1c1 1 2 3 2 4l2 5-1 1-1-1-1 2h0c1 1 1 1 1 2h-1v-1c0-1-1-3-1-4-3-6-6-14-12-18-2-2-4-4-7-5-1-1-4-2-6-3h-4v-2c1-1 2-1 3-2z" class="P"></path><defs><linearGradient id="W" x1="803.193" y1="313.139" x2="804.343" y2="298.403" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#c1bfbb"></stop></linearGradient></defs><path fill="url(#W)" d="M819 282c1 5-4 11-7 15v1h0l2 1c0 1-1 2 0 3 2-1 4-4 5-6l2-1c0-1 1-2 2-3l-1 3-5 5-4 4v1l-1 1-4 3-1 1c-1 1-12 6-12 6-3 1-5 2-8 3l1-2v-1c14-8 25-18 31-34z"></path><path d="M805 308c0-1 2-2 3-3l1 1h3l-4 3-1-1h-2z" class="N"></path><path d="M805 308h2l1 1-1 1c-1 1-12 6-12 6-3 1-5 2-8 3l1-2c4-2 8-3 13-5 1-2 3-2 4-4z" class="S"></path><defs><linearGradient id="X" x1="752.346" y1="262.081" x2="761.64" y2="258.912" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#bbb9b9"></stop></linearGradient></defs><path fill="url(#X)" d="M769 248c-1 1-1 3-2 4-1 0-2 1-2 2 0 2-1 3-2 4h0v2c1-1 1-1 2-1 1-1 1-1 2-1h0l1 1c2 0 4-2 6-3l-4 3c-1 0-4 2-5 3l-8 8c-2 0-2-1-4 0l-2 2v-2l-1-1c-1-1-1 0-1-1l-1-1 2-3 1-1a30.44 30.44 0 0 1 8-8l1-2c2 0 2 0 3-1 2-2 4-3 6-4z"></path><path d="M750 264l1-1c1 2 1 2 3 3l1-1c1 0 2 0 3-1-1 2-2 3-4 4l-3-1-1-3z" class="S"></path><path d="M748 267l2-3 1 3 3 1-1 1-1 1h-1l-1-1c-1-1-1 0-1-1l-1-1z" class="T"></path><path d="M769 248c-1 1-1 3-2 4-1 0-2 1-2 2 0 2-1 3-2 4h0v-1l-1 1c-1-1-1-1-2-1 2-2 3-3 3-5 2-2 4-3 6-4z" class="F"></path><path d="M768 259c2 0 4-2 6-3l-4 3c-1 0-4 2-5 3l-8 8c-2 0-2-1-4 0l-2 2v-2h1l1-1 1-1c2-1 3-2 4-4l3-2c3 0 5-2 7-3z" class="V"></path><path d="M305 628c2 1 4 5 6 8 1 2 3 4 4 5h1l-4-5v-2c4 6 32 40 33 43h-1l-4-4-3-5-1-1-2-2c-1-1-1-2-2-2 0-1-1-1-1-1v-1c0-1-1-1-1-2-1 1-1 1-2 1 0 1 0 1 1 1v1c1 1 2 2 3 4h-1c-2-2-5-4-6-6h0c-1-2-3-3-4-4-3-3-5-6-8-8h1l1 1v-1s-1-2-2-2c-1-2-5-7-5-10 1 2 2 3 3 5 3 4 6 8 9 11 0-1-9-13-10-15l-5-9z" class="R"></path><defs><linearGradient id="Y" x1="246.953" y1="503.212" x2="233.798" y2="489.485" xlink:href="#B"><stop offset="0" stop-color="#bfbebd"></stop><stop offset="1" stop-color="#e1e0de"></stop></linearGradient></defs><path fill="url(#Y)" d="M241 492h1c1 0 1-1 3-1 4-1 8 1 13 1 1 0 2 1 3 1l4 1 1 1h-8v1 1l1 1-7-1c-5-1-11 0-15 1h-1c-4 1-7 2-10 3 0-2 0-4 1-6 3-3 8-3 13-3h1z"></path><path d="M241 492h1c1 0 1-1 3-1 4-1 8 1 13 1 1 0 2 1 3 1l4 1 1 1h-8v1 1h-3l1-1c-1-2-3-2-4-3-4-2-8-1-12-1h-1 1 1z" class="E"></path><path d="M366 748l2-2v3l1 1 2-3v2l-3 8c0 5 0 9 4 13 2 1 3 2 5 3v1c-1 1-1 3-1 4v1c-1 0-2 0-3-1l-2-1h-2c-4-3-6-7-7-11l-1-3h1l1-1c0-1 0-2 1-4v-1c0-2 0-4 1-7l1-2z" class="G"></path><path d="M366 748l2-2v3l1 1 2-3v2l-3 8c-1-2-2-3-2-5v-4z" class="O"></path><path d="M364 758v-1c0-2 0-4 1-7 0 5 2 9 2 13 0 2 1 3 2 4v5c-3-4-4-8-5-14z" class="H"></path><path d="M361 763h1l1-1c0-1 0-2 1-4 1 6 2 10 5 14 2 2 4 4 4 6l-2-1h-2c-4-3-6-7-7-11l-1-3z" class="L"></path><path d="M243 272h2l1 1h3v-1l-3-3c2 1 3 2 5 1l5 5h0c2 1 3 3 5 4l6 9 1 2 1 1 1 6h-8c0-1-1-3-1-4-2-6-5-10-10-15-2-2-5-4-8-6z" class="V"></path><path d="M264 286c1 1 3 4 4 4l1 1 1 6h-8c0-1-1-3-1-4 1-2-1-6-2-8 3 3 5 6 7 8 0-1-1-4-2-5v-1-1z" class="W"></path><path d="M243 272h2l1 1h3v-1l-3-3c2 1 3 2 5 1l5 5h0c2 1 3 3 5 4l6 9 1 2c-1 0-3-3-4-4-1-3-4-5-7-7-2 0-2-1-3-1h-3c-2-2-5-4-8-6z" class="E"></path><path d="M486 299l-1-1h0c3 2 5 5 7 9 5 7 10 16 13 25l5 18c0 2 1 2 2 4v4 1l1 1v-1 1 3 2h0c0 4 1 8 0 13-1-5-3-10-4-16h1c1-2 1-5 1-7-1-2-1-4-2-6h-1l-3-3-5-11c2 0 2 0 3-1v-3c0-1-1-4-2-4v1c-2-1-3-2-3-3 0-2-2-6-3-8h-1c0-1 0-1-1-2l-3-6c0-1 1-1 1-2-1-3-3-6-5-8z" class="L"></path><path d="M491 307l6 10h-2-1c0-1 0-1-1-2l-3-6c0-1 1-1 1-2z" class="G"></path><path d="M495 317h2c1 3 3 6 4 10v1c-2-1-3-2-3-3 0-2-2-6-3-8z" class="H"></path><defs><linearGradient id="Z" x1="502.657" y1="344.045" x2="505.593" y2="336.293" xlink:href="#B"><stop offset="0" stop-color="#676768"></stop><stop offset="1" stop-color="#7c7b7a"></stop></linearGradient></defs><path fill="url(#Z)" d="M503 331c3 5 4 12 6 18h-1l-3-3-5-11c2 0 2 0 3-1v-3z"></path><defs><linearGradient id="a" x1="356.141" y1="759.521" x2="372.342" y2="758.221" xlink:href="#B"><stop offset="0" stop-color="#b4b3b2"></stop><stop offset="1" stop-color="#d6d3d3"></stop></linearGradient></defs><path fill="url(#a)" d="M365 779c-1-1-2-1-3-2-4-4-6-10-6-16s2-12 6-17c4-3 7-4 11-3 1 1 1 0 0 1-2 1-3 2-5 3v1l-2 2-1 2c-1 3-1 5-1 7v1c-1 2-1 3-1 4l-1 1h-1l1 3c-1 1-1 2-1 4 0 1 3 5 3 7l1 1v1z"></path><path d="M361 763v-1c1-6 3-13 7-17v1l-2 2-1 2c-1 3-1 5-1 7v1c-1 2-1 3-1 4l-1 1h-1z" class="W"></path><path d="M645 747l1-2c1 1 2 1 3 2l1 1h0l2-1c1 2 2 4 4 5 1 1 2 2 2 4l1 5c1-1 1-2 1-4h0c1 5 1 10-3 15-3 4-7 5-12 6h-2v-1c1 0 2 0 3-1l-1-1c1-1 1 0 1-1 2-1 3-2 4-4l-1-1c2-3 4-6 4-9 0-5-2-8-5-12-1 0-3-1-3-1z" class="N"></path><path d="M645 747l1-2c1 1 2 1 3 2l1 1h0c2 3 4 4 4 7-1 0-1 0-1-1-1-2-2-4-4-5l-1-1c-1 0-3-1-3-1z" class="T"></path><path d="M656 752c1 1 2 2 2 4l1 5c1-1 1-2 1-4h0c1 5 1 10-3 15-3 4-7 5-12 6 5-4 8-7 11-12 1-5 2-9 0-14z" class="L"></path><defs><linearGradient id="b" x1="610.912" y1="611.944" x2="627.141" y2="591.523" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#b)" d="M623 594c1 0 2-1 3-1 5-1 11-3 16-2h1c2 0 3 1 4 1h0c-1-1-2-1-2-2v-1c2 1 3 2 4 3 0 1 0 1 1 2 2 1 4 2 6 4h0v1h-1c-2-1-5-2-8-3v-1c-2 0-3 0-5-1-5-1-12 1-16 4l-1 1c-4 3-8 6-10 12l-2 8h0c0-2 1-7 0-9l-3 1v-1c2-7 7-13 13-16z"></path><path d="M724 629h1c1 4 6 5 9 8-9 3-17 5-25 10l-7 5h-2l-1-1 1-2 1-1 6-7v1c2 0 2 0 3-1l3-3h-1c1-2 1-3 2-4l1 1 2-2c3 0 5-2 7-4z" class="O"></path><defs><linearGradient id="c" x1="700.278" y1="647.716" x2="708.031" y2="644.576" xlink:href="#B"><stop offset="0" stop-color="#83807f"></stop><stop offset="1" stop-color="#999998"></stop></linearGradient></defs><path fill="url(#c)" d="M710 641l1 2c1-1 2-1 3-2h1 0l1 1-2 1c-2 1-5 2-6 4h1l-7 5h-2l-1-1 1-2 1-1 6-7v1c2 0 2 0 3-1z"></path><path d="M247 301c2 0 3 0 5 2 0 1-2 3-2 5 4-2 9-4 13-2 2 1 3 2 3 3 1 2 1 4 0 5-1 2-3 3-5 4-4-1-8-3-12-2l-3-2c-1-3 1-3 1-6 0-1-1-2-2-2-1-1-2-1-3-2v-1c2-1 3-1 5-2z" class="D"></path><path d="M492 443l3-6c0-1 1-1 2-2-1 2-1 4-2 6 1 1 2 3 1 4 0 3-1 4-1 7l-1 2-3 3c-4 3-9 7-14 7-1 0-2-1-3-1h0v-1h3v-12-3l1 1 3 2v3c1 1 1 1 3 2v-1c2-4 4-8 7-10l1-1z" class="K"></path><path d="M481 450v3c1 1 1 1 3 2v-1c2-4 4-8 7-10l1-1c-1 2-1 4-3 6l-1 2c-2 2-5 7-8 7l-2 1c0-3-1-8 0-11l3 2z" class="U"></path><path d="M495 441c1 1 2 3 1 4 0 3-1 4-1 7l-1 2-3 3c-4 3-9 7-14 7-1 0-2-1-3-1h0v-1h3 1c8-3 15-14 17-21z" class="P"></path><defs><linearGradient id="d" x1="522.206" y1="343.303" x2="506.794" y2="332.697" xlink:href="#B"><stop offset="0" stop-color="#3d3d3c"></stop><stop offset="1" stop-color="#565557"></stop></linearGradient></defs><path fill="url(#d)" d="M510 294l1 2c1 1 1 2 2 4 0 1 0 1 1 1l1 1c-1 1-1 1-1 3h1c2-1 0-6 1-7l1 1h1c0 3-1 6-1 9v6 5 27l-2 13h-1v-3c0-12 1-25-2-37v-6c-1-5-3-11-4-16 1-1 1-2 2-3z"></path><path d="M514 317l3-3v5 2l-1 1-2-2c1-1 0-1 0-2v-1z" class="Z"></path><path d="M510 294l1 2c1 4 0 9 1 13v2 1 1c-1-5-3-11-4-16 1-1 1-2 2-3z" class="J"></path><path d="M514 305h1c2-1 0-6 1-7l1 1h1c0 3-1 6-1 9v6l-3 3v-5l1-1v-1h-1c-1-2 0-4 0-5z" class="K"></path><defs><linearGradient id="e" x1="713.088" y1="647.605" x2="721.421" y2="654.489" xlink:href="#B"><stop offset="0" stop-color="#908e8d"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#e)" d="M702 659h0c5-5 10-10 16-13 7-4 17-5 24-3 3 1 6 2 8 3-6 0-12 0-18 2-2 0-3 1-4 1-1 1-2 1-3 1-2 0-3 1-4 1l-1 1c-2 0-3 1-4 2l-1 1-3 2v-1l-11 7c0-2 2-2 1-4z"></path><path d="M742 643l8 3c-6 0-12 0-18 2-2 0-3 1-4 1-1 1-2 1-3 1-2 0-3 1-4 1l-1 1c-2 0-3 1-4 2l-1 1-3 2v-1c2-3 8-5 11-7 2-1 3-3 5-3 3-2 7-2 10-3h1 3z" class="B"></path><path d="M758 301h5c1 2 1 3 0 5-1 1-1 1-1 3h1c1 0 1 1 2 2-1 0-1-1-2-1v1c2 2 6 5 8 8-7-3-14-6-21-4-2 0-5 1-8 1l-1-1c-1-1-2-3-1-5 0-2 1-3 2-4 4-2 11 1 15 2l-1-4c0-2 1-2 2-3z" class="Q"></path><path d="M778 397c0-3 1-7 2-11 1-9-2-16-4-25 1 1 2 4 3 5v-1l-1-5c4 7 5 14 7 21 0 4-1 9-1 12l-2 7v3l-1 1c0 1-1 3-2 4s-1 2-2 3c-1 3-2 5-3 7-1 1-1 2-2 3 0 1-1 1-2 2h0v-1-3c1-2 2-3 2-4l2-5 3-8v-1c1-2 1-2 1-4z" class="F"></path><defs><linearGradient id="f" x1="254.481" y1="478.844" x2="232.522" y2="466.717" xlink:href="#B"><stop offset="0" stop-color="#bfbebd"></stop><stop offset="1" stop-color="#f1efee"></stop></linearGradient></defs><path fill="url(#f)" d="M244 459c2 1 3 3 5 4 1 2 3 2 4 5l-1 1v1l-4-1-1 1 5 1v1h-5c1 0 1 0 2 1h0c1 1 2 1 3 1 2 0 3 1 4 1v2c-1 0-2 0-2 1-10-2-18-3-28-3 1-1 8-5 8-6 0-2-1-2 0-4 2-3 5-2 8-3 2 0 2-1 2-3z"></path><path d="M247 472c-1-1-2-1-2-1h-1l-1 1h-2c-1-1-1-1-2-1 1-1 1-2 2-3s3-1 4-1c0 1 0 2 1 3h1l5 1v1h-5z" class="B"></path><path d="M496 469c2-2 5-5 8-7v4c0 4-1 8-1 12l-1 17h0l-4-23h0c-2 1-1 3-1 5v25l-1 14c0 3-1 7 0 10v1c1-1 2-3 2-4 2-5 1-10 3-15h0v12c0 2 0 4-1 5 0 1 0 2-1 3 0 1 0 2-1 3l-1 2c-1 1-1 4-1 5l1 1h-1 0c-1-2-1-3-1-4 1-1 1-2 1-3-2-3-1-10-1-13 1-1 1-4 1-5 1-3 0-10 0-13 1-1 1-3 1-4-1-3 0-7-1-10v-8-1c-1-1 0-3 0-4h-1l-1 1c0 1-1 1-1 2v1l-1 2c0 1-1 2-1 3h0l-2 5c-1 1-3 2-4 2s-2 1-3 1h-2-2v-1-1c-1-2-1-1-1-2h0v-3c-1-2-1 0-1-2v-2h0c0-1 0-2 1-3 0-1 2-4 4-4l1-1 1-1c2-1 3-2 4-3v1c-4 3-9 6-10 11h0v2c0 2 1 5 2 7 1 1 3 1 5 1 1 0 3-1 4-2 1-2 2-6 3-8 1-4 3-7 5-11z" class="b"></path><defs><linearGradient id="g" x1="308.897" y1="646.02" x2="284.535" y2="663.385" xlink:href="#B"><stop offset="0" stop-color="#c0bfbd"></stop><stop offset="1" stop-color="#f3f1f3"></stop></linearGradient></defs><path fill="url(#g)" d="M287 649l-1-1c-5-2-12 0-17 0 6-5 11-6 19-5 14 1 25 10 35 20-2 1-3 2-4 3-6-7-13-12-22-15-2-2-3-1-5-1-1 0-3-1-4-1h-1 0z"></path><defs><linearGradient id="h" x1="582.312" y1="776.274" x2="591.89" y2="809.515" xlink:href="#B"><stop offset="0" stop-color="#acaba9"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#h)" d="M586 773c5 3 9 6 11 13 2 6 0 13-3 19-2 3-5 6-9 8-1-1-2-2-4-3l1-1 1-1c3-2 6-4 7-7v-1c1-2 1-6 1-9h0c-1-3-2-5-4-7s-4-5-6-6l-1-1h0c2 0 4 1 5 2 0 1 0 1 1 1l-4-4c1-1 1-1 3-1l1-2z"></path><defs><linearGradient id="i" x1="365.049" y1="694.064" x2="338.8" y2="650.632" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#c1c0bf"></stop></linearGradient></defs><path fill="url(#i)" d="M342 643c0 4 1 9 2 13 1 1 2 3 3 4h0c1-1 1 0 0-1v-2l-1-4v-3c1 3 2 7 3 11h1v2 1c1 1 1 2 1 3 1 0 1 1 2 2 0 1 1 1 1 2l1 2h1c0 1 0 1 1 2v1 1l1 1c2 6 6 10 6 16v1h-2l-2-1-25-46 2 1c0 1 0 2 1 2l2-1c1 0 1-1 2-2-1-1-1-4 0-5z"></path><path d="M577 350c9 2 16 6 21 13 3 3 4 6 6 10-7-7-12-11-23-12-5 0-14 1-19 5v1 1c-1 0-1 0-2 1h1l-1 2v-1l-1-1s1-2 1-3c2-3 6-5 8-8 4-3 5-5 9-8z" class="B"></path><path d="M253 249l2 2 2 2 1 1 1 1 1 1-1 1-1-1c0 2 1 4 1 7 1 0 3 2 3 4 0 0 0 1 1 2 1 2 2 4 4 7 1 2 1 3 1 6 0 1 0 1-1 2h0c1 1 1 2 0 3v1l-6-9c-2-1-3-3-5-4h0l-5-5-7-7h1l1 1c1-1 1-1 2-1h0l2-2c-2-2-3-3-5-4h0v-1c2 2 5 3 7 5 1 0 1 0 2 1l1-1v-1c0-3-2-7-2-11z" class="K"></path><path d="M261 279s1 0 2 1v-1l-1-1 2-1c0-1-1-2-1-3-1-1-1 0 0-1 1 0 1 2 4 3 1 2 1 3 1 6 0 1 0 1-1 2h0c1 1 1 2 0 3v1l-6-9z" class="W"></path><path d="M250 261c-2-2-3-3-5-4h0v-1c2 2 5 3 7 5 1 0 1 0 2 1l1-1v-1c2 2 5 5 5 7v1c-2-1-3-2-5-4v1l5 6c-1 0-1 0-2-1l-1 1-2-1h0c1 2 2 3 4 5h-1-2 0l-5-5-7-7h1l1 1c1-1 1-1 2-1h0l2-2z" class="H"></path><path d="M250 261l3 3 2 2h0c-2 0-2 0-2 1-2-1-3-2-5-3v1c-1 0-1-1-2-1 1-1 1-1 2-1h0l2-2z" class="N"></path><path d="M353 738c-6 0-12-1-18-4-7-4-9-9-11-15l6 6a24.56 24.56 0 0 0 15 5c3-4-1-6 0-9 4 0 10 2 14 4h1c0 1 1 1 2 1v1s0 1-1 2c-2 0-3 0-5 1-2 0-3 0-4 1l1 1h1s1 1 1 2c1 0 3 1 4 1-3 1-6 1-8 2l2 1z" class="I"></path><defs><linearGradient id="j" x1="540.408" y1="510.235" x2="525.092" y2="515.265" xlink:href="#B"><stop offset="0" stop-color="#c4c1c2"></stop><stop offset="1" stop-color="#ececea"></stop></linearGradient></defs><path fill="url(#j)" d="M530 474l5 44 3 22c0 4 2 8 2 12 0 3-1 7-1 10-2-5-3-11-4-17-4-23-7-46-8-69l3-2z"></path><path d="M623 594c-3 0-6 3-9 5 2-1 3-3 4-5-1-1-1 0-2 0l-1-1 2-2c7-6 17-9 27-8h0c5 1 11 5 14 10v1c0 1-2 2-2 4-2-2-4-3-6-4-1-1-1-1-1-2-1-1-2-2-4-3v1c0 1 1 1 2 2h0c-1 0-2-1-4-1h-1c-5-1-11 1-16 2-1 0-2 1-3 1z" class="R"></path><path d="M644 583c5 1 11 5 14 10v1c0 1-2 2-2 4-2-2-4-3-6-4-1-1-1-1-1-2l3 1-1-1c-5-3-8-5-13-7l10 1-4-2h-1l1-1z" class="Y"></path><defs><linearGradient id="k" x1="477.36" y1="478.938" x2="493.813" y2="477.202" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#656564"></stop></linearGradient></defs><path fill="url(#k)" d="M498 457c0 2 0 3-1 5h1c-2 3-3 4-3 7h1c-2 4-4 7-5 11-1 2-2 6-3 8-1 1-3 2-4 2-2 0-4 0-5-1-1-2-2-5-2-7v-2h0c1-5 6-8 10-11s6-7 9-11l2-1z"></path><path d="M766 213c2-1 5-1 7-2 5 0 10 0 15 1 14 1 25 6 36 14 5 4 11 9 17 10 3 0 4-2 6-4 2 1 4 3 5 4l-1 1-1-1c0 1 0 1-1 2l-1-1c-1 1-3 2-3 3l-4 2-6-3-9-6c0-1-2-2-3-3-13-10-27-15-43-16 1-1 2-1 3-1h4-1c-1-1-2-1-3-1-5 0-11 0-16 1h-1z" class="I"></path><defs><linearGradient id="l" x1="551.315" y1="395.04" x2="541.769" y2="368.108" xlink:href="#B"><stop offset="0" stop-color="#adabaa"></stop><stop offset="1" stop-color="#cccac9"></stop></linearGradient></defs><path fill="url(#l)" d="M547 373c1 0 2-1 2-2v-1c5-1 7-7 11-8l-1 1 1 1v1 1c0 1-1 3-1 3l1 1v1h-1c-2 3-6 5-9 8-7 6-14 14-15 23-1 0-2 0-3-1h0c0-4 1-8 3-12 0-2 1-3 2-5 1-5 4-10 7-14l1-1 1-2 1 1v4 1z"></path><path d="M537 384h1l3-3c-2 3-3 6-5 9l-1-1h0c0-2 1-3 2-5z" class="F"></path><path d="M545 369l1-2 1 1v4 1h-1v1c-2 2-3 5-5 7l-3 3h-1c1-5 4-10 7-14l1-1z" class="C"></path><path d="M479 337c-2-4-5-7-8-10h0c3 2 5 5 10 6v-1c3 4 6 9 8 13 6 12 12 26 13 39h-4v3l-1-6c0-1 0-2-1-4l-3-10 1-3c-2-4-4-8-7-12l-1 1 1 1h0l-3-2h0c-1-1-1-2-2-3 0-1-1-1-1-2v-1c-1-1-1-2-2-3v-1-2c0-1-1-1-2-2h2l1 1h0v-1l-1-1z" class="G"></path><defs><linearGradient id="m" x1="473.878" y1="306.963" x2="478.674" y2="289.851" xlink:href="#B"><stop offset="0" stop-color="#bebdbc"></stop><stop offset="1" stop-color="#e5e4e1"></stop></linearGradient></defs><path fill="url(#m)" d="M493 322c-4-7-9-15-15-21-2-2-4-4-7-6-1-2-6-5-7-7l1-1c3-2 9 1 12 2l2 2c-1 1 0 1-1 1 0 1 1 2 2 3h-1c2 2 4 2 5 5h1l1-1c2 2 4 5 5 8 0 1-1 1-1 2l3 6c1 1 1 1 1 2h1c1 2 3 6 3 8 0 1 1 2 3 3v-1c1 0 2 3 2 4v3c-1 1-1 1-3 1l-7-13z"></path><path d="M486 299c2 2 4 5 5 8 0 1-1 1-1 2l-7-9h1 1l1-1z" class="O"></path><path d="M493 322v-1c-1-3-4-5-4-8h0c2 2 4 4 5 6s2 4 4 6c0 1 1 2 3 3v-1c1 0 2 3 2 4v3c-1 1-1 1-3 1l-7-13z" class="U"></path><path d="M663 723c4-1 8-1 12-2 4 0 9 3 13 5h0c-2 2-6 3-8 4 6 2 14 3 20 0l5-4c-2 4-7 8-13 9-7 1-17-1-24-3l-5-1-1-1c-1-1-1 0-2-1h-5c0-1 2-2 2-3l4-2 2-1z" class="I"></path><path d="M660 729h-5c0-1 2-2 2-3l4-2 1 3h1c1-1 2-1 3-1v1l-3 2c1 1 2 0 3 1 1 0 1 1 2 2l-5-1-1-1c-1-1-1 0-2-1z" class="F"></path><defs><linearGradient id="n" x1="364.272" y1="771.172" x2="394.832" y2="772.599" xlink:href="#B"><stop offset="0" stop-color="#bcb9b9"></stop><stop offset="1" stop-color="#e9e8e5"></stop></linearGradient></defs><path fill="url(#n)" d="M377 773c3 0 4 1 7 0l-1-1c0-2 0-5 1-6 1-2 2-3 4-4 2 0 4 0 5 1 2 1 3 3 3 5 0 3-1 6-3 8-4 5-9 7-15 7-5 0-10-1-13-4v-1l-1-1c0-2-3-6-3-7 0-2 0-3 1-4 1 4 3 8 7 11h2l2 1c1 1 2 1 3 1v-1c0-1 0-3 1-4v-1z"></path><path d="M388 766h1c2 1 2 1 2 3 0 1 0 2-2 3-1 1-1 1-3 0v-1c0-2 1-3 2-5z" class="L"></path><path d="M755 209c-4 0-9 2-13 3-2 1-4 1-5 2v1l8-3c2 0 4-1 5-1v1c-2 2-5 3-8 3-1 1-2 1-3 1l1-1s1 0 2-1h-1c-1 0-2 1-3 1h-1c-1 0-1 1-2 1l-1 1c-1-1-1-1-1-2 1-1 2-2 3-2h1l15-4c0-1 1 0 2-1h3c1-1 2-1 3-1h3c1-1 2-1 4-1 1-1 3 0 4 0 1-1 3-1 4-1h3c1-1 3-1 4-1h-1c-2-1-2-2-3-4 0-1-1-2-2-4h0c-1-1-1-2-1-3-1-1 0-1 0-1 2-1 5-1 6 0h1c1 0 1 0 2 1h0c2 1 1 0 2 1s2 2 3 2c1 1 2 1 3 2 2 0 3 2 4 3l1 1s1 1 1 2v1c1 0 1 0 1 1v1c1 1 1 0 0 1v-1c-3 1-6 1-8 0h-9c-7 0-14 0-21 1-1 1-2 1-3 1h-1-1-1z" class="b"></path><path d="M793 204c-1-1-1-2-1-3l2-1c1 1 3 3 4 6l1 1c-3 1-6 1-8 0h-9c-7 0-14 0-21 1-1 1-2 1-3 1h-1-1-1c6-2 12-2 18-3 7-1 13-2 19-2h1z" class="M"></path><path d="M793 204c-1-1-1-2-1-3l2-1c1 1 3 3 4 6h-5v-1h3c-1-1-2-1-3-1z" class="Y"></path><path d="M793 204l-1-1c-4 0-11-4-13-7l-1-1v1c0 1 2 5 3 5 1 1 1 0 3 1-1 0-2 1-3 1-2-3-4-7-6-10l1-1h1c2 0 4 0 6 1 1 1 2 2 4 3 2 2 4 2 7 4l-2 1c0 1 0 2 1 3z" class="W"></path><path d="M477 289c1-1 5-4 6-4 5 5 9 9 15 12l6-6 2 8c-1 2 0 3-1 5v2h-2-2l1 2h1v1c-1 1-2 0-3 1s-1 2-2 2l-4-5h-2c-2-4-4-7-7-9h0l1 1-1 1h-1c-1-3-3-3-5-5h1c-1-1-2-2-2-3 1 0 0 0 1-1l-2-2z" class="I"></path><defs><linearGradient id="o" x1="520.197" y1="542.468" x2="525.781" y2="542.148" xlink:href="#B"><stop offset="0" stop-color="#2d2c2e"></stop><stop offset="1" stop-color="#797877"></stop></linearGradient></defs><path fill="url(#o)" d="M521 484c1 9 0 18 0 27h1v-9l4 25c1 14 1 28 1 42 0 4 1 10-1 13 0-2 0-4-1-6v-2-2l-1-1v11h0l-1-37v3h0v8l-1 1-2 1 1-74z"></path><path d="M226 501c-7 3-14 6-23 6-12 0-21-5-29-13 5 2 10 5 15 6 8 2 21 4 28-1 1-1 2-2 2-3-1-2-1-3-3-5h0c3-2 5-3 9-3 0 1 0 1 1 2 3 1 9 0 13 0l2 2h-1c-5 0-10 0-13 3-1 2-1 4-1 6z" class="I"></path><path d="M823 230c1 1 3 2 3 3l9 6 6 3 4-2-1 2c-1 0-2 0-3 1 0 1 1 1 1 2 0 3 1 5 2 8l1 4c1 1 1 2 1 3 0 2 1 4 2 6 2 5 6 8 9 12h-1c-3-2-5-6-7-9l-3-4c1 1 3 6 3 7l-1 1 3 4c1 2 2 3 3 5l2 2 1 1h-1l1 2c1 1 0 5 0 7v1c0 2 0 3-1 4h-1v-2c1-2 1-8 0-10l-2-4c-3-5-7-11-10-16-4-7-7-15-11-22-1-3-4-5-5-8s-4-4-4-7z" class="Y"></path><path d="M826 233l9 6 6 3 4-2-1 2c-1 0-2 0-3 1 0 1 1 1 1 2 0 3 1 5 2 8l1 4h-1-1c0 2 3 5 2 8-4-6-7-13-10-19-1-1-1-3-2-4s-1-2-2-3c-2-2-4-3-5-6z" class="U"></path><path d="M844 253c-2-1-5-3-5-4-1-2-2-4-4-5 0 0-1-1-1-2v-1c0-1-2-2-2-2l1-1s1 1 2 1h0l6 3 4-2-1 2c-1 0-2 0-3 1 0 1 1 1 1 2 0 3 1 5 2 8z" class="N"></path><path d="M552 353l3-3h2 3c-1-1-1-1-2-1h1 1 1l-2-2v-3c1-1 1-2 3-2-1 1-2 3-1 5 0 1 2 2 3 2 4 2 9 0 13 1-4 3-5 5-9 8-2 3-6 5-8 8v-1-1l-1-1 1-1c-4 1-6 7-11 8v1c0 1-1 2-2 2v-1-4l-1-1-1 2-1-1-1 1v-1c2-5 5-9 8-13 0-1 0-1 1-2z" class="E"></path><defs><linearGradient id="p" x1="298.934" y1="655.418" x2="288.815" y2="664.849" xlink:href="#B"><stop offset="0" stop-color="#cdc8c5"></stop><stop offset="1" stop-color="#eceeef"></stop></linearGradient></defs><path fill="url(#p)" d="M292 650c2 0 3-1 5 1 9 3 16 8 22 15 4 5 7 10 12 14h-1l-3-3-1 1c-15-11-29-20-48-20l2-1-1-1h2c3-1 6 0 9-1l-1-1v-2c0-1 0-1 1-1h2v-1z"></path><path d="M300 654c2 1 3 2 5 4 1 1 3 2 4 2 4 3 8 7 12 10h0v1l-1 1c-1-1-2-1-2-2h-1c-4-2-7-4-9-7-3-3-8-4-12-5 1-1 1-1 3 0h0l4 1v-1l-1-1c-1-1-1-1-2-3z" class="G"></path><path d="M297 651c9 3 16 8 22 15 4 5 7 10 12 14h-1l-3-3c-1 0-2-1-2-2-1-2-2-3-4-5-4-3-8-7-12-10-1 0-3-1-4-2-2-2-3-3-5-4-1-1-2-1-3-3z" class="M"></path><path d="M292 650c2 0 3-1 5 1 1 2 2 2 3 3 1 2 1 2 2 3l1 1v1l-4-1h0c-2-1-2-1-3 0-1 0-2 0-3-1h-2-4c-1-1-2-1-4-1-1 1-2 1-3 1l-1-1h2c3-1 6 0 9-1l-1-1v-2c0-1 0-1 1-1h2v-1z" class="J"></path><path d="M242 303h0v1c1 1 2 1 3 2 1 0 2 1 2 2 0 3-2 3-1 6l3 2c-5 1-8 3-12 5-1 2-4 4-4 5s-1 3-2 3h-1c-1 1-2 2-2 3l-4 4h0v-1c2-2 2-4 2-6 0-1-1-1-1-2-2 3-4 5-5 9l-2 6c0-2 0-3 1-4l-1-1c1-6 4-11 6-16l4-5 2-3h0l2-2 2-1 8-7z" class="S"></path><path d="M242 308c-1 2-4 4-5 6l-9 9-2-1c2-3 4-5 6-7 3-3 7-5 10-7z" class="M"></path><path d="M242 303h0v1c1 1 2 1 3 2h-1l-2 2c-3 2-7 4-10 7l-2-2 2-2 2-1 8-7z" class="C"></path><path d="M245 306c1 0 2 1 2 2 0 3-2 3-1 6l3 2c-5 1-8 3-12 5v-1c1-3 4-4 6-7 1-2 0-2 0-4 0-1 1-2 1-3h1z" class="J"></path><path d="M230 313l2 2c-2 2-4 4-6 7l2 1-3 4c-2 3-4 5-5 9l-2 6c0-2 0-3 1-4l-1-1c1-6 4-11 6-16l4-5 2-3h0z" class="F"></path><path d="M226 322l2 1-3 4c-2 3-4 5-5 9l-2 6c0-2 0-3 1-4 0-6 4-12 7-16z" class="W"></path><path d="M358 678c1 1 2 2 2 3 2 2 5 6 7 7 2 2 3 5 4 7 2 4 5 7 7 10 5 6 11 11 16 16 0 2 0 3-1 5h0c-2-1-5-3-6-4-10-7-19-17-27-26-4-5-9-9-12-15h2c4 3 6 10 10 13l2 1h2v-1c0-6-4-10-6-16z" class="Z"></path><path d="M818 273c-2-4-8-11-5-16 0-1 2-2 3-2 1-1 2 0 4 0 2 2 3 4 3 6 1 4 0 7-1 10l-3 11c-6 16-17 26-31 34l-4 1c-1 0-1 0-2-1v-1l3-1c1-2 1-2 1-4l2-1 4-3-2 3h2l3-1h0c6-3 15-13 18-19-1-3 1-7 1-10 0-2 1-3 1-5l3-1z" class="R"></path><path d="M788 309l4-3-2 3h2l3-1c-3 3-7 4-10 6 1-2 1-2 1-4l2-1z" class="G"></path><path d="M818 273c0 6-2 11-5 16-1-3 1-7 1-10 0-2 1-3 1-5l3-1z" class="B"></path><path d="M816 258h2c1 0 2 1 2 3 2 3 0 7-1 10-2-3-4-7-5-10 0-1 1-2 2-3z" class="D"></path><defs><linearGradient id="q" x1="775.398" y1="386.91" x2="787.851" y2="391.242" xlink:href="#B"><stop offset="0" stop-color="#b6b4b3"></stop><stop offset="1" stop-color="#eae8e7"></stop></linearGradient></defs><path fill="url(#q)" d="M787 357c1-2 1-3 1-5 1-3 0-6 0-9 1-1 1-1 1-2s-1-2-1-3c1 0 2 0 3 1 1 6 2 12 2 19 1 18-4 38-13 55-4 8-9 15-13 23h0c0-4 0-6 1-9 0-2 0-3 1-5h1v1h0c1-1 2-1 2-2 1-1 1-2 2-3 1-2 2-4 3-7 1-1 1-2 2-3s2-3 2-4l1-1v-3c5-14 6-28 5-43z"></path><defs><linearGradient id="r" x1="500.998" y1="435.991" x2="475.501" y2="410.51" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#b8b6b6"></stop></linearGradient></defs><path fill="url(#r)" d="M488 385c2 2 4 3 6 5 0 3 0 3 2 5 0 1 1 2 1 3v1c0 1 0 2 1 3 2 11 1 23-1 33-1 1-2 1-2 2l-3 6-1 1c-3 2-5 6-7 10v1c-2-1-2-1-3-2v-3c3-1 4-3 5-5 2-3 3-7 4-11 2-11 1-23-2-34 1 1 1 2 2 3h0c1-3 3-8 2-10v-1-1c-2-2-3-4-4-6z"></path><defs><linearGradient id="s" x1="383.305" y1="738.797" x2="374.195" y2="727.203" xlink:href="#B"><stop offset="0" stop-color="#0f0e11"></stop><stop offset="1" stop-color="#2c2d2b"></stop></linearGradient></defs><path fill="url(#s)" d="M359 718c10 3 17 9 26 14l1 1c0 1 0 0 1 1s3 2 5 3l-1 1h-1-2c-5-1-11 0-16 0-6 0-12 1-19 0l-2-1c2-1 5-1 8-2-1 0-3-1-4-1 0-1-1-2-1-2h-1l-1-1c1-1 2-1 4-1 2-1 3-1 5-1 1-1 1-2 1-2v-1c-1 0-2 0-2-1h-1l-1-1c0-3 0-4 1-6z"></path><path d="M367 732c2 0 5 0 7 1 6 1 11 3 16 5h-2c-5-1-11 0-16 0-1-1-1 0-2-1h-2c-1-1-1-2-2-2h0v-1h2l-1-2z" class="E"></path><path d="M356 730c2 0 4 0 6 1h-5c3 1 7 1 10 1l1 2h-2v1h0c1 0 1 1 2 2h2c1 1 1 0 2 1-6 0-12 1-19 0l-2-1c2-1 5-1 8-2-1 0-3-1-4-1 0-1-1-2-1-2h-1l-1-1c1-1 2-1 4-1z" class="J"></path><path d="M359 718c10 3 17 9 26 14l1 1-6-3c-1 0-1-1-2-1v1 1c-5-2-11-1-16 0-2-1-4-1-6-1 2-1 3-1 5-1 1-1 1-2 1-2v-1c-1 0-2 0-2-1h-1l-1-1c0-3 0-4 1-6z" class="B"></path><path d="M755 462l3-4c2-3 5-4 8-6v1c-2 2-4 3-4 5-1 2-1 4 1 6h2c1 2 1 2 0 5 0 1-2 3-4 3 0 1-1 1-1 1l-6 1c0 1-1 3 0 4l1-1 1 1c-8 1-16 3-23 6h-2l-4 2h0v-1l9-8c1-1 3-3 5-4 1-3 2-4 3-5l3-3c1 0 1 0 2-1l1-1c1-1 1-2 2-3 0-2 1-4 2-5v4 1 1l1 1z" class="L"></path><path d="M754 455v4 1 1l-8 7c-2 2-3 4-5 5 1-3 2-4 3-5l3-3c1 0 1 0 2-1l1-1c1-1 1-2 2-3 0-2 1-4 2-5z" class="T"></path><path d="M731 484v-1c3-1 6-3 8-4 5-2 10-3 15-5 0 1-1 3 0 4l1-1 1 1c-8 1-16 3-23 6h-2z" class="G"></path><path d="M755 462l3-4c2-3 5-4 8-6v1c-2 2-4 3-4 5-1 2-1 4 1 6h2c1 2 1 2 0 5 0 1-2 3-4 3 0 1-1 1-1 1-2-1-3 0-5 0 1-2 2-2 3-3v-1l-2 1h-1l1-1v-1l-2 2-1-1h-2-1c2-2 4-4 5-7h0z" class="C"></path><path d="M728 649c6-1 12-1 18 3 3 3 5 7 7 11l3 6c0 5-1 9-4 13-5 5-11 7-19 8-2 0-6 0-8-1h-1l-1-1 4 1c4 0 7 0 11-1h1c5-1 10-4 13-8 2-4 3-8 2-13l-1-4c0 6-1 11-6 15-2 3-5 4-8 4-2 0-5-1-7-3-1-2-2-4-2-6 1-2 2-3 3-4 1-2 3-2 5-2 1 0 2 1 3 2 0 1 0 2-2 3h-1c-2 1-2 1-2 2 1 1 1 2 3 3 1 0 2 0 4-1s3-3 4-6 1-7-1-9c-3-4-6-6-10-7h-5l-4-1c-1-1-2-1-3-1l1-2c1 0 2 0 3-1zm-441 0h0 1c1 0 3 1 4 1v1h-2c-1 0-1 0-1 1v2l1 1c-3 1-6 0-9 1h-2l1 1-2 1h0c-2 1-3 1-4 1-2 1-4 3-5 5s-1 5 0 7c2 3 5 5 7 6h4l-2-1c-1 0-2-1-2-2-1-1-1-3 0-4 1-2 2-3 4-3 1-1 3 0 5 1 1 1 3 2 3 4 1 2 0 4-1 6-2 2-4 3-7 4-3 0-7-1-10-3s-6-7-7-11c0-5 1-9 5-13 5-6 12-6 19-6z" class="Q"></path><defs><linearGradient id="t" x1="664.001" y1="760.01" x2="626.928" y2="763.615" xlink:href="#B"><stop offset="0" stop-color="#c4c2c2"></stop><stop offset="1" stop-color="#e5e4e3"></stop></linearGradient></defs><path fill="url(#t)" d="M651 739c5 2 9 6 12 11 2 6 3 13 1 18-3 6-7 10-12 12s-11 2-16 0c-4-2-7-5-9-9-1-2-1-5 0-7 1-1 3-3 4-3 2-1 4-1 5 0s2 3 2 5c1 1 0 4 0 5 1 1 3 1 5 1s4-2 6-3l1 1c-1 2-2 3-4 4 0 1 0 0-1 1l1 1c-1 1-2 1-3 1v1h2c5-1 9-2 12-6 4-5 4-10 3-15 1-3-2-7-3-9-2-2-4-3-4-5v-1l-2-2v-1z"></path><path d="M632 766c1 0 1-1 3 0 0 2 0 3-1 4h-1c-1 0-1-1-2-2 0-1 0-1 1-2z" class="b"></path><path d="M504 290l4 7c1 5 3 11 4 16v6c3 12 2 25 2 37h-1c0 1 0 2-1 2v-4c-1-2-2-2-2-4l-5-18c-3-9-8-18-13-25h2l4 5c1 0 1-1 2-2s2 0 3-1v-1h-1l-1-2h2 2v-2c1-2 0-3 1-5l-2-8v-1z" class="L"></path><path d="M512 341c1 3 1 6 1 10-1-1 0-1-1-2 0-1 0-1-1-1v-1c-1-2 0-4 1-6z" class="W"></path><path d="M511 348c1 0 1 0 1 1 1 1 0 1 1 2v5c0 1 0 2-1 2v-4c-1-2-2-2-2-4l1-2z" class="a"></path><path d="M509 314c2 7 2 13 3 20v1l-1 1h0c0 1 0 4 1 5-1 2-2 4-1 6v1l-1 2-5-18 1-1v-1h1l1 4v1c1-2 0-1 1-2s0-1 1-2c-1-1-1-1-2-1v-1c0-1 0-2-1-2l1-1c-1-1-1-2-2-3h2c1-2-1-4 0-6 1-1 1-1 1-3z" class="K"></path><path d="M506 299l1 6 1 5 1 4c0 2 0 2-1 3-1 2 1 4 0 6h-2c1 1 1 2 2 3l-1 1c1 0 1 1 1 2v1c1 0 1 0 2 1-1 1 0 1-1 2s0 0-1 2v-1l-1-4h-1v1l-1 1c-3-9-8-18-13-25h2l4 5c1 0 1-1 2-2s2 0 3-1v-1h-1l-1-2h2 2v-2c1-2 0-3 1-5z" class="E"></path><path d="M506 299l1 6 1 5c-1 3-2 3-4 5h0 2v1c-2 1-2 1-3 2v1h-1c-2-2-4-5-4-7 1 0 1-1 2-2s2 0 3-1v-1h-1l-1-2h2 2v-2c1-2 0-3 1-5z" class="B"></path><path d="M507 305l1 5c-1 3-2 3-4 5h0 2v1c-2 1-2 1-3 2l-1-1 1-2v-1c-1-1 1-2 2-3h-1-1c1-1 2-2 2-3s1-2 2-3z" class="J"></path><path d="M753 498c5 2 9 5 12 9-8-1-16-3-24-2-23 3-38 23-56 34-10 6-21 9-32 12l-1-1h-3c0-1 1-1 1-2 2 0 3 0 4-1 1 0 2-1 3-2h1 1 1c1-1 2-1 4-2l1-1h1c0-1 1-1 2-2 1 0 0 0 1-1 1 0 3-1 4-2 0 0 1 0 2 1h-1l1 1 1-1c1-1 2-1 3-2 4-1 8-3 11-5 10-5 18-13 27-19l5-3 11-5c5-2 9-3 13-5h3c2-1 3-1 4-1zm73-346h2c1 1 1 5 1 6v19h-23l1-14 1-4c0-1 1-1 1-2 0-2 1-3 1-5h16z" class="B"></path><path d="M810 152h16c-3 3-6 11-10 11h-9l1-4c0-1 1-1 1-2 0-2 1-3 1-5z" class="D"></path><defs><linearGradient id="u" x1="512.057" y1="435.827" x2="488.66" y2="388.076" xlink:href="#B"><stop offset="0" stop-color="#7f7e7d"></stop><stop offset="1" stop-color="#b2b0b0"></stop></linearGradient></defs><path fill="url(#u)" d="M495 382c-1-2 0-4-1-6v-1c0-2-1-5-2-8 0-1-2-3-1-4l2 4 3 10c1 2 1 3 1 4l1 6v-3h4 2c-1 2-1 2 0 4l1 4c3 14 4 31 1 45v-1l-1 1-1 1c-1 1-1 2-2 3h-1 0c0-1 0-1-1-2-1 5-2 9-5 13 0-3 1-4 1-7 1-1 0-3-1-4 1-2 1-4 2-6 2-10 3-22 1-33 0-4-1-8-2-12l-1-8z"></path><path d="M495 382c-1-2 0-4-1-6v-1c0-2-1-5-2-8 0-1-2-3-1-4l2 4 3 10c1 2 1 3 1 4l1 6c4 17 6 36 2 52-1 5-2 9-5 13 0-3 1-4 1-7 1-1 0-3-1-4 1-2 1-4 2-6 2-10 3-22 1-33 0-4-1-8-2-12l-1-8z" class="M"></path><defs><linearGradient id="v" x1="249.044" y1="452.918" x2="243.33" y2="442.119" xlink:href="#B"><stop offset="0" stop-color="#a09e9d"></stop><stop offset="1" stop-color="#bfbebc"></stop></linearGradient></defs><path fill="url(#v)" d="M239 440c3 1 2 3 4 5h1v-2c-1-1-1 0-1-2h0c0-1 0-1-1-1v-2l-1-1 2-1c1 2 4 4 4 7 1 2 3 4 3 6l1 1 2-1 3 4v1 2c0 3 2 4 4 7 2 0 2 0 4 2l1 2c1 1 1 2 3 3l3 3c2 1 5 4 7 6l-1 1c-2 0 0-2-3-1l7 8c-1 0-4-4-6-5l-2 2c-6-3-13-4-19-6 0-1 1-1 2-1v-2c-1 0-2-1-4-1-1 0-2 0-3-1h0c-1-1-1-1-2-1h5v-1l-5-1 1-1 4 1v-1l1-1c-1-3-3-3-4-5-2-1-3-3-5-4v-1c-1-3-8-7-11-8v-1c4 2 8 5 13 5h0c-1-5-4-10-7-14z"></path><path d="M254 463h1c2 2 6 6 9 7l10 9 7 8c-1 0-4-4-6-5l-13-7c-2-2-5-3-7-4l5 1c1 0 2 1 3 1s1 1 2 0c-1-2-7-6-9-7s-3-1-3-3l1 1h1l-1-1z" class="L"></path><defs><linearGradient id="w" x1="273.476" y1="478.238" x2="256.411" y2="480.337" xlink:href="#B"><stop offset="0" stop-color="#8f8d8d"></stop><stop offset="1" stop-color="#bab9b7"></stop></linearGradient></defs><path fill="url(#w)" d="M249 473c4 0 7 1 10 2h3l13 7-2 2c-6-3-13-4-19-6 0-1 1-1 2-1v-2c-1 0-2-1-4-1-1 0-2 0-3-1h0z"></path><path d="M253 449l3 4v1 2c0 3 2 4 4 7 2 0 2 0 4 2l1 2c1 1 1 2 3 3l3 3c2 1 5 4 7 6l-1 1c-2 0 0-2-3-1l-10-9c-3-1-7-5-9-7h-1l-1-1c-1-2-2-2-2-3 0-2-1-2-1-3h-2v-2c1-1 1-2 2-2l1 1h0 1c-1-1-1-2-2-3v-1l1 1 2-1z" class="H"></path><path d="M253 449l3 4v1 2c-2-2-3-4-5-6l2-1z" class="G"></path><path d="M251 459l3 2v-2c2 1 3 3 5 5 2 0 5 4 5 6-3-1-7-5-9-7h-1l-1-1c-1-2-2-2-2-3z" class="Z"></path><path d="M655 537c4-1 8-3 11-5 23-15 38-38 45-65h0c1 2 1 3 2 5 1 17-20 39-31 52-2 1-4 3-5 4h-1c1 1 1 1 2 1v1 1c-1 1-1 2-2 3l-3 3c-1 1-3 2-4 2-1 1 0 1-1 1-1 1-2 1-2 2h-1l-1 1c-2 1-3 1-4 2h-1-1-1c-1 1-2 2-3 2-1 1-2 1-4 1 0 1-1 1-1 2h-1c-1-1-2-2-3-2h0l-1-1 1-1h-2l-2-2 3-3c4-1 8-2 11-4z" class="I"></path><path d="M644 541c4-1 8-2 11-4v1c-2 3-4 4-7 5 3 0 5-1 8-2l1 1c-1 0-2 1-3 1l1 1 2 1c-1 1-2 2-3 2-1 1-2 1-4 1 0 1-1 1-1 2h-1c-1-1-2-2-3-2h0l-1-1 1-1h-2l-2-2 3-3z" class="J"></path><path d="M658 594c4 7 7 14 8 22 1 4 1 8 1 12 1-4 2-9 1-13 0-5-2-11-1-16h1c4 4 4 10 4 15 0 6-2 13-3 19 2 1 4 0 6 1 1 6-5 14-8 19-3 4-5 9-7 14l-1-1 2-4v-5l2-6c2-8 2-17 0-25 0-1 0-2-1-3h-1c-1-5-3-10-6-15-2-2-5-4-7-7 2 0 5 0 7-1l1-1v-1h0c0-2 2-3 2-4zM174 176c-1-2 0-5 0-8v-16h45v4c1 2 0 3 1 4v1c0 2 1 2 0 3-7-1-14-2-22-4-1 0-3 0-4 1-7 2-9 4-13 9-2 1-3 3-4 4s-1 1-1 2h-2z" class="I"></path><path d="M500 525c1-1 1-3 1-5v-12h0c-2 5-1 10-3 15 0 1-1 3-2 4v-1c-1-3 0-7 0-10l1-14v-25c0-2-1-4 1-5h0l4 23h0l1-17 1-2c0-1 1-2 2-2 0 5 1 11 0 16l1 9 1-7 1 25c1 11 0 22 0 33-1-3-1-8 0-12l-1 1c-1-1-1-1-2-1 0 1 0 3-1 4-1-2-1-3-1-5h0v-1c0 1-1 4 0 5 0 1 0 2-1 3 0-2 0-7-1-9v-3-1 4c-2-3 0-7-2-10z" class="W"></path><path d="M503 478l1-2c0-1 1-2 2-2 0 5 1 11 0 16l1 9 1-7 1 25c-1 5 1 11-2 16-1-3-1-5-2-8 0-5-1-10-1-15v-1-8-2 6l-1-1h-1c0-3 1-6 0-9l1-17z" class="V"></path><defs><linearGradient id="x" x1="500.895" y1="488.334" x2="507.158" y2="490.182" xlink:href="#B"><stop offset="0" stop-color="#4d4d4e"></stop><stop offset="1" stop-color="#6b6a68"></stop></linearGradient></defs><path fill="url(#x)" d="M503 478l1-2c0-1 1-2 2-2 0 5 1 11 0 16v1 18h-1c0-3 0-5-1-8v-2 6l-1-1h-1c0-3 1-6 0-9l1-17z"></path><path d="M661 623h1c1 1 1 2 1 3 2 8 2 17 0 25l-2 6v5l-2 4 1 1-4 9-3 4c-4 8-8 16-14 23-2 3-4 6-7 9 0-3 3-5 4-8l3-6c-3 4-5 8-9 10 3-4 5-8 7-12 2-3 3-7 6-11 6-13 12-26 13-41 1-5 1-10 1-15v-2c0-1 1-3 1-4h1c1 0 0 0 1 1l1-1z" class="S"></path><path d="M654 673c0 1 1 2 2 3l-3 4-1-1c-1-2 1-4 2-6z" class="G"></path><path d="M660 657h1v5l-2 4 1 1-4 9c-1-1-2-2-2-3l6-16z" class="J"></path><path d="M650 672h1l-12 26c-3 4-5 8-9 10 3-4 5-8 7-12 2-3 3-7 6-11 3-1 6-10 7-13z" class="K"></path><path d="M657 651h1c0-1 0-1 1-2v-2c1-2 3-5 4-7s0-7 0-9c-1-1-1-4-1-5h1c2 8 2 17 0 25l-2 6h-1c0-2 0-5-1-7 0 3-2 11-4 13-1-1 0-2 0-3l2-9z" class="G"></path><path d="M661 623h1c1 1 1 2 1 3h-1c0 1 0 4 1 5 0 2 1 7 0 9s-3 5-4 7v2c-1 1-1 1-1 2h-1l1-1 2-8h-1c-3 3-3 12-4 16l-1 4-2 4c0 2-1 3-2 6s-4 12-7 13c6-13 12-26 13-41 1-5 1-10 1-15v-2c0-1 1-3 1-4h1c1 0 0 0 1 1l1-1z" class="O"></path><defs><linearGradient id="y" x1="234.581" y1="395.31" x2="219.079" y2="400.038" xlink:href="#B"><stop offset="0" stop-color="#bfbebc"></stop><stop offset="1" stop-color="#f3f1f1"></stop></linearGradient></defs><path fill="url(#y)" d="M243 326c3-1 6-2 10-2l-14 5c-2 1-3 2-5 3l1 1c-2 1-3 2-4 2-7 5-11 14-13 22-5 23 5 51 18 71 1 1 1 2 2 2h0c1 1 2 2 2 3l2 2c1 1 0 1 1 1l-2 1 1 1v2c1 0 1 0 1 1h0c0 2 0 1 1 2v2h-1c-2-2-1-4-4-5l-1-2c-15-25-28-51-25-81 1-7 3-14 5-20l1 1c-1 1-1 2-1 4l2-6c1-4 3-6 5-9 0 1 1 1 1 2 0 2 0 4-2 6v1h0l4-4c0-1 1-2 2-3h1c1 0 2-2 2-3l4-1c1 1 4 0 6 1z"></path><path d="M220 336h3v2c0 1 0 0 1 1l-2 3-1 1c-1 2-1 4-3 5-1-1 0-4 0-5v-1l2-6z" class="F"></path><path d="M233 326l4-1c1 1 4 0 6 1-5 2-10 5-15 9l-1 1v1l-3 4c-1 1-1 1-2 1l2-3c-1-1-1 0-1-1v-2h-3c1-4 3-6 5-9 0 1 1 1 1 2 0 2 0 4-2 6v1h0l4-4c0-1 1-2 2-3h1c1 0 2-2 2-3z" class="N"></path><path d="M745 492c4-3 12-8 18-7 1 1 1 2 2 3 2 1 4 0 6 0 7-1 13 0 19 3-1 1-3 4-3 6 2 3 6 4 11 5h0c9 1 19-1 27-5 2-1 5-3 6-3-8 8-17 13-29 13-17 1-31-14-49-10v1c-1 0-2 0-4 1h-3c-1 0-5 1-7 2h-1c1-1 2-2 4-2l-1-1-1-1c-1-1-1-1-2-1 1-2 2-3 3-5-1-1 0-1-1-2-2 0-3 2-5 2h0c1-2 3-3 5-4l3 3 1 2h1z" class="Q"></path><path d="M735 491c1-2 3-3 5-4l3 3 1 2h1v2c1 0 1-1 2-1h1v-1c2 1 4 0 5 0h1c3-1 6-2 9-1h0c-5 1-10 1-14 4-2 1-3 3-4 4l8-2v1c-1 0-2 0-4 1h-3c-1 0-5 1-7 2h-1c1-1 2-2 4-2l-1-1-1-1c-1-1-1-1-2-1 1-2 2-3 3-5-1-1 0-1-1-2-2 0-3 2-5 2h0z" class="G"></path><path d="M741 491c0 2 0 2 1 3s1 1 1 2l1 1v1h-1l-1 1h0l-1-1-1-1c-1-1-1-1-2-1 1-2 2-3 3-5z" class="T"></path><path d="M504 384h7c2 5 2 12 2 17 1 13 2 25 0 37 0 5-1 10-3 14v1c-1 3-3 6-6 9-3 2-6 5-8 7h-1c0-3 1-4 3-7h-1c1-2 1-3 1-5l-2 1-1-1 2-3h0c-2 1-2 2-3 3l-1 1-2-1 3-3 1-2c3-4 4-8 5-13 1 1 1 1 1 2h0 1c1-1 1-2 2-3l1-1 1-1v1c3-14 2-31-1-45l-1-4c-1-2-1-2 0-4h0z" class="B"></path><path d="M504 447v3h0c0 1-1 2-1 3l-1 2h1 1l1 1-1 1c-1 1-2 3-3 5l1 1c1-2 4-4 5-6v-1c1-1 1-3 2-4l1 1c-1 3-3 6-6 9-3 2-6 5-8 7h-1c0-3 1-4 3-7h-1c1-2 1-3 1-5 2-3 4-7 6-10z" class="J"></path><path d="M500 439c1 1 1 1 1 2h0 1c1-1 1-2 2-3l1-1 1-1v1c0 1 0 3-1 4l-1 6c-2 3-4 7-6 10l-2 1-1-1 2-3h0c-2 1-2 2-3 3l-1 1-2-1 3-3 1-2c3-4 4-8 5-13z" class="T"></path><path d="M494 454h0c2-1 4-4 6-5h0c0 1-2 4-3 5h0c-2 1-2 2-3 3l-1 1-2-1 3-3z" class="U"></path><path d="M500 449l3-7c1 0 1-1 2-1l-1 6c-2 3-4 7-6 10l-2 1-1-1 2-3c1-1 3-4 3-5z" class="Z"></path><path d="M466 339c-5-2-10-5-14-9-3-2-6-6-6-10 0-3 2-5 5-7-1-1-1-2-2-3 1-2 1-2 2-3h2c13 4 22 15 28 25v1c-5-1-7-4-10-6h0c3 3 6 6 8 10l1 1v1h0l-1-1h-2c1 1 2 1 2 2v2 1c1 1 1 2 2 3v1c0 1 1 1 1 2 1 1 1 2 2 3h0c1 1 1 2 1 3v3l-1-1c0-1 0-1-1-2l-1-2c-1-3-3-4-5-5h-1c-2-1-4-4-6-6l-4-3z" class="I"></path><path d="M467 336l-4-4h1c1 1 3 2 4 3l-3-4h1l2 2c0-1-1-2-2-3h0c2 1 6 6 8 6 1 0 1 0 2 1h3l1 1v1h0l-1-1h-2c1 1 2 1 2 2v2 1c1 1 1 2 2 3v1c0 1 1 1 1 2 1 1 1 2 2 3h0c1 1 1 2 1 3v3l-1-1c0-1 0-1-1-2l-1-2c-1-3-3-4-5-5h-1c-2-1-4-4-6-6l-4-3c1-1 1-2 1-3z" class="E"></path><path d="M467 336h1c1 1 4 2 4 4l-2 2-4-3c1-1 1-2 1-3z" class="F"></path><path d="M512 516c-1-5 0-11 0-16l1-33h1v8l1-3h0v3c0-2 0-5 1-7s1-8 1-11c1 4 1 8 1 12 0 2-1 4 0 6l2-16v10l1 15-1 74v9c1 1 0 7 0 8h-1v-11c-1-1-1-3-1-5h1-1c-1-1-1-1-2-1l-1 3c0-1 0-1-1-2 0 2 0 3-1 5h0l-1-28v-20z" class="V"></path><path d="M512 516v-10c2 2 1 9 1 11l1-4c1 3 1 5 0 8-1 1-1 3-1 4v4c0 2 0 5-1 7v-20zm7 22h-1v-11c-1 0-1-1-2-1 0-1 0-2-1-2l-1-1v-1l2-2v-2c-1-2-1-5-1-7 2-4 1-28 1-34h1v32l1 1c1 5 0 11 1 16v12z" class="K"></path><path d="M518 475l2-16v10l1 15-1 74v9c-1-3-1-7-1-11v-18-12c-1-5 0-11-1-16v-35z" class="C"></path><path d="M735 465l5-6c1 0 1 1 1 3 1 1 0 2 0 3l1 1c0-2 1-2 2-3v5c-1 1-2 2-3 5-2 1-4 3-5 4l-9 8v1h0l4-2h2c-13 7-22 18-32 29l-4 4-9 10c-3 3-7 6-9 9-1 1-2 1-3 2l-1 1-1-1h1c-1-1-2-1-2-1l3-3c1-1 1-2 2-3v-1-1c-1 0-1 0-2-1h1c1-1 3-3 5-4 2-2 5-4 7-6 6-6 12-14 18-22 6-9 13-18 17-28l3-5h1v1c1 3 0 6 0 10l7-9z" class="B"></path><path d="M678 531l1 1v-1h1l1-1c1 0 2-1 3-2 1-2 5-4 6-6 1-1 1-3 2-4 1 0 1 0 2-1 1 0 0 0 1-1 1 1 1 1 2 1l-9 10c-3 3-7 6-9 9-1 1-2 1-3 2l-1 1-1-1h1c-1-1-2-1-2-1l3-3c1-1 1-2 2-3z" class="D"></path><path d="M735 465l5-6c1 0 1 1 1 3 1 1 0 2 0 3l1 1c0-2 1-2 2-3v5c-1 1-2 2-3 5-2 1-4 3-5 4l-9 8v1h0 0l-5 5-1-1c1-1 2-1 2-3 1 0 0-1 0-1 0-1 1-2 2-3 2-3 5-6 8-9 1-2 3-3 4-5 0-1 0-1 1-1 2-3 2-4 2-8-1 2-3 5-5 5z" class="G"></path><path d="M742 466c0-2 1-2 2-3v5c-1 1-2 2-3 5-2 1-4 3-5 4l-1-1c3-3 5-6 8-10h-1z" class="H"></path><defs><linearGradient id="z" x1="525.412" y1="433.878" x2="540.388" y2="410.229" xlink:href="#B"><stop offset="0" stop-color="#929090"></stop><stop offset="1" stop-color="#b2b0af"></stop></linearGradient></defs><path fill="url(#z)" d="M532 401h0c1 1 2 1 3 1l1 3v7h1c0 8 0 14 2 22v4l2 2c2 4 5 7 7 11l1 1c2 1 3 1 4 2h1l-1-1h1c1 0 2 1 2 1l1 1v-1l1-1 1 1 5-7c0 2 0 4 1 6 1 4 2 6-1 10-4 2-8 3-13 2h-2-1l2 2c-1 1-1 1-2 1-1 1-2 1-3 2h-2l-1-1 1-1c0-2-1-3-1-4v-1c-2-3-5-5-7-8v-1c0-4-2-5-3-8-1-4-2-8-3-11v-8c0-9 1-18 3-26z"></path><path d="M545 461c0-2-4-5-6-7-2-3-5-8-6-13-1-1-1 0 0-1 1 3 2 5 4 8h0c2 3 5 5 7 7 1 2 1 3 2 4v1h-1l2 1 1 1c-1 0-3-1-3-1z" class="L"></path><path d="M535 442h1l1 1c1 2 5 7 7 8h1l4 5-1 1-4-2c-2-2-5-4-7-7 0-2-1-4-2-6z" class="K"></path><path d="M535 442l-1-1h0v-1-2h1s1 1 1 2c1 0 1-1 2 0 0 0 2 3 2 4 2 2 3 4 6 6v-1c-2-2-4-5-6-8l1-1c2 4 5 7 7 11l1 1h-1c-2 0-2-1-3-1h-1c-2-1-6-6-7-8l-1-1h-1z" class="U"></path><path d="M535 455s1 0 1-1l1 1c3 4 7 8 11 10l2 2c-1 1-1 1-2 1-1 1-2 1-3 2h-2l-1-1 1-1c0-2-1-3-1-4v-1c-2-3-5-5-7-8z" class="N"></path><path d="M564 447c0 2 0 4 1 6 1 4 2 6-1 10-4 2-8 3-13 2h-2c0-1 0 0-1-1s-2-1-4-2l1-1s2 1 3 1l-1-1-2-1h1v-1c-1-1-1-2-2-4l4 2 1-1-4-5c1 0 1 1 3 1h1c2 1 3 1 4 2h1l-1-1h1c1 0 2 1 2 1l1 1v-1l1-1 1 1 5-7z" class="F"></path><path d="M555 459v-2c-2-1-3-1-4-2v-1c1 1 2 1 3 1h1 0c1 1 1 1 2 1 1 1 3 3 5 3v1s-1 0-1 1l-1-1c-2 0-3 0-5-1z" class="E"></path><path d="M545 451c1 0 1 1 3 1h1c2 1 3 1 4 2h1l1 1h-1c-1 0-2 0-3-1v1c1 1 2 1 4 2v2h-2l-5-2 1-1-4-5z" class="X"></path><path d="M544 455l4 2 5 2h2c2 1 3 1 5 1l1 1-4 1c-2 0-6 0-9-1h0-1l-2-1h1v-1c-1-1-1-2-2-4z" class="M"></path><path d="M544 455l4 2 5 2c-1 1-1 1-2 1-2-1-2 0-3 0h-2 0v-1c-1-1-1-2-2-4z" class="R"></path><path d="M547 461h1 0c3 1 7 1 9 1l1 1c1 0 2 0 4-1l2 1c-4 2-8 3-13 2h-2c0-1 0 0-1-1s-2-1-4-2l1-1s2 1 3 1l-1-1z" class="E"></path><path d="M367 579c2-1 5-1 7-1-1 0-2 1-2 1-1 1-1 3-2 4-1 0-3 1-4 2-5 2-10 5-13 9h0l1-1h1c1 0 2-1 3-1 2 1 4-1 5 0l-1 1s-1 1-2 1c-1 1-2 1-3 2h0l-2 1-1 1-2 3h0 1c1 0 2 0 3 1h0c-6 6-10 13-11 21l1 3v7c1 2 1 5 0 7 0 2-1 3 0 5 1 1 0 3 0 5v3l1 4v2c1 1 1 0 0 1h0c-1-1-2-3-3-4-1-4-2-9-2-13-1 1-1 4 0 5-1 1-1 2-2 2l-2 1c-1 0-1-1-1-2l-2-1c-2-3-4-7-5-10 0-2 0-3 2-5l4 1c-1-9-2-18-1-26 0-3 1-7 4-9v2c1 2 0 4 0 6-1 5-2 14 0 19 1-10 2-20 6-29 5-10 13-15 22-18z" class="B"></path><path d="M345 623l1 3v7c1 2 1 5 0 7 0 2-1 3 0 5 1 1 0 3 0 5v3l1 4v2c1 1 1 0 0 1h0c-1-1-2-3-3-4-1-4-2-9-2-13-1-3-1-5-1-8 1 1 1 2 1 3 0 2 0 5 1 6v-13h1c0 1 0 4 1 5 1-2 0-9 0-12v-1z" class="G"></path><path d="M285 485c0-3-2-6-3-9v-4c-1-1-1-2-1-3l1-1c0 1 1 1 2 2v1c11 19 25 38 41 52h1l-4-4 2-1c12 13 25 23 43 23h5v5l-7 3c4 0 7 1 11 0h3v1c-3 1-6 1-8 2l10-1c-6 3-13 3-19 1l-13-4c0-1-4-2-5-3-4-2-8-4-11-7-11-8-19-17-28-26 0-2 0-3-2-5l-12-13c0-3-4-6-6-9z" class="Q"></path><path d="M535 378c0-2 3-4 4-7h1c1 0 1-1 2-1h1 1c-3 4-6 9-7 14-1 2-2 3-2 5-2 4-3 8-3 12-2 8-3 17-3 26v8c1 3 2 7 3 11 1 3 3 4 3 8v1c2 3 5 5 7 8v1c0 1 1 2 1 4l-1 1-3-2c-2-1-4-2-6-4l-4-4-1-2c-6-8-7-21-8-31-1-15 0-31 2-46 1-1 1-1 2-1 3 0 5 1 7 0 2 0 2-1 4-1z" class="B"></path><path d="M528 410c0-1 0-1-1-2-1 0-2 0-3-1h0 2l1-1-2-1v-2h0l1-1h0l-1-1h0c1-1 2-1 3-2v-4h0c0-1-1-1-1-2h-1 0l4-1h0c1-2 1-3 2-4l-4 22z" class="D"></path><path d="M530 444l5 10v1c2 3 5 5 7 8v1c0 1 1 2 1 4l-1 1-3-2c-2-1-4-2-6-4l-4-4c2 0 4 2 5 4l1 1h4v-1c-1 1-1 1-3 0v-2h0v-1l-1-2-1-2v-1h-1c0-1-1-2-1-3l-1-1-2-5 1-2z" class="O"></path><path d="M539 467c1 0 1-1 1-2l-1-2 1-1 1 2h1c0 1 1 2 1 4l-1 1-3-2z" class="G"></path><path d="M535 378c0-2 3-4 4-7h1c1 0 1-1 2-1h1 1c-3 4-6 9-7 14-1 2-2 3-2 5-2 4-3 8-3 12-2 8-3 17-3 26v8c1 3 2 7 3 11 1 3 3 4 3 8l-5-10c-2-11-3-22-2-34l4-22 3-10z" class="a"></path><defs><linearGradient id="AA" x1="492.16" y1="375.38" x2="458.293" y2="375.112" xlink:href="#B"><stop offset="0" stop-color="#c7c6c4"></stop><stop offset="1" stop-color="#f2f1f0"></stop></linearGradient></defs><path fill="url(#AA)" d="M462 347h1c4 1 8 7 12 3 1 0 1-1 1-2h1c2 1 4 2 5 5l1 2c1 1 1 1 1 2l1 1v-3c0-1 0-2-1-3l3 2h0l-1-1 1-1c3 4 5 8 7 12l-1 3-2-4c-1 1 1 3 1 4 1 3 2 6 2 8v1c1 2 0 4 1 6l1 8c1 4 2 8 2 12-1-1-1-2-1-3v-1c0-1-1-2-1-3-2-2-2-2-2-5-2-2-4-3-6-5 1 2 2 4 4 6v1 1c1 2-1 7-2 10h0c-1-1-1-2-2-3-3-8-7-17-15-22-2-1-7-4-8-6 2-2 5-3 8-4-2-4-5-5-8-7l-2-2c-1-2-1-2-3-3v-1l1-1c-1-2-2-3-2-5l3-2z"></path><defs><linearGradient id="AB" x1="495.462" y1="374.562" x2="471.664" y2="370.64" xlink:href="#B"><stop offset="0" stop-color="#a4a2a3"></stop><stop offset="1" stop-color="#dbdbd8"></stop></linearGradient></defs><path fill="url(#AB)" d="M484 352l3 2h0l-1-1 1-1c3 4 5 8 7 12l-1 3-2-4c-1 1 1 3 1 4 1 3 2 6 2 8v1c1 2 0 4 1 6l1 8c1 4 2 8 2 12-1-1-1-2-1-3v-1c0-1-1-2-1-3-2-2-2-2-2-5-2-2-4-3-6-5-5-7-11-13-14-21-1-2-1-3-2-4l-1-1h0l-3-3h1l2 1v1h1c-1-1-1-1 0-1h1 1c1 2 3 3 6 3l2-2v-1c1 1 1 2 1 3 0-1 1-2 0-3v-1-1c1 1 1 1 1 2l1 1v-3c0-1 0-2-1-3z"></path><path d="M494 390c-1-1-1-2-1-3v-1c-1 0-2-1-3-2l2-1v-1c0 1 0 2 1 3l3 4v1c1 4 2 8 2 12-1-1-1-2-1-3v-1c0-1-1-2-1-3-2-2-2-2-2-5z" class="G"></path><path d="M484 352l3 2h0l-1-1 1-1c3 4 5 8 7 12l-1 3-2-4c-1 1 1 3 1 4 1 3 2 6 2 8v1c1 2 0 4 1 6l-2-2h0v-1c-1-1-1-2-2-3v-2c-1 0 0 0-1-1 0-1-1-2-1-4-1-4-1-8-4-11v-3c0-1 0-2-1-3z" class="H"></path><path d="M237 419l1 1c2-2 2-9 3-12 1 1 2 3 2 5l1 4c5-7 8-12 15-17 3-2 5-3 7-4 2 1 2 3 2 5 10-2 19-3 28 2 4 3 7 8 8 12v4c-3-6-6-10-13-12-3-1-6-1-8-3h1l-1-1c-6 1-10 2-15 5 7-2 13-4 20-1v3c-8-1-14 0-20 5-3 2-7 6-9 10h1c-1 2-2 2-2 4 2 1 2 1 4 1l-1 1h-1c-2 1-2 2-3 4-1-1-2-1-3-2v9c1 1 1 2 1 3-1 2 1 5 1 8l-3-4-2 1-1-1c0-2-2-4-3-6 0-3-3-5-4-7-1 0 0 0-1-1l-2-2c0-1-1-2-2-3 0-4-1-8-2-12l1 1z" class="Q"></path><path d="M254 415c1 0 2 0 3-1s2-1 4-2h0l1 1-1 2c-1 0-1 1-2 2 0 1-1 3-1 4s0 3-1 4h2 1c-1 2-2 2-2 4 2 1 2 1 4 1l-1 1h-1c-2 1-2 2-3 4-1-1-2-1-3-2v9c1 1 1 2 1 3-1 2 1 5 1 8l-3-4-2 1-1-1c0-2-2-4-3-6 1 1 2 1 3 2l-1-3c-2-2-2-4-2-7s0-7 1-11v1 7h0c2-3 2-7 3-10s1-5 3-8v1z" class="U"></path><path d="M247 435c2 6 4 10 6 14l-2 1-1-1c0-2-2-4-3-6 1 1 2 1 3 2l-1-3c-2-2-2-4-2-7z" class="E"></path><path d="M258 421c0 1 0 3-1 4h2 1c-1 2-2 2-2 4 2 1 2 1 4 1l-1 1h-1c-2 1-2 2-3 4-1-1-2-1-3-2l1-1v-3l3-8z" class="J"></path><path d="M254 415c1 0 2 0 3-1s2-1 4-2h0l1 1-1 2c-1 0-1 1-2 2-2 2-3 5-4 8l-1 2c0 2-1 4-1 6v1c-1 2 0 5 0 7v1c-1 0-1-1-1-2v-1c0-1-1-2-1-3v-1h-1v1h0v3h0c-1-1-1-3-2-4v-3h0c2-3 2-7 3-10s1-5 3-8v1z" class="H"></path><path d="M254 415c1 0 2 0 3-1s2-1 4-2h0l1 1-1 2c-1 0-1 1-2 2-2 2-3 5-4 8l-1 2h-1c0-1 1-4 1-5-1 1-2 2-2 3s0 1-1 2c0-2 1-5 2-7l1-5z" class="D"></path><defs><linearGradient id="AC" x1="549.52" y1="381.638" x2="568.941" y2="306.183" xlink:href="#B"><stop offset="0" stop-color="#b9b8b8"></stop><stop offset="1" stop-color="#f3f1ef"></stop></linearGradient></defs><path fill="url(#AC)" d="M531 378l-1-1c0-3 1-5 2-8 3-10 7-21 13-30 6-10 14-20 24-27 2-2 5-4 8-5l1 1c0 2-2 4-3 6h2l2 1c3 1 5 2 6 4s1 5 0 7c-2 4-7 6-11 8s-8 4-12 8h0c-2 0-2 1-3 2v3l2 2h-1-1-1c1 0 1 0 2 1h-3-2l-3 3c-1 1-1 1-1 2-3 4-6 8-8 13v1l1-1 1 1-1 1h-1-1c-1 0-1 1-2 1h-1c-1 3-4 5-4 7-2 0-2 1-4 1v-1z"></path><path d="M551 340c1-2 3-4 5-5l1 2h-1l-4 4-1-1z" class="F"></path><path d="M539 363c0 1 1 2 1 3-1 3-3 6-5 9h0l4-12z" class="a"></path><path d="M544 354l-1 1c0 1 0 2-1 4h1 1l-4 7c0-1-1-2-1-3 1-3 2-5 3-7l2-2z" class="M"></path><path d="M551 340l1 1c-1 1-2 2-3 4-2 2-6 7-5 9l-2 2c0-2 0-3 1-4l1-1-1-1c0-1 0-1 1-2 1-2 3-3 4-5 0-1 1-2 3-3z" class="J"></path><path d="M549 345l2 2-7 12h-1-1c1-2 1-3 1-4l1-1c-1-2 3-7 5-9z" class="W"></path><defs><linearGradient id="AD" x1="557.131" y1="338.743" x2="575.076" y2="329.068" xlink:href="#B"><stop offset="0" stop-color="#aaa9a7"></stop><stop offset="1" stop-color="#cecdcb"></stop></linearGradient></defs><path fill="url(#AD)" d="M559 335c1-1 2-2 3-2 2-2 5-4 7-5s3-2 5-2c-1 0-1 0-1 1h3v1c-1 1-2 3-2 5v1c-4 2-8 4-12 8h0c-2 0-2 1-3 2v3l2 2h-1-1-1c1 0 1 0 2 1h-3-2l-3 3c0-2 1-2 2-4l1-1 1 1 1-1v-1l1-1-1-1h1v-2c-1-1 0-2 0-3l-1-1c1-1 1-2 2-3v-1z"></path><path d="M559 335v1c-1 1-1 2-2 3l1 1c0 1-1 2 0 3v2h-1l1 1-1 1v1l-1 1-1-1-1 1c-1 2-2 2-2 4-1 1-1 1-1 2-3 4-6 8-8 13v1l1-1 1 1-1 1h-1-1c-1 0-1 1-2 1h-1c-1 3-4 5-4 7-2 0-2 1-4 1v-1h2c0-2 1-2 2-3h0c2-3 4-6 5-9l4-7 7-12-2-2c1-2 2-3 3-4l4-4h1l2-2z" class="N"></path><path d="M556 337c0 1 0 2-1 2l2 1v1c-2 1-2 2-3 3l-1 1-2 2-2-2c1-2 2-3 3-4l4-4z" class="S"></path><path d="M746 425c-6-7-11-9-20-11l3-4c-8-1-14-1-22 0h0c0-1 0-2 1-2 2-4 7-6 12-7h1c6-1 11 0 17 1l1-6c7 2 15 8 19 14 2 2 3 4 4 6h1c1-1 1-3 2-5h0c2 2 4 4 4 7v1c1-2 2-4 2-5 3-6 5-11 7-17 0 2 0 2-1 4v1l-3 8-2 5c0 1-1 2-2 4v3h-1c-1 2-1 3-1 5-1 3-1 5-1 9h0l-5 11c-1 3-2 5-4 7-1 2-1 3-2 4l-2 2v-1-4c-1 1-2 3-2 5-1 1-1 2-2 3l-1 1c-1 1-1 1-2 1l-3 3v-5l3-4 2-1c2-1 2-3 2-5 1-2 1-3 1-4 0-3 0-4-2-6l-2-7-4-6h1c2 0 2 1 3 0 0-2 0-2-1-3 1-1 1-2 1-3l-1 1h-1z" class="I"></path><path d="M756 424c-1-2-2-4-2-7 0 0 1-1 1-2l1-1 1 1c-2 2-1 6-1 9z" class="B"></path><path d="M756 424c0-3-1-7 1-9 2 3 4 6 4 10h-1v4h-1 0-1l-1-2-1-3z" class="E"></path><path d="M759 429h1v-4h1 0l1 3c2 4 2 10 0 14v5c-1 3-2 5-4 7 0-1 1-3 1-4v-1c0-2 0-2 1-4 0-1 1-6 0-8 0-1-1-2-1-3v-5z" class="G"></path><path d="M759 429h1v-4h1 0v15c1 2 0 2 0 4v3l-1 1-1 2v-1c0-2 0-2 1-4 0-1 1-6 0-8 0-1-1-2-1-3v-5z" class="N"></path><path d="M748 420l-3-4h1c1 1 2 2 3 4h2c3 2 3 6 6 7l1 2h1 0v5c0 1 1 2 1 3 1 2 0 7 0 8-1 2-1 2-1 4v1c0 1-1 3-1 4-1 2-1 3-2 4l-2 2v-1-4c-1 1-2 3-2 5-1 1-1 2-2 3l-1 1c-1 1-1 1-2 1l-3 3v-5l3-4 2-1c2-1 2-3 2-5 1-2 1-3 1-4 0-3 0-4-2-6l-2-7-4-6h1c2 0 2 1 3 0 0-2 0-2-1-3 1-1 1-2 1-3-1-1-3-2-4-4l2-1 1 1h1z" class="V"></path><path d="M749 420h2c3 2 3 6 6 7l1 2v11c-2-7-5-13-9-20z" class="F"></path><path d="M759 434c0 1 1 2 1 3 1 2 0 7 0 8-1 2-1 2-1 4v1c0 1-1 3-1 4-1 2-1 3-2 4l-2 2v-1-4l2-4 2-8c1-3 1-5 1-9z" class="S"></path><path d="M756 451c0 2 1 3 0 5h0l-1 1 1 1-2 2v-1-4l2-4z" class="U"></path><path d="M754 438c1 4 2 8 0 12v1c0 2-1 3-1 4-1 3-4 7-6 10l-3 3v-5l3-4 2-1c2-1 2-3 2-5 1-2 1-3 1-4l1-1c1-2 0-4 0-6 1-2 1-2 1-4z" class="S"></path><path d="M748 424c-1-1-3-2-4-4l2-1 1 1h1c2 3 3 8 4 11 1 2 2 5 2 7s0 2-1 4c0 2 1 4 0 6l-1 1c0-3 0-4-2-6l-2-7-4-6h1c2 0 2 1 3 0 0-2 0-2-1-3 1-1 1-2 1-3z" class="G"></path><path d="M748 436l1 1h2c0-2-1-5-1-7h1s0 1 1 1h0c1 2 2 5 2 7s0 2-1 4c0 2 1 4 0 6l-1 1c0-3 0-4-2-6l-2-7z" class="H"></path><path d="M506 381c-7-27-16-53-34-74-8-10-19-16-28-24-12-10-21-21-25-36-2-11-2-24 4-34 4-7 9-12 17-14 7-2 15-1 21 3 5 3 9 9 10 14s0 11-2 15c-3 4-7 7-12 8-4 1-9 0-12-3s-5-6-5-10c0-3 1-6 3-8s4-3 6-3c3 0 5 1 7 2 1 2 1 4 1 6-1 1-2 2-3 2-2 0-3 0-5-1v-3c1-1 1-2 3-2h0-2c-1 0-2 1-3 2s-1 3-1 4c1 3 2 4 4 6h0c2 1 4 1 6 1 3-1 6-2 8-5s2-6 2-9v-1c-1-2-1-4-3-5-2-4-6-7-10-7-7-2-14 0-20 4s-9 10-11 17c-1 7 0 13 1 19 6 26 34 38 51 57 19 22 30 51 35 79-1 0-1 0-2 1l-1-1z" class="I"></path><path d="M595 177c15 0 29 5 39 16 11 12 14 27 13 43 0 19-7 38-13 56l-15 51-43 140-30 96-12 37c-2 6-3 13-6 19l-15-42c-2-6-5-13-7-19h1l1 1v1 1s0 1 1 2c0 0 0 1 1 2v2c1 1 1 1 1 2 0 0 0 1 1 2v1s0 1 1 2v1c0 1 0 1 1 2l1 2h0v1c0 1 0 1 1 2v1c0 2 1 3 2 4l1 6c1 1 1 2 2 3l1 5c1 1 1 2 2 3v1 2c1 1 1 2 2 3v1 1c1 1 1 2 1 3 1 0 1 1 1 2l1-2v-3l1-2c1-2 1-4 2-6 0-1 1-2 1-3l1-2c0-2 1-5 2-7 0-2 1-4 2-6v-3l4-11c0-1 1-2 1-3l1-2v-3l3-7v-3l3-6c0-2 1-5 2-7 0-2 1-4 2-6v-3l1-2c0-1 1-2 1-3l2-5c0-2 1-5 2-7 0-2 1-4 2-6v-3l3-7c0-2 1-4 2-6 0-2 1-5 2-7v-3l1-2c0-1 1-2 1-3l2-5v-1c0-2 1-4 1-5-2 1-4 3-6 3-2 1-3 1-5 2h-6-1c-1-1-2-1-3-1l-1-1-3-3h0c0-1-1-2-1-3l-5-8v9c1 1 0 3 1 5v3 1 4 1c1 1 1 2 1 3s-1 3 0 3v2 1c1 1 1 2 1 4h0v3c0 2 0 3 1 4v3c0 1 0 2 1 3v4 1c0 1 0 2 1 3v1 1c1 1 1 3 1 5v9 2c-1 1-1 2-1 4v1c-1 2 0 0 0 2l-2 2h0l-1-3c-1-2 0-1-1-2v-2l-1-1v-2c-1-2 0-1-1-2h0v-2-3c-1-2-1-2-1-4h0c-1-1-1-1 0-2h0v-3-7c-1-1-1-2-3-3h-1c-2 6 3 14 0 19v1l-3-22v-1l1-1 1 1v-3c1-4 0-8-1-13 0-3 1-6 1-9-1-3 0-5 0-8-1-2-1-7-1-9l1-2c0-1 0-1 1-1 0-1 0-1 1-3h0l1 1c0 1 0 1 2 2v-1-1c-1-1-1 0-2-1h0-1c-1-1-2-2-3-2l-2-2c-1 0-1 0-2-2h1c2 2 4 3 6 4l3 2 1 1h2c1-1 2-1 3-2 1 0 1 0 2-1l-2-2h1 2c5 1 9 0 13-2 3-4 2-6 1-10-1-2-1-4-1-6 0-1 0-1-1-2v-1-1l1 1 1 1v6 1h0c0 1 0 1 1 2s1 2 1 3v1 1 1c-1 2-2 3-4 4v1c-1 1-1 2 0 3 0 1 0 1 1 1 1 3 0 2-1 4v4c2 2 3 2 5 3h4c2-1 0 0 2 0h1c1-1 2-4 2-5s1-2 1-3l2-5c0-2 1-5 2-7v-3l1-2c0-1 1-2 1-3l4-11v-3l1-2c0-1 1-2 1-3l1-2v-3l1-2 1-1v-2c1-2 1-5 2-7s1-4 2-6 1-5 2-7c0-1 1-2 1-3l3-8v-3l3-6c0-2 1-5 2-7v-3l1-2c1-2 1-5 2-7l2-4v-3l2-5c1-2 1-5 2-7 0-1 1-2 1-3l2-5v-3l3-8c1-2 1-4 2-6 0-1 1-2 1-3l2-5c0-2 1-5 2-7 0-2 1-4 2-6v-3l1-2c0-1 1-2 1-3l1-2c0-2 1-5 2-7v-1c1-3 2-5 2-8l3-8v-2l1-2c0-1 0-2 1-3v-3c0-1 0-1 1-2v-3c0-1 0-2 1-3v-4c1-3 1-11 1-14v-5c-1-1-1-2-1-3v-3l-1-2-2-5c0-2-1-2-1-4s-3-5-4-7l-1-1c0-1-1-1-2-2-1-3-3-5-5-7h-1l-5-4h0c-3-1-6-4-9-4l-1-1c-3-1-5-1-8-2h-1c-2-1-5 0-7-1h0c-1 0-1-1-2-1h-1z" class="b"></path><path d="M551 467l4 3c2 1 8-1 9 1l-2 2c-1-1-3 1-5 1s-3-1-5-2v-1c-1-1-1-2-1-4z" class="K"></path><path d="M545 541c3-3 2-8 3-11l1 7c1 4 1 8-1 11v2 1l-1 1v1c-1-1-1-1-1-2-1-3-2-6-2-9 1-2 0-4 1-5v4z" class="P"></path><path d="M545 541c3-3 2-8 3-11l1 7-2 7h-1l-1-3z" class="M"></path><path d="M543 493l5 37c-1 3 0 8-3 11v-4c-1 1 0 3-1 5l-2-9v-3h1c1-2 0-7 0-9v-1l-1-2-1-8v-5c1-4 1-8 2-12z" class="Y"></path><path d="M550 467h1c0 2 0 3 1 4v1c2 1 3 2 5 2s4-2 5-1v1c0 2 0 3 1 4 2 2 6 3 9 3h3c-5 4-10 7-17 6-3 0-5-1-7-4-1 0-2-1-2-2-2-3-4-7-5-10l-1-1h2c1-1 2-1 3-2 1 0 1 0 2-1z" class="T"></path><path d="M544 471c1 0 2 1 3 1l1 1c1 2 1 3 1 6v1 1c-2-3-4-7-5-10z" class="K"></path><path d="M537 484c0-3-1-10 2-12h0v1l1 10c2-2 1-7 1-10h1l1 20c-1 4-1 8-2 12v5l1 8 1 2v1c0 2 1 7 0 9h-1v-7c-1-1-1-2-3-3h-1c-2 6 3 14 0 19v1l-3-22v-1l1-1 1 1v-3c1-4 0-8-1-13 0-3 1-6 1-9-1-3 0-5 0-8z" class="L"></path><path d="M539 473l1 10c2-2 1-7 1-10h1l1 20c-1 4-1 8-2 12v5l1 8 1 2v1c0 2 1 7 0 9h-1v-7l-2-19c-1-4-1-9-1-13-1-4 0-8 0-12v-5h0v-1z" class="O"></path><path d="M578 225c-2-2-2-2-2-4s1-3 2-4 4-1 6-1c3 1 5 3 7 6 1 2 1 5 0 8s-3 6-7 8c-3 1-8 2-12 0-4-1-8-5-10-10-2-4-2-11 0-16 3-5 8-10 14-12 7-3 15-2 22 1 8 4 13 10 15 18 4 9 3 19 2 28-4 10-9 19-17 27-3 3-7 7-11 9-5 4-10 7-15 11-10 7-18 16-25 27-6 9-10 20-13 31-2 4-3 9-4 14l-2 8c0 1-1 3-1 3 0 1 0 1-1 1l-1-1s-1 0-1-1c0-3 2-9 2-12 5-27 18-55 39-73 13-10 28-17 38-30 7-10 11-21 9-33-2-7-5-14-11-19-6-4-13-6-20-4-5 1-10 3-12 8-2 3-3 7-2 11 1 3 3 6 6 7 2 1 5 1 7 0 2 0 4-2 4-4 1-2 1-3 0-5v2c-1 1-2 2-3 2-2 0-3-1-3-1z" class="I"></path><path d="M578 225v-2c1 0 2-1 3-2v-1h2l1 2v2c-1 1-2 2-3 2-2 0-3-1-3-1z" class="D"></path><path d="M293 628c-8-6-19-10-25-19-4-6-4-13-4-20l5 4c0-6 0-12 1-18l1-1c1-5 5-11 9-16-1 5-1 10 0 14s2 7 4 10c2 4 3 7 6 10l2 2h1l1-3 1-1c8 7 12 21 16 31 2 4 5 8 7 12h0c0 2 0 3 1 4v1l-3-2c-1-1-2-1-3-3s-2-4-4-6l-1-2-2-1c1 3 6 8 6 10v2l4 5h-1c-1-1-3-3-4-5-2-3-4-7-6-8l5 9c1 2 10 14 10 15-3-3-6-7-9-11-1-2-2-3-3-5 0 3 4 8 5 10 1 0 2 2 2 2v1l-1-1h-1 0c-1 0-2-1-3-1l-1-1h0c-1-1-2-1-2-2-1-1-1-2-2-3-2-1-4-3-5-5h-2c-1-1-1-1-2-3s-2-3-3-5z" class="Q"></path><path d="M305 628l-1-2c-3-4-4-9-7-13 0-2-3-5-2-7l11 18c1 3 6 8 6 10v2l4 5h-1c-1-1-3-3-4-5-2-3-4-7-6-8z" class="Y"></path><path d="M278 582l1 1 4 11c1 2 3 5 3 8v8l-3-5c-2-2-3-4-5-6 0 2-1 4-1 6-1-4-2-6-2-10l3-12v-1z" class="P"></path><path d="M293 628h1c1-2 0-3 1-4 1 0 1-1 1-1 0-1 0-1-1-2l1-1c1 1 2 3 3 4 1 2 3 4 4 5s0 0 1 2l2 3h1c0 1 0 1 1 2 0 3 4 8 5 10 1 0 2 2 2 2v1l-1-1h-1 0c-1 0-2-1-3-1l-1-1h0c-1-1-2-1-2-2-1-1-1-2-2-3-2-1-4-3-5-5h-2c-1-1-1-1-2-3s-2-3-3-5z" class="E"></path><path d="M319 666c1-1 2-2 4-3 12 12 22 25 33 37 9 9 19 18 30 25 2 1 5 5 7 5v-4c1-2 1-3 1-5 1 1 2 2 4 2l4-6 6 9 18 12 7 2-3 2c1 1 3 1 4 2 5 1 10 3 15 4 4 0 8 0 12 2l4 10h-1c-1-1-4-2-5-2h-1c-1-1-2-1-3-1l-1 1c-2 0-4-1-6-1h1v1l6 1c-2 1-3 1-5 0-2 0-4 0-6-1l2 1c2 1 5 1 7 2v1c-9-2-18-5-27-9-14-6-27-15-40-21h-1c-9-5-16-11-26-14v-1c-3-2-7-2-11-4h1c3 0 6 1 9 1l1-1-39-33c0-2-1-2-2-3h1 2 1l10 7h0l-6-6 1-1 3 3h1c-5-4-8-9-12-14z" class="I"></path><path d="M419 740c-2-1-6-4-7-5-2-2-6-7-6-9h1c3 6 10 10 16 13-1 0-2-1-3 0 0 1 0 1-1 1z" class="P"></path><path d="M408 726l18 12-3 1h0c-6-3-13-7-16-13h1z" class="F"></path><path d="M426 738l7 2-3 2c1 1 3 1 4 2h-1l1 1h-1l-1 1h0l-2-1c-4-1-7-3-11-5 1 0 1 0 1-1 1-1 2 0 3 0h0l3-1z" class="R"></path><path d="M426 738l7 2-3 2c-2-1-5-2-7-3l3-1z" class="G"></path><path d="M349 713c3 0 6 1 9 1l1-1 27 19h-1c-9-5-16-11-26-14v-1c-3-2-7-2-11-4h1zm94 41c-10-4-18-7-27-14v-1c5 4 13 8 19 8h1c2 0 5 1 7 2l2 1c1 0 3 0 4 1v1c-3-1-5-1-7-2 1 2 2 1 2 2s0 1-1 2z" class="R"></path><path d="M434 744c5 1 10 3 15 4 4 0 8 0 12 2l4 10h-1c-1-1-4-2-5-2h-1c-1-1-2-1-3-1l-12-3c1-1 1-1 1-2s-1 0-2-2c2 1 4 1 7 2v-1c-1-1-3-1-4-1l-2-1c-2-1-5-2-7-2l-4-1h0l1-1h1l-1-1h1z" class="b"></path><path d="M510 289h-1c-1-2-1-4-2-6-2-6-6-12-12-15-2-2-4-2-7-3 1-2 3-3 5-3 5-1 9 0 13 2-1-8-4-14-7-21h0c2-1 4-1 6 0 1 0 4 1 5 1l3-24 11 27c3-2 4-3 8-3h1c0 3-3 7-4 11-1 3-2 6-2 9 6-2 12-3 18-3 2 0 5 0 8 1v1c-2 1-6 2-8 3-12 6-16 16-18 28 0 4-1 9 1 12l1 1 3-3c3-5 6-18 10-20 2 1 4 2 5 4 4-1 8-2 12-2l1 1c0 2 0 2-1 3-2 4-6 7-9 10-4 6-8 11-11 17-3 3-6 7-8 12v-1l-4 11c-1 2-1 10-3 11h-1 0-1c0-1 0-2-1-4l-1-2 1-10-1-11h0v-11c-2 2-1 7-1 10h0c0-3 0-5-1-8v-1h0c1-1 1 0 1-1 0-2 0-3-2-4 0-3 1-6 1-9h-1l-1-1c-1 1 1 6-1 7h-1c0-2 0-2 1-3l-1-1c-1 0-1 0-1-1-1-2-1-3-2-4l-1-2v-2-3z" class="I"></path><path d="M521 285c0 3 0 6 1 9h0l1 1c1-1 1 0 1-1v-1c-1-1 0-1 0-2 2 1 1 2 2 4v7c-1-1 0-3-1-4l-1 1c0 2 0 3-2 4h-1v-9h0c-1-3 0-6 0-9z" class="E"></path><defs><linearGradient id="AE" x1="522.428" y1="330.229" x2="540.072" y2="318.771" xlink:href="#B"><stop offset="0" stop-color="#211d21"></stop><stop offset="1" stop-color="#3b3f3a"></stop></linearGradient></defs><path fill="url(#AE)" d="M534 314l9-13-8 18c-2 3-3 6-4 9l-4 11c-1 2-1 10-3 11h-1l3-14 1-6 3-9 4-7z"></path><path d="M510 289c0 1 1 2 1 3l1-1v-1-3c1-5 1-12 1-17-1-5-1-9-2-13v-1c2 3 2 8 2 12l1 10c1 3 0 7 1 10 0 1 0 1 2 2h0l1-17c1 8 0 18 0 26h-1l-1-1c-1 1 1 6-1 7h-1c0-2 0-2 1-3l-1-1c-1 0-1 0-1-1-1-2-1-3-2-4l-1-2v-2-3z" class="X"></path><path d="M518 273l-1-17c-1-3-1-6-1-9 4 12 4 25 5 38 0 3-1 6 0 9v13l-1 7v9h0v-11c-2 2-1 7-1 10h0c0-3 0-5-1-8v-1h0c1-1 1 0 1-1 0-2 0-3-2-4 0-3 1-6 1-9 0-8 1-18 0-26z" class="P"></path><path d="M521 294h0v9h1c2-1 2-2 2-4l1-1c1 1 0 3 1 4l-1 1c1 2 0 5 1 8v3c1 1 1 0 3 0v1l-1 1c-1 0 0 0-1 1h2c2-2 3-3 5-3l-4 7-3 9-1 6-3 14h0-1c0-1 0-2-1-4l-1-2 1-10-1-11v-9l1-7v-13z" class="N"></path><path d="M527 317h2c2-2 3-3 5-3l-4 7s0-1-1-1c0 1-1 1-2 2 0-2 0-2-1-2 1-2 1-1 1-2v-1z" class="E"></path><path d="M527 317c-1 0-2 1-2 1l-1-1c0-1-1-3 0-4 1-4 0-7 1-10 1 2 0 5 1 8v3c1 1 1 0 3 0v1l-1 1c-1 0 0 0-1 1z" class="G"></path><path d="M521 307h1v1-2h1c1 2 0 4 0 7v7l-3-6 1-7z" class="S"></path><path d="M520 314l3 6c-1 1-1 2 0 4v1l-1 1h1c1-1 1-1 1-2v-1l1-1c1 1 1 1 0 3s-2 3-2 5h0-1c0 2 0 5-1 7v-3l-1-11v-9z" class="T"></path><path d="M523 330h0c0-2 1-3 2-5l1 1 1 1h0v3l-1 6-3 14h0-1c0-1 0-2-1-4l-1-2 1-10v3c1-2 1-5 1-7h1z" class="K"></path><path d="M523 330h0c0-2 1-3 2-5l1 1 1 1h0v3l-1 6c-1-1-1-2-1-4h0s-1 1-1 2 0 2 1 3l-1 1-2-2c1-2 1-4 1-6z" class="S"></path><path d="M804 230c11 3 20 11 26 22 3 5 4 12 5 19-1 2-2 5-3 7-2 3-3 5-5 8 0 1-3 6-4 6-1 1-2 2-2 3l-2 1c-1 2-3 5-5 6-1-1 0-2 0-3l-2-1h0v-1c3-4 8-10 7-15l3-11c1-3 2-6 1-10 0-2-1-4-3-6-2 0-3-1-4 0-1 0-3 1-3 2-3 5 3 12 5 16l-3 1c0 2-1 3-1 5 0 3-2 7-1 10-3 6-12 16-18 19h0l-3 1h-2l2-3-4 3-2 1-1 1-4 1c-1 0-1 1-2 1-2-2-3-4-5-6h-1l-4-3-1-1-3-1-2-1h-5v-3-1c-2 0-5 0-7-1 1-3 2-6 4-9 2 0 3-1 5-3 1-1 1-1 2-1 2-1 5-5 7-7l2-2-1 1c-1 0-2 0-3 1s-3 0-4 0c2-3 5-5 8-6 0-1 2-2 3-2h0l1-1h0l3-3h-6-1l-1 1c-1 0-2 0-3-1 2-1 2-3 3-5h0l4-3c-2 1-4 3-6 3l-1-1h0c-1 0-1 0-2 1-1 0-1 0-2 1v-2h0c1-1 2-2 2-4 0-1 1-2 2-2 1-1 1-3 2-4s3-3 5-4l6-3c0-1 1-2 0-3-1 1-1 1-2 1-2 1-4 2-6 2-1 0-1 0-1 1h-1v-1-2l9-4c5-2 10-4 15-4 3-1 7 0 10-1z" class="I"></path><path d="M763 258h0c1-1 2-2 2-4 0-1 1-2 2-2 0 1 0 2 1 3h1v1l5-2v1h2 1c-1 1-2 1-3 1-2 1-4 3-6 3l-1-1h0c-1 0-1 0-2 1-1 0-1 0-2 1v-2z" class="C"></path><path d="M787 285c1 0 2-1 3-2v2l-1 1h1 2c2 0 2-1 4-2 0 0 0-1 1-1 1-1 3-3 5-3v1l2-1s0 1 1 2c-1 2-2 4-3 5h-1 0l-2 1v-2h0c-1 1-2 3-4 4 0-2 1-3 2-4h-1l-3 4c-1-1-1-1-1-2-1-2-1-1-3-1-1 1-1 0-1 1h-1c0-1 0-1 1-2l-1-1z" class="B"></path><path d="M772 264c5-3 16-6 23-4 2 1 4 3 5 5 1 1 1 3 0 4-1 2-3 3-4 3-6 2-8 0-13-1v-1h4 2c2 1 4 1 6 0 1-1 2-2 2-4 0-1-1-2-2-3-3-2-13 0-17 1h-6z" class="R"></path><path d="M777 278c2-1 2-1 3 0l1 2c-1 0-1 1-2 2h1 2c0 2-4 2-3 4l2-1h0l-2 2 1 1 2-2 5-1 1 1c-1 1-1 1-1 2h1c0-1 0 0 1-1 2 0 2-1 3 1 0 1 0 1 1 2h0c-2 2-3 3-5 3-1 0-1 1-2 1h0c-1 1-2 1-2 1-2 0-2 0-3 1v-1-1l-1 1-1-1c-3 2-3 2-5 1l2-2-1-2h-1l-1-1v-1c1 0 1-1 1-2-1 1-1 1-2 1h-1l6-6h0v-4z" class="O"></path><path d="M787 285l1 1c-1 1-1 1-1 2h1c0-1 0 0 1-1 2 0 2-1 3 1 0 1 0 1 1 2h0c-2 2-3 3-5 3-1 0-1 1-2 1h0l-1-1h1c0-1 0-2-1-2h-2c-1 0-2 2-3 2h-1-1l-1-1c2-1 4-3 4-5l1-1 5-1z" class="D"></path><path d="M778 264c4-1 14-3 17-1 1 1 2 2 2 3 0 2-1 3-2 4-2 1-4 1-6 0h-2-4v1l-2 1-3 2c-2 1-3 1-4 3 0 0-1 1-2 1l-2 1v-1c0-1-1-1-1-2l2-2-1 1c-1 0-2 0-3 1s-3 0-4 0c2-3 5-5 8-6 0-1 2-2 3-2h0l1-1h0l3-3z" class="Q"></path><path d="M763 276c2-3 5-5 8-6h0c-1 1-1 2-2 3v1l1-1h1c1 0 4-2 5-1-1 1-2 1-4 2h-1 0l-1 1c-1 0-2 0-3 1s-3 0-4 0z" class="F"></path><path d="M776 272l2-2h1 1 3v1l-2 1-3 2c-2 1-3 1-4 3 0 0-1 1-2 1l-2 1v-1c0-1-1-1-1-2l2-2h0 1c2-1 3-1 4-2z" class="B"></path><path d="M769 276c0 1 1 1 1 2v1l2-1-5 7 3-3c2-1 5-3 7-4v4h0l-6 6h1c1 0 1 0 2-1 0 1 0 2-1 2v1l1 1h1l1 2-2 2-1 1c-2 1-3 2-4 3l1 1c-1 1-1 2-2 3l-3-1-2-1h-5v-3-1c-2 0-5 0-7-1 1-3 2-6 4-9 2 0 3-1 5-3 1-1 1-1 2-1 2-1 5-5 7-7z" class="Z"></path><path d="M769 276c0 1 1 1 1 2v1c-1 2-4 4-5 6l-1 1c-1-1-1 0-1-1h-1l-2 2 2-4c2-1 5-5 7-7z" class="D"></path><path d="M758 298c1-2 3-3 4-4h1c1-1 1-2 2-2v5l-1 1h1c0 1 0 2 1 3l-1 1-2-1h-5v-3z" class="R"></path><path d="M755 287c2 0 3-1 5-3 1-1 1-1 2-1l-2 4c-2 1-2 2-3 3l1 1 2-3c0 2-1 2-2 4-1 1-1 1-2 3l1 1c1-1 2-2 2-3h1c0 1-1 2-1 3-1 1-1 0-1 1-2 0-5 0-7-1 1-3 2-6 4-9z" class="W"></path><path d="M771 288h1c1 0 1 0 2-1 0 1 0 2-1 2v1l1 1h1l1 2-2 2-1 1c-2 1-3 2-4 3l1 1c-1 1-1 2-2 3l-3-1 1-1c-1-1-1-2-1-3h-1l1-1c2-3 3-7 6-9z" class="S"></path><path d="M773 290l1 1h1l1 2-2 2-1 1-3-1h-1l2-2c1 0 1-1 2-2v-1z" class="E"></path><path d="M814 279c0 3-2 7-1 10-3 6-12 16-18 19h0l-3 1h-2l2-3-4 3-2 1-1 1-4 1c-1 0-1 1-2 1-2-2-3-4-5-6h-1l-4-3-1-1c1-1 1-2 2-3l-1-1c1-1 2-2 4-3l1-1c2 1 2 1 5-1l1 1 1-1v1 1c1-1 1-1 3-1 0 0 1 0 2-1h0c1 0 1-1 2-1 2 0 3-1 5-3h0l3-4h1c-1 1-2 2-2 4 2-1 3-3 4-4h0v2l2-1h0 1c1-1 2-3 3-5 0 0 1-1 2-1v1c-1 0-2 1-2 2-2 2-3 6-4 9 0 1-1 3-1 5h1l2-1c1-2 4-4 5-6h1c2-4 4-8 5-12z" class="J"></path><path d="M793 290l-2 3h1l3-2h0v1h1l2-1h1l-1 2h2c-1 3-2 4-2 7-1 0-2 1-2 2 0-2 0-3 1-4v-1l-1-2h-3c-2 0-3 1-4 0s-2-1-3-1c1 0 1-1 2-1 2 0 3-1 5-3z" class="N"></path><path d="M786 294h0c1 0 2 0 3 1s2 0 4 0h3l1 2v1c-1 1-1 2-1 4l-1 1h1c-1 1-3 3-4 3h-2l1-1h1c1-1 1-2 1-4-2-1-3-1-5-2h-1c-3 0-4-2-7-2l1-1c1-1 1-1 3-1 0 0 1 0 2-1z" class="S"></path><path d="M774 295c2 1 2 1 5-1l1 1 1-1v1 1l-1 1c3 0 4 2 7 2h1c2 1 3 1 5 2 0 2 0 3-1 4h-1l-1 1h2l-4 3-2 1-1 1-4 1c-1 0-1 1-2 1-2-2-3-4-5-6h-1l-4-3-1-1c1-1 1-2 2-3l-1-1c1-1 2-2 4-3l1-1z" class="K"></path><path d="M774 295c2 1 2 1 5-1l1 1 1-1v1 1l-1 1-4 3h-1v-1c-1 1-2 0-3 1l-1-1 1-1-2 2-1-1c1-1 2-2 4-3l1-1z" class="X"></path><path d="M770 300c0 1 0 2 1 3 1 0 2-1 3-1 3 0 7 1 9 2 3 0 4-1 7 1-1 2-4 2-4 4h2l-2 1-1 1-4 1c-1 0-1 1-2 1-2-2-3-4-5-6h-1l-4-3-1-1c1-1 1-2 2-3z" class="Y"></path><path d="M769 304l1-1c1 1 2 1 3 1s2 0 2-1c1 1 1 2 2 3h1 1l1 1 1-1c1 1 3 3 4 5l-4 1c-1 0-1 1-2 1-2-2-3-4-5-6h-1l-4-3z" class="W"></path><path d="M624 723h3c2-1 4-3 5-5 6-4 11-10 16-16 10-12 19-24 26-39 4-8 6-16 8-24 1-3 2-9 5-10 0 1 1 3 2 4s2 1 3 0v1c1 1 0 6 0 7l1 1c6-4 10-10 13-16 6-13 10-29 22-38l1 8c2-2 4-3 5-5 6-9 6-23 4-34 5 7 11 15 12 25v11l5-3c-1 13-6 23-17 30-5 3-9 5-13 9h-1c-2 2-4 4-7 4l-2 2-1-1c-1 1-1 2-2 4h1l-3 3c-1 1-1 1-3 1v-1l-6 7-1 1-1-1-3 3-1-1c-4 4-7 9-10 13-9 13-18 27-29 39l-14 14c-5 6-13 11-16 18 6-4 12-9 18-15 13-12 25-27 38-40l20-20c1 2-1 2-1 4l11-7v1l-5 5c-2 1-3 2-4 3l-5 5v1l1 1-7 6 1 1c0-1 1-1 2-2l1 1c-2 1-2 2-3 3h0l2-1c1 1 1 0 0 1l-1 2h0l-42 39c-2 0-3 1-4 2-2 2-6 3-7 6l-2 1c-3 2-6 3-9 4l2 1c-14 4-26 11-40 16-8 3-16 5-24 8h-1c1-1 2-1 2-1l6-3c-3 1-6 2-10 2l1-1h0c4-1 9-3 13-4v-1h-1-2 1c-2-1-4-1-6-1v-1h1l2-1c1-1 1-1 2-1s1 0 2-1c1 0 1 0 2-1h2c0-1 0 0 1 0 5-2 9-6 14-7 4-2 8-4 11-7 2 0 4-2 6-3l3-3 1-1c1-1 2-2 2-4l1-1c1 1 1 2 3 3z" class="I"></path><path d="M699 643l1 1c1 0 0 1 1 0l1 1-1 3-1 1-1-1-3 3-1-1 4-7z" class="R"></path><path d="M720 624v1l4-1v1h-1 2c-1 1-3 2-5 4h1 3c-2 2-4 4-7 4l-2 2-1-1c1-1 3-2 3-4-1 0-1 0-2-1l1-2h1l3-3z" class="F"></path><path d="M739 587l1-3 3-11c2 5 3 9 3 14 1 2 1 5 0 7v2l-1 2c0-2-1-5 0-7v-3h-3c-1 1 0 0-2 1l-1-2z" class="O"></path><path d="M583 746h1c2 0 5-2 8-2 1 0 2-1 3-1l1-1 11-7v1c-9 6-18 13-28 17v-1h-1-2 1c-2-1-4-1-6-1v-1h1l2-1c1-1 1-1 2-1s1 0 2-1c1 0 1 0 2-1h2c0-1 0 0 1 0z" class="P"></path><path d="M739 587l1 2c2-1 1 0 2-1h3v3c-1 2 0 5 0 7 0 1-1 3-1 4h-1v-4h-1c-2 1-3 3-4 4s-3 4-4 5c0 1-1 1-2 1 0-2 1-5 2-6 1-5 4-10 5-15h0z" class="M"></path><path d="M738 602l-1-1c0-2 0-3 1-5l1-1c1-2 2-3 4-5l2 1c-1 2 0 5 0 7 0 1-1 3-1 4h-1v-4h-1c-2 1-3 3-4 4z" class="R"></path><path d="M699 643l3-3v1c7-8 13-18 19-27h1l-3 4c1 1 1 1 1 2v1 1l3-3v1l-3 4-3 3h-1l-1 2c1 1 1 1 2 1 0 2-2 3-3 4s-1 2-2 4h1l-3 3c-1 1-1 1-3 1v-1l-6 7 1-3-1-1c-1 1 0 0-1 0l-1-1z" class="L"></path><path d="M719 618c1 1 1 1 1 2v1 1l3-3v1l-3 4-3 3h-1 0c-2 1-4 4-5 6 1-5 5-10 8-15z" class="J"></path><path d="M715 629c1 1 1 1 2 1 0 2-2 3-3 4s-1 2-2 4h1l-3 3c-1 1-1 1-3 1v-1c2-4 5-8 8-12z" class="H"></path><path d="M178 278a49.23 49.23 0 0 1 6-29c6-11 16-17 29-21 1 2 4 2 6 2s4 0 6 1c4 2 8 4 11 6 1 0 3 2 3 2l9 6c2 2 4 3 5 4 0 4 2 8 2 11v1l-1 1c-1-1-1-1-2-1-2-2-5-3-7-5v1h0c2 1 3 2 5 4l-2 2h0c-1 0-1 0-2 1l-1-1h-1l7 7c-2 1-3 0-5-1l3 3v1h-3l-1-1h-2s-2-1-3-2c-5-2-9-2-14-2l2 2c5 1 9 2 14 5l4 2 1 1 2 2v1l-2 3 1 1v1l-1 1-1 1c-1 1-1 1-2 1l1 2h0l-3-1 2 2h-1-1c0 1 1 2 2 2l1 1h1c1 1 1 2 1 3h0l-1 1h0l1 2c-2 1-3 1-5 2h0l-8 7-2 1-2 2h0l-2 3-2-1-12-6s-1 0-2 1h0l-5-2-1 1 1 1c-1 0-2 0-2 1-1-1-2-2-2-3 0-2-4-4-5-5-1-2-1-2-3-3 1 3 2 5 5 7l1 1-3 3-3-2c-10-9-15-19-17-31z" class="I"></path><path d="M216 285l1-1v-2h3s0 1 1 1l1 1v2h0l-2-1-2 2h0l-2-2z" class="C"></path><path d="M228 285l-1-1-2-2 1-1c1 0 3-1 4-2s1-1 3-1h0c2 1 2 1 3 2l-1 1h-1v2s-1-1-2-1v1l-4 2z" class="B"></path><path d="M234 238c2 1 3 1 4 2l3 3v1l-2-1v1h-1l-1-1h-2 0l-8-3h-3l1-1c1 0 2 1 3 1s1 0 2-1h2c1 0 1 0 2-1h0z" class="F"></path><path d="M207 289h0c1-3 0-3 0-6h1 0c0 2 1 2 2 3l2-2-1-2h1l2 2h0v-2h0 0c1 0 1 1 2 2v1l2 2h0c-2 1-2 2-4 2l-2-1h-1c-1 1-2 3-4 4l-2 1v-1c1-1 1-1 2-3z" class="C"></path><path d="M216 284v1l2 2h0c-2 1-2 2-4 2l-2-1c1 0 2-1 2-1l1-1v-1l1-1z" class="D"></path><path d="M228 270h-2c-1 0-1 1-2 1-2 2-7 2-10 1-1 0-3-1-4-3-1-1-1-3 0-5s3-4 5-4c5-2 13 0 17 2-5 0-15-2-19 2 0 0-1 1-1 2s0 2 2 3c0 1 2 1 3 1s2 0 3-1c2 0 4-1 6-1l2 2z" class="P"></path><path d="M188 261c0-3 1-5 3-6s3-1 4-1c1 1 3 2 3 3 1 1 0 3 0 4-1 2-4 7-5 8-1 0-2-1-2-1-1-2-1-4-2-6l-1-1z" class="R"></path><path d="M189 262c1-2 2-3 3-5 1-1 1-1 3 0 1 1 1 1 1 3-1 2-3 5-4 7l-1 1c-1-2-1-4-2-6z" class="D"></path><path d="M230 253h-1v-1l3-1c4-1 9 3 12 4h0l1 1v1h0c2 1 3 2 5 4l-2 2h0c-1 0-1 0-2 1l-1-1h-1l-2-1-3-3c-3-1-6-4-9-6z" class="E"></path><path d="M230 253c2-1 3-1 5-1 3 2 6 2 8 6l-1 2c-1 0-2-1-3-1-3-1-6-4-9-6z" class="C"></path><path d="M188 290c1 1 2 2 3 2 2 1 4 2 5 3l5 5 1-1c-2-2-4-3-4-6 4 5 10 12 16 16 0 0-1 0-2 1h0l-5-2-1 1 1 1c-1 0-2 0-2 1-1-1-2-2-2-3 0-2-4-4-5-5-1-2-1-2-3-3-1-1-1-2-2-4s-2-1-3-3c-1-1-1-2-2-3z" class="E"></path><path d="M193 296c4 5 9 9 14 12h0l-1 1 1 1c-1 0-2 0-2 1-1-1-2-2-2-3 0-2-4-4-5-5-1-2-1-2-3-3-1-1-1-2-2-4z" class="Y"></path><defs><linearGradient id="AF" x1="195.375" y1="305.197" x2="183.31" y2="290.249" xlink:href="#B"><stop offset="0" stop-color="#959393"></stop><stop offset="1" stop-color="#c7c5c4"></stop></linearGradient></defs><path fill="url(#AF)" d="M178 278h1v1c0 1 0 3 1 4l1 4c0 2 1 3 2 4l1-1h2v-1h0l-1-1c0-1 0-1-1-2s0 0 0-1l-1-2v-2c-1-2-2-4-1-6v-1c1 1 1 1 1 2s1 1 1 2v3l1 1v2c1 2 2 3 2 4 1 0 1 1 1 2h0c1 1 1 2 2 3 1 2 2 1 3 3s1 3 2 4c1 3 2 5 5 7l1 1-3 3-3-2c-10-9-15-19-17-31z"></path><path d="M195 309l2-3c1 1 1 1 2 1h1l1 1-3 3-3-2z" class="K"></path><defs><linearGradient id="AG" x1="250.758" y1="252.635" x2="239.823" y2="243.06" xlink:href="#B"><stop offset="0" stop-color="#999797"></stop><stop offset="1" stop-color="#b4b3b2"></stop></linearGradient></defs><path fill="url(#AG)" d="M219 230c2 0 4 0 6 1 4 2 8 4 11 6 1 0 3 2 3 2l9 6c2 2 4 3 5 4 0 4 2 8 2 11v1l-1 1c-1-1-1-1-2-1-2-2-5-3-7-5l-1-1 2-3-5-4c-2-2-4-3-6-5h2l1 1h1v-1l2 1v-1l-3-3c-1-1-2-1-4-2-4-2-9-5-13-6-1 0-2-1-2-2z"></path><path d="M248 245c2 2 4 3 5 4 0 4 2 8 2 11v1l-1 1c-1-1-1-1-2-1-2-2-5-3-7-5l-1-1 2-3-5-4v-1c4 3 7 7 11 9l-2-5c-1-2-1-3-2-4l-2-2 1 1 1-1z" class="U"></path><path d="M246 252l9 9-1 1c-1-1-1-1-2-1-2-2-5-3-7-5l-1-1 2-3z" class="J"></path><path d="M226 268c-2 0-4 1-6 1-1 1-2 1-3 1s-3 0-3-1c-2-1-2-2-2-3s1-2 1-2c4-4 14-2 19-2 1 0 2 1 2 1h1c1 0 1 1 2 1l5 2h1c0-1-1-2-2-3l1-1 2 1 7 7c-2 1-3 0-5-1l3 3v1h-3l-1-1h-2s-2-1-3-2c-5-2-9-2-14-2z" class="Q"></path><path d="M242 262l2 1 7 7c-2 1-3 0-5-1l3 3v1h-3l-1-1h-2s-2-1-3-2c2 0 3 0 5 1 1 0 2 1 3 1h0c-2-1-3-2-4-3-2-2-4-3-6-4l-1-1 5 2h1c0-1-1-2-2-3l1-1z" class="J"></path><defs><linearGradient id="AH" x1="212.063" y1="289.258" x2="201.137" y2="296.363" xlink:href="#B"><stop offset="0" stop-color="#121112"></stop><stop offset="1" stop-color="#323131"></stop></linearGradient></defs><path fill="url(#AH)" d="M188 261l1 1c1 2 1 4 2 6 0 0 1 1 2 1-1 2-1 3 0 6v1c0 4 2 8 4 11h1 1c1 2 1 3 3 4l1-1v1l4-2c-1 2-1 2-2 3v1l2-1 1 2c0 1 0 2 1 3h0l1 1 4 4c0 1 1 2 1 2 0 1 2 1 2 1h1 1c3 3 4 6 9 6h3 1l-2 2h0l-2 3-2-1-12-6c-6-4-12-11-16-16-4-6-9-14-8-21 0-4-2-7-2-11z"></path><defs><linearGradient id="AI" x1="212.645" y1="299.764" x2="201.063" y2="293.83" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#c4c3c1"></stop></linearGradient></defs><path fill="url(#AI)" d="M197 287h1 1c1 2 1 3 3 4l1-1v1l4-2c-1 2-1 2-2 3v1l2-1 1 2c0 1 0 2 1 3h0l1 1 4 4c0 1 1 2 1 2 0 1 2 1 2 1h1 1c3 3 4 6 9 6h3 1l-2 2h0c-2-1-6-2-8-3-9-4-20-13-25-23z"></path><path d="M242 275l4 2 1 1 2 2v1l-2 3 1 1v1l-1 1-1 1c-1 1-1 1-2 1l1 2h0l-3-1 2 2h-1-1c0 1 1 2 2 2l1 1h1c1 1 1 2 1 3h0l-1 1h0l1 2c-2 1-3 1-5 2h0l-8 7-2 1h-1-3c-5 0-6-3-9-6h-1-1s-2 0-2-1c0 0-1-1-1-2l-4-4-1-1h0c-1-1-1-2-1-3l-1-2c2-1 3-3 4-4h1l2 1c2 0 2-1 4-2h0 0l2-2 2 1h0v-2l2 3 4-2 4-2v-1c1 0 2 1 2 1v-2h1l1-1c-1-1-1-1-3-2h0c2 0 2 0 4 1 0-1 0-1 1-2 2 0 3-1 4-2z" class="H"></path><path d="M247 278l2 2v1l-2 3 1 1v1l-1 1-1 1c-1 1-1 1-2 1l1 2h0l-3-1h-1l-1-2h0 2s-1-1-1-2c1 0 1-1 2-1l1-1h0l-1-1 1-1h1v-2c0-1 1-1 2-2z" class="V"></path><path d="M242 275l4 2c-1 1-2 1-2 2h-2l-1 1v1h-1v1h-2c0 1 0 1 1 2h-1c0 1 0 1 1 2h-1l-1-2-1 1c1 1 1 2 0 3 0 1-1 1-1 2h-1c-1 1-2 1-2 1l-1 1c0 1-1 1-2 1h0-3-2c-1 1-4 1-5 1h-1c-1 0-1 0-1 1-2 2-4 2-6 1 1-1 2-1 3-1h1 1v-1-2l2 1c1-1 2-1 3-2h2 1c1 0 1 0 2-1l1-1 2 1h1l3-3 1-1c0-2-1-2-2-3v-1c1 0 2 1 2 1v-2h1l1-1c-1-1-1-1-3-2h0c2 0 2 0 4 1 0-1 0-1 1-2 2 0 3-1 4-2z" class="X"></path><path d="M232 283c1 1 2 1 2 3l-1 1-3 3h-1l-2-1-1 1c-1 1-1 1-2 1h-1-2c-1 1-2 1-3 2l-2-1v2 1h-1-1c-1 0-2 0-3 1l-1-1-1 2h0c-1-1-1-2-1-3l-1-2c2-1 3-3 4-4h1l2 1c2 0 2-1 4-2h0 0l2-2 2 1h0v-2l2 3 4-2 4-2z" class="G"></path><path d="M218 287l2-2 2 1h0c1 0 2 1 3 2l-2 2c-3-1-4-2-5-3z" class="D"></path><path d="M232 283c1 1 2 1 2 3l-1 1c-2 1-3 1-4 2h0c-2-1-3-1-5-2l4-2 4-2zm-14 4l1 2c0 1 0 1-1 1h-1s-1 1-2 1l-1-1h-1c-1 2-3 4-5 4l-1-2c2-1 3-3 4-4h1l2 1c2 0 2-1 4-2z" class="J"></path><path d="M219 305c-1-1-2-2-3-4h1c0-1 0-1 1-1 1-1 1 0 2-1h1c1-1 0-1 1-1 1-1 5-1 6-1h1c3 0 4-1 7-3 1 0 3-1 4-2v-1l1-1h1l2 2h-1-1c0 1 1 2 2 2l1 1h1c1 1 1 2 1 3h0l-1 1h0l1 2c-2 1-3 1-5 2h0l-8 7-2 1h-1-3c-5 0-6-3-9-6z" class="Z"></path><path d="M245 295h1c1 1 1 2 1 3h0l-1 1h0l1 2c-2 1-3 1-5 2h0l-8 7-2 1h-1c0-1 0 0-1-1-2-1-3-2-5-4 0-2 2-1 3-3 1-1 1-2 3-2s4-2 6 0h2c0-1-1-1-1-2h1c1 0 1 1 2 1l1-1-1-1h2 1v-1h2 0l-1-2z" class="M"></path><path d="M234 310l-2-1-1-1v-3c2-2 4-1 6-2 1-1 2-1 4-1l1 1-8 7z" class="L"></path><path d="M383 537l2 6 75 205 1 2c-4-2-8-2-12-2-5-1-10-3-15-4-1-1-3-1-4-2l3-2-7-2-18-12-6-9-4 6c-2 0-3-1-4-2-5-5-11-10-16-16-2-3-5-6-7-10-1-2-2-5-4-7-2-1-5-5-7-7 0-1-1-2-2-3l-1-1v-1-1c-1-1-1-1-1-2h-1l-1-2c0-1-1-1-1-2-1-1-1-2-2-2 0-1 0-2-1-3v-1-2h-1c-1-4-2-8-3-11 0-2 1-4 0-5-1-2 0-3 0-5 1-2 1-5 0-7v-7l-1-3c1-8 5-15 11-21h0c-1-1-2-1-3-1h-1 0l2-3 1-1 2-1h0c1-1 2-1 3-2 1 0 2-1 2-1l1-1c-1-1-3 1-5 0-1 0-2 1-3 1h-1l-1 1h0c3-4 8-7 13-9 1-1 3-2 4-2 1-1 1-3 2-4 0 0 1-1 2-1-2 0-5 0-7 1l-1-1h0 2c1 0 2-1 3-1h0 1c1-1 3 0 4-1v-1h-3-1l-2-2-6-3c-4-4-5-9-5-14v-2l1-1h1-1c-1-1-2-1-2-1-1 0-2-1-2-1-2 0-2 0-3-1-2 0-3-1-4-2h0l13 4c6 2 13 2 19-1l-10 1c2-1 5-1 8-2v-1h-3c-4 1-7 0-11 0l7-3v-5l8-3c1 0 2 0 3-1z" class="b"></path><path d="M394 594c2 3 4 5 4 8-2 0-3 0-5-1h0 0l-1-2c0-2 1-3 2-5z" class="P"></path><path d="M358 675h2l7 13c-2-1-5-5-7-7 0-1-1-2-2-3l-1-1v-1l1-1z" class="T"></path><path d="M383 537l2 6c-2 1-3 1-5 2-2 0-5 1-8 1v-5l8-3c1 0 2 0 3-1z" class="L"></path><path d="M383 537l2 6c-2 1-3 1-5 2l1-2c0-1-1-1-2-2 0-1 1-1 1-3 1 0 2 0 3-1z" class="P"></path><defs><linearGradient id="AJ" x1="448.939" y1="746.236" x2="434.962" y2="742.65" xlink:href="#B"><stop offset="0" stop-color="#595757"></stop><stop offset="1" stop-color="#7f7f7f"></stop></linearGradient></defs><path fill="url(#AJ)" d="M433 740l27 8 1 2c-4-2-8-2-12-2-5-1-10-3-15-4-1-1-3-1-4-2l3-2z"></path><defs><linearGradient id="AK" x1="372.223" y1="569.183" x2="357.905" y2="560.424" xlink:href="#B"><stop offset="0" stop-color="#afadad"></stop><stop offset="1" stop-color="#d6d5d3"></stop></linearGradient></defs><path fill="url(#AK)" d="M359 556l1-2c1 1 1 1 1 2 0 5 2 9 6 13 2 1 4 2 7 2h1v-1c1-1 0-1 1-1 2 2 2 3 2 5-1 1-6 0-8-1l-6-3c-4-4-5-9-5-14z"></path><path d="M351 637c0 5 0 10 1 15 2 8 5 16 8 23h-2c-2-3-3-6-4-9-2-3-3-5-3-8s-1-6-1-10c0-1 0-1-1-2v-5l1 1 1-5z" class="K"></path><path d="M388 589c2 2 4 3 6 5-1 2-2 3-2 5l1 2h0-2c0 2 2 4 3 6 1 3 1 6 2 9-3-5-6-9-10-13 1-1 2-2 2-3v-5l1-1c0-2 0-3-1-5z" class="M"></path><path d="M416 704h3c0 1-1 2-1 2-2 2-4 4-4 7 0 9 5 15 11 20v1h0c-3-1-8-7-10-9-3-5-5-11-3-16 0-2 2-4 4-5z" class="D"></path><path d="M346 650c0-2 1-4 0-5-1-2 0-3 0-5l1 1v4h1v-1-1-1 1l1 3c1 1 1 1 1 2 0 4 1 7 1 10s1 5 3 8c1 3 2 6 4 9l-1 1v-1c-1-1-1-1-1-2h-1l-1-2c0-1-1-1-1-2-1-1-1-2-2-2 0-1 0-2-1-3v-1-2h-1c-1-4-2-8-3-11z" class="H"></path><defs><linearGradient id="AL" x1="354.25" y1="638.03" x2="346.134" y2="622.244" xlink:href="#B"><stop offset="0" stop-color="#adacab"></stop><stop offset="1" stop-color="#dad7d6"></stop></linearGradient></defs><path fill="url(#AL)" d="M346 626c1-4 3-6 6-7l3-2-3 8-1 6v6l-1 5-1-1v5l-1-3v-1 1 1 1h-1v-4l-1-1c1-2 1-5 0-7v-7z"></path><path d="M349 631c0-2 0-4 1-6h2l-1 6h-2z" class="E"></path><path d="M349 631h2v6l-1 5-1-1v5l-1-3c1-4 1-8 1-12z" class="N"></path><path d="M380 651c1-1 3-1 4-1l3 3v2c-1 2-2 3-4 4l-2 1c-3 2-4 5-4 8h-1c-1 4-1 8-1 12 0 1 1 2 1 3h-1c-2-6-2-14-1-20 1-5 2-9 6-12z" class="I"></path><defs><linearGradient id="AM" x1="378.88" y1="590.434" x2="365.44" y2="596.3" xlink:href="#B"><stop offset="0" stop-color="#8b8a89"></stop><stop offset="1" stop-color="#b8b7b5"></stop></linearGradient></defs><path fill="url(#AM)" d="M354 593c3-2 7-5 10-6v2l-2 1h1 9c1 0 3 0 4 1 2 0 4 0 6 3 0 2 1 4 2 6l-6-3c-9-3-16-2-24 1l1-1 2-1h0c1-1 2-1 3-2 1 0 2-1 2-1l1-1c-1-1-3 1-5 0-1 0-2 1-3 1h-1z"></path><path d="M364 587c6-3 11-2 17-1l4 1 3 2c1 2 1 3 1 5l-1 1v5c0 1-1 2-2 3l-2-3c-1-2-2-4-2-6-2-3-4-3-6-3-1-1-3-1-4-1h-9-1l2-1v-2z" class="Z"></path><defs><linearGradient id="AN" x1="381.627" y1="585.697" x2="368.713" y2="590.36" xlink:href="#B"><stop offset="0" stop-color="#7e7c7c"></stop><stop offset="1" stop-color="#969594"></stop></linearGradient></defs><path fill="url(#AN)" d="M364 587c6-3 11-2 17-1l4 1c0 1-1 2-1 3l1 1c-1 0-2-1-4 0 0 0-1-1-2-1h0c-2-1-4-1-6-1-3-1-6-1-9 0v-2z"></path><path d="M381 586l4 1c0 1-1 2-1 3-1-1-2-1-3-2v-2z" class="T"></path><path d="M661 152h149c0 2-1 3-1 5 0 1-1 1-1 2l-1 4-1 14h-4c-26 1-49 12-66 31-4 5-8 11-11 17l-1 1c-5 12-9 25-13 37l-11 36-44 140-37 116-42 132-57 178h-1c0-1-1-1-2-2l-7-9c2-1 3-2 4-4 3-5 4-12 6-18l11-40h-3c1-1 1-1 2-1h1c2-2 3-4 4-7l14-43 15-47 5-14 28-89 6-17 9-31-1-2h0 1 1c0-1 1-2 1-3 3-6 4-14 6-20l33-104 4-11c0-2 1-4 1-6h-2l1-1c1-1 2-1 3-2 3-8 6-18 8-27l9-28a104.13 104.13 0 0 0 5-12c0-2 0-2-1-3v-1l1 1c0-2 0-2-1-3s-1-3 0-5h0l-1-1v1c-1 1 0 0-1 2v1h-1l-1-1-4-3 5-17v-1c2-9 5-18 8-27l9-27c5-17 11-34 22-48 4-5 9-10 14-13 4-3 7-5 10-7-9-1-19-1-28-1h-51-1l-1-22h1z" class="Q"></path><path d="M681 307h6l1 1-1 2h-1c-2 0-4 0-5-1v-2z" class="T"></path><path d="M802 177c1-4 2-7 3-10s2-6 3-8l-1 4-1 14h-4z" class="X"></path><path d="M660 152h1c1 2 1 5 1 8v14h-1l-1-22z" class="C"></path><path d="M220 152h207 20l-11 7 10 16v1c-4 1-8 1-12 1-14 3-27 11-35 23-3 4-4 8-6 12-3 14-3 26 0 40 3 18 10 34 16 51l32 88 47 131 18 52h0c2 6 5 13 7 19l15 42c3-6 4-13 6-19l12-37 30-96 43-140 15-51c6-18 13-37 13-56 1-16-2-31-13-43-10-11-24-16-39-16 0-1-3 0-4-1v-24h5 13 51l1 22h1 51c9 0 19 0 28 1-3 2-6 4-10 7-5 3-10 8-14 13-11 14-17 31-22 48l-9 27c-3 9-6 18-8 27v1l-5 17 4 3 1 1h1v-1c1-2 0-1 1-2v-1l1 1h0c-1 2-1 4 0 5s1 1 1 3l-1-1v1c1 1 1 1 1 3a104.13 104.13 0 0 1-5 12l-9 28c-2 9-5 19-8 27-1 1-2 1-3 2l-1 1h2c0 2-1 4-1 6l-4 11-33 104c-2 6-3 14-6 20 0 1-1 2-1 3h-1-1 0l1 2-9 31-6 17-28 89-5 14-15 47-14 43c-1 3-2 5-4 7h-1c-1 0-1 0-2 1h3l-11 40c-2 6-3 13-6 18-1 2-2 3-4 4l-1-1c-3 1-6 3-9 5-2-9-6-17-9-25l-14-40-2-7c-1 0-8-22-10-26l-4-10-1-2-75-205-2-6-11-30-23-64-52-142-17-49c-5-14-9-28-17-39-14-25-42-35-69-36h-13-6l-1-1h2c0-1 0-1 1-2s2-3 4-4c4-5 6-7 13-9 1-1 3-1 4-1 8 2 15 3 22 4 1-1 0-1 0-3v-1c-1-1 0-2-1-4v-4h1z" class="I"></path><path d="M641 292s0 1 1 2l-1 5v1h-1v-3h-1l2-5z" class="D"></path><path d="M334 224c1 1 2 1 2 4l-1 1c-1-1-2-1-2-1-1-1-1-1-1-2-1 0-1 0-1-1 2 0 2-1 3-1z" class="H"></path><path d="M333 228c-3-1-5 0-7 0 1-2 2-4 4-5s2 0 4 1c-1 0-1 1-3 1 0 1 0 1 1 1 0 1 0 1 1 2z" class="X"></path><path d="M393 287h2c0 1 0 2 1 3-2 1-4 2-6 4h-1v-1c0-3 2-4 4-6z" class="H"></path><path d="M376 197h0c0 4-3 8-2 11l1-1h-1c1-2 2-4 2-7l3-3c-3 9-6 17-7 27-2-8 0-20 4-27z" class="a"></path><path d="M476 555c1-2 2-3 3-4 2 4 4 9 5 14h-3l-1-1h0c0-2-1-3-1-4l-3-5z" class="X"></path><path d="M300 190l4-2 1 1h0 0 2l1-1c4 2 8 3 12 3 6 1 12 2 19 2v1c-1 1-1 2-1 3 2 1 4 1 6 2 3 2 5 4 7 6h-1c-2 0-4-3-6-5-10-5-22-8-33-10h-1-6-2-2zm40 158v-1c2 0 3-1 3-3h0l3-9c0-1 0-1 1-2v1h0v2 1h0c-1 1-1 1-1 2s-1 2-1 3 0 1-1 2v2c-1 1-1 2-1 4l1 1v1c1 0 2 0 3 1h1l-2 1-1-1-1 1c2 1 2 1 4 1v1h-2c1 1 2 1 3 2-1 1-2 1-2 1v1c1 0 1 0 2-1v1c-1 1-1 1-2 1v1h3 0v1c-1 0-1 0-2 1h3v1h-2c1 1 0 1 1 1l-1 1c-1 0-2-1-3-1l-6-18z" class="F"></path><path d="M336 307c1-1 2-1 3-2l1 1c-1 1-1 1-2 1-1 1-3 2-4 4h1 1 1c2 1 2 1 4 1l1 1c0 1 0 1-1 1 0 1 1 1 1 2l-1 1h1l1 1c-1 0-1 1-2 1l-3-1c0 3 3 3 1 7v1c0 1-1 1-1 2-2 0-2-1-3-1 0-2 0-2-1-3v-1l1-1-1-1v-2h0l3-3-4-1v-1l2-1-2-1-1-1v-1c2-1 3-1 4-3h0z" class="C"></path><path d="M342 178l2-1c11 18 15 44 15 65 0 4 0 8-1 11v2c0-17-2-33-6-50l-3-12c-2-5-5-10-7-15z" class="P"></path><path d="M302 211c-1-5-2-10-2-15 1-1 1-4 2-5-1 9 2 19 4 28 5 15 12 28 21 41 6 8 13 17 20 25 9 11 18 22 20 37 1 1 1 3 1 5-1-6-2-11-5-16-8-19-24-34-37-50-6-8-10-17-15-26-4-8-6-15-9-24h0z" class="R"></path><path d="M288 191c1 1 1 1 2 1l1 1 7-3h2 2v1c-1 1-1 4-2 5 0 5 1 10 2 15h-1v-2c-1-1-1 0-2-1v1h-1c-2 0-3 0-4-2v1l1 1-1 1c-1-1-2-1-3-2l-1 1-4-7v-4l2-1c1-1 2-2 2-3h-1c-1 0-1-1-1-3z" class="B"></path><path d="M290 194h1v1h0c0 1 0 2-1 3h1 2v1l-2 1c-2 1-1 0-2 1l1 1c1 0 2 0 2 1 2 1 3 1 5 1-1 1 0 1-2 1l-3 1h0-2c1 0 3 1 4 1h0v1l1 1-1 1c-1-1-2-1-3-2l-1 1-4-7v-4l2-1c1-1 2-2 2-3z" class="F"></path><path d="M393 212h0c0-1 0-2 1-3h0v-1-1c1-1 2-3 2-4l1-1v-1c2-2 3-4 5-6 1-1 2-2 3-4h0c1 0 1 0 1-1 2-2 5-4 8-6 1 0 2-1 3-1-1-1-1-3-1-4s0-2 1-3v-5-2h-1c1-1 1-1 2-1 0 3-1 6 0 8l1-1v-9c1-4 0-9 1-11v11c2-1 5-2 7-3 3-1 6-3 9-4l10 16v1c-4 1-8 1-12 1-14 3-27 11-35 23-3 4-4 8-6 12z" class="B"></path><path d="M374 346c1-1 5-4 5-6 1 0 0-3 0-3l1-13c0-5 0-11 1-16v30l14-15c-4-11-8-21-10-33 3 5 4 11 6 17l29 79 24 66 8 21c1 3 2 5 3 8v4l1 2 6 16h-1c-1-3-6-18-7-19-1 0-2 0-2-1v-1c-1-4-3-9-5-14l-14-37c-1-4-8-26-10-27l-19 24c4 9 10 18 17 26l-1 1-17-25h-1-1v-2c0-1-2-5-2-6-3-5-6-12-6-18 3 7 6 15 10 22l20-24-28-76h0l-11 11c0 1-2 3-3 4v5l2 24c-1-1-1 0-1-1v-2c-2-8-2-16-3-24l-2 3-2 1-1-1z" class="b"></path><path d="M294 207c1 2 2 2 4 2h1v-1c1 1 1 0 2 1v2h1 0c3 9 5 16 9 24-2-1-2-1-3 0s0 3-1 4c1 1 1 1 1 2 0 2 0 2 1 3v4 1 1l2 1v1c1 1 2 2 3 2h0l-1-2c2 1 7 6 8 8l1 4v1h-1l-1 1h3v1 1c-1 0-1 1-1 2-2 3-3 4-2 7v3 1h1c1-1 2-1 4-1v-5h1v1 1c1 1 1 1 2 1v1h-1c1 1 2 2 2 3 1 1 1 1 1 2 2 4 4 10 5 14h-1c-2 1-4 2-5 4v2c1 1 2 3 4 3h3 0c-1 2-2 2-4 3v1l1 1 2 1-2 1v1l4 1-3 3h0v2l1 1-1 1v1c1 1 1 1 1 3 1 0 1 1 3 1l1 1c0 1-1 1-1 2v1l-1 1v1c1 1 1 4 0 5v1l-47-131 1-1c1 1 2 1 3 2l1-1-1-1v-1z" class="E"></path><path d="M321 281c1-1 2-1 4-1l-4 11-1-8v-1c1 0 1 0 1-1h0z" class="B"></path><path d="M329 304c1 1 2 3 4 3h3 0c-1 2-2 2-4 3v1l1 1h-2l1 2c1 2 2 3 1 5v1 1h-1v-2-3c-1-2-1-4-2-6s-1-3-1-6zm-8-39c-1-1-1-1-3-1l-3-8c-2 0-4 0-6-1v-1-1-1h1c-1-1-1-2-2-3v-2s-1 0-1-1v-1-1l-1-1 1-1v-1c0-1-1-1-1-2v-3l2-1c-1 1 0 3-1 4 1 1 1 1 1 2 0 2 0 2 1 3v4 1 1l2 1v1c1 1 2 2 3 2h0l-1-2c2 1 7 6 8 8l1 4v1h-1z" class="J"></path><path d="M421 454c-7-8-13-17-17-26l19-24c2 1 9 23 10 27l14 37c2 5 4 10 5 14v1c-2 1-4 4-5 6l-26-35z" class="I"></path><path fill="#161617" d="M639 175c2-1 5 0 7 0h-1v1h1l-1 1c13 11 20 28 21 44 1 10 0 21-2 31-2 12-6 24-10 37l-4 15-15 44-56 184-11 36-4 11c0 1-1 4-1 5 0 0 2 2 2 3-1 0-1 1-2 1 2 3 5 8 6 12h1l1 1-2 7h0v1c1 1 2 3 3 4 2 4 1 5 1 9l-1 1h-1-6c-1-1-1-1-2-1-1 1-2 2-3 2-2 3-3 6-3 9s0 4-1 7v1l-1 1v2h0c-1 1-1 2-2 3v3l-4 11-3 9v3c-1 5 0 11-3 15v1c1 2 0 3-1 5h-1c-1-1-1-2-2-4v-1l-1 2c0-1-1-1-1-2 0-2-2-6-2-7 0-2 1-3 1-6l-7 22-4-9-1-4-2-6-10-29-22-62c0-1-1-2-1-3h-1l1-1-1-1v-2l1-2 40 109 21-70 59-193 27-89 16-51c3-9 6-17 8-27 3-11 4-22 4-34-1-20-11-36-25-49z"></path><defs><linearGradient id="AO" x1="572.815" y1="666.142" x2="530.322" y2="616.599" xlink:href="#B"><stop offset="0" stop-color="#bbbdbd"></stop><stop offset="1" stop-color="#ece8e4"></stop></linearGradient></defs><path fill="url(#AO)" d="M536 676c1-8 4-15 6-22l13-45 4-12c1-3 2-7 4-10v1c2 3 5 8 6 12h1l1 1-2 7h0v1c1 1 2 3 3 4 2 4 1 5 1 9l-1 1h-1-6c-1-1-1-1-2-1-1 1-2 2-3 2-2 3-3 6-3 9s0 4-1 7v1l-1 1v2h0c-1 1-1 2-2 3v3l-4 11-3 9v3c-1 5 0 11-3 15v1c1 2 0 3-1 5h-1c-1-1-1-2-2-4v-1l-1 2c0-1-1-1-1-2 0-2-2-6-2-7 0-2 1-3 1-6z"></path><path d="M553 635c1 0 2 0 3 1l-1 1c-1 0-2 0-3-1l1-1z" class="Q"></path><path d="M560 624c1-5 4-11 7-14l2-2v1c1 1 2 3 3 4 2 4 1 5 1 9l-1 1h-1-6c-1-1-1-1-2-1-1 1-2 2-3 2z" class="E"></path><path d="M572 613c2 4 1 5 1 9-2-2-6-1-8-1h0c1-2 2-3 4-3v-1h1c-1 0-2 0-3-1 1 0 2-1 3-1 0 0 1-1 2-1v-1z" class="X"></path><path d="M377 346l2-3c1 8 1 16 3 24v2c0 1 0 0 1 1l-2-24v-5c1-1 3-3 3-4l11-11h0l28 76-20 24c-4-7-7-15-10-22 0 6 3 13 6 18 0 1 2 5 2 6v2h1c-2 3-5 6-5 8v2h-2-1l-11 14-1-1-3 4-5-12-10-30-10-27-1-4-2-4-1-3c0-2-1-2-1-3l5-6 20-22 1 1 2-1z" class="I"></path><path d="M393 434c-2-2-5-20-5-24l8 24h-3z" class="L"></path><path d="M356 380h1l-1-2h1l2 1c0 1 1 1 1 2s0 2-1 3c-2 1-4 3-4 4h-1l-1-4-2-4h5z" class="D"></path><path d="M351 380h5c-1 2-2 3-3 4l-2-4z" class="C"></path><path d="M401 430h1c-2 3-5 6-5 8v2h-2-1l-11 14-1-1 12-15-1-4h3v1l5-5z" class="P"></path><path d="M360 396h5v-1c2-1 2-2 4-2l1 1-1 1h-2c1 1 1 1 2 1v1c-1 2-2 6-3 9l-1 2h-1c-1-4-3-8-4-12z" class="F"></path><path d="M354 388h1c0-1 2-3 4-4-1 1-1 2-1 3v2c-1 2 1 5 2 7 1 4 3 8 4 12h1l-1 4v3l-10-27z" class="G"></path><path d="M374 346l1 1 2-1-27 31c0-2-1-2-1-3l5-6 20-22z" class="R"></path><path d="M364 412c1 1 1 2 1 3l1 3h3l5-8 1 1-2 3c1 1 1 1 1 2s-1 1-1 2l2 3-3 3h1c1 0 1 0 2 1l-2 1h1l1 1c-1 0-2 1-2 1 0 1 1 1 2 2h0l-3 1c1 1 1 1 2 1 1 1 0 1 1 1h-1c-1 0 0 0-1 1 0 0 1 1 2 1v1h-1c0 2 1 4 2 5v1c-1 1-1 0-1 1l-1 2-10-30v-3z" class="E"></path><path d="M402 430h1l17 25 1-1 26 35c1-2 3-5 5-6 0 1 1 1 2 1 1 1 6 16 7 19h1l-6-16-1-2v-4l16 47c1 2 2 5 3 7l4 13 1 3c-1 1-2 2-3 4l-31 40c-3 5-6 10-10 14v-1s-1 0-1-1h-1c-1-1-2-5-2-6l-10-27-11-31 3-6-1-1-3 4-8-22-5-14-3-7-4-12-7-19c0-2-2-5-2-7 1-2 2-3 3-4v-1l11-14h1 2v-2c0-2 3-5 5-8z" class="I"></path><path d="M413 537l1-1c1 1 1 2 1 2 1 1 1 0 2 1-3 2-2 3-3 6v1c1 1 1 2 2 4 1 0 0 0 0 1 2 1 1 2 2 3s1 2 1 3v1 1l1 1v1c1 1 0 0 0 2l1 1 1 4c0 2 1 4 0 6h-1l-11-31 3-6z" class="J"></path><g class="R"><path d="M477 548h1l1 3c-1 1-2 2-3 4l-31 40c-3 5-6 10-10 14v-1s-1 0-1-1l41-55c1-1 1-2 2-4z"></path><path d="M397 438c9 26 20 50 32 74l17-21-26-36 1-1 26 35c1-2 3-5 5-6 0 1 1 1 2 1 1 1 6 16 7 19h1l-6-16-1-2v-4l16 47c1 2 2 5 3 7l4 13h-1c-1 2-1 3-2 4h-1v-1c-3-2-4-9-5-12-1-5-4-10-5-15l-2-2-1 1c0 6 0 13-3 18 0-2 1-5 1-8 3-13-5-29-12-40l-10 13c-2 3-5 5-6 8 7 16 13 33 17 50h0l-18-49-16 21-1 1-1-1 1-2 5-7 11-13c-7-18-17-34-25-51l-5-13c-2-2-3-8-5-10h1 2v-2z"></path></g><path d="M461 523c-2-11-7-22-13-32l5-6 6 15v5c1 0 1 1 1 2l3 8c0 1 0 2 1 3v1c-1 1 0 3 0 5l-2-2-1 1z" class="J"></path><path d="M459 500l5 14c1 1 2 1 2 3 1 2 4 10 5 11 1 2 2 5 3 7l4 13h-1c-1 2-1 3-2 4h-1v-1c-3-2-4-9-5-12-1-5-4-10-5-15 0-2-1-4 0-5v-1c-1-1-1-2-1-3l-3-8c0-1 0-2-1-2v-5z" class="E"></path><path d="M464 514c1 1 2 1 2 3 1 2 4 10 5 11 1 2 2 5 3 7l4 13h-1c0-2-1-3-2-5-1-4-2-8-4-11h-1c-1-1-1-1-2-3l-3-9c0-2-1-4-1-6z" class="S"></path><path d="M402 430h1l17 25 26 36-17 21c-12-24-23-48-32-74 0-2 3-5 5-8zm-8 10c2 2 3 8 5 10l5 13c8 17 18 33 25 51l-11 13-5 7-1 2-3 4-8-22-5-14-3-7-4-12-7-19c0-2-2-5-2-7 1-2 2-3 3-4v-1l11-14z" class="I"></path><path d="M380 459c1-2 2-3 3-4v1c0 1-1 2-1 3 2 2 3 3 5 4l2 2c1 0 3 1 4 1v1c-1 0-2-1-3-1l-1 1h-1c-2-2-3-4-5-5l-3-3z" class="B"></path><path d="M396 496c0-1-1-3 0-4s1-3 2-4c1-3 3-5 5-7v2h0c-1 2-1 4-2 6v2h-1v1c0 2-2 3-3 4h-1z" class="G"></path><path d="M396 496h1c1-1 3-2 3-4v-1h1v-2c0 2 1 3 1 5v2l-1 2c-2 1-3 2-5 3 0 1 0 0 1 1v1l-1 1-3-7 2-1h1z" class="E"></path><path d="M383 462c2 1 3 3 5 5h1l3 1 1 1h-1-2c1 1 3 1 3 2h-2v1h1c1 1 0 1 1 2h-2c1 1 1 1 2 1l-1 1h-1c0 1 0 1 1 2-1 0-1 0-1 1-1 0-2-1-2-2-1-1-1-3-2-5-1-4-3-7-4-10z" class="C"></path><path d="M389 485l-7-19c0-2-2-5-2-7l3 3c1 3 3 6 4 10 1 2 1 4 2 5 0 1 1 2 2 2v1l-1 1h1l-1 1c0 1-1 2-1 3z" class="O"></path><path d="M401 498l-1 3-1-1-1 1c1 3 2 3 4 4h0l1 1c1 1 1 1 2 1 1-2 1-3 3-4l1 1-2 1c1 2 2 1 2 3-1 2-2 3-4 3l-1 1c-1 0-2 0-3 1 1 1 2 1 3 1h2c-3 1-4 1-5 4l-5-14 1-1v-1c-1-1-1 0-1-1 2-1 3-2 5-3z" class="B"></path><path d="M396 504l1-1 4 3c0 1 0 1-1 2h1l1 1h-1l-1 1 4 2c-1 0-2 0-3 1 1 1 2 1 3 1h2c-3 1-4 1-5 4l-5-14z" class="X"></path><path d="M406 514v2c-1 1 0 1 0 2v1c-1 1-1 0-2 1v1c1 0 2 0 3 1l-2 1c0 2 1 1 1 2v2c0 1 1 2 2 3 3-1 4-2 6-4l1-1-1-1h1l3 3-5 7-1 2-3 4-8-22c1-3 2-3 5-4z" class="C"></path><path d="M414 526l1-1-1-1h1l3 3-5 7v-1c0-1 0-1-1-2l-2 2-1-1s0-1 1-1l4-4h1l-1-1z" class="B"></path><path d="M661 174h1 51c9 0 19 0 28 1-3 2-6 4-10 7-5 3-10 8-14 13-11 14-17 31-22 48l-9 27c-3 9-6 18-8 27l-52 162c-2 9-5 18-8 28l-9 28-4 12-3 9 7 3c-2 0-5-2-7-1-1 0-2 1-2 2l-1-2 1-1v-3c-2 0-2 2-3 3-2 3-2 6-5 9-1 2-1 5-2 8v1 1l-1 1 1 2v1c-4 5-6 11-9 16-3 8-7 16-10 25l-1-1h-1c-1-4-4-9-6-12 1 0 1-1 2-1 0-1-2-3-2-3 0-1 1-4 1-5l4-11 11-36 56-184 15-44 4-15c4-13 8-25 10-37 2-10 3-21 2-31-1-16-8-33-21-44l1-1h-1v-1h1c-2 0-5-1-7 0-1 0 0-1 0-1h22z" class="Q"></path><path d="M569 591h3v3h-1c-1-1-1-2-2-3z" class="D"></path><path d="M702 196h2v2c-1 0-2 1-3 2h0c0-2 0-3 1-4z" class="F"></path><path d="M699 202h2v2l-1 1h-1l1 2c-1 0-2 0-2 1l-1 1 1 1h-1l-1-1c1-1 1-2 1-3 1-1 0-1 1-2 0 0 1-1 1-2z" class="D"></path><path d="M702 196c1-1 1-2 2-2h3 1 0l-2-2h1 4v1 1c-1 1-1 1-3 1-1 0-1 1-2 1h-2-2z" class="C"></path><path d="M565 587c2 3 6 8 5 12v1h-1c-1-4-4-9-6-12 1 0 1-1 2-1z" class="R"></path><path d="M703 177h21s-1 1-2 1c0 1-1 6-3 6v-1c-2 0-6 0-7 1l-1 1v-1-2h0v-1h5v-1c-2 0-7 0-9-1h3c1 0 1-1 2-1h1 4c-3 0-10 0-13-1h-1z" class="C"></path><path d="M662 313v-2h0l1 1h1l1 3c1 1 2 1 3 0s1-3 2-4v-1-2l1-1v-1h0v-1l1-1v-2h0-1l-1 3h-1l1-1v-3h3l-8 28c-2 4-3 9-5 13v-3l1-1v-1h0c-1-2-1-2 0-3-1-2 1-4 2-6-2-2-1-1-1-2v-1c-1-3 0-6 0-8v-1-3z" class="F"></path><path d="M662 313c0 1 1 3 2 4s1 1 1 2l1 1-2 3c-1 1 0 3-1 4v1h0c-2-2-1-1-1-2v-1c-1-3 0-6 0-8v-1-3z" class="C"></path><defs><linearGradient id="AP" x1="615.158" y1="497.463" x2="606.69" y2="495.386" xlink:href="#B"><stop offset="0" stop-color="#878786"></stop><stop offset="1" stop-color="#a09f9c"></stop></linearGradient></defs><path fill="url(#AP)" d="M626 450c1 2 1 3 0 6v3h0c-2 9-5 18-8 28l-9 28-4 12-3 9 7 3c-2 0-5-2-7-1-1 0-2 1-2 2l-1-2 1-1v-3c-2 0-2 2-3 3-2 3-2 6-5 9l4-12c0-2 1-3 1-5 1-1 0-1 1-2h1l-1-1c0-1 1-1 1-2l3-11 21-53c0-2 1-4 1-5 1-2 2-4 2-5z"></path><defs><linearGradient id="AQ" x1="772.319" y1="255.299" x2="580.095" y2="336.255" xlink:href="#B"><stop offset="0" stop-color="#0e100e"></stop><stop offset="1" stop-color="#312e2e"></stop></linearGradient></defs><path fill="url(#AQ)" d="M661 174h1 51c9 0 19 0 28 1-3 2-6 4-10 7-5 3-10 8-14 13-11 14-17 31-22 48l-9 27c-3 9-6 18-8 27l-52 162h0v-3c1-3 1-4 0-6 2-4 3-8 4-11l10-31 14-46c2-7 5-13 6-20 2-4 3-9 5-13l8-28 15-48 8-27 6-16c1-3 3-6 3-10-1 0-1-1-1-2h0v-2h2c1 0 1-1 2-1 2 0 2 0 3-1v-1-1h0c-1-1-1-1-1-2 1 0 2 0 3-1l-1-1-1-1 1-2h-1l1-1c1-1 5-1 7-1v1c2 0 3-5 3-6 1 0 2-1 2-1h-21c-3-1-9 0-12 0h-28c-6 0-12 0-17-1h-1v-1h1c-2 0-5-1-7 0-1 0 0-1 0-1h22z"></path><path d="M704 198l2-1h4l-5 8c0-1 0-2 1-2 0-2 0-2-1-3h0c-1 0-1-1-1-2z" class="C"></path><path d="M724 177l9-1c-8 6-17 13-23 21h-4l-2 1h0v-2h2c1 0 1-1 2-1 2 0 2 0 3-1v-1-1h0c-1-1-1-1-1-2 1 0 2 0 3-1l-1-1-1-1 1-2h-1l1-1c1-1 5-1 7-1v1c2 0 3-5 3-6 1 0 2-1 2-1z" class="E"></path><path d="M712 185h1 2 3v1c-1 1-2 1-3 1h0-3v1l-1-1 1-2z" class="F"></path><path d="M219 152h1v1l1 9c1 2 1 4 2 5h1c8 0 38 28 44 34l1-1h1c2-2 5-3 7-4v-1c-4-6-10-11-15-15-2-2-7-4-8-6h142c-7 7-13 14-17 23l-3 3c0 3-1 5-2 7h1l-1 1c-1-3 2-7 2-11h0c0-1 1-3 2-4 3-6 6-11 10-16h-44l-2 1c0-1-1-1-1-1-1-1-2 0-3 0h-8-54v1c-1 0-3-1-4 0l3 1c2 1 4 2 5 4 1 0 1 1 2 1 2 1 3 3 4 6l2 1c0 2 0 3 1 3h1c0 1-1 2-2 3l-2 1v4l4 7 47 131 3 8 6 18c1 0 2 1 3 1 1 1 1 1 2 1h3l-5 6c0 1 1 1 1 3l1 3 2 4 1 4 10 27 10 30 5 12 3-4 1 1v1c-1 1-2 2-3 4 0 2 2 5 2 7l7 19 4 12 3 7 5 14 8 22 3-4 1 1-3 6 11 31 10 27c0 1 1 5 2 6h1c0 1 1 1 1 1v1c4-4 7-9 10-14l31-40 3 5c0 1 1 2 1 4h0l1 1h3l4 11s1 2 1 3l-1 2v2l1 1-1 1h1c0 1 1 2 1 3l22 62 10 29 2 6 1 4 4 9 7-22c0 3-1 4-1 6 0 1 2 5 2 7 0 1 1 1 1 2l1-2v1c1 2 1 3 2 4h1c1-2 2-3 1-5v-1c3-4 2-10 3-15v-3l3-9 4-11v-3c1-1 1-2 2-3h0v-2l1-1v-1c1-3 1-4 1-7s1-6 3-9c1 0 2-1 3-2 1 0 1 0 2 1h6 1l1-1c0-4 1-5-1-9-1-1-2-3-3-4v-1h0l2-7c3-9 7-17 10-25 3-5 5-11 9-16v-1l-1-2 1-1v-1-1c1-3 1-6 2-8 3-3 3-6 5-9 1-1 1-3 3-3v3l-1 1 1 2c0-1 1-2 2-2 2-1 5 1 7 1l-7-3 3-9 4-12 9-28c3-10 6-19 8-28l52-162v1l-5 17 4 3 1 1h1v-1c1-2 0-1 1-2v-1l1 1h0c-1 2-1 4 0 5s1 1 1 3l-1-1v1c1 1 1 1 1 3a104.13 104.13 0 0 1-5 12l-9 28c-2 9-5 19-8 27-1 1-2 1-3 2l-1 1h2c0 2-1 4-1 6l-4 11-33 104c-2 6-3 14-6 20 0 1-1 2-1 3h-1-1 0l1 2-9 31-6 17-28 89-5 14-15 47-14 43c-1 3-2 5-4 7h-1c-1 0-1 0-2 1h3l-11 40c-2 6-3 13-6 18-1 2-2 3-4 4l-1-1c-3 1-6 3-9 5-2-9-6-17-9-25l-14-40-2-7c-1 0-8-22-10-26l-4-10-1-2-75-205-2-6-11-30-23-64-52-142-17-49c-5-14-9-28-17-39-14-25-42-35-69-36h-13-6l-1-1h2c0-1 0-1 1-2s2-3 4-4c4-5 6-7 13-9 1-1 3-1 4-1 8 2 15 3 22 4 1-1 0-1 0-3v-1c-1-1 0-2-1-4v-4z" class="I"></path><path d="M519 800h4l1-1c0 1 0 2-1 3 0 2-1 2-2 3l-1-1c-1-1-1-2-1-4z" class="E"></path><path d="M482 788h1c2-2 7-8 9-8 1 1 1 1 1 3-3 2-7 5-10 6l-1-1zm-42-140h4v3c-1 1-1 2-3 2h-2c-2-1-2-2-2-3s2-1 3-2z" class="O"></path><path fill="#161617" d="M515 804c1 0 1-2 2-3v1c-1 4-4 17-7 20-3-2-6-12-6-15 1-1 0-1 0-2s0-1 1-1h0 1 0l4 1h0l1 1h2 0v-1h1v-1h1z"></path><path d="M510 805h0l1 1h2 0v-1h1v-1h1l-5 11c-2-3-3-7-4-11l4 1z" class="T"></path><defs><linearGradient id="AR" x1="520.567" y1="801.917" x2="509.711" y2="772.212" xlink:href="#B"><stop offset="0" stop-color="#6f6e6d"></stop><stop offset="1" stop-color="#b3b2b2"></stop></linearGradient></defs><path fill="url(#AR)" d="M518 771l-1-1h0c1-1 1-1 2-1 1-1 0-1 1-1l1 2c-1 2-1 3-1 4l1 2 1 1h1l1 4-7 18 1 1h1c0 2 0 3 1 4h-2c0-1 0-2-1-2v-1c-1 1-1 3-2 3h-1v1h-1v1h0-2l-1-1h0l-4-1h0l-2-5c3-1 4-1 6-3 1-2 2-3 3-5h0c1-1 1-1 1-2s0-1 1-2v-2-4-1c0-1 0-2 2-3h-1v-1l2-2v-3z"></path><path d="M513 791c0 3-1 5-2 8-1 2-1 3-3 3 1 1 1 1 2 1v2l-4-1h0l-2-5c3-1 4-1 6-3 1-2 2-3 3-5z" class="S"></path><path d="M592 546c3-3 3-6 5-9 1-1 1-3 3-3v3l-1 1 1 2c0-1 1-2 2-2l-47 145c0-3 1-6 2-9 2-9 6-18 8-27-1-1-1 0-1-1 0-3-1-4-2-7h-1c-1 0 0 0-1-1-1-2-2-3-3-5 0-3 1-6 3-9 1 0 2-1 3-2 1 0 1 0 2 1h6 1l1-1c0-4 1-5-1-9-1-1-2-3-3-4v-1h0l2-7c3-9 7-17 10-25 3-5 5-11 9-16v-1l-1-2 1-1v-1-1c1-3 1-6 2-8z" class="L"></path><path d="M574 607c1 3 2 5 1 7v1c-2-1-3-4-4-5 2-1 2-2 3-3z" class="F"></path><path d="M569 608c1-1 1-2 1-3l2-2c1 1 2 3 2 4-1 1-1 2-3 3l-2-1v-1h0z" class="C"></path><path d="M590 559c1-1 2-1 4-1l-6 16-1-1h-3c2-4 4-9 6-13v-1z" class="F"></path><path d="M592 546c3-3 3-6 5-9 1-1 1-3 3-3v3l-1 1 1 2c-2 5-3 13-6 18-2 0-3 0-4 1l-1-2 1-1v-1-1c1-3 1-6 2-8z" class="H"></path><path d="M584 573h3l1 1c-1 2-5 16-6 17v2c-2 4-4 10-5 15h0c-1-2-2-5-4-7h-1c3-10 8-19 12-28z" class="C"></path><path d="M582 593h-4l-1-1 1-2c2 0 3 0 4 1v2z" class="Q"></path><path d="M560 624c1 0 2-1 3-2 1 0 1 0 2 1h6 1c-1 2-1 5-2 6l-5 18c-1-1-1 0-1-1 0-3-1-4-2-7h-1c-1 0 0 0-1-1-1-2-2-3-3-5 0-3 1-6 3-9z" class="C"></path><path d="M560 624c1 0 2-1 3-2 1 0 1 0 2 1-3 1-4 3-5 6-1 2 0 4 0 5l2 5h-1c-1 0 0 0-1-1-1-2-2-3-3-5 0-3 1-6 3-9z" class="X"></path><path d="M560 634c2 0 6 1 7 0-1-1-3-1-5-1l1-1h4 1v-1l2-2-5 18c-1-1-1 0-1-1 0-3-1-4-2-7l-2-5z" class="F"></path><defs><linearGradient id="AS" x1="546.335" y1="708.845" x2="519.943" y2="700.156" xlink:href="#B"><stop offset="0" stop-color="#969594"></stop><stop offset="1" stop-color="#cccac8"></stop></linearGradient></defs><path fill="url(#AS)" d="M557 633c1 2 2 3 3 5 1 1 0 1 1 1h1c1 3 2 4 2 7 0 1 0 0 1 1-2 9-6 18-8 27-1 3-2 6-2 9l-31 98-1-4h-1l-1-1-1-2c0-1 0-2 1-4v1h1c-1-1-1-4-1-5v-1c0-3-1-5 0-8 1-1 1-2 1-3 0 0 0-1-1-1 1-2 1-4 1-6 1-1 1-4 1-5v-7c1-1 1-3 1-4v-1l-1-1v-1c0-1 0-1-1-2v-1-1l-1-2c0-1-1-3-2-4l-5-12-3-3-1-2 1-1h0 1c2 1 3 4 4 6l3 6v-3c-1-2 0-2 0-3-1-3 0-6-1-8v-2l-1-1h4l-1-1h-2v-1c2 0 4-1 6-1 0-1 0-2 1-2v-1l4 9 7-22c0 3-1 4-1 6 0 1 2 5 2 7 0 1 1 1 1 2l1-2v1c1 2 1 3 2 4h1c1-2 2-3 1-5v-1c3-4 2-10 3-15v-3l3-9 4-11v-3c1-1 1-2 2-3h0v-2l1-1v-1c1-3 1-4 1-7z"></path><path d="M521 770v1h1c-1-1-1-4-1-5v-1c0-3-1-5 0-8l1 6h1v1h-1c0 2 0 3 1 5v1 7h-1l-1-1-1-2c0-1 0-2 1-4z" class="E"></path><path d="M277 196v-1c-4-6-10-11-15-15-2-2-7-4-8-6h142c-7 7-13 14-17 23l-3 3c0 3-1 5-2 7h1l-1 1c-1-3 2-7 2-11h0c0-1 1-3 2-4 3-6 6-11 10-16h-44l-2 1c0-1-1-1-1-1-1-1-2 0-3 0h-8-54v1c-1 0-3-1-4 0l3 1c2 1 4 2 5 4 1 0 1 1 2 1 2 1 3 3 4 6l2 1c0 2 0 3 1 3h1c0 1-1 2-2 3l-2 1v4l4 7 47 131 3 8 6 18c1 0 2 1 3 1 1 1 1 1 2 1h3l-5 6c0 1 1 1 1 3l1 3 2 4 1 4 10 27 10 30 5 12 3-4 1 1v1c-1 1-2 2-3 4 0 2 2 5 2 7l7 19 4 12 3 7 5 14 8 22 3-4 1 1-3 6 11 31 10 27c0 1 1 5 2 6h1c0 1 1 1 1 1 0 2-1 2 0 4 1 6 4 11 6 17l11 29 19 53 14 35h0l11 31 5 13 3 9 2 5h-1 0c-1 0-1 0-1 1s1 1 0 2c-3-5-4-11-6-16l-12-31-26-71-64-175-54-150-38-106c-7-19-12-39-22-56l-3-4-2-2z" class="b"></path><path d="M346 366c1 0 2 1 3 1 1 1 1 1 2 1h3l-5 6-3-8z" class="C"></path><path d="M278 187l3 3c0 2 3 5 3 7l-1 1-8-11h1l1 1 1-1z" class="H"></path><path d="M266 178c2 0 3 0 4 2 3 2 5 4 8 7l-1 1-1-1h-1c-1 0-2-1-2-2-3-2-5-4-7-7z" class="S"></path><path d="M266 178h0v-1h10v1c-1 0-3-1-4 0l3 1v1c1 1 3 2 3 3 1 1 2 2 2 3v1c1 1 2 1 4 2h0-1c-1 0-2 1-2 1l-3-3c-3-3-5-5-8-7-1-2-2-2-4-2z" class="E"></path><path d="M275 179c2 1 4 2 5 4 1 0 1 1 2 1 2 1 3 3 4 6l2 1c0 2 0 3 1 3h1c0 1-1 2-2 3l-2 1v4l-3-4 1-1c0-2-3-5-3-7 0 0 1-1 2-1h1 0c-2-1-3-1-4-2v-1c0-1-1-2-2-3 0-1-2-2-3-3v-1z" class="O"></path><path d="M219 152h1v1l1 9c1 2 1 4 2 5h1c8 0 38 28 44 34l1-1h1c2-2 5-3 7-4l2 2 3 4-7 4c1-2 2-3 3-4h-1c-3 1-5 2-8 1v1c3 6 7 12 10 19 3 9 5 19 9 27h1c2-1 3-2 5-2l1-1h0v1 1c0 2-2 5-3 7s-2 3-3 5l1 2c1 3 2 7 4 10l8 23 21 58 4 13c1 2 2 5 4 8 0 1 0 2 1 4l2 7 13 34 4 14c1 2 2 6 4 8 3 0 2 0 4-2l1 1c-1 1-1 2-2 3h0-1-1c-1 1-1 1-1 2l9 22 22 62 47 130 7 16 35 95 4 11c1 2 1 4 3 6h0l1 1-1 1c0 2 1 4 2 6l7 17a30.44 30.44 0 0 1 8-8l1 1c-3 3-6 5-9 8 2 7 5 13 8 20 3 6 6 13 10 19-3 1-6 3-9 5-2-9-6-17-9-25l-14-40-2-7c-1 0-8-22-10-26l-4-10-1-2-75-205-2-6-11-30-23-64-52-142-17-49c-5-14-9-28-17-39-14-25-42-35-69-36h-13-6l-1-1h2c0-1 0-1 1-2s2-3 4-4c4-5 6-7 13-9 1-1 3-1 4-1 8 2 15 3 22 4 1-1 0-1 0-3v-1c-1-1 0-2-1-4v-4z" class="B"></path><path d="M475 786c0 1 1 2 2 2h0 1 1 0l1 1h0l-1 1s-1 1-1 2l-1 1-2-7z" class="C"></path><path d="M277 196l2 2c-3 1-6 1-9 3l-1-1h1c2-2 5-3 7-4z" class="D"></path><path d="M476 555l3 5c0 1 1 2 1 4h0l1 1h3l4 11s1 2 1 3l-1 2v2l1 1-1 1h1c0 1 1 2 1 3l22 62 10 29 2 6 1 4v1c-1 0-1 1-1 2-2 0-4 1-6 1v1h2l1 1h-4l1 1v2c1 2 0 5 1 8 0 1-1 1 0 3v3l-3-6c-1-2-2-5-4-6h-1 0l-1 1 1 2 3 3 5 12c1 1 2 3 2 4l1 2v1 1c1 1 1 1 1 2v1l1 1v1c0 1 0 3-1 4v7c0 1 0 4-1 5 0 2 0 4-1 6 1 0 1 1 1 1 0 1 0 2-1 3-1 3 0 5 0 8v1c0 1 0 4 1 5h-1v-1l-1-2c-1 0 0 0-1 1-1 0-1 0-2 1h0l1 1v3l-2 2v1h1c-2 1-2 2-2 3v1 4 2c-1 1-1 1-1 2s0 1-1 2h0c-1 2-2 3-3 5-2 2-3 2-6 3l-3-9-5-13-11-31h0l-14-35-19-53-11-29c-2-6-5-11-6-17-1-2 0-2 0-4v1c4-4 7-9 10-14l31-40z" class="Q"></path><path d="M511 679c-2-5-4-12-6-18h1l1 2c1 3 1 5 2 8l2 5 1-1h0l1-1h4v1c-1 0-4 0-5 1h0c3 0 6 0 8 1v1h-7c-1 0-1 0-2 1z" class="D"></path><path d="M484 565l4 11s1 2 1 3l-1 2-1-1h0v-1c0-1-1-1-1-2-1-1-1-1-1-2s-2-2-2-2h-3l2-1-1-1 1-1-1-1v-1-1h-1l1-2h3z" class="S"></path><path d="M512 650l10 29c-2 0-8 1-9-1h7v-1c-2-1-5-1-8-1h0c1-1 4-1 5-1v-1h-4v-1h0v-2h4v-1c-1 0-3 1-4 0v-1h0c1-1 2-1 3-1-1-1-4 0-6 0v-1h2l1-2h0c0-1 1-1 2-2v-2l-3-9v-2z" class="O"></path><path d="M459 600h0c1 2 1 3 1 5 1 1 0 1 1 2l1 5 1 2v2c1 1 1 2 1 2l1 2v2 2s1 1 1 2 0 2 1 2v2 1c0 1 1 2 1 2v2s1 1 1 2l1 3c0 2 1 3 2 5l4 8v2c-2-1-4-8-6-10l1-1c-1-2-2-3-2-4v-1l-1-1v-2l-1-3c-1-1-1-2-1-3s-1-3-1-4v-2h-1 0v3c1 1 1 1 1 2v2c1 1 1 1 1 2v2c1 2 2 5 2 7 1 1 2 3 2 4v3l1 3 1 2c0 2 1 3 1 5-1 0-1 0-1-1v-2l-2-4-1-5-1-2v-1l-1-4c0-1 0-3-1-3v-2c-1-2-1-2-1-3v-2c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-2c-1-1-1-1-1-2v-2l-1-1v-2-1-1c-1-1-1-2-1-3s0-2-1-3v-2-5z" class="I"></path><path d="M477 687c3 14 5 27 13 39 2 3 5 6 8 6 1 0 1 0 2-1 2-5-2-14-4-19-1-2-2-7-2-9l1 2c2 7 6 14 7 21 1 2 1 5 0 7-1 1-2 1-3 1-3 0-5-2-7-3-11-11-15-29-15-44z" class="P"></path><path d="M490 588l22 62v2l-1-2h-2c1 1 1 2 0 3l-1-1v-1l-2-1c-1-1-1-1-2-1v-2-1h0v-1c0-2-1-3-1-4 1-2 2-1 4-2h-2 2l-1-3-2-5c0-1-1-1-1-2v-1-1l-1-1v-1h0l-1-1v-3h-4l-1-1h3 1 0c0-1-1-2-1-3v-2l-2-3v-2c-1-1-2-3-2-4s-1-2-1-3l-1-4c-1-1-2-3-2-4v-1c-1-2-1-3-1-6z" class="F"></path><path d="M504 645h4l1 2h0c-2 0-3-1-5 0v-1h0v-1z" class="C"></path><path d="M511 679c1-1 1-1 2-1 1 2 7 1 9 1l2 6 1 4v1c-1 0-1 1-1 2-2 0-4 1-6 1v1h2l1 1h-4c0-1 0-1-1-2v-1l-2-4c0-3-2-6-3-9z" class="F"></path><path d="M524 685l1 4v1c-1 0-1 1-1 2-2 0-4 1-6 1v1h2l1 1h-4c0-1 0-1-1-2v-1l-2-4c3 0 6 1 8-1h0-1c-1-1-3 0-4-1 1-1 5-1 7-1z" class="C"></path><path d="M485 746v1c3 3 3 11 7 11l-1-2v-2-1h0c2 3 2 7 3 10l1 1c0 1 0 2 1 4 0 0 1 1 2 1l-1 1c0 2 1 4 2 5l1 1c1 2 2 3 2 5 1 2 3 5 5 6h2c1-1 2-2 2-4l1-2 1-2v-1-2l3-2v-1c1-1 1-2 2-2v3l-2 2v1h1c-2 1-2 2-2 3v1 4 2c-1 1-1 1-1 2s0 1-1 2h0c-1 2-2 3-3 5-2 2-3 2-6 3l-3-9-5-13-11-31z" class="G"></path><path d="M496 777c2 1 3 3 4 5 0 1 1 2 1 3 1 1 1 2 2 3l1 1c0 1 0 0 1 1v1l1 1c1 0 3 1 4 0 1 0 2-2 2-3v-1c1-2 2-5 3-7v4 2c-1 1-1 1-1 2s0 1-1 2h0c-1 2-2 3-3 5-2 2-3 2-6 3l-3-9-5-13z" class="N"></path><path d="M501 790l3 2v1l2 2-1 1h3c1 1 1 0 2 0h0c-2 2-3 2-6 3l-3-9z" class="H"></path></svg>