<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="64 40 552 648"><!--oldViewBox="0 0 656 752"--><style>.B{fill:#363536}.C{fill:#484848}.D{fill:#585758}.E{fill:#605f5f}.F{fill:#b1b0b1}.G{fill:#c0bfc0}.H{fill:#4f4f4f}.I{fill:#919091}.J{fill:#a09fa0}.K{fill:#818081}.L{fill:#3b3b3b}.M{fill:#666565}.N{fill:#2b2a2b}.O{fill:#444344}.P{fill:#6d6d6d}.Q{fill:#999898}.R{fill:#a8a7a7}.S{fill:#bab8b9}.T{fill:#cfcecf}.U{fill:#7c7b7c}.V{fill:#535253}.W{fill:#777677}.X{fill:#403f40}.Y{fill:#c9c7c8}.Z{fill:#717071}.a{fill:#252525}.b{fill:#5b5b5b}.c{fill:#8c8a8b}.d{fill:#878686}.e{fill:#313031}.f{fill:#222121}.g{fill:#e8e7e7}.h{fill:#dfdedf}.i{fill:#dad9da}.j{fill:#1e1d1d}.k{fill:#f5f4f5}.l{fill:#d6d5d6}.m{fill:#121212}</style><path d="M306 145c1-1 2-1 3-2v1l-1 2c0 1 0 1 1 1v1l-1-1-2-2z" class="N"></path><path d="M306 145l2 2 1 1c-1 0-2 1-3 1 0-1-1-2-1-2-1 0-1-1-2-1l3-1z" class="B"></path><path d="M355 359l-1 1c-1 0-3 0-4-1l-1-1 2-2h1c0 2 0 2 2 2 0 1 0 1 1 1z" class="C"></path><path d="M313 144c1 0 2-1 3-2 1 1 1 3 1 4-1 0-2 0-2 1l-1 1h-1l-1-1c1-1 2-1 2-2l-1-1z" class="D"></path><path d="M224 610h2 2c2 0 3 1 4 1l-19-1c2 0 5 1 7 0h1 3z" class="X"></path><path d="M532 342l1 6v6c-2-4-2-7-3-11 1 1 1 1 1 2h0 1v-3z" class="g"></path><path d="M372 344h1v3 2-5c1-1 1-5 1-6v-1 16h-2v-1-1c1-2 0-5 0-7z" class="O"></path><path d="M383 281c1 5 0 11 0 16 1 1 1 2 1 2v10s-1 1-1 2v-30z" class="a"></path><path d="M360 148h1l2 2 7 4-1 1v1 2c-3-3-6-7-9-10z" class="M"></path><path d="M436 250c3 6 2 13-1 20 0 1 0 1-1 2v-1c1-2 1-4 2-6v-6-9z" class="D"></path><path d="M336 674c1-2 2-4 3-5l-2 9c-1 2-1 4-2 6 0 1 0 3-1 4v-7l2-7z" class="B"></path><path d="M379 328l5-2c0 6 0 11-1 17v5l-1-14 1-4s0-1-1-2h-3z" class="N"></path><path d="M313 144l1 1c0 1-1 1-2 2l1 1h1l-6 3c-1 0-1 1-2 1h0c-1 1-2 1-4 1 2-1 3-2 4-3v-1c1 0 2-1 3-1v-1c1 0 1 0 2-1l2-2z" class="E"></path><path d="M375 331c0-1 0-1 1-1l3-2h3c1 1 1 2 1 2l-1 4v-3c-1 0-4 1-5 2-1 0-2 1-2 1-1 1-1 2-1 3v-6h1z" class="O"></path><path d="M375 331c0-1 0-1 1-1l3-2h3c1 1 1 2 1 2-2 0-4 0-6 1h-2z" class="B"></path><path d="M62 135l-11 2c4-3 10-5 14-5h-1 9 3c-1 1-7 1-9 1 0 0-1 0-2 1h0 1l-4 1z" class="I"></path><path d="M294 149l3 3v2 2h0 2l-1 1c-1 1-2 3-3 4h0c-1-1-2-2-2-4 1 1 1 2 1 2h1v-1c0-2 0-3-1-4 0-2-1-3-1-5h1z" class="e"></path><path d="M234 447v-2c1 1 1 3 2 4 1 2 1 4 2 5l3 10v1c-4-5-6-12-7-18z" class="g"></path><path d="M495 67l14 16h-3c-4-4-8-9-11-13-1-1-1-2 0-3z" class="K"></path><path d="M360 148h-1v-3c3-1 11 1 14 2v1c-3 1-5 1-8 1l-2 1-2-2h-1z" class="B"></path><path d="M358 342l-3-3 14-6 1 4h-1c-2 1-3 2-5 3-1 0-3 1-5 1l-1 1z" class="C"></path><path d="M180 54l1 1c0 1-3 3-4 5-2 2-3 3-4 5-2 2-3 4-4 5s-1 2-2 3l-1-1h1l1-3c-3 4-6 8-10 12-1 2-3 4-5 5l27-32z" class="T"></path><path d="M430 271c2-6 4-11 3-17v-6l1-1c1 1 1 2 2 3v9c-1 5-2 9-4 13h0c-1-1-1-1-2-1zM238 421c0 2-1 4-1 5l-2 9v-1s1 0 1-1h0v-1c1-1 1-2 1-3s0-2 1-2v-2c1-2 1-3 3-5-1 2-1 5-1 7-1 3-3 7-4 10-1 4 0 8 0 12-1-1-1-3-2-4v2-6c0-7 1-14 4-20z" class="h"></path><defs><linearGradient id="A" x1="302.798" y1="628.069" x2="298.878" y2="631.315" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M294 623c5 3 10 6 14 10l4 5v3c-6-7-13-12-21-17 1 0 2 0 3-1z"></path><path d="M302 146h1c1 0 1 1 2 1 0 0 1 1 1 2v1c-1 1-2 2-4 3l-1 1-2 2h-2 0v-2-2l-3-3c0-1 1-1 1 0l2-2h0 2l3-1z" class="a"></path><path d="M297 147h2v2h0l-2-2z" class="f"></path><path d="M302 146h1c1 0 1 1 2 1 0 0 1 1 1 2v1l-1-1h-1c-1 0-2-2-2-3z" class="j"></path><path d="M297 374l-1-1c0-1 0-3 1-4s6-5 8-5h2 0c0-1 0-2 1-2v-1s0-1 1-1c0-1 0-1 1-1 0 1 1 1 1 1 1 1 1 0 2 0v-1-1-1h3v3l-1 1h-3c-1 1-2 1-4 1 0 2 1 4 1 5 1 0 1 1 2 1l-2 2h-1c0-1-1-2-1-4h-1v1h-2v1c-1 0-1 1-1 1h-1-1s-1 0-1 1c-1 1-2 1-3 2v2z" class="e"></path><path d="M317 353c4-3 10-6 15-7v1s1 1 2 1 2-1 3-1c-1 0-1 1-2 1l-1 1h0l-1-1h-3v1c0 1-1 1-2 2v1h-1l-1-1-1 1c0 2 1 4 1 6-1 1-2 1-3 1v-1h-1l-1 1h-1c1-1 1-1 1-2h-2c0 1 0 1-1 2h0l-1-1-1-1c1 0 1-1 2-2h0 1c1 0 1 0 2-1h0 4v-2c-2-1-3 0-5 1h-3z" class="B"></path><path d="M238 454v-1c0-1 0 1 0-1-1-2 0-6 0-8 0-1 0-2 1-3 0-2 0-3 1-4-1 9 0 21 5 29l6 9c-4-2-9-6-10-10h0v-1l-3-10z" class="G"></path><path d="M355 133c2 3 4 4 8 5h1l18 6 5 2h-1c-1 0-3-1-3 0h0c1 0 2 1 3 1 1 1 2 1 4 2 0 0 1 1 2 1-2 0-4-2-6-2-3-2-7-3-10-4l-28-8h1 4v-1-1c0-1 1-1 2-1z" class="C"></path><path d="M344 344c1-1 3-1 4-2h3c3 0 5 1 6 3 2 2 3 6 2 9 0 1 0 3-2 4l-1 1h-1 0c-1 0-1 0-1-1 1-1 1-2 2-4l-1-1 1-1c-1-1-1-3-2-4s-3-2-4-3c-1 0-2-1-3-1l-2 1c0-1-1-1-1-1z" class="b"></path><path d="M344 344c1-1 3-1 4-2h3 1c0 1 1 1 1 1 2 1 3 3 4 5 0 1 0 3-1 4h0c-1-1-1-3-2-4s-3-2-4-3c-1 0-2-1-3-1l-2 1c0-1-1-1-1-1z" class="j"></path><path d="M318 128l4-1c-1 2-4 3-4 5h0 3 3l8-1c2 0 3 0 5 1-2 0-3 0-4 1l-21 5c-2 0-2-1-3-2h0l1-1v-1h1l7-6z" class="M"></path><path d="M318 128l4-1c-1 2-4 3-4 5h0 3l-9 3-1-1 7-6z" class="I"></path><defs><linearGradient id="C" x1="372.51" y1="147.885" x2="372.032" y2="157.97" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#C)" d="M373 147c2 1 7 1 8 3v3h0l1 1-3 4v1h-1 0l-2-2h-1-2-1c0-1-1-2-2-3l-7-4 2-1c3 0 5 0 8-1v-1z"></path><path d="M350 345c1 1 3 2 4 3s1 3 2 4l-1 1h-4c-1 1-4 4-4 6v1s-1-1-1 0c-1 0-2 0-3 1h1v1h-3c0-1-1-1-1-1v-1c-1 0-1-1 0-1l1-1c1 0 1 0 1-1v-1h-1c1-1 1-2 2-3l2-2-1-1 1-1v-1h-1c0-1 2-2 3-2s2-1 3-1z" class="M"></path><path d="M342 357h1v4h-1c-1 0-1-1-2-1 1-1 1-1 1-2 1 0 1 0 1-1z" class="D"></path><path d="M432 272h0c2-4 3-8 4-13v6c-1 2-1 4-2 6v1c-2 5-5 10-8 14-7 10-15 18-25 25 1-1 2-3 3-3l1-1h1c1-1 1-2 2-3l-1 1c5-5 11-10 14-17 4-5 8-10 11-16z" class="M"></path><path d="M470 78c3-6 6-12 7-19 0-3 0-6 1-9h1c6 3 11 11 16 17-1 1-1 2 0 3-6-5-9-12-15-17-1 6-1 12-3 17-1 4-4 8-6 11l-1 1v-1c0-1 1-1 1-3h-1z" class="I"></path><path d="M319 651c1-1 1-1 1-3h1c5 8 9 17 11 27l1 9h1v-3 7l-1 11-2-17c-3-11-6-21-12-31z" class="P"></path><path d="M430 271c1 0 1 0 2 1-3 6-7 11-11 16-10 11-21 19-34 26-3 2-7 4-10 5l-9 3h-1c1-1 0-1 0-1v-1l3-1 7-2h0l5-3c3-1 6-3 8-4 16-9 32-23 40-39z" class="k"></path><path d="M370 319l7-2h0c-2 2-4 2-7 3h2l1-1h1c1-1 2 0 3 0l-9 3h-1c1-1 0-1 0-1v-1l3-1z" class="g"></path><defs><linearGradient id="D" x1="368.305" y1="316.9" x2="372.862" y2="331.186" xlink:href="#B"><stop offset="0" stop-color="#656464"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#D)" d="M407 305l1-1c-1 1-1 2-2 3h-1l-1 1c-1 0-2 2-3 3-15 11-31 19-48 26-7 3-14 5-20 8 0-1 0-1 1-2 2-1 5-2 7-3 4-2 8-5 12-7 6-3 13-4 19-6 4-2 9-5 13-8l22-14z"></path><path d="M344 344s1 0 1 1l2-1c1 0 2 1 3 1-1 0-2 1-3 1s-3 1-3 2h1v1l-1 1 1 1-2 2c-1 1-1 2-2 3h-1 0-3c-1 0-2 1-3 1l-1 1v2c0 1 1 2 1 3h0l-1 1-2 2v-3c0-1-2-4-3-5l-1 2c0 1-1 1-2 2h0l1-4c0-2-1-4-1-6l1-1 1 1h1v-1c1-1 2-1 2-2v-1h3l1 1h0l1-1c1 0 1-1 2-1 1-1 3-2 4-2h1l2-1z" class="U"></path><path d="M330 353l1-2h1c1 0 1 0 1 1v6l-3-4v-1z" class="T"></path><path d="M344 350l1 1-2 2c-1 1-1 2-2 3h-1 0-3c2-2 5-4 7-6z" class="D"></path><path d="M333 350h3c3-1 5-2 8-2-3 2-8 4-10 7h0c-1-1-1-4-1-5z" class="V"></path><path d="M330 353v1l-1 1v1l1 1 3 3c0 1 1 2 1 3h0l-1 1-2 2v-3c0-1-2-4-3-5l-1 2c0 1-1 1-2 2h0l1-4s1-1 1-2l1-1c0-1 1-1 2-2z" class="G"></path><path d="M344 344s1 0 1 1l2-1c1 0 2 1 3 1-1 0-2 1-3 1s-3 1-3 2c-3 0-5 1-8 2h-3v-2l1 1h0l1-1c1 0 1-1 2-1 1-1 3-2 4-2h1l2-1z" class="L"></path><path d="M316 357l1 1 1 1h0c1-1 1-1 1-2h2c0 1 0 1-1 2h1l1-1h1v1c1 0 2 0 3-1l-1 4h0c1-1 2-1 2-2l1-2c1 1 3 4 3 5v3c-2 2-4 3-6 4-3 1-6 1-9 1l-1-1c-1 0-3-1-4-2-1 0-1-1-2-1 0-1-1-3-1-5 2 0 3 0 4-1h3l1-1v-3h0z" class="Q"></path><path d="M323 358v1c1 0 2 0 3-1l-1 4c-1 2-2 3-3 4h-1v-1-1c1-2 2-4 2-6z" class="C"></path><path d="M315 370h0c-1-1-1-2-1-3v-1l-1-1c0-2 0-2 1-3 1 0 6-1 7 0h0v3 1h0c-1-1-1-1-1-2h-3l-1 1c-1 2 0 4 0 6l-1-1z" class="T"></path><path d="M325 362h0c1-1 2-1 2-2l1-2c1 1 3 4 3 5v3c-2 2-4 3-6 4-3 1-6 1-9 1 0-2-1-4 0-6l1-1h3c0 1 0 1 1 2h0 1c1-1 2-2 3-4z" class="g"></path><path d="M325 362h0c1-1 2-1 2-2l1-2c1 1 3 4 3 5v3c-2 2-4 3-6 4l1-2c1-2 3-3 4-4 0-1 0-1-1-2-3 1-3 2-4 3l-2 2c-1 0-1 0-2-1h1c1-1 2-2 3-4z" class="h"></path><path d="M163 584c3 3 5 5 9 8 11 8 24 13 38 16l14 2h-3-1c-2 1-5 0-7 0h-1c-14 0-32-4-42-14-2-3-7-9-7-12z" class="V"></path><path d="M417 589c4 3 8 5 13 6 10 3 21 4 31 3 14-2 29-6 40-16 3-3 5-6 7-8-1 3-2 6-4 8-4 5-8 9-14 13l-1-1c-1 0-3 1-4 1l-9 3h0l-13 2c-16 2-35 0-48-10v-1h2z" class="k"></path><defs><linearGradient id="E" x1="336.129" y1="121.392" x2="333.141" y2="134.575" xlink:href="#B"><stop offset="0" stop-color="#c2c1c2"></stop><stop offset="1" stop-color="#f3f1f2"></stop></linearGradient></defs><path fill="url(#E)" d="M335 120l18 11c0 1 1 1 1 1l1 1c-1 0-2 0-2 1v1 1h-4-1l-6-2c-2-1-4-1-5-2-2-1-3-1-5-1l-8 1h-3-3 0c0-2 3-3 4-5 4-2 8-6 13-7z"></path><path d="M332 131c4-1 8 0 12 0 2 0 5 0 7 1h-4v2h0c-1 0-2-1-3-1-1 1-2 1-2 1-2-1-4-1-5-2-2-1-3-1-5-1z" class="U"></path><path d="M353 131c0 1 1 1 1 1l1 1c-1 0-2 0-2 1v1 1h-4-1l-6-2s1 0 2-1c1 0 2 1 3 1h0v-2h4c1 0 1 0 2-1z" class="D"></path><path d="M353 131c0 1 1 1 1 1h0c-1 1-2 1-3 0l-1 1 2 2h0-1c-1 0-3 0-4-1h0v-2h4c1 0 1 0 2-1z" class="E"></path><defs><linearGradient id="F" x1="379.111" y1="153.06" x2="468.725" y2="188.625" xlink:href="#B"><stop offset="0" stop-color="#545453"></stop><stop offset="1" stop-color="#7f7f7f"></stop></linearGradient></defs><path fill="url(#F)" d="M392 150c-1 0-2-1-2-1-2-1-3-1-4-2-1 0-2-1-3-1h0c0-1 2 0 3 0h1 1l28 12 6 3c8 5 15 9 22 14 4 3 8 7 12 10 0 1 1 2 1 2 4 3 9 8 12 13v2c-1 0-2-1-2-1l-3-4-13-12c-8-7-18-14-27-20-10-5-20-9-31-14l-1-1z"></path><defs><linearGradient id="G" x1="285.997" y1="151.809" x2="294.766" y2="161.356" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#G)" d="M277 154v-1c0-1 1-1 2-1 3-1 7-4 10-3 2 2 3 6 4 8 0 2 1 3 2 4h0c-1 1-1 3-2 4l1 1c-1 1-1 3-1 4v4l1 2h1v3l-1 2c0-1 1-3-1-4h-2 0-1v-1h-1l-1 1h1c-1 1-3 1-4 1 0-2 0-3-1-5v-6c-1-2-1-7-3-9h-2v2l-2-6z"></path><path d="M277 154l1-1 10-2 1 3c-4 1-7 2-10 4h0v2l-2-6z" class="U"></path><path d="M279 158h0c3-2 6-3 10-4l2 6h0c-2 2-6 2-7 5v2c-1-2-1-7-3-9h-2z" class="R"></path><path d="M291 160c0 2 0 4 2 5h0l1 1c-1 1-1 3-1 4v4l1 2h1v3l-1 2c0-1 1-3-1-4h-2 0-1v-1h-1l-1 1h1c-1 1-3 1-4 1 0-2 0-3-1-5v-6-2c1-3 5-3 7-5h0z" class="Q"></path><path d="M285 174c1 1 2 1 3 1 2-3 2-6 3-9h1c0 2-1 5 1 7v1l1 2h1v3l-1 2c0-1 1-3-1-4h-2 0-1v-1h-1l-1 1h1c-1 1-3 1-4 1 0-2 0-3-1-5l1 1z" class="S"></path><path d="M284 173l1 1v1h1c2 0 3 1 5-1v1l1-1v1c0 1 1 1 1 2h-2 0-1v-1h-1l-1 1h1c-1 1-3 1-4 1 0-2 0-3-1-5z" class="G"></path><path d="M279 158h2c2 2 2 7 3 9v6c1 2 1 3 1 5 1 2 1 3 2 5 0 1 1 3 1 4v6 9c1 3 0 6 0 9v17-1c-1 2-1 3-1 5v5c-1 2 0 2-1 4s-1 4-1 6v2-2-1l-2-53c-1-11-2-23-4-33v-2z" class="k"></path><path d="M347 148c1-2 1-3 1-5l1 1 1-1v1c0 1 1 1 2 2l3 3 6 3c1 1 2 2 4 3 0 1 0 1 1 1 1 1 1 2 2 3 2 2 4 4 4 7v4c1-1 0-1 1-1v-1-2-2h0c1 2 1 4 1 6l-2 1h0c-1 0-3-2-4-3-3-3-6-5-10-7l-3-2-3-3v2c-1 0-1-1-1-1v-1c-1 1-1 1-1 2v-2h-1l-1 1h-2c-1 0-1 0-2 1v5c1 0 1 1 1 2l-1 1h0-1l1 1v2c-1 0-2-1-2-2 0 0 0-2-1-2 0-1-1-2-1-3h0v-1l-1-1 1-1c0-3 3-5 4-7v-1l3-3z" class="N"></path><path d="M358 154c-1 0-2-2-3-3-1 0-2-1-3-2s-1-2-2-4l10 8h-1c-1-1-2-1-3-2 0 1 2 2 2 3z" class="B"></path><path d="M358 154c0-1-2-2-2-3 1 1 2 1 3 2h1c4 2 10 7 11 12 1 1 1 3 0 5-2-2-2-5-4-7l-3-3c-2-1-4-2-4-4h0c0-1-1-1-2-2z" class="C"></path><path d="M357 154l3 2c0 2 2 3 4 4l3 3c2 2 2 5 4 7l1 1h0c-1 0-3-2-4-3-3-3-6-5-10-7l-3-2c2-2 2-1 4-1v-1l-3-2 1-1z" class="E"></path><path d="M347 148v1c1 0 2-1 3-1 0 1 1 1 2 2h0c1 1 4 4 5 4l-1 1 3 2v1c-2 0-2-1-4 1l-3-3v2c-1 0-1-1-1-1v-1c-1 1-1 1-1 2v-2h-1l-1 1h-2c-1 0-1 0-2 1v5c1 0 1 1 1 2l-1 1h0-1l1 1v2c-1 0-2-1-2-2 0 0 0-2-1-2 0-1-1-2-1-3h0v-1l-1-1 1-1c0-3 3-5 4-7v-1l3-3z" class="B"></path><path d="M345 154h2c1 0 0 0 1 1h-1-2v1l-1-1v-1h1z" class="N"></path><path d="M352 156c-1-1-2-3-3-5v-2c1 1 2 1 3 1h0c1 1 4 4 5 4l-1 1 3 2v1c-2 0-2-1-4 1l-3-3z" class="C"></path><defs><linearGradient id="H" x1="368.878" y1="528.639" x2="427.637" y2="560.225" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#H)" d="M384 490v8c0 2 1 4 1 6l1 15c1 17 4 32 12 47 4 8 9 16 16 21l2 2h1-2v1c-2 0-3 0-4 1h-3v1 1c-5-4-9-9-14-13h2c0-1 1-1 1-2-1-2 0-4-1-6v-1h1l-1-1h1c-1-3-3-6-4-9-3-6-5-12-7-19 0 0 0-3-1-4 0-3-1-6-1-9 0-8-1-16-1-24l1-15z"></path><path d="M397 570c4 5 11 17 17 19h2 1-2v1c-2 0-3 0-4 1h-3v1 1c-5-4-9-9-14-13h2c0-1 1-1 1-2-1-2 0-4-1-6v-1h1l-1-1h1z" class="Q"></path><path d="M304 368v-1h2v-1h1c0 2 1 3 1 4h1c1 1 2 1 3 1v1h0c1 1 2 2 3 2h0l2 1c1 1 2 1 2 3h0 1 1v6c0 1 1 2 1 3-1 1 0 2 0 3v5h0v2h0c-3-1-4-1-5-3l-1-1v1h-1-1c-2-1-3-1-4-2l-1 1 1 1c-2-1-4-2-6-4l-1-1c-2-2-3-4-5-7h0l-3-6c2 1 2 3 3 4h0v-1s0-1-1-1v-3-1-2c1-1 2-1 3-2 0-1 1-1 1-1h1 1s0-1 1-1z" class="c"></path><path d="M309 391h1c-2-2-2-5-2-7 2 2 2 4 3 6 0 1-1 1-1 2 0 0-1 0-1-1z" class="b"></path><path d="M303 389l2-1c1 1 1 0 1 1 1 0 2 1 3 2 0 1 1 1 1 1 0-1 1-1 1-2l1 1c1 2 1 2 2 3-2-1-3-1-4-2l-1 1 1 1c-2-1-4-2-6-4l-1-1z" class="B"></path><path d="M302 369h1s0-1 1-1v2c1 1 1 2 2 3l1 3h0 0c1 1 1 0 1 1h0c-1 1-1 1-1 3h0v3c1 1 0 2 0 3v1c-1-1-1-2-1-2l-1-1v-1c0-1 1-1 1-1v-2h0v-3c-1-2-3-4-4-7v-1z" class="V"></path><path d="M311 382c1 2 1 5 3 6 0 1 1 3 2 4h1l3 2c1 1 1 1 2 1h0v2h0c-3-1-4-1-5-3l-1-1v1h-1-1c-1-1-1-1-2-3h1v-1c-1-1-1-2-1-3-1-2-2-3-3-5h2z" class="M"></path><path d="M307 376h2v2h1c1-1 1-1 2-1s2 1 2 1c1 1 2 1 2 2h-1v2l-2-2v2l1 1v2l1 1c-1 1-1 1-1 2-2-1-2-4-3-6h-2c0-1-1-2-2-2 0-2 0-2 1-3h0c0-1 0 0-1-1h0z" class="K"></path><path d="M311 382c0-1 0-1-1-2 0 0-1 0-1-1l1-1h1c1 1 2 1 3 2h-1v2l1 1v2l1 1c-1 1-1 1-1 2-2-1-2-4-3-6z" class="R"></path><path d="M304 368v-1h2v-1h1c0 2 1 3 1 4h1c1 1 2 1 3 1v1h0c1 1 2 2 3 2h0l2 1-3 3s-1-1-2-1-1 0-2 1h-1v-2h-2 0l-1-3c-1-1-1-2-2-3v-2z" class="U"></path><path d="M304 370c2 0 2 1 4 1l1 1v1h-2-1c-1-1-1-2-2-3z" class="R"></path><path d="M307 376c1-1 1-2 2-2v1l2 1 1-1-1-3h1c1 1 2 2 3 2h0l2 1-3 3s-1-1-2-1-1 0-2 1h-1v-2h-2 0z" class="J"></path><path d="M312 377v-3h1c0 1 0 1 1 2v-1h0l1-1 2 1-3 3s-1-1-2-1z" class="R"></path><path d="M317 375c1 1 2 1 2 3h0 1 1v6c0 1 1 2 1 3-1 1 0 2 0 3v5c-1 0-1 0-2-1l-3-2h-1c-1-1-2-3-2-4s0-1 1-2l-1-1v-2l-1-1v-2l2 2v-2h1c0-1-1-1-2-2l3-3z" class="U"></path><path d="M317 388l1-1v1c1 1 3 2 3 4v1h0v1h-1l-3-2v-2h1 0c0-1 0-1-1-2z" class="J"></path><path d="M314 383l1 2h1 2 1l1 1c-1 1-1 1-2 1l-1 1c1 1 1 1 1 2h0-1v2h-1c-1-1-2-3-2-4s0-1 1-2l-1-1v-2z" class="I"></path><path d="M315 386c0 1 1 2 1 3-1 0-1-1-2-1 0-1 0-1 1-2zm2 2c0-1 0-2 1-3h1l1 1c-1 1-1 1-2 1l-1 1z" class="F"></path><path d="M317 375c1 1 2 1 2 3h0 1l1 1-2 2h-1s0 1 1 2h0c-1 0-1 0-1 1v1h-2-1l-1-2-1-1v-2l2 2v-2h1c0-1-1-1-2-2l3-3z" class="d"></path><path d="M313 380l2 2c1 1 1 1 1 3h-1l-1-2-1-1v-2z" class="M"></path><path d="M297 374v-2c1-1 2-1 3-2 0-1 1-1 1-1h1v1c1 3 3 5 4 7v3h0v2s-1 0-1 1v1c-1 0-1 1-1 2h1v2l-2 1c-2-2-3-4-5-7h0l-3-6c2 1 2 3 3 4h0v-1s0-1-1-1v-3-1z" class="a"></path><path d="M301 377l1-1c2 1 3 2 4 4h0v2s-1 0-1 1v1c-1 0-1 1-1 2h-1c-1 0-2-2-3-2l4-4-3-3z" class="J"></path><path d="M297 374v-2c1-1 2-1 3-2 0-1 1-1 1-1h1v1c1 3 3 5 4 7v3c-1-2-2-3-4-4l-1 1-1 2h-1c-1 0-1-3-2-4h0v-1z" class="P"></path><path d="M300 379l-1-1v-3c0-1 0-1 1-1s0 1 1 2h1l-1 1-1 2z" class="Q"></path><path d="M306 377l-1 1-4-4v-3l1-1c1 3 3 5 4 7z" class="J"></path><path d="M324 139v-1c1-1 3-1 4-1 2-1 3-2 5-2h0 1l1 1c2 1 8 1 10 3 0 2-2 3-2 5 2-2 3-4 5-5 0 3-5 8-7 12h0c0 2-1 4-2 6 1 0 1 0 1-1v-1h1l3-4v1c-1 2-4 4-4 7l-1 1 1 1v1h0c0 1 1 2 1 3 1 0 1 2 1 2 0 1 1 2 2 2l2 2c1 1 2 1 2 2-2 1-3 2-5 3h0l-2 3c0 1-1 1-1 2v2h-1v-1h-2v-1c-1 0-1-1-2-2h-1v-4-26c-1-2-2-2-4-2l-1 1h-1v-1l-1-1v-2c-1 0-1 0-1-1l-1-2-1-2h0z" class="B"></path><path d="M335 179v-2h0c1 1 3 2 3 4h-1c-1 0-1-1-2-2z" class="f"></path><path d="M324 139c2 0 3 0 4-1 2 0 4 0 6-1v7 5c-1-2-2-2-4-2l-1 1h-1v-1l-1-1v-2c-1 0-1 0-1-1l-1-2-1-2z" class="F"></path><path d="M325 141c1-1 2-1 3-1 1 1 1 1 1 2-1 0-1 0-1 1 0 0 0 1-1 1s-1 0-1-1l-1-2z" class="S"></path><path d="M329 142s0 1 1 2c0-1 1-2 2-3 1 0 1 0 1 1l1 2v5c-1-2-2-2-4-2l-1 1h-1v-1l-1-1v-2c1 0 1-1 1-1 0-1 0-1 1-1z" class="i"></path><defs><linearGradient id="I" x1="336.941" y1="164.574" x2="345.487" y2="165.136" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#I)" d="M341 151h0c0 2-1 4-2 6 1 0 1 0 1-1v-1h1l3-4v1c-1 2-4 4-4 7l-1 1 1 1v1h0c0 1 1 2 1 3 1 0 1 2 1 2 0 1 1 2 2 2l2 2c1 1 2 1 2 2-2 1-3 2-5 3h0c-1-1-2-2-3-2v-1c0-1-3-4-3-5-1-4 1-8 2-12 1-2 1-3 2-5z"></path><path d="M340 173c1 0 2 1 3 1h0c-1-2-2-2-2-3 1 0 3 1 4 1s1 0 1-1c1 1 2 1 2 2-2 1-3 2-5 3h0c-1-1-2-2-3-2v-1z" class="O"></path><path d="M358 342l1-1c2 0 4-1 5-1 2-1 3-2 5-3h1c1 2 2 5 2 7s1 5 0 7v1 1h2v8-6c-1 0-1 0-1 1-2 3-4 7-7 10-4 3-8 6-14 6l-5-1c-3 0-5-1-8-3-1-1-3-3-4-5h-1 0c0-1-1-2-1-3v-2l1-1c1 0 2-1 3-1h3 0 1 1v1c0 1 0 1-1 1l-1 1c-1 0-1 1 0 1v1s1 0 1 1h3v-1h-1c1-1 2-1 3-1 0-1 1 0 1 0 1 1 1 2 3 3 1 1 3 1 5 0s6-4 7-7c2-5-2-10-4-14z" class="Z"></path><path d="M364 342h1v1c0 1 0 1-1 2h-1c-1 0-1 0-1-1v-1c1 0 1-1 2-1z" class="U"></path><path d="M334 363v-1-1c2 0 3 0 4 1-1 1-2 1-3 1h-1 0z" class="L"></path><path d="M368 345h1c1 1 1 2 2 3 0 1 1 2 0 3l-1-1h0-2c-1-1-2-1-2-3 0-1 1-1 2-2zm-29 23v-1c0-1 0-1 1-1v-2c0 2 0 2 1 2v1c2 0 2 0 3 1s2 1 3 2v1c-3 0-5-1-8-3z" class="D"></path><path d="M338 362c4 1 5 2 7 4v1s-1-1-2-1c-1-1-1-2-3-2v2c-1 0-1 0-1 1v1c-1-1-3-3-4-5 1 0 2 0 3-1z" class="O"></path><path d="M366 366c0-2 1-4 0-6 0-3 3-4 4-7h0 1c1 0 1 1 2 2h1c-1 0-1 0-1 1-2 3-4 7-7 10z" class="D"></path><path d="M457 187l1-1a30.44 30.44 0 0 1 8 8c9 9 17 19 24 30 3 5 6 12 10 17 3 6 5 12 8 18 5 14 11 27 15 41l5 23 3 13c0 2 1 4 1 6v3h-1 0c0-1 0-1-1-2l-5-24c-6-21-13-42-22-62-4-8-7-16-12-24l-22-31v-2c-3-5-8-10-12-13z" class="k"></path><path d="M469 200c1 1 2 2 3 4 2 2 5 4 7 7 3 5 12 16 12 22l-22-31v-2z" class="h"></path><defs><linearGradient id="J" x1="369.127" y1="314.747" x2="374.481" y2="324.435" xlink:href="#B"><stop offset="0" stop-color="#848183"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#J)" d="M421 288c-3 7-9 12-14 17l-22 14c-4 3-9 6-13 8-6 2-13 3-19 6-4 2-8 5-12 7-2 1-5 2-7 3h-1v-4c0-1 0-2 1-4h2c-1 0-1 0-2-1h1-1v-1l7-2-4-1c-1 1-2 1-3 2h-1v-7l1 1h6l3-1 13-2h3l2-1 6-2v1s1 0 0 1h1l9-3c3-1 7-3 10-5 13-7 24-15 34-26z"></path><path d="M367 320v1s1 0 0 1h1c-3 1-5 1-7 2l-2-1 2-1 6-2z" class="h"></path><path d="M339 334h2 0c2-1 3-1 4-1h1c1 1 1 1 0 2-2 1-3 1-6 1v-1l-1-1z" class="d"></path><path d="M339 334l1 1v1c0 1-2 2-2 2-1 1-4 1-5 1 0-1 0-2 1-4h2c1 0 2 0 3-1z" class="W"></path><defs><linearGradient id="K" x1="346.43" y1="329.134" x2="353.621" y2="320.788" xlink:href="#B"><stop offset="0" stop-color="#b4b4b6"></stop><stop offset="1" stop-color="#dddbdb"></stop></linearGradient></defs><path fill="url(#K)" d="M356 323h3l2 1-13 3-8 1 1-1h1l-1-1h-1l3-1 13-2z"></path><path d="M333 325l1 1h6 1l1 1h-1l-1 1 8-1 1 1h0 0l-3 2c-2 1-4 1-5 1l-4-1c-1 1-2 1-3 2h-1v-7z" class="M"></path><path d="M337 330c3 0 5-1 8-1l1 1c-2 1-4 1-5 1l-4-1z" class="D"></path><path d="M333 325l1 1h6 1l1 1h-1l-1 1h-6c-1 1 0 3 0 4h-1v-7z" class="F"></path><path d="M333 343h1c-1 1-1 1-1 2l-1 1c-5 1-11 4-15 7-10 5-19 11-28 18-10 8-19 18-27 28-4 5-8 10-11 15-4 6-7 13-10 19l-1 4c-1 1-1 2-1 4-1 1-1 2-1 3 0 2-1 6 0 8v1 1c-1-1-1-3-2-5 0-4-1-8 0-12 1-3 3-7 4-10 0-2 0-5 1-7 2-7 6-15 11-21 2-3 4-6 7-8 1-1 3-2 4-3s1-3 2-3l1-1 1-1c2-2 5-6 8-7h2c0 1 0 1-1 1l-3 3c3-2 5-3 7-4l4-3 16-12 11-7 10-6c3-2 7-4 11-5h1z" class="I"></path><path d="M255 399c2 0 4-2 6-3-1 2-2 3-3 4-2 4-5 6-7 10 0 1-2 4-3 5 0-1 1-2 1-3 0 0 0-1 1-1 0-1 0-1 1-2l1-2 1-1v-2c0-2 2-3 4-4h-3l1-1z" class="F"></path><path d="M273 380c3-2 5-3 7-4-2 3-4 5-7 7-4 4-8 9-12 13-2 1-4 3-6 3l2-2h1l1-1h1l1-1h0c1 0 1-1 1-2v1h-1l12-14z" class="Y"></path><defs><linearGradient id="L" x1="249.467" y1="399.249" x2="264.668" y2="405.72" xlink:href="#B"><stop offset="0" stop-color="#bfbdbe"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#L)" d="M241 420c2-7 6-15 11-21 2-3 4-6 7-8 1-1 3-2 4-3s1-3 2-3l1-1 1-1c2-2 5-6 8-7h2c0 1 0 1-1 1l-3 3-12 14h1v-1c0 1 0 2-1 2h0l-1 1h-1l-1 1h-1l-2 2-1 1h3c-2 1-4 2-4 4v2l-1 1-1 2c-1 1-1 1-1 2-1 0-1 1-1 1 0 1-1 2-1 3-2 5-7 12-7 18l-1 4c-1 1-1 2-1 4-1 1-1 2-1 3 0 2-1 6 0 8v1 1c-1-1-1-3-2-5 0-4-1-8 0-12 1-3 3-7 4-10 0-2 0-5 1-7z"></path><defs><linearGradient id="M" x1="367.537" y1="172.443" x2="399.223" y2="195.189" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#666665"></stop></linearGradient></defs><path fill="url(#M)" d="M386 148c2 0 4 2 6 2l1 1-1 1v1c-1 4-2 8-3 11-2 9-2 17-3 25-1 6-1 14-2 20 0 4 0 9-1 13v-3l-6-2v-6h0c-1-3 0-6 0-9 0-8-1-17 0-26 0-2 0-4 1-6 0-2 1-3 0-6-1-1 0-4 0-5h0 1v-1l3-4-1-1c1-1 2-3 2-4l3-1z"></path><path d="M386 148c2 0 4 2 6 2l1 1-1 1c-1-1-2-1-4-1h0-1c-1-1-2-1-4 0 0 0-1 2-1 3l-1-1c1-1 2-3 2-4l3-1z" class="B"></path><path d="M384 209l-1-16c1-10 1-20 4-30 1-4 2-7 5-10-1 4-2 8-3 11-2 9-2 17-3 25-1 6-1 14-2 20z" class="L"></path><path d="M310 134v1l-1 1h0c1 1 1 2 3 2-6 2-13 3-19 5-7 2-15 6-23 9-9 4-19 8-28 14-10 5-19 12-28 20l-6 6c-8 7-15 15-21 23s-10 16-15 24c-2 3-3 6-5 10-6 13-12 27-17 41l-3 10c0 1 0 3-1 4 0 0-1 1-2 1 1-5 3-11 4-16l1-3 21-50c9-17 21-33 35-46 11-11 24-19 37-27 4-2 8-5 12-7 3-2 6-3 9-4 7-3 14-6 21-8l16-5c3 0 8-3 10-5z" class="H"></path><path d="M333 364v2h1c1 1 2 2 2 3 1 1 3 2 4 3l6 3h7l1 1h-1c-1 1-1 1-1 2-1 1-2 2-3 2s-2 1-3 1c2 1 4 0 6 1-4 0-6 1-8 4l-3 2c0 2-1 2-1 3 1 1 2 1 2 2h-3 0c-1-1 0-1-1-1h0 0c0 1-1 1-1 2-1 2-1 3-1 5h0 1v1l-1 2c0 1 0 1-1 2l-2-1-1 1h0-1l-3-1v-3h0c-1-1-2-2-2-3-1-1-1-3-1-4-1-1-1-4-1-7l-2 2c0 2 2 4 0 7h0v-5c0-1-1-2 0-3 0-1-1-2-1-3v-6h-1-1 0c0-2-1-2-2-3l-2-1h0c-1 0-2-1-3-2h0v-1c-1 0-2 0-3-1l2-2c1 1 3 2 4 2l1 1c3 0 6 0 9-1 2-1 4-2 6-4l2-2z" class="N"></path><path d="M337 378h0c-1-2-1-4-2-6 1 0 1 0 2 1l1 2c0 1-1 2-1 3h0z" class="L"></path><path d="M315 374c1 0 1 0 3 1 0 0 2-1 3-1v4h0-1-1 0c0-2-1-2-2-3l-2-1h0z" class="Z"></path><path d="M340 372l6 3c-1 1-3 1-4 2l-2-2h-1v3l-1 1c0-1-1-1-1-1h0c0-1 1-2 1-3l-1-2s1 0 1-1h2z" class="a"></path><path d="M324 385v-12c2-1 3 0 4-1s2-2 5-2h0c1 4 0 7 0 10s1 6 1 9v2l-1 2h0c-1-1-1-2-1-3-1 1-1 3-1 5v1h-1l-2 4h0c-1-1-2-2-2-3-1-1-1-3-1-4l-1-8z" class="I"></path><path d="M331 379c0 1 1 2 1 3-2 0-2-1-4 0l3-3z" class="F"></path><path d="M326 376v-1c1-1 3-2 5-2h1c0 2 0 4-1 6 0-1 0-2-1-3h-1c0-1-1-1-2-1l-1 1h0z" class="G"></path><path d="M326 376l1-1c1 0 2 0 2 1h1c1 1 1 2 1 3l-3 3v3l-1 1v-5c0-2 0-4-1-5z" class="i"></path><path d="M326 376l1-1c1 0 2 0 2 1h1-1c-1 0-1 1-1 1 0 2 0 2-1 4 0-2 0-4-1-5z" class="T"></path><path d="M326 376h0c1 1 1 3 1 5v5c-1 0-1 1-1 2v1c0 2 1 5 1 7h0c1 2 1 2 1 4-1-1-2-2-2-3-1-1-1-3-1-4l-1-8c2-2 1-4 1-6h0v-1c1-1 1-1 1-2z" class="F"></path><path d="M328 385v2l1 1 2-3c0 2-1 5 0 7 0 1-1 1-1 1v3l-2 4h0c0-2 0-2-1-4h0c0-2-1-5-1-7v-1c0-1 0-2 1-2l1-1z" class="G"></path><path d="M326 389l1-1c1 1 0 2 1 2 1 1 1 0 1 1v1c0 1 0 2 1 3-1 1-1 1-2 1h-1c0-2-1-5-1-7z" class="g"></path><defs><linearGradient id="N" x1="333.987" y1="402.219" x2="348.006" y2="374.164" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#7b7a7b"></stop></linearGradient></defs><path fill="url(#N)" d="M346 375h7l1 1h-1c-1 1-1 1-1 2-1 1-2 2-3 2s-2 1-3 1c2 1 4 0 6 1-4 0-6 1-8 4l-3 2c0 2-1 2-1 3 1 1 2 1 2 2h-3 0c-1-1 0-1-1-1h0 0c0 1-1 1-1 2-1 2-1 3-1 5h0 1v1l-1 2c0 1 0 1-1 2l-2-1-1 1h0-1l-3-1v-3l2-4h1v-1c0-2 0-4 1-5 0 1 0 2 1 3h0l1-2v-2l3-5c0-1 1-1 1-2 1-2 2-3 4-5 1-1 3-1 4-2z"></path><path d="M333 403h-1c-1-1 0-4 0-5v4c1-1 1-1 2-1l2 1c0 1 0 1-1 2l-2-1z" class="O"></path><path d="M337 384c1 1 1 1 1 2v1c-1 1-2 2-2 4h-1-1v-2l3-5z" class="W"></path><path d="M331 395c0-2 0-4 1-5 0 1 0 2 1 3h0l1-2h1c0 3-1 6-2 8l-1-5h0l-1 1z" class="K"></path><path d="M331 396h1v2c0 1-1 4 0 5h1l-1 1h0-1l-3-1v-3l2-4h1z" class="j"></path><path d="M132 239c1-2 1-3 2-5v-9-2c0-2 1-3 2-5 2-1 2-1 4-1h0c0 3 1 6-1 7 0 1-1 1-2 1v1c0 1-1 1-1 1-1 0-1 1-1 1v1 6c0 6 0 12 1 17 0-3-1-7 0-10h4v1 12c0 1 1 1 1 2v14 7l-1-6v5c0 3 0 6-1 8 0 1 1 2 0 3v5h0v9c-1 2 0 3-1 4v1l-2 8v11c0 2 0 6 1 9v2c-1 6-2 12-4 17v-8l-1-13v-60-34zm164 157c0-3 2-3 2-6 1-1 0-2-1-2 1 0 1-1 2-1l1-1v2h0c0 1 0 1 1 2h3c2 2 4 3 6 4l-1-1 1-1c1 1 2 1 4 2h1 1v-1l1 1c1 2 2 2 5 3h0v-2c2-3 0-5 0-7l2-2c0 3 0 6 1 7 0 1 0 3 1 4 0 1 1 2 2 3h0v3l3 1 2 2c1 5 1 11 1 16 0 2-1 4 0 7-1 0-1 1-1 2v1 2c-1 0-2-1-3-2h0c-1-1-2-3-4-4v-1l-3-4c-4-3-8-7-12-10-5-4-13-9-15-15v-2z" class="g"></path><path d="M323 423l3 1 4 4c-2 0-2 0-4-1l-3-4z" class="V"></path><path d="M326 427c2 1 2 1 4 1 1 1 2 2 3 4v2c-1 0-2-1-3-2h0c-1-1-2-3-4-4v-1z" class="B"></path><path d="M322 395c2-3 0-5 0-7l2-2c0 3 0 6 1 7 0 1 0 3 1 4 0 1 1 2 2 3h0v3l-18-9-1-1 1-1c1 1 2 1 4 2h1 1v-1l1 1c1 2 2 2 5 3h0v-2z" class="a"></path><defs><linearGradient id="O" x1="307.648" y1="416.09" x2="312.324" y2="404.703" xlink:href="#B"><stop offset="0" stop-color="#6f746e"></stop><stop offset="1" stop-color="#8d878d"></stop></linearGradient></defs><path fill="url(#O)" d="M296 396c2 2 4 5 6 7l9 6 15 15-3-1c-4-3-8-7-12-10-5-4-13-9-15-15v-2z"></path><path d="M490 595c6-4 10-8 14-13 0 4-3 9-5 11-11 13-27 16-43 17-21 2-42 3-63 9-15 3-28 10-38 21-7 9-12 18-16 29-1 1-2 3-3 5l2-8 1-4c5-14 14-27 26-35 3-2 6-3 8-4v-1-1c5-3 13-7 20-7 0-1 2-1 2-1l6-1 7-2c7-2 15-3 22-3 9-1 18-1 27-2 5-1 11-2 16-5 2 0 2 0 3-2h0 0l9-3c1 0 3-1 4-1l1 1z" class="H"></path><path d="M395 613l6-1c0 1 1 1 1 1l-7 2h-1l1-2z" class="G"></path><path d="M393 614c0-1 2-1 2-1l-1 2h1l-7 2-15 6v-1-1c5-3 13-7 20-7z" class="F"></path><path d="M393 614c0-1 2-1 2-1l-1 2h1l-7 2h0v-1h0c1-1 3-1 4-2h1z" class="S"></path><path d="M408 610c7-2 15-3 22-3 2 1 5 0 7 0l1 1h0c-1 0-1 1-2 1l-34 4s-1 0-1-1l7-2z" class="k"></path><defs><linearGradient id="P" x1="465.366" y1="608.129" x2="457.522" y2="594.793" xlink:href="#B"><stop offset="0" stop-color="#b7b6b5"></stop><stop offset="1" stop-color="#eae8ed"></stop></linearGradient></defs><path fill="url(#P)" d="M476 598h0l9-3c1 0 3-1 4-1l1 1c-6 4-14 7-22 10-6 2-13 3-20 3-4 1-8 1-12 1 1 0 1-1 2-1h0l-1-1c-2 0-5 1-7 0 9-1 18-1 27-2 5-1 11-2 16-5 2 0 2 0 3-2h0z"></path><defs><linearGradient id="Q" x1="201.994" y1="89.546" x2="215.28" y2="79.108" xlink:href="#B"><stop offset="0" stop-color="#747474"></stop><stop offset="1" stop-color="#929192"></stop></linearGradient></defs><path fill="url(#Q)" d="M184 51c1-1 2-1 3-2 0 2 0 3 1 5l1 8c3 11 9 22 18 29 6 4 14 7 21 9l22 3c1 0 3 0 5 1h-12-5c-1-1-3-1-4-1h-3c1 1 1 1 1 2l-4-1c-4 0-8-1-12-2 6 3 11 3 17 5 3 1 7 3 11 4h0-5l-8-1-12-3v2c-2 0-4-2-5-2-2-1-2-1-3-2h-1 0l-1 2h-1c-1-1-1 0-2 0s-1-1-1-2v-1c-1 0-2 0-2-1h0v-1c-1-1-2-1-4-2l1-1 1-1-2-1c0-1-2-2-2-2l-5-5-1-1c-4-3-8-9-8-14-1-1-2-2-2-3 0-2 1-5-1-7l-6 9c0-2 0-2 1-3 0-1 1-1 1-2h0c-1 1-2 2-3 2h-1l-3 3h0 0c1-2 2-2 2-4h-2c1-1 2-3 4-5 1-2 2-3 4-5 1-2 4-4 4-5l-1-1c0-1 3-3 4-3z"></path><path d="M228 102l6 1h-3c1 1 1 1 1 2l-4-1h0l-1-1h1-1l1-1z" class="i"></path><path d="M187 74l3 3c0 1 1 1 2 2v-2c6 11 17 20 29 24l7 1-1 1h1-1l1 1h0c-4 0-8-1-12-2-1-1-4-1-5-2-3-2-5-3-7-5 0-1-1-1-1-2l-1-1-1-1c0-1-2-2-2-3h-1c0-1-1-1-1-2h-1v-1c-1-1-3-4-4-5s-2-1-3-3c-1-1-1-1-2-3z" class="Y"></path><path d="M184 51h2c1 1 0 3 1 5v1c1 1 0 3 0 4v3 4c1 3 3 7 5 9v2c-1-1-2-1-2-2l-3-3c1 2 1 2 2 3 1 2 2 2 3 3s3 4 4 5v1h1c0 1 1 1 1 2h1c0 1 2 2 2 3l1 1 1 1c0 1 1 1 1 2-3-2-7-5-9-9-1-1-1-2-2-3l-8-8h0c-1-2-1-3-1-5-1-1-3-4-2-5v-2l-2 2-6 9c0-2 0-2 1-3 0-1 1-1 1-2h0c-1 1-2 2-3 2h-1l-3 3h0 0c1-2 2-2 2-4h-2c1-1 2-3 4-5 1-2 2-3 4-5 1-2 4-4 4-5l-1-1c0-1 3-3 4-3z" class="G"></path><path d="M182 61l2-1h0c1 2 2 5 3 8s3 7 5 9v2c-1-1-2-1-2-2l-3-3c-2-3-5-9-5-13z" class="i"></path><path d="M184 51h2c1 1 0 3 1 5v1c1 1 0 3 0 4v3 4l-3-8h0l-2 1-4 4c-1 1-1 1-2 1h-3v1-2c1-2 2-3 4-5 1-2 4-4 4-5l-1-1c0-1 3-3 4-3zm-4 14l2-2v2c-1 1 1 4 2 5 0 2 0 3 1 5h0l8 8c1 1 1 2 2 3 2 4 6 7 9 9 2 2 4 3 7 5 1 1 4 1 5 2 6 3 11 3 17 5 3 1 7 3 11 4h0-5l-8-1-12-3v2c-2 0-4-2-5-2-2-1-2-1-3-2h-1 0l-1 2h-1c-1-1-1 0-2 0s-1-1-1-2v-1c-1 0-2 0-2-1h0v-1c-1-1-2-1-4-2l1-1 1-1-2-1c0-1-2-2-2-2l-5-5-1-1c-4-3-8-9-8-14-1-1-2-2-2-3 0-2 1-5-1-7z" class="I"></path><path d="M201 98l4 3-2 1c-1-1-2-1-4-2l1-1 1-1z" class="E"></path><path d="M205 101l5 2-4 1-1 1v-1c-1 0-2 0-2-1h0v-1l2-1z" class="X"></path><path d="M210 103l9 4v2c-2 0-4-2-5-2-2-1-2-1-3-2h-1 0l-1 2h-1c-1-1-1 0-2 0s-1-1-1-2l1-1 4-1z" class="j"></path><path d="M193 88c4 3 8 8 12 10l2 2h0c-2-1-4-2-5-3-1 0-2-1-2-1l-1 1c0-1-2-2-2-2l-5-5v-1h1v-1z" class="Z"></path><path d="M180 65l2-2v2c-1 1 1 4 2 5 0 2 0 3 1 5h0c1 4 5 9 8 13v1h-1v1l-1-1c-4-3-8-9-8-14-1-1-2-2-2-3 0-2 1-5-1-7z" class="K"></path><path d="M192 89c-2-3-5-6-6-10-1-1-2-2-2-4h1 0c1 4 5 9 8 13v1h-1z" class="P"></path><path d="M321 613v-2h1l1 3 1 1 1-1v2l1-1 1 2c1 0 2 0 2-1v1l2 2c0 1 0 3-1 4h0c-1 1-1 2-2 2v2h1v1c1 0 2 0 2 1l1 1c-1 0-1 0-2 1 1 1 1 2 1 3v3h-1c0 2 1 3 2 4h1c0 2 0 3 1 5h0 1v3l-1 1 1 6v2c1 1 1 3 3 3h0c1 0 1 0 1 1l-1 4-2 8-2 7v3h-1l-1-9c-2-10-6-19-11-27h-1c0 2 0 2-1 3l-7-10v-3l-4-5v-2l-1-1c-1-1-1-1-1-2-1 0-1-1-2-2s-1-1-2-3c0-2 1-4 0-6l-2-2v-1h0l-4-2c2 0 3 0 4 1 2 1 5 3 7 3l1-1v-1h1c1-1 2 0 3-1v-1c1 0 2 0 4 1h-1l1 1h0c1-1 1-2 1-3h1v2l2-2c0 1 0 1 1 2z" class="h"></path><path d="M312 638c2 2 3 5 5 7h1v-1c1 1 3 3 3 4h-1c0 2 0 2-1 3l-7-10v-3z" class="I"></path><path d="M333 654c1 0 1 0 1-1v-3l1 6v2c1 1 1 3 3 3h0c1 0 1 0 1 1l-1 4-2 8-2 7v3h-1l-1-9c0 1 1 2 1 3v1h1v-4c1-3 1-5 1-8 0-2-1-5-1-8 0-1-1-3-1-5z" class="F"></path><path d="M335 658c1 1 1 3 3 3h0c1 0 1 0 1 1l-1 4c-1-2-2-2-2-4-1-1-1-3-1-4z" class="Q"></path><defs><linearGradient id="R" x1="332.718" y1="650.26" x2="316.086" y2="620.525" xlink:href="#B"><stop offset="0" stop-color="#1e1e1f"></stop><stop offset="1" stop-color="#383737"></stop></linearGradient></defs><path fill="url(#R)" d="M310 621l1-1c1 1 2 2 3 2 3 1 6 5 8 6 1 2 2 3 3 4l3 3 2 2c0 2 1 3 2 4h1c0 2 0 3 1 5h0 1v3l-1 1v3c0 1 0 1-1 1 0-2-1-5-2-7h-1l-1-1c-4-2-6-7-9-11l-1-1c0-1-1-2-1-3l-1-3-7-7z"></path><path d="M317 628c2 2 6 6 8 9-1 0-2-1-3-2l-2-2h0l1 1-1 1-1-1c0-1-1-2-1-3l-1-3z" class="F"></path><path d="M320 635l1-1-1-1h0l2 2c1 1 2 2 3 2 2 3 4 7 6 10h-1l-1-1c-4-2-6-7-9-11z" class="G"></path><path d="M300 614c3 2 6 4 9 7h0 1l7 7 1 3c0 1 1 2 1 3h0s0 1-1 1c0 1-2 2-3 2s-6-5-7-6l-1-1c-1-1-1-1-1-2-1 0-1-1-2-2s-1-1-2-3c0-2 1-4 0-6l-2-2v-1z" class="Y"></path><path d="M309 621h1l7 7 1 3-1-1h0l-1-1c-1-2-1-2-3-3h0v-1h-1c-2-1-2-2-3-4z" class="S"></path><path d="M321 613v-2h1l1 3 1 1 1-1v2l1-1 1 2c1 0 2 0 2-1v1l2 2c0 1 0 3-1 4h0c-1 1-1 2-2 2v2h1v1c1 0 2 0 2 1l1 1c-1 0-1 0-2 1 1 1 1 2 1 3v3h-1l-2-2-3-3c-1-1-2-2-3-4-2-1-5-5-8-6-1 0-2-1-3-2l-1 1h-1 0c-3-3-6-5-9-7h0l-4-2c2 0 3 0 4 1 2 1 5 3 7 3l1-1v-1h1c1-1 2 0 3-1v-1c1 0 2 0 4 1h-1l1 1h0c1-1 1-2 1-3h1v2l2-2c0 1 0 1 1 2z" class="M"></path><path d="M309 614h2v1 1h-2l-1-1v-1h1zm14 12l-1-1v-1h3v2h-2z" class="S"></path><path d="M329 617l2 2c0 1 0 3-1 4h-1c0-1-1-2-1-3-1 1-1 2-1 2h-1c0-1 0-1 1-3l2-2z" class="C"></path><path d="M326 625l1-1h1v1 2h1v1c1 0 2 0 2 1l1 1c-1 0-1 0-2 1 1 1 1 2 1 3v3h-1l-2-2-3-3c0-2-1-4-2-6h2v-2l1 1z" class="S"></path><path d="M328 627l-1 1h0c-1-1-1-2 0-3l1-1v1 2z" class="T"></path><path d="M330 631h-2l1-2h2l1 1c-1 0-1 0-2 1z" class="M"></path><path d="M325 624l1 1c0 1-1 2-1 3l2 2c0 1 1 3 2 4h0c-1 0-1 1-1 1l-3-3c0-2-1-4-2-6h2v-2z" class="c"></path><path d="M321 613v-2h1l1 3 1 1 1-1v2l1-1 1 2c1 0 2 0 2-1v1l-2 2c-2 2-3 4-5 5-4-1-6-3-9-5l-2-1v-1l1-1-1-1v-1h-2c1-1 2 0 3-1v-1c1 0 2 0 4 1h-1l1 1h0c1-1 1-2 1-3h1v2l2-2c0 1 0 1 1 2z" class="Y"></path><path d="M318 611v2 3l1-1 1 1h-1l-1 1v-1c-1 0-1-1-1-1h-1c-1 1 1 3-1 3h-1l-1 1-2-1v-1l1-1-1-1v-1h-2c1-1 2 0 3-1v-1c1 0 2 0 4 1h-1l1 1h0c1-1 1-2 1-3h1z" class="Q"></path><path d="M312 612c1 0 2 0 4 1h-1c0 1-1 1-1 2v2s-1 0-2 1l-1-1 1-1-1-1v-1h-2c1-1 2 0 3-1v-1z" class="R"></path><path d="M321 613v-2h1l1 3 1 1 1-1v2l-2 2v1c-1 1-2 1-3 1-2-1-2-2-2-3l1-1h1l-1-1-1 1v-3l2-2c0 1 0 1 1 2z" class="X"></path><path d="M320 616h1v2h-2v-2h1z" class="e"></path><path d="M321 613v-2h1l1 3 1 1 1-1v2l-2 2v1-2c-2-2-2-3-2-4z" class="K"></path><path d="M375 334s1-1 2-1c1-1 4-2 5-2v3l1 14v101h0c0 2 0 4-1 5-1-1-2-1-3-3-1 0-2 0-2-1l-1-1v2c-1 6-2 10-5 14h-1l1-1c2-4 2-8 3-13 1-8 0-18 0-26v-64-8-16h0c0-1 0-2 1-3z" class="U"></path><path d="M375 334c0 1 1 0 1 1v15 25 63 13c-1 6-2 10-5 14h-1l1-1c2-4 2-8 3-13 1-8 0-18 0-26v-64-8-16h0c0-1 0-2 1-3z" class="i"></path><defs><linearGradient id="S" x1="59.295" y1="146.699" x2="144.912" y2="182.986" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#909090"></stop></linearGradient></defs><path fill="url(#S)" d="M76 132h61 0-21-9v1c1 0 1 0 1 1s1 3 2 3c0 0 1 0 1 1v-1 1h1l8 3h0c4 2 6 6 9 10 1 0 1 1 1 1 1 1 3 5 4 5v1c1 1 2 5 3 6l2 8c0 1 0 2 1 3 0 3 0 6-1 9 1 1 1 2 1 3v7 1c1 1 1 1 0 2l1 1v6 11c0 2 1 5 1 8-1 1-1 3-2 4s-3 2-4 3v2 10c-1 3 0 7 0 10-1-5-1-11-1-17v-6-1s0-1 1-1c0 0 1 0 1-1v-1c1 0 2 0 2-1 2-1 1-4 1-7h0c-2 0-2 0-4 1-1 2-2 3-2 5v2 9c-1 2-1 3-2 5v-19l-1-20c-2-19-6-38-20-52-8-8-19-12-30-13-7-1-13 0-19 0l4-1h-1 0c1-1 2-1 2-1 2 0 8 0 9-1z"></path><path d="M130 152c1 1 3 5 4 5v1 1s0-1-1-1v1c-2-2-3-4-3-6v-1z" class="Q"></path><path d="M120 141c4 2 6 6 9 10 1 0 1 1 1 1v1c-2-3-6-6-8-8-1-1-3-3-3-4h1z" class="F"></path><path d="M132 220c1-2 0-4 1-6s1-5 1-8c0-8-1-16-3-25-1-6-2-12-5-18-2-3-4-7-6-10-2-2-5-5-6-8 8 5 10 15 16 21 0 1 1 1 1 1 1-1 1-2 1-2v-1h1v1c4 5 5 13 6 19 1 1 1 2 1 3v7 1c1 1 1 1 0 2l1 1v6 11c0 2 1 5 1 8-1 1-1 3-2 4s-3 2-4 3v2 10c-1 3 0 7 0 10-1-5-1-11-1-17v-6-1s0-1 1-1c0 0 1 0 1-1v-1c1 0 2 0 2-1 2-1 1-4 1-7h0c-2 0-2 0-4 1-1 2-2 3-2 5v2 9c-1 2-1 3-2 5v-19z" class="Y"></path><path d="M325 308h1 1c1 1 2 2 3 2l3 2c1 3 0 6 0 9l-1 1c1 0 1 0 1 1v2 7h1c1-1 2-1 3-2l4 1-7 2v1h1-1c1 1 1 1 2 1h-2c-1 2-1 3-1 4v4h-1c-4 1-8 3-11 5l-10 6-11 7-16 12-4 3c-2 1-4 2-7 4l3-3c1 0 1 0 1-1h-2c-3 1-6 5-8 7l-1 1-1 1c-1 0-1 2-2 3s-3 2-4 3c-3 2-5 5-7 8-5 6-9 14-11 21-2 2-2 3-3 5v2c-1 0-1 1-1 2s0 2-1 3v1h0c0 1-1 1-1 1v1l2-9c0-1 1-3 1-5h0c1-4 3-9 5-13 8-15 18-29 31-40 4-3 7-6 11-9l9-7 2-1c4-2 8-3 11-6h1c1-4 2-7 3-11h0 0 0c-1-2 1-5 1-7 0 0 1-1 1-2s1-2 1-3 1-1 2-1c0-3 3-7 4-9h0l2-2c1 0 1-1 2-1h0l1-1z" class="g"></path><path d="M275 376c2 0 4-2 6-3h3l-4 3c-2 1-4 2-7 4l3-3c1 0 1 0 1-1h-2z" class="h"></path><path d="M311 334c1 1 1 1 2 3 1 1 3 1 5 1v1c1 0 1 0 1 1-3 1-7 3-11 5 1-4 2-7 3-11z" class="E"></path><path d="M313 337c1 1 3 1 5 1v1h-2c0 1-3 2-4 2l-1-1h-1v-1l2-2h1z" class="U"></path><path d="M334 334h1-1c1 1 1 1 2 1h-2c-1 2-1 3-1 4v4h-1v-1-1c-2 1-3 1-5 2l-6 3c-2 0-5 2-6 2v-1h1 1l1-1h0c-2 1-4 1-5 0-2-1-6 3-8 3 3-2 7-4 11-5 5-3 11-6 16-7 1 0 1-1 1-1-1-1-2-1-3-1l4-1z" class="G"></path><path d="M315 348c1 0 4-2 6-2l6-3c2-1 3-1 5-2v1 1c-4 1-8 3-11 5l-10 6-11 7-1-1-2 1v-1-4h-5 1 0c1-1 2-1 3-1h1 1l2-2 2-1 1-1c1 0 3-2 5-2h0 4c0-1 0 0 1-1h2z" class="h"></path><path d="M325 308h1 1c1 1 2 2 3 2l3 2c1 3 0 6 0 9l-1 1c1 0 1 0 1 1v2 7h1c1-1 2-1 3-2l4 1-7 2v1l-4 1-11 5c0-1 0-1-1-1v-1c-2 0-4 0-5-1-1-2-1-2-2-3h0 0 0c-1-2 1-5 1-7 0 0 1-1 1-2s1-2 1-3 1-1 2-1c0-3 3-7 4-9h0l2-2c1 0 1-1 2-1h0l1-1z" class="S"></path><path d="M327 314c1 1 1 2 2 2h2 0c0 2 0 2-1 3h-2c0 1 0 1-1 1l1-1-1-1c-1-1-1-2-2-3 1 0 2 0 2-1z" class="Y"></path><path d="M325 318c0-2-1-6 0-7h1c1 1 1 2 1 3s-1 1-2 1c1 1 1 2 2 3l1 1-1 1c-1 1-2 1-3 1l1-3z" class="i"></path><path d="M327 320h-1c0-1-1-1 0-2h1l1 1-1 1z" class="g"></path><path d="M325 308h1 1c1 1 2 2 3 2l3 2c1 3 0 6 0 9l-1 1c1 0 1 0 1 1-1 0-2 0-3 1v2c-4 1-7 0-10 0h1l-1-2c1-2 0-3 1-5 1 1 2 2 3 2v-3h1l-1 3c1 1 0 2 1 2 2-1 5 0 7-2l1-1v-2c-1 0-1-1-1-1l1-1v-1h-1c-1-1-3-1-4-2-1-2-1-3-3-4l-1 1v-1l1-1z" class="c"></path><path d="M320 324c1-2 0-3 1-5 1 1 2 2 3 2v5h-3l-1-2z" class="X"></path><path d="M320 312h0l2-2c1 0 1-1 2-1h0v1 1 7 3c-1 0-2-1-3-2-1 2 0 3-1 5l1 2h-1l-4-1-1-1v2c-2 2-2 3-3 6v-5s1-1 1-2 1-2 1-3 1-1 2-1c0-3 3-7 4-9z" class="V"></path><path d="M320 324c-1 0-2-1-3-2v-2c0-1 1-2 2-3 0-1 1-2 2-2v1c0 1 0 2 1 3h0-1c-1 2 0 3-1 5z" class="I"></path><path d="M320 312h0l2-2c1 0 1-1 2-1h0v1 1 7 3c-1 0-2-1-3-2h1 0c-1-1-1-2-1-3v-1-2l-1-1z" class="X"></path><path d="M322 319c1-2 0-3 2-4v-4 7 3c-1 0-2-1-3-2h1 0z" class="H"></path><path d="M330 326v-2c1-1 2-1 3-1v2 7h1c1-1 2-1 3-2l4 1-7 2v1l-4 1-11 5c0-1 0-1-1-1v-1c-2 0-4 0-5-1-1-2-1-2-2-3h0 0 0c-1-2 1-5 1-7v5c1-3 1-4 3-6v-2l1 1 4 1c3 0 6 1 10 0z" class="k"></path><path d="M315 329c0 1 0 1 1 2 2 0 2 0 3 1 0 1 0 2 1 3h2c-1 1-2 1-3 1 0-1-1-1-1-2v-1c-1 0-1-1-2-1l-1-1v-2zm15-3v-2c1-1 2-1 3-1v2 7l-3 1c0-1 1-1 2-2h0c0-1 1-3 0-4l-2-1z" class="h"></path><path d="M315 326v-2l1 1c-1 1 0 3-1 4v2l1 1c1 0 1 1 2 1v1c0 1 1 1 1 2h-2c-2-1-3-1-4-2s-1-1-1-2c1-3 1-4 3-6z" class="Y"></path><path d="M315 326v-2l1 1c-1 1 0 3-1 4v2l1 1c1 0 1 1 2 1v1c0 1 1 1 1 2h-2v-3c-1 0-1-1-3 0h0l-1-1v-1h1c1-2 1-3 1-5z" class="i"></path><path d="M311 334h0 0 0c-1-2 1-5 1-7v5c0 1 0 1 1 2s2 1 4 2h2c1 0 2 0 3-1l8-2 3-1h1c1-1 2-1 3-2l4 1-7 2v1l-4 1-11 5c0-1 0-1-1-1v-1c-2 0-4 0-5-1-1-2-1-2-2-3z" class="C"></path><defs><linearGradient id="T" x1="390.459" y1="600.126" x2="408.277" y2="634.004" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#T)" d="M408 593v-1-1h3c1-1 2-1 4-1 13 10 32 12 48 10l13-2h0c-1 2-1 2-3 2-5 3-11 4-16 5-9 1-18 1-27 2-7 0-15 1-22 3l-7 2-6 1s-2 0-2 1c-7 0-15 4-20 7v1 1c-2 1-5 2-8 4-12 8-21 21-26 35 0-1 0-1-1-1h0c-2 0-2-2-3-3v-2l-1-6 1-1c4-13 17-23 27-32 9-8 23-13 35-16l3-1h3l7-1 1-2c0 1 1 1 2 1h1 1l-7-5z"></path><path d="M410 599h3-1 0c-18 3-35 9-50 19v-1c9-8 23-13 35-16l3-1h3l7-1z" class="l"></path><path d="M362 617v1l-8 8c-7 7-13 14-16 23-1 2-2 5-3 7l-1-6 1-1c4-13 17-23 27-32z" class="T"></path><path d="M365 627l-2 1c3-3 8-5 9-9h0c6-4 11-6 17-8 5-1 11-1 16-2 1 1 2 1 3 1l-7 2-6 1s-2 0-2 1c-7 0-15 4-20 7v1 1c-2 1-5 2-8 4z" class="d"></path><path d="M285 178c1 0 3 0 4-1h-1l1-1h1v1h1 0 2c2 1 1 3 1 4v71c-1 7 0 14 1 21l1 3c0 2 1 4 3 5l-1 1c1 1 1 2 2 2 0 1-1 2-1 2-2 1-2 0-3 2 0 1-1 2-1 3s1 1 1 2h0c-1 0-1 1-1 1 0 1-1 1 0 2v1c-2 2-1 8-1 11v10 2 22 9 1l-9 7c-1-3 0-6 0-9v-9-95 1 2-2c0-2 0-4 1-6s0-2 1-4v-5c0-2 0-3 1-5v1-17c0-3 1-6 0-9v-9-6c0-1-1-3-1-4-1-2-1-3-2-5z" class="h"></path><path d="M295 273l1 3c0 2 1 4 3 5l-1 1c1 1 1 2 2 2 0 1-1 2-1 2-2 1-2 0-3 2 0 1-1 2-1 3s1 1 1 2h0c-1 0-1 1-1 1 0 1-1 1 0 2v1c-2 2-1 8-1 11-1-4 0-8 0-13v-14l1-8z" class="P"></path><path d="M294 320v22 9 1l-9 7c-1-3 0-6 0-9v-9 7c2-1 3-2 4-3 1-2 3-2 4-3 1-2 1-4 1-6 0-5-1-10 0-15v-1z" class="I"></path><path d="M285 350v7c3-3 5-5 9-6v1l-9 7c-1-3 0-6 0-9z" class="a"></path><path d="M299 281c3 3 7 7 11 8h1c4 2 10 1 15 1h1 6c0 3 0 15 1 17l-1 1h-1c-2-1-3-1-5-2 0 1-1 1-2 2l-1 1h0c-1 0-1 1-2 1l-2 2h0c-1 2-4 6-4 9-1 0-2 0-2 1s-1 2-1 3-1 2-1 2c0 2-2 5-1 7h0 0 0c-1 4-2 7-3 11h-1c-3 3-7 4-11 6l-2 1v-1-9-22-2-10c0-3-1-9 1-11v-1c-1-1 0-1 0-2 0 0 0-1 1-1h0c0-1-1-1-1-2s1-2 1-3c1-2 1-1 3-2 0 0 1-1 1-2-1 0-1-1-2-2l1-1z" class="k"></path><path d="M296 300l1 1 1 1c1 1 3 2 4 2 4 2 8 1 11 1-3 2-7 1-10 2-1-1-2-1-3-1-2-1-3-2-4-3v-3z" class="V"></path><path d="M313 305c5 0 10 0 14 1 0 1-1 1-2 2l-1 1h0c-1 0-1 1-2 1l-2 2h0c-1 2-4 6-4 9-1 0-2 0-2 1l-3 3c1-1 1-2 1-4h0c1-3 4-8 6-11 1 0 1-1 2-2h-1c-6 0-11 1-17 0 0 0 0 1-1 1 0-1-1-2-1-2h0l-1-1h1c1 0 2 0 3 1 3-1 7 0 10-2z" class="f"></path><path d="M299 306h1c1 0 2 0 3 1h3l-1 1h1-4s0 1-1 1c0-1-1-2-1-2h0l-1-1z" class="a"></path><path d="M299 281c3 3 7 7 11 8h1c-2 2-2 5-4 7s-6 3-9 6l-1-1-1-1c0-1-1-2-1-3v-1c-1-1 0-1 0-2 0 0 0-1 1-1h0c0-1-1-1-1-2s1-2 1-3c1-2 1-1 3-2 0 0 1-1 1-2-1 0-1-1-2-2l1-1z" class="I"></path><path d="M304 288l1 1c0 2-1 2-2 2-1 1-1 1-2 1v-1l1-1c0-1-1-1-2-1h3l1-1z" class="c"></path><path d="M298 292c-1 0-1-1-2-1 0-1 0-1 1-2h3c1 0 2 0 2 1l-1 1c-1 0-2 1-3 1z" class="J"></path><path d="M300 284c1 2 2 3 4 4l-1 1h-3-3c-1 1-1 1-1 2 1 0 1 1 2 1v1l-3 1s0-1 1-1h0c0-1-1-1-1-2s1-2 1-3c1-2 1-1 3-2 0 0 1-1 1-2z" class="U"></path><path d="M299 281c3 3 7 7 11 8h1c-2 2-2 5-4 7s-6 3-9 6l-1-1 1-2c0-1 1-3 3-3 2-1 3-3 5-5v-1l-1-1-1-1c-2-1-3-2-4-4-1 0-1-1-2-2l1-1z" class="G"></path><path d="M295 297c0 1 1 2 1 3v3c1 1 2 2 4 3h-1l1 1h0s1 1 1 2c1 0 1-1 1-1 6 1 11 0 17 0h1c-1 1-1 2-2 2-2 3-5 8-6 11h0c0 2 0 3-1 4l3-3c0 1-1 2-1 3s-1 2-1 2c0 2-2 5-1 7h0 0 0c-1 4-2 7-3 11h-1c-3 3-7 4-11 6l-2 1v-1-9-22-2-10c0-3-1-9 1-11z" class="m"></path><path d="M297 312l1-1c1 0 1 1 1 2s-1 1 0 3c-1 1-2 3-2 5v-9z" class="M"></path><path d="M297 312c0-2 0-4 1-6h1l1 1-1 5 1 1c-1 1-1 2-1 3-1-2 0-2 0-3s0-2-1-2l-1 1z" class="D"></path><path d="M295 297c0 1 1 2 1 3v3l-1-1h0c-1 5 0 10-1 15v1-10c0-3-1-9 1-11z" class="f"></path><path d="M300 313l1-1v1c-1 4-3 7-3 11l-1 2c-1-1 0-3 0-5s1-4 2-5c0-1 0-2 1-3z" class="P"></path><path d="M300 307h0s1 1 1 2c1 0 1-1 1-1 6 1 11 0 17 0h1c-1 1-1 2-2 2-1-1-4 0-6 0l-5-1-2 2-1 3-5 12v-3l-1 1c0-4 2-7 3-11v-1l-1 1-1-1 1-5z" class="B"></path><path d="M296 339c0-1 0-1 1-2 0 1-1 3 0 4 3 0 6 0 8-1h1v-1 4 2h1c-3 3-7 4-11 6-1-3 0-8 0-12z" class="C"></path><path d="M296 343c2 1 7 2 9 1-1 2-2 2-4 2-2 1-3 1-5 0v-3z" class="Z"></path><path d="M296 339c0-1 0-1 1-2 0 1-1 3 0 4 3 0 6 0 8-1h1v-1 4l-1 1c-2 1-7 0-9-1v-4z" class="U"></path><path d="M304 314l1 5c0 1-1 2-2 3-2 3-3 7-4 10 0 1-1 3 0 5 1 0 2 0 3-1v1l2-2s2 1 3 1l-1 3v1h-1c-2 1-5 1-8 1-1-1 0-3 0-4 0-4 1-7 2-11l5-12z" class="T"></path><path d="M305 311l2-2 5 1c2 0 5-1 6 0-2 3-5 8-6 11h0c0 2 0 3-1 4l3-3c0 1-1 2-1 3s-1 2-1 2c0 2-2 5-1 7h0 0 0c-1 4-2 7-3 11h-1-1v-2-4l1-3c-1 0-3-1-3-1l-2 2v-1c-1 1-2 1-3 1-1-2 0-4 0-5 1-3 2-7 4-10 1-1 2-2 2-3l-1-5 1-3z" class="h"></path><defs><linearGradient id="U" x1="305.028" y1="334.816" x2="312.066" y2="333.88" xlink:href="#B"><stop offset="0" stop-color="#27262a"></stop><stop offset="1" stop-color="#3a3b38"></stop></linearGradient></defs><path fill="url(#U)" d="M312 321h0c0 2 0 3-1 4l3-3c0 1-1 2-1 3s-1 2-1 2c0 2-2 5-1 7h0 0 0c-1 4-2 7-3 11h-1-1v-2-4l1-3c1-5 3-9 5-14v-1z"></path><path d="M305 311l2-2 5 1c2 0 5-1 6 0-2 3-5 8-6 11v1c-1 0-1-1-1-1h0c-3 0-5 6-8 8v-1h-1l3-6c3-3 6-4 7-8 0-1 0-1-1-2l-6-1z" class="Y"></path><defs><linearGradient id="V" x1="196.869" y1="170.394" x2="229.32" y2="253.548" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#acabac"></stop></linearGradient></defs><path fill="url(#V)" d="M272 139h4c0 1 0 2 1 3h0c2 1 5 1 6 2h1c-7 2-14 5-21 8-3 1-6 2-9 4-4 2-8 5-12 7-13 8-26 16-37 27-14 13-26 29-35 46l-21 50v-1h-1c-1 0-1 0-2 1v-3c1-7 4-14 7-20 9-23 18-45 34-64 4-6 10-11 15-16 2-2 5-4 7-6h0c0-1 1-2 2-3h0 0l-3 2h-1l-2 2-1-1c0-3 2-6 3-8l1-1c2-1 3 0 5 0 1 0 2 0 3-1l-2-1v-1l4-4c1-1 2-2 4-3 1 1 1 1 2 0v-1l1-1c1 0 0 1 1 2l1-1c1-1 2-1 3-1l1-1c1 0 1-1 2-2v1l2 1 4-2c1 0 1 0 2-1 9-6 20-10 31-13z"></path><path d="M241 152c9-6 20-10 31-13l2 1c-1 1-9 3-12 4l-11 6c-6 2-10 5-15 8-4 2-9 5-13 7-4 4-9 8-14 12 0-1 1-2 2-3h0 0l-3 2h-1l-2 2-1-1c0-3 2-6 3-8l1-1c2-1 3 0 5 0 1 0 2 0 3-1l-2-1v-1l4-4c1-1 2-2 4-3 1 1 1 1 2 0v-1l1-1c1 0 0 1 1 2l1-1c1-1 2-1 3-1l1-1c1 0 1-1 2-2v1l2 1 4-2c1 0 1 0 2-1z" class="Y"></path><path d="M207 169l1-1c2-1 3 0 5 0 1 0 1 1 2 1-3 3-6 5-8 7l-2 2-1-1c0-3 2-6 3-8z" class="f"></path><path d="M233 153v1l2 1c-7 5-14 9-20 14h0c-1 0-1-1-2-1 1 0 2 0 3-1l-2-1v-1l4-4c1-1 2-2 4-3 1 1 1 1 2 0v-1l1-1c1 0 0 1 1 2l1-1c1-1 2-1 3-1l1-1c1 0 1-1 2-2z" class="C"></path><path d="M224 158h2 0c-2 4-7 5-9 8v1h0-1l-2-1v-1l4-4c1-1 2-2 4-3 1 1 1 1 2 0z" class="a"></path><defs><linearGradient id="W" x1="349.096" y1="460.221" x2="393.052" y2="580.766" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#7d7c7c"></stop></linearGradient></defs><path fill="url(#W)" d="M376 451v-2l1 1c0 1 1 1 2 1 1 2 2 2 3 3 1-1 1-3 1-5h0l1 41-1 15c0 8 1 16 1 24 0 3 1 6 1 9 1 1 1 4 1 4 2 7 4 13 7 19 1 3 3 6 4 9h-1l1 1h-1v1c1 2 0 4 1 6 0 1-1 1-1 2h-2l-3-5c-2-2-4-5-5-8-2-4-3-7-4-11-1-3-2-6-3-8h-1c0-1 0-1-1-2v-1l-1 1h0v2l-1-3c-3-7-8-10-14-14-2-1-5-3-8-5-2-1-4-1-6-2l-11-4h0c-1-1-2-1-3-2 1-2 1-7 1-9 0-3 0-7-1-10h1c-1-1 0-2 0-4l-1 1v1-2h0l1-2 1 1 1-1c-1-2 0-3 0-5s0-3 1-5l1-3 2-3-1-2v-1h1 1v-1h0l2-2 1 1-1 1 2 2c4 1 9 2 13 1 1 0 2-1 3-2l-2 3h3c2-2 4-5 6-9l1-1c0-1 0-1 1-2h1c3-4 4-8 5-14z"></path><path d="M376 469c1 0 0 1 1 1v1c0 1-1 2-2 3h-1 0c0-2 1-3 2-5z" class="G"></path><path d="M390 560l2 2c-1 1-1 0-2 0v1l-2-3c-1-2-1-3-1-4v-2h0-1v-1-2l1-1c0 4 1 7 3 10z" class="K"></path><path d="M386 542c2 7 4 13 7 19 1 3 3 6 4 9h-1l-1-3-3-5h0c0-1 0-1-1-2h-1c-2-3-3-6-3-10l-1-5h0v-3z" class="I"></path><path d="M360 480v1c-1 2-1 3-3 5-3 3-7 6-10 8-2 2-4 3-6 4h-1l16-14c2-1 3-3 4-4z" class="R"></path><path d="M361 499v-1h0c2-3 5-6 7-9 3-3 5-6 8-10v9h-1v-1 1l-1 4 1-8c-5 6-9 10-14 15z" class="G"></path><path d="M335 500c6 2 10 6 15 10 6 3 13 6 18 11v1h-1c-1-1-2-2-4-3l-9-5-11-7c-2-2-6-4-8-6v-1z" class="F"></path><path d="M374 492l1-4v-1 1h1v32c0 1 1 6 0 7l-1-1h0v-1c-1-3-1-7-1-11v-22z" class="i"></path><defs><linearGradient id="X" x1="357.065" y1="521.193" x2="354.325" y2="542.129" xlink:href="#B"><stop offset="0" stop-color="#7d7b7b"></stop><stop offset="1" stop-color="#b2b3b3"></stop></linearGradient></defs><path fill="url(#X)" d="M334 509h0v9h1 0c2 0 3 1 5 1 11 3 22 8 31 16 4 3 6 8 8 13h-1c0-1 0-1-1-2v-1l-1 1h0v2l-1-3c-3-7-8-10-14-14-2-1-5-3-8-5-2-1-4-1-6-2l-11-4h0c-1-1-2-1-3-2 1-2 1-7 1-9z"></path><path d="M343 471l1 1-1 1 2 2c4 1 9 2 13 1 1 0 2-1 3-2l-2 3h3c-1 1-2 2-2 3-1 1-2 3-4 4l-16 14-1 1h1 1v1c0 1 2 1 3 2 2 1 4 3 6 4l9-6 2-1c5-5 9-9 14-15l-1 8v22c0 4 0 8 1 11v1h0 0c-2 0-6-4-7-5-5-5-12-8-18-11-5-4-9-8-15-10l-1-1c-1-1 0-2 0-4l-1 1v1-2h0l1-2 1 1 1-1c-1-2 0-3 0-5s0-3 1-5l1-3 2-3-1-2v-1h1 1v-1h0l2-2z" class="j"></path><path d="M340 488l2 2 1 2c1 0 1-1 2-1v-2h1c0 2 0 2-1 3h-2l-1 1-2-1v-4z" class="N"></path><path d="M337 483c0 1-1 3 0 5 0 3 3 5 2 8l-1 1-2-2c0-1 0-1-1-1h-1v1l-1 1v1-2h0l1-2 1 1 1-1c-1-2 0-3 0-5s0-3 1-5z" class="O"></path><path d="M341 479s1 1 2 1c0 1 0 2 1 3l-1 1c0 1 0 2 1 3v1l1 1v2c-1 0-1 1-2 1l-1-2-2-2v-1h0c-1-2-1-4-1-6l2-2z" class="X"></path><path d="M342 490c0-2 0-5 1-7v1c0 1 0 2 1 3v1l1 1v2c-1 0-1 1-2 1l-1-2z" class="C"></path><path d="M371 516h0c0 2 1 3 1 5-3-3-6-5-9-7l-9-6c2-1 3-2 5-3l-1 2c0 1 0 1 1 1l4 3c2 2 3 4 5 4 1 1 2 1 2 2l1-1z" class="X"></path><path d="M351 482c2-1 6-4 8-5h3c-1 1-2 2-2 3-1 1-2 3-4 4-1 0-1 0-2 1h-1c-1 0-3 1-4 1l-1 1c-1 1-2 1-4 1v-1c-1-1-1-2-1-3l1-1c-1-1-1-2-1-3 0 0 3 2 4 2 1-1 2-1 4 0z" class="m"></path><path d="M343 480s3 2 4 2c1-1 2-1 4 0l-6 5h-1c-1-1-1-2-1-3l1-1c-1-1-1-2-1-3z" class="B"></path><path d="M344 483c1 0 2-1 3 0h0c-1 1-3 2-3 3l1 1h-1c-1-1-1-2-1-3l1-1z" class="N"></path><path d="M343 471l1 1-1 1 2 2c4 1 9 2 13 1 1 0 2-1 3-2l-2 3c-2 1-6 4-8 5-2-1-3-1-4 0-1 0-4-2-4-2-1 0-2-1-2-1l-2 2s0-1 1-2h0l-2 1 2-3-1-2v-1h1 1v-1h0l2-2z" class="C"></path><path d="M340 477v-1c1 0 2 1 3 1-1 1-2 1-2 2l-2 2s0-1 1-2h0l-2 1 2-3z" class="N"></path><path d="M343 471l1 1-1 1 2 2-1 1h0c-1-1-2-1-3-3h0l2-2z" class="L"></path><path d="M367 502c1-2 2-3 3-5l1 19-1 1c0-1-1-1-2-2-2 0-3-2-5-4l-4-3c-1 0-1 0-1-1l1-2c1 0 2-1 3-2 2 0 3-1 5-1z" class="D"></path><path d="M365 508h-2v-1h0c1-1 3-1 4-1 1 1 2 3 2 4s0 1-1 1c0-1-1-2-3-3z" class="C"></path><path d="M367 502h0c0 1 0 1 1 1-1 1-2 1-2 1-3 0-6 1-7 4h0c-1 0-1 0-1-1l1-2c1 0 2-1 3-2 2 0 3-1 5-1z" class="H"></path><path d="M365 508c2 1 3 2 3 3l1 1v2l-1 1c-2 0-3-2-5-4l1-1v-1l1-1z" class="b"></path><defs><linearGradient id="Y" x1="323.616" y1="392.443" x2="386.93" y2="413.989" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#Y)" d="M366 366c3-3 5-7 7-10 0-1 0-1 1-1v6 64c0 8 1 18 0 26-1 5-1 9-3 13l-1 1c-1 1-1 1-1 2l-1 1c-2 4-4 7-6 9h-3l2-3c-1 1-2 2-3 2-4 1-9 0-13-1l-2-2 1-1-1-1-2 2c0-1 0-1-1-2l-1 1v-1-1c1-2 2-2 3-3v-2c0-1 1-1 1-2h-1c-1 0-2 0-3-1-2-1-4-2-5-3-1-3-1-6-1-9v-12c0-1 1-3 0-4v-2-1c0-1 0-2 1-2-1-3 0-5 0-7 0-5 0-11-1-16l-2-2h1 0l1-1 2 1c1-1 1-1 1-2l1-2v-1h-1 0c0-2 0-3 1-5 0-1 1-1 1-2h0 0c1 0 0 0 1 1h0 3c0-1-1-1-2-2 0-1 1-1 1-3l3-2c2-3 4-4 8-4-2-1-4 0-6-1 1 0 2-1 3-1s2-1 3-2c0-1 0-1 1-2h1l-1-1h-7l-6-3c-1-1-3-2-4-3 0-1-1-2-2-3h-1v-2l1-1h1c1 2 3 4 4 5 3 2 5 3 8 3l5 1c6 0 10-3 14-6z"></path><path d="M364 372c2-1 3-2 5-3l1-3v6-3l-3 3-1 1c-1 1-3 2-4 2 0-1 1-2 2-3z" class="O"></path><path d="M367 372l3-3v3 1c1 9 0 17 1 25v26 9c-2-4-5-9-8-14-1 0-2-1-2-2l-1-1c-1-1-2-1-2-2 2-1 4-3 6-4 1 0 1 0 2-1h1v-1c-1-1-1-1-2-1 0-2 0-3 2-4l-1-2h-1v-10c-1-1-2-3-3-5l-2-2c-1 1-1 1-3 1-1-1-3-2-5-3s-4 0-6-1c1 0 2-1 3-1s2-1 3-2c0-1 0-1 1-2h1l-1-1h1c3-1 7-2 10-3-1 1-2 2-2 3 1 0 3-1 4-2l1-1z" class="V"></path><path d="M367 410v1c0 1 0 3 1 4l-1 1-1-1v1 1c-1-1-2-1-2-2l3-5z" class="M"></path><path d="M368 400c0 1 0 1 1 2v2c0 1 0 2 1 3l-1 1-2-1v1c-1-1-1-1-2-1 0-2 0-3 2-4 0-1 1-2 1-3z" class="L"></path><path d="M367 409v1l-3 5v1h-1c-1 0-1 0-2-1l-1 1 3 3c-1 0-2-1-2-2l-1-1c-1-1-2-1-2-2 2-1 4-3 6-4 1 0 1 0 2-1h1z" class="X"></path><path d="M367 372l3-3v3 1 1 13-1c-1 1-2 1-2 2-1-1-1-3-2-4v-3h0c0-1 0-1-1-1h0c0-1-1-2-1-3v-1h-2l-1-1h1c1 0 3-1 4-2l1-1z" class="W"></path><path d="M364 377s1-1 2-1c0 2 1 3 0 4v1c0-1 0-1-1-1h0c0-1-1-2-1-3z" class="P"></path><path d="M362 375c1 0 3-1 4-2v3c-1 0-2 1-2 1v-1h-2l-1-1h1z" class="E"></path><path d="M367 372l3-3v3 1 1 13-1c-1 1-2 1-2 2-1-1-1-3-2-4v-3c1 2 1 4 3 5v-3c-1-2 0-4-1-6v-1h1c-1-2-1-3-2-4z" class="b"></path><path d="M353 375h1c3-1 7-2 10-3-1 1-2 2-2 3h-1l1 1h2v1c0 1 1 2 1 3h0c1 0 1 0 1 1h0v3c1 1 1 3 2 4 1 4 2 9 1 14-1-1-1-1-1-2 0 1-1 2-1 3l-1-2h-1v-10c-1-1-2-3-3-5l-2-2c-1 1-1 1-3 1-1-1-3-2-5-3s-4 0-6-1c1 0 2-1 3-1s2-1 3-2c0-1 0-1 1-2h1l-1-1z" class="B"></path><path d="M366 384l-1-2c-1-2-3-5-5-6h0v-1h1l1 1h2v1c0 1 1 2 1 3h0c1 0 1 0 1 1h0v3z" class="D"></path><defs><linearGradient id="Z" x1="369.7" y1="398.159" x2="361.156" y2="389.121" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#656665"></stop></linearGradient></defs><path fill="url(#Z)" d="M361 380c5 6 6 13 7 20 0 1-1 2-1 3l-1-2h-1v-10c-1-1-2-3-3-5h1c-1-2-1-3-2-4h0l1 1h1c-1-1-1-2-2-3z"></path><path d="M354 376c2 1 4 1 6 3l1 1c1 1 1 2 2 3h-1l-1-1h0c1 1 1 2 2 4h-1l-2-2c-1 1-1 1-3 1-1-1-3-2-5-3s-4 0-6-1c1 0 2-1 3-1s2-1 3-2c0-1 0-1 1-2h1z" class="K"></path><path d="M356 380s-1-1-2-1v-1c1 1 3 1 4 1 1 1 1 0 2 0l1 1c1 1 1 2 2 3h-1l-1-1h0l-3-2h-2z" class="W"></path><path d="M356 380h2l3 2c1 1 1 2 2 4h-1l-2-2c-1-1-2-3-4-4z" class="M"></path><path d="M341 388l3-2c2-3 4-4 8-4 2 1 4 2 5 3 2 0 2 0 3-1l2 2c1 2 2 4 3 5v10h1l1 2c-2 1-2 2-2 4 1 0 1 0 2 1v1h-1c-1 1-1 1-2 1-2 1-4 3-6 4-2 0-3 0-4 1l-10-5c-4-2-7-4-11-4l-2-2h1 0l1-1 2 1c1-1 1-1 1-2l1-2v-1h-1 0c0-2 0-3 1-5 0-1 1-1 1-2h0 0c1 0 0 0 1 1h0 3c0-1-1-1-2-2 0-1 1-1 1-3z" class="U"></path><path d="M365 401h1l1 2c-2 1-2 2-2 4h-2v-1c1-2 2-3 2-5z" class="X"></path><g class="B"><path d="M359 399c1-1 3-2 4-2h0c1 1 1 2 1 4v1h0c-1 0-2-1-3-1l-2-2z"></path><path d="M361 401c1 0 2 1 3 1l-2 3c-2 1-3 2-4 3l-1-2h2c0-2 0-3 1-4h0l1-1z"></path></g><path d="M361 401c1 0 2 1 3 1l-2 3c-1-2-1-2-2-3l1-1z" class="X"></path><path d="M352 402h1 0c1 2 1 2 3 3l1 1 1 2h-3c-1 0-1 0-2-1h0l-1-1v-1-3z" class="a"></path><path d="M356 398v1c1 0 2 1 3 0h0l2 2-1 1h0c-1 1-1 2-1 4h-2l-1-1c-2-1-2-1-3-3l1 1h1v-4c0-1 1-1 1-1z" class="O"></path><path d="M360 389h0c1 1 1 1 1 2 1 1 1 2 0 3l-1 1c-2 0-1-2-2-1h0l-2 4s-1 0-1 1v4h-1l-1-1h0-1v-1c0-1-1-1-1-2l-1-1-1-1c-1-2 0-2 1-3 0 1 0 1 1 2h1l1-1c0-1 0-1 1-1l3-4c2 0 2 0 3-1z" class="B"></path><path d="M353 395l2-1 1 1c-1 1-1 1-2 1v1 1 1c-1 0-1-1-2-1h-1l-1-2h1 1l1-1z" class="E"></path><path d="M350 394c0 1 0 1 1 2h-1l1 2h1c1 0 1 1 2 1v-1l1 1v4h-1l-1-1h0-1v-1c0-1-1-1-1-2l-1-1-1-1c-1-2 0-2 1-3z" class="V"></path><path d="M341 388l3-2c2-3 4-4 8-4 2 1 4 2 5 3l3 4c-1 1-1 1-3 1l-3 4c-1 0-1 0-1 1l-1 1h-1c-1-1-1-1-1-2-1-1-2-1-3-2-1 0-2-1-3-1l-3-3z" class="a"></path><path d="M352 386l2 1c1 1 1 1 1 2s1 1 2 1h-4c-1 0-1 1-2 2 0-1-1-1-1-2h0 1c1-2 1-2 1-4z" class="V"></path><path d="M351 392c1-1 1-2 2-2h4 0l-3 4v-2c-1 0-2 1-3 1v-1z" class="E"></path><path d="M346 389h0c0-2 2-3 3-3h3c0 2 0 2-1 4h-1 0l-4-1z" class="D"></path><path d="M350 390h0v-4h2c0 2 0 2-1 4h-1z" class="P"></path><path d="M346 389l4 1c0 1 1 1 1 2v1c1 0 2-1 3-1v2c-1 0-1 0-1 1l-1 1h-1c-1-1-1-1-1-2-1-1-2-1-3-2-1 0-2-1-3-1l2-2z" class="e"></path><defs><linearGradient id="a" x1="343.554" y1="412.74" x2="354.18" y2="398.658" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#a)" d="M342 393c0-1-1-1-2-2 0-1 1-1 1-3l3 3c1 0 2 1 3 1 1 1 2 1 3 2-1 1-2 1-1 3l1 1 1 1c0 1 1 1 1 2v1 3 1l1 1h0l-2 3c1 0 2 1 3 1 1 1 3 1 4 0 2-1 3-2 5-4h2c1 0 1 0 2 1v1h-1c-1 1-1 1-2 1-2 1-4 3-6 4-2 0-3 0-4 1l-10-5c-4-2-7-4-11-4l-2-2h1 0l1-1 2 1c1-1 1-1 1-2l1-2v-1h-1 0c0-2 0-3 1-5 0-1 1-1 1-2h0 0c1 0 0 0 1 1h0 3z"></path><path d="M341 397l1 1-1 3-3 4c0-1 1-4 1-6h0v-1l2-1z" class="H"></path><path d="M341 401l1-3c1 1 3 1 3 3v2l-1 1v-2h-1c-1 0-1-1-2-1z" class="B"></path><path d="M351 399c0 1 1 1 1 2v1 3 1c-1 1-1 2-3 3-1-1-2-1-3-1-1-1-2-1-2-1-1-1-1-1-1-2l1-1 1-1v-2h1c0 1 0 2-1 3 1 0 1 0 2-1h1 0 1l2-4z" class="b"></path><path d="M345 403v2h0 1c1-1 2-1 3-2-1 1-3 3-3 5-1-1-2-1-2-1-1-1-1-1-1-2l1-1 1-1z" class="H"></path><path d="M342 393c0-1-1-1-2-2 0-1 1-1 1-3l3 3c1 0 2 1 3 1 1 1 2 1 3 2-1 1-2 1-1 3l1 1 1 1-2 4h-1 0-1c-1 1-1 1-2 1 1-1 1-2 1-3h-1c0-2-2-2-3-3l-1-1-2 1v1l-2 1v-1h-1 0c0-2 0-3 1-5 0-1 1-1 1-2h0 0c1 0 0 0 1 1h0 3z" class="E"></path><path d="M337 399l1-2c1-1 2 0 3 0l-2 1v1l-2 1v-1z" class="O"></path><path d="M337 394c0-1 1-1 1-2h0 0c1 0 0 0 1 1h0 3 1l2 1v1c-1 0-2 0-2-1h-1c-2 0-2-1-4 1h-1 0v-1z" class="Z"></path><defs><linearGradient id="b" x1="342.825" y1="452.217" x2="360.041" y2="418.624" xlink:href="#B"><stop offset="0" stop-color="#3f3f40"></stop><stop offset="1" stop-color="#6b6b6b"></stop></linearGradient></defs><path fill="url(#b)" d="M333 406c4 0 7 2 11 4l10 5c3 2 6 3 7 6 4 5 7 12 9 18 1 2 2 5 2 7s0 5-1 7c0 3-2 8 0 11l-1 1c-1 1-1 1-1 2l-1 1c0-1-1-1 0-2 0-4 0-9-1-13v-1c-2-1-3-3-4-4-1-3-3-6-5-8-5-6-12-12-20-13-2 1-3 1-4 2-1-3 0-5 0-7 0-5 0-11-1-16z"></path><defs><linearGradient id="c" x1="362.267" y1="441.594" x2="368.987" y2="424.968" xlink:href="#B"><stop offset="0" stop-color="#595558"></stop><stop offset="1" stop-color="#7e8180"></stop></linearGradient></defs><path fill="url(#c)" d="M359 421h0 1 1c4 5 7 12 9 18 1 2 2 5 2 7-1 0-1 0-2-1v-1-1c-1-1 0-1-1-1-2-5-4-10-6-14-2-3-2-5-4-7z"></path><path d="M344 410l10 5c3 2 6 3 7 6h-1-1 0c-2 0-3-1-4-2-2-1-4-2-5-1 0 0 1 1 1 2s0 1-1 2c0 0 0-1-1-2h-1c0-1-1-2-2-3-1-2-2-2-2-4-1-1-1-2 0-3z" class="U"></path><defs><linearGradient id="d" x1="359.628" y1="436.713" x2="350.438" y2="441.528" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#d)" d="M338 427h3 0c1 0 1 1 1 1l1-1h0c1 1 2 1 3 1 8 2 14 10 18 17 2 2 3 5 3 8v-1c-2-1-3-3-4-4-1-3-3-6-5-8-5-6-12-12-20-13z"></path><path d="M338 427c8 1 15 7 20 13 2 2 4 5 5 8 1 1 2 3 4 4v1c1 4 1 9 1 13-1 1 0 1 0 2-2 4-4 7-6 9h-3l2-3c-1 1-2 2-3 2-4 1-9 0-13-1l-2-2 1-1-1-1-2 2c0-1 0-1-1-2l-1 1v-1-1c1-2 2-2 3-3v-2c0-1 1-1 1-2h-1c-1 0-2 0-3-1-2-1-4-2-5-3-1-3-1-6-1-9v-12c0-1 1-3 0-4v-2-1c0-1 0-2 1-2 1-1 2-1 4-2z" class="j"></path><path d="M347 434h1c-1 2-1 3-2 4-1 2-1 3-2 5 0-1-1-1 0-2 0-2 1-6 3-7z" class="C"></path><path d="M346 433l1 1c-2 1-3 5-3 7-1 1 0 1 0 2s-1 2-1 4l-1-3c0-3 1-8 4-11z" class="e"></path><path d="M339 454c1 2 2 2 2 4h0 1l1 1v-1l2 6h0c-1 0-1 0-2-1h-1c-1 0-2 0-3-1 1 0 2 0 2-1v-1l-2-6z" class="B"></path><path d="M343 463c1 1 1 1 2 1h0c1 3 2 5 4 7h-1c-1 0-2-1-2-1l-1-2h-1l-1 3-2 2c0-1 0-1-1-2l-1 1v-1-1c1-2 2-2 3-3v-2c0-1 1-1 1-2z" class="N"></path><path d="M342 467l1 1-2 2h-2c1-2 2-2 3-3z" class="a"></path><path d="M363 466l1-1c1 0 1 0 1 1-1 3-2 6-4 8-1 1-2 2-3 2-4 1-9 0-13-1l-2-2 1-1c3 2 7 3 11 3l1-1h1c3-2 5-5 6-8z" class="h"></path><path d="M341 458v-3c-1-1 0-3-1-5v-3-1-2c1-1 0-4 0-6v-2c0-1 0-4 1-5 1 0 2 0 3 1l2 1c-3 3-4 8-4 11l1 3c-1 4 0 7 0 11v1l-1-1h-1z" class="N"></path><path d="M344 432l2 1c-3 3-4 8-4 11l-1-1c0-2 0-8 2-9l1-2z" class="B"></path><path d="M341 443l1 1 1 3c-1 4 0 7 0 11v1l-1-1c-1-5-1-10-1-15z" class="X"></path><defs><linearGradient id="e" x1="358.458" y1="447.606" x2="361.316" y2="460.552" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#8e8e8e"></stop></linearGradient></defs><path fill="url(#e)" d="M355 448c1 0 2-1 3-1 3 1 5 3 6 6 2 3 2 9 1 13 0-1 0-1-1-1l-1 1v-1c0-3 0-6-1-8-2-3-4-5-7-6l-1-1h0l1-2z"></path><path d="M353 450v-2h2l-1 2h0l1 1c3 1 5 3 7 6 1 2 1 5 1 8v1c-1 3-3 6-6 8h-1s-1-1 0-2v-3c1-4-1-7-2-11-1-2-1-5-1-8z" class="f"></path><path d="M356 455c1 1 2 2 2 3 2 4 3 8 1 13h0v-5c-1-2-1-4-2-5 0-2-2-4-1-6z" class="V"></path><path d="M334 429c0 1-1 3 0 4l2-2c1 1 1 0 1 1 1 1 1 2 1 3 0 7 0 13 1 19l2 6v1c0 1-1 1-2 1-2-1-4-2-5-3-1-3-1-6-1-9v-12c0-1 1-3 0-4v-2-1c0-1 0-2 1-2z" class="W"></path><path d="M334 429c0 1-1 3 0 4l2-2c1 1 1 0 1 1 1 1 1 2 1 3-1-1-1-1-1-2-2 2-2 7-2 10s1 6 1 9v6c2 1 3 3 5 3v-1 1c0 1-1 1-2 1-2-1-4-2-5-3-1-3-1-6-1-9v-12c0-1 1-3 0-4v-2-1c0-1 0-2 1-2z" class="D"></path><path d="M350 437h1 1l3 3-2 8v2c0 3 0 6 1 8 1 4 3 7 2 11v3c-2-2-5-3-6-5s-3-5-3-7c-1-3-2-5-2-7 0-6 1-11 5-16z" class="O"></path><path d="M356 469s-1 0-1-1c-3-3-5-11-4-15 0-1 0-2 1-3h1c0 3 0 6 1 8 1 4 3 7 2 11z" class="b"></path><path d="M350 437h1 1c1 3-2 8-3 12l-1-1v-1c0 1 0 3-1 4-1 3 0 6 0 9-1-3-2-5-2-7 0-6 1-11 5-16z" class="D"></path><path d="M350 437l1 1c-1 3-2 5-3 7-1 3-1 5-2 8h-1c0-6 1-11 5-16z" class="F"></path><defs><linearGradient id="f" x1="383.937" y1="558.173" x2="368.138" y2="567.802" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#f)" d="M335 525c2-1 4-1 6 0h1 1c1 1 4 1 4 2 1 0 1 1 0 2v5c3-1 5-3 6-6h0l-1 1c-1 1-2 3-3 3l-1 1h0v-5c0-1 0-2 1-2l3 1h1v-1l8 5c6 4 11 7 14 14l1 3v-2h0l1-1v1c1 1 1 1 1 2h1c1 2 2 5 3 8 1 4 2 7 4 11 1 3 3 6 5 8l3 5c5 4 9 9 14 13l7 5h-1-1c-1 0-2 0-2-1l-1 2-7 1h-3l-3 1c-12 3-26 8-35 16-10 9-23 19-27 32v-3h-1 0c-1-2-1-3-1-5h-1c-1-1-2-2-2-4h1v-3c0-1 0-2-1-3 1-1 1-1 2-1l-1-1c0-1-1-1-2-1v-1h-1v-2c1 0 1-1 2-2h0c1-1 1-3 1-4l1 1v-1l1-2c-1-1 0-2 0-4v-4c1-4 1-9 1-14v-12-1c-1-4 0-8-2-12-1-3-2-6-4-8-1-2-3-4-5-4v-1h3v-1c-1-1-2-2-3-4h-1c0-2-1-4-2-7 1-2 0-6 1-8 1 0 1 0 1 1l1 1v-8-2h1 1c1 0 2-1 3-1 2-1 5-2 7-3z"></path><path d="M364 548c0-2 1-3 2-4 1 1 1 3 2 4-1 0-1 1-2 1-1-1-1-1-2-1z" class="C"></path><path d="M362 542c1 0 1-1 2 0h1 0v-4c-1 0-1 0-2 1 0 0-1 1-2 1v-1h0c1 0 1 0 1-1 1 0 2 0 2-1 1 1 2 5 2 6v1c-1 1-2 2-2 4v1c-1 1-2 3-3 5l-1-2h0c1-1 2-3 3-5 1-1 1-3 1-4h-1l-1-1h0z" class="X"></path><path d="M368 548c2 3 3 6 6 9h-1c-1 0-2-1-2-1v2c-1-1-2-3-3-5l-2-1v-3c1 0 1-1 2-1z" class="D"></path><path d="M364 548c1 0 1 0 2 1v3l2 1c-1 1-1 2-2 2h-2l1-1-1-1v1h-2v2h0v1c1 0 1 0 2 1l-3-1h-3v-1l3-2c1-2 2-4 3-5v-1z" class="V"></path><path d="M364 548c1 0 1 0 2 1v3l2 1c-1 1-1 2-2 2h-2l1-1-1-1c-1 0-1 0-1-1v-1c1 1 1 1 2 1v-1c0-1-1-1-1-2h0v-1z" class="K"></path><path d="M368 553c1 2 2 4 3 5s3 5 3 7h0c-3-4-6-6-10-7-1-1-1-1-2-1v-1h0v-2h2v-1l1 1-1 1h2c1 0 1-1 2-2z" class="E"></path><path d="M348 536c1 0 3 0 4 1h0 1c0-1 1-1 2-1s1 1 2 2h1l-1-1 1-1h0c1 2 2 3 3 5 0 1 0 1-1 2l-3-3c0-1-1-1-1-1v1h-1v1h-2v1 1 1h-3c-1 0-1-1-2-2 1-1 1-1 1-2-1-1-1-2-1-4z" class="H"></path><path d="M352 538h1c1 0 2 1 2 2v1h-2v1c-1-1-1-2 0-2v-1l-1-1z" class="e"></path><path d="M349 540c1 0 2-1 3-2l1 1v1c-1 0-1 1 0 2v1 1h-3c-1 0-1-1-2-2 1-1 1-1 1-2z" class="f"></path><defs><linearGradient id="g" x1="381.041" y1="544.478" x2="381.412" y2="571.42" xlink:href="#B"><stop offset="0" stop-color="#bab7b8"></stop><stop offset="1" stop-color="#dcdede"></stop></linearGradient></defs><path fill="url(#g)" d="M376 548v-2h0l1-1v1c1 1 1 1 1 2h1c1 2 2 5 3 8 1 4 2 7 4 11 1 3 3 6 5 8h-1l-2-2h0v2h1v4c-3-5-5-10-8-15-2-5-4-10-5-16z"></path><defs><linearGradient id="h" x1="390.864" y1="583.738" x2="386.827" y2="587.774" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#h)" d="M383 580v-6h0c1 1 1 3 2 4 2 5 7 10 12 12 0 1 1 1 1 1l3 3-1 1h-1c-1-1-2-1-2-1l-4 1c-5-4-8-9-10-15z"></path><path d="M397 594v-1h-1v-1c-1 0-1-1-2-1-3-1-6-5-8-7h1c0 1 4 4 5 5h1c0 1 0 1 1 1h0c2 1 3 1 4 1l3 3-1 1h-1c-1-1-2-1-2-1z" class="V"></path><path d="M382 582s1-1 1-2h0c2 6 5 11 10 15l4-1s1 0 2 1c-1 0-1 0-1 1h0c1 1 2 3 2 4l-3 1c0-1-1-1-1-2h-1c-2 1-4 1-6 1 1 0 1-1 1-1 0-1 0-1-1-2-1-2-3-3-4-5v-2c-1-1-1-2-2-3v-1c-1 0-1-1-2-2l1-2z" class="j"></path><path d="M397 594s1 0 2 1c-1 0-1 0-1 1h-1c-2 0-3 0-4-1l4-1z" class="C"></path><path d="M385 590c2 2 4 5 7 6l3 3c-2 1-4 1-6 1 1 0 1-1 1-1 0-1 0-1-1-2-1-2-3-3-4-5v-2z" class="L"></path><path d="M389 579v-4h-1v-2h0l2 2h1l3 5c5 4 9 9 14 13l7 5h-1-1c-1 0-2 0-2-1-4-1-7-3-10-6l-12-12z" class="k"></path><path d="M381 584c1 1 1 2 2 2v1c1 1 1 2 2 3v2c1 2 3 3 4 5 1 1 1 1 1 2 0 0 0 1-1 1l-6 2-6 2-2-1c-1-1-1-2-1-3l2-2h-1l1-1v-1h-1l2-3 2-3c1-2 2-3 2-6z" class="e"></path><path d="M381 584c1 1 1 2 2 2 0 1-1 1-1 1 0 2 0 3-1 4-1 0-1 0-2-1 1-2 2-3 2-6z" class="a"></path><path d="M376 601h1c1-1 1-1 1-2 1-1 0-1 1-2v-1l2 2v1l-2 2h0-1c-1 1-1 1-2 0z" class="H"></path><path d="M381 598v-1l-2-2c1-1 2-1 2-2l3 3c0 2-1 3-2 4-1 0-2 1-3 1h0l2-2v-1z" class="C"></path><path d="M381 593c1 0 2 0 2-2-1-1 0-2 0-3v-1c1 1 1 2 2 3v2c1 2 3 3 4 5 1 1 1 1 1 2 0 0 0 1-1 1l-6 2-6 2-2-1c-1-1-1-2-1-3l2-2v3c1 1 1 1 2 0h1c1 0 2-1 3-1 1-1 2-2 2-4l-3-3z" class="b"></path><path d="M381 593c1 0 2 0 2-2-1-1 0-2 0-3v-1c1 1 1 2 2 3v2l-1-1c0-1 0-1-1-2v4h1v2 1l-3-3z" class="D"></path><path d="M383 602v-3c2 0 2-1 4-3 0 1 1 2 2 2v-1c1 1 1 1 1 2 0 0 0 1-1 1l-6 2z" class="H"></path><path d="M356 540v-1s1 0 1 1l3 3c1-1 1-1 1-2l1 1h0l1 1h1c0 1 0 3-1 4-1 2-2 4-3 5h0c-1 2-3 4-5 4-3 0-6 2-9 3-1 1-3 2-5 3 2-2 3-4 4-6h-1c0-1 10-6 11-7 0 0 0-1 1-1 0 0 0-1 1-1v-3h-1c-1 1-1 2-1 2l-2 2v-1c-1-2-1-2-3-3h3v-1-1-1h2v-1h1z" class="D"></path><path d="M355 540h1l1 3c0 2 1 3 0 4 0 1-1 2-2 2 0 0 0-1 1-1 0 0 0-1 1-1v-3h-1c-1 1-1 2-1 2l-2 2v-1c-1-2-1-2-3-3h3v-1-1-1h2v-1z" class="B"></path><path d="M355 540h1l1 3s-1 0-1 1h-1l-2-1v-1-1h2v-1z" class="L"></path><defs><linearGradient id="i" x1="381.211" y1="562.903" x2="364.404" y2="587.525" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#i)" d="M371 558v-2s1 1 2 1h1c4 4 6 11 7 17 1 2 1 4 1 7v1l-1 2c0 3-1 4-2 6l-2 3h0-1c0 1-1 1-2 1h0-3 0v-3c2-1 4-4 5-6l1-1c-1-2 0-3 0-4v-5h-1c0-4-1-7-2-10h0c0-2-2-6-3-7z"></path><path d="M377 575v-1c1 1 1 2 1 2 0 1 1 1 1 2 0 2-1 5-1 7l-1-1c-1-2 0-3 0-4v-5z" class="c"></path><path d="M377 584l1 1c-2 3-4 7-7 9v-3c2-1 4-4 5-6l1-1z" class="J"></path><defs><linearGradient id="j" x1="343.224" y1="564.063" x2="377.138" y2="580.014" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#343334"></stop></linearGradient></defs><path fill="url(#j)" d="M360 552l1 2-3 2v1h3l3 1c4 1 7 3 10 7 1 3 2 6 2 10h1v5c0 1-1 2 0 4l-1 1c-1 0-2 0-3 1 0 1-1 1-1 1l-1-1c-1 1-1 2-3 2l-1-1h0v-1c3-2 4-4 5-6v-7h0l-1-2h0c-1-3-2-5-4-6v-1h-1v-3h-1c-2-2-5-2-8-2-1 1-1 1-2 1l-6 3c-5 3-9 11-11 17v-1c0-1 0-1-1-1h0c-1 1-1 2-2 4v-2h-1c0-1 1-4 1-5l-1-10v-2h0v3l1 6c0-2-1-4 0-5l1-1v-1c2-3 5-7 8-9h1c-1 2-2 4-4 6 2-1 4-2 5-3 3-1 6-3 9-3 2 0 4-2 5-4z"></path><path d="M366 561c2 1 3 2 4 3h-3-1v-3z" class="O"></path><path d="M360 552l1 2-3 2v1h3c-3 0-5 1-8 1l2-2c2 0 4-2 5-4z" class="B"></path><path d="M367 564h3c1 1 1 2 2 4 0 2 1 4 1 6v3h0 0v-1-2c-1 2-1 3 0 4l-1 2v-7h0l-1-2h0c-1-3-2-5-4-6v-1z" class="H"></path><path d="M372 580l1-2c-1-1-1-2 0-4v2 1h0 0v-3c1 2 1 3 1 4v1c0 2-1 4-3 6v1c-1 1-1 2-3 2l-1-1h0v-1c3-2 4-4 5-6z" class="X"></path><defs><linearGradient id="k" x1="343.996" y1="578.418" x2="344.249" y2="556.553" xlink:href="#B"><stop offset="0" stop-color="#272729"></stop><stop offset="1" stop-color="#454544"></stop></linearGradient></defs><path fill="url(#k)" d="M344 556h1c-1 2-2 4-4 6 2-1 4-2 5-3 3-1 6-3 9-3l-2 2c-4 3-9 6-12 10-2 2-3 6-3 9l-1 1h0c-1 1-1 2-2 4v-2h-1c0-1 1-4 1-5l-1-10v-2h0v3l1 6c0-2-1-4 0-5l1-1v-1c2-3 5-7 8-9z"></path><path d="M344 556h1c-1 2-2 4-4 6l-6 10c1-2 1-4 1-6v-1c2-3 5-7 8-9z" class="S"></path><defs><linearGradient id="l" x1="346.026" y1="532.049" x2="325.806" y2="549.737" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#504e4f"></stop></linearGradient></defs><path fill="url(#l)" d="M335 525c2-1 4-1 6 0h1 1c1 1 4 1 4 2 1 0 1 1 0 2v5l1 2c0 2 0 3 1 4 0 1 0 1-1 2 1 1 1 2 2 2 2 1 2 1 3 3v1l2-2s0-1 1-2h1v3c-1 0-1 1-1 1-1 0-1 1-1 1-1 1-11 6-11 7-3 2-6 6-8 9v1l-1 1c-1 1 0 3 0 5l-1-6v-3h0v2l1 10c0 1-1 4-1 5v2c-1-4 0-8-2-12-1-3-2-6-4-8-1-2-3-4-5-4v-1h3v-1c-1-1-2-2-3-4h-1c0-2-1-4-2-7 1-2 0-6 1-8 1 0 1 0 1 1l1 1v-8-2h1 1c1 0 2-1 3-1 2-1 5-2 7-3z"></path><path d="M335 525c2-1 4-1 6 0h1 1c1 1 4 1 4 2 1 0 1 1 0 2v5l1 2c0 2 0 3 1 4 0 1 0 1-1 2 1 1 1 2 2 2 2 1 2 1 3 3v1l2-2s0-1 1-2h1v3c-1 0-1 1-1 1-1 0-1 1-1 1-1 1-11 6-11 7-3 2-6 6-8 9v1l-1 1c-1 1 0 3 0 5l-1-6v-3-2c2-1 2-3 3-5 2-2 3-4 4-6 2-4 2-13 2-17 0-2-1-4-2-6 0-1-1-1-1-2h-5z" class="j"></path><path d="M344 547c1 0 1 1 2 1l2-3v-2h0v-1c1 1 1 2 2 2 2 1 2 1 3 3v1l-10 4c1-2 1-3 1-5z" class="O"></path><path d="M335 525c2-1 4-1 6 0h1 1c1 1 4 1 4 2 1 0 1 1 0 2v5l1 2c0 2 0 3 1 4 0 1 0 1-1 2v1h0v2l-2 3c-1 0-1-1-2-1 0-2 1-3 1-5 0-5 0-11-3-15h-1c0-1-1-1-1-2h-5z" class="N"></path><path d="M332 529l1-1c1 1 1 31 1 35v2l1 10c0 1-1 4-1 5v2c-1-4 0-8-2-12-1-3-2-6-4-8-1-2-3-4-5-4v-1h3v-1c-1-1-2-2-3-4h-1c0-2-1-4-2-7 1-2 0-6 1-8 1 0 1 0 1 1l1 1v-8h2c2-1 4-2 7-2z" class="k"></path><path d="M323 531h2c2-1 4-2 7-2h-1l-6 3c0 1 1 3 0 4l-1 1c0 1 1 1 1 1 1 2 0 4 0 5v1 1l-1 1h1c0 1 1 1 2 0v3c-1 1-1 0-2 1-2-3-2-8-2-11v-8z" class="S"></path><defs><linearGradient id="m" x1="320.815" y1="552.79" x2="332.983" y2="552.811" xlink:href="#B"><stop offset="0" stop-color="#363537"></stop><stop offset="1" stop-color="#565452"></stop></linearGradient></defs><path fill="url(#m)" d="M320 545c1-2 0-6 1-8 1 0 1 0 1 1l1 1c0 3 0 8 2 11 2 2 4 5 5 8 2 2 2 5 4 7l1 10c0 1-1 4-1 5v2c-1-4 0-8-2-12-1-3-2-6-4-8-1-2-3-4-5-4v-1h3v-1c-1-1-2-2-3-4h-1c0-2-1-4-2-7z"></path><path d="M338 580c2-6 6-14 11-17l6-3c1 0 1 0 2-1 3 0 6 0 8 2h1v3h1v1c2 1 3 3 4 6h0l1 2h0v7c-1 2-2 4-5 6v1h0l1 1c2 0 2-1 3-2l1 1s1 0 1-1c1-1 2-1 3-1-1 2-3 5-5 6v3h0 3 0c1 0 2 0 2-1h1 0l-2 3h1v1l-1 1h1l-2 2c0 1 0 2 1 3l2 1 6-2 6-2c2 0 4 0 6-1h1c0 1 1 1 1 2-12 3-26 8-35 16-10 9-23 19-27 32v-3h-1 0c-1-2-1-3-1-5h-1c-1-1-2-2-2-4h1v-3c0-1 0-2-1-3 1-1 1-1 2-1l-1-1c0-1-1-1-2-1v-1h-1v-2c1 0 1-1 2-2h0c1-1 1-3 1-4l1 1v-1l1-2c-1-1 0-2 0-4v-4c1-4 1-9 1-14v-12-1-2h1v2c1-2 1-3 2-4h0c1 0 1 0 1 1v1z" class="H"></path><path d="M375 605c-1 0-1-1-2-2v-3h1c0 1 0 2 1 3l2 1c-1 0-1 1-2 1z" class="B"></path><path d="M356 566h2l-2 1h0c2 1 4 1 5 2l1 1c1 0 2 1 3 2-1 0-2 0-2-1-2-1-3-2-4-2h-1c-2-1-4 0-6-1 1-1 3-2 4-2z" class="Z"></path><path d="M356 589c2 2 3 4 6 4h4c2 1 3 1 5 1-3 1-7 1-9 1-2-1-4-2-6-4h1l-1-2z" class="E"></path><path d="M358 566c4 1 7 3 9 7h0l-2 1v-2c-1-1-2-2-3-2l-1-1c-1-1-3-1-5-2h0l2-1z" class="I"></path><path d="M356 591l-1-1c-1-3-2-6-1-9 0-3 2-6 5-7h2c1 0 2 2 3 3 0 2 0 2-1 4l-1-1v-2-1c-1-1-1-1-3-1-2 1-4 4-4 6-1 2 0 5 1 7l1 2h-1z" class="P"></path><path d="M356 589c-1-2-2-5-1-7 0-2 2-5 4-6 2 0 2 0 3 1 0 1 0 3-1 4 0-1-1-1-1-1v-1c-1 0-2 1-3 2 0 1 0 1-1 2v1c0 2 2 4 3 5l1 2c3-1 4-2 6-3l1-1 1 1c2 0 2-1 3-2l1 1s1 0 1-1c1-1 2-1 3-1-1 2-3 5-5 6v3h0 0c-2 0-3 0-5-1h-4c-3 0-4-2-6-4z" class="f"></path><path d="M372 587s1 0 1-1c1-1 2-1 3-1-1 2-3 5-5 6v3h0 0c-2 0-3 0-5-1h-4 4l3-3h1c1-1 1-2 2-3z" class="L"></path><path d="M366 593c2 0 3-1 5-2v3h0 0c-2 0-3 0-5-1z" class="G"></path><path d="M340 591c1-4 1-9 3-13h1c-1 6-2 12 2 17 2 4 6 7 10 8s11 0 14-3h2 0c0 1-1 3-2 4l-2-1-2 1s0 1-1 1c-2 1-4 2-6 2v-1c-1 0-2-1-3-1-5-1-8-3-12-7h-1l-2-3-1-4z" class="f"></path><path d="M366 604s0 1-1 1c-2 1-4 2-6 2v-1c-1 0-2-1-3-1 3 0 7 0 10-1z" class="V"></path><path d="M344 598c4 4 7 6 12 7 1 0 2 1 3 1v1c2 0 4-1 6-2 1 0 1-1 1-1l2-1 2 1h-1l1 1c0 1-1 1-2 2l-4 2c-4 2-7 2-11 1h-1c-3-1-8-5-10-8l2-4z" class="e"></path><path d="M369 604l1 1c0 1-1 1-2 2l-4 2c-4 2-7 2-11 1 0-1 2-1 3-1h0c5 1 10-2 13-5z" class="W"></path><path d="M344 598c4 4 7 6 12 7 1 0 2 1 3 1v1c-3 0-5 0-7-1-2 0-4-2-6-2 2 3 5 4 8 5h2c-1 0-3 0-3 1h-1c-3-1-8-5-10-8l2-4z" class="E"></path><path d="M371 571l1 2h0v7c-1 2-2 4-5 6v1h0l-1 1c-2 1-3 2-6 3l-1-2c-1-1-3-3-3-5v-1c1-1 1-1 1-2 1-1 2-2 3-2v1s1 0 1 1c1-1 1-3 1-4v1 2l1 1v1h1c1-1 1-3 1-4 1-1 0-2 0-3v-1l2-1 1 2v-2l1 1v-1h1l1-2z" class="N"></path><path d="M360 580s1 0 1 1v1 2c-1 0-1 0-2-1 0-1 0-2 1-3z" class="j"></path><path d="M371 571l1 2h0v7c-1 2-2 4-5 6v1h0l-1 1-1-1 3-2c2-3 2-6 2-9l-1-2v-1h1l1-2z" class="M"></path><path d="M371 571l1 2h0c-1 1-1 2-2 3l-1-2v-1h1l1-2z" class="P"></path><path d="M367 573l1 2c0 3-1 6-4 8 0 1-1 1-1 1h-1l-1-2v-1c1-1 1-3 1-4v1 2l1 1v1h1c1-1 1-3 1-4 1-1 0-2 0-3v-1l2-1z" class="E"></path><path d="M366 588c-2 1-3 2-6 3l-1-2c-1-1-3-3-3-5v-1c1-1 1-1 1-2 1-1 2-2 3-2v1c-1 1-1 2-1 3 0 2 0 4 1 5 1 0 2 1 2 1h1c0-1-1-1-1-2-1 0-2-1-2-2h0 1c1 1 2 1 3 2h1l1 1z" class="L"></path><path d="M338 580c2-6 6-14 11-17l6-3c1 0 1 0 2-1 3 0 6 0 8 2h1v3h1v1c2 1 3 3 4 6h0l-1 2h-1v1l-1-1v2l-1-2h0c-2-4-5-6-9-7h-2c-1 0-3 1-4 2-3 1-5 3-7 5h0c0 2-1 4-1 5h-1c-2 4-2 9-3 13l1 4 2 3h1l-2 4c2 3 7 7 10 8h1c4 1 7 1 11-1l2 1-3 3h0c-2 1-4 3-7 4l-4 1c-2 2-4 4-6 5h0c-2 1-2 1-3 0-1 0-1 0-2-1v-2h1v-1c1-1 0-2 0-2v-3c0-2-1-4-1-5v-4l-2-2h2c1-2 0-1-1-2s-1-3-1-4h0c0-1 0-2-1-4 0 0 0-1-1-2v-2l1-9h0z" class="B"></path><path d="M359 563h-3v-1c1-1 2-1 3-1v2z" class="X"></path><path d="M341 595l2 3c-1 1-1 2-2 3v-1c-1-1-1-1-1-2v-2h1v-1z" class="D"></path><path d="M366 561v3h1v1c-1 1 0 2 0 4l-3-3-2-1 3-3s1 0 1-1z" class="E"></path><path d="M365 562v3l-1 1-2-1 3-3z" class="Z"></path><path d="M367 565c2 1 3 3 4 6h0l-1 2h-1c-1-1-1-3-2-4 0-2-1-3 0-4z" class="W"></path><path d="M340 596c-2-5-2-8-2-13l2 8 1 4v1h-1z" class="E"></path><path d="M359 561h1c1-1 4 0 5 0h1c0 1-1 1-1 1l-3 3c-1-1-2-1-3-2v-2z" class="C"></path><path d="M353 614l-2-1h1c3 1 6 1 9 0 0-1 1-1 2 0-2 1-4 3-7 4h0l-3-3z" class="D"></path><path d="M353 614h4 0c0 1-1 2-1 3l-3-3z" class="E"></path><path d="M345 573h0v-2c1-1 3-4 5-5 2-2 5-2 7-3 2 1 3 1 5 2h-1-1c-1 0-3 0-4 1-1 0-3 1-4 2-3 1-5 3-7 5h0zm-6 30h2l1 3v1c1 2 0 3 1 5h0c1 1 1 2 2 2v-4h1v1c0 3 0 6-1 8-1 1-2 3-2 4-1 0-1 0-2-1v-2h1v-1c1-1 0-2 0-2v-3c0-2-1-4-1-5v-4l-2-2z" class="N"></path><path d="M346 611h1c2 1 4 4 6 4v1 1l-1 1c-2 2-4 4-6 5h0c-2 1-2 1-3 0 0-1 1-3 2-4 1-2 1-5 1-8z" class="E"></path><path d="M346 611h1c0 1-1 2 0 3h0 2 0c-1 2-1 4-3 5h-1c1-2 1-5 1-8z" class="V"></path><path d="M346 623c0-1 1-1 2-2v-1c0-1 0-2 1-2 1-1 3-1 4-1l-1 1c-2 2-4 4-6 5z" class="C"></path><path d="M337 578c1 0 1 0 1 1v1h0l-1 9v2c1 1 1 2 1 2 1 2 1 3 1 4h0c0 1 0 3 1 4s2 0 1 2h-2l2 2v4c0 1 1 3 1 5v3s1 1 0 2v1h-1v2c1 1 1 1 2 1 1 1 1 1 3 0h0c2-1 4-3 6-5l4-1c3-1 5-3 7-4h0l3-3 9-5c1 0 1-1 2-1l6-2 6-2c2 0 4 0 6-1h1c0 1 1 1 1 2-12 3-26 8-35 16-10 9-23 19-27 32v-3h-1 0c-1-2-1-3-1-5h-1c-1-1-2-2-2-4h1v-3c0-1 0-2-1-3 1-1 1-1 2-1l-1-1c0-1-1-1-2-1v-1h-1v-2c1 0 1-1 2-2h0c1-1 1-3 1-4l1 1v-1l1-2c-1-1 0-2 0-4v-4c1-4 1-9 1-14v-12-1-2h1v2c1-2 1-3 2-4h0z" class="a"></path><path d="M336 634c0-1 1-2 2-3l1 2c-2 3-3 6-4 9h-1c0-2 1-7 2-8z" class="V"></path><path d="M336 605v5c1 1 1 2 1 3 0 0 0 1 1 1l-1 1c0 2 2 2 1 4l-2 2v4c0-1-1-2-1-3l1-1c0-1 0-1-1-2v-5l1-9z" class="E"></path><path d="M334 632h1c0 1 0 1 1 2-1 1-2 6-2 8h1c0 1 0 2-1 4h0c-1-2-1-3-1-5h-1c-1-1-2-2-2-4h1v-3h1l1 1h1v-3z" class="B"></path><path d="M331 634h1l1 1h1l-2 6c-1-1-2-2-2-4h1v-3z" class="U"></path><path d="M336 625v-4l2-2c0 1-1 1 0 2 0 1 0 2-1 3l-1 1c1 1 2 2 3 1h2c1 0 1 0 1 1l-3 6-1-2c-1 1-2 2-2 3-1-1-1-1-1-2h-1l1-6 1-1z" class="C"></path><path d="M335 632c0-1 0-2 1-3 1 0 2 1 2 1v1h0c-1 1-2 2-2 3-1-1-1-1-1-2z" class="a"></path><path d="M334 583c1 3 0 6 1 9l1 13-1 9v5h-1 0l-1-2c-1-1 0-2 0-4v-4c1-4 1-9 1-14v-12z" class="c"></path><path d="M341 620c0-1-1-3-1-5s0-4-1-6-1-4-1-6c-1-3-1-6-1-9 1 3 2 6 2 9l2 2v4c0 1 1 3 1 5v3s1 1 0 2v1h-1z" class="O"></path><path d="M333 617l1 2h0 1c1 1 1 1 1 2l-1 1c0 1 1 2 1 3l-1 1-1 6v3h-1l-1-1h-1c0-1 0-2-1-3 1-1 1-1 2-1l-1-1c0-1-1-1-2-1v-1h-1v-2c1 0 1-1 2-2h0c1-1 1-3 1-4l1 1v-1l1-2z" class="I"></path><path d="M330 623h2l1 1-1 1-1 1h0c1 1 1 2 1 3h-1c0-1-1-1-2-1v-1h-1v-2c1 0 1-1 2-2z" class="G"></path><path d="M333 617l1 2h0 1c1 1 1 1 1 2l-1 1c0 1 1 2 1 3l-1 1-2-2-1-1h-2 0c1-1 1-3 1-4l1 1v-1l1-2z" class="F"></path><path d="M335 619c1 1 1 1 1 2l-1 1v-1c-1 0-1 1-2 1h0c0-2 0-2 1-3h0 1z" class="M"></path><path d="M333 622h0c1 0 1-1 2-1v1c0 1 1 2 1 3l-1 1-2-2v-2z" class="Z"></path><defs><linearGradient id="n" x1="154.294" y1="280.286" x2="168.037" y2="187.259" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#393939"></stop></linearGradient></defs><path fill="url(#n)" d="M112 138h1 19 22c7 0 17-1 24 3h1c2 1 4 3 6 4l3 4h1 0c1-1 2-1 3-1l3 2-1 1c0 1 0 1 1 2h0l2 2s1 1 2 1c1 2 2 3 3 4 0 1 1 2 1 4 1-1 1-1 2-1v1l2 1v4c-1 2-3 5-3 8l1 1 2-2h1l3-2h0 0c-1 1-2 2-2 3h0c-2 2-5 4-7 6-5 5-11 10-15 16-16 19-25 41-34 64-3 6-6 13-7 20v3c1-1 1-1 2-1h1v1l-1 3c-1 5-3 11-4 16l-7 32v-2c-1-3-1-7-1-9v-11l2-8v-1c1-1 0-2 1-4v-9h0v-5c1-1 0-2 0-3 1-2 1-5 1-8v-5l1 6v-7-14c0-1-1-1-1-2v-12-1h-4v-10-2c1-1 3-2 4-3s1-3 2-4c0-3-1-6-1-8v-11-6l-1-1c1-1 1-1 0-2v-1-7c0-1 0-2-1-3 1-3 1-6 1-9-1-1-1-2-1-3l-2-8c-1-1-2-5-3-6v-1c-1 0-3-4-4-5 0 0 0-1-1-1-3-4-5-8-9-10h0c-3-1-5-2-8-3z"></path><path d="M166 212v-1c0-1 0-2 1-2l2 1-2 2h-1z" class="N"></path><path d="M146 251v3c1 0 2 1 2 2l-2 4v-9z" class="e"></path><path d="M164 213v-2h1v2l1 1 1-2v1c-1 3-2 6-4 9v-1-4l1-3v-1z" class="P"></path><path d="M163 209h0c1 0 1 1 1 1v1l1-1v-2h1 0l1 1c-1 0-1 1-1 2v1h1l-1 2-1-1v-2h-1v2l-1 1c-2 1-2 4-3 6 0-4 1-8 3-11z" class="E"></path><path d="M143 263h0v8c0 2 0 4 1 5v3-1c0-1 0-1 1-2v1c-1 2-1 4-1 7 0 2 0 4-1 6h-1v-8c1-2 1-5 1-7v-12z" class="N"></path><path d="M164 213v1l-1 3v4 1c0 2-1 4-2 6l-1 1c-1-1-1-2-1-3v-1c1-1 1-4 1-5 1-2 1-5 3-6l1-1z" class="K"></path><path d="M160 220c1-2 1-5 3-6-1 2-2 4-2 6v1c0 1 1 1 0 2 0 1-1 3 0 5l-1 1c-1-1-1-2-1-3v-1c1-1 1-4 1-5z" class="W"></path><path d="M187 187h1c1 0 1 0 2-1h2c-1 1-2 2-4 3h0c-3 2-6 5-8 9l-2 2c-1 1-1 2-2 3 0-1-1-1-1-2v-2c2-1 3-2 4-3v-3-1c3-2 5-4 8-5z" class="Z"></path><path d="M179 192l2 1c0 1-1 2-2 3v-3-1z" class="C"></path><path d="M187 187h1c1 0 1 0 2-1h2c-1 1-2 2-4 3-1-1-1-1-1 0-2 1-4 3-6 4l-2-1c3-2 5-4 8-5z" class="D"></path><path d="M153 241v-1c1 1 1 2 1 4-1 1-2 3-3 4-1 3-1 5-3 8 0-1-1-2-2-2v-3c0-4-1-8-1-11l1 1s1 0 2-1h1 0 1 1s0 1 1 1h1z" class="L"></path><path d="M152 241h1v2h-2c0-1 0-1 1-2z" class="E"></path><path d="M148 240h1 0 1 1s0 1 1 1c-1 1-1 1-1 2l-2-1h0c0-1-1-1-1-2z" class="C"></path><path d="M176 191l2 2h1v3c-1 1-2 2-4 3v2c0 1 1 1 1 2l-2 2c0 1-1 2-1 3l-1 1-1-1h-1v1l-1 1-2-1-1-1h0c1-3 1-5 3-7 0-1 0-2 1-3 2-3 3-5 6-7z" class="J"></path><path d="M173 204v-1h-2c0-1 1-2 1-3l1 1h2c0 1 1 1 1 2l-2 2-1-1z" class="M"></path><path d="M173 204l1 1c0 1-1 2-1 3l-1 1-1-1h-1v1l-1 1-2-1-1-1c1 0 1 0 2 1 1-1 1-3 1-4 1-1 1-1 2-1v1h2v-1z" class="W"></path><path d="M173 204l1 1c0 1-1 2-1 3h-1c-1-1-1-2-1-3h2v-1z" class="d"></path><path d="M178 193h1v3c-1 1-2 2-4 3v2h-2l-1-1c2-2 4-5 6-7z" class="H"></path><path d="M189 176l3 1h0 0 5l-1 1v2c-1 1-2 1-3 2s-3 3-4 3l-2 2c-3 1-5 3-8 5v1h-1l-2-2c-3 2-4 4-6 7l-1-2h1v-1c1-1 0-2 0-3s1-1 1-2c1-1 3-2 5-2v-1c0-1 0-2-1-3l2-2v-2l1-1h2l1 2h3l3-3v-2h2z" class="V"></path><path d="M192 177h5l-1 1s-1 0-2 1h-1l-1-1v-1z" class="X"></path><path d="M176 191l2-2h0c2 0 6-1 8-2 1-1 1-2 3-2l-2 2c-3 1-5 3-8 5v1h-1l-2-2z" class="I"></path><path d="M177 180l1-1h2l1 2v1l3 1h-1l1 1 1-1 1 1c-2 0-4 1-6 2-1 0-3 1-4 2v-1c0-1 0-2-1-3l2-2v-2z" class="c"></path><path d="M177 180l1-1h2l1 2v1c-1 0-2 1-3 0h-1v-2z" class="G"></path><path d="M189 176l3 1h0 0v1c0 2 0 2-1 3s-3 2-5 3l-1-1-1 1-1-1h1l-3-1v-1h3l3-3v-2h2z" class="K"></path><path d="M189 176l3 1h0 0v1h-1s-1 0-1 1h0c-2 0 0-1-2-2l-1 1v-2h2z" class="C"></path><path d="M140 175c2 5 2 12 3 17 0 2 0 3 1 4h1v-2c-1-1-1-3 0-4-1-1-1-2-1-3l1-1c1 1 1 2 1 3h3v-1-2l1-1 3 3h0l1 6c0 1 0 2 1 4h0c-1 1-2 1-4 1h0c-1 1-1 1-1 2h0v3h1l-1 2h3l-2 2h1v1c0 1-1 2-1 2v1h-2v-1c0-1 0-1-1-2h-4l-1 54h0v12c0 2 0 5-1 7v8-1c0-1 0-2-1-3h0c1-1 0-1 0-1-1-1 0-5 0-7v-7-14c0-1-1-1-1-2v-12-1h-4v-10-2c1-1 3-2 4-3s1-3 2-4c0-3-1-6-1-8v-11-6l-1-1c1-1 1-1 0-2v-1-7c0-1 0-2-1-3 1-3 1-6 1-9z" class="H"></path><path d="M151 208h1v1c0 1-1 2-1 2h-1v-2-1h1z" class="B"></path><path d="M145 190c-1-1-1-2-1-3l1-1c1 1 1 2 1 3 0 2 0 2 1 3v2 1 1h-1v-2h-1v-4zm2 11h3 0v3h1l-1 2h3l-2 2h-1-5v-6l1 1v-1h1v-1z" class="M"></path><path d="M147 201h3 0v3h1l-1 2h-2l-1-1c0-1 1 0 2-1v-1h-3v-1h1v-1z" class="K"></path><path d="M154 193v1c0 1 0 2 1 4h0c-1 1-2 1-4 1h0c-1 1-1 1-1 2h-3v1h-1v1l-1-1v-4h1 1v-1-1-1h0c1-1 1-1 2-1h0l2 1 2 1h0 1v-3z" class="P"></path><path d="M147 201l-1-1v-1h3 2c-1 1-1 1-1 2h-3z" class="J"></path><path d="M154 193v1c0 1 0 2 1 4h0c-1 1-2 1-4 1-1-1 0-1 0-2 1 0 1 0 2-1h0 0 1v-3z" class="C"></path><path d="M150 185l3 3h0l1 6v-1 3h-1 0l-2-1-2-1h0c-1 0-1 0-2 1h0v-1-2c-1-1-1-1-1-3h3v-1-2l1-1z" class="b"></path><path d="M151 195l1-2h1 1v3h-1 0l-2-1z" class="X"></path><path d="M150 185l3 3v3h-1c-1 1-2 2-3 2h0v-2-2-1-2l1-1z" class="a"></path><path d="M140 187c1 3 1 6 1 9 1 4 2 7 2 11v23 33 12c0 2 0 5-1 7v8-1c0-1 0-2-1-3h0c1-1 0-1 0-1-1-1 0-5 0-7v-7-14c0-1-1-1-1-2v-12-1h-4v-10-2c1-1 3-2 4-3s1-3 2-4c0-3-1-6-1-8v-11-6l-1-1c1-1 1-1 0-2v-1-7z" class="S"></path><path d="M140 243c2 3 1 8 2 12v8 13 6 8-1c0-1 0-2-1-3h0c1-1 0-1 0-1-1-1 0-5 0-7v-7-14c0-1-1-1-1-2v-12z" class="T"></path><path d="M165 188c0 2 0 2 1 2h1c0-1 0-1 1-2h1l1 2h1c0 1-1 1-1 2s1 2 0 3v1h-1l1 2c-1 1-1 2-1 3-2 2-2 4-3 7h-1v2l-1 1v-1s0-1-1-1h0c-2 3-3 7-3 11 0 1 0 4-1 5v1c0 1 0 2 1 3-1 0-1 0-2 1l-1 1c1 1 1 2 1 3l-1 1-1-3c-1 1-2 2-2 3-1 1-1 4-1 5v1h-1c-1 0-1-1-1-1h-1-1 0-1c-1 1-2 1-2 1l-1-1v-4-6-2-4l-1-2 1-1h0c0-2 0-4 1-6v-1c1-1 2-2 3-2h2v-1s1-1 1-2v-1h-1l2-2h-3l1-2h-1v-3h0c0-1 0-1 1-2h0c2 0 3 0 4-1s2-3 3-4v-1l2-2h1v-2h1v-1h1c1 1 1 2 1 2s0-1 1-2z" class="c"></path><path d="M157 206h0l-2 7h0l-2-2 1-2h-1c1-2 2-3 4-3z" class="H"></path><path d="M160 200c2-3 4-5 7-8 0 1 1 1 1 2v1l-3 3h0l-1-1-2 2c0 1-1 1-1 2v1c-1 1-2 3-2 5h-1v-1h-1 0l1-3s1 0 1 1c0-2 0-3 1-4z" class="P"></path><path d="M164 197h0c1-2 1-2 3-3l1 1-3 3h0l-1-1z" class="U"></path><path d="M156 202c1 0 2-1 4-2-1 1-1 2-1 4 0-1-1-1-1-1l-1 3c-2 0-3 1-4 3h-1v-1h-1l2-2h-3l1-2 2-1 3-1z" class="W"></path><path d="M153 206c2-1 3-2 4-3h1l-1 3c-2 0-3 1-4 3h-1v-1h-1l2-2z" class="O"></path><path d="M154 216c1 2 1 2 1 4-1 2-3 5-5 7 0 1-1 2-2 2l-2 1v-1-2s3-2 4-3 2-3 3-5l1-3z" class="J"></path><path d="M148 233v1 4c1 1 1 1 2 1h0c1-1 1-2 2-3h0v-1l1-1c0-1 0-2 1-3h2v1c-1 1-2 2-2 3-1 1-1 4-1 5v1h-1c-1 0-1-1-1-1h-1-1 0-1c-1 1-2 1-2 1l-1-1v-4l1-1 1 2c1-1 0-2 1-4h0z" class="H"></path><path d="M150 216h1c1 1 2 2 2 3-1 2-2 4-3 5s-4 3-4 3v2 1l2-1 1 1-1 3h0c-1 2 0 3-1 4l-1-2-1 1v-6-2-4h1c1-1 3-2 4-3 0-1 0-1-1-2h-1l1-1c1 0 1-1 1-2z" class="O"></path><path d="M148 229l1 1-1 3h0c-1 2 0 3-1 4l-1-2v-1-4l2-1z" class="e"></path><path d="M170 192c0 1 1 2 0 3v1h-1l1 2c-1 1-1 2-1 3-2 2-2 4-3 7h-1v2l-1 1v-1s0-1-1-1h0c0-1 1-2 1-3h0c0-1 0-1-1-2l-2 1 4-7h0l3-3v-1l2-2z" class="D"></path><path d="M170 192c0 1 1 2 0 3v1h-1-1v1l1 1-1 1s-1 0-1 1h-1v1l-2 2-1 1h0 0l-2 1 4-7h0l3-3v-1l2-2z" class="C"></path><path d="M163 204c1 1 1 1 1 2h0c0 1-1 2-1 3-2 3-3 7-3 11 0 1 0 4-1 5v1c0 1 0 2 1 3-1 0-1 0-2 1l-1 1c0-1 0-3-1-3v-2c-1-5 2-11 3-16 1-2 1-3 2-5l2-1z" class="L"></path><path d="M156 226c0-1 1-2 1-3l1-1c0 1 1 2 1 3v1c0 1 0 2 1 3-1 0-1 0-2 1l-1 1c0-1 0-3-1-3v-2z" class="B"></path><path d="M156 228c2-1 2-1 3-2 0 1 0 2 1 3-1 0-1 0-2 1l-1 1c0-1 0-3-1-3z" class="C"></path><path d="M153 209h1l-1 2 2 2h0v1l-1 2-1 3c0-1-1-2-2-3h-1c0 1 0 2-1 2l-1 1h1c1 1 1 1 1 2-1 1-3 2-4 3h-1l-1-2 1-1h0c0-2 0-4 1-6v-1c1-1 2-2 3-2h2v-1s1-1 1-2h1z" class="E"></path><path d="M152 214v-1c1 0 2 1 2 1h1l-1 2-1 3c0-1-1-2-2-3h-1l2-2z" class="D"></path><path d="M152 214v-1c1 0 2 1 2 1v1c-1 1-1 0-2 0v-1z" class="C"></path><path d="M146 215v-1c1-1 2-2 3-2h2c-3 3-4 5-5 8l1 1-1 1v-1c-1-1 0-4 0-6z" class="V"></path><path d="M146 215c0 2-1 5 0 6v1l1-1 1-2h1c1 1 1 1 1 2-1 1-3 2-4 3h-1l-1-2 1-1h0c0-2 0-4 1-6z" class="B"></path><path d="M165 188c0 2 0 2 1 2h1c0-1 0-1 1-2h1l1 2h1c0 1-1 1-1 2l-2 2c0-1-1-1-1-2-3 3-5 5-7 8-2 1-3 2-4 2l-3 1-2 1h-1v-3h0c0-1 0-1 1-2h0c2 0 3 0 4-1s2-3 3-4v-1l2-2h1v-2h1v-1h1c1 1 1 2 1 2s0-1 1-2z" class="R"></path><path d="M170 190h1c0 1-1 1-1 2l-2 2c0-1-1-1-1-2 1-1 2-2 3-2z" class="E"></path><path d="M162 189v-1h1c1 1 1 2 1 2-1 1-1 2-2 3l-2 2c0 1-1 1-1 2v-2l-1-1v-1l2-2h1v-2h1z" class="J"></path><path d="M162 189v-1h1c1 1 1 2 1 2-1 1-1 2-2 3l-1-1h1v-3z" class="c"></path><path d="M158 194l1 1v2c0 1 0 1-1 2-1 0-1 0-2 1v2l-3 1-2 1h-1v-3h0c0-1 0-1 1-2h0c2 0 3 0 4-1s2-3 3-4z" class="S"></path><path d="M153 201h0c1-1 1-1 2-1h1v2l-3 1v-2z" class="G"></path><path d="M150 201h1c1 0 1 0 2-1v1 2l-2 1h-1v-3z" class="F"></path><defs><linearGradient id="o" x1="180.339" y1="253.773" x2="156.128" y2="244.475" xlink:href="#B"><stop offset="0" stop-color="#acabac"></stop><stop offset="1" stop-color="#f2f0f1"></stop></linearGradient></defs><path fill="url(#o)" d="M207 176h1l3-2h0 0c-1 1-2 2-2 3h0c-2 2-5 4-7 6-5 5-11 10-15 16-16 19-25 41-34 64-3 6-6 13-7 20v3c1-1 1-1 2-1h1v1l-1 3c-1 5-3 11-4 16l-7 32v-2c-1-3-1-7-1-9v-11l2-8v-1c1-1 0-2 1-4v-9h0v-5c1-1 0-2 0-3 1-2 1-5 1-8v-5l1 6c0 2-1 6 0 7 0 0 1 0 0 1h0c1 1 1 2 1 3v1h1c1-2 1-4 1-6 0-3 0-5 1-7 3-12 8-23 12-33l15-30 10-13c1-3 4-7 6-9 0-1 1-1 1-3h-1 0c2-1 3-2 4-3h-2c-1 1-1 1-2 1h-1l2-2c1 0 3-2 4-3s2-1 3-2v-2l1-1h1 3l1 1v1h1l1-2 1 1 2-2z"></path><path d="M146 283v3c1-1 1-1 2-1h1v1l-1 3h-2 0 0c-1-1 0-5 0-6z" class="Y"></path><path d="M188 192c1-2 3-3 5-4l-11 13c1-3 4-7 6-9z" class="C"></path><path d="M198 177h3l1 1v1h1l1-2 1 1-9 8s-2 2-3 2c-2 1-4 2-5 4 0-1 1-1 1-3h-1 0c2-1 3-2 4-3h-2c-1 1-1 1-2 1h-1l2-2c1 0 3-2 4-3s2-1 3-2v-2l1-1h1z" class="L"></path><path d="M198 177h3l1 1-2 2h-2l-2 1v-1-2l1-1h1z" class="a"></path><path d="M112 138h1 19 22c7 0 17-1 24 3h1c2 1 4 3 6 4l3 4h1 0c1-1 2-1 3-1l3 2-1 1c0 1 0 1 1 2h0l2 2s1 1 2 1c1 2 2 3 3 4 0 1 1 2 1 4 1-1 1-1 2-1v1l2 1v4c-1 2-3 5-3 8l-1 2h-1v-1l-1-1h-3-1-5 0 0l-3-1h-2v2l-3 3h-3l-1-2h-2l-1 1v2l-2 2c1 1 1 2 1 3v1c-2 0-4 1-5 2h-1l-1-2h-1c-1 1-1 1-1 2h-1c-1 0-1 0-1-2-1 1-1 2-1 2s0-1-1-2h-1v1h-1v2h-1l-2 2v1c-1 1-2 3-3 4h0c-1-2-1-3-1-4l-1-6h0l-3-3-1 1v2 1h-3c0-1 0-2-1-3l-1 1c0 1 0 2 1 3-1 1-1 3 0 4v2h-1c-1-1-1-2-1-4-1-5-1-12-3-17-1-1-1-2-1-3l-2-8c-1-1-2-5-3-6v-1c-1 0-3-4-4-5 0 0 0-1-1-1-3-4-5-8-9-10h0c-3-1-5-2-8-3z" class="e"></path><path d="M156 150h1c1 1 1 1 0 2-2 2-4 4-5 6v-4c1-1 1-1 1-2l3-2zm8 2c3-1 5-1 8 1 1 0 3 2 4 3l-2 2c0-1-1-2-2-2 0-1-1-2-2-3-2-1-4-1-7 0 0-1 0-1 1-1z" class="I"></path><path d="M156 175h0c-2-5-3-10-1-15 2-4 5-7 9-8-1 0-1 0-1 1l-1 1c-3 2-5 5-6 7s-1 5 0 7v1c1 2 0 3 2 5v-1-1h0c0-1 0-1 1-1 1 2 2 4 4 6v1 1 1h0c-3-1-4-3-6-4l-1-1z" class="M"></path><path d="M158 174v-1-1h0c0-1 0-1 1-1 1 2 2 4 4 6v1 1c-1 0-2-1-3-2s-2-1-2-3z" class="B"></path><path d="M163 153c3-1 5-1 7 0 1 1 2 2 2 3h0-1-5c-3 2-5 3-6 5 0 1-1 3-1 3-1 2-1 3-1 4l1 3c-1 0-1 0-1 1h0v1 1c-2-2-1-3-2-5v-1c-1-2-1-5 0-7s3-5 6-7l1-1z" class="L"></path><path d="M158 168h0v-4c-1 0-1 0-1-1 1-1 2-1 3-2 0 1-1 3-1 3-1 2-1 3-1 4z" class="V"></path><path d="M160 161c1-2 3-3 6-5 0 1 1 2 1 3h-1l-1 1h0c1 1 1 2 1 3h-1c0 1 0 2 1 2-1 2-1 4-1 6v1l2 2c-2 0-3 0-5-1 1 1 2 2 3 4h-2c-2-2-3-4-4-6l-1-3c0-1 0-2 1-4 0 0 1-2 1-3z" class="J"></path><path d="M158 168c0-1 0-2 1-4 0 2 0 4 1 6s2 2 2 3c1 1 2 2 3 4h-2c-2-2-3-4-4-6l-1-3z" class="E"></path><path d="M165 160c1 1 1 2 1 3h-1c0 1 0 2 1 2-1 2-1 4-1 6v1h0c-2 0-2-2-3-3v-5c1-2 2-3 3-4z" class="L"></path><path d="M162 164v3h2c1-1 1-2 1-4h0c0 1 0 2 1 2-1 2-1 4-1 6v1h0c-2 0-2-2-3-3v-5z" class="H"></path><defs><linearGradient id="p" x1="178.657" y1="146.383" x2="157.998" y2="152.206" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#p)" d="M160 144h2 0c1-1 2-2 4-2 1 0 1 1 2 1 1 1 3 1 4 1 3 1 6 4 8 6 1 2 1 3 1 4h-1l-1-1-1 1c-2-2-4-3-7-5-4-1-7-1-12 1l-2 2c1-1 1-1 0-2h-1c1-1 5-4 5-5l-1-1z"></path><path d="M171 149h3 0c2 1 4 2 5 4l-1 1c-2-2-4-3-7-5z" class="W"></path><path d="M172 156c1 0 2 1 2 2l2-2c1 3 1 6 0 8s-2 3-3 5c-1 0-1-1-2-1s-1 0-2 1h-1l-1 1-1-1c-1 2 0 2-1 3v-1c0-2 0-4 1-6-1 0-1-1-1-2h1c0-1 0-2-1-3h0l1-1h1c0-1-1-2-1-3h5 1 0z" class="U"></path><path d="M172 160h0v3c-1 1-2 2-3 2l1-3 2-2z" class="Q"></path><path d="M172 156c1 0 2 1 2 2 1 1 1 2 1 3l-2 1c0-1 0-2-1-3 0-1 0-2-1-3h1 0z" class="W"></path><path d="M166 156h5c1 1 1 2 1 3v1h0l-2 2-1 3c-1 0-2 1-3 1v-1c-1 0-1-1-1-2h1c0-1 0-2-1-3h0l1-1h1c0-1-1-2-1-3z" class="I"></path><path d="M169 161v1h1l-1 3c-1 0-2 1-3 1 0-2 1-2 2-3s1-2 1-2z" class="J"></path><path d="M169 161l-1-5c2 1 3 2 4 4l-2 2h-1v-1zm-20 13c2-2 1-3 1-5v-6c0 5 1 8 3 12h3l1 1c2 1 3 3 6 4h0v-1-1l2 2c2 0 3 1 5 1h1c1 0 3 0 4-1h2v2l-2 2c1 1 1 2 1 3v1c-2 0-4 1-5 2h-1l-1-2h-1c-1 1-1 1-1 2h-1c-1 0-1 0-1-2h0c-1-1-1-1-2-1v-1h0l-1-1h0c-4-2-6-4-8-6 0-1-1-2-2-2 0 0-1 1-2 0v-2l-1-1z" class="R"></path><path d="M170 187c1-1 1 0 2-1h2 0s0 1 1 1h1v1c-2 0-4 1-5 2h-1l-1-2h2v-1h-1z" class="Q"></path><path d="M163 186c2 0 5 1 7 1h1v1h-2-1c-1 1-1 1-1 2h-1c-1 0-1 0-1-2h0c-1-1-1-1-2-1v-1h0z" class="c"></path><path d="M153 175h3l1 1c2 1 3 3 6 4h0v-1-1l2 2c2 0 3 1 5 1h1c1 0 3 0 4-1h2v2l-2 2c-3 1-7 1-10 0-5-1-9-5-12-9z" class="O"></path><path d="M145 181l1-1h0c1-3 0-5 0-7l2-2 1 3 1 1v2c1 1 2 0 2 0 1 0 2 1 2 2 2 2 4 4 8 6h0l1 1h0v1c1 0 1 0 2 1h0c-1 1-1 2-1 2s0-1-1-2h-1v1h-1v2h-1l-2 2v1c-1 1-2 3-3 4h0c-1-2-1-3-1-4l-1-6h0l-3-3h-1-1c-1-1-2-2-3-4z" class="I"></path><path d="M153 188c1 0 2 1 2 1h0c2 0 4 1 5 2l-2 2v1c-1 1-2 3-3 4h0c-1-2-1-3-1-4l-1-6z" class="K"></path><path d="M155 189c2 0 4 1 5 2l-2 2-1-1c-1 0-1 1-2 0v-3z" class="L"></path><path d="M162 185c0 1-1 1-2 2l1 1h-1c-1 0-2-1-4-1 0 0-1 0-2-1-2-2-6-6-6-9 0-1 0-2-1-2v-2h1v1c1 0 1 1 2 1v2c1 1 2 0 2 0 1 0 2 1 2 2 2 2 4 4 8 6h0z" class="U"></path><path d="M150 177c1 1 2 0 2 0 1 0 2 1 2 2h-1v2h0-1c-1-1-1-2-2-4z" class="d"></path><defs><linearGradient id="q" x1="163.55" y1="155.547" x2="142.321" y2="160.942" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#a2a0a0"></stop></linearGradient></defs><path fill="url(#q)" d="M146 153h0c1-3 4-5 6-7 2-1 6-3 8-2l1 1c0 1-4 4-5 5l-3 2c0 1 0 1-1 2v4l-1 5h-1v6c0 2 1 3-1 5l-1-3-2 2c0 2 1 4 0 7h0l-1 1c0-1-1-2-1-3 0 0 0-1 1-1 0-1-2-13-2-15 0-4 1-6 3-9z"></path><path d="M150 155v-2s0-1 1-2l2 1c0 1 0 1-1 2v4l-1 5h-1v6c0 2 1 3-1 5l-1-3c-1-2-2-4-2-6 0-3 2-7 4-10z" class="S"></path><path d="M150 155v-2s0-1 1-2l2 1c0 1 0 1-1 2v4l-1 5-1-2c0-3 1-4 1-6v-1h1v-1l-2 2z" class="J"></path><path d="M180 158v-1c2 1 2 3 2 4h1s1 0 1 1 1 1 1 2h0l1 1c0 2 0 4 1 5v1 1h-1v1c1 0 2 1 2 2h1v1h-2v2l-3 3h-3l-1-2h-2l-1 1h-2c-1 1-3 1-4 1h-1c-2 0-3-1-5-1l-2-2v-1h2c-1-2-2-3-3-4 2 1 3 1 5 1l-2-2c1-1 0-1 1-3l1 1 1-1h1 1c1 2 2 2 4 2v1l1-1c0-1-1-1-1-2h2c1 0 2-2 3-4 2-2 2-4 1-7z" class="W"></path><path d="M185 164l1 1c0 2 0 4 1 5l-1 1h-1-1l1 1-1 1c-1-1-1-2-2-3l3-6z" class="V"></path><path d="M182 161h1s1 0 1 1 0 3-1 5c0 0-2 3-3 3-1 1-2 1-3 1 2-2 3-3 4-6 1-1 1-2 1-4z" class="b"></path><path d="M180 158v-1c2 1 2 3 2 4 0 2 0 3-1 4-1 3-2 4-4 6h-1v-2c1 0 2-2 3-4 2-2 2-4 1-7z" class="Q"></path><path d="M180 177c1-1 1-2 3-3 1 1 1 1 2 1h1l1 1v2l-3 3h-3l-1-2v-2z" class="f"></path><path d="M180 177c1-1 1-2 3-3 1 1 1 1 2 1h1c-1 1-1 2-2 2h-1v-1h-1 0c0 1 0 1-1 2 1 1 1 1 1 2l2 1h-3l-1-2v-2z" class="L"></path><path d="M173 177v-1c3-1 6-5 9-5v2c-2 1-4 0-5 2h0 2v1l-1 1h2v2h-2l-1 1h-2c-1 1-3 1-4 1h-1l-1-1 1-1 1-1h1l1-1z" class="D"></path><path d="M172 178h1c1 1 1 1 2 1v-1h0l1-1h0v1s1 0 1 1l1-2h2v2h-2l-1 1h-2c-1 1-3 1-4 1h-1l-1-1 1-1 1-1h1z" class="I"></path><path d="M165 172c1-1 0-1 1-3l1 1 1-1h1 1c1 2 2 2 4 2v1h0 2v1c-2 1-2 1-4 1 0 1 1 2 1 3l-1 1h-1l-1 1-1 1 1 1c-2 0-3-1-5-1l-2-2v-1h2c-1-2-2-3-3-4 2 1 3 1 5 1l-2-2z" class="d"></path><path d="M163 177h2l2 1v1h-1s-1 0-1 1l-2-2v-1z" class="V"></path><path d="M174 172h2v1c-2 1-2 1-4 1-1 1-1 0-2 0v-1c1-1 2-1 4-1zm-9 0c1-1 0-1 1-3l1 1 1-1h1 1c-1 2-1 2-1 3v1 1h-2l-2-2z" class="I"></path><path d="M189 149h0c1-1 2-1 3-1l3 2-1 1c0 1 0 1 1 2h0l2 2s1 1 2 1c1 2 2 3 3 4 0 1 1 2 1 4 1-1 1-1 2-1v1l2 1v4c-1 2-3 5-3 8l-1 2h-1v-1l-1-1h-3-1-5 0 0l-3-1v-1h-1c0-1-1-2-2-2v-1h1v-1-1c-1-1-1-3-1-5l-1-1h0c0-1-1-1-1-2s-1-1-1-1h-1c0-1 0-3-2-4v1h0l-2-4 1-1 1 1h1c0-1 0-2-1-4 2 2 4 5 5 7v-2l1 1s1 0 2 1h1l-2-4 1-1v-3h1z" class="C"></path><path d="M195 153l2 2s1 1 2 1v3l-3-3-2-1c1 0 1-1 1-2zm0 10c0-1-1-1-1-2v-1h1c1 1 1 2 2 3 0 1 1 1 1 1 1 0 0 2 0 3s-2 2-2 4c-2-1-2 0-3-1 1-1 2-1 3-2h0c1-1 0-1-1-2l2-2-1-1h-1z" class="Z"></path><path d="M195 163h1l1 1-2 2c1 1 2 1 1 2h0c-1 1-2 1-3 2-1 0-3-1-3-2h0 2l1-2c1 0 1-1 1-1l-1-1v-1h2z" class="K"></path><path d="M190 162v-1l2-1v4h1l1 1s0 1-1 1l-1 2h-2c0-1-1-1-1-1 0-1 1-1 1-2s0-1 1-2l-1-1z" class="B"></path><path d="M192 164h1l1 1s0 1-1 1-1 1-2 0c0-1 1-1 1-2h0zm8 1c1 1 1 2 2 2-1 2-2 4-4 5l-1 1c-4 0-6-2-9-5 0-1 0-1 1-1 0 0 1 0 1 1h0c0 1 2 2 3 2 1 1 1 0 3 1h0 1c1-1 2-4 3-6z" class="D"></path><path d="M199 156c1 2 2 3 3 4 0 1 1 2 1 4h0v1h2v1c-1 2-2 3-4 5v1h-3c2-1 3-3 4-5-1 0-1-1-2-2 0 0 1-2 1-3-1-1-2-2-2-3v-3z" class="W"></path><path d="M201 162c0 1 2 3 1 5h0c-1 0-1-1-2-2 0 0 1-2 1-3z" class="H"></path><path d="M189 149h0c1-1 2-1 3-1l3 2-1 1c0 1 0 1 1 2h0c0 1 0 2-1 2l2 1c-1 1-1 2-1 3l-2-1v1c2 0 2 0 2 1h0-1v1c0 1 1 1 1 2h-2v1h-1v-4l-2 1v1c-1-1-1-2-1-3v-2l-2-4 1-1v-3h1z" class="M"></path><path d="M190 151l2 1 2 3 2 1c-1 1-1 2-1 3l-2-1c-1-2-2-5-4-7h1z" class="B"></path><path d="M188 149h1l1 1v1h-1v1c0 1 1 3 1 5 1 1 1 2 2 3l-2 1v1c-1-1-1-2-1-3v-2l-2-4 1-1v-3z" class="X"></path><path d="M189 149h0c1-1 2-1 3-1l3 2-1 1c0 1 0 1 1 2h0c0 1 0 2-1 2l-2-3-2-1v-1l-1-1z" class="W"></path><path d="M190 150v-1c1 0 1 1 2 1v2l-2-1v-1z" class="K"></path><path d="M203 164c1-1 1-1 2-1v1l2 1v4c-1 2-3 5-3 8l-1 2h-1v-1l-1-1h-3-1-5 0 0l-1-2h2v-1-1c1 1 3 0 4 0l1-1h3v-1c2-2 3-3 4-5v-1h-2v-1h0z" class="I"></path><path d="M193 174v1h3l1-1 2 2h-1v1h-1-5 0 0l-1-2h2v-1z" class="J"></path><path d="M196 175l1-1 2 2h-1v1h-1-5 0c1-1 3 0 4-1v-1z" class="Q"></path><path d="M180 150c2 2 4 5 5 7v-2l1 1s1 0 2 1h1v2c0 1 0 2 1 3l1 1c-1 1-1 1-1 2s-1 1-1 2c-1 0-1 0-1 1 3 3 5 5 9 5-1 0-3 1-4 0v1 1h-2l1 2-3-1v-1h-1c0-1-1-2-2-2v-1h1v-1-1c-1-1-1-3-1-5l-1-1h0c0-1-1-1-1-2s-1-1-1-1h-1c0-1 0-3-2-4v1h0l-2-4 1-1 1 1h1c0-1 0-2-1-4z" class="d"></path><path d="M191 175c-1-1-1-2-3-2v-1-1c1 0 1 1 2 2h3v1 1h-2z" class="F"></path><defs><linearGradient id="r" x1="187.126" y1="162.428" x2="186.29" y2="154.813" xlink:href="#B"><stop offset="0" stop-color="#929193"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#r)" d="M185 157v-2l1 1s1 0 2 1h1v2 4h-1c-2-1-2-4-3-6h0z"></path><path d="M112 138h1 19 22c7 0 17-1 24 3h1c2 1 4 3 6 4l3 4v3l-1 1 2 4h-1c-1-1-2-1-2-1l-1-1v2c-1-2-3-5-5-7s-5-5-8-6c-1 0-3 0-4-1-1 0-1-1-2-1-2 0-3 1-4 2h0-2c-2-1-6 1-8 2-2 2-5 4-6 7h0c-2 3-3 5-3 9 0 2 2 14 2 15-1 0-1 1-1 1 0 1 1 2 1 3 1 2 2 3 3 4h1 1l-1 1v2 1h-3c0-1 0-2-1-3l-1 1c0 1 0 2 1 3-1 1-1 3 0 4v2h-1c-1-1-1-2-1-4-1-5-1-12-3-17-1-1-1-2-1-3l-2-8c-1-1-2-5-3-6v-1c-1 0-3-4-4-5 0 0 0-1-1-1-3-4-5-8-9-10h0c-3-1-5-2-8-3z" class="e"></path><path d="M147 140h4c-2 1-4 3-5 4l-1-1h0c1-1 1-1 1-2l1-1z" class="O"></path><path d="M140 140h7l-1 1c0 1 0 1-1 2h0l1 1v1c-1-1-2-1-3-1h0l-1-1 1-2h0-5-5c2-1 5-1 7-1z" class="V"></path><path d="M143 146c1 0 1 0 2-1-1 3-2 4-3 6-1 1-1 2-2 3 0-1 0-2 1-3v-2l-1-1-1 2h0v-1l-1-1h1c2 0 2 0 4-1v-1z" class="H"></path><path d="M138 141h5 0l-1 2 1 1h0c1 0 2 0 3 1h-1c-1 1-1 1-2 1 0 0 0-1-1-2 0 0-2-1-3-1s-1 1-2 1v-2l1-1z" class="E"></path><path d="M134 144h1c1-1 1-1 1-2h1v2c1 0 1-1 2-1s3 1 3 1c1 1 1 2 1 2v1c-2 1-2 1-4 1h-1l1 1v1l-1 2c0-1-1-2-2-3-1 0-1-1-2-2v1l-1-1h1 2 0l1 2 1-1h1l1-1c-1-2-1-1-3-1 0 0-1-1-1-2h-1-1z" class="W"></path><path d="M144 152c1 1 1 1 2 1-2 3-3 5-3 9 0 2 2 14 2 15-1 0-1 1-1 1-2-4-3-7-4-11 0-5 2-10 4-15z" class="T"></path><path d="M133 144h1 1 1c0 1 1 2 1 2 2 0 2-1 3 1l-1 1h-1l-1 1-1-2h0-2-1l1 1v-1c1 1 1 2 2 2 1 1 2 2 2 3 1 1 1 2 1 3h-1 0-1c0 2 0 2 1 4v3c-1-2-2-3-2-5l-1-1-6-10 2-1 2-1z" class="V"></path><path d="M134 148v-1c1 1 1 2 2 2 1 1 2 2 2 3 1 1 1 2 1 3h-1c-2-2-3-5-4-7h0z" class="I"></path><path d="M133 144h1 1 1c0 1 1 2 1 2 2 0 2-1 3 1l-1 1h-1l-1 1-1-2c-1-1-1-1-2-1-1-1-1-1-2 0v1c0 3 3 5 4 8l-1 1-6-10 2-1 2-1z" class="c"></path><defs><linearGradient id="s" x1="151.055" y1="137.702" x2="149.168" y2="161.167" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#s)" d="M112 138h1 19 22c7 0 17-1 24 3h1c2 1 4 3 6 4l3 4v3l-1 1 2 4h-1c-1-1-2-1-2-1l-1-1v2c-1-2-3-5-5-7s-5-5-8-6c-1 0-3 0-4-1-1 0-1-1-2-1-2 0-3 1-4 2h0-2c-2-1-6 1-8 2-2 2-5 4-6 7h0c-1 0-1 0-2-1 3-4 6-7 10-9v-1c1 0 1 0 2-1h0 0l-1-1c-4-1-11 0-15 0-2 0-5 0-7 1h5l-1 1h-1c0 1 0 1-1 2h-1-1l-2 1-2 1 6 10 1 1c0 2 1 3 2 5 0 1 0 2-1 2-1-1-2-5-3-6v-1c-1 0-3-4-4-5 0 0 0-1-1-1-3-4-5-8-9-10h0c-3-1-5-2-8-3z"></path><path d="M134 157h0c1 1 1 2 2 3h0l-1-2 1-1c0 2 1 3 2 5 0 1 0 2-1 2-1-1-2-5-3-6v-1z" class="N"></path><path d="M120 141c1-1 2-1 3 0 2 1 3 2 4 4 0 0 0 1 1 1v1h0c1 1 1 2 1 4-3-4-5-8-9-10h0z" class="B"></path><path d="M128 143l-1-2h6 5l-1 1h-1c0 1 0 1-1 2h-1-1l-2 1-2 1-1-3z" class="P"></path><path d="M130 144l1-2c1 0 1 0 2 1v1l-2 1-1-1z" class="E"></path><path d="M128 143l2-1v1 1l1 1-2 1-1-3z" class="K"></path><path d="M154 143c5-2 10-3 14-2 8 2 14 5 19 12l2 4h-1c-1-1-2-1-2-1l-1-1v2c-1-2-3-5-5-7s-5-5-8-6c-1 0-3 0-4-1-1 0-1-1-2-1-2 0-3 1-4 2h0-2c-2-1-6 1-8 2-2 2-5 4-6 7h0c-1 0-1 0-2-1 3-4 6-7 10-9z" class="R"></path><path d="M324 139h0l1 2 1 2c0 1 0 1 1 1v2l1 1v1h1l1-1c2 0 3 0 4 2v26 4h1c1 1 1 2 2 2v1h2v1h1v-2c0-1 1-1 1-2l2-3h0c2-1 3-2 5-3h2 2c1 0 1 1 2 1h2v3c2 1 2 3 3 4h-1v1h0v1s0 1 1 2c0 0-1 1-1 2-1 3-4 2-4 6 1 1 1 1 2 1l-1 1h0c-1 1-2 2-2 4h-3c0 1-1 2-1 4h-1v5 1h-3-1c0-1-2-1-2-1-2 0-4-1-6-1-1 0-2 0-3-1 1 4 1 9 1 13v5 1 2c-1-1-1-1-2-1h-2c1 0 2 1 3 2v3 1 6 3c0 1 0 2-1 2 1 1 1 2 1 2v4 9 3 1 2 2l1 2-1 22h-1-6c-5 0-11 1-15-1h-1c-4-1-8-5-11-8-2-1-3-3-3-5l-1-3c-1-7-2-14-1-21v-71l1-2v-3h-1l-1-2v-4c0-1 0-3 1-4l-1-1c1-1 1-3 2-4s2-3 3-4l1-1 2-2 1-1c2 0 3 0 4-1h0c1 0 1-1 2-1l6-3 1-1c0-1 1-1 2-1h1 1l-1-3 2-2c2-1 2 0 3-1 0 0 1 0 1-1z" class="a"></path><path d="M294 252h0c0 3 0 6 1 9v2-2c1 2 1 6 1 8v7l-1-3c-1-7-2-14-1-21z" class="O"></path><path d="M298 199h0 0c1 1 1 3 2 4l3 5c-2-1-3 0-4 0v1 1l-1-1v-1l-1-2c0-2 1-5 1-7z" class="T"></path><path d="M298 267v4c1 0 1 1 2 1l-2 2v3 2c1 1 2 1 2 2h-1c-2-1-3-3-3-5v-7h1 0l1-2z" class="L"></path><path d="M297 185c1 2 1 5 2 6v-5h0c0 4 0 8 2 13h-1v3 1c-1-1-1-3-2-4h0 0-1v-14z" class="C"></path><path d="M309 163h1c0 1 0 1 1 1-3 2-5 4-8 8-2 4-3 8-4 14h0v5c-1-1-1-4-2-6h0c0-2 0-4 1-5 1 0 1-1 1-2 1-4 3-9 6-12l2-2 2-1z" class="B"></path><path d="M297 206l1 2v1l1 1c1 1 1 2 1 4 4 4 6 8 7 13l-1-1-2-3h-2 0-1c0-1 0-2-1-3h-1l-1 1c-1 0-1-4-1-5l-1-3h0 0v-2c0-1 0-1 1-2v-3z" class="L"></path><path d="M296 213c2 1 3 3 5 5l-3 1-1-3-1-3z" class="Q"></path><path d="M297 206l1 2v1l1 1c1 1 1 2 1 4-1-1-2-2-3-2 0 0 0-1-1-1 0-1 0-1 1-2v-3z" class="S"></path><path d="M297 216l1 3 3-1c1 1 3 3 3 5h-2 0-1c0-1 0-2-1-3h-1l-1 1c-1 0-1-4-1-5z" class="U"></path><path d="M297 231h1c1 1 1 2 2 4 1 0 2 1 3 1 0 0 1 1 2 0 1 0 2-2 3-3v2c0 1 0 2-1 3l-4 4h0v4h0 0l-1 1c0 1 0 2-1 3 0-1 0-1-1-2h-1c-2 1-1 4-2 6v-6-17z" class="L"></path><path d="M303 242v4h0 0c-1-1-3-1-5-2 0-1 0-1 1-1v1c2 0 3-2 4-2z" class="Y"></path><path d="M298 244v-1c-1-2-1-10 0-12 1 1 1 2 2 4-1 1-1 3-1 4v1l1 1c0 1 0 1-1 2-1 0-1 0-1 1z" class="G"></path><path d="M308 233v2c0 1 0 2-1 3l-4 4h0c-1 0-2 2-4 2v-1c1-1 1-1 1-2l-1-1v-1c0-1 0-3 1-4 1 0 2 1 3 1 0 0 1 1 2 0 1 0 2-2 3-3z" class="h"></path><path d="M298 221l1-1h1c1 1 1 2 1 3h1 0 2l2 3 1 1c1 2 1 4 1 6-1 1-2 3-3 3-1 1-2 0-2 0-1 0-2-1-3-1-1-2-1-3-2-4h-1c0-1 0-2 1-3v-1-3-3z" class="e"></path><path d="M299 230c2-1 2-1 4-1 0 1 0 2 1 2 0 1-1 1-1 2h-1l-3-3z" class="G"></path><path d="M304 223l2 3 1 1c1 2 1 4 1 6-1 1-2 3-3 3-1 1-2 0-2 0h1c1-1 1-3 1-4s1-1 1-2h-1l-1 1v-1-1-2l-2-2c1-1 1-1 2-1v-1z" class="M"></path><path d="M298 221l1-1h1c1 1 1 2 1 3h1 0 2v1c-1 0-1 0-2 1l2 2v2 1 1h0c-1 0-1-1-1-2-2 0-2 0-4 1 0-2 0-5-1-6v-3z" class="S"></path><path d="M304 227l-1 1c-1 0-2 0-3-1h0c1-1 1-1 1-2v-1l1 1 2 2z" class="Y"></path><path d="M299 210v-1-1c1 0 2-1 4 0l1 1c4 6 11 7 11 15h0v5c-1 1-1 2-1 3h1c0 2-1 3-2 5-1 1-2 3-3 5-2 3-4 5-6 8-1-1-1 0-1 0l-1-3 1-1h0 0v-4h0l4-4c1-1 1-2 1-3v-2c0-2 0-4-1-6-1-5-3-9-7-13 0-2 0-3-1-4z" class="l"></path><path d="M308 235h1 1c-1 1-1 3-2 4v-1h-1c1-1 1-2 1-3z" class="T"></path><path d="M304 209c4 6 11 7 11 15h0-1v-2c-1-1-2-2-3-4l-3-3h0-1l-1 1h0c-1-2-1-2-1-3v-1h-2c-1-1-1-1-1-2l2-1z" class="R"></path><path d="M319 162c1 0 2 1 3 2 1 2 1 2 0 3 0 1-1 1-2 2-3 1-6 3-8 6 0 0 0 1-1 2v-3h-1 0 0l-1 2-2 6c0 3 1 7 2 10-1 0-1-1-2-1l-3 3c0 1-1 2-1 3-1 1 0 2-1 4l-1-2c-2-5-2-9-2-13 1-6 2-10 4-14 3-4 5-6 8-8 2 0 4-1 6-1h3c-1 0-1-1-1-1z" class="T"></path><path d="M307 171c1 1 1 2 1 3h2l-1 2-1-1h0c-1-1-1-1-2-1 0-1 1-2 1-3z" class="F"></path><path d="M307 171h0c0-1 0-1 1-2l4 2-2 3h0-2c0-1 0-2-1-3z" class="Y"></path><path d="M319 162c1 0 2 1 3 2 1 2 1 2 0 3 0 1-1 1-2 2-3 1-6 3-8 6 0 0 0 1-1 2v-3h-1 0l2-3c1-1 2-2 4-3 1-1 4-2 5-3-1-1-2-1-4-2h3c-1 0-1-1-1-1z" class="C"></path><defs><linearGradient id="t" x1="307.653" y1="180.743" x2="296.516" y2="194.364" xlink:href="#B"><stop offset="0" stop-color="#b7b7b4"></stop><stop offset="1" stop-color="#d9d8da"></stop></linearGradient></defs><path fill="url(#t)" d="M303 172l2 2c-1 1-3 2-3 3h0c2 0 2 0 3-1l1-1v1c0 1-1 2-2 4h0c1-1 2-2 3-4h0v-1c1 1 0 2 0 3h0v2h0v2c0 3 1 7 2 10-1 0-1-1-2-1l-3 3c0 1-1 2-1 3-1 1 0 2-1 4l-1-2c-2-5-2-9-2-13 1-6 2-10 4-14z"></path><path d="M304 180h0c1-1 2-2 3-4h0v-1c1 1 0 2 0 3h0v2h0v2c0 3 1 7 2 10-1 0-1-1-2-1l-3 3h0c-1 0-1 0-2-1 0-2 0-5 1-7l1-6z" class="i"></path><path d="M315 147c0 1 1 1 1 2l6-1c0 1 0 1-1 2h1l1 1h-1c0 2 1 2 2 4l-2 1v3l-1 1h-3v2h1s0 1 1 1h-3c-2 0-4 1-6 1-1 0-1 0-1-1h-1l-2 1-2 2c-3 3-5 8-6 12 0 1 0 2-1 2 0-3 1-6 1-9 0 1-1 2-2 4 0 0 0 1-1 1-1 1 0 2-1 2v1-3h-1l-1-2v-4c0-1 0-3 1-4l-1-1c1-1 1-3 2-4s2-3 3-4l1-1 2-2 1-1c2 0 3 0 4-1h0c1 0 1-1 2-1l6-3 1-1z" class="E"></path><path d="M311 157l1-2h1 1c1-2 3-3 5-4l-1 1-2 3c-1 2-3 2-5 2z" class="I"></path><path d="M304 156c1 0 1-1 2-1s1 1 2 2l-1 1c-1 1-2 2-2 3l-2-2-1-1 2-2z" class="J"></path><path d="M303 159c0-1 0-1 1-1h3c-1 1-2 2-2 3l-2-2z" class="I"></path><path d="M298 166c1-1 2-2 4-2-3 4-5 8-7 12v-4h0v-1c1-1 1-1 1-2s2-2 2-3z" class="G"></path><path d="M302 158l1 1 2 2-3 3c-2 0-3 1-4 2l-2-2h1c2 0 2-1 3-1l-1-3c1 0 2-1 3-2z" class="R"></path><path d="M302 158l1 1c0 1 0 1-1 2 0 1-1 1-2 2l-1-3c1 0 2-1 3-2z" class="F"></path><path d="M295 166c0-1 0-1 1-2h0l2 2c0 1-2 2-2 3s0 1-1 2v1h0v4h0-1l-1-2v-4l1-2 1-2z" class="g"></path><path d="M295 166c0 1 0 3-1 4 0 1 1 1 1 2v4h0-1l-1-2v-4l1-2 1-2z" class="h"></path><path d="M298 157l2 1 1-1s1-1 1-2l2 1-2 2c-1 1-2 2-3 2l1 3c-1 0-1 1-3 1h-1 0c-1 1-1 1-1 2l-1 2-1 2c0-1 0-3 1-4l-1-1c1-1 1-3 2-4s2-3 3-4z" class="G"></path><path d="M294 166c0-1 1-2 2-3s2-2 2-3h1l1 3c-1 0-1 1-3 1h-1 0c-1 1-1 1-1 2l-1 2-1 2c0-1 0-3 1-4z" class="T"></path><path d="M315 147c0 1 1 1 1 2l-1 2c-2 2-5 3-7 6-1-1-1-2-2-2s-1 1-2 1l-2-1c0 1-1 2-1 2l-1 1-2-1 1-1 2-2 1-1c2 0 3 0 4-1h0c1 0 1-1 2-1l6-3 1-1z" class="I"></path><path d="M315 147c0 1 1 1 1 2l-1 2c-1-1-1-1-2-1-2 0-5 5-7 4h-1-4l1-1c2 0 3 0 4-1h0c1 0 1-1 2-1l6-3 1-1z" class="d"></path><path d="M321 150h1l1 1h-1c0 2 1 2 2 4l-2 1v3l-1 1h-3v2h1s0 1 1 1h-3c-2 0-4 1-6 1-1 0-1 0-1-1h-1l-2 1-1-1v-1c1-1 3-3 3-4h2v-1c2 0 4 0 5-2l2-3 1-1 2-1z" class="I"></path><path d="M309 163c0-1 0-2 1-3 0-1 0 0 1 0v2h0l-1 1h-1z" class="d"></path><path d="M319 159l3-3v3l-1 1h-3v2h0c-1-1-2-1-2-1l3-2z" class="Y"></path><path d="M311 162c2-1 4-1 7 0h0 1s0 1 1 1h-3c-2 0-4 1-6 1-1 0-1 0-1-1l1-1z" class="L"></path><path d="M321 150h1l1 1h-1c0 2 1 2 2 4l-2 1-3 3-2-2h-1c-1 1-2 2-4 3h-1v-2-1c2 0 4 0 5-2l2-3 1-1 2-1z" class="J"></path><path d="M318 152l1 1s0 1 1 1h1v1c-2 1-3 1-4 2-1-1-1-1-1-2l2-3z" class="F"></path><path d="M304 194l3-3c1 0 1 1 2 1 1 2 2 4 4 5 4 4 11 7 17 8l3 1c1 4 1 9 1 13v5 1c-2-2-4-3-6-4-1 2-1 2-1 3h0c-1 0-1 0-2-1h-1-1c-1 0 0-1-2-1l-1 1h0-1c0 2 0 5-1 7-1-1-1-1-2-1h-1v-5h0c0-8-7-9-11-15l-1-1-3-5v-1-3h1l1 2c1-2 0-3 1-4 0-1 1-2 1-3z" class="g"></path><path d="M310 211c5 4 11 7 18 10-1 2-1 2-1 3h0c-1 0-1 0-2-1h-1-1c-1 0 0-1-2-1l-1 1h0l-1-1v-1c0-1-1-2-2-2v-1c-1-2-2-2-3-3-1 0-2-1-3-2s-1-1-1-2z" class="B"></path><defs><linearGradient id="u" x1="314.257" y1="212.576" x2="305.576" y2="215.857" xlink:href="#B"><stop offset="0" stop-color="#242320"></stop><stop offset="1" stop-color="#4c4b4d"></stop></linearGradient></defs><path fill="url(#u)" d="M300 203v-1-3h1l1 2c1 2 2 4 4 6 1 1 3 2 4 4 0 1 0 1 1 2s2 2 3 2c1 1 2 1 3 3v1c1 0 2 1 2 2v1l1 1h-1c0 2 0 5-1 7-1-1-1-1-2-1h-1v-5h0c0-8-7-9-11-15l-1-1-3-5z"></path><path d="M317 219c1 0 2 1 2 2v1l-1 1-1 3v-1c-1-3-1-4 0-6z" class="C"></path><path d="M319 222l1 1h-1c0 2 0 5-1 7-1-1-1-1-2-1 0-2 0-2 1-3l1-3 1-1z" class="V"></path><path d="M324 139h0l1 2 1 2c0 1 0 1 1 1v2l1 1v1h1l1-1c2 0 3 0 4 2v26 4h1c1 1 1 2 2 2v1h2v1h1v-2c0-1 1-1 1-2l2-3h0c2-1 3-2 5-3h2 2c1 0 1 1 2 1h2v3c2 1 2 3 3 4h-1v1h0v1s0 1 1 2c0 0-1 1-1 2-1 3-4 2-4 6 1 1 1 1 2 1l-1 1h0c-1 1-2 2-2 4h-3c0 1-1 2-1 4h-1v5 1h-3-1c0-1-2-1-2-1-2 0-4-1-6-1-1 0-2 0-3-1l-3-1c-6-1-13-4-17-8-2-1-3-3-4-5-1-3-2-7-2-10l2-6 1-2h0 0 1v3c1-1 1-2 1-2 2-3 5-5 8-6 1-1 2-1 2-2 1-1 1-1 0-3-1-1-2-2-3-2h-1v-2h3l1-1v-3l2-1c-1-2-2-2-2-4h1l-1-1h-1c1-1 1-1 1-2l-6 1c0-1-1-1-1-2s1-1 2-1h1 1l-1-3 2-2c2-1 2 0 3-1 0 0 1 0 1-1z" class="h"></path><path d="M331 148h1c1 0 1 1 1 2-1 1-1 1-2 1h-2l-1-1c1-2 2-2 3-2z" class="k"></path><path d="M318 162v-2h3v2h1 1v-1c0 1 3 3 3 3l1 1v1l3-1v2c0 2 0 4-1 6s-2 3-3 5h-1v-2l-1-1 1-1h-1-1l-1 1v-1c0-1 0-1 1-1v-2c0-1 1-1 1-2h-1c-1 0-1 1-1 2-1-1-2-1-2-2 1-1 2-1 2-2 1-1 1-1 0-3-1-1-2-2-3-2h-1z" class="T"></path><path d="M327 165v1l3-1v2 2c-1 0-2 1-3 1-1-2-1-3 0-5z" class="J"></path><path d="M325 176l2-1v-1l-2-2c0-1 0-1 1-2h1c0 1-1 1 0 2h1 1v1c-1 2-2 3-3 5h-1v-2z" class="R"></path><path d="M328 187v-3-2c1-2 1-3 1-5s1-3 2-5c1 1 2 3 2 4h0v1c1 5-1 11 0 17 0 1 1 3 0 5-1 0-1 0-2-1s-1-3-1-5c-1-2-2-4-2-6z" class="k"></path><path d="M320 169c0 1 1 1 2 2 0-1 0-2 1-2h1c0 1-1 1-1 2v2c-1 0-1 0-1 1v1l1-1h1 1l-1 1-1 1c0 1-1 1-1 2v1l3 3c-1 1-1 1-2 1h-1c-2 1-1 1-2 2-2 0-3-1-4-2v-3h0-2 0l-1-2h0l-2 2v-2-1c1-1 1-2 1-2 2-3 5-5 8-6z" class="F"></path><path d="M316 174c1-1 1-2 3-2l1 1h-1l-1 2c-1 0-1-1-2-1z" class="C"></path><path d="M316 174c1 0 1 1 2 1h0l-1 1h0c0 1-1 3-1 4h-2c0-3 0-4 2-6z" class="D"></path><path d="M318 175l1-2h1c1 2 1 4 2 5v1l-1 1h-1c0-1 0 0-1-1 0-1 0-3-1-4h0z" class="O"></path><path d="M316 180c0-1 1-3 1-4h0l1-1c1 1 1 3 1 4 1 1 1 0 1 1h1l1-1 3 3c-1 1-1 1-2 1h-1c-2 1-1 1-2 2-2 0-3-1-4-2v-3h0z" class="S"></path><path d="M320 180c1 1 2 1 2 3h0-3c-1 0-1 0-1-1l2-2z" class="Y"></path><path d="M322 179l3 3c-1 1-1 1-2 1h-1 0c0-2-1-2-2-3h0 1l1-1z" class="E"></path><path d="M324 139h0l1 2 1 2c0 1 0 1 1 1v2l1 1v1 3l2 2c1 4 0 8 0 12l-3 1v-1l-1-1s-3-2-3-3v1h-1-1v-2l1-1v-3l2-1c-1-2-2-2-2-4h1l-1-1h-1c1-1 1-1 1-2l-6 1c0-1-1-1-1-2s1-1 2-1h1 1l-1-3 2-2c2-1 2 0 3-1 0 0 1 0 1-1z" class="S"></path><path d="M323 160c1 0 2 0 3-1v-1l1-1v1 1c0 1-1 1-1 2h0l2 2c-2 0-3-1-3-2-1 0-1 0-1-1h-1z" class="T"></path><path d="M325 156l2-1h0v2l-1 1v1c-1 1-2 1-3 1v1 1h-1-1v-2l1-1v-3l2-1 1 1z" class="i"></path><path d="M324 155l1 1-3 3v-3l2-1z" class="T"></path><path d="M326 150c0-1-1-3-2-4h0c0-1 1-1 1-1l1-1h1v1 1l1 1v1 3l2 2c1 4 0 8 0 12l-3 1v-1l-1-1h1 1 1 0c-2-4 0-7-1-10l-1-1c-1-1 0 0 0-1s-1-2-1-2z" class="W"></path><path d="M324 139h0l1 2 1 2c0 1 0 1 1 1v2-1-1h-1l-1 1s-1 0-1 1h0c1 1 2 3 2 4l-1 1c-1 1 0 1-1 1s-1 0-1-1l-1-1h-1c1-1 1-1 1-2l-6 1c0-1-1-1-1-2s1-1 2-1h1 1l-1-3 2-2c2-1 2 0 3-1 0 0 1 0 1-1z" class="I"></path><path d="M324 139h0l1 2 1 2-1 1h-2l-1-3h-2c2-1 2 0 3-1 0 0 1 0 1-1z" class="N"></path><path d="M319 146h0 1 1c1 0 2 1 2 1v1h-1 0l-6 1c0-1-1-1-1-2s1-1 2-1h1 1z" class="P"></path><defs><linearGradient id="v" x1="321.369" y1="205.481" x2="312.712" y2="173.728" xlink:href="#B"><stop offset="0" stop-color="#1b1b1a"></stop><stop offset="1" stop-color="#3d3c3d"></stop></linearGradient></defs><path fill="url(#v)" d="M310 174h0 0 1v3 1 2l2-2h0l1 2h0 2 0v3c1 1 2 2 4 2 1-1 0-1 2-2h1c1 0 1 0 2-1l3 5c0 2 1 4 2 6 0 2 0 6-1 8 0 1 1 2 1 3v1c-6-1-13-4-17-8-2-1-3-3-4-5-1-3-2-7-2-10l2-6 1-2z"></path><path d="M311 178v2c0 1 1 2 1 3s-1 1 0 3v2h1c0 1 1 2 1 2 1 0 1 0 2 1-1 1-2 2-2 3l-2-2v-1c0-1-1-1-1-2v-1c-1-3-1-7 0-10z" class="Q"></path><path d="M320 196h2c0 1 0 2 1 1h4 1c0 1 0 2-1 3 0 1-1 1-1 1h-1v-2h-1v1h-1s-2-1-2-2c-1 0-1-2-1-2z" class="U"></path><path d="M322 187l1 1 1 3 1 2h-1c-1 1-1 3-1 4-1 1-1 0-1-1h-2c-1-2-3-1-4-1s-1 0-2-1c0-1 1-2 2-3v1c1 0 3-1 5-2h-2l1-2c1 0 2-1 2-1z" class="Y"></path><path d="M322 187l1 1 1 3v1c-1 0-2 0-2-1v-1h-1 0-2l1-2c1 0 2-1 2-1z" class="l"></path><defs><linearGradient id="w" x1="325.766" y1="196.524" x2="329.617" y2="189.824" xlink:href="#B"><stop offset="0" stop-color="#404342"></stop><stop offset="1" stop-color="#615c5c"></stop></linearGradient></defs><path fill="url(#w)" d="M325 182l3 5c0 2 1 4 2 6 0 2 0 6-1 8l-1-1h-1c1-1 1-2 1-3h-1-4c0-1 0-3 1-4h1l-1-2-1-3-1-1 1-1c0-1 0-2-1-3h1c1 0 1 0 2-1z"></path><path d="M323 186l1-2h0l3 6-1 1-1-2c-1 0-1-1-2-1l-1-1 1-1z" class="G"></path><path d="M323 188c1 0 1 1 2 1l1 2 1-1 1 7h-1-4c0-1 0-3 1-4h1l-1-2-1-3z" class="F"></path><path d="M311 180l2-2h0l1 2h0 2 0v3c1 1 2 2 4 2 1-1 0-1 2-2 1 1 1 2 1 3l-1 1s-1 1-2 1l-1 2h2c-2 1-4 2-5 2v-1c-1-1-1-1-2-1 0 0-1-1-1-2h-1v-2c-1-2 0-2 0-3s-1-2-1-3z" class="Y"></path><path d="M313 188v-1h1c1 1 2 3 4 3h1 2c-2 1-4 2-5 2v-1c-1-1-1-1-2-1 0 0-1-1-1-2z" class="h"></path><path d="M314 180h2 0v3c1 1 2 2 4 2 1-1 0-1 2-2 1 1 1 2 1 3l-1 1s-1 1-2 1l-4-2c-1-2-2-4-2-6h0z" class="H"></path><path d="M348 173h2 2c1 0 1 1 2 1h2v3c2 1 2 3 3 4h-1v1h0v1s0 1 1 2c0 0-1 1-1 2-1 3-4 2-4 6 1 1 1 1 2 1l-1 1h0c-1 1-2 2-2 4h-3c0 1-1 2-1 4h-1v5 1h-3-1c0-1-2-1-2-1-2 0-4-1-6-1-1 0-2 0-3-1l-3-1v-1c0-1-1-2-1-3 1-2 1-6 1-8 0 2 0 4 1 5s1 1 2 1c1-2 0-4 0-5 1-6 1-13 1-19v4h1c1 1 1 2 2 2v1h2v1h1v-2c0-1 1-1 1-2l2-3h0c2-1 3-2 5-3z" class="f"></path><path d="M334 198h1c1 0 1 1 2 2 0 1 0 3 1 5l-2-2c-1 0-1-1-1-2l-1-1v-2z" class="C"></path><path d="M344 207c0-1-1-1-2-2 0-2-1-4 0-7 0 1 1 3 2 4h2c0-1 1-1 2-1-1 1-1 1-1 2-1 1-1 2-1 3l-2 1z" class="B"></path><path d="M344 207v-3c1-1 1-1 3-1-1 1-1 2-1 3l-2 1z" class="C"></path><path d="M337 200c0-5 0-12 3-16v1c-1 6-2 14-1 20h-1 0c-1-2-1-4-1-5z" class="d"></path><path d="M352 173c1 0 1 1 2 1h2v3c2 1 2 3 3 4h-1c-1 0-2-1-3-1-2 0-3 1-4 2 0-1-1-1-2-1l2-3c1 0 2-1 2-2h-1c0-1-1-1-2-2h0 1l1-1z" class="C"></path><path d="M348 173h2 2l-1 1h-1a30.44 30.44 0 0 0-8 8c0 1-1 3-2 3v-1c-3 4-3 11-3 16-1-1-1-2-2-2h-1v2l-1 3v3h0l-3-1v-1c0-1-1-2-1-3 1-2 1-6 1-8 0 2 0 4 1 5s1 1 2 1c1-2 0-4 0-5 1-6 1-13 1-19v4h1c1 1 1 2 2 2v1h2v1h1v-2c0-1 1-1 1-2l2-3h0c2-1 3-2 5-3z" class="D"></path><path d="M334 175v4h1c1 1 1 2 2 2v1h0-2v2l1 1c-1 5-2 9-2 13v2l-1 3v3h0l-3-1v-1c0-1-1-2-1-3 1-2 1-6 1-8 0 2 0 4 1 5s1 1 2 1c1-2 0-4 0-5 1-6 1-13 1-19z" class="Z"></path><path d="M351 182c1-1 2-2 4-2 1 0 2 1 3 1v1h0v1s0 1 1 2c0 0-1 1-1 2-1 3-4 2-4 6 1 1 1 1 2 1l-1 1h0c-1 1-2 2-2 4h-3c0 1-1 2-1 4h-1v-1l1-1h-1 0c-1 0-2 0-2 1h-2c-1-1-2-3-2-4 0-2 0-4 1-6 0-4 3-8 6-11 1 0 2 0 2 1z" class="D"></path><path d="M348 201c1-2-1-2-1-4-1-1-1-3-1-4l1 3 2 2h2l-1 1c0 1-1 2-1 4h-1v-1l1-1h-1z" class="N"></path><path d="M351 182c1-1 2-2 4-2 1 0 2 1 3 1v1h0v1s0 1 1 2c0 0-1 1-1 2-1 3-4 2-4 6 1 1 1 1 2 1l-1 1h-2l-1 1c-1 0-1 0-2-1s-2-3-3-4c0-4 2-7 4-9z" class="O"></path><path d="M351 182c1-1 2-2 4-2 1 0 2 1 3 1v1h0c-2 0-4 0-5 1-2 2-2 4-3 6 2 2 2 3 3 6l-1 1c-1 0-1 0-2-1s-2-3-3-4c0-4 2-7 4-9z" class="f"></path><path d="M328 221c2 1 4 2 6 4v2c-1-1-1-1-2-1h-2c1 0 2 1 3 2v3 1 6 3c0 1 0 2-1 2 1 1 1 2 1 2v4 9 3 1 2 2l1 2-1 22h-1-6c-5 0-11 1-15-1h-1c-4-1-8-5-11-8h1c0-1-1-1-2-2v-2-3l2-2c-1 0-1-1-2-1v-4c-1-3-1-9-1-13 1-2 0-5 2-6h1c1 1 1 1 1 2 1-1 1-2 1-3l1 3s0-1 1 0c2-3 4-5 6-8 1-2 2-4 3-5 1-2 2-3 2-5h-1c0-1 0-2 1-3h1c1 0 1 0 2 1 1-2 1-5 1-7h1 0l1-1c2 0 1 1 2 1h1 1c1 1 1 1 2 1h0c0-1 0-1 1-3z" class="g"></path><path d="M314 254c1 0 2-1 2-1 1 1 1 2 1 3v2 2c-1-1 0-2-2-2v5c-2-3-1-6-1-9z" class="B"></path><path d="M314 254c1-5 3-10 5-14 1 1 0 3 1 4-1 2-2 5-2 7-1 1-1 3-1 5 0-1 0-2-1-3 0 0-1 1-2 1z" class="L"></path><path d="M320 223h0l1-1c2 0 1 1 2 1h1 1c1 1 1 1 2 1h0l-4 9v-2-3-1h0-1c0 1-2 1-3 3l-1 1v-1c1-2 1-5 1-7h1z" class="l"></path><path d="M315 263v-5c2 0 1 1 2 2 0 2 1 2 2 4h1c1 1 2 2 3 2 1-2 0-4 0-5v-1 1c1 1 2 2 3 2v2 1c2 1 2 0 2 1v1c-1 1-4 1-5 1-3-1-7-3-8-6z" class="a"></path><path d="M307 266c3 7 6 12 11 16 2 2 5 3 7 5h-2c-3-1-6-3-9-5-2-2-5-5-6-7s-1-6-1-9z" class="C"></path><path d="M314 282c3 2 6 4 9 5h2c2 1 4 2 7 3h-6c-5 0-11 1-15-1h-1c0-1-1-1-1-2 2 0 5 1 7 1l1-1-2-1v-1c-1-1-1-2-1-3z" class="f"></path><path d="M328 221c2 1 4 2 6 4v2c-1-1-1-1-2-1h-2c-2 3-3 7-5 11-2 1-2 3-4 5l-1 2c-1-1 0-3-1-4l4-7 4-9c0-1 0-1 1-3z" class="N"></path><path d="M321 242c1 1 1 1 1 2-1 2-1 5 0 7h0l2 2c0-1 0-1 1-2l1 1h0l-2 2h-1s-1 0-1 1c3 2 4 5 4 8v2-2c-1 0-2-1-3-2v-1 1c0 1 1 3 0 5-1 0-2-1-3-2h-1c-1-2-2-2-2-4v-2-2c0-2 0-4 1-5 0-2 1-5 2-7l1-2z" class="S"></path><path d="M319 260l3 2c0 1 0 1-1 2h-1-1v-4z" class="T"></path><path d="M317 258c1 0 2 1 2 2v4c-1-2-2-2-2-4v-2z" class="Y"></path><path d="M323 261h0c-1-2-1-3-2-5 0-1-1-2-1-4h1l1 3c3 2 4 5 4 8v2-2c-1 0-2-1-3-2v-1 1z" class="H"></path><path d="M316 229c1 0 1 0 2 1v1c-1 1-2 3-3 4l-5 10c-1 2-2 4-3 7-1 4-1 9 0 14 0 3 0 7 1 9s4 5 6 7c0 1 0 2 1 3v1c-1-1-2-2-2-3l-1-1c-1-1-2-1-2-2-4-5-6-11-7-17h-1c-1-1-1-2-1-3l-1-2v-5c0-1 1-2 1-3 1-1 1-2 1-3l1 3s0-1 1 0c2-3 4-5 6-8 1-2 2-4 3-5 1-2 2-3 2-5h-1c0-1 0-2 1-3h1z" class="e"></path><path d="M302 247l1 3s0-1 1 0l-2 2v1c1 3 1 6 1 10h-1c-1-1-1-2-1-3l-1-2v-5c0-1 1-2 1-3 1-1 1-2 1-3z" class="S"></path><path d="M297 254c1-2 0-5 2-6h1c1 1 1 1 1 2s-1 2-1 3v5l1 2c0 1 0 2 1 3h1c1 6 3 12 7 17 0 1 1 1 2 2l1 1c0 1 1 2 2 3l2 1-1 1c-2 0-5-1-7-1 0 1 1 1 1 2-4-1-8-5-11-8h1c0-1-1-1-2-2v-2-3l2-2c-1 0-1-1-2-1v-4c-1-3-1-9-1-13z" class="O"></path><path d="M310 283l3 3s1 0 0 1c-2 0-4-2-6-3 1 0 2 1 3 0v-1z" class="d"></path><path d="M302 273c1 0 1 0 2 1v1l2 2c0 1 0 2 1 3l1 2 2 1v1c-1 1-2 0-3 0-3-1-6-4-8-7 1 0 1-1 1-2h0v-1-1h2 0z" class="S"></path><path d="M299 277c1 0 1-1 1-2h0v-1-1h2l-1 1v2c1 0 1 0 1 1l1 1-1 1c1 2 4 3 5 4l1-1 2 1v1c-1 1-2 0-3 0-3-1-6-4-8-7z" class="J"></path><path d="M302 263h1c1 6 3 12 7 17 0 1 0 2 1 3s2 2 2 3l-3-3-2-1-1-2c-1-1-1-2-1-3l-2-2v-1c-1-1-1-1-2-1 0-1-1-1-1-2v-1h1v-1c0-1-1-3-1-3 1-2 1-1 1-3z" class="W"></path><path d="M302 269v-1c1 1 4 7 4 8v1c1 1 1 2 1 3-1-1-1-2-1-3l-2-2v-1c-1-1-1-1-2-1 0-1-1-1-1-2v-1h1v-1z" class="J"></path><path d="M297 254c1-2 0-5 2-6h1c1 1 1 1 1 2s-1 2-1 3v5l1 2c0 1 0 2 1 3 0 2 0 1-1 3 0 0 1 2 1 3v1h-1v1c0 1 1 1 1 2h0-2v1 1h0c0 1 0 2-1 2l-1-3 2-2c-1 0-1-1-2-1v-4c-1-3-1-9-1-13z" class="c"></path><path d="M300 248c1 1 1 1 1 2s-1 2-1 3c0 0 0-1-1-2v-2l1-1z" class="J"></path><path d="M302 270c-1 0-1-1-1-1-1-1-1-2-1-4 0-1 0-3 1-5 0 1 0 2 1 3 0 2 0 1-1 3 0 0 1 2 1 3v1z" class="M"></path><path d="M330 226c1 0 2 1 3 2v3 1 6 3c0 1 0 2-1 2 1 1 1 2 1 2v4 9 3 1 2h-1v2l-2 2h-2v-1c0-1 0 0-2-1v-1-2c0-3-1-6-4-8 0-1 1-1 1-1h1l2-2h0l-1-1c-1 1-1 1-1 2l-2-2h0c-1-2-1-5 0-7 0-1 0-1-1-2 2-2 2-4 4-5 2-4 3-8 5-11z" class="Y"></path><path d="M330 252l1 1c1 1 1 1 1 2s-1 1-1 1c0-1 0-1-1-1l-1-1v-1l1-1zm-2-3h0 3s0 1 1 1l-1 1c-1 0-2 1-3 0v-2z" class="g"></path><path d="M328 243c0-1 0-2 1-2h4c0 1 0 2-1 2 0 2 0 3-1 4-1 0-1-1-2-1-1-1-1-2-1-3z" class="V"></path><path d="M325 248v-1l1-1c1 2 1 4 1 6v2h0v3l-3-3 2-2h0l-1-1c-1 1-1 1-1 2l-2-2 1-1c0-1 1 0 1 0l1-1v-1z" class="B"></path><path d="M321 242c2-2 2-4 4-5 0 2-1 6 0 8-1 1-1 1-1 2v2h0l1-1v1l-1 1s-1-1-1 0l-1 1h0c-1-2-1-5 0-7 0-1 0-1-1-2z" class="X"></path><path d="M330 257l1-1s1 1 0 2c0 3 0 5 1 8l-2 2h-2v-1c0-1 0 0-2-1h1c0-1 1-1 0-1 0-2 1-4 1-6l2-2z" class="H"></path><path d="M330 257l1-1s1 1 0 2c0 3 0 5 1 8l-2 2c0-1 0-1-1-1 0-3 2-4 2-6s0-3-1-4z" class="Z"></path><path d="M328 243h-1s-1 0-1-1v-1c1-2 0-3 1-4 0-1 1-1 1-2s0-1 1-1v-2l1-1v-1l1-1h1 0l1 2v1 6 3h-4c-1 0-1 1-1 2z" class="h"></path><path d="M369 156v-1l1-1c1 1 2 2 2 3h1 2 1l2 2c0 1-1 4 0 5 1 3 0 4 0 6-1 2-1 4-1 6-1 9 0 18 0 26 0 3-1 6 0 9h0v6l6 2v3 28 11 5 15 30h-1v1c-2 2-5 1-7 4h0 1s1 0 1-1h1 0 1l1-1h2l-5 3h0l-7 2-3 1-6 2-2 1h-3l-13 2-3 1h-6l-1-1v-2c0-1 0-1-1-1l1-1c0-3 1-6 0-9l-3-2c-1 0-2-1-3-2h-1-1c1-1 2-1 2-2 2 1 3 1 5 2h1l1-1c-1-2-1-14-1-17h-6-1 6 1l1-22-1-2v-2-2-1-3-9-4s0-1-1-2c1 0 1-1 1-2v-3-6-1-3c-1-1-2-2-3-2h2c1 0 1 0 2 1v-2-1-5c0-4 0-9-1-13 1 1 2 1 3 1 2 0 4 1 6 1 0 0 2 0 2 1h1 3v-1-5h1c0-2 1-3 1-4h3c0-2 1-3 2-4h0l1-1c-1 0-1 0-2-1 0-4 3-3 4-6 0-1 1-2 1-2-1-1-1-2-1-2v-1h0v-1h1c-1-1-1-3-3-4v-3h-2c-1 0-1-1-2-1h-2-2c0-1-1-1-2-2l-2-2v-2l-1-1h1 0l1-1c0-1 0-2-1-2v-5c1-1 1-1 2-1h2l1-1h1v2c0-1 0-1 1-2v1s0 1 1 1v-2l3 3 3 2c4 2 7 4 10 7 1 1 3 3 4 3h0l2-1c0-2 0-4-1-6 0 0-1-3-2-3l-2-3v-2z" class="f"></path><path d="M354 292l-1-1h0l3-1c0 1-1 1-1 2h-1zm-1-60l1 1c1 1 1 2 2 3l-1 1-3-3v-1l1-1z" class="C"></path><path d="M356 236c1 1 1 3 2 4-2-1-3-2-4-2h-1c1-1 1-1 2-1l1-1z" class="B"></path><path d="M339 245c1 2 1 5 1 7 0-1 0-2-1-3l-1 1c0-2 0-4-2-5l1-1c1 0 1 0 2 1h0z" class="L"></path><path d="M350 233c-1-1-1-2-1-3l1-1c1 0 2 1 3 1 1 1 1 2 1 3l-1-1-1 1v1h0c-1 0-1 0-2-1z" class="N"></path><path d="M350 233v-1h3l-1 1v1h0c-1 0-1 0-2-1z" class="L"></path><path d="M342 236c-1-2-2-7-1-10h3c1 1 2 1 2 2-1 0-2 0-3 1s-1 1-1 2v5z" class="C"></path><path d="M339 260v7c1 2 1 2 1 3h0c-1 0-1-1-1-1-1-1-1-1-2-1-1-1-1-3-2-4 0-1 0-1-1-1v1c0 1-1 1-1 2v-2-2h2l1 1v1h1c2-1 1-2 2-4z" class="N"></path><path d="M347 246c1 0 1-1 1-1 1 1 1 2 2 3v4c-1 3-3 5-5 7l-1-1s1-1 1-2l2-1c0-1 1-3 1-4s0-3-1-5z" class="c"></path><path d="M338 250l1-1c1 1 1 2 1 3-1 3-2 6-1 8-1 2 0 3-2 4h-1v-1l-1-1v-1h1c1-1 1-3 1-5h0c-1-1-1-1-1-2-1 0-1-1-1-1h1l1-1 1-2z" class="C"></path><path d="M337 252h1c0 1 0 2-1 2v2c-1-1-1-1-1-2-1 0-1-1-1-1h1l1-1z" class="D"></path><path d="M337 256v4c1 1 1 1 1 3h-1-1l-1-1v-1h1c1-1 1-3 1-5z" class="V"></path><path d="M345 254v1c-2 0-3 1-3 3h-1 0c1-3 1-8 0-11v-4h-1l1-1 3 3c1 1 1 3 1 4v5z" class="X"></path><path d="M345 254h-2c0-1 0-4 1-5h1 0v5z" class="H"></path><path d="M352 255l1-1h-1c1-3 0-6 2-7 2 4 4 8 5 13-2 0-4 0-6 1 0 0 0-1-1-1v-4-1z" class="E"></path><path d="M352 255h1v3c1 1 0 1 0 3 0 0 0-1-1-1v-4-1z" class="M"></path><path d="M374 254c2 4 1 9 1 13v5c2-2 2-7 2-9 0 2 1 7 0 9l-1 1v8 15c0 5 0 9-1 14l-1-7v-49z" class="Y"></path><path d="M342 236v-5c0-1 0-1 1-2s2-1 3-1c1 5 2 9 4 13h0c0 1 0 1 1 2v7s0 1-1 2v-4c-1-1-1-2-2-3 0 0 0 1-1 1l-1-4c-1-1-2-1-2-3-1-1-1-2-2-3z" class="b"></path><path d="M344 239h0v-3c-1-1-1-1-1-2h1c2 2 4 5 4 8 1 2 2 4 2 6-1-1-1-2-2-3 0 0 0 1-1 1l-1-4c-1-1-2-1-2-3z" class="P"></path><path d="M346 242v-1c2 1 2 2 2 4 0 0 0 1-1 1l-1-4z" class="d"></path><path d="M367 282l1 1c0 2-1 3-3 5h0c0 1-1 2-2 4 0 1 0 1 1 3l1 1v2s1 1 1 2l1 1v1l1 2v1h0c0 1 0 1 1 2v1l1 1v2h-1l-4-8c-1-2-3-3-4-5s-4-4-7-6h1c0-1 1-1 1-2 4-2 8-5 11-8z" class="V"></path><path d="M361 293c-1 1-1 1-2 1v-2-1h1l2 1c0 1-1 1-1 1zm1-3s0-1 1-1c0-1 1-2 2-2v1c0 1-1 2-2 4l-1-2z" class="P"></path><path d="M362 290l1 2c0 1 0 1 1 3l1 1v2s1 1 1 2l1 1v1l1 2v1h0c0 1 0 1 1 2v1l1 1v2h-1l-4-8c0-1 0-2-1-3 0-2-1-3-3-3 1-2 1-2 1-3l-1-1s1 0 1-1l-2-1c1 0 1 0 2-1z" class="b"></path><path d="M367 282l3-5c1 4 1 9 1 14v1s0 1 1 2h1c0 2-1 3-1 4-1 1-2 1-3 1v1c-1 2-1 2-1 4 1-1 1-1 1-2h0c1 0 1-1 3-1 0 1 0 2-1 2v1 1c0 1-1 2-1 4l-1-1v-1c-1-1-1-1-1-2h0v-1l-1-2v-1l-1-1c0-1-1-2-1-2v-2l-1-1c-1-2-1-2-1-3 1-2 2-3 2-4h0c2-2 3-3 3-5l-1-1z" class="V"></path><path d="M368 286c1 0 1 1 2 2l-1 1c-1 0-1-1-2-1 0-1 1-2 1-2z" class="b"></path><path d="M369 300h-2v-1c1-1 2-3 4-4 0-1 0 0 1 0 0 2-2 3-3 4v1zm-3-11c1 1 2 1 3 3l-3 3h0c-1 0-1-1-2-1 0-3 1-4 2-5z" class="P"></path><path d="M367 282l3-5c1 4 1 9 1 14h0l-1-2v-2c0-1 0-2-1-2l-1 1s-1 1-1 2l-1 1c-1 1-2 2-2 5 1 0 1 1 2 1h0 1c0 1-1 2-2 3v-2l-1-1c-1-2-1-2-1-3 1-2 2-3 2-4h0c2-2 3-3 3-5l-1-1z" class="C"></path><defs><linearGradient id="x" x1="351.633" y1="263.395" x2="344.007" y2="248.467" xlink:href="#B"><stop offset="0" stop-color="#403e40"></stop><stop offset="1" stop-color="#5b5b5a"></stop></linearGradient></defs><path fill="url(#x)" d="M350 252c1-1 1-2 1-2v-7c-1-1-1-1-1-2h0c1 2 3 4 4 6-2 1-1 4-2 7h1l-1 1v1 4c1 0 1 1 1 1-2 1-4 5-5 7s-2 3-2 4h-1-2-1-1c1 0 1 0 2-1v-1c0-1 0-1 1-2-1 0-2-1-2-2v-1-1c0-2 1-4 2-6l1 1c2-2 4-4 5-7z"></path><path d="M344 268h2c0 1-1 2-1 3-1 1-1 0-2 0v-1c0-1 0-1 1-2z" class="m"></path><path d="M352 256v4c1 0 1 1 1 1-2 1-4 5-5 7h-1v-1c1-1 1-2 1-3-1 0-2 1-3 2 0 0 0-1-1-2v-1c1-1 2-2 3-2l5-5z" class="E"></path><path d="M364 223h2c1 0 1-1 2 0v1l-1 1 2 2-2 2h1c-1 3 0 4 1 6h0 2c1 1 1 3 1 5l-1 5c-1 3-1 7-1 10h0v-2-1l-1 1h0c-1-4-2-6-4-9-1-2-2-3-2-4h0s1 0 1 1h0v-1c0-1 0-1-1-2-1-3-1-7-1-11h0l1-3 1 6 1-3c0-1 0-3-1-4z" class="H"></path><path d="M365 236h1c0 2 1 5 0 6h0c-1-2-1-4-1-6zm-35-10h2c1 0 1 0 2 1h0 3c1 1 1 6 1 8 1 1 2 3 1 4v1 4 1h0c-1-1-1-1-2-1l-1 1c2 1 2 3 2 5l-1 2-1 1h-1s0 1 1 1c0 1 0 1 1 2h0c0 2 0 4-1 5h-1v1h-2v-1-3-9-4s0-1-1-2c1 0 1-1 1-2v-3-6-1-3c-1-1-2-2-3-2z" class="O"></path><path d="M338 240v1 1c-1 1-1 1-1 2l-1 1h0-1c0-1 1-2 1-4 1 0 1-1 2-1z" class="b"></path><path d="M336 241c0-1-1-2-1-3 0-2 0-5 1-6v2 3l1-1v-5 1 4h0c1 1 1 2 1 3v1c-1 0-1 1-2 1z" class="H"></path><path d="M336 245c2 1 2 3 2 5l-1 2-1 1h-1l-1-2c1-2 1-4 2-6h0z" class="f"></path><path d="M333 232c1 2 0 4 0 6l1 8v4 1l1 2s0 1 1 1c0 1 0 1 1 2h0c0 2 0 4-1 5h-1v1h-2v-1-3-9-4s0-1-1-2c1 0 1-1 1-2v-3-6z" class="Z"></path><path d="M333 258c0-1 1-2 1-3 1 2 1 3 1 4v2 1h-2v-1-3z" class="C"></path><path d="M374 199c1 10 0 21 0 31v18c-1-2-1-5-1-8v-3-2-5h-1c1 1 0 2 0 4h0v1 5c0-2 0-4-1-5h-2 0c-1-2-2-3-1-6h-1l2-2-2-2 1-1v-1c-1-1-1 0-2 0h-2v-3c-1 0-1-1-2-2 1-1 1-2 1-3l3 1 1-2 1-2 1-1v-3l2-3c1 0 2-1 2-2 1-1 1-3 1-4z" class="O"></path><path d="M368 224l1-1 2 2-1 1-1 1-2-2 1-1z" class="N"></path><path d="M371 217h0c-1 2-1 4-1 6h-1c-1-1-1-1-1-2 1-2 1-3 3-4zm-1 9h1c1 1 1 2 1 3v1l-4-1h-1l2-2 1-1z" class="D"></path><path d="M366 216l1-2c0 1 0 2 1 3-1 1-1 2-2 2-1 1-1 0-2 0v1c-1 0-1-1-2-2 1-1 1-2 1-3l3 1z" class="Z"></path><path d="M363 215l3 1-2 3v1c-1 0-1-1-2-2 1-1 1-2 1-3z" class="L"></path><path d="M368 229l4 1v3l-1 2h-2 0c-1-2-2-3-1-6z" class="m"></path><path d="M371 205c1 1 1 1 1 2-1 1-1 2-1 3h0c-1 2-2 5-3 7-1-1-1-2-1-3l1-2 1-1v-3l2-3z" class="W"></path><defs><linearGradient id="y" x1="387.053" y1="297.693" x2="370.027" y2="290.338" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#8e8d8e"></stop></linearGradient></defs><path fill="url(#y)" d="M376 281l1-6c1 0 2 0 3-1 3-2 2-5 2-8h1v15 30h-1c-3 1-5 2-7 4l-1-12 1 7c1-5 1-9 1-14v-15z"></path><path d="M355 195s1 0 1 1v1c-1 1-2 2-2 3l1 1v1l1 2c2-3 5-5 8-6 1 0 1 1 1 2l1-1 2-1c1 0 1-1 2-1 1 1 1 1 1 2 0 3-1 5-2 7v2 3l-1 1-1 2-1 2-3-1c0 1 0 2-1 3 1 1 1 2 2 2v3c1 1 1 3 1 4l-1 3-1-6c-1-2-2-5-4-6-3-5-9-8-14-9h3v-1-5h1c0-2 1-3 1-4h3c0-2 1-3 2-4z" class="N"></path><g class="O"><path d="M357 208l1 1c1 0 2 2 2 3-1 0-1 0-2 1-1 0-1-1-2-1 0-2 0-3 1-4z"></path><path d="M363 210l1 1c-1 1-2 1-1 3v-1 2c0 1 0 2-1 3l-1-1c0-2-2-3-3-4 1-1 1-1 2-1 2 0 3-1 3-2z"></path></g><path d="M358 213c1-1 1-1 2-1 1 1 1 0 2 1l-1 4c0-2-2-3-3-4z" class="L"></path><path d="M355 202l1 2h0l-3 6h-1c-1-1 0-2-1-3l-1 1h-2v-5h1c0 1 0 2 1 3v1h1s1 0 1-1c1-2 2-3 3-4z" class="D"></path><path d="M359 204c1 0 2 0 2 1 0 0 0 1 1 1 0 1 1 2 1 2l1 1-1 1c0 1-1 2-3 2 0-1-1-3-2-3l-1-1c0-2 1-3 2-4z" class="M"></path><path d="M355 195s1 0 1 1v1c-1 1-2 2-2 3l1 1v1c-1 1-2 2-3 4 0 1-1 1-1 1h-1v-1c-1-1-1-2-1-3 0-2 1-3 1-4h3c0-2 1-3 2-4z" class="d"></path><path d="M350 199h3c-1 1-2 3-2 4v2l1 1c0 1-1 1-1 1h-1v-1c-1-1-1-2-1-3 0-2 1-3 1-4zm18-1c1 0 1-1 2-1 1 1 1 1 1 2 0 3-1 5-2 7v2 3l-1 1-1 2-1 2-3-1v-2 1c-1-2 0-2 1-3l-1-1 1-1-1-1s-1-1-1-2c-1 0-1-1-1-1 0-1-1-1-2-1 2-2 4-3 6-4l1-1 2-1z" class="E"></path><path d="M361 205c1-1 2-1 3-1h1l-3 2c-1 0-1-1-1-1z" class="D"></path><path d="M365 204h1 0c0 1-1 1-2 2v3l-1-1s-1-1-1-2l3-2z" class="W"></path><path d="M366 212c1 0 1-1 2 0l-1 2-1 2-3-1v-2c1 0 1-1 2-1h1 0z" class="B"></path><path d="M368 198c1 0 1-1 2-1 1 1 1 1 1 2 0 3-1 5-2 7v2 3l-1 1c-1-1-1 0-2 0v-2l1-1c-1-1-1-1-1 0l-1-1v-2h2l1 1c0-1 0-2-1-3h0v-1-1h-1c1-1 2-2 2-4z" class="C"></path><path d="M367 209l1-1c1 1 0 1 1 2v1l-1 1c-1-1-1 0-2 0v-2l1-1z" class="N"></path><defs><linearGradient id="z" x1="381.715" y1="217.663" x2="371.107" y2="218.478" xlink:href="#B"><stop offset="0" stop-color="#bebabc"></stop><stop offset="1" stop-color="#e1e2e2"></stop></linearGradient></defs><path fill="url(#z)" d="M369 156v-1l1-1c1 1 2 2 2 3h1 2 1l2 2c0 1-1 4 0 5 1 3 0 4 0 6-1 2-1 4-1 6-1 9 0 18 0 26 0 3-1 6 0 9v52c0 2 0 7-2 9v-5c0-4 1-9-1-13v-6-18c0-10 1-21 0-31v-15c0-3-1-6 0-8v-2c-1-1 0-2 0-3v-1c0-2 0-4-1-6 0 0-1-3-2-3l-2-3v-2z"></path><path d="M369 156v-1l1-1c1 1 2 2 2 3h1 2 1l2 2c0 1-1 4 0 5-1 0-2 0-3 1l1 2v3c-1-1-1-1-1-2-1 1 0 3-1 5v3-2c-1-1 0-2 0-3v-1c0-2 0-4-1-6 0 0-1-3-2-3l-2-3v-2z" class="F"></path><path d="M373 157h2 1l-1 1c-1 2 0 5-1 7v-4-1c-1-1-1-2-1-3z" class="P"></path><path d="M374 160v1 4 6-1c0-2 0-4-1-6 0 0-1-3-2-3 1 0 2 0 3-1z" class="M"></path><path d="M369 156v-1l1-1c1 1 2 2 2 3h1c0 1 0 2 1 3-1 1-2 1-3 1l-2-3v-2z" class="Z"></path><path d="M369 156v-1l1-1c1 1 2 2 2 3l-1 1-2-2z" class="K"></path><defs><linearGradient id="AA" x1="391.883" y1="225.474" x2="366.133" y2="261.323" xlink:href="#B"><stop offset="0" stop-color="#6d6c6b"></stop><stop offset="1" stop-color="#acacad"></stop></linearGradient></defs><path fill="url(#AA)" d="M377 211h0v6l6 2v3 28 11 5h-1c0 3 1 6-2 8-1 1-2 1-3 1l-1 6v-8l1-1c1-2 0-7 0-9v-52z"></path><path d="M333 206c1 1 2 1 3 1 2 0 4 1 6 1 0 0 2 0 2 1h1c5 1 11 4 14 9 2 1 3 4 4 6l-1 3h0c0 4 0 8 1 11 1 1 1 1 1 2v1h0c0-1-1-1-1-1h0c-5-6-7-12-14-15-4-2-11-3-15-1v-5c0-4 0-9-1-13z" class="b"></path><path d="M344 209h1c5 1 11 4 14 9h0c-3-2-5-4-8-5s-5-2-8-3v-1h1z" class="I"></path><defs><linearGradient id="AB" x1="348.888" y1="289.905" x2="353.969" y2="240.871" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#AB)" d="M353 238h1c1 0 2 1 4 2 2 2 6 6 7 8 2 3 3 5 4 8 1 4 1 8 0 12-1 6-5 12-10 15-6 4-15 8-22 6l-3-1v1 1h-1l1-22c2 3 4 4 7 4h1 1 2c1 0 5 0 5 1l2-1c3-1 6-2 7-5 4-8 1-17-4-24-1-2-3-3-3-5h1z"></path><path d="M368 259l-1-2c0-2-1-5-3-7h0 1 0c1 2 1 3 2 4v-1l-1-1v-2l-1-1v-1c2 3 3 5 4 8 0 1 0 1-1 1v-1 3z" class="Q"></path><path d="M369 256c1 4 1 8 0 12h0-1v-9-3 1c1 0 1 0 1-1z" class="I"></path><path d="M353 238h1c1 0 1 1 1 2 1 0 1 1 1 1 2 2 3 4 4 6 2 4 3 7 3 12 0 4-1 9-5 12-3 3-6 3-9 3-2 0-3-1-5-1h6l2-1c3-1 6-2 7-5 4-8 1-17-4-24-1-2-3-3-3-5h1z" class="S"></path><path d="M345 165c0-1 0-2-1-2v-5c1-1 1-1 2-1h2l1-1h1v2c0-1 0-1 1-2v1s0 1 1 1v-2l3 3 3 2c4 2 7 4 10 7 1 1 3 3 4 3h0l2-1v1c0 1-1 2 0 3v2c-1 2 0 5 0 8v15c0 1 0 3-1 4 0 1-1 2-2 2l-2 3v-2c1-2 2-4 2-7 0-1 0-1-1-2-1 0-1 1-2 1l-2 1-1 1c0-1 0-2-1-2-3 1-6 3-8 6l-1-2v-1l-1-1c0-1 1-2 2-3v-1c0-1-1-1-1-1h0l1-1c-1 0-1 0-2-1 0-4 3-3 4-6 0-1 1-2 1-2-1-1-1-2-1-2v-1h0v-1h1c-1-1-1-3-3-4v-3h-2c-1 0-1-1-2-1h-2-2c0-1-1-1-2-2l-2-2v-2l-1-1h1 0l1-1z" class="N"></path><path d="M344 169v-2l-1-1h1 0l1-1c1 3 2 5 5 8h-2c0-1-1-1-2-2l-2-2z" class="H"></path><path d="M350 158c0-1 0-1 1-2v1s0 1 1 1v-2l3 3 3 2c4 2 7 4 10 7 1 1 3 3 4 3h0l2-1v1c0 1-1 2 0 3v2c-1 2 0 5 0 8-2-1-3-2-3-4l-3-6-4-4c-3-2-6-3-9-4-2-1-5-1-7-1l-1-1v-2l-1-1v-2l4-1z" class="O"></path><path d="M352 158v-2l3 3 3 2v1l-6-4z" class="N"></path><defs><linearGradient id="AC" x1="374.247" y1="174.912" x2="357.008" y2="170.979" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#AC)" d="M364 170c-1-3-4-5-7-7 2 0 3 1 4 1 2 1 5 4 7 4 1 1 3 3 4 3h0l2-1v1c0 1-1 2 0 3v2c-1 2 0 5 0 8-2-1-3-2-3-4l-3-6-4-4z"></path><defs><linearGradient id="AD" x1="370.401" y1="185.163" x2="351.854" y2="181.689" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#757475"></stop></linearGradient></defs><path fill="url(#AD)" d="M351 170h-1v-1c1-1 4-1 6-1 4 1 8 4 10 8 3 3 3 8 3 13 0 2 0 5 1 7h-1-2c-1 1-1 2-1 3l-1 1c0-1 0-2-1-2-3 1-6 3-8 6l-1-2v-1l-1-1c0-1 1-2 2-3v-1c0-1-1-1-1-1h0l1-1c-1 0-1 0-2-1 0-4 3-3 4-6 0-1 1-2 1-2-1-1-1-2-1-2v-1h0v-1h1c-1-1-1-3-3-4v-3h0c-1-1-3-2-5-3v-1z"></path><path d="M361 195h1l-2 2h1c1 0 1 0 2-1h0v1h0 0l1-1h1 2v-1h2-1l-1-1v-1c1 0 0 0 1 1 0 0 1 0 1 1v1h-2c-1 1-1 2-1 3l-1 1c0-1 0-2-1-2-3 1-6 3-8 6l-1-2v-1c1 0 1-1 2-2 0 0 1-1 2-1 0-1 1-2 1-3h1z" class="E"></path><path d="M364 198c0-1 2-2 3-2-1 1-1 2-1 3l-1 1c0-1 0-2-1-2z" class="X"></path><path d="M353 171v-1c2 0 4 2 5 3 3 4 7 9 7 14l-4 8h-1c0 1-1 2-1 3-1 0-2 1-2 1-1 1-1 2-2 2l-1-1c0-1 1-2 2-3v-1c0-1-1-1-1-1h0l1-1c-1 0-1 0-2-1 0-4 3-3 4-6 0-1 1-2 1-2-1-1-1-2-1-2v-1h0v-1h1c-1-1-1-3-3-4v-3h0c-1-1-3-2-5-3v-1l2 1z" class="Q"></path><path d="M361 186c0 2 0 3-1 5s-2 4-4 6v-1c0-1-1-1-1-1h0l1-1 1-3h1c2-1 2-4 3-5z" class="U"></path><path d="M351 170l2 1c3 1 5 3 6 6l1 1c1 2 1 5 1 8-1 1-1 4-3 5h-1l-1 3c-1 0-1 0-2-1 0-4 3-3 4-6 0-1 1-2 1-2-1-1-1-2-1-2v-1h0v-1h1c-1-1-1-3-3-4v-3h0c-1-1-3-2-5-3v-1z" class="B"></path><path d="M332 290h1 1c2 1 5 1 7 1h7c1 0 1 1 2 2 3 1 6 4 8 6h0c4 5 7 9 10 15 0 1 1 2 1 3s0 1 1 2l-3 1-6 2-2 1h-3l-13 2-3 1h-6l-1-1v-2c0-1 0-1-1-1l1-1c0-3 1-6 0-9l-3-2c-1 0-2-1-3-2h-1-1c1-1 2-1 2-2 2 1 3 1 5 2h1l1-1c-1-2-1-14-1-17h-6-1 6z" class="E"></path><path d="M356 322h1c3-1 1-3 2-5 2 1 3 1 5 1-1 0-3 1-4 2l-1 1c1 0 1 1 2 1l-2 1h-3v-1z" class="B"></path><path d="M368 314s-1 0-1 1c-1-2-2-3-3-5-2-3-5-7-8-10h1l1 1h1l-2-1 1-1c4 5 7 9 10 15z" class="I"></path><path d="M369 317c0 1 0 1 1 2l-3 1-6 2c-1 0-1-1-2-1l1-1c1-1 3-2 4-2 2-1 4 0 5-1z" class="N"></path><path d="M327 306c2 1 3 1 5 2h1l1-1c4-1 8 0 12 2 5 4 7 8 10 13v1l-13 2-3 1h-6l-1-1v-2c0-1 0-1-1-1l1-1c0-3 1-6 0-9l-3-2c-1 0-2-1-3-2h-1-1c1-1 2-1 2-2z" class="j"></path><path d="M352 319s0 1 1 1l-1 1s-3 1-4 1c0-1 0-1-1-1 0 1 0 1-1 1h-4l-1-12h0l2 1h-1v2h0c1 3 1 6 4 7h0v-2l1-1c0 1 0 2 1 2 2 0 2 1 4 0z" class="X"></path><defs><linearGradient id="AE" x1="335.48" y1="325.439" x2="337.883" y2="320.126" xlink:href="#B"><stop offset="0" stop-color="#19191a"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#AE)" d="M333 321l1-1h0 1 4 1v1c0 1-1 3 0 4h3l-3 1h-6l-1-1v-2c0-1 0-1-1-1l1-1z"></path><path d="M352 319c-2 1-2 0-4 0-1 0-1-1-1-2l-1 1v2h0c-3-1-3-4-4-7h0v-2h1c3 2 7 4 9 8z" class="H"></path><path d="M327 306c2 1 3 1 5 2h1v1l1 1h0c1 0 2-1 3-1l2 2v2l1 7h-1-4-1 0l-1 1c0-3 1-6 0-9l-3-2c-1 0-2-1-3-2h-1-1c1-1 2-1 2-2z" class="O"></path><path d="M327 306c2 1 3 1 5 2-1 1-4 0-6 0h-1c1-1 2-1 2-2z" class="a"></path><path d="M339 320l-2-2c0-1-1-2-1-4l2-2 1 1 1 7h-1z" class="D"></path><path d="M358 133c4-2 10-1 15-1h37l157-1h27c2 1 5 1 8 1h1c3 1 8 2 11 5-4-1-7-2-11-2-14-2-31 0-43 9-15 11-22 29-24 47-3 18-2 37-2 55v67 20c0 3 0 6-1 9v6l-1-6c0-2-1-4-1-6l-3-13-5-23c-4-14-10-27-15-41-3-6-5-12-8-18-4-5-7-12-10-17-7-11-15-21-24-30a30.44 30.44 0 0 0-8-8l-1 1s-1-1-1-2c-4-3-8-7-12-10-7-5-14-9-22-14l-6-3-28-12h-1l-5-2-18-6c-2-2-4-3-6-5z" class="E"></path><path d="M432 141c1-1 2-2 4-3v2 1h-1-3z" class="K"></path><path d="M436 140c0-1 0-2 1-3l1-1c2 0 4-1 6 0h2 2v1c-1 0-2 0-3 1l-1 1-1-2c-1-1-2 0-3 0s-1 1-1 2l-1-1v3h-2v-1z" class="L"></path><path d="M394 142c-2 0-3-1-5-1s-3 1-6 0v-1c1 0 2 0 2 1h1l1-1h-1l-1-1 1-1h2-1l1 1c2 0 4 0 5 1h0l1 2z" class="M"></path><path d="M432 141h3v2h1v1 1l-1 1h0c-1 0-1 1-2 2-1-1-2-2-2-3-1 0-1 0-1-1h0v-2s1-1 2-1h0z" class="K"></path><path d="M432 141h3v2h1v1 1l-1 1h0c-2-2-2-4-3-5h0z" class="P"></path><path d="M438 141c1 1 1 2 2 3h0 0l2 2v1s-1 0-2 1h0-2c-1-1-2-1-3-2l1-1v-1-1h-1v-2h1 2z" class="X"></path><path d="M438 141c1 1 1 2 2 3-1 0-2-1-2-1h-2-1v-2h1 2z" class="B"></path><path d="M436 144c1 1 2 1 2 2 1 0 2 1 2 2h0-2c-1-1-2-1-3-2l1-1v-1z" class="K"></path><path d="M448 136h1c1 0 1 0 2 1v-1c1 0 1 0 2 1h1 1 1 0c1 0 2 0 3 1h3c2 1 2 0 4 0h1 0 2 3c1 0 4 1 4 0h8v-1h1 9c-2 1-11 0-12 1-7 1-14 0-21 1h-2l-2 1h0-1c0 1-1 1-1 1-1-1-1-2-2-4h-1c-1 0-1 1-1 1-1 0-1-1-2-1v1h-1v-1-1z" class="C"></path><path d="M457 140l-2-2h0c2 0 3 0 4 1l-2 1z" class="e"></path><path d="M482 138l-1 1c-1 1-3 1-5 2-3 1-6 2-10 2l-2-1-3-3h0c7-1 14 0 21-1z" class="m"></path><path d="M528 175v5h1v2c0 1 0 2 1 2v4c1 0 0 0 1-1v-1c1-2 1-4 2-5 0-3 3-9 2-12h1v2 1-1-1c1 0 1 0 1-1v-1h1c-1 3-3 7-4 11l-2 11c0 3 0 5-1 8v-1-3c0-2-1-5-2-6-1-2-1-4-1-6h-1v1h0c0-3 0-6 1-8z" class="U"></path><path d="M550 139h0c-8 6-13 14-18 24v-1h0c-1 1-1 1-1 2h-2c4-9 8-17 16-24l5-1z" class="G"></path><path d="M531 299c0 2 0 2 1 4h0v8 5c0 2 0 4 1 6 2 6-1 14 0 20v6l-1-6c0-2-1-4-1-6l-3-13c2 1 2 3 2 5h0 1l-1-1c0-1 0-4 1-5h0v-23z" class="W"></path><path d="M494 137h1 1c1 1 4 1 6 1h1 5 5 0c2-1 6-1 8 0h7 13c-3 1-7 0-11 0h-22c-3 0-7 0-10 1-6 0-10 2-15 5-1 0-2 1-2 1v-1h-1v-1h0l-5 1c1-1 3-1 3-3h-2c2-1 4-1 5-2l1-1c1-1 10 0 12-1z" class="L"></path><path d="M476 141c2-1 4-1 5-2l3 1h0c-2 1-3 2-4 3h0l-5 1c1-1 3-1 3-3h-2z" class="H"></path><path d="M541 138h10 0-1v1l-5 1h-9-7-5-5-5v2l-1 1h-1c-3-1-5-1-8-2v-2h-6c3-1 7-1 10-1h22c4 0 8 1 11 0z" class="m"></path><path d="M449 138v-1c1 0 1 1 2 1 0 0 0-1 1-1h1c1 2 1 3 2 4 0 0 1 0 1-1h1 0l2-1h2 0l3 3 2 1c4 0 7-1 10-2h2c0 2-2 2-3 3l-12 6h-2 0-2c-1 0-2-1-2-1h-5l2-1c3 0 5-2 9-2h0v-1c-2 1-5 0-7 0h-1c-2-1-3-3-5-4l-1-3z" class="P"></path><path d="M459 139h2 0l3 3 2 1c-3 0-6 1-9 0 0 0-1 0-1-1l1-1v-1h0l2-1z" class="D"></path><defs><linearGradient id="AF" x1="525.447" y1="161.723" x2="536.013" y2="164.757" xlink:href="#B"><stop offset="0" stop-color="#1b1b1c"></stop><stop offset="1" stop-color="#393937"></stop></linearGradient></defs><path fill="url(#AF)" d="M536 140h9c-8 7-12 15-16 24-2 5-4 11-5 16 0 2-1 6-3 8l-1-1h0v-2-1s0-1 1-1v-1-1c1 0 1-1 1-2h-2c3-4 4-8 5-13v-1s0-1 1-2c0-1 1-2 1-3v-1c0-1-1-2 0-4l2-3c1-1 1-2 1-3l7-8h-8v-1h7z"></path><path d="M529 140h7 0c1 1 2 1 3 1h-1v1h1c-1 1-2 2-3 4l-2 4-4 5-3 5v-1c0-1-1-2 0-4l2-3c1-1 1-2 1-3l7-8h-8v-1z" class="B"></path><path d="M512 143h1l1-1v-2h5 5 5v1h8l-7 8c0 1 0 2-1 3l-2 3c-1 2 0 3 0 4v1c0 1-1 2-1 3-1 1-1 2-1 2v1-1c0-6-2-12-6-16-2-2-4-4-7-6z" class="E"></path><path d="M522 145c-1-1-1-2-1-3h3l-1 1-1 2z" class="C"></path><path d="M524 142l2 1 1 1c-1 1-1 1-2 1l-2-2 1-1z" class="H"></path><path d="M523 143l2 2 1 1h-1-1v1 1c-1 0-2-1-2-1v-2l1-2z" class="W"></path><path d="M529 141h8l-7 8c0 1 0 2-1 3l-2 3c0-2-2-4-2-5-1-1-1-1-1-2v-1l1 1v1l1-1v-1l3 1 1-1-2-1 1-2h2v-1s-2-1-2-2z" class="Z"></path><path d="M529 152c-1-1-2-1-3-3h4c0 1 0 2-1 3z" class="C"></path><path d="M512 143h1l1-1v-2h5 5 0c-1 1-3 1-4 1-1 1 0 1 0 2l-1 2c1 0 2 1 2 2 1 2 2 3 3 4s2 3 3 4c-1 2 0 3 0 4v1c0 1-1 2-1 3-1 1-1 2-1 2v1-1c0-6-2-12-6-16-2-2-4-4-7-6z" class="e"></path><path d="M519 140h5 0c-1 1-3 1-4 1-1 1 0 1 0 2l-1 2-4-4 4-1z" class="D"></path><path d="M439 139c0-1 0-2 1-2s2-1 3 0l1 2 1-1c1-1 2-1 3-1v1h1l1 3c2 1 3 3 5 4h1c2 0 5 1 7 0v1h0c-4 0-6 2-9 2l-2 1h5s1 1 2 1h2 0l-5 2-3 1h-2v1h-3l-8-2-2-2-1-1c0-1 0-1 1-1h2 0c1-1 2-1 2-1v-1l-2-2h0 0c-1-1-1-2-2-3v-3l1 1z" class="j"></path><path d="M445 138c1-1 2-1 3-1v1h1l1 3h-1c-2-1-3-1-5-2l1-1z" class="N"></path><path d="M445 138c1-1 2-1 3-1v1 1c-2 0-2-1-3-1z" class="f"></path><path d="M438 141v-3l1 1h0l4 1c1 1 1 1 2 1l3 3c0 1 0 1 1 1 1 1 1 2 2 3h3l-2 1h-5l-2-3h0c-1 0-1 0-1-1v-1c-1 0-2 0-3-1h0l-1 1h0c-1-1-1-2-2-3z" class="H"></path><path d="M439 139l4 1c1 1 1 1 2 1v1c-1 0-1 1-1 1-1 0-2-1-2-1h-1c-1-1-1-2-2-3z" class="L"></path><path d="M445 146l1-1h0l1 1h1 1 0v2h2 3l-2 1h-5l-2-3z" class="W"></path><path d="M440 144l1-1h0c1 1 2 1 3 1v1c0 1 0 1 1 1h0l2 3h5 5s1 1 2 1h2 0l-5 2-3 1h-2v1h-3l-8-2-2-2-1-1c0-1 0-1 1-1h2 0c1-1 2-1 2-1v-1l-2-2h0z" class="Q"></path><path d="M444 151c4 1 8 1 12 1l-3 1h-2v1c-3-1-5-1-7-3z" class="P"></path><path d="M438 150l6 1c2 2 4 2 7 3h-3l-8-2-2-2z" class="M"></path><path d="M440 144l1-1h0c1 1 2 1 3 1v1c0 1 0 1 1 1h0l2 3h5 5s1 1 2 1c-7 1-12 0-19-2h0c1-1 2-1 2-1v-1l-2-2h0z" class="E"></path><path d="M388 139c1-1 6-1 8-1l37-1c0 1-1 2-2 2s-1 2-2 2v2h-1c1 1 1 2 2 3l1-1c0 1 1 2 2 3 1-1 1-2 2-2h0c1 1 2 1 3 2-1 0-1 0-1 1l1 1 2 2 8 2v1h-3c-1 0-3 0-4 1-3 0-4 1-7 2-12-7-25-15-39-18h-2c-1-1-3-1-5-1z" class="a"></path><path d="M433 154v-1h4l3 1h0c-1 1-3 1-4 2h-3-1c1-1 2-1 3-1v-1h-2z" class="M"></path><path d="M418 145l-9-4c-1 0-2 0-3-1l10 1v1c0 1 1 1 1 1v1l1 1h0z" class="b"></path><path d="M417 143c1 0 3 1 3 1 1 0 2 1 3 1s2 1 3 2h0c1 1 1 1 1 2-1 0-1-1-2 0-1 0-4-2-5-2l-2-2h0l-1-1v-1z" class="L"></path><path d="M420 147l1-1c2 1 3 1 5 1h0c1 1 1 1 1 2-1 0-1-1-2 0-1 0-4-2-5-2z" class="O"></path><path d="M418 142c2-2 4-1 6-2l2 2h-2 0l1 1v1l-2 1c-1 0-2-1-3-1 0 0-2-1-3-1 0 0-1 0-1-1v-1l2 1z" class="C"></path><path d="M416 141l2 1h1c1 0 1 0 2 1h0c-1 0-1 0-1 1 0 0-2-1-3-1 0 0-1 0-1-1v-1z" class="E"></path><path d="M421 143c1-1 1 0 1-1s-1 0 0-1l2 1h0l1 1v1l-2 1c-1 0-2-1-3-1 0-1 0-1 1-1z" class="W"></path><path d="M424 142h2l2 1c1 1 1 2 2 3l1-1c0 1 1 2 2 3 1-1 1-2 2-2h0c1 1 2 1 3 2-1 0-1 0-1 1l1 1 2 2h-3v1h-4v1l-1-1 1-1c-3 0-6-1-8-3 1-1 1 0 2 0 0-1 0-1-1-2h0c-1-1-2-2-3-2l2-1v-1l-1-1h0z" class="H"></path><path d="M435 146h0c1 1 2 1 3 2-1 0-1 0-1 1h0c-1 0-2-1-4-1 1-1 1-2 2-2z" class="I"></path><path d="M424 142h2l2 1c1 1 1 2 2 3l2 3h0c-2-1-3-2-5-3l-1 1h0c-1-1-2-2-3-2l2-1v-1l-1-1h0z" class="Z"></path><path d="M480 143h0v1h1v1c-1 1-3 1-4 3 0 0 0 1-1 1-2 2-5 5-7 8s-5 6-4 10v2s0 1-1 2h0v2h3v1c-1 0-2 0-3 1v1c-1 1-1 1-2 1h-1c0-3-1-5-2-7h0l-1-2c-1-1-2-1-4-1h-2l-1 2-11-7-4-3-2-1c3-1 4-2 7-2 1-1 3-1 4-1h3v-1h3v-1h2l3-1 5-2h2l12-6 5-1z" class="D"></path><path d="M467 152h1l-1 2c-1 1-3 2-3 3-1 1-2 3-3 3v1c-2 0-3 1-4 1h-3l-1-1h1c2 0 2 0 4-1 4-2 6-5 9-8z" class="c"></path><path d="M461 150h2c0 1-1 2-2 3s-6 2-6 3c1 1 3 0 4 0-2 1-3 1-5 1l-1-1 1-1h0c-2 0-4 0-6 1-1 0-2 0-3-1h3v-1h3v-1h2l3-1 5-2z" class="O"></path><path d="M480 143h0v1l-13 8c-2 0-3 0-4 2-1 1-1 2-3 2h-1c-1 0-3 1-4 0 0-1 5-2 6-3s2-2 2-3l12-6 5-1z" class="a"></path><path d="M480 144h1v1c-1 1-3 1-4 3 0 0 0 1-1 1-2 2-5 5-7 8h0c-2 0-2 0-3 2h-1l-1 3h-1v-1l1-3v-1c0-1 2-2 3-3l1-2h-1 0l13-8z" class="W"></path><path d="M464 157v1l-1 3v1h1l1-3h1c1-2 1-2 3-2h0c-2 3-5 6-4 10v2s0 1-1 2h0v2h3v1c-1 0-2 0-3 1v1c-1 1-1 1-2 1h-1c0-3-1-5-2-7h0l-1-2c-1-1-2-1-4-1v-3h-2v-1s1-1 1-2h0l1 1h3c1 0 2-1 4-1v-1c1 0 2-2 3-3z" class="P"></path><path d="M461 160l1 1c0 1 0 2-1 3h-1c0-1 0-2 1-3v-1z" class="D"></path><path d="M457 162c1 0 2-1 4-1-1 1-1 2-1 3h1 1 2v4h0c-1 0-1-1-2-1 0-1 0-1-1-2h-1-1c-1 0-2-1-3-2l1-1z" class="W"></path><path d="M459 170v-2h1c1 1 2 2 2 4 1 0 1 1 1 1h1 3v1c-1 0-2 0-3 1v1c-1 1-1 1-2 1h-1c0-3-1-5-2-7z" class="J"></path><path d="M453 161l1 1h3l-1 1c1 1 2 2 3 2h1v3h-1v2h0l-1-2c-1-1-2-1-4-1v-3h-2v-1s1-1 1-2h0z" class="K"></path><defs><linearGradient id="AG" x1="440.096" y1="153.957" x2="454.375" y2="162.166" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#7f7d7e"></stop></linearGradient></defs><path fill="url(#AG)" d="M434 158c3-1 4-2 7-2h5v1c2 1 4 1 6 1 1 0 2 1 3 1s1 0 2 1h1c-2 1-2 1-4 1h-1 0c0 1-1 2-1 2v1h2v3h-2l-1 2-11-7-4-3-2-1z"></path><path d="M448 160c2 0 3 1 4 1v1l-1 1v1h3v3h-2-1c-1-2-1-2-3-3 1-1 1-2 0-4z" class="M"></path><path d="M438 158c1-1 2-1 3 0 2 1 3 1 5 1l2 1c1 2 1 3 0 4 2 1 2 1 3 3h1l-1 2-11-7-4-3s1-1 2-1z" class="N"></path><path d="M441 158c2 1 3 1 5 1-1 1-1 2-2 2s-2-1-3-3zm-5 1s1-1 2-1v1h2v3l-4-3z" class="L"></path><path d="M446 159l2 1c1 2 1 3 0 4l-4-3c1 0 1-1 2-2z" class="H"></path><defs><linearGradient id="AH" x1="529.524" y1="217.796" x2="522.207" y2="217.669" xlink:href="#B"><stop offset="0" stop-color="#c6c4c5"></stop><stop offset="1" stop-color="#fafafa"></stop></linearGradient></defs><path fill="url(#AH)" d="M529 164h2c0-1 0-1 1-2h0v1c-1 4-3 8-4 12-1 2-1 5-1 8h0v-1h1c0 2 0 4 1 6 1 1 2 4 2 6v3 1l-1 15c0 4 1 9 0 13v-5l-2-1h-1v1h-1c0 2-1 7 0 9h0c-1 3-1 6-1 9l-1 24v6l-1 9-1-1v-8l-1-12v-3-1c1-2 1-4 1-6v-11c-1-2 0-3-1-5l-1 1-1-1h0c1-1 1-2 1-3v-2l1-1-1-1c0-1 0-1-1-2l-3-3c-4-4-5-9-6-14h0v-1-1s1 1 2 1v-1l4 1c1-1 2-1 3-2v-1h-3c0-1 1-1 1-2h-1l2-2c2-1 3-2 4-3v-1-1-1c-1 1-2 1-2 1l-1-1c1-1 1-2 2-3h0c2-2 3-6 3-8 1-5 3-11 5-16z"></path><path d="M521 188c2-2 3-6 3-8l-1 13h-1v-1-1c-1 1-2 1-2 1l-1-1c1-1 1-2 2-3h0z" class="K"></path><path d="M521 257h0c1 2 1 4 1 6 0 1 0 2 1 3v6-1-1-4h0c1-1 1-2 1-3v6l-1 9-1-1v-8l-1-12z" class="G"></path><path d="M519 202c0 1 1 1 2 1h1v12h0v-4l-2-1c0-1-1-1-2-1l1-1v-2-1l-3-1c1-1 2-1 3-2z" class="U"></path><path d="M519 205l2 2 1 4-2-1c0-1-1-1-2-1l1-1v-2-1z" class="V"></path><path d="M522 193h1c-1 3 0 5-1 8v1 1h-1c-1 0-2 0-2-1v-1h-3c0-1 1-1 1-2h-1l2-2c2-1 3-2 4-3v-1z" class="Q"></path><path d="M522 202h-1s-3-1-3-2c1-1 2-2 4-2v3 1z" class="K"></path><path d="M520 210l2 1v4h0v13 8c-1-2 0-3-1-5l-1 1-1-1h0c1-1 1-2 1-3v-2l1-1-1-1c0-1 0-1-1-2 1-2 1-5 1-7h0c1 0 1-1 1-1v1c0-2 0-3-1-5z" class="H"></path><path d="M520 210l2 1v4l-1 7-1 2c0-1 0-1-1-2 1-2 1-5 1-7h0c1 0 1-1 1-1v1c0-2 0-3-1-5z" class="B"></path><path d="M512 203l4 1 3 1v1 2l-1 1c1 0 2 0 2 1 1 2 1 3 1 5v-1s0 1-1 1h0c0 2 0 5-1 7l-3-3c-4-4-5-9-6-14h0v-1-1s1 1 2 1v-1z" class="H"></path><path d="M518 213l-2-3v1c-1-1-2-2-2-3h0 1l3 1c1 0 2 0 2 1 1 2 1 3 1 5v-1s0 1-1 1h0l-2-2z" class="f"></path><path d="M516 219v-3l-1-1v-1l-1-1 1-1c0 1 0 1 1 1h2l2 2c0 2 0 5-1 7l-3-3z" class="b"></path><path d="M512 203l4 1 3 1v1 2l-1 1-3-1v-1c-1 0-2 0-3-1l-2-1h0v-1-1s1 1 2 1v-1z" class="P"></path><path d="M512 206l1-1c1 0 2 0 3 1l-1 1h0c-1 0-2 0-3-1z" class="Q"></path><defs><linearGradient id="AI" x1="534.711" y1="197.922" x2="519.631" y2="204.76" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#b2b1b1"></stop></linearGradient></defs><path fill="url(#AI)" d="M527 183h0v-1h1c0 2 0 4 1 6 1 1 2 4 2 6v3 1l-1 15c0 4 1 9 0 13v-5l-2-1h-1v1h-1c0 2-1 7 0 9h0c-1 3-1 6-1 9v-15-31c1-3 1-7 2-10z"></path><path d="M475 190c1 0 2 0 3 1l1 1h2v-1c3 1 7 5 9 7v1l1 1v1 1l1 1v1h4 1v-3c0-1 1-1 1 0l2-1 1-1s-1-1-1-2v-1l2 1h0l1-1 2 2c1 2 1 3 2 4l3 2v1h0c1 5 2 10 6 14l3 3c1 1 1 1 1 2l1 1-1 1v2c0 1 0 2-1 3h0l1 1 1-1c1 2 0 3 1 5v11c0 2 0 4-1 6v1 3l1 12v8h0c-2-1-2-5-3-7 0-2-1-4-1-6-2-6-5-12-7-18h0l-10-21c0-1-1-2-1-2-1-3-3-5-4-7-2-3-3-5-5-7l-9-11c-2-2-4-5-7-8z" class="N"></path><path d="M514 232c1 0 1-1 2-1l1 1h0c0 3 1 6 0 8 0-1-1-2-1-3-1-1-1-2-1-3l-1-2zm-5-9c1 1 1 1 2 1l2 2v2l-1 1-1-1-3 6c-1-2 0-4 0-5 1-1 1-1 1-2v-1-3z" class="d"></path><path d="M509 223c1 1 1 1 2 1l2 2v2l-1 1-1-1v-2h-1-1v-3z" class="Q"></path><path d="M516 237c-1 1-2 1-2 2-1 0 0 1-1 2 0-1-1-1-1-1 1-1 1-2 1-3l-1-1-1 1h-1c0-2 1-3 0-5l1-1 1 1v1 1h0 2 1c0 1 0 2 1 3z" class="L"></path><path d="M519 231l1 1 1-1c1 2 0 3 1 5v11c0 2 0 4-1 6v-9-3c-1-1-1-5-1-5-1 0-1 0-2-1 0-1 1-2 1-4z" class="O"></path><path d="M513 226c1-1 0-2 1-3h2v1l4 2v2c0 1 0 2-1 3l-1-1v1l-1 1-1-1c-1 0-1 1-2 1-1-1-2-2-2-3l1-1v-2z" class="I"></path><path d="M517 228h3c0 1 0 2-1 3l-1-1v1l-1 1-1-1 1-1v-2z" class="B"></path><path d="M517 230h-2c-1-1-1-2-1-4 0-1-1-1 0-2l1 1v1c0 1 1 1 2 2h0v2z" class="J"></path><path d="M475 190c1 0 2 0 3 1l1 1h2v-1c3 1 7 5 9 7v1l1 1v1 1c0 1-1 2-1 3 0-1 0-1-1-2h-1c0-1 0-2-1-2 0-1-3-4-4-5h-1c0-1-1-1-2-2 1 1 2 2 2 3v1c-2-2-4-5-7-8z" class="X"></path><path d="M490 199l1 1v1 1c0 1-1 2-1 3 0-1 0-1-1-2l1-1c-1-1-1-2-1-2l1-1z" class="B"></path><path d="M511 246v-1c0-2-1-5 1-7v3h1c1 1 1 2 1 4l3 7 1 2c1-2 2-3 2-5 0-1-1-3-1-4v-4l-1-4v-1h1v1 1h0c0 1 0 3 1 4v7l1-4c0 2-1 5 0 7v2h0v3l1 12v8h0c-2-1-2-5-3-7 0-2-1-4-1-6-2-6-5-12-7-18z" class="m"></path><defs><linearGradient id="AJ" x1="499.214" y1="218.202" x2="506.018" y2="213.499" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M501 199l4 8c-2 1-1 3-1 5 1 1 1 2 1 3v1c1 2 1 5 1 7v5l1 2h0c-3-2-4-8-5-10l-3-6c-1-1-2-2-2-3v-1c0-1-1-1-2-2l-5-3c0-1 1-2 1-3l1 1v1h4 1v-3c0-1 1-1 1 0l2-1 1-1z"></path><path d="M501 199l4 8c-2 1-1 3-1 5-2-2-1-5-3-7l-1-1h0c0-1 0-2-1-2l-1-1h0l2-1 1-1z" class="L"></path><path d="M491 202l1 1v1h4 1l1 1c0 1 0 2-1 3h-2l-5-3c0-1 1-2 1-3z" class="W"></path><path d="M502 197l1-1 2 2c1 2 1 3 2 4l3 2v1h0c1 5 2 10 6 14l3 3c1 1 1 1 1 2l1 1-1 1-4-2v-1h-2c-1 1 0 2-1 3l-2-2c-1 0-1 0-2-1 0-2-1-4-1-6-1-4-2-7-3-10l-4-8s-1-1-1-2v-1l2 1h0z" class="a"></path><path d="M507 202l3 2v1c-1 0-1 0-2-1 0 3 1 4 1 7l-3-6c1-1 1-2 1-3z" class="X"></path><path d="M510 215c2 3 3 6 6 9v-1h-2c-1 1 0 2-1 3l-2-2c0-1 1-2 0-4 0 0 0-1-1-2v-1c-1-1 0-2 0-2z" class="K"></path><defs><linearGradient id="AK" x1="507.273" y1="209.088" x2="504.519" y2="210.262" xlink:href="#B"><stop offset="0" stop-color="#747474"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#AK)" d="M502 197l1-1 2 2c1 2 1 3 2 4 0 1 0 2-1 3l3 6c0 1 1 3 1 4 0 0-1 1 0 2v1c1 1 1 2 1 2 1 2 0 3 0 4-1 0-1 0-2-1 0-2-1-4-1-6-1-4-2-7-3-10l-4-8s-1-1-1-2v-1l2 1h0z"></path><path d="M502 197l1-1 2 2c1 2 1 3 2 4 0 1 0 2-1 3v-2c-1-2-3-4-4-6z" class="E"></path><defs><linearGradient id="AL" x1="504.26" y1="289.955" x2="552.856" y2="257.179" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#AL)" d="M525 239c0-3 0-6 1-9h0c-1-2 0-7 0-9h1v-1h1l2 1v5 20c0 8 1 16 0 24v1 9 1 5l1 13v23h0c-1 1-1 4-1 5l1 1h-1 0c0-2 0-4-2-5l-5-23c-4-14-10-27-15-41-3-6-5-12-8-18v-7h0c0 1 0 1 1 2v-1c1-1 0-1 1-2 0-2 0-4-1-6v-2l10 21h0c2 6 5 12 7 18 0 2 1 4 1 6 1 2 1 6 3 7h0l1 1 1-9v-6l1-24z"></path><path d="M524 269h0c1 0 1 1 0 1v3c2 0 2 0 3 1v3c-1 2-2 2-4 3v3c-1 0-1-3-1-4l1-1 1-9z" class="Z"></path><defs><linearGradient id="AM" x1="456.008" y1="176.335" x2="441.47" y2="196.228" xlink:href="#B"><stop offset="0" stop-color="#888788"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#AM)" d="M393 140h2c14 3 27 11 39 18l2 1 4 3 11 7 1-2h2c2 0 3 0 4 1l1 2h0c1 2 2 4 2 7h1c1 0 1 0 2-1h2 1v1c0 1 1 1 1 2 1 1 1 1 1 2 0 0 1 1 2 1 1 1 5 6 7 6l3 3v1h-2l-1-1c-1-1-2-1-3-1 3 3 5 6 7 8l9 11c2 2 3 4 5 7 1 2 3 4 4 7 0 0 1 1 1 2v2c1 2 1 4 1 6-1 1 0 1-1 2v1c-1-1-1-1-1-2h0v7c-4-5-7-12-10-17-7-11-15-21-24-30a30.44 30.44 0 0 0-8-8l-1 1s-1-1-1-2c-4-3-8-7-12-10-7-5-14-9-22-14l-6-3-28-12h0 1l1 1v-2h1c0 1 1 1 1 1h1c2 0 3-1 5-2h0l-4-2-1-2h0z"></path><path d="M395 140c14 3 27 11 39 18l2 1 4 3 11 7 2 2c1 1 1 2 2 3-1 0-1-1-3-1l-17-12c-2-1-4-3-6-4-3-2-8-4-12-6l-17-7c-1-1-4-2-6-3l1-1z" class="T"></path><path d="M452 167h2c2 0 3 0 4 1l1 2h0c1 2 2 4 2 7h1c1 0 1 0 2-1h2 1v1c0 1 1 1 1 2 1 1 1 1 1 2 0 0 1 1 2 1 1 1 5 6 7 6l3 3v1h-2l-1-1c-1-1-2-1-3-1 3 3 5 6 7 8l9 11c2 2 3 4 5 7h0c-1 0-1 0-1-1h-1v1l-1-2-3-3-7-9c-3-4-6-7-9-10l-9-8c-1-1-2-3-4-4-3-3-6-5-9-7 2 0 2 1 3 1-1-1-1-2-2-3l-2-2 1-2z" class="S"></path><path d="M454 172c1 0 2 0 3-1 0 0 0-1 1-2l1 1h0c1 2 2 4 2 7h0l-7-5z" class="f"></path><path d="M452 167h2c2 0 3 0 4 1l1 2-1-1c-1 1-1 2-1 2-1 1-2 1-3 1l-1-1-2-2 1-2z" class="j"></path><path d="M464 176h2 1v1c0 1 1 1 1 2 1 1 1 1 1 2 0 0 1 1 2 1 1 1 5 6 7 6l3 3v1h-2l-1-1c-1-1-2-1-3-1l-14-13h0 1c1 0 1 0 2-1z" class="N"></path><path d="M498 139h6v2c3 1 5 1 8 2 3 2 5 4 7 6 4 4 6 10 6 16v1c-1 5-2 9-5 13h2c0 1 0 2-1 2v1 1c-1 0-1 1-1 1v1 2h0l1 1h0c-1 1-1 2-2 3l1 1s1 0 2-1v1 1 1c-1 1-2 2-4 3l-2 2h1c0 1-1 1-1 2h3v1c-1 1-2 1-3 2l-4-1v1c-1 0-2-1-2-1v1l-3-2c-1-1-1-2-2-4l-2-2-1 1h0l-2-1v1c0 1 1 2 1 2l-1 1-2 1c0-1-1-1-1 0v3h-1-4v-1l-1-1v-1-1l-1-1v-1c-2-2-6-6-9-7l-3-3c-2 0-6-5-7-6-1 0-2-1-2-1 0-1 0-1-1-2 0-1-1-1-1-2v-1h-1-2v-1c1-1 2-1 3-1v-1h-3v-2h0c1-1 1-2 1-2v-2c-1-4 2-7 4-10s5-6 7-8c1 0 1-1 1-1 1-2 3-2 4-3 0 0 1-1 2-1 5-3 9-5 15-5z" class="d"></path><path d="M499 192c1-1 2-1 3-1l2 2-1 1s-1 0-1-1h-2l-1-1z" class="b"></path><path d="M504 191h0-1 0c1-1 2-2 4-3h0c0 1 1 1 1 1l-2 2h-2z" class="K"></path><path d="M507 188c1-1 2-1 3-1 0-1 0-1 1-1h0 1l-1 1h1s1 0 1-1h0l3-3c-1 2-3 4-4 6h0l-1-1-3 1s-1 0-1-1zm-10 4l2-1v1l1 1h2c0 1 1 1 1 1l1-1 2 2 2 4-3-1-2-2-1 1h0l-2-1-2-3-1-1z" class="Z"></path><path d="M497 192l2-1v1l1 1 2 4-2-1-2-3-1-1z" class="I"></path><path d="M508 189l3-1 1 1-3 2v1h1c0 1-1 0-1 2l1 1-1 1-1-2-2 1-2-2v-2h2l2-2z" class="d"></path><path d="M504 191h2c1 1 1 2 2 3h0l-2 1-2-2v-2z" class="Q"></path><path d="M519 149c4 4 6 10 6 16v1l-1 1h0c-1-3-1-6-2-9-1-4-2-6-5-8l2-1z" class="J"></path><path d="M509 153c1-1 1-1 2 0 4 3 5 6 6 11 0 4-1 9-3 12-1 1-2 2-2 3 0-1 0-2 1-2v-3h0c1-3 1-5 1-7 1-6-1-10-5-14z" class="i"></path><path d="M508 194l1 2c1 1 2 1 3 2 2 1 3 1 4 1h1c0 1-1 1-1 2h3v1c-1 1-2 1-3 2l-4-1v1c-1 0-2-1-2-1v1l-3-2c-1-1-1-2-2-4l3 1-2-4 2-1z" class="R"></path><path d="M505 198l3 1h0l1 1c0 1 2 2 3 3v1c-1 0-2-1-2-1v1l-3-2c-1-1-1-2-2-4z" class="M"></path><path d="M478 157h2v1h0 0l1-1h0v3h0c1 2 1 4 2 6 1 1 2 2 2 3 1 1 2 1 2 2v1h0v1h-1l-1 1h-1l-1-1-2-1v-2h-1-3c0-1 1-2 2-3 0-1-1-2-1-3v-5-2z" class="S"></path><path d="M479 167c1 1 1 1 3 2v2h2c-1 1-2 1-3 1v-2h-1-3c0-1 1-2 2-3z" class="F"></path><path d="M484 171v-1h1c0 1 0 1 1 1l1 1h0v1h-1l-1 1h-1l-1-1-2-1c1 0 2 0 3-1z" class="c"></path><defs><linearGradient id="AN" x1="493.104" y1="142.557" x2="498.524" y2="154.85" xlink:href="#B"><stop offset="0" stop-color="#a8a7a8"></stop><stop offset="1" stop-color="#cac8c8"></stop></linearGradient></defs><path fill="url(#AN)" d="M504 141c3 1 5 1 8 2 3 2 5 4 7 6l-2 1c-3-3-7-6-11-7h-6c-3 0-5 1-8 2-4 1-8 4-9 8-1 1-1 3-2 4l-1 1h0 0v-1h-2c0-3 3-6 5-9 6-6 13-7 21-7z"></path><path d="M516 183c1 0 3-3 4-4h2c0 1 0 2-1 2v1 1c-1 0-1 1-1 1v1 2h0l1 1h0c-1 1-1 2-2 3l1 1s1 0 2-1v1 1 1c-1 1-2 2-4 3l-2 2c-1 0-2 0-4-1-1-1-2-1-3-2l1-1-1-1c0-2 1-1 1-2h-1v-1l3-2h0c1-2 3-4 4-6z" class="f"></path><path d="M515 190c2-2 3-4 5-6h0v1 2h0l1 1h0c-1 1-1 2-2 3l1 1s1 0 2-1v1 1 1c-1 1-2 2-4 3l-2 2c-1 0-2 0-4-1h2v-1c1-3-1-2-1-4l2-2v-1z" class="V"></path><path d="M519 193h1s1 1 2 1c-1 1-2 2-4 3 0-1 0-1-1-2l2-2z" class="e"></path><path d="M515 190c2-2 3-4 5-6h0v1 2h0l1 1h0c-1 1-1 2-2 3l1 1s1 0 2-1v1 1 1c-1 0-2-1-2-1h-1-1c-1 0-1 1-2 1h-1l1-1v-1l-1-1v-1z" class="O"></path><path d="M515 190c2-2 3-4 5-6h0v1c-1 2-2 4-2 6-1 0-2 0-2-1h-1z" class="E"></path><defs><linearGradient id="AO" x1="488.563" y1="140.036" x2="490.505" y2="160.569" xlink:href="#B"><stop offset="0" stop-color="#151416"></stop><stop offset="1" stop-color="#454443"></stop></linearGradient></defs><path fill="url(#AO)" d="M498 139h6v2c-8 0-15 1-21 7-2 3-5 6-5 9v2 5c0 1 1 2 1 3-1 1-2 2-2 3h3 1v2l2 1-2 1h0c-1 1 0 1-2 1l1 1v1s-1 0-1 1l-1-1v2h-1-5l-1 1 1 1c-1 0-1 1-1 1-1 0-2-1-2-1 0-1 0-1-1-2 0-1-1-1-1-2v-1h-1-2v-1c1-1 2-1 3-1v-1h-3v-2h0c1-1 1-2 1-2v-2c-1-4 2-7 4-10s5-6 7-8c1 0 1-1 1-1 1-2 3-2 4-3 0 0 1-1 2-1 5-3 9-5 15-5z"></path><path d="M475 157c1 0 1 0 1 1s-1 2-1 2v2l2 1v1l-1 1c-1-1-2-2-3-2v-2-2c1-1 1-2 2-2z" class="E"></path><path d="M477 151h1c0 2-3 4-3 6-1 0-1 1-2 2v-1h-1-1 1c1-3 3-5 5-7z" class="P"></path><path d="M477 163c0-1 0-3 1-4v5c0 1 0 2-1 3h-1l-2 2v-1l-1-2v-3c1 0 2 1 3 2l1-1v-1z" class="D"></path><path d="M478 164c0 1 1 2 1 3-1 1-2 2-2 3l-2 1h-4 0v-1c-1 0-1-1-2-2h0 0c1 0 3 0 4 1h1v-1 1l2-2h1c1-1 1-2 1-3z" class="Z"></path><path d="M471 158h1 1v1 2 2 3l1 2v1h-1c-1-1-3-1-4-1l-1-1v-1l-1-2c2-2 3-4 4-6z" class="W"></path><path d="M472 158h1v1 2 3l-2-2c1-1 0-2 1-4z" class="d"></path><path d="M468 166c1-1 1-2 2-2l1 1v2c1 0 1-1 2-1l1 2v1h-1c-1-1-3-1-4-1l-1-1v-1z" class="K"></path><path d="M476 149v1c1 1 1 0 1 1h0c-2 2-4 4-5 7h-1c-1 2-2 4-4 6l1 2v1l1 1h0 0c1 1 1 2 2 2v1h0c-3 0-4 0-6-2v-2c-1-4 2-7 4-10s5-6 7-8z" class="B"></path><path d="M469 168c-1 1-1 1-2 1l-1-1c0-2 0-2 1-4l1 2v1l1 1h0z" class="V"></path><path d="M465 169c2 2 3 2 6 2h4l2-1h3 1v2l2 1-2 1h0c-1 1 0 1-2 1l1 1v1s-1 0-1 1l-1-1v2h-1-5l-1 1 1 1c-1 0-1 1-1 1-1 0-2-1-2-1 0-1 0-1-1-2 0-1-1-1-1-2v-1h-1-2v-1c1-1 2-1 3-1v-1h-3v-2h0c1-1 1-2 1-2z" class="I"></path><path d="M479 174l-1-1v-1c1-1 1-1 3-2v2l2 1-2 1h0-2z" class="O"></path><path d="M465 169c2 2 3 2 6 2h4c-1 1-2 2-3 2-1-1-1 0-2 0h-1v2c-1 1-2 0-3 1h-2v-1c1-1 2-1 3-1v-1h-3v-2h0c1-1 1-2 1-2z" class="K"></path><defs><linearGradient id="AP" x1="469.096" y1="179.829" x2="476.543" y2="175.609" xlink:href="#B"><stop offset="0" stop-color="#343333"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#AP)" d="M479 174h2c-1 1 0 1-2 1l1 1v1s-1 0-1 1l-1-1v2h-1-5l-1 1 1 1c-1 0-1 1-1 1-1 0-2-1-2-1 0-1 0-1-1-2 0-1-1-1-1-2h1c2 0 6-1 8-2h0 2 1v-1z"></path><path d="M490 168l-4-4c-1-3-1-5 0-8 2-4 5-6 9-7 2 0 7 0 10 1 1 1 2 2 4 3 4 4 6 8 5 14 0 2 0 4-1 7h0v3c-1 0-1 1-1 2 0-1 1-2 2-3-1 1-1 3-2 4-1 3-4 6-7 7h0v1c-1 1-2 2-4 3h-2 0l-2 1 1 1 2 3v1c0 1 1 2 1 2l-1 1-2 1c0-1-1-1-1 0v3h-1-4v-1l-1-1v-1-1l-1-1v-1c-2-2-6-6-9-7l-3-3c-2 0-6-5-7-6 0 0 0-1 1-1l-1-1 1-1h5 1v-2l1 1c0-1 1-1 1-1v-1l-1-1c2 0 1 0 2-1h0l2-1 1 1h1l1-1h1v-1h1 3v-1c-2 0-2-1-3-2h0l1-1v1l1-1z" class="N"></path><path d="M489 158c1-3 3-5 5-6 3-1 5 0 8 0 0 1 0 1-1 1h0-2v3h-5v-2l-2 2c0 1 0 2-1 2h-1-1z" class="S"></path><path d="M494 154h1l1-1c2 0 4-1 5 0h-2v3h-5v-2z" class="H"></path><path d="M492 156l2-2v2h5c1 0 2 1 3 2h0l-2 1 2 3c-2 0-3-2-4-3-1 0-1 1-1 1v1c0 2 0 5-1 5v1c-1 1-1 1-2 1l-1-1c-3-1-3-2-4-4v-2-3h1 1c1 0 1-1 1-2z" class="Q"></path><path d="M489 161l3-1h0c0 2 0 2-1 2l-2 1v-2z" class="F"></path><path d="M489 163l2-1 1 1s0 1 1 2c0 1 0 1 1 2h2c-1 1-1 1-2 1l-1-1c-3-1-3-2-4-4z" class="c"></path><path d="M492 156l2-2v2h5c1 0 2 1 3 2h0l-2 1 2 3c-2 0-3-2-4-3-1 0-1 1-1 1v1c0 2 0 5-1 5v-2c-1 0-1 0-1-1h1c0-1 0-1-1-2l1-1c1-1 1-1 2-3h0c-2 0-3 1-4 2h-1l-1-3z" class="I"></path><path d="M502 152c2 1 5 3 6 5h0 0c1 2 2 3 2 4v1c2 3 1 7 0 10-1 4-3 6-6 8 2 1 3 0 4 0-2 3-5 4-8 4h0c-3 1-5 1-8 0-2 0-3-1-5-2l1-3c1-1 1-1 2-1v1h3l1 2h1 2c2-1 4-3 6-5l1-3c1-2 2-4 2-6 0-4-2-6-4-9-1-1-2-2-3-2v-3h2 0c1 0 1 0 1-1z" class="L"></path><path d="M504 178v-1-2l3-3c1-1 1-2 2-3v1c-1 3-2 6-5 8z" class="N"></path><path d="M502 152c2 1 5 3 6 5h0 0c1 2 2 3 2 4v1c2 3 1 7 0 10-1 4-3 6-6 8 2 1 3 0 4 0-2 3-5 4-8 4h0c-3 1-5 1-8 0-2 0-3-1-5-2l1-3c1-1 1-1 2-1v1h3l1 2h1l1 1 5-2c1 0 2-1 3-2 3-2 4-5 5-8 1-2 1-5 0-7 0-1 0-2-1-3v-1c-1-2-3-4-5-5l-2-1c1 0 1 0 1-1z" class="D"></path><path d="M490 178v1h3l1 2c-3 0-4 0-6-2 1-1 1-1 2-1z" class="U"></path><path d="M504 180c2 1 3 0 4 0-2 3-5 4-8 4h0-3v-1c1-1 3-1 4-2 1 0 2 0 3-1z" class="L"></path><path d="M502 158c2 3 4 5 4 9 0 2-1 4-2 6l-1 3c-2 2-4 4-6 5h-2-1l-1-2h-3v-1l-2-3c0-1-1-2-1-2v-1h1 3v-1c-2 0-2-1-3-2h0l1-1v1l1-1 3 3h0c2-1 4-2 5-3h0-4 0c1 0 1 0 2-1v-1c1 0 1-3 1-5v-1s0-1 1-1c1 1 2 3 4 3l-2-3 2-1h0z" class="U"></path><path d="M500 163h2c0 1 1 3 1 4 0 2-1 3-2 4h-1c0-1 1-2 0-4h-1c0-2 1-1 1-2v-2z" class="H"></path><path d="M490 168l3 3h0v1c1 1 1 1 2 1 1-1 0-1 0-2l2-1 1 1v1 1h2c1 0 3 1 4 0l-1 3c-2 2-4 4-6 5h-2-1l-1-2h-3v-1l-2-3c0-1-1-2-1-2v-1h1 3v-1c-2 0-2-1-3-2h0l1-1v1l1-1z" class="Z"></path><path d="M497 181l-1-1c0-1 0-2 1-3 1 0 2 0 2-1 1-1 1 0 1-1 1 0 2 1 3 1-2 2-4 4-6 5z" class="d"></path><path d="M485 174l1-1h1s1 1 1 2l2 3c-1 0-1 0-2 1l-1 3c2 1 3 2 5 2 3 1 5 1 8 0h0c3 0 6-1 8-4 3-1 4-4 5-6h0v3c-1 0-1 1-1 2 0-1 1-2 2-3-1 1-1 3-2 4-1 3-4 6-7 7h0v1c-1 1-2 2-4 3h-2 0l-2 1-2-2c0-1-1-1-2-2h-2l-1 1h-1c-1 0-1 0-1 1-1-1-3-2-4-3h-2c0-1-1-1-2-2v-1h2c-1-1-1-2-2-4h2v-2c-1 1-1 1-2 1l-1-1c0-1 1-1 1-1v-1l-1-1c2 0 1 0 2-1h0l2-1 1 1h1z" class="Q"></path><path d="M482 184c-1-1-1-2-2-4h2c1 1 1 1 2 1-1 1 0 1 0 3 1 1 2 1 3 2-2-1-4-1-5-2z" class="d"></path><path d="M499 191l-1-1c1-1 2-3 4-3h1c1 0 1-1 2-1v1 1c-1 1-2 2-4 3h-2z" class="R"></path><path d="M501 191v-3h4c-1 1-2 2-4 3z" class="I"></path><path d="M482 184c1 1 3 1 5 2s4 2 6 2h-2l-1 1h-1c-1 0-1 0-1 1-1-1-3-2-4-3h-2c0-1-1-1-2-2v-1h2z" class="b"></path><path d="M485 174l1-1h1s1 1 1 2 0 2-1 3c0 1-1 2-3 3-1 0-1 0-2-1v-2c-1 1-1 1-2 1l-1-1c0-1 1-1 1-1v-1l-1-1c2 0 1 0 2-1h0l2-1 1 1h1z" class="N"></path><path d="M484 174h1l1 2h0c-2 1-2 1-4 0 1 0 2-1 2-2z" class="P"></path><path d="M485 174l1-1h1s1 1 1 2 0 2-1 3l-1-2-1-2z" class="R"></path><path d="M483 173l1 1c0 1-1 2-2 2h0-1v1l1 1c-1 1-1 1-2 1l-1-1c0-1 1-1 1-1v-1l-1-1c2 0 1 0 2-1h0l2-1z" class="X"></path><path d="M513 174h0v3c-1 0-1 1-1 2 0-1 1-2 2-3-1 1-1 3-2 4-2 1-4 2-6 4-1 1-2 1-3 1h-1c-3 2-7 2-10 1h-1c-1 0-1 0-2-1-1 0-1-1-2-1-1-1 0-1 0-2h0c2 1 3 2 5 2 3 1 5 1 8 0h0c3 0 6-1 8-4 3-1 4-4 5-6z" class="G"></path><path d="M472 181l-1-1 1-1h5 1v-2l1 1 1 1c1 0 1 0 2-1v2h-2c1 2 1 3 2 4h-2v1c1 1 2 1 2 2h2c1 1 3 2 4 3 0-1 0-1 1-1h1l1-1h2c1 1 2 1 2 2l2 2 1 1 2 3v1c0 1 1 2 1 2l-1 1-2 1c0-1-1-1-1 0v3h-1-4v-1l-1-1v-1-1l-1-1v-1c-2-2-6-6-9-7l-3-3c-2 0-6-5-7-6 0 0 0-1 1-1z" class="U"></path><path d="M477 182c1 0 1 0 2 1v1h-2v-2zm-5-1s1 1 2 1c2 1 3 4 6 4v1 1h-2c-2 0-6-5-7-6 0 0 0-1 1-1z" class="C"></path><path d="M484 187c1 1 3 2 4 3l3 3 4 4c0 1 0 1 1 2l1 2v3h-1-4v-1l-1-1v-1h1 0v-2h0c-1-1-1-1-2-1l1-1h0v-1c-1-2-2-3-4-4-1-1-3-2-4-4l1-1z" class="R"></path><path d="M491 196c1 0 1 1 1 1l3 3c-1 0-1 1-1 1-1 0-2 1-2 2l-1-1v-1h1 0v-2h0c-1-1-1-1-2-1l1-1h0v-1z" class="Q"></path><path d="M490 189l1-1h2c1 1 2 1 2 2l2 2 1 1 2 3v1c0 1 1 2 1 2l-1 1-2 1c0-1-1-1-1 0l-1-2c-1-1-1-1-1-2l-4-4-3-3c0-1 0-1 1-1h1z" class="c"></path><path d="M490 189l1-1h2c1 1 2 1 2 2s-1 1-2 2l-3-3z" class="H"></path><path d="M495 190l2 2 1 1-1 2c-2-1-2-2-4-3 1-1 2-1 2-2z" class="D"></path><path d="M498 193l2 3v1c0 1 1 2 1 2l-1 1-2 1c0-1-1-1-1 0l-1-2h1v-1-2-1l1-2z" class="U"></path><path d="M499 199l1 1-2 1c0-1-1-1-1 0l-1-2h1 2z" class="I"></path><path d="M499 199h-1c-1-1 0-2 0-3h1l1 1c0 1 1 2 1 2l-1 1-1-1z" class="b"></path><path d="M285 382c0-1 0-2 1-3 1-4 5-4 7-7h0 1c0 1 1 3 1 4l3 6h0c2 3 3 5 5 7l1 1h-3c-1-1-1-1-1-2h0v-2l-1 1c-1 0-1 1-2 1 1 0 2 1 1 2 0 3-2 3-2 6v2c2 6 10 11 15 15 4 3 8 7 12 10l3 4v1c2 1 3 3 4 4h0c1 1 2 2 3 2 1 1 0 3 0 4v12c0 3 0 6 1 9 1 1 3 2 5 3 1 1 2 1 3 1h1c0 1-1 1-1 2v2c-1 1-2 1-3 3v1 1l1-1c1 1 1 1 1 2h0v1h-1-1v1l1 2-2 3-1 3c-1 2-1 3-1 5s-1 3 0 5l-1 1-1-1-1 2h0v2-1l1-1c0 2-1 3 0 4h-1c1 3 1 7 1 10 0 2 0 7-1 9 1 1 2 1 3 2h0l11 4c2 1 4 1 6 2v1h-1l-3-1c-1 0-1 1-1 2v5h0l1-1c1 0 2-2 3-3l1-1h0c-1 3-3 5-6 6v-5c1-1 1-2 0-2 0-1-3-1-4-2h-1-1c-2-1-4-1-6 0s-5 2-7 3c-1 0-2 1-3 1h-1-1v2 8l-1-1c0-1 0-1-1-1-1 2 0 6-1 8 1 3 2 5 2 7h1c1 2 2 3 3 4v1h-3v1c2 0 4 2 5 4 2 2 3 5 4 8 2 4 1 8 2 12v1 12c0 5 0 10-1 14v4c0 2-1 3 0 4l-1 2v1l-1-1-2-2v-1c0 1-1 1-2 1l-1-2-1 1v-2l-1 1-1-1-1-3h-1v2c-1-1-1-1-1-2l-2 2v-2h-1c0 1 0 2-1 3h0l-1-1h1c-2-1-3-1-4-1v1c-1 1-2 0-3 1h-1v1l-1 1c-2 0-5-2-7-3-1-1-2-1-4-1l4 2h0v1l2 2c1 2 0 4 0 6 1 2 1 2 2 3s1 2 2 2c0 1 0 1 1 2l1 1v2c-4-4-9-7-14-10-1 1-2 1-3 1-6-3-12-5-19-7-13-3-26-4-40-6-1 0-2-1-4-1h-2-2l-14-2c-14-3-27-8-38-16-4-3-6-5-9-8-2-3-3-7-5-10 2 2 4 4 5 6 7 7 15 11 24 14 17 6 42 6 58-2 18-9 27-27 33-45 7-24 6-50 7-75-1-2 0-5 0-7v-17-63-3h0z" class="l"></path><path d="M305 519l9-3h2 1l-1 1c-2 1-5 2-6 3h-4l-1-1z" class="h"></path><path d="M320 442v-2c-1-1-1-1-1-2l1-1h1c1 2 2 3 3 4l-2 2v3c-1 0-1 1-1 2 0-2 0-4-1-6z" class="X"></path><path d="M327 515l6 3c1 1 2 1 3 2h0v1c-1 0-1 1-1 1l-1 1h-1c-2-2-3-3-5-4-3-1-5-2-7-4 1 0 3 0 5 1h1 0v-1z" class="B"></path><path d="M294 611c-3 0-6-2-8-3s-5-2-8-3-7-2-10-3c-2 0-4-1-6-1h-1c-2-1-4-1-6-1-1 0-2-1-2 0h-1c-2 0-2 0-3 1l-3 2c0-2 4-5 5-5h1c1 1 2 1 3 1h1 0l10 2c8 1 15 4 22 7l6 3z" class="R"></path><defs><linearGradient id="AQ" x1="299.809" y1="535.623" x2="287.743" y2="548.198" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#AQ)" d="M287 551l3-6c2-6 7-9 13-11-2 3-5 4-7 6-3 3-5 7-6 10h-1 0c-1 1-1 1-2 1z"></path><path d="M249 611l2-1v1h1c1-1 2 0 3 0h0 2s1 0 2 1h1 0 1 3l2 1h1c1 1 1 0 2 0 1 1 1 1 3 1 0 1 0 1 1 1h1 2c1 1 3 1 4 2h0c1 0 2 0 2 1 2 0 3 0 5 1h1l2 1c4 2 7 4 10 6 1 1 3 2 4 3v-1c-1-1-3-2-3-4 1 0 0-1 0-2 1 1 1 2 1 3s3 4 4 5h1l1 1v2c-4-4-9-7-14-10-4-2-8-3-12-5l-12-3-21-4z" class="g"></path><path d="M270 615l12 3c4 2 8 3 12 5-1 1-2 1-3 1-6-3-12-5-19-7l-2-2z" class="F"></path><defs><linearGradient id="AR" x1="242.961" y1="619.375" x2="254.816" y2="605.506" xlink:href="#B"><stop offset="0" stop-color="#545053"></stop><stop offset="1" stop-color="#777876"></stop></linearGradient></defs><path fill="url(#AR)" d="M226 610l23 1 21 4 2 2c-13-3-26-4-40-6-1 0-2-1-4-1h-2z"></path><defs><linearGradient id="AS" x1="272.011" y1="562.965" x2="282.333" y2="575.811" xlink:href="#B"><stop offset="0" stop-color="#424140"></stop><stop offset="1" stop-color="#696869"></stop></linearGradient></defs><path fill="url(#AS)" d="M287 551c1 0 1 0 2-1h0 1l-2 5c-1 4-2 7-4 11-3 6-6 12-11 17-1 1-2 3-3 3v1c-1 0-2 1-3 1v1h-1-2c5-6 10-11 14-18 4-6 7-13 9-20z"></path><path d="M285 472h0v-4h0c0 1 0 2 1 3h0c-1 3-1 6-1 9v30c-1 2 0 4 0 6l-2 14c0 2-3 10-2 11 0 2-1 5-1 6h0l1-1v-1-1c0-1 0-1 1-1v-1-1-2c0-1 0 1 1-1v-1-1-1c0-1 0-1 1-2v-1-1-1c0-1 0-2 1-3v-6c1-2 0-5 1-8 1-1 0-3 0-5 1-2 1-5 1-7v-3-7-10c-1-1 0-3 0-4h-1v-1c1-1 1-2 1-3v-1h1c-1 4-1 9-1 13v23c-1 19-5 36-10 54h-1v-1l1-2v-1l1-1v-2c1-1 1-1 1-2-1 1-2 3-2 4l-5 10c-2 4-5 8-7 12-1 1-1 4-3 5v-1c2-2 3-5 5-8 1-1 1-2 2-3l5-10 1-2 1-2c0-1 1-2 1-3 0-2 1-3 1-4v-4c7-24 6-50 7-75z" class="k"></path><path d="M284 566l1 1c-1 2-2 3-3 5l-2 4-4 7-1 1c-2 3-5 6-7 9l-4 3-3 2c-1 0-1 1-2 1h-3 0-1c-1 0-2 0-3-1 5-3 8-5 12-9h2 1v-1c1 0 2-1 3-1v-1c1 0 2-2 3-3 5-5 8-11 11-17z" class="j"></path><path d="M264 589h2 1v-1c1 0 2-1 3-1v-1c-4 5-8 10-15 12v1h1-1c-1 0-2 0-3-1 5-3 8-5 12-9z" class="b"></path><path d="M264 596l4-3c0 1 0 2 1 3l1-1v1l-1 1 3 1 4-3h1l-4 5c2 1 3 1 5 1 5 2 9 3 13 5 4 1 8 4 11 6 2 1 4 2 6 2h0v1l-1 1c-2 0-5-2-7-3-1-1-2-1-4-1l-2-1-6-3c-7-3-14-6-22-7l-10-2h3c1 0 1-1 2-1l3-2z" class="e"></path><path d="M276 595h1l-4 5c-2 0-5-1-7-2h3c1 1 1 1 2 1l1-1 4-3z" class="C"></path><path d="M264 596l4-3c0 1 0 2 1 3l1-1v1l-1 1 3 1-1 1c-1 0-1 0-2-1h-3l-2-2z" class="V"></path><path d="M336 520l11 4c2 1 4 1 6 2v1h-1l-3-1c-1 0-1 1-1 2v5h0l1-1c1 0 2-2 3-3l1-1h0c-1 3-3 5-6 6v-5c1-1 1-2 0-2 0-1-3-1-4-2h-1-1c-2-1-4-1-6 0s-5 2-7 3c-1 0-2 1-3 1h-1-1c-2 2-5 2-9 2-1 0-1 0-2 1h0l-3 1c-1 0-2 1-2 2-2 1-4 2-5 4-1 1-2 1-4 2-1 2-3 3-4 5 0 1-1 1-1 1v2h-1v2l-2 4-1 1-1-1 2-5c1-3 3-7 6-10 2-2 5-3 7-6 7-4 15-5 23-8 2-1 5-2 8-3l1-1s0-1 1-1v-1z" class="f"></path><path d="M285 382c0-1 0-2 1-3 1-4 5-4 7-7h0 1c0 1 1 3 1 4l3 6h0c2 3 3 5 5 7l1 1h-3c-1-1-1-1-1-2h0v-2l-1 1c-1 0-1 1-2 1 1 0 2 1 1 2 0 3-2 3-2 6v2l-1-1h0v3 22 6 3 11c-1 2 0 4-1 5v3-9l-1-6v-19-6-3c0-1 0-2-1-3v-1c0-3 1-7 1-10 1-2 0-4 0-6 0-1-1-2-1-2-2-1-5-2-7-1v1-3h0z" class="G"></path><path d="M295 384c1 1 2 1 2 2l-1 1c-1 1-1 2-2 2-1-1-1-3 0-4v3c1-1 0-2 1-3v-1z" class="F"></path><path d="M294 400c0-2-1-6 0-8h1 0c0 2 0 3 1 4h0v2l-1-1h0v3h-1z" class="L"></path><path d="M294 400h1v22 6 3 11c-1 2 0 4-1 5v-47z" class="C"></path><path d="M285 382c0-1 0-2 1-3 1-4 5-4 7-7h0 1c0 1 1 3 1 4l3 6h0l-2 1-1 1v1c-1 1 0 2-1 3v-3-1l-1-1c-3-1-5-1-8-1h0z" class="J"></path><path d="M285 382c2-1 3-2 5-3 1 0 1 0 2 1s1 2 2 3v1l-1-1c-3-1-5-1-8-1h0z" class="F"></path><path d="M282 572c1 1 1 1 1 2 1 0 1 1 1 2s0 1 1 2v3l3 8c0 2 1 3 2 4l1 2v1c-1-1-1-3-4-3l2 4c-1-1-1-1-2-1-1 1-2 1-3 2l-2 2-1-1-1 1v-1h-3-1 0v1h0c1 0 2 1 2 1-2 0-3 0-5-1l4-5h-1l-4 3-3-1 1-1v-1l-1 1c-1-1-1-2-1-3 2-3 5-6 7-9l1-1 4-7 2-4z" class="R"></path><path d="M285 594v1c-1 2-3 3-4 4-1-2 2-4 2-6h2v1z" class="G"></path><path d="M283 589v-1h1c1 1 3 4 3 5l2 4c-1-1-1-1-2-1s-1-1-2-2v-1c-1-2-1-3-2-4z" class="M"></path><path d="M281 584l1-1c1 0 2 0 2 2v3h-1v1c0 3-4 7-7 10h0v1h0c1 0 2 1 2 1-2 0-3 0-5-1l4-5h-1c3-4 4-7 5-11z" class="E"></path><path d="M282 572c1 1 1 1 1 2 1 0 1 1 1 2s0 1 1 2v3l3 8c0 2 1 3 2 4l1 2v1c-1-1-1-3-4-3 0-1-2-4-3-5v-3c0-2-1-2-2-2l-1 1v-1h-1c1-2 1-4 0-7l2-4z" class="C"></path><defs><linearGradient id="AT" x1="280.231" y1="574.559" x2="282.527" y2="580.613" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#848283"></stop></linearGradient></defs><path fill="url(#AT)" d="M282 572c1 1 1 1 1 2l-1 9-1 1v-1h-1c1-2 1-4 0-7l2-4z"></path><path d="M276 583l4-7c1 3 1 5 0 7h1v1c-1 4-2 7-5 11l-4 3-3-1 1-1v-1l-1 1c-1-1-1-2-1-3 2-3 5-6 7-9l1-1z" class="E"></path><path d="M275 584l1-1h0 2c0 2-1 2-1 3h-1c-1-1-1-1-1-2z" class="V"></path><path d="M280 583h1v1c-1 4-2 7-5 11l-4 3-3-1 1-1h1c1-1 1-2 2-2 3-3 5-7 7-11z" class="R"></path><path d="M287 593c3 0 3 2 4 3 1 0 1 1 2 1 0 1 1 2 2 3 2 0 4 1 6 2l2 1h3s1 0 2 1h0 1c2-1 4-2 5-3h1 2l1 1c-1 1-1 2-2 3h0l4-1c1 1 1 1 1 2l1-1 1 1v1c0 1 0 3 1 4 0 1-1 2-1 3l-1-3h-1v2c-1-1-1-1-1-2l-2 2v-2h-1c0 1 0 2-1 3h0l-1-1h1c-2-1-3-1-4-1v1c-1 1-2 0-3 1h-1 0c-2 0-4-1-6-2-3-2-7-5-11-6-4-2-8-3-13-5 0 0-1-1-2-1h0v-1h0 1 3v1l1-1 1 1 2-2c1-1 2-1 3-2 1 0 1 0 2 1l-2-4z" class="K"></path><path d="M287 593c3 0 3 2 4 3 1 0 1 1 2 1 0 1 1 2 2 3h-2v2c-1-1-2-1-2-2-1-1-2-2-2-3l-2-4z" class="L"></path><defs><linearGradient id="AU" x1="292.869" y1="604.979" x2="312.119" y2="606.969" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#4a4849"></stop></linearGradient></defs><path fill="url(#AU)" d="M293 602v-2h2c2 0 4 1 6 2-1 0-3 0-3 1s4 4 5 5h1c1 1 3 2 5 2h3c1 1 1 2 0 2v1c-2 0-7-1-8-2l-1-1c-4-2-7-5-10-8z"></path><path d="M314 601h1 2l1 1c-1 1-1 2-2 3h0l4-1c1 1 1 1 1 2l1-1 1 1v1c0 1 0 3 1 4 0 1-1 2-1 3l-1-3h-1v2c-1-1-1-1-1-2l-2 2v-2h-1c0 1 0 2-1 3h0l-1-1h1c-2-1-3-1-4-1 1 0 1-1 0-2h-3c-2 0-4-1-5-2h-1c-1-1-5-4-5-5s2-1 3-1l2 1h3s1 0 2 1h0 1c2-1 4-2 5-3z" class="R"></path><path d="M309 610c3 0 5 0 8-1h1v2h-1c0 1 0 2-1 3h0l-1-1h1c-2-1-3-1-4-1 1 0 1-1 0-2h-3z" class="M"></path><path d="M317 609c2 0 4-1 6-2 0 1 0 3 1 4 0 1-1 2-1 3l-1-3h-1v2c-1-1-1-1-1-2l-2 2v-2-2h-1z" class="Z"></path><path d="M316 605l4-1c1 1 1 1 1 2 0 0-1 0-2 1h-3l-1 1h-2-1c-1 0-1 0-2-1l1-1h2c1 0 2-1 3-1h0z" class="F"></path><path d="M316 605l4-1c1 1 1 1 1 2 0 0-1 0-2 1-1 0-1 0-2-1l-1-1z" class="G"></path><path d="M314 601h1 2l1 1c-1 1-1 2-2 3-1 0-2 1-3 1l-3-1-1-1c2-1 4-2 5-3z" class="f"></path><path d="M301 602l2 1h3s1 0 2 1h0 1l1 1c-1 1-2 0-3 1h0 0c0 1 1 1 1 1-1 1-3 1-4 1h0 0-1c-1-1-5-4-5-5s2-1 3-1z" class="P"></path><path d="M301 602l2 1h3s1 0 2 1h0v1c-4 1-6-1-10-2h0c0-1 2-1 3-1z" class="B"></path><defs><linearGradient id="AV" x1="315.217" y1="499.606" x2="289.155" y2="518.595" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#525153"></stop></linearGradient></defs><path fill="url(#AV)" d="M294 480c0 3 1 5 2 8 2 2 3 4 4 6 2 3 5 5 8 8l5 5c4 3 9 6 14 8v1h0-1c-2-1-4-1-5-1-3 0-4 0-7 1l-9 3c-1 1-2 2-3 2-4 3-6 5-9 9 1-12 1-24 1-36 0-2-1-13 0-14z"></path><path d="M308 508c0 1 1 2 1 2 2 1 3 2 5 3l-5 2c-1 0-1-1-1-1h-1l-1 1h0c-1-1 0-2 0-3 1 0 1 0 1-1 1-1 1-1 1-3z" class="d"></path><path d="M308 514v-2l1-1c1 0 1 1 1 2l-1 1v1c-1 0-1-1-1-1z" class="J"></path><path d="M294 480c0 3 1 5 2 8v3 2 1c1 0 1 1 1 1l1 2c1 1 1 2 1 2l-1 1c0-1-1-2-1-4 0 0 0-1-1-1v-2c0-1-1-2-1-3 0 1 0 3-1 4h0c0-2-1-13 0-14z" class="B"></path><path d="M304 511l2 1c0 1-1 2 0 3h0l1-1h1s0 1 1 1l-1 1h0c-2 0-3 0-4 1s-2 1-3 1h-1-1-1-2 0c1 0 2-2 3-3l2 2v-1-2h0c1 1 2 1 3 1v-1l-1-2 1-1z" class="D"></path><path d="M299 499c2 3 4 6 6 7 1 1 2 2 3 2 0 2 0 2-1 3 0 1 0 1-1 1l-2-1-1 1 1 2v1c-1 0-2 0-3-1h0v2 1l-2-2c-1 1-2 3-3 3 1-3 1-5 3-8h-1l-1 1v-1-4-2l1-1c-1-1-1-1-1-2h1v1h1c0-1 0-1-1-2l1-1z" class="K"></path><path d="M299 510h3c0 1 1 1 1 2l-1 1c-2 0-2 0-3-1v-2zm-2-4h3 0l-1-1v-1c1 0 1 1 2 1v1l1 2v1h-2-1c-1-1-1-1-2-3z" class="G"></path><path d="M304 511h-1c0-1 0-2 1-3h0l2 1s1-1 2-1c0 2 0 2-1 3 0 1 0 1-1 1l-2-1z" class="T"></path><path d="M299 510v2c1 1 1 1 3 1-1 1-2 1-2 1l-1 1c-1 1-2 3-3 3 1-3 1-5 3-8z" class="F"></path><path d="M294 480v-3c-1-2 0-7-1-7-1 1-1 1-1 3v-3-3c1-2 1-4 1-6 1 3 0 7 1 10 1 1 0 2 1 3v1 1 1c1 1 1 1 1 2s1 2 2 2c3 0 3 0 5 1h1c1 1 1 1 1 2 1 1 2 3 2 4h2v-2h1 0c1 2 2 2 4 4h1c6 4 12 7 18 9 1 3 1 7 1 10 0 2 0 7-1 9l-6-3c-5-2-10-5-14-8l-5-5c-3-3-6-5-8-8-1-2-2-4-4-6-1-3-2-5-2-8z" class="g"></path><defs><linearGradient id="AW" x1="321.261" y1="420.489" x2="279.044" y2="447.071" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#AW)" d="M295 400v-3h0l1 1c2 6 10 11 15 15 4 3 8 7 12 10l3 4v1c0 2 0 5-1 6l-1 1v3l1 1c0 1 0 1-1 2-1-1-2-2-3-4h0c1-1 1-1 1-3h-3c-1 1-2 2-4 2-4 2-9 7-11 11-2 2-3 5-4 8-2 5-2 11-3 17v8c-3-9-3-20-3-30v-3c1-1 0-3 1-5v-11-3-6-22z"></path><path d="M298 452l1-1-3 10v-6c1-1 1-2 2-3z" class="E"></path><path d="M320 431h0c0-1 1-2 1-3h0l2 2v2c0 1-1 1-1 2h-3c0-1 1-2 1-3z" class="Y"></path><path d="M323 430l1 5v3l1 1c0 1 0 1-1 2-1-1-2-2-3-4h0c1-1 1-1 1-3 0-1 1-1 1-2v-2z" class="S"></path><defs><linearGradient id="AX" x1="301.2" y1="408.388" x2="299.418" y2="415.03" xlink:href="#B"><stop offset="0" stop-color="#393a39"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#AX)" d="M297 414v-9h1c1 2 3 3 4 5h-1c1 1 1 2 1 3s0 2 1 3v2h-1s0 1-1 1-1 0-2-1h0c0-2-1-2-1-4h-1z"></path><path d="M297 414l1-7 2 5c-1 1-1 2-2 2h-1z" class="M"></path><path d="M300 412c0 1 1 2 1 4l1 2s0 1-1 1-1 0-2-1h0c0-2-1-2-1-4 1 0 1-1 2-2z" class="Q"></path><path d="M299 418c1-2 1-2 2-2l1 2s0 1-1 1-1 0-2-1h0z" class="R"></path><path d="M303 435l1-1v1c1 1 1 2 1 3 0 0 1 0 1 1-1 1-2 2-2 3-2 3-4 6-5 9l-1 1c-1 1-1 2-2 3 0-3 0-7 1-11v2c1-1 1-1 1-2 1-3 3-6 5-9z" class="Q"></path><path d="M303 435v1 2c-4 4-4 9-5 14-1 1-1 2-2 3 0-3 0-7 1-11v2c1-1 1-1 1-2 1-3 3-6 5-9z" class="D"></path><path d="M297 414h1c0 2 1 2 1 4h0c1 1 1 1 2 1 0 1 0 2 1 3-1 0-1 0-1 1l1 1 1 1h1 0v2c1 0 1 0 1 1l-1 1h0l1 2h1v2c-1 0-1 1-2 1h0l-1 1c-2 3-4 6-5 9 0 1 0 1-1 2v-2-6-3-1-5-3-12z" class="U"></path><path d="M297 434c1-1 1-1 1-2h2v2l-2 1h0-1v-1z" class="R"></path><path d="M297 426l1-3h0c1 2 2 3 2 5l-2-1v2h-1v-3z" class="F"></path><path d="M298 429v-2l2 1c1 1 1 1 1 2-1 1-1 1-2 1h-1v-2z" class="Y"></path><path d="M299 418c1 1 1 1 2 1 0 1 0 2 1 3-1 0-1 0-1 1l1 1 1 1h1 0v2c1 0 1 0 1 1l-1 1v-1c-1 0-1 0-2-1-2-1-2-3-4-5h0v-2h1v-2z" class="F"></path><path d="M298 444c-1-2 0-5 0-7h1c0-2 1-2 2-3 0-1 0-1 1-2s1-2 2-3l1 2h1v2c-1 0-1 1-2 1h0l-1 1c-2 3-4 6-5 9z" class="G"></path><path d="M302 413c0-1 0-2-1-3h1l10 8c2 2 5 3 6 6h0-1l-2 2v-2s1-1 1-2h-1l-2 2c0 1-1 1-2 1h-1l-1 1h-3s0 1-1 2c0-1 0-1-1-1v-2h0-1l-1-1-1-1c0-1 0-1 1-1-1-1-1-2-1-3 1 0 1-1 1-1h1v-2c-1-1-1-2-1-3z" class="H"></path><path d="M310 421l2-1h1c0 2-2 3-2 5h-1l-1 1h-3c0-1 1-2 1-2 1-1 2-3 3-3z" class="G"></path><path d="M307 424c2 0 2 0 3 1l-1 1h-3c0-1 1-2 1-2z" class="l"></path><path d="M302 413c2 0 2 0 2 1v2h1l1-1c0 1 1 1 1 2 1 0 1 1 1 1h2v1 1 1c-1 0-2 2-3 3 0 0-1 1-1 2 0 0 0 1-1 2 0-1 0-1-1-1v-2h0-1l-1-1-1-1c0-1 0-1 1-1-1-1-1-2-1-3 1 0 1-1 1-1h1v-2c-1-1-1-2-1-3z" class="K"></path><path d="M307 419h2v2h-2v-2z" class="F"></path><path d="M302 422c0-1 1-1 2-1h1l1-1h-1c-1-1-1-1-1-2v-1h1l2 2h0v2c0 1 0 2-1 3-1 0-1 0-1-1-1 1-1 1-1 2h0-1l-1-1-1-1c0-1 0-1 1-1z" class="G"></path><path d="M311 425c1 0 2 0 2-1l2-2h1c0 1-1 2-1 2v2l2-2h1v4 1h1c0 1 0 1 1 2 0 1-1 2-1 3-1 1-2 2-4 2-4 2-9 7-11 11v-1s0-1 1-2v-3l-1 1c0-1 1-2 2-3 0-1-1-1-1-1 0-1 0-2-1-3v-1h0c1 0 1-1 2-1v-2h-1l-1-2h0l1-1c1-1 1-2 1-2h3l1-1h1z" class="C"></path><path d="M306 431c0 1 0 1 1 1l1 1h0 1 1l1 1c-1 2-3 3-4 4l-1 1c0-1-1-1-1-1 0-1 0-2-1-3v-1h0c1 0 1-1 2-1v-2z" class="l"></path><path d="M310 433l1 1c-1 2-3 3-4 4v-4c1 1 2 0 2 0l1-1z" class="J"></path><path d="M311 425c1 0 2 0 2-1l2-2h1c0 1-1 2-1 2v2l2-2h1v4l-1-1h-2-1l1 1h2v2c-1 0-1 1-1 0l-2-1v1c0 1 0 1 1 2v1c1 0 1-2 2-1l1 1-1 1h-2l-1-1v-1h-1l-1 1c-1-1-2-1-2-2h-1v2h-1 0l-1-1c-1 0-1 0-1-1h-1l-1-2h0l1-1c1-1 1-2 1-2h3l1-1h1z" class="S"></path><path d="M306 426h3c-1 1-2 3-3 4h0c-1 0-1 0-1 1l-1-2h0l1-1c1-1 1-2 1-2z" class="Y"></path><path d="M308 430h0c1-1 1-1 1-2l1-1c1 0 1 0 2 1v1h0c1 1 1 2 1 3l-1 1c-1-1-2-1-2-2h-1v2h-1 0l-1-1c-1 0-1 0-1-1h-1c0-1 0-1 1-1h0 2z" class="G"></path><path d="M305 431c0-1 0-1 1-1h0 2c0 1 0 1-1 2-1 0-1 0-1-1h-1z" class="i"></path><path d="M326 428c2 1 3 3 4 4h0c1 1 2 2 3 2 1 1 0 3 0 4v12c0 3 0 6 1 9 1 1 3 2 5 3 1 1 2 1 3 1h1c0 1-1 1-1 2v2c-1 1-2 1-3 3v1 1l1-1c1 1 1 1 1 2h0v1h-1-1v1l1 2-2 3-1 3c-1 2-1 3-1 5s-1 3 0 5l-1 1-1-1-1 2h0v2-1l1-1c0 2-1 3 0 4h-1c-6-2-12-5-18-9l-3-4c-3-3-4-6-5-9-1-1-1-2-1-3-1-9-1-18 3-26 1-2 2-3 3-5 0-1 0-1 1-1 1-2 1-2 3-2l1 1h2 0c0 1 1 1 1 1 1 2 1 4 1 6 0-1 0-2 1-2v-3l2-2c1-1 1-1 1-2l-1-1v-3l1-1c1-1 1-4 1-6z" class="g"></path><path d="M327 466c2-1 2-2 4-1 0 0 0 1-1 1 0 1 0 3 1 4-2 0-2 0-3-1l-2-2v-1h1z" class="h"></path><path d="M327 466c2-1 2-2 4-1 0 0 0 1-1 1v2l-1 1c0-1-1-2-2-3z" class="l"></path><path d="M331 445h0v7c0 3-1 5-4 7l-1-1c2-1 2-3 2-6v-4h1v-1c0-1 1-2 2-2z" class="J"></path><path d="M331 445h0v5c-1 0-1 0-2-1v-1-1c0-1 1-2 2-2z" class="c"></path><path d="M329 473v3c1 1 0 2 1 3v4c0 1-1 2-1 3s0 3-1 5c2-1 1-3 3-3-1 2-2 4-3 5s-1 1-1 0 0-3 1-5v-7l-1-4v-2c0-1 1-2 2-2zm1-41c1 1 2 2 3 2 1 1 0 3 0 4v12 4c-1-2-1-5 0-6v-1c0-2-1-3-1-4h-1v1c0 1 1 2 1 3 0 2 0 4-1 5v-7h0c-1-3-2-10-1-13z" class="i"></path><path d="M325 481c1 1 1 1 2 1l1-1v7c-1 2-1 4-1 5-2 0-3-1-4-2-2 0-3-1-4-1h2c1 0 2-1 2-2l2-3v-2-1-1z" class="e"></path><path d="M325 481c1 1 1 1 2 1l1-1v7l-1-1h0c-1 1-1 2-2 2-1-1 2-4 1-6h-1v-1-1z" class="D"></path><path d="M321 463v1h1c1-2 3-4 4-6l1 1c-1 2-3 4-5 5 0 2 1 2 1 2h3v1l2 2c1 1 1 1 3 1-1 1-1 2-2 3-1 0-2 1-2 2v2l-1-2c-1-4-4-6-5-9v-3z" class="Y"></path><path d="M326 467l2 2c-2 1-2 1-3 0 0-2 0-1 1-2z" class="S"></path><defs><linearGradient id="AY" x1="331.751" y1="435.631" x2="326.339" y2="442.158" xlink:href="#B"><stop offset="0" stop-color="#90908d"></stop><stop offset="1" stop-color="#abaaac"></stop></linearGradient></defs><path fill="url(#AY)" d="M326 428c2 1 3 3 4 4h0c-1 3 0 10 1 13-1 0-2 1-2 2v1h-1v4-12l-2 2c-2 2-2 1-4 1l2-2c1-1 1-1 1-2l-1-1v-3l1-1c1-1 1-4 1-6z"></path><path d="M325 439c1-1 1-2 1-4 0-1 1-2 1-3h1c1 5 0 11 0 16v4-12l-2 2c-2 2-2 1-4 1l2-2c1-1 1-1 1-2z" class="T"></path><defs><linearGradient id="AZ" x1="321.799" y1="494.326" x2="326.881" y2="488.123" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#333131"></stop></linearGradient></defs><path fill="url(#AZ)" d="M312 486h0c2 0 3 2 5 2 0 1 2 1 2 2 1 0 2 1 4 1 1 1 2 2 4 2 0 1 0 1 1 0s2-3 3-5h1c0 2 0 5 1 7v2-1l1-1c0 2-1 3 0 4h-1c-6-2-12-5-18-9l-3-4z"></path><path d="M322 443c2 0 2 1 4-1l2-2v12c0 3 0 5-2 6-1 2-3 4-4 6h-1v-1l1-2v-2c1-4 1-9 0-13v-3z" class="g"></path><path d="M322 443v1c1 1 1 2 1 3h1v-2 4c1 3 1 6 0 9 0 1-1 3-2 3v-2c1-4 1-9 0-13v-3z" class="i"></path><path d="M333 454v-4c0 3 0 6 1 9 1 1 3 2 5 3 1 1 2 1 3 1h1c0 1-1 1-1 2v2c-1 1-2 1-3 3v1 1l1-1c1 1 1 1 1 2h0v1h-1-1v1l1 2-2 3-1 3c-1 2-1 3-1 5s-1 3 0 5l-1 1-1-1v-16l-1-23z" class="D"></path><path d="M333 454v-4c0 3 0 6 1 9 1 1 3 2 5 3 1 1 2 1 3 1h1c0 1-1 1-1 2-1 0-3 0-4-1l-1-1c0-1-3-2-3-2v10 6l-1-23z" class="U"></path><path d="M326 475l1 2 1 4-1 1c-1 0-1 0-2-1v1 1 2l-2 3c0 1-1 2-2 2h-2c0-1-2-1-2-2-2 0-3-2-5-2h0c-3-3-4-6-5-9h0c1-1 2-2 3-1 0 1 0 2 1 3h1v-3l1-1 1 1 3 2c2 0 4-1 5-2h2v-1h2z" class="E"></path><path d="M314 480l-1-4h1l3 2v2c-1-1-2-1-3 0z" class="c"></path><path d="M314 480c1-1 2-1 3 0s3 2 5 3l3-1v1 2c-1 0-1 0-2 1-1 0-4-1-6-2-1-1-3-2-3-4z" class="Z"></path><path d="M307 477h0c1-1 2-2 3-1 0 1 0 2 1 3 0 1 1 3 1 4 1 2 7 5 9 5h2 0c0 1-1 2-2 2h-2c0-1-2-1-2-2-2 0-3-2-5-2h0c-3-3-4-6-5-9z" class="C"></path><path d="M326 475l1 2 1 4-1 1c-1 0-1 0-2-1v1l-3 1c-2-1-4-2-5-3v-2c2 0 4-1 5-2h2v-1h2z" class="R"></path><path d="M320 479h1c1 0 2 1 3 2l-1 1c-1 0-2-1-3-1v-1-1z" class="S"></path><path d="M326 475l1 2 1 4-1 1c-1 0-1 0-2-1 0-2 0-3-1-6h2z" class="O"></path><path d="M306 474c-1-9-1-18 3-26 1-2 2-3 3-5 0-1 0-1 1-1 1-2 1-2 3-2l1 1h2 0c0 1 1 1 1 1 1 2 1 4 1 6 0-1 0-2 1-2 1 4 1 9 0 13v2l-1 2v3c1 3 4 5 5 9h-2v1h-2c-1 1-3 2-5 2l-3-2-1-1-1 1v3h-1c-1-1-1-2-1-3-1-1-2 0-3 1h0c-1-1-1-2-1-3z" class="e"></path><path d="M316 473l1 3h0c-2 0-3-2-4-3s-1-6-1-8l1 1v2 2h1v1l1 1 1 1z" class="R"></path><path d="M315 455c0 1 1 3 2 4v1c-2 2-3 4-4 6l-1-1c1-3 3-7 3-10z" class="T"></path><path d="M318 469c2 1 3 2 4 3v1h-1c-1 1-1 2-2 2h-1v-1-2h-1l-1 1-1-1c1-1 1-1 3-1v-1-1z" class="B"></path><path d="M316 440l1 1h2 0c0 1-1 2-1 2 0 3-1 4-2 6v4c0 1 0 1-1 1 0-2-2-5-2-7-1-1-1-2 0-3h1 0c0 2 0 4 1 5l1 1v-7-3z" class="Y"></path><path d="M319 441c0 1 1 1 1 1 1 2 1 4 1 6-1 4-1 8-4 12v-1c-1-1-2-3-2-4v-1c1 0 1 0 1-1v-4c1-2 2-3 2-6 0 0 1-1 1-2z" class="g"></path><path d="M322 446c1 4 1 9 0 13v2l-1 2v3l-1-1-1 2c0 1-1 1-1 2v1 1c-2 0-2 0-3 1l-1-1v-1h-1v-2-2c1-2 2-4 4-6 3-4 3-8 4-12 0-1 0-2 1-2z" class="d"></path><path d="M317 466l2 1c0 1-1 1-1 2v1h-1v-4z" class="I"></path><path d="M316 463l1 1c-1 2-1 5-3 6h-1v-2c1-2 2-3 3-5z" class="S"></path><path d="M319 462l3-3v2l-1 2v3l-1-1-1 2-2-1c1-1 2-2 2-4z" class="R"></path><path d="M322 446c1 4 1 9 0 13l-3 3v-1c-1 0-2 0-3 1v1c-1 2-2 3-3 5v-2c1-2 2-4 4-6 3-4 3-8 4-12 0-1 0-2 1-2z" class="B"></path><path d="M306 474h0c1-9 3-17 6-26v6c0 1 1 2 1 3-1 4-2 7-2 11 0 2 1 5 2 7l-1 1v3h-1c-1-1-1-2-1-3-1-1-2 0-3 1h0c-1-1-1-2-1-3z" class="K"></path><path d="M307 535c0-1 1-2 2-2l3-1h0c1-1 1-1 2-1 4 0 7 0 9-2v2 8l-1-1c0-1 0-1-1-1-1 2 0 6-1 8 1 3 2 5 2 7h1c1 2 2 3 3 4v1h-3v1c2 0 4 2 5 4 2 2 3 5 4 8 2 4 1 8 2 12v1 12c0 5 0 10-1 14v4c0 2-1 3 0 4l-1 2v1l-1-1-2-2v-1c0 1-1 1-2 1l-1-2-1 1v-2l-1 1-1-1c0-1 1-2 1-3-1-1-1-3-1-4v-1l-1-1-1 1c0-1 0-1-1-2l-4 1h0c1-1 1-2 2-3l-1-1h-2-1c-1 1-3 2-5 3h-1 0c-1-1-2-1-2-1h-3l-2-1c-2-1-4-2-6-2-1-1-2-2-2-3-1 0-1-1-2-1v-1l-1-2c-1-1-2-2-2-4-1-2-2-5-3-8v-3c-1-1-1-1-1-2s0-2-1-2c0-1 0-1-1-2 1-2 2-3 3-5l-1-1c2-4 3-7 4-11l1 1 1-1 2-4v-2h1v-2s1 0 1-1c1-2 3-3 4-5 2-1 3-1 4-2 1-2 3-3 5-4z" class="G"></path><path d="M300 550l1 2-1 1h-1l1-3z" class="T"></path><path d="M295 554l5-6v2l-1 3-2 1c-1 1-2 2-2 3s-1 2-1 2v1c-1 1-2 3-3 4v-2c1-3 4-5 4-8z" class="F"></path><path d="M285 581c0-1 0-6 1-7s0-1 1-1c0-1 1-2 1-2-1 3-1 6-1 9 0 1 1 3 1 4 0 2-1 4 0 5-1-2-2-5-3-8z" class="R"></path><path d="M302 559v1l-2 2-1 1-1 1c-1 2-3 4-4 7v-2l-2 2c0 1 0 2-1 3v1c-1 2-1 3-1 5v-8l1-3c2-5 6-8 11-10z" class="C"></path><path d="M291 575v-1c1-1 1-2 1-3l2-2v2c0 2-1 3-1 5v6 1c1 1 2 3 2 4l-1 2c0 1 1 1 2 2s1 2 2 3c-1-1-3-2-4-3-3-3-4-7-4-11 0-2 0-3 1-5z" class="X"></path><path d="M291 575v-1c1-1 1-2 1-3l2-2v2c0 2-1 3-1 5v3c0 1-1 1-1 2-1 0-1 0-2-1v-2c1-1 1-2 1-2v-1z" class="B"></path><path d="M287 564v-1h1c2-4 4-6 7-9 0 3-3 5-4 8v2l-3 7s-1 1-1 2c-1 0 0 0-1 1s-1 6-1 7v-3c-1-1-1-1-1-2s0-2-1-2c0-1 0-1-1-2 1-2 2-3 3-5l2-3z" class="J"></path><path d="M285 567l2-3c0 2-1 4-1 6-1 2-1 5-1 8-1-1-1-1-1-2s0-2-1-2c0-1 0-1-1-2 1-2 2-3 3-5z" class="D"></path><path d="M306 557h0c-2-2-4-3-5-5v-1c1-1 2-1 3-2l1 2v1c1 1 2 1 3 2l2 2 6 4c1 1 2 1 2 2-1 0-1 0-2-1-1 0-1 0-2 1-2 0-3-1-5-1h0l-1 2h-1l-2-2-7 3 1-1 1-1 2-2v-1c1-1 2-1 4-2z" class="b"></path><path d="M310 556h-2c-1-1-3-1-4-2v-1l1-1c1 1 2 1 3 2l2 2z" class="W"></path><path d="M308 557h0c1 0 1 0 2 1l-1 1c-2 0-3 0-4 2l-7 3 1-1 1-1 2-2v-1c1-1 2-1 4-2h2z" class="H"></path><path d="M308 557h0c1 0 1 0 2 1l-1 1c-1-1-2 0-3-1l2-1z" class="O"></path><defs><linearGradient id="Aa" x1="286.494" y1="555.175" x2="296.43" y2="557.102" xlink:href="#B"><stop offset="0" stop-color="#1f201e"></stop><stop offset="1" stop-color="#403f41"></stop></linearGradient></defs><path fill="url(#Aa)" d="M298 541c2-1 3-1 4-2v2 5l-2 2-5 6c-3 3-5 5-7 9h-1v1l-2 3-1-1c2-4 3-7 4-11l1 1 1-1 2-4v-2h1v-2s1 0 1-1c1-2 3-3 4-5z"></path><path d="M298 541c2-1 3-1 4-2v2c-1 1 0 4-1 5h-2c-1-2 0-3-1-5z" class="e"></path><path d="M292 551c1-1 1-1 1-2 1-1 2-1 2-2l3-3v1c-1 4-4 7-7 10h-1l2-4z" class="P"></path><path d="M290 593h0c0-2 0-2-1-3l1-1v1c1 0 0 0 1-1v1c1 2 2 4 4 5h0 2c1 1 3 0 4 0 2 1 4 1 6 0 3-1 5-3 6-6 0 2 0 3-1 5s-4 5-4 8v2c-1-1-2-1-2-1h-3l-2-1c-2-1-4-2-6-2-1-1-2-2-2-3-1 0-1-1-2-1v-1l-1-2z" class="T"></path><path d="M299 599c1-1 1-2 2-2 1 1 0 2 1 3-2 0-2 0-3-1z" class="G"></path><path d="M291 595c2 1 3 1 5 2h0c1 1 0 1 1 1s1 0 2 1h0c1 1 1 1 3 1 0 1 0 1 1 1v2l-2-1c-2-1-4-2-6-2-1-1-2-2-2-3-1 0-1-1-2-1v-1z" class="F"></path><path d="M314 562c1-1 1-1 2-1 1 1 1 1 2 1h1l1-1c1 1 2 1 3 2 1 0 2 1 2 2 1 2 3 4 4 5-1 1-1 2-1 3h0-1-1c0 1 0 1-1 1 1 2 1 3 1 5-2-4-4-7-8-11v3c-1-1-3-3-5-4-4-1-6 0-9 1h-1c-1 0-3 3-4 3-2 3-2 5-2 8 0 1 1 2 1 2v1c-1 0-1 0-1 1h0c0-1-1-2-1-3l-3 3v-1-6c0-2 1-3 1-5 1-3 3-5 4-7l7-3 2 2h1l1-2h0c2 0 3 1 5 1z" class="W"></path><path d="M314 562c1-1 1-1 2-1 1 1 1 1 2 1h1l-1 1 1 1c-2 0-3-1-5-2h0z" class="D"></path><path d="M293 582c0-2 0-4 1-6 1 1 1 3 2 4l-3 3v-1z" class="I"></path><path d="M308 563l1-2h0c2 0 3 1 5 1h0v1 1h-2c-1-1-3-1-4-1z" class="P"></path><path d="M325 574c0-3-3-5-3-7l1-1v1c1 1 1 1 1 2 1 0 1 0 1 1 1 0 1 1 1 1 1 0 1 1 2 1v1h-1-1c0 1 0 1-1 1z" class="R"></path><path d="M325 565c1 2 3 4 4 5-1 1-1 2-1 3h0v-1c-1 0-1-1-2-1 0 0 0-1-1-1 0-1 0-1-1-1 0-1 0-1-1-2l2-2z" class="U"></path><path d="M319 562l1-1c1 1 2 1 3 2 1 0 2 1 2 2l-2 2v-1c-1-1-3-1-4-2l-1-1 1-1z" class="e"></path><path d="M299 571c1-3 2-4 5-5 2-1 6-2 8-1l6 3v3c-1-1-3-3-5-4-4-1-6 0-9 1h-1c-1 0-3 3-4 3z" class="V"></path><path d="M307 535c0-1 1-2 2-2l3-1h0c1-1 1-1 2-1 4 0 7 0 9-2v2 8l-1-1c0-1 0-1-1-1-1 2 0 6-1 8 1 3 2 5 2 7h1c1 2 2 3 3 4v1h-3v1h-1l-1-3h-1c-1 1-1 1-2 1s-3-2-4-3l-2 2 4 3v2l-6-4-2-2v-1-3l-2-1h-1l-1-2c-1-2-1-3-1-6-1 2 0 3-1 5v-5-2c1-2 3-3 5-4z" class="J"></path><path d="M312 554l-1 1s-1 0-1-1c-1 0-1-1-1-2h1l2 2z" class="I"></path><path d="M316 537l1-2h1v4h-1c-1 0-1-1-1-2z" class="l"></path><path d="M310 552l-2-3v-2h1l1 3c1 1 2 3 4 3l-2 2v-1l-2-2z" class="Y"></path><path d="M303 541l1-1h1 0c0 3 0 6 1 9h-1l-1-2c-1-2-1-3-1-6z" class="l"></path><path d="M315 539l-1-1c-1-1-2 0-3 0h-1v-1c-1 1-2 2-3 2v-1-1c1-1 3-2 5-2 1 0 2 0 3 1 0 0 0 1 1 1 0 1 0 2 1 2h1v1c0 1-1 1-1 2l-2-3z" class="G"></path><path d="M310 545c-1-1-1-3-1-4 0 0 1-2 2-2 1-1 3 0 4 0l2 3v1h-2c0 1-1 1-2 2v2h-2l-1-2h0z" class="H"></path><path d="M310 545v-2l3-3h0c1 0 1 1 1 1 0 1-1 1-2 2l-1 2c0 1 0 1-1 0h0z" class="N"></path><path d="M311 547h2c1 2 2 3 3 4h4l2 1h1c1 2 2 3 3 4v1h-3v1h-1l-1-3h-1c-1 1-1 1-2 1s-3-2-4-3c-2 0-3-2-4-3h3c-1-1-2-2-2-3z" class="B"></path><path d="M310 550h3c2 2 6 3 8 5h-1c-1 1-1 1-2 1s-3-2-4-3c-2 0-3-2-4-3z" class="S"></path><path d="M307 535c0-1 1-2 2-2l3-1h0c1-1 1-1 2-1 4 0 7 0 9-2v2 8l-1-1c0-1 0-1-1-1-1 2 0 6-1 8 1 3 2 5 2 7l-2-1h-4c-1-1-2-2-3-4v-2c1-1 2-1 2-2h2v-1c0-1 1-1 1-2s0-2 1-2c1-1 1-2 1-3-2-1-2-1-3-1l-4-2-6 3z" class="Z"></path><path d="M318 545v-1c1-1 1-2 2-3v4c1 3 2 5 2 7l-2-1h-4l1-5 1-1z" class="U"></path><path d="M318 545c1 0 1 0 2 2h-2l-1-1 1-1z" class="Q"></path><path d="M307 535c0-1 1-2 2-2l3-1h0c1-1 1-1 2-1 4 0 7 0 9-2v2 8l-1-1c0-1 0-1-1-1-1 2 0 6-1 8v-4-8c-1 0-1 0-2-1h0-5l-6 3z" class="L"></path><path d="M314 553c1 1 3 3 4 3s1 0 2-1h1l1 3h1c2 0 4 2 5 4 2 2 3 5 4 8 2 4 1 8 2 12v1 12c0 5 0 10-1 14v4c0 2-1 3 0 4l-1 2v1l-1-1-2-2v-1c0 1-1 1-2 1l-1-2-1 1v-2l-1 1-1-1c0-1 1-2 1-3-1-1-1-3-1-4v-1l-1-1-1 1c0-1 0-1-1-2l-4 1h0c1-1 1-2 2-3l-1-1h-2-1c0-1 0-1 1-1v-1h0l3-3c1-1 1-2 2-3v-1c2-4 2-9 2-14-1-1-1-2-1-3v-1l-3-3v-3c4 4 6 7 8 11 0-2 0-3-1-5 1 0 1 0 1-1h1 1 0c0-1 0-2 1-3-1-1-3-3-4-5 0-1-1-2-2-2-1-1-2-1-3-2l-1 1h-1c0-1-1-1-2-2v-2l-4-3 2-2z" class="D"></path><path d="M317 601c1-1 2-1 3-1l1 1h0 0c2-1 4-5 5-7 0 1 0 1 1 1-1 4-4 7-8 9h-1v-2l-1-1z" class="J"></path><path d="M324 602c2-2 3-3 4-5 1-1 1-2 2-4l1 1h0c1 1 1 1 0 2s-2 4-3 6c-1 1-2 3-3 4l-2 1v-1l-1-1-1 1c0-1 0-1-1-2 2 0 3-1 4-2z" class="R"></path><path d="M320 604c2 0 3-1 4-2 1 1 1 1 1 2l-1 1 1 1-2 1v-1l-1-1-1 1c0-1 0-1-1-2z" class="S"></path><path d="M322 605l1-1 1 1 1 1-2 1v-1l-1-1z" class="Q"></path><path d="M325 574c1 0 1 0 1-1h1 1c-1 2 1 5 2 7h0v7h0v3l-3 5c-1 0-1 0-1-1 0-2 1-4 1-5 0-3 0-7-1-10 0-2 0-3-1-5z" class="T"></path><path d="M330 590l-2-2v-3c0-1 0-2 1-2l1 1v3h0v3z" class="l"></path><path d="M325 574c1 0 1 0 1-1h1 1c-1 2 1 5 2 7h0l-1 2h-1v-2h-1v8 1c0-3 0-7-1-10 0-2 0-3-1-5z" class="I"></path><path d="M318 568c4 4 6 7 8 11 1 3 1 7 1 10 0 1-1 3-1 5-1 2-3 6-5 7h0 0l-1-1c-1 0-2 0-3 1h-2-1c0-1 0-1 1-1v-1h0l3-3c1-1 1-2 2-3v-1c2-4 2-9 2-14-1-1-1-2-1-3v-1l-3-3v-3z" class="e"></path><path d="M321 574c2 4 3 10 2 14-1 6-4 10-8 13h-1c0-1 0-1 1-1v-1h0l3-3c1-1 1-2 2-3v-1c2-4 2-9 2-14-1-1-1-2-1-3v-1z" class="F"></path><path d="M314 553c1 1 3 3 4 3s1 0 2-1h1l1 3h1c2 0 4 2 5 4 2 2 3 5 4 8 2 4 1 8 2 12v1 12c0 5 0 10-1 14v4c0 2-1 3 0 4l-1 2v1l-1-1-2-2v-1c0 1-1 1-2 1l-1-2-1 1v-2l-1 1-1-1c0-1 1-2 1-3-1-1-1-3-1-4l2-1c1-1 2-3 3-4 1-2 2-5 3-6s1-1 0-2h0l-1-1c2-5 1-12 1-18-1-1-1-3-2-5-1-1-3-3-4-5 0-1-1-2-2-2-1-1-2-1-3-2l-1 1h-1c0-1-1-1-2-2v-2l-4-3 2-2z" class="i"></path><path d="M331 603v4c-1 2 0 3-2 5h2s1-1 1-2v-2-4c1-3 1-6 2-9 0 5 0 10-1 14v4c0 2-1 3 0 4l-1 2v1l-1-1-2-2v-1c0 1-1 1-2 1l-1-2c1-1 2-2 3-4s1-5 2-8z" class="T"></path><path d="M333 609v4c0 2-1 3 0 4l-1 2v1l-1-1-2-2v-1c1 0 1 0 1-1s-1-1-1-2l1-1 1 1c1-1 2-3 2-4z" class="G"></path><path d="M328 602c1-2 2-5 3-6v7c-1 3-1 6-2 8s-2 3-3 4l-1 1v-2l-1 1-1-1c0-1 1-2 1-3-1-1-1-3-1-4l2-1c1-1 2-3 3-4z" class="X"></path><path d="M328 606c1 2-1 4 1 5-1 2-2 3-3 4l-1 1v-2l3-8z" class="Q"></path><path d="M328 602c1-2 2-5 3-6v7c-1 3-1 6-2 8-2-1 0-3-1-5l1-1v-1c0-1 0-1-1-2z" class="D"></path><path d="M314 553c1 1 3 3 4 3s1 0 2-1h1l1 3h1c2 0 4 2 5 4 2 2 3 5 4 8v4c-1-1 0-3-1-4 0 0 0-1-1-1-1-2-2-4-3-5l-2-2s-1-1-1-2h-1c0 1 0 1 1 2h0c1 1 1 1 1 2 2 3 5 5 6 8v3c-1-1-1-3-2-5-1-1-3-3-4-5 0-1-1-2-2-2-1-1-2-1-3-2l-1 1h-1c0-1-1-1-2-2v-2l-4-3 2-2z" class="g"></path><path d="M316 558l2 1 1-2c1 1 1 2 2 3 2 1 2 1 2 3-1-1-2-1-3-2l-1 1h-1c0-1-1-1-2-2v-2z" class="T"></path><path d="M304 568c3-1 5-2 9-1 2 1 4 3 5 4l3 3v1c0 1 0 2 1 3 0 5 0 10-2 14v1c-1 1-1 2-2 3l-3 3h0v1c-1 0-1 0-1 1-1 1-3 2-5 3h-1 0v-2c0-3 3-6 4-8s1-3 1-5c-1 3-3 5-6 6-2 1-4 1-6 0l-3-1c-1-1-1-2-2-3s-2-1-2-2l1-2c0-1-1-3-2-4l3-3c0 1 1 2 1 3h0c0-1 0-1 1-1v-1s-1-1-1-2c0-3 0-5 2-8 1 0 3-3 4-3h1z" class="i"></path><path d="M307 576h-1 2 1 0c1 0 3 1 3 2 1 0 2 2 3 2v3 5c0 3-3 8-5 11 1-1 3-2 3-4 1-1 2-2 2-3s1-3 1-4c1-2-1-9 1-10 1 3 0 8 0 11l-1 2-2 4c-1 2-4 6-6 7 0-3 3-6 4-8s1-3 1-5l1-1v-2c1-3 0-5-2-7v-1c-2-1-3-1-5-1v-1z" class="g"></path><path d="M304 568c3-1 5-2 9-1 2 1 4 3 5 4l3 3v1c0 1 0 2 1 3 0 5 0 10-2 14v1c-1 1-1 2-2 3 0-1 1-3 1-3 0-1 1-1 1-2h0v-1l1-1c1-1 0-3 0-4v-2-2h0v-2c-1 0-1 0-1-1h0c0-1-1-1-1-2-2-1-3-3-5-3-1 0-2 0-3-1h-2c-2 0-4 0-5 2-2 1-2 3-2 5 1 1 2 1 2 2l-1 1c0 1 0 2-1 2l-1-1c-2-2-3-4-3-7 1-3 3-6 6-8z" class="T"></path><defs><linearGradient id="Ab" x1="306.638" y1="593.989" x2="302.949" y2="584.804" xlink:href="#B"><stop offset="0" stop-color="#201f1f"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#Ab)" d="M299 571c1 0 3-3 4-3h1c-3 2-5 5-6 8 0 3 1 5 3 7l1 1c1 0 1-1 1-2l1-1 2-1h0l2-2-1-1v-1 1c2 0 3 0 5 1v1c2 2 3 4 2 7v2l-1 1c-1 3-3 5-6 6-2 1-4 1-6 0l-3-1c-1-1-1-2-2-3s-2-1-2-2l1-2c0-1-1-3-2-4l3-3c0 1 1 2 1 3h0c0-1 0-1 1-1v-1s-1-1-1-2c0-3 0-5 2-8z"></path><path d="M307 577c2 0 3 0 5 1v1l-1 1 1 1v5-1c0-2-1-3-1-4-1-2-3-3-4-4z" class="C"></path><path d="M307 589l3-1v1c-1 2-2 3-4 3-2 1-4 1-5 0 2 0 4-1 6-2v-1z" class="U"></path><path d="M311 581c0 1 1 2 1 4v1c-1 1-1 3-2 3v-1l-3 1c1-1 2-3 2-4 2-1 2-2 2-4z" class="M"></path><path d="M307 576v1c1 1 3 2 4 4 0 2 0 3-2 4h-2v-1h-1l1-1c0-2 0-2-1-3h0l2-2-1-1v-1z" class="R"></path><path d="M301 587h0l3 1c1 0 2 0 2 1l1 1c-2 1-4 2-6 2s-3-1-4-2c2 0 2 0 3-1v-1l1 1c-1-1-1-1 0-2z" class="Q"></path><g class="R"><path d="M301 587h0l1 1h1c0 2 0 2-1 2s-1-1-1-1c-1-1-1-1 0-2z"></path><path d="M293 583l3-3c0 1 1 2 1 3h0l4 4c-1 1-1 1 0 2l-1-1v1c-1 1-1 1-3 1l-2-3c0-1-1-3-2-4z"></path></g><path d="M297 583h0l4 4c-1 1-1 1 0 2l-1-1v1l-1-1c-1-1-3-3-3-4l1-1z" class="J"></path><path d="M299 571c1 0 3-3 4-3h1c-3 2-5 5-6 8 0 3 1 5 3 7l1 1c1 0 1-1 1-2l1-1 2-1c1 1 1 1 1 3l-1 1h1v1h2c0 1-1 3-2 4v1l-1-1c0-1-1-1-2-1l-3-1h0l-4-4c0-1 0-1 1-1v-1s-1-1-1-2c0-3 0-5 2-8z" class="H"></path><path d="M306 580c1 1 1 1 1 3l-1 1v-1l-1 1v-1h-1c0 1 0 2-1 2l-1-1c1 0 1-1 1-2l1-1 2-1z" class="D"></path><path d="M305 584l1-1v1h1v1h2c0 1-1 3-2 4v1l-1-1c0-1-1-1-2-1v-2l1-2z" class="d"></path><path d="M471 81c2-3 5-7 6-11 2-5 2-11 3-17 6 5 9 12 15 17 3 4 7 9 11 13h3c9 10 20 18 32 25 4 2 8 4 12 7l50 17h-1c-3 0-6 0-8-1h-27l-157 1h-37c-5 0-11-1-15 1 2 2 4 3 6 5h-1c-4-1-6-2-8-5l-1-1s-1 0-1-1l-18-11c-5 1-9 5-13 7l-4 1-7 6h-1c-2 2-7 5-10 5l-16 5h-1c-1-1-4-1-6-2h0c-1-1-1-2-1-3h-4c-11 3-22 7-31 13-1 1-1 1-2 1l-4 2-2-1v-1c-1 1-1 2-2 2l-1 1c-1 0-2 0-3 1l-1 1c-1-1 0-2-1-2l-1 1v1c-1 1-1 1-2 0-2 1-3 2-4 3l-4 4v1l2 1c-1 1-2 1-3 1-2 0-3-1-5 0l-1 1v-4l-2-1v-1c-1 0-1 0-2 1 0-2-1-3-1-4-1-1-2-2-3-4-1 0-2-1-2-1l-2-2h0c-1-1-1-1-1-2l1-1-3-2c-1 0-2 0-3 1h0-1l-3-4c-2-1-4-3-6-4h-1c-7-4-17-3-24-3h-22-19-1-1v-1 1c0-1-1-1-1-1-1 0-2-2-2-3s0-1-1-1v-1h9 21 0-61-3-9 1l24-8c8-3 16-6 24-10 14-6 29-16 40-28 2-1 4-3 5-5 4-4 7-8 10-12l-1 3h-1l1 1c1-1 1-2 2-3h2c0 2-1 2-2 4h0 0l3-3h1c1 0 2-1 3-2h0c0 1-1 1-1 2-1 1-1 1-1 3l6-9c2 2 1 5 1 7 0 1 1 2 2 3 0 5 4 11 8 14l1 1 5 5s2 1 2 2l2 1-1 1-1 1c2 1 3 1 4 2v1h0c0 1 1 1 2 1v1c0 1 0 2 1 2s1-1 2 0h1l1-2h0 1c1 1 1 1 3 2 1 0 3 2 5 2v-2l12 3 8 1h5 0c-4-1-8-3-11-4-6-2-11-2-17-5 4 1 8 2 12 2l4 1c0-1 0-1-1-2h3c1 0 3 0 4 1h5 12 15 0 88 35c12 0 24 0 37-3 12-2 23-6 33-14 2-3 5-5 7-9h1c0 2-1 2-1 3v1l1-1z" class="g"></path><path d="M151 117c1 2 1 4 1 6l-3-3 1-1h1v-2z" class="T"></path><path d="M168 126h0 3v1l-1 1h-9l7-2z" class="R"></path><path d="M389 126l3-1v2l-2 1h-5c1-1 2-2 4-2z" class="J"></path><path d="M313 120h1c1 1 2 1 2 2 0 2-1 2-2 2 0 1-1 1-1 1l-1-1c0-2 0-3 1-4zm-33 6c3 0 5 2 8 2h-1c-2 1-7 0-9 0-1-1-1-1-2-1 1-1 3-1 4-1z" class="Y"></path><path d="M148 112c2 0 3 0 4 1v1 1l-1-1-2 2c1 1 1 1 2 1h0v2h-1l-1 1c-1-2-1-3-1-5v-1-2z" class="G"></path><path d="M470 78h1c0 2-1 2-1 3-1 1-1 1-1 2-2 2-3 4-5 5 0-1 0-1 1-2l1-1 1-1c-1 1-2 3-4 3 2-3 5-5 7-9z" class="Y"></path><path d="M147 116l1-1h0c0 2 0 3 1 5l3 3c1 1 2 2 2 3h0c-2-1-6 0-8 0 1-1 4-1 6-1h0l-1-1h-1l1-1h0c-1-1-2-1-3-1h0v-3c-1-1-2-1-2-2l1-1z" class="C"></path><path d="M508 98v-1h1l7 7-1 1-1-2-2 1c-2 0-3-1-5-3 1 0 1 0 2-1l-1-2z" class="O"></path><path d="M509 100c2 0 4 2 5 3l-2 1c-2 0-3-1-5-3 1 0 1 0 2-1z" class="R"></path><path d="M516 104l7 7 4 4c-1 0-1 0-1 1h0c-1 0-1-1-1-1l-3-2-3-3h0c-1 0-1 0-2-1 0-1-1-3-2-4l1-1z" class="e"></path><path d="M368 121l11 6c2 0 3 0 4 2h-5c-1-1-1-2-2-2h0l-1-1v1l-1-1-3-2c-2-1-2 0-3 1l-1-1v-1c1 0 1-1 1-1v-1z" class="T"></path><path d="M134 115c0 1-1 2-2 4-4 2-9 6-15 5l17-9z" class="M"></path><path d="M145 106l13-13-1 4-3 3h0c0 1-1 2-2 3h0c-1 0-3 2-4 3h-3z" class="J"></path><path d="M145 106h3c-1 1-2 2-3 4l-6 4-7 5c1-2 2-3 2-4 4-2 8-6 11-9z" class="E"></path><path d="M158 93l4-4c4-5 7-10 11-14 0 1 0 2-1 4-1 1-3 3-3 4-4 4-7 10-12 14l1-4z" class="V"></path><path d="M169 83v1l-1 2-3 4c0 1-1 2-1 3h-1c-1 1-1 1-1 2v1h-1c-1 0-3 2-3 3l-1 1h-3 0l3-3c5-4 8-10 12-14z" class="B"></path><path d="M146 126c2 0 6-1 8 0-1 1-3 0-4 1h-2l-1 1h-10c-2 0-5 0-7-1h0-2c-1-1-2 0-2 0-3 0-5 0-7-1h9 18z" class="i"></path><path d="M483 65c0-1 0-1 1-2 1 0 1 0 2 1 2 2 2 4 3 5 1 2 2 3 3 4 2 3 4 5 6 7 2 3 3 6 5 8s4 3 4 5c1 1 1 0 1 1s1 1 1 2l-6-6-4-5-4-5-1-1-9-13c-1-1-1-1-2-1z" class="G"></path><path d="M552 126h1c1 0 2 0 3 1h0c-1 1-4-1-5 1h-4c-3 1-7 0-10 0l-16 1h-11-2-2-2c-1 0-2 0-3-1h-6s-1 0-1 1h-3-1-2c2-1 4 0 6-2h2l1 1h27c1 0 1-1 2-2h18c2 0 5 1 8 0z" class="i"></path><path d="M307 115c2 0 2 0 4 1-8 3-16 7-23 12-3 0-5-2-8-2-2 0-3-1-5-2h1 3 1 5c3 0 7-1 9-3 1 0 1 0 2-1h1l3-2 5-2h2v-1z" class="N"></path><path d="M506 83h3c9 10 20 18 32 25 4 2 8 4 12 7h-2c-2 0-4-2-6-3-15-7-28-17-39-29z" class="c"></path><path d="M185 123h8v1 1h5s0 1 1 1h3 1c5 0 11-1 16 0h4 0 2c-1 1-2 1-3 1-1-1-2-1-3-1h-3c-1 1-2 2-4 2l-1-1c1 0 1 0 2-1-1 0-2 1-3 1h-10 0c1 1 2 1 2 1h1 2 3 1 1v-1l1 1c-1 1-3 1-4 1-3 0-6-1-9-1h-15-8c-2-1-3 0-5 0l1-1h1l1-1h0 0c2-2 9-3 12-3z" class="T"></path><defs><linearGradient id="Ac" x1="192.88" y1="122.926" x2="178.592" y2="125.496" xlink:href="#B"><stop offset="0" stop-color="#2a2928"></stop><stop offset="1" stop-color="#454343"></stop></linearGradient></defs><path fill="url(#Ac)" d="M185 123h8v1 1l-20 1h0c2-2 9-3 12-3z"></path><path d="M277 116c1-1 0-2 2-3l1 1 1 1h1c2 0 5 0 7 1h-1 1 0 1c1 0 1 1 2 1h1 0c1 0 1-1 2-1s1 2 2 1c0 1 0 1 1 1h2l-3 2h-1c-1 1-1 1-2 1-2 2-6 3-9 3h-5-1-3-1c-1-1-1-2-1-3h2l3-1c1 0 3 0 4-1-1 0-1-1-2-1h0v-3c-1 0-2 0-2 1h-2 0z" class="E"></path><path d="M293 117c1 0 1-1 2-1s1 2 2 1c0 1 0 1 1 1h2l-3 2h-1c0-1 0-1-1-1l-2-2z" class="P"></path><path d="M285 117l3 1h2 1 1l-2 1h-5-2c-1 0-1-1-2-1h0 0 4v-1z" class="F"></path><path d="M277 116c1-1 0-2 2-3l1 1 1 1h1s1 1 2 1c1 1 0 1 1 1v1h-4 0v-3c-1 0-2 0-2 1h-2 0z" class="P"></path><path d="M283 119h2 5l-3 3c-1 1-2 1-2 2h-5-1-3-1c-1-1-1-2-1-3h2l3-1c1 0 3 0 4-1z" class="U"></path><path d="M280 122c1 0 1-1 2-1h1c0 1 0 1-1 2 0 0-1 0-2 1h-1l1-2z" class="Q"></path><path d="M274 121h2v1h0 4l-1 2h-3-1c-1-1-1-2-1-3z" class="K"></path><path d="M298 112h20c-2 2-4 3-7 4-2-1-2-1-4-1v1h-2l-5 2h-2c-1 0-1 0-1-1-1 1-1-1-2-1s-1 1-2 1h0-1c-1 0-1-1-2-1h-1 0-1 1c-2-1-5-1-7-1h-1l-1-1-1-1v-1h6 4 9z" class="j"></path><path d="M280 114c4 1 10 1 14 1h13v1h-2l-5 2h-2c-1 0-1 0-1-1-1 1-1-1-2-1s-1 1-2 1h0-1c-1 0-1-1-2-1h-1 0-1 1c-2-1-5-1-7-1h-1l-1-1z" class="D"></path><path d="M297 117c1-1 1-1 2-1 2-1 4-1 6-1v1l-5 2h-2c-1 0-1 0-1-1z" class="W"></path><path d="M185 120h1c2 1 4 1 7 1l2-3c1 0 2 0 3 1h4c1 0 4 0 5 1h4v1c-2 2-7 1-8 5h-1-3c-1 0-1-1-1-1h-5v-1-1h-8c-3 0-10 1-12 3h0 0l-1 1h-1v-1h-3 0c-3-2-4-1-7-1 2-1 4-1 6-2l17-3h1 0z" class="G"></path><path d="M198 120l2-1v1c1 0 1 0 1 1h2c1-1 2-1 4-1h4v1c-2 2-7 1-8 5h-1-3c-1 0-1-1-1-1h-5v-1-1h-8s1 0 1-1h9c1-1 2-1 3-2z" class="F"></path><path d="M198 120l2-1v1c1 0 1 0 1 1h2c1-1 2-1 4-1-1 1-3 1-4 2s-1 1-3 1v-1l-2-2z" class="T"></path><path d="M193 123h3 5l1 1h0v2h-3c-1 0-1-1-1-1h-5v-1-1z" class="B"></path><path d="M277 116h0 2c0-1 1-1 2-1v3h0c1 0 1 1 2 1-1 1-3 1-4 1l-3 1h-2c0 1 0 2 1 3 2 1 3 2 5 2-1 0-3 0-4 1-2-1-4-1-6 0h0c-1 0-3 0-5-1h1c-3-2-5-3-9-4h-4v-3h-3 3c3-1 5-1 8-1h3l1-1h2l3 1h0c3 0 4 0 7-2z" class="F"></path><path d="M261 118h3l1-1h2l3 1h-2v1 1c-2 0-3 0-4-1s-2-1-3-1z" class="S"></path><path d="M256 121l-1-1v-1c2 0 3 0 5 1h4-2v1h-1-5z" class="T"></path><path d="M264 120c1 1 2 1 3 2l3 3v2h0c-1 0-3 0-5-1h1c-3-2-5-3-9-4l-1-1h5 1v-1h2z" class="G"></path><path d="M264 120c1 1 2 1 3 2-2 0-4 0-5-1v-1h2z" class="l"></path><path d="M277 116h0 2c0-1 1-1 2-1v3h0c1 0 1 1 2 1-1 1-3 1-4 1l-3 1h-2c-1 1-1 1-2 1l-2-2h-2v-1-1h2 0c3 0 4 0 7-2z" class="K"></path><path d="M270 118c2 1 9 0 11 0 1 0 1 1 2 1-1 1-3 1-4 1-1-1-2-1-3-1-2 0-5 0-6 1h-2v-1-1h2 0z" class="G"></path><path d="M270 120c1-1 4-1 6-1 1 0 2 0 3 1l-3 1h-2c-1 1-1 1-2 1l-2-2z" class="T"></path><path d="M310 131c4-4 10-7 15-11 3-1 6-4 9-4 8 4 18 10 24 17 2 2 4 3 6 5h-1c-4-1-6-2-8-5l-1-1s-1 0-1-1l-18-11c-5 1-9 5-13 7l-4 1-7 6h-1c-2 2-7 5-10 5 4-3 7-5 10-8z" class="N"></path><path d="M318 128c3-2 7-5 10-7 1 0 3-1 4-2 1 0 2 0 3 1-5 1-9 5-13 7l-4 1z" class="P"></path><path d="M250 119h3v3h4c4 1 6 2 9 4h-1c2 1 4 1 5 1h0c2-1 4-1 6 0 1 0 1 0 2 1-1 0-2-1-3-1-5 0-9 0-14 1h1 3c2 1 5 0 7 1h0c-5 0-10-1-14-1h-13c-5 0-10 1-15 0v-1c-1-1-2-1-3-1 1-1 2-2 4-2 0-1 1-1 2-1h3c1 0 1 0 2-1h1c1 0 1 0 2-1 2-1 5-2 7-2h2z" class="T"></path><path d="M236 123c1 0 1 0 2-1v2c-1 0-3 1-4 2h0l-1-1-1-1c2-1 3-1 4-1z" class="Y"></path><path d="M250 119h3v3h4c4 1 6 2 9 4h-1-12-19 0c1-1 3-2 4-2v-2h1c1 0 1 0 2-1 2-1 5-2 7-2h2z" class="f"></path><path d="M250 119h3v3h-8l3-3h2z" class="J"></path><path d="M239 122c1 0 1 0 2-1 2-1 5-2 7-2l-3 3-7 2v-2h1z" class="F"></path><path d="M146 110c1-1 1-1 3-1-1 1-1 2-1 3v2 1h0l-1 1-1 1c0 1 1 1 2 2v3h0c1 0 2 0 3 1h0l-1 1h1l1 1h0c-2 0-5 0-6 1h-18-9c-2 0-4 0-7 1l5-3c6 1 11-3 15-5l7-5 6-4h1z" class="m"></path><path d="M136 119c0 1 0 1 1 2h2 0v2c1 1 4 0 5 0l-7 1c-1-2-1-3-1-5h0z" class="O"></path><path d="M137 124c-1 0-2 0-3-1h-3-1c1-2 4-3 6-4 0 2 0 3 1 5z" class="B"></path><path d="M146 110c1-1 1-1 3-1-1 1-1 2-1 3v2 1h0l-1 1c-1 0-2-1-3-2l-2 3-2 2v1h-2l1 1h-2c-1-1-1-1-1-2l3-2c0-1 1-2 2-3h-2l6-4h1z" class="H"></path><path d="M144 114c0-1 1-1 2-2 1 1 1 1 2 1v1 1h0l-1 1c-1 0-2-1-3-2z" class="J"></path><path d="M145 110h1c-1 2-3 3-4 5-1 0-2 1-3 2 0-1 1-2 2-3h-2l6-4z" class="N"></path><path d="M144 114c1 1 2 2 3 2l-1 1c0 1 1 1 2 2v3h0c1 0 2 0 3 1h0l-1 1-4-1h-2c-1 0-4 1-5 0v-2h0l-1-1h2v-1l2-2 2-3z" class="K"></path><path d="M139 121l-1-1h2v-1c2 1 3-1 4 2h-5 0z" class="E"></path><path d="M139 121h5c0 1 0 1-1 2h3-2c-1 0-4 1-5 0v-2zm208-9v-1c3-1 9 0 12 0 2 1 4 0 6 1l25-1c12 0 24 0 36-1 2 1 3 1 5 1l1-1h1l1 1h0c1 0 2 0 4-1h0c-1 1-2 1-3 1v1c-1 0-2 1-2 1l-1 1h0c2 2 4 2 7 2-3 0-6 1-8 1l-1 1h-1c-1-1-6-1-7-2h-4c-2 0-5 0-8 1-1 0-2 1-3 1-2 0-4 1-6 1-1-2-6-2-8-3l-3-3h0c-2-2-32-1-36-1h-5c-1-1-1 0-2 0z" class="V"></path><path d="M434 111h0c1 0 2 0 4-1h0c-1 1-2 1-3 1v1c-1 0-2 1-2 1l-1 1c-2 0-7 1-10 1-1 0-3 0-4-2l15-3 1 1z" class="m"></path><path d="M390 113c1-1 2-1 3 0 1 0 1 0 2 1 0 0 0-1 1-1h6 16c1 2 3 2 4 2 3 0 8-1 10-1h0c2 2 4 2 7 2-3 0-6 1-8 1l-1 1h-1c-1-1-6-1-7-2h-4c-2 0-5 0-8 1-1 0-2 1-3 1-2 0-4 1-6 1-1-2-6-2-8-3l-3-3z" class="f"></path><path d="M422 115c3 0 8-1 10-1h0c2 2 4 2 7 2-3 0-6 1-8 1l-1 1h-1c-1-1-6-1-7-2h-4c0-1-7 0-8 0 2-1 3-1 5-1h7z" class="c"></path><path d="M390 113c1-1 2-1 3 0 1 0 1 0 2 1 4 3 10 2 15 2 1 0 8-1 8 0-2 0-5 0-8 1-1 0-2 1-3 1-2 0-4 1-6 1-1-2-6-2-8-3l-3-3z" class="R"></path><defs><linearGradient id="Ad" x1="536.297" y1="132.125" x2="519.104" y2="117.195" xlink:href="#B"><stop offset="0" stop-color="#0f0d0c"></stop><stop offset="1" stop-color="#383839"></stop></linearGradient></defs><path fill="url(#Ad)" d="M517 109c1 1 1 1 2 1h0l3 3 3 2s0 1 1 1h0c0-1 0-1 1-1l-4-4h2s1 1 2 1c1 1 2 2 4 3l11 6c3 2 7 3 10 5-3 1-6 0-8 0h-18-4-12c1-1 2-2 3-2h0c1-2 3-4 3-5l1-3v-1-6z"></path><path d="M516 119h2c0 1 0 2 1 3 1 0 1 0 1 1h1 0c-3 0-5 1-8 1 1-2 3-4 3-5z" class="M"></path><defs><linearGradient id="Ae" x1="533.497" y1="121.465" x2="526.644" y2="120.554" xlink:href="#B"><stop offset="0" stop-color="#353435"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#Ae)" d="M526 116h0l3 2c2 1 5 3 6 5h-13 3c0-1 1-1 1-2 1-1 0-4 0-5z"></path><path d="M523 111h2s1 1 2 1c1 1 2 2 4 3l11 6c3 2 7 3 10 5-3 1-6 0-8 0h-18-4c2-1 5 0 8 0h17l-16-9c-1 1-1 1-2 1l-3-2c0-1 0-1 1-1l-4-4z" class="d"></path><path d="M526 116c0-1 0-1 1-1l4 2c-1 1-1 1-2 1l-3-2z" class="a"></path><path d="M517 109c1 1 1 1 2 1h0l3 3 3 2s0 1 1 1c0 1 1 4 0 5 0 1-1 1-1 2h-3-1 0-1c0-1 0-1-1-1-1-1-1-2-1-3h-2l1-3v-1-6z" class="D"></path><path d="M518 119c1 0 4 0 6 1-1 0-1 0-1 1h-2l1 1v1h-1-1c0-1 0-1-1-1-1-1-1-2-1-3z" class="I"></path><path d="M517 109c1 1 1 1 2 1h0l3 3 3 2c-1 1-2 1-3 1l2 1v1c-2 0-4 1-5 0l-1-1-1-1v-1-6z" class="d"></path><path d="M522 113l3 2c-1 1-2 1-3 1 0-1 0-1-1-2 0 0 0-1 1-1z" class="Z"></path><path d="M517 109c1 1 1 1 2 1h0l-1 1c0 1 1 2 1 2 0 1 0 2-1 3v1l-1-1v-1-6z" class="E"></path><path d="M160 108h4c1 1 2 1 3 2l1 1c1-1 2-1 2-1l2-1h1l2 2c1 0 2 1 2 2 2 1 2 2 3 3v1c-1 0-2 0-3 1 0 0 1 0 1 1 2 0 5-1 6-1v2l-17 3c-2 1-4 1-6 2l-1-1-5-3c-2-2-3-4-3-6v-1l1-2h2c1 0 3-2 4-3 0 0 1 0 1-1z" class="I"></path><path d="M163 114l-2-1 1-1c1-1 2-1 3-1 0 1-1 2-2 3h0z" class="F"></path><path d="M160 111l1 1c-1 2-1 3-2 4l-2-2 3-3z" class="M"></path><path d="M173 114c1 1 1 2 3 2v1l-1 1c-1-1-2-1-3-2l1-2z" class="F"></path><path d="M171 113l2 1-1 2c-1 1-1 2-3 2v-2h1l-1-1 2-2z" class="S"></path><path d="M172 109h1l2 2c1 0 2 1 2 2v1l1 2h-1-1 0c-2 0-2-1-3-2l-2-1h0-1l-1-1v-1h-1c1-1 2-1 2-1l2-1z" class="K"></path><path d="M171 113h3c1 1 2 2 2 3h0c-2 0-2-1-3-2l-2-1h0z" class="d"></path><path d="M165 111c1-1 1-1 2-1l1 1h1v1l1 1h1 0l-2 2 1 1h-1 0 0c-1 0-1 1-2 2h-1c-1 0-2 0-3-1s-2 0-3-1l1-1s1-1 2-1h0c1-1 2-2 2-3z" class="R"></path><path d="M165 111c1-1 1-1 2-1l1 1h1c0 2-4 4-6 5h-2v-1s1-1 2-1h0c1-1 2-2 2-3z" class="S"></path><path d="M160 108c2 0 3 0 4 1v1c-2 0-2 1-4 1l-3 3c0 2-1 3 0 4l1 2h-2s-1 0-1 1c-2-2-3-4-3-6v-1l1-2h2c1 0 3-2 4-3 0 0 1 0 1-1z" class="G"></path><path d="M152 114l1-2h2l-1 1v1l2 2-1 2c0 1 1 1 1 2 0 0-1 0-1 1-2-2-3-4-3-6v-1z" class="S"></path><path d="M154 114l2 2-1 2-1-1h-1c0-1 1-2 1-3z" class="l"></path><path d="M157 114l2 2c2 2 3 4 5 5 3 0 6-1 9-2 1-1 3-1 4-1 0 0 1 0 1 1 2 0 5-1 6-1v2l-17 3c-2 1-4 1-6 2l-1-1-5-3c0-1 1-1 1-1h2l-1-2c-1-1 0-2 0-4z" class="D"></path><path d="M158 120c3 3 5 4 9 3-2 1-4 1-6 2l-1-1-5-3c0-1 1-1 1-1h2z" class="F"></path><defs><linearGradient id="Af" x1="373.942" y1="109.225" x2="376.254" y2="120.297" xlink:href="#B"><stop offset="0" stop-color="#0b070a"></stop><stop offset="1" stop-color="#333632"></stop></linearGradient></defs><path fill="url(#Af)" d="M347 112c1 0 1-1 2 0h5c4 0 34-1 36 1h0l3 3c2 1 7 1 8 3h-4-1l-1 1c-2 0-3-1-4 0l2 2c0 1 0 1-1 1v2l-3 1c-2 0-3 1-4 2l-2 1c-1-2-2-2-4-2l-11-6-15-6c-2-1-4-1-6-3z"></path><path d="M389 117c2 0 5 1 8 2h-1l-1 1c-2 0-3-1-4 0h-2c0-1-1-1-1-2l1-1z" class="F"></path><path d="M347 112c1 0 1-1 2 0h5-2c2 2 5-1 6 2h1-2c-1 1-2 1-4 1-2-1-4-1-6-3z" class="a"></path><path d="M368 115h13 6 1-1l-2 2-5 1c-1 0-2 0-3 1l-1-1-1 1-1-1h0-1c0-1 1-1 1-2h-4c-1 0-1 1-1 1h-1v-2z" class="D"></path><path d="M374 118l1-1c2-1 3 0 5 1-1 0-2 0-3 1l-1-1-1 1-1-1z" class="K"></path><path d="M380 118l5-1 2-2h1v2h1l-1 1c0 1 1 1 1 2h2l2 2c0 1 0 1-1 1v2l-3 1c-2-2-4-1-6-2-3 0-6-2-9-3v-1l-1-1 1-1h0l1 1 1-1 1 1c1-1 2-1 3-1z" class="Z"></path><path d="M392 123h-2l-2-1c-1 0-1 0-2-1 0-1 0-1 1-1h2 2l2 2c0 1 0 1-1 1z" class="E"></path><path d="M380 118l5-1 2-2h1v2h1l-1 1c0 1 1 1 1 2h-2v-1l-6 1v-1c-2 0-2 0-4 1v-1c1-1 2-1 3-1z" class="J"></path><path d="M357 114l1 1h10v2h1s0-1 1-1h4c0 1-1 1-1 2h1l-1 1 1 1v1c3 1 6 3 9 3 2 1 4 0 6 2-2 0-3 1-4 2l-2 1c-1-2-2-2-4-2l-11-6-15-6c2 0 3 0 4-1z" class="j"></path><defs><linearGradient id="Ag" x1="362.813" y1="112.265" x2="370.649" y2="120.177" xlink:href="#B"><stop offset="0" stop-color="#585356"></stop><stop offset="1" stop-color="#696a69"></stop></linearGradient></defs><path fill="url(#Ag)" d="M358 115h10v2h1s0-1 1-1h4c0 1-1 1-1 2h1l-1 1 1 1v1c-1 0-15-5-17-6h1z"></path><defs><linearGradient id="Ah" x1="169.377" y1="101.629" x2="161.009" y2="117.508" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#Ah)" d="M168 86l1-2 2 1h0 2c2 1 3 2 3 4h0 2c1 0 1 1 1 1 0-1 0-1 1-2h1 0l-1 3h1v1c-1 4-1 8 0 12l1 1v2c0 2 1 3 2 5l-1 1s-1 0-1-1v1h-1c1 1 1 1 0 2v2l-1-1c-1-1-1-2-3-3 0-1-1-2-2-2l-2-2h-1l-2 1s-1 0-2 1l-1-1c-1-1-2-1-3-2h-4c0 1-1 1-1 1-1 1-3 3-4 3h-2l-1 2v-1c-1-1-2-1-4-1 0-1 0-2 1-3-2 0-2 0-3 1h-1c1-2 2-3 3-4s3-3 4-3h0c1-1 2-2 2-3h3l1-1c0-1 2-3 3-3h1v-1c0-1 0-1 1-2h1c0-1 1-2 1-3l3-4z"></path><path d="M148 106c1-1 3-3 4-3-2 2-3 3-3 6-2 0-2 0-3 1h-1c1-2 2-3 3-4z" class="B"></path><path d="M153 112c0-3 2-4 4-5 0 1 1 1 2 2-1 1-3 3-4 3h-2z" class="H"></path><path d="M175 104c1-1 1-1 3-1 2 1 3 3 4 4 0 2 1 3 2 5l-1 1s-1 0-1-1v-2c-1-3-4-5-7-6z" class="T"></path><path d="M157 107c0-1 1-1 1-1 2-1 6-2 8-1 0 1 0 2 1 3 1 0 2-1 3-1h0l1 2h1l-2 1s-1 0-2 1l-1-1c-1-1-2-1-3-2h-4c0 1-1 1-1 1-1-1-2-1-2-2z" class="b"></path><path d="M162 105h3l-1 2s-1 0-1 1l-1-1v-2z" class="B"></path><path d="M164 108h5v1l1 1s-1 0-2 1l-1-1c-1-1-2-1-3-2z" class="P"></path><path d="M166 105c7 1 10 3 15 8 1 1 1 1 0 2v2l-1-1c-1-1-1-2-3-3 0-1-1-2-2-2l-2-2h-1-1l-1-2h0c-1 0-2 1-3 1-1-1-1-2-1-3z" class="B"></path><path d="M158 99s1-1 2 0c1 0 1-1 2-1h0 1c1 0 1-1 1-1 1 0 1 1 1 1l1 1h3 1 2 0l-1 2v1h-4l-1-1c-1 0-2 0-3 1h-5c-1-1-2 0-3-1h2v-1l1-1z" class="G"></path><path d="M166 99h3 1 2 0l-1 2h-1c-1-1-3-2-4-2z" class="F"></path><path d="M179 90c0-1 0-1 1-2h1 0l-1 3h1v1c-1 4-1 8 0 12l1 1v2c-1-1-2-3-4-4-2 0-2 0-3 1l-1-1c-1 0-2 0-3-1v-1l1-2h2l1-1 1-2v-7h2c1 0 1 1 1 1z" class="G"></path><path d="M171 101c1 0 1-1 2 0 0 0 0 1 1 1v1h0c-1 0-2 0-3-1v-1z" class="T"></path><path d="M175 98c1-1 1-1 1-3h2 0l-1 1v3h0-3l1-1z" class="R"></path><path d="M176 89h2c1 0 1 1 1 1l-1 2v3h-2c0 2 0 2-1 3l1-2v-7z" class="J"></path><path d="M168 86l1-2 2 1h0 2c2 1 3 2 3 4h0v7l-1 2-1 1h-2 0-2-1-3l-1-1s0-1-1-1c0 0 0 1-1 1h-1 0c-1 0-1 1-2 1-1-1-2 0-2 0 0-1 2-3 3-3h1v-1c0-1 0-1 1-2h1c0-1 1-2 1-3l3-4z" class="c"></path><path d="M171 94c0 1 0 2 1 3-1 0-1 0-2 1v1h-1c0-1 0-1-1-1h0c1-2 2-3 3-4z" class="C"></path><path d="M169 91h2c0 1-1 2-2 3h-4c-1 0-1 0-2-1h1c2-1 4-1 5-2z" class="Q"></path><path d="M173 93c1-1 1-1 2-1 1 1 1 2 1 4l-1 2-1 1h-2 0-2v-1c1-1 1-1 2-1-1-1-1-2-1-3 1 0 1-1 2-1z" class="L"></path><path d="M171 94c1 0 1-1 2-1v3l-1 1c-1-1-1-2-1-3z" class="P"></path><path d="M158 99c0-1 2-3 3-3h1v-1c0-1 0-1 1-2 1 1 1 1 2 1l3 1h0v1c-1 0-1 1-2 2h-1s0-1-1-1c0 0 0 1-1 1h-1 0c-1 0-1 1-2 1-1-1-2 0-2 0z" class="H"></path><path d="M168 86l1-2 2 1h0 2c2 1 3 2 3 4h0v7c0-2 0-3-1-4-1 0-1 0-2 1 0-1 0-1-1-2h0c1 1 2 1 2 1l1-1c-1-1-1-1-2-1s-3 0-4 1-3 1-5 2c0-1 1-2 1-3l3-4z" class="D"></path><path d="M171 87h2c0 1-1 1-1 2h-3v-1l1-1h1z" class="K"></path><path d="M168 86l1-2 2 1h0 2v1h0l-2 1h-1c-1 0-1-1-2-1z" class="H"></path><path d="M216 102c4 1 8 2 12 2l4 1c0-1 0-1-1-2h3c1 0 3 0 4 1h5 12 15 0l-1 1c1 0 0 1 1 1h5 8 1l-3 1h-4-5-2c0 1-1 1-1 2s0 1 1 1c2 1 5 1 7 1l1-1h0 1c1-1 2-1 2-1l1 1c2 1 4 1 6 1 4 1 7 0 10 1h-9-4-6v1c-2 1-1 2-2 3-3 2-4 2-7 2h0l-3-1h-2l-1 1h-3c-3 0-5 0-8 1h-3-2c-2 0-5 1-7 2-1 1-1 1-2 1v-1h-1l1-1c1 0 1-1 3-2h0v-1h-3c-1 1-2 1-3 1v-1h-1-7l-12-3h2v-2 1l1-1s1-1 2-1h0 0c-1-1 0-1-1-2h-1v-2l12 3 8 1h5 0c-4-1-8-3-11-4-6-2-11-2-17-5z" class="R"></path><path d="M275 112h10-6v1c-2 1-1 2-2 3-3 2-4 2-7 2h0l-3-1h2c0-1 0-1-1-2h0c1 0 2-1 3-1 1-1 1-1 2-1h2v-1z" class="a"></path><path d="M275 113h1c-2 2-4 3-7 4 0-1 0-1-1-2h0c1 0 2-1 3-1 1-1 1-1 2-1h2z" class="F"></path><path d="M231 110l8 1h5l31 1v1h-2v-1c-5 0-11-1-16 1-4 1-8 0-12 1-5-1-10-2-14-4z" class="H"></path><path d="M245 114c4-1 8 0 12-1 5-2 11-1 16-1v1c-1 0-1 0-2 1-1 0-2 1-3 1h0c-4 1-9 1-13 1-2 0-4-1-5 0-4-1-8 0-12-1 2 0 5 0 7-1h0z" class="N"></path><defs><linearGradient id="Ai" x1="257.723" y1="110.66" x2="245.566" y2="126.176" xlink:href="#B"><stop offset="0" stop-color="#bcbfbc"></stop><stop offset="1" stop-color="#e2dce2"></stop></linearGradient></defs><path fill="url(#Ai)" d="M250 116c1-1 3 0 5 0 4 0 9 0 13-1 1 1 1 1 1 2h-2-2l-1 1h-3c-3 0-5 0-8 1h-3-2c-2 0-5 1-7 2-1 1-1 1-2 1v-1h-1l1-1c1 0 1-1 3-2h0v-1h1 0c1 0 1 0 1-1h6z"></path><path d="M234 103c1 0 3 0 4 1h5 12 15 0l-1 1c1 0 0 1 1 1h5 8 1l-3 1h-4-5-2c0 1-1 1-1 2v-3h-18l-1 1c-1 1-2 1-3 2-4 1-9-2-13-4h-2c0-1 0-1-1-2h3z" class="T"></path><path d="M234 103c1 0 3 0 4 1h5 0c3 1 6 1 8 1-3 2-10 1-14 0h-3-2c0-1 0-1-1-2h3z" class="k"></path><path d="M234 103c1 0 3 0 4 1-1 0-3-1-4 0h0c1 0 2 1 3 1h-3-2c0-1 0-1-1-2h3z" class="h"></path><path d="M219 107l12 3c4 2 9 3 14 4h0c-2 1-5 1-7 1 4 1 8 0 12 1h-6c0 1 0 1-1 1h0-1-3c-1 1-2 1-3 1v-1h-1-7l-12-3h2v-2 1l1-1s1-1 2-1h0 0c-1-1 0-1-1-2h-1v-2z" class="R"></path><path d="M219 112s1-1 2-1v3c-1 0-1 0-2-1v-1z" class="G"></path><path d="M219 107l12 3c4 2 9 3 14 4h0c-2 1-5 1-7 1-4 0-8-3-12-4-2 0-3-1-5 0-1-1 0-1-1-2h-1v-2z" class="a"></path><path d="M455 101l12-6-4 5h2l2 1h1v-2c0-1 0-2 1-2 1-1 1-1 2 0s1 2 2 3 0 1 1 2h2v1-2h1l1 1v3 1l1 1h0 0l-1 1c0 1-1 2-1 4h0c-6 5-13 6-20 7h-1 0c-1 1-1 1-2 1l-4 1h0l2 1c1 0 1 0 2 1l-1 1v-1h-2l-2-2h-2v1c1 1 1 1 1 2h3-4c-1-1-1-1-2 0v-1h0v-1h-1c-1-1-2-1-3-1-2-1-4-1-6-2h-2c0-1-2-1-3-1l1-1c2 0 5-1 8-1-3 0-5 0-7-2h0l1-1s1-1 2-1v-1c1 0 2 0 3-1h0c-2 1-3 1-4 1h0l-1-1h-1l-1 1c-2 0-3 0-5-1 10-2 20-5 29-9z" class="J"></path><path d="M476 103v-2h1l1 1v3 1l1 1h0 0l-1 1c0 1-1 2-1 4h0c-6 5-13 6-20 7h-1 0c1-1 3-1 5-1 1-1 3-2 5-2l1-1c4-1 6-3 7-6l1-4 1-2z" class="C"></path><g class="E"><path d="M476 103v-2h1l1 1v3 1c-1 2-2 2-4 3l1-4 1-2z"></path><path d="M469 97c1-1 1-1 2 0s1 2 2 3 0 1 1 2h2v1l-1 2c0 1-1 1-1 2l-1 1s0 1-1 1c0 1-1 2-1 2h-1v1l-1-1-2 2v-1c0-1 1-2 1-3h0-1v-1-1-3h0v-3h1v-2c0-1 0-2 1-2z"></path></g><path d="M467 107l1-1v-1h1c1 1 1 2 1 3-1 0-1 0-2 1h0-1v-1-1z" class="c"></path><path d="M468 99c1 0 2 0 3 1l-1 1v2c-1-1-2-1-2-2v-2z" class="Q"></path><path d="M469 105h1c0-1 1-1 2-1v1l-1 4-1-1c0-1 0-2-1-3z" class="H"></path><path d="M468 109c1-1 1-1 2-1l1 1-2 2-2 2v-1c0-1 1-2 1-3z" class="b"></path><path d="M469 97c1-1 1-1 2 0s1 2 2 3 0 1 1 2h2v1l-1 2c0 1-1 1-1 2l-1 1s0 1-1 1c0 1-1 2-1 2h-1v1l-1-1 2-2 1-4c0-1 0-3-1-5-1-1-2-1-3-1 0-1 0-2 1-2z" class="F"></path><path d="M463 100h2l2 1v3h0v3 1 1h1 0c0 1-1 2-1 3v1c-6 3-14 5-21 5-3 0-7 0-11 1h-2c0-1-2-1-3-1l1-1c2 0 5-1 8-1l5-1c3 0 7-1 9-3 1-1 2-3 3-4 1 1 1 1 2 1 0 0 1-1 1-2l-1-1v-1h2v-2c2 0 3-1 3-2v-1h0z" class="M"></path><path d="M449 115c2 0 4 0 6-1l-1 1 1 1c-2 0-4 0-6-1z" class="U"></path><path d="M458 110c0 1 1 1 1 1v1c-1 0-1 1-1 1 0 1 0 1 1 1-1 1-2 1-4 2l-1-1 1-1 3-4z" class="d"></path><path d="M456 108c1 1 1 1 2 1v1l-3 4c-2 1-4 1-6 1-1 0-2 1-3 1s-1 0-2-1c3 0 7-1 9-3 1-1 2-3 3-4z" class="H"></path><path d="M463 100h2l2 1v3h0v3 1 1h1 0c0 1-1 2-1 3-2 0-3 1-4 1l-4 1c-1 0-1 0-1-1 0 0 0-1 1-1v-1s-1 0-1-1v-1s1-1 1-2l-1-1v-1h2v-2c2 0 3-1 3-2v-1h0z" class="K"></path><path d="M463 100h2l2 1v3h0c-1-1-2-2-4-3v-1h0z" class="N"></path><path d="M459 111c1-1 1-2 2-3l-1-1v-1h2c0-1 1-2 1-3h1l1 2v2l-1-1-1 1c0 2-1 4-3 5l-1 1v-1-1z" class="J"></path><path d="M465 105l1 1v1l1 1v1h1 0c0 1-1 2-1 3-2 0-3 1-4 1l-4 1c-1 0-1 0-1-1 0 0 0-1 1-1v1l1-1c2-1 3-3 3-5l1-1 1 1v-2z" class="I"></path><path d="M466 107l1 1v1h1 0c0 1-1 2-1 3-2 0-3 1-4 1 1-1 1-2 2-3 0-1 1-2 1-3z" class="U"></path><path d="M455 101l12-6-4 5h0v1c0 1-1 2-3 2v2h-2v1l1 1c0 1-1 2-1 2-1 0-1 0-2-1-1 1-2 3-3 4-2 2-6 3-9 3l-5 1c-3 0-5 0-7-2h0l1-1s1-1 2-1v-1c1 0 2 0 3-1h0c-2 1-3 1-4 1h0l-1-1h-1l-1 1c-2 0-3 0-5-1 10-2 20-5 29-9z" class="f"></path><path d="M440 111l7-3 7-2c2 1 1 0 2-1h2v1l1 1c0 1-1 2-1 2-1 0-1 0-2-1l-1-1c-1 0-1 0-2 1s-2 1-4 2h-2v1h-1-6z" class="O"></path><path d="M440 111h6 1v-1h2c2-1 3-1 4-2s1-1 2-1l1 1c-1 1-2 3-3 4-2 2-6 3-9 3l-5 1c-3 0-5 0-7-2h0l1-1 3-1h1l3-1z" class="F"></path><path d="M436 112h1c2 0 3 0 4 1h1 0v2h0 0c-3-1-5 1-8-1h0l2-2z" class="G"></path><defs><linearGradient id="Aj" x1="445.348" y1="102.249" x2="447.097" y2="106.097" xlink:href="#B"><stop offset="0" stop-color="#5d5b5b"></stop><stop offset="1" stop-color="#828081"></stop></linearGradient></defs><path fill="url(#Aj)" d="M455 101l12-6-4 5h0c-6 3-12 5-18 7l-11 4-1-1h-1l-1 1c-2 0-3 0-5-1 10-2 20-5 29-9z"></path><path d="M407 118c1 0 2-1 3-1 3-1 6-1 8-1h4c1 1 6 1 7 2h1c1 0 3 0 3 1h2c2 1 4 1 6 2 1 0 2 0 3 1h1v1h0v1c1-1 1-1 2 0h4-3c0-1 0-1-1-2v-1h2l2 2h2v1l1-1c-1-1-1-1-2-1l-2-1h0l4-1c1 0 1 0 2-1h0 1c1 0 2 0 3 1h1 1c0 1 1 1 2 1v-1c1 0 4-1 6 0 1 0 1 1 2 1v1 1h5c0 1 1 2 1 2l13 1c0 1 0 1 1 1h2c-2 2-4 1-6 2h-32c-6 0-12 0-17-1h-2c-3 1-7 0-11 0h-25c-2 0-6 1-8 0h-3l2-1v-2-2c1 0 1 0 1-1l-2-2c1-1 2 0 4 0l1-1h1 4c2 0 4-1 6-1z" class="Y"></path><path d="M396 123c1 1 1 1 1 2l-1 2h0-2v-1c0-1 1-2 2-3z" class="C"></path><path d="M396 123h3 2c-1 1-3 3-5 4l1-2c0-1 0-1-1-2z" class="D"></path><path d="M429 118h1c1 0 3 0 3 1h2c-1 0-1 1-1 2-2 0-2 0-3-1l-2-2z" class="G"></path><path d="M406 120c0 1 0 1-1 2h-3l-1 1h-2l1-1c2-1 4-2 6-2z" class="I"></path><path d="M429 122c3 1 6 3 9 5-3-1-6-1-8-1v-1h0l1-1h0l-2-1v-1z" class="E"></path><path d="M420 120c3 1 6 1 9 2v1c-1-1-2-1-4-1l-1 1h-3l-1-3zm-15 2c0 1 0 1 1 2-1 0-3 1-3 2h0-2v-1h-1v1s-1 0-1 1h-3 0c2-1 4-3 5-4l1-1h3z" class="R"></path><path d="M407 118c1 0 2-1 3-1 3-1 6-1 8-1h4c-4 0-9 1-13 3v1h0 3c1-1 2-1 2-1v2c-2 2-5 2-8 3-1-1-1-1-1-2 1-1 1-1 1-2s1-1 1-2z" class="S"></path><path d="M414 119l6 1 1 3h3l1-1c2 0 3 0 4 1l2 1h0l-1 1h0v1h-27 0c0-1 2-2 3-2 3-1 6-1 8-3v-2z" class="j"></path><path d="M424 123l1-1c2 0 3 0 4 1l2 1h0l-1 1-6-2z" class="S"></path><path d="M414 119l6 1 1 3c-2 0-6 0-7-1v-1-2z" class="Q"></path><defs><linearGradient id="Ak" x1="462.957" y1="127.789" x2="472.027" y2="118.603" xlink:href="#B"><stop offset="0" stop-color="#332f31"></stop><stop offset="1" stop-color="#474a48"></stop></linearGradient></defs><path fill="url(#Ak)" d="M456 119h1c1 0 2 0 3 1h1 1c0 1 1 1 2 1v-1c1 0 4-1 6 0 1 0 1 1 2 1v1 1h5c0 1 1 2 1 2-10 0-20 1-30 0h-4l-1-1c-1 0-2 0-3-1h1l1 1h3c1-1 1-1 2 0h4-3c0-1 0-1-1-2v-1h2l2 2h2v1l1-1c-1-1-1-1-2-1l-2-1h0l4-1c1 0 1 0 2-1h0z"></path><path d="M456 119h1c1 0 2 0 3 1h1 1c0 1 1 1 2 1v-1c1 0 4-1 6 0 1 0 1 1 2 1v1 1c-2 0-4 0-6-1l-1 1h-4c-2 0-3-2-5-1h-1c1 1 2 1 2 2h-6-3c0-1 0-1-1-2v-1h2l2 2h2v1l1-1c-1-1-1-1-2-1l-2-1h0l4-1c1 0 1 0 2-1h0z" class="S"></path><defs><linearGradient id="Al" x1="470.989" y1="104.704" x2="510.403" y2="133.41" xlink:href="#B"><stop offset="0" stop-color="#acabab"></stop><stop offset="1" stop-color="#d3d2d3"></stop></linearGradient></defs><path fill="url(#Al)" d="M504 102h-1v-1-1h1c1 0 2 1 3 1 2 2 3 3 5 3l2-1 1 2c1 1 2 3 2 4v6 1l-1 3c0 1-2 3-3 5h0c-1 0-2 1-3 2h12 4c-1 1-1 2-2 2h-27l-1-1h-2-2c-1 0-1 0-1-1l-13-1s-1-1-1-2h-5v-1-1c-1 0-1-1-2-1-2-1-5 0-6 0v1c-1 0-2 0-2-1h-1-1c-1-1-2-1-3-1 7-1 14-2 20-7l1 1 1 1h-1v1c1 0 3-1 4-1s1 1 2 1l1-2c0-1 1-1 1-2l1-1v-1c1-1 2-1 4-2 2-2 5-2 8-3h4v-2h1z"></path><path d="M515 117c0-1 1-1 2-1l-1 3c0 1-2 3-3 5h0c0-1-1-2 0-3s1-2 2-4z" class="S"></path><path d="M477 123c4 0 10 1 14 3l-13-1s-1-1-1-2z" class="B"></path><path d="M475 119h0v-1h6 2l3 1c2 1 5 2 7 3h-1c-6-2-11-3-17-3z" class="C"></path><path d="M513 117c0 1 0 2-1 2-2 2-3 3-5 4-1 1-1 1-2 1h-1c-1 1-1 0-3 0l6-3c1-1 1-2 2-3 0-1 0-1 1-1s1 1 2 1l1-1z" class="G"></path><path d="M485 113v2l1 1-2 1-1 1h-2-6v1h0-1c-1 0-1 0-1-1 1-2 3-2 4-3h1c1 0 3-1 4-1s1 1 2 1l1-2z" class="b"></path><path d="M485 113v2l1 1-2 1h-3c1-1 2-1 3-1v-1l1-2z" class="H"></path><path d="M481 118h-3v-1h3 3l-1 1h-2z" class="Z"></path><path d="M494 118h2l1 1h2v1l-1 1c1 0 1 0 2-1h2 1l-2 2h0-3c0 1 0 1 1 2h-1c-2 0-4-1-6-2h1c-2-1-5-2-7-3 2-1 4 0 7 0l1-1z" class="M"></path><path d="M497 119h2v1l-1 1c-1-1-2-1-3-1l2-1z" class="K"></path><path d="M494 118h2l1 1-2 1-2-1 1-1z" class="c"></path><path d="M486 111c1 0 2 1 2 1 1-1 2-1 2-1 0 1-1 2-1 3l1 1 1 1c1-1 1-2 2-2h1c-1 1-2 1-2 3 1 0 1 1 2 1l-1 1c-3 0-5-1-7 0l-3-1 1-1 2-1-1-1v-2c0-1 1-1 1-2z" class="K"></path><path d="M486 111c1 0 2 1 2 1l1 1-3 3-1-1v-2c0-1 1-1 1-2z" class="X"></path><path d="M507 115v2l-2 2 2 2-6 3h-3 1c-1-1-1-1-1-2h3 0l2-2h-1-2c-1 1-1 1-2 1l1-1v-1h0l1-1h0c2-1 2-2 3-3 1 1 2 1 2 1l2-1z" class="U"></path><path d="M503 115c1 1 2 1 2 1l-3 3c-1 0-1-1-1-1h-1c2-1 2-2 3-3z" class="I"></path><path d="M505 108c1 1 1 1 2 1 0 1 1 1 2 1 1 1 2 3 3 3l-1 1h-1l-1 1c1 0 2 1 3 1h1 0v1l-1 1c-1 0-1-1-2-1s-1 0-1 1c-1 1-1 2-2 3l-2-2 2-2v-2l-2 1s-1 0-2-1c-1 1-1 2-3 3h0l-1 1h0-2l-1-1h-2c-1 0-1-1-2-1 0-2 1-2 2-3l1-1 2-1s1-1 2-1h2c0-1 1-2 2-2l2-1z" class="F"></path><path d="M500 114l1-3c1 2 1 3 2 4-1 1-1 2-3 3h0l-1-1c0-1 0-2 1-3z" class="S"></path><path d="M507 115l-1-1c0-1-1-2-2-3h0c1 0 2 1 3 2s2 3 2 5c-1 1-1 2-2 3l-2-2 2-2v-2z" class="D"></path><path d="M497 112c1 1 1 1 3 2-1 1-1 2-1 3l1 1-1 1h0-2l-1-1h-2c-1 0-1-1-2-1 0-2 1-2 2-3l1-1 2-1z" class="S"></path><path d="M492 117h1c1-1 1-1 2-1h1v1c-1 0-1 0 0 1h0-2c-1 0-1-1-2-1z" class="I"></path><path d="M497 112c1 1 1 1 3 2-1 1-1 2-1 3l1 1-1 1h0-2l-1-1h0s1 0 1-1c1 0 1-1 1-1l-3-3 2-1z" class="Q"></path><path d="M504 102h-1v-1-1h1c1 0 2 1 3 1 2 2 3 3 5 3l2-1 1 2c1 1 2 3 2 4v6 1c-1 0-2 0-2 1v-1l1-1h-1c0 1-1 1-2 1h0-1c-1 0-2-1-3-1l1-1h1l1-1c-1 0-2-2-3-3-1 0-2 0-2-1-1 0-1 0-2-1l-2 1c-1 0-2 1-2 2h-2c-1 0-2 1-2 1l-2 1-1 1h-1c-1 0-1 1-2 2l-1-1-1-1c0-1 1-2 1-3 0 0-1 0-2 1 0 0-1-1-2-1l1-1v-1c1-1 2-1 4-2 2-2 5-2 8-3h4v-2h1z" class="C"></path><path d="M499 104h4 1c2 1 3 1 4 2-2 1-2 1-4 1l-1-1c-1-1-1 0-3 0h0v-1-1h-1z" class="Z"></path><path d="M508 104c1 0 2 1 4 1v2 2c-2-1-3-2-4-3s-2-1-4-2h4z" class="J"></path><path d="M490 111c2 0 4-2 5-3h1c1 0 2 0 3 1 3-1 4-1 6-1l-2 1c-1 0-2 1-2 2h-2c-1 0-2 1-2 1l-2 1-1 1h-1c-1 0-1 1-2 2l-1-1-1-1c0-1 1-2 1-3z" class="c"></path><path d="M504 102h-1v-1-1h1c1 0 2 1 3 1 2 2 3 3 5 3l2-1 1 2c1 1 2 3 2 4v6 1c-1 0-2 0-2 1v-1l1-1h-1c0 1-1 1-2 1h0-1c-1 0-2-1-3-1l1-1h1l1-1v-4-2-2c-2 0-3-1-4-1h-4-1v-2h1z" class="Y"></path><path d="M504 102c1 0 3 1 4 2h-4-1v-2h1zm8 2l2-1 1 2c1 1 2 3 2 4v6c-1-1-2-1-2-1v-1c0-1 0-1 1-2v-1c-2-2-2-4-4-6z" class="F"></path><path d="M512 107c1 1 1 3 3 4h1c-1 1-1 1-1 2v1s1 0 2 1v1c-1 0-2 0-2 1v-1l1-1h-1c0 1-1 1-2 1h0-1c-1 0-2-1-3-1l1-1h1l1-1v-4-2z" class="G"></path><path d="M483 65c1 0 1 0 2 1l9 13 1 1 4 5 4 5 6 6v1h-1v1l1 2c-1 1-1 1-2 1s-2-1-3-1h-1v1 1h1-1v2h-4c-3 1-6 1-8 3-2 1-3 1-4 2v1l-1 1c0 1-1 1-1 2l-1 2c-1 0-1-1-2-1s-3 1-4 1v-1h1l-1-1-1-1h0c0-2 1-3 1-4l1-1h0 0l-1-1v-1-3l-1-1h-1v2-1h-2c-1-1 0-1-1-2s-1-2-2-3-1-1-2 0c-1 0-1 1-1 2v2h-1l-2-1h-2l4-5-12 6c0-2 1-2 2-3v-1c3-1 4-4 6-5h1l-1-1 1-1c-3 2-5 4-7 5 2-2 5-4 7-6v-1c2-1 3-3 5-5 0-1 0-1 1-2v1l1-1c-2 4-5 7-8 11 2-1 3-1 4-2 4-4 8-7 12-11 0-2 2-5 2-7 1-2 2-5 2-7z" class="F"></path><path d="M479 79l-2 4c-2 3-6 6-10 7 4-4 8-7 12-11z" class="G"></path><path d="M483 65c1 0 1 0 2 1-2 1-1 4-1 6v1c-1 1-1 3-1 5v1c-1 0-1 1-1 2l1 1s0 1 1 1v3h-1c-1 0-1 0-2-1h-1-1c0 1-1 2-2 3 0 1-2 2-2 3v1l-3 1c-1 0-1 0-1-1 4-3 7-7 9-13 1-1 1-3 2-4v-1c0-1 0-1-1-2 1-2 2-5 2-7z" class="B"></path><path d="M479 85h1 1c1 1 1 1 2 1l-1 1v2h0l-2 2v1h1-3v1s1 0 1 1h-1c0 1-1 1-1 2-1 1-1 2-2 2h-1v1l-1 1c-1-1-1-2-2-3s-1-1-2 0c-1 0-1 1-1 2v2h-1l-2-1h-2l4-5c1 0 2-2 4-3 0 1 0 1 1 1l3-1v-1c0-1 2-2 2-3 1-1 2-2 2-3z" class="P"></path><path d="M479 85h1 1c1 1 1 1 2 1l-1 1v2h-1c-1-1-1-2-2-4z" class="J"></path><path d="M472 95h1c1 1 1 1 3 1l-2 2v1l-1 1c-1-1-1-2-2-3 0-1 0-1 1-2z" class="L"></path><path d="M467 95c1 0 2-2 4-3 0 1 0 1 1 1l-2 2h2c-1 1-1 1-1 2-1-1-1-1-2 0-1 0-1 1-1 2v2h-1l-2-1h-2l4-5z" class="D"></path><path d="M465 100v-1c1-1 2-2 4-2-1 0-1 1-1 2v2h-1l-2-1z" class="d"></path><defs><linearGradient id="Am" x1="483.005" y1="70.087" x2="493.571" y2="76.623" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#302f2e"></stop></linearGradient></defs><path fill="url(#Am)" d="M485 66l9 13-2 2-1-1v1c0 1-1 2-2 2h-2c0-1-1-1-1-2v-1c-2 0-2 0-4 1 0-1 0-2 1-2v-1c0-2 0-4 1-5v-1c0-2-1-5 1-6z"></path><path d="M484 73c2 2 5 4 7 7v1c0 1-1 2-2 2h-2c0-1-1-1-1-2v-1c-2 0-2 0-4 1 0-1 0-2 1-2v-1c0-2 0-4 1-5z" class="H"></path><path d="M486 81h2c0-1 0-1 1-1s2 0 2 1-1 2-2 2h-2c0-1-1-1-1-2z" class="B"></path><path d="M482 81c2-1 2-1 4-1v1c0 1 1 1 1 2v1 2h0 1l1 1h0c-1 2-1 3-1 5h0v2s-1 0-1 1v1 3 4h0l1 2c1 0 2 1 3 2-2 1-3 1-4 2v1l-1 1c0 1-1 1-1 2l-1 2c-1 0-1-1-2-1s-3 1-4 1v-1h1l-1-1-1-1h0c0-2 1-3 1-4l1-1h0 0l-1-1v-1-3l-1-1h-1v2-1h-2c-1-1 0-1-1-2l1-1v-1h1c1 0 1-1 2-2 0-1 1-1 1-2h1c0-1-1-1-1-1v-1h3-1v-1l2-2h0v-2l1-1h1v-3c-1 0-1-1-1-1l-1-1z" class="T"></path><path d="M488 105c1 0 2 1 3 2-2 1-3 1-4 2h0c0-2 1-3 1-4z" class="R"></path><path d="M478 105h1c1 1 2 1 3 1-1 2-3 4-5 6 0-2 1-3 1-4l1-1h0 0l-1-1v-1z" class="D"></path><path d="M482 100l2-1 1 2-2 2c0 1-1 2-1 3-1 0-2 0-3-1 1-2 2-4 3-4l1-1h-1z" class="C"></path><path d="M478 113h2c1-1 2-2 3-4 1-1 2-3 3-4h0c0 1-1 2-1 4 0 0 1 0 2 1l-1 1c0 1-1 1-1 2l-1 2c-1 0-1-1-2-1s-3 1-4 1v-1h1l-1-1zm4-32c2-1 2-1 4-1v1c0 1 1 1 1 2v1 2h0 1l1 1h0c-1 2-1 3-1 5h0c-1 0-2-1-3-2 0-1 1-1 0-2l-1 1v1c0 1 2 2 2 4l-1 1c0 1 0 2 1 3-1 1-1 2-1 3l-1-2v-3h0l-1-1h0 0-1-1v-2-1h-1v-1l2-2h0v-2l1-1h1v-3c-1 0-1-1-1-1l-1-1z" class="F"></path><path d="M482 89c1 2 2 4 2 7h0 0l-1-1h0 0-1-1v-2-1h-1v-1l2-2z" class="L"></path><path d="M474 98h1c1 0 1-1 2-2 0-1 1-1 1-2h1c0-1-1-1-1-1v-1h3v1 2h1 1 0 0l1 1h0v3l-2 1h1l-1 1c-1 0-2 2-3 4h-1v-3l-1-1h-1v2-1h-2c-1-1 0-1-1-2l1-1v-1z" class="P"></path><path d="M483 95l1 1h0v3l-2 1-1-1c0-2 1-3 2-4z" class="e"></path><path d="M474 98h1c1 0 1-1 2-2 0-1 1-1 1-2h1c0-1-1-1-1-1v-1h3v1 2h-2l-1 1c0 1 0 1 1 2h1v1h0-1-1v2c1 1 1 1 1 2-1 0-1 0-1-1l-1-1h-1v2-1h-2c-1-1 0-1-1-2l1-1v-1z" class="d"></path><path d="M494 79l1 1 4 5 4 5 6 6v1h-1v1l1 2c-1 1-1 1-2 1s-2-1-3-1h-1v1 1h1-1v2h-4c-3 1-6 1-8 3-1-1-2-2-3-2l-1-2h0v-4-3-1c0-1 1-1 1-1v-2h0c0-2 0-3 1-5h0l-1-1h-1 0v-2-1h2c1 0 2-1 2-2v-1l1 1 2-2z" class="O"></path><path d="M492 95l1 1c0 1 0 2-1 3 0 0-1 1-2 1v-1c0-2 1-3 2-4z" class="B"></path><path d="M492 93c1 0 2 1 3 1-1 1-1 1-1 2h0c1 0 1 1 1 2h1c-1 1-2 1-4 1 1-1 1-2 1-3l-1-1h0v-2z" class="b"></path><path d="M493 86l1 1c1 1 0 3 0 4l1 1h-1l1 1h1v1h-1c-1 0-2-1-3-1v-2l1-2h0c-1-2-1-2 0-3z" class="Q"></path><path d="M489 87c0-1 0-1 2-2 0 0 1-1 2-1l1 1v1h-1c-1 1-1 1 0 3h-3s-1-1-1-2h0z" class="e"></path><path d="M491 80l1 1c1 1 2 3 3 4h-1l-1-1c-1 0-2 1-2 1-2 1-2 1-2 2l-1-1h-1 0v-2-1h2c1 0 2-1 2-2v-1z" class="Q"></path><path d="M494 79l1 1 4 5 4 5c0 2 0 3 1 4-1 0-1 0-2 1h0s0 1-1 1v1c-1-1-2-2-3-2l-2-1v-1h-1l-1-1h1l-1-1c0-1 1-3 0-4l-1-1h1v-1h1c-1-1-2-3-3-4l2-2z" class="O"></path><path d="M494 79l1 1c0 2 0 3 1 4v1h-1c-1-1-2-3-3-4l2-2z" class="L"></path><path d="M495 92l1-2h1l1 1v1 1l2-1 2 3s0 1-1 1v1c-1-1-2-2-3-2l-2-1v-1h-1l-1-1h1z" class="J"></path><path d="M499 85l4 5c0 2 0 3 1 4-1 0-1 0-2 1h0l-2-3h-1v-2l1 1 1-1-3-4s0-1 1-1z" class="L"></path><path d="M503 90l6 6v1h-1v1l1 2c-1 1-1 1-2 1s-2-1-3-1h-1v1 1h1-1v2h-4c-3 1-6 1-8 3-1-1-2-2-3-2l-1-2h0v-4-3-1c0-1 1-1 1-1v-2h0c1 3 2 5 2 8h0c1 0 2-1 2-1 2 0 3 0 4-1h-1c0-1 0-2-1-2h0c0-1 0-1 1-2h1l2 1c1 0 2 1 3 2v-1c1 0 1-1 1-1h0c1-1 1-1 2-1-1-1-1-2-1-4z" class="G"></path><path d="M497 99h1v1 1h-1-1-1l2-2z" class="T"></path><path d="M490 100c1 0 2 1 3 1v1c-1 1-1 1-2 1h-2c-1-1-1-2-1-4h0l2 1h0z" class="Y"></path><path d="M495 94h1l2 1c1 0 2 1 3 2v-1c1 0 1-1 1-1h0 0l2 2h0c-1 1-1 1-1 2-1-1-3 0-4-1v-1h-1v1h-2-1c0-1 0-2-1-2h0c0-1 0-1 1-2z" class="O"></path><path d="M503 90l6 6v1h-1v1h-1c-1 1-2 1-4 1 0-1 0-1 1-2h0l-2-2h0c1-1 1-1 2-1-1-1-1-2-1-4z" class="H"></path><path d="M502 95c1-1 1-1 2-1 1 1 1 2 3 4h0c-1 1-2 1-4 1 0-1 0-1 1-2h0l-2-2h0z" class="e"></path><path d="M180 65c2 2 1 5 1 7 0 1 1 2 2 3 0 5 4 11 8 14l1 1 5 5s2 1 2 2l2 1-1 1-1 1c2 1 3 1 4 2v1h0c0 1 1 1 2 1v1c0 1 0 2 1 2s1-1 2 0h1l1-2h0 1c1 1 1 1 3 2 1 0 3 2 5 2h1c1 1 0 1 1 2h0 0c-1 0-2 1-2 1l-1 1v-1 2h-2l12 3h7 1v1c1 0 2 0 3-1h3v1h0c-2 1-2 2-3 2l-1 1h1v1h-1c-1 1-1 1-2 1h-3c-1 0-2 0-2 1-2 0-3 1-4 2h-2-2 0-4c-5-1-11 0-16 0 1-4 6-3 8-5v-1h-4c-1-1-4-1-5-1h-4c-1-1-2-1-3-1l-2 3c-3 0-5 0-7-1h-1 0-1v-2c-1 0-4 1-6 1 0-1-1-1-1-1 1-1 2-1 3-1v-1l1 1v-2c1-1 1-1 0-2h1v-1c0 1 1 1 1 1l1-1c-1-2-2-3-2-5v-2l-1-1c-1-4-1-8 0-12v-1h-1l1-3h0-1c-1 1-1 1-1 2 0 0 0-1-1-1h-2 0c0-2-1-3-3-4h-2 0l-2-1v-1c0-1 2-3 3-4 1-2 1-3 1-4l1-1 6-9z" class="X"></path><path d="M187 111c2 2 3 3 5 4h-4l-1-1v-3z" class="J"></path><path d="M183 97v-4-1l1-1s0-1 1-1c0 1 0 1 1 1h0 1c0 2-1 3-2 4h0l1 1c-1 1-2 1-3 1z" class="Z"></path><path d="M182 105c1 2 3 5 5 6v3-1c-2 0-2-1-3-1-1-2-2-3-2-5v-2zm18 12l1-1h1l4 1 8 1-1 1h-2l-11-2z" class="F"></path><path d="M198 113c1 0 1 0 1-1-1 0-1-1-2-2h1c1 1 1 2 3 3 0 0 1 1 2 1l1 1 1-1h1v1 1 1l-4-1h-1l-1 1c0-1-1-1-2-1s-2-1-3-2c0 0 1 0 2-1v1c1 0 1 0 1-1z" class="P"></path><path d="M205 114h1v1 1 1l-4-1 1-1h1l1-1z" class="E"></path><path d="M184 112c1 0 1 1 3 1v1l1 1h4c3 2 6 2 10 4h-4c-1-1-2-1-3-1l-2 3c-3 0-5 0-7-1h-1 0-1v-2c-1 0-4 1-6 1 0-1-1-1-1-1 1-1 2-1 3-1v-1l1 1v-2c1-1 1-1 0-2h1v-1c0 1 1 1 1 1l1-1z" class="F"></path><path d="M187 118l-2-2h2c1 0 3 1 5 2h-1v1c-1 0-1 0-1 1h-2c-1-1-1-1-1-2z" class="C"></path><path d="M187 118c1 0 1-1 3 0v2h-2c-1-1-1-1-1-2z" class="X"></path><path d="M184 112c1 0 1 1 3 1v1l1 1-2 1h1-2l2 2c0 1 0 1 1 2h-3 0-1v-2c-1 0-4 1-6 1 0-1-1-1-1-1 1-1 2-1 3-1v-1l1 1v-2c1-1 1-1 0-2h1v-1c0 1 1 1 1 1l1-1z" class="W"></path><path d="M184 112c1 0 1 1 3 1v1l1 1-2 1c0-1-1-1-2-1l-2-2v-1c0 1 1 1 1 1l1-1zm52 6c1 0 2 0 3-1h3v1h0c-2 1-2 2-3 2l-1 1h1v1h-1c-1 1-1 1-2 1h-3c-1 0-2 0-2 1-2 0-3 1-4 2h-2-2 0-4l1-1h-6l-1-1h-6c2-1 4-3 6-2 1 0 2-1 3-1h0 2c2 0 4-1 7-1s7-1 11-2z" class="G"></path><path d="M213 124c3-1 6 0 8-2h1l-2 2v1h-6l-1-1z" class="B"></path><path d="M236 118c1 0 2 0 3-1h3v1h0c-2 1-2 2-3 2l-1 1h1v1h-1c-1 1-1 1-2 1h-3c1-1 2-1 2-2h0 0c-2 0-3 1-4 1 0 1-1 1-2 1 0 1-1 1-2 1v-1-1c-1 1-1 1-2 1s-1-1-2-1h-1 0c1-1 2-1 3-2 3 0 7-1 11-2z" class="i"></path><path d="M199 107h0v-2c2 0 3-1 4-1v1h2c0 1 0 2 1 2s1-1 2 0h1l1-2h0 1c1 1 1 1 3 2 1 0 3 2 5 2h1c1 1 0 1 1 2h0 0c-1 0-2 1-2 1l-1 1v-1 2h-2l12 3h7 1v1c-4 1-8 2-11 2s-5 1-7 1h-2c-1-1-3-1-5-2h0 2l1-1-8-1v-1-1-1h-1l-1 1-1-1c-1 0-2-1-2-1-2-1-2-2-3-3v-1h-1l2-2z" class="M"></path><path d="M214 118c4 0 9-1 13 0-3 1-6 1-9 2v1h-2c-1-1-3-1-5-2h0 2l1-1z" class="R"></path><path d="M214 107c1 0 3 2 5 2h1c1 1 0 1 1 2h0 0c-1 0-2 1-2 1l-1 1v-1 2h-2 0c-1 0 0 0-1-1h-1l-1-1c-1 0-2-1-3-3v-2h1l2 2c0-1 0-2 1-2z" class="Q"></path><path d="M216 114c0-1-1-2-1-2-1 0-1 0-1-1v-1c2 0 2 0 4 1v1 2h-2 0z" class="Y"></path><path d="M199 107h0v-2c2 0 3-1 4-1v1c1 1 1 2 1 3 0 0 1 0 0 1v3h1l1-2c0-1-1-1-1-2h1c1 0 1 0 1 1v1 1c0 1 0 1 1 1 1-1 1-1 2 0-1 2-3 0-3 3h-1v-1h-1l-1 1-1-1c-1 0-2-1-2-1-2-1-2-2-3-3v-1h-1l2-2z" class="Q"></path><path d="M200 109h2s0 1 1 2h-2c0-1-1-1-1-2z" class="U"></path><path d="M201 111h2v3c-1 0-2-1-2-1v-2z" class="W"></path><path d="M198 109h2c0 1 1 1 1 2v2c-2-1-2-2-3-3v-1z" class="I"></path><path d="M180 65c2 2 1 5 1 7 0 1 1 2 2 3 0 5 4 11 8 14l1 1 5 5s2 1 2 2l2 1-1 1-1 1c2 1 3 1 4 2v1h0c0 1 1 1 2 1v1h-2v-1c-1 0-2 1-4 1v2h0l-2 2h1v1h-1c1 1 1 2 2 2 0 1 0 1-1 1 0 1 0 1-1 1v-1c-1 1-2 1-2 1-1 0-2-1-2-2-1 1-1 1-2 1-1-1-1-2-2-3-3-3-6-8-6-13 1 0 2 0 3-1l-1-1h0c1-1 2-2 2-4h-1l1-1h-1l1-2h-1l-2-1c-1 2-2 3-3 5v-1h-1l1-3h0-1c-1 1-1 1-1 2 0 0 0-1-1-1h-2 0c0-2-1-3-3-4h-2 0l-2-1v-1c0-1 2-3 3-4 1-2 1-3 1-4l1-1 6-9z" class="D"></path><path d="M194 102c1-1 2-2 2-3 2-1 2 0 4 0l-1 1c-1 1-2 2-3 2h-2z" class="c"></path><path d="M185 86h1l1 2h1c2 1 2 2 3 3 0 1-1 1-1 1l-2-2h-1-1l1-2h-1l-2-1 1-1z" class="W"></path><path d="M179 83c1-1 1-2 3-3v1h0l-1 1c-1 1-1 3-1 5h-1c-1 0-2 0-2-1h0v-1c1-1 1-2 2-2z" class="J"></path><path d="M194 102h2c-1 2 0 4 0 6l1 1h1v1h-1c1 1 1 2 2 2 0 1 0 1-1 1l-1-1c-2-2-3-4-3-7v-3z" class="U"></path><path d="M177 77h1 1v-1c1 0 1-1 2-1l1 1v1c0 2-1 2-2 3 0 1-1 1-2 2 0-1-1-1-1-2v-3h0z" class="J"></path><path d="M189 105c1 1 2 2 3 2l1-1v2c1 2 2 4 4 5-1 1-2 1-2 1-1 0-2-1-2-2-1 0-1 0-1-1-1-2-2-3-3-5v-1z" class="c"></path><path d="M195 96h1v2c-2 1-3 3-3 5v3l-1 1c-1 0-2-1-3-2h0c1-2 1-4 0-5v-1h1c2 0 3-1 5-3z" class="F"></path><path d="M199 100c2 1 3 1 4 2v1h0c0 1 1 1 2 1v1h-2v-1c-1 0-2 1-4 1v2h0l-2 2-1-1c0-2-1-4 0-6 1 0 2-1 3-2z" class="B"></path><path d="M199 107c-1-1-1 0-2 0v-1c0-1 1-1 1-2l1-1c1-1 2 0 4 0 0 1 1 1 2 1v1h-2v-1c-1 0-2 1-4 1v2h0z" class="K"></path><path d="M187 90h1l2 2s1 0 1-1c2 1 5 4 5 5h-1c-2 2-3 3-5 3v-2l1-1h0c-2 0-2 2-3 2l-2-2-1-1h0c1-1 2-2 2-4h-1l1-1z" class="P"></path><path d="M187 94c1 0 1-1 2 0 1 0 2 0 2 1h0c-2 0-3 0-4-1z" class="O"></path><path d="M185 95c1 0 1 0 2-1 1 1 2 1 4 1h0l2-1 2 2c-2 2-3 3-5 3v-2l1-1h0c-2 0-2 2-3 2l-2-2-1-1z" class="H"></path><path d="M174 80l1-1c0-1 1-1 2-2h0v3c0 1 1 1 1 2l1 1c-1 0-1 1-2 2v1h0c0 1 1 1 2 1h1c1 0 1-1 2-1h3l-1 1c-1 2-2 3-3 5v-1h-1l1-3h0-1c-1 1-1 1-1 2 0 0 0-1-1-1h-2 0c0-2-1-3-3-4h-2l-1-1c0-1 3-3 4-4z" class="F"></path><path d="M174 80l1-1c0-1 1-1 2-2h0v3c0 1 1 1 1 2l1 1c-1 0-1 1-2 2v1c0-2-1-3-2-4-1 0-1-1-1-2z" class="V"></path><path d="M186 96l2 2c1 0 1-2 3-2h0l-1 1v2h-1v1c1 1 1 3 0 5h0v1c1 2 2 3 3 5 0 1 0 1 1 1-1 1-1 1-2 1-1-1-1-2-2-3-3-3-6-8-6-13 1 0 2 0 3-1z" class="U"></path><path d="M185 98h1c1 1 1 1 1 2h-1-1 0v-2z" class="b"></path><path d="M189 110c1-1-2-5-3-7 0-1 0 0 1-1h1c0 1 1 3 1 4 1 2 2 3 3 5 0 1 0 1 1 1-1 1-1 1-2 1-1-1-1-2-2-3z" class="D"></path><defs><linearGradient id="An" x1="174.878" y1="66.121" x2="174.964" y2="82.957" xlink:href="#B"><stop offset="0" stop-color="#282627"></stop><stop offset="1" stop-color="#515252"></stop></linearGradient></defs><path fill="url(#An)" d="M180 65c2 2 1 5 1 7 0 1 1 2 2 3l-1 1-1-1c-1 0-1 1-2 1v1h-1-1c-1 1-2 1-2 2l-1 1c-1 1-4 3-4 4l1 1h0l-2-1v-1c0-1 2-3 3-4 1-2 1-3 1-4l1-1 6-9z"></path><defs><linearGradient id="Ao" x1="204.959" y1="119.3" x2="209.373" y2="152.925" xlink:href="#B"><stop offset="0" stop-color="#494747"></stop><stop offset="1" stop-color="#626364"></stop></linearGradient></defs><path fill="url(#Ao)" d="M137 132h125 24 15c3 0 6 0 9-1-3 3-6 5-10 8l-16 5h-1c-1-1-4-1-6-2h0c-1-1-1-2-1-3h-4c-11 3-22 7-31 13-1 1-1 1-2 1l-4 2-2-1v-1c-1 1-1 2-2 2l-1 1c-1 0-2 0-3 1l-1 1c-1-1 0-2-1-2l-1 1v1c-1 1-1 1-2 0-2 1-3 2-4 3l-4 4v1l2 1c-1 1-2 1-3 1-2 0-3-1-5 0l-1 1v-4l-2-1v-1c-1 0-1 0-2 1 0-2-1-3-1-4-1-1-2-2-3-4-1 0-2-1-2-1l-2-2h0c-1-1-1-1-1-2l1-1-3-2c-1 0-2 0-3 1h0-1l-3-4c-2-1-4-3-6-4h-1c-7-4-17-3-24-3h-22-19-1-1v-1 1c0-1-1-1-1-1-1 0-2-2-2-3s0-1-1-1v-1h9 21 0z"></path><path d="M212 138c2 0 4-1 6-1l-1 1-1 2-1 2c-1-1-1-1-2-1 0 0-1 0-2-1l-2-1c1-1 2-1 3-1z" class="D"></path><path d="M216 140c-1-1-1-1-1-2h2l-1 2z" class="E"></path><path d="M209 139c1-1 2-1 3-1 0 1 1 2 1 3 0 0-1 0-2-1l-2-1z" class="B"></path><path d="M189 141c-2-1-5-2-7-3l12 1h4c2 0 5 0 6 1-1 2-2 2-4 3-4 0-7-1-11-2z" class="m"></path><path d="M209 139l2 1c1 1 2 1 2 1 1 0 1 0 2 1-2 2-6 3-9 3h-1c-1 0-4 0-5 1h-1v-1c-2-1-2-2-4-1v1l-6-3h0v-1c4 1 7 2 11 2 2-1 3-1 4-3h0 2c0-1 2-1 3-1z" class="P"></path><path d="M209 139l2 1s-1 1-2 1c-2 0-3 1-3 2-2 1-4 1-5 0h-1c2-1 3-1 4-3h0 2c0-1 2-1 3-1z" class="E"></path><path d="M233 138h21 16c3-1 7-1 10 0 0 1 0 0-1 1h-3-4c-11 3-22 7-31 13h-2c0-1 0-1-1-1h0c0-2 3-3 4-4 2 0 4-2 5-2l1-1 4-1-1-2h1c1 0 2 0 3-1l-13 1c-1 0-2 0-3 1-1 0-2-1-3 0v1h0c-1 0-1 0-2-1 0-1 0-2-1-3v-1z" class="f"></path><path d="M255 140h4l-7 3-1-2h1c1 0 2 0 3-1z" class="E"></path><path d="M236 143v-1c1-1 2 0 3 0 1-1 2-1 3-1l13-1c-1 1-2 1-3 1h-1l1 2-4 1-1 1c-1 0-3 2-5 2-1 1-4 2-4 4h0c1 0 1 0 1 1h2c-1 1-1 1-2 1l-4 2-2-1v-1c-1 1-1 2-2 2l-1 1c-1 0-2 0-3 1l-1 1c-1-1 0-2-1-2l-1 1v1c-1 1-1 1-2 0-2 1-3 2-4 3l-1-2h0v-1-1c-1-1-4 1-6-1h-1v-2c3 0 6 0 9-1h0c3-1 4-1 7-3s6-3 8-6v-2h0c1 1 1 1 2 1h0z" class="C"></path><path d="M248 144v-3c1 0 2 0 3 1v-1l1 2-4 1z" class="b"></path><path d="M241 147l2-2h0 2v-3h0c1 0 2 2 2 3-1 0-3 2-5 2h-1zm-5-4c1-1 3-1 4 0v1c2-1 1-2 3-2v1c-1 2-2 1-3 3 0 0-1 1-2 1 0 1-1 1-1 1-1 1-2 0-3 0 1-1 2-2 4-3 1 0 1-1 2-2h-1c-1 0-1 1-2 1l-1-1h0z" class="M"></path><defs><linearGradient id="Ap" x1="224.798" y1="148.032" x2="236.955" y2="147.164" xlink:href="#B"><stop offset="0" stop-color="#5e5e5c"></stop><stop offset="1" stop-color="#777578"></stop></linearGradient></defs><path fill="url(#Ap)" d="M234 142c1 1 1 1 2 1l1 1c1 0 1-1 2-1h1c-1 1-1 2-2 2-2 1-3 2-4 3l-4 2c-2 2-5 3-8 3h-3c3-1 4-1 7-3s6-3 8-6v-2h0z"></path><path d="M241 147h1c-1 1-4 2-4 4h0c1 0 1 0 1 1h2c-1 1-1 1-2 1l-4 2-2-1v-1c-1 1-1 2-2 2l-1 1c-1 0-2 0-3 1-1 0-1 0-1-1 1-1 1-1 2-1l5-4h0c-1 0-2 0-3-1l4-2c1 0 2 1 3 0 0 0 1 0 1-1h2 1z" class="W"></path><path d="M233 153c1-1 1-1 3-2l3 2-4 2-2-1v-1z" class="f"></path><path d="M238 147h2l-2 2c-2 0-3 1-5 2h0c-1 0-2 0-3-1l4-2c1 0 2 1 3 0 0 0 1 0 1-1z" class="X"></path><path d="M219 153h3c3 0 6-1 8-3 1 1 2 1 3 1h0l-5 4c-1 0-1 0-2 1 0 1 0 1 1 1l-1 1c-1-1 0-2-1-2l-1 1v1c-1 1-1 1-2 0-2 1-3 2-4 3l-1-2h0v-1-1c-1-1-4 1-6-1h-1v-2c3 0 6 0 9-1h0z" class="B"></path><path d="M215 156c4-1 9-1 13-1-1 0-1 0-2 1 0 1 0 1 1 1l-1 1c-1-1 0-2-1-2l-1 1v1c-1 1-1 1-2 0v-1c-1 0-2-1-3-1h-4z" class="b"></path><path d="M211 156h4 4c1 0 2 1 3 1v1c-2 1-3 2-4 3l-1-2h0v-1-1c-1-1-4 1-6-1z" class="M"></path><path d="M218 137c2-1 5-1 7 0h2c2 0 4 0 6 1v1c1 1 1 2 1 3h0v2c-2 3-5 4-8 6-2 0-4 1-5 1l-2-1h-1c-4 1-9 0-13-1-2-1-3-2-5-3 1-1 4-1 5-1h1c3 0 7-1 9-3l1-2 1-2 1-1z" class="a"></path><path d="M217 145h0c1-2 2-3 4-4l1 1h0l-2 2c-1 1-2 3-3 3h0v-2z" class="C"></path><path d="M222 142c2-1 2-2 4-2 1 1 1 1 1 2l-2 1c-1 2-3 3-5 5l1 1-2 1h-1c-4 1-9 0-13-1-2-1-3-2-5-3 1-1 4-1 5-1l-1 1h0c1 1 2 1 3 1s2 1 3 1c3 0 5-2 7-3v2h0c1 0 2-2 3-3l2-2z" class="D"></path><path d="M222 142c2-1 2-2 4-2 1 1 1 1 1 2l-2 1c-1 2-3 3-5 5-1 1-2 1-4 1h-3v-1h3l1-1c1 0 2-2 3-3l2-2z" class="Z"></path><path d="M222 142c2-1 2-2 4-2 1 1 1 1 1 2l-2 1c-2 0-2 1-3 2-1 0-2-1-2-1l2-2z" class="E"></path><path d="M227 137c2 0 4 0 6 1v1c1 1 1 2 1 3h0v2c-2 3-5 4-8 6-2 0-4 1-5 1l-2-1 2-1-1-1c2-2 4-3 5-5l2-1c0-1 0-1-1-2h2c0-1-1-2-1-3z" class="X"></path><path d="M229 145c1-2 0-4-1-6h5c1 1 1 2 1 3h-1 0l-1 1h-1c-1 0-1 0-1 1l-1 1z" class="Z"></path><path d="M229 145l1-1c0-1 0-1 1-1h1l1-1h0 1 0v2c-2 3-5 4-8 6-2 0-4 1-5 1l-2-1 2-1c3-1 6-3 8-4z" class="Q"></path><path d="M186 145l-1-2c2 0 5 3 7 3v-1c2 0 2 0 3 1h0l1-1h-1v-1c2-1 2 0 4 1v1h1c2 1 3 2 5 3 4 1 9 2 13 1h1l2 1c1 0 3-1 5-1-3 2-4 2-7 3h0c-3 1-6 1-9 1v2h1c2 2 5 0 6 1v1 1h0l1 2-4 4v1l2 1c-1 1-2 1-3 1-2 0-3-1-5 0l-1 1v-4l-2-1v-1c-1 0-1 0-2 1 0-2-1-3-1-4-1-1-2-2-3-4-1 0-2-1-2-1l-2-2h0c-1-1-1-1-1-2l1-1-3-2c-1 0-2 0-3 1h0-1l-3-4h1z" class="a"></path><path d="M186 145c2 0 4 1 6 3-1 0-2 0-3 1h0-1l-3-4h1z" class="M"></path><path d="M201 153c1 1 1 1 2 1v2h0c1-1 1-1 2-1 1 1 2 1 2 2 1 0 2 0 3-1h1c2 2 5 0 6 1v1h-5l-2 1c-1 0-3-1-4 0-2-1-4-4-5-6z" class="D"></path><path d="M195 145v-1c2-1 2 0 4 1v1h1c2 1 3 2 5 3h-1l-1 1h-2l-5-2h0c-1-1-3-1-4-2v-1c2 0 2 0 3 1h0l1-1h-1z" class="K"></path><path d="M196 145c1 1 2 1 3 2-1 1-2 0-3 1h0c-1-1-3-1-4-2v-1c2 0 2 0 3 1h0l1-1z" class="P"></path><path d="M195 150l6 4v-1c1 2 3 5 5 6 0 1 0 2 1 3v1h1l1 1-2 1-2-1v-1c-1 0-1 0-2 1 0-2-1-3-1-4-1-1-2-2-3-4-1 0-2-1-2-1l-2-2h0c-1-1-1-1-1-2l1-1z" class="K"></path><path d="M197 155l1-1 3 3 1 1v1c1 0 1 1 1 1 1 1 2 2 2 3-1 0-1 0-2 1 0-2-1-3-1-4-1-1-2-2-3-4-1 0-2-1-2-1z" class="Q"></path><path d="M203 150l1-1h1c4 1 9 2 13 1h1l2 1c1 0 3-1 5-1-3 2-4 2-7 3h0c-3 1-6 1-9 1h0c-4-1-6-2-9-4h2z" class="J"></path><path d="M221 151c1 0 3-1 5-1-3 2-4 2-7 3h0l-7-1c3 0 6-1 9-1z" class="K"></path><path d="M203 150c3 1 6 2 9 2l7 1c-3 1-6 1-9 1h0c-4-1-6-2-9-4h2z" class="E"></path><path d="M217 158v1h0l1 2-4 4v1l2 1c-1 1-2 1-3 1-2 0-3-1-5 0l-1 1v-4l2-1-1-1h-1v-1c-1-1-1-2-1-3 1-1 3 0 4 0l2-1h5z" class="K"></path><path d="M211 163h1 1c1 1 1 1 1 2v1h-2c0-1 0-2-1-3z" class="M"></path><path d="M206 159c1-1 3 0 4 0l2-1c1 1 2 1 3 2v1h-2c-1 0-3 0-4 1l-1 1h-1v-1c-1-1-1-2-1-3z" class="P"></path><path d="M206 159c1-1 3 0 4 0h-2c0 1 0 1 1 1l-2 2c-1-1-1-2-1-3z" class="E"></path><path d="M208 163l1-1 1 2 1-1c1 1 1 2 1 3h2l2 1c-1 1-2 1-3 1-2 0-3-1-5 0l-1 1v-4l2-1-1-1z" class="U"></path><path d="M211 163c1 1 1 2 1 3h-1c1 1 0 0 0 1h-1-2c1-1 2-2 2-3h0l1-1z" class="c"></path></svg>