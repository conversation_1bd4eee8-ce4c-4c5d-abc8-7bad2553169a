<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="102 111 824 852"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#c7c9cd}.C{fill:#cdd5db}.D{fill:#787c80}.E{fill:#2f3135}.F{fill:#efeff1}.G{fill:#07080b}.H{fill:#3d3f44}.I{fill:#aeb7bd}.J{fill:#f8f8f9}.K{fill:#d9dadc}.L{fill:#87939c}.M{fill:#a7aaaf}.N{fill:#969a9e}.O{fill:#5a5d64}.P{fill:#84888e}.Q{fill:#dfe0e2}.R{fill:#6d737a}.S{fill:#bcc2c7}.T{fill:#646a6f}.U{fill:#111214}.V{fill:#adb2b7}.W{fill:#42454b}.X{fill:#9da1a6}.Y{fill:#e5e6e8}.Z{fill:#1e1f22}.a{fill:#35383d}.b{fill:#51565a}.c{fill:#9fa7ae}</style><path d="M649 380c1 1 2 2 2 3v1h-2c-1-1-2-2-2-4h2z" class="Z"></path><path d="M471 410c-1-1-2-3-3-4v-1l4 1 2 2v1l-3 1z" class="K"></path><path d="M570 463c1 2 1 3 2 5l1 2v-1h1c0 3 0 5-1 7-1-5-2-8-4-12l1-1z" class="H"></path><path d="M638 484l1 4c-1 3-1 6-3 8l-1-3 3-9z" class="S"></path><path d="M635 493l1 3c0 2-1 4-2 6h0c-1 1-1 2-3 2l4-11z" class="V"></path><path d="M616 289c3-2 5-2 9-3-4 2-7 4-10 8l-1-1c1-2 2-3 2-4z" class="B"></path><path d="M547 466c2-2 3-4 4-6v-1-1h0c2 2 1 7 3 9-3-1-5-1-7-1z" class="H"></path><path d="M640 369c2 1 3 2 4 4l1 2c-1 0-1 1-2 1-2-3-5-4-8-6 1 0 2-1 3 0 1 0 1 0 2-1z" class="W"></path><path d="M650 358c1 0 3 1 5 2 1 1 1 3 1 4s1 1 1 1v8l-3-5c1-1 1-3 1-5-1-1-1-2-2-3l-3-2z" class="Z"></path><path d="M482 360l1 2c-1 3-3 6-3 9l-1-1v-2c-1 0-2 0-3-1 3-2 4-4 6-7h0z" class="J"></path><path d="M481 446c1 2 1 4 0 6l-1 1h-1c0 2 1 2 0 4l-3-6c1-2 4-3 5-5z" class="F"></path><path d="M584 601h10c-6 3-12 5-18 3h-1 5c1-1 3-2 4-3z" class="E"></path><path d="M600 400c4 4 6 6 11 7 1 1 0 1 1 1-7-1-11-2-15-7 1 0 2 0 3-1z" class="M"></path><path d="M578 600l6 1c-1 1-3 2-4 3h-5c-2 0-3-1-5-1v-1l-2-1 4 1h6v-2z" class="W"></path><path d="M646 333l-2-1c1-1 3-2 4-3h1c1-1 2-2 3-2h1l-2 2h3v1l-3 3c-1 1-3 2-4 2 0-1 0 0 1-1h1l-1-1h0 0-1-1z" class="E"></path><path d="M564 387l2-1v-1c1-1 1-1 2-1 4 1 8 3 11 6 0 1 0 1 1 2h-1-1l-1-1v-1c-1-1-2-2-3-2-2-1-7-1-10-1z" class="N"></path><path d="M642 339c1-1 1-1 2-1v-1h3v-1c1 0 0 0 1-1 1 0 2-1 4-2v1l-1 1h0 2l3 1 1 1s0 1-1 1l-1-1c-4 1-8 2-13 2z" class="E"></path><path d="M639 337h4c-2 1-3 1-5 2h2 1c0-1 0-1 1-1v1c5 0 9-1 13-2l1 1h0c-3-1-5 1-8 2-2 0-4 1-6 1v-1c-3 1-6 0-8-1h-1l6-2z" class="a"></path><path d="M634 404c2 3 0 10-1 13h0l-6-1c4-4 6-7 7-12z" class="P"></path><path d="M187 346c7 0 12-2 19-6h0c-5 5-12 8-19 9l-2-2h0v-1h2z" class="M"></path><path d="M651 318l1 1-1 4c-3 3-6 5-9 6 0-1 1-1 1-2l-1-1c3-3 6-5 9-8z" class="X"></path><path d="M651 318l6-8c1-1 1 0 1-1 1 4-1 8-3 11h0l-4 3 1-4-1-1z" class="Z"></path><path d="M421 441c1-2 2-2 4-2s3 0 4 2c1 1 1 2 1 3-1 2-3 2-3 4v1h-2v-1c-1-2-1-4 0-5h2v-1l-1 1c-2-1-3-1-5-2z" class="F"></path><path d="M169 349l1-1h-1l1-2h2 2 1l8 1h2l2 2c-5 1-12 1-18 0z" class="B"></path><path d="M138 189c1 1 2 1 3 3l-1 1c-1 1-3 1-4 2-2-1-4-1-6-1-1 0-2-1-3-1 3-3 6-4 11-4z" class="b"></path><path d="M454 384v-1c1-1 10 0 14-4l-2-2h1l2-1c5 0 7-2 10-6l1 1c-1 1-2 4-3 5 0 0-2 1-2 2-4 2-8 3-12 5-3 1-6 1-9 1z" class="C"></path><path d="M481 487l-8-12c5 1 8 2 12 5h0l-4 7z" class="H"></path><path d="M161 321c3 9 8 16 15 21l-3-1h0c-4 0-6-1-9-3l2-1c0-1-2-4-3-6-1-3-2-6-2-10z" class="B"></path><path d="M616 425v-2l1-1c3 1 7 6 10 9v2c0 2-2 4-3 6l-1-1c-1-6-3-10-7-13z" class="N"></path><path d="M580 588h0c-4 2-8 2-12 3h-3 0-2l-1-1c-1 0-1-1-2-2 3 0 6 0 9-1 4 0 7-2 10-3 1 1 2 1 2 2v1c-1 0-1 0-1 1z" class="W"></path><path d="M602 310c1-2 1-4 1-6 2-6 7-12 13-15 0 1-1 2-2 4-1 1-2 2-3 4-3 4-5 7-6 13h-2-1z" class="C"></path><path d="M547 454v-2c-2-1-4-3-5-5l1-1c3 2 6 4 6 8-1 3-4 5-7 7-1 1-3 3-3 4v5h0v1h-1v-5c-1-2 0-4 2-6l7-6z" class="P"></path><path d="M421 441c2 1 3 1 5 2l1-1v1h-2c-1 1-1 3 0 5v1c0 1 0 4 1 5v1h0c-3 0-6-4-7-6v-2c0-3 0-5 2-6z" class="B"></path><defs><linearGradient id="A" x1="599.689" y1="578.524" x2="580.045" y2="581.517" xlink:href="#B"><stop offset="0" stop-color="#101016"></stop><stop offset="1" stop-color="#434648"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M579 584c3-1 7-2 10-4 4-3 9-7 14-9-7 8-13 14-23 17 0-1 0-1 1-1v-1c0-1-1-1-2-2z"></path><path d="M550 399c8-7 16-9 27-8l1 1c-11 0-17 2-25 9l-1 1-3 4c-1-1-1-1-2-3l3-4z" class="Y"></path><path d="M555 379l1-1h0 1c0 2 0 2 2 2 1 2 3 2 6 3 1 0 5-1 6 0-1 0-2 1-3 1s-1 0-2 1v1l-2 1c-6 2-12 5-17 9-2 0-3 2-5 4 0-1 1-2 1-3l1-1c1-1 2-1 3-2 2-1 3-1 4-3l8-5 1-1v-2c-1-1-3-2-4-3l-1-1z" class="S"></path><defs><linearGradient id="C" x1="550.616" y1="465.84" x2="540.846" y2="478.881" xlink:href="#B"><stop offset="0" stop-color="#95989d"></stop><stop offset="1" stop-color="#b1b3ba"></stop></linearGradient></defs><path fill="url(#C)" d="M547 466c2 0 4 0 7 1 2 1 3 3 4 5h-1-2c-4-2-8-2-11 0-2 1-3 2-4 3h-1l-1 1h0l-1-1 1-4h1v-1h0 2l6-4z"></path><path d="M612 504c0-6 0-12-4-17l-1-1c-1 0-1 0-1-1l15-13c0 1-1 1-1 2-1 1-2 1-3 2v1c-1 0-1 1-2 1-1 1-3 2-4 3s-2 2-2 3h1c1 1 1 2 2 3l3 6v2h0c1 2 1 7 0 9l-1 2c0-2 1-4 1-6l-1 2h0c-1 1-1 1-1 2h-1z" class="Z"></path><defs><linearGradient id="D" x1="215.628" y1="264.605" x2="210.97" y2="267.88" xlink:href="#B"><stop offset="0" stop-color="#202225"></stop><stop offset="1" stop-color="#373941"></stop></linearGradient></defs><path fill="url(#D)" d="M206 260h1c2 1 5 2 7 3h1l5 6c-7-1-13-2-20-2 1 0 3 0 4-1v-2l-3-1h5v-1l1-1h-2l1-1z"></path><defs><linearGradient id="E" x1="584.824" y1="611.005" x2="561.483" y2="609.835" xlink:href="#B"><stop offset="0" stop-color="#151519"></stop><stop offset="1" stop-color="#303134"></stop></linearGradient></defs><path fill="url(#E)" d="M562 609c7-2 15 0 22-2 1 0 1 0 2 1h0-1c-1 1-2 1-3 1-2 0-4 1-6 2h-3c-1 1-2 2-3 2s-1 0-2 1c0 0-1 0-1 1-3 1-3-2-5-3h-1c0-1 0-2 1-3z"></path><path d="M583 498v4l-19 23c0-1 0-1 1-2v-3c1-1 2-4 3-6l5-6 3-3c1-1 2-1 2-2l5-5z" class="O"></path><path d="M423 284l-7-3c-10-4-20-2-30 3 0-1 1-2 3-3 6-4 17-7 25-5 1 1 2 2 4 3h0l-1 1h1c1 1 1 1 3 2h0c1 1 2 1 2 2z" class="V"></path><defs><linearGradient id="F" x1="548.615" y1="572.986" x2="537.885" y2="557.514" xlink:href="#B"><stop offset="0" stop-color="#5d605f"></stop><stop offset="1" stop-color="#737683"></stop></linearGradient></defs><path fill="url(#F)" d="M551 544c1-1 2-2 3-4l1 1c-4 7-8 16-11 24-2 6-3 12-5 17l-1-1c0-3 0-5 1-8 2-7 5-17 9-24l3-5z"></path><path d="M576 611l-3 2c2 2 6 1 9 2-1 0-2 1-3 1-5 2-10 5-15 8-1-2-1-8-3-10h0v-1c-1-2-1-3-1-4h2c-1 1-1 2-1 3h1c2 1 2 4 5 3 0-1 1-1 1-1 1-1 1-1 2-1s2-1 3-2h3z" class="Z"></path><defs><linearGradient id="G" x1="598.907" y1="476.119" x2="589.938" y2="502.83" xlink:href="#B"><stop offset="0" stop-color="#494a4f"></stop><stop offset="1" stop-color="#5e6165"></stop></linearGradient></defs><path fill="url(#G)" d="M600 481c1 0 2-2 3-2 4-3 8-6 12-8h1l-33 31v-4c3-2 5-5 8-7 3-3 6-6 9-10z"></path><path d="M542 584v-5-1-1h1v-1-2l1-1v-1l1-2 1-2v-2h1c0-1 0-2 1-3l1-3c0-1 1-2 1-3l2-4 1-2v-1c0-1 0-1 1-2v-1l2-2c0-1 0-2 1-3l4-6h-1c3-2 4-5 6-8 3-4 6-8 9-10-1 3-4 5-5 7l3-3 1 1c-1 2-4 4-5 6-2 3-3 4-5 6-1 1-1 3-2 4l-1 1c-2 2-3 5-5 7-2 4-4 6-4 10-3 6-10 20-9 26l-1 1z" class="a"></path><path d="M607 277c9-3 19-2 28 3 2 1 4 2 6 4-3-2-6-3-9-4-8-2-17-1-24 1-4 2-7 4-10 7 0-1 0-2 1-2h0v-1l-3 3-1-1c2-2 4-3 7-5 2-1 4-2 5-4v-1z" class="M"></path><defs><linearGradient id="H" x1="410.964" y1="304.068" x2="417.354" y2="296.968" xlink:href="#B"><stop offset="0" stop-color="#d3d9dd"></stop><stop offset="1" stop-color="#fdfcfe"></stop></linearGradient></defs><path fill="url(#H)" d="M414 301c-3-6-7-10-13-13-1 0-2-1-3-1 8 0 14 3 19 9 5 5 7 12 6 18 0 5-2 8-3 12l-1 1-1-1c2-3 3-7 3-10 0-7-1-12-6-17v1l-1 1z"></path><defs><linearGradient id="I" x1="559.265" y1="399.311" x2="556.883" y2="389.779" xlink:href="#B"><stop offset="0" stop-color="#676f78"></stop><stop offset="1" stop-color="#818b96"></stop></linearGradient></defs><path fill="url(#I)" d="M564 387c3 0 8 0 10 1 1 0 2 1 3 2v1c-11-1-19 1-27 8-3 1-6 4-8 6l-1-1 6-8c5-4 11-7 17-9z"></path><defs><linearGradient id="J" x1="624.548" y1="318.386" x2="616.127" y2="327.076" xlink:href="#B"><stop offset="0" stop-color="#b4b8be"></stop><stop offset="1" stop-color="#e8e7e9"></stop></linearGradient></defs><path fill="url(#J)" d="M614 293l1 1c-4 7-7 12-4 21 1 5 5 9 9 12 6 3 12 2 19 0l3-1 1 1c0 1-1 1-1 2l-3 2c-5 1-11 2-16 0h0c-3-1-5-2-7-3-4-3-7-7-7-12-1-5-1-13 2-18h1l-1-1c1-2 2-3 3-4z"></path><path d="M639 327l3-1 1 1c0 1-1 1-1 2l-3 2-2-1h0l-1-1c1 0 2-1 3-2z" class="V"></path><defs><linearGradient id="K" x1="565.027" y1="452.425" x2="574.045" y2="438.171" xlink:href="#B"><stop offset="0" stop-color="#b0b6b9"></stop><stop offset="1" stop-color="#d9d9dc"></stop></linearGradient></defs><path fill="url(#K)" d="M566 441c-2-3-5-6-5-9l1-1c2-1 4 1 6 3 5 4 9 11 10 18v9l-1 1-2-3c-1-6-5-14-9-18z"></path><path d="M552 590c2 3 6 7 9 8v1c-1 0-2-1-4-1v10c1 7 3 13 5 20h-3l-3-11-3-1-1-2-1-3c1-2 1-4 1-7l-1-1c-1-4 1-9 1-13z" class="T"></path><path d="M554 607l2 10-3-1-1-2c1-3 1-5 2-7z" class="E"></path><path d="M552 604l2-5v4 4c-1 2-1 4-2 7l-1-3c1-2 1-4 1-7z" class="H"></path><path d="M554 599c0-1 0-4 1-5 1 2 2 2 2 4v10c0-2 0-5-1-6l-2 1v-4z" class="b"></path><path d="M552 590c2 3 6 7 9 8v1c-1 0-2-1-4-1 0-2-1-2-2-4-1 1-1 4-1 5l-2 5-1-1c-1-4 1-9 1-13z" class="E"></path><path d="M627 450l2 1v1c-1 0-1 1-1 2l-9 11 6-6 1 1v-1c1 0 1 0 2-1 1 0 2-1 3-2h1c-5 5-10 11-16 15h-1c-4 2-8 5-12 8-1 0-2 2-3 2-1 1-2 1-3 3h-2c10-11 23-22 32-34z" class="H"></path><defs><linearGradient id="L" x1="644.903" y1="421.984" x2="635.728" y2="419.545" xlink:href="#B"><stop offset="0" stop-color="#151616"></stop><stop offset="1" stop-color="#37393e"></stop></linearGradient></defs><path fill="url(#L)" d="M640 378c6 5 8 13 8 21 1 4 0 8 0 12-1 4-1 9-3 13-3 11-9 21-16 29l-1 1c0-1 0-2 1-2v-1l-2-1c3-4 7-7 9-12 3-5 6-10 7-16v-1l2-16c1-7 0-14-5-21h1l1-1c-1-2-1-3-2-5z"></path><path d="M566 441c4 4 8 12 9 18v3c0 5 1 12-2 16v-2c1-2 1-4 1-7h-1v1l-1-2c-1-2-1-3-2-5l-1 1-12-18c3-2 6-4 9-5z" class="T"></path><path d="M570 463c-1-3-4-9-3-12 0 0 1 0 1-1 1 1 2 1 2 3 3 4 4 11 4 16h-1v1l-1-2c-1-2-1-3-2-5z" class="W"></path><defs><linearGradient id="M" x1="618.415" y1="382.501" x2="594.248" y2="378.932" xlink:href="#B"><stop offset="0" stop-color="#979ba2"></stop><stop offset="1" stop-color="#dadcde"></stop></linearGradient></defs><path fill="url(#M)" d="M613 362h7c0 1 2 2 1 4h-1 0 0c-6 1-13 3-17 8-4 4-6 11-5 16 0 4 1 7 2 10-1 1-2 1-3 1-2-2-3-4-3-7-2-6-1-13 2-19 4-7 10-11 17-13z"></path><defs><linearGradient id="N" x1="629.921" y1="314.986" x2="612.568" y2="328.948" xlink:href="#B"><stop offset="0" stop-color="#2f3236"></stop><stop offset="1" stop-color="#5a6065"></stop></linearGradient></defs><path fill="url(#N)" d="M611 297l1 1h-1c-3 5-3 13-2 18 0 5 3 9 7 12 2 1 4 2 7 3h0c5 2 11 1 16 0l3-2c3-1 6-3 9-6l4-3c-1 1-1 2-2 3s-2 1-3 2l-2 2c-2 1-4 2-5 3 0 1 0 2-1 4h1 0 1v-1h2 1 1 0 0l1 1h-1c-1 1-1 0-1 1-1 1-2 1-3 2h-1-4l-6 2h-1l-1-1c-1-1-2-1-4-1l-2-1c-4-1-7-2-10-4s-8-7-9-11-1-7-1-11c1-6 3-9 6-13z"></path><path d="M627 337c3 0 9-1 12 0l-6 2h-1l-1-1c-1-1-2-1-4-1z" class="T"></path><defs><linearGradient id="O" x1="864.817" y1="263.48" x2="917.648" y2="238.023" xlink:href="#B"><stop offset="0" stop-color="#b1b5b8"></stop><stop offset="1" stop-color="#ecebed"></stop></linearGradient></defs><path fill="url(#O)" d="M902 206c10 13 14 30 12 47l-1 4v1c-2 10-7 20-15 26-5 3-11 4-16 3-3-1-7-3-9-5l1-1h0c1 1 1 0 2 1l3 1c4 1 9 1 13-1 2-1 4-2 6-4l2-2h2c4-3 7-11 8-16 2-11 2-23-1-34-1-4-3-7-5-11l-1-3c-1-2-2-4-1-6z"></path><defs><linearGradient id="P" x1="633.315" y1="359.599" x2="634.382" y2="380.689" xlink:href="#B"><stop offset="0" stop-color="#242428"></stop><stop offset="1" stop-color="#3a3d41"></stop></linearGradient></defs><path fill="url(#P)" d="M636 359c3 0 5 1 8 2h2c1 2 3 2 4 3l2 2c1 0 1 1 1 1-2 1-3 4-4 6s-1 4 0 6v1h-2l-4-4c1 0 1-1 2-1l-1-2c-1-2-2-3-4-4-1 1-1 1-2 1-1-1-2 0-3 0-5-2-9-3-15-4h0 0 1c1-2-1-3-1-4h-7l1-1-1-1h16c2-1 4-1 7-1z"></path><path d="M620 362c7 1 14 2 20 7-1 1-1 1-2 1-1-1-2 0-3 0-5-2-9-3-15-4h0 0 1c1-2-1-3-1-4z" class="X"></path><path d="M414 276c12 2 21 8 28 18s8 22 5 34c0 4-2 7-4 11-1 0-2 0-3-1 3-8 6-18 4-27h0c-1-8-5-13-9-18h-1l-1 1c-3-4-6-7-10-10 0-1-1-1-2-2h0c-2-1-2-1-3-2h-1l1-1h0c-2-1-3-2-4-3z" class="Y"></path><path d="M418 279c6 3 13 8 17 14h-1l-1 1c-3-4-6-7-10-10 0-1-1-1-2-2h0c-2-1-2-1-3-2h-1l1-1z" class="I"></path><defs><linearGradient id="Q" x1="393.209" y1="363.008" x2="433.8" y2="389.327" xlink:href="#B"><stop offset="0" stop-color="#c0c2c5"></stop><stop offset="1" stop-color="#e6e6e7"></stop></linearGradient></defs><path fill="url(#Q)" d="M399 363c6-2 14-2 19 1 7 4 11 10 13 17 2 6 1 13-2 18-2 4-6 7-11 8l-6 1 4-1c4-2 8-5 10-9 2-5 3-12 1-18-3-5-7-10-13-12s-14-1-20 1c-4 2-7 5-10 8l-2 1v-1c1-2 4-5 6-7 3-4 7-5 11-7z"></path><path d="M533 360v-2l3 1c1 0 1-1 2-2h1c1 1 2 2 2 4l2 2h1v1c1 1 2 3 3 4h1v1c0 3 1 4 3 6 2 1 4 1 6 1l1 1c0 1 0 1-1 1h-1 0l-1 1 1 1c1 1 3 2 4 3v2l-1 1-8 5c-4-1-6-4-9-6 1-2 1-3 1-5 0-1-1-2-2-3l-7-13c0-1-1-3-1-4z" class="C"></path><path d="M541 361l2 2 4 10c-4 0-4-5-7-6 0-2-1-3-1-4l2-2z" class="X"></path><path d="M538 365l2 2h0c3 1 3 6 7 6l1 3c2 2 4 3 7 3l1 1c-3 0-5-1-7-2-5-4-8-7-11-13z" class="M"></path><path d="M533 360v-2l3 1c1 0 1-1 2-2h1c1 1 2 2 2 4l-2 2c0 1 1 2 1 4h0l-2-2h0l-2-2-1 1h0-1c0-1-1-3-1-4z" class="I"></path><defs><linearGradient id="R" x1="536.664" y1="370.687" x2="537.656" y2="364.868" xlink:href="#B"><stop offset="0" stop-color="#91939c"></stop><stop offset="1" stop-color="#a7abae"></stop></linearGradient></defs><path fill="url(#R)" d="M534 364h1 0l1-1 2 2h0c3 6 6 9 11 13-1 0-2 1-3 1s-2-2-4-2l1 1c0 1 1 1 1 2h-1c0-1-1-2-2-3l-7-13z"></path><defs><linearGradient id="S" x1="544.753" y1="383.046" x2="553.295" y2="379.156" xlink:href="#B"><stop offset="0" stop-color="#6b737c"></stop><stop offset="1" stop-color="#858e96"></stop></linearGradient></defs><path fill="url(#S)" d="M544 380c0-1-1-1-1-2l-1-1c2 0 3 2 4 2s2-1 3-1c2 1 4 2 7 2 1 1 3 2 4 3v2l-1 1-8 5c-4-1-6-4-9-6 1-2 1-3 1-5h1z"></path><path d="M543 380h1c1 3 3 7 6 8h2c1-1 2-1 3-2l2-1 2 1-8 5c-4-1-6-4-9-6 1-2 1-3 1-5z" class="L"></path><defs><linearGradient id="T" x1="179.517" y1="269.978" x2="184.529" y2="283.875" xlink:href="#B"><stop offset="0" stop-color="#6b6f73"></stop><stop offset="1" stop-color="#9ca1a9"></stop></linearGradient></defs><path fill="url(#T)" d="M198 257l1 1 5-1c-1 1-3 1-3 3 2 0 3-1 5 0l-1 1h2l-1 1v1h-5l3 1v2c-1 1-3 1-4 1-13 2-24 7-32 18-3 3-5 7-6 10-1-2-1-3 0-5l2-5-1-1 6-9v-2c4-4 8-6 11-9 3-2 7-4 10-5l8-2z"></path><path d="M183 266h2v1c-8 4-16 11-21 18l-1-1 6-9c3-1 4-4 7-5l3-3c2-1 2 1 4-1z" class="H"></path><path d="M198 257l1 1 5-1c-1 1-3 1-3 3 2 0 3-1 5 0l-1 1h2l-1 1v1h-5l-16 4v-1h-2c-2 2-2 0-4 1l-3 3c-3 1-4 4-7 5v-2c4-4 8-6 11-9 3-2 7-4 10-5l8-2z" class="a"></path><path d="M201 260c2 0 3-1 5 0l-1 1h2l-1 1v1h-5l-16 4v-1h-2v-1s1 0 1-1h2c1-1 3-1 4-2 2-1 4-1 5-1 1-1 2-1 3-1h3 0z" class="W"></path><path d="M415 300v-1c5 5 6 10 6 17 0 3-1 7-3 10l-2 2c-2 0-5 4-7 5l-1 1-2 1c-1 0-1-1-2 0-1 0 0 0-1-1h0l2-2 2-2-7 2c-3 1-6 2-10 2-2-1-4-1-6-2v-1c-3-1-5-3-8-5h0l1-1-7-8c-3-4-4-8-3-13 3 8 8 17 16 21 6 3 13 5 20 3 4-1 8-5 10-9 3-7 3-12 1-18l1-1z" class="F"></path><path d="M376 326h0l1-1c7 6 14 7 23 7-3 1-6 2-10 2-2-1-4-1-6-2v-1c-3-1-5-3-8-5z" class="M"></path><defs><linearGradient id="U" x1="415.239" y1="305.731" x2="413.113" y2="331.464" xlink:href="#B"><stop offset="0" stop-color="#4d515d"></stop><stop offset="1" stop-color="#7d8082"></stop></linearGradient></defs><path fill="url(#U)" d="M415 300v-1c5 5 6 10 6 17 0 3-1 7-3 10l-2 2c-2 0-5 4-7 5l-1 1-2 1c-1 0-1-1-2 0-1 0 0 0-1-1h0l2-2 2-2 1-1c4-3 8-8 9-13h0c1-6 0-11-2-16z"></path><defs><linearGradient id="V" x1="658.489" y1="351.79" x2="633.061" y2="348.082" xlink:href="#B"><stop offset="0" stop-color="#0b0b0c"></stop><stop offset="1" stop-color="#52555c"></stop></linearGradient></defs><path fill="url(#V)" d="M653 335c1 0 2-1 3-1 1 1 1 1 2 1v-3c1 4-1 9 0 13 0 1 0 3-1 4h1c0 4-1 11 1 13v5h-1v-3l-1 1s-1 0-1-1 0-3-1-4c-2-1-4-2-5-2l3 2c1 1 1 2 2 3 0 2 0 4-1 5l-1-1s0-1-1-1l-2-2c-1-1-3-1-4-3h-2c-3-1-5-2-8-2s-5 0-7 1c0-1 1-1 1-1h1l-1-1c0-1 2-1 3-1-1-1-1-1-2-1 1 0 1-1 2 0h1 1c0-2-1-5-2-7h0c1 0 2 0 3-1h-1 0l6-2c2 1 3 0 4 0l6-3c1-1 2-2 3-2 0-1 1-2 2-3h0c1 0 1-1 1-1l-1-1-3-1z"></path><path d="M638 358h-2c2-1 5-2 7-2h1s0 1 1 1c1 1 3 1 4 2l1-1 3 2c1 1 1 2 2 3 0 2 0 4-1 5l-1-1s0-1-1-1l-2-2c-1-1-3-1-4-3h-2c-3-1-5-2-8-2h2v-1z" class="H"></path><path d="M643 359c4 1 5 1 8 4l-1 1c-1-1-3-1-4-3h0c0-1-1-2-3-2z" class="a"></path><path d="M638 358h4l1 1c2 0 3 1 3 2h0-2c-3-1-5-2-8-2h2v-1z" class="E"></path><path d="M607 277v1c-1 2-3 3-5 4-3 2-5 3-7 5l1 1 3-3v1h0c-1 0-1 1-1 2-6 6-9 11-11 19v9c-1-1-2-1-2-2l-1 5h0l-1 1c1 5 4 13 4 19h-2c0 1-1 1-1 2-3-4-5-9-6-14h0c-2-10-1-20 3-28 5-11 14-18 26-22z" class="Y"></path><path d="M581 311c0 2 1 8 2 9 1 5 4 13 4 19h-2c-3-10-5-18-4-28z" class="L"></path><path d="M585 300c0 2-1 5-1 7 1 0 2-1 3-1v1 9c-1-1-2-1-2-2l-1 5h0l-1 1c-1-1-2-7-2-9 1-4 2-7 4-11z" class="D"></path><path d="M584 307c1 0 2-1 3-1v1 9c-1-1-2-1-2-2l-1 5c-1-4 0-8 0-12z" class="C"></path><path d="M595 287l1 1 3-3v1h0c-1 0-1 1-1 2-6 6-9 11-11 19v-1c-1 0-2 1-3 1 0-2 1-5 1-7h0c2-5 6-9 10-13z" class="V"></path><defs><linearGradient id="W" x1="611.261" y1="562.163" x2="549.873" y2="580.889" xlink:href="#B"><stop offset="0" stop-color="#171719"></stop><stop offset="1" stop-color="#646a71"></stop></linearGradient></defs><path fill="url(#W)" d="M607 537l7 3c-2 0-6-1-8 0l-3 2h-2-1c3 2 7 3 8 5-7-1-13-2-20-1h0c-12 3-21 12-27 21-3 5-6 12-4 16 1 7 3 10 9 13 4 2 8 3 12 4v2h-6l-4-1 2 1v1c-3-1-6-3-9-4v-1c-3-1-7-5-9-8h-1l1-2c2-15 8-32 22-40v-1c5-2 11-5 16-6h2c1-1 1-1 1-2 5-2 10 1 14-2z"></path><path d="M561 598l1-1 10 5-4-1 2 1v1c-3-1-6-3-9-4v-1z" class="b"></path><path d="M644 404c0-4 0-8-1-12 0-2-3-6-3-8 5 7 6 14 5 21l-2 16v1c-1 6-4 11-7 16-2 5-6 8-9 12-9 12-22 23-32 34h2c1-2 2-2 3-3-3 4-6 7-9 10-3 2-5 5-8 7l-5 5c0 1-1 1-2 2l-3 3-5 6v-1c1-3 3-6 5-9v-1l5-7 6-6c4-5 9-10 15-15l20-20h0c3-3 6-7 8-10 10-12 17-26 17-41z" class="U"></path><path d="M595 484h2c1-2 2-2 3-3-3 4-6 7-9 10-3 2-5 5-8 7l-5 5c0 1-1 1-2 2l-3 3 1-2c2-4 5-6 8-9l13-13z" class="a"></path><path d="M584 535c9-1 15 0 23 2-4 3-9 0-14 2 0 1 0 1-1 2h-2c-5 1-11 4-16 6v1c-14 8-20 25-22 40l-1 2c-1-1-2-1-2-2h0l1-7c-1-1-1-3-1-4l6-18c3-6 7-11 10-15l5-5v1 2h1l3-3 4-3c2 0 4 0 6-1z" class="E"></path><defs><linearGradient id="X" x1="554.109" y1="551.399" x2="561.391" y2="566.101" xlink:href="#B"><stop offset="0" stop-color="#626467"></stop><stop offset="1" stop-color="#727782"></stop></linearGradient></defs><path fill="url(#X)" d="M565 544c1 3-2 6-2 9-7 9-10 17-13 28-1-1-1-3-1-4l6-18c3-6 7-11 10-15z"></path><defs><linearGradient id="Y" x1="589.095" y1="548.77" x2="576.743" y2="534.176" xlink:href="#B"><stop offset="0" stop-color="#303138"></stop><stop offset="1" stop-color="#5c6166"></stop></linearGradient></defs><path fill="url(#Y)" d="M584 535c9-1 15 0 23 2-4 3-9 0-14 2-12 1-21 5-30 14 0-3 3-6 2-9l5-5v1 2h1l3-3 4-3c2 0 4 0 6-1z"></path><path d="M602 310h1 2c0 4 0 7 1 11s6 9 9 11 6 3 10 4l2 1c2 0 3 0 4 1l1 1h1 1c2 1 5 2 8 1v1c2 0 4-1 6-1 3-1 5-3 8-2-1 1-2 2-2 3-1 0-2 1-3 2l-6 3c-1 0-2 1-4 0-3-1-10 0-15-1h-6c-5-1-10-3-15-5-1 0-3-1-4-2 0-3 0-5 1-9 1-1 2-1 3-2h1c-2-5-5-12-4-17z" class="D"></path><path d="M605 327c1 1 1 2 2 3 4 5 8 12 13 15-5-1-10-3-15-5-1 0-3-1-4-2 0-3 0-5 1-9 1-1 2-1 3-2z" class="I"></path><path d="M602 329c2 3 3 8 3 11-1 0-3-1-4-2 0-3 0-5 1-9z" class="K"></path><path d="M626 345c-1-1-2-2-3-2l-1-1h-2l-1-1c0-1-1-1-1-2h-1c-1-2-4-4-5-7h1c2 2 4 3 6 4s4 2 7 2v-1l-1-1 2 1c2 0 3 0 4 1l1 1h1 1c2 1 5 2 8 1v1c2 0 4-1 6-1 3-1 5-3 8-2-1 1-2 2-2 3-1 0-2 1-3 2l-6 3c-1 0-2 1-4 0-3-1-10 0-15-1z" class="T"></path><path d="M619 336c2 1 4 2 7 2v1c1 2 6 3 9 4 2 1 4 1 6 1-8 2-13-1-19-5-2-1-3-1-3-3z" class="b"></path><path d="M625 336l2 1c2 0 3 0 4 1l1 1h1 1c2 1 5 2 8 1v1c2 0 4-1 6-1 3-1 5-3 8-2-1 1-2 2-2 3-1 0-2 1-3 2-2-1-3-1-5-1v1h-1l-4 1c-2 0-4 0-6-1-3-1-8-2-9-4v-1-1l-1-1z" class="H"></path><path d="M625 336l2 1c2 0 3 0 4 1l-5 1v-1-1l-1-1z" class="R"></path><path d="M633 339h1c2 1 5 2 8 1v1c2 0 4-1 6-1 3-1 5-3 8-2-1 1-2 2-2 3l-2-1h-2l-1 1-9 2c-3-1-7-2-10-4h2 1z" class="E"></path><defs><linearGradient id="Z" x1="563.393" y1="626.061" x2="551.804" y2="637.079" xlink:href="#B"><stop offset="0" stop-color="#070708"></stop><stop offset="1" stop-color="#2c2d31"></stop></linearGradient></defs><path fill="url(#Z)" d="M545 613c1-1 2-1 2-2 1 0 1 0 2-1l1 1h1l1 3 1 2 3 1 3 11h3l3 6h1c3 2 6 2 9 5v4l1 1v2l-3 5c-2 0-3-2-5-3-3-3-6-5-8-7-5-3-8-5-12-7l-1-1c-1-2 0-1-1-2v-1h-1 0l-1-10 1-7h0z"></path><path d="M567 637l3 8c-3-1-6-5-7-8l1 1h1 0c1 0 1 0 2-1z" class="a"></path><path d="M553 616l3 1 3 11v2c-3-4-4-9-6-14z" class="W"></path><path d="M562 628l3 6 2 3c-1 1-1 1-2 1h0-1l-1-1-4-7v-2h3z" class="H"></path><path d="M565 634h1c3 2 6 2 9 5v4l1 1v2l-3 5c-2 0-3-2-5-3-3-3-6-5-8-7 4 1 7 5 11 8l-1-1v-1-2h0l-3-8-2-3z" class="U"></path><path d="M153 291c1-2 2-3 3-5 4-6 9-12 15-17 1-1 3-3 5-3l1-1c1-1 2-1 3-1-3 3-7 5-11 9v2l-6 9 1 1-2 5c-1 2-1 3 0 5-2 8-3 15-2 23 0 1 1 3 1 3 0 4 1 7 2 10 1 2 3 5 3 6l-2 1c-1-1-3-2-4-3-7-6-10-15-11-24 0-5 0-9 2-13v-2c0-1 0-2 1-4 1 0 1-1 1-1z" class="Y"></path><defs><linearGradient id="a" x1="152.135" y1="293.9" x2="158.522" y2="303.039" xlink:href="#B"><stop offset="0" stop-color="#86898d"></stop><stop offset="1" stop-color="#a8aaaf"></stop></linearGradient></defs><path fill="url(#a)" d="M152 306h0c1-13 9-25 17-33v2l-6 9 1 1-2 5c-2 2-3 4-3 6v2c0 4-1 6-4 8-1 0-1 1-3 0z"></path><path d="M159 296h-2v-1c1-4 3-9 6-11l1 1-2 5c-2 2-3 4-3 6z" class="T"></path><defs><linearGradient id="b" x1="159.363" y1="293.717" x2="155.603" y2="315.5" xlink:href="#B"><stop offset="0" stop-color="#96969c"></stop><stop offset="1" stop-color="#c7ccce"></stop></linearGradient></defs><path fill="url(#b)" d="M159 296c0-2 1-4 3-6-1 2-1 3 0 5-2 8-3 15-2 23 0 1 1 3 1 3 0 4 1 7 2 10-2-1-2-1-3-2-1-2-1-1-1-2v-2h-1v-3h-1v-3c-3-4-5-8-5-13 2 1 2 0 3 0 3-2 4-4 4-8v-2z"></path><defs><linearGradient id="c" x1="378.823" y1="332.413" x2="378.139" y2="337.894" xlink:href="#B"><stop offset="0" stop-color="#55595f"></stop><stop offset="1" stop-color="#6b7073"></stop></linearGradient></defs><path fill="url(#c)" d="M362 325c0-2 0-5 1-8l1 3c3 0 6 2 8 3l1 1c0 1 2 2 3 2 3 2 5 4 8 5v1c2 1 4 1 6 2 4 0 7-1 10-2l7-2-2 2-2 2h0c1 1 0 1 1 1 1-1 1 0 2 0l2-1 1-1c2-1 5-5 7-5l2-2 1 1 1-1 1 1h-1c-1 1-1 2-2 3-3 6-13 11-20 13-5 1-13 0-18 0-4-4-10-3-14-7v-1c-2-1-2-2-4-3v-7z"></path><path d="M362 325c0-2 0-5 1-8l1 3 2 2c0 2-1 2 0 3l2 3c-1 1-4 2-5 3-1-1-1-4-1-6z" class="E"></path><path d="M364 320c3 0 6 2 8 3l1 1c0 1 2 2 3 2 3 2 5 4 8 5v1c-2 0-6-2-8-1-4-2-7-5-10-9l-2-2z" class="B"></path><path d="M406 335c-1 1-2 1-3 2l-5 1-1 1h-3c-2 0-4 0-6-1h-1c-2 0-3 0-4-1-3-1-6-2-8-4-2-1-3-2-5-4l1-1 3 3h2c2-1 6 1 8 1 2 1 4 1 6 2 4 0 7-1 10-2l7-2-2 2-2 2h0c1 1 0 1 1 1 1-1 1 0 2 0zm12-9l1 1 1-1 1 1h-1c-1 1-1 2-2 3-3 6-13 11-20 13-5 1-13 0-18 0-4-4-10-3-14-7 2-1 4-2 6 0 3 1 6 3 10 4s14 2 18-1c4-1 7-3 10-5l6-6 2-2z" class="P"></path><defs><linearGradient id="d" x1="564.17" y1="515.904" x2="544.232" y2="520.504" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#28282a"></stop></linearGradient></defs><path fill="url(#d)" d="M568 495v1c0 3-1 5-2 7 1 0 2-1 3-2 0 1-1 2-1 3v2c-1 4-2 9-3 13v1 3c-1 1-1 1-1 2s-1 2-2 3l-2 4c-1 2-4 8-5 9l-1-1c-1 2-2 3-3 4l3-9c0 1-1 1-1 2l-2-2 1-1-2-1c-1-2-3-3-2-6-3-3-7-8-6-12l1-3c1 0 2 0 3-1l1 2c1 1 0 0 2 1v-1c1-2 0-4-1-6h3l-2-2c0-1 0-1-1-1-1-1-1-2-1-4h1c5 1 9 0 14-1h1c2-1 3-1 5-4z"></path><path d="M546 511l1 2c-1 2-2 3-3 5l-2-2v-1l1-3c1 0 2 0 3-1z" class="U"></path><path d="M565 507l-3 12-1 3h-1c0-2 1-3 1-5-3 2-3 6-6 8 1-5 4-9 7-14 1-1 1-2 3-4z" class="a"></path><path d="M555 525c3-2 3-6 6-8 0 2-1 3-1 5h1l-1 3h1c0 2-1 3-1 4h0v3c-1 2-4 8-5 9l-1-1c-1 2-2 3-3 4l3-9c0 1-1 1-1 2l-2-2 1-1-2-1c-1-2-3-3-2-6l4 4 3-6z" class="E"></path><path d="M560 525h1c0 2-1 3-1 4h0v3c-1 2-4 8-5 9l-1-1c-1 2-2 3-3 4l3-9 6-10z" class="T"></path><path d="M568 495v1c0 3-1 5-2 7 1 0 2-1 3-2 0 1-1 2-1 3v2c-1 4-2 9-3 13v1 3c-1 1-1 1-1 2s-1 2-2 3l-2 4v-3h0c0-1 1-2 1-4h-1l1-3 1-3 3-12v-3c-3 2-7 4-10 4-2 0-3 0-4-1l-2-2c0-1 0-1-1-1-1-1-1-2-1-4h1c5 1 9 0 14-1h1c2-1 3-1 5-4z" class="b"></path><path d="M562 519l1 2c0 2 0 4-1 5v1 1l-2 4v-3h0c0-1 1-2 1-4h-1l1-3 1-3z" class="O"></path><path d="M548 500c5 1 9 0 14-1v1c-2 3-4 4-7 6l-6-1c0-1 0-1-1-1-1-1-1-2-1-4h1z" class="R"></path><path d="M624 376l12-2c2 1 3 2 4 4s1 3 2 5l-1 1h-1c0 2 3 6 3 8 1 4 1 8 1 12 0 15-7 29-17 41-2 3-5 7-8 10h0 0c0-1 0-1 1-2 0-3 3-4 4-7h-1l1-4c-1 1-1 2-2 2-1-1 0-4 0-5l1-1 1 1c1-2 3-4 3-6v-2l1-1c4-3 5-8 5-13h0c1-3 3-10 1-13 0-1 1-6 1-7 0-2-1-4-1-6v-1c-2-7-7-7-12-10-1-1-1-1-1-3 1-1 2-1 3-1z" class="W"></path><path d="M624 376l12-2c2 1 3 2 4 4s1 3 2 5l-1 1-2-1c-3-3-7-6-12-6-1 0-2 0-3-1z" class="X"></path><path d="M624 442l1-1h1c5-8 8-16 10-25 1-4 0-7 1-11 0-5 2-11 1-16 0-2-1-3-1-5 3 4 5 7 5 11 1 3 1 6 2 8v1c0 15-7 29-17 41-2 3-5 7-8 10h0 0c0-1 0-1 1-2 0-3 3-4 4-7h-1l1-4zm-85 33h1c1-1 2-2 4-3 3-2 7-2 11 0h2 1c1 3 1 5 0 8-1 7-6 15-10 20h-1c0 2 0 3 1 4 1 0 1 0 1 1l2 2h-3c1 2 2 4 1 6v1c-2-1-1 0-2-1l-1-2c-1 1-2 1-3 1h0-1-1c-2-3-4-7-5-11-2-2-2-4-5-6h1v-1c2-2 2-5 2-8l1-2v-1c1-3 3-5 4-8z" class="E"></path><path d="M555 472h2l1 3c-1 1-1 1-2 1-2 2-7 15-9 15 1-3 3-7 4-10 1-2 2-3 2-5h0-2-1c1-1 3-1 5-1h1l1-1-2-2z" class="Z"></path><path d="M551 476h2 0c0 2-1 3-2 5-1 3-3 7-4 10-1 2-1 6-1 8 0 1 0 1 1 1 0 2 0 3 1 4 1 0 1 0 1 1l2 2h-3l-6-12c1 0 1-1 1-2 1-3 4-7 5-10 1-1 1-2 1-3h1c0-2 0-2 1-4z" class="H"></path><path d="M550 476h1c-1 2-1 2-1 4h-1c0 1 0 2-1 3-1 3-4 7-5 10 0 1 0 2-1 2l-3-6h1c0-1 1-1 1-2 2-5 5-8 9-11z" class="E"></path><path d="M539 475h1c1-1 2-2 4-3 3-2 7-2 11 0l2 2-1 1h-1c-2 0-4 0-5 1-4 3-7 6-9 11 0 1-1 1-1 2h-1 0c-1-2-2-5-4-5v-1c1-3 3-5 4-8z" class="G"></path><path d="M535 484c2 0 3 3 4 5h0l3 6 6 12c1 2 2 4 1 6v1c-2-1-1 0-2-1l-1-2c-1 1-2 1-3 1h0-1-1c-2-3-4-7-5-11-2-2-2-4-5-6h1v-1c2-2 2-5 2-8l1-2z" class="B"></path><path d="M532 495v-1c2-2 2-5 2-8l2 6c1 5 6 10 8 15 0 1 1 3 2 4-1 1-2 1-3 1h0-1-1c-2-3-4-7-5-11-2-2-2-4-5-6h1z" class="H"></path><path d="M446 453c0-4 5-11 7-14s7-9 11-10c1 0 1 1 2 1 0 3-3 7-4 10l6 6c0 1-4 5-5 6-4 5-6 10-9 16 0 3-1 5-2 8v1c1 5 3 11 5 16 0 1 1 3 2 4 3 1 7 1 10 0 5-1 10-5 12-10l4-7h0l1-2 3 3c-1 1-2 2-2 4-1 1-1 3-2 4s-1 2-1 3l-1 2c-1 1-2 3-2 5-2 1-3 2-5 2l-3 3-1-1c-2-1-5-1-7-2s-4-2-6-2h-1s0-1-1-1h0l-2-2-1-3v-2l-3-8-1-2c0-2 0-3-1-5 0-2 0-4-1-6 0-2 0-5-1-7-1-3 0-7-1-10z" class="F"></path><path d="M486 478l3 3c-1 1-2 2-2 4-1 1-1 3-2 4 1-3 1-6 0-9h0l1-2z" class="E"></path><path d="M456 447l3-4c1 1 3 2 4 4h0c-1 2-1 3 0 5-4 5-6 10-9 16 0 3-1 5-2 8v1l-1-6c-1-9 0-17 5-24z" class="L"></path><path d="M456 447l3-4c1 1 3 2 4 4h0c-1 2-1 3 0 5-4 5-6 10-9 16-1-4 2-10 4-13 1-3 3-5 2-8-2-1-2 0-4 0z" class="M"></path><defs><linearGradient id="e" x1="170.321" y1="311.618" x2="138.382" y2="311.311" xlink:href="#B"><stop offset="0" stop-color="#d2d3d4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#e)" d="M150 275l3-2h0v1h0c-1 1-1 1-1 2v2h0c-1 1-3 4-3 6-3 6-2 13-4 19l1 2c0-1 1-2 1-2v-2h1v1c-1 1-1 3 0 4v1c0-3 1-7 3-9-2 4-2 8-2 13 1 9 4 18 11 24 1 1 3 2 4 3 3 2 5 3 9 3h0l3 1c4 2 7 3 11 4h-2v1h0-2l-8-1h-1-2-2l-1 2h1l-1 1c-6-1-13-5-17-10-10-8-14-20-14-32-1-5 0-10 1-15 1-3 2-6 4-10 1-1 1 0 0-1v-1l6-6 1 1z"></path><path d="M145 303l1 2c-1 3-2 8 0 11-1 1-1 0-2 0h0c-1-5-1-9 1-13z" class="Y"></path><path d="M139 292c1 1 2 1 3 1-1 3-2 7-2 11h-1v-6c0 2 0 2-1 4v5c-1-5 0-10 1-15z" class="C"></path><path d="M149 274l1 1c-2 2-2 4-3 6-2 4-4 8-5 12-1 0-2 0-3-1 1-3 2-6 4-10 1-1 1 0 0-1v-1l6-6z" class="V"></path><path d="M146 316c0 1 1 2 1 3 1 4 3 12 8 15h2l2 2h-1c-1 1-2 1-3 1-7-4-9-14-11-21 1 0 1 1 2 0z" class="S"></path><path d="M148 307c0-3 1-7 3-9-2 4-2 8-2 13 1 9 4 18 11 24 1 1 3 2 4 3 3 2 5 3 9 3h0l3 1c4 2 7 3 11 4h-2v1h0-2l-8-1c-6-2-10-6-16-10l-2-2h-2c-5-3-7-11-8-15 0-1-1-2-1-3-2-3-1-8 0-11 0-1 1-2 1-2v-2h1v1c-1 1-1 3 0 4v1z" class="D"></path><path d="M146 305c0-1 1-2 1-2v-2h1v1c-1 1-1 3 0 4v1c0 3 1 9-1 12 0-1-1-2-1-3-2-3-1-8 0-11z" class="I"></path><defs><linearGradient id="f" x1="616.038" y1="335.7" x2="603.2" y2="352.21" xlink:href="#B"><stop offset="0" stop-color="#434950"></stop><stop offset="1" stop-color="#909396"></stop></linearGradient></defs><path fill="url(#f)" d="M584 319l1-5c0 1 1 1 2 2 1 8 4 16 10 20l4 2c1 1 3 2 4 2 5 2 10 4 15 5h6c5 1 12 0 15 1l-6 2h0 1c-1 1-2 1-3 1h0c1 2 2 5 2 7h-1-1c-1-1-1 0-2 0 1 0 1 0 2 1-1 0-3 0-3 1l1 1h-1s-1 0-1 1h-16c-7-1-15-4-21-9-3-3-6-6-8-10 0-1 1-1 1-2h2c0-6-3-14-4-19l1-1h0z"></path><path d="M592 351l1-1c-2-1-4-3-4-5 0-1 1-1 1-2h1c3 2 6 6 10 8 8 4 19 8 29 7l1 1h-1s-1 0-1 1h-16c-7-1-15-4-21-9z" class="O"></path><path d="M584 319l1-5c0 1 1 1 2 2 1 8 4 16 10 20l4 2c1 1 3 2 4 2 5 2 10 4 15 5h6c5 1 12 0 15 1l-6 2h0c-12 1-27-1-37-8-7-4-12-12-14-21h0z" class="J"></path><defs><linearGradient id="g" x1="609.642" y1="527.679" x2="564.601" y2="520.703" xlink:href="#B"><stop offset="0" stop-color="#18191d"></stop><stop offset="1" stop-color="#47494d"></stop></linearGradient></defs><path fill="url(#g)" d="M614 506c-1 4-2 9-6 11 0 1 0 0-1 1h-1-1l1 1c1-1 5-2 7-2 0-1 1-2 1-3l1-1v-2l1-1v-3c0-1 1-2 1-3v-3c0-1 1-1 1-1v-6h-1c0-2 0-4-1-5 0-1 0-2 1-3v-1c0-1 1-2 2-3h1 0 1c0-1 1-1 1-1h1c-2 2-4 2-6 5v4c1 3 3 9 1 12v2l-1 1v1 1c0 2-3 8-3 9s1 1 0 2l-1 1c-1 2-1 2-3 3h0c-1 1-1 1-2 1h-2c0 1-1 1-2 1h-1-1c-6 1-14 6-18 10v1c-2 1-4 1-6 1l-4 3-3 3h-1v-2-1l-5 5c-3 4-7 9-10 15l-6 18c0 1 0 3 1 4l-1 7c-2-1-2-1-3-2h0l-3-3c-1-6 6-20 9-26 0-4 2-6 4-10 2-2 3-5 5-7l1-1c1-1 1-3 2-4 2-2 3-3 5-6 1-2 4-4 5-6l-1-1-3 3c1-2 4-4 5-7 0-1 9-12 11-13l6 6c4 1 9 2 12 0 4-1 6-4 8-7h1c0-1 0-1 1-2h0l1-2c0 2-1 4-1 6z"></path><path d="M570 539h0c2-4 4-8 8-10 1-1 4-2 5-2h1l-1 1c1 2-3 6-5 8l-4 3-3 3h-1v-2-1z" class="W"></path><path d="M574 539v-1c3-4 6-7 9-10 1 2-3 6-5 8l-4 3z" class="U"></path><defs><linearGradient id="h" x1="602.529" y1="513.865" x2="589.471" y2="539.135" xlink:href="#B"><stop offset="0" stop-color="#020002"></stop><stop offset="1" stop-color="#2d3132"></stop></linearGradient></defs><path fill="url(#h)" d="M614 516c0 1 1 1 0 2l-1 1c-1 2-1 2-3 3h0c-1 1-1 1-2 1h-2c0 1-1 1-2 1h-1-1c-6 1-14 6-18 10v1c-2 1-4 1-6 1 2-2 6-6 5-8l1-1c3-1 5-3 9-4s7-2 11-3c3 0 5-1 8-1h1l1-3z"></path><path d="M552 557h0c4-4 6-10 11-14-1 2-1 3-2 5l-6 9v2l-6 18c0 1 0 3 1 4l-1 7c-2-1-2-1-3-2h0l-3-3c-1-6 6-20 9-26z" class="O"></path><path d="M549 577c0 1 0 3 1 4l-1 7c-2-1-2-1-3-2h0v-1l2-7 1-1z" class="R"></path><path d="M548 578l-1 1c-1-2 0-6 1-8l2-5 1-1 1-1v-2c1-2 2-4 3-5v2l-6 18-1 1z" class="b"></path><defs><linearGradient id="i" x1="442.258" y1="329.102" x2="403.274" y2="324.74" xlink:href="#B"><stop offset="0" stop-color="#838789"></stop><stop offset="1" stop-color="#b0b2b8"></stop></linearGradient></defs><path fill="url(#i)" d="M435 293c4 5 8 10 9 18h0c2 9-1 19-4 27 1 1 2 1 3 1-1 3-3 6-6 8-2 2-4 4-6 5-6 5-18 8-26 7h-3l3-1c-5-1-9-1-14-1l3-1h0l1-2h0-2c0-1 1-1 2-2l2-2h-5l-3-2c-3 0-6-1-9-2l-4-1c-5-1-9-4-11-8l1-1c4 4 10 3 14 7 5 0 13 1 18 0 7-2 17-7 20-13 1-1 1-2 2-3h1c2 1 3 1 4 3v2 5c6-3 10-8 12-15 3-10 1-20-4-28l1-1h1z"></path><path d="M389 348c2 0 7 0 8 1 2 1 4 0 6 0 0 2-2 3-3 5v1c2 1 2 1 3 1h2c2 0 7-2 9-3v1h0 2 0c1 0 1-1 2-1 0 0 1-1 2-1h0s1-1 2-1h-1 2c-6 3-11 6-18 7-5-1-9-1-14-1l3-1h0l1-2h0-2c0-1 1-1 2-2l2-2h-5l-3-2z" class="I"></path><path d="M423 351l6-3c2-2 6-7 8-8 0 2 0 2-1 3 0 2 0 3 1 4-2 2-4 4-6 5-6 5-18 8-26 7h-3l3-1c7-1 12-4 18-7z" class="T"></path><path d="M431 352v-1c0-2 4-6 5-8 0 2 0 3 1 4-2 2-4 4-6 5z" class="R"></path><defs><linearGradient id="j" x1="404.163" y1="306.522" x2="405.377" y2="347.295" xlink:href="#B"><stop offset="0" stop-color="#d5d5d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#j)" d="M433 294l1-1c5 10 9 19 6 31-3 7-8 13-14 17-9 4-19 7-29 8-1-1-6-1-8-1-3 0-6-1-9-2l-4-1c-5-1-9-4-11-8l1-1c4 4 10 3 14 7 5 0 13 1 18 0 7-2 17-7 20-13 1-1 1-2 2-3h1c2 1 3 1 4 3v2 5c6-3 10-8 12-15 3-10 1-20-4-28z"></path><path d="M421 327c2 1 3 1 4 3v2h-1c-1 0-1 1-1 2-2 4-4 6-8 7h0c-11 4-25 6-35 2 5 0 13 1 18 0 7-2 17-7 20-13 1-1 1-2 2-3h1z" class="I"></path><path d="M543 583l3 3h0c1 1 1 1 3 2h0c0 1 1 1 2 2h1c0 4-2 9-1 13l1 1c0 3 0 5-1 7h-1l-1-1c-1 1-1 1-2 1 0 1-1 1-2 2h0l-1 7 1 10c0 2-1 3-2 4 0-1-1-2-2-3l-1 1v1l-1 1-5 7c-3 5-6 9-8 14 0 1-1 2-1 3-1 3-4 9-3 12-2 4-2 9-3 14h0v8h0v-8l-2-1c0-2 0-3-1-5l-1-1 2-17c1-6 2-13 6-18 2-2 4-4 5-6h0c0-2 0-3-1-5l-1-5c-2-9-5-19 0-27 2-3 5-4 8-6 2-1 4-1 5-3l1 1v5 1h0c0 2 0 2-1 3v1 1 4l-1 1v4 2c0 1 0 1-1 2v2 2l-1 2v2c-1 1-1 2-1 4-1 1-1 2-2 4v1l-1 1h0c1 0 1-1 1-1l1-1c1-1 1-3 1-4v-1c1-1 1-2 2-3v-3h0c0-2 0-2 1-3 0-2-1-2 0-3 0-2 0-3 1-4v-3-1c0-1 0-2 1-3v-3l1-1h-1c0-2 0-2 1-3h0v-1c0-1 1-3 1-5v-6l1-1z" class="U"></path><path d="M541 595l1 1v1 6l-1 2h1v11l-3 9c0 2-1 4-2 5s-2 3-3 4l3-9c2-9 3-19 4-29v-1z" class="O"></path><path d="M542 605v11l-2 1c-1-3 1-9 1-12h1z" class="b"></path><path d="M534 609c0-4 2-7 3-11h2c-2 9-3 32-10 38h-1 0c0-2 0-3-1-5 1-1 1-2 1-4l1 1c1-2 2-5 2-7 1-4 1-7 1-11h0l1-2 1 1z" class="D"></path><path d="M533 608l1 1-3 16c0 4-1 7-3 11 0-2 0-3-1-5 1-1 1-2 1-4l1 1c1-2 2-5 2-7 1-4 1-7 1-11h0l1-2z" class="H"></path><path d="M534 634c1-1 2-3 3-4l-1 5c0 2-1 3-2 5v1c-3 5-6 9-8 14 0 1-1 2-1 3-1 3-4 9-3 12-2 4-2 9-3 14h0v8h0v-8l-2-1c0-2 0-3-1-5 0-6 3-12 4-17 2-4 3-8 5-11 1-3 4-7 5-9 1-3 3-5 4-7z" class="R"></path><defs><linearGradient id="k" x1="523.222" y1="604.104" x2="535.09" y2="611.266" xlink:href="#B"><stop offset="0" stop-color="#050403"></stop><stop offset="1" stop-color="#1e2026"></stop></linearGradient></defs><path fill="url(#k)" d="M539 590l1 1v5c-1 1-1 1-1 2h-2c-1 4-3 7-3 11l-1-1-1 2h0c0 4 0 7-1 11 0 2-1 5-2 7l-1-1c0 2 0 3-1 4l-1-5c-2-9-5-19 0-27 2-3 5-4 8-6 2-1 4-1 5-3z"></path><path d="M527 617c1 2 1 4 1 6h0v4c0 2 0 3-1 4l-1-5c1-2 1-7 1-9z" class="E"></path><path d="M527 617c-1-6 0-11 3-16 1-1 3-4 5-4l1 1c2-2 2-5 4-7v5c-1 1-1 1-1 2h-2c-1 4-3 7-3 11l-1-1-1 2h0c0 4 0 7-1 11 0 2-1 5-2 7l-1-1v-4h0c0-2 0-4-1-6z" class="a"></path><path d="M528 623c1-1 0-3 0-4 0-4 1-10 2-14 1-1 1-2 3-3 1 2 0 4 0 6l-1 2h0c0 4 0 7-1 11 0 2-1 5-2 7l-1-1v-4z" class="b"></path><path d="M531 621h-1c0-2-1-3 0-4v-2c0-2 1-4 1-6l1 1c0 4 0 7-1 11z" class="O"></path><defs><linearGradient id="l" x1="552.585" y1="593.372" x2="536.406" y2="610.137" xlink:href="#B"><stop offset="0" stop-color="#50595f"></stop><stop offset="1" stop-color="#77757b"></stop></linearGradient></defs><path fill="url(#l)" d="M543 583l3 3h0c1 1 1 1 3 2h0c0 1 1 1 2 2h1c0 4-2 9-1 13l1 1c0 3 0 5-1 7h-1l-1-1c-1 1-1 1-2 1 0 1-1 1-2 2h0l-1 7 1 10c0 2-1 3-2 4 0-1-1-2-2-3l-1 1v1l-1 1-5 7v-1c1-2 2-3 2-5l1-5c1-1 2-3 2-5l3-9v-11h-1l1-2v-6-1l-1-1c0-1 1-3 1-5v-6l1-1z"></path><path d="M545 613v-3c-1-4-1-9 2-12v5l-2 10h0z" class="O"></path><path d="M543 583l3 3h0c-1 1-1 1-1 2-1 1 0 3-1 5 0 4-1 8-2 12h-1l1-2v-6-1l-1-1c0-1 1-3 1-5v-6l1-1z" class="W"></path><path d="M540 632h-1c0-2 0-4 1-6s2-4 4-6l1 10c0 2-1 3-2 4 0-1-1-2-2-3l-1 1z" class="M"></path><path d="M549 588c0 1 1 1 2 2h1c0 4-2 9-1 13l1 1c0 3 0 5-1 7h-1l-1-1c-1 1-1 1-2 1 0 1-1 1-2 2l2-10v-5c1-3 1-7 2-10z" class="U"></path><path d="M547 603l1 1 1-1c1 2 2 3 2 5 0-1-1-3 0-5l1 1c0 3 0 5-1 7h-1l-1-1c-1 1-1 1-2 1 0 1-1 1-2 2l2-10z" class="Z"></path><path d="M549 610c-1-1-1-1-1-4l1-1h0l1 6-1-1z" class="H"></path><defs><linearGradient id="m" x1="579.839" y1="887.898" x2="518.516" y2="791.875" xlink:href="#B"><stop offset="0" stop-color="#263a4d"></stop><stop offset="1" stop-color="#586a74"></stop></linearGradient></defs><path fill="url(#m)" d="M582 754c1-1 2-3 3-4 2 0 3-2 4-3v2c0 1 1 2 2 3h0l-67 157-11 22c-1 2-2 4-4 6-1-3 1-8 2-10l4-20 67-153z"></path><defs><linearGradient id="n" x1="605.432" y1="471.046" x2="590.886" y2="460.873" xlink:href="#B"><stop offset="0" stop-color="#2f3035"></stop><stop offset="1" stop-color="#63676e"></stop></linearGradient></defs><path fill="url(#n)" d="M616 425c4 3 6 7 7 13l-1 1c0 1-1 4 0 5 1 0 1-1 2-2l-1 4h1c-1 3-4 4-4 7-1 1-1 1-1 2h0l-20 20c-6 5-11 10-15 15l-6 6-5 7v1c-2 3-4 6-5 9v1c-1 2-2 5-3 6v-1c1-4 2-9 3-13v-2c0-1 1-2 1-3-1 1-2 2-3 2 1-2 2-4 2-7v-1c2-6 4-11 5-17 3-4 2-11 2-16v-3l2 3 1-1v-9c1-1 1-2 2-3 1 0 2 0 3-1s1-2 3-2c1 1 1 2 1 3 1 2 1 3 2 4h0c1-2 2-3 3-5h2 2v-5c1-1 1-2 2-2 1-2 3-3 5-3s3 2 4 3l2 4v1c2-1 3-2 4-3 0-2 0-5-1-7 0-1-1-4-1-5 1-2 2-3 3-4l1 1h1v-3z"></path><path d="M581 480l1 1-2 5c0 1-1 1-1 2l-1 1c-1 1-1 1-3 2l2-3c2-2 3-5 4-8z" class="O"></path><path d="M600 458h1 0l-2 4c-1 2-2 3-3 5l-1-1c0-1 0-2 1-2l1-3 3-3z" class="b"></path><path d="M596 443c2 1 3 2 4 3l1 1c-1 3-2 5-4 8l-3-7h2v-5z" class="E"></path><path d="M596 443c1-1 1-2 2-2 1-2 3-3 5-3s3 2 4 3c-3 2-4 3-5 6l-1 1v-1l-1-1c-1-1-2-2-4-3z" class="M"></path><path d="M613 443c0-2 0-5-1-7 0-1-1-4-1-5 1-2 2-3 3-4l1 1v1l1 6c0 6-1 14-4 19-1 1-2 1-3 2s-2 3-3 3c0 0 3-6 3-7-1-1 0-5 0-7v1c2-1 3-2 4-3z" class="D"></path><path d="M609 445v1c2-1 3-2 4-3-1 4-2 6-4 9-1-1 0-5 0-7z" class="U"></path><path d="M587 449c1 2 1 3 2 4h0c1-2 2-3 3-5h2l3 7c-1 5-8 7-9 12-1 1-3 3-4 5l3-12c0-4 1-7 0-11z" class="D"></path><defs><linearGradient id="o" x1="580.772" y1="483.622" x2="581.809" y2="464.382" xlink:href="#B"><stop offset="0" stop-color="#63666d"></stop><stop offset="1" stop-color="#a3a5a9"></stop></linearGradient></defs><path fill="url(#o)" d="M583 448c1-1 1-2 3-2 1 1 1 2 1 3 1 4 0 7 0 11l-3 12c-1 2-2 5-3 8s-2 6-4 8l3-11-1-1v-1c1-1 1-2 1-3h0v-4-2l-1 2c-1-2 0-4 0-6l-1-1v-9c1-1 1-2 2-3 1 0 2 0 3-1z"></path><defs><linearGradient id="p" x1="581.628" y1="463.4" x2="586.756" y2="447.403" xlink:href="#B"><stop offset="0" stop-color="#abadb1"></stop><stop offset="1" stop-color="#dbdbde"></stop></linearGradient></defs><path fill="url(#p)" d="M583 448c1-1 1-2 3-2 1 1 1 2 1 3 1 4 0 7 0 11-1 0-2 1-2 2-1 1 0 1-1 2h0v-3c0-3 0-5-1-7v-1-5z"></path><path d="M583 448v5c0 4 0 9-1 12s-1 9-2 12l-1-1v-1c1-1 1-2 1-3h0v-4-2l-1 2c-1-2 0-4 0-6l-1-1v-9c1-1 1-2 2-3 1 0 2 0 3-1z" class="W"></path><path d="M583 448v5c0 4 0 9-1 12v-1c-1-5-1-10-2-15 1 0 2 0 3-1z" class="D"></path><path d="M575 459l2 3 1-1 1 1c0 2-1 4 0 6l1-2v2 4h0c0 1 0 2-1 3v1l1 1-3 11-2 3c0 2-1 5-1 8 0 1-1 2-1 3v1 1c-2 3-4 6-5 9v1c-1 2-2 5-3 6v-1c1-4 2-9 3-13v-2c0-1 1-2 1-3-1 1-2 2-3 2 1-2 2-4 2-7v-1c2-6 4-11 5-17 3-4 2-11 2-16v-3z" class="a"></path><path d="M575 459l2 3 1-1c-1 6-1 11-2 17-1-2-1-4-1-6 1-3 1-7 0-10v-3z" class="X"></path><path d="M575 462c1 3 1 7 0 10 0 2 0 4 1 6-1 4-2 9-4 13-1 3-3 7-3 10-1 1-2 2-3 2 1-2 2-4 2-7v-1c2-6 4-11 5-17 3-4 2-11 2-16z" class="D"></path><path d="M825 252c5-1 11 2 15 4l2 1-1 1s2 1 3 2c0 0 4 3 5 4 4 3 9 7 13 12 1 0 2 2 3 2l-1-2v-1l3 5 2-1c1 1 2 3 3 3 3 6 5 16 4 23 0 12-4 24-12 33-8 8-19 12-30 12l-9-1c-5-1-12-4-16-9 3 2 6 4 10 5 7 1 13 1 19-3 8-5 13-15 16-24l1-7c-1-6-2-11-3-16h0c-5-11-12-19-23-24-4-2-9-3-14-4h-9l1-1c0-2 1-3 3-4-2-1-5-1-7 0h-2l7-10c1 1 1 1 2 1v1h1c0 1 0 3-1 3v2h3l3-3h-1v-1l2-1c-1 0 0 0-1-1h3l6-1z" class="J"></path><path d="M852 295v-1c1 2 2 3 3 5l2 2c1 2 2 3 3 5l2 2v-1c1 2 1 5 1 8l-1 1v1c-1-3-1-4-3-6-1-2-1-3-1-4-1-3-1-3-3-4h0v7 1c-1-6-2-11-3-16z" class="B"></path><path d="M869 279c1 1 2 3 3 3 3 6 5 16 4 23h-1v-5-1 6c-1-1-1-3-1-4 0-3 0-6-1-8v3c0-1-1-2-1-3v10h-1c-2-7 2-17-4-23l2-1z" class="Y"></path><path d="M864 293c3 10 4 19 1 29v3c-2 3-3 6-6 8-3 1-5 5-8 5 7-7 11-13 13-22 1-8 1-15-1-22l1-1z" class="T"></path><path d="M862 276c1 0 2 2 3 2l-1-2v-1l3 5c6 6 2 16 4 23 0 7-1 17-6 21v1-3c3-10 2-19-1-29l-1-4h1l3 7c0-3 0-9-1-12l-1-1c-1-2-2-5-3-7z" class="B"></path><path d="M844 260l5 4c4 3 9 7 13 12 1 2 2 5 3 7l1 1c1 3 1 9 1 12l-3-7h-1l1 4-1 1-2-4c-2-4-4-8-7-11-1-4-4-6-5-9-2-3-5-7-5-10z" class="M"></path><path d="M849 264c4 3 9 7 13 12 1 2 2 5 3 7l1 1c1 3 1 9 1 12l-3-7c0-4-3-8-5-11-3-5-8-9-10-14z" class="O"></path><defs><linearGradient id="q" x1="837.144" y1="267.429" x2="830.885" y2="280.35" xlink:href="#B"><stop offset="0" stop-color="#2e3034"></stop><stop offset="1" stop-color="#65686b"></stop></linearGradient></defs><path fill="url(#q)" d="M825 252c5-1 11 2 15 4l2 1-1 1s2 1 3 2c0 3 3 7 5 10 1 3 4 5 5 9 3 3 5 7 7 11l2 4c2 7 2 14 1 22 0-2 0-3-1-5h0v3 1c0-3 0-6-1-8v1l-2-2c-1-2-2-3-3-5l-2-2c-1-2-2-3-3-5v1h0c-5-11-12-19-23-24-4-2-9-3-14-4h-9l1-1c0-2 1-3 3-4-2-1-5-1-7 0h-2l7-10c1 1 1 1 2 1v1h1c0 1 0 3-1 3v2h3l3-3h-1v-1l2-1c-1 0 0 0-1-1h3l6-1z"></path><path d="M854 292h1c1 1 1 2 2 3 1-1 0-1 2-1h-1l2 2h0c0 2 0 2-1 3v-1c-1 0-1 0-2-1s-2-3-3-5z" class="R"></path><path d="M810 262c6 1 12 2 18 5 1 1 1 2 1 4-4-2-9-3-14-4h-9l1-1c0-2 1-3 3-4z" class="C"></path><path d="M828 267c10 4 18 11 24 20 0 1 2 5 2 5 1 2 2 4 3 5v4l-2-2c-1-2-2-3-3-5v1h0c-5-11-12-19-23-24 0-2 0-3-1-4z" class="I"></path><path d="M830 263c1-1 1-1 3-1 3 2 7 4 9 6 4 4 8 9 12 11 3 3 5 7 7 11l2 4c2 7 2 14 1 22 0-2 0-3-1-5h0v3 1c0-3 0-6-1-8v1l-2-2c-1-2-2-3-3-5v-4c1 1 1 1 2 1v1c1-1 1-1 1-3h0l-1-2c-5-15-16-25-29-31z" class="F"></path><path d="M860 296c2 4 2 8 2 11v1l-2-2c-1-2-2-3-3-5v-4c1 1 1 1 2 1v1c1-1 1-1 1-3z" class="D"></path><path d="M825 252c5-1 11 2 15 4l2 1-1 1s2 1 3 2c0 3 3 7 5 10 1 3 4 5 5 9-4-2-8-7-12-11-2-2-6-4-9-6-2 0-2 0-3 1-6-2-11-3-17-4l3-3h-1v-1l2-1c-1 0 0 0-1-1h3l6-1z" class="J"></path><path d="M819 253l6-1 7 2c0 1 0 1 1 2 1 0 1 0 2 1-5-1-9-3-14-3l-2-1z" class="C"></path><path d="M813 259l3-3h-1v-1l2-1c-1 0 0 0-1-1h3l2 1-2 1v1c3 3 11 1 14 5v1c-2 0-2 0-3 1-6-2-11-3-17-4z" class="Q"></path><path d="M825 252c5-1 11 2 15 4l2 1-1 1s2 1 3 2c0 3 3 7 5 10 1 3 4 5 5 9-4-2-8-7-12-11-2-2-6-4-9-6v-1c4 1 6 3 9 4h1l-1-1c0-3-5-5-7-7-1-1-1-1-2-1-1-1-1-1-1-2l-7-2z" class="B"></path><defs><linearGradient id="r" x1="416.293" y1="455.648" x2="409.001" y2="466.392" xlink:href="#B"><stop offset="0" stop-color="#4f4e4e"></stop><stop offset="1" stop-color="#616c76"></stop></linearGradient></defs><path fill="url(#r)" d="M384 377c2-1 3-3 5-3 0-1 0-1 1 0 4 1 8 1 12 2 1 0 2 1 3 2-1 2-5 3-7 4-1 0-3 2-4 2-3 6-4 10-4 16 2 6 3 10 8 14-2 2-4 2-5 4 0 4 1 7 4 9l3 3c1 0 1-1 1-2 1-2 2-6 5-7h3c3 2 4 5 4 8 1 4-2 11 1 15 1 2 3 3 5 5 1 2 4 6 7 6h0v-1c-1-1-1-4-1-5h2v-1l1 4h1c0-1 0-2 1-3h4c1 0 1 1 2 2v3c1-3 2-7 4-9 2 1 2 2 3 3 1 2 2 3 3 5h0c1 3 0 7 1 10 1 2 1 5 1 7 1 2 1 4 1 6 1 2 1 3 1 5l1 2 3 8v2l1 3 2 2h0c1 0 1 1 1 1h1c2 0 4 1 6 2s5 1 7 2l1 1 3-3c2 0 3-1 5-2 0-2 1-4 2-5l1-2c0-1 0-2 1-3s1-3 2-4c2 1 2 1 3 3l-1 3v5c-1 4-4 9-3 13-2 3-4 4-5 7-4 6-7 12-12 18h-1s-7-11-9-13c-1-2-4-5-6-7l-16-16-12-12-2 2c-4-3-7-6-11-9l-14-12-1-1-3-2c2 0 3 0 4-1l-6-7-2-3h-1v-4-4l1-3h0 1 0 1l-2-6c-1-3-3-4-5-6l-9-9c1-1 1 0 2-1h0l2 1h0c0-2 0-4-1-6-2-3-3-6-4-9l-1-3-1-3c1-1 1-5 1-7l3-9h0c3-1 4-4 6-5l1-1v-1z"></path><path d="M394 439h1l2 2 5 6 41 39 9 11c2 2 3 6 4 9v1c-1-1-1-2-1-3l-1-1c-1-1-1-2-2-2-8-15-24-26-36-38-9-7-16-15-22-24z" class="F"></path><path d="M382 414v-14 6l1 1c0 4 0 8 1 11 2 7 7 12 11 18 1 2 3 5 5 7h0c5 5 11 7 14 13h1c2 1 17 16 20 18v1c3 3 7 7 9 10l-1 1-41-39-5-6-2-2h-1c-2-1-5-6-6-8 0-2-2-4-3-5-2-4-3-8-3-12z" class="X"></path><path d="M443 448c1 2 2 3 3 5h0c1 3 0 7 1 10 1 2 1 5 1 7 1 2 1 4 1 6 1 2 1 3 1 5l1 2 3 8v2l1 3 2 2h0c-1 2 0 5 1 7v2c1 1 1 0 1 1s0 1 1 2v4c1 2 1 2 1 4-1-1-2-3-3-4-2-2-4-3-5-4-4-4-7-9-11-13l-9-9 3 1-2-3-4-5c1 0 1 0 2 1h1c2 1 3 2 5 4l1 1c2 0 5 3 6 4v1c3 2 4 7 7 9 1 0 1 1 2 2l1 1c0 1 0 2 1 3v-1c-1-3-2-7-4-9l-9-11 1-1c-2-3-6-7-9-10v-1l5 5 4 4c1 2 2 4 4 6 0-3-1-6-2-9-2-10-3-21-3-32z" class="S"></path><path d="M430 481c1 0 1 0 2 1h1c2 1 3 2 5 4l1 1c2 0 5 3 6 4v1l6 10-15-13-2-3-4-5z" class="R"></path><path d="M430 481c1 0 1 0 2 1h1c2 1 3 2 5 4l1 1c1 1 3 3 3 4h0c-2-2-4-3-7-5h-1l-4-5z" class="O"></path><path d="M384 377c2-1 3-3 5-3 0-1 0-1 1 0 4 1 8 1 12 2 1 0 2 1 3 2-1 2-5 3-7 4-1 0-3 2-4 2-1 1-2 1-3 2l-3 3c-5 2-5 6-6 11v14l-1-3-1 1v4l3 9h0c-1-1-3-4-3-4 0-2 0-4-1-6-2-3-3-6-4-9l-1-3-1-3c1-1 1-5 1-7l3-9h0c3-1 4-4 6-5l1-1v-1z" class="D"></path><path d="M377 384h0c3-1 4-4 6-5l-4 6c0 4-1 9 0 13l1 1c-1 3-1 6-1 9v6c-2-3-2-6-3-9l-1 1-1-3-1-3c1-1 1-5 1-7l3-9z" class="B"></path><path d="M377 384h0c3-1 4-4 6-5l-4 6c-2 6-5 14-3 20l-1 1-1-3-1-3c1-1 1-5 1-7l3-9z" class="E"></path><path d="M384 377c2-1 3-3 5-3 0-1 0-1 1 0 4 1 8 1 12 2 1 0 2 1 3 2-1 2-5 3-7 4-2-1-5-1-7 0h0l-1-1c-4 3-6 5-8 9-1 3-2 6-2 9l-1-1c-1-4 0-9 0-13l4-6 1-1v-1z" class="F"></path><path d="M384 378v1c-1 2-2 3-2 5-1 2-2 7-2 9l1-1v-2h1 0c-1 3-2 6-2 9l-1-1c-1-4 0-9 0-13l4-6 1-1z" class="K"></path><path d="M402 376c1 0 2 1 3 2-1 2-5 3-7 4-2-1-5-1-7 0h0l-1-1c2-2 6-4 9-5h3z" class="P"></path><path d="M382 400c1-5 1-9 6-11l3-3c1-1 2-1 3-2-3 6-4 10-4 16 2 6 3 10 8 14-2 2-4 2-5 4 0 4 1 7 4 9l3 3c1 0 1-1 1-2 1-2 2-6 5-7h3c3 2 4 5 4 8 1 4-2 11 1 15 1 2 3 3 5 5 1 2 4 6 7 6h0v-1c-1-1-1-4-1-5h2v-1l1 4h1c0-1 0-2 1-3h4c1 0 1 1 2 2v3c1-3 2-7 4-9 2 1 2 2 3 3 0 11 1 22 3 32 1 3 2 6 2 9-2-2-3-4-4-6l-4-4-5-5c-3-2-18-17-20-18h-1c-3-6-9-8-14-13h0c-2-2-4-5-5-7-4-6-9-11-11-18-1-3-1-7-1-11l-1-1v-6z" class="Q"></path><defs><linearGradient id="s" x1="379.319" y1="397.967" x2="394.59" y2="419.099" xlink:href="#B"><stop offset="0" stop-color="#9d9fa5"></stop><stop offset="1" stop-color="#bfc3c3"></stop></linearGradient></defs><path fill="url(#s)" d="M382 400c1-5 1-9 6-11l3-3c1-1 2-1 3-2-3 6-4 10-4 16h0v-6h-1c0 2-1 5-1 8 0 4 1 8 2 12 1 5 0 9 2 13 1 3 2 6 4 9 1 1 2 2 3 4h0c1 1 1 2 1 3h0c-2-2-4-5-5-7-4-6-9-11-11-18-1-3-1-7-1-11l-1-1v-6z"></path><path d="M390 442h1 0 1c5 8 11 16 18 23l4 4c6 1 8 4 12 7 2 1 2 4 4 5l4 5 2 3-3-1 9 9c4 4 7 9 11 13 1 1 3 2 5 4 1 1 2 3 3 4 0-2 0-2-1-4v-4c-1-1-1-1-1-2s0 0-1-1v-2c-1-2-2-5-1-7h0 0c1 0 1 1 1 1h1c2 0 4 1 6 2s5 1 7 2l1 1 3-3c2 0 3-1 5-2 0-2 1-4 2-5l1-2c0-1 0-2 1-3s1-3 2-4c2 1 2 1 3 3l-1 3v5c-1 4-4 9-3 13-2 3-4 4-5 7-4 6-7 12-12 18h-1s-7-11-9-13c-1-2-4-5-6-7l-16-16-12-12-2 2c-4-3-7-6-11-9l-14-12-1-1-3-2c2 0 3 0 4-1l-6-7-2-3h-1v-4-4l1-3h0z" class="Q"></path><path d="M489 491v5c-1 4-4 9-3 13-2 3-4 4-5 7-4 6-7 12-12 18h-1c10-13 15-29 21-43z" class="M"></path><path d="M398 463c1 1 3 2 3 3l24 20-2 2c-4-3-7-6-11-9l-14-12-1-1-3-2c2 0 3 0 4-1z" class="E"></path><path d="M398 463c1 1 3 2 3 3 1 1 1 2 2 3-1-1-1 0-2-1s-2-1-3-1l-1-1-3-2c2 0 3 0 4-1z" class="Z"></path><defs><linearGradient id="t" x1="409.151" y1="448.234" x2="401.167" y2="469.755" xlink:href="#B"><stop offset="0" stop-color="#bcb3b2"></stop><stop offset="1" stop-color="#d4dce1"></stop></linearGradient></defs><path fill="url(#t)" d="M390 442h1 0 1c5 8 11 16 18 23l4 4c6 1 8 4 12 7 2 1 2 4 4 5l4 5 2 3-3-1v-1h-1l-7-7c-1 0-2-1-3-2h-1c-1 0-3 0-3-1-2-1-4-4-6-6l-6-6c-3-3-9-9-13-10 0 0-1 0-1 1l-2-3h-1v-4-4l1-3h0z"></path><path d="M389 445l1-3h0l3 9h0l-4-2h0v-4z" class="M"></path><path d="M414 469c6 1 8 4 12 7 2 1 2 4 4 5l4 5 2 3-3-1v-1l-19-18z" class="D"></path><path d="M457 498h0c1 0 1 1 1 1h1c2 0 4 1 6 2s5 1 7 2l1 1 3-3c2 0 3-1 5-2 0 2-1 4-2 6v1c-1 1-1 1-1 2l-1 2c-1 4-3 8-5 13 0 0-1 2-2 3l-2-5-1 1c0 2 2 6 2 8-2-1-2-4-3-6-2-2-3-4-5-6 0-2 0-2-1-4v-4c-1-1-1-1-1-2s0 0-1-1v-2c-1-2-2-5-1-7h0z" class="G"></path><path d="M481 499c0 2-1 4-2 6v1c-1 1-1 1-1 2l-1 2c-4-2-8-4-13-5 3-1 5-1 8-2l1 1 3-3c2 0 3-1 5-2z" class="S"></path><path d="M457 498h0c1 0 1 1 1 1h1c2 0 4 1 6 2s5 1 7 2c-3 1-5 1-8 2l-3-1c-1 4 5 14 7 17l-1 1c0 2 2 6 2 8-2-1-2-4-3-6-2-2-3-4-5-6 0-2 0-2-1-4v-4c-1-1-1-1-1-2s0 0-1-1v-2c-1-2-2-5-1-7h0z" class="C"></path><path d="M461 518c0-2 0-2-1-4v-4c-1-1-1-1-1-2s0 0-1-1v-2c-1-2-2-5-1-7l2 6c2 6 5 12 8 18 0 2 2 6 2 8-2-1-2-4-3-6-2-2-3-4-5-6z" class="V"></path><defs><linearGradient id="u" x1="517.976" y1="502.789" x2="536.745" y2="506.461" xlink:href="#B"><stop offset="0" stop-color="#555c63"></stop><stop offset="1" stop-color="#68747f"></stop></linearGradient></defs><path fill="url(#u)" d="M552 402c1 1 1 1 1 2h1c1 1 1 1 2 1v1c-2 2-5 4-6 6 1 1 1 0 2 1v1c-2 2-3 4-4 7 1 1 2 1 2 1h0c-1 1-3 2-3 4-1 1-1 1 0 3 1 0 2 0 3 2l-2 2c0 1 0 0 1 1v1c-1 2-2 2-3 3-3 2-3 4-3 7v1l-1 1c1 2 3 4 5 5v2l-7 6c-2 2-3 4-2 6v5l-1 4 1 1h0l1-1c-1 3-3 5-4 8v1l-1 2c0 3 0 6-2 8v1h-1c3 2 3 4 5 6 1 4 3 8 5 11h1 1 0l-1 3c-1 4 3 9 6 12-1 3 1 4 2 6l2 1-1 1 2 2c0-1 1-1 1-2l-3 9-3 5c-4 7-7 17-9 24-1 3-1 5-1 8l1 1v2s-1 1-1 2l1 2v2c-1 2-3 2-5 3-3 2-6 3-8 6-5 8-2 18 0 27l1 5c1 2 1 3 1 5h0c-1 2-3 4-5 6-4 5-5 12-6 18l-1-1c0 1 0 2-1 3-1 3 0 5-1 7h0c-2-3-1-9-1-13l1-37v-5c-2-9 0-19 0-28l1-50c1-1 1-3 1-5h0c2-4 1-10 1-14 0-2 1-5 1-7v-8c1-4 2-8 2-12l5-34c0-3 1-7 1-10 1 1 2 1 2 2s0 1 1 2l1-6 1-5 1-4c3-11 10-20 16-29h1l3-4z"></path><path d="M526 446c1 1 2 1 2 2s0 1 1 2l-3 20c-1-1 0-2-1-4 0-3 1-6 1-9l-1-1c0-3 1-7 1-10zm21 8l-2-2h-5c-1-2-1-5-2-7h1l1-1c0-1 1-2 1-2 1-2 1-5 2-6 0-1 2-2 2-2h1c2 0 2 0 3 1-1 2-2 2-3 3-3 2-3 4-3 7v1l-1 1c1 2 3 4 5 5v2z" class="R"></path><path d="M524 500c0-1 2-4 2-5 2-5 4-9 7-13 1-2 2-5 4-7l1 1h0l1-1c-1 3-3 5-4 8v1l-1 2c0 3 0 6-2 8v1l-3-1-4 12h-1v-6z" class="V"></path><path d="M535 483v1l-1 2c0 3 0 6-2 8v1l-3-1 6-11z" class="Z"></path><path d="M516 531h0c1 6 1 11 1 17-1 3-1 6-1 9v27c-1 4 0 8-1 11-2 6 0 13-1 19-2-9 0-19 0-28l1-50c1-1 1-3 1-5z" class="R"></path><path d="M521 580l3-8 1-1c3 3 5 11 5 16 0 1 0 3-1 4l-3 3c-4 4-5 7-6 12v3c-1-4-1-9 1-12 2-5 6-9 6-15h-1v-5c-1-1-1 0-1-1v-2h0c-1 2-2 4-4 5v1z" class="H"></path><path d="M552 402c1 1 1 1 1 2h1c1 1 1 1 2 1v1c-2 2-5 4-6 6 1 1 1 0 2 1v1h-1-1c-2 1-3 2-5 3 0 1 0 1-1 1-1 1-1 2-2 3v1c-1 1-1 0-1 1 0 2-2 3-2 5-1 2-2 4-2 6-1-1-1-3 0-4l1-1v-1c0-1 1-1 1-2l1-4v-1l-2 4c-2 3-4 7-5 11l-1-1c3-11 10-20 16-29h1l3-4z" class="O"></path><path d="M531 572c2-1 2-2 3-3l1-1c0 1 0 1 1 2v1c1 0 0 0 1-1-1 4-2 6 1 10v1l1 1v2s-1 1-1 2l1 2v2c-1 2-3 2-5 3-3 2-6 3-8 6l-1-1c3-3 7-5 10-9-2-4-4-8-5-13 0-1 0-3 1-4h0z" class="M"></path><path d="M534 578l4 8 1 2c-1 0-1 1-2 1l-1-1-1-2c-1-3-2-6-1-8z" class="S"></path><path d="M531 572c2-1 2-2 3-3l1-1c0 1 0 1 1 2v1c1 0 0 0 1-1-1 4-2 6 1 10v1l1 1v2s-1 1-1 2l-4-8-3-6z" class="Z"></path><path d="M524 500v6h1c-2 6-3 12-3 18 0 2 0 4 1 6 0 5 1 10 2 15s3 10 4 15c0 2 0 5 1 8 0 1 1 3 1 4-1 1-1 3-1 4-1-1-1-3-1-4-1-3-3-5-3-8-1-7-4-14-5-21-2-10 0-19 1-28 0-5 0-11 2-15z" class="c"></path><path d="M521 580v-1c2-1 3-3 4-5h0v2c0 1 0 0 1 1v5h1c0 6-4 10-6 15-2 3-2 8-1 12v3l1-3v-1c1-4 2-7 4-10l1 1c-5 8-2 18 0 27l1 5c1 2 1 3 1 5h0c-1 2-3 4-5 6-4 5-5 12-6 18l-1-1c0 1 0 2-1 3-1 3 0 5-1 7h0c-2-3-1-9-1-13l1-37c1-3 1-6 1-9l3-22c0-2 1-4 2-6 0-1 0-1 1-2z" class="E"></path><path d="M520 612l1-3v-1c1-4 2-7 4-10l1 1c-5 8-2 18 0 27l1 5c1 2 1 3 1 5h0c-1 2-3 4-5 6-4 5-5 12-6 18l-1-1c1-3 1-6 2-10 2-5 3-9 4-15 0-5-1-9-2-14v-8z" class="P"></path><path d="M525 506l4-12 3 1h-1c3 2 3 4 5 6 1 4 3 8 5 11h1 1 0l-1 3c-1 4 3 9 6 12-1 3 1 4 2 6l2 1-1 1 2 2c0-1 1-1 1-2l-3 9-3 5c-4 7-7 17-9 24-1 3-1 5-1 8v-1c-3-4-2-6-1-10-1 1 0 1-1 1v-1c-1-1-1-1-1-2l-1 1c-1 1-1 2-3 3h0c0-1-1-3-1-4-1-3-1-6-1-8-1-5-3-10-4-15s-2-10-2-15c-1-2-1-4-1-6 0-6 1-12 3-18z" class="U"></path><path d="M537 570l13-30c-3-3-6-5-8-8h1c1 2 4 5 6 6 2 0 2 0 4-1 0-1 1-1 1-2l-3 9-3 5c-4 7-7 17-9 24-1 3-1 5-1 8v-1c-3-4-2-6-1-10z" class="b"></path><path d="M525 506l4-12 3 1h-1c3 2 3 4 5 6 1 4 3 8 5 11h1 1 0l-1 3c-1 4 3 9 6 12-1 3 1 4 2 6l2 1-1 1c-4-4-8-9-11-14s-4-10-7-14c-4 3-6 7-8 12v1c-1 2-2 4-2 7v2 1c-1-2-1-4-1-6 0-6 1-12 3-18z" class="N"></path><path d="M525 506l4-12 3 1h-1v6 2c-4 4-6 11-6 16v1c-1 2-2 4-2 7v2 1c-1-2-1-4-1-6 0-6 1-12 3-18z" class="E"></path><defs><linearGradient id="v" x1="500.723" y1="290.771" x2="536.933" y2="281.41" xlink:href="#B"><stop offset="0" stop-color="#c8c9ca"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#v)" d="M515 209h0l1-10c0 2 0 3 1 4 2 10 3 20 4 30l13 79c2 10 4 21 8 31 2 4 5 8 8 12l-6 3c2 4 4 6 7 9 0 1-1 1-3 1h-1c-1-1-2-3-3-4v-1h-1l-2-2c0-2-1-3-2-4h-1c-1 1-1 2-2 2l-3-1v2c0 1 1 3 1 4l7 13c1 1 2 2 2 3 0 2 0 3-1 5 3 2 5 5 9 6-1 2-2 2-4 3-1 1-2 1-3 2l-1 1c0 1-1 2-1 3 2-2 3-4 5-4l-6 8 1 1c2-2 5-5 8-6l-3 4c1 2 1 2 2 3h-1c-6 9-13 18-16 29l-1 4-1 5-1 6c-1-1-1-1-1-2s-1-1-2-2c0 3-1 7-1 10l-5 34c0 4-1 8-2 12v8c0 2-1 5-1 7 0 4 1 10-1 14h0v-52-10-27-22-17h0c0 1 0 2-1 3v-5l-1 11v1c-1-7 0-15 0-21l1-76c-1-3-1-6-1-9v-20l1-78z"></path><defs><linearGradient id="w" x1="523.889" y1="380.112" x2="506.111" y2="373.888" xlink:href="#B"><stop offset="0" stop-color="#b2c5ca"></stop><stop offset="1" stop-color="#e9e0e7"></stop></linearGradient></defs><path fill="url(#w)" d="M515 316c1 3 0 7 0 11v20c1-3 1-6 1-9v-18h0l1 78c0 7 0 15-1 22v-17h0c0 1 0 2-1 3v-5l-1 11v1c-1-7 0-15 0-21l1-76z"></path><defs><linearGradient id="x" x1="523.536" y1="348.502" x2="538.377" y2="334.495" xlink:href="#B"><stop offset="0" stop-color="#babfc4"></stop><stop offset="1" stop-color="#f5f2ef"></stop></linearGradient></defs><path fill="url(#x)" d="M526 332l2-2v-7h0l-2-10c2 2 4 8 4 10 0 3 1 6 2 8l-1 1 1 1v1c0 2 2 5 2 7l5 16h-1c-1 1-1 2-2 2l-3-1v2c-1-2-2-5-4-7h-1v2 3c0 2 0 0-1 2v8l-1-1h0c-2-3 0-7 0-9v-5c2-7 0-14 0-21h0z"></path><defs><linearGradient id="y" x1="517.534" y1="398.924" x2="523.77" y2="320.18" xlink:href="#B"><stop offset="0" stop-color="#888f96"></stop><stop offset="1" stop-color="#bfc1c3"></stop></linearGradient></defs><path fill="url(#y)" d="M518 367c-1-10 1-19 0-29 0-2-1-4-1-5l2-10c0-2 1-3 3-3-1-4-1-9 0-12 1 8 2 16 4 24h0c0 7 2 14 0 21v5c0 2-2 6 0 9h0l1 1-2 25h0c-1 0-2 1-3 2v-1c-1-3 0-7 0-10-1 3-1 17-2 18l-1-2h0c-2-4 0-10 0-14v-18l-1-1z"></path><defs><linearGradient id="z" x1="532.426" y1="414.022" x2="517.462" y2="414.349" xlink:href="#B"><stop offset="0" stop-color="#707980"></stop><stop offset="1" stop-color="#8a929a"></stop></linearGradient></defs><path fill="url(#z)" d="M527 368v-8c1-2 1 0 1-2v-3-2h1c2 2 3 5 4 7 0 1 1 3 1 4l7 13c1 1 2 2 2 3 0 2 0 3-1 5 3 2 5 5 9 6-1 2-2 2-4 3v-1-1c-1-1-2-1-3-1h-1l1-1-2-1h-1l-1 4c-3 2-5 6-7 9 0 2-2 4-2 6-1 1-1 2-2 2l-3 13c-1 0-1 1-1 2v1 2h1c0 1 0 0-1 1 0 1 1 2 0 3 0 1 0 3-1 4 0 1 1 2 0 3 0 1 0 2-1 3h0v4c-1-5 0-9-1-13 0-4 2-10 0-12v-3l-2 1c0 2 1 5 0 7 0 2-1 4-1 6 0 3 1 7 0 11-3 12-1 24-3 36v-10-27-22c1-7 1-15 1-22 1-3 0-6 0-8 1-7 0-16 1-23l1 1v18c0 4-2 10 0 14h0l1 2c1-1 1-15 2-18 0 3-1 7 0 10v1c1-1 2-2 3-2h0l2-25z"></path><path d="M537 382c1 4-3 12-5 15 0-1 0-1 1-2h-1c-2 1-2 4-3 4 1-3 3-7 4-10 2-2 3-5 4-7z" class="O"></path><path d="M522 395c1-1 2-2 3-2-1 3-1 6-1 9s-1 8 1 10v1h0c1 2 1 3 0 5h-1v-2c0-1 0-2-1-3 0-1-1-1-1-2v-1-8-4h0v-3z" class="L"></path><path d="M518 367l1 1v18c0 4-2 10 0 14h0v11l-1 17c-1 4 0 10-2 14v-22c1-7 1-15 1-22 1-3 0-6 0-8 1-7 0-16 1-23z" class="c"></path><path d="M541 377c1 1 2 2 2 3 0 2 0 3-1 5 3 2 5 5 9 6-1 2-2 2-4 3v-1-1c-1-1-2-1-3-1h-1l1-1-2-1h-1l-1 4c-3 2-5 6-7 9 0 2-2 4-2 6-1 1-1 2-2 2 1-3 2-6 4-10 1-5 5-11 5-16v-2-1l-1 1c-1 2-2 5-4 7 1-3 2-5 3-7 1-3 2-4 5-5z" class="N"></path><path d="M538 381c0-1 0-1 1-2 2 1 2 2 3 3-1 2-1 3-2 4-2 5-4 10-7 14 1-5 5-11 5-16v-2-1z" class="L"></path><path d="M527 368v-8c1-2 1 0 1-2v-3-2h1c2 2 3 5 4 7 0 1 1 3 1 4l7 13c-3 1-4 2-5 5-1 2-2 4-3 7s-3 7-4 10l-1 2-3 12v-1c-2-2-1-7-1-10s0-6 1-9h0l2-25z" class="Y"></path><path d="M527 368v-8c1-2 1 0 1-2v-3-2h1c-1 3 0 7 0 11l-1 17c0 6-2 11-2 17 0 1 1 2 1 4 0 0-1 1-1 2v1l1-1v-2l1-1h0l-3 12v-1c-2-2-1-7-1-10s0-6 1-9h0l2-25z" class="S"></path><defs><linearGradient id="AA" x1="515.958" y1="458.301" x2="524.096" y2="458.184" xlink:href="#B"><stop offset="0" stop-color="#717981"></stop><stop offset="1" stop-color="#859098"></stop></linearGradient></defs><path fill="url(#AA)" d="M540 393l1-4h1l2 1-1 1h1c1 0 2 0 3 1v1 1c-1 1-2 1-3 2l-1 1c0 1-1 2-1 3 2-2 3-4 5-4l-6 8 1 1c2-2 5-5 8-6l-3 4c1 2 1 2 2 3h-1c-6 9-13 18-16 29l-1 4-1 5-1 6c-1-1-1-1-1-2s-1-1-2-2c0 3-1 7-1 10l-5 34c0 4-1 8-2 12v8c0 2-1 5-1 7 0 4 1 10-1 14h0v-52c2-12 0-24 3-36 1-4 0-8 0-11 0-2 1-4 1-6 1-2 0-5 0-7l2-1v3c2 2 0 8 0 12 1 4 0 8 1 13v-4h0c1-1 1-2 1-3 1-1 0-2 0-3 1-1 1-3 1-4 1-1 0-2 0-3 1-1 1 0 1-1h-1v-2-1c0-1 0-2 1-2l3-13c1 0 1-1 2-2 0-2 2-4 2-6 2-3 4-7 7-9z"></path><defs><linearGradient id="AB" x1="536.849" y1="420.71" x2="529.257" y2="418.157" xlink:href="#B"><stop offset="0" stop-color="#939da5"></stop><stop offset="1" stop-color="#cdd7df"></stop></linearGradient></defs><path fill="url(#AB)" d="M540 393l1-4h1l2 1-1 1h1c1 0 2 0 3 1v1 1c-1 1-2 1-3 2l-1 1c0 1-1 2-1 3 2-2 3-4 5-4l-6 8 1 1c2-2 5-5 8-6l-3 4c1 2 1 2 2 3h-1c-6 9-13 18-16 29l-1 4-1 5-1 6c-1-1-1-1-1-2s-1-1-2-2c0 3-1 7-1 10h0l-1 1v-1l1-1c-1-1-1-2-1-3h1l-2-1c0-4 1-8 2-12 2-14 4-33 15-43v-1h0v-2z"></path><path d="M528 437c1 2 0 5 2 7l-1 6c-1-1-1-1-1-2s-1-1-2-2c0-2 1-7 2-9z" class="L"></path><path d="M542 400c2-2 3-4 5-4l-6 8c-2 3-4 7-5 10-2 4-3 8-5 12v-1l-1 1c0 1 0 1-1 2l3-9c2-5 4-12 7-16 1-1 2-1 2-2l1-1z" class="N"></path><defs><linearGradient id="AC" x1="540.65" y1="404.166" x2="528.341" y2="435.437" xlink:href="#B"><stop offset="0" stop-color="#676e72"></stop><stop offset="1" stop-color="#99a2ab"></stop></linearGradient></defs><path fill="url(#AC)" d="M542 405c2-2 5-5 8-6l-3 4c1 2 1 2 2 3h-1c-6 9-13 18-16 29l-1 4-1 5c-2-2-1-5-2-7-1-3 0-6 1-9 1-1 1-1 1-2l1-1v1c2-4 3-8 5-12 1-3 3-7 5-10l1 1z"></path><path d="M542 405c2-2 5-5 8-6l-3 4c1 2 1 2 2 3h-1c-6 9-13 18-16 29l-1 4-1-1c0-3 2-6 3-9l7-16v-1l1-2c0-2 0-3 1-5z" class="B"></path><path d="M542 405c2-2 5-5 8-6l-3 4-7 9 1-2c0-2 0-3 1-5z" class="D"></path><defs><linearGradient id="AD" x1="350.229" y1="297.786" x2="427.431" y2="306.094" xlink:href="#B"><stop offset="0" stop-color="#95adb8"></stop><stop offset="1" stop-color="#c5c9d0"></stop></linearGradient></defs><path fill="url(#AD)" d="M389 200c2-1 4-2 5-4 2-1 4-3 6-4 7-4 15-8 23-10 7-2 14-2 21-3-2 1-3 3-5 5 0 1-1 3-2 3-2 1-6 2-9 3-6 1-13 4-19 8-24 17-32 53-39 80-1 7-2 15-4 22 0 1 0 3 1 4-1 5 0 9 3 13l7 8-1 1h0c-1 0-3-1-3-2l-1-1c-2-1-5-3-8-3l-1-3c-1 3-1 6-1 8v7c2 1 2 2 4 3v1l-1 1c2 4 6 7 11 8l4 1c3 1 6 2 9 2l3 2h5l-2 2c-1 1-2 1-2 2h2 0l-1 2h0l-3 1c5 0 9 0 14 1l-3 1-11 1c-3 0-9-1-12 2 1 1 3 1 4 2 3 1 7 0 9-1 3-1 5 0 7 0-4 2-8 3-11 7-2 2-5 5-6 7v1l2-1h0v1l-1 1c-2 1-3 4-6 5h0l-3 9c0 2 0 6-1 7l1 3h0c-1-1-2-1-2-2l-2-5c-1-2-2-5-2-7l-1-1c-1 1-2 5-2 7-1 3-1 6-1 9-1 2 0 3-1 5 0 3 0 7 1 10 0 2 1 6 0 8h0c1 2 1 2 1 3l1 2-1 1c0-2 0-4-1-5s-2-1-3 0c-2 1-3 5-5 5h-1c-3 0-5-1-7-3l-3-6 1-1 1 2v-1c1-1 2-2 4-3 1 0 2-1 3-2l1-1s1 0 1-1c0-2 0-4-1-6l-2-11c-6-28-9-57-5-86 2-15 5-29 9-43 2-9 5-19 8-28 4-12 11-24 17-35 3-2 6-5 7-8z"></path><path d="M361 354l3 3c0 2 0 2-1 4h-1c-1-2-1-5-1-7z" class="W"></path><path d="M356 420l1-2c2 1 2 5 2 7s0 5-2 6c-1 1-1 1-2 1l1-1c2-3 0-8 0-11z" class="Q"></path><path d="M366 300c0 1 0 3 1 4-1 5 0 9 3 13h-1c-3-1-4-2-6-4l3-13z" class="O"></path><path d="M347 425v1l9-6c0 3 2 8 0 11l-1 1v1c-3 0-5-1-7-3l-3-6 1-1 1 2z" class="G"></path><path d="M362 341c0-2 0-2 1-4 2 1 3 3 4 4 2 1 3 2 5 3 4 3 9 4 14 5h2c1 1 3 1 4 1h5l-2 2c-1 1-2 1-2 2h2 0l-1 2h0l-3 1h-1-1c-2 1-4 0-6 0-1 1 0 1-1 1-3 0-6 1-9 0-2 0-3-1-4-2-3-2-5-2-8-4 0-3 0-7 1-11h0z" class="F"></path><path d="M362 341h0c3 5 5 8 11 11 5 3 11 4 17 5h-1c-2 1-4 0-6 0-1 1 0 1-1 1-3 0-6 1-9 0-2 0-3-1-4-2-3-2-5-2-8-4 0-3 0-7 1-11z" class="O"></path><path d="M362 341h0c3 5 5 8 11 11-1 1-1 1-2 1-3 0-5-1-7-3-3-3-2-5-2-9z" class="a"></path><path d="M361 352c3 2 5 2 8 4 1 1 2 2 4 2 3 1 6 0 9 0 1 0 0 0 1-1 2 0 4 1 6 0h1 1c5 0 9 0 14 1l-3 1-11 1c-3 0-9-1-12 2 1 1 3 1 4 2 3 1 7 0 9-1 3-1 5 0 7 0-4 2-8 3-11 7-2 2-5 5-6 7v1l2-1h0v1l-1 1c-2 1-3 4-6 5h0l-3 9c0 2 0 6-1 7-3-3-5-11-6-15l-1-2-3-18c1-2 4-5 5-7l-1-1c-1 0-2-1-3 0l-3-3v-2z" class="N"></path><path d="M377 360h2v1h-1l-5 4c1 2 4 3 4 5v3c-1-2-2-3-3-4h-2c-1 0-1 0-2-1 0-1-1-3-1-4 1-4 5-2 7-3l1-1z" class="I"></path><defs><linearGradient id="AE" x1="368.996" y1="391.943" x2="379.263" y2="383.272" xlink:href="#B"><stop offset="0" stop-color="#7b7e81"></stop><stop offset="1" stop-color="#a1a4a8"></stop></linearGradient></defs><path fill="url(#AE)" d="M377 373v-3c3 6 1 9 0 14l-3 9c0 2 0 6-1 7-3-3-5-11-6-15 4-1 6-2 9-5 1-2 1-5 1-7z"></path><path d="M372 369h2c1 1 2 2 3 4 0 2 0 5-1 7-3 3-5 4-9 5l-1-2c1-5 2-9 6-14z" class="G"></path><defs><linearGradient id="AF" x1="363.104" y1="369.697" x2="370.44" y2="367.444" xlink:href="#B"><stop offset="0" stop-color="#777b81"></stop><stop offset="1" stop-color="#9a9b9b"></stop></linearGradient></defs><path fill="url(#AF)" d="M367 357c1 0 2 1 3 2 2 1 4 1 7 1l-1 1c-2 1-6-1-7 3 0 1 1 3 1 4 1 1 1 1 2 1-4 5-5 9-6 14l-3-18c1-2 4-5 5-7l-1-1z"></path><path d="M127 193c1 0 2 1 3 1 2 0 4 0 6 1 1-1 3-1 4-2l1 1c4 2 7 6 11 8s10 2 14 4h1c2 0 5 3 6 4 4 3 7 5 10 8s5 6 8 8l2 2c2 1 3 3 4 4l2 2v1h-2l-1 1h0c1 2 3 4 5 6l3 4h-1c4 5 9 11 12 17h-1c-2-1-5-2-7-3h-1c-2-1-3 0-5 0 0-2 2-2 3-3l-5 1-1-1-8 2c-3 1-7 3-10 5-1 0-2 0-3 1l-1 1c-2 0-4 2-5 3-6 5-11 11-15 17-1 2-2 3-3 5 0 0 0 1-1 1-1 2-1 3-1 4v2c-2 2-3 6-3 9v-1c-1-1-1-3 0-4v-1h-1v2s-1 1-1 2l-1-2c2-6 1-13 4-19 0-2 2-5 3-6h0v-2c0-1 0-1 1-2h0v-1h0l-3 2-1-1-6 6v1c-5 4-10 6-17 6-4-1-9-3-12-6h0c-2-2-5-5-6-7l-1-2c-7-12-8-32-4-46 3-13 10-24 22-32l2-1z" class="Y"></path><path d="M110 233v1h1c1 0 2-1 2-1l-3 8-1-4 1-4z" class="b"></path><path d="M125 219h4l-3 2c0 1-2 1-3 2l-3 3c-1 0-2 1-2 1 1-4 4-6 7-8z" class="C"></path><path d="M187 243v1h-1v1 1h-2-1c-1 1-3 2-4 2-2 1-3 1-4 2s-1 1-2 1l-1-2 1-1c2-3 10-4 14-5z" class="F"></path><path d="M141 214c2-1 4-1 7 0l-1 1h-6c2 1 3 1 5 1-6 0-12 1-17 3h-4c3-2 5-3 8-4 2-1 5-1 8-1z" class="B"></path><path d="M149 274c5-4 11-12 18-13-5 4-9 8-12 13-1 1-2 3-3 4v-2c0-1 0-1 1-2h0v-1h0l-3 2-1-1z" class="K"></path><path d="M114 281c1 0 2 0 3 1s2 2 4 2l1-1c-1 0-2-1-3-1v-1l4 2h1c5 2 10 1 14-1 2-1 3-2 5-2h0v1c-5 4-10 6-17 6-4-1-9-3-12-6h0z" class="S"></path><path d="M118 220l-1 2h0c2-2 5-4 8-6 2-1 3-2 5-2 4-1 6-1 11 0-3 0-6 0-8 1-3 1-5 2-8 4s-6 4-7 8l-3 2h-1-1 0c1-4 3-7 5-10v1z" class="V"></path><path d="M104 239c0-2 2-6 2-9h1c1-4 2-7 4-10v4c-1 3-2 6-2 9l-1 1c0 1 0 2 1 3l1 4-1 1c0 1 0 1-1 2v2h1-1-2v2c-2-2 0-6-2-9z" class="C"></path><defs><linearGradient id="AG" x1="129.446" y1="210.294" x2="110.556" y2="210.208" xlink:href="#B"><stop offset="0" stop-color="#b4b2b2"></stop><stop offset="1" stop-color="#e4edf4"></stop></linearGradient></defs><path fill="url(#AG)" d="M127 193c1 0 2 1 3 1 2 0 4 0 6 1 1-1 3-1 4-2l1 1v4c0 2-1 4-2 6l-1 1h-3l-3-3h0l-9 9-2 2-3 6c-2 3-4 6-5 10h0 1 1l-2 4s-1 1-2 1h-1v-1l-1 4c-1-1-1-2-1-3l1-1c0-3 1-6 2-9v-4c2-5 6-11 10-16l-1-1c2-2 5-4 8-6v-2l-3-1 2-1z"></path><path d="M127 193c1 0 2 1 3 1v4s-1 0-2-1h0v-2l-3-1 2-1z" class="V"></path><path d="M129 201c1 1 1 1 3 1l-9 9-2 2-2-1c3-4 6-8 10-11z" class="H"></path><path d="M119 212l2 1-3 6c-2 3-4 6-5 10h0 1 1l-2 4s-1 1-2 1h-1v-1c2-7 5-14 9-21z" class="a"></path><path d="M136 195c1-1 3-1 4-2l1 1v4c0 2-1 4-2 6l-1 1h-3l-3-3h0c-2 0-2 0-3-1 2-2 4-4 7-6z" class="G"></path><defs><linearGradient id="AH" x1="196.984" y1="253.117" x2="187.173" y2="251.753" xlink:href="#B"><stop offset="0" stop-color="#525359"></stop><stop offset="1" stop-color="#707478"></stop></linearGradient></defs><path fill="url(#AH)" d="M199 241l4 5c4 5 9 11 12 17h-1c-2-1-5-2-7-3h-1c-2-1-3 0-5 0 0-2 2-2 3-3l-5 1-1-1-8 2-1-1 1-2c-2 0-5 1-7 2h-1l2-2h-2c-1 0-3 0-4-1 2-1 5-2 7-2 2-1 4-2 6-2h1v-1h-2c-1-3-3-2-4-4v-1-1h1v-1h0c2-1 5-1 8-1 1 0 3 0 4-1z"></path><path d="M178 255c2-1 5-2 7-2l1 1h8l1 1-1 1c2 0 3-1 4 1h0l-8 2-1-1 1-2c-2 0-5 1-7 2h-1l2-2h-2c-1 0-3 0-4-1z" class="D"></path><path d="M199 241l4 5c4 5 9 11 12 17h-1c-2-1-5-2-7-3h-1c-2-1-3 0-5 0 0-2 2-2 3-3h1v-2c-2-1-3-2-5-4h1l-1-2c-1-1-2-2-4-2h0c-1-2-1-2-3-3v-1c1 0 1 0 2-1 1 0 3 0 4-1z" class="Z"></path><path d="M108 274l-1-2c-7-12-8-32-4-46 3-13 10-24 22-32l3 1v2c-3 2-6 4-8 6l1 1c-4 5-8 11-10 16-2 3-3 6-4 10h-1c0 3-2 7-2 9 0 5 0 10 1 15v4l2 6 2 7v1s0 1-1 2z" class="F"></path><path d="M120 203l1 1c-4 5-8 11-10 16-2 3-3 6-4 10h-1c0 3-2 7-2 9 0 5 0 10 1 15v4l2 6 2 7v1c-1-2-2-4-2-5-2-5-3-9-4-13v-11c0-3 1-7 2-10 2-12 6-21 15-30z" class="V"></path><path d="M174 224l1-1c7 0 16 10 20 14l4 4c-1 1-3 1-4 1-3 0-6 0-8 1h0c-4 1-12 2-14 5l-1 1c0-2 0-4-1-5-1-5-2-10-5-15 1 1 2 0 3 0 3-1 4-3 5-5z" class="G"></path><path d="M132 202h0l3 3h3l1-1 11 1c1 0 3 0 4 2h-2l1 2-1 1c1 0 1 1 3 1 6 2 14 4 18 9-7-4-14-7-22-8h0c2 1 5 2 7 4l6 3c2 1 3 2 4 3 2 1 5 1 6 2-1 2-2 4-5 5-1 0-2 1-3 0-1 0-1-1-2-2-5-6-10-10-18-11-2 0-3 0-5-1h6l1-1c-3-1-5-1-7 0-5-1-7-1-11 0-2 0-3 1-5 2-3 2-6 4-8 6h0l1-2v-1l3-6 2-2 9-9z" class="M"></path><path d="M123 211c4 0 9-1 14-2 3 0 6 1 9 0 1 0 2 0 2 1-4 1-8 0-12 1-7 2-13 4-18 9v-1l3-6 2-2z" class="F"></path><path d="M132 202h0l3 3h3c1 0 2 1 3 1 1 1 6 1 7 2-1 0-2 0-2 1-3 1-6 0-9 0-5 1-10 2-14 2l9-9z" class="B"></path><path d="M167 261c3-2 7-4 11-6 1 1 3 1 4 1h2l-2 2h1c2-1 5-2 7-2l-1 2 1 1c-3 1-7 3-10 5-1 0-2 0-3 1l-1 1c-2 0-4 2-5 3-6 5-11 11-15 17-1 2-2 3-3 5 0 0 0 1-1 1-1 2-1 3-1 4v2c-2 2-3 6-3 9v-1c-1-1-1-3 0-4v-1h-1v2s-1 1-1 2l-1-2c2-6 1-13 4-19 0-2 2-5 3-6h0c1-1 2-3 3-4 3-5 7-9 12-13z" class="Q"></path><path d="M151 296l-1 1h-1v-4c1-2 2-3 3-4l1 2s0 1-1 1c-1 2-1 3-1 4z" class="F"></path><path d="M152 278v1l3-4c1-1 2-1 4-2-1 3-3 6-5 7l-2 1-3 3h0c0-2 2-5 3-6z" class="C"></path><path d="M167 261c3-2 7-4 11-6 1 1 3 1 4 1-4 2-9 4-12 7-4 3-7 6-10 9l-1 1c-2 1-3 1-4 2l-3 4v-1h0c1-1 2-3 3-4 3-5 7-9 12-13z" class="T"></path><path d="M183 258c2-1 5-2 7-2l-1 2 1 1c-3 1-7 3-10 5-1 0-2 0-3 1l-1 1c-2 0-4 2-5 3-6 5-11 11-15 17-1 2-2 3-3 5l-1-2 2-5c3-5 7-9 12-14 2-2 4-4 7-6 2-2 4-2 6-4 1 0 1 0 2-1 1 0 1 0 2-1z" class="J"></path><path d="M141 194c4 2 7 6 11 8s10 2 14 4h1c2 0 5 3 6 4 4 3 7 5 10 8s5 6 8 8l2 2c2 1 3 3 4 4l2 2v1h-2l-1 1h0c1 2 3 4 5 6l3 4h-1l-4-5-4-4c-4-4-13-14-20-14l-1 1c-1-1-4-1-6-2-1-1-2-2-4-3l-6-3c-2-2-5-3-7-4h0c8 1 15 4 22 8-4-5-12-7-18-9-2 0-2-1-3-1l1-1-1-2h2c-1-2-3-2-4-2l-11-1c1-2 2-4 2-6v-4z" class="O"></path><path d="M141 198c3 1 6 5 9 7l-11-1c1-2 2-4 2-6z" class="D"></path><path d="M196 236c-3-3-7-6-10-9-2-2-3-4-4-5-7-6-16-9-25-12 3-2 4-3 7-2 7 2 11 5 16 9s9 9 12 14c2 1 3 3 4 5h0z" class="a"></path><path d="M164 208c7 2 11 5 16 9-1 1-3-1-5-2-3-1-7-2-10-4-1-1-1-1-2-1s-1 0-2-1c1 0 2-1 3-1z" class="H"></path><path d="M115 239h0c2-8 10-15 17-19 5-3 11-3 16-1 8 2 12 8 15 14 3 5 5 10 6 16-6 4-13 8-18 13-1 2-3 4-4 6-2 2-3 5-4 7-4 5-13 7-18 6-6-2-10-6-13-11-3-7-5-16-4-24h1-1 3c1-2 2-5 4-7z" class="G"></path><path d="M115 239c1 4-2 8-1 12 1-1 1-4 1-6 1-2 1-4 3-6v1c-2 5-3 11-1 16 2 3 4 6 7 7 2 1 5 1 7 2-6 0-10 0-14-5-4-4-5-9-6-13-1-1-2-1-2-1h-1 3c1-2 2-5 4-7z" class="Q"></path><defs><linearGradient id="AI" x1="148.639" y1="234.53" x2="134.297" y2="217.812" xlink:href="#B"><stop offset="0" stop-color="#70747a"></stop><stop offset="1" stop-color="#9d9fa3"></stop></linearGradient></defs><path fill="url(#AI)" d="M115 239h0c2-8 10-15 17-19 5-3 11-3 16-1 8 2 12 8 15 14 0 1 0 0-1 1-4-5-8-9-14-11h-1c-5-1-11 1-16 3-6 4-9 9-13 14v-1c-2 2-2 4-3 6 0 2 0 5-1 6-1-4 2-8 1-12z"></path><path d="M777 295l9-21c9-21 19-42 35-59 15-16 33-27 56-28l3 2c1 0 3 1 4 1l5 4 1 1c4 3 7 5 10 9l2 2c-1 2 0 4 1 6l1 3c2 4 4 7 5 11 3 11 3 23 1 34-1 5-4 13-8 16h-2l-2 2c-2 2-4 3-6 4-4 2-9 2-13 1l-3-1c-1-1-1 0-2-1h0l-1 1h-1c-1 0-2-2-3-3l-2 1-3-5v1l1 2c-1 0-2-2-3-2-4-5-9-9-13-12-1-1-5-4-5-4-1-1-3-2-3-2l1-1-2-1c-4-2-10-5-15-4l-6 1h-3c1 1 0 1 1 1l-2 1v1h1l-3 3h-3v-2c1 0 1-2 1-3h-1v-1c-1 0-1 0-2-1l-7 10h2c2-1 5-1 7 0-2 1-3 2-3 4l-1 1h9c-8 0-15 0-22 6-4 4-6 9-9 13-2 3-4 8-7 9z" class="G"></path><path d="M819 240c0 1 0 1 1 2h3c1 1 4 1 5 1 5 1 10 3 15 5 1 1 2 2 3 2 9 7 18 14 23 24 4 4 9 7 15 7 5 1 9-1 12-4h1c0 1 0 1 1 1h0c-2 2-4 3-6 4-4 2-9 2-13 1l-3-1c-1-1-1 0-2-1h0l-1 1h-1c-1 0-2-2-3-3l-2 1-3-5v1l1 2c-1 0-2-2-3-2-4-5-9-9-13-12-1-1-5-4-5-4-1-1-3-2-3-2l1-1-2-1c-4-2-10-5-15-4l-6 1h-3c1 1 0 1 1 1l-2 1v1h1l-3 3h-3v-2c1 0 1-2 1-3h-1v-1c-1 0-1 0-2-1l3-5 8-7z" class="C"></path><path d="M864 275v-1c2 1 4 3 5 5l-2 1-3-5z" class="F"></path><path d="M828 243c5 1 10 3 15 5 1 1 2 2 3 2 9 7 18 14 23 24-5 0-8-6-11-9-5-4-10-9-15-13-3-2-8-3-12-5h0v-1l-3-3z" class="J"></path><path d="M819 240c0 1 0 1 1 2h3l-1 1 1 1s0 1-1 1c-1 1-1 2-2 3h-1v1h8l15 6c1 1 3 2 4 3l-1 1-3-2-2-1c-4-2-10-5-15-4l-6 1h-3c1 1 0 1 1 1l-2 1v1h1l-3 3h-3v-2c1 0 1-2 1-3h-1v-1c-1 0-1 0-2-1l3-5 8-7z" class="I"></path><path d="M819 240c0 1 0 1 1 2h3l-1 1 1 1s0 1-1 1c-1 1-1 2-2 3h-1l-2 1v1h-1c-2 1-4 2-4 4h-1-1v-1c-1 0-1 0-2-1l3-5 8-7z" class="E"></path><path d="M875 193c3 2 5 3 7 5l1 1 1-1 2 2v-3l1 1h1c0-1 0-2 1-3v-1l1 1c4 3 7 5 10 9l2 2c-1 2 0 4 1 6l1 3c2 4 4 7 5 11 3 11 3 23 1 34-1 5-4 13-8 16h-2l-2 2h0c-1 0-1 0-1-1 2-2 4-4 5-7 3-6 6-18 4-25h0c-2 1-2 3-2 5-1 5-2 9-6 12s-7 3-11 3c0-1 1-1 2-1h1c3-1 6-3 8-6s2-8 1-11v-1c-1-3-2-5-3-8-2-1-3-2-4-3-4-5-8-8-14-10s-12-1-17 2h-1c-3 2-6 6-8 9l-2-1 1-1-3-3c2-4 6-8 10-10v-3l-2-1s1-1 2-1c2-1 4-1 6-3h-2-1v-2l-4 1 2-2v-1c-3-1-6 0-9 0v-2l3-1h0l5-1h10c0-2 0-4 1-5 2-2 3-2 5-3h0c0-2 0-3 1-4z" class="F"></path><defs><linearGradient id="AJ" x1="888.362" y1="206.489" x2="899.128" y2="204.94" xlink:href="#B"><stop offset="0" stop-color="#81868e"></stop><stop offset="1" stop-color="#9c9e9d"></stop></linearGradient></defs><path fill="url(#AJ)" d="M886 197l1 1h1c0-1 0-2 1-3v-1l1 1c4 3 7 5 10 9l2 2c-1 2 0 4 1 6l1 3c-2-1-4-2-6-2l-12-13v-3z"></path><path d="M890 195c4 3 7 5 10 9l2 2c-1 2 0 4 1 6-3-3-5-6-8-9-2-1-4-3-4-5-1 0 0-2-1-3z" class="C"></path><path d="M898 213c2 0 4 1 6 2 2 4 4 7 5 11 3 11 3 23 1 34-1 5-4 13-8 16h-2c2-2 4-4 5-6 4-7 5-14 5-22 0-2 0-5-1-8s-2-7-3-11c-2-6-5-11-8-16z" class="P"></path><path d="M875 193c3 2 5 3 7 5l1 1 2 2c0 1 0 2-1 3v1c1 1 1 1 2 1h0c-1 1-2 1-3 2l-1-1-1 1c1 3 5 3 7 6l9 7c2 1 4 3 5 6l1 1v2l2 7c0 1 0 3 1 4l-1 1c0-1-1-1-1-2v-3l-1-2c0-1-1-3-2-5v-1c-1-1-2-2-3-4 0-1-1-2-2-3 0 0 0-1-1-1v-1c-3-2-5-3-8-4-5-3-12-5-18-5l-4-1-4 1-4 1 2-2v-1c-3-1-6 0-9 0v-2l3-1h0l5-1h10c0-2 0-4 1-5 2-2 3-2 5-3h0c0-2 0-3 1-4z" class="M"></path><path d="M875 209c2 0 4 1 5 0l-1-1c1-1 1-1 3-1l-1 1c1 3 5 3 7 6l-9-3c-3-1-7-2-11-2v-1c3 0 5 0 7 1z" class="C"></path><path d="M885 201c0 1 0 2-1 3v1c1 1 1 1 2 1h0c-1 1-2 1-3 2l-1-1c-2 0-2 0-3 1l1 1c-1 1-3 0-5 0-1-2-2-2-4-2h0l-1-1h4l1-1h6l3-3h0l1-1z" class="B"></path><path d="M858 205h10c0-2 0-4 1-5 2-2 3-2 5-3v2l-5 6-5 1v1h-3v1c2 0 3 1 5 1 0 1 0 1-1 1l-4 1-4 1 2-2v-1c-3-1-6 0-9 0v-2l3-1h0l5-1z" class="D"></path><path d="M875 193c3 2 5 3 7 5l1 1 2 2-1 1h0l-3 3h-6l-1-6v-2h0c0-2 0-3 1-4z" class="G"></path><path d="M861 211l4-1 4 1c6 0 13 2 18 5 3 1 5 2 8 4v1c1 0 1 1 1 1 1 1 2 2 2 3 1 2 2 3 3 4v1c1 2 2 4 2 5-2-1-3-4-5-6v1c1 4 2 6 2 9v3c0 1 1 3 1 5-1-2-1-3-2-5-1-1-2-4-3-4-2-1-3-2-4-3-4-5-8-8-14-10s-12-1-17 2h-1c-3 2-6 6-8 9l-2-1 1-1-3-3c2-4 6-8 10-10v-3l-2-1s1-1 2-1c2-1 4-1 6-3h-2-1v-2z" class="H"></path><path d="M877 218c2 1 4 1 6 3l1 1v1h-1c-2 1-4 0-5-1-4 0-7-1-11 0h0c-3 1-5 2-7 3l-1 1c-3 1-5 4-7 6 0-2 1-3 2-4 5-5 11-9 18-9 2-1 3-1 5-1z" class="N"></path><path d="M877 218c2 1 4 1 6 3l1 1-12-3c2-1 3-1 5-1z" class="C"></path><path d="M858 221c3-2 5-3 9-4s7 0 10 0v1c-2 0-3 0-5 1-7 0-13 4-18 9-1 1-2 2-2 4l-1 2-3-3c2-4 6-8 10-10z" class="Y"></path><path d="M861 211l4-1 4 1c6 0 13 2 18 5 3 1 5 2 8 4v1c1 0 1 1 1 1 1 1 2 2 2 3 1 2 2 3 3 4v1c1 2 2 4 2 5-2-1-3-4-5-6v1c1 4 2 6 2 9v3c-1-1-1-2-1-3v-2c-3-6-9-12-15-14v-1l-1-1c-2-2-4-2-6-3v-1c-3 0-6-1-10 0s-6 2-9 4v-3l-2-1s1-1 2-1c2-1 4-1 6-3h-2-1v-2z" class="B"></path><path d="M861 213h9l-8 3c-2 0-3 1-4 2l-2-1s1-1 2-1c2-1 4-1 6-3h-2-1z" class="R"></path><path d="M862 216c1 1 1 1 2 1l3-1c4-1 8-1 13 0 2 1 5 2 7 3v1c-3-1-6-2-10-3-3 0-6-1-10 0s-6 2-9 4v-3c1-1 2-2 4-2z" class="c"></path><path d="M877 217c4 1 7 2 10 3 1 2 2 2 4 3 0 1 1 1 2 1h0c0-1-2-3-3-4 0 0-1 0-1-1v-1c4 2 5 6 9 7 1 2 2 3 3 4v1c1 2 2 4 2 5-2-1-3-4-5-6v1c1 4 2 6 2 9v3c-1-1-1-2-1-3v-2c-3-6-9-12-15-14v-1l-1-1c-2-2-4-2-6-3v-1z" class="K"></path><path d="M898 230l-3-2c-1-1 0-2 0-4 1 2 2 3 3 5v1z" class="Q"></path><defs><linearGradient id="AK" x1="812.721" y1="224.988" x2="833.999" y2="243.746" xlink:href="#B"><stop offset="0" stop-color="#292b30"></stop><stop offset="1" stop-color="#576068"></stop></linearGradient></defs><path fill="url(#AK)" d="M777 295l9-21c9-21 19-42 35-59 15-16 33-27 56-28l3 2c1 0 3 1 4 1l5 4v1c-1 1-1 2-1 3h-1l-1-1v3l-2-2-1 1-1-1c-2-2-4-3-7-5-1 1-1 2-1 4h0c-2 1-3 1-5 3-1 1-1 3-1 5h-10l-5 1h0l-3 1v2c3 0 6-1 9 0v1l-2 2 4-1v2h1 2c-2 2-4 2-6 3-1 0-2 1-2 1l2 1v3c-4 2-8 6-10 10l3 3-1 1c-2 5-3 10-4 15-1 0-2-1-3-2-5-2-10-4-15-5-1 0-4 0-5-1h-3c-1-1-1-1-1-2l-8 7-3 5-7 10h2c2-1 5-1 7 0-2 1-3 2-3 4l-1 1h9c-8 0-15 0-22 6-4 4-6 9-9 13-2 3-4 8-7 9z"></path><path d="M880 189c1 0 3 1 4 1l5 4v1c-1 1-1 2-1 3h-1l-1-1v3l-2-2-1 1-1-1h1l1-1-4-3c-1-2 1-2 2-3l-2-2h0z" class="R"></path><path d="M884 190l5 4v1c-1 1-1 2-1 3h-1l-1-1c-1 0-1-1-2-2v-1-1h1l-1-3z" class="P"></path><defs><linearGradient id="AL" x1="861.524" y1="194.656" x2="868.946" y2="199.823" xlink:href="#B"><stop offset="0" stop-color="#292c32"></stop><stop offset="1" stop-color="#44484e"></stop></linearGradient></defs><path fill="url(#AL)" d="M858 205c0-1-1-1-2-2 0-1 0-2 1-3 3-4 9-8 14-8 2-1 3 0 4 1-1 1-1 2-1 4h0c-2 1-3 1-5 3-1 1-1 3-1 5h-10z"></path><path d="M792 273l-1-1v-3l1-1v-2l1-1v-3l2-3h0l1-2c0-1 0 0 1-1l5-8c1-1 2-3 3-4l7-10 2-3c2-3 5-5 7-7l1-1h1c3-2 5-7 8-9 4-2 8-5 11-7 3-3 8-9 12-9 1 0 1 0 2-1h0 1c-1 2-3 3-5 5-1 1-1 2-2 3h-1 4v1h0l-3 1v2l-3 1c-4 1-7 2-10 5l-10 11-10 12c-2 3-5 6-6 9l-3 5-7 10h2c2-1 5-1 7 0-2 1-3 2-3 4l-1 1h9c-8 0-15 0-22 6h-1z" class="W"></path><path d="M836 214c1-2 9-5 11-7l2-2h4v1h0l-3 1v2l-3 1c-4 1-7 2-10 5l-1-1z" class="T"></path><path d="M836 214l1 1-10 11c-2 0-4 1-5 2l1-3 9-8c1-1 3-2 4-3z" class="R"></path><path d="M792 273v-1c0-1 1-4 2-5v-1c5-11 13-21 20-31 2-3 6-7 9-10l-1 3c1-1 3-2 5-2l-10 12c-2 3-5 6-6 9l-3 5-7 10h2c2-1 5-1 7 0-2 1-3 2-3 4l-1 1h9c-8 0-15 0-22 6h-1z" class="O"></path><path d="M822 228c1-1 3-2 5-2l-10 12c-2 3-5 6-6 9l-3 5-7 10-4 4h0c2-3 3-6 5-9l2-4c4-9 12-17 18-25z" class="D"></path><defs><linearGradient id="AM" x1="839.275" y1="221.786" x2="856.196" y2="224.505" xlink:href="#B"><stop offset="0" stop-color="#6e7880"></stop><stop offset="1" stop-color="#a0a7b0"></stop></linearGradient></defs><path fill="url(#AM)" d="M850 209c3 0 6-1 9 0v1l-2 2 4-1v2h1 2c-2 2-4 2-6 3-1 0-2 1-2 1l2 1v3c-4 2-8 6-10 10l3 3-1 1c-2 5-3 10-4 15-1 0-2-1-3-2-5-2-10-4-15-5-1 0-4 0-5-1h-3c-1-1-1-1-1-2l-8 7c1-3 4-6 6-9l10-12 10-11c3-3 6-4 10-5l3-1z"></path><path d="M856 217l-14 5c-2 0-4 1-5 1l12-7c4-2 8-3 13-3h2c-2 2-4 2-6 3-1 0-2 1-2 1z" class="T"></path><defs><linearGradient id="AN" x1="833.429" y1="214.95" x2="838.358" y2="228.141" xlink:href="#B"><stop offset="0" stop-color="#2d3138"></stop><stop offset="1" stop-color="#494d57"></stop></linearGradient></defs><path fill="url(#AN)" d="M850 209c3 0 6-1 9 0v1l-2 2c-7 2-14 6-20 9-3 3-6 5-9 8-1 1-4 3-5 5h1l-5 6-8 7c1-3 4-6 6-9l10-12 10-11c3-3 6-4 10-5l3-1z"></path><path d="M850 209c3 0 6-1 9 0v1l-2 2c-7 2-14 6-20 9l1-2h0c2-3 4-3 6-4s4-1 6-2h0c2-1 3-2 4-3l-1-1-6 1 3-1z" class="H"></path><path d="M824 234c4-4 8-7 13-10l3 1c2 2 6 4 7 7l1-1 3 3-1 1c-2 5-3 10-4 15-1 0-2-1-3-2-5-2-10-4-15-5-1 0-4 0-5-1h-3c-1-1-1-1-1-2l5-6z" class="G"></path><path d="M847 232l1-1 3 3-1 1c-2 5-3 10-4 15-1 0-2-1-3-2 0-5 2-11 4-16z" class="B"></path><path d="M138 188c-9-2-18-2-27-2 6-4 13-7 20-9h1c2 1 3 0 6 0 4 0 9 0 13 2l2 1 3 1c8 4 16 7 23 12 32 22 48 59 61 93l45 117 9 21c2 5 5 11 6 16l1 1c1 5 4 9 5 13l1 2 1 3h0c0 2 1 2 1 4l1 2c1 1 0-1 1 1 2 4 3 8 5 13l1 1v1c1 3 2 6 4 9 1 4 3 7 4 10v2 1c1 1 1 1 1 2h1c1 4 3 9 4 13l2 2h0c0 1 0 2 1 3l2 4c0 1 1 2 1 3 0 0-1 0 0 1s1 2 1 3v1l1 1 2 7c0 1 1 3 2 5v-1c3 5 5 11 7 16l9 21 20 49 30 70 8 18 12 30 12 25c0 1 5 10 4 10 0 2 1 3 1 4l1 2c1 1 1 2 1 3l1 1c1 1 1 1 1 2l1 1v2l1 1v1h0l1 1v1l1 1v1c1 1 2 3 2 5l1 1c1 2 3 4 2 6l1 1s1 1 1 2l1 1c0 1 1 2 1 4 0 0 1 1 1 2l2 4c1 2 8 12 7 14l1 1h0v2h0v1l1 1v1l-2-1h0l-1 1h0 1c2 1 1 2 3 1h0 1c1 1 1 2 2 3h0c1 1 1 2 1 3l4 7h1l3 5 1 2 1 2h0l1 2c1 1 2 3 3 4l2 2c3 4 6 9 9 12 4 4 7 8 10 11l-4 20c-1 2-3 7-2 10h-1 0l-30-57c-2-2-3-6-4-8l-15-31-71-166-66-157-25-59-12-32c-3-4-4-9-6-14l-10-24-44-114c-2-2-4-4-5-6l-5-6c-3-6-8-12-12-17h1l-3-4c-2-2-4-4-5-6h0l1-1h2v-1l-2-2c-1-1-2-3-4-4l-2-2c-3-2-5-5-8-8s-6-5-10-8c-1-1-4-4-6-4h-1c-4-2-10-2-14-4s-7-6-11-8l-1-1 1-1c-1-2-2-2-3-3v-1z" class="C"></path><path d="M478 880l2 2 4 9 9 17c1-2 1-5 3-6s3-4 5-4l1-2h1 1c0-1 0 0-1-1s0 0 0-1l-6-7c-1-1-1-2-1-3-1 0-2-2-2-2l2 2c3 4 6 9 9 12 4 4 7 8 10 11l-4 20c-1 2-3 7-2 10h-1 0l-30-57z" class="L"></path><defs><linearGradient id="AO" x1="141.927" y1="252.891" x2="213.543" y2="223.959" xlink:href="#B"><stop offset="0" stop-color="#ccd2d8"></stop><stop offset="1" stop-color="#fcfdfc"></stop></linearGradient></defs><path fill="url(#AO)" d="M138 188c-9-2-18-2-27-2 6-4 13-7 20-9h1c2 1 3 0 6 0 4 0 9 0 13 2l2 1 3 1c8 4 16 7 23 12 32 22 48 59 61 93l45 117v-1c-1 0-1-1-1-2l-1-3-2-5c-1-1-1-2-2-4l-3-9-1-2-2-4c0-1-1-2-1-4l-2-5-2-4-3-8-1-2c-2-6-5-11-6-16l-2-4-2-4c0-1-1-3-1-4l-4-9-1-2-1-5-2-5-1-1v-1l-3-6h-1 0c-1-2-3-4-5-4h-2c-1-1-1-1-2-1v-1l1 5 4 10c1 3 2 5 3 7 0 2 1 2 1 4 1 2 2 5 3 7l3 8c0 2 1 2 1 4 1 2 2 5 3 8 1 1 1 1 1 2 2 3 2 5 3 8 1 0 1 1 1 2l2 4 9 23 1 2c1 3 2 6 4 10l1 3v1c2 3 3 7 4 10 1 1 2 3 2 5l2 3c0 1 1 3 1 4 1 1 1 2 1 3l1 1c0 1 1 1 1 2l1 4h0c-3-4-4-9-6-14l-10-24-44-114c-2-2-4-4-5-6l-5-6c-3-6-8-12-12-17h1l-3-4c-2-2-4-4-5-6h0l1-1h2v-1l-2-2c-1-1-2-3-4-4l-2-2c-3-2-5-5-8-8s-6-5-10-8c-1-1-4-4-6-4h-1c-4-2-10-2-14-4s-7-6-11-8l-1-1 1-1c-1-2-2-2-3-3v-1z"></path><defs><linearGradient id="AP" x1="191.6" y1="208.565" x2="173.579" y2="249.88" xlink:href="#B"><stop offset="0" stop-color="#262628"></stop><stop offset="1" stop-color="#4a5868"></stop></linearGradient></defs><path fill="url(#AP)" d="M138 188c23 3 46 17 60 36 8 10 14 21 20 33 2 5 5 12 7 18-2-2-4-4-5-6l-5-6c-3-6-8-12-12-17h1l-3-4c-2-2-4-4-5-6h0l1-1h2v-1l-2-2c-1-1-2-3-4-4l-2-2c-3-2-5-5-8-8s-6-5-10-8c-1-1-4-4-6-4h-1c-4-2-10-2-14-4s-7-6-11-8l-1-1 1-1c-1-2-2-2-3-3v-1z"></path><path d="M482 360v-2c0-1-3-2-4-3 3-4 5-8 7-12 6-15 9-33 12-48l12-75 5-42c1 2 1 6 1 8l2 17c-1-1-1-2-1-4l-1 10h0l-1 78v20c0 3 0 6 1 9l-1 76c0 6-1 14 0 21v-1l1-11v5c1-1 1-2 1-3h0v17 22 27 10 52c0 2 0 4-1 5l-1 50c0 9-2 19 0 28v5l-1 37c-1 2-1 5-1 8l-1-7v-3-1h-1l-1-3v1c0 4 2 13-1 16h-1l-2-15c0-2 0-3-1-4v-4c-1-2-1-4-1-6l-3-17h-1c0-2 1-5 0-7 0-4 1-8-1-12-1-2-4-4-6-5h-2c-1-1-2-1-3-1-1-2-1-3-1-5-1-1-1-1 0-2l5-13-1-2c-1-2-2-5-3-8-2-5-3-11-5-16-2-6-5-12-8-17l12-22c1-2 2-4 2-6v-1-1h0l-1 1-1 5c-1-4 2-9 3-13v-5l1-3c-1-2-1-2-3-3 0-2 1-3 2-4l-3-3-4-4c-2-2-5-3-8-3h-2v-1c1-1 1-3 3-3 4-1 8 1 12 2h0v-2c-2-4-5-7-8-10 1-2 0-2 0-4h1l1-1c1-2 1-4 0-6 1-2 1-3 1-5-2-2-4-3-6-5 0-2 2-2 3-3l-1-1c-1-1-2-1-3-2v-1c1-1 2-1 3-2-1-5-3-3-5-5 0-1 1-1 2-2h0c0-3-2-5-3-7v-1-1c0-1 0-1-1-1l3-1v-1l-2-2 1-1c-7-8-13-12-24-13h-3v-1l2-2c2-2 5-3 6-5 3 0 6 0 9-1 4-2 8-3 12-5 0-1 2-2 2-2 1-1 2-4 3-5 0-3 2-6 3-9l-1-2z" class="F"></path><path d="M512 499v-4c-1-1-1-20-1-23 1 0 1-1 1-2h0c1-3-1-6 1-8v31l-1 6z" class="Q"></path><path d="M513 392h1c0 6-1 14 0 21v43l-1-1h0v-63z" class="D"></path><path d="M494 381l1-3 2-2c1 1 1 1 1 2 1 3 0 7 0 10 0 2 1 3 0 5 0 2-1 3 0 4v2c-2-5-3-9-6-14 1-1 2-2 2-3v-1z" class="Q"></path><path d="M510 525c-3-7-3-15-4-21l-1-2c0-1 0-3 1-4 1 2 1 5 1 7l1-3h0c0 1 0 2 1 3v2h0 0l1-1c0 2 0 2 1 3v13l-1 3z" class="Y"></path><path d="M498 399h0c1 2 1 4 2 6l-1 1v-2c-1-1-1-2-1-3-1 1 0 1 0 3v3c-1-1-1-2-1-2 0-2-1-5-2-7h0c-1-3-2-5-3-7-1-1-2-3-2-4s1-2 1-4c1-2 2-3 3-5v-1h0v4 1c0 1-1 2-2 3 3 5 4 9 6 14z" class="I"></path><path d="M507 337v1l-2 62c-1 1 0 2-1 3h0v-1l-1-4v-1-1-1c1-2 1-6 1-8 1-2 0-9 0-12h1v5-3l1-1-1-1c1-2 1-5 1-7 1-2 0-7 0-9 1-2 1-18 1-22z" class="C"></path><path d="M478 418h0c3 4 6 8 8 12 1 1 1 1 1 2 4 5 7 11 10 16-2-8-5-15-9-23 0 1 1 1 1 2h1c0-1-1-3-1-4l-1-1v-1h1s1 1 1 2v1s1 1 1 2 1 2 1 3c1 1 1 1 1 2 1 1 1 2 1 4 1 0 0 1 0 2 4 9 6 19 8 29-1 1-1 0-1 1v1-1c-1-5-3-13-6-17l-8-17h-1c-1-1-2-2-3-4 0 0-1-1-1-2-1 0-1-1-1-2-1-1-3-3-3-4v-3z" class="K"></path><defs><linearGradient id="AQ" x1="486.761" y1="417.237" x2="479.739" y2="427.263" xlink:href="#B"><stop offset="0" stop-color="#787a7d"></stop><stop offset="1" stop-color="#a4a7a9"></stop></linearGradient></defs><path fill="url(#AQ)" d="M473 405c6 7 10 13 15 20 4 8 7 15 9 23-3-5-6-11-10-16 0-1 0-1-1-2-2-4-5-8-8-12h0c-1-1-4-5-5-6l-1 1v-1-1c0-1 0-1-1-1l3-1v-1l-2-2 1-1z"></path><defs><linearGradient id="AR" x1="455.097" y1="374.694" x2="487.364" y2="370.506" xlink:href="#B"><stop offset="0" stop-color="#95969a"></stop><stop offset="1" stop-color="#b9bbbf"></stop></linearGradient></defs><path fill="url(#AR)" d="M483 362l3-8c0 2 0 3-1 6h1c1 1 1 2 1 2s1-1 1-2 1-2 1-3l1-1v-1h1c0-1 0-2 1-3v-1h0c0 3-2 14-5 16-1 1-1 1-2 3s-4 4-6 6c-6 6-10 9-18 11-4 1-9 2-13 2 2-2 5-3 6-5 3 0 6 0 9-1 4-2 8-3 12-5 0-1 2-2 2-2 1-1 2-4 3-5 0-3 2-6 3-9z"></path><path d="M513 392l1-187 1 4-1 78v20c0 3 0 6 1 9l-1 76h-1z" class="b"></path><path d="M513 493c1 5 1 10 1 15v23c-1 7 0 14-2 21 0 3 1 6 1 9v17c0 7-1 15-2 23 0 2 1 8 0 10 0 4 0 9-1 13l-1-63c1 2 0 4 1 6v9-3l1-3c0-3 1-6 0-8v-1h1c0-2-1-32-2-36l1-3v-1c1-3 1-6 1-9 1-4 0-8 0-13l1-6z" class="I"></path><defs><linearGradient id="AS" x1="533.472" y1="460.418" x2="495.655" y2="436.164" xlink:href="#B"><stop offset="0" stop-color="#869c99"></stop><stop offset="1" stop-color="#c0bcd3"></stop></linearGradient></defs><path fill="url(#AS)" d="M514 413v-1l1-11v5c1-1 1-2 1-3h0v17 22 27 10 52c0 2 0 4-1 5l-1-5v-23c0-5 0-10-1-15v-31-7h0l1 1v-43z"></path><path d="M514 531l1 5-1 50c0 9-2 19 0 28v5l-1 37c-1 2-1 5-1 8l-1-7v-3-1c-1-9-2-19-1-29 1-4 1-9 1-13 1-2 0-8 0-10 1-8 2-16 2-23v-17c0-3-1-6-1-9 2-7 1-14 2-21z" class="c"></path><path d="M482 441v-3c-2-3-2-5-3-8l1-2c1 0 2 1 3 2l2 4c1 1 1 3 1 4 0 3 0 7 1 10 1 5 3 10 4 15 0 2 1 5 0 8l-2 3c-2 0-4-1-7 0-2-2-5-3-8-3h-2v-1c1-1 1-3 3-3 4-1 8 1 12 2h0v-2c-2-4-5-7-8-10 1-2 0-2 0-4h1l1-1c1-2 1-4 0-6 1-2 1-3 1-5z" class="K"></path><path d="M479 457c1-2 0-2 0-4h1c2 1 5 2 6 4v1l1 1c2 2 2 4 2 8h-1-1c-2-4-5-7-8-10z" class="I"></path><path d="M448 389c4 0 9-1 13-2 8-2 12-5 18-11 2-2 5-4 6-6s1-2 2-3c-1 5-3 8-6 11-1 2 0 0-1 1l-2 2a30.44 30.44 0 0 1-8 8h-1c-1 1-2 1-2 2v1c11 6 19 14 25 26h0l-2-2 1 2c-2-1-5-6-5-7l-1-2h0l-1-3c-2-1-3-3-5-3-2-1-2-3-4-3v-1c0-1-1-2-2-3-1 0-2-1-3-1v2s1 0 2 1 3 2 3 4c1 1 3 2 4 4l3 3 2 4 1 1 4 7h-1v1l1 1c0 1 1 3 1 4h-1c0-1-1-1-1-2-5-7-9-13-15-20-7-8-13-12-24-13h-3v-1l2-2z" class="S"></path><path d="M449 392c3-1 6-2 9-1 14 5 23 18 30 30v1l1 1c0 1 1 3 1 4h-1c0-1-1-1-1-2-5-7-9-13-15-20-7-8-13-12-24-13z" class="J"></path><path d="M489 481c1 2 2 3 3 5 14 23 16 50 17 75l1 63c-1 10 0 20 1 29h-1l-1-3v1c0 4 2 13-1 16h-1l-2-15c0-2 0-3-1-4v-4c-1-2-1-4-1-6l-3-17h-1c0-2 1-5 0-7 0-4 1-8-1-12-1-2-4-4-6-5h-2c-1-1-2-1-3-1-1-2-1-3-1-5-1-1-1-1 0-2l5-13-1-2c-1-2-2-5-3-8-2-5-3-11-5-16-2-6-5-12-8-17l12-22c1-2 2-4 2-6v-1-1h0l-1 1-1 5c-1-4 2-9 3-13v-5l1-3c-1-2-1-2-3-3 0-2 1-3 2-4z" class="T"></path><path d="M495 499h2c2 5 5 10 6 15-1 0-1-1-2-2v-2l-1-1c0-2-2-4-3-5h-1c0-2-1-3-1-5z" class="C"></path><path d="M495 563c1 1 1 2 2 3 0 1 0 1 1 2l5 13-2-3-1-1c-1-3-4-5-6-8 1-2 1-4 1-6z" class="X"></path><path d="M501 578l2 3c2 4 4 9 4 14 1 5 1 10 1 15 0 2 1 5 0 7l-1 1c-1-4 0-9-1-14v-6c-1-2-1-3-1-4-1-3-2-5-2-8-1-3-2-6-2-8z" class="I"></path><defs><linearGradient id="AT" x1="501.666" y1="577.47" x2="495.835" y2="583.53" xlink:href="#B"><stop offset="0" stop-color="#afb1b5"></stop><stop offset="1" stop-color="#ced1d2"></stop></linearGradient></defs><path fill="url(#AT)" d="M494 569c2 3 5 5 6 8l1 1c0 2 1 5 2 8 0 3 1 5 2 8 0 1 0 2 1 4v6l-2-5c0-1-1-3-2-4-1-3-2-7-4-10-2-2-3-4-3-7h0c0-3 0-4-2-6l1-3z"></path><path d="M497 504c1 1 3 3 3 5l1 1v2c1 1 1 2 2 2 1 3 1 6 2 9 1 6 1 12 1 17l2 51-6-17c-2-4-3-9-6-13-1 0-3-3-3-4-1-1 2-6 3-8 3-9 6-20 4-30 1-3-3-7-4-11h1c1-2 0-2 0-4z" class="Y"></path><defs><linearGradient id="AU" x1="517.016" y1="663.354" x2="485.096" y2="580.028" xlink:href="#B"><stop offset="0" stop-color="#d1d2d4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AU)" d="M493 572c2 2 2 3 2 6h0c0 3 1 5 3 7 2 3 3 7 4 10 1 1 2 3 2 4l2 5c1 5 0 10 1 14l2 32v1c0 4 2 13-1 16h-1l-2-15c0-2 0-3-1-4v-4c-1-2-1-4-1-6l-3-17h-1c0-2 1-5 0-7 0-4 1-8-1-12-1-2-4-4-6-5h-2c-1-1-2-1-3-1-1-2-1-3-1-5-1-1-1-1 0-2l5-13c1-2 2-3 2-4z"></path><path d="M493 572c2 2 2 3 2 6h0c0 3 1 5 3 7 2 3 3 7 4 10-1-2-3-4-5-5l-6-3c1-2 2-3 3-5 0-2 0-3-1-5-2 2-2 5-3 7-1 1-2 2-2 3s-1 1-2 2l5-13c1-2 2-3 2-4z" class="Y"></path><path d="M486 591v1c6 3 14 2 16 10 1 6-1 13-2 19h-1c0-2 1-5 0-7 0-4 1-8-1-12-1-2-4-4-6-5h-2c-1-1-2-1-3-1-1-2-1-3-1-5z" class="U"></path><path d="M489 481c1 2 2 3 3 5v1l1 2-1 4h2l3 6h-2c0 2 1 3 1 5h1c0 2 1 2 0 4h-1c1 4 5 8 4 11 2 10-1 21-4 30-1 2-4 7-3 8 0 1 2 4 3 4l-1 2c0 2 0 4-1 6l-1 3c0 1-1 2-2 4l-1-2c-1-2-2-5-3-8-2-5-3-11-5-16-2-6-5-12-8-17l12-22c1-2 2-4 2-6v-1-1h0l-1 1-1 5c-1-4 2-9 3-13v-5l1-3c-1-2-1-2-3-3 0-2 1-3 2-4z" class="G"></path><path d="M489 481c1 2 2 3 3 5v1l1 2-1 4h2l3 6h-2c0 2 1 3 1 5h1c0 2 1 2 0 4h-1c1 4 5 8 4 11l-4-8c0 2 1 4 0 6l-4-5c-2 0-3-1-5-1h-1c1-2 2-4 2-6v-1-1h0l-1 1-1 5c-1-4 2-9 3-13v-5l1-3c-1-2-1-2-3-3 0-2 1-3 2-4z" class="Q"></path><path d="M494 493l3 6h-2 0c-1 0-1 1-1 2l-2 2 1 1c2 1 2 3 3 5l-1 1c0-2-1-3-1-4l-2-2-2 1 2 2v1c0 2-1 3 0 4-2 0-3-1-5-1 1-2 2-4 2-5 0-3 2-6 4-9 0-1 0-2 1-4z" class="B"></path><path d="M489 481c1 2 2 3 3 5v1l1 2-1 4h2c-1 2-1 3-1 4-2 3-4 6-4 9 0 1-1 3-2 5h-1c1-2 2-4 2-6v-1-1h0l-1 1-1 5c-1-4 2-9 3-13v-5l1-3c-1-2-1-2-3-3 0-2 1-3 2-4z" class="N"></path><defs><linearGradient id="AV" x1="259.875" y1="594.014" x2="842.508" y2="118.902" xlink:href="#B"><stop offset="0" stop-color="#c6cfd5"></stop><stop offset="1" stop-color="#f3eeec"></stop></linearGradient></defs><path fill="url(#AV)" d="M131 177c3-1 6-2 9-2 5-1 11 0 16 0h36l109-1h107 41l-5 5c-7 1-14 1-21 3-8 2-16 6-23 10-2 1-4 3-6 4-1 2-3 3-5 4-1 3-4 6-7 8-6 11-13 23-17 35-3 9-6 19-8 28-4 14-7 28-9 43-4 29-1 58 5 86l2 11c1 2 1 4 1 6 0 1-1 1-1 1l-1 1c-1 1-2 2-3 2-2 1-3 2-4 3v1l-1-2-1 1 3 6c2 2 4 3 7 3h1c2 0 3-4 5-5 1-1 2-1 3 0s1 3 1 5l1-1-1-2c0-1 0-1-1-3h0c1-2 0-6 0-8-1-3-1-7-1-10 1-2 0-3 1-5 0-3 0-6 1-9 0-2 1-6 2-7l1 1c0 2 1 5 2 7l2 5c0 1 1 1 2 2h0l1 3c1 3 2 6 4 9 1 2 1 4 1 6h0l-2-1h0c-1 1-1 0-2 1l9 9c2 2 4 3 5 6l2 6h-1 0-1 0l-1 3v4 4h1l2 3 6 7c-1 1-2 1-4 1l3 2 1 1 14 12c4 3 7 6 11 9l2-2 12 12 16 16c2 2 5 5 6 7 2 2 9 13 9 13h1c5-6 8-12 12-18 1-3 3-4 5-7l1-5 1-1h0v1 1c0 2-1 4-2 6l-12 22c3 5 6 11 8 17 2 5 3 11 5 16 1 3 2 6 3 8l1 2-5 13c-1 1-1 1 0 2 0 2 0 3 1 5 1 0 2 0 3 1h2c2 1 5 3 6 5 2 4 1 8 1 12 1 2 0 5 0 7h1l3 17c0 2 0 4 1 6v4c1 1 1 2 1 4l2 15h1c3-3 1-12 1-16v-1l1 3h1v1 3l1 7c0-3 0-6 1-8 0 4-1 10 1 13h0c1-2 0-4 1-7 1-1 1-2 1-3l1 1-2 17 1 1c1 2 1 3 1 5l2 1v8h0v-8h0c1-5 1-10 3-14-1-3 2-9 3-12 0-1 1-2 1-3 2-5 5-9 8-14l5-7 1-1v-1l1-1c1 1 2 2 2 3 1-1 2-2 2-4h0 1v1c1 1 0 0 1 2l1 1c4 2 7 4 12 7 2 2 5 4 8 7 2 1 3 3 5 3l3-5v-2l31-75 22-60 2-5c2 0 2-1 3-2h0c1-2 2-4 2-6 2-2 2-5 3-8l-1-4 14-45 5-27c1-9 3-19 5-28 1-12 2-25 2-37 0-35-4-69-15-101-6-17-13-35-28-46-5-4-11-7-17-9-4-1-7-1-10-2-1 0-4 0-4-1-1 0-2-2-3-3l-5-6h-1c-1-1-2-2-2-4h1c1-1 3-1 5-1h16 72 139 39 21l9 3c7 3 13 5 20 9-8 0-16 0-24 1-23 1-41 12-56 28-16 17-26 38-35 59l-9 21-31 77-75 183-24 57c-1 2-6 18-7 19l-49 121h0c-1-1-2-2-2-3v-2c-1 1-2 3-4 3-1 1-2 3-3 4l-67 153c-3-3-6-7-10-11-3-3-6-8-9-12l-2-2c-1-1-2-3-3-4l-1-2h0l-1-2-1-2-3-5h-1l-4-7c0-1 0-2-1-3h0c-1-1-1-2-2-3h-1 0c-2 1-1 0-3-1h-1 0l1-1h0l2 1v-1l-1-1v-1h0v-2h0l-1-1c1-2-6-12-7-14l-2-4c0-1-1-2-1-2 0-2-1-3-1-4l-1-1c0-1-1-2-1-2l-1-1c1-2-1-4-2-6l-1-1c0-2-1-4-2-5v-1l-1-1v-1l-1-1h0v-1l-1-1v-2l-1-1c0-1 0-1-1-2l-1-1c0-1 0-2-1-3l-1-2c0-1-1-2-1-4 1 0-4-9-4-10l-12-25-12-30-8-18-30-70-20-49-9-21c-2-5-4-11-7-16v1c-1-2-2-4-2-5l-2-7-1-1v-1c0-1 0-2-1-3s0-1 0-1c0-1-1-2-1-3l-2-4c-1-1-1-2-1-3h0l-2-2c-1-4-3-9-4-13h-1c0-1 0-1-1-2v-1-2c-1-3-3-6-4-10-2-3-3-6-4-9v-1l-1-1c-2-5-3-9-5-13-1-2 0 0-1-1l-1-2c0-2-1-2-1-4h0l-1-3-1-2c-1-4-4-8-5-13l-1-1c-1-5-4-11-6-16l-9-21-45-117c-13-34-29-71-61-93-7-5-15-8-23-12l-3-1-2-1c-4-2-9-2-13-2-3 0-4 1-6 0h-1z"></path><path d="M299 317v-6c1-1 0-4 1-5l2 1c-1 2-1 4-1 5 0 2-1 3-2 5z" class="K"></path><path d="M631 504c2 0 2-1 3-2h0l-4 12-1-5 2-5z" class="c"></path><path d="M657 537l5 5-2 2c-3-1-5-4-7-7 2 0 3 1 4 0z" class="X"></path><path d="M456 601l1 1v4c1 0 5-1 7-1v1l-3 1c-1 1-4 4-5 4h-1 0c0-1-1-3 0-3v-2l1-1v-4z" class="K"></path><path d="M231 236c0-1 0-1 1-2 1-3 4-5 7-7 1 0 2-1 3 0h-1l2 1h1c1 1 3 1 4 1h0c-4 0-7 0-11 2-1 0-2 1-4 2l-1 3h-1z" class="V"></path><path d="M374 504c1 4-1 7-2 11-1 2-1 5-2 8v-7-1c0-4 0-8 4-11z" class="R"></path><path d="M875 181c5 0 10 1 15 1h1l-1 1c-1 0-1 0-2 1h0l-1 1h-2-1c-1-1-1-1-2-1h0-3l-1-1h-1-1-1v-1h0-1-5l6-1z" class="J"></path><path d="M649 525v-2c-1-9 1-18 4-26 1-4 2-8 4-11-4 11-8 22-7 34v2l-1 3z" class="D"></path><defs><linearGradient id="AW" x1="646.94" y1="525.189" x2="657.12" y2="533.36" xlink:href="#B"><stop offset="0" stop-color="#737378"></stop><stop offset="1" stop-color="#899093"></stop></linearGradient></defs><path fill="url(#AW)" d="M650 522c2 5 4 10 7 15-1 1-2 0-4 0-2-4-3-8-4-12l1-3z"></path><path d="M747 246v1l-1 2c1 0 2-1 3-1v-1c1 0 1-1 2-1h1c0-1 1-1 2-1h0c1 0 2-1 2-1h2c2-1 6-1 9 0-1 0-1 0-1 1 1 0 2 0 2 1h1l2 1h0c2 1 3 2 3 3-3-2-6-4-9-5h-1c-6-1-11 1-16 4-1 1-2 1-2 2s-1 1-1 1l-5 4v-2l7-8z" class="Q"></path><path d="M706 295h1c-1 5-7 10-12 13-3 2-10 4-14 5 0-1 1-1 1-1 10-4 17-9 24-17z" class="N"></path><path d="M232 236l1-3c2-1 3-2 4-2 4-2 7-2 11-2l2 1c-1 1 0 1-1 1-4 1-7 3-10 4-2 1-4 2-5 1h-2z" class="F"></path><path d="M708 216c2 1 4 3 6 4s4 2 6 2c1-1 3-1 4-1l1-1c3-2 5-5 8-7h1c-2 3-6 7-9 9l-1 1c-1 1-2 1-3 2h0c-2 2-2 2-4 2-3-4-7-7-9-11z" class="B"></path><path d="M432 624l-1 1c0 1-1 2-2 4l-1 2c2-2 3-4 6-5-2 2-5 4-6 6h0c-2 1-3 3-5 5-8 4-14 6-23 6l13-4 6-3h1l2-1v-1c2-1 4-3 6-5h0l1-2 3-3zm223-191l5-21c0-2 1-3 2-4-1 5-2 12-3 17l-1 4c0 1 0 2-1 2 0 2-1 4-1 6-1 1-1 2-1 4l-1 1v2s0 1-1 1c-1 1-1 2-1 4v1l-1 2c-1 1 0 1-1 2v3c-1 1-1 1-1 2-1 2-1 3-2 5 1-3 1-5 2-8 0-1 0-2 1-3 2-6 5-14 5-20z" class="C"></path><path d="M370 499c0-1 0-2-1-3 0-1 0-3-1-4v-1-1l1 1c2 5 3 9 5 12v1c-4 3-4 7-4 11v1h0c-1-1-2-3-2-4-1-2-3-4-4-7 2-1 4-1 6-3v-3z" class="J"></path><path d="M698 201v-1-3c1-2 3-4 5-5h2l4-3h2l2-1c-2 3-5 5-7 8l-1 1h-1l1 1h7-1c0-1 0 0-1-1h0c1 0 2 0 3 1 1 0 2 0 3 1-4 0-8-1-12 0-2 1-4 0-6 2z" class="D"></path><path d="M279 301c3-1 3-2 5-4-1 2-1 4-1 6h1c-3 4-15 9-20 9h-2c1-1 2-1 3-2 5-2 10-5 14-9z" class="X"></path><path d="M752 211c1-1 0-1 1-1 5-3 10-6 14-10 2-1 4-3 6-4 1 0 0 0 2-1l2-1h1c1-1 1-1 2-1h1c1-1 1-1 2-1h4c1-1 6-1 7-1 2 1 4 1 6 2v1c2 0 2 1 3 2-5-2-10-4-15-3-2 0-3 0-5 1l-7 2c-1 0-3 1-4 2-6 3-9 9-14 12-2 0-4 0-6 1z" class="C"></path><path d="M272 202c-1-1-1-2-2-3 1-2 1-2 2-3 3-3 13-9 17-9l-4 4-8 7c5 0 10-1 15 0-5 1-16-1-19 2 0 1 0 1-1 2z" class="P"></path><path d="M677 416h1c1 6 1 9-1 14-1 1-2 2-3 2-1 1-4 0-5-1-2 0-3-1-4-2 1-1 1-2 2-2 1-2 0-4 1-6 3 3 4 4 8 4l2-2c0-3 0-4-1-7z" class="U"></path><path d="M655 433c0 6-3 14-5 20-1 1-1 2-1 3-1 3-1 5-2 8l-8 24-1-4 14-45 3-6z" class="B"></path><path d="M299 270c-1 0-1-1-1-2l-1-4 1-5v-1-3c0-1 0-1 1-2v-3l1-2v-2l1-2v-1l1-2v-2l1-2c0-1 0-3 1-3v-1c1-1 1-1 1-2 1-1 2-1 3-2h1l1-2 1-2c1-2 0-1 1-2l2-2v-1l1-1v-1c1-1 2-2 2-3h1l2-2c-12 17-20 34-20 55l-1-1c0-4 0-7 1-11 0-3 1-7 2-9l2-9v-1c-1 2-2 4-2 6-3 9-5 18-3 27zm-26-91c1 0 5-1 6 0s2 1 3 1h-81l-1-1c8-1 16 0 24 0h49z" class="F"></path><path d="M399 674c3 2 5 8 7 11l7 13c3 6 5 12 10 17h0-2l-2-1c-1 0-2-1-2-1-3-4-5-9-7-13l-11-26z" class="T"></path><path d="M701 234c2 0 3 2 4 3 5 9 11 23 8 34 0 0 0 1-1 1-2-13-5-26-11-38z" class="P"></path><path d="M687 210c1-1 1-1 2-1h1l1 1s1 0 2 1h0l4 4 1 2c4 5 9 12 11 19l3 6c1 1 1 2 1 3v1c1 0 1 1 1 1l-1 1c-5-11-11-24-20-33-2-2-4-4-6-5z" class="F"></path><path d="M467 605l1 1c-2 8-5 14-8 21-1 2-1 2-1 3s0 2-1 2c0-2-1-4-1-6-1-6 1-11 5-16 2-1 4-3 5-5h0z" class="U"></path><path d="M639 572c0 1 0 1-1 1l-1-5v-1l-2-8-2-6v-2-3l-1-5c-1-2 0-5-1-8h0c1-2 0-6 1-8v-1c0-1 1-2 1-3l1-6 1-4c1-3 3-6 4-9l2-4 1 1-3 6c-1 1-1 2-1 3-2 7-4 15-4 23-1 13 2 26 5 39z" class="J"></path><path d="M299 270c-2-9 0-18 3-27 0-2 1-4 2-6v1l-2 9c-1 2-2 6-2 9-1 4-1 7-1 11l1 1h0c1-1 1-8 2-10h0c0-2 1-3 2-5 0 1 0 1 1 2l-1 4c0 1 0 2-1 3v4l-1 1 1 1c1-1 1-2 2-3 0 2 0 3-1 5v2c-1 1 0 3-1 4 0 3 0 5-1 7l-1 1c-1-3-1-6-2-10v-4z" class="B"></path><defs><linearGradient id="AX" x1="690.126" y1="249.013" x2="702.874" y2="252.987" xlink:href="#B"><stop offset="0" stop-color="#9ea1a7"></stop><stop offset="1" stop-color="#babdbd"></stop></linearGradient></defs><path fill="url(#AX)" d="M686 233c4 6 17 22 17 29h-1c-2-2-3-5-5-7-1-1-3-2-3-4-1-1 0-1-1-2-2-2-4-4-8-5-3-1-6-1-9 1-3 3-4 6-5 10 0 4 0 7 1 10l3 6v1c-1-1-1-2-2-3-2-5-4-13-2-18v-1c1-1 1-2 2-3 1-2 4-4 6-4 6-1 10 1 14 4l-7-13v-1z"></path><path d="M705 237c3 1 6 8 7 11 1 2 1 3 2 5v4l1 1v2-5c-1-2-1-4-2-7 2 2 2 2 2 4v1h1c0 1-1 4 0 5v7c0 3-1 7-2 9-1 5 1 10 1 15 1 0 1-1 2-2v3c-2 2-1 8-1 11-1 1-1 3-2 3 0-2 1-5 0-7h-1v-1-1h0v-2c1-3 1-5 1-8-1-2 0-4 0-6s-2-4-2-6v-1c1 0 1-1 1-1 3-11-3-25-8-34zm-413-39c-5-1-10 0-15 0l8-7c4 0 11 3 15 5h-2l1 1h8c2 0 5 1 7 2h-10c-4-1-9 0-12-1z" class="B"></path><path d="M315 233h1c2-4 5-7 8-11 1-2 2-2 3-3-2 5-5 9-7 13 0 1-1 2-1 2l-5 9-3 6c-1 3-3 4-4 7l-2 5v2 1 1c-1 1-1 2-2 3l-1-1 1-1v-4c1-1 1-2 1-3l1-4 2-4 1-2c1-6 4-11 7-16z" class="I"></path><path d="M709 381c1 0 2-1 4-1 2 2 3 4 3 6 0 3-1 7-1 10-2-1-5-3-8-3-1 1-2 1-3 2v-1-3l1-1v-1c0-3 1-6 4-8zM421 717l1 1h6l1-1h2c1 1 2 2 2 4h1c1 2 0 4 0 6l-1 1c-1 1-4 1-5 0-3 0-5-2-7-3-1-3-2-5-3-7l1-1h2z" class="G"></path><path d="M499 658l1-1v1l1 3v2c1 1 0 3 1 4 1 0 1 1 1 2s-1 1-1 2 0 1 1 2v1c0 1 0 1 1 2v1h0v11 2 1h-1c0 3 0 3-2 4-1 1-3 2-4 2-2 0-3 1-5 1h-1-5-1-1s-1 0-1-1c2 0 6 0 8-1 4-1 6-5 7-9 4-8 3-15 3-24 0-1-1-4-2-5z" class="K"></path><path d="M497 697v-1-1c1-2 3-4 5-5l1 1c0 3 0 3-2 4-1 1-3 2-4 2z" class="E"></path><path d="M384 512c1 1 1 3 2 5 1 10 2 18 0 28-1 8-5 16-10 24-2 3-4 6-7 8h0c0-1 2-4 2-6 3-4 5-8 8-12 5-11 8-26 6-38l-1-9z" class="L"></path><path d="M675 271c3 3 5 4 9 5h2v1c-1 3-2 8-4 11-1 1-2 1-3 1-2 0-3 0-4-1-2-2-3-8-2-10s2-3 2-5v-1-1zm2 191c3 1 5 2 7 4 0 5 0 8-4 13h0c-3-1-5-2-8-4 0-3 0-5 1-8 1-2 1-4 4-5zm87-191c1 1 2 1 3 2 1 2 3 4 3 6 0 3-2 5-3 7-2 1-5 2-7 2-1-1-1-2-1-3-1-2-4-11-3-12 1 0 1 0 2 1 2 0 4-1 5-2l1-1z" class="G"></path><path d="M227 213l3-1h0c0 1-1 1-1 2v2c-1 1-3 3-4 3l-1 1c-1 1-2 0-4 0-1-1-2-1-3-2v-1c-1-1-2-2-3-2v-1h0v-1-1h-1c-1-2 0-1-1-2h0 0v-1c-1 0 0 0-1 1-3-3-6-8-7-11v-1-1h0v-2l1-1v-1c0-1 2-3 3-3l1-1h1 1c1-1 1-1 2-1h3c1-1 1-1 2-1h5l2 1h1l4 2c2 0 3 1 4 1h1v1c1 0 2 1 2 1 3 2 8 5 9 9-2-1-4-3-6-4 0-1 0 0-1-2 0-1-1-1-2-2h-1l-3-2c-1-1-2-1-4-2 0 0-1 0-2-1h-1-2v-1c-3 0-6 0-9 1h-3-1c-2 1-4 2-5 4s-1 4 0 7c2 2 3 4 6 6 5 2 5 11 11 12l4-4v-1z" class="B"></path><path d="M633 192c2 2 3 3 4 5h1c1 0 2 1 2 1 4 0 8 0 12 1l9 3-3 2h-1v1l-1 2c-2 2-3 5-6 6-2-2-2-5-4-7-3-3-6-5-9-7 0-1-1-1-1-2-2-1-3-3-3-5z" class="X"></path><path d="M652 206s-1 2-3 2v-2c-1-1-1-3-1-4h4l1 1c0 1 0 2-1 3z" class="F"></path><path d="M652 202l-1-1c-1 0-2 0-3 1l-1 1h-1l-7-4 1-1c4 0 8 0 12 1l9 3-3 2h-1c-2 0-3 1-5 2 1-1 1-2 1-3l-1-1z" class="P"></path><path d="M244 272l3 1c3 2 4 0 8 0-1 4-2 12-4 15-3 0-5 0-7-2s-3-5-3-8 1-4 3-6z" class="G"></path><path d="M701 234c-1-1-3-4-3-5-4-6-7-12-11-17-6-7-15-15-22-19h-1c0-1 1-1 1-1l-1-1c3 1 5 3 7 4 6 5 11 10 16 15 2 1 4 3 6 5 9 9 15 22 20 33h0c1 3 1 5 2 7v5-2l-1-1v-4c-1-2-1-3-2-5-1-3-4-10-7-11-1-1-2-3-4-3z" class="L"></path><path d="M723 353c1-1 2-2 3-2 2-1 4 0 6 1 2 2 3 3 3 6 0 2-1 5-3 7-1 1-2 2-4 2s-3-1-4-3c-2-3-2-7-1-11z" class="G"></path><path d="M686 234c-3-2-5-5-8-7-2 1-4 3-5 5-2 3-3 4-6 5-2 0-3 0-3-2-2-1-2-3-2-5 1-3 4-5 6-6h1c2-1 4-2 7-1 4 1 8 6 10 10v1z" class="X"></path><path d="M672 224h4v1c-4 3-4 8-8 10 0 1-1 1-2 1-1-1-2-1-2-3 0-1-1-3 0-4 2-3 4-4 8-5z" class="J"></path><path d="M750 239c5-3 9-6 14-6 6 1 13 5 19 6-3 1-12-3-15-4h-8c-4 3-10 7-13 11l-7 8v2c-2 2-4 5-5 7 0 1-1 2-2 3-2 2-3 3-4 5l-1 1c2-10 8-18 14-25l2-2 6-6z" class="D"></path><path d="M642 501v-1l2-4c1-1 1-1 1-2 0-3 4-6 5-8l1 1-5 8c-2 3-4 6-5 10-1 1-1 3-2 5l-1 6c-2 8-1 18-1 26l2 22c0 2 1 5 0 8-3-13-6-26-5-39 0-8 2-16 4-23 0-1 0-2 1-3l3-6z" class="c"></path><path d="M284 349c3 0 5 1 7 3 2 1 2 3 2 6 0 4-2 6-5 9h-1c-2 0-3-1-5-2-2-2-2-5-2-8 0-4 1-5 4-8z" class="G"></path><path d="M701 363l1 4-8 26-2 6-10 32-1-1 3-7v-3l1-2c1-2 0-1 1-2v-2l1-1v-2c0-1 1-2 1-3l3-9c-1 1-1 2-2 2v-1l-1 1c-2 1-3 3-4 4l-1-1c0 1-1 2-1 3l-2 3v1c-1 1-2 3-1 5v2c0 2 1 5 0 7v2 1c-1 0-1 1-1 2h-1 0c2-5 2-8 1-14v-4c2-4 5-8 7-12 6-12 13-25 16-37z" class="J"></path><path d="M660 537c-2-2-3-5-4-7v-1l-1-3c-1-3-1-6-1-9 0-11 2-22 7-31 3-5 6-9 11-10-1 3-4 4-6 7-5 8-6 15-8 24-1 2-2 5-1 7h0l2-1c0 1 0 1 1 2 0 1 0 2-1 3l-2 2c0 4 1 7 2 11 0 2 1 4 1 6zm44-143v1l-3 12-9 33-6 18c-1 2-2 5-2 8-2-2-4-3-7-4l2-1h2c2-1 3-6 3-8l6-20c2-5 3-10 5-14 1-1 1-2 2-3l2-8 3-10c1-1 1-1 1-2v-1l1-1z" class="N"></path><path d="M682 222h0c-1-3-3-5-4-7h-1v-1l3 3 1-1-5-5 1-1 4 6c1 1 2 1 3 3l-1 1c2 2 7 6 8 10 0 1 1 2 2 3h0c1-1 1-1 1-2 0 1 1 1 1 1 4 6 8 13 10 20 2 5 2 11 3 16 0 3 1 6 2 9v19 5h-1l-1-6h-1-1c5-11 2-21 0-32l-1-5c-3-4-5-10-7-14h0c-5-8-10-15-16-22z" class="P"></path><path d="M698 244c1 0 3 1 4 2v1c2 4 3 7 3 11-3-4-5-10-7-14z" class="X"></path><path d="M725 284c3 4 1 13 2 18h0l1 22 3 18c1 3 1 6 2 9l-1 1c-2-1-4-2-6-1-1 0-2 1-3 2v-1c0-3-1-6-2-9v-2c1 3 2 5 4 8h1v-4c-1-1-1-2 0-3h0v2h1c-1-2-1-4 0-5v-2-3c-1-1-1-1-1-2v-3c-1-1-1 0-1-1s1-2 0-3v-1-5h-1l1-3v-32z" class="R"></path><path d="M725 316c1 6 1 12 2 18 1 5 2 11 4 16-1 1-4 1-5 0-1 0-1 0-1-1h1v-4c-1-1-1-2 0-3h0v2h1c-1-2-1-4 0-5v-2-3c-1-1-1-1-1-2v-3c-1-1-1 0-1-1s1-2 0-3v-1-5h-1l1-3z" class="K"></path><path d="M660 197c3 0 4 0 6 2 1 0 1 1 2 1 11 7 21 20 27 32 0 0-1 0-1-1 0 1 0 1-1 2h0c-1-1-2-2-2-3-1-4-6-8-8-10l1-1c-1-2-2-2-3-3l-4-6-1 1 5 5-1 1-3-3v1h1c1 2 3 4 4 7h0c-3-2-6-5-9-8-1-1-3-2-4-4-3-3-8-4-11-6l3-2c4 2 7 4 11 6-2-4-8-8-12-11z" class="N"></path><path d="M701 207c-1-2-2-4-4-5-1-1-1-2-2-3v-3c1-1 3-3 4-3 1-1 1 0 2-1l4-2s1 0 2-1c2-2 6-5 10-6l1 1-4 4h-1l-2 1h-2l-4 3h-2c-2 1-4 3-5 5v3 1h1c0 1 0 1 1 1 0 1 1 1 2 1l8 3c3 1 7 3 10 5l7 4c-2 2-5 4-8 3h-1s-1 0-2-1h-1l-3-1c0-1-1-1-1-2l-3-1c-1-1-1-2-3-2 0-1-1-1-2-2l-1-2h-1z" class="L"></path><path d="M283 178c4 0 9 0 14-1 6 0 13 2 20 2 6 0 11-1 17-1 2 0 7 1 8 0 3 0 17 0 18-1h11c1 0 3 1 4 0h1 3 0c2 0 5-1 6 0h1 1 1l1 1h0c-2 0-4 0-6 1l-2-1c-1 0-2 0-3 1l-1-1h0-1-1c-3 1-7 1-10 1-1 0-2-1-3 0h-14s5 1 7 1h12c3 0 10-1 12 0h0c2-1 2 0 4 0h6 12 5c2 0 7 0 9-1h3c3-1 6 0 9-1h3v1c-3 0-5 0-7 1h-8-2l-1 1c-1 0-5-1-6 0h-1-6-10-14l-58-1c-6 0-12 0-18 1h0c0-1-1-1-2-1h0c-5-2-10-1-14-2z" class="F"></path><defs><linearGradient id="AY" x1="306.522" y1="234.58" x2="314.571" y2="236.808" xlink:href="#B"><stop offset="0" stop-color="#919496"></stop><stop offset="1" stop-color="#aeb4b7"></stop></linearGradient></defs><path fill="url(#AY)" d="M320 213c4-5 8-10 15-12-1 1-2 2-2 3s-1 1-2 2c-1 2 0 0-1 1-1 2-3 4-5 6-6 8-14 18-17 28h0c0 3-2 6-2 10h1l-2 4c-1-1-1-1-1-2-1 2-2 3-2 5h0c-1 2-1 9-2 10h0c0-21 8-38 20-55z"></path><path d="M304 253c1-4 2-8 4-12 0 3-2 6-2 10h1l-2 4c-1-1-1-1-1-2z" class="K"></path><path d="M633 192l-1-2c1-1 1-1 2-1 4 1 8 0 13 1 3 0 9 1 12 4 3 1 7 3 9 6-1 0-1-1-2-1-2-2-3-2-6-2 4 3 10 7 12 11-4-2-7-4-11-6l-9-3c-4-1-8-1-12-1 0 0-1-1-2-1h-1c-1-2-2-3-4-5z" class="J"></path><path d="M649 193l2-1h0c2 0 3 0 4 1 1 0 3 0 4 1 3 1 7 3 9 6-1 0-1-1-2-1-2-2-3-2-6-2-4-2-7-3-11-4z" class="P"></path><path d="M633 192l-1-2c1-1 1-1 2-1 4 1 8 0 13 1 3 0 9 1 12 4-1-1-3-1-4-1-1-1-2-1-4-1h0l-2 1c-5-1-10-1-15-3l4 7h-1c-1-2-2-3-4-5z" class="L"></path><path d="M743 199c2 1 2 1 2 3-3 4-8 7-11 11h-1c-3 2-5 5-8 7l-1 1c-1 0-3 0-4 1-2 0-4-1-6-2s-4-3-6-4c-2-2-7-7-7-9h1l1 2c1 1 2 1 2 2 2 0 2 1 3 2l3 1c0 1 1 1 1 2l3 1h1c1 1 2 1 2 1h1c3 1 6-1 8-3l-7-4c4 0 9 0 12-2l7-6-1-1c-2 0-5 1-6 1-3-1-7 0-9-1h0-1l-1-1h0l-1-1h2-1 10l1-1h11z" class="I"></path><path d="M732 199h11c0 1-1 1-2 1l-20 1-1-1h2-1 10l1-1z" class="W"></path><path d="M453 624l1 1c0 1 1 2 0 4 0 8-2 18 0 27 0 3-1 6 0 9l11 68c0 1 0 0 1 1 1 4 1 8 1 12l6 25c0 4 3 10 2 13-3-9-5-19-7-28-6-25-10-50-14-75l-2-21v-17-7c1-3 0-5 1-7v-5z" class="L"></path><path d="M716 199h11 5l-1 1h-10 1-2l1 1h0l1 1h1 0c2 1 6 0 9 1 1 0 4-1 6-1l1 1-7 6c-3 2-8 2-12 2-3-2-7-4-10-5l-8-3c-1 0-2 0-2-1-1 0-1 0-1-1h-1c2-2 4-1 6-2 4-1 8 0 12 0z" class="K"></path><path d="M716 199h11 5l-1 1h-10 1-2l1 1h0-22-1c2-2 4-1 6-2 4-1 8 0 12 0z" class="H"></path><defs><linearGradient id="AZ" x1="290.859" y1="192.123" x2="296.893" y2="179.449" xlink:href="#B"><stop offset="0" stop-color="#d0d0d2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AZ)" d="M273 179v-1h10c4 1 9 0 14 2h0c1 0 2 0 2 1h0l10 9c3 2 5 4 7 6 0 1 0 1-1 1s-3 0-4-1c-3 0-4-1-7-1h0c1 1 2 1 3 2h-8l-1-1h2c-4-2-11-5-15-5l4-4c2 0 5-5 7-7h-14c-1 0-2 0-3-1s-5 0-6 0z"></path><path d="M315 197c-1-1-3-2-4-3l-8-6h0l7 4c0-1 0-1-1-2h0c3 2 5 4 7 6 0 1 0 1-1 1z" class="J"></path><path d="M315 201l1-1c0 3-2 4-4 6-4 4-9 10-14 13-2 1-4 4-6 5h-1l-1-1c-4-2-5-4-7-8-3-3-7-7-10-11 3 1 6 1 10 1 1 1 4 0 4 1-1 1-3 1-5 1h-1c5 1 14 3 18 6h1c2-2 4-3 7-5 2-2 5-4 8-7z" class="S"></path><path d="M283 215l3 3c3 1 5 0 7 2 0 2-1 2-2 4l-1-1c-4-2-5-4-7-8z" class="C"></path><path d="M299 213l1 1c1 0 0 0 1 1l-3 3h-3c-2 0-4 0-5-1h-1c-3-2-7-5-9-8l-1-1c-1 0-1 0-2-1 2 0 2 1 4 0 5 1 14 3 18 6z" class="M"></path><path d="M460 597h1c4-1 7-4 10-6l-1 5h1 0l1 7v-1l2 2v3l-4 9 2 1c-2 4-5 8-7 12l-6 8v4c-1-2-1-6-1-9 1 0 1-1 1-2s0-1 1-3c3-7 6-13 8-21l-1-1-1-1-2 1c-2 0-6 1-7 1v-4l-1-1v-1h0c2-1 3-1 4-3z" class="I"></path><path d="M460 627c1 0 1 0 2-1v-1l1 1-2 4h1c1-1 1-1 3-1l-6 8v4c-1-2-1-6-1-9 1 0 1-1 1-2s0-1 1-3z" class="N"></path><path d="M472 602l2 2v3l-4 9c0 2-3 5-4 7h-1c0-3 3-7 4-10s2-6 3-10v-1z" class="Q"></path><path d="M457 602h0c4-1 9-3 12-5l-1 9-1-1-1-1-2 1c-2 0-6 1-7 1v-4z" class="Z"></path><path d="M252 228c5 1 10 7 13 12 2 2 4 5 6 8h1c0 1 3 5 3 5 1 2 2 5 1 7-2-2-4-5-6-7-6-4-13-8-21-7-2 1-4 1-6 2 2-2 3-2 5-3h5c2-1 4-1 6-1 1 0 1 0 2 1v1h0l1 1c1 0 2 1 3 1 1 1 2 2 3 2l-1-2-2-2h0l-1-2c-1 0-2 0-3-1-1 0-1-1-2-2-3-2-6-3-9-4h-5c-4 1-10 3-13 1 0 0 0-1-1-2h1 2c1 1 3 0 5-1 3-1 6-3 10-4 1 0 0 0 1-1l-2-1h0c2 0 3 0 4-1z" class="I"></path><path d="M252 228c5 1 10 7 13 12 2 2 4 5 6 8l-1 1c-6-6-9-13-16-16l-1-1c-1 0-2-1-4-1 1 0 0 0 1-1l-2-1h0c2 0 3 0 4-1z" class="K"></path><path d="M742 239c3-2 7-3 9-5 2-4 6-7 9-9 4-5 10-8 15-11 3-1 7-3 10-4h1c1-1 2-1 3-1-1 1-1 1-3 2-1 0-1 0-1 1h-1-1-1c-1 1-1 1-2 1l-1 1h-2l-2 2c-1 0-1 1-2 1-1 1-2 1-2 2-2 1-3 2-5 2-1 1-3 2-4 3v1l-4 3c-2 1-5 4-5 7l1-1 1-1c1-1 2-1 3-1v-2c1 0 1 0 2-1h1c1 0 1 0 2-1h1 2 3l1-1c1 0 1 0 3 1h0c2 0 2 0 4 1h1c1 0 3 2 3 3l1 1c1 0 1 1 2 2 0 1 0 2 1 3v1h-2c-6-1-13-5-19-6-5 0-9 3-14 6l-6 6-2 2h-2c-1 1-1 2-3 2v1l-1 1c-1 1 0 0-1 2 0 1-1 1-2 2 2-4 4-7 6-11 1-1 1-4 3-5z" class="S"></path><path d="M739 244h0 1l1-1c3-2 5-3 9-5v1l-6 6-2 2h-2c-1 1-1 2-3 2v1l-1 1c-1 1 0 0-1 2 0 1-1 1-2 2 2-4 4-7 6-11z" class="B"></path><path d="M750 238c5-4 9-7 16-8h1c5-1 9 0 14 2l1 1c1 0 1 1 2 2 0 1 0 2 1 3v1h-2c-6-1-13-5-19-6-5 0-9 3-14 6v-1z" class="J"></path><path d="M716 301c0-3-1-9 1-11 0 16-1 33 4 49v2 2l-3-8-8 38c0 3-2 6-1 8-3 2-4 5-4 8v1l-1 1v3l-1 1v1c0 1 0 1-1 2l-3 10-2 8c-1 1-1 2-2 3l19-78-1-2v-8l-1-2h0c-1-1 0-3 0-4 0-3 0-6 1-9 1-2 1-4 2-6v1l1 2v-12z" class="P"></path><path d="M712 325c0-3 0-6 1-9 1-2 1-4 2-6v1l1 2c1 10 0 18-2 28l-1-2v-8l-1-2h0c-1-1 0-3 0-4z" class="B"></path><path d="M349 470c-3 5-7 7-12 8-1-4-1-8 0-12l-6-3c-2-2-4-10-5-13l-10-30-5-16c-1-3-1-6-3-8 0-2-2-2-3-3-3-1-7 2-9 3 0-3-1-6 0-10 0-2 0-5 3-6h3c2 1 3 3 4 5h1l23 75c1 1 1 2 2 2h1c4-1 8 0 12 2h1c1 1 3 4 3 6z" class="G"></path><path d="M292 198c3 1 8 0 12 1h10c1 0 2 0 2 1l-1 1c-3 3-6 5-8 7-3 2-5 3-7 5h-1c-4-3-13-5-18-6h1c2 0 4 0 5-1 0-1-3 0-4-1-4 0-7 0-10-1v-2h-1c1-1 1-1 1-2 3-3 14-1 19-2z" class="J"></path><path d="M283 205h16 1l-2 2c-4 0-7 0-11-1 0-1-3 0-4-1z" class="B"></path><path d="M301 201h14c-3 3-6 5-8 7v-1l1-1c-3 0-6 1-10 1h0l2-2h-1c0-1 0 0 1 0 3-1 9 0 11-2v-1l-10-1h0z" class="K"></path><path d="M298 207h0c4 0 7-1 10-1l-1 1v1c-3 2-5 3-7 5h-1c-4-3-13-5-18-6h1c2 0 4 0 5-1 4 1 7 1 11 1z" class="Y"></path><path d="M292 198c3 1 8 0 12 1h10c1 0 2 0 2 1l-1 1h-14 0l-28 1h-1c1-1 1-1 1-2 3-3 14-1 19-2z" class="O"></path><path d="M314 199c1 0 2 0 2 1l-1 1h-14c-4-1-8 0-12 0v-1h7c2-1 5-1 8-1h10z" class="H"></path><path d="M679 439l2 1-14 28v1c1-1 2-2 2-4 2-2 4-6 7-8 0 1 0 2-1 3l1 1h3l-2 1c-3 1-3 3-4 5-1 3-1 5-1 8v1c-5 1-8 5-11 10-5 9-7 20-7 31 0 3 0 6 1 9l1 3v1c1 2 2 5 4 7l1 1c1 1 2 1 2 3v1h-1l-5-5c-3-5-5-10-7-15v-2c-1-12 3-23 7-34 1-1 1-2 2-2l-1-1h-1c1-1 1-3 2-4h-1l1-1c0-1 1-2 1-3l2-2c2-4 3-8 6-11v-1l3-6 1-1c1-2 2-5 4-7 0-1 1-2 1-3 1-2 2-3 2-5z" class="F"></path><path d="M758 210c5-3 8-9 14-12 1-1 3-2 4-2l7-2c2-1 3-1 5-1 5-1 10 1 15 3v1h1c1 1 1 3 1 4s0 2-1 2c-3-3-6-4-11-5-2-1-4 0-7 0-2 1-5 1-7 2-13 5-20 15-28 26-3 4-7 8-9 12v1c-2 1-2 4-3 5-2 4-4 7-6 11-2 2-3 6-4 9h0c-1-1 0-2 1-3v-1c0-1 1-2 1-3l1-1-2 1v-1c-1 2-1 1-1 2s-1 1-1 2v1l-1 2-1 4v-1l1-6c7-18 17-36 31-50z" class="J"></path><path d="M710 296c1 2 1 3 1 5l1 1c0 2 0 2 2 4v-1-1c1 0 1-2 2-3v12l-1-2v-1c-1 2-1 4-2 6-1 3-1 6-1 9 0 1-1 3 0 4-1 2-1 4-1 6l-1-1c-1 8-3 17-5 25-1 3-2 5-3 8l-1-4c-3 12-10 25-16 37-2 4-5 8-7 12v4h-1c0-1-1-2 0-3h0c-1-2-1-3 0-4 9-17 17-35 22-53l6-21c1-2 1-4 2-6 1-3 1-7 2-10l2-12-2-6h1v-5z" class="I"></path><path d="M710 296c1 2 1 3 1 5l1 1c0 2 0 2 2 4v-1-1c1 0 1-2 2-3v12l-1-2v-1c-1 2-1 4-2 6-1 3-1 6-1 9h0c-1-3 0-7 0-9 1-3 0-7-1-9l-2-6h1v-5z" class="V"></path><path d="M701 363l1-6 4-11v-3c2-3 3-7 3-10v-1h1v2c-1 8-3 17-5 25-1 3-2 5-3 8l-1-4z" class="K"></path><path d="M752 211c2-1 4-1 6-1-14 14-24 32-31 50l-1 6-3 7v2l-1 3v1 1c-1 1-1 1-1 2-1 1 0 2-1 3h0c-1-2 0-7 1-9v-1s1-1 1-2h0v-1l1-1v-3h0c0-1 0-2 1-3 0-1 1-1 1-2l-1-1c-1-2 0-6 0-8l1-4v-4-3-1c1-1 0-3 1-4v-2h1v-3l1-1c1-2 1-4 4-6 2-1 3-3 5-4 1-1 1-2 3-3 1-1-1 1 1-1l1-1 9-6h1z" class="B"></path><path d="M724 262c2-5 1-9 2-14l3-15c1 6 0 11-1 17 0 2 0 5-1 7v2 1l-1 6-3 7v2l-1 3v1 1c-1 1-1 1-1 2-1 1 0 2-1 3h0c-1-2 0-7 1-9v-1s1-1 1-2h0v-1l1-1v-3h0c0-1 0-2 1-3 0-1 1-1 1-2l-1-1z" class="Q"></path><path d="M246 202c2 0 2 0 3 1v1h1c1 0 1 1 2 1s2 1 2 1h1c1 1 1 1 1 2h1c1 1 1 1 2 1l2 2c2 1 0 0 2 0l1 1c2 2 5 4 7 7l3 3 2 4 3 3v1c2 2 3 5 4 8l1 4h1v2c0 1 0 3 1 4 0 1-1 3 0 5 0 1 0 1 1 3v2c0 1 0 1 1 3 0 1-1 3 0 5 1 0 1 1 1 2v1c0 1 0 1 1 2v2c0 1 0 1 1 2v3 1c-1 1 0 1-1 1l-1-8-7-18v-2c-3-8-10-15-15-22-2-3-4-7-7-10-2-1-6-5-6-7v-1l-3-3c-1 0-2-1-3-2-2-2-2-3-2-5z" class="B"></path><path d="M284 242v5 5c-1-1-1-2-1-3h0c0-2-1-4-2-6-1-3-2-5-4-8-2-5-6-9-9-13-3-5-8-8-12-12l-3-3c1 0 2 0 3 1h1c1 1 1 1 2 1l2 2c2 1 0 0 2 0l1 1c2 2 5 4 7 7l3 3 2 4 3 3v1c2 2 3 5 4 8l1 4z" class="Y"></path><path d="M443 631c2 7 1 15 1 22s0 16 1 24l2 21c2 21 6 42 11 63 4 15 8 31 16 44 5 10 13 19 20 27h0c-2-1-4-4-6-5-3-3-7-5-9-8-3-5-7-10-10-16-12-25-17-53-22-81-1-10-3-20-4-30v-28c0-4 0-9-1-13v-6c0-4-1-10 1-14z" class="L"></path><path d="M343 547v-1c0-2-1-4-2-6v-2c0 1 1 1 1 2l12 28 35 82 7 17c1 2 2 6 3 7l11 26c2 4 4 9 7 13 0 0 1 1 2 1l2 1v2h-2l-1 1c1 2 2 4 3 7 2 8 5 15 8 23l13 29 4 10c1 2 2 3 3 5h-1c-1-2-1-4-3-6 1 0-4-9-4-10l-12-25-12-30-8-18-30-70-20-49-9-21c-2-5-4-11-7-16z" class="J"></path><path d="M335 201h1l1-1h0l3-3 4-2c0-1 1-1 2-1h1c2-2 5-4 8-4 2-1 3-2 6-2l1-1h3l4-1h1c2 0 8 0 9 1h-1c-1 0-1 0-2 1h0 8 1c1 0 2 0 3 1 2 0 3-1 5-1v1l-3 3h-1c-9 0-19 0-28 2-3 1-7 3-10 4-1 1-3 1-4 2-5 3-11 7-14 12l-6 7c-1 1-2 1-3 3-3 4-6 7-8 11h-1c-3 5-6 10-7 16l-1 2h-1c0-4 2-7 2-10h0c3-10 11-20 17-28 2-2 4-4 5-6 1-1 0 1 1-1 1-1 2-1 2-2s1-2 2-3z" class="Q"></path><path d="M315 233c5-10 13-20 22-27 4-3 8-7 13-9l1 1c-1 1-3 1-4 2-5 3-11 7-14 12l-6 7c-1 1-2 1-3 3-3 4-6 7-8 11h-1z" class="c"></path><path d="M388 189c2 0 3-1 5-1v1l-3 3h-1c-9 0-19 0-28 2-3 1-7 3-10 4l-1-1c8-4 17-5 26-6 4-1 8-1 12-2z" class="L"></path><defs><linearGradient id="Aa" x1="523.376" y1="788.185" x2="494.743" y2="744.737" xlink:href="#B"><stop offset="0" stop-color="#3a3f49"></stop><stop offset="1" stop-color="#717980"></stop></linearGradient></defs><path fill="url(#Aa)" d="M512 720c1 10 0 20 0 29v7 9s1 2 0 2v6l-1 1h0v4c0 3-1 7 0 10 1 2 0 8 0 10l-1 1h0c-4-8-7-18-8-27-3-11-4-24-4-35v-5c2 2 3 4 3 6 1 1 1 2 2 3v1c1 0 2 2 3 2 1-2 1-5 1-7s0-5 1-6c0 2 0 4 1 6h1v1c1-6 1-12 2-18z"></path><path d="M507 737c2 4 0 12 0 16-1-4-2-7-4-11 1 0 2 2 3 2 1-2 1-5 1-7z" class="D"></path><path d="M510 763l2-7v9s1 2 0 2v6l-1 1h0v4c-1-4-1-10-1-15z" class="H"></path><path d="M512 720c1 10 0 20 0 29v7l-2 7c-1-4 0-9 0-13v-12c1-6 1-12 2-18z" class="b"></path><path d="M498 737v-5c2 2 3 4 3 6 1 1 1 2 2 3l-1 1h-1v1s1 1 0 2c0 1 0 2 1 3v3c1 5 0 9 0 14 1 1 1 0 0 1v-1 4c1 1 0 2 0 3-3-11-4-24-4-35z" class="R"></path><path d="M786 198c3 0 5-1 7 0 5 1 8 2 11 5-1 2-2 4-4 5-1 0-3-1-5-1-1 0-4 2-6 2-1 0-2 0-3 1h-1c-3 1-7 3-10 4-5 3-11 6-15 11-3 2-7 5-9 9-2 2-6 3-9 5v-1c2-4 6-8 9-12 8-11 15-21 28-26 2-1 5-1 7-2z" class="M"></path><path d="M788 199c2 0 4 0 7 1-2 0-4-1-5 0s-2 1-3 1l-1 1c-6 4-13 6-17 11v1s-12 10-14 10h0l10-12s0-1 1-2h1l2-2 1-1c1 0 1-1 2-1l2-1 3-2h1c1 0 1 0 2-1l3-2h1c2 0 3 0 4-1z" class="Q"></path><path d="M786 198c3 0 5-1 7 0 5 1 8 2 11 5-1 2-2 4-4 5-1 0-3-1-5-1h-3-1c-2 0-3 0-5-1-6 2-11 5-17 8v-1c4-5 11-7 17-11l1-1c1 0 2 0 3-1s3 0 5 0c-3-1-5-1-7-1l-2-1z" class="J"></path><path d="M786 198c3 0 5-1 7 0 5 1 8 2 11 5-1 2-2 4-4 5-1 0-3-1-5-1h-3-1c-2 0-3 0-5-1 3-1 7-1 10 0 1 0 3 1 5 1 1-2 1-2 1-4-2-2-4-3-7-3-3-1-5-1-7-1l-2-1z" class="X"></path><defs><linearGradient id="Ab" x1="587.089" y1="750.567" x2="615.469" y2="681.693" xlink:href="#B"><stop offset="0" stop-color="#607382"></stop><stop offset="1" stop-color="#93aab7"></stop></linearGradient></defs><path fill="url(#Ab)" d="M582 754l48-116v2c-1 1-2 3-2 4v1l-1 2v1c-1 1-1 1-1 2l-1 1v2l-1 1c-1 2-2 5-3 7v1c-1 2-1 2-1 4h-1v2h-1v1l-1 2v1s-1 1-1 2v1l-1 2c-1 0-1 1-1 1v2l-1 1 1 1 1-1 1 1c1 1 1 1 2 1 0-1 0-1 1-2l1-3h0l1-2v-1s0-1 1-1c0-2 1-3 1-5h0c1-1 1-1 1-2l1-2 2-5v-1l1-1c0-1 0-2 1-2v-2c1-1 1-2 2-3l1-4c0-1 1-1 1-2h0v-1c1-1 1-2 1-2l1-1c0-1 0-2 1-3l1-2 1-3c0-1 1-1 2-2l-49 121h0c-1-1-2-2-2-3v-2c-1 1-2 3-4 3-1 1-2 3-3 4z"></path><path d="M509 650l1 3c0 7 2 15 2 22v13 4 28c-1 6-1 12-2 18v-1h-1c-1-2-1-4-1-6-1 1-1 4-1 6s0 5-1 7c-1 0-2-2-3-2v-1c-1-1-1-2-2-3 0-2-1-4-3-6v5c-1-4-1-7-1-10 0-4 0-7 1-11v-2l1-2c1-5 3-8 5-12-1-1 0-2 0-2l2-2 1-5-1-9c0-5 0-11-1-16 0-2-1-5-1-6-1-3-2-6-2-8h3l2 15h1c3-3 1-12 1-16v-1z" class="P"></path><path d="M504 715c1 1 1 4 1 6 1-2 1-5 2-7v-1 5c-1 2 0 4 0 6 0 4-1 8-1 12l-2-5c-1-5 0-11 0-16z" class="L"></path><defs><linearGradient id="Ac" x1="503.813" y1="734.952" x2="497.187" y2="725.048" xlink:href="#B"><stop offset="0" stop-color="#808a92"></stop><stop offset="1" stop-color="#9ea5ad"></stop></linearGradient></defs><path fill="url(#Ac)" d="M498 716v-2h1l1 1v8h0c1 5 3 9 2 14l-1 1c0-2-1-4-3-6v5c-1-4-1-7-1-10 0-4 0-7 1-11z"></path><path d="M498 716v-2h1l1 1v8c-1 0-1 1-1 2l-1 1v-10z" class="c"></path><path d="M512 688v4 28c-1 6-1 12-2 18v-1h-1c-1-2-1-4-1-6 0-3 0-5 1-7 0-4 0-8 1-12l1-14c0-3 0-7 1-10z" class="R"></path><path d="M507 691c2 2 0 8 1 12v2c-1 1-1 6-1 8v1c-1 2-1 5-2 7 0-2 0-5-1-6v-2h-2c-1-1-1-1-2 0l-1-1c1-5 3-8 5-12-1-1 0-2 0-2l2-2 1-5z" class="N"></path><path d="M504 700c0 4-1 9 0 13h-2c-1-1-1-1-2 0l-1-1c1-5 3-8 5-12z" class="U"></path><path d="M509 650l1 3c0 7 2 15 2 22-1 5-1 13-3 17v2c1 2 1 8 0 10v1h-1v-2c-1-4 1-10-1-12l-1-9c0-5 0-11-1-16 0-2-1-5-1-6-1-3-2-6-2-8h3l2 15h1c3-3 1-12 1-16v-1z" class="M"></path><path d="M671 255c1-4 2-7 5-10 3-2 6-2 9-1 4 1 6 3 8 5 1 1 0 1 1 2 0 2 2 3 3 4 2 2 3 5 5 7l1 1 1 1c0-2-1-3-1-5l1 1h0c1 1 1 2 1 4l1-1c2 11 5 21 0 32-7 8-14 13-24 17l1-1-1-1c1 0 2-1 3-1v-1c2-2 6-4 8-6s3-4 5-6c3-3 5-5 5-10h0c1-7-1-13-4-18-1 0-1 0-2-1-2-3-4-4-6-7-1-1-1-2-2-3s-3-1-4-1c-3 0-3 2-4 3l-1 1h0l-2 2 1 5s0 1 1 2c0 1 1 2 2 3l3 1c0 1 0 1 1 2v1 1-1h-2c-4-1-6-2-9-5l-3-6c-1-3-1-6-1-10z" class="F"></path><path d="M671 255c1 1 1 2 1 3 1 2 2 3 3 5 1 1 2 3 2 5v-4c-1-1 0-3 0-4 0 3 0 5 2 7 0 0 0 1 1 2 0 1 1 2 2 3l3 1c0 1 0 1 1 2v1 1-1h-2c-4-1-6-2-9-5l-3-6c-1-3-1-6-1-10z" class="B"></path><path d="M677 260c1-3 2-5 3-7 2-2 4-4 6-4s5 0 6 1c0 0 1 0 1 1h-1c-1-1-2-1-4-1h0v1l2 1c6 4 11 13 14 20v1c2 6 2 14-2 20-3 7-10 12-17 15 2-2 6-4 8-6s3-4 5-6c3-3 5-5 5-10h0c1-7-1-13-4-18-1 0-1 0-2-1-2-3-4-4-6-7-1-1-1-2-2-3s-3-1-4-1c-3 0-3 2-4 3l-1 1h0l-2 2 1 5c-2-2-2-4-2-7z" class="X"></path><path d="M678 262c1-3 1-5 3-8h0c2 0 4-1 6 0 5 2 10 9 12 14-1 0-1 0-2-1-2-3-4-4-6-7-1-1-1-2-2-3s-3-1-4-1c-3 0-3 2-4 3l-1 1h0l-2 2z" class="Q"></path><defs><linearGradient id="Ad" x1="525.007" y1="734.22" x2="504.493" y2="729.28" xlink:href="#B"><stop offset="0" stop-color="#0b0a0c"></stop><stop offset="1" stop-color="#656c75"></stop></linearGradient></defs><path fill="url(#Ad)" d="M510 653h1v1 3l1 7c0-3 0-6 1-8 0 4-1 10 1 13h0c1-2 0-4 1-7 1-1 1-2 1-3l1 1-2 17 1 1c1 2 1 3 1 5l2 1v8h0v-8h0l-1 34h0c1-1 1-2 1-3v-2c1 2 0 4 2 6l1-3c1-4 3-8 6-11v-1c0 2 0 4-1 6s-4 6-3 8v1-2l1 2h1c-3 6-6 12-6 18v3c-1 1-1 1-2 3v1c-1 1-1 2-1 3h1l1-1c0 1-1 2-1 3-1 1-1 1-1 3h0c-2 11-2 23-3 34h-1 0 0c0-6 0-13-1-19 1 0 0-2 0-2v-9-7c0-9 1-19 0-29v-28-4-13c0-7-2-15-2-22z"></path><path d="M512 692l1 1c0 1 0 2 1 4v22 19c0 3 1 6 0 9 0 0 0-1-1-2v-1c-1-2 0-7 0-10v2c-1 4 0 10-1 13 0-9 1-19 0-29v-28z" class="H"></path><defs><linearGradient id="Ae" x1="518.001" y1="683.582" x2="508.164" y2="676.683" xlink:href="#B"><stop offset="0" stop-color="#292f30"></stop><stop offset="1" stop-color="#4b4d58"></stop></linearGradient></defs><path fill="url(#Ae)" d="M510 653h1v1 3l1 7c0-3 0-6 1-8 0 4-1 10 1 13h0c1-2 0-4 1-7 1-1 1-2 1-3l1 1-2 17 1 1-2 19c-1-2-1-3-1-4l-1-1v-4-13c0-7-2-15-2-22z"></path><path d="M227 213v1l-4 4c-6-1-6-10-11-12-3-2-4-4-6-6-1-3-1-5 0-7s3-3 5-4h1 3c3-1 6-1 9-1v1h2 1c1 1 2 1 2 1 2 1 3 1 4 2l3 2h1c1 1 2 1 2 2h0c-3-1-6-3-8-4-6-3-16-3-22-1-1 1-2 1-3 3 0 1 0 2 1 3s5 0 7 0c4 0 7 0 11 1h1c9 2 19 10 25 17l3 3 5 6c1 3 5 6 6 9 0 1 0 1 1 2h0c0 2 1 3 2 4 1 2 2 4 3 7 1 0 1 1 1 2h-1c-2-3-4-6-6-8-3-5-8-11-13-12l-1-1c-2-1-4-3-5-5s-7-7-9-9c-1 0-2-1-2-2-1 0-2 0-3-1h-1v-1h-3v3l-1 1z" class="c"></path><path d="M215 204h3c1 2 1 3 3 3l2 1 2 2c1 1 1 2 0 3-1 2-1 3-3 3h0c-2-1-3-4-4-5-1-2-3-4-3-7z" class="Y"></path><path d="M247 214l2 2h0l-2-2 1-1c1 0 1 0 1 1l1 1h1l3 3 5 6h-1c1 3 4 5 5 8-2-1-5-3-6-5h0c-1-4-7-5-8-9-1-1-2-2-2-4z" class="V"></path><defs><linearGradient id="Af" x1="230.052" y1="202.554" x2="221.803" y2="212.115" xlink:href="#B"><stop offset="0" stop-color="#b5b9be"></stop><stop offset="1" stop-color="#eae9e9"></stop></linearGradient></defs><path fill="url(#Af)" d="M215 204h-1 0-1c-2 0-4-2-5-3l-1-1c3-2 9 1 13-1 5 2 10 3 14 6 3 3 6 6 9 7 2 0 3 1 4 2 0 2 1 3 2 4 1 4 7 5 8 9-11-7-17-16-30-21-1 1-2 2-4 2l-2-1c-2 0-2-1-3-3h-3z"></path><path d="M218 204c3 0 6 1 9 2-1 1-2 2-4 2l-2-1c-2 0-2-1-3-3z" class="D"></path><path d="M629 509l1 5-7 23c-1 3-2 6-4 9-2 8-41 109-41 110h-1v1l-1 2-1 1c0 1 0 3-1 4l-1 2v1c-1 1-1 2-2 3v2c-1 2-2 3-3 5h-1c-1-1 0-2 0-3l-1-1c1 0 1-1 1-2h-1l1-2c1-1 2-3 2-5s2-5 3-6c1-2 1-4 2-6s2-3 2-5v-1-2l31-75 22-60z" class="L"></path><path d="M629 509l1 5-7 23c-1 3-2 6-4 9 0-1 0-2 1-3h0c0-1 0-2 1-3v-2h1c2-4 2-8 4-12 0-1 0-2 1-3l-1-1v-1c-4 13-10 26-15 38-1 4-2 8-4 11v-1l22-60z" class="N"></path><defs><linearGradient id="Ag" x1="248.854" y1="281.221" x2="278.455" y2="265.913" xlink:href="#B"><stop offset="0" stop-color="#d4d5d7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ag)" d="M243 248c2-1 4-1 6-2 8-1 15 3 21 7 2 2 4 5 6 7 4 7 6 12 8 19 0 2 0 3 1 5v1 1c1 1 1 2 1 3-1 3-1 6-2 8-2 2-2 3-5 4-4 4-9 7-14 9l-1-1c1-1 2-1 3-2 0-1 0-1 1-2l2-1 6-6v-1c1-1 1-1 1-2h1l1-2v-1c1-3 0-6-1-9l-1-7c-2-6-6-14-12-17-3-2-6-3-10-2h-1l-1 1h-1l-3 3c0 1 0 2-1 2l-1 1v1 1 4h0c-1 1-1 1 0 2v1l-3-1c-1 0-2-2-3-3 0-1-1-1-1-2l-1-1c0-1 0-2-1-2 0-1 0-2-1-3h0v-3l1-1v-3-1l1-1v-1c1-1 2-2 4-3h0z"></path><path d="M284 279c0 2 0 3 1 5v1 1c0 3 0 7-3 9 1-5 2-10 2-16z" class="I"></path><path d="M285 286c1 1 1 2 1 3-1 3-1 6-2 8-2 2-2 3-5 4l3-6c3-2 3-6 3-9z" class="c"></path><path d="M247 270c-1-1-1-2-1-4h-1c0-2 1-4 1-5v-1h0c1-3 3-6 6-7 0-1 1-1 1-1h1 4c9 3 15 8 19 16 4 7 6 16 3 23-1 5-4 9-8 12-1 1-2 2-4 2l2-1 6-6v-1c1-1 1-1 1-2h1l1-2v-1c1-3 0-6-1-9l-1-7c-2-6-6-14-12-17-3-2-6-3-10-2h-1l-1 1h-1l-3 3c0 1 0 2-1 2l-1 1v1 1 4h0z" class="B"></path><path d="M487 656c1 1 2 2 2 3l-3 3 1 4 1 1c1-2 2-3 4-3 1 0 3-1 4-1h0 5c0 9 1 16-3 24-1 4-3 8-7 9-2 1-6 1-8 1-2-1-3-2-4-3-2-1-2-2-3-3l-1-1h0c-2-2-3-5-4-8 1-1 2-2 2-3l3-6c1 0 1-1 2-2l4-8c2-2 4-5 5-7z" class="L"></path><path d="M487 670c1 2 1 3 1 5 0 1 0 1-2 2h0v-3l1-4z" class="N"></path><path d="M496 663h5c0 9 1 16-3 24h-2-1c1-1 2-1 2-2 1-1 1-3 1-4h0c0-1 1-2 1-3 1-5-1-10-3-14-2 1-4 3-6 3h-1l3-3c1 0 3-1 4-1h0z" class="Z"></path><path d="M487 656c1 1 2 2 2 3l-3 3 1 4 1 1c1-2 2-3 4-3l-3 3c-1 1-1 2-2 3l-1 4v3h-2c-2-1-3-2-5-2-1-1-2-1-3-2 1 0 1-1 2-2l4-8c2-2 4-5 5-7z" class="B"></path><path d="M486 674c-1 0-1-1-1-2v-1c-1-3 0-6 1-9l1 4 1 1c1-2 2-3 4-3l-3 3c-1 1-1 2-2 3l-1 4z" class="I"></path><defs><linearGradient id="Ah" x1="478.399" y1="676.843" x2="483.639" y2="685.439" xlink:href="#B"><stop offset="0" stop-color="#989a9f"></stop><stop offset="1" stop-color="#b7b9be"></stop></linearGradient></defs><path fill="url(#Ah)" d="M476 673c1 1 2 1 3 2v1l1 1 3 2c1 0 2 0 3-1 1 0 1 1 2 1l2-2h1c1 2 1 3 0 4s-1 1-1 2h0c0 1 0 1-1 1-3 2-11 3-14 6h0c-2-2-3-5-4-8 1-1 2-2 2-3l3-6z"></path><path d="M480 677l3 2c1 0 2 0 3-1 1 0 1 1 2 1v1c-2 0-3 1-4 1-2 0-3 1-4 1v-1h0c-1-2-1-2 0-4z" class="X"></path><path d="M498 681c0 1 0 3-1 4 0 1-1 1-2 2h1 2c-1 4-3 8-7 9-2 1-6 1-8 1-2-1-3-2-4-3-2-1-2-2-3-3l-1-1c3-3 11-4 14-6l9-3z" class="G"></path><path d="M495 687h1l-6 6c-1-1-1-2-2-3 0 0-2-1-3-1 3-1 7-2 10-2z" class="P"></path><path d="M476 691c3 0 6-1 9-2 1 0 3 1 3 1 1 1 1 2 2 3-4 1-7 2-11 1h0c-2-1-2-2-3-3z" class="M"></path><path d="M496 687h2c-1 4-3 8-7 9-2 1-6 1-8 1-2-1-3-2-4-3h0c4 1 7 0 11-1l6-6z" class="U"></path><path d="M351 198c3-1 7-3 10-4 9-2 19-2 28-2h1c-1 1-1 2-2 3h-1v3h-8 0l-15 2 1 1v1l4 2c0 1-1 1-2 1 0-1-1-1-2-1-4 1-7 1-10 3l-4 2c-3 1-5 3-8 5l-9 11-6 8c-4 6-7 14-10 20-1 3-2 5-3 7h-1v1c0 2-1 4-2 5v1-1l-1-1 7-21h-1 0c1-1 1-1 1-2h0v-1h-1c1-2 2-4 2-6v-1s1-1 1-2c2-4 5-8 7-13l6-7c3-5 9-9 14-12 1-1 3-1 4-2z" class="Q"></path><path d="M351 198c3-1 7-3 10-4 9-2 19-2 28-2-2 2-3 1-5 1-1 0-1 1-1 1-1 1-3 0-4 0l-8 1c-7 1-14 3-21 5h-3c1-1 3-1 4-2z" class="S"></path><path d="M360 201c1-1 3-1 4-1l1 1v1l4 2c0 1-1 1-2 1 0-1-1-1-2-1-4 1-7 1-10 3l-4 2c-3 1-5 3-8 5l-9 11-6 8c-4 6-7 14-10 20-1 3-2 5-3 7h-1v1c0 2-1 4-2 5v1-1l-1-1 7-21 5-10c4-7 9-14 15-19 4-5 8-8 13-11 2-2 6-2 9-3z" class="N"></path><path d="M360 201c1-1 3-1 4-1l1 1v1l4 2c0 1-1 1-2 1 0-1-1-1-2-1-4 1-7 1-10 3l-4 2c-3 1-5 3-8 5 3-5 7-6 12-9 2-1 4-2 6-4h-1z" class="V"></path><path d="M312 266l2-7 1-4 1-3 4-11c1-2 3-4 5-6 2-3 5-8 9-10l-6 8c-4 6-7 14-10 20-1 3-2 5-3 7h-1v1c0 2-1 4-2 5v1-1z" class="M"></path><defs><linearGradient id="Ai" x1="530.088" y1="762.786" x2="515.836" y2="758.364" xlink:href="#B"><stop offset="0" stop-color="#161415"></stop><stop offset="1" stop-color="#3c4047"></stop></linearGradient></defs><path fill="url(#Ai)" d="M539 721h5l1 1-1 2-14 33-12 30c-2 5-4 11-7 15l-1-3h0l1-1c0-2 1-8 0-10-1-3 0-7 0-10v-4h0l1-1v-6c1 6 1 13 1 19h0 0 1c1-11 1-23 3-34h0c0-2 0-2 1-3 0-1 1-2 1-3l-1 1h-1c0-1 0-2 1-3v-1c1-2 1-2 2-3v-3c1-3 5-12 8-14l5-2c2-1 4 0 6 0z"></path><path d="M520 762c2-1 2-3 3-5 0 2 0 3-1 5l1 1c-1 2-2 6-3 8 0-1-1-2 0-3v-2h0c-1-2 0-2 0-3v-1z" class="a"></path><path d="M520 762l1-2c1-1 1-2 1-3h1l-1-1-1 1v-1c-1 0 0 0-1 1v1h-1v-2l1-1c0-1 1-3 2-4 3-4 5-9 8-13-2 7-5 13-7 19-1 2-1 4-3 5z" class="W"></path><defs><linearGradient id="Aj" x1="531.808" y1="723.236" x2="533.677" y2="738.644" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#1e2027"></stop></linearGradient></defs><path fill="url(#Aj)" d="M539 721h5l1 1-1 2c-3 0-5 1-7 3h-1c-3 2-7 6-9 9l-10 16c0-2 0-2 1-3 0-1 1-2 1-3l-1 1h-1c0-1 0-2 1-3v-1c1-2 1-2 2-3v-3c1-3 5-12 8-14l5-2c2-1 4 0 6 0z"></path><path d="M539 721h5c-5 1-9 3-13 6 1-2 2-3 2-6 2-1 4 0 6 0z" class="O"></path><path d="M520 737c1-3 5-12 8-14l5-2c0 3-1 4-2 6-2 2-4 4-5 6-2 2-4 8-6 9-1-1-1-2 0-2v-3z" class="T"></path><path d="M712 329h0l1 2v8l1 2-19 78c-2 4-3 9-5 14l-6 20c0 2-1 7-3 8h-2-3l-1-1c1-1 1-2 1-3-3 2-5 6-7 8 0 2-1 3-2 4v-1l14-28-2-1c0 2-1 3-2 5 0-5 3-9 5-13l10-32 2-6 8-26c1-3 2-5 3-8 2-8 4-17 5-25l1 1c0-2 0-4 1-6z" class="J"></path><path d="M710 334l1 1-8 39c-1 1-1 0-2 1-1 2-1 3-2 5l-1 4c-1 1-1 2-2 3l-2 6h0l8-26 3-8c2-8 4-17 5-25z" class="c"></path><path d="M694 393h0l2-6c1-1 1-2 2-3l1-4c1-2 1-3 2-5 1-1 1 0 2-1-3 12-7 23-10 34-5 16-8 35-17 49-3 2-5 6-7 8 0 2-1 3-2 4v-1l14-28-2-1c0 2-1 3-2 5 0-5 3-9 5-13l10-32 2-6z" class="I"></path><path d="M679 439l1-2 2-5h0c1-1 1-1 1-2s1-2 2-4c-1 6-2 10-4 14h0l-2-1z" class="C"></path><path d="M746 251c0-1 1-1 2-2 5-3 10-5 16-4h1c3 1 6 3 9 5 1 2 2 3 3 6h0c1 2 0 3 0 5h0c-2 4-5 7-8 10-1 1-2 1-2 2-1-1-2-1-3-2 1-2 1-5 1-8-2-4-4-7-8-8-4 0-6 1-8 2-9 7-13 18-14 28-1 6 1 11 4 15 2 3 11 11 11 14v1l-1 1c1 1 2 1 3 2h-1c-2 0-4-1-5-1-5-3-12-7-15-12-1 0-3-3-3-3v-1l-1 1h0c-1-5 1-14-2-18 0-3 2-9 3-12l1-1c1-2 2-3 4-5 1-1 2-2 2-3 1-2 3-5 5-7l5-4s1 0 1-1z" class="I"></path><path d="M746 251c7-4 12-6 20-4 3 1 7 3 9 6s2 5 1 8c-1 2-3 4-5 6h0l-1-1c0-1 0-1-1-1-1-2-1-4-1-5-2-5-5-8-10-9h-2c-7 1-12 6-16 11-2 2-3 4-4 6v1l-2 5h0c-1 1-1 1-1 2v1 2c-1 2-1 5-2 8v-5c0-3 1-4 1-6s0-3 1-4c0-1 0-2 1-3v-2c1-1 1-3 1-4 1-2 3-5 5-7l5-4s1 0 1-1z" class="J"></path><path d="M733 266c1-1 2-2 2-3 0 1 0 3-1 4v2c-1 1-1 2-1 3-1 1-1 2-1 4s-1 3-1 6v5c1-3 1-6 2-8v-2-1c0-1 0-1 1-2h0l-1 5c-1 6-2 13 2 19s9 12 15 17l-1 1c1 1 2 1 3 2h-1c-2 0-4-1-5-1-5-3-12-7-15-12-1 0-3-3-3-3v-1l-1 1h0c-1-5 1-14-2-18 0-3 2-9 3-12l1-1c1-2 2-3 4-5z" class="c"></path><path d="M728 272l1-1c1-2 2-3 4-5l-3 9-1 4c-2 3-2 13-1 17 1 2 3 5 3 6 0 2-1 1 0 3h0c-1 0-3-3-3-3v-1l-1 1h0c-1-5 1-14-2-18 0-3 2-9 3-12z" class="L"></path><path d="M733 266c1-1 2-2 2-3 0 1 0 3-1 4v2c-1 1-1 2-1 3-1 1-1 2-1 4s-1 3-1 6v5c1-3 1-6 2-8v-2-1c0-1 0-1 1-2h0l-1 5c-1 6-2 13 2 19s9 12 15 17l-1 1c-6-5-13-10-17-17v-1c-4-6-3-13-3-19l1-4 3-9z" class="F"></path><defs><linearGradient id="Ak" x1="517.679" y1="737.895" x2="563.367" y2="741.529" xlink:href="#B"><stop offset="0" stop-color="#455564"></stop><stop offset="1" stop-color="#607c8b"></stop></linearGradient></defs><path fill="url(#Ak)" d="M576 646v1c0 2-1 3-2 5s-1 4-2 6c-1 1-3 4-3 6s-1 4-2 5l-1 2h1c0 1 0 2-1 2l1 1c0 1-1 2 0 3h1c1-2 2-3 3-5v-2c1-1 1-2 2-3v-1l1-2c1-1 1-3 1-4l1-1 1-2v-1h1l-35 88-4 10c-3 11-8 22-13 32-3 9-6 18-11 25l-4-9c3-4 5-10 7-15l12-30 14-33 1-2 11-29 1-1 2-6 2-3 2-7h0c2-5 4-9 5-14 2-3 4-7 5-11l3-5z"></path><path d="M561 683l2-7 1 4-3 3zm-22 71c0-3 0-5 1-7s2-4 2-5 1-2 1-2v-1h0v5l-4 10z" class="T"></path><path d="M472 617c1-2 2-5 4-7 1-1 2 0 4 0 0 1 0 1 1 2v8c0 4-3 14-1 17h0c3 2 3 5 4 8h-1c0 1 1 2 1 3h0c2 2 3 5 3 7v1c-1 2-3 5-5 7l-4 8c-1 1-1 2-2 2l-3 6c0 1-1 2-2 3l-1-2v-1c-1-2-1-3-1-5-1-6-5-14-7-20-1-4-2-9-3-13v-4l6-8c2-4 5-8 7-12z" class="G"></path><path d="M480 637c3 2 3 5 4 8h-1c0 1 1 2 1 3v1c-1 1-1 2-1 3-1 1-1 2-2 3 0-1 0-1-1-2v-2 1c-1 2-1 3-3 4 2-5 5-13 3-19z" class="I"></path><path d="M484 648h0c2 2 3 5 3 7v1c-1 2-3 5-5 7l-4 8c-1 1-1 2-2 2l-3 6c0 1-1 2-2 3l-1-2v-1c-1-2-1-3-1-5 4-6 6-11 8-18 2-1 2-2 3-4v-1 2c1 1 1 1 1 2 1-1 1-2 2-3 0-1 0-2 1-3v-1z" class="S"></path><path d="M484 648h0c2 2 3 5 3 7v1c-1 2-3 5-5 7l-4 8c-1 1-1 2-2 2l-3 6c0 1-1 2-2 3l-1-2c5-5 8-14 11-20h0v-4-1c1-1 1-2 2-3 0-1 0-2 1-3v-1z" class="E"></path><path d="M484 649c1 3 1 4 0 7l-3 4h0v-4-1c1-1 1-2 2-3 0-1 0-2 1-3z" class="B"></path><path d="M251 215c-6-7-16-15-25-17h-1c-4-1-7-1-11-1-2 0-6 1-7 0s-1-2-1-3c1-2 2-2 3-3 6-2 16-2 22 1 2 1 5 3 8 4h0c1 2 1 1 1 2 2 1 4 3 6 4 0 2 0 3 2 5 1 1 2 2 3 2l3 3v1c0 2 4 6 6 7 3 3 5 7 7 10 5 7 12 14 15 22v2l7 18 1 8c1 1 1 2 1 3 1 2 0 4 1 6s0 6 1 8 1 10 0 12c-1 4-1 9-2 13 0 3 0 6-1 9-1 1-1 3-1 5v1l-3 7-2 5c-2-1-2-2-3-3 0-2-1-4-1-6v-1c5-10 6-20 6-31 0-2 0-3-1-5h-1-1c0-2 0-4 1-6s1-5 2-8c0-1 0-2-1-3v-1-1c-1-2-1-3-1-5-2-7-4-12-8-19 1-2 0-5-1-7 0 0-3-4-3-5s0-2-1-2c-1-3-2-5-3-7-1-1-2-2-2-4h0c-1-1-1-1-1-2-1-3-5-6-6-9l-5-6-3-3z" class="F"></path><path d="M265 233c6 7 12 16 14 24h0c-2-1-3-3-4-4 0 0-3-4-3-5s0-2-1-2c-1-3-2-5-3-7-1-1-2-2-2-4h0c-1-1-1-1-1-2z" class="N"></path><path d="M275 253c1 1 2 3 4 4h0c3 5 4 11 5 17l2 11h-1v-1c-1-2-1-3-1-5-2-7-4-12-8-19 1-2 0-5-1-7z" class="c"></path><path d="M285 285h1c2 15 3 32-2 47-1 2-2 5-4 8h0v-1c5-10 6-20 6-31 0-2 0-3-1-5h-1-1c0-2 0-4 1-6s1-5 2-8c0-1 0-2-1-3v-1z" class="O"></path><path d="M286 289c1 6 0 13 0 19 0-2 0-3-1-5h-1-1c0-2 0-4 1-6s1-5 2-8z" class="L"></path><path d="M522 670h0l1-1v4l1 3 1 2h4l4 5 12 5h2l3 1 1 1 6 2-1 1-11 29-1-1h-5c-2 0-4-1-6 0l-5 2c-3 2-7 11-8 14 0-6 3-12 6-18h-1l-1-2v2-1c-1-2 2-6 3-8s1-4 1-6v1c-3 3-5 7-6 11l-1 3c-2-2-1-4-2-6v2c0 1 0 2-1 3h0l1-34c1-5 1-10 3-14z" class="G"></path><path d="M525 678h4l4 5-3-1v1c-2-1-4-3-5-5z" class="b"></path><path d="M551 690l6 2-1 1c-3 1-6 0-9-2 2 0 3 0 4-1zm-13 0c2 0 3 0 5 1h2v1h-1l2 2c-1 1-2 2-3 2s0 0-1-1c-1 0-1 0-1-1l2-1-1-1h-2 1c-1-2-2-2-3-2z" class="W"></path><path d="M530 683v-1l3 1 12 5h2l3 1 1 1c-1 1-2 1-4 1-6-2-12-4-17-8z" class="D"></path><path d="M527 688l3-1c1 1 1 1 1 3h3 2c-2 2-4 3-6 5l-8 10c0-2 0-4 1-6l2-9c0-2 1-2 2-2z" class="E"></path><path d="M527 688h3v1c-1 4-4 6-6 9 0 1-1 1-1 1l2-9c0-2 1-2 2-2z" class="a"></path><defs><linearGradient id="Al" x1="533.176" y1="705.203" x2="525.128" y2="701.202" xlink:href="#B"><stop offset="0" stop-color="#5b626e"></stop><stop offset="1" stop-color="#797d81"></stop></linearGradient></defs><path fill="url(#Al)" d="M536 690h2c1 0 2 0 3 2h-1 2l1 1-2 1c0 1 0 1 1 1 1 1 0 1 1 1l-4 1c-5 1-8 3-11 7v1c-3 3-5 7-6 11l-1 3c-2-2-1-4-2-6 1-3 2-5 3-8l8-10c2-2 4-3 6-5z"></path><path d="M536 690h2c1 0 2 0 3 2h-1c-3 1-6 3-10 3 2-2 4-3 6-5z" class="H"></path><defs><linearGradient id="Am" x1="548.891" y1="697.683" x2="533.417" y2="718.182" xlink:href="#B"><stop offset="0" stop-color="#222327"></stop><stop offset="1" stop-color="#404246"></stop></linearGradient></defs><path fill="url(#Am)" d="M546 694c2 2 3 3 3 6 0 4-1 8-3 11l-1 1c-1 2-2 2-3 3-2 1-4 3-5 4l-2 1h-1c1-2 2-4 2-5 1-2 3-4 3-6l1-1 1-1-1-2c0-1 1-1 1-2 1-1 1-1 1-2-1 0-2-1-2-1 1 0 2 0 3-1v-1c-2-1-2-1-4-1l4-1c1 0 2-1 3-2z"></path><path d="M539 697c2 0 2 0 4 1v1c-1 1-2 1-3 1 0 0 1 1 2 1 0 1 0 1-1 2 0 1-1 1-1 2l1 2-1 1-1 1c0 2-2 4-3 6 0 1-1 3-2 5h1l2-1 2 2c-2 0-4-1-6 0l-5 2c-3 2-7 11-8 14 0-6 3-12 6-18h-1l-1-2v2-1c-1-2 2-6 3-8s1-4 1-6c3-4 6-6 11-7z" class="H"></path><path d="M528 713c2-4 4-9 8-11 0 2-1 4-3 6l-1 2-1-1c0 1-1 3-3 4z" class="E"></path><path d="M539 697c2 0 2 0 4 1v1c-1 1-2 1-3 1l-4 2c-4 2-6 7-8 11l-2 6h-1l-1-2v2-1c-1-2 2-6 3-8s1-4 1-6c3-4 6-6 11-7z" class="G"></path><path d="M477 595c1-1 2-3 2-4l5-6c0 4 1 8 2 11h0 1c1 0 2 0 3 1h2c2 1 5 3 6 5 2 4 1 8 1 12 1 2 0 5 0 7h1l3 17c0 2 0 4 1 6v4c1 1 1 2 1 4h-3c0 2 1 5 2 8 0 1 1 4 1 6 1 5 1 11 1 16v5h0v-7c-2-2-1-5-1-7-1-1 0-4-1-5v-1-1-2c0-1 0-2-1-3 0-1-1-2-1-4 0-1-1-2-1-4v1 1h-1v-2c-1-1-2-1-2-2l-1-2v-1c-1-1-1-3-2-4s-1-2-1-3l-2-2-2-2h0c0-1-1-2-1-3h0c0-1 0-2-1-3v-2l-1-2v-3c-1-2-1-4-1-5h-1v3 3 2 2s0 1 1 2v1c0 2 3 7 5 9h0v1l1 1c0 1 0 1 1 2h1v1 1h1c0 1 1 1 1 2h-1l4 9c1 1 2 4 2 5h-5 0c-1 0-3 1-4 1-2 0-3 1-4 3l-1-1-1-4 3-3c0-1-1-2-2-3v-1c0-2-1-5-3-7h0c0-1-1-2-1-3h1c-1-3-1-6-4-8h0c-2-3 1-13 1-17v-8c-1-1-1-1-1-2-2 0-3-1-4 0-2 2-3 5-4 7l-2-1 4-9h0c2-4 2-8 3-12z" class="K"></path><path d="M487 620l2 2c1 4 2 8 3 11l-2-2v-1h-1v-1c-1-1-1-1-1-2l-1-7z" class="C"></path><path d="M485 612c1 1 1 2 2 4 0 0 1 1 1 2l1 4-2-2v-1c-1-2-1-2-2-3-1 2-2 4-2 6-1 0-1-1-1-2 0-2-1-5 0-7l3-1z" class="V"></path><path d="M484 645h1c2-3 0-10 0-13l2 5c1 1 2 3 2 3l6 9 4 9c1 1 2 4 2 5h-5 0c-1 0-3 1-4 1-2 0-3 1-4 3l-1-1-1-4 3-3c0-1-1-2-2-3v-1c0-2-1-5-3-7h0c0-1-1-2-1-3h1z" class="P"></path><path d="M489 640l6 9 4 9c1 1 2 4 2 5h-5c1 0 2-1 2-1 1-3-9-16-9-22z" class="D"></path><path d="M484 645h1c2-3 0-10 0-13l2 5 2 11c0 1 0 1 1 2h0c1 3 3 5 3 8-2 1-3 1-3 3 1 1 1 1 3 2h0c1-1 2 0 3 0-1 0-3 1-4 1-2 0-3 1-4 3l-1-1-1-4 3-3c0-1-1-2-2-3v-1c0-2-1-5-3-7h0c0-1-1-2-1-3h1z" class="N"></path><path d="M477 595c1-1 2-3 2-4l5-6c0 4 1 8 2 11h0 1c1 0 2 0 3 1h2c2 1 5 3 6 5 2 4 1 8 1 12 1 2 0 5 0 7h1l3 17c0 2 0 4 1 6v4c1 1 1 2 1 4h-3c-1-3-1-5-2-7-7-13-8-28-9-42-1-3-2-4-4-6 0 12 3 25 6 36 1 3 1 6 3 9 0 1 2 4 1 5-2-4-4-9-5-14-1-3-2-7-3-11l-1-4c0-1-1-2-1-2-1-2-1-3-2-4l-3 1c-1 2 0 5 0 7h-1v-8c-1-1-1-1-1-2-2 0-3-1-4 0-2 2-3 5-4 7l-2-1 4-9h0c2-4 2-8 3-12z" class="L"></path><defs><linearGradient id="An" x1="489.358" y1="629.211" x2="500.642" y2="607.789" xlink:href="#B"><stop offset="0" stop-color="#b6b6ba"></stop><stop offset="1" stop-color="#d7d9da"></stop></linearGradient></defs><path fill="url(#An)" d="M500 645v-1-2c-1 0-1-1-1-1l-4-13c-1-3-1-6-1-10l-2-17h0c4 2 3 6 4 10 2 3 1 7 1 10 0 2 0 3 1 5h0c0 3 0 6 1 9 1 2 1 3 2 5h0c0 1 0 1 1 2 0 1 1 2 1 3 0 2 0 2 1 3s1 2 1 4h-3c-1-3-1-5-2-7z"></path><path d="M477 595l4-2c5 5 4 13 4 19l-3 1c-1 2 0 5 0 7h-1v-8c-1-1-1-1-1-2-2 0-3-1-4 0-2 2-3 5-4 7l-2-1 4-9h0c2-4 2-8 3-12z" class="C"></path><path d="M432 624v-2c1-1 0-2 1-3v-3c-1-1-3-2-4-3h-1c-1-1-1-1-2-1h-1v-1c1-1 2-1 3-1h5v-1h2s1-1 1-2v-4-1c-1-1-1-2-1-3-1-1-1-1-1-2-1-1-1-2-2-3h2c0 1 2 1 2 2 2 1 3 1 5 1 0-1 1-1 2-1 1-1 5 0 7-1 1-1 1-1 2-1l1 1 3-3v3 5 1 4l-1 1v2c-1 0 0 2 0 3h0c0 1 0 2-1 3l-1 5c0 2 1 4 0 5v5c-1 2 0 4-1 7v7 17l2 21c4 25 8 50 14 75 2 9 4 19 7 28h0c0 1 0 1 1 2 0 2 1 3 1 4h0-1v-2l-2-6c0-1-1-2-1-3v-1h-1v-1-2c0-1-1-2-1-3v-3c-1-1-1-2-1-3l-1-3v-2c-1-1-1-3-1-4-1-1-1-2-1-3s-1-2-1-3v-3c-1-1-1-2-1-3v-1l-1-6c-1 0 0 0-1-1v-2-1l-2-8v-2c-1-1-1-2-1-3v-2c-1-1-1-2-1-3v-2c-1-1-1-2-1-3h-1l-1 1v1c0 3 0 6 1 9l-1 2v-2c0 1 0 2-1 3l-1-1h-1 0c-1 0-1 1-1 2v1 1c0 1 0 1 1 2v3c0 1 0 2 1 3v3c0 1 0 2 1 3v1 1 2c1 1 1 2 1 3 0 2 0 3 1 4v1c0 1 0 0 1 2v2 1l1 1v1 1s0 1 1 2h0v2c0 1 1 2 1 3v2l1 3c0 1 1 1 1 2l3 9c0 1 0 1 1 2v1l1 2v1s0 1 1 1v1l1 2h0l1 2c0 1 0-1 0 1v1h1c0 1 0 1 1 2v1c0 1 1 1 2 2v1h-1c-8-13-12-29-16-44-5-21-9-42-11-63l-2-21c-1-8-1-17-1-24s1-15-1-22h0v-3-2-1c-1-2-1-4-2-6 0 0-1 0-2-1-1 2-2 3-3 5h0c-1 1-1 2-2 2v1c-3 1-4 3-6 5l1-2c1-2 2-3 2-4l1-1z" class="F"></path><path d="M582 179c7 1 14 1 21 3 13 4 25 13 34 23 5 7 9 14 13 22 19 38 23 85 19 127l-3 27c0 2-1 5-1 8v2c-1 1-1 1-1 2v1c0 1 0 2-1 2v6l-1 6c-1 1-2 2-2 4l-5 21-3 6 5-27c1-9 3-19 5-28 1-12 2-25 2-37 0-35-4-69-15-101-6-17-13-35-28-46-5-4-11-7-17-9-4-1-7-1-10-2-1 0-4 0-4-1-1 0-2-2-3-3l-5-6z" class="I"></path><path d="M657 412l2-2c0-2 1-4 2-6v-2c1-3 1-8 2-12 0-2 0-3 1-4 0 2-1 3-1 5v7c-1 1-1 2-1 4v-2h1v-4 6l-1 6c-1 1-2 2-2 4l-5 21-3 6 5-27z" class="S"></path><path d="M545 630h1v1c1 1 0 0 1 2l1 1c4 2 7 4 12 7 2 2 5 4 8 7 2 1 3 3 5 3-1 4-3 8-5 11-1 5-3 9-5 14h0l-2 7-2 3-2 6-6-2-1-1-3-1h-2l-12-5-4-5h-4l-1-2-1-3v-4l-1 1h0c-1-3 2-9 3-12 0-1 1-2 1-3 2-5 5-9 8-14l5-7 1-1v-1l1-1c1 1 2 2 2 3 1-1 2-2 2-4h0z" class="a"></path><path d="M544 653h0c1 6 1 12 5 16h1c1 2 2 4 2 6 2 2 3 5 3 7s0 2-2 2c-2 1-4 3-6 4h-2c1-1 2-1 2-2 1 0 2-1 3-2s2-3 2-5c0 0-1-2-1-3-2-5-6-9-7-15v-1-1l-1-1v-1h1v-4z" class="E"></path><path d="M522 670c-1-3 2-9 3-12v5c0-1 0-1 1-2v-1l2-1-1 2c0 3 0 6 1 9s2 5 4 7h1 1c1-1 1-2 2-3 2 1 4 1 6 3v1c-1 1-3 1-5 1h-2v1c-1 0-1-1-3-1-1 0-2 0-3-1h-4l-1-2-1-3v-4l-1 1h0z" class="W"></path><path d="M536 674c2 1 4 1 6 3v1c-3 1-6 0-9-1h1c1-1 1-2 2-3z" class="R"></path><path d="M522 670c-1-3 2-9 3-12v5c0-1 0-1 1-2v-1l2-1-1 2c-2 5-3 9-3 15l-1-3v-4l-1 1h0z" class="Z"></path><path d="M545 630h0l1 4v4c0 1 0 2-1 3 0 4-1 8-1 12h0v-1c-1 0-2 1-2 2h-1c-2 2-4 5-5 8s-1 7-4 9c1 2 2 2 4 3-1 1-1 2-2 3h-1-1c-2-2-3-4-4-7s-1-6-1-9l1-2-2 1v1c-1 1-1 1-1 2v-5c0-1 1-2 1-3 2-5 5-9 8-14l5-7 1-1v-1l1-1c1 1 2 2 2 3 1-1 2-2 2-4z" class="T"></path><path d="M545 630h0l1 4v4c0 1 0 2-1 3l-1 3c-3-3 0-8-4-12l1-1c1 1 2 2 2 3 1-1 2-2 2-4z" class="D"></path><path d="M545 630h0l1 4v4l-2-1c-1-1-1-2-1-3 1-1 2-2 2-4z" class="P"></path><path d="M545 641c0 4-1 8-1 12h0v-1c-1 0-2 1-2 2h-1c-2 2-4 5-5 8s-1 7-4 9v-2c-3-7 8-18 11-24l1-1 1-3z" class="U"></path><path d="M540 633c2 1 2 3 2 6s-2 6-4 9c-3 4-8 9-9 14-1 3-1 6-1 8-1-3-1-6-1-9l1-2-2 1v1c-1 1-1 1-1 2v-5c0-1 1-2 1-3 2-5 5-9 8-14l5-7 1-1z" class="O"></path><path d="M539 634c1 1 1 3 1 4-1 4-4 7-6 9-3 4-5 8-6 12l-2 1v1c-1 1-1 1-1 2v-5c0-1 1-2 1-3 2-5 5-9 8-14l5-7z" class="U"></path><path d="M545 630h1v1c1 1 0 0 1 2l1 1c4 2 7 4 12 7 2 2 5 4 8 7 2 1 3 3 5 3-1 4-3 8-5 11-1 5-3 9-5 14h0l-2 7-2 3-2 6-6-2-1-1-3-1c2-1 4-3 6-4 2 0 2 0 2-2s-1-5-3-7c0-2-1-4-2-6h-1c-4-4-4-10-5-16 0-4 1-8 1-12 1-1 1-2 1-3v-4l-1-4z" class="G"></path><defs><linearGradient id="Ao" x1="560.205" y1="669.001" x2="543.46" y2="657.264" xlink:href="#B"><stop offset="0" stop-color="#3e3e43"></stop><stop offset="1" stop-color="#535d62"></stop></linearGradient></defs><path fill="url(#Ao)" d="M546 634l1 8c0 1 1 2 0 3 2 7 4 13 7 19 1 3 1 5 3 8v4h-2c-1-3-3-6-4-8l-1-2v2 1h-1c-4-4-4-10-5-16 0-4 1-8 1-12 1-1 1-2 1-3v-4z"></path><path d="M546 634l1 8c0 1 1 2 0 3v3 4c1 6 2 10 4 16l-1-2v2 1h-1c-4-4-4-10-5-16 0-4 1-8 1-12 1-1 1-2 1-3v-4z" class="W"></path><path d="M550 668c-2-3-5-12-3-16 1 6 2 10 4 16l-1-2v2z" class="E"></path><path d="M567 663l1-1c-1 5-3 9-5 14h0l-2 7-2 3-2 6-6-2-1-1-3-1c2-1 4-3 6-4 2 0 2 0 2-2s-1-5-3-7c0-2-1-4-2-6v-1-2l1 2c1 2 3 5 4 8h2v-4c3-2 7-6 10-9z" class="Z"></path><path d="M550 689h0c1-2 3-3 5-3l4-2v2l-2 6-6-2-1-1z" class="U"></path><path d="M567 663l1-1c-1 5-3 9-5 14l-1-1h-3l1 2c0 1 0 3-1 4 0 1 0 1-1 1h0c-1-3-3-4-3-6h2v-4c3-2 7-6 10-9z" class="E"></path><path d="M567 663l1-1c-1 5-3 9-5 14l-1-1h-3l-1-2c1-1 1 0 1-1 3-2 5-3 6-6 1-1 2-2 2-3z" class="U"></path><path d="M301 312c1 1 1 2 1 3-1 3 0 6 0 9l5 21 2 10h0c1 1 2 1 2 2h0c1 2 2 5 4 7l2 6c-1 2 0 4 1 6h0-3l4 18c1 2 1 5 2 7v-1l1 1c1 3 2 5 4 8 1 1 2 2 2 3 6 14 13 28 20 41 3 6 8 12 10 17 1 3 3 4 4 6l13 18c2 4 4 8 6 11 1 2 1 5 3 7l1 9c2 12-1 27-6 38-3 4-5 8-8 12v-1c1-2 2-3 3-5 2-6 5-13 6-19l1-3c1-2 1-4 1-6h-1c1-3 1-8 0-11l-2-13-2-6v-2c-1-1-2-1-3-2-2-3-3-7-5-12l-1-1v1 1c1 1 1 3 1 4 1 1 1 2 1 3-2-2-1-3-2-4-1-2-1-3-2-4v-1l-1-2c-1-1-2-1-2-3-2-2-4-3-5-5-2-2-4-3-5-6l-1-1-3-3h0c0-2-2-5-3-6h-1c-4-2-8-3-12-2h-1c-1 0-1-1-2-2l-23-75h1 0c-1-2-1-3-2-4v-2-2s0 1 1 2v2h0c1 1 1 2 2 3h1v-1-1c-1-1 0-1-1-2h0v-1-2l-1-1v-1c0-1 0-2-1-2v-2-1-1h0l-8-52c1-2 2-3 2-5z" class="J"></path><path d="M370 491c11 13 13 29 12 46h-1c1-3 1-8 0-11l-2-13-2-6v-2c-1-1-2-1-3-2-2-3-3-7-5-12h1z" class="L"></path><path d="M321 401v-1l1 1c1 3 2 5 4 8 1 1 2 2 2 3 6 14 13 28 20 41 3 6 8 12 10 17-15-19-28-42-36-65l-1-4z" class="D"></path><path d="M330 460l1-1c1 0 1-1 2-2s2-2 5-2v-1l-1-1c-1-2-1-4-2-6l-2-4v-1-1c1 1 1 2 2 3l1 2c1 2 2 3 3 5l6 8 25 32h-1l-1-1v1 1c1 1 1 3 1 4 1 1 1 2 1 3-2-2-1-3-2-4-1-2-1-3-2-4v-1l-1-2c-1-1-2-1-2-3-2-2-4-3-5-5-2-2-4-3-5-6l-1-1-3-3h0c0-2-2-5-3-6h-1c-4-2-8-3-12-2h-1c-1 0-1-1-2-2z" class="C"></path><path d="M301 312c1 1 1 2 1 3-1 3 0 6 0 9l5 21 2 10h0c1 1 2 1 2 2h0c1 2 2 5 4 7l2 6c-1 2 0 4 1 6h0-3l4 18c1 2 1 5 2 7l1 4v1c-2 3 0 7 1 10l5 13c1 2 2 5 3 7h-1c-1-1-2-3-2-5-2-3-3-6-4-9l-2-5-4-11h0l-11-37-8-52c1-2 2-3 2-5z" class="S"></path><path d="M309 355c1 1 2 1 2 2h0c1 2 2 5 4 7l2 6c-1 2 0 4 1 6h0-3l-6-21z" class="L"></path><path d="M301 312c1 1 1 2 1 3-1 3 0 6 0 9l5 21h-1c0-1-1-2-1-3v-3h-1v-3-2c-1-1-1-2-1-3h0v2h-1v2c1 1 0 3 1 4v1 3c1 2 1 4 1 6 1 2 1 4 1 6l1 4c1 3 1 8 2 11 1 1 1 2 1 3v1c1 3 2 7 3 10l1 1v1l5 15v1c0 1 1 2 1 3v2l-1-1h0l-11-37-8-52c1-2 2-3 2-5z" class="V"></path><path d="M355 433h1c2 0 3-4 5-5 1-1 2-1 3 0s1 3 1 5l1-1-1-2c0-1 0-1-1-3h0c1-2 0-6 0-8-1-3-1-7-1-10 1-2 0-3 1-5 0-3 0-6 1-9 0-2 1-6 2-7l1 1c0 2 1 5 2 7l2 5c0 1 1 1 2 2h0l1 3c1 3 2 6 4 9 1 2 1 4 1 6h0l-2-1h0c-1 1-1 0-2 1l9 9c2 2 4 3 5 6l2 6h-1 0-1 0l-1 3v4 4h1l2 3 6 7c-1 1-2 1-4 1l3 2h-1c0 1-1 1-2 1-2 3 0 9-1 10l-1 1h0l-1 1c1 15 9 30 15 43 3 6 5 12 9 17l4-1c2 2 2 2 5 2 2 0 4-1 6-1-1 1-3 2-4 2h-2-2v1h-2l-1 1 3 1c0 1 1 1 2 1h2l2 1h-1-8c1 2 2 4 3 5 9 14 20 27 36 32l-3 3c-2 1-5 4-7 6-3 1-6 4-10 3-14-4-42-63-52-78-1-2-1-4-2-5-2-2-2-5-3-7-2-3-4-7-6-11l-13-18c-1-2-3-3-4-6-2-5-7-11-10-17-7-13-14-27-20-41v-4l1-1c0-2-1-5-2-7h1c0 2 2 4 3 7h1c2 5 5 12 9 16 1 2 3 6 5 7h2c2 2 4 3 7 3z" class="Q"></path><path d="M390 436l2 6h-1 0-1 0l-1 3c0-3 1-6 1-9z" class="V"></path><path d="M379 415c1 2 1 4 1 6h0l-2-1h0c-1 1-1 0-2 1l-2-2c1-2 3-3 5-4z" class="G"></path><path d="M389 453h1l2 3 6 7c-1 1-2 1-4 1l-1-1c-1 1-2 1-3 2h0l-1-12z" class="U"></path><path d="M397 466l1 1 14 12c4 3 7 6 11 9l2-2 12 12 16 16c2 2 5 5 6 7 2 2 9 13 9 13h1c5-6 8-12 12-18 1-3 3-4 5-7l1-5 1-1h0v1 1c0 2-1 4-2 6l-12 22c3 5 6 11 8 17 2 5 3 11 5 16 1 3 2 6 3 8l1 2-5 13c-1 1-1 1 0 2 0 2 0 3 1 5h-1 0c-1-3-2-7-2-11l-5 6c0 1-1 3-2 4-1 4-1 8-3 12h0v-3l-2-2v1l-1-7h0-1l1-5c-3 2-6 5-10 6h-1c-1 2-2 2-4 3h0v-5h1v-9h-2l3-3c-16-5-27-18-36-32-1-1-2-3-3-5h8 1l-2-1h-2c-1 0-2 0-2-1l-3-1 1-1h2v-1h2 2c1 0 3-1 4-2-2 0-4 1-6 1-3 0-3 0-5-2l-4 1c-4-5-6-11-9-17-6-13-14-28-15-43l1-1h0l1-1c1-1-1-7 1-10 1 0 2 0 2-1h1z" class="K"></path><path d="M453 520h1l8 12c1 1 3 3 3 5 1 0 0 1 1 2l-1 1c-1-2-3-3-4-5l-3-5c0-1 0 0-1-1-1-2-2-4-4-7v-2zm-3 11h2l1-1c1 2 3 3 4 6l2 2 4 3-1 2h-1 0-1c-2-1-4-3-5-4-2-1-3-2-3-3l-2-3 1-2h-1z" class="B"></path><path d="M451 531c4 3 7 9 10 12h0-1c-2-1-4-3-5-4-2-1-3-2-3-3l-2-3 1-2z" class="Y"></path><path d="M454 520l2 1 1-1c1 1 1 1 2 1 2 2 9 13 9 13h1c-1 2-2 3-3 5-1-1 0-2-1-2 0-2-2-4-3-5l-8-12z" class="R"></path><path d="M437 498l16 16c2 2 5 5 6 7-1 0-1 0-2-1l-1 1-2-1h-1c-1-2-2-3-3-5l-9-9c-2-2-4-4-5-6l1-2z" class="T"></path><path d="M466 540l3-3c7 8 11 19 14 29-1-1-2-2-3-2 0-2-1-3-2-4 1 5 4 10 5 15-3-2-3-4-4-7s-3-6-5-9c-1-1-3-6-4-9-1-2-4-4-5-6 0-2 1-2 1-4z" class="O"></path><path d="M474 559c-1-1-3-6-4-9-1-2-4-4-5-6 0-2 1-2 1-4 5 6 9 13 12 20 1 5 4 10 5 15-3-2-3-4-4-7s-3-6-5-9z" class="B"></path><path d="M423 488l2-2 12 12-1 2c-2 3-4 7-8 8-3 0-4 0-6-2h-2 1l-1-2c-2-6 0-11 3-16z" class="G"></path><path d="M419 538l8-3c1 0 3-1 5-1 4-1 9 0 13 1 1 0 2 1 3 1 2 0 4 2 6 3 3 3 7 6 9 10l8 11 3 6v2c1 1 1 2 1 4h1v-1h1l1-1c0-3-3-7-4-10v-1c2 3 4 6 5 9s1 5 4 7c-1-5-4-10-5-15 1 1 2 2 2 4 1 0 2 1 3 2h0c2 5 3 10 4 15 2-2 3-4 3-7l1 2-5 13c-1 1-1 1 0 2 0 2 0 3 1 5h-1 0c-1-3-2-7-2-11l-5 6c0 1-1 3-2 4-1 4-1 8-3 12h0v-3l-2-2v1l-1-7h0-1l1-5c-3 2-6 5-10 6h-1v-1c1-3 5-4 6-7 2-2 4-3 4-6 1-8-6-18-11-24v-2c-3-5-12-9-17-10l-1-1c-4-1-9-1-13 0l-2-1h-2c-1 0-2 0-2-1l-3-1 1-1h2v-1h2 2c1 0 3-1 4-2-2 0-4 1-6 1-3 0-3 0-5-2z" class="F"></path><path d="M472 602c1-4 1-8 2-12h0v6 4l1-1c-1-3-1-6-1-9l1-1v-8c-1-1-1-2-1-3 0-2-1-3 0-4h0l1 3v1 1c1 1 0 2 1 3v9c-1 2 0 5-1 7 0 2 0 4-1 6l-2-2z" class="K"></path><path d="M474 559c2 3 4 6 5 9s1 5 4 7c1 2 1 4 2 7-2 2-4 5-6 7l1-2c1-5-1-11-3-16l1-1c0-3-3-7-4-10v-1z" class="C"></path><path d="M478 560c1 1 2 2 2 4 1 0 2 1 3 2h0c2 5 3 10 4 15 2-2 3-4 3-7l1 2-5 13c-1 1-1 1 0 2 0 2 0 3 1 5h-1 0c-1-3-2-7-2-11l-5 6c0 1-1 3-2 4-1 4-1 8-3 12h0v-3c1-2 1-4 1-6 1-2 0-5 1-7h1l2-2c2-2 4-5 6-7-1-3-1-5-2-7-1-5-4-10-5-15z" class="T"></path><path d="M419 538l8-3c1 0 3-1 5-1 4-1 9 0 13 1 1 0 2 1 3 1 2 0 4 2 6 3 3 3 7 6 9 10l8 11 3 6v2c1 1 1 2 1 4l-1 1v-1h-1c0-2-1-3-1-4l-3-5-1-2c-1-1-1-2-2-3l-1-1c0-1-1-2-2-2-1-3-5-6-7-8v-1c-2-1-5-3-7-3 0 0-1-1-2-1l-4-1-1-1h-3c-3-1-6-1-9-1-2 0-4 1-6 1-3 0-3 0-5-2z" class="J"></path><path d="M428 546c4-1 9-1 13 0l1 1c5 1 14 5 17 10v2c5 6 12 16 11 24 0 3-2 4-4 6-1 3-5 4-6 7v1c-1 2-2 2-4 3h0v-5h1v-9h-2l3-3c-16-5-27-18-36-32-1-1-2-3-3-5h8 1z" class="G"></path><path d="M457 595c2-3 6-5 9-6-1 3-5 4-6 7v1c-1 2-2 2-4 3h0v-5h1z" class="C"></path><path d="M428 546c4-1 9-1 13 0l1 1c5 1 14 5 17 10v2l-6-5c-8-4-17-7-26-4l-5 1c-1-1-2-3-3-5h8 1z" class="D"></path><path d="M397 466l1 1 14 12c4 3 7 6 11 9-3 5-5 10-3 16l1 2h-1c0 1-1 3-1 4v3l-1 3h1c2 1 5 1 7 1 4 1 8 3 11 4l9 6 4 4h1l-1 2 2 3c0 1 1 2 3 3h-1c-2-1-4-3-6-3-1 0-2-1-3-1-4-1-9-2-13-1-2 0-4 1-5 1l-8 3-4 1c-4-5-6-11-9-17-6-13-14-28-15-43l1-1h0l1-1c1-1-1-7 1-10 1 0 2 0 2-1h1z" class="G"></path><path d="M408 498v5h2v7c1 2 1 4 2 6l-2 2c-2-7-4-13-2-20z" class="K"></path><path d="M412 516c4 3 9 4 13 4 2 2 5 3 7 5 2 1 6 2 7 4v1c-9-4-17-4-26-6l-3-6 2-2z" class="N"></path><path d="M397 466l1 1 14 12c4 3 7 6 11 9-3 5-5 10-3 16l1 2h-1c0 1-1 3-1 4v3l-1 3h1c2 1 5 1 7 1 4 1 8 3 11 4l9 6 4 4h1l-1 2 2 3c0 1 1 2 3 3h-1c-2-1-4-3-6-3-1 0-2-1-3-1v-1l-6-4v-1c-1-2-5-3-7-4-2-2-5-3-7-5-4 0-9-1-13-4-1-2-1-4-2-6v-7h-2v-5c0-4 2-8 3-12l-1-2c-5-5-12-6-18-6h0l1-1c1-1-1-7 1-10 1 0 2 0 2-1h1z" class="F"></path><path d="M425 520c9 2 18 7 25 13l2 3c0 1 1 2 3 3h-1c-2-1-4-3-6-3-1 0-2-1-3-1v-1l-6-4v-1c-1-2-5-3-7-4-2-2-5-3-7-5z" class="C"></path><path d="M412 479c4 3 7 6 11 9-3 5-5 10-3 16l1 2h-1c0 1-1 3-1 4v3l-1 3c-1-1-1-2-2-2-4-6-3-14-2-20 1-2 1-5 2-7 0-1 0-3-1-4s-2-3-3-4z" class="M"></path><path d="M387 198h2v2c-1 3-4 6-7 8-6 11-13 23-17 35-3 9-6 19-8 28-4 14-7 28-9 43-4 29-1 58 5 86l2 11c1 2 1 4 1 6 0 1-1 1-1 1l-1 1c-1 1-2 2-3 2-2 1-3 2-4 3v1l-1-2-1 1 3 6h-2c-2-1-4-5-5-7-4-4-7-11-9-16h-1c-1-3-3-5-3-7h-1c1 2 2 5 2 7l-1 1v4c0-1-1-2-2-3-2-3-3-5-4-8l-1-1v1c-1-2-1-5-2-7l-4-18h3 0c-1-2-2-4-1-6l-2-6c-2-2-3-5-4-7h0c0-1-1-1-2-2h0l-2-10-5-21c0-3-1-6 0-9 0-1 0-2-1-3 0-1 0-3 1-5 0-10 2-21 5-31 0-1 0-3 1-5h1c0-2 0-2 1-4 0 0 0-1 1-2l1 1v1-1c1-1 2-3 2-5v-1h1c1-2 2-4 3-7 3-6 6-14 10-20l6-8 9-11c3-2 5-4 8-5l4-2c3-2 6-2 10-3 1 0 2 0 2 1 1 0 2 0 2-1l-4-2v-1l-1-1 15-2h0 8z" class="Q"></path><path d="M326 251c2-1 3-3 5-3 2-1 2 0 3 0-1 2-4 2-6 3h-2z" class="N"></path><path d="M309 324c1 1 2 2 2 3 1 1 1 1 1 2l6 19h-1c-1-1-1-2-2-3v-2h-1c-1-1-1-3-2-5-1-4-2-9-3-14z" class="D"></path><path d="M310 296l1 1h1 0c1 3 4 6 5 8h0c-2-1-3-3-5-4l-1-1c-1 2-2 4-1 6v2c1 2 1 5 1 7 1 4 2 8 1 11v3c0-1 0-1-1-2 0-1-1-2-2-3h0c-1-8-2-16-1-23l2-5z" class="C"></path><path d="M310 296l1 1c-4 10-1 19 1 29v3c0-1 0-1-1-2 0-1-1-2-2-3h0c-1-8-2-16-1-23l2-5z" class="P"></path><path d="M330 244c2-4 8-13 12-14 1-1 3-1 5 0s3 4 4 7 1 5 1 8c-1 9-1 18-8 25h0v-1-2h0c2-2 3-6 4-9v-1l1-1v-3c1-1 1-4 1-5 1-4 0-12-3-15-1-1-3-1-4-1-4 1-5 5-8 8-2 1-3 2-5 4z" class="B"></path><path d="M357 252c2-4 2-10 2-14 0-2 0-4 1-6 2 11 0 24-7 34-2 3-5 7-8 8l-1-1c0-1 0-1 1-2 2-2 3-4 5-6 2-5 5-8 7-13z" class="D"></path><defs><linearGradient id="Ap" x1="344.043" y1="228.966" x2="327.091" y2="238.492" xlink:href="#B"><stop offset="0" stop-color="#808488"></stop><stop offset="1" stop-color="#a5a8ab"></stop></linearGradient></defs><path fill="url(#Ap)" d="M333 233c2-3 5-6 9-6 3 0 6 2 8 4 2 5 4 11 3 17 0 0-1 0-1 1v-4c0-3 0-5-1-8s-2-6-4-7-4-1-5 0c-4 1-10 10-12 14-1 1-2 3-4 4-1 1-1 2-1 2l-1 1v1l-1-1 4-9c2-4 4-6 6-9z"></path><path d="M320 292l-2-4c-1-5-1-12 0-16s1-7 3-10c2-4 8-8 12-9h4v2c-6 1-13 6-16 11-1 2-2 3-2 5v1c-1 4-1 9 0 13h0v2 1c1 2 4 7 6 9s5 4 7 7c1 3 6 6 8 8-2 0-4-2-5-4-7-5-11-9-15-16z" class="D"></path><path d="M345 274c2 0 4-1 5-1 1 2 2 4 2 7 0 2-1 5-3 7s-4 2-6 2c-4-3-2-9-4-13-1-1-2-1-3-2l2-1c2 1 5 2 7 1z" class="G"></path><path d="M310 267s0-1 1-2l1 1v1c-1 1-1 2-1 3v1c-2 4 0 10 1 14 0 3 0 7 2 10 0 2 2 3 3 4h0c7 9 18 13 27 19-10-2-18-6-27-13-1-2-4-5-5-8h0-1l-1-1c1-2 0-4 0-6-2-8-1-15 0-23z" class="X"></path><path d="M387 198h2v2c-1 3-4 6-7 8h0l-6 3c-2 2-3 4-6 5h0c-1-4-1-6-1-9 0-1-1-1-2-1s-2-1-2-2c1 0 2 0 2 1 1 0 2 0 2-1l-4-2v-1l-1-1 15-2h0 8z" class="S"></path><path d="M373 203h2l2 4c-1 2-2 4-4 6h-1v-6c0-2 0-2 1-4z" class="Q"></path><path d="M387 201l2-1c-1 3-4 6-7 8h0l-6 3 1-2c2-3 6-6 10-8z" class="K"></path><path d="M387 198h2v2l-2 1c-2-1-7-2-9 0l-3 2h-2v-1l1-1c1 0 1 0 2-1 1 0 2-1 3-2h0 8z" class="M"></path><path d="M364 200l15-2c-1 1-2 2-3 2-1 1-1 1-2 1l-1 1v1c-1 2-1 2-1 4l-3-3-4-2v-1l-1-1z" class="X"></path><path d="M365 201c3 0 5 0 8 1v1c-1 2-1 2-1 4l-3-3-4-2v-1z" class="C"></path><path d="M314 343h1v2c1 1 1 2 2 3h1c4 10 8 20 13 30 1 3 4 6 5 10 1 2 2 4 3 5 6 9 10 18 16 25l-1 1-3-4c-4-6-9-11-13-18-4-6-7-12-10-18-5-11-12-24-14-36z" class="R"></path><defs><linearGradient id="Aq" x1="351.087" y1="262.708" x2="355.315" y2="230.476" xlink:href="#B"><stop offset="0" stop-color="#9a9ca1"></stop><stop offset="1" stop-color="#c2c4c7"></stop></linearGradient></defs><path fill="url(#Aq)" d="M338 225c3-3 7-5 11-4 3 0 5 1 7 3s3 5 4 8c-1 2-1 4-1 6 0 4 0 10-2 14-2 5-5 8-7 13-2 2-3 4-5 6-1 1-1 1-1 2l1 1h0c-2 1-5 0-7-1 3 0 4-2 6-3h0c7-7 7-16 8-25v4c0-1 1-1 1-1 1-6-1-12-3-17-2-2-5-4-8-4-4 0-7 3-9 6h0c0-2 1-3 2-4l-1-1c2-1 2-3 4-3z"></path><path d="M338 225c3-3 7-5 11-4 3 0 5 1 7 3s3 5 4 8c-1 2-1 4-1 6 0 4 0 10-2 14 0-5 2-11 1-16s-2-9-6-12c-3-1-6-2-9-1-1 0-3 1-4 2h-1z" class="N"></path><path d="M338 225h1c1-1 3-2 4-2 3-1 6 0 9 1l3 4c1 1 0 0 0 2h1c-1 2-1 3-4 3 0 0-1 0-1-1l-1-1c-2-2-5-4-8-4-4 0-7 3-9 6h0c0-2 1-3 2-4l-1-1c2-1 2-3 4-3z" class="K"></path><path d="M312 267v-1c1-1 2-3 2-5v-1h1c1-2 2-4 3-7 3-6 6-14 10-20-1 3-2 6-1 9l-4 9 1 1v-1l1-1 1 1h2c2-1 5-1 6-3 2 2 3 4 5 6h-1l-1 1v-2h-4c-4 1-10 5-12 9-2 3-2 6-3 10s-1 11 0 16l2 4c-1 2-1 4 0 6l1 2h0c-1 0-3-2-4-2v1c-1-1-3-2-3-4-2-3-2-7-2-10-1-4-3-10-1-14v-1c0-1 0-2 1-3z" class="S"></path><defs><linearGradient id="Ar" x1="303.869" y1="332.418" x2="324.75" y2="330.751" xlink:href="#B"><stop offset="0" stop-color="#afb7be"></stop><stop offset="1" stop-color="#d6d9dd"></stop></linearGradient></defs><path fill="url(#Ar)" d="M302 307c0-10 2-21 5-31 0-1 0-3 1-5h1c0-2 0-2 1-4-1 8-2 15 0 23 0 2 1 4 0 6l-2 5c-1 7 0 15 1 23h0l3 14c1 2 1 4 2 5 2 12 9 25 14 36l-1 1c1 1 1 2 2 3l1 1-1 3h0-1l-1-3h-1l1 1v1l-11-23-1 1c-2-2-3-5-4-7h0c0-1-1-1-2-2h0l-2-10-5-21c0-3-1-6 0-9 0-1 0-2-1-3 0-1 0-3 1-5z"></path><path d="M310 290c0 2 1 4 0 6l-2 5v-1c-1-3 0-7 2-10z" class="C"></path><path d="M302 315c2 10 4 20 7 29l5 13c0 2 2 5 2 6l-1 1c-2-2-3-5-4-7h0c0-1-1-1-2-2h0l-2-10-5-21c0-3-1-6 0-9z" class="P"></path><path d="M315 364l1-1 11 23v-1l-1-1h1l1 3h1 0l1-3-1-1c-1-1-1-2-2-3l1-1c3 6 6 12 10 18 4 7 9 12 13 18l3 4c-1 1-2 2-3 2-2 1-3 2-4 3v1l-1-2-1 1 3 6h-2c-2-1-4-5-5-7-4-4-7-11-9-16h-1c-1-3-3-5-3-7h-1c1 2 2 5 2 7l-1 1v4c0-1-1-2-2-3-2-3-3-5-4-8l-1-1v1c-1-2-1-5-2-7l-4-18h3 0c-1-2-2-4-1-6l-2-6z" class="Y"></path><path d="M351 415l3 4c-1 1-2 2-3 2-2 1-3 2-4 3v1l-1-2-3-6c2 0 4 0 6 1h0c2 0 2 0 3 1h0 0c-1-1-1-2-1-3v-1z" class="B"></path><path d="M341 412l2 5 3 6-1 1 3 6h-2c-2-1-4-5-5-7 0-2-1-5-2-7v-1h1l1-3z" class="S"></path><path d="M341 412l2 5 3 6-1 1c-1-1-1-2-2-2l-3-7 1-3z" class="E"></path><defs><linearGradient id="As" x1="342.393" y1="402.822" x2="323.962" y2="398.266" xlink:href="#B"><stop offset="0" stop-color="#303430"></stop><stop offset="1" stop-color="#4a4a52"></stop></linearGradient></defs><path fill="url(#As)" d="M315 364l1-1 11 23 14 26-1 3h-1v1c1 2 2 5 2 7-4-4-7-11-9-16l-3-6h1v-3l-13-28-2-6z"></path><path d="M330 398c2 3 4 7 6 11 1 2 3 4 3 6v1c1 2 2 5 2 7-4-4-7-11-9-16l-3-6h1v-3z" class="C"></path><defs><linearGradient id="At" x1="325.898" y1="379.733" x2="323.602" y2="399.767" xlink:href="#B"><stop offset="0" stop-color="#b2b3b6"></stop><stop offset="1" stop-color="#d1d5d9"></stop></linearGradient></defs><path fill="url(#At)" d="M318 376h0c-1-2-2-4-1-6l13 28v3h-1l3 6h-1c-1-3-3-5-3-7h-1c1 2 2 5 2 7l-1 1v4c0-1-1-2-2-3-2-3-3-5-4-8l-1-1v1c-1-2-1-5-2-7l-4-18h3z"></path><path d="M318 376l2 10 3 9c1 4 3 9 5 13v4c0-1-1-2-2-3-2-3-3-5-4-8l-1-1v1c-1-2-1-5-2-7l-4-18h3z" class="P"></path></svg>