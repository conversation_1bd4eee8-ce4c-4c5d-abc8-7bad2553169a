<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 92 823 872"><!--oldViewBox="0 0 1023 1024"--><style>.B{fill:#2c2c2c}.C{fill:#424141}.D{fill:#101010}.E{fill:#171717}.F{fill:#1d1d1d}.G{fill:#7f7e7f}.H{fill:#cdcccd}.I{fill:#373737}.J{fill:#aeadae}.K{fill:#626262}.L{fill:#242324}.M{fill:#f1f1f1}.N{fill:#504f50}.O{fill:#929192}.P{fill:#6c6b6b}.Q{fill:#595858}.R{fill:#afaeae}</style><path d="M365 316c-2 1-3 1-4 0v-1c2-1 3-1 4-1v2z" class="D"></path><path d="M618 479c1 2 2 2 3 4-1 1-1 1-2 1l-2-1c0-1 0-2 1-4z" class="C"></path><path d="M441 608l7 2-2 2h-3l-2-4z" class="I"></path><path d="M386 525h1c0 1 1 1 2 2 0-1 1-2 2-2l1-1h-2v-2h1l2 2c-1 1-2 3-3 4-2-1-3-2-4-3z" class="B"></path><path d="M587 806c-3 1-6 2-10 3l7-5c0 1 0 1 1 2h0 2zm-1-323l5 8v2c0-1-1-1-1-2-3-2-4-4-4-8zM448 610c1 1 4 2 5 3-4 0-8-1-13-1h3 3l2-2z" class="F"></path><path d="M385 509c1-1 2-2 4-3l1 1c0 2-2 3-3 5-1-1-1-2-2-3z" class="K"></path><path d="M288 379c2 0 3 0 4 1h0c0 1 1 1 2 2v3l-3-1-1-3c-1-1 0-1-1-2h-1z" class="L"></path><path d="M599 411v-2c2 4 2 7 3 11l-4-5v-1c1-1 1-2 1-3z" class="D"></path><path d="M415 586l12-1v1c-2 0-4 1-6 1h-2c-1-1-3-1-4-1z" class="J"></path><path d="M416 560h1c2 2 4 4 3 8h0c-3-3-4-5-4-8z" class="B"></path><path d="M385 509c1 1 1 2 2 3-2 2-3 4-5 5 0 0 1-1 1-2h0l-1-3 3-3z" class="G"></path><path d="M444 464c3-1 6-1 9-1-4 2-7 4-11 5 1-1 1-2 2-3v-1z" class="B"></path><path d="M595 168c2 1 4 1 6 2l-10 1h-5c1 0 3-1 4-1v1c2-2 3-2 5-3z" class="M"></path><path d="M634 525c2-1 4-3 6-4v2c-1 2-1 3-3 4l-1 1-2-3z" class="C"></path><path d="M426 840c2 4 3 7 3 11-1-2-3-3-4-5 1-2 1-4 1-6z" class="F"></path><path d="M291 384l-4-1c-1-2 0-2-1-4l-1-1h1c1 0 1 0 2 1h1c1 1 0 1 1 2l1 3z" class="D"></path><path d="M589 664l3 3v-1l1-1-1 8-3-5v-4z" class="L"></path><path d="M578 613h-6c2-1 4-3 7-3 1 1 4 1 6 2-3 1-4 1-7 1h0z" class="B"></path><path d="M614 675c1-1 2-1 3-2v-1l1-1v1l7 7-11-4z" class="O"></path><path d="M613 606h2v1c-1 2-2 3-4 5l-1 1h-1c1-3 2-5 4-7zM449 448h0v4c0 2-1 6-3 8-1 0-1 0-1-1l4-11z" class="D"></path><path d="M423 834l3 6c0 2 0 4-1 6l-3-5 1-7z" class="I"></path><path d="M449 277c1-1 1-1 2-1 3 0 6 0 9-1h1l1 1c-2 1-6 3-9 3-1-1-3-2-4-2z" class="C"></path><path d="M404 342h10 1c1 1 2 1 3 1-1 1-4 1-6 1-3 0-5 0-8-2z" class="B"></path><path d="M723 323c3 0 4 1 7 3h-1c-2 1-4 1-7 1 0-2 0-3 1-4z" class="E"></path><path d="M437 556c2-2 4-4 7-5l2 1c0 2-2 5-3 6l-1-3c-2 0-3 0-5 1zm166 114c1 3 2 4 5 5 0 2-3 5-4 7-1-4-1-8-1-12zM223 361l2 4h1 1v-1l2 11c0 1 1 3 0 4v-2-3c-1-1-1-1-1-2-1-2 0 0 0-2h0c-1-1-1-1-1-2h0v-1h-1v3 2 1 1h-1c-1-4-1-8-2-13z" class="B"></path><path d="M801 312l-1-1c0-4 1-9 2-12 1 4 0 8 2 13v2c-1-1-1-2-3-2z" class="E"></path><path d="M591 515c-3 4-6 7-9 10l1-4c1-2 3-4 5-7 1 0 2 1 3 1z" class="B"></path><path d="M604 571c1-4 2-10 6-13v1c-1 3-2 6-3 8-1 1 0 1 0 2l-3 2z" class="E"></path><path d="M671 647c-2 2-4 3-6 4s-3-1-4-2v-1h-1l1-1 2-1c0 1 1 1 2 2l1-1 1 1c1 0 1 0 2-1h2z" class="D"></path><path d="M366 422c4 2 8 5 11 7v1s-1 0-1-1h-1l-1-1h-1c-1 1-3 1-4 3 1 0 1 1 2 1s1 1 2 1h-1c-2-1-6-3-7-5h0l1 1c3 0 4-1 7-2-2-1-5-3-6-5h-1z" class="B"></path><path d="M683 300l3-2h0l-8 8-2-5v-1c1 0 2-1 3-1 1 2 0 2 1 4l3-3z" class="N"></path><path d="M399 493l1 1c1 0 3-1 4-1l-7 7-3-1v-1l5-5z" class="P"></path><path d="M578 449h0c2 2 6 9 5 11-1 0-2 0-3-1-1 0-2-5-2-6 0-2-1-2 0-4z" class="D"></path><path d="M422 668c1 4 1 10 0 13-2-2-4-5-6-7 2 0 4 1 5-1 1 0 1-3 1-5z" class="B"></path><path d="M437 751l6 9h-1c-2 0-3-1-5-2l-2-4h0c0-1 1-2 2-3z" class="G"></path><path d="M640 602c2 0 3 2 5 3l1 1v-1c0-1 0-1 1-1 4 3 7 8 10 12-6-5-11-10-17-14z" class="M"></path><path d="M301 322h2v2h0l5 4c-3 0-5-1-7-1l-8-1c3-1 5-3 8-4z" class="F"></path><path d="M428 452h1c-5 5-8 9-11 15l-1 1v-2c1-7 6-11 11-14z" class="D"></path><path d="M450 538c2 1 4 3 5 4l-2 8-3-3v-1c1-3 1-4 0-8z" class="E"></path><path d="M352 648l5 2v1h-2c0 2 0 2 1 3l-1 1v1c1 0 1 0 2 1-2 0-3-1-5-2 0-2 0-2-1-4h0l1-3z" class="L"></path><path d="M748 486c1-2 2-5 4-6 0 0 0 1-1 1v3h1c1 0 2 1 4 2v-2h1v-2c1 3 0 5-1 7-2-2-5-3-8-3z" class="B"></path><path d="M738 379h1 0v1 2c-2 2-5 3-8 4-1 0-2 1-3 1 0-1 1-3 1-3 2-2 7-2 9-5zM573 716c0-2 0-2 1-3l2-3 4 4c1 0 5 0 5 1h1l-1 2c-2-2-6-2-9-2-2 0-2 0-3 1z" class="D"></path><path d="M592 494h2c1 1 3 1 4 2-8 0-16 0-23 1h-1c6-2 12-3 18-3z" class="G"></path><path d="M609 341c4-1 8 1 13 1l-2 2c-4 1-7 1-11 0h1v-1c-1 0 0-1-1-2h0z" class="C"></path><path d="M317 342h1c2 4 2 6 1 10l-1 1h-2c-1-2 1-8 1-11z" class="D"></path><path d="M580 692c2 0 4-1 6 0 0 2-3 2-5 3l-5 3v-6h4z" class="J"></path><path d="M643 517l-1-1c-2-2-6-6-6-9 1-1 1-1 2-1 2 1 4 3 5 5-1 2-1 4 0 6z" class="C"></path><path d="M438 692h1c2-1 8-1 10 1v5c-2-2-4-3-6-4-2 0-4-1-5-1v-1z" class="O"></path><path d="M648 397h1c-1 1-2 1-4 2l1 1c-1 1-2 2-3 2h-1-1-1c-1 0-1 0-2-1-1 0-2 0-3 1h2l1 1h1 5l1-1c2 0 3-1 5-2h1-1c-1 2-5 3-7 3-3 1-7 0-10-2h1s1 0 2-1c4 0 8-1 12-3h0z" class="B"></path><path d="M630 522h1c3-2 5-3 9-2 1 1 1 1 0 3h0v-2c-2 1-4 3-6 4-1 0-2 1-3 1 0-1 0-2-1-4h0z" class="F"></path><path d="M618 479c2-2 5-6 8-6v4c-1 2-4 5-5 6-1-2-2-2-3-4z" class="B"></path><path d="M403 362c4 0 8-1 11-2v1c-1 0-2 1-3 1-1 1-1 0-2 1l3 6h-2c-3-2-5-4-5-7h-2z" class="E"></path><path d="M639 312c-4 0-8-2-11-3l6-1h7c2 0 4 1 7 0l1 1h0c-1 1-1 0-1 1h-3c-1-1-2-1-3-1h-2c-1-1-4-1-4 0h-1 2v1c1 0 2-1 3 0 1 0 2 0 3 1h3 0c2 0 2 0 3 1h1l-1 1v-1h-2-3c-1-1-3-1-4-1l-1 1z" class="D"></path><path d="M598 501c4-5 8-10 14-14-3 5-6 9-10 13h0c-1 0-2 1-3 1h-1z" class="G"></path><path d="M683 517h1c6-1 10 0 16 3-4 0-7-1-11-1-5-1-10 0-15 0h0c3-1 6-2 9-2zm23-205c4-3 9-5 14-6l-12 10h-3l2-3-1-1z" class="C"></path><path d="M669 545c1 0 1 0 2 1 1 0 3 0 4 1 1 0 1 1 2 1s2 0 2 1h0-1c-2-1-1 0-2 0l2 1c1 0 2 1 3 1 0 0 1 1 2 1 0 0 1 0 2 1h0 0c-1 0-2 0-3-1h-1l-1 1c-3 0-7-3-9-4-1-1-2-1-4-2h2c1 0 3 1 5 1l-1-1c-2 0-3-1-4-2z" class="D"></path><path d="M615 494v1c-2 5-6 10-10 14-1 2-3 4-5 6h0l15-21z" class="F"></path><path d="M429 610c4-2 8-2 12-2l2 4h-3 0l-4 1c-2-2-5-2-7-3z" class="N"></path><path d="M436 799c4 3 9 6 12 9-5 0-10-3-14-5l1-1c0-1 0-2 1-3z" class="F"></path><path d="M434 494c7 0 12 1 18 5-7-2-14-2-22-3v-1h1l3-1z" class="K"></path><path d="M392 397h0c0 2 0 3-2 4-2 2-5 2-7 2-2-1-5-1-7-3h1c2 1 4 2 6 2h0c-1-1-1-2-1-3h0c-1 0-2-1-2-1v-1l1 1c4 2 7 1 11-1z" class="B"></path><defs><linearGradient id="A" x1="421.037" y1="387.83" x2="427.475" y2="379.78" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M429 380c-4 4-5 7-6 12-1-2-2-5-2-8 2-2 3-4 5-6l3 2z"></path><path d="M611 818h0c0 2 1 3 2 4l-1 1v2l-3 6v-1l-1 2c-1 0-1 0-2-1v-2l5-11z" class="Q"></path><path d="M630 273c0 3 0 5-2 7v1c-3 4-5 6-10 7h0v-1c4-4 8-8 12-14z" class="E"></path><defs><linearGradient id="C" x1="444.168" y1="673.277" x2="439.213" y2="680.644" xlink:href="#B"><stop offset="0" stop-color="#616060"></stop><stop offset="1" stop-color="gray"></stop></linearGradient></defs><path fill="url(#C)" d="M435 684c4-7 11-14 18-17l-13 15-1-1-1 1c-1 1-2 1-3 2z"></path><path d="M676 301c-1-3-4-5-5-8h0c3 0 6 2 8 3l1 1s1 1 2 1l1-1v2 1l-3 3c-1-2 0-2-1-4-1 0-2 1-3 1v1h0z" class="C"></path><path d="M371 484c3 1 8 1 10 3 3 1 6 5 6 8h0c-2 0-3-2-5-2-3-3-7-6-11-9z" class="D"></path><path d="M437 268c5 4 10 9 13 14l3 9c-6-6-11-15-16-23z" class="I"></path><path d="M590 466c1 1 6 3 7 4 1 2 4 4 5 5 2 1 3 3 4 5l-11-7c-1-2-2-3-4-5-1-1-1-1-1-2z" class="K"></path><path d="M590 444l-7-3c-2-1-3-2-4-4-2-2-5-3-7-5 2 1 5 1 8 3h0c1 1 2 2 3 2 2 1 6 3 7 5v2zm-6-172h1v1c-4 5-7 12-12 17h0c2-8 5-13 11-18zM437 758c2 1 3 2 5 2h1c2 2 5 4 6 7h0c-1-1-1 0-2-1v-1c-1 1-2 1-2 2s1 2 2 2h-1c-3-2-6-5-9-8v-3z" class="B"></path><path d="M409 484c-4-1-8-5-10-8v-1c1 0 1 0 2-1h1c2 1 3 2 4 4 2 2 3 3 3 5v1z" class="I"></path><path d="M606 825c-1 4-4 8-6 11h-1c0-1 1-1 1-3v-4c1-3 4-8 6-11 0 2-1 5 0 7zM419 490c5-7 14-14 21-17l-20 18-1-1z" class="F"></path><path d="M411 339c7-2 13-4 19-9-1 4-3 6-7 8-1 1-2 2-3 2-2-1-7-1-9-1z" class="E"></path><path d="M597 462c0-3-2-5-2-7h1c3 4 10 11 11 17h-1c-4-3-6-7-9-10z" class="L"></path><path d="M705 567c2-3 6-6 9-8l6-3c1-1 2-2 3-2-3 4-5 8-9 11l-1-1-1 1v-1c-3 1-5 2-7 3z" class="F"></path><path d="M399 338c4 1 8 2 12 1 2 0 7 0 9 1l-6 2h-10l-6-3 1-1z" class="N"></path><path d="M407 656c3 4 4 7 4 11-2 2-2 4-3 7h2c1-1 1-1 2 0h0c-3 2-7 3-11 4h1l2-2c2-2 4-4 4-6 1-5 0-9-2-12 0-1 1-1 1-2z" class="R"></path><path d="M630 522c1 2 1 3 1 4-2 4-5 6-8 9v-9c3 0 5-2 7-4z" class="I"></path><path d="M411 482l1 1-1 4-6 5v1h-1c-1 0-3 1-4 1l-1-1c3-3 6-6 10-9v-1l2-1z" class="G"></path><path d="M431 503c4 5 8 10 11 16-2-1-4-3-6-4-1-1-2-2-2-3l-6-8c1 0 2 0 3-1z" class="Q"></path><path d="M314 527c-4-2-21-20-22-23 1 0 2 1 3 2l1 1 21 19h0c-2 0-3-1-4-2h0l2 3h-1z" class="J"></path><path d="M397 517c1-1 1-2 3-2 4 1 11 4 13 8 1 1 2 2 2 4l-2-1c-1-1-1-1-1-2-1-1-2-1-3-2h-1c-3-2-8-4-11-5zm14-246h1c2 1 3 2 5 2l1 1-3 9c-1 0-1 1-2 2h0l-2-14z" class="D"></path><path d="M586 483c-1-3-4-6-5-10l6 6v1c2 2 7 10 7 14h0-2l-1-1v-2c-2-3-3-5-5-8z" class="L"></path><path d="M598 761l1 1c-5 7-10 13-16 19h-1c1-1 1-4 1-5 1-2 5-4 7-6 3-2 5-6 8-9z" class="D"></path><path d="M664 427v1c-2 2-8 6-10 6h1c1-1 1-2 2-2h0l-1-3c-1 0-1 0-2 1h0-1c-1 1-1 1-2 1 0 0-1 0-1-1 3-3 8-6 12-9v1c-1 2-3 3-5 4 1 1 3 2 5 2h0l1-1h1z" class="B"></path><defs><linearGradient id="D" x1="593.353" y1="512.034" x2="591.293" y2="505.917" xlink:href="#B"><stop offset="0" stop-color="#343435"></stop><stop offset="1" stop-color="#4f4e4d"></stop></linearGradient></defs><path fill="url(#D)" d="M598 501h1c1 0 2-1 3-1h0l-11 15c-1 0-2-1-3-1l10-13z"></path><path d="M426 378c6-5 12-8 19-7h1c-6 3-12 4-17 9l-3-2z" class="N"></path><path d="M382 637h0l-2 2c-3 2-5 6-5 10-1 3-1 7 0 9v1 1l-2-3c-2-4-2-10 0-14s5-5 9-6z" class="H"></path><path d="M395 274h0c1 2 2 4 3 5 3 3 7 4 8 8l-1 1c-3-1-4-1-6-3-4-4-4-6-4-11z" class="D"></path><path d="M302 378c6-4 11-8 16-13 1 1 1 1 0 2-1 4-6 9-9 10l-3 1h-3-1z" class="K"></path><path d="M621 518c2-1 4-1 6-1 2 1 2 1 3 3 1 0 3-3 3-4 1-2 0-3 0-5v-1c2 2 6 7 7 10-4-1-6 0-9 2h-1l-2-3-7-1z" class="D"></path><defs><linearGradient id="E" x1="654.561" y1="555.913" x2="645.363" y2="549.481" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#E)" d="M644 546c4 3 8 6 12 10 0 0 1 1 1 2v1c-6-3-10-6-15-10 1-1 2-1 2-3z"></path><path d="M296 349h2c2 2 2 5 3 8l5-4h0c-1 1-2 3-4 4-2 0 0 1-1 2 0 0-1 0-1 1l-2 2h-1c1-1 2-1 2-3l-2-2c-2-4-4-4-8-5 3-1 5-2 7-3z" class="B"></path><path d="M707 366h1c4 5 10 8 15 11-2 1-3 1-4 1-4-1-9-5-11-7-1-2-1-3-1-5z" class="K"></path><path d="M322 314l13 7c1 1 3 3 5 3v2c-5-3-10-4-16-7l-1-1c-1-1-2-2-1-4z" class="J"></path><path d="M599 532h1l3-3v1c-1 8 0 17-1 25-2-5-3-14-2-20 1-1 1-2 1-2h-1 0l-1-1z" class="E"></path><defs><linearGradient id="F" x1="669.622" y1="305.374" x2="670.39" y2="312.236" xlink:href="#B"><stop offset="0" stop-color="#585858"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#F)" d="M670 300l3 8 1 7c-1 2 0 4-1 6v-6h-4c0-3-1-5-2-8v-1c1 0 2-1 3-1h0 1c-1-2-1-3-1-5z"></path><path d="M595 473c-7-3-14-7-22-10 6 0 12 0 17 3 0 1 0 1 1 2 2 2 3 3 4 5z" class="C"></path><path d="M722 358v-1h0v-2h2l5-6c2 1 4 2 6 4v1h-4c-2 1-7 7-7 6h-2v-2z" class="B"></path><defs><linearGradient id="G" x1="706.311" y1="565.4" x2="706.148" y2="573.056" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#4d4c4c"></stop></linearGradient></defs><path fill="url(#G)" d="M705 567c2-1 4-2 7-3v1l1-1 1 1-12 11c0-1 0 0 1-1 0-1 0-1-1-2h-2l-4 3 9-9z"></path><defs><linearGradient id="H" x1="326.183" y1="519.685" x2="332.317" y2="508.315" xlink:href="#B"><stop offset="0" stop-color="#1c191b"></stop><stop offset="1" stop-color="#414343"></stop></linearGradient></defs><path fill="url(#H)" d="M341 514c-3 1-5 0-7 1-8 1-14 2-22 1 9-3 21-4 30-4 0 1-1 1-1 2z"></path><path d="M323 320c-6-4-12-8-17-13 5 2 11 4 16 7-1 2 0 3 1 4l1 1-1 1z" class="G"></path><path d="M798 370l1-3c1 0 1 0 1 1-1 10-4 20-7 30h0c-1-1 0-3 0-5 1-7 3-15 5-23z" class="C"></path><path d="M590 753v2c1 0 2-1 3-1v1h1c-3 4-7 9-11 12-2 1-2 1-3 1l-1-1c4-5 8-9 11-14z" class="P"></path><path d="M662 403v1c0 1 0 1-1 2 0 1 0 2-1 3l-1 2c-1 5-4 10-9 13h0l-1 1v-1c1-2 5-5 6-7h0-1c-1 0-1 0-2 2l-2 2v-1c0-1 1-2 2-3h1c1-3 4-8 5-11v-2h1v3 1h0 1c0-1 1-3 2-5z" class="D"></path><path d="M428 504c-5-6-11-11-15-18 2 1 4 2 6 4l1 1 11 12c-1 1-2 1-3 1z" class="G"></path><path d="M397 566c8-1 18 0 25 6l1 1-1 1c-9-3-16-6-25-7v-1z" class="J"></path><path d="M616 360h-1c-2-1-4-1-6-2-4-2-9-4-11-8l1-1c6 5 13 8 20 9 2 1 5 1 7 2-3 1-6 1-10 0z" class="H"></path><defs><linearGradient id="I" x1="721.609" y1="405.269" x2="714.798" y2="408.536" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#5b5b5b"></stop></linearGradient></defs><path fill="url(#I)" d="M708 418c3-5 8-10 12-14 2-2 9-9 11-8h0c-1 0-1 1-2 2h0l-3 3v1c-1 1-3 2-3 3-1 1-1 2-2 2-1 1-1 2-2 3s-3 2-4 3v-2l-4 5c-1 1-2 1-3 2z"></path><path d="M446 585l9 2c-2 2-5 4-6 8v1c-2 0-4 0-6-1 1-2 3-7 3-10z" class="C"></path><path d="M597 462v2c1 1 1 1 1 2v1l-2-1c1-4-3-6-4-9l-9-12c1 1 1 0 1 1l6 3 6 6h-1c0 2 2 4 2 7z" class="D"></path><path d="M406 792h1l1 3v1h1 1c0 3 2 6 3 8l10 20h0c-1-2-2-5-3-6l-2-3c0-1-1-3-2-4s-1-2-1-2l-1-2-1 1v1 1l3 6c0 1 0 1 1 2h0v2l1 1h0c1 1 0 0 1 2v1h0l1 2c1 0 1 1 1 2h0v1c-3-4-5-11-7-15-1-3-2-6-3-8-2-5-4-10-5-14z" class="B"></path><path d="M744 501c2 1 4 3 6 3l-1 1 1 2-3 7c-2-1-5-3-6-5l3-8z" class="G"></path><path d="M648 308l-14-5 23 2 1 1c-1 2-2 3-3 5-2-1-4-1-6-2l-1-1zM431 495c4-9 7-18 16-23-3 6-7 11-10 17-1 1-3 3-3 4v1l-3 1z" class="F"></path><path d="M573 716c1-1 1-1 3-1 3 0 7 0 9 2-1 3-2 6-4 8-1 1-2 2-3 2 1-1 2-2 3-2l-2-1c0-1 0-1-1-1v-1c0-1 0-3-2-4l-3-2z" class="L"></path><defs><linearGradient id="J" x1="596.516" y1="846.927" x2="607.206" y2="832.149" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#535252"></stop></linearGradient></defs><path fill="url(#J)" d="M606 829v2c1 1 1 1 2 1l1-2v1c-3 8-8 16-14 22 3-8 7-16 11-24z"></path><path d="M584 595l-7 2-1 1 1-7c-2-2-4-1-5-2v-1c1-2 4-2 6-3 1 2 2 3 3 5 1 1 2 3 2 5h1z" class="C"></path><path d="M639 312l1-1c1 0 3 0 4 1h3 2v1h4l2 1c4 0 7 0 11 2-4 0-7 1-10 1-5 0-12-1-17-3v-1c2 1 3 1 5 0-2-1-3-1-5-1z" class="L"></path><path d="M365 314c2 0 4-1 5-1 2 0 4 0 5-1h2c5-1 9-2 14-2l-9 3c-2 1-4 3-5 3-4 1-9 1-12 0v-2z" class="F"></path><path d="M674 637c1 1 1 2 3 3h0l-5 5-1 2h-2c-1 1-1 1-2 1l-1-1-1 1c-1-1-2-1-2-2 3-3 7-6 11-9z" class="L"></path><path d="M447 713h1c1 1 1 2 2 2 0 1 1 1 2 2-1 1-3 2-5 4v5h-1-1c-3-3-3-6-4-11 2 0 4-1 6-2z" class="I"></path><path d="M579 443l17 23c-2-1-5-2-6-3h-1c-1-2-2-3-3-4-3-5-6-10-7-16z" class="F"></path><path d="M706 312l1 1-2 3c-5 3-11 5-16 7-2 1-4 3-6 3h-2c-1-2-1-2-1-4l3-3h0l-1 2c0 2 0 2 1 4h2l3-3 7-4c3-3 7-4 11-6z" class="J"></path><path d="M722 360c-6-6-10-12-11-20v-1h-1l-1 1c0 4 2 9 3 12l-5-1c-2-1-2-1-2-3-1-3 2-7 4-10v1h2c2 1 1 2 1 4 1 2 1 3 2 5 0 2 1 3 2 5h1c1 2 3 4 4 5h1v2z" class="D"></path><path d="M338 517c2-1 9 0 12 0 2 0 3 1 4 2l-1 1c0-1-4-1-6-1-1-1-7-1-9 0h-3l-8 4c-2 0-4 0-5-1h-1c2-2 5-3 8-3l1-1h3 1c1-1 3-1 4-1z" class="E"></path><defs><linearGradient id="K" x1="611.705" y1="333.602" x2="617.327" y2="343.453" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#K)" d="M609 341c-4-1-8-2-11-5h0 1c1 1 2 1 3 1l1 1 1-1-2-1v-1c-2 0-3-1-4-3h0c2 1 3 3 5 4h1c7 3 15 3 23 2h1l-1 1-5 3c-5 0-9-2-13-1z"></path><path d="M817 368l-10 6c-1-1-2-2-3-4l1-5c1 0 1 1 2 1 0 0 1-1 2-1 1 1 2 0 3 0h3c1 1 2 1 2 3z" class="Q"></path><path d="M341 636c2 4 5 7 8 10l3 2-1 3h0c1 2 1 2 1 4l-3-2-8-6 1-2c-1-3-1-6-1-9z" class="C"></path><path d="M349 646l3 2-1 3h0c1 2 1 2 1 4l-3-2 1-2h-1c-1-2-1-3 0-5zm360-264h-6c-3-1-7-4-10-6 6 1 12 3 19 4 2 0 12-2 13-1-2 1-4 3-6 3h-10z" class="B"></path><path d="M581 370h1c6 0 14 5 19 9 1 2 3 3 3 5h0c-2 1-3 0-5 0-3-5-7-7-12-10-2-1-5-2-6-4z" class="G"></path><path d="M673 308l1-1v-3c-2-4-3-7-6-11h0 2c0-2-3-3-4-5h1l4 4h1v-1l-1-1v-2l2 2c2 4 6 1 9 3l-2 1v1h0l-1 1h0c-2-1-5-3-8-3h0c1 3 4 5 5 8-2 3 0 7-1 10l1 1c0 1-1 2-2 3l-1-7zM405 492c2 1 3 2 4 4 4 7 10 12 16 18h-1l-5-5-2-2-2-1v-1c-1 0-1-1-1-2h-1v1l1 1c1 3 5 6 7 8h0c-2 0-5-2-6-4-2-3-4-10-7-11h-1c1 3 3 6 4 9-2-2-5-4-7-7 0-2 0-5 1-7h0v-1z" class="D"></path><defs><linearGradient id="L" x1="338.322" y1="584.563" x2="347.537" y2="590.442" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#L)" d="M336 582l1-1c1 1 2 2 4 3 3 1 5 4 8 6h0c0-1-1-2-1-2v-1h1l2-2 2 1c2 1 2 1 3 3l-2 2c-3 2-7 1-10 0-3-2-5-6-8-9z"></path><defs><linearGradient id="M" x1="615.491" y1="572.509" x2="618.009" y2="564.491" xlink:href="#B"><stop offset="0" stop-color="#8a898a"></stop><stop offset="1" stop-color="#afafaf"></stop></linearGradient></defs><path fill="url(#M)" d="M607 569c6-2 14-4 21-2v1c-2 1-9 1-12 2-8 2-14 5-21 8 3-2 6-5 9-7l3-2z"></path><defs><linearGradient id="N" x1="414.37" y1="354.238" x2="414.149" y2="360.263" xlink:href="#B"><stop offset="0" stop-color="#b0b1b0"></stop><stop offset="1" stop-color="#d5d2d5"></stop></linearGradient></defs><path fill="url(#N)" d="M431 347v1c-2 4-7 7-11 10-1 0-5 2-6 2-3 1-7 2-11 2-1-1-3-1-4-2 13 0 22-5 32-13z"></path><path d="M630 576c-1-4-1-10 1-14l3-5c0 7 1 13 3 20v2l-1-1c0-1-1-2-1-3l-2-2c-1 1 0 2-1 4l-1-2-1 1z" class="I"></path><path d="M357 634c4 1 9 2 13 2 1 0 1 1 2 2s0 3 0 4l-1 4v1c-2 0-2-1-2-2-3-4-8-7-12-11z" class="B"></path><defs><linearGradient id="O" x1="356.334" y1="517.909" x2="356.166" y2="512.091" xlink:href="#B"><stop offset="0" stop-color="#232424"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#O)" d="M342 512c12 0 23-1 33 7l-2 2-1-1c-5-6-24-6-31-6 0-1 1-1 1-2z"></path><path d="M630 576l1-1 1 2c1-2 0-3 1-4l2 2c0 1 1 2 1 3l1 1c2 4 4 8 7 12h0l-4-2h0l-3-3-1 1h0l2 2h0-1c-4-4-6-8-7-13z" class="N"></path><path d="M593 400h1c2 2 5 3 8 3l-3 6v2c-1 1 0 1-1 1v1l-1-2-1-1-1 2h0l-2-1c0-1 0-2-1-3h0l-1-1-1-1v-1c0-1 1-3 1-4l2-1z" class="B"></path><defs><linearGradient id="P" x1="425.7" y1="833.205" x2="409.926" y2="822.184" xlink:href="#B"><stop offset="0" stop-color="#3e3e3f"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#P)" d="M414 814c2 4 4 11 7 15l2 5-1 7c-3-6-6-12-8-18 0-2 0-2-1-4h0l1-2v-3z"></path><path d="M431 710c3 6 5 11 8 16 2 3 5 5 7 8v1l-1-2c-2 0-4-3-5-4v-1c-2 1-3 1-4 1l-1 1h1v1l1 1c2 2 4 4 6 7h0c-4-4-8-8-10-13l-1-1c-2-5-1-9-1-15z" class="B"></path><path d="M410 493c7 5 11 12 16 18 2 3 5 7 8 9h0l-1 1h0-1c-2-1-3-2-4-3s-1-3-3-5c-4-7-12-11-15-19v-1z" class="D"></path><path d="M606 818l8-15c1-2 2-4 3-7h0c1-1 1-1 2-1v-2c1-1 0-1 0-2 1 0 2-1 2-2l-6 20c-1 1-2 3-2 4h-1l-6 12c-1-2 0-5 0-7z" class="B"></path><defs><linearGradient id="Q" x1="707.247" y1="423.563" x2="705.131" y2="421.968" xlink:href="#B"><stop offset="0" stop-color="#525152"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#Q)" d="M708 418c1-1 2-1 3-2l4-5v2c-3 4-6 8-9 11-3 5-5 11-8 15-1 2-3 3-4 4 3-6 5-12 9-18 1-2 3-5 5-7z"></path><path d="M446 445l1 1c-1 5-3 12-8 15-3 3-6 5-10 7 2-4 6-8 9-12 3-3 5-8 8-11z" class="E"></path><path d="M619 639c4-2 6-3 11-4h0c7-1 14 0 19 5l2 1 2 2c2 6 1 11-1 17-1-2 0-7-1-10 0-2 0-4-1-6-1-3-5-5-8-7-6-2-14-1-20 1-1 1-1 1-2 1h-1z" class="H"></path><path d="M584 472c3 0 6 2 8 3 4 3 8 6 11 9 2 1 3 3 4 4l-2 1c-6-3-12-7-17-13-2-1-3-3-4-4z" class="D"></path><path d="M217 278l1-1c2 4 4 9 6 13 0 1 0 1 1 2l1 2v1l1 1v2l2 4v3c1 2 2 5 2 8 1 0 1 1 1 1 0 1 0 1 1 2v2c0-3 0-6 1-8v-1c-1-1-1-2-1-3v-4c-1-1-1-2-1-2l-1-3c-1-2-2-4-2-5-1-3-2-5-3-7-1-1-1-2-2-3v-1c3 5 5 9 7 15 3 7 6 14 4 22-1 3-1 4-3 6v-5h0c0-4-2-7-3-11-2-10-6-22-12-30z" class="E"></path><path d="M576 506h2c2 1 3 5 3 7 1 2 1 6 1 8-1 1-2 3-3 5-2-6-3-14-6-19h0c0-1 2-1 3-1z" class="F"></path><defs><linearGradient id="R" x1="415.94" y1="713.694" x2="406.617" y2="693.712" xlink:href="#B"><stop offset="0" stop-color="#b3b2b2"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#R)" d="M402 693l3-7c1 5 2 9 5 14s9 9 10 15c-2-1-3-2-4-4l-11-12c0-3 1-7-1-10 0 1-1 2-1 3l-1 1z"></path><path d="M390 595h1c2 0 3-1 4-1s1 2 1 3h-1c-1 1-2 1-3 2h0c-6 3-12 7-17 11l-6 6c4-8 13-17 21-21z" class="M"></path><path d="M392 302h2c-2 1-4 2-7 3-5 2-10 5-16 6-2-1-3-3-4-6h6l19-3zm277 85h1c2 1 3 3 4 4l7 8c1 0 4 2 5 3v1c-1 1-2 1-2 1-2 0-4 0-5-1-3-2-8-7-9-11l-1-5z" class="E"></path><path d="M314 385c6 0 12 2 17 2 4 0 8-2 11-1v1l-1 1c-3 2-5 2-8 3-8 1-13-1-19-6z" class="F"></path><defs><linearGradient id="S" x1="785.122" y1="389.599" x2="796.826" y2="381.595" xlink:href="#B"><stop offset="0" stop-color="#252724"></stop><stop offset="1" stop-color="#59565a"></stop></linearGradient></defs><path fill="url(#S)" d="M796 368l-7 38h0l-1-1c1-15 3-28 7-42v2c1 1 0 2 1 3l-1-1c1-1 1-2 2-3l-1 4z"></path><path d="M323 361l-9-6c9-1 18-1 26 1v1c-5 2-11 4-17 4z" class="G"></path><path d="M448 505c2 0 2-1 4 1 0 3-1 6-2 9l-3 9c-1-1-2-3-2-4-2-3-2-7-1-11 1-2 2-3 4-4z" class="E"></path><defs><linearGradient id="T" x1="319.217" y1="432.944" x2="324.283" y2="417.556" xlink:href="#B"><stop offset="0" stop-color="#3c3c3a"></stop><stop offset="1" stop-color="#69696b"></stop></linearGradient></defs><path fill="url(#T)" d="M318 422c-1-2-3-4-4-6l-7-10 1-1c8 8 17 19 21 30 1 2 2 4 2 5v1c-5-6-8-13-13-19z"></path><path d="M440 447l1 1c-1 4-6 7-9 11-3 3-6 7-9 10-2 2-3 2-5 3h-1c3-3 5-7 7-10 4-6 10-11 16-15z" class="F"></path><path d="M808 346c1-8-5-18-7-27l3 4h1c1 0 3 2 3 3-2 0-1 0-2-1 0 1 1 1 1 3h0c1 1 1 2 2 3 2 2 5 5 4 9v1c-1 1-1 4-2 6l-1 1h-1l-1-2z" class="B"></path><path d="M809 331c2 2 5 5 4 9v1c-1 1-1 4-2 6 0-6-2-11-2-16z" class="L"></path><path d="M429 610c2 1 5 1 7 3l-6 3c-2 2-3 4-4 7h-1c-1-2-2-2-3-3h-7l2-3c4-3 7-5 12-7z" class="G"></path><path d="M422 620c3-1 5-4 8-4-2 2-3 4-4 7h-1c-1-2-2-2-3-3z" class="P"></path><path d="M318 422l-1-1-1-1-1-1c0-1-1-2-2-2l-2-2v-1l-1 1 2 3 1 1 1 1c1 1 0 1 0 2-5-7-10-14-16-20h0c2 0 4 3 5 4h0l-2-2v-1l-1-1s-1 0-1-1c-1 0-1-1-1-2l-1-1c-1-1-1-2-3-3l-1-1h1s2 1 3 1c3 3 8 6 11 10l-1 1 7 10c1 2 3 4 4 6z" class="B"></path><path d="M590 444v-2c-1-2-5-4-7-5-1 0-2-1-3-2h0c11 4 22 10 31 18 2 2 5 4 7 7-2-1-4-3-6-4s-4-1-5-2c-2 0-5-2-6-4-4-2-7-4-11-6z" class="C"></path><defs><linearGradient id="U" x1="819.841" y1="348.124" x2="802.159" y2="358.376" xlink:href="#B"><stop offset="0" stop-color="#514f4e"></stop><stop offset="1" stop-color="#727273"></stop></linearGradient></defs><path fill="url(#U)" d="M811 347c1-2 1-5 2-6 1 0 1 1 1 2l1-2 1-1 1 2c0 2-1 4-1 5-1 2-2 3-2 4-2 5-3 10-5 14-1 0-2 1-2 1-1 0-1-1-2-1l3-9c-1-3 0-6 0-10l1 2h1l1-1z"></path><path d="M811 347c1-2 1-5 2-6 1 0 1 1 1 2-1 4-4 9-6 13-1-3 0-6 0-10l1 2h1l1-1zm-505 31v1h4c4-1 8-2 11-2 2-1 5-1 6-1s2 1 3 1v1h0c-3 1-6 3-9 5-6 1-12 0-18-3 0 0-1-1-2-1h0l-1-1h2 1 3z" class="F"></path><path d="M363 557l1 1c2-2 4-3 6-6 0-1 1-1 2-2 1 0 2-1 3-1 0 2 0 4 1 6v1c0 1 0 2-1 3l-12 3c-3 1-5 2-9 2l2-1c2-1 4-3 6-5l1-1z" class="D"></path><path d="M449 277c-2-2-5-6-7-8 4 0 10 1 14 1 3 0 7 0 10 1v1c-1 2-2 3-4 4l-1-1h-1c-3 1-6 1-9 1-1 0-1 0-2 1z" class="F"></path><path d="M820 321c-6-2-11-6-13-13-3-8-2-17 2-25 0 8-1 15 2 23 1 4 5 11 9 14v1z" class="I"></path><path d="M613 320v-1c3 0 6-1 9 0 1-2 3-3 5-4l1 1v2h0c1-2 2-3 3-5h8v1h-1v1l3 2c-2 1-5 4-7 4-1 1-2 1-3 2 0-1-2-1-3-1l-1-1h-2c-2-1-4 0-6 0v-1l-2 1c-1-1-2-1-4-1z" class="E"></path><defs><linearGradient id="V" x1="592.653" y1="396.64" x2="582.682" y2="391.768" xlink:href="#B"><stop offset="0" stop-color="#525252"></stop><stop offset="1" stop-color="#757374"></stop></linearGradient></defs><path fill="url(#V)" d="M590 405c-3-4-4-8-6-13l-3-6c1-2 1-2 3-3 1 0 2 2 3 3h1l-1-3h1c3 5 3 11 5 17h0l-2 1c0 1-1 3-1 4z"></path><defs><linearGradient id="W" x1="660.189" y1="320.046" x2="669.883" y2="337.196" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#b6b4b5"></stop></linearGradient></defs><path fill="url(#W)" d="M669 315h4v6c-1 8-4 17-11 22-1 1-4 1-5 1h-1l3-3c8-7 10-16 10-26z"></path><path d="M597 470c11 6 21 15 29 23 2 2 5 4 5 6-1 1-1 1-2 1-4-2-6-5-9-7-4-5-9-9-14-13-1-2-2-4-4-5-1-1-4-3-5-5z" class="G"></path><defs><linearGradient id="X" x1="752.329" y1="490.593" x2="745.486" y2="501.886" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#X)" d="M748 486c3 0 6 1 8 3l-6 18-1-2 1-1c-2 0-4-2-6-3l4-15z"></path><path d="M570 269h13v2c-4 1-4 6-8 7h-1c-6-1-10-3-14-8l10-1z" class="E"></path><defs><linearGradient id="Y" x1="781.032" y1="325.631" x2="802.968" y2="363.369" xlink:href="#B"><stop offset="0" stop-color="#626362"></stop><stop offset="1" stop-color="#8e8e8f"></stop></linearGradient></defs><path fill="url(#Y)" d="M795 363c0-6 2-12 1-19-1-5-3-10-5-15s-4-9-4-14h0c1 1 2 4 3 5 5 10 9 19 9 31-1 4-1 9-2 13-1 1-1 2-2 3l1 1c-1-1 0-2-1-3v-2z"></path><path d="M584 585c-1-5-1-10-1-14-1-4-1-8-1-12v1h1c3 4 4 8 5 12l1 6c2 1 2 1 4 1-1 2-2 3-3 4v1h9 0l-15 1z" class="D"></path><path d="M588 572l1 6c2 1 2 1 4 1-1 2-2 3-3 4-2 0-2-1-3-1-1-3 1-6 1-9v-1z" class="F"></path><defs><linearGradient id="Z" x1="430.489" y1="477.595" x2="421.977" y2="470.55" xlink:href="#B"><stop offset="0" stop-color="#4c4c4d"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#Z)" d="M444 464v1c-1 1-1 2-2 3-11 5-22 12-31 19l1-4-1-1c1-1 4-2 6-4 9-6 16-11 27-14z"></path><defs><linearGradient id="a" x1="684.691" y1="620.465" x2="689.442" y2="624.653" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#a)" d="M674 637c4-4 9-8 12-13 5-8 8-16 14-22-4 14-12 28-23 38h0c-2-1-2-2-3-3z"></path><path d="M606 615c3 1 5 3 8 3l1 4v5c-2 1-4 3-6 4h-2l-1 1c0-1-1-2-1-3l-2-2c-1-2-2-2-2-4 2-2 5-5 5-8z" class="I"></path><path d="M709 382h10c-6 3-11 8-17 9-5 2-13 0-17-3-1-1-1-1-1-2 2-2 8 1 11 1l9-3c1-1 4-1 5-2h0z" class="E"></path><path d="M624 749l1-1c0-2 1-3 1-4 1 1 1 0 2 1h0c0 5-1 10-2 15 0 3-2 6-2 8l1 1c0 3-5 9-7 11h-1c-1-3 0-7 1-10 3-7 5-13 6-21z" class="K"></path><defs><linearGradient id="b" x1="388.501" y1="560.789" x2="397.339" y2="575.605" xlink:href="#B"><stop offset="0" stop-color="#343333"></stop><stop offset="1" stop-color="#555"></stop></linearGradient></defs><path fill="url(#b)" d="M390 556c3 3 5 7 7 10v1 1 6c-1 7-6 13-11 17l-1-1c0-3 2-5 3-7s1-3 2-5c1-1 1-3 2-5l-1-3c1-4 1-9-1-13v-1z"></path><path d="M392 573c1 2 2 5 1 7 0 2-1 3-2 4h-2l-1-1c1-2 1-3 2-5 1-1 1-3 2-5z" class="O"></path><defs><linearGradient id="c" x1="420.467" y1="664.293" x2="409.144" y2="664.817" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#c)" d="M411 667h0l8-13 3 14c0 2 0 5-1 5-1 2-3 1-5 1h-4 0c-1-1-1-1-2 0h-2c1-3 1-5 3-7z"></path><defs><linearGradient id="d" x1="223.985" y1="401.366" x2="213.814" y2="402.598" xlink:href="#B"><stop offset="0" stop-color="#a9a7a9"></stop><stop offset="1" stop-color="#e0e2e1"></stop></linearGradient></defs><path fill="url(#d)" d="M207 376c12 10 18 26 20 42 1 6 2 14 2 20-1-3-2-8-3-12-3-16-6-33-18-45v-1c1 1 2 1 2 2l1 1h1c-1-2-4-4-5-6v-1z"></path><path d="M722 540v1l-2 2 2 1h1l1-1h1c-2 2-6 5-8 7-9 7-17 14-27 17h-3l-1 1c0-1-1-2-1-3 14-4 26-15 37-25z" class="G"></path><path d="M583 560l-4-8c2 1 7 2 9 3 1 1 2 3 2 4 3 6 4 13 3 20-2 0-2 0-4-1l-1-6c-1-4-2-8-5-12z" class="C"></path><defs><linearGradient id="e" x1="344.551" y1="292.62" x2="349.911" y2="301.424" xlink:href="#B"><stop offset="0" stop-color="#282727"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#e)" d="M359 287h0v1l1-1v1c-2 2-4 4-5 6-3 4-5 8-8 11-2-1-3-3-4-4l-8-8h1c3 0 9-1 11 1h1 1l1-2c2-3 5-4 9-5z"></path><path d="M431 347l3-3v1l-1 2c-1 1-1 2-1 2-1 1-2 3-3 4l4 2c1 0 2-2 3-2 0 1 0 0-1 1-7 7-14 11-23 15l-3-6c1-1 1 0 2-1 1 0 2-1 3-1v-1c1 0 5-2 6-2 4-3 9-6 11-10v-1z" class="I"></path><defs><linearGradient id="f" x1="591.752" y1="619.91" x2="594.337" y2="609.712" xlink:href="#B"><stop offset="0" stop-color="#606060"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#f)" d="M579 610c10-2 17-2 26 4l1 1c0 3-3 6-5 8 0 2 1 2 2 4h-1c-1-1-1-2-2-3l-1-2c-3-6-9-8-14-10-2-1-5-1-6-2z"></path><path d="M327 601c1 0 1 0 1 1l10 15c2 2 3 5 5 8l6 8c3 4 7 7 10 11h0l-1-1-1 1c0 1 0 1-1 2 1 0 1 1 2 2h-1c-10-9-18-22-24-34-1-2-2-5-3-7s-3-4-3-6z" class="C"></path><defs><linearGradient id="g" x1="360.322" y1="555.912" x2="372.048" y2="571.417" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#dddcdc"></stop></linearGradient></defs><path fill="url(#g)" d="M345 567c12 2 27 0 38-6 2-2 5-5 7-5v1c-3 7-11 10-17 12-8 3-17 3-25 2v-1c-1 0-1 0-2-1-1 0-1-1-1-2z"></path><path d="M371 589l1 1c6 0 9-3 13-7 0-1 1-2 2-2h1c0-1 0-1 1-2l1-1c-1 2-1 3-2 5s-3 4-3 7l1 1c-6 4-17 7-23 7-2-1-3-2-3-3 1 0 6-3 8-4l3-2z" class="B"></path><defs><linearGradient id="h" x1="442.057" y1="591.532" x2="429.222" y2="586.287" xlink:href="#B"><stop offset="0" stop-color="#585859"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#h)" d="M432 584h10l4 1c0 3-2 8-3 10-6-1-12-2-18-2h1l1-1c1-1 2-3 3-4h0c-3-1-6 0-9-1h0c2 0 4-1 6-1v-1h0l5-1z"></path><defs><linearGradient id="i" x1="231.09" y1="343.999" x2="221.932" y2="341.779" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#i)" d="M232 319v5c-2 5-4 9-5 14-2 9-1 17 0 26v1h-1-1l-2-4c-1-4-2-8-2-12 0-3 0-6 1-9 2-8 6-14 10-21z"></path><path d="M454 432l1 1c-1 0-1 1-2 1-4 1-5 3-8 5l-15 9c-5 2-10 5-14 8h0l1-1h-2c-1 0-2 0-3 1v-1c6-6 14-11 22-15 7-3 13-6 20-8z" class="B"></path><defs><linearGradient id="j" x1="792.533" y1="423.748" x2="818.918" y2="386.716" xlink:href="#B"><stop offset="0" stop-color="#8e8e8e"></stop><stop offset="1" stop-color="#eae7e9"></stop></linearGradient></defs><path fill="url(#j)" d="M817 377c0 3-3 4-3 6 1 0 0 0 1-1l1-1h1c-12 15-15 34-18 51-1 4-1 10-3 14v-1c0-5 1-10 1-15 2-19 6-39 20-53z"></path><path d="M210 340l6 13c3 5 7 11 5 17-1 2-1 2-2 3-4 0-10-5-13-7 2-3 7-3 10-4l-4-10c-1-3-2-7-3-11l1-1z" class="Q"></path><defs><linearGradient id="k" x1="616.934" y1="724.565" x2="633.13" y2="734.791" xlink:href="#B"><stop offset="0" stop-color="#292a2a"></stop><stop offset="1" stop-color="#646263"></stop></linearGradient></defs><path fill="url(#k)" d="M618 704l4-5c2 9 4 18 5 28 1 5 2 12 1 18h0c-1-1-1 0-2-1 0 1-1 2-1 4l-1 1c-1-5 0-9-1-14 0-4-1-12-6-15-1-1-2-1-3 0-3 3-3 6-5 9v-1-1c0-2 1-3 2-5l1-1v-1c1-1 1-1 3-1 2-1 3 0 4 2 1-1 2-1 2-2 1-2 2-3 4-5-1-1-1-2-1-3h0l-1-1 1-2-1-1v-2c-1-1-1-3-2-4-1 1-1 2-3 3h0z"></path><defs><linearGradient id="l" x1="650.475" y1="285.931" x2="663.67" y2="308.92" xlink:href="#B"><stop offset="0" stop-color="#101011"></stop><stop offset="1" stop-color="#585858"></stop></linearGradient></defs><path fill="url(#l)" d="M667 307l-2-4c-6-10-13-10-23-12 3-1 5-4 8-5 2 0 4 0 6 1 6 3 11 7 14 13 0 2 0 3 1 5h-1 0c-1 0-2 1-3 1v1z"></path><path d="M696 576l4-3h2c1 1 1 1 1 2-1 1-1 0-1 1-3 5-9 10-12 15h1 1c0 2-3 4-4 6-1 0-1 0-2 1l-10 3 3-4 2-3c1-3 4-5 6-8 3-3 6-7 9-10z" class="Q"></path><path d="M692 591c0 2-3 4-4 6-1 0-1 0-2 1l-10 3 3-4 2-3c1 0 2 0 3 1h3c1-1 3-2 4-4h1z" class="P"></path><path d="M681 594c1 0 2 0 3 1h3c-3 2-5 2-8 2l2-3z" class="K"></path><defs><linearGradient id="m" x1="439.576" y1="546.709" x2="434.788" y2="518.901" xlink:href="#B"><stop offset="0" stop-color="#252524"></stop><stop offset="1" stop-color="#838383"></stop></linearGradient></defs><path fill="url(#m)" d="M425 519l17 12c2 2 5 6 8 7 1 4 1 5 0 8v1l-25-22c-1-1-1-2-1-2l-1-1c2-1 2-1 2-3z"></path><defs><linearGradient id="n" x1="584.873" y1="544.149" x2="592.485" y2="519.806" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#n)" d="M600 521c0 1 0 2 2 3h0l1 1-28 22-2-6 27-20z"></path><defs><linearGradient id="o" x1="704.364" y1="585.212" x2="712.267" y2="589.641" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#404040"></stop></linearGradient></defs><path fill="url(#o)" d="M707 576l2-2 2-1c2-2 3-4 5-6l-11 39-2-2c-1-2 1-5 1-7l-1 1c-1-1-3-1-5 0v-3c2-3 4-5 5-7l7-12h-1l-1 1-1-1z"></path><defs><linearGradient id="p" x1="444.377" y1="573.743" x2="431.464" y2="563.126" xlink:href="#B"><stop offset="0" stop-color="#202121"></stop><stop offset="1" stop-color="#4e4d4e"></stop></linearGradient></defs><path fill="url(#p)" d="M437 556c2-1 3-1 5-1l1 3c-2 3-3 7-4 11h0c0 4-1 11 0 15h3-10 4c0-1 0 0-1-1h-1c-1-2-1-4-2-6-1-6 1-16 5-21z"></path><defs><linearGradient id="q" x1="274.836" y1="497.78" x2="283.032" y2="508.187" xlink:href="#B"><stop offset="0" stop-color="#6a6a6a"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#q)" d="M280 516c-2-4-4-8-5-12-2-5-4-10-5-15 0-1 1-1 1-1h0 1v-1l1-1v1l1-1v-1h3l7 23h0c-1 1-3 2-4 3s-1 1-1 2c1 1 1 2 1 3z"></path><defs><linearGradient id="r" x1="369.441" y1="287.938" x2="367.563" y2="306.517" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#r)" d="M358 296l3-3c1-1 2-1 3-2h1c3-3 9-4 13-3l6 3c-9 1-16 3-22 11-1 2-3 5-4 7v2c-1-1-3-2-4-3v-2c1-4 3-7 4-10z"></path><path d="M354 306l2-1v1l2 3v2c-1-1-3-2-4-3v-2z" class="K"></path><defs><linearGradient id="s" x1="793.684" y1="341.321" x2="804.811" y2="342.367" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#818181"></stop></linearGradient></defs><path fill="url(#s)" d="M795 320c7 11 13 22 9 36l-4 12c0-1 0-1-1-1l-1 3c-1 0-1-1-2-2l1-4c1-4 1-9 2-13h2c1-11-3-19-7-29l1-2z"></path><path d="M799 351h2c0 5 0 11-2 16l-1 3c-1 0-1-1-2-2l1-4c1-4 1-9 2-13z" class="E"></path><defs><linearGradient id="t" x1="623.389" y1="600.372" x2="632.606" y2="591.133" xlink:href="#B"><stop offset="0" stop-color="#a0a09f"></stop><stop offset="1" stop-color="#e2e0e2"></stop></linearGradient></defs><path fill="url(#t)" d="M606 585c13 2 26 6 36 15 1 1 4 2 5 4-1 0-1 0-1 1v1l-1-1c-2-1-3-3-5-3-1 0-2-1-3-1l-6-2c-5-3-11-4-17-5l1-1 1 1v-4h0 2l-10-3c-2 0-5 0-6-1 1-1 3-1 4-1z"></path><defs><linearGradient id="u" x1="669.603" y1="552.752" x2="652.472" y2="572.662" xlink:href="#B"><stop offset="0" stop-color="#7a7b7a"></stop><stop offset="1" stop-color="#cbcaca"></stop></linearGradient></defs><path fill="url(#u)" d="M690 567l-1 1c-11 5-28 5-39 1-4-2-13-6-15-11v-2c14 11 33 14 50 9 0 1 1 2 1 3l1-1h3z"></path><path d="M239 315v4c-3 9-7 18-8 27-1 16 6 31 6 48l-1-1-3-12c-4-18-8-35 0-52 1-5 4-9 6-14z" class="B"></path><defs><linearGradient id="v" x1="585.967" y1="807.412" x2="616.518" y2="775.989" xlink:href="#B"><stop offset="0" stop-color="#1c1c1d"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#v)" d="M618 770c-1 3-2 7-1 10h1c-8 11-20 20-31 26h-2 0c-1-1-1-1-1-2 7-5 14-11 20-17 6-5 10-11 14-17z"></path><defs><linearGradient id="w" x1="380.315" y1="546.705" x2="394.165" y2="535.875" xlink:href="#B"><stop offset="0" stop-color="#747474"></stop><stop offset="1" stop-color="#9a999a"></stop></linearGradient></defs><path fill="url(#w)" d="M378 541c6-2 14-4 20-2l1 1v1c-6 2-11 6-16 10-2 2-5 3-7 5v-1c-1-2-1-4-1-6-1 0-2 1-3 1-1 1-2 1-2 2-2 3-4 4-6 6l-1-1 9-9c1 0 2-2 3-2 0 0 3-2 4-2l-1-3z"></path><path d="M379 544c4-1 7-2 11-3-4 3-10 6-15 8-1 0-2 1-3 1-1 1-2 1-2 2-2 3-4 4-6 6l-1-1 9-9c1 0 2-2 3-2 0 0 3-2 4-2z"></path><defs><linearGradient id="x" x1="429.537" y1="593.005" x2="395.701" y2="590.397" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#d2d1d0"></stop></linearGradient></defs><path fill="url(#x)" d="M390 595c2-1 5-3 8-4 5-2 11-4 17-5 1 0 3 0 4 1h2 0c3 1 6 0 9 1h0c-1 1-2 3-3 4l-1 1h-1c-8 1-16 1-23 3-2 1-5 1-7 2-1 1-2 1-3 1h0c1-1 2-1 3-2h1c0-1 0-3-1-3s-2 1-4 1h-1z"></path><path d="M637 577l6 6c2 1 2 2 4 3h1 1c-2-1-4-3-5-4l1-1c4 4 7 7 12 9l8 4h0l1 1-1 1c-1 1-4 2-6 2-7-1-18-4-22-9h1 0l-2-2h0l1-1 3 3h0l4 2h0c-3-4-5-8-7-12v-2z" class="B"></path><path d="M403 321c2-1 4-1 6 0v1c-12 9-23 9-37 8-3 0-4 0-6-2 0-1-1-2 0-3 9 1 17 0 25-2h1 0c4 0 7-1 11-2z" class="F"></path><defs><linearGradient id="y" x1="345.085" y1="369.273" x2="333.488" y2="355.007" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#y)" d="M340 356c2 0 4 0 5 1h2 1l1 1c1 0 2 0 3 1h0c-5 4-10 8-11 14v2c-6-5-12-9-18-14 6 0 12-2 17-4v-1z"></path><path d="M442 379h0c0 2 0 2-1 3h0l-1 1c0 1 0 1 1 2v-1c1 1 1 1 2 1h1l1 1h0c1 0 1 0 1-1h1c-4 9-8 18-14 26l-2 2c-1-2-3-4-4-5h0c0-2-1-2-2-3v-1s1 0 1-1c5-1 8-4 8-9 1-2 1-4 1-5 2-4 5-7 7-10z" class="B"></path><defs><linearGradient id="z" x1="630.06" y1="807.761" x2="610.559" y2="781.895" xlink:href="#B"><stop offset="0" stop-color="#5f6061"></stop><stop offset="1" stop-color="#a8a6a5"></stop></linearGradient></defs><path fill="url(#z)" d="M621 789l5-21 1 1h4c-4 19-10 37-19 56v-2l1-1c-1-1-2-2-2-4h0l2-5c0-1 1-3 2-4l6-20z"></path><defs><linearGradient id="AA" x1="397.573" y1="811.955" x2="412.894" y2="774.127" xlink:href="#B"><stop offset="0" stop-color="#6e6c6f"></stop><stop offset="1" stop-color="#b5b5b3"></stop></linearGradient></defs><path fill="url(#AA)" d="M395 764c1 1 1 2 2 4 1-1 2-1 3-2v3l1 1c1 7 3 15 5 22 1 4 3 9 5 14 1 2 2 5 3 8v3l-1 2h0c1 2 1 2 1 4l-6-12c-5-13-9-26-12-39l-1-8z"></path><path d="M395 764c1 1 1 2 2 4 1-1 2-1 3-2v3 1l-1-1h-2v1l-1 2-1-8z" class="H"></path><defs><linearGradient id="AB" x1="604.409" y1="666.634" x2="614.776" y2="664.064" xlink:href="#B"><stop offset="0" stop-color="#4e4d4d"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#AB)" d="M607 655l8 13c0-4 0-8 2-11 3-5 8-9 13-10s11-2 16 1v1c-6-2-13-2-18 1-5 2-9 7-11 13 0 2-1 6 1 8l-1 1v1c-1 1-2 1-3 2l-3-1c-2-1-2 0-3 1-3-1-4-2-5-5 1-5 2-10 4-15z"></path><defs><linearGradient id="AC" x1="348.845" y1="509.337" x2="345.768" y2="537.19" xlink:href="#B"><stop offset="0" stop-color="#7c7c7c"></stop><stop offset="1" stop-color="#a5a4a5"></stop></linearGradient></defs><path fill="url(#AC)" d="M375 519l7-7 1 3h0c0 1-1 2-1 2-7 10-19 18-31 20-13 2-26 0-36-9 0-1 0-1-1-1h1l-2-3h0c1 1 2 2 4 2h0c1 0 2 1 2 1l7 4c3 2 8 4 12 4 14 1 25-5 35-14l2-2z"></path><defs><linearGradient id="AD" x1="339.505" y1="614.576" x2="326.084" y2="635.71" xlink:href="#B"><stop offset="0" stop-color="#555656"></stop><stop offset="1" stop-color="#8d8b8c"></stop></linearGradient></defs><path fill="url(#AD)" d="M324 612v-4c-1-1-1-1 0-2h3c4 10 8 21 14 30 0 3 0 6 1 9l-1 2c-6-6-12-15-14-23 0-3-2-9-3-12z"></path><defs><linearGradient id="AE" x1="636.882" y1="319.548" x2="634.106" y2="329.63" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#313132"></stop></linearGradient></defs><path fill="url(#AE)" d="M613 320c2 0 3 0 4 1l2-1v1c2 0 4-1 6 0h2l1 1c1 0 3 0 3 1 2 2 18 2 21 2 2 0 6-1 8 1h0c0 1-1 2-2 3s-4 1-5 1c-6 1-11 1-16 1-7-1-18-4-22-10-1 0 0 0-1-1h-1z"></path><path d="M616 360v1c0 3-2 4-4 7h0c-8-4-15-8-21-14-1-1-3-2-4-4 0 0 1-1 1-2v1 1h1l1-1v-1c0-1-1 0 0-1h0v-1h-1c0-1 1-3 1-5 2 2 6 5 8 8h1l-1 1c2 4 7 6 11 8 2 1 4 1 6 2h1z" class="C"></path><defs><linearGradient id="AF" x1="617.152" y1="517.919" x2="616.787" y2="551.124" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#5b5b5b"></stop></linearGradient></defs><path fill="url(#AF)" d="M604 553l1-14c2-10 8-15 16-21l7 1 2 3h0c-2 2-4 4-7 4-2 0-4 2-5 3-6 7-9 16-12 23l-2 1z"></path><path d="M598 414l-1-1v-1c-1 2-1 3-2 5 0 1-2 3-3 3 3 4 9 6 12 11l4 4v1l-2-1-1-1c-2-2-4-3-6-5-4-2-6-5-10-6 1 2 2 3 4 5 1 0 2 1 3 2l-6-3c-2 1-1 2-2 4 0-1 0-3-1-4v-2c-2-2-2-6-3-10-1-3-2-5-4-8 0-2-1-5 0-7h1l2 2h0c-1-1-2-1-2-2-1 1-1 0-1 2h0 3l2 3c1 1 2 2 4 3h1v-2l1 1 1 1h0c1 1 1 2 1 3l2 1h0l1-2 1 1 1 2v-1c1 0 0 0 1-1 0 1 0 2-1 3z" class="E"></path><defs><linearGradient id="AG" x1="439.647" y1="716.619" x2="424.345" y2="714.825" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#b0afb0"></stop></linearGradient></defs><path fill="url(#AG)" d="M435 684c1-1 2-1 3-2l1-1 1 1c-10 17-15 35-10 54 1 6 4 10 7 15-1 1-2 2-2 3h0c-8-10-11-23-12-36 2-11 5-25 12-34z"></path><defs><linearGradient id="AH" x1="580.141" y1="593.461" x2="614.188" y2="585.433" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#AH)" d="M599 584l7 1c-1 0-3 0-4 1 1 1 4 1 6 1l10 3h-2 0v4l-1-1-1 1c-10-1-20-1-30 1h-1c0-2-1-4-2-5-1-2-2-3-3-5h6l15-1z"></path><defs><linearGradient id="AI" x1="686.65" y1="616.603" x2="699.799" y2="632.618" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#989798"></stop></linearGradient></defs><path fill="url(#AI)" d="M703 598l1-1c0 2-2 5-1 7l2 2-4 12c-1 2-2 5-2 7-4 11-12 23-23 28l2-5c0-2 2-4 3-5 2-2 3-4 5-6 4-6 6-12 9-19s7-13 8-20z"></path><defs><linearGradient id="AJ" x1="336.827" y1="606.052" x2="357.158" y2="602.359" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#AJ)" d="M339 587c5 6 9 13 14 20 3 4 6 7 8 11l-1 1c-4 0-8-2-11-4-2-1-4-3-6-5l-4-4c-2-1-3-3-4-5h0 1v-3c0-1 0 0 1-1v-1h2c1 0 1 1 2 1l-3-3-1-1c1-2 2-4 2-6z"></path><defs><linearGradient id="AK" x1="635.18" y1="516.857" x2="617.823" y2="501.194" xlink:href="#B"><stop offset="0" stop-color="#535455"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#AK)" d="M600 521c6-5 13-10 20-14l12-6c5-3 12-5 17-8 2 1 5 2 6 3h1 1v-1h1 1 3c-22 5-41 17-59 30l-1-1h0c-2-1-2-2-2-3z"></path><path d="M794 322h0c-2-2-4-4-4-7-1-5 1-10 3-14 3-8 7-16 12-23 4-5 8-11 12-15 2-1 3-2 4-3l1 2c-1 2-3 4-4 6 0 0-1 2-2 3-3 1-6 6-8 9-4 7-7 14-9 22-2 5-5 10-5 16l1 2-1 2z" class="L"></path><defs><linearGradient id="AL" x1="392.054" y1="512.006" x2="398.673" y2="502.155" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#898989"></stop></linearGradient></defs><path fill="url(#AL)" d="M361 495l1-1h0c1 1 2 0 3 0 2-1 4-1 6-1h2l4 1 3 1 15 6c10 5 21 11 30 18 0 2 0 2-2 3l1 1s0 1 1 2c-21-13-39-27-64-30z"></path><defs><linearGradient id="AM" x1="691.614" y1="622.04" x2="701.176" y2="628.893" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#fefcfd"></stop></linearGradient></defs><path fill="url(#AM)" d="M717 582h0c0 3-2 6-3 9h3 0 1l-12 28c-7 15-15 31-31 38-5 2-10 3-14 3-2 1-4 1-6 1 7-3 14-3 21-8 11-5 19-17 23-28l18-43z"></path><defs><linearGradient id="AN" x1="424.722" y1="772.693" x2="406.11" y2="779.829" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#AN)" d="M398 752l1-2c1 1 2 1 2 2 1 3 0 6 2 9 0-1 0-1-1-2v-4c1 0 1 1 1 2s1 2 2 3c1 3 2 7 4 10 5 9 13 19 22 25l5 4c-1 1-1 2-1 3l-1 1c-10-6-19-15-26-24-3-3-4-6-7-9h0l-1-1v-3l-2-14z"></path><defs><linearGradient id="AO" x1="678.291" y1="366.99" x2="700.535" y2="364.051" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#AO)" d="M691 355l20-1c-3 2-5 4-8 6l-5 5-14 10-3-9c-2-2-4-5-6-5v-1h1 1c1-1 1-1 2-1s0 0 1-1h1c2-1 4-2 6-2 0-1 0-1 1-1h3z"></path><path d="M691 355l20-1c-3 2-5 4-8 6l-21-2 9-3z" class="J"></path><defs><linearGradient id="AP" x1="584.26" y1="717.777" x2="601.9" y2="715.293" xlink:href="#B"><stop offset="0" stop-color="#6b6b6b"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#AP)" d="M590 753c9-15 11-31 7-48-3-8-6-16-11-23-1-2-7-9-7-11 2 2 4 4 6 5 9 9 15 22 17 35 3 13 1 31-7 43l-1 1h-1v-1c-1 0-2 1-3 1v-2z"></path><path d="M643 511c10 12 21 23 38 24s33-13 44-24l10-9h1c-11 13-25 30-41 35h-1c-11 3-26 0-35-6-6-4-11-9-16-14-1-2-1-4 0-6z" class="O"></path><defs><linearGradient id="AQ" x1="352.774" y1="628.584" x2="360.66" y2="619.75" xlink:href="#B"><stop offset="0" stop-color="#2d2d2e"></stop><stop offset="1" stop-color="#5b5b5b"></stop></linearGradient></defs><path fill="url(#AQ)" d="M361 618l1 1c3 5 6 10 8 17h0c-1 0-3-1-4-2-6-1-13-3-17-8-3-3-5-7-8-10-4-6-9-11-13-17 0-1-1-2-1-3h1c3 5 6 10 10 14v-1c1-1 1-2 1-3l4 4c2 2 4 4 6 5 3 2 7 4 11 4l1-1z"></path><path d="M339 606l4 4c0 1 0 2 1 3 0 2 1 2 2 3h-2l-6-6v-1c1-1 1-2 1-3z" class="D"></path><path d="M343 610c2 2 4 4 6 5v4 1l-1-1h-1l-3-3h2c-1-1-2-1-2-3-1-1-1-2-1-3z" class="F"></path><defs><linearGradient id="AR" x1="405.941" y1="729.312" x2="394.146" y2="727.111" xlink:href="#B"><stop offset="0" stop-color="#cecccd"></stop><stop offset="1" stop-color="#fefeff"></stop></linearGradient></defs><path fill="url(#AR)" d="M402 693l1-1c0-1 1-2 1-3 2 3 1 7 1 10-5 17-9 35-7 53l2 14c-1 1-2 1-3 2-1-2-1-3-2-4-4-23-1-49 7-71z"></path><defs><linearGradient id="AS" x1="328.475" y1="615.297" x2="320.525" y2="619.859" xlink:href="#B"><stop offset="0" stop-color="#c4c3c4"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#AS)" d="M308 589c-1-3-4-10-3-12h1c1-2-1-4-1-5v-1l22 53c2 8 8 17 14 23l8 6 3 2c2 1 3 2 5 2 4 3 10 1 14 4-7 0-16-2-22-5-18-9-25-29-33-46-3-7-6-14-8-21z"></path><defs><linearGradient id="AT" x1="403.854" y1="516.272" x2="412.902" y2="552.163" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#AT)" d="M391 522h1v-2h0c0-2 1-2 1-4h0v1 4h1c1-1 2-2 3-4 3 1 8 3 11 5h1c1 1 2 1 3 2 0 1 0 1 1 2l2 1c4 6 5 11 6 18 0 2 0 5-1 7h-1l1 2h-1c-5-7-9-16-13-24h-1c-1 1-1 3-2 5v-2-1c1-1 1-2 1-3h1l1-1-1-1c-2 1-2 5-4 4-2 0-4-3-5-5 0 0-1-2-2-3l-1 1-2-2z"></path><path d="M394 523c1-1 3-1 4-1 4 1 7 1 9 4v2l-2-3-1 1c-1 2-1 4-3 5-2 0-4-3-5-5 0 0-1-2-2-3z" class="B"></path><path d="M396 526h3l1-1c2 0 2 0 4 1-1 2-1 4-3 5-2 0-4-3-5-5z" class="C"></path><path d="M404 526l1-1 2 3c5 8 9 15 12 24l1 2h-1c-5-7-9-16-13-24h-1c-1 1-1 3-2 5v-2-1c1-1 1-2 1-3h1l1-1-1-1c-2 1-2 5-4 4 2-1 2-3 3-5z"></path><path d="M368 544c2-1 5-2 6-3l1-1 2 1h1l1 3c-1 0-4 2-4 2-1 0-2 2-3 2l-9 9-1 1c-2 2-4 4-6 5l-2 1c-3 0-6 0-9-1s-13-3-14-6c3 0 6 0 9-1 5-1 13-3 18-7 2-2 4-4 7-5h3z" class="F"></path><path d="M362 558v-1-3h1c2-2 6-5 9-6l-9 9-1 1z" class="I"></path><path d="M368 544c2-1 5-2 6-3l1-1 2 1h1l1 3c-1 0-4 2-4 2l-7 2h0c-2 1-5 3-7 3h0 1v-2h-1c1-1 3-2 4-3 1 0 2-1 3-2h0z" class="C"></path><path d="M377 541h1l1 3c-1 0-4 2-4 2l-7 2h0l9-7z" class="K"></path><path d="M420 636v15l-2-1c-2-4-6-8-9-10-2-2-5-3-7-5-2-3-6-9-5-12 0-2 1-4 2-5 2-1 4-1 6-1h12l-2 3c-2 1-3 3-4 5v4h0c2 3 7 4 9 7z" class="E"></path><path d="M708 577l1-1h1l-7 12c-1 2-3 4-5 7l-12 16h-1l1-1v-1l-2 2c0-1 0-1 1-2s1-2 1-3c-3 2-6 5-9 7-4 4-7 5-12 6-1 0-1 0-1-1l12-17 10-3c1-1 1-1 2-1 1-2 4-4 4-6 0-1 3-3 5-4 3-3 7-7 10-11l1 1z" class="B"></path><path d="M681 607c0-1 1-2 2-3 3-2 7-7 10-7l-7 9c-3 2-6 5-9 7l4-5v-1z" class="Q"></path><path d="M708 577l1-1h1l-7 12c-1 2-3 4-5 7l-12 16h-1l1-1v-1l-2 2c0-1 0-1 1-2s1-2 1-3l7-9c3-1 5-7 7-9 2-4 5-7 8-11z"></path><defs><linearGradient id="AU" x1="677.74" y1="611.157" x2="672.352" y2="605.017" xlink:href="#B"><stop offset="0" stop-color="#6e6e6e"></stop><stop offset="1" stop-color="#888686"></stop></linearGradient></defs><path fill="url(#AU)" d="M676 601l10-3c-1 2-3 5-6 7-1 2-4 4-5 6 2-1 4-2 6-4v1l-4 5c-4 4-7 5-12 6-1 0-1 0-1-1l12-17z"></path><defs><linearGradient id="AV" x1="330.549" y1="513.06" x2="335.919" y2="485.106" xlink:href="#B"><stop offset="0" stop-color="#201f21"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#AV)" d="M295 506c2-2 5-3 8-5 5-3 11-6 17-7 16-5 33-7 49-3 4 1 8 2 11 4l-3-1-4-1h-2c-2 0-4 0-6 1-1 0-2 1-3 0h0l-1 1c-11-1-22 0-33 3-8 2-14 5-21 8-1 0-3 1-5 2-2 0-3 0-5-1h-1l-1-1z"></path><defs><linearGradient id="AW" x1="591.398" y1="540.753" x2="610.552" y2="555.376" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#AW)" d="M630 541c-1 0-2 0-3 1-3 1-6 3-9 5-10 8-17 17-23 28 1-4 1-8 0-12-2-6-6-12-5-18v-1c1-6 5-8 9-12l1 1c-2 9-1 17 2 26l2-6h0l2-1v1c6-4 7-14 13-18 1 0 2 0 3 1s0 2 0 4c1 0 0 0 1 1h1 2 4z"></path><path d="M685 514c-3 1-5 0-7 1-5 2-11 3-17 4-2 0-4-1-6 0h-1l-1-1c1 0 2-1 4-1h0v-1h-7v1l-1-2c5-4 13-9 20-10l1-1c6-1 14 0 20 2l1 6c7 1 15 2 21 5 1 0 2 0 2 1h0c-6-1-12-2-18-2-4-1-8-1-11-2z" class="E"></path><defs><linearGradient id="AX" x1="702.494" y1="521.781" x2="675.506" y2="506.719" xlink:href="#B"><stop offset="0" stop-color="#282729"></stop><stop offset="1" stop-color="#5f605f"></stop></linearGradient></defs><path fill="url(#AX)" d="M691 512c7 1 15 2 21 5 1 0 2 0 2 1h0c-6-1-12-2-18-2-4-1-8-1-11-2-4-1-11 0-15 0h-1c-4 1-7 2-10 3v-1c11-4 20-4 32-4z"></path><defs><linearGradient id="AY" x1="695.126" y1="516.047" x2="685.751" y2="483.274" xlink:href="#B"><stop offset="0" stop-color="#1f1f20"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#AY)" d="M649 493c19-6 41-6 60 1 6 2 11 5 17 8 1 1 2 2 4 3h0c0 1 0 1-1 2l-2-1v1h-5c-1-1-3-2-4-2l-12-5c-13-5-30-7-44-5h-3-1-1v1h-1-1c-1-1-4-2-6-3z"></path><defs><linearGradient id="AZ" x1="672.93" y1="631.064" x2="665.194" y2="618.635" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#AZ)" d="M698 595v3l-14 19c-4 5-9 11-14 14-5 2-10 3-15 6 3-1 11-3 13-2-2 3-8 5-11 6-1 1-3 1-4 2l-2-2h0l1-1c0-4 3-8 5-11 2-4 4-8 7-11 0 1 0 1 1 1 5-1 8-2 12-6 3-2 6-5 9-7 0 1 0 2-1 3s-1 1-1 2l2-2v1l-1 1h1l12-16z"></path><defs><linearGradient id="Aa" x1="693.619" y1="412.929" x2="688.545" y2="402.117" xlink:href="#B"><stop offset="0" stop-color="#434344"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#Aa)" d="M738 379c-2 3-7 3-9 5 0 0-1 2-1 3-23 11-42 27-64 40h-1l-1 1h0c-2 0-4-1-5-2 2-1 4-2 5-4v-1l30-20c15-9 29-18 46-22z"></path><defs><linearGradient id="Ab" x1="610.619" y1="731.459" x2="632.001" y2="726.688" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#fafafa"></stop></linearGradient></defs><path fill="url(#Ab)" d="M618 704c-4 3-7 8-11 10h-1c0-1 0-2 1-3 3-4 7-8 9-12 3-4 4-9 5-13 4 8 7 17 9 26 4 19 3 38 1 57h-4l-1-1c-1 0 0 0-1 1l-1-1c0-2 2-5 2-8 1-5 2-10 2-15 1-6 0-13-1-18-1-10-3-19-5-28l-4 5z"></path><defs><linearGradient id="Ac" x1="429.454" y1="536.406" x2="412.439" y2="553.605" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#Ac)" d="M406 530c4 8 8 17 13 24h1l-1-2h1c1-2 1-5 1-7 1 1 0 7 1 10v1c2-3 0-9 1-10l1 1v-3c0 3 0 8 1 12 2-10 1-17-1-26 3 3 8 7 10 11l1 1c2 4-4 17-4 21-1 3-1 6-1 10h0c-2-2-3-4-5-7-3-5-7-9-11-13-3-4-7-6-10-9-2-1-3-3-5-3v-1l-1-1h2c1-1 2-1 3-2s1-2 2-4c0-1 0-2 1-3z"></path><path d="M399 540c4 1 10 4 13 8l3 3-1 2c-3-4-7-6-10-9-2-1-3-3-5-3v-1z" class="I"></path><defs><linearGradient id="Ad" x1="331.785" y1="411.198" x2="336.501" y2="401.737" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#616161"></stop></linearGradient></defs><path fill="url(#Ad)" d="M292 380c5 1 10 3 15 5 14 7 27 15 40 23 6 5 13 9 19 14h1c1 2 4 4 6 5-3 1-4 2-7 2l-1-1h0c-7-4-13-9-20-13-14-10-29-20-46-28-1 0-3-1-5-2v-3c-1-1-2-1-2-2h0z"></path><path d="M294 382l3 1c1 0 3 1 4 2h-1l-2-1v2h2l-1 1c-1 0-3-1-5-2v-3z" class="I"></path><path d="M365 544v-1c-3 0-7 3-9 5-6 3-17 8-23 6h3 3l1-1h1c2-1 0 0 1-1 2 0 3 0 4-1 1 0 2-1 3-1 2 0 5-2 7-4h2v-1h-1c-1 0-1 1-2 1l-10 3c-1 1-2 1-3 1s-2 0-3 1h-1-6c7-1 13-3 20-5v-1c-2 1-5 1-7 2h0l4-2h1c3-1 5 0 7-1l1-1h0c2 0 4-1 5-2l2-2c1 0 3-2 4-3 0 0 0-1 1-1 3-2 4-4 6-7 4-3 7-6 10-9v-1 1c0 2-1 2-2 3 1 0 1 1 1 1v1l1 1c1 1 2 2 4 3 1-1 2-3 3-4l1-1c1 1 2 3 2 3 1 2 3 5 5 5 2 1 2-3 4-4l1 1-1 1h-1c0 1 0 2-1 3v1c0 2 0-1 0 2 1-2 1-4 2-5h1c-1 1-1 2-1 3-1 2-1 3-2 4s-2 1-3 2h-2c-6-2-14 0-20 2h-1l-2-1-1 1c-1 1-4 2-6 3h-3z" class="E"></path><defs><linearGradient id="Ae" x1="304.307" y1="534.682" x2="283.527" y2="550.887" xlink:href="#B"><stop offset="0" stop-color="#949494"></stop><stop offset="1" stop-color="#c0bebf"></stop></linearGradient></defs><path fill="url(#Ae)" d="M284 508c3 6 5 14 8 20 4 5 9 11 14 15l1 3v1l-2-1c-1-1-2-1-3-1l-8-7 7 21c1 3 4 8 4 12v1c0 1 2 3 1 5h-1c-1 2 2 9 3 12-2-3-3-6-4-9l-6-16-12-31c-2-6-5-11-6-17 0-1 0-2-1-3 0-1 0-1 1-2s3-2 4-3h0z"></path><defs><linearGradient id="Af" x1="628.239" y1="665.096" x2="630.367" y2="636.47" xlink:href="#B"><stop offset="0" stop-color="#212122"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#Af)" d="M619 639h1c1 0 1 0 2-1 6-2 14-3 20-1 3 2 7 4 8 7 1 2 1 4 1 6 1 3 0 8 1 10l-1 1c-1-1 0-7 0-9l-3-3-1 1-1-1v-1c-5-3-11-2-16-1s-10 5-13 10c-2 3-2 7-2 11l-8-13c0-1 1-3 1-4 2-3 7-10 11-12z"></path><defs><linearGradient id="Ag" x1="693.328" y1="559.025" x2="654.682" y2="555.748" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#Ag)" d="M632 539c3-1 7-1 11-1l1 1h2v1h1 2l1 1h0c2 0 3 2 4 2v-2c2 2 3 2 6 2v1h-1c-1-1-2 0-3 0l11 9h1c-1-2-3-3-4-4 5 2 10 5 16 6h1c4 1 9 2 13 2 0 1 0 1-1 1-2 2-4 2-6 3-6 2-11 3-16 2h-1c-7-1-10-4-14-9v2c-4-4-8-7-12-10 0 2-1 2-2 3l-6-4-6-4h-4c2-1 4-2 6-2z"></path><path d="M656 545c1 2 2 3 3 5h-1l-5-3 3-2z" class="G"></path><defs><linearGradient id="Ah" x1="653.86" y1="547.098" x2="630.416" y2="537.712" xlink:href="#B"><stop offset="0" stop-color="#767675"></stop><stop offset="1" stop-color="#a8a6a8"></stop></linearGradient></defs><path fill="url(#Ah)" d="M632 539c4 0 8 0 12 1h1c3 1 8 3 11 5l-3 2c-5-3-10-5-16-5v1c0 1 0 1-1 2l-6-4h-4c2-1 4-2 6-2z"></path><path d="M637 543v-1c6 0 11 2 16 5l5 3-1 1c-1 1-2 0-3 2l2 1v2c-4-4-8-7-12-10 0 2-1 2-2 3l-6-4c1-1 1-1 1-2z" class="E"></path><path d="M637 543l7 3c0 2-1 2-2 3l-6-4c1-1 1-1 1-2z" class="G"></path><defs><linearGradient id="Ai" x1="714.346" y1="527.6" x2="743.562" y2="573.139" xlink:href="#B"><stop offset="0" stop-color="#8f8d8c"></stop><stop offset="1" stop-color="#b8b9b9"></stop></linearGradient></defs><path fill="url(#Ai)" d="M741 509c1 2 4 4 6 5l-29 77h-1 0-3c1-3 3-6 3-9h0c-1-3 14-41 16-46l-7 6-1 1h-1l-1 1h-1l-2-1 2-2v-1c3-4 8-7 11-11 4-6 6-14 8-20z"></path><defs><linearGradient id="Aj" x1="583.346" y1="638.815" x2="601.212" y2="638.331" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#373737"></stop></linearGradient></defs><path fill="url(#Aj)" d="M585 612c5 2 11 4 14 10l1 2c1 1 1 2 2 3h1l2 2c0 1 1 2 1 3-8 8-13 22-13 33l-1 1v1l-3-3v4c-3-4-4-10-5-14-1-6-2-14 2-19 2-3 5-6 7-9v-3c-1-3-5-6-7-8h-1c-2-2-4-2-7-2h0c3 0 4 0 7-1z"></path><path d="M600 624c1 1 1 2 2 3h1l2 2c0 1 1 2 1 3-8 8-13 22-13 33l-1 1v1l-3-3c1-5 1-9 2-14 1 0 1-1 1-2v-1c2-3 4-7 6-10l1-1c0-1 1-2 2-3 1 0 2 1 4-1h-1c-1-1-2-2-3-2-1-2-1-4-1-6z" class="C"></path><defs><linearGradient id="Ak" x1="399.921" y1="667.022" x2="391.692" y2="635.493" xlink:href="#B"><stop offset="0" stop-color="#1d1c1e"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#Ak)" d="M382 637c4-3 12-2 17-1 7 1 14 9 17 14l1 1c1 1 1 2 2 3l-8 13h0c0-4-1-7-4-11 0 1-1 1-1 2-2-3-5-5-8-7-5-4-10-4-16-2h-1c-1 1-2 1-3 1-1 1-2 3-3 4v4c-1-2-1-6 0-9 0-4 2-8 5-10l2-2h0z"></path><path d="M382 649c2-3 6-3 9-3 5 0 10 3 14 7l2 3c0 1-1 1-1 2-2-3-5-5-8-7-5-4-10-4-16-2z" class="J"></path><defs><linearGradient id="Al" x1="425.153" y1="721.2" x2="398.216" y2="748.756" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#Al)" d="M405 699l11 12c1 2 2 3 4 4l3 3h0c1 13 4 26 12 36l2 4v3c-6-8-11-16-15-25-2-3-3-6-5-9-1-4-2-6-5-9-3 3-6 5-7 9h0c-1 5-1 9-1 14l-1 4c-1 5 1 10 2 15-1-1-2-2-2-3s0-2-1-2v4c1 1 1 1 1 2-2-3-1-6-2-9 0-1-1-1-2-2l-1 2c-2-18 2-36 7-53z"></path><defs><linearGradient id="Am" x1="665.825" y1="569.254" x2="663.561" y2="591.658" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#424142"></stop></linearGradient></defs><path fill="url(#Am)" d="M644 582c-3-3-6-8-6-12v-2h-1l1-1c6 2 10 7 16 8 12 2 24 0 36-3l1-1c1 0 5 0 6 1h0l-1 1c-3 4-7 6-10 10-2 2-3 4-4 6-1 1-2 1-4 2s-4 1-6 1l-3-2c1-2 3-3 3-4-2-1-5-1-7 0-1 1-2 2-2 4l2 4h0l-8-4c-5-2-8-5-12-9l-1 1z"></path><path d="M645 581h0l10 7c3-3 5-5 10-6 1 0 1 0 2 1-2 1-3 2-5 2-1 0-2 1-2 1-1 1-2 3-3 4-5-2-8-5-12-9z" class="C"></path><path d="M678 591v-1c0-1-1-2-2-2-1-1-2-1-3-2h0c1-1 2-2 3-2 5-2 11-6 16-9 1-1 2-2 4-2-3 4-7 6-10 10-2 2-3 4-4 6-1 1-2 1-4 2z" class="B"></path><path d="M208 381c-3-2-6-4-10-5s-7 1-10 3l-17-32 14-36 3 3c3 1 6 1 8 0 5-1 10-5 13-10 4-7 4-17 1-25-3-10-9-18-19-23-12-6-26-6-38-1-3 1-6 4-9 5l1-1c8-8 20-11 31-10s21 6 29 14c4 5 7 11 9 17 2 10 2 20-5 29-3 4-9 9-15 9-2 1-5 0-7-1l-8 19c-2 3-3 7-4 10 0 1 1 2 1 3 2 5 4 9 7 12h0l6 12c1 0 1 1 2 0 2-2 5-1 8-1 3 1 6 2 8 4v1c1 2 4 4 5 6h-1l-1-1c0-1-1-1-2-2v1zm614-119l4-4c8-7 20-10 31-9 8 0 20 4 26 11v1c-11-8-22-11-37-9-10 2-19 8-25 17-5 8-8 19-5 29 1 5 5 11 10 14 3 2 6 3 10 2 2 0 4-1 5-2l15 35c-6 10-13 22-17 33l-1-1c-3-2-6-4-10-4-4 1-8 4-11 6h-1l-1 1c-1 1 0 1-1 1 0-2 3-3 3-6l2-2 5-2c4-2 8-2 13 0h0l15-27c-2-6-5-13-8-19-1-3-2-7-4-9-1-1-7 0-8 0-8-1-12-7-17-13v-1c-5-12-4-22 1-33 1-1 2-3 2-3 1-2 3-4 4-6z" class="M"></path><path d="M436 613l4-1v1c-2 4-8 7-6 12 1 4 4 8 6 12 4 6 2 21-1 28-1 3-3 4-6 7l-1-11c-1-10-5-18-12-25-2-3-7-4-9-7h0v-4c1-2 2-4 4-5h7c1 1 2 1 3 3h1c1-3 2-5 4-7l6-3z" class="F"></path><path d="M426 630l5 2 3 2c2 2 4 4 5 7l-1 1h-2-1l-1-3c-2-4-5-6-8-9z"></path><path d="M424 632c3 0 6 4 7 6s2 3 4 5c2 3 2 11 1 16v2c-1-1-1-1-1-2v-1c1-10-4-19-11-26z"></path><path d="M411 625c1-2 2-4 4-5h7c1 1 2 1 3 3h1c-1 2-2 5-3 7 0 1 0 1 1 2 7 7 12 16 11 26v1l-1 5c-1-1-1-2-2-3-1-10-5-18-12-25-2-3-7-4-9-7h0v-4z" class="I"></path><path d="M411 625c1-2 2-4 4-5h7c1 1 2 1 3 3h1c-1 2-2 5-3 7h0c-4-2-8-4-12-5z" class="N"></path><path d="M676 312c2-3 6-6 8-9h1v1h-1c0 1-1 1-1 2-2 2-4 3-5 5l-1 1h2c1-1 2-1 3-1h2l1-1c1 0 1 0 2-1 1 0 2 0 3-1v-1l2-2h1l1-1h0c-3 3-5 6-8 10 0 1-2 5-3 5h0l-3 3c0 2 0 2 1 4h2c2 0 4-2 6-3 5-2 11-4 16-7h3c-5 4-10 8-15 13-8 7-16 16-26 20-6 2-13 2-19 2-5 0-10 0-14-3-2-2-5-4-7-7v-2l1-1h0l1 1c4 4 9 5 14 6 2 0 11-2 13-1h1c1 0 4 0 5-1 7-5 10-14 11-22 1-2 0-4 1-6 1-1 2-2 2-3zm-326-8a30.44 30.44 0 0 1 8-8c-1 3-3 6-4 10v2c1 1 3 2 4 3-1 3-1 6-1 9 1 9 4 17 12 23 1 0 5 1 7 1 5 1 11 1 15-2l3-3c2-1 3-1 5-1l-1 1h-1c-1 1-2 3-2 4-1 1-1 1-1 2-2 3-6 5-10 6h-1c-5 1-11 1-16 0-14-2-24-12-34-21-3-3-7-6-10-10l1-1c6 3 11 4 16 7v-2l3 2h1c0-3 0-5-1-7-1 0 0 0-1-1-3-6-5-10-10-14h0l1 1c2 0 4 3 6 5l1 2h1 3c0 1 1 1 3 1h1v-2c1-1 1-2 1-3h0l-1 1c-2 0-3-1-4-2v-1c-2 0-2-1-3-2-2-1-1 0-2-1 0-1-1-1-1-2 3 3 6 5 9 8l3-5z" class="B"></path><path d="M343 319l2 1h2c0 2 1 4-1 5 0 1-1 1-1 2-2 0-3 0-5-1v-2l3 2h1c0-3 0-5-1-7z" class="G"></path><defs><linearGradient id="An" x1="356.077" y1="305.782" x2="347.923" y2="308.218" xlink:href="#B"><stop offset="0" stop-color="#161717"></stop><stop offset="1" stop-color="#373636"></stop></linearGradient></defs><path fill="url(#An)" d="M350 304a30.44 30.44 0 0 1 8-8c-1 3-3 6-4 10v2l-1 3c-1-1-1 0-1-1v-1c-1 3-1 5-3 7 0-2 0-4 1-6v-5-1z"></path><defs><linearGradient id="Ao" x1="363.337" y1="322.472" x2="354.386" y2="331.302" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#b6b5b6"></stop></linearGradient></defs><path fill="url(#Ao)" d="M354 308c1 1 3 2 4 3-1 3-1 6-1 9 1 9 4 17 12 23h-2c-2 0-4-1-5-3-8-8-10-17-9-28v-1l1-3z"></path><path d="M354 308c1 1 3 2 4 3-1 3-1 6-1 9 0-2 0-4-1-6-1-1-2-2-3-2v-1l1-3z" class="G"></path><path d="M811 306v-2c-1-1-1-2-1-3v-2c-1-1-1-3-1-4s0-6 1-7v-5h-1-1v-1h0c1-2 3-5 4-7 1 0 1-1 2-2h0c0 1-1 2-1 3s-1 2-1 3v3c-1 1-1 2-1 3s0 9 1 10v3c1 2 1 3 1 5v1h1c0 1 0 0 1 1h0c5 6 9 12 17 13 1 0 7-1 8 0 2 2 3 6 4 9 3 6 6 13 8 19l-15 27h0c-5-2-9-2-13 0l-5 2c0-1 0 0-1 0-2 0-6 3-7 5l-3 3v1l-1-1 1-1c1-3 6-9 9-10 1-1 2-1 3-2s2-2 4-2c0 1-2 2-2 3h1 0l1-1 2-2h2 1 1l2-2c1-2 3-3 4-4 0 1 0 3-1 5v2c0-1 0-1 1-2h0c1-1 0-1 1-2v-2c1-2 1-2 1-3-4 0-5 5-8 7-1 0-3 0-4 1-1-1-2-1-3-2v-1h-1-1l-4 3c0-2-1-2-2-3h-3c-1 0-2 1-3 0 2-4 3-9 5-14 0-1 1-2 2-4 0-1 1-3 1-5l-1-2-1 1-1 2c0-1 0-2-1-2v-1c1-4-2-7-4-9-1-1-1-2-2-3h0c0-2-1-2-1-3 1 1 0 1 2 1 0-1-2-3-3-3-1-1-2-3-2-4h0c-1-2-2-5-2-7 2 0 2 1 3 2 2 2 3 4 5 6 2 0 3 0 5 1 6 2 11 4 17 6 1-2 3-3 3-5h-2c-2 2-8 1-11 0 0 0-1 0-1-1v-1c-4-3-8-10-9-14z" class="E"></path><path d="M813 358c3 2 7 3 9 6h1v1h0-1-1c-1-1-3-2-5-3-1 0-1-1-2-1l-1-3z" class="B"></path><path d="M832 337h2c2 0 5-1 7-2 1 1 1 1 0 2v8c-2-2-5-4-7-6l-2-2z"></path><defs><linearGradient id="Ap" x1="813.527" y1="356.824" x2="811.149" y2="364.363" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#Ap)" d="M809 365c2-4 3-9 5-14 0 2 0 3-1 5h0c1 1 0 2 0 2l1 3c1 0 1 1 2 1 2 1 4 2 5 3l-4 3c0-2-1-2-2-3h-3c-1 0-2 1-3 0z"></path><defs><linearGradient id="Aq" x1="826.474" y1="336.668" x2="818.256" y2="332.39" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#Aq)" d="M817 330l-1-2c2-1 3 1 5 1 4 2 7 6 11 8l2 2h-1l-6-4c-1 3-2 5 0 9-2-2-2-3-3-5-2-3-5-7-7-9z"></path><defs><linearGradient id="Ar" x1="828.041" y1="346.156" x2="818.839" y2="343.455" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#Ar)" d="M801 312c2 0 2 1 3 2 2 2 3 4 5 6 4 3 8 5 12 9-2 0-3-2-5-1l1 2c2 2 5 6 7 9 1 2 1 3 3 5 1 1 2 2 2 4v1c-1 2-1 3-2 3s-3 0-4-1c-2-1-3-3-4-5h0c-1 0-1 1-2 2l-1-1c0-1 1-3 1-5l-1-2-1 1-1 2c0-1 0-2-1-2v-1c1-4-2-7-4-9-1-1-1-2-2-3h0c0-2-1-2-1-3 1 1 0 1 2 1 0-1-2-3-3-3-1-1-2-3-2-4h0c-1-2-2-5-2-7z"></path><path d="M803 319l1-1c2 2 4 4 7 6h0c0 3 6 7 4 10l-7-8c0-1-2-3-3-3-1-1-2-3-2-4z" class="G"></path><path d="M801 312c2 0 2 1 3 2 2 2 3 4 5 6 4 3 8 5 12 9-2 0-3-2-5-1l1 2c-1-1-1-1-2-1-1-2-2-4-4-5h0c-3-2-5-4-7-6l-1 1h0c-1-2-2-5-2-7z" class="K"></path><path d="M809 331c-1-1-1-2-2-3h0c0-2-1-2-1-3 1 1 0 1 2 1l7 8c2 2 4 5 5 8 1 2 0 5 1 7 1 1 2 1 3 1 2 0 3 0 5-1-1 2-1 3-2 3s-3 0-4-1c-2-1-3-3-4-5h0c-1 0-1 1-2 2l-1-1c0-1 1-3 1-5l-1-2-1 1-1 2c0-1 0-2-1-2v-1c1-4-2-7-4-9zM305 571c0-4-3-9-4-12l-7-21 8 7c1 0 2 0 3 1l2 1v-1l-1-3c12 11 23 19 39 24 0 1 0 2 1 2 1 1 1 1 2 1v1c8 1 17 1 25-2 6-2 14-5 17-12 2 4 2 9 1 13l1 3c-1 2-1 4-2 5l-1 1c-1 1-1 1-1 2h-1c-1 0-2 1-2 2-4 4-7 7-13 7l-1-1-3 2c-2 1-7 4-8 4-1-4 2-5 1-8-2-2-5-2-7-3l-1 1v1l-2-1-2 2h-1v1s1 1 1 2h0c-3-2-5-5-8-6-2-1-3-2-4-3l-1 1-2-3-1 1 6 7c0 2-1 4-2 6l1 1 3 3c-1 0-1-1-2-1h-2v1c-1 1-1 0-1 1v3h-1 0c-1 0-2-2-3-3l-4-6c-2-3-6-9-9-11l8 25h-3c-1 1-1 1 0 2v4c1 3 3 9 3 12l-22-53z"></path><defs><linearGradient id="As" x1="320.575" y1="590.138" x2="336.192" y2="589.294" xlink:href="#B"><stop offset="0" stop-color="#1a1c1b"></stop><stop offset="1" stop-color="#434142"></stop></linearGradient></defs><path fill="url(#As)" d="M319 581c-1 0-1-1-1-1l1-2v-2c5 5 9 11 14 15 2 2 3 3 5 3l3 3c-1 0-1-1-2-1h-2v1c-1 1-1 0-1 1v3h-1 0c-1 0-2-2-3-3l-4-6c-2-3-6-9-9-11z"></path><path d="M337 593c-4-6-10-12-15-17-1-1-4-4-4-6l1 2h2l1-1c2 0 3 1 5 3l6 6h0l6 7c0 2-1 4-2 6z" class="P"></path><path d="M306 543c12 11 23 19 39 24 0 1 0 2 1 2 1 1 1 1 2 1v1h0c-17-2-34-15-46-26 1 0 2 0 3 1l2 1v-1l-1-3z" class="G"></path><defs><linearGradient id="At" x1="320.56" y1="570.605" x2="316.403" y2="604.696" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#At)" d="M324 612c-4-9-6-20-9-30-1-5-3-10-5-15 3 2 7 6 9 9v2l-1 2s0 1 1 1l8 25h-3c-1 1-1 1 0 2v4z"></path><defs><linearGradient id="Au" x1="355.944" y1="565.371" x2="356.967" y2="586.057" xlink:href="#B"><stop offset="0" stop-color="#090808"></stop><stop offset="1" stop-color="#272727"></stop></linearGradient></defs><path fill="url(#Au)" d="M386 567c1-1 2-2 4-2h0l1 1h0v4l1 3c-1 2-1 4-2 5l-1 1c-1 1-1 1-1 2h-1c-1 0-2 1-2 2-4 4-7 7-13 7l-1-1-3 2c-2 1-7 4-8 4-1-4 2-5 1-8-2-2-5-2-7-3l-1 1v1l-2-1-2 2h-1v1s1 1 1 2h0c-3-2-5-5-8-6-2-1-3-2-4-3l-1 1-2-3-1 1h0l-6-6c-2-2-3-3-5-3l-4-2c0-1-2-2-2-3 2 0 5-1 7 0 1 0 5 3 7 4 5 3 10 4 17 5 4 1 8 1 12 1l6-1c7 0 15-4 21-8z"></path><defs><linearGradient id="Av" x1="356.367" y1="577.857" x2="358.04" y2="593.759" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#4e4d4e"></stop></linearGradient></defs><path fill="url(#Av)" d="M354 584l-1-1c-3 0-7-2-8-4l1-1c1 1 3 1 5 1 4 0 14 0 17 3l1 2c0 2-1 4-2 6h0l1 1c-2 1-7 4-8 4-1-4 2-5 1-8-2-2-5-2-7-3z"></path><path d="M386 567c1-1 2-2 4-2h0l1 1h0v4l1 3c-1 2-1 4-2 5l-1 1c-1 1-1 1-1 2h-1c-1 0-2 1-2 2-4 4-7 7-13 7l-1-1-3 2-1-1h0c1-2 2-4 2-6 1-2 4-2 6-3l1-1 2 1c2-1 4-3 6-3h0c1-1 5-6 5-8h0c0-2 1-3 0-4-1 0-1 1-2 1h-1z" class="D"></path><path d="M378 581c2-1 4-3 6-3-4 5-7 8-13 11l-3 2-1-1h0c1-2 2-4 2-6 1-2 4-2 6-3l1-1 2 1z" class="C"></path><path d="M369 584c1-2 4-2 6-3l1-1 2 1-11 9h0c1-2 2-4 2-6z" class="D"></path><defs><linearGradient id="Aw" x1="242.451" y1="299.648" x2="187.535" y2="364.349" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#222"></stop></linearGradient></defs><path fill="url(#Aw)" d="M214 280v-4h0 0c1 0 1 1 2 2h1c6 8 10 20 12 30 1 4 3 7 3 11h0c-4 7-8 13-10 21l-1-1v1 4c-1 1-1 5-1 6l1 4v1c1 2 1 4 2 6v4c1 1 1 5 1 6-1 1-1 2-1 4-1 1 0 5 0 7h-1v-1h-1c0-2-1-2-2-3 0-2-3-3-3-4h3l2-1v-2h1v-1-3c-1-1 0-2 0-4l-1-1v-1l-1-2c1-2 1-3 0-4v-1-2-1-1c-1-2-1-5 0-6v-3-1-1 1c-2 4-1 10-1 15-2-3-4-8-5-12h-1 0l-1-1v-1l-2-2h-1l1 1-1 1c1 4 2 8 3 11l4 10c-3 1-8 1-10 4h-2c-1 1-1 1-2 1 0 0-3 1-4 1s-3-5-5-6c-1 0-9-1-10-1h0c-3-3-5-7-7-12 0-1-1-2-1-3 1-3 2-7 4-10l8-19c2 1 5 2 7 1 6 0 12-5 15-9 7-9 7-19 5-29z"></path><path d="M198 349h4l1 1c1 1 1 0 2 2h-2c-1 0-3 1-5 0v-3z"></path><path d="M202 327h0c-1 2-2 3-3 4l-1 1c2 1 5 1 7 1 1 0 1 0 1 1-3 4-5 8-6 14l2 1h-4 0s1 0 1-1v-1l1-1c0-2 0-4 1-5v-1-1c1-1 1-2 2-3l2-2c-3-1-5-1-8-2v-2c2-2 3-2 5-3z" class="B"></path><path d="M182 332c3 1 6 4 9 5l2-1c-1 4-6 8-9 10-2 1-3 2-5 2h-2l1-1h2 1c1-1 2-1 2-2-1 0-1 0-2-1v-2h0v2c4 0 6-3 8-5l-7-5c0-1 1-1 0-2z"></path><path d="M204 366h-1 1v-1c2-2 3-5 5-7l3-3v-1-1c-1 0-1-1-2-1v-1c1 1 2 1 2 1l4 10c-3 1-8 1-10 4h-2z" class="I"></path><defs><linearGradient id="Ax" x1="200.987" y1="325.107" x2="214.013" y2="328.393" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#Ax)" d="M202 327c5-3 11-4 15-7 3-2 5-4 7-6-1 2-2 4-4 5v1l-1 1c-1 0-3 2-4 3-2 3-4 5-7 8 0 0-1 2-2 2 0-1 0-1-1-1-2 0-5 0-7-1l1-1c1-1 2-2 3-4h0z"></path><path d="M214 343l-1-3v-1c0-4 10-13 13-17h0c-2 5-5 11-6 16v1 1c-2 4-1 10-1 15-2-3-4-8-5-12z" class="B"></path><path d="M224 314l1-4h0c1 6-1 10-5 15l-9 9c-2 2-4 4-5 6s-1 4-2 7c0 0 1 2 0 2 0 1 0 1-1 1l-1-1-2-1c1-6 3-10 6-14 1 0 2-2 2-2 3-3 5-5 7-8 1-1 3-3 4-3l1-1v-1c2-1 3-3 4-5z" class="P"></path><defs><linearGradient id="Ay" x1="201.389" y1="303.974" x2="218.847" y2="308.474" xlink:href="#B"><stop offset="0" stop-color="#19191a"></stop><stop offset="1" stop-color="#383838"></stop></linearGradient></defs><path fill="url(#Ay)" d="M220 287h0c1 3 2 6 2 9 0 7-3 16-8 21-6 6-14 5-21 5h-2v-1h1 1 3c6-1 12-4 15-8 7-8 9-16 9-26z"></path><path d="M649 357h1c7-1 17 0 24 6 6 6 6 14 7 22-4-5-8-8-13-11-7-4-13-5-21-3-7 2-12 8-16 14-5 10-6 23-3 33 4 11 15 21 25 26 12 6 25 8 38 3 23-8 42-27 52-48 4-8 9-20 8-29 0-3-2-7-3-10h40c-2 3-4 7-5 12v10l1 22 11 113c2 16 5 33 12 49 3 5 6 11 9 16-6-5-12-11-17-16-11-12-19-24-25-39-3-9-6-19-8-29l-5-20c-1-9-2-19-4-28-1-3-3-6-5-8-1-2-2-2-4-2s-4 0-6 2c-7 8-6 26-6 36h0c-15-11-33-5-50-2-17 2-34-5-48-16-16-12-26-28-29-48-1-14 3-28 11-39 8-10 17-15 29-16zm-280 0c10-1 20 1 29 7 10 7 18 20 20 32 3 17-2 34-11 48-10 14-28 28-46 31-8 1-15 1-22 0-11-2-23-5-34-3-6 1-11 3-15 6 0-11 0-27-6-35-1-2-3-3-5-3-1-1-3 0-4 1-8 4-9 31-11 40s-3 18-6 28c-4 13-9 26-16 39-8 14-20 24-32 34 12-18 16-37 20-59 7-41 11-84 13-126 0-8 1-16 0-24 0-5-3-9-5-13h42c-2 4-3 8-3 13 0 20 14 42 28 56 11 10 30 21 46 21 16-1 30-7 41-18 7-8 10-19 9-29 0-10-5-21-12-27-6-5-13-7-20-6-11 1-18 8-24 16 0-7 0-15 5-20 4-6 12-8 19-9zm95 30c0-5 0-11-1-17-4-21-15-41-31-56-8-7-20-13-30-15-2-1-11-2-11-3h245c-6 2-12 2-18 4-18 5-32 17-42 33-8 14-11 30-13 46-2 18-1 36-1 54l1 88v152 55c0 28 0 58 8 86 6 25 18 51 42 65 13 8 28 11 44 11-11 4-22 6-34 6s-23-1-35-2-27-2-37 3c-14 8-24 25-30 39l-8 20c-6-19-14-36-29-50-4-4-8-8-13-10-7-3-14-4-21-4-11 0-20 1-31 2-6 1-14 2-20 2-3 0-6-1-8-2-7-1-14-2-20-4 23-3 50-10 65-30 12-16 19-35 23-56 4-24 4-49 4-73v-65-133l1-110v-36z" class="M"></path><path d="M425 304l-7-4h14-1c-2 1-6 0-7 2l1 2z" class="O"></path><path d="M525 356c1 0 0 0 1 1v1c-2 5-5 8-8 12h-1v-7c1-3 5-5 8-7z"></path><path d="M509 497l3 3h-1l1 1v41c-2-6-2-13-2-19 0-9-1-17-1-26z" class="P"></path><defs><linearGradient id="Az" x1="515.334" y1="349.787" x2="500.347" y2="355.933" xlink:href="#B"><stop offset="0" stop-color="#aaa9a7"></stop><stop offset="1" stop-color="#e0e1e4"></stop></linearGradient></defs><path fill="url(#Az)" d="M508 336c2 1 3 1 4 2l-2 39c-2-2-1-9-1-12-1 2-2 4-4 5h-1v-1c1-1 1-3 1-4 1-1 2-3 3-5v-9c0-5 1-10 0-15z"></path><path d="M527 440c1 8 4 16 0 23-2 3-5 5-7 7-4 4-9 7-12 12-4 5-7 11-10 16 1-4 2-7 3-11 3-7 9-13 13-19 6-8 11-19 13-28zm-13-46c2 2 6 10 7 13 6 14 2 29-3 43l-4 6c1-3 2-6 2-9 0-9-1-19-1-28 0-8 1-17-1-25zm7-74l28 1-17 18c-4 3-6 7-10 11-1 1-5 4-6 6-1-5-1-13 0-18 4-4 10-8 16-11 2-1 5-1 7-3-11 2-23 2-34 1l-20-2c6 3 12 5 18 9 2 1 3 3 5 4 1 5 0 10 0 15v9c-1 2-2 4-3 5 0 1 0 3-1 4l-9-13c2-2 3-4 5-5 1-1 1-1 1-2l-26-27c-1-1-1 0-1-1l6-1h41z"></path><defs><linearGradient id="BA" x1="407.233" y1="406.196" x2="498.721" y2="420.777" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#BA)" d="M432 300h50c6 0 13 1 19 0 1-1 2-1 3 0h1 1 0-1l-1 1h-1-1-1v1c1 1 1 3 1 5h0c0 2 0 3-1 4 0 1 1 2 0 3 0 2 0 2-1 4h0c0 1-1 1-1 2h5 3c3 0 10-1 14 0h-41l-6 1c0 1 0 0 1 1l26 27c0 1 0 1-1 2-2 1-3 3-5 5l-5-5c-5-5-12-13-19-16 2 3 4 9 7 12v-1-1h1v1c1 3 3 8 4 12 2 9 4 18 5 27 1 15 1 30 1 45v54 182 54 31l-1 1v-2-24-55-168-42-10-5 28c-1 1-1 2-1 3-1 2 0 18 0 21-1 1-1 3-1 4-1 1 0 3 0 5h-1v3c-1 3-1 5-2 6v2c-1 0-1 1-1 2h1v2l-3 2c0 1-1 2-1 3v1c-1 2-3 4-4 6 0 1-1 2-2 3-1 0-2 0-2-1-1-1-1-1-1-2-2 2-1 7-1 10-1 2 0 27 0 31 1 2 0 7 0 10-1 1 0 2 0 4h-1v-16l-1-1v-9-4c1-1 1-3 1-5v-12-83-24c0-3 1-8 0-10s-1-2-1-4h0c0-2-1-2-2-3 0 2 0 3-1 4v1-36c1 1 1 2 1 3v1c1 0 2-1 2-1v-3c2-3 0-13 0-16-5-27-19-51-42-67l-1-2c1-2 5-1 7-2h1z"></path><path d="M473 325l-11-10c5 0 11 1 17 2-2 0-4 0-5 1-1 0-1 0 0 1 0 1 1 1 1 1h1 4 0l-6 1c0 1 0 0 1 1-1 1-1 2-2 3z" class="M"></path><defs><linearGradient id="BB" x1="483.598" y1="321.455" x2="489.902" y2="351.045" xlink:href="#B"><stop offset="0" stop-color="#bfc4c4"></stop><stop offset="1" stop-color="#f6eff2"></stop></linearGradient></defs><path fill="url(#BB)" d="M475 322l26 27c0 1 0 1-1 2-3-1-4-3-6-5-4-4-8-8-11-12-3-3-7-6-10-9 1-1 1-2 2-3z"></path><path d="M510 770c2 1 2-1 5-1 0 1 1 3 1 5l-3 1c-1 0-2 1-2 2h0c-2 1-3 2-3 4-1 2-1 7 1 10h1 0v-1c-1-3 0-7 0-10 1-1 1-2 3-2 1 2 1 9 1 12h0 1l1 1c0 1-1 3 0 4h0c-1 1-1 2-1 2l-1 2c0 3 4 7 6 8 6-7 10-15 12-25 1 0 2-1 3-1s1-1 2-2h1c2 22 6 44 15 64 3 9 8 18 14 26-6-1-12-2-18-1-4 2-8 5-12 7-3 3-7 5-10 8-6 5-11 12-15 19-3-4-6-8-10-12l-9-8c-10-8-19-15-32-15 4-7 9-14 13-23 9-21 12-43 14-66 2 3 2 9 3 12 1 2 2 3 3 4 2 0 2 0 3-1l3 10c1-2 4-4 6-6-2-2-3-4-4-7-1-5-2-11 1-15 1-3 3-5 6-6l1 1z"></path><path d="M524 875c1 1 3 2 3 4v1l-1 1-3-3c0-1 0-2 1-3z" class="O"></path><path d="M533 804v1c-1 4-2 12-5 14-2-5 2-11 5-15z" class="E"></path><path d="M515 877l2-1c0 1 0 1-1 2l2 2c-3 2-10 7-14 7-2 0-4-2-5-3v-1l4 3c4-2 10-5 12-8v-1z" class="G"></path><path d="M490 853s1 0 1 1v2c-5 5-12 9-20 10h-1c2-2 4-2 6-3 4-1 7-4 10-6 2-1 3-2 4-4z" class="B"></path><defs><linearGradient id="BC" x1="496.516" y1="803.438" x2="497.049" y2="795.035" xlink:href="#B"><stop offset="0" stop-color="#262627"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#BC)" d="M491 790c1 2 2 3 3 4 2 0 2 0 3-1l3 10-2 3c-3-5-5-10-7-16z"></path><path d="M496 848c2 8 5 15 11 22 1-1 2-1 3-2l-1-1c2 0 3 0 4-1h0v4l-1 2c1 0 3 0 4-1-1 2-3 1-5 2 0 1-1 2-1 2-1 1-2 1-3 1 1 0 2-1 2-2-8-6-13-13-15-22 1 0 1 0 2-1v-3z" class="J"></path><path d="M492 836c-1 1-1 3-2 5-1 4-4 7-7 11s-7 8-12 11c-1 1-2 2-4 3 11-9 16-22 25-33v3z" class="H"></path><defs><linearGradient id="BD" x1="534.673" y1="860.157" x2="516.115" y2="855.656" xlink:href="#B"><stop offset="0" stop-color="#838281"></stop><stop offset="1" stop-color="#c1bfc0"></stop></linearGradient></defs><path fill="url(#BD)" d="M535 843c2 1 3 1 3 3h0c0 1 0 2 1 3-2 7-11 13-17 16l-9 5v-4l2-1c7-3 14-6 18-12 2-4 3-6 2-10z"></path><defs><linearGradient id="BE" x1="544.098" y1="835.935" x2="516.719" y2="824.787" xlink:href="#B"><stop offset="0" stop-color="#b1b1b3"></stop><stop offset="1" stop-color="#d7d5d5"></stop></linearGradient></defs><path fill="url(#BE)" d="M520 818v-2c1 0 1-1 2-1v-1-1c2 5 7 11 9 16 4 5 6 11 10 16 2 3 4 5 7 8 1 1 3 3 4 5-5-4-10-8-14-12 0-2-1-2-3-3-1-3-3-6-5-9l-10-16z"></path><path d="M532 782c1 0 2-1 3-1s1-1 2-2c0 2 0 4-1 6-1 3 0 6-2 9-2 6-6 14-11 18-1-1-2-3-3-5 6-7 10-15 12-25z" class="H"></path><defs><linearGradient id="BF" x1="526.067" y1="873.958" x2="521.404" y2="866.497" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#cfcdcd"></stop></linearGradient></defs><path fill="url(#BF)" d="M516 871h1c3-1 6-2 9-4 7-4 13-6 21-6 2-1 5-1 6 0-1 1 0 0-1 0-9 1-22 7-28 14h0c-1 1-1 2-1 3h-2l-3 2-2-2c1-1 1-1 1-2l-2 1-8 2c-3 1-7 3-11 2-1 0-2 0-2-1 4 0 8-2 13-4 1 0 2 0 3-1 0 0 1-1 1-2 2-1 4 0 5-2z"></path><defs><linearGradient id="BG" x1="499.135" y1="852.514" x2="517.379" y2="769.13" xlink:href="#B"><stop offset="0" stop-color="#d5d4d5"></stop><stop offset="1" stop-color="#fcfcfc"></stop></linearGradient></defs><path fill="url(#BG)" d="M510 770c2 1 2-1 5-1 0 1 1 3 1 5l-3 1c-1 0-2 1-2 2h0c-2 1-3 2-3 4-1 2-1 7 1 10h1 0v-1c-1-3 0-7 0-10 1-1 1-2 3-2 1 2 1 9 1 12h0 1l1 1c0 1-1 3 0 4h0c-1 1-1 2-1 2l-1 2c0 3 4 7 6 8 1 2 2 4 3 5l-1 1v1 1c-1 0-1 1-2 1v2l10 16c2 3 4 6 5 9 1 4 0 6-2 10-4 6-11 9-18 12l-2 1h0c-1 1-2 1-4 1l1 1c-1 1-2 1-3 2-6-7-9-14-11-22v3c-1 1-1 1-2 1-2-6-2-11-2-16v-3c-1-2 0-4 0-6 0-8 2-14 6-21l2-3c1-2 4-4 6-6-2-2-3-4-4-7-1-5-2-11 1-15 1-3 3-5 6-6l1 1z"></path><path d="M496 835c0-13 5-21 13-32l7 10h0c-1 2-3 3-4 4l1 1c1 2 1 3 1 5l-1 2-1 1-1 1-1 1c-1 0-2 1-3 1l-1-3c-2 2-5 5-8 7 0 1-2 2-2 2z"></path><path d="M506 826c0-3 1-6 3-8 1-2 2-4 4-4l-1 1c-1 1-1 2-1 3l1-1 1 1c1 2 1 3 1 5l-1 2-1 1-1 1-1 1c-1 0-2 1-3 1l-1-3z" class="M"></path><path d="M512 826l1-1c1 1 2 2 2 4v1c1 3 0 7 0 11l1 19v3l-1 2-2 1h0c-1 1-2 1-4 1l1 1c-1 1-2 1-3 2-6-7-9-14-11-22 0-4 0-5 2-8v-1c0-1 1-2 2-3v-1l-1-1-3 3-1-1 1-1s2-1 2-2c3-2 6-5 8-7l1 3c1 0 2-1 3-1l1-1 1-1z" class="H"></path><path d="M512 826l1-1c1 1 2 2 2 4v1c0 3 0 6-1 9l-1 3 1-15-2-1z" class="Q"></path><path d="M515 830c1 3 0 7 0 11l1 19v3l-1 2-2 1h0c1-2 0-6 0-8v-16l1-3c1-3 1-6 1-9z" class="N"></path><path d="M498 840c3-4 7-7 10-12v1c-2 9-1 20 0 29 0 2 0 7 1 9l1 1c-1 1-2 1-3 2-6-7-9-14-11-22 0-4 0-5 2-8zm18-27c1 1 2 3 3 4l1 1 10 16c2 3 4 6 5 9 1 4 0 6-2 10-4 6-11 9-18 12l1-2v-3l-1-19c0-4 1-8 0-11v-1c0-2-1-3-2-4l1-2c0-2 0-3-1-5l-1-1c1-1 3-2 4-4z"></path><path d="M517 821c2 1 3 2 4 4-1 1-2 2-3 4l-3-6 2-2z" class="P"></path><path d="M516 813c1 1 2 3 3 4-1 1-2 2-2 4l-2 2h-1c0-2 0-3-1-5l-1-1c1-1 3-2 4-4z" class="H"></path><path d="M521 825c4 5 8 10 11 16-4-3-12-8-14-12 1-2 2-3 3-4z" class="N"></path><path d="M479 345c7 6 13 13 18 20 1 2 4 5 4 7v1c0 2 0 4 1 6h1l2-1c1 0 1 2 1 3 2 6 0 13 0 19 2-4 4-7 7-11-1 5-2 9-3 13-1 7-1 15-1 22 0 9-2 20 1 29l2 7c-6 8-11 17-14 27-4 11-2 25 0 37 3 16 10 32 17 46 2 3 3 7 5 10 1-3 1-4 0-7-2-5-3-9-3-15-2-5-5-10-7-16-3-6-5-12-7-18-2-5-3-9-3-14s3-12 7-16l2 3c0 9 1 17 1 26 0 6 0 13 2 19l3 9 1-30c0-5 1-12-1-16v-1h1c7 9 13 23 13 35 0 3 0 6-1 9 1-1 2-2 2-4 3-13-1-32-8-43-2-4-6-7-8-11v-2l1-1c2-5 6-9 9-13v-2c-2-1-2-2-4-2 2-2 5-4 7-7 4-7 1-15 0-23v-1-1c1-1 1-4 1-6 2-9 1-17 0-26-1-5-4-11-6-16-1-1-4-6-4-7 0 0 1-3 2-3l8-12 20-23c-5 13-8 25-9 38-2 15-2 31-2 46v60 198 40c0 19 0 38-5 55-2 10-6 18-12 25-2-1-6-5-6-8l1-2s0-1 1-2h0c-1-1 0-3 0-4l-1-1h-1 0c0-3 0-10-1-12-2 0-2 1-3 2 0 3-1 7 0 10v1h0-1c-2-3-2-8-1-10 0-2 1-3 3-4h0c0-1 1-2 2-2l3-1c0-2-1-4-1-5-3 0-3 2-5 1l-1-1c-3 1-5 3-6 6-3 4-2 10-1 15 1 3 2 5 4 7-2 2-5 4-6 6l-3-10c-1 1-1 1-3 1-1-1-2-2-3-4-1-3-1-9-3-12-1-3 1-7 2-9v-4l-1-14v-31-54-182-54c0-15 0-30-1-45-1-9-3-18-5-27-1-4-3-9-4-12v-1z"></path><path d="M526 493c4 6 5 15 6 21-1-1-5-11-5-11v-1c-1-3-1-6-1-9z" class="F"></path><path d="M506 572c1-1 1-1 1-2-1-3 0-7 1-10l1-1v2c0 1 0 1 1 2 2 4 1 10 1 14l-5-5z" class="N"></path><path d="M515 790c6-4 8-6 10-13 0-1 1-2 1-3 1 7-2 14-6 18-2 2-3 4-5 5 0 0 0-1 1-2h0c-1-1 0-3 0-4l-1-1z" class="H"></path><defs><linearGradient id="BH" x1="532.858" y1="382.507" x2="527.104" y2="385.493" xlink:href="#B"><stop offset="0" stop-color="#a3a3a3"></stop><stop offset="1" stop-color="#c4c3c7"></stop></linearGradient></defs><path fill="url(#BH)" d="M526 374h1c2 0 4 2 5 4 3 8 0 17-3 25l-1 1h0c1-8 4-15 1-24 0-2-2-4-3-6z"></path><path d="M517 558c2 5 3 9 5 14s4 9 2 14v1c-1-1-4-6-4-7 1-3 1-4 0-7-2-5-3-9-3-15zm-13 25h1c1 3 0 8 0 11 0 4-1 7 0 11 0 4 1 10 0 13v-1c-1-2-2-4-2-6-2-8-3-21 1-28z" class="H"></path><defs><linearGradient id="BI" x1="492.204" y1="663.289" x2="501.915" y2="648.787" xlink:href="#B"><stop offset="0" stop-color="#787878"></stop><stop offset="1" stop-color="#a0a09f"></stop></linearGradient></defs><path fill="url(#BI)" d="M500 632h0c0 4-2 8-3 12v2h2l2-6c1-2 2-3 3-5v1s-1 3-1 4c-2 3-3 6-4 9-1 9 0 18 0 26l-1-3c-5-13-3-28 2-40z"></path><defs><linearGradient id="BJ" x1="492.055" y1="791.25" x2="493.04" y2="779.63" xlink:href="#B"><stop offset="0" stop-color="#5e5c5c"></stop><stop offset="1" stop-color="#8d8e8e"></stop></linearGradient></defs><path fill="url(#BJ)" d="M490 765c2 9 4 19 7 28-1 1-1 1-3 1-1-1-2-2-3-4-1-3-1-9-3-12-1-3 1-7 2-9v-4z"></path><defs><linearGradient id="BK" x1="534.341" y1="451.442" x2="517.973" y2="465.215" xlink:href="#B"><stop offset="0" stop-color="#bababb"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#BK)" d="M527 440v-1c3 3 5 10 5 15 0 7-3 14-8 20v-2c-2-1-2-2-4-2 2-2 5-4 7-7 4-7 1-15 0-23z"></path><defs><linearGradient id="BL" x1="523.003" y1="489.8" x2="515.674" y2="487.457" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#707070"></stop></linearGradient></defs><path fill="url(#BL)" d="M515 487c2-1 3-4 5-5 3 3 5 7 6 11 0 3 0 6 1 9v1c-2-3-4-6-6-8s-6-4-7-7l1-1z"></path><path d="M514 678c0 1-1 2-1 3l1 1h1l1-1c0 3-4 7-2 10-1 5-2 9-1 15-2 0-4-1-5-2v-1c-2-5 0-11 1-16 1-1 1-3 1-5h0c1-2 3-3 4-4zm-13 67l2-1 2-2v-1c2 3 3 7 5 9 1 2 3 5 4 7v8l-1 3c-1 1-2 1-3 2l-1-1c0-3 0-7-1-11s-5-9-7-13z" class="H"></path><path d="M500 424l1 1v1c-1 4-3 8-3 12-2 12 2 21 6 31l-3 7-4-8c-4-13-3-32 3-44z" class="R"></path><defs><linearGradient id="BM" x1="491.886" y1="726.92" x2="505.563" y2="728.926" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#c3c3c3"></stop></linearGradient></defs><path fill="url(#BM)" d="M499 699c-3 15-1 28 6 42v1l-2 2-2 1v-1c-7-11-10-29-7-42 0 1 1 2 0 3v7h3v-5-1-2c0-2 1-2 1-4l1-1z"></path><defs><linearGradient id="BN" x1="521.945" y1="643.019" x2="522.048" y2="684.487" xlink:href="#B"><stop offset="0" stop-color="#b9b8b9"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#BN)" d="M514 678c8-7 15-14 15-24l-1-19c1 4 3 8 4 12 1 8 0 17-3 24-3 5-7 10-10 14-2 1-2 2-3 3v1l-1 2h-1c-2-3 2-7 2-10l-1 1h-1l-1-1c0-1 1-2 1-3z"></path><path d="M501 373c0 2 0 4 1 6h1c-1 3-3 6-4 10-1 5 0 10 1 15 1 10 5 20 6 30-2-2-3-6-5-9l-1-1-6-15c-3-13 0-24 7-36z" class="H"></path><path d="M509 711l3 3 6 6 2 5 1 1 1 1c-2 4-4 5-7 8l-1 1-1 16c-2 0-1-1-3-2-4-11-2-26-2-37 1-1 0-1 1-2z" class="R"></path><path d="M512 714l6 6 2 5 1 1c-2 2-4 5-6 6l-1 1v-1c-1 0 0-3 0-4-1-5-1-10-2-14z" class="L"></path><path d="M518 720l2 5 1 1c-2 2-4 5-6 6v-2c1-1 1-2 1-4 1-2 1-4 2-6z" class="E"></path><path d="M515 735c3-3 5-4 7-8v2c3 10 4 22-1 32-2 3-4 5-7 7h-1l1-3v-8c-1-2-3-5-4-7 2 1 1 2 3 2l1-16 1-1z"></path><path d="M515 735c2 7 1 14 1 22-1 2 0 5-1 7l-1 1v-8c-1-2-3-5-4-7 2 1 1 2 3 2l1-16 1-1z" class="F"></path><path d="M509 587c-3-6-8-12-11-18-8-16-6-30-1-45 0 5-2 11-2 16-1 13 3 23 11 32l5 5 11 15c2-4 5-8 7-12 1-3 1-5 2-8 0 5-1 11-3 16-1 2-3 6-3 9 0 1 1 2 1 3h-1v-1h-1l-3 2c-1 0-1 0-2-1-2 3-5 7-7 10l-5 9c-1 2-1 3-3 4 1-2 3-5 4-7 0-10-2-20 1-29z" class="M"></path><path d="M514 594c1 2 3 4 4 6l-2 2c-1 0-1 0-1-1-1-2-1-4-1-7z" class="I"></path><path d="M509 587c2 2 5 5 5 7 0 3 0 5 1 7 0 1 0 1 1 1l-8 14c0-10-2-20 1-29z" class="C"></path><defs><linearGradient id="BO" x1="528.151" y1="750.413" x2="514.767" y2="750.582" xlink:href="#B"><stop offset="0" stop-color="#aeadae"></stop><stop offset="1" stop-color="#e4e3e4"></stop></linearGradient></defs><path fill="url(#BO)" d="M516 689v-1c1-1 1-2 3-3 3 3 6 7 8 11l3 9c0 4 0 9-1 12v2c-1 1-1 2-1 2-1 2-1 3-1 4v1c4 10 3 29-2 39-2 5-8 10-13 12h-1 0c0-1 1-2 2-2l3-1c0-2-1-4-1-5-3 0-3 2-5 1 1-1 2-1 3-2h1c3-2 5-4 7-7 5-10 4-22 1-32v-2l-1-1-1-1-2-5-6-6-3-3s-3-2-4-3-2-2-2-4c0-1 0-2 1-3 1 2 2 2 4 3h0c1 1 3 2 5 2-1-6 0-10 1-15h1l1-2z"></path><path d="M523 718l2 2v3c1 1 0 4 0 5l-1 1v1c-1 0-1-1-2-1v-2l-1-1-1-1c2-2 2-5 3-7z" class="H"></path><path d="M520 706l1-7h1c1 2 2 5 3 8v13l-2-2-5-8 1-1c0-1 1-1 1-3z"></path><path d="M516 689v-1c1-1 1-2 3-3 3 3 6 7 8 11 0 2 0 6-1 9h0c0 1-1 2-1 2-1-3-2-6-3-8-1-3-3-5-4-7l-2-3z" class="M"></path><path d="M515 691l1-2 2 3c1 2 3 4 4 7h-1l-1 7c0 2-1 2-1 3l-1 1-2-2c-1-1-2-2-3-2-1-6 0-10 1-15h1z" class="L"></path><path d="M518 692c1 2 3 4 4 7h-1l-1 7c-1-5-3-9-2-14z" class="E"></path><path d="M515 691v2c2 3 1 11 1 15-1-1-2-2-3-2-1-6 0-10 1-15h1z" class="C"></path><path d="M509 711s-3-2-4-3-2-2-2-4c0-1 0-2 1-3 1 2 2 2 4 3h0c1 1 3 2 5 2 1 0 2 1 3 2l2 2 5 8c-1 2-1 5-3 7l-2-5-6-6-3-3z" class="M"></path><defs><linearGradient id="BP" x1="484.651" y1="659.594" x2="534.389" y2="650.721" xlink:href="#B"><stop offset="0" stop-color="#c4c2c1"></stop><stop offset="1" stop-color="#f1f0f2"></stop></linearGradient></defs><path fill="url(#BP)" d="M504 623c2-1 2-2 3-4l5-9c2-3 5-7 7-10 1 1 1 1 2 1l3-2h1v1h1c3 9 3 20 2 29-1 10-2 18-6 26-3 7-6 13-10 19l-9 15c-2 3-3 7-4 10l-1 1c0 2-1 2-1 4v2 1 5h-3v-7c1-1 0-2 0-3 1-8 4-17 9-25 1-3 4-6 5-9v-9-31c-1 3-2 5-4 8v-1c-1 2-2 3-3 5l-2 6h-2v-2c1-4 3-8 3-12h0c1-3 3-6 4-9z"></path><path d="M508 659c2 0 2 0 3 1v1h1 1c0 1-1 2-1 3-2 2-4 4-5 7-1 2-2 5-4 7v-1c1-3 4-6 5-9v-9z" class="H"></path><path d="M515 615l5-11c5 16 5 34-2 49-2 3-4 5-5 8h-1-1v-1c-1-1-1-1-3-1v-31s0-2 1-2c0-4 3-8 6-11z"></path><path d="M509 626c0-4 3-8 6-11-1 8 0 16 0 24 0 7 0 13-2 19-1 1-1 2-1 3h-1v-1c-1-1-1-1-3-1v-31s0-2 1-2z" class="H"></path><path d="M509 626v4l1 1v4c1 4 1 9 1 13 0 2-1 6 0 8 0 1 2 2 2 2-1 1-1 2-1 3h-1v-1c-1-1-1-1-3-1v-31s0-2 1-2z" class="J"></path><path d="M377 264c-1 1-11 3-13 3-15 4-30 9-44 16-21 11-35 27-42 50-1 4-2 10-1 13 0 3 2 5 3 7l-42 1c2-6 5-11 6-16 0-6 0-11-1-17-3-20-12-41-25-57-10-12-21-23-37-25-15-2-31 3-42 12-16 13-20 31-22 51-3-6-5-13-5-20-2-22 7-41 21-56 15-17 36-33 57-39 16-5 34-3 51-3l44-1h133 79l-1-17c-23 4-47 7-71 4 8-2 16-5 24-9 25-13 47-38 57-66 4-8 5-17 7-25 5 26 15 47 33 67 14 14 30 25 49 31-2 1-3 1-5 3v-1c-1 0-3 1-4 1h5c-8 2-18 0-26-1-4 0-8 0-12-1l-22-3v17h30 47 145l40 1c14 0 29-1 43 3 23 6 44 22 59 40 13 16 22 35 20 56-1 6-2 14-6 19-1-18-3-35-17-48-12-11-28-16-44-15-14 1-25 8-34 18-18 20-33 51-31 79 0 6 3 12 6 17h-42c2-2 3-5 4-8 1-4-2-11-3-15-9-24-25-39-48-50-9-4-19-7-29-10-37-10-74-13-113-13h-16c-2 0-6-1-8 0-7 3-13 15-16 21-1 3-1 5-2 8-1 1-1 3-2 5l-4-1c-2-10-4-22-13-28s-23-5-33-5l-46 2-41 5z" class="M"></path><path d="M409 228c0 1 1 2 1 3l-14 2c1-2 3-2 5-3h0c1-1 2-1 3-1 2 0 3 0 5-1z" class="E"></path><path d="M397 227l4 3h0c-2 1-4 1-5 3h-3c-1 1-2 1-3 1l2-2 3-2h0c0-2 1-2 2-3z" class="H"></path><path d="M355 237v-3l1 2c4 1 9 0 13 1h1l-16 4v-1c1 0 2-1 3-1l1-1-3-1z" class="I"></path><path d="M420 226c3 0 6 0 8 1l4 2h1l-23 2c0-1-1-2-1-3l11-2z"></path><path d="M511 240c0 11 1 23-1 34v-1c-3-6-6-11-12-15 1 0 2 0 2-1 2-1 3-4 5-5 1-1 3-2 4-2 2-3 1-5 2-9v-1z" class="K"></path><defs><linearGradient id="BQ" x1="405.727" y1="217.342" x2="408.412" y2="227.166" xlink:href="#B"><stop offset="0" stop-color="#a2a1a2"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#BQ)" d="M392 220c6-2 12-2 17-1 6 0 12 2 18 2h1v1h1c3 2 6 3 8 5-1 0-2 0-3-1h0c-1 0-2-1-2-1l-1-1h-1-1c-3-1-4-2-7-2-1 1-1 1-1 2v1c2 0 3 0 5 1h1 1l1 1h-1c-2-1-5-1-8-1l-11 2c-2 1-3 1-5 1-1 0-2 0-3 1l-4-3c-1-1-3-2-4-3h0c-2-1-3 0-5 0h-5c2-2 7-3 9-4z"></path><path d="M393 224c7-3 15-2 23-1 1 0 2 1 4 2v1l-11 2c-2 1-3 1-5 1-1 0-2 0-3 1l-4-3c-1-1-3-2-4-3z"></path><path d="M416 223c1 0 2 1 4 2v1l-11 2c-2 1-3 1-5 1 3-3 7-4 11-6h1z" class="R"></path><path d="M514 189c0 2 0 4 1 5 1 2 2 5 3 7 4 7 8 15 14 22 0 2-1 6-2 8-2 2-3 4-4 6-4-5-9-14-11-20v-1c-1-3-1-7-1-11v-16z" class="H"></path><path d="M383 224h5c2 0 3-1 5 0h0c1 1 3 2 4 3-1 1-2 1-2 3h0l-3 2-2 2-20 3h-1c-4-1-9 0-13-1l-1-2h1v-3c1-1 3-1 4-2 7-3 15-4 22-6l1 1z" class="C"></path><path d="M356 234c5 0 11 0 17 1l-4 2c-4-1-9 0-13-1l-1-2h1zm27-10h5c2 0 3-1 5 0h0c1 1 3 2 4 3-1 1-2 1-2 3h0l-3 2c-3-1-6-2-9-2-2-1-4 0-5-1-6 1-11 2-17 2-2 1-3 1-5 0 1-1 3-1 4-2 7-3 15-4 22-6l1 1z" class="J"></path><path d="M378 229c3-2 9-4 12-3 2 1 4 2 4 3l1 1-3 2c-3-1-6-2-9-2-2-1-4 0-5-1zm67-13c9-1 18-1 26 2 2 1 5 2 7 3l1 1c3 1 7 1 10 1l1 1c0 2 0 4 1 6l-25-1h-15-8c-2 0-4-1-6-2h0c-2-2-5-3-8-5h-1v-1h-1 0l10-3h3c2-1 4-2 5-2z"></path><path d="M427 221h0l10-3c2 1 3 1 4 2-3 1-10 1-12 2h-1v-1h-1z" class="O"></path><path d="M479 222c3 1 7 1 10 1l1 1c-3 1-6 2-8 4-2 0-3-1-5-2v-3l2-1z" class="H"></path><defs><linearGradient id="BR" x1="461.458" y1="214.607" x2="470.537" y2="226.922" xlink:href="#B"><stop offset="0" stop-color="#787576"></stop><stop offset="1" stop-color="#a1a1a1"></stop></linearGradient></defs><path fill="url(#BR)" d="M445 216c9-1 18-1 26 2 2 1 5 2 7 3l1 1-2 1v3c-7-2-15-5-22-6-5-1-9 0-14 0-1-1-2-1-4-2h3c2-1 4-2 5-2z"></path><path d="M440 218c6-1 11 0 17 0l-2 2c-5-1-9 0-14 0-1-1-2-1-4-2h3z" class="G"></path><defs><linearGradient id="BS" x1="419.295" y1="235.956" x2="392.785" y2="253.505" xlink:href="#B"><stop offset="0" stop-color="#a0a09f"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#BS)" d="M377 264c1-1 1-1 3-1 1-1 1 0 2 0 0-1 1-1 1-1 2 0 2 0 3-1 1 0 2 1 3 0h2c1 0 0-1 2-1 0 0 1 1 2 1 0-1 1-1 1-1h2 1l-1-1v-1l-3-2h0c-1-1 0-1-1-1-1-1-2-2-2-4h0c-2-1-4-4-5-6 0 0-1-1-1-2-1-1-1 0-1-1v-1l-1-1v-1h-1c-2 1-3 0-5 0h0 1v-1h1 1c2 1 21-3 25-3l17-2v1h-4c0 4 2 8 4 12 1 2 2 4 3 7h5v-1h0c3 1 6 1 8 0h1l4 1-30 2c-1 1-3 3-4 3 3 0 5 0 8 1l-41 5z"></path><path d="M343 211c1-1 3-1 4-1h3 0 2 10 1c-1-1-1-2-2-2v-1c-1-1-1-1 0-2l-1-1v-3c-1-1-1-2-1-3v-1c0-3-1-5 1-7h1l1-1h0c2-1 3-1 6-1 1-1 4 0 5 0l1-1c3 0 7 0 10 1h7 12 64 0l-81 1c3 1 8 0 11 0l-1 1c-2 2-3 3-3 6 0 1 0 2 1 3v7c2 1 4 2 5 4v1h-56zm211-23h67 13 7c3-1 7-1 10-1l1 1c1 0 3-1 4 0 1 0 1 0 2 1l1-1 1 1h2l1 1v1h1c1 0 1 0 1 1v1c0 1 1 3 0 5 0 1 0 1-1 3v2 1l-1 1v1c-1 2-3 3-4 4h23 0c-5 1-11 1-16 1h-40v-1h-11c2-1 4-1 6-2 1 0 2-1 4-2 0-3 2-8 1-12v-2c-1-2-2-1-2-3h7c-5-1-12 0-16 0h-39c-7 0-15 0-22-1h0z" class="H"></path><defs><linearGradient id="BT" x1="524.731" y1="142.237" x2="594.409" y2="153.222" xlink:href="#B"><stop offset="0" stop-color="#999998"></stop><stop offset="1" stop-color="#efeeef"></stop></linearGradient></defs><path fill="url(#BT)" d="M526 117c3 4 14 21 18 22 0-1-3-3-3-4-1-2-3-3-4-5h1c1 0 1 1 1 1l2 2 1 1c0 1 1 1 2 2l2 2v-1c14 14 30 25 49 31-2 1-3 1-5 3v-1c-1 0-3 1-4 1h5c-8 2-18 0-26-1-4 0-8 0-12-1l1-1h-2 0-2c-1 0-2 0-3-1h0-1l-1-3h1c2 1 3 0 5 1h2c-3-2-8-2-11-2-5-1-9-3-13-4l-1 10v-9-9c1-3 1-29-1-31 0-1 0-1-1-2v-1z"></path><defs><linearGradient id="BU" x1="492.865" y1="146.193" x2="624.12" y2="205.735" xlink:href="#B"><stop offset="0" stop-color="#868685"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#BU)" d="M514 189V95c3 8 8 15 12 22v1c1 1 1 1 1 2 2 2 2 28 1 31v9 9l1 16c7 2 17 3 25 3h0c7 1 15 1 22 1h39c4 0 11-1 16 0h-7c0 2 1 1 2 3v2c1 4-1 9-1 12-2 1-3 2-4 2-2 1-4 1-6 2h11v1c-6 1-13 0-20 0h-41-4l-10-1h0 3v-1h-3l-1-1h-6-1c-2-1-4-1-6-1-3 4-5 11-5 16-6-7-10-15-14-22-1-2-2-5-3-7-1-1-1-3-1-5z"></path><path d="M544 208c6-1 12-1 18 0l-1 1 1 1c1 0 2-1 3 1h-4l-10-1h0 3v-1h-3l-1-1h-6z" class="J"></path><defs><linearGradient id="BV" x1="507.436" y1="251.451" x2="409.345" y2="172.85" xlink:href="#B"><stop offset="0" stop-color="#5c5c5c"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#BV)" d="M467 188l43-2 1 39c0 4 1 11 0 15v1c-1 4 0 6-2 9-1 0-3 1-4 2-2 1-3 4-5 5 0 1-1 1-2 1-5-2-10-3-15-4-13-2-26-2-39-1l-4-1h-1c-2 1-5 1-8 0h0v1h-5c-1-3-2-5-3-7-2-4-4-8-4-12h4v-1c24-2 48-1 72 0-1-8-1-18-4-26l-15 1h0-9c-2 0-4 0-5 1-2 0-3-1-5 1h3v1h-8-21-9-23v-1c-1-2-3-3-5-4v-7c-1-1-1-2-1-3 0-3 1-4 3-6l1-1c-3 0-8 1-11 0l81-1h0z"></path><path d="M431 211c5-3 13 1 18-2-2 0-3-1-4 0h-1l-1-1h14 19 0-9c-2 0-4 0-5 1-2 0-3-1-5 1h3v1h-8-21z" class="J"></path><path d="M431 211h21 8 28c1 4 1 8 1 12-3 0-7 0-10-1l-1-1c-2-1-5-2-7-3-8-3-17-3-26-2-1 0-3 1-5 2h-3l-10 3h0c-6 0-12-2-18-2-5-1-11-1-17 1-2 1-7 2-9 4l-1-1c-7 2-15 3-22 6-1 1-3 1-4 2v3h-1v3l3 1-1 1c-1 0-2 1-3 1v1l-37 13c-11 4-24 10-33 18-9 7-15 17-19 27-2-14-6-27-13-39-10-16-27-31-45-38-6-3-13-5-19-6 14-5 28-5 42-5h40 73 56 23 9z"></path><path d="M355 223c1 0 6-1 7-1v1c-2 1-4 2-6 4h0l-1-1c0-1-1-2 0-3z" class="G"></path><path d="M287 217l2-1 1 1v1c-2 2-6 3-8 5 0-1 1-3 0-5h1v1 1h1l3-3z" class="C"></path><path d="M256 256l6 6c-1 1-2 1-4 1-1-2-3-4-4-6l2-1z" class="G"></path><path d="M245 225h0c-1-2-2-3-2-5v-1c2 0 5-1 7 0 0 1 1 2 1 3l-6 3z" class="I"></path><path d="M263 218c7-2 13-1 19-2 1-1 3 1 4 1h1l-3 3h-1v-1-1h-1c-3 1-12 1-15 1-1-1-2-1-4-1z" class="B"></path><path d="M366 219c1-1 2-1 3-2 3-1 5-2 8-2s6 1 9 1c2 1 4 1 7 1-3 1-6 2-9 2l-2 1c-6-1-10-2-16-1z" class="L"></path><path d="M262 262c7 2 13 3 21 2-1 1-2 3-3 3h-1c-1 0-2 1-3 1-7 0-13-1-18-5 2 0 3 0 4-1z" class="Q"></path><path d="M393 217c8 0 16-2 25 0-1 0-3 1-4 1-1 1-2 0-3 0s-1 0-2 1c-5-1-11-1-17 1h-10l2-1c3 0 6-1 9-2z" class="I"></path><path d="M344 220l1-1c-2 0-5 1-6 2-2 0-3 0-4-2 2-1 25-5 28-5h0c-5 3-13 4-17 9v-1c0-1-1-2-2-2z" class="C"></path><path d="M263 218c2 0 3 0 4 1 3 0 12 0 15-1 1 2 0 4 0 5l-1 5c-1-1-2-3-3-3v5c-1-2-1-5 0-7 0-1 1-1 1-3-6 0-10 1-16 2-1-1-5-1-7-2l7-2z" class="N"></path><defs><linearGradient id="BW" x1="248.806" y1="253.712" x2="242.154" y2="236.183" xlink:href="#B"><stop offset="0" stop-color="#a6a7a5"></stop><stop offset="1" stop-color="#c9c7c9"></stop></linearGradient></defs><path fill="url(#BW)" d="M254 257c-5-7-11-13-17-20l4-1c2 3 6 7 9 10 2 3 4 7 6 10l-2 1z"></path><path d="M320 217c7 2 13 5 19 8-1 1-1 1-2 3 0 0-11-5-13-5-4-1-9-1-13-1 3-1 6 0 8-1v-1c2 0 5 0 7 1l-6-3h0v-1z" class="O"></path><path d="M418 217c5 1 9-1 13-1h14c-1 0-3 1-5 2h-3l-10 3h0c-6 0-12-2-18-2 1-1 1-1 2-1s2 1 3 0c1 0 3-1 4-1z" class="C"></path><path d="M290 223c9-2 19-3 29-3v1c-2 1-5 0-8 1-7 0-14 1-20 5-2 1-4 3-6 4l-1-1c0-1-1-1 0-3s4-3 6-4z" class="J"></path><defs><linearGradient id="BX" x1="371.497" y1="218.113" x2="374.209" y2="223.279" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#66676a"></stop></linearGradient></defs><path fill="url(#BX)" d="M355 223c3-3 8-4 11-4 6-1 10 0 16 1h10c-2 1-7 2-9 4l-1-1h0c-5-3-15-1-20 0v-1c-1 0-6 1-7 1z"></path><path d="M290 223c0-1 0-2 1-3 2-1 5-1 7-2 7-1 15-3 22-1v1h0l6 3c-2-1-5-1-7-1-10 0-20 1-29 3z" class="B"></path><path d="M336 237h-2c-7 2-19 3-26 1-3-1-3-2-4-4h0l35-1c-1 2-1 3-3 4h0z" class="R"></path><path d="M244 235v-2h3c1 1 1 3 2 3 1 2 3 4 5 5 8 7 19 8 29 6 2 0 6-2 8-1 1 0 1 0 2 1-2 1-5 2-7 3-10 3-22 1-31-4-4-2-9-5-11-11z" class="H"></path><defs><linearGradient id="BY" x1="285.201" y1="270.187" x2="281.923" y2="265.805" xlink:href="#B"><stop offset="0" stop-color="#747474"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#BY)" d="M283 264l6-3c-2-1-5-3-8-3l1-1c3 1 6 2 9 2s7-2 10-3c5-1 10-3 15-4-6 4-12 6-19 9-9 4-18 10-26 18-2 2-5 4-7 7h0v-4l4-4 9-9 3-2c1 0 2-2 3-3z"></path><path d="M249 236h1s1 1 2 1c3 1 8 0 12 0h18l6 6c-6-1-12 1-19 1-5-1-10-3-15-3-2-1-4-3-5-5z" class="F"></path><path d="M344 220c1 0 2 1 2 2v1c-1 2-1 4 0 6 2 4 5 6 9 8l3 1-1 1c-1 0-2 1-3 1h-1c-3-1-6-3-9-5-6 6-12 9-20 12h0v-1h-8c-8 0-12-4-17-9v-1c0-1 0-2 1-2l1-1v1c2 4 6 7 10 8 6 2 14 2 19 0 2-1 4-3 6-5h0c2-1 2-2 3-4 1-1 2-1 2-2-1-2-2-2-4-3 1-2 1-2 2-3h1c1-2 3-3 4-5z" class="H"></path><path d="M278 230v-5c1 0 2 2 3 3l3 6h16c-1 0-1 1-1 2v1c5 5 9 9 17 9h8v1h0l-2 2c-13 3-23 0-34-6l-6-6c-1-1-2-2-3-4l-1-3z" class="M"></path><path d="M288 238h1c3-1 7 1 10-1 0 2 0 3 1 4 0 1 1 3 1 4-4-2-9-4-13-7z" class="L"></path><path d="M284 234h16c-1 0-1 1-1 2v1h0c-3 2-7 0-10 1h-1 0l-4-4z" class="J"></path><path d="M299 237h0c5 5 9 9 17 9h8v1c-8 1-15 1-23-2 0-1-1-3-1-4-1-1-1-2-1-4z" class="D"></path><path d="M251 222l5-2c2 1 6 1 7 2 6-1 10-2 16-2 0 2-1 2-1 3-1 2-1 5 0 7l1 3c1 2 2 3 3 4h-18c-4 0-9 1-12 0-1 0-2-1-2-1h-1c-1 0-1-2-2-3h-3v2c-1-1-1-2 0-4 0-1 1-2 1-4v-2l6-3z"></path><path d="M251 222l5-2c2 1 6 1 7 2-5 1-10 3-14 7h0-1c-1-1-2-1-3-2v-2l6-3z" class="K"></path><path d="M245 227c1 1 2 1 3 2h1c-1 2-1 3 0 4h30c1 2 2 3 3 4h-18c-4 0-9 1-12 0-1 0-2-1-2-1h-1c-1 0-1-2-2-3h-3v2c-1-1-1-2 0-4 0-1 1-2 1-4z" class="J"></path><path d="M551 210l10 1h4 41c7 0 14 1 20 0h40c5 0 11 0 16-1l75 1h33c16 0 33-1 48 5-24 4-46 20-60 40-9 13-13 28-16 44-4-10-9-19-17-26-5-4-11-8-16-10-8-4-16-8-24-11l-34-12-30-7h-2-2-5c-1-1-2-1-3-1-2-1-4 0-5 0-1-1-3-1-4-1-1-1-3 0-4 0-1-1-2-1-3-1h-5v3h0c0 2 0 3-1 4h0c-1 2 0 1-1 2s-2 3-3 4c-1 2-3 3-3 5v1c-2 1 0 1-1 1 0 0-1 0-1 1l-1 1h-1-1-2-1-1-1c0-1-1 0-1 0-2-1-2-1-3-1-1-1-2 0-3-1-1 0-2-1-2-1l-1-1h-1v-1c-2 0-3-2-4-3h-1l-1-2s-1-1-1-2l-1-2-1-1-1-1c0-1 0-1-1-1l-2-2-2-5h-4c-2 2-8 0-10 1h-1-4c-3-1-7 0-10 1 1-7 2-14 4-20 3-1 9 0 12-1z"></path><path d="M722 237h2c-1 3-2 6-6 8h-1l1-2h-1c2-2 4-4 5-6z" class="H"></path><path d="M785 237l1-1-1-1c2-2 7-2 10-2-2 1-4 2-5 4h-1c-1 1-3 2-4 3h-1c1-1 1-1 1-2h1l-1-1z" class="G"></path><path d="M777 220h1c1-1 3-2 4-3 3-1 5-1 7-1 0 0-1 0-1 1-2 1-5 3-6 6v1l-1-1c-2 0-3-2-4-3z" class="F"></path><path d="M540 224l-2 1h0l1-2c0-2 0-4 2-6 1 0 2 0 2 1 3 1 2 3 3 4-2 1-4 1-6 2h0zm130-7c8-1 15 2 22 3v1h-5c-3-1-7-2-10-2v1c-3-1-5-2-7-3z" class="I"></path><path d="M710 216c7 0 13 1 19 3l3 2c-3 0-6 0-8-1l-5-1v-1h-1c-2-1-6 0-8-2z" class="Q"></path><path d="M729 219c2-1 4-1 6-2 4 0 9 0 13 1h0l-8 1c-1 0 0 0-1 1h2l-5 1h-1-3l-3-2z" class="C"></path><path d="M572 216c7-3 13 0 20 1 2 1 4 1 7 1-4 0-8 0-11 1l-16-3z" class="B"></path><path d="M748 218c3 1 6 3 8 2 1 1 2 1 3 2-2 1-2 1-4 1l2 2c-3-1-5-2-8-2l-1 1v-1c-2-2-4-2-7-3h-2c1-1 0-1 1-1l8-1z" class="N"></path><path d="M770 256l1 2c-2 4-7 9-12 10-3 1-6 0-9 1-1 0-1 0-1-1 1 0 1 0 2-1h-2-1c1-1 4 0 6 0 7-1 11-5 16-11z" class="K"></path><path d="M741 220c3 1 5 1 7 3v1l-1 4-2 2-1 1-4-6c-1-2-3-3-5-4h1l5-1z"></path><path d="M785 237l1 1h-1c0 1 0 1-1 2h1c1-1 3-2 4-3h1l-9 9c-4 4-7 8-10 12l-1-2 6-9c3-4 6-7 9-10z" class="R"></path><path d="M599 218c8-2 14-3 22-1-4 0-8 2-13 3h-10c-3 1-7 0-10-1 3-1 7-1 11-1z" class="N"></path><path d="M630 219h1c0-1 1-2 2-2 1-1 4-1 6-1 5-1 10 0 16 0h11c-6 0-12 1-18 2l-13 4c-2-1-3-2-5-3z" class="B"></path><path d="M743 265l5 2h1 2c-1 1-1 1-2 1 0 1 0 1 1 1 2 1 4 2 5 3 6 5 6 11 7 17-2-4-3-8-6-12-4-5-13-7-18-11 2-1 3 0 5-1z" class="G"></path><path d="M748 218l1-1 1-1v1c8 3 16 1 23 2l4 1c1 1 2 3 4 3l1 1v1l-3 3c-1-2-1-3-2-4-1 0-3-1-5-1-4-1-9-3-13-2v1c-1-1-2-1-3-2-2 1-5-1-8-2h0z" class="I"></path><path d="M621 217l9 2c2 1 3 2 5 3l-2 1c-2 1-3 1-5 2-6-4-16-3-23-3-2-2-4-2-7-2h10c5-1 9-3 13-3z" class="K"></path><path d="M608 220c6 0 12 0 17 1l8 2c-2 1-3 1-5 2-6-4-16-3-23-3-2-2-4-2-7-2h10z" class="G"></path><path d="M692 220l11-3c2 0 4-1 7-1 2 2 6 1 8 2h1v1l-15 2h0c-7 0-14 2-22 2h0-1l-4-2v-1-1c3 0 7 1 10 2h5v-1z" class="F"></path><defs><linearGradient id="BZ" x1="741.546" y1="250.817" x2="735.324" y2="263.434" xlink:href="#B"><stop offset="0" stop-color="#6d6c6d"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#BZ)" d="M738 266c-9-6-18-9-28-13 4 0 7 1 11 2 4 2 9 5 13 5 1 0 3-1 3-2 4-1 8-3 12-5 3-1 7-2 10-2-1 1-3 1-5 2-4 3-10 6-14 9v1l3 2c-2 1-3 0-5 1z"></path><path d="M759 222v-1c4-1 9 1 13 2 2 0 4 1 5 1 1 1 1 2 2 4 0 1 1 2 1 4-2-1-6-1-8 0-6-1-10-4-15-7l-2-2c2 0 2 0 4-1z"></path><path d="M740 225l4 6 1 2c-4 8-8 13-16 15-7 3-20 4-27 1l-1-1c11 1 24 1 33-7l3-3c1-1 1-2 1-4l1-1c1-2 0-4-2-6 1-1 2-1 3-2z" class="H"></path><defs><linearGradient id="Ba" x1="749.961" y1="232.264" x2="768.933" y2="249.026" xlink:href="#B"><stop offset="0" stop-color="#bcb9bb"></stop><stop offset="1" stop-color="#edeced"></stop></linearGradient></defs><path fill="url(#Ba)" d="M779 228l3-3 2 7c-5 10-14 14-24 17-6 2-18 3-24 0 0-1 0-1-1-1v-1c3-1 6 0 9 0 11 1 23-1 31-9 2-2 4-4 5-6 0-2-1-3-1-4z"></path><path d="M684 225c1 0 2 1 2 2v1c-1 2 0 2 0 4 1 1 1 1 2 1 2 1 3 1 5 0h7v1l-1 1c-1 0-2 0-3 1l-5 2h-2v1c7 5 13 6 21 6 3-1 5-2 7-2h1l-1 2c-2 1-5 2-7 2-4 0-9 0-13-1h0c-5-2-9-4-11-9l-1-1-1 1-2 1c-3 3-6 3-10 2l-3-1c2 0 4-1 5-1 3-1 5-2 6-5 1-2 1-3 0-5v-3h3 1z" class="H"></path><path d="M710 245c3-1 5-2 7-2h1l-1 2c-2 1-5 2-7 2l-3-1h0 2 1 0l-4-1h4z" class="M"></path><defs><linearGradient id="Bb" x1="566.068" y1="217.019" x2="565.947" y2="226.783" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#b9b7b9"></stop></linearGradient></defs><path fill="url(#Bb)" d="M546 222c6-1 10-4 16-5 3-1 7-1 10-1l16 3c3 1 7 2 10 1 3 0 5 0 7 2-4 0-7 1-10 2-2 0-6 2-8 2v-1c1 0 2 0 3-1h2c1-1 1 0 1-1-1 0-3-1-4-1l-1 1s-1 0-2-1c-3-1-5-1-8-1-8-1-15-1-23 3-3 1-8 4-12 4l-3-4h0c2-1 4-1 6-2z"></path><path d="M699 235c2 0 4-1 6-1 6-1 12 0 18 0l2 2-1 1h-2c-1 2-3 4-5 6-2 0-4 1-7 2-8 0-14-1-21-6v-1h2l5-2c1-1 2-1 3-1z" class="E"></path><path d="M699 235c2 0 4-1 6-1 6-1 12 0 18 0l2 2-1 1h-2c-6 2-12 2-19 1h-12l5-2c1-1 2-1 3-1z" class="J"></path><path d="M748 224l1-1c3 0 5 1 8 2 5 3 9 6 15 7 2-1 6-1 8 0-1 2-3 4-5 6-2 0-3 2-5 3-9 1-18 0-26-1 0-2 1-4 1-6v-1l-1-2 1-1 2-2 1-4z" class="Q"></path><path d="M747 233l16 1 1 1c-6 0-13 1-18 0v-1l1-1z" class="H"></path><defs><linearGradient id="Bc" x1="762.407" y1="225.418" x2="764.593" y2="233.582" xlink:href="#B"><stop offset="0" stop-color="#9a9a99"></stop><stop offset="1" stop-color="#bdbbbc"></stop></linearGradient></defs><path fill="url(#Bc)" d="M748 224l1-1c3 0 5 1 8 2 5 3 9 6 15 7h0 6c0 1 0 2-1 2l-13 1-1-1-16-1-1 1c-1-1-1-3-1-4l2-2 1-4z"></path><path d="M749 230l4-4c2 1 4 3 7 4 1 1 3 2 5 3l-2 1-16-1-1 1c-1-1-1-3-1-4l2-2c0 1 1 2 2 2z"></path><path d="M747 228c0 1 1 2 2 2l-2 3-1 1c-1-1-1-3-1-4l2-2z" class="R"></path><path d="M719 219l5 1c2 1 5 1 8 1h3c2 1 4 2 5 4-1 1-2 1-3 2 2 2 3 4 2 6l-1 1c0 2 0 3-1 4-3 1-5 1-8 1-1 0-1-1-2-2h0c-1-1-1-1-2-1l-2-2c-6 0-12-1-18 0-2 0-4 1-6 1l1-1v-1h-7c-2 1-3 1-5 0-1 0-1 0-2-1 0-2-1-2 0-4v-1c0-1-1-2-2-2l-3-2h1 0c8 0 15-2 22-2h0l15-2z"></path><path d="M719 219l5 1-1 2c-6-1-12 0-18 0v-1h-1 0l15-2z" class="O"></path><path d="M724 220c2 1 5 1 8 1h3c2 1 4 2 5 4-1 1-2 1-3 2h0c-5-3-9-4-14-5l1-2z" class="P"></path><path d="M723 234h15c0 2 0 3-1 4-3 1-5 1-8 1-1 0-1-1-2-2h0c-1-1-1-1-2-1l-2-2z" class="O"></path><path d="M681 223h1 0c8 0 15-2 22-2h1v1c-7 2-12 3-19 6v-1c0-1-1-2-2-2l-3-2z" class="J"></path><defs><linearGradient id="Bd" x1="708.224" y1="224.069" x2="705.81" y2="233.815" xlink:href="#B"><stop offset="0" stop-color="#a2a1a0"></stop><stop offset="1" stop-color="#c0bec1"></stop></linearGradient></defs><path fill="url(#Bd)" d="M688 233c12-2 21-8 32-10-6 4-13 8-20 11v-1h-7c-2 1-3 1-5 0z"></path><path d="M648 218c6-1 12-2 18-2l4 1c2 1 4 2 7 3v1l4 2 3 2h-1-3v3c1 2 1 3 0 5-1 3-3 4-6 5-1 0-3 1-5 1l3 1-1 1-30-7c-10-2-20-3-30-4h4l1-1 12-4c2-1 3-1 5-2l2-1 13-4z"></path><path d="M677 221l4 2 3 2h-1-3v3c-1-2-3-4-6-5 1-2 1-2 3-2z" class="R"></path><path d="M660 235c5 1 11 1 16-1 2-1 3-1 4-1-1 3-3 4-6 5-1 0-3 1-5 1 0-1-1-1-2-1-1-1-1 0-2-1l-1-1h-2c-1-1-1 0-2-1h-1 1z" class="C"></path><path d="M648 218c6-1 12-2 18-2l4 1c2 1 4 2 7 3v1c-2 0-2 0-3 2l-6-3c-4-1-9-1-13-1-2-1-5-1-7-1z" class="P"></path><defs><linearGradient id="Be" x1="650.443" y1="218.982" x2="645.285" y2="235.664" xlink:href="#B"><stop offset="0" stop-color="#909090"></stop><stop offset="1" stop-color="#cfcccf"></stop></linearGradient></defs><path fill="url(#Be)" d="M648 218c2 0 5 0 7 1-8 2-13 5-20 9l24-2c-3 1-5 2-7 3 5 1 10 1 15 0 2-1 5-1 7-1 1 0 2 0 3 1-1 1-4 2-6 3l-7 1h-5v1l1 1h-1 1c1 1 1 0 2 1h2l1 1c1 1 1 0 2 1 1 0 2 0 2 1l3 1-1 1-30-7c-10-2-20-3-30-4h4l1-1 12-4c2-1 3-1 5-2l2-1 13-4z"></path><defs><linearGradient id="Bf" x1="643.991" y1="230.368" x2="643.571" y2="237.14" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#464648"></stop></linearGradient></defs><path fill="url(#Bf)" d="M616 229c5 1 9-1 14 0 2 0 5 1 7 2 9 1 18 2 27 2h-5v1l1 1h-1 1c1 1 1 0 2 1h2l1 1c1 1 1 0 2 1 1 0 2 0 2 1l3 1-1 1-30-7c-10-2-20-3-30-4h4l1-1z"></path><path d="M605 222c7 0 17-1 23 3l-12 4-1 1h-4c10 1 20 2 30 4h-2-2-5c-1-1-2-1-3-1-2-1-4 0-5 0-1-1-3-1-4-1-1-1-3 0-4 0-1-1-2-1-3-1h-5v3h0c0 2 0 3-1 4h0c-1 2 0 1-1 2s-2 3-3 4c-1 2-3 3-3 5v1c-2 1 0 1-1 1 0 0-1 0-1 1l-1 1h-1-1-2-1-1-1c0-1-1 0-1 0-2-1-2-1-3-1-1-1-2 0-3-1-1 0-2-1-2-1l-1-1h-1v-1c-2 0-3-2-4-3h-1l-1-2s-1-1-1-2l-1-2-1-1-1-1c0-1 0-1-1-1l-2-2-2-5h-4c-2 2-8 0-10 1h-1-4c7-3 16-1 23-2 3 0 6-2 9-3l9-3c1 1 2 1 2 1l1-1c1 0 3 1 4 1 0 1 0 0-1 1h-2c-1 1-2 1-3 1v1c2 0 6-2 8-2 3-1 6-2 10-2z" class="H"></path><path d="M605 222c7 0 17-1 23 3l-12 4-1 1h-4-10l-23-2 9-2c2 0 6-2 8-2 3-1 6-2 10-2z"></path></svg>