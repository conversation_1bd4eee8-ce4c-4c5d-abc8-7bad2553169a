<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="56 32 600 640"><!--oldViewBox="0 0 716 752"--><style>.B{fill:#a8a7a8}.C{fill:#3c3b3c}.D{fill:#bfbebe}.E{fill:#919090}.F{fill:#858484}.G{fill:#aeadad}.H{fill:#9f9e9e}.I{fill:#494848}.J{fill:#626162}.K{fill:#4f4e4f}.L{fill:#c7c6c7}.M{fill:#989798}.N{fill:#7e7d7d}.O{fill:#b4b3b3}.P{fill:#575657}.Q{fill:#535252}.R{fill:#5b5a5b}.S{fill:#353434}.T{fill:#757475}.U{fill:#8b8a8a}.V{fill:#bab9b9}.W{fill:#434243}.X{fill:#6f6f6f}.Y{fill:#cdcccd}.Z{fill:#6c6b6c}.a{fill:#d6d5d5}.b{fill:#676666}.c{fill:#797879}.d{fill:#2f2f2f}.e{fill:#5e5d5d}.f{fill:#a3a2a2}.g{fill:#e3e3e3}.h{fill:#262526}.i{fill:#dddcdc}.j{fill:#efeeef}.k{fill:#202020}</style><path d="M478 523h1 2l-1 1 1 1c-1 0-2 1-4 2v-1l1-3z" class="a"></path><path d="M641 383c1 0 3-2 3-3 0 0 1-2 1-3l-1 8h0v-1h-2l-1-1h0z" class="c"></path><path d="M156 489h1v-2c0 2 0 2 1 4h1l-1 1v3c-1-1-2-4-2-6z" class="X"></path><path d="M564 456l1 1h2l-5 5c0-2 1-4 2-6z" class="H"></path><path d="M570 452v1l-1 1h1l-3 3h-2l2-4c1 0 2-1 3-1z" class="G"></path><path d="M483 521c1 0 2-1 3-1l1-1v1h1v-1l1 1-8 5-1-1 1-1 2-2z" class="i"></path><path d="M300 63v-1c1-4 0-7-1-11l3 3v1 4 3h-1v1h-1z" class="K"></path><path d="M495 145c3 2 6 4 10 6-1 0-1 1-2 1-1-1-3-2-5-3h1 1c-1-1-4-2-5-3v-1z" class="g"></path><path d="M216 267c0 1 1 1 1 1l-4 9c-1-2-1-3-1-4h1c0-1 1-1 1-2l1-1c1-1 1-2 1-3z" class="X"></path><path d="M518 366h0v12 5l3-2v1c0 2-2 3-3 5v-21z" class="C"></path><path d="M202 299c2 1 4 2 6 4l2 2h2v1 1c-4-1-8-3-12-5h3v-1l-2-1 1-1z" class="F"></path><path d="M609 187l2-2c2-2 5-4 7-5-1 3-3 5-5 7-2 1-4 4-6 5l-1-1c0-1 3-3 4-4h-1z" class="R"></path><path d="M69 430c6 0 13 0 19 2-4 1-8 0-11 0h-9c-1 0-1-1-1-1 1 0 1-1 2-1z" class="I"></path><path d="M60 354v4-3h1v2h0v5 1c-1 5-1 11 0 16 0 0-1 1-1 2l-1-1c-1-8-1-18 1-26z" class="B"></path><path d="M644 372l1-1v4 2c0 1-1 3-1 3 0 1-2 3-3 3v-2l-1-1c0-1 1-1 1-2h-2 0c1-1 1-2 2-3l-3 1c2-2 4-3 6-4z" class="M"></path><path d="M644 372l1-1v4s-1 1-1 2h-4l3-3 1-1v-1z" class="f"></path><path d="M91 259l-1 2h1l-7 14c-1 1-2 3-2 5h-1l-1-1c3-7 7-14 11-20z" class="g"></path><path d="M63 407l2 10 2 6c0 1 0 3 1 4l1-1v2 2h0c-1 0-1 1-2 1-1-2-1-5-2-7-1-5-3-11-3-16 1 0 1 0 1-1z" class="R"></path><path d="M103 238l1-1v3l-1 2-12 19h-1l1-2c1-3 3-6 4-8l8-13z" class="j"></path><path d="M623 431c2 0 4-1 6 0s6 0 8 0c-3 1-6 1-9 1-5 0-11 1-16 2-4 1-7 2-11 3-3 1-4 3-7 2 4-2 9-4 13-5l12-3h4z" class="I"></path><defs><linearGradient id="A" x1="593.925" y1="222.807" x2="599.409" y2="220.389" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#6d6d6c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M595 221l1-4-1-1c0-2 2-4 3-7 0-1 0-1 1-2v1c0 2-1 4-1 7-1 3-1 6-1 9 1 1 1 3 1 4s2 4 1 5v2l-1 1v-2c-1-3-2-5-2-7h0c-1-2-1-4-1-6z"></path><path d="M123 447c1 0 2 1 3 1s2 1 4 2l6 6 1 2c1 1 6 7 7 7 1 1 3 4 4 6 0 2 1 3 1 5-4-6-7-12-12-17-4-4-10-7-14-12z" class="S"></path><path d="M521 381c1-1 2-3 4-3 0 2 0 3 1 4l1-1v1h0c-1 1-1 2-1 3-1 1-2 2-4 3-1 1-3 2-3 3h-1v1c-1 1-1 2-1 3l1-8c1-2 3-3 3-5v-1z" class="K"></path><path d="M301 63c0 5-2 10-3 15-1 3-2 7-4 10-1 3-3 6-5 9l-7 7-1-2c3-3 6-6 9-10 3-5 6-11 8-18 1-3 2-7 2-11h1z" class="j"></path><path d="M61 380l3-3h0 1c0 2-2 4-1 5 1 0 1 1 1 1-1 2-2 4-2 6l-1 2v3 1c1 2 1 5 1 8v4c0 1 0 1-1 1l-3-28 1 1c0-1 1-2 1-2v1z" class="c"></path><path d="M61 380l3-3h0 1c0 2-2 4-1 5 1 0 1 1 1 1-1 2-2 4-2 6l-1 2v3c-1-2-1-4-1-6v-8z" class="I"></path><path d="M61 379c-1-5-1-11 0-16v8l1-1c2-2 4-5 6-6v1h0v5c-2 2-4 4-5 6l5-4h0c1 1 1 2 0 3l-1 1v2 1l-3 3c-1-1 1-3 1-5h-1 0l-3 3v-1z" class="T"></path><path d="M62 370c2-2 4-5 6-6v1c-2 4-4 6-6 9h-1v-3l1-1z" class="P"></path><path d="M240 527c3 3 7 6 10 8l12 9-24-11 1-1c1-1 1-4 1-5z" class="j"></path><path d="M464 331l1-2 3-3 1-1h1l2-2v1l-2 12-7 6 1-6v-5z" class="U"></path><path d="M464 331l1-2 3-3 1-1h1l2-2v1c-1 1-2 2-2 3l-4 6c-1 1-1 2-2 3v-5z" class="V"></path><path d="M465 318c2-1 3-3 5-5 1 0 2-1 3-2v-2c0 5 0 9-1 14l-2 2h-1l-1 1-3 3-1 2c0-4 0-9 1-13z" class="D"></path><defs><linearGradient id="C" x1="468.36" y1="364.786" x2="462.314" y2="345.394" xlink:href="#B"><stop offset="0" stop-color="#4e4c4d"></stop><stop offset="1" stop-color="#656565"></stop></linearGradient></defs><path fill="url(#C)" d="M470 336l-1 6-5 27c-1 0-2 1-2 2l1-20v-9l7-6z"></path><path d="M470 336l-1 6-3 3c-1 1-1 2-2 3s-1 2-1 3v-9l7-6z" class="X"></path><defs><linearGradient id="D" x1="205.606" y1="304.667" x2="201.229" y2="290.544" xlink:href="#B"><stop offset="0" stop-color="#959494"></stop><stop offset="1" stop-color="#b5b4b5"></stop></linearGradient></defs><path fill="url(#D)" d="M196 286c3 2 5 5 8 8s6 7 8 11h-2l-2-2c-2-2-4-3-6-4l-1 1c-2-1-5-2-7-4-1-1-2-3-3-5v-2h2 1l2 1c0 1 1 1 2 1h2 0c-1-2-3-3-4-4v-1z"></path><path d="M191 291v-2h2c4 2 5 8 9 10l-1 1c-2-1-5-2-7-4-1-1-2-3-3-5z" class="C"></path><path d="M599 233l17 27 6 12c1 1 2 3 2 5l-2-2v-1c-1 1-1 1-2 3v-1s0-1 1-2v-1-1l-3-6s2 4 2 6c0 1 0 1-1 1h-1c1-1 1-1 1-2 0 0 0-1-1-1 0-1 0-1-1-2 0-2-1-4-3-6v1h0l-3-1v-3-3l-6-9-2-4-1 1-1-1v1h-1-1c0-1-1-2-1-3v-1-2h1l-1-2 1-1v-2z" class="g"></path><path d="M611 256l3 6v1h0l-3-1v-3-3z" class="B"></path><path d="M599 235c1 3 2 5 4 8l-1 1-1-1v1h-1-1c0-1-1-2-1-3v-1-2h1l-1-2 1-1z" class="K"></path><path d="M598 240v-2h1l1 6h-1c0-1-1-2-1-3v-1z" class="M"></path><path d="M86 179c6 4 10 10 16 14l1 1 4 3c1 0 2 1 3 1 0 1 1 1 2 2 1 0 2 1 3 1-1 1-1 2-2 3 0 1 0 1-1 2h0c-1-1-1-2-1-3-1 1-1 1-2 1l-1-1v2l-1 1c-1 0-1-1-1-1h-1l-5-9c-2-4-6-8-9-11-2-2-4-3-5-6z" class="I"></path><path d="M102 193l1 1 4 3c1 0 2 1 3 1 0 1 1 1 2 2 1 0 2 1 3 1-1 1-1 2-2 3 0 1 0 1-1 2h0c-1-1-1-2-1-3-1 1-1 1-2 1l-1-1c-2-3-6-7-6-10z" class="N"></path><path d="M111 203c-1-1-2-1-2-2 1 0 2 0 3 1h0l1 2c0 1 0 1-1 2h0c-1-1-1-2-1-3z" class="K"></path><defs><linearGradient id="E" x1="396.42" y1="61.879" x2="404.368" y2="69.147" xlink:href="#B"><stop offset="0" stop-color="#252425"></stop><stop offset="1" stop-color="#404040"></stop></linearGradient></defs><path fill="url(#E)" d="M400 56c1 0 2 0 2-1 1 0 3-3 3-4 0 3-1 7-1 10s1 6 1 9l-1-1v1c0 1 0 1 1 2-1 0-2 1-2 1h-1 0c-1-1-1-1-2-1-1 1-2 1-2 3h0c0 1 0 1-1 2 0 1 0 2-1 3v-5c0-4 0-8 1-12 1-2 4-5 3-7z"></path><path d="M587 217h1c0 1 0 2 1 3l-1 1h1c1 1 1 2 1 3 1-2 1-2 3-3h0 2c0 2 0 4 1 6h0c0 2 1 4 2 7v2l1 2h-1v2 1h-1l-1-2-2-1v-1c-1 0-1 0-2-1l-1-2c-2 0-3-1-4-2 0-1 0-2-1-3l-1-2c-1-3 1-8 2-10z" class="T"></path><path d="M588 221h1c1 1 1 2 1 3 0 2 1 3 2 5 1 0 1 0 1 1s0 2-1 2v1c-1-2-3-3-4-4-1-3-1-5 0-8z" class="h"></path><path d="M595 221c0 2 0 4 1 6h0c0 2 1 4 2 7v2l1 2h-1v2 1h-1l-1-2-2-1v-1h0c-1-2-1-3-2-4v-1c1 0 1-1 1-2s0-1-1-1c-1-2-2-3-2-5 1-2 1-2 3-3h0 2z" class="X"></path><path d="M590 224c1-2 1-2 3-3v7 2c0-1 0-1-1-1-1-2-2-3-2-5z" class="d"></path><path d="M593 228c1 2 1 3 1 5 1 1 1 2 1 2l1 4-2-1v-1h0c-1-2-1-3-2-4v-1c1 0 1-1 1-2v-2z" class="Q"></path><path d="M595 235v-6c0-1 0-1 1-2 0 2 1 4 2 7v2l1 2h-1v2 1h-1l-1-2-1-4z" class="N"></path><path d="M598 234v2l1 2h-1v2c-1-2-1-4 0-6z" class="U"></path><path d="M75 351l2-2v1 5l-1 13v3h-1c-1 0-2 1-3 2l-1 1v1l-4 3v-2l1-1c1-1 1-2 0-3h0l-5 4c1-2 3-4 5-6v-5l2-2v-2c-1 1-1 1-2 1v-1c1-2 1-4 3-6 1-2 2-3 4-4z" class="B"></path><path d="M75 351l2-2v1 5c-1 1-2 1-4 1h0l1-4 1-1z" class="D"></path><path d="M71 355c1-2 2-3 4-4l-1 1c-2 2-3 3-3 7 1 0 1 0 2-1v1c0 2-1 4-2 6 0 2-1 2-1 4h1l1 1h0c0 1-1 2-1 3v1 1l-4 3v-2l1-1c1-1 1-2 0-3h0l-5 4c1-2 3-4 5-6v-5l2-2v-2c-1 1-1 1-2 1v-1c1-2 1-4 3-6z" class="U"></path><path d="M68 361c1-2 1-4 3-6v4h1c0 1 0 1-1 2 0 0 0 1-1 2v-2c-1 1-1 1-2 1v-1z" class="N"></path><path d="M471 516h1v1 1h2c1 1 1 2 1 4v3c-1 1-1 3-1 4l-30 15c4-4 9-7 13-11 6-4 10-11 14-17z" class="j"></path><path d="M644 359l1 9v3l-1 1c-2 1-4 2-6 4l-5 4-4 4v-5h0v-7-2c2-1 4-3 6-4 1-1 2-1 3-2 2-1 4-3 6-3v-2z" class="g"></path><path d="M644 359l1 9-1 1h0c-2 0-3 1-4 2h0c-3 2-7 4-9 6l-1 1-1-1v-2l9-6c2-1 4-3 6-3h0c1-2 0-4 0-5v-2z" class="L"></path><path d="M640 371h0c1-1 2-2 4-2h0l1-1v3l-1 1c-2 1-4 2-6 4l-5 4-4 4v-5h0v-7 3 2l1 1 1-1c2-2 6-4 9-6z" class="O"></path><path d="M640 371v1c-1 1-2 2-4 3 0 1-1 1-2 1-1 1-2 3-3 3 0 1-1 1 0 2h0l2-1-4 4v-5h0v-7 3 2l1 1 1-1c2-2 6-4 9-6z" class="f"></path><defs><linearGradient id="F" x1="630.623" y1="389.124" x2="639.041" y2="392.323" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#F)" d="M638 376l3-1c-1 1-1 2-2 3h0 2c0 1-1 1-1 2l1 1v2h0l1 1h2v1c0 2-1 3-3 4h0c-1 2-3 3-4 5-2 1-4 2-5 3l-2 2c-1 0-1-1-1-1l-1-1v-1-3l1-11v-3h0v5l4-4 5-4z"></path><path d="M642 384h2v1c0 2-1 3-3 4h0 0l1-5z" class="F"></path><path d="M630 389c1-2 3-4 5-4h0c1 1 2 1 3 1l-5 3h-3z" class="X"></path><path d="M638 376l3-1c-1 1-1 2-2 3h0 2c0 1-1 1-1 2l1 1v2h0l-3 3c-1 0-2 0-3-1h0c-2 0-4 2-5 4s1 4-1 6c0 1 0 2 1 3 0 0 1 0 2-1h0l-2 2c-1 0-1-1-1-1l-1-1v-1-3l1-11v-3h0v5l4-4 5-4z" class="F"></path><defs><linearGradient id="G" x1="626.335" y1="389.108" x2="639.671" y2="385.191" xlink:href="#B"><stop offset="0" stop-color="#504e4f"></stop><stop offset="1" stop-color="#767677"></stop></linearGradient></defs><path fill="url(#G)" d="M629 382v3h1c3-1 6-5 9-6 0 2-2 4-3 5 0 0-1 0-1 1-2 0-4 2-5 4s1 4-1 6c0 1 0 2 1 3 0 0 1 0 2-1h0l-2 2c-1 0-1-1-1-1l-1-1v-1-3l1-11z"></path><path d="M387 483h0v76 25 13c0 2 1 5 2 7l-5 5h-2c-1-2-1-5-1-8l-1-1v-14c1 0 1-3 1-3 0-1 2-1 3-1 1-1 2-3 3-4-1-3 0-6 0-9v-6-13-21-10-36z" class="Q"></path><path d="M387 578c0 2 0 5-1 7v1h0c1 2 1 6 1 8-2 1-3 3-4 4s-1 3-2 3l-1-1v-14c1 0 1-3 1-3 0-1 2-1 3-1 1-1 2-3 3-4z" class="J"></path><path d="M86 179l-4-2c16 6 33 15 51 12l-2 2h-6v1h-2c-1 1-1 1-2 1h0c-1 0-2 0-3 1h0-1-2c-1 1-2 0-3 0s-1-1-2-1h-2-1l-1 1 1 1v2l-4-3-1-1c-6-4-10-10-16-14z" class="a"></path><path d="M103 194h1 0c0-1 0-1-1-2v-1h0s1 0 1-1l2-2 1 1v1h1c2 0 4 1 7 1 3 1 7 0 10 0v1h-2c-1 1-1 1-2 1h0c-1 0-2 0-3 1h0-1-2c-1 1-2 0-3 0s-1-1-2-1h-2-1l-1 1 1 1v2l-4-3z" class="L"></path><path d="M535 480c2-2 3-2 5-2 2 1 3 2 3 4v2h1l1 1v1h0c0 1 1 1 1 2v1h0c1-1 1-1 2-1-1 3-2 5-3 9-3 0-12 0-15 2h0l-1-1c1-2 2-3 2-5 1-5 3-9 4-13z" class="g"></path><path d="M228 511l9 12c0 1 3 3 3 4s0 4-1 5l-1 1c-3 0-4-2-6-3l-6-3c-3-1-5-3-8-4 0-1 1-2 1-3l7-7 1-1 1-1z" class="L"></path><path d="M237 523c0 1 3 3 3 4s0 4-1 5c-1-1-2-1-3-2s-1-1-1-2l1-2c0-1 0-2 1-3z" class="O"></path><path d="M76 368v11l1 8h0-2v1c-1 1-2 2-4 3-2 2-7 8-8 12 0-3 0-6-1-8v-1-3l1-2c0-2 1-4 2-6 0 0 0-1-1-1l3-3v-1l4-3v-1l1-1c1-1 2-2 3-2h1v-3z" class="d"></path><defs><linearGradient id="H" x1="67.373" y1="388.123" x2="66.648" y2="379.666" xlink:href="#B"><stop offset="0" stop-color="#525153"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#H)" d="M69 379h1 0c1-1 2-1 4-1-4 4-9 9-12 13l1-2c0-2 1-4 2-6 0 0 0-1-1-1l3-3v1l2-1z"></path><path d="M67 379v1l-2 3s0-1-1-1l3-3z" class="Q"></path><path d="M76 368v11h0v-1h-1-1c-2 0-3 0-4 1h0-1l-2 1v-1-1l4-3v-1l1-1c1-1 2-2 3-2h1v-3z" class="N"></path><path d="M71 375h1c0 1-2 3-3 4l-2 1v-1-1l4-3z" class="b"></path><defs><linearGradient id="I" x1="60.447" y1="393.997" x2="78.003" y2="385.899" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#I)" d="M76 379h0l1 8h0-2v1c-1 1-2 2-4 3-2 2-7 8-8 12 0-3 0-6-1-8h1c3-5 6-9 10-13 1-1 2-3 3-3z"></path><path d="M73 382c0 3-2 5-4 7-1 2-2 4-4 5h-1l-1 1c3-5 6-9 10-13z" class="b"></path><path d="M387 529v21 13 6c0 3-1 6 0 9-1 1-2 3-3 4-1 0-3 0-3 1 0 0 0 3-1 3v14-9-17-24l1-1c1-3 3-4 5-6h0c1-4 0-9 1-13v-1z" class="G"></path><path d="M387 550v13c-2 1-4 3-7 4l1-7v-1c1-2 4-4 5-6 0-1 0-2 1-3z" class="H"></path><path d="M380 567c3-1 5-3 7-4v6c0 3-1 6 0 9-1 1-2 3-3 4-1 0-3 0-3 1 0 0 0 3-1 3v-8-11z" class="N"></path><path d="M380 567c3-1 5-3 7-4v6c-2 2-3 4-6 4 0 1 0 3-1 5v-11z" class="E"></path><path d="M408 635c1 0 1 0 2-1 0-2 1-4 2-5h3l10 3c7 2 15 4 22 5v5h-5c-3-1-7-1-10-1l-18-3-5-1v-1l-1-1z" class="j"></path><path d="M408 635c1 0 1 0 2-1 0-2 1-4 2-5h3l10 3c-2 0-5-1-7 1l-2 1c0 1-2 2-2 4l-5-1v-1l-1-1z" class="a"></path><path d="M125 191h6l-1 2h0c1 1 1 1 2 1 0 1 1 1 1 2 1 2 3 5 3 7l-2 1h0-2 0l-1 1h-4-2c-4-1-6-2-9-3l-1-1c-1 0-2-1-3-1-1-1-2-1-2-2-1 0-2-1-3-1v-2l-1-1 1-1h1 2c1 0 1 1 2 1s2 1 3 0h2 1 0c1-1 2-1 3-1h0c1 0 1 0 2-1h2v-1z" class="f"></path><path d="M118 197h1c3-1 4-2 6-4 0 1-1 2-1 3h0c0 1-1 2-2 3h-1v-1c-2 0-3 0-4 1 0 1-1 1-1 1l-1-1c1 0 1-1 1-1h1l-1-1c-2 0-3 1-4 0 1 0 3 0 5-1h0c1 1 1 1 1 2v-1z" class="M"></path><path d="M120 201c0 1-1 1-2 1s-6-4-7-5h1c1 1 2 0 4 0l1 1h-1s0 1-1 1l1 1s1 0 1-1c1-1 2-1 4-1v1h1l-2 2z" class="E"></path><path d="M107 197v-2l-1-1 1-1h1 2c1 0 1 1 2 1s2 1 3 0h2 1 0c1-1 2-1 3-1h0c1 0 1 0 2-1h2c-2 3-5 3-7 5v1c0-1 0-1-1-2h0c-2 1-4 1-5 1h-1c1 1 6 5 7 5s2 0 2-1l2 2 1 1 2 1c-4-1-6-2-9-3l-1-1c-1 0-2-1-3-1-1-1-2-1-2-2-1 0-2-1-3-1z" class="G"></path><defs><linearGradient id="J" x1="128.512" y1="193.8" x2="124.702" y2="203.296" xlink:href="#B"><stop offset="0" stop-color="#81807f"></stop><stop offset="1" stop-color="#9a999a"></stop></linearGradient></defs><path fill="url(#J)" d="M122 203c1-2 3-4 4-5 1-2 2-4 4-5h0c1 1 1 1 2 1 0 1 1 1 1 2 1 2 3 5 3 7l-2 1h0-2 0l-1 1h-4-2l-2-1-1-1z"></path><path d="M128 200c0-1 1-2 2-2h1v2h1c1 0 2 3 2 4h0-2 0l-1 1h-4-2l-2-1c2-1 3-3 5-4z" class="G"></path><path d="M132 204h-2v-1c1-1 1-1 2 0 1 0 1 0 0 1h0z" class="V"></path><path d="M128 200s1 0 1 1 0 1-1 2v1l-1 1h-2l-2-1c2-1 3-3 5-4z" class="D"></path><path d="M315 257c2 0 8 0 9 1l1 1h-1c-2 2-4 5-7 6-1-1-3-1-5-1h-20l-12 2 1-1 1-2c1-2 2-3 3-4l2-1h5l23-1z" class="S"></path><path d="M213 504l2 2h1c1 1 3 2 5 2l6 4-1 1-7 7c0 1-1 2-1 3 3 1 5 3 8 4l6 3h-3c-1 0-1-1-2-1-4-1-7-3-10-5-5-3-10-5-14-9h-1 3l1-2c1-2 3-5 4-7 1-1 2-1 3-2z" class="Q"></path><path d="M213 512h1v1c0 1 1 2 1 4-1 1-1 1-1 3v-2c0-1 0-1-1-1h0-3c1-2 2-3 3-5z" class="E"></path><path d="M213 517h0c1 0 1 0 1 1v2c2 0 2-1 4 0h1c0 1-1 2-1 3l-6-3-2-2v-1h3z" class="H"></path><path d="M210 517h3v2l-1 1-2-2v-1z" class="B"></path><path d="M213 504l2 2h1v1h-4c-1 2-2 3-2 5-1 1-1 1-1 2s-1 2-1 2c-2 0-4 0-5-1h-1 3l1-2c1-2 3-5 4-7 1-1 2-1 3-2z" class="X"></path><path d="M216 506c1 1 3 2 5 2l6 4-1 1-7 7h-1c-2-1-2 0-4 0 0-2 0-2 1-3 0-2-1-3-1-4v-1h-1c1-2 2-3 3-5v-1z" class="T"></path><path d="M221 508l6 4-1 1-7 7h-1c-2-1-2 0-4 0 0-2 0-2 1-3h0l2 1h1l1-2c2-3 2-4 2-8z" class="F"></path><defs><linearGradient id="K" x1="107.423" y1="204.239" x2="110.785" y2="240.415" xlink:href="#B"><stop offset="0" stop-color="#525152"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#K)" d="M108 203l1 1c3 5 5 18 4 24 0 1-1 2-1 3v4 1c-1 1-1 2-1 4v2l-1 4-1 2c-1 0-2 3-2 3l-3 3v-5-3l-1 1v-5l1-2v-3l-1 1 3-6c2-6 2-14 1-20l-2-7h1s0 1 1 1l1-1v-2z"></path><path d="M111 242c-1-6-1-11 1-17h1c0 2-1 4-1 5v1 4 1c-1 1-1 2-1 4v2z" class="e"></path><path d="M107 212v-1c2 2 2 5 2 7 0 8-1 15-2 22v1c1 0 1 0 2 1h0v6c-1 0-2 3-2 3l-3 3v-5-3l-1 1v-5l1-2v-3l-1 1 3-6c2-6 2-14 1-20z" class="H"></path><path d="M104 240l2-4h0c0 2-1 4-1 7 0 2 0 4-1 6v-3l-1 1v-5l1-2z" class="K"></path><path d="M107 240v1c1 0 1 0 2 1h0v6c-1 0-2 3-2 3-2-3-1-8 0-11z" class="F"></path><defs><linearGradient id="L" x1="589.931" y1="197.525" x2="596.56" y2="209.745" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#828282"></stop></linearGradient></defs><path fill="url(#L)" d="M609 187h1c-1 1-4 3-4 4l1 1v1h0c0 1-1 2-2 3l-5 9-1 2c-1 1-1 1-1 2-1 3-3 5-3 7l1 1-1 4h-2 0c-2 1-2 1-3 3 0-1 0-2-1-3h-1l1-1c-1-1-1-2-1-3h-1c1-2 2-3 1-5h0l-2-1h0c0-1-1-2-2-2s-2-1-3-1l-5-1-2-1-1-1h1c4 0 8-1 12-3 6-2 11-5 16-9l1-1 6-5z"></path><path d="M584 209c1 0 1 0 2 1v-1l1-2h0-1 1c1 0 1 0 1 1 2 0 2-1 3-2h0l1-1h1c-1 1-4 5-6 6h-1c0-1-1-2-2-2z" class="J"></path><path d="M593 208h0v-1l1-1c1-2 3-3 5-3 0 0 1 1 1 2l-1 2v-2h0c-4 0-4 1-6 3z" class="T"></path><path d="M593 208c2-2 2-3 6-3h0v2c-1 1-1 1-1 2-1 3-3 5-3 7l1 1-1 4h-2 0c-2 1-2 1-3 3 0-1 0-2-1-3h-1l1-1c-1-1-1-2-1-3l1-1c1-3 2-5 4-8h0z" class="U"></path><path d="M589 220c0-1 1-2 2-2h1c0 1 0 2-1 2 1 1 1 1 2 1h0c-2 1-2 1-3 3 0-1 0-2-1-3h-1l1-1z" class="R"></path><path d="M593 208h1v2l-3 6h-2c1-3 2-5 4-8z" class="H"></path><path d="M424 103c7 7 15 13 23 18 2 1 7 4 7 4-1 1-1 1-1 2-1 0-3 1-3 2l-1-1-2 2-1 1v1 1 1l-9-6v-1l-10-8c-2 0-6-4-7-6 0-1-1-3-1-4s1-2 2-2c1-1 2-1 2-2l2 2v-1c0-1-1-2-1-3z" class="L"></path><path d="M437 127l8 4c1 0 2-1 2-1l-1 1v1 1 1l-9-6v-1z" class="K"></path><path d="M423 105l2 2 1 2s-1 0-1 1c-2 0-2 1-3 2 0 1 1 2 1 3 1 1 3 2 4 4h0c-2 0-6-4-7-6 0-1-1-3-1-4s1-2 2-2c1-1 2-1 2-2z" class="B"></path><defs><linearGradient id="M" x1="89.539" y1="248.217" x2="97.127" y2="271.782" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#M)" d="M110 246h1l2-2c1-2 2-3 3-4 1 0 2-1 2-1-2 3-6 7-7 11h0l-2 4v1c0 1-1 2-1 3-1 1-1 2-2 3-4 3-6 10-10 12 0 0 0 1-1 1l-2 2c-1 1-3 4-5 5l-1-1-2 1c-1-1-1-1 0-2-1-1-1-1-2-1l-1 3v-1c0-2 1-4 2-5l7-14 12-19v5l1-1v3 5l3-3s1-3 2-3l1-2z"></path><path d="M84 275h1c2 1 4-1 6-3 1 0 2-1 3-1-2 3-4 6-7 9l-2 1c-1-1-1-1 0-2-1-1-1-1-2-1l-1 3v-1c0-2 1-4 2-5z" class="E"></path><path d="M110 246h1l2-2c1-2 2-3 3-4 1 0 2-1 2-1-2 3-6 7-7 11h0l-2 4c0 1-2 2-2 3l-5 3h-1v-3c1-1 1-1 1-3v-2c0-1 1-2 1-3v-2l1-1v3 5l3-3s1-3 2-3l1-2z" class="b"></path><path d="M103 247l1-1v3 5c0 1-1 2-1 3h1l-1 1h-1c1-3 1-6 1-9v-2z" class="I"></path><path d="M110 246h1l2-2c1-2 2-3 3-4 1 0 2-1 2-1-2 3-6 7-7 11h0c-3 2-5 5-7 7h-1c0-1 1-2 1-3l3-3s1-3 2-3l1-2z" class="C"></path><defs><linearGradient id="N" x1="101.58" y1="259.049" x2="97.033" y2="274.208" xlink:href="#B"><stop offset="0" stop-color="#81807f"></stop><stop offset="1" stop-color="#a6a6a7"></stop></linearGradient></defs><path fill="url(#N)" d="M109 254v1c0 1-1 2-1 3-1 1-1 2-2 3-4 3-6 10-10 12 0 0 0 1-1 1l-2 2c-1 1-3 4-5 5l-1-1c3-3 5-6 7-9s4-7 7-9c1-2 3-2 4-3 1 0 2-1 2-2s2-2 2-3z"></path><path d="M573 190c13 0 24-2 36-8 5-2 9-4 14-6l-5 4c-2 1-5 3-7 5l-2 2-6 5-1 1c-5 4-10 7-16 9-4 2-8 3-12 3h0c-1-1-2 0-3-1v-1c1-2 1-4 2-6l1-1v-5l-1-1h0z" class="Y"></path><path d="M574 205c0-1 0-1 1-1s2 1 4 0c0-1 1-1 2-2 1 0 2-1 4-2 3-2 7-4 10-6 1-1 2-4 4-4h0l-2 2 1 1 2-2h0l2-2h1l-2 2c-1 2-5 4-7 6 1 0 2 0 2-1 1 0 1-1 2-1h0v-1h1c1-1 1-1 2-1v-1l1 1c-5 4-10 7-16 9-4 2-8 3-12 3h0z" class="G"></path><path d="M601 191c-1 2-5 4-7 6 1 0 2 0 2-1 1 0 1-1 2-1h0v-1h1c1-1 1-1 2-1v-1l1 1c-5 4-10 7-16 9h-1c0-1 1-1 2-2l6-3c3-2 5-4 8-6z" class="a"></path><defs><linearGradient id="O" x1="398.806" y1="86.843" x2="407.378" y2="84.076" xlink:href="#B"><stop offset="0" stop-color="#323133"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#O)" d="M405 72c-1-1-1-1-1-2v-1l1 1 1 2c1 3 2 7 3 10 1 1 3 5 3 6 3 5 8 11 12 15h0c0 1 1 2 1 3v1l-2-2c0 1-1 1-2 2-1 0-2 1-2 2s1 3 1 4l-5-4-4-4c-1-1-3-3-4-5h-1l-1-4h-1c1 1 1 2 1 3v1 1c-1-2-3-5-4-8-2-4-4-8-5-13 1-1 1-2 1-3 1-1 1-1 1-2h0c0-2 1-2 2-3 1 0 1 0 2 1h0 1s1-1 2-1z"></path><path d="M405 72c-1-1-1-1-1-2v-1l1 1 1 2h-1c0 2 0 3 1 4h-2l-1-1c0-1 1-1 2-2v-1z" class="b"></path><path d="M406 76c-1-1-1-2-1-4h1c1 3 2 7 3 10 1 1 3 5 3 6 3 5 8 11 12 15h0c0 1 1 2 1 3v1l-2-2c0 1-1 1-2 2-1 0-2 1-2 2s1 3 1 4l-5-4v-4l-2-2c-1 0-3-2-3-4v-3c-1-1-1-2-1-4h-2l-2-3c0-3 1-4 2-7 0-2-1-4-1-6z" class="M"></path><path d="M415 95c1 1 2 3 2 4h0c3 2 5 4 6 6 0 1-1 1-2 2v-1c-1-1-1 1-2 1h-2v-1c1-1 2-2 2-3h0v-1l-1-1h-2l-1-1v-5z" class="H"></path><path d="M415 95h0c-2-2-4-5-4-8l1 1c3 5 8 11 12 15h0c0 1 1 2 1 3v1l-2-2c-1-2-3-4-6-6h0c0-1-1-3-2-4z" class="a"></path><defs><linearGradient id="P" x1="550.037" y1="454.75" x2="553.292" y2="457.378" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#P)" d="M562 439h1l-6 7h1v3c0-1 1-1 2 0h0v3c0 3-1 6-2 9 0 1-1 1-1 3h0c1 1 2 1 2 2-5 7-8 14-11 22-1 0-1 0-2 1h0v-1c0-1-1-1-1-2h0v-1l-1-1h-1v-2c0-2-1-3-3-4-2 0-3 0-5 2l1-3 2-5v-2c2-3 3-6 5-8l3-5 1-1 2-3 1-2 5-5 2-3 5-4z"></path><path d="M562 439h1l-6 7c-4 4-9 10-12 15l-2 2v-1l3-5 1-1 2-3 1-2 5-5 2-3 5-4z" class="i"></path><defs><linearGradient id="Q" x1="543.615" y1="470.461" x2="546.975" y2="476.803" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#d0cfcf"></stop></linearGradient></defs><path fill="url(#Q)" d="M547 468c0-2 2-5 3-7 0-2 1-3 2-5v4h0v1c-1 1-1 1-1 2 0 3-1 5-2 7-1 1-1 3-1 4-1 3-2 5-3 7v-2-1-1h-2v-1l-1-1c1-2 1-4 2-5h1c2-1 2-1 2-2z"></path><defs><linearGradient id="R" x1="545.171" y1="469.929" x2="540.118" y2="477.03" xlink:href="#B"><stop offset="0" stop-color="#b0b0af"></stop><stop offset="1" stop-color="#d2d0d1"></stop></linearGradient></defs><path fill="url(#R)" d="M543 463l2-2c0 2 0 6 1 7h1c0 1 0 1-2 2h-1c-1 1-1 3-2 5l1 1v1h2v1 1 2 4l-1-1h-1v-2c0-2-1-3-3-4-2 0-3 0-5 2l1-3 2-5v-2c2-3 3-6 5-8v1z"></path><path d="M538 470l2-1c0 2-1 3 0 3 1 2 1 2 0 3 0 1 0 2-1 2-1 1-2 1-3 0l2-5v-2z" class="D"></path><path d="M543 463l2-2c0 2 0 6 1 7h1c0 1 0 1-2 2h-1v-2h-1c-1 1-2 3-3 4-1 0 0-1 0-3l-2 1c2-3 3-6 5-8v1z" class="B"></path><path d="M543 463l2-2c0 2 0 6 1 7 0 1 0 1-2 1v-2c0-1-1 0-2-2l1-2z" class="M"></path><defs><linearGradient id="S" x1="558.252" y1="462.154" x2="541.818" y2="486.659" xlink:href="#B"><stop offset="0" stop-color="#bebdbe"></stop><stop offset="1" stop-color="#f2f1f2"></stop></linearGradient></defs><path fill="url(#S)" d="M558 449c0-1 1-1 2 0h0v3c0 3-1 6-2 9 0 1-1 1-1 3h0c1 1 2 1 2 2-5 7-8 14-11 22-1 0-1 0-2 1h0v-1c0-1-1-1-1-2h0v-1-4c1-2 2-4 3-7 0-1 0-3 1-4 1-2 2-4 2-7 1-1 2-3 3-4v-2l1-2c0-2 1-4 3-6z"></path><defs><linearGradient id="T" x1="553.718" y1="453.313" x2="559.836" y2="460.18" xlink:href="#B"><stop offset="0" stop-color="#929292"></stop><stop offset="1" stop-color="#bbb8b9"></stop></linearGradient></defs><path fill="url(#T)" d="M558 449c0-1 1-1 2 0h0v3c0 3-1 6-2 9 0 1-1 1-1 3h0 0l-3 1v-1c-1 0 2-7 1-9l-1 2 1-2c0-2 1-4 3-6z"></path><path d="M644 385h0v6c0 7-1 16-3 23v4l-2 9c-1 2-1 3-2 4h0c-2 0-6 1-8 0s-4 0-6 0h-4-1l1-2c2-2 5-3 5-6h1c0-1 1-1 1-2h1c-1-1-2-1-2-2 1-1 1-2 1-3v-1h0l2 1-1-3c-1-1-1-2-1-4h0v-5h-1c1-2 1-5 2-7 0-1 0-3 1-4v3 1l1 1s0 1 1 1l2-2c1-1 3-2 5-3 1-2 3-3 4-5h0c2-1 3-2 3-4z" class="W"></path><path d="M628 393v3 1l1 1s0 1 1 1c-1 1-1 1-2 1-1-1-1-2-1-3s0-3 1-4z" class="K"></path><path d="M634 410h3 1c0 1-1 2-2 3h-1l-2-2 1-1z" class="h"></path><path d="M634 410c0-1-1-2 0-3 1 0 1-1 2 0 1 0 1 0 2 1 0 1 0 1-1 2h-3z" class="D"></path><path d="M644 385h0v6c0 1 0 2-1 2l-4 4h-1c-1 0-1-2-1-2 0-1 3-2 4-4-1 1-2 2-4 3h0c1-2 3-3 4-5h0c2-1 3-2 3-4z" class="X"></path><path d="M644 391c0 7-1 16-3 23h-1v-4c0-2 0-3 1-5 0-1 0-2 1-4 0-1 0-1-1-2h-1 0-2c-1 0-2 0-2 1v-1l2-1v-1h1l4-4c1 0 1-1 1-2zm-11 30h0c1 0 2 1 2 0l1-1v-1-2h2v-3 5-1l1 1h0l1-2 1 1-2 9c-1 2-1 3-2 4h0c-2 0-6 1-8 0s-4 0-6 0c4-1 8 0 12-1h1c0-2 0-3-1-4l-6-9c2 1 2 2 3 4h1z" class="K"></path><path d="M629 417c2 1 2 2 3 4h1c1 1 1 1 2 1 1-1 1-1 1-2 1-1 1-2 1-2 0 2-1 11-1 12 0-2 0-3-1-4l-6-9z" class="J"></path><path d="M625 419c1-1 1-2 1-3v-1h0l2 1 1 1 6 9c1 1 1 2 1 4h-1c-4 1-8 0-12 1h-4-1l1-2c2-2 5-3 5-6h1c0-1 1-1 1-2h1c-1-1-2-1-2-2z" class="D"></path><path d="M625 419c2 0 3 1 4 2v1h-1c-1 0-1 0-1-1-1-1-2-1-2-2z" class="Y"></path><path d="M635 430l-4-1v-1s1 0 1-1h0c1 0 2 0 3-1h0c1 1 1 2 1 4h-1z" class="i"></path><path d="M592 423l4-1c-1 3-3 4-3 7s-2 7-3 10v1h1l-6 3c-2 1-5 3-7 5l-8 6h-1l1-1v-1c-1 0-2 1-3 1l-2 4-1-1c-1 2-2 4-2 6l-3 4c0-1-1-1-2-2h0c0-2 1-2 1-3 1-3 2-6 2-9v-3h0c-1-1-2-1-2 0v-3h-1l6-7h-1c1-1 4-3 5-4v1s1-1 2-1h1c1-1 2-2 3-2l10-5c2-2 6-3 9-5z" class="F"></path><path d="M568 440c1 1 2 1 2 2l1 1v2c0 1-1 2-2 2-1 1-2 1-3 0l-2-2c0-1 0-2 1-3v1c1-2 1-2 3-3z" class="h"></path><path d="M568 440c1 1 2 1 2 2l1 1c-1 0-2 1-3 1-1 1-2 0-3-1 1-2 1-2 3-3z" class="L"></path><path d="M573 433h1v6c-1 1-1 4-2 6h-1v-2l-1-1c0-1-1-1-2-2h-1c1-1 2-2 2-3s0-1 1-2 2-2 3-2z" class="F"></path><path d="M570 435h1v1h2v2l-2 2h0v-3h-2c0-1 0-1 1-2z" class="T"></path><path d="M569 437h2v3h0c1 2 1 3 1 5h-1v-2l-1-1c0-1-1-1-2-2h-1c1-1 2-2 2-3z" class="e"></path><path d="M560 452c1-2 1-4 1-6 1-2 2-3 2-4v4h0c1 1 1 1 1 2h2c1 1 1 3 1 4h0v1l-2 4-1-1c-1 2-2 4-2 6l-3 4c0-1-1-1-2-2h0c0-2 1-2 1-3 1-3 2-6 2-9z" class="G"></path><path d="M563 446h0c1 1 1 1 1 2h2c1 1 1 3 1 4h0v1l-2 4-1-1 2-6c-1-1-2-2-3-2l-1 4c0-2 0-4 1-6z" class="E"></path><defs><linearGradient id="U" x1="558.28" y1="447.955" x2="562.719" y2="457.529" xlink:href="#B"><stop offset="0" stop-color="#6f716e"></stop><stop offset="1" stop-color="#9c9a9b"></stop></linearGradient></defs><path fill="url(#U)" d="M560 452c1-2 1-4 1-6 1-2 2-3 2-4v4c-1 2-1 4-1 6 0 3-1 6-2 8-1 1-1 1-2 1 1-3 2-6 2-9z"></path><defs><linearGradient id="V" x1="571.546" y1="436.586" x2="580.867" y2="443.718" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#b9b8b8"></stop></linearGradient></defs><path fill="url(#V)" d="M573 433l10-5c-1 2-3 4-4 6v4 6 1h1 0c-1 1-2 1-2 3l-8 6h-1l1-1v-1c-1 0-2 1-3 1v-1l2-3h0l2-1c0-1 0-2 1-3 1-2 1-5 2-6v-6h-1z"></path><path d="M569 449h0c1 0 1 0 2-1l2-1c-1 2-2 3-3 5-1 0-2 1-3 1v-1l2-3z" class="H"></path><path d="M572 445c1-2 1-5 2-6l1 2-1 1c0 2-1 4-1 5l-2 1c-1 1-1 1-2 1h0 0l2-1c0-1 0-2 1-3z" class="E"></path><defs><linearGradient id="W" x1="580.237" y1="430.73" x2="588.465" y2="436.83" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#b4b3b4"></stop></linearGradient></defs><path fill="url(#W)" d="M592 423l4-1c-1 3-3 4-3 7s-2 7-3 10v1h1l-6 3c-2 1-5 3-7 5 0-2 1-2 2-3h0-1v-1-6-4c1-2 3-4 4-6 2-2 6-3 9-5z"></path><defs><linearGradient id="X" x1="587.844" y1="426.686" x2="593.573" y2="435.578" xlink:href="#B"><stop offset="0" stop-color="#717272"></stop><stop offset="1" stop-color="#908d8e"></stop></linearGradient></defs><path fill="url(#X)" d="M592 423l4-1c-1 3-3 4-3 7s-2 7-3 10v1h1l-6 3c4-6 4-13 7-20z"></path><path d="M138 203l1-2h1v2h0l2 5c0 2 0 4 1 6-3 0-5-3-7-3h-2 0-1-1c-1-1-2 0-2 0h-2v1c-1 1-1 1-1 2v2h-1c0 1 1 1 1 2 0 2 1 3 0 5 0 1 1 3 0 4h0c-1 1-1 3-1 4-1 1-2 1-2 2h-1 0-1l-4 6s-1 1-2 1c-1 1-2 2-3 4l-2 2h-1l1-4v-2c0-2 0-3 1-4v-1-4c0-1 1-2 1-3 1-6-1-19-4-24 1 0 1 0 2-1 0 1 0 2 1 3h0c1-1 1-1 1-2 1-1 1-2 2-3l1 1c3 1 5 2 9 3h2 4l1-1h0 2 0l3-1h1 0z" class="k"></path><path d="M124 220c-1-2-2-5-2-7l1-1c1 0 2 1 2 0h3c-1 1-1 1-1 2l-1-1s-1 0-2 1v6z" class="S"></path><path d="M138 203l1-2h1v2h0l2 5c-2 1-2 1-4 1l2-1-1-1h-2 0c1-1 2-2 2-4h-1 0z" class="d"></path><path d="M124 220v-6c1-1 2-1 2-1l1 1v2h-1c0 1 1 1 1 2 0 2 1 3 0 5 0 1 1 3 0 4h0l-3-7z" class="C"></path><path d="M142 208c0 2 0 4 1 6-3 0-5-3-7-3h-2 0-1-1c-1-1-2 0-2 0h-2v-1h0c3-1 4-1 7-1 1 0 2-1 3 0 2 0 2 0 4-1z" class="S"></path><path d="M134 204l3-1h1 1c0 2-1 3-2 4s-2 0-4 0h-1c-3 2-10 1-12 5l-1-1-1-1c1-1 2-1 3-2 3-1 7-2 10-3l1-1h0 2 0z" class="C"></path><path fill="#121212" d="M138 203h1c0 2-1 3-2 4s-2 0-4 0h-1l2-1c2-1 3-1 4-3z"></path><path d="M115 201l1 1c3 1 5 2 9 3h2 4c-3 1-7 2-10 3-1 1-2 1-3 2l1 1 1 1c1 3 2 8 1 12 0 4-1 7-4 10-1 1-3 1-5 2v-1-4c0-1 1-2 1-3 1-6-1-19-4-24 1 0 1 0 2-1 0 1 0 2 1 3h0c1-1 1-1 1-2 1-1 1-2 2-3z" class="X"></path><path d="M118 224h1c0 2 0 4-1 5 0 1-1 1-1 1h-1c1-2 1-4 2-6z" class="N"></path><path d="M112 206c1 0 2 1 3 1v3c-1 1-1 1-1 2-1-1-2-4-2-6h0z" class="Q"></path><path d="M116 218c0-1-1-1-2-2l1-1v-3l1-1c1-1 1-1 2-1-1 2-1 4-2 6v2z" class="c"></path><path d="M115 201l1 1c0 1-1 2 0 3h0 2v1 1l1 1h-3l-1 2v-3c-1 0-2-1-3-1 1-1 1-1 1-2 1-1 1-2 2-3z" class="J"></path><path d="M116 219l1-1v1c1 1 0 4 1 5-1 2-1 4-2 6l-3 3 3-14z" class="S"></path><defs><linearGradient id="Y" x1="121.896" y1="206.667" x2="120.547" y2="201.801" xlink:href="#B"><stop offset="0" stop-color="#626061"></stop><stop offset="1" stop-color="#777776"></stop></linearGradient></defs><path fill="url(#Y)" d="M116 202c3 1 5 2 9 3h2 4c-3 1-7 2-10 3l-1-1-1 1-1-1v-1-1h-2 0c-1-1 0-2 0-3z"></path><path d="M118 205c2 1 3 1 4 1 0 1-1 1-2 1l-1 1-1-1v-1-1z" class="R"></path><path d="M118 210h0l1 1 1 1c1 3 2 8 1 12 0 0-1 0-1-1-1-1-1-3-2-5l-1 1v-1l-1 1v-1-2c1-2 1-4 2-6z" class="P"></path><path d="M640 322l1 9c2 9 4 19 3 28v2c-2 0-4 2-6 3-1 1-2 1-3 2-2 1-4 3-6 4v-1-4-8l-4-26-2-7c1 0 1 0 2-1l2-1v1c1 0 1 0 1-1l-2 4c-1 0-1 1 0 2 1 0 2-1 3-1 1-1 3-2 5-2 1-1 3-2 4-2 1-1 1-1 2-1z" class="j"></path><path d="M636 341c1 1 2 1 2 2v2c-1 1-2 2-3 2-2 0-2-1-3-1 0-1 0-1-1-2 0-1 0-1 1-2s1 1 3 1c1 0 1-1 1-2z" class="K"></path><path d="M629 357v3c2-1 3-2 4-3l9-5h0c0 2-2 4-3 5-3 2-8 4-10 7 0 2 1 3 0 5v-4-8z" class="Y"></path><path d="M640 322l1 9c-1-1-1-2-1-3v-1c-1-1 0-1 0-2h-1v-2c-1 0-2 0-3 1 1 0 1 0 2 1v3c-1-1-1-1-1-2h0c-5 1-8 3-12 5l-2-7c1 0 1 0 2-1l2-1v1c1 0 1 0 1-1l-2 4c-1 0-1 1 0 2 1 0 2-1 3-1 1-1 3-2 5-2 1-1 3-2 4-2 1-1 1-1 2-1z" class="a"></path><defs><linearGradient id="Z" x1="461.486" y1="149.401" x2="485.786" y2="130.589" xlink:href="#B"><stop offset="0" stop-color="#c5c3c4"></stop><stop offset="1" stop-color="#eff0ef"></stop></linearGradient></defs><path fill="url(#Z)" d="M454 125l27 13c4 2 9 4 14 7v1c1 1 4 2 5 3h-1-1c2 1 4 2 5 3v1h1l1 1c0 1 1 3 1 4l-2-2h-1c1 1 2 3 2 5-1 1-1 2-1 2-1 1-2 2-2 3v1c1 1 1 2 2 3-2-1-4-2-5-3-1 0-1 0-2-1l-3-1c-1 0-2-1-3-2s-2-5-2-7l-1-2c-1-1-2-2-3-2l-2-2-6-4-6-3-20-9c-1 0-4-1-5-3l1-1 2-2 1 1c0-1 2-2 3-2 0-1 0-1 1-2z"></path><path d="M451 134c2 0 3 0 4 1h3c2-1 3-3 6-3h0c-1 2-2 3-3 4 1 0 3-1 5 0h0l2 2h1l1 1c1 0 1 1 2 2 0 1-1 1-1 2h0l-20-9z" class="G"></path><path d="M466 136c1 0 2 0 3 1h0c2 0 3 1 4 3 0 1 0 1-1 2 1 1 2 2 4 2h1c2 1 3-1 5-2l-3 4c2 1 4 3 6 4l3-3c1 0 1-1 2-1h2 1c0 1 1 1 2 1h0c1 1 1 0 1 0 1 1 2 1 3 2h-1c-1 0-1-1-2 0-2 1-2 2-3 3 0 1 0 1-1 1 0 0-1 0-1-1 1-1 1-2 2-3h-1c-1 0-2 1-3 2h-2c-1 0-1-1-2-1h-2l-6-4-6-3h0c0-1 1-1 1-2-1-1-1-2-2-2l-1-1h-1l-2-2h0z" class="Y"></path><path d="M483 150h2c1 0 1 1 2 1h2c1-1 2-2 3-2h1c-1 1-1 2-2 3 0 1 1 1 1 1 1 0 1 0 1-1 1-1 1-2 3-3 1-1 1 0 2 0 2 1 4 2 5 3v1h1l1 1c0 1 1 3 1 4l-2-2h-1-1 0c-1 0-2-1-3-1l-1 1h-1 1-1l-2 4h-2c-1-1-1-2-1-3-2-1-3-2-4-3s-2-2-3-2l-2-2z" class="D"></path><path d="M485 152c3 0 3 1 6 2h2c1 1 1 1 1 2h0c-1 1-1 2-1 3h0 1v-1h0c1-2 1-2 3-2l-2 4h-2c-1-1-1-2-1-3-2-1-3-2-4-3s-2-2-3-2z" class="G"></path><path d="M488 154c1 1 2 2 4 3 0 1 0 2 1 3h2l2-4h1-1 1l1-1c1 0 2 1 3 1h0 1c1 1 2 3 2 5-1 1-1 2-1 2-1 1-2 2-2 3v1c1 1 1 2 2 3-2-1-4-2-5-3-1 0-1 0-2-1l-3-1c-1 0-2-1-3-2s-2-5-2-7l-1-2z" class="c"></path><path d="M494 165c2-1 2-1 3-1l1 1c-1 0-1 1-1 1l-3-1z" class="U"></path><path d="M497 156h1l1-1c1 0 2 1 3 1v2h-1-3 0l-1-2z" class="g"></path><path d="M503 158c0 2-1 3-3 4-1 0-1 0-2-1l-1-2h0 2c2 0 2 0 4-1z" class="S"></path><path d="M488 154c1 1 2 2 4 3 0 1 0 2 1 3h2c-1 1 0 2 0 3h-2c-1 0-1-2-2-4 0-1-1-1-2-2v-1l-1-2z" class="M"></path><path d="M116 423c4 1 7 4 11 6 0 0 1 1 2 1 2 1 4 3 6 4l2 2 6 5c2 2 6 5 7 7l-1 1 3 4 5 8c1 0 1 1 1 2h0l-1 1c-2 3-6 7-7 10-1-1-1-2-2-3-1-2-3-5-4-6-1 0-6-6-7-7l-1-2-6-6c-2-1-3-2-4-2s-2-1-3-1v-1l-2-1-1-1c-2-1-4-2-7-3 1-1 1-1 1-2l3-5 1-2 1-1s0-1 1-2v-1h1c-1-1-2-2-3-2l1-1c-1 0-2-1-3-2h0z" class="I"></path><path d="M139 445l1-1 1 1c-1 1-1 2-2 3-1 0-1 0-2-1l2-2z" class="R"></path><path d="M134 444c2 0 3 1 4 0l1 1-2 2h-1c-1-1-2-2-2-3h0z" class="h"></path><path d="M137 440c1 0 1 0 1 1 1 1 1 2 0 3h0c-1 1-2 0-4 0v-1l1-1c0-2 1-2 2-2z" class="f"></path><path d="M137 436l6 5c2 2 6 5 7 7l-1 1c-1 0-2-2-3-3h0l-3-3c-1-1-3-3-5-3v1c0-1 0-1-1-1v-2h-3l-2-1h1c1 0 1-1 2-1 1 1 1 0 2 0z" class="T"></path><path d="M146 446h0v5l-2 2c-1 1-2 1-2 1-2 1-3 3-5 4l-1-2v-1c1-1 2-3 3-3h1s1-2 2-3c0-1 0-2 1-3h3 0z" class="K"></path><path d="M140 452s1-2 2-3c0-1 0-2 1-3h3c-1 2-1 3-2 4-1 2-2 3-4 5v-1-2z" class="C"></path><path d="M134 438h3v2c-1 0-2 0-2 2l-1 1h0 0c-1 1 0 1-1 2 0 2-1 4-3 5h0c-2-1-3-2-4-2v-2c2-2 3-4 5-4 1-2 2-3 3-4z" class="N"></path><path d="M134 438h3v2c-1 0-2 0-2 2l-1 1h0v-1c-1 2-2 3-3 4-1 0-1 0-2-1 0-1 1-2 2-3 1-2 2-3 3-4z" class="U"></path><defs><linearGradient id="a" x1="120.886" y1="441.168" x2="131.126" y2="433.977" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#a)" d="M129 430c2 1 4 3 6 4l2 2c-1 0-1 1-2 0-1 0-1 1-2 1h-1l2 1c-1 1-2 2-3 4-2 0-3 2-5 4v2c-1 0-2-1-3-1v-1l-2-1-1-1h0c0-2 2-4 3-6 2-2 2-3 4-5v-1h3l-1-2z"></path><path d="M129 430c2 1 4 3 6 4l-1 1c-2 0-2 0-3-1-1 0-1-1-1-1-1-1-2 0-3 0v-1h3l-1-2z" class="B"></path><path d="M121 445s0-1 1-1c0-1 4-6 5-6 0 1 0 2-1 3-1 2-3 3-3 5l-2-1z" class="J"></path><path d="M132 437l2 1c-1 1-2 2-3 4-2 0-3 2-5 4v2c-1 0-2-1-3-1v-1c0-2 2-3 3-5l1 1 5-5z" class="Z"></path><defs><linearGradient id="b" x1="122.207" y1="442.129" x2="122.37" y2="425.597" xlink:href="#B"><stop offset="0" stop-color="#969495"></stop><stop offset="1" stop-color="#bbb9ba"></stop></linearGradient></defs><path fill="url(#b)" d="M116 423c4 1 7 4 11 6 0 0 1 1 2 1l1 2h-3v1c-2 2-2 3-4 5-1 2-3 4-3 6h0c-2-1-4-2-7-3 1-1 1-1 1-2l3-5 1-2 1-1s0-1 1-2v-1h1c-1-1-2-2-3-2l1-1c-1 0-2-1-3-2h0z"></path><path d="M146 446c1 1 2 3 3 3l3 4 5 8c1 0 1 1 1 2h0l-1 1c-2 3-6 7-7 10-1-1-1-2-2-3-1-2-3-5-4-6-1 0-6-6-7-7 2-1 3-3 5-4 0 0 1 0 2-1l2-2v-5z" class="T"></path><path d="M146 446c1 1 2 3 3 3l3 4v3c0 1-2 5-3 6 0-2 0-2 1-3v-1c1 0 0 0 1-1v-1-1 1l-4 5c0 2-1 3-2 4l-1-1v1c-1 0-6-6-7-7 2-1 3-3 5-4 0 0 1 0 2-1l2-2v-5z" class="b"></path><path d="M149 453c1-1 1-1 2 0 0 3-4 7-5 10h0l-1 1-1-1v-1c0-2 3-8 5-9z" class="Q"></path><path d="M404 637v-3-1l4 2 1 1v1l5 1 18 3c3 0 7 0 10 1h-3c-1 0-1 1-1 1h-4-20-42-83-19-5s0-1-1-1h-3l33-5h110z" class="d"></path><path d="M404 637v-3-1l4 2 1 1v1h-5zm210-375c2 2 3 4 3 6 1 1 1 1 1 2 1 0 1 1 1 1 0 1 0 1-1 2h1c1 0 1 0 1-1 0-2-2-6-2-6l3 6v1 1c-1 1-1 2-1 2v1c1-2 1-2 2-3v1l2 2c8 14 12 30 16 45-1 0-1 0-2 1-1 0-3 1-4 2-2 0-4 1-5 2-1 0-2 1-3 1-1-1-1-2 0-2l2-4c0 1 0 1-1 1v-1l-2 1c-1 1-1 1-2 1l-1-6c-3-11-8-22-13-33l-4-8c1-2 3-4 4-5h1c4-2 3-5 4-9h0 0v-1z" class="j"></path><path d="M622 318c2 0 4-3 6-5-1 1-2 3-2 4l-2 2v4h1c-1 1-1 1-2 1l-1-6z" class="g"></path><path d="M619 290h0 2 0l1 2c1 0 2-1 2-2 1 1 0 1 0 2v1c-1 1-2 1-3 1s-2 0-2-1c-1-1-1-2 0-3z" class="C"></path><path d="M614 262c2 2 3 4 3 6l-1-2c0 1-1 1-1 2 0 3-2 4-4 6h0c-1 2-2 4-4 5l2 3v3l-4-8c1-2 3-4 4-5h1c4-2 3-5 4-9h0 0v-1z" class="D"></path><defs><linearGradient id="c" x1="317.669" y1="310.538" x2="326.432" y2="310.661" xlink:href="#B"><stop offset="0" stop-color="#b6b5b6"></stop><stop offset="1" stop-color="#d9d8d8"></stop></linearGradient></defs><path fill="url(#c)" d="M323 253l3 1h1c1 3 0 8 0 11v19c0 2 1 5 0 8-1 5 1 10-1 14v5 13 5l1 31v4l-1 1-2-1v1l2 1-1 1-1 1-1-1v-1h-1v1c-1 0-1 0-2-1l-1 1v-3-1l-2 2v-10-12-12-6-10-4-23-3-2-9-9c3-1 5-4 7-6h1l-1-1c-1-1-7-1-9-1 3 0 5-1 8 0h1c1 0 0-1 1 0v-2h1c-1-1-3-1-3-2z"></path><path d="M323 366v-2h1 0v1l2 1-1 1-1 1-1-1v-1z" class="Y"></path><path d="M321 283c1-1 1-1 2-1h1l-7 6v-3c2 0 3-2 4-2z" class="N"></path><path d="M317 311c1 0 1-1 2-1 1-1 4-3 5-4 0 2-2 2-2 4h1c-1 1-4 4-6 5v-4z" class="E"></path><path d="M323 310h1v1c0 3 2 13-1 15v1l-2 1c-1 1-2 2-3 2h0l-1 1v-6-10c2-1 5-4 6-5z" class="M"></path><path d="M323 310h1v1c-2 1-5 4-6 6h0 1c1-1 2-2 4-3h1 0c-4 3-5 4-6 9v1l1-1h1l-1 1-2 1v-10c2-1 5-4 6-5z" class="F"></path><path d="M320 323c1-1 3-2 4-2v2l-1 2v2l-2 1c-1 1-2 2-3 2h0l-1 1v-6l2-1 1-1z" class="X"></path><path d="M319 324v2c0 2-1 3-1 4l-1 1v-6l2-1z" class="J"></path><path d="M325 259v8c-1 5-1 10-1 15h0-1c-1 0-1 0-2 1-1 0-2 2-4 2v-2-9-9c3-1 5-4 7-6h1z" class="H"></path><path d="M317 265c3-1 5-4 7-6v4h0c-2 3-3 4-6 6-2 2 0 11-1 14v-9-9z" class="b"></path><path d="M325 259v8c-1 5-1 10-1 15h0-1c-1 0-1 0-2 1-1-1-1-1-2 0 1-2 1-3 2-5h0c0-2 2-3 3-4-2 0-3 1-4 2l-1-1v-3l5-5v-4-4h1zm-4 69h0 3 0v1c0 2 1 6 0 8v21l-1 2c1 0 1 0 1 1-1 1-1 2-2 3v2 1c-1 0-1 0-2-1l-1 1v-3-1l-2 2v-10-12-12l1-1h0c1 0 2-1 3-2z" class="O"></path><path d="M318 330h0c0 2 1 1 1 3v4c0 1-1 1 0 2 1 0 2-2 4-3 0 1 0 1-1 2 0 1-3 4-5 5v-12l1-1z" class="E"></path><path d="M321 328h0 3 0v1c0 2-1 3-2 4l-3 4v-4c0-2-1-1-1-3 1 0 2-1 3-2z" class="H"></path><path d="M324 337v21c0-1 0-1-1-1l1-1v-6h-1c-1 1-2 4-4 4v-2s1 0 0-1v-2l3-3c0-1 0-1 1-2v-1h0 0v-1h0-1l2-2v-3z" class="V"></path><path d="M536 473s1-1 2-1l-2 5-1 3c-1 4-3 8-4 13 0 2-1 3-2 5l1 1h-1v-1l-1-1c-2 2-4 4-7 4-9 5-17 11-26 16l-6 3-1-1v1h-1v-1l-1 1c-1 0-2 1-3 1l-2 2h-2-1l-1 3v1l-3 2c0-1 0-3 1-4v-3c0-2 0-3-1-4h-2v-1-1h-1l8-13 1 1v1c3-1 6-3 8-4l22-12 16-10 3-2h0l7-4z" class="L"></path><path d="M485 504l1 1-1 2c-3 0-5 5-8 4v-1c2-3 5-4 8-6z" class="c"></path><path d="M519 490h0c1-1 2-3 3-5 4 0 5-2 8-4-1 2-1 4-2 6l-1-2c0 1-1 2-1 3-1-1 0-2 0-3h0 0c-2 1-3 1-4 1v1l-4 8v-2s1 0 1-1h0v-2z" class="V"></path><defs><linearGradient id="d" x1="479.952" y1="509.591" x2="479.442" y2="517.797" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#acacaa"></stop></linearGradient></defs><path fill="url(#d)" d="M477 510v1c3 1 5-4 8-4-2 3-5 7-6 11 1 2 0 4-1 5l-1 3v1l-3 2c0-1 0-3 1-4 0-1 1-2 1-3l1-2c1-2 0-3-1-5 0 0-1 0-1-1s1-2 2-4z"></path><path d="M479 518c1 2 0 4-1 5l-1 3v1l-3 2c0-1 0-3 1-4 0-1 1-2 1-3 1 0 1-1 2-2h0v1 1 1l1-5z" class="D"></path><defs><linearGradient id="e" x1="502.333" y1="502.652" x2="491.942" y2="506.624" xlink:href="#B"><stop offset="0" stop-color="#9d9d9c"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#e)" d="M497 498c2-1 3-2 5-2-3 6-5 12-8 18h0c0-3 1-4 1-6l-1-1c-1 0-1 1-2 1v2h-1v-1-5l-1 1v-1h-2l2-2 4-2 3-2z"></path><path d="M497 498s1 1 0 1l-3 5h-1v-1l1-3 3-2z" class="M"></path><path d="M488 504l2-2 4-2-1 3v1h1c0 1-1 3-2 4v2h-1v-1-5l-1 1v-1h-2z" class="B"></path><path d="M488 504h2v1l1-1v5 1h1c-1 2-2 3-3 5h0l-1 1c-1 0-1 1-2 2h0l-1 1h0c-1 1-2 1-2 2l-2 2h-2-1c1-1 2-3 1-5 1-4 4-8 6-11l1-2-1-1h3z" class="V"></path><path d="M488 504h2v1c-2 3-3 8-6 11l-1 1c0 1 0 1-1 2l3-9 3-6h0z" class="H"></path><path d="M490 505l1-1v5 1h1c-1 2-2 3-3 5h0l-1 1c-1 0-1 1-2 2h0l-1 1h0c-1 1-2 1-2 2l-2 2h-2 1c0-1 1-1 1-2h1 0c1-1 2-3 2-5 3-3 4-8 6-11z" class="D"></path><path d="M536 473s1-1 2-1l-2 5-1 3c-1 4-3 8-4 13 0 2-1 3-2 5l1 1h-1v-1l-1-1c-2 2-4 4-7 4 1-2 3-2 4-4 1 0 1-2 1-3l2-7c1-2 1-4 2-6-3 2-4 4-8 4-1 2-2 4-3 5h0l-1-1v-1h-1l-4 9v-1c0-1 0-1 1-2l-3 3h-1v-2-2c1 0 1-1 1-1v-1h0l-2 1v-1c1 0 2-1 2-1l1-1s1 0 1-1c-1 0-1 1-2 1h-1l16-10 3-2h0l7-4z" class="f"></path><path d="M511 495c0-2 1-3 1-4 1-3 3-4 5-5 0 2-1 3-2 4v1l-2 2c0 1-1 2-2 2h0z" class="M"></path><path d="M529 477l1 1c-2 2-6 4-8 5-1 1-4 2-5 3-2 1-4 2-5 5 0 1-1 2-1 4h-1v-2c1 0 1-1 1-1v-1h0l-2 1v-1c1 0 2-1 2-1l1-1s1 0 1-1c-1 0-1 1-2 1h-1l16-10 3-2z" class="D"></path><path d="M536 473s1-1 2-1l-2 5-1 3c-1 4-3 8-4 13 0 2-1 3-2 5l1 1h-1v-1l-1-1c1-5 3-11 5-17 0-1 0-3 2-4 0-1 1-2 1-3z" class="I"></path><defs><linearGradient id="f" x1="67.573" y1="306.024" x2="87.714" y2="315.992" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#dad9da"></stop></linearGradient></defs><path fill="url(#f)" d="M109 255c1 0 1 0 0 1h0v1 3l-8 14c-1 1-2 4-3 5-8 16-14 33-18 51l-2 15c0 1 0 4-1 5v-1l-2 2c-2 1-3 2-4 4-2 2-2 4-3 6v1c1 0 1 0 2-1v2l-2 2h0v-1c-2 1-4 4-6 6l-1 1v-8-1-5h0v-2h-1v3-4c0-3 0-5 1-8 1-15 5-30 10-45l9-22 1 1h1v1l1-3c1 0 1 0 2 1-1 1-1 1 0 2l2-1 1 1c2-1 4-4 5-5l2-2c1 0 1-1 1-1 4-2 6-9 10-12 1-1 1-2 2-3 0-1 1-2 1-3z"></path><path d="M82 289h-1c1-2 3-4 4-5 1 1 1 1 1 2 0 0 0 2-1 2h-1c0 1-1 1-1 1h-1z" class="Y"></path><path d="M71 341h-1c-1 1-2 1-3 0 1 0 1 0 1-1 1-1 3-3 4-3l1 1v3h-1l-1-1v1h0z" class="j"></path><path d="M83 289v3h1c1 0 1-1 2-2h1v2c0 1-1 2-2 2s-2 0-3-1c-1 0-1-1-1-2l1-2h1z" class="S"></path><path d="M65 333c0-1 0-3 1-4 2-4 9-7 13-9 0 1-4 3-5 4-2 1-4 4-6 6 0 0-1 2-2 2 0 1-1 1-1 1z" class="F"></path><path d="M80 279l1 1-8 21h0l2-5c0-1 0-1 1-2 0-1-1-1 0-1 0-1 0-2 1-2v-3l2-3v-1l-3 6-5 12v-1l9-22z" class="j"></path><path d="M72 341l1 3c1 0 2-1 3-1 1-1 2-3 2-4v-2c1-2 1-4 2-6v-1l-2 15-1 1c-1 1-2 1-3 2h-4c-1 1-2 1-3 1 1 0 2-1 3-2-1 0-2-1-3-2s-1-2-1-3h1-1c1 1 2 1 3 1l2-2h0v-1l1 1z" class="E"></path><path d="M66 342h1-1l1 1c0 1 1 1 2 1h1l2-2v2c-1 2-1 2-2 3-1 0-2-1-3-2s-1-2-1-3z" class="d"></path><path d="M65 333s1 0 1-1c1 0 2-2 2-2 2-2 4-5 6-6-1 3-3 5-6 7-2 2-3 5-3 7v3l1 1c0 1 0 2 1 3s2 2 3 2c-1 1-2 2-3 2 0 2-2 4-3 5-1 2-1 3-2 4 0 1-1 2-1 3v1-5c0-4 1-10 2-14 0-4 0-7 2-10z" class="H"></path><path d="M67 345c1 1 2 2 3 2-1 1-2 2-3 2 0 2-2 4-3 5v-2c0-2 1-5 3-7z" class="B"></path><defs><linearGradient id="g" x1="92.142" y1="265.566" x2="101.674" y2="280.398" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#d8d7d8"></stop></linearGradient></defs><path fill="url(#g)" d="M109 255c1 0 1 0 0 1h0v1 3l-8 14c-1 1-2 4-3 5-1-1-1-1-2 0-1 0-2 1-3 2v1c0 1-2 4-3 6 0-1 1-3 1-4h0c-1 2-2 5-3 6-1-1-1-1-2-1l-1 1v1h-1c0-1 1-2 0-3h1c1 0 1-2 1-2 0-1 0-1-1-2h1c3-2 5-5 7-7 1 0 1-1 2-1h-2l2-2c1 0 1-1 1-1 4-2 6-9 10-12 1-1 1-2 2-3 0-1 1-2 1-3z"></path><defs><linearGradient id="h" x1="65.253" y1="355.532" x2="69.125" y2="358.063" xlink:href="#B"><stop offset="0" stop-color="#514f50"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#h)" d="M78 345c0 1 0 4-1 5v-1l-2 2c-2 1-3 2-4 4-2 2-2 4-3 6v1c1 0 1 0 2-1v2l-2 2h0v-1c-2 1-4 4-6 6l-1 1v-8-1-1c0-1 1-2 1-3 1-1 1-2 2-4 1-1 3-3 3-5 1 0 2 0 3-1h4c1-1 2-1 3-2l1-1z"></path><path d="M62 370c0-1 0-1 1-2v-1c1-2 3-3 4-5 0-1 0-1 1-1v1c1 0 1 0 2-1v2l-2 2h0v-1c-2 1-4 4-6 6z" class="Z"></path><path d="M67 349c1 0 2 0 3-1h4c-4 3-7 6-11 10 0 1-1 2-2 3 0-1 1-2 1-3 1-1 1-2 2-4 1-1 3-3 3-5z" class="N"></path><path d="M273 262c4-2 9-3 14-4l-2 1c-1 1-2 2-3 4l-1 2-1 1c-2 1-4 2-7 3-7 4-11 12-14 20-5 13-8 26-12 39l-4 17-5 20c-1 2-1 5-3 6v2 1l-1 1h0l-6-61c0-3-1-7 0-11 1-1 4-2 5-4 1-1 1-4 2-5h1v4l1 24 4-11 6-20 5-15h1l2-2c1 0 2-2 2-3 2-2 5-5 8-7h3l5-2z" class="C"></path><path d="M265 264h3c-7 5-13 11-15 19-2 5-3 11-5 15v-4c1 0 0-1 0-2l1-1v-3h0c2-5 3-9 6-14 1 0 2-2 2-3 2-2 5-5 8-7z" class="O"></path><path d="M252 276h1l2-2c-3 5-4 9-6 14h0v3l-1 1c0 1 1 2 0 2v4c0 2-3 15-4 16l-1-1h1c0-2-1-3-1-4h-1c0 1 0 1-1 2h0l6-20 5-15z" class="L"></path><defs><linearGradient id="i" x1="251.668" y1="304.3" x2="216.43" y2="359.304" xlink:href="#B"><stop offset="0" stop-color="#d8d6d8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#i)" d="M228 314c0-3-1-7 0-11 1-1 4-2 5-4 1-1 1-4 2-5h1v4l1 24 4-11h0c1-1 1-1 1-2h1c0 1 1 2 1 4h-1l1 1c-2 5-4 10-5 16-1 7-2 14-2 21-1 8-2 15-2 23l-1 1h0l-6-61z"></path><defs><linearGradient id="j" x1="519.584" y1="188.033" x2="557.997" y2="169.294" xlink:href="#B"><stop offset="0" stop-color="#d6d5d6"></stop><stop offset="1" stop-color="#fefdfe"></stop></linearGradient></defs><path fill="url(#j)" d="M505 151c10 6 21 12 31 20l12 9c3 1 5 4 8 5 4 2 10 4 14 3l-1 2c-4 3-9 17-9 22v3s0 1-1 2c-2-2-4-6-6-8v4h0v1c-2 0-2 0-4-1 0-1-1-2-2-3 1-1 1-1 1-2h0v-1c-1-2-1-2-2-2-2-1-4-1-5-1-2 1-3 1-4 1v-1c0-1 1-1 1-2v-1c-1-2-3-4-4-5 0-1-2-2-2-2-2-2-5-4-7-5-1-1-2-2-4-3-1-1-2-2-4-3h0c-1-1-2-2-4-3 0 0-2 0-2 1l-1 1h0 0 0c-1-1-1-2-1-3l-1-1h1 3v-1c-1 0-1-1-2-1l-1-1-2-1h3c-1 0-1 0-1-1-1 0-2-1-2-1v-2h0l-1 1c-1 0-2-1-2-1-1-1-1-2-2-3v-1c0-1 1-2 2-3 0 0 0-1 1-2 0-2-1-4-2-5h1l2 2c0-1-1-3-1-4l-1-1h-1v-1c1 0 1-1 2-1z"></path><path d="M513 171v3c1-1 0-1 1-2l1-1h1c0 1-1 4 0 5l1 1v1l-1 1c-1-2-3-3-4-3v-3-1c0-1 1-1 1-1z" class="D"></path><path d="M556 197h1 1v3c-1 1-2 2-3 2-2 0-2 0-3-1v-1c0-2 0-2 2-3v1 1 1c1-1 2-2 2-3z" class="I"></path><path d="M503 153c1 0 2 0 2 1 2 1 3 2 4 2s3 1 4 2c0 2-1 3-1 5h0-1c0-2 1-3 1-5-1 2-2 3-3 4v1l-1 1h0l-1 1 1-4h0c-1-1-1 0-1 0l-1-1v-2c0-1-1-3-1-4l-1-1h-1z" class="Y"></path><path d="M508 164h0l1-1v-1c1-1 2-2 3-4 0 2-1 3-1 5h1c0 2 0 4 1 6h0 0 0c1-1 0-1 1-1h0l-1 3s-1 0-1 1v1 3l-2-2h0v-3c-1-1-1 0-1-1v-1-2s0-1-1-1v-1h1v-1h-1z" class="V"></path><path d="M536 193c6 5 11 11 17 16v4h0v1c-2 0-2 0-4-1 0-1-1-2-2-3 1-1 1-1 1-2h0v-1c-1-2-1-2-2-2-2-1-4-1-5-1 0 0 0-1 1-1s2 0 3 1h1v-1c-1-1-2-2-3-2-2-1-3-2-5-4-1-1-2-2-2-4z" class="I"></path><path d="M548 208l5 5h0v1c-2 0-2 0-4-1 0-1-1-2-2-3 1-1 1-1 1-2z" class="h"></path><path d="M503 156h1l2 2v2l1 1s0-1 1 0h0l-1 4 1-1h1v1h-1v1c1 0 1 1 1 1v2 1c0 1 0 0 1 1v3h0 0c-1 0-1 0-1-1-1 0-2-1-2-1v-2h0l-1 1c-1 0-2-1-2-1-1-1-1-2-2-3v-1c0-1 1-2 2-3 0 0 0-1 1-2 0-2-1-4-2-5z" class="G"></path><path d="M504 170c-1-1-1-2-2-3v-1c0-1 1-2 2-3v2h0c1 0 1 1 1 1v3c1 0 2 0 2-1l1-2c1 0 1 1 1 1v2 1c0 1 0 0 1 1v3h0 0c-1 0-1 0-1-1-1 0-2-1-2-1v-2h0l-1 1c-1 0-2-1-2-1z" class="E"></path><path d="M507 174h3 0l2 2c1 0 3 1 4 3l1-1c2 3 5 4 8 6l11 9c0 2 1 3 2 4 2 2 3 3 5 4 1 0 2 1 3 2v1h-1c-1-1-2-1-3-1s-1 1-1 1c-2 1-3 1-4 1v-1c0-1 1-1 1-2v-1c-1-2-3-4-4-5 0-1-2-2-2-2-2-2-5-4-7-5-1-1-2-2-4-3-1-1-2-2-4-3h0c-1-1-2-2-4-3 0 0-2 0-2 1l-1 1h0 0 0c-1-1-1-2-1-3l-1-1h1 3v-1c-1 0-1-1-2-1l-1-1-2-1z" class="d"></path><path d="M507 174h3 0l2 2c1 0 3 1 4 3 0 1 1 2 2 2h0c1 1 1 1 2 1v1c1 1 2 1 3 2 3 2 7 5 9 8v1c-2-2-5-4-7-5-1-1-2-2-4-3-1-1-2-2-4-3h0c-1-1-2-2-4-3 0 0-2 0-2 1l-1 1h0 0 0c-1-1-1-2-1-3l-1-1h1 3v-1c-1 0-1-1-2-1l-1-1-2-1z" class="h"></path><path d="M509 179v-1c2 1 3 1 4 2 0 0-2 0-2 1l-1 1h0 0 0c-1-1-1-2-1-3z" class="S"></path><path d="M478 225h13l23 1v3c-13 0-25 2-38 4-2 1-7 1-9 3-1 2 0 6 0 8v16 28c1-1 1-2 1-3h-1 1c1-1 4-2 6-2v2 6 14l-1 4v2c-1 1-2 2-3 2-2 2-3 4-5 5-1 4-1 9-1 13v5l-1 6v9l-1 20c0 1 0 3-1 4-1-1-3-13-3-16l-11-52c-1-7-4-14-6-20-1-3-2-5-3-7l4-4 3-1c-3-4-6-7-9-9 1 0 3 0 4 1l1-1h0 1 1 1c1 1 2 1 3 2 2 2 3 4 5 6 1 2 2 4 2 6 2 4 3 9 5 13 0 3 1 6 2 9 0 1 1 3 1 5v4h-1c0-1 0-1-1-2 0 2 1 4 2 6v1h0c1-2 1-7 1-8s0-2 1-2v-1-2c1-2 0-4 1-6v-8-3c1-2 0-3 1-5h0c-2-3 1-8-1-11h0c-1-2 0-3 0-4 1-1 0-3 0-4v1c-1-8 1-17-1-26l-1-1c0-1-2-1-3-1 1 0 2 0 3-1l-7 1c3-3 6-6 9-7 1-1 2-1 2-2 1 0 5 0 6-1h5z" class="j"></path><path d="M472 303h1v2h1l-1 4v2c-1 1-2 2-3 2-2 2-3 4-5 5 0-2 1-4 2-6s3-3 4-4c0-1 0-2 1-2h0c1-1 0-2 0-3z" class="i"></path><defs><linearGradient id="k" x1="472.19" y1="285.595" x2="469.111" y2="300.345" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#d2d2d2"></stop></linearGradient></defs><path fill="url(#k)" d="M474 283v2 6 14h-1v-2h-1c0-1 0-1 1-2h0c-2 0-4 0-5-1h-1v2 2l-1 2c0-3 1-6 1-10v-7-1c1-1 1-2 1-3h-1 1c1-1 4-2 6-2z"></path><defs><linearGradient id="l" x1="469.131" y1="283.783" x2="470.879" y2="289.5" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#b3b2b1"></stop></linearGradient></defs><path fill="url(#l)" d="M474 283v2 6c-1 0-1-1-1-2v-1-2c0 1 0 0-1 1s0 1-1 2-3 2-4 3v-3-1c1-1 1-2 1-3h-1 1c1-1 4-2 6-2z"></path><path d="M446 283l1 1s0 1 1 1c0 0 1 0 1 1h0l2 3v2 1c1 1 1 3 2 5 0 1-1 2-2 3-1 2-3 4-4 7-1-7-4-14-6-20l3-3c1 0 1 0 2-1z" class="L"></path><path d="M445 294l6-5v2h-1c-1 1-3 4-5 4v-1z" class="Y"></path><path d="M451 292c1 1 1 3 2 5-2 0-4 3-5 4h-1v-2c0-1 3-3 4-4v-3z" class="a"></path><path d="M449 286h0l2 3-6 5c0-1 0-1-1-1v-2l3-4c1 0 1-1 2-1z" class="D"></path><path d="M441 266h1 1 1c1 1 2 1 3 2 2 2 3 4 5 6 1 2 2 4 2 6 2 4 3 9 5 13 0 3 1 6 2 9 0 1 1 3 1 5v4h-1c0-1 0-1-1-2v-1l-1-1c-1-1-2-3-2-4l-3-10c-1-4-2-8-4-12l-1-1v6h0c0-1-1-1-1-1-1 0-1-1-1-1l-1-1c-1 1-1 1-2 1l-3 3c-1-3-2-5-3-7l4-4 3-1c-3-4-6-7-9-9 1 0 3 0 4 1l1-1h0z" class="O"></path><path d="M445 275l1-1c2 2 3 5 4 7l-1-1v6h0c0-1-1-1-1-1-1 0-1-1-1-1l-1-1 2-2c0-1-1-3-2-4l-1-2z" class="Y"></path><path d="M441 266h1 1 1c1 1 2 1 3 2 2 2 3 4 5 6 1 2 2 4 2 6l1 5c0 1 1 2 0 3v1c-2-5-4-11-7-15-2-3-4-6-7-8z" class="S"></path><path d="M445 275h0l1 2c1 1 2 3 2 4l-2 2c-1 1-1 1-2 1l-3 3c-1-3-2-5-3-7l4-4 3-1z" class="N"></path><path d="M445 275h0l1 2h0c-1 1-1 2-1 3 0 0-3 2-4 2 1-3 2-3 4-4l-1-1h-1l-1-1 3-1z" class="E"></path><path d="M446 277c1 1 2 3 2 4l-2 2c-1 1-1 1-2 1h-1-1l-1-1v-1c1 0 4-2 4-2 0-1 0-2 1-3h0z" class="f"></path><path d="M454 280c2 4 3 9 5 13 0 3 1 6 2 9 0 1 1 3 1 5v4h-1c0-1 0-1-1-2v-1l-5-19v-1c1-1 0-2 0-3l-1-5z" class="W"></path><path d="M392 225h34 8l7 1 2-1h9 7 0 14c-1 1-5 1-6 1 0 1-1 1-2 2-3 1-6 4-9 7l7-1c-1 1-2 1-3 1 1 0 3 0 3 1l1 1c2 9 0 18 1 26v-1c0 1 1 3 0 4 0 1-1 2 0 4h0c2 3-1 8 1 11h0c-1 2 0 3-1 5v3 8c-1 2 0 4-1 6v2 1c-1 0-1 1-1 2s0 6-1 8h0v-1c-1-2-2-4-2-6 1 1 1 1 1 2h1v-4c0-2-1-4-1-5-1-3-2-6-2-9-2-4-3-9-5-13 0-2-1-4-2-6-2-2-3-4-5-6-1-1-2-1-3-2-1 0-2-1-2-2h2c-1 0-2-1-4-2 0-2-1-2-3-3-1 0-1-2-2-2v-2c0-1-1-2-2-2v-1l2-1 1-3-1-1 1-2-1-1h0l3-6c1-1 1-1 1-2l-2-1h0-45-3 0c2-1 3-1 4-2v-1h0l-1-1-1 1h-1c0 1-2 1-3 1l-1-1 7-5 1-1h-2v-1z" class="g"></path><path d="M452 225h7l3 1c0 2-5 6-7 8h-5-1-1v-1c-1 0-2 1-3 1-1 1-3 1-4 0-1-3 0-6 2-9h9z" class="j"></path><path d="M462 253h0l1 4v-3 34c0 5 1 10 0 15v5c0 1 0 6-1 8h0v-1c-1-2-2-4-2-6 1 1 1 1 1 2h1v-4c0-2-1-4-1-5-1-3-2-6-2-9 0-2-1-5-1-7-1-2-1-4-2-6 0-1 0-2 1-3h-1 2c1 0 3-1 4-1h0v-13-10z" class="c"></path><path d="M456 277h2c1 0 3-1 4-1h0v3c1 1 0 4 0 5h-2-1c0-1 0-1-1-2 0-1-1-3-1-4v-1h-1z" class="F"></path><defs><linearGradient id="m" x1="456.848" y1="297.981" x2="466.016" y2="288.583" xlink:href="#B"><stop offset="0" stop-color="#5b5b5c"></stop><stop offset="1" stop-color="#747473"></stop></linearGradient></defs><path fill="url(#m)" d="M457 277v1c0 1 1 3 1 4v1c1 1 1 2 2 3 1-1 2-1 2-2l1-1v5c0 5-1 10 0 15v5c0 1 0 6-1 8h0v-1c-1-2-2-4-2-6 1 1 1 1 1 2h1v-4c0-2-1-4-1-5-1-3-2-6-2-9 0-2-1-5-1-7-1-2-1-4-2-6 0-1 0-2 1-3z"></path><path d="M441 226l2-1c-2 3-3 6-2 9 1 1 3 1 4 0 1 0 2-1 3-1l-1 1v1h8c2 0 6 0 8 1v3 15 3l-1-4-1-1c1-2 1-3 1-5 0-1 0-3-1-4v-1h-1c-1-1 0-1 0-2h-2c-1 1-2 2-3 1h0-3c-1 0-1 0-2 1h-1 0s0 1-1 2h0c-3-1-6 2-7 3l-3 2h0v-2h-2v1l-1-1 1-2-1-1h0l3-6c1-1 1-1 1-2l-2-1h0-45 11 19 8 0c4-2 8-4 11-7v-2z" class="F"></path><path d="M437 235h-4c1-1 4-4 6-4 0 1-1 2 0 4h-2 0z" class="g"></path><path d="M436 247l1-2h2c1-1 1-1 3-2h0c1-1 2-1 3-1h4s0 1-1 2h0c-3-1-6 2-7 3l-3 2h0v-2h-2z" class="G"></path><path d="M439 239c1-1 1-2 2-2v-2h3 11c2 0 6 0 8 1v3 15 3l-1-4-1-1c1-2 1-3 1-5 0-1 0-3-1-4v-1h-1c-1-1 0-1 0-2h-2c-5-1-11 0-16 2-2 0-3 0-4 1h-1l2-4z" class="Q"></path><path d="M439 239c1-1 1-2 2-2v-2h3 11c2 0 6 0 8 1v3c-1 0-2-1-3-1h-12c-2 0-5 2-7 1h-2z" class="d"></path><defs><linearGradient id="n" x1="398.603" y1="221.248" x2="433.793" y2="239.205" xlink:href="#B"><stop offset="0" stop-color="#c5c4c5"></stop><stop offset="1" stop-color="#fefdfd"></stop></linearGradient></defs><path fill="url(#n)" d="M392 225h34 8l7 1v2c-3 3-7 5-11 7h0-8-19-11-3 0c2-1 3-1 4-2v-1h0l-1-1-1 1h-1c0 1-2 1-3 1l-1-1 7-5 1-1h-2v-1z"></path><path d="M392 225h34 8l-30 1c-1 2-1 3-4 4h0l-1 1h1c-1 2-4 2-6 3v-1h-1v-1h0l-1-1-1 1h-1c0 1-2 1-3 1l-1-1 7-5 1-1h-2v-1z" class="O"></path><path d="M394 226l5-1c0 1-1 2-2 2h-4l1-1z" class="Y"></path><path d="M399 225l2 1c0 1-3 3-4 3l-4 3h0l-1-1 5-4c1 0 2-1 2-2z" class="a"></path><path d="M393 227h4l-5 4-1 1h-1c0 1-2 1-3 1l-1-1 7-5zm65 13h2c0 1-1 1 0 2h1v1c1 1 1 3 1 4 0 2 0 3-1 5l1 1h0v10 13h0c-1 0-3 1-4 1h-2 1c-1 1-1 2-1 3 1 2 1 4 2 6 0 2 1 5 1 7-2-4-3-9-5-13 0-2-1-4-2-6-2-2-3-4-5-6-1-1-2-1-3-2-1 0-2-1-2-2h2c-1 0-2-1-4-2 0-2-1-2-3-3-1 0-1-2-2-2v-2c0-1-1-2-2-2v-1l2-1 1-3v-1h2v2h0l3-2c1-1 4-4 7-3h0c1-1 1-2 1-2h0 1c1-1 1-1 2-1h3 0c1 1 2 0 3-1z" class="G"></path><g class="D"><path d="M455 242c1 1 1 1 2 1v1 1c1 0 1 0 1 1l1 2v2l-1 1c0 1 0 1-1 2 0 1 0 1-1 1-2 1-3 3-5 2l-1-1 2-1h-1c1-1 2-2 3-2s1 1 2 1c1-1 1-2 2-3 0-3-1-4-2-5-2-1-3-1-4 0l3-3z"></path><path d="M450 258c1-1 2-1 3-2h1 3c1-1 2-1 3 0v2h0c0 1 1 4 0 5l-2 1h-1v-2c-1 0-2 0-3 1h0-1c-1 0-4 1-5 0s-2-1-3-2c1-1 2-1 3-2 1 0 1 0 2-1z"></path></g><path d="M450 258h3c2-1 4-1 5-1 1 1 1 1 0 2s-1 1-2 1c-2 1-5 3-7 1h-1-1-1c1 0 1 0 1-1l1-1c1 0 1 0 2-1z" class="Y"></path><defs><linearGradient id="o" x1="451.064" y1="246.435" x2="441.068" y2="245.898" xlink:href="#B"><stop offset="0" stop-color="#a3a1a2"></stop><stop offset="1" stop-color="#cac8c9"></stop></linearGradient></defs><path fill="url(#o)" d="M458 240h2c0 1-1 1 0 2h1v1c1 1 1 3 1 4 0 2 0 3-1 5l1 1h0c-2 1-2 1-4 1h0l1-1c2-2 2-4 2-7l-2-2-2-1c1 0 0-1 0-1v-1l-2 1-3 3-9 6h-2c0-1 1-2 1-3h-1v-1c1-1 4-4 7-3h0c1-1 1-2 1-2h0 1c1-1 1-1 2-1h3 0c1 1 2 0 3-1z"></path><path d="M452 245c1-1 2-1 4 0 1 1 2 2 2 5-1 1-1 2-2 3-1 0-1-1-2-1s-2 1-3 2h-3v1l-6 3c-2 0-2 0-3-1l1-1c-1-1-2-1-3-1v-2l-1-1h0c0-1 1-2 2-3l3-2v1h1c0 1-1 2-1 3h2l9-6z" class="a"></path><path d="M438 249l3-2v1h1c0 1-1 2-1 3h2-1l-3 2h1 1 0l1-1h1c0 1-1 1-2 2h1c1 0-1 0 0 0h1s-1 2-2 2c2 0 3-1 5-2h2v1l-6 3c-2 0-2 0-3-1l1-1c-1-1-2-1-3-1v-2l-1-1h0c0-1 1-2 2-3z" class="L"></path><path d="M436 247h2v2h0c-1 1-2 2-2 3h0l1 1v2c1 0 2 0 3 1l-1 1c1 1 1 1 3 1l6-3h2 0c-2 2-6 3-7 6h0c1 0 1 1 1 1 2 1 3 2 6 2v1h3 0c0 1-1 1-2 2 1 1 2 3 3 4 2 0 4 0 5-1 0-1 0-2-1-2 0-1 1-1 2-2-1-1-1-1-2-1v-1l2-1h2v13h0c-1 0-3 1-4 1h-2 1c-1 1-1 2-1 3 1 2 1 4 2 6 0 2 1 5 1 7-2-4-3-9-5-13 0-2-1-4-2-6-2-2-3-4-5-6-1-1-2-1-3-2-1 0-2-1-2-2h2c-1 0-2-1-4-2 0-2-1-2-3-3-1 0-1-2-2-2v-2c0-1-1-2-2-2v-1l2-1 1-3v-1z" class="M"></path><path d="M462 263v13h0c-1 0-3 1-4 1h-2v-1-2h-1c0-3-1-1-3-3h1 1c1 0 2 0 3 1 1 0 1 0 2 2l-2 2h1 0 2 0v-8h0l1-2-1-1v-2h2z" class="U"></path><path d="M442 264h2c1 1 2 1 3 2 1 0 1 1 2 1h1c0 1 0 1-1 2 1 1 2 2 3 2 2 2 3 0 3 3h1v2 1h1c-1 1-1 2-1 3 1 2 1 4 2 6 0 2 1 5 1 7-2-4-3-9-5-13 0-2-1-4-2-6-2-2-3-4-5-6-1-1-2-1-3-2-1 0-2-1-2-2z" class="Q"></path><defs><linearGradient id="p" x1="284.199" y1="570.236" x2="300.198" y2="634.264" xlink:href="#B"><stop offset="0" stop-color="#cbc9cb"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#p)" d="M319 545c1 0 3-3 3-4 2-1 3-3 5-4l1 1-1 3v11c1 6 0 12 1 18v4 1l2-1 1-1 2-2c0 1-1 2-1 2v1c1 0 1 1 1 2h0c1 2 0 3 0 5 1 2 3 3 2 5l-2 1v1c1-1 1-1 3-1h0l2 1v1h3 0c1 0 1 0 2-1h1c0 1-1 2-1 3l-1 1h0c1 1 1 2 1 3l-1 3v1l-1 3-1 2-2 2 1 3 1-1 1 2h1c0 1-1 2-1 3v1s-3 2-3 3l-8 4c0 1-1 2-2 2-1-1-3-1-4 0-1 0-1 1-2 1l-1 2c-1 0-2 1-2 2h-4c-3 1-5 2-7 3-1 0-1-1-2-1l-5 3c-2 0-5 2-6 3l-1 1-33 5h-4l1-6c5 0 10-1 15-2 10-3 20-6 28-12s14-14 16-25v-16-3-12-19l2-2z"></path><path d="M324 609v3c0 2-1 4-3 5l-1 1c2-3 3-6 4-9z" class="C"></path><path d="M325 560c-1 4-5 6-6 9 0 1 0 2 1 3l3-3v-1l1-1 1 1c-1 1-2 2-2 3-2 1-3 2-4 4h0l-2 3v-12h1c1 0 5-4 7-6z" class="L"></path><path d="M319 569c0 1 0 2 1 3l-1 1c-1 1-1 0-1 0 0-2 0-2 1-4zm-2 12h1c0-1 0-1 1-2 0 0 1-1 1-2l1-1 1-1c1-1 1 0 1-1v-1l1 1h0c-1 2 1 5 0 7l-1 1c-2 1-2 3-4 4h-1v1c2 0 5-3 7-4-1 2-2 3-3 5l-4 4v3h0l-1 2v-16z" class="Y"></path><defs><linearGradient id="q" x1="317.454" y1="550.356" x2="326.898" y2="553.137" xlink:href="#B"><stop offset="0" stop-color="#b4b2b3"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#q)" d="M319 545c1 0 3-3 3-4 2-1 3-3 5-4l1 1-1 3c-1 1-1 13-1 16 0 1 0 3-1 3-2 2-6 6-7 6h-1v-19l2-2z"></path><defs><linearGradient id="r" x1="329.928" y1="604.768" x2="312.052" y2="601.963" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#474648"></stop></linearGradient></defs><path fill="url(#r)" d="M327 552c1 6 0 12 1 18v4 1l2-1 1-1 2-2c0 1-1 2-1 2v1c1 0 1 1 1 2h0c1 2 0 3 0 5 1 2 3 3 2 5l-2 1v1c1-1 1-1 3-1h0l2 1v1h3 0c1 0 1 0 2-1h1c0 1-1 2-1 3l-1 1h0c1 1 1 2 1 3l-1 3v1l-1 3-1 2-2 2 1 3 1-1 1 2h1c0 1-1 2-1 3v1s-3 2-3 3l-8 4c0 1-1 2-2 2-1-1-3-1-4 0-1 0-1 1-2 1l-1 2c-1 0-2 1-2 2h-4c-3 1-5 2-7 3-1 0-1-1-2-1 5-3 10-7 14-12l1-1c2-1 3-3 3-5v-3c2-4 3-8 3-12v-16c0-1-1-3 0-5 0-1-1-2-1-3 0-2 1-4 1-6v-15z"></path><path d="M327 594c1 2 1 5 0 8v1 4c-1 2-2 4-2 5h-1v-3c2-4 3-8 3-12v-1-2z" class="S"></path><path d="M327 552c1 6 0 12 1 18v4 1l2-1 1-1 2-2c0 1-1 2-1 2v1c1 0 1 1 1 2h0c1 2 0 3 0 5 1 2 3 3 2 5l-2 1c-1 1-1 1-1 2 0 4 2 7 0 11l-1 1-1-8c0-3-1-7 0-9l1-1c0-2 0-7-1-8l-2 1v5c-1 4-1 9-1 13v2 1-16c0-1-1-3 0-5 0-1-1-2-1-3 0-2 1-4 1-6v-15z" class="b"></path><path d="M339 603h1v1l-2 2 1 3 1-1 1 2h1c0 1-1 2-1 3v1s-3 2-3 3l-8 4c0 1-1 2-2 2-1-1-3-1-4 0-1 0-1 1-2 1h-3-1c3-3 7-5 10-7v-2-2-2c5-2 8-4 11-8z" class="L"></path><g class="P"><path d="M338 606l1 3c-1 0-2 2-3 3v-2h-1c1-2 2-3 3-4z"></path><path d="M328 613h0c3-1 5-2 7-3-2 3-4 5-7 7v-2-2zm11-4l1-1 1 2h1c0 1-1 2-1 3v1s-3 2-3 3l-8 4c0 1-1 2-2 2-1-1-3-1-4 0 5-3 8-6 11-10l1-1c1-1 2-3 3-3z"></path></g><path d="M339 609l1-1 1 2h1c0 1-1 2-1 3l-5 3h0v-2l-1-1 1-1c1-1 2-3 3-3z" class="W"></path><defs><linearGradient id="s" x1="333.985" y1="610.123" x2="334.989" y2="587.79" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#7d7c7c"></stop></linearGradient></defs><path fill="url(#s)" d="M333 588c1-1 1-1 3-1h0l2 1v1h3 0c1 0 1 0 2-1h1c0 1-1 2-1 3l-1 1h0c1 1 1 2 1 3l-1 3v1l-1 3-1 2v-1h-1c-3 4-6 6-11 8 0-3 2-7 3-10l1-1c2-4 0-7 0-11 0-1 0-1 1-2v1z"></path><path d="M333 588c1-1 1-1 3-1h0l-1 3h-1c-1 0-1-1-1-2z" class="E"></path><path d="M341 596h1v3l-1 3-1 2v-1h-1c1-3 1-5 2-7z" class="D"></path><path d="M343 588h1c0 1-1 2-1 3l-1 1h0c1 1 1 2 1 3l-1 3v1-3h-1v-1l1-1-4-5h3 0c1 0 1 0 2-1z" class="U"></path><path d="M512 422h1 0v1h0c0 1 1 2 1 3l1 1c2 3 3 6 5 9l5 5v2 1l2 2c1 1 2 1 4 1h1l2 1 3 2 6 3 4 3-1 1-3 5c-2 2-3 5-5 8v2c-1 0-2 1-2 1l-7 4h0l-3 2-16 10-22 12c-2 1-5 3-8 4v-1l-1-1c1-3 4-6 5-9l10-19c7-17 13-35 18-53z" class="I"></path><path d="M510 458c-2 1-5 5-5 7l-1 1h-1c0-2 1-3 2-4l1-2c1-2 2-3 4-4v2z" class="P"></path><path d="M498 483h0c1 2 0 4-1 5l-2 3-1 1-3 4h1l3-2c1-1 3-2 4-2s2-1 3-1c1-1 1-1 2-1h-1l-9 5c-1 1-2 2-3 2l-1-1 1-1 2-3v-1-1c1 0 1 0 1-1 0 0 1-1 1-2l3-4z" class="K"></path><path d="M520 436l5 5v2 1l-2-1h0v2c-1 0-1 0-2-1h0l-1-1v-1-1l-2-1-1-3c1 1 2 2 3 2v-3z" class="V"></path><path d="M521 444v-3h0c1 0 2 0 2 1v1h0v2c-1 0-1 0-2-1h0z" class="Y"></path><path d="M518 440v1c1 1 1 1 1 2h-1c-1 0-1 0-2-1s-4-8-4-10v-1-1l1-1 4 8 1 3z" class="C"></path><path d="M512 422h1 0v1h0c0 1 1 2 1 3l1 1c2 3 3 6 5 9v3c-1 0-2-1-3-2l-4-8-1-2h0c-1 3-2 7-3 10l-5 17c-2 6-5 12-7 18l-9 18-5 10c-1 1-2 3-3 4l-1-1c1-3 4-6 5-9l10-19c7-17 13-35 18-53z" class="D"></path><path d="M527 446c1 1 2 1 4 1h1l2 1 3 2 6 3 4 3-1 1-3 5c-2 2-3 5-5 8v2c-1 0-2 1-2 1l-7 4h0l-3 2-3-1c-2-1-3-1-5-2-1-1-2-2-3-2 0-1-1-1-1-1h1c-1-2-2-3-3-5 0 0-1-1-1-2v-1h1c-1-3-2-5-2-7v-2-1c4-4 9-7 14-7h2c1-1 1-1 1-2z" class="K"></path><path d="M529 459h1c1 0 1 0 1 1v1c0 1-1 2-2 3l-1-1c-1-1-2-2-2-3l1-1c0 1 0 1 1 1l1-1z" class="J"></path><path d="M537 466l-2 3h-1c0-1 0-3-1-4v-3-1c0-1 0-1 1-2s3 0 4 1h1 0 0c0 3-2 4-2 6h0 0z" class="R"></path><path d="M517 464c0-1 0-3 1-5h0l-1-1c1-1 2-2 3-2h2c1 0 1 0 2 1 0 0 1 1 0 2 0 3-1 2-2 3h0l3 1 2 1c0 1-1 1-1 2 1 1 1 3 0 4h0 0l-2-2h-4c-1 0-2-2-3-4z" class="e"></path><path d="M521 463h0 4l2 1c0 1-1 1-1 2 1 1 1 3 0 4h0 0l-2-2s1 0 1-1h-1c-1-1-2-1-3-1-1-1-1-1-1-2l1-1z" class="P"></path><path d="M521 463h0 4l2 1c0 1-1 1-1 2h0c-1-1-2-2-4-2h0l-1-1z" class="c"></path><path d="M510 456v-1c4-4 9-7 14-7 1 1 1 2 1 3-1 0-2 1-4 1v2c-1 0-1 0-2 1l-3 3c1 2 0 3 0 5 1 0 1 1 1 1 1 2 2 4 3 4h4l2 2h0 0c1-1 1-3 0-4 0-1 1-1 1-2 1 2 2 2 2 4 0 3-1 4-3 6-2 1-3 1-5 1l-3-1c-4-2-5-5-7-9h1c-1-3-2-5-2-7v-2z" class="M"></path><path d="M513 459c0-1 1-2 1-3 2-2 5-3 7-4v2c-1 0-1 0-2 1l-3 3-1 2h-2v-1z" class="S"></path><path d="M512 465c1 1 2 2 3 4l3 3h0c3 2 5 2 8 2-2 1-3 1-5 1l-3-1c-4-2-5-5-7-9h1z" class="X"></path><path d="M513 459v1h2l1-2c1 2 0 3 0 5 1 0 1 1 1 1 1 2 2 4 3 4h4l2 2h0c-1 1-2 1-3 2-2 0-4-1-6-2-3-3-4-7-4-11z" class="W"></path><path d="M527 446c1 1 2 1 4 1h1l2 1 3 2 6 3 4 3-1 1-3 5c-2 2-3 5-5 8v2c-1 0-2 1-2 1l-7 4h0l-3 2-3-1c-2-1-3-1-5-2-1-1-2-2-3-2 0-1-1-1-1-1h1c-1-2-2-3-3-5 0 0-1-1-1-2v-1c2 4 3 7 7 9l3 1c1 1 2 1 4 0h0c2 0 3 0 4-2 2-1 2-3 2-5v-2h1l1 2v2h1c1-1 2-2 3-4h0l2-2c1-2 2-3 3-5-4-2-8-4-12-4-2-1-5-1-7-1h-2 0v-2c2 0 3-1 4-1 0-1 0-2-1-3h2c1-1 1-1 1-2z" class="d"></path><path d="M543 453l4 3-1 1-6-2c1-1 2-1 3-2z" class="Z"></path><path d="M535 451c0-1 1-1 2-1l6 3c-1 1-2 1-3 2l-5-3h0v-1z" class="N"></path><path d="M527 446c1 1 2 1 4 1h1l2 1 3 2c-1 0-2 0-2 1v1h0l-5-1h-5c0-1 0-2-1-3h2c1-1 1-1 1-2z" class="Z"></path><path d="M530 449c2 0 3 1 5 2v1h0l-5-1s-2-1-2-2h2z" class="E"></path><path d="M524 448h2c2 0 3 1 4 1h-2c0 1 2 2 2 2h-5c0-1 0-2-1-3z" class="U"></path><defs><linearGradient id="t" x1="505.669" y1="221.025" x2="462.709" y2="261.59" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#t)" d="M478 203c0 1 0 1 1 2h0 1 1l2-1v4c-1 0-2 0-3 1 1 1 3 0 5 1l7 1 7 3 4 2-2-3h2l3 2h0v-1-1h2c1 0 2 1 2 1l3 3h1l2 2 3 3c0 1-1 2-1 2 0 1-1 1-1 2l4 6v1h-2c-1 0-2 1-3 1-1 1-3 0-3 1-1 1-2 2-3 2h-1c2 1 4 4 6 5 1 1 2 1 3 1l1 1h-2 0l1 3c-6-2-11-2-17 1h-1c-2 2-3 3-4 5l-3 3h0c-1 1-2 3-3 4l-2 2-5 4-1-1v-1c-2-2-3-4-4-7l-1-2c0-2 1-2 1-4l3-3c0-2 2-4 3-4l3-1v-2h-1 0l-5 2c-2 1-4 3-5 5l-1 1c-2 8-1 17-1 26v10-2c-2 0-5 1-6 2h-1 1c0 1 0 2-1 3v-28-16c0-2-1-6 0-8 2-2 7-2 9-3 13-2 25-4 38-4v-3l-23-1h-13-5-14-4 1c1-1 2-1 3-1h1c0-1-1-2-1-3l1-1v3h0c0-1 0-2 1-3l2-3v-1c1-2 3-3 4-5l1-1 4-3c1 0 3-1 4-2l-1-1c1 0 2 0 3-1z"></path><defs><linearGradient id="u" x1="468.69" y1="266.752" x2="471.275" y2="283.728" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#u)" d="M467 260h0l1 7 5 2v-9-2c1 1 1 21 1 25-2 0-5 1-6 2h-1 1c0 1 0 2-1 3v-28z"></path><path d="M509 237c2 1 4 4 6 5 1 1 2 1 3 1l1 1h-2 0l1 3c-6-2-11-2-17 1h-1c-2 2-3 3-4 5l-3 3h0c-1 1-2 3-3 4l-2 2-5 4-1-1v-1c-2-2-3-4-4-7 1 1 2 6 5 6 1-1 3-2 4-4h0c1-1 1-1 1-2h0c1-1 1-1 0-2 0 0 0-1-1-1v-1h0c0-2 1-4 0-6h0l2-2v5l1 1 1-2h0v-4c1-1 4-3 6-4l1-1c3-1 6-1 9 0 0 1 0 0 1 0v-2l1-1z" class="W"></path><path d="M498 247c1 1 1 1 2 1-2 2-3 3-4 5l-3 3h0l-1-1 6-8z" class="B"></path><path d="M492 255l1 1c-1 1-2 3-3 4l-2 2-5 4-1-1 10-10z" class="O"></path><defs><linearGradient id="v" x1="505.782" y1="239.44" x2="507.731" y2="247.375" xlink:href="#B"><stop offset="0" stop-color="#adacad"></stop><stop offset="1" stop-color="#cdccce"></stop></linearGradient></defs><path fill="url(#v)" d="M509 237c2 1 4 4 6 5 1 1 2 1 3 1l1 1h-2 0l1 3c-6-2-11-2-17 1h-1c-1 0-1 0-2-1 3-3 7-4 11-4-1-2-1-4-1-5l1-1z"></path><path d="M478 203c0 1 0 1 1 2h0 1 1l2-1v4c-1 0-2 0-3 1 1 1 3 0 5 1l7 1 7 3 4 2-2-3h2l3 2h0v-1-1h2c1 0 2 1 2 1l3 3h1l2 2 3 3c0 1-1 2-1 2 0 1-1 1-1 2l4 6v1h-2c-1 0-2 1-3 1-1 1-3 0-3 1-1 1-2 2-3 2h-1l-1 1v2c-1 0-1 1-1 0-3-1-6-1-9 0l-1 1c-1 0-2 0-3 1l-1 1-1-1h-1c-1 1-2 1-3 1 0-1-1-2-1-2 1-1 1-1 1-2-2 0-3 1-4 1v-2 1h1c4-1 7-3 11-4 3-1 5-1 7-2h5c1-1 2-1 3-1h2c1-1 1-2 1-3v-3l-23-1h-13-5-14-4 1c1-1 2-1 3-1h1c0-1-1-2-1-3l1-1v3h0c0-1 0-2 1-3l2-3v-1c1-2 3-3 4-5l1-1 4-3c1 0 3-1 4-2l-1-1c1 0 2 0 3-1z" class="h"></path><path d="M501 213h2l3 2c1 1 1 2 2 3l-5-2-2-3zm-38 3c1-2 3-3 4-5l1 2h1 1c-1 2-2 2-3 4s-3 5-5 7l-2-1c0-1 0-2 1-3l2-3v-1z" class="H"></path><path d="M463 216c1-2 3-3 4-5l1 2h1c-2 1-3 3-4 4-2 1-2 3-3 4l-1-1 2-3v-1z" class="D"></path><path d="M508 213c1 0 2 1 2 1l3 3h1l2 2 3 3c0 1-1 2-1 2 0 1-1 1-1 2l-1-1c-3-3-5-5-8-7-1-1-1-2-2-3h0v-1-1h2z" class="B"></path><path d="M516 219l3 3c0 1-1 2-1 2 0 1-1 1-1 2l-1-1c1-1 1-1 1-2l-2-2 1-2z" class="U"></path><path d="M508 213c1 0 2 1 2 1l3 3h1l2 2-1 2c-2-3-6-5-9-7v-1h2z" class="M"></path><path d="M478 203c0 1 0 1 1 2h0 1 1l2-1v4c-1 0-2 0-3 1 1 1 3 0 5 1-2 0-5-1-7 0-4 1-7 4-11 7 1-2 2-2 3-4h-1-1l-1-2 1-1 4-3c1 0 3-1 4-2l-1-1c1 0 2 0 3-1z" class="J"></path><path d="M478 203c0 1 0 1 1 2h0c-3 2-5 5-8 7 0 0-1 0-1 1h-1-1l-1-2 1-1 4-3c1 0 3-1 4-2l-1-1c1 0 2 0 3-1z" class="V"></path><path d="M467 211l1-1c1 1 2 1 3 2 0 0-1 0-1 1h-1-1l-1-2z" class="Y"></path><path d="M462 224v1h4c1-2 1-3 2-5 3-4 8-6 13-8h4c3 0 7 1 10 2 3 2 7 3 10 5s5 4 7 6c1 1 1 1 2 1l-23-1h-13-5-14-4 1c1-1 2-1 3-1h1c0-1-1-2-1-3l1-1v3h0l2 1z" class="W"></path><path d="M489 219c3 2 5 3 7 6l-5-1-5-2c1-1 2-1 3-3z" class="H"></path><path d="M473 224h-2v-1-1h1v-1l1-1v-1h1c1 0 2 0 2-1 1 0 2-1 3-1 0 0 1 0 1 1h5l4 1c-1 2-2 2-3 3l5 2v1h-13 0-5v-1z" class="R"></path><path d="M482 221h-4l-1-1c1 0 2-1 3-1h2c1 1 0 1 0 2z" class="D"></path><path d="M485 218l4 1c-1 2-2 2-3 3h-1c-1-1-1-1-3-1 0-1 1-1 0-2h-2c1-1 4-1 5-1z" class="G"></path><path d="M473 224s1-1 2-1c1-1 1-1 2-1 0-1 1-1 2 0h0c2 1 5 1 6 0h1l5 2v1h-13 0-5v-1z" class="h"></path><path d="M570 188c1 1 2 1 3 2h0l1 1v5l-1 1c-1 2-1 4-2 6v1c1 1 2 0 3 1h0-1l1 1 2 1 5 1c1 0 2 1 3 1s2 1 2 2h0l2 1h0c1 2 0 3-1 5s-3 7-2 10l1 2c1 1 1 2 1 3 1 1 2 2 4 2l1 2c1 1 1 1 2 1v1l2 1 1 2h1c0 1 1 2 1 3h1 1v-1l1 1 1-1 2 4 6 9v3 3l3 1h0c-1 4 0 7-4 9h-1c-1 1-3 3-4 5-3-6-7-13-11-19h-1 0c0-1-1-2-2-3 0-1 0-1-1-2s-1-3-3-5l-6-10c-1-1-1-2-2-3l-2-2c-1-2-1-3-2-5l-1-3-5 10h0l-1 1c-1 2-2 5-3 8l-1 1c0 2 0 4-1 5l-3-1v-5-1c-1 0-5 2-7 2v-1-1l3-3h-2c-3 1-5 2-8 2-1-1-2-1-3-2-3-2-5-3-7-6h0c2-6-2-12 2-18 2-2 4-4 6-5 1 0 2-1 3-1 1 1 2 2 2 3 2 1 2 1 4 1v-1h0v-4c2 2 4 6 6 8 1-1 1-2 1-2v-3c0-5 5-19 9-22l1-2z" class="k"></path><path d="M574 215v-1l5-1c-2 3-3 6-4 9l-1-1c0-1 0-2 1-3v-3h-1z" class="I"></path><path d="M569 208c2 1 4 1 6 1l1 1c1 0 1 0 2-1v1h0c-2 2-2 2-4 2l-1-1h-1-3c-1 0-2 0-4 1h-1-1l1-2c2-1 3-1 4-1l1-1z" class="W"></path><path d="M586 211l2 1h0c1 2 0 3-1 5s-3 7-2 10l1 2c1 1 1 2 1 3-2-1-2-3-3-4v-1c-1-5 0-11 2-16z" class="U"></path><path d="M579 234c-2-2-3-5-4-8v-1c2-5 5-9 6-13l1-1h1c0 2-1 5-1 7-1 2-1 4-1 6 1 1 1 2 1 3l-1-2h-1l-1 2c-1 1 0 6 0 7zm-9-20h1 0l2 2h0v1l-2 5-3 3h0-1c0-1 0-1-1-1v-1l-4 3-1 1c-1-1-2-1-3-2h0c1-2 3-3 3-4l9-7z" class="C"></path><path d="M570 214h1 0l2 2h0v1l-2 5-2-2c1-2 2-3 2-5v-1h-1z" class="S"></path><path d="M566 223c1-1 1-3 3-4v1l2 2-3 3h0-1c0-1 0-1-1-1v-1z" class="I"></path><path d="M574 215h1v3c-1 1-1 2-1 3l1 1-7 14c-1 2-2 5-3 8l-1 1c0 2 0 4-1 5l-3-1v-5-1c-1 0-5 2-7 2v-1-1l3-3h-2c2-1 4-3 6-5 2-3 5-7 8-10h0l3-3 2-5v-1l1-1z" class="L"></path><path d="M556 240c1-1 2-1 4-2v1c0 1-1 1-2 2l-5 3v-1l3-3z" class="i"></path><path d="M558 241c2 1 2 1 4 1l1-1v3h1v1c0 2 0 4-1 5l-3-1v-5-1c-1 0-5 2-7 2v-1l5-3z" class="V"></path><path d="M574 215h1v3c-1 1-1 2-1 3l1 1-7 14c-1 2-2 5-3 8l-1 1v-1h-1v-3-2-1c1-1 1-2 2-4l6-8-3-1 3-3 2-5v-1l1-1z" class="R"></path><path d="M573 217l1 1c0 2 0 4-2 6l-1 1v1l-3-1 3-3 2-5z" class="Z"></path><path d="M579 227l1-2h1l1 2v1h0c1 1 1 1 1 2 1 1 2 3 3 4h1c0-2-2-2-3-4v-1-1c1 1 1 3 3 4 1 1 2 2 4 2l1 2c1 1 1 1 2 1v1l2 1 1 2v3 1h-1v2 2c-1 0-1 0-1-1v1h-1l5 8c-1 0-2-1-3-2-1-2-2-3-3-4h-1v3 1c-1-2-3-5-4-7l-6-9c-1-1-2-3-3-5 0-1-1-6 0-7z" class="Q"></path><path d="M582 228h0c1 1 1 1 1 2 1 1 2 3 3 4h1c0-2-2-2-3-4v-1-1c1 1 1 3 3 4 1 1 2 2 4 2l1 2v1c0 1 0 2 1 3v5c-1-2-1-3-2-4l-3-6c-1-1-2-1-3-1 0-1-1-2-2-3 0 0-1 0-1-1v-2z" class="S"></path><path d="M592 236c1 1 1 1 2 1v1l2 1 1 2v3 1h-1v2 2c-1 0-1 0-1-1v1h-1c0-1-1-2-1-3h-2l-2-2 1-1h1c-1-1-1-2-2-2s-1-1-1-1c1 0 1 0 2 1h1c1 1 1 2 2 4v-5c-1-1-1-2-1-3v-1z" class="R"></path><path d="M594 238l2 1 1 2v3 1h-1c-1-1-1-3-2-5v-1c-1 0 0-1 0-1z" class="I"></path><defs><linearGradient id="w" x1="581.208" y1="238.419" x2="595.469" y2="250.318" xlink:href="#B"><stop offset="0" stop-color="#565656"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#w)" d="M579 234c0-1-1-6 0-7 0 0 0 3 1 3v3l1 1h2c1 1 1 1 2 1 0 1 0 1 1 1 1 1 2 1 2 3v1s0 1 1 1 1 1 2 2h-1l-1 1 2 2h2c0 1 1 2 1 3l5 8c-1 0-2-1-3-2-1-2-2-3-3-4h-1v3 1c-1-2-3-5-4-7l-6-9c-1-1-2-3-3-5z"></path><path d="M570 188c1 1 2 1 3 2h0l1 1v5l-1 1c-1 2-1 4-2 6v1c1 1 2 0 3 1h0-1l1 1 2 1 2 1h-3-1-5l-1 1c-1 0-2 0-4 1l-1 2h1 1c2-1 3-1 4-1h3c-4 2-8 4-11 7l-2 2h-1c0-1 0-1-1-2h-4c0-1 1-1 2-1h1v-1h-1 1l-3-3h0v-4c2 2 4 6 6 8 1-1 1-2 1-2v-3c0-5 5-19 9-22l1-2z" class="Q"></path><path d="M557 218h3 1l-2 2h-1c0-1 0-1-1-2z" class="W"></path><path d="M569 192l1 1-5 9c2 1 4 2 4 4h5l2 1 2 1h-3-1-5l-1 1c-1 0-2 0-4 1l-1 2c0 1-1 1-1 2h-1c0-2 0-4 1-5 2-6 4-11 7-17z" class="h"></path><path d="M570 188c1 1 2 1 3 2h0l1 1v5l-1 1c-1 2-1 4-2 6v1c1 1 2 0 3 1h0-1l1 1h-5c0-2-2-3-4-4l5-9-1-1v-1-1l1-2z" class="W"></path><defs><linearGradient id="x" x1="603.211" y1="252.827" x2="597.805" y2="273.072" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#x)" d="M597 241h1c0 1 1 2 1 3h1 1v-1l1 1 1-1 2 4 6 9v3 3l3 1h0c-1 4 0 7-4 9h-1c-1 1-3 3-4 5-3-6-7-13-11-19l-2-3v-1-3h1c1 1 2 2 3 4 1 1 2 2 3 2-2-3-3-5-5-8h1v-1c0 1 0 1 1 1v-2-2h1v-1-3z"></path><path d="M598 259l5 2 2-1v1c0 2-2 2-3 3-2-1-4-3-4-5z" class="M"></path><path d="M612 263h0l1 1c-1 3-2 5-5 7h-1 0c1-3 3-6 5-8z" class="U"></path><path d="M605 247l6 9v3 3h0c0-1 0-1-1-2-1-2-1-3-2-4v-1c-1 0-1-1-2-1v-1c-1-2-1-4-1-6z" class="H"></path><path d="M601 244v-1l1 1 1 5 2 1c0 1-1 5 0 6v4l-2 1-5-2h0c0-1-2-3-2-4 1 1 2 2 3 2l1 1c1-2 1-3 2-4 1-2 1-4 0-6l-1-1v-3z" class="F"></path><path d="M601 244v-1l1 1 1 5c0 3 0 6-1 10h-1l-1-1c1-2 1-3 2-4 1-2 1-4 0-6l-1-1v-3z" class="W"></path><path d="M597 241h1c0 1 1 2 1 3h1 1v3l1 1c1 2 1 4 0 6-1 1-1 2-2 4l-1-1-5-8h1v-1c0 1 0 1 1 1v-2-2h1v-1-3z" class="Q"></path><path d="M597 241h1c0 1 1 2 1 3h1 1v3c-2 0-3-2-4-3v-3z" class="J"></path><path d="M597 252v-2l1-1h1c1 1 1 2 1 3s0 1-1 2c-1 0-1-1-2-2z" class="T"></path><path d="M601 247l1 1c1 2 1 4 0 6-1 1-1 2-2 4l-1-1-5-8h1c1 0 1 2 2 3s1 2 2 2c1-1 1-1 1-2 1-1 1-3 0-4h0l1-1z" class="S"></path><path d="M544 211c1 0 2-1 3-1 1 1 2 2 2 3 2 1 2 1 4 1v-1l3 3h-1 1v1h-1c-1 0-2 0-2 1h4c1 1 1 1 1 2h1c1 1 1 1 2 1 0 1-2 2-3 4h0c1 1 2 1 3 2l1-1 4-3v1c1 0 1 0 1 1h1c-3 3-6 7-8 10-2 2-4 4-6 5-3 1-5 2-8 2-1-1-2-1-3-2-3-2-5-3-7-6h0c2-6-2-12 2-18 2-2 4-4 6-5z" class="Q"></path><path d="M541 225c0-1-1-1-1-2v-1h0c1 0 1 0 2 1v1l-1 1z" class="C"></path><path d="M544 239s-1 0-1-1h-1c-1 0-2-2-4-3 0-2 0-4 1-6 1 1 1 2 2 2 0 1 1 2 1 2h-1l1 4c1 0 2 1 2 2z" class="e"></path><path d="M544 211h3s1 0 1 1v1c-1 0-2 0-3 1-1 0-1 1-2 1-2 1-2 4-4 5-1-1-1-2-1-4 2-2 4-4 6-5z" class="J"></path><defs><linearGradient id="y" x1="552.331" y1="239.749" x2="546.654" y2="229.133" xlink:href="#B"><stop offset="0" stop-color="#565455"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#y)" d="M544 239c0-1-1-2-2-2l-1-4h1c1 1 2 1 2 1 2 1 2 0 4 1h1 0c2 1 3 1 5 0h1 5c-2 2-4 4-6 5-3 1-5 2-8 2l1-1h-1l2-1h0c-1 0-3-1-4-1z"></path><path d="M545 225h1v1c1 1 1 1 2 1l1-1 2 2c0 1 2 2 2 3l1-1h0v-1c1-1 1-1 2-1 2 1 0 2 3 2l1-1h2l-1 1-1 1-1 2c-1 1-3 1-5 1h0c-2-1-3-1-5-1-3-1-7-4-9-7l1-1 1-1 2 3c1-1 1-1 1-2z" class="S"></path><path d="M545 225h1v1c1 1 1 1 2 1l1-1 2 2c0 1 2 2 2 3h-1c-3 0-6-2-8-4 1-1 1-1 1-2z" class="B"></path><path d="M549 213c2 1 2 1 4 1v-1l3 3h-1 1v1h-1c-1 0-2 0-2 1h-2c-1 1-2 2-2 4-1 1 0 2 0 4l-1 1c-1 0-1 0-2-1v-1h-1c0 1 0 1-1 2l-2-3v-1h0v-3c1-2 3-4 6-5-1-1-1-1 0-1l1-1z" class="G"></path><path d="M542 223l1 1c2 0 1-1 1-3l1-1 2-2h1c-2 2-3 2-3 5v2c0 1 0 1-1 2l-2-3v-1h0z" class="H"></path><path d="M549 213c2 1 2 1 4 1v-1l3 3h-1l-7-1c-1-1-1-1 0-1l1-1z" class="S"></path><path d="M548 218c1 0 2-1 3 0h0c-1 1-2 2-2 4-1 1 0 2 0 4l-1 1c-1 0-1 0-2-1v-1h-1v-2c0-3 1-3 3-5z" class="L"></path><path d="M553 218h4c1 1 1 1 1 2h1c1 1 1 1 2 1 0 1-2 2-3 4h0c1 1 2 1 3 2l1-1 1 1-1 1c0 1 1 1 0 2 0 1-1 1-2 1l1-1 1-1h-2l-1 1c-3 0-1-1-3-2-1 0-1 0-2 1v1h0l-1 1c0-1-2-2-2-3l-2-2c0-2-1-3 0-4 0-2 1-3 2-4h2z" class="C"></path><path d="M555 227v-1-2h1 1l1 1c1 1 2 1 3 2l1-1 1 1-1 1h0c-3 0-4 0-7-1z" class="E"></path><path d="M551 223c2-2 4-3 6-3l1 1c0 1-1 2-1 3h-1-1v2 1c-1-1-2-1-3-3 0 0 0-1-1-1z" class="I"></path><path d="M552 224l1-1 3-1v2h-1v2 1c-1-1-2-1-3-3z" class="c"></path><path d="M553 218h4c1 1 1 1 1 2h1c1 1 1 1 2 1 0 1-2 2-3 4h0l-1-1c0-1 1-2 1-3l-1-1c-2 0-4 1-6 3h-2v-1c0-2 1-3 2-4h2z" class="d"></path><defs><linearGradient id="z" x1="558.537" y1="434.359" x2="534.707" y2="427.395" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#3a3939"></stop></linearGradient></defs><path fill="url(#z)" d="M526 385c1-1 1-1 2-1l1 2c0 1-1 1-2 1v1c1 0 1 0 2 1h0v1l3 1 1 1 1-1 1-1c1 0 1 1 1 1 2 4 5 6 7 9 1 2 3 3 4 4s1 2 2 3c0 0 0 1 1 1s2 0 3-1l-3-3h3 0l1-1 5 4c1 1 3 1 4 2 0 0 0 1 1 2h0v3l2 1 1 3 1 2s-1 1-1 2v1c0 1-1 1-1 3 0 1 1 3 1 4l-2 3c1 0 2 1 3 1l-1 1c-1 1-4 3-5 4l-5 4-2 3-5 5-1 2-2 3-4-3-6-3-3-2-2-1h-1c-2 0-3 0-4-1l-2-2v-1-2l-5-5c-2-3-3-6-5-9l-1-1c0-1-1-2-1-3h0v-1h0-1l5-27c0-1 0-2 1-3v-1h1c0-1 2-2 3-3 2-1 3-2 4-3z"></path><path d="M544 423c1 1 1 1 2 1-2 1-3 2-4 3l-2 4s0 1-1 1v-4c1-2 3-4 5-5z" class="H"></path><path d="M542 444c3 0 5 2 8 2h5l-5 5v-1c-1-1-2-2-4-3l-4-3z" class="P"></path><defs><linearGradient id="AA" x1="542.708" y1="439.97" x2="558.303" y2="434.343" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#c2c0c2"></stop></linearGradient></defs><path fill="url(#AA)" d="M562 436c1-1 2-3 3-3s2 1 3 1l-1 1c-1 1-4 3-5 4l-5 4c0-1 0-1-1-2-4 2-7 1-11 0-4-2-4-5-5-8h0c1 0 1 0 1 1 2 2 3 4 6 5 2 1 5 1 7-1h1 0c2 0 3-2 4-3v1h3z"></path><path d="M562 436c1-1 2-3 3-3s2 1 3 1l-1 1c-1 1-4 3-5 4l-5 4c0-1 0-1-1-2 3-1 5-3 6-5z" class="C"></path><path d="M530 427c1-2 3-5 4-7 2-3 4-4 7-5h5l-2 2h0c-3 1-7 3-8 6-1 1-2 3-2 4 0 4 0 7 2 11v1c-3-1-3-1-5 0-1-1-1-3-3-3-1-1-2-1-2-2v-1l2 1h0 2v-1-1h1v-1h-1v-2-1-1z" class="D"></path><path d="M528 436l1-1h2 0c1 0 1 0 2-1v-1-1c0-1-1-2 0-3 0-1 1-1 1-2 0 4 0 7 2 11v1c-3-1-3-1-5 0-1-1-1-3-3-3z" class="O"></path><path d="M536 438l4 4 2 2 4 3c2 1 3 2 4 3v1l-1 2-2 3-4-3-6-3-3-2c-1-1-1-2-1-2v-1c-2-1-3-3-5-5 0 0-1 0-1-1h0 1c1 0 3 1 4 2h0 1l-2-2c2-1 2-1 5 0v-1z" class="S"></path><path d="M536 438l4 4h0l-1 1v1c-1 0-1 1-1 1l-5-4-2-2c2-1 2-1 5 0v-1z" class="M"></path><path d="M538 445s0-1 1-1v-1l1-1h0l2 2 4 3c2 1 3 2 4 3v1l-1 2c-1-2-6-4-8-5l-1-1-2-2z" class="B"></path><path d="M538 445s0-1 1-1v-1l1-1h0l2 2 4 3h0c-2 0-3 0-4-1l-1 2-1-1-2-2z" class="D"></path><path d="M540 444l2 2-1 2-1-1v-3z" class="H"></path><path d="M538 445s0-1 1-1v-1l1-1-1 1 1 1v3l-2-2z" class="B"></path><path d="M544 423c2 0 4 0 6 1h1c0 1 1 1 1 2h1l-1-2h0l1 2c1-1 2-1 2-1 1 3 2 5 2 8v1l-2 3v1h0-1c-2 2-5 2-7 1-3-1-4-3-6-5 0-1 0-1-1-1h0-1v-1c1 0 1-1 1-1l2-4c1-1 2-2 4-3-1 0-1 0-2-1z" class="d"></path><path d="M544 423c2 0 4 0 6 1v1l1 1c1 1 1 4 1 6 0 1-1 2-2 2s-2 0-3-1v-2c0-1 1-1 2-1h1c0-1-1-3-2-4 0-1-1-1-2-2-1 0-1 0-2-1z" class="F"></path><path d="M542 427c0 1 1 2 0 3v2l2 2 1-1h0c0 2 0 2 1 3 1 0 1 0 2 1 1 0 3-1 4-1s1 1 2 1h1v1h0-1c-2 2-5 2-7 1-3-1-4-3-6-5 0-1 0-1-1-1h0-1v-1c1 0 1-1 1-1l2-4z" class="C"></path><path d="M554 403l5 4c1 1 3 1 4 2 0 0 0 1 1 2h0v3l2 1 1 3 1 2s-1 1-1 2v1c0 1-1 1-1 3 0 1 1 3 1 4l-2 3c-1 0-2 2-3 3h-3v-1c-1 1-2 3-4 3v-1l2-3v-1c0-3-1-5-2-8-1-2-2-4-3-5-3-3-5-3-8-3h0 5c1 0 1 0 2-1h0 0c1 0 1 0 2-1l2 2v-2h0v-1c-1 0-5-2-5-3-1 0 0-1 0-2v-1c1 0 2 0 3-1l-3-3h3 0l1-1z" class="c"></path><path d="M553 415l2 2c0 1 1 3 2 4v1h-1c-1-2-3-4-5-6h0c1 0 1 0 2-1z" class="V"></path><path d="M555 417v-2c0 1 1 2 2 3s1 2 1 3l1 1c1-1 2-2 2-3 1 1 1 1 1 2v3 1h0v2h-1c-2-2-2-4-4-6h0c-1-1-2-3-2-4z" class="C"></path><path d="M563 420l1-1 3 3v1c0 1-1 1-1 3 0 1 1 3 1 4l-2 3c-1 0-2 2-3 3h-3v-1c-1 1-2 3-4 3v-1l2-3c1-2 1-3 2-5h1 1c1-1 2-1 2-1v-3h-1 0v-1-3c1 0 1 0 1-1z" class="M"></path><path d="M562 421c1 0 1 0 1-1 1 2 1 3 2 5h-1 0-2v-1-3z" class="G"></path><path d="M563 420l1-1 3 3v1c0 1-1 1-1 3l-1-1c-1-2-1-3-2-5z" class="K"></path><path d="M559 435l2-2c0-1 0-1 1-2 0-1 1-1 1-2l1 1c0 1-2 3-2 4v2h-3v-1z" class="L"></path><path d="M554 403l5 4c1 1 3 1 4 2 0 0 0 1 1 2h0v3l2 1 1 3 1 2s-1 1-1 2l-3-3-1 1c0 1 0 1-1 1 0-1 0-1-1-2 0 1-1 2-2 3l-1-1c0-1 0-2-1-3s-2-2-2-3h0v-1c-1 0-5-2-5-3-1 0 0-1 0-2v-1c1 0 2 0 3-1l-3-3h3 0l1-1z" class="X"></path><path d="M560 410c0 1 0 1-1 1-1-1-2-1-2-2v-1l1 1c1 0 2 1 2 1z" class="P"></path><path d="M550 404h3v2h2 1l-2 2-1-1-3-3zm9 3c1 1 3 1 4 2 0 0 0 1 1 2-1 0-3 0-4-1 0 0-1-1-2-1l1-2z" class="c"></path><path d="M562 416v-1c1 0 2 0 2 1 1 1 1 2 2 3 1 0 1 0 1-1l1 2s-1 1-1 2l-3-3c-1-1-2-2-2-3z" class="N"></path><path d="M557 412c2 1 3 1 4 3l1 1c0 1 1 2 2 3l-1 1c0 1 0 1-1 1 0-1 0-1-1-2v-1c-1-2-2-4-4-6z" class="b"></path><path d="M553 407l1 1 1 1 2 3c2 2 3 4 4 6v1c0 1-1 2-2 3l-1-1c0-1 0-2-1-3s-2-2-2-3h0v-1c-1 0-5-2-5-3-1 0 0-1 0-2v-1c1 0 2 0 3-1z" class="h"></path><path d="M553 407l1 1 1 1h-5v-1c1 0 2 0 3-1z" class="C"></path><path d="M534 391l1-1c1 0 1 1 1 1 2 4 5 6 7 9 1 2 3 3 4 4s1 2 2 3c0 0 0 1 1 1v1c0 1-1 2 0 2 0 1 4 3 5 3v1h0v2l-2-2c-1 1-1 1-2 1h0 0c-1 1-1 1-2 1h-5l2-2h-5c-3 1-5 2-7 5-1 2-3 5-4 7-1 0-2-2-3-3-3-6-3-12-2-18 0-2 0-4 1-5 1-4 4-7 7-9l1-1z" class="W"></path><path d="M549 415c1 1 1 1 2 1h0 0c-1 1-1 1-2 1h-5l2-2h-5c2-1 4-1 6 0h2z" class="B"></path><path d="M549 415h0c-2-2-4-3-6-4h0c1-1 2-1 3 0 2 1 5 2 7 4-1 1-1 1-2 1s-1 0-2-1z" class="D"></path><path d="M537 396h1c0 1 1 1 1 2l1 1c0 1 1 2 0 3s-2 1-3 2c-1 0-1 0-2 1l-1 1v-1l1-1c1-1 2-2 3-4h0 1c0-1-1-1-1-2v-1c-1 0-2 1-3 2h-1l3-3z" class="I"></path><defs><linearGradient id="AB" x1="535.125" y1="418.416" x2="514.09" y2="416.046" xlink:href="#B"><stop offset="0" stop-color="#373636"></stop><stop offset="1" stop-color="#605f60"></stop></linearGradient></defs><path fill="url(#AB)" d="M526 385c1-1 1-1 2-1l1 2c0 1-1 1-2 1v1c1 0 1 0 2 1h0v1l3 1 1 1c-3 2-6 5-7 9-1 1-1 3-1 5-1 6-1 12 2 18 1 1 2 3 3 3v1 1 2h1v1h-1v1 1h-2 0l-2-1v1c0 1 1 1 2 2 2 0 2 2 3 3l2 2h-1 0c-1-1-3-2-4-2h-1 0c0 1 1 1 1 1 2 2 3 4 5 5v1s0 1 1 2l-2-1h-1c-2 0-3 0-4-1l-2-2v-1-2l-5-5c-2-3-3-6-5-9l-1-1c0-1-1-2-1-3h0v-1h0-1l5-27c0-1 0-2 1-3v-1h1c0-1 2-2 3-3 2-1 3-2 4-3z"></path><path d="M514 426l4-4c1-2 0-4 0-6 1 2 1 7 1 9l-1 1s-1 0-1-1h-1c-1 1-1 2-1 2l-1-1z" class="R"></path><path d="M525 441l7 6h-1c-2 0-3 0-4-1l-2-2v-1-2z" class="M"></path><path d="M522 399v6l2 1c-2 1-1 3-2 5 0 2 2 3 1 5-1 0-1-1-2-1v-1h-1c0-5 0-10 2-15z" class="V"></path><path d="M526 385c1-1 1-1 2-1l1 2c0 1-1 1-2 1v1c1 0 1 0 2 1h0v1l-2 2v-1-1l-1-1c-1 0-5 3-5 3l-1 1v-1c0-2 1-3 2-4 2-1 3-2 4-3z" class="T"></path><defs><linearGradient id="AC" x1="526.865" y1="390.717" x2="524.733" y2="404.867" xlink:href="#B"><stop offset="0" stop-color="#acaaac"></stop><stop offset="1" stop-color="#cbcaca"></stop></linearGradient></defs><path fill="url(#AC)" d="M529 390l3 1 1 1c-3 2-6 5-7 9-1 1-1 3-1 5h-1l-2-1v-6c1-3 3-5 5-7l2-2z"></path><path d="M524 406h1c-1 6-1 12 2 18 1 1 2 3 3 3v1 1 2h1v1h-1v1 1h-2 0l-2-1v1c-1-2-3-6-2-8-2-4-3-8-4-12h1v1c1 0 1 1 2 1 1-2-1-3-1-5 1-2 0-4 2-5z" class="B"></path><path d="M527 424c1 1 2 3 3 3v1 1 2h0l-2-1c-1-1-1-4-1-6z" class="F"></path><path d="M524 426l5 7-1 1-2-1v1c-1-2-3-6-2-8z" class="e"></path><defs><linearGradient id="AD" x1="288.769" y1="65.585" x2="307.789" y2="97.743" xlink:href="#B"><stop offset="0" stop-color="#464444"></stop><stop offset="1" stop-color="#696869"></stop></linearGradient></defs><path fill="url(#AD)" d="M302 55c1 1 1 1 1 2 1 0 1-1 2-1 2 1 4 3 6 4l4 4v3c-1 1-2 1-2 2l-1 2 1 1h0c2-1 3-1 5 0h0v1h3c1 1 1 2 1 2v2 5c1 7-1 13-3 20h0l-1 1c2 0 2 0 3 1l1 1h-1l-1 1-1 1c-1 2-2 3-3 4l-4 6c0 1 0 1-1 1l-1 1h-1-1l-2 2v-4l-1 1c-1 1-2 1-4 2 0 2 1 1 0 3h0c0-2-1-2-2-2 0 2-1 3-2 4s-2 1-2 2c-1 0-1 0-1-1-1 1-1 2-2 2v1 1 3l-7 5c-2 2-5 3-8 5-5 3-10 6-15 8-1 0-2 0-3-1l-1 1c-1-1-1-2 0-2v-1-2l1-3c0-3 1-5 0-8-1 1-2 1-3 2l-1-1v-1-1-3l1-1c0-2 0-5 1-6l-1-1h1-1v-1h0c9-6 17-13 25-20l1 2 7-7c2-3 4-6 5-9 2-3 3-7 4-10 1-5 3-10 3-15v-1h1v-3-4z"></path><path d="M287 114l-1-1v-1c1-1 1-1 2-1 2-1 3-3 5-4l1-1h0c-2 3-5 6-7 8z" class="R"></path><path d="M305 56c2 1 4 3 6 4l4 4v3c-1 1-2 1-2 2h-1-1 0v-1h-2 0c0 2 0 3-1 4v-6c0-3-3-7-3-10z" class="B"></path><path d="M305 91v1c-1 4-4 8-7 12 0 0-1 2-2 2v1l-3 3c-3 2-5 5-7 7-1 0-3 2-3 2-2 1-3 2-4 4l-2 1c-2 1-3 2-4 5l-1 1h-1v-1h-1-1l-1 1-2 1c-1 1-2 1-3 2s-2 2-4 2h0c-1 1-2 1-3 2l-1-1v-1c2 0 4-2 6-3 2 0 3-1 4-2l6-4 2-1 2-1 5-4 1-1 2-2 3-2 1-1c2-2 5-5 7-8 2 0 2-2 3-3 3-4 6-8 8-12z" class="D"></path><path d="M298 78h0c0 3 1 5 0 7 0 3-1 5-2 8l-3 12c-2 1-2 2-3 3s-2 2-3 2c-2 2-2 3-1 5l-3 2c-1-1-1-4-1-5h-1c0-2 1-6 0-8h1l7-7c2-3 4-6 5-9 2-3 3-7 4-10z" class="T"></path><path d="M282 104l7-7s-1 2-1 3c-1 2-2 3-3 4l-2 2c0 1 0 4-1 6h-1c0-2 1-6 0-8h1z" class="F"></path><defs><linearGradient id="AE" x1="312.529" y1="102.371" x2="303.157" y2="71.526" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#aaa8a8"></stop></linearGradient></defs><path fill="url(#AE)" d="M309 68h2v1h0 1 1l-1 2 1 1h0c2-1 3-1 5 0h0v1h3c1 1 1 2 1 2v2 5h0l-1 3c-1 1-2 3-3 4-2 4-4 5-7 8h-1l-1-1-1 1h0c-3 1-3 5-6 6h-1s-1 1-1 2-1 1-2 2c0 0-1 0-2-1 1 0 2-2 2-2 3-4 6-8 7-12v-1c2-6 3-12 3-19 1-1 1-2 1-4h0z"></path><path d="M310 93h1c-1 1-1 2-2 3l-1 1h0c-3 1-3 5-6 6 1-2 3-3 3-5l1-1 4-4z" class="N"></path><path d="M318 89c-2 4-4 5-7 8h-1l-1-1c1-1 1-2 2-3h-1c0-1 2-2 3-2v1c1 0 2 0 2-1h1c1-1 2-1 2-2z" class="E"></path><path d="M309 68c3 5 0 15-2 20l-1 3-1 1v-1c2-6 3-12 3-19 1-1 1-2 1-4h0z" class="a"></path><path d="M313 72c2-1 3-1 5 0h0v1h3c1 1 1 2 1 2v2 5h0c-1 0-2 1-3 2-1-1-1-1-1-2l1-1v-2h1l1-1v1-1-2-1l-1-1h-1 0c-1 1 0 2-1 2-1 1-2 1-3 1-2-2-2-3-2-5z" class="U"></path><path d="M281 102l1 2h-1c1 2 0 6 0 8h1c0 1 0 4 1 5l-2 2-1 1-5 4-2 1-2 1-6 4c-1 1-2 2-4 2-2 1-4 3-6 3v-1-3l1-1c0-2 0-5 1-6l-1-1h1-1v-1h0c9-6 17-13 25-20z" class="D"></path><path d="M256 130v2h0c1-2 2-3 5-5v-3-1h0c1-2 1-3 2-4-1 4-2 9-2 12v1c-2 1-4 3-6 3v-1-3l1-1z" class="V"></path><path d="M255 135v-1-3 2c2 0 2-1 3-2 1 1 1 1 2 1l1-1v1c-2 1-4 3-6 3z" class="B"></path><defs><linearGradient id="AF" x1="271.335" y1="123.978" x2="264.666" y2="120.961" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#acaaab"></stop></linearGradient></defs><path fill="url(#AF)" d="M271 114c0 2 0 3-1 6v1 1 1h1v3l-6 4c-1 1-2 2-4 2v-1c0-3 1-8 2-12l1-1 4-2 3-2z"></path><path d="M268 116c-1 2-2 5-3 6v-3l-1-1 4-2z" class="O"></path><path d="M264 118l1 1c-2 3-2 7-2 10h1l1-1v1 1c-1 1-2 2-4 2v-1c0-3 1-8 2-12l1-1z" class="M"></path><path d="M281 104c1 2 0 6 0 8h1c0 1 0 4 1 5l-2 2-1 1-5 4-2 1-2 1v-3h-1v-1-1-1c1-3 1-4 1-6l9-9 1-1z" class="U"></path><path d="M271 121l1-2h1v2c0 1-1 1-1 1l-1-1z" class="H"></path><path d="M270 121h1l1 1s1 0 1-1v1l2 2-2 1-2 1v-3h-1v-1-1z" class="E"></path><path d="M273 122l2 2-2 1c-1-1 0-1 0-3z" class="F"></path><path d="M275 112h1c1 0 1 1 2 2 0 1-2 3-3 4h-1-1c-1-2 1-4 2-6z" class="M"></path><path d="M281 104c1 2 0 6 0 8h1c0 1 0 4 1 5l-2 2-1 1c-1-2-1-6-1-8 1-2 1-4 1-7l1-1z" class="b"></path><path d="M282 112c0 1 0 4 1 5l-2 2v-7h1z" class="N"></path><path d="M322 82h0c1 7-1 13-3 20h0l-1 1c2 0 2 0 3 1l1 1h-1l-1 1-1 1c-1 2-2 3-3 4l-4 6c0 1 0 1-1 1l-1 1h-1-1l-2 2v-4l-1 1c-1 1-2 1-4 2 0 2 1 1 0 3h0c0-2-1-2-2-2 0 2-1 3-2 4s-2 1-2 2c-1 0-1 0-1-1-1 1-1 2-2 2v1 1 3l-7 5c-2 2-5 3-8 5-5 3-10 6-15 8-1 0-2 0-3-1l-1 1c-1-1-1-2 0-2v-1-2l1-3c0-3 1-5 0-8h0c2 0 3-1 4-2s2-1 3-2l2-1 1-1h1 1v1h1l1-1c1-3 2-4 4-5l2-1c1-2 2-3 4-4 0 0 2-2 3-2 2-2 4-5 7-7l3-3v-1c1 1 2 1 2 1 1-1 2-1 2-2s1-2 1-2h1c3-1 3-5 6-6h0l1-1 1 1h1c3-3 5-4 7-8 1-1 2-3 3-4l1-3z" class="U"></path><path d="M287 132c0-3 2-6 4-8 1-1 2-2 4-3 0-1 1-1 2-1l3-3c0 1-1 3-1 4 0 2-1 3-2 4s-2 1-2 2c-1 0-1 0-1-1l1-2h-1c-2 0-3 3-4 5-1 1-1 2-3 3z" class="B"></path><path d="M287 132c2-1 2-2 3-3 1-2 2-5 4-5h1l-1 2c-1 1-1 2-2 2v1 1 3l-7 5h-2c1-2 3-4 4-5v-1h0z" class="L"></path><path d="M283 119s2-2 3-2c2-2 4-5 7-7l-1 3c-2 2-3 5-2 8h0s1 0 1-1c2 0 4-2 6-3l-3 3c-1 0-1 0-1 1h-1c-1 1-2 1-2 2h0v-1h-1v-1c-1-1-1 0-2 0-1 1-2 4-2 5v1c0 2-1 2-1 3-1 1-2 2-2 3l-2 2-1-1-1 1-1 2c-1-1-1 0-1-1 1-3 0-6 1-9l2-4c1-2 2-3 4-4z" class="F"></path><path d="M283 119v1c-1 3-1 5-1 7-1 2-2 3-3 4 0 2-1 3-1 4l-1 2c-1-1-1 0-1-1 1-3 0-6 1-9l2-4c1-2 2-3 4-4z" class="I"></path><path d="M302 103c3-1 3-5 6-6h0c-1 2-2 5-3 6h-1c1 1 1 2 2 3v1h1 1c-1 2-2 3-3 5h0c-2 1-3 1-5 1 0 1-1 1-2 2 0 1-1 1-1 2-2 1-4 3-6 3 0 1-1 1-1 1h0c-1-3 0-6 2-8l1-3 3-3v-1c1 1 2 1 2 1 1-1 2-1 2-2s1-2 1-2h1z" class="T"></path><path d="M304 103c1 1 1 2 2 3v1h1 1c-1 2-2 3-3 5h-3l-2-2v-4h1v1-1l3-3z" class="d"></path><path d="M304 103c1 1 1 2 2 3v1c-1 0-2 1-3 1l-2-1v-1l3-3z" class="i"></path><path d="M296 107l1 1c1 0 1 0 1 1-1 2 0 3-1 4v1c0 1 0 1-1 1 0 1-1 0-1 1-1 0-2 2-3 2l-1 2c0 1-1 1-1 1h0c-1-3 0-6 2-8l1-3 3-3z" class="J"></path><path d="M292 113l1-1c0-1 1-1 1-1 1 0 1 0 2 1-1 2-2 4-4 6h0l-1 2c0 1-1 1-1 1h0c-1-3 0-6 2-8z" class="K"></path><defs><linearGradient id="AG" x1="321.141" y1="99.477" x2="305.745" y2="98.809" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#AG)" d="M322 82h0c1 7-1 13-3 20h0l-1 1c2 0 2 0 3 1l1 1h-1l-1 1-1 1c-1 2-2 3-3 4l-4 6c0 1 0 1-1 1l-1 1h-1-1l-2 2v-4l-1 1c-1 1-2 1-4 2 0 2 1 1 0 3h0c0-2-1-2-2-2 0-1 1-3 1-4h0c0-1 0-2 1-3l1 1 1-1c1 0 2-1 2-2h0c1-2 2-3 3-5h-1-1v-1c-1-1-1-2-2-3h1c1-1 2-4 3-6l1-1 1 1h1c3-3 5-4 7-8 1-1 2-3 3-4l1-3z"></path><path d="M321 85v1c0 1 1 1 0 2 0 2-1 3-2 4-3 4-7 8-9 12-1 1-1 2-2 3h-1-1v-1c-1-1-1-2-2-3h1c1-1 2-4 3-6l1-1 1 1h1c3-3 5-4 7-8 1-1 2-3 3-4z" class="F"></path><defs><linearGradient id="AH" x1="317.724" y1="110.755" x2="299.863" y2="110.292" xlink:href="#B"><stop offset="0" stop-color="#a09fa0"></stop><stop offset="1" stop-color="#bcbbba"></stop></linearGradient></defs><path fill="url(#AH)" d="M306 115c1-1 1-2 2-3 2-2 3-4 5-7 2-1 3-3 5-4l1 1-1 1c2 0 2 0 3 1l1 1h-1l-1 1-1 1c-1 2-2 3-3 4l-4 6c0 1 0 1-1 1l-1 1h-1-1l-2 2v-4l-1 1c-1 1-2 1-4 2 0 2 1 1 0 3h0c0-2-1-2-2-2 0-1 1-3 1-4h0c0-1 0-2 1-3l1 1 1 1 1-1h2z"></path><path d="M300 117c0-1 0-2 1-3l1 1 1 1 1-1h2l-3 2c-1 0-1-1-2-2v1l-1 1z" class="B"></path><path d="M318 103c2 0 2 0 3 1l1 1h-1l-1 1-1 1c-1 2-2 3-3 4l-4 6c0 1 0 1-1 1l-1 1h-1-1c3-6 7-10 10-16z" class="C"></path><path d="M277 124l2-1-2 4c-1 3 0 6-1 9 0 1 0 0 1 1l1-2 1-1 1 1 2-2c0-1 1-2 2-3 1 0 1 1 1 1v2c-2 1-2 1-2 2v1-1h0c2-1 3-2 4-3v1c-1 1-3 3-4 5h2c-2 2-5 3-8 5-5 3-10 6-15 8-1 0-2 0-3-1l-1 1c-1-1-1-2 0-2v-1-2l1-3c0-3 1-5 0-8h0c2 0 3-1 4-2s2-1 3-2l2-1 1-1h1 1v1h1l1-1c1-3 2-4 4-5z" class="H"></path><path d="M272 130l1-1c1-3 2-4 4-5 0 1 0 1-1 2v1s-1 2-2 2c0 1-1 1-2 1z" class="B"></path><path d="M282 133c-1 1-2 2-2 4-1 1-1 2-3 2v1c-1 0-1 0-2 1l-2 2 2-5c0-1 0-2 1-2 0 1 0 0 1 1l1-2 1-1 1 1 2-2z" class="U"></path><path d="M271 139v-1c0-1 0-3 1-4h0c0-1 1-2 2-2 0-1 0-1 1-1v1c-1 1-1 2-1 3 0 2-1 3-2 4-1 3 0 5-3 7-3 1-6 3-9 4h-1l-1 1c-1-1-1-2 0-2v-1s1 0 1-1h1c1 0 1-1 2-1h0c2 0 3-1 3-2h1l1-1c1-2 2-3 4-4z" class="G"></path><path d="M267 143c1-2 2-3 4-4l-2 2c-1 1-1 1-1 2-1 1-1 1-2 1l1-1z" class="B"></path><path d="M269 129l1 1c0 3-1 7-3 10v3l-1 1h-1c0 1-1 2-3 2h0c-1 0-1 1-2 1h-1c0 1-1 1-1 1v-2l1-3c0-3 1-5 0-8h0c2 0 3-1 4-2s2-1 3-2l2-1 1-1z" class="E"></path><path d="M259 143l1-3h1v1h0v3c2 0 3 1 4 0 0 1-1 2-3 2h0c-1 0-1 1-2 1h-1c0 1-1 1-1 1v-2l1-3z" class="H"></path><path d="M269 129l1 1c0 3-1 7-3 10l-2 2h-1c0-1 0-2-1-3v-6c1-1 2-1 3-2l2-1 1-1z" class="R"></path><path d="M268 130c1 0 1 0 1 1s-1 4-2 4c0 1-1 1-2 1v-2-1c1 0 1-1 1-2l2-1z" class="K"></path><defs><linearGradient id="AI" x1="157.661" y1="477.073" x2="223.042" y2="480.346" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#AI)" d="M162 455c6-3 14-4 20-2 2 0 3 1 5 2 1 0 2 0 3 1l1 2v-1c1 0 2 1 3 2 1 3 2 4 4 6 1 1 2 3 2 4s1 1 2 2v1h-1v2h1v1l1 1 1-2c1 0 1 0 2 1 1-2 0-2 0-3l1-1c2 4 5 9 6 13l1 1 14 26-1 1-6-4c-2 0-4-1-5-2h-1l-2-2c-1 1-2 1-3 2-1 2-3 5-4 7l-1 2h-3 0c-2 0-3-1-5-2 0-1 0-1-1-1h-1v-1c-2-1-3-2-5-3-2 0-4-2-6-3-3-2-6-3-8-6l-1-1c-1 0 0 1-1 2-1-1-1-1-2-1-2 0-4-2-6-2l-8-1v-1-3l1-1h-1c-1-2-1-2-1-4v2h-1l-1-2-3-6-3-5c0-2-1-3-1-5 1 1 1 2 2 3 1-3 5-7 7-10l1-1h0c0-1 0-2-1-2l1-1-1-2c1 0 1-1 2-1s2-1 3-2z"></path><path d="M174 466c0-2 1-2 2-3h0v5c-1 0-1 0-1 1h-1v-3z" class="G"></path><path d="M197 466h0l2 2-2 2-1 3c-1-2-1-4 0-7h1z" class="P"></path><path d="M212 493l1 1v2c0 1 0 2-1 3h-1l-2-2v-1h1v-2h2v-1z" class="Z"></path><path d="M182 456h3v6c-1-1-1-1-1-2s-1-1-1-2c-1 1-1 2-1 3-1 2 0 4-1 5h0-1c1-1 1-2 1-3l-1-1 1-1v-3l1-2z" class="C"></path><path d="M189 464h0c1 0 1 1 1 1-1 1-1 2-2 3 0 1-1 2-2 2 0 1-1 1-2 1h0v-1h-1c3-2 4-3 6-6z" class="G"></path><path d="M175 469c0-1 0-1 1-1 2 2 3 2 5 2h2 1v1h0v1c-1 0-3 1-4 1s-1-1-2-1l-3-3z" class="V"></path><path d="M187 455c1 0 2 0 3 1l1 2h0c0 2 0 5-1 7 0 0 0-1-1-1h0v-1c0-3 0-5-2-8z" class="E"></path><path d="M168 476l6 4c1 0 3 1 3 2v1h1v1h-1 0 0c-1-1-2-1-4-2 0 0-1-1-2-1-1-2-2-3-3-4v-1z" class="B"></path><path d="M201 496c5 2 10 5 15 8h0l-1 1v1l-2-2h-2s-1 0-1-1c-1-1-3-2-4-2l-1-1c-1-1-3-2-4-3h-1l1-1z" class="L"></path><path d="M158 460c3 4 6 10 8 15l2 4-1 1v-1l-2-3s-1-1-1-2v-2l-1-1c0-1-1-2-2-3l-3-5c0-1 0-2-1-2l1-1z" class="M"></path><path d="M201 474h1v1l1 1h0c-1 1-1 1-1 2-1 0-1 1-1 2v1l1 1c0-1 1-1 1-2 0 1 1 1 1 2s0 1 1 2c1 2 0 2 0 4h1 1v1h-1c-1 1 0 1 0 2v2c0-2-2-4-2-6h0c-1-1-1-2-2-3l-1-1h-1v-1-6l1-2z" class="J"></path><path d="M201 474h1v1l1 1h0c-1 1-1 1-1 2-1 0-1 1-1 2v1l1 1h-1c-1-1 0-4-1-6l1-2z" class="Z"></path><path d="M200 483l-1 2h0v-3c-1-2 0-4-1-6v-2c0-2 0-4 2-5 0 1 1 1 2 2v1h-1v2l-1 2v6 1z" class="P"></path><path d="M174 466v3h1l3 3c1 0 1 1 2 1s3-1 4-1v-1c1 0 2 0 2-1 1 0 2-1 2-2 1 1 1 2 2 2-3 3-5 4-8 5h0c-2 1-4 0-5-1s-1-1-2-1v-1c-2-2-2-4-1-6z" class="c"></path><path d="M166 475c1 0 2 0 2 1v1c1 1 2 2 3 4 1 0 2 1 2 1 2 2 2 3 2 6-1 1-1 2-1 3l-1 1-1 1v-1l-4-13-2-4z" class="K"></path><path d="M171 481c1 0 2 1 2 1 2 2 2 3 2 6-1 1-1 2-1 3l-1 1-1 1v-1h1v-1c-2-3 0-4 0-7 0-1-1-2-2-3z" class="J"></path><path d="M212 486l2-1 14 26-1 1-6-4c-2 0-4-1-5-2h-1v-1l1-1 9 5-13-23z" class="i"></path><path d="M204 474c1 0 1 0 2 1 1-2 0-2 0-3l1-1c2 4 5 9 6 13l1 1-2 1c0-1-1-3-1-4h-1c0-1 0-2-1-3 0-1-1-1-1-1v-1h-1v-1c0 1-1 3-1 4v2l-1 2c-1-1-1-1-1-2s-1-1-1-2c0 1-1 1-1 2l-1-1v-1c0-1 0-2 1-2 0-1 0-1 1-2h0l1-2z" class="T"></path><path d="M191 457c1 0 2 1 3 2 1 3 2 4 4 6l-1 1h0l-1-1h-1c-1 2-1 3-2 5l-3 2c-1 2-4 4-6 5-2 0-8-1-10-4v-1h0l1 1c1 0 1 0 2 1s3 2 5 1h0c3-1 5-2 8-5-1 0-1-1-2-2 1-1 1-2 2-3 1-2 1-5 1-7h0v-1z" class="d"></path><path d="M191 458c1 3 1 7 0 10l-1 2c-1 0-1-1-2-2 1-1 1-2 2-3 1-2 1-5 1-7z" class="U"></path><path d="M167 480l1-1 4 13v1h0l1 5c-2-1-4-1-7-1l-8-1v-1-3l1-1 2-3 3-4 3-4z" class="a"></path><path d="M165 489h1c0 1 1 1 2 1 0 0 0 1 1 1 0 1 0 1-1 3h-2c-1-1-2-1-2-2s0-2 1-3z" class="Q"></path><path d="M161 488c2-1 3-3 5-3 1-1 1-1 2 0h-2c0 1-1 1-1 2-2 0-2 1-3 3v2c-1 1-2 0-4 0l1-1 2-3z" class="Y"></path><path d="M167 480l1-1 4 13v1h0-2c0-2-1-3-2-5v-3h0c-1-1-1-1-2 0-2 0-3 2-5 3l3-4 3-4z" class="O"></path><path d="M148 471c1 1 1 2 2 3 1-3 5-7 7-10l1-1h0l3 5c1 1 2 2 2 3l1 1v2c0 1 1 2 1 2l2 3v1l-3 4-3 4-2 3h-1c-1-2-1-2-1-4v2h-1l-1-2-3-6-3-5c0-2-1-3-1-5z" class="F"></path><path d="M162 481v-1l2-2 3 1v1l-3 4v-3h-2z" class="c"></path><path d="M156 474h3 0c-2 2-2 4-3 5l-1-1h-1s-1 0-1 1v-2c1 0 2-2 3-3z" class="X"></path><path d="M162 481h2v3l-3 4-2 3h-1c-1-2-1-2-1-4l5-6z" class="R"></path><path d="M148 471c1 1 1 2 2 3 1-3 5-7 7-10l1-1h0l3 5c1 1 2 2 2 3l1 1c-1 1-1 2-2 2h-1-1-1-3c-1 1-2 3-3 3v2c1 3 2 5 2 8l-3-6-3-5c0-2-1-3-1-5z" class="W"></path><path d="M161 468c1 1 2 2 2 3l1 1c-1 1-1 2-2 2h-1-1-1-3c-1 1-2 3-3 3h0c2-3 4-6 7-8 1 0 1-1 1-1z" class="e"></path><path d="M163 471l1 1c-1 1-1 2-2 2h-1-1-1-3c1-1 2-2 4-2h2l1-1z" class="b"></path><path d="M177 482l6 3 5 3c1 1 12 8 13 8l-1 1h1c1 1 3 2 4 3l1 1c1 0 3 1 4 2 0 1 1 1 1 1h2c-1 1-2 1-3 2-1 2-3 5-4 7l-1 2h-3 0c-2 0-3-1-5-2 0-1 0-1-1-1h-1v-1c-2-1-3-2-5-3-2 0-4-2-6-3-3-2-6-3-8-6l-1-1c-1 0 0 1-1 2-1-1-1-1-2-1-2 0-4-2-6-2 3 0 5 0 7 1l-1-5h0l1-1 1-1c0-1 0-2 1-3 0-3 0-4-2-6 2 1 3 1 4 2h0 0 1v-1h-1v-1z" class="X"></path><path d="M192 502s1 1 1 2c0 0 0 1 1 0 1 0 1 0 1-1 0 1 0 2-1 3v1h-1c-1 0-2 0-2-1-1 0-1 0-1-1h0l2-3z" class="J"></path><path d="M197 495h0l1 3c0 1 0 1-1 2 0 1-1 2-2 3 0 1 0 1-1 1-1 1-1 0-1 0 0-1-1-2-1-2l2-4c1-1 1-3 3-3z" class="K"></path><defs><linearGradient id="AJ" x1="197.181" y1="502.302" x2="200.789" y2="511.369" xlink:href="#B"><stop offset="0" stop-color="#a5a3a4"></stop><stop offset="1" stop-color="#c2c1c2"></stop></linearGradient></defs><path fill="url(#AJ)" d="M201 497c1 1 3 2 4 3-1 1-1 3-2 5l-2 8h-1l-6-4c3-2 6-7 7-10v-1-1z"></path><path d="M201 497c1 1 3 2 4 3-1 1-1 3-2 5 0-1 0-1-1-1h0-1c1-2 1-4 0-6v-1z" class="H"></path><path d="M205 500l1 1c1 0 3 1 4 2 0 1 1 1 1 1h2c-1 1-2 1-3 2-1 2-3 5-4 7l-1 2h-3c0-1-1-1-2-2h1l2-8c1-2 1-4 2-5z" class="M"></path><path d="M205 500l1 1c0 3-1 4-2 6 0 2-1 4-1 5l-1 1h-1l2-8c1-2 1-4 2-5z" class="N"></path><path d="M177 482l6 3 5 3c-1 0-1 0-2 1h0l-1 1c-2 0-2 2-4 3l-1-1c0 1-1 2-1 3-1 1-1 2-2 2-1 1-1 1-2 1l-1-1v-1-4c2-2 2-4 3-6 0 0 1 0 1-1l-1-1h0 1v-1h-1v-1z" class="O"></path><path d="M183 485l5 3c-1 0-1 0-2 1h0l-1 1c-2 0-2 2-4 3l-1-1c0 1-1 2-1 3-1 1-1 2-2 2 0-2 1-4 2-6s1-3 2-5l2-1z" class="H"></path><path d="M180 492c1-2 2-4 3-5 1 0 2 1 3 2l-1 1c-2 0-2 2-4 3l-1-1z" class="R"></path><defs><linearGradient id="AK" x1="182.539" y1="490.024" x2="187.953" y2="500.651" xlink:href="#B"><stop offset="0" stop-color="#9f9d9e"></stop><stop offset="1" stop-color="#bcbbbb"></stop></linearGradient></defs><path fill="url(#AK)" d="M186 489h0c1-1 1-1 2-1 1 1 12 8 13 8l-1 1h-1c0-1-1-1-2-2h0c-2 0-2 2-3 3l-2 4-2 3h0v2c-2-1-4-3-5-4l-4-2-3-2c0-1 1-2 2-2s1-1 2-1v-1l1-1c1-1 2-3 2-4l1-1z"></path><path d="M188 495c0-1 0-4 1-5l1 2v2l-1 2-1-1h0z" class="N"></path><path d="M181 501l-3-2c0-1 1-2 2-2s1-1 2-1v-1 3c1-1 1-2 1-2h1c-1 2-2 3-3 5z" class="B"></path><defs><linearGradient id="AL" x1="194.796" y1="495.378" x2="188.203" y2="498.46" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#959394"></stop></linearGradient></defs><path fill="url(#AL)" d="M190 492c2 0 2 1 3 1 1 1 3 1 4 2-2 0-2 2-3 3l-2 4-2 3h0v2c-2-1-4-3-5-4 0 0 0-1 1-1 0-1 0-2 1-3l1-4h0l1 1 1-2v-2z"></path><path d="M190 494v2c-1 1-1 3-1 4-1 0-2 0-2-1l1-4h0l1 1 1-2z" class="U"></path><path d="M186 502v1h1c1-1 1-1 1-2h1c0 1 0 1 1 2h0v2h0v2c-2-1-4-3-5-4 0 0 0-1 1-1z" class="B"></path><path d="M400 56c1 2-2 5-3 7-1 4-1 8-1 12v5c1 5 3 9 5 13 1 3 3 6 4 8v-1-1c0-1 0-2-1-3h1l1 4h1c1 2 3 4 4 5l4 4 5 4c1 2 5 6 7 6l10 8v1l9 6v-1-1-1c1 2 4 3 5 3l20 9 6 3 6 4 2 2c1 0 2 1 3 2l1 2c0 2 1 6 2 7l-1 2v1h0c0-1 0-2-1-2-1-1 0-1-1 0v1c-1 3-1 6-1 9v7 2l-2 1h0v5c0 1 0 1-1 1h0c-1-2-3-5-5-7v-1-1c0-3-2-4-1-7v-1c-1-3-6-7-9-8-2 0-5-1-7-2l-6-3c-2-1-3-1-4-2l-1-1c-1-2-4-3-6-4-1 0-1 0-2-1l1-1c-3-2-7-3-11-5-9-4-18-9-26-16-2-2-4-4-5-6-2-2-5-3-6-6h-1c-5-6-10-12-12-20-3-8-4-18-1-26l1-1 1-1c-1-1 0 0-2-1v-1c2-3 5-6 9-7v-1h1c3-1 6-3 8-4z" class="a"></path><path d="M399 105l1-1c1 0 2 1 3 2l-1 1h-1c-1-1-1-1-2-1v-1z" class="j"></path><path d="M397 110c-1-1-1-2 0-3 0 0 1 0 1 1h2c2 0 2 0 2-1l1 1v2c-1 1-2 2-3 2s-2-1-3-2z" class="C"></path><path d="M479 159l1-2v3c-1 1-1 2 0 3h0v1c0 1 0 4-1 5l-1 1c-4-5-8-7-13-10 3 0 4 2 6 3 1 0 2 1 3 1h0 1v1h0 1c0-2 0-2 1-3s1-2 2-3z" class="H"></path><path d="M446 134v-1-1-1c1 2 4 3 5 3l20 9 6 3-1 1s-1-1-2-1l-6-3c-7-3-15-5-22-9z" class="b"></path><path d="M477 148c3 1 8 5 10 7l-1 1-2 1c0 1-1 1-1 2h0v1c1 1 1 2 2 2h0v6h-1v-1-3h-1-2c0-1-1-1-1-1-1-1-1-2 0-3v-3l-1 2v-1-3-1s0-1 1-1v-2c-1 0-1-1-2-1 0 1-1 2 0 3v1h-3-1l-1-1h0 1 2c1-1 1-3 2-4l-1-1z" class="G"></path><path d="M480 157c0-1 0-3 1-5l1 1v3l1-1h1c-1 0-1 1-1 1-1 2-2 3-3 4v-3z" class="E"></path><path d="M484 155l1 1h1l-2 1c0 1-1 1-1 2h0v1c1 1 1 2 2 2h0v6h-1v-1-3h-1-2c0-1-1-1-1-1-1-1-1-2 0-3s2-2 3-4c0 0 0-1 1-1z" class="c"></path><path d="M480 163s1 0 1 1h2 1v3 1h1v7h-1v7l-1 1-3-7v-1h0c0-2-1-4-2-5l1-1c1-1 1-4 1-5v-1h0z" class="U"></path><path d="M483 174c-1-1-2-1-2-2v-2l2 1 1 2-1 1z" class="G"></path><path d="M480 167c1-1 2-1 3-1 0 0 1 0 1 1v1 2c-2-1-3-1-4-3z" class="f"></path><path d="M480 163s1 0 1 1h2 1v3c0-1-1-1-1-1-1 0-2 0-3 1h0v-4h0z" class="F"></path><path d="M483 171l1 1v3 7l-1 1-3-7 2-2h1 0l1-1-1-2zm-5-17v4 1s0 1-1 1v1c-1 0-1 1-1 1-1 1-2 0-3 0h-1c-2-1-4-2-5-3v-1h1l-1-1v-1-1c0-1 0-1 1-2h1 4 0l1 1h1 3z" class="f"></path><path d="M473 153l1 1v3c-1 1-2 1-3 1s-2 0-2-1c-1-1-1-2-1-2 1-1 3-1 4-1l1-1z" class="S"></path><path d="M405 101v-1-1c0-1 0-2-1-3h1l1 4h1c1 2 3 4 4 5l4 4 5 4c1 2 5 6 7 6l10 8v1c-3-1-6-3-8-5-9-6-18-13-24-22z" class="C"></path><path d="M477 146l6 4 2 2c1 0 2 1 3 2l1 2c0 2 1 6 2 7l-1 2v1h0c0-1 0-2-1-2-1-1 0-1-1 0v1c-1 3-1 6-1 9v7 2l-2 1h0c-1 0-1-1-2-1l1-1v-7h1v-7-6h0c-1 0-1-1-2-2v-1h0c0-1 1-1 1-2l2-1 1-1c-2-2-7-6-10-7l-1-1 1-1z" class="S"></path><path d="M487 155v1c-1 2-2 4-4 4v-1h0c0-1 1-1 1-2l2-1 1-1z" class="X"></path><path d="M486 168c1-2 0-5 1-7v-1c1 1 1 2 1 4v1c-1 3-1 6-1 9v7 2l-2 1h0c-1 0-1-1-2-1l1-1v-7h1v-7-6c1 1 0 7 1 7v-1z" class="T"></path><path d="M486 175l-1 9h0c-1 0-1-1-2-1l1-1v-7h1v1l1-1z" class="I"></path><path d="M485 162c1 1 0 7 1 7v-1 7l-1 1v-1-7-6z" class="C"></path><path d="M444 151l15 7c2 0 4 2 6 2 5 3 9 5 13 10 1 1 2 3 2 5h0v1l3 7c1 0 1 1 2 1v5c0 1 0 1-1 1h0c-1-2-3-5-5-7v-1-1c0-3-2-4-1-7v-1c-1-3-6-7-9-8-2 0-5-1-7-2l-6-3c-2-1-3-1-4-2l-1-1c-1-2-4-3-6-4-1 0-1 0-2-1l1-1z" class="d"></path><defs><linearGradient id="AM" x1="385.871" y1="89.496" x2="397.833" y2="95.173" xlink:href="#B"><stop offset="0" stop-color="#a4a3a3"></stop><stop offset="1" stop-color="#c2c0c1"></stop></linearGradient></defs><path fill="url(#AM)" d="M394 78v-5c0-1 0-2 1-3 0 2 0 3 1 5v5c1 5 3 9 5 13l-1-1c-1-1-1-1-1-2v2c0 1 1 2 1 3-1 0-1 0-2 1-2 0-4 3-6 5s-3 3-5 4c1 3 3 6 5 8h0 1l3 5h0-1c-5-6-10-12-12-20 0-2 1-5 2-7s1-4 2-6c2-3 5-5 6-8l1 1z"></path><path d="M393 86v1 1c-1 0-1 1-2 2s-1 2-1 4l-2 2c-1 0-1 0-1-1 2-1 1-2 3-4l-1-1 1-1c1 0 1-1 2-2l1-1z" class="B"></path><path d="M394 78v-5c0-1 0-2 1-3 0 2 0 3 1 5v5c1 5 3 9 5 13l-1-1c-1-1-1-1-1-2v2l-1-1c-1-1-1-1-1-2s0-1-1-1v-4c-1-2-1-4-2-6z" class="D"></path><path d="M400 56c1 2-2 5-3 7-1 4-1 8-1 12-1-2-1-3-1-5-1 1-1 2-1 3v5l-1-1c-1 3-4 5-6 8-1 2-1 4-2 6s-2 5-2 7c-3-8-4-18-1-26l1-1 1-1c-1-1 0 0-2-1v-1c2-3 5-6 9-7v-1h1c3-1 6-3 8-4z" class="Z"></path><path d="M389 69c1 0 2 1 2 1v2h0-1c-2-1-1 0-3 1v-1c0-2 1-2 2-3z" class="i"></path><path d="M391 72l1 1c0 1-1 2-2 3s-1 0-3 0l-1-1v-1c2-1 2 0 3 0l2-2z" class="S"></path><path d="M389 69l3-3 3-3h1 1c-1 4-1 8-1 12-1-2-1-3-1-5-1 1-1 2-1 3v5l-1-1h0v-3l-1-1v-1h-1v-2s-1-1-2-1z" class="F"></path><path d="M391 70c1-1 3-2 4-3v1 2c-1 1-1 2-1 3v5l-1-1h0v-3l-1-1v-1h-1v-2z" class="f"></path><path d="M391 72h1v1l1 1v3h0c-1 3-4 5-6 8-1 2-1 4-2 6s-2 5-2 7c-3-8-4-18-1-26 1 1 2 2 2 3 0 2-1 3-1 4h1c0-1 0-2 1-3v1 3-2s0-1 1-2h1c2 0 2 1 3 0s2-2 2-3l-1-1h0z" class="N"></path><path d="M387 79h0 2c0 1-1 2-2 3l-1 1-1-1c0-1 1-2 2-3z" class="E"></path><defs><linearGradient id="AN" x1="347.515" y1="319.436" x2="428.194" y2="364.434" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#AN)" d="M435 244l1 1-1 2 1 1-1 3-2 1v1c1 0 2 1 2 2v2c1 0 1 2 2 2 2 1 3 1 3 3 2 1 3 2 4 2h-2c0 1 1 2 2 2h-1-1-1 0l-1 1c-1-1-3-1-4-1 3 2 6 5 9 9l-3 1-4 4c-1-3-3-5-5-7-3-4-9-7-14-8-4-1-9-1-14-1h-18v219h0 0l-1-1c-2 2-3 3-5 4-1 0-1-1-1-2l1-29v-23l-1-21h0l-1-1v-1l-2-2v-3c-1-2 0-5 0-8v-79l-7-3v1l-2 2c-1-1-2-2-2-4l-1-1c-1-1-2-1-4-1h-1v-1l1-1h1c1-2 3-3 4-4 1 0 1 0 2-1s3-2 3-4l2-3v-2c0-1-3-2-4-3 0-1-1-2-1-4l-1-1-1-1h0v-1h2c1 0 1 0 2 1h0c2-1 5 2 7 3v-30-6h10c-2 0-5 0-6-1 2-1 7 0 10 0h6 6c2-2 4-3 5-5l1-1v1l-1 1c0 1 0 0 1 1l-1 1v1h4c2 0 4 0 6 1h1c-1 1-2 1-4 1h0 5c2 0 5 0 7-1h2c2-1 5-6 6-8z"></path><defs><linearGradient id="AO" x1="385.101" y1="441.885" x2="381.221" y2="456.804" xlink:href="#B"><stop offset="0" stop-color="#858485"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#AO)" d="M381 432v6c1 1 1 2 1 3h2 1l1-1v-2l1 16c-3 1-4 3-6 4v-3-23z"></path><defs><linearGradient id="AP" x1="386.493" y1="455.065" x2="380.314" y2="485.33" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#AP)" d="M381 455v3c2-1 3-3 6-4v29l-1-1c-2 2-3 3-5 4-1 0-1-1-1-2l1-29z"></path><path d="M435 244l1 1-1 2 1 1-1 3-2 1v1c1 0 2 1 2 2v2c1 0 1 2 2 2 2 1 3 1 3 3 2 1 3 2 4 2h-2c0 1 1 2 2 2h-1-1-1 0l-1 1c-1-1-3-1-4-1-5-3-9-5-15-7-6-1-13-1-19-1l-21-1-1 154h0l-1-1v-1l-2-2v-3c-1-2 0-5 0-8v-79l-7-3v1l-2 2c-1-1-2-2-2-4l-1-1c-1-1-2-1-4-1h-1v-1l1-1h1c1-2 3-3 4-4 1 0 1 0 2-1s3-2 3-4l2-3v-2c0-1-3-2-4-3 0-1-1-2-1-4l-1-1-1-1h0v-1h2c1 0 1 0 2 1h0c2-1 5 2 7 3v-30-6h10c-2 0-5 0-6-1 2-1 7 0 10 0h6 6c2-2 4-3 5-5l1-1v1l-1 1c0 1 0 0 1 1l-1 1v1h4c2 0 4 0 6 1h1c-1 1-2 1-4 1h0 5c2 0 5 0 7-1h2c2-1 5-6 6-8z" class="Y"></path><path d="M415 253h5c2 0 5 0 7-1h2l-5 4c-2-1-5-1-8-2l-3-1h2z" class="I"></path><path d="M428 255h1 3 1v3c1 1 3 2 4 3 2 1 3 2 5 3 0 1 1 2 2 2h-1-1-1 0c-4-4-10-7-15-9l2-2z" class="C"></path><path d="M435 247l1 1-1 3-2 1v1c1 0 2 1 2 2v2c1 0 1 2 2 2 2 1 3 1 3 3 2 1 3 2 4 2h-2c-2-1-3-2-5-3-1-1-3-2-4-3v-3h-1-3-1c3-3 5-5 7-8z" class="e"></path><defs><linearGradient id="AQ" x1="401.222" y1="254.951" x2="402.167" y2="250.666" xlink:href="#B"><stop offset="0" stop-color="#3e393e"></stop><stop offset="1" stop-color="#555855"></stop></linearGradient></defs><path fill="url(#AQ)" d="M408 247l1-1v1l-1 1c0 1 0 0 1 1l-1 1v1h4c2 0 4 0 6 1h1c-1 1-2 1-4 1h0-2l3 1-29-1c-2 0-5 0-6-1 2-1 7 0 10 0h6 6c2-2 4-3 5-5z"></path><path d="M408 247l1-1v1l-1 1c0 1 0 0 1 1l-1 1v1h4c2 0 4 0 6 1h1c-1 1-2 1-4 1-4-1-8-1-12-1 2-2 4-3 5-5z" class="E"></path><path d="M377 396c1-8-1-18 0-26 1-1 1-1 2-1l1 1v15 6l-2 1h0c1 1 2 0 2 2v10 4l-1 1h0l-2-2v-3c-1-2 0-5 0-8z" class="j"></path><defs><linearGradient id="AR" x1="376.862" y1="299.566" x2="364.523" y2="298.416" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#AR)" d="M368 288l-1-1-1-1h0v-1h2c1 0 1 0 2 1h0c2 1 5 3 7 5v23l-1 1-1-1c-2 0-4-1-6-2l-1 1c1 1 2 1 2 1v1l-2 2c-1-1-2-2-2-4l-1-1c-1-1-2-1-4-1h-1v-1l1-1h1c1-2 3-3 4-4 1 0 1 0 2-1s3-2 3-4l2-3v-2c0-1-3-2-4-3 0-1-1-2-1-4z"></path><path d="M374 303c1 3 0 8 1 11-2 0-4-1-6-2h-1v-1c2 1 2 1 4 0h0c2-1 2-6 2-8z" class="I"></path><defs><linearGradient id="AS" x1="374.041" y1="301.424" x2="364.586" y2="306.928" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#6f6e6d"></stop></linearGradient></defs><path fill="url(#AS)" d="M373 297l2 1c0 2-1 3-1 5s0 7-2 8h0c-2 1-2 1-4 0v1h1l-1 1c1 1 2 1 2 1v1l-2 2c-1-1-2-2-2-4l-1-1c-1-1-2-1-4-1h-1v-1l1-1h1c1-2 3-3 4-4 1 0 1 0 2-1s3-2 3-4l2-3z"></path><path d="M364 309c1-1 3-3 6-3 1 0 1 1 1 1 0 1 0 1-1 2s-3 1-5 1l-1-1z" class="c"></path><path d="M362 309h2l1 1c2 0 4 1 6 1h1c-2 1-2 1-4 0v1h1l-1 1c1 1 2 1 2 1v1l-2 2c-1-1-2-2-2-4l-1-1c-1-1-2-1-4-1h-1v-1l1-1h1z" class="J"></path><defs><linearGradient id="AT" x1="526.793" y1="246.44" x2="540.864" y2="207.771" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#4d4b4c"></stop></linearGradient></defs><path fill="url(#AT)" d="M488 164c1-1 0-1 1 0 1 0 1 1 1 2h0v-1l1-2c1 1 2 2 3 2l3 1c1 1 1 1 2 1 1 1 3 2 5 3 0 0 1 1 2 1l1-1h0v2s1 1 2 1c0 1 0 1 1 1h-3l2 1 1 1c1 0 1 1 2 1v1h-3-1l1 1c0 1 0 2 1 3h0 0 0l1-1c0-1 2-1 2-1 2 1 3 2 4 3h0c2 1 3 2 4 3 2 1 3 2 4 3 2 1 5 3 7 5 0 0 2 1 2 2 1 1 3 3 4 5v1c0 1-1 1-1 2v1c1 0 2 0 4-1 1 0 3 0 5 1 1 0 1 0 2 2v1h0c0 1 0 1-1 2-1 0-2 1-3 1-2 1-4 3-6 5-4 6 0 12-2 18h0c2 3 4 4 7 6 1 1 2 1 3 2 3 0 5-1 8-2h2l-3 3v1 1c2 0 6-2 7-2v1 5c0 2 0 4 1 6v3l1 2c0 2 1 4 1 5l4 2h-3-1c-1 1-2 1-3 2h-1l-1 1 1 1v1h-1c-2 0-4-1-5-2l-2-1-3-1c-4-1-8-3-12-4l1-1c0-1 0 0-1-1s-1-2-1-3c-1-2-4-4-6-5h0c1-1 1-1 1-2l-6-3c-2-1-3-1-4-2h-2l-1-3h0 2l-1-1c-1 0-2 0-3-1-2-1-4-4-6-5h1c1 0 2-1 3-2 0-1 2 0 3-1 1 0 2-1 3-1h2v-1l-4-6c0-1 1-1 1-2 0 0 1-1 1-2l-3-3-2-2h-1l-3-3s-1-1-2-1h-2v1 1h0l-3-2h-2l2 3-4-2-7-3c-1-1-1-1-1-2h-1c1-1 1-2 0-3h0v-2h1c1 0 1-1 2-2l-2-2-4-5-1-1c-1-1-1-2-2-4 1 0 1 0 1-1v-5h0l2-1v-2-7c0-3 0-6 1-9v-1z"></path><path d="M518 197c2-1 3-1 4-1 1 1 2 1 3 2l1 1c0 1-1 2-1 3h0v1h-1l-1-1c-1 0-2-1-2-2-1 0-1 0-2-1-2 0-4 0-6 2h0-1c1 0 1 0 2 1v1l-1 1c-1-1-2-1-3-2l2-3c1 0 1-1 2-1 2-1 3-1 4-1h0z" class="C"></path><path d="M518 197c2-1 3-1 4-1 1 1 2 1 3 2l1 1c0 1-1 2-1 3l-1-2c-1-2-3-3-6-3h0z" class="G"></path><path d="M524 223l-4-3c-1-2-2-3-3-5s-3-3-3-5 1-3 1-5v-2c2-2 3-1 4-1l1-1v1 1c-1 1-1 2-2 3l1 1v1c-1 1-2 1-2 2v1h-1c1 1 3 2 4 3l7-1h-1c-2 1-3 2-5 2-1 1 0 1 0 2l3 6z" class="Q"></path><path d="M520 202v1c-1 1-1 2-2 3l1 1v1c-1 1-2 1-2 2v1c-2-3-1-5-1-8h0v-1h4z" class="X"></path><path d="M506 201h1v-2h0l2-2v1h1c0 1 1 1 2 1l-2 3-1 4c0 2 0 4 1 5 1 2 3 4 4 6h-1l-3-3s-1-1-2-1h-2v1 1h0l-3-2 1-1h1 1v-1-1l2 1c1-3-2-3-3-4 0-1-1-1-1-2 0-2 0-4 1-6l1 2z" class="B"></path><path d="M506 211v-1l2 1h0c1 1 1 1 2 1l-1 1h-1 0-1c-1-1-1-1-1-2z" class="Y"></path><path d="M506 212v-1c0 1 0 1 1 2h1 0-2v1 1h0l-3-2 1-1h1 1z" class="O"></path><path d="M506 201h1v-2h0l2-2v1s0 1 1 2h-2c0 1 0 2-1 2l2 2h0c-1 1-2 1-2 2-1 0 0 0 0 1l-1-1c-1-1-1-2-1-4l1-1z" class="L"></path><path d="M515 191c3 0 4 1 6 1l2 2h0 0l1 1h0c1 1 1 2 2 2l-1 1c-1-1-2-1-3-2-1 0-2 0-4 1h0c-1 0-2 0-4 1-1 0-1 1-2 1s-2 0-2-1h-1v-1l-2 2h0v2h-1l-1-2c1-1 1-2 2-3 1 0 3-2 3-2 1-1 2-1 3-1l2-2z" class="V"></path><path d="M510 198v-1h4 1l1-1 2 1h0c-1 0-2 0-4 1-1 0-1 1-2 1s-2 0-2-1z" class="N"></path><path d="M513 194h1 0c2-1 3-1 5 0v1c-1 1-2 1-3 1 0 0 0-1-1-1l-1 1c-1 0-2 0-2 1l-1-1 2-2z" class="L"></path><path d="M515 191c3 0 4 1 6 1l2 2h0 0-4c-2-1-3-1-5 0h0-1-3c1-1 2-1 3-1l2-2z" class="B"></path><path d="M519 222c7 8 12 18 17 26-3 0-5-2-7-4-3-4-4-8-7-11l-1-1-4-6c0-1 1-1 1-2 0 0 1-1 1-2z" class="c"></path><defs><linearGradient id="AU" x1="543.62" y1="261.168" x2="555.525" y2="255.779" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#5b5a5a"></stop></linearGradient></defs><path fill="url(#AU)" d="M537 249c1 1 2 1 3 2h1-1v-2-1h1l-1 1 2 2c0-1 1-1 1-1h1c1-1 2-1 3-2v3h1v-1h1l1 3v1c-1 2 0 3-1 4l1 1h1c1 0 3 1 3 1l5 2v1c0 1 0 1 1 2h0c-2-1-2-1-4-1-5-2-10-6-14-9l-3-2c0-2 0-2-2-4z"></path><path d="M518 247l-1-3h0 2l9 3h1c-1-1-1-2-2-3h0 2c2 2 4 4 7 4l1 1c2 2 2 2 2 4l3 2-3 3-1-1h0c0 1-1 1-1 1l-1-1c-2-2-4-4-6-5l-6-3c-2-1-3-1-4-2h-2z" class="i"></path><path d="M527 244h2c2 2 4 4 7 4l1 1c2 2 2 2 2 4l-3-2c-3-1-5-2-8-4h1c-1-1-1-2-2-3h0z" class="K"></path><path d="M524 249h3c2 1 6 4 8 3 0 0 1 0 1-1l3 2 3 2-3 3-1-1h0c0 1-1 1-1 1l-1-1c-2-2-4-4-6-5l-6-3z" class="D"></path><defs><linearGradient id="AV" x1="532.649" y1="237.518" x2="552.18" y2="244.346" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#cbc9ca"></stop></linearGradient></defs><path fill="url(#AV)" d="M528 224h0v-2c1-1 2-1 3-1l1 1c0 4 1 9 4 12 2 3 4 4 7 6 1 1 2 1 3 2 3 0 5-1 8-2h2l-3 3v1 1c-2 0-4 0-6-1-2 0-5 0-7-1-6-3-9-7-11-13v-1c-1-2-1-3-1-5z"></path><path d="M528 224h0v-2c1-1 2-1 3-1l1 1c0 4 1 9 4 12 2 3 4 4 7 6l-1 1c-1 0-2 0-3-1h0c-3-2-5-4-7-6-1-2-2-3-3-5s-1-3-1-5z" class="L"></path><defs><linearGradient id="AW" x1="512.391" y1="234.651" x2="526.885" y2="247.997" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#AW)" d="M521 232l1 1c3 3 4 7 7 11h-2 0c1 1 1 2 2 3h-1l-9-3-1-1c-1 0-2 0-3-1-2-1-4-4-6-5h1c1 0 2-1 3-2 0-1 2 0 3-1 1 0 2-1 3-1h2v-1z"></path><path d="M520 238c1 0 3 1 3 2v3h-1c-1-1-1-1-2-1 0-1-1-2-2-2 1-1 1-2 2-2z" class="Z"></path><path d="M522 233c3 3 4 7 7 11h-2c-2-2-2-5-4-7-1 0-2 0-3 1-1 0-1 1-2 2l-2-1v-1-1-1c1-1 2-2 3-2h2s0-1 1-1z" class="W"></path><path d="M558 247c1-1 2-1 2-3v5c0 2 0 4 1 6v3l1 2c0 2 1 4 1 5l4 2h-3-1l-4-1-3-2c2 0 2 0 4 1h0c-1-1-1-1-1-2v-1l-5-2s-2-1-3-1h-1l-1-1c1-1 0-2 1-4v-1c0-1 1-2 1-2 2-3 4-3 6-4h1z" class="R"></path><path d="M558 247c1 1 0 2 0 4l-1-2c-1 0 0 0-1 1h0c-1 0-2 0-2 1h-2c-2 2 0 5-2 7 1 0 0 0 1 1h-1l-1-1c1-1 0-2 1-4v-1c0-1 1-2 1-2 2-3 4-3 6-4h1z" class="K"></path><path d="M554 260l1-2c-1-1-2-1-3-2 1-1 1-2 1-4 1 0 1 0 2-1h1c2 1 2 4 2 6v4h1v1h0l-5-2z" class="Z"></path><path d="M558 247c1-1 2-1 2-3v5c0 2 0 4 1 6v3l1 2c0 2 1 4 1 5h0c-2 0-3-2-4-3v-4c0-1-1-2 0-3 0-2 0-3-1-4 0-2 1-3 0-4z" class="I"></path><path d="M559 262c1-2 0-3 1-4h1l1 2c0 2 1 4 1 5h0c-2 0-3-2-4-3z" class="C"></path><path d="M530 252c2 1 4 3 6 5l1 1s1 0 1-1h0l1 1 3-3c4 3 9 7 14 9l3 2 4 1c-1 1-2 1-3 2h-1l-1 1 1 1v1h-1c-2 0-4-1-5-2l-2-1-3-1c-4-1-8-3-12-4l1-1c0-1 0 0-1-1s-1-2-1-3c-1-2-4-4-6-5h0c1-1 1-1 1-2z" class="B"></path><path d="M552 266v-2h2v2h-1-1z" class="L"></path><path d="M548 268v-2l1-1v1h2 1 1l-2 2v1h0l-3-1z" class="V"></path><path d="M543 262l1-1c-1 0-1 0-1-1l-1-1 1-1c1 1 1 0 1 1 1 1 1 1 2 1s1 1 1 1c0 1 0 1-1 2h-2v2h-1l-1-1 1-1v-1z" class="a"></path><path d="M553 270v-2-1c1 0 2 2 2 2h1c1-1 2-2 2-3h0 1l4 1c-1 1-2 1-3 2h-1l-1 1 1 1v1h-1c-2 0-4-1-5-2z" class="V"></path><path d="M546 263c0 1 0 1 1 2v1h0c1-1 1-2 1-3v-1h1c1 0 1 0 2 1v1c-1 0-2 1-2 2v-1l-1 1v2c-4-1-8-3-12-4l1-1h3 3l-1 1 1 1h1v-2h2z" class="G"></path><path d="M530 252c2 1 4 3 6 5l1 1c1 0 2 1 3 2h1v1h2v1 1h-3-3c0-1 0 0-1-1s-1-2-1-3c-1-2-4-4-6-5h0c1-1 1-1 1-2z" class="C"></path><path d="M535 259c2 1 3 1 5 4h0-3c0-1 0 0-1-1s-1-2-1-3z" class="R"></path><path d="M517 183c2 1 3 2 4 3 2 1 3 2 4 3 2 1 5 3 7 5 0 0 2 1 2 2 1 1 3 3 4 5v1c0 1-1 1-1 2v1l-3 3-2 2c-1 0-2 0-3 1l-2 2-7 1c-1-1-3-2-4-3h1v-1c0-1 1-1 2-2v-1l-1-1c1-1 1-2 2-3v-1h3l1 1h1v-1h0c0-1 1-2 1-3l-1-1 1-1c-1 0-1-1-2-2h0l-1-1h0 2s0-1-1-1c-1-1 0-1-1-2 0-1-1-2-2-2-1-1-1-2-1-3l-3-3z" class="S"></path><path d="M532 206l2 2-2 2c-1 0-2 0-3 1 1-2 2-3 3-5z" class="P"></path><path d="M526 199v1 2h1v1l1 1c1 0 1 0 1 1-1 3-3 4-5 5h-1c0-1 1-1 1-2l1-1h0c1-2 1-3 1-4l-1-1h0c0-1 1-2 1-3z" class="D"></path><path d="M519 207l1-1 5 1-1 1c0 1-1 1-1 2h1c-3 1-4 1-7 1v-1c0-1 1-1 2-2v-1z" class="M"></path><path d="M519 207l1-1 5 1-1 1-2 1c-1 0-1 1-1 1l-2-2v-1z" class="N"></path><path d="M525 202l1 1c0 1 0 2-1 4h0l-5-1-1 1-1-1c1-1 1-2 2-3v-1h3l1 1h1v-1z" class="R"></path><path d="M526 203c0 1 0 2-1 4h0l-5-1h1l1-1c1-1 2-1 3 0 1-1 1-1 1-2z" class="X"></path><path d="M523 194h2l1 1s0 1 1 1c1 2 3 4 3 6 0 1 0 2-1 3 0-1 0-1-1-1l-1-1v-1h-1v-2-1l-1-1 1-1c-1 0-1-1-2-2h0l-1-1h0z" class="O"></path><path d="M526 197c1 1 1 3 2 4v1h-1-1v-2-1l-1-1 1-1z" class="a"></path><path d="M532 206c1-1 1-3 1-4 1-2 0-4 0-6h1c1 1 3 3 4 5v1c0 1-1 1-1 2v1l-3 3-2-2z" class="K"></path><defs><linearGradient id="AX" x1="549.164" y1="217.771" x2="531.917" y2="220.517" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#AX)" d="M541 204c1 0 3 0 5 1 1 0 1 0 2 2v1h0c0 1 0 1-1 2-1 0-2 1-3 1-2 1-4 3-6 5-4 6 0 12-2 18h0c-3-3-4-8-4-12l-1-1c-1 0-2 0-3 1v2h0c0 2 0 3 1 5v1c-2-2-4-5-5-7l-3-6c0-1-1-1 0-2 2 0 3-1 5-2h1l2-2c1-1 2-1 3-1l2-2 3-3c1 0 2 0 4-1z"></path><path d="M541 204c1 0 3 0 5 1l-4 2c-3 3-8 7-10 11h0l-2-2-3 3v-1c0-2 2-4 3-5l2-3 2-2 3-3c1 0 2 0 4-1z" class="Y"></path><path d="M541 204c1 0 3 0 5 1l-4 2-1-1h-3l-8 10h0l-3 3v-1c0-2 2-4 3-5l2-3 2-2 3-3c1 0 2 0 4-1z" class="M"></path><path d="M529 211c1-1 2-1 3-1l-2 3c-1 1-3 3-3 5v1l3-3 2 2h0v1h-2v1s1 0 1 1l1 1-1-1c-1 0-2 0-3 1v2h0c0 2 0 3 1 5v1c-2-2-4-5-5-7l-3-6c0-1-1-1 0-2 2 0 3-1 5-2h1l2-2z" class="Z"></path><path d="M521 217c2 2 3 5 6 6 0 1 1 1 1 1 0 2 0 3 1 5v1c-2-2-4-5-5-7l-3-6z" class="R"></path><path d="M527 223l-1-1c0-1-1-1-1-2h0l-2-2v-1c1-2 4-3 6-4h1c-1 1-3 3-3 5v1l3-3 2 2h0v1h-2v1s1 0 1 1l1 1-1-1c-1 0-2 0-3 1v2h0s-1 0-1-1z" class="N"></path><path d="M488 164c1-1 0-1 1 0 1 0 1 1 1 2h0v-1l1-2c1 1 2 2 3 2l3 1c1 1 1 1 2 1 1 1 3 2 5 3 0 0 1 1 2 1l1-1h0v2s1 1 2 1c0 1 0 1 1 1h-3l2 1 1 1c1 0 1 1 2 1v1h-3-1l1 1c0 1 0 2 1 3h0 0 0l1-1c0-1 2-1 2-1 2 1 3 2 4 3h0l3 3c0 1 0 2 1 3 1 0 2 1 2 2 1 1 0 1 1 2 1 0 1 1 1 1h-2 0l-2-2c-2 0-3-1-6-1l-2 2c-1 0-2 0-3 1 0 0-2 2-3 2-1 1-1 2-2 3-1 2-1 4-1 6 0 1 1 1 1 2 1 1 4 1 3 4l-2-1v1 1h-1-1l-1 1h-2l2 3-4-2-7-3c-1-1-1-1-1-2h-1c1-1 1-2 0-3h0v-2h1c1 0 1-1 2-2l-2-2-4-5-1-1c-1-1-1-2-2-4 1 0 1 0 1-1v-5h0l2-1v-2-7c0-3 0-6 1-9v-1z" class="I"></path><path d="M504 194c0-1 1-2 2-2h0v1h1v3h0c-1 1-1 2-2 3-1 2-1 4-1 6 0 1 1 1 1 2 1 1 4 1 3 4l-2-1v1 1c-2-1-4-2-5-4 1 0 2 0 3 1l1-1-1-1c0-1-1-1-2-1 0-1-1-1-1-2s-1-3-1-3l-1-1v-1c1 0 2-1 2-1v-3c1 0 2 0 3-1z" class="R"></path><path d="M493 202l1 1 7 5c1 2 3 3 5 4h-1-1l-1 1h-2l2 3-4-2-7-3c-1-1-1-1-1-2h-1c1-1 1-2 0-3h0v-2h1c1 0 1-1 2-2z" class="B"></path><path d="M493 202l1 1c-1 1-1 2-2 2l-1 1h-1v-2h1c1 0 1-1 2-2z" class="M"></path><path d="M493 207c2 1 3 2 4 3l1 1c1 1 2 2 3 2l2 3-4-2-1-2-4-2c-1-1 0 0 0-2l-1-1z" class="E"></path><path d="M490 206h1c1 1 1 1 2 1l1 1c0 2-1 1 0 2l4 2 1 2-7-3c-1-1-1-1-1-2h-1c1-1 1-2 0-3h0z" class="N"></path><path d="M485 184l2-1v4l2 1c2 2 3 2 6 2 1 0 2-1 4-2v2l2 1s0 1 1 1l1-1c1 0 2-1 2-2 1 1 1 2 1 2-1 1-2 1-2 3-1 1-2 1-3 1-2 1-6 2-8 1-2 0-3 0-4-1s-1 0-2 0l-1-1c-1-1-1-2-2-4 1 0 1 0 1-1v-5h0z" class="d"></path><path d="M485 189l1 1h3 0c1 0 1 1 2 1h0 3v2c2 1 4 1 5 1-3 0-5 0-8-1h0c-1 0-2 0-3-1 0-1 0-1-1-1h-2l1 3c-1-1-1-2-2-4 1 0 1 0 1-1z" class="X"></path><path d="M485 184l2-1v4l2 1c2 2 3 2 6 2h-3l-1 1h0c-1 0-1-1-2-1h0-3l-1-1v-5h0z" class="E"></path><path d="M485 184l2-1v4l-1 1v2h3-3l-1-1v-5h0z" class="F"></path><path d="M495 190c1 0 2-1 4-2v2l2 1s0 1 1 1c0 1-2 1-3 2-1 0-3 0-5-1v-2h-3l1-1h3z" class="H"></path><path d="M495 190c1 0 2-1 4-2v2h-1c1 2 1 2 1 3h-1c-1 0-1-1-2-1 0-1-1-1-2-1h-3l1-1h3z" class="V"></path><path d="M510 182h0l1-1c0-1 2-1 2-1 2 1 3 2 4 3h0l3 3c0 1 0 2 1 3 1 0 2 1 2 2 1 1 0 1 1 2 1 0 1 1 1 1h-2 0l-2-2c-2 0-3-1-6-1l-2 2c-1 0-2 0-3 1 0 0-2 2-3 2h0v-3h-1v-1h0c-1 0-2 1-2 2 0-2 1-2 2-3 0 0 0-1-1-2 0 1-1 2-2 2l-1 1c-1 0-1-1-1-1l-2-1v-2l1-1h1v1l1-1 1-1c1 0 2 0 3 1h0 1 1c1-1 2-2 2-3v-2z" class="Z"></path><path d="M512 187c0-1 1-1 2-2l1 1h1v1h0s-1 0-1 1c-1 1-2 1-3 2h0v-3z" class="T"></path><path d="M511 186c1-2 1-3 2-3 2 1 3 2 5 3h0l-1 1v-1h-2l-1-1c-1 1-2 1-2 2l-2 1 1-2z" class="b"></path><path d="M511 186c0-2 0-3 1-4 2 0 3 0 5 1h0l3 3c0 1 0 2 1 3 1 0 2 1 2 2 1 1 0 1 1 2 1 0 1 1 1 1h-2 0l-2-2c-2 0-3-1-6-1v-1c0-1 1-2 2-3l1-1h0c-2-1-3-2-5-3-1 0-1 1-2 3z" class="I"></path><path d="M518 186h0v2l3 4c-2 0-3-1-6-1v-1c0-1 1-2 2-3l1-1h0z" class="R"></path><path d="M510 182h0l1-1c0-1 2-1 2-1 2 1 3 2 4 3-2-1-3-1-5-1-1 1-1 2-1 4l-1 2c-1 2-1 3-3 5h-1v-1h0c-1 0-2 1-2 2 0-2 1-2 2-3 0 0 0-1-1-2 0 1-1 2-2 2l-1 1c-1 0-1-1-1-1l-2-1v-2l1-1h1v1l1-1 1-1c1 0 2 0 3 1h0 1 1c1-1 2-2 2-3v-2z" class="C"></path><path d="M500 187h1v1l1-1c1 1 3 1 3 2s-1 2-2 2l-1 1c-1 0-1-1-1-1l-2-1v-2l1-1z" class="B"></path><path d="M500 187h1v1l1 1c0 1-1 1-1 2l-2-1v-2l1-1z" class="D"></path><defs><linearGradient id="AY" x1="506.688" y1="173.51" x2="500.615" y2="180.509" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#383738"></stop></linearGradient></defs><path fill="url(#AY)" d="M488 164c1-1 0-1 1 0 1 0 1 1 1 2h0v-1l1-2c1 1 2 2 3 2l3 1c1 1 1 1 2 1 1 1 3 2 5 3 0 0 1 1 2 1l1-1h0v2s1 1 2 1c0 1 0 1 1 1h-3l2 1 1 1c1 0 1 1 2 1v1h-3-1l1 1c0 1 0 2 1 3h0 0v2c0 1-1 2-2 3h-1-1 0c-1-1-2-1-3-1l-1 1-1 1v-1h-1l-1 1c-2 1-3 2-4 2-3 0-4 0-6-2l-2-1v-4-2-7c0-3 0-6 1-9v-1z"></path><path d="M495 172c3 1 5 2 7 4v1l-1 1c0 1-1 1-1 2 0-1-1-2-1-3-1-1-3-2-4-3-1 0-1-1-1-1l1-1z" class="H"></path><path d="M488 164c1-1 0-1 1 0 1 0 1 1 1 2h0c3 1 5 2 7 3l1 1 1 1h-1-1-1c-1-1-3-1-4-1s-3 1-4 0v-5-1z" class="h"></path><defs><linearGradient id="AZ" x1="494.998" y1="170.111" x2="499.492" y2="166.014" xlink:href="#B"><stop offset="0" stop-color="#484545"></stop><stop offset="1" stop-color="#626363"></stop></linearGradient></defs><path fill="url(#AZ)" d="M491 163c1 1 2 2 3 2l3 1c1 1 1 1 2 1 1 1 3 2 5 3 0 0 1 1 2 1l1-1h0v2s1 1 2 1c0 1 0 1 1 1h-3c-3-2-7-4-10-5-2-1-4-2-7-3v-1l1-2z"></path><path d="M502 177v-1c3 2 4 3 4 7v4c-1-1-2-1-3-1l-1 1-1 1v-1h-1l1-4-1-3c0-1 1-1 1-2l1-1z" class="G"></path><path d="M502 177h1v2l-1 1c-1-1-1-1-1-2l1-1z" class="D"></path><path d="M501 183l1 1v1 2l-1 1v-1h-1l1-4z" class="V"></path><path d="M488 165v5c1 1 3 0 4 0 1 1 2 1 3 1v1l-1 1s0 1 1 1c1 1 3 2 4 3 0 1 1 2 1 3l1 3-1 4-1 1c-2 1-3 2-4 2-3 0-4 0-6-2l-2-1v-4-2-7c0-3 0-6 1-9z" class="I"></path><path d="M492 185v-4c0-1 0-1 1-2l1 1c1 0 1 1 2 2v3c-1 1-1 1-1 2h0v2h0c0-1 0-1-1-2-1 0 0-1-1-1 0-1-1-1-1-1z" class="K"></path><path d="M494 180c1 0 1 1 2 2v3c-1-1-1-1-2-1-1-1-1-2-1-4h1z" class="J"></path><path d="M489 188l1-1c1-2 0-3 0-5v-1c0-2 1-4 1-5h1 3v1c-1-1-1-1-2 0h-2v7l1 1s1 0 1 1c1 0 0 1 1 1 1 1 1 1 1 2v1c-3 0-4 0-6-2z" class="C"></path><path d="M488 165v5c1 1 3 0 4 0 1 1 2 1 3 1v1l-1 1s0 1 1 1c1 1 3 2 4 3-1 1-2 1-3 1l-1-1v-1h-3-1c0 1-1 3-1 5v1c0 2 1 3 0 5l-1 1-2-1v-4-2-7c0-3 0-6 1-9z" class="d"></path><path d="M487 174l1 1 3-3h0v1h1c-1 1-2 1-2 2-1 1 0 2-2 3h0l-1-1c0 1 1 3 0 4v-7z" class="C"></path><path d="M247 127l9-5h0v1h1-1l1 1c-1 1-1 4-1 6l-1 1v3 1 1l1 1c1-1 2-1 3-2 1 3 0 5 0 8l-1 3v2 1c-1 0-1 1 0 2-2 1-6 2-7 4h-2 0-1c-2 0-3 1-4 1l-2 3-7 4-5 3c-3 3-7 6-8 10h-1v-9c-1-1-1-2-1-3h-1v-1l-1-1h-1l-1 3h0c0 1-1 2-2 2v1 1c-1 2-4 2-5 3-3 1-7 2-10 4-1 1-3 3-4 3-2 1-4 2-5 2-3 2-6 4-8 6-2 1-3 2-5 3a57.31 57.31 0 0 0-11 11l-2 2v1h-3l-1 1s-1 0-1 1l-1-1c-2 1-2 2-3 3h-2l-1 1-3 3c-2 2-4 4-5 7-1-1-1-4-1-5-1-2-1-4-1-6l-2-5h0v-2h-1l-1 2h0-1l-3 1 2-1c0-2-2-5-3-7 0-1-1-1-1-2-1 0-1 0-2-1h0l1-2 2-2h0 1c5-1 10-1 14-4 3-1 6-4 9-6l10-7c13-9 26-17 40-25 5-3 10-6 15-8 1 0 4-2 4-2l21-10z" class="g"></path><path d="M202 156h0 0v2c0 1 0 1 1 1 2 0 2 0 3-1v-2l1 1c1 1 0 2 0 3-1 1-2 2-3 2s-2 0-3-1c0 0-1-2-1-3s1-1 2-2z" class="S"></path><path d="M226 137v1c0 1 0 0 1 0v1h-1c-2 2-2 4-3 6s-4 5-6 7l-1-1c1-1 1-2 1-3 0 1 1 2 0 3h1v-1c1 0 1-1 0-1 3-2 3-6 4-8h1l-1-1v-1c1 0 4-2 4-2z" class="a"></path><path d="M216 151l1 1c2-2 5-5 6-7s1-4 3-6h1c0 2-2 7-2 8h1l-1 1c-3 2-5 4-8 6l-1-1v-2z" class="L"></path><path d="M209 166c0-1 0-3 1-3l1-1 1-1c0-2 1-3 1-4 1-3 1-7 3-11 0-1 1-3 2-4l-1 6c0 1 0 2-1 3v2l1 1c0 1-3 2-3 3-1 1 0 4 0 5v1c-1 2-4 2-5 3z" class="O"></path><path d="M133 189h0 1 0c3 2 4 5 5 8 0 2 1 3 2 4h1-1c0 1 0 2-1 2h0v-2h-1l-1 2h0-1l-3 1 2-1c0-2-2-5-3-7 0-1-1-1-1-2-1 0-1 0-2-1h0l1-2 2-2z" class="C"></path><path d="M133 189h0 1 0l-1 1v1h0c0 2 1 4 2 5 2 3 3 4 3 7h0-1l-3 1 2-1c0-2-2-5-3-7 0-1-1-1-1-2-1 0-1 0-2-1h0l1-2 2-2z" class="b"></path><path d="M141 201v-1c2-3 2-8 2-12 1 4 0 8 1 11 1 1 1 3 1 4h0 1-1c-1 1-1 2-1 2l1 1v1 6 1 1c1-1 2-2 2-3 1 0 1-1 2-1 1-1 1-2 3-2l-3 3c-2 2-4 4-5 7-1-1-1-4-1-5-1-2-1-4-1-6l-2-5c1 0 1-1 1-2h1-1z" class="D"></path><path d="M225 148l1 1-2 1c1 2 1 3 0 5 2 1 2 1 3 1l2-3v2c0 1-1 2 0 3l-1 1-1 1v1c-1 0-1 1-2 1l-1-1c0 2-1 3-2 4l-1 2c-1-1-1-2-1-3h-1v-1l-1-1h-1l-1 3h0c0 1-1 2-2 2v-1h-1c1-1 1-2 1-3v-1c0-1-1-4 0-5 0-1 3-2 3-3 3-2 5-4 8-6z" class="C"></path><path d="M214 163v-2h1c1 0 1 1 1 2v2h0c0 1-1 2-2 2v-1h-1c1-1 1-2 1-3z" class="h"></path><path d="M218 158h3l1 1 2 2c0 2-1 3-2 4l-1 2c-1-1-1-2-1-3l1-3h-1-1 0v-1h1v-1l-2-1z" class="Z"></path><path d="M218 158h-2c1-2 4-4 5-5h0c1 1 1 1 1 2 1 1 0 3 0 4l-1-1h-3z" class="H"></path><path d="M221 153l3-3c1 2 1 3 0 5 2 1 2 1 3 1l2-3v2c0 1-1 2 0 3l-1 1-1 1v1c-1 0-1 1-2 1l-1-1-2-2c0-1 1-3 0-4 0-1 0-1-1-2h0z" class="G"></path><path d="M222 159c1 0 2 0 3 1 0-1 0-2 1-3v3h1v1c-1 0-1 1-2 1l-1-1-2-2z" class="H"></path><path d="M224 155c2 1 2 1 3 1l2-3v2c0 1-1 2 0 3l-1 1h-1v-3h-1l-1 1v-1h-1v-1z" class="V"></path><path d="M214 163c0 1 0 2-1 3h1v1 1 1c-1 2-4 2-5 3-3 1-7 2-10 4-1 1-3 3-4 3-2 1-4 2-5 2-3 2-6 4-8 6-2 1-3 2-5 3a57.31 57.31 0 0 0-11 11l-2 2v1h-3l-1 1s-1 0-1 1l-1-1c-2 1-2 2-3 3h-2c3-4 7-7 11-10 7-6 14-13 23-18l11-7 11-7c1-1 4-1 5-3z" class="h"></path><path fill="#121212" d="M213 166h1v1 1c-1 2-4 2-5 2-2 1-3 2-5 2h0v-1l9-5z"></path><path d="M214 163c0 1 0 2-1 3l-9 5c-2 1-3 2-6 2l11-7c1-1 4-1 5-3z" class="P"></path><path d="M247 127l9-5h0v1h1-1l1 1c-1 1-1 4-1 6l-1 1v3 1 1l1 1c1-1 2-1 3-2 1 3 0 5 0 8l-1 3v2 1c-1 0-1 1 0 2-2 1-6 2-7 4h-2 0-1c-2 0-3 1-4 1l-2 3-7 4-5 3c-3 3-7 6-8 10h-1v-9l1-2c1-1 2-2 2-4l1 1c1 0 1-1 2-1v-1l1-1 1-1c-1-1 0-2 0-3v-2l-2 3c-1 0-1 0-3-1 1-2 1-3 0-5l2-1-1-1 1-1h-1c0-1 2-6 2-8v-1c-1 0-1 1-1 0v-1l21-10z" class="H"></path><path d="M230 153h1c0 1 0 1 1 2 1 0 1-1 2-1h2v2c-1 1-2 2-3 2h-1 0c-2-1-2-2-3-3v-2h1z" class="C"></path><defs><linearGradient id="Aa" x1="256.063" y1="138.195" x2="251.47" y2="146.217" xlink:href="#B"><stop offset="0" stop-color="#71706f"></stop><stop offset="1" stop-color="#868486"></stop></linearGradient></defs><path fill="url(#Aa)" d="M254 137h2v2 1c-1 1-1 2-1 4-1 1-1 3-3 4h0c1-3 0-5 1-8l1-3z"></path><path d="M255 144l1-1c1 2 0 3 1 5 0-1 0-1 1-2v2 1c-1 0-2 1-2 1h-1c0-1-1-1-1-2-1 1-2 2-2 3l-2 2h-1c1-2 2-3 3-5h0 0c2-1 2-3 3-4z" class="E"></path><path d="M256 137c1-1 2-1 3-2 1 3 0 5 0 8l-1 3c-1 1-1 1-1 2-1-2 0-3-1-5l-1 1c0-2 0-3 1-4v-1-2h0z" class="F"></path><path d="M236 145c1 2 0 5 1 6 0 1 1 2 1 3 1 1 1 2 1 3-1 1-1 1-1 2h2l-1 1h-2c0-2 0-5-1-7 0 0-1 0-1-1h-1 0v-1h-1c0 1 0 1-1 2h-1-1v-1h1 0-1v-1c1-1 1-1 2-3 1-1 3-2 4-3z" class="D"></path><path d="M248 141c1-1 2-1 3-2l-1 2c-1 1-1 2-1 3-1 2-2 4-2 5-2 3-3 9-6 10h-1l3-6 1-1c-1-4 2-8 4-11z" class="V"></path><path d="M247 141h1c-2 3-5 7-4 11l-1 1-3 6h0-2c0-1 0-1 1-2 0-1 0-2-1-3 0-1-1-2-1-3-1-1 0-4-1-6 4-1 8-2 11-4z" class="L"></path><path d="M239 157h0c1-1 1-3 1-4v-1c1-1 2-1 3-1v2l-3 6h0-2c0-1 0-1 1-2z" class="a"></path><path d="M229 155c1 1 1 2 3 3h0 1v3l-2 1h0c-1 1-1 1-2 1v1c1-1 1-1 2-1h0 0 4l-5 3c-3 3-7 6-8 10h-1v-9l1-2c1-1 2-2 2-4l1 1c1 0 1-1 2-1v-1l1-1 1-1c-1-1 0-2 0-3z" class="U"></path><path d="M229 155c1 1 1 2 3 3h0 0c0 2 0 2-1 3h-1v-3h-1c-1-1 0-2 0-3z" class="H"></path><path d="M221 167l1-2c0 2 1 3 0 5h1 0c1-1 2-2 4-3h0c1-1 2-1 3-1-3 3-7 6-8 10h-1v-9z" class="N"></path><path d="M247 127l9-5h0v1h1-1l1 1c-1 1-1 4-1 6l-1 1v3 1 1l1 1h0-2l-3 2c-1 1-2 1-3 2h-1c-3 2-7 3-11 4-1 1-3 2-4 3-1 2-1 2-2 3v1h1 0-1v1h-1l-2 3c-1 0-1 0-3-1 1-2 1-3 0-5l2-1-1-1 1-1h-1c0-1 2-6 2-8v-1c-1 0-1 1-1 0v-1l21-10z" class="g"></path><path d="M228 145l9-9c0 2-1 3-2 5h1c1 0 2-1 3-2v1h1c0-1 0-2 1-3 0 1-1 2 0 3h0c-4 1-7 3-10 4-1 0-2 1-3 1z" class="L"></path><path d="M248 128h1c0 2-1 4-2 6 0 1 0 2-1 3l1 1c1-1 1-2 2-2 0-1 2 0 2 0h1v1l3-1 1 1h0-2l-3 2c-1 1-2 1-3 2h-1v-2c-7 2-13 5-19 9l-2 1-1-1 1-1 2-2c1 0 2-1 3-1 3-1 6-3 10-4h0c1 0 3-1 3-2 1-1 1-2 1-3 1-3 2-5 3-7z" class="G"></path><path d="M255 136l1 1h0-2l-3 2c-1 1-2 1-3 2h-1v-2l5-2 3-1z" class="g"></path><path d="M228 148c6-4 12-7 19-9v2c-3 2-7 3-11 4-1 1-3 2-4 3-1 2-1 2-2 3v1h1 0-1v1h-1l-2 3c-1 0-1 0-3-1 1-2 1-3 0-5l2-1 2-1z" class="j"></path><path d="M226 149l2-1h0c-1 1-1 2-2 4h0c2 0 4-2 6-4-1 2-1 2-2 3v1h1 0-1v1h-1l-2 3c-1 0-1 0-3-1 1-2 1-3 0-5l2-1z" class="L"></path><defs><linearGradient id="Ab" x1="255.941" y1="131.18" x2="251.273" y2="129.241" xlink:href="#B"><stop offset="0" stop-color="#9d9b9b"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#Ab)" d="M247 127l9-5h0v1h1-1l1 1c-1 1-1 4-1 6l-1 1v3 1 1l-3 1v-1h-1s-2-1-2 0c-1 0-1 1-2 2l-1-1c1-1 1-2 1-3 1-2 2-4 2-6h-1l-1-1z"></path><path d="M247 127l9-5h0v1h1-1c-4 3-5 5-7 9 0 1 0 2-1 3l-1 1 1-2h-1c1-2 2-4 2-6h-1l-1-1z" class="a"></path><path d="M237 236s1-1 2-1h13 25c3 0 8-1 11 1h0 0l1 1h-2l-1 1h-1-5c1 1 3 1 5 1 1-1 2-1 2-1 7-1 14-1 20 0 2 1 4 0 6 1 1 0 1 0 2-1 2 0 7 0 9 1v1c1 0 1 0 2 1 2 0 5-1 6 0v1l1 1h2 0v1 1c0 1 0 1-1 2 0 1-1 3-2 3h-1 1v1c-2 1-5 1-8 2h1s1 0 1 1l-3-1c0 1 2 1 3 2h-1v2c-1-1 0 0-1 0h-1c-3-1-5 0-8 0l-23 1h-5c-5 1-10 2-14 4l-5 2h-3c-3 2-6 5-8 7 0 1-1 3-2 3l-2 2h-1l-5 15-6 20-4 11-1-24v-4h-1c-1 1-1 4-2 5-1 2-4 3-5 4-1 4 0 8 0 11-1-4 0-8 0-13v-27l-1-18c0-3 0-6-1-9 3-1 5-3 7-5l1-5c1 0 1 1 1 1l2-2z" class="H"></path><path d="M313 244c1 0 1-1 3 0 1 0 3 1 4 1s1 0 1 1h0c-2 1-2 2-4 2-3 1-6 4-10 2h-1c0-1 0-1 1-1l6-5z" class="G"></path><path d="M313 244c1 1 2 1 2 2s-1 2-2 3c-2 0-4 1-6 1v-1l6-5zm-12 0c2-1 3-1 5-1h0c2 0 3 0 4 1h0c-1 1-2 3-4 4-1 1-3 2-4 3l-1 1h0c-1-1-4-2-5-2l1 1h-2c-1-1-1-1-1-2s1-1 1-2c3 0 4-2 6-3z" class="D"></path><path d="M309 241c2-1 3-1 5-1h1c2 1 5 1 7 1 1 0 1 0 2 1 1 0 3 0 4 1v1c3 0 4-1 7-1v1 1c0 1 0 1-1 2 0 1-1 3-2 3h-1 1v1c-2 1-5 1-8 2h1s1 0 1 1l-3-1-36 1h-3v-1c4-1 7-1 10-1h7 0l1-1c3 1 6 1 9 1 1-1 3 0 4-1 1 0 2-1 4-2h1 0l2-2c1 0 1 0 2-1h0c-1-1-1-2-2-3h-1-1-1v-1c-1 0-3 0-4 1h-2v-1-1l-1-1-3 1z" class="T"></path><path d="M335 245c0 1 0 1-1 2 0 1-1 3-2 3h-1 1v1c-2 1-5 1-8 2h1s1 0 1 1l-3-1-36 1h-3v-1c4-1 7-1 10-1h7 0 5 11c2-1 4-1 6-1h5c1-1 3-2 4-3l3-3z" class="K"></path><path d="M261 250c-1 0-1 0-2-1 1 0 1-1 2-2-1 0-2 0-3 1h0-1l2-3h0c2 0 3-2 4-2 5-1 9-1 14-1 1 0 2 0 4 1h0c2 0 4 0 6 1h0c1 0 2 1 3 1h2c1-1 7-1 9-1-2 1-3 3-6 3 0 1-1 1-1 2s0 1 1 2h2l-1-1c1 0 4 1 5 2h-7c-3 0-6 0-10 1-2 0-4 2-7 1s-6 0-8-1-6 0-7-2v-1h0-1z" class="M"></path><path d="M268 250c0 1-1 1-1 1-1 0-2 0-3-1v-2h-2 0v-2c2-1 3-2 5-3 3-1 8 0 11 0 0 1 0 1 1 1l2-1c2 0 4 0 6 1h0c1 0 2 1 3 1h2c-1 1-1 1-2 1s-2 0-3-1c-2-1-4-1-6-1s-2 1-4 2l-1-1 1-1c-1-1-3 0-4 0-4 1-5 2-7 4v1c0 1 1 1 2 1z" class="B"></path><path d="M268 250c-1 0-2 0-2-1v-1c2-2 3-3 7-4 1 0 3-1 4 0l-1 1 1 1h0c-1 1-3 2-4 3 2 2 7 2 10 2-4 1-8 1-12 0v-1l-1-1h0l-2 1z" class="O"></path><path d="M270 249h-3c0-1 1-2 2-3 2-1 4-1 6-1h0c-1 2-3 3-4 4h-1 0z" class="D"></path><defs><linearGradient id="Ac" x1="294.364" y1="251.247" x2="277.702" y2="242.253" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#d0cfd0"></stop></linearGradient></defs><path fill="url(#Ac)" d="M292 245c1-1 7-1 9-1-2 1-3 3-6 3 0 1-1 1-1 2s0 1 1 2h2l-1-1c1 0 4 1 5 2h-7v-1c-1 0-1-1-1-1-1-1-2-1-3-1-2 0-4 1-6 1l-1 1c-3 0-8 0-10-2 1-1 3-2 4-3h0c2-1 2-2 4-2s4 0 6 1c1 1 2 1 3 1s1 0 2-1z"></path><path d="M277 246c2-1 2-2 4-2s4 0 6 1h0c-3 1-5 0-7 2-1 0-3 1-4 1s-1 0-1 1h0 2c2 1 5 0 7 1l-1 1c-3 0-8 0-10-2 1-1 3-2 4-3h0z" class="D"></path><path d="M280 238c1 1 3 1 5 1 1-1 2-1 2-1 7-1 14-1 20 0 2 1 4 0 6 1 1 0 1 0 2-1 2 0 7 0 9 1v1c1 0 1 0 2 1 2 0 5-1 6 0v1l1 1h2 0c-3 0-4 1-7 1v-1c-1-1-3-1-4-1-1-1-1-1-2-1-2 0-5 0-7-1h-1c-2 0-3 0-5 1-2 0-3 1-6 1h0l1-1v-1h-1c-2 0-3 1-4 1-2 2-6 2-8 2-4 0-7-2-10 0-2-1-3-1-4-1-5 0-9 0-14 1-1 0-2 2-4 2h0l-2 3h1 0c1-1 2-1 3-1-1 1-1 2-2 2 1 1 1 1 2 1-2 1-5 3-7 4s-4 3-5 4h0c-1 1-1 1-2 1 0-1 0-2-1-2v-1-1c0-1 1-2 1-3l-1 1v-4c0-1-1-1-1-2l1-2c-1-1-1-1-2-1v-1l2-2s1-1 2-1v-1l3-1h10 19z" class="J"></path><path d="M254 240h4 0l-1 2h1c-3 2-6 4-7 6-1 1-1 2-2 2l-2 2c0-1 1-3 0-4v-3c0-1 1-2 2-3h0c2-1 3-1 5-2z" class="X"></path><path d="M254 240h4 0l-1 2c-3 1-5 3-8 4h0c0-2 3-4 5-6z" class="F"></path><path d="M280 238c1 1 3 1 5 1 1-1 2-1 2-1 7-1 14-1 20 0 2 1 4 0 6 1 1 0 1 0 2-1 2 0 7 0 9 1v1c1 0 1 0 2 1 2 0 5-1 6 0v1l1 1h2 0c-3 0-4 1-7 1v-1c-1-1-3-1-4-1-1-1-1-1-2-1-2 0-5 0-7-1h-1c-2 0-3 0-5 1-2 0-3 1-6 1h0l1-1v-1h-1-1-2v-1h-1-2 0-3c-3 1-6 0-8 1-4 1-8 0-11 0h-11l-1-1h0c-1 1-1 1-2 1v-1c-5 0-8 1-12 1-1 0-2 1-3 1 0 0 1-1 2-1v-1l3-1h10 19z" class="K"></path><path d="M258 242c1-1 3-2 5-2 2 2 7 1 9 1 3 0 6 1 10 1 1 0 2-1 3-1 2 0 6 1 8 0l1-1c1 1 3 1 5 1-2 2-6 2-8 2-4 0-7-2-10 0-2-1-3-1-4-1-5 0-9 0-14 1-1 0-2 2-4 2h0l-2 3h1 0c1-1 2-1 3-1-1 1-1 2-2 2 1 1 1 1 2 1-2 1-5 3-7 4s-4 3-5 4h0c-1 1-1 1-2 1 0-1 0-2-1-2v-1-1c0-1 1-2 1-3h0l2-2c1 0 1-1 2-2 1-2 4-4 7-6z" class="F"></path><path d="M249 250l1 1c2-1 4-3 5-2 2 0 2-1 3 0-1 2-3 3-5 4-1 1-2 1-4 1-1 0-1 1-2 1v1 2h2 0c-1 1-1 1-2 1 0-1 0-2-1-2v-1-1c0-1 1-2 1-3h0l2-2z" class="c"></path><defs><linearGradient id="Ad" x1="274.958" y1="258.068" x2="239.862" y2="277.309" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#Ad)" d="M261 250h1 0v1c1 2 5 1 7 2s5 0 8 1 5-1 7-1v1h3l36-1c0 1 2 1 3 2h-1v2c-1-1 0 0-1 0h-1c-3-1-5 0-8 0l-23 1h-5c-5 1-10 2-14 4l-5 2h-3c-3 2-6 5-8 7 0 1-1 3-2 3l-2 2h-1l-5 15v-3-1s1-1 1-2v-1-1l-3 3-2-2 1-1v-1h-1c1-1 1-3 1-4-1-1-1-2-1-4v-2c0-1 1-1 1-2v-3h0c0-1 0-1-1-2 0-2 0-5 1-7v-3-1l2-1 1-1c0 1-1 2-1 3v1 1c1 0 1 1 1 2 1 0 1 0 2-1h0c1-1 3-3 5-4s5-3 7-4z"></path><path d="M252 276c1-2 3-4 4-5h0 1c0 1-1 3-2 3l-2 2h-1z" class="Y"></path><path d="M248 277h1 0c0 2-2 3-3 4h-1v-2c1-1 2-1 3-2z" class="T"></path><path d="M246 253l1-1c0 1-1 2-1 3v1 1c1 0 1 1 1 2 1 0 1 0 2-1h0c1-1 3-3 5-4v1 3h0l-1 2-9 6v-8-3-1l2-1z" class="X"></path><path d="M261 250h1 0v1c1 2 5 1 7 2s5 0 8 1 5-1 7-1v1h3c-6 1-10 2-16 4 1 0 1-1 2-1h-3c1-1 1-1 3-2h0c-1-1-1-1-2-1l-1 1h-1-1c-3 0-7-1-9 0s-4 3-6 5l1-2h0v-3-1c2-1 5-3 7-4z" class="R"></path><path d="M287 254l36-1c0 1 2 1 3 2h-1v2c-1-1 0 0-1 0h-1c-3-1-5 0-8 0l-23 1h-5c-5 1-10 2-14 4l-5 2h-3c-3 2-6 5-8 7h-1 0c4-6 9-9 15-13 6-2 10-3 16-4z" class="a"></path><path d="M265 264c1-1 4-3 5-3l3 1-5 2h-3z" class="L"></path><path d="M237 236s1-1 2-1h13 25c3 0 8-1 11 1h0 0l1 1h-2l-1 1h-1-5-19-10l-3 1v1c-1 0-2 1-2 1l-2 2v1c1 0 1 0 2 1l-1 2c0 1 1 1 1 2v4l-2 1v1 3c-1 2-1 5-1 7 1 1 1 1 1 2h0v3c0 1-1 1-1 2v2c0 2 0 3 1 4 0 1 0 3-1 4h1v1l-1 1 2 2 3-3v1 1c0 1-1 2-1 2v1 3l-6 20-4 11-1-24v-4h-1c-1 1-1 4-2 5-1 2-4 3-5 4-1 4 0 8 0 11-1-4 0-8 0-13v-27l-1-18c0-3 0-6-1-9 3-1 5-3 7-5l1-5c1 0 1 1 1 1l2-2z" class="d"></path><path d="M243 288v2 1h-1c0-1 0-1-1-2 0-1-1-3-1-5v-13c0-2 0-6 1-8v-4c0-1 1-3 1-4 0-2-1-4-1-6 0-3 0-6 1-9 3-2 5-2 9-2l-3 1v1c-1 0-2 1-2 1l-2 2v1c1 0 1 0 2 1l-1 2c0 1 1 1 1 2v4l-2 1v1 3c-1 2-1 5-1 7 1 1 1 1 1 2h0v3c0 1-1 1-1 2v2c0 2 0 3 1 4 0 1 0 3-1 4h1v1l-1 1v4z" class="W"></path><path d="M244 243v1c1 0 1 0 2 1l-1 2c0 1 1 1 1 2v4l-2 1 1-2v-2c1 0 0-1 0-1h-1c-1-1 0-2 0-4h1-1v-1-1z" class="P"></path><path d="M243 288l-1-2c0-3-1-9 0-12v-6-9c0-2 1-3 2-4v3c-1 2-1 5-1 7 1 1 1 1 1 2h0v3c0 1-1 1-1 2v2c0 2 0 3 1 4 0 1 0 3-1 4h1v1l-1 1v4z" class="Q"></path><path d="M234 237c1 0 1 1 1 1l2-2-1 62v-4h-1c-1 1-1 4-2 5-1 2-4 3-5 4-1 4 0 8 0 11-1-4 0-8 0-13v-27l-1-18c0-3 0-6-1-9 3-1 5-3 7-5l1-5z" class="Y"></path><path d="M228 274h0l1-1c0-1 3-3 5-4v17 8c-2 2-4 4-6 5v2-27z" class="V"></path><path d="M216 165l1-3h1l1 1v1h1c0 1 0 2 1 3v9h1v1 7h1l2-2v2-1c1 0 2 1 3 1 0 1-1 2-1 4-2 0-3 1-4 3h0c-1 3-3 4-4 6l-2 2-1 1v1c-1 0-1 1-2 2l-3-1c-1 1-4 3-5 4v2c-1 1-1 1-2 1-1 1-2 3-3 4l-6 5v-2c1 0 1 0 2-1-1-1-1 0-2-1-1 0 0 0-1-1h0c-1 2-2 2-4 4l-1 1-1 2c-2 2-4 5-6 7l-4 6c-2 4-4 8-7 11h-1c0 1 0 1-1 2h0c0 1 0 1-1 2h0c1 1 1 1 0 2 0 0-1 1-2 1h1v-2-2h-4c-3-1-7-1-11 0-2 1-4 2-5 3-1 2-3 4-2 5v1h-1v3 1c0 1-1 1-2 1 0 1 0 1-1 1l2 2h0l-1 1v1l-6 3h-2-1-3l-4-2 1-1c2 1 3 1 4 1h1l2-1c-1-1 0-2 0-4l1-1 1-2v-6h3v-5c0-2 0-4 1-6h1c0-1 0-1 1-1v-2c-1 0-1 0-2-1 0-1 0-1-1-2h0l-2-2-3-3-1 1c-1-1-2-2-4-3v2-1l-2-3c1-1 0-3 0-4 1-2 0-3 0-5 0-1-1-1-1-2h1v-2c0-1 0-1 1-2v-1h2s1-1 2 0h1 1 0 2c2 0 4 3 7 3 0 1 0 4 1 5 1-3 3-5 5-7l3-3 1-1h2c1-1 1-2 3-3l1 1c0-1 1-1 1-1l1-1h3v-1l2-2a57.31 57.31 0 0 1 11-11c2-1 3-2 5-3 2-2 5-4 8-6 1 0 3-1 5-2 1 0 3-2 4-3 3-2 7-3 10-4 1-1 4-1 5-3v-1-1c1 0 2-1 2-2h0z" class="I"></path><path d="M144 248h2c0 1 0 2-1 3h-1l-1-1c0-1 1-1 1-2z" class="Q"></path><path d="M136 253h3c0 2 0 5-1 8h0v-3h0l-2 1v-6z" class="T"></path><path d="M178 216s0-1-1-1-1 1-1 0c0-2-1-3-1-4l1 1v2 1h1c0-1 0-2 1-3h-1s-1 0-1-1h0l3 1v1c1 2 2 3 2 6h-1c-1-1-1-1-1-3h-1z" class="J"></path><path d="M159 206c0-1 1-1 1-1l1-1h3v-1l2-2h0l3 3c1 1 2 3 3 3 1 1 2 0 2 1h1-2c-2 0-3 0-5-1-1-1-3-2-5-2 0 0 0 1-1 1v1h-1l-2-1z" class="C"></path><defs><linearGradient id="Ae" x1="126.695" y1="265.405" x2="142.597" y2="260.68" xlink:href="#B"><stop offset="0" stop-color="#494846"></stop><stop offset="1" stop-color="#606061"></stop></linearGradient></defs><path fill="url(#Ae)" d="M138 261v1h1l1-1c1-2 0-3 1-4h1c1 2 0 3 0 4s0 1-1 1l2 2h0l-1 1v1l-6 3h-2-1-3l-4-2 1-1c2 1 3 1 4 1h1l2-1c-1-1 0-2 0-4l1-1 1-2 2-1h0v3h0z"></path><path d="M141 262l2 2h0l-1 1c0 1 0 1-1 1s-2 0-3 1l-1-1c1-1 0-1 1-1l3-3z" class="I"></path><path d="M138 267c1-1 2-1 3-1s1 0 1-1v1l-6 3h-2-1-3c1-1 3-1 4-1 1-1 2-1 4-1z" class="Z"></path><path d="M136 259l2-1h0v3c-1 2-2 3-4 5-1-1 0-2 0-4l1-1 1-2z" class="E"></path><path d="M179 212l1 1h1l2 1c1 1 2 1 4 1 0 1-1 2-1 3 1 1 0 2-1 3v1 1s-2 1-2 2h-1v2l-4 6c0-1 0-1 1-2v-2-3c-1-2-1-4-2-6 0-1-1-2-1-3h1 0 1v-1h1c0 2 0 2 1 3h1c0-3-1-4-2-6v-1z" class="Q"></path><path d="M162 207v-1c1 0 1-1 1-1 2 0 4 1 5 2 2 2 3 3 4 5s3 5 3 7v2 3h0-1c-1 0-2-1-3-1-1-2-1-5-2-7h0l-3-5-4-4z" class="D"></path><path d="M162 207v-1c1 0 1-1 1-1 2 0 4 1 5 2 2 2 3 3 4 5h-1c-2-1-2-3-4-3h-1v2l-4-4z" class="f"></path><path d="M171 218c0-1 0-2-1-4v-2c1 2 1 3 2 4 1 2 2 3 3 5v-2 2 3h0-1c-1 0-2-1-3-1-1-2-1-5-2-7 1 1 1 1 1 2h1z" class="a"></path><path d="M169 216c1 1 1 1 1 2h1l1 1v3l2 1h1v-2-2 2 3h0-1c-1 0-2-1-3-1-1-2-1-5-2-7z" class="O"></path><path d="M179 195c0-1 0-3 1-3 4-3 8-8 13-10 1-1 1-2 3-2 1-1 1-1 2-1 0 1-1 1-1 2h-1c-1 1-1 3-2 4l1 3v5c-1 0-1 1-2 1s-1 0-2 1c-1-1-2-1-2-1h-1c-1-1-1-1-2-1h-2c-2 0-3 1-5 2z" class="T"></path><path d="M194 185l1 3v5c-1 0-1 1-2 1s-1 0-2 1c-1-1-2-1-2-1h-1c0-2 1-2 1-3 2 1 1 1 2 2l3-3c0-1-1-3 0-5h0z" class="b"></path><path d="M175 221c0 2 1 3 0 5v3c-1 6-4 10-9 13-3 2-6 2-9 2h-4c0-1-1-1-1-2-1-1-1-1-1-2h1 6l1-1h1c3-1 8-4 9-8h0c1-1 1-2 1-4v-3l1-1c1 0 2 1 3 1h1 0v-3z" class="a"></path><path d="M175 221c0 2 1 3 0 5v3h0c0-1 0-2-1-3-1 0-2-1-3-1l-1 2v-3l1-1c1 0 2 1 3 1h1 0v-3z" class="F"></path><path d="M175 229h0c-1 6-4 10-9 13-3 2-6 2-9 2v-2h1 1c1 1 1 0 2-1 0 0 1 1 2 0 0 0 1 0 1-1 1 0 2-1 2-1 2-2 2-3 4-4h0v-2-1c0-1 1-1 2-2h0c0-1 2-1 3-1z" class="D"></path><path d="M172 230l1 1v1c-1 1-2 1-3 1v-1c0-1 1-1 2-2z" class="i"></path><path d="M179 195c2-1 3-2 5-2v3h0-1v1c2 0 4 0 5 1h1c1 1 2 2 3 4 0 1 0 2 1 3h0v5h3v1h-1c-1 1-1 1-1 2-1 2-2 2-4 4l-1 1-1 2c-2 2-4 5-6 7v-2h1c0-1 2-2 2-2v-1-1c1-1 2-2 1-3 0-1 1-2 1-3-2 0-3 0-4-1l-2-1v-2h-1c-1-1-2-1-4-3 0-2-2-5-2-7 0-3 3-5 5-6z" class="J"></path><path d="M188 198h1c1 1 2 2 3 4h-2v-1c-2-1-5-1-7 0v-1c2-1 3-1 5-2z" class="I"></path><path d="M183 197c2 0 4 0 5 1-2 1-3 1-5 2h-2c-1 0-1-1-2-1 1-1 3-2 4-2z" class="S"></path><path d="M180 205c2-2 3-3 6-3h1c1 1 1 2 2 3-1 0-1 1-2 2v1h-1-1v-1s1 0 2-1v-2c-1 0-2-1-3-1-1 1-1 1-2 3h-2v-1zm13 0h0v5h3v1h-1c-1 1-1 1-1 2-1 2-2 2-4 4l-1 1c0-2 1-3 1-5 1-3 0-3 2-5 0-1 0-2 1-3z" class="X"></path><path d="M193 210h3v1h-1c-1 1-1 1-1 2-1 2-2 2-4 4 1-2 3-4 3-7z" class="L"></path><path d="M182 206c1-2 1-2 2-3 1 0 2 1 3 1v2c-1 1-2 1-2 1v1c0 1 0 3-1 3v1c-1 0-2 0-3-1h-1l1-1-1-1h-1c0-1 1-2 1-3h2z" class="E"></path><path d="M182 206l1 1 1 3h-1c-1 0-2 0-3-1h-1c0-1 1-2 1-3h2z" class="Z"></path><path d="M182 206l1 1c-2 1-2 1-3 1v-2h2z" class="N"></path><path d="M187 210l1-1h1v1h1v1c-1 1-1 3-1 5 0 1-1 2-1 4-2 2-4 5-6 7v-2h1c0-1 2-2 2-2v-1-1c1-1 2-2 1-3 0-1 1-2 1-3-2 0-3 0-4-1l-2-1v-2c1 1 2 1 3 1 2 0 2-1 3-2z" class="R"></path><path d="M187 210h1c1 3 0 3-1 5-2 0-3 0-4-1l-2-1v-2c1 1 2 1 3 1 2 0 2-1 3-2z" class="d"></path><path d="M179 195c2-1 3-2 5-2v3h0-1v1c-1 0-3 1-4 2 1 0 1 1 2 1h2v1c-1 0-1 1-2 2s-1 1-1 2v1c0 1-1 2-1 3h1l1 1-1 1c-1-1-2-1-4-3 0-2-2-5-2-7 0-3 3-5 5-6z" class="L"></path><path d="M177 203c1 1 1 1 2 1l2-1c-1 1-1 1-1 2v1c0 1-1 2-1 3-2-2-2-4-2-6z" class="Q"></path><path d="M177 203c0-2 1-3 2-4 1 0 1 1 2 1h2v1c-1 0-1 1-2 2l-2 1c-1 0-1 0-2-1z" class="C"></path><path d="M195 188c2 3 2 4 5 6 1 2 3 3 5 3 2 1 3 1 5 0 0 0 1 0 1 1l1-1c1 1 2 1 3 1l-2 3-2 1c-1 1-4 3-5 4v2c-1 1-1 1-2 1-1 1-2 3-3 4l-6 5v-2c1 0 1 0 2-1-1-1-1 0-2-1-1 0 0 0-1-1h0c0-1 0-1 1-2h1v-1h-3v-5h0c-1-1-1-2-1-3-1-2-2-3-3-4h-1c-1-1-3-1-5-1v-1h1 0v-3h2c1 0 1 0 2 1h1s1 0 2 1c1-1 1-1 2-1s1-1 2-1v-5z" class="K"></path><path d="M212 197c1 1 2 1 3 1l-2 3c-1-2-1-2-1-4z" class="R"></path><path d="M200 194c1 2 3 3 5 3v1c1 1 1 1 1 3 0 1-1 1-2 2l-1 1h0 0v-1l-2-5 1-1h-1-1-2c1-1 1-1 1-2l1-1z" class="P"></path><path d="M202 197c1 0 1 1 2 2 0 2 1 2-1 4l-2-5 1-1z" class="b"></path><path d="M199 212l7-6v2c-1 1-1 1-2 1-1 1-2 3-3 4l-6 5v-2c1 0 1 0 2-1-1-1-1 0-2-1-1 0 0 0-1-1h0c0-1 0-1 1-2h1c0 1 0 1 1 2 1 0 1-1 2-1z" class="Y"></path><path d="M198 205v-5-1c1 0 1 0 1 1 1 1 1 2 1 3v6s0 1-1 1v2c-1 0-1 1-2 1-1-1-1-1-1-2v-1l1-1 1-2v-2z" class="I"></path><path d="M193 205c0-1 0-1 1-2s2-1 3-1c1 1 1 2 1 3v2l-1 2-1 1h-3v-5z" class="V"></path><path d="M197 209l-3-1h-1c1-2 2-3 3-3 2 1 1 1 2 2l-1 2z" class="a"></path><path d="M195 188c2 3 2 4 5 6l-1 1c0 1 0 1-1 2h2v2h0l-1-1-2 2-1-1c-2-2-3-3-5-4 1-1 1-1 2-1s1-1 2-1v-5z" class="R"></path><path d="M198 197h-1l-1-1c1-1 2-1 3-1 0 1 0 1-1 2z" class="K"></path><path d="M184 193h2c1 0 1 0 2 1h1s1 0 2 1c2 1 3 2 5 4l1 1v2c-1 0-2 0-3 1s-1 1-1 2h0c-1-1-1-2-1-3-1-2-2-3-3-4h-1c-1-1-3-1-5-1v-1h1 0v-3z" class="L"></path><path d="M184 193h2c1 0 1 0 2 1h1-2c0 1 0 1 1 2l1 1c1 0 1 0 2 1h-2-1c-1-1-3-1-5-1v-1h1 0v-3z" class="D"></path><path d="M184 193h2v3h-2 0v-3z" class="a"></path><path d="M191 198s1 0 2 1c0 1 0 1 1 2h1c0-1 1-1 1-2l1 1v2c-1 0-2 0-3 1s-1 1-1 2h0c-1-1-1-2-1-3-1-2-2-3-3-4h2z" class="f"></path><path d="M216 165l1-3h1l1 1v1h1c0 1 0 2 1 3v9h1v1 7h1l2-2v2-1c1 0 2 1 3 1 0 1-1 2-1 4-2 0-3 1-4 3h0c-1 3-3 4-4 6l-2 2-1 1v1c-1 0-1 1-2 2l-3-1 2-1 2-3c-1 0-2 0-3-1l-1 1c0-1-1-1-1-1-2 1-3 1-5 0-2 0-4-1-5-3-3-2-3-3-5-6l-1-3c1-1 1-3 2-4h1c0-1 1-1 1-2h1c2-1 3-2 5-3l1 1-1 1c1 0 2 0 2 2l1-1h1 1v1h0l2 2c1 0 1 0 2 1h1c0-1 0-1-1-3 0-1-1-1-2-2h0c1-2 1-2 1-4v-1h-3l-1 1c-2 0-3 1-4 1v1h-5c3-2 7-3 10-4 1-1 4-1 5-3v-1-1c1 0 2-1 2-2h0z" class="B"></path><path d="M219 163v1h1c0 1 0 2 1 3v9h1v1c-1 0-1 1-2 1v1c0 1-1 1-1 2v-18z" class="K"></path><path d="M198 188l1-1 1 1c1 1 1 2 2 3h2 5v-1c2 0 3-1 5-2 0 1 0 1 1 2l-6 3h-7c-2-1-3-3-4-5z" class="L"></path><defs><linearGradient id="Af" x1="218.452" y1="168.949" x2="206.988" y2="188.991" xlink:href="#B"><stop offset="0" stop-color="#2e2d2e"></stop><stop offset="1" stop-color="#555353"></stop></linearGradient></defs><path fill="url(#Af)" d="M214 167c1 0 2-1 2-2 0 3 1 5 0 7-1 1-1 2-1 3-1 1 0 1 0 1 1 2 1 7 0 8 0 1-1 3-1 4-2 1-3 2-5 2h-2c1-2 3-2 5-3 0-1 1-3 1-4h1c0-1 0-1-1-3 0-1-1-1-2-2h0c1-2 1-2 1-4v-1h-3l-1 1c-2 0-3 1-4 1v1h-5c3-2 7-3 10-4 1-1 4-1 5-3v-1-1z"></path><defs><linearGradient id="Ag" x1="205.598" y1="179.797" x2="206.812" y2="189.123" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#Ag)" d="M204 178c1 0 2 0 2 2l1-1h1 1v1h0l2 2c1 0 1 0 2 1 0 1-1 3-1 4-2 1-4 1-5 3h2v1h-5-2c-1-1-1-2-2-3l-1-1v-1-2-1c1-3 3-4 5-5z"></path><path d="M199 186h4v1h-1c-1 1-1 1-2 1l-1-1v-1z" class="N"></path><path d="M200 188c1 0 1 0 2-1h1v2l1 2h-2c-1-1-1-2-2-3z" class="F"></path><path d="M209 180h0l2 2c1 0 1 0 2 1 0 1-1 3-1 4-2 1-4 1-5 3h2v1c-1 0-2-1-3-1h0c0-1 1-3 2-4s2-1 2-3l-1-3zm-5-2c1 0 2 0 2 2l1-1v1c0 1 1 2 1 3s0 1-1 2l-2 1h0c-2-1-3-1-4-1h-2v-1-1c1-3 3-4 5-5z" class="R"></path><path d="M204 178c1 0 2 0 2 2 0 0 0 1-1 1-1 1-3 3-5 3l-1-1c1-3 3-4 5-5z" class="C"></path><defs><linearGradient id="Ah" x1="215.025" y1="195.469" x2="204.85" y2="177.804" xlink:href="#B"><stop offset="0" stop-color="#393838"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#Ah)" d="M199 179c2-1 3-2 5-3l1 1-1 1c-2 1-4 2-5 5v1 2 1l-1 1c1 2 2 4 4 5 3 2 5 0 7 1h0 1 0c1-1 1-1 2-1l1 1h1c3-4 4-8 5-13 0-1 1-1 1-2v-1c1 0 1-1 2-1v7h1l2-2v2-1c1 0 2 1 3 1 0 1-1 2-1 4-2 0-3 1-4 3h0c-1 3-3 4-4 6l-2 2-1 1v1c-1 0-1 1-2 2l-3-1 2-1 2-3c-1 0-2 0-3-1l-1 1c0-1-1-1-1-1-2 1-3 1-5 0-2 0-4-1-5-3-3-2-3-3-5-6l-1-3c1-1 1-3 2-4h1c0-1 1-1 1-2h1z"></path><path d="M219 194c0 1-1 1-1 2s-2 2-1 3l-1 1v1c-1 0-1 1-2 2l-3-1 2-1 2-3 4-4z" class="O"></path><path d="M199 179c2-1 3-2 5-3l1 1-1 1c-2 1-4 2-5 5v1 2 1l-1 1h-1c0-4 0-6 2-9z" class="V"></path><path d="M225 184v-1c1 0 2 1 3 1 0 1-1 2-1 4-2 0-3 1-4 3h0c-1 3-3 4-4 6l-2 2c-1-1 1-2 1-3s1-1 1-2c0-2 5-7 6-10z" class="L"></path><defs><linearGradient id="Ai" x1="146.211" y1="225.954" x2="141.24" y2="233.215" xlink:href="#B"><stop offset="0" stop-color="#403f3f"></stop><stop offset="1" stop-color="#5f5e5f"></stop></linearGradient></defs><path fill="url(#Ai)" d="M153 208h2c1-1 1-2 3-3l1 1 2 1h1l4 4 3 5h0c1 2 1 5 2 7l-1 1v3c0 2 0 3-1 4h0c-1 4-6 7-9 8h-1l-1 1h-6-1c0 1 0 1 1 2 0 1 1 1 1 2l-3-1-9-1c0-1 0-1 1-1v-2c-1 0-1 0-2-1 0-1 0-1-1-2h0l-2-2-3-3-1 1c-1-1-2-2-4-3v2-1l-2-3c1-1 0-3 0-4 1-2 0-3 0-5 0-1-1-1-1-2h1v-2c0-1 0-1 1-2v-1h2s1-1 2 0h1 1 0 2c2 0 4 3 7 3 0 1 0 4 1 5 1-3 3-5 5-7l3-3 1-1z"></path><path d="M143 226h3 1c-1 1-3 1-3 2-1 0-3 0-3-1 1 0 1-1 2-1z" class="U"></path><path d="M128 211h2c0 1 1 1 1 1l4 3c-1 1-1 3-1 4l1 1v3c0 2 0 2-2 3l-4-6v-1l-2-3v-2c0-1 0-1 1-2v-1z" class="d"></path><path d="M129 219h3c1 1 1 1 1 2l1 1c0-1 1-1 1-2v3c0 2 0 2-2 3l-4-6v-1z" class="I"></path><path d="M148 225l1 1h2c1 0 1 0 2 1h1l-3 3c-1 0-1 0-2 1 1 1 2 1 4 1l3-1h2c0 1-1 2-2 3l-1 1h-1c-1 1-2 1-4 0h-1c-4-1-7-4-10-7h5c0-1 2-1 3-2l1-1z" class="W"></path><path d="M151 226c1 0 1 0 2 1h1l-3 3-1-3h0l1-1z" class="K"></path><path d="M155 235h-1c-1 0-3-1-4-1-1-1-3-2-4-4 1-1 1-1 2-1h0v2h1c1 1 2 1 4 1l3-1h2c0 1-1 2-2 3l-1 1z" class="S"></path><path d="M130 211s1-1 2 0h1 1 0 2c2 0 4 3 7 3 0 1 0 4 1 5l-1 3c2 0 3 0 4-1l1 1c1 0 1 0 2 1l-2 2-1 1h-1-3 1l-2-2h0 0c-2-1-3-3-4-4-1 0-2-1-2-1-1-1 0-1 0-2h-1v-2h0l-4-3s-1 0-1-1z" class="J"></path><path d="M143 222s0 1 1 1h2l1 1c-1 1-2 1-3 2l-2-2h0 0l1-1v-1z" class="W"></path><path d="M135 215l3 1c2-1 3-1 4-1v1 1h-1-1c-2-1-3 0-4 0h-1v-2z" class="Q"></path><path d="M148 222c1 0 1 0 2 1l-2 2-1 1h-1-3 1c1-1 2-1 3-2v-1h0v-1h1z" class="N"></path><path d="M131 212h1 3c1 0 3 2 4 3l-1 1-3-1h0l-4-3z" class="H"></path><path d="M136 217c1 0 2-1 4 0 1 2 2 3 3 4v1 1l-1 1c-2-1-3-3-4-4-1 0-2-1-2-1-1-1 0-1 0-2z" class="d"></path><defs><linearGradient id="Aj" x1="142.254" y1="227.428" x2="140.188" y2="239.891" xlink:href="#B"><stop offset="0" stop-color="#a0a0a1"></stop><stop offset="1" stop-color="#c4c1c2"></stop></linearGradient></defs><path fill="url(#Aj)" d="M127 227c1-1 0-3 0-4 1-2 0-3 0-5 0-1-1-1-1-2h1l2 3v1l4 6c2 2 4 5 7 7 4 3 10 6 14 6l2 1 3-1-1 1h-6-1c0 1 0 1 1 2 0 1 1 1 1 2l-3-1-9-1c0-1 0-1 1-1v-2c-1 0-1 0-2-1 0-1 0-1-1-2h0l-2-2-3-3-1 1c-1-1-2-2-4-3v2-1l-2-3z"></path><path d="M139 236h1s0 1 1 1c0 1 1 1 2 1v1c1 1 2 2 3 2s2 1 2 1c1 0 1 1 2 1l-9-1c0-1 0-1 1-1v-2c-1 0-1 0-2-1 0-1 0-1-1-2z" class="E"></path><path d="M127 227c1-1 0-3 0-4 1-2 0-3 0-5 0-1-1-1-1-2h1l2 3v1 4c1 2 3 5 5 7l-1 1c-1-1-2-2-4-3v2-1l-2-3z" class="R"></path><defs><linearGradient id="Ak" x1="152.119" y1="222.28" x2="169.341" y2="228.562" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#Ak)" d="M153 208h2c1-1 1-2 3-3l1 1 2 1h1l4 4 3 5h0c1 2 1 5 2 7l-1 1v3c0 2 0 3-1 4h0c-1 4-6 7-9 8h-1l-3 1c0-1 0-1-1-2h0c0-2 3-4 4-5-1 0-2 0-3 1 1-1 2-2 2-3h-2l-3 1c-2 0-3 0-4-1 1-1 1-1 2-1l3-3h-1c-1-1-1-1-2-1h-2l-1-1 2-2c-1-1-1-1-2-1l-1-1c-1 1-2 1-4 1l1-3c1-3 3-5 5-7l3-3 1-1z"></path><defs><linearGradient id="Al" x1="164.205" y1="207.351" x2="166.459" y2="223.331" xlink:href="#B"><stop offset="0" stop-color="#2b2b2a"></stop><stop offset="1" stop-color="#525254"></stop></linearGradient></defs><path fill="url(#Al)" d="M161 207h1l4 4 3 5h0c1 2 1 5 2 7l-1 1s-1 0-2-1c0-1-1-4-1-5-1-3-2-5-4-7h-1c-1 0-1 0-2 1s-1 0-3 0c1 0 1-1 2-1h-1c1-1 0-2 1-3h1l1-1z"></path><path d="M159 211h0v-2l1-1 3 3h-1c-1 0-1 0-2 1s-1 0-3 0c1 0 1-1 2-1z" class="C"></path><defs><linearGradient id="Am" x1="152.638" y1="222.418" x2="164.478" y2="221.53" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#4b4a4b"></stop></linearGradient></defs><path fill="url(#Am)" d="M153 208h2c1-1 1-2 3-3l1 1 2 1-1 1h-1c-1 1 0 2-1 3h1c-1 0-1 1-2 1h-1c1 0 2 1 3 2s2 2 3 4c1 1 3 3 3 4 1 2 0 4 0 6-1 2-3 4-5 5h-1c-1 0-2 0-3 1 1-1 2-2 2-3h-2l2-1c2 0 3-2 4-3l1-2c0-1 0-1-1-2-1-3-3-5-5-6h-1l-1-1c-2-1-4-1-6 0v-1-3l3-3 1-1z"></path><path d="M153 208h2c1-1 1-2 3-3l1 1 2 1-1 1h-1c-1 1 0 2-1 3-1 0-2 0-2-1-2 1-3 3-5 4h0l-2 1v-3l3-3 1-1z" class="k"></path><path d="M149 212v3 1c2-1 4-1 6 0l1 1h1c2 1 4 3 5 6 1 1 1 1 1 2l-1 2c-1 1-2 3-4 3l-2 1-3 1c-2 0-3 0-4-1 1-1 1-1 2-1l3-3h-1c-1-1-1-1-2-1h-2l-1-1 2-2c-1-1-1-1-2-1l-1-1c-1 1-2 1-4 1l1-3c1-3 3-5 5-7z" class="B"></path><path d="M161 225h2l-1 2-1-1c0 1-1 1-1 1-1 1-1 2-3 2h0c0-1 1-2 1-3 2 0 2 0 3-1z" class="Z"></path><path d="M155 216l1 1h1c2 1 4 3 5 6 1 1 1 1 1 2h-2c-1 1-1 1-3 1 0-1 0-2-1-3 0-1 0-2-1-2v3c0-2-1-2-2-4l-1-1c0-1 1-2 2-3z" class="H"></path><path d="M157 223h2c1 0 1 0 1-1l1 1-1 1c1 0 1 0 1 1-1 1-1 1-3 1 0-1 0-2-1-3z" class="F"></path><path d="M155 216l1 1h1l-1 1 1 2h-1-1-1l-1-1c0-1 1-2 2-3z" class="U"></path><path d="M149 212v3 1c2-1 4-1 6 0-1 1-2 2-2 3l1 1c1 2 2 2 2 4-1 1-1 2-2 3h-1c-1-1-1-1-2-1h-2l-1-1 2-2c-1-1-1-1-2-1l-1-1c-1 1-2 1-4 1l1-3c1-3 3-5 5-7z" class="C"></path><path d="M149 216c2-1 4-1 6 0-1 1-2 2-2 3h0c-2-1-3-2-4-3h0z" class="F"></path><path d="M153 219h0l1 1c1 2 2 2 2 4-1 1-1 2-2 3h-1c-1-1-1-1-2-1 1-1 0-3 1-5 0 0 0-1 1-2z" class="W"></path><path d="M500 248h1c6-3 11-3 17-1h2c1 1 2 1 4 2l6 3c0 1 0 1-1 2h0c2 1 5 3 6 5 0 1 0 2 1 3s1 0 1 1l-1 1c4 1 8 3 12 4l3 1 2 1c1 1 3 2 5 2h1c0 1-1 1-1 2 1 1 1 2 2 2l3-3c0 1 1 1 1 2h-1l-1 1c0 1 2 2 2 2v2c1 1 2 1 2 2l-2-1c0 1 0 2 1 3s2 1 3 1 2 1 3 2h1c1 1 2 1 3 1h2c2-1 4-1 6-2v1l3 3v1h1l1-1v-1h0c0 1 1 1 1 1 1 1 1 3 2 4l1 2h1v3h-1v2c-2-1-5 0-8-1h-2 0-1c-2 0-4 0-6-1h-1-1-3-8-1v2h-2c-1 0-1 0-2 1 1 0 2 1 3 1l-2 1v1c-1-1-1-1-2-1v1h-1-5-3c-2 0-3 0-5 1-2 0-3 1-4 2l-5 3c1-1 1-2 1-3l-1-1c-1 0-2 1-3 1l1 1h-2c-1 0-2 1-2 2l-6 5c-2 2-4 3-6 6 0 1 0 1-1 2h-2 0l-1-1-3-9c-1-3-3-8-3-11v-1h-2c-1 0-2 0-4 1s-4 3-7 3c-1 1-1 0-1 0 0-2 4-6 5-8 1-1 3-3 4-5 0-1 0-1-1-2l-1 2v-1l-5-8-3-6-1-3-3-3c-1-2-1-5-3-6l5-4 2-2c1-1 2-3 3-4h0l3-3c1-2 2-3 4-5z" class="D"></path><path d="M520 297v-1-1c2-1 2-3 4-5 1-1 1-2 2-4 1 1 2 2 3 2 2 1 3 2 3 4 1 0 1 1 1 1h0c0 1-1 1-1 2l-1 2h-6-2c-1-1-2 0-3 0z" class="O"></path><path d="M523 297c0-2 1-3 3-4h1 0c-1 2-1 3-2 4h-2z" class="L"></path><path d="M533 293c3 0 4 0 7 1h5v1c2-1 5-1 7-1h2 1c1 0 1 0 2-1h0c1 0 1 0 2-1h2c-1 2 0 3 0 5h-30l1-2c0-1 1-1 1-2z" class="B"></path><path d="M520 297c1 0 2-1 3 0h2 6 30 7 4 8c4 0 8 0 12 2v2c-2-1-5 0-8-1h-2 0-1c-2 0-4 0-6-1h-1-1-3-8-1v2h-2c-2-1-4-1-6-1-6-1-10-1-16 0-1 0-1-1-3 0h0c-4 1-8-1-11-1-6-1-13 1-18 4v-1h-2l1-1c1 0 2 0 2-1 5-1 9-2 13-3h1z" class="C"></path><path d="M565 284c1 1 2 1 3 1s2 1 3 2h1c1 1 2 1 3 1h2c2-1 4-1 6-2v1l3 3v1h1l1-1v-1h0c0 1 1 1 1 1 1 1 1 3 2 4l1 2h1v3h-1c-4-2-8-2-12-2h-8-4-7c0-2-1-3 0-5 1-1 1-2 1-3h0c0-1 1-1 1-2v-2h0 2v1-2z" class="F"></path><path d="M574 290h1l1 1s-1 0-1 1h-1l-3-2h3z" class="c"></path><path d="M583 287l3 3v1c-2-1-4-1-6-1 1-1 1-1 2-1l1-1v-1z" class="M"></path><path d="M583 286v1 1l-1 1c-1 0-1 0-2 1-1-1-2-2-3-2 2-1 4-1 6-2z" class="E"></path><defs><linearGradient id="An" x1="569.324" y1="297.39" x2="562.346" y2="285.035" xlink:href="#B"><stop offset="0" stop-color="#828081"></stop><stop offset="1" stop-color="#a2a1a1"></stop></linearGradient></defs><path fill="url(#An)" d="M565 284c1 1 2 1 3 1s2 1 3 2h1c1 1 2 1 3 1h2c-1 1-1 1-3 2h0-3l3 2v2 1h4c-2 0-5 0-7 2h-3 0-7c0-2-1-3 0-5 1-1 1-2 1-3h0c0-1 1-1 1-2v-2h0 2v1-2z"></path><path d="M571 290v2c-1 1-1 1-2 1l-1-1h0c1 0 1 0 2 1v-2l-1-2h-2v-3c1 0 1 1 2 1h2 1c1 1 2 1 3 1h2c-1 1-1 1-3 2h0-3z" class="E"></path><defs><linearGradient id="Ao" x1="539.161" y1="272.975" x2="544.897" y2="294.42" xlink:href="#B"><stop offset="0" stop-color="#3d3c3c"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#Ao)" d="M525 282c1-3 2-4 4-6 4-2 8-4 12-3s7 2 9 5c2 2 2 4 3 6 0 1 1 2 1 2l2 1h1c1 0 2 1 2 1 1 1 1 1 1 2s-1 1-1 2c-6 2-16 0-22-2-4-1-9-4-12-8z"></path><path d="M544 289c-1 0-3-1-4-1 0 0-1 0-1-1-1 0-2-1-3-1-2-2-5-3-6-5l2-2c0 2 0 2 1 3l1 1c3 2 3 4 7 4h0c1 0 2 1 3 2z" class="J"></path><path d="M533 279c1-2 2-3 3-3 3-1 6 0 8 1h1c1 0 2 1 4 2 0 3 1 4 1 7-1 1-2 1-4 1h-3v1c1 1 1 1 3 1h1c-1 1-2 0-3 0-1-1-2-2-3-2h0c-4 0-4-2-7-4l-1-1c-1-1-1-1-1-3h1z" class="b"></path><path d="M535 279c1-1 1-1 2-1h2c1 0 5-1 6 0s2 3 3 4l1 3c-1 1-2 0-3 1h-3c-2-1-1-3-3-5-1 0-2 0-3 1h-2c-1 0-2-1-2-2v-1h2z" class="c"></path><path d="M535 279c1 0 2 0 3 1v1h0l-2 1c-1-1-2-1-2-2l1-1z" class="F"></path><defs><linearGradient id="Ap" x1="548.286" y1="282.596" x2="540.349" y2="280.772" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#Ap)" d="M546 286c-2-1-3-1-4-3 0 0-2-1-2-3l1-1s1 0 2-1c1 1 2 2 3 2l2 2 1 3c-1 1-2 0-3 1z"></path><defs><linearGradient id="Aq" x1="526.641" y1="314.259" x2="505.682" y2="302.792" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#Aq)" d="M505 303c5-3 12-5 18-4 3 0 7 2 11 1h0c2-1 2 0 3 0 6-1 10-1 16 0 2 0 4 0 6 1-1 0-1 0-2 1 1 0 2 1 3 1l-2 1v1c-1-1-1-1-2-1v1h-1-5-3c-2 0-3 0-5 1-2 0-3 1-4 2l-5 3c1-1 1-2 1-3l-1-1c-1 0-2 1-3 1l1 1h-2c-1 0-2 1-2 2l-6 5c-2 2-4 3-6 6 0 1 0 1-1 2h-2 0l-1-1-3-9c-1-3-3-8-3-11z"></path><path d="M510 305c1 0 1 0 2 1s1 1 1 3l1 1c0 1-1 1-2 1h-1l1-1h-1c-1 0-2-2-2-2v-3h1z" class="U"></path><path d="M537 300c6-1 10-1 16 0 2 0 4 0 6 1-1 0-1 0-2 1 1 0 2 1 3 1l-2 1v1c-1-1-1-1-2-1v1h-1-5-3c-2 0-3 0-5 1-2 0-3 1-4 2l-5 3c1-1 1-2 1-3l-1-1c-1 0-2 1-3 1l1 1h-2c-1 0-2 1-2 2l-6 5c-2 2-4 3-6 6 0 1 0 1-1 2-1-2-1-4 0-6 5-10 13-14 23-17v-1z" class="O"></path><path d="M537 300c6-1 10-1 16 0 2 0 4 0 6 1-1 0-1 0-2 1 1 0 2 1 3 1l-2 1v1c-1-1-1-1-2-1v1h-1-5-3c-2 0-3 0-5 1-2 0-3 1-4 2 0-1 1-2 2-2h0v-1l-1-1h-1c1-1 1-1 2 0v-1h0c1 0 1-1 2-1h1 2c0-1 0-1-1-1-2 0-5 1-7 2h0-1l1-1c1 0 2 0 3-1h-1-2v-1z" class="f"></path><path d="M542 306c2-2 4-2 6-3h0c1 0 2 1 3 1s1 0 2-1v-1c1-1 3 0 4 0s2 1 3 1l-2 1v1c-1-1-1-1-2-1v1h-1-5-3c-2 0-3 0-5 1z" class="M"></path><defs><linearGradient id="Ar" x1="553.9" y1="302.556" x2="506.146" y2="278.699" xlink:href="#B"><stop offset="0" stop-color="#979696"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#Ar)" d="M535 263l1 1c4 1 8 3 12 4l3 1 2 1c1 1 3 2 5 2h1c0 1-1 1-1 2 1 1 1 2 2 2l3-3c0 1 1 1 1 2h-1l-1 1c0 1 2 2 2 2v2c1 1 2 1 2 2l-2-1c0 1 0 2 1 3v2-1h-2 0v2c0 1-1 1-1 2h0l-2-3-1 1h-1-1-1l-2-1s-1-1-1-2c-1-2-1-4-3-6-2-3-5-4-9-5s-8 1-12 3c-2 2-3 3-4 6v-1-1 3h0l1 1c-1 1-1 2-1 2 0 3-2 4-3 6-1 1-1 2-2 2l-1 1v1h-1l-1 1h1 1c-4 1-8 2-13 3 0 1-1 1-2 1l-1 1c-1 0-2 0-4 1s-4 3-7 3c-1 1-1 0-1 0 0-2 4-6 5-8 1-1 3-3 4-5 0-1 0-1-1-2 0 0 4-4 4-5 1-1 1-1 2-1l1 1 1-1 8-7 3-2c5-3 12-6 18-6h7c-1 0-2 0-2-1l-4-2c-2-1-2-2-2-4z"></path><path d="M507 285l1 1h1 0c0 1-1 2-2 3-1-1 0-2-1-2-1 1-4 4-4 5v1h-2c1-3 4-5 6-7l1-1z" class="G"></path><path d="M503 286c1-1 1-1 2-1l1 1c-2 2-5 4-6 7h0c0-1 0-1-1-2 0 0 4-4 4-5z" class="Q"></path><path d="M558 276c2 1 4 3 6 5h0c0 1 0 2 1 3h-2c-1 0-1 0-2-1s-3-2-3-2v-3-1-1z" class="H"></path><path d="M504 298l-2 1h-1c2-3 7-9 10-9h1c0 2-1 3-2 5l-6 3z" class="W"></path><path d="M502 293c1-2 2-2 3-3l1-1h-1l2 1c-1 0-1 0-1 1-2 0-3 1-4 2s-2 1-3 2c-1 2-2 4-4 6 2 0 2-2 4-1 0 1-1 2-1 3 0-1 1-2 2-2h2l1-1c1-1 1-1 1-2l6-3h1c0 1-1 2-1 3h-2l-1 1c-1 0-1 1-1 1 0 1-1 1-2 1l-1 1c-1 0-2 0-4 1s-4 3-7 3c-1 1-1 0-1 0 0-2 4-6 5-8 1-1 3-3 4-5h0 2z" class="L"></path><path d="M535 263l1 1c4 1 8 3 12 4l3 1 2 1c1 1 3 2 5 2h1c0 1-1 1-1 2 1 1 1 2 2 2l3-3c0 1 1 1 1 2h-1l-1 1c0 1 2 2 2 2v2c1 1 2 1 2 2l-2-1h0c-2-2-4-4-6-5l-6-3-9-3c-1 0-2 0-2-1l-4-2c-2-1-2-2-2-4z" class="W"></path><path d="M500 248h1c6-3 11-3 17-1h2c1 1 2 1 4 2l6 3c0 1 0 1-1 2h0c2 1 5 3 6 5 0 1 0 2 1 3s1 0 1 1l-1 1-1-1c0 2 0 3 2 4l4 2c0 1 1 1 2 1h-7c-6 0-13 3-18 6l-3 2-8 7-1 1-1-1c-1 0-1 0-2 1 0 1-4 5-4 5l-1 2v-1l-5-8-3-6-1-3-3-3c-1-2-1-5-3-6l5-4 2-2c1-1 2-3 3-4h0l3-3c1-2 2-3 4-5z" class="J"></path><path d="M515 278s-1-1-1-2c0 0 0-1 1-1s2 1 3 1l-3 2z" class="T"></path><path d="M514 270c1 0 3 0 4-1 1 1 1 1 1 2l-2 2h0v-1c-1 0-1 0-1-1h0l-1 1c-1 0-2 1-2 1h-1c1-1 1-2 2-3z" class="N"></path><path d="M502 270c1 1 1 1 2 0 0 0 1 1 2 0h1 3 4c-1 1-1 2-2 3h1s1-1 2-1l-2 2-1-1v-1h0v-1c-1 0-3 0-4-1h0v1 1h1-1 0c-2 0-2-1-4 0-1 0-2 1-3 2h-1v-1-1h1c0-1 0-1 1-2z" class="X"></path><path d="M517 261h3v2c0 1-1 2-3 3-2 2-3 2-5 2h-2l-3-1v-1h1c2 0 2-1 4-2 1 0 2 0 3-1h0l2-2z" class="H"></path><path d="M517 261h3v2h-2-3l2-2z" class="D"></path><path d="M515 249h4c1 0 2 2 2 2l-2-1c-3-1-5 3-8 1-2 0-2 1-3 2v2l3 3v1s1 1 2 1h2 0l-1 1v1h1 1l1-1h0l-2 2h0c-1 1-2 1-3 1-2 1-2 2-4 2h-1v1l3 1h-2-1l-1-1-1-1s0-1-1-1v-1c-1 1-1 2-1 2 1 1 2 1 2 2 0 0 1 0 0 1h1l1-1c1 1 1 1 2 1h0v1l-2-1h0c0 1-1 1-1 1h-1v-1l-1-1-2 2c-1 1-1 1-1 2h-2-1c1-1 1-1 1-2 1-1 2-2 3-4h0l1-3c0-1 1-2 2-2v-1h-2v-7h1c-1-1-1-1-2-1h1c0-1 1-2 3-2l1-1h8z" class="P"></path><path d="M513 260c0 1 0 1 1 2l-1 1h-1c-1-1-2-2-2-4h1s1 1 2 1z" class="I"></path><path d="M503 253l2 1v2c0 1 0 2 1 2l2 4-2 2-3-1c0-1 1-2 2-2v-1h-2v-7z" class="Z"></path><path d="M500 274h1c1-1 2-2 3-2 2-1 2 0 4 0-1 1-2 1-2 2-1 1-1 2-1 4 1 0 1 1 2 1h0c1 0 1 0 2 1v1c-1 0-2 1-3 1 0 0-1 2-1 3-1 0-1 0-2 1 0 1-4 5-4 5l-1 2v-1l-5-8c1-2 1-4 2-5v-1c1 0 1-1 1-1 0-2 1-3 2-4h2v1z" class="T"></path><path d="M495 279h0v4c0 1 0 1 1 2 0 0 1 1 1 2 1 0 2 0 3 1 1-1 2-2 3-2 0 1-4 5-4 5l-1 2v-1l-5-8c1-2 1-4 2-5z" class="J"></path><path d="M500 274h1c1-1 2-2 3-2 2-1 2 0 4 0-1 1-2 1-2 2-2 0-3 0-4 1h0c1 1 1 2 2 3 1 0 1 0 2 1h0c-1 1-2 1-2 2-2 2-3 3-5 4h-1c0-2 0-2-1-3v-4l2-2v-2h1z" class="F"></path><path d="M511 259v-1l-3-3v-2c1-1 1-2 3-2 3 2 5-2 8-1l2 1c3 0 6 2 8 3h0c2 1 5 3 6 5 0 1 0 2 1 3s1 0 1 1l-1 1-1-1-1 2h-3c-2-2-4-5-6-6-1 0-2-2-4-3-1 0-2 1-2 1 0 1-1 1-1 2h-3v1h-2c-1 0-2-1-2-1z" class="Z"></path><path d="M517 253h1l1 1v1c-2 0-3 3-5 4h-2v-1c1-2 3-4 5-5z" class="E"></path><path d="M500 248h1c6-3 11-3 17-1h2c1 1 2 1 4 2l6 3c0 1 0 1-1 2-2-1-5-3-8-3 0 0-1-2-2-2h-4-8l-1 1c-2 0-3 1-3 2h-1c1 0 1 0 2 1h-1v7h2v1c-1 0-2 1-2 2l-1 3h0c-1 2-2 3-3 4 0 1 0 1-1 2h1 2-1v1h-2c-1 1-2 2-2 4 0 0 0 1-1 1v1c-1 1-1 3-2 5l-3-6-1-3-3-3c-1-2-1-5-3-6l5-4 2-2c1-1 2-3 3-4h0l3-3c1-2 2-3 4-5z" class="d"></path><path d="M492 271l1 2v-4c1 1 1 2 1 4-1 0-1 0 0 1h0c0 1 0 1 1 3v1 1c-1 1-1 3-2 5l-3-6c2-2 2-5 2-7z" class="C"></path><path d="M494 274c1-4 1-7 2-10 2-4 3-8 4-12 1-1 3-3 5-3 3-1 7-1 10 0h-8l-1 1c-2 0-3 1-3 2h-1c-1 3-1 6-1 9h-1c-2 1-2 3-3 4v2l-2 5v3 2c-1-2-1-2-1-3z" class="I"></path><path d="M502 252c1 0 1 0 2 1h-1v7h2v1c-1 0-2 1-2 2l-1 3h0c-1 2-2 3-3 4 0 1 0 1-1 2h1 2-1v1h-2c-1 1-2 2-2 4 0 0 0 1-1 1v-1-2-3l2-5v-2c1-1 1-3 3-4h1c0-3 0-6 1-9z" class="K"></path><path d="M503 260h2v1c-1 0-2 1-2 2l-1 3h0c-1 2-2 3-3 4 0 1 0 1-1 2h1 2-1v1h-2v-1c0-2 1-3 1-4 1-2 1-4 3-5 0-1 0-1 1-1v-2z" class="e"></path><path d="M493 256h0l3-3-1 5-3 13c0 2 0 5-2 7l-1-3-3-3c-1-2-1-5-3-6l5-4 2-2c1-1 2-3 3-4z" class="F"></path><path d="M490 260h1v4c-2 0-2-1-3-2l2-2z" class="W"></path><path d="M493 256h0l3-3-1 5c-1 1-3 5-4 5v-2-1h-1c1-1 2-3 3-4z" class="E"></path><defs><linearGradient id="As" x1="485.493" y1="264.213" x2="490.635" y2="271.117" xlink:href="#B"><stop offset="0" stop-color="#646263"></stop><stop offset="1" stop-color="#7d7e7c"></stop></linearGradient></defs><path fill="url(#As)" d="M488 262c1 1 1 2 3 2l-1 1c0 1 0 3-1 4v6l-3-3c-1-2-1-5-3-6l5-4z"></path><path d="M180 338v-1h2l1 2 3 4h1 1l-2 13c0 3 0 6-1 9v22c1 3 1 6 1 9l4 21c0 2 0 5 2 7l15 47-1 1c0 1 1 1 0 3-1-1-1-1-2-1l-1 2-1-1v-1h-1v-2h1v-1c-1-1-2-1-2-2s-1-3-2-4c-2-2-3-3-4-6-1-1-2-2-3-2v1l-1-2c-1-1-2-1-3-1-2-1-3-2-5-2-6-2-14-1-20 2-1 1-2 2-3 2s-1 1-2 1l1 2-1 1-5-8-3-4 1-1c-1-2-5-5-7-7l-6-5-2-2c-2-1-4-3-6-4-1 0-2-1-2-1-4-2-7-5-11-6l-2-1h3 4l2 1h0l1-1 1-2c0 2 2 3 3 4h1l2-1 3-3 4-5v-1l-2-2h1l1-2c-3 0-6 1-8 2-3 1-7-1-9 1l-2-1-1 1h-1v1s-1 0-1-1h-2-1-1c1-1 1-2 2-3 1 0 1 0 2 1h1c3-1 6 0 9-1 8-1 17-5 23-11h0c5-4 8-9 11-14l2-2c5-4 9-11 12-16 0-1 1-2 2-2l-1 1h1v-1c0-1 0-1 1-2s1-4 1-6c0-3 1-8 3-11h1l-1-3-1-5z" class="C"></path><path d="M168 388a30.44 30.44 0 0 1 8 8h-3c-1 0-1-1-1-1l1-1c-1 0-1 0-1-1-1-1-2-1-3-1-1-1-2-1-3-2l2-2z" class="M"></path><path d="M190 454l-1-1s-1 0-1-1c1 0 1 0 1 1h1c5 2 11 9 12 14l1 2h-1l-2-4c-1-1-3-4-4-4h0l-2-2c-1-1-2-2-3-2l-1-3z" class="H"></path><path d="M194 459l2 2h0c1 0 3 3 4 4l2 4h1l-1-2h2c0 2 2 4 1 6l-1 1-1 2-1-1v-1h-1v-2h1v-1c-1-1-2-1-2-2s-1-3-2-4c-2-2-3-3-4-6z" class="I"></path><path d="M201 474v-2h1l1 2h1l-1 2-1-1v-1h-1z" class="J"></path><path d="M185 423h1c1 1 2 2 1 4v3c-2 3-4 5-7 7h-1-1v-1c1-1 2-2 2-3l5-10z" class="K"></path><path d="M185 428h1l1 2-6 6h-1 0-1l1-1c1-1 1-1 1-2 1 0 2-1 2-2 1-1 1-3 2-3z" class="Q"></path><path d="M180 404c2 3 2 8 2 12 0 1 0 4-2 5h0c0-1 0-1-1-1h-1v1l2 1v1l-2 1v-3h-2c1-2 0-8 0-11h1v-1c0-1 0-1 1-2h0 1c0-1 1-2 1-3z" class="T"></path><path d="M179 407h1v3 2c-1 0-1 1-2 1 0-1 0-2-1-3v-1c0-1 0-1 1-2h0 1z" class="E"></path><path d="M179 407h1v3c-1-1-1-1-2-1v-2h0 1z" class="P"></path><path d="M178 415h1s1-1 2-1c0-1 0-1 1-2v4c0 1 0 4-2 5h0c0-1 0-1-1-1h-1c0-1 1-2 2-3l-2-2z" class="E"></path><path d="M176 410h1c1 1 1 2 1 3v2l2 2c-1 1-2 2-2 3v1l2 1v1l-2 1v-3h-2c1-2 0-8 0-11z" class="f"></path><path d="M168 448c2-1 4-2 5-3 5-4 9-8 14-12v1c1 0 1 1 1 1 1 2-3 4-4 6v1c-1 2-4 3-6 4-1 0-2-1-3 0l-1 1-3 3c-1 0-2 0-3-1v-1z" class="V"></path><path d="M204 467c-2-4-3-8-5-12l-1-4v-1l-4-13c-1-3-1-7-2-9l-1 1c0 1-1 2-1 2l-1 2h-1 0c-1-1 1-3 1-5 1-1 1-3 2-4h1l15 47-1 1c0 1 1 1 0 3-1-1-1-1-2-1l1-1c1-2-1-4-1-6z" class="b"></path><defs><linearGradient id="At" x1="174.935" y1="455.605" x2="176.49" y2="448.872" xlink:href="#B"><stop offset="0" stop-color="#757575"></stop><stop offset="1" stop-color="#989898"></stop></linearGradient></defs><path fill="url(#At)" d="M175 446c1-1 2 0 3 0 4 3 9 4 12 7h-1c0-1 0-1-1-1 0 1 1 1 1 1l1 1 1 3v1l-1-2c-1-1-2-1-3-1-2-1-3-2-5-2-6-2-14-1-20 2v-1-1-1c2 0 5-2 6-4v1c1 1 2 1 3 1l3-3 1-1z"></path><path d="M175 446c1-1 2 0 3 0 4 3 9 4 12 7h-1c0-1 0-1-1-1 0 1 1 1 1 1l1 1c-2-1-3-2-5-2l-1-1c-1-1-2 0-4 0 0-1-2-2-3-3l-1-1-1-1z" class="B"></path><path d="M170 420c2 2 2 5 3 7 2-2 3-4 3-6h2v3l2-1c0 2-2 5-3 7-2 5-3 10-7 14h-2l3-5c-1-1-1-1-2-1 0-2 1-4 1-6 0-3-2-5-2-8 1-1 0-1 1-2 0-1 0-1 1-2z" class="T"></path><path d="M172 430h2l1-2-2 6h-1v-4z" class="G"></path><path d="M176 421h2v3c-2 1-2 3-3 4l-1 2h-2v-3h1c2-2 3-4 3-6z" class="V"></path><path d="M170 420c2 2 2 5 3 7h-1v3 4h1c-1 2-1 4-2 5h0c-1-1-1-1-2-1 0-2 1-4 1-6 0-3-2-5-2-8 1-1 0-1 1-2 0-1 0-1 1-2z" class="M"></path><path d="M148 414c4-5 8-8 11-13 1-2 3-6 5-6l1-1v-2c0-1 0-1 1-2h0c1 1 2 1 3 2 1 0 2 0 3 1 0 1 0 1 1 1l-1 1s0 1 1 1h3c1 3 3 5 4 8 0 1-1 2-1 3h-1 0c-1 1-1 1-1 2v1h-1c0 3 1 9 0 11 0 2-1 4-3 6-1-2-1-5-3-7-1 1-1 1-1 2-1 1 0 1-1 2-1-2-2-3-4-4h0c-2-1-5-1-8-1l-3 1-4 3c-1 0-1 0-2 1l1-2h-1-1v1l-1 1v-1l3-6h-1c0-2 0-2 1-3z" class="W"></path><path d="M161 413c1 0 2 0 3 1 1 0 1 1 1 1l3 1h-3l-4-1v-2z" class="K"></path><path d="M169 395h3s0 1 1 1l1 3-1 1c-1-2-2-4-4-5z" class="U"></path><path d="M154 412c2-1 3-3 5-4l1 1c-1 2-2 3-3 4l-3-1z" class="O"></path><path d="M166 390h0c1 1 2 1 3 2 1 0 2 0 3 1 0 1 0 1 1 1l-1 1h-3c-1-2-3-3-3-5z" class="V"></path><path d="M169 416c1-1 2-1 2-2h1 1l-1 3-1 1v1h-2l-1-1c-1-1-1-1-3-2h3 1z" class="J"></path><path d="M168 418c1 0 1-1 3-1v1 1h-2l-1-1z" class="C"></path><path d="M149 416c1-2 3-3 5-4l3 1c-1 2-3 3-5 4h-1c-1 1-1 1-2 0v-1z" class="L"></path><path d="M160 409l1 1c0 1 0 1-1 2h0l1 1v2h-2c-2 1-4 2-6 2h-1c2-1 4-2 5-4 1-1 2-2 3-4z" class="K"></path><path d="M159 415v-2l1-1 1 1v2h-2z" class="P"></path><path d="M174 399l2 2h0l1 1 1 4v1h0c-1 1-1 1-1 2v1h-1l-1-3c0-3-1-5-2-7l1-1z" class="M"></path><path d="M173 396h3c1 3 3 5 4 8 0 1-1 2-1 3h-1v-1l-1-4-1-1h0l-2-2-1-3z" class="B"></path><path d="M166 395l1 1-1 2c1 2 1 3 2 5v1c-1 1-2 1-3 1 0 0 0-1-1-1 0-1-2-2-3-2h-1v-1l1-1 2-2c2-1 2-2 3-3z" class="Q"></path><path d="M166 395l1 1-1 2c0 2 0 2-1 3h-4v-1l2-2c2-1 2-2 3-3z" class="J"></path><path d="M166 395h1v1c1 1 3 2 4 3 0 1 1 3 1 4s0 2 1 3v8h-1-1c0 1-1 1-2 2 1-4 3-8 3-12 0-1-2-2-3-2h0v2c0 2 0 2-1 3h0v-1c1-1 1-1 0-2v-1c-1-2-1-3-2-5l1-2-1-1z" class="I"></path><path d="M161 415l4 1c2 1 2 1 3 2l1 1 1 1c-1 1-1 1-1 2-1 1 0 1-1 2-1-2-2-3-4-4h0c-2-1-5-1-8-1l-3 1-4 3c-1 0-1 0-2 1l1-2h-1-1v1l-1 1v-1l3-6 1-1v1c1 1 1 1 2 0h1 1c2 0 4-1 6-2h2z" class="O"></path><path d="M156 419c3 0 6 0 8 1h0c2 1 3 2 4 4 0 3 2 5 2 8 0 2-1 4-1 6 1 0 1 0 2 1l-3 5c-4 4-8 7-13 8l-1 1 3 5 1 2-1 1-5-8-3-4 1-1c-1-2-5-5-7-7l1-2 2 1c-2-2-3-4-4-7v-4l-1-1-2 1h0v-1l1-1c0-1 0-1 1-2 0 0 1 0 2-1h0l2-1v1l1-1v-1h1 1l-1 2c1-1 1-1 2-1l4-3 3-1z" class="C"></path><path d="M153 420l3-1-1 3h-1v-1c0-1-1-1-1-1z" class="W"></path><path d="M164 420c2 1 3 2 4 4 0 3 2 5 2 8l-1-1c0-2-1-3-2-4h0l-1 1-1-1c0-2-2-3-3-4 1 1 1 1 2 1v1c1 0 2 0 3-1v-1h-1-1l1-1c-1 0-1 0-2-1v-1z" class="I"></path><path d="M156 423c1 0 2 0 3 1l-1 1-1 1c-2 1-3 2-3 4-1 0-1 1-1 2h-1 0c-1-1-1-2-1-4 1-2 3-3 5-5z" class="E"></path><path d="M156 423h5 1c1 1 3 2 3 4l-1 1v4 4c-1-1-1-2-1-3v-4l-1-1-1-1-1-1h-3l1-1 1-1c-1-1-2-1-3-1z" class="Z"></path><path d="M156 423h5 1c1 1 3 2 3 4l-1 1c-1-1-1-2-2-3h-4 0l1-1c-1-1-2-1-3-1z" class="T"></path><path d="M157 426h3l1 1-2 1 1 1-1 2c-1 1-2 1-3 1s-1-1-2-1h0v1s1 1 2 1l1 1h-2c-1 0-2 0-3-2h1c0-1 0-2 1-2 0-2 1-3 3-4z" class="X"></path><path d="M157 426h3l1 1-2 1h0c-1 1-2 1-3 2h-2c0-2 1-3 3-4z" class="C"></path><path d="M159 428l2-1 1 1 1 1v4l-1 2c0 1 0 1-1 1h-3v-2h-1l-1-1c-1 0-2-1-2-1v-1h0c1 0 1 1 2 1s2 0 3-1l1-2-1-1z" class="I"></path><defs><linearGradient id="Au" x1="154.469" y1="437.523" x2="158.011" y2="441.167" xlink:href="#B"><stop offset="0" stop-color="#afabab"></stop><stop offset="1" stop-color="#cacacc"></stop></linearGradient></defs><path fill="url(#Au)" d="M163 433c0 1 0 2 1 3-1 1-2 3-2 3-1 1-4 2-5 3h-7c1-2 1-2 2-2l-2-1c2 0 3 0 4-1 0 0 1-1 2-1h2v-1h3c1 0 1 0 1-1l1-2z"></path><path d="M158 436h3c-2 3-4 4-8 4h-1l-2-1c2 0 3 0 4-1 0 0 1-1 2-1h2v-1z" class="Q"></path><path d="M145 424l1-1v-1h1 1l-1 2c1-1 1-1 2-1-2 5-3 8-1 13 0 1 1 2 2 3h0l2 1c-1 0-1 0-2 2-1-1-3-1-4-2-2-2-3-4-4-7v-4l-1-1-2 1h0v-1l1-1c0-1 0-1 1-2 0 0 1 0 2-1h0l2-1v1z" class="f"></path><path d="M145 424l1-1v-1h1 1l-1 2h0c-2 3-2 5-2 9v3c-1-2-3-5-2-8 0-1 1-3 2-4z" class="V"></path><defs><linearGradient id="Av" x1="154.539" y1="432.751" x2="162.235" y2="444.376" xlink:href="#B"><stop offset="0" stop-color="#282727"></stop><stop offset="1" stop-color="#474647"></stop></linearGradient></defs><path fill="url(#Av)" d="M166 428l1-1h0c1 1 2 2 2 4l1 1c0 2-1 4-1 6 1 0 1 0 2 1l-3 5c-4 4-8 7-13 8l-1 1 3 5 1 2-1 1-5-8-3-4 1-1c-1-2-5-5-7-7l1-2 2 1c1 1 3 1 4 2h7c1-1 4-2 5-3 0 0 1-2 2-3v-4-4l1-1 1 1z"></path><path d="M165 427l1 1c0 1 1 3 0 4h-2v-4l1-1z" class="R"></path><path d="M169 438c1 0 1 0 2 1l-3 5c-4 4-8 7-13 8l-1 1 3 5 1 2-1 1-5-8-3-4 1-1 3 4h0c1-1 3-2 4-3 5-3 9-6 12-11z" class="D"></path><defs><linearGradient id="Aw" x1="135.685" y1="401.893" x2="192.053" y2="347.342" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Aw)" d="M180 338v-1h2l1 2 3 4h1 1l-2 13c0 3 0 6-1 9v22 4-3h-5c-1 2-2 3-3 4-1 0-2-1-3-1 0-2-1-3-1-6 1 0 1 0 2-1h0l2-2h-1c-1-1-1-1-1-3 0 1-1 1-1 2-2 2-4 4-5 7h-1l-2 2h0c-1 1-1 1-1 2v2l-1 1c-2 0-4 4-5 6-3 5-7 8-11 13-1 1-1 1-1 3h1l-3 6-2 1h0c-1 1-2 1-2 1-1 1-1 1-1 2l-1 1v1h0l2-1 1 1v4c1 3 2 5 4 7l-2-1-1 2-6-5-2-2c-2-1-4-3-6-4-1 0-2-1-2-1-4-2-7-5-11-6l-2-1h3 4l2 1h0l1-1 1-2c0 2 2 3 3 4h1l2-1 3-3 4-5v-1l-2-2h1l1-2c-3 0-6 1-8 2-3 1-7-1-9 1l-2-1-1 1h-1v1s-1 0-1-1h-2-1-1c1-1 1-2 2-3 1 0 1 0 2 1h1c3-1 6 0 9-1 8-1 17-5 23-11h0c5-4 8-9 11-14l2-2c5-4 9-11 12-16 0-1 1-2 2-2l-1 1h1v-1c0-1 0-1 1-2s1-4 1-6c0-3 1-8 3-11h1l-1-3-1-5z"></path><path d="M181 343c1 4 1 8 0 12-1 1-1 1-3 2 0-3 1-8 3-11h1l-1-3z" class="B"></path><path d="M181 355c0 4-1 8-2 12-3 7-8 14-13 19v-3c4-5 8-11 10-17v-1c0-1 0-1 1-2s1-4 1-6c2-1 2-1 3-2z" class="N"></path><path d="M175 379l6-9c0-1 1-3 1-5v1 3h1l1 1h0v-4c1-1 1-4 1-5v4 22 4-3h-5c-1 2-2 3-3 4-1 0-2-1-3-1 0-2-1-3-1-6 1 0 1 0 2-1h0l2-2h-1c-1-1-1-1-1-3z" class="R"></path><path d="M178 382h2 0 2v-1l1 1v2c1 1 1 2 1 2l-1 1h0-3c1-1 3-2 2-5-1 0-1 1-2 2h0c-2 0-3 1-5 1l3-3z" class="J"></path><path d="M176 382c1-1 1-2 2-2 1-1 2-3 3-3h1c0 1-3 4-4 5l-3 3-1 1c0 2 1 3 3 4 1 0 1-1 2-2h1c-1 2-2 3-3 4-1 0-2-1-3-1 0-2-1-3-1-6 1 0 1 0 2-1h0l2-2h-1z" class="Q"></path><path d="M175 385c2 0 3-1 5-1h0c1-1 1-2 2-2 1 3-1 4-2 5l-1 1c-1 1-1 2-2 2-2-1-3-2-3-4l1-1z" class="X"></path><path d="M174 367c0-1 1-2 2-2l-1 1h1c-2 6-6 12-10 17v3c-4 6-8 12-12 17-1 0-1-1-2-2h0 0c-5 2-9 7-14 9-3 0-6 1-8 2-3 1-7-1-9 1l-2-1-1 1h-1v1s-1 0-1-1h-2-1-1c1-1 1-2 2-3 1 0 1 0 2 1h1c3-1 6 0 9-1 8-1 17-5 23-11h0c5-4 8-9 11-14l2-2c5-4 9-11 12-16z" class="Y"></path><path d="M114 410c1 0 1 0 2 1h1l-3 2h-1-1c1-1 1-2 2-3z" class="G"></path><path d="M166 383v3c-4 6-8 12-12 17-1 0-1-1-2-2h0 0 0l9-12c1-2 3-4 5-6z" class="B"></path><defs><linearGradient id="Ax" x1="139.101" y1="426.237" x2="122.673" y2="434.303" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#Ax)" d="M152 401h0 0v3c-1 1-3 3-3 4v2c0 1-2 2-2 4h1c-1 1-1 1-1 3h1l-3 6-2 1h0c-1 1-2 1-2 1-1 1-1 1-1 2l-1 1v1h0l2-1 1 1v4c1 3 2 5 4 7l-2-1-1 2-6-5-2-2c-2-1-4-3-6-4-1 0-2-1-2-1-4-2-7-5-11-6l-2-1h3 4l2 1h0l1-1 1-2c0 2 2 3 3 4h1l2-1 3-3 4-5v-1l-2-2h1l1-2c5-2 9-7 14-9z"></path><path d="M133 423h3c-2 2-3 4-6 3h-1s1 0 1-1l3-2z" class="F"></path><path d="M125 420c0 2 2 3 3 4h1l1 1h0c0 1-1 1-1 1l-6-3 1-1 1-2z" class="U"></path><path d="M144 439c-2-1-2-2-3-3s-1-1-1-2l-3-3c0-1-1-1-1-2l2-1v1 1h1l1 2c0 1 1 1 1 1h1c1 3 2 5 4 7l-2-1z" class="P"></path><path d="M140 418c1 1 1 1 2 1v2h0v3h1 0c-1 1-2 1-2 1-1 1-1 1-1 2l-1 1v1h0l2-1 1 1v4h-1s-1 0-1-1l-1-2h-1v-1-1c1-2 1-3 0-5l-1-1c1-2 2-3 3-4z" class="J"></path><path d="M140 418c1 1 1 1 2 1v2h0-1c-1 0-2 1-2 1l-1 1-1-1c1-2 2-3 3-4z" class="I"></path><path d="M152 401h0 0v3c-1 1-3 3-3 4v2c0 1-2 2-2 4h1c-1 1-1 1-1 3h1l-3 6-2 1h-1v-3h0v-2c-1 0-1 0-2-1-1 1-2 2-3 4l-1 1h-3l-3 2h0l-1-1 2-1 3-3 4-5v-1l-2-2h1l1-2c5-2 9-7 14-9z" class="M"></path><path d="M133 423c1-1 3-2 4-3 0-1 1-2 1-3l1-1c1-2 2-1 4-2l-3 4c-1 1-2 2-3 4l-1 1h-3z" class="U"></path><path d="M145 412l4-4v2c0 1-2 2-2 4h1c-1 1-1 1-1 3h1l-3 6-2 1h-1v-3h0v-2c-1 0-1 0-2-1l3-4 2-2z" class="h"></path><path d="M143 414l2-2c0 3-2 5-3 7-1 0-1 0-2-1l3-4z" class="e"></path><path d="M560 303v-1c2 1 4 1 5 2h0 1v3h0 2c-1 1 0 2 0 4 3 5 5 11 6 17l1 6c1 1 2 2 4 3 2 0 3 0 5-1l1 1-3 1v1h-2l-1 1h1v1h-1-1c-1 0-1 0-2 1v3c1-1 2-1 3-3 0 1 1 1 1 1l1 1v2l1 2v1h0v1 1c-3 2-7 4-10 6 0 1 1 1 1 1l1 1 1 1-1 1-6 3c-5 3-8 8-12 12l-1-1-2-1c-1 3-3 5-5 7-1 0-2 1-3 1l-2 1v1c0 2 1 3 2 4l4 4-2 1 1 1h-1l2 2 5 7-1 1h0-3l3 3c-1 1-2 1-3 1s-1-1-1-1c-1-1-1-2-2-3s-3-2-4-4c-2-3-5-5-7-9 0 0 0-1-1-1l-1 1-1 1-1-1-3-1v-1h0c-1-1-1-1-2-1v-1c1 0 2 0 2-1l-1-2c-1 0-1 0-2 1 0-1 0-2 1-3h0v-1l-1 1c-1-1-1-2-1-4-2 0-3 2-4 3l-3 2v-5-12h0c0-9-2-19-3-27l-4-16 1 1h0 2c1-1 1-1 1-2 2-3 4-4 6-6l6-5c0-1 1-2 2-2h2l-1-1c1 0 2-1 3-1l1 1c0 1 0 2-1 3l5-3c1-1 2-2 4-2 2-1 3-1 5-1h3 5 1v-1c1 0 1 0 2 1v-1l2-1z" class="I"></path><path d="M575 345h0c2 1 3 0 5 1l-1 2h-2-1v2l-1-5z" class="R"></path><path d="M535 361h1l1 2c0 1-2 2-3 3h-1-1c0-2 1-3 3-5z" class="J"></path><path d="M574 346h0l1-2v1l1 5v1l-1 1h0c-2-2-3-4-3-6h2z" class="E"></path><path d="M536 350l2 2v1l4 6c0 1 1 1 1 2l2-1c1 2 1 2 1 3v1h-2c-4-4-7-9-8-14z" class="B"></path><path d="M529 347l-1-1c-1-2-1-8-1-11-1 2 0 4 0 6s-1 3-1 5l1 2v1c-2-3-1-8-1-11 1-7 3-15 9-20l-1 1v3c0 2 0 4-1 5l-1 2-1-1-2 5c-1 3-1 9 0 12h0 1c0 1-1 1-1 2z" class="X"></path><path d="M531 328l3-9v3c0 2 0 4-1 5l-1 2-1-1z" class="U"></path><path d="M571 333l1 3h2c0-1 0-2 1-2 1 1 2 2 4 3 2 0 3 0 5-1l1 1-3 1v1h-2l-1 1h1v1h-1-1c-1 0-2-1-2-1h-1v4l-1 2h0-2c-2-4-1-9-1-13z" class="H"></path><path d="M574 339l-2-2 1-1h1l1 1c0 1-1 1-1 2z" class="E"></path><path d="M572 346v-2h2v2h-2z" class="G"></path><path d="M574 339h1c-1 1-1 2-1 2h0c-1 1-1 1-2 1v-1c0-1 1-1 2-2z" class="O"></path><path d="M575 334c1 1 2 2 4 3 2 0 3 0 5-1l1 1-3 1v1h-2l-1 1h1v1h-1-1c-1 0-2-1-2-1h-1l-1 1s0-1 1-2h-1 0c0-1 1-1 1-2l-1-1h0c0-1 0-2 1-2z" class="f"></path><path d="M533 327l2 1v2s0 1 1 2h0c-2 2-1 5-4 6h0c0 1 0 1-1 2 0 2 0 3-1 5h-1 0c-1-3-1-9 0-12l2-5 1 1 1-2z" class="c"></path><path d="M533 327l2 1v2c-1 1-2 3-3 4v3c-1-2 0-6 0-8l1-2z" class="e"></path><path d="M535 330s0 1 1 2h0c-2 2-1 5-4 6h0v-1-3c1-1 2-3 3-4z" class="Q"></path><path d="M566 307h2c-1 1 0 2 0 4 3 5 5 11 6 17l1 6c-1 0-1 1-1 2h-2l-1-3-1-8c-1-5-2-8-6-12l1-1c0 1 0 1 1 0l-1-1h1v-4z" class="B"></path><path d="M570 325v-3c2 1 2 2 2 4 1 0 1 1 2 2l1 6c-1 0-1 1-1 2h-2l-1-3-1-8z" class="D"></path><path d="M537 378c2 0 2 0 3-1-1-2 0-3-1-4h-1l-1 1c-1-1-1-2-1-3-1 0-1-1-1-1h-1 0l1-1c1-1 1-1 1-2v-2h2 3l2 2c1 0 3 2 4 3v2c1 0 1 1 1 1v1h1c1 0 1-1 2-1h1 1l1 1h-1c-1 3-3 5-5 7-1 0-2 1-3 1l-2 1h-1 0l-3-3-2-2z" class="P"></path><path d="M545 382v-1c-1-1-1-2-1-3 3-1 2 0 3 2l1 1c-1 0-2 1-3 1z" class="e"></path><path d="M539 380l1-2h1c1 1 2 2 2 4h0c-1 1 0 1-1 1l-3-3z" class="W"></path><path d="M540 367v-1h1v1c1 2 1 2 2 3h1l-1 1-2 2h-1c-1-1-1-2-3-2v-1l1-1 2-2z" class="J"></path><path d="M541 373v-2c0-1 1-1 2-1h0v1l-2 2zm-1-6l1 1-2 2h0l-1-1 2-2z" class="X"></path><path d="M560 303v-1c2 1 4 1 5 2h0 1v3h0v4h-1l1 1c-1 1-1 1-1 0l-1 1c-1-1-3-2-5-3-4-2-10-3-14-2-7 1-11 5-15 10-5 8-7 16-6 25l-2-1v-4-2c0-3 1-6 2-8 2-6 5-12 9-17l5-3c1-1 2-2 4-2 2-1 3-1 5-1h3 5 1v-1c1 0 1 0 2 1v-1l2-1z" class="E"></path><path d="M555 305h1l2 2v1c-1 0-1 0-2-1-3 0-7 0-9-2h3 5z" class="F"></path><path d="M550 305h5v1 1l-4-1c-1 0-1 0-2-1h1z" class="c"></path><path d="M560 303v-1c2 1 4 1 5 2h0 1v3h0v4h-1 0c-2-2-4-3-7-4l-2-2v-1c1 0 1 0 2 1v-1l2-1z" class="B"></path><path d="M565 304h1v3h0v4h-1 0c0-2-1-4-1-6l1-1z" class="L"></path><path d="M560 303v-1c2 1 4 1 5 2h0l-1 1-1 1-1 1h0c-1-1-2-2-3-2h-1v-1l2-1z" class="U"></path><path d="M582 349h0v1 1c-3 2-7 4-10 6 0 1 1 1 1 1l1 1 1 1-1 1-6 3c-5 3-8 8-12 12l-1-1-2-1h1l1-3c0-2-1-2-2-2h-2c-1-1-2-2-3-2-2-1-3-2-4-3h2v-1c0-1 0-1-1-3-1 0-2-1-2-2h1v-1c2 2 4 3 8 4h3c4 0 9-1 12-4 5-1 11-5 15-8z" class="H"></path><path d="M548 367l1-1-2-2h0v-1c0-1 1-2 2-2 0 1 1 1 1 2l-1 1v2c1 0 2 1 3 2l-1 1c-1-1-2-2-3-2z" class="F"></path><path d="M582 349h0v1 1c-3 2-7 4-10 6-3 1-5 3-7 4-4 1-8 2-12 2-1-1-1-1-1-2h3c4 0 9-1 12-4 5-1 11-5 15-8z" class="L"></path><path d="M573 358l1 1 1 1-1 1-6 3c-5 3-8 8-12 12l-1-1-2-1h1l1-3c0-2-1-2-2-2h-2l1-1c-1-1-2-2-3-2v-2l1-1 1 1h3v-1h2 0c1 0 1 0 2 1h3c2-1 4-1 6-1 1-1 2-1 3-2 3 0 2-1 3-3z" class="T"></path><path d="M551 364h3v-1h2 0c1 0 1 0 2 1h3-1c-2 0-3 1-4 2h-4c-1-1-1-1-1-2z" class="E"></path><path d="M553 369c1-1 1-1 3-1 1-1 1-1 2 0l1 1c-1 1-3 4-4 5v1l-2-1h1l1-3c0-2-1-2-2-2z" class="U"></path><path d="M545 329h1c2 2 4 2 7 1h1c-1 2-4 1-5 3 0 1 0 1 1 1h-1s-1 0-1-1v2h2c-1 0-2 0-3 1 3 0 6-1 9 1l-1 1-2-1c-1 0-3 0-4 1h0l-1 1c-3 0-5 1-6 4-2 2-2 5-2 8 1 2 2 4 4 6v1h-1c0 1 1 2 2 2l-2 1c0-1-1-1-1-2l-4-6v-1l-2-2-1-2h-1v-1c-1-1 0-2-1-3 1 2 0 3 0 5 1 2 2 5 2 6l-1 1c0 1 0 2-1 3l-1-1s-1-1-1-2c0-2-1-2-1-3 0-2-1-3-1-5-1 1-1 1-1 2l-1-1 2-2c0-1 1-1 1-2 1-2 1-3 1-5 1-1 1-1 1-2h0c3-1 2-4 4-6h0v-2c0 1 1 1 1 2s1 1 1 2v1h1c2-2 5-1 6-2v-1h1l1 1h0l2-1-1-1h-1c-1 0-1-1-2-2z" class="J"></path><path d="M547 336c3 0 6-1 9 1l-1 1-2-1c-1 0-3 0-4 1h0l-1 1c-3 0-5 1-6 4-2 2-2 5-2 8 1 2 2 4 4 6v1h-1c0 1 1 2 2 2l-2 1c0-1-1-1-1-2l-4-6v-1l-2-2-1-2-1-5c0-2 0-2 1-3h1l1 7c1-5 3-8 7-11h3z" class="V"></path><path d="M547 336c3 0 6-1 9 1l-1 1-2-1h-3-5v1c-1-1-1-1-1-2h3z" class="Y"></path><defs><linearGradient id="Ay" x1="558.714" y1="339.952" x2="564.332" y2="328.224" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#Ay)" d="M546 311c3-1 6-1 9-1 1 1 3 2 4 3s2 1 3 2h0c2 2 3 4 4 6s1 4 1 5c1 2 0 4 1 6v3c-1 1-1 2-2 3v3c0 1-1 4-2 5h-1v1c-2-1-3-1-5-1 0-1 1-2 1-2v-2l-1-3c-1-1-2-1-2-2-3-2-6-1-9-1 1-1 2-1 3-1h-2v-2c0 1 1 1 1 1h1c-1 0-1 0-1-1 1-2 4-1 5-3l1-1v-1-1s0-1 1-1c1-1 1-3 2-4 0-3 0-5-2-7l-1-1-2-2c-2-1-4-1-7-1z"></path><path d="M559 342l1-1c1 0 2 0 2 1v2l-1 1-2-1v-2z" class="T"></path><path d="M562 342h0c0-2 0-3 1-4h2v4l-1 2c0-1 0-1-1-1l-1 1v-2z" class="F"></path><path d="M562 344l1-1c1 0 1 0 1 1l-1 2v1c-2-1-3-1-5-1 0-1 1-2 1-2l2 1 1-1z" class="E"></path><path d="M559 329h0 1c1 1 1 1 1 2v1c-1 1-1 1-2 1-1-1-2-1-2-2l2-2z" class="H"></path><path d="M559 329c0 2 0 2 1 3h1c-1 1-1 1-2 1-1-1-2-1-2-2l2-2z" class="E"></path><path d="M559 325s1-1 1-2c1 0 1 1 2 0 0-1 0-1 1-2l3 3c0 1 1 3 0 4v3c0 2-1 4-1 6v-3c-1-1-1-1-1-2v-1h0v-1-2h1l-2-1h-1c-1 0-1 1-2 1v1h-1 0l-2 2c0 1 1 1 2 2h-3 0c1 1 3 2 3 3 1 0 1 1 1 2-1 0-1 1-2 1-1-1-2-1-2-2-3-2-6-1-9-1 1-1 2-1 3-1h-2v-2c0 1 1 1 1 1h1c-1 0-1 0-1-1 1-2 4-1 5-3l1-1 4-4z" class="X"></path><path d="M556 330l1 1c0 1 1 1 2 2h-3 0c-1 1-1 1-2 1v-1c0-1 1-2 2-3z" class="U"></path><path d="M556 330c1-1 2-1 2-3 0-1 2-2 3-3h0c1 0 2-1 2-2h1c0 2 1 4 1 5s0 3 1 4c0 2-1 4-1 6v-3c-1-1-1-1-1-2v-1h0v-1-2h1l-2-1h-1c-1 0-1 1-2 1v1h-1 0l-2 2-1-1z" class="N"></path><path d="M546 311c3-1 6-1 9-1 1 1 3 2 4 3s2 1 3 2h0c2 2 3 4 4 6s1 4 1 5c1 2 0 4 1 6v3c-1 1-1 2-2 3v3c0 1-1 4-2 5h-1l1-2 1-2v-5c0-2 1-4 1-6v-3c1-1 0-3 0-4l-3-3c-1 1-1 1-1 2-1 1-1 0-2 0 0 1-1 2-1 2l-4 4v-1-1s0-1 1-1c1-1 1-3 2-4 0-3 0-5-2-7l-1-1-2-2c-2-1-4-1-7-1z" class="J"></path><path d="M555 314l1-1c2 2 4 4 4 7 0 2-1 4-1 5l-4 4v-1-1s0-1 1-1c1-1 1-3 2-4 0-3 0-5-2-7l-1-1z" class="C"></path><path d="M546 311c3 0 5 0 7 1l2 2 1 1c2 2 2 4 2 7-1 1-1 3-2 4-1 0-1 1-1 1v1 1l-1 1h-1c-3 1-5 1-7-1h-1c1 1 1 2 2 2h1l1 1-2 1h0l-1-1h-1v1c-1 1-4 0-6 2h-1v-1c0-1-1-1-1-2s-1-1-1-2v2c-1-1-1-2-1-2v-2l-2-1c1-1 1-3 1-5v-3l1-1c2-3 8-6 11-7z" class="W"></path><path d="M545 320c1-1 2-1 3 0h1c-1 1-1 2-2 3-1-1-2-1-3-2h0l1-1z" class="N"></path><path d="M542 319l1-1h0v2h2l-1 1c-1 0-1 1-2 1v-1c-1 1-1 2-2 2l-1-1 3-3z" class="J"></path><path d="M544 321c1 1 2 1 3 2h4l1 1c-2 1-3 1-5 2-1-1-2-2-3-4v-1z" class="O"></path><path d="M544 321h0v1c1 2 2 3 3 4s3 1 4 1 1 0 2-1h3c-1 0-1 1-1 1v1 1l-1 1h-1c-3 1-5 1-7-1-1-1 0-1 0-2h0c-2-1-3-3-4-5 1 0 1-1 2-1z" class="H"></path><path d="M546 327c3 2 6 1 9 0v1 1l-1 1h-1c-3 1-5 1-7-1-1-1 0-1 0-2z" class="S"></path><path d="M556 315c2 2 2 4 2 7-1 1-1 3-2 4h-3c-1 1-1 1-2 1s-3 0-4-1c2-1 3-1 5-2l-1-1c1-2 2-3 3-4l-1-3h0c1-1 2-1 3-1z" class="G"></path><path d="M554 319l1-2v1 1c1 1 1 1 1 2-1 1-2 2-4 3l-1-1c1-2 2-3 3-4z" class="L"></path><path d="M546 311c3 0 5 0 7 1l2 2 1 1c-1 0-2 0-3 1h0c-1-1-2-3-4-3-3 0-6 2-8 4l-2 1-2 1-3 3v-3l1-1c2-3 8-6 11-7z" class="H"></path><path d="M539 318v1h1s1 0 1-1l1 1-3 3 1 1c1 0 1-1 2-2v1c1 2 2 4 4 5h0c0 1-1 1 0 2h-1c1 1 1 2 2 2h1l1 1-2 1h0l-1-1h-1v1c-1 1-4 0-6 2h-1v-1c0-1-1-1-1-2s-1-1-1-2v2c-1-1-1-2-1-2v-2l-2-1c1-1 1-3 1-5l3-3 2-1z" class="X"></path><path d="M540 332c0 1-1 1-1 2h0l-2-2c0-1 1-2 1-3l3-3 3 2c-1 1-1 2-2 2-1 1-2 1-2 2z" class="N"></path><path d="M540 332h-2v-1c1-1 1-2 2-2h1l1 1c-1 1-2 1-2 2z" class="F"></path><path d="M539 322l1 1c1 0 1-1 2-2v1c1 2 2 4 4 5h0c0 1-1 1 0 2h-1l-1-1-3-2c0-1 0-1-1-2l-1 1c0 1-1 2-1 2l-1 1v-3c1-1 2-2 2-3z" class="K"></path><path d="M539 318v1h1s1 0 1-1l1 1-3 3c0 1-1 2-2 3v3l-1 2v2c-1-1-1-2-1-2v-2l-2-1c1-1 1-3 1-5l3-3 2-1z" class="X"></path><path d="M536 323l1 2h0v3l-1 2v2c-1-1-1-2-1-2v-2c1-1 1-1 1-3v-2z" class="I"></path><path d="M539 319h1s1 0 1-1l1 1-3 3c0 1-1 2-2 3h0l-1-2c1-1 2-3 3-4z" class="C"></path><defs><linearGradient id="Az" x1="544.618" y1="339.194" x2="570.029" y2="355.584" xlink:href="#B"><stop offset="0" stop-color="#4a4949"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#Az)" d="M566 321c3 4 2 8 3 12 0 3 0 6-1 9h0c1 4 3 8 2 12h0c0 1 0 1 1 1h-1c0 1-1 1-2 2h-1c-3 3-8 4-12 4h-3c-4-1-6-2-8-4s-3-4-4-6c0-3 0-6 2-8 1-3 3-4 6-4l1-1h0c1-1 3-1 4-1l2 1 1-1c0 1 1 1 2 2l1 3v2s-1 1-1 2c2 0 3 0 5 1v-1h1c1-1 2-4 2-5v-3c1-1 1-2 2-3v-3c-1-2 0-4-1-6 0-1 0-3-1-5z"></path><path d="M563 347l1 1c-1 1-1 2-2 2-1 1-1 1-2 1 0-1 1-1 1-2l-1-1 3-1z" class="K"></path><path d="M568 342c1 4 3 8 2 12l-1-1v-1c-1-3-1-7-1-10z" class="P"></path><path d="M549 350c0 1 2 2 3 3 3 0 5 0 7-2l1 2v1h-1c0 1 1 1 1 1v1h0v-1c-1 0-2 0-3 1h-3 0-3c-1 0-2-1-2-1h-1 1 1v-1l-1-1v-3z" class="b"></path><path d="M552 353c3 0 5 0 7-2l1 2-2 1-1 1-2-1c-1-1-2-1-3-1h0z" class="P"></path><path d="M558 346c2 0 3 0 5 1h0l-3 1 1 1c0 1-1 1-1 2h-1c-2 2-4 2-7 2-1-1-3-2-3-3l-1-1 1-1c1 1 2 2 4 2h1c1-2 3-2 4-4z" class="C"></path><path d="M558 346c2 0 3 0 5 1h0l-3 1c-2 1-4 3-7 3v-1h1c1-2 3-2 4-4z" class="c"></path><path d="M556 337c0 1 1 1 2 2l1 3v2s-1 1-1 2c-1 2-3 2-4 4h-1 0c-1-1-1-2-1-3-1 0 0-3 0-4-1-1-2-1-2-2l2 1s1 0 1 1l2-2c-2-2-4-2-7-2l1-1h0c1-1 3-1 4-1l2 1 1-1z" class="G"></path><path d="M555 341c1 1 2 1 2 3 0 1-1 1-2 2l-3 1c-1 0 0-3 0-4-1-1-2-1-2-2l2 1s1 0 1 1l2-2z" class="Q"></path><path d="M547 342c1-1 2-1 3-1 0 1 1 1 2 2 0 1-1 4 0 4 0 1 0 2 1 3h0c-2 0-3-1-4-2l-1 1 1 1v3l1 1v1h-1-1v-1c0-1-2-1-3-2 0-1-1-2-1-2 0-1-1-2-1-2-1 1-1 1-1 2 0-3 1-4 2-6 1-1 2-2 3-2z" class="T"></path><path d="M547 342c1 0 1 1 2 2v2c-1 1-1 1-2 1v4l-3-3c0-1 0-2 1-3l-1-1c1-1 2-2 3-2z" class="N"></path><defs><linearGradient id="BA" x1="569.478" y1="389.841" x2="510.24" y2="326.034" xlink:href="#B"><stop offset="0" stop-color="#292728"></stop><stop offset="1" stop-color="#4b4b4c"></stop></linearGradient></defs><path fill="url(#BA)" d="M531 309l-1-1c1 0 2-1 3-1l1 1c0 1 0 2-1 3-4 5-7 11-9 17-1 2-2 5-2 8v2 4l2 1v4c1 6 3 13 6 19 0 0 1 2 1 3l6 9 2 2 3 3h0 1v1c0 2 1 3 2 4l4 4-2 1 1 1h-1l2 2 5 7-1 1h0-3l3 3c-1 1-2 1-3 1s-1-1-1-1c-1-1-1-2-2-3s-3-2-4-4c-2-3-5-5-7-9 0 0 0-1-1-1l-1 1-1 1-1-1-3-1v-1h0c-1-1-1-1-2-1v-1c1 0 2 0 2-1l-1-2c-1 0-1 0-2 1 0-1 0-2 1-3h0v-1l-1 1c-1-1-1-2-1-4-2 0-3 2-4 3l-3 2v-5-12h0c0-9-2-19-3-27l-4-16 1 1h0 2c1-1 1-1 1-2 2-3 4-4 6-6l6-5c0-1 1-2 2-2h2z"></path><path d="M514 324c1-1 1-1 1-2 1 0 2 1 3 1-1 1-2 1-4 1h0z" class="P"></path><path d="M515 322c2-3 4-4 6-6 1 1 1 1 1 2h0c-1 0-1 0-2 1s-1 2-1 3 0 1-1 1-2-1-3-1z" class="K"></path><path d="M534 382h0c1 1 2 2 3 2 1 1 1 2 2 3 0 0 0-1 1-1v1l3 3c1 1 1 1 3 2l1 1 1 1h-1l2 2 5 7-1 1h0-3c-6-7-11-14-16-22z" class="U"></path><path d="M553 404v-1c-1 0-1 0-2-1s-3-3-3-5l1-1 5 7-1 1z" class="T"></path><path d="M540 386v1l3 3c1 1 1 1 3 2l1 1 1 1h-1-1-2c-1-1-5-6-5-7 0 0 0-1 1-1z" class="H"></path><path d="M531 309l-1-1c1 0 2-1 3-1l1 1c0 1 0 2-1 3-4 5-7 11-9 17-1 2-2 5-2 8v2 3c0 1-1 1-1 2l-2-2v-4c0-5 1-12 4-17 2-3 6-7 8-10v-1z" class="L"></path><path d="M521 343l-2-2v-4c0-5 1-12 4-17 0 4-1 7-2 11 0 1 0 3-1 4v4l1 2h0v-2c0-1 0-1 1-1v-2 2 3c0 1-1 1-1 2z" class="D"></path><path d="M521 343c0-1 1-1 1-2v-3 4l2 1v4c1 6 3 13 6 19 0 0 1 2 1 3l6 9 2 2 3 3h0 1v1c0 2 1 3 2 4l4 4-2 1-1-1c-2-1-2-1-3-2l-3-3v-1c-1 0-1 1-1 1-1-1-1-2-2-3-1 0-2-1-3-2h0c-3-4-5-8-8-13-3-7-5-14-6-22v-3h1v-1z" class="L"></path><path d="M521 343c0-1 1-1 1-2v-3 4l2 1v4c1 6 3 13 6 19-1-1-2-1-3-1-3-6-5-13-5-19v-1h-1-1v2-3h1v-1z" class="G"></path><path d="M522 342l2 1v4c0-1 0-1-1-1h-1v-4z" class="M"></path><defs><linearGradient id="BB" x1="543.354" y1="392.9" x2="540.56" y2="377.459" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#BB)" d="M531 369l6 9 2 2 3 3h0 1v1c0 2 1 3 2 4l4 4-2 1-1-1c-2-1-2-1-3-2l-3-3v-1c-2-4-4-7-6-10-1-2-3-4-3-7z"></path><path d="M515 339v-3c0-1 1-2 1-3h1c1 4 0 9 1 13 1 13 6 26 14 37l1 2 3 4v1h-1l-1 1-1 1-1-1-3-1v-1h0c-1-1-1-1-2-1v-1c1 0 2 0 2-1l-1-2c-1 0-1 0-2 1 0-1 0-2 1-3h0v-1l-1 1c-1-1-1-2-1-4-2 0-3 2-4 3l-3 2v-5-12h0c0-9-2-19-3-27z" class="J"></path><path d="M522 373h0 0l-1 2h-1c0-1 1-2 2-2z" class="P"></path><path d="M532 383l1 2 3 4v1h-1l-1 1c-1-1-2 0-3-1v-3h-2 0l1-2c1 0 1-1 2-2z" class="I"></path><path d="M532 383l1 2v1c-1 0-1 0-2-1h-1c1 0 1-1 2-2z" class="R"></path><defs><linearGradient id="BC" x1="363.323" y1="235.257" x2="363.335" y2="243.408" xlink:href="#B"><stop offset="0" stop-color="#2a282a"></stop><stop offset="1" stop-color="#4b4b4a"></stop></linearGradient></defs><path fill="url(#BC)" d="M386 232l1 1c1 0 3 0 3-1h1l1-1 1 1h0v1c-1 1-2 1-4 2h0 3 45 0l2 1c0 1 0 1-1 2l-3 6h0c-1 2-4 7-6 8h-2c-2 1-5 1-7 1h-5 0c2 0 3 0 4-1h-1c-2-1-4-1-6-1h-4v-1l1-1c-1-1-1 0-1-1l1-1v-1l-1 1c-1 2-3 3-5 5h-6-6c-3 0-8-1-10 0 1 1 4 1 6 1h-10v6 30c-2-1-5-4-7-3h0c-1-1-1-1-2-1h-2v1h0l1 1 1 1c0 2 1 3 1 4 1 1 4 2 4 3v2l-2 3c0 2-2 3-3 4s-1 1-2 1c-1 1-3 2-4 4h-1v-2-2c1-2 4-3 5-4v-1h1v-1c1 0 1 0 2-1-2-1-2 0-3 0-4 1-8 3-11 5-1 0-1-1-2-2l3-3-1-1s-1 0-1 1c-1 1-2 2-4 3-1 1-2 2-3 2l-4 4h-1l-1 1-1 2v-2h-1c-1 1-1 1-1 2s0 1-1 2h0c-1 2-2 4-2 6l-1 1-2 2-2 1c-1 1-1 1-2 1l-1-18v-13c1-3 0-6 0-8v-19c0-3 1-8 0-11h-1c0-1-1-1-1-1h-1c3-1 6-1 8-2v-1h-1 1c1 0 2-2 2-3 1-1 1-1 1-2v-1-1h0-2l-1-1v-1c-1-1-4 0-6 0-1-1-1-1-2-1v-1c-2-1-7-1-9-1-1 1-1 1-2 1-2-1-4 0-6-1-6-1-13-1-20 0 0 0-1 0-2 1-2 0-4 0-5-1h5 1l1-1h2l-1-1h0c2-1 5-1 7-1h22 30 12c2-1 4 0 6 0l11-1c4 0 7 1 10-2z"></path><path d="M353 243h0c2 0 3 0 4-1h0 1-1c-1 1-2 2-3 2l-1-1z" class="K"></path><path d="M386 232l1 1c1 0 3 0 3-1h1l1-1 1 1h0v1c-1 1-2 1-4 2h0-30c2-1 4 0 6 0l11-1c4 0 7 1 10-2z" class="L"></path><path d="M353 243l1 1c1 0 2-1 3-2v2h0c1 1 1 0 2 0 1-1 3-1 4-1h0c-2 2-5 4-7 6 0 1 0 1-1 1h-2c-1 0-1-1-2-1-1 1-1 1-2 1v-2c1 0 1-1 2-1l-1-1 1-1h-3 0-1-4c-1 0-2 1-3 1 1-1 2-1 2-2h1c1-1 2-1 3-1h0c2 1 2 0 3 0l1 1c1 0 2-1 3-1z" class="P"></path><defs><linearGradient id="BD" x1="432.336" y1="238.803" x2="433.012" y2="247.704" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#636363"></stop></linearGradient></defs><path fill="url(#BD)" d="M408 239c3-1 6-1 9-1h9c4 0 8 1 12 0l-3 6h0c-1 2-4 7-6 8h-2c-2 1-5 1-7 1h-5 0c2 0 3 0 4-1h0 3c2-1 5-1 6-2l2-2c1-1 2-3 2-4h0c1-1 1-1 1-2h0-5v-2c-1-1-3 0-4-1s-2 0-3 0h-13z"></path><path d="M366 242c1-1 3-1 4-1h1v-1c1 0 2 0 3 1 1 0 1 0 2-1h0c1 1 1 1 2 1l1-1h1 3-1l-1-1h3-1c-2-1-4 0-6 0 2-1 4-1 6 0 1 0 2-1 3 0h2c7-1 13 0 20 0h13c1 0 2-1 3 0s3 0 4 1v2h5 0c0 1 0 1-1 2h0c0 1-1 3-2 4l-2 2c-1 1-4 1-6 2h-3 0-1c-2-1-4-1-6-1h-4v-1l1-1 1-1-1-1 4-4h0 1l-1-1c-1 0-2 0-3 1 0 1-1 1-2 1-2-1-2-1-5-1l3-1c-1 0-1 0-2-1h-7-11-7c-5 1-9 2-13 1z" class="I"></path><path d="M421 241h2c1-1 3-1 5-1v2h5 0c0 1 0 1-1 2h0c0 1-1 3-2 4-1-1-1-1-2-1s-1 0-1-1h-1-5v-1l-1-1h-3c-2 1-5 2-7 4l-1-1 4-4h0 1l-1-1c-1 0-2 0-3 1 0 1-1 1-2 1-2-1-2-1-5-1l3-1c3-1 6-2 10-1v1c2 0 3-1 5-1z" class="N"></path><path d="M426 246c1-1 2-1 3-2h2 0 1c0 1-1 3-2 4-1-1-1-1-2-1s-1 0-1-1h-1z" class="F"></path><path d="M421 241h2c1-1 3-1 5-1v2h5 0c0 1 0 1-1 2v-1l-3 1c-2-1-4 0-6-1h0v-2h-2z" class="R"></path><path d="M410 248c2-2 5-3 7-4h3l1 1v1h5 1c0 1 0 1 1 1s1 0 2 1l-2 2c-1 1-4 1-6 2h-3 0-1c-2-1-4-1-6-1h-4v-1l1-1 1-1z" class="H"></path><path d="M412 251c-1-1-1-1-1-2 1-1 5-3 6-3h1c1 0 1 1 1 2l1-1h2v1h2 0c0 1 1 1 0 2-2 1-4 1-5 2h0-1c-2-1-4-1-6-1z" class="B"></path><defs><linearGradient id="BE" x1="366.946" y1="240.096" x2="345.63" y2="276.872" xlink:href="#B"><stop offset="0" stop-color="#777676"></stop><stop offset="1" stop-color="#8f8d8e"></stop></linearGradient></defs><path fill="url(#BE)" d="M386 241h11 7c1 1 1 1 2 1l-3 1c3 0 3 0 5 1 1 0 2 0 2-1 1-1 2-1 3-1l1 1h-1 0l-4 4 1 1-1 1c-1-1-1 0-1-1l1-1v-1l-1 1c-1 2-3 3-5 5h-6-6c-4-1-9-1-13 0l-2 1c-2 0-4 0-5 1l-2-1h0 0l-1-1c0 1-1 2-2 2-1 1-2 1-2 1h0c-1 1-3 2-4 2 0 1-1 2-1 2-2 1-4 2-5 3-2 1-3 3-5 4-2 2-3 4-5 5v-1l-2 2h0c-1 0-1 1-2 1h0-1c0-1 0-2 1-3h0 0c0-2 0-3 1-4 0-1 0-2 1-3 1-2 2-4 4-6 1 0 1 1 2 2h0c2 0 3-1 4-2h0l1-1c1 0 1-1 2-2l-3 2-2-2h0v-1c1 0 1 0 1-1l-1-1h-1-1v-2c0-1 2-2 2-3l1 1c-1 0-1 1-2 1v2c1 0 1 0 2-1 1 0 1 1 2 1h2c1 0 1 0 1-1 2-2 5-4 7-6h1c0-1 1-1 2-1 4 1 8 0 13-1h7z"></path><path d="M364 255c-1 0-2 1-3 0h0c1-2 2-2 4-3l-1-1h1c2 0 2 0 3 1 0 1-1 2-2 2-1 1-2 1-2 1h0z" class="X"></path><path d="M366 242c4 1 8 0 13-1h7l-1 1c-1 1-2 1-3 1-2 0-3 1-5 1l-1-1c-1 1-2 1-2 1-3 0-3 1-4 1-2 1-4 1-5 2s-1 2-2 3c-2 1-3 1-5 2-1 1-2 2-3 2h0l-3 2-2-2h0v-1c1 0 1 0 1-1l-1-1h-1-1v-2c0-1 2-2 2-3l1 1c-1 0-1 1-2 1v2c1 0 1 0 2-1 1 0 1 1 2 1h2c1 0 1 0 1-1 2-2 5-4 7-6h1c0-1 1-1 2-1z" class="Z"></path><path d="M366 242c4 1 8 0 13-1h7l-1 1c-5 0-10 0-14 2h-5l-1-1h-1c0-1 1-1 2-1z" class="R"></path><path d="M369 253l1-1v-2h0 1v-1l2-2c1 0 1 0 2-1h0c2-1 4-1 6-2 1 0 2 0 3-1h1 4c1-1 2-1 3 0v1c2 0 3-1 4-1 1-1 4-1 6-1l-1 2c1 0 2 0 2-1 3 0 3 0 5 1 1 0 2 0 2-1 1-1 2-1 3-1l1 1h-1 0l-4 4 1 1-1 1c-1-1-1 0-1-1l1-1v-1l-1 1c-1 2-3 3-5 5h-6-6c-4-1-9-1-13 0l-2 1c-2 0-4 0-5 1l-2-1z" class="F"></path><path d="M387 244h1 2c-2 2-3 3-5 4l-2 1c-2 1-4 1-7 2-1 0-2 1-3 1s-2 1-2 1v-1l1-1s0-1 1-2c0-1 3-2 4-2l6-1v-1c2 0 3 0 4-1z" class="M"></path><path d="M391 246c2-1 3-2 4-2h1 2c1 1 1 1 1 2h2 0-1v1h1 1 5c0-1 0-1 1 0-1 2-3 3-5 5h-6c-1 0-2 0-3-1s-4-1-7-1h1l-1-1h1l3-3z" class="E"></path><path d="M391 246l6-1c-2 3-4 5-8 4h0-1l3-3z" class="H"></path><path d="M407 247c0-1 0-1 1 0-1 2-3 3-5 5-2-1-6 0-8-2l1-1c0-1 2-1 3-1h0c3 0 5 0 7-1h1z" class="f"></path><defs><linearGradient id="BF" x1="325.053" y1="283.378" x2="353.339" y2="280.986" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#7d7d7d"></stop></linearGradient></defs><path fill="url(#BF)" d="M333 243c1-1 2-1 4-1 0 1-1 2 0 3 2 0 4-1 6-1h-1c0 1-1 1-2 2 1 0 2-1 3-1h4 1 0 3l-1 1c0 1-2 2-2 3v2h1 1l1 1c0 1 0 1-1 1v1h0l2 2 3-2c-1 1-1 2-2 2l-1 1h0c-1 1-2 2-4 2h0c-1-1-1-2-2-2-2 2-3 4-4 6-1 1-1 2-1 3-1 1-1 2-1 4h0 0c-1 1-1 2-1 3h1 0c1 0 1-1 2-1-2 1-3 2-4 4 0 2-2 4-2 6h1 0v1c-1 1-1 3-1 4 0 0-1 1-1 2v1l1 1c0 1 0 1-1 1 0 2 0 4 1 5 1 0 3-1 4 0 0 0 1 0 1 1v1h-1c0-1 0-1-1-2-1 0-1 0-2 1h1c0 1 1 1 1 1h-2-1 0 0v1c-1 0-1 0-1 1-1 1-1 5 0 7 0 1 1 2 2 2v2c-1 2-2 4-2 6l-1 1-2 2-2 1c-1 1-1 1-2 1l-1-18v-13c1-3 0-6 0-8v-19c0-3 1-8 0-11h-1c0-1-1-1-1-1h-1c3-1 6-1 8-2v-1h-1 1c1 0 2-2 2-3 1-1 1-1 1-2v-1-1h0-2z"></path><path d="M343 245h4 1 0 3l-4 1v1-1c0 1 0 1-1 1 0 1-1 1-1 2h1-1c-1 0-3 1-4 1 1-1 2-2 2-3-1 0-1 0-2-1l2-1z" class="Z"></path><path d="M330 322c0-5 2-10 1-15 0-2 0-4-1-7-1-5 0-11 0-17 1-2 0-4 1-6v2h1v4c0 3-1 6-1 10 1 3 1 6 2 10 0 2 0 7 1 9l1 1h-1c-1 1-1 2-1 4-1 1-1 2-1 4l-2 1z" class="C"></path><path d="M332 283c1-4 2-10 1-14-1-2-1-4 0-6l1-2c1 0 1 0 2-1l1-1h1c-1 2-3 3-4 4-1 2-1 4-1 6l1 2v-1h1v-2l1 9-2 2h0v4 2 1c0 1 0 3-1 4v3 1h0c0 1 0 2 1 3v2h2v1c-1 0-1 0-1 1-1 1-1 5 0 7 0 1 1 2 2 2v2c-1 2-2 4-2 6l-1 1-2 2c0-2 0-3 1-4 0-2 0-3 1-4h1l-1-1c-1-2-1-7-1-9-1-4-1-7-2-10 0-4 1-7 1-10z" class="P"></path><path d="M349 251h1l1 1c0 1 0 1-1 1v1h0l2 2 3-2c-1 1-1 2-2 2l-1 1h0c-1 1-2 2-4 2h0c-1-1-1-2-2-2-2 2-3 4-4 6-1 1-1 2-1 3-1 1-1 2-1 4h0 0c-1 1-1 2-1 3h1 0c1 0 1-1 2-1-2 1-3 2-4 4 0 2-2 4-2 6h1 0v1c-1 1-1 3-1 4 0 0-1 1-1 2v1l1 1c0 1 0 1-1 1 0 2 0 4 1 5 1 0 3-1 4 0 0 0 1 0 1 1v1h-1c0-1 0-1-1-2-1 0-1 0-2 1h1c0 1 1 1 1 1h-2-1 0 0-2v-2c-1-1-1-2-1-3h0v-1-3c1-1 1-3 1-4v-1-2-4h0l2-2-1-9c1-1 1-1 1-2l1-2c1 0 1 0 2-1 0-1 1-2 2-2l2-1-1-1h-1 1c0-1 0-1-1-2 0-1 0-1 1-1 1-2 2-3 5-3h0l2-2z" class="T"></path><path d="M347 253c1 0 1 0 1 1s-1 2-1 2c-1 1-2 1-3 1v-1l3-3h0z" class="F"></path><path d="M335 268c1-1 1-1 1-2l1-2c1 0 1 0 2-1 0-1 1-2 2-2l-3 6v3h0v3h0c0 2-1 3-2 4l-1-9z" class="J"></path><path d="M378 252c4-1 9-1 13 0-3 0-8-1-10 0 1 1 4 1 6 1h-10v6 30c-2-1-5-4-7-3h0c-1-1-1-1-2-1h-2v1h0l1 1 1 1c0 2 1 3 1 4 1 1 4 2 4 3v2l-2 3c0 2-2 3-3 4s-1 1-2 1c-1 1-3 2-4 4h-1v-2-2c1-2 4-3 5-4v-1h1v-1c1 0 1 0 2-1-2-1-2 0-3 0-4 1-8 3-11 5-1 0-1-1-2-2l3-3-1-1s-1 0-1 1c-1 1-2 2-4 3-1 1-2 2-3 2l-4 4h-1l-1 1-1 2v-2h-1c-1 1-1 1-1 2s0 1-1 2h0v-2c-1 0-2-1-2-2-1-2-1-6 0-7 0-1 0-1 1-1v-1h0 0 1 2s-1 0-1-1h-1c1-1 1-1 2-1 1 1 1 1 1 2h1v-1c0-1-1-1-1-1-1-1-3 0-4 0-1-1-1-3-1-5 1 0 1 0 1-1l-1-1v-1c0-1 1-2 1-2 0-1 0-3 1-4v-1h0-1c0-2 2-4 2-6 1-2 2-3 4-4h0l2-2v1c2-1 3-3 5-5 2-1 3-3 5-4 1-1 3-2 5-3 0 0 1-1 1-2 1 0 3-1 4-2h0s1 0 2-1c1 0 2-1 2-2l1 1h0 0l2 1c1-1 3-1 5-1l2-1z" class="F"></path><path d="M365 276l3-1h1l1 1c-2 1-3 2-5 3-1 0-1 0-2 1l2 4c-1 0-2 1-2 1 1 1 1 2 2 2v1h-1l1 1-2 1h-2v-1-1-2s0-1 1-1 1 0 1-1l-1-1c-1-1-1-2 0-3 0 0 1-1 2-1h0c1-1 1-1 2-1 1-1 2-1 2-2-1 0-2 1-4 2-1 1-1 1-2 1h-1l2-2c1 0 1 0 2-1z" class="T"></path><path d="M344 271c2-1 3-3 5-5 2-1 3-3 5-4 0 1-1 1-1 2h0l-2 3c-1 1 0 2 0 3l-2 1c-3 2-5 4-6 7l1 1 2-1v1c-1 0-4 2-4 3l-1 3h-1v-1h0l-2 2c0-1 1-2 2-3v-2h0c0-2 1-3 1-4 1-1 0-3 1-4 0-1 2-2 2-2z" class="H"></path><path d="M351 270h1c1 0 2-1 3-2h2 0v3c-1 0-3 1-3 2h-1v3c-1 1-2 1-3 2-2 0-2 1-3 2l-1-1h0v-1l-2 1-1-1c1-3 3-5 6-7l2-1z" class="G"></path><defs><linearGradient id="BG" x1="337.942" y1="300.379" x2="346.232" y2="301.898" xlink:href="#B"><stop offset="0" stop-color="#7c7d7b"></stop><stop offset="1" stop-color="#959395"></stop></linearGradient></defs><path fill="url(#BG)" d="M337 304c1-2 2-3 3-4 1 1 2 0 3 0 2-1 3-3 4-5 0-1 2-2 2-2h0c0 2-1 2-1 3-1 0-1 1-1 1 1 1 1 1 1 2l-3 2 2 2-4 4h-1l-1 1-1 2v-2h-1c-1 1-1 1-1 2s0 1-1 2h0v-2c-1 0-2-1-2-2-1-2-1-6 0-7 0-1 0-1 1-1v-1h0 0 1 2 0l-2 2c-1 1-1 2 0 3h0z"></path><path d="M343 307c-1-1-1-1 0-1v-1c0-2 0-2 2-4l2 2-4 4z" class="E"></path><defs><linearGradient id="BH" x1="335.531" y1="307.038" x2="342.118" y2="304.977" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#838182"></stop></linearGradient></defs><path fill="url(#BH)" d="M336 299h0 0 1 2 0l-2 2c-1 1-1 2 0 3h0l-1 2h1c1 0 2-1 3-2 1 0 1-1 2-2l1 1c0 1-1 1-1 2-1 1-2 2-2 3h-1c-1 1-1 1-1 2s0 1-1 2h0v-2c-1 0-2-1-2-2-1-2-1-6 0-7 0-1 0-1 1-1v-1z"></path><defs><linearGradient id="BI" x1="346.816" y1="295.47" x2="344.976" y2="273.576" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#BI)" d="M346 279h0l1 1c1-1 1-2 3-2 1-1 2-1 3-2v-3h1c1 1 0 1 1 2l1 1c1-1 3-1 4-2v1c-1 1-2 2-2 3-1 1-2 1-3 2l-1 2h0l1 1c-1 1-1 2-3 3l-5 4h-1c-1 1-3 2-3 3h-2c-1 1-2 3-3 3 0 0-1 0-1 1v-4-1l1-1c0-1-1-2-1-2h-1l1-1 1 1h0l-1-1c0-1 1-2 1-2l2-2h0v1h1l1-3c0-1 3-3 4-3z"></path><path d="M346 285h2v1l-2 2h-1v-2l1-1z" class="B"></path><path d="M360 274v1c-1 1-2 2-2 3-1 1-2 1-3 2s-3 3-5 4c-1 1-1 1-2 0h0c2-3 6-5 8-8 1-1 3-1 4-2z" class="G"></path><path d="M349 293c2 0 2-2 3-2s2 0 3-1h-1-1-1c-1 1-3 1-4 2h0c0-1 1-1 2-2h1c1-1 2-1 3-1s1-1 2-1h1l1-1h0c1-1 2-1 2-3v2c-1 1-3 2-5 3v1h1 2c1 0 0 1 1 2l2-1 1 1h0v-2h-2 0l1-1v1h2l2-1-1-1h1v-1c1 0 2 1 3 1 0 2 1 3 1 4 1 1 4 2 4 3v2l-2 3c0 2-2 3-3 4s-1 1-2 1c-1 1-3 2-4 4h-1v-2-2c1-2 4-3 5-4v-1h1v-1c1 0 1 0 2-1-2-1-2 0-3 0-4 1-8 3-11 5-1 0-1-1-2-2l3-3-1-1s-1 0-1 1c-1 1-2 2-4 3-1 1-2 2-3 2l-2-2 3-2c0-1 0-1-1-2 0 0 0-1 1-1 0-1 1-1 1-3h0z" class="M"></path><path d="M348 299l1-2c1 0 1-1 1-1v-1l1-1c2 0 2-2 4-2h0v-1l1 1c1 0 2 0 2 1s1 2 0 3l-2 2-1-1s-1 0-1 1c-1 1-2 2-4 3-1 1-2 2-3 2l-2-2 3-2z" class="f"></path><path d="M365 287c1 0 2 1 3 1 0 2 1 3 1 4 1 1 4 2 4 3v2l-2 3c0 2-2 3-3 4s-1 1-2 1c0-1 0-2 1-2 0-1 1-1 1-2s1-2 2-3l-1-1h-4c-1 1-3 2-4 2s-1 0-2 1h-1l-1-1h0v-1l1-1c1-1 3-2 4-5h0v-2h-2 0l1-1v1h2l2-1-1-1h1v-1z" class="U"></path><path d="M362 292h0v-2h-2 0l1-1v1h2l2-1v1c0 1 0 2-1 3 0 1-1 1-2 1h2 1c-1 1-2 2-4 3-1 1-2 2-3 2v-2c1-1 3-2 4-5z" class="N"></path><path d="M365 287c1 0 2 1 3 1 0 2 1 3 1 4 1 1 4 2 4 3v2l-2 3c0 2-2 3-3 4s-1 1-2 1c0-1 0-2 1-2 0-1 1-1 1-2s1-2 2-3l2-2-1-1c-2 0-3-1-6-1h-1-2c1 0 2 0 2-1 1-1 1-2 1-3v-1l-1-1h1v-1z" class="Z"></path><path d="M378 252c4-1 9-1 13 0-3 0-8-1-10 0 1 1 4 1 6 1h-10v6 30c-2-1-5-4-7-3h0c-1-1-1-1-2-1h-2v1h0l1 1 1 1c-1 0-2-1-3-1s-1-1-2-2c0 0 1-1 2-1l-2-4c1-1 1-1 2-1 2-1 3-2 5-3l-1-1h-1l-3 1h1v-2c-2 1-3 2-5 4v-1c-3 2-5 3-6 6l-1-1h0l1-2c1-1 2-1 3-2 0-1 1-2 2-3v-1c-1 1-3 1-4 2l-1-1c-1-1 0-1-1-2 0-1 2-2 3-2v-3h0-2c-1 1-2 2-3 2h-1c0-1-1-2 0-3l2-3h0c0-1 1-1 1-2 1-1 3-2 5-3 0 0 1-1 1-2 1 0 3-1 4-2h0s1 0 2-1c1 0 2-1 2-2l1 1h0 0l2 1c1-1 3-1 5-1l2-1z" class="e"></path><path d="M376 253l2-1h0c-2 2-5 3-7 5-1 1-1 3-2 4l1 1s0 1-1 1c0 1 0 1 1 2l1 1c0 1-1 2-1 3v1l-4 4c-2 1-3 2-5 4v-1c1-1 2-1 3-3 1-1 2-2 2-3 2-1 3-2 4-4-1 0-1-1-2-1v-1-1h-2 0l1-2c1-1 2-4 2-6 1-1 3-2 4-2 1-1 2-1 3-1z" class="Z"></path><defs><linearGradient id="BJ" x1="369.239" y1="270.222" x2="355.171" y2="269.607" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#9f9f9f"></stop></linearGradient></defs><path fill="url(#BJ)" d="M367 262l-1 2h0 2v1 1c1 0 1 1 2 1-1 2-2 3-4 4 0 1-1 2-2 3-1 2-2 2-3 3-3 2-5 3-6 6l-1-1h0l1-2c1-1 2-1 3-2 0-1 1-2 2-3v-1c-1 1-3 1-4 2l-1-1c-1-1 0-1-1-2 0-1 2-2 3-2 2-1 4-3 5-4 0-1 1-1 1-1l4-4z"></path><path d="M360 274c1-1 2-1 3-2v1c0 1-1 1-3 2v-1z" class="E"></path><path d="M367 262l-1 2h0 2v1c-2 1-3 1-5 1l4-4z" class="c"></path><defs><linearGradient id="BK" x1="371.039" y1="255.845" x2="351.028" y2="268.421" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#aaa8a9"></stop></linearGradient></defs><path fill="url(#BK)" d="M368 252l1 1h0 0l2 1c1-1 3-1 5-1-1 0-2 0-3 1-1 0-3 1-4 2 0 2-1 5-2 6l-4 4s-1 0-1 1c-1 1-3 3-5 4v-3h0-2c-1 1-2 2-3 2h-1c0-1-1-2 0-3l2-3h0c0-1 1-1 1-2 1-1 3-2 5-3 0 0 1-1 1-2 1 0 3-1 4-2h0s1 0 2-1c1 0 2-1 2-2z"></path><path d="M353 264l2 1s0 1-1 1c0 1-2 1-2 2-1 0-1 0-1-1l2-3z" class="B"></path><defs><linearGradient id="BL" x1="377.926" y1="271.956" x2="365.784" y2="270.093" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#BL)" d="M371 280c1 0 2-1 2-2v-4s0-1-1-2l-2 1h0v-1h-1l2-2c1-1 2-2 1-4 0-1 0-1-1-2v-3h-1c1-1 1-1 1-2 1 0 1-1 1-1 2-2 3-2 5-2v3 30c-2-1-5-4-7-3h0c-1-1-1-1-2-1h-2v1h0l1 1 1 1c-1 0-2-1-3-1s-1-1-2-2c0 0 1-1 2-1l-2-4c1-1 1-1 2-1h1 2c1 0 1-1 2 0 1 0 1 1 1 1z"></path><path d="M365 279h1 2c1 0 1-1 2 0 1 0 1 1 1 1-2 2-4 2-6 4h0l-2-4c1-1 1-1 2-1z" class="b"></path><path d="M127 227h0l2 3v1-2c2 1 3 2 4 3l1-1 3 3 2 2h0c1 1 1 1 1 2 1 1 1 1 2 1v2c-1 0-1 0-1 1h-1c-1 2-1 4-1 6v5h-3v6l-1 2-1 1c0 2-1 3 0 4l-2 1h-1c-1 0-2 0-4-1l-1 1c-2 0-2-2-3-2l-2-1-1-1v-1c0-1 1-3 0-4h-3l-5 8c-1 2-1 3-2 5v2 1 2h0c0 1 0 2-1 2v2 2c-1 0-1 0-1 1l-1 1v2l-2 3-3 4c-1 2-2 4-2 7v4c1 1 0 2 0 3v1l2 1c0 1-1 2-2 3s-1 3-2 4c1 10 4 16 12 23v1h0l-2-1-1 1v1c-1 1-2 1-3 1s-2 1-3 1-3 0-4 1h2v1c4 0 8 2 11 3 3 0 6 2 8 2l4 3 9 10h0c2 2 5 3 7 5l6 7c3 3 7 9 10 9h0 1c0-1 1-2 2-3v1l1 1h0c1 0 2 1 2 2-3 5-6 10-11 14h0c-6 6-15 10-23 11-3 1-6 0-9 1h-1c-1-1-1-1-2-1-1 1-1 2-2 3h1 1 2c0 1 1 1 1 1v-1h1l1-1 2 1c2-2 6 0 9-1 2-1 5-2 8-2l-1 2h-1l2 2v1l-4 5-3 3-2 1h-1c-1-1-3-2-3-4l-1 2-1 1h0l-2-1h-4-3l2 1h0c1 1 2 2 3 2l-1 1c1 0 2 1 3 2h-1v1c-1 1-1 2-1 2l-1 1-1 2-3 5c0 1 0 1-1 2l-9-5h-3c-1-1-3-1-4-2l-5-1-4-1c-6-2-13-2-19-2h0v-2-2l-1 1c-1-1-1-3-1-4l-2-6-2-10v-4c1-4 6-10 8-12 2-1 3-2 4-3v-1h2 0l-1-8v-11l1-13v-5c1-1 1-4 1-5l2-15c4-18 10-35 18-51 1-1 2-4 3-5l8-14v-3-1h0c1-1 1-1 0-1v-1l2-4h0c1-4 5-8 7-11l4-6h1 0 1c0-1 1-1 2-2 0-1 0-3 1-4z" class="C"></path><path d="M121 417h1c2 1 2 2 3 3l-1 2-1 1h0c-1-1 0-2-1-3-2-1-5-2-7-2 2-1 4-1 6-1z" class="M"></path><path d="M80 404c1 3 1 6 2 8l-2 2c0 2-1 3-2 4-1 0-1-1-1-2-1-1 0-2 1-4h0c2-3 2-5 2-8z" class="b"></path><path d="M100 300v4c1 1 0 2 0 3v1l2 1c0 1-1 2-2 3s-1 3-2 4c-1-6 0-11 2-16z" class="B"></path><path d="M78 391l2 13c0 3 0 5-2 8l-1-1v1l-1-1c0 1-1 1-2 1v-1l2-2h0c1-2 2-3 2-5 0-3-1-5-1-8 0-2 0-3 1-5z" class="N"></path><path d="M78 404c1 2 0 5-1 7v1l-1-1c0 1-1 1-2 1v-1l2-2h0c1-2 2-3 2-5z" class="Q"></path><path d="M78 418c1-1 2-2 2-4l2-2c0 1 1 2 2 2 0 1 2 1 2 1 10 2 19 4 28 7l2 1h0c1 1 2 2 3 2l-1 1c-3-2-5-2-7-2l-6-3c-2 0-3 0-5-1l-2-1-12-2c-2 0-2 0-4 1h-1 0c-1 1-1 1-1 2-1 1-2 2-4 2l2-4z" class="a"></path><path d="M78 418c1-1 2-2 2-4l2-2c0 1 1 2 2 2 0 1 2 1 2 1-1 0-3 0-4 1l-1 2h0c-1 1-1 1-1 2-1 1-2 2-4 2l2-4z" class="F"></path><defs><linearGradient id="BM" x1="129.814" y1="236.98" x2="107.017" y2="248.809" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#BM)" d="M127 227h0l2 3h-1 0v2c0 1-1 3-2 4-1 0-1 1-2 2s-2 2-2 3l-3 3-2 3-6 9c0 1-1 4-2 4v-3-1h0c1-1 1-1 0-1v-1l2-4h0c1-4 5-8 7-11l4-6h1 0 1c0-1 1-1 2-2 0-1 0-3 1-4z"></path><path d="M111 250l1-1c2-1 3-3 5-4-2 2-7 8-6 11h0c0 1-1 4-2 4v-3-1h0c1-1 1-1 0-1v-1l2-4h0z" class="Z"></path><path d="M77 411l1 1h0c-1 2-2 3-1 4 0 1 0 2 1 2l-2 4c2 0 3-1 4-2 0-1 0-1 1-2h0 1v3h0c0 2-1 4-1 6l-1 1c-2 1-1 1-2 2h-2-7v-2-2l-1 1c-1-1-1-3-1-4l-2-6 1-1 1 1v-1l3-1c0-1 0-1 1-2h0l-1-1c1 0 2 0 4-1v1c1 0 2 0 2-1l1 1v-1z" class="h"></path><path d="M67 416l3-1-1 3h-1l-1-1v-1z" class="S"></path><path d="M74 411v1c1 0 2 0 2-1l1 1c-3 4-6 9-8 14l-1 1c-1-1-1-3-1-4l1 1h0l1-1c1-1 1-2 1-3s1-2 2-2v-1c1-1 2-3 2-4h-3 0l-1-1c1 0 2 0 4-1z" class="C"></path><defs><linearGradient id="BN" x1="77.033" y1="419.746" x2="71.127" y2="419.361" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#868486"></stop></linearGradient></defs><path fill="url(#BN)" d="M77 411l1 1h0c-1 2-2 3-1 4 0 1 0 2 1 2l-2 4v3l-2-1v-1h-1v1c-1 2-3 3-4 4v-2c2-5 5-10 8-14v-1z"></path><path d="M76 422c2 0 3-1 4-2 0-1 0-1 1-2h0 1v3h0c0 2-1 4-1 6l-1 1c-2 1-1 1-2 2h-2-7v-2c1-1 3-2 4-4v-1h1v1l2 1v-3z" class="D"></path><path d="M74 424l2 1h0l2 2v1c-2 0-3 0-5-1 0-1 1-2 1-3z" class="I"></path><path d="M76 422c2 0 3-1 4-2 0-1 0-1 1-2h0 1v3h0c0 2-1 4-1 6-1-1-1-2-1-3v-2c-2 0-1 1-2 2h-2v1h0v-3z" class="B"></path><path d="M82 418c2-1 2-1 4-1l12 2 2 1c-1 3-3 6-4 8 0 1-1 3-2 4h-1c0 1 0 1-1 1l-4-1c-6-2-13-2-19-2h0 7 2c1-1 0-1 2-2l1-1c0-2 1-4 1-6h0v-3z" class="M"></path><path d="M96 428c-1-1 1-6 1-8l1-1 2 1c-1 3-3 6-4 8z" class="P"></path><path d="M83 421c0-1 0-2 1-3h0 2 1 0c1 1 2 1 2 2 0 2-1 3-2 3-1 1-2 1-3 1-1-1-2-2-2-3h0 1z" class="h"></path><path d="M83 421c0-1 0-2 1-3h0 2 1c0 2 0 2-1 3s-2 1-3 0z" class="Y"></path><path d="M80 428l1 1c1-1 2-1 3-2 1 0 1 1 2 1l8-6v5c0 2 0 4-1 5 0 1 0 1-1 1l-4-1c-6-2-13-2-19-2h0 7 2c1-1 0-1 2-2z" class="O"></path><defs><linearGradient id="BO" x1="63.449" y1="405.055" x2="74.533" y2="403.003" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#535252"></stop></linearGradient></defs><path fill="url(#BO)" d="M77 387l1 3v1c-1 2-1 3-1 5 0 3 1 5 1 8 0 2-1 3-2 5h0l-2 2c-2 1-3 1-4 1l1 1h0c-1 1-1 1-1 2l-3 1v1l-1-1-1 1-2-10v-4c1-4 6-10 8-12 2-1 3-2 4-3v-1h2 0z"></path><path d="M67 416c0-2 0-3-1-4v-2l1-1 3 3 1 1h0c-1 1-1 1-1 2l-3 1z" class="W"></path><path d="M75 394l-3 1v-1l2-3c1-1 1-2 2-2l1 1h1v1c-1 2-1 3-1 5h-1v-3h0l-1 1z" class="J"></path><path d="M75 395v2c-1 1-2 3-3 5 0 0-1 1-1 2v2h2c0 1 1 2 1 2h1l1 1-2 2c-2 1-3 1-4 1l-3-3v-1-3l3-3 5-7z" class="d"></path><path d="M71 406h2c0 1 1 2 1 2l-1 2h-3c0-1-1-1-1-3l2-1z" class="B"></path><path d="M75 394l1-1h0v3h1c0 3 1 5 1 8 0 2-1 3-2 5h0l-1-1h-1s-1-1-1-2h-2v-2c0-1 1-2 1-2 1-2 2-4 3-5v-2-1z" class="W"></path><path d="M100 420c2 1 3 1 5 1l6 3c2 0 4 0 7 2 1 0 2 1 3 2h-1v1c-1 1-1 2-1 2l-1 1-1 2-3 5c0 1 0 1-1 2l-9-5h-3c-1-1-3-1-4-2l-5-1c1 0 1 0 1-1h1c1-1 2-3 2-4 1-2 3-5 4-8z" class="O"></path><path d="M107 429c1-1 2-3 4-3h0l-2 3v-1l-2 1z" class="G"></path><path d="M107 429l2-1v1c-2 2-4 5-5 7h-3l6-7z" class="H"></path><path d="M100 420c2 1 3 1 5 1-1 1-1 2-2 3h0c-2 3-3 6-5 9-1 0-1 1-1 1l-5-1c1 0 1 0 1-1h1c1-1 2-3 2-4 1-2 3-5 4-8z" class="B"></path><path d="M88 319h0c1-1 1-3 2-5s2-2 2-4v-1h0v-1c1-1 1-1 1-2h0v-1l1-1c0 1-1 2 0 3v-1l1-1v-2l1 1c-1 3-2 7-2 10v1h1c0 3 1 5 2 8 0 3 2 5 3 8l3 6h1c1 1 1 1 2 1l2 1-1 1v1c-1 1-2 1-3 1s-2 1-3 1-3 0-4 1h2v1c-2 1-3 3-4 6v1l-1 1-1-1h0c-1 0-1-1-2-1 0-1 0-1 1-1v-2-2c1-1 1-2 1-3h-1-1c-1 0-1 1-2 2h-1c0-1 0-1 1-2l-2-1-1 1h0l-1-2c-1-3 0-6 1-9 0-3 0-5 1-7s1-4 1-6z" class="P"></path><path d="M94 326c2 2 3 4 4 6 1 1 1 3 2 3 0 1 3 2 3 2h1c-1 1-2 1-3 1-3 1-6 1-8 2-1 0-2 1-3 2h-2v-1-4h1v-2 3l1 1h3c2-1 3-5 3-7s-2-4-2-6z" class="T"></path><path d="M100 339c3 0 5 0 7 1v1c-1 1-2 1-3 1s-2 1-3 1-3 0-4 1h2v1c-2 1-3 3-4 6v1l-1 1-1-1h0c-1 0-1-1-2-1 0-1 0-1 1-1v-2-2c1-1 1-2 1-3 2-2 4-3 7-4z" class="L"></path><path d="M93 343c2-2 4-3 7-4 0 1-1 2-1 3-3 1-5 2-7 4 1-1 1-2 1-3z" class="V"></path><path d="M97 344h2v1c-2 1-3 3-4 6v1l-1 1-1-1h0c-1 0-1-1-2-1 0-1 0-1 1-1 2-2 3-4 5-6z" class="U"></path><defs><linearGradient id="BP" x1="86.922" y1="328.567" x2="95.048" y2="329.725" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#BP)" d="M88 341c-1-1-1-2-1-2h0v1h-1v-1-1c0-3 0-5 1-7l5-13 1 2 1 6c0 2 2 4 2 6s-1 6-3 7h-3l-1-1v-3 2h-1v4z"></path><path d="M93 320l1 6c0 2 2 4 2 6s-1 6-3 7h-3l1-1c1-1 4-3 4-5h0v-1c-1-1 0-3-1-4-1 0-1 0-1-1h0c0-1 0-1-1-2h0c1-1 0-2 0-3s0-1 1-2z" class="N"></path><defs><linearGradient id="BQ" x1="115.431" y1="239.281" x2="126.884" y2="263.685" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#BQ)" d="M129 231v-2c2 1 3 2 4 3l1-1 3 3 2 2h0c1 1 1 1 1 2 1 1 1 1 2 1v2c-1 0-1 0-1 1h-1c-1 2-1 4-1 6v5h-3v6l-1 2-1 1c0 2-1 3 0 4l-2 1h-1c-1 0-2 0-4-1l-1 1c-2 0-2-2-3-2l-2-1-1-1v-1c0-1 1-3 0-4h-3l-5 8c-1 2-1 3-2 5-2 1-3 4-4 6 0-1 3-5 3-7 1-1 1-3 2-4-1-1 2-5 2-6s0-1 1-2l1-1v-1l1-1v-1l1-1-1-1c1 0 1-1 1-2 1 0 1-1 1-1l1-2 1-1c0-1 2-1 2-2 1-1 1-2 2-3v-1h1c0-1 1-1 1-2l1-3c1 0 1-1 1-2s0-1 1-1c-1-1 0-1-1-1v-1h1v1z"></path><path d="M128 242l1-3h1v1l1 1c1 1 2 3 2 5-1-1-3-3-5-4z" class="C"></path><path d="M128 242c2 1 4 3 5 4 2 3 1 7 1 11h0c0-2 0-6-1-8h-2 0c-1-1-1-1-1-2l-1-1c-1 1-1 3-2 4-2 3-4 9-5 13h-1l-1-1c0-1 1-3 0-4h-3c0-1 1-2 2-3 3-4 6-8 9-13z" class="K"></path><path d="M129 231v-2c2 1 3 2 4 3l1-1 3 3 2 2h0c1 1 1 1 1 2 1 1 1 1 2 1v2c-1 0-1 0-1 1h-1c-1 2-1 4-1 6v5h-3v-4c-1-6-4-13-7-18z" class="X"></path><path d="M136 249c1 1 1 2 3 2v-2-1 5h-3v-4z" class="b"></path><path d="M136 238c2 1 2 1 3 2 0 2 0 2-1 3h-1c-1-2-1-3-1-5z" class="S"></path><path d="M137 234l2 2h0c1 1 1 1 1 2 1 1 1 1 2 1v2c-1 0-1 0-1 1h-1c0-1 0-1-1-2s-1-1-3-2h0c-1 0-1 0-1-1h-1l-1-2h1 2l1-1z" class="F"></path><path d="M120 262l1 1h1c1-4 3-10 5-13 1-1 1-3 2-4l1 1c0 1 0 1 1 2h0 2c1 2 1 6 1 8v4h1l-1 1c0 2-1 3 0 4l-2 1h-1c-1 0-2 0-4-1l-1 1c-2 0-2-2-3-2l-2-1-1-1v-1z" class="e"></path><path d="M130 255c0-1 0-2 1-3 0-1 0-1 1-1h0v2c0 1 0 1-1 2h-1 0z" class="X"></path><path d="M130 255h0 1c1-1 1-1 1-2 0 1 1 3 0 4h-2c0 1-1 1-1 2h0l-1-1c0-1 1-2 2-3zm-3-3l1 1h1c-1 2-2 4-4 6h-1 0l3-7z" class="b"></path><path d="M129 259c0-1 1-1 1-2h2l1 1v3l-1 1v1c1 0 1 0 2-1 0 2-1 3 0 4l-2 1-3-3h-2c0-1 0-2 1-3l1-2h0z" class="N"></path><path d="M129 259l1-1c1 0 1 0 1 1s1 1 1 2c-1 1-2 0-3 1l-1-1 1-2h0z" class="F"></path><defs><linearGradient id="BR" x1="84.096" y1="411.948" x2="123.872" y2="359.967" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#514f50"></stop></linearGradient></defs><path fill="url(#BR)" d="M79 383h-1v-16l1-1v-1-1c1-2 0-3 1-5 0-2 0-7 1-9h1v-1l1-1v-2c0-4 0-8 1-12 0-6 2-11 3-16h1 0v1c0 2 0 4-1 6s-1 4-1 7c-1 3-2 6-1 9l1 2h0l1-1 2 1c-1 1-1 1-1 2h1c1-1 1-2 2-2h1 1c0 1 0 2-1 3v2 2c-1 0-1 0-1 1 1 0 1 1 2 1h0l1 1 1-1v-1c1-3 2-5 4-6 4 0 8 2 11 3 3 0 6 2 8 2l4 3 9 10-1 1-2-1v2l-3-1c-8-1-18-1-25 4-3 2-5 5-7 8 1 1 1 2 2 2h1c-1 1-1 2-1 3v2 1h2v10h0c1 3 3 6 4 8h1l3 3h1l2 1c-1 1-1 2-2 2v1l8 1c-1 1-1 2-2 3h1 1 2c0 1 1 1 1 1v-1h1l1-1 2 1c2-2 6 0 9-1 2-1 5-2 8-2l-1 2h-1l2 2v1l-4 5-3 3-2 1h-1c-1-1-3-2-3-4-1-1-1-2-3-3h-1-5c-4-1-10 1-14-1h-1v1h-2c-1-1-2-1-2-1h-1-1c-1 0-3-1-4-1h-1c-1-1-2-1-3-1h0c2 0 3 0 4-1-2 0-4 0-6-1h-1c0-1-1-2-1-3l-1-5-3-12v-9h0z"></path><path d="M134 420v-3c1-1 2-1 4-2l-4 5z" class="I"></path><path d="M122 417c1 1 3 1 4 2 2 2 1 4 5 4l-2 1h-1c-1-1-3-2-3-4-1-1-1-2-3-3z" class="P"></path><path d="M89 345c1-1 1-2 2-2h1l-1 2c-2 3-2 6-2 9l2 4c1 2 2 3 4 3l1 1h-2 0c-1-1-1 0-1-1-2-1-3-2-3-3l-1-2v2c-1-2-1-6-1-8s0-3 1-5h0z" class="C"></path><path d="M121 413c2-2 6 0 9-1 2-1 5-2 8-2l-1 2h-1c-6 1-13 4-18 2l1-1h1 0 1z" class="G"></path><path d="M92 343h1c0 1 0 2-1 3v2 2c-1 0-1 0-1 1 1 0 1 1 2 1h0l1 1 1-1v5l2 2v1c-1 1-1 1-2 1-2 0-3-1-4-3l-2-4c0-3 0-6 2-9l1-2z" class="F"></path><path d="M89 354l2-1c0 1 1 2 1 3l-1 1v1l-2-4z" class="N"></path><path d="M95 352v5c-1 0-2-1-2-2v-3l1 1 1-1z" class="R"></path><path d="M89 354c0-3 0-6 2-9v8l-2 1z" class="X"></path><path d="M110 353c2-1 3-1 4-1l1 1h1 1c2 1 3 2 5 4l1 2h-1c1 1 1 1 1 2v1h-2l-2-2c-1 0-2 2-4 2l-10-1h2c2-1 7-1 8-3 1-1 0-1 0-2h-1l-2-2h-3l1-1z" class="b"></path><path d="M110 353h1c1 1 1 1 3 1 1 0 2 1 3 1v1c0 1 0 1 1 2h2c1 0 1 2 2 2l-1 1-2-2c-1 0-1 1-2 1-1 1-3 0-5 0-1 0-3 1-5 1 2-1 7-1 8-3 1-1 0-1 0-2h-1l-2-2h-3l1-1z" class="T"></path><defs><linearGradient id="BS" x1="102.431" y1="406.437" x2="108.167" y2="415.796" xlink:href="#B"><stop offset="0" stop-color="#888688"></stop><stop offset="1" stop-color="#aaa"></stop></linearGradient></defs><path fill="url(#BS)" d="M92 406l1-1 1 1h0c1 1 2 2 4 2h0c1 0 1 0 2-1l1 2h1c1 0 1-1 2-1l2 1 8 1c-1 1-1 2-2 3h1 1 2c0 1 1 1 1 1v-1h1l1-1 2 1h-1 0-1l-1 1h0c-5 0-10 0-14-1-5-1-9-3-12-7z"></path><path d="M102 409c1 0 1-1 2-1l2 1 8 1c-1 1-1 2-2 3-2-2-4-1-6-2-2 0-3-1-4-2z" class="Y"></path><path d="M103 360l1-1 1-1c-1-1-2-2-3-2 0-1-1-1-1-1v-2c0-1 1-1 2-2 1 0 1 1 2 1 0 1 0 1 1 1l2 1h1 3l2 2h1c0 1 1 1 0 2-1 2-6 2-8 3h-2c-1 1-3 1-5 1h-4l-1-1c1 0 1 0 2-1v-1l2 1v1h2c1 0 2 0 2-1z" class="N"></path><path d="M108 354h1 3l-1 1c-1 0-3 1-4 2h0l-1-1-1-1v-1h3z" class="F"></path><path d="M112 354l2 2v1c-2 2-5 2-8 2v-1l1-1h0c1-1 3-2 4-2l1-1z" class="H"></path><path d="M111 355v1c-1 1-1 1-2 1h-2c1-1 3-2 4-2z" class="B"></path><path d="M83 375l2-1c1 2-1 5-1 7 0 0 1 1 1 2h0v6c0 4 0 9 1 13h0c2 3 4 6 6 8 0 0 1 1 1 2 1 0 0 0 0 1h-2c-2 0-4 0-6-1h-1c0-1-1-2-1-3l-1-5-3-12v-9h0 0v-8h0v9 1h1c0 2-1 4 0 6v-5h0v-7s0-1 1-1c0-1 1-2 2-3z" class="K"></path><path d="M83 375h1c0 2 0 5-1 7 0 1-1 2-1 3 0 2 2 4 2 6s-1 4-1 5v-1c0-1 0-1-1-2h0c0-1 1-1 0-2 0-1 1-2 0-4-1 3 0 6-1 8v1c-1-3 0-7 0-10h-1 0v-7s0-1 1-1c0-1 1-2 2-3zm16-30c4 0 8 2 11 3 3 0 6 2 8 2l4 3 9 10-1 1-2-1c-1-1-2-2-4-3l-2-3c-2-2-3-3-5-4h-1-1l-1-1c-1 0-2 0-4 1l-1 1h-1l-2-1c-1 0-1 0-1-1-1 0-1-1-2-1-1 1-2 1-2 2v2s1 0 1 1c1 0 2 1 3 2l-1 1-1 1c0 1-1 1-2 1h-2v-1l-2-1-2-2v-5-1c1-3 2-5 4-6z" class="P"></path><path d="M95 351h1c1 0 1 0 1-1s1-1 2-2h0c-1 2-3 4-2 7 1 2 2 3 3 5h-1l-2-1-2-2v-5-1z" class="S"></path><path d="M99 345c4 0 8 2 11 3h0c-1 0-1-1-2 0 1 0 2 0 3 1h-3c-3-1-6-2-9-1h0c-1 1-2 1-2 2s0 1-1 1h-1c1-3 2-5 4-6z" class="d"></path><path d="M103 360v-1c0-1-1-1-2-1v-1l-1-1v-3h-2v-1c0-1 1-2 2-3h4l2 1c1 0 4 0 6 1h2c1 0 2 1 3 2h0-1-1l-1-1c-1 0-2 0-4 1l-1 1h-1l-2-1c-1 0-1 0-1-1-1 0-1-1-2-1-1 1-2 1-2 2v2s1 0 1 1c1 0 2 1 3 2l-1 1-1 1z" class="J"></path><path d="M108 349h3c-1-1-2-1-3-1 1-1 1 0 2 0h0c3 0 6 2 8 2l4 3 9 10-1 1-2-1c-1-1-2-2-4-3l-2-3c-2-2-3-3-5-4h0c-1-1-2-2-3-2-2-1-4-1-6-2z" class="S"></path><path d="M92 378l1-2c1 1 1 2 2 2h1c-1 1-1 2-1 3v2 1h2v10h0c1 3 3 6 4 8h1l3 3h1l2 1c-1 1-1 2-2 2v1l-2-1c-1 0-1 1-2 1h-1l-1-2c-1 1-1 1-2 1h0c-2 0-3-1-4-2h0l-1-1-1 1-3-6c-2-5-2-11 0-16 0-1 1-2 1-3l2-3z" class="H"></path><path d="M106 405l2 1c-1 1-1 2-2 2v1l-2-1c-1 0-1 1-2 1h-1c1-2 2-1 3-1 0 0 1-2 2-2v-1zm-16-24l2-3c1 0 1 1 2 1 0 1 0 1-1 2v2 1c-1 0-1 0-2-1h0c0-1-1-1-1-2z" class="G"></path><path d="M101 402h1l3 3h-1v1c-1 1-1 1-2 1-1-1-2-1-3-3l2-2z" class="E"></path><path d="M90 381c0 1 1 1 1 2h0c1 1 1 1 2 1v1c-1 1-2 2-2 3v5l1 1v1h-2v-1c1-2 1-3 0-5v-4l1-1h-1l-1 1v-1c0-1 1-2 1-3z" class="U"></path><path d="M89 400c-2-5-2-11 0-16v1l1-1h1l-1 1v4c1 2 1 3 0 5v1h2v1h-1c-1 0-1 0-2 1v2 1z" class="F"></path><defs><linearGradient id="BT" x1="89.573" y1="402.051" x2="99.16" y2="402.089" xlink:href="#B"><stop offset="0" stop-color="#696969"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#BT)" d="M89 400v-1-2c1-1 1-1 2-1h1c1 2 1 4 2 6 2 2 4 3 6 5-1 1-1 1-2 1h0c-2 0-3-1-4-2h0l-1-1-1 1-3-6z"></path><defs><linearGradient id="BU" x1="93.11" y1="384.89" x2="99.342" y2="402.472" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#797979"></stop></linearGradient></defs><path fill="url(#BU)" d="M95 383v1h2v10h0c1 3 3 6 4 8l-2 2c-2-1-4-4-5-7-1-4-1-11 1-14z"></path><path d="M131 363h0c2 2 5 3 7 5l6 7c3 3 7 9 10 9h0 1c0-1 1-2 2-3v1l1 1h0c1 0 2 1 2 2-3 5-6 10-11 14h0c-6 6-15 10-23 11-3 1-6 0-9 1h-1c-1-1-1-1-2-1l-8-1v-1c1 0 1-1 2-2l-2-1h-1l-3-3h-1c-1-2-3-5-4-8h0v-10h-2v-1-2c0-1 0-2 1-3h-1c-1 0-1-1-2-2 2-3 4-6 7-8 7-5 17-5 25-4l3 1v-2l2 1 1-1z" class="K"></path><path d="M109 373v-1c2-1 5-1 7-1-1 0-2 0-2 2l1 1v1h0l-1-1v1-1l-1-1s-1-1-2-1l-1 1h-1z" class="W"></path><path d="M116 377h2c0 1 0 1-1 1-2 1-3 2-4 3l-1-1-1 1-1 1v-1c0-1 0-1-1-2 3-1 5-2 7-2z" class="G"></path><path d="M122 377c1-1 2-1 3-1s1 1 2 2h-1c-3 1-3 3-5 3 0-1 1-1 0-2h-1-1l-2-1c1 0 1 0 1-1h-2c1-1 2-1 3 0h0l1 1s1 0 2-1z" class="c"></path><path d="M105 385c1-3 2-4 4-6 1 1 1 1 1 2v1l1-1 1-1 1 1-1 3c-2 0-3 0-4 1l-1 1h0l-1 1h-1v-2z" class="L"></path><path d="M108 406c4 1 8 3 13 4h5c-3 1-6 0-9 1h-1c-1-1-1-1-2-1l-8-1v-1c1 0 1-1 2-2z" class="H"></path><path d="M122 377c-1-1-1-1-1-2h-2v-1c4-1 7-1 11 0l7 8c1 2 1 3 1 5v1h2-3c-1-1-2-1-3-2v1s0 1-1 1c-1 1-2 0-3 1h-2v1h-1l-1-1c-1-1-3-1-4 0h-2c-2 0-3-1-4-2v-3l1-1c1-1 2-2 4-2s2-2 5-3h1c-1-1-1-2-2-2s-2 0-3 1z" class="P"></path><path d="M128 389c1-1 1-1 0-2h0c2 0 3 0 5-1h1v1s0 1-1 1c-1 1-2 0-3 1h-2z" class="b"></path><path d="M121 381c2 0 2-2 5-3v1c-1 1-1 1-1 2h-2v1h0 1v1c-2 0-4 0-6 1l-1-1c1-1 2-2 4-2z" class="U"></path><path d="M122 377c-1-1-1-1-1-2h-2v-1c4-1 7-1 11 0l7 8c0 1 0 1 1 2l-1 1c-1 0-1 0-2-1l-2-1v1h-2-1c-1-1-3-1-4-1h-1-1v-1h-1 0v-1h2c0-1 0-1 1-2v-1h1c-1-1-1-2-2-2s-2 0-3 1z" class="J"></path><path d="M133 383h-2 0c1-1 3-1 4-2 1 1 1 2 1 3h0-1l-2-1z" class="N"></path><defs><linearGradient id="BV" x1="129.643" y1="379.035" x2="125.314" y2="381.339" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#8b8a8b"></stop></linearGradient></defs><path fill="url(#BV)" d="M127 378c2 0 3 0 4 1 0 1-1 2-1 3-1 1-5 0-6 0h-1 0v-1h2c0-1 0-1 1-2v-1h1z"></path><path d="M117 378l2 1h1 1c1 1 0 1 0 2-2 0-3 1-4 2l-1 1v3c1 1 2 2 4 2h2c-3 2-4 3-4 6v3h0 2v-1c1 2 2 2 3 2l-1 1 1 1c2 1 4 2 7 1-2 1-5 0-6 1h-4c-2 1-4 0-6 0 0 0-1 0-1-1-4-1-7-5-9-8s-2-4-1-7c0-1 0-1 1-1h0v1h0l1-2v2h1l1-1h0l1-1c1-1 2-1 4-1l1-3c1-1 2-2 4-3z" class="S"></path><path d="M114 396c1 1 2 1 3 2h1 0 2v-1c1 2 2 2 3 2l-1 1 1 1c2 1 4 2 7 1-2 1-5 0-6 1-5 0-9-1-13-4 1-1 1-1 2-1 0-1 1-1 1-2z" class="E"></path><path d="M120 397c1 2 2 2 3 2l-1 1 1 1h-1s-1-1-2-1c-2 0-3 0-5-2h0 2 1 0 2v-1z" class="O"></path><path d="M107 386h0l1-1c1-1 2-1 4-1-1 1-1 3-1 4l1 5c1 1 2 2 2 3s-1 1-1 2c-1 0-1 0-2 1-1 0-2-2-2-2-2-2-3-4-4-5 0-2-1-3-1-5h0l1-2v2h1l1-1z" class="H"></path><path d="M107 386h0l1-1c1-1 2-1 4-1-1 1-1 3-1 4v1c-2-1-2-1-3-1 0-1 0-1-1-2h0z" class="O"></path><path d="M105 392l1-1c1-1 1-1 0-2h1c2 2 3 3 4 5l1-1c1 1 2 2 2 3s-1 1-1 2c-1 0-1 0-2 1-1 0-2-2-2-2-2-2-3-4-4-5z" class="R"></path><path d="M112 393c1 1 2 2 2 3s-1 1-1 2c-1 0-1 0-2 1-1 0-2-2-2-2v-2h0 2v-1l1-1z" class="N"></path><defs><linearGradient id="BW" x1="111.912" y1="388.489" x2="119.815" y2="387.648" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#BW)" d="M117 378l2 1h1 1c1 1 0 1 0 2-2 0-3 1-4 2l-1 1v3c1 1 2 2 4 2h2c-3 2-4 3-4 6v3h-1c-1-1-2-1-3-2 0-1-1-2-2-3l-1-5c0-1 0-3 1-4l1-3c1-1 2-2 4-3z"></path><path d="M93 376c2-3 4-6 7-8 7-5 17-5 25-4 0 1-1 2-1 3h1v2c3 1 7 3 9 6h1c2 2 3 1 3 3 1 2 2 3 2 4 1 1 1 3 2 5-1 0-1 0-2 1h-2v-1c0-2 0-3-1-5l-7-8c-4-3-8-3-13-3h-1c-2 0-5 0-7 1v1c-2 1-3 2-5 3v1c0 2-1 3-2 4s-2 2-2 3c-1 2 0 5-1 7 0 3 2 8 4 11h0c0 1 0 1-1 0h-1c-1-2-3-5-4-8h0v-10h-2v-1-2c0-1 0-2 1-3h-1c-1 0-1-1-2-2z" class="S"></path><path d="M96 378h0v2l1 2v2h-2v-1-2c0-1 0-2 1-3z" class="I"></path><path d="M104 376v-1c0-1 0-2 1-2 1-2 3-2 4-2 3 0 5-1 8 0h-1c-2 0-5 0-7 1v1c-2 1-3 2-5 3z" class="C"></path><path d="M100 373v-1c1 0 1-1 1-1 1-1 1-1 1-2 3-2 9-3 13-4 1 0 3 0 3 1 1 1 0 1 1 2-6-1-13 0-18 4v1 3h0-1v-1-2z" class="M"></path><path d="M93 376c2-3 4-6 7-8 7-5 17-5 25-4 0 1-1 2-1 3h1v2l-6-1c-1-1 0-1-1-2 0-1-2-1-3-1-4 1-10 2-13 4 0 1 0 1-1 2 0 0 0 1-1 1v1c-1 2-3 3-4 5h0-1c-1 0-1-1-2-2z" class="a"></path><path d="M131 363h0c2 2 5 3 7 5l6 7c3 3 7 9 10 9h0 1c0-1 1-2 2-3v1l1 1h0c1 0 2 1 2 2-3 5-6 10-11 14h0-1c-1 1-1 1-2 1-3 0-5 3-8 3-2 0-4 1-6 1-2 1-4 1-6 1h-6c-2-1-4-1-6-2 2 0 4 1 6 0h4c1-1 4 0 6-1-3 1-5 0-7-1l-1-1 1-1c-1 0-2 0-3-2v1h-2 0v-3c0-3 1-4 4-6 1-1 3-1 4 0l1 1h1v-1h2c1-1 2 0 3-1 1 0 1-1 1-1v-1c1 1 2 1 3 2h3c1-1 1-1 2-1-1-2-1-4-2-5 0-1-1-2-2-4 0-2-1-1-3-3h-1c-2-3-6-5-9-6v-2h-1c0-1 1-2 1-3l3 1v-2l2 1 1-1z" class="Y"></path><path d="M134 375v-2l-3-3-4-3h0c2-1 2 0 4 0v2c2 1 4 0 5 2v1c-1 1-1 2-1 3h-1z" class="g"></path><path d="M123 401h4 0c4-1 8-1 11-3 0 0 1-1 2-1l1 1c-3 2-7 4-11 4-3 1-5 0-7-1z" class="B"></path><path d="M131 363h0c2 2 5 3 7 5l6 7v3c1 1 1 2 2 3 0 1 1 2 0 4 0-1 0-2-1-3h0c0-1-1-2-2-3-1-2-2-3-4-5-3-4-6-8-11-9v-2l2 1 1-1z" class="I"></path><path d="M134 386c1 1 2 1 3 2h3c1-1 1-1 2-1 0 0 1 0 0 1v2c-1 3-3 6-6 7s-6 2-8 2h-5c-1 0-2 0-3-2v1h-2 0v-3c0-3 1-4 4-6 1-1 3-1 4 0l1 1h1v-1h2c1-1 2 0 3-1 1 0 1-1 1-1v-1z" class="K"></path><path d="M129 390c1 1 2 1 3 2v1h-2-1l-1-1 1-2z" class="P"></path><path d="M121 393c0 2 0 3 1 4 2 2 3 2 6 2h-5c-1 0-2 0-3-2v-1c0-1 1-2 1-3z" class="J"></path><path d="M142 387s1 0 0 1v2l-1-1c-2 0-3 1-5 3h0v-1-1-1l1-1h3c1-1 1-1 2-1z" class="M"></path><path d="M134 386c1 1 2 1 3 2l-1 1v1 1 1c-1 1-3 1-4 1v-1c-1-1-2-1-3-2h-1v-1h2c1-1 2 0 3-1 1 0 1-1 1-1v-1z" class="E"></path><path d="M132 392c0-1 0-1 1-2l1 1h2v1c-1 1-3 1-4 1v-1z" class="V"></path><path d="M122 389c1-1 3-1 4 0l1 1c-2 0-4 1-6 3 0 1-1 2-1 3v1 1h-2 0v-3c0-3 1-4 4-6z" class="D"></path><defs><linearGradient id="BX" x1="127.734" y1="409.007" x2="151.615" y2="382.135" xlink:href="#B"><stop offset="0" stop-color="#373638"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#BX)" d="M144 375c3 3 7 9 10 9h0 1c0-1 1-2 2-3v1l1 1h0c1 0 2 1 2 2-3 5-6 10-11 14h0-1c-1 1-1 1-2 1-3 0-5 3-8 3-2 0-4 1-6 1-2 1-4 1-6 1h-6c-2-1-4-1-6-2 2 0 4 1 6 0h4c1-1 4 0 6-1 4 0 8-2 11-4 3-3 5-6 5-10-1-2-1-4-1-6 1 1 1 2 1 3 1-2 0-3 0-4-1-1-1-2-2-3v-3z"></path><path d="M145 382c1 1 1 2 1 3 2 1 4 2 6 2v1c-1 1-2 1-3 1l-2-2c-1 0-1 0-1 1-1-2-1-4-1-6z" class="T"></path><path d="M144 375c3 3 7 9 10 9h0 1 1v1l1 1h2c-1 1-2 1-3 1h-3-1c-2 0-4-1-6-2 1-2 0-3 0-4-1-1-1-2-2-3v-3z" class="C"></path><path d="M377 404v3l2 2v1l1 1h0l1 21v23l-1 29c0 1 0 2 1 2 2-1 3-2 5-4l1 1h0v36 10 1c-1 4 0 9-1 13h0c-2 2-4 3-5 6l-1 1v24 17 9l1 1c0 3 0 6 1 8h2l5-5c1 3 2 5 3 7 2 2 3 4 5 5v1l5 5 2 1 2 2c3 1 6 3 9 4h-3c-1 1-2 3-2 5-1 1-1 1-2 1l-4-2v1 3H294l1-1c1-1 4-3 6-3l5-3c1 0 1 1 2 1 2-1 4-2 7-3h4c0-1 1-2 2-2l1-2c1 0 1-1 2-1 1-1 3-1 4 0 1 0 2-1 2-2l8-4c0-1 3-3 3-3v-1c0-1 1-2 1-3h-1l-1-2-1 1-1-3 2-2 1-2 1-3v-1l1-3c0-1 0-2-1-3h0l1-1c0-1 1-2 1-3h-1c-1 1-1 1-2 1h0-3v-1l-2-1h0c-2 0-2 0-3 1v-1l2-1c1-2-1-3-2-5 0-2 1-3 0-5h0c0-1 0-2-1-2v-1s1-1 1-2l-2 2-1 1-2 1v-1-4c-1-6 0-12-1-18v-11l1-3c0-1 1-1 2-2h1l2 2v-1l2-1c1 0 1-1 2-2 1 0 2-1 2-2h0 2v-1h0c1-3 3-4 5-5-1-1-1-2-1-2-2-2-3-1-5-3l-1 1c-1 0-2 1-2 1h-1c-1 1-1 2-2 2 1-1 2-2 2-3h1l10-10 8-6 7-7h1c1 0 1-1 2-2h0c2-1 3-3 5-4l3-3 1-1 1-2 1 1 1-1v-13c-1-1 0-3 0-5l-1-10-1-1h1v-1c1-1 1-4 1-5v-11-37z" class="a"></path><path d="M306 630c1 0 1 1 2 1l-1 1h16v1h-9-13l5-3z" class="h"></path><defs><linearGradient id="BY" x1="310.441" y1="625.644" x2="332.491" y2="637.848" xlink:href="#B"><stop offset="0" stop-color="#131113"></stop><stop offset="1" stop-color="#303233"></stop></linearGradient></defs><path fill="url(#BY)" d="M319 628h0l-6 3h6 17 1v1c-4 0-9 0-13 1h-1v-1h-16l1-1c2-1 4-2 7-3h4z"></path><path d="M397 616v1l5 5 2 1c-2 2-3 5-5 6h-1l-6-6-2-1 1-1c2-1 3-3 5-4l1-1z" class="E"></path><path d="M397 616v1c-2 2-3 4-5 6l-2-1 1-1c2-1 3-3 5-4l1-1zm5 6l2 1c-2 2-3 5-5 6h-1c1-3 3-5 4-7z" class="N"></path><path d="M404 623l2 2c3 1 6 3 9 4h-3c-1 1-2 3-2 5-1 1-1 1-2 1l-4-2-2-2-3-2c2-1 3-4 5-6z" class="D"></path><path d="M404 623l2 2-1 1c0 1-1 1-1 2-1 1-1 2-2 3h0l-3-2c2-1 3-4 5-6z" class="E"></path><path d="M336 631c-2-1-6 0-7 0 1-3 3-5 5-8h1l-2 3c0 1 1 1 1 2l-1 1c1 1 1 1 2 1s1-1 2-1h1v-1h6l1 1h1l-1 1v1c2 1 3 0 5 0h7c-1 0-1 1 0 1 3 0 7 0 11 1h-7-13-24c4-1 9-1 13-1v-1h-1z" class="Q"></path><path d="M338 628h6l1 1c-1 0-2 1-3 2h-1c-1 0-2 0-3-1v-1-1z" class="J"></path><path d="M387 519v10 1c-1 4 0 9-1 13h0c-2 2-4 3-5 6l-1 1c0-8-1-16 1-23v3c-1 1-1 2 0 3l1-1c0-1 2-3 4-4v1c0-2-1-4 0-5 0-2 0-3 1-5z" class="D"></path><path d="M389 604c1 3 2 5 3 7 2 2 3 4 5 5l-1 1c-2 1-3 3-5 4l-1 1c-4-4-6-7-8-12v-1h2l5-5z" class="M"></path><path d="M391 612h0l1-1c2 2 3 4 5 5l-1 1-1-2c-3 0-4 1-6 3h-1c0-2 4-3 4-5l-1-1z" class="F"></path><defs><linearGradient id="BZ" x1="390.425" y1="608.48" x2="385.699" y2="613.401" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#BZ)" d="M389 604c1 3 2 5 3 7l-1 1h0c-1 1-3 3-5 3h0v-1c0-1 0-2-1-4-1-1-1 0-3 0v-1h2l5-5z"></path><path d="M384 609c1 0 2 0 3-1 1 1 1 3 1 4-1 1-1 2-2 2 0-1 0-2-1-4-1-1-1 0-3 0v-1h2z" class="U"></path><defs><linearGradient id="Ba" x1="321.242" y1="625.131" x2="331.829" y2="627.394" xlink:href="#B"><stop offset="0" stop-color="#2d2c2e"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#Ba)" d="M338 617v2l-2 1c2 0 4-1 5-2 0 1-1 1-2 2l-4 3h-1c-2 3-4 5-5 8 1 0 5-1 7 0h-17-6l6-3h0c0-1 1-2 2-2l1-2c1 0 1-1 2-1 1-1 3-1 4 0 1 0 2-1 2-2l8-4z"></path><path d="M338 617v2l-2 1-6 3c-1 2-3 4-5 4l-1 1v-1l-2 2c-1 0-2 1-3 2h-6l6-3h0c0-1 1-2 2-2l1-2c1 0 1-1 2-1 1-1 3-1 4 0 1 0 2-1 2-2l8-4z" class="S"></path><path d="M377 404v3l2 2v1l1 1h0l1 21v23l-1 29v4h0v-26-4-11c-1 0-2-1-2 0l-1 11c0 7 1 14 0 22v-6c-1-1 0-3 0-5l-1-10-1-1h1v-1c1-1 1-4 1-5v-11-37z" class="g"></path><path d="M375 487l1 1 1-1v29 13c0 2-1 5 0 7v1c-1 2 1 10-1 11h-2l-1-1c-1 0-3-1-4-1h-1c0-1 0-2 1-3 0 0 1 0 1-1l1-1h0c1-1 1-2 1-4h-1c0-3 1-6 0-9l-3 1c0-1 0-1-1-1h0 0c1-1 3-2 3-3 1 0 0-4 1-5h0v-5h-1c1-1 1-3 2-4 0-1 1-1 1-2v-1-1c-1-2-1-4-2-6 1-2 1-3 0-5l-1-1c-1 1-2 2-4 3l-1-1h0c2-1 3-3 5-4l3-3 1-1 1-2z" class="K"></path><path d="M375 487l1 1v17l-2-13v-3l1-2z" class="C"></path><path d="M374 489v3c-2 5 2 11 1 16-1 1 0 5 0 7v6 6l-1 11v6 4l-1-1h0 0l1-2c-1-3 0-5 0-8v-13-5-5c0-2-1-4-1-5v-1-1c-1-2-1-4-2-6 1-2 1-3 0-5l-1-1c-1 1-2 2-4 3l-1-1h0c2-1 3-3 5-4l3-3 1-1z" class="J"></path><path d="M370 493l1 1h1c2 2 1 6 2 9v4c0 1 0 0-1 1v-1c-1-2-1-4-2-6 1-2 1-3 0-5l-1-1c-1 1-2 2-4 3l-1-1h0c2-1 3-3 5-4z" class="T"></path><path d="M373 509c0 1 1 3 1 5v5 5 13c0 3-1 5 0 8l-1 2h0 0c-1 0-3-1-4-1h-1c0-1 0-2 1-3 0 0 1 0 1-1l1-1h0c1-1 1-2 1-4h-1c0-3 1-6 0-9l-3 1c0-1 0-1-1-1h0 0c1-1 3-2 3-3 1 0 0-4 1-5h0v-5h-1c1-1 1-3 2-4 0-1 1-1 1-2z" class="Z"></path><path d="M368 529c0-1 0-1-1-1h0 0c1-1 3-2 3-3 1 0 0-4 1-5h0v-5h-1c1-1 1-3 2-4v3c1 1 1 1 1 3l-1 1c0 3 1 5 0 7 0 1 1 3 1 4 0 2 0 5-1 8h-1c0-3 1-6 0-9l-3 1z" class="H"></path><path d="M345 598h0 0c1 2 1 3 1 4v2h2c1 1 0 4 1 5 1 0 2-1 2-2h3c0-1 1-1 1-1 2-1 2-1 2-2l2-2h0 1l1 4-1 1 1 2c-1 1-1 1-2 3 2 1 3 2 3 4v1c-1 1-3 2-4 3h-2l-3 2-5 1h-1c-1 0-2 0-3 1l-1 3h0l1 1h-6v1h-1c-1 0-1 1-2 1s-1 0-2-1l1-1c0-1-1-1-1-2l2-3 4-3c1-1 2-1 2-2-1 1-3 2-5 2l2-1v-2c0-1 3-3 3-3v-1c0-1 1-2 1-3h-1l-1-2-1 1-1-3 2-2 1-2 1-3v-1l1 1c0 1 0 1 1 2v-2c0-1 0-1 1-1z" class="e"></path><path d="M345 619v-1h0c1 0 1 0 2-1v-1l1-1v4h0-3z" class="X"></path><path d="M359 608h0c-1 0-1 0-2 1h0-1l1-1h0v-1l-2 1h0 0l1-1c1-1 2-1 3-1v2z" class="P"></path><path d="M345 619h3 0c-1 1-1 2-1 3v1h1-1-1l-1-1c0-2 1-1 0-3z" class="b"></path><path d="M359 606c1 0 1 0 1 1l1 2c-1 1-1 1-2 3h0v-3-1-2z" class="I"></path><path d="M338 628l1-1 2-2c1-1 2-1 3-1l-1 3h0l1 1h-6z" class="Q"></path><path d="M345 598h0c0 3 1 6 1 8h1v6s-1 1-1 2c0-2 0-4-1-6 0 0-1 0 0-1v-4c0-1 0-3-1-4h0c0-1 0-1 1-1z" class="K"></path><path d="M339 620l2 1s-1 1-1 2c-1 1-3 2-5 3v1c1 0 2 0 3 1l-1 1c-1 0-1 1-2 1s-1 0-2-1l1-1c0-1-1-1-1-2l2-3 4-3z" class="b"></path><path d="M351 614s1-1 2-1c2 0 3 0 4-1l1 1-2 2 1 1v-1h2c1 1 1 2 1 3-1 1-1 1-2 1s-1 1-2 1l-3 2c0-2 1-3 2-4h1v-2c-1 0-1 0-2 1-1 0-2 0-3 1h0l-1-1c1-1 2-2 4-3h0-3z" class="Z"></path><path d="M351 614h3 0c-2 1-3 2-4 3l1 1h0c1-1 2-1 3-1 1-1 1-1 2-1v2h-1c-1 1-2 2-2 4l-5 1h-1v-1c0-1 0-2 1-3h0 0v-4c1 0 2 0 3-1z" class="T"></path><path d="M342 598l1 1c0 1 0 1 1 2v-2h0c1 1 1 3 1 4v4c-1 1 0 1 0 1 1 2 1 4 1 6h-1c-1 1-1 2-1 3h-1c-1 0-1 0-2 1s-3 2-5 2l2-1v-2c0-1 3-3 3-3v-1c0-1 1-2 1-3h-1l-1-2-1 1-1-3 2-2 1-2 1-3v-1z" class="C"></path><path d="M342 598l1 1c0 1 0 1 1 2 0 5 1 9-3 13v-1c0-1 1-2 1-3h-1l-1-2-1 1-1-3 2-2 1-2 1-3v-1z" class="Z"></path><path d="M341 602c1 1 1 3 1 4 0 2-1 2-1 3v1l-1-2-1 1-1-3 2-2 1-2z" class="K"></path><defs><linearGradient id="Bb" x1="382.57" y1="621.944" x2="343.53" y2="620.579" xlink:href="#B"><stop offset="0" stop-color="#2d2c2c"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#Bb)" d="M366 613h0l-1-3 1-2c0 2 1 3 2 4h2c1 1 1 2 2 3 1 0 1 0 2 1 2 1 3 3 5 4s3 2 5 3l1 1v1l-2 2v3h1l1 1c0 1 0 0-1 1l-1 1h-15c-4-1-8-1-11-1-1 0-1-1 0-1h-7c-2 0-3 1-5 0v-1l1-1h-1l-1-1-1-1h0l1-3c1-1 2-1 3-1h1l5-1 3-2h2c1-1 3-2 4-3v-1c0-2-1-3-3-4 1-2 1-2 2-3 1 2 2 5 3 7 1 0 2 0 3-1l-1-2z"></path><path d="M365 616l2 2-1 1h-1c0-2-1-2 0-3z" class="d"></path><path d="M367 618l2 2v1c1 2 3 3 5 4l-1 1c-1 0-1-1-2-1h-1-4v-2-4l1-1z" class="W"></path><path d="M366 623c1 0 3-1 4 0l1 1-1 1h-4v-2z" class="b"></path><path d="M346 629l12-1 1 1-2 2h-7c-2 0-3 1-5 0v-1l1-1z" class="E"></path><path d="M366 613h0l-1-3 1-2c0 2 1 3 2 4h2c1 1 1 2 2 3 1 0 1 0 2 1 2 1 3 3 5 4s3 2 5 3l1 1v1l-2 2v3h1l1 1c0 1 0 0-1 1-4-2-7-4-10-7-2-1-4-2-5-4v-1l-2-2-2-2h-1c1 0 2 0 3-1l-1-2z" class="Q"></path><path d="M366 613h1c1 0 2 1 2 2l-1 1v2h1v2l-2-2-2-2h-1c1 0 2 0 3-1l-1-2z" class="e"></path><path d="M369 621c3 0 4 3 7 4 1 1 3 2 5 3l2 2h1l1 1c0 1 0 0-1 1-4-2-7-4-10-7-2-1-4-2-5-4z" class="J"></path><path d="M366 613h0l-1-3 1-2c0 2 1 3 2 4h2c1 1 1 2 2 3 1 0 1 0 2 1 2 1 3 3 5 4s3 2 5 3l1 1v1h0c-1-1-2-1-3-1-1 1-1 1-2 1h-2c-1-1-2-1-3-2l-4-5c-1-1-1-2-2-3h0c0-1-1-2-2-2h-1z" class="X"></path><path d="M372 615c1 0 1 0 2 1 2 1 3 3 5 4s3 2 5 3h-1c-1 0-2 1-2 0-5-1-7-5-9-8z" class="a"></path><defs><linearGradient id="Bc" x1="397.06" y1="631.498" x2="363.3" y2="558.302" xlink:href="#B"><stop offset="0" stop-color="#131314"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#Bc)" d="M367 546h1 1l2 2h0c2 1 4 2 5 4 1 1 1 2 1 4v8 28c0 4-1 9 0 13 1 1 2 3 3 4 1 4 4 8 6 11 2 1 5 4 5 6l1 1 7 5c0 1 0 1-1 1h-15l1-1c1-1 1 0 1-1l-1-1h-1v-3l2-2v-1l-1-1c-2-1-3-2-5-3s-3-3-5-4c-1-1-1-1-2-1-1-1-1-2-2-3h-2c-1-1-2-2-2-4l-1 2 1 3h0l1 2c-1 1-2 1-3 1-1-2-2-5-3-7l-1-2 1-1-1-4v-2-2l1-5h1v3c0-2 0-4 1-6 0-1 1-2 2-3 0-2 2-3 3-4v-4l2-1c1-1 1-2 2-3v-6c1-1 1-2 1-3-1-1-2-1-3-1l-1-1 2-4v-1l-2 1v-1c0-1-1-2-1-4l2-2c-1-1-2-1-3-1h-1 0l1-1s1-1 1-2h0-4l3-3z"></path><path d="M377 605c1 1 2 3 3 4 1 4 4 8 6 11 2 1 5 4 5 6-7-5-12-12-14-21z" class="O"></path><defs><linearGradient id="Bd" x1="390.336" y1="631.978" x2="385.322" y2="627.841" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#Bd)" d="M385 624l1 1c2 2 4 3 5 5l1 2c2 0 5 0 6 1h-15l1-1c1-1 1 0 1-1l-1-1h-1v-3l2-2v-1z"></path><path d="M385 624l1 1c0 1 1 2 1 2v1 1c-1 0-3 0-3 1h-1v-3l2-2v-1z" class="I"></path><path d="M375 570v-1-3l1-1c0 4 0 9-1 13v3c-1 3 0 6 0 9v1c0 1 0 2 1 3v1h0c-1 1-1 1-1 2 0 2 0 6-1 9h0v1h1v1h-1v2 2s0 1-1 1v1l1 2c-1-1-1-1-2-1-1-1-1-2-2-3h-2c-1-1-2-2-2-4l-1 2 1 3h0l1 2c-1 1-2 1-3 1-1-2-2-5-3-7l-1-2 1-1-1-4v-2-2l1-5h1v3c0-2 0-4 1-6 0 1 1 1 1 2 1 0 0 2 1 3 0 3 2 7 4 8l1 1c1 1 1 1 2 1l1-1s0-1 1-1v-11-17c1-2 1-3 1-5z" class="C"></path><path d="M364 604l1-1c3 2 5 4 8 5h0l-2 1c-2 0-4-2-6-3l-1-2z" class="a"></path><path d="M365 606c2 1 4 3 6 3v3c1 1 1 2 2 2l1 2c-1-1-1-1-2-1-1-1-1-2-2-3h-2c-1-1-2-2-2-4l-1-2z" class="F"></path><path d="M375 570v-1-3l1-1c0 4 0 9-1 13v3c-1 3 0 6 0 9v1c0 3 0 12-2 14-1 1-1 1-2 0-1 0-2-1-3-2-2-1-2-2-3-4-1-1-1-2-1-3h0v-4h-1v5l-1-1c0-2 0-4 1-6 0 1 1 1 1 2 1 0 0 2 1 3 0 3 2 7 4 8l1 1c1 1 1 1 2 1l1-1s0-1 1-1v-11-17c1-2 1-3 1-5z" class="I"></path><path d="M361 593h1v3l1 1c0 2 1 4 2 6l-1 1 1 2 1 2-1 2 1 3h0l1 2c-1 1-2 1-3 1-1-2-2-5-3-7l-1-2 1-1-1-4v-2-2l1-5z" class="N"></path><path d="M361 593h1v3l1 1c0 2 1 4 2 6l-1 1h0c-1-1-2-2-2-3-1 0-1 1-1 2v3h0l-1-4v-2-2l1-5z" class="B"></path><path d="M367 546h1 1l2 2h0c2 1 4 2 5 4h0l-1 1c-1 0-1 3-1 4 0 4 0 9 1 13 0 2 0 3-1 5v17 11c-1 0-1 1-1 1l-1 1c-1 0-1 0-2-1l-1-1c-2-1-4-5-4-8-1-1 0-3-1-3 0-1-1-1-1-2s1-2 2-3c0-2 2-3 3-4v-4l2-1c1-1 1-2 2-3v-6c1-1 1-2 1-3-1-1-2-1-3-1l-1-1 2-4v-1l-2 1v-1c0-1-1-2-1-4l2-2c-1-1-2-1-3-1h-1 0l1-1s1-1 1-2h0-4l3-3z" class="b"></path><path d="M365 587l1 1v4c0 3 2 6 2 9l1 2c-2-1-4-5-4-8-1-1 0-3-1-3 0-1-1-1-1-2s1-2 2-3z" class="e"></path><path d="M371 588h0v1l1 1-1 7c1 0 1 0 2 1-1 1-1 2-2 4l-1-1h-1v-2l1-1-1-1c-1-1-1-2-1-3-1-1-2-2-1-3 0-2 2-3 4-3z" class="c"></path><path d="M367 546h1 1l2 2-2 2 1 2v2h1c0 1 1 1 1 2v3c1 1-1 4 0 5 0 1 1 1 1 1 0 3 0 7-1 10v-6c1-1 1-2 1-3-1-1-2-1-3-1l-1-1 2-4v-1l-2 1v-1c0-1-1-2-1-4l2-2c-1-1-2-1-3-1h-1 0l1-1s1-1 1-2h0-4l3-3z" class="F"></path><path d="M371 548c2 1 4 2 5 4h0l-1 1c-1 0-1 3-1 4 0 4 0 9 1 13 0 2 0 3-1 5v17 11c-1 0-1 1-1 1l-1 1c-1 0-1 0-2-1h2c1-2 1-4 2-6 0-1-1-3-1-5v-4c0-2-1-4 0-6 0-3 0-6 1-10v-9-13c-2 0-3-1-4-2l1-1z" class="Q"></path><path d="M348 564c3-5 10-11 15-13 1 0 2 0 2-1h2v1l-1 1h0 1c1 0 2 0 3 1l-2 2c0 2 1 3 1 4v1l2-1v1l-2 4 1 1c1 0 2 0 3 1 0 1 0 2-1 3v6c-1 1-1 2-2 3l-2 1v4c-1 1-3 2-3 4-1 1-2 2-2 3-1 2-1 4-1 6v-3h-1l-1 5v2 2h-1 0l-2 2c0 1 0 1-2 2 0 0-1 0-1 1h-3c0 1-1 2-2 2-1-1 0-4-1-5h-2v-2c0-1 0-2-1-4h0 0c-1 0-1 0-1 1v2c-1-1-1-1-1-2l-1-1 1-3c0-1 0-2-1-3h0l1-1c0-1 1-2 1-3h-1c-1 1-1 1-2 1h0-3v-1l-2-1h0c-2 0-2 0-3 1v-1l2-1c1-2-1-3-2-5 0-2 1-3 0-5h0c0-1 0-2-1-2v-1s1-1 1-2c1-1 3-2 4-3h0c2-2 4-4 5-6v-1l4-2c-1 2-2 4-4 6h0c1-1 3-2 4-3-2 2-2 2-2 4l2-2h2z" class="V"></path><path d="M339 578v-1c1-1 3-2 4-4 2-2 6-2 7-5h2v1h-1l-1 1h2v-1h1c-1 1-1 1-2 1-3 1-6 5-9 7-1 0-1 0-2 1h-1l-1 1 1-1z" class="D"></path><path d="M368 555c0 2 1 3 1 4v1l2-1v1l-2 4 1 1c1 0 2 0 3 1 0 1 0 2-1 3v6c-1 1-1 2-2 3 1-2 0-3 1-5 0-2 1-3 0-6 0-3-3-1-5-2l1-1 1-1 1-1v-1l-1 1h-3v-1c0-2 0-2 2-4l1-2z" class="E"></path><defs><linearGradient id="Be" x1="359.724" y1="558.911" x2="354.63" y2="555" xlink:href="#B"><stop offset="0" stop-color="#a6a3a6"></stop><stop offset="1" stop-color="#bdbebd"></stop></linearGradient></defs><path fill="url(#Be)" d="M348 564c3-5 10-11 15-13 1 0 2 0 2-1h2v1l-1 1h0 1c1 0 2 0 3 1l-2 2-1 2v-2c1 0 1-1 1-1-2 0-4 2-6 2l4-3-6 3c-2 1-4 2-6 4-1 1-4 4-6 4h0z"></path><path d="M344 580c2-1 4-2 6-2 1 1 1 1 1 3v1h1c2 0 5-3 7-4h0c0 2-2 3-3 5 2-2 4-2 7-3v1l-1 1c-2 0-3 1-5 3-2 1-5 2-7 4h0c-1 2-1 3-2 4h0l-1 2 1-1h2c-1 1-2 1-4 2h0v-1c1-2 2-4 2-6v-4c0-1 0-1 1-2v-1h-1s-1 0-1 1h-1c-1-1 0-2-2-3z" class="f"></path><path d="M342 561l4-2c-1 2-2 4-4 6h0c1-1 3-2 4-3-2 2-2 2-2 4l-1 1c0 1-1 2-1 3h0v1c-1 1-2 1-2 3-1 0-1 1-1 1-1 1-1 2 0 3l-1 1 1-1 2 1c1 1 0 2 1 2v1l2-2c2 1 1 2 2 3h1c0-1 1-1 1-1h1v1c-1 1-1 1-1 2v4c0 2-1 4-2 6v-2c1-1 1-1 1-2v-3c-1-1-2 0-3 0h-1c-1 1-1 1-2 1h0-3v-1l-2-1h0c-2 0-2 0-3 1v-1l2-1c1-2-1-3-2-5 0-2 1-3 0-5h0c0-1 0-2-1-2v-1s1-1 1-2c1-1 3-2 4-3h0c2-2 4-4 5-6v-1z" class="M"></path><path d="M342 565c1-1 3-2 4-3-2 2-2 2-2 4l-1 1-2 2v-2c1-1 1-1 1-2h0z" class="B"></path><path d="M341 569l2-2c0 1-1 2-1 3h0v1c-1 1-2 1-2 3-1 0-1 1-1 1-1 1-1 2 0 3l-1 1 1-1 2 1c1 1 0 2 1 2v1l-2 1h-2v-2l-1-1c-1-1-1-2-1-3 1-1 2-2 2-4 0-1 2-3 3-4z" class="G"></path><path d="M342 561l4-2c-1 2-2 4-4 6h0 0c-3 2-3 3-4 6 0 1-1 2-2 2-1 3-1 5-1 8v1h1v1s1 0 1 1c1 0 1 0 2 2v1h1l3-3c1 1 0 1 0 2v2c-1 1-1 1-2 1h0-3v-1l-2-1h0c-2 0-2 0-3 1v-1l2-1c1-2-1-3-2-5 0-2 1-3 0-5h0c0-1 0-2-1-2v-1s1-1 1-2c1-1 3-2 4-3h0c2-2 4-4 5-6v-1z" class="F"></path><path d="M333 571c1-1 3-2 4-3 0 1 0 2-1 3 0 1-1 1-1 2s-1 1-2 2v1h0c0-1 0-2-1-2v-1s1-1 1-2z" class="X"></path><path d="M362 585c0-1 2-2 3-3l3-3v4c-1 1-3 2-3 4-1 1-2 2-2 3-1 2-1 4-1 6v-3h-1l-1 5v2 2h-1 0l-2 2c0 1 0 1-2 2 0 0-1 0-1 1h-3c0 1-1 2-2 2-1-1 0-4-1-5h-2v-2c0-1 0-2-1-4h0 0c-1 0-1 0-1 1v2c-1-1-1-1-1-2l-1-1 1-3c0-1 0-2-1-3h0l1-1c0-1 1-2 1-3 1 0 2-1 3 0v3c0 1 0 1-1 2v2 1h0c2-1 3-1 4-2l1-1c1-2 2-4 4-4 1-1 2-1 3-2s2-2 4-2z" class="N"></path><path d="M343 591h2v1c0 1-1 2-1 3h-1c0-1 0-2-1-3h0l1-1z" class="J"></path><path d="M343 595h1c1 1 1 1 1 3-1 0-1 0-1 1v2c-1-1-1-1-1-2l-1-1 1-3z" class="Q"></path><path d="M354 600l2-2h1v-1c1 0 2 0 3 1v2l-1-1c-1 0-1 1-2 1l-1-1-2 1z" class="F"></path><path d="M354 600l2-1 1 1c-1 1-3 4-5 5l-1-1c0-2 1-3 3-4h0zm8-15c0-1 2-2 3-3l3-3v4c-1 1-3 2-3 4-1 1-2 2-2 3-1 2-1 4-1 6v-3h-1-1c-2 1-4 4-7 4h0c-2 1-4 4-6 5v-1c1-1 1-1 1-2-1 0-1-1-2-2h-1v-3c1-1 1-2 2-3 0 1 0 1-1 2v2 1h0c2-1 3-1 4-2l1-1c1-2 2-4 4-4 1-1 2-1 3-2s2-2 4-2z" class="E"></path><path d="M351 593c1-2 2-4 4-4 1-1 2-1 3-2s2-2 4-2c-1 1-2 2-3 2v1h0c-3 1-5 3-8 5z" class="G"></path><path d="M366 498c2-1 3-2 4-3l1 1c1 2 1 3 0 5 1 2 1 4 2 6v1 1c0 1-1 1-1 2-1 1-1 3-2 4h1v5h0c-1 1 0 5-1 5 0 1-2 2-3 3h0 0c1 0 1 0 1 1l3-1c1 3 0 6 0 9h1c0 2 0 3-1 4h0l-1 1c0 1-1 1-1 1-1 1-1 2-1 3h-1l-3 3h4 0c0 1-1 2-1 2v-1h-2c0 1-1 1-2 1-5 2-12 8-15 13h-2l-2 2c0-2 0-2 2-4-1 1-3 2-4 3h0c2-2 3-4 4-6l-4 2v1c-1 2-3 4-5 6h0c-1 1-3 2-4 3l-2 2-1 1-2 1v-1-4c-1-6 0-12-1-18v-11l1-3c0-1 1-1 2-2h1l2 2v-1l2-1c1 0 1-1 2-2 1 0 2-1 2-2h0 2v-1h0c1-3 3-4 5-5-1-1-1-2-1-2-2-2-3-1-5-3l-1 1c-1 0-2 1-2 1h-1c-1 1-1 2-2 2 1-1 2-2 2-3h1l10-10 8-6 7-7h1c1 0 1-1 2-2l1 1z" class="M"></path><path d="M348 560l-1-2c1-1 3-2 4-3-1 1-1 2-2 3l-1 2z" class="B"></path><path d="M332 554v-2l1-6v-3c0-1 1-1 1-2 0 6-1 10 0 16h0c1 0 0 0 1 1h0c-1 1-1 2-1 3v2l1 1 4-1c-2 2-3 4-5 5h-1v-2c-1-2-1-4-1-7v-5z" class="N"></path><defs><linearGradient id="Bf" x1="330.085" y1="569.911" x2="341.937" y2="563.758" xlink:href="#B"><stop offset="0" stop-color="#4f4e4e"></stop><stop offset="1" stop-color="#6a6868"></stop></linearGradient></defs><path fill="url(#Bf)" d="M331 573c-1-1-1-2 0-3 0-3-1-6 0-9s0-5 1-7v5c0 3 0 5 1 7v2h1c2-1 3-3 5-5l3-2v1c-1 2-3 4-5 6h0c-1 1-3 2-4 3l-2 2z"></path><defs><linearGradient id="Bg" x1="333.335" y1="538.608" x2="341.908" y2="535.415" xlink:href="#B"><stop offset="0" stop-color="#7e7c7c"></stop><stop offset="1" stop-color="#a5a4a4"></stop></linearGradient></defs><path fill="url(#Bg)" d="M338 536c1-1 1-2 2-2 1-1 2-1 2-2h1 1 1c0 1 0 1-1 1h0 1 1 0l-3 2c-1 1-2 1-3 2-1 0-1 1-1 1-2 1-3 2-5 3 0 1-1 1-1 2v3l-1 6v2c-1 2 0 4-1 7s0 6 0 9c-1 1-1 2 0 3l-1 1 1-24v-9c-1-1-1-1-1-2v-3h1l2 2v-1l2-1c1 0 1-1 2-2l1 2z"></path><path d="M335 536c1 0 1-1 2-2l1 2c-1 1-2 1-4 2h0l-1-1 2-1z" class="E"></path><path d="M330 536v3c0 1 0 1 1 2v9l-1 24-2 1v-1-4c-1-6 0-12-1-18v-11l1-3c0-1 1-1 2-2z" class="d"></path><path d="M339 538s0-1 1-1c1-1 2-1 3-2-2 2-3 4-4 7h1c-1 1-3 3-3 4h1 2c-1 1-2 2-2 3-1 0-1 1-1 1l1 1v2 1h1 0c1 0 2 0 3-1 1 0 3-2 5-3h1v1c1-1 1-1 2-1h1c-2 2-4 4-5 6-1 1-2 1-3 2-2 1-4 4-6 4v-1-1l-1-1 1-3-2-1v-5-1-2c2-4 2-6 4-9z" class="G"></path><defs><linearGradient id="Bh" x1="361.508" y1="551.641" x2="350.598" y2="540.471" xlink:href="#B"><stop offset="0" stop-color="#a6a5a5"></stop><stop offset="1" stop-color="#cecdce"></stop></linearGradient></defs><path fill="url(#Bh)" d="M368 529l3-1c1 3 0 6 0 9h1c0 2 0 3-1 4h0l-1 1c0 1-1 1-1 1-1 1-1 2-1 3h-1l-3 3h4 0c0 1-1 2-1 2v-1h-2c0 1-1 1-2 1-5 2-12 8-15 13h-2l-2 2c0-2 0-2 2-4l2-2 1-2c1-1 1-2 2-3l7-6c1 0 3-3 3-4h-1c-1 1 0 0-1 0-4 1-5 4-8 5h-1c-1 0-1 0-2 1v-1h-1c-2 1-4 3-5 3-1 1-2 1-3 1h0-1v-1-2l-1-1s0-1 1-1c0-1 1-2 2-3v-1c1-2 4-3 6-4 1-1 2-3 3-4 3-1 6-3 9-4v1h0c1 0 2-1 3-2 2-1 4-2 5-3h2z"></path><path d="M347 550c3-3 7-6 11-8 1 0 1 0 2 1v1s0 1 1 1h-1c-1 1 0 0-1 0-4 1-5 4-8 5h-1c-1 0-1 0-2 1v-1h-1z" class="B"></path><defs><linearGradient id="Bi" x1="358.325" y1="542.951" x2="349.204" y2="535.141" xlink:href="#B"><stop offset="0" stop-color="#adabac"></stop><stop offset="1" stop-color="#cbcaca"></stop></linearGradient></defs><path fill="url(#Bi)" d="M368 529l3-1c1 3 0 6 0 9h1c0 2 0 3-1 4h0l-1 1c0 1-1 1-1 1-1 1-1 2-1 3h-1c0-1-1-2-2-2 0-1 1-1 2-2 1 0 2 0 2-1 1-1 1-2 1-3-1-2 0-4 1-5 0-1 0-2-1-3-1 0-1 1-2 1l-3 3c-2 1-5 3-7 4-3 2-5 4-8 6-4 2-7 4-10 7 0 1-1 1-2 2h0v-2l-1-1s0-1 1-1c0-1 1-2 2-3v-1c1-2 4-3 6-4 1-1 2-3 3-4 3-1 6-3 9-4v1h0c1 0 2-1 3-2 2-1 4-2 5-3h2z"></path><defs><linearGradient id="Bj" x1="350.441" y1="542.19" x2="344.63" y2="538.076" xlink:href="#B"><stop offset="0" stop-color="#aeaaad"></stop><stop offset="1" stop-color="#c6c7c5"></stop></linearGradient></defs><path fill="url(#Bj)" d="M358 533v1h0c-4 3-9 5-13 8l-4 4-3 3c0-1 1-2 2-3v-1c1-2 4-3 6-4 1-1 2-3 3-4 3-1 6-3 9-4z"></path><path d="M366 498c2-1 3-2 4-3l1 1c1 2 1 3 0 5 1 2 1 4 2 6v1 1c0 1-1 1-1 2-1 1-1 3-2 4h1v5h0c-1 1 0 5-1 5 0 1-2 2-3 3h0 0c1 0 1 0 1 1h-2c-1 1-3 2-5 3-1 1-2 2-3 2h0v-1c-3 1-6 3-9 4-1 1-2 3-3 4-2 1-5 2-6 4v1h-2-1c0-1 2-3 3-4h-1c1-3 2-5 4-7l3-2h0-1-1 0c1 0 1 0 1-1h-1-1-1c0 1-1 1-2 2-1 0-1 1-2 2l-1-2c1 0 2-1 2-2h0 2v-1h0c1-3 3-4 5-5-1-1-1-2-1-2-2-2-3-1-5-3l-1 1c-1 0-2 1-2 1h-1c-1 1-1 2-2 2 1-1 2-2 2-3h1l10-10 8-6 7-7h1c1 0 1-1 2-2l1 1z" class="L"></path><defs><linearGradient id="Bk" x1="365.694" y1="497.079" x2="365.442" y2="506.08" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#Bk)" d="M366 498c2-1 3-2 4-3l1 1c1 2 1 3 0 5h-1c-1 1-1 1-1 2h0c1 1 1 1 1 2h-1c0 1-1 1-1 2l-1-1 1-1h0c-2 0-4 1-6 2h0c-1 2-3 4-5 5h0l1-1v-1c1 0 2-1 2-2h-1l2-2v-2c1-1 2-1 3-2 0-1 1-2 2-4z"></path><path d="M370 505c0-1 0-1-1-2h0c0-1 0-1 1-2h1c1 2 1 4 2 6v1 1c0 1-1 1-1 2-1 1-1 3-2 4h1v5h0c-1 1 0 5-1 5 0 1-2 2-3 3h0 0c1 0 1 0 1 1h-2c-1 1-3 2-5 3-1 1-2 2-3 2h0v-1c2-2 5-3 7-5 1-1 2-2 3-2h0v-3h0c1-2 1-3 1-6h1v-2c-1 1-2 1-3 1 1 0 2-1 3-2 0-1 0-2 1-3v-1h0v-2c0-1 0-1-1-1h0v-2z" class="G"></path><path d="M365 497l1 1c-1 2-2 3-2 4-1 1-2 1-3 2v2l-2 2h1c0 1-1 2-2 2s0 0-1 1 1 0-1 0c0 1-1 1-1 2-1 0-2 1-2 1-3 3-5 4-6 8 1 0 2 0 3 1-2 2-5 5-7 6-1 1-1 2-1 3h0l-1-1h0c1-3 3-4 5-5-1-1-1-2-1-2-2-2-3-1-5-3l-1 1c-1 0-2 1-2 1h-1c-1 1-1 2-2 2 1-1 2-2 2-3h1l10-10 8-6 7-7h1c1 0 1-1 2-2z" class="D"></path><path d="M365 497l1 1c-1 2-2 3-2 4-1 1-2 1-3 2-1 0-1 1-2 0 1-2 3-3 4-5 1 0 1-1 2-2z" class="J"></path><path d="M355 506h1l-5 5-11 10-1 1c-1 0-2 1-2 1h-1c-1 1-1 2-2 2 1-1 2-2 2-3h1l10-10 8-6z" class="i"></path><path d="M568 236l1-1h0l5-10 1 3c1 2 1 3 2 5l2 2c1 1 1 2 2 3l6 10c2 2 2 4 3 5s1 1 1 2c1 1 2 2 2 3h0 1c4 6 8 13 11 19l4 8c5 11 10 22 13 33l1 6 2 7 4 26v8 4 1 2 7 3l-1 11c-1 1-1 3-1 4-1 2-1 5-2 7h1v5h0c0 2 0 3 1 4l1 3-2-1h0v1c0 1 0 2-1 3 0 1 1 1 2 2h-1c0 1-1 1-1 2h-1c0 3-3 4-5 6l-1 2h1l-12 3c-4 1-9 3-13 5l-3 1h-1v-1c1-3 3-7 3-10s2-4 3-7l-4 1c-3 2-7 3-9 5l-10 5c-1 0-2 1-3 2h-1c-1 0-2 1-2 1v-1l1-1c-1 0-2-1-3-1l2-3c0-1-1-3-1-4 0-2 1-2 1-3v-1c0-1 1-2 1-2l-1-2-1-3-2-1v-3h0c-1-1-1-2-1-2-1-1-3-1-4-2l-5-4-5-7-2-2h1l-1-1 2-1-4-4c-1-1-2-2-2-4v-1l2-1c1 0 2-1 3-1 2-2 4-4 5-7l2 1 1 1c4-4 7-9 12-12l6-3 1-1-1-1-1-1s-1 0-1-1c3-2 7-4 10-6v-1-1h0v-1l-1-2v-2l-1-1s-1 0-1-1c-1 2-2 2-3 3v-3c1-1 1-1 2-1h1 1v-1h-1l1-1h2v-1l3-1-1-1c-2 1-3 1-5 1-2-1-3-2-4-3l-1-6c-1-6-3-12-6-17 0-2-1-3 0-4h-2 0v-3h-1 0c-1-1-3-1-5-2v1c-1 0-2-1-3-1 1-1 1-1 2-1h2v-2h1 8 3 1 1c2 1 4 1 6 1h1 0 2c3 1 6 0 8 1v-2h1v-3h-1l-1-2c-1-1-1-3-2-4 0 0-1 0-1-1h0v1l-1 1h-1v-1l-3-3v-1c-2 1-4 1-6 2h-2c-1 0-2 0-3-1h-1c-1-1-2-2-3-2s-2 0-3-1-1-2-1-3l2 1c0-1-1-1-2-2v-2s-2-1-2-2l1-1h1c0-1-1-1-1-2l-3 3c-1 0-1-1-2-2 0-1 1-1 1-2h-1 1v-1l-1-1 1-1h1c1-1 2-1 3-2h1 3l-4-2c0-1-1-3-1-5l-1-2v-3c-1-2-1-4-1-6l3 1c1-1 1-3 1-5l1-1c1-3 2-6 3-8z" class="C"></path><path d="M573 241c0 1 0 1 1 1l-4 8-1-2c0-1 1-3 2-4 0-1 1-2 2-3z" class="I"></path><path d="M612 354h2 0c-1 3-2 5-4 6-1 0-1-1-1-1 1-2 2-3 3-4v-1z" class="E"></path><path d="M623 414c0-2 1-6 2-7 0 1 1 2 1 2 0 2 0 3 1 4-1 1-1 1-2 1h-2 0z" class="H"></path><path d="M611 354h1v1c-1 1-2 2-3 4 0 0 0 1 1 1-3 1-5 1-7 0h0c4-2 6-3 8-6z" class="L"></path><path d="M565 244v1 2c0 1 0 2 1 3v4h0l-1 2h-1c0-1 0-3-1-4v-1-1c1-1 1-3 1-5l1-1z" class="I"></path><path d="M607 302c2 5 3 10 1 15v-1s0-1-1-2v-5c0-1-1-2-2-2v-1c1-1 1 0 2-1v-3z" class="M"></path><path d="M615 337h1 0c0 2 1 3 2 5 0 0 1 1 1 2v2c1 3 1 5 0 8v-1c0-3 0-6-2-8v-1c1 0 0-1 0-2v1c-1-1-2-2-2-3v-3h0z" class="I"></path><path d="M566 254l2 3v1c1 0 1 1 2 1 1 1 1 2 2 3l-1 1c-1 0-1 0-2-1-1 0-2-1-3-1v-1c0-1 0-3-1-4l1-2z" class="F"></path><path d="M566 254l2 3-1 2c0 1 3 1 2 3-1 0-2-1-3-1v-1c0-1 0-3-1-4l1-2z" class="H"></path><path d="M565 256c1 1 1 3 1 4v1h0c-1 2 0 4 1 5v1l-4-2c0-1-1-3-1-5h0c1 0 1 0 2 1h1v-1c-1-1-1-2-1-3v-1h1z" class="N"></path><path d="M611 329l1-1h1v2l1 1c-1 2-2 4-5 4-1 1-3 1-5 1 1-1 1-3 2-4l-1 3h0l2-1c1-1 3-3 4-5z" class="I"></path><path d="M624 375l1-2h1 0c1 0 1 15 0 17v1 1l-1 1s1 0 0 1v1 1l-1 2c0 1 0 3-1 4 1-5 1-10 1-15 1-4 2-8 2-13l-1-1-1 2z" class="W"></path><path d="M606 332c1-1 3-3 3-5l1-1h0v-1h0c1-1 1-1 1-2v-1c1 0 1 0 1-1h0v-1c0-1 0-1 1-1v-2h0c1-1 1-1 1-2v-2c1 0 1-1 0-2v-2-2h-1l1-1v1h1v-1c-1-1-1-2-1-3 1 2 2 4 2 6h-1c0 4 0 7-2 10-1 2-1 4-2 6 0 1-1 2-1 3h0c1 0 0 0 1 1-1 2-3 4-4 5l-2 1h0l1-3z" class="K"></path><path d="M577 254c0-2 0-3 2-5h1v-2c1 0 2 2 3 3 0 0 1 0 1 1 1 1 1 2 3 3 0 0 1 1 1 2h0c0 1 1 2 1 3h2 0 0l1 1c1 1 2 3 3 5h-1l-1-1c0-1 0-2-1-3l-3-1c-1-1-1-1-1-2h0c0-1-1-1-1-2s-2-3-3-4v-1l-1 1-2-1-3 2-1 1z" class="I"></path><path d="M560 249l3 1v1 1c1 1 1 3 1 4v1c0 1 0 2 1 3v1h-1c-1-1-1-1-2-1h0l-1-2v-3c-1-2-1-4-1-6z" class="B"></path><path d="M561 255v2h2v1l-1 1v1h0l-1-2v-3z" class="E"></path><defs><linearGradient id="Bl" x1="596.154" y1="343.739" x2="611.413" y2="340.313" xlink:href="#B"><stop offset="0" stop-color="#949594"></stop><stop offset="1" stop-color="#b0aeaf"></stop></linearGradient></defs><path fill="url(#Bl)" d="M594 340h0 4c3-1 7-2 9-1h1c2 1 4 2 5 5 1 0 1 1 1 2 0-1-1-1-1-2h-3c-1-2-3-2-5-3-2 0-6 0-8 1-1 0-2-1-3-1h-1l1-1z"></path><path d="M590 263h2c1 1 1 2 1 3 1 2 1 3 0 5-2 0-3 1-4 2v1h-1l-1-1v-2l1-1c0-2 0-2 1-3h1v-2-2z" class="P"></path><path d="M597 342c2-1 6-1 8-1 2 1 4 1 5 3h3c0 1 1 1 1 2 1 3 2 5 0 8h0-2-1c0-1 0-5-1-6v-1c-2-2-4-3-7-4-2 0-5 0-6-1z" class="V"></path><path d="M584 425c9-5 19-8 30-9v1h-3l3 1-6 1-6 1c-1 1-6 1-6 2l-4 1c-3 2-7 3-9 5l-10 5c-1 0-2 1-3 2h-1c-1 0-2 1-2 1v-1l1-1 7-4 9-5z" class="g"></path><path d="M584 259c1-1 1-2 1-3h1 1v2h0c1 2 1 3 2 4 0 1 1 3 1 4-2 1-3 2-3 4h0l-2 2h-1v1l-1-1c-1 0-1-1-1-2v-2l-1 1c-1 1-1 1-2 0 0 0-1-1-1-2-1 0-1 0-2-1 1-1 2-1 3-2 1 0 1-2 2-2 1-2 2-2 3-3z" class="Q"></path><path d="M584 259c1-1 1-2 1-3h1 1v2 3c0 1 0 1-1 2v1c-1 1-1 1-1 2s1 2 1 2v2s0 1-1 1c0-1 0-1-1-2h0c0-1-1-1-1-2s0-2 1-3c0-1 1-1 1-2s-1-2-1-3z" class="J"></path><path d="M587 258h0c1 2 1 3 2 4 0 1 1 3 1 4-2 1-3 2-3 4h0-1v-2s-1-1-1-2 0-1 1-2v-1c1-1 1-1 1-2v-3z" class="R"></path><path d="M578 253l3-2 2 1 1-1v1c1 1 3 3 3 4s1 1 1 2h0-1 0v-2h-1-1c0 1 0 2-1 3s-2 1-3 3c-1 0-1 2-2 2v-1c-1-1-1-2-2-2h-1c-1 1-2 2-3 2h-2l1-1c-1-1-1-2-2-3-1 0-1-1-2-1h3c1 0 1 0 2-1h0 1c0-1 1-2 2-2l1-1 1-1z" class="K"></path><path d="M578 253l3-2 2 1c0 1 0 1-1 2v1h-1 0v-1h-2s-1 0-1-1z" class="e"></path><path d="M570 259c1 0 3 1 4 1 1 1 1 1 2 1h0c-1 1-2 2-3 2h-2l1-1c-1-1-1-2-2-3z" class="N"></path><path d="M611 329c-1-1 0-1-1-1h0c0-1 1-2 1-3v2h0 1 1 1v-1 2l1 1c0-1 1-1 1-1l2-2h1v2h-1c-1 2-1 4-2 6-1 0-1 1-1 1h1 1v1l1 1c-1 0-1 0-1 1l1 1h0c0 1 0 2 1 3v1c0 1 1 1 1 2h0v1l1 1 1-1c1-2 1-3 0-5v-3-4c2 3 5 17 3 21l-1 1c-1 1-1 2-2 3-2 2-3 4-5 5-1 1-3 2-4 3h0v-2l3-3c1-2 3-6 3-8 1-3 1-5 0-8v-2c0-1-1-2-1-2-1-2-2-3-2-5h0-1c-1-1-1-1-1-2v-2-1l1 1 1-1s0-1 1-2v-1h0v-1c-2 1-2 2-3 3l-1-1v-2h-1l-1 1z" class="P"></path><path d="M617 364c1-1 1-2 2-3 0-1 1-5 3-6l2 1c-1 1-1 2-2 3-2 2-3 4-5 5z" class="K"></path><path d="M611 325c1-2 1-4 2-6 2-3 2-6 2-10h1c1 5 3 10 4 15 1 3 1 6 2 10v4 3c1 2 1 3 0 5l-1 1-1-1v-1h0c0-1-1-1-1-2v-1c-1-1-1-2-1-3h0l-1-1c0-1 0-1 1-1l-1-1v-1h-1-1s0-1 1-1c1-2 1-4 2-6h1v-2h-1l-2 2s-1 0-1 1l-1-1v-2 1h-1-1-1 0v-2z" class="b"></path><path d="M620 345c0-2 0-2 1-3h0c1 1 1 2 0 3h-1 0zm-4-17c-1 0-1 0-1-1 0-2 0-5 1-7h1c1 2 1 4 1 6l-2 2z" class="T"></path><path d="M610 348c1 1 1 5 1 6-2 3-4 4-8 6h0-1l-1-1h1v-1-2c-1 0-1 0-2 1 0 1-1 1-2 2 0 0-1 0-1 1-1 0-2 1-3 1 3 1 6 1 9 2l2 1h-1 0l-4-1c-1-1-3-1-4 0h1c2 1 4 1 5 2h0v1h-1l-2-1c-3-2-6-2-9-3h-8c-4 1-7 2-10 3 0 0 1-2 2-2 1-1 4-1 5-2h0l1-1c3 0 5-1 7-2 1-1 1-1 2-1h1c1-1 2-1 3-2 1 0 2-1 3-1l2-1h4 1l1-1c2 1 3 1 4 1l1 1h1 0v-1-5z" class="Q"></path><path d="M603 353l1-1c2 1 3 1 4 1v1c-1 1-1 1-2 1s-2-1-3-2z" class="J"></path><path d="M596 354c1 0 1 1 2 2h-1l-1 1s-1 0-2 1h-1c-2 1-6 1-8 3h1c1 1 2 1 3 1h1-8c-4 1-7 2-10 3 0 0 1-2 2-2 1-1 4-1 5-2h0l1-1c3 0 5-1 7-2 1-1 1-1 2-1h1c1-1 2-1 3-2 1 0 2-1 3-1z" class="e"></path><path d="M624 375l1-2 1 1c0 5-1 9-2 13 0 5 0 10-1 15s0 8-5 11v1c-1 0 0 0-1 1-1-1-4-1-6 0h0s-2-1-3-1h0 3v-1h1l-1-1c-1 0-2 1-3 1 4-2 6-6 9-9l2-5c0 1 0 2-1 3 0 1-1 3-1 4l1-1c1 0 1-2 2-3 0-1 0-2 1-2h0v-1-1s1-1 1-2v-2-1h0v-2c0-1-1-1-1-2l1-1v-3c1-1 1-1 1-2-1 0-2-1-2-2v-2h0c1-1 1-1 1-2v-1c1 0 1-1 2-1z" class="P"></path><path d="M624 375l1 1v2c-1 2-1 3-2 4l-1-1c0-3 0-3 2-6z" class="J"></path><defs><linearGradient id="Bm" x1="622.069" y1="397.194" x2="617.1" y2="411.709" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#Bm)" d="M622 396h1c1 3 0 8-2 10 0 3-3 6-5 7h-1l-1-1c1-2 3-3 4-5l3-6c0-1 1-1 0-3h0s1-1 1-2z"></path><path d="M567 413l1 2c1 0 3 1 4 1 3 1 5 0 7 1l1 1c1 0 2 1 2 2v2c0 1 0 2 1 2l1 1-9 5-7 4c-1 0-2-1-3-1l2-3c0-1-1-3-1-4 0-2 1-2 1-3v-1c0-1 1-2 1-2l-1-2-1-3h1 0v-2z" class="U"></path><path d="M570 419c1 0 2 0 2 1 1 0 2 1 3 1l1 3c-3-1-4-3-6-5z" class="K"></path><path d="M568 420c2 2 4 5 7 5h5c-2 1-4 1-6 1h0l-1 1h-1c-2-1-3-2-5-4v-1c0-1 1-2 1-2z" class="T"></path><path d="M582 422c0 1 0 2 1 2l1 1-9 5-1-2c0-1-1 0-2-1h1l1-1h0c2 0 4 0 6-1 1-1 1-2 2-3z" class="K"></path><path d="M568 415c1 0 3 1 4 1 3 1 5 0 7 1l1 1c0 2 1 1 1 3 0 1-2 2-2 3h-3l-1-3c-1 0-2-1-3-1 0-1-1-1-2-1l-1-1-1-3z" class="I"></path><path d="M569 418c1-1 1-1 3-1 1 0 3 1 4 1 1 1 1 2 1 3h-2c-1 0-2-1-3-1 0-1-1-1-2-1l-1-1zm-2 5c2 2 3 3 5 4 1 1 2 0 2 1l1 2-7 4c-1 0-2-1-3-1l2-3c0-1-1-3-1-4 0-2 1-2 1-3z" class="d"></path><defs><linearGradient id="Bn" x1="588.855" y1="327.578" x2="610.171" y2="316.256" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#bebdbe"></stop></linearGradient></defs><path fill="url(#Bn)" d="M603 293c1 1 1 3 2 4 1 2 2 3 2 5v3c-1 1-1 0-2 1v1c1 0 2 1 2 2v5c1 1 1 2 1 2v1c-2 6-5 12-9 17-1 2-3 3-5 5v-1c-1-1 0-3 1-5 1-1 1-2 2-3h0 1v-1l1-1c1-1 2-2 3-4v-1-2h0c0-1 0-3-1-4v-1-1h-1v-1c-1-1-1-2-1-2v-1h1 0v-4c0-1-1-2-2-2 1-1 2-1 2-2 1-2 1-3 1-5h0l1-2c1-1 1-2 1-3z"></path><path d="M604 311c1 0 1-1 2-1l-1-1-1-1-1-1v-1c1 0 1 0 2 1 1 0 2 1 2 2v5c1 1 1 2 1 2-1-1-1-1-1-3-1-1-1-3-1-4 0 1 0 1-1 2h-1z" class="O"></path><path d="M603 293c1 1 1 3 2 4 1 2 2 3 2 5v3c-1 1-1 0-2 1v1c-1-1-1-1-2-1v1l1 1 1 1 1 1c-1 0-1 1-2 1l-1-1c0-1-1-1-2-2 0 0 0-1-1-1 0-1-1-2-2-2 1-1 2-1 2-2 1-2 1-3 1-5h0l1-2c1-1 1-2 1-3z" class="G"></path><path d="M605 297c1 2 2 3 2 5v3c-1 1-1 0-2 1v1c-1-1-1-1-2-1v1l1 1 1 1 1 1c-1 0-1 1-2 1l-1-1c0-1-1-1-2-2 0 0 0-1-1-1 0-1-1-2-2-2 1-1 2-1 2-2h1 0v-1 2h1v1l1-1c2-1 2 0 3 1v-3h0v-2h-1v-3z" class="B"></path><path d="M603 343c3 1 5 2 7 4v1 5 1h0-1l-1-1c-1 0-2 0-4-1l-1 1h-1-4l-2 1c-1 0-2 1-3 1-1 1-2 1-3 2h-1c-1 0-1 0-2 1-2 1-4 2-7 2l-1 1-4 1 1-2c1-1 2-1 3-2 2-1 4-2 5-3 3-3 6-6 9-8s7-3 10-4z" class="K"></path><path d="M598 353c-1 0-1 0-2-1l2-1c1-1 3-1 4-1v3h-4z" class="C"></path><path d="M603 343c3 1 5 2 7 4v1 5h-1s0-1-1-2c0-2-1-3-3-4-1-1-7-1-8 0h-1v1c-2 0-3 2-4 3-2 2-4 4-6 5-1 1-1 2-2 2-1 1-2 2-4 2l-1 1-4 1 1-2c1-1 2-1 3-2 2-1 4-2 5-3 3-3 6-6 9-8s7-3 10-4z" class="d"></path><path d="M590 281c1-2 1-5 2-8 1 2 2 5 3 7 1 3 1 6 3 9 1 1 3 2 5 4 0 1 0 2-1 3l-1 2h0c0 2 0 3-1 5 0 1-1 1-2 2 1 0 2 1 2 2v4h0-1c0-2-2-3-2-5l-1-1h0 0-2v-1l-3 1-2-1c0-1 3-2 4-2v-3-3h-1l-1-2c-1-1-1-3-2-4 0 0-1 0-1-1h0v1l-1 1h-1v-1h1v-2l-2-1c-1-1 0-3-1-4 1 0 1-1 1-2h1c-1-1 0-2 0-2 1 1 3 1 4 2z" class="L"></path><path d="M601 298c0 2 0 3-1 5 0 1-1 1-2 2h0v-2c0-1 3-4 3-5z" class="D"></path><path d="M601 298l-3-3c1 0 1 0 1-1l1-1c1 1 2 2 2 3l-1 2z" class="g"></path><path d="M586 279c1 1 3 1 4 2l-1 1 2 2c1 2 2 5 4 6 0 1 1 2 1 4v1l-1 1-1-1h-1v1h-1l-1-2c-1-1-1-3-2-4 0 0-1 0-1-1h0v1l-1 1h-1v-1h1v-2l-2-1c-1-1 0-3-1-4 1 0 1-1 1-2h1c-1-1 0-2 0-2z" class="B"></path><path d="M586 281c0 1 1 1 1 2v3c-1 1-1 1-2 1-1-1 0-3-1-4 1 0 1-1 1-2h1z" class="R"></path><path d="M591 284c1 2 2 5 4 6 0 1 1 2 1 4v1l-1 1-1-1h-1v1h-1l-1-2c1-1 1-1 2-1v-1c0-1-2-5-2-6h-3-1v-2c1 0 2 0 3 1l1-1z" class="V"></path><path d="M567 266c1 1 2 1 3 2 1 0 1 0 1 1 1 0 1 1 1 1 2 1 4 1 6 2h0c3 1 6 5 8 7 0 0-1 1 0 2h-1c0 1 0 2-1 2 1 1 0 3 1 4l2 1v2h-1l-3-3v-1c-2 1-4 1-6 2h-2c-1 0-2 0-3-1h-1c-1-1-2-2-3-2s-2 0-3-1-1-2-1-3l2 1c0-1-1-1-2-2v-2s-2-1-2-2l1-1h1c0-1-1-1-1-2l-3 3c-1 0-1-1-2-2 0-1 1-1 1-2h-1 1v-1l-1-1 1-1h1c1-1 2-1 3-2h1 3v-1z" class="O"></path><path d="M567 266c1 1 2 1 3 2 1 0 1 0 1 1 1 0 1 1 1 1l-8-3h3v-1z" class="J"></path><path d="M575 274l1-1c1 0 1 0 1 1 2 0 4 2 5 4l1 1h1l-1 1h0c-3-1-4-4-6-4 0 0-2-1-2-2z" class="a"></path><path d="M559 272h3s0-1 1-1 3-1 4-1v1c2 0 3 1 4 2h0c-2-1-6-2-8 0l-3 3c-1 0-1-1-2-2 0-1 1-1 1-2z" class="H"></path><path d="M577 276c2 0 3 3 6 4h0l1-1h1v2c0 1 0 2-1 2 1 1 0 3 1 4l2 1v2h-1l-3-3v-1-1h-1-1-1v-2-1l-3-6z" class="L"></path><path d="M584 283c1 1 0 3 1 4l2 1v2h-1l-3-3v-1-1h-1 0l2 1v-3z" class="O"></path><path d="M563 273c2-2 6-1 8 0 2 0 3 1 4 1 0 1 2 2 2 2l3 6-2 1-1-1-2-4-2-2c-1-1-4-1-6-1h-2-1c0-1-1-1-1-2z" class="C"></path><path d="M564 278s-2-1-2-2l1-1h1 1 2c2 0 5 0 6 1l2 2 2 4 1 1 2-1v1 2h1 1 1v1c-2 1-4 1-6 2h-2c-1 0-2 0-3-1h-1c-1-1-2-2-3-2s-2 0-3-1-1-2-1-3l2 1c0-1-1-1-2-2v-2z" class="Z"></path><path d="M564 278l4 3 4 2c2-1 4 0 5-1l1 1 2-1v1c-2 1-4 2-6 2-1 0-2 0-3-1-2 0-4-1-5-2 0-1-1-1-2-2v-2z" class="e"></path><path d="M564 278l4 3c1 1 2 1 3 2v1h0c-2 0-4-1-5-2 0-1-1-1-2-2v-2z" class="K"></path><path d="M569 276c1 0 2 0 3 1 2 0 2 1 3 2 0 1 0 2 1 2h-3c-1 1-2 0-4 0-1-1-3-2-3-4 1-1 2-1 3-1z" class="N"></path><path d="M564 281l2 1c1 1 3 2 5 2 1 1 2 1 3 1 2 0 4-1 6-2v2h1 1 1v1c-2 1-4 1-6 2h-2c-1 0-2 0-3-1h-1c-1-1-2-2-3-2s-2 0-3-1-1-2-1-3z" class="G"></path><defs><linearGradient id="Bo" x1="598.281" y1="421.344" x2="605.709" y2="433.137" xlink:href="#B"><stop offset="0" stop-color="#979596"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#Bo)" d="M623 414h0 2c1 0 1 0 2-1l1 3-2-1h0v1c0 1 0 2-1 3 0 1 1 1 2 2h-1c0 1-1 1-1 2h-1c0 3-3 4-5 6l-1 2h1l-12 3c-4 1-9 3-13 5l-3 1h-1v-1c1-3 3-7 3-10s2-4 3-7c0-1 5-1 6-2l6-1 6-1-3-1h3v-1c2-1 5-1 7-1 1 0 1-1 2-1z"></path><path d="M622 417v1c1 1 1 2 1 3s-2 3-3 4c-1-1-2-1-3-1 0-1-1-2-1-3s0-1 1-2c0 1 1 2 2 3h2c1 0 1 0 1-1 0-2-1-2-2-3l2-1z" class="h"></path><path d="M614 416c2-1 5-1 7-1 1 0 2 0 2 1v1l-1 1v-1c-3-1-6 1-8 1l-3-1h3v-1z" class="a"></path><path d="M600 429h1l3-3 5-5c1 1 1 1 1 2v1h0c-3 1-5 4-7 7-1 1-3 2-4 3 0-1 0-4 1-5z" class="G"></path><path d="M608 419h2c0 1-1 2-1 2l-5 5-3 3h-1l2-9 6-1z" class="F"></path><path d="M623 414h0 2c1 0 1 0 2-1l1 3-2-1h0v1c0 1 0 2-1 3 0 1 1 1 2 2h-1c0 1-1 1-1 2h-1c0 3-3 4-5 6l-1 2h1l-12 3 3-3c1-3 2-4 3-7v-1c0-1 1-2 1-3 1-1 1-1 2-1l1-1h1c0 1-1 1-1 1-1 1-1 1-1 2s1 2 1 3c1 0 2 0 3 1 1-1 3-3 3-4s0-2-1-3l1-1v-1c0-1-1-1-2-1 1 0 1-1 2-1z" class="B"></path><path d="M616 419l1-1h1c0 1-1 1-1 1-1 1-1 1-1 2s1 2 1 3c1 0 2 0 3 1-1 0-2 1-2 2-1 1-1 2-1 2-1 1-1 1-2 1l-1-1c0-2 1-2 1-4v-4s1-1 1-2z" class="F"></path><defs><linearGradient id="Bp" x1="591.883" y1="418.576" x2="602.824" y2="351.384" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#Bp)" d="M605 364c1 0 3 0 4-1 4-1 8-7 10-10v1c0 2-2 6-3 8l-3 3v2h0c1-1 3-2 4-3 2-1 3-3 5-5 0 2 0 2-1 4 0 1-1 2-1 3l3-2c0 1-1 1-1 2v2 2h0v-2l-1-1c-2 0-3 2-4 2l-1 2c0 1 1 2 2 2h1c0 1 0 2 1 3 0 1 1 2 0 3h0c-1 1 1 3 1 4 1 1 0 3 0 4-1 2 0 3-1 4v4c0 1-1 3-1 4l-2 5c-3 3-5 7-9 9 1 0 2-1 3-1l1 1h-1v1h-3 0c1 0 3 1 3 1h0c2-1 5-1 6 0h-1-1-2c-1 0-2 0-3 1h-2c-1 0-1 0-2 1-1 0-1-1-2 0h-2l-1 1h-2c-3 1-4 0-6 0l-6-1c-1 0-2 2-3 1l-1-1h-3 0-1c-2-1-4 0-7-1-1 0-3-1-4-1l-1-2v2h0-1l-2-1v-3h0c-1-1-1-2-1-2-1-1-3-1-4-2l-5-4-5-7-2-2h1l-1-1 2-1c5 4 9 9 14 11l3 2 3 1c3 1 6 2 10 3 2 0 5 0 8-1l10-3 1 1h2c1 0 3 0 4-1v-1h0c3-1 4-5 5-7v-1c1-3 1-8 1-12-1-3-2-6-4-8l1-1-4-4h-2l-1-2v-1c1-1 1-1 1-2h1v-1h0c-1-1-3-1-5-2h-1c1-1 3-1 4 0l4 1h0 1z"></path><path d="M616 392h2c0 2-1 5-2 7h-1c0-1 0-1-1-2 1-2 1-3 2-5z" class="F"></path><path d="M614 397c1 1 1 1 1 2h1c0 2-1 3-2 4-1 2-2 4-4 5l-1-1c1-2 2-3 3-4 1-2 2-3 2-5v-1z" class="T"></path><path d="M616 380c2 4 2 8 2 12h-2c0-3 0-6-1-9l1-3z" class="E"></path><path d="M600 410c1 0 5-1 6 0h0c-5 2-9 2-14 4h-1l-1-1c1-1 8-2 10-3z" class="G"></path><path d="M601 366h1c2 2 4 3 6 4 3 3 6 6 8 10l-1 3c0-1 0-1-1-2l-1 1c-2-2-4-5-6-7l-4-4h-2l-1-2v-1c1-1 1-1 1-2z" class="B"></path><path d="M603 371h1c2 0 5 3 6 5l4 5-1 1c-2-2-4-5-6-7l-4-4z" class="a"></path><path d="M604 405c1 0 1 0 2 1l-3 3h-1l-2 1c-2 1-9 2-10 3l1 1h1-2c-1 1-2 1-3 1h-2c1 0 3 0 4-1v-2c-1-2-1-3-2-4l10-3 1 1h2c1 0 3 0 4-1z" class="T"></path><path d="M579 409c2 0 5 0 8-1 1 1 1 2 2 4v2c-1 1-3 1-4 1l-17-3-1 1v2h0-1l-2-1v-3h0c-1-1-1-2-1-2l4 1 4 1c4 1 6 0 10-1h-1 0l2-1h-3z" class="M"></path><path d="M563 409l4 1h0c-1 1-1 1-2 1l1 2c-1 0-1 0-2 1v-3h0c-1-1-1-2-1-2z" class="E"></path><defs><linearGradient id="Bq" x1="563.893" y1="401.901" x2="561.245" y2="407.111" xlink:href="#B"><stop offset="0" stop-color="#a3a1a2"></stop><stop offset="1" stop-color="#c0bebf"></stop></linearGradient></defs><path fill="url(#Bq)" d="M549 392c5 4 9 9 14 11l3 2 3 1c3 1 6 2 10 3h3l-2 1h0 1c-4 1-6 2-10 1l-4-1-4-1c-1-1-3-1-4-2l-5-4-5-7-2-2h1l-1-1 2-1z"></path><path d="M561 404c2 1 3 1 5 1l3 1v1c-4 1-6-1-8-3z" class="G"></path><path d="M549 392c5 4 9 9 14 11l3 2c-2 0-3 0-5-1h-1c-5-3-7-8-12-10l-1-1 2-1z" class="U"></path><defs><linearGradient id="Br" x1="614.646" y1="401.32" x2="599.041" y2="384.995" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#9c9c9c"></stop></linearGradient></defs><path fill="url(#Br)" d="M607 375c2 2 4 5 6 7l1-1c1 1 1 1 1 2 1 3 1 6 1 9-1 2-1 3-2 5v1c0 2-1 3-2 5-1 1-2 2-3 4l1 1c-2 1-3 2-4 2h0c-1-1-5 0-6 0l2-1h1l3-3c-1-1-1-1-2-1v-1h0c3-1 4-5 5-7v-1c1-3 1-8 1-12-1-3-2-6-4-8l1-1z"></path><path d="M614 388c-1 1-1 1-2 1v-4l2-1v4z" class="S"></path><path d="M614 381c1 1 1 1 1 2 1 3 1 6 1 9-1 2-1 3-2 5v1c-2-2 0-3 0-5-1-1-2 0-2-2h1v1h1c1-2 0-2 0-4v-4c-1 0-1-1-1-2l1-1z" class="G"></path><path d="M561 301v-2h1 8 3 1 1c2 1 4 1 6 1h1 0 2c3 1 6 0 8 1v-2h1v3c-1 0-4 1-4 2l2 1 3-1v1h2 0 0l1 1c0 2 2 3 2 5v1s0 1 1 2v1h1v1 1c1 1 1 3 1 4h0v2 1c-1 2-2 3-3 4l-1 1v1h-1 0c-1 1-1 2-2 3-1 2-2 4-1 5v1 1l-1 1h1c1 0 2 1 3 1 1 1 4 1 6 1-3 1-7 2-10 4s-6 5-9 8c-1 1-3 2-5 3-1 1-2 1-3 2h-1l-1-1-1-1s-1 0-1-1c3-2 7-4 10-6v-1-1h0v-1l-1-2v-2l-1-1s-1 0-1-1c-1 2-2 2-3 3v-3c1-1 1-1 2-1h1 1v-1h-1l1-1h2v-1l3-1-1-1c-2 1-3 1-5 1-2-1-3-2-4-3l-1-6c-1-6-3-12-6-17 0-2-1-3 0-4h-2 0v-3h-1 0c-1-1-3-1-5-2v1c-1 0-2-1-3-1 1-1 1-1 2-1h2z" class="f"></path><path d="M587 343h1c2-1 3-2 4-3-1 2-1 3-3 4-1-1-2 0-3 0l1-1z" class="G"></path><path d="M592 334l1 1v2c-1 2-1 2-3 3h0c0-1 0-1-1-1l2-2v-1l1-2z" class="F"></path><path d="M574 359h2v-1c0-1 2-2 3-2l5-1c-1 1-3 2-5 3-1 1-2 1-3 2h-1l-1-1z" class="H"></path><path d="M586 344c1 0 2-1 3 0-2 3-4 5-7 7v-1-1h0v-1c1-1 3-2 4-4z" class="D"></path><path d="M587 327s0-1 1-1v6c0-1 1-3 1-4h1c1 2 1 4 2 6l-1 2v1l-1-1h0l-1 1c-1 1-3 2-4 3l-1-1h-1l-1-1 3-1-1-1c1-2 2-3 2-5l1-4z" class="N"></path><path d="M587 327s0-1 1-1v6c0 2-2 4-3 5l-1-1c1-2 2-3 2-5l1-4z" class="L"></path><path d="M585 340c1-1 3-2 4-3l1-1h0l1 1-2 2-1 2-1 2-1 1c-1 2-3 3-4 4l-1-2v-2l-1-1s-1 0-1-1c-1 2-2 2-3 3v-3c1-1 1-1 2-1h1 1v-1h-1l1-1h2v-1l1 1h1l1 1z" class="C"></path><path d="M579 341h1v-1h-1l1-1h2v-1l1 1h1l1 1c-2 1-4 1-6 1h0z" class="E"></path><path d="M581 346c2-2 4-4 7-5l-1 2-1 1c-1 2-3 3-4 4l-1-2z" class="Q"></path><defs><linearGradient id="Bs" x1="576.721" y1="314.708" x2="589.216" y2="299.775" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#Bs)" d="M561 301v-2h1 8 3 1 1c2 1 4 1 6 1h1 0 2c3 1 6 0 8 1v-2h1v3c-1 0-4 1-4 2l2 1 3-1v1c1 1 2 1 2 3 1 4 3 9 0 14 0 3-1 7-2 10-2-3-2-6-3-9-3-6-6-12-12-16-5-3-11-5-18-6z"></path><path d="M591 305l3-1v1c1 1 2 1 2 3 1 4 3 9 0 14v-1c1-2 1-3 1-4-1-5-2-9-6-12z" class="E"></path><defs><linearGradient id="Bt" x1="572.393" y1="324.324" x2="579.686" y2="322.813" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#626061"></stop></linearGradient></defs><path fill="url(#Bt)" d="M559 301h2c7 1 13 3 18 6 1 3 4 5 5 7s2 4 3 5l2 2v3s-1 1-1 2h0c-1 0-1 1-1 1l-1 4c0 2-1 3-2 5-2 1-3 1-5 1-2-1-3-2-4-3l-1-6c-1-6-3-12-6-17 0-2-1-3 0-4h-2 0v-3h-1 0c-1-1-3-1-5-2v1c-1 0-2-1-3-1 1-1 1-1 2-1z"></path><path d="M574 317v-1c3 1 4 2 6 5h1l-1-1h0c2 2 3 5 2 8 0 2-1 3-2 4 0-4 2-6 0-10-1-1-1-2-2-3h-1v3h0v2h1v1 1 2h-1 0c-1-4-1-8-3-11z" class="K"></path><path d="M574 317c0-1 0-2-1-3h0l-2-2c0-1-1-1-1-2h1c1 1 2 2 4 3s5 5 6 7c2 2 2 4 3 6v4l1 1h1c0 2-1 3-2 5-2 1-3 1-5 1l1-1c-1 0-1-1 0-1v-2-1c1-1 2-2 2-4 1-3 0-6-2-8h0l1 1h-1c-2-3-3-4-6-5v1z" class="I"></path><path d="M559 301h2c7 1 13 3 18 6 1 3 4 5 5 7s2 4 3 5l2 2v3s-1 1-1 2h0c-1 0-1 1-1 1l-1 4h-1l-1-1v-4c-1-2-1-4-3-6-1-2-4-6-6-7s-3-2-4-3c-1 0-1-1-2 0h0l-1 1c0-2-1-3 0-4h-2 0v-3h-1 0c-1-1-3-1-5-2v1c-1 0-2-1-3-1 1-1 1-1 2-1z" class="d"></path><path d="M569 307h3l1 1c1 0 1 0 2 1h1c1 0 1 1 2 1h1v1c2 1 4 4 5 6 1 1 2 3 3 4h0c1-1 1 0 0-1v-1l2 2v3s-1 1-1 2h0c-1 0-1 1-1 1s-1-1-1-2 0-2-1-4c-2-4-5-8-9-11-2-1-4-2-7-3z" class="G"></path><path d="M559 301h2c7 1 13 3 18 6 1 3 4 5 5 7s2 4 3 5v1c1 1 1 0 0 1h0c-1-1-2-3-3-4-1-2-3-5-5-6v-1h-1c-1 0-1-1-2-1h-1c-1-1-1-1-2-1l-1-1h-3-1-2 0v-3h-1 0c-1-1-3-1-5-2v1c-1 0-2-1-3-1 1-1 1-1 2-1z" class="D"></path><path d="M576 360l-1 2 4-1h0c-1 1-4 1-5 2-1 0-2 2-2 2 3-1 6-2 10-3h8c3 1 6 1 9 3l2 1c0 1 0 1-1 2v1l1 2h2l4 4-1 1c2 2 3 5 4 8 0 4 0 9-1 12v1c-1 2-2 6-5 7h0v1c-1 1-3 1-4 1h-2l-1-1-10 3c-3 1-6 1-8 1-4-1-7-2-10-3l-3-1-3-2c-5-2-9-7-14-11l-4-4c-1-1-2-2-2-4v-1l2-1c1 0 2-1 3-1 2-2 4-4 5-7l2 1 1 1c4-4 7-9 12-12l6-3 1-1h1z" class="C"></path><path d="M560 396h1c1 2 3 4 5 6h0c-1 0-1 0-1-1-3-1-4-2-5-4v-1z" class="R"></path><path d="M596 383c1 3 1 6 1 9-2 2-4 5-6 7l1-3c1-2 2-3 3-4 2-3 1-6 1-9z" class="N"></path><path d="M592 396l-1 3c-4 2-8 4-13 4 1-1 2-1 3-2 1 0 1 0 2-1 2 0 4-2 6-3l3-1z" class="U"></path><path d="M596 370c2-1 3 2 5 1h2l4 4-1 1-1-2h-1l-1 2c-2-2-5-4-7-6zm1 35c6-2 7-4 10-10 0 4-1 6-3 9h0v1c-1 1-3 1-4 1h-2l-1-1z" class="E"></path><defs><linearGradient id="Bu" x1="569.696" y1="401.92" x2="561.368" y2="395.525" xlink:href="#B"><stop offset="0" stop-color="#312f30"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#Bu)" d="M562 394l2 2c2 3 7 6 10 7-2 0-3-1-4-1l1 1c-2-1-3-1-5-1-2-2-4-4-5-6l1-2z"></path><path d="M592 379c1 1 2 2 3 4h1c0 3 1 6-1 9l-1-1c-1-1-1-2-1-4h0c-1-2 0-3-1-4 0-1-1-2-1-3l1-1z" class="O"></path><path d="M565 391h0l1 2 1 1c0 1 1 2 2 3s2 2 4 2h0v1c3 2 5 1 8 1-1 1-2 1-3 2h-4c-3-1-8-4-10-7l-2-2v-1h2c1-1 1-1 1-2z" class="B"></path><path d="M565 376h2v1l-1 1 1 1-2 7s-1 1-1 2l1 1v1 1c0 1 0 1-1 2h-2c0-1 1-1 1-2-1 0-1 0-2-1h0v-1-4h0l1-2c0-2 2-5 2-7h1 0z" class="H"></path><path d="M565 376h2v1l-1 1 1 1-2 7v-3h-1c0-2 1-3 2-5-1 0-1-1-1-1v-1h0z" class="c"></path><path d="M561 389c0-1 0-2 1-3 0-1 0-1 1-1 0 1 1 2 1 3l1 1v1 1c0 1 0 1-1 2h-2c0-1 1-1 1-2-1 0-1 0-2-1h0v-1z" class="G"></path><path d="M561 390l1-1h1l1 1h1v1c0 1 0 1-1 2h-2c0-1 1-1 1-2-1 0-1 0-2-1z" class="L"></path><path d="M580 374l2 1v-1c1 1 1 1 1 2l6 3h1 2l-1 1c0 1 1 2 1 3 1 1 0 2 1 4h0c0 2 0 3 1 4l1 1c-1 1-2 2-3 4l-3 1-1-1h0 0l1-2c2-2 3-5 2-8v-1-1c-2-4-8-7-11-8l-1-1 1-1z" class="H"></path><path d="M603 376l1-2h1l1 2c2 2 3 5 4 8 0 4 0 9-1 12v1c-1 2-2 6-5 7h0 0c2-3 3-5 3-9v-2c1-6 0-12-4-17z" class="G"></path><path d="M575 389c2 0 3-1 5 0s3 2 4 3h0c1 2 0 4 1 6 1-1 2-1 3-2l1 1c-2 1-4 3-6 3-1 1-1 1-2 1-3 0-5 1-8-1v-1c2 1 4 1 6-1 1 0 2-1 2-3-1-1-2-1-3-1 0-1-1-1-1-2s-1-1-2-2h0-1l1-1z" class="V"></path><path d="M575 390c2 0 3 0 5 1 0 1 1 2 1 3v1c-1-1-2-1-3-1 0-1-1-1-1-2s-1-1-2-2z" class="e"></path><path d="M568 389c1-1 1-1 3-1 1 2 1 2 2 2h0 1 1 0c1 1 2 1 2 2s1 1 1 2c1 0 2 0 3 1 0 2-1 3-2 3-2 2-4 2-6 1h0c-2 0-3-1-4-2s-2-2-2-3l-1-1 2-2h0v-2z" class="K"></path><path d="M567 394c1 0 1-1 3 0v3h-1c-1-1-2-2-2-3z" class="C"></path><path d="M573 390h1 1l-1 1v2h-5l-1-2h3c1 0 1 1 2 1l1-1-1-1z" class="X"></path><path d="M568 389c1-1 1-1 3-1 1 2 1 2 2 2h0l1 1-1 1c-1 0-1-1-2-1h-3 0 0v-2z" class="c"></path><defs><linearGradient id="Bv" x1="574.787" y1="371.005" x2="572.515" y2="367.954" xlink:href="#B"><stop offset="0" stop-color="#6f6e6e"></stop><stop offset="1" stop-color="#908e8f"></stop></linearGradient></defs><path fill="url(#Bv)" d="M572 365c3-1 6-2 10-3 1 0 2 0 3 1h-1v1h-3l3 1c-1 0-2 1-2 1-1 1-2 0-3 1 0 1 0 1-1 2h1 0c-4 1-9 5-11 9l-1 1h0l-1-1 1-1v-1h-2c1-1 2-2 2-3s0-1 1-2v-3l4-3z"></path><path d="M572 365c3-1 6-2 10-3 1 0 2 0 3 1h-1v1h-3c-3 1-6 2-9 4h0c-1 1-2 2-4 3v-3l4-3z" class="L"></path><defs><linearGradient id="Bw" x1="592.435" y1="369.26" x2="580.366" y2="364.671" xlink:href="#B"><stop offset="0" stop-color="#585456"></stop><stop offset="1" stop-color="#6e6e6e"></stop></linearGradient></defs><path fill="url(#Bw)" d="M582 362h8c3 1 6 1 9 3l2 1c0 1 0 1-1 2v1l1 2c-2 1-3-2-5-1h-1c-5-2-10-2-16-1h0-1c1-1 1-1 1-2 1-1 2 0 3-1 0 0 1-1 2-1l-3-1h3v-1h1c-1-1-2-1-3-1z"></path><path d="M582 362h8c3 1 6 1 9 3l2 1c0 1 0 1-1 2v1c-3-1-6-3-8-4-2 0-3 0-5-1h-1c-1 0-1 1-2 1h0l-3-1h3v-1h1c-1-1-2-1-3-1z" class="D"></path><path d="M599 365l2 1c0 1 0 1-1 2h-1c-1-1-1-1-1-2l1-1z" class="Y"></path><path d="M573 376h0c1 0 2 1 2 1h1c1 0 3 0 4-1 0 2 0 4-1 6-1 0-2 1-3 1l-1 2v1h0v1c-1 0-1 1-1 2h1l-1 1h-1 0c-1 0-1 0-2-2-2 0-2 0-3 1v2h0l-2 2-1-2h0v-1-1l2-2c-1-1 0-2 0-4 1-1 1-3 2-4 2-1 3-2 4-3z" class="b"></path><path d="M567 387c1-1 0-2 2-4-1 2-1 5-1 6v2h0l-2 2-1-2h0v-1-1l2-2z" class="J"></path><path d="M565 391l1-1c1 0 1 0 2 1h0l-2 2-1-2z" class="W"></path><path d="M569 383c1-2 1-3 3-4h1c1 0 1-1 2-1h1v1l2 1c-1 1-2 2-2 3l-1 2v1h0v1c-1 0-1 1-1 2h1l-1 1h-1 0c-1 0-1 0-2-2v-1c0-1 1-1 1-1h0c1-2 2-2 3-3 0-1 1-2 1-3l-1-1c-1 0-1 1-2 1s-2 2-3 3h-1z" class="J"></path><path d="M573 376h0c1 0 2 1 2 1h1c1 0 3 0 4-1 0 2 0 4-1 6-1 0-2 1-3 1 0-1 1-2 2-3l-2-1v-1h-1c-1 0-1 1-2 1h-1c-2 1-2 2-3 4h0c-2 2-1 3-2 4-1-1 0-2 0-4 1-1 1-3 2-4 2-1 3-2 4-3zm-14 5v-2h0l1 1h1l1 1-1 1h-1c1 0 1 1 2 1l-1 2h0v4 1h0c1 1 1 1 2 1 0 1-1 1-1 2v1l-1 2h-1v1l-2-2c-1 0-1 1-2 1s-1-1-2-2c0 0-1 0-2-1l6 6c2 1 3 2 5 4-5-2-9-7-14-11l-4-4c-1-1-2-2-2-4h1v2c1 0 4 2 6 2l1-1v-1c1-1 2-2 3-2l1 1c2-1 3-2 4-4z" class="K"></path><path d="M561 390h0c1 1 1 1 2 1 0 1-1 1-1 2v1l-1 2h-1l-2-2h0c1 0 2-1 2-1v-1l-1-1h0l2-1z" class="Z"></path><path d="M559 381v-2h0l1 1h1l1 1-1 1h-1c1 0 1 1 2 1l-1 2h0l-8 3c0-1 2-2 2-3 2-1 3-2 4-4z" class="B"></path><path d="M560 382c1 0 1 1 2 1l-1 2-2-1c0-1 1-1 1-2z" class="L"></path><path d="M573 376c1-1 2-2 3-2 1-1 3-1 5-1l-1 1-1 1 1 1c3 1 9 4 11 8v1 1c1 3 0 6-2 8l-1 2h0 0c-1 1-2 1-3 2-1-2 0-4-1-6h0c-1-1-2-2-4-3s-3 0-5 0h-1c0-1 0-2 1-2v-1h0v-1l1-2c1 0 2-1 3-1 1-2 1-4 1-6-1 1-3 1-4 1h-1s-1-1-2-1h0z" class="I"></path><path d="M576 360l-1 2 4-1h0c-1 1-4 1-5 2-1 0-2 2-2 2l-4 3v3c-1 1-1 1-1 2s-1 2-2 3h0-1c0 2-2 5-2 7-1 0-1-1-2-1h1l1-1-1-1h-1l-1-1h0v2c-1 2-2 3-4 4l-1-1c-1 0-2 1-3 2v1l-1 1c-2 0-5-2-6-2v-2h-1v-1l2-1c1 0 2-1 3-1 2-2 4-4 5-7l2 1 1 1c4-4 7-9 12-12l6-3 1-1h1z" class="C"></path><path d="M559 381c-1 2-2 3-4 4l-1-1c0-1 1-2 2-2h0l3-1z" class="I"></path><g class="V"><path d="M568 368v3c-1 1-1 1-1 2h-2l-1-2c1-1 3-2 4-3z"></path><path d="M565 373h2c0 1-1 2-2 3h0-1c0 2-2 5-2 7-1 0-1-1-2-1h1l1-1-1-1h-1l-1-1h0l1-1c1-2 1-4 4-5 0 1 0 0 1 0h0z"></path></g><path d="M565 373h2c0 1-1 2-2 3h0-1-1l1-1v-2c0 1 0 0 1 0h0z" class="B"></path><path d="M553 374l2 1 1 1c-1 1-1 2-2 3-2 2-5 4-6 7-1-1-2-2-2-3-1 0-1 1-2 1h-1v-1l2-1c1 0 2-1 3-1 2-2 4-4 5-7z" class="H"></path><path d="M555 375l1 1c-1 1-1 2-2 3 0 0-1 0-1-1-1-2 1-1 1-2 1 0 1-1 1-1z" class="E"></path><path d="M194 213h0c1 1 0 1 1 1 1 1 1 0 2 1-1 1-1 1-2 1v2c-2 2-5 4-7 6h2c2-1 5-3 7-5v1c-1 1-3 2-4 3h1 0 1c1-1 1-1 2-1 0-1 2-2 3-2l-3 2c-1 1-3 2-4 3-1 0-1 1-2 2l-1 1s-1 1-1 2 0 1 1 2l2-1c0 1 1 1 1 1h2l19 5 6 3 2 2c2 1 3 3 4 5h0c1 3 1 6 1 9 0 0-1 0-1 1-2 2-4 6-6 8l-3 3s-1 0-1-1c0 1 0 2-1 3l-1 1c0 1-1 1-1 2h-1c0 1 0 2 1 4l-2 3-2 3v2c-2 3-3 6-5 9-3-3-5-6-8-8v1c1 1 3 2 4 4h0-2c-1 0-2 0-2-1l-2-1h-1-2v2c1 2 2 4 3 5 2 2 5 3 7 4l2 1v1h-3 0c-1 0-1 0-2 1h1c0 1 0 2-1 3l-2 6-2 8-1 2-2 5-3 16h-1-1l-3-4-1-2h-2v1l1 5 1 3h-1c-2 3-3 8-3 11 0 2 0 5-1 6s-1 1-1 2v1h-1l1-1c-1 0-2 1-2 2-3 5-7 12-12 16l-2 2c0-1-1-2-2-2h0l-1-1v-1c-1 1-2 2-2 3h-1 0c-3 0-7-6-10-9l-6-7c-2-2-5-3-7-5h0l-9-10-4-3c-2 0-5-2-8-2-3-1-7-3-11-3v-1h-2c1-1 3-1 4-1s2-1 3-1 2 0 3-1v-1l1-1 2 1h0v-1c-8-7-11-13-12-23 1-1 1-3 2-4s2-2 2-3l-2-1v-1c0-1 1-2 0-3v-4c0-3 1-5 2-7l3-4 2-3v-2l1-1c0-1 0-1 1-1v-2-2c1 0 1-1 1-2h0v-2-1-2c1-2 1-3 2-5l5-8h3c1 1 0 3 0 4v1l1 1 2 1c1 0 1 2 3 2l4 2h3 1 2l6-3v-1l1-1h0l-2-2c1 0 1 0 1-1 1 0 2 0 2-1v-1-3h1v-1c-1-1 1-3 2-5 1-1 3-2 5-3 4-1 8-1 11 0h4v2 2h-1c1 0 2-1 2-1 1-1 1-1 0-2h0c1-1 1-1 1-2h0c1-1 1-1 1-2h1c3-3 5-7 7-11l4-6c2-2 4-5 6-7l1-2 1-1c2-2 3-2 4-4z" class="Y"></path><path d="M124 274h2v1l-1 1-1 1-1-1c1-1 0-1 1-2z" class="i"></path><path d="M189 244h1 1l1 1-1 2h-4c1 0 1 0 2-1h1v-1h-4c1-1 2-1 3-1z" class="L"></path><path d="M102 293l3-4h0c0 1 0 2 1 3 0 1 0 1-1 2l-3-1z" class="G"></path><path d="M189 286h2v1c-1 1-2 1-3 2v1l-1-1v-1c0-2 1-2 2-2z" class="O"></path><path d="M168 270c2 0 4 0 6-1 1 1 1 1 1 2 0 0-1 0 0 1-2 1-5-1-6-2h-9 2 6z" class="H"></path><path d="M160 264h-3l-1 1h0v-1c1-2 4-3 6-5 2-1 3-2 5-2-1 1-6 5-7 7z" class="V"></path><path d="M117 278h1c1 2 1 3 1 4v2 1c0 1-1 1-3 2v-1c-1-1 0-4-1-5l1-1c1 0 1-1 1-2z" class="E"></path><path d="M116 286v-3-1c1 0 1 0 1 1l2 1v1c0 1-1 1-3 2v-1z" class="R"></path><path d="M102 293l3 1c-2 2-4 9-5 13 0-1 1-2 0-3v-4c0-3 1-5 2-7z" class="D"></path><path d="M112 266c0 2 0 3 1 4v1c1-1 1-1 2-1v4l-2-2-1 1c-2 2-2 5-2 8-1 2-2 4-3 5v-2l1-1c0-1 0-1 1-1v-2-2c1 0 1-1 1-2h0v-2-1-2c1-2 1-3 2-5z" class="P"></path><path d="M157 277c3-2 7-3 10-3s6 1 8 4v1l-1 1c-1-2-3-3-5-4h-1-4l-4 2h-1 0c-1-1-2 1-3 1l2-1-1-1z" class="W"></path><path d="M191 291c1 2 2 4 3 5-1 1-2 0-3 0-1-1-1-2-2-2h-1v1 1c-2-1-2 0-4 0 0 1-2 1-3 1h-10c1-1 1-1 2-1v-1l1-1h2l1-1c1 1 0 2 1 2h2c0-1 1-1 2-1l1 1c1 1 2 0 3 0 0 0 0-1 1-1h1l1-1v1h2l1 1c0-1-1-1-1-2v-2z" class="O"></path><path d="M167 257c3-2 5-4 8-5v1h1c-2 2-5 4-7 6-2 1-5 5-7 6l-2-1c1-2 6-6 7-7z" class="S"></path><defs><linearGradient id="Bx" x1="164.053" y1="269.73" x2="153.19" y2="266.364" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#4c4949"></stop></linearGradient></defs><path fill="url(#Bx)" d="M162 266c2-1 2-2 4-2 1 1 1 1 0 2 0 2-2 3-3 3l-1 1h-2l-8 3c-1-1-1-1-1-2l9-4 2-1z"></path><path d="M157 277l1 1-2 1c1 0 2-2 3-1h0v1c-2 1-3 2-4 3-1 2-3 6-4 7h-3-6v-1c0-1 1-1 1-1 2 0 4 0 6 1 2-4 4-8 8-11h0z" class="C"></path><path d="M141 271c2 0 4-1 5-1h4c4-2 7-3 10-3l-9 4c0 1 0 1 1 2l-3 1c-1 1-3 2-4 3-1-1-1-2-3-3 0-1 0-2-2-3h1z" class="D"></path><path d="M151 271c0 1 0 1 1 2l-3 1c-1 0-1-1-2-1h-1l5-2z" class="P"></path><path d="M141 271c1 0 2 0 2 1v1h1 2 1c1 0 1 1 2 1-1 1-3 2-4 3-1-1-1-2-3-3 0-1 0-2-2-3h1z" class="F"></path><path d="M142 261c1 0 2 0 2-1v-1-3h1v-1c-1-1 1-3 2-5 0 2 0 3 1 4 0 1-1 3 1 4l-1 3c1 1 2 0 4 1l-1 1-7 3c0-1 2-2 2-4h-1l-2 2h0l-2-2c1 0 1 0 1-1z" class="R"></path><defs><linearGradient id="By" x1="128.929" y1="284.399" x2="137.557" y2="273.727" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#818080"></stop></linearGradient></defs><path fill="url(#By)" d="M140 271c2 1 2 2 2 3 2 1 2 2 3 3-2 2-3 3-6 5-2 1-4 2-6 2-1 1-4 1-5 0-1 0-2-1-2-2h-1c0-1 1-3 1-4 3-5 9-6 14-7z"></path><path d="M133 284h-1c2-2 3-3 5-4 1-1 2-2 3-2h0l-1 1v3c-2 1-4 2-6 2z" class="R"></path><path d="M142 274c2 1 2 2 3 3-2 2-3 3-6 5v-3l1-1h0l2-2-1-1 1-1z" class="C"></path><path d="M140 271c2 1 2 2 2 3l-1 1c-1-1-2-1-4 0h-1c-3 0-6 2-8 5l-1 2h-1-1c0-1 1-3 1-4 3-5 9-6 14-7z" class="S"></path><path d="M117 258h3c1 1 0 3 0 4v1l1 1 2 1c1 0 1 2 3 2l4 2h3 1 2l-13 4c-2 2-4 3-5 5h-1c0 1 0 2-1 2l-1 1v-1c-1-2 0-2 1-3 0-1-1-2-1-3v-4c-1 0-1 0-2 1v-1c-1-1-1-2-1-4l5-8z" class="F"></path><path d="M113 270l2-1h1c0 3 1 6 1 9 0 1 0 2-1 2l-1 1v-1c-1-2 0-2 1-3 0-1-1-2-1-3v-4c-1 0-1 0-2 1v-1z" class="e"></path><path d="M118 273c-1-1-1-2-1-3l2-3h0c1 2 2 2 3 3l1 1h-1c-1 1-3 4-4 3h0v1-2z" class="E"></path><path d="M121 264l2 1c1 0 1 2 3 2l4 2h3 1 2l-13 4h-1c1-1 1-2 3-2h1v-1c0-1 0-1-1-1h-2l-1 1c0-2-1-3-2-5h1v-1h0z" class="X"></path><path d="M117 258h3c1 1 0 3 0 4v1l1 1h0v1h-1 0c-1-1-1-1-1-2h-1c-1 0-1 1-1 2h-1c0 1-1 2 0 3v1h0-1l-2 1c-1-1-1-2-1-4l5-8z" class="b"></path><path d="M147 250c1-1 3-2 5-3 4-1 8-1 11 0h4v2l-4 4c-1 1-4 3-4 4v1c-2 2-5 4-8 5l1-1c-2-1-3 0-4-1l1-3c-2-1-1-3-1-4-1-1-1-2-1-4z" class="Z"></path><path d="M149 258h0c-1-1-1-3-1-5 1-2 3-3 5-4 3-2 5-2 8-1-1 0-3 0-5 1h-2c-1 0-4 3-5 4 0 1 0 3 1 4v2h1c0 1 0 1 1 2v1h0c-2-1-3 0-4-1l1-3z" class="c"></path><defs><linearGradient id="Bz" x1="166.063" y1="249.195" x2="162.015" y2="250.839" xlink:href="#B"><stop offset="0" stop-color="#616061"></stop><stop offset="1" stop-color="#787778"></stop></linearGradient></defs><path fill="url(#Bz)" d="M163 247h4v2l-4 4h-1l-1-1v-1c0-1-1-1-2-2v2l-1-2h-1-1c2-1 4-1 5-1s2 1 3 1l-1-2z"></path><path d="M151 259h-1v-2c-1-1-1-3-1-4 1-1 4-4 5-4h2 1 1l1 2-1 2h-1v1c0 1-1 1-1 1h-1c0-1 0-2-1-3h-1v4 3l-1 2c-1-1-1-1-1-2z" class="U"></path><path d="M156 250h2v1l-1 1h-1l-1-1 1-1z" class="M"></path><path d="M151 253c1 0 1 0 1 1s0 1-1 2h-1c0-1 0-2 1-3z" class="G"></path><path d="M153 256v3l-1 2c-1-1-1-1-1-2v-1c0-1 1-1 2-2z" class="M"></path><path d="M159 251v-2c1 1 2 1 2 2v1l1 1h1c-1 1-4 3-4 4v1c-2 2-5 4-8 5l1-1h0v-1l1-2v-3-4h1c1 1 1 2 1 3h1s1 0 1-1v-1h1l1-2z" class="N"></path><path d="M159 251v-2c1 1 2 1 2 2v1c-2 1-3 2-4 4l-1 1h0c-1-1-1-1-1-2h1s1 0 1-1v-1h1l1-2z" class="F"></path><path d="M162 253h1c-1 1-4 3-4 4v1c-2 2-5 4-8 5l1-1h0v-1l1-2c1-1 1-1 2-1s3-2 4-4c1 0 2-1 3-1z" class="Z"></path><path d="M164 276h4 1c2 1 4 2 5 4l1-1c1 1 0 2 0 3-1 3-4 6-7 7-5 2-19 5-23 3-2 0-3-1-4-3l1-1v1h6 3c1-1 3-5 4-7 1-1 2-2 4-3v-1h1l4-2z" class="J"></path><path d="M164 276h4c2 1 5 3 5 5 0 1 0 1-1 1h-1c0-1-1-2-2-3-2-2-3-2-5-3z" class="Z"></path><path d="M169 279c1 1 2 2 2 3l-1 1c-1 0-1 2-3 2-1 1-3 2-5 3-1 0-2 0-3 1 0-1 0-1-1-1l2-2 1-1h-1 0l-1 1c-1 0-2 0-3-1h0v-2c1 1 1 1 2 1h0c1 0 3-1 4 0 1 0 1 1 2 1 2 1 3-2 5-3v-1c1-1 0-1 0-2z" class="c"></path><path d="M164 276c2 1 3 1 5 3 0 1 1 1 0 2v1c-2 1-3 4-5 3-1 0-1-1-2-1-1-1-3 0-4 0h0c-1 0-1 0-2-1 2-2 3-4 4-5l4-2z" class="F"></path><path d="M163 278h3c1 0 1 1 1 1-1 2-2 2-4 3h-3v-1l3-3z" class="f"></path><defs><linearGradient id="CA" x1="180.715" y1="225.051" x2="182.793" y2="245.684" xlink:href="#B"><stop offset="0" stop-color="#373535"></stop><stop offset="1" stop-color="#525353"></stop></linearGradient></defs><path fill="url(#CA)" d="M194 213h0c1 1 0 1 1 1 1 1 1 0 2 1-1 1-1 1-2 1v2c-2 2-5 4-7 6h2c2-1 5-3 7-5v1c-1 1-3 2-4 3h1 0 1c1-1 1-1 2-1 0-1 2-2 3-2l-3 2c-1 1-3 2-4 3-1 0-1 1-2 2l-1 1s-1 1-1 2 0 1 1 2h0l2 2c0 2-1 3-2 4h-1c-1 1-2 1-2 1l-1-2c-1 0-1 1-2 1v1h-1c-3 5-9 7-13 11l-11 8v-1c0-1 3-3 4-4l4-4v2h-1c1 0 2-1 2-1 1-1 1-1 0-2h0c1-1 1-1 1-2h0c1-1 1-1 1-2h1c3-3 5-7 7-11l4-6c2-2 4-5 6-7l1-2 1-1c2-2 3-2 4-4z"></path><path d="M183 239c1-3 2-5 4-7h1v2c1 0 2 1 2 1 0 2 0 2-1 3s-2 1-2 1l-1-2c-1 0-1 1-2 1v1h-1z" class="Q"></path><defs><linearGradient id="CB" x1="179.332" y1="227.546" x2="188.582" y2="231.806" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#d8d8da"></stop></linearGradient></defs><path fill="url(#CB)" d="M194 213h0c1 1 0 1 1 1 1 1 1 0 2 1-1 1-1 1-2 1v2c-2 2-5 4-7 6l-11 15c-2 3-4 7-7 9 0-2 0-3 1-4 3-3 5-7 7-11l4-6c2-2 4-5 6-7l1-2 1-1c2-2 3-2 4-4z"></path><path d="M181 297c1 0 3 0 3-1 2 0 2-1 4 0v-1-1h1c1 0 1 1 2 2 1 0 2 1 3 0 2 2 5 3 7 4l2 1v1h-3 0c-1 0-1 0-2 1h1c0 1 0 2-1 3l-2 6-2 8-1 2c-1 1-2 1-3 1l1-2c0-2-1-4-3-6h1c0-2-1-3-2-4h0v-3c-1-2-2-4-4-5-1 0-2-1-3-1h-1c-1 1-2 0-3 0v-2l-1-1-19 1h-1c-3 0-7 0-11 1l-3 1h2l1 1c-3 1-7 2-10 3-2 0-5 1-7 2l-2 1v-1-1c-1 0-1-1-2-1h-3l1-1 3-2h-1s1 0 2-1h-4c-2 0-4 0-5-1-3 1-4 2-6 4h-1l-3-2 1-1h3c1-1 3-3 5-4h11c4 0 8-1 13-1 3 0 6 1 10 1l22-1h10z" class="W"></path><path d="M181 297c1 0 3 0 3-1 2 0 2-1 4 0v-1-1h1c1 0 1 1 2 2 1 0 2 1 3 0 2 2 5 3 7 4l2 1v1h-3 0-1c-6-2-11-4-18-5z" class="H"></path><defs><linearGradient id="CC" x1="182.84" y1="299.616" x2="182.898" y2="306.643" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#888686"></stop></linearGradient></defs><path fill="url(#CC)" d="M175 299h9c3 0 6 1 9 2 2 1 4 2 5 2h1c0 1 0 2-1 3v-2h-1 0c-1-1-1-1-3-1h0c-1 0-1-1-2 0-2 0-2 0-3 1v2h0c0 1 1 1 1 2h-3 0c-1-2-2-4-4-5-1 0-2-1-3-1h-1c-1 1-2 0-3 0v-2l-1-1z"></path><path d="M116 301h10c4 0 8-1 12-1 1 0 2 0 3 1h3l-3 1h2l1 1c-3 1-7 2-10 3-2 0-5 1-7 2l-2 1v-1-1c-1 0-1-1-2-1h-3l1-1 3-2h-1s1 0 2-1h-4c-2 0-4 0-5-1z" class="J"></path><path d="M125 307c5-3 10-4 16-5h2l1 1c-3 1-7 2-10 3-2 0-5 1-7 2l-2 1v-1-1z" class="i"></path><defs><linearGradient id="CD" x1="191.5" y1="318.25" x2="193.114" y2="303.905" xlink:href="#B"><stop offset="0" stop-color="#6b6a6b"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#CD)" d="M189 306v-2c1-1 1-1 3-1 1-1 1 0 2 0h0c2 0 2 0 3 1h0 1v2l-2 6-2 8-1 2c-1 1-2 1-3 1l1-2c0-2-1-4-3-6h1c0-2-1-3-2-4h0v-3h0 3c0-1-1-1-1-2h0z"></path><path d="M189 306v-2c1-1 1-1 3-1 1-1 1 0 2 0h0c2 0 2 0 3 1h0 1v2l-2 6s-1-1 0-1v-1h-1c1-2 1-3 1-5l-1-1h-4-1l-1 2z" class="F"></path><path d="M187 308h0c2 2 4 4 4 7 0 1 0 3 1 4 0 1 1 1 2 1l-1 2c-1 1-2 1-3 1l1-2c0-2-1-4-3-6h1c0-2-1-3-2-4h0v-3z" class="R"></path><defs><linearGradient id="CE" x1="195.054" y1="261.393" x2="165.627" y2="258.32" xlink:href="#B"><stop offset="0" stop-color="#5f5e5f"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#CE)" d="M190 232l2-1c0 1 1 1 1 1h2l19 5 6 3 2 2c2 1 3 3 4 5h0c1 3 1 6 1 9 0 0-1 0-1 1-2 2-4 6-6 8l-3 3s-1 0-1-1c0 1 0 2-1 3l-1 1c0 1-1 1-1 2h-1c0 1 0 2 1 4l-2 3-2 3v2c-2 3-3 6-5 9-3-3-5-6-8-8h0c-3-3-8-6-11-9l-9-4h-1v-1c-1-1 0-1 0-1 0-1 0-1-1-2-2 1-4 1-6 1h-6l1-1c1 0 3-1 3-3 1-1 1-1 0-2-2 0-2 1-4 2v-1c2-1 5-5 7-6 2-2 5-4 7-6h-1v-1c4-2 8-3 12-5h4l1-2-1-1h-1-1c-1 0-2 0-3 1h-2v-2c-1-1 0-2 0-4v-1c1 0 1-1 2-1l1 2s1 0 2-1h1c1-1 2-2 2-4l-2-2h0z"></path><path d="M172 262h2c-1 1-2 2-2 3h-1l-1-1 2-2zm7 5c0 2-3 4-4 6v-1c-1-1 0-1 0-1 0-1 0-1-1-2-2 1-4 1-6 1 1-1 2-1 3-1 1-1 2-1 2-1 1 0 1 1 2 1l-1-1h1c1 0 3-1 4-1z" class="F"></path><path d="M182 263c0-1 0-2 1-3h2c2 0 2 0 3 1v2 1h-2v3h0l-1 1c-1 0-2-1-4-1h-1v-2c0-1 1-2 2-2z" class="V"></path><path d="M180 267v-2c0-1 1-2 2-2 1 1 2 3 4 4l-1 1c-1 0-2-1-4-1h-1z" class="J"></path><path d="M199 261h2c0 2-3 5-4 6h0l-1 1c-2 1-6 1-8 1l-2-2v-3h2c1 0 3 1 4 1 2 0 4-3 7-4z" class="Y"></path><path d="M179 267h1 1c2 0 3 1 4 1l1-1h0l2 2h0v1l-2-1-1 1v4c1 1 2 2 2 3h-2l-9-4h-1c1-2 4-4 4-6z" class="Z"></path><path d="M179 267h1 1c0 2-2 3-3 5-1 0-1 0-2 1h-1c1-2 4-4 4-6z" class="T"></path><path d="M187 250c2 0 5-1 7 0h2c3 1 6 2 8 5 1 1 2 3 3 5 0 1 0 1-1 2v-3h-1v1l-3 4-3 3h-2c1-1 4-4 4-6h-2-1-1l-1 1-1-4c-2-3-3-4-4-7v-1h0c-2 1-2 1-4 0h0z" class="Q"></path><path d="M200 260c-1 0-1-1-2-1-1-1-1-1-1-3 0-1 0-1 1-2 1 0 1 0 2 1h0v2 3h0z" class="Z"></path><path d="M200 255v1c1 0 1 4 1 4h1l2-1 1 1-3 4-3 3h-2c1-1 4-4 4-6h-2-1c1 0 1 0 2-1h0v-3-2h0z" class="b"></path><defs><linearGradient id="CF" x1="207.811" y1="267.787" x2="185.139" y2="273.161" xlink:href="#B"><stop offset="0" stop-color="#4e4d4d"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#CF)" d="M205 260v-1h1v3c1-1 1-1 1-2l1 6c1 3 2 5 2 8v3c0 1 0 2 1 3l-2 3v2c-2 3-3 6-5 9-3-3-5-6-8-8h0c-3-3-8-6-11-9h2c0-1-1-2-2-3v-4l1-1 2 1v-1h0c2 0 6 0 8-1l1-1h0 2l3-3 3-4z"></path><path d="M189 272l2 1 1 1c-1 1-1 1-3 1-1 0-1-1-1-2s1-1 1-1z" class="E"></path><path d="M189 272h2l1-1-1-1 2 2h2v3s0 1-1 1h-2l-1 1v-1h0l3-1v-1h-2l-1-1-2-1z" class="N"></path><path d="M185 277h2c2 2 5 1 6 2s1 2 2 3c0 1 1 1 2 2 2 1 3 3 6 3 0 1 1 1 2 1v-1c1-1 2-2 4-2-2 3-3 6-5 9-3-3-5-6-8-8h0c-3-3-8-6-11-9z" class="P"></path><path d="M206 268h0 2c1 2 1 4 2 6v3c0 1 0 2 1 3l-2 3v2c-2 0-3 1-4 2h-2c-1-1-2-1-2-3 1 1 1 1 3 2l2-2c1-1 0-2 0-3 0-2-1-3-1-4v-1-2c-2-1-2-2-4-2l1-1c2 0 3-1 4-3z" class="Z"></path><path d="M206 268h0 2c1 2 1 4 2 6v3c0 1 0 2 1 3l-2 3c1-1 1-3 0-4v-3c-1-2-1-5-3-8z" class="P"></path><path d="M200 272h1c2 0 2 1 4 2v2 1c0 1 1 2 1 4 0 1 1 2 0 3l-2 2c-2-1-2-1-3-2h-1c-1-1-3-2-4-3h0c-1-3 0-5 1-7 1-1 2-1 3-2z" class="U"></path><path d="M196 281h0c-1-3 0-5 1-7 1-1 2-1 3-2v1c0 1 0 1-1 2v1h0c0 1 0 1-1 2 1 1 1 2 2 3h0c1 0 1 1 2 1s2-1 2-2c1-1 1-1 1-2 0 2 0 3-1 4s-2 1-3 1c-1-1-2-1-3-2h-1-1z" class="F"></path><path d="M200 272h1c2 0 2 1 4 2v2 1c0 1 1 2 1 4 0 1 1 2 0 3l-2 2c-2-1-2-1-3-2h-1c-1-1-3-2-4-3h1 1c1 1 2 1 3 2 1 0 2 0 3-1s1-2 1-4c-1-1-1-2-1-4-1 0-1-1-2-1h0 0c-1 0-1 0-2-1z" class="c"></path><defs><linearGradient id="CG" x1="215.246" y1="236.57" x2="199.162" y2="255.069" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#CG)" d="M190 232l2-1c0 1 1 1 1 1h2l19 5 6 3 2 2c2 1 3 3 4 5h0c1 3 1 6 1 9 0 0-1 0-1 1-2 2-4 6-6 8l-3 3s-1 0-1-1c0 1 0 2-1 3l-1 1c0 1-1 1-1 2h-1c0 1 0 2 1 4l-2 3c-1-1-1-2-1-3v-3c0-3-1-5-2-8l-1-6c-1-2-2-4-3-5-2-3-5-4-8-5h-2c-2-1-5 0-7 0l-4 1c-2 0-4 1-7 2h-1v-1c4-2 8-3 12-5h4l1-2-1-1h-1-1c-1 0-2 0-3 1h-2v-2c-1-1 0-2 0-4v-1c1 0 1-1 2-1l1 2s1 0 2-1h1c1-1 2-2 2-4l-2-2h0z"></path><path d="M201 242c2 0 4-1 5-1l-2 4c-1 1-2 1-3 1h-1-2l1-1h1 1s1 0 1-1v-1l-1-1z" class="E"></path><path d="M200 246h1c1 0 2 0 3-1 2 2 4 4 6 7-2-1-3-2-5-3l-5-3z" class="U"></path><path d="M218 244h1v3c0 1-1 2-2 3h0v-1c1-1 1-1 1-2l-2 2c-1 0-1 0-1-1h-1v-1h0l-1 1h0c0-1-1-1-1-3l1 1 2-1c1 0 1 1 2 1 1-1 1-1 1-2z" class="S"></path><path d="M189 244h4c3 0 6 0 8-2l1 1v1c0 1-1 1-1 1h-1-1-3c-2 1-1 2-4 0l-1-1h-1-1z" class="O"></path><path d="M184 239v-1c1 0 1-1 2-1l1 2s1 0 2-1h1 1 0l-1 1-2 2c-1 1-2 1-4 2h0c-1-1 0-2 0-4z" class="C"></path><path d="M213 260c2 2 4 4 7 5l-3 3s-1 0-1-1l-3-3v-1c-1-1 0-2 0-3z" class="V"></path><defs><linearGradient id="CH" x1="184.428" y1="244.725" x2="213.846" y2="275.366" xlink:href="#B"><stop offset="0" stop-color="#2d2b2c"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient></defs><path fill="url(#CH)" d="M192 245c3 2 2 1 4 0h3l-1 1h2l5 3c2 1 3 2 5 3l3 8c0 1-1 2 0 3v1l3 3c0 1 0 2-1 3l-1 1c0 1-1 1-1 2h-1c0 1 0 2 1 4l-2 3c-1-1-1-2-1-3v-3c0-3-1-5-2-8l-1-6c-1-2-2-4-3-5-2-3-5-4-8-5h-2c-2-1-5 0-7 0l-4 1c-2 0-4 1-7 2h-1v-1c4-2 8-3 12-5h4l1-2z"></path><path d="M192 245c3 2 2 1 4 0h3l-1 1h2l5 3c2 1 3 2 5 3l3 8c0 1-1 2 0 3v1l3 3c0 1 0 2-1 3l-1 1c0 1-1 1-1 2h-1c0-2-1-4-1-7-1-4-2-11-5-14-4-4-10-5-15-5l1-2z" class="G"></path><path d="M214 271v-2s-1-1-1-2v-2-1l3 3c0 1 0 2-1 3l-1 1z" class="R"></path><defs><linearGradient id="CI" x1="143.253" y1="345.984" x2="176.595" y2="352.123" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#CI)" d="M155 300h1l19-1 1 1v2c1 0 2 1 3 0h1c1 0 2 1 3 1 2 1 3 3 4 5v3h0c1 1 2 2 2 4h-1c2 2 3 4 3 6l-1 2c1 0 2 0 3-1l-2 5-3 16h-1-1l-3-4-1-2h-2v1l1 5 1 3h-1c-2 3-3 8-3 11 0 2 0 5-1 6s-1 1-1 2v1h-1l1-1c-1 0-2 1-2 2-3 5-7 12-12 16l-2 2c0-1-1-2-2-2h0l-1-1v-1c-1 1-2 2-2 3h-1 0c-3 0-7-6-10-9l-6-7c-2-2-5-3-7-5h0l-9-10-4-3c-2 0-5-2-8-2-3-1-7-3-11-3v-1h-2c1-1 3-1 4-1s2-1 3-1 2 0 3-1v-1l1-1 2 1h0v-1c-8-7-11-13-12-23 1-1 1-3 2-4s2-2 2-3l-2-1h3c1-1 1-3 2-4h0 1v-1l3 2h1c2-2 3-3 6-4 1 1 3 1 5 1h4c-1 1-2 1-2 1h1l-3 2-1 1h3c1 0 1 1 2 1v1 1l2-1c2-1 5-2 7-2 3-1 7-2 10-3l-1-1h-2l3-1c4-1 8-1 11-1z"></path><path d="M167 333h1c1 0 2 0 2 2l-1 1h-2c-1-1 0-1 0-2v-1z" class="X"></path><path d="M175 337c1 1 3 2 3 3v2c1 1 2 0 2 3h0c-1 0-2 1-2 1-1 1 0 4-1 6 0-5-1-10-2-15z" class="O"></path><path d="M175 336c0-1 0-1 1-1 1 1 2 1 3 1h0c1 1 1 2 1 2l1 5 1 3h-1l-1-1h0c0-3-1-2-2-3v-2c0-1-2-2-3-3v-1z" class="Y"></path><defs><linearGradient id="CJ" x1="180.773" y1="358.175" x2="175.474" y2="347.347" xlink:href="#B"><stop offset="0" stop-color="#b1aeaf"></stop><stop offset="1" stop-color="#d1d1d2"></stop></linearGradient></defs><path fill="url(#CJ)" d="M177 352c1-2 0-5 1-6 0 0 1-1 2-1l1 1c-2 3-3 8-3 11 0 2 0 5-1 6s-1 1-1 2v1h-1l1-1c-1 0-2 1-2 2 2-6 3-10 3-15z"></path><path d="M142 319l2-2h0v2c0 3 1 5 2 7h0c1 2 3 3 5 3 2 1 4 0 6-1 2-2 2-2 2-5v-1c-1-2-2-2-2-3l-1-2 1 1h1c1 0 4 2 5 3s2 1 2 2l1 1c1 0 1 2 2 2 2 3 4 7 5 10 0 1 1 2 1 3s0 1 1 2v1 2c-1-1-1-3-1-4 0 1 0 2-1 3v-1-2-1c0-1 0-1-1-2s-1-3-2-5h0c-1-1-1-2-2-3 0 0-1 1-1 2h0 0l-1 1c-3 2-2 0-4 0h-3 1-1-2l-2 2 1 1h0c1 0 2 1 2 1-2 0-4 0-6 1l-1-1 1-1-1-1c-2 0-3-1-5-2 0-1 0-1-1-1s0 0-1 1c0-2 1-2 2-3-1-1-2-1-2-2-2-3-2-5-2-8z" class="K"></path><path d="M162 326v-3-1c2 0 2 1 3 2-1 1-2 1-3 2z" class="P"></path><path d="M162 326c1-1 2-1 3-2l6 9c1 2 2 4 3 7 0 1 0 2-1 3v-1-2-1c0-1 0-1-1-2s-1-3-2-5h0c-1-1-1-2-2-3 0 0-1 1-1 2h0 0l-1 1c-3 2-2 0-4 0h-3 1-1-2l-2 2 1 1h0c1 0 2 1 2 1-2 0-4 0-6 1l-1-1 1-1-1-1c-2 0-3-1-5-2 0-1 0-1-1-1s0 0-1 1c0-2 1-2 2-3 1 1 2 1 4 2h0 3 4l1-1 2-1c1-1 1-2 2-3z" class="Z"></path><path d="M160 329c1 0 2 0 2-1 2 0 1-1 3-1 1 1 1 1 2 3h0l-2 2c-2 0-2-1-3-1-1-1-2 0-4-1l2-1z" class="X"></path><path d="M146 332l1-1s1 0 2 1h2c1 0 2 0 2 1h1c1-1 2-1 3-1l-2 2 1 1h0c1 0 2 1 2 1-2 0-4 0-6 1l-1-1 1-1-1-1c-2 0-3-1-5-2zm23 22v5h0v1c-1 1-1 2-1 3-1 3-1 5-5 8-2-1-4-3-7-3h1c0 1 1 1 2 2h1c0 1 1 2 2 2h2v1 1c0 1-1 3-3 4h-2c-1 0-1-1-2-1l-2 2-2-2c-2-2-5-4-7-7 0-1 0-2-1-3v-2c3-1 9-1 12-1h0c1 1 1 1 2 1h1l1-1c4-1 6-6 8-10z" class="Q"></path><path d="M145 367v-2c3-1 9-1 12-1h0c1 1 1 1 2 1h1c-3 1-6 1-9 2-1 0-2 0-3 1-1 0-2-1-3-1z" class="d"></path><path d="M153 377l5-2c-2 0-5-1-6-2s-1-2-1-2l-1-1-1-1c2 0 4-1 6-1h1 1c0 1 1 1 2 2h1c0 1 1 2 2 2h2v1 1c0 1-1 3-3 4h-2c-1 0-1-1-2-1l-2 2-2-2z" class="b"></path><path d="M155 369h1c-1 1-1 2-3 2v-1c0-1 1-1 2-1z" class="F"></path><path d="M155 368h1 1c0 1 1 1 2 2h1c0 1 1 2 2 2h2v1 1c-1 0-1 0-1 1l-2 2c-1-1-1-1-1-2h0l-1-1v-1h-1-2c-1 0-2-1-3-2 2 0 2-1 3-2h-1v-1z" class="T"></path><defs><linearGradient id="CK" x1="152.346" y1="337.53" x2="151.418" y2="361.741" xlink:href="#B"><stop offset="0" stop-color="#adacad"></stop><stop offset="1" stop-color="#dad9da"></stop></linearGradient></defs><path fill="url(#CK)" d="M157 332h2 1c1 1 2 1 2 2h0c1 2 2 3 3 5 1 1 2 2 2 4 2 4 3 7 2 11-2 4-4 9-8 10l-1 1h-1c-1 0-1 0-2-1h0c-3 0-9 0-12 1v2c1 1 1 2 1 3h0c-2-1-3-3-4-5 1-1 3-1 4-3l-6-1c-2-1-3-2-5-2v-1h2 1c5 2 11 3 16 2 4-1 6-3 8-7l1-1c0-3-1-7-3-10v-1c-1-1-2-2-2-3 0 0 1 0 2-1l-2-1s-1-1-2-1h0l-1-1 2-2z"></path><path d="M146 362h1c3 0 6 1 9 0h1l1-1h0c1 0 1-1 2-1s1-1 1-1h1l1-1c0-1 0-2 1-2 0 2 0 2-1 4-2 2-4 3-6 4-3 0-9 0-12 1v2c1 1 1 2 1 3h0c-2-1-3-3-4-5 1-1 3-1 4-3z" class="L"></path><defs><linearGradient id="CL" x1="154.398" y1="363.365" x2="164.853" y2="335.173" xlink:href="#B"><stop offset="0" stop-color="#242223"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#CL)" d="M157 332h2 1c1 1 2 1 2 2h0c1 2 2 3 3 5 1 1 2 2 2 4 2 4 3 7 2 11-2 4-4 9-8 10l-1 1h-1c-1 0-1 0-2-1h0c2-1 4-2 6-4 1-2 1-2 1-4l2-3c1-3 1-6 0-9-1-2-3-6-6-7l-2-1s-1-1-2-1h0l-1-1 2-2z"></path><path d="M154 311c2 0 4-1 6 0s5 3 7 5c6 6 9 12 12 20h0c-1 0-2 0-3-1-1 0-1 0-1 1 0-1-1-2-1-2-3-7-9-16-17-19-2-1-5 0-7 1-1 0-2 2-2 3v1c1 1 5-2 7-2l1-1 1 2c0 1 1 1 2 3v1c0 3 0 3-2 5-2 1-4 2-6 1-2 0-4-1-5-3h0c-1-2-2-4-2-7 1-3 2-4 5-6 2-1 3-2 5-2z" class="L"></path><path d="M156 317l1 2c0 1 1 1 2 3v1c0 3 0 3-2 5v-1h0c-1-1-2 0-3 0h-1c-1 0-2 0-3-1-2-1-2-3-3-5 0-1 0-1 1-2v1c1 1 5-2 7-2l1-1z" class="c"></path><path d="M154 327c1-1 2-1 3-3 0 0 0-1 1-1h1c0 3 0 3-2 5v-1h0c-1-1-2 0-3 0z" class="O"></path><path d="M156 317l1 2c0 1 1 1 2 3l-2-1h-1l-1 1c0 1 0 2-1 3h0l-1-1c-2 0-3-1-5-2 0 0 0-1-1-1 0-1 0-1 1-2v1c1 1 5-2 7-2l1-1z" class="Q"></path><path d="M153 324l1-1v-1h-1c-1-1-1-1-1-3h1c1-1 2-1 4 0v2h-1l-1 1c0 1 0 2-1 3h0l-1-1z" class="R"></path><path d="M110 338l3 2h2 1c1 2 3 5 5 7s11 11 14 12c2 0 3 1 5 2l6 1c-1 2-3 2-4 3 1 2 2 4 4 5h0c2 3 5 5 7 7l2 2c1 0 2 1 2 2h0c-1 1-2 2-2 3h-1 0c-3 0-7-6-10-9l-6-7c-2-2-5-3-7-5h0l-9-10-4-3c-2 0-5-2-8-2-3-1-7-3-11-3v-1h-2c1-1 3-1 4-1s2-1 3-1 2 0 3-1v-1l1-1 2 1h0v-1-1z" class="B"></path><path d="M108 339l2 1v1l3 1c4 2 9 7 11 10v1h1c1 2 2 3 3 4h0-1s-1 0-1-1h-1c0-1 0-1-1-2h-1l-1-1-4-3c-2 0-5-2-8-2-3-1-7-3-11-3v-1h-2c1-1 3-1 4-1s2-1 3-1 2 0 3-1v-1l1-1z" class="O"></path><path d="M99 344c5-1 12 1 17 4 1 0 1 1 2 2-2 0-5-2-8-2-3-1-7-3-11-3v-1z" class="b"></path><defs><linearGradient id="CM" x1="143.29" y1="360.79" x2="144.179" y2="380.93" xlink:href="#B"><stop offset="0" stop-color="#a6a6a7"></stop><stop offset="1" stop-color="#cac8c8"></stop></linearGradient></defs><path fill="url(#CM)" d="M140 361l6 1c-1 2-3 2-4 3 1 2 2 4 4 5h0c2 3 5 5 7 7l2 2c1 0 2 1 2 2h0c-1 1-2 2-2 3h-1 0c-3 0-7-6-10-9l-6-7c-2-2-5-3-7-5v-2h2l1 1c1 1 1 1 2 1 1 1 2 1 3 2h0c1 0 2 1 3 2h0c0-1-1-1 0-2v-1c1-1 2-1 2-1-1-1-3-1-4-2h0z"></path><path d="M138 368c2 1 4 1 5 3s3 3 4 5c2 3 5 4 6 7l1 1h0c-3 0-7-6-10-9l-6-7z" class="B"></path><defs><linearGradient id="CN" x1="143.988" y1="359.333" x2="154.387" y2="319.639" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#7a797a"></stop></linearGradient></defs><path fill="url(#CN)" d="M138 321c2-1 3-3 4-4h1c-1 1-1 1-1 2 0 3 0 5 2 8 0 1 1 1 2 2-1 1-2 1-2 3 1-1 0-1 1-1s1 0 1 1c2 1 3 2 5 2l1 1-1 1 1 1c2-1 4-1 6-1l2 1c-1 1-2 1-2 1 0 1 1 2 2 3v1c2 3 3 7 3 10l-1 1c-2 4-4 6-8 7-5 1-11 0-16-2h0c0-1 3 0 4 0 2 1 4 1 6 0h-5c-3-1-9-2-10-4s0-2 0-3v-1c1-2 3-3 4-5-1-3 0-7 0-11s0-9 1-13z"></path><path d="M155 348v-1h1l1 1c0 1-1 1-1 2h-2l1-2z" class="Q"></path><path d="M152 345v-1c1-1 2-1 3-1v2h-1-2z" class="J"></path><path d="M154 350h2c-1 1-2 2-3 2-1 1-1 1-3 0l1-1h-1l2-1h2z" class="I"></path><path d="M146 342v3c1 1 1 1 3 2v-1 1c1 0 2 2 4 1l1-1c0 1 0 0 1 1l-1 2h-2-2c-1 0-2 0-3-1-2-3-2-4-1-7z" class="M"></path><path d="M152 337c2-1 4-1 6-1l2 1c-1 1-2 1-2 1 0 1 1 2 2 3v1c-2-2-4-3-7-2-2 0-3 1-4 2v3h0v1 1c-2-1-2-1-3-2v-3c1-2 2-3 4-4l2-1z" class="G"></path><path d="M152 337c2-1 4-1 6-1l2 1c-1 1-2 1-2 1h-7 0-1l2-1z" class="L"></path><path d="M149 345h0v-3c1-1 2-2 4-2 3-1 5 0 7 2 2 3 3 7 3 10l-1 1h-1v-3c-1-2-1-4-3-5-1-2-1-2-3-2-1 0-2 0-3 1v1 2h-1l-2-2z" class="W"></path><path d="M133 351c2-2 4-3 6-4 3 0 3 0 5 2v1l1 1c2 1 2 1 3 3h1c2 0 3 1 5 2v1c-1 1-3 1-5 1h-1-5c-3-1-9-2-10-4s0-2 0-3z" class="J"></path><defs><linearGradient id="CO" x1="140.717" y1="355.576" x2="140.953" y2="350.734" xlink:href="#B"><stop offset="0" stop-color="#6c6a6b"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#CO)" d="M138 350h3c1 1 2 3 3 4 1 0 2 1 3 1v1 1c-1 0-2 0-4-1-1 0-3 0-4-1-1 0-4-1-5-2l1-1c0-1 2-2 3-2z"></path><path d="M110 305c2-2 3-3 6-4 1 1 3 1 5 1h4c-1 1-2 1-2 1h1l-3 2-1 1h3c1 0 1 1 2 1v1 1l2-1c2-1 5-2 7-2 1 0 2 1 3 2h0v1l-2 6-6 12c-1 2-2 4-2 6 1 1 0 3 1 3v1l-2 2c1 1 1 1 1 2l1-1 1 1c1 1 1 2 0 3 0 1 0 2 1 3h-1c0 1-1 1 0 1 0 1 1 2 2 2h2v1c0 1-1 1 0 3s7 3 10 4h5c-2 1-4 1-6 0-1 0-4-1-4 0h0-1-2v1c-3-1-12-10-14-12s-4-5-5-7h-1-2l-3-2v1c-8-7-11-13-12-23 1-1 1-3 2-4s2-2 2-3l-2-1h3c1-1 1-3 2-4h0 1v-1l3 2h1z" class="D"></path><path d="M109 305h1v1l-2 3v-1h-1c0-2 1-2 2-3z" class="B"></path><path d="M112 330h1v1c0 1 0 2-1 2s-2-1-2-2v-1h2z" class="L"></path><path d="M110 336c2 0 4 1 5 3v1h-2l-3-2v-2z" class="H"></path><path d="M120 343c1 1 3 2 5 1l1 1c1 0 1 0 1-1h2c0 1 0 2 1 3h-1c0 1-1 1 0 1 0 1 1 2 2 2h2v1c0 1-1 1 0 3s7 3 10 4h5c-2 1-4 1-6 0-1 0-4-1-4 0h0-1s-2-1-2-2c-5-3-12-8-15-13z" class="K"></path><path d="M103 308l1 1 1-1v1c-1 0-1 1-1 2-1 1-1 2-1 2 0 2-1 3-1 4v4c-1 1 0 2 0 2 2 5 4 9 6 13h1v-3h0l1 2v1 2 1c-8-7-11-13-12-23 1-1 1-3 2-4s2-2 2-3l-2-1h3z" class="O"></path><defs><linearGradient id="CP" x1="115.53" y1="331.391" x2="126.053" y2="330.035" xlink:href="#B"><stop offset="0" stop-color="#a3a1a2"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#CP)" d="M114 327h1v-1l-1-1v-2h1l1 1h1v-1-1h1c-1 3 0 7 1 10l2 2 1 1h1c2 0 3 0 4-2 1 1 0 3 1 3v1l-2 2c1 1 1 1 1 2l1-1 1 1c1 1 1 2 0 3h-2c0 1 0 1-1 1l-1-1c-2 1-4 0-5-1-2-3-4-6-5-9v-3c-1-1-1-2-1-4z"></path><path d="M128 336v1l-2 2-1 1-1-1 1-1 3-2z" class="a"></path><path d="M114 327c2 1 2 1 2 3l1 4h-2v-3c-1-1-1-2-1-4z" class="U"></path><defs><linearGradient id="CQ" x1="125.077" y1="344.766" x2="121.097" y2="334.765" xlink:href="#B"><stop offset="0" stop-color="#373637"></stop><stop offset="1" stop-color="#51504f"></stop></linearGradient></defs><path fill="url(#CQ)" d="M115 334h2c1 2 2 4 4 5s4 2 6 2h0l1-1 1 1c1 1 1 2 0 3h-2c0 1 0 1-1 1l-1-1c-2 1-4 0-5-1-2-3-4-6-5-9z"></path><path d="M121 339c2 1 4 2 6 2h0c-1 1-2 1-4 1-1 0-1-1-2-1v-2z" class="T"></path><path d="M110 305c2-2 3-3 6-4 1 1 3 1 5 1h4c-1 1-2 1-2 1h1l-3 2-1 1h3c1 0 1 1 2 1v1 1c-1 1-2 1-3 3v1h1v-1c1 0 2 0 3-1l1 1c-2 1-4 2-6 4l-1 1h-2c-1 0-2 1-2 2l-1 1v1h-1l1-1h-1v2h-1c0 2-1 6-1 8h-2 1l-2-3c-2-6-2-12-1-18l2-3v-1z" class="Z"></path><path d="M119 312c1-2 4-3 6-4v1c-1 1-2 1-3 3h-3z" class="L"></path><path d="M123 303h1l-3 2c-2 0-4 2-6 2v-2c3-2 5-2 8-2z" class="c"></path><path d="M122 312v1h1v-1c1 0 2 0 3-1l1 1c-2 1-4 2-6 4l-1 1h-2c-1 0-2 1-2 2l-1 1v1h-1l1-1h-1v2h-1c1-4 3-8 6-10h3z" class="V"></path><path d="M110 306c1 0 2 0 3 1v2c-2 3-2 7-2 10 0 2 0 7-1 8h-1c-2-6-2-12-1-18l2-3z" class="W"></path><defs><linearGradient id="CR" x1="135.421" y1="322.563" x2="121.102" y2="324.03" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#CR)" d="M134 306c1 0 2 1 3 2h0v1l-2 6-6 12c-1 2-2 4-2 6-1 2-2 2-4 2h-1l-1-1-2-2c-1-3-2-7-1-10l3-6c2-2 4-3 6-4l-1-1c-1 1-2 1-3 1v1h-1v-1c1-2 2-2 3-3l2-1c2-1 5-2 7-2z"></path><path d="M123 332c0 1 1 2 1 3h-2l-1-1c0-1 1-2 2-2z" class="Q"></path><path d="M121 330l2 2c-1 0-2 1-2 2l-2-2c1-1 2-1 2-2z" class="P"></path><defs><linearGradient id="CS" x1="124.643" y1="312.008" x2="129.551" y2="327.599" xlink:href="#B"><stop offset="0" stop-color="#313030"></stop><stop offset="1" stop-color="#4b4b4d"></stop></linearGradient></defs><path fill="url(#CS)" d="M134 306c1 0 2 1 3 2h0c0 1 0 1-1 2 0 0-1 0-2 1s-3 1-4 2c-4 2-8 8-9 12v5c0 1-1 1-2 2-1-3-2-7-1-10l3-6c2-2 4-3 6-4l-1-1c-1 1-2 1-3 1v1h-1v-1c1-2 2-2 3-3l2-1c2-1 5-2 7-2z"></path><path d="M134 306c1 0 2 1 3 2-3 0-7 2-10 4l-1-1c-1 1-2 1-3 1v1h-1v-1c1-2 2-2 3-3l2-1c2-1 5-2 7-2z" class="B"></path><path d="M155 300h1l19-1 1 1v2c1 0 2 1 3 0h1c1 0 2 1 3 1 2 1 3 3 4 5v3h0c1 1 2 2 2 4h-1c2 2 3 4 3 6l-1 2c1 0 2 0 3-1l-2 5-3 16h-1-1l-3-4-1-2h-2v1s0-1-1-2c-3-8-6-14-12-20-2-2-5-4-7-5s-4 0-6 0-3 1-5 2c-3 2-4 3-5 6v-2h0l-2 2c0-1 0-1 1-2h-1c-1 1-2 3-4 4-1 4-1 9-1 13s-1 8 0 11c-1 2-3 3-4 5h-2c-1 0-2-1-2-2-1 0 0 0 0-1h1c-1-1-1-2-1-3 1-1 1-2 0-3l-1-1-1 1c0-1 0-1-1-2l2-2v-1c-1 0 0-2-1-3 0-2 1-4 2-6l6-12 2-6v-1h0c-1-1-2-2-3-2 3-1 7-2 10-3l-1-1h-2l3-1c4-1 8-1 11-1z" class="C"></path><path d="M145 309l5-1-3 2-5 3 1-2c0-1 1-1 2-2z" class="G"></path><path d="M184 318h1 2c1 2 2 3 3 3h1 0l-1 2h0v1c-2-1-5-4-6-6z" class="B"></path><path d="M130 340v2c1-1 1-2 1-2h1 0c1-1 1-2 1-2 0 3-1 7-3 9-1-1-1-2-1-3 1-1 1-2 0-3l-1-1h2z" class="U"></path><path d="M138 321s1-1 1-2c2-3 6-6 10-6-3 2-4 3-5 6v-2h0l-2 2c0-1 0-1 1-2h-1c-1 1-2 3-4 4z" class="e"></path><path d="M150 308c4-1 9-2 13-2s8 2 12 4l1 2c-9-4-19-5-29-2l3-2z" class="H"></path><defs><linearGradient id="CT" x1="172.482" y1="327.934" x2="182.067" y2="318.745" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#CT)" d="M154 311c3-1 5-1 7-1 4 0 9 2 12 3 4 2 7 4 9 6l6 6c2 0 3 1 3 2l-3 16h-1-1l-3-4-1-2h-2v1s0-1-1-2c-3-8-6-14-12-20-2-2-5-4-7-5s-4 0-6 0z"></path><path d="M173 313c4 2 7 4 9 6l6 6-2 2h0c-1-2-1-3-2-4 0-1-1-1-2-2-1-2-4-5-6-6-1 0-2-1-3-2z" class="X"></path><path d="M155 300h1l19-1 1 1v2c1 0 2 1 3 0h1c1 0 2 1 3 1 2 1 3 3 4 5v3h0c1 1 2 2 2 4h-1c2 2 3 4 3 6h0-1c-1 0-2-1-3-3h-2-1l-2-2-2-1-4-3-1-2c-4-2-8-4-12-4s-9 1-13 2l-5 1c-1 1-2 1-2 2l-1 2c-5 4-7 9-8 15v2c0 2 0 5-1 8h0s0 1-1 2h0-1s0 1-1 2v-2h-2l-1 1c0-1 0-1-1-2l2-2v-1c-1 0 0-2-1-3 0-2 1-4 2-6l6-12 2-6v-1h0c-1-1-2-2-3-2 3-1 7-2 10-3l-1-1h-2l3-1c4-1 8-1 11-1z" class="V"></path><path d="M185 318c0-2 1-2 1-3h1v1c1 1 1 1 0 2h-2z" class="Y"></path><path d="M137 309l1 1 1-1h2 1c-2 2-5 3-6 5l-1 1 2-6z" class="B"></path><path d="M187 316l1-1-3-3v-1l3 4c2 2 3 4 3 6h0-1c-1 0-2-1-3-3 1-1 1-1 0-2zm-58 11c1 1 3 1 4 3v1c-1 0-1 0-2 1v2c0 1-1 1-2 2 0 1 2 1 2 3l-1 1h-2l-1 1c0-1 0-1-1-2l2-2v-1c-1 0 0-2-1-3 0-2 1-4 2-6z" class="L"></path><path d="M145 309h0c0-2 2-2 4-3 6-1 14-3 20-1 2 1 5 2 7 3 1 1 4 2 5 4v2l-1 1-4-3-1-2c-4-2-8-4-12-4s-9 1-13 2l-5 1z" class="Y"></path><defs><linearGradient id="CU" x1="176.324" y1="307.558" x2="178.722" y2="301.38" xlink:href="#B"><stop offset="0" stop-color="#4a494a"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#CU)" d="M155 300h1l19-1 1 1v2c1 0 2 1 3 0h1c1 0 2 1 3 1 2 1 3 3 4 5v3h0c1 1 2 2 2 4h-1l-3-4h-1c-4-5-12-9-19-10-3-1-6-1-10-1z"></path><path d="M327 292v13l1 18c1 0 1 0 2-1l2-1 2-2 1-1c0-2 1-4 2-6h0c1-1 1-1 1-2s0-1 1-2h1v2l1-2 1-1h1l4-4c1 0 2-1 3-2 2-1 3-2 4-3 0-1 1-1 1-1l1 1-3 3c1 1 1 2 2 2 3-2 7-4 11-5 1 0 1-1 3 0-1 1-1 1-2 1v1h-1v1c-1 1-4 2-5 4v2 2l-1 1v1h1c2 0 3 0 4 1l1 1c0 2 1 3 2 4l2-2v-1l7 3v79c0 3-1 6 0 8v37 11c0 1 0 4-1 5v1h-1l1 1 1 10c0 2-1 4 0 5v13l-1 1-1-1-1 2-1 1-3 3c-2 1-3 3-5 4h0c-1 1-1 2-2 2h-1l-7 7-8 6-10 10h-1c0 1-1 2-2 3 1 0 1-1 2-2h1s1-1 2-1l1-1c2 2 3 1 5 3 0 0 0 1 1 2-2 1-4 2-5 5h0v1h-2 0c0 1-1 2-2 2-1 1-1 2-2 2l-2 1v1l-2-2h-1c-1 1-2 1-2 2l-1-1c-2 1-3 3-5 4 0 1-2 4-3 4l-2 2v-88c0-2 0-4 1-5v-1c-1 0-1 0-1-1v-3-8-34-25-10-4c1 0 1-1 2-1l1-1c1 1 1 1 2 1v-1h1v1l1 1 1-1 1-1-2-1v-1l2 1 1-1v-4l-1-31v-5-13-5c2-4 0-9 1-14z" class="g"></path><path d="M361 470c1-2 1-6 3-8h0l1-1h0l2-2c0 1-2 3-2 4 0 2 0 4-2 5v1l-2 1z" class="i"></path><path d="M336 410l2-1 1-1h0l1-1v-1l1 1h0c-2 2-2 2-2 4 0 1-1 2-1 3h-1c0 1-1 2-2 3v1-1-1-1c1 0 0-1 0-2 1-1 1-2 1-3z" class="a"></path><path d="M332 426v-1c0-1 1-2 0-3h-1v-1c1 0 1-1 1-1l1-1c0-1 1-2 2-3v1c-1 1-2 2-2 4h0c1 0 1-1 2-1h0c1 0 1-1 1-1h1 0 1c2 1 0 2 2 4-2 1-5 3-8 3h0z" class="D"></path><path d="M361 333s1-1 2-1h2v1l-1 2h1s1 0 1-1h2c0 1 0 1-1 2v1 2 1l-1 1h0c1 1 2 0 3-1l2-1h0l1 2h-1l-5 4-2 2h-1c1-1 3-3 4-3v-1-1c-1 1-2 1-3 2v-1-1-1h-1c-1 0-2 2-3 3l-1-1c2-1 4-2 5-4 1-1 2-2 3-4-1 0-2 1-3 2-2 1-4 2-5 3v-1h0l2-2h1l1-1h0v-2c-1-1-1-1-2-1z" class="V"></path><path d="M368 378c0 1 1 2 1 3l-1 1v1c1 0 1 0 2 1 0 3-1 6 0 9v2c-1 2-1 4-1 6h-1-1-1c0 1-1 2-2 1 1-1 1-4 3-6 1-1 1-2 2-3-1 0 0-1-1-2s-1 0-2 0c1-2 1-3 2-4v-2h-3-1c1-1 1-1 1-2h0l1-1 1-1h1v-3z" class="D"></path><path d="M369 440c-1 0-2 0-3-1 1 0 1-1 1-1l1 1h2c1 1 1 3 2 4 0 1-1 2-1 3-1 1-1 2 0 3v1c0 1-1 2-1 3s1 1 1 2c1 1 1 1 0 3h-1l-2 2h1 1c1 1 1 1 1 2l-2 2v3l1 1v1 3c1 2 0 3-1 4h-1l-2 2c0-1 0-1 1-2h0v-1-1-1h-1c1-2 2-2 2-4-1 0-1 0-2 1h-1-1l-1-1v-1c2-1 2-3 2-5 0-1 2-3 2-4l2-2h1v-1h-1c-2 0-2 1-4 0h1 1 0c1-1 1-2 1-3h-1s0-1 1-1h0v-1c1 0 1 0 2-1h-1l-1-1-1 1h0-1l2-2v-1s0-1 1-1v-1-2h0v-1h0v-1-1h0z" class="L"></path><defs><linearGradient id="CV" x1="369.5" y1="355.711" x2="362.288" y2="358.624" xlink:href="#B"><stop offset="0" stop-color="#a3a2a2"></stop><stop offset="1" stop-color="#c0bfbf"></stop></linearGradient></defs><path fill="url(#CV)" d="M363 347h1c0 2 0 3 1 5v1c-1 1-2 2-3 4 2-1 5-4 7-4h1c0 2 0 2-1 4h0c0 2 0 2 1 3h0c1 1 1 2 0 3h0c0 1 0 2 1 3v2c-1 1-2 1-3 2-2 1-6 3-7 5l-1 1h0-1c1-1 1-2 2-3l3-1c1-2 3-2 5-3v-1l-2-2-3 3s-1 1-2 1l1-1 2-2c0-1 1-2 2-2l1-1v-1s-2-1-2 0h-1-2c-1 0-2 1-2 1h0c0-2 1-1 2-2h2 0c0-1 0-1-1-3h1 0c-2 1-4 0-6 1h-1 0v-1h1-1c-1 1-3 1-4 2 2-2 4-3 6-5v-1l1-1c1-1 0 1 1-1v1 1l1-1h0v-1h0c1-1 0-2 0-3h-1v-1c-2 0-4 3-5 4h-1l7-6z"></path><path d="M369 405h1c0 1 0 3-1 4v1 1h0c1 2 1 2 1 4l-2 2c1 0 1 0 1 1s-1 2-2 4h1 0 2c1 1 1 2 1 3s-1 2-1 2c0 1 1 1 1 2 1 1 0 5-1 7l-3 2s0 1-1 1c1 1 2 1 3 1-2 0-3 1-4 1-1 1-3 1-5 1 0-1 4-2 5-4s3-3 3-5h0c0-1 0-1 1-1v-1-2c-2 1-4 2-7 3 2-2 3-2 4-4 1 0 1-1 2-1h0v-1h1c0-1 0-1-1-2-1 1-3 2-5 2h-2c2-1 1-2 1-4 1-2 4-3 4-6h1v-1h2c0-1-1-2-1-2-1 0-1-1-2-1 1-1 1-1 1-2h1v-2c1-1 0 0 0-1l1-1h-1c-1 1-2 2-3 2h-2v-2h0 1v1 1c1 0 1 0 1-1 1 0 1-1 2-1l1-1h1z" class="Y"></path><path d="M361 375c1-2 5-4 7-5 1 0 2 1 2 2h1c0 2-1 3 0 4v2c0 2-1 1-1 3h0l2 1c0 2-1 7 0 9v2c0 1 0 2-1 3 0 2 0 6 1 7 0 2-1 5 0 7l1 1s-1 2-1 3v1c1 1 1-1 1 1 0 1-1 1 0 3 0 1 1 2 1 3v4c-1 2 0 5 0 7-1 1-2 2-3 2l-1 1c1-2 2-6 1-7 0-1-1-1-1-2 0 0 1-1 1-2s0-2-1-3h-2 0-1c1-2 2-3 2-4s0-1-1-1l2-2c0-2 0-2-1-4h0v-1-1c1-1 1-3 1-4h-1c-1-1 0-1-1-1v-1c1-1 1-2 1-2 0-2 0-4 1-6v-2c-1-3 0-6 0-9-1-1-1-1-2-1v-1l1-1c0-1-1-2-1-3-1-1-3-3-5-4v2l-2-1z" class="O"></path><defs><linearGradient id="CW" x1="389.108" y1="382.642" x2="361.232" y2="388.39" xlink:href="#B"><stop offset="0" stop-color="#353232"></stop><stop offset="1" stop-color="#6b6c6b"></stop></linearGradient></defs><path fill="url(#CW)" d="M370 314l7 3v79c0 3-1 6 0 8v37 11c0 1 0 4-1 5v1h-1-2c0-1-1 0-2 0 1-2 1-2 0-3 0-1-1-1-1-2s1-2 1-3v-1c-1-1-1-2 0-3 0-1 1-2 1-3-1-1-1-3-2-4h-2l-1-1 3-2 1-1c1 0 2-1 3-2 0-2-1-5 0-7v-4c0-1-1-2-1-3-1-2 0-2 0-3 0-2 0 0-1-1v-1c0-1 1-3 1-3l-1-1c-1-2 0-5 0-7-1-1-1-5-1-7 1-1 1-2 1-3v-2c-1-2 0-7 0-9l-2-1h0c0-2 1-1 1-3v-2c-1-1 0-2 0-4h-1c0-1-1-2-2-2 1-1 2-1 3-2v-2c-1-1-1-2-1-3h0c1-1 1-2 0-3h0c-1-1-1-1-1-3h0c1-2 1-2 1-4h-1c-2 0-5 3-7 4 1-2 2-3 3-4v-1c-1-2-1-3-1-5l2-2 5-4h1v-2l-1-1v-1c0-2 0-4-1-6 1-1 1-1 1-2s-1-1-1-2v-3c0-2-1-4-2-5h-2 0l2-2 2-2v-1z"></path><path d="M370 436l1-1c2 3 2 4 2 7 1 4 1 8 1 11v4l1 1h1-1-2c0-1-1 0-2 0 1-2 1-2 0-3 0-1-1-1-1-2s1-2 1-3v-1c-1-1-1-2 0-3 0-1 1-2 1-3-1-1-1-3-2-4h-2l-1-1 3-2z" class="B"></path><path d="M366 345l5-4c0 2-2 4-2 6h2v2c1 3 1 4 0 6v3c1 0 2 1 2 1v2c-1 3 0 4-1 7v1 7 6c1 3 1 8 1 11v4 11c1 1 1 2 1 3s-1 2-2 3c0-1 1-3 1-3l-1-1c-1-2 0-5 0-7-1-1-1-5-1-7 1-1 1-2 1-3v-2c-1-2 0-7 0-9l-2-1h0c0-2 1-1 1-3v-2c-1-1 0-2 0-4h-1c0-1-1-2-2-2 1-1 2-1 3-2v-2c-1-1-1-2-1-3h0c1-1 1-2 0-3h0c-1-1-1-1-1-3h0c1-2 1-2 1-4h-1c-2 0-5 3-7 4 1-2 2-3 3-4v-1c-1-2-1-3-1-5l2-2z" class="M"></path><path d="M366 345c0 2-1 4 0 5h1v1c0 1-1 1-2 2h0v-1c-1-2-1-3-1-5l2-2z" class="f"></path><defs><linearGradient id="CX" x1="361.585" y1="496.725" x2="339.291" y2="483.933" xlink:href="#B"><stop offset="0" stop-color="#c1c0c1"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#CX)" d="M371 458c1 0 2-1 2 0h2l1 1 1 10c0 2-1 4 0 5v13l-1 1-1-1-1 2-1 1-3 3c-2 1-3 3-5 4h0c-1 1-1 2-2 2h-1l-7 7-8 6h-1l3-3c-3 1-5 3-8 5-2 1-3 2-5 1l-1-1c1-2 2-2 1-5v-1-1-2-3l2-2h0v-4c-1-1-1-1-1-2l1-2v-1-1l1-3h-1v-1c2-1 3-2 5-3v-1l2-3c1 0 1 0 2-1h0 1c1-1 2-1 3-2l1-1h1c1-1 2-1 3-1 2-1 3-3 5-4l2-1 1 1h1 1c1-1 1-1 2-1 0 2-1 2-2 4h1v1 1 1h0c-1 1-1 1-1 2l2-2h1c1-1 2-2 1-4v-3-1l-1-1v-3l2-2c0-1 0-1-1-2h-1-1l2-2h1z"></path><path d="M364 492c-1 1-2 1-3 2h-1c-1 1-1 1-2 1v-1c1-1 2-2 3-2h3z" class="D"></path><path d="M366 478l2-2c2 2 2 3 3 6v3h-1c0 1 0 1-1 2v2h0c-1 2-3 3-4 4a57.31 57.31 0 0 1-11 11h0 0l1-1v-1c2-3 5-5 7-7l3-3h-1-3l5-4c1-1 1-1 1-2h1v-1h0c-1 0-1 0-2 1v-1c-1-2 1-3 1-5l-1-1c-2 1-3 2-4 2l4-3z" class="G"></path><path d="M371 458c1 0 2-1 2 0h2l1 1 1 10c0 2-1 4 0 5v13l-1 1-1-1-1 2-1 1-3 3c-2 1-3 3-5 4h0c-1 1-1 2-2 2h-1l-7 7-8 6h-1l3-3 5-5h0a57.31 57.31 0 0 0 11-11c1-1 3-2 4-4h0v-2c1-1 1-1 1-2h1v-3c-1-3-1-4-3-6h1c1-1 2-2 1-4v-3-1l-1-1v-3l2-2c0-1 0-1-1-2h-1-1l2-2h1z" class="F"></path><path d="M365 497h0c0-3 5-6 7-7h1l-3 3c-2 1-3 3-5 4z" class="O"></path><path d="M371 458c1 0 2-1 2 0h2l1 1-2 2v13c0 4 0 8-1 13h1c0 1-2 2-2 2 0-5 1-10-1-15l1-1c1-1 1-6 0-7v-1c-1-1-1-3 0-4v-1h0c-1 0-1-1-1-1l-1-1h1z" class="J"></path><path d="M374 487h-1c1-5 1-9 1-13v-13l2-2 1 10c0 2-1 4 0 5v13l-1 1-1-1h-1z" class="W"></path><path d="M327 292v13l1 18c1 0 1 0 2-1l2-1 2-2 1-1c0-2 1-4 2-6h0c1-1 1-1 1-2s0-1 1-2h1v2l1-2 1-1h1l4-4c1 0 2-1 3-2 2-1 3-2 4-3 0-1 1-1 1-1l1 1-3 3c1 1 1 2 2 2 3-2 7-4 11-5 1 0 1-1 3 0-1 1-1 1-2 1v1h-1v1c-1 1-4 2-5 4v2 2l-1 1v1h1c2 0 3 0 4 1l1 1c0 2 1 3 2 4l-2 2h0 2c1 1 2 3 2 5v3c0 1 1 1 1 2s0 1-1 2c1 2 1 4 1 6v1l1 1v2l-1-2h0l-2 1c-1 1-2 2-3 1h0l1-1v-1-2-1c1-1 1-1 1-2h-2c0 1-1 1-1 1h-1l1-2v-1h-2c-1 0-2 1-2 1-1 2-2 2-4 2 0 0 0-1 1-1-3 0-5 4-7 5l-8 6h0 0 1c0-1 0-1 1-1h1l1-1h0c-2 1-3 2-4 4-1 0-1 1-2 1l-1 1 1 1h0 2c-1 1-3 2-4 3v1c2 0 3-1 4-2h0c-1 1-4 3-4 5h0l1-1h0c1 0 2 0 2-1h1l-3 3c1 0 1-1 2-1v-1h1c-1 1-1 2-2 3s-2 2-2 4h0l1-1s1 0 1-1c0 2-2 4-3 5l-1 1v3c-1 1-1 2-1 4-1 1 0 3-1 4l1 1h0c1 1 1 2 1 3v1h-1v2c0 1 0 1 1 2v1c-1 2 0 3-1 4 0 2 0 4-1 5 0 1 1 2 0 3v1c-1 1-1 1-1 2h1 0v3h1 0l1-1h0c1 0 1-1 1-1h0c0 2-3 4-4 6l2 1v-1c0 1 0 2-1 3 0 1 1 2 0 2v1c-1 1-2 2-2 3l-1 1s0 1-1 1v1h1c1 1 0 2 0 3v1h0c3 0 6-2 8-3l3-1v1c-1 0-3 1-4 1l-9 5-3 1c-1-1 0-4 0-5v-13-45l-1-1-2-1v-1l2 1 1-1v-4l-1-31v-5-13-5c2-4 0-9 1-14z" class="O"></path><path d="M341 318h1c0 1 0 2-1 3v2c1 0 1 0 2 1-1 1-1 2-2 2 0 2 1 3 1 4-2 2-4 4-6 5l1-3c1 0 1-1 1-1-1-2-1-2-1-4v-3c0-2 3-5 4-6z" class="H"></path><path d="M327 305l1 18c1 0 1 0 2-1l2-1 2-2 1-1c0-2 1-4 2-6h0c1-1 1-1 1-2s0-1 1-2h1v2h0c1 1 2 0 3 1v1 1c1 0 1 1 1 1v2l-2 2h-1c1-1 2-2 2-3h-1c-2 0-3 2-5 3l-6 5c-1 0-2 1-3 2 0-1 0-1-1-1h0c0-6-1-13 0-19z" class="F"></path><path d="M337 312h0c1-1 1-1 1-2s0-1 1-2l1 1c0 2-1 2-1 3 1 0 1 0 2 1-1 2-5 4-7 6l1-1c0-2 1-4 2-6z" class="X"></path><path d="M347 303c1 0 2-1 3-2 2-1 3-2 4-3 0-1 1-1 1-1l1 1-3 3c1 1 1 2 2 2 3-2 7-4 11-5 1 0 1-1 3 0-1 0-2 0-2 1h-2c0 1-1 1-2 2h-1-1c-2 2-4 3-7 4-2 1-5 3-7 5 0 2 1 3-1 4 0 1-1 1-2 2v-2s0-1-1-1v-1-1c-1-1-2 0-3-1h0l1-2 1-1h1l4-4z" class="H"></path><path d="M340 310l1-2 1 1h2c1 1 1 0 2 0v1c0 1 0 1-1 2 1 1 1 1 1 2s-1 1-2 2v-2s0-1-1-1v-1-1c-1-1-2 0-3-1h0z" class="M"></path><path d="M347 303c1 0 2-1 3-2 2-1 3-2 4-3 0-1 1-1 1-1l1 1-3 3c-1 1-2 1-3 1-1 2-3 3-4 5l-1 1c-1 0-1 0-2 1 0-1 0-1-1-2h1l4-4z" class="B"></path><defs><linearGradient id="CY" x1="328.092" y1="326.646" x2="334.158" y2="328.377" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#474646"></stop></linearGradient></defs><path fill="url(#CY)" d="M337 318c2-1 3-3 5-3h1c0 1-1 2-2 3s-4 4-4 6v3c0 2 0 2 1 4 0 0 0 1-1 1l-1 3c-3 1-6 3-9 4l1-9c0-2-1-4 0-5s2-2 3-2l6-5z"></path><path d="M337 318c2-1 3-3 5-3h1c0 1-1 2-2 3s-4 4-4 6v3c0 2 0 2 1 4 0 0 0 1-1 1v-1l-2-2h1v-2c-1 0-1 0-1-1h0v-1c0-1 0-2 1-2h0c1-2 2-3 2-4l-1-1z" class="e"></path><path d="M327 430v-7-17l1-66 12-6c0 2-1 4-2 5-2 3-3 3-3 6v6c-1 2-1 3-1 4 0 2-1 3-1 5v4c-1 2-2 5-2 6v1 4c-1 5 0 8 0 13l-1 26c0 2 0 8 1 10v1 1h1 0c3 0 6-2 8-3l3-1v1c-1 0-3 1-4 1l-9 5-3 1z" class="W"></path><defs><linearGradient id="CZ" x1="366.268" y1="330.932" x2="340.514" y2="327.129" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#d6d5d5"></stop></linearGradient></defs><path fill="url(#CZ)" d="M366 313c0 2 1 3 2 4l-2 2h0 2c1 1 2 3 2 5v3c0 1 1 1 1 2s0 1-1 2c1 2 1 4 1 6v1l1 1v2l-1-2h0l-2 1c-1 1-2 2-3 1h0l1-1v-1-2-1c1-1 1-1 1-2h-2c0 1-1 1-1 1h-1l1-2v-1h-2c-1 0-2 1-2 1-1 2-2 2-4 2 0 0 0-1 1-1-3 0-5 4-7 5l-8 6c-1-1-1-1-2-1l1-3s0-1 1-1v-1c1-1 1-2 3-3v-1s0 1-1 1l-1-1h1v-1-1h-3 0-1-1c2-2 5-3 6-6l-1-1v-1c0-1 0-1 1-2 1-2 4-4 6-5l2-2h1c0-1 1-1 1-1 2-1 5-2 7-1v1h1 0c0-1 1-1 2-2z"></path><path d="M366 334h0c0-1 0-1 1-2-1-1-1-1-2-1h1v-1h1v-1c-1-1-1-1-1-2v-1h-1 0-1c0-1 0-2 1-3h0-2c-1 0-1 0-2-1l3-2c1 0 1-1 2-1h0 2c1 1 2 3 2 5v3c0 1 1 1 1 2s0 1-1 2c1 2 1 4 1 6v1l1 1v2l-1-2h0l-2 1c-1 1-2 2-3 1h0l1-1v-1-2-1c1-1 1-1 1-2h-2z" class="B"></path><path d="M322 366h1v1l1 1 1-1 1-1 1 1v45 13c0 1-1 4 0 5l3-1 9-5c1 0 3-1 4-1v-1h0c1-1 1-1 3-1l6-3c-4 3-8 7-13 10l-3 3c0 1-1 1-1 2-1 1-1 3-1 4 1 1 1 2 1 2h1c0 1-1 2-1 3 0 4 1 8 1 11 0 2-1 3-2 5 0 1 1 4 1 5v15c-1 2-1 4-1 7 1 1 2 2 2 4 1 3-2 5 0 9v3h0v1 3 2 1 1c1 3 0 3-1 5l1 1c2 1 3 0 5-1 3-2 5-4 8-5l-3 3h1l-10 10h-1c0 1-1 2-2 3 1 0 1-1 2-2h1s1-1 2-1l1-1c2 2 3 1 5 3 0 0 0 1 1 2-2 1-4 2-5 5h0v1h-2 0c0 1-1 2-2 2-1 1-1 2-2 2l-2 1v1l-2-2h-1c-1 1-2 1-2 2l-1-1c-2 1-3 3-5 4 0 1-2 4-3 4l-2 2v-88c0-2 0-4 1-5v-1c-1 0-1 0-1-1v-3-8-34-25-10-4c1 0 1-1 2-1l1-1c1 1 1 1 2 1v-1z" class="a"></path><path d="M318 453v-1h1c1-2 3-3 4-5l1 1v1c-1 0-1 1-1 2l-3 3h-1c-1 1-1 1-1 2l-1 3c0-2 0-4 1-5v-1z" class="Y"></path><defs><linearGradient id="Ca" x1="330.606" y1="534.365" x2="343.314" y2="522.416" xlink:href="#B"><stop offset="0" stop-color="#818081"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#Ca)" d="M327 504v18c0 3 0 6 1 9v1c0-1 0-2 1-2h1c0-1-1-1-1-1l1-1c1-3 4-5 7-6h-1c0 1-1 2-2 3 1 0 1-1 2-2h1s1-1 2-1l1-1c2 2 3 1 5 3 0 0 0 1 1 2-2 1-4 2-5 5h0v1h-2 0c0 1-1 2-2 2-1 1-1 2-2 2l-2 1v1l-2-2h-1c-1 1-2 1-2 2l-1-1c-2 1-3 3-5 4 0 1-2 4-3 4 1-3 4-7 7-9 1-1 1 0 1-1v-21c0-3-1-6 0-9v-1z"></path><path d="M331 536c2 0 2-2 4-2v2l-2 1v1l-2-2z" class="c"></path><path d="M330 530c1 1 0 3 0 4l-2 2h0v-4c0-1 0-2 1-2h1z" class="C"></path><path d="M337 522h-1c0 1-1 2-2 3 1 0 1-1 2-2h1s1-1 2-1c-2 2-4 3-5 4s-2 2-4 3v-1h0c1-3 4-5 7-6z" class="a"></path><path d="M322 366h1v1l1 1v1 17 5c0 3 0 5-1 7v3h0l-1 1c-1 2-3 3-4 4l-1 1v-25-10-4c1 0 1-1 2-1l1-1c1 1 1 1 2 1v-1z" class="D"></path><path d="M322 366h1v1l1 1v1c-2 1-2 2-2 4h0 0v3h1v1l1 1c-1 1-3 2-4 2l-2 2h-1v-10-4c1 0 1-1 2-1l1-1c1 1 1 1 2 1v-1z" class="V"></path><defs><linearGradient id="Cb" x1="326.973" y1="479.035" x2="333.237" y2="478.961" xlink:href="#B"><stop offset="0" stop-color="#38383a"></stop><stop offset="1" stop-color="#555451"></stop></linearGradient></defs><path fill="url(#Cb)" d="M346 421l6-3c-4 3-8 7-13 10-2 1-7 3-9 6 0 2 2 4 1 6s-1 3-1 5c0 7 1 13 1 20v16 21c0 7-1 14 0 21 2-2 4-3 6-4 3-2 6-6 9-7h1l-10 10c-3 1-6 3-7 6l-1 1s1 0 1 1h-1c-1 0-1 1-1 2v-1c-1-3-1-6-1-9v-18-71c1-3 10-5 13-7 2-1 4-3 6-5z"></path><path d="M346 512c-3 1-6 5-9 7-2 1-4 2-6 4-1-7 0-14 0-21v-21-16c0-7-1-13-1-20 0-2 0-3 1-5s-1-4-1-6c2-3 7-5 9-6l-3 3c0 1-1 1-1 2-1 1-1 3-1 4 1 1 1 2 1 2h1c0 1-1 2-1 3 0 4 1 8 1 11 0 2-1 3-2 5 0 1 1 4 1 5v15c-1 2-1 4-1 7 1 1 2 2 2 4 1 3-2 5 0 9v3h0v1 3 2 1 1c1 3 0 3-1 5l1 1c2 1 3 0 5-1 3-2 5-4 8-5l-3 3z" class="f"></path><path d="M348 60h12 31v1c-4 1-7 4-9 7v1c2 1 1 0 2 1l-1 1-1 1c-3 8-2 18 1 26 2 8 7 14 12 20h1c1 3 4 4 6 6 1 2 3 4 5 6 8 7 17 12 26 16 4 2 8 3 11 5l-1 1c1 1 1 1 2 1 2 1 5 2 6 4l1 1c1 1 2 1 4 2l6 3c2 1 5 2 7 2 3 1 8 5 9 8v1c-1 3 1 4 1 7v1 1c2 2 4 5 5 7h0c1 2 1 3 2 4l1 1 4 5 2 2c-1 1-1 2-2 2h-1v2h0c1 1 1 2 0 3h1c0 1 0 1 1 2l-7-1c-2-1-4 0-5-1 1-1 2-1 3-1v-4l-2 1h-1-1 0c-1-1-1-1-1-2-1 1-2 1-3 1l1 1c-1 1-3 2-4 2l-4 3-1 1c-1 2-3 3-4 5v1l-2 3c-1 1-1 2-1 3h0v-3l-1 1c0 1 1 2 1 3h-1c-1 0-2 0-3 1h-1 4 0-7-9l-2 1-7-1h-8-34v1h2l-1 1-7 5c-3 3-6 2-10 2l-11 1c-2 0-4-1-6 0h-12-30-22c-2 0-5 0-7 1h0c-3-2-8-1-11-1h-25-13c-1 0-2 1-2 1l-2 2s0-1-1-1l-1 5c-2 2-4 4-7 5h0c-1-2-2-4-4-5l-2-2-6-3-19-5h-2s-1 0-1-1l-2 1c-1-1-1-1-1-2s1-2 1-2l1-1c1-1 1-2 2-2 1-1 3-2 4-3l3-2c-1 0-3 1-3 2-1 0-1 0-2 1h-1 0-1c1-1 3-2 4-3v-1c-2 2-5 4-7 5h-2c2-2 5-4 7-6l6-5c1-1 2-3 3-4 1 0 1 0 2-1v-2c1-1 4-3 5-4l3 1c1-1 1-2 2-2v-1l1-1 2-2c1-2 3-3 4-6h0c1-2 2-3 4-3 0-2 1-3 1-4-1 0-2-1-3-1v1-2l-2 2h-1v-7-1c1-4 5-7 8-10l5-3 7-4 2-3c1 0 2-1 4-1h1 0 2c1-2 5-3 7-4l1-1c1 1 2 1 3 1 5-2 10-5 15-8 3-2 6-3 8-5l7-5v-3-1-1c1 0 1-1 2-2 0 1 0 1 1 1 0-1 1-1 2-2s2-2 2-4c1 0 2 0 2 2h0c1-2 0-1 0-3 2-1 3-1 4-2l1-1v4l2-2h1 1l1-1c1 0 1 0 1-1l4-6c1-1 2-2 3-4l1-1 1-1h1l-1-1c-1-1-1-1-3-1l1-1h0c2-7 4-13 3-20v-5-2s0-1-1-2h-3v-1h0c-2-1-3-1-5 0h0l-1-1 1-2c0-1 1-1 2-2v-3l-4-4h1c4-1 8 0 12 0h24z" class="S"></path><path d="M460 215v2h3l-2 3c-1 1-1 2-1 3h0v-3c-1-2-1-3 0-5z" class="P"></path><path d="M458 212l3 1 1 1s0 1 1 2v1h-3v-2c-2-1-2-1-2-3z" class="Z"></path><path d="M399 207h7 0c1-1 2-1 2-2h2 0c1 1 1 1 2 1h2 1l-1 1c-5 0-10 1-15 0z" class="B"></path><path d="M419 202v2l3 2c-3 0-6 1-8 1l1-1 1-1v-2l3-1z" class="M"></path><path d="M361 208l-3-1c1-1 0-3 0-5v-14c0-2 0-4 1-7 1 2 2 3 2 5h0v-1h-1c0-1 0-1-1-2 0 2-1 7 0 9v1c0 1 0 2 1 3v1h0v2c-1 2-1 4-1 6l1 1 1-1v1 1l1 1h-1z" class="C"></path><path d="M458 205h1l1 1c2 3 2 4 2 8l-1-1-3-1-2-3h0l-3-3s0-1 1-1h0c1 0 1 1 2 1h2v-1z" class="G"></path><path d="M460 206c2 3 2 4 2 8l-1-1-3-1-2-3c1 0 2 1 3 1v1l1-1c1-1 0-3 0-4z" class="M"></path><path d="M379 200h3c1 0 2 0 3 1 1 0 2 1 3 2 2 1 4 1 6 2h5l1-1c2 1 8-1 10 1h-2c0 1-1 1-2 2h0-7c-7 0-14-3-20-7z" class="O"></path><path d="M387 197l6 3c5 3 9 2 14 2-3 1-7 0-10 1h3l-1 1h1 0l-1 1h-5c-2-1-4-1-6-2-1-1-2-2-3-2-1-1-2-1-3-1 0-1 0-1 1-2h1 3v-1z" class="a"></path><path d="M387 197l6 3c5 3 9 2 14 2-3 1-7 0-10 1h-1c-4-2-9-2-12-5h3v-1z" class="B"></path><path d="M431 196c0 1 0 2-1 2s-1 1-2 1h2l1-1v1h10v1c1 0 1 1 2 1l1 1c-7 0-15 1-22 4l-3-2v-2s1-1 2-1h1c2-1 4-2 6-4v1c1-1 2-1 3-2z" class="D"></path><path d="M421 201h1c2-1 4-2 6-4v1h0c-1 0-1 1-1 1 1 1 2 1 4 0 1 0 2 0 3 1h-4 0c1 0 2 1 3 0h0c-2 1-5 1-8 1-1 0-2 1-4 0z" class="G"></path><defs><linearGradient id="Cc" x1="386.485" y1="202.46" x2="369.974" y2="210.816" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#Cc)" d="M374 200c6 4 11 7 18 9h-2 0c0 1-1 1-2 1s-2-1-3-1c-1-1-2-1-3 0h-1c0 1 0 1 1 2l-14-2h-1v-2l6-5h1l-1-1 1-1z"></path><path d="M377 206h1v1 1h-2v-1l1-1z" class="J"></path><path d="M373 202h1v1h0c-2 1-5 4-6 6h-1v-2l6-5z" class="I"></path><path d="M360 197v-1h1v-1h0c-1 0-1 0-1-1h0c0-1 0-2 1-2l1-1c1 0 1 0 2 1v1h1v-1c-1-2-2-2-3-3 0-1 0-1-1-1 2 0 3 1 4 2v1l1 1 1 1c1 1 1 2 2 3 0 1 1 1 2 2h1l2 2-1 1 1 1h-1l-6 5v2l-5-1-1-1v-1-1l-1 1-1-1c0-2 0-4 1-6v-2h0z" class="Q"></path><path d="M362 192h1c0 1 0 1-1 2h0-1c0-1 1-1 1-2z" class="e"></path><path d="M364 195h1c1 1 1 2 2 3-2 2-3 5-5 6h-1-1c2-2 0-3 1-6 0-1 2-3 3-3z" class="Z"></path><path d="M367 207h-4v-1c2-2 3-5 6-6h0c1 0 1 1 1 1 1 1 1 0 2 0s1 1 1 1l-6 5z" class="J"></path><path d="M363 183c-1 0-1-1-2-2 1-2 3-4 4-6l3 6 4 5c4 4 9 8 14 10l1 1v1h-3-1c-1 1-1 1-1 2h-3c-2 0-3-2-4-3-5-3-9-8-11-13l-1-1z" class="D"></path><path d="M376 194l2 1 1 1v1h-2c-1 0-2-1-2-2l1-1z" class="g"></path><path d="M363 183c-1 0-1-1-2-2 1-2 3-4 4-6l3 6 4 5-1 1c1 2 5 5 7 7v1h0l-2-1-1 1c-2-2-5-4-6-6l-5-5-1-1z" class="B"></path><path d="M363 183c0-1 0-2 1-3 1 0 1 0 1 1 2 5 7 9 11 13l-1 1c-2-2-5-4-6-6l-5-5-1-1z" class="i"></path><defs><linearGradient id="Cd" x1="415.891" y1="185.643" x2="423.11" y2="199.148" xlink:href="#B"><stop offset="0" stop-color="#b9b8b9"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#Cd)" d="M422 166c3-1 6-2 9-1 2 1 3 1 4 2l3 3c2 3 2 7 1 11-2 5-6 13-11 16-2 2-4 3-6 4h-1c-1 0-2 1-2 1l-3 1v2l-1 1h-1-2c-1 0-1 0-2-1h0c-2-2-8 0-10-1h0-1l1-1h-3c3-1 7 0 10-1 9-1 16-4 23-10 4-3 6-9 7-14-1-1-1-2-2-3-1-3-3-4-6-6l-1-1c-1 0-3-1-4 0h-1c0 1-1 1-1 2l-2-1 2-3z"></path><path d="M410 205h1c2-1 3-2 5-2v2l-1 1h-1-2c-1 0-1 0-2-1z" class="O"></path><path d="M422 166c3-1 6-2 9-1 2 1 3 1 4 2 1 3 3 5 3 7v4-2c-1 0-1-1-1-1h-1l1 1v2c-1-1-1-2-2-3-1-3-3-4-6-6l-1-1c-1 0-3-1-4 0h-1c0 1-1 1-1 2l-2-1 2-3z" class="Y"></path><path d="M428 168c1-1 2-1 3-1 2 2 4 4 5 7h0c1 1 1 1 2 0v4-2c-1 0-1-1-1-1h-1l1 1v2c-1-1-1-2-2-3-1-3-3-4-6-6l-1-1z" class="D"></path><path d="M440 205h4c1 1 0 1 1 2h1c1-1 1-1 3-1h0c1 2 1 1 3 2 0 0 1 2 2 3s1 3 2 4 1 2 1 3h0v1l2 1v1c0 1 1 2 1 3h-1c-1 0-2 0-3 1h-1 4 0-7-9v-1-1c-2-1-3-3-5-5 0 0-1 0-2 1h-1l-1 1v-1-1c-2-1-5-2-7-2v3c-1 1-2 0-3 1-1 0-3 1-4 0h1v-1h1 4c1 0 0 0 1-1l-2-2-4 1h0c-1 0-3 0-4-1-2-1-5 0-8 0-1 1-5 0-6 1v1l-4-2c-1 0-2-1-3-1h0-3l-11-4c-1-1-1-1-1-2h1c1-1 2-1 3 0 1 0 2 1 3 1s2 0 2-1h0 2c3 1 6 1 9 2h4 2v1c1 0 2 0 3-1h1 3 3v-1h3c1 0 1-1 2-1 6-2 12-3 18-4z" class="Q"></path><path d="M450 218h1c1 0 1 0 1 2v1h0-1c0-1 0-1-1-2v-1h0z" class="I"></path><path d="M431 209c1 0 3 2 3 3v2h-1c-1-1-2-3-2-4v-1z" class="R"></path><path d="M440 205h4v1h-3-1l-1 3-2-1v-1c1-1 2-1 3-2z" class="J"></path><path d="M402 212h1c1 1 2 1 3 2v2h-3 0 0c0-2-1-2-1-4z" class="R"></path><path d="M439 209l1-3h1 3c-1 1-1 2-2 3h-3z" class="X"></path><path d="M416 214c-2 1-5 2-8 2 3-2 5-3 8-4v2z" class="O"></path><path d="M438 216c2-1 3-1 4-1l2 1h1 1c1 1 0 3 1 4 0 1-1 2-2 2v-2c0-1-1-3-2-4h-5z" class="e"></path><path d="M443 216c1 1 2 3 2 4v2c0 1 0 2-1 3h8-9v-1c1-1 1-2 1-3-1 0-1-1-1-1l-1-1c0-1 0-2 1-3z" class="G"></path><path d="M396 215l-1-2 1-1c2 1 3 1 5 0h1c0 2 1 2 1 4h0 0-1-1c-1-1-2 0-2 0-1 0-2-1-3-1z" class="N"></path><path d="M438 216h5c-1 1-1 2-1 3l1 1s0 1 1 1c0 1 0 2-1 3v-1c-2-1-3-3-5-5h0v-2z" class="c"></path><path d="M438 216h5c-1 1-1 2-1 3v-1c-1 1-2 0-3 0h-1v-2zm-22-4c2 0 5 0 6 2l1 1h0 3v1h-1l-4 1h0c-2-1-4-1-5-3v-2z" class="M"></path><path d="M381 209h1 1 1 1c1 0 1 1 2 1 2 1 5-1 6 2 1 1 1 2 2 2-1 1-1 1-2 1l-11-4c-1-1-1-1-1-2z" class="P"></path><path d="M419 151c0-1 1-1 2-1l1 1c2 1 4 0 6 0l1 1h0 1l1-1c4 3 7 6 10 9l1-1-1-2c-1-1-1-2-1-2 0-1 3 0 4 0l8 3c1 1 2 1 4 2l6 3c2 1 5 2 7 2 3 1 8 5 9 8v1c-1 3 1 4 1 7v1 1c2 2 4 5 5 7h0c1 2 1 3 2 4l1 1 4 5 2 2c-1 1-1 2-2 2h-1v2h0c1 1 1 2 0 3h1c0 1 0 1 1 2l-7-1c-2-1-4 0-5-1 1-1 2-1 3-1v-4l-2 1h-1-1 0c-1-1-1-1-1-2-1 1-2 1-3 1l1 1c-1 1-3 2-4 2l-4 3-1 1c-1 2-3 3-4 5-1-1-1-2-1-2 0-4 0-5-2-8l-1-1h-1v1h-2c-1 0-1-1-2-1h0c-1 0-1 1-1 1l-3-2-6-2-1-1c-1 0-1-1-2-1v-1h-10v-1l-1 1h-2c1 0 1-1 2-1s1-1 1-2c-1 1-2 1-3 2v-1c5-3 9-11 11-16 1-4 1-8-1-11v-3c-3-3-7-5-11-7l1-1h0 1c1 0 2 1 4 0h0c-1 0-2-1-4-2l-6-3c-1-1-2-2-2-3h-1-1z" class="a"></path><path d="M449 198c4 2 7 3 9 7v1h-2c-1 0-1-1-2-1h0c-1 0-1 1-1 1l-3-2h1 1c1 0 2 0 3 1h0l1-1c-1-1-2-2-4-2l-1-1h-2-1l-2-1h-1v-1c2 0 2 0 4-1z" class="L"></path><path d="M442 193c0-4 2-6 3-10 0-3 0-6 1-9h1c1 1 2 1 2 3 1 1 1 2 2 3v1c-1 0-1 1-2 2h0c-1 0-2 1-3 1-1 3-2 6-4 9z" class="G"></path><path d="M446 184l2-5c0-1 0-1 1-2 1 1 1 2 2 3v1c-1 0-1 1-2 2h0c-1 0-2 1-3 1z" class="C"></path><defs><linearGradient id="Ce" x1="431.058" y1="171.298" x2="448.748" y2="163.043" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#c6c5c6"></stop></linearGradient></defs><path fill="url(#Ce)" d="M419 151c0-1 1-1 2-1l1 1c2 1 4 0 6 0l1 1h0 1l1-1c4 3 7 6 10 9l4 3 13 18v3h-1c-2-2-2-4-4-6l-3-3c0-1 0-2-1-2-1-2-2-3-3-4s-1-2-2-3c-3-2-5-5-7-7-2-1-5-3-7-3l-1 1-6-3c-1-1-2-2-2-3h-1-1z"></path><defs><linearGradient id="Cf" x1="434.522" y1="158.453" x2="435.428" y2="194.159" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#656464"></stop></linearGradient></defs><path fill="url(#Cf)" d="M427 160l1-1h0 1c1 0 2 1 4 0h0c5 3 8 6 9 11 2 6 1 12-2 17-1 2-3 6-5 7s-3 1-4 2-2 1-3 2v-1c5-3 9-11 11-16 1-4 1-8-1-11v-3c-3-3-7-5-11-7z"></path><path d="M451 180h0l2 1c1 1 1 2 2 3s1 1 2 0h1c2 2 2 3 5 3h1v-1 1l-1 2 2 1c-2-1-4-2-5-2v2l-1 1c2 2 5 3 7 5 1 1 2 2 2 4h2 0l-1 1 1 1 5 2h0l1 1c-1 1-3 2-4 2l-4 3-1 1c-1 2-3 3-4 5-1-1-1-2-1-2 0-4 0-5-2-8l-1-1h-1c-2-4-5-5-9-7-2 1-2 1-4 1v1h-1-2v-3h0l-1-1c0-1 0-2 1-3 2-3 3-6 4-9 1 0 2-1 3-1h0c1-1 1-2 2-2v-1z" class="J"></path><path d="M453 188l-2-3c0-2 0-2 1-3 1 2 1 3 3 4h1-3v2z" class="C"></path><path d="M442 197l7 1c-2 1-2 1-4 1v1h-1-2v-3z" class="O"></path><path d="M450 189c0-1 0-1 1-2h1c1 1 2 2 3 4 0 0 1 0 1 1l-1 1c-1 0-2 1-2 1l2-2v-1h-6v-1l1-1z" class="c"></path><path d="M453 188v-2h3l4 2v2l-1 1-3-1-3-2z" class="S"></path><path d="M451 180h0l2 1c1 1 1 2 2 3s1 1 2 0h1c2 2 2 3 5 3h1v-1 1l-1 2 2 1c-2-1-4-2-5-2l-4-2h-1c-2-1-2-2-3-4l-1-1v-1z" class="G"></path><path d="M445 195c1-1 2-3 3-4 0-1 0-1 1-1v1h6v1l-2 2c-1 1-1 1-1 2h-1-1l-1 1-2-1h-1-2l1-1z" class="N"></path><path d="M445 195c1-1 2-3 3-4 0-1 0-1 1-1v1h0c0 2-2 3-2 4 1 0 3 0 5 1h-1-1l-1 1-2-1h-1-2l1-1z" class="Z"></path><path d="M446 184c1 0 2-1 3-1h0c0 2-1 3-1 4s1 1 2 2l-1 1c-1 0-1 0-1 1-1 1-2 3-3 4l-1 1c-1 0-1 1-2 1l-1-1c0-1 0-2 1-3 2-3 3-6 4-9z" class="S"></path><path d="M448 187c0 1 1 1 2 2l-1 1c-1 0-1 0-1 1-1 1-2 3-3 4l3-8z" class="P"></path><path d="M456 190l3 1c2 2 5 3 7 5 1 1 2 2 2 4h2 0l-1 1 1 1 5 2h0l1 1c-1 1-3 2-4 2l-4 3-1 1c-1 2-3 3-4 5-1-1-1-2-1-2 0-4 0-5-2-8l-1-1v-1h-1c0-2-1-4-3-6h0c-1-1-1-1-1-2v-1l1 1 2-2v-2s0-1-1-1v-1z" class="R"></path><path d="M462 197v-1c2 1 3 2 4 4 0 2 0 2 1 4h-1-1c0-1-1-2-1-3-1-2-2-2-2-4z" class="Z"></path><path d="M470 206l1-1c0-1-2-2-2-3h1l5 2h0l1 1c-1 1-3 2-4 2h0v-1h-2 0z" class="K"></path><defs><linearGradient id="Cg" x1="457.124" y1="207.666" x2="466.953" y2="200.062" xlink:href="#B"><stop offset="0" stop-color="#363636"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#Cg)" d="M455 198l2-2s1 0 1 1c0 0 1 1 1 2 3 3 3 6 7 8 1 0 3-1 4-1h0 2v1h0l-4 3-1 1c-1 2-3 3-4 5-1-1-1-2-1-2 0-4 0-5-2-8l-1-1v-1h-1c0-2-1-4-3-6z"></path><path d="M472 181c2 1 3 1 4 2v1l1-1h2 0c2 2 4 5 5 7h0c1 2 1 3 2 4l1 1 4 5 2 2c-1 1-1 2-2 2h-1v2h0c1 1 1 2 0 3h1c0 1 0 1 1 2l-7-1c-2-1-4 0-5-1 1-1 2-1 3-1v-4l-2 1h-1-1 0c-1-1-1-1-1-2-1 1-2 1-3 1h0l-5-2-1-1 1-1h0-2c0-2-1-3-2-4-2-2-5-3-7-5l1-1v-2c1 0 3 1 5 2l-2-1 1-2v-1l1-1h1 1c0-1-1-2 0-3 0 0 3 0 5-1h0z" class="C"></path><path d="M471 191c1 1 2 1 2 3v2c0 1 0 1-1 1l-4-4 2-1 1-1z" class="E"></path><path d="M464 187l7 4-1 1-2 1-2-2c-1 0-1 0-1-1l-2-1 1-2z" class="L"></path><path d="M466 196h0c1 0 2 1 3 1 1 1 2 1 2 1 1 1 1 0 1 1 1 0 2 0 2 1h1l1-1h-1c1-1 1-1 2-1l2 2 1-1v2c-4-1-7-1-10-1h0-2c0-2-1-3-2-4z" class="I"></path><path d="M470 200c3 0 6 0 10 1l3 2v1h0-3l-1 1c-1-1-1-1-1-2-1 1-2 1-3 1h0l-5-2-1-1 1-1z" class="O"></path><path d="M475 204v-1c0-1 0-1 1-1l2 1c-1 1-2 1-3 1h0z" class="L"></path><path d="M472 181c2 1 3 1 4 2v1l1 1v1 2l1 2v3l2 6-1 1-2-2h-2 0-1l1-3c0 1-1 1-2 1v-2c0-2-1-2-2-3l-7-4v-1l1-1h1 1c0-1-1-2 0-3 0 0 3 0 5-1h0z" class="I"></path><path d="M475 195h1 0l-3-6c2 1 4 2 5 4l2 6-1 1-2-2h-2 0-1l1-3z" class="d"></path><path d="M472 181c2 1 3 1 4 2v1l1 1v1c0 1 0 2-1 3h-1-1c-3 0-5-2-8-4h1c0-1-1-2 0-3 0 0 3 0 5-1h0z" class="C"></path><path d="M467 185c2-1 4 0 6 0l2 4h-1c-3 0-5-2-8-4h1z" class="R"></path><path d="M479 183c2 2 4 5 5 7h0c1 2 1 3 2 4l1 1 4 5 2 2c-1 1-1 2-2 2h-1v2h0c1 1 1 2 0 3h1c0 1 0 1 1 2l-7-1c-2-1-4 0-5-1 1-1 2-1 3-1v-4l-2 1h-1-1 0l1-1h3 0v-1l-3-2v-2l-2-6v-3l-1-2v-2-1l-1-1 1-1h2 0z" class="B"></path><path d="M481 191h-2c0-1-1-2-1-3h1l2 3z" class="V"></path><path d="M491 200l2 2c-1 1-1 2-2 2h-1 0l-1-1c0-1 0-1 1-1h0s1-1 1-2z" class="G"></path><path d="M483 198c-1-1-1-2-2-2h0v-1h2v1l1-1c1 0 1 0 2 1l-2 2h1 2 1v1l-1 1h-1 0c0 2 2 3 4 4h0v2h0c-1 0-1 0-2-1s-1-2-1-2l-1-1h-1v-2c0-1-1-2-2-2z" class="E"></path><path d="M479 183c2 2 4 5 5 7-1 1-1 1-3 1l-2-3-1-1h0 0l-1 1v-2-1l-1-1 1-1h2 0z" class="F"></path><path d="M476 184l1-1h2c0 1 0 1-1 2h-1l-1-1z" class="f"></path><path d="M478 190c2 2 4 3 6 5l-1 1v-1h-2v1h0c1 0 1 1 2 2 0 1 1 2 1 3-1 1-1 1-1 2l-3-2v-2l-2-6v-3z" class="J"></path><path d="M483 198c1 0 2 1 2 2v2h1l1 1s0 1 1 2 1 1 2 1c1 1 1 2 0 3h1c0 1 0 1 1 2l-7-1c-2-1-4 0-5-1 1-1 2-1 3-1v-4l-2 1h-1-1 0l1-1h3 0v-1c0-1 0-1 1-2 0-1-1-2-1-3z" class="T"></path><path d="M483 204c1 1 3 1 4 2l-4 2v-4z" class="K"></path><path d="M441 157c-1-1-1-2-1-2 0-1 3 0 4 0l8 3c1 1 2 1 4 2l6 3c2 1 5 2 7 2 3 1 8 5 9 8v1c-1 3 1 4 1 7v1 1h0-2l-1 1v-1c-1-1-2-1-4-2h0c-2 1-5 1-5 1-1 1 0 2 0 3h-1-1l-1 1v1h-1c-3 0-3-1-5-3v-3l-13-18-4-3 1-1-1-2z" class="G"></path><path d="M449 165c2 2 3 4 5 6 1 1 3 2 4 3h1c0-1-1-2 0-4l2-2c0 2-1 3 0 4s2 1 2 1v2c-1 0-3 1-4 1l-1-1h-2v1c-2-1-3-4-4-5l-3-5v-1z" class="M"></path><path d="M441 157c-1-1-1-2-1-2 0-1 3 0 4 0l8 3c1 1 2 1 4 2 0 1 0 1 1 2 1 2 1 2 3 3h-2l-2-2h0c-1 0-3-1-4-1 0-1-1 0-2 0 0 0-1-1-1-2-1 0-1 0-2 1 1 1 2 3 2 4v1c-1 0-3-4-4-5v2l-4-3 1-1-1-2z" class="E"></path><path d="M441 157l4 4v2l-4-3 1-1-1-2z" class="P"></path><defs><linearGradient id="Ch" x1="467.956" y1="176.338" x2="459.896" y2="173.252" xlink:href="#B"><stop offset="0" stop-color="#717071"></stop><stop offset="1" stop-color="#8f8e8e"></stop></linearGradient></defs><path fill="url(#Ch)" d="M456 163h0l2 2h2l1-1c1 1 2 1 2 2v4c2 0 3 1 5 1h2v3c-1 1-3 2-4 4 1 1 1 1 2 1-2 0-4 0-6 1v1h-1c-1-4-2-2-4-3l-1-2v-1h2l1 1c1 0 3-1 4-1v-2s-1 0-2-1 0-2 0-4v-2c-1-1-2 0-3 0h-1c-1-1-1-2-1-3z"></path><path d="M445 161c1 1 3 5 4 5l3 5c1 1 2 4 4 5l1 2c2 1 3-1 4 3h1v-1c2-1 4-1 6-1-1 0-1 0-2-1 1-2 3-3 4-4v-3l1 1v1c1 0 1-1 1-2 1 1 1 1 1 2-1 1-2 1-3 2 0 1 0 1 1 2v1c1 1 2 1 3 0l-1 1h-2c0 1 1 1 1 2h0c-2 1-5 1-5 1-1 1 0 2 0 3h-1-1l-1 1v1h-1c-3 0-3-1-5-3v-3l-13-18v-2z" class="e"></path><path d="M468 179l2 2h0c-3 1-5 0-8 0v-1c2-1 4-1 6-1z" class="D"></path><path d="M458 181l6 6h-1c-3 0-3-1-5-3v-3z" class="L"></path><defs><linearGradient id="Ci" x1="478.406" y1="178.247" x2="464.658" y2="167.938" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#Ci)" d="M456 160l6 3c2 1 5 2 7 2 3 1 8 5 9 8v1c-1 3 1 4 1 7v1 1h0-2l-1 1v-1c-1-1-2-1-4-2 0-1-1-1-1-2h2l1-1c-1 1-2 1-3 0v-1c-1-1-1-1-1-2 1-1 2-1 3-2 0-1 0-1-1-2 0 1 0 2-1 2v-1l-1-1h-2c-2 0-3-1-5-1v-4c0-1-1-1-2-2l-1 1c-2-1-2-1-3-3-1-1-1-1-1-2z"></path><path d="M474 178h1l4 4v1h0-2l-1 1v-1c-1-1-2-1-4-2 0-1-1-1-1-2h2l1-1z" class="E"></path><defs><linearGradient id="Cj" x1="405.136" y1="199.032" x2="405.197" y2="165.286" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#Cj)" d="M408 149c1 0 3 0 4 2 1 0 1 0 1 1 1 0 4 0 4-1h1 1 1 1c0 1 1 2 2 3l6 3c2 1 3 2 4 2h0c-2 1-3 0-4 0h-1 0l-1 1c4 2 8 4 11 7v3l-3-3c-1-1-2-1-4-2-3-1-6 0-9 1l-2 3 2 1c0-1 1-1 1-2h1c1-1 3 0 4 0l1 1c3 2 5 3 6 6 1 1 1 2 2 3-1 5-3 11-7 14-7 6-14 9-23 10-5 0-9 1-14-2l-6-3-1-1c-5-2-10-6-14-10l-4-5h2v-2c-1 0-1-1-1-2 0-2 0-3 1-5l2-2c0-2 2-4 4-5 1 0 1 0 1-1s-1-2-2-2l1-2c1 0 4-2 5-2l1 1h2c1-2 3-4 5-5h1 0l1 1s1-1 2-1c1 1 1 1 2 1 1-2 3-3 5-4h0l3-1 4-1h1z"></path><path d="M393 200h11c3 0 7-1 10-2 2-1 4-1 6-2 2 0 4-2 6-3 1-1 2-1 4-1-7 6-14 9-23 10-5 0-9 1-14-2z" class="P"></path><path d="M429 182s1 1 2 1v1c-1 1-2 2-3 2-1 4-6 6-9 7-5 2-8 3-13 4v-1h-1c1-1 3-1 4-1l-1-1v-1c2 0 3-1 5-1l5-2h1l2-1c3-2 6-4 8-7z" class="f"></path><path d="M419 190l1 1c0 1-2 1-3 2-2 0-5 1-8 2l-1-1v-1c2 0 3-1 5-1l5-2h1z" class="L"></path><path d="M372 170l2-2c0 1 0 5 2 6h0v2l-1 1s1 2 1 3 1 3 1 4c1 1 2 3 4 5s4 4 5 7c-5-2-10-6-14-10l-4-5h2v-2c-1 0-1-1-1-2 0-2 0-3 1-5l2-2z" class="P"></path><path d="M372 170l2-2c0 1 0 5 2 6h0c0 2-1 2-2 3-1 0-2 1-3 1-1-2-1-4-1-6l2-2z" class="b"></path><path d="M376 160c1 0 4-2 5-2l1 1h2l-1 4c-1 2-1 6-2 8v2h3 0c1 0 2-1 2-2h1v2c1 4 3 5 5 8 1 2 1 4 1 6 0 0 0 1-1 1h-1l-1 1h0l-1 1h0c1 1 2 2 4 3s4 1 5 3h-1c-5-1-11-5-14-8-3-4-4-7-5-12-1-2-1-5-1-8 0-1 1-3 0-4 0-1-1-2-2-2l1-2z" class="V"></path><path d="M380 161h1v2h-2v-1l1-1z" class="D"></path><path d="M378 176v-3 2c0 1 0 1 1 2h0v-2 2c2 3 5 6 5 10l-1 1c-3-4-4-7-5-12z" class="L"></path><path d="M379 177c1-2 0-3 1-5 0-1 0-1-1-3v-1-1l2 2v-1-2-1c0-1 1-1 2-2-1 2-1 6-2 8v2c0 2 1 4 1 6 2 4 4 8 8 10h0 0l-1 1h0c1 1 2 2 4 3s4 1 5 3h-1c-5-1-11-5-14-8l1-1c0-4-3-7-5-10z" class="B"></path><path d="M387 171v2c1 4 3 5 5 8 1 2 1 4 1 6 0 0 0 1-1 1h-1l-1 1h0c-4-2-6-6-8-10 0-2-1-4-1-6h3 0c1 0 2-1 2-2h1z" class="b"></path><path d="M386 179h1l1 1c1 2 2 4 2 6 0 1-1 0-1 0l-3-3v-4z" class="c"></path><path d="M387 171v2c-1 1-1 2-1 3l-3 3h-1c0-2-1-4-1-6h3 0c1 0 2-1 2-2h1z" class="W"></path><path d="M420 169l2 1c0-1 1-1 1-2h1c1-1 3 0 4 0l1 1c3 2 5 3 6 6 0 1-1 1-2 2v1c0 2 0 2-1 4l-1 2v-1c-1 0-2-1-2-1-2 3-5 5-8 7l-2 1h-1l-5 2c-2 0-3 1-5 1v1l1 1c-1 0-3 0-4 1-2 1-5 0-7 0-1-2-3-2-5-3s-3-2-4-3h0l1-1h0l1-1h1c1 0 1-1 1-1 0-2 0-4-1-6-2-3-4-4-5-8v-2s1-1 1-2l1 1c1 0 2 0 2 1l2 6h1 1l1 1h2c1 1 2 1 3 2 2 1 4 1 6 0 1-1 3-1 3-3 1 0 2-1 2-1 1-2 1-4 1-6v-1l1 1c1-1 1-1 2-1h0l1 2h1l1-1 1-1z" class="S"></path><path d="M422 180h0c2 1 6 0 7 0 1 1 0 1 0 2-2 3-5 5-8 7v-1c1-2 3-3 4-5h0-2c-2-1-3-1-5-1h2l1-1c1 1 2 1 4 1h0c1 0 1 1 1 1l1-1 1-1c-1 0-5 1-7 0h0l1-1z" class="I"></path><path d="M389 170c1 0 2 0 2 1l2 6h1 1l1 1c0 1 1 2 2 3s6 3 7 2c2-1 4-1 6-2 1-1 2-3 4-4h0c0 1 0 0 1 1h0v1c-2 2-6 4-10 5-3 1-6 0-8-2h-1c-4-3-7-8-8-12z" class="M"></path><path d="M413 170v-1l1 1c1-1 1-1 2-1h0l1 2h1v1h-2l-1 2c-2 0-2 0-2 2 1 0 1 0 2 1h0 0c-2 1-3 3-4 4-2 1-4 1-6 2-1 1-6-1-7-2s-2-2-2-3h2c1 1 2 1 3 2 2 1 4 1 6 0 1-1 3-1 3-3 1 0 2-1 2-1 1-2 1-4 1-6z" class="D"></path><path d="M420 169l2 1c0-1 1-1 1-2h1c1-1 3 0 4 0l1 1c-1 0-4 0-5 1-2 2-2 2-2 4v2c0 1 0 2 1 4h-1l-1 1c-2-2-3-4-6-4h0c-1-1-1-1-2-1 0-2 0-2 2-2l1-2h2v-1l1-1 1-1z" class="Y"></path><path d="M422 176c0 1 0 2 1 4h-1l-1 1c-2-2-3-4-6-4h0c2 0 2-1 4 0h1 2v-1z" class="G"></path><path d="M420 169l2 1c0-1 1-1 1-2h1c1-1 3 0 4 0l1 1c-1 0-4 0-5 1-2 2-2 2-2 4v2l-1-1h-4v-1-1h5v-1c-1-1-2-1-3-2l1-1z" class="V"></path><path d="M424 170c1-1 4-1 5-1 3 2 5 3 6 6 0 1-1 1-2 2v1c0 2 0 2-1 4l-1 2v-1c-1 0-2-1-2-1 0-1 1-1 0-2-1 0-5 1-7 0h0 1c-1-2-1-3-1-4v-2c0-2 0-2 2-4z" class="K"></path><path d="M424 175c1 0 2-1 3 0l1 1h0 0v1c-1 0-2 0-4-1v-1z" class="Z"></path><path d="M422 174v1h2v1c2 1 3 1 4 1-2 2-3 2-5 3-1-2-1-3-1-4v-2z" class="P"></path><path d="M424 170c1-1 4-1 5-1 3 2 5 3 6 6 0 1-1 1-2 2 0-1-1-2-2-3-2-3-3-2-6-2l-1-2z" class="d"></path><path d="M428 177v-1h0l3 1c1 0 1 1 2 1 0 2 0 2-1 4l-1 2v-1c-1 0-2-1-2-1 0-1 1-1 0-2-1 0-5 1-7 0h0 1c2-1 3-1 5-3z" class="H"></path><path d="M392 181l4 4c1 0 2 1 4 1h0c3 1 8 1 10 0l3-1c2 0 4-2 5-3 2 0 3 0 5 1h2 0c-1 2-3 3-4 5v1l-2 1h-1l-5 2c-2 0-3 1-5 1v1l1 1c-1 0-3 0-4 1-2 1-5 0-7 0-1-2-3-2-5-3s-3-2-4-3h0l1-1h0l1-1h1c1 0 1-1 1-1 0-2 0-4-1-6z" class="K"></path><path d="M400 186h0c3 1 8 1 10 0l3-1c-3 2-4 3-7 3h-3c0-1-3-2-3-2z" class="C"></path><path d="M423 183h2 0c-1 2-3 3-4 5h-3v-1c-1 0-1 0-2 1-1 0-4 1-5 1h0c1-2 3-2 5-3 1-1 2-2 3-2h1c0-1 1-1 2-1h0 1z" class="Z"></path><path d="M411 189h0c1 0 4-1 5-1 1-1 1-1 2-1v1h3v1l-2 1h-1l-5 2c-2 0-3 1-5 1-3 0-8 0-11-1h6c3-1 6-1 8-3z" class="J"></path><path d="M413 192l2-3c2 1 2 1 3 1l-5 2z" class="Z"></path><path d="M398 196c-1-2-3-2-5-3s-3-2-4-3h0l1-1c3 1 5 2 7 3 3 1 8 1 11 1v1l1 1c-1 0-3 0-4 1-2 1-5 0-7 0z" class="V"></path><path d="M408 149c1 0 3 0 4 2 1 0 1 0 1 1 1 0 4 0 4-1h1 1 1 1c0 1 1 2 2 3l6 3c2 1 3 2 4 2h0c-2 1-3 0-4 0h-1 0l-1 1c4 2 8 4 11 7v3l-3-3c-1-1-2-1-4-2-3-1-6 0-9 1l-2 3-1 1-1 1h-1l-1-2h0c-1 0-1 0-2 1l-1-1v1c0 2 0 4-1 6 0 0-1 1-2 1 0 2-2 2-3 3-2 1-4 1-6 0-1-1-2-1-3-2h-2l-1-1h-1-1l-2-6c0-1-1-1-2-1l-1-1c0 1-1 2-1 2h-1c0 1-1 2-2 2h0-3v-2c1-2 1-6 2-8l1-4c1-2 3-4 5-5h1 0l1 1s1-1 2-1c1 1 1 1 2 1 1-2 3-3 5-4h0l3-1 4-1h1z" class="P"></path><path d="M417 171c2-2 2-2 2-4 1-1 2-1 3-1l-2 3-1 1-1 1h-1z" class="X"></path><path d="M408 157l3-2c-1 2-1 3-1 4s1 2 1 3h0c1 1 1 1 2 1l1 1h-1l-2-1h-2c-1 1-1 1-1 0l-1-1c0-1-1-2-2-3l3-2z" class="K"></path><path d="M411 155c1-1 2-1 3-2h0c3 0 6 0 9 1l6 3c2 1 3 2 4 2h0c-2 1-3 0-4 0h-1 0l-1 1c-2-2-3-3-5-4-3-1-5-1-7 0-1 1-3 2-3 4h0-1v-1h-1c0-1 0-2 1-4z" class="S"></path><path d="M408 149c1 0 3 0 4 2 1 0 1 0 1 1 1 0 4 0 4-1h1 1 1 1c0 1 1 2 2 3-3-1-6-1-9-1h0c-1 1-2 1-3 2l-3 2-3 2c0 1-1 2-1 3v2l-3 2v-1c-1-5 2-9 6-12 0-1 2-2 3-2v-1l-2-1z" class="G"></path><path d="M408 149c1 0 3 0 4 2 1 0 1 0 1 1 1 0 4 0 4-1h1 1 1 1c0 1 1 2 2 3-3-1-6-1-9-1h0c-1 1-2 1-3 2l-3 2c0-1 0-1-1-1v-1h0l1 1h1c0-1 0-2 1-3h1l1-1c-1-1-1-1-2-1v-1l-2-1z" class="E"></path><path d="M405 159c1 1 2 2 2 3l1 1c0 1 0 1 1 0h2l2 1c1 1 1 2 2 2l1 3h0c-1 0-1 0-2 1l-1-1v1c0 2 0 4-1 6 0 0-1 1-2 1-1-1 0-2 0-3-1-1-2-1-3-1l-2-1c0-1-1-1-1-2-2-1-2-2-3-4l3-2v-2c0-1 1-2 1-3z" class="f"></path><path d="M411 168v-1c0-1 0-1 1-2 1 1 1 1 2 3l1-2 1 3h0c-1 0-1 0-2 1l-1-1v1c-1-1-1-2-2-2z" class="G"></path><path d="M411 168c1 0 1 1 2 2 0 2 0 4-1 6 0 0-1 1-2 1-1-1 0-2 0-3-1-1-2-1-3-1 1-3 2-4 4-5z" class="P"></path><path d="M405 159c1 1 2 2 2 3l1 1c0 1 0 1 1 0l1 1v1l-2 3-1 1c-1-1-2-3-3-5v-2c0-1 1-2 1-3z" class="I"></path><path d="M404 164v-2c1 1 1 2 2 2 1 2 1 3 2 4l-1 1c-1-1-2-3-3-5z" class="Q"></path><defs><linearGradient id="Ck" x1="405.58" y1="179.242" x2="397.473" y2="156.596" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#Ck)" d="M407 149h1l2 1v1c-1 0-3 1-3 2-4 3-7 7-6 12v1c1 2 1 3 3 4 0 1 1 1 1 2l2 1c1 0 2 0 3 1 0 1-1 2 0 3 0 2-2 2-3 3-2 1-4 1-6 0-1-1-2-1-3-2h-2l-1-1h-1-1l-2-6c0-1-1-1-2-1l-1-1c0 1-1 2-1 2h-1c0 1-1 2-2 2h0-3v-2c1-2 1-6 2-8l1-4c1-2 3-4 5-5h1 0l1 1s1-1 2-1c1 1 1 1 2 1 1-2 3-3 5-4h0l3-1 4-1z"></path><path d="M407 149h1l2 1v1c-1 0-3 1-3 2h-3c-1 1-2 1-2 2-1 1-2 1-2 2-3 4-3 8-3 13 0 0 1 3 0 4-1 0-2-2-3-3-2-5-1-11 1-16 1-2 3-3 5-4h0l3-1 4-1z" class="C"></path><path d="M407 149h1l2 1v1c-1 0-3 1-3 2h-3l1-1c1-1 1-1 1-2h-3l4-1z" class="W"></path><path d="M389 154h1 0l1 1s1-1 2-1c1 1 1 1 2 1-2 5-3 11-1 16 1 1 2 3 3 3l1 2v2h-2l-1-1h-1-1l-2-6c0-1-1-1-2-1l-1-1c0 1-1 2-1 2h-1c0 1-1 2-2 2h0-3v-2c1-2 1-6 2-8l1-4c1-2 3-4 5-5z" class="O"></path><path d="M389 154h1c0 2-2 5-3 7 0 2 1 4 0 6h-1c-1 1-1 3-1 4 1 1 0 0 1 0 0 1-1 2-2 2h0-3v-2c1-2 1-6 2-8l1-4c1-2 3-4 5-5z" class="I"></path><path d="M387 161c0 2 1 4 0 6h-1c-1 1-1 3-1 4 1 1 0 0 1 0 0 1-1 2-2 2h0c1-4 1-8 3-12z" class="G"></path><defs><linearGradient id="Cl" x1="283.675" y1="232.261" x2="434.876" y2="194.85" xlink:href="#B"><stop offset="0" stop-color="#030303"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#Cl)" d="M354 208h0c1-3 1-5 1-8 1-2 1-5 1-7v-3c0-2 0-4 1-6l1-1c0 1 0 2-1 2l1 1c0 1 0 6-1 7s-1 4-1 5l1 2v8h4 1l5 1h1l14 2 11 4h3 0c1 0 2 1 3 1l4 2v-1c1-1 5 0 6-1 3 0 6-1 8 0 1 1 3 1 4 1h0l4-1 2 2c-1 1 0 1-1 1h-4-1v1h-1c1 1 3 0 4 0 1-1 2 0 3-1v-3c2 0 5 1 7 2v1 1l1-1h1c1-1 2-1 2-1 2 2 3 4 5 5v1 1l-2 1-7-1h-8-34v1h2l-1 1-7 5c-3 3-6 2-10 2l-11 1c-2 0-4-1-6 0h-12-30-22c-2 0-5 0-7 1h0c-3-2-8-1-11-1 2 0 4 0 6-1l12-8c-2 2-4 5-6 7 4-2 7-5 11-8h3c-4-1-8 0-11 0-4 0-8 0-11-1 0-1 2-1 3-2 2-1 4-2 6-2 2-1 5-1 6-2s2-1 3-2h1l1-1c1 0 3 0 5-1l4-1 16-3c6-1 13-3 20-2 1 0 2-1 4-1 0 2 0 5 1 7h1c1-2 1-4 2-6z"></path><path d="M436 219c1-1 2-1 2-1 2 2 3 4 5 5v1 1l-2 1-7-1h-8 0 11c1 0 3-1 4 0h1v-1-1h0-1 0c-2 0-4-2-5-4h0z" class="C"></path><path d="M300 225h3 89v1h-58l-17-1c-5 0-9 0-13 1-2 0-3 0-4 1h0c-1 2-2 2-3 3-2 1-5 2-6 4h0l4 1c-2 0-5 0-7 1h0c-3-2-8-1-11-1 2 0 4 0 6-1l12-8c-2 2-4 5-6 7 4-2 7-5 11-8z" class="B"></path><path d="M295 235l-4-1h0c1-2 4-3 6-4 1-1 2-1 3-3h0c1-1 2-1 4-1 4-1 8-1 13-1l17 1h1c3 0 6-1 9 0-2 3-7 6-9 7-1 0-1 0-2 1-2 1-4 0-5 0h-2c-3 0-6 0-9 1h-22z" class="j"></path><path d="M392 226h2l-1 1-7 5c-3 3-6 2-10 2l-11 1c-2 0-4-1-6 0h-12-30c3-1 6-1 9-1h2c1 0 3 1 5 0 1-1 1-1 2-1 2-1 7-4 9-7-3-1-6 0-9 0h-1 58z" class="a"></path><defs><linearGradient id="Cm" x1="325.973" y1="217.806" x2="260.653" y2="186.081" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#515050"></stop></linearGradient></defs><path fill="url(#Cm)" d="M344 181h1c2 3 3 9 4 12 1 5 0 9 1 14-2 0-3 1-4 1-7-1-14 1-20 2l-16 3-4 1c-2 1-4 1-5 1l-1 1h-1c-1 1-2 1-3 2s-4 1-6 2c-2 0-4 1-6 2-1 1-3 1-3 2 3 1 7 1 11 1 3 0 7-1 11 0h-3c-4 3-7 6-11 8 2-2 4-5 6-7l-12 8c-2 1-4 1-6 1h-25-13c-1 0-2 1-2 1l-2 2s0-1-1-1l-1 5c-2 2-4 4-7 5h0c-1-2-2-4-4-5l-2-2-6-3-19-5h-2s-1 0-1-1l-2 1c-1-1-1-1-1-2s1-2 1-2l1-1 19-1 1-1c2 0 4 1 6 0h1 1 13 14v-1c-1-1-1-2-1-3v-4h0l-1-3c1-3 4-5 6-7l3-2-1-1h0c0-2-1-1-2-2 2-2 4-4 7-4h1c1 0 3-1 3-2 1 1 2 1 3 1h1 2c1 0 1-1 1-1 2 1 5 3 7 3h1c1 0 3 2 4 2-1 0-1 1-1 1v1 1l-1 1 7 1 6 1h12 0c11-1 21-6 29-13 2-3 6-7 7-10l3-3h1s1 1 1 2v-2z"></path><path d="M307 212h0 1v-1c1 0 2-1 3-1h0l-1 3-4 1c1 0 1 0 1-1v-1z" class="Q"></path><path d="M270 211v1h1v-1c1-1 1-1 1 0l1-1 3 3-1 1v1c-3-3-4-3-8-3 1-1 2-1 3-1z" class="P"></path><path d="M267 212h-3s0-1-1-2l2-1h2c2 1 2 1 3 2-1 0-2 0-3 1z" class="e"></path><path d="M307 212v1c0 1 0 1-1 1-2 1-4 1-5 1l-1 1c-2-1-4-1-6-1h6c1-1 1-1 1-2h1c2-1 3-1 5-1z" class="J"></path><path d="M258 218l2-2h1s0-1 1-1c2 0 6 0 7 1h-1-1-3l-2 2-1 1c-2 0-2-1-3-1z" class="P"></path><path d="M294 215c-2 0-3 1-5 1s-4 2-5 1h0c1-2 3-2 5-3 4-2 8-2 12-1 0 1 0 1-1 2h-6z" class="F"></path><path d="M258 218c1 0 1 1 3 1l1-1 2-2h3c-4 2-6 5-8 9h35c-3 1-6 0-9 0h-20-22c-4 0-8 1-11 0h14 3 10c0-1 0-2-1-4-1 0-1-1-1-2l1-1z" class="M"></path><path d="M254 208v1 1c1 0 1-3 4-2l2 2h0c-1 0-2-1-3-1h-1v1l-1 1h0c1 0 1-1 2-1s2 1 4 1h0c-4 2-8 3-10 7-1 1-1 2-2 2s-1 0-2-1c0-1-1-2-1-3-1 0 1-2 1-2 2-3 4-5 7-6z" class="P"></path><path d="M247 219c0-1-1-2-1-3-1 0 1-2 1-2v3c1 1 1 1 2 1h2c-1 1-1 2-2 2s-1 0-2-1z" class="Q"></path><path d="M268 196c2 1 5 3 7 3h1c1 0 3 2 4 2-1 0-1 1-1 1v1 1l-1 1 7 1 6 1 3 1h-5v1c-1 0-2 1-4 0h-1-2c-1 0-2-1-3-1-3-2-8-2-11-3h-5-1l1 2v2h-2-1c-2-2-3-2-5-2l-1 1c-3 1-5 3-7 6 0 0-2 2-1 2 0 1 1 2 1 3l-2-1c0 1 0 1 1 2v1 2h0s0 1 1 1h0c1 0 1 1 2 1h-3v-1c-1-1-1-2-1-3v-4h0l-1-3c1-3 4-5 6-7l3-2-1-1h0c0-2-1-1-2-2 2-2 4-4 7-4h1c1 0 3-1 3-2 1 1 2 1 3 1h1 2c1 0 1-1 1-1z" class="S"></path><defs><linearGradient id="Cn" x1="271.297" y1="195.91" x2="262.851" y2="208.204" xlink:href="#B"><stop offset="0" stop-color="#aba9a9"></stop><stop offset="1" stop-color="#dfdee1"></stop></linearGradient></defs><path fill="url(#Cn)" d="M268 196c2 1 5 3 7 3h1c1 0 3 2 4 2-1 0-1 1-1 1v1 1l-1 1c-8-2-15-4-22-1l-3 1-1-1h0c0-2-1-1-2-2 2-2 4-4 7-4h1c1 0 3-1 3-2 1 1 2 1 3 1h1 2c1 0 1-1 1-1z"></path><path d="M261 196c1 1 2 1 3 1l-1 1c-1 1-1 1-1 2h-1c-1 0-1 0-1-1-2 0-2 2-3 2h0c-1 0-2 1-2 2l1 1-3 1-1-1h0c0-2-1-1-2-2 2-2 4-4 7-4h1c1 0 3-1 3-2z" class="D"></path><path d="M252 204c0-2 0-2 1-3s2-1 3-1l1 1c-1 0-2 1-2 2l1 1-3 1-1-1z" class="g"></path><path d="M344 181h1c2 3 3 9 4 12 1 5 0 9 1 14-2 0-3 1-4 1-7-1-14 1-20 2h-2c-1-1-1-4-1-5 1-2 5-4 7-5 1-1 1-2 2-3 1 0 3-1 3-2s1-3 2-4c2-2 5-4 6-7l1-1v-2z" class="I"></path><path d="M343 184c1 1 1 1 1 2 1 3 1 5 1 8l-1 1h-1l-2-2v-3c-1 1-1 3-3 4 0 1-2 1-3 1 0-1 1-3 2-4 2-2 5-4 6-7z" class="P"></path><defs><linearGradient id="Co" x1="349.172" y1="205.862" x2="344.79" y2="181.69" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#464544"></stop></linearGradient></defs><path fill="url(#Co)" d="M344 181h1c2 3 3 9 4 12 1 5 0 9 1 14-2 0-3 1-4 1l1-1c1-3 0-5 0-8 0-5 0-9-3-13 0-1 0-1-1-2l1-1v-2z"></path><path d="M211 225c2 0 4 1 6 0h1 1 13c3 1 7 0 11 0h22 20c3 0 6 1 9 0h6c-4 3-7 6-11 8 2-2 4-5 6-7l-12 8c-2 1-4 1-6 1h-25-13c-1 0-2 1-2 1l-2 2s0-1-1-1l-1 5c-2 2-4 4-7 5h0c-1-2-2-4-4-5l-2-2-6-3-19-5h-2s-1 0-1-1l-2 1c-1-1-1-1-1-2s1-2 1-2l1-1 19-1 1-1z" class="j"></path><path d="M212 230l5 1 2 1 7 2h-2 0c0 2-2 3-3 4v1h2c0 1-1 2-1 3l-2-2-6-3c-1 0-1-1-2-2l1-1c0-2 0-2-1-4z" class="T"></path><path d="M219 232l7 2h-2 0c0 2-2 3-3 4 0-1 1-2 2-3v-1h-1l-2 3-1-1 1-2c0-1-1-2-1-2z" class="F"></path><path d="M212 230l5 1v1 4l1 2h2v2l-6-3c-1 0-1-1-2-2l1-1c0-2 0-2-1-4z" class="e"></path><path d="M190 232c-1-1-1-1-1-2s1-2 1-2c2 1 4 0 5 1l17 1c1 2 1 2 1 4l-1 1c1 1 1 2 2 2l-19-5h-2s-1 0-1-1l-2 1z" class="W"></path><path d="M190 232c-1-1-1-1-1-2s1-2 1-2c2 1 4 0 5 1h-3 0c1 1 1 1 1 2h-1l-2 1z" class="I"></path><path d="M226 234c3 0 6 0 8 2v1l-1 5c-2 2-4 4-7 5h0c-1-2-2-4-4-5 0-1 1-2 1-3h-2v-1c1-1 3-2 3-4h0 2z" class="E"></path><path d="M301 123h0c1-2 0-1 0-3 2-1 3-1 4-2l1-1v4l2-2h1 1c-2 4-5 7-8 9l2 2h0c1 1 1 1 2 1l1 2c0 1 0 2 1 3 2 2 5 4 7 4 1 1 2 1 2 2 1 0 1 1 2 1-1 1-1 1-1 2h1c1 0 0 0 1 1h0 0l1 1v1c-1 0-1 1-1 2l4 1h0c0 1 1 1 1 1 0 1-1 1-2 2l3 2-1 1 1 2h1c3 0 3 1 6 2l-1 2c1 1 2 3 3 4h1c1-1 2-1 3-1l-1 2 1 1 3-3s1 0 1 1c1 1 1 2 2 2v-1h0c1 1 1 2 1 3h0l-3 1h0c0 1 1 2 2 2v1h0c0 1 1 2 0 3l-1 1v2 2c0-1-1-2-1-2h-1l-3 3c-1 3-5 7-7 10-8 7-18 12-29 13h0-12l-6-1-7-1 1-1v-1-1s0-1 1-1c-1 0-3-2-4-2h-1c-2 0-5-2-7-3 0 0 0 1-1 1h-2-1c-1 0-2 0-3-1 0 1-2 2-3 2h-1c-3 0-5 2-7 4 1 1 2 0 2 2h0l1 1-3 2c-2 2-5 4-6 7l1 3h0v4c0 1 0 2 1 3v1h-14-13-1-1c-2 1-4 0-6 0l-1 1-19 1c1-1 1-2 2-2 1-1 3-2 4-3l3-2c-1 0-3 1-3 2-1 0-1 0-2 1h-1 0-1c1-1 3-2 4-3v-1c-2 2-5 4-7 5h-2c2-2 5-4 7-6l6-5c1-1 2-3 3-4 1 0 1 0 2-1v-2c1-1 4-3 5-4l3 1c1-1 1-2 2-2v-1l1-1 2-2c1-2 3-3 4-6h0c1-2 2-3 4-3 0-2 1-3 1-4-1 0-2-1-3-1v1-2l-2 2h-1v-7-1c1-4 5-7 8-10l5-3 7-4 2-3c1 0 2-1 4-1h1 0 2c1-2 5-3 7-4l1-1c1 1 2 1 3 1 5-2 10-5 15-8 3-2 6-3 8-5l7-5v-3-1-1c1 0 1-1 2-2 0 1 0 1 1 1 0-1 1-1 2-2s2-2 2-4c1 0 2 0 2 2z" class="L"></path><path d="M268 156h1c-1 2-5 5-7 6h-1c2-3 4-4 7-6z" class="D"></path><path d="M239 181c2 0 4 0 5 1l-7 5v-1c0-2 0-2 1-3l1-2z" class="J"></path><path d="M273 158v-2c3-3 9-5 13-5l2 1c-4 1-7 1-11 3-2 1-3 2-4 3z" class="F"></path><path d="M240 188c1-1 3-4 5-4 0 0 1 0 1-1h1 1l-1 2h1l1-1c1 0 1 0 2 1h0 1v1c-2 0-6 2-7 1h-1s-1 1-2 1h-2z" class="O"></path><path d="M240 188h2l-9 8-1 1h-1l-1-2 1-2h2c2-1 4-3 7-5z" class="B"></path><path d="M286 151c2-1 4-1 6 0s3 1 5 1v-1c1 1 3 3 3 5 0 1-1 1-2 1l-1-2c-3-2-6-3-9-3l-2-1z" class="O"></path><path d="M288 152c3 0 6 1 9 3l-1 2-1-1h-2c-4-2-7-2-11-1h-2c-1 1-2 1-3 0 4-2 7-2 11-3z" class="S"></path><path d="M252 185c0-3 1-5 2-7h1l5 13v3c-1 0-1 0-1-1-1-1-2-2-2-3v-1c0-1-1-2-2-3h-1-2v-1z" class="C"></path><path d="M242 188c1 0 2-1 2-1h1c1 1 5-1 7-1h2c-1 2-4 3-6 4l-6 4-4 1c-1 1-1 2-3 3h0l-1 1-1-1v-2l9-8z" class="d"></path><path d="M238 195c2-2 3-3 5-4v-1c2-1 3 0 5 0l-6 4-4 1z" class="C"></path><path d="M270 152c7-5 15-8 23-8 3 1 6 2 8 2 1 1 1 0 1 1l-3 1c0-1 0-1-1-2-2 0-3 2-4 2h-2-5s0-1-1-1-2 1-4 1c-5 2-9 4-14 8-3 2-5 3-7 6 0 1-1 2-2 3 0 2 1 2 0 4l-2-1c-1 2 0 4-2 4h-1v-4l-2 3v-1c0-1 1-3 2-4 1-2 2-4 4-6v-1l-1-1-2 1h-1s-1 0-1 1l-4-1c2 0 3-1 4-1l11-5h2c1 0 0-1 2-1h2z" class="g"></path><path d="M264 153h2c1 0 0-1 2-1h2c-7 4-11 10-16 16l-2 3v-1c0-1 1-3 2-4 1-2 2-4 4-6v-1l-1-1-2 1h-1s-1 0-1 1l-4-1c2 0 3-1 4-1l11-5z" class="T"></path><path d="M226 173l1 1v2l1 1h-1l2 2v-2h0c1 1 1 2 2 3h1c2 2 4 2 6 3-1 1-1 1-1 3v1l-4 3h0l-2 3-1 2 1 2h1l1-1v2l1 1c-1 1-2 1-3 1h0l-4 1h0-1c-1 1-2 1-4 1l2-4 3-10c0-2 1-3 1-4-1 0-2-1-3-1v1-2l-1-3c0-2 1-5 2-6z" class="I"></path><path d="M224 198s0 1 1 1v2h1c-1 1-2 1-4 1l2-4z" class="Q"></path><path d="M227 201v-1l-1-2v-1l1 1 3 2h1l-4 1z" class="K"></path><path d="M229 179v1c0 1-1 1-1 1-1 1-1 1-2 0v-2c1-1 1-1 1-2l2 2z" class="H"></path><path d="M232 197l1-1v2l1 1c-1 1-2 1-3 1h0-1l-3-2c1-1 1-1 2-1h0 3z" class="C"></path><path d="M231 180h1c2 2 4 2 6 3-1 1-1 1-1 3v1l-4 3h0l-2 3-1 2c0-2 1-4 0-6h0v-2c0-1-1-2-1-3 1-1 2-2 2-3v-1z" class="P"></path><path d="M233 190c-1 0-1 0-2-1v-1c1-1 1-1 2-1 1 1 0 1 0 3h0z" class="K"></path><path d="M277 155c1 1 2 1 3 0h2c-1 1-1 1-2 1l-4 4h0c-1 1-1 2-2 3h0c0 1-1 1-1 2v1h-1c-4 2-6 4-7 8v2 1h2l1 1v2 2c0 2 0 3 1 5l4 6s1 1 2 1v1l-2 2 3 2h-1c-2 0-5-2-7-3-1-2-2-3-3-4-3-4-6-12-5-17l1-3c1-6 6-11 12-14 1-1 2-2 4-3z" class="C"></path><path d="M271 194l1-1h1s1 1 2 1v1l-2 2c-1-1-2-2-2-3z" class="O"></path><path d="M264 184c-1-1-2-4-2-6v-1c0-1 0-2 1-4 0-1 1-2 1-3 2-2 5-3 7-5 1-1 1-1 3-2 0 1-1 1-1 2v1h-1c-4 2-6 4-7 8v2l-1 3c-1 1-1 2 0 2v3z" class="e"></path><path d="M264 184v-3c-1 0-1-1 0-2l1-3v1h2l1 1v2 2c0 2 0 3 1 5l4 6h-1l-1 1h-1c-3-3-5-6-6-10z" class="X"></path><path d="M265 183v-2-3h0 2c1 1 0 2 0 4 0 0-1 1-2 1z" class="D"></path><path d="M267 182h1c0 2 0 3 1 5l4 6h-1l-1 1h-1l-5-11c1 0 2-1 2-1z" class="f"></path><defs><linearGradient id="Cp" x1="231.663" y1="178.278" x2="251.586" y2="169.341" xlink:href="#B"><stop offset="0" stop-color="#4a494a"></stop><stop offset="1" stop-color="#9f9d9e"></stop></linearGradient></defs><path fill="url(#Cp)" d="M255 159l2-1 1 1v1c-2 2-3 4-4 6-1 1-2 3-2 4v1c-1 4-4 8-8 11h0c-1-1-3-1-5-1l-1 2c-2-1-4-1-6-3h-1c-1-1-1-2-2-3h0v-1c0-2 2-2 2-4 1-1 3-2 4-3 1-2 3-3 5-4l2-2c2-1 4-2 7-4l4 1c0-1 1-1 1-1h1z"></path><path d="M229 177h2c1 0 1-2 2-1 1 0 1 1 1 1h2v-1 1c1 0 2 0 3 1h-1c-2 0-4 0-6 1v1h-1c-1-1-1-2-2-3z" class="Z"></path><path d="M239 178c1 0 2 1 3 1h0l1 1s1 1 1 2h0c-1-1-3-1-5-1l-1 2c-2-1-4-1-6-3v-1c2-1 4-1 6-1h1z" class="T"></path><path d="M239 181l-2-2v-1h1l1 1h1c1 0 1 1 2 1h1s1 1 1 2h0c-1-1-3-1-5-1z" class="F"></path><path d="M255 159l2-1 1 1v1c-2 2-3 4-4 6l-1-1c-1 0-2 2-3 3v1 1h-1 0c-1 0-2 0-3 1l-1 1h0c-1 1-1 1-1 2h1v1c-2 1-3 0-5-1h0c-1-1-1-2-2-2l-1-1 1-1c1-1 1-1 1-2l1-1v-2l2-2c2-1 4-2 7-4l4 1c0-1 1-1 1-1h1z" class="B"></path><path d="M249 159l4 1c0-1 1-1 1-1h1c-4 2-8 3-11 5-1 1-3 2-4 3l1 1c0 1-1 2-2 2h-1c1-1 1-1 1-2l1-1v-2l2-2c2-1 4-2 7-4z" class="c"></path><path d="M238 170h1c1 0 2-1 2-2l-1-1c1-1 3-2 4-3l-1 2 1 1v1c0 1-2 3-1 5h0v-1h1l1-1c0-1 1-2 1-2h3 1v1h-1 0c-1 0-2 0-3 1l-1 1h0c-1 1-1 1-1 2h1v1c-2 1-3 0-5-1h0c-1-1-1-2-2-2l-1-1 1-1z" class="E"></path><defs><linearGradient id="Cq" x1="250.278" y1="206.54" x2="243.11" y2="192.429" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#Cq)" d="M254 186h1c1 1 2 2 2 3v1c0 1 1 2 2 3 0 1 0 1 1 1v-3l1 5c0 1-2 2-3 2h-1c-3 0-5 2-7 4 1 1 2 0 2 2h0l1 1-3 2c-2 2-5 4-6 7l1 3-2 2s0 1-1 1c-1-1-2-3-3-4-2-3-4-5-6-6v-1c-1-1-1-1-1-2v-1c-1-1-1-2-1-3-1 0-1 0-2 1h-2v-3h0l4-1h0c1 0 2 0 3-1l1-1h0c2-1 2-2 3-3l4-1 6-4c2-1 5-2 6-4z"></path><path d="M238 195l4-1c-1 1-4 4-5 4h-2 0c2-1 2-2 3-3z" class="I"></path><path d="M235 202c1-1 1-1 1-2h1v1c0 1-1 2-1 3h-1c0 1-2 1-2 2h-1c-1-1-1-2-1-3h1c1-1 2-1 3-1z" class="Q"></path><path d="M232 203c1 0 3 0 3 1s-2 1-2 2h-1c-1-1-1-2-1-3h1z" class="W"></path><path d="M231 200c2 0 3 0 4 1v1c-1 0-2 0-3 1h-1c-1 0-1 0-2 1h-2v-3h0l4-1h0z" class="Y"></path><path d="M236 209c1-1 2-1 2-1l1-1c1 1 1 2 1 4h1c1 0 1-1 1-2v-1l1-1c0-1 1-1 1-1-1 2-2 4-2 7l1 1h1l1 3-2 2v-1s1 0 1-1l-1-1c-1-2-3-4-5-6l-2-1z" class="R"></path><path d="M232 206h1l3 3 2 1c2 2 4 4 5 6l1 1c0 1-1 1-1 1v1s0 1-1 1c-1-1-2-3-3-4-2-3-4-5-6-6v-1c-1-1-1-1-1-2v-1z" class="B"></path><path d="M238 210c2 2 4 4 5 6 0 1 0 1-1 1-1-1-2-3-3-4l-2-2 1-1z" class="O"></path><path d="M250 202c1 1 2 0 2 2h0l1 1-3 2c-2 2-5 4-6 7h-1l-1-1c0-3 1-5 2-7 2-2 4-3 6-4z" class="G"></path><path d="M252 204l1 1-3 2h-3v-1c0-1 0-1 1-2h2 2 0z" class="Y"></path><path d="M301 123h0c1-2 0-1 0-3 2-1 3-1 4-2l1-1v4l2-2h1 1c-2 4-5 7-8 9l2 2h0c1 1 1 1 2 1l1 2c0 1 0 2 1 3 2 2 5 4 7 4 1 1 2 1 2 2 1 0 1 1 2 1-1 1-1 1-1 2h1c1 0 0 0 1 1h0 0l1 1v1c-1 0-1 1-1 2v1l-1 1h-1c-1-1-3-1-4-2l-1 1v1c-1 1-1 1-1 2h-1l-1-1c-1 0-2-1-2-1-1-2-3-2-4-4-1 0-1-1-2-1 0-1 0 0-1-1-2 0-5-1-8-2-8 0-16 3-23 8h-2c-2 0-1 1-2 1h-2l-11 5c-1 0-2 1-4 1-3 2-5 3-7 4l-2 2c-2 1-4 2-5 4-1 1-3 2-4 3 0 2-2 2-2 4v1 2l-2-2h1l-1-1v-2l-1-1c-1 1-2 4-2 6l1 3-2 2h-1v-7-1c1-4 5-7 8-10l5-3 7-4 2-3c1 0 2-1 4-1h1 0 2c1-2 5-3 7-4l1-1c1 1 2 1 3 1 5-2 10-5 15-8 3-2 6-3 8-5l7-5v-3-1-1c1 0 1-1 2-2 0 1 0 1 1 1 0-1 1-1 2-2s2-2 2-4c1 0 2 0 2 2z" class="h"></path><path d="M259 150c1 1 2 1 3 1l-20 8 2-3c1 0 2-1 4-1h1 0 2c1-2 5-3 7-4l1-1z" class="M"></path><path d="M226 173c3-3 6-5 9-7 1-1 3-1 4-2 1 0 2-1 3-1l-2 2c-2 1-4 2-5 4-1 1-3 2-4 3 0 2-2 2-2 4v1 2l-2-2h1l-1-1v-2l-1-1z" class="C"></path><path d="M301 123h0c1-2 0-1 0-3 2-1 3-1 4-2l1-1v4c-4 4-9 9-14 12v-3-1-1c1 0 1-1 2-2 0 1 0 1 1 1 0-1 1-1 2-2s2-2 2-4c1 0 2 0 2 2z" class="G"></path><path d="M299 121c1 0 2 0 2 2l-3 3c-1-1-1 0-1-1 1-1 2-2 2-4z" class="D"></path><path d="M301 130h1v6 4h-1v1h-2 0c-2 1-1 0-3 0-1 0-4 1-5 2v1h2 0c-8 0-16 3-23 8h-2c-2 0-1 1-2 1h-2c5-4 11-6 17-9 7-4 14-9 20-14z" class="b"></path><path d="M302 128l2 2h0c1 1 1 1 2 1l1 2c0 1 0 2 1 3 2 2 5 4 7 4 1 1 2 1 2 2 1 0 1 1 2 1-1 1-1 1-1 2h1c1 0 0 0 1 1h0 0l1 1v1c-1 0-1 1-1 2v1l-1 1h-1c-1-1-3-1-4-2l-1 1v1c-1 1-1 1-1 2h-1l-1-1c-1 0-2-1-2-1-1-2-3-2-4-4-1 0-1-1-2-1 0-1 0 0-1-1-2 0-5-1-8-2h0-2v-1c1-1 4-2 5-2 2 0 1 1 3 0h0 2v-1h1v-4-6h-1l1-2z" class="R"></path><path d="M308 138c1 1 1 2 1 3s0 1-1 2c-1 0-3 0-4-1l1-2h0 1c1 0 1-1 2-2z" class="L"></path><path d="M320 146l1 1v1c-1 0-1 1-1 2v1l-1 1h-1c-1-1-3-1-4-2l-3-1h0 0 2-1c-1-1-2-1-3-2h0 0 2l6 3c1-1 2-2 3-4h0z" class="D"></path><path d="M293 144c3 0 7 0 10 1 2 0 4 1 6 2h0c1 1 2 1 3 2h1-2 0 0l3 1-1 1v1c-1 1-1 1-1 2h-1l-1-1c-1 0-2-1-2-1-1-2-3-2-4-4-1 0-1-1-2-1 0-1 0 0-1-1-2 0-5-1-8-2h0z" class="a"></path><path d="M304 148h3c2 1 3 1 4 1h0l3 1-1 1v1c-1 1-1 1-1 2h-1l-1-1c-1 0-2-1-2-1-1-2-3-2-4-4z" class="M"></path><path d="M307 148c2 1 3 1 4 1h0l-1 1c1 1 1 1 1 2l-1 1c-1-1-2-3-3-5z" class="G"></path><path d="M302 128l2 2h0c1 1 1 1 2 1l1 2c0 1 0 2 1 3v2h0c-1 1-1 2-2 2h-1 0l-1 2c-1 1-1 1-2 1h-1c-1 1-2 0-3 0h0c1-1 3-2 4-2h1c-1 0-1-1-2-1h1v-4-6h-1l1-2z" class="I"></path><path d="M304 130c1 1 1 1 2 1l1 2c0 1 0 2 1 3v2h0c-1 1-1 2-2 2h-1 0l-1-10z" class="B"></path><path d="M305 140c0-1 1-2 1-3 1 0 1 0 2 1h0c-1 1-1 2-2 2h-1z" class="Y"></path><path d="M223 191h0c1-2 2-3 4-3l-3 10-2 4c2 0 3 0 4-1h1v3h2c1-1 1-1 2-1 0 1 0 2 1 3v1c0 1 0 1 1 2v1c2 1 4 3 6 6 1 1 2 3 3 4 1 0 1-1 1-1l2-2h0v4c0 1 0 2 1 3v1h-14-13-1-1c-2 1-4 0-6 0l-1 1-19 1c1-1 1-2 2-2 1-1 3-2 4-3l3-2c-1 0-3 1-3 2-1 0-1 0-2 1h-1 0-1c1-1 3-2 4-3v-1c-2 2-5 4-7 5h-2c2-2 5-4 7-6l6-5c1-1 2-3 3-4 1 0 1 0 2-1v-2c1-1 4-3 5-4l3 1c1-1 1-2 2-2v-1l1-1 2-2c1-2 3-3 4-6z" class="Q"></path><path d="M210 226l3-4c3-3 7-4 12-5v1c-1 1-2 1-3 2l2 1h-1c-1 1-3 2-4 3v1h-1-1c-2 1-4 0-6 0l-1 1z" class="Y"></path><path d="M222 220l2 1h-1c-1 1-3 2-4 3v1h-1-1c-2 1-4 0-6 0 2-1 5-3 7-4 1 0 3-1 4-1z" class="J"></path><path d="M233 210c2 1 4 3 6 6h-1c-2-1-3-2-4-4-2 0-3-1-4-1-2-1-4 0-6 0h0c-2 1-3 1-4 1-2 0-2 0-3 1h-1c-1 0-1 0-2 1h0c-1 1-1 1-2 1-2 1-5 2-7 4h-2c-2 0-3 3-6 3l3-2c2-1 4-4 7-5 1 0 3-2 5-2 2-1 4-2 6-2 5-1 9-2 14-1v1l1-1z" class="C"></path><defs><linearGradient id="Cr" x1="229.981" y1="228.091" x2="234.321" y2="218.255" xlink:href="#B"><stop offset="0" stop-color="#10100e"></stop><stop offset="1" stop-color="#434243"></stop></linearGradient></defs><path fill="url(#Cr)" d="M225 217c2 0 3 1 5 1h1c1 0 2-1 3-1v1c2 1 3 1 5 1l3 4c1 0 1 0 2 1h2v1h-14-13v-1c1-1 3-2 4-3h1l-2-1c1-1 2-1 3-2v-1z"></path><path d="M231 218c1 0 2-1 3-1v1c2 1 3 1 5 1l3 4c-3-1-5-3-7-4h0c-1-1-1-1-2-1h-1c1 1 1 2 1 3 0-1-1-2-2-3z" class="I"></path><path d="M225 217c2 0 3 1 5 1h1c1 1 2 2 2 3h0v1s-5-2-6-2-2 1-3 1l-2-1c1-1 2-1 3-2v-1z" class="B"></path><path d="M223 191h0c1-2 2-3 4-3l-3 10-2 4c2 0 3 0 4-1h1v3h2c1-1 1-1 2-1 0 1 0 2 1 3v1c0 1 0 1 1 2v1l-1 1v-1c-5-1-9 0-14 1-2 0-4 1-6 2-2 0-4 2-5 2-3 1-5 4-7 5-1 0-3 1-3 2-1 0-1 0-2 1h-1 0-1c1-1 3-2 4-3v-1c-2 2-5 4-7 5h-2c2-2 5-4 7-6l6-5c1-1 2-3 3-4 1 0 1 0 2-1v-2c1-1 4-3 5-4l3 1c1-1 1-2 2-2v-1l1-1 2-2c1-2 3-3 4-6z" class="H"></path><path d="M205 214c0-2 0-3 2-4s3-3 5-4v1s0 1 1 2l2-2v2c-3 1-7 2-10 5z" class="E"></path><path d="M216 201h1l-1 2-2 1c0 1 1 1 1 2h-2v-2h-1c-4 3-7 7-11 9 1-1 2-3 3-4 1 0 1 0 2-1v-2c1-1 4-3 5-4l3 1c1-1 1-2 2-2z" class="D"></path><path d="M223 191l1 1c0 1-1 2-1 2v2c0 1 0 1-1 2 0 1-1 1-2 2h0 0c-1 2-2 2-2 4v1c-1-1-2-1-2-2l1-2h-1v-1l1-1 2-2c1-2 3-3 4-6z" class="E"></path><path d="M217 199l2-2c0 3 0 3-2 4h0-1v-1l1-1z" class="f"></path><path d="M223 191h0c1-2 2-3 4-3l-3 10-2 4c2 0 3 0 4-1h1v3h2l-1 1-1-1-1 1-1 1h-1v-1c0-1-1-1-1-1h-1c-1 1-1 2-2 2-1-1 0-2 0-3l-1-1c1 0 1-1 1-2h0c1-1 2-1 2-2 1-1 1-1 1-2v-2s1-1 1-2l-1-1z" class="G"></path><path d="M226 201h1v3h2l-1 1-1-1-1 1-1 1h-1v-1c0-1-1-1-1-1h-1c-1 1-1 1-2 0 0-1 0-1 1-2h1c2 0 3 0 4-1z" class="D"></path><path d="M223 204c1 0 2-1 3-1l1 1-1 1-1 1h-1v-1c0-1-1-1-1-1z" class="B"></path><path d="M229 204c1-1 1-1 2-1 0 1 0 2 1 3v1c0 1 0 1 1 2v1l-1 1v-1c-5-1-9 0-14 1-2 0-4 1-6 2-2 0-4 2-5 2-3 1-5 4-7 5-1 0-3 1-3 2-1 0-1 0-2 1h-1 0-1c1-1 3-2 4-3v-1c2-2 6-4 8-5 3-3 7-4 10-5v-2c2 0 3 0 5 1 1-1 1-1 1-2 1 0 2-1 3-1v1h1l1-1 1-1 1 1 1-1z" class="h"></path><path d="M215 207c2 0 3 0 5 1 1-1 1-1 1-2 1 0 2-1 3-1v1c1 1 1 1 2 1h1l-12 2v-2z" class="U"></path><path d="M229 204c1-1 1-1 2-1 0 1 0 2 1 3v1c0 1 0 1 1 2l-6-2h-1c-1 0-1 0-2-1h1l1-1 1-1 1 1 1-1z" class="E"></path><path d="M229 204c1-1 1-1 2-1 0 1 0 2 1 3v1l-4-2 1-1z" class="O"></path><path d="M302 147c1 0 1 1 2 1 1 2 3 2 4 4 0 0 1 1 2 1l1 1h1c0-1 0-1 1-2v-1l1-1c1 1 3 1 4 2h1l1-1v-1l4 1h0c0 1 1 1 1 1 0 1-1 1-2 2l3 2-1 1 1 2h1c3 0 3 1 6 2l-1 2c1 1 2 3 3 4h1c1-1 2-1 3-1l-1 2 1 1 3-3s1 0 1 1c1 1 1 2 2 2v-1h0c1 1 1 2 1 3h0l-3 1h0c0 1 1 2 2 2v1h0c0 1 1 2 0 3l-1 1v2 2c0-1-1-2-1-2h-1l-3 3c-1 3-5 7-7 10-8 7-18 12-29 13h0-12l-6-1-7-1 1-1v-1-1s0-1 1-1c-1 0-3-2-4-2l-3-2 2-2v-1c-1 0-2-1-2-1l-4-6c-1-2-1-3-1-5v-2-2l-1-1h-2v-1-2c1-4 3-6 7-8h1v-1c0-1 1-1 1-2h0c1-1 1-2 2-3h0l4-4c1 0 1 0 2-1 4-1 7-1 11 1h2l1 1 1-2 1 2c1 0 2 0 2-1 0-2-2-4-3-5h0l-1-1c1-1 1 0 2-1 0 0 1 0 1-1l3-1z" class="S"></path><path d="M304 165h0 1l1 1c0 1 0 1-1 2-1 0-1 0-2 1 0-2 0-3 1-4z" class="W"></path><path d="M302 147c1 0 1 1 2 1 1 2 3 2 4 4l-2 1c-3-2-6-3-8-3l-1 1-1-1c1-1 1 0 2-1 0 0 1 0 1-1l3-1z" class="O"></path><path d="M307 169v-3-1c0-2-1-4-1-7 0 0-4-4-4-5 1 1 2 2 3 2 1 1 2 1 4 2h-1-1 0c0 1 2 3 2 5 0 1 0 4-1 5 0 1 0 2-1 2z" class="I"></path><path d="M303 161c0 2 0 3 1 4-1 1-1 2-1 4-2 2-4 3-6 4-1-1-2-2-2-3l-3-4 2 1c0 1 1 2 2 3 1 0 1-1 1-1 1 0 2 1 2 0 0 0 2-1 2-2l2-1v-5z" class="E"></path><path d="M291 166h1l3 4c-1 0-2 0-3-1l-1 1v1c1 3 3 3 5 4 0 1 0 2 1 2h0l2 1v2l-4-2c-2-1-4-3-5-5 0-2 0-5 1-7z" class="I"></path><path d="M297 163l2 1v1l1 1h0c0 1 0 1 1 1h0c0 1-2 2-2 2 0 1-1 0-2 0 0 0 0 1-1 1-1-1-2-2-2-3l-2-1h-1c1 0 2 0 3-1h0c0-1 0-1 1-1h1v-1h1 0z" class="O"></path><path d="M297 163l2 1v1l1 1h0c-1 1-1 2-3 2l-3-3c0-1 0-1 1-1h1v-1h1 0z" class="K"></path><path d="M297 163l2 1v1l-2 1h0v-3h0z" class="C"></path><path d="M297 155l1 2c1 0 2 0 2-1 1 2 2 4 3 5v5l-2 1h0c-1 0-1 0-1-1h0l-1-1v-1l-2-1h0-1v1h-1c-1 0-1 0-1 1h0c-1-1-1-2-2-3h-1-1c1-3 1-5 3-6h2l1 1 1-2z" class="W"></path><path d="M292 162c1-1 2-3 3-4l1 1c1 2 1 2 1 4h0-1v1h-1c-1 0-1 0-1 1h0c-1-1-1-2-2-3z" class="R"></path><path d="M296 159c1 2 1 2 1 4h0-1-1s0-1-1-2c1 0 1-1 1-1 0-1 1-1 1-1z" class="N"></path><path d="M300 156c1 2 2 4 3 5v5l-2 1h0c-1 0-1 0-1-1h0v-1c1-2-1-6-2-8 1 0 2 0 2-1z" class="f"></path><path d="M312 154c0-1 0-1 1-2v-1l1-1c1 1 3 1 4 2 0 1 1 1 1 2-1 1-3 1-4 2l1 2c0 1 0 2-1 3 0 1 2 3 2 4l1 1c-2 0-4 0-5 1 0 1-1 2-2 2h0c1-2 2-3 3-4-1 0-2-1-3-1l1-1c0-2-3-4-4-6h1l3 3-2-2c0-1 0-1-1-2v-1c-1-1-2-2-3-2l2-1s1 1 2 1l1 1h1z" class="H"></path><path d="M314 158c0-1 1-1 1-2l1 2c0 1 0 2-1 3l-1-3z" class="N"></path><path d="M309 155c3 3 4 6 5 9v1c-1 0-2-1-3-1l1-1c0-2-3-4-4-6h1l3 3-2-2c0-1 0-1-1-2v-1z" class="C"></path><path d="M312 154c0-1 0-1 1-2v-1l1-1c1 1 3 1 4 2 0 1 1 1 1 2-1 1-3 1-4 2 0 1-1 1-1 2 0-1-2-3-2-4z" class="F"></path><path d="M307 169c1 0 1-1 1-2 1-1 1-4 1-5 0-2-2-4-2-5h0 1c1 2 4 4 4 6l-1 1c1 0 2 1 3 1-1 1-2 2-3 4h0c0 2 0 3-1 5l-1 1c0 2-2 3-3 4-3 1-5 1-7 1v-2l-2-1c1-1 1-2 2-2l1-1c0-1 0-1 1-2 1 0 2 0 3 1h0c1 0 1-1 1-1 0-1 1-2 2-3z" class="Q"></path><path d="M306 176l-1-2h1c1 0 1 1 2 1s1 0 1-1 1-2 1-3c-1-1-1-1-1-2l2-1v-3-1h0c1 0 2 1 3 1-1 1-2 2-3 4h0c0 2 0 3-1 5l-1 1c0 2-2 3-3 4v-3z" class="J"></path><path d="M297 177c1-1 1-2 2-2l1-1c0-1 0-1 1-2 1 0 2 0 3 1h0c1 0 1-1 1-1v1c-1 1-3 2-4 3 1 1 1 1 2 1 1-1 2-1 3-1v3c-3 1-5 1-7 1v-2l-2-1z" class="W"></path><path d="M292 162c1 1 1 2 2 3-1 1-2 1-3 1-1 2-1 5-1 7 1 2 3 4 5 5l4 2c2 0 4 0 7-1 1-1 3-2 3-4l1-1c1-2 1-3 1-5 1 0 2-1 2-2 1-1 3-1 5-1 0 1 1 2 1 3v1h-2-2v1c-1 3-1 5-3 8-2 0-3 1-4 2h-1c-3 1-5 2-8 1h-1c-2 0-4-1-5-1-2-1-3-2-4-3l-1-1-1 1h-1c-1 0-2-1-3-1h0v-2l-1-1h0 2c0-1 1-1 1-1l-1-1c1 0 1 0 2-1h1 1v-3c1-3 2-4 4-6z" class="Y"></path><path d="M312 174h0c0-2 1-4 1-6l1 1c0 1 0 1 1 1v1c-1 1-2 3-3 3z" class="M"></path><path d="M318 166c0 1 1 2 1 3v1h-2-2c-1 0-1 0-1-1h1l3-3z" class="B"></path><path d="M289 178l1-1c3 2 5 4 8 5-2 0-4-1-5-1-2-1-3-2-4-3z" class="E"></path><path d="M312 174c1 0 2-2 3-3-1 3-1 5-3 8-2 0-3 1-4 2h-1c2-2 3-4 5-7z" class="U"></path><path d="M282 155c4-1 7-1 11 1-2 1-2 3-3 6h1 1c-2 2-3 3-4 6v3h-1-1c-1 1-1 1-2 1l-1-1c0-1 0-1-1-2-1-2-1-3-3-4h-4l-2 1v-1c0-1 1-1 1-2h0c1-1 1-2 2-3h0l4-4c1 0 1 0 2-1z" class="P"></path><path d="M284 162h1v3l-1 1-1-2c0-1 0-1 1-2z" class="I"></path><path d="M279 165l2 1h1c1 1 3 2 5 4 0-1 1-1 1-2v3h-1-1c-1 1-1 1-2 1l-1-1c0-1 0-1-1-2-1-2-1-3-3-4z" class="D"></path><path d="M274 163c1-1 1-2 2-2 1-1 1 0 1-1 1-1 2-1 3-2h1s1-1 2-1 2 0 3 1h-3l1 1c-3 0-6 1-7 3 0 1 0 1 1 2v1h-3l-2 1v-1c0-1 1-1 1-2h0z" class="b"></path><path d="M284 159h0 0c-2 1-3 3-4 4 0 1 1 2 1 3l-2-1h-4 3v-1c-1-1-1-1-1-2 1-2 4-3 7-3z" class="T"></path><path d="M315 170h2v1c1 1 2 1 2 3 0 0 1 1 0 2 0 2 0 3-1 4l-3-1c-2 4-7 6-11 7-3 2-6 1-9 1h-2c-3-1-5-3-7-4-3 0-7 1-9-1v-1l1-1 1 1h4v-4c1 0 2 1 3 1h1l1-1 1 1c1 1 2 2 4 3 1 0 3 1 5 1h1c3 1 5 0 8-1h1c1-1 2-2 4-2 2-3 2-5 3-8v-1z" class="S"></path><path d="M315 179l2-2c1 0 1 0 2-1h0c0 2 0 3-1 4l-3-1z" class="W"></path><path d="M283 177c1 0 2 1 3 1h1c-1 2-2 2-4 3v-4z" class="O"></path><path d="M315 170h2v1c-1 1-1 1-1 2v3l-1 2c-1 1-5 3-6 4h0l3-3c2-3 2-5 3-8v-1zm-29 13s1-1 2-1h1c2 2 4 3 7 4 1 0 2 1 3 1h0c2 0 3 0 5-1-3 2-6 1-9 1h-2c-3-1-5-3-7-4z" class="W"></path><path d="M307 181h1c1-1 2-2 4-2l-3 3h-1c-2 1-4 2-6 2-4 1-7-1-9-3 1 0 3 1 5 1h1c3 1 5 0 8-1z" class="N"></path><path d="M320 151v-1l4 1h0c0 1 1 1 1 1 0 1-1 1-2 2l3 2-1 1 1 2h1c3 0 3 1 6 2l-1 2h-1-2l-2-1c-1 0-1 0-2 1s0 2 0 3l1 4c0 2 0 6-1 8s-3 5-4 6v-2c1-1 1-2 1-4l-2 3v-1h-2 0c1-1 1-2 1-4 1-1 0-2 0-2 0-2-1-2-2-3v-1h2v-1c0-1-1-2-1-3l-1-1c0-1-2-3-2-4 1-1 1-2 1-3l-1-2c1-1 3-1 4-2 0-1-1-1-1-2h1l1-1z" class="H"></path><path d="M324 163s0 1-1 1c-1-1-1-1-1-2h1c1-2 5-2 7-2 1 1 1 2 1 3h-2l-2-1c-1 0-1 0-2 1h-1z" class="L"></path><path d="M320 151v-1l4 1h0c0 1 1 1 1 1 0 1-1 1-2 2l3 2-1 1c0-1-1-1-1-1-1 0-3 2-4 2l1 1c-2 1-2 0-3 2v-2c-1 0-1-1-2-1l-1-2c1-1 3-1 4-2 0-1-1-1-1-2h1l1-1z" class="G"></path><path d="M315 156c1-1 3-1 4-2 0 1 0 3 1 4h0l1 1c-2 1-2 0-3 2v-2c-1 0-1-1-2-1l-1-2z" class="M"></path><path d="M320 151v-1l4 1h0c0 1 1 1 1 1 0 1-1 1-2 2-1 0-1 1-2 1-1-1-1-3-1-4z" class="P"></path><path d="M316 158c1 0 1 1 2 1v2h0c1 2 1 4 2 6 0 0 1 1 1 2v-2c0-1 0-2 1-2 0-1 0-1 1-1l1 1v-2h1c-1 1 0 2 0 3l1 4c0 2 0 6-1 8s-3 5-4 6v-2c1-1 1-2 1-4l-2 3v-1h-2 0c1-1 1-2 1-4 1-1 0-2 0-2 0-2-1-2-2-3v-1h2v-1c0-1-1-2-1-3l-1-1c0-1-2-3-2-4 1-1 1-2 1-3z" class="E"></path><path d="M319 170h2c0 1-2 2-2 3v1c0-2-1-2-2-3v-1h2zm6-4l1 4v1h-1c-1 1-1 2-1 4 0-2 0-3-1-5-1-1-1-1-1-2 2-1 2-1 3-2z" class="F"></path><path d="M326 170c0 2 0 6-1 8s-3 5-4 6v-2c1-1 1-2 1-4 1-1 1-2 2-3 0-2 0-3 1-4h1v-1z" class="T"></path><path d="M275 165h4c2 1 2 2 3 4 1 1 1 1 1 2l1 1 1 1s-1 0-1 1h-2 0l1 1v2h0v4h-4l-1-1-1 1v1c2 2 6 1 9 1 2 1 4 3 7 4h2l-1 1 1 1c-3 0-5-1-8-2-3 0-6-2-9-2l-1 2c1 1 3 2 4 3h-1c-1 0-1 1-2 1l-4-3-4-2-1 1c-1-2-1-3-1-5v-2-2l-1-1h-2v-1-2c1-4 3-6 7-8h1l2-1z" class="R"></path><path d="M283 175v2h0v4h-4l1-1c2-1 3-2 3-5z" class="D"></path><path d="M271 179c1-2 2-3 4-4 1 1 2 3 2 4v1h-2c-1 1-1 1-1 2 1 2 2 3 3 5 1 1 3 2 4 3h-1c-1 0-1 1-2 1l-4-3-4-2-1 1c-1-2-1-3-1-5v-2h1l2-1z" class="B"></path><path d="M268 180h1l2-1c0 3 0 5 2 7l1 2-4-2-1 1c-1-2-1-3-1-5v-2z" class="K"></path><path d="M275 165h4c2 1 2 2 3 4 1 1 1 1 1 2l1 1 1 1s-1 0-1 1h-2 0v2h-1 0c-1-1-2-3-3-4-1 0-3 1-4 1-1 1-1 1-1 2-2 1-2 2-2 4l-2 1h-1v-2l-1-1h-2v-1-2c1-4 3-6 7-8h1l2-1z" class="L"></path><path d="M275 165h4c2 1 2 2 3 4 1 1 1 1 1 2l1 1 1 1s-1 0-1 1h-2v-2c0-1 0-1-1-2h0v-1c-1-1-1-1-2-1-3 0-6 0-9 2v2h-2 0v-2c1 0 2-1 2-1l2-3h1l2-1z" class="a"></path><path d="M268 178c1-3 2-5 3-6 2-2 3-3 5-3 3 0 4 1 6 3v2h0v2h-1 0c-1-1-2-3-3-4-1 0-3 1-4 1-1 1-1 1-1 2-2 1-2 2-2 4l-2 1h-1v-2z" class="S"></path><defs><linearGradient id="Cs" x1="297.509" y1="200.291" x2="299.939" y2="180.514" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#Cs)" d="M331 163h1c1 1 2 3 3 4h1c1-1 2-1 3-1l-1 2 1 1 3-3s1 0 1 1c1 1 1 2 2 2v-1h0c1 1 1 2 1 3h0l-3 1h0c0 1 1 2 2 2v1h0c0 1 1 2 0 3l-1 1v2 2c0-1-1-2-1-2h-1l-3 3c-1 3-5 7-7 10-8 7-18 12-29 13h0-12l-6-1-7-1 1-1v-1-1s0-1 1-1c-1 0-3-2-4-2l-3-2 2-2v-1c-1 0-2-1-2-1l-4-6 1-1 4 2 4 3c1 0 1-1 2-1h1c-1-1-3-2-4-3l1-2c3 0 6 2 9 2 3 1 5 2 8 2l-1-1 1-1c3 0 6 1 9-1 4-1 9-3 11-7l3 1h0 2v1l2-3c0 2 0 3-1 4v2c1-1 3-4 4-6s1-6 1-8l-1-4c0-1-1-2 0-3s1-1 2-1l2 1h2z"></path><path d="M320 181l2-3c0 2 0 3-1 4v2c-1 2-2 2-4 3-5 5-10 7-16 9h-3 0v-1c1-1 3-2 4-2h0c6-2 10-5 14-10l2-3h2v1z" class="D"></path><path d="M318 180h2v1l-1 2v-1l-3 1 2-3z" class="H"></path><path d="M320 181l2-3c0 2 0 3-1 4v2c-1 2-2 2-4 3l2-4 1-2z" class="N"></path><defs><linearGradient id="Ct" x1="296.82" y1="185.382" x2="304.103" y2="201.274" xlink:href="#B"><stop offset="0" stop-color="#b9b7b8"></stop><stop offset="1" stop-color="#e7e6e7"></stop></linearGradient></defs><path fill="url(#Ct)" d="M323 188c1 0 2-1 3-1l-1 1v1l-2 2c1 1 1 2 2 2l-1 1-2-2c-2 1-4 4-6 5-3 1-6 3-9 4-3 0-5 0-8 1-1 0-3 0-4 1-3-1-7-1-10-1-2-1-3-1-5-1-1 0-3-2-4-2l-3-2 2-2c4 3 10 5 15 5 7 1 17-1 23-4 4-3 7-5 10-8z"></path><path d="M269 187l1-1 4 2 4 3c1 0 1-1 2-1h1c3 1 6 2 9 2 4 1 8 1 12 1-1 0-3 1-4 2v1h0 3 1c0 1-2 1-3 2-5 1-11 0-15-2h-2c-2-1-3-1-4-1-1-1-2-1-3-1s-2-1-2-1l-4-6z" class="d"></path><path d="M269 187l1-1 4 2 4 3 2 1h-1c-2 0-3-1-5-2h-1 0c0 2 1 2 2 3 2 1 4 1 6 2 1 0 2 1 3 1h-2c-2-1-3-1-4-1-1-1-2-1-3-1s-2-1-2-1l-4-6z" class="C"></path><path d="M281 190c3 1 6 2 9 2 4 1 8 1 12 1-1 0-3 1-4 2v1h0v-1 1h-1c-5 1-13-2-17-4l-2-1c1 0 1-1 2-1h1z" class="M"></path><path d="M278 191c1 0 1-1 2-1 5 3 10 3 14 5 1 1 3 1 4 1h-1c-5 1-13-2-17-4l-2-1z" class="F"></path><path d="M315 179l3 1h0l-2 3c-4 5-8 8-14 10h0c-4 0-8 0-12-1-3 0-6-1-9-2-1-1-3-2-4-3l1-2c3 0 6 2 9 2 3 1 5 2 8 2l-1-1 1-1c3 0 6 1 9-1 4-1 9-3 11-7z" class="K"></path><defs><linearGradient id="Cu" x1="321.487" y1="199.306" x2="290.7" y2="205.459" xlink:href="#B"><stop offset="0" stop-color="#818081"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#Cu)" d="M295 203c1-1 3-1 4-1 3-1 5-1 8-1 3-1 6-3 9-4 2-1 4-4 6-5l2 2h0l2 1 1 1v1c1-1 3-3 4-5l1 2c-8 7-18 12-29 13h0-12l-6-1-7-1 1-1v-1-1s0-1 1-1c2 0 3 0 5 1 3 0 7 0 10 1z"></path><path d="M324 194l2 1 1 1v1c-2 0-4 2-5 2l-1-1h-1v-1c1-1 2-2 4-3z" class="b"></path><path d="M285 206h2c1-1 1-3 3-3h0v2c1 1 1 1 2 1 3 1 7 0 11 1h-12l-6-1z" class="E"></path><path d="M280 201c2 0 3 0 5 1 3 0 7 0 10 1h-1c-1 1-2 3-4 2v-2h0c-2 0-2 2-3 3h-2l-7-1 1-1v-1-1s0-1 1-1z" class="H"></path><path d="M280 201c2 0 3 0 5 1-1 0-2 1-3 1s-1 0-1-1h0l-1-1zm51-38h1c1 1 2 3 3 4h1c1-1 2-1 3-1l-1 2 1 1 3-3s1 0 1 1c1 1 1 2 2 2v-1h0c1 1 1 2 1 3h0l-3 1h0c0 1 1 2 2 2v1h0c0 1 1 2 0 3l-1 1v2 2c0-1-1-2-1-2h-1l-3 3c-1 3-5 7-7 10l-1-2c-1 2-3 4-4 5v-1l-1-1-2-1h0l1-1c-1 0-1-1-2-2l2-2v-1l1-1c-1 0-2 1-3 1l2-2-3 1h0c1-3 2-6 3-8v-1c1-2 1-6 1-8l-1-4c0-1-1-2 0-3s1-1 2-1l2 1h2z" class="G"></path><path d="M334 176l2 2c0 1 0 1 1 2 0 1-1 2 0 3v1s-1 0-2 1c0-1-1-2-1-3 0-2-1-2-2-2l2-4z" class="M"></path><path d="M339 182l-1-1c-1-1-1-2-1-2l-1-1c0-1 0-4 1-5s1-1 2-1l2 2c0-1 0-1 2-2 0 1 1 2 2 2v1h0c0 1 1 2 0 3l-1 1v2 2c0-1-1-2-1-2h-1-1-1v-1c-1 1-1 1-1 2z" class="D"></path><path d="M340 180c-1-1-2-4-1-5h1l4 4v2 2c0-1-1-2-1-2h-1-1-1v-1z" class="C"></path><path d="M341 174c0-1 0-1 2-2 0 1 1 2 2 2v1h0c0 1 1 2 0 3l-1 1-4-4h1v-1z" class="B"></path><path d="M345 178h-1c-1-1-1-1 0-2l1-1h0c0 1 1 2 0 3z" class="M"></path><path d="M332 180c1 0 2 0 2 2 0 1 1 2 1 3 1-1 2-1 2-1l2-2c0-1 0-1 1-2v1h1 1l-3 3c-1 3-5 7-7 10l-1-2c-1 2-3 4-4 5v-1l-1-1-2-1h0l1-1c-1 0-1-1-2-2l2-2c3-2 5-6 7-9z" class="F"></path><path d="M326 195h0c1-1 1-2 1-4 0-1 1-3 2-4 2-2 2-4 4-5 1 2-1 5 0 7h0v1c0 1-1 2-2 2-1 2-3 4-4 5v-1l-1-1z" class="T"></path><path d="M331 163h1c1 1 2 3 3 4h1c1-1 2-1 3-1l-1 2 1 1c-2 2-3 4-5 7l-2 4c-2 3-4 7-7 9v-1l1-1c-1 0-2 1-3 1l2-2-3 1h0c1-3 2-6 3-8v-1c1-2 1-6 1-8l-1-4c0-1-1-2 0-3s1-1 2-1l2 1h2z" class="b"></path><path d="M331 163h1c1 1 2 3 3 4v1h-2c-1-2-2-3-4-5h2z" class="G"></path><path d="M335 167h1c1-1 2-1 3-1l-1 2c-2 1-3 2-4 3 0 1-1 2-2 2l1-5h2v-1z" class="f"></path><path d="M338 168l1 1c-2 2-3 4-5 7l-2 4c-2 3-4 7-7 9v-1l1-1c-1 0-2 1-3 1l2-2c1-1 2-3 3-4 2-3 4-6 4-9 1 0 2-1 2-2 1-1 2-2 4-3z" class="Y"></path><path d="M325 163c1-1 1-1 2-1v1c1 3 4 5 5 8-1 2-2 3-3 5v3c-1 1-1 2-1 3-1 1-2 3-3 4l-3 1h0l3-8v-1c1-2 1-6 1-8l-1-4c0-1-1-2 0-3z" class="W"></path><defs><linearGradient id="Cv" x1="413.838" y1="106.352" x2="389.918" y2="121.671" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#Cv)" d="M348 60h12 31v1c-4 1-7 4-9 7v1c2 1 1 0 2 1l-1 1-1 1c-3 8-2 18 1 26 2 8 7 14 12 20h1c1 3 4 4 6 6 1 2 3 4 5 6 8 7 17 12 26 16 4 2 8 3 11 5l-1 1c1 1 1 1 2 1 2 1 5 2 6 4l1 1-8-3c-1 0-4-1-4 0 0 0 0 1 1 2l1 2-1 1c-3-3-6-6-10-9l-1 1h-1 0l-1-1c-2 0-4 1-6 0l-1-1c-1 0-2 0-2 1h-1-1c0 1-3 1-4 1 0-1 0-1-1-1-1-2-3-2-4-2h-1l-4 1-3 1h0c-2 1-4 2-5 4-1 0-1 0-2-1-1 0-2 1-2 1l-1-1h0-1c-2 1-4 3-5 5h-2l-1-1c-1 0-4 2-5 2l-1 2c-4 2-7 4-10 8-2 2-4 4-5 7l-3 5c0 1-1 2-2 4v3 8 3s-1 0-1 1v2 5c-1 2-1 4-2 6h-1c-1-2-1-5-1-7-1-5 0-9-1-14-1-3-2-9-4-12h-1v-2l1-1c1-1 0-2 0-3h0v-1c-1 0-2-1-2-2h0l3-1h0c0-1 0-2-1-3h0v1c-1 0-1-1-2-2 0-1-1-1-1-1l-3 3-1-1 1-2c-1 0-2 0-3 1h-1c-1-1-2-3-3-4l1-2c-3-1-3-2-6-2h-1l-1-2 1-1-3-2c1-1 2-1 2-2 0 0-1 0-1-1h0l-4-1c0-1 0-2 1-2v-1l-1-1h0 0c-1-1 0-1-1-1h-1c0-1 0-1 1-2-1 0-1-1-2-1 0-1-1-1-2-2-2 0-5-2-7-4-1-1-1-2-1-3l-1-2c-1 0-1 0-2-1h0l-2-2c3-2 6-5 8-9l1-1c1 0 1 0 1-1l4-6c1-1 2-2 3-4l1-1 1-1h1l-1-1c-1-1-1-1-3-1l1-1h0c2-7 4-13 3-20v-5-2s0-1-1-2h-3v-1h0c-2-1-3-1-5 0h0l-1-1 1-2c0-1 1-1 2-2v-3l-4-4h1c4-1 8 0 12 0h24z"></path><path d="M400 136v-2c0-1 1-2 2-3v1 1 1l-2 2z" class="C"></path><path d="M384 109c2 2 3 4 5 5 1 0 1 0 1 1-1 1-2 1-2 2l-1-2c-1-2-2-3-3-5v-1z" class="c"></path><path d="M366 73c3 1 7 1 9 0h1c2 0 2-1 4-2l-1 3h-11l-4 1v-1c1 0 1 0 2-1z" class="B"></path><path d="M390 115l4 6-2-1h0-3v1-1l-1-1v-2c0-1 1-1 2-2z" class="F"></path><path d="M373 81c1 0 2 0 3 1s2 2 2 3l1 3v1h0c-1 1-1 2-1 3l-1-2c-1-2-2-4-4-5h0v-4z" class="Q"></path><path d="M376 82c1 1 2 2 2 3l1 3-1 1c-1-1-2-2-2-3s0-2-1-2c0-1 0-1 1-2z" class="J"></path><path d="M396 134l2-5c0 4 1 8 1 11l1 2h1c2 1 5 1 6 1l3 1c-2 0-4 0-5-1l-1 1h0c-1 0-1 0-1 1-2 0-2-2-4-1-1 1-1 1-2 1h-1-1c-1-3 0-7 0-10l1-1h0z" class="H"></path><path d="M396 134l1-1c1 2 0 4 1 5 0 1-1 1-1 2h0c1 0 1 1 2 2h-2l-1 1 1 2h-1-1c-1-3 0-7 0-10l1-1h0z" class="a"></path><path d="M374 91c1-1 2-1 3-1l1 2 5 12 1 3v2 1c1 2 2 3 3 5h-1c-2-2-3-3-5-3-3-1-3-1-5 0s-2 1-3 2h-2l-1 1c-1-1-1-2-1-3l4-4v-2c1-1 1-4 2-6h-1l1-4v-1l-1-4z" class="O"></path><path d="M374 91c1-1 2-1 3-1l1 2 5 12-2-1c0-1 0-1-1-2v-1c-1-1-1-1-2-1v-1c0-1 1-1 1-2l-2-2c-1 0-1 0-2 1v1-1l-1-4z" class="B"></path><path d="M376 100h1v1 1c1 1 1 2 0 3h1 1c1-1 1-1 2-1h0c1 1 0 1 1 2l2 1v2 1h-1v-2l-1-1c-2-3-2-1-4-2l-1 1c-1 1-1 1-2 1l-2-1c1-1 1-4 2-6h1z" class="Y"></path><path d="M375 100h1c-1 1-1 1-1 2v1c1 1 1 1 1 2l1 1c-1 1-1 1-2 1l-2-1c1-1 1-4 2-6z" class="D"></path><path d="M377 106l1-1c2 1 2-1 4 2l1 1v2h1c1 2 2 3 3 5h-1c-2-2-3-3-5-3-3-1-3-1-5 0s-2 1-3 2h-2l-1 1c-1-1-1-2-1-3l4-4v-2l2 1c1 0 1 0 2-1z" class="a"></path><path d="M373 108c0 1 1 1 2 2h0c0-2 1-2 2-3 1 1 0 2 1 2h0l-1 1c-2 0-4 2-6 4l-1 1c-1-1-1-2-1-3l4-4z" class="D"></path><path d="M378 109c2 0 3 0 5 1h1c1 2 2 3 3 5h-1c-2-2-3-3-5-3-3-1-3-1-5 0s-2 1-3 2h-2c2-2 4-4 6-4l1-1h0z" class="P"></path><defs><linearGradient id="Cw" x1="425.501" y1="142.307" x2="416.736" y2="152.58" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#Cw)" d="M402 133c2 0 4 0 6 1l32 19h0 1c1 0 1 1 2 1l1 1c-1 0-4-1-4 0 0 0 0 1 1 2l1 2-1 1c-3-3-6-6-10-9l-1 1h-1 0l-1-1c-2 0-4 1-6 0l-1-1c-1 0-2 0-2 1h-1-1c0 1-3 1-4 1 0-1 0-1-1-1-1-2-3-2-4-2h-1c-1 0-1-1-2-2h-2 0c0-1 0-1-1 0h-5c-2 0-3 1-5 1 1-1 2-1 3-2 1 0 1-1 0-1h1 1c1 0 1 0 2-1 2-1 2 1 4 1 0-1 0-1 1-1h0l1-1c1 1 3 1 5 1l-3-1c-1 0-4 0-6-1h-1l-1-2c1-1 1-3 1-4l2-2v-1h0z"></path><path d="M407 143h0c1-1 3-1 4-1h0 4v1h-3 0c1 0 2 0 2 1h1 2 0c1 0 2 0 2 1h0c-3-1-6-1-9-1l-3-1z" class="R"></path><path d="M402 133h0c1 1 1 2 1 4 1 1 0 3 0 5 3-1 5-1 8 0-1 0-3 0-4 1h0c-1 0-4 0-6-1h1v-8-1z" class="J"></path><path d="M402 134v8h-1-1l-1-2c1-1 1-3 1-4l2-2z" class="I"></path><path d="M404 144l1-1c1 1 3 1 5 1 3 0 6 0 9 1 4 2 9 4 12 6l-1 1h-1 0l-1-1c-2 0-4 1-6 0l-1-1c-1 0-2 0-2 1h-1c1-1 2-2 2-3-1 0-1-1-2-1-3-1-6-1-10-2 0 0-2 1-2 0-1 0-1-1-2-1z" class="B"></path><path d="M397 145c1 0 1 0 2-1 2-1 2 1 4 1 0-1 0-1 1-1h0c1 0 1 1 2 1 0 1 2 0 2 0 4 1 7 1 10 2 1 0 1 1 2 1 0 1-1 2-2 3h-1c0 1-3 1-4 1 0-1 0-1-1-1-1-2-3-2-4-2h-1c-1 0-1-1-2-2h-2 0c0-1 0-1-1 0h-5c-2 0-3 1-5 1 1-1 2-1 3-2 1 0 1-1 0-1h1 1z" class="Y"></path><path d="M397 145c1 0 1 0 2-1 2-1 2 1 4 1 0-1 0-1 1-1h0l-1 1c-1 1-7 2-8 1 1 0 1-1 0-1h1 1zm8 2c2 0 4 0 6 1h0l1 1v-1h4v1c0 1 0 2 1 2 0 1-3 1-4 1 0-1 0-1-1-1-1-2-3-2-4-2h-1c-1 0-1-1-2-2z" class="B"></path><path d="M412 148h4v1 1h-3l-1-2z" class="J"></path><path d="M356 77c1 1 2 2 3 2s2 0 2-1c2 0 4 1 6 2 1 0 2 1 3 2 1-1 2-1 3-1h0v4h0c2 1 3 3 4 5-1 0-2 0-3 1l1 4v1l-1 4h1c-1 2-1 5-2 6v2l-4 4h-3l-2-1c-2-1-2-3-3-5l-1-3c0-1-1-1-1-2-1-1-1-2-1-3h0c2-2 1-3 1-4v-1c-1-2-1-4-1-6h-1l-1-1 1-1-1-1v-5h-1c1-1 1-1 1-2z" class="B"></path><path d="M370 82c1-1 2-1 3-1h0v4l-3-3z" class="P"></path><path d="M370 87v-2c1 0 1 1 3 1v-1c2 1 3 3 4 5-1 0-2 0-3 1l-1-2c-1-1-2-2-3-2z" class="E"></path><path d="M370 87c1 0 2 1 3 2 0 1 0 1-1 3h-1l-1-1c-1 0-3 1-4 2 0 1-1 2-1 3h-1l-1-1 2-4c1-2 3-3 5-4z" class="S"></path><path d="M373 89l1 2 1 4c-1-1-2-1-3-1l-2 1-1-1-2 2c-1 1-2 4-2 5 0-1-1-2 0-3v-2c0-1 1-2 1-3 1-1 3-2 4-2l1 1h1c1-2 1-2 1-3z" class="P"></path><path d="M357 85c0-2 2-4 3-5 2 0 3 0 5 1 1 0 2 1 3 2v1c-1 0-2 0-2 1v2h-1c-1 0-1 0-1-1h-2-1c-2 2-2 5-2 7-1-2-1-4-1-6h-1l-1-1 1-1z" class="J"></path><path d="M357 85c0-2 2-4 3-5 2 0 3 0 5 1 0 1-1 2-2 2v1l-2-1c-1 0-1 1-2 1l-1 3h-1l-1-1 1-1z" class="C"></path><path d="M359 93c0-2 0-5 2-7 1 2 2 2 2 3s-1 1-1 2c-1 2-1 2 0 4h1 0 0l1 1h1v2c-1 1 0 2 0 3h0c-1 1-1 2-2 2 0 2 0 3 1 5 0 1 1 2 0 3-2-1-2-3-3-5l-1-3c0-1-1-1-1-2-1-1-1-2-1-3h0c2-2 1-3 1-4v-1z" class="K"></path><path d="M359 93c0-2 0-5 2-7 1 2 2 2 2 3s-1 1-1 2c-1 2-1 2 0 4h1 0v5c-2-2-3-4-4-6v-1z" class="N"></path><path d="M365 101c0-1 1-4 2-5l2-2 1 1 2-1c1 0 2 0 3 1v1l-1 4h1c-1 2-1 5-2 6v2l-4 4h-3l-2-1c1-1 0-2 0-3-1-2-1-3-1-5 1 0 1-1 2-2h0z" class="c"></path><path d="M369 98l2 4c0 1 0 1-1 2h-1-1c1-2 1-2 0-4 0-1 0-1 1-2z" class="J"></path><path d="M369 104l1 1v2c-1 1-1 1-2 1l-2 1h0c-1-3 0-4 1-5h1 0 1z" class="M"></path><path d="M365 101c0-1 1-4 2-5l2-2 1 1 2-1c1 0 2 0 3 1v1l-1 4h0c-1 0-1 1-2 0 0 0 0-1-1-1 0-1-1-2-2-2v1c-1 1-1 1-1 2 1 2 1 2 0 4h0l-1-2h-2c-1 2-1 4-1 6-1-2-1-3-1-5 1 0 1-1 2-2h0z" class="Z"></path><path d="M348 60h12 31v1c-4 1-7 4-9 7l-2 3c-2 1-2 2-4 2h-1c-2 1-6 1-9 0-1 1-1 1-2 1v1l4-1c-1 2-4 1-6 1 0 0-1 0-2 1h1 2v1h-1c-1-1-2-1-3-1v1h0c1 0 2 1 2 1 0 1-1 1-2 1s-2-1-3-2c0 1 0 1-1 2v1h-1c-1-1-1-1-1-2h-1l-1 1v-4s0 1-1 1c-1-1-1-1-2-1-1-1-2-1-3-2h0v-1l1-2c0-2 2-6 3-9l2-1h0-3z" class="D"></path><path d="M359 72v1c1 0 1 0 2-1 1-2 3-4 4-7 1-1 2-3 4-4 1 0 5 0 7 1-3 2-5 4-8 7-1 1-3 1-4 3v1c3 0 4-1 7 0-1 1-1 0-2 0h0-3c-1 1-1 1-2 1h-6l1-2z" class="O"></path><path d="M348 60h12c-2 1-5-1-6 0v1c1 1 1 2 2 3s2 2 3 4v4l-1 2h6v1l4-1c-1 2-4 1-6 1 0 0-1 0-2 1h1 2v1h-1c-1-1-2-1-3-1v1h0c1 0 2 1 2 1 0 1-1 1-2 1s-2-1-3-2c0 1 0 1-1 2v1h-1c-1-1-1-1-1-2h-1l-1 1v-4s0 1-1 1c-1-1-1-1-2-1-1-1-2-1-3-2h0v-1l1-2c0-2 2-6 3-9l2-1h0-3z" class="C"></path><path d="M359 68v4l-1 2h6v1c-3 0-7-1-10-1 1 0 2 0 3-1 0-1 0-2-1-3 0-1 1-1 1-2h2z" class="E"></path><path d="M352 70v3l1 1c1-2 1-4 2-5s1-1 2-1c0 1-1 1-1 2 1 1 1 2 1 3-1 1-2 1-3 1h-1v2c1 1 2 1 3 1 0 1 0 1-1 2v1h-1c-1-1-1-1-1-2h-1l-1 1v-4h0c1-1 1-4 1-5z" class="B"></path><path d="M353 76c1 1 2 1 3 1 0 1 0 1-1 2v1h-1v-1c0-1 0-1-1-3z" class="H"></path><path d="M354 61c1 1 1 2 2 3s2 2 3 4h-2c-1 0-1 0-2 1s-1 3-2 5l-1-1v-3l2-9z" class="G"></path><defs><linearGradient id="Cx" x1="345.991" y1="68.971" x2="354.172" y2="62.52" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#a8a6a7"></stop></linearGradient></defs><path fill="url(#Cx)" d="M348 60h12c-2 1-5-1-6 0v1l-2 9c0 1 0 4-1 5h0s0 1-1 1c-1-1-1-1-2-1-1-1-2-1-3-2h0v-1l1-2c0-2 2-6 3-9l2-1h0-3z"></path><path d="M345 73c2-1 2-1 4-1l1-1c2 2 1 2 1 4h0s0 1-1 1c-1-1-1-1-2-1-1-1-2-1-3-2h0z" class="Q"></path><path d="M351 79l1-1h1c0 1 0 1 1 2h1v-1h1v5l1 1-1 1 1 1h1c0 2 0 4 1 6v1c0 1 1 2-1 4h0c0 1 0 2 1 3 0 1 1 1 1 2l1 3c1 2 1 4 3 5l2 1h3c0 1 0 2 1 3l-1 1v1l-2 1c1 1 1 2 1 3h-4-3c-2 0-3 0-4 1l-1 1h-2v-1c-1-1-1 0-1-1-2-1-3-1-5-2h-1l-3-1h-5-1s-1 0-1 1h0l-1-2v-2l1-1h1l2-1v-1c1-1 1-2 1-3l2-5c2-4 3-7 4-11l-1-5 1-3c1 0 2 1 2 1h1l1-1v-4-2z" class="a"></path><path d="M356 91v-5l1 1h1c0 2 0 4 1 6v1c0 1 1 2-1 4h0l-2-7z" class="I"></path><path d="M351 79l1-1h1c0 1 0 1 1 2h1v-1h1v5l1 1-1 1v5-2-2c0-1-1-2-2-3l-1 1v-4h-2v-2z" class="D"></path><path d="M360 109c-1 1-1 4-1 6v1h0l-1-1c-1-3-2-7-1-10 0-2 0-2 1-3l2 1 1 3v3h0l-1-2v1 1z" class="i"></path><path d="M351 85l1-1h0c1 1 1 3 1 4l-1 9c0 5 1 10 1 15 1 4 2 6 1 10-1-1-1 0-1-1l-1-7-1-10h-1c-1 0-1-1-2-2 0-2 1-7 1-8 1-2 1-5 1-7v-1l1-1z" class="j"></path><path d="M361 106c1 2 1 4 3 5l2 1h3c0 1 0 2 1 3l-1 1v1l-2 1c1 1 1 2 1 3h-4-3c-2 0-3 0-4 1l-1 1h-2v-1-1c1-1 0-2 1-3h1c-1 1-1 1-1 2l1-1 1-1s1 0 1-1 2 0 3-1l-1-1c0-1-1-2 0-3v-3-1-1l1 2h0v-3z" class="L"></path><path d="M366 112h3c0 1 0 2 1 3l-1 1c-1-1-2-2-4-3l1-1z" class="a"></path><path d="M354 121c2-1 4-2 5-3 2-1 6-1 8 0 1 1 1 2 1 3h-4-3c-2 0-3 0-4 1l-1 1h-2v-1-1z" class="C"></path><path d="M346 88l1-3c1 0 2 1 2 1h1v1c0 2 0 5-1 7 0 1-1 6-1 8 1 1 1 2 2 2h1l1 10 1 7c-2-1-3-1-5-2h-1l-3-1h-5-1s-1 0-1 1h0l-1-2v-2l1-1h1l2-1v-1c1-1 1-2 1-3l2-5c2-4 3-7 4-11l-1-5z" class="Y"></path><path d="M346 88l1-3c1 0 2 1 2 1h1v1c-1 1-2 1-3 1v5l-1-5z" class="D"></path><path d="M344 115h2v1h-1c0 1 0 1 1 2 0 0 1 0 1 1l-3-1c-1 0-1 0-1-1-1 0-1 0 0-1l1-1z" class="L"></path><path d="M344 110c1 1 1 0 1 1s0 3-1 4l-1 1v-1l-1-2c0-1 1-2 2-3z" class="V"></path><path d="M350 104h1l1 10-4-1h0l1-1c1 0 1 0 2-1v-1-1l-1-2v-3z" class="i"></path><path d="M341 109h3v1c-1 1-2 2-2 3l1 2v1c-1 1-1 1 0 1 0 1 0 1 1 1h-5-1s-1 0-1 1h0l-1-2v-2l1-1h1l2-1v-1c1-1 1-2 1-3z" class="g"></path><path d="M336 117h1c2 0 3-2 5-4l1 2v1c-1 1-1 1 0 1 0 1 0 1 1 1h-5-1s-1 0-1 1h0l-1-2z" class="B"></path><path d="M324 60h24 3 0l-2 1c-1 3-3 7-3 9l-1 2v1h0c1 1 2 1 3 2 1 0 1 0 2 1 1 0 1-1 1-1v4 2 4l-1 1h-1s-1-1-2-1l-1 3 1 5c-1 4-2 7-4 11l-2 5c0 1 0 2-1 3v1l-2 1h-1l-1 1c-3-2-3-4-7-5h0c-4 0-6 0-9 2l-2 1c-1 0-1-1-2-2 1-1 2-2 3-4l1-1 1-1h1l-1-1c-1-1-1-1-3-1l1-1h0c2-7 4-13 3-20v-5-2s0-1-1-2h-3v-1h0c-2-1-3-1-5 0h0l-1-1 1-2c0-1 1-1 2-2v-3l-4-4h1c4-1 8 0 12 0z" class="D"></path><path d="M324 105h0c0 1-1 1-2 1v2h1c-1 1-2 1-3 1v-3l1-1h2 1z" class="H"></path><path d="M332 109l2-1h0 1c1 2 2 3 3 5v1h-1l-5-5z" class="T"></path><path d="M331 107v1c0-1-1-1-1-1-1 0-2-1-2-2s0-1 1-2c-1 0-2-1-2-2l1-1h2v1c0 2 1 4 1 6z" class="i"></path><path d="M319 107l1-1v3c1 0 2 0 3-1 1 0 3-1 3 0 1 0 2 1 3 2-4 0-6 0-9 2l-2 1c-1 0-1-1-2-2 1-1 2-2 3-4z" class="M"></path><path d="M316 111c1-1 2-2 3-4 1 2 1 2 1 4v1l-2 1c-1 0-1-1-2-2z" class="T"></path><defs><linearGradient id="Cy" x1="329.887" y1="89.642" x2="337.289" y2="104.988" xlink:href="#B"><stop offset="0" stop-color="#2b2a2b"></stop><stop offset="1" stop-color="#5a5958"></stop></linearGradient></defs><path fill="url(#Cy)" d="M334 87c1 0 2 1 3 1-1 2-1 2-3 3-1 1-1 5-2 6 0 2 1 3 1 4v1 1l1 2v3l-2 1-1-2c0-2-1-4-1-6 0-3 0-7 1-10v-2c1-1 2-1 3-2z"></path><path d="M337 88l2 2c0 1 1 2 1 3h1v2 2h0l-3 2h0l-1 2c-1-1-1-1-2-1 0 1 0 2-1 3v2l-1-2v-1-1c0-1-1-2-1-4 1-1 1-5 2-6 2-1 2-1 3-3z" class="P"></path><path d="M337 88l2 2c0 1 1 2 1 3h1c-1 1-1 1-2 1s-2 1-3 0h0l1-1c0-1-1-1-1-2h-2c2-1 2-1 3-3z" class="C"></path><path d="M339 90c0 1 1 2 1 3h1c-1 1-1 1-2 1s-2 1-3 0h0l1-1v1l1-1 1-3z" class="K"></path><path d="M341 93v2 2h0l-3 2h0l-1 2c-1-1-1-1-2-1 0 1 0 2-1 3v-1c1-2 1-4 1-6l1-1c1 0 2 0 3-1 1 0 1 0 2-1z" class="X"></path><path d="M341 93v2 2h0c-2 0-3 1-4 1l-1-2h-1l1-1c1 0 2 0 3-1 1 0 1 0 2-1z" class="J"></path><path d="M338 99c2 1 3 1 4 1v2l1 2-2 5c0 1 0 2-1 3v1l-2 1h0v-1c-1-2-2-3-3-5h-1 0v-3-2c1-1 1-2 1-3 1 0 1 0 2 1l1-2z" class="F"></path><path d="M338 109h1c1 1 1 2 1 3v1l-2-2v-2z" class="N"></path><path d="M335 105c1-1 1-2 2-2h3l-1 1v2c1 1 2 0 1 1s-2 1-2 2v2l2 2-2 1h0v-1c-1-2-2-3-3-5h0v-3z" class="H"></path><path d="M335 105c1-1 1-2 2-2h3l-1 1c0 1-1 2-1 3h-2c0-1-1-1-1-2z" class="O"></path><defs><linearGradient id="Cz" x1="340.109" y1="79.439" x2="344.478" y2="101.332" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#Cz)" d="M338 79c1-1 2-1 2 0 2 0 2 0 3 1 2 2 3 5 3 8l1 5c-1 4-2 7-4 11l-1-2v-2c-1 0-2 0-4-1h0l3-2h0v-2-2h-1c0-1-1-2-1-3l-2-2c-1 0-2-1-3-1s-1-1-2-1c0-1 0-1-1-2l1-1v-1c2-1 4-3 6-3z"></path><path d="M341 95h2c0 2 0 3-1 4v1c-1 0-2 0-4-1h0l3-2h0v-2z" class="c"></path><path d="M338 79c1-1 2-1 2 0-1 1-3 3-4 5 2 1 4 2 6 2 2 2 1 6 1 9h-2v-2h-1c0-1-1-2-1-3l-2-2c-1 0-2-1-3-1s-1-1-2-1c0-1 0-1-1-2l1-1v-1c2-1 4-3 6-3z" class="H"></path><path d="M332 86h8v4c1 1 2 0 2 1s0 1-1 1l-1 1c0-1-1-2-1-3l-2-2c-1 0-2-1-3-1s-1-1-2-1z" class="O"></path><defs><linearGradient id="DA" x1="332.068" y1="73.742" x2="325.501" y2="105.013" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#DA)" d="M311 60h1c1 0 2 1 3 1l7 8c1 1 2 2 3 4v1c1 1 2 1 3 1h7c3 0 7 1 9 0 1 0 1-1 1-2 1 1 2 1 3 2 1 0 1 0 2 1 1 0 1-1 1-1v4 2 4l-1 1h-1s-1-1-2-1l-1 3c0-3-1-6-3-8-1-1-1-1-3-1 0-1-1-1-2 0-2 0-4 2-6 3v1l-1 1h-1c-2 1-2 3-2 5l-3 5-1 6c0 1-1 3 0 5h-1-2 1l-1-1c-1-1-1-1-3-1l1-1h0c2-7 4-13 3-20v-5-2s0-1-1-2h-3v-1h0c-2-1-3-1-5 0h0l-1-1 1-2c0-1 1-1 2-2v-3l-4-4z"></path><defs><linearGradient id="DB" x1="320.491" y1="72.753" x2="314.785" y2="66.275" xlink:href="#B"><stop offset="0" stop-color="gray"></stop><stop offset="1" stop-color="#9a9a9a"></stop></linearGradient></defs><path fill="url(#DB)" d="M315 64c2 2 6 5 7 9v4-2s0-1-1-2h-3v-1h0c-2-1-3-1-5 0h0l-1-1 1-2c0-1 1-1 2-2v-3z"></path><path d="M315 67c1 2 2 3 3 5h0 0c-2-1-3-1-5 0h0l-1-1 1-2c0-1 1-1 2-2z" class="Y"></path><path d="M324 100v-6h0v-1-1c0-2 1-5 1-7v-5l1-3c3 0 7 1 11 0h0l2 1h-1v1c-2 0-4 2-6 3v1l-1 1h-1c-2 1-2 3-2 5l-3 5-1 6z" class="I"></path><path d="M325 94h0v-1h0c0-1 0-4 1-5 0-1 1-1 1-2l1-1v-1l1-1 1-1s1 0 2-1h0c1 0 2-1 2-1v-2h3 0 1v1c-2 0-4 2-6 3v1l-1 1h-1c-2 1-2 3-2 5l-3 5z" class="K"></path><defs><linearGradient id="DC" x1="343.436" y1="77.759" x2="343.593" y2="84.151" xlink:href="#B"><stop offset="0" stop-color="#979697"></stop><stop offset="1" stop-color="#b2b3b2"></stop></linearGradient></defs><path fill="url(#DC)" d="M345 73c1 1 2 1 3 2 1 0 1 0 2 1 1 0 1-1 1-1v4 2 4l-1 1h-1s-1-1-2-1l-1 3c0-3-1-6-3-8-1-1-1-1-3-1 0-1-1-1-2 0v-1h1l-2-1c1-1 2 0 3 0 0-1 0-1 1-1h0c-2 0-4-1-6-1 3 0 7 1 9 0 1 0 1-1 1-2z"></path><path d="M345 78l2-1c1 1 1 2 2 3l1 1h1v-2 2 4l-1 1h-1c0-2-3-7-4-8z" class="b"></path><path d="M345 73c1 1 2 1 3 2 1 0 1 0 2 1 1 0 1-1 1-1v4 2h-1l-1-1c-1-1-1-2-2-3l-2 1c-2-1-4-1-6 0l-2-1c1-1 2 0 3 0 0-1 0-1 1-1h0c-2 0-4-1-6-1 3 0 7 1 9 0 1 0 1-1 1-2z" class="d"></path><path d="M350 76c1 0 1-1 1-1v4 2h-1l-1-1v-1-1c0-1 1-2 1-2z" class="e"></path><defs><linearGradient id="DD" x1="331.351" y1="72.444" x2="333.104" y2="61.022" xlink:href="#B"><stop offset="0" stop-color="#807e7f"></stop><stop offset="1" stop-color="#b3b1b2"></stop></linearGradient></defs><path fill="url(#DD)" d="M324 60h24 3 0l-2 1c-1 3-3 7-3 9l-1 2v1h0c0 1 0 2-1 2-2 1-6 0-9 0h-7c-1 0-2 0-3-1v-1c-1-2-2-3-3-4l-7-8c-1 0-2-1-3-1 4-1 8 0 12 0z"></path><path d="M332 70h3c2 0 3 0 4-1v-1c1 0 1 1 2 1-1 1-2 2-2 3 2 0 5-1 7-2l-1 2c0 1-1 1-2 2-1 0-3 0-5-1v-1h0l-1-1-1 1h0c-2 1-3 1-5 1-1-1-1-2-1-2-1-1 0-1 0-2h2 0l-1 1h1z" class="E"></path><path d="M312 60c4-1 8 0 12 0h0-4c4 3 9 1 14 1l-6 5h-1l-1-1c-3-2-7-4-11-4-1 0-2-1-3-1z" class="D"></path><path d="M324 60h24 3 0l-2 1h-5-10c-5 0-10 2-14-1h4 0z" class="Y"></path><path d="M344 61h5c-1 3-3 7-3 9-2 1-5 2-7 2 0-1 1-2 2-3-1 0-1-1-2-1v1c-1 1-2 1-4 1h-3-1l1-1c1 0 2-3 3-4 1 0 1 0 1-1h1l2-2h3 0l2-1z" class="O"></path><path d="M332 70l1-1h1 1l1-1c1 1 1 1 2 0 0-1 2-2 3-3 1-2 3-3 5-4-2 2-3 5-5 7v1c-1 0-1-1-2-1v1c-1 1-2 1-4 1h-3zm-12 42c3-2 5-2 9-2h0c4 1 4 3 7 5v2l1 2h0c0-1 1-1 1-1h1 5l3 1h1c2 1 3 1 5 2 0 1 0 0 1 1v1h2l1-1c1-1 2-1 4-1h3 4c0-1 0-2-1-3l2-1v-1l1-1 1-1h2c1-1 1-1 3-2s2-1 5 0c2 0 3 1 5 3h1l1 2v2l1 1v1-1h3 0l2 1 5 4c0 1 0 1-1 2l-3 5-1 1 2 1-1 1c0 3-1 7 0 10 1 0 1 1 0 1-1 1-2 1-3 2 2 0 3-1 5-1h5c1-1 1-1 1 0h0 2c1 1 1 2 2 2l-4 1-3 1h0c-2 1-4 2-5 4-1 0-1 0-2-1-1 0-2 1-2 1l-1-1h0-1c-2 1-4 3-5 5h-2l-1-1c-1 0-4 2-5 2l-1 2c-4 2-7 4-10 8-2 2-4 4-5 7l-3 5c0 1-1 2-2 4v3 8 3s-1 0-1 1v2 5c-1 2-1 4-2 6h-1c-1-2-1-5-1-7-1-5 0-9-1-14-1-3-2-9-4-12h-1v-2l1-1c1-1 0-2 0-3h0v-1c-1 0-2-1-2-2h0l3-1h0c0-1 0-2-1-3h0v1c-1 0-1-1-2-2 0-1-1-1-1-1l-3 3-1-1 1-2c-1 0-2 0-3 1h-1c-1-1-2-3-3-4l1-2c-3-1-3-2-6-2h-1l-1-2 1-1-3-2c1-1 2-1 2-2 0 0-1 0-1-1h0l-4-1c0-1 0-2 1-2v-1l-1-1h0 0c-1-1 0-1-1-1h-1c0-1 0-1 1-2-1 0-1-1-2-1 0-1-1-1-2-2-2 0-5-2-7-4-1-1-1-2-1-3l-1-2c-1 0-1 0-2-1h0l-2-2c3-2 6-5 8-9l1-1c1 0 1 0 1-1l4-6c1 1 1 2 2 2l2-1z" class="B"></path><path d="M388 139c0-1-1-2-1-2l-1-1v-1h1 2v-1c1 1 1 1 3 2l-4 3z" class="H"></path><path d="M394 129c0-2 1-3 3-3 1 0 1 0 1 1l-3 5-1 1-2 3c-2-1-2-1-3-2 0-1 1-1 1-1h0c1-2 2-3 4-4z" class="T"></path><path d="M397 126c1 0 1 0 1 1l-3 5-1-1v-1l2-2h0c1-1 1-1 1-2z" class="F"></path><path d="M392 120l2 1 5 4c0 1 0 1-1 2 0-1 0-1-1-1-2 0-3 1-3 3l-2-2h0c0-1-1-2-1-4h0c0-1 1-1 1-2v-1z" class="U"></path><path d="M367 132l2-2 1 3c1 2 3 4 6 6 3 1 6 2 9 1l3-1 4-3 2-3 2 1-1 1v1c-1 0-1 1-2 1-1 2-5 5-7 5l-1 1h0c-3 0-5 0-7-1-3 0-5-1-7-3l-1-1c-1 0-2 2-3 3-1 0-2 1-3 2v1h0c-1 1-1 2-1 3s0 1-1 1v-1-2 1h-1c1-1 1-3 2-4 1-2 2-6 4-8h1v-1l-1-1z" class="S"></path><defs><linearGradient id="DE" x1="386.033" y1="139.337" x2="386.366" y2="151.081" xlink:href="#B"><stop offset="0" stop-color="#525051"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#DE)" d="M378 142c2 1 4 1 7 1h0l1-1c2 0 6-3 7-5 1 0 1-1 2-1v-1c0 3-1 7 0 10 1 0 1 1 0 1-1 1-2 1-3 2-2 1-4 1-6 3l-1-1v-2l-1-1-1 1c1 1 1 3 1 4l-1 1h0l-3-1h-2c0-1-1-4 0-5l1-1h0 0v-2h0l-1-2z"></path><path d="M382 149h-1-1c0-1 0-2 1-2h1 0l1 1c1 1 1 3 1 4l-1 1h0l-3-1c1-1 1-2 2-3z" class="e"></path><path d="M382 149l1 2v2h0l-3-1c1-1 1-2 2-3z" class="P"></path><path d="M392 148c2 0 3-1 5-1h5c1-1 1-1 1 0h0 2c1 1 1 2 2 2l-4 1-3 1h0c-2 1-4 2-5 4-1 0-1 0-2-1-1 0-2 1-2 1l-1-1h0-1c-2 1-4 3-5 5h-2l-1-1c-1 0-4 2-5 2s-2 0-3 1c-2 1-4 2-6 4h-1v-1l1-2c1 0 3-2 3-2 1 0 2-1 3-1 3-2 7-3 10-6h0l1-1c0-1 0-3-1-4l1-1 1 1v2l1 1c2-2 4-2 6-3z" class="a"></path><path d="M403 147h2c1 1 1 2 2 2l-4 1-3 1h0c-2 1-4 2-5 4-1 0-1 0-2-1-1 0-2 1-2 1l-1-1h0-1c1-2 2-3 4-3 4-2 7-3 10-4z" class="H"></path><path d="M390 154c1-2 2-2 4-3 1 0 2-1 3-1s2 0 3 1c-2 1-4 2-5 4-1 0-1 0-2-1-1 0-2 1-2 1l-1-1z" class="D"></path><path d="M367 141c1-1 2-3 3-3l1 1c2 2 4 3 7 3l1 2h0v2h0 0l-1 1c-1 1 0 4 0 5h2l3 1c-3 3-7 4-10 6-1 0-2 1-3 1h-1-1 0-1c-1-1 0-1-1-1h-2v-3-4s0-1-1-1l1-2-1-1c0-1 1-3 1-4v-1c1-1 2-2 3-2z" class="c"></path><path d="M367 141c1-1 2-3 3-3l1 1c-1 1-2 2-2 3l-1 1c-1 2-2 4-2 6v2c-1 2-1 4-2 5v-4s0-1-1-1l1-2-1-1c0-1 1-3 1-4v-1c1-1 2-2 3-2z" class="e"></path><path d="M364 144v-1c1-1 2-2 3-2 0 2-2 5-3 7v1l-1-1c0-1 1-3 1-4z" class="W"></path><path d="M376 112c2-1 2-1 5 0 2 0 3 1 5 3h1l1 2v2l1 1v1-1h3 0v1c0 1-1 1-1 2-1 1-1 1-1 2 0 3 0 5-2 7-2 1-4 3-6 3h-1 0-2c-2 1-2 2-3 3v1c-3-2-5-4-6-6l-1-3-2 2 1 1-3-1v-1c-1 1-2 1-2 1h-2l-2 1h-1v-4-1c-1-1-1-1-3 0 1-3 4-5 6-7h3 4c0-1 0-2-1-3l2-1v-1l1-1 1-1h2c1-1 1-1 3-2z" class="H"></path><path d="M373 114c1-1 1-1 3-2 0 1 1 2 2 2h2c-2 0-3 1-5 1l-1 1h0c-1 0-1-1-1-2z" class="C"></path><path d="M370 115l1-1h2c0 1 0 2 1 2l-4 4c-1-1-1-2-1-3v-1l1-1z" class="K"></path><path d="M372 121c1-2 3-4 5-4 1 0 2-1 3 0s0 1 0 2-1 2-2 3l-6 5h0v-1-2h0c0-1 1-1 1-2l-1-1z" class="I"></path><path d="M376 119l1-1v1c0 2-1 2-2 3h-1v-1l2-2z" class="d"></path><path d="M369 127c1-1 1-2 2-3v-2l1-1 1 1c0 1-1 1-1 2h0v2 1c0 1 0 2 1 3v2c2 2 3 3 6 3h0c-2 1-2 2-3 3v1c-3-2-5-4-6-6l-1-3c-1-1-1-2-1-4l1 1z" class="E"></path><path d="M368 126l1 1h1c0 1-1 2 0 3 0 1 1 1 1 1l-1 2-1-3c-1-1-1-2-1-4z" class="N"></path><path d="M376 112c2-1 2-1 5 0 2 0 3 1 5 3-1 1-1 1-1 2h0c1 1 0 2 0 3l-3 6c-1 0-2 1-2 1-2 2-4 3-5 5h-2v-2c-1-1-1-2-1-3h0l6-5c1 0 1 1 2 2h0c2-2 3-4 3-7 0-1-1-1-1-2l-2-1h-2c-1 0-2-1-2-2z" class="d"></path><path d="M378 122c1 0 1 1 2 2l-7 6c-1-1-1-2-1-3h0l6-5z" class="B"></path><path d="M368 121c1-1 2-1 2 0 0 0 1 0 1 1l-1 1c-1 0-2-1-4 0 1 1 2 2 2 3 0 2 0 3 1 4l-2 2 1 1-3-1v-1c-1 1-2 1-2 1h-2l-2 1h-1v-4-1c-1-1-1-1-3 0 1-3 4-5 6-7h3 4z" class="d"></path><path d="M358 129c1 1 1 1 2 1h2c0 1-1 1-1 2l-2 1h-1v-4h0z" class="C"></path><path d="M364 126c2 2 1 4 1 5-1 1-2 1-2 1h-2c0-1 1-1 1-2 1-2 1-3 2-4z" class="E"></path><path d="M366 123h0c1 1 2 2 2 3 0 2 0 3 1 4l-2 2 1 1-3-1v-1c0-1 1-3-1-5h1c1-1 1-2 1-3z" class="C"></path><path d="M365 126c1 2 2 4 2 6l1 1-3-1v-1c0-1 1-3-1-5h1z" class="N"></path><path d="M368 121c1-1 2-1 2 0 0 0 1 0 1 1l-1 1c-1 0-2-1-4 0h0l-1-1c-3 1-5 4-7 7h0v-1c-1-1-1-1-3 0 1-3 4-5 6-7h3 4z" class="O"></path><defs><linearGradient id="DF" x1="380.052" y1="118.665" x2="386.857" y2="130.493" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#DF)" d="M386 115h1l1 2v2l1 1v1-1h3 0v1c0 1-1 1-1 2-1 1-1 1-1 2 0 3 0 5-2 7-2 1-4 3-6 3h-1 0-2 0c-3 0-4-1-6-3h2c1-2 3-3 5-5 0 0 1-1 2-1l3-6c0-1 1-2 0-3h0c0-1 0-1 1-2z"></path><path d="M389 120l-1 1h0c-1 0-1 0-2-1h1l1-1 1 1z" class="W"></path><path d="M389 121v-1h3 0v1c0 1-1 1-1 2-1 1-1 1-1 2l-1-4z" class="M"></path><path d="M375 132s1 0 1-1h3l2-2h1 0c-1 1-2 2-2 3 1 1 1 2 1 3h-2 0c-3 0-4-1-6-3h2z" class="P"></path><path d="M382 129l2-1c1 0 3 3 4 4-2 1-4 3-6 3h-1 0c0-1 0-2-1-3 0-1 1-2 2-3z" class="J"></path><defs><linearGradient id="DG" x1="362.529" y1="164.99" x2="342.016" y2="158.36" xlink:href="#B"><stop offset="0" stop-color="#dbd6da"></stop><stop offset="1" stop-color="#f1f4f2"></stop></linearGradient></defs><path fill="url(#DG)" d="M350 133v-5h1 1v-3c1-1 1-1 2-1v2h0c1 1 1 2 1 2 2-1 2-1 3 0v1 4h1l2-1h2s1 0 2-1v1l3 1v1h-1c-2 2-3 6-4 8-1 1-1 3-2 4h1v-1 2 1c1 0 1 0 1-1s0-2 1-3h0c0 1-1 3-1 4l1 1-1 2c1 0 1 1 1 1v4 3h2c1 0 0 0 1 1h1 0 1 1s-2 2-3 2l-1 2v1h1c2-2 4-3 6-4 1-1 2-1 3-1l-1 2c-4 2-7 4-10 8-2 2-4 4-5 7l-3 5c0 1-1 2-2 4v3 8 3s-1 0-1 1v2 5c-1 2-1 4-2 6h-1c-1-2-1-5-1-7-1-5 0-9-1-14-1-3-2-9-4-12h-1v-2l1-1c1-1 0-2 0-3h0v-1c-1 0-2-1-2-2h0l3-1h0c0-1 0-2-1-3l1-1c1 0 2 0 3 1l-1-3 1-7c-1-3-1-5-2-8v-3l-2-2-2-1h1 2v-1l-1-3 1-1 1 1h2c0-1 1-2 0-3h0c0-1 0-2-1-2v-1-1h2 0z"></path><path d="M354 126c1 1 1 2 1 2 2-1 2-1 3 0v1 4c0 1 0 1-1 2-1 3-1 6-1 9s-1 5 0 8v1c0 1 0 1-1 2v3h-1 0l1 6c0 2 0 4-1 6 0-9-1-18 0-26v-9c0-2 1-4 0-6v-3z" class="D"></path><path d="M350 133v-5h1 1v-3c1-1 1-1 2-1v2l-1-1c0 1 0 1-1 1v8c0 2-1 5-1 7v20c0 3 1 6 0 8v-4h0v4l-1 1v1-3c-1-1-1-2-1-3v-7c-1-3-1-5-2-8v-3l-2-2-2-1h1 2v-1l-1-3 1-1 1 1h2c0-1 1-2 0-3h0c0-1 0-2-1-2v-1-1h2 0z" class="a"></path><path d="M361 132h2s1 0 2-1v1l3 1v1h-1c-2 2-3 6-4 8-1 1-1 3-2 4-1 2-1 5-2 7l-1 2v1c-1 1-1 0-1 1 0 0 0 1-1 1 0 2-1 4 0 6h-1l-1-6h0 1v-3c1-1 1-1 1-2v-1c-1-3 0-5 0-8s0-6 1-9c1-1 1-1 1-2h1l2-1z" class="G"></path><path d="M361 132h2s1 0 2-1v1 1c-1 0-1 0-2 1h0 0v-1h-1v1c-1 2-1 2-2 3-1-1-1-3-1-4h0l2-1z" class="H"></path><path d="M365 132l3 1v1h-1c-2 2-3 6-4 8-1 1-1 3-2 4-1 2-1 5-2 7-1-2 0-4 0-6 1-2 0-4 1-5h0c1-2 2-4 3-5v-3h0c1-1 1-1 2-1v-1z" class="F"></path><path d="M349 158v7c0 1 0 2 1 3v3-1l1-1v-4h0v4l1 28v1-2-1-1h0c0-3 1-6 1-8v6 7h-2v-6c-1 1 0 2 0 4l-2-4c-1-3-2-9-4-12h-1v-2l1-1c1-1 0-2 0-3h0v-1c-1 0-2-1-2-2h0l3-1h0c0-1 0-2-1-3l1-1c1 0 2 0 3 1l-1-3 1-7z" class="L"></path><path d="M345 168l1-1c1 0 2 0 3 1v6l-2 1v2l-2-2h0v-1c-1 0-2-1-2-2h0l3-1h0c0-1 0-2-1-3z" class="K"></path><path d="M343 172h0l3-1h0c1 1 1 3 3 3l-2 1v2l-2-2h0v-1c-1 0-2-1-2-2z" class="D"></path><path d="M345 175l2 2s0 1 1 1l1 1v3h0l2 11c-1 1 0 2 0 4l-2-4c-1-3-2-9-4-12h-1v-2l1-1c1-1 0-2 0-3z" class="B"></path><path d="M345 175l2 2s0 1 1 1l1 1v3h0 0v-2c-1 0-1 0-2-1 0 1-1 1-1 2h-1-1v-2l1-1c1-1 0-2 0-3z" class="G"></path><path d="M355 164h1c-1-2 0-4 0-6 1 0 1-1 1-1 0-1 0 0 1-1v-1 8c-1 4-2 7-2 11v2h1v1c1 0 2-1 3 0l-3 5c0 1-1 2-2 4v3 8 3s-1 0-1 1v2 5c-1 2-1 4-2 6h-1c-1-2-1-5-1-7-1-5 0-9-1-14l2 4c0-2-1-3 0-4v6h2v-7-6c0-5 0-11 1-16h0c1-2 1-4 1-6z" class="E"></path><path d="M351 193v6h2c0 3 0 6-1 9-1-2-2-7-1-9v-2h0c0-2-1-3 0-4z" class="G"></path><path d="M354 170c0 1 0 2 1 3v5l-1 1h1 1c0 1 0 1-1 2v2 1c-1 1-1 2 0 2l-1 1c-1 2 0 3-1 5v-6c0-5 0-11 1-16z" class="B"></path><defs><linearGradient id="DH" x1="358.063" y1="158.713" x2="365.612" y2="160.212" xlink:href="#B"><stop offset="0" stop-color="#333232"></stop><stop offset="1" stop-color="#5a595a"></stop></linearGradient></defs><path fill="url(#DH)" d="M361 146h1v-1 2 1c1 0 1 0 1-1s0-2 1-3h0c0 1-1 3-1 4l1 1-1 2c1 0 1 1 1 1v4 3h2c1 0 0 0 1 1h1 0 1 1s-2 2-3 2l-1 2v1h1c2-2 4-3 6-4 1-1 2-1 3-1l-1 2c-4 2-7 4-10 8-2 2-4 4-5 7-1-1-2 0-3 0v-1h-1v-2c0-4 1-7 2-11v-8l1-2c1-2 1-5 2-7z"></path><path d="M357 176l3-9c0 2 1 2 2 3 1 0 2-1 3 0-2 2-4 4-5 7-1-1-2 0-3 0v-1z" class="B"></path><path d="M367 162l-1 2v1h1c2-2 4-3 6-4 1-1 2-1 3-1l-1 2c-4 2-7 4-10 8-1-1-2 0-3 0-1-1-2-1-2-3v-3l1-1 1 1c2 0 4-1 5-2z" class="D"></path><path d="M320 112c3-2 5-2 9-2h0c4 1 4 3 7 5v2l1 2h0c0-1 1-1 1-1h1 5l3 1h1c2 1 3 1 5 2 0 1 0 0 1 1v1h2l1-1c1-1 2-1 4-1-2 2-5 4-6 7 0 0 0-1-1-2h0v-2c-1 0-1 0-2 1v3h-1-1v5h0-2v1 1c1 0 1 1 1 2h0c1 1 0 2 0 3h-2l-1-1-1 1 1 3v1h-2-1l2 1 2 2v3c1 3 1 5 2 8l-1 7 1 3c-1-1-2-1-3-1l-1 1h0v1c-1 0-1-1-2-2 0-1-1-1-1-1l-3 3-1-1 1-2c-1 0-2 0-3 1h-1c-1-1-2-3-3-4l1-2c-3-1-3-2-6-2h-1l-1-2 1-1-3-2c1-1 2-1 2-2 0 0-1 0-1-1h0l-4-1c0-1 0-2 1-2v-1l-1-1h0 0c-1-1 0-1-1-1h-1c0-1 0-1 1-2-1 0-1-1-2-1 0-1-1-1-2-2-2 0-5-2-7-4-1-1-1-2-1-3l-1-2c-1 0-1 0-2-1h0l-2-2c3-2 6-5 8-9l1-1c1 0 1 0 1-1l4-6c1 1 1 2 2 2l2-1z" class="Q"></path><path d="M307 130c1 0 1 1 2 1v2h-2l-1-2 1-1z" class="K"></path><path d="M307 130h0v-2l1-1c1-2 2-3 3-4h1v1c0 2 1 2 2 3h3v1c-2 1-4 3-4 4-1 0-3-2-4-2v1c-1 0-1-1-2-1z" class="F"></path><path d="M337 119c0-1 1-1 1-1h1 5l3 1h1c2 1 3 1 5 2 0 1 0 0 1 1v1h2l-1 1c-1 0-2 0-3-1h-1 0l-1 1c-1-1-2-1-3-2h-1c-1-1-1-1-3-1h-7c0-1 1-2 1-2z" class="S"></path><path d="M348 119c2 1 3 1 5 2 0 1 0 0 1 1v1h2l-1 1c-1 0-2 0-3-1h-1 0v-2c-1-1-1 0-2 0 0-1-1-1-1-2z" class="I"></path><path d="M320 112c3-2 5-2 9-2h0v1c-1 1-3 2-5 2h-2c-1 1-2 2-4 3-2 2-3 5-3 8v1h-1c-1 0-1 0-1-1 0-3 1-5 2-7v-1l3-3 2-1z" class="d"></path><path d="M325 115c1 0 1 0 2-1 1 0 3 1 4 1 2 2 4 4 4 7l1 2v1h-2l-1 1v1l-1-1v2c-1 0-1-1-2-1h0v2l-1-1-2 1c-2-2-4-4-6-7-1-1-2-2-2-3s0-1 1-2l1 1c1-2 2-2 4-3z" class="S"></path><path d="M331 119c1 1 1 2 1 3v2l1 1-1 1v2c-1 0-1-1-2-1h0v2l-1-1-1-1c1 0 2-1 2-2l-1-1-2-2c0-1-1-1-1-1s2-1 2 0c1 0 1 2 3 2 1-1 0-3 0-4z" class="N"></path><path d="M322 119c1 1 1 2 3 1h0l1 1s1 0 1 1l2 2 1 1c0 1-1 2-2 2-2-3-5-4-6-8z" class="M"></path><path d="M321 122c-1-1-2-2-2-3s0-1 1-2l1 1 1 1c1 4 4 5 6 8l1 1-2 1c-2-2-4-4-6-7z" class="C"></path><path d="M325 115c1 0 1 0 2-1 1 0 3 1 4 1 2 2 4 4 4 7l1 2v1h-2l-1 1v1l-1-1 1-1-1-1v-2c0-1 0-2-1-3s-2-2-4-2h-1c-1 1-1 1-1 3h0c-2 1-2 0-3-1l-1-1c1-2 2-2 4-3z" class="B"></path><path d="M321 122c2 3 4 5 6 7l2-1 1 1v-2h0c1 0 1 1 2 1v-2l1 1v-1l1-1h2l-1 5v1l-1 2-1 1-1-1c-1 1-2 2-3 2-1 1-1 1-1 2l-1-1c-1-1-1-1-2-1h-1-2c-3 0-6-1-9-3h0c0-1 2-3 4-4v-1h-3c2 0 3 0 4 1 0 1 0 1-1 1 1 1 1 2 2 2v-2-4c-1 0-1-1-2-1h0v-1h2c1-1 1-1 2-1z" class="R"></path><path d="M325 134c0-1 1-2 2-3h1l1 1-1 1h1l-4 1z" class="X"></path><path d="M329 128l1 1s1 1 1 2c-1 1-1 2-2 2h-1l1-1-1-1-1-2 2-1z" class="S"></path><path d="M319 129h3 1 2c0 2-1 2-1 3-2 0-2-1-3-1s-1 1-2 1v-1-2z" class="Z"></path><path d="M331 131h1v1h0c1 1 1 1 2 1l-1 1-1-1c-1 1-2 2-3 2-1 1-1 1-1 2l-1-1c-1-1-1-1-2-1h-1-2c1-1 3-1 3-1l4-1c1 0 1-1 2-2z" class="H"></path><path d="M317 123h2l3 3h1c1 1 2 1 2 3h0-2-1-3v-4c-1 0-1-1-2-1h0v-1z" class="J"></path><path d="M333 127v-1l1-1h2l-1 5v1l-1 2c-1 0-1 0-2-1h0v-1h-1c0-1-1-2-1-2v-2h0c1 0 1 1 2 1v-2l1 1z" class="G"></path><path d="M332 126l1 1h1c0 2-1 1-2 2v2h-1c0-1-1-2-1-2v-2h0c1 0 1 1 2 1v-2z" class="E"></path><path d="M338 122c1-1 2-1 3-1 2 0 3 2 4 3l1 1h0c2 2 2 7 4 8h0-2v1 1c1 0 1 1 1 2h0c1 1 0 2 0 3h-2l-1-1-1 1 1 3v1h-2-1l2 1h-2l-2-3-1-3-3-3c0-2-1-4-2-5v-1l1-5v-1l-1-2h3z" class="L"></path><path d="M339 135l1-1c1 1 2 1 2 2s0 2-1 2c-1-1-1-2-2-3z" class="i"></path><path d="M344 144c0-2 0-3-1-5l1-1 1 1v1l1 3v1h-2z" class="V"></path><path d="M337 136c2-2 2-2 2-4v3c1 1 1 2 2 3v4l-1-3-3-3z" class="O"></path><path d="M342 124c2 2 3 3 3 5 1 1 1 2 1 2l-2 2c-1-1-2-2-2-3-1 0-1 0-1-1h0 2c1-2-1-3-1-5h0z" class="C"></path><path d="M338 122c1 1 3 1 4 2h0c0 2 2 3 1 5h-2l-3-3c-1-1-2-1-2-2l-1-2h3z" class="S"></path><path d="M336 124c0 1 1 1 2 2l3 3h0c-1 1-1 2-2 3 0 2 0 2-2 4 0-2-1-4-2-5v-1l1-5v-1z" class="H"></path><path d="M336 124c0 1 1 1 2 2-1 1 0 3-1 4h-2l1-5v-1z" class="e"></path><path d="M309 131v-1c1 0 3 2 4 2h0c3 2 6 3 9 3h2 1c1 0 1 0 2 1l1 1c0-1 0-1 1-2 1 0 2-1 3-2l1 1 1-1 1-2c1 1 2 3 2 5l3 3-2 1h0c-2 0-2 1-3 2-2 2-3 3-6 5l-2 1-2 1-1 2-4-1c0-1 0-2 1-2v-1l-1-1h0 0c-1-1 0-1-1-1h-1c0-1 0-1 1-2-1 0-1-1-2-1 0-1-1-1-2-2-2 0-5-2-7-4-1-1-1-2-1-3h2v-2z" class="S"></path><path d="M329 135c1 0 2-1 3-2l1 1c-1 1-2 2-4 3v1-3z" class="F"></path><path d="M315 140c3 1 8 2 11 1 1-1 2-1 3-1l-2 2h-3c-2 0-3 1-5 1-1 0-1-1-2-1 0-1-1-1-2-2z" class="K"></path><path d="M319 143c2 0 3-1 5-1h3c-1 1-2 1-3 2-1 0-3 1-3 2h-1 0 0c-1-1 0-1-1-1h-1c0-1 0-1 1-2z" class="J"></path><path d="M309 131v-1c1 0 3 2 4 2h0c3 2 6 3 9 3h2 1c1 0 1 0 2 1l1 1c0-1 0-1 1-2v3c-1 0-2 1-3 1-4 1-9 0-12-2h-1l-4-4v-2z" class="f"></path><path d="M324 135h1c1 0 1 0 2 1l1 1c0-1 0-1 1-2v3c-1 0-2 1-3 1 0-1 0-1-1-2h-3c-1 0-1 0-2-1h3l1-1z" class="E"></path><path d="M323 136c-2 0-7 0-8-1-1 0-2-1-3-2h0l1-1c3 2 6 3 9 3h2l-1 1z" class="G"></path><path d="M329 140h1c2-1 3-3 5-4 1 1 2 3 3 4h0c-2 0-2 1-3 2-2 2-3 3-6 5l-2 1-2 1-1 2-4-1c0-1 0-2 1-2v-1l-1-1h1c0-1 2-2 3-2 1-1 2-1 3-2l2-2h0z" class="J"></path><path d="M327 148c0-1 0-2 1-3 1 0 3 0 4-1s1-2 3-2c-2 2-3 3-6 5l-2 1z" class="T"></path><path d="M320 150c0-1 0-2 1-2h4v1l-1 2-4-1z" class="K"></path><path d="M329 140h4c0 1 0 1-1 1l-2 2c-2 2-6 1-9 4l-1-1h1c0-1 2-2 3-2 1-1 2-1 3-2l2-2h0z" class="X"></path><path d="M340 139l1 3 2 3h2l2 2v3c1 3 1 5 2 8l-1 7 1 3c-1-1-2-1-3-1l-1 1h0v1c-1 0-1-1-2-2 0-1-1-1-1-1l-3 3-1-1 1-2c-1 0-2 0-3 1h-1c-1-1-2-3-3-4l1-2c-3-1-3-2-6-2h-1l-1-2 1-1-3-2c1-1 2-1 2-2 0 0-1 0-1-1h0l1-2 2-1 2-1c3-2 4-3 6-5 1-1 1-2 3-2h0l2-1z" class="N"></path><path d="M336 144l1-2 1 1c1 1 1 2 1 2 0 1-1 2 0 3 0 0 1 0 1 1v1h-2-1 0c-1-1-1-2-1-3 1 0 1-1 1-1l-2-1 1-1z" class="M"></path><path d="M337 150h0 1l2 2c1 1 0 3 3 4 0 2 0 2-2 4l-1-1v-1h-2c-1-2-2-1-2-3l-1-1h0-1 1c1-1 1-3 2-4z" class="U"></path><path d="M334 154l-2-2c-1 0-3 0-4-2v-2c1-1 2-1 3-2 1 0 1-1 2-1h1c0-1 1-1 2-1l-1 1 2 1s0 1-1 1c0 1 0 2 1 3-1 1-1 3-2 4h-1z" class="E"></path><path d="M335 145l2 1s0 1-1 1c0 1 0 2 1 3-2 1-2-1-3-1l-1 2h0c-1-1-2-1-3-1h-1c0-2 3-2 4-3l2-2z" class="f"></path><path d="M340 139l1 3 2 3h2l2 2v3c1 3 1 5 2 8l-1 7 1 3c-1-1-2-1-3-1l-1 1h0v1c-1 0-1-1-2-2 0-1-1-1-1-1-1 0-1 0-1-1 1 0 1-1 2-1h1l-1-1c1-1 2-1 2-2 0-2 0-3-1-5-1-3-1-5-2-8-2-2-2-5-4-8h0l2-1z" class="Q"></path><path d="M348 162v3l1 3c-1-1-2-1-3-1l-1 1h0c0-1 0-2 1-4 0 1 0 1 1 1 1-1 1-2 1-3z" class="e"></path><path d="M344 164h1 1c-1 2-1 3-1 4v1c-1 0-1-1-2-2 0-1-1-1-1-1-1 0-1 0-1-1 1 0 1-1 2-1h1z" class="L"></path><path d="M343 145h2l2 2v3c1 3 1 5 2 8l-1 7v-3l-5-17z" class="D"></path><path d="M324 151l1-2c0 2 0 2 1 3l1 1c1 0 1 0 2 1s3 1 4 2c1 2 2 3 4 3h1 0 1 0c0 1 1 2 2 3 1-1 2-1 3-2 1-2 1-3 0-4 1 2 1 3 1 5 0 1-1 1-2 2l1 1h-1c-1 0-1 1-2 1 0 1 0 1 1 1l-3 3-1-1 1-2c-1 0-2 0-3 1h-1c-1-1-2-3-3-4l1-2c-3-1-3-2-6-2h-1l-1-2 1-1-3-2c1-1 2-1 2-2 0 0-1 0-1-1h0z" class="b"></path><path d="M326 156l10 5c2 2 4 3 7 3-1 0-1 1-2 1 0 1 0 1 1 1l-3 3-1-1 1-2c-1 0-2 0-3 1h-1c-1-1-2-3-3-4l1-2c-3-1-3-2-6-2h-1l-1-2 1-1z" class="D"></path><path d="M333 161l2 2c1 1 2 0 3 1 0 0 1 1 2 1 0 1 0 1-1 1s-2 0-3 1h-1c-1-1-2-3-3-4l1-2z" class="E"></path></svg>