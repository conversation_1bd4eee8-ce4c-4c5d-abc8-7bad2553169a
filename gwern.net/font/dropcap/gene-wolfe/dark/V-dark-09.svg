<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="171 105 679 832"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#2b292b}.C{fill:#272628}.D{fill:#050505}.E{fill:#454446}.F{fill:#131212}.G{fill:#7f7e80}.H{fill:#bcb9ba}.I{fill:#5f5d60}.J{fill:#070808}.K{fill:#d8d6d7}.L{fill:#353436}.M{fill:#525153}.N{fill:#fdfdfd}.O{fill:#2e2d2e}.P{fill:#e6e5e5}.Q{fill:#f1f0f1}.R{fill:#9d9a9c}.S{fill:#e1e0df}.T{fill:#ecebec}.U{fill:#cac8c7}.V{fill:#d51115}.W{fill:#da0f15}.X{fill:#ad050b}.Y{fill:#3a0e11}.Z{fill:#e82829}.a{fill:#e7262c}.b{fill:#b20e15}</style><path d="M730 549l1-1 1 1 1 1-1 2h-1-1c-1-2 0-2 0-3z" class="E"></path><path d="M707 553l2-1v1c0 1 0 2-1 3h-2v-2l1-1z" class="I"></path><path d="M731 630c1 0 1 0 2 1 0 1 0 1-1 3h-1l-1-1c0-1 0-1 1-3z" class="G"></path><path d="M709 671h1 1v4h-2v-1c-1-1-1-2 0-3z" class="E"></path><path d="M712 738h1l1 1c0 2 0 2-1 3h-2v-3l1-1z" class="H"></path><path d="M733 619h1l1 1c0 1-1 2-1 3h-3c0-3 0-2 2-4z" class="M"></path><path d="M718 573h2v1c0 2 0 2-1 3h-2l-1-1c1-2 1-2 2-3z" class="G"></path><path d="M719 600h1c1 0 1 0 2 1-1 2-1 2-2 3h-1c-1 0-1-1-1-2s0-1 1-2zm10-40h2v1c0 2 1 2-1 3-1 1-1 1-2 0 0-2 0-2 1-4zm-25 5h1l1 1c-1 2-1 2-2 3h-2l-1-1c1-2 1-2 3-3z" class="I"></path><path d="M448 853h2c2 0 2 0 2 1 0 2 0 2-1 3h-1c-2 0-2-1-2-2v-2z" class="Q"></path><path d="M715 644h1c1 1 1 1 1 2 0 2 0 2-2 3h-1c-1-1-1-1-1-2 0-2 0-2 2-3z" class="G"></path><path d="M411 605c2-4 7-10 11-13l-8 13c-1 0-1 0-2 1h0l-1 1v-2z" class="I"></path><path d="M612 688l4 1h0v2l2 2v2c-4-2-7-3-10-4 2 0 3-2 4-3z" class="G"></path><path d="M577 710s1 0 1 1c0 2-6 6-8 9-1 1-1 2-2 2l-2-1 2-1c0-1 0-1 1-1 1-4 5-7 8-9z" class="B"></path><path d="M628 237l1-1v-2h1c2 7 4 16 3 24 0-8-4-14-5-21z" class="D"></path><path d="M593 808c1 1 4 6 6 6s3 0 5-1v1c-2 2-3 2-6 2s-5-1-7-2c1-2 0-1 0-2 0-2 1-2 1-3 0 0 0-1 1-1z" class="T"></path><path d="M333 330l1 2c-3 3-7 4-10 7-1-1-3-1-4-1 3-4 8-7 13-8z" class="R"></path><path d="M562 832h4v1c2 2 2 6 2 9h-3c-2-3-3-6-3-10z" class="V"></path><path d="M388 642h-1-6v-1l1-2c6-1 12-2 18-2 2 0 3 0 4 1-1 1-1 1-2 0-2 0-6 0-8 1h-1c-1 1-3 1-5 2v1z" class="I"></path><path d="M563 729v-4c1-1 1-2 2-4h0 1l-1 25c-2-2-2-8-2-11v-6z" class="W"></path><path d="M416 631c-1-2-1-7-1-9v-2l1-1v-3c1-1 1-2 2-3v1c-2 9 0 15 3 23-1 1-2 0-2 0-1-1-1-2-1-3-1-1-1-2-2-3z" class="C"></path><path d="M309 394v1h0c0-2 0-3-1-6l-3-3c-2 0-4 0-6-1-3 0-6-1-9-2 1-1 1-1 2 0 3 0 5 0 8-1h1c0 1 1 2 1 2 1 0 6 0 6 1v2c1 1 2 2 2 3l1 2v1l-2 1z" class="T"></path><path d="M564 705h2v1c-1 2-1 4-1 6l1 6v3h-1 0c-1 2-1 3-2 4v4c-1-3-1-9 0-12v-10l1-2z" class="b"></path><path d="M565 712l1 6-1 1c0-1 0-1-1-1-1-2 0-3 0-5l1-1zm-2 23c0 3 0 9 2 11l1 17v1l-3-1v-28z" class="V"></path><path d="M386 710c0-3 3-4 4-7 1-1 1-2 2-3 1 0 1 0 2-1 3-2 6-4 10-6 1 0 2 0 3 1-8 4-14 9-21 16z" class="E"></path><path d="M396 642h1c2 0 3 1 5 2l1 2c1 2 2 3 4 5 3 8 5 14 3 23h-1c1-3 1-7 0-10v-1l-1-5c-3-6-5-13-12-16z" class="H"></path><path d="M609 716h1c-1 0-2 1-2 2-3 2-4 5-4 8 0 2 1 5 3 6 2 2 4 2 7 2h1v2h-1c-2 0-3 1-5 0-2 0-5-3-6-4-1-3-1-6-1-9 1-3 4-6 7-7z" class="P"></path><path d="M439 194c3 5 6 12 5 18 0 2 0 3-2 4-1 1-2 1-4 1v-1h0c3-3 4-6 3-10 0-2 0-4-1-6 0-1 0-1-1-2v-1-3z" class="N"></path><path d="M425 218c2 0 4-1 6-1 1 0 1 0 2 1 5 0 12 0 16 3v1h-1l1 1-1 1c0-1 0-1-1-1h0c-2-1-3-1-4-2h0c-2-1-3 0-5 0-5-1-8-1-13 0v-2-1z" class="U"></path><path d="M406 788c0 1 1 2 1 3v1c0 1 1 1 1 2v2 5h0c-1 1-1 2-2 3v1l-2 2-1 1h0c-2-1-1-2-1-3 0-2 1-2 1-4 1-1 2-3 2-5 0-3 0-5 1-8z" class="F"></path><path d="M516 892l1-6h0c2 3 1 6 3 9l-1 23c-1-1-1-3-1-4-1-1-1-2-1-3l-1-19z" class="W"></path><path d="M566 239l1-1h0l1 2c-1 5-1 11 2 15 1 2 3 4 4 5 1-2 0-2-1-3l1-2c2 4 4 7 9 9v1l-6-2c-5-2-9-7-11-13h0c-1-3-1-6 0-9v-2h0z" class="S"></path><path d="M573 767c4 3 10 8 12 12l1 1v-1-1h0c2 4 4 8 4 12l-1 1c-2-2-2-4-3-6-2-4-5-7-8-10-1-4-3-5-5-7v-1z" class="E"></path><path d="M393 639h1c2-1 6-1 8-1 1 1 1 1 2 0l1 4-1 1 3 8c-2-2-3-3-4-5l-1-2c-2-1-3-2-5-2h-1-8v-1c2-1 4-1 5-2z" class="G"></path><path d="M393 639h1c2-1 6-1 8-1 1 1 1 1 2 0l1 4-1 1c-1-1-3-3-5-4h-6z" class="L"></path><path d="M455 722h0c-1 2-1 4-2 5l1 1c-2 8-3 16 1 23v1c3 3 5 5 9 6h1-1c-2 0-4-1-6-2-4-3-7-9-8-15-2-7 1-13 5-19zm-57-174l-2-1c-5-3-10-8-11-13v-1c-2-5-1-10 0-15 0-4 3-9 6-12-2 6-4 11-5 17-1 5 2 12 5 17 2 3 5 5 7 8z" class="H"></path><path d="M440 267c3-1 6-3 9-3h1l1-1c2 0 5 1 7 3l2 2c-2-1-3-1-4-2h-1l2 2h0c-6-1-10-3-15 0-4 2-8 7-9 12v5c-1 0-1-1-2-1 0-1-1-1-2-1v-1l2-2v-1c1-5 5-10 9-12z" class="G"></path><path d="M431 773c4-3 6-6 8-11s2-11-1-16c-1-3-4-5-7-6s-6 0-8 1h0-1l1-1c2-2 5-2 8-1 4 0 7 2 9 6 3 5 3 9 2 14-2 5-5 11-9 13l-2 1z" class="Q"></path><path d="M516 872l3-13v11c1 8 2 17 1 25-2-3-1-6-3-9h0l-1 6c-1-7 1-14 0-20z" class="X"></path><defs><linearGradient id="A" x1="415.244" y1="632.225" x2="410.626" y2="639.995" xlink:href="#B"><stop offset="0" stop-color="#626164"></stop><stop offset="1" stop-color="#797979"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M411 635c0-7 0-12 1-18 1-3 2-6 4-9h0c1 4-2 12-3 16 0 7 1 14 4 20h-1l-1 1c-2-3-2-7-4-10z"></path><path d="M547 767v4c-1 6 1 51 0 52-1 0-1-1-2-1v-6c0-6 1-41 0-43 0-1-1-1-1-1l1-4 2-1z" class="Y"></path><path d="M386 523l1-1s0-1 1-1v-1c1 2-1 6 1 8v3c0 1 0 1 1 2v2c2 2 3 6 6 7v1h1c2 1 3 1 5 2 1 1 3 2 4 2l-1 2c1 0 1 0 2 1-2 1-3 0-4 0-2 0-3-1-4-1l-1-1c-2-3-5-5-7-8-3-5-6-12-5-17z" class="F"></path><path d="M393 695h1c1 1 4-1 5-2 2-1 3-1 5-1v1c-4 2-7 4-10 6-1 1-1 1-2 1-1 1-1 2-2 3-1 3-4 4-4 7h0l-3 3c0-1 0-1 1-2 0-1 1-2 1-3v-1s1-1 1-2l-3 4-3 3-1 2-1-1c3-6 7-10 12-15l3-3z" class="C"></path><path d="M599 580c4-3 17-5 19-9s2-8 5-12c1-2 3-4 5-4 0 2-2 5-3 7 0 3-1 6-1 9v-1c-1-1-1-3-1-4v-1c-2 1-1 2-2 3 0 1 0 1 1 3 0 1 0 1-1 2l-1 1h-2-2v1h-1c-3 1-5 2-7 3-1 0-2 0-3 1h-1l-2 2h-1c-1 0-1 0-2 1h0l-1-1 1-1zM306 447v1l-4 3c3 0 3-2 6-1h0l-3 3h-1v1s1 0 2 1 4 3 4 5l2 2-2 2-1-3h-1c1 2 2 5 1 8v2h0l-1-6c0-3-2-8-4-10h-6-11c-2 0-4 0-5-1h10c5-1 10-4 14-7z" class="K"></path><defs><linearGradient id="C" x1="662.338" y1="491.353" x2="656.828" y2="511.053" xlink:href="#B"><stop offset="0" stop-color="#545455"></stop><stop offset="1" stop-color="#737075"></stop></linearGradient></defs><path fill="url(#C)" d="M660 513h0c0-3 0-6-1-8-1-4-3-10-3-14 1-4 2-8 4-11v-1c1-1 1-2 2-3 0 4-4 7-4 12 0 2 1 6 2 8 2 7 4 14 4 21v4c0 1-1 2-1 3s-1 1-1 2c0-4 1-9-2-11v-2z"></path><path d="M562 767c2 0 2 1 3 2v13c0 2 0 4 1 5-1 2-1 4 0 6h-3l-2-2c0-4 0-1 2-3h-2c-1-5-1-10 0-14l1-7z" class="a"></path><path d="M566 793l-1 22c0 4 1 9 0 13 0 1 0 1-1 2h1c-1 1-3 0-4 0v-2l2-24c0-3 1-7 0-11h3z" class="Z"></path><path d="M334 468l1-1c0-2 1-2 3-2-1 1-2 2-1 3l-9 24h-1c-5 11-4 22-5 34v-5c-1-2-1-5 0-7v-6c1-1 0-3 0-4 1-1 0-2 1-3v-2l1-1v-1-2l1-1c0-1 0-2 1-3v-2c1-1 0-3 1-4 0-2 1-3 2-4v-1c0-1 0-2 1-2v-1-1c0-1 0-1 1-1v-1l3-6z" class="L"></path><path d="M721 481v-7l3 1c-1 5 1 12 3 17 4 3 8 6 9 12 0 3-1 6-2 8l-1 1c0-2 1-4 1-6 0-3-1-5-2-7s-3-3-4-4h-1l-2-2c-1 0-2-1-2-2-2-2-1-8-2-11z" class="Q"></path><path d="M627 283l1-1c0-3 0-6 1-8s1-3 2-4c3-12-1-23-4-34-1-3-2-6-2-8 2 2 3 6 3 9 1 7 5 13 5 21v7c-1 5-3 9-4 13v17c0 2 1 4 0 6 0 2 0 5-1 7v-2c-1-7-1-14-2-21 1-1 1-2 1-2z" class="L"></path><path d="M306 447l5-5c6-6 10-14 10-23-1-6-4-15-9-18-2-1-5-1-7-2l1-1c4 1 8 2 10 4 4 3 7 9 7 14 0 3 0 6-1 8s-1 4-2 6c-2 7-8 14-14 18v-1z" class="P"></path><path d="M453 260l1-1c4 2 11 9 13 14 1 4 1 10-1 14s-4 7-8 8c-2 1-5 1-7 0s-3-2-4-3v-1h0c3 3 4 4 8 4 2 0 4-1 6-3 3-4 4-9 4-14-1-4-3-7-5-10l-2-2-3-3c0-2-1-2-2-3h0z" class="K"></path><path d="M517 911c0 1 0 2 1 3 0 1 0 3 1 4 1 3 0 7 0 10 1 16 2 32 1 48 0 3 0 6-1 8-1 3 0 7 0 10l-1-1-1-82z" class="V"></path><path d="M550 750l1-2c0 4 2 10 5 13 1 1 5 2 7 2l3 1 3 1 1-1c-1-2-1-3-2-5h1c2 2 2 6 4 8v1c-2-1-5-2-7-2h-4c-2-1-4-1-5-1h-8v-2c0-2 0-3-1-5l2-8z" class="G"></path><path d="M549 763c0-1 1-2 1-2l2 1c1 0 2-1 3-1v1h-2l-1 1c2 1 2 1 5 2h-8v-2z" class="I"></path><path d="M550 750c1 4 3 8 5 11-1 0-2 1-3 1l-2-1s-1 1-1 2c0-2 0-3-1-5l2-8z" class="E"></path><path d="M397 777v-1l-1-1 1-1 3 3c2 2 3 4 4 7h0l2 4c-1 3-1 5-1 8 0 2-1 4-2 5 0 2-1 2-1 4h-1c0 1 0 2-1 3v2c1-1 0-1 1-1l1 1-3 3c-3 4-7 8-11 12v-1c0-1 3-4 4-5 5-8 10-17 10-26 0-3-1-5-1-8s-2-6-4-8z" class="I"></path><path d="M404 784l2 4c-1 3-1 5-1 8 0 2-1 4-2 5v-11c0-1 1-1 1-1v-5z" class="M"></path><path d="M461 716l1 4c-1 2-2 4-4 6-1 2-2 4-2 7-1 2-1 4-1 6l1 1v2l2 5h1c1 2 3 3 4 5h0-1l-1-1h-1c-1-1-4-4-4-6-1 0-1-1-1-1l-1 1c1 2 2 3 1 6h0c-4-7-3-15-1-23l-1-1c1-1 1-3 2-5h0c1-3 4-5 6-6zm151-513l3-6c1 5 1 11 3 15s3 7 4 11l2 8c-1 2-1 3-2 5v2l-2-2h0l-8-33z" class="E"></path><path d="M618 212c2 4 3 7 4 11l-1 3c-1-5-3-9-3-14z" class="O"></path><path d="M622 223l2 8c-1 2-1 3-2 5l-1-10 1-3z" class="B"></path><path d="M663 339c7 2 12 2 18 0 7-3 14-11 17-18l5 5 7-11 1 1c-1 4-6 6-7 10v1 1 1c-1 1-2 1-3 3l-1 1h-1 0c0-2 0-4 2-6l-2-2v2c-1 2-3 4-4 6-5 4-10 8-16 10-1 1-3 1-5 0l-1-1c-2-1-9-1-10-3h0z" class="K"></path><path d="M402 616c0 2 0 3 1 4l1 1c1 0 1 0 1 1h1 0c-9 9-21 15-32 16 1-1 4-3 6-4 5-3 11-6 15-10 3-3 5-5 7-8z" class="I"></path><path d="M716 481h5c1 3 0 9 2 11 0 1 1 2 2 2l2 2h1l-5 24-1-1h0v-1c0-4-2-9-2-13 0-8-2-16-4-24z" class="C"></path><path d="M722 518c1-3 0-6 0-8 1-6 0-11 0-16 1 1 3 2 4 2h1 1l-5 24-1-1h0v-1z" class="T"></path><path d="M362 728h2 0c0 1-1 1-1 2h1c1-1 3 0 4 0h1c0 1 1 1 1 1l2 2v-5l1-1h0v-3c1-1 1-1 1-2h1c-3 14 0 30 9 42l2 2 2-1c3 4 6 6 9 9l-1 1 1 1v1c2 2 4 5 4 8-3-7-10-12-15-17-3-3-5-6-8-9-3-6-5-11-6-17-1-3-1-6-2-8-1-1-2-1-2-1-5-1-7 1-11 4l5-9z" class="S"></path><path d="M388 765c3 4 6 6 9 9l-1 1 1 1v1a57.31 57.31 0 0 1-11-11l2-1z" class="M"></path><path d="M589 791l1-1 1 7h0c1 2 1 3 2 4 0 2-1 4 0 7-1 0-1 1-1 1 0 1-1 1-1 3 0 1 1 0 0 2-3 4-6 8-9 11-5 4-10 6-16 7h-4c-4-1-8-2-12-4h-2c-4-3-10-11-11-16 2 4 6 9 9 12 4 3 10 5 15 6 1 0 3 1 4 0 2 0 5-1 7-2 7-4 13-11 16-18 2-5 3-14 1-19z" class="N"></path><path d="M721 445c2 2 5 8 6 11 0 0 1 2 2 2 0 0 2 0 2-1 6-1 15-1 20 3 2 1 3 2 4 4-7-3-15-6-23-3-2 1-4 2-5 4-1 4-1 7-3 10l-3-1 1-6v-5c0-2 0-5-1-8s-1-6-1-9l1-1z" class="P"></path><path d="M721 455l1-1 1-1v1c2 2 2 3 4 5l-2 2v4c-1 1-1 2-1 3l1 1-1 1-1-2h-1v-5c0-2 0-5-1-8z" class="S"></path><defs><linearGradient id="D" x1="411.809" y1="607.519" x2="405.101" y2="624.446" xlink:href="#B"><stop offset="0" stop-color="#757378"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#D)" d="M411 605v2l1-1h0c1-1 1-1 2-1-3 6-6 12-6 18 0 3 0 6 1 10v1 1 1l1 1h1v-2h0c2 3 2 7 4 10 0 1 2 4 2 6-1 1 0 4 0 6h-1-1c0-2-1-4-2-5h-1c-1-4-3-8-4-12-2-6-2-11-2-18h0-1c0-1 0-1-1-1l-1-1c-1-1-1-2-1-4 1-3 4-7 7-8 1-1 1-2 2-3z"></path><path d="M402 616c1-3 4-7 7-8l-3 14h-1c0-1 0-1-1-1l-1-1c-1-1-1-2-1-4z" class="L"></path><path d="M408 623c0 3 0 6 1 10v1 1 1l1 1h1v-2h0c2 3 2 7 4 10 0 1 2 4 2 6-1 1 0 4 0 6h-1-1c0-2-1-4-2-5-3-9-7-20-5-29z" class="C"></path><path d="M621 501h1l1-1 1-1 1-1c1-2 4-5 6-6 1-1 2-1 3-2h1l1-1c-1-2-2-3-2-5 0-1 0-2 1-2h1l-1 2c0 1 2 3 3 4v3s1 0 1 1c0 3 0 4 2 6-2 0-3-1-5 0l-1-1c-5 2-11 8-13 13-2 4-3 9-5 14l-3-1c1-4 3-9 4-13l3-9z" class="D"></path><path d="M546 828h1v13l-1 31c0 9 1 17 2 26 0 4-1 8-1 12 0 5 0 11 1 17 1 3 2 5 2 8 0 1 0 1-1 2h-1c-1 0-2 0-2-1-2-2-2-5-2-7v-13c1-6-1-12 0-18 0-7 1-14 1-20 1-17 0-34 1-50z" class="V"></path><defs><linearGradient id="E" x1="626.312" y1="538.085" x2="614.188" y2="529.915" xlink:href="#B"><stop offset="0" stop-color="#b7b3b6"></stop><stop offset="1" stop-color="#d4d4d5"></stop></linearGradient></defs><path fill="url(#E)" d="M622 510l2 1c-1 2-2 4-3 7 0 9 0 14 6 21l-1 1 2 1h-4c2 4 5 7 9 8s7 0 11-1h0c1 0 1 1 2 0 4 0 8-3 10-7 2-2 4-5 5-8 0-2 0-4 1-7h0c0-1 1-1 1-2s1-2 1-3c0 3 0 6-1 9-1 7-4 13-10 18-6 3-13 5-19 3-7-1-12-6-15-11s-3-11-2-16c2-5 3-10 5-14z"></path><path d="M620 534h0c-2-5-1-11 1-16 0 9 0 14 6 21l-1 1 2 1h-4c-2-2-3-4-4-7z" class="B"></path><path d="M620 534h2l2 3c0 1 2 2 2 3l2 1h-4c-2-2-3-4-4-7z" class="C"></path><defs><linearGradient id="F" x1="358.666" y1="478.199" x2="351.706" y2="472.305" xlink:href="#B"><stop offset="0" stop-color="#424143"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#F)" d="M346 480c4-9 12-19 20-25v1c2 0 4 0 5 1h1c2 1 2 0 4 2l1 1c-14 4-24 16-31 28-2 3-4 6-5 10-1 2-2 5-4 7 0-3 1-7 3-10 1-5 3-10 6-15z"></path><defs><linearGradient id="G" x1="367.249" y1="455.42" x2="351.861" y2="473.243" xlink:href="#B"><stop offset="0" stop-color="#131314"></stop><stop offset="1" stop-color="#49494a"></stop></linearGradient></defs><path fill="url(#G)" d="M346 480c4-9 12-19 20-25v1c2 0 4 0 5 1h1l-6 3c-5 2-10 9-13 13-1 1-2 3-4 3-1 1-1 3-3 4h0z"></path><defs><linearGradient id="H" x1="589.268" y1="235.531" x2="558.109" y2="229.617" xlink:href="#B"><stop offset="0" stop-color="#cccdcf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#H)" d="M566 239h0c-2 4-2 9-2 13l-6-6-5 5c0-2 1-4 2-6 2-4 6-9 9-12 11-10 22-15 36-19 2 1 4 0 7-1v1c-7 4-16 5-23 9-4 1-9 5-11 8s-4 5-5 9l-1-2h0l-1 1z"></path><path d="M462 720l3 10 6 25c-3-1-5-1-8-3h0c-1-2-3-3-4-5h-1l-2-5v-2l-1-1c0-2 0-4 1-6 0-3 1-5 2-7 2-2 3-4 4-6z" class="F"></path><defs><linearGradient id="I" x1="348.481" y1="496.927" x2="336.054" y2="494.033" xlink:href="#B"><stop offset="0" stop-color="#cac9cb"></stop><stop offset="1" stop-color="#fcfafc"></stop></linearGradient></defs><path fill="url(#I)" d="M337 493c4-7 6-15 11-21 2-5 6-9 10-12 2-3 9-9 13-9l-5 4c-8 6-16 16-20 25-3 5-5 10-6 15-2 3-3 7-3 10-3 11-4 23-3 34l-1 1c-1-12-2-23 0-34l-1-1h0l-2 3c0-4 1-7 2-10h1l1-2 1 1v-2-1l2-1z"></path><path d="M332 498h1l1-2 1 1v-2-1l2-1-4 13-1-1h0l-2 3c0-4 1-7 2-10z" class="M"></path><path d="M616 689c9 4 14 12 20 18 2 2 5 3 7 5 2 3 4 6 6 10 1 2 2 4 2 7h1v3-1c-2-6-7-16-13-19-2-1-3 0-4 0-2 3-2 4-3 7v10 18 58h-1l-1-67c0-9 1-17-1-26-1-8-4-13-11-17h0v-2l-2-2v-2z" class="N"></path><defs><linearGradient id="J" x1="424.893" y1="694.151" x2="423.38" y2="686.148" xlink:href="#B"><stop offset="0" stop-color="#2c2b2d"></stop><stop offset="1" stop-color="#454446"></stop></linearGradient></defs><path fill="url(#J)" d="M452 681l1 4 2 7-3-1c-6-1-12-2-18-2-9-1-19 1-27 5-1-1-2-1-3-1v-1c-2 0-3 0-5 1-1 1-4 3-5 2h-1c6-4 13-7 20-9 2-1 3-1 5-1v-1h0l5-1v-1h20c3 0 6 0 9-1z"></path><path d="M426 684h7c7 1 14 2 20 1l2 7-3-1s0-1-1-1c-3-4-9-4-14-5h-12l1-1h0z" class="D"></path><path d="M452 681l1 4c-6 1-13 0-20-1h-7v-1h-3v-1h20c3 0 6 0 9-1z" class="E"></path><path d="M638 488v-7c1-5 5-10 9-13 1-1 3-1 5-1 3-1 5-1 7 0h1c2 0 4 1 5 3h0-1 0-2c-2-1-7-1-9 0l-3 1c-3 3-5 8-5 12-1 4 0 8 2 12 0 5 3 6 3 10h0c-4-2-9-5-15-3-4 2-8 5-11 9h0l-2-1c2-5 8-11 13-13l1 1c2-1 3 0 5 0-2-2-2-3-2-6 0-1-1-1-1-1v-3z" class="M"></path><path d="M570 680c2 2 2 2 2 4v1c-1 1-2 2-2 3h0c2 0 4 0 6-1h5c4 1 11-1 16-1 3-1 7 0 11 1l4 1c-1 1-2 3-4 3-5 0-11 0-16 1h-1c-6 1-11 4-16 7-3 2-6 4-9 7v-1h-2l6-25z" class="C"></path><path d="M566 705c0-2 0-4 1-5h1c1-2 3-3 5-4h0v1 1c-1 0-2 1-2 2h0 1c1-1 2-1 3-1-3 2-6 4-9 7v-1z" class="E"></path><defs><linearGradient id="K" x1="588.361" y1="702.706" x2="597.289" y2="682.582" xlink:href="#B"><stop offset="0" stop-color="#49474f"></stop><stop offset="1" stop-color="#787876"></stop></linearGradient></defs><path fill="url(#K)" d="M573 696l3-3c10-5 21-6 32-6l4 1c-1 1-2 3-4 3-5 0-11 0-16 1h-1c-6 1-11 4-16 7-1 0-2 0-3 1h-1 0c0-1 1-2 2-2v-1-1h0z"></path><path d="M400 777c-3-5-8-8-11-12s-7-13-7-18c1-2 1-5 0-6 0-3-1-6 0-9 1-2 0-3 2-6l-1 4c0 3 0 7 1 10 0 4 1 8 2 11 2 7 6 13 12 17 7 5 17 8 25 7 1 0 2-1 3-1h1l1-1h3l2-1c4-2 7-8 9-13 1 3 1 4 0 7-2 4-6 8-10 10h-1c-1 2-3 2-5 3h-1c-6 2-15 1-22 0l-3-2z" class="L"></path><path d="M551 286c0-8 3-15 6-21h0c-3 13-3 27 4 38 4 5 11 11 17 11 8 2 14 0 20-4v1 4c-1 1-1 2-2 2-2 0-3 1-5 1v1c1 0 3 0 4-1v1c0 1-1 2-1 3-3-1-5 0-7 0s-4 1-5 0h-1c-7 0-9-2-15-6h0v-1c-1 0-1-1-2-1v-1l-1-1c-1-2 0 0-1-1l-3-3v-1l-1-1c0-1-1-2-1-2l-1-1v-1c0-1 0-3-1-3v-2c-1-2-1-2-1-3-1-2 0-6 0-7-1-2 0-4-1-5-1 1-1 1-1 2l-1 2z" class="Q"></path><path d="M556 302h1v1c1 1 2 2 3 4l1 1c1 1 3 4 5 5l2 1 2 1 1 1h1c1 1 1 1 2 1h1c2 0 7 1 9 0 1 0 1 0 3-1h2 0c1-1 3-2 5-2v-1c1 0 1 0 2-1l2-1v4c-1 1-1 2-2 2-2 0-3 1-5 1v1c1 0 3 0 4-1v1c0 1-1 2-1 3-3-1-5 0-7 0s-4 1-5 0h-1c-7 0-9-2-15-6h0v-1c-1 0-1-1-2-1v-1l-1-1c-1-2 0 0-1-1l-3-3v-1l-1-1c0-1-1-2-1-2l-1-1v-1z" class="K"></path><path d="M663 339c1 2 8 2 10 3l1 1c2 1 4 1 5 0 6-2 11-6 16-10 1-2 3-4 4-6v-2l2 2c-2 2-2 4-2 6h0 1c-3 6-6 12-9 17l-3 5-3 6-3 2-3 10h-1v-5c0-2 0-4 1-5l1-1c1-2 0-5-1-7 0-1-1-3-1-4h-1c-1-3-3-6-6-8h0c-2 0-4 0-5-1h-1c-1 0-2-1-3-1l1-2z" class="D"></path><path d="M682 363c1-4 0-7 2-11 1-2 1-4 2-6h1c0-1-1 0 0-1l2-2c1 1 1 1 0 2l-1 2-1 2s-1 0-1 2v2h0c2-1 3-2 5-3l-3 5-3 6-3 2z" class="C"></path><path d="M563 707v10c-1 3-1 9 0 12v6 28c-2 0-6-1-7-2-3-3-5-9-5-13 2-13 7-27 12-41z" class="D"></path><path d="M551 286l1-2c0-1 0-1 1-2 1 1 0 3 1 5 0 1-1 5 0 7 0 1 0 1 1 3v2c1 0 1 2 1 3v1l1 1s1 1 1 2l1 1v1l3 3c1 1 0-1 1 1l1 1v1c1 0 1 1 2 1v1h0c6 4 8 6 15 6h1c1 1 3 0 5 0s4-1 7 0l-1 1-1 2c-7 2-12 2-19 1l-4-2h-4l-1 1c-1-1-2-1-4-1h-1v2c-1-1-1-2-2-3h0c-2-4-3-7-5-10h1 2l-3-9c0-1-1-2-1-3v-1l-1 1c0-5 0-11 1-15z" class="P"></path><path d="M593 323c-5 1-13 2-19 0h0c-2-1-4-1-5-2-5-4-13-11-14-17 2 1 3 5 5 6 1 2 3 4 5 6h1c6 4 8 6 15 6h1c1 1 3 0 5 0s4-1 7 0l-1 1z" class="L"></path><path d="M552 304c1 0 1 1 1 2 2 8 9 14 16 18h-4l-1 1c-1-1-2-1-4-1h-1v2c-1-1-1-2-2-3h0c-2-4-3-7-5-10h1 2l-3-9z" class="B"></path><path d="M552 313h1 2l5 11h-1v2c-1-1-1-2-2-3h0c-2-4-3-7-5-10z" class="S"></path><path d="M319 400c6 5 9 10 10 19v8c0-1-1-1-1-3 1-1 0-1 0-2v-1-2h0c0 3 0 7-1 10-2 5-4 9-7 13-1 2-3 5-4 7-2 7-2 15-3 22 0 1-1 3-1 4h-1v-2c1-1 1-2 1-2 0-1 0-2 1-3h-2l-1-4 2-2-2-2c0-2-3-4-4-5s-2-1-2-1v-1h1l3-3h0c-3-1-3 1-6 1l4-3c6-4 12-11 14-18 1-2 1-4 2-6s1-5 1-8l1 1c0-6-2-9-4-14-1-1-1-2-1-3z" class="P"></path><path d="M319 400c6 5 9 10 10 19v8c0-1-1-1-1-3 1-1 0-1 0-2v-1-2h0c0 3 0 7-1 10 0-6-1-12-1-17-1-4-3-7-5-10l-1 1c-1-1-1-2-1-3z" class="C"></path><path d="M323 416l1 1c1 3 1 9 0 12-2 6-5 11-8 17-1 2-1 4-3 6-1 0-1 0-1 1l1 2h-1l-2-1v-1c2-2 4-4 5-6 1-5 5-9 6-14l1-1c1-3 2-7 1-10-2 9-5 22-14 27l-1 1c-3-1-3 1-6 1l4-3c6-4 12-11 14-18 1-2 1-4 2-6s1-5 1-8z" class="I"></path><path d="M409 674h1v1 2c-1 1-1 2-1 3s-1 1-1 2h4 0 4c2 0 5-1 7 0v1l-5 1h0v1c-2 0-3 0-5 1-7 2-14 5-20 9l-3 3c-5 5-9 9-12 15l-3 9h-1c0 1 0 1-1 2v3h0l-1 1v5l-2-2s-1 0-1-1h-1c-1 0-3-1-4 0h-1c0-1 1-1 1-2h0-2c2-2 4-4 8-5h0l11-24c1-2 0-4-1-5-2-1-2-1-4-1h0c1-1 2-1 3-1 2 1 4 3 5 5 4-4 8-7 11-10s5-6 8-8c1-1 1-1 2-1 0 1 0 0 1 1h1c1-1 1-2 2-3v-1-1z" class="T"></path><path d="M409 674h1v1 2c-1 1-1 2-1 3s-1 1-1 2h4 0 4c2 0 5-1 7 0v1l-5 1h0v1c-2 0-3 0-5 1-7 2-14 5-20 9l-3 3c-3 0-3 1-5 2h0c1-3 2-5 4-7 2-1 4-3 6-5 1-1 6-4 6-5v-1c1-1 2-2 2-3 1-1 1-1 2-1 0 1 0 0 1 1h1c1-1 1-2 2-3v-1-1z" class="C"></path><path d="M418 684v1c-2 0-3 0-5 1-7 2-14 5-20 9l-3 3c-3 0-3 1-5 2h0c1-3 2-5 4-7 2 0 3-2 5-3 3-1 6-3 9-4 5-2 10-1 15-2z" class="H"></path><path d="M379 714l1-2 3-3 3-4c0 1-1 2-1 2v1c0 1-1 2-1 3-1 1-1 1-1 2l3-3c1 1 1 1 1 2v-1l1 2-3 7-1 3v2 1c-2 3-1 4-2 6-1 3 0 6 0 9 1 1 1 4 0 6 0 5 4 14 7 18s8 7 11 12h0l-3-3c-3-3-6-5-9-9l-2 1-2-2c-9-12-12-28-9-42l3-9 1 1z" class="F"></path><path d="M375 722l3-9 1 1-3 10v12c2 8 4 15 8 23 1 2 2 4 4 6l-2 1-2-2c-9-12-12-28-9-42z" class="E"></path><path d="M687 401l1-5c0-1 0-2 1-3v2c1 9 5 18 12 25 4 3 9 5 14 7h1c2-1 3-2 6-2 1 1 1 1 1 2l1 2c4 0 10 0 14-3 3-2 4-5 6-8-2 6-4 10-9 13-2 1-5 2-7 3s-3 3-5 5c-2 1-3 1-5 1-1 0-1 0-2 1l1 1 1 1v1l1-1v1l2 1-1 1c0 2 0 3-1 4v7l-1 15-6-17c-1-1-1-4-2-5h-1 0v1-1c-1-1-1-1-1-2v-2c-2-4-4-7-5-10-2-5-3-8-8-11-4-7-7-15-9-23l1-1z" class="Q"></path><path d="M717 442l1 1v1l1-1v1l2 1-1 1c0 2 0 3-1 4l-2-8z" class="F"></path><path d="M686 402l1-1c2 9 8 20 16 25 3 2 10 6 14 5h1 1 1c1 1 2 1 3 3-1 1-1 2-2 2-3 2-8 2-11 2 1 2 3 4 4 6 1 3 2 10 1 13 0-2 0-5-1-7-1-1-3-2-3-3h-2v3h0v1-1c-1-1-1-1-1-2v-2c-2-4-4-7-5-10-2-5-3-8-8-11-4-7-7-15-9-23z" class="J"></path><defs><linearGradient id="L" x1="605.041" y1="625.351" x2="666.82" y2="639.348" xlink:href="#B"><stop offset="0" stop-color="#dad9da"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#L)" d="M628 577c6 2 10 5 15 9 1-2 3-5 5-7h3l2 2h-1c-1 0-3 0-4 2s-2 5-2 7c2 4 3 7 6 11 1 2 3 3 4 5s2 4 2 5c4 1 9 1 12 5 1 2 2 6 2 9l-2-5c-1-1-2-2-3-2-2-1-4-1-6 1s-3 6-3 9c0 8 4 14 6 21 1 5 1 9 1 13-2-5-2-13-8-16-2-1-4-1-6 0-2 0-4 5-5 7s-3 4-4 5c-13 16-34 26-54 21l1-1c3-1 9-2 12-2 1 1 0 1 1 1 1-1 1-3 2-4l12-3c13-4 23-10 30-23 6-13 8-30 4-43-4-12-12-18-22-23v-1c1 0 1 0 2-1 0 0-1 0-2-1h-1l1-1z"></path><path d="M704 327c2-2 4-4 6-7 1-2 2-4 2-6 2-5 4-10 3-15v-13c3 10 5 23 0 33-2 4-4 8-9 10-1 1-2 1-2 2h2c1 1 0 2 0 3s-1 2-1 3v4h0c-1 3-2 6-4 9 2 2 4 3 5 7 0 2 0 4-1 6-2 1-4 2-5 3-4 3-6 9-8 13l-3 11v5-2c-1 1-1 2-1 3l-1 5-1 1v-2c-1 0-1 1-1 2 0-1-1-2-2-3h0l1 9 2 5c1 4 4 9 5 13h0c-2-5-5-9-8-13v-1c0-3-2-6-2-9-1-4-1-9-1-13l-1-3c-1-1-1-2-2-3l-1 1v-2c-1-1-1-2-1-2l1 2 1-1-1-2 2-7h1l3-10 3-2 3-6 3-5c3-5 6-11 9-17l1-1c1-2 2-2 3-3v-1-1z" class="Q"></path><path d="M695 351l1 1c1 1 0 3-1 5h-2 0c1-2 1-4 2-6z" class="F"></path><defs><linearGradient id="M" x1="702.95" y1="354.913" x2="697.044" y2="343.935" xlink:href="#B"><stop offset="0" stop-color="#030301"></stop><stop offset="1" stop-color="#252528"></stop></linearGradient></defs><path fill="url(#M)" d="M705 337v4h0c-3 4-6 7-5 13 0 1 1 2 2 3-2 1-4 0-5 0h-2c1-2 2-4 1-5l-1-1c3-5 6-10 10-14z"></path><defs><linearGradient id="N" x1="687.59" y1="365.338" x2="698.512" y2="391.22" xlink:href="#B"><stop offset="0" stop-color="#0d0d0c"></stop><stop offset="1" stop-color="#312f34"></stop></linearGradient></defs><path fill="url(#N)" d="M686 400c-2-10-1-20 3-29 1-5 3-8 7-10 2 0 5-1 6-1 0 4-5 5-7 7-2 3-3 6-5 10 0 2-3 10-1 13h0v5-2c-1 1-1 2-1 3l-1 5-1 1v-2z"></path><path d="M689 356l2-3c0 3-4 9-5 12-1 5-3 10-4 14-1 7 0 14 1 20l1 9 2 5c1 4 4 9 5 13h0c-2-5-5-9-8-13v-1c0-3-2-6-2-9-1-4-1-9-1-13l-1-3c-1-1-1-2-2-3l-1 1v-2c-1-1-1-2-1-2l1 2 1-1-1-2 2-7h1l3-10 3-2 3-6 1 1z" class="G"></path><path d="M685 361l3-6 1 1c-2 6-5 12-7 19 0 2 0 7-2 9 0-8 2-15 5-23z" class="P"></path><path d="M682 363l3-2c-3 8-5 15-5 23v6l-1-3c-1-1-1-2-2-3l-1 1v-2c-1-1-1-2-1-2l1 2 1-1-1-2 2-7h1l3-10z" class="O"></path><path d="M562 766h4c2 0 5 1 7 2 2 2 4 3 5 7 3 3 6 6 8 10 1 2 1 4 3 6 2 5 1 14-1 19-3 7-9 14-16 18-2 1-5 2-7 2h-1c1-1 1-1 1-2 1-4 0-9 0-13l1-22c-1-2-1-4 0-6-1-1-1-3-1-5v-13c-1-1-1-2-3-2v-1z" class="J"></path><path d="M562 766h4c2 0 5 1 7 2 2 2 4 3 5 7-3-2-6-3-8-5-2-1-3-3-5-3v2c-1-1-1-2-3-2v-1z" class="O"></path><defs><linearGradient id="O" x1="447.221" y1="683.781" x2="400.673" y2="649.568" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#282729"></stop></linearGradient></defs><path fill="url(#O)" d="M416 631c1 1 1 2 2 3 0 1 0 2 1 3 0 0 1 1 2 0 3 7 8 12 15 15l5 3c1 0 1 0 2-1l9 27c-3 1-6 1-9 1h-20c-2-1-5 0-7 0h-4 0-4c0-1 1-1 1-2s0-2 1-3v-2-1c2-9 0-15-3-23-1-3-2-5-3-8l1-1c1 0 2-1 3-2 1 4 3 8 4 12h1c1 1 2 3 2 5h1 1c0-2-1-5 0-6 0-2-2-5-2-6l1-1h1l1-1c1 2 3 4 3 6h1c-1-3-3-5-4-8s-1-7-2-10z"></path><path d="M413 652c1 1 2 3 2 5h1 1c1 4 4 8 7 11 2 4 4 7 7 10h1-1c-7-4-12-11-15-19h0v2c1 5 3 12 0 17h0c0-5 0-9-1-13-1-5-2-9-3-13h1z" class="G"></path><path d="M417 644l1-1c1 2 3 4 3 6h1c1 2 1 4 3 6s4 4 6 5c-1 0-2 1-3 1l6 6c1 0 3 1 4 2h-3 0l-6-5c0 1 1 2 2 3 4 5 8 6 14 6v1c-2 0-5 0-7-1-2 0-4-2-6-3-7-6-11-12-15-19 0-2-2-5-2-6l1-1h1z" class="R"></path><path d="M417 644l1-1c1 2 3 4 3 6h1c1 2 1 4 3 6s4 4 6 5c-1 0-2 1-3 1-6-4-9-10-12-17h1z" class="C"></path><path d="M416 631c1 1 1 2 2 3 0 1 0 2 1 3 0 0 1 1 2 0 3 7 8 12 15 15l5 3c-3 0-6-2-9-3h0l5 4c-3-1-5-2-8-4h-1c1 2 9 7 9 7-2 1-3 0-4-1h-1l-2-2c-2-1-3-2-4-5l-3-2c1 2 4 7 6 8h0l2 1c1 1 4 2 6 4h-1c-2 0-3-1-5-2s-4-3-6-5-2-4-3-6c-1-3-3-5-4-8s-1-7-2-10z" class="M"></path><defs><linearGradient id="P" x1="575.861" y1="258.359" x2="599.653" y2="229.03" xlink:href="#B"><stop offset="0" stop-color="#090a0b"></stop><stop offset="1" stop-color="#2d2c2e"></stop></linearGradient></defs><path fill="url(#P)" d="M589 222h2l-1 1c2 2 6 3 9 4 2 2 6 7 8 7h1c1 0 1 2 1 2 1 7 1 14-3 21-4 5-12 7-18 8h-5v-1c-5-2-7-5-9-9l-1 2c1 1 2 1 1 3-1-1-3-3-4-5-3-4-3-10-2-15 1-4 3-6 5-9s7-7 11-8h0c2 0 3-1 5-1z"></path><path d="M585 234c2-2 3-3 6-3 4-1 8-1 11 2 2 2 5 5 5 8l-1 1h0 0l-1-1v-1c-2-3-5-6-9-7s-7-1-11 1z" class="U"></path><path d="M589 222h2l-1 1c2 2 6 3 9 4-8 0-12 0-17 5-5 4-6 10-7 16l-1-2c1-7 4-14 9-18 2-2 4-2 5-4h1v-2z" class="H"></path><path d="M605 254h1c2-3 3-9 3-12-1-1-1-3-1-4l-1-2h2c1 7 1 14-3 21-4 5-12 7-18 8h-5v-1c2 1 4 0 7 0l-4-4c-4-4-7-10-6-16 0-1 0-3 2-4-2 6-1 11 2 17 3 3 4 3 8 4h2c2-1 4-1 5-2 2-1 4-3 6-5z" class="G"></path><path d="M584 223h0c2 0 3-1 5-1v2h-1c-1 2-3 2-5 4-5 4-8 11-9 18l1 2h0v2h0l-1-1v-5h-1v4l1 7-1 2c1 1 2 1 1 3-1-1-3-3-4-5-3-4-3-10-2-15 1-4 3-6 5-9s7-7 11-8z" class="I"></path><path d="M573 248l-1-2c0-1-1-3 0-5h0c1-3 2-5 4-7l1-1c1-2 5-7 7-8 2 0 3-2 5-1h-1c-1 2-3 2-5 4-5 4-8 11-9 18l1 2h0v2h0l-1-1v-5h-1v4z" class="L"></path><path d="M585 234c4-2 7-2 11-1s7 4 9 7v1l1 1h0 0l1-1c1 5 0 9-2 13-2 2-4 4-6 5-1 1-3 1-5 2h-2c-4-1-5-1-8-4-3-6-4-11-2-17h0c1-1 1-1 1-2 1-1 2-2 2-4z" class="D"></path><path d="M606 242h0l1-1c1 5 0 9-2 13-2 2-4 4-6 5-1 1-3 1-5 2-2-1-4-2-5-3-4-2-6-6-6-10v1c1 2 2 5 4 6 2 2 5 3 8 3s6-2 7-4c4-3 4-7 4-12z" class="T"></path><path d="M647 495c3 8 10 12 13 18v2c3 2 2 7 2 11h0c-1 3-1 5-1 7-1 3-3 6-5 8-2 4-6 7-10 7-1 1-1 0-2 0h0c-4 1-7 2-11 1s-7-4-9-8h4l-2-1 1-1c-6-7-6-12-6-21 1-3 2-5 3-7h0c3-4 7-7 11-9 6-2 11 1 15 3h0c0-4-3-5-3-10z" class="J"></path><path d="M652 510h1c1 1 2 1 2 3v1h-2c-1-1-2-2-2-3l1-1z" class="O"></path><path d="M647 495c3 8 10 12 13 18v2c3 2 2 7 2 11h0c-1 3-1 5-1 7-1 3-3 6-5 8-2 4-6 7-10 7-1 1-1 0-2 0h0c-4 1-7 2-11 1s-7-4-9-8h4l-2-1 1-1c4 4 8 6 12 6 6 0 11-2 15-6 5-5 7-11 7-18 0-1 0-4-1-5-1-3-7-8-10-11h0c0-4-3-5-3-10z" class="H"></path><path d="M628 541c4 3 9 6 14 5 6-1 9-4 14-7-3 4-8 7-12 9h0c-4 1-7 2-11 1s-7-4-9-8h4z" class="D"></path><path d="M370 437l3 9-1 3v1l-1 1c-4 0-11 6-13 9-4 3-8 7-10 12-5 6-7 14-11 21l-2 1v1 2l-1-1-1 2h-1c-1 3-2 6-2 10l2-3h0l1 1c-2 11-1 22 0 34l1-1c2 7 3 13 8 19 1 2 3 4 4 5 1 2 1 5 2 7 1 3 2 5 4 8-2 9 15 16 19 23 2 2 3 5 4 7-2-1-2-3-3-4-1-2-3-4-4-5-4-4-10-6-13-11-5-6-7-14-11-20-2-4-5-4-7-6-2-1-3-4-4-5 2 12 6 24 4 36 0 5-1 9-3 14-2 4-5 8-6 12v11c1 5 0 10 0 15-1 15-6 30-6 45 0 13 3 25 3 38-1-1-1-8-2-10-1-14-4-29-1-43 1-10 4-19 5-30 0-4 0-8-1-12 0-5-1-11-1-15 1-4 2-7 4-11 4-8 5-14 5-23s-4-17-6-26c-3-11-5-21-6-32 1-12 0-23 5-34h1l9-24c-1-1 0-2 1-3l1-2 5-8c0 2-2 5-3 6l4-4c2-3 4-6 8-6 1 0 3-3 5-4 1-3 8-7 10-9 1 0 2-1 2-1z" class="N"></path><path d="M330 508l2-3h0l1 1c-2 11-1 22 0 34l3 11c1 3 2 5 2 7v-1c-7-9-10-38-8-49zm28-61c-1 3-7 8-9 11l6-5-5 6c-6 6-9 14-13 22v-1h-1c-3 7-5 14-7 22-1 4 0 9-2 12 0-6 1-12 2-18l-1-1s0 1-1 2h0c0 2 0 5-1 6h-1c0-1 0-2 1-3v-1-1-2c1-2 1-3 1-4h1l9-24c-1-1 0-2 1-3l1-2 5-8c0 2-2 5-3 6l4-4c2-3 4-6 8-6 1 0 3-3 5-4z" class="E"></path><path d="M337 468c0 3-2 7-3 10l-5 18-1-1s0 1-1 2h0c0 2 0 5-1 6h-1c0-1 0-2 1-3v-1-1-2c1-2 1-3 1-4h1l9-24z" class="T"></path><path d="M358 447c-1 3-7 8-9 11l6-5-5 6c-6 6-9 14-13 22v-1h-1c3-12 9-20 17-29 1 0 3-3 5-4z" class="K"></path><defs><linearGradient id="Q" x1="370.069" y1="469.931" x2="336.943" y2="466.341" xlink:href="#B"><stop offset="0" stop-color="#0f1011"></stop><stop offset="1" stop-color="#4f4d4e"></stop></linearGradient></defs><path fill="url(#Q)" d="M370 437l3 9-1 3v1l-1 1c-4 0-11 6-13 9-4 3-8 7-10 12-5 6-7 14-11 21l-2 1v1 2l-1-1-1 2h-1c1-6 3-12 5-17 4-8 7-16 13-22l5-6-6 5c2-3 8-8 9-11s8-7 10-9c1 0 2-1 2-1z"></path><path d="M368 438v3 1l-6 6h1c1 1 1 0 2 0v1c-2 1-4 3-6 5v-1c-4 2-5 6-9 6l5-6-6 5c2-3 8-8 9-11s8-7 10-9z" class="C"></path><defs><linearGradient id="R" x1="598.637" y1="326.291" x2="589.754" y2="321.131" xlink:href="#B"><stop offset="0" stop-color="#090807"></stop><stop offset="1" stop-color="#252628"></stop></linearGradient></defs><path fill="url(#R)" d="M598 310c7-7 11-16 14-25s3-17 3-25c0-13-1-25-4-37-1-4-2-8-3-11l-1-1-2-9 3 5h1l1-1 2-3 8 33h0c1 17 5 35 1 52l-1-2h1v-3-3h0c1-2 1-2 1-3-2 0-2 7-3 9l-1 1c-1 4-2 9-3 13-7 18-19 35-32 49-5 5-9 9-15 12-3 1-6 3-9 4l-3 1v1h1l2 2-8-1c-1 0-2 0-3-1h-4-2v-1c1 0 1-1 2-1v-1c-1 1-2 1-2 1-2 0-3 1-5 1v2 1 2h-1v-1l-4-1c-4 0-6-1-10-3-1-2-1-3-3-4l1-1h-2l1-1h2v-2l1-1 8 1 3-1 1-1v-3c2 0 5 1 7 0h1c5-1 9-4 11-8 5-7 5-14 4-22h0c1 1 1 2 2 3v-2h1c2 0 3 0 4 1l1-1h4l4 2c7 1 12 1 19-1l1-2 1-1c0-1 1-2 1-3v-1c-1 1-3 1-4 1v-1c2 0 3-1 5-1 1 0 1-1 2-2v-4-1z"></path><path d="M618 260v-9c1 1 1 3 1 4h0v-2c1 3 0 9 2 12l-3 17c-1 3-1 6-3 9v-1c1-2 1-2 1-3v-2c1-2 1-2 1-3 1-1 0-3 0-5 1-1 1-3 1-4 1-2 0-10 0-13z" class="J"></path><path d="M621 265c1 2 0 3 0 5 0 6-2 11-3 17-1 4-2 9-3 13-7 18-19 35-32 49-5 5-9 9-15 12-3 1-6 3-9 4l-3 1v1h1l2 2-8-1c-1 0-2 0-3-1h-4c3-1 6-1 9-3 3 0 7-3 11-5 2 1 2 0 4 0 5-2 9-6 13-9 11-12 21-25 28-40 2-6 3-12 6-18v-1c2-3 2-6 3-9l3-17z" class="G"></path><path d="M553 364c3 0 7-3 11-5 2 1 2 0 4 0-3 2-6 4-9 5-2 1-4 1-5 2h2v1h1l2 2-8-1c-1 0-2 0-3-1h-4c3-1 6-1 9-3z" class="B"></path><path d="M612 203l8 33h0c1 17 5 35 1 52l-1-2h1v-3-3h0c1-2 1-2 1-3-2 0-2 7-3 9l-1 1c1-6 3-11 3-17 0-2 1-3 0-5-2-3-1-9-2-12v2h0c0-1 0-3-1-4v9l-2-4c0-11-1-21-4-32l-1-8c-1-1-2-3-1-4 0-2 1-3 1-4l-1-2 2-3z" class="F"></path><defs><linearGradient id="S" x1="576.731" y1="327.674" x2="587.38" y2="334.724" xlink:href="#B"><stop offset="0" stop-color="#d1d0d2"></stop><stop offset="1" stop-color="#faf8f9"></stop></linearGradient></defs><path fill="url(#S)" d="M598 310c7-7 11-16 14-25s3-17 3-25c0-13-1-25-4-37-1-4-2-8-3-11l-1-1-2-9 3 5h1l1-1 1 2c0 1-1 2-1 4-1 1 0 3 1 4l1 8c3 11 4 21 4 32 0 6 1 12 0 18-6 31-24 64-50 84l-2 1c-4 2-8 5-11 5h-1c-1 0-3 1-4 1h-3c1-1 1-1 2-1 2 0 3-1 5-2v-1c7-2 13-6 18-12-1-1-1-1-1-2 1-2 1-4 1-7-1-2 0-4-1-6v-1c0-1 0-1 1-1v-1c-1-1-2-3-4-3h0l-1-1-1-2 1-1h4l4 2c7 1 12 1 19-1l1-2 1-1c0-1 1-2 1-3v-1c-1 1-3 1-4 1v-1c2 0 3-1 5-1 1 0 1-1 2-2v-4-1z"></path><defs><linearGradient id="T" x1="577.014" y1="326.111" x2="576.747" y2="338.128" xlink:href="#B"><stop offset="0" stop-color="#0f0e0f"></stop><stop offset="1" stop-color="#454345"></stop></linearGradient></defs><path fill="url(#T)" d="M569 324l4 2c7 1 12 1 19-1-4 5-9 11-14 16-3 2-5 6-8 8-1-1-1-1-1-2 1-2 1-4 1-7-1-2 0-4-1-6v-1c0-1 0-1 1-1v-1c-1-1-2-3-4-3h0l-1-1-1-2 1-1h4z"></path><path d="M569 324l4 2h-1v1c-1 0-2-1-2-1-1 0-2 0-3 1 0 1 0 0-1 1h0l-1-1-1-2 1-1h4z" class="F"></path><path d="M570 340c1 0 1 0 2 1 1-1 1-2 3-2h2 0l1 1c-1 1-1 1-3 1l-6 6c1-2 1-4 1-7z" class="M"></path><path d="M560 324c2 0 3 0 4 1l1 2 1 1h0c2 0 3 2 4 3v1c-1 0-1 0-1 1v1c1 2 0 4 1 6 0 3 0 5-1 7 0 1 0 1 1 2-5 6-11 10-18 12v1c-2 1-3 2-5 2-1 0-1 0-2 1h3c1 0 3-1 4-1h1c-3 2-6 2-9 3h-2v-1c1 0 1-1 2-1v-1c-1 1-2 1-2 1-2 0-3 1-5 1v2 1 2h-1v-1l-4-1c-4 0-6-1-10-3-1-2-1-3-3-4l1-1h-2l1-1h2v-2l1-1 8 1 3-1 1-1v-3c2 0 5 1 7 0h1c5-1 9-4 11-8 5-7 5-14 4-22h0c1 1 1 2 2 3v-2h1z" class="O"></path><path d="M559 341c0 2 0 3 1 5h0l-1 1 1 1c-2 5-6 8-10 12 0-1 0-1-1-1v-3c6-4 8-8 10-15z" class="K"></path><path d="M544 359c2-1 3-2 5-3v3c1 0 1 0 1 1l-1 1h3v1c-2 1-3 2-5 2-1 0-1 0-2 1h3c1 0 3-1 4-1h1c-3 2-6 2-9 3h-2v-1c1 0 1-1 2-1v-1c-1 1-2 1-2 1-2 0-3 1-5 1 0-1 0-2-1-4 0 0 0-1 1-1 2 0 5-1 7-2z" class="N"></path><path d="M544 359c2-1 3-2 5-3v3c1 0 1 0 1 1l-1 1-2 2c-1 0-2-2-3-4h0z" class="P"></path><path d="M521 358l3 1h1c2 1 5 1 8 1l2-2c0 2 0 4 1 5v-1c1 2 1 3 1 4v2 1 2h-1v-1l-4-1c-4 0-6-1-10-3-1-2-1-3-3-4l1-1h-2l1-1h2v-2z" class="H"></path><defs><linearGradient id="U" x1="546.092" y1="356.824" x2="563.354" y2="328.755" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#f1eff0"></stop></linearGradient></defs><path fill="url(#U)" d="M560 324c2 0 3 0 4 1l1 2-1 1v3h-1c-1 6-1 11-3 17l-1-1 1-1h0c-1-2-1-3-1-5l1-3h0c-1 1-2 3-2 5-3 6-8 11-14 13-2 1-5 2-8 1l-1-1h-1v-3c2 0 5 1 7 0h1c5-1 9-4 11-8 5-7 5-14 4-22h0c1 1 1 2 2 3v-2h1z"></path><path d="M560 324c2 0 3 0 4 1l1 2-1 1v3h-1c-1-3-2-5-3-7z" class="O"></path><path d="M563 331h1v-3l1-1 1 1h0c2 0 3 2 4 3v1c-1 0-1 0-1 1v1c1 2 0 4 1 6 0 3 0 5-1 7 0 1 0 1 1 2-5 6-11 10-18 12h-3l1-1c4-4 8-7 10-12 2-6 2-11 3-17z" class="C"></path><path d="M563 331h1v-3l1-1 1 1v3c0 1 0 5 1 7 0 1 0 4-1 6v3h-1c0 2 0 2-1 3h0c-1-1-1-2-1-2v-1 1l-3 3h0v-2-1c2-6 2-11 3-17z" class="B"></path><path d="M525 294c5-2 10-3 15-1 3 1 4 3 6 6 1 1 1 2 3 2h1l1-1v1c0 1 1 2 1 3l3 9h-2-1c2 3 3 6 5 10 1 8 1 15-4 22-2 4-6 7-11 8h-1c-2 1-5 0-7 0v3l-1 1-3 1-8-1-1 1v2h-2l-1 1h2l-1 1h-1c-1 1-2 1-2 2l-1 1h-1v-1c-3-2-7-3-11-3h-5 0c-1 1-2 1-3 1v-1-1h-1c-3-1-6 0-8-1h6l-1-1c1-1 1 0 2-1h1v-1c-1 0-1 0-2-1-2-1-3 0-5-2h0c-1 0-2-1-2-1h-1l-1-1h-2c-1 0-1-1-2-1s-1 0-2-1h-1s-1-1-2-1l1-1c7 1 14 1 22 1 0 0 0-1 0 0 2-1 3-1 5-2v-1h2c2 0 3-2 4-3 3-4 6-8 9-13 1-4 1-8 2-13l1-11v-5h0c2-2 4-4 5-6z" class="D"></path><path d="M525 294c5-2 10-3 15-1 3 1 4 3 6 6 1 1 1 2 3 2h1l1-1v1c0 1 1 2 1 3l3 9h-2-1c-2-2-3-4-6-6l-1-1c1-1 2-2 2-3h-1c-1-1-1-2-2-3-1-2-3-4-5-5-3-1-7 0-10 1-4 2-5 6-7 10-1 4-1 9-1 14h1-1l-1-1h0v-6-1c-2 3-1 9-1 13-1 2-1 3-2 5v-1c1-4 1-8 2-13l1-11v-5h0c2-2 4-4 5-6z" class="Q"></path><path d="M521 320c3-6 8-12 15-14 2-1 4-1 7-1l2 2c0 1 0 1 1 2h0c-1-1-2-1-3-1v1c2 2 3 4 4 6l-1 2c0-1-1-2-1-3 0-2-2-2-3-3s-3-1-4-1v-1c-3 0-5 0-8 1-2 1-5 4-6 7v1c2-2 3-3 5-4 2-2 5-4 7-4 4 1 6 2 8 5l2 3c-1 2-1 4-2 5h-1 0c1-2 2-3 1-5s-3-5-5-6c-2 0-5 0-7 1-4 2-8 7-10 11l-1 3v1c-1 6 0 14 4 19 2 3 5 5 8 6h1v3l-1 1-3 1-8-1-1 1v2h-2l-1 1h2l-1 1h-1c-1 1-2 1-2 2l-1 1h-1v-1c-3-2-7-3-11-3h-5 0c-1 1-2 1-3 1v-1-1h-1c-3-1-6 0-8-1h6l-1-1c1-1 1 0 2-1h1v-1c-1 0-1 0-2-1-2-1-3 0-5-2h0c-1 0-2-1-2-1h-1l-1-1h-2c-1 0-1-1-2-1s-1 0-2-1h-1s-1-1-2-1l1-1c7 1 14 1 22 1 0 0 0-1 0 0 2-1 3-1 5-2v-1h2c2 0 3-2 4-3 3-4 6-8 9-13v1c1-2 1-3 2-5 0-4-1-10 1-13v1 6h0l1 1h1-1z" class="P"></path><path d="M530 358l-1-1h-1c0-2 0-2 1-3 2 0 3 1 4 3l-3 1z" class="N"></path><path d="M516 356h1v3s-1 1-1 2v1l-2 1h-1c0-1 0 0 1-1 0-2 1-4 2-6z" class="Q"></path><path d="M487 353h3c1 0 3 1 4 1h1c1 0 1 0 2 1 1 0 3-1 5 1-1 1 0 2-1 4-2 0-4 0-6 1v-1h-1c-3-1-6 0-8-1h6l-1-1c1-1 1 0 2-1h1v-1c-1 0-1 0-2-1-2-1-3 0-5-2h0z" class="H"></path><path d="M517 329v1l-2 5h0c1 2 0 5-1 7v11h1v-3c0-1 0-2 1-3v-1-4c1 0 0 0 1-1 0 1 1 1 1 1 0 1-1 2-1 2-2 4-2 8-3 12-1-5-1-10 0-14v-2l-1-1c-1 5 1 8-2 13l-1 1c-3 0-6 0-9-1h-2s-1-1-2-1h-2l-2-2c1-1 2-1 4-1h0c2-1 3-1 5-2v-1h2c2 0 3-2 4-3 3-4 6-8 9-13z" class="N"></path><path d="M334 332l21 60 3 8h-1 0c-1 1-1 1-2 1-2 2-4 4-5 7-2 3-5 6-7 9l-5 7c-3 1-3 2-5 3h0v-2-1-2c1-3 2-6 2-9l-1-1v4l-1 1-1-1h0l-2-2c0 2 0 3-1 5-1-9-4-14-10-19v-1c-2-3-6-4-10-5l2-1v-1l-1-2c0-1-1-2-2-3v-2c0-1-5-1-6-1 0 0-1-1-1-2 1-1 3-2 5-1v-1c0-1-1-1-1-2 0-2 2-5 3-7 0-3 1-7 1-10 2-9 5-15 11-23 1 0 3 0 4 1 3-3 7-4 10-7z" class="J"></path><path d="M342 391h9c1 0 2 1 4 1l3 8h-1 0c-1 1-1 1-2 1 0-1 0-1-1-3h-5c0-2 0-4-2-6l-5-1z" class="B"></path><path d="M314 365c1 8 5 17 12 22 4 3 12 4 16 4l5 1c2 2 2 4 2 6h-11l2-2h3v-1c-5-3-10-3-14-4l-1-1c-3-1-4-2-6-4-2-3-4-5-5-8-2-2-3-3-3-5v-1c-1-1-1-1-1-2-1-3-1-3 1-5z" class="S"></path><path d="M320 338c1 0 3 0 4 1-7 7-11 14-10 23v3c-2 2-2 2-1 5 0 1 0 1 1 2v1c0 2 1 3 3 5 1 3 3 5 5 8 2 2 3 3 6 4l1 1c4 1 9 1 14 4v1h-3l-2 2-3-1c-10-2-17-7-21-16-2-3-3-6-5-8 1 4 2 8 4 12 1 2 3 4 4 6h-1l-3-3v-1 1l-1 1-4-4c0-1-5-1-6-1 0 0-1-1-1-2 1-1 3-2 5-1v-1c0-1-1-1-1-2 0-2 2-5 3-7 0-3 1-7 1-10 2-9 5-15 11-23z" class="N"></path><defs><linearGradient id="V" x1="353.606" y1="407.924" x2="315.578" y2="406.031" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#242325"></stop></linearGradient></defs><path fill="url(#V)" d="M308 385l4 4 1-1v-1 1l3 3h1c-1-2-3-4-4-6-2-4-3-8-4-12 2 2 3 5 5 8 4 9 11 14 21 16l3 1h11 5c1 2 1 2 1 3-2 2-4 4-5 7-2 3-5 6-7 9l-5 7c-3 1-3 2-5 3h0v-2-1-2c1-3 2-6 2-9l-1-1v4l-1 1-1-1h0l-2-2c0 2 0 3-1 5-1-9-4-14-10-19v-1c-2-3-6-4-10-5l2-1v-1l-1-2c0-1-1-2-2-3v-2z"></path><path d="M310 390c3 1 5 2 8 4 4 2 7 5 11 7 1 0 3 1 4 1 2 0 4 1 6 3l1 1-1 1c-2-2-3-3-5-4h-1l-1 1c-1-1-1-1-3 0v-1c-2-1-4-2-5-3-5-3-8-7-13-8l-1-2z" class="U"></path><path d="M333 403h1c2 1 3 2 5 4l1-1c1 2 1 3 0 5 0 2-1 7-3 8-1 1-2 1-2 1-1 1-1 3-2 4v-2c1-3 2-6 2-9-1-5-2-6-6-9 2-1 2-1 3 0l1-1z" class="L"></path><path d="M329 404c2-1 2-1 3 0l1-1c3 3 4 5 4 9-1 2-2 5-2 8-1 1-1 3-2 4v-2c1-3 2-6 2-9-1-5-2-6-6-9z" class="K"></path><path d="M311 392c5 1 8 5 13 8 1 1 3 2 5 3v1c4 3 5 4 6 9l-1-1v4l-1 1-1-1h0l-2-2c0 2 0 3-1 5-1-9-4-14-10-19v-1c-2-3-6-4-10-5l2-1v-1z" class="C"></path><path d="M309 394l2-1c6 3 11 7 16 12 2 2 3 4 4 6s1 4 1 5h0l-2-2c0 2 0 3-1 5-1-9-4-14-10-19v-1c-2-3-6-4-10-5z" class="S"></path><path d="M622 236c1-2 1-3 2-5 1 10 3 20 3 30v22s0 1-1 2c1 7 1 14 2 21v2c0 2 1 6 2 8l-2 8-1 6-9 40c-2 0-3 0-4 1h-15c-13 0-26 0-40-2l-2-2h-1v-1l3-1c3-1 6-3 9-4 6-3 10-7 15-12 13-14 25-31 32-49 1-4 2-9 3-13l1-1c1-2 1-9 3-9 0 1 0 1-1 3h0v3 3h-1l1 2c4-17 0-35-1-52l2 2v-2z" class="D"></path><path d="M583 349c0 4-3 6-6 9-2 1-4 3-7 3h-2c6-3 10-7 15-12zm38-32h1v-1-1-3l1 1h1l2 8c1 1 2 2 2 3l-1 6c-2-2-3-5-4-7s-1-4-2-6z" class="B"></path><path d="M621 317h1v-1-1-3l1 1h1l2 8-1-1c-1-1-1-1-1-2h-1v1c1 1 1 2 0 4-1-2-1-4-2-6z" class="M"></path><path d="M619 320c0-4-1-11 1-14 0 3 0 6 1 9v1c-2 2-1 7-1 10 1 3 2 5 3 8l-8 5h-2c2-3 4-6 5-8 1-1 1-2 1-3v-1c1-3 0-5 0-7z" class="C"></path><path d="M620 348h1v1l-31 20c-1 1-1 1-2 1 4-4 8-7 12-10 6-5 13-9 20-12z" class="I"></path><path d="M602 349l-22 17c2-2 4-5 6-7 5-5 10-9 14-14l2 1-6 6h1c2-1 3-3 5-3z" class="E"></path><path d="M602 346c5-5 13-11 14-18h1l2-8c0 2 1 4 0 7v1c0 1 0 2-1 3-1 2-3 5-5 8h2l-7 5-4 4-2 1h0c-2 0-3 2-5 3h-1l6-6z" class="B"></path><path d="M622 236c1-2 1-3 2-5 1 10 3 20 3 30v22s0 1-1 2c1 7 1 14 2 21v2c0 2 1 6 2 8l-2 8c0-1-1-2-2-3l-2-8h-1l-1-1v3 1 1h-1v-1-1c-1-3-1-6-1-9-2 3-1 10-1 14l-2 8h-1c-1 7-9 13-14 18l-2-1c5-3 10-8 13-12 5-8 5-18 5-27l3-18c4-17 0-35-1-52l2 2v-2z" class="I"></path><path d="M625 285v-3l1-1v4c1 7 1 14 2 21v2c0 2 1 6 2 8l-2 8c0-1-1-2-2-3l-2-8c0-5-1-9-1-13 0-5 1-10 2-15z" class="J"></path><path d="M622 236c1-2 1-3 2-5 1 10 3 20 3 30v22s0 1-1 2v-4l-1 1v3c-2 0-2 9-3 11v-3-2c0-1 0-4 1-5v-1c0-2 0-3 1-4 1-5 0-11 0-15v-7c-1-4-1-8-1-12h0c-1-2-1-4-1-6v-3-2z" class="C"></path><g class="B"><path d="M623 247c0 2 1 3 1 5 0 0 0 1 1 2v7 1c-1 1-1 2-1 4v-7c-1-4-1-8-1-12z"></path><path d="M626 281v-4c0-5-3-13 0-17l1 1v22s0 1-1 2v-4z"></path></g><path d="M425 219v2c5-1 8-1 13 0 2 0 3-1 5 0h0c1 1 2 1 4 2h0c1 0 1 0 1 1l1-1-1-1h1c6 4 12 11 14 19v7l-2-2c-2 1-2 2-3 3 0 5-1 7-4 10l-1 1h0c1 1 2 1 2 3l3 3c-2-2-5-3-7-3l-1 1h-1c-3 0-6 2-9 3-4 2-8 7-9 12v1l-2 2v1c1 0 2 0 2 1 1 0 1 1 2 1 0 6 2 11 3 16 0 0 0 1-1 2h0v-1c-1-1 0-1-1-2v1l-1 1-4 2-15-20c-3-4-5-9-6-13-1-1-1-2-1-3h0c-1 0-1 1-1 1-1-1-1-3-2-4 1-1 0-5-1-6-1-3-1-8 0-11h-1v-4-1h-1v-3h-1c0-2 0-3 1-5l1-1 4-5c5-5 10-8 17-9 0-1 2-1 2-1z" class="J"></path><path d="M429 283c1 0 2 0 2 1 1 0 1 1 2 1 0 6 2 11 3 16 0 0 0 1-1 2h0v-1c-1-1 0-1-1-2v1l-1 1c0-3-1-6-1-9-1-4-1-7-3-10z" class="K"></path><path d="M437 223c5 1 12 4 15 7l-2 1c-8-6-16-6-25-7 4-2 8 0 12-1z" class="B"></path><path d="M423 260c0-1-1-1-1-2-2-3-2-8-1-11 1-4 4-7 7-9 1 0 2-1 3 0-1 0-2 1-3 1-4 4-4 7-5 12v1c-1 1 0 2 0 4 1 3 3 5 5 6v1c-2-1-3-3-4-4l-1 1z" class="G"></path><path d="M425 221c5-1 8-1 13 0l9 3c-3 0-7-2-10-1-4 1-8-1-12 1s-7 3-10 5c0-1 0-1-1-1h0c3-3 7-5 11-7z" class="I"></path><path d="M403 248c1-6 3-11 7-16 1 6-3 13-4 19v10 3c1 1 1 3 1 3v1c-1 0-1 1-1 1-1-1-1-3-2-4 1-1 0-5-1-6-1-3-1-8 0-11z" class="D"></path><path d="M425 219v2c-4 2-8 4-11 7h0c1 0 1 0 1 1-2 1-3 2-5 3-4 5-6 10-7 16h-1v-4-1h-1v-3h-1c0-2 0-3 1-5l1-1 4-5c5-5 10-8 17-9 0-1 2-1 2-1z" class="L"></path><path d="M425 219v2c-4 2-8 4-11 7h0c1 0 1 0 1 1-2 1-3 2-5 3-4 5-6 10-7 16h-1v-4-1c2-7 9-15 15-20 2-1 4-1 6-3h0c0-1 2-1 2-1z" class="G"></path><path d="M438 221c2 0 3-1 5 0h0c1 1 2 1 4 2h0c1 0 1 0 1 1l1-1-1-1h1c6 4 12 11 14 19v7l-2-2c-2 1-2 2-3 3l-2 2h-1l1-2-2-1c-2-4-3-7-6-10h1c3 1 5 6 7 9h1c1-2 1-4 1-6-1-4-3-8-6-11s-10-6-15-7c3-1 7 1 10 1l-9-3z" class="N"></path><path d="M454 248l2 1-1 2h1l2-2c0 5-1 7-4 10l-1 1h0c1 1 2 1 2 3l3 3c-2-2-5-3-7-3l-1 1h-1c-3 0-6 2-9 3v-1c-1 0-3 0-4-1-5 0-9-1-13-5l1-1c1 1 2 3 4 4v-1c-2-1-4-3-5-6 0-2-1-3 0-4h0c2 4 4 7 8 9s10 0 14-2c5-2 7-6 9-11z" class="H"></path><path d="M456 251l2-2c0 5-1 7-4 10l-1 1-1 2c-1 0-2-1-4-1v-1l6-5c0-1 2-2 2-2v-2z" class="P"></path><path d="M453 260h0c1 1 2 1 2 3l3 3c-2-2-5-3-7-3l-1 1h-1c-3 0-6 2-9 3v-1c-1 0-3 0-4-1 4 0 8-3 12-4 2 0 3 1 4 1l1-2z" class="B"></path><path d="M358 400c1 2 2 5 2 7l4 11 1 4 3 7 2 8s-1 1-2 1c-2 2-9 6-10 9-2 1-4 4-5 4-4 0-6 3-8 6l-4 4c1-1 3-4 3-6l-5 8-1 2c-2 0-3 0-3 2l-1 1h-1-1c0 1-1 1-2 1-1 1-1 2-1 3-1 1-1 2-1 3h-1v2c0 1 0 1-1 2v1 1l-1 1c0 2-1 3-1 5v2l-1 1h0v1l-1 1v3l-1-1-3 19c-1 13-1 28 3 41 1 4 3 8 4 11 1 1 2 3 2 4s-1 2-1 3c-2 4-4 7-2 11v2 1c0-1-1-2-1-3-2-5 0-8 2-13-1-5-4-9-7-14-2-4-4-9-5-13-3-7-4-14-5-20-1-5-1-9-3-13-1 0-1-1-2-2h-2c-1 3 1 5 0 8 0 2-4 4-6 6-2 1-4 4-4 7 0 4 0 7 3 10l1 1h-1l-1-1c-3-3-4-7-3-11 0-6 5-9 9-12l-1-9 5-2c1-5 1-11 2-17s2-11 4-17v-2c1-3 0-6-1-8h1l1 3 1 4h2c-1 1-1 2-1 3 0 0 0 1-1 2v2h1c0-1 1-3 1-4 1-7 1-15 3-22 1-2 3-5 4-7 3-4 5-8 7-13 1-3 1-7 1-10h0v2 1c0 1 1 1 0 2 0 2 1 2 1 3v-8c1-2 1-3 1-5l2 2h0l1 1 1-1v-4l1 1c0 3-1 6-2 9v2 1 2h0c2-1 2-2 5-3l5-7c2-3 5-6 7-9 1-3 3-5 5-7 1 0 1 0 2-1h0 1z" class="N"></path><defs><linearGradient id="W" x1="335.214" y1="440.959" x2="338.945" y2="446.266" xlink:href="#B"><stop offset="0" stop-color="#474446"></stop><stop offset="1" stop-color="#5e5e5f"></stop></linearGradient></defs><path fill="url(#W)" d="M333 445c3-5 5-8 10-10v2l-1 1h1c-5 6-10 12-14 20-4 9-7 19-9 29-1 3-1 6-2 8 0 1 0 2-1 3 0 2 1 4-1 6v-3-1c0-1 0-3 1-4v-3-2h0c0-3 1-7 2-10l5-17c2-6 6-13 9-18v-1z"></path><defs><linearGradient id="X" x1="338.933" y1="498.35" x2="307.567" y2="459.15" xlink:href="#B"><stop offset="0" stop-color="#2a2326"></stop><stop offset="1" stop-color="#3d4142"></stop></linearGradient></defs><path fill="url(#X)" d="M336 434c1 4-3 7-3 11v1c-3 5-7 12-9 18l-5 17c-1 3-2 7-2 10-1 6-3 12-3 17v28c-1-1-1-6-1-8-1-8-2-16-1-25 0-8 3-17 4-26 2-5 4-11 5-16 2-4 3-7 4-10 3-6 7-12 11-17z"></path><defs><linearGradient id="Y" x1="354.09" y1="407.615" x2="345.558" y2="440.947" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#2a292c"></stop></linearGradient></defs><path fill="url(#Y)" d="M358 400c1 2 2 5 2 7l4 11-6 3v1l1 1c0 2-1 3-2 4-1 0-1 2-2 2-2 0-4 1-5 2-2 1-5 6-7 7h-1l1-1v-2c-5 2-7 5-10 10 0-4 4-7 3-11l1-1c0-2 0-2 1-3-1-1-2-1-3-2l3-4 5-7c2-3 5-6 7-9 1-3 3-5 5-7 1 0 1 0 2-1h0 1z"></path><path d="M343 435c2-2 5-6 8-7l-1 3c-2 1-5 6-7 7h-1l1-1v-2z" class="E"></path><path d="M358 422l1 1c0 2-1 3-2 4-1 0-1 2-2 2-2 0-4 1-5 2l1-3 1-1 6-5z" class="I"></path><path d="M358 400c1 2 2 5 2 7l-3 3c-1 2-3 4-6 6l-14 17c0-2 0-2 1-3-1-1-2-1-3-2l3-4 5-7c2-3 5-6 7-9 1-3 3-5 5-7 1 0 1 0 2-1h0 1z" class="E"></path><path d="M343 417c1 1 1 1 1 2-2 1-3 3-4 5 2-1 4-4 6-5s3-3 5-3l-14 17c0-2 0-2 1-3-1-1-2-1-3-2l3-4 5-7z" class="U"></path><path d="M358 400c1 2 2 5 2 7l-3 3c-1 0-2 1-3 2h-1l-1-1c-1-1-1-2-1-3h-1c1-3 3-5 5-7 1 0 1 0 2-1h0 1z" class="O"></path><defs><linearGradient id="Z" x1="309.745" y1="423.009" x2="326.725" y2="489.871" xlink:href="#B"><stop offset="0" stop-color="#1f1c1d"></stop><stop offset="1" stop-color="#4f5254"></stop></linearGradient></defs><path fill="url(#Z)" d="M333 417l1-1v-4l1 1c0 3-1 6-2 9v2 1 2h0c2-1 2-2 5-3l-3 4c1 1 2 1 3 2-1 1-1 1-1 3l-1 1c-4 5-8 11-11 17-1 3-2 6-4 10-1 5-3 11-5 16v-1-1h1l-1-2c0 1 0 2-1 4 0 2 0 2-2 4 0 0-1 3-1 4-2 7-4 16-3 24h0-1c-1-3-1-6-1-9 1-9 3-17 5-25 0-1 1-3 1-4 1-7 1-15 3-22 1-2 3-5 4-7 3-4 5-8 7-13 1-3 1-7 1-10h0v2 1c0 1 1 1 0 2 0 2 1 2 1 3v-8c1-2 1-3 1-5l2 2h0l1 1z"></path><path d="M329 419c1-2 1-3 1-5l2 2h0l1 1-3 12c0-1 0-1-1-2v-8z" class="U"></path><path d="M321 461c-1 5-3 11-5 16v-1-1h1l-1-2c0 1 0 2-1 4 0 2 0 2-2 4 1-6 3-12 5-18 1 1 0 2 0 3v1l1-1c0-2 1-4 2-5h0z" class="T"></path><path d="M335 428c1 1 2 1 3 2-1 1-1 1-1 3l-1 1c-4 5-8 11-11 17-1 3-2 6-4 10h0c-1 1-2 3-2 5l-1 1v-1c0-1 1-2 0-3 0-2 1-5 2-6 1-4 3-8 4-12 3-6 8-11 11-17z" class="P"></path><defs><linearGradient id="a" x1="350.897" y1="447.182" x2="344.902" y2="442.582" xlink:href="#B"><stop offset="0" stop-color="#272728"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#a)" d="M364 418l1 4 3 7 2 8s-1 1-2 1c-2 2-9 6-10 9-2 1-4 4-5 4-4 0-6 3-8 6l-4 4c1-1 3-4 3-6l-5 8-1 2c-2 0-3 0-3 2l-1 1h-1-1c0 1-1 1-2 1-1 1-1 2-1 3-1 1-1 2-1 3h-1v2c0 1 0 1-1 2v1 1l-1 1c0 2-1 3-1 5v2l-1 1h0v1l-1 1v3l-1-1c1-5 3-10 4-15 4-11 8-21 14-30 5-7 11-13 16-20 1 0 1-2 2-2 1-1 2-2 2-4l-1-1v-1l6-3z"></path><path d="M364 418l1 4c0-1-1-1-1-2h0c-1 2-2 2-3 3s-2 2-3 4h-1c1-1 2-2 2-4l-1-1v-1l6-3z" class="E"></path><path d="M333 468v-2c0-2 1-4 3-5l2-2c0 1 0 3 1 4l-1 2c-2 0-3 0-3 2l-1 1h-1z" class="C"></path><path d="M338 459c2-4 4-7 7-11 4-4 9-8 13-12 3-3 6-6 10-7l2 8s-1 1-2 1c-2 2-9 6-10 9-2 1-4 4-5 4-4 0-6 3-8 6l-4 4c1-1 3-4 3-6l-5 8c-1-1-1-3-1-4z" class="F"></path><path d="M412 293l1 1h0c-2-3-4-7-4-10v-1c0 2 1 3 2 4h1 1c0-1 0-1 1-3l15 20 4-2 1-1v-1c1 1 0 1 1 2v1h0c1-1 1-2 1-2 6 13 13 22 27 27 7 3 17 3 24-1 6-2 11-8 13-14 0-1 1-3 1-5l-1-4c-1-4-3-6-6-8-4-2-9-2-13-1l-3 1c3-2 6-4 10-4 6-1 11 1 16 5l6 2c1 0 4 1 5 1 3-1 8-5 10-6-1 2-3 4-5 6h0v5l-1 11c-1 5-1 9-2 13-3 5-6 9-9 13-1 1-2 3-4 3h-2v1c-2 1-3 1-5 2 0-1 0 0 0 0-8 0-15 0-22-1l-1 1c1 0 2 1 2 1h1c1 1 1 1 2 1s1 1 2 1h2l1 1h1s1 1 2 1h0c2 2 3 1 5 2 1 1 1 1 2 1v1h-1c-1 1-1 0-2 1l1 1h-6-3-11l-11 1c1 0 1-1 2-1h1c-2-2-4-1-6-2h-1c-2-1-4-1-5 0h-2c-1 0-2 1-3 1l-8 2c-2 1-8 2-10 1-1 0-1-1-1-1h-1l-1-3-1-4-2-7-6-19-3-10-6-24h1c1-1 1 0 3 0z" class="S"></path><path d="M513 311l-1 2c1 1 1 1 0 2h0v-4c-1-2 0-4-1-6v-2h2l1 2h0c-1 2-1 4-1 6z" class="N"></path><path d="M507 303l1-1h1v3l1 1v4c1 2 0 5 1 7l-2 2h-1c1-6 1-10-1-16z" class="Q"></path><path d="M513 303c1 0 1 0 2 1 1-1 3-2 4-3l1-1v5s-2 1-2 2c-2 2-3 4-4 7l-1-3c0-2 0-4 1-6h0l-1-2z" class="K"></path><path d="M504 300c2 0 2 2 3 3 2 6 2 10 1 16-2 5-3 9-7 14h-2v-1h0c2-3 4-6 5-10 4-7 3-15 0-22z" class="E"></path><path d="M499 332h0v1h2c-6 6-12 8-20 10-12 3-25-2-37-7h3l1 1h1c1 0 2 1 3 1-1-1-1-1-2-1-1-1-2-1-3-2v-1c2 1 7 4 9 4 4 1 8 3 13 4h1c4 0 8 0 13-1h1c6-2 10-5 15-9z" class="B"></path><defs><linearGradient id="b" x1="422.413" y1="333.999" x2="433.785" y2="328.403" xlink:href="#B"><stop offset="0" stop-color="#0c0c0e"></stop><stop offset="1" stop-color="#3c3a3a"></stop></linearGradient></defs><path fill="url(#b)" d="M414 317h2v1c0 2 1 3 2 4 8 8 16 13 25 19h0c-1 0-2-1-3-1v1h-1l-3-1c-1-2-5-3-7-4-3-2-7-4-10-7 0-1-1-1-2-2l-3-10z"></path><defs><linearGradient id="c" x1="463.94" y1="358.482" x2="463.55" y2="339.018" xlink:href="#B"><stop offset="0" stop-color="#2d3133"></stop><stop offset="1" stop-color="#494242"></stop></linearGradient></defs><path fill="url(#c)" d="M439 341h1v-1c1 0 2 1 3 1h0 1l13 6c11 4 23 8 34 11l1 1h-6-3-11l-11 1c1 0 1-1 2-1h1c-2-2-4-1-6-2h-1l1-1 2-1c0-1-1-2-1-2-6-3-14-6-20-9-1-2-2-2-3-3v-1l3 1z"></path><path d="M436 340l3 1c5 3 10 5 14 7l22 8c-5 0-11-2-16-3-6-3-14-6-20-9-1-2-2-2-3-3v-1z" class="H"></path><path d="M459 353c5 1 11 3 16 3 1 0 3 1 4 2 2 0 3 0 4 1h-11l-11 1c1 0 1-1 2-1h1c-2-2-4-1-6-2h-1l1-1 2-1c0-1-1-2-1-2z" class="L"></path><path d="M501 308l-1-4c-1-4-3-6-6-8-4-2-9-2-13-1l-3 1c3-2 6-4 10-4 6-1 11 1 16 5l-1 2 1 1c3 7 4 15 0 22-1 4-3 7-5 10-5 4-9 7-15 9h-1c-5 1-9 1-13 1h-1c-5-1-9-3-13-4l1-1h2c2 1 4 1 7 1 3 1 8 1 12 0h0c1 0 2 0 2-1h1c1 0 2 0 3-1h0c2-1 3-1 4-2h1l2-1 1-1c2-2 3-3 4-5h0c1 0 1-1 2-2h0l2-3v-1c1-1 1-1 1-2v-1c1-2 1-3 1-5-1-2-1-3-1-4v-1z" class="N"></path><path d="M518 307c0-1 2-2 2-2l-1 11c-1 5-1 9-2 13-3 5-6 9-9 13-1 1-2 3-4 3h-2v1c-2 1-3 1-5 2 0-1 0 0 0 0-8 0-15 0-22-1h-1l17-4 6-3c10-5 14-16 17-26 1-3 2-5 4-7z" class="C"></path><path d="M518 307c0 3-1 5-2 7l-3 10-2 5-1 2c-1 1-1 2-1 3 0 2-5 6-7 7-1 0-1 0-2 1h-2c-2 1-4 2-6 2l-1-1 6-3c10-5 14-16 17-26 1-3 2-5 4-7z" class="B"></path><defs><linearGradient id="d" x1="425.558" y1="350.701" x2="454.501" y2="340.604" xlink:href="#B"><stop offset="0" stop-color="#1c1d1e"></stop><stop offset="1" stop-color="#393636"></stop></linearGradient></defs><path fill="url(#d)" d="M417 327c1 1 2 1 2 2 3 3 7 5 10 7 2 1 6 2 7 4v1c1 1 2 1 3 3 6 3 14 6 20 9 0 0 1 1 1 2l-2 1-1 1c-2-1-4-1-5 0h-2c-1 0-2 1-3 1l-8 2c-2 1-8 2-10 1-1 0-1-1-1-1h-1l-1-3-1-4-2-7-6-19z"></path><path d="M419 329c3 3 7 5 10 7 2 1 6 2 7 4v1c1 1 2 1 3 3-6-2-13-5-18-9-1-2-1-4-2-6z" class="R"></path><path d="M423 345v-1c2 0 4 1 6 1 5 2 10 3 15 5l14 6-1 1c-2-1-4-1-5 0l-1-1c-8-2-15-7-23-8-2-1-3-2-5-3z" class="H"></path><path d="M423 345c2 1 3 2 5 3 8 1 15 6 23 8l1 1h-2c-1 0-2 1-3 1l-8 2c-2 1-8 2-10 1-1 0-1-1-1-1h-1l-1-3-1-4-2-7v-1z" class="M"></path><path d="M423 345c2 1 3 2 5 3h-2c-1 0 0 0-1-1l-1 1c2 2 4 2 6 2 4 1 7 4 10 5h0c-3 0-6-1-8-1-3-1-5 0-7-1l-2-7v-1z" class="F"></path><path d="M425 353c2 1 4 0 7 1 2 0 5 1 8 1h0c2 0 3 1 5 1v1c-2 0-4-1-5 0l-1 1h-1c-1 0-2 0-2-1l-2 1h0c-2-1-6-1-8-1l-1-4z" class="O"></path><path d="M412 293l1 1h0c-2-3-4-7-4-10v-1c0 2 1 3 2 4h1 1c0-1 0-1 1-3l15 20 4-2 1-1v-1c1 1 0 1 1 2v1h0c1-1 1-2 1-2 6 13 13 22 27 27 7 3 17 3 24-1 6-2 11-8 13-14 0-1 1-3 1-5v1c0 1 0 2 1 4 0 2 0 3-1 5v1c0 1 0 1-1 2v1l-2 3h0c-1 1-1 2-2 2h0c-1 2-2 3-4 5l-1 1-2 1h-1c-1 1-2 1-4 2h0c-1 1-2 1-3 1h-1c0 1-1 1-2 1h0c-4 1-9 1-12 0-3 0-5 0-7-1h-2l-1 1c-2 0-7-3-9-4v1c1 1 2 1 3 2 1 0 1 0 2 1-1 0-2-1-3-1h-1l-1-1h-3c-4-2-9-4-14-7-2-2-5-5-7-5-3-1-5-4-7-6v-1h-2l-6-24h1c1-1 1 0 3 0z" class="T"></path><path d="M433 322h1c3 0 6 4 9 6 4 1 8 6 12 7 2 0 5 1 8 2 4 1 8 1 12 0h3l7-2c1-1 2-1 3-2h0c1 0 1-1 2-1l1-1 1-1c1-1 0 0 2-1l2-2c-1 2-2 3-4 5l-1 1-2 1h-1c-1 1-2 1-4 2h0c-1 1-2 1-3 1h-1c0 1-1 1-2 1h0c-4 1-9 1-12 0-3 0-5 0-7-1h-2l-1 1c-2 0-7-3-9-4-5-3-11-7-14-12z" class="H"></path><path d="M412 287h1c2 5 5 9 8 13 4 5 8 11 13 14l4 2-2-7c-1-2-1-4-1-6h0c1 3 2 8 4 11 1 2 3 4 5 6 8 7 14 11 24 14 3 1 7 1 10 1-3 1-8 0-12-1-6-1-13-4-19-8-2-3-5-5-7-8-3-2-6-3-9-5-3-3-6-6-8-10-4-5-8-10-11-16z" class="L"></path><path d="M413 287c0-1 0-1 1-3l15 20 4-2 1-1v-1c1 1 0 1 1 2v1c0 2 0 4 1 6l2 7-4-2c-5-3-9-9-13-14-3-4-6-8-8-13z" class="P"></path><path d="M433 302l1-1v-1c1 1 0 1 1 2v1c0 2 0 4 1 6l-1 1-2-2 1-1v-1c-1-1-1 0-3-1 0 0-1-1-2-1l4-2z" class="T"></path><defs><linearGradient id="e" x1="420.557" y1="322.317" x2="432.541" y2="313.031" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#e)" d="M408 293h1c1-1 1 0 3 0 0 2 1 4 2 5 2 1 5 6 6 8-2-1-3-3-4-4h0l8 12h2l7 8c3 5 9 9 14 12v1c1 1 2 1 3 2 1 0 1 0 2 1-1 0-2-1-3-1h-1l-1-1h-3c-4-2-9-4-14-7-2-2-5-5-7-5-3-1-5-4-7-6v-1h-2l-6-24z"></path><path d="M655 366l7-28 1 1h0l-1 2c1 0 2 1 3 1h1c1 1 3 1 5 1h0c3 2 5 5 6 8h1c0 1 1 3 1 4 1 2 2 5 1 7l-1 1c-1 1-1 3-1 5v5l-2 7 1 2-1 1-1-2s0 1 1 2v2l1-1c1 1 1 2 2 3l1 3c0 4 0 9 1 13 0 3 2 6 2 9v1c3 4 6 8 8 13h0c-1-4-4-9-5-13l-2-5-1-9h0c1 1 2 2 2 3 0-1 0-2 1-2v2c2 8 5 16 9 23 5 3 6 6 8 11 1 3 3 6 5 10v2c0 1 0 1 1 2v1-1h0 1c1 1 1 4 2 5l6 17 1-15v-7c1-1 1-2 1-4 0 3 0 6 1 9s1 6 1 8v5l-1 6v7h-5c2 8 4 16 4 24 0 4 2 9 2 13v1h0l1 1c-1 7-3 16-7 22 1-4 2-7 3-11 0-5 0-12-1-17-2-10-7-22-16-28-5-3-9-3-14-2h-1c2-3 5-5 4-9-1-2-2-5-3-7-5-6-10-12-17-16-5-1-9-4-14-5-5-2-11-3-17-3h-4l1-4v-4l2-6 3-11c0-1 1-3 1-5l5-22 6-22c0-1 1-3 1-4z" class="N"></path><path d="M692 469c3 2 3 4 4 7 0 1 0 2 1 4 1 1 2 1 4 2 2 0 4 2 5 3h1s1 3 2 4c-6-3-10-6-16-8 1-5 1-7-1-12z" class="M"></path><defs><linearGradient id="f" x1="699.34" y1="466.534" x2="711.426" y2="471.863" xlink:href="#B"><stop offset="0" stop-color="#57555c"></stop><stop offset="1" stop-color="#7e7d7a"></stop></linearGradient></defs><path fill="url(#f)" d="M702 455h1l1 3c1 4 2 7 3 11 2 6 3 13 6 19h0l-8-17c-2-3-3-7-5-10l2-1 1-2c-1-1-1-2-1-3h0z"></path><defs><linearGradient id="g" x1="646.634" y1="448.421" x2="670.888" y2="435.202" xlink:href="#B"><stop offset="0" stop-color="#0c0f0f"></stop><stop offset="1" stop-color="#423e41"></stop></linearGradient></defs><path fill="url(#g)" d="M644 436h5l4 1h4c12 4 28 9 35 21-3-3-6-5-9-7-6-4-13-6-20-9h0-1c-1-1-1-1-2-1h-2l-1-1h-1c-4 0-7 0-10-1l-4 1v-1c1-1 2-2 2-3z"></path><path d="M637 440h5 0l4-1c3 1 6 1 10 1h1l1 1h2c1 0 1 0 2 1h1 0c2 2 6 3 9 4l-1 1v1h1-2c-1-1-2-1-3-1 0 0-1 0-2-1h-6l-2 1c-5-2-11-3-17-3h-4l1-4z" class="L"></path><path d="M637 440h5 0l4-1-1 1v1c1 1 2 1 4 1-2 1-7 1-9 1v1h-4l1-4z" class="B"></path><path d="M657 424h4c3 2 6 4 9 5 5 3 9 7 13 11 3 1 4 3 6 5 4 5 8 10 11 16 2 3 3 7 5 10h-1c-2-1-5-5-5-7-6-8-12-15-20-20-1-1-2-2-2-4l1-1v-1-1c0-1-2-2-3-3-5-4-12-7-18-10z" class="K"></path><path d="M678 437c5 4 9 9 12 14 3 3 7 8 9 13-6-8-12-15-20-20-1-1-2-2-2-4l1-1v-1-1z" class="B"></path><path d="M686 400v2c2 8 5 16 9 23 4 8 8 16 11 24l-1-1c-5-3-5-11-9-15-1 1 1 5 1 6 2 3 4 6 5 10 1 2 1 3 1 6h-1c-1-5-3-11-6-15l-3-6h0v-2l-2-6h0c-1-4-4-9-5-13l-2-5-1-9h0c1 1 2 2 2 3 0-1 0-2 1-2z" class="K"></path><path d="M643 414c3 1 9 1 12 3 4 2 7 3 10 5 1 0 4 2 4 2l12 8v3c3 2 7 6 8 10-2-2-3-4-6-5-4-4-8-8-13-11-3-1-6-3-9-5h-4l-12-4c-1 0-2 0-3-1 0-1 1-3 1-5z" class="R"></path><path d="M655 417c4 2 7 3 10 5 1 0 4 2 4 2l12 8v3c-4-3-7-6-10-8l-2-1c-2 0-3-1-5-2s-3-2-4-3-4-2-5-3v-1z" class="G"></path><path d="M643 414c3 1 9 1 12 3v1c1 1 4 2 5 3-3-1-7-3-10-2 1 0 2 1 2 1h-2-5c-1 0-2 0-3-1 0-1 1-3 1-5z" class="L"></path><path d="M671 419l4 3c2 1 3 2 4 3l1 1 1 1v1c1 1 2 1 3 2 1 2-1 0 1 1l1 2v-1-1c1 0 1 1 2 2l1 1c0 1 0 2 1 2 0-1-1-4-2-5v-1c1 1 1 2 2 3h1l2 1 3 6c3 4 5 10 6 15h0c0 1 0 2 1 3l-1 2-2 1c-3-6-7-11-11-16-1-4-5-8-8-10v-3l-12-8h2l1 1c1 0 2 1 3 1v1l7 5c1 1 3 2 4 4h1c-1-1-1-2-2-2-3-4-6-8-10-10l-1-1c0-1-1-1-1-2-1 0-2-1-2-2z" class="B"></path><path d="M681 432c6 6 11 11 15 18 1 3 3 4 5 6-2-5-5-10-5-16 3 4 5 10 6 15h0c0 1 0 2 1 3l-1 2-2 1c-3-6-7-11-11-16-1-4-5-8-8-10v-3z" class="E"></path><path d="M657 447l2-1h6c1 1 2 1 2 1 1 0 2 0 3 1h2-1v-1l1-1c8 3 16 9 21 16 2 2 3 5 5 8 3 4 6 8 8 12l1 3h-1c-1-1-3-3-5-3-2-1-3-1-4-2-1-2-1-3-1-4-1-3-1-5-4-7h0l-6-7c-3-3-6-5-10-7-1-1-3-3-5-3-5-1-9-4-14-5zm38-22c5 3 6 6 8 11 1 3 3 6 5 10v2c0 1 0 1 1 2v1-1h0 1c1 1 1 4 2 5l6 17 1-15v-7c1-1 1-2 1-4 0 3 0 6 1 9s1 6 1 8v5l-1 6v7h-5c0-4-1-8-3-12 0 3 1 6 2 8 0 5 1 10 2 16 1 3 2 6 2 9l-7-35c-2-7-4-12-6-18-3-8-7-16-11-24z" class="B"></path><defs><linearGradient id="h" x1="653.354" y1="417.539" x2="660.868" y2="442.547" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#2f2f32"></stop></linearGradient></defs><path fill="url(#h)" d="M642 419c1 1 2 1 3 1l12 4c6 3 13 6 18 10 1 1 3 2 3 3v1 1l-1 1c0 2 1 3 2 4-7-3-12-5-19-7h-3-4l-4-1h-5c0 1-1 2-2 3v1h0-5v-4l2-6 3-11z"></path><path d="M664 434v-1c-1 0-1-1-2-1-2-1-3-1-4-2 2-1 2-1 4 0 4 1 7 4 11 7-2-1-3-1-4-2h0-1l1 1h-2c-1-1-2-1-3-2z" class="B"></path><defs><linearGradient id="i" x1="638.518" y1="432.994" x2="652.016" y2="433.156" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#2f2e30"></stop></linearGradient></defs><path fill="url(#i)" d="M639 430l1 1 12 1h0v1l1 1-1 1h-3v1h0-5c0 1-1 2-2 3v1h0-5v-4l2-6z"></path><path d="M637 436h7c0 1-1 2-2 3v1h0-5v-4z" class="F"></path><path d="M652 432l6 1h0c1 0 4 0 4 1h2c1 1 2 1 3 2h2l-1-1h1 0c1 1 2 1 4 2l4 3c0 2 1 3 2 4-7-3-12-5-19-7h-3-4l-4-1h0v-1h3l1-1-1-1v-1z" class="E"></path><path d="M662 434h2c1 1 2 1 3 2-2 1-4 0-6-1v-1h1z" class="I"></path><path d="M652 432l6 1h0c1 0 4 0 4 1h-1v1l-1 1c-3 0-5-1-7 1l-4-1h0v-1h3l1-1-1-1v-1z" class="B"></path><path d="M660 436c-2-1-3-1-5-2v-1h2 1c1 0 4 0 4 1h-1v1l-1 1z" class="M"></path><path d="M648 392c2 0 3 0 4 1 2 0 3 1 4 2 6 5 10 12 15 17 1 3 3 4 5 6 1 2 0 2 1 3 1 2 3 3 3 5l-1-1c-1-1-2-2-4-3l-4-3c0 1 1 2 2 2 0 1 1 1 1 2l1 1c4 2 7 6 10 10 1 0 1 1 2 2h-1c-1-2-3-3-4-4l-7-5v-1c-1 0-2-1-3-1l-1-1h-2s-3-2-4-2c-3-2-6-3-10-5-3-2-9-2-12-3l5-22z" class="J"></path><path d="M652 393c2 0 3 1 4 2 6 5 10 12 15 17 1 3 3 4 5 6 1 2 0 2 1 3 1 2 3 3 3 5l-1-1c-1-1-2-2-4-3l-4-3h0c-3-2-4-5-7-7-2-2-4-4-7-6-2-3-6-5-7-8h0c4 1 5 4 8 5 2 0 2 1 3 0-2-3-4-6-7-8l-2-2z" class="E"></path><defs><linearGradient id="j" x1="659.086" y1="372.992" x2="677.038" y2="439.499" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#252428"></stop></linearGradient></defs><path fill="url(#j)" d="M654 370c0-1 1-3 1-4 2 1 3 3 4 5h0c0 2 4 9 4 10l2 2 18 29v1c3 4 6 8 8 13l2 6v2h0l-2-1h-1c-1-1-1-2-2-3v1c1 1 2 4 2 5-1 0-1-1-1-2l-1-1c-1-1-1-2-2-2v1 1l-1-2c-2-1 0 1-1-1-1-1-2-1-3-2v-1l-1-1c0-2-2-3-3-5-1-1 0-1-1-3-2-2-4-3-5-6-5-5-9-12-15-17-1-1-2-2-4-2-1-1-2-1-4-1l6-22z"></path><path d="M654 370c0-1 1-3 1-4 2 1 3 3 4 5h0c-2 2-2 6-3 8-1-3 0-7-2-9z" class="M"></path><path d="M656 379c1-2 1-6 3-8 0 2 4 9 4 10-1 1 0 1 0 3l-1 1v-1l-2-3c0-1-1-1-1-2 0 1-1 2-1 2 1 2 1 3 2 5l-4-7z" class="I"></path><path d="M681 415h0c1 0 1 0 1 1l5 5v-1c-1-1 0-1-1-1v-1c-1-2-2-3-3-4v-1c3 4 6 8 8 13l2 6v2h0l-2-1c-3-6-7-11-11-16h0 2l-1-2z" class="S"></path><defs><linearGradient id="k" x1="667.161" y1="392.295" x2="664.017" y2="394.423" xlink:href="#B"><stop offset="0" stop-color="#625f62"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#k)" d="M660 386c-1-2-1-3-2-5 0 0 1-1 1-2 0 1 1 1 1 2l2 3v1c1 3 3 6 5 10 3 5 6 10 9 14 1 2 3 5 5 6h0l1 2h-2 0c-1 0-3-3-3-4-7-8-12-17-17-27z"></path><path d="M663 381l2 2 18 29v1 1c1 1 2 2 3 4v1c1 0 0 0 1 1v1l-5-5c0-1 0-1-1-1h0 0c-2-1-4-4-5-6-3-4-6-9-9-14-2-4-4-7-5-10l1-1c0-2-1-2 0-3z" class="K"></path><path d="M655 366l7-28 1 1h0l-1 2c1 0 2 1 3 1h1c1 1 3 1 5 1h0c3 2 5 5 6 8h1c0 1 1 3 1 4 1 2 2 5 1 7l-1 1c-1 1-1 3-1 5v5l-2 7 1 2-1 1-1-2s0 1 1 2v2l1-1c1 1 1 2 2 3l1 3c0 4 0 9 1 13 0 3 2 6 2 9l-18-29-2-2c0-1-4-8-4-10h0c-1-2-2-4-4-5z" class="F"></path><path d="M661 356l7 11 3 5c0 2 1 3 2 5v1 3c-1-1-2-2-2-3l-3-6-7-12c-1-2 0-3 0-4z" class="C"></path><path d="M661 356v-3h1l2 2 3 4c4 7 7 14 9 21l1 2-1 1-1-2s0 1 1 2v2l2 6h-1v1l-4-11v-3-1c-1-2-2-3-2-5l-3-5-7-11z" class="M"></path><path d="M665 383s1 0 2 1c0 1 1 1 1 2l1 1c1 1 1 2 2 3h1v-1c-1 0-1 0-1-1s0-1-1-2v-2c-1-2-3-3-3-6h0c1 1 1 2 2 3h0c1 1 1 3 2 3l4 9c1 1 1 3 1 5 1 1 2 3 3 4 0-1 0-2-1-3v-2h-1l-1-4 1-1v-1h1l-2-6 1-1c1 1 1 2 2 3l1 3c0 4 0 9 1 13 0 3 2 6 2 9l-18-29z" class="C"></path><path d="M671 343c3 2 5 5 6 8h1c0 1 1 3 1 4 1 2 2 5 1 7l-1 1c-1 1-1 3-1 5v5l-2 7c-2-7-5-14-9-21l-3-4c2-2 2-3 2-6v-1c1-2 1-2 3-2l2-3z" class="L"></path><path d="M666 349l2 3c-1 3 0 5-1 7l-3-4c2-2 2-3 2-6z" class="O"></path><path d="M673 364c1-2 1-2 0-3v-2c0-1 1 1 2 1 1 2 1 3 1 6v3h0 0c-1 0-1-1-1-2-1-1-2-2-2-3zm-2-21c3 2 5 5 6 8h1c0 1 1 3 1 4h-1v2 1h0c-1-1-2-3-3-4v-2c-1-1-1-2-2-2h0-1s-1-1-1-2l-2-2 2-3z" class="B"></path><path d="M666 349v-1c1-2 1-2 3-2l2 2c0 1 1 2 1 2h1c0 4 1 7 2 10-1 0-2-2-2-1v2c1 1 1 1 0 3l-3-6-2-6-2-3z" class="E"></path><path d="M620 574l1-1c1-1 1-1 1-2-1-2-1-2-1-3 1-1 0-2 2-3v1c0 1 0 3 1 4v1c0 2 0 4 2 5l2 1-1 1h1c1 1 2 1 2 1-1 1-1 1-2 1v1c10 5 18 11 22 23 4 13 2 30-4 43-7 13-17 19-30 23l-12 3c-1 1-1 3-2 4-1 0 0 0-1-1-3 0-9 1-12 2l-1 1c-5-1-9-3-13-5-1-1-2-1-2-2l1-6 16-55v4c1 4 3 11 7 13 1 1 0 1 1 0-4-5-6-11-7-17v-1c0-2 0-4 1-6 1-4 2-8 4-12v-2c0-1 0-2 1-3 0-2 0-2 1-3 0-1 1-1 1-2 1-1 1-1 2-1h1l2-2h1c1-1 2-1 3-1 2-1 4-2 7-3h1v-1h2 2z" class="J"></path><path d="M612 601l2-1c2 1 2 1 3 2l-1 1v-1h-1l-1 1c1 0 1 0 1 1h-1v2h-1c-1 1-2 1-2 2l-1 1v-1h-1l1-1c1-1 2-3 2-5h-1l-1-1h2z" class="D"></path><path d="M574 666c3 1 5 3 7 4-2 0-4 1-6 3v1c-1-1-2-1-2-2l1-6z" class="G"></path><path d="M592 607v-1c0-1 1-2 1-2 2 2 3 12 4 13v-2l1-1v2c1 1 2 3 3 4 0 2 2 3 3 4h0l-2 1v2c-5-5-7-9-9-15v-5h-1z" class="B"></path><path d="M608 627c1 0 0-1 1 0 2 1 6 0 8 0h1c3-1 6-3 7-6 1-2 2-4 2-6v-1c1-3 0-6 1-9 1 4 2 8 1 12v1c0 4-1 7-4 9h-1l-6 3c0-1 0-1-1-1 0-1-5 0-6 0s-2-1-3-1v-1z" class="L"></path><path d="M620 574l1-1c1-1 1-1 1-2-1-2-1-2-1-3 1-1 0-2 2-3v1c0 1 0 3 1 4v1c0 2 0 4 2 5l2 1-1 1h1c1 1 2 1 2 1-1 1-1 1-2 1v1h0c0 5 3 7 6 10s8 10 8 14c0-1-3-6-4-7-4-6-12-10-15-17 0-1 1-2 0-2v-1-2c-1-1-1-2-3-2z" class="U"></path><path d="M620 574c2 0 2 1 3 2v2 1c1 0 0 1 0 2-3-2-7-3-11-2-5 1-10 5-13 9-1 1-2 3-3 4v-2c0-1 0-2 1-3 0-2 0-2 1-3 0-1 1-1 1-2 1-1 1-1 2-1h1l2-2h1c1-1 2-1 3-1 2-1 4-2 7-3h1v-1h2 2z" class="H"></path><defs><linearGradient id="l" x1="590.765" y1="670.368" x2="597.984" y2="677.893" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#bab8b9"></stop></linearGradient></defs><path fill="url(#l)" d="M581 670c8 3 15 4 23 3-1 1-1 3-2 4-1 0 0 0-1-1-3 0-9 1-12 2l-1 1c-5-1-9-3-13-5v-1c2-2 4-3 6-3z"></path><path d="M592 607h1v5c2 6 4 10 9 15v-2l2-1c1 1 2 2 4 3v1c1 0 2 1 3 1s6-1 6 0c1 0 1 0 1 1l6-3c-2 3-6 5-10 6-5 1-9 0-13-4-7-6-8-13-9-22z" class="T"></path><path d="M602 627v-2l2-1c1 1 2 2 4 3v1c1 0 2 1 3 1s6-1 6 0c1 0 1 0 1 1l-3 1c-5 0-9-1-13-4z" class="F"></path><path d="M399 169h80c1 1 0 3 0 5-1 1-3 1-5 1l-11 2c-11 2-22 5-31 10v1c3 1 5 3 7 6v3 1c1 1 1 1 1 2 0 7 1 12-5 16 0 1-1 1-2 2-1-1-1-1-2-1-2 0-4 1-6 1v1s-2 0-2 1c-7 1-12 4-17 9l-4 5-1 1c-1 2-1 3-1 5h1v3h1v1 4h1c-1 3-1 8 0 11 1 1 2 5 1 6 1 1 1 3 2 4 0 0 0-1 1-1h0c0 1 0 2 1 3 1 4 3 9 6 13-1 2-1 2-1 3h-1-1c-1-1-2-2-2-4v1c0 3 2 7 4 10h0l-1-1c-2 0-2-1-3 0h-1l6 24 3 10 6 19 2 7 1 4 1 3h1s0 1 1 1c2 1 8 0 10-1l8-2c1 0 2-1 3-1h2c1-1 3-1 5 0h1c2 1 4 0 6 2h-1c-1 0-1 1-2 1l11-1h11 3c2 1 5 0 8 1h1v1 1c1 0 2 0 3-1h0 5c4 0 8 1 11 3v1h1l1-1c0-1 1-1 2-2h1c2 1 2 2 3 4 4 2 6 3 10 3l4 1v1h1v-2-1-2c2 0 3-1 5-1 0 0 1 0 2-1v1c-1 0-1 1-2 1v1h2 4c1 1 2 1 3 1l8 1c14 2 27 2 40 2h15c1-1 2-1 4-1l9-40 1-6 2-8c-1-2-2-6-2-8 1-2 1-5 1-7 1 2 1 3 3 5 5-33 13-74-9-103-11-15-28-23-46-26-3-1-8-1-10-2-1-1-1-4 0-5 0 0 0-1 1-1h4 10 44 116 84c1 2 0 3 0 4-1 2-23 2-27 2-8 0-16 1-24 2-17 3-34 10-48 19-16 11-28 26-37 44-6 12-9 26-13 40l-15 58-7 28c0 1-1 3-1 4l-6 22-5 22c0 2-1 4-1 5l-3 11-2 6v4l-1 4-15 57-3 9c-1 4-3 9-4 13l-10 37c-2 6-5 14-5 20l-1 1 1 1h0c0 1-1 1-1 2-1 1-1 1-1 3-1 1-1 2-1 3v2c-2 4-3 8-4 12-1 2-1 4-1 6v1c1 6 3 12 7 17-1 1 0 1-1 0-4-2-6-9-7-13v-4l-16 55-1 6-3 8-6 25-1 2c-5 14-10 28-12 41l-1 2-2 8c1 2 1 3 1 5v2h-1l-1 2-2 1-1 4-5 17c-1 3-2 6-2 8l1 1-1 1h0c-2 2-2 5-3 7l-8 28 4 10c-2-2-3-5-5-7l-6 22-3 13-13 56c-2-7-2-14-3-21l-10-64-2-12c-1-1-1-3-1-4s-1-1-1-1c1-4-4-21-5-26l-10-45-6-25-3-10-1-4c-1-2-1-5-1-6l-5-18-2-7-1-4-9-27-64-193-2-1-1-1c-2-2-2-1-4-2h-1c-1-1-3-1-5-1v-1l5-4 1-1v-1l1-3-3-9-2-8-3-7-1-4-4-11c0-2-1-5-2-7l-3-8-21-60-1-2-26-64-13-30c-4-7-7-14-12-20-17-25-46-36-74-39l-19-2-10-1c-1 0-3 0-4-1 0-1-1-2 0-4h224z" class="N"></path><path d="M629 301c1 2 1 3 3 5-1 3-1 7-2 10-1-2-2-6-2-8 1-2 1-5 1-7z" class="C"></path><path d="M373 446l6 15-2-1-1-1c-2-2-2-1-4-2h-1c-1-1-3-1-5-1v-1l5-4 1-1v-1l1-3z" class="F"></path><path d="M402 227v1h1c2-2 4-3 6-4-1 1-3 3-3 5l-4 5-1 1c-1 2-1 3-1 5h1v3h1v1 4h1c-1 3-1 8 0 11 1 1 2 5 1 6h-1c0-1 0-2-1-3v-1-1h-1l-1 1h0l-1-26v-5l3-3z" class="I"></path><path d="M349 303c-2-9-3-18-3-27v-10l10 31c-1-1-2-2-2-4l-1 1h0c-1 3-2 7-4 9z" class="Q"></path><path d="M338 174h2l-4 1-9 3c-2 4-2 10-3 14l-6-1c-1-1-3-2-4-4 0-2 0-3 1-5 3-4 12-6 17-7h1c2 0 4-1 5-1z" class="S"></path><path d="M400 261l1-1h1v1 1c1 1 1 2 1 3h1c1 1 1 3 2 4 0 0 0-1 1-1h0c0 1 0 2 1 3 1 4 3 9 6 13-1 2-1 2-1 3h-1-1c-1-1-2-2-2-4v1c0 3 2 7 4 10h0l-1-1c-2 0-2-1-3 0h-1c-1-1-2-4-2-6-3-9-5-17-6-26z" class="E"></path><path d="M403 265h1c1 1 1 3 2 4 0 0 0-1 1-1h0c0 1 0 2 1 3 1 4 3 9 6 13-1 2-1 2-1 3h-1-1c-1-1-2-2-2-4 0-1-2-4-2-6-2-4-3-8-4-12z" class="H"></path><path d="M641 175c8 1 17 2 23 6 3 1 6 5 7 8 0 2 0 3-2 5s-4 2-6 2c-4-3-3-8-5-11-2-5-12-8-17-10zM374 282c-2-4-4-9-6-14-5-13-10-29-8-43 2-9 4-17 10-24 7-9 18-14 30-15-10 7-22 13-28 24-10 22-5 49 2 71v1h0z" class="S"></path><path d="M374 282h0c5 10 9 20 15 30 4 6 8 10 10 16l12 37 19 61 40 131 18 63 1 4v1h0L374 282z" class="K"></path><defs><linearGradient id="m" x1="427.606" y1="396.388" x2="334.662" y2="379.104" xlink:href="#B"><stop offset="0" stop-color="#babab9"></stop><stop offset="1" stop-color="#eceaea"></stop></linearGradient></defs><path fill="url(#m)" d="M349 303c2-2 3-6 4-9h0l1-1c0 2 1 3 2 4l62 205h-1l-68-199z"></path><path d="M426 507c-2-4-3-9-4-14l-5-13-26-87-6-19-16-50-12-35c-3-10-5-23-12-32-5-6-13-8-19-13-2-3-4-5-3-10 1-1 1-1 3-2 2 1 4 6 6 8 3 3 6 4 9 5 1-3 2-7 2-11 1-6 3-15 0-21-3-4-7-6-11-8v-1c3-3 4-7 6-12 1-4 2-9 6-10 3-1 6 1 8 3l11-6c4-2 9-4 13-4h0v1h-3c-6 3-12 6-16 12v1c-9 13-8 29-7 45 1 14 3 28 7 42l30 103 40 128h-1z" class="P"></path><path d="M399 169h80c1 1 0 3 0 5-1 1-3 1-5 1l-11 2c-11 2-22 5-31 10v1c3 1 5 3 7 6v3 1c1 1 1 1 1 2 0 7 1 12-5 16 0 1-1 1-2 2-1-1-1-1-2-1-2 0-4 1-6 1v1s-2 0-2 1c-7 1-12 4-17 9 0-2 2-4 3-5-2 1-4 2-6 4h-1v-1l-3 3v5h-1c-1 1-2 3-2 5-3 7-4 13-3 20v7c1 2 1 3 1 5l-3-26c-3-17 0-34 10-47 11-16 31-23 49-26 3-1 10 0 13-2l1-1c-6-1-12 0-17 0h-33c-4 0-10 0-14-1h-1z" class="K"></path><path d="M399 230c1-4 2-9 3-13 5-13 16-23 28-29 2 1 3 4 5 6l1 1c2 1 2 2 3 3s1 1 1 2c0 7 1 12-5 16 0 1-1 1-2 2-1-1-1-1-2-1-2 0-4 1-6 1v1s-2 0-2 1c-7 1-12 4-17 9 0-2 2-4 3-5-2 1-4 2-6 4h-1v-1l-3 3z" class="J"></path><path d="M436 195c2 1 2 2 3 3s1 1 1 2c0 7 1 12-5 16 0 1-1 1-2 2-1-1-1-1-2-1 3-2 5-5 6-9 2-4 1-9-1-13z" class="S"></path><path d="M402 227l3-3c3-3 7-6 11-6 3-1 7-1 9 0v1s-2 0-2 1c-7 1-12 4-17 9 0-2 2-4 3-5-2 1-4 2-6 4h-1v-1z" class="K"></path><defs><linearGradient id="n" x1="407.344" y1="449.019" x2="350.065" y2="461.889" xlink:href="#B"><stop offset="0" stop-color="#bab9b8"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#n)" d="M459 686c-3-8-5-16-7-24l-8-27-35-126-10-28c-2-7-5-14-8-21-2-4-5-7-7-10-5-7-7-13-10-20l-21-62-36-94-23-55c7 7 12 17 19 25 4 6 10 12 13 18 3 7 5 14 7 21l14 41 38 109 19 52 10 28 5 14 3 6h2l1-1c0-1-1-4-1-6l-5-17-2-7h1c3 7 4 16 6 24l6 19 6 28 14 65 10 44c0 1 1 3 0 4l1 2h0-1c0-2 0-1-1-2h0z"></path><path d="M427 507l14 44c2 6 3 13 5 18l2 2c1-2 0-4 0-5l-18-73-6-21c-1-2-2-6-2-8l10 34 16 56 10 38c15 55 29 111 39 168l5 27c2 17 3 34 3 52 0 10 1 20 0 30v1h0c0-2 0-3 1-5v-6l1-12c1-12 2-25 1-38-1-17-5-35-8-52l-3-16c0-2 0-4-1-6v-2h0v-1-2-1l-1-1c0-1 1-1 0-2v-1c-1-2-1-1-1-2v-2-1-1h-1v-2-2l-1-3-1-2v-1c0-2-3-9-2-10v-3h-1 0v-1c0-2 0-2-1-3v-1-1l1-1c1-1 1-1 1-3l1 4v2c0 1 1 2 1 3s1 2 0 4h1v3c0 1 1 2 1 3v1c1 2 2 6 2 8v3l1 1v1h0c0 1 0 2 1 3 0 1 0 3 1 4v1 3c1 1 1 1 1 2 0 2 1 3 1 5l1 1 3 16c0 4 2 8 2 12 2 11 5 22 6 32v13 10 1h0c0-2 0-3 1-5v-5l2-10 8-42c5-20 12-40 19-59l15-51 40-144 26-97 12-45c1 3-3 15-4 19l-14 52-3 16-23 96-6 28c-1 3-2 7-2 10l6-21 9-33 36-136 7-29 1-1h0v-1c1-1 1-1 1-2l3-13h0v-1c0-2 1-3 1-5 0-1 1-2 1-2 0-1 1-2 1-3 0-2 1-5 2-8 0-1 0-1 1-2v-1c0-1 0-2 1-3h0v-3l1-1v-2-1l1-3c0-1 1-2 1-3v-1h0v3h0c0 1 0 1-1 2v2h0c0 2 0 0-1 2 0 2 0 3-1 5 0 1 1 1 0 2v2c0 1 0 0-1 1v3c0 1-1 3-1 5v1l-1 1h0v3c-1 2-2 5-2 7v1h0c0 2-1 3-1 4v2l-1 1v1 2c0 1 0 0-1 1v1 2 1l-1 1v2 1 2l-1 1v1c0 5-2 12-4 18l-11 56c-4 16-8 31-10 46-1 4-3 14-1 17v1-1h1c-1 4-3 9-4 13l-10 37c-2 6-5 14-5 20l-1 1 1 1h0c0 1-1 1-1 2-1 1-1 1-1 3-1 1-1 2-1 3v2c-2 4-3 8-4 12-1 2-1 4-1 6v1c1 6 3 12 7 17-1 1 0 1-1 0-4-2-6-9-7-13v-4l-16 55-1 6-3 8-6 25-1 2c-5 14-10 28-12 41l-1 2-2 8c1 2 1 3 1 5v2h-1l-1 2-2 1-1 4-5 17c-1 3-2 6-2 8l1 1-1 1h0c-2 2-2 5-3 7l-8 28 4 10c-2-2-3-5-5-7l-6 22-3 13-13 56c-2-7-2-14-3-21l-10-64-2-12c-1-1-1-3-1-4s-1-1-1-1c1-4-4-21-5-26l-10-45-6-25c0-2 0-3 1-4v2l3 9c1 3 2 5 3 8s2 6 4 9v1c1 1 1 1 1 2 1-2-2-6-2-7-1-1-1-2-1-3-1-2 0-1 0-3h1l1 1 1 4v2c0 1 1 1 1 2v2h1l-3-9-6-23c-2-6-3-13-5-19l-3-12c-1-1-2-3-2-5l-1-1h0c1 1 1 0 1 2h1 0l-1-2c1-1 0-3 0-4l30 114c0-8-1-17-2-25-3-19-7-38-11-57l-23-96c-9-37-16-74-28-111h1z" class="U"></path><path d="M548 758c1 2 1 3 1 5v2h-1l-1 2-2 1 3-10z" class="B"></path><path d="M452 357c1-1 3-1 5 0h1c2 1 4 0 6 2h-1c-1 0-1 1-2 1l11-1h11 3c2 1 5 0 8 1h1v1 1c1 0 2 0 3-1h0 5c4 0 8 1 11 3v1h1l1-1c0-1 1-1 2-2h1c2 1 2 2 3 4 4 2 6 3 10 3l4 1v1h1v-2-1-2c2 0 3-1 5-1 0 0 1 0 2-1v1c-1 0-1 1-2 1v1h2 4c1 1 2 1 3 1l8 1c14 2 27 2 40 2h15c1-1 2-1 4-1l-4 14-1 7-2 5-5 21-4 15c-1 4-2 8-4 11l-45 167-2 7-6 21-1 1-15 62c-3-6-4-12-5-18-2-9-5-18-7-27l-4-12-5-17c-1-4-3-8-3-12-4-9-7-19-9-28l-2-4-11-38c0-1 0-2-1-3l-39-130-4-11-4-13c0-1-1-4-1-5l-2-5-2-9-3-9h1s0 1 1 1c2 1 8 0 10-1l8-2c1 0 2-1 3-1h2z" class="D"></path><path d="M529 587l3 19c-1 1 0 2-1 4v-1c-1-2-1-5-1-7v-1c0-3-2-2-4-3v-1c0-1 1-1 1-2v-2h1v-1c0-2 0-3 1-5z" class="Y"></path><path fill="#daaeaf" d="M528 577h1c0 4 1 7 2 10 0 2 0 3 1 4l12 47h1l-1 1c-1-2-2-5-3-7-4-9-6-17-9-26l-3-19-1-10z"></path><path fill="#db7e88" d="M526 393l1-2v42 18c0 4 0 7 1 11v8c0 4-1 11 1 15v9 14h-1c-1 1-2 2-2 3V393z"></path><path d="M533 377c1 1 1 2 1 4h1c0-1 0-1 1-2v-3c1 0 1 1 1 1l-1 5c0 5-1 10-1 15l-1 14h1v1c-2 6-1 14-2 20 0 8-1 15-1 23-1 4 0 9-1 13s-1 9-1 13l-1 1c-1-1 0-10 0-12h-1v-8c-1-4-1-7-1-11v-18-42-4c1-1 1-1 1-2 1-2 0-5 0-6h4v1c1-1 1-2 1-3z" class="J"></path><path d="M533 377c1 1 1 2 1 4h1c0-1 0-1 1-2v-3c1 0 1 1 1 1l-1 5c0 5-1 10-1 15l-1 14h1v1c-2 6-1 14-2 20 0 8-1 15-1 23-1 4 0 9-1 13s-1 9-1 13l-1 1c-1-1 0-10 0-12l2-28 1-16c0-2 1-8 0-9l-2 2-1-1 2-3c2-5 1-12 1-17v-5c-1-1 0-5 0-7v-6c1-1 1-2 1-3z" class="R"></path><path fill="#db7e88" d="M522 366c4 2 6 3 10 3 1 1 1 2 1 4h0l1 2h-1v1 1h0c0 1 0 2-1 3v-1h-4c0 1 1 4 0 6 0 1 0 1-1 2v4l-1 2h-1c-1 1 0 1-1 1v3h-1l-5 154c0 3 0 8-1 9-1-1 0-7-1-8l-2-2v-3-3h0c1-2 1-3 1-5l1 2h0l1-4c-1-1 0-4 0-5l1-15-1-31c1-5 2-10 2-15 1-10 1-20 1-30v-18h-1v2c-1-1 0-3-1-4v-8c-1-2-1-6-1-8 2-3 1-9 1-12-1-2 0-5 0-6v-1-1c0-1 1-1 1-2v-1h1c0-3 0-5-1-7 1-1 1-3 1-4l2-2v-3z"></path><path d="M517 537l-1 15-2-2v-3-3h0c1-2 1-3 1-5l1 2h0l1-4z" class="V"></path><path d="M517 405c2-3 1-9 1-12-1-2 0-5 0-6v-1-1c0-1 1-1 1-2v-1h1v41h-1v2c-1-1 0-3-1-4v-8c-1-2-1-6-1-8z" class="Z"></path><path d="M522 366c4 2 6 3 10 3 1 1 1 2 1 4h0l1 2h-1v1 1h0c0 1 0 2-1 3v-1h-4c0 1 1 4 0 6 0 1 0 1-1 2v4l-1 2h-1c-1 1 0 1-1 1v3h-1c0-2 0-4 1-6h0v-1c-1-1 0-3 0-4l1-5c-1-2-1-3-1-4s-1-1-1-2c0-2 0-4-1-6v-3z" class="F"></path><path d="M515 396v-6h1v2c0 2 0 3 1 5 0 3-1 6 0 8 0 2 0 6 1 8v8c1 1 0 3 1 4v-2h1v18c0 10 0 20-1 30 0 5-1 10-2 15l1 31-1 15c0 1-1 4 0 5l-1 4h0l-1-2-3-1s-1 1-1 2h-3c0-1 0-3 1-4l1-36c-1-4 0-9 0-12v-1-5c0-1 0-2-1-3-1-6 0-14 0-20v-6-13-4h1v-1c0 1 0 2 1 2v-10-7c0-2-1-3 1-5v3h1l1 1v-2c0-1 1-1 2-2h0v-16-1c0-1 0-1-1-2z" class="W"></path><path d="M509 479c-1-6 0-14 0-20l1 7v12l1 1 1 1c0 2-2 2-1 3 2 1 3 4 4 5 0 1 0 3-1 4s-1 2-2 3h0v-4l-2-1v10c-1-4 0-9 0-12v-1-5c0-1 0-2-1-3z" class="V"></path><path d="M513 418l1 1v-2c0-1 1-1 2-2v9h-1l-1 3c-1 1 0 3 0 4 0 4 0 9-1 13 0 2 1 3-1 5-1-2-1-5-1-7 1-1 1-1 1-2v-5-9-8h1z" class="Z"></path><path d="M512 449c2-2 1-3 1-5 1-4 1-9 1-13 0-1-1-3 0-4l1-3 1 23-1 16c0 1 1 5 0 6 0 1 0 1-1 1-2-3 0-9-1-12s-1-6-1-9z" class="a"></path><path d="M515 396v-6h1v2c0 2 0 3 1 5 0 3-1 6 0 8 0 2 0 6 1 8v8c1 1 0 3 1 4v-2h1v18c-1 1-1 5-1 7h-1v-4h0c-1 0-1 0-1 1-1 1-1 1-1 2l-1-23h1v-9h0v-16-1c0-1 0-1-1-2z" class="V"></path><path d="M505 538c1-1 2-1 4-2-1 1-1 3-1 4h3c0-1 1-2 1-2l3 1c0 2 0 3-1 5h0v3 3l2 2c1 1 0 7 1 8 1-1 1-6 1-9l1 28-1 59c-1 6 0 12-1 18l-4-12-5-17c-1-4-3-8-3-12-4-9-7-19-9-28 0-1 1-1 1-2s1-2 1-4h1c1-2 0-1 0-2l1-1v-2l1-1v-3c1-1 1-1 1-2v-3l1-1v-5c1-3 1-6 1-9v-1l1-1v-2h1l-1-1v-1l1-1s0-2-1-2v-5z" class="W"></path><path d="M507 582l1-1v-2c0-1 0-1 1-2 1 2 1 3 1 5l1 4c1 12 2 23 2 35v14c0 3 1 6 0 9l-5-17c-1-4-3-8-3-12 1-2 1-3 1-5 1-2 0-6 0-7 1-2 1-2 1-3-2-1-2-2-2-3 1-2 1-5 1-7 1-1 0-3 0-4 1-2 1-2 1-4z" class="D"></path><path fill="#1f0c0f" d="M507 582l1-1v-2c0-1 0-1 1-2 1 2 1 3 1 5l1 4c-1-1-2-1-3-1v4l-1 1c0 1 1 4 0 6v4c-2-1-2-2-2-3 1-2 1-5 1-7 1-1 0-3 0-4 1-2 1-2 1-4z"></path><path d="M505 538c1-1 2-1 4-2-1 1-1 3-1 4h3c0-1 1-2 1-2l3 1c0 2 0 3-1 5h0v3 3c-2 2-2 3-2 5 0 3 0 5-1 8l-1 6v13c0-2 0-3-1-5-1 1-1 1-1 2v2l-1 1c0 2 0 2-1 4 0 1 1 3 0 4 0 2 0 5-1 7 0 1 0 2 2 3 0 1 0 1-1 3 0 1 1 5 0 7 0 2 0 3-1 5-4-9-7-19-9-28 0-1 1-1 1-2s1-2 1-4h1c1-2 0-1 0-2l1-1v-2l1-1v-3c1-1 1-1 1-2v-3l1-1v-5c1-3 1-6 1-9v-1l1-1v-2h1l-1-1v-1l1-1s0-2-1-2v-5z" class="J"></path><path d="M505 538c1-1 2-1 4-2-1 1-1 3-1 4h3c0-1 1-2 1-2l3 1c0 2 0 3-1 5h0v3 3c-2 2-2 3-2 5 0 3 0 5-1 8l-1 6v13c0-2 0-3-1-5-1 1-1 1-1 2v2l-1 1v-14c-1-4 1-11-1-16h-2v-1l1-1v-2h1l-1-1v-1l1-1s0-2-1-2v-5z" class="F"></path><path d="M507 568s0-7 1-9v4h1l1-3v9 13c0-2 0-3-1-5-1 1-1 1-1 2v2l-1 1v-14z" class="Y"></path><path d="M508 540h3c0-1 1-2 1-2l3 1c0 2 0 3-1 5h0v3 3c-2 2-2 3-2 5 0 3 0 5-1 8l-1 6v-9l-1-11c0-3 1-6-1-9z" class="X"></path><path d="M495 503c0-2-1-7 1-9 0-1 1-3 1-4v-1l1-2c0-2 1-3 2-5h1 3c3 1 3 0 5 0h1v5 1c0 3-1 8 0 12l-1 36c-2 1-3 1-4 2v5c1 0 1 2 1 2l-1 1v1l1 1h-1v2l-1 1v1c0 3 0 6-1 9v5l-1 1v3c0 1 0 1-1 2v3l-1 1v2l-1 1c0 1 1 0 0 2h-1c0 2-1 3-1 4s-1 1-1 2l-2-4-11-38c0-1 0-2-1-3l1-1c1-1 2-2 3-4 3-6 3-14 4-20v3h3l1-16 1-1z" class="D"></path><path d="M495 503c0-2-1-7 1-9 0-1 1-3 1-4v-1l1-2c0-2 1-3 2-5h1v1 1c-2 1-2 2-3 4v1c-1 4-1 9-1 14l1 13c0 3-1 7-2 11h-2c2-8 1-16 1-24z" class="G"></path><path d="M500 493l-1-1v-3-1c1-1 1-2 1-2l2-2c0 1 0 1 1 2l1 1h1c1 2 0 45 0 51v5c1 0 1 2 1 2l-1 1h-1v-3h-2v-2-15c0-1 1-3 0-4v-2h0c0-5-1-11-1-16 0-3 0-8-1-11z" class="Y"></path><path d="M500 493c1 3 1 8 1 11 0 5 1 11 1 16h0v2c1 1 0 3 0 4v15 2h2v3h1v1l1 1h-1v2l-1 1v1c0 3 0 6-1 9v5l-1 1v3c0 1 0 1-1 2v3l-1 1v2l-1 1c0 1 1 0 0 2h-1c0 2-1 3-1 4s-1 1-1 2l-2-4 1-1 3-25c1-4 1-9 2-13v-1c-1-1-1-2-1-3 1-7 1-14 0-21l1-26z" class="D"></path><path d="M500 493c1 3 1 8 1 11 0 5 1 11 1 16l-1 24c-1 13-3 26-5 39l-1-1 3-25c1-4 1-9 2-13v-1c-1-1-1-2-1-3 1-7 1-14 0-21l1-26z" class="R"></path><path d="M494 504l1-1c0 8 1 16-1 24h2c0 5-2 10-2 15l1 1c1-1 2-1 3-1h1v-2c0 1 0 2 1 3v1c-1 4-1 9-2 13l-3 25-1 1-11-38c0-1 0-2-1-3l1-1c1-1 2-2 3-4 3-6 3-14 4-20v3h3l1-16z" class="F"></path><path d="M494 527h2c0 5-2 10-2 15l1 1h2 0-2-4-1c0-5 3-12 4-16z" class="Z"></path><path d="M482 542l1-1c1-1 2-2 3-4 3-6 3-14 4-20v3h3c0 6-1 11-3 17-1 3-1 6-4 8l-1 1-1-1h-1c0-1 0-2-1-3z" class="G"></path><path d="M452 357c1-1 3-1 5 0h1c2 1 4 0 6 2h-1c-1 0-1 1-2 1l11-1h11 3c2 1 5 0 8 1h1v1 1c1 0 2 0 3-1h0 5c4 0 8 1 11 3v1h1l1-1c0-1 1-1 2-2h1c2 1 2 2 3 4v3l-2 2c0 1 0 3-1 4 1 2 1 4 1 7h-1v1c0 1-1 1-1 2v1 1c0 1-1 4 0 6 0 3 1 9-1 12-1-2 0-5 0-8-1-2-1-3-1-5v-2h-1v6c1 1 1 1 1 2v1 16h0c-1 1-2 1-2 2v2l-1-1h-1v-3c-2 2-1 3-1 5v7 10c-1 0-1-1-1-2v1h-1v4l-1-1-2 1v1l-1 1c-2-1-2-1-3-2l1-2 1-2c1-2 1-6 0-8v-1c-2-4-5-9-6-13l-1-3c0 1 0 1-1 1l1-1c-1-1-1-2-2-2 0 1 1 1 0 2-4-4-10-8-16-10h-1c-3-1-7-2-11-1h0l-6 3v1l-15-12-2-1c-1-1-2-1-3-2l-3-1h-2-1c0-1-1-4-1-5l-2-5-2-9-3-9h1s0 1 1 1c2 1 8 0 10-1l8-2c1 0 2-1 3-1h2z" class="J"></path><path d="M472 387c3 0 7 1 9 3h-2c-2-1-5 0-8 0h0v-1l1-2z" class="D"></path><path d="M493 364h5c-1 2-1 4-1 6h-1 0l-1-1c-1-2-1-3-2-5z" class="X"></path><path d="M494 360h1v1 1c1 0 2 0 3-1h0c1 2 1 2 2 3v1l-2-1h-5l-3-1h0c2 0 3-2 4-3z" class="U"></path><path d="M480 396h-1c-6 0-14 1-20 4h0-1l1-1c4-2 8-4 12-5 3 1 6 1 10 2h-1z" class="R"></path><path d="M499 386v1h0c0 2-1 8 0 10v3 1h-2-1v-4-11h3z" class="b"></path><path d="M471 394c7-1 14-1 20 4 3 2 4 5 5 7-3-2-5-4-8-6h-1l-7-3h1c-4-1-7-1-10-2z" class="G"></path><path d="M496 385l-2-1c1-1 1-1 1-2-2-2-4-2-6-2-4-1-8-2-13-3 4-1 9-1 14-1 2 1 4 1 5 1l1-4v3c1 0 2 1 3 2 0 0 1 1 2 1l-2 1c-1 2-1 4 0 6h-3v-1z" class="D"></path><path d="M496 376c1 0 2 1 3 2 0 0 1 1 2 1l-2 1c-1 2-1 4 0 6h-3v-1-9z" class="X"></path><path d="M501 379c1 4 0 9 2 13 1 3 0 6 1 9 0 7-1 16 1 23 0 2 0 3 1 5s0 6 0 9c-1 1 0 2 0 3l-1 1c-2-1-2-1-3-2l1-2 1-2c1-2 1-6 0-8v-1c-2-4-5-9-6-13l-1-3h1v1l2 2 1-1v-5s-1-1 0-2v-2c0-3 0-5-2-7-1-2 0-8 0-10h0v-1c-1-2-1-4 0-6l2-1h0z" class="V"></path><path d="M483 359h3c2 1 5 0 8 1-1 1-2 3-4 3h0-7c-1 2-2 2-4 3l-5 2h-2c-1 0-2 0-3 1-1 0-2 0-3 1-4 2-7 4-11 7-2 1-4 1-6 3v1l1-1c1 0 2-1 3-1 0-1 1-1 2-1 0-1 2-1 3-1h0 1l1-1h0c1-1 1 0 1-1h2l2-1h1c-1 2-4 3-6 4-3 2-8 5-10 8-2 2-4 3-4 5v1l-2-1c-1-1-2-1-3-2l-3-1h-2-1c0-1-1-4-1-5h0c1 0 2 0 2-2h0c1-2 1-2 3-3l1-1h1l2-2c1 0 2-1 3-1 3-1 6-3 9-5l8-3h2 2c1-1 2-1 3-2h0-4c0-2 1-2 2-3l4-2h11z" class="O"></path><path d="M441 389l9-3c-2 2-4 3-4 5v1l-2-1c-1-1-2-1-3-2z" class="B"></path><path d="M436 381h0c1-2 1-2 3-3l1-1h1l2-2 1 2 1-1h2c1 1 2 0 3 0h0c-1 1-3 3-5 4h-3c-1 1-1 0-2 0s-1 0-2 1h-2z" class="C"></path><path d="M436 381h2c1-1 1-1 2-1s1 1 2 0h3l-3 2h1c2-1 4-3 6-3-2 3-7 8-11 9h-2-1c0-1-1-4-1-5h0c1 0 2 0 2-2z" class="D"></path><path d="M483 359h3c2 1 5 0 8 1-1 1-2 3-4 3h0-7c-4 1-8 2-13 3s-11 3-16 6c-1 0-6 3-7 3l-1-1c3-1 6-3 9-5l8-3h2 2c1-1 2-1 3-2h0-4c0-2 1-2 2-3l4-2h11z" class="Q"></path><path d="M452 357c1-1 3-1 5 0h1c2 1 4 0 6 2h-1c-1 0-1 1-2 1l11-1-4 2c-1 1-2 1-2 3h4 0c-1 1-2 1-3 2h-2-2l-8 3c-3 2-6 4-9 5-1 0-2 1-3 1l-2 2h-1l-1 1c-2 1-2 1-3 3h0c0 2-1 2-2 2h0l-2-5-2-9-3-9h1s0 1 1 1c2 1 8 0 10-1l8-2c1 0 2-1 3-1h2z" class="C"></path><path d="M459 366l1-1h3v1l-8 3c-3 2-6 4-9 5-1 0-2 1-3 1l-2 2h-1l-1 1c-2 1-2 1-3 3h0c0 2-1 2-2 2h0l-2-5 6-3c1 0 2-1 2-1 1 0 2-1 3-1 1-1 1-1 2-1l1-1c1 0 1 0 2-1 0 0 1-1 2-1l2-1h1c2 0 4-1 6-2z" class="B"></path><path d="M461 360l11-1-4 2c-1 1-2 1-2 3h4 0c-1 1-2 1-3 2h-2-2v-1h-3l-1 1c0-1-1-1-2-1-1-1-2 0-3 0-4 1-9 3-12 3-1 1-2 1-3 2 3-5 11-5 17-7-2 0-3 0-4 1l-16 4h-1c8-4 17-6 26-8z" class="P"></path><path d="M452 357c1-1 3-1 5 0h1c2 1 4 0 6 2h-1c-1 0-1 1-2 1-9 2-18 4-26 8-2 0-4 1-5 1l-3-9h1s0 1 1 1c2 1 8 0 10-1l8-2c1 0 2-1 3-1h2z" class="B"></path><path d="M498 361h5c4 0 8 1 11 3v1h1l1-1c0-1 1-1 2-2h1c2 1 2 2 3 4v3l-2 2c0 1 0 3-1 4 1 2 1 4 1 7h-1v1c0 1-1 1-1 2v1 1c0 1-1 4 0 6 0 3 1 9-1 12-1-2 0-5 0-8-1-2-1-3-1-5v-2h-1v6c1 1 1 1 1 2v1 16h0c-1 1-2 1-2 2v2l-1-1h-1v-3c-2 2-1 3-1 5v7 10c-1 0-1-1-1-2v1h-1v4l-1-1-2 1v1c0-1-1-2 0-3 0-3 1-7 0-9s-1-3-1-5c-2-7-1-16-1-23-1-3 0-6-1-9-2-4-1-9-2-13h0c-1 0-2-1-2-1-1-1-2-2-3-2v-3-3h1c0-2 0-4 1-6l2 1v-1c-1-1-1-1-2-3z" class="a"></path><path d="M503 392v-8c1 1 1 0 1 2 1-1 1-1 1-2h1c0 3 1 7 1 11l-1-2-1 1c-1 2-1 4-1 7-1-3 0-6-1-9z" class="Y"></path><path d="M509 378c1-1 1-1 0-2h0c1-2 1-2 2-2 2 0 4-1 5 1 0 0-1 0-1 1v1c0 1 0 3-2 5 1 1 1 1 1 2-1 0-1-1-2-2 0-1-2-3-3-4z" class="F"></path><path d="M513 418v-3-1l1-1c1-2 0-4 0-5l-1-1-1 1h0c0-3-1-7 0-10 0-2 0-5 1-7l1 1v5l1-1c1 1 1 1 1 2v1 16h0c-1 1-2 1-2 2v2l-1-1z" class="W"></path><path d="M498 364l2 1h0c4 4 6 8 12 8 2 0 4 0 5-2l1-1h1c0-1 0-2 1-3h0v4c0 1 0 3-1 4s-2 1-4 2v-1c0-1 1-1 1-1-1-2-3-1-5-1-1 0-1 0-2 2h0c1 1 1 1 0 2l-1-1c0 1 0 0-1 1l1-3-3-2h-1v3l-2 2c0-1-1-2-1-2v3h0c-1 0-2-1-2-1-1-1-2-2-3-2v-3-3h1c0-2 0-4 1-6z" class="B"></path><path d="M497 370l1 1h1v-5c2 2 4 4 6 7h-1v3l-2 2c0-1-1-2-1-2v3h0c-1 0-2-1-2-1-1-1-2-2-3-2v-3-3h1z" class="b"></path><path d="M498 361h5c4 0 8 1 11 3v1h1l1-1c0-1 1-1 2-2h1c2 1 2 2 3 4v3l-2 2v-4h0c-1 1-1 2-1 3h-1l-1 1c-1 2-3 2-5 2-6 0-8-4-12-8h0v-1c-1-1-1-1-2-3z" class="H"></path><path d="M519 362c2 1 2 2 3 4v3l-2 2v-4h0c-1 1-1 2-1 3h-1v-1l-2 2c-1-1-2-1-3-1-2 1-2 1-4 1l-1-1c-1-2 0-1-1-2-2-1-3-3-5-5 2 1 4 2 7 3 2 0 3-1 5 1v1l1-1h0 1 1c0-2 1-4 2-5z" class="R"></path><path fill="#1f0c0f" d="M504 401c0-3 0-5 1-7l1-1 1 2 2 13c0-1 0-1 1-2v7c1 2 0 6 1 7v7 10c-1 0-1-1-1-2v1h-1v4l-1-1-2 1v1c0-1-1-2 0-3 0-3 1-7 0-9s-1-3-1-5c-2-7-1-16-1-23z"></path><path d="M509 420c1 2 1 4 1 6l1 1v10c-1 0-1-1-1-2v1h-1c0-5-1-10 0-16z" class="a"></path><path d="M509 408c0-1 0-1 1-2v7c1 2 0 6 1 7v7l-1-1c0-2 0-4-1-6v-12z" class="Z"></path><path d="M435 388h1 2l3 1c1 1 2 1 3 2l2 1 15 12v-1l6-3h0c4-1 8 0 11 1h1c6 2 12 6 16 10 1-1 0-1 0-2 1 0 1 1 2 2l-1 1c1 0 1 0 1-1l1 3c1 4 4 9 6 13v1c1 2 1 6 0 8l-1 2-1 2c1 1 1 1 3 2l1-1v-1l2-1 1 1v13 6c0 6-1 14 0 20 1 1 1 2 1 3h-1c-2 0-2 1-5 0h-3-1c-1 2-2 3-2 5l-1 2v1c0 1-1 3-1 4-2 2-1 7-1 9l-1 1-1 16h-3v-3c-1 6-1 14-4 20-1 2-2 3-3 4l-1 1-39-130-4-11-4-13z" class="J"></path><path d="M461 426c7 3 13 12 17 18 0 2 2 3 3 4 6 13 10 28 12 42 1 5 0 9 1 14l-1 16h-3v-3-8c1-25-5-54-20-74l-9-9z" class="R"></path><path d="M439 401h3l1 1 3 2c2 1 3 3 5 4 1 0 2 0 3 1 3 3 9 7 10 11h0l-1 1c1 4 6 6 7 9 1 1 2 1 2 3l1 1c1 0 1 1 1 2 1 1 3 3 3 5l2 2c1 1 0 0 0 1l1 1c0 1 1 2 1 3-1-1-3-2-3-4-4-6-10-15-17-18-2-1-2-3-3-4-3-1-5-5-8-6l-3-3c-1 0-2-1-3-1h-1l-4-11z" class="F"></path><path d="M451 408c1 0 2 0 3 1 3 3 9 7 10 11h0l-1 1c1 4 6 6 7 9h0l-2-2c-4-2-6-6-9-9l-6-5c0-1 0-1 1-2 0-2-2-3-3-4z" class="O"></path><defs><linearGradient id="o" x1="492.418" y1="447.357" x2="471.642" y2="465.761" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#27272a"></stop></linearGradient></defs><path fill="url(#o)" d="M464 420c1 1 1 2 2 3l1 1h0 1l-1-1h1 0c2 0 3 0 4-1 10 12 17 26 22 41 2 5 3 11 6 16 1 1 2 2 4 3h-3-1c-1 2-2 3-2 5l-1 2v1c0 1-1 3-1 4-2 2-1 7-1 9l-1 1c-1-5 0-9-1-14-2-14-6-29-12-42 0-1-1-2-1-3l-1-1c0-1 1 0 0-1l-2-2c0-2-2-4-3-5 0-1 0-2-1-2l-1-1c0-2-1-2-2-3-1-3-6-5-7-9l1-1z"></path><path d="M435 388h1 2l3 1c1 1 2 1 3 2l2 1 15 12c8 8 16 17 22 28 8 13 11 28 17 43v4c-3-5-4-11-6-16-5-15-12-29-22-41-1 1-2 1-4 1h0-1l1 1h-1 0l-1-1c-1-1-1-2-2-3h0c-1-4-7-8-10-11-1-1-2-1-3-1-2-1-3-3-5-4l-3-2-1-1h-3l-4-13z" class="H"></path><path d="M436 388h2l3 1c1 1 2 1 3 2 1 2 2 2 2 4-2 0-2-1-4-1h0c-3-2-5-3-6-6z" class="I"></path><path d="M435 388h1c1 3 3 4 6 6h0c11 8 21 18 30 28-1 1-2 1-4 1h0-1l1 1h-1 0l-1-1c-1-1-1-2-2-3h0c-1-4-7-8-10-11-1-1-2-1-3-1-2-1-3-3-5-4l-3-2-1-1h-3l-4-13z" class="B"></path><path d="M461 403l6-3h0c4-1 8 0 11 1h1c6 2 12 6 16 10 1-1 0-1 0-2 1 0 1 1 2 2l-1 1c1 0 1 0 1-1l1 3c1 4 4 9 6 13v1c1 2 1 6 0 8l-1 2-1 2c1 1 1 1 3 2l1-1v-1l2-1 1 1v13 6c0 6-1 14 0 20 1 1 1 2 1 3h-1c-2 0-2 1-5 0-2-1-3-2-4-3v-4c-6-15-9-30-17-43-6-11-14-20-22-28v-1z" class="D"></path><path d="M500 475c3 3 5 3 9 4 1 1 1 2 1 3h-1c-2 0-2 1-5 0-2-1-3-2-4-3v-4z" class="X"></path><path d="M461 403l6-3h0c4-1 8 0 11 1h1c6 2 12 6 16 10 1-1 0-1 0-2 1 0 1 1 2 2l-1 1c1 0 1 0 1-1l1 3c1 4 4 9 6 13v1c1 2 1 6 0 8l-1 2 1-4c1-5-4-13-7-17 0 0-1 4-1 5h0c0-2 0-7-1-8-6-5-15-10-23-11h-11z" class="G"></path><path d="M537 366c2 0 3-1 5-1 0 0 1 0 2-1v1c-1 0-1 1-2 1v1h2 4c1 1 2 1 3 1l8 1c14 2 27 2 40 2h15c1-1 2-1 4-1l-4 14-1 7-2 5-5 21-4 15c-1 4-2 8-4 11l-45 167-2 7-6 21h-1l-12-47c-1-1-1-2-1-4-1-3-2-6-2-10h-1c-1-1-1-3-1-4 0-5-1-9-1-14v-28-20c0-1 1-2 2-3h1v-14-9c-2-4-1-11-1-15h1c0 2-1 11 0 12l1-1c0-4 0-9 1-13s0-9 1-13c0-8 1-15 1-23 1-6 0-14 2-20v-1h-1l1-14c0-5 1-10 1-15l1-5s0-1-1-1v3c-1 1-1 1-1 2h-1c0-2 0-3-1-4h0v-1-1h1l-1-2h0c0-2 0-3-1-4l4 1v1h1v-2-1-2z" class="J"></path><path d="M554 398c3-2 6-4 8-7h0 1l-10 13c0-1 0-1-1-1 1-2 2-3 2-5z" class="B"></path><path d="M561 386l4 1c-1 2-1 3-2 4h-1 0c-2 3-5 5-8 7l-2-1c3-4 6-8 9-11z" class="O"></path><path d="M532 455h0v7c0 1 0 1 1 2 0 4 0 8-1 12l-1 24v-2c-1-5-1-12-1-17 0-4 0-9 1-13s0-9 1-13z" class="E"></path><defs><linearGradient id="p" x1="550.793" y1="398.829" x2="549.455" y2="412.196" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#545357"></stop></linearGradient></defs><path fill="url(#p)" d="M552 397l2 1c0 2-1 3-2 5 1 0 1 0 1 1l-9 17-1-1c0-3 3-6 3-9 1-1 1-3 2-4 1-3 2-6 4-10z"></path><path d="M597 412c4 6 5 13 5 20-1 4-2 8-4 11 0-8 1-15-1-23 1-3 1-5 0-8z" class="H"></path><path d="M590 399c2 1 4 3 7 5l1 1 3 6c1 1 2 2 2 3v1c1 1 2 1 3 2l-4 15c0-7-1-14-5-20v-2l-7-11z" class="C"></path><path d="M590 399c2 1 4 3 7 5l1 1 3 6c1 1 2 2 2 3v1h-1c-1-1-1-3-2-4s-1-2-2-4h0c-1-1-1-1-1-2l-1 1c1 1 1 2 1 4h0l-7-11z" class="O"></path><defs><linearGradient id="q" x1="529.406" y1="552.95" x2="544.891" y2="549.181" xlink:href="#B"><stop offset="0" stop-color="#232123"></stop><stop offset="1" stop-color="#48484b"></stop></linearGradient></defs><path fill="url(#q)" d="M528 470h1c0 2-1 11 0 12l1-1c0 5 0 12 1 17v2c0 28 3 55 11 82 3 9 8 18 10 27l1 1-2 7c-1-2-1-6-2-8-2-7-6-14-8-21-3-9-5-19-8-29v-2c-1 0-1 1 0 2h0l-2 1v-3l-1 3v-9l-1-19c0-1-1-2-1-2-1-2 1-6-1-8-1 3 0 6-1 9v-20c0-1 1-2 2-3h1v-14-9c-2-4-1-11-1-15z"></path><path fill="#daaeaf" d="M526 511c0-1 1-2 2-3h1v24c0-1-1-2-1-2-1-2 1-6-1-8-1 3 0 6-1 9v-20z"></path><path d="M537 377c2 1 3 2 4 3h0c1 0 1 0 2 1 1 0 2 1 4 1h2l7 2h2l3 2c-3 3-6 7-9 11-2 4-3 7-4 10-1 1-1 3-2 4 0 3-3 6-3 9l1 1c-6 13-9 29-11 43-1-1-1-1-1-2v-7h0c0-8 1-15 1-23 1-6 0-14 2-20v-1h-1l1-14c0-5 1-10 1-15l1-5z" class="D"></path><path d="M537 377c2 1 3 2 4 3h0c1 0 1 0 2 1h-1c0 1 1 1 2 2h-1l-4-2v1h-3l1-5z" class="F"></path><path d="M536 382h3c-1 2-1 3-1 5-2 4 0 7-3 10 0-5 1-10 1-15z" class="B"></path><path d="M532 455c1-12 5-25 9-36 2-2 3-5 4-8 1 2-3 10-4 12-1 3-2 7-3 10h1l4-13 1 1c-6 13-9 29-11 43-1-1-1-1-1-2v-7z" class="R"></path><path d="M526 531c1-3 0-6 1-9 2 2 0 6 1 8 0 0 1 1 1 2l1 19v9l1-3v3l2-1h0c-1-1-1-2 0-2v2c3 10 5 20 8 29 2 7 6 14 8 21 1 2 1 6 2 8l-6 21h-1l-12-47c-1-1-1-2-1-4-1-3-2-6-2-10h-1c-1-1-1-3-1-4 0-5-1-9-1-14v-28z" class="D"></path><path d="M526 531c1-3 0-6 1-9 2 2 0 6 1 8 0 0 1 1 1 2l1 19v9l6 30c2 6 3 11 5 16h0c-3-5-4-11-6-16-1-3-2-6-2-9-1-3-1-7-2-10 0 5 1 9 1 14 1 5 2 10 4 15 2 3 2 9 4 14 1 5 3 11 4 17h0l-13-44c-1-3-2-6-2-10h-1c-1-1-1-3-1-4 0-5-1-9-1-14v-28z" class="H"></path><path d="M537 366c2 0 3-1 5-1 0 0 1 0 2-1v1c-1 0-1 1-2 1v1h2 4c1 1 2 1 3 1l8 1c14 2 27 2 40 2h15c1-1 2-1 4-1l-4 14-1 7-2 5-5 21c-1-1-2-1-3-2v-1c0-1-1-2-2-3l-3-6-1-1c-3-2-5-4-7-5l7 11v2c1 3 1 5 0 8l-1-2c-5-14-17-24-31-31l-4-1-3-2h-2l-7-2h-2c-2 0-3-1-4-1-1-1-1-1-2-1h0c-1-1-2-2-4-3 0 0 0-1-1-1v3c-1 1-1 1-1 2h-1c0-2 0-3-1-4h0v-1-1h1l-1-2h0c0-2 0-3-1-4l4 1v1h1v-2-1-2z" class="J"></path><path d="M599 371h15l1 1h-5v1c-6 1-11 1-17 1h-9c4-1 8 0 12-1h-2v-1c1 0 4 0 5-1z" class="R"></path><path d="M554 374h1c2 0 5 1 7 1 7 2 14 3 20 4l18 2c1 0 1-1 2 0h3c3 1 5 1 7 2l2 1-1 7c-1-1-1-2-2-3-2-1-4-1-5-1-1-1-1-1-2-1h-1-2c0-1-1-2-1-3h-2c-3 1-10 0-13-1l-6-2c-2 0-3-1-5 0l-2 1c-2 0-15-5-18-7z" class="U"></path><path d="M598 383c-2-1-5-1-7-1-3 0-6-1-8-2 2 0 5 1 8 1 2 0 5-1 7 0h2c1 0 1-1 2 0h3c3 1 5 1 7 2l2 1-1 7c-1-1-1-2-2-3-2-1-4-1-5-1-1-1-1-1-2-1h-1-2c0-1-1-2-1-3h-2z" class="O"></path><path d="M612 383l2 1-1 7c-1-1-1-2-2-3l1-1c-1 0-2-1-2-2h1l1-2z" class="D"></path><defs><linearGradient id="r" x1="611.538" y1="389.236" x2="599.395" y2="388.638" xlink:href="#B"><stop offset="0" stop-color="#0f0f10"></stop><stop offset="1" stop-color="#302e31"></stop></linearGradient></defs><path fill="url(#r)" d="M574 380c2-1 3 0 5 0l6 2c3 1 10 2 13 1h2c0 1 1 2 1 3h2 1c1 0 1 0 2 1 1 0 3 0 5 1 1 1 1 2 2 3l-2 5c-2-1-3-1-5-2s-5-1-7-2c-4-1-7-3-11-5l-4-1c-4-2-7-4-12-5l2-1z"></path><path d="M574 380c7 2 14 4 20 7 4 1 11 3 15 5h-1c-2 0-5-1-8-2-1 0-3-1-5-1l6 3c1 0 4 1 4 1 1 0 0 1 1 1-2-1-5-1-7-2-4-1-7-3-11-5l-4-1c-4-2-7-4-12-5l2-1z" class="K"></path><path d="M537 366c2 0 3-1 5-1 0 0 1 0 2-1v1c-1 0-1 1-2 1v1h2 4c1 1 2 1 3 1l8 1c14 2 27 2 40 2-1 1-4 1-5 1v1h2c-4 1-8 0-12 1-7 0-14-2-20-3l-1 1h-6c1 0 2 1 3 1-1 0 0 0-1 1h2l1 1h0c-2 0-5-1-7-1h-1c-1 0-2 0-3 1v1l-4-1-1 2c-2-1-7-2-9-4v-2-2-1-2z" class="U"></path><path d="M537 369c4 0 8 0 11 1h2 4c3 1 7 1 10 1l-1 1h-6c1 0 2 1 3 1-1 0 0 0-1 1h2l1 1h0c-2 0-5-1-7-1h-1c-1 0-2 0-3 1v1l-4-1-1 2c-2-1-7-2-9-4v-2-2z" class="D"></path><path d="M537 373c6-1 12-1 18 1h-1c-1 0-2 0-3 1v1l-4-1-1 2c-2-1-7-2-9-4z" class="M"></path><path d="M584 386l4 1c4 2 7 4 11 5 2 1 5 1 7 2s3 1 5 2l-5 21c-1-1-2-1-3-2v-1c0-1-1-2-2-3l-3-6-1-1c-3-2-5-4-7-5l-8-8c0-3 0-3-2-5 1-1 1 0 2 0h2z" class="J"></path><path d="M584 386l4 1-1 2 5 3c1 1 0 1 1 1l2 2 1 1 3 3v1s0 1 1 2h1l1 1h0c0 1 1 1 1 2v1h-1l-2-2h-2v1l-1-1c-3-2-5-4-7-5l-8-8c0-3 0-3-2-5 1-1 1 0 2 0h2z" class="B"></path><defs><linearGradient id="s" x1="576.58" y1="402.352" x2="581.98" y2="394.747" xlink:href="#B"><stop offset="0" stop-color="#bbbabc"></stop><stop offset="1" stop-color="#f5f3f3"></stop></linearGradient></defs><path fill="url(#s)" d="M532 369l4 1v1h1v2c2 2 7 3 9 4l1-2 4 1v-1c1-1 2-1 3-1 3 2 16 7 18 7 5 1 8 3 12 5h-2c-1 0-1-1-2 0 2 2 2 2 2 5l8 8 7 11v2c1 3 1 5 0 8l-1-2c-5-14-17-24-31-31l-4-1-3-2h-2l-7-2h-2c-2 0-3-1-4-1-1-1-1-1-2-1h0c-1-1-2-2-4-3 0 0 0-1-1-1v3c-1 1-1 1-1 2h-1c0-2 0-3-1-4h0v-1-1h1l-1-2h0c0-2 0-3-1-4z"></path><path d="M532 369l4 1v1 3c7 4 16 5 22 10h-2l-7-2h-2c-2 0-3-1-4-1-1-1-1-1-2-1h0c-1-1-2-2-4-3 0 0 0-1-1-1v3c-1 1-1 1-1 2h-1c0-2 0-3-1-4h0v-1-1h1l-1-2h0c0-2 0-3-1-4z" class="C"></path><defs><linearGradient id="t" x1="579.144" y1="382.978" x2="562.3" y2="382.018" xlink:href="#B"><stop offset="0" stop-color="#252324"></stop><stop offset="1" stop-color="#3d3d3f"></stop></linearGradient></defs><path fill="url(#t)" d="M546 377l1-2 4 1v-1c1-1 2-1 3-1 3 2 16 7 18 7 5 1 8 3 12 5h-2c-1 0-1-1-2 0 2 2 2 2 2 5-2-2-5-4-8-6-9-5-19-5-28-8z"></path></svg>