<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="90 84 850 884"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#2f2e2f}.C{fill:#2a2929}.D{fill:#4e4d4d}.E{fill:#171616}.F{fill:#3b3a3a}.G{fill:#353435}.H{fill:#111110}.I{fill:#4f4e4e}.J{fill:#474647}.K{fill:#585757}.L{fill:#1d1c1d}.M{fill:#222121}.N{fill:#191919}.O{fill:#252424}.P{fill:#090808}.Q{fill:#d0cece}.R{fill:#b0afaf}.S{fill:#7b7a7b}.T{fill:#434242}.U{fill:#c4c2c3}.V{fill:#908e90}.W{fill:#656464}.X{fill:#838282}.Y{fill:#1f1e1e}.Z{fill:#a09f9f}.a{fill:#979696}.b{fill:#dbdada}.c{fill:#716f70}.d{fill:#c8c7c7}.e{fill:#767475}.f{fill:#e3e3e2}.g{fill:#8a8989}.h{fill:#6a6969}.i{fill:#d3d2d3}.j{fill:#a9a7a8}.k{fill:#f2f2f2}</style><path d="M405 558l2-3c1 0 0 0 1 1s1 2 1 3c-2 0-3-1-4-1z" class="L"></path><path d="M558 345l3 5c-1 0-2 0-4-1 0-2 0-2 1-4z" class="J"></path><path d="M417 618l2-1 2 2v2 1-1c-2-1-2-2-4-2h-1l1-1z" class="M"></path><path d="M527 434l1-6c1 2 1 3 1 5l-1 4c0-1 0-2-1-3z" class="D"></path><path d="M207 212h8 1l4 2c-5 1-8-1-13-2z" class="E"></path><path d="M172 199h6l1 1v1h0-8v-1h1v-1z" class="W"></path><path d="M543 735l3-2c-1 4-1 5-4 8l-1 1h0c0-1 0-2 1-3v-1h0c1-2 1-2 1-3z" class="H"></path><path d="M448 630l3 8c0 1 0 2-1 3 0-1-1-2-1-4-1 0-1-1-1-1-1-2-1-4 0-6z" class="O"></path><path d="M744 366h1l-1 2c1 1 2 1 3 1-4 0-5 2-8 3 1-2 3-4 5-6z" class="H"></path><path d="M529 422l1 1-1 10c0-2 0-3-1-5v-5l1-1z" class="W"></path><path d="M207 190c4-1 9 0 13 0v1h-2l-14-1h3zm-14 29c0-1-1-2-1-4h0-2 0-1c0-1 1-1 1-2 2 0 3 0 4 1l1 2-2 3z" class="P"></path><path d="M483 713h2-1l-1 1v3s1 0 2 1l-1 1h-1c-1 0-2 0-3-1 1-1 1-2 1-3h0l2-2z" class="H"></path><path d="M495 735l3 8h0v1c0 1 0 1-1 3l-3-9h1v-2-1z" class="P"></path><path d="M590 630l-2-2 1-1-1-1c0-1-1-1-2-1h-1v-1l2-1 1 1h0l2 1v1l3 3-2 1h-1z" class="H"></path><path d="M415 611h2c1 1 0 1 1 3 0 0 1 2 2 2l-1 1-2 1c-2-1-2-5-2-7z" class="N"></path><path d="M373 523l1 4c0 2 1 3 2 4h1l1 1c1 0 3 1 4 2h-2c-1 0-2-1-2-1h-1c-1-1-1-1-2-1v-1c0 1 0 1-1 2v1-3l-2-1c0-1 0-2 1-3v-4z" class="M"></path><path d="M210 194c2 1 4 1 6 1l2-1 2 1c-1 1-1 2-1 3h-4c-1-1-3-2-5-4z" class="H"></path><path d="M243 331c2 0 6 0 8 1v1h-4c-2-1-5 1-7 0 1-2 1-2 3-2z" class="d"></path><path d="M373 542c3 0 4 1 6 2v1c-1 0-1 0-1 1l-16-1h0 4 6l1-3z" class="T"></path><path d="M430 615h3v1c1 0 1 0 2 1h-1c-1 1-2 1-3 1h-2l-1 1c-1 0-1 0-2-1v-1c1-1 2-2 4-2z" class="H"></path><path d="M732 419c-2-1-3-2-5-2v-1c1 0 2-1 2-1l2-2c1 1 2 1 3 2v2h0c1 1 1 1 1 2h-1-2z" class="D"></path><path d="M509 888l3 8c-1 1-2 1-2 2h0l-3-5 1-1c0-1-1-2-1-3s1-1 2-1z" class="J"></path><path d="M742 229l1-1c0-1 0-1 1-2l2-1c2-2 4-2 7-2h1c1-1 2-1 3-2l-1 3h-3-1c-1 1-1 1-2 1h0c-1 1-2 1-3 2-2 1-3 1-5 2z" class="P"></path><path d="M681 526c2-2 6-1 8-1 1 1 2 1 3 2v1c-4 0-10 0-12-1l1-1z" class="R"></path><path d="M800 236l4-4 2 2c2 0 2 0 3 1l-7 4h-1l1-2-2-1z" class="Q"></path><path d="M594 684h3c2 0 3 1 5 1h1 1-1 4c3 0 6 1 8 3l-21-4z" class="E"></path><path d="M696 488c5 0 10 2 15 4l1-1v1l-1 2c-2 0-2 0-3-1-3-2-7-2-11-3l-1-2z" class="N"></path><path d="M277 365l6 3h0l3 3 2 2 2 2v1l-2-1c-3 0-9-7-11-10z" class="B"></path><path d="M464 402c2 2 3 3 5 3s3 0 5-1h0l1-1c0 2 0 2-1 4-2 0-4 1-6 1l-2-1c-1-1-2-3-2-5z" class="i"></path><path d="M855 199c4 1 8 1 13 3h-7l-13-1c2-2 4-1 7-2z" class="B"></path><path d="M489 829h1 1v-1c2 4 3 9 5 13 1 1 1 3 1 5l-8-17z" class="K"></path><path d="M503 377l2-2c-1 3-2 6-1 8 0 2 2 4 4 5l1-2 1 1c-1 2-4 4-6 6 0-2 0-2 1-3l-1-1c0-3-2-5-2-8 1-2 1-3 1-4z" class="f"></path><path d="M156 201l6-2h1 6 3v1h-1v1h-7c-2 1-4 1-5 1l-1-1h-2zm395 133l5 8c0 1 2 2 2 3-1 2-1 2-1 4-2-2-2-3-3-5 0-1-1-1-1-2v-2c-1-2-2-4-2-6z" class="K"></path><path d="M537 431h2 0l-5 14-1-1v-3c0-1 1-1 1-2l3-8z" class="d"></path><path d="M628 674c-3-1-5-2-8-4 1-1 3-2 4-3 1 1 5 2 5 3-1 1-1 1-1 3v1z" class="D"></path><path d="M824 209c7-1 15-1 22 0-2 1-5 2-7 2l-1-1h-4c-4 0-8 0-12 1l-1-1c1 0 2-1 3-1z" class="Z"></path><path d="M762 389c1 0 2-1 3-2h1c1-1 8 1 10 1l6 3c-7 0-13-2-20-2z" class="G"></path><path d="M773 331h1c1 0 5-1 6 0h1 1l1 2h0-3c-2 1-4 2-7 2l-2-2 2-2z" class="E"></path><path d="M821 208l4-1c-1 1-1 1-1 2-1 0-2 1-3 1l1 1c-1 1-3 1-5 1l-11 1c3-1 5-3 8-3 2 0 5-1 7-2z" class="N"></path><path d="M195 238h-1c1-2 6-2 9-2h5c3 1 5 1 8 2h0v1c-3 0-5-1-8-1-4 1-9 0-13 0z" class="Z"></path><path d="M780 387c5 2 12 4 16 8-4-1-9-3-14-4l-6-3h4-1l1-1z" class="I"></path><path d="M220 195l1-1 2 2 1-1v1 4c1 1 1 1 1 2v2l-1 1-1-1c0-1-1-1-1-1-2-2-3-3-3-5 0-1 0-2 1-3z" class="F"></path><path d="M430 603h1l1 1h1v1h2c-1 3 1 6 2 10l-1 1c-1-2-2-4-2-7-1-1-1-2-1-3-2 0-3 1-4 2h0c-1 1-3 1-4 0h-1-1v-1l4-2c1 0 2-1 2-1l1-1z" class="C"></path><path d="M544 418h4c-4 4-6 9-9 13h0 0-2c0-1 1-3 1-4l6-9z" class="R"></path><path d="M210 260h4 0c1 0 2-1 3-1s1 0 2 1c-2 0-4 0-6 1h-1c-6 2-12 6-17 8 1-3 9-7 12-9h3z" class="c"></path><path d="M424 634c0 2-4 4-6 5-4 1-9 3-12 2h-3 0l1-1h0l2-1 9-2c3 0 6-2 9-3z" class="R"></path><path d="M463 672h1c0 1 0 2 1 2h1l-1-2 1-1c0 1 1 1 1 3 0 1 1 1 2 2-1 1-1 1-1 2-1-1-1-1-1-3-1 1-1 1-1 2 0 2 2 4 2 6 0 1 1 2 1 3 1 1 1 2 2 4h-1c-1-1-2-2-2-4l-1-1c-2-3-3-9-4-13z" class="H"></path><path d="M239 209c1 0 1 0 1 1v1c1 1 3 1 4 1l-6 4c-3 1-6 2-9 2 1-1 1-2 2-2h3 1l2-2-1-1c-1 0-2 1-3 1h0l-1 1h-1c-1 1-1 0-2 1 0 0-1-1-2-1 3 0 5-1 7-2l5-4z" class="L"></path><path d="M399 667v1l4 2c-5 2-11 6-16 6 3-3 7-6 12-9z" class="T"></path><path d="M483 719c1 2 3 4 4 6l3 9 2 5 1 3c0 2 1 3 1 5h0l-1-1c0-2-2-5-3-7-2-7-6-13-7-20z" class="E"></path><path d="M685 493c4-3 7-5 11-5l1 2c-1 0-2-1-3 0 0 1-1 1 0 1 1 1 1 1 1 2 2 0 2 0 4 2h-1l-1 1v-1l-1 1-1-1-2-1c0-2 0-2-1-3v1c-2 1-3 2-5 3l-1 2v-1c0-1 0-2-1-3z" class="H"></path><path d="M524 462v-3c-1-7 1-17 3-25 1 1 1 2 1 3l-4 25z" class="I"></path><path d="M734 415l4 1 1 1c1 0 3 1 4 3v2c-2 1-6-1-8-2l-3-1h2 1c0-1 0-1-1-2h0v-2z" class="J"></path><path d="M668 546c3-1 5-1 8 0l12 2 3 3c-6-1-13-2-18-4l-5-1z" class="I"></path><path d="M270 353l4 5c3 3 6 6 9 10l-6-3-9-11c1-1 1-1 2-1z" class="J"></path><path d="M348 558c1-2 1-2 3-2l1-1c1-1 2-1 3-1s3 2 3 3v1c1 0 2 1 2 1l2 2-14-3z" class="E"></path><path d="M244 212h1 0c0 2 0 3 1 4-1 1-1 1-2 1-1 1-2 1-4 2l1-1-1-1c-2 0-2 0-2 2h-4c-2-1-5-1-7-1h2c3 0 6-1 9-2l6-4z" class="i"></path><path d="M244 212h1 0c0 2 0 3 1 4-1 1-1 1-2 1v-1c-2-2-2 0-4 0h-2l6-4z" class="d"></path><path d="M479 396c1 2 2 6 2 8h0-1l-3 2-1 1h-1c-1 1-4 2-6 2l-1-1c2 0 4-1 6-1 1-2 1-2 1-4 0-1 3-5 4-7z" class="R"></path><path d="M387 676l-7 5c3-5 10-12 16-15l3 1c-5 3-9 6-12 9z" class="b"></path><path d="M269 364c1 0 2 1 3 1h1l2 2h-1 0-3c1 1 3 1 4 1h2l2 2c-3 0-7-1-10-2-4 0-7 0-11-2h4c1-1 3-1 5-1h0l2-1z" class="O"></path><path d="M374 643c4-1 9-1 13-1l17-2h0l-1 1h0 3l-26 3h0l1-1c2 0 0 1 2 0h-4-5z" class="Q"></path><path d="M216 258c6 0 10 3 16 6l5 4 2 2c-3-1-7-4-10-6-3-1-7-2-10-4-1-1-1-1-2-1s-2 1-3 1h0-4c2-1 4-1 6-2z" class="I"></path><path d="M839 228c7 6 16 13 22 21h0l-2-1c-3-4-9-9-14-12-2-2-5-3-7-5 1 0 2 0 3 1s2 1 3 2l-1-1c-2-1-3-2-4-4v-1z" class="Q"></path><path d="M299 483l3-2 13-4h1l-1 1-12 4h9 5c2 0 3 1 5 1 1 0 1 0 2 1l2 1c-1 0-2 0-3-1h-5c-4 0-8 0-12-1h-3-4z" class="O"></path><path d="M557 404c0-2 1-3 2-4 1 2 0 6 0 8-2 5-8 7-11 10h-4l3-3c3-2 8-5 11-8l-1-1 1-2h-1z" class="d"></path><path d="M663 646c5 1 10 1 14 3-3 1-7 0-10 0l-10 1v-1h-6c1 0 3 0 4-1h-1c0-1-1-1-2-1l11-1z" class="J"></path><path d="M419 675c2 0 2 0 3 1s4 2 4 3l-1 1v2c-1 1-2 2-4 2l-4-4h-1c2-2 3-3 3-5z" class="N"></path><path d="M411 595l1-1c-1-1-1-3-1-4l1-1v1l3 3c0-1 0 0 1-1 0 0 1 1 2 1 1 1 1 1 1 2l1 1v2c1 0 1 1 1 2s1 2 1 3v3 1c-2-2-1-3-1-4-1-1-3 0-3-2v-1-1c-1-2-1-3-2-4h-1-4z" class="H"></path><path d="M518 378c2 1 2 2 3 3 0 1-1 1-1 2-1 2-1 3 0 5 0 1 0 1 1 2 0 2 0 2-1 4l-5-5-3-2c-1-2-1-5 0-7 1 1 0 5 2 7h1c1-1 2-1 3-2 1-2 1-4 0-7z" class="Q"></path><path d="M412 653l2-2c0-1 0-1 1-2v-1c0-1 0-2 1-3l1-1 2-2h1l1 1c0 1-1 1-1 2-1-1-1-1-2-1v2l-2 2c0 1 1 1 1 2l1 2c-1 2-1 3-1 5v3c-2 0-3-2-4-3l-1-2v-2z" class="B"></path><path d="M486 710c2 3 3 6 4 9l5 16v1 2h-1c-2-4-3-9-4-14-1-3-3-5-3-8v-3h0l-1-1v-2z" class="H"></path><path d="M593 629c5 3 9 6 14 8 3 1 7 1 10 2l1 2-7-1-10-3c-2-1-4-2-7-4 0-1-1-2-1-4z" class="R"></path><path d="M217 237l3 3c0 1-1 1-2 2h0c-5 0-9-1-13-2-4 0-7 0-10-2 4 0 9 1 13 0 3 0 5 1 8 1v-1h0l1-1z" class="I"></path><path d="M495 456c1-2 1-4 2-6l3 38v1c-2-3-2-9-2-12l-3-21z" class="Z"></path><path d="M230 197l1 1-1 1c0 2 2 1 0 4l-2 2v1c1-1 2-1 2-2 1 0 2-1 3-1h1c1-1 0-1 1-2 0-2 2-1 3-1h1v1h1 2l1 1h1 0 1v-1-1-1h0c1 1 2 1 2 2l2 1h0v-1c1 0 1 0 1 1 3 2 6 3 9 5l-8-1c-1-2-3-3-4-5l-1 1v1h-2c-2-1-3-1-5-2h-3c-1 2-1 3-3 4-2 0-2 0-4 2-1 0-2-1-3-1l1-1c1 0 1-1 2-2 0-2 1-3 1-5v-1z" class="H"></path><path d="M765 384c5 0 10 1 15 3l-1 1h1-4c-2 0-9-2-10-1h-1c-1 1-2 2-3 2l-6 2c1-1 3-4 5-5h4l-1-2h1z" class="K"></path><path d="M332 492c-1 4-6 7-9 9s-5 4-7 6c-2 1-4 2-5 4l-3 1h0c4-5 9-8 13-11 2-2 4-3 5-5 2-2 4-4 6-4z" class="U"></path><path d="M762 363c1-1 3 0 4-1s3 0 5 0c5-1 10-1 15-1l1 1c-1 1-2 1-3 2-3 0-6 0-8 1l-2-1h2-1-3-7-2c0-1-1-1-1-1z" class="D"></path><defs><linearGradient id="A" x1="548.416" y1="328.297" x2="538.329" y2="319.908" xlink:href="#B"><stop offset="0" stop-color="#717170"></stop><stop offset="1" stop-color="#898788"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M539 315l11 18-2-2c-1-1-2-1-2-2l-2 1c-2-1-2-4-3-5-1-2-3-4-3-5-1-3 0-3 1-5z"></path><path d="M736 370h0c2 0 3-1 5-3 3-3 6-7 10-9l-7 8c-2 2-4 4-5 6-3 2-6 3-8 5h0v-1l-1 1v1l1-1 1 1s1 0 2 1l-7-1h0v-1c1 0 2-1 2-1h1l2-2 2-2 2-2z" class="C"></path><path d="M252 327l2 2c4 9 10 17 16 24-1 0-1 0-2 1-5-6-10-13-13-19l-1-1c-1-2-2-4-2-7z" class="I"></path><path d="M606 690c4 1 7 2 10 2 2 0 5-1 7 0h1c3 1 9 3 10 6-1 0 0 0-1-1h-2c-2 0-4 0-5-1-3 0-5-1-8-1v-1c-2-1-6-1-8-2h0c-2 0-2-1-4-2z" class="K"></path><path d="M655 549c-1 1-1 2 0 3 2-1 4-4 6-6h7l5 1c-2 1-7 1-8 2-2 1-7 8-8 7l1-2h0l7-6h-1-1l-2 1c0 1 0 0-1 1s1 1-1 3l-1-2-1 1h0l-1 1c-1 1-2 3-2 5h-2v-9l2-1 1 1zm185-311c0-1-1-2-2-3s-2-1-2-2 0-1 1-1 5 4 8 4c5 3 11 8 14 12-1 0-1 0-2-1l-17-9z" class="B"></path><path d="M350 569v-1l-3-3h1c4 2 7 6 11 9 2 1 3 2 4 3 3 4 7 7 10 11h-3c-4-5-9-10-14-15l-6-3v-1z" class="F"></path><path d="M651 649h6v1c-7 1-13 2-19 5-2 2-5 8-7 9h0c1-1 3-4 3-5-2 2-4 5-6 6h-1c1-2 3-4 4-6 2-2 5-4 5-6h0c5-3 10-3 15-4z" class="D"></path><path d="M802 239h1c2 0 5-1 7-2 6-1 13-1 19 0-9 3-17 3-27 4l-1-2h1z" class="X"></path><path d="M348 649c-4 0-8 1-12 1 6-2 13-3 19-4l19-3h5 4c-2 1 0 0-2 0l-1 1h0l-1 1-31 4z" class="d"></path><path d="M627 665h1c6 5 11 11 15 17l-6-5-9-3v-1c0-2 0-2 1-3 0-1-4-2-5-3 1-1 2-1 3-2z" class="Q"></path><path d="M628 674v-1c0-2 0-2 1-3 3 2 6 4 8 7l-9-3z" class="C"></path><path d="M617 611v-1c1 0 2 0 2 1 1 1 1 2 1 3 0 2-1 4-2 7-3 2-4 5-5 8l6-1h1c1 0 1 0 2 1 0 0 1 0 2 1-2 1-4 1-6 1v-1h2l-1-1-1 1h-3v1c-1-1-1-1-2-1v1h-4c1-1 2-1 3-1v-1h-1l-1 1v-1l2-2c0-1 0-1 1-2 1-2 2-3 3-4v-2h0l1-3v-5z" class="M"></path><path d="M450 727v1h1 0v-3c1-1 2-1 4-2h1c0 1-1 2-1 2 1-1 1-1 3-1-1 1-2 2-2 3v1c-1 3-1 6 0 9v3l3 1c-1 1-1 1-3 1-1-1-1-2-1-3l-1-1v-2c0-1 0-2-1-2l-2-3h-4v-2c1 0 2-1 3-2z" class="O"></path><defs><linearGradient id="C" x1="531.84" y1="444.325" x2="525.232" y2="469.297" xlink:href="#B"><stop offset="0" stop-color="#c4c3c3"></stop><stop offset="1" stop-color="#f9f7f8"></stop></linearGradient></defs><path fill="url(#C)" d="M525 468c1-6 4-23 8-27v3l1 1-7 25h-1l-1-2z"></path><path d="M567 754c2 0 2 0 3 1 0 1 0 2-1 3-1 0-1-1-2-1-3 4-3 9-6 13v2c0 1-1 2-1 3h-1 0c-1 0-2 2-2 3s0 2-1 3v1l-1 1c0 2 0 0-1 2v1c0 1-1 3-2 4-1 2-1 5-3 7v1 1h-1v1-3l1-1c0-2 1-4 2-6v-1c0-1 1-3 2-4 2-2 2-5 3-7l4-8c0-2 1-5 2-6 2-2 2-4 3-6h0l1-1 1-1v-1-1z" class="L"></path><defs><linearGradient id="D" x1="654.147" y1="548.003" x2="666.704" y2="542.638" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#6d6b6b"></stop></linearGradient></defs><path fill="url(#D)" d="M663 539h1c0 1 0 1 1 2h4l1 1-2 1h-1l2 1h0 1c2 1 4 1 6 2-3-1-5-1-8 0h-7c-2 2-4 5-6 6-1-1-1-2 0-3h0l1-4h1l1 1c0-1 2-3 4-4v-1h1 0v-2z"></path><path d="M381 188c1-1 3-1 4-3v-1l-1 1c-1 1-2 1-3 2-1-1-1-1-3-2 0-2 2-3 3-4 1 0 2 1 3 1v-1h1 8l-10 11-1-2c0-1-1-1-1-2z" class="R"></path><path d="M100 163h1c3 2 4 4 7 6 1 1 3 2 5 3 4 2 9 1 13 1s8 0 12-1v1 2c1 3 4 3 7 5 2 1 5 3 6 5 1 0 3 1 4 0 0 0 1 0 1 1h0-2 0c-1 1-3 1-3 1l-3-3c-2-2-5-4-7-5h-1c-2-1-3-2-4-4v-1c-7 0-16 2-22 0-5-2-11-7-14-11z" class="T"></path><path d="M195 205l10 2 1 1c3 2 6 3 9 4h-8c-1-1-2-1-3-1h-2c-2-1-6-1-8-1-2-1-5 0-7 0h-1c-1 0-3 1-5 0l1-1c2 0 3-1 4-1h12l-3-3z" class="L"></path><path d="M246 314c1 0 3 3 4 3l-1 2v1 1h-1v1h-2-1c-2-1-3 0-4 0-3 3-7 4-10 6 2-3 4-5 6-7 3-3 6-5 9-7z" class="X"></path><path d="M246 314c1 0 3 3 4 3l-1 2v1 1h-1l-3-5c-2 1-5 4-7 5h-1c3-3 6-5 9-7z" class="R"></path><path d="M499 512v-4c1-1 1-1 1-2v1h1c2 13 1 28 1 41h-1l-2-36z" class="I"></path><path d="M407 666l11 7 1 2c0 2-1 3-3 5-3-4-8-7-12-10 0-2-1-3 0-4h3z" class="R"></path><path d="M500 384c2 4 3 7 2 11-3 5-4 7-5 12 0 2 0 4-1 6v1 2c-1-1-1-3-1-4l1-1c0-2 0-4-1-6 0-1 1-6 2-7 1-2 1-6 1-8v-1h1l1-5z" class="O"></path><path d="M759 383c1 1 5 0 6 1h-1l1 2h-4c-2 1-4 4-5 5-5 3-10 6-14 9h-1c-1 1 1 0-1 1-1 0-3 0-4 1 2-2 4-3 5-5 3-2 6-4 8-6 3-2 5-3 7-5l3-1v-2z" class="c"></path><path d="M525 468l1 2h1l-7 77h0l1-28c0-4 0-8 1-12l3-39z" class="k"></path><path d="M542 388l2-1c-1 5-2 9 1 13 1 2 3 4 5 4 3 1 4 0 6 0h1 1l-1 2c-2 2-4 3-6 2-4 0-6-2-8-5l-2-5c1-3-1-7 1-10z" class="Q"></path><path d="M563 731c3 1 5 2 8 2 1 0 1 1 2 2 0 1-3 3-4 4h0l-1 1-1-4c-2 1-2 2-4 3h-1c-2 1-4 1-6 1 1-2 0-3 0-4 1 0 0-1 1 0h1l3-3c1 0 1-1 2-2h0z" class="C"></path><path d="M404 691c0-1-1-1-1-2l-2-4c-1-2-1-3-2-4l-2-3-1-1c1-1 1-1 3-1 1 1 1 2 2 3l3 5c1 1 1 3 2 4h1l1-1c0-1-1-1-1-2v-1-2h1v-1l4 6h3c1-1 3-1 4-1 3 1 7 0 10 1-9 0-17 0-25 4z" class="E"></path><path d="M799 249c3 1 7 1 10 3l2 1c10 5 20 12 28 21l1 1h-1c-11-9-22-18-36-23h0c-1-1-2-1-4-1l-1-1 1-1z" class="b"></path><path d="M336 556c5 0 8 3 13 5 4 2 8 3 13 3-2 2-4 2-7 1-2-1-3-1-5-1v-1l-1 1c2 1 3 2 5 3 3 3 6 7 10 10h-1c-1-1-2-2-4-3-4-3-7-7-11-9h-1l3 3v1l-3-3c-2-2-4-5-6-6-1-2-4-3-5-4z" class="D"></path><path d="M311 492c6-2 12-4 19-5 1 1 1 1 3 1h0l1 1h0c2 2 3 2 5 3v3c-1 1-1 2-2 3l-8 4h-1c0 1-1 1-2 1 3-3 8-5 11-7v-1c-1-1-2-1-2-2-1 0-2-1-3-1-2 0-4 2-6 4l-1-2 1-1h0c2-1 4-2 4-3-1-1-2-1-4-1-1 1-2 1-3 1-2 1-4 1-5 2h-7z" class="E"></path><path d="M807 197l1-1c1-1 2-1 3-1 2 0 1 0 2-1h3c3-1 6-1 10-1 5 0 11-1 16 0-5 0-8 1-12 2h-1c-2 0-4 0-6 1-2 0-2 1-3 1l-1 1-2-1-1 1h0l-7 2-1 1h0-3v-1c1-1 1-2 2-3z" class="C"></path><path d="M807 197h3c-1 1-1 2-1 3l-1 1h0-3v-1c1-1 1-2 2-3z" class="G"></path><path d="M617 639l35 4 16 3c6 1 13 1 18 4-3-1-6-1-9-1-4-2-9-2-14-3-4-1-8-1-13-1l-32-4-1-2z" class="b"></path><path d="M317 482h6c3 1 5 0 8 1l2 2h1c2 0 4 0 6 1 1 1 2 1 4 1h0l-1 1c0 1 1 1 1 2l-4-1h-2c-1 0-1-1-2-1s-2 0-2 1l-1-1h0c-2 0-2 0-3-1-7 1-13 3-19 5v1l-1-1h0c2-3 12-5 15-6 2 0 2 0 3-1h-2l-2-1c-1-1-1-1-2-1-2 0-3-1-5-1z" class="a"></path><path d="M333 488h1c-1-1-2-1-3-2h1c1 0 1 0 3 1h3 1l1-1c1 1 2 1 4 1h0l-1 1c0 1 1 1 1 2l-4-1h-2c-1 0-1-1-2-1s-2 0-2 1l-1-1z" class="g"></path><defs><linearGradient id="E" x1="844.824" y1="197.145" x2="856.603" y2="195.367" xlink:href="#B"><stop offset="0" stop-color="#7b797a"></stop><stop offset="1" stop-color="#a3a3a2"></stop></linearGradient></defs><path fill="url(#E)" d="M844 194c6 1 12 3 17 4 6 2 16 4 21 7v1c-5-1-10-3-14-4-5-2-9-2-13-3s-9 0-13 0v-1-1h-1c1-2 2-1 3-1l-1-1-1-1h2z"></path><path d="M768 213h8l-1 2c1 0 0 0 1 1-2 1-5 1-8 1h-6v1l1 1-1 1c2 1 5 0 6 1v1c-5 0-8 0-12 2l1-3h1 3v-1l-1-1-1-1c-1-1-2-1-3-2h-2v-1c3-2 10-2 14-2z" class="L"></path><path d="M417 723c1 0 1 1 2 1h2c0 3 3 6 3 9h1c0 1 0 1 1 2h3v2l-2-1v2c0 2 1 5 2 7 1 1 2 2 1 4v1l-1-1h-1s-1-1-1-2l-5-12-3-6h-3c-2-1-4 0-5-1l1-1c2 0 3-1 5-1h1 0l-1-3z" class="F"></path><path d="M424 733h1c0 1 0 1 1 2h3v2l-2-1v2c0 2 1 5 2 7 1 1 2 2 1 4v1l-1-1c0-1-1-3-2-4-1-3-2-4-3-6v-2l-1-3 1-1z" class="L"></path><path d="M258 366c-6 0-18 0-23-4h0l1-1h2 8c5 1 9 1 14 2h3 1c2 0 3 1 5 1l-2 1h0c-2 0-4 0-5 1h-4z" class="G"></path><defs><linearGradient id="F" x1="758.533" y1="371.825" x2="761.967" y2="359.675" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#3a3739"></stop></linearGradient></defs><path fill="url(#F)" d="M745 366c3-1 6-1 9-2 1-1 3-1 4-1h1 3s1 0 1 1h2 7 3 1-2l2 1c-10 1-19 4-29 4-1 0-2 0-3-1l1-2z"></path><path d="M225 395c7-5 17-8 25-10h6v1h-2 1c3 1 4 1 6 4l-1-1c-6 0-12 1-17 2-6 1-12 4-18 4z" class="K"></path><path d="M243 391c1-2 1-2 4-2l-1-1c2 0 7-1 9-1 1 1 0 1 1 1s2 0 4 1c-6 0-12 1-17 2z" class="J"></path><path d="M768 338c6-2 17 1 22 4 5 2 10 6 14 10 2 2 5 4 8 8h-1-1c-1-3-4-4-7-6-8-7-15-10-25-13l-9-2c-1 0 0 0-1-1z" class="k"></path><path d="M497 372s2 1 3 0c2 0 2-1 4 1-1 1-1 2-1 4 0 1 0 2-1 4 0 3 2 5 2 8l1 1c-1 1-1 1-1 3l-2 2c1-4 0-7-2-11-2-5-4-6-9-9-1 1-1 0-3 0h0l1-1v-2c1 0 2 1 3 1l1-1h1v1c1 0 2-1 3-1z" class="e"></path><path d="M497 372s2 1 3 0c2 0 2-1 4 1-1 1-1 2-1 4 0 1 0 2-1 4-1-2-1-5-1-7h-1l-1 2h0l-2-2c-2-1-3-1-6 1-1 1-1 0-3 0h0l1-1v-2c1 0 2 1 3 1l1-1h1v1c1 0 2-1 3-1z" class="I"></path><path d="M384 605l5-2-1 2c-1 1-2 2-3 2l-1 1-2 1-8 1-10 5c-3 0-5 1-7 3l-18 7c3-2 7-4 10-6 6-3 12-7 18-9l17-5z" class="i"></path><path d="M374 610c4-2 9-4 14-5-1 1-2 2-3 2l-1 1-2 1-8 1z" class="j"></path><path d="M767 316c2-1 5-2 7-3l2 1c5 1 11 7 14 12v1l-2-1c-1-1-3-2-5-3-2-2-2-3-5-4-1 1-3 2-4 1-2-1-6 1-8 1l2-2h-3c0-1 1-2 2-3z" class="a"></path><path d="M767 316c2-1 5-2 7-3l2 1-8 5h-3c0-1 1-2 2-3z" class="C"></path><path d="M778 319c-1-1-2-1-3-2l1-1 1-1c1 1 2 2 4 3 2 2 4 4 6 7l1 1c-1-1-3-2-5-3-2-2-2-3-5-4z" class="S"></path><path d="M310 451l-21-6c-4-1-8-2-11-4-2-1-2-1-2-2v-1l6 1c1 0 2 0 3 1 1 0 3 0 4 1h2l3 1 3 1 2 1c1 0 3 1 4 1 3 1 5 3 7 5v1z" class="C"></path><path d="M531 415c1-1 2-1 2-2v-2l1 2h0l1 1v1 4l-2 10c-2 6-7 33-9 35h0v-2l4-25 1-4 1-10c0-1 1-2 1-3v-5z" class="i"></path><defs><linearGradient id="G" x1="381.063" y1="653.552" x2="396.509" y2="659.978" xlink:href="#B"><stop offset="0" stop-color="#504f4e"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#G)" d="M375 651c0-1 0-1-1-1 1-1 1-1 2-1 1 2 1 2 3 2 1 0 2 0 3 1h1c1 0 3 1 4 1 4 2 9 6 13 8l2 1 5 4h-3c-1 1 0 2 0 4l-5-4c-9-4-15-12-24-15z"></path><path d="M399 666l-1-3v-1h4l5 4h-3c-1 1 0 2 0 4l-5-4z" class="Z"></path><path d="M430 655s1 1 2 1l1 1h0 1 1 0c1 1 2 1 3 1h1c2 1 3 0 5 0h6 1c-4 2-10 2-14 2-1 1 0 1-1 2 1 1 1 2 0 3 0 2-1 3-2 5-1 0-1-1-2-2 0-1-2-3-1-5v-1c0-1-1-1-1-2-2 0-2 1-3 1l-1 1c-1 0-3 0-4-1v-1l1-1c0-1 1-1 2-2 2 0 3-1 5-2z" class="V"></path><path d="M432 668l2-2-1-1c0-1 1-1 2-2-1-1-2-2-3-2h-1l1-1h5c-1 1 0 1-1 2 1 1 1 2 0 3 0 2-1 3-2 5-1 0-1-1-2-2z" class="F"></path><path d="M430 655s1 1 2 1l1 1h0 1 1 0c1 1 2 1 3 1h1c-3 0-6 1-8 0h-1c-3 1-5 2-8 2l1-1c0-1 1-1 2-2 2 0 3-1 5-2z" class="G"></path><path d="M190 193c3 0 7 0 11 1h9c2 2 4 3 5 4h4c0 2 1 3 3 5 0 0 1 0 1 1l1 1 1-1c3-1 1-3 2-4 0-1 1-2 1-3h2v1c0 2-1 3-1 5-1 1-1 2-2 2l-1 1c1 0 2 1 3 1 2-2 2-2 4-2l-3 3c-1-1-3-1-4 0-1-2-2-3-4-3h-1l-2-2h-1c-1 0-3-2-4-2h-1c-2-2-4-3-7-4h0c-2 2-2 0-3-1l-1 1-2-1c0-1-2 0-3 0-3-1-5-2-7-3z" class="C"></path><path d="M206 197l2-1 4 2s2 1 3 1h1c1 0 2 1 2 2v1l1 1h-1c-1 0-3-2-4-2h-1c-2-2-4-3-7-4z" class="G"></path><path d="M212 269h4c1 1 3 1 5 2h2l4 2 4 1 1 1c1 0 3 1 4 1 7 2 11 6 15 11-2-1-3-3-5-3-1 0-1 0-2-1l-24-9-6-3c0-1 0-1-1-1l-1-1z" class="B"></path><path d="M180 194l10-1c2 1 4 2 7 3h-4c-2-1-3-1-5-1l-4 1c2 0 3 0 4 1l2 1-1 1c-1 0-1 0-2-1h-1l-2 1c-4-1-7-2-11-1h-1c-2 0-2 0-3 1h-6-1l-6 2s-1 0-2 1l-10 3h-2c-1 1-2 1-3 1v-1c4-2 11-4 16-5l25-6z" class="c"></path><path d="M180 194l10-1c2 1 4 2 7 3h-4c-2-1-3-1-5-1l-4 1-4-1c1 0 1 0 2-1h-2z" class="T"></path><path d="M348 649l31-4c-1 2-3 2-4 3h-2c1 1 1 1 3 1h1 1c2 0 2 1 3 1l10 5 6 2c1 1 3 2 4 3l-1 1c-4-2-9-6-13-8-1 0-3-1-4-1h-1c-1-1-2-1-3-1-2 0-2 0-3-2-1 0-1 0-2 1 1 0 1 0 1 1-5-1-9-1-14-1-4 0-9 0-13-1z" class="D"></path><path d="M509 437h1v17c0 3 1 5 1 8l-1 14 1 36c0 10 2 20 0 30l-1-42-1-23v-12l-1-15c0-2 1-4 1-6s-1-5 0-7z" class="V"></path><path d="M550 722v2l3 1 1 1h1c1 0 1-1 2-1l1 1c0 1 1 2 2 3l3 2h0c-1 1-1 2-2 2l-3 3h-1c-1-1 0 0-1 0 0 1 1 2 0 4l-2-1h-1c-1 0-2 0-3-1h0-1-1v-1c1-1 1-1 1-2v-2l1-1v-1h1c0-2 0-2-1-3l-2 1v-2c1-2 1-3 2-5z" class="E"></path><path d="M555 726c1 0 1-1 2-1l1 1c0 1 1 2 2 3l-1 1v1c-1 0-1 0-1 1l-1 3c-2-1-3-1-5-1v-1-1-2h0c1-2 1-3 2-4h1z" class="P"></path><path d="M808 268c1 0 1 0 2 1l1 1c-1 1-2 1-3 1l-20 8c-5 2-10 5-15 4 1 0 1 0 1-1 1-1 2 0 4 0l1-1c-1 0-2 0-3-1 1-2 5-2 7-3l3-1 2-1c1 0 2-1 3-1l4-1c1-1 2-1 3-2h3l4-2c1 0 2 0 3-1z" class="G"></path><path d="M202 225c2 0 4 0 6 1h2l2 1s0 1 1 1 1 0 2 1h1c1 1 3 2 4 3h0l-1 1v-1h-5l2 4-9-4c-5-2-8-3-13-1h-1c-6 3-11 6-16 9 1-2 12-9 15-10s7-2 10-4v-1z" class="U"></path><path d="M470 665l2 1h0c0-1 0-2 1-2l7 23c2 7 6 15 7 23-4-2-2-5-4-7l-2 1h-2c1-2 1-2 3-2l1-1-1-1c-1 0-2-1-2-1 1-1 2-1 2-3-3-2-6 0-8-2v-2h2 2 1v-1l-1-2c-2-3-1-6-2-9-1-1-2-3-3-5v-2c0-1 0-1-1-1v-1s0-3-1-3c-1-1-4 1-6-1l1-1h4v-1z" class="E"></path><defs><linearGradient id="H" x1="363.232" y1="554.064" x2="359.969" y2="563.831" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#H)" d="M336 556l-3-1 1-1c4 0 9 2 14 4l14 3c8 3 22-3 30-6l-2 2c-9 5-17 8-28 7-5 0-9-1-13-3-5-2-8-5-13-5z"></path><defs><linearGradient id="I" x1="780.339" y1="348.275" x2="794.161" y2="344.225" xlink:href="#B"><stop offset="0" stop-color="#30302e"></stop><stop offset="1" stop-color="#484749"></stop></linearGradient></defs><path fill="url(#I)" d="M767 338h1c1 1 0 1 1 1l9 2c10 3 17 6 25 13 3 2 6 3 7 6-2 0-6-3-8-5l-9-5c-6-3-11-5-18-6-2 0-5 0-8-1l-3-1 3-4z"></path><path d="M767 338h1c1 1 0 1 1 1l9 2h-1-1c-1 1-8 1-9 0v-3z" class="G"></path><path d="M407 604h1c1 0 2 0 3 1v1h4v1h0v1 3c0 2 0 6 2 7l-1 1h-4c-1-1-2-1-3-1s-2-2-3-3l-1 2-2-3c0-4 0-5 2-8 0-1 1-1 2-2z" class="C"></path><path d="M406 615l1-2c2 1 2 2 3 3l-1 1v1c-1 0-2-2-3-3h0z" class="M"></path><path d="M407 604h1c1 0 2 0 3 1l-1 1h-2c-2 1-3 2-3 4v1h0c-1-1 0-3 0-5 0-1 1-1 2-2z" class="H"></path><path d="M405 606c0 2-1 4 0 5 0 2 0 3 1 4h0l-1 2-2-3c0-4 0-5 2-8zm6 0h4v1h0v1 3c-2 0-3-1-5-2l-1-1c1-1 2-1 2-2z" class="N"></path><defs><linearGradient id="J" x1="550.903" y1="405.243" x2="539.404" y2="413.191" xlink:href="#B"><stop offset="0" stop-color="#7d7a7a"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#J)" d="M541 398l2 5c2 3 4 5 8 5 2 1 4 0 6-2l1 1c-3 3-8 6-11 8l-3 3-6 9c1-3 2-5 2-7-1-1-1-1-1-2v-1l1-1h0c0-3 1-7 1-10l-2-4h0c1-2 1-3 2-4z"></path><path d="M540 416l1 3h1c1-2 2-3 4-4h1l-3 3-6 9c1-3 2-5 2-7-1-1-1-1-1-2v-1l1-1z" class="S"></path><path d="M541 398l2 5-1 4 1 1c0 1-2 6-3 8 0-3 1-7 1-10l-2-4h0c1-2 1-3 2-4z" class="e"></path><path d="M656 577c2-1 4-2 5-4 5-3 10-6 14-9 1-1 1-1 2-1h0c3 1-1 0 2 1 2 0 6 4 7 6v1h0c-2-2-4-4-6-5l-2-2-22 18c-4 1-7 4-10 6h-1l-5 3-2-1c3-1 5-3 7-6 1 0 2-2 4-3 0 0 1 0 2-1 1 0 1-1 2-2l2-1h1z" class="F"></path><path d="M649 581s1 0 2-1c1 0 1-1 2-2l2-1h1a57.31 57.31 0 0 1-11 11l-5 3-2-1c3-1 5-3 7-6 1 0 2-2 4-3z" class="K"></path><path d="M480 724l5 13c6 15 12 29 20 43 2 3 4 7 6 10h1v3c1 0 0 0 1-1 2-1 4-5 5-6l9-16c1-1 1-2 2-3-1 5-4 11-7 15l-1 1-2 4c-2 4-4 8-7 11v-1l-1-1v-1c-1-6-5-11-8-17l-9-17-10-23-6-13 1-1v1l1-1z" class="T"></path><path d="M607 668l1 1 1 1c-3 3-5 4-6 8 1 1 1 1 2 1l-1 1v1c0 1-1 2-2 2s-2-1-3-2l-1-1v-1c0-1 0-1 1-2-1 0-2 0-3 1-4 1-4 2-7 2h-2v1l1 2-8-3v-1c1 0 1 0 2-1l3-1c1 0 1-1 2-1 3 0 4-1 6-2 1-1 3-1 4-2 2-1 3-1 6-1l4-3z" class="L"></path><path d="M593 674c1-1 3-1 4-2 2-1 3-1 6-1-2 2-4 3-6 4s-4 1-5 3c-1 0-2 0-3 1l-1-1c-1-1-4 0-6 0l3-1c1 0 1-1 2-1 3 0 4-1 6-2z" class="O"></path><path d="M652 563c4 1 11-2 15-3 7-2 15-5 23-6-3 2-6 3-9 5-3 0-5 2-8 3-5 1-11 3-17 3h-1-3c-5 1-12-2-17-4l-6-4 1-2c5 4 10 6 17 7h3l2 1z" class="b"></path><path d="M646 588c3-2 6-5 10-6-6 8-16 13-21 22l-7-3c0-1 0-1 1-2l-3-1c3-1 6-3 9-5l3-3 2 1 5-3h1z" class="E"></path><path d="M645 588h1c-5 5-11 7-16 11h-1l-3-1c3-1 6-3 9-5l3-3 2 1 5-3z" class="B"></path><path d="M544 330l2-1c0 1 1 1 2 2l2 2 1 1c0 2 1 4 2 6v2c0 1 1 1 1 2 1 2 1 3 3 5 2 1 3 1 4 1 1 1 3 2 3 3l-1 1c-2-1-4-2-5-2-2-1-4 0-6 1v1c-2 1-4 1-4 3h-1v-2c0-1 1-1 1-2 0-2 1-3 2-4v-1-3h-1l-4-4h2l-1-2c0-3-1-6-2-9z" class="e"></path><path d="M450 619c6 4 9 10 12 16 4 9 8 19 11 29h0c-1 0-1 1-1 2h0l-2-1c-2-4-3-8-5-12 0 0 0-2-1-2-1-2 0-1 0-2-1-2-2-3-3-5l-1 1-1-1v-1l-1-1c0-1 0-2 1-3 1 0 1 1 2 1-1-3-2-6-4-9-1-3-4-8-7-9 0-1-1-1-1-1l1-2z" class="Y"></path><path d="M810 218l18-2c2 4 4 7 8 10l3 2v1c1 2 2 3 4 4l1 1c-1-1-2-1-3-2s-2-1-3-1c-6-2-10-5-16-6-1-1-2-1-3-1l-2-2c2 0 3 0 4-1v-1c-3 0-7 0-11-1v-1z" class="k"></path><path d="M742 400l21-8h1c-4 2-9 3-13 5-11 4-20 12-28 21-1 0-2 0-3 1h0c-2-1-2-1-2-2l2-3c1-2 2-3 2-4s2-3 2-3l4-4 4 1c1-1 3-2 4-2 1-1 3-1 4-1 2-1 0 0 1-1h1z" class="h"></path><path d="M724 407l4-4 4 1-2 2c-1 3-5 8-8 10-1-1-2-1-2-2 1-2 2-3 2-4s2-3 2-3z" class="e"></path><path d="M724 407l4-4 4 1-2 2c-1 1-2 2-4 2l-2-1z" class="S"></path><path d="M747 383h12v2l-3 1c-2 2-4 3-7 5-2 2-5 4-8 6-1 2-3 3-5 5-1 0-3 1-4 2l-4-1 7-7c1 0 2-1 3-2l6-4v-1l-1-1v-1l1-1c1-1 2-1 3-2v-1z" class="g"></path><path d="M747 383h12v2l-3 1c-2 0-3 0-4 1l-8 3v-1l-1-1v-1l1-1c1-1 2-1 3-2v-1z" class="d"></path><path d="M227 218c2 0 5 0 7 1h4c0-2 0-2 2-2l1 1-1 1c-2 1-4 1-5 3l-2 1-2 1c-1 0-1 0-2-1h-6-3s-1 1-2 1c-1 1-4-1-7 0h-2-2c-2-1-2-2-2-3 1-1 1-1 3-1l2-1c1-1 4 0 6 0-2 0-5 0-6-1h-1 18z" class="b"></path><path d="M208 220h7 4l1 1c-3 0-8-1-10 2-1 0-1 0-1 1h-2c-2-1-2-2-2-3 1-1 1-1 3-1z" class="d"></path><path d="M220 221h13l2 1-2 1-2 1c-1 0-1 0-2-1h-6-3s-1 1-2 1c-1 1-4-1-7 0h-2c0-1 0-1 1-1 2-3 7-2 10-2z" class="Z"></path><path d="M636 653c0 2-3 4-5 6l-27 22v-1l1-1c-1 0-1 0-2-1 1-4 3-5 6-8 2-1 5-3 7-5 3-2 6-3 8-5 4-3 8-5 12-7z" class="X"></path><path d="M512 803v-2l1-1c0 1 0 1 1 2 0 1 1 1 1 2 1 1 1 2 1 3 1 1 2 1 2 3h1v2l1 1c0 1-1 3 0 4 0 1 0 2 1 3 0 1-1 4 0 5 0 2 0 5 1 7 0 1-1 2 0 4v1 1 4l3-5v-3c1-1 1-2 2-4 0-1 1-3 1-4l1-1v-1l2-2-11 29c-1-2-1-7-1-10 0-8-1-18-2-26-1-2-1-5-2-7l-2-2c0-1 0-2-1-3z" class="G"></path><path d="M516 430h1c0 2-2 5-2 6 0 0 1 1 1 2 1 1 0 5-1 7-1 3-1 7-1 10l-2 58v-22l-1-19c0-2 1-5 1-7 0-4-1-7-1-11 0-3 1-7 0-11 0-3 1-7 0-10h0l1-1 1 1v-1c1-1 1 1 3-2z" class="R"></path><defs><linearGradient id="K" x1="657.79" y1="563.939" x2="667.446" y2="571.504" xlink:href="#B"><stop offset="0" stop-color="#464545"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#K)" d="M656 565c6 0 12-2 17-3 3-1 5-3 8-3-2 1-3 3-4 4-1 0-1 0-2 1-4 3-9 6-14 9-1 2-3 3-5 4h-1l-2 1c-1 1-1 2-2 2-1 1-2 1-2 1 1-3 5-6 6-10v-2l1-4z"></path><defs><linearGradient id="L" x1="297.066" y1="402.842" x2="284.723" y2="408.795" xlink:href="#B"><stop offset="0" stop-color="#7a7879"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#L)" d="M272 392c2 1 3 1 4 2 3 3 7 4 10 6 1 1 4 4 5 4 2 0 2-1 3-2 3 3 7 6 9 9-2 3-2 5-3 8-8-9-16-16-25-23l-3-4z"></path><path d="M422 712h0c1 2 1 2 3 3v1 3l1 1c1 3 4 6 4 10-1 0-2 0-3 1v1 1h-2-1c0-3-3-6-3-9h-2c-1 0-1-1-2-1l-2-3c0-2-1-3-2-5 2 0 3-2 6-1 1-1 2-1 3-2z" class="P"></path><path d="M422 712h0c1 2 1 2 3 3v1 3h-3v1h0c-1-2-2-4-2-6h0-1c1-1 2-1 3-2z" class="F"></path><path d="M413 715c2 0 3-2 6-1-1 0-2 1-2 2 1 1 3 7 4 8h-2c-1 0-1-1-2-1l-2-3c0-2-1-3-2-5z" class="C"></path><path d="M422 720v-1h3l1 1c1 3 4 6 4 10-1 0-2 0-3 1-2-3-4-8-5-11z" class="I"></path><path d="M377 531c4 0 7 1 11 2l12 4 5 2h2 1l-1 5 6 5c0 1 1 2 3 3-1 1-1 1 0 2h-1v2l-1 1-1-2c-2-4-6-7-9-10l-1-1c-6-4-14-7-21-10-1-1-3-2-4-2l-1-1z" class="c"></path><path d="M377 531c4 0 7 1 11 2l12 4 5 2h2 1l-1 5c-6-4-12-7-18-10-2 0-4-1-6-2h-5l-1-1z" class="U"></path><defs><linearGradient id="M" x1="375.76" y1="535.506" x2="369.656" y2="537.19" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#4b494a"></stop></linearGradient></defs><path fill="url(#M)" d="M370 521l1 1c1 0 1 0 2 1v4c-1 1-1 2-1 3l2 1v3l2 1h2c1 1 1 1 1 2l1 2-1 1-1-2-1 2c1 1 0 0 2 1v-1l1 2c0 1 0 0-1 1h1c1 0 2 1 3 1s1 0 2 1v-1 1h1l1-1h-1v-2h0c2 1 2 1 2 3-1 0-1 1-2 1-1 1-7 0-8 0h0c0-1 0-1 1-1v-1c-2-1-3-2-6-2-1 0-1 0-2-1v-3c-1-2-2-3-4-4v-4l-1-1v-2l1-2 3-3v-1z"></path><path d="M370 521l1 1c1 0 1 0 2 1v4c-1 1-1 2-1 3l2 1v3l2 1c-1 1-2 1-2 1-1-1-1-1-1-2-1 0-2-1-2-1l-2-1v-2c-1-2-2-3-2-5l3-3v-1z" class="B"></path><path d="M371 522c1 0 1 0 2 1v4c-1 1-1 2-1 3l-1-1c1-3 0-5 0-7z" class="C"></path><path d="M370 522v-1 9c0 1 0 1 1 3l-2-1v-2c-1-2-2-3-2-5l3-3z" class="F"></path><path d="M778 319c3 1 3 2 5 4l-5-2-6 5c-1 1-2 3-3 5-2 4-4 7-6 11-3 5-8 11-12 16-4 2-7 6-10 9-2 2-3 3-5 3h0l1-2 2-2 7-8c7-7 13-14 18-23 1-2 3-4 4-7 1-1 1-2 2-3s1-2 2-3l2-2c1 1 3 0 4-1z" class="I"></path><path d="M567 754l1-1h2c2-2 3-3 4-6 0-1 0-1 2-2 0 1 0 1-1 2 0 2-1 3-1 5h-2c0 1 0 2-1 3-3 11-8 22-12 33-3 6-5 12-8 18h-2l10-24h-1c0-1 1-2 1-3l1-1v-1-1c1-1 1-1 1-2h1l-1-2v-2c3-4 3-9 6-13 1 0 1 1 2 1 1-1 1-2 1-3-1-1-1-1-3-1z" class="j"></path><path d="M561 772v-2c3-4 3-9 6-13 1 0 1 1 2 1l-7 17c-1 2-2 5-3 7h-1c0-1 1-2 1-3l1-1v-1-1c1-1 1-1 1-2h1l-1-2z" class="G"></path><path d="M514 366l1 1c1 1 2 3 3 4l-1-4h1v1h1c1 1 1 1 2 1 2 1 4 2 6 4v-1-1c2 1 3 1 5 3 1-1 1-1 1-2 1 0 2 1 2 2v1l-1 1c-2-1-5-1-8 0-1 1-1 2-2 3-2 5-3 8-1 13v1c1 1 1 3 2 4 0 3-1 6 0 8v1c-1-4-3-8-5-12 1-2 1-2 1-4-1-1-1-1-1-2-1-2-1-3 0-5 0-1 1-1 1-2-1-1-1-2-3-3-1-3-2-8-4-12z" class="i"></path><defs><linearGradient id="N" x1="489.361" y1="478.392" x2="503.639" y2="481.108" xlink:href="#B"><stop offset="0" stop-color="#323335"></stop><stop offset="1" stop-color="#51504f"></stop></linearGradient></defs><path fill="url(#N)" d="M493 464c3-4-2-19-3-24h0v1c1 3 2 8 3 10v-2-4-1-1c1 4 2 9 2 13l3 21c0 3 0 9 2 12v-1c1 2 1 5 1 7v12h-1v-1c0 1 0 1-1 2v4l-6-48z"></path><defs><linearGradient id="O" x1="411.653" y1="601.289" x2="396.852" y2="602.277" xlink:href="#B"><stop offset="0" stop-color="#1c1b1b"></stop><stop offset="1" stop-color="#454343"></stop></linearGradient></defs><path fill="url(#O)" d="M411 595h0 4c0 1 0 2-1 3v2l2 1-1 1h-2l-1 1h-1c-1 0-2 1-3 1h-1c-2 0-3 0-5 1-2 0-5 0-7 1h-2l-3 1c-2 1-7 3-9 3l1-1 2-1 1-1c1 0 2-1 3-2l1-2h0l22-8z"></path><path d="M411 595v1c-2 2-4 4-7 5-7 3-13 5-20 7l1-1c1 0 2-1 3-2l1-2h0l22-8z" class="Z"></path><path d="M688 469h1 1c5 1 10 2 15 4l18 8c5 2 18-5 21-2-1 1-3 1-4 1-4 0-9 1-12 2l2 2h0-4c-5-1-9-2-13-2-3 0-5 1-8 1 0-1 0-1 1-2h-3l2-1v-1c1 0 2 0 3-1-1 0-2-1-3-1-4-3-10-6-15-6h-2v-2z" class="f"></path><path d="M688 469h1c1 2 3 1 5 2 4 2 9 4 13 5 6 3 13 4 19 8-5-1-9-2-13-2-3 0-5 1-8 1 0-1 0-1 1-2h-3l2-1v-1c1 0 2 0 3-1-1 0-2-1-3-1-4-3-10-6-15-6h-2v-2z" class="D"></path><path d="M708 478l4 2v1c-1 1-4 0-6 0h-3l2-1v-1c1 0 2 0 3-1z" class="R"></path><defs><linearGradient id="P" x1="478.709" y1="334.992" x2="463.623" y2="345.773" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#676666"></stop></linearGradient></defs><path fill="url(#P)" d="M475 328l2 3c0 1 1 2 2 2l-1 5v2h1c-2 2-3 4-5 5-1 0-1 1-1 2 0 2 0 4 2 6-2 0-3 0-4-2-4 0-7-1-10 0 5-7 10-15 14-23z"></path><path d="M539 417v1c0 1 0 1 1 2 0 2-1 4-2 7 0 1-1 3-1 4-1 2-2 5-3 8 0 1-1 1-1 2-4 4-7 21-8 27l-3 39c0-1-1-2-1-3h0c0-7 0-36 3-40h0c2-2 7-29 9-35 0 1 0 1 1 1 1-2 3-4 3-7 0-1 1-1 1-3v-2l1-1z" class="e"></path><path d="M236 201c1 1 1 1 0 2 0 1-1 2-2 3h0 1c1-1 1-1 2-1l2 1v2l1 1h-1l-5 4c-2 1-4 2-7 2-2 0-5-1-7-1l-4-2h-1c-3-1-6-2-9-4h5 1c1 0 2 1 3 1l1-2h0l1-1c2 0 4 1 6 1l-2-2h1c2 0 3 1 4 3 1-1 3-1 4 0l3-3c2-1 2-2 3-4z" class="D"></path><path d="M216 207h0l4 2 1 1v1c-1 1-3 1-5 1h-1c-3-1-6-2-9-4h5 1c1 0 2 1 3 1l1-2z" class="c"></path><path d="M216 207h0l4 2v1h-1l-4-1 1-2z" class="X"></path><path d="M236 201c1 1 1 1 0 2 0 1-1 2-2 3h0 1c1-1 1-1 2-1l2 1v2l1 1h-1l-5 4c0-2 1-4 0-6h-1c-1 1-2 2-3 2h0c-2 1-3 1-5 1l-2-3-2-2h1c2 0 3 1 4 3 1-1 3-1 4 0l3-3c2-1 2-2 3-4zm421 333c2-1 2-1 3-1l1 1c0 1 0 3 1 4l1 1v2h0-1v1c-2 1-4 3-4 4l-1-1h-1l-1 4h0l-1-1-2 1v9 5l-2-1c1-2 1-3 1-5v-1c-1-1 0-1-1-2s0-2-1-3l-2-2c0-1 0-3-1-4v-1c0-1 0-1-1-2-1 0-1 1-2 1v-2l1-1c0 1 1 1 2 1h0l4 1c1-1 2-3 2-4s-1-2-2-2l1-1h1c2 0 3 0 4-2l1 1z" class="T"></path><path d="M657 534c2-1 2-1 3-1l1 1c0 1 0 3 1 4l1 1v2h0-1v1c-2 1-4 3-4 4l-1-1h-1c-1-1-1-2-2-2v-1c0-2 2-4 4-6v-1l-1-1z" class="I"></path><path d="M635 604l19 5 4-11 3-6c0 2-1 4-2 6l-4 12 15 7c4 3 9 5 13 8l-10-4c-3-1-7-2-10-4-2-2-5-3-8-4l-3-2c-4-2-7-3-10-4-6-1-12-4-18-4l-8-3c-2-1-6-2-8-3-1-1-1-1-1-2h1l-1-2h1c4 2 9 4 14 6l4-1 3 1c-1 1-1 1-1 2l7 3z" class="d"></path><path d="M622 599l4-1 3 1c-1 1-1 1-1 2l-6-2z" class="L"></path><path d="M210 254c5-2 9-4 14-4 0 0 0 1 1 1h1l-1 1 2 1-1 1-1-1-1 1-1 1c-2 0-3-1-5 0 0 2-1 2-2 3-2 1-4 1-6 2h-3c-3 2-11 6-12 9-2 2-7 4-10 5l-4 3c7-10 19-17 29-23z" class="f"></path><path d="M225 251h1l-1 1 2 1-1 1-1-1-1 1-1 1c-2 0-3-1-5 0 0 2-1 2-2 3-2 1-4 1-6 2h-3c-3 2-11 6-12 9-2 2-7 4-10 5 6-7 15-11 22-15 6-4 10-7 18-8z" class="F"></path><path d="M207 260c3-1 5-3 7-4 2 0 3 0 4-1 0 2-1 2-2 3-2 1-4 1-6 2h-3z" class="j"></path><path d="M214 256c4-2 8-4 11-4l2 1-1 1-1-1-1 1-1 1c-2 0-3-1-5 0-1 1-2 1-4 1z" class="Q"></path><path d="M310 450l3 1h1 2c3 2 6 6 10 6h3l3 2 4 4 2 3h1l1 2-1 2c-2 0-4-1-5 0h0c-2-1-3 0-4-1 0-1-2-2-2-3-1 0-2-1-2-1-1-1-2-1-4-1h-5c-2-1-5-1-6-2l1-1c2 0 6-1 8 0 1 1 1 1 1 2h2 0c-4-4-8-10-13-12v-1z" class="B"></path><path d="M328 466c1 1 2 2 4 2l-1-1 1-2c1 1 1 1 2 1h4 1l1 2-1 2c-2 0-4-1-5 0h0c-2-1-3 0-4-1 0-1-2-2-2-3z" class="N"></path><path d="M429 687l17 2c-4 1-8 1-12 1-2 1-6 1-9 1-5 1-11 1-15 3 2 0 6-1 9 1h0v1h-1c-2-1-3-1-4-1-3 0-6 0-9 1l-8 2c-1 0-3 1-5 1v-1h-1l-2 1h-1c3-4 11-5 15-7l1-1c8-4 16-4 25-4z" class="T"></path><path d="M429 687l17 2c-4 1-8 1-12 1-6-1-14-1-20 0-5 1-11 3-16 5-2 1-5 1-7 3l-2 1h-1c3-4 11-5 15-7l1-1c8-4 16-4 25-4z" class="b"></path><path d="M544 382c0 2-1 4-2 6-2 3 0 7-1 10-1 1-1 2-2 4h0l2 4c0 3-1 7-1 10h0l-1 1-1 1v2c0 2-1 2-1 3 0 3-2 5-3 7-1 0-1 0-1-1l2-10v-4-1l-1-1h0l-1-2v2c0 1-1 1-2 2v-1c0-4 2-7 2-10 1-2 1-4 1-6 1-1 2-2 2-3h0c4-3 6-9 8-13z" class="d"></path><path d="M537 410v-2c0-2-1-5 0-8h1l1 2h0 0l-2 8z" class="Q"></path><path d="M537 410l2-8 2 4c0 3-1 7-1 10h0l-1 1-1 1v2c0 2-1 2-1 3 0 3-2 5-3 7-1 0-1 0-1-1l2-10 2-9z" class="W"></path><path d="M561 751l1-1 1 2c1 0 2 1 4 2v1 1l-1 1-1 1h0c-1 2-1 4-3 6-1 1-2 4-2 6l-4 8c-1 2-1 5-3 7v-1c-1 1-2 2-2 3-1 1-1 2-1 3h0l-1 1c0 1-1 3-1 4-1 1-1 2-1 3v1h-1c0 1 0 1-1 2v2c-1 1-1 3-2 4v1 1l-1 1h0c0 1-1 2-1 2 0 1 0 2-1 2v2 1l-1 1c-1 1-1 3-1 4h-1v2l-1-1c1-3 2-6 4-9l7-20 11-36 2-2c0-2 1-3 1-5z" class="B"></path><path d="M561 751l1-1 1 2c1 0 2 1 4 2v1 1l-1 1-1 1h0-1c-1 1-1 2-1 3l-1-1-1 1 1-4c-1-1-1-1-2-1 0-2 1-3 1-5z" class="M"></path><path d="M563 752c1 0 2 1 4 2v1l-2 1h-1c-1-1-1-3-1-4z" class="P"></path><path d="M418 652c1-1 1-1 2 0-1-2-2-3-2-4v-1c1-1 2-2 4-2h0l1-1c3 0 4 0 7 2l2 1h0c1 1 1 0 1 2 1 0 1 1 2 2 0 1 1 1 1 2 0 0 1 0 1 1v1l1 1c0 1 1 1 2 2h4c-2 0-3 1-5 0h-1c-1 0-2 0-3-1h0-1-1 0l-1-1c-1 0-2-1-2-1-2 1-3 2-5 2-1 1-2 1-2 2l-1 1h-1c0 1 0 1-1 1s-2-1-2-1h-1v-3c0-2 0-3 1-5z" class="M"></path><path d="M429 647c-2 0-4 0-6 1v1c-1-1-2-1-3-1h-1c1-1 1-2 3-2 3-1 5-1 7 1h0z" class="j"></path><path d="M430 646l2 1h0c1 1 1 0 1 2 1 0 1 1 2 2 0 1 1 1 1 2 0 0 1 0 1 1v1l1 1c0 1 1 1 2 2h4c-2 0-3 1-5 0h-1c-1 0-2 0-3-1h0-1-1 0l-1-1c-1 0-2-1-2-1v-1c-2-1-4-1-7-1 2-1 4-1 5 0h2c1 0 3 0 5 1h0 0 1c-1-1-2-2-2-3v-1c-1-1-3-3-5-3h0l1-1z" class="L"></path><path d="M421 660v-1c-1-1-2 0-4-1 0-1 1-1 1-2h0c0-2 0-2 1-3 2-1 2-1 4 0 3 0 5 0 7 1v1c-2 1-3 2-5 2-1 1-2 1-2 2l-1 1h-1z" class="H"></path><defs><linearGradient id="Q" x1="544.631" y1="760.334" x2="558.428" y2="753.328" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#4b494a"></stop></linearGradient></defs><path fill="url(#Q)" d="M541 795h0v-2l1-1v-1l1-1 1-3h0c1-1 1-2 1-3s1-1 1-1v-2c1-2 2-4 3-5v-1-1c1 0 1-1 1-1 1-1 1-2 1-3h1v-2l1-1h0v-1-2h-1l-1 1-2 2-1-1c-1-1-2-1-2-3-1-1 0-3 0-4-1-2-1-4-1-5l1-1v-1-1h-1c-1 1-2 0-4 1v1c-1 1-1 2-1 2 0 1-1 2-1 3h-1v-1l2-6c4-1 8 0 12 0 3-1 6-1 9 0 0 2-1 3-1 5l-2 2c-2 1-3 7-4 10l-7 14c-1 1-6 13-6 13z"></path><path d="M549 806h2l-30 75-6 11s-1 2-1 3h-1l36-89z" class="U"></path><path d="M476 506c3 6 5 13 8 18l13 32c4 9 7 18 13 26v1l-4-4c-5-6-9-16-11-24l-1-1c-2-5-4-9-6-13 0-2-1-5-2-6-1-3-2-5-4-8-3 1-7 3-10 5 0-1 0-1-1-1-1-1-2-3-2-4-2-4-4-8-5-11l1-1c2 3 3 7 4 10l4-2c2-1 5-3 7-2h1v-1h-1v-2c-2-3-4-8-4-12z" class="R"></path><path d="M447 731h4l2 3c1 0 1 1 1 2v2l1 1c0 1 0 2 1 3 2 0 2 0 3-1 1 0 1-1 2-1s2-1 3-1l1-1h1l4-4v2c-1 4-5 6-8 7s-8 0-10 2c0 1 1 2 2 2-1 1-1 2-1 3l2 3c2 3 1 7 3 10 1 0 1 1 1 1l1 3c1 1 1 0 1 2h0l1 2v1c1 1 1 3 2 5 1 1 1 2 1 4-2-3-3-6-5-9l-5-11c-2-4-3-7-4-11h-1v2l-1 1 1 1c-1 1 0 2-1 3h0v-1l-1 1h-4c1-1 1-2 2-3s3-4 4-5c1 0 1 0 2-1-2-2-2-5-3-7v-1h0v-1h-1v-3-1h0l-1-2h1l-1-1h-1l1-1z" class="E"></path><defs><linearGradient id="R" x1="833.23" y1="236.091" x2="824.77" y2="226.909" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#4a4849"></stop></linearGradient></defs><path fill="url(#R)" d="M811 223h2c1 0 3-1 4-1l2 2c1 0 2 0 3 1 6 1 10 4 16 6 2 2 5 3 7 5-3 0-7-4-8-4s-1 0-1 1 1 1 2 2 2 2 2 3c-5-2-13-9-19-7h-4l-8 4c-1-1-1-1-3-1l-2-2-4 4h-1l-1-1c2-2 4-3 6-4 3-2 5-4 9-5-1-1-2-1-3 0h-1l-2-1 4-2z"></path><path d="M811 223h2c1 0 3-1 4-1l2 2c1 0 2 0 3 1h-6c-1 0-2 1-3 1-1-1-2-1-3 0h-1l-2-1 4-2zm-7 9c2-1 3-2 5-3 5-3 9-3 15-2-2 1-5 2-7 4h0l-8 4c-1-1-1-1-3-1l-2-2z" class="U"></path><path d="M212 360h-1 0c1-4 15-15 19-17 5-3 17-6 23-5 1 1 2 2 2 3-2 1-5 2-7 2-5 1-10 3-15 5-4 2-9 5-13 8-2 2-5 4-8 4z" class="Q"></path><path d="M253 338c1 1 2 2 2 3-2 1-5 2-7 2v-1h-1-1c-3 0-4 1-7 1v1c-4 1-7 3-11 5-1 1-4 4-5 3h-1c4-3 8-6 13-8 4-2 8-3 11-4 3 0 6 0 7-2z" class="T"></path><path d="M222 352h1c1 1 4-2 5-3 4-2 7-4 11-5v-1c3 0 4-1 7-1h1 1v1c-5 1-10 3-15 5-4 2-9 5-13 8-2 2-5 4-8 4 2-3 7-6 10-8z" class="I"></path><path d="M211 224c3-1 6 1 7 0 1 0 2-1 2-1h3 6c1 1 1 1 2 1l2-1v1c-1 1-5 4-6 6 0 1 0 2-1 3l1 2 1-1c1 1 1 1 1 2 1 1 2 1 4 2h1v1l-1 1c-2-2-4-3-5-4-3 3-5 6-8 8-3 3-6 5-8 8-1 0-1 0-2 1 1-5 5-8 8-11 1-1 2-1 2-2l-3-3-1-1-2-4h5v1l1-1h0c-1-1-3-2-4-3h-1c-1-1-1-1-2-1s-1-1-1-1v-1h3l-4-2z" class="b"></path><path d="M211 224c3-1 6 1 7 0 1 0 2-1 2-1h3 6c1 1 1 1 2 1-2 1-4 4-5 5v2h-1l-1-1c-2-2-6-4-9-4l-4-2z" class="X"></path><path d="M258 384c4-1 8 0 12 0h25-5l-1 1h0c-2 0-3 0-4 1l-1 2h1c-3 1-6 0-9 0h-3l6 4 6 3h0l2 3h1v1h0-1 0l-1 1c-3-2-7-3-10-6-1-1-2-1-4-2l3 4c-5-3-9-5-14-6-2-3-3-3-6-4h-1 2v-1h-6c2-1 5-1 8-1z" class="j"></path><path d="M279 392l6 3h0l2 3c-2 0-3 0-5-1-2-2-4-3-6-4h0c1 0 2-1 3-1z" class="R"></path><path d="M273 388h0c-2 0-3 0-4-2v-1h9c2 0 4 1 7 1l-1 2h1c-3 1-6 0-9 0h-3z" class="U"></path><path d="M250 385c2-1 5-1 8-1h4l-2 1 2 1 2 1h0c3 1 6 3 8 5l3 4c-5-3-9-5-14-6-2-3-3-3-6-4h-1 2v-1h-6z" class="W"></path><path d="M630 182l-4-4c8 0 16 0 24 4v2c1 1 2 2 4 3h1c2 3 6 5 8 8h-2-1c0-1-1-1-2-2l-8-7c-1 2-2 4-4 5h0c-1 2 0 3 0 5h-3l-13-14z" class="Y"></path><defs><linearGradient id="S" x1="639.297" y1="184.127" x2="641.176" y2="195.702" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#939291"></stop></linearGradient></defs><path fill="url(#S)" d="M630 182c2-1 7-1 10-1 2 1 2 1 3 2s3 0 3 1c1 0 1 1 2 1-1 1-2 2-2 3l-4-2 1-1v-1c-2 1-2 2-3 4 1 1 3 2 4 3h2c-1 2 0 3 0 5h-3l-13-14z"></path><path d="M195 216l14 2h1c1 1 4 1 6 1-2 0-5-1-6 0l-2 1c-2 0-2 0-3 1 0 1 0 2 2 3h2 2l4 2h-3v1l-2-1h-2c-2-1-4-1-6-1-12 2-23 10-33 18-3 3-6 5-10 8 2-3 5-5 7-8 9-8 20-14 27-24l2-3z" class="Q"></path><path d="M244 432h1c6-1 12-1 18-1 5-1 9-2 14-2 7-1 15 0 22 0h1c1 1 2 1 3 1l1 1-1 1h0c-3 0-7 0-10 1l9 3c-1 1-2 1-3 0h0-2c0-1 0-1-1-1s-3 0-5 1c-3-1-5-2-8-2-5 0-8 1-12 0h-11c-3 0-6 0-9-1-3 0-5 1-7-1z" class="k"></path><path d="M244 432c16 1 32-1 49 1l9 3c-1 1-2 1-3 0h0-2c0-1 0-1-1-1s-3 0-5 1c-3-1-5-2-8-2-5 0-8 1-12 0h-11c-3 0-6 0-9-1-3 0-5 1-7-1z" class="F"></path><path d="M716 444c-1 3-3 4-4 6l4-2c6-4 13-6 19-8h2c1-1 1-1 2-1h1c1 0 0 0 1-1h5v1c0 1-1 1-2 2-2 1-4 1-7 2-5 2-13 6-20 6-2 0-4 2-6 3-5 4-10 9-15 13l-2 1c-2 1-3 1-4 3h-1-1c-3 0-5 1-8 0v-2l3-2 1-1 4-2h0 2l5-1v-1c3-1 5-2 7-4l14-12z" class="O"></path><path d="M691 464c2 0 4-1 6-2v1c-1 0-1 1-1 2l-2 1h-6l3-2z" class="L"></path><path d="M688 462h0 2l5-1c-1 1-3 2-4 3l-3 2-1 1c-1-1-2-1-3-1l-1-1 1-1 4-2z" class="B"></path><path d="M683 465l1 1c1 0 2 0 3 1l1-1h6c-2 1-3 1-4 3h-1-1c-3 0-5 1-8 0v-2l3-2z" class="N"></path><defs><linearGradient id="T" x1="469.509" y1="682.137" x2="461.298" y2="684.022" xlink:href="#B"><stop offset="0" stop-color="#3d3d3f"></stop><stop offset="1" stop-color="#5b5a59"></stop></linearGradient></defs><path fill="url(#T)" d="M448 630l-1-1c1 0 2 1 3 2l1-1h1v1h-1-1l1 1c0 2 1 4 1 7l4 10c2 8 4 15 7 23 1 4 2 10 4 13l13 39-1 1v-1l-1 1c-1-2-2-5-2-7-11-25-17-52-26-77 1-1 1-2 1-3l-3-8z"></path><path d="M515 429l-3 1-1-1-1-3h0v5l-2-4v-7-6c0-3 0-5 1-8 0-2 0-3 1-4 0 2 0 4 1 6l2-12c1 3 0 8 0 11 0 1 0 2 1 3 0-5 0-9 1-13 0 2 0 5 1 8 0 2 0 3 2 4v2h0c1 0 1-1 2-1v1c-1 1-3 2-2 3l1-1h1v1c-1 1-1 0-1 2v7c-1 2-1 4-2 7h-1l-1-1z" class="i"></path><path d="M518 414l1-1h1v1c-1 1-1 0-1 2v7c-1 2-1 4-2 7h-1l-1-1v-7c1-1 0-3 1-4h1c0 1 0 2-1 4h0l1 1 1-1v-8z" class="Q"></path><path d="M552 353c2-1 4-2 6-1 1 0 3 1 5 2l1-1v1c-6 5-8 10-9 17-1 1-1 3-1 4-1 2-5 4-6 5-2 2-3 5-4 7l-2 1c1-2 2-4 2-6v-1-2c-2-2-6-3-9-4v-1c0-1-1-2-2-2v-1c2-1 2-1 4-1l1 1c1 0 1 1 2 1 4 0 6 0 9-1 4-2 6-11 8-15-3 2-5 2-8 3l-1-1-1-1h1c0-2 2-2 4-3v-1z" class="b"></path><path d="M547 357h1c0-2 2-2 4-3v-1h10c-2 1-4 2-5 3-3 2-5 2-8 3l-1-1-1-1zm-14 15v-1c2-1 2-1 4-1l1 1c1 0 1 1 2 1s2 1 2 1c1 0 1 0 3 1h2v1c-1 1-1 2-1 3l2 2c-2 2-3 5-4 7l-2 1c1-2 2-4 2-6v-1-2c-2-2-6-3-9-4v-1c0-1-1-2-2-2z" class="f"></path><defs><linearGradient id="U" x1="466.699" y1="801.652" x2="496.906" y2="810.236" xlink:href="#B"><stop offset="0" stop-color="#2b2d29"></stop><stop offset="1" stop-color="#605d62"></stop></linearGradient></defs><path fill="url(#U)" d="M489 829l-24-62-1-1h0c0-1 0-1-1-1v-1c0-1 0-3-1-4v-1l-3-8 1-1h3c1 1 1 1 2 1h6v1h-7c-2-1-2-1-4-1 0 1 1 2 1 4 1 1 1 2 2 3l1 4v1l1 1c0 1 0 2 1 4 0 1 1 2 1 4 1 1 1 2 2 4 0 1 1 3 2 5 0 1 0 1 1 2h1c0-4-4-7-4-11 1 2 2 3 3 5 2 2 3 4 4 7v1c1 1 2 2 2 4h0c0 1 1 1 1 2v1c1 1 1 2 1 3v1l1 2 2 5 6 15c2 3 4 7 5 11h-1v-1l-1 1 3 6h0c1 2 1 4 1 6-2-4-3-9-5-13v1h-1-1z"></path><path d="M581 672l2-2 1-1 1 1h-1c-1 2-3 5-4 8v1 1l8 3 6 1 21 4c2 0 3 1 4 1v-1c1-4 4-9 6-12 1 1 1 1 1 2-1 4-4 8-6 11l4 3h-1c-2-1-5 0-7 0-3 0-6-1-10-2-6-2-13-2-20-4-2-1-4-2-5-3s-1-1-2-1h-1l-7 3c-2 1-5 2-8 4v1l-1-1 1-1v-1l5-4v-1h-2l-1-1h1l-1-1-2-2h12c1 0 3 1 4 0s2-4 2-6z" class="W"></path><path d="M565 680c1 0 3-1 4 0 2 0 1 1 2 0h3l-1 1 1 1c0-1 1-1 2-2h2c0 1 0 1-1 1l-14 7v-1l5-4v-1h-2l-1-1h1l-1-1z" class="M"></path><path d="M734 432c6-1 15-1 20 0-7 1-14 1-21 2h0c-7 2-12 6-17 10l-14 12c-2 2-4 3-7 4v1l-5 1h-2 0l3-3 8-7 16-14c0-2 1-3 2-4 2 0 4 0 6-1h-1c2-1 4 0 5 0l7-1z" class="J"></path><path d="M722 433c2-1 4 0 5 0-4 1-8 3-12 5 0-2 1-3 2-4 2 0 4 0 6-1h-1z" class="b"></path><path d="M734 432c6-1 15-1 20 0-7 1-14 1-21 2h0c-7 2-12 6-17 10l-14 12v-2c6-5 11-13 19-16l1-1c3-2 7-3 11-4h3l-2-1z" class="D"></path><path d="M571 755c2 0 3 0 3 1l2 2c0 1 0 1-1 2h-1l-1-2-1 1-28 71c-1 2-3 6-3 7l-19 45-1-1 30-75c3-6 5-12 8-18 4-11 9-22 12-33z" class="E"></path><path d="M734 238v1c0 3 0 5 1 7h0-1c0 1 0 2 1 4h0v4l-1 1v1 1c-1 0-2 1-2 2-1 1-1 0-2 1s-2 3-3 4-2 2-2 3c-2 2-3 5-4 7l1 1c1 0 2-2 3-3v-1c0-1 1-2 2-3h1 0c1 2 2 1 4 2 1 1 3 1 4 2-1 0-3-1-4 0 1 1 2 1 3 1-1 2-1 2-3 3h0-2v1l-1 1-1 1h0-1l-1 2h2c-1 2-2 1-2 2-1 1-1 2-1 2h-1v1 2c0 1-1 1 0 2l-1 2-1 1-1 1-1 1h0c-1 0-1 0-1-1l-1 1-1 1v1c-1 1-2 2-2 3-1 2-1 4-3 6 1-6 3-13 5-19 1-1 1-3 2-4s1-2 1-3c1-1 1 0 1-1l1-2v-1c-2 1-4 3-6 4 1-2 2-3 2-5 3-6 6-13 12-17 1 0 2-1 2-1 2-2 2-7 2-9-1-2-1-3-1-5l1-5z" class="M"></path><path d="M727 279c-1 0-3-1-4-2h0c1 0 1-1 1 0l2 1h2c0-2 1-3 0-4h-1v-1h1v-1c-1-1 0-1-1-1l1-3c1 2 2 1 4 2 1 1 3 1 4 2-1 0-3-1-4 0 1 1 2 1 3 1-1 2-1 2-3 3h0-2v1l-1 1-1 1h0-1z" class="I"></path><path d="M717 287l2 1v1l-1 1h1l1-1v1l1 1c1-1 0-1 1-2l1-1-3-2 1-1 3 3c0 1-1 1 0 2l-1 2-1 1-1 1-1 1h0c-1 0-1 0-1-1l-1 1-1 1v1c-1 1-2 2-2 3-1 2-1 4-3 6 1-6 3-13 5-19z" class="B"></path><defs><linearGradient id="V" x1="645.006" y1="625.838" x2="641.721" y2="615.754" xlink:href="#B"><stop offset="0" stop-color="#121313"></stop><stop offset="1" stop-color="#3e3c3d"></stop></linearGradient></defs><path fill="url(#V)" d="M618 621c2-2 7-6 10-7h1v-1c1 0 1 0 2 1l-3 3c-3 1-7 6-10 9h3c3-1 7-4 10-6l2-1c11-5 18-6 29-2-2-2-6-3-8-4h1c3 1 6 2 8 4 3 2 7 3 10 4-1 0-2 0-2 1-6-2-10-3-15-3-4-1-7-1-10 0-2 0-4 1-5 1l-7 3c-2 1-4 2-5 4-2 1-4 1-5 3-1-1-2-1-2-1-1-1-1-1-2-1h-1l-6 1c1-3 2-6 5-8z"></path><path d="M405 696c3-1 6-1 9-1 1 0 2 0 4 1h1c1 0 2 1 4 2l1 1h0l2 2c0 1 1 2 1 3h1 1 0c1-1 0-1 1-1h0v3l-3 3c-2 1-4 1-5 3-1 1-2 1-3 2-3-1-4 1-6 1v-1c0-1-1-3-1-3l-5-13v-1l-2-1z" class="P"></path><path d="M407 697c2 2 6 12 6 15 1 0 0 1 0 1 3 0 6-3 8-4s5-3 6-5h1 1 0c1-1 0-1 1-1h0v3l-3 3c-2 1-4 1-5 3-1 1-2 1-3 2-3-1-4 1-6 1v-1c0-1-1-3-1-3l-5-13v-1z" class="R"></path><path d="M414 695c1 0 2 0 4 1h1c1 0 2 1 4 2l1 1h0l2 2c0 1 1 2 1 3-1 2-4 4-6 5l-1-2h-1v-1c-1 0-1-1-1-2-1-2-1-3-2-4s-1-1-1-2c-1-1-1-2-1-3z" class="C"></path><defs><linearGradient id="W" x1="428.59" y1="661.911" x2="417.797" y2="669.427" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#2e2d2d"></stop></linearGradient></defs><path fill="url(#W)" d="M413 657c1 1 2 3 4 3h1s1 1 2 1 1 0 1-1h1v1c1 1 3 1 4 1l1-1c1 0 1-1 3-1 0 1 1 1 1 2v1c-1 2 1 4 1 5 1 1 1 2 2 2 0 2-1 4-1 6-2 0-3 1-4 1-4-1-7-5-11-6-1 0-1 0-2-1h0-1c-1-1-2-1-3-2h0c2 2 4 3 6 5l-11-7-5-4-2-1 1-1c1 0 3 2 3 2 4 2 8 5 12 7-2-4-4-6-6-10h0l1 1v-1c1-1 1-1 1-2h1z"></path><path d="M558 726h0 1c2 2 3 3 6 4l2 1h1 2c1 0 0 0 1 1 3 1 8-1 11 0l1 1c1 0 1 0 2 1l-1 2h-1v2c-2 1-3 3-3 5 0 1 0 1-1 2h-1v1l1 1h-1c0 1-1 2-1 3s-1 1-1 2v3l-2 1c0-1-1-1-3-1 1-1 1-2 1-3h2c0-2 1-3 1-5 1-1 1-1 1-2-2 1-2 1-2 2-1 3-2 4-4 6h-2l-1 1c-2-1-3-2-4-2l-1-2c2-1 2-4 3-6h0c0-1-1-1-1-1l1-1h2c1-1 2-1 2-3 1-1 4-3 4-4-1-1-1-2-2-2-3 0-5-1-8-2l-3-2c-1-1-2-2-2-3z" class="I"></path><path d="M567 743h1v3l1 1h0c1 0 1 0 2 1v1c-1 1-1 2-2 3h-1l1-2v-1c-2 0-2 2-3 3l-2-1c0-2 1-3 2-5 0-1 0-2 1-3z" class="P"></path><path d="M694 486c0-1 1-1 2-1 5 2 10 4 14 5 1 0 1 0 2 1l-1 1c-5-2-10-4-15-4-4 0-7 2-11 5l-7 5c-1 1-2 2-3 2-4 4-9 9-13 14h-1-1l-1 1c0-1-1-2-1-3h-1-1l-1-1c1-1 2-3 3-4l1-2 3-3 4-4 3-3 3-2 1 1c2 0 3-1 4-2v-1l5-2c1-1 2-1 3-2 2 1 3 1 5 0l4-1z" class="K"></path><path d="M682 489c1-1 2-1 3-2 2 1 3 1 5 0-2 2-5 3-8 4-2 1-4 2-5 3v-1-1-1l5-2z" class="X"></path><path d="M671 501c4-2 8-6 12-8 1-1 2-2 4-3v1c-1 1-2 2-4 3s-3 2-5 4h0c-1 1-2 2-3 2-4 4-9 9-13 14h-1-1l1-1 1-1c0-2 1-2 0-4h-1l1-1 2-2 1 1v1c2-2 5-4 6-6z" class="B"></path><path d="M669 495l3-2 1 1-2 2v2l2-1c-1 1-1 2-2 3v1c-1 2-4 4-6 6v-1l-1-1-2 2-1 1h1c1 2 0 2 0 4l-1 1-1 1-1 1c0-1-1-2-1-3h-1-1l-1-1c1-1 2-3 3-4l1-2 3-3 4-4 3-3z" class="J"></path><path d="M662 502h1l5-2c-2 3-6 7-10 7l1-2 3-3z" class="K"></path><path d="M662 508c1 2 0 2 0 4l-1 1-1 1-1 1c0-1-1-2-1-3h-1-1l1-1c0-1 1-1 1-2 1 0 2 0 4-1z" class="C"></path><path d="M658 512l1-1h1l1 2-1 1-1 1c0-1-1-2-1-3z" class="E"></path><path d="M669 495l3-2 1 1-2 2v2c-1 1-2 1-3 2l-5 2h-1l4-4 3-3z" class="W"></path><path d="M188 195c2 0 3 0 5 1h4c1 0 3-1 3 0l2 1 1-1c1 1 1 3 3 1h0c3 1 5 2 7 4h1c1 0 3 2 4 2h1l2 2 2 2c-2 0-4-1-6-1l-1 1h0l-1 2c-1 0-2-1-3-1h-1-5l-1-1-10-2c-5-2-11-3-16-4h0v-1l-1-1h-6-3c1-1 1-1 3-1h1c4-1 7 0 11 1l2-1h1c1 1 1 1 2 1l1-1-2-1c-1-1-2-1-4-1l4-1z" class="g"></path><path d="M178 199c2 0 3 0 6 1h1l1 1h1l2 1c4 1 10 1 14 4l2 1-10-2c-5-2-11-3-16-4h0v-1l-1-1z" class="S"></path><defs><linearGradient id="X" x1="217.464" y1="202.292" x2="207.933" y2="205.938" xlink:href="#B"><stop offset="0" stop-color="#595857"></stop><stop offset="1" stop-color="#727072"></stop></linearGradient></defs><path fill="url(#X)" d="M211 202c2 0 5 2 7 1h1l2 2 2 2c-2 0-4-1-6-1l-1 1h0c-2-1-3-1-4-2-4 0-7-1-11-2 2 0 4 1 6 0 1 0 2 0 4-1z"></path><path d="M184 196l4-1c2 2 3 2 6 3l1 1c3 1 7 2 9 2 1 0 2 0 3 1h1 3c-2 1-3 1-4 1-2 1-4 0-6 0l-17-4 2-1h1c1 1 1 1 2 1l1-1-2-1c-1-1-2-1-4-1z" class="W"></path><path d="M188 195c2 0 3 0 5 1h4c1 0 3-1 3 0l2 1 1-1c1 1 1 3 3 1h0c3 1 5 2 7 4h1c1 0 3 2 4 2-2 1-5-1-7-1h-3-1c-1-1-2-1-3-1-2 0-6-1-9-2l-1-1c-3-1-4-1-6-3z" class="K"></path><path d="M734 379c8-1 17-2 25-1 11 1 22 7 32 12 4 2 8 4 11 7h1l-7-2c-4-4-11-6-16-8s-10-3-15-3c-1-1-5 0-6-1h-12-27l-6-1-5-1h0c2-1 4 0 7 0s7-1 10-3h1l7 1z" class="k"></path><path d="M587 754h6c2 1 4 1 6 2v1h-1-4v-1h-5l-36 85c-2-1-6 0-9-1h-3v-3c0-1 2-5 3-7 0-1 1-1 2-2 2 0 4 3 6 3h3v-1l2-2c1-1 1-4 2-5l5-13 10-25c2-4 4-8 5-11 3-6 5-14 8-20z" class="U"></path><path d="M461 351c3-1 6 0 10 0 1 2 2 2 4 2 1 2 1 3 0 5l-1 1 2 1h0c-1 2-2 3-2 5 1 1 3 3 5 4s5 0 8 2l2 1v2l-1 1c-3 0-5 0-7 1l-1-2h-2v1c-2 1-2 1-4 0v3c-1-1-5-4-6-5v-3c-2-6-3-11-7-15l-1-1h-1l-1-1 3-2z" class="j"></path><path d="M474 375c-2-1-3-2-5-4l1-1c1 1 2 1 3 1l1 1c1 1 2 2 4 2v1c-2 1-2 1-4 0z" class="U"></path><path d="M460 354c1-1 1-1 3-1 2-1 7 0 10 1v2c-1 1-2 1-4 1-3 0-5-2-8-2l-1-1z" class="k"></path><path d="M474 359l2 1h0c-1 2-2 3-2 5 1 1 3 3 5 4s5 0 8 2l2 1v2l-1 1c-3 0-5 0-7 1l-1-2h-2c-2 0-3-1-4-2l-1-1c-1-1-2-3-2-4 0-2-2-6-3-8h3c1 0 2 1 3 0h0z" class="L"></path><path d="M474 372h2c2 1 2 1 4 1 2-1 4-1 7-2l2 1v2l-1 1c-3 0-5 0-7 1l-1-2h-2c-2 0-3-1-4-2z" class="j"></path><path d="M446 689c1 0 3 0 5 1-1 0-2 1-3 1-1-1-1 0-2 0h-1-2v1h-5v1l1 1v1l1-1h2v1 1 1h1l1 1v-1l4 1h0l-1 1c0 1 0 1-1 2v-1-1h0-1v1h0l-1 1-1-1v1 1c1 0 1 1 3 1h0c0 1 0 1 1 2h-1v1s0 1-1 2l-1 1c-1 1-1 1-1 3-1 0-1-1-2-1 1-1 1-2 1-2v-1h1v-3c-2 1-2 1-4 1h0c-1 0-1 0-2 1v-1h-5-2v-3h0c-1 0 0 0-1 1h0-1-1c0-1-1-2-1-3l-2-2h0l-1-1c-2-1-3-2-4-2v-1h0c-3-2-7-1-9-1 4-2 10-2 15-3 3 0 7 0 9-1 4 0 8 0 12-1z" class="B"></path><path d="M420 695c2-1 6-1 8 0 1 0 1 0 2 1 2 1 5 0 8 1v1 3l-18-6z" class="J"></path><path d="M419 695h1l18 6c2 1 3 1 4 2-1 2-1 2-3 3-1 0-1 0-2 1v-1h-5-2v-3h0c-1 0 0 0-1 1h0-1-1c0-1-1-2-1-3l-2-2h0l-1-1c-2-1-3-2-4-2v-1z" class="S"></path><path d="M426 701l2 1h0l3-1 1 1c-1 0-1 0-1 1l2-1v1h3l1 1v2h-5-2v-3h0c-1 0 0 0-1 1h0-1-1c0-1-1-2-1-3z" class="Z"></path><defs><linearGradient id="Y" x1="311.991" y1="382.62" x2="239.014" y2="382.38" xlink:href="#B"><stop offset="0" stop-color="#d9d8d9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Y)" d="M304 378h5 1c1 1 1 1 2 1v2l-1-1-2 2h0l-8 1-6 1h-25c-4 0-8-1-12 0-3 0-6 0-8 1-8 2-18 5-25 10l-6 2c9-7 21-12 31-16 5-1 9-3 13-3 10 0 20 1 30 0h11z"></path><path d="M392 555c0-1 1-2 1-2 1-1 1-1 1-2h1 1l-1-1v-1l1-2c3 1 5 4 7 7-1 1-1 2 0 2l1 2v1c1 0 2 1 3 1l2 1c1 0 2 0 3 1l2-2c1 1 1 2 0 4v1 3c0 1-1 2-2 2h-1v1c-1 0-1 0-2 1v-1h-3c-1 1 0 1-1 1-3 0-7 0-10-2v-2c0-1-1-1-2-2-1 0-3-3-4-3h-5c2-2 4-3 6-6h0l2-2z" class="P"></path><path d="M404 559c1 0 2 1 3 1l2 1-1 2v-1h-2c-1-1-2-2-2-3z" class="E"></path><path d="M409 561c1 0 2 0 3 1l2-2c1 1 1 2 0 4v1 3c0 1-1 2-2 2h-1v1c-1 0-1 0-2 1v-1l3-3v-2h0c1-1 1-3 0-4-2 1-2 1-4 1l1-2z" class="C"></path><path d="M609 606c1-1 1-1 2-1l1 1c1 0 1 1 2 1 1 1 2 1 2 3l1 1v5l-1 3h0c-1 1-2 3-4 3v1l-1 1h0-1-1c0 1-1 1-1 2-2 0-4 0-4 1-4 0-8-3-11-6v-1c0-1 0-1 1-1v-1h-3l-2 1h-1v1c-1 1-1 1-2 1-2 0-3-1-5-1h0l1-1-1-1c0-1 0-1 1-2h0v-4h1v4h5l1 1c1-1 1-1 3-1 1 0 2-1 3-1 1-1 2-1 3-3h0 3c1-1 2-2 4-2s3 0 4 1v1l2-1v-1-1c-2 0-2 0-2-1l1-2h-1z" class="L"></path><path d="M609 606c1-1 1-1 2-1l1 1c1 0 1 1 2 1 1 1 2 1 2 3l1 1v5l-1 3c-2 1-4 3-6 4h-1v-2l-1-1h0c1-1 2-2 3-4l-1-1v-1c1-1 1-1 1-3v-1-1c-2 0-2 0-2-1l1-2h-1z" class="B"></path><path d="M407 539c2 0 3 1 5 0h7c2 1 3 1 4 2l3 4-1 3 1 3c1 2 1 5 2 7v1c-1 2-1 3-2 4l-6 3-1-1-1 1c-1 0-2-1-4-1v-1c1-2 1-3 0-4l-2 2c-1-1-2-1-3-1l-2-1c-1 0-2-1-3-1v-1h1c1 0 2 1 4 1s3 0 5-1v-1l1-1v-2h1c-1-1-1-1 0-2-2-1-3-2-3-3l-6-5 1-5h-1z" class="T"></path><path d="M420 552h0c2 0 3-1 4-1 0 2 0 2-1 4l1 1-2 2h-2c0-1 0-1-1-2 0 0-1 0-2-1h0l3-3h0z" class="I"></path><path d="M421 543c1 2 2 3 3 5h0c-1-1-1-1-2-1 0 1-1 1 0 2l1 1h0c1-1 1 0 2-1 0 1 0 1-1 2-1 0-2 1-4 1h0-1c-1-1-2 0-3 0 0-1-1-2-1-3h0c1 0 1-1 2 0 1 0 1 0 2-1v-1-1l2-3z" class="h"></path><path d="M416 552c1 0 2-1 3 0h1 0l-3 3h0c1 1 2 1 2 1 1 1 1 1 1 2v1l2-1 1 1c-1 1-1 2-2 3v2h1c1-1 1-2 2-3 1 0 1 1 2 2l-6 3-1-1-1 1c-1 0-2-1-4-1v-1c1-2 1-3 0-4l-2 2c-1-1-2-1-3-1l-2-1c-1 0-2-1-3-1v-1h1c1 0 2 1 4 1s3 0 5-1v-1l1-1v-2h1c-1-1-1-1 0-2z" class="K"></path><path d="M417 555c1 1 2 1 2 1 1 1 1 1 1 2v1c0 1 0 0-1 1v1c-1-1-1-2-2-3v-3z" class="Y"></path><path d="M407 539c2 0 3 1 5 0h7c2 1 3 1 4 2l3 4-1 3h-1c-1-2-2-3-3-5l-2 3v1 1c-1 1-1 1-2 1-1-1-1 0-2 0h0c0 1 1 2 1 3-2-1-3-2-3-3l-6-5 1-5h-1z" class="X"></path><path d="M408 539l2 2h2l1 2h0c-1 0-1 0-1 1h-2v1c1 1 2 2 3 4h0l-6-5 1-5z" class="R"></path><path d="M415 540l8 1 3 4-1 3h-1c-1-2-2-3-3-5-2-1-4-1-6-3z" class="O"></path><path d="M415 549c0-2-1-3-2-5h3v-1l-2-2h-1v-1h2c2 2 4 2 6 3l-2 3v1 1c-1 1-1 1-2 1-1-1-1 0-2 0h0z" class="c"></path><path d="M236 201h3c2 1 3 1 5 2h2v-1l1-1c1 2 3 3 4 5l8 1c1 1 2 1 3 1 3 1 5 3 7 4l1 3 3 3v1l-2 2-2-1c0-1 1-1 1-2l-1-1-1 1-4 5-4-1h0c-2 0-4-1-5-2-2-1-2-2-3-4h0c-2 0-3-1-4-2l-1-2h-2-1c-1 0-3 0-4-1v-1c0-1 0-1-1-1h1l-1-1v-2l-2-1c-1 0-1 0-2 1h-1 0c1-1 2-2 2-3 1-1 1-1 0-2z" class="Y"></path><path d="M240 209c0-1 1-1 1-2 1-1 0-2 1-3v1 1c1 0 1 1 2 1 1 1 1 0 2 1h1v1c0 1 1 1 1 2h1v1h-2-2-1c-1 0-3 0-4-1v-1c0-1 0-1-1-1h1z" class="E"></path><path d="M260 211h-1l-1-1c-1 0-1-1-2-1v1 1c-2 0-3 0-4-1 1-1 0-1 1-2l-2-2h0l8 1c1 1 2 1 3 1 3 1 5 3 7 4l1 3-2-2c-1 0-2 0-2-1-2-1-3-1-6-1z" class="O"></path><path d="M260 211c3 0 4 0 6 1 0 1 1 1 2 1l2 2 3 3v1l-2 2-2-1c0-1 1-1 1-2l-1-1-1 1-4 5-4-1h0c-2 0-4-1-5-2-2-1-2-2-3-4l1-1c1 1 3 1 5 1v-1h-3c0-1 2-1 3-2l2-1c0 1 0 0 1 1v1l1-1-2-2z" class="G"></path><path d="M260 211c3 0 4 0 6 1 0 1 1 1 2 1l-2 1h-2v1c-1-1-2-1-3-1s-2-1-3-1l2-1c0 1 0 0 1 1v1l1-1-2-2z" class="C"></path><path d="M255 220c0-1 1-2 1-3 2 0 4 0 5 2l1-1c2 0 2 0 3-1l1-1h1l1 2-4 5-4-1h0c-2 0-4-1-5-2z" class="H"></path><path d="M403 614l2 3 1-2c1 1 2 3 3 3s2 0 3 1h4 1c2 0 2 1 4 2v1c-3 1-6 3-9 3l-1-1-1 1 1 1c1 0 2 1 3 2l-1 1h0c-1 2-3 1-4 2h1c-4 0-7 1-10-1-1 0-3 0-4-1-2 0-3 0-4-1-1 0-2-1-3-1-2-1-4-2-7-4-3-1-6-3-8-5l-3-1h-3-3l7-3c11 1 21 5 30 11 1 1 1 1 2 1 1 1 2 1 4 1v-2l-6-6c0-2 0-3 1-5z" class="B"></path><path d="M412 619h4 1c0 1-1 3-2 3-2 1-3 1-4 0 1-1 1-2 1-3z" class="E"></path><path d="M406 615c1 1 2 3 3 3s2 0 3 1c0 1 0 2-1 3-3-1-5-3-6-5l1-2z" class="H"></path><path d="M393 626c2 1 4 1 6 2l1 1h1l1 1c0-1 0-1 1-1l-1-1h-1 6c1 0 3-1 3-1-1-1-1-2-2-3l1-1 2 1-1 1 1 1c1 0 2 1 3 2l-1 1h0c-1 2-3 1-4 2h1c-4 0-7 1-10-1-2-1-5-2-7-4z" class="L"></path><path d="M374 618c2-1 2-1 4 0 6 1 10 5 15 8 2 2 5 3 7 4-1 0-3 0-4-1-2 0-3 0-4-1-1 0-2-1-3-1-2-1-4-2-7-4-3-1-6-3-8-5z" class="E"></path><path d="M670 189c5 3 10 10 13 14 3 5 5 11 7 16 3 12 3 24-1 36l-3 9-10 24h-1l-4 10h0c-1 0 0-2 0-2 2-6 4-11 6-16 1-2 1-3 1-4 0-2 1-3 1-5v-1c1-1 2-2 2-3 0-2 0-3 1-4s0-3 0-4v-3-3c0-1 1-2 1-3-1-2 0 0 0-2l-1-1 1-1-1-2-1-3 1-2h0v-1l2-2-1-2 1-3-1-1c0-1 1-2 2-4l-2-5v-5l6 18v1c1 3 1 11 0 14v1c2-6 3-14 2-21-2-16-9-28-21-40z" class="D"></path><path d="M685 248h1c1 1 1 1 1 2v1c-1 0-1 1-1 2l1 2c0 1-1 2-1 4 0 1-1 3-1 5s-2 6-3 8h-1v-1c1-2 2-4 3-7 0-2 1-4 0-7 0-1 1-2 0-3v-3l1-3z" class="I"></path><path d="M683 216l6 18v1c1 3 1 11 0 14v1 4l-4 10c0-2 1-4 1-5 0-2 1-3 1-4l-1-2c0-1 0-2 1-2v-1c0-1 0-1-1-2h-1l-1 3-2-4 1-1-1-2-1-3 1-2h0v-1l2-2-1-2 1-3-1-1c0-1 1-2 2-4l-2-5v-5z" class="G"></path><path d="M685 230c0 1 2 2 2 3 0 7 2 14 0 21v1l-1-2c0-1 0-2 1-2v-1c0-1 0-1-1-2 0-1 0-1 1-2 0-3-1-5-1-8v-5l-1-3z" class="D"></path><path d="M685 226v4l1 3v5c0 3 1 5 1 8-1 1-1 1-1 2h-1l-1 3-2-4 1-1-1-2-1-3 1-2h0v-1l2-2-1-2 1-3-1-1c0-1 1-2 2-4z" class="T"></path><path d="M682 239v-1l2-2c1 2 1 3 1 5 0 1-1 6 0 6v1l-1 3-2-4 1-1-1-2-1-3 1-2h0z" class="K"></path><path d="M681 241l1-2h0c1 2 2 4 2 6l-1 1-1-2-1-3z" class="h"></path><path d="M402 605c2-1 3-1 5-1-1 1-2 1-2 2-2 3-2 4-2 8-1 2-1 3-1 5l6 6v2c-2 0-3 0-4-1-1 0-1 0-2-1-9-6-19-10-30-11l-7 3-8 2h-1l1-1c2-2 4-3 7-3l10-5 8-1-1 1c2 0 7-2 9-3l3-1h2c2-1 5-1 7-1z" class="c"></path><path d="M390 607l3-1v3l-1 1c0 1 1 1 1 2h0v1h1c2 1 4 4 5 6-2-1-4-3-6-4-1 0-1 0-2-1-1 0-2-1-3-1v-1l2-1 2-2v-1l-2-1z" class="h"></path><path d="M364 615c2 0 3 0 5-1 3-2 7-3 10-3h2c-1 1-3 2-5 3h-3-1l-7 3-8 2h-1l1-1c2-2 4-3 7-3z" class="X"></path><path d="M402 605c2-1 3-1 5-1-1 1-2 1-2 2-2 3-2 4-2 8-1 2-1 3-1 5l6 6v2c-2 0-3 0-4-1-1 0-1 0-2-1l2-1c-2-2-3-4-5-5-1-2-3-5-5-6h-1v-1h0c0-1-1-1-1-2l1-1v-3h2c2-1 5-1 7-1z" class="K"></path><path d="M393 606h2 2l1 1c-1 1-2 1-3 2v1h0l2 1v1h-4 0c0-1-1-1-1-2l1-1v-3z" class="W"></path><path d="M402 605c2-1 3-1 5-1-1 1-2 1-2 2-2 3-2 4-2 8-1 2-1 3-1 5-1-1-2-2-3-4l1-2c0-1-1-2-1-2v-1-2c1 0 2-1 3-2v-1z" class="G"></path><path d="M681 470h0l2 1c1-1 3-1 5 0h2c5 0 11 3 15 6 1 0 2 1 3 1-1 1-2 1-3 1v1l-2 1h3c-1 1-1 1-1 2l-9 2c-1 0-2 0-2 1l-4 1c-2 1-3 1-5 0-1 1-2 1-3 2l-5 2v1c-1 1-2 2-4 2l-1-1 3-3 1-1v-1c1-2 2-3 2-5h1l1-7v-2l1-4z" class="U"></path><path d="M682 479v-1c2-2 3-3 6-3 0 2-1 2-2 3h-1c-1 0-1 0-2 1h-1z" class="Q"></path><path d="M705 477c1 0 2 1 3 1-1 1-2 1-3 1h-5c1-2 3-2 5-2z" class="j"></path><path d="M683 479v1c1 1 2 1 3 3h0c-1 1-1 2-2 3l-2 2v1l-5 2v1c-1 1-2 2-4 2l-1-1 3-3 1-1v-1c1-2 2-3 2-5h1 1l2-3v-1h0 1z" class="a"></path><path d="M683 479v1c1 1 2 1 3 3h0c-1 1-1 2-2 3l-2 2v1l-5 2h0 0c0-3 4-5 6-7l-1-2 1-2-1-1h0 1z" class="j"></path><path d="M700 479h5v1l-2 1h3c-1 1-1 1-1 2l-9 2c-1 0-2 0-2 1l-4 1c-2 1-3 1-5 0-1 1-2 1-3 2v-1l2-2c3-2 6-5 10-6h0c2-1 4-1 6-1z" class="Q"></path><path d="M685 487c2-1 3-2 5-3l4 2-4 1c-2 1-3 1-5 0z" class="V"></path><path d="M703 481h3c-1 1-1 1-1 2l-9 2c-1 0-2 0-2 1l-4-2c4-2 8-3 13-3z" class="a"></path><path d="M732 245c0-2-1-4 0-6v-2l1-1v-1-2l2-6c1-1 2-3 3-4l4-7v-1l10-10c2-1 2-2 5-2-4 3-7 6-10 9-6 6-10 14-12 22l-1 4-1 5c0 2 0 3 1 5 0 2 0 7-2 9 0 0-1 1-2 1-6 4-9 11-12 17 0 2-1 3-2 5-1 3-2 6-4 8l-1-1c1-1 1-2 1-3 1-1 1-1 1-2 1-1 1-1 1-2l-1-1 1-2c0-2 1-3 2-5h0v-1l1-4c0-1 2-4 2-5 1-2 3-5 4-7 0-1-1-2-1-4 0 0 1-2 1-3 2-6 4-11 7-17 1 2 1 2 1 4-1 2-1 3-1 4 1 2 1 3 0 5l2 1z" class="e"></path><path d="M730 244l2 1v7c1 1 0 2-1 3l-2 2c-1-1-1-1-1-3l3-3-1-1h-2v-1h1l1-1c1-1 0-3 0-4z" class="J"></path><path d="M723 255l1-2v2h2c1 0 2-1 2-1 0 2 0 2 1 3-3 2-6 2-9 4l-1 1c1-2 3-5 4-7z" class="W"></path><path d="M730 231c1 2 1 2 1 4-1 2-1 3-1 4 1 2 1 3 0 5 0 1 1 3 0 4l-1 1h-1v1h2l1 1-3 3s-1 1-2 1h-2v-2l-1 2c0-1-1-2-1-4 0 0 1-2 1-3 2-6 4-11 7-17z" class="F"></path><path d="M730 239c1 2 1 3 0 5 0 1 1 3 0 4l-1 1c-1-1-2-2-2-3l3-7z" class="f"></path><path d="M727 246c0 1 1 2 2 3h-1v1h2l1 1-3 3s-1 1-2 1h-2v-2c1-2 2-5 3-7z" class="e"></path><path d="M658 518c4 1 9 2 13 3l1-1 2 1v2l-1-1v1h0c1 2 0 4-1 6v2s-1 1-1 2 0 2 1 3v1h1c5 2 10 4 14 7 5 2 11 6 16 7h3v1h0c-3-1-5-1-8 0-2 0-5-1-7-1l-3-3-12-2c-2-1-4-1-6-2h-1 0l-2-1h1l2-1-1-1h-4c-1-1-1-1-1-2h-1l-1-1c-1-1-1-3-1-4l-1-1c0-2 0-2-1-4h0l1-2v-2c-1-2-2-5-2-7z" class="b"></path><path d="M688 548h2c3 1 6 2 8 4-2 0-5-1-7-1l-3-3z" class="W"></path><path d="M669 531h0 2v2c0 1 0 2 1 3v1c-3-1-5-2-8-4l1-2c1 1 2 1 3 1l1-1z" class="E"></path><path d="M660 533c0-2 0-2-1-4h0l1-2c0 1 1 2 1 3v2c1 2 4 3 5 5 1 1 3 2 5 2 3 2 7 3 10 4 1 1 3 2 4 2l5 3h-2l-12-2c-2-1-4-1-6-2h-1 0l-2-1h1l2-1-1-1h-4c-1-1-1-1-1-2h-1l-1-1c-1-1-1-3-1-4l-1-1z" class="e"></path><path d="M661 534c1 1 3 2 3 4v1h-1l-1-1c-1-1-1-3-1-4z" class="W"></path><path d="M676 546c-2-1-4-1-6-2h-1 0l-2-1h1c1 0 8 1 9 0h0 1c2 1 5 2 7 3v-1l5 3h-2l-12-2z" class="g"></path><path d="M658 518c4 1 9 2 13 3l1-1 2 1v2l-1-1v1h0c1 2 0 4-1 6v2s-1 1-1 2v-2h-2 0l-1 1c-1 0-2 0-3-1l-1 2-3-3c0-1-1-2-1-3v-2c-1-2-2-5-2-7z" class="M"></path><path d="M665 531c-1-3-2-1-4-3 1-1 1-2 2-3l-2-2h0v-2c2 0 4 1 6 1h0v2l2 1v6l-1 1c-1 0-2 0-3-1z" class="P"></path><path d="M572 655l1 1c4 3 8 5 14 7h3c3 1 8 2 11 0h0v1h0c1 1 2 1 3 2l5-1 3-1 1 1-6 3h0l-4 3c-3 0-4 0-6 1-1 1-3 1-4 2-2 1-3 2-6 2-1 0-1 1-2 1l-3 1c-1 1-1 1-2 1v-1c1-3 3-6 4-8h1l-1-1-1 1-2 2c0 2-1 5-2 6h0c-1-1-1-2-1-2 0-3-7-4-8-6 0-1 1-2 1-3 1-1 0-2 1-3h0l1-1-3-3 1-1v-1l1-2v-1z" class="U"></path><path d="M588 670l1 1 18-3h0l-4 3c-3 0-4 0-6 1-1 1-3 1-4 2h-6v-1l1-1h-2c-1 1 0 1-1 0h0c0-1 1-1 1-2h2z" class="I"></path><path d="M572 655l1 1c0 1 0 1-1 2 6 5 12 7 20 7l-2 2h-3 0-6v1c-1-1-2-1-3-2h-1c-1-1-2-1-2-2l-1-1h-1l-3-3 1-1v-1l1-2v-1z" class="T"></path><path d="M573 663h1l1 1c0 1 1 1 2 2h1c1 1 2 1 3 2v-1h6 0 3l2-2c3 0 6 0 9 2l-13 3h-2c0 1-1 1-1 2h0c1 1 0 1 1 0h2l-1 1v1h6c-2 1-3 2-6 2-1 0-1 1-2 1l-3 1c-1 1-1 1-2 1v-1c1-3 3-6 4-8h1l-1-1-1 1-2 2c0 2-1 5-2 6h0c-1-1-1-2-1-2 0-3-7-4-8-6 0-1 1-2 1-3 1-1 0-2 1-3h0l1-1z" class="F"></path><path d="M570 670h0c1-1 3-1 3-2l1-1h0v1c1 1 0 0 2 0l1 1v-1h3 1l-2 2 1 1c1 0 1 0 1 1 0 2-1 5-2 6h0c-1-1-1-2-1-2 0-3-7-4-8-6z" class="C"></path><path d="M475 328c4-7 9-14 14-21l12-20c2-3 4-7 6-9v3l-1 2-6 15c-2 6-5 12-7 17-4 9-7 18-14 25h-1v-2l1-5c-1 0-2-1-2-2l-2-3z" class="B"></path><path d="M599 721h2l1 1c-1 1-2 1-2 3h0 1l1-1v2h1c3-1 8 1 11 1-4 1-7 2-11 2-1 1-3 5-3 7-1 1-1 4-2 5-2 3-4 7-4 10l-1 3h-6-3c-2 0-3-1-5-2l-1-1v-4h1l-1-1v-1h1c1-1 1-1 1-2 0-2 1-4 3-5v-2h1l1-2h0l1-1v1h1l1-1h3c0-1 2-1 2-1 0-1 1-1 1-2h2 1l1-2v-1l-4 1c-3 0-6 0-9-1h1c4 0 9 0 12-1v-2-1l1-2z" class="b"></path><path d="M579 747c1 1 2 1 3 2h1c1 0 2 0 3 1 2 1 5 0 8 1l-1 3h-6-3c-2 0-3-1-5-2l-1-1v-4h1z" class="H"></path><path d="M598 741c-2 0-2 4-3 6-1 0-2 1-2 2h-1l-1-1 5-14 3-6c0-1 0-1 1-2l1 1h1v1c-1 3-2 5-3 7l1 1c-1 1-1 4-2 5z" class="B"></path><path d="M593 732c0-1 1-1 1-2h2l-5 13-3 6h-3-2-1c-1-1-2-2-3-4 1-1 1-1 1-2 0-2 1-4 3-5v-2h1l1-2h0l1-1v1h1l1-1h3c0-1 2-1 2-1z" class="M"></path><path d="M580 743h4c1 1 0 2 1 3l2 2 1 1h-3-2-1c-1-1-2-2-3-4 1-1 1-1 1-2z" class="E"></path><path d="M593 732v2c-1 1-2 4-2 5l-1 1v-1c-1 1-1 2-1 2h-3-1-1l1-2 3-3-1-2 1-1h3c0-1 2-1 2-1z" class="I"></path><path d="M586 741c0-1 1-1 1-2 1-1 2-1 2-2l2-2v2l-1 2c-1 1-1 2-1 2h-3z" class="P"></path><defs><linearGradient id="Z" x1="613.451" y1="605.616" x2="638.185" y2="615.529" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#686768"></stop></linearGradient></defs><path fill="url(#Z)" d="M616 600l8 3c6 0 12 3 18 4 3 1 6 2 10 4l3 2h-1c2 1 6 2 8 4-11-4-18-3-29 2l-2 1c-3 2-7 5-10 6h-3c3-3 7-8 10-9l3-3c-1-1-1-1-2-1v1h-1c-3 1-8 5-10 7 1-3 2-5 2-7 0-1 0-2-1-3 0-1-1-1-2-1v1l-1-1c0-2-1-2-2-3h2 1l1-1-3-2v-1c1-1 1-2 1-3z"></path><path d="M624 603c6 0 12 3 18 4 3 1 6 2 10 4-9-1-22-3-28-8z" class="a"></path><path d="M799 195h0c2 1 2 0 3 0 1-1 1 0 2 0 1 2 0 3 1 5h0v1l-1 2v3c-2 1-4 2-6 2-1 0-1 1-2 1l-1 5h1l2 1-6-1c-1 1-2 1-3 2l1 1h0c-2 0-4-1-6-2s-4-3-6-4l-2 2h-8c2-1 2-1 5-1v-1h-1l2-3-1-2-1 2c-2 0-3 0-4-1h1v-1-1c1 0 2 0 2-1h1l1 1v-2h2s1-1 2-1l1-1s1-1 2-1l2 1c0-1 0-1 1-1l1-1h2c1-1 1-1 3-1h3c1 1 2 1 3 2 1 0 1 0 2 1 0 0 1 1 1 2h2c-1-1-1-2-2-2 0-2 0-4 1-5v-1z" class="P"></path><path d="M799 195h0c2 1 2 0 3 0 1-1 1 0 2 0 1 2 0 3 1 5h0v1l-1 2c-1 1-3 3-4 3s-3 0-4 1l-2-2h5c1-1 2-1 2-2-2-3-2-4-2-8z" class="B"></path><path d="M794 205l2 2c1-1 3-1 4-1s3-2 4-3v3c-2 1-4 2-6 2-1 0-1 1-2 1l-1 5h1l2 1-6-1c-1 0-2 0-3 1h-1s0-1-1-1v-3c0-2-1-3-2-4h0l1-1h0l1-1c1-1 1-1 2-1s1 0 2-1h0l3 2z" class="G"></path><path d="M370 522l-3 3-1 2v2l1 1v4c2 1 3 2 4 4v3c1 1 1 1 2 1l-1 3h-6-4 0l-11 2c-5 1-11 2-17 3l-4 1c-5 1-10 0-15 1h0c1-1 4-1 6-1 9-5 18-11 28-13 5-2 9-4 12-7 0-1 2-2 2-3l3-3 4-3z" class="X"></path><path d="M370 522l-3 3-1 2-1 1v1 2c-1 2-1 3-1 4v2 2 2h2v2c-1 0-1 0-3 1 0-2 0-3 1-4l-1-1-1 1-2-1v-1c0-1 1-2 2-3s0-2 0-4c0 0 2-1 2-2l-1-1 3-3 4-3z" class="h"></path><path d="M366 527v2l1 1v4c2 1 3 2 4 4v3c1 1 1 1 2 1l-1 3h-6-4 0l-11 2 3-2c2 0 3 0 5-1 1 0 3-1 4 0 2-1 2-1 3-1v-2h-2v-2-2-2c0-1 0-2 1-4v-2-1l1-1z" class="D"></path><path d="M366 529l1 1v4c2 1 3 2 4 4v3c1 1 1 1 2 1l-1 3h-6l1-1c1-1 2-1 3-1h1 1c-3-1-4-3-5-5v-1h-2c2-3 1-5 1-8z" class="I"></path><path d="M361 531c0 1 0 3-1 4s-3 2-4 3c-4 2-9 3-14 4-4 2-9 5-14 7l-3 2h-4c9-5 18-11 28-13 5-2 9-4 12-7z" class="f"></path><path d="M328 549h2c1 1 2 0 3-1 3-2 6-4 10-5 2 0 6-2 9-1h2l1-1c1 0 2 0 3 1v1l-2 1h3c-2 1-3 1-5 1l-3 2c-5 1-11 2-17 3l-4 1c-5 1-10 0-15 1h0c1-1 4-1 6-1h4l3-2z" class="a"></path><path d="M356 544h3c-2 1-3 1-5 1l-3 2c-5 1-11 2-17 3l1-2c4-2 11-2 16-3h1c2-1 3-1 4-1z" class="S"></path><path d="M842 193l2 1h-2l1 1 1 1c-1 0-2-1-3 1h1v1 1c4 0 9-1 13 0-3 1-5 0-7 2-8 0-17 4-23 6l-4 1c-2 1-5 2-7 2-3 0-5 2-8 3l-8 2-2-1h-1l1-5c1 0 1-1 2-1 2 0 4-1 6-2v-3l1-2h3 0l1-1 7-2h0l1-1 2 1 1-1c1 0 1-1 3-1 2-1 4-1 6-1h1c4-1 7-2 12-2z" class="W"></path><path d="M829 195h1l-2 2h-1c-1 0 0 1-1 1 0 1-1 2-2 2-2 1-4 3-7 3l1-1c0-1 1-2 1-3 1 0 2 0 3-1h3c0-1 0-1 1-2 1 0 2-1 3-1z" class="D"></path><path d="M816 198h0l1-1 2 1 1-1c1 0 1-1 3-1 2-1 4-1 6-1-1 0-2 1-3 1-1 1-1 1-1 2h-3c-1 1-2 1-3 1 0 1-1 2-1 3l-1 1c-2 0-4 2-6 2 0-1-1-1-2-1 3-3 5-3 7-6z" class="I"></path><path d="M842 199c-1 1-2 1-3 2l-11 2c-1 1-4 2-6 1l2-1 1-1h1l3-3-1-1c4-2 8-2 13-3l1-1 1 1 1 1c-1 0-2-1-3 1h1v1 1z" class="c"></path><path d="M809 200l7-2c-2 3-4 3-7 6h0l-3 3c-2 1-4 3-7 3 0 1-1 1-1 1l1 1c6 1 11-6 18-5 2 0 5-3 7-2l-2 2c-1 0-1 0-2 1h1c-2 1-5 2-7 2-3 0-5 2-8 3l-8 2-2-1h-1l1-5c1 0 1-1 2-1 2 0 4-1 6-2v-3l1-2h3 0l1-1z" class="T"></path><defs><linearGradient id="a" x1="576.353" y1="653.41" x2="599.581" y2="641.029" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#302e2f"></stop></linearGradient></defs><path fill="url(#a)" d="M593 629h0c0 2 1 3 1 4 3 2 5 3 7 4l10 3h-3c-3 1-8-1-11 1v1l2 1h1l-2 1-3 2c-1 0-1 1-1 1-1 1-1 1-1 2-2 1-2 2-2 4h-1v3h0c-1 1-1 1-1 2l-1-1-1 1c0 1 0 2 1 3l-1 2c-6-2-10-4-14-7l-1-1h2l1 1h2v-1-2c-1-3 0-6 1-9h1l-1-1h-1l-1-1 2-1c0-1 1-2 2-4l3-3 7-4h1l2-1z"></path><path d="M580 637l3-3c1 2 1 3 1 5h0c0 2-1 2-2 3 0-1 0-1-1-2l-1-3z" class="C"></path><path d="M593 629h0c0 2 1 3 1 4 3 2 5 3 7 4l-2 1c-2 0-3 0-5-1h-3v-1-1l-2 2-1 1-1-1v1h-1c-1 1-1 1-2 1 0-2 0-3-1-5l7-4h1l2-1z" class="D"></path><path d="M593 629h0c0 2 1 3 1 4-1 0-2 0-4-1 0-1 1-1 1-2l2-1z" class="J"></path><path d="M590 630h1c0 1-1 1-1 2v1c-1 1-2 1-4 2-1 1-1 1-1 2 1 0 2-1 3 0h1l-1 1-1-1v1h-1c-1 1-1 1-2 1 0-2 0-3-1-5l7-4z" class="G"></path><defs><linearGradient id="b" x1="494.903" y1="422.859" x2="476.448" y2="436.576" xlink:href="#B"><stop offset="0" stop-color="#11100f"></stop><stop offset="1" stop-color="#404040"></stop></linearGradient></defs><path fill="url(#b)" d="M493 464c-3-11-6-23-12-34-1-4-3-7-5-10-3-3-7-5-10-8-3-2-4-4-4-6s-1-4 1-6c0 1 1 1 1 2h0c0 2 1 4 2 5l2 1 1 1c2 0 5-1 6-2h1l1-1c2 0 3 0 5-2 1 1 1 3 2 4 1 4 2 9 4 12 2 4 2 8 2 11 1 4 2 7 3 11v1 1 1 4 2c-1-2-2-7-3-10v-1h0c1 5 6 20 3 24z"></path><path d="M476 410c2 0 4 3 5 4v2h-1v1c-2 0-2-1-3-2s-1-2-2-3h0l1-2z" class="C"></path><defs><linearGradient id="c" x1="685.442" y1="425.569" x2="703.142" y2="441.922" xlink:href="#B"><stop offset="0" stop-color="#686666"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#c)" d="M681 431l2-1v1c2 0 4 0 6 1 6 1 12 1 18 1 2 1 5 1 7 1l2-1h6 1c-2 1-4 1-6 1-1 1-2 2-2 4l-16 14-8 7-3 3-4 2h-1c1-1 1-1 1-2 1-3 2-5 4-7 1-4 2-6 2-10 0-1 0-2-1-3v1c0-1 0-1-1-2l-1-3h-1 0l-3-3c-1 0-2-1-2-1l-5-1c1 0 2 0 3-1v-1h2z"></path><path d="M681 434v-1h1 0 1c1 1 2 1 4 1l1 2h-1c0 1 0 1-1 2h0l-3-3c-1 0-2-1-2-1z" class="h"></path><path d="M691 459l-1-2c3-4 6-7 10-10-1 2-1 3-1 5l-8 7z" class="Q"></path><path d="M700 447c5-5 11-10 17-13-1 1-2 2-2 4l-16 14c0-2 0-3 1-5z" class="d"></path><path d="M516 284c0-2 0-3-1-5l1-1 17 27 6 10c-1 2-2 2-1 5 0 1 2 3 3 5 1 1 1 4 3 5 1 3 2 6 2 9l1 2h-2c-1 0-1-1-2-2-2 1-4 1-6 1-1 0-2 0-3 1l-3 1c1-1 1-2 2-4h1c0-3 0-5-1-7 0-4-2-7-3-11l-7-18c0-1-1-2-1-2l-3-9-3-7z" class="f"></path><path d="M535 317c1 1 1 2 1 3l3 6c2 5 4 9 7 13l1 2h-2c-1 0-1-1-2-2l-1-1c-1 0-2-1-2-1-1-2-2-3-3-4l1-1h1 0l-2-8c-1-1-1-1-1-2-1-2-1-3-1-5z" class="b"></path><path d="M533 305l6 10c-1 2-2 2-1 5 0 1 2 3 3 5 1 1 1 4 3 5 1 3 2 6 2 9-3-4-5-8-7-13l-3-6c0-1 0-2-1-3-1-3-3-7-4-10l1 1h2c-1-1-1-2-1-3z" class="R"></path><path d="M541 795s5-12 6-13l7-14c1-3 2-9 4-10l-11 36-7 20c-2 3-3 6-4 9l-21 52c-1-2-1-6-1-8 1-4 2-8 4-11l2-5 11-29 10-27z" class="U"></path><path d="M524 379c1-1 1-2 2-3 3-1 6-1 8 0l1-1c3 1 7 2 9 4v2 1c-2 4-4 10-8 13h0c0 1-1 2-2 3 0 2 0 4-1 6 0 3-2 6-2 10v1 5c0 1-1 2-1 3l-1-1-1 1c-1-4-2-8-3-13v-4-1c-1-2 0-5 0-8-1-1-1-3-2-4v-1c-2-5-1-8 1-13z" class="G"></path><path d="M532 385c0-1 1-3 2-4 0-1 1-1 2-1l1 1-1 1h2 0l-1 3-1-1c-1 0-3 0-4 1z" class="K"></path><path d="M529 408c1 2 1 3 1 5l1 1v1 5c0 1-1 2-1 3l-1-1c-1-5-1-9 0-14z" class="e"></path><path d="M524 379c1-1 1-2 2-3 3-1 6-1 8 0 0 2 0 3-2 5-2 1-2 4-3 5s-1 2-2 3l-1 1-1-1c0-1-1-1-1-2v-4-4z" class="H"></path><path d="M537 385v2h0c2-2 4-5 7-6v1c-2 4-4 10-8 13h0c0 1-1 2-2 3 0 2 0 4-1 6 0 3-2 6-2 10l-1-1c0-2 0-3-1-5-2-5-1-14 2-19 0-1 1-3 1-4 1-1 3-1 4-1l1 1z" class="W"></path><defs><linearGradient id="d" x1="397.377" y1="639.916" x2="391.317" y2="655.243" xlink:href="#B"><stop offset="0" stop-color="#2b292a"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#d)" d="M424 634h0c2-2 4-4 6-4v1 4h4 0 0l-2 1h-1s-1 0-1 1h0 1 1l-1 2c-1-1-1-1-2-1s-2 0-3 1h-1-1 0-2c0 1-1 1-1 1h-1-1 1l-1 1-1-1c-2 1-4 1-6 1v1c-2-1-2 0-4 0h-2c-1 1-2 1-3 1v1l1 1h1 1c1-1 2-1 3-1v1h2 2s0-1 1-1 2-1 2-1l1 1h-2l-2 2v2l2-2h0c0 2-1 2-2 3 0 1-1 3-1 4v2l1 2h-1c0 1 0 1-1 2v1l-1-1h0c2 4 4 6 6 10-4-2-8-5-12-7 0 0-2-2-3-2-1-1-3-2-4-3l-6-2-10-5c-1 0-1-1-3-1h-1-1c-2 0-2 0-3-1h2c1-1 3-1 4-3l1-1 26-3c3 1 8-1 12-2 2-1 6-3 6-5z"></path><path d="M424 634h0c2-2 4-4 6-4v1c0 1-1 1-1 1v4h0c-1 1-2 1-3 1-3 1-5 2-8 2 2-1 6-3 6-5z" class="D"></path><path d="M728 268c1 0 3 0 4 1l7 2 11 5c1-1 2-1 2-1 1 1 2 1 3 1h1c1-1 1-1 2-1s2-1 3-2v3h2c-1 2-1 2-2 3h-2c1 1 2 1 3 2v1l4 2c3 2 10 4 12 6v2h-1-7c-4-1-7-3-12-2-2 0-4 0-5-1-2-1-4-1-5-1l-2-1-2 2v1l-1-1-3-1h0c-2-2-3-2-4-4v-2c-2 1-1 2-1 4h0c-1-1-2-1-3-1l-4 2v1h-1v-2h0c-1 0-2 0-2-1 0 0 0-1 1-2 0-1 1 0 2-2h-2l1-2h1 0l1-1 1-1v-1h2 0c2-1 2-1 3-3-1 0-2 0-3-1 1-1 3 0 4 0-1-1-3-1-4-2-2-1-3 0-4-2h0z" class="V"></path><path d="M736 272h1c1 1 2 1 3 1v1h0c-2-1-2-1-4-1 1 1 1 1 2 1l2 2c-1 0-4 0-6 1h-1l-1-1h0c2-1 2-1 3-3-1 0-2 0-3-1 1-1 3 0 4 0z" class="W"></path><path d="M758 275c1 0 2-1 3-2v3h2c-1 2-1 2-2 3h-2c1 1 2 1 3 2v1l-12-6c1-1 2-1 2-1 1 1 2 1 3 1h1c1-1 1-1 2-1z" class="D"></path><path d="M743 279c2 0 4 0 6 1 2 0 3 1 5 2 1 0 1 0 2 1l4 2c1 0 1 0 2 1 2 0 4 1 6 1 2 1 5 2 6 2l3 2 1-1v2h-1-7c-4-1-7-3-12-2-2 0-4 0-5-1-2-1-4-1-5-1 0-1 0-2 1-3 0-1 1-2 1-3-3-1-3-3-6-3h-1z" class="S"></path><path d="M758 290c1-1 2-1 3-1 0-2-2-2-3-3h-1c6 0 11 3 17 3l3 2 1-1v2h-1-7c-4-1-7-3-12-2z" class="B"></path><path d="M730 277v-1h2l1 1h1c2 0 5 0 7 1l2 1h1c3 0 3 2 6 3 0 1-1 2-1 3-1 1-1 2-1 3l-2-1-2 2v1l-1-1-3-1h0c-2-2-3-2-4-4v-2c-2 1-1 2-1 4h0c-1-1-2-1-3-1l-4 2v1h-1v-2h0c-1 0-2 0-2-1 0 0 0-1 1-2 0-1 1 0 2-2h-2l1-2h1 0l1-1 1-1z" class="K"></path><path d="M730 277v-1h2l1 1-1 1v2l-2 2-1-1 1-2v-2z" class="T"></path><path d="M732 285v-1c0-2 1-4 2-5 1 0 1 1 2 1v1l2-1 1 1v1h-1l-1 1-1 1v-2c-2 1-1 2-1 4h0c-1-1-2-1-3-1z" class="C"></path><path d="M741 278l2 1h1v1h-2c-2 2-2 3-2 6h1c0 1 0 2-1 2h0c-2-2-3-2-4-4l1-1 1-1h1v-1l-1-1-2 1v-2c1-1 3 0 5-1z" class="F"></path><path d="M744 279c3 0 3 2 6 3 0 1-1 2-1 3-1 1-1 2-1 3l-2-1-2 2v1l-1-1-3-1c1 0 1-1 1-2h-1c0-3 0-4 2-6h2v-1z" class="D"></path><path d="M741 286v-1l1-1v-3h3c1 0 1 1 2 1 0 1 0 2-1 3h0c-1 1-2 3-3 4l-3-1c1 0 1-1 1-2z" class="Y"></path><defs><linearGradient id="e" x1="718.346" y1="381.908" x2="730.574" y2="396.086" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#e)" d="M714 382l6 1h27v1c-1 1-2 1-3 2l-1 1v1l1 1v1l-6 4c-1 1-2 2-3 2l-7 7-4 4s-2 2-2 3c-1 1-5 4-5 6l-3 3-1-2c0-2-1-4 0-6l1-4c1-2 3-7 2-10l-1-1v-5l-1-1v-1h0 1c1-2 0-3 0-4h-1v-3z"></path><path d="M714 382l6 1-3 1c-1 2 1 2 0 3l-1-1-1-1h-1v-3z" class="g"></path><path d="M729 397c3-3 10-8 15-8v1l-6 4c-1 1-2 2-3 2v-1c-2 1-4 2-6 2z" class="f"></path><defs><linearGradient id="f" x1="715.216" y1="408.717" x2="736.284" y2="398.783" xlink:href="#B"><stop offset="0" stop-color="#bdbcbb"></stop><stop offset="1" stop-color="#eeeded"></stop></linearGradient></defs><path fill="url(#f)" d="M729 397c2 0 4-1 6-2v1l-7 7-4 4s-2 2-2 3c-1 1-5 4-5 6l-3 3-1-2c0-2-1-4 0-6l1-4h7l2-5c2-1 4-4 6-5z"></path><defs><linearGradient id="g" x1="404.494" y1="600.037" x2="383.777" y2="577.711" xlink:href="#B"><stop offset="0" stop-color="#0a0909"></stop><stop offset="1" stop-color="#2f2d2e"></stop></linearGradient></defs><path fill="url(#g)" d="M396 575h1c2 2 6 1 7 5v1h2c1 2 2 3 3 4v-2c1-1 1-1 2-1 1 1 1 0 1 2h0 0l1 1v2c0 1 0 0 1 1v2l-2-1-1 1c0 1 0 3 1 4l-1 1h0l-22 8h0l-5 2-1-3-4-4c-3-4-6-7-9-10h3l2 2 1 1c2 0 2 0 4-1-2-2-3-3-3-5v-1l1 1c1 1 1 2 2 2 2 2 5 2 8 2l7-6-1-1 1-1c2-2 1-1 1-3v-3z"></path><path d="M380 590c2 2 7 6 8 9l-1 1-1-1h-1l-3-3c-2-1-4-3-6-5 2 0 2 0 4-1z" class="C"></path><path d="M370 588h3l2 2 1 1c2 2 4 4 6 5l3 3h-1c-2-1-5-4-6-5h0c1 2 5 5 5 6l1 1v1h-1l-4-4c-3-4-6-7-9-10z" class="L"></path><defs><linearGradient id="h" x1="744.348" y1="328.341" x2="757.293" y2="350.542" xlink:href="#B"><stop offset="0" stop-color="#7c7a7a"></stop><stop offset="1" stop-color="#a2a0a1"></stop></linearGradient></defs><path fill="url(#h)" d="M733 332c5 1 9 2 13 1 9-1 17-7 26-11-1 1-1 2-2 3s-1 2-2 3c-1 3-3 5-4 7-5 9-11 16-18 23l-7 8v-1-1l-1-1c-1 0-3 0-4-1 0-1 0-3 1-4h1c-1-2-2-2-2-4 2-1 3-2 5-4 0-2 1-3 1-5 1-2 1-5-1-6h0c-2-3-4-4-6-6v-1z"></path><path d="M739 350c-1 2-2 5-1 6v1c0 1-1 2 0 3v1c1 0 1 0 1 1v-1h1c2 0 3-2 4-3h2l-7 8v-1-1l-1-1c-1 0-3 0-4-1 0-1 0-3 1-4h1c-1-2-2-2-2-4 2-1 3-2 5-4z" class="X"></path><path d="M286 280v1h1c2 0 4 1 6 1 1 1 1 1 2 1 2-1 3-2 4-4v1c0 2 1 2 1 3v3l1 2 1 3-1 2c1 2 2 4 3 7v1l-1-1h-1l-2-2c0 2 0 2-1 3v1 1 2c-1 0-1-1-2-1v3 1c-2-1-2-1-3 0l-3-2 1-2-1-4h-1l-1 2h-1c-2-1-6 0-8-1-1 0-2 0-3-1-2 0-5 0-7-1l2-1h4s0 1 1 1h2l1-1-1-1c1-1 3-2 4-3v-1-1c3 0 4-1 5-3l1-1-3-3c-5 2-10 3-15 4l-3 1c-3 1-6 0-8 0-1 1-1 1-2 1l-6 1-4 1c-2 1-3 1-4 1-1-1-1-1-1-2 1-1 2-2 3-2h1l1-1h1c1-1 1-1 2-1h2c1-1 2-1 3-2 2 0 5 0 7-1l15-4 7-2 1 1z" class="C"></path><path d="M289 288v-2c1-1 2-2 3-2l1 1c1 0 2 1 2 2v1h0c0 1-1 1-1 2l-2 2c-1 0-2-1-2-1h-1v-1l-1-1 1-1z" class="B"></path><path d="M293 285c1 0 2 1 2 2v1c-1 0-2 0-3-1v-1l1-1z" class="N"></path><path d="M290 291s1 1 2 1l2-2c0-1 1-1 1-2l3 2-1 2h0 1 0c2 1 3 2 4 4h-1l-1-1h-1l1 2h-1v1h1c0 2 0 2-1 3v1 1 2c-1 0-1-1-2-1v3 1c-2-1-2-1-3 0l-3-2 1-2-1-4h-1l-1 2h-1c-2-1-6 0-8-1-1 0-2 0-3-1-2 0-5 0-7-1l2-1h4s0 1 1 1h2l1-1-1-1c1-1 3-2 4-3v-1-1c3 0 4-1 5-3l1 1v1h1z" class="T"></path><path d="M288 289l1 1v1h1c0 1-1 1-1 3l-1-1v-1c-1 0-2 1-3 1l-1 1h0 1c0 1 1 1 0 2l-1 2h-2c-1 1-1 1-1 2l-1 1c-1 0-2 0-3-1-2 0-5 0-7-1l2-1h4s0 1 1 1h2l1-1-1-1c1-1 3-2 4-3v-1-1c3 0 4-1 5-3z" class="Z"></path><path d="M292 304c1-2 2-3 2-5v-1h1c1 0 1-1 2-2l2 6v1 2c-1 0-1-1-2-1v3 1c-2-1-2-1-3 0l-3-2 1-2h0z" class="K"></path><path d="M292 304h2c1-1 2-2 3-2v2h0v3 1c-2-1-2-1-3 0l-3-2 1-2h0z" class="h"></path><defs><linearGradient id="i" x1="585.541" y1="681.271" x2="601.909" y2="699.006" xlink:href="#B"><stop offset="0" stop-color="#2b2a29"></stop><stop offset="1" stop-color="#474547"></stop></linearGradient></defs><path fill="url(#i)" d="M578 682h1c1 0 1 0 2 1s3 2 5 3c7 2 14 2 20 4 2 1 2 2 4 2h0c2 1 6 1 8 2v1s-1 0-1 1c-2 5-6 10-6 16l3 2h-2l-2-2-5-3c-1-1-2-1-3-2-1 0-2 0-2-1-2-1-3 0-5-1 0-1 0-1-1-2-1 0-1 0-2 1h-2c-1-1-3-1-4-2-2 0-2-1-2-2v-1c-1-4-3-7-6-9-2-1-2-1-2-2-2-1-3-2-5-3l7-3z"></path><path d="M578 690c1-1 2-2 3-2l1 1-1 1c3 3 3 4 4 8 0 1 0 1 1 1h1 0l-3 1v-1c-1-4-3-7-6-9z" class="C"></path><path d="M571 685l7-3c0 1 0 3 1 4s2 0 2 2c-1 0-2 1-3 2-2-1-2-1-2-2-2-1-3-2-5-3z" class="E"></path><path d="M598 695c1 0 2 0 3-1l-1-1c5-1 12 1 17 1h1v1s-1 0-1 1h-1l-1 1-1-1c-2 0-2 0-3 1l-1-1-1-1h-11z" class="B"></path><path d="M598 695h11l1 1 1 1c1-1 1-1 3-1l1 1 1-1h1c-2 5-6 10-6 16l3 2h-2l-2-2-5-3c-1-1-2-1-3-2-1 0-2 0-2-1-2-1-3 0-5-1 0-1 0-1-1-2-1 0-1 0-2 1h-2c-1-1-3-1-4-2-2 0-2-1-2-2l3-1h2c1 1 2 1 2 3l1-1h1v-2l3-1h-1c-1 0-1-1-2-2 2-1 3-1 5-1z" class="e"></path><path d="M598 695h11l1 1c-5 1-9 1-14 2h-1c-1 0-1-1-2-2 2-1 3-1 5-1z" class="O"></path><path d="M611 697c1-1 1-1 3-1l1 1-6 13-3-2c1-4 3-8 5-11zM357 507l3-3 2 2-1 2 1 1c2-1 3 0 5 2l2 2c2 2 4 5 6 7s5 5 8 6c1 2 5 4 6 6h-1v1c-4-1-7-2-11-2h-1c-1-1-2-2-2-4l-1-4c-1-1-1-1-2-1l-1-1v1l-4 3-3 3c0 1-2 2-2 3-3 3-7 5-12 7v-3l-1-2c0-1 0-2-1-3 0-3-1-9 0-11v-3-1c-1-2-1-3-2-4h1 0c2-3 4-2 7-2 2 0 3 0 4-2z" class="H"></path><path d="M354 519v-1c2 0 2-1 3-1 1-1 3 0 4 0v-1c2-1 3 0 4 1l1-1c1 0 1 0 2 1-1 1-1 1-3 1h0-1c-1 0-2 0-3 1h-3v1h-2c-1 0-1-1-2-1z" class="E"></path><path d="M345 511h1c2 1 3 2 5 4l5-1c-1 1-2 2-3 2h-3c1 1 1 1 1 2v1l2 1c0-1 1-1 1-1 1 0 1 1 2 1-2 0-3 0-4 1l-1-1c-1 1-1 1-2 3v3 1l-1 1h0l1 1v3 1 2l-1-2c0-1 0-2-1-3 0-3-1-9 0-11v-3-1c-1-2-1-3-2-4z" class="B"></path><path d="M357 527h3c2-1 4-2 6-2l-3 3c0 1-2 2-2 3-3 3-7 5-12 7v-3-2-1-3c2-2 1 0 3 0 1 0 2-1 3-1v1c1 0 1-1 2-2z" class="E"></path><path d="M364 518h1 0c0 1 0 2-1 3h-1l-6 6c-1 1-1 2-2 2v-1c-1 0-2 1-3 1-2 0-1-2-3 0l-1-1h0l1-1v-1-3c1-2 1-2 2-3l1 1c1-1 2-1 4-1h2v-1h3c1-1 2-1 3-1z" class="G"></path><path d="M364 518h1 0c0 1 0 2-1 3h-1l-6 6c-1 1-1 2-2 2v-1c-1 0-2 1-3 1-2 0-1-2-3 0l-1-1h0l1-1v-1l2 2h1v-3h3c2-1 4-2 6-4v-1l2-1 1-1z" class="O"></path><defs><linearGradient id="j" x1="698.294" y1="370.85" x2="727.461" y2="368.578" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#j)" d="M728 356c2 0 3-1 6-2 0 2 1 2 2 4h-1c-1 1-1 3-1 4 1 1 3 1 4 1l1 1v1 1l-2 2-1 2-2 2-2 2-2 2h-1s-1 1-2 1v1h0-1c-3 2-7 3-10 3s-5-1-7 0h0c-2 0-4-1-5-1l-13-4v-1l-6-3v-2h-1l1-1 3 1c3 2 7 4 10 5v-3h0c3-1 6-1 8-2 2-2 5-3 7-5h0c3-2 6-3 10-6l2-1-1-1c1-1 2 0 3-1h1z"></path><path d="M727 359v2c-1 4 0 9 1 12l1 1h3l-2 2c-1-1-2-2-4-1h0l-1-1 1-3c-1-1-2-5-1-6l1-2v-2l-1-1 2-1z" class="F"></path><path d="M727 356h1l1 1c0 2 0 8 1 10 0 1 2 1 2 3h4l-2 2-2 2h-3l-1-1c-1-3-2-8-1-12v-2l-2 1c-3 1-9 5-12 5h0c3-2 6-3 10-6l2-1-1-1c1-1 2 0 3-1z" class="K"></path><path d="M727 359v-1c1 0 1 0 2 2h-1c0 3 0 9 2 11h1 1c1 0 1 0 2 1l-2 2h-3l-1-1c-1-3-2-8-1-12v-2z" class="J"></path><path d="M728 356c2 0 3-1 6-2 0 2 1 2 2 4h-1c-1 1-1 3-1 4 1 1 3 1 4 1l1 1v1 1l-2 2-1 2h-4c0-2-2-2-2-3-1-2-1-8-1-10l-1-1z" class="e"></path><path d="M729 357c2 0 2 0 3 2 0 1-1 4-1 6l2 1v3h0 2 0c1-1 2-1 2-1l-1 2h-4c0-2-2-2-2-3-1-2-1-8-1-10z" class="W"></path><path d="M685 369l3 1c3 2 7 4 10 5 4 1 8 2 13 2 5 1 10 0 15 1-3 2-7 3-10 3s-5-1-7 0h0c-2 0-4-1-5-1l-13-4v-1l-6-3v-2h-1l1-1z" class="U"></path><defs><linearGradient id="k" x1="301.306" y1="386.166" x2="287.799" y2="394.027" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#k)" d="M301 383l8-1c0 1 0 2 1 3h1c-1 3-4 3-4 6v2 3l-2-1-1 2v2c1 1 1 0 0 2 0 3 4 6 6 9 0 2 1 4 1 5v6 1l3 6c0 1-1 0-2 1l-5-1c0-1-1-2-2-3l-5-6c1-3 1-5 3-8-2-3-6-6-9-9-1 1-1 2-3 2-1 0-4-3-5-4l1-1h0 1 0v-1h-1l-2-3h0l-6-3-6-4h3c3 0 6 1 9 0h-1l1-2c1-1 2-1 4-1h0l1-1h5l6-1z"></path><path d="M301 383l8-1c0 1 0 2 1 3h-1c-4 0-5 0-8-2z" class="W"></path><defs><linearGradient id="l" x1="309.222" y1="387.246" x2="302.145" y2="388.144" xlink:href="#B"><stop offset="0" stop-color="#737172"></stop><stop offset="1" stop-color="#878685"></stop></linearGradient></defs><path fill="url(#l)" d="M310 385h1c-1 3-4 3-4 6v2c-2-1-2-1-3-2h0l-1 1s0-1-1-1v-3-1c2 0 4-1 6-2h1 1z"></path><path d="M303 392l1-1h0c1 1 1 1 3 2v3l-2-1-1 2v2c1 1 1 0 0 2 0 3 4 6 6 9 0 2 1 4 1 5v6 1l-4-5c4-6-7-13-5-20 0 0 1 0 1-1v-2c1-1 1-1 0-2z" class="X"></path><defs><linearGradient id="m" x1="314.048" y1="423.829" x2="303.409" y2="420.313" xlink:href="#B"><stop offset="0" stop-color="#2f2d2e"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#m)" d="M303 411v1l2 2 2 3 4 5 3 6c0 1-1 0-2 1l-5-1c0-1-1-2-2-3l-5-6c1-3 1-5 3-8z"></path><path d="M303 411v1l2 2c-1 4-1 7 1 10l-1 1-5-6c1-3 1-5 3-8z" class="K"></path><path d="M273 388h3 0c3 2 5 2 8 3 9 4 15 12 20 20l-1 1v-1c-2-3-6-6-9-9-1 1-1 2-3 2-1 0-4-3-5-4l1-1h0 1 0v-1h-1l-2-3h0l-6-3-6-4z" class="b"></path><path d="M285 395c3 3 6 5 9 7-1 1-1 2-3 2-1 0-4-3-5-4l1-1h0 1 0v-1h-1l-2-3z" class="Z"></path><defs><linearGradient id="n" x1="696.479" y1="366.67" x2="715.779" y2="354.96" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#666565"></stop></linearGradient></defs><path fill="url(#n)" d="M698 337l1 2c1 1 3 1 4 0h2c1 1 3 2 4 3 2 1 2 2 4 3v2c1 0 2 0 3 2v1h2l1 1h-1c2 1 3 1 4 1 1 1 3 3 5 4-1 1-2 0-3 1l1 1-2 1c-4 3-7 4-10 6h0c-2 2-5 3-7 5-2 1-5 1-8 2h0v3c-3-1-7-3-10-5l2-1v-1c1-3 5-5 8-6v-1c-1-1-1-2-2-2-1-2-3-5-3-7 0-1 1-3 2-4l1-5 2-6z"></path><path d="M709 342c2 1 2 2 4 3v2s0 1 1 1c0 1 1 1 1 2v2 2h0-1c0-1 0-1-1-1v-1-1l-1-1v-1c0-3-2-4-3-7z" class="W"></path><path d="M706 365s-1-1-1-2v-1h-1c1-2 1 0 2 0 2-1 3-3 6-4l-1 2c1 1 2 0 3 1l-8 4z" class="h"></path><path d="M718 351c2 1 3 1 4 1 1 1 3 3 5 4-1 1-2 0-3 1l-10 4c-1-1-2 0-3-1l1-2h1l2-1c2-1 3-1 5-2-1-1-1-2-2-4z" class="S"></path><path d="M724 357l1 1-2 1c-4 3-7 4-10 6h0c-2 2-5 3-7 5-2 1-5 1-8 2 3-3 5-5 8-7l8-4 10-4z" class="L"></path><path d="M688 370l2-1v-1c1-3 5-5 8-6h0c-1 1-1 2-2 3 0 0 1 0 1 1l2 1-1 2v2l-2 1v1l2-1v3c-3-1-7-3-10-5z" class="B"></path><path d="M698 337l1 2c1 1 3 1 4 0h2c1 1 3 2 4 3 1 3 3 4 3 7v1l1 1v1l-3 1c-2 2-8 7-12 8-1-1-1-2-2-2-1-2-3-5-3-7 0-1 1-3 2-4l1-5 2-6z" class="J"></path><path d="M708 347l2 2v1c-1 0-2 1-2 1-3 1-6 5-9 7v-2c0-1-1-1-2-2h2l1 1c2-2 4-4 6-5 1-1 1-2 2-3z" class="F"></path><path d="M710 350l1 1h0c-1 1-1 1-1 2-2 2-8 7-12 8-1-1-1-2-2-2l3-1c3-2 6-6 9-7 0 0 1-1 2-1z" class="O"></path><path d="M698 337l1 2c1 1 3 1 4 0h2c1 1 3 2 4 3 1 3 3 4 3 7v1l1 1v1l-3 1c0-1 0-1 1-2h0l-1-1v-1l-2-2c-2 0-3-1-4-2s-3-1-3-2h2v-1l-1-1v1h-2l-3 2c1 2 0 1 2 2l-1 1-1 1c1 2 2 3 4 4-1 1-1 1-2 1-2 0-3-3-4-5l1-5 2-6z" class="F"></path><path d="M704 345c-1-2-1-3 0-5h1c1 1 2 2 2 4h1c1 1 2 2 3 4 0 0 0 1 1 1v1l1 1v1l-3 1c0-1 0-1 1-2h0l-1-1v-1l-2-2c-2 0-3-1-4-2z" class="B"></path><path d="M450 752v-2h1c1 4 2 7 4 11l5 11c2 3 3 6 5 9l5 11c2 4 4 8 5 13l34 83c-1 0-2 0-2 1s1 2 1 3l-1 1-13-31-31-73-10-22c-1-4-3-7-4-10h0c1-1 0-2 1-3l-1-1 1-1z" class="H"></path><path d="M450 752v-2h1c1 4 2 7 4 11l5 11c2 3 3 6 5 9l5 11c2 4 4 8 5 13-1-2-3-3-4-5l-1-4v3 3l-20-50z" class="J"></path><path d="M460 772c2 3 3 6 5 9l5 11c2 4 4 8 5 13-1-2-3-3-4-5l-1-4-1-2c-2-2-2-3-2-5l-3-6c-1-1-1-3-2-4s-1-2-2-4v-3z" class="F"></path><path d="M470 796l1 4c1 2 3 3 4 5l34 83c-1 0-2 0-2 1-1-1-2-3-2-4l-7-16-28-67v-3-3z" class="D"></path><path d="M468 370v3c1 1 5 4 6 5v-3c2 1 2 1 4 0v-1h2l1 2c2-1 4-1 7-1h0l1 2h0l1 1c0 2 0 2 1 3v2c1 2 2 3 3 6h1v2c1 1 1 2 1 4l-1 1v2l-1 3c-1 1-1 2-1 3v2c0 3 0 6 1 9v2h0c0 1 0 3-1 4 2 9 4 20 4 29-1 2-1 4-2 6 0-4-1-9-2-13v-1c-1-4-2-7-3-11 0-3 0-7-2-11-2-3-3-8-4-12-1-1-1-3-2-4-2 2-3 2-5 2l3-2h1 0c0-2-1-6-2-8-2-5 0-10-4-14-2-2-4-4-6-5-2-3-2-5-1-7z" class="J"></path><path d="M478 374h2l1 2c-1 0-2 1-3 1v2c0 1 0 0-1 1-1-1-2-1-3-2v-3c2 1 2 1 4 0v-1z" class="R"></path><path d="M488 407c-2-7-5-15-8-22v-1h2v2h2c2 2 6 18 6 21h-2z" class="e"></path><path d="M488 407h2l1 4 1 1c1 2 0 4 1 6v3c2 9 4 20 4 29-1 2-1 4-2 6 0-4-1-9-2-13v-1c0-5-1-10-2-15v-7c-1-4-2-8-3-13z" class="S"></path><defs><linearGradient id="o" x1="492.317" y1="379.833" x2="482.113" y2="397.186" xlink:href="#B"><stop offset="0" stop-color="#0a0909"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#o)" d="M481 376c2-1 4-1 7-1h0l1 2h0l1 1c0 2 0 2 1 3v2c1 2 2 3 3 6h1v2c1 1 1 2 1 4l-1 1v2l-1 3c-1 1-1 2-1 3v2c0 3 0 6 1 9v2h0c0 1 0 3-1 4v-3c-1-2 0-4-1-6l-1-1-1-4c0-3-4-19-6-21h-2v-2c-1-1-1-2-2-3h0c-2-1-1-2-2-4 1 0 2-1 3-1z"></path><path d="M495 391c1 1 1 2 1 4l-1 1v2l-1 3-1-8h1c0 1 0 1 1 2v-4z" class="E"></path><path d="M480 381c1 1 3 1 4 0s2-1 3-1h1v1c0 3 1 7 0 11v-4c-1-2-2-3-3-4s0-1-1-1l-2 1c-1-1-1-2-2-3z" class="O"></path><path d="M482 384l2-1c1 0 0 0 1 1s2 2 3 4v4 1h2c1 2 1 5 1 7v2l2 4h0c-1 1-1 2-1 4l-1 1-1-4c0-3-4-19-6-21h-2v-2z" class="G"></path><defs><linearGradient id="p" x1="383.652" y1="193.372" x2="348.834" y2="223.045" xlink:href="#B"><stop offset="0" stop-color="#858485"></stop><stop offset="1" stop-color="#a2a0a0"></stop></linearGradient></defs><path fill="url(#p)" d="M373 185c2 0 2 0 3 1s2 1 4 2h1c0 1 1 1 1 2l1 2c-9 10-17 22-18 37-1 4-1 9 0 14l-1-1c-1-2-1-9 0-10v-5c1-1 0-2 1-3v-2c-1 1-1 2-1 4-1 3 0 6-2 9 0-1 0-1-1-1h0v-1h-1v-2c0-1-1-1-2-2h-1c-2-1-2-1-3-2v-1h-1l-2 3c0-2 0-2-2-3 0 2 0 3-1 5 0-7 2-15 5-21 0-2 1-4 2-5 4-9 10-15 18-20z"></path><path d="M357 207c1 1 2 1 2 3l-2 1v-4z" class="g"></path><path d="M358 202c2 0 4-1 6 0 0 1 1 1 1 2v2h-1c-1-1-3-2-4-3l-2-1z" class="b"></path><path d="M358 207c0-2 0-3 2-4 1 1 3 2 4 3-2 2-2 2-4 2l-2-1z" class="G"></path><path d="M355 205l1-1 2-2 2 1c-2 1-2 2-2 4h-1v4c-2-1-3-1-4-1 0-2 1-4 2-5z" class="a"></path><path d="M382 190l1 2c-9 10-17 22-18 37-1 4-1 9 0 14l-1-1c-1-2-1-9 0-10v-5c1-1 0-2 1-3v-2c-1 1-1 2-1 4-1 3 0 6-2 9 0-1 0-1-1-1h0v-1h-1v-2c0-1-1-1-2-2h-1c-2-1-2-1-3-2v-1l1-1c1 0 2 0 2 1l3 1c0 1 1 2 1 3l1 3c1-2 1-7 1-10 4-13 11-23 19-33z" class="d"></path><path d="M349 469c2 0 2 0 4 1h1 0c2 1 3 2 4 3s1 1 2 1l2 2 1 1c0 2 0 3 1 5l1 1s0 1 1 1v2h-2l-1 2h-5c-3 0-6-1-8-2l-2-1-2 1-2-1v1c-2-1-4-3-7-3 0-1-1-1-2-2h0-3l1 1v1h-2c-3-1-5 0-8-1h-6-5-9l12-4 1-1h-1l-13 4-3 2-10 2c2-1 4-2 5-3l-15-3h-1c2-1 4-1 6 0 4 1 9 2 13 2 3 0 8-3 11-4 7-3 14-6 22-8 1 1 2 0 4 1h0c1-1 3 0 5 0 1 0 2 0 4-1h5 1z" class="a"></path><path d="M343 481h1v1c0 1 2 2 2 2h1v-1h-2v-4c1 0 2 0 3-1s2-1 3-1c0 2 1 4 0 5v1l-1 1v2l-2-1c-2 0-4-1-5-3v-1z" class="Y"></path><path d="M356 474h-1 0c-2 0-4-1-5-1-2 0-1 0-2-1-1 1-1 1-2 0l-1 1v1h-2v-1c-1-1 0-1-1-2h1c1 0 2 0 3 1h1c0-1 1-1 2-2l1 1h1c0-1 1-1 2-1h1 0c2 1 3 2 4 3s1 1 2 1l2 2-1 1-1-1-2 1v-1c-1 0-1-1-1-2h-1z" class="G"></path><path d="M315 478c2 1 9 2 11 1 1-1 2-3 2-4h1v1 1h0c1-1 3-2 4-2s3 0 4 1h0c0 2 0 3-1 5h1c1-1 2-2 3-2h0c1-1 2-1 3-1 0 1-1 2 0 3v1c1 2 3 3 5 3l-2 1-2-1v1c-2-1-4-3-7-3 0-1-1-1-2-2h0-3l1 1v1h-2c-3-1-5 0-8-1h-6-5-9l12-4z" class="U"></path><path d="M351 477v-1h-2l-1-1 1-1 1 1 1-1v1c0 1 0 2 1 3 1 0 1-1 2-2-1 0-1 0-2-1l4-1h1c0 1 0 2 1 2v1l2-1 1 1 1-1 1 1c0 2 0 3 1 5l1 1s0 1 1 1v2h-2l-1 2h-5c-3 0-6-1-8-2v-2l1-1v-1c1-1 0-3 0-5z" class="C"></path><path d="M362 476l1 1c0 2 0 3 1 5l1 1s0 1 1 1v2h-2l-1 2h-5l1-1v-1h-1c-1-1-1 0-1-1l-1-1h1c1-1 1-1 2-1l1-1v1 1c0 1 1 2 2 2h0v-1c-1-2-1-6-1-8l1-1z" class="Y"></path><path d="M326 424c0 1 0 3 1 4 1 0 1-1 1-1 4-1 7-1 10-2v2l-1 2-1-1c-1 1-1 2-1 3h-1 0l-1-1-1-1h-1-2l-1 1 1 1v2c-1 2 1 6 1 8h1v-1h1v1c-1 1-2 3-1 5l2 2c-1 1-1 2-1 3 1 1 1 2 2 3 1 0 1 1 1 2l1 1 1 2h-1v3 1l-4-4-3-2h-3c-4 0-7-4-10-6h-2l-11-9-12-6c2-1 4-1 5-1s1 0 1 1h2 0c1 1 2 1 3 0l-9-3c3-1 7-1 10-1h0l1-1-1-1c-1 0-2 0-3-1h-1l8-1 5 1c3 0 7 0 9-1l4 1 1-1-1-3 1-1z" class="S"></path><path d="M303 432h10-1l-9 1h0s1 1 2 0v1c1 2 1 2 3 3l2 2c-1 0-2-1-3-1h-1l-4-2-9-3c3-1 7-1 10-1z" class="i"></path><path d="M321 445l9 9-1 2h1c1 1 1 2 2 3h0l-3-2-6-6c-1-1-2-2-2-3v-3z" class="Q"></path><path d="M315 432c4 0 7-1 11-1 0 2 1 5 0 7h-3c-1-1-1-1-2-3h-3c-1 0-2-1-3-1l-1-1 1-1h0z" class="W"></path><path d="M305 433l6 3c3 2 7 6 10 9v3c0 1 1 2 2 3-3-1-6-5-9-8-3-2-5-3-8-5h1c1 0 2 1 3 1l-2-2c-2-1-2-1-3-3v-1z" class="d"></path><defs><linearGradient id="q" x1="316.881" y1="424.682" x2="308.513" y2="434.441" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#f1eff0"></stop></linearGradient></defs><path fill="url(#q)" d="M326 424c0 1 0 3 1 4 1 0 1-1 1-1 4-1 7-1 10-2v2l-1 2-1-1c-1 1-1 2-1 3h-1 0l-1-1-1-1h-1-2l-1 1s-1 1-2 1c-4 0-7 1-11 1h-2-10 0l1-1-1-1c-1 0-2 0-3-1h-1l8-1 5 1c3 0 7 0 9-1l4 1 1-1-1-3 1-1z"></path><path d="M291 436c2-1 4-1 5-1s1 0 1 1h2 0c1 1 2 1 3 0l4 2 8 5c3 3 6 7 9 8l6 6h-3c-4 0-7-4-10-6h-2l-11-9-12-6z" class="J"></path><path d="M303 442c5 1 10 5 13 9h-2l-11-9z" class="F"></path><path d="M477 346c3-2 5-3 9-2s9 4 13 7h0c1 1 1 3 2 4l1-1 1-1c2 1 4 1 5 2 2 2 2 4 2 6h0l-2 7c-1 1-1 1-1 2l-1 1v1c0 1 0 2-1 3l-2 2c0-2 0-3 1-4-2-2-2-1-4-1-1 1-3 0-3 0-1 0-2 1-3 1v-1h-1l-1 1c-1 0-2-1-3-1l-2-1c-3-2-6-1-8-2s-4-3-5-4c0-2 1-3 2-5h0l-2-1 1-1c1-2 1-3 0-5-2-2-2-4-2-6 0-1 0-2 1-2v2h1l2-1z"></path><path d="M475 353c-2-2-2-4-2-6 0-1 0-2 1-2v2h1l2-1c1 0 1 1 2 2v5c1 4 1 8 5 10v1l3 1 2 1h1v1h-2-3v-1h-1c-2-1-3-3-4-4v-1c-1-1-1-2-1-4h-2l-1 3-2-1 1-1c1-2 1-3 0-5z" class="O"></path><path d="M508 355c2 2 2 4 2 6h0l-2 7c-1 1-1 1-1 2l-1 1v1c0 1 0 2-1 3l-2 2c0-2 0-3 1-4-2-2-2-1-4-1-1 1-3 0-3 0 0-1 1-1 2-1 0 0 1 0 2-1 2-1 3-7 4-9 0-1 0-2 1-2 1-1 2-2 2-4z" class="G"></path><path d="M508 355c2 2 2 4 2 6h0-1c-1 2-2 5-3 6 0-2 1-4-1-6 0-1 0-2 1-2 1-1 2-2 2-4z" class="M"></path><path d="M430 706h2 5v1c1-1 1-1 2-1h0c2 0 2 0 4-1v3h-1v1s0 1-1 2c1 0 1 1 2 1 0-2 0-2 1-3l1-1c1-1 1-2 1-2h3c1 0 2 0 3 1h1c1 0 1 0 2 1h0c-1 1-1 1-1 2h-1c-1 1-1 2-1 3 2 3 3 6 7 7 2-2 5-1 8-2h1 0l-2 2h-3l-2 2c-1 0-3 2-3 2-2 0-2 0-3 1 0 0 1-1 1-2h-1c-2 1-3 1-4 2v3h0-1v-1c-1 1-2 2-3 2v2l-1 1h1l1 1h-1l1 2h0v1c-1 0-1 0-2 1s-2 0-3 0h-3c-1-1-2-1-3-1-2-1-3 0-4 0-2-1-3-1-4-1h-3c-1-1-1-1-1-2h2v-1-1c1-1 2-1 3-1 0-4-3-7-4-10l-1-1v-3-1c-2-1-2-1-3-3h0c1-2 3-2 5-3l3-3z" class="L"></path><path d="M434 722v-2c2 1 1 1 3 1v5l1-1v-1h1l1 1v-5h1 1l1 1c2 0 4 1 6 3h-1l-1 2c1 0 2 1 3 1-1 1-2 2-3 2v2l-1 1h1l1 1h-1l1 2h0v1c-1 0-1 0-2 1s-2 0-3 0h-3c-1-1-2-1-3-1-2-1-3 0-4 0-2-1-3-1-4-1h-3c-1-1-1-1-1-2h2v-1-1c1-1 2-1 3-1l2 2 2-1 1 1 2 2c1-1 2-1 2-1l1-1c-2 0-3-2-3-3l-1-1c0-1 0-1-1-2 0-2 0-3-1-4h0z" class="F"></path><path d="M427 731c1-1 2-1 3-1l2 2 2-1 1 1 2 2c1-1 2-1 2-1l1-1c1 0 2 2 3 3h0c-3 0-6 0-9-1-2 0-3 1-5 0-1 0-2 0-3 1-1-1-1-1-1-2h2v-1-1z" class="O"></path><path d="M427 731c1-1 2-1 3-1l2 2 2-1 1 1c-1 1-2 1-2 1-2-1-1 0-2 0s-3-1-4-1v-1z" class="K"></path><path d="M430 706h2 5v1c1-1 1-1 2-1h0c2 0 2 0 4-1v3h-1v1s0 1-1 2c1 0 1 1 2 1 1 1 0 1 1 1h1c1 1 4 2 5 3l-1 1 1 1c-1 1-5 0-7-1-1 0-2 0-3-1l-1-2c-1 1-1 3-2 4h-2c-1 0-1 0-3-1 0 2 1 3 1 4l1 1h0c1 1 1 2 1 4 1 1 1 1 1 2l1 1c0 1 1 3 3 3l-1 1s-1 0-2 1l-2-2-1-1-2 1-2-2c0-4-3-7-4-10l-1-1v-3-1c-2-1-2-1-3-3h0c1-2 3-2 5-3l3-3z" class="P"></path><path d="M422 712h3c0 2 0 3 1 4 3 4 7 10 8 15h0l-2 1-2-2c0-4-3-7-4-10l-1-1v-3-1c-2-1-2-1-3-3z" class="O"></path><path d="M430 706h2 5v1c1-1 1-1 2-1h0c2 0 2 0 4-1v3h-1v1s0 1-1 2c1 0 1 1 2 1 1 1 0 1 1 1h1c1 1 4 2 5 3l-1 1 1 1c-1 1-5 0-7-1-1 0-2 0-3-1l-1-2v-3h0l2-3h-1-1c-1 1-1 2-1 3v2l-1-1-1 1h1l-1 1-3-1h-1c-2-1-4-1-6-1h-1-3 0c1-2 3-2 5-3l3-3z" class="D"></path><path d="M430 709c2-1 1-1 3-1 1 1 2 1 2 2l1 1c-2 0-4-1-6-2z" class="V"></path><path d="M430 706h2 5v1c-1 1-2 1-2 3 0-1-1-1-2-2-2 0-1 0-3 1l-1 1h-1l-2 2h-1-3 0c1-2 3-2 5-3l3-3z" class="g"></path><defs><linearGradient id="r" x1="388.992" y1="575.041" x2="350.318" y2="570.956" xlink:href="#B"><stop offset="0" stop-color="#201f1f"></stop><stop offset="1" stop-color="#545253"></stop></linearGradient></defs><path fill="url(#r)" d="M362 564c11 1 19-2 28-7h0c-2 3-4 4-6 6h5c1 0 3 3 4 3 1 1 2 1 2 2v2c3 2 7 2 10 2 1 0 0 0 1-1h3v1l-2 2c-2 0-5 0-7-1l-1 2h-2-1v3c0 2 1 1-1 3l-1 1 1 1-7 6c-3 0-6 0-8-2-1 0-1-1-2-2l-1-1v1c0 2 1 3 3 5-2 1-2 1-4 1l-1-1-2-2c-3-4-7-7-10-11h1c-4-3-7-7-10-10-2-1-3-2-5-3l1-1v1c2 0 3 0 5 1 3 1 5 1 7-1z"></path><path d="M364 577c4 3 11 8 14 12-1 1-2 1-3 1l-2-2c-3-4-7-7-10-11h1z" class="I"></path><path d="M377 577c0-2 0-4 1-6l1-1c0-2 1-2 3-3h0l1-1h1c2 0 3-1 4 0 1 0 1 0 2 1h0c2 0 4 2 5 3 3 2 7 2 10 2 1 0 0 0 1-1h3v1l-2 2c-2 0-5 0-7-1l-1 2h-2-1v3c0 2 1 1-1 3l-1 1 1 1-7 6c-3 0-6 0-8-2-1 0-1-1-2-2l-1-1v1h-1v-3c0-1 0-2-1-2 1-1 1-2 2-2v1h1l-1-2z" class="L"></path><path d="M377 577c0-2 0-4 1-6l1-1c0-2 1-2 3-3h0l1-1h1l1 1h1c0 1 0 2-1 2h-4c-1 2-1 2-1 4h1v1l-4 3z" class="C"></path><path d="M396 575v3c0 2 1 1-1 3l-1 1 1 1-7 6c-3 0-6 0-8-2-1 0-1-1-2-2v-2h1c1 1 2 2 4 2h1l1 1c1-1 1-1 1-2l-1-1c-1-1 0-1 0-2 0 0-1-1-2-1l-1-1c0-2 0-2 1-2l3 1h2c1 1 5 2 7 1l-1-1h0-1l1-2 2-1z" class="H"></path><path d="M384 566c2 0 3-1 4 0 1 0 1 0 2 1h0c2 0 4 2 5 3 3 2 7 2 10 2 1 0 0 0 1-1h3v1l-2 2c-2 0-5 0-7-1l-1 2h-2-1l-2 1-2 1c0-1-1-1-1-1l-1-1h-3c0-1 0-1-1-2h-2l-1-1c1-1 2-2 3-2v-1h-1c1 0 1-1 1-2h-1l-1-1z" class="B"></path><path d="M334 489c0-1 1-1 2-1s1 1 2 1h2l4 1 2 2h0c3 2 5 4 8 5l2 3c1 1 1 1 2 1l-1 6h0c-1 2-2 2-4 2-3 0-5-1-7 2h0-1c1 1 1 2 2 4v1 3c-1 2 0 8 0 11 1 1 1 2 1 3l-4-6c-2 0-3 1-5 1s-6 1-8-1c1-1 2-1 4-2h1c1 0 3 0 4-1h1c-6-4-11-9-18-11-4-1-8-1-12-2 1-2 3-3 5-4 2-2 4-4 7-6s8-5 9-9c1 0 2 1 3 1 0 1 1 1 2 2v1c-3 2-8 4-11 7 1 0 2 0 2-1h1l8-4c1-1 1-2 2-3v-3c-2-1-3-1-5-3h0z" class="K"></path><path d="M323 513c2-2 2-2 5-2 2 0 8 2 10 4s2 5 3 8v1c-6-4-11-9-18-11z" class="U"></path><path d="M332 492c1 0 2 1 3 1 0 1 1 1 2 2v1c-3 2-8 4-11 7l-3 1c-2 1-3 2-4 3v1h2c4-1 10 0 15-2h2 8c2 1 3 0 4 1h1 2 0v2c-3 0-5-1-7 2h0-1c1 1 1 2 2 4v1 3c-1 2 0 8 0 11-1-1-2-3-2-4v-13l-3-1c-4-3-9-3-14-3h-11l-1-2c2-2 4-4 7-6s8-5 9-9z" class="G"></path><path d="M351 507h2 0v2c-3 0-5-1-7 2h0-1c-1-1-1-1-1-2 2-2 4 0 6-1 0 0 1 0 1-1z" class="O"></path><path d="M334 489c0-1 1-1 2-1s1 1 2 1h2l4 1 2 2h0c3 2 5 4 8 5l2 3c1 1 1 1 2 1l-1 6h0c-1 2-2 2-4 2v-2h0-2-1c-1-1-2 0-4-1h-8-2c-5 2-11 1-15 2h-2v-1c1-1 2-2 4-3l3-1c1 0 2 0 2-1h1l8-4c1-1 1-2 2-3v-3c-2-1-3-1-5-3h0z" class="P"></path><path d="M338 506c3-1 6-1 9-2 2 0 4 1 5 2 0 1 0 1 1 1h-2-1c-1-1-2 0-4-1h-8z" class="H"></path><path d="M356 500c1 1 1 1 2 1l-1 6h0c-1 2-2 2-4 2v-2h2v-1c0-1 1-1 0-2l-2-1v-2c1 1 1 1 2 1l1-2z" class="N"></path><path d="M334 489c0-1 1-1 2-1s1 1 2 1h2l4 1 2 2h0c3 2 5 4 8 5l2 3-1 2c-1 0-1 0-2-1v2l-8-4c-3-1-4-2-6-4v-3c-2-1-3-1-5-3h0z" class="G"></path><path d="M346 492h0c3 2 5 4 8 5l2 3-1 2c-1 0-1 0-2-1l-3-3-3-3-1 1c-1-1-2-1-3-2v-1c1 0 2-1 3-1z" class="N"></path><path d="M354 497l2 3-1 2c-1 0-1 0-2-1l-3-3c1 0 1 0 2 1h1l1-2z" class="P"></path><path d="M242 191l2-1c4 1 8 3 12 5l6 3s1-1 2-1c14 9 23 22 29 37 3 6 5 12 8 18 4 7 9 14 12 22v1l-1 1c-1-1-2-3-3-4-2-2-3-5-5-8l-5-9c-1-2-2-3-2-4s0-2-1-2c-1 1-3 0-3 2v1 2h1v-2c1 2 1 4 1 5l-1 1 4 4c0 1 2 2 2 3v1c0 1 1 1 1 3l2 2v1l2 3v1h-1l-3-5-2-4v-1l-7-7c-3 0-3 0-5-2l-1-1h-2v-1l-1-1c-1-2-3-3-4-5-1-1-2-2-4-2l-1-1 1-1c0-1 0-2 1-3s1-1 1-2v-3c0-2 0-4 2-5h3c1-1 0-1-1-2v-2c-2-2-8-3-10-3-1-2-1-3 0-4l2-2v-1l-3-3-1-3c2 2 5 4 5 7l1 1c0 1 1 1 2 2h-1v1c1 2 2 3 4 4h1l1 1c0 1 1 2 1 3 1 1 1 1 1 2l1 1c1 2 3 5 2 7-1 3 0 7 1 10 1 1 0 1 0 2 1-1 0-2 0-3-1-2 0-5 0-7v-3l-1-1v-2-2l-1-1-1-3v-1c0-1-1-1-1-2v-1s-1-1-1-2h0v-1c-1 0-2-1-2-2 0-2 0-2-1-3l-2-2c-1-1-1-2-1-3-2-3-7-7-9-9s-4-3-6-4c-3-2-7-4-9-5s-5-2-7-3c-1-1-2-1-4-2z" class="Y"></path><path d="M275 247l-1-1 1-1c0-1 0-2 1-3s1-1 1-2v-3c0-2 0-4 2-5h3l1 1v1c2 2 3 6 2 8 0 4 0 7 1 10l1 3h-1-1v-1c-1-2-3-4-5-5h-1c-1-1-2-2-4-2z" class="P"></path><path d="M292 259c-1-1-2-1-2-3-2-4-1-7-1-11v-4c-1-1-1-1-1-2v-1-1l-1-5v-1c-1-2-3-5-4-7l1-1c1 1 1 2 2 3v1l1 3c1 0 1 1 1 2h0c1 1 1 1 1 2v3c1 1 1 1 1 3 1 1 0 3 1 5v-3h1l-1-2v-3h0l3 5 2 6v1c-1 1-3 0-3 2v1 2h1v-2c1 2 1 4 1 5l-1 1 4 4c0 1 2 2 2 3v1c0 1 1 1 1 3l2 2v1l2 3v1h-1l-3-5-2-4v-1l-7-7z" class="F"></path><path d="M294 242l2 6c-1 1-2 1-4 1l-1-1 3-6z" class="U"></path><path d="M480 795l1 1v1h1 0c0 2 1 4 2 5v1l1 2v1l1 2 1 1v2l1 1c0 1 1 2 1 4h1v1 1h1v1c0 1 1 2 1 3 0 0 1 1 1 2h0c1 1 0 1 1 2 0 1 1 2 1 3 1 1 1 1 1 2v1l1 1h0c1 0 2 1 3 1l3 1h2 1 3v1h2v-17c0-3 0-8 1-11v-5c1 1 1 2 1 3l2 2c1 2 1 5 2 7 1 8 2 18 2 26 0 3 0 8 1 10l-2 5c-2 3-3 7-4 11 0 2 0 6 1 8 0 2-2 7-3 9-1-5-3-9-5-14l-10-24c0-2 0-4-1-5 0-2 0-4-1-6h0l-3-6 1-1v1h1c-1-4-3-8-5-11l-6-15-2-5-1-2v-1z" class="c"></path><path d="M513 806l2 2c1 2 1 5 2 7 1 8 2 18 2 26 0 3 0 8 1 10l-2 5c-2 3-3 7-4 11v6c-2-2-1-57-1-64v-3z" class="D"></path><path d="M513 806l2 2c1 2 1 5 2 7 1 8 2 18 2 26 0 3 0 8 1 10l-2 5c-1-2-1-9-1-11l-4-36v-3z" class="T"></path><path d="M480 795l1 1v1h1 0c0 2 1 4 2 5v1l1 2v1l1 2 1 1v2l1 1c0 1 1 2 1 4h1v1 1h1v1c0 1 1 2 1 3 0 0 1 1 1 2h0c1 1 0 1 1 2 0 1 1 2 1 3 1 1 1 1 1 2v1l1 1h0c1 0 2 1 3 1l3 1h2 1 3v1h2v-17c0-3 0-8 1-11v57 9c0 3 1 5 1 8l-3-12-6-15c-3-9-6-18-10-26-1-4-3-8-5-11l-6-15-2-5-1-2v-1z" class="G"></path><path d="M686 497l1-2c2-1 3-2 5-3v-1c1 1 1 1 1 3l2 1 1 1c1 1 2 2 4 3l1 1h1l6 6 2 1h0l5 5c-5 0-9 0-14 1-6 1-11 5-16 8l-4 4v1l-1 1h-1c-2 2-5 7-6 10h-1v-1c-1-1-1-2-1-3s1-2 1-2v-2c1-2 2-4 1-6h0v-1l1 1v-2l-2-1v-1l-6-2h-4v-3c4-5 9-10 13-14 1 0 2-1 3-2l7-5c1 1 1 2 1 3v1z" class="F"></path><path d="M671 533c0-1 1-2 1-2v-2c1-2 2-4 1-6h0v-1l1 1c1 1 1 3 0 5 0 0-1 2-1 3l1 1c0 1 0 2-1 2l-1 2c-1-1-1-2-1-3z" class="C"></path><path d="M670 515s1 0 2 1c1-2 3-4 5-5-1 2-3 4-4 7 0 1 0 1-1 1l-6-2s1-1 2-1h1l1-1z" class="M"></path><path d="M674 508v1l1-1h2 1 3v1c-1 1-1 0-3 0 0 0-1 1-1 2-2 1-4 3-5 5-1-1-2-1-2-1v-1c2 0 2-1 4-2-2-1-3-1-4-2 1-1 2-2 4-2z" class="E"></path><path d="M681 525h-2c1-1 1-2 1-3s1-2 1-3c0-3 0-5 3-7 2-1 5-1 8-1 2 1 6 0 9 2-6 1-11 5-16 8l-4 4z" class="R"></path><path d="M685 493c1 1 1 2 1 3v1h1c4 3 9 4 13 6 2 1 4 3 5 5 1 1 1 1 0 2-1 0-2-1-3-1-4-1-9-2-13-1l-2-2h-8 0-1c-1 0-4 1-4 2-2 0-3 1-4 2 1 1 2 1 4 2-2 1-2 2-4 2v1l-1 1h-1c-1 0-2 1-2 1h-4v-3c4-5 9-10 13-14 1 0 2-1 3-2l7-5z" class="P"></path><path d="M246 313c-9-5-20-7-31-7-6-1-13 0-19-1h0l87-12v1c-1 1-3 2-4 3l1 1-1 1h-2c-1 0-1-1-1-1h-4l-2 1-2 2 2 2c0 1-1 2-2 4v1l2 3c-2 3 1 6-1 9l2 1c0 1 0 2-1 3 0 1-1 1-2 1v2l-13-6c-1 1-1 1-1 3l-5-3v-1-1l1-2c-1 0-3-3-4-3v-1z" class="i"></path><path d="M256 305c2-1 1 0 2 1h3c0 2 0 2-1 4l-2-2c-1 1-1 1-1 2v-1l-1-1v-3z" class="f"></path><path d="M254 316c2-1 2-1 4-1h0l-1-1 1-1c1 1 2 2 4 3h0v1 1c-1 0-1 0-2 1h-1l-5-3z" class="R"></path><path d="M263 316h2l1 1v-1c-1-1-1-2-1-2 1-1 1-2 1-3l1-1v-1l-3-4 1-1c-1-2-1-3-1-4v-1h-2c3-1 7-1 10-1l-2 1-2 2 2 2c0 1-1 2-2 4v1l2 3c-2 3 1 6-1 9l-6-4z" class="U"></path><path d="M246 313l8 3 5 3h1c1-1 1-1 2-1v-1-1h1l6 4 2 1c0 1 0 2-1 3 0 1-1 1-2 1v2l-13-6c-1 1-1 1-1 3l-5-3v-1-1l1-2c-1 0-3-3-4-3v-1z" class="C"></path><path d="M262 317l2 1-1 3-4-2h1c1-1 1-1 2-1v-1z" class="Z"></path><path d="M264 318c3 2 4 3 5 6l-6-3 1-3z" class="V"></path><path d="M249 321v-1-1l1-2 5 4c-1 1-1 1-1 3l-5-3z" class="f"></path><defs><linearGradient id="s" x1="584.714" y1="794.701" x2="542.581" y2="799.111" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#b2afaf"></stop></linearGradient></defs><path fill="url(#s)" d="M578 747v4l1 1c2 1 3 2 5 2h3c-3 6-5 14-8 20-1 3-3 7-5 11l-10 25-5 13c-1 1-1 4-2 5l-2 2v1h-3c-2 0-4-3-6-3-1 1-2 1-2 2l28-71 1-1 1 2h1c1-1 1-1 1-2l-2-2 2-1v-3c0-1 1-1 1-2s1-2 1-3z"></path><defs><linearGradient id="t" x1="577.662" y1="757.547" x2="577.796" y2="766.996" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#t)" d="M578 747v4l1 1c2 1 3 2 5 2h3c-3 6-5 14-8 20 0-2 0-3 1-4h0v-1c0-1 0-1 1-2 0-1 0-2 1-3-1 1-3 3-4 3-2 1-2-1-4-1l-1-1v-1c1-1 1-2 1-3v-1h1c1-1 1-1 1-2l-2-2 2-1v-3c0-1 1-1 1-2s1-2 1-3z"></path><path d="M579 752c2 1 3 2 5 2h-1c-3 0-3 3-5 4h-1c1-2 2-4 2-6z" class="O"></path><defs><linearGradient id="u" x1="590.05" y1="167.489" x2="659.095" y2="152.953" xlink:href="#B"><stop offset="0" stop-color="#c0bebe"></stop><stop offset="1" stop-color="#f6f5f4"></stop></linearGradient></defs><path fill="url(#u)" d="M627 170c-5-1-10 0-15-1-2 0-4 0-6-1-8-5-15-13-24-16h74l-3 1h2v2h0c0 1-1 1-1 2h1c2 1 3 3 5 4l1 1-1 3v2 1h-6v-1h-1c-1 0-1 0-2-1 0-1-2-1-3-1-1-1-2 0-3 0h0c-1 0-3 1-4 1h-1c-2 2-8 3-10 2h-2-1c-2 0-4 0-6 1h6v1z"></path><path d="M278 228l3 2c1 1 2 1 1 2h-3c-2 1-2 3-2 5v3c0 1 0 1-1 2s-1 2-1 3l-1 1 1 1c2 0 3 1 4 2 1 2 3 3 4 5l1 1v1h2l1 1v1c1 2 1 2 0 3h0c2 1 4 3 5 5h0l2 3c2 3 2 4 3 7l1 1c0-1 0-2 1-2 1 2 2 2 3 4-1 4 0 9 2 12h-2l-1-3-1-2v-3c0-1-1-1-1-3v-1c-1 2-2 3-4 4-1 0-1 0-2-1-2 0-4-1-6-1h-1v-1l-1-1-7 2-15 4c1-1 2-2 3-2 2-1 3-1 4-2v-2l1-1v-1-3h0l3-3c0-2 1-3 2-4-1-1-1-1-1-2s-1-2-1-4h-1l1-1-1-1-3-3h-2c-1-1-1-2-1-3h1l1-1h-3l-1-1c2 0 2 0 3-1v-2l-9 2h-2v-1h-4c1-1 1-1 3-2h0v-1c0-1 0-1 1-2v-2h0c1-1 0-2 0-3l-1-1v-2h4l5-1h1v-2-4h3l1 1c2-1 4 0 7 0h2 0c-1-1-1-1-1-2h0z" class="I"></path><path d="M271 240l2-2c0 2 0 3-1 4v3l-2-2c0-2 0-2 1-3z" class="B"></path><path d="M283 270c0-1 0 0-1-1s0-1-1-3l2-1c2 3 7 8 7 11l-1 1h0c0-2-1-3-2-4h0l-4-3z" class="c"></path><path d="M283 270l4 3h0c1 1 2 2 2 4h0l-4 2-7 2c2-2 4-2 6-3 1-1 2-1 2-2-1-1-1-2-2-3l-1-3z" class="S"></path><defs><linearGradient id="v" x1="277.676" y1="258.856" x2="279.843" y2="269.034" xlink:href="#B"><stop offset="0" stop-color="#605f5f"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#v)" d="M278 254c2 2 0 4 1 6 1 0 1 1 2 1v3c1 1 2 0 2 1l-2 1c1 2 0 2 1 3s1 0 1 1l1 3c-1-1-3-2-4-3-2 0-3 0-4 2l-2-1c0-2 1-3 2-4h0v-3l-1-1 1-1c0-1 0-2-1-3h0l1-1h0l1-1c1-1 1-2 1-3z"></path><path d="M278 228l3 2c1 1 2 1 1 2h-3c-2 1-2 3-2 5v3c0 1 0 1-1 2s-1 2-1 3l-1 1 1 1h0l-2 2-1-1 2-9 1-2 1-2v-3l-1 1-2 5-2 2v-4-1c0-2 0-4-1-5 2-1 4 0 7 0h2 0c-1-1-1-1-1-2h0z" class="O"></path><path d="M269 248h1c1 1 2 2 4 3l1 1 1-1c1 1 2 2 2 3s0 2-1 3l-1 1h0l-1 1h0c1 1 1 2 1 3l-1 1 1 1v3h0c-1-1-1-1-1-2s-1-2-1-4h-1l1-1-1-1-3-3h-2c-1-1-1-2-1-3h1l1-1h-3l-1-1c2 0 2 0 3-1v-2h1z" class="D"></path><path d="M275 247c2 0 3 1 4 2 1 2 3 3 4 5l1 1v1h2l1 1v1c-1 1-1 1-2 1v3h1c1 1 1 1 2 3s3 4 4 6c2 2 4 3 3 6 0 1-1 1-2 2h-2l1-2c0-4-5-10-8-13-2-3-1-5-3-8-1-2-2-2-3-4v-1l-1-2-2-2h0z" class="B"></path><path d="M287 258c1 2 1 2 0 3h0c2 1 4 3 5 5h0l2 3c2 3 2 4 3 7l1 1c0-1 0-2 1-2 1 2 2 2 3 4-1 4 0 9 2 12h-2l-1-3-1-2v-3c0-1-1-1-1-3v-1c-1 2-2 3-4 4-1 0-1 0-2-1-2 0-4-1-6-1h-1v-1c2-1 3-1 5-1h2c1-1 2-1 2-2 1-3-1-4-3-6-1-2-3-4-4-6s-1-2-2-3h-1v-3c1 0 1 0 2-1z" class="E"></path><path d="M276 272c1-2 2-2 4-2 1 1 3 2 4 3s1 2 2 3c0 1-1 1-2 2-2 1-4 1-6 3l-15 4c1-1 2-2 3-2 2-1 3-1 4-2v-2l1-1v-1-3h0l3-3 2 1z" class="X"></path><path d="M274 271l2 1c-1 1-1 2-3 3l-2-1 3-3z" class="S"></path><path d="M275 275c1-1 2-2 4-3h1c1 2 1 3 3 5v1h-1c-1-1-2-1-4-1v1h-2s-1-1-1-2v-1z" class="a"></path><path d="M275 275l3-1 1 1v1h-2l-1 2s-1-1-1-2v-1z" class="Z"></path><path d="M283 277h1v1c-2 1-4 1-6 3l-15 4c1-1 2-2 3-2 2-1 3-1 4-2v-2l1-1c1-1 2-1 4-2 0 1 1 2 1 2h2v-1c2 0 3 0 4 1h1v-1z" class="V"></path><path d="M271 278c1-1 2-1 4-2 0 1 1 2 1 2v2l-6 1v-2l1-1z" class="a"></path><defs><linearGradient id="w" x1="263.981" y1="236.314" x2="258.678" y2="244.442" xlink:href="#B"><stop offset="0" stop-color="#424041"></stop><stop offset="1" stop-color="#615f60"></stop></linearGradient></defs><path fill="url(#w)" d="M266 233v-4h3l1 1c1 1 1 3 1 5v1 4c-1 1-1 1-1 3l2 2-1 3h-1-1-1l-9 2h-2v-1h-4c1-1 1-1 3-2h0v-1c0-1 0-1 1-2v-2h0c1-1 0-2 0-3l-1-1v-2h4l5-1h1v-2z"></path><path d="M256 236h4v2 1l-2-1-1 1-1-1v-2z" class="W"></path><path d="M256 246c1 0 2-1 3-2 2 0 3 0 5-1 1 0 2 0 3-1l-1 3c2 2 2 0 3 3h-1l-9 2h-2v-1h-4c1-1 1-1 3-2h0v-1z" class="I"></path><path d="M266 233v-4h3l1 1c1 1 1 3 1 5v1 4c-1 1-1 1-1 3l2 2-1 3h-1-1c-1-3-1-1-3-3l1-3v-1l-1-1 2-3h-2-1v-2h1v-2z" class="P"></path><path d="M267 241c1 2 1 3 2 4h1v-2l2 2-1 3h-1-1c-1-3-1-1-3-3l1-3v-1z" class="N"></path><path d="M266 233v-4h3l-2 1s0 1 1 1c1 1 1 4 1 5l-1 1h-2-1v-2h1v-2z" class="G"></path><defs><linearGradient id="x" x1="610.053" y1="629.418" x2="635.769" y2="659.706" xlink:href="#B"><stop offset="0" stop-color="#131111"></stop><stop offset="1" stop-color="#727172"></stop></linearGradient></defs><path fill="url(#x)" d="M600 643h-1l-2-1v-1c3-2 8 0 11-1h3l7 1 32 4c5 0 9 0 13 1l-11 1c1 0 2 0 2 1h1c-1 1-3 1-4 1-5 1-10 1-15 4h0c-4 2-8 4-12 7-2 2-5 3-8 5-2 2-5 4-7 5l-1-1-1-1h0l6-3-1-1-3 1-5 1c-1-1-2-1-3-2h0v-1h0c-3 2-8 1-11 0h-3l1-2c-1-1-1-2-1-3l1-1 1 1c0-1 0-1 1-2h0v-3h1c0-2 0-3 2-4 0-1 0-1 1-2 0 0 0-1 1-1l3-2 2-1z"></path><path d="M606 645c1-1 1-1 2 0l1-1v1l1-1h1v2h-2c-1 1-1 2-1 4-1 0-1 0-2-1 0-2 1-2 0-4z" class="O"></path><path d="M607 660l2-3v3l-1 2h0c-1 2-2 2-3 3l-2-1v-1h1c0-1 0 0 1-1l2-2z" class="C"></path><path d="M600 643c1 1 3 2 4 2h2c1 2 0 2 0 4v1h0c-2-4-5-5-8-6l2-1z" class="B"></path><path d="M609 665l1-3h2c1-1 4-4 6-5h0c1 0 2-1 3-1-2 2-5 4-7 6 0 1-1 2-2 2l-3 1z" class="K"></path><path d="M621 656l3-3c3-3 6-4 10-4-2 1-5 3-7 3-1 0-2 1-2 2h0 2c1-1 2-2 3-2h1c1-1 1-1 2-1-4 3-8 6-13 9-2 2-5 4-7 5l-1-1c1 0 2-1 2-2 2-2 5-4 7-6z" class="e"></path><path d="M652 647c1 0 2 0 2 1h1c-1 1-3 1-4 1-5 1-10 1-15 4h0c-4 2-8 4-12 7-2 2-5 3-8 5-2 2-5 4-7 5l-1-1-1-1h0l6-3c2-1 5-3 7-5 5-3 9-6 13-9 6-2 12-3 19-4z" class="G"></path><path d="M598 644c3 1 6 2 8 6h0v1l1 2h1 0v1c-1 1-1 1-1 2l-1 2h0l1 2-2 2c-1 1-1 0-1 1h-1v1h0c-1-1-1-1-2-1h0c-3 2-8 1-11 0h-3l1-2c-1-1-1-2-1-3l1-1 1 1c0-1 0-1 1-2h0v-3h1c0-2 0-3 2-4 0-1 0-1 1-2 0 0 0-1 1-1l3-2z" class="F"></path><path d="M587 663l1-2c-1-1-1-2-1-3l1-1 1 1 3 2c0 1-1 1-1 2v1h-1-3z" class="C"></path><path d="M602 652l1 3h2l-1 3v3l-1 1-1-1v1l-1-1-2 1h1c-2 0-2-1-3-2v-1c1-1 2-1 2-2 1 0 1 0 1-1 1-1 2-2 2-4z" class="L"></path><path d="M601 661c0-1 0-2 2-3v1c0 1 0 1-1 2v1l-1-1zm-7-14c1 0 3 0 4 1h0c2 0 3 1 4 2v2c0 2-1 3-2 4 0 1 0 1-1 1 0 1-1 1-2 2v1h-4l-1-2c-1-1-2-1-2-2h0v-3h1c0-2 0-3 2-4 0-1 0-1 1-2z" class="E"></path><path d="M592 658c1 0 1 0 3-1 1 0 1 0 2 1v1 1h-4l-1-2z" class="P"></path><path d="M245 212h2l1 2c1 1 2 2 4 2h0c1 2 1 3 3 4 1 1 3 2 5 2h0l4 1 4-5 1-1 1 1c0 1-1 1-1 2l2 1c-1 1-1 2 0 4 2 0 8 1 10 3v2l-3-2h0c0 1 0 1 1 2h0-2c-3 0-5-1-7 0l-1-1h-3v4 2h-1l-5 1h-4v2l1 1c0 1 1 2 0 3h0v2c-1 1-1 1-1 2v1h0c-2 1-2 1-3 2h4v1c-4 1-9 1-13 1-7-1-14-2-20-1-5 0-9 2-14 4v-1c1-1 1-1 2-1 2-3 5-5 8-8 3-2 5-5 8-8 1 1 3 2 5 4l1-1v-1h-1c-2-1-3-1-4-2 0-1 0-1-1-2l-1 1-1-2c1-1 1-2 1-3 1-2 5-5 6-6v-1l2-1c1-2 3-2 5-3s3-1 4-2c1 0 1 0 2-1-1-1-1-2-1-4h0z" class="g"></path><path d="M255 238l-2-1v-1-1c0-1 0-1 2-2v1 1l-1 2 1 1zm-2 4v-1c-1 0-1-1-2-2h0 4c0 2 0 3-1 5l-1-2z" class="S"></path><path d="M269 220l2 1c-1 1-1 2 0 4-2-1-4-1-4-2s1-2 2-3z" class="C"></path><path d="M246 224c1 0 2 1 4 1v1c1 2 2 3 2 4-1 1-1 1-2 3h0-1v-1c0-1 0-2 1-3l-1-1h-2v-1c-1-1-1-2-1-3h0z" class="V"></path><path d="M245 212h2l1 2s0 1-1 2h1c0 1-1 1-1 2v1c0 1-2 0-2 2v1h0c-1-1-1 0-1-1h-2-1c-1 0-3 1-3 1l-2 1v1h-2-1v-1l2-1c1-2 3-2 5-3s3-1 4-2c1 0 1 0 2-1-1-1-1-2-1-4h0z" class="a"></path><path d="M259 229v-2c1-1 1-2 2-2h3c1 1 3 1 5 1 1 0 2 0 3 1l6 1h0c0 1 0 1 1 2h0-2c-3 0-5-1-7 0l-1-1h-3v4 2c-2-1-4-1-5-1h0l1-2-1-1-2-2z" class="K"></path><path d="M272 227l6 1h0c0 1 0 1 1 2h0-2l-2-2h-7v-1h4z" class="I"></path><path d="M259 229h2l2 2h2v2h1v2c-2-1-4-1-5-1h0l1-2-1-1-2-2z" class="J"></path><path d="M246 224l2-2c0-2-1-2 1-3h1l1 1-1 2c1 0 1 1 2 1 0-1 1-1 1-3h-1l1-1c1 2 3 3 4 4 2 1 3 1 4 2h0c-1 0-1 1-2 2v2l2 2 1 1-1 2h0c-1 1-2 0-3 0l-1-1-3-3-1-4h0v-1c-1 1-1 1-2 1h-1v-1c-2 0-3-1-4-1z" class="e"></path><path d="M257 223c2 1 3 1 4 2h0c-1 0-1 1-2 2v2l2 2c-1 1-1 1-2 1-1-1-2-2-2-3-1-2 0-4 0-6z" class="W"></path><path d="M233 238c-1-1-2-2-2-3 1-1 2-2 2-3 0-2-1-2 0-3h2l1-1c1 2 2 4 3 5v1h0l1 1h0v-1l2 1v-6c0-2 0-2-2-3h1c2 1 3 2 5 4v3c0 4 0 6-2 8-1 1-1 1-1 2-1 0-2-1-3-1v1c2 0 3 1 5 2-3 0-6-1-8-2-1-1-4-2-4-3l1-1v-1h-1z" class="F"></path><path d="M234 238c0-1 0-1 2-2l1 2c0 1-1 1-2 2l-1-1v-1z" class="N"></path><path d="M246 233c0 4 0 6-2 8-2 0-2 0-4-1 1-2 3-1 4-3 1 0 1-3 2-4z" class="O"></path><path d="M246 230h1c1 2 1 4 1 6l-1 2h0c1 1 1 0 1 1h3c0 2 0 2 2 3l1 2c1-2 1-3 1-5v-1l-1-1 1-2 1 1v2l1 1c0 1 1 2 0 3h0v2c-1 1-1 1-1 2v1h0c-2 1-2 1-3 2h4v1c-4 1-9 1-13 1-7-1-14-2-20-1-5 0-9 2-14 4v-1c1-1 1-1 2-1 2-3 5-5 8-8 3-2 5-5 8-8 1 1 3 2 5 4 0 1 3 2 4 3 2 1 5 2 8 2-2-1-3-2-5-2v-1c1 0 2 1 3 1 0-1 0-1 1-2 2-2 2-4 2-8v-3z" class="c"></path><path d="M248 239h3c0 2 0 2 2 3l1 2-2 2v1h-1c-2 0-4-1-5-3s1-3 2-5z" class="V"></path><path d="M249 244h2v2h-1c-1-1-1-1-1-2z" class="a"></path><defs><linearGradient id="y" x1="231.026" y1="243.29" x2="232.352" y2="252.085" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242222"></stop></linearGradient></defs><path fill="url(#y)" d="M220 244c1 0 2 0 3-1 2 0 4 0 6 1h1c2 1 5 2 7 3h5c3 0 8 1 11 2h4v1c-4 1-9 1-13 1-7-1-14-2-20-1-5 0-9 2-14 4v-1c1-1 1-1 2-1 2-3 5-5 8-8z"></path><path d="M242 247c3 0 8 1 11 2h4v1c-4 1-9 1-13 1l3-1c1-1 1 0 3 0-1-1-2-1-3-1-2 0-3-1-5-2z" class="G"></path><path d="M257 250h2l9-2v2c-1 1-1 1-3 1l1 1h3l-1 1h-1c0 1 0 2 1 3h2l3 3 1 1-1 1h1c0 2 1 3 1 4s0 1 1 2c-1 1-2 2-2 4l-3 3h0v3 1l-1 1v2c-1 1-2 1-4 2-1 0-2 1-3 2-2 1-5 1-7 1-1 0-2-1-3-2h0c0-3-7-8-9-10-1-1-3-3-5-4l-2-2-5-4c-6-3-10-6-16-6 1-1 2-1 2-3 2-1 3 0 5 0l1-1 1-1 1 1 1-1-2-1 1-1h-1c-1 0-1-1-1-1 6-1 13 0 20 1 4 0 9 0 13-1z" class="f"></path><path d="M224 254l1-1 1 1h3 4c-3 2-5 2-8 2h0l-1-2z" class="d"></path><path d="M224 254l1 2h-2l-1 1c2 1 5 3 7 4 1 1 2 1 3 3-6-3-10-6-16-6 1-1 2-1 2-3 2-1 3 0 5 0l1-1z" class="i"></path><path d="M256 256l-2 4v3l-1 1c-1 1 0 3 0 4l-1 1h0c-2 0-2 0-3 1s-2 1-3 2l-1-1h-2l-1-1c1-1 0-2 2-3 1 1 1 1 1 3 2 0 3-2 5-3 1 1 1 1 2 1-2-1-2-1-2-4h2v-2h0c1-3 2-4 4-6z" class="U"></path><path d="M256 256l3-2-1 1c0 1-1 2-1 3v1l-1 1v1h1c0-1 1-2 1-3l2-1 1 1c-1 2-2 2-2 4v3c0 3 1 4 4 6h-4l-1-1-1-2v1 3 1h0v2h-1l-2-2-1 2v1 2h-1c-1-1-2-2-3-2v-1c-2-1-2-1-5-1-1-1-3-3-5-4l-2-2c2 0 3 1 5 2h0l1 1h2l1 1c1-1 2-1 3-2s1-1 3-1h0l1-1c0-1-1-3 0-4l1-1v-3l2-4z" class="Q"></path><path d="M249 275c0-1 1-2 1-3 2 0 3-2 4-2s1-1 2-1v-3-1h-1 0l1-2c2 2 1 3 1 5v1 3 1h0v2h-1l-2-2-1 2v1 2h-1c-1-1-2-2-3-2v-1zm8-25h2l9-2v2c-1 1-1 1-3 1l1 1h3l-1 1h-1c0 1 0 2 1 3h2l3 3-1 1v1l-2-3-1-1c-1 0-1 1-2 1h-4-1-1l-1-1-2 1c0 1-1 2-1 3h-1v-1l1-1v-1c0-1 1-2 1-3l1-1h0c1-1 2-1 3-3h0c-1 0-2 0-2 1-2 1-10 2-12 2h-4-3-5-3-4-3l1-1-2-1 1-1h-1c-1 0-1-1-1-1 6-1 13 0 20 1 4 0 9 0 13-1z" class="R"></path><path d="M229 254c2-2 5-2 7-2v2h-3-4z" class="U"></path><path d="M257 268l1 2v2c1 1 3 2 4 3 3 0 6 1 9-1v3 1l-1 1v2c-1 1-2 1-4 2-1 0-2 1-3 2-2 1-5 1-7 1-1 0-2-1-3-2h0c0-3-7-8-9-10 3 0 3 0 5 1v1c1 0 2 1 3 2h1v-2-1l1-2 2 2h1v-2h0v-1-3-1z" class="Z"></path><path d="M258 272c1 1 3 2 4 3l-1 1c-1 0-2 0-3-1v-3z" class="V"></path><defs><linearGradient id="z" x1="270.514" y1="278.401" x2="256.491" y2="278.54" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#z)" d="M262 275c3 0 6 1 9-1v3 1l-1 1c-2 0-3 0-4 2-1 1-3 0-5 1h-3c-1-1-1-2-2-3 1 0 3 0 4 1h0c1-1 1-1 2-1s1 0 2-1h0c-1-1-2-1-3-2l1-1z"></path><path d="M263 258h4c1 0 1-1 2-1l1 1 2 3v-1l1-1 1 1-1 1h1c0 2 1 3 1 4s0 1 1 2c-1 1-2 2-2 4l-3 3h0c-3 2-6 1-9 1-1-1-3-2-4-3v-2l1 1h4c-3-2-4-3-4-6v-3c0-2 1-2 2-4h1 1z" class="I"></path><path d="M261 258h1 1c1 1 1 1 2 1 2 1 2 2 3 2v1h-2c-1 0-1 0-1 1-1 0-3-2-3-2l-3 1c0-2 1-2 2-4z" class="g"></path><path d="M275 265c0 1 0 1 1 2-1 1-2 2-2 4l-3 3h0c-3 2-6 1-9 1-1-1-3-2-4-3v-2l1 1c3 3 5 2 8 2l6-3 2-4v-1z" class="C"></path><path d="M263 258h4c1 0 1-1 2-1l1 1 2 3v-1l1-1 1 1-1 1h1c0 2 1 3 1 4v1c-1 1-3-1-4-1v-1l-2-3h-1c-1 0-1-1-3-2-1 0-1 0-2-1z" class="S"></path><path d="M261 263h1l1 1c0 1 1 1 2 2h1l1 1c0 1 0 2-1 4h-3c-3-2-4-3-4-6l2-2z" class="F"></path><path d="M261 263h1l1 1c0 1 1 1 2 2h1l1 1h-1-3c-1 1-1 0-2 0v-4z" class="E"></path><defs><linearGradient id="AA" x1="512.372" y1="860.141" x2="548.899" y2="911.152" xlink:href="#B"><stop offset="0" stop-color="#cccac8"></stop><stop offset="1" stop-color="#f1f2f2"></stop></linearGradient></defs><path fill="url(#AA)" d="M541 837v3h3c3 1 7 0 9 1l-25 63-10 26c-2 3-3 8-5 10-1-2 0-7 0-9-1-8-2-17 0-25-1-1-1-2-2-3l-1-5h0c0-1 1-1 2-2l1-1h1c0-1 1-3 1-3l6-11 1 1 19-45z"></path><path d="M521 881l1 1-9 24c-1-1-1-2-2-3l-1-5h0c0-1 1-1 2-2l1-1h1c0-1 1-3 1-3l6-11z" class="M"></path><path d="M512 896l1-1h1l-3 8-1-5h0c0-1 1-1 2-2z" class="F"></path><defs><linearGradient id="AB" x1="650.796" y1="452.43" x2="677.544" y2="439.659" xlink:href="#B"><stop offset="0" stop-color="#2e2d2d"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#AB)" d="M664 420c2-2 3-3 5-4l1 1-2 2-1 1c0 1 0 1 1 2l6 3-1 2h0 2c1 1 0 2 2 1h1l1 1h3l-1 2h-2v1c-1 1-2 1-3 1l5 1s1 1 2 1l3 3h0 1l1 3c1 1 1 1 1 2v-1c1 1 1 2 1 3 0 4-1 6-2 10-2 2-3 4-4 7 0 1 0 1-1 2h1l-1 1-3 2v2c3 1 5 0 8 0v2c-2-1-4-1-5 0l-2-1h0c-5 1-11 0-15-2l-9-2c-2-1-3-2-5-4l-1 1-3-4h0c1 0 1-1 2-2h2 1v-2c-1-2 0-6-1-8h-1l2-2-1-3h2s1 1 2 1l1-1c0-2 1-5 2-7l1-1c0-1 1-1 1-2h1v-2-1-2c1 0 1-1 2-2 0-1 0-2-1-4l1-1z"></path><path d="M668 448v-4l1-1v3h0c1 1 2 3 3 4h0l-2-1-1-1 1 4c-1-1-2-2-2-4z" class="F"></path><path d="M668 422l6 3-1 2c-2-1-3-1-5-2v-3z" class="K"></path><path d="M662 429l1 1c0 1 0 2-1 3l-1 1c-1 2-1 3-1 4-1 2-1 3-3 4h0c0-2 1-5 2-7l1-1c0-1 1-1 1-2h1v-2-1z" class="B"></path><path d="M662 454l-1-1-1 1-1-4c0-1 1-1 1-1 1 0 1 1 2 1s2 0 3 1c0 1 1 2 0 3v1l2-2h2v2l-2 1h-1c-1 0-2 0-3 1l-1-3zm11-27h2c1 1 0 2 2 1h1l1 1h3l-1 2h-2v1c-1 1-2 1-3 1-1 1-2 2-3 2h0-1v-3h0c1-1 1-2 1-3h-1l1-2z" class="I"></path><path d="M661 459c5 2 9 3 14 4 1 0 1 0 2-1 1 1 2 1 3 2-2 1-4 2-5 4v1l-9-3v-1c1 1 2 1 3 1h0c2 1 1 0 3 0 0-1 3 0 3 1l2-2h1v-1c-1 0-3 1-5 1-1 0-3-1-5-1-1-1-2-1-3-1l-1-1h0c-2-1-2-1-3-3z" class="G"></path><path d="M650 457h2l2 2c3 3 7 5 12 7l9 3h5c3 1 5 0 8 0v2c-2-1-4-1-5 0l-2-1h0c-5 1-11 0-15-2l-9-2c-2-1-3-2-5-4l-1 1-3-4h0c1 0 1-1 2-2z" class="i"></path><path d="M650 457h2l2 2-1 1 2 1c-1 1-2 1-3 1l-1 1-3-4h0c1 0 1-1 2-2z" class="h"></path><path d="M676 433l5 1s1 1 2 1l3 3h0 1l1 3-2 1c0 1 0 2-1 3l2 1-1 3h0c-1 2-5 4-7 5l-6-3h2c1 0 2 1 2 1 1 0 2-1 3-1v-1h-1-2-1c-1 0-1 0-2-1h0c-1 0-2-1-2-2-1-1-2-3-2-4v-1c1 0 1-1 1-1 0-1 1-1 1-3v-1l1-2c1 0 2-1 3-2z" class="F"></path><path d="M675 448l-3-3v-4c1-1 1-2 3-3l1 2h0c-1 3-1 4-1 6v2z" class="Y"></path><path d="M676 440c0 3 1 3 3 5 1 0 1 0 2-1 1 1 0 0 0 1 1 1 1 2 0 3h0c-2-1-3 0-4 0h-1-1v-2c0-2 0-3 1-6z" class="C"></path><path d="M674 436c1-1 2-1 4-1v1h0c1 1 1 1 2 1l1 1-2 3c1 1 3 1 4 1h3c0 1 0 2-1 3v1h-1-2v-2l-1-1h0v1c-1 1-1 1-2 1-2-2-3-2-3-5h0l-2-4z" class="I"></path><path d="M676 433l5 1s1 1 2 1l3 3h0 1l1 3-2 1h-3c-1 0-3 0-4-1l2-3-1-1c-1 0-1 0-2-1h0v-1c-2 0-3 0-4 1l-2 1 1-2c1 0 2-1 3-2z" class="i"></path><path d="M687 438c-1 1-1 1-1 2h-2c-1-1-1-2-2-2 1-1 1 0 2 0h2 0 1z" class="k"></path><path d="M688 441c1 1 1 1 1 2v-1c1 1 1 2 1 3 0 4-1 6-2 10-2 2-3 4-4 7 0 1 0 1-1 2h1l-1 1-3 2v2h-5v-1c1-2 3-3 5-4-1-1-2-1-3-2-1 1-1 1-2 1-5-1-9-2-14-4l-1-1h0l3 1c-1-2-1-3-1-5l1 3c1-1 2-1 3-1h1l2-1v-2c0-1-1-3-2-4h-1v-1l1-1 1 1c0 2 1 3 2 4l-1-4 1 1 2 1 1 1 6 3c2-1 6-3 7-5h0l1-3-2-1c1-1 1-2 1-3l2-1z" class="T"></path><path d="M688 441c1 1 1 1 1 2 0 2-1 5-3 6l1-3-2-1c1-1 1-2 1-3l2-1z" class="Q"></path><path d="M661 459l-1-1h0l3 1c2 0 6 1 8 2 1 0 2 0 3 1 1 0 2 0 3-1l2-1h1v2h1v1 1h-1c-1-1-2-1-3-2-1 1-1 1-2 1-5-1-9-2-14-4z" class="F"></path><path d="M670 452l-1-4 1 1 2 1 1 1 6 3c2-1 6-3 7-5l1 1c-1 2-3 3-5 5-2 1-3 1-6 2-2-1-5-2-6-5z" class="M"></path><path d="M538 688h0 1c0-1 0-1 1-2 0 3-2 6-1 8l-2 8 2 1-2 5-1 2 1 1c1-1 1-1 2-1l1-1v1c0 1 0 2-1 4l2-1v1c2 0 3 1 4 1l-1 1 1 1-1 4h1c1-1 1-2 2-2h1c1 1 1 0 1 1-1 2-2 4-3 5l-1 4v2c-1 1-1 1-1 2l-1 2c0 1 0 1-1 3h0v1c-1 1-1 2-1 3l-1 2-1 2h0c-1 3-2 7-4 10l-2 4-3 6c0 1 0 0-1 1s-1 2-2 3l-9 16c-1 1-3 5-5 6-1 1 0 1-1 1v-3c-1-7-3-15-1-21 1-1 1-1 1-2v-1h1 1c-2 2-2 4-2 6 0 3 0 6-1 9 2-3 4-8 5-11l-1-1v-1l2-3c0-1-1-1-1-2l-2-1-2 1-2 2v-2h1c2-2 3-3 4-6 0-1 2-4 3-5l7-22c1-2 2-5 2-7l6-17c1-6 3-12 5-18z" class="b"></path><path d="M541 714c2 0 3 1 4 1l-1 1c-2 2-3 5-4 8h0c0-2 0-2-1-4 0-1 1-2 2-4v-2z" class="H"></path><path d="M520 771c-2 5-5 10-7 15l-1 1v-4c1-2 1-4 2-6 2-3 3-6 5-9l1 3z" class="M"></path><path d="M535 722l-1-1c1 0 1-1 1-2-1-1-1-3 0-5l1-1 1 1h2l2-1v1 2c-1 2-2 3-2 4 1 2 1 2 1 4h0c-5 16-12 32-20 47l-1-3c4-10 10-20 13-30l1-4c0-1 1-3 1-4s0-2 1-2v-1h-1c0-1 0-2 1-3v-2z" class="B"></path><path d="M535 722l-1-1c1 0 1-1 1-2-1-1-1-3 0-5l1-1 1 1h2l2-1v1 2c-1 2-2 3-2 4 0 2-1 4-1 6h-1-1c1-3 2-6 2-8 1-1 1-2 1-2l-1-2c-1 1-1 2-1 3l-1 2c-1 1-1 2-1 3z" class="P"></path><path d="M544 721h1c1-1 1-2 2-2h1c1 1 1 0 1 1-1 2-2 4-3 5l-1 4v2c-1 1-1 1-1 2l-1 2c0 1 0 1-1 3h0v1c-1 1-1 2-1 3l-1 2-1 2h0c-1 3-2 7-4 10l-2 4-3 6c0 1 0 0-1 1s-1 2-2 3l-9 16c-1 1-3 5-5 6 1-7 7-14 10-21l7-14c4-7 6-14 9-22 2-5 3-9 5-14z" class="M"></path><path d="M538 688h0 1c0-1 0-1 1-2 0 3-2 6-1 8l-2 8 2 1-2 5-1 2 1 1c1-1 1-1 2-1l1-1v1c0 1 0 2-1 4h-2l-1-1-1 1c-1 2-1 4 0 5 0 1 0 2-1 2l1 1v2c-1 1-1 2-1 3-2 8-5 16-9 24-2 6-5 13-9 19l-1-1v-1l2-3c0-1-1-1-1-2l-2-1-2 1-2 2v-2h1c2-2 3-3 4-6 0-1 2-4 3-5l7-22c1-2 2-5 2-7l6-17c1-6 3-12 5-18z" class="N"></path><path d="M525 730c1-1 2-1 2-3v-1l1 2c1-1 1-3 2-4h0c-3 12-8 26-15 37 0 1 1 1 1 2l-2-1-2 1-2 2v-2h1c2-2 3-3 4-6 0-1 2-4 3-5l7-22z" class="Q"></path><path d="M538 688h0 1c0-1 0-1 1-2 0 3-2 6-1 8l-2 8 2 1-2 5-1-1-4 12c0 1-1 4-2 5h0c-1 1-1 3-2 4l-1-2v1c0 2-1 2-2 3 1-2 2-5 2-7l6-17c1-6 3-12 5-18z" class="R"></path><path d="M536 707l1-5 2 1-2 5-1-1z" class="M"></path><path d="M778 211c2 1 4 3 6 4s4 2 6 2h0l20 1v1c4 1 8 1 11 1v1c-1 1-2 1-4 1-1 0-3 1-4 1h-2l-4 2 2 1h1c1-1 2-1 3 0-4 1-6 3-9 5-2 1-4 2-6 4l1 1h1l2 1-1 2 1 2c3 3 7 6 8 10l1 1v1l-2-1c-3-2-7-2-10-3-7-1-15 0-22-1v-1c-1 0-1 0-2-1 1 0 2-1 3-1v2l1-1c0-1 0-1 2-2l-3-1c-2 2-3 2-6 2l1-1c0-1 1-2 2-3h0c0-1-3-2-5-2v-1l1-1v-3h-1l-1 1c0-1-1-2-1-3l-2 2h-3-1-6c-1-1-2-1-3 0h-2c0-1 0-1-1-2 0 0-1-1-2-1h-1l-1-2c1 0 1 0 1-1h1l-1-1c1-1 2-1 3-2h0c1 0 1 0 2-1h1 3c4-2 7-2 12-2v-1c-1-1-4 0-6-1l1-1-1-1v-1h6c3 0 6 0 8-1-1-1 0-1-1-1l1-2 2-2z" class="X"></path><path d="M785 239c1 1 2 3 3 4h-1c-2 0-2-1-4 0l-1 1h-1l-3-1c3-1 5-2 7-4z" class="G"></path><path d="M785 239c2-1 4-2 7-3l2 2c-1 1-1 1-2 1-1 1-2 3-4 4-1-1-2-3-3-4z" class="B"></path><path d="M794 225h1l2-1c1-1 5-1 6-1l1 1h1l2 1-7 4-2 2c-1-1 1-1 0-3-1 1-2 0-4 0 0-1 0-1-1-2v-1h1z" class="K"></path><path d="M794 225h1l2-1c1-1 5-1 6-1l1 1h1l2 1-7 4c0-2 2-3 4-5l-10 1z" class="e"></path><path d="M807 225h0l2 1h1c1-1 2-1 3 0-4 1-6 3-9 5-2 1-4 2-6 4l1 1h1l2 1-1 2 1 2c3 3 7 6 8 10l1 1v1l-2-1v-1c0-2-3-3-4-5h0c-1-2-3-3-5-5-1-1-2-3-3-4-1-2-2-3-2-4 0-2 1-1 0-2 0-1-1-2-1-3 2 0 3 1 4 0 1 2-1 2 0 3l2-2 7-4z" class="d"></path><path d="M778 211c2 1 4 3 6 4s4 2 6 2h0l20 1v1c4 1 8 1 11 1v1c-1 1-2 1-4 1-1 0-3 1-4 1h-2s-1-1-1-2h-4-8c-4 0-8 0-12-2-3-1-5-1-7-4h0c-1-2-1-2-1-4z" class="Q"></path><path d="M787 243l-1 1c-2 1-3 1-4 1 0 1 0 1 1 1l6-3c2-1 6-1 8-1 3 1 5 3 7 5l1-1c1 2 4 3 4 5v1c-3-2-7-2-10-3-7-1-15 0-22-1v-1c-1 0-1 0-2-1 1 0 2-1 3-1v2l1-1c0-1 0-1 2-2h1l1-1c2-1 2 0 4 0z" class="H"></path><path d="M804 247l1-1c1 2 4 3 4 5v1c-3-2-7-2-10-3-7-1-15 0-22-1v-1c5 0 10 1 15 0h0c2-1 5-1 6 0s1 0 2 0l1 1h2l1-1z" class="N"></path><path d="M768 222v-1c-1-1-4 0-6-1l1-1-1-1v-1h6 0c-1 1-2 2-3 2v1h5l1 1c1-1 2-1 3-2h3 2l1 1c1 1 4 2 6 3 1 0 1 0 2 1s2 2 2 4v1c-1 0-1-1-2 0 0 1 0 1 2 2l1-1v3l-2 3-8 5-8 3c0-1 1-2 2-3h0c0-1-3-2-5-2v-1l1-1v-3h-1l-1 1c0-1-1-2-1-3l-2 2h-3-1-6c-1-1-2-1-3 0h-2c0-1 0-1-1-2 0 0-1-1-2-1h-1l-1-2c1 0 1 0 1-1h1l-1-1c1-1 2-1 3-2h0c1 0 1 0 2-1h1 3c4-2 7-2 12-2z" class="K"></path><path d="M763 225h2v2l-1 1c1 0 1 0 2 1h-4c0-1-1-2-2-3 1 0 2-1 3-1z" class="L"></path><path d="M753 234c0-2 0-2 1-3 2 0 3 0 4-1s2-1 3 0h0c1 1 1 0 2 1 0 0 1 0 1 1h-1c-2 0-4 0-6 1l-1 1c-1-1-2-1-3 0z" class="O"></path><path d="M778 228v2c1 0 1 0 1 1 1 1 2 2 4 3h2 2l2 2-8 5c0-1-1-2-1-2-1 0-2 0-4-1 0-2-1-3-1-4l1-1v-1c0-1 1-2 2-4z" class="D"></path><path d="M778 230c1 0 1 0 1 1 1 1 2 2 4 3h2 2v1c-1 1-2 2-3 1h-1l-1 2h-1c0-2-1-3-2-4s-1-3-1-4z" class="E"></path><path d="M788 224c1 1 2 2 2 4v1c-1 0-1-1-2 0 0 1 0 1 2 2l1-1v3l-2 3-2-2h-2-2c-2-1-3-2-4-3 0-1 0-1-1-1v-2c1-1 2-1 3-3 2-1 5-1 7-1z" class="I"></path><path d="M778 228h3c0 2 3 4 2 5v1c-2-1-3-2-4-3 0-1 0-1-1-1v-2z" class="B"></path><path d="M756 224c4-2 7-2 12-2v1 1h-5v1c-1 0-2 1-3 1 1 1 2 2 2 3l-1 1c-1-1-2-1-3 0s-2 1-4 1c-1 1-1 1-1 3h-2c0-1 0-1-1-2 0 0-1-1-2-1h-1l-1-2c1 0 1 0 1-1h1l-1-1c1-1 2-1 3-2h0c1 0 1 0 2-1h1 3z" class="H"></path><path d="M756 224c4-2 7-2 12-2v1 1h-5v1c-1 0-2 1-3 1h-4l-1 1c-1 1-4 0-6 0-1 0 0 0-1 1l-1-1c1-1 2-1 3-2h0c1 0 1 0 2-1h1 3z" class="F"></path><defs><linearGradient id="AC" x1="643.208" y1="503.785" x2="664.963" y2="466.936" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#595858"></stop></linearGradient></defs><path fill="url(#AC)" d="M632 497v-1-5c1-4 3-14 6-16 1-1 1-1 1-2l2-2c2-3 4-5 5-9v-1-1l2-1 3 4 1-1c2 2 3 3 5 4l9 2c4 2 10 3 15 2l-1 4v2l-1 7h-1c0 2-1 3-2 5v1l-1 1-3 3-3 2-3 3-4 4-3 3-4 1h0c-2 0-3 0-5 2l-1-1c-1-1-2-2-4-3l-1 1s0 1 1 2l-1 1c-3 0-3-2-5-4v-1l1-1v-2c-1-1-1-1-2-1l-1 1v-1c-1-1-3-1-4-2h-1z"></path><path d="M657 466l9 2-1 1c-1 0-2-1-4-1v1c-1 0-3 0-4-1v-2z" class="D"></path><path d="M655 506h0l2-2c1-2 5-5 7-7l1 1h1 0l-4 4-3 3-4 1h0z" class="B"></path><path d="M639 503l1-1 2 2h1v-1h6c-1 1 0 1-1 2l1 2c-1-1-2-2-4-3l-1 1s0 1 1 2l-1 1c-3 0-3-2-5-4v-1z" class="C"></path><path d="M649 490h1v3c1 0 1 0 2 1v1c-2 1-2 1-4 1h-1v1h0c-2-2-1-3-1-5 1-2 1-2 3-2z" class="D"></path><path d="M666 498v-1h-2s-1-1-2-1c-2 0-3 0-4-1l-1-1-3-3c0-2 0-3 1-4 1 3 3 6 6 6 1 1 3 0 4 0l4-1v3l-3 3h0z" class="C"></path><path d="M655 487h0c-1-2-1-3-1-5l1-1v-2c2-2 4-4 6-5l-1 2h-1c-1 2-3 3-3 5h0c1 1 1 2 1 3s0 1 1 2l1 1c0 2 1 3 3 4h4c1-1 0-1 1-1l1 1-3 2c-1 0-3 1-4 0-3 0-5-3-6-6z" class="h"></path><path d="M660 476c1 1 1 2 2 3 0 1 0 2 1 2l1 1c2 0 4 1 5 2s1 2 1 3l-3 3c-1 0 0 0-1 1h-4c-2-1-3-2-3-4l-1-1c-1-1-1-1-1-2s0-2-1-3h0c0-2 2-3 3-5h1z" class="B"></path><path d="M662 486l2-1 2 2v1c-1 1-2 1-3 1-1-1-1-1-1-3h0z" class="E"></path><path d="M660 476c1 1 1 2 2 3 0 1 0 2 1 2l1 1c0 2 1 2 0 3l-2 1v-1l-2-1v-2-1c0-1 0-3-1-5h1z" class="K"></path><path d="M632 497v-1-5c1-4 3-14 6-16 1-1 1-1 1-2l2-2c2-3 4-5 5-9v-1-1l2-1 3 4h0-1c-2 1-2 2-2 5-1 1-1 2-1 3-1 1-4 3-5 5-2 2-3 5-4 7v2l-2 1v1c0 1-1 2-2 3v1c0 1-1 2-1 4 0 1 0 1 1 2h-1-1z" class="G"></path><path d="M666 468c4 2 10 3 15 2l-1 4v2l-1 7h-1c0 2-1 3-2 5v1l-1 1-3 3-3 2v-3l-4 1 3-2-1-1 3-3c0-1 0-2-1-3s-3-2-5-2l-1-1c-1 0-1-1-1-2-1-1-1-2-2-3l1-2h1 1l1-1 2 1 1-1c1-1 2-1 4 0h3 0c-1-1-2-2-3-2-2-1-3-1-5-2h-1l1-1z" class="c"></path><path d="M673 487c2-1 3-5 4-7 0 1 1 2 1 3 0 2-1 3-2 5v1l-1 1v-1h-1-1v-2z" class="S"></path><path d="M668 491h0 1l2-2c0-1 1-1 1-2h1v2h1 1v1l-3 3-3 2v-3l-4 1 3-2z" class="G"></path><path d="M663 474h3c2 1 4 4 6 5v3l-1-1-1 1c-1-1-2-1-3-2l-3-1c-1-1-1-1-2-3v-2h1z" class="Q"></path><path d="M676 478l-2 2c-2-2-4-3-5-5v-1h-1l-1-1c2 0 3 1 5 1h1c2 0 3 1 5 0l-1-1 1-1 2 2h0v2l-1 7h-1c0-1-1-2-1-3s-1-1-1-2z" class="S"></path><path d="M676 478c-1-1-1-1-1-2h5l-1 7h-1c0-1-1-2-1-3s-1-1-1-2z" class="g"></path><path d="M716 280c2-1 4-3 6-4v1l-1 2c0 1 0 0-1 1 0 1 0 2-1 3s-1 3-2 4c-2 6-4 13-5 19-1 1-1 2-1 3l-2 6c0 2-3 6-3 8 2 0 3 1 5 2 1 1 1 1 1 2 1 1 2 2 3 2v1h1l3-4c0-1 1-1 1-2l1 1h2c1 0 2 1 2 1 2 2 2 4 4 4h2v1c1 1 1 1 2 1v1c2 2 4 3 6 6h0c2 1 2 4 1 6 0 2-1 3-1 5-2 2-3 3-5 4-3 1-4 2-6 2h-1c-2-1-4-3-5-4-1 0-2 0-4-1h1l-1-1h-2v-1c-1-2-2-2-3-2v-2c-2-1-2-2-4-3-1-1-3-2-4-3h-2c-1 1-3 1-4 0l-1-2 1-1-1-1-1-1v-2l1-1c0-2 1-2 2-4h-3c-2 2-3 5-4 6-2 3-2 7-5 9l-16 37v-4l2-5c1-3 3-6 4-9v-1c1-1 1-2 1-3s1-2 1-3c1-1 1-2 1-3l1-1v-1h0c0-5 2-9 4-13l-1-4 6-12c3-2 6-7 7-10h0l1-1v2h1v-2c1-2 1-3 2-4 1-2 2-3 3-4l1-2 3-6 2-6 1 1c2-2 3-5 4-8z" class="S"></path><path d="M702 312v1c0 1-1 3-1 5-1 2-4 5-2 7h1v-1c0-1 0 0 1-1 1 0 3 1 4 2 0-1 1-1 1-2 2 0 3 1 5 2 1 1 1 1 1 2 1 1 2 2 3 2v1h1l-2 1-1-1c-1-1-2-1-3-2-1 0-2-1-3-2-1 0-1 0-2-1l-1 1c1 0 1 1 1 1 1 1 1 2 2 3l-2 1c-1-1-2-1-2-1v-1l1-1c-1 0-3-1-4-1h-3c-2 2-3 5-4 6-2 3-2 7-5 9l14-30z" class="N"></path><path d="M707 330c-1-1-1-2-2-3 0 0 0-1-1-1l1-1c1 1 1 1 2 1 1 1 2 2 3 2 1 1 2 1 3 2l1 1 2-1 3-4c0 1 1 1 1 2 0 3 1 4 2 6 0 1 0 1-1 2s-2 4-2 6h0 0c-1 0-1-1-1-1-1-1 0-2-2-4h-2l-7-7z" class="D"></path><path d="M716 280c2-1 4-3 6-4v1l-1 2c0 1 0 0-1 1 0 1 0 2-1 3s-1 3-2 4c-2 6-4 13-5 19-1 1-1 2-1 3l-2 6c0 2-3 6-3 8 0 1-1 1-1 2-1-1-3-2-4-2-1 1-1 0-1 1v1h-1c-2-2 1-5 2-7 0-2 1-4 1-5v-1l10-24c2-2 3-5 4-8z" class="P"></path><path d="M719 326c0-1 1-1 1-2l1 1h2c1 0 2 1 2 1 2 2 2 4 4 4h2v1c1 1 1 1 2 1v1c2 2 4 3 6 6h0c2 1 2 4 1 6 0 2-1 3-1 5-2 2-3 3-5 4-3 1-4 2-6 2h-1c-2-1-4-3-5-4-1 0-2 0-4-1h1l-1-1h-2v-1c-1-2-2-2-3-2v-2c-2-1-2-2-4-3-1-1-3-2-4-3h-2c-1 1-3 1-4 0l-1-2 1-1-1-1-1-1v-2l1-1c0-2 1-2 2-4 1 0 3 1 4 1l-1 1v1s1 0 2 1l2-1 7 7h2c2 2 1 3 2 4 0 0 0 1 1 1h0 0c0-2 1-5 2-6s1-1 1-2c-1-2-2-3-2-6 0-1-1-1-1-2z" class="X"></path><path d="M729 338c0-2 0-2-2-3v-1c2-1 4-1 6-1 2 2 4 3 6 6v3l-1-2-1-1c-1-2-2-3-5-4l-1 2 1 1h-1-2z" class="Q"></path><path d="M727 342l2 1c1-1 2-1 3-3v1l-1 1 1 1 2-2 2 1c0 1 1 1 0 2-1 2-1 3-3 4h0l-2 1c0 1 1 1 2 2 0 0-1 1-2 1l-1-1h-1l-3-1c1-1 1-1 3-2l-3-3v-1h0l1-2z" class="T"></path><path d="M733 348v-1h-1 0c0-1 0-1 1-2s2-1 3-1c-1 2-1 3-3 4h0z" class="J"></path><path d="M726 344c3 0 4-1 6 2-1 1-2 1-2 3l-1-1-3-3v-1h0z" class="L"></path><path d="M719 342c2-1 2-3 3-4l3-3v1c1 2 2 2 4 2l-2 3v1l-1 2h0v1l3 3c-2 1-2 1-3 2l-1 1-1-1c-1-3-4-5-5-8h0z" class="C"></path><path d="M724 346h-1c-1-1-1-1-2-3l1-1h2v4z" class="M"></path><path d="M725 336c1 2 2 2 4 2l-2 3v1l-1 2h0v1c0 1-1 1-2 1v-4c1-2 1-4 1-6z" class="B"></path><defs><linearGradient id="AD" x1="729.774" y1="354.706" x2="732.553" y2="347.729" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#363434"></stop></linearGradient></defs><path fill="url(#AD)" d="M739 339h0c2 1 2 4 1 6 0 2-1 3-1 5-2 2-3 3-5 4-3 1-4 2-6 2h-1c-2-1-4-3-5-4l-7-7h1c2 1 3 2 4 3v1c2 0 2 1 3 2v1h2v-1l1-1 3 1h1l1 1c1 0 2-1 2-1 2-1 4-2 6-4v-5-3z"></path><path d="M719 326c0-1 1-1 1-2l1 1h2c1 0 2 1 2 1 2 2 2 4 4 4h2v1c1 1 1 1 2 1v1c-2 0-4 0-6 1v1c2 1 2 1 2 3h0c-2 0-3 0-4-2v-1l-3 3c-1 1-1 3-3 4h0c0-2 1-5 2-6s1-1 1-2c-1-2-2-3-2-6 0-1-1-1-1-2z" class="V"></path><path d="M700 327c1 0 3 1 4 1l-1 1v1s1 0 2 1l2-1 7 7h2c2 2 1 3 2 4 0 0 0 1 1 1 1 3 4 5 5 8l1 1v1h-2v-1c-1-1-1-2-3-2v-1c-1-1-2-2-4-3h-1l7 7c-1 0-2 0-4-1h1l-1-1h-2v-1c-1-2-2-2-3-2v-2c-2-1-2-2-4-3-1-1-3-2-4-3h-2c-1 1-3 1-4 0l-1-2 1-1-1-1-1-1v-2l1-1c0-2 1-2 2-4z" class="H"></path><path d="M698 331h3c5 5 9 11 14 14l7 7c-1 0-2 0-4-1h1l-1-1h-2v-1c-1-2-2-2-3-2v-2c-2-1-2-2-4-3-1-1-3-2-4-3h-2c-1 1-3 1-4 0l-1-2 1-1-1-1-1-1v-2l1-1z" class="Q"></path><path d="M698 331h3l-1 1-1 2c2 2 4 4 6 5h-2c-1 1-3 1-4 0l-1-2 1-1-1-1-1-1v-2l1-1z" class="J"></path><defs><linearGradient id="AE" x1="676.359" y1="426.906" x2="755.868" y2="432.781" xlink:href="#B"><stop offset="0" stop-color="#cac9c9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AE)" d="M705 387h4l3 1s1 1 2 1v1l1 1v5l1 1c1 3-1 8-2 10l-1 4c-1 2 0 4 0 6l1 2 3-3c0-2 4-5 5-6 0 1-1 2-2 4l-2 3c0 1 0 1 2 2h0c1-1 2-1 3-1l-9 10 7 1c7-1 15-1 22 0 6 0 11 1 16 2 6 0 13-1 18 1h0-23c-5-1-14-1-20 0l-7 1c-1 0-3-1-5 0h-6l-2 1c-2 0-5 0-7-1-6 0-12 0-18-1-2-1-4-1-6-1v-1l-2 1 1-2h-3l-1-1h-1c-2 1-1 0-2-1h-2 0l1-2-6-3c-1-1-1-1-1-2l1-1 2-2c1 2 2 3 4 4l2-1v-1c-3-1-4-2-5-4h1c0-2 0-2 1-3 2 0 3-1 4-1 3 0 3-1 4-2v1h5c0-2 1-2 2-3l3-3h0c-3 1-6 3-8 2v-1h-1-1l2-1 6-4c3-1 7-3 9-5v-1l-1-1v-1c1 1 2 1 3 1 1-2 2-3 3-4h0c0-1 2-2 2-2z"></path><path d="M668 419c3 0 3 2 6 4h1c1 1 2 2 4 2h-2c1 1 1 1 2 1l1 1h-1c-2 0-4-1-5-2l-6-3c-1-1-1-1-1-2l1-1z" class="i"></path><path d="M676 420h1l1-2 2 2v2c2 1 4 2 7 3h1l1 2c-5-1-10-3-15-6l2-1z" class="M"></path><path d="M706 427c-1-2 0-5-1-7v-2h0c0-2 0-3 1-4 0 0 1-1 1-2h0c1-1 0-1 1-1-1 4-1 7-1 11-1 2-1 3 0 5h1v1h3 3l7 1h-11-12l8-1v-1z" class="D"></path><path d="M709 406c1-1 2-2 3-2h-1c-1 3-3 4-3 7-1 0 0 0-1 1h0c0 1-1 2-1 2-1 1-1 2-1 4h0v2c1 2 0 5 1 7-1 0-1-1-2-1v-3h1l-2-2v-2-1l1-2c1-1 1-2 1-4h-2v-3h1 1c2 0 2-1 3-3h1z" class="I"></path><path d="M712 404l2-3c0 4-2 7-5 10 0 4-1 10 1 14l-2 2h-1c-1-2-1-3 0-5 0-4 0-7 1-11 0-3 2-4 3-7h1z" class="h"></path><path d="M674 425c1 1 3 2 5 2 10 5 25 6 37 6l-2 1c-2 0-5 0-7-1-6 0-12 0-18-1-2-1-4-1-6-1v-1l-2 1 1-2h-3l-1-1h-1c-2 1-1 0-2-1h-2 0l1-2z" class="D"></path><path d="M714 390l1 1v5l1 1c1 3-1 8-2 10l-1 4c-1 2 0 4 0 6l1 2-1 2-3 4c-2-4-1-10-1-14 3-3 5-6 5-10 0-2 1-4 0-5v-6z" class="S"></path><path d="M722 410c0 1-1 2-2 4l-2 3c0 1 0 1 2 2h0c1-1 2-1 3-1l-9 10h-3-3v-1l2-2 3-4 1-2 3-3c0-2 4-5 5-6z" class="I"></path><path d="M717 416c0 1 0 1-1 2v3l-2 2-1-2 1-2 3-3z" class="D"></path><path d="M713 421l1 2c-2 2-3 4-3 5h-3v-1l2-2 3-4z" class="F"></path><defs><linearGradient id="AF" x1="688.39" y1="425.066" x2="703.194" y2="421.318" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#4e4c4d"></stop></linearGradient></defs><path fill="url(#AF)" d="M696 414c2 0 2 1 3 2v1c1 1 1 1 1 2s1 2 2 2 1-3 1-3v1 2l2 2h-1v3c1 0 1 1 2 1v1l-8 1c-3 0-6-1-9-2l-1-2h3l-1-2c1-1 1-2 2-2 1-2 2-3 3-5 1-1 1-1 1-2z"></path><path d="M696 414c2 0 2 1 3 2h-1-2c0 2 1 2 0 4-2 1-2 1-4 1 1-2 2-3 3-5 1-1 1-1 1-2z" class="B"></path><path d="M714 396c1 1 0 3 0 5l-2 3c-1 0-2 1-3 2h-1c-1 2-1 3-3 3h-1-1v3h2c0 2 0 3-1 4l-1 2s0 3-1 3-2-1-2-2 0-1-1-2v-1c-1-1-1-2-3-2v-1l-2-1c-1 0-2 0-3 1v-2c-2-2-3-1-5-1 0-2 1-2 2-3l3-3 7-4v1c1 0 4 1 5 2 2 0 5-1 7-2s3-3 4-5z" class="E"></path><path d="M696 407v-2-1l3 3c0-1 0-1 1-1h0c1 1 1 1 1 2v1s1-2 2-2h1c1 1 4-1 5-1h-1c-1 2-1 3-3 3h-1-1v3h2c0 2 0 3-1 4l-1 2s0 3-1 3-2-1-2-2 0-1-1-2v-1c-1-1-1-2-3-2v-1l-2-1v-1c1-2 1-3 2-4z" class="T"></path><path d="M696 407h1c1 2 3 4 4 6h2v1l-1 1c-1-2-2-2-4-2-1-1-2-1-3-1l1-1c-1-1-1-1-2 0 1-2 1-3 2-4z" class="L"></path><path d="M677 411c3 0 3-1 4-2v1h5c2 0 3-1 5 1v2c1-1 2-1 3-1l2 1v1c0 1 0 1-1 2-1 2-2 3-3 5-1 0-1 1-2 2l1 2h-3-1c-3-1-5-2-7-3v-2l-2-2-1 2h-1v-1c-3-1-4-2-5-4h1c0-2 0-2 1-3 2 0 3-1 4-1z" class="P"></path><path d="M694 412l2 1v1c0 1 0 1-1 2-1 2-2 3-3 5-1 0-1 1-2 2l1 2h-3-1c2-2 0-2 0-4l2-1v-1c-1 0-2 0-3-1v-3l2 1v-1l3-2c1-1 2-1 3-1z" class="N"></path><path d="M696 413v1c0 1 0 1-1 2-1 2-2 3-3 5-1 0-1 1-2 2l1 2-2-2v-1l4-4c0-2 0-2 1-3s1 0 1-1l1-1z" class="O"></path><path d="M705 387h4l3 1s1 1 2 1v1 6c-1 2-2 4-4 5s-5 2-7 2c-1-1-4-2-5-2v-1l-7 4h0c-3 1-6 3-8 2v-1h-1-1l2-1 6-4c3-1 7-3 9-5v-1l-1-1v-1c1 1 2 1 3 1 1-2 2-3 3-4h0c0-1 2-2 2-2z" class="X"></path><path d="M705 387h4l3 1s1 1 2 1v1 6c-1 2-2 4-4 5h-1c0-1 2-2 2-3 1-1 1 0 1-1l-1-1c-1 0-1-1-2-1h-2c-1 0-1 0-2-1 1-1 1-1 1-2-1-2-2 0-4-1 1-1 1-1 1-2h0c0-1 2-2 2-2z" class="a"></path><path d="M566 682h2v1l-5 4v1l-1 1 1 1v-1c3-2 6-3 8-4 2 1 3 2 5 3 0 1 0 1 2 2 3 2 5 5 6 9v1c0 1 0 2 2 2 1 1 3 1 4 2h2c1-1 1-1 2-1 1 1 1 1 1 2 2 1 3 0 5 1 0 1 1 1 2 1 1 1 2 1 3 2l5 3 2 2-1 1h-1l-2 3c-1-2 0-2-1-3-1 0-2 0-2 1l-1 1c-2 1-1 0-2 1l1 1c-1 1-2 1-2 2h0-2l-1 2v1 2c-3 1-8 1-12 1h-1c3 1 6 1 9 1l4-1v1l-1 2h-1-2c0 1-1 1-1 2 0 0-2 0-2 1h-3l-1 1h-1v-1l-1 1h0c-1-1-1-1-2-1l-1-1c-3-1-8 1-11 0-1-1 0-1-1-1h-2-1l-2-1c-3-1-4-2-6-4h-1 0l-1-1c-1 0-1 1-2 1h-1l-1-1-3-1v-2c-1 2-1 3-2 5v2c0 2-1 2-1 4h-1l-3 2 1-2c0-1 0-1 1-2v-2l1-4c1-1 2-3 3-5 0-1 0 0-1-1h-1c-1 0-1 1-2 2h-1l1-4-1-1 1-1c1 1 3 1 4 2l1-2c1-1 1-2 1-4 1 0 1-1 1-2v-1l3-8 2-7c1-1 1-1 1-2h1l2-6v-1h0c2-2 3-2 5-2z" class="Y"></path><path d="M586 733c-1-1-1-1-2-1l-1-2h2l1-1 2 2 3 2h-3l-1 1h-1v-1z"></path><path d="M594 728l4-1v1l-1 2h-1-2c0 1-1 1-1 2 0 0-2 0-2 1l-3-2c4 0 4-1 6-3z" class="H"></path><path d="M575 711v1c1 0 3-3 5-3v2l-4 3c-1 1-2 2-4 3 0-1 1-2 1-4l-1-1h0 1 0v-1h2z" class="L"></path><path d="M550 722c0-1 0-1 1-2 1 0 3 0 4-1h2v1c-1 2-2 3-2 6h-1l-1-1-3-1v-2z"></path><path d="M583 720l3-2c1 1 1 2 2 3l-3 2-2 1c-1 2-6 3-9 4 1-1 3-2 4-2 2-1 1-1 2-3 1-1 3-2 3-3z" class="S"></path><path d="M576 714v2c0 1 1 1 2 2 4 0 6-2 9-4l6-3c-1 1-1 2-2 3h1l-6 4-3 2c-2 0-3 1-4 2-3 0-5-2-7-4v-1c2-1 3-2 4-3z" class="O"></path><path d="M558 726l1-2c3 0 4-2 6-3v1l1 1v-2h0l1 1c1 1 3 1 4 2 2 1 2 1 3 3v1c1 1 1 0 2 0s1 1 2 1c0 1-1 1-2 2-2 0-4-2-6-1l-1 1c-2-1-2-3-3-4-2 0-1 1-3 0 0-1-1-1-1-2l-1 1h-2-1z" class="P"></path><path d="M590 704h2c1-1 1-1 2-1 1 1 1 1 1 2 2 1 3 0 5 1 0 1 1 1 2 1 1 1 2 1 3 2-2 1-2 1-4 0l-5 2h-3l-6 3c-3 2-5 4-9 4-1-1-2-1-2-2v-2l4-3c2-1 3-2 4-3 2-1 3-2 4-3 1 0 2 0 2-1z" class="K"></path><path d="M605 709l5 3 2 2-1 1h-1l-2 3c-1-2 0-2-1-3-1 0-2 0-2 1l-1 1c-2 1-1 0-2 1l1 1c-1 1-2 1-2 2h0-2l-1 2v1 2c-3 1-8 1-12 1l-1-1h-1l-1-2 2-1 3-2c-1-1-1-2-2-3l6-4h-1c1-1 1-2 2-3h3l5-2c2 1 2 1 4 0z" class="G"></path><path d="M596 713c0 1 0 1 1 2-1 1-2 2-4 2-2 1-4 3-5 4-1-1-1-2-2-3l6-4 4-1z" class="V"></path><path d="M600 712c3 0 3 0 5 2v2l-1 1c-2 1-1 0-2 1l1 1c-1 1-2 1-2 2-1-1-1-1-1-2s1-2 1-3l-1-1h-3c-1-1-1-1-1-2 1-1 3-1 4-1z" class="e"></path><path d="M605 709l5 3 2 2-1 1h-1l-2 3c-1-2 0-2-1-3-1 0-2 0-2 1v-2c-2-2-2-2-5-2-1 0-3 0-4 1l-4 1h-1c1-1 1-2 2-3h3l5-2c2 1 2 1 4 0z" class="E"></path><path d="M593 711h3v1h4c-1 0-3 0-4 1l-4 1h-1c1-1 1-2 2-3z" class="L"></path><path d="M585 723h2 1c2-1 3-2 4-3 2 0 3-1 4-2h1 0c1 1 2 1 2 3l-1 2v1 2c-3 1-8 1-12 1l-1-1h-1l-1-2 2-1z" class="H"></path><path d="M566 682h2v1l-5 4v1l-1 1 1 1v-1c3-2 6-3 8-4 2 1 3 2 5 3 0 1 0 1 2 2 3 2 5 5 6 9v1c0 1 0 2 2 2 1 1 3 1 4 2 0 1-1 1-2 1-1 1-2 2-4 3-1 1-2 2-4 3v-2c-2 0-4 3-5 3v-1c1-1 1-1 1-2l-1-1h-1l-2 2v-1c-2 0-3 0-4 1-2 1-2 1-4 0 1 2 1 4 1 6v1h-1c-1-1-2-1-4-1v1c-2 0-2 0-4-1h-4l-2-1c1-1 1-2 1-4 1 0 1-1 1-2v-1l3-8 2-7c1-1 1-1 1-2h1l2-6v-1h0c2-2 3-2 5-2z" class="P"></path><path d="M563 706l-2 2 1 2h-1c-1-1-1-2-2-2l-1-3 1-1c1 1 3 1 4 2h0z" class="E"></path><path d="M563 689c3-2 6-3 8-4 2 1 3 2 5 3h-3-2 0c-1 0-2 0-3 1h-1c-1 1-1 1-2 1l-2-1z" class="M"></path><path d="M563 706h3v1c2-1 4-3 5-5v-2c-1-2-1-4 0-6 1-1 2-1 3-1 3-1 4 2 6 4h2c0 2-1 3 0 4l2-1c0 1 0 2 2 2 1 1 3 1 4 2 0 1-1 1-2 1-1 1-2 2-4 3-1 1-2 2-4 3v-2c-2 0-4 3-5 3v-1c1-1 1-1 1-2l-1-1h-1l-2 2v-1c-2 0-3 0-4 1-2 1-2 1-4 0 1 0 1-1 2-2l-3-2h0z" class="B"></path><path d="M577 706c0-1 0-3 2-4v1c1 1 1 1 2 1l2 1-1 1c-1 1-4 1-5 0z" class="N"></path><path d="M572 709l-3-1v-1c1-1 2-2 3-2 2 1 2 1 2 3l-2 2v-1z" class="Z"></path><path d="M582 706h1l2-2c2 0 2 1 3 1-1 1-2 2-4 3-1 1-2 2-4 3v-2c-2 0-4 3-5 3v-1c1-1 1-1 1-2l-1-1c0-1 1-1 2-2 1 1 4 1 5 0z" class="C"></path><path d="M581 615c1-1 1-2 1-3v4h0c-1 1-1 1-1 2l1 1-1 1c0 1-1 1-1 3l-1 1v1c-1 0-1 1-1 1 0 1 0 2-1 2 0 2 0 3-1 4 0 1 0 2-1 3l-2 7v1c-1 3 1-2-1 2v1l-1 1v3s-1 1-1 2l-1 3v3h1v-1h0c1-1 1-1 2-1l-1 2v1l-1 1 3 3-1 1h0c-1 1 0 2-1 3 0 1-1 2-1 3 1 2 8 3 8 6 0 0 0 1 1 2h0c-1 1-3 0-4 0h-12l2 2 1 1h-1l1 1c-2 0-3 0-5 2h0v1l-2 6h-1c0 1 0 1-1 2l-2 7-3 8v1c0 1 0 2-1 2 0 2 0 3-1 4l-1 2c-1-1-3-1-4-2-1 0-2-1-4-1v-1l-2 1c1-2 1-3 1-4v-1l-1 1c-1 0-1 0-2 1l-1-1 1-2 2-5-2-1 2-8c-1-2 1-5 1-8-1 1-1 1-1 2h-1 0c2-9 4-17 7-25 4-12 8-24 15-35l1-1c0 1 0 1 1 1v-2c1-1 3-2 4-2l1-1c4-4 9-6 12-10l2 2z"></path><path d="M549 692l2 1c-1 4-4 9-4 13h0l-1-1h-1l-1-1c0-1 1-1 2-2 0-1 0-3 1-5v-1c1-2 1-3 2-4z" class="C"></path><path d="M549 692c1-1 1-1 1-2v-3c1 0 1-1 1-2s1-2 1-4h1l-1-2 1-1c1-2 1-4 2-6 0-1 1-2 2-3 1-3 1-7 2-10 1-2 1-4 3-6v1 1 1l-11 37-2-1z" class="Y"></path><path d="M549 661c2-6 11-30 14-33h1l1-1c0 1 1 2 0 3s-2 3-3 4c-1 2-2 5-3 8-1 1-1 3-2 5h1v2h-1v1h-1-1v3c1 1 1 2 1 3v1h1c0 1-1 1-1 2s2 0 1 2c-1 1-1 1-1 2s0 1-1 2c0-1-1-2 0-3 0-1 0-2 1-2l-1-1v-1-4c-1 1-2 1-2 2s1 1 1 3h-2v1l1 1-2 2h0c0 1 0 2-1 2v-3l-1-1z" class="M"></path><path d="M549 661l1 1v3 1 1c-2 4-3 9-4 14v1c1 0 1 0 1-1v-2l1-1c1 1 1 2 2 2l-2 2 1 1h0l1 1c-1 1-2 1-2 2l-1 1c1 0 1 1 1 2s0 1-1 2c0 0-1 2-1 3s0 2-1 3v3l-1 1-1-1c0 1 0 1-1 2l1 1v2l1-1 1 1h1l1 1c-1 2-2 6-3 7h-2v-1l-1 1-2 1c1-2 1-3 1-4v-1l-1 1c-1 0-1 0-2 1l-1-1 1-2 2-5-2-1 2-8 10-33z" class="L"></path><path d="M539 703c0-2 0-3 1-4l1-1c-1-3 2-5 2-7v-1h1v3l2 1c0 1 0 2-1 3v3l-1 1-1-1c0 1 0 1-1 2l1 1v2l1-1 1 1h1l1 1c-1 2-2 6-3 7h-2v-1l-1 1-2 1c1-2 1-3 1-4v-1l-1 1c-1 0-1 0-2 1l-1-1 1-2 2-5z" class="H"></path><path d="M542 713c1-2 1-4 1-6l-1-1h-2v-4l1-1c0-1 0-1 1-2l1 1c0 1 0 1-1 2l1 1v2l1-1 1 1h1l1 1c-1 2-2 6-3 7h-2z" class="Y"></path><path d="M579 613l2 2c-2 2-6 5-7 8v2l-2 4c-1 1-1 3-2 4h-1c0-2 1-4 1-5-2 2-3 2-4 4l-2 2h-2c1-1 2-3 3-4s0-2 0-3l-1 1h-1c-3 3-12 27-14 33l-10 33c-1-2 1-5 1-8-1 1-1 1-1 2h-1 0c2-9 4-17 7-25 4-12 8-24 15-35l1-1c0 1 0 1 1 1v-2c1-1 3-2 4-2l1-1c4-4 9-6 12-10z" class="Q"></path><path d="M565 627v-1c4-3 8-7 13-9h0l-6 5-6 6v1c2-2 5-5 8-6v2l-2 4c-1 1-1 3-2 4h-1c0-2 1-4 1-5-2 2-3 2-4 4l-2 2h-2c1-1 2-3 3-4s0-2 0-3z" class="H"></path><path d="M581 615c1-1 1-2 1-3v4h0c-1 1-1 1-1 2l1 1-1 1c0 1-1 1-1 3l-1 1v1c-1 0-1 1-1 1 0 1 0 2-1 2 0 2 0 3-1 4 0 1 0 2-1 3l-2 7v1c-1 3 1-2-1 2v1l-1 1v3s-1 1-1 2l-1 3v3h1v-1h0c1-1 1-1 2-1l-1 2v1l-1 1 3 3-1 1h0c-1 1 0 2-1 3 0 1-1 2-1 3 1 2 8 3 8 6 0 0 0 1 1 2h0c-1 1-3 0-4 0h-12l2 2 1 1h-1l1 1c-2 0-3 0-5 2h0v1l-2 6h-1c0 1 0 1-1 2l-2 7-3 8v1c0 1 0 2-1 2 0 2 0 3-1 4l-1 2c-1-1-3-1-4-2-1 0-2-1-4-1v-1l1-1v1h2c1-1 2-5 3-7h0c0-4 3-9 4-13l11-37 8-23c1-1 1-3 2-4l2-4v-2c1-3 5-6 7-8z" class="O"></path><path d="M562 678h1l2 2 1 1h-1l1 1c-2 0-3 0-5 2h0v1l-2 6h-1c0 1 0 1-1 2l1-2c0-2 1-3 1-5 0-1 1-2 1-3v-1h1l1-4z" class="N"></path><path d="M547 706h0c0 1 0 2 1 3l-2 5 2 1c1-1 2-3 3-4 0 2 0 3-1 4l-1 2c-1-1-3-1-4-2-1 0-2-1-4-1v-1l1-1v1h2c1-1 2-5 3-7z" class="Q"></path><path d="M581 615c1-1 1-2 1-3v4h0c-1 1-1 1-1 2l1 1-1 1c0 1-1 1-1 3l-1 1v1c-1 0-1 1-1 1 0 1 0 2-1 2-1 1-1 2-1 3l-1 1c0 1 0 2-1 3v-1-1c0-2 1-3 0-5v-1l1-2v-1l-1 1v-2c1-3 5-6 7-8z" class="N"></path><path d="M569 655v3h1v-1h0c1-1 1-1 2-1l-1 2v1l-1 1 3 3-1 1h0c-1 1 0 2-1 3 0 1-1 2-1 3 1 2 8 3 8 6 0 0 0 1 1 2h0c-1 1-3 0-4 0h-12-1c2-5 3-10 5-15l1-5c1-1 1-2 1-3z" class="P"></path><path d="M574 625l1-1v1l-1 2-26 82c-1-1-1-2-1-3 0-4 3-9 4-13l11-37 8-23c1-1 1-3 2-4l2-4z" class="d"></path><defs><linearGradient id="AG" x1="283.292" y1="334.381" x2="266.514" y2="347.21" xlink:href="#B"><stop offset="0" stop-color="#9f9d9d"></stop><stop offset="1" stop-color="#cdcccc"></stop></linearGradient></defs><path fill="url(#AG)" d="M270 299c2 1 5 1 7 1 1 1 2 1 3 1 2 1 6 0 8 1h1l1-2h1l1 4-1 2 3 2c1-1 1-1 3 0h0c0 1 0 1 1 2v2l1 2h-4v3c2-2 4-2 6-1h3c-1 1-2 1-3 2 0 1 0 2-1 2l1 1h2c0 1 1 2 1 3h1v1c1 1 1 1 1 2l-1 2s1 1 1 2v3 1l-1-1-2 1 1 1h1 0l-1 1h0c-1 2-1 3-1 4 0 4 0 6-3 9h0c-1 1-2 2-3 2-2 1-4 1-6 1h-3l1 1h0l-3-1s0 1-1 1v1c1 1 1 2 2 3h2l1 2c-1 1-1 3 0 5 0 2 0 4-1 5s-2 1-3 1l-3-3h0c-3-4-6-7-9-10l-4-5c-6-7-12-15-16-24l-2-2c-2-2-4-3-6-5h2v-1h1l5 3c0-2 0-2 1-3l13 6v-2c1 0 2 0 2-1 1-1 1-2 1-3l-2-1c2-3-1-6 1-9l-2-3v-1c1-2 2-3 2-4l-2-2 2-2z"></path><path d="M280 348l1 3h-1c-1-1-4-1-5-3h1 2 2z" class="d"></path><path d="M278 348l1-1c2-2 2-2 2-4h-2l2-1h0l1 3h0c1 2 1 3 2 5-1 1 0 2-1 3 0-1 0-1-1-2 0-2 0-2-2-3h-2z" class="R"></path><path d="M248 321h1l5 3 5 3c-1 1-1 1-2 1l-2-2c-1 1-1 2-1 3l-2-2c-2-2-4-3-6-5h2v-1z" class="a"></path><path d="M254 324c0-2 0-2 1-3l13 6c3 1 5 2 9 2h5l1 1c3 1 6 0 9-1v1c-1 0-1 0-2 1-1 0-2 0-3 1l-1 1c-11 1-17-1-27-6l-5-3z" class="k"></path><path d="M301 321h2c0 1 1 2 1 3h1c-1 1-1 2-2 3-1 0-3 3-3 4l1 3-1 1-1-1-1 1-1-2c-2-1-4 0-6 0l-2 2c-2 0-4 2-5 3v-1l3-3 1-1h0-2l1-1c1-1 2-1 3-1 1-1 1-1 2-1v-1c-3 1-6 2-9 1l-1-1h0 3c3-1 5-1 7-3l3-2 6-3z" class="f"></path><path d="M288 333c2-1 5-1 7-2v-1h-1c4-3 6-5 10-6h1c-1 1-1 2-2 3-1 0-3 3-3 4l1 3-1 1-1-1-1 1-1-2c-2-1-4 0-6 0l-2 2c-2 0-4 2-5 3v-1l3-3 1-1h0z" class="V"></path><defs><linearGradient id="AH" x1="288.754" y1="366.738" x2="276.99" y2="355.013" xlink:href="#B"><stop offset="0" stop-color="#a5a3a4"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#AH)" d="M283 353c1-1 0-2 1-3l2 3s0 1-1 1v1c1 1 1 2 2 3h2l1 2c-1 1-1 3 0 5 0 2 0 4-1 5s-2 1-3 1l-3-3h0c-3-4-6-7-9-10 4 0 4 4 8 5v-1c0-2-1-3-3-4 0 0-1-2-1-3s0-1 1-2c1 0 1 0 2 1l2-1z"></path><path d="M283 353c1-1 0-2 1-3l2 3s0 1-1 1v1c1 1 1 2 2 3l-6-4 2-1z" class="j"></path><path d="M289 358l1 2c-1 1-1 3 0 5 0 2 0 4-1 5s-2 1-3 1l-3-3c1 0 2 1 2 1 1 1 1 1 2 1 1-2 0-2 0-3 1-2 2-3 1-4 0-2-1-2-1-3l2-2z" class="Z"></path><path d="M305 324v1c1 1 1 1 1 2l-1 2s1 1 1 2v3 1l-1-1-2 1 1 1h1 0l-1 1h0c-1 2-1 3-1 4 0 4 0 6-3 9h0c-1 1-2 2-3 2-2 1-4 1-6 1h-3l1 1h0l-3-1-2-3c-1-2-1-3-2-5h1c0-3-1-4 1-6v-1c1-1 3-3 5-3l2-2c2 0 4-1 6 0l1 2 1-1 1 1 1-1-1-3c0-1 2-4 3-4 1-1 1-2 2-3z" class="X"></path><path d="M284 338c1-1 3-3 5-3l2-2c1 1 1 2 1 3l-1 2h0v1l-1 1v-2h-1-1-1c-1 0-1 0-2 1l1 2h-1l-1-2v-1z" class="U"></path><path d="M291 333c2 0 4-1 6 0l1 2h1c0 1-1 1-1 1l1 2h-1c-1 0-2 1-3 0-1 2-1 2-4 1v-1h0l1-2c0-1 0-2-1-3z" class="b"></path><path d="M284 339l1 2h1 1l-2 2h0c0 1 0 1-1 2 1 1 1 1 2 3 1 0 1 0 1 1l-1 1c2 1 3 2 5 3h0-3l1 1h0l-3-1-2-3c-1-2-1-3-2-5h1c0-3-1-4 1-6z" class="Q"></path><path d="M283 345c1 2 2 3 3 5 2 1 3 2 5 3h0-3l1 1h0l-3-1-2-3c-1-2-1-3-2-5h1z" class="B"></path><path d="M305 324v1c1 1 1 1 1 2l-1 2s1 1 1 2v3 1l-1-1-2 1 1 1h1 0l-1 1h0c-1 2-1 3-1 4 0 4 0 6-3 9h0c-1 1-2 2-3 2-2 1-4 1-6 1h0l1-1-1-2-2-1c-1-1 0-1-1-2 0 0-1-2-2-2 1-2 2-2 3-4l2 1 1-1 2 2 1-1c1-1 2 0 3 0h1c1 1 1 2 1 4h1c1-1 2-2 2-4-1 0-1-1-2-2 0 0 0-1 1-1-1-2-1-3-3-4h-1l1-1 1 1 1-1-1-3c0-1 2-4 3-4 1-1 1-2 2-3z" class="K"></path><path d="M294 343l1-1c1-1 2 0 3 0v1c0 1-1 1-2 2v1h2v1h0v2l-2 2h-1-1v-1h-2v-2h-2v1l1 1-2-1c-1-1 0-1-1-2 0 0-1-2-2-2 1-2 2-2 3-4l2 1 1-1 2 2z" class="I"></path><path d="M292 347c-1 0-2-1-3-3h1l4 1v1l-2 1z" class="N"></path><path d="M294 343l1-1c1-1 2 0 3 0v1c0 1-1 1-2 2v1h2v1h0c-1 1-2 2-4 2 0-1 0-1-1-1l-1-1 2-1v-1-2z" class="C"></path><path d="M270 299c2 1 5 1 7 1 1 1 2 1 3 1 2 1 6 0 8 1h1l1-2h1l1 4-1 2 3 2c1-1 1-1 3 0h0c0 1 0 1 1 2v2l1 2h-4v3c2-2 4-2 6-1h3c-1 1-2 1-3 2 0 1 0 2-1 2l1 1-6 3-3 2c-2 2-4 2-7 3h-3 0-5c-4 0-6-1-9-2v-2c1 0 2 0 2-1 1-1 1-2 1-3l-2-1c2-3-1-6 1-9l-2-3v-1c1-2 2-3 2-4l-2-2 2-2z" class="F"></path><path d="M281 325c0-1-1-1-1-1 1-1 2-2 4-2v3h-3z" class="B"></path><path d="M282 329c0-2 0-2-1-4h0 3c1 2 1 2 1 4h-3z" class="J"></path><path d="M275 308l2 3v2h2l2-2 2 4-3 4c-3-2-4-3-6-6 0-2 0-3 1-5z" class="E"></path><path d="M271 321l3 2h0c2 1 3 2 4 4v2h-1c-4 0-6-1-9-2v-2c1 0 2 0 2-1 1-1 1-2 1-3z" class="N"></path><path d="M270 299c2 1 5 1 7 1 1 1 2 1 3 1 2 1 6 0 8 1h1l-2 2-3 2 1 1v3c-1 2-1 3-2 5l-2-4-2 2h-2v-2l-2-3c0-2 1-4 2-5 0 0-2 1-3 2v1c-1 2-2 1-2 3v3l1 1h0c-1 3 2 6 1 10l-3-2-2-1c2-3-1-6 1-9l-2-3v-1c1-2 2-3 2-4l-2-2 2-2z" class="V"></path><path d="M277 311s1 0 1-1l-1-1h3v-1l-1-1v-1c1 0 2-1 2-2 1-1 1 0 2 0v3l1-1 1 1v3c-1 2-1 3-2 5l-2-4-2 2h-2v-2z" class="M"></path><path d="M289 302l1-2h1l1 4-1 2 3 2c1-1 1-1 3 0h0c0 1 0 1 1 2v2l1 2h-4v3c2-2 4-2 6-1h3c-1 1-2 1-3 2 0 1 0 2-1 2l1 1-6 3-3 2c-2 2-4 2-7 3 0-2 0-2-1-4v-3c-1-1-1-2-1-3h-3 0l3-4c1-2 1-3 2-5v-3l-1-1 3-2 2-2z" class="I"></path><path d="M287 314c0 3-1 6 0 9l1 1h-1-3v-2h1c-1-1-1-2-1-3 1-2 1-3 3-5z" class="D"></path><path d="M288 313l1 1c1 2 1 4 1 6v1l-1 1-1 2-1-1c-1-3 0-6 0-9l1-1z" class="h"></path><path d="M289 302l1-2h1l1 4-1 2c0 2 1 2 1 3v1h-2l-3-2-2 2v-3l-1-1 3-2 2-2z" class="G"></path><path d="M284 306l3-2c2 0 2 1 3 2-1 1-2 2-3 2l-2 2v-3l-1-1z" class="B"></path><path d="M292 310v-1c0-1-1-1-1-3l3 2c1-1 1-1 3 0h0c0 1 0 1 1 2v2l1 2h-4v3c2-2 4-2 6-1h3c-1 1-2 1-3 2 0 1 0 2-1 2l1 1-6 3-3 2h-1l-1 1c-1-2 0-3-1-5l1-1v-1c0-2 0-4-1-6l-1-1 1-1c1 0 1 1 2 0h1 0v-2h0z" class="D"></path><path d="M295 317c2-2 4-2 6-1h3c-1 1-2 1-3 2 0 1 0 2-1 2l1 1-6 3-2-1h-1l1-1-1-1v-3h3v-1z" class="K"></path><path d="M742 229c2-1 3-1 5-2l1 1h-1c0 1 0 1-1 1l1 2h1c1 0 2 1 2 1 1 1 1 1 1 2h2c1-1 2-1 3 0h6 1 3l2-2c0 1 1 2 1 3l1-1h1v3l-1 1v1c2 0 5 1 5 2h0c-1 1-2 2-2 3l-1 1c3 0 4 0 6-2l3 1c-2 1-2 1-2 2l-1 1v-2c-1 0-2 1-3 1 1 1 1 1 2 1v1c7 1 15 0 22 1l-1 1 1 1c2 0 3 0 4 1h0v1l3 1c2 1 3 2 4 3 1 0 2 1 2 2 2 0 4 1 5 2 4 2 8 5 11 8-7-4-18-12-27-9-10 3-18 9-26 16-2 2-5 7-7 8h-2l-4-2v-1c-1-1-2-1-3-2h2c1-1 1-1 2-3h-2v-3c-1 1-2 2-3 2s-1 0-2 1h-1c-1 0-2 0-3-1 0 0-1 0-2 1l-11-5-7-2c2-3 0-7 2-10 1-1 1-2 1-3h0l1-1v-1h0v-4l-1-1c1-1 1-2 1-3 0-2 0-3 1-4l3-4h2 1c0-2-2-3-2-6 0-1 1-2 1-3z" class="V"></path><path d="M764 269l2-1v4c-2 1-2 1-3 2l1 1 2 2c1 0 3 0 4-1l4-3 1 1c1-1 1-2 3-2-1 1-3 3-3 4-2 2-5 7-7 8h-2l-4-2v-1c-1-1-2-1-3-2h2c1-1 1-1 2-3h-2v-3c2-1 2-2 3-4z" class="S"></path><path d="M775 274c1-1 1-2 3-2-1 1-3 3-3 4-2 2-5 7-7 8h-2l-4-2v-1l1-1h3l1-1v1c3-2 5-4 8-6z" class="I"></path><path d="M789 263c-2 2-6 5-9 6-1 1-2 0-4 1-3 2-5 4-9 4h-1c1-1 1-2 2-3h0c0-1 1-2 1-4v-1c0-2 1-4 0-6s-1-2 0-3l-4-5 1 1v-1l-1-1c1-1 1-1 1-2h0 1l1 1 1 1 1 1 2 2v2h-1v1h0v1h1l1 3h0l-1 5h0c-1 1-1 1-1 2v1c-1 1-2 2-2 3h0c1 0 1-1 2-2 0 0 1 0 1-1 1 0 2-2 2-2 2-1 5 0 6 0l2-2h0c2-1 3-2 5-2h2z" class="Q"></path><path d="M769 251l1 1 2 2v2h-1v1h0c-1-2-3-3-4-5l2-1z" class="i"></path><path d="M770 252h5c3 1 5 2 7 1s3-1 5 0h9c2 0 3 1 4 2h1 1c1 0 3 1 4 2h-3c-2 1-4 2-6 2l-8 4h-2c-2 0-3 1-5 2h0l-2 2c-1 0-4-1-6 0 0 0-1 2-2 2 0 1-1 1-1 1-1 1-1 2-2 2h0c0-1 1-2 2-3v-1c0-1 0-1 1-2h0l1-5h0l-1-3h-1v-1h0v-1h1v-2l-2-2z" class="U"></path><path d="M797 259l-3-1-2 2-1-1c0-1-1-1 0-2h1c3-1 7-2 11 0-2 1-4 2-6 2z" class="b"></path><path d="M770 252h5c3 1 5 2 7 1s3-1 5 0h9c2 0 3 1 4 2-3 1-6 0-9 1h-5-1v2 1l-1-1h-1c-3-3-8-2-11-4l-2-2z" class="d"></path><path d="M750 254c2-2 5-3 8-3h2v1c2 0 4 3 5 4s2 6 2 7l-1 5-2 1c-1 2-1 3-3 4v-1l-1-1v-1-1c-1 1-2 1-3 1s-2 0-3-1h-2c-2 0-4-2-5-3l1-1v-2c0-1 0-1-1-2h-1c0-3 1-4 3-6l1-1z" class="J"></path><path d="M760 262v-1c2 1 2 1 3 2 0 2-1 3-3 5h-1c0-4 1-2 3-5-1-1-1-1-2-1z" class="G"></path><path d="M760 269c1 0 2-1 3-1l1 1c-1 2-1 3-3 4v-1l-1-1v-1-1z" class="F"></path><path d="M752 262c1-2 2-2 4-3 0 1 1 2 2 3-2 1-3 1-4 2-1-1-2-1-2-2z" class="E"></path><path d="M752 262c0 1 1 1 2 2 1-1 2-1 4-2h0 2c1 0 1 0 2 1-2 3-3 1-3 5l-2 1c-1-1-2-1-2-1-2-1-3-2-4-3 0-2 0-2 1-3z" class="Y"></path><path d="M742 229c2-1 3-1 5-2l1 1h-1c0 1 0 1-1 1l1 2h1c1 0 2 1 2 1 1 1 1 1 1 2h2c1-1 2-1 3 0h6 1 3l2-2c0 1 1 2 1 3l1-1h1v3l-1 1v1c2 0 5 1 5 2h0c-1 1-2 2-2 3l-1 1c3 0 4 0 6-2l3 1c-2 1-2 1-2 2l-1 1v-2c-1 0-2 1-3 1 1 1 1 1 2 1v1c-2-1-5-1-7-2-6-3-11-7-17-10h-1l3 3c-1 1-2 2-3 2l1 1h3c1 1 1 2 1 3 0 0-1 0-1 1h1c1 0 1 1 2 1h1c1 1 2 1 2 1l1 1c-2 0-4 1-5 0h-2-1c-1 1-2 1-3 2l-1 1c-1 1-1 1-1 2l-1 1c-2 2-3 3-3 6h1c1 1 1 1 1 2v2l-1 1c1 1 3 3 5 3h2c1 1 2 1 3 1s2 0 3-1v1 1l1 1v1c-1 1-2 2-3 2s-1 0-2 1h-1c-1 0-2 0-3-1 0 0-1 0-2 1l-11-5-7-2c2-3 0-7 2-10 1-1 1-2 1-3h0l1-1v-1h0v-4l-1-1c1-1 1-2 1-3 0-2 0-3 1-4l3-4h2 1c0-2-2-3-2-6 0-1 1-2 1-3z" class="C"></path><path d="M766 234l2-2c0 1 1 2 1 3l1-1h1v3l-1 1v1c2 0 5 1 5 2h0c-1 1-2 2-2 3l-1 1c3 0 4 0 6-2l3 1c-2 1-2 1-2 2l-1 1v-2c-1 0-2 1-3 1 1 1 1 1 2 1v1c-2-1-5-1-7-2 0-2-1-2-2-3h-1c-2-1-2-1-3-2-1 0-2-1-2-1v-2c0-1 0-1 1-2l1 1c0 1 0 0 1 1 1 0 3 0 4-1h0c-2-3 1 1-1-2h-1-1v-1z" class="T"></path><path d="M762 238l1 1c1 1 2 1 4 1 1 0 0 0 2 1h1c1 1 1 3 2 4 3 0 4 0 6-2l3 1c-2 1-2 1-2 2l-1 1v-2c-1 0-2 1-3 1 1 1 1 1 2 1v1c-2-1-5-1-7-2 0-2-1-2-2-3h-1c-2-1-2-1-3-2-1 0-2-1-2-1v-2z" class="Y"></path><path d="M745 233l-1-1h3c1 0 2 1 3 2l1 1 2 1h-1l3 3c-1 1-2 2-3 2h-1 0c-1 1-1 2-1 2-1 1-2 1-3 2 0 1-1 2-1 2l-1 1h-1v1h-1l-2 1c0 1 1 1 0 1-1 1-1 3-1 4v3c0 1-1 2-2 3l-1 2v-9c0-2 0-6 1-7 1-2 2-3 3-4l1-2 1-1c2-1 3-2 4-4 0-1-1-2-2-3z" class="F"></path><path d="M738 261h0l-1-3c0-1 2-3 2-5 0-1 1-3 1-3l2-2c0-1 0-2 1-3v4l-2 1c0 1 1 1 0 1-1 1-1 3-1 4v3c0 1-1 2-2 3z" class="J"></path><path d="M745 233l-1-1h3c1 0 2 1 3 2l1 1 2 1h-1l3 3c-1 1-2 2-3 2h-1 0c-1 1-1 2-1 2-1 1-2 1-3 2 0 1-1 2-1 2l-1 1h-1v1h-1v-4c1-3 4-4 5-7l2-3c-1-1-3-1-5-2z" class="I"></path><path d="M751 235l2 1h-1l3 3c-1 1-2 2-3 2h-1 0c-1 1-1 2-1 2-1 1-2 1-3 2l1-2c1-1 0-2 1-3h1c0-2 1-2 1-3v-2z" class="K"></path><path d="M737 254v9l1-2v3c1 1 2 2 3 2 2 0 2 1 4 1l1-1v-5h0 1c1 1 1 1 1 2v2l-1 1c1 1 3 3 5 3h2c1 1 2 1 3 1s2 0 3-1v1 1l1 1v1c-1 1-2 2-3 2s-1 0-2 1h-1c-1 0-2 0-3-1 0 0-1 0-2 1l-11-5 1-1v-1h-3c-2-3-2-5-2-8 1-2 1-2 1-3 0-2 1-3 1-4z" class="G"></path><path d="M761 272v1c-1 1-2 2-3 2-1-2-2-2-4-3h7z" class="C"></path><path d="M752 275v-3h2c2 1 3 1 4 3-1 0-1 0-2 1h-1c-1 0-2 0-3-1z" class="L"></path><path d="M737 263l1-2v3c1 1 2 2 3 2 2 0 2 1 4 1l1-1c1 1 2 3 2 5-1 0-2 0-2-1l-1-1c-1 0 0 0-1 1h0l-2-2-1 1c-2-1-3-1-4-2-1-2-1-3 0-4z" class="J"></path><path d="M747 245c1-1 2-1 3-2 0 0 0-1 1-2h0 1l1 1h3c1 1 1 2 1 3 0 0-1 0-1 1h1c1 0 1 1 2 1h1c1 1 2 1 2 1l1 1c-2 0-4 1-5 0h-2-1c-1 1-2 1-3 2l-1 1c-1 1-1 1-1 2l-1 1c-2 2-3 3-3 6h0v5l-1 1c-2 0-2-1-4-1-1 0-2-1-3-2v-3c1-1 2-2 2-3v-3c0-1 0-3 1-4 1 0 0 0 0-1l2-1h1v-1h1l1-1s1-1 1-2z" class="W"></path><path d="M749 255v-2l-1-1c-1 1-1 2-3 3v-1c1-1 2-2 3-2 0-2 0-2 1-3 0 1 1 2 2 3-1 1-1 1-1 2l-1 1z" class="c"></path><path d="M743 249h1v-1h1c0 2 1 3 1 3h-1c-1 1-1 1-1 2l-1 1v3c0 1-1 1-2 2 0 1 1 1-1 3 1 1 2 0 3 1 1-2 1-4 1-6 1 2 1 3 2 4h0v5l-1 1c-2 0-2-1-4-1-1 0-2-1-3-2v-3c1-1 2-2 2-3v-3c0-1 0-3 1-4 1 0 0 0 0-1l2-1z" class="K"></path><path d="M746 261h0v5l-2-2v-2l2-1z" class="J"></path><path d="M688 342c3-2 3-6 5-9 1-1 2-4 4-6h3c-1 2-2 2-2 4l-1 1v2l1 1 1 1-1 1-2 6-1 5c-1 1-2 3-2 4 0 2 2 5 3 7 1 0 1 1 2 2v1c-3 1-7 3-8 6v1l-2 1-3-1-1 1h1v2l6 3v1l13 4c1 0 3 1 5 1l5 1v3h1c0 1 1 2 0 4h-1 0c-1 0-2-1-2-1l-3-1h-4s-2 1-2 2h0c-1 1-2 2-3 4-1 0-2 0-3-1v1l1 1v1c-2 2-6 4-9 5l-6 4-2 1h1 1v1c2 1 5-1 8-2h0l-3 3c-1 1-2 1-2 3h-5v-1c-1 1-1 2-4 2-1 0-2 1-4 1-1 1-1 1-1 3h-1c1 2 2 3 5 4v1l-2 1c-2-1-3-2-4-4l-1-1c-2 1-3 2-5 4l-1 1c1 2 1 3 1 4-1 1-1 2-2 2v2 1 2h-1c0 1-1 1-1 2l-1 1c-1 2-2 5-2 7l-1 1c-1 0-2-1-2-1h-2l1 3-2 2h1c1 2 0 6 1 8v2h-1-2c-1 1-1 2-2 2h0l-2 1v1 1c-1 4-3 6-5 9l-2 2c0 1 0 1-1 2-3 2-5 12-6 16v5 1c-1-1-2-1-3-2l-5-2-1-1 1-3 3-6 2-6 1-1 2-6 1-1 10-23 1-3v-1c1-1 1-3 2-4 0-1 0-1 1-2v-1l2-5 1-2c2-5 5-10 7-15 3-8 7-17 12-24h2l1 1 1-1c-1-1-2-1-2-2h-1c0-2 2-6 2-8l16-37z" class="P"></path><path d="M662 410c1-2 1-3 3-4h0v5h1v-2h1v1c-1 1-1 1-1 2h-1l-3-2z" class="E"></path><path d="M676 398c1 2 3 3 5 3v1l2 2-2 1-4 2-2 1v-3l1-7z" class="F"></path><path d="M624 489l1 2 2 1c0-1 1-1 1-1 1-1 2-8 3-9 1-4 3-8 5-11h1v-2c1-2 2-1 4-2l-1 1c-1 3-4 5-6 8-1 4-3 7-4 11v1c-1 1-1 2-1 3 0 2 2 3 0 4l-5-2-1-1 1-3z" class="H"></path><path d="M650 457c0-2 0-4-2-6v-1c0-1-1-3-1-4 0 0 1-1 1-2 1-2 0-2 1-4h1l1 1v1h1l1 3-2 2h1c1 2 0 6 1 8v2h-1-2z" class="E"></path><path d="M681 405h1 1v1c2 1 5-1 8-2h0l-3 3c-1 1-2 1-2 3h-5v-1c-1 1-1 2-4 2h-5v2l-1-1c-1-1-1-1-2-1h0l6-3 2-1 4-2z" class="J"></path><path d="M641 467s4-6 4-7v-1h3 0l-2 1v1 1c-1 4-3 6-5 9l-2 2c0 1 0 1-1 2-3 2-5 12-6 16v5 1c-1-1-2-1-3-2 2-1 0-2 0-4 0-1 0-2 1-3v-1c1-4 3-7 4-11 2-3 5-5 6-8l1-1zm21-57l3 2h1c0-1 0-1 1-2v-1l1 2h1c1 0 1 0 2 1l1 1v-2h5c-1 0-2 1-4 1-1 1-1 1-1 3h-1c1 2 2 3 5 4v1l-2 1c-2-1-3-2-4-4l-1-1c-2 1-3 2-5 4l-1 1c1 2 1 3 1 4-1 1-1 2-2 2v2 1 2h-1c0 1-1 1-1 2l-1 1c-1 2-2 5-2 7l-1 1c-1 0-2-1-2-1h-2-1v-1c1 0 2-1 2-1l1-1 1 1v1l1-1v-1c1-2 0-4 0-5 1-2 1-2 2-3 1-2 2-6 2-8v-1l2-1v-2-1l1-1 2-2c-1-1-1-1-1-2h-3c0-1 1-2 1-3z" class="O"></path><path d="M664 420c0-1 0-1-1-2 1-2 4-2 4-4v-1c3 0 3 1 4 2 1 2 2 3 5 4v1l-2 1c-2-1-3-2-4-4l-1-1c-2 1-3 2-5 4z" class="D"></path><defs><linearGradient id="AI" x1="678.901" y1="360.55" x2="703.49" y2="344.925" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#323030"></stop></linearGradient></defs><path fill="url(#AI)" d="M681 376v-4-1-1c0-2 2-3 3-4 1-2 1-6 2-8 2-7 5-12 8-18 1-3 2-5 3-8v2l1 1 1 1-1 1-2 6-1 5c-1 1-2 3-2 4 0 2 2 5 3 7 1 0 1 1 2 2v1c-3 1-7 3-8 6v1l-2 1-3-1-1-1-1 1v1c0 2-1 4-2 6z"></path><defs><linearGradient id="AJ" x1="677.521" y1="384.994" x2="687.215" y2="390.266" xlink:href="#B"><stop offset="0" stop-color="#313030"></stop><stop offset="1" stop-color="#4c4b4c"></stop></linearGradient></defs><path fill="url(#AJ)" d="M683 370v-1l1-1 1 1-1 1h1v2l6 3c-2 3-2 5-2 8l-1 3 1 2-1 1c-1 2-1 3-1 5h2l-2 2h-2l-1 1 1 1h1v1h0l3 1h0l-6 4-2-2v-1c-2 0-4-1-5-3 0-1 0-3 1-5 0-2-1-5 0-7 0-1 1-2 2-3 1-2 2-4 2-7 1-2 2-4 2-6z"></path><path d="M681 402h2l1-1c-1-1 0-1-1-1v-1h1 0l1 1 1-1h0l3 1h0l-6 4-2-2zm2-32v-1l1-1 1 1-1 1h1v2l6 3c-2 3-2 5-2 8l-1 3 1 2-1 1h-3c-1 0-1 0-2-2 1-2 1-3 2-4l1 1c0-2 1-8 0-10h-2v-2c0-1-1-1-1-2z" class="J"></path><path d="M691 375v1l13 4c1 0 3 1 5 1l5 1v3h1c0 1 1 2 0 4h-1 0c-1 0-2-1-2-1l-3-1h-4s-2 1-2 2h0c-1 1-2 2-3 4-1 0-2 0-3-1v1l1 1v1c-2 2-6 4-9 5h0l-3-1h0v-1h-1l-1-1 1-1h2l2-2h-2c0-2 0-3 1-5l1-1-1-2 1-3c0-3 0-5 2-8z" class="F"></path><path d="M689 383l3 3v1h0-1c-1 0-1 0-2 1l-1-2 1-3z" class="M"></path><path d="M686 399l1-2c2-1 2-1 4-3v-1h1v1h0c0 1 0 2 1 2-1 1-1 1-2 1l-1 1c0 1-1 1-1 2l-3-1z" class="E"></path><path d="M691 375v1c1 2 1 4 2 6-1 1-1 2-1 4l-3-3c0-3 0-5 2-8z" class="C"></path><defs><linearGradient id="AK" x1="697.981" y1="381.974" x2="712.047" y2="379.698" xlink:href="#B"><stop offset="0" stop-color="#4f4e4e"></stop><stop offset="1" stop-color="#787776"></stop></linearGradient></defs><path fill="url(#AK)" d="M691 376l13 4c1 0 3 1 5 1l5 1v3h1c0 1 1 2 0 4h-1 0c-1 0-2-1-2-1l-3-1h-4v-3h-1l-2-2h-2v1c1 1 0 1 1 1l1 1-1 1-2-1c0 1 0 2-1 3v-1-3c0-2 0-3-2-5h-1v2c-1 1-1 1-2 1-1-2-1-4-2-6z"></path><path d="M702 382c2 0 3 1 5 2h3v1l-1 1v1h-4v-3h-1l-2-2z" class="I"></path><defs><linearGradient id="AL" x1="753.968" y1="302.289" x2="812.446" y2="312.715" xlink:href="#B"><stop offset="0" stop-color="#b2b1b1"></stop><stop offset="1" stop-color="#e4e4e4"></stop></linearGradient></defs><path fill="url(#AL)" d="M735 286c0-2-1-3 1-4v2c1 2 2 2 4 4h0l3 1 1 1v-1l2-2 2 1c1 0 3 0 5 1 1 1 3 1 5 1-1 0-2 1-3 2l-1 1h-1l-2-1c-1 0-2 0-3-1h-3v2c1 0 3 0 5 1h0 2 2c1 0 2 0 3 1l2 1 4 1c15 0 29 3 44 5l12 2c2 0 6 0 8 1-7 0-15 0-22 1-11 0-21 3-31 7-2 1-5 2-7 3-1 1-2 2-2 3h3l-2 2c2 0 6-2 8-1l-2 2c-9 4-17 10-26 11-4 1-8 0-13-1-1 0-1 0-2-1v-1h-2c-2 0-2-2-4-4 0 0-1-1-2-1h-2l-1-1c0 1-1 1-1 2l-3 4h-1v-1c-1 0-2-1-3-2 0-1 0-1-1-2-2-1-3-2-5-2 0-2 3-6 3-8l2-6c0-1 0-2 1-3 2-2 2-4 3-6 0-1 1-2 2-3v-1l1-1 1-1c0 1 0 1 1 1h0l1-1 1-1 1-1 1-2c-1-1 0-1 0-2v-2-1h1c0 1 1 1 2 1h0v2h1v-1l4-2c1 0 2 0 3 1h0z"></path><path d="M744 298h1c1 0 2-1 3-1 3 1 6 1 9 2 0 3 0 4-1 7l-2 2c0-2 1-5 1-7l-1-1-1-1c-1 0-4 0-5 1l-2 1c-2 0-2 0-3-2l1-1z" class="a"></path><path d="M741 295l-1-1c2-1 4 0 5 0 5 1 9 2 14 2l4 1h-5c-1-1-2-1-3 0 2 0 3 0 4 1h3c-4 1-9-1-14-1-1 0-2 1-3 1h-1c1-1 1-1 1-2-1-1-2-1-4-1z" class="i"></path><path d="M754 300l1 1c0 2-1 5-1 7 1 1 1 2 2 3l1 1v1c1 1 2 1 3 2l3-1h0l-1 1c-2 0-3 1-5 1h-1c-1 0-3-1-4-3 1-1 1-1 1-3l-1-1v-7c1-1 2 0 2-2z" class="V"></path><path d="M741 295c2 0 3 0 4 1 0 1 0 1-1 2l-1 1c1 2 1 2 3 2l2-1c1-1 4-1 5-1l1 1c0 2-1 1-2 2v7l1 1c0 2 0 2-1 3 1 2 3 3 4 3h1l-1 2c-2-1-4-2-5-4v-4c-1-3-1-4-1-7l1-1h0c-2 0-2 0-3 1h-5-5-1v-1s1 0 2-1h1c1 0 1-1 2-1-1-1-1-1-2-1l-1-1 1-1h2 0l-1-2z" class="X"></path><path d="M748 303c1-1 1-1 3-1h0l-1 1c0 3 0 4 1 7v4c1 2 3 3 5 4l1-2c2 0 3-1 5-1-2 1-3 2-5 3-1 1-3 4-4 4l-1-1v1c-1 0-2 1-3 1v-1c-1-1-2 0-3 0-1-2-1-5-1-8l2-5c1-2 1-4 1-6z" class="W"></path><path d="M756 318h-3c-1 1-2 1-3 1l-1-4c0-3 1-5 0-7 0-2 1-3 1-5 0 3 0 4 1 7v4c1 2 3 3 5 4z" class="c"></path><path d="M735 286c0-2-1-3 1-4v2c1 2 2 2 4 4h0l3 1 1 1v-1l2-2 2 1c1 0 3 0 5 1 1 1 3 1 5 1-1 0-2 1-3 2l-1 1h-1l-2-1c-1 0-2 0-3-1h-3v2c1 0 3 0 5 1h0 2 2c1 0 2 0 3 1l2 1c-5 0-9-1-14-2-1 0-3-1-5 0l1 1 1 2h0-2l-1 1 1 1c1 0 1 0 2 1-1 0-1 1-2 1h-1c-1 1-2 1-2 1v1h1 5 5c0 2 0 4-1 6l-2 5h0l-2 3h-5l-2-3c1-1-1-3-1-4v-2-2c-1-1 0-2-1-3l2-3v-1c-1 0-2 1-3 0l1-1h3v-1l-2-2h1c2 0 2 0 3-1l-1-2h-1l-2-4v-2z" class="N"></path><path d="M736 314l1-2c1 0 2 1 3 1 2 1 3-1 5 1l-2 3h-5l-2-3z" class="H"></path><path d="M744 290v-1l2-2 2 1c1 0 3 0 5 1 1 1 3 1 5 1-1 0-2 1-3 2-2 0-1-1-3-1-1-1-3 0-5-1h-1-2z" class="I"></path><path d="M738 303h5 5c0 2 0 4-1 6 0-1-1-1-1-1-1 1-3 0-4 1v1l-1-1-1 1-1-1v-4h0l-1-2z" class="C"></path><path d="M738 303h5c1 1 1 2 2 3l-1 1-3-2h-2 0l-1-2z" class="M"></path><path d="M745 314h0c0 3 0 6 1 8 1 0 2-1 3 0v1c1 0 2-1 3-1v-1l1 1-1 2c1 0 1 0 1-1 2-1 3-1 5-2l9-5c-1 1-2 2-2 3h3l-2 2c2 0 6-2 8-1l-2 2c-9 4-17 10-26 11-4 1-8 0-13-1-1 0-1 0-2-1v-1h-2c-2 0-2-2-4-4 0 0-1-1-2-1h-2l-1-1 1-1c1-1 1-2 3-2l3 1h1l1-1-1-1c1-1 1-1 2-1 1 1 3 1 4 1v-1l2-1v1h1v-1h1v-1h5l2-3z" class="f"></path><path d="M727 323c1-1 1-1 2-1 2 1 3 2 4 4l2-1 1 1 3 3c-5-1-8-3-12-6z" class="T"></path><path d="M752 324c1 0 1 0 1-1 2-1 3-1 5-2v2l2 1-8 3v-1h-1l-1-1c1 0 2-1 2-1z" class="F"></path><path d="M728 322l1-1-1-1c1-1 1-1 2-1 1 1 3 1 4 1h2l1 2c-1 2-1 2-1 4l-1-1-2 1c-1-2-2-3-4-4-1 0-1 0-2 1v-1h1z" class="G"></path><path d="M767 316c-1 1-2 2-2 3h3l-2 2-6 3-2-1v-2l9-5zm-22-2h0c0 3 0 6 1 8 1 0 2-1 3 0v1c1 0 2-1 3-1v-1l1 1-1 2s-1 1-2 1l1 1h1v1c-4 2-8 3-13 2l-3-3c0-2 0-2 1-4l-1-2h-2v-1l2-1v1h1v-1h1v-1h5l2-3z" class="B"></path><path d="M738 317h5c-1 1-2 2-3 2s-2-1-2-1v-1z" class="N"></path><path d="M732 285c1 0 2 0 3 1h0v2l2 4h1l1 2c-1 1-1 1-3 1h-1l2 2v1h-3l-1 1c1 1 2 0 3 0v1l-2 3c1 1 0 2 1 3v2 2c0 1 2 3 1 4l2 3v1h-1v1h-1v-1l-2 1v1c-1 0-3 0-4-1-1 0-1 0-2 1l1 1-1 1h-1l-3-1c-2 0-2 1-3 2l-1 1c0 1-1 1-1 2l-3 4h-1v-1c-1 0-2-1-3-2 0-1 0-1-1-2-2-1-3-2-5-2 0-2 3-6 3-8l2-6c0-1 0-2 1-3 2-2 2-4 3-6 0-1 1-2 2-3v-1l1-1 1-1c0 1 0 1 1 1h0l1-1 1-1 1-1 1-2c-1-1 0-1 0-2v-2-1h1c0 1 1 1 2 1h0v2h1v-1l4-2z" class="M"></path><path d="M717 297v-1l1-1 1-1c0 1 0 1 1 1h0v3c0 1 0 1 1 2h-3-1v2h-1l-1-1c1-1 1-2 2-2v-1-1zm9 10h0c1-1 2-2 4-2 1-1 2-1 3 0h0c0 1 1 1 1 2s0 2-1 4h0c1 2 1 3 2 6l1 1-2 1v-1c-1 0-1-1-2-1h-1v-2l1-1c-1 0-1-1-1-2h-1l1-1c0-1 1-3 1-4h-6z" class="G"></path><path d="M713 309c1-2 0-3 1-4s1-1 1-2h1 1c1 2 2 3 3 3 0 3-2 3-3 6v1l1 1h2l1 1c-1 0-3 1-3 1-2-1-1-1-3-1-1 0-1 0-2-1h0v-2-3z" class="C"></path><path d="M720 312l2-1c0 1 0 1 1 1v-2l1-1v-1c0-1 0-1 2-1h6c0 1-1 3-1 4l-1 1h1c0 1 0 2 1 2l-1 1v2h1c1 0 1 1 2 1v1 1c-1 0-3 0-4-1-1 0-1 0-2 1l1 1-1 1-1-4c-2 0-3-1-4-2h0v-1s0-1-1-1l-2-2z" class="T"></path><path d="M723 315l1-1-1-1c1 1 1 0 2 1h1l1 1h4v2h1c1 0 1 1 2 1v1 1c-1 0-3 0-4-1-1 0-1 0-2 1l1 1-1 1-1-4c-2 0-3-1-4-2h0v-1z" class="C"></path><path d="M732 285c1 0 2 0 3 1h0v2l2 4h1l1 2c-1 1-1 1-3 1h-1 0l-4 2h-1c0 1-1 1-2 1l-4 1-3 1c-1-1-1-1-1-2v-3l1-1 1-1 1-1 1-2c-1-1 0-1 0-2v-2-1h1c0 1 1 1 2 1h0v2h1v-1l4-2z" class="W"></path><path d="M721 294l1 1h2l1 2v1h-1-1-3v-3l1-1z" class="T"></path><path d="M723 292c1 0 2-2 3-2l1 1v1h1l1-1v2c0 1 2-1 1 2h-5-1-2l-1-1 1-1 1-1z" class="J"></path><path d="M723 292c1 0 2-2 3-2l1 1v1c-1 1-2 1-2 2l-3-1 1-1z" class="F"></path><path d="M732 285c1 0 2 0 3 1h0v2l-2-1-1 1 1 1c-1 0-2 0-3-1l-3 3-1-1c-1 0-2 2-3 2l1-2c-1-1 0-1 0-2v-2-1h1c0 1 1 1 2 1h0v2h1v-1l4-2z" class="J"></path><defs><linearGradient id="AM" x1="708.543" y1="324.5" x2="722.396" y2="315.952" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#292727"></stop></linearGradient></defs><path fill="url(#AM)" d="M711 309l1 1v-1h1v3 2h0c1 1 1 1 2 1 2 0 1 0 3 1 0 0 2-1 3-1l-1-1h-2v-1c1-1 1-1 2-1l2 2c1 0 1 1 1 1v1h0c1 1 2 2 4 2l1 4h-1l-3-1c-2 0-2 1-3 2l-1 1c0 1-1 1-1 2l-3 4h-1v-1c-1 0-2-1-3-2 0-1 0-1-1-2-2-1-3-2-5-2 0-2 3-6 3-8l2-6z"></path><path d="M711 309l1 1v-1h1v3 2l-1 1h0v-1c-1 0-2 1-3 1l2-6z" class="L"></path><path d="M723 316h0c1 1 2 2 4 2l1 4h-1l-3-1c-2 0-2 1-3 2h0c-1-1-2-1-2-1v-1-1c1-1 1-2 3-2l1-2z" class="B"></path><path d="M623 492h0l1 1 5 2c1 1 2 1 3 2h1c1 1 3 1 4 2v1l1-1c1 0 1 0 2 1v2l-1 1v1c2 2 2 4 5 4l1-1c-1-1-1-2-1-2l1-1c2 1 3 2 4 3l1 1c2-2 3-2 5-2h0l4-1-1 2c-1 1-2 3-3 4l1 1h1 1c0 1 1 2 1 3l1-1h1 1v3h4l6 2v1l-1 1c-4-1-9-2-13-3 0 2 1 5 2 7v2l-1 2h0c1 2 1 2 1 4-1 0-1 0-3 1l-1-1c-1 2-2 2-4 2h-1l-1 1c1 0 2 1 2 2s-1 3-2 4l-4-1h0c-1 0-2 0-2-1l-1 1v2c1 0 1-1 2-1 1 1 1 1 1 2v1c1 1 1 3 1 4l2 2c1 1 0 2 1 3s0 1 1 2v1c0 2 0 3-1 5h-3c-7-1-12-3-17-7l-1 2 6 4v3l-1 1h-1-1-3c-1 0-2 1-3 2l1 1-3 3v3 2c-1 1-1 1-2 1l-2-1v-1c-1-1-2-1-4-1l-12-2h-4c0-1 0-2 1-3l2-4 2-4 5-11c2-2 3-4 4-6 0-1 0-2 1-3v-2c1-1 1-3 2-5-1 0-1-1-2-1 2-1 5-2 7-2s5-1 7-2c-2 0-4 0-6-2h1c0-1-2-1-3-2h0v-1c-2 0-4 0-6 1-1 0-3 0-5-1h0l1-3 3-9c1-1 2-3 3-5h0c1-2 1-4 2-6 1-3 3-6 4-9z" class="H"></path><path d="M618 552c1-2 2-3 4-5h1 0v1c2 2 3 4 5 6l2 1-1 2-4-2-7-3z" class="V"></path><path d="M647 533l1 1v4l-1 1-2-2-1 1c1 1 1 2 2 2h0v1h0c-1 0-2 0-2-1l-1 1v2 2h-1v2c0 1 0 2 1 4h0v1h-1c0-1-1-2-1-4v-1l-1-4h-1c0-2 0-4 1-5h1c0 1 0 1 1 2h0l1-1v-4h2l2-2z" class="M"></path><path d="M643 543c1 0 1-1 2-1 1 1 1 1 1 2v1c1 1 1 3 1 4l2 2c1 1 0 2 1 3s0 1 1 2v1c0 2 0 3-1 5h-3 0c1 0 1 0 2-1 0-2 0-2-2-3h1 1l1 3v-1-1c1-1 0-2 0-3h-1l-1-1c-2 0-3 0-5-1 0-1 0-2 1-3h1c0-1-1-1-1-3-1-1-1-2-1-3v-2z" class="G"></path><path d="M629 529c2 0 2 0 3 1h2l1 1h0 1 1l1-1v1l1 2-1 1v-1c-2-1-5-1-7 0-2 0-5 1-7 2-4 3-7 6-10 9 0-1 0-2 1-3v-2c1-1 1-3 2-5-1 0-1-1-2-1 2-1 5-2 7-2s5-1 7-2z" class="J"></path><path d="M617 534h2 2l-1 1c-2 1-3 3-4 5 0 1-1 1-1 1v-2c1-1 1-3 2-5z" class="C"></path><path d="M629 529c2 0 2 0 3 1h2l1 1h0 1 1l1-1v1l1 2-1 1v-1c-2-1-5-1-7 0-2 0-5 1-7 2 1-1 3-2 4-2l-1-1-1-1h-4c2 0 5-1 7-2z" class="D"></path><path d="M644 521c1 0 1-1 2-2 0 1 0 3-1 4 0 1 0 1 1 2l1 1c1 2 2 1 2 4l-1-1c-2 1-1 1-2 3l1 1-2 2h-2v4l-1 1h0c-1-1-1-1-1-2h-1c-1 1-1 3-1 5 0 1 1 3 1 5v2c1 1 0 1 0 2-1-4-2-9-2-13v1c0-1 0-2 1-3h1v-1h-1-1l-2-2h0c-2 0-3-1-5-1 2-1 5-1 7 0v1l1-1-1-2c2-1 3-4 4-6 0-1 1-1 2-2v-2z" class="E"></path><path d="M646 519l1-1c0-1 1-1 1-3h0 1 2s1 0 2 1c1 0 1 0 2 1l-1 1 1 2c-2 0-2 0-3-1l-1 1c1 1 3 1 4 2l1-1c1-2 1-2 0-3l1-1 1 1c0 2 1 5 2 7v2l-1 2h0c1 2 1 2 1 4-1 0-1 0-3 1l-1-1c-1 2-2 2-4 2h-1l-1 1c1 0 2 1 2 2s-1 3-2 4l-4-1v-1h0c-1 0-1-1-2-2l1-1 2 2 1-1v-4l-1-1-1-1c1-2 0-2 2-3l1 1c0-3-1-2-2-4l-1-1c-1-1-1-1-1-2 1-1 1-3 1-4z" class="L"></path><path d="M656 521c1-2 1-2 0-3l1-1 1 1c0 2 1 5 2 7v2l-1 2h0c1 2 1 2 1 4-1 0-1 0-3 1l-1-1v-1c-1 0-1 0-2 1h0-1v-1c-1-1-1-2-2-4-1-1-2-3-3-4v-2c1 0 1 0 2 1 0-1 0-1 1-2h0v-1c1 1 3 1 4 2l1-1z" class="F"></path><path d="M653 532c2-2 1-5 1-7 1 0 2 1 3 1-1 3 1 4 0 6h-1c-1 0-1 0-2 1h0-1v-1z" class="B"></path><path d="M656 521c1-2 1-2 0-3l1-1 1 1c0 2 1 5 2 7v2l-1 2h0c1 2 1 2 1 4-1 0-1 0-3 1l-1-1v-1h1c1-2-1-3 0-6v-1-1-1c0 1 1 1 1 1l-2-3z" class="D"></path><path d="M604 572h5 0v-2c1-3 3-5 4-8 1-4 3-7 5-10l7 3 4 2 6 4v3l-1 1h-1-1-3c-1 0-2 1-3 2l1 1-3 3v3 2c-1 1-1 1-2 1l-2-1v-1c-1-1-2-1-4-1l-12-2z" class="O"></path><path d="M625 562h1l1 1-1 1h-2 0l1-2zm0-7l4 2 6 4v3l-1 1v-3c-3-4-6-2-9-4v-3z" class="B"></path><path d="M623 492h0l1 1 5 2c1 1 2 1 3 2h1c1 1 3 1 4 2v1l1-1c1 0 1 0 2 1v2l-1 1v1c2 2 2 4 5 4l1-1c-1-1-1-2-1-2l1-1c2 1 3 2 4 3l1 1c2-2 3-2 5-2h0l4-1-1 2c-1 1-2 3-3 4l1 1h1 1c0 1 1 2 1 3l1-1h1 1v3h4l6 2v1l-1 1c-4-1-9-2-13-3l-1-1-1 1c1 1 1 1 0 3l-1 1c-1-1-3-1-4-2l1-1c1 1 1 1 3 1l-1-2 1-1c-1-1-1-1-2-1-1-1-2-1-2-1h-2-1 0c0 2-1 2-1 3l-1 1c-1 1-1 2-2 2v2c-1 1-2 1-2 2-1 2-2 5-4 6v-1l-1 1h-1-1 0l-1-1h-2c-1-1-1-1-3-1s-4 0-6-2h1c0-1-2-1-3-2h0v-1c-2 0-4 0-6 1-1 0-3 0-5-1h0l1-3 3-9c1-1 2-3 3-5h0c1-2 1-4 2-6 1-3 3-6 4-9z" class="D"></path><path d="M628 516h0c-2 1 0 0-2 2 1 1 2 1 3 2h-4l-1-1c-1 0-1 0-2-1v-1l-1-1c1-1 1-2 1-2 1-2 1-2 2-3 0 1 0 2-1 3v1h0 1l1 1h3z" class="Y"></path><path d="M624 515c0-1 1-2 2-3l1 1c1-1 1-2 2-4h1v1l1 2c0 1-1 2-2 3l-1 1h-3l-1-1z" class="E"></path><path d="M639 514h1v-2c2 0 2 0 3 1v1c0 1 0 1 1 1 0 2-1 2-2 3h0-1v-1c1-1 1 0 1-1l-1-1c-1 2-1 3-1 5l-1 1h-2c1-1 1-1 1-2h-3c0-1 1-2 2-2h1c0-2 0-2 1-3z" class="J"></path><path d="M625 521c2 0 4 1 5 2 0 1 0 1-1 2l1 1v1c1 1 2 1 3 1l1 2h-2c-1-1-1-1-3-1s-4 0-6-2h1c0-1-2-1-3-2h0 4c1-2 1-2 0-4z" class="B"></path><path d="M624 494l1 1h2l1 1 1 1c1 0 0 0 1 1-1 0-1-1-2-1h-1v1s1 1 2 1h1l2 1v1c-2 0-3 1-4 2l-1 1c-1-1-2-2-3-4l-2 1v-4c1-1 1-1 1-2l1-1z" class="P"></path><path d="M623 492h0l1 1v1l-1 1c0 1 0 1-1 2v4l2-1c1 2 2 3 3 4l1-1 1 3h-1c-1-1-3 0-4 0l-2-2-1 1v1h-1l-3 1c1-2 1-4 2-6 1-3 3-6 4-9z" class="N"></path><path d="M644 515v-2l1-1 2 2-3 7h0v2c-1 1-2 1-2 2-1 2-2 5-4 6v-1l-1 1h-1-1 0l-1-1-1-2c-1 0-2 0-3-1v-1l1-1s1 0 2-1h1-1l1-1 1 1h0c2 0 2 0 4-1 1-1 1-1 1-3s0-3 1-5l1 1c0 1 0 0-1 1v1h1 0c1-1 2-1 2-3z" class="M"></path><path d="M635 526l2-1c0 1-1 2-2 3h-2l2-2z" class="N"></path><path d="M634 524l1 2-2 2h0c-1 0-2 0-3-1v-1l1-1s1 0 2-1h1z" class="H"></path><path d="M617 507l3-1h1v1c0 1-1 2-1 4h0c0 2 0 3-1 5l-1 5h1l2-1c1 0 3 1 4 1 1 2 1 2 0 4h-4v-1c-2 0-4 0-6 1-1 0-3 0-5-1h0l1-3 3-9c1-1 2-3 3-5h0z" class="P"></path><path d="M617 507v1c0 2 1 3 0 4l-2 2c0 2 0 4-2 5 0 1-1 1-1 1h-1v1l3-9c1-1 2-3 3-5z" class="E"></path><path d="M637 500l1-1c1 0 1 0 2 1v2l-1 1v1c2 2 2 4 5 4l1-1c-1-1-1-2-1-2l1-1c2 1 3 2 4 3l1 1c2-2 3-2 5-2h0l4-1-1 2c-1 1-2 3-3 4l1 1h1 1c0 1 1 2 1 3l1-1h1 1v3h4l6 2v1l-1 1c-4-1-9-2-13-3l-1-1-1 1c1 1 1 1 0 3l-1 1c-1-1-3-1-4-2l1-1c1 1 1 1 3 1l-1-2 1-1c-1-1-1-1-2-1-1-1-2-1-2-1h-2-1 0c0 2-1 2-1 3l-1 1c-1 1-1 2-2 2h0l3-7-2-2-1 1v2c-1 0-1 0-1-1v-1c-1-1-1-1-3-1v2h-1v-2c-1-1-2-2-3-2-2-2-4-3-5-5l1-2c1-1 2-1 3-1 0 0 1-2 2-2z" class="U"></path><path d="M637 500l1-1c1 0 1 0 2 1v2l-1 1v1c2 2 2 4 5 4l1 2c-1 0-1 1-2 1-3 0-7-3-10-4v-3l2-2s1-2 2-2z" class="P"></path><path d="M637 500l1-1c1 0 1 0 2 1v2l-1 1-4 1-2 3v-3l2-2s1-2 2-2z" class="M"></path><path d="M645 507c-1-1-1-2-1-2l1-1c2 1 3 2 4 3l1 1c2-2 3-2 5-2h0l4-1-1 2c-1 1-2 3-3 4l1 1h1 1c0 1 1 2 1 3l1-1h1 1v3l-10-3c-4-2-5-3-7-7z" class="Y"></path><path d="M649 509h2c1 0 3 0 4 1l-1 2c-1 0-3 0-4-1s-1-1-1-2zm-14 52c5 2 12 5 17 4h3 1l-1 4v2c-1 4-5 7-6 10-2 1-3 3-4 3-2 3-4 5-7 6l-3 3c-3 2-6 4-9 5l-4 1c-5-2-10-4-14-6h-1l1 2h-1c0 1 0 1 1 2 2 1 6 2 8 3 0 1 0 2-1 3v1l3 2-1 1h-1-2c-1 0-1-1-2-1l-1-1c-1 0-1 0-2 1h1l-1 2c0 1 0 1 2 1v1 1l-2 1v-1c-1-1-2-1-4-1s-3 1-4 2h-3 0c-1 2-2 2-3 3-1 0-2 1-3 1-2 0-2 0-3 1l-1-1h-5v-4h-1c0 1 0 2-1 3l-2-2c-3 4-8 6-12 10l-1 1c-1 0-3 1-4 2v2c-1 0-1 0-1-1l-1 1h0-2c0-1 1-2 2-3-1-1-1-1-2-1h-1v-1l-1-3 4-6 3-3c2 0 3-2 4-3h-1l-1 1v-2-1c0-1 1-3 1-4v-1l1-3c0-1 1-2 1-3l2-4c1-1 0-1 1-2 0-1 1-1 1-2 1-1 1-2 2-3v-1l3-2 1-1c0-1 1-2 2-2l2-1c3-1 6-1 9-2h5c6-1 13-2 19-1h1c2 0 3 0 4 1v1l2 1c1 0 1 0 2-1v-2-3l3-3-1-1c1-1 2-2 3-2h3 1 1l1-1v-3z" class="H"></path><path d="M560 614l3-3c-1 3-2 4-3 7h0c-1 2-2 4-3 5l-1-3 4-6z" class="N"></path><path d="M610 576h4 0l1-1c2 0 2 0 4 1l-2 1h-4c-1 0-4 3-5 4l-1 1-2-2c2-1 4-2 5-4z" class="C"></path><path d="M607 582l-5 6h-1v-4c1-2 3-3 4-4l2 2z" class="Y"></path><defs><linearGradient id="AN" x1="597.697" y1="574.364" x2="610.719" y2="576.621" xlink:href="#B"><stop offset="0" stop-color="#474545"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#AN)" d="M616 574c2 0 3 0 4 1v1h-1c-2-1-2-1-4-1l-1 1h0-4c-5 1-9 2-13 4v-1c0-1 1-1 1-2h-1l-1-2c6-1 13-2 19-1h1z"></path><path d="M558 624l4-4c1-3 3-4 6-6l2-2c1-1 3-2 4-3h1v-1l1-1v1l2-1h0 1v1h-1l-2 2h0c-1 0-2 1-2 1l-2 1c1 1 1 1 2 1l-3 3c-1 0-2 1-3 2-2 1-6 7-8 7-1-1-1-1-2-1z" class="L"></path><path d="M560 625c2 0 6-6 8-7 1-1 2-2 3-2l3-3c-1 0-1 0-2-1l2-1s1-1 2-1l3 3c-3 4-8 6-12 10l-1 1c-1 0-3 1-4 2v2c-1 0-1 0-1-1l-1 1h0-2c0-1 1-2 2-3z" class="B"></path><path d="M652 565h3 1l-1 4v2c-1 4-5 7-6 10-2 1-3 3-4 3-2 3-4 5-7 6l-3 3c0-1 1-2 2-3h1c0-1 0-1 1-2h-1c1-1 3-2 4-3s2-3 3-4c2-1 4-3 4-5 0-1 0-1 1-2v-2l-4 7c-1 2-1 2-3 3l-2 1h0v-1c3-2 5-4 7-8h1l3-9z" class="M"></path><path d="M655 565h1l-1 4v2c-1 4-5 7-6 10-2 1-3 3-4 3 1-2 3-3 4-6l3-6c1-2 2-4 3-7zm-67 26c1 0 1 0 2-1 0-1 1-2 1-3h1c0 2-1 3-2 5 0 1 0 2-1 3s-1 2-1 3c0 2-2 3-2 5-1 2-2 7-3 8-1-1-1-2-1-3h1v-1c0-2 1-4 2-5l3-5c-6 3-11 5-16 8-2 1-3 2-5 3h-1c1-1 1-2 2-3h0c0-2 4-6 6-7l1 1-2 1h1c2-2 4-3 6-4 3-1 4-2 5-4 1 0 2-1 3-1z" class="D"></path><path d="M607 593l1 2h-1c0 1 0 1 1 2 2 1 6 2 8 3 0 1 0 2-1 3v1l3 2-1 1h-1-2c-1 0-1-1-2-1l-1-1c-1 0-1 0-2 1-1-1-4-1-5-1h-1c-3 1-6 5-8 5v-2c0-1 1-1 1-1v-3c2-1 2 0 4-1v-3h2c1-1 2-3 3-5l1-1 1-1z" class="Y"></path><path d="M627 568c1 0 5 2 5 3l3 3 1-1 1 1c-1 1-1 2-2 3v1c0 1-1 2-2 2l-2 1c-1 0-1 0-2 1-1-1-1-1-2-1l-1 1c2 2 3 2 6 2v1h-1c-3 0-3-1-5-3h-1 0l3 3h-1c-1 0-2-1-2-2l-1-1c-1 0-2 0-3-1h-1l-2 1h0 0c0-1 0-2 1-3 1 1 2 1 3 0v-1l-1-1h-4l2-1h1l2 1c1 0 1 0 2-1v-2-3l3-3z" class="L"></path><path d="M635 561c5 2 12 5 17 4l-3 9h-1c-2 4-4 6-7 8v1l-1 1c-2 0-3 1-4 2h-1-1c-1-1-1-1-2-1v-1c-3 0-4 0-6-2l1-1c1 0 1 0 2 1 1-1 1-1 2-1l2-1c1 0 2-1 2-2v-1c1-1 1-2 2-3l-1-1-1 1-3-3c0-1-4-3-5-3l-1-1c1-1 2-2 3-2h3 1 1l1-1v-3z" class="D"></path><path d="M640 571h-1v-1c0-1 0-1-1-1l1-1c1 0 1 1 2 2l1 1h1c1 3 1 3 0 5h-1c0-2 0-3-2-5z" class="F"></path><path d="M640 571c2 2 2 3 2 5v3c-1 1-1 2-2 3h1v1l-1 1c-2 0-3 1-4 2h-1-1c-1-1-1-1-2-1v-1c-3 0-4 0-6-2l1-1c1 0 1 0 2 1 1-1 1-1 2-1l2-1c1 0 2-1 2-2l2 1v1l-2 1 1 1c1-1 3-3 3-4l1-1c1-1 0-4 0-6z" class="G"></path><path d="M591 575h5l1 2h1c0 1-1 1-1 2v1c-2 2-3 4-4 7h-1-1c0 1-1 2-1 3-1 1-1 1-2 1s-2 1-3 1c-1 2-2 3-5 4-2 1-4 2-6 4h-1l2-1-1-1c-2 1-6 5-6 7h0c-1 1-1 2-2 3l-1 1v-2-1c0-1 1-3 1-4v-1l1-3c0-1 1-2 1-3l2-4c1-1 0-1 1-2 0-1 1-1 1-2 1-1 1-2 2-3v-1l3-2 1-1c0-1 1-2 2-2l2-1c3-1 6-1 9-2z" class="H"></path><path d="M582 586h2v4c-1 0-1 1-1 1-1-2-1-3-1-5z" class="N"></path><path d="M583 591l-2 1h-2v-2c0-2 0-4 2-5l1 1c0 2 0 3 1 5z" class="K"></path><path d="M591 575h5l1 2h1c0 1-1 1-1 2v1c-2 2-3 4-4 7h-1-1c0 1-1 2-1 3-1 1-1 1-2 1v-9-1c-1-1-1-2-1-3l-1 1c-1 0-3 0-4 1h-3l-5 4h0v-1l3-2 1-1c0-1 1-2 2-2l2-1c3-1 6-1 9-2z" class="F"></path><path d="M587 578h6 1l-2 1c-1 1-2 2-4 3v-1c-1-1-1-2-1-3z" class="N"></path><path d="M591 575h5l1 2h1c0 1-1 1-1 2v1c-2 2-3 4-4 7-1-2 1-4 1-6l-2-2 2-1h3c-2-2-2-1-4-1-1 0-1-1-2-2z" class="T"></path><path d="M588 582c2-1 3-2 4-3l2 2c0 2-2 4-1 6h-1-1c0 1-1 2-1 3-1 1-1 1-2 1v-9z" class="G"></path><path d="M364 482l1-1c0-1-1-3-1-4v-1l2-1 1 2 1 1h2c2 1 2 3 4 3v-1-1c1 0 2-1 4-1 4 0 8-1 12-1l8 1c2 1 4 1 5 2l3 3 1 3c1 2 2 5 3 7 2 2 4 4 5 6h1c0 2 3 8 5 9l1 2h-1l-2-2c-1 2-1 4-2 6 0 3 2 3 3 5h-1c0 2 0 2 1 4 1 1 1 3 1 4l-2-3c0 2 1 4 1 6 1 2 1 4 1 6l1 2h-1c-1 1-1 1-2 1h-7c-2 1-3 0-5 0h-2l-5-2-12-4v-1h1c-1-2-5-4-6-6-3-1-6-4-8-6s-4-5-6-7l-2-2c-2-2-3-3-5-2l-1-1 1-2-2-2-3 3h0l1-6c-1 0-1 0-2-1l-2-3c-3-1-5-3-8-5h0l-2-2c0-1-1-1-1-2l1-1h0c-2 0-3 0-4-1-2-1-4-1-6-1h-1l-2-2h2v-1l-1-1h3 0c1 1 2 1 2 2 3 0 5 2 7 3v-1l2 1 2-1 2 1c2 1 5 2 8 2h5l1-2h2v-2c-1 0-1-1-1-1l-1-1z" class="c"></path><path d="M366 497v-2c2 0 3 1 5-1l1-1v2h1v2h-2-3-1-1z" class="K"></path><path d="M373 495l2-1 1 1h0c2-1 3 0 4 0 0 2 0 3-1 4-1 0-1 0-2-1-1 0-2 0-2-1h-2v-2z" class="I"></path><path d="M378 490l4-1c1 2 4 1 6 1h1 0l-1 2h-5c-2 0-3 0-5-1-2 1-6 0-8 0 2-1 4 0 6-1h2z" class="b"></path><path d="M376 490h2 1c1 0 1 1 2 0v1h-1-2c-2 1-6 0-8 0 2-1 4 0 6-1z" class="i"></path><path d="M348 485l2 1c2 1 5 2 8 2h5l4 1 1-1s1 0 1 1h2v-1l3 1s2 0 2 1c-2 1-4 0-6 1-4 0-9-1-14-2h0l-4-1h-2l-4-2 2-1z" class="d"></path><path d="M379 480l3 1v1l2 2c2 3 2 4 5 6h-1c-2 0-5 1-6-1l-4 1h-2c0-1-2-1-2-1h1c1-1 1-2 1-4v-2c1-2 1-2 3-3z" class="Z"></path><path d="M331 483h2v-1l-1-1h3 0c1 1 2 1 2 2 3 0 5 2 7 3v-1l2 1 4 2h2l4 1h0v1c1 1 2 1 4 2l1 2 3 3h2 1 1l2 3-1 1 5 4 2 2c-2 0-3-1-4-1h-1l-3-4h-3c0-1-1-1-2-2l-3 1v1l-1-1h-1c-1 0-1 0-2-1l-2-3c-3-1-5-3-8-5h0l-2-2c0-1-1-1-1-2l1-1h0c-2 0-3 0-4-1-2-1-4-1-6-1h-1l-2-2z" class="e"></path><path d="M366 497h1 1l2 3-1 1-4-4h-1 2z" class="I"></path><path d="M346 492c1-1 1 0 2-1 3 2 6 4 9 5h0l6 4-3 1v1l-1-1h-1c-1 0-1 0-2-1l-2-3c-3-1-5-3-8-5z" class="T"></path><path d="M331 483h2v-1l-1-1h3 0c1 1 2 1 2 2 3 0 5 2 7 3v-1l2 1 4 2h2l4 1h0v1c1 1 2 1 4 2l1 2 3 3h1c-1 1-3-1-4-1-5-3-10-5-14-7-1-1-2-1-3-2h0c-2 0-3 0-4-1-2-1-4-1-6-1h-1l-2-2z" class="R"></path><path d="M364 482l1-1c0-1-1-3-1-4v-1l2-1 1 2 1 1h2c2 1 2 3 4 3v-1-1c1 0 2-1 4-1 4 0 8-1 12-1l8 1 1 1c1 4 3 8 2 11l-1 1c-2 1-5 1-8 2l-4-1 1-2h0c-3-2-3-3-5-6l-2-2v-1l-3-1c-2 1-2 1-3 3v2c0 2 0 3-1 4h-1l-3-1v1h-2c0-1-1-1-1-1l-1 1-4-1 1-2h2v-2c-1 0-1-1-1-1l-1-1z" class="B"></path><path d="M364 482l1-1c0-1-1-3-1-4v-1l2-1 1 2v4l1 1v1h1v-3h1v1c0 2 0 3 1 4h3 2c0 2 0 3-1 4h-1l-3-1v1h-2c0-1-1-1-1-1l-1 1-4-1 1-2h2v-2c-1 0-1-1-1-1l-1-1z" class="C"></path><path d="M390 477l8 1 1 1c1 4 3 8 2 11h-3 0c-2-3-3-8-7-9-1-1-2-1-3-2v-2h2z" class="P"></path><path d="M379 480c1-1 2-1 4-1h1 0c2 0 3 1 4 1h0c1 2 3 4 4 5 1 2 2 4 3 5h3 0 3l-1 1c-2 1-5 1-8 2l-4-1 1-2h0c-3-2-3-3-5-6l-2-2v-1l-3-1z" class="D"></path><path d="M398 490h3l-1 1c-2 1-5 1-8 2l-4-1 1-2c3 0 6 1 9 0h0z" class="U"></path><path d="M392 493c3-1 6-1 8-2l1 1c0 3 0 6-1 9l-2 2 1 1h-1c-1 1-1 1-1 2s-1 2-2 2l-1 1-1 1h1c1 1 1 2 1 3s-1 1-1 2-1 1-1 2l3 2 2 2h-2l-1-1-1 1h0c-1 0-2-1-2-1l-1-1-1-1c-1 0-2-1-3-2l-1 1c-1-2-2-2-3-4l-2-2-5-4-2-2-5-4 1-1-2-3h3 2 2c0 1 1 1 2 1 1 1 1 1 2 1 1-1 1-2 1-4l1 1v-2l1-1v1 1 1h1 3c1 1 2 1 3 0l1 1 3 1 5 2v-1s-1-1-1-2l-2-2 1-2h-1c-1 0-1 1-2 1-1 1-1 1-1 2l-2-1v-1l2-1z" class="F"></path><path d="M373 497h2l-1 1h0-4 0l1 1-1 1h1c1 1 3 2 3 4v1l-5-4 1-1-2-3h3 2z" class="J"></path><path d="M381 511v-2l-1-1c1 0 1 0 2 1v-1-1h0l1 1c0 1 1 2 1 3 1-1 1-1 1-2l-1-1v-2l1-1 2 1c0 1 0 2-1 3v3l1 1h0c1 2 2 3 3 3h1c1 0 1 0 2 1l3 2 2 2h-2l-1-1-1 1h0c-1 0-2-1-2-1l-1-1-1-1c-1 0-2-1-3-2l-1 1c-1-2-2-2-3-4l-2-2z" class="C"></path><path d="M392 493c3-1 6-1 8-2l1 1c0 3 0 6-1 9l-2 2 1 1h-1c-1 1-1 1-1 2s-1 2-2 2l-1 1-1 1h1c1 1 1 2 1 3s-1 1-1 2-1 1-1 2c-1-1-1-1-2-1 0-2 0-3-1-5h0c1-2 1-1 3-2 0 0 1-2 2-3s1-2 2-3c-3-3-3 0-6 0l-1-1 2-2-1-1-1 1-1-1 1-2 3 1 5 2v-1s-1-1-1-2l-2-2 1-2h-1c-1 0-1 1-2 1-1 1-1 1-1 2l-2-1v-1l2-1z" class="M"></path><path d="M396 493l2 1c1 1 1 1 1 3h-2l-2-2 1-2z" class="H"></path><defs><linearGradient id="AO" x1="399.583" y1="536.539" x2="380.709" y2="510.812" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#2e2d2e"></stop></linearGradient></defs><path fill="url(#AO)" d="M363 500c1 1 2 1 2 2h3l3 4h1c1 0 2 1 4 1l5 4 2 2c1 2 2 2 3 4l1-1c1 1 2 2 3 2l1 1 1 1s1 1 2 1h0l1-1 1 1h2l1 1c1 1 2 1 3 3l-1 1 1 1v1c-1 3 1 6-2 9l-12-4v-1h1c-1-2-5-4-6-6-3-1-6-4-8-6s-4-5-6-7l-2-2c-2-2-3-3-5-2l-1-1 1-2-2-2-3 3h0l1-6h1l1 1v-1l3-1z"></path><path d="M383 513c1 2 2 2 3 4 2 2 3 4 5 6-1 0-2-1-3-1-2-1-3-3-5-5l1-1c-1-1-1-1-1-3z" class="J"></path><path d="M367 507c2 1 3 2 5 3l4 6-7-5c-1-1-4-3-4-4h2z" class="B"></path><path d="M388 522c1 0 2 1 3 1s2 1 3 2v1c1 1 2 3 4 4h0c1 1 2 1 2 3h-1c-2-1-3-3-5-5s-4-3-6-6z" class="O"></path><path d="M365 502h3l3 4h1c1 0 2 1 4 1l5 4 2 2c0 2 0 2 1 3l-1 1c-2-1-3-2-5-3s-3-2-4-3c-1-3-4-3-5-5s-2-3-4-4z" class="D"></path><path d="M363 500c1 1 2 1 2 2 2 1 3 2 4 4s4 2 5 5l-2-1c-2-1-3-2-5-3h-2c0 1 3 3 4 4v2l-2-2c-2-2-3-3-5-2l-1-1 1-2-2-2-3 3h0l1-6h1l1 1v-1l3-1z" class="I"></path><path d="M362 506h1l-2-2v-1c1 0 1-1 2 0 2 1 3 2 4 4h-2c0 1 3 3 4 4v2l-2-2c-2-2-3-3-5-2l-1-1 1-2z" class="F"></path><path d="M398 478c2 1 4 1 5 2l3 3 1 3c1 2 2 5 3 7 2 2 4 4 5 6h1c0 2 3 8 5 9l1 2h-1l-2-2c-1 2-1 4-2 6 0 3 2 3 3 5h-1c0 2 0 2 1 4 1 1 1 3 1 4l-2-3c0 2 1 4 1 6 1 2 1 4 1 6l1 2h-1c-1 1-1 1-2 1h-7c-2 1-3 0-5 0h-2l-5-2c3-3 1-6 2-9v-1l-1-1 1-1c-1-2-2-2-3-3l-1-1-2-2-3-2c0-1 1-1 1-2s1-1 1-2 0-2-1-3h-1l1-1 1-1c1 0 2-1 2-2s0-1 1-2h1l-1-1 2-2c1-3 1-6 1-9l-1-1 1-1c1-3-1-7-2-11l-1-1z" class="J"></path><path d="M403 492l1-2c1 1 1 1 1 2 1 6 4 6 0 12h0c0-2 0-4-1-6l-1 1v-2h1v-3l-1-1v-1z" class="K"></path><path d="M398 478c2 1 4 1 5 2l3 3 1 3c-1 2-1 4-1 6 0-1-1-1-1-2 0-2-1-3-2-4-1-2-1-3-1-4-1-2-1-2-3-3l-1-1z" class="Y"></path><path d="M417 509v-2h1l1 1c-1 2-1 4-2 6 0 3 2 3 3 5h-1c0 2 0 2 1 4 1 1 1 3 1 4l-2-3c-1-1-2-3-2-4-2-3-3-6-4-9h3l1-2z" class="P"></path><path d="M401 492v-2c1 0 1 1 2 2v1l1 1v3h-1v2l1-1c1 2 1 4 1 6v4c-2-2-4-4-5-7 1-3 1-6 1-9z" class="R"></path><path d="M406 492c0-2 0-4 1-6 1 2 2 5 3 7 2 2 4 4 5 6h1c0 2 3 8 5 9l1 2h-1l-2-2-1-1h-1v2l-6-5c-1-1-1-2-2-3 0-4-1-6-3-9z" class="E"></path><path d="M415 499h1c0 2 3 8 5 9l1 2h-1l-2-2-1-1h-1v2l-6-5c-1-1-1-2-2-3 2 0 2 0 4 1v2h1v-1c0-1 0-2 1-3v-1z" class="L"></path><path d="M405 504h0l2 2v1c1 2 2 3 3 5l3 6c2 4 4 8 7 12 1 2 1 4 1 6l1 2h-1c-2-1-3-4-4-6s-2-3-3-5c-2-4-3-8-5-12-1-2-3-4-4-7v-4z" class="S"></path><path d="M398 504h1l-1-1 2-2c1 3 3 5 5 7 1 3 3 5 4 7 2 4 3 8 5 12 1 2 2 3 3 5s2 5 4 6c-1 1-1 1-2 1h-7c-2 1-3 0-5 0h-2l-5-2c3-3 1-6 2-9v-1l-1-1 1-1c-1-2-2-2-3-3l-1-1-2-2-3-2c0-1 1-1 1-2s1-1 1-2 0-2-1-3h-1l1-1 1-1c1 0 2-1 2-2s0-1 1-2z" class="H"></path><path d="M398 504h1l-1-1 2-2c1 3 3 5 5 7 1 3 3 5 4 7h-1-1l-2-2c0-1 1-2 0-4-1 1-1 1-2 1h-2c0-1 0 0-1-1-2-2-2-3-2-5z" class="C"></path><path d="M399 522c2 0 3 1 5 2v1c1 0 1 1 2 2-1 1 0 1-1 2l1 1h-1v9l-5-2c3-3 1-6 2-9v-1l-1-1 1-1c-1-2-2-2-3-3z" class="N"></path><defs><linearGradient id="AP" x1="469.017" y1="757.365" x2="483.706" y2="937.229" xlink:href="#B"><stop offset="0" stop-color="#4e4f4d"></stop><stop offset="1" stop-color="#918e91"></stop></linearGradient></defs><path fill="url(#AP)" d="M429 735c1 0 2 0 4 1 1 0 2-1 4 0 1 0 2 0 3 1h3c1 0 2 1 3 0s1-1 2-1v3h1v1h0v1c1 2 1 5 3 7-1 1-1 1-2 1-1 1-3 4-4 5s-1 2-2 3h4l1-1v1c1 3 3 6 4 10l10 22 31 73 13 31 3 5 1 5c1 1 1 2 2 3-2 8-1 17 0 25 0 2-1 7 0 9 0 1 0 1-1 1-2-1-4-8-5-10l-12-29-51-121-5-12c-1-3-2-6-3-8-2-4-7-8-8-12h1l1 1v-1c1-2 0-3-1-4-1-2-2-5-2-7v-2l2 1v-2z"></path><path d="M429 735c1 0 2 0 4 1 1 0 2-1 4 0 1 0 2 0 3 1h3c1 0 2 1 3 0s1-1 2-1v3h1v1h0v1h-4l-1 1h1v1 1c-2 0-1 0-2-1h-1c-1 0-1-1-2-1l-1-1-1 1c1 1 2 2 3 2v1c1 1 1 2 2 4l1-1v1h1l1 2c-1 1-2 1-2 1-2 1-2 2-2 4l-2 1-1-2c1-1 0-1 1-1v-3l-1-1c0 1-1 1-2 1 0-1 0-1-2-1v1h-3l-1 1-1-2v-1c1-2 0-3-1-4-1-2-2-5-2-7v-2l2 1v-2z" class="M"></path><path d="M433 745h2c1 1 1 1 2 1v1h-1l3 3c0 1-1 1-2 1 0-1 0-1-2-1v1h-3c0-1 1-2 2-3v-1l-1-2z" class="C"></path><path d="M430 750v-1c1-2 0-3-1-4-1-2-2-5-2-7v-2l2 1c1 1 1 1 1 2 0 2 0 3 1 5l1 1h1l1 2v1c-1 1-2 2-2 3l-1 1-1-2z"></path><path d="M429 735c1 0 2 0 4 1 1 0 2-1 4 0 1 0 2 0 3 1h3c1 0 2 1 3 0s1-1 2-1v3s-1 1-2 1c-1 1-1 0-3 1-1 0-2 0-3-1 0 0 0-1-1-2s-1 1-3 1c-1-1-1-1-2-1v-1h-1-1-3v-2z" class="O"></path><defs><linearGradient id="AQ" x1="662.884" y1="278.885" x2="624.897" y2="269.015" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#AQ)" d="M663 195h1c-1-1-2-1-2-2v-1l-1-1c-1-2-4-4-5-6 3 1 8 5 9 8l1 1c1 1 2 1 4 3 4 5 11 12 13 19v5l2 5c-1 2-2 3-2 4l1 1-1 3 1 2-2 2v1h0l-1 2h0c-1-1-3-1-4-2l-3 16-8 20h0l-8 19h-1v-3c-1 2-1 4-2 6l-1 1v1c-1 1-1 2-2 4l-1 2c0 1 1-1 0 1s-2 5-3 8l-1 2c0 1 0 0-1 1l-1 2c0 2 1 0 0 2s-2 5-3 7l-1 1v1l-5 11v1l-2 4c0-1-1-1-1-2l1-1c-1 0-2 0-3 1-2 1-3 2-5 3-1 1-2 4-3 5-2 1-4 3-6 3-1-1-1-2-1-2 0-1 1-3 1-3 2-5 6-11 6-17h0c2-7 6-14 9-21l17-42c3-7 7-15 8-22 4-20-3-36-14-52h3c0-2-1-3 0-5h0c2-1 3-3 4-5l8 7c1 1 2 1 2 2h1 2z"></path><path d="M667 203l2 4-3 1-1-1c1-1 1-2 1-4h1z" class="D"></path><path d="M668 227h-1c0-1 0-2 1-2 0-1 1-2 2-2 2 0 2 0 3 1 0 1 1 2 0 3-1-1-2-1-4-1l-1 1z" class="f"></path><path d="M670 217h1c1 1 1 2 2 3 0 1 0 1-1 2h-2-1-1c0-1 0-2 1-3l1-1v-1z" class="c"></path><path d="M668 227l1-1c2 0 3 0 4 1 0 2 0 2-1 3-2 1-2 0-3 0l-1-2v-1z" class="N"></path><path d="M665 272c0 2 0 4-1 5l2-2-8 19h-1v-3-1l3-6v-1l1-1v-1l4-9z" class="F"></path><path d="M672 212l1-1 2 5c1 2 1 4 2 6 1 3 1 7 0 10l1 2-1 5c-1-1-1-2-1-3l1-1h-1v3l-1-1c1-9 0-16-3-25z" class="L"></path><path d="M677 222l2 2 1-1c1 1 1 2 2 3v3l1 1 1 1-1 3 1 2-2 2v1h0l-1 2h0c-1-1-3-1-4-2h0l1-5-1-2c1-3 1-7 0-10z" class="W"></path><path d="M682 229l1 1 1 1-1 3c-1-1-1-3-1-5z" class="K"></path><path d="M677 232l1-3h1 1c0 1 1 1 1 1 1 1 1 2 0 3s-1 0-2 1h1l1 1v1h0v-1l-3-1-1-2z" class="c"></path><path d="M678 234l3 1v1h0l1 3-1 2h0c-1-1-3-1-4-2h0l1-5z" class="S"></path><path d="M675 237l1 1v-3h1l-1 1c0 1 0 2 1 3h0l-3 16-8 20h0l-2 2c1-1 1-3 1-5l1-2c0-2 1-2 1-4 1-2 2-4 2-6 1-1 1-2 2-3v-3c2-5 3-11 4-17z" class="B"></path><path d="M663 195h1c-1-1-2-1-2-2v-1l-1-1c-1-2-4-4-5-6 3 1 8 5 9 8l1 1c1 1 2 1 4 3v2l-1 1c0-1-1-3-2-4l-1 1c1 4 7 9 7 14l-1 1c-1-1-2-3-3-5l-2-4c-1-1-1-2-2-3-1 1-1 2-1 3-1 2-2 2-5 2-1 0-2-1-2-2-1-1-1-2 0-3v1c1 1 1 1 3 2 1-1 2-1 3-2 0-3-1-4-2-6h2z" class="C"></path><path d="M673 211c0-5-6-10-7-14l1-1c1 1 2 3 2 4l1-1v-2c4 5 11 12 13 19v5l2 5c-1 2-2 3-2 4l-1-1v-3c-1-1-1-2-2-3l-1 1-2-2c-1-2-1-4-2-6l-2-5z" class="D"></path><path d="M680 215l3 6 2 5c-1 2-2 3-2 4l-1-1v-3c-1-4-1-7-2-11z" class="J"></path><path d="M670 197c4 5 11 12 13 19v5l-3-6c-1-2-3-4-4-6 0-1-1-3-1-3-1 0-1 1-2 0-1-2-3-4-4-6l1-1v-2z" class="F"></path><path d="M510 272l1-1 1 1c2 2 3 4 4 6l-1 1c1 2 1 3 1 5l3 7 3 9s1 1 1 2l7 18c1 4 3 7 3 11 1 2 1 4 1 7h-1c-1 2-1 3-2 4l3-1c1-1 2-1 3-1 2 0 4 0 6-1 1 1 1 2 2 2l4 4h1v3 1c-1 1-2 2-2 4 0 1-1 1-1 2v2l1 1 1 1c3-1 5-1 8-3-2 4-4 13-8 15-3 1-5 1-9 1-1 0-1-1-2-1l-1-1c-2 0-2 0-4 1v1c0 1 0 1-1 2-2-2-3-2-5-3v1 1c-2-2-4-3-6-4-1 0-1 0-2-1h-1v-1h-1l1 4c-1-1-2-3-3-4l-1-1c-1-1-1-2-2-3l-2-2c0-2 0-4-2-6-1-1-3-1-5-2l-1 1-1 1c-1-1-1-3-2-4h0c-4-3-9-6-13-7s-6 0-9 2l-2 1h-1v-2c2-1 3-3 5-5 7-7 10-16 14-25 2-5 5-11 7-17l6-15 1-2v-3l3-6z" class="J"></path><path d="M511 332c0 10 0 20 1 30v1l-2-2c0-2 0-4-2-6-1-1-3-1-5-2l-1 1-1 1c-1-1-1-3-2-4h0v-1c-1-1 0-1-1-1l-1-1h2v1h1 4c1 1 3 1 5 1 1-1 1-2 1-3 1-4-2-12 1-15z" class="Q"></path><path d="M549 345h1v3 1c-1 1-2 2-2 4 0 1-1 1-1 2v2l1 1c-1 3 1 5-1 8-1 3-4 3-7 4l-1-1c1-1 1-2 1-3h0c1-1 2-3 3-4h0l1-2c1-1 0-2 0-3v-2-6l2-2c1 1 2 1 3 1h1l-1-3z" class="N"></path><path d="M510 272c1 2 1 4 1 6v37c0 5 1 11 0 17-3 3 0 11-1 15v-1c-1-2-1-3-1-5-1-2-1-3-1-5 0-4 1-9 1-14l1-37c-1 0-2 0-3-1v-3-3l3-6z" class="i"></path><path d="M510 272c1 2 1 4 1 6-1 3-1 5-1 7-1 0-2 0-3-1v-3-3l3-6z" class="R"></path><path d="M506 283l1-2v3c1 1 2 1 3 1l-1 37c0 5-1 10-1 14 0 2 0 3 1 5 0 2 0 3 1 5v1c0 1 0 2-1 3-2 0-4 0-5-1l-9-4c2 0 5 0 6 1h6v-1l-1-1c0-3-1-4-2-7l1-2c-1-2-1-3-2-4v-2h-4l1-2h3l2-2c2 0 1 0 2-1l-2-2 1-1v-7l1 1v-1c0-1 0-1 1-2h0l-1-2v-1c-1-2 0-6 0-9 1-3 2-9 0-12-1-2-1-3-1-4v-1z" class="Z"></path><path d="M503 329l3-1v1c-1 2-1 4 0 6v2 1c0 1 0 1 1 2v2 1c1 1 1 1 1 3l-1 1c-2 0-4 0-5-1h-1 6v-1l-1-1c0-3-1-4-2-7l1-2c-1-2-1-3-2-4v-2z" class="V"></path><defs><linearGradient id="AR" x1="503.935" y1="335.088" x2="497.014" y2="329.981" xlink:href="#B"><stop offset="0" stop-color="#767474"></stop><stop offset="1" stop-color="#8c8a8a"></stop></linearGradient></defs><path fill="url(#AR)" d="M499 329h4v2c1 1 1 2 2 4l-1 2c1 3 2 4 2 7l1 1v1h-6c-1-1-4-1-6-1l-4-2c-1-2-2-2-2-5h1l1-1c1-1 1-2 3-2h1v-1l-1-1c0-1 0-1 1-2l2-2h2z"></path><path d="M492 337h3c2 1 1 1 2 1s2 0 3 1h0 2l3 3v2l-1 1c-2-1-3 0-4-1-2 0-5-2-6-3-2 0-2 0-3-1 0-1 0-2 1-3z" class="K"></path><path d="M512 272c2 2 3 4 4 6l-1 1c1 2 1 3 1 5v4c-1 2-1 2-1 4h-1c-1 4 0 8 1 12 1 7 2 14 2 21l1 1v-1h1l1 1c-1 0 0 0-1 1s-2 3-3 5c0 1-1 2-1 3h-3v-40c0-7-1-16 0-23z" class="i"></path><defs><linearGradient id="AS" x1="499.888" y1="322.549" x2="484.164" y2="349.299" xlink:href="#B"><stop offset="0" stop-color="#c2c0c0"></stop><stop offset="1" stop-color="#f2f2f2"></stop></linearGradient></defs><path fill="url(#AS)" d="M479 340c7-7 10-16 14-25l1 1-1 2c1 1 1 1 2 1 0-1 1-2 2-3v3c-1 3-1 6-3 8l2 2c-1 0-1 1-1 2-1 1-1 1-1 2l1 1v1h-1c-2 0-2 1-3 2l-1 1h-1c0 3 1 3 2 5l4 2 9 4h-4-1v-1h-2l1 1c1 0 0 0 1 1v1c-4-3-9-6-13-7s-6 0-9 2l-2 1h-1v-2c2-1 3-3 5-5z"></path><path d="M495 319c0-1 1-2 2-3v3c-1 3-1 6-3 8l2 2c-1 0-1 1-1 2-1 1-1 1-1 2l1 1v1h-1c-2 0-2 1-3 2l-1 1h-1c0 3 1 3 2 5-1-1-4-2-5-3 1-1 1-2 1-3 1-2 3-4 4-6 1-4 4-8 4-12z" class="R"></path><defs><linearGradient id="AT" x1="507.698" y1="305.361" x2="493.39" y2="304.9" xlink:href="#B"><stop offset="0" stop-color="#878585"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#AT)" d="M493 315c2-5 5-11 7-17l6-15v1c0 1 0 2 1 4 2 3 1 9 0 12 0 3-1 7 0 9v1l1 2h0c-1 1-1 1-1 2v1l-1-1v7l-1 1 2 2c-1 1 0 1-2 1l-2 2h-3l-1 2h-2l-2 2c0-1 0-2 1-2l-2-2c2-2 2-5 3-8v-3c-1 1-2 2-2 3-1 0-1 0-2-1l1-2-1-1z"></path><path d="M497 316v-1s0-1 1-1c1 1 1 3 1 4l-1 1c-1 2-1 6-1 9v1l-2 2c0-1 0-2 1-2l-2-2c2-2 2-5 3-8v-3z" class="Z"></path><path d="M498 326c1-2 1-4 2-6h1 0l-1 3h2l1 1c1 0 1-2 2-2l2 2c-1 1 0 1-2 1l-2 2h-3l-1 2h-2v-1l1-2z" class="g"></path><path d="M497 328l1-2c1 1 1 1 2 1l-1 2h-2v-1z" class="V"></path><path d="M543 339c1 1 1 2 2 2l4 4 1 3h-1c-1 0-2 0-3-1l-2 2v6 2l-1 1c-1 2-2 4-4 5l-1 1c-1 1-3 1-4 2-3 1-7 2-10 2-3-1-5-2-5-5l-1-1s0-1-1-1c0 1 0 2-1 3l-1-1v-1-6l1-1-1-1v-1h2v-3l2-2c2 0 5-2 8-3l4-3 3-1c1-1 2-1 3-1 2 0 4 0 6-1z"></path><path d="M519 348c2 0 5-2 8-3-1 1-2 3-4 3v1 1h2c0 1 0 1 1 2-1 2-2 4-3 5-1-1-1-3-1-5-3 1-4 2-6 3l-1-1v-1h2v-3l2-2z" class="Q"></path><path d="M543 339c1 1 1 2 2 2l4 4 1 3h-1c-1 0-2 0-3-1-1-2-3-3-5-3-4-1-9 2-12 4-1 1-2 2-3 4-1-1-1-1-1-2h-2v-1-1c2 0 3-2 4-3l4-3 3-1c1-1 2-1 3-1 2 0 4 0 6-1z" class="k"></path><path d="M516 284l3 7 3 9s1 1 1 2l7 18c1 4 3 7 3 11 1 2 1 4 1 7h-1c-1 2-1 3-2 4l-4 3c-3 1-6 3-8 3l-2 2v3h-2v1l1 1-1 1-1 2h-1c-1-5 0-11 0-16l-1-7h3c0-1 1-2 1-3 1-2 2-4 3-5s0-1 1-1l-1-1h-1v1l-1-1c0-7-1-14-2-21-1-4-2-8-1-12h1c0-2 0-2 1-4v-4z" class="R"></path><path d="M512 335h3c0-1 1-2 1-3 1-2 2-4 3-5s0-1 1-1l-1-1h-1v1l-1-1c0-7-1-14-2-21-1-4-2-8-1-12h1c0-2 0-2 1-4 0 4-1 8-1 11 0 4 2 8 3 12 1 2 0 5 0 7s1 5 2 6 1 1 1 3h-1c-1 1-1 2-2 4 0 1-1 3-2 4v1c0 1 0 1-1 2v2l-2 2-1-7z" class="Q"></path><path d="M519 291l3 9s1 1 1 2c-3 4 7 19 2 28 0 1 1 1 0 2 0 0-1 2-1 3-1 1-2 3-3 5s-3 3-4 3l-2 2h0l1 1v1 1c1-1 2 0 3 0l-2 2v3h-2c0-2-2-4-1-6h0v-1-1c1-3 4-5 4-8 0-1 0-1 1-2v-2c1-1 3-4 4-6 0-2 0-5-1-7 0-1 0-3-1-4v-3c0-2-1-3-1-5-1-2-1-5-2-7-1-3-1-8 1-10z" class="V"></path><path d="M523 302l7 18c1 4 3 7 3 11 1 2 1 4 1 7h-1c-1 2-1 3-2 4l-4 3c-3 1-6 3-8 3-1 0-2-1-3 0v-1-1l-1-1h0l2-2c1 0 3-1 4-3s2-4 3-5c0-1 1-3 1-3 1-1 0-1 0-2 5-9-5-24-2-28z" class="W"></path><path d="M808 169h55-4c1 1 1 1 2 1v1h2c4 0 9 0 13 1 3 0 6 0 9 2-1 2 0 3-2 4-5 3-9 4-12 9-11-2-22-3-32-3-15-1-28-1-42 2-10 2-19 6-28 9-1 1-2 2-4 3l-2 1c-2 1-3 2-4 3-1 0-2 1-2 1-3 0-3 1-5 2l-10 10v1l-4 7c-1 1-2 3-3 4l-2 6v2 1l-1 1v2c-1 2 0 4 0 6l-2-1c1-2 1-3 0-5 0-1 0-2 1-4 0-2 0-2-1-4h0c3-6 6-11 9-17l-2 2v1c-1 1-2 3-3 5-1 1-2 3-3 4-1-3-1-8-1-11 0-2-1-4-2-6v-2c-2-2-3-3-4-5-2-1-3-2-5-4 1-1 1-3 1-5v-2l-1 2c-1 2-2 4-4 5-1 0-2 0-2 1-3 0-4 0-7-1 0-1-1-1-2-2 0-1-1-4-1-5v-1c1-2 1-3 3-5h2c1-1 0-1 1-1h1l-1-1c-1 0-2 0-3 1h-1v-7c-1-1-2-2-2-4 2-2 6 0 9-2h58 30 8l-2-1h2v-1z" class="Q"></path><path d="M808 169h55-4c1 1 1 1 2 1v1h2c4 0 9 0 13 1l-76-1h8l-2-1h2v-1z" class="D"></path><defs><linearGradient id="AU" x1="801.821" y1="180.888" x2="808.992" y2="211.966" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#3e3c3d"></stop></linearGradient></defs><path fill="url(#AU)" d="M739 214l6-7c6-6 14-11 21-14 24-12 48-16 74-14l21 1c2 1 5 1 7 1 1 0 3-3 4-3 3-2 7-4 10-4 2 0 1 0 2 1 0 1 0 1-1 2-3 2-6 3-8 4s-4 4-5 4c0 1-2 0-2 0l-9-1c-13-1-25-2-38-2-13 1-27 3-40 8-4 2-8 3-11 5h-1c-1 1-2 2-4 3l-2 1c-2 1-3 2-4 3-1 0-2 1-2 1-3 0-3 1-5 2l-10 10v1l-4 7c-1 1-2 3-3 4l-2 6v2 1l-1 1v2c-1 2 0 4 0 6l-2-1c1-2 1-3 0-5 0-1 0-2 1-4 0-2 0-2-1-4h0c3-6 6-11 9-17z"></path><defs><linearGradient id="AV" x1="480.149" y1="541.191" x2="673.199" y2="398.603" xlink:href="#B"><stop offset="0" stop-color="#373535"></stop><stop offset="1" stop-color="#5f6061"></stop></linearGradient></defs><path fill="url(#AV)" d="M573 453c2-8 6-15 9-22l20-47 14-35c2-5 4-11 7-16h0c0 6-4 12-6 17 0 0-1 2-1 3 0 0 0 1 1 2 2 0 4-2 6-3 1-1 2-4 3-5 2-1 3-2 5-3 1-1 2-1 3-1l-1 1c0 1 1 1 1 2s-1 2-1 4l-2 3-2 6-1 2c0 1-1 2-1 3l-3 6v1l-1 1-5 13-1 2-1 2h0c0 1 0 1-1 2v1l-1 1-1 4-2 4-3 6c0 2-1 3-1 4l-1 2-1 3-3 6v1l-1 3c-1 1-1 2-2 3l-1 2v2l-1 2c-1 1 0-1-1 1 0 1 0 2-1 3v1l-1 2-4 9v1c-1 2-2 3-2 4h2l-10 24-2 6-6 14c-1 3-3 6-5 9l-3 7c-1 2-3 4-3 7-1 2-3 6-3 8l-2 3-1-1-5 11c-5 10-8 21-14 31-1 2-3 5-4 7l-7 11c-1 2-2 3-3 4h-1l1-3c0-1-1-6 0-6l3-3c2-2 4-3 5-5h-2c-2-1-3-2-5-2h-2l-1-1c3-7 5-13 8-19l14-33c4-8 6-17 11-25l20-47z"></path><path d="M588 456h2l-10 24-2 6-6 14c-1 3-3 6-5 9l-3 7c-1 2-3 4-3 7-1 2-3 6-3 8l-2 3-1-1 3-8c1-1 1-1 1-2 1-1 1-3 2-4 0 0 0-1 1-1l6-17 1-1 6-14c1-1 1-2 1-3l1-1c1-1 0 0 0-1l1-2c1-2 1-1 1-2l1-2c0-1 0-2 1-2 1-2 0 0 0-2 1-2 2-3 3-5v-1c1-1 1-2 1-3 1-2 0 0 1-1l1-2c0-1 1-2 1-3zm-15-3c0 2 0 3-1 4s-1 2-1 3c1-2 3-4 4-6 2-4 3-9 7-10h1c0 2-1 4-1 6-2 6-4 11-7 16-1 3-2 6-3 8l-1 1v1l-3 6c-2 2-3 3-4 5 0 2-2 5-4 7 0 0-2 1-3 2 0 0-2 0-2 1-1 0-1 2-2 3h0 0l20-47z" class="D"></path><path d="M421 527c0-1 0-3-1-4-1-2-1-2-1-4h1l1 2h1l4 3c6 4 9 8 10 15v4c-1 2-2 3-4 3 1 2 1 2 1 4 1 0 2 1 3 2 2 1 2 1 3 3h0l1 5v2l1 2c0 1 0 3 1 5h0c0 1-1 2-1 3 1 2 1 3 3 5 0 2 1 3 2 4v1c1 2 3 4 4 6l1 3c1 1 1 2 1 3h-1c1 3 2 6 4 9 3 5 6 10 8 15 1 1 1 3 2 4l11 31 6 20 7 25c0 1 0 4 1 6 1 1 1 2 2 3 3 1 8 2 10 4h1l2 1v-1c1 1 1 1 1 2 2-1 2 0 3-1h4v1l1 2h-2c-1 1-1 1-1 2h0l10-2h0c3-2 5-3 7-4 2 0 2 0 3-1l-1-1c1-1 1-2 2-3h1l-6 17c0 2-1 5-2 7l-7 22c-1 1-3 4-3 5-1 3-2 4-4 6h-1v2l-2 1c-2-1-2-4-3-6-3-5-5-11-7-17-1-3-2-5-3-8l-5-16c-1-3-2-6-4-9h1c-1-8-5-16-7-23l-7-23h0c-3-10-7-20-11-29-3-6-6-12-12-16l-3-2c-1 0-1 0-2-1l-1 1-2-1h0v1c0 1 0 2 1 3s1 2 2 4h0c-2-3-4-4-4-8-1-1-1-3-1-4-1-2-1-4-3-6v-3h-1l-1 2h-2v-1h-1l-1-1h-1v-3l-1-1v-2l-1 1h1l-1 1c-1-1-2-2-2-4 0-1-1-1-1-2-1 0-2 0-2 1l-1-1h-3 0-1c-1 0-2-1-2-1-1 1-1 0-1 1l-3-3v-1l2 1v-2c-1-1-1 0-1-1v-2l-1-1h0 0c0-2 0-1-1-2-1 0-1 0-2 1v2c-1-1-2-2-3-4h-2v-1c-1-4-5-3-7-5h2l1-2c2 1 5 1 7 1l2-2c1-1 1-1 2-1v-1h1c1 0 2-1 2-2v-3c2 0 3 1 4 1l1-1 1 1 6-3c1-1 1-2 2-4v-1c-1-2-1-5-2-7l-1-3 1-3-3-4c-1-1-2-1-4-2 1 0 1 0 2-1h1l-1-2c0-2 0-4-1-6 0-2-1-4-1-6l2 3z" class="P"></path><path d="M447 613h3 1 0c5 4 7 4 8 10l-6-6-4-3c-1 0-1 0-2-1z" class="L"></path><path d="M433 588l-2 1-1-1c1-1 1-1 2-1v-1c1-1 1-2 2-2h1l1 1h0c3 1 5 3 6 5 1 1 3 4 3 5h-2v-2c-1-2-2-2-4-4-1 0-3 0-4-1h-1-1z" class="E"></path><path d="M433 588h1 1c1 1 3 1 4 1 2 2 3 2 4 4v2c-2 1-4 3-6 5l-2 1v-6h-1l-1-7z" class="L"></path><path d="M437 592h2l2 2-1 1h0c-1-1-1-1-2-1-1-1-1-1-1-2z" class="E"></path><path d="M498 732l-1-3-1-2c0-1 0-3-1-4v-1c0-1 0-3-1-4s0-2-1-3c-2-3-3-6-3-9 2 2 4 5 7 6l1 1c1 1 1 2 3 2l2 2h0-3v1h0l-1-2h-1c-1 2 1 6 2 8-2 1-2 3-2 5s1 4 1 6c0-1-1-2-1-3z"></path><path d="M426 563c1-1 1-2 2-4 0 2 1 3 1 5v1 2h-1l-1 1h1 1l1 1-1 1c-1 0-1 0-2-1l-1 1 2 2h2c-1 1 0 1-1 2 1 1 2 1 4 2l1 1-1 1c-1 1-2 1-3 1l-2-2c-1 0-1 0-1 1v1l-1 1c0 1 0 2 1 3l-1 1h0c-1-1-1-1-1-2-1-2-3-4-3-7-1-1-2-2-2-3h0l-1-2-1-1h0v-3l1-1 1 1 6-3z" class="E"></path><path d="M418 566l1-1 1 1c1 1 2 1 2 2l-2 1v3l-1-2-1-1h0v-3z" class="L"></path><path d="M443 595h2c1 3 0 5-1 7s-2 1-3 2v1c1 1 1 2 2 3l1-1c1 0 4 1 5 0h3 0c-1 2-3 1-5 2h-2-1c0 1 0 2 1 2l1 1 1 1-1 1c2 1 3 2 5 3 4 3 7 7 9 12h0c-2-3-5-8-8-10l-1-1c-1-1-3-2-5-3-1-1-1-2-3-3l-1 1c1 1 2 1 3 2v1l-1 1-2-1h0v1c0 1 0 2 1 3s1 2 2 4h0c-2-3-4-4-4-8-1-1-1-3-1-4-1-2-1-4-3-6v-3h-1l-1 2h-2v-1h-1l-1-1c1-1 3-1 4-2l2-1c2-2 4-4 6-5z" class="B"></path><path d="M443 595h2c1 3 0 5-1 7s-2 1-3 2v1c-1-1-1-1-1-2h3c0-1-1-1-1-2h0c-1-1-1-1-1-2l-1 1h-3c2-2 4-4 6-5z" class="O"></path><path d="M414 565c2 0 3 1 4 1v3h0l1 1c-1 0-2-1-3-1-1 3 1 3-2 5l-1 2c1 0 2 1 2 3h0c-1 4 2 7 1 11 0 1 0 1 2 2v-1c1 0 1-1 2-1l1 1-2 2h-1c-1 0-2-1-2-1-1 1-1 0-1 1l-3-3v-1l2 1v-2c-1-1-1 0-1-1v-2l-1-1h0 0c0-2 0-1-1-2-1 0-1 0-2 1v2c-1-1-2-2-3-4h-2v-1c-1-4-5-3-7-5h2l1-2c2 1 5 1 7 1l2-2c1-1 1-1 2-1v-1h1c1 0 2-1 2-2v-3z" class="E"></path><path d="M445 616v-1c-1-1-2-1-3-2l1-1c2 1 2 2 3 3 2 1 4 2 5 3l1 1c3 2 6 7 8 10h0c-2-5-5-9-9-12-2-1-3-2-5-3l1-1h0c1 1 1 1 2 1l4 3 6 6c11 20 18 44 24 66l3 12c0 3 2 6 3 9 4 11 6 21 10 32l5 13c0 3 2 6 3 9h1c0-2 0-2 1-3l1 2v2l-2 1c-2-1-2-4-3-6-3-5-5-11-7-17-1-3-2-5-3-8l-5-16c-1-3-2-6-4-9h1c-1-8-5-16-7-23l-7-23h0c-3-10-7-20-11-29-3-6-6-12-12-16l-3-2c-1 0-1 0-2-1z" class="W"></path><path d="M421 527c0-1 0-3-1-4-1-2-1-2-1-4h1l1 2h1l4 3c6 4 9 8 10 15v4c-1 2-2 3-4 3 1 2 1 2 1 4 1 0 2 1 3 2 2 1 2 1 3 3h0l1 5v2l1 2c0 1 0 3 1 5h0c0 1-1 2-1 3 1 2 1 3 3 5 0 2 1 3 2 4v1c1 2 3 4 4 6l1 3c1 1 1 2 1 3h-1s-1-3-2-5c-2-4-4-6-7-8-9-5-7-14-10-23v-1l-1-1c-1-5-2-8-5-11l-3-4c-1-1-2-1-4-2 1 0 1 0 2-1h1l-1-2c0-2 0-4-1-6 0-2-1-4-1-6l2 3z" class="D"></path><path d="M429 542c1 1 2 3 3 4 1 2 1 2 1 4l-2-1c0-1-1-1-1-2s0-2-1-3l-1-1 1-1z" class="e"></path><path d="M431 549l2 1c1 0 2 1 3 2 2 1 2 1 3 3h0c-2 0-4 0-6-1 0-2-1-3-2-4v-1z" class="W"></path><path d="M446 582l-3-1c-1-1 0-1-1-2v-1c0-2-1-3-1-4-1-2-2-4-4-6l-1-1-1-1v-3c-1-2-1-3-1-5l1 1h1l4 1v2l1 2c0 1 0 3 1 5h0c0 1-1 2-1 3 1 2 1 3 3 5 0 2 1 3 2 4v1z" class="B"></path><path d="M441 572l-1-1c-1-2-4-5-4-7 1-1 3-2 4-2l1 2c0 1 0 3 1 5h0c0 1-1 2-1 3z" class="Y"></path><path d="M421 527c0-1 0-3-1-4-1-2-1-2-1-4h1l1 2h1l4 3c6 4 9 8 10 15v4c-1 2-2 3-4 3-1-1-2-3-3-4l-2-1c0-5-4-10-6-14z"></path><path d="M532 706h1l-6 17c0 2-1 5-2 7l-7 22c-1 1-3 4-3 5-1 3-2 4-4 6h-1l-1-2c-1 1-1 1-1 3h-1c-1-3-3-6-3-9l-5-13c1-2 0-3 0-4l-2-5 1-1c0 1 1 2 1 3 0-2-1-4-1-6s0-4 2-5c-1-2-3-6-2-8h1l1 2h0v-1h3 0 3 3 0 2 0l10-2h0c3-2 5-3 7-4 2 0 2 0 3-1l-1-1c1-1 1-2 2-3z" class="b"></path><path d="M508 738h0c1 2 1 4 1 7h0l1-1c2 2 2 3 2 5l-2 3h0c-2-4-3-8-4-12h0l2 2v-3-1z" class="O"></path><path d="M500 718h2c0 2 0 4 1 5l1-1v3l1-2 3 15v1 3l-2-2h0c-3-7-4-15-6-22h0z" class="P"></path><defs><linearGradient id="AW" x1="508.945" y1="715.129" x2="512.394" y2="737.629" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#AW)" d="M521 715c0 2 0 5-1 7l-2 12c0 2 0 4-1 5h-1l1-1h-1l-1-1c1-5 1-9 1-13-3-3-7-2-11-2v1l-1 2v-3l-1 1c-1-1-1-3-1-5h-2v-1h3 0 3 3 0 2 0l10-2h0z"></path><path d="M500 724l4 18 5 13c1 1 1 3 2 3h1v1l1-1 1-1h1 0c-1 3-2 4-4 6h-1l-1-2c-1 1-1 1-1 3h-1c-1-3-3-6-3-9l-5-13c1-2 0-3 0-4l-2-5 1-1c0 1 1 2 1 3 0-2-1-4-1-6s0-4 2-5z" class="O"></path><path d="M500 724l4 18v1c-1-1-1-2-1-2l-1-1v3 1c1 1 0 2 1 4v1c1 2 1 4 1 6l-5-13c1-2 0-3 0-4l-2-5 1-1c0 1 1 2 1 3 0-2-1-4-1-6s0-4 2-5z" class="L"></path><path d="M505 723v-1c4 0 8-1 11 2 0 4 0 8-1 13l1 1h1l-1 1h1c-1 3-2 5-3 7l-2 3c0-2 0-3-2-5l-1 1h0c0-3 0-5-1-7h0l-3-15z" class="T"></path><path d="M514 746c-1-3 0-6 1-9l1 1h1l-1 1h1c-1 3-2 5-3 7z" class="G"></path><path d="M532 706h1l-6 17c0 2-1 5-2 7l-7 22c-1 1-3 4-3 5h0-1l-1 1-1 1v-1h-1c-1 0-1-2-2-3 2-1 3-2 5-3l1-1c4-11 7-24 8-36h-2c3-2 5-3 7-4 2 0 2 0 3-1l-1-1c1-1 1-2 2-3z" class="L"></path><path d="M509 755c2-1 3-2 5-3 0 2-1 4-2 6h-1c-1 0-1-2-2-3z" class="U"></path><path d="M521 715c3-2 5-3 7-4 1 1 1 2 1 3h0c-2 2-3 4-3 6h-2v-1c1-1 1-3 1-4v-1l-1 1h-1-2z"></path><path d="M532 706h1l-6 17c0-1 0-1-1-2l-1 1h-1v-1s1 0 1-1h1c0-2 1-4 3-6h0c0-1 0-2-1-3 2 0 2 0 3-1l-1-1c1-1 1-2 2-3z" class="O"></path><path d="M360 233h1v1h0c1 0 1 0 1 1 2-3 1-6 2-9 0-2 0-3 1-4v2c-1 1 0 2-1 3v5c-1 1-1 8 0 10l1 1c2 13 8 25 14 38l18 42 68 157c1 4 11 24 11 26 0 4 2 9 4 12v2h1v1h-1c-2-1-5 1-7 2l-4 2c-1-3-2-7-4-10l-1 1-14-34-33-77-48-110-12-28c-3-7-5-14-7-21-1-5-2-10-2-15 1-2 1-3 1-5 2 1 2 1 2 3l2-3h1v1c1 1 1 1 3 2h1c1 1 2 1 2 2v2z" class="f"></path><path d="M351 229l2-3h1v1c1 1 1 1 3 2h1c1 1 2 1 2 2v2c-1 2-4 4-6 4h-4c0 3 1 6 0 9-1-5-2-10-2-15 1-2 1-3 1-5 2 1 2 1 2 3z" class="R"></path><path d="M351 229l2-3h1v1c1 1 1 1 3 2h1c0 1 0 1-1 2 0 0-1 1-2 1-2-1-3-2-4-3z" class="C"></path><path d="M656 152h157 78 32c1 0 2 0 3 1 0 1 1 2 0 3l-1 2-2 3v1c-1 1-2 2-2 3-1 1-4 4-6 5l-1 1-1 1h-1c-2 1-3 1-5 2s-4 0-6 0c-5 0-10 1-14 0h-2 0c-3-2-6-2-9-2-4-1-9-1-13-1h-2v-1c-1 0-1 0-2-1h4-55v1h-2l2 1h-8-30-58-51c-4 0-9-1-13 0h-6v-1h-14-1v-1h-6c2-1 4-1 6-1h1 2c2 1 8 0 10-2h1c1 0 3-1 4-1h0c1 0 2-1 3 0 1 0 3 0 3 1 1 1 1 1 2 1h1v1h6v-1-2l1-3-1-1c-2-1-3-3-5-4h-1c0-1 1-1 1-2h0v-2h-2l3-1z" class="k"></path><path d="M770 169h38v1h-2l2 1h-8-30v-2z"></path><defs><linearGradient id="AX" x1="903.229" y1="176.64" x2="883.887" y2="151.729" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#363433"></stop></linearGradient></defs><path fill="url(#AX)" d="M926 153c0 1 1 2 0 3l-1 2-2 3v1c-1 1-2 2-2 3-1 1-4 4-6 5l-1 1-1 1h-1c-2 1-3 1-5 2s-4 0-6 0c-5 0-10 1-14 0h-2 0c-3-2-6-2-9-2-4-1-9-1-13-1h-2v-1c-1 0-1 0-2-1h4 9 18c4 0 9 1 12-1 8-3 15-11 23-15h1z"></path><path d="M627 169h-6c2-1 4-1 6-1h1 2c2 1 8 0 10-2h1c1 0 3-1 4-1h0c1 0 2-1 3 0 1 0 3 0 3 1 1 1 1 1 2 1h1v1h6 5c4 0 9 1 14 0 3 0 21 1 22 0 3 0 10-1 12 0h17l1 1h39v2h-58-51c-4 0-9-1-13 0h-6v-1h-14-1v-1z" class="C"></path><path d="M627 169h-6c2-1 4-1 6-1h1 2c2 1 8 0 10-2h1c1 0 3-1 4-1h0c1 0 2-1 3 0 1 0 3 0 3 1 1 1 1 1 2 1h1v1h6 5c4 0 9 1 14 0 3 0 21 1 22 0 3 0 10-1 12 0h17l1 1h-12-25-44-23z" class="Q"></path><defs><linearGradient id="AY" x1="597.033" y1="434.749" x2="645.476" y2="447.793" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#AY)" d="M722 251c0 2 1 3 1 4-1 2-3 5-4 7 0 1-2 4-2 5l-1 4v1h0c-1 2-2 3-2 5l-1 2 1 1c0 1 0 1-1 2 0 1 0 1-1 2 0 1 0 2-1 3l-2 6-3 6-1 2c-1 1-2 2-3 4-1 1-1 2-2 4v2h-1v-2l-1 1h0c-1 3-4 8-7 10l-6 12 1 4c-2 4-4 8-4 13h0v1l-1 1c0 1 0 2-1 3 0 1-1 2-1 3s0 2-1 3v1c-1 3-3 6-4 9l-2 5v4c0 2-2 6-2 8h1c0 1 1 1 2 2l-1 1-1-1h-2c-5 7-9 16-12 24-2 5-5 10-7 15l-1 2-2 5v1c-1 1-1 1-1 2-1 1-1 3-2 4v1l-1 3-10 23-1 1-2 6-1 1-2 6-3 6-1 3h0c-1 3-3 6-4 9-1 2-1 4-2 6h0c-1 2-2 4-3 5h0-1l-1 4-1-2c1-1 1-2 1-3l-2-1c-1 2-1 5-2 7h-1 0c-1 0-2 1-2 2-3 1-6 2-9 2-1 1-2 1-2 2h-1l-1 2v-3c-4 1-6 3-9 4h-1l-3 2c-2 1-3 1-5 3l-5 3-4 4c-1-1 0-1 0-2v-1c-1 1-2 2-2 3h0c-1 0-1 1-2 1h0c-2 0-4 1-5 0l1-1c0-1 0-2 1-3h0v-4c0-2 2-6 3-8 0-3 2-5 3-7l3-7c2-3 4-6 5-9l6-14 2-6 10-24 5-12 2-3 7-17c2-1 2-1 3-2l2 1v1c2 1 4 1 6 2h2c-2 4-4 8-5 12-1 3-3 5-3 8 2-2 2-5 4-7h0c1 0 1-1 1-1 1-2 1-3 2-5l1 1 2-4v-2l3-7 1-2 1-1c0-2 1-4 2-6h1v-1c1 0 1-1 1-1 0-1 1-1 1-2l1-2v-1l1-2c1-2 3-5 3-7h0l1-2 1-2c0-1 1-2 1-3l1-2 1-2 1-2 3-8 1-2h1c1-1 1-3 2-5 1-1 2-3 2-5 1-2 2-7 5-8 3 0 7 3 10 6 1 1 1 1 1 3l1 1c1 1 1 1 2 1 0 1-1 1-1 2v1c0 1-1 1-1 2v1l-1 1v1l-1 1v1 1l18-41c14-28 24-58 40-85z"></path><path d="M596 515l1 3-1 3c-1 1-2 1-2 2h-1c0-1 0-2 1-4l1-2 1-2z" class="F"></path><path d="M570 530c0-1 1-2 1-3h0 2c0-2 4-4 5-6 1 2 2 3 3 4h1l1 1h-1-2l-2 1c-1 0-2 1-2 1-1 1-2 1-3 2-1 0-1 1-2 1v-1h-1z" class="K"></path><path d="M581 509h1c1-1 2-1 2-1h1c-1 1-1 2-2 3-3 4-5 7-8 10-1 1-2 3-4 3v-1-2c0-1 0-1 1-1l1-3c1 0 2-1 2-2l6-6z" class="E"></path><path d="M581 509h1c1-1 2-1 2-1h1c-1 1-1 2-2 3-3 0-3 2-5 3l-1 1c-1 2-3 4-5 5l1-3c1 0 2-1 2-2l6-6z" class="N"></path><path d="M592 481v-1c1-2 2-3 4-4v1h1l1 1c-1 1-1 3-2 4 0 1-1 1-1 2v1l-2 4-4 11c-1 1-1 1-1 2s-1 4-3 6h-1s-1 0-2 1h-1c3-4 6-9 8-14 2-2 3-7 4-10l-2-2c0-1 1-1 1-2z" class="C"></path><path d="M592 481v-1c1-2 2-3 4-4v1l-3 8-2-2c0-1 1-1 1-2z" class="S"></path><path d="M609 446c2-2 2-5 4-7h0c1 0 1-1 1-1 1-2 1-3 2-5l1 1-2 4-1 3-1 1c0 1-1 2-1 3l-1 2v1c-1 1-1 2-2 3v2l-2 4-6 13v2l-3 5v1l-1-1h-1v-1-1c0-2 1-2 2-3 0-1 1-3 1-4 2-4 4-8 5-13l5-9z" class="F"></path><path d="M591 483l2 2c-1 3-2 8-4 10-2 5-5 10-8 14l-6 6c0 1-1 2-2 2v-1c1-2 3-3 4-5-1 1-3 3-5 3h0c0-3 2-5 4-7h1v-1-1c1 0 2-1 3-3 0-1 1-1 2-2h0l2-2c0-3 2-5 3-7s2-3 3-4l1-4z" class="g"></path><path d="M591 483l2 2c-1 3-2 8-4 10l-3 3v-1l4-10 1-4z" class="c"></path><path d="M577 506v-1c1 0 2-1 3-3 0-1 1-1 2-2h0c0 2 0 3-1 4l-1 1c-1 0-2 1-2 3h-1v3c-1 1-3 3-5 3h0c0-3 2-5 4-7h1v-1z" class="a"></path><path d="M564 516l3-7 1 3h0l2 1c0 1 1 1 2 1h0c2 0 4-2 5-3-1 2-3 3-4 5v1l-1 3c-1 0-1 0-1 1v1c-1 1-1 1-1 2-1 1 0 1 0 2 0 2-1 1 0 4h1v1c1 0 1-1 2-1 1-1 2-1 3-2 0 0 1-1 2-1l2-1h2l-3 2c-2 1-3 1-5 3l-5 3-4 4c-1-1 0-1 0-2v-1c-1 1-2 2-2 3h0c-1 0-1 1-2 1h0c-2 0-4 1-5 0l1-1c0-1 0-2 1-3h0v-4c0-2 2-6 3-8 0-3 2-5 3-7z" class="e"></path><path d="M561 523l2 1c0-1 0-1 1-2 1 0 1 0 2 1h-2v1l2 2h-1c-1 0-1 0-2 1h-2v-1l1-1s-1-1-1-2z" class="g"></path><path d="M564 516l3-7 1 3h0l2 1c0 1 1 1 2 1h0l-2 1h-2l1 1c-1 1-1 1-2 1h-2l-1-1z" class="V"></path><path d="M564 516l1 1h2v1c1 0 1 1 1 2v1l1 1-2 1h-1c-1-1-1-1-2-1-1 1-1 1-1 2l-2-1c0-3 2-5 3-7z" class="X"></path><path d="M584 481h0c0-2 1-2 1-3 1-1 0-2 1-4 1 1 1 1 2 1v-2c2 0 3 1 4 1v1c0 1-1 3-1 3l1 3c0 1-1 1-1 2l-1 4c-1 1-2 2-3 4s-3 4-3 7l-2 2h0c-1 1-2 1-2 2-1 2-2 3-3 3v1 1h-1c-2 2-4 4-4 7-1 0-2 0-2-1l-2-1h0l-1-3c2-3 4-6 5-9l6-14 2-6c2 0 2 0 4 1z" class="R"></path><path d="M580 480c2 0 2 0 4 1-2 1-4 2-4 4h-1l-1 1 2-6zm-4 27h0v-2h0c0-1 1-2 1-2 1-1 2-3 3-4l-1-1v-2h2c1 3 0 5-2 7-1 1-2 2-2 3v1h-1z" class="j"></path><path d="M591 478l1 3c0 1-1 1-1 2l-1 4c-1 1-2 2-3 4s-3 4-3 7l-2 2h0c-1 1-2 1-2 2-1 2-2 3-3 3v1c0-1 1-2 2-3 2-2 3-4 2-7 1-3 3-5 5-8 1-1 1-3 2-4 1-2 1-3 2-4l1-2z" class="Z"></path><path d="M591 478l1 3c0 1-1 1-1 2l-1 4c-1 1-2 2-3 4 1-4 3-7 3-11l1-2z" class="V"></path><path d="M607 422l2 1v1c2 1 4 1 6 2h2c-2 4-4 8-5 12-1 3-3 5-3 8l-5 9c-1 5-3 9-5 13 0 1-1 3-1 4-1 1-2 1-2 3v1c-2 1-3 2-4 4v1l-1-3s1-2 1-3v-1c-1 0-2-1-4-1v2c-1 0-1 0-2-1-1 2 0 3-1 4 0 1-1 1-1 3h0c-2-1-2-1-4-1l10-24 5-12 2-3 7-17c2-1 2-1 3-2z" class="Z"></path><path d="M603 434v1 5c-1-1-1-2-1-2-1-2 0-2 1-4z" class="j"></path><path d="M599 442l-1-1h1c1-1 1-1 3-1l1 2v1h-1-1c0 1 0 1-1 2h0c-1-1-1-2-1-3z" class="R"></path><path d="M595 444l2-3c1 1 1 1 2 1 0 1 0 2 1 3h0v1h-1c-2 0-2-1-4-2zm-1 6h2l1 2h-1v1l2 1h2 1l-1 1c-3 0-4 1-6-1v-4z" class="V"></path><path d="M609 424c2 1 4 1 6 2h0v1h-2c-1 0-1 1-2 1h-1l-1 1h3 1 0v1h-1-1v4c-2 0-3 0-4-1l-2-1c-1 0-1 0-2-1 0-1 0-1 1-2 0 0 0-1 1-1h1c-1-1-1-1 0-2 0 0 1 0 1 1h1l1-1c-1 0-2-1-2-2h2z" class="R"></path><path d="M592 475c1 0 2-1 2-2s1-1 1-2l3-6v-3c1 0 1 0 2-1v-2h-1v-1h1l1 1 2-2s1-1 1-2c-1 5-3 9-5 13 0 1-1 3-1 4-1 1-2 1-2 3v1c-2 1-3 2-4 4v1l-1-3s1-2 1-3z" class="g"></path><path d="M722 251c0 2 1 3 1 4-1 2-3 5-4 7 0 1-2 4-2 5l-1 4v1h0c-1 2-2 3-2 5l-1 2 1 1c0 1 0 1-1 2 0 1 0 1-1 2 0 1 0 2-1 3l-2 6-3 6-1 2c-1 1-2 2-3 4-1 1-1 2-2 4v2h-1v-2l-1 1h0c-1 3-4 8-7 10l-6 12 1 4c-2 4-4 8-4 13h0v1l-1 1c0 1 0 2-1 3 0 1-1 2-1 3s0 2-1 3v1c-1 3-3 6-4 9l-2 5v4c0 2-2 6-2 8h1c0 1 1 1 2 2l-1 1-1-1h-2c-5 7-9 16-12 24-2 5-5 10-7 15l-1 2-2 5v1c-1 1-1 1-1 2-1 1-1 3-2 4v1l-1 3-10 23-1 1-2 6-1 1-2 6-3 6-1 3h0c-1 3-3 6-4 9-1 2-1 4-2 6h0c-1 2-2 4-3 5h0-1l-1 4-1-2c1-1 1-2 1-3l-2-1c-1 2-1 5-2 7h-1 0c-1 0-2 1-2 2-3 1-6 2-9 2l1-3-1-3-1 2v-1c0-1 1-2 2-3 0-2 2-3 3-3l2-1c1-1 0-1 1-1h1c1 0 2-1 2-1h3l1-3 3-9 1-2h0v-1-1l1-1v-1l1 1c0-1 17-42 19-46l29-67 18-41c14-28 24-58 40-85z" class="B"></path><defs><linearGradient id="AZ" x1="626.184" y1="482.477" x2="619.868" y2="482.113" xlink:href="#B"><stop offset="0" stop-color="#525354"></stop><stop offset="1" stop-color="#706e6e"></stop></linearGradient></defs><path fill="url(#AZ)" d="M628 464v1h1v4 1c-1 1-1 0-1 1-1 1-1 4-1 5l3-6h1l1-1v-1l1 1-1 1-2 6-1 1-2 6-3 6-1 3h0-1c-1 1-1 2-2 4v-1c-1-1 0-1 0-3h0l-1 1v-1-1c-3 4-4 11-7 16h-1l17-43z"></path><path d="M691 320c2-5 5-10 7-15l6-15c2-3 4-6 5-9 2-4 5-10 8-14l-1 4v1h0c-1 2-2 3-2 5l-1 2 1 1c0 1 0 1-1 2 0 1 0 1-1 2 0 1 0 2-1 3l-2 6-3 6-1 2c-1 1-2 2-3 4-1 1-1 2-2 4v2h-1v-2l-1 1h0c-1 3-4 8-7 10zm-96 197v-1c0-1 1-2 2-3 0-2 2-3 3-3l2-1c1-1 0-1 1-1h1c1 0 2-1 2-1h3l1-3 3-9 1-2h0v-1-1l1-1v-1l1 1-6 17 1 1v-1h1c3-5 4-12 7-16v1 1l1-1h0c0 2-1 2 0 3v1c1-2 1-3 2-4h1c-1 3-3 6-4 9-1 2-1 4-2 6h0c-1 2-2 4-3 5h0-1l-1 4-1-2c1-1 1-2 1-3l-2-1c-1 2-1 5-2 7h-1 0c-1 0-2 1-2 2-3 1-6 2-9 2l1-3-1-3-1 2z" class="c"></path><path d="M596 515c0-1 0-1 1-1 1-2 2-3 4-3 3-1 5-2 8 0l1-1h0c-1 2-1 5-2 7h-1 0c-1 0-2 1-2 2-3 1-6 2-9 2l1-3-1-3z" class="I"></path><path d="M596 515c0-1 0-1 1-1 1-2 2-3 4-3 3-1 5-2 8 0l1-1h0c-1 2-1 5-2 7h-1c0-1-1-4-1-5s1-1 0-1h-1c-2 2-4 2-6 4-1 1-1 2-2 3l-1-3z" class="B"></path><defs><linearGradient id="Aa" x1="661.929" y1="408.572" x2="649.515" y2="402.141" xlink:href="#B"><stop offset="0" stop-color="#3c3d3d"></stop><stop offset="1" stop-color="#7d7a7b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M685 332l1 4c-2 4-4 8-4 13h0v1l-1 1c0 1 0 2-1 3 0 1-1 2-1 3s0 2-1 3v1c-1 3-3 6-4 9l-2 5v4c0 2-2 6-2 8h1c0 1 1 1 2 2l-1 1-1-1h-2c-5 7-9 16-12 24-2 5-5 10-7 15l-1 2-2 5v1c-1 1-1 1-1 2-1 1-1 3-2 4v1l-1 3-10 23-1-1v1l-1 1h-1l-3 6c0-1 0-4 1-5 0-1 0 0 1-1v-1-4h-1v-1l13-30 16-39c1-2 3-5 4-7l7-16 17-40z"></path><path d="M657 395l-1 4v2c0 3-2 6-3 9l-1 2c-1 3-3 5-4 8-2 4-4 9-6 13l-1 1 16-39z" class="h"></path><defs><linearGradient id="Ab" x1="624.737" y1="292.174" x2="729.246" y2="202.634" xlink:href="#B"><stop offset="0" stop-color="#8e8c8c"></stop><stop offset="1" stop-color="#e8e7e6"></stop></linearGradient></defs><path fill="url(#Ab)" d="M670 189c-1 0-3-2-4-2l-2-2-7-4c-1 0-2 0-2-1-3-1-5-3-7-5-2-1-4-4-6-3-7 0-14 1-20 0h0 15c2-1 0-1 2-1h3 0 6c4-1 9 0 13 0h51c-3 2-7 0-9 2 0 2 1 3 2 4v7h1c1-1 2-1 3-1l1 1h-1c-1 0 0 0-1 1h-2c-2 2-2 3-3 5v1c0 1 1 4 1 5 1 1 2 1 2 2 3 1 4 1 7 1 0-1 1-1 2-1 2-1 3-3 4-5l1-2v2c0 2 0 4-1 5 2 2 3 3 5 4 1 2 2 3 4 5v2c1 2 2 4 2 6 0 3 0 8 1 11 1-1 2-3 3-4 1-2 2-4 3-5v-1l2-2c-3 6-6 11-9 17h0c-3 6-5 11-7 17 0 1-1 3-1 3-16 27-26 57-40 85l-18 41v-1-1l1-1v-1l1-1v-1c0-1 1-1 1-2v-1c0-1 1-1 1-2-1 0-1 0-2-1l-1-1c0-2 0-2-1-3-3-3-7-6-10-6-3 1-4 6-5 8 0 2-1 4-2 5-1 2-1 4-2 5h-1l-1 2-3 8-1 2-1 2-1 2c0 1-1 2-1 3l-1 2-1 2h0c0 2-2 5-3 7l-1 2v1l-1 2c0 1-1 1-1 2 0 0 0 1-1 1v1h-1c-1 2-2 4-2 6l-1 1-1 2-3 7v2l-2 4-1-1c-1 2-1 3-2 5 0 0 0 1-1 1h0c-2 2-2 5-4 7 0-3 2-5 3-8 1-4 3-8 5-12h-2c-2-1-4-1-6-2v-1l-2-1c-1 1-1 1-3 2l-7 17-2 3-5 12h-2c0-1 1-2 2-4v-1l4-9 1-2v-1c1-1 1-2 1-3 1-2 0 0 1-1l1-2v-2l1-2c1-1 1-2 2-3l1-3v-1l3-6 1-3 1-2c0-1 1-2 1-4l3-6 2-4 1-4 1-1v-1c1-1 1-1 1-2h0l1-2 1-2 5-13 1-1v-1l3-6c0-1 1-2 1-3l1-2 2-6 2-3c0-2 1-3 1-4l2-4v-1l5-11v-1l1-1c1-2 2-5 3-7s0 0 0-2l1-2c1-1 1 0 1-1l1-2c1-3 2-6 3-8s0 0 0-1l1-2c1-2 1-3 2-4v-1l1-1c1-2 1-4 2-6v3h1l8-19h0l8-20 3-16c1 1 3 1 4 2h0l1 3 1 2-1 1 1 1c0 2-1 0 0 2 0 1-1 2-1 3v3 3c0 1 1 3 0 4s-1 2-1 4c0 1-1 2-2 3v1c0 2-1 3-1 5 0 1 0 2-1 4-2 5-4 10-6 16 0 0-1 2 0 2h0l4-10h1l10-24 3-9c4-12 4-24 1-36-2-5-4-11-7-16-3-4-8-11-13-14z"></path><path d="M703 199c-2-2-2-5-1-8 0-3 2-5 4-6-2 2-2 3-3 5v1c0 1 1 4 1 5l-1 3z" class="B"></path><path d="M719 193c0 2-1 5-3 7-1 2-3 3-6 3-2 0-5-1-6-3l-1-1 1-3c1 1 2 1 2 2 3 1 4 1 7 1 0-1 1-1 2-1 2-1 3-3 4-5z" class="M"></path><path d="M686 264c0 3 0 4-1 6l-6 12c0 2-2 4-1 6l2-4v-1l1-3c1-1 1-2 1-2l1-2 1-1 1-4 1 1-21 48-1-2c1-2 2-5 4-8l8-22 10-24z"></path><path d="M628 400l2-2c1-1 0-1 1-2 2-3 3-7 4-10l8-18 21-50 1 2c-2 7-5 13-8 19l-14 33-6 14c-1 2-1 4-2 5s-1 1-1 2l-2 5c0 1-1 2-1 3l-1 2c-1 1-1 2-2 4 0 1 0 1-1 2v2c-1 0-1 1-1 1-1 2-2 4-2 6l-1 1-1 2-3 7v2l-2 4-1-1c-1 2-1 3-2 5 0 0 0 1-1 1h0c-2 2-2 5-4 7 0-3 2-5 3-8 1-4 3-8 5-12l11-25v-1z" class="T"></path><path d="M670 189c-1 0-3-2-4-2l-2-2-7-4c-1 0-2 0-2-1-3-1-5-3-7-5-2-1-4-4-6-3-7 0-14 1-20 0h0 15c2-1 0-1 2-1h3 0 6c4-1 9 0 13 0h0c-2 0-3 1-5 1 2 1 2 1 4 1l-2 1 1 1v3c3 2 6 3 9 5 13 9 22 23 25 39 3 14 1 27-4 41-1 3-2 6-3 8v1l-1-1-1 4-1 1-1 2s0 1-1 2l-1 3v1l-2 4c-1-2 1-4 1-6l6-12c1-2 1-3 1-6l3-9c4-12 4-24 1-36-2-5-4-11-7-16-3-4-8-11-13-14z" class="H"></path><path d="M648 171c4-1 9 0 13 0h0c-2 0-3 1-5 1 2 1 2 1 4 1l-2 1c0 1 0 2-1 3 0 0 0 1 0 0-1 0-2-1-2-1-2-2-4-3-7-5h0z" class="f"></path><defs><linearGradient id="Ac" x1="657.88" y1="360.02" x2="672.653" y2="336.783" xlink:href="#B"><stop offset="0" stop-color="#717070"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#Ac)" d="M644 373l1-2c0-1 1-2 1-4l2-3 1-2c0-1 1-3 2-5h0c1-2 0 0 0-1l1-2 3-6v-1l1-3 3-6v-1c0-1 1-1 2-2h4c3 0 6 1 9 3l5 2s0-1 1-1c0-1 0-2 1-3h1l-18 41v-1-1l1-1v-1l1-1v-1c0-1 1-1 1-2v-1c0-1 1-1 1-2-1 0-1 0-2-1l-1-1c0-2 0-2-1-3-3-3-7-6-10-6-3 1-4 6-5 8 0 2-1 4-2 5-1 2-1 4-2 5h-1z"></path><path d="M677 239c1 1 3 1 4 2h0l1 3 1 2-1 1 1 1c0 2-1 0 0 2 0 1-1 2-1 3v3 3c0 1 1 3 0 4s-1 2-1 4c0 1-1 2-2 3v1c0 2-1 3-1 5 0 1 0 2-1 4-2 5-4 10-6 16 0 0-1 2 0 2h0l4-10h1l-8 22c-2 3-3 6-4 8l-21 50-8 18c-1 3-2 7-4 10-1 1 0 1-1 2l-2 2v1l-11 25h-2c-2-1-4-1-6-2v-1l-2-1c-1 1-1 1-3 2l-7 17-2 3-5 12h-2c0-1 1-2 2-4v-1l4-9 1-2v-1c1-1 1-2 1-3 1-2 0 0 1-1l1-2v-2l1-2c1-1 1-2 2-3l1-3v-1l3-6 1-3 1-2c0-1 1-2 1-4l3-6 2-4 1-4 1-1v-1c1-1 1-1 1-2h0l1-2 1-2 5-13 1-1v-1l3-6c0-1 1-2 1-3l1-2 2-6 2-3c0-2 1-3 1-4l2-4v-1l5-11v-1l1-1c1-2 2-5 3-7s0 0 0-2l1-2c1-1 1 0 1-1l1-2c1-3 2-6 3-8s0 0 0-1l1-2c1-2 1-3 2-4v-1l1-1c1-2 1-4 2-6v3h1l8-19h0l8-20 3-16z" class="R"></path><path d="M636 367h1l1 1h-1-2v-1h1zm-4 11h-2c-1-1-2-2-2-3l1-1c1 1 1 2 2 2l1 2z" class="U"></path><path d="M629 374c1-1 2-1 4-1 1 1 2 1 3 3-1 1-1 1-2 1l-3-1c-1 0-1-1-2-2z" class="b"></path><path d="M634 352h1c1 1 1 1 2 1v1c-1 0-2 0-3 2v1l-1 1v1c0 1-1 2-1 3l-1-1c0 2-1 3-1 5l1 1-2 1h0v2h-1c-1 0-1-1-1 0l7-18z" class="Z"></path><path d="M670 291c0 3 1 4 0 6s-2 3-2 5v2c-1 1-1 2-1 3l-5 10v-2-4c1-1 1-1 1-2 3-6 4-12 7-18zm5-28v-1c0-1 1-2 2-3v-2h0v-2c0-1 0-2 1-3h1l-1 4v3 1h-1c0 1-1 1 0 3l2-3c0 2 1 3 0 5h-1l1 2c0 1 0 1-1 2v2c0 1-1 2-1 3l-1-1c0-1 0-2 1-3h0l-1-3-1 1v2c-1-2 0-5 0-7z" class="V"></path><path d="M636 376l1 1c-1 2-3 5-2 7 0 2 0 2-2 3-1-2-2-1-3-1l-3-3v-1c1 0 1-1 2-1v-1h4l1 1 1-1c-1-1-2-1-3-2l-1-2 3 1c1 0 1 0 2-1zm36-89h-1c0-1 0-2 1-3v-1l-2-1h0c1 0 2 0 2-1s0-1-1-2l-1-1 1-2v-4h1l1-1v-2-2c1-1 1-1 1-2s1-2 1-2c0 2-1 5 0 7v-2l1-1 1 3h0c-1 1-1 2-1 3l1 1c0 1-1 3-2 4 0 3-2 5-3 7v2zm-31 81l-3-2v-1c1 0 1 0 2-1 0-2 0-2 1-4h1v-2h0c2-1 3-2 3-4 0-1 0-1 1-1v-1c-1-1-1-1-1-2l1-1h0l1-1v-2c0-1 2-4 2-5 2-3 2-7 4-10 0 0 1-1 1-2v-1h0l8-17v4 2c-2 4-4 8-5 12 0 1-1 2-1 2-2 3-3 8-4 11l-5 10c-2 5-3 10-6 14v2z" class="Z"></path><path d="M677 239c1 1 3 1 4 2h0l1 3 1 2-1 1 1 1c0 2-1 0 0 2 0 1-1 2-1 3v3 3c0 1 1 3 0 4s-1 2-1 4c0 1-1 2-2 3v1c0 2-1 3-1 5 0 1 0 2-1 4-2 5-4 10-6 16 0 0-1 2 0 2-1 2-2 2-2 4l-1 2v-2c0-2 1-3 2-5s0-3 0-6h1c0-1 0-2 1-4v-2c1-2 3-4 3-7 1-1 2-3 2-4s1-2 1-3v-2c1-1 1-1 1-2l-1-2h1c1-2 0-3 0-5h0v-1h1v-3l1-1c0-2-1-2-1-4v-3c-1-2-2-2-3-2-1 2-1 7-3 9h0l3-16z" class="X"></path><path d="M682 244l1 2-1 1 1 1c0 2-1 0 0 2 0 1-1 2-1 3h-1c1-1 1-2 0-3v-2c1-1-1-2-1-3h2v-1zm-11 54h0l4-10h1l-8 22c-2 3-3 6-4 8l-21 50-8 18c-1 3-2 7-4 10-1 1 0 1-1 2l-2 2c0-2 1-3 1-4 1-2 3-5 3-7 1-1 1-2 1-2 2-1 2-1 2-3-1-2 1-5 2-7 0-1 1-3 0-4s-1 0-1-1l3-1c0-1 1-2 2-3v-2c3-4 4-9 6-14l5-10c1-3 2-8 4-11 0 0 1-1 1-2 1-4 3-8 5-12l5-10c0-1 0-2 1-3l1-2c0-2 1-2 2-4z" class="S"></path><path d="M657 291v3h1l-24 58-7 18-8 18h0c0 3-2 7-3 9l2 1h0-2v1l1 2c1-1 2-1 3-2 1 0 1 1 2 2l1 1h0 2l-1 1v1h1c1-1 1-2 2-3h1l-11 25h-2c-2-1-4-1-6-2v-1l-2-1c-1 1-1 1-3 2l-7 17-2 3-5 12h-2c0-1 1-2 2-4v-1l4-9 1-2v-1c1-1 1-2 1-3 1-2 0 0 1-1l1-2v-2l1-2c1-1 1-2 2-3l1-3v-1l3-6 1-3 1-2c0-1 1-2 1-4l3-6 2-4 1-4 1-1v-1c1-1 1-1 1-2h0l1-2 1-2 5-13 1-1v-1l3-6c0-1 1-2 1-3l1-2 2-6 2-3c0-2 1-3 1-4l2-4v-1l5-11v-1l1-1c1-2 2-5 3-7s0 0 0-2l1-2c1-1 1 0 1-1l1-2 3-8c1-2 0 0 0-1l1-2c1-2 1-3 2-4v-1l1-1c1-2 1-4 2-6z" class="T"></path><path d="M619 388h0c0 3-2 7-3 9l2 1h0-2v1l1 2c1-1 2-1 3-2 1 0 1 1 2 2l1 1h0 2l-1 1v1h1c1-1 1-2 2-3h1l-11 25h-2c-2-1-4-1-6-2v-1l-2-1c-1 1-1 1-3 2l2-5 13-31z" class="Z"></path><path d="M608 419l1-2c1 0 1 0 3 1l-1 2-2-1h-1z" class="R"></path><path d="M624 404h1c1-1 1-2 2-3h1l-11 25h-2c-2-1-4-1-6-2v-1l-2-1c-1 1-1 1-3 2l2-5h2 1l2 1 1-2h3v-1c0-1 1-1 1-1 1 0 2 0 3-1v-1c-2-1-2-1-4-1v-1h3s1 0 1-1h2l1-1-1-1c-1 0-2-1-3-1l1-1h0 3 0 1v-1c-1-1-2-1-3-1s0 0-1-1h0 5z" class="a"></path><g class="j"><path d="M615 418c1 0 1 0 2-1l1 1c-1 1-1 2-2 3 0-1-1-1-1-1h-4l1-2h3z"></path><path d="M604 424l2-5h2 1l2 1v1c3 0 3 0 5 1v2h-5l-2-1-2-1c-1 1-1 1-3 2z"></path></g><path d="M604 424l2-5h2 1v3c-1-1-2-1-3-1v1h1c-1 1-1 1-3 2z" class="a"></path><path d="M610 510l2 1c0 1 0 2-1 3l1 2 1-4h1 0l-3 9-1 3h0c2 1 4 1 5 1 2-1 4-1 6-1v1h0c1 1 3 1 3 2h-1c2 2 4 2 6 2-2 1-5 2-7 2s-5 1-7 2c1 0 1 1 2 1-1 2-1 4-2 5v2c-1 1-1 2-1 3-1 2-2 4-4 6l-5 11-2 4-2 4c-1 1-1 2-1 3h4l12 2h-1c-6-1-13 0-19 1h-5c-3 1-6 1-9 2l-2 1c-1 0-2 1-2 2l-1 1-3 2v1c-1 1-1 2-2 3 0 1-1 1-1 2-1 1 0 1-1 2l-2 4c0 1-1 2-1 3l-1 3v1c0 1-1 3-1 4v1 2l1-1h1c-1 1-2 3-4 3l-3 3-4 6 1 3v1h1c1 0 1 0 2 1-1 1-2 2-2 3h2 0c-7 11-11 23-15 35-3 8-5 16-7 25-2 6-4 12-5 18h-1c-1 1-1 2-2 3l1 1c-1 1-1 1-3 1-2 1-4 2-7 4h0l-10 2h0c0-1 0-1 1-2h2l-1-2v-1h-4c-1 1-1 0-3 1 0-1 0-1-1-2v1l-2-1h-1c-2-2-7-3-10-4-1-1-1-2-2-3-1-2-1-5-1-6l-7-25-6-20-11-31c-1-1-1-3-2-4-2-5-5-10-8-15-2-3-3-6-4-9h1c0-1 0-2-1-3l-1-3c-1-2-3-4-4-6v-1c-1-1-2-2-2-4-2-2-2-3-3-5 0-1 1-2 1-3l1 1c3 3 7 5 10 8 10 9 17 24 22 36 2 4 3 8 5 11l8 21 5 17 9 25c3 5 6 8 8 12h1l1-4c0-1 0-2 1-3-1-1 0-6-1-8v-41l-1-8h1l17-39 27-63 2-3v4h0c-1 1-1 2-1 3l-1 1c1 1 3 0 5 0h0c1 0 1-1 2-1h0c0-1 1-2 2-3v1c0 1-1 1 0 2l4-4 5-3c2-2 3-2 5-3l3-2h1c3-1 5-3 9-4v3l1-2h1c0-1 1-1 2-2 3 0 6-1 9-2 0-1 1-2 2-2h0 1c1-2 1-5 2-7z" class="E"></path><path d="M554 622c0-1 0-2 1-3l1-1 1-4h0c-1 1-1 3-2 4-1-2 1-3 1-4 2-4 4-8 6-11v1h1v-1h0c0-1 1-2 1-2 1-1 1-2 1-3v-1l2-1v-2l1-1c0-1 1-2 2-2l-2 4c0 1-1 2-1 3l-1 3-1 1c-1 1-2 3-3 5-2 5-5 10-8 15z" class="G"></path><path d="M446 581l1-1c1 1 3 2 3 3 1 1 1 3 2 4 5 9 12 19 14 30l-14-23c0-1 0-2-1-3l-1-3c-1-2-3-4-4-6v-1z" class="Y"></path><path d="M610 510l2 1c0 1 0 2-1 3l1 2 1-4h1 0l-3 9-1 3h0c2 1 4 1 5 1h0c-2 0-4 1-6 0-5 1-13 6-16 11-4 6-6 15-7 22-1 2-1 6-2 9l-2-2c1-1 2-5 2-6 2-10 5-19 10-28h0c3-3 8-5 11-9h1c2-1 2-2 2-5 1-2 1-5 2-7z" class="b"></path><path d="M610 510l2 1c0 1 0 2-1 3l1 2c-1 1-1 2-2 4l-1 1c-1 1-2 1-3 1 2-1 2-2 2-5 1-2 1-5 2-7z" class="S"></path><path d="M623 527c2 2 4 2 6 2-2 1-5 2-7 2s-5 1-7 2c-8 2-18 7-22 14-2 6-3 12-4 18-1 3-2 6-2 9h2c1-2 3-3 4-5h1l1-1 2-1c1 0 1 1 2 1h1 1v1c-1 1-1 2-1 3h4l12 2h-1c-6-1-13 0-19 1h-5c-3 1-6 1-9 2l-2 1c2-1 4-2 5-4s2-5 3-8c1-7 2-15 6-21s10-9 17-13c1 0 3-2 5-2 1 0 1 0 2-1h-1c1-1 2-2 3-2h3z" class="i"></path><path d="M595 568l2-1c1 0 1 1 2 1h1 1v1c-1 1-1 2-1 3-4 0-8 1-11 2 1-2 3-3 4-5h1l1-1z" class="E"></path><path d="M477 643c2 2 2 4 3 7 1 2 3 4 4 7l7 25c2 5 5 18 9 21 1 1 4 1 6 1h-1v1c-2 0-4 1-5 1 0 0-2-2-3-2l-2-1v1l6 3c1 1 2 1 3 2-5-1-9-4-13-6l-1 1c-1-2-1-5-1-6h1v-3c-1-1-1-1-1-2v-2l-1-1c1-9-4-17-4-25l-7-22z" class="C"></path><path d="M484 665c2 5 4 11 5 17 1 4 2 7 2 11 0 2 0 3 1 4 1 2 1 3 1 4l4 3-2-1v1l6 3c1 1 2 1 3 2-5-1-9-4-13-6l-1 1c-1-2-1-5-1-6h1v-3c-1-1-1-1-1-2v-2l-1-1c1-9-4-17-4-25z" class="F"></path><path d="M607 517h1c0 3 0 4-2 5h-1c-3 4-8 6-11 9h0c-5 9-8 18-10 28 0 1-1 5-2 6-1-2 0-5-1-7 0-4 1-9 2-13 1-3 1-6 3-9l4-9c0-1 1-2 2-2l1-2h1c0-1 1-1 2-2 3 0 6-1 9-2 0-1 1-2 2-2h0z" class="h"></path><path d="M599 524c2-1 4-1 6-2-3 4-8 6-11 9h0-1l-2 2v-1c2-4 4-4 7-7l1-1z" class="V"></path><path d="M607 517h1c0 3 0 4-2 5h-1c-2 1-4 1-6 2-2 0-3 1-5 2h-1l-1 1c0 1 0 1-1 1-2 1-2 4-3 5s-1 2-1 3l-1 1v-1l4-9c0-1 1-2 2-2l1-2h1c0-1 1-1 2-2 3 0 6-1 9-2 0-1 1-2 2-2h0z" class="D"></path><path d="M566 601v1c0 1-1 3-1 4v1 2l1-1h1c-1 1-2 3-4 3l-3 3-4 6c-7 17-13 33-18 50-3 10-6 20-8 30 0 2 0 3-1 5h0c0 1 0 1 1 2 0 0 1-1 2-1-1 1-1 2-2 3l1 1c-1 1-1 1-3 1-2 1-4 2-7 4h0l-10 2h0c0-1 0-1 1-2h2l-1-2v-1h-4c-1 1-1 0-3 1 0-1 0-1-1-2v1l-2-1h-1c-2-2-7-3-10-4-1-1-1-2-2-3l1-1c4 2 8 5 13 6 0 0 1 1 2 1 5 2 11 0 16-2l1-1c2-1 5-2 6-5h0c2-5 2-11 4-16l8-29c2-9 5-17 9-25 1-3 3-7 4-10 3-5 6-10 8-15 1-2 2-4 3-5l1-1z" class="U"></path><path d="M566 601v1c0 1-1 3-1 4v1 2l1-1h1c-1 1-2 3-4 3l-3 3 5-12 1-1z" class="M"></path><path d="M451 594h1l14 23c2 5 4 9 6 14l5 12 7 22c0 8 5 16 4 25l1 1v2c0 1 0 1 1 2v3h-1l-7-25-6-20-11-31c-1-1-1-3-2-4-2-5-5-10-8-15-2-3-3-6-4-9z" class="J"></path><path d="M556 620l1 3v1h1c1 0 1 0 2 1-1 1-2 2-2 3h2 0c-7 11-11 23-15 35-3 8-5 16-7 25-2 6-4 12-5 18h-1c-1 0-2 1-2 1-1-1-1-1-1-2h0c1-2 1-3 1-5 2-10 5-20 8-30 5-17 11-33 18-50z" class="Y"></path><path d="M558 628c-1 0-2 1-2 1v-1l1-1h-1c1-1 1 0 1-1l-1-1 1-1h1c1 0 1 0 2 1-1 1-2 2-2 3z" class="E"></path><defs><linearGradient id="Ad" x1="592.764" y1="550.806" x2="607.346" y2="556.356" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252424"></stop></linearGradient></defs><path fill="url(#Ad)" d="M589 574h-2c0-3 1-6 2-9 1-6 2-12 4-18 4-7 14-12 22-14 1 0 1 1 2 1-1 2-1 4-2 5v2c-1 1-1 2-1 3-1 2-2 4-4 6l-5 11-2 4-2 4v-1h-1-1c-1 0-1-1-2-1l-2 1-1 1h-1c-1 2-3 3-4 5z"></path><path d="M595 558l1-4c1-2 4-5 6-6h1l1-1c1 0 2-1 3-2h0 1l-3 4c-2 0-4 2-5 4l-1 1-2 3-2 1z" class="Y"></path><path d="M605 549h3v1h1l1-1v1l-5 11c-1 0-1-1-1-1 1-1 1-1 1-2v-1c1-2 1-3 2-5-4 0-5 1-8 2l1-1c1-2 3-4 5-4z" class="G"></path><path d="M599 554c3-1 4-2 8-2-1 2-1 3-2 5v1c0 1 0 1-1 2 0 0 0 1 1 1l-2 4-2 4v-1h-1-1c-1 0-1-1-2-1l-2 1-1 1h-1l2-11 2-1 2-3z" class="C"></path><path d="M595 568c0-1 0-1 1-2 2-2 2-2 5-2l1-1c0 1 0 0 1 1v1l-2 4v-1h-1-1c-1 0-1-1-2-1l-2 1z" class="M"></path><path d="M599 554c3-1 4-2 8-2-1 2-1 3-2 5-1 1-2 4-3 5l-1-1c-1 1-2 1-2 1-1 0-2-1-3-2l1-3 2-3z" class="T"></path><path d="M441 572c0-1 1-2 1-3l1 1c3 3 7 5 10 8 10 9 17 24 22 36 2 4 3 8 5 11l8 21 5 17 9 25c3 5 6 8 8 12v2l-4 1-5-3h-1c-1-1-3-3-3-4 0-2-1-4-1-5-2-8-4-16-7-23 0-2-1-5-2-7-2-4-3-8-5-11 0-2-1-2-1-4-1-2-2-5-3-7s-2-3-2-6v-1h-1c0-1-1-2-1-3s0-1-1-1v-2l-2-2c0-1 0-1-1-2h-1c0-2-1-4-2-6v-2c-1-2-1-2-1-3 1-1-1-2-1-3h1c-4-10-9-21-17-28-1-1-1-2-2-2v1h-1l-2-2c-2-2-2-3-3-5z" class="P"></path><defs><linearGradient id="Ae" x1="537.886" y1="692.534" x2="554.941" y2="528.428" xlink:href="#B"><stop offset="0" stop-color="#181716"></stop><stop offset="1" stop-color="#3c3b3b"></stop></linearGradient></defs><path fill="url(#Ae)" d="M583 526c3-1 5-3 9-4v3c-1 0-2 1-2 2l-4 9c-2 3-2 6-3 9-1 4-2 9-2 13 1 2 0 5 1 7l2 2c-2 3-5 3-8 5-2 1-4 2-5 3-7 6-11 13-15 20-10 21-18 43-26 65-3 10-6 21-10 31-2 3-5 6-8 9h-1l1-4c0-1 0-2 1-3-1-1 0-6-1-8v-41l-1-8h1l17-39 27-63 2-3v4h0c-1 1-1 2-1 3l-1 1c1 1 3 0 5 0h0c1 0 1-1 2-1h0c0-1 1-2 2-3v1c0 1-1 1 0 2l4-4 5-3c2-2 3-2 5-3l3-2h1z"></path><defs><linearGradient id="Af" x1="520.14" y1="609.638" x2="528.124" y2="614.421" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#9e9c9f"></stop></linearGradient></defs><path fill="url(#Af)" d="M529 597l1-1 6-12h2l-26 60-1-8h1l17-39z"></path><path d="M583 526c3-1 5-3 9-4v3c-1 0-2 1-2 2-3 2-7 3-9 5-2 0-2 1-3 2-1 0-2 1-2 1l-2 2c-1 0-2 1-2 1l-1 1-1 1-3 2-1 1c-2 1-3 3-5 4v1c-1 2-3 3-4 5l-4 4c0 1-1 2-1 2l-1 1c1-2 2-4 3-5 3-6 6-12 11-17l4-4 5-3c2-2 3-2 5-3l3-2h1z" class="G"></path><path d="M579 528c0 1 0 1-1 1-1 2-3 3-5 4l-2 1h-2l5-3c2-2 3-2 5-3z" class="F"></path><path d="M556 534l2-3v4h0c-1 1-1 2-1 3l-1 1c1 1 3 0 5 0h0c1 0 1-1 2-1h0c0-1 1-2 2-3v1c0 1-1 1 0 2-5 5-8 11-11 17-1 1-2 3-3 5-1 4-4 7-6 10l-7 14h-2l-6 12-1 1 27-63z" class="h"></path><path d="M590 527l-4 9c-2 3-2 6-3 9-1 4-2 9-2 13 1 2 0 5 1 7l2 2c-2 3-5 3-8 5-2 1-4 2-5 3-7 6-11 13-15 20-10 21-18 43-26 65-3 10-6 21-10 31-2 3-5 6-8 9h-1l1-4c0-1 0-2 1-3 9-13 12-29 17-44l16-41c3-7 7-15 11-22 3-5 6-10 10-14 2-2 5-4 7-6 1 0 3-1 4-2 1-2 1-5 2-7 0-4 1-9 2-13 1-5 3-10 5-15l-6 3c2-2 6-3 9-5z" class="f"></path><defs><linearGradient id="Ag" x1="338.631" y1="363.972" x2="350.351" y2="359.036" xlink:href="#B"><stop offset="0" stop-color="#201e1f"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Ag)" d="M296 249c1 0 1 1 1 2s1 2 2 4l5 9c2 3 3 6 5 8 1 1 2 3 3 4l1-1c9 16 16 34 24 51l14 31c2 4 4 9 6 13 1 2 1 4 2 5h0 1l2 5h0c3-3 7-5 9-9l4-4v-1c-1-1-1-3-2-4h0v-3l22 49 16 38c2 5 4 11 7 16 1 4 4 8 5 12l2 7-3-3c-1 1-2 2-4 1h-1c-3-2-8-1-11-1l-3 2c-1-1-3-1-5-2l-8-1c-4 0-8 1-12 1-2 0-3 1-4 1v1 1c-2 0-2-2-4-3h-2l-1-1-1-2-2 1v1c0 1 1 3 1 4l-1 1c-1-2-1-3-1-5l-1-1-2-2c-1 0-1 0-2-1s-2-2-4-3h0-1c-2-1-2-1-4-1h-1-5c-2 1-3 1-4 1l1-2-1-2h-1l-2-3v-1-3h1l-1-2-1-1c0-1 0-2-1-2-1-1-1-2-2-3 0-1 0-2 1-3l-2-2c-1-2 0-4 1-5v-1h-1v1h-1c0-2-2-6-1-8v-2l-1-1 1-1h2 1l1 1 1 1h0 1c0-1 0-2 1-3l1 1 1-2v-2c-3 1-6 1-10 2 0 0 0 1-1 1-1-1-1-3-1-4l-1 1 1 3-1 1-4-1c-2 1-6 1-9 1 1-1 2 0 2-1l-3-6v-1-6c0-1-1-3-1-5-2-3-6-6-6-9 1-2 1-1 0-2v-2l1-2 2 1v-3-2c0-3 3-3 4-6h-1c-1-1-1-2-1-3h0l2-2 1 1v-2c-1 0-1 0-2-1h-1-5-11 0c-2-1-2-2-3-2v-1l-2-2-2-2c1 0 2 0 3-1s1-3 1-5c-1-2-1-4 0-5l-1-2h-2c-1-1-1-2-2-3v-1c1 0 1-1 1-1l3 1h0l-1-1h3c2 0 4 0 6-1 1 0 2-1 3-2h0c3-3 3-5 3-9 0-1 0-2 1-4h0l1-1h0-1l-1-1 2-1 1 1v-1-3c0-1-1-2-1-2l1-2c0-1 0-1-1-2v-1h-1c0-1-1-2-1-3h-2l-1-1c1 0 1-1 1-2 1-1 2-1 3-2h-3c-2-1-4-1-6 1v-3h4l-1-2v-2c-1-1-1-1-1-2h0v-1-3c1 0 1 1 2 1v-2-1-1c1-1 1-1 1-3l2 2h1l1 1v-1c-1-3-2-5-3-7l1-2h2c-2-3-3-8-2-12-1-2-2-2-3-4-1 0-1 1-1 2l-1-1c-1-3-1-4-3-7l-2-3h0c-1-2-3-4-5-5h0c1-1 1-1 0-3v-1c2 2 2 2 5 2l7 7v1l2 4 3 5h1v-1l-2-3v-1l-2-2c0-2-1-2-1-3v-1c0-1-2-2-2-3l-4-4 1-1c0-1 0-3-1-5v2h-1v-2-1c0-2 2-1 3-2z"></path><path d="M383 453v1l5 20h5c-2 1-6 1-7 0s-1-2-2-2c-1-1-3-2-4-3l1-5-1-1h-2l1-2v-1l1-1 3-3v-3z" class="U"></path><path d="M379 460l1-1 3-3-2 8-1-1h-2l1-2v-1z" class="Y"></path><path d="M351 362l4 9 2-1c1 2 1 4 2 5 2 4 4 9 6 13l8 19-1 1 1 1h0v2 1c-1-1-2-4-3-5 0-1-1-2-1-3s0-2-1-2c-1-1-3-4-3-5-1-6-5-11-7-16h0v5l1 1c0 2 1 4 2 6h0c1 1 1 2 1 3-1-1-2-2-2-3-1-3-2-5-3-8-1-2-1-5-2-8l-4-14v-1z" class="K"></path><path d="M357 370c1 2 1 4 2 5 2 4 4 9 6 13l8 19-1 1 1 1h0c-3-3-4-7-5-11-5-9-9-18-13-27l2-1z" class="E"></path><path d="M312 276l1-1c9 16 16 34 24 51l14 31c2 4 4 9 6 13l-2 1-4-9-39-86zm61 131l9 21 18 42h10c4 1 8 3 11 3l1 2 1-1 2 7-3-3c-1 1-2 2-4 1h-1c-3-2-8-1-11-1l-3 2c-1-1-3-1-5-2l-8-1c-4 0-8 1-12 1l1-5 1-4c1 1 3 2 4 3 1 0 1 1 2 2s5 1 7 0l6 1c-1-7-4-14-7-21l-11-24c-3-6-5-13-8-19v-2h0l-1-1 1-1z" class="H"></path><path d="M380 469c1 1 3 2 4 3 1 0 1 1 2 2s5 1 7 0l6 1c8 0 18-1 23 3-1 1-2 2-4 1h-1c-3-2-8-1-11-1l-3 2c-1-1-3-1-5-2l-8-1c-4 0-8 1-12 1l1-5 1-4z" class="d"></path><path d="M348 402c0-2 0-5 1-6 0-2 1-4 3-4l2 2s1 0 2 1c1 0 2 2 2 3l3 4v1c1 1 1 1 1 2l3 6v1c1 1 2 3 2 4l3 6c0 2 1 3 2 5s2 4 3 7 3 6 4 8c1 4 2 8 4 11v3l-3 3-1 1c-2-2-1-4-1-7-1-2-2-5-3-7l-1 1c-1 0-1 0-2-1s-3-2-5-2v-1h4l-9-25c-1-1-2-2-3-2-1 1 0 1-1 1h-1c-1 0-1-1-3-2h-1c-1-1-2-1-2-2l-1-1h-1c-1-1-1-2-2-2v-2-1c2-1 4 0 6 0 0-2-2-3-3-3s-2 1-3 2c0-2 0-3 1-4z" class="P"></path><path d="M358 398l3 4v1c1 1 1 1 1 2l3 6v1c1 1 2 3 2 4h0c-1-1-1-1-1-2h-1c-2-1-2-2-3-4l-1-2 1-1c0-1-1-2-1-3-1-1-2-2-2-3-1-1-1-2-1-3z" class="N"></path><path d="M354 415l1-1c1 0 1-1 1-2l1-1v-3l1-1 1 2c2 2 2 3 2 5h0c2 2 2 3 3 5 0 1 1 2 1 4s2 4 3 6l5 13c1 1 2 3 2 4l-1 1c-1 0-1 0-2-1s-3-2-5-2v-1h4l-9-25c-1-1-2-2-3-2-1 1 0 1-1 1h-1c-1 0-1-1-3-2z" class="M"></path><defs><linearGradient id="Ah" x1="374.317" y1="459.189" x2="353.269" y2="451.383" xlink:href="#B"><stop offset="0" stop-color="#2f2e2f"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#Ah)" d="M350 463v-5c1-2 2-2 4-3h0l1-3c2-1 4-3 6-5 1-1 1-2 2-2h2v-1h0 2c2 0 4 1 5 2s1 1 2 1l1-1c1 2 2 5 3 7 0 3-1 5 1 7v1l-1 2h2l1 1-1 5-1 4-1 5c-2 0-3 1-4 1v1 1c-2 0-2-2-4-3h-2l-1-1-1-2-2 1v1c0 1 1 3 1 4l-1 1c-1-2-1-3-1-5l-1-1-2-2c-1 0-1 0-2-1s-2-2-4-3h0-1c-2-1-2-1-4-1v-6-1l1 1z"></path><path d="M350 463h2c2-1 2-2 4-2 1 1 1 1 1 3-2 1-2 1-2 2l-1 1 1 2-1 1h0-1c-2-1-2-1-4-1v-6-1l1 1z" class="D"></path><path d="M374 447l1-1c1 2 2 5 3 7 0 3-1 5 1 7v1l-1 2h2l-2 1v1h-2v2c0-1 0-1-1-2h0v-1c0-1 0-2-1-2h-1-1c-1 1-1 1-1 2l-1-1-2 2s0-1-1-1h-1c-1 1-2 0-3 1-1-1-1-1-1-2 2-2 2-1 4-1 0-1 1-2 2-2s2 0 3 1c1-1 1-3 3-3l2-2c0-1 0-2-1-3-1-2-1-3-1-5v-1z" class="C"></path><path d="M368 465l2-2 1 1c0-1 0-1 1-2h1 1c1 0 1 1 1 2v1h0c1 1 1 1 1 2v-2h2v-1l2-1 1 1-1 5-1 4h0-2l-1 1c-1-2-2-1-3-1l-2-1h-2s-2-2-2-3h1l-1-3 1-1z" class="F"></path><path d="M376 467v-2h2v3l-1 1c-1-1-1-1-1-2z" class="E"></path><path d="M363 465c1-1 2 0 3-1h1c1 0 1 1 1 1l-1 1 1 3h-1c0 1 2 3 2 3h2l2 1c1 0 2-1 3 1l1-1h2 0l-1 5c-2 0-3 1-4 1v1 1c-2 0-2-2-4-3h-2l-1-1-1-2-2 1v1c0 1 1 3 1 4l-1 1c-1-2-1-3-1-5l-1-1-2-2c-1 0-1 0-2-1s-2-2-4-3l1-1-1-2 1-1h2v-1c1 0 1 0 2 1 1 0 1 1 2 1l1 1c1 0 1 1 2 1h0c-1-1-2-2-2-3l1-1z" class="J"></path><path d="M363 477v-3l-1-1-1 1-2-2 1-2h2v1c1 1 3 3 4 3 1 1 1 0 2 2v2l-1-1-1-2-2 1v1c0 1 1 3 1 4l-1 1c-1-2-1-3-1-5z" class="D"></path><defs><linearGradient id="Ai" x1="395.9" y1="411.457" x2="361.15" y2="381.657" xlink:href="#B"><stop offset="0" stop-color="#6e6d6e"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#Ai)" d="M359 375h0 1l2 5h0c3-3 7-5 9-9l4-4v-1c-1-1-1-3-2-4h0v-3l22 49c-2 1-3 2-4 3l-1 1c-3 5-3 12-8 16l-9-21-8-19c-2-4-4-9-6-13z"></path><defs><linearGradient id="Aj" x1="427.644" y1="460.631" x2="379.931" y2="426.452" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#Aj)" d="M382 428c5-4 5-11 8-16l1-1c1-1 2-2 4-3l16 38c2 5 4 11 7 16 1 4 4 8 5 12l-1 1-1-2c-3 0-7-2-11-3h-10l-18-42z"></path><path d="M353 415h1c2 1 2 2 3 2h1c1 0 0 0 1-1 1 0 2 1 3 2l9 25h-4v1h-2 0v1h-2c-1 0-1 1-2 2-2 2-4 4-6 5l-1 3h0c-2 1-3 1-4 3v5l-1-1v1 6h-1-5c-2 1-3 1-4 1l1-2-1-2h-1l-2-3v-1-3h1l-1-2-1-1c0-1 0-2-1-2-1-1-1-2-2-3 0-1 0-2 1-3l-2-2c-1-2 0-4 1-5v-1h-1v1h-1c0-2-2-6-1-8v-2l-1-1 1-1h2 1l1 1 1 1h0 1c0-1 0-2 1-3l1 1 1-2v-2h0l9-3c2-1 3-2 5-3 1-2 1-2 1-4z" class="J"></path><path d="M338 446v-1c2 0 3 0 5 2l1 1c-2 0-2 1-4 1-1 0-1 0-2 1v1h-1c0-1 0-2 1-3v-2z" class="E"></path><path d="M343 469c0-1 1-1 1-1v-6l1 1 1 1v-1c0-1 0-1 1-2 1 0 1 1 2 1v1 6h-1-5 0z" class="G"></path><path d="M349 463v6h-1v-2h-1c-1-1-1-2 0-3l2-1z" class="Y"></path><path d="M339 464h0v-1c0-2 0-4 1-6l1 1 2 2-1 2h0c1 1 1 1 1 2v5h0c-2 1-3 1-4 1l1-2-1-2v-2z" class="F"></path><path d="M339 464l1-1 1 1v1h1l1-1v5h0c-2 1-3 1-4 1l1-2-1-2v-2zm9-14l1 1 3-3c1 0 2 0 3-1s3-2 5-2h1c-1 2-1 2-2 3-2 1-3 1-4 1-2 2-3 3-5 4v1c-1-1-1 0-1-1v-1c-2-1-2 0-4 0v-1-1h3z" class="C"></path><path d="M334 448h1l1-3s1 1 2 1v2c-1 1-1 2-1 3h1c0 2 1 3 3 5h3l1 1h-1-2l-1 1-1-1c-1 2-1 4-1 6v1h0v2h-1l-2-3v-1-3h1l-1-2-1-1c0-1 0-2-1-2-1-1-1-2-2-3 0-1 0-2 1-3l5 8h1v-2c-1 0-2 0-2-1-1-1-2-3-3-4v-1z" class="W"></path><path d="M344 448h0c1-1 2-1 2-2 0-2 1-4 2-5 0-1-1-1-1-3h1l1 1v4c1 0 1 0 2 2v1l-1 1h-2l-1-1h0l-1 1 2 2v1h-3v1 1c2 0 2-1 4 0v1c0 1 0 0 1 1-2 1-2 1-4 1-1 0-2 1-2 1h-3c-2-2-3-3-3-5v-1c1-1 1-1 2-1 2 0 2-1 4-1z" class="O"></path><path d="M338 451v-1c1-1 1-1 2-1 0 1 1 3 2 4h3v1l1 1c-1 0-2 1-2 1h-3c-2-2-3-3-3-5z" class="L"></path><path d="M357 417h1c1 0 0 0 1-1 1 0 2 1 3 2l-3 3c0 1 1 1 2 2 0 0 0 1 1 2v1c2 4 3 10 5 15v1c-1 0-3-1-4-1l-1 1c0 1 0 0 1 1h-2-1c-1 0-2 0-3-1-1 0-1 0-2 1h-1c-2-1-2-1-3-2v-2-1-6l2-1v-3-3h0v-1-1c0-1 0-1 1-2l1 1v-1c0-2-1-1-2-3h3c0 1 0 1 1 2l1-1-1-2z" class="I"></path><path d="M353 425c1 1 3 2 4 4 0 2-1 6-2 7l-1 2s-1 0-1 1l-2-1v-6l2-1v-3-3z" class="G"></path><path d="M353 415h1c2 1 2 2 3 2l1 2-1 1c-1-1-1-1-1-2h-3c1 2 2 1 2 3v1l-1-1c-1 1-1 1-1 2v1 1h0v3l-1-1c-1 1-2 2-3 2l-1 1s-1 0-1 1v1h1v2h0c-1 0-1 0-2 1h0l-1-1c-1 1 0 2-1 2l-1 3 1 1v1h-2c-1 0-2-1-2-2v-1c-2 1-3 4-4 6v1l-1 3h-1v1c1 1 2 3 3 4 0 1 1 1 2 1v2h-1l-5-8-2-2c-1-2 0-4 1-5v-1h-1v1h-1c0-2-2-6-1-8v-2l-1-1 1-1h2 1l1 1 1 1h0 1c0-1 0-2 1-3l1 1 1-2v-2h0l9-3c2-1 3-2 5-3 1-2 1-2 1-4z" class="j"></path><path d="M338 425h0l9-3v1 1h-1c0 1-1 2-1 3-1-1-1-2-2-2h-1c-2 1-2 1-3 2v2h-1v-2-2z" class="R"></path><path d="M339 427c1-1 1-1 3-2 1 2 1 3 1 5l1 2c-1 1-1 3-1 5l1-1-1 3 1 1v1h-2c-1 0-2-1-2-2 1 0 1-1 2-1-1-2-2-3-2-4v-1h1l1-1h-1c-1-2-1-2 0-3-1-2-1-2-2-2z" class="X"></path><path d="M339 427c1 0 1 0 2 2-1 1-1 1 0 3h1l-1 1h-1l-3-1v1l2 1c-1 1-1 1-1 2l-2-2c-2 1-3 0-4 1-2-1-2-3-2-4l1-2h1l1 1 1 1h0 1c0-1 0-2 1-3l1 1 1-2v2h1v-2z" class="L"></path><path d="M338 436c0-1 0-1 1-2l-2-1v-1l3 1v1c0 1 1 2 2 4-1 0-1 1-2 1v-1c-2 1-3 4-4 6v1l-1 3h-1 0c-1-1-1-2-1-3-1-2 0-3 1-5 1-1 1-2 1-2 1-1 2-2 3-2z" class="U"></path><path d="M328 430l1-1h2l-1 2c0 1 0 3 2 4 1-1 2 0 4-1l2 2c-1 0-2 1-3 2 0 0 0 1-1 2-1 2-2 3-1 5 0 1 0 2 1 3h0v1c1 1 2 3 3 4 0 1 1 1 2 1v2h-1l-5-8-2-2c-1-2 0-4 1-5v-1h-1v1h-1c0-2-2-6-1-8v-2l-1-1z" class="F"></path><path d="M287 257c2 2 2 2 5 2l7 7v1l2 4 3 5h1c0 1 0 2 1 3 0 1 0 1 1 2 0 2 1 4 2 5l3 6 1 2 3 8c1 1 1 0 1 1 0 2 1 3 2 4 1 4 3 7 4 10l4 8 1 2c2 5 5 10 7 15 1 5 5 9 6 14l4 8c0 1 1 2 1 3l3 8c1 2 1 5 2 7-1 2-2 2-3 2h-2c-1 0-2-1-2-1h-1l1-2c1 0 1 0 2-1h0l-1-1c-1 0-1 0-2-1 1-1 1-2 1-3 0-2-1-6-3-8v1h-1l-1-2-1-3c0-4-1-5-4-8 0-1-1-3-1-5l-1 1-1-3c-1-1-1-1-1-2v3c-1-2-2-4-2-7v-4l-1-3-2-3c-1 0-2 1-3 1l-6 6-4 3-1-1-3 3c-1-1-2-1-3-2l-2-1c0-1 0-2 1-4h0l1-1h0-1l-1-1 2-1 1 1v-1-3c0-1-1-2-1-2l1-2c0-1 0-1-1-2v-1h-1c0-1-1-2-1-3h-2l-1-1c1 0 1-1 1-2 1-1 2-1 3-2h-3c-2-1-4-1-6 1v-3h4l-1-2v-2c-1-1-1-1-1-2h0v-1-3c1 0 1 1 2 1v-2-1-1c1-1 1-1 1-3l2 2h1l1 1v-1c-1-3-2-5-3-7l1-2h2c-2-3-3-8-2-12-1-2-2-2-3-4-1 0-1 1-1 2l-1-1c-1-3-1-4-3-7l-2-3h0c-1-2-3-4-5-5h0c1-1 1-1 0-3v-1z"></path><path d="M328 338c2 4 4 8 5 12l-1 1-1-3c-1-1-1-1-1-2v3c-1-2-2-4-2-7v-4z" class="G"></path><path d="M318 318l1 6 5-3v1l-1 4c-1 2-1 3-3 4h-1c-1 0-1 1-2 1h-1c-1 2-1 4-1 6l2-2h0c2-2 3-2 5-2l-6 6-4 3-1-1-3 3c-1-1-2-1-3-2l-2-1c0-1 0-2 1-4h0l1-1h0-1l-1-1 2-1 1 1v-1-3c0-1-1-2-1-2l1-2c0-1 0-1-1-2l1-1h0c2-1 4-1 7-1h2c2-2 2-2 3-5z" class="E"></path><path d="M306 324h0c2-1 4-1 7-1v5c-2 0-2 0-3-1 0-1 0-1-1-2l-1 1c-1-1-2-1-2-2z" class="H"></path><path d="M302 279l4 12c4 9 11 18 12 27-1 3-1 3-3 5h-2c-3 0-5 0-7 1h0l-1 1v-1h-1c0-1-1-2-1-3h-2l-1-1c1 0 1-1 1-2 1-1 2-1 3-2h-3c-2-1-4-1-6 1v-3h4l-1-2v-2c-1-1-1-1-1-2h0v-1-3c1 0 1 1 2 1v-2-1-1c1-1 1-1 1-3l2 2h1l1 1v-1c-1-3-2-5-3-7l1-2h2c-2-3-3-8-2-12z" class="B"></path><path d="M304 316c1 0 1 0 3-1h0 4v1c1 1 1 0 2 1h0c1-1 1-1 2 0v3c0 1-1 1-1 2h-3c-2 1-4-1-5-2-2 0-2 0-3 1h-2l-1-1c1 0 1-1 1-2 1-1 2-1 3-2z" class="F"></path><defs><linearGradient id="Ak" x1="304.792" y1="302.196" x2="301.254" y2="309.706" xlink:href="#B"><stop offset="0" stop-color="#464444"></stop><stop offset="1" stop-color="#626061"></stop></linearGradient></defs><path fill="url(#Ak)" d="M304 301v-1c-1-3-2-5-3-7l1-2h2l2 5c1 2 1 3 1 5 1 2 2 4 2 6l1 2h2c0 1 1 1 1 3h1l1 1h-10l-6 1-1-2v-2c-1-1-1-1-1-2h0v-1-3c1 0 1 1 2 1v-2-1-1c1-1 1-1 1-3l2 2h1l1 1z"></path><path d="M299 303c1 0 3 1 4 1v2c-2 0-2 0-4-1v-2z" class="I"></path><path d="M304 291l2 5c1 2 1 3 1 5-2-3-4-6-5-10h2z" class="Y"></path><path d="M297 308h1c1 1 2 2 4 2 1 0 1 1 1 2h1v1h1l-6 1-1-2v-2c-1-1-1-1-1-2z" class="h"></path><path d="M300 298l2 2h1l1 1 1 2c0 1-1 1-2 1s-3-1-4-1v-1-1c1-1 1-1 1-3z" class="G"></path><path d="M322 333c1 0 2-1 3-1l2 3 1 3v4c0 3 1 5 2 7v-3c0 1 0 1 1 2l1 3 1-1c0 2 1 4 1 5 3 3 4 4 4 8l1 3 1 2h1v-1c2 2 3 6 3 8 0 1 0 2-1 3 1 1 1 1 2 1l1 1h0c-1 1-1 1-2 1l-1 2h1s1 1 2 1h2c1 0 2 0 3-2v1c1 0 2 0 2 1s0 2 1 3c0 0 0 1 1 2v2c1 2 3 5 4 7 1 1 2 2 2 4l-3-4c0-1-1-3-2-3-1-1-2-1-2-1l-2-2c-2 0-3 2-3 4-1 1-1 4-1 6-1 1-1 2-1 4 1-1 2-2 3-2s3 1 3 3c-2 0-4-1-6 0v1 2c1 0 1 1 2 2h1l1 1c0 1 1 1 2 2 0 2 0 2-1 4-2 1-3 2-5 3l-9 3h0c-3 1-6 1-10 2 0 0 0 1-1 1-1-1-1-3-1-4l-1 1 1 3-1 1-4-1c-2 1-6 1-9 1 1-1 2 0 2-1l-3-6v-1-6c0-1-1-3-1-5-2-3-6-6-6-9 1-2 1-1 0-2v-2l1-2 2 1v-3-2c0-3 3-3 4-6h-1c-1-1-1-2-1-3h0l2-2 1 1v-2c-1 0-1 0-2-1h-1-5-11 0c-2-1-2-2-3-2v-1l-2-2-2-2c1 0 2 0 3-1s1-3 1-5c-1-2-1-4 0-5l-1-2h-2c-1-1-1-2-2-3v-1c1 0 1-1 1-1l3 1h0l-1-1h3c2 0 4 0 6-1 1 0 2-1 3-2h0c3-3 3-5 3-9l2 1c1 1 2 1 3 2l3-3 1 1 4-3 6-6z" class="J"></path><path d="M332 405h2c0 1-1 1-1 2v1h-2v-1c0-1 0-2 1-2z" class="G"></path><path d="M333 380l2-1c2 0 2 0 3 1 0 0 1 1 1 2h-2v1h-3v-1c-1 1-2 1-3 1l1-2s1 0 1-1z" class="B"></path><path d="M337 412l1-2h1c1 0 2 0 3-1v1h0c0 3 1 5-1 6-1-1-1 0-1-1h-1s-1 0 0-1c0-1 0-1-1-1l-1-1z" class="M"></path><path d="M326 424l1-2-1-1c0-2 0-3 1-5 0 1 0 1 1 0v-2c2 0 3 0 4 1h0c-1 2-2 2-4 2v5c0 1 0 2-1 3 1 1 1 1 1 2 0 0 0 1-1 1-1-1-1-3-1-4z" class="O"></path><path d="M324 408v-2c1-1 1-1 2-1 1 1 2 1 2 3l-1 1h1 0 2c0 1 0 0-1 1-1 0-2 1-3 2 0 1-1 1-2 2 0-2 0-3 1-5l-1-1z" class="G"></path><path d="M324 408l1 1c-1 2-1 3-1 5 1-1 2-1 2-2l1 1c0 1-1 2-1 2v3h-1c-2 0-5 0-6-1l1-1-1-4c1-1 1-1 0-3 1 0 2 1 3 0l2-1z" class="I"></path><path d="M319 409c1 0 2 1 3 0l1 4-3 3-1-4c1-1 1-1 0-3z" class="F"></path><path d="M337 383v-1l2 1-1 2h1l1-1c1 1 1 1 0 2v2l-1-1-1-1c-1 2-1 4-1 5 1 1 1 2 1 2h-2l-1-1c0-1 0-1-1-1v1l1 3h-1l-1-1v-3h0v-1l-1 1-1-1 2-2c0-2-1-2-1-4 2 0 3 0 5-1z" class="G"></path><path d="M321 399c0 1 1 2 2 2 1-1 0-1 1-2h2c-1 1 0 1-1 2v2c1 0 3 0 4 1h0v-1h1v3l-2 2c0-2-1-2-2-3-1 0-1 0-2 1v2l-2 1c-1 1-2 0-3 0-2 0-3-1-5-2h2l3-1 1-2h1c0-2 0-2-1-3l1-2z" class="C"></path><path d="M337 405h1c1-1 1-1 3-2h1c1-2 2-2 4-3v1c1 0 1 0 2 1-1 1-1 2-1 4 1-1 2-2 3-2s3 1 3 3c-2 0-4-1-6 0v1 2h-2v-1l-1 1v-2l-2-1h-4l-5 1v-1c0-1 1-1 1-2h-2l2-1h1c0 2 0 1 1 2 1 0 1 0 1-1z" class="F"></path><path d="M344 410l1-1v1h2c1 0 1 1 2 2h1l1 1c0 1 1 1 2 2 0 2 0 2-1 4-2 1-3 2-5 3l-9 3h0c-3 1-6 1-10 2 0-1 0-1-1-2 1-1 1-2 1-3v-5c2 0 3 0 4-2h0l2-1v-1h0c1 0 1 0 2-1h1l1 1c1 0 1 0 1 1-1 1 0 1 0 1v3 1c1 0 2 0 2 1s1 1 2 2v-1c0-1 0-2 1-3-1-1-1-1-1-2s1-5 1-6z" class="H"></path><path d="M344 410l1-1v1 2l2 1v1l-1 1c-1 0-2 1-2 3-1-1-1-1-1-2s1-5 1-6z" class="N"></path><path d="M334 355c3 3 4 4 4 8l1 3 1 2h1v-1c2 2 3 6 3 8 0 1 0 2-1 3 1 1 1 1 2 1l1 1h0c-1 1-1 1-2 1l-1 2h1s1 1 2 1h2c1 0 2 0 3-2v1c1 0 2 0 2 1s0 2 1 3c0 0 0 1 1 2v2c1 2 3 5 4 7 1 1 2 2 2 4l-3-4c0-1-1-3-2-3-1-1-2-1-2-1l-2-2c-2 0-3 2-3 4-1 1-1 4-1 6-1-1-1-1-2-1v-1c-2 1-3 1-4 3h-1c-2 1-2 1-3 2h-1v-1c-1-1-1-2-2-3 1-1 1-1 1-2h3v-1-1c-1-1 0-2 0-3l-1-1s0-1-1-2c0-1 0-3 1-5l1 1 1 1v-2c1-1 1-1 0-2l-1 1h-1l1-2-2-1h2c0-1-1-2-1-2l2-2-1-1h-1v-1l1-1 1 1h0v-2-1c-1-1-2-2-2-3v-2l-4-13z" class="M"></path><path d="M347 390l1-1v-1c0-1 1-2 2-2l1 2-1 1h0-1l1 1c0 1-1 2-2 3 0 1-1 2-1 3-1-1-1-2-1-3h-1-1c0-2 1-1 3-3z" class="H"></path><path d="M337 391h2l1-1 2 2c1-2 0-4 1-6h1v4l1-1 2 1c-2 2-3 1-3 3h1l-1 1c-1-1 0-1-1-2l-1 1v4 1c-1-1-1-1-1-2h-1c-1-1-1-1-1-2l-1-1s0-1-1-2z" class="C"></path><path d="M351 383c1 0 2 0 2 1s0 2 1 3c0 0 0 1 1 2v2c1 2 3 5 4 7 1 1 2 2 2 4l-3-4c0-1-1-3-2-3-1-1-2-1-2-1l-2-2c-2 0-3 2-3 4-1 1-1 4-1 6-1-1-1-1-2-1v-1c-2 1-3 1-4 3h-1c-2 1-2 1-3 2h-1v-1c-1-1-1-2-2-3 1-1 1-1 1-2h3v-1-1c-1-1 0-2 0-3 0 1 0 1 1 2h1c0 1 0 1 1 2v-1-4l1-1c1 1 0 1 1 2l1-1h1c0 1 0 2 1 3 0-1 1-2 1-3 1-1 2-2 2-3l1-1h1v1h1v-2-1l-1-1c0-1 0-2-1-3z" class="G"></path><defs><linearGradient id="Al" x1="323.192" y1="419.462" x2="309.509" y2="414.574" xlink:href="#B"><stop offset="0" stop-color="#333132"></stop><stop offset="1" stop-color="#5d5c5b"></stop></linearGradient></defs><path fill="url(#Al)" d="M307 391c1-2 2-3 4-4 2 0 4 1 6 2l5 5v-2h1v3h-1v2 1l-1 1-1 2c1 1 1 1 1 3h-1l-1 2-3 1h-2c2 1 3 2 5 2 1 2 1 2 0 3l1 4-1 1c1 1 4 1 6 1-1 2-1 2-1 4 0 1 0 3 1 3l1 3-1 1-4-1c-2 1-6 1-9 1 1-1 2 0 2-1l-3-6v-1-6c0-1-1-3-1-5-2-3-6-6-6-9 1-2 1-1 0-2v-2l1-2 2 1v-3-2z"></path><path d="M311 415v1c1 1 1 2 1 3h0c0 1 0 1 1 2l3 5 1 2h3 1c-2 1-6 1-9 1 1-1 2 0 2-1l-3-6v-1-6z" class="K"></path><path d="M319 412c-4-2-10-5-12-10v-3l3 3-1 1c2 2 3 3 5 4s3 2 5 2c1 2 1 2 0 3z" class="D"></path><path d="M320 401c1 1 1 1 1 3h-1l-1 2-3 1h-2c-2-1-3-2-5-4l1-1c2 1 3 1 6 1h0c2-1 3-1 4-2z" class="N"></path><path d="M316 407c0-1-1-1-1-2 2-1 3-2 5-2v1l-1 2-3 1z" class="H"></path><path d="M320 421l1-1v1c1 0 1 0 2 1h1c0 1 0 3 1 3l1 3-1 1-4-1h-1-3l-1-2 1-3c1-1 1-2 3-2z" class="B"></path><path d="M316 426l1-3c1-1 1-2 3-2-1 1-2 3-2 4 0 2 1 2 2 3h-3l-1-2z" class="F"></path><path d="M323 422h1c0 1 0 3 1 3l1 3-1 1-4-1v-4c0-1 1-2 2-2z" class="E"></path><path d="M307 391c1-2 2-3 4-4 2 0 4 1 6 2l5 5v-2h1v3h-1-2l-1 1h0-5 2l1 1h-3v1l-1 1c-1-1-3-2-4-3h0l-1 1-1-1v-3-2z" class="d"></path><path d="M314 396s-1 0-2-1v-1h-1-1v-1c1 0 2-1 2-1h3l1 1h3l1 2-1 1h0-5z" class="D"></path><path d="M330 349v-3c0 1 0 1 1 2l1 3 1-1c0 2 1 4 1 5l4 13v2c0 1 1 2 2 3v1 2h0l-1-1-1 1v1h1l1 1-2 2c-1-1-1-1-3-1l-2 1c0 1-1 1-1 1-1 1-1 3-2 3-2 1-5-1-5 2 0 1 0 2 1 3h-1l1 1 1-1h1c0 1 0 1-1 2h-1c-1 1-1 1 0 2l-1 1h-1v-3l-1 1h-1v2l-5-5c-2-1-4-2-6-2-2 1-3 2-4 4 0-3 3-3 4-6h-1c-1-1-1-2-1-3h0l2-2 1 1v-2c-1 0-1 0-2-1h-1 0l15-3c0-1 0-1-1-2-1-2-3-4-5-5l-1-2c0-1 0-1 1-2 0-1 1-2 2-2v-1l-2-2h2 1 0l1-1h1c0 1 0 2 1 3h2c1 0 1-1 2-2l3-2v-3c0-1-1-3-1-5z" class="I"></path><path d="M333 380c0-1 0-1-1-1v-1c0-1 1-1 2-2h1l1 1c0-2 1-3 2-4l2 1v2h0l-1-1-1 1v1h1l1 1-2 2c-1-1-1-1-3-1l-2 1z" class="C"></path><path d="M313 383h1c3 0 4 0 6 2s2 3 2 5h-2c-1-2-3-4-4-5l-3-1v-1z" class="F"></path><path d="M337 369c1 1 1 1 1 2-1 2-3 3-5 3l-1 1-2 2c-1-1-1-2-1-3v-1l4-2 4-2z" class="Q"></path><path d="M329 374c2 0 2 0 3 1l-2 2c-1-1-1-2-1-3z" class="d"></path><path d="M324 375c1 0 3-1 5-2v1c0 1 0 2 1 3-2 0-2-1-3 0s-2 1-4 1v1c-3 1-11 3-14 3l2-2 1 1v-2c-1 0-1 0-2-1h-1 0l15-3z" class="R"></path><path d="M330 349v-3c0 1 0 1 1 2l1 3c1 4 2 9 4 14 0 1 1 3 1 4l-4 2v-2l-1-2h0c-1 0-1-1-2-1l1-2c0-2 0-2-1-3l-2-2 3-2v-3c0-1-1-3-1-5z" class="M"></path><path d="M332 367v-3l1-1c1 1 2 3 2 4s0 1-2 2l-1-2h0z" class="H"></path><path d="M331 357c1 1 1 1 1 2v2c1 0 1 1 1 2l-1 1v3c-1 0-1-1-2-1l1-2c0-2 0-2-1-3l-2-2 3-2z" class="N"></path><path d="M318 359h2 1 0l1-1h1c0 1 0 2 1 3h2c1 0 1-1 2-2l2 2c1 1 1 1 1 3l-1 2c1 0 1 1 2 1h0l1 2v2l-4 2c-2 1-4 2-5 2 0-1 0-1-1-2-1-2-3-4-5-5l-1-2c0-1 0-1 1-2 0-1 1-2 2-2v-1l-2-2z" class="I"></path><path d="M318 359h2 1 0l1-1h1c0 1 0 2 1 3h2c1 0 1-1 2-2l2 2h-2c-2 1-2 2-3 4l-5-4-2-2z" class="B"></path><path d="M328 361h2c1 1 1 1 1 3l-1 2c1 0 1 1 2 1h0l-1 1v1c-1 1-3 3-4 3s-1-1-2-2v-2h2 2c0-2 0-2-1-4l1-2-1-1z" class="D"></path><defs><linearGradient id="Am" x1="322.358" y1="368.648" x2="318.135" y2="364.36" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#6f6e6e"></stop></linearGradient></defs><path fill="url(#Am)" d="M317 366c0-1 0-1 1-2 0-1 1-2 2-2v2c1 1 4 2 3 5v2c1 1 0 0 0 2-1-2-3-4-5-5l-1-2z"></path><path d="M322 333c1 0 2-1 3-1l2 3 1 3v4c0 3 1 5 2 7 0 2 1 4 1 5v3l-3 2c-1 1-1 2-2 2h-2c-1-1-1-2-1-3h-1l-1 1h0-1-2l2 2v1c-1 0-2 1-2 2-1 1-1 1-1 2l1 2c2 1 4 3 5 5 1 1 1 1 1 2l-15 3h0-5-11 0c-2-1-2-2-3-2v-1l-2-2-2-2c1 0 2 0 3-1s1-3 1-5c-1-2-1-4 0-5l-1-2h-2c-1-1-1-2-2-3v-1c1 0 1-1 1-1l3 1h0l-1-1h3c2 0 4 0 6-1 1 0 2-1 3-2h0c3-3 3-5 3-9l2 1c1 1 2 1 3 2l3-3 1 1 4-3 6-6z" class="S"></path><path d="M290 375h3c1-2 1-2 2-3l1-1c-1-1-1-3-1-4 0-3-1-4 0-7 0-1-1-1-2-2h1c0-1 0-1 1-1v1c1 0 2 0 3 1h-1l1 1v1h-1c-1 3-2 5 0 8v1c0 3 0 5-2 7l-2 1c-2-1-2-2-3-2v-1z" class="D"></path><path d="M286 353l3 1 1 1-1 1v-1l-1 1 2 1 2 2v1l-1 2h1v4l1 1c-1 3-1 3-3 6h-2l-2-2c1 0 2 0 3-1s1-3 1-5c-1-2-1-4 0-5l-1-2h-2c-1-1-1-2-2-3v-1c1 0 1-1 1-1z" class="V"></path><path d="M300 357c1-1 2-1 3-1 0 1 1 1 2 1v-1l-1-1c1-2 1-2 2-3 0 1 1 2 1 2 1 0 1 0 1 1 1 0 2 1 3 1h0c1 1 2 1 3 1 1 1 1 3 3 3l1 2c-1 1-1 1-2 1h-1c0-1-1-1-1-2l-2 1c-3-1-6-4-9-5h-3z" class="g"></path><path d="M312 342l4-3h2l-1 2h-1 0c-1 1-2 1-2 2-1 0-2 2-3 3-2 2-3 4-5 6-1 1-1 1-2 3l1 1v1c-1 0-2 0-2-1-1 0-2 0-3 1h0c-1 1-2 0-3-1l15-14z" class="a"></path><path d="M308 344l3-3 1 1-15 14c-1 1-2 1-3 0l-4-1-1-1h0l-1-1h3c2 0 4 0 6-1 1 0 2-1 3-2h0c3-3 3-5 3-9l2 1c1 1 2 1 3 2z" class="L"></path><path d="M305 342c1 1 2 1 3 2h-1c-1 1-1 2-2 3h-1c0-3 0-3 1-5z" class="H"></path><defs><linearGradient id="An" x1="320.468" y1="368.783" x2="300.018" y2="371.09" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2c2a2b"></stop></linearGradient></defs><path fill="url(#An)" d="M298 359h3 0c2 1 2 1 3 2 5 1 9 3 13 5l1 2c2 1 4 3 5 5 1 1 1 1 1 2l-15 3h0-5-11 0l2-1c2-2 2-4 2-7v-1c-2-3-1-5 0-8h1v-1l-1-1h1z"></path><path d="M303 363c3 0 6 2 9 3-2 1-3 0-6 0-2-1-4-2-6-1v2 1c-1-1-1-2-1-3 1-2 1-1 2-1s2-1 2-1z" class="G"></path><path d="M300 367l3 3c1 1 1 2 2 3 2 1 2 2 1 4l3 1h0-5-3c0-1 0-2 1-2v-2c1-3-1-4-2-6v-1z" class="B"></path><path d="M298 359h3 0c2 1 2 1 3 2 5 1 9 3 13 5l1 2h0c1 1 2 2 3 4h-1c-2-2-5-4-8-6h0c-3-1-6-3-9-3 0 0-1 1-2 1s-1-1-2 1c0 1 0 2 1 3 1 2 3 3 2 6v2c-1 0-1 1-1 2h3-11 0l2-1c2-2 2-4 2-7v-1c-2-3-1-5 0-8h1v-1l-1-1h1z" class="I"></path><path d="M301 378h-2l-1-1c0-1 1-2 1-3v-3c0-1 0-2-1-2l-1-1c0-1 0-3 1-5 2-1 2-1 5 0 0 0-1 1-2 1s-1-1-2 1c0 1 0 2 1 3 1 2 3 3 2 6v2c-1 0-1 1-1 2z" class="T"></path><path d="M322 333c1 0 2-1 3-1l2 3 1 3v4c0 3 1 5 2 7 0 2 1 4 1 5v3l-3 2c-1 1-1 2-2 2h-2c-1-1-1-2-1-3h-1l-1 1h0-1-2l-2-3c-4 0-6-3-8-4 2-3 4-6 7-8h1v-3h1l1-2h-2l6-6z" class="J"></path><path d="M327 335l1 3v4h-2-1c0-3 1-4 2-7z" class="T"></path><path d="M322 333c1 0 2-1 3-1 0 2 0 3-2 4l-2 1c-1 1-2 1-3 2h-2l6-6z" class="Q"></path><path d="M317 341h0 3l1 1-1 1s0 1 1 2v1l-1 1-1 1 1 1v1l-2 2 2 1 2-1s1 1 1 2h1c1 1 1 1 1 2l-1 1-1 1h-1l-1 1h0-1-2l-2-3c-4 0-6-3-8-4 2-3 4-6 7-8h1v-3h1z" class="G"></path><path d="M316 352h0l-1-1c0-2 0-3 1-4h1l-1 5z" class="B"></path><path d="M317 347v1c1-1 2-1 3-1l-1 1 1 1v1l-2 2h0-2l1-5z" class="D"></path><path d="M364 190l3-3c1-1 3-1 4-2v-1c1 0 2 0 2 1-8 5-14 11-18 20-1 1-2 3-2 5-3 6-5 14-5 21 0 5 1 10 2 15 2 7 4 14 7 21l12 28 48 110 33 77 14 34c1 3 3 7 5 11 0 1 1 3 2 4 1 0 1 0 1 1 3-2 7-4 10-5 2 3 3 5 4 8 1 1 2 4 2 6 2 4 4 8 6 13l1 1c2 8 6 18 11 24l4 4v-1l3 3h2c1-3 3-6 5-8l1 1h2c2 0 3 1 5 2h2c-1 2-3 3-5 5l-3 3c-1 0 0 5 0 6l-1 3h1c1-1 2-2 3-4l7-11c1-2 3-5 4-7 6-10 9-21 14-31l5-11 1 1-27 63-17 39h-1l1 8v41c1 2 0 7 1 8-1 1-1 2-1 3l-1 4h-1c-2-4-5-7-8-12l-9-25-5-17-8-21c-2-3-3-7-5-11-5-12-12-27-22-36-3-3-7-5-10-8l-1-1h0c-1-2-1-4-1-5l-1-2v-2l-1-5h0c-1-2-1-2-3-3-1-1-2-2-3-2 0-2 0-2-1-4 2 0 3-1 4-3v-4c-1-7-4-11-10-15l-4-3h-1l-1-2c-1-2-3-2-3-5 1-2 1-4 2-6l2 2h1l-1-2c-2-1-5-7-5-9h-1c-1-2-3-4-5-6-1-2-2-5-3-7l-1-3-3-3 3-2c3 0 8-1 11 1h1c2 1 3 0 4-1l3 3-2-7c-1-4-4-8-5-12-3-5-5-11-7-16l-16-38-22-49-12-27-15-34c-1-1-1-4-2-6h0c-3-4-5-9-6-14-7-15-13-33-11-50 1-1 1-4 2-5l1 1c-1 8-1 15 1 24 0 2 0 5 1 7s1 4 2 6v-1l-1-4c-1-4-3-14-1-18 0-9 1-18 5-27 1-2 2-5 4-7v1l-3 6v1c-1 1-1 1-1 2l-1 1v2 1h-1v3c1-1 2-4 3-5 0-1 0-2 1-3l3-6c1-2 2-3 3-4 1-2 5-6 7-7l1-1h1c0-1 1-1 2-2h0c1 0 1 0 2-1s2 0 3 0 2-2 3-2z" class="H"></path><path d="M364 190c-1 2-2 3-3 4-3 3-5 5-7 8-3 4-5 9-7 15 0 1-1 2-1 4-1 6 0 12 0 18l-1 1v-2h-2l1-1v-1h-2l1-1v-1h-1v-1h1v-1-1l1-2h-1v-1c1 0 1 0 2-1v-2-3l-1-1c0-1 0-3 1-4v-1c0-1 0 0 1-1h1c-1-2-1-2-2-2-1 2-2 4-3 5v1c-1 1-1 1-2 3h1c-1 1-1 2-2 3v1h-2 1c-1-1-1-1-1-2h1c0-1 0-1 1-2v-1c1-3 3-5 4-8 1 0 1-1 1-1 1-1 1-2 2-3 0-1 1-1 1-3 1 0 1-1 2-2v-1c1-1 1-1 2-3 3-3 7-5 10-8 1 0 2-2 3-2z" class="h"></path><defs><linearGradient id="Ao" x1="347.438" y1="246.802" x2="338.972" y2="251.599" xlink:href="#B"><stop offset="0" stop-color="#6d6c6d"></stop><stop offset="1" stop-color="#868585"></stop></linearGradient></defs><path fill="url(#Ao)" d="M343 238h2v2l1-1c0 4 1 8 2 12s2 7 3 11c-1 0 0 0-1-1l-2-2c0-1 0-1-1-2l-1 1c-1 0-1 0-2-1-1 1-2 1-3 1l1-1c-1-2-1-2-1-3-1-2-1-2-2-2-1-1 0-1-1-1h-1v-1h1c-1-1 0-4 0-5v-3h1v-1h2v-1c1-1 1-2 2-2z"></path><path d="M343 238h2v2h-4c1-1 1-2 2-2zm-1 13h1 1v1l1 1c1 1 1 2 2 3h-1l-2-2v-1c-1-1-2-1-2-2z" class="S"></path><path d="M415 410c5 8 7 16 11 24 3 7 7 15 9 23l-2-2h-3l-1-1h2v-1h-3c-1 1-1 1-2 1v-1h1l2-2h-1l-2 1h0v-1l-1-2c-1-1-1-1-1-2s0 0 1-1l1-1h-3c2-1 2-1 3-3l1-1c-2 0-2 0-3-1 1-1 0 0 0-1s-1-1-2-2h1l1-1h-2l1-2c-1-1-2-1-2-2h-1v-1l1-1v-1c0-1 0-1-1-2h-1v-1l1-1c-2-1-2-1-3-1v-1 1l1-1v-1c-1 0-1 0-2-1l1-1-1-1v-5c-1-2-1-2-1-4z" class="h"></path><path d="M423 445c2-1 2-1 3-3l1 1c1 0 0 0 1 1 1 0 1 1 0 2 1 1 1 1 1 3l-2 1v-1h-2c-1-1-1-1-1-2s0 0 1-1l1-1h-3z" class="c"></path><path d="M410 431h0v-2c0-1 1-1 2-2h4l1 1c-1 0-2 0-2 1 1 1 4-1 6 1l-1 1v1h1c0 1 1 1 2 2l-1 2h2l-1 1h-1c1 1 2 1 2 2s1 0 0 1c1 1 1 1 3 1l-1 1c-1 2-1 2-3 3h3l-1 1c-1 1-1 0-1 1-1-1-1-1-3-1v1h0v1c-1 0-2 1-2 2v1c0 1 1 1 0 2l-9-22z" class="g"></path><path d="M423 445l-1-2v-1h0c-1 0-2 0-2-1h1v-1-1l-2 1v-1l1-1-3-3c1 0 1 0 1-1l-2-1h1v-1l-1-1 1-1h2l1 1v1h1c0 1 1 1 2 2l-1 2h2l-1 1h-1c1 1 2 1 2 2s1 0 0 1c1 1 1 1 3 1l-1 1c-1 2-1 2-3 3z" class="S"></path><path d="M337 226h2v-1c1-1 1-2 2-3h-1c1-2 1-2 2-3v-1c1-1 2-3 3-5 1 0 1 0 2 2h-1c-1 1-1 0-1 1v1c-1 1-1 3-1 4l1 1v3 2c-1 1-1 1-2 1v1h1l-1 2v1 1h-1v1h1v1l-1 1h2v1l-1 1c-1 0-1 1-2 2v1h-2v1h-1v3c0 1-1 4 0 5h-1v1h1c1 0 0 0 1 1-1 0-2 0-2 1-1 2-1 6 0 8l1 1v1 1l1 1 1 3c1 1 1 2 1 3 1 2 3 4 2 7-1-3-2-6-4-9-2-4-4-8-4-13 1-2 0-4 0-5l1-1c-1-5 0-11-1-16l2-1v-2l-1-1c0-2 1-2 1-3v-1z" class="e"></path><path d="M471 541c2 3 3 7 5 11s3 8 4 12c1 2 3 4 2 6l1 1 12 23h0c-2 0-2 0-3-2 0 0 0-1-1-2v1l-1-2c-3-4-5-9-8-14l-10-16-6-9c0-1 0-2 1-3-1 0-2 0-2-1v-1l1 1h1 4v-1-1-3z" class="K"></path><path d="M471 541c2 3 3 7 5 11-1 0-1 1-1 2h1l-2 2h-2 0c1 1 2 0 3 2-1 1-1 1-2 1l-1-1v1l-6-9c0-1 0-2 1-3-1 0-2 0-2-1v-1l1 1h1 4v-1-1-3z" class="D"></path><defs><linearGradient id="Ap" x1="329.541" y1="223.585" x2="338.459" y2="242.415" xlink:href="#B"><stop offset="0" stop-color="#2f2d2f"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#Ap)" d="M341 204v1l-3 6v1c-1 1-1 1-1 2l-1 1v2 1h-1v3c1-1 2-4 3-5 0-1 0-2 1-3l3-6c1-2 2-3 3-4 1-2 5-6 7-7l1-1h1c0-1 1-1 2-2h0c1 0 1 0 2-1s2 0 3 0c-3 3-7 5-10 8-1 2-1 2-2 3v1c-1 1-1 2-2 2 0 2-1 2-1 3-1 1-1 2-2 3 0 0 0 1-1 1-1 3-3 5-4 8v1c-1 1-1 1-1 2h-1c0 1 0 1 1 2h-1v1c0 1-1 1-1 3l1 1v2l-2 1c1 5 0 11 1 16l-1 1c0 1 1 3 0 5-2-5-3-12-3-18 0-9 1-18 5-27 1-2 2-5 4-7z"></path><path d="M335 234c-1-2-1-2 0-4-1-2 0-3 1-5 1-6 4-15 9-19l1-1c1-2 1-3 2-4l3-1c-1 2-1 2-2 3v1c-1 1-1 2-2 2 0 2-1 2-1 3-1 1-1 2-2 3 0 0 0 1-1 1-1 3-3 5-4 8v1c-1 1-1 1-1 2h-1c0 1 0 1 1 2h-1v1c0 1-1 1-1 3l1 1v2l-2 1z" class="D"></path><path d="M510 582l3 3c0 3 1 5 2 7s3 6 3 8c1 2-5 17-6 21h-1v-3c-3-3-6-10-7-14v-1c1-2 0-1 1-2s1-1 1-2v-2l1-1c0-2 0-2 2-4h2v-1c1-3 1-6-1-8v-1z" class="b"></path><path d="M504 604v-1c1-2 0-1 1-2s1-1 1-2v-2l1-1c0-2 0-2 2-4h2v-1 27c-3-3-6-10-7-14z" class="D"></path><path d="M493 581c1-1 2-3 4-3v1l1 1v-1c1 0 1-1 2-1s2 0 2 1v2h2l2-2 4 4c2 2 2 5 1 8v1h-2c-2 2-2 2-2 4l-1 1v2c0 1 0 1-1 2s0 0-1 2v1l-7-12-4-11z" class="N"></path><path d="M493 581c1-1 2-3 4-3v1l1 1v-1c1 0 1-1 2-1s2 0 2 1v2c-2 1-3 3-6 3h-1v2c1 1 2 4 3 6h-1l-4-11z" class="B"></path><defs><linearGradient id="Aq" x1="502.02" y1="580.165" x2="486.864" y2="557.824" xlink:href="#B"><stop offset="0" stop-color="#2e2c2d"></stop><stop offset="1" stop-color="#676767"></stop></linearGradient></defs><path fill="url(#Aq)" d="M484 561l8-6 1-1c1 0 1 1 2 1 2 8 6 18 11 24l-2 2h-2v-2c0-1-1-1-2-1s-1 1-2 1v1l-1-1v-1c-2 0-3 2-4 3l-9-20z"></path><defs><linearGradient id="Ar" x1="491.126" y1="555.037" x2="473.868" y2="530.536" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#afaeae"></stop></linearGradient></defs><path fill="url(#Ar)" d="M471 531c1 0 1 0 1 1 3-2 7-4 10-5 2 3 3 5 4 8 1 1 2 4 2 6 2 4 4 8 6 13l1 1c-1 0-1-1-2-1l-1 1-8 6-13-30z"></path><defs><linearGradient id="As" x1="484.326" y1="596.202" x2="491.042" y2="593.183" xlink:href="#B"><stop offset="0" stop-color="#d4d4d3"></stop><stop offset="1" stop-color="#fdfcfc"></stop></linearGradient></defs><path fill="url(#As)" d="M464 553c-1-2-1-4 0-6l2 3 6 9 10 16c3 5 5 10 8 14l1 2 20 45 1 8v41c1 2 0 7 1 8-1 1-1 2-1 3l-1-33c0-5 1-13 0-18-1-4-4-9-5-12l-14-31c-3-8-7-15-12-22-4-8-8-16-13-22l-1-1c-1-2 1 0 0-2l-2-2z"></path><path d="M448 487l7 17 6 14c1 1 2 3 2 4 2 5 4 10 6 14 0 1 2 4 2 5v3c0 1 0-1 0 1v1h-4-1l-1-1v1c0 1 1 1 2 1-1 1-1 2-1 3l-2-3-2-2c-1-2-3-4-4-6-2-4-4-8-5-12s0-8 2-11h-1c-2-2-4-3-6-5-2-3-6-6-8-10-1-2-2-3-3-5 1 0 2 0 3-1 2-1 4-1 6-1h1l1-1h0l-1-1c-1 0-1 0-1-1h2v-4z" class="h"></path><path d="M456 517l2 3h1c-1 2-2 2-4 2l1-1c0-2-1-2 0-4z" class="S"></path><path d="M459 520l2 1-1 1v1h0c2 3 3 4 3 7l-1 2c1 0 2 1 3 2 0 1 0 1-1 1l1 3c-2 1-3 0-5 0 0-1-1-1-2-1-1-1-1-2-2-3 0-1 0-2-1-3v-2l1-1v-2c1-1 1-2 0-3h-2v-1h1c2 0 3 0 4-2z" class="X"></path><path d="M456 526h3v3h0c-2-1-2-1-3-3z" class="S"></path><path d="M456 526c1 2 1 2 3 3v1l-1 1h1 1c0 1 0 1-1 2h1l2 2h-4c0-1-1-2-1-4h0l-1-1-1 1v-2l1-1v-2z" class="c"></path><path d="M455 531l1-1 1 1h0c0 2 1 3 1 4h4l1 1 1-1 1 3c-2 1-3 0-5 0 0-1-1-1-2-1-1-1-1-2-2-3 0-1 0-2-1-3z" class="e"></path><path d="M448 487l7 17 6 14c1 1 2 3 2 4v5c1 2 2 5 2 8 1 1 3 2 3 3s0 0-1 1c2 0 1 0 2-1l1 1c-1 1-1 1-2 1h1v1 1l-1 1h-3v-1c-1-1-1-1-2-1-1-1-2-3-3-3 2 0 3 1 5 0l-1-3c1 0 1 0 1-1-1-1-2-2-3-2l1-2c0-3-1-4-3-7h0v-1l1-1-2-1h-1l1-2-2-2v-3c-1-2-2-4-5-4v-1s1 0 2-1h-1v-2h-2-1c0-1 1-2 1-3h1c-1-1-3-1-4-1 0-1 2-3 3-4-1 0-1-1-2-1-1-1-2-1-2-2h-1 1l1-1h0l-1-1c-1 0-1 0-1-1h2v-4z" class="c"></path><path d="M440 495c2-1 4-1 6-1h1c0 1 1 1 2 2 1 0 1 1 2 1-1 1-3 3-3 4 1 0 3 0 4 1h-1c0 1-1 2-1 3h1 2v2h1c-1 1-2 1-2 1v1c3 0 4 2 5 4v3l2 2-1 2-2-3v-1h-1-1c-2-2-4-3-6-5-2-3-6-6-8-10-1-2-2-3-3-5 1 0 2 0 3-1z" class="j"></path><path d="M440 495c2-1 4-1 6-1h1c0 1 1 1 2 2 1 0 1 1 2 1-1 1-3 3-3 4h-2l1-1c-1-1 0-1 0-2l1-1h-2l-1-2c-2 1-2 1-5 0z" class="S"></path><path d="M456 516s0-1-1-2l-2-2c-2-1-3-3-4-4l1-1v-1h-1c-1 0-1 1-2 1l-1-1 1-2-1-1c-1 0-2 0-3-1l-1-1v-3h5c0 1-1 1 0 2l-1 1h2c1 0 3 0 4 1h-1c0 1-1 2-1 3h1 2v2h1c-1 1-2 1-2 1v1c3 0 4 2 5 4v3l2 2-1 2-2-3v-1z" class="X"></path><path d="M419 453c1-1 0-1 0-2v-1c0-1 1-2 2-2v-1h0v-1c2 0 2 0 3 1 0 1 0 1 1 2l1 2v1h0l2-1h1l-2 2h-1v1c1 0 1 0 2-1h3v1h-2l1 1h3l2 2 9 20c1 3 2 7 4 10v4h-2c0 1 0 1 1 1l1 1h0l-1 1h-1c-2 0-4 0-6 1-1 1-2 1-3 1-1-2-2-5-3-7l-1-1c-1-3-3-7-4-10l-10-25z" class="a"></path><path d="M445 489c-2 0-4-1-6 0l2 1v1 2h-2c-1-1-1-2-2-3h0c-1-1 0-1 0-2l-1-1h1 1 1c2 0 3 0 4 1h0l2 1z" class="S"></path><path d="M430 455h3l2 2 9 20c1 3 2 7 4 10v4h-2c0 1 0 1 1 1l1 1h0l-1 1v-1h-4c-1-1-1-1-1-3h4l-1-1-2-1 2-2-2-2v-1h-2l2-2c-1-2-2-3-3-4h-2v-1l1-1-1-1 1-1-1-1h-1l-1-1h1v-1c-1-1 0-1 0-2v-3c-2-1-2-1-4-1v-1h1v-1h-2c-2 0-2-1-3 0v-1h0c2 0 2 0 4-1v-1h-1l-1 1c-1-1-1-1-2-1s0 0-1-1h4v-1c-1 0-1 0-2-1v-1z" class="e"></path><path d="M419 453c1-1 0-1 0-2v-1c0-1 1-2 2-2v-1h0v-1c2 0 2 0 3 1 0 1 0 1 1 2l1 2v1h0l2-1h1l-2 2h-1v1c1 0 1 0 2-1h3v1h-2l1 1v1c1 1 1 1 2 1v1h-4c1 1 0 1 1 1s1 0 2 1l1-1h1v1c-2 1-2 1-4 1h0v1c1-1 1 0 3 0h2v1h-1v1c2 0 2 0 4 1v3c-1 1-1 1-2 1h-3c-1 1-1 0-1 0-1 0-1 1-3 1l1-1c-1-1-1-2-2-3v4l1 1v1h0c1 1 1 1 1 2v1c1 1 1 1 1 2l-1 1-10-25z" class="V"></path><path d="M432 462h2v1h-1v1c2 0 2 0 4 1v3c-1 1-1 1-2 1h-3c-1 1-1 0-1 0-1 0-1 1-3 1l1-1c-1-1-1-2-2-3 0-1 0-2-1-3l1-1 1 1h0v1l1 1c1 0 2 0 3-1h0-3c1-1 2-1 3-2zm-40-104l16 35c2 6 6 12 7 17 0 2 0 2 1 4v5l1 1-1 1c1 1 1 1 2 1v1l-1 1v-1 1c1 0 1 0 3 1l-1 1v1h1c1 1 1 1 1 2v1c-2-2-5 0-6-1 0-1 1-1 2-1l-1-1h-4c-1 1-2 1-2 2v2h0c-2-3-4-7-5-11-1-2-3-6-3-8-1-1-1-2-1-2l-2-5c-4-6-6-13-9-19l1-1h0c-1-2-1-2-1-3 2 0 3-1 4-3l3-3v-1h-1l-1 1c0 1 0 0-1 1l-1-1h1v-1h0l-1 1-1-1c2-1 3 0 5-2l-1-1c-1 0-3 1-4 1v-1c1 0 2-1 3-2v-1l-1-1h-1-1l1-1v-3l-1-6z" class="S"></path><path d="M395 381h2 0l1 2-1 1h1l2-2h1v1h0v2l-1 1h-2v-1l-2-1c-1 1-1 1-2 1l1-2v-2z" class="V"></path><path d="M405 420c1-1 0-2 0-3h0l1-1h0v2l2 1v1h1l-1-1 1-1h1l1 1h2l2 1 1-1 1 1-1 1c1 1 1 1 2 1v1l-1 1v-1 1c1 0 1 0 3 1l-1 1v1h1c1 1 1 1 1 2v1c-2-2-5 0-6-1 0-1 1-1 2-1l-1-1h-4c-1 1-2 1-2 2v2h0c-2-3-4-7-5-11z" class="X"></path><path d="M394 379l2-1c2 1 2 1 3 3h0-1c-1-1-1-1-2-1l-1 1v2l-1 2c1 0 1 0 2-1l2 1v1h-1c1 1 1 1 1 3h1 2c1 0 1 0 2 1l-2 2h2 1l1 1h-1c-1 1-1 3-2 4h1l2-2h1c0 1 0 1-1 2h2v1c-1 1-1 2-1 2-2 1-5 0-7 2l1 1v-1c1 0 7-1 9-1 0 2 0 2-1 4 1 1 1 0 1 1v1c-1 1-3 2-4 2h-1-1v1c1 0 1-1 2-1 0 0 0 1 1 1s4-2 5-2c0 0 0 1 1 1l-1 1h0c-2 0-3 0-4 1l-2 1v-1l-1 1h1v1h-2 0c0-1 0-1-1-1h0c-1-1-1-2-1-2l-2-5c-4-6-6-13-9-19l1-1h0c-1-2-1-2-1-3 2 0 3-1 4-3z" class="Z"></path><path d="M397 386c1 1 1 1 1 3h1 2 0c-1 1-1 1-2 1l-1 1h-2l1-2-2-2 2-1z" class="U"></path><path d="M339 252c1 0 1 0 2 2 0 1 0 1 1 3l-1 1c1 0 2 0 3-1 1 1 1 1 2 1l1-1c1 1 1 1 1 2l2 2c1 1 0 1 1 1l13 29c1 2 3 6 3 7v4h0c1 2 2 3 2 6l-2-2-1 1 1 2h-1l-3-2h-1v1h0c1 0 1 0 2 1h-1 0l1 2-1 1c1 0 1 0 2-1l1 1v1l-1 1v2c0 1-1 1-1 2 1 1 2 3 3 4l-1 1h1c0 1 0 2 1 3 0 1 0 1 1 2-1 0-1 1-2 1v2l-15-34-9-19c1-3-1-5-2-7 0-1 0-2-1-3l-1-3-1-1v-1-1l-1-1c-1-2-1-6 0-8 0-1 1-1 2-1z" class="g"></path><path d="M350 286c1-1 1-1 1-2l1-1h-4c2-1 2-1 4-1 1-1 2-1 4-2h1l1 1-1 1 1 1c-1 1-1 1-2 1-1 1 0 1-2 1-1 1-2 1-4 1zm-11-21l2-1-1-1h-1c1-1 0-1 1-1v-1c-1-1-1-2-1-3h1l2 3v1l1 1h0v1h1 0c0 1 0 1 1 2v2l1 1v1h1v-1c1-1 1-2 2-3h1l1 1v1h-1c-1 0-1 0-2 1h1 1c1 0 2 0 3 1v2l1 1v1l1 1c-1 1-1 1-3 1h0v-2h-1-3 0l1 2v2c-2-1-2-2-3-3h-2l1-1-1-1v-1l-1-1h-1-1c0-1 0-2-1-3l-1-3z" class="a"></path><path d="M358 283l1 1c0 1-1 2-1 3h0 2l1 2-2 2h0 2v1l-1 1h2c0 1-1 1-2 2h1 2l1 1v1h-1l-1 1h2v1c1 1 1 2 2 3l-1 1h1c0 1 0 1 1 2v1l-1 1 1 2h-1l-3-2h-1v1h0c1 0 1 0 2 1h-1 0l1 2-1 1c1 0 1 0 2-1l1 1v1l-1 1v2c0 1-1 1-1 2 1 1 2 3 3 4l-1 1h1c0 1 0 2 1 3 0 1 0 1 1 2-1 0-1 1-2 1v2l-15-34h0v-2-1h1v-1h-1v-1h3c1 0 0 0 1-1h-1c-1 1-2 0-3 0v-1c0-1 1-2 3-3h-1c-2 1-3 0-5 1v-1l1-1c2 0 3 0 4-1 2 0 1 0 2-1 1 0 1 0 2-1z" class="Z"></path><path d="M354 297c2 0 3 0 4 1l-1 1-2 1-1-1v-2z" class="V"></path><path d="M367 298c2 4 3 8 5 11l6 15 11 25c1 3 3 6 3 9l1 6v3l-1 1h1 1l1 1v1c-1 1-2 2-3 2v1c1 0 3-1 4-1l1 1c-2 2-3 1-5 2l1 1 1-1h0v1h-1l1 1c1-1 1 0 1-1l1-1h1v1l-3 3c-1 2-2 3-4 3 0 1 0 1 1 3h0l-1 1-18-43-5-12v-2c1 0 1-1 2-1-1-1-1-1-1-2-1-1-1-2-1-3h-1l1-1c-1-1-2-3-3-4 0-1 1-1 1-2v-2l1-1v-1l-1-1c-1 1-1 1-2 1l1-1-1-2h0 1c-1-1-1-1-2-1h0v-1h1l3 2h1l-1-2 1-1 2 2c0-3-1-4-2-6h0v-4z" class="X"></path><path d="M365 314l1-1v-1l-1-1c-1 1-1 1-2 1l1-1-1-2h0 1c-1-1-1-1-2-1h0v-1h1l3 2h1l-1-2 1-1 2 2v1l-2 1v1l1-1h2v1l-1 1v1h1 2v1h-2c1 1 1 1 2 1l-1 1c-1 0-3 0-5-1l-1-1z" class="a"></path><path d="M382 341l1 1-1 1 1 1h1l1 1c0 1 0 1-1 2h2v1l-2 1v1h1c1 0 2 0 2 1-1 0-2 0-3 1h0 0 4c-1 1-2 2-3 2h-1c2 0 3 0 4 1-1 1-1 1-2 1v-1h-5c0-1 1-2 2-3h-2l1-1h1l-2-2h-1 0 2l1 1 1-1c-1-1-2-2-3-2h-1v-1h2v-1c-2 0-2 0-3-1h1c0-1 1-2 2-2v-1z" class="g"></path><path d="M365 314l1 1v1c2 1 3 1 5 1h2v1l-1 1c-1 0-1-1-2-1h-2l1 1h1v1 1l-2-1c1 2 2 2 2 3s1 2 2 2l1 1v-1l-2-1 1-1c1 0 2 1 3 1 0 1 0 1-1 1 1 1 1 1 2 1v1h-3l1 1v1l1 1 1-1h2c-1 1-1 1-2 1l1 1h2v3h1v1h-1l-1 1h2l-1 1h-1l-1 1-1 1h1l1 2h0-1l-1 1h3v1h-2v1c-1-1-1-1-3-1h0-2l-5-12v-2c1 0 1-1 2-1-1-1-1-1-1-2-1-1-1-2-1-3h-1l1-1c-1-1-2-3-3-4 0-1 1-1 1-2v-2z" class="j"></path><path d="M375 333v1h-3l1-1 1 1v-1-1h-1v-1-1h-1v-1h2 0l1 1 1-1h2c-1 1-1 1-2 1 0 1 0 2-1 3h0z" class="a"></path><path d="M375 333h0c1-1 1-2 1-3l1 1h2v3h1v1h-1l-1 1c-1-1-2-1-2-1l-1-1h2v-1h-2z" class="g"></path><path d="M372 343h2 0c2 0 2 0 3 1v-1h2v-1h-3l1-1h1 0l-1-2h-1l1-1 1-1h1 0c1 1 1 1 2 1v1l-1 1c1 0 1 1 2 1v1c-1 0-2 1-2 2h-1c1 1 1 1 3 1v1h-2v1h1c1 0 2 1 3 2l-1 1-1-1h-2 0 1l2 2h-1l-1 1h2c-1 1-2 2-2 3h5v1h-3l-1 1h5l2-1c0 1 0 2 1 2l3 6v3l-1 1h1 1l1 1v1c-1 1-2 2-3 2v1c1 0 3-1 4-1l1 1c-2 2-3 1-5 2l1 1 1-1h0v1h-1l1 1c1-1 1 0 1-1l1-1h1v1l-3 3c-1 2-2 3-4 3 0 1 0 1 1 3h0l-1 1-18-43z" class="a"></path><path d="M392 373h-1c-1-1-1 0-1-1l-1-1h1l-1-1v-1h1c-1-2-2-2-2-3l-1 1-1-1c1-1 1-1 3-2h1s0-1 1-1c-1-1-2 0-3 0h-1c1-2 2-3 3-5l3 6v3l-1 1h1 1l1 1v1c-1 1-2 2-3 2v1z" class="V"></path><path d="M373 359l-12-27-15-34c-1-1-1-4-2-6h0c-3-4-5-9-6-14-7-15-13-33-11-50 1-1 1-4 2-5l1 1c-1 8-1 15 1 24 0 2 0 5 1 7s1 4 2 6v-1l-1-4c-1-4-3-14-1-18 0 6 1 13 3 18 0 5 2 9 4 13 2 3 3 6 4 9l9 19 15 34 5 12 18 43c3 6 5 13 9 19l2 5s0 1 1 2c0 2 2 6 3 8 1 4 3 8 5 11l9 22 10 25c1 3 3 7 4 10l1 1c1 2 2 5 3 7s2 3 3 5c2 4 6 7 8 10 2 2 4 3 6 5h1c-2 3-3 7-2 11s3 8 5 12c1 2 3 4 4 6l2 2c-1 2-1 4 0 6-6-7-12-13-18-19-3-2-5-4-8-5-5-2-7-5-12-5l-4-3h-1l-1-2c-1-2-3-2-3-5 1-2 1-4 2-6l2 2h1l-1-2c-2-1-5-7-5-9h-1c-1-2-3-4-5-6-1-2-2-5-3-7l-1-3-3-3 3-2c3 0 8-1 11 1h1c2 1 3 0 4-1l3 3-2-7c-1-4-4-8-5-12-3-5-5-11-7-16l-16-38-22-49z" class="d"></path><path d="M419 508l2 2h1c3 2 5 4 6 8v3c-1 1-2 1-3 1-1-1-2-1-3-1h-1l-1-2c-1-2-3-2-3-5 1-2 1-4 2-6z" class="E"></path><path d="M377 360c1 1 1 2 2 3l5 11 35 83c7 16 12 34 22 49h-1c-2-1-3-3-4-5h-1 0-1c-2-5-4-10-6-14-1-2-2-5-3-6l-2-7c-1-4-4-8-5-12 2 2 3 5 4 8v-1-1c0-1-1-1-1-2s-1-1-1-2c0-2 0-1-1-2v-2c-2-8-7-16-10-24l-20-47-8-17c-1-4-3-8-4-12z" class="K"></path><path d="M418 462c2 2 3 5 4 8v-1-1c0-1-1-1-1-2s-1-1-1-2c0-2 0-1-1-2v-2c3 5 4 10 6 15l10 26h-1c-2-5-4-10-6-14-1-2-2-5-3-6l-2-7c-1-4-4-8-5-12z" class="U"></path><path d="M373 359l-12-27-15-34c-1-1-1-4-2-6h0c-3-4-5-9-6-14-7-15-13-33-11-50 1-1 1-4 2-5l1 1c-1 8-1 15 1 24 0 2 0 5 1 7s1 4 2 6c0 1 0 2 1 3v1c0 1 1 2 2 3 0 3 2 6 3 8l12 28c3 7 7 14 10 21l4 10 3 9 2 3v1l1 3c1 1 1 0 1 1 1 2 2 5 3 7l1 1c1 4 3 8 4 12l8 17 20 47c3 8 8 16 10 24v2c1 1 1 0 1 2 0 1 1 1 1 2s1 1 1 2v1 1c-1-3-2-6-4-8-3-5-5-11-7-16l-16-38-22-49z" class="f"></path><path d="M422 478l3 3c1 1 2 4 3 6 2 4 4 9 6 14h1 0 1c1 2 2 4 4 5h1l11 11v5l-2-1h0v1l1 2c1 3 2 5 3 8 1 4 3 9 5 12l-3-3c-3-4-7-7-11-10-5-4-10-6-14-9-2-1-2-2-2-4s-1-3-2-5-4-5-6-5c-2-1-5-7-5-9h-1c-1-2-3-4-5-6-1-2-2-5-3-7l-1-3-3-3 3-2c3 0 8-1 11 1h1c2 1 3 0 4-1z" class="a"></path><path d="M407 482c1-2 4-2 6-2 2 2 2 4 4 6h-2s-1-1-1-2l-2-1-1-1h-4 0z" class="g"></path><path d="M407 482h0 4l1 1c0 2 1 3 1 5 1 2 1 4 3 6v1l-1-1c-1 0-1 1-1 2h0c1 1 2 2 2 3h-1c-1-2-3-4-5-6-1-2-2-5-3-7l-1-3 1-1z" class="X"></path><path d="M435 501h1c1 2 2 4 4 5h1l11 11v5l-2-1c0-1-2-3-3-4-3-3-5-6-8-9-1-2-2-5-4-7z" class="D"></path><defs><linearGradient id="At" x1="450.911" y1="641.317" x2="496.589" y2="593.683" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#7f7e7d"></stop></linearGradient></defs><path fill="url(#At)" d="M426 524c5 0 7 3 12 5 3 1 5 3 8 5 6 6 12 12 18 19l2 2c1 2-1 0 0 2l1 1c5 6 9 14 13 22 5 7 9 14 12 22l14 31c1 3 4 8 5 12 1 5 0 13 0 18l1 33-1 4h-1c-2-4-5-7-8-12l-9-25-5-17-8-21c-2-3-3-7-5-11-5-12-12-27-22-36-3-3-7-5-10-8l-1-1h0c-1-2-1-4-1-5l-1-2v-2l-1-5h0c-1-2-1-2-3-3-1-1-2-2-3-2 0-2 0-2-1-4 2 0 3-1 4-3v-4c-1-7-4-11-10-15z"></path><path d="M441 564c1 0 2 0 3 1s1 1 1 2c-1 0-2 1-3 2-1-2-1-4-1-5z" class="c"></path><path d="M436 543c0 3 1 7 0 9-1-1-2-2-3-2 0-2 0-2-1-4 2 0 3-1 4-3z" class="S"></path><path d="M442 569c1-1 2-2 3-2h1l3 3c11 6 17 18 22 29 2 3 5 7 5 10v1c-1-2-2-5-3-7-1 0-1 0-2 1 2 3 5 7 4 10-5-12-12-27-22-36-3-3-7-5-10-8l-1-1h0z" class="X"></path><defs><linearGradient id="Au" x1="458.241" y1="566.913" x2="467.206" y2="560.11" xlink:href="#B"><stop offset="0" stop-color="#7d7b7b"></stop><stop offset="1" stop-color="#989796"></stop></linearGradient></defs><path fill="url(#Au)" d="M426 524c5 0 7 3 12 5 3 1 5 3 8 5 6 6 12 12 18 19l2 2c1 2-1 0 0 2l1 1c5 6 9 14 13 22 5 7 9 14 12 22l-6-3c0-1-1-1-1-2-2-1-3-2-4-4v-1l-1 1v1h-1-1c-1-1-4-3-4-4-1-2-2-5-3-6-1-2-2-3-3-4v-1-1c-1-1-1-2-2-3-1-2-2-2-3-4 0-1 0-1-1-1l-14-17v1c-2-3-4-5-5-8-1-2-3-3-4-5s1-3-1-4l-1 1v1h-1c-1-7-4-11-10-15z"></path><path d="M100 163c0-1 0-1-1-2-2-3-3-5-4-8l2-1c2 0 9-1 10 0h332c1 1 1 1 1 2 0 5-8 12-12 15l-5 3c-1 1-2 2-3 2l-1-2-1 1h0-17c-7-1-14-2-20-1l-1 1 1 1c-1 2-3 4-5 5l-1 1c-1 0-2 1-3 1v-1h-2l-6 3-3 1c-7 5-16 12-20 20-2 2-3 5-4 7-4 9-5 18-5 27-2 4 0 14 1 18l1 4v1c-1-2-1-4-2-6s-1-5-1-7c-2-9-2-16-1-24l-1-1c-1 1-1 4-2 5-2 17 4 35 11 50 1 5 3 10 6 14h0c1 2 1 5 2 6l15 34 12 27v3h0c1 1 1 3 2 4v1l-4 4c-2 4-6 6-9 9h0l-2-5h-1 0c-1-1-1-3-2-5-2-4-4-9-6-13l-14-31c-8-17-15-35-24-51v-1c-3-8-8-15-12-22-3-6-5-12-8-18-6-15-15-28-29-37-1 0-2 1-2 1l-6-3c-4-2-8-4-12-5l-2 1s-1 0-2-1h-2v-1h-2l-3-1c-1 0-2 0-4-1s-5-1-7-1c-3-1-7-2-10-2s-7 1-10 0h-9-2c-1 1-4 0-6 0h-11c-3 1-5 0-8 1-3 0-7 1-10 1 0-1-1-1-1-1-1 1-3 0-4 0-1-2-4-4-6-5-3-2-6-2-7-5v-2-1c-4 1-8 1-12 1s-9 1-13-1c-2-1-4-2-5-3-3-2-4-4-7-6h-1z" class="f"></path><path d="M412 169h1c7-1 13-8 19-12 2-1 4-3 7-4 0 3-3 6-4 8l-6 6c-1 1-6 3-6 5-1 1-2 2-3 2l-1-2h-1c-1-2-3-2-6-3z" class="G"></path><path d="M352 169h60c3 1 5 1 6 3h1l-1 1h0-17c-7-1-14-2-20-1l-1 1 1 1c-1 2-3 4-5 5l-1 1c-1 0-2 1-3 1v-1h-2l-6 3-3 1h-1-1-1v-1c1-1 2-2 2-3l5-2c0-2 0-3 1-4h1v-2l5-1c-6-1-13 0-19 0 1-1 2-1 3-1l1 1v-1c-2 0-3 0-5-1z" class="H"></path><path d="M375 171h1c1 0 2 0 3 1 0 2-3 4-4 5h-1c-1 1-2 2-4 3l-6 3-3 1h-1-1-1v-1c1-1 2-2 2-3l5-2c0-2 0-3 1-4h1v-2l5-1h3z" class="B"></path><path d="M375 171l1 1c-1 1-3 1-4 2-2 1-3 3-5 4l-1-1v-3h1v-2l5-1h3z" class="f"></path><path d="M186 169h120 35c3 0 8-1 11 0 2 1 3 1 5 1v1l-1-1c-1 0-2 0-3 1 6 0 13-1 19 0l-5 1H238h-6-11-11c4-1 7-1 11-1h4c2 0 5 0 7-1h0l-4-1h-12-21c-2 0-7 1-9 0z" class="I"></path><defs><linearGradient id="Av" x1="144.708" y1="192.226" x2="181.168" y2="136.544" xlink:href="#B"><stop offset="0" stop-color="#1e1c1d"></stop><stop offset="1" stop-color="#414041"></stop></linearGradient></defs><path fill="url(#Av)" d="M100 163c0-1 0-1-1-2-2-3-3-5-4-8l2-1c2 0 9-1 10 0H97c8 5 15 13 23 17 2 1 7 0 9 0h20 37c2 1 7 0 9 0h21 12l4 1h0c-2 1-5 1-7 1h-4c-4 0-7 0-11 1h-13c-5-1-10 0-15 0h-23c-7 0-14-1-21 0-4 1-8 1-12 1s-9 1-13-1c-2-1-4-2-5-3-3-2-4-4-7-6h-1z"></path><path d="M238 172h129v2h-1c-1 1-1 2-1 4l-5 2c0 1-1 2-2 3v1h1 1 1c-7 5-16 12-20 20-2 2-3 5-4 7-4 9-5 18-5 27-2 4 0 14 1 18l1 4v1c-1-2-1-4-2-6s-1-5-1-7c-2-9-2-16-1-24h0c0-2 1-4 1-5s-1-2 0-3h0v-1c1-2 0-1 1-2 0-1 0-2 1-4-2-1-3-3-4-5-3-3-8-3-12-4l-3 3-1-1-1 1c-2 0-5 0-7-2-4-3-4-5-4-9l-1 2h0v-2c1-2 0 0 1-1v-3l1-1c-3 0-5-1-7-2-1-1-1-1-2-1l-6-3c-2-1-8-4-9-6l-1-1c-1-1-1 0-1-1-2-2-32 1-37-1h-1z" class="b"></path><path d="M315 198c0-2 1-2 1-3h-1v-4c0-1-2-2-2-2-1-1 0-1-1-2 0-1-1-2-2-2h-1v-1h2c1 0 3 1 4 1 3 4 3 6 3 10l-3 3z" class="Q"></path><path d="M301 192c0-2 1-3 2-5v1c-1 2-1 4 0 6s3 4 5 5h4c1-1 2-1 3-1l3-3c-1 4-2 5-5 7l-1 1c-2 0-5 0-7-2-4-3-4-5-4-9z" class="M"></path><defs><linearGradient id="Aw" x1="358.38" y1="183.963" x2="343.974" y2="192.625" xlink:href="#B"><stop offset="0" stop-color="#353636"></stop><stop offset="1" stop-color="#676566"></stop></linearGradient></defs><path fill="url(#Aw)" d="M360 180c0 1-1 2-2 3v1h1 1c-2 2-6 4-8 6-4 3-6 6-10 9-1 0-2 2-3 3h-1c5-9 13-18 22-22z"></path><path d="M360 184h1c-7 5-16 12-20 20-2 2-3 5-4 7-4 9-5 18-5 27-2 4 0 14 1 18l1 4v1c-1-2-1-4-2-6s-1-5-1-7c-2-9-2-16-1-24h0c0-2 1-4 1-5 1-6 3-12 7-17h1c1-1 2-3 3-3 4-3 6-6 10-9 2-2 6-4 8-6z" class="Z"></path><path d="M210 172h11 11 6 1c5 2 35-1 37 1 0 1 0 0 1 1l1 1c1 2 7 5 9 6l6 3c1 0 1 0 2 1 2 1 4 2 7 2l-1 1v3c-1 1 0-1-1 1v2h0l1-2c0 4 0 6 4 9 2 2 5 2 7 2l1-1 1 1 3-3c4 1 9 1 12 4 1 2 2 4 4 5-1 2-1 3-1 4-1 1 0 0-1 2v1h0c-1 1 0 2 0 3s-1 3-1 5h0l-1-1c-1 1-1 4-2 5-2 17 4 35 11 50 1 5 3 10 6 14h0c1 2 1 5 2 6l15 34 12 27v3h0c1 1 1 3 2 4v1l-4 4c-2 4-6 6-9 9h0l-2-5h-1 0c-1-1-1-3-2-5-2-4-4-9-6-13l-14-31c-8-17-15-35-24-51v-1c-3-8-8-15-12-22-3-6-5-12-8-18-6-15-15-28-29-37-1 0-2 1-2 1l-6-3c-4-2-8-4-12-5l-2 1s-1 0-2-1h-2v-1h-2l-3-1c-1 0-2 0-4-1s-5-1-7-1c-3-1-7-2-10-2s-7 1-10 0h-9-2c-1 1-4 0-6 0h-11c-3 1-5 0-8 1-3 0-7 1-10 1 0-1-1-1-1-1-1 1-3 0-4 0-1-2-4-4-6-5-3-2-6-2-7-5v-2-1c7-1 14 0 21 0h23c5 0 10-1 15 0h13z" class="Q"></path><path d="M339 294h2l1 1v1l-1 1c-1 0-1 0-2-1v-2z" class="B"></path><path d="M138 172c7-1 14 0 21 0l-4 1h2 1c-3 1-7 1-11 1h-2l-1 1-6-2v-1z" class="U"></path><path d="M210 172h11c-3 1-6 0-9 2 0 2 0 3-1 5v-1-4-1l-1 1c-5-1-16 1-19-2h6 13z" class="b"></path><defs><linearGradient id="Ax" x1="197.245" y1="171.2" x2="198.755" y2="198.3" xlink:href="#B"><stop offset="0" stop-color="#0d0b0d"></stop><stop offset="1" stop-color="#393937"></stop></linearGradient></defs><path fill="url(#Ax)" d="M138 173l6 2c2 1 4 2 5 3 1 0 3 3 4 3h4l14-1c13-1 28-2 41-1 15 2 28 6 41 12 3 2 8 4 11 6-1 0-2 1-2 1l-6-3c-4-2-8-4-12-5l-2 1s-1 0-2-1h-2v-1h-2l-3-1c-1 0-2 0-4-1s-5-1-7-1c-3-1-7-2-10-2s-7 1-10 0h-9-2c-1 1-4 0-6 0h-11c-3 1-5 0-8 1-3 0-7 1-10 1 0-1-1-1-1-1-1 1-3 0-4 0-1-2-4-4-6-5-3-2-6-2-7-5v-2z"></path></svg>