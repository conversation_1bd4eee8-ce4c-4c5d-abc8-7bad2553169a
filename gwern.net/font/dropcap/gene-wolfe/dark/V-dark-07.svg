<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 12 838 972"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#2d2d2d}.C{fill:#434242}.D{fill:#7f7f7f}.E{fill:#fdfdfd}.F{fill:#535353}.G{fill:#363636}.H{fill:#4b4a4a}.I{fill:#171616}.J{fill:#1c1b1b}.K{fill:#696869}.L{fill:#d4d3d3}.M{fill:#6e6d6e}.N{fill:#8a8989}.O{fill:#5b5a5b}.P{fill:#b3b2b3}.Q{fill:#a9a8a9}.R{fill:#151515}.S{fill:#bebdbd}.T{fill:#3c3b3c}.U{fill:#d4d3d4}.V{fill:#dbdadb}.W{fill:#f0eff0}.X{fill:#605f5f}.Y{fill:#939293}.Z{fill:#9d9c9d}.a{fill:#262525}</style><path d="M847 210h1l1 3v1l-2-1v-1-2z" class="V"></path><path d="M605 548l2-2c1 2 1 2 1 4h-1l-2-2z" class="O"></path><path d="M601 560c1-1 2-2 3-2 1 2 1 2 0 3l-1 1-2-2z" class="X"></path><path d="M602 653l2-1-1 7c0-2-1-2-1-3-1-1 0-2 0-3z" class="B"></path><path d="M615 330c1 1 2 2 2 4 0 1 0 2-1 3-1-2-1-5-1-7z" class="R"></path><path d="M550 489c1-2 2-3 3-5 0 3-1 6-1 9l-1-3-1-1z" class="K"></path><path d="M685 576v-4c0-1 1-3 2-3l1 2-1 1-1 1c-1 1 0 2-1 3z" class="J"></path><path d="M452 322c1 1 3 0 5 0-1 1-1 2-2 2-1 1-3 1-4 0l1-2z" class="K"></path><path d="M547 668c0-1-1-1-2-2v-1c2-1 3 0 5 1l-3 2z" class="V"></path><path d="M742 245l2 2-1 5c-1-1-2-3-2-4l1-3z" class="S"></path><path d="M521 751c1-2 4-3 6-4l-5 6-1-2z" class="I"></path><path d="M514 487c2 1 2 1 4 1l-1 3c-2-1-3-1-4-2l1-2z" class="K"></path><path d="M773 166c-1-1-3-2-3-4h4v1c0 1 1 1 0 2l-1 1z" class="G"></path><path d="M513 489c1 1 2 1 4 2l-2 3-3-3 1-2z" class="X"></path><path d="M786 333c0-1-1-1-2-1 0-2 0-2 1-4h2c0 2 0 3-1 5z" class="J"></path><path d="M294 186l7-3-2 2v1l2 1h-2-2c-2 0-2 0-3-1z" class="Y"></path><path d="M383 366l1-12v-1 9 1c0 1 1 3 1 4l-1-1h-1z" class="B"></path><path d="M575 629h2 0c0 1 0 1-1 1h0c-1 1-1 1-2 1h-1-5-3l4-1c2 0 3 0 5-1h1z" class="I"></path><path d="M485 408c0 1 1 3 2 4h1v3c-2-1-3-2-4-3l1-4z" class="D"></path><path d="M646 396l2 3 1 1v-2h1v3h-5-1c2-2 2-3 2-5z" class="H"></path><path d="M334 123l-5-4h-1v-1c2 0 4 0 6 1v4z" class="T"></path><path d="M278 485l2 5-2 2c-1-2-1-3-1-4l-2-1 3-2zm-79-185h5v5c-2 0-3 0-4-1v-1l1 1c1-1 1-1 1-3-1 0-2 0-3-1z" class="P"></path><path d="M647 393c1 1 2 3 3 5h-1v2l-1-1-2-3v-2l1-1z" class="C"></path><path d="M482 387c0 2 0 3-1 5-2 0-2 0-4-1v-2c2 0 3 0 4-1l1-1z" class="O"></path><path d="M450 642l1 10c-1-1-4-3-4-4l-1-1v-1l1 1 1 1v-1c1 0 1 1 2 1-1-2-1-4 0-6z" class="H"></path><path d="M605 548l-1-2c-2-1-2-3-2-5l5 5-2 2z" class="F"></path><path d="M324 159c-1-2-3-3-4-5l1-1c2 0 4 3 6 4h-3v1 1z" class="H"></path><path d="M567 605l-2-8c1 1 3 2 4 4 0 1 0 2-1 3l-1 1z" class="G"></path><path d="M386 764l4 2-2 7c0-2 0-3 1-5l-2-1c-1 0-1 0-2 2l1-5z" class="L"></path><path d="M629 286l1 3c-1 1-1 3-2 4h0l-2-3 1-1h0c1-1 1-2 2-3z" class="F"></path><path d="M521 861c2 2 3 3 3 6h0c-2-1-3-2-4-3l1-3z" class="C"></path><path d="M541 514c1 4 0 7-1 11l-1-6c1-2 1-3 2-5z" class="G"></path><path d="M760 469l4-4 3 2c-2 1-4 2-5 3l-2-1z" class="Q"></path><path d="M199 247l3 1h0l3 3v1c-2 0-3 0-5-1l1-1-2-3z" class="O"></path><path d="M387 760c2 1 3 2 4 3v1l-1 2-4-2 1-2v-2z" class="S"></path><path d="M387 760c2 1 3 2 4 3v1c-1-1-3-1-4-2v-2z" class="P"></path><path d="M238 473c1 0 4-1 5-1 1 1 1 1 1 2s-1 2-1 2l-2 1c-1-1-1-2-1-3l-2-1zm70 30c1 1 2 1 1 3v1l-2 1-3-4 4-1z" class="T"></path><path d="M620 323c0 1 1 2 1 3l-1 3-5-5h3c0-1 1-1 2-1z" class="B"></path><path d="M688 565c0-3 2-6 3-9 1 1 1 3 2 5-2 1-4 2-5 4zm115-270c-1-1-3-1-4-2 0 0 0-1 1-1 1-2 2-2 3-2 1 1 1 2 0 4v1z" class="J"></path><path d="M472 364c0 1 1 2 0 3 0 1 1 1 1 1 0 2-1 3-1 4h-1c-2-1-1 0-2-1 1-3 2-5 3-7z" class="Q"></path><path d="M830 219l2 2-6 7 4-9z" class="T"></path><path d="M473 371l2-4 3 1-2 5-3-2z" class="P"></path><path d="M634 535h1c1 2 1 6 1 8l-2-2c-1-2-1-3 0-6z" class="K"></path><path d="M622 457c1-3 1-6 3-9h0c0 4-1 9-2 14v-1c-1-1-1-2-1-4zM489 601c0-2-3-10-2-12 0 2 0 3 1 5v1l1 1h0c2 1 2 2 3 4l-1 2c0-1-1-1-2-1z" class="G"></path><path d="M575 407l2 3c0 2 0 2-2 4l-2-4c2-1 1-1 2-3z" class="H"></path><path d="M231 369l3-2 3 3-4 3c0-2 0-3-2-4z" class="Q"></path><path d="M440 304l2 2-4 6v-1c0-3 0-5 2-7z" class="T"></path><path d="M345 121c-4-2-7-4-10-6 2 0 5 0 7 2l1 1c1 1 2 2 2 3z" class="O"></path><path d="M526 848c1 1 3 2 4 4 0 1 0 2-1 3-1 0-1 0-2-1 1 0 0-1 0-1-1-2-1-3-1-5z" class="R"></path><path d="M522 846l4 2c0 2 0 3 1 5 0 0 1 1 0 1s-2-1-4-2c2 0 2 0 3 1-2-2-3-4-4-7z" class="a"></path><path d="M757 467c1-1 3-3 5-3l2 1-4 4-3-2zm-284-96l3 2 1 4h-2l-2-1v-4-1z" class="Z"></path><path d="M285 144c-1 2-1 3-1 5l1-1c1-1 2-1 4-2-2 2-4 4-7 5l-2-1 4-6h1z" class="V"></path><path d="M231 369c2 1 2 2 2 4l-4 3 1-3v1-2c-1-1-2 0-3 0 1-1 3-2 4-3z" class="S"></path><path d="M507 489l3 3-3 8c-1-4-1-8 0-11z" class="B"></path><path d="M586 409c1 2-1 5-2 7l-2-4c1-2 2-2 4-3z" class="a"></path><path d="M501 428c1-2 2-3 3-5 0 2 0 4 1 5l-2 5v-2h-1l-1-3z" class="P"></path><path d="M411 793c1 0 3 0 3-1 1 0 1-1 2-1v1h1c-1 1-1 4-2 4h0-4v-3z" class="K"></path><path d="M483 416l1-4c1 1 2 2 4 3l-1 4-4-3z" class="M"></path><path d="M447 290l1 1c1 0 2 0 3 1v1l-2 1-3 1-1-1 1-2 1-2z" class="Y"></path><path d="M446 292c1 2 2 1 5 1l-2 1-3 1-1-1 1-2z" class="N"></path><path d="M585 635h0c2 0 2 1 3 2s1 3 1 5c-1-1-3-2-3-3-1-1-1-3-1-4z" class="O"></path><path d="M696 119h0c0-2 1-3 2-5l1 1h-1v1h0l1-1v1 3l-1-1v10c-2-3 0-6-2-9z" class="I"></path><path d="M304 504l3 4-7 3c1-1 1-2 2-2h1c0-1-1-2-1-2-1 0-3 1-4 1 2-2 4-3 6-4z" class="F"></path><path d="M379 466c2 0 4-1 6 0l2 2h1l1 1v1c-3-1-9-2-10-4z" class="J"></path><path d="M802 364l1-2c0-1 0-2 2-2 1 0 3 1 5 2h-3c-1 1-1 1-1 2v1 1l-4-2z" class="U"></path><path d="M474 633c1-2 0-5 1-7l1 16h0c-1-1-1-3-1-4-1-2-1-3-1-5z" class="V"></path><path d="M667 361c1-1 1-2 3-2 2 1 6 3 8 5-4 0-7-3-11-3z" class="G"></path><path d="M791 376l4 1c-2 2-2 3-3 6l-3-2v-1c1 0 1-1 1-2l1-2z" class="Q"></path><path d="M626 746c1-1 2-2 3-2l2 4c-2 0-2 1-3 2l-1 1-1-5z" class="O"></path><path d="M577 669l6 5v1l-3-1c-2-1-3-2-5-3l2-2z" class="M"></path><path d="M490 420c1 3 2 5 3 7l-2 4-1-1c1-2 0-3-1-5 1-2 1-3 1-5z" class="C"></path><path d="M711 565l1-1 4-1c0 2-1 3 0 4v1h-3c-1-1-1-1-2-3z" class="P"></path><path d="M549 654c-2-2-2-5-3-8 2 1 5 6 6 8v1c-1 1-1 0-1 1 0-1-1-2-2-2z" class="I"></path><path d="M402 740c2-3 3-5 5-7h0 0c-1 1-1 1-1 2l-1 1v2h-1v1l2 2c1-1 1-2 2-2v-2l2-2h0c0 1-3 7-4 9-1-2-2-3-4-4z" class="B"></path><path d="M441 313c1 1 2 1 3 2l-3 8c-1-4-1-7 0-10z" class="I"></path><path d="M504 423l2-5 2 2-3 8c-1-1-1-3-1-5z" class="U"></path><path d="M634 767c1-2 3-2 4-3l3 3-2 2v1 1c-2-1-3-3-5-4z" class="V"></path><path d="M428 812l4 1-1 6c-1-2-2-2-4-3 0-1 1-3 1-4z" class="C"></path><path d="M748 495c2 1 4 3 6 5-1 0-2 0-2-1-1-1-2-1-3-1l-1 1 3 3 1 1c-2-1-4-3-6-4 1-1 1-3 2-4z" class="S"></path><path d="M470 385c1 2 0 4 0 5s2 3 3 4l-2 5c-1-3-3-6-2-8s1-4 1-6z" class="G"></path><path d="M307 528l3-2c2 1 4 2 6 4l-5 2c0-2-2-3-4-4z" class="F"></path><path d="M206 396l3 2v1l-4 1h0c-2-2-3-2-6-3 2 0 5 0 7-1z" class="V"></path><path d="M568 349l5 4v2c-1 2-1 2-2 3l-6-6c2 0 3 1 4 2h0l2-1-3-3v-1z" class="L"></path><path d="M813 273l-6 1 1-2h1c1-3 3-3 5-4-1 2-1 3-1 5z" class="J"></path><path d="M304 529l3-1c2 1 4 2 4 4l-5 2c0-1-1-3-2-5z" class="K"></path><path d="M246 320c2 1 5 1 6 3-1 0-3 1-4 2-2-2-4-2-6-3 0 0 3-2 4-2z" class="a"></path><path d="M234 468c2 0 4-1 6-1 1 1 1 1 1 3 0 1-1 1-3 2h0l-1-1c-2-1-2-1-3-3zm396-179c1 3 2 5 2 9h-1c-1-1-2-3-3-5h0c1-1 1-3 2-4z" class="C"></path><path d="M718 247c0-1 1-2 2-4l1 14c-1-1-1-2-1-4h0l-1-1-1-1v-4z" class="R"></path><path d="M205 295l8-3-2 2c2 1 4 0 5 0s2 1 3 2c-1 0-3 1-4 1-3-3-6 0-10-2z" class="D"></path><path d="M795 377c1 1 2 1 3 2l-3 5-3-1c1-3 1-4 3-6z" class="P"></path><path d="M583 674c5 2 9 6 12 9l-15-9 3 1v-1z" class="K"></path><path d="M481 422c1 1 3 2 4 3l-2 6c-2-3-3-6-2-9z" class="B"></path><path d="M231 361l5-3c1 1 1 1 2 3-1 0-1 1-1 2l-3 2-3-4z" class="Y"></path><path d="M774 163c2 0 2 0 4 1v1c1 1 1 1 1 2l1 1c0 1-1 1-1 1-2 0-4-2-5-3h-1l1-1c1-1 0-1 0-2z" class="H"></path><path d="M774 163c2 0 2 0 4 1l-1 2h-3 0-1l1-1c1-1 0-1 0-2z" class="T"></path><path d="M624 524c1 3 2 7 3 10 0 2 2 4 2 6-1 0-1-1-2-1-4-4-3-11-3-15z" class="F"></path><path d="M762 127c1 0 1-1 2 0 3 0 6 0 8 1l-5 4-5-5z" class="Y"></path><path d="M707 389c2 0 3-1 4 0l-3 7-3-3 2-4z" class="X"></path><path d="M789 360l4 2c-1 1-1 3-1 5-2-1-3-2-5-2l2-5z" class="N"></path><path d="M512 841c-3-3-5-7-7-11l10 9c-3 0-4-3-6-4l4 6h-1z" class="C"></path><path d="M625 747c0 1 1 3 1 5l-3 5-1-5c1-2 1-3 3-5z" class="F"></path><path d="M690 592c1-2 2-3 4-4l1 4c-1 1-3 3-4 5l-1-5z" class="K"></path><path d="M537 822c2 2 3 2 3 5l-1 3v1c-2-1-4-2-5-3l1-1c2-1 2-3 2-5z" class="I"></path><path d="M784 210l1 1c2-1 3-1 4-2 0 2-1 4-2 6v1c-1 0-1 0-2-1h1c-1-2-3-3-4-4h0l2-1z" class="G"></path><path d="M539 656l3-11 1 3h-1v3-1c0-1 1-1 1-1l1-1v-1c0 1 1 2 0 3 0 1-1 2-1 3l-2 2h-1 0c-1 1 0 1-1 1z" class="I"></path><path d="M710 384c2-1 1-2 3-3v1 1 1h0c0 2-1 3-2 5-1-1-2 0-4 0l3-5z" class="C"></path><path d="M285 209c0-1 0-2 1-2 1-1 6-4 7-4h1c-1 2-1 3-3 4s-4 2-6 2z" class="B"></path><path d="M751 496v-2-2-1c1 1 1 1 2 1s3 2 4 2c-2 1-2 3-3 4l-3-2z" class="S"></path><path d="M696 119c2 3 0 6 2 9 1 2 1 7 1 9-1-2-2-4-2-6-1-4-1-8-1-12z" class="B"></path><path d="M444 591c1 1 1 2 2 3l2 8v3c-1 1-1 1 0 2h-1 0l-3-16z" class="K"></path><path d="M645 522v1 3h0c1 1 2 3 2 4s1 2 1 3 1 1 0 3l-2-1v-2l-2-1c0-3 0-7 1-10z" class="Z"></path><path d="M246 320c2-1 4-2 7-2 1 0 1 1 2 2l-3 3h0c-1-2-4-2-6-3zm58 150l5-4c0 1 1 3 1 4l-3 3h0c-1 0-2-1-3-1v-2z" class="R"></path><path d="M567 449c1 2 1 3 1 6l-2 5-3-5c3-2 3-4 4-6z" class="G"></path><path d="M482 360v1l-3 3h1 0l2-1c0-1 1-1 1-1h1c-2 2-5 4-6 6l-3-1c2-3 4-5 7-7z" class="L"></path><path d="M233 440l5-4h1l1 3c-1 3-1 3-3 4h-1v-1h0v-2h-3z" class="V"></path><path d="M662 431l1 1c3 0 6 0 9 1l-12 3c1-2 1-3 2-5z" class="C"></path><path d="M822 297c2 1 4 1 7 1 0 2 0 3 2 4v1l-6-1-1-1-2-4z" class="P"></path><path d="M601 560c0-1-1-2-1-2-2-3-3-5-3-8 2 1 5 6 7 8-1 0-2 1-3 2z" class="H"></path><path d="M286 190c2-1 5-3 8-4 1 1 1 1 3 1h2c-3 2-7 4-10 4-1 0-2 0-3-1z" class="N"></path><path d="M555 652c-4-3-6-8-7-13 2 3 5 6 7 9v4z" class="O"></path><path d="M359 445c1 0 3-1 4-1-1 3-3 6-6 8l-2-1 4-6z" class="M"></path><path d="M839 218l1 5c-2 2-3 4-5 5v-6l4-4z" class="X"></path><path d="M743 491l5 4c-1 1-1 3-2 4l-5-4c1-1 1-2 2-4z" class="P"></path><path d="M284 144c-1-1-2 0-3 0l1-1-1-1-1 1-1-1c3-2 5-5 8-7-1 2-1 4-2 6v3h-1zm535 199c-1-1-1-1-2-1 0-2 0-2 1-3 1 1 1 1 3 1s5 1 7 3h-1c-2 0-2 0-3-1l-2-1-1 2 1 1c2 1 3 1 4 3l-7-4z" class="S"></path><path d="M745 256c2 0 3 0 5 1l1 1c1 0 2 0 3 1l-6 3-3-6z" class="D"></path><path d="M588 275c0-7 0-14 1-21 0 8 0 15 2 23h-1l-2-2z" class="W"></path><path d="M240 439l3 8c-1 1-2 1-3 2l-3-6c2-1 2-1 3-4z" class="P"></path><path d="M389 685l5-5c1 0 1 0 1 2s-1 4-2 6c-1-1-2-1-3-2 0-1 0-1-1-1z" class="I"></path><path d="M620 745c-1-3-2-7-1-9 0-2 0-2 1-3l3 6-1 2h-1l-1 4z" class="a"></path><path d="M598 203c1 1 2 2 3 2l1-1c0 2-1 4-1 6h-4l1-7z" class="V"></path><path d="M351 217v1l-2 2c-2 2-4 4-7 4l-1-1v-1c3-2 6-4 10-5z" class="F"></path><path d="M196 290c1 2 2 3 3 4v2l3 3h-1c-3 0-5-2-7-4h1c1-2 0-3 0-5l1 1v-1z" class="S"></path><path d="M212 334l9-7 3 4c-3 2-6 5-9 6 0-1 1-2 2-3h1l2-2c0-1-1-1-1-2-2 2-4 4-6 4h-1z" class="U"></path><path d="M769 160c0-1-1-2-1-3h0c-1-2-1-1-1-2l1-2 2 1v1l1 1c2 1 4 2 5 4h-7z" class="O"></path><path d="M745 253l1 1v1h3 4l1 1c1 0 2-1 3-1 1-1 2-1 3-1-2 2-4 4-6 5-1-1-2-1-3-1l-1-1c-2-1-3-1-5-1-1-1-1-2 0-3z" class="Y"></path><path d="M791 384c2 1 5 2 6 5h-1l-1-1c-1 1-2 2-2 3 1 1 1 1 1 3h0c-2-2-2-3-4-4 0-2 0-4 1-6z" class="S"></path><path d="M579 717h4 2 2c-1 1-2 5-3 6h0-1 0c-1 1-1 1-3 0h-4c1-2 4 0 6-1 0-2 0-2 1-3v-1h0-2l-2-1z" class="I"></path><path d="M223 333c2 1 2 2 3 4l-8 5h0c1-1 1-2 2-3h0l2-1v-1l-1-1c-1 0-2 2-3 2h-2c2-2 5-3 7-5z" class="U"></path><path d="M561 640c-4-3-6-6-9-10l3 1a30.44 30.44 0 0 1 8 8c-1 0-2 1-2 1z" class="F"></path><path d="M419 574h0c-1 0-1 1-3 1-1-1-1-2-1-3h0v-2-1c1-2 2-3 3-4 0 3 1 6 1 9z" class="R"></path><path d="M543 352c0-7 0-13-4-18l-3-3h1c5 5 8 9 8 16l-1 7-1-2z" class="E"></path><path d="M717 563l8-4v1c-1 1-5 2-6 3v3l1-1h1 0c1-1 0 0 2-1h1l1-1h1 0 2l-12 5v-1c-1-1 0-2 0-4h1z" class="L"></path><path d="M428 836l2-2c1 0 2 0 3 1l-2 7c-1-1-3-1-4-1 0-1 1-3 1-5z" class="N"></path><path d="M464 730l-4 5c-2-1-3-5-4-7l1-1 1 1h0c2 0 4 1 6 2z" class="a"></path><path d="M275 487l2 1c0 1 0 2 1 4l-6 4-1-4h-1l5-5z" class="U"></path><path d="M545 473l4 4c0 1 0 2-1 3v1c0 1 0 1-1 2-1-4-4-6-7-8h4l1-2z" class="S"></path><path d="M310 518l4 4c-2 1-4 2-6 2-1-1-2-3-3-4 1-1 3-2 5-2z" class="H"></path><path d="M342 175c3-1 9-1 11 0 1 1 1 2 0 3s-5 2-7 2l1-4 3-1c-1-1-6 0-8 0z" class="B"></path><path d="M765 263v-1c2-2 4-3 5-4-2 5-5 9-8 14-1 1-2 3-3 4h-1c2-4 5-8 7-12v-1z" class="I"></path><path d="M648 712h3 1l1-1c1-1 2-2 2-3l3 6h-6l-1 2c-1-1-2-3-3-4z" class="J"></path><path d="M447 290l2-6c2 1 4 2 6 1-1 3-2 4-4 7-1-1-2-1-3-1l-1-1z" class="Z"></path><path d="M533 816l4 6c0 2 0 4-2 5l-1 1c-1-2-3-4-4-5 1 0 2 1 3 1 0-2 0-3 1-4 0 0-1-3-1-4zm90-77v3l1 2 1 3c-2 2-2 3-3 5l-1-2v-2l-1-3 1-4h1l1-2z" class="G"></path><path d="M621 748v-1c0-2 1-4 2-5l1 2c-2 1-2 3-3 4v2-2z" class="C"></path><path d="M621 750v-2c1-1 1-3 3-4l1 3c-2 2-2 3-3 5l-1-2z" class="H"></path><path d="M467 351l3 2-3 10c-1-2-2-3-3-5 1-2 2-5 3-7z" class="D"></path><path d="M658 693v-2c1-1 2-1 3-2v1l1 2 1 3v3h-4v2l-1-7z" class="Q"></path><path d="M659 698v-2h2 0 0l2-1v3h-4z" class="S"></path><path d="M763 473c3 1 7 2 10 4h-1-3l-1-1-1 2c1 1 0 1 1 1 1 1 2 1 3 2-4-1-6-2-9-4h0c0-2 1-3 1-4z" class="U"></path><path d="M334 119l11 4c-2 0-5 1-6 2-2 0-4-2-5-2v-4z" class="O"></path><path d="M482 419l1-3 4 3v2l-2 4c-1-1-3-2-4-3 0-1 0-2 1-3z" class="H"></path><path d="M482 419l1-3 4 3v2c-2 0-2 0-3-1v-1h-2z" class="F"></path><path d="M607 504c1-1 2-1 3-2l-6 16c-1-4 2-10 3-14z" class="X"></path><path d="M814 268c1-1 3-1 4-2 1 2 2 3 3 5-3 1-5 2-8 2 0-2 0-3 1-5z" class="B"></path><path d="M785 383l6 1c-1 2-1 4-1 6l-7-2 2-5z" class="N"></path><path d="M388 391h1c3 2 4 5 7 7 2 2 4 3 5 5-2 0-7-4-9-6-2-1-4-3-4-6z" class="J"></path><path d="M549 477c2 2 3 5 4 7-1 2-2 3-3 5l-1-1-1-2-1-3c1-1 1-1 1-2v-1c1-1 1-2 1-3z" class="N"></path><path d="M473 338c-1 0 0-1 0-1l1-1v-1c0 6-2 12-4 18l-3-2c2-4 6-9 6-13z" class="Y"></path><path d="M520 864c-4-5-7-9-9-14 4 3 7 7 10 11l-1 3z" class="F"></path><path d="M278 415c1 1 1 3 1 5l-6 2-1-5 6-2z" class="M"></path><path d="M603 566c0 2-1 3-2 4h1 1c1-1 2-1 3-2h4l-5 5h-2c-2 0-3 0-4-1l4-6z" class="Q"></path><path d="M538 475c4 2 7 4 9 8-2 1-3 1-5 1-1-2-4-4-6-5l1-1 2 1h2c-1-2-2-2-3-4z" class="S"></path><path d="M362 243l6 19h0l-1-2c-1-2 0 0-1-1v-1l-1-2c0 2 1 4 2 6v2 3c-3-6-4-14-5-19v-5z" class="G"></path><path d="M643 538v2c1 6 1 13 0 18 0-2-2-7-2-10l-1-4c2-2 2-4 3-6z" class="X"></path><path d="M643 538v2c0 3 0 4-1 6v1l-1 1-1-4c2-2 2-4 3-6z" class="D"></path><path d="M576 178v-1c3-2 4-4 4-8s-2-8-5-11l-2-2h0c4 2 7 6 8 10 1 3 1 9-1 11 0 1-1 2-2 2v-1c1 0 1-1 2-2v-1h0c0-1 1-1 0-2 0 1 0 1-1 2-1 2-1 2-3 3z" class="V"></path><path d="M609 768l-2-4 1-6c2 2 4 4 5 7 0 2-1 2-2 4l-1-1h-1z" class="a"></path><path d="M398 459h1 1l-1 1v2h0 0c2 4 5 6 8 10l1 2-1 2c-1-1-3-2-4-4l-1-2c0-3-1-4-4-6v-1c-1-1 0-2 0-4z" class="L"></path><path d="M756 267l2 1-1 3c2-1 3-2 4-3-1 2-2 3-3 4s-1 3-2 5c-1-2-3-4-4-6 2 0 3-3 4-4z" class="J"></path><path d="M649 477c1 4 2 8 2 13h-1l-2 1-1-1c0-4 1-9 2-13z" class="C"></path><path d="M161 263c-1-1-1-2-2-3-2-5-4-13-2-18 0 7 1 10 5 16l-1 1 2 4h-2z" class="L"></path><path d="M448 228c2-9 2-16-2-25 6 8 5 17 5 26l-3-1z" class="V"></path><path d="M537 782h0c0 2-1 4-2 6-1 4-3 8-5 12 0-1-1-2-1-2v-2c0-2 1-3 2-4 2-3 4-6 6-10z" class="B"></path><path d="M213 124c2-1 2-1 4 0l-1 1v1h1c-2 2-5 4-7 6-2-1-2-1-2-2 0-3 3-5 5-6z" class="Y"></path><path d="M600 642v-4h1c1 2 0 4 1 5v2h0c1-2 1-3 2-4 1 3 0 7 0 10v1l-2 1c0-4 0-8-2-11z" class="a"></path><path d="M285 538h0c1 0 2-1 2-1 1 0 2 0 2 1 1 0 1 1 2 1-2 3-4 5-7 6h0 0v-1c1 0 2-1 3-2-1 0-3-1-4-1s-2 2-3 2h-2l7-5z" class="L"></path><path d="M645 401h0c-1 0-2 1-4 0v-1c0-2 2-7 3-8 1 0 2 0 3 1l-1 1v2c0 2 0 3-2 5h1z" class="G"></path><path d="M235 449c2 1 3 1 4 3 1 1 3 4 3 5v1l-2-2c-1 0-1 0-1 1s0 2 1 3h-1c-2-2-3-4-5-7 1 0 1-1 1-1v-3z" class="Z"></path><path d="M364 125h2c1 1 1 2 0 4l-1 1v2c-1 0-2 1-3 0h-1c-1-1-1-3-2-5 2 0 2-1 3-1l2-1z" class="R"></path><defs><linearGradient id="A" x1="714.692" y1="373.469" x2="712.039" y2="380.212" xlink:href="#B"><stop offset="0" stop-color="#1b1a1b"></stop><stop offset="1" stop-color="#373736"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M714 371h0l2 2c0 1 0 2-1 3 0 1-1 7-2 8h0v-1-1-1c-2 1-1 2-3 3l4-13z"></path><path d="M572 391l6 8c-1 1-3 2-3 3-1-1-3-6-5-6-1-1-1-2-1-3h2l1-2z" class="Y"></path><path d="M499 756h1c3 0 5 0 8 1l-8 7-1-2c0-2-1-5 0-6z" class="C"></path><path d="M561 460l1 2c1 2 2 4 1 6l-2 6c-1-1-1-2-2-3l-1-3c2-2 2-5 3-8z" class="a"></path><path d="M561 460l1 2c0 3 0 7-3 9l-1-3c2-2 2-5 3-8z" class="G"></path><path d="M767 467l7 4c-2 1-3 1-5 2l-1 1c-2-1-4-3-6-4 1-1 3-2 5-3z" class="P"></path><path d="M382 532h-1c-3-2-6-12-7-15l1-1c1 0 2 0 3 1h-1c2 1 5 2 7 4l-6-2v1 1l1 1-1 1 1 1v2c1 2 2 5 3 6z" class="a"></path><path d="M615 560h1c1 3 5 8 5 12h-2l-3-4c-2-3-2-5-1-8z" class="C"></path><path d="M783 350l6 3h1c-1 2-2 3-1 5l-3-1-6-2c1-2 2-3 3-5z" class="O"></path><path d="M786 357c1-1 1-3 2-4h1 1c-1 2-2 3-1 5l-3-1z" class="M"></path><defs><linearGradient id="C" x1="391.793" y1="753.428" x2="389.621" y2="759.715" xlink:href="#B"><stop offset="0" stop-color="#868586"></stop><stop offset="1" stop-color="#a3a2a3"></stop></linearGradient></defs><path fill="url(#C)" d="M389 755l3-2c1 0 1 0 2 1l-3 9c-1-1-2-2-4-3l2-5z"></path><path d="M779 382h3l3 1-2 5h-3c-1-1-3-1-5-1 1-3 2-4 4-5z" class="M"></path><path d="M782 382l3 1-2 5h-3v-3h0c0-1 1-2 2-3z" class="D"></path><path d="M780 355l-7-1 4-6 6 2c-1 2-2 3-3 5zm-123 44c2-4 3-7 7-10-1 5-1 10-3 14v-1c0-3-1-1-2-2-1 0-2-1-2-1z" class="a"></path><path d="M657 475l1 1c2 1 4 5 5 8 0 2 0 2-1 4h-1c-1-1-1-1-1-2-2-2-4-8-3-11z" class="T"></path><path d="M500 754c2 1 6-1 9-2h6l-7 5c-3-1-5-1-8-1h-1c0-1 1-2 1-2z" class="O"></path><path d="M708 288c2 2 1 5 3 7-1 1-3 6-4 6l-2-2c0-1 0-2 1-2v-3c0 2-1 3-2 4h-1l5-10z" class="Q"></path><path d="M455 287c0 1-1 5 0 5l1 2c-1 1-1 2-1 3l-1 2 2 3h-2c-2-1-3-3-4-5l5-10z" class="Z"></path><path d="M388 401c4 3 11 9 14 13-3 0-5-2-7-4-3-2-6-4-7-7-1-1-1-1 0-2z" class="B"></path><path d="M388 702l-2-2v-1c3 0 9-8 11-9h1c1 2 1 3 1 5l-3 3-3-2c-2 1-4 3-5 5v1z" class="I"></path><path d="M761 153l-4-4h8 0c2-1 3-1 5-2 1-1 1-1 2-1-1 2-3 4-5 5-2 0-3 1-3 2h-3z" class="J"></path><path d="M814 159c-1-7 0-16-6-21l-1-1 1-1c3 3 5 6 7 10 2 3 1 7 0 11l-1 2z" class="C"></path><path d="M778 216c3 2 5 3 7 5 0 2-1 3-2 5-1 1-2 1-3 2-1-2-1-9-2-12z" class="J"></path><path d="M500 721c3 7 2 18 1 26-2-9-2-17-1-26z" class="F"></path><path d="M292 432l3-6c1 2 2 5 2 8-1 1-1 3-3 4h0-1c0-1-1-2-2-3l1-3z" class="J"></path><path d="M549 654c1 0 2 1 2 2 0-1 0 0 1-1v-1l12 14c-6-3-13-8-15-14zM371 327v-2h1c2 3 2 9 1 13 0 2 0 2-1 4v1l-2-11v-1c0-1 1-3 1-4z" class="G"></path><path d="M691 377l13-15-10 17c-1-1-2-1-3 0h-1l1-2z" class="U"></path><defs><linearGradient id="D" x1="444.445" y1="300.165" x2="445.036" y2="295.004" xlink:href="#B"><stop offset="0" stop-color="#676666"></stop><stop offset="1" stop-color="#807e7f"></stop></linearGradient></defs><path fill="url(#D)" d="M445 294l1 1 3-1c-2 4-5 8-7 12l-2-2 5-10z"></path><path d="M580 694c3 2 7 4 9 7 0 1 0 1-1 2-3 0-5-1-7-3h0v-1c0-2 0-3-1-5z" class="M"></path><path d="M464 358c1 2 2 3 3 5-1 5-3 10-5 15-1-7 1-13 2-20z" class="T"></path><path d="M705 539l-3-4 2-8 4 6c0 3-1 4-3 6z" class="B"></path><path d="M572 689l8 5c1 2 1 3 1 5v1h0c-2-1-4-2-6-4v-2c-1-2-2-3-3-5h0z" class="D"></path><path d="M290 483c1 2 2 3 2 5-3 2-5 4-8 6 0-2 0-4-1-5 1-2 5-5 7-6z" class="K"></path><path d="M350 396l1-1v2c1-1 1-1 2-1h0c-2 3-5 6-7 9l2 5h0c-2-1-2-3-4-4l-3 4h0l-6 5 5-6 5-6 5-7z" class="L"></path><path d="M556 451l5 9c-1 3-1 6-3 8l-2-7h0c2-3 1-8 0-10z" class="C"></path><path d="M504 722c2 3 4 6 5 10 1 3 1 6 0 8l-1 1h-1l-3-19z" class="H"></path><path d="M283 489c1 1 1 3 1 5-3 2-7 5-10 6l1-1 1-1h0c1-1 1-1 2-1v-2h-1c-2 1-3 2-5 2h0l11-8z" class="L"></path><path d="M305 520c1 1 2 3 3 4l-6 4-1-1-1 1c-1-1-1-1-2-3v-1c2-2 5-3 7-4z" class="D"></path><defs><linearGradient id="E" x1="426.595" y1="277.018" x2="430.783" y2="269.151" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#767475"></stop></linearGradient></defs><path fill="url(#E)" d="M433 271l-6 9c-1-2-1-4-1-6l4-6 3 3z"></path><defs><linearGradient id="F" x1="490.402" y1="421.44" x2="492.708" y2="415.789" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#727271"></stop></linearGradient></defs><path fill="url(#F)" d="M489 414c2 0 3 0 4 1h1c1 4 1 8-1 12-1-2-2-4-3-7 0-2-1-4-1-6z"></path><defs><linearGradient id="G" x1="458.268" y1="326.21" x2="458.894" y2="320.928" xlink:href="#B"><stop offset="0" stop-color="#5c5b5b"></stop><stop offset="1" stop-color="#747273"></stop></linearGradient></defs><path fill="url(#G)" d="M461 317c2 1 2 1 4 1l-1 2-5 8h-2l-1-1 4-9 1-1z"></path><path d="M461 317c2 1 2 1 4 1l-1 2-3-1-1-1 1-1z" class="D"></path><path d="M393 539h0c-4-2-9-1-13-4 9 1 19 2 27 6-4 0-11 0-14-2z" class="F"></path><path d="M558 678c-4-1-7-3-10-6-2 0-4-1-4-3l1-1c2 0 4 2 6 3h1l6 3h-1c-1 0-2 0-3-1l-1 1c2 1 4 2 5 4z" class="K"></path><defs><linearGradient id="H" x1="629.075" y1="269.759" x2="626.356" y2="263.27" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#868586"></stop></linearGradient></defs><path fill="url(#H)" d="M623 264h1c2 1 4-1 5-2l2 3c0 1 1 3 0 4s-2 1-3 2l-1 1-4-8z"></path><defs><linearGradient id="I" x1="697.745" y1="589.729" x2="701.367" y2="593.778" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#929292"></stop></linearGradient></defs><path fill="url(#I)" d="M697 591v-1-1l1-1 1-1 1 2h2l2 8-1-1h-1c-1 1-2 1-2 3l-3-8z"></path><path d="M321 608c4-6 9-12 14-18v1c-1 2-2 3-3 4s-2 2-3 4l-1 1v1c1-1 3-2 4-3 0-2 3-4 4-5v1l-11 12c-1 1-2 2-3 4l-1-2z" class="B"></path><path d="M229 146c4-1 6-2 10-2 0 1-1 2-1 3 1 1 1 2 1 3-2 1-3 1-5 1-2-1-3-3-5-5z" class="X"></path><path d="M295 217h0c-1 10-2 18-7 27 0-4 3-12 4-16s1-7 3-11z" class="C"></path><path d="M788 192h0c0-2 1-2 2-3l1 1h1c1 0 2 0 3 1h0l1-1 1 2-4 6c-1-2-4-3-5-4v-2z" class="F"></path><path d="M672 404v1c2 1 5-1 7-1h1c-1 1-1 1 0 2v2c-2 0-4 1-6 1h-1c-2 1-3 1-5 0v-1c1-2 2-3 4-4z" class="G"></path><path d="M506 418c3-7 5-13 7-20v-1-16c3 12-1 28-5 39l-2-2z" class="V"></path><path d="M733 228h2c3 6 6 12 7 17l-1 3c-3-6-5-13-8-20z" class="Q"></path><path d="M313 193c5 3 10 7 14 11 3-2 6-5 8-6l1 2c-2 1-5 5-7 5-5 0-13-9-16-12z" class="D"></path><path d="M645 522c0-7 2-13 5-20 0 1-1 3-1 5-1 2-1 5-1 7-1 6 0 12 0 19 0-1-1-2-1-3s-1-3-2-4h0v-3-1z" class="Q"></path><defs><linearGradient id="J" x1="598.063" y1="825.1" x2="601.851" y2="828.327" xlink:href="#B"><stop offset="0" stop-color="#595a59"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#J)" d="M598 823h0l1 1 2-2 1 1c0 4 1 10 0 14h0v-7c-1 1-1 2-1 3h0l-1-2c-1 2 1 7-1 10l-1-18z"></path><path d="M342 117c7 3 14 6 22 8l-2 1c-7-1-11-2-17-5 0-1-1-2-2-3l-1-1z" class="K"></path><path d="M261 387h0l1-1 1 2v1h1c-1 4-2 10-5 13 0-1 0-2 1-2v-1c-1-1-1-1-2-1l-2 2 5-13z" class="H"></path><path d="M324 159v-1-1h3l14 9c-7-1-11-3-17-7z" class="X"></path><defs><linearGradient id="K" x1="617.357" y1="295.756" x2="611.317" y2="288.792" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#K)" d="M613 287l6 7c-1 2-1 3-3 4l-8-10h5v-1z"></path><path d="M452 640l1-1c0-3 1-6 0-8v-1h1l1 13c1 1 1 4 1 6h-1l1 2c0 1 0 1-1 2l-1-1c-1-3-2-8-2-12z" class="M"></path><defs><linearGradient id="L" x1="603.169" y1="320.326" x2="596.587" y2="313.911" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#L)" d="M598 311c3 4 5 7 7 10-1 1-2 1-4 1l-6-9c1 0 2-1 3-2z"></path><defs><linearGradient id="M" x1="582.085" y1="421.86" x2="576.586" y2="412.539" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#413f3f"></stop></linearGradient></defs><path fill="url(#M)" d="M577 410c2 2 4 4 5 7 0 1 1 2 1 3s-1 3-2 5v1c-1-2-1-4-2-7-1-1-3-3-4-5 2-2 2-2 2-4z"></path><path d="M857 389c7-3 14-4 21-7 3-1 5-2 8-4 1-1 3-2 4-2-1 1-4 3-6 4-5 3-11 6-17 8l-1-1c-3 0-6 2-9 3v-1z" class="E"></path><path d="M229 321c1 2 1 3 2 5l-7 5-3-4 8-6zm-30-27c3 1 4 1 6 1 4 2 7-1 10 2-5 1-9 2-13 2l-3-3v-2z" class="Z"></path><path d="M361 248l-2-8c-1-4-4-19-2-22 1 3 0 6 1 9 1 5 3 10 4 16v5h-1z" class="T"></path><path d="M464 350l12-24-2 9v1l-1 1s-1 1 0 1l-7 13-2-1z" class="S"></path><path d="M559 607c1 0 2 2 3 3l7 13-1 1c-1-1-2-1-3-2-3-3-6-11-6-15z" class="F"></path><path d="M719 252l1 1h0c0 2 0 3 1 4 2 8 1 16-3 23h0c-1-3 1-7 1-10 1-3 1-6 1-9h0v-3c-1-1-1-3-1-4v-2z" class="J"></path><path d="M774 162h1c5-2 8-4 11-8-1 5-3 10-6 14l-1-1c0-1 0-1-1-2v-1c-2-1-2-1-4-1v-1z" class="O"></path><path d="M294 203c-1-2-2-4-3-7 1-1 1-1 3-1 2 1 4 4 5 7h0c-1 1-1 3-2 4 0 0-1 0-1-1l-1 1c0-1-1-2-1-3z" class="T"></path><path d="M293 542c1-1 3-2 5-1-1 3 0 7 1 11h-3c0-1-1-2-1-3s-2-2-3-2l1-1c-1-1-1-3 0-4z" class="U"></path><path d="M448 300l2-3c1 2 2 4 4 5h2c2 1 3 3 3 5v2c-3-1-6-4-8-5s-2-2-3-4z" class="D"></path><path d="M465 731c0 2 1 3 2 5 0 1 0 1 1 2l-2 2-3 4-2-6c-1-2 1-4 2-5l2-2z" class="B"></path><path d="M465 731c0 2 1 3 2 5 0 1 0 1 1 2l-2 2c-1 0-1-1-1-1l-2-6 2-2z" class="H"></path><path d="M393 763h4 1c-1 4-2 8-4 12v-1-1h0c-1-1-1-1-3-1 0 1 0 2-1 2l-1 1 4-12z" class="P"></path><path d="M547 701c7 3 12 9 17 14h-3c-2-1-3-2-5-4-3-3-6-6-9-10z" class="F"></path><path d="M340 567c3-2 6-3 9-5v5c-1 2-3 3-5 4h-1c-1 1-1 1-2 1l-1-3 1-1-1-1z" class="R"></path><path d="M649 688v-1c1-1 3-2 5-2l2 9-5 2v1h0l-2-9z" class="Z"></path><path d="M831 302h5l-4 16-1-15v-1z" class="S"></path><defs><linearGradient id="N" x1="449.263" y1="339.903" x2="457.007" y2="335.676" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#383736"></stop></linearGradient></defs><path fill="url(#N)" d="M456 327l1 1h2 0c-2 3-4 7-5 10s-2 7-3 11c-1-7 0-11 3-18h0c0-1 1-3 2-4z"></path><path d="M456 327l1 1h2 0c-2 2-3 3-5 3 0-1 1-3 2-4z" class="H"></path><path d="M806 534v1c10 3 22-1 31-5-11 9-26 8-40 6h-3c2-1 3 0 5 0h3c2 0 5 1 7 0-2 0-3 0-4-1l1-1z" class="E"></path><path d="M697 282c2-1 5-1 8-2 2 0 3 0 5 1-2 2-7 3-10 3-2 1-4 1-6 1h-1s-1 0-1-1l1-2 3-1 1 1z" class="Y"></path><path d="M696 281l1 1h-1c0 1-1 1-2 1v2h-1s-1 0-1-1l1-2 3-1z" class="N"></path><defs><linearGradient id="O" x1="458.193" y1="364.236" x2="461.534" y2="358.52" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#O)" d="M464 350l2 1c-3 5-4 11-6 17-1-1-2-3-2-5 0-4 3-10 6-13z"></path><path d="M495 393c7-7 11-15 13-25 1-2 1-5 1-8 0 4 1 7 0 10-2 8-6 15-10 22v1s-1 0-1 1h-1l-2-1z" class="L"></path><path d="M768 201v-2c4 4 7 7 12 8 1 1 3 2 4 3l-2 1h0c1 1 3 2 4 4h-1c-5-3-10-7-15-11l1-1-3-2z" class="H"></path><path d="M775 139h0c-1 4-3 6-7 7-5 2-9 0-14-1l-1-2c1-1 10 2 12 1 5 0 7-2 10-5z" class="C"></path><path d="M772 146h1c1-2 3-4 3-6v-5c1 1 1 4 0 6 0 1 0 2-1 2v1c-2 3-5 6-7 9l-1 2c0 1 0 0 1 2h0c0 1 1 2 1 3-4-1-6-4-8-7h3c0-1 1-2 3-2 2-1 4-3 5-5z" class="B"></path><defs><linearGradient id="P" x1="539.767" y1="513.667" x2="533.696" y2="507.815" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#656465"></stop></linearGradient></defs><path fill="url(#P)" d="M531 504c1 1 1 2 3 2l2-1c1 2 3 4 4 6 0 1 0 3 1 3-1 2-1 3-2 5l-4-8c-1-2-3-4-4-7z"></path><defs><linearGradient id="Q" x1="358.386" y1="247.145" x2="351.614" y2="245.355" xlink:href="#B"><stop offset="0" stop-color="#1b1a19"></stop><stop offset="1" stop-color="#363536"></stop></linearGradient></defs><path fill="url(#Q)" d="M350 246h1v-1c0-2 0-3 1-4l2 1h1c3 3 4 8 5 13-3-4-6-6-10-9z"></path><path d="M361 248h1c1 5 2 13 5 19l2 6c0 2 0 4 1 7l1 5-1 2-9-39z" class="X"></path><path d="M569 387l3 4-1 2h-2c0 1 0 2 1 3h0l-1 2c-1 0-1-1-2 0h-1c0-1 0-1-1-2l-1-3v-2l1-2 1 1c0-1 0-1 1-2 0 1 0 0 1 1l1-2z" class="P"></path><path d="M564 393c2 0 3 1 4 2v-1-2h1v1c0 1 0 2 1 3h0l-1 2c-1 0-1-1-2 0h-1c0-1 0-1-1-2l-1-3z" class="Z"></path><path d="M841 211c1 1 3 1 6 2h0l2 1c-3 3-7 6-9 9l-1-5c1-2 2-3 3-5l-2-1h0l1-1z" class="N"></path><path d="M847 375c7-4 11-8 13-15 1-3 1-6 1-8 1 4 1 10-1 14-2 5-8 11-13 13h0l-1-1c1 0 2-1 2-1l3-2c2-1 3-2 4-4l1-1c1-1 1 0 1-1-3 3-6 5-10 6z" class="E"></path><defs><linearGradient id="R" x1="421.03" y1="215.716" x2="423.868" y2="202.158" xlink:href="#B"><stop offset="0" stop-color="#949395"></stop><stop offset="1" stop-color="#bdbcbb"></stop></linearGradient></defs><path fill="url(#R)" d="M424 200c2 1 3 1 3 3 0 4-4 9-5 12l-2 2h-1c-1-5 2-9 4-13 1-1 2-1 2-2l-1-2z"></path><path d="M591 652c-4-2-7-5-11-8-1-1-3-2-4-4l1-1c4 1 10 7 14 9h1v3l-1 1z" class="M"></path><path d="M600 861c1 1 2 2 3 2-2 9-3 18-2 27-1-3-2-7-3-10 2-2 0-7 2-9 1-2 0-7 0-10z" class="N"></path><path d="M618 461c0-4 1-10 4-13h1c1-1 1-1 1-2s1-2 1-2h0c1 1 1 2 0 4h0c-2 3-2 6-3 9l-3 7h-1v-2-1z" class="R"></path><path d="M602 506v6c0 4-1 8 1 11 0 1-1 3-2 4l-3-8c0-5 2-8 4-13z" class="B"></path><defs><linearGradient id="S" x1="410.578" y1="777.293" x2="407.24" y2="789.565" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#S)" d="M405 790c2-4 3-9 4-13 1 1 0 1 1 0l3 1-4 13c-1-1-2-1-4-1z"></path><defs><linearGradient id="T" x1="508.083" y1="503.801" x2="514.356" y2="498.853" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#3d3b3b"></stop></linearGradient></defs><path fill="url(#T)" d="M512 491l3 3c-1 3-2 6-2 9l1 2h-1-1l-1 6c-3-7-2-13 1-20z"></path><path d="M729 126c-2-4-2-8-3-11l3 1h4 1l2 5h-1v2h-1-5v3z" class="O"></path><defs><linearGradient id="U" x1="397.152" y1="742.582" x2="392.153" y2="751.852" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#U)" d="M396 740c1 1 0 2 1 3h2 0v-1 1l-5 11c-1-1-1-1-2-1l-3 2 7-15z"></path><path d="M242 322c2 1 4 1 6 3-3 1-7 4-11 5l-3-4c1-1 6-3 8-4z" class="G"></path><defs><linearGradient id="V" x1="587.098" y1="183.683" x2="594.495" y2="193.632" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#919191"></stop></linearGradient></defs><path fill="url(#V)" d="M594 197c-2-5-6-9-10-13h0c1 0 2-1 2 0 5 1 8 5 10 8l-2 5z"></path><path d="M591 561c2 0 6 7 8 9h0c-3 2-7 4-11 5h-4-1l4-2c2 0 3-1 5-2l3-1c-1-2-4-5-4-6-1-1 0-2 0-3z" class="B"></path><defs><linearGradient id="W" x1="708.945" y1="124.698" x2="704.271" y2="130.055" xlink:href="#B"><stop offset="0" stop-color="#292728"></stop><stop offset="1" stop-color="#474546"></stop></linearGradient></defs><path fill="url(#W)" d="M705 133c0-5 0-11 2-16v2 1h1v-1h1c-1 2-1 3-1 6 1 1 1 2 1 3s2 4 1 6c-1 1-1 2-1 4v2l-2-2c0-2 1-3-1-6v1h-1z"></path><path d="M709 128c0 1 2 4 1 6-1 1-1 2-1 4 0-1 0-1-1-2 0-2-1-7 1-8z" class="G"></path><defs><linearGradient id="X" x1="299.197" y1="439.27" x2="289.604" y2="447.958" xlink:href="#B"><stop offset="0" stop-color="#0f0e0e"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#X)" d="M289 448c2-4 5-8 9-11 1 0 1 1 2 2 0 2-2 4-4 6s-3 4-5 7c0-2 0-3-1-4h-1z"></path><defs><linearGradient id="Y" x1="448.623" y1="333.745" x2="450.384" y2="327.676" xlink:href="#B"><stop offset="0" stop-color="#312f30"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#Y)" d="M451 324c1 1 3 1 4 0v1l-6 14-3-7 4-6 1-2z"></path><path d="M451 324c1 1 3 1 4 0v1c-1 1-2 1-3 1-1 1-1 0-2 0l1-2z" class="O"></path><defs><linearGradient id="Z" x1="225.433" y1="115.341" x2="216.239" y2="123.803" xlink:href="#B"><stop offset="0" stop-color="#545253"></stop><stop offset="1" stop-color="#898889"></stop></linearGradient></defs><path fill="url(#Z)" d="M213 124c4-4 9-9 15-8h2l-13 10h-1v-1l1-1c-2-1-2-1-4 0z"></path><defs><linearGradient id="a" x1="478.633" y1="387.61" x2="475.605" y2="377.518" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#a)" d="M477 389c-2-6-5-10-4-17v4l2 1h2c0 1 1 3 2 5s2 3 3 5l-1 1c-1 1-2 1-4 1z"></path><path d="M716 295l3-6v-1l1 1h-1v1 1h-1v1l1 1c0-1 0-2 1-3v-1c1 1 1 2 1 3l-1 1v2 2c0-1 0-2 1-2v1 7c0-1 0-1 1-2v-1-1h1l-3 10c0-2-1-4-2-6-1-3-1-5-2-8z" class="B"></path><path d="M321 707h0l2 6c1 2 1 4 2 6h0v-7c-1-1 0-2 0-2v-4-1l1 1v1c-1 8 1 16 1 25 0 5 0 10-1 15v-1c0-9 0-17-2-26l-3-13z" class="E"></path><defs><linearGradient id="b" x1="797.773" y1="297.428" x2="805.286" y2="299.342" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#b)" d="M806 302l-9-2h-1c1-2 1-3 2-4 5-1 12 0 18 1h-6c-1 1-2 1-3 2v1c-1 1 0 1-1 2z"></path><defs><linearGradient id="c" x1="211.995" y1="255.987" x2="202" y2="256.013" xlink:href="#B"><stop offset="0" stop-color="#373536"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#c)" d="M197 251c5 2 9 3 14 3v3l4 2-2 1c-5-1-10-2-15-4h2 2v-2c-2-1-3-1-4-2l-1-1z"></path><defs><linearGradient id="d" x1="766.403" y1="373.106" x2="772.802" y2="374.954" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#d)" d="M774 379l-9-3 3-6 6 3h1l1 1c-1 1-2 2-1 4v1h-1z"></path><path d="M775 373l1 1c-1 1-2 2-1 4v1h-1v-1c-1-2 0-3 1-5z" class="C"></path><defs><linearGradient id="e" x1="572.548" y1="406.177" x2="567.763" y2="398.051" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#e)" d="M570 396l5 11c-1 2 0 2-2 3-4-4-6-8-8-14 1 1 1 1 1 2h1c1-1 1 0 2 0l1-2z"></path><path d="M301 187c1-1 4-2 6-2l3 1c-8 2-16 5-23 9h-2-1c0-1 0-3 1-4 0 0 1 0 1-1 1 1 2 1 3 1 3 0 7-2 10-4h2z" class="F"></path><path d="M261 454c-1 1-2 2-2 3-2 2-4 4-7 5l-2-2c1-2 3-4 5-6 1-2 2-4 3-5 1 1 1 3 3 5z" class="G"></path><path d="M234 326l3 4c-4 2-8 4-11 7-1-2-1-3-3-4h0l11-7z" class="Z"></path><path d="M304 166c1 0 4-1 5 0v1h0c-1 1-2 1-3 1h-1s-1 1-2 1h-2c-3 1-13 5-16 4-1-1-1-2-2-3 5-1 10 0 15-1 1-1 3-2 4-2h1l1-1z" class="I"></path><path d="M254 388h0c1-1 1 0 2-1 1 0 1 0 2 1v1h1c-1 3-2 6-4 9-1 0-1 1-2 2-2-1-2-2-2-4l3-8z" class="M"></path><path d="M666 382l-1 1c-2 0-2 0-3-1v-1-2c-1 0-1 0-1-1v-1h-1v-1h0c-1-1-1-2-2-2h0l-1 1h0c1-3 3-6 5-8v1 2c0 1 0 1 1 2 2 2 2 7 3 10z" class="G"></path><defs><linearGradient id="f" x1="441.845" y1="311.215" x2="447.449" y2="306.539" xlink:href="#B"><stop offset="0" stop-color="#323131"></stop><stop offset="1" stop-color="#4e4c4c"></stop></linearGradient></defs><path fill="url(#f)" d="M441 313c2-5 4-9 7-13 1 2 1 3 3 4-3 4-5 7-7 11-1-1-2-1-3-2z"></path><defs><linearGradient id="g" x1="610.21" y1="783.456" x2="617.143" y2="791.977" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#g)" d="M610 783l2-2h2l4 14h-4c-1-1-1-3-1-4l-3-8z"></path><defs><linearGradient id="h" x1="578.946" y1="363.075" x2="571.336" y2="357.626" xlink:href="#B"><stop offset="0" stop-color="#9c9a9b"></stop><stop offset="1" stop-color="#c8c7c8"></stop></linearGradient></defs><path fill="url(#h)" d="M573 353c3 3 6 7 8 10-1 2-3 3-4 5-2-4-4-7-6-10 1-1 1-1 2-3v-2z"></path><path d="M712 569c4 1 7-1 11-3v1c-1 1-3 1-4 2l1 1s1 1 1 2v-1c1 0 2-1 3-1l1-1h2c-5 3-10 6-16 6v-5l1-1z" class="S"></path><path d="M198 468h0c2 0 1 0 2-1 2 0 6 0 9 1v1l-1 1c-5-2-8 1-13 2-5 2-9 2-14 0 2 0 5 1 7 0h1c0-1 1-1 2-1l1-1c2 0 4-1 6-2z" class="W"></path><defs><linearGradient id="i" x1="242.694" y1="356.025" x2="237.683" y2="358.304" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#i)" d="M247 350c1 2 0 4 0 6l-10 7c0-1 0-2 1-2-1-2-1-2-2-3l11-8z"></path><defs><linearGradient id="j" x1="406.731" y1="790.452" x2="404.824" y2="801.234" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#aeadae"></stop></linearGradient></defs><path fill="url(#j)" d="M405 790c2 0 3 0 4 1-1 7-2 14-5 21v-3-1c1-1 1-1 1-2 1-1 0-1 0-2 1-1 1-1 1-2h-2c-1 2-1 4-2 5l-1 1 4-18z"></path><path d="M512 841h1l-4-6c2 1 3 4 6 4l7 7c1 3 2 5 4 7-1-1-1-1-3-1-4-2-8-8-11-11z" class="G"></path><path d="M173 391c-3-2-5-5-7-7-9-7-16-13-27-16h1c10 1 19 8 26 13 2 2 5 4 7 6h-1l-6-5h-1v1c3 1 7 5 8 8z" class="W"></path><path d="M644 532l2 1v2l2 1c1 3 0 8 0 12-1 2-1 6-2 8l-1-16c0-2-1-5-1-8z" class="C"></path><path d="M644 532l2 1v2l2 1v1-1c-2 2-1 7-2 9-1-1-1-3-1-5s-1-5-1-8z" class="N"></path><path d="M398 375l1 3 2 4c1 5 3 9 2 15v1l-8-21c2 1 2 3 2 4l1 1v-3-1-3z" class="P"></path><path d="M275 408h3l1-1c2 0 3-2 5-3 2 2 3 4 4 6-4 1-8 3-11 4-1-2-1-4-2-6z" class="I"></path><defs><linearGradient id="k" x1="483.098" y1="439.384" x2="491.17" y2="431.866" xlink:href="#B"><stop offset="0" stop-color="#0e0d0e"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#k)" d="M491 431c-2 6-4 10-5 15 0-2-3-7-2-9l5-12c1 2 2 3 1 5l1 1z"></path><defs><linearGradient id="l" x1="239.984" y1="288.469" x2="231.631" y2="292.755" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#2a292a"></stop></linearGradient></defs><path fill="url(#l)" d="M228 289l9-1s2-1 3-1c0 0 0 1 1 1 0 1 1 2 1 4-2 1-9 2-11 2-1-2-2-3-3-5z"></path><path d="M563 639l3 2c6-1 9-6 14-9l2-1c-2 3-4 5-6 7-3 2-6 4-8 6v1c0-1-1-1-1-1l-2 1c-1-1-3-4-4-5 0 0 1-1 2-1z" class="M"></path><path d="M573 426v1c0 3 2 4 2 7 1 1-1 3-1 4s-1 3-1 3l-1 1-3-8c0-2-1-3-1-5 1-1 3-2 5-3z" class="B"></path><path d="M278 415c2-1 8-4 11-3 0 2 1 3 1 5-4 1-7 3-11 3 0-2 0-4-1-5z" class="J"></path><defs><linearGradient id="m" x1="582.518" y1="408.252" x2="578.849" y2="403.466" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#515050"></stop></linearGradient></defs><path fill="url(#m)" d="M578 399c3 3 6 6 8 10-2 1-3 1-4 3l-7-10c0-1 2-2 3-3z"></path><path d="M716 295c1 3 1 5 2 8 1 2 2 4 2 6l-4 9v-5c1-1 0-1 0-2h1c-1-1-1-1-1-2v-1h-1v-2-5l-2 2 3-8z" class="C"></path><path d="M343 383l2-3h0c1 1 1 2 2 2h1c0 2 1 3 1 4 2 3 1 5 2 8v1h0l-1 1c-2-1-4-5-5-8l-2-5z" class="a"></path><path d="M343 383l2-3h0c1 1 1 2 2 2h1c0 2 1 3 1 4h0c-1-1-2-1-2-2l-1 1c0 1 0 2-1 3l-2-5z" class="G"></path><defs><linearGradient id="n" x1="491.261" y1="413.81" x2="493.331" y2="400.321" xlink:href="#B"><stop offset="0" stop-color="#7e7d7c"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#n)" d="M493 400v-1l1 1h2c-1 5-2 10-2 15h-1c-1-1-2-1-4-1 1-6 2-10 4-14z"></path><defs><linearGradient id="o" x1="352.647" y1="611.1" x2="345.298" y2="618.765" xlink:href="#B"><stop offset="0" stop-color="#444244"></stop><stop offset="1" stop-color="#6d6c6d"></stop></linearGradient></defs><path fill="url(#o)" d="M352 608c1 1 1 2 2 2-1 3-3 6-4 9h0l-1 2c-2-2-5-3-7-3l10-10z"></path><defs><linearGradient id="p" x1="597.69" y1="343.639" x2="593.516" y2="334.321" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#p)" d="M592 333c4 3 7 7 9 11l2 3-5 1-2-3-6-10v-2h1 1z"></path><path d="M601 344l2 3-5 1-2-3c3 0 3 0 5-1z" class="M"></path><path d="M576 596h1c3 4 5 11 8 15 1 3 3 5 3 8h-2 0c-2-1-3-4-4-6-2-5-6-11-6-17z" class="D"></path><path d="M333 143c9-1 16 1 24 3l1 1c-8 2-19-1-25-4z" class="Z"></path><defs><linearGradient id="q" x1="675.982" y1="403.122" x2="680.434" y2="396.091" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#q)" d="M672 404c3-2 5-4 4-8v-4c3 1 3 1 6 1-1 3-1 7-2 11h-1c-2 0-5 2-7 1v-1z"></path><path d="M623 547l1 1c0 3 1 6 1 8 1 4 3 9 2 12l-1-1c-3-5-4-12-6-18 1-1 2-1 3-2z" class="O"></path><path d="M378 521v-1-1l6 2c-2-2-5-3-7-4h1c4 1 9 2 13 4h-1-2l-1 1h1 1c1 0 1 1 2 2h0c3 1 6 2 7 5l-6-2-14-6z" class="U"></path><path d="M732 200h1l2-2 3 3 1 4c-1 2 0 3 0 5 0 1-1 3-2 4v-2c-2-4-6-8-5-12z" class="Y"></path><defs><linearGradient id="r" x1="239.991" y1="476.054" x2="226.538" y2="473.773" xlink:href="#B"><stop offset="0" stop-color="#424141"></stop><stop offset="1" stop-color="#737273"></stop></linearGradient></defs><path fill="url(#r)" d="M225 472c5 1 8 2 13 1l2 1c0 1 0 2 1 3-7 2-11 1-17-1v-1c2-1 1-1 1-3z"></path><defs><linearGradient id="s" x1="633.267" y1="553.232" x2="624.733" y2="537.768" xlink:href="#B"><stop offset="0" stop-color="#6d6e6c"></stop><stop offset="1" stop-color="#989697"></stop></linearGradient></defs><path fill="url(#s)" d="M620 536h1l10 9c3 3 7 6 8 11h-1c-5-5-11-9-15-15-2-1-3-3-3-5z"></path><path d="M874 244l2-3c4-7 6-15 11-21 5-5 9-7 16-7-2 0-3 1-4 1-14 4-15 18-21 29 0-1 0-3 1-4v-1c-2 2-2 5-5 6z" class="E"></path><defs><linearGradient id="t" x1="700.705" y1="596.871" x2="704.987" y2="606.039" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#cccbcc"></stop></linearGradient></defs><path fill="url(#t)" d="M700 599c0-2 1-2 2-3h1l1 1 6 17-1-1c-1-1-2-5-3-7l-2 2c0 2 3 6 1 8l-5-17z"></path><path d="M568 304c0-7 1-13 3-19-1 9 0 15 4 24l-1 4v1l-1-1c0-1 0-2-1-2 0-1-1-1-1-2v-1c-1-2-1-3-3-4z" class="E"></path><path d="M553 684c-1 0-2-1-3-2-3-1-6-3-8-4h-1l2-1 3-2c2-1 10 5 12 6-2 1-4 1-5 3z" class="Z"></path><defs><linearGradient id="u" x1="473.601" y1="406.029" x2="477.481" y2="392.639" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#4a4949"></stop></linearGradient></defs><path fill="url(#u)" d="M477 391c2 1 2 1 4 1-2 6-5 11-7 18-1-3-2-7-1-11l4-8z"></path><path d="M558 678c-1-2-3-3-5-4l1-1c1 1 2 1 3 1h1l10 6c1 0 3 1 4 2v2h-1v1c1 1 1 0 1 1l-14-8z" class="N"></path><path d="M490 658v22 12l-4-20c1-1 1-1 1-2v-1-7l1 1c1 2 0 4 0 6h1v-5h0l1-6z" class="J"></path><path d="M487 662l1 1c1 2 0 4 0 6h1v-5h0c1 4 1 10 0 14l-2-8v-1-7z" class="T"></path><path d="M608 288l-3-3c4-2 6-3 8-7h1l6 6c-2 1-6 0-7 3v1h-5z" class="N"></path><defs><linearGradient id="v" x1="603.434" y1="770.853" x2="613.725" y2="779.55" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#v)" d="M610 783l-5-8c-1-1-1-2-1-4 0-1 0-2 1-3l2 1c3 3 5 8 7 12h-2l-2 2z"></path><defs><linearGradient id="w" x1="462.738" y1="317.15" x2="470.79" y2="304.367" xlink:href="#B"><stop offset="0" stop-color="#918f90"></stop><stop offset="1" stop-color="#bcbbbc"></stop></linearGradient></defs><path fill="url(#w)" d="M470 304c0-2 1-2 3-4l3-3c-3 8-7 15-11 21-2 0-2 0-4-1 3-5 6-8 9-13z"></path><path d="M327 127h1c0 1-1 2-2 3l-8 6c-3 3-5 6-8 8-2 3-5 4-7 7l-1 1v-1c2-3 4-7 7-9 2-3 5-5 7-7 4-2 7-5 11-8z" class="P"></path><defs><linearGradient id="x" x1="725.126" y1="475.713" x2="732.024" y2="478.438" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#x)" d="M734 484l-10-6c0-2 1-4 2-6l11 7-3 2c-1 1 0 1-1 1v1l1 1z"></path><path d="M773 252l-3 6c-1 1-3 2-5 4v1l-4 5c-1 1-2 2-4 3l1-3-2-1c1-2 4-5 6-6l2-2c1 0 1 0 2-1h0c3-1 5-4 7-6z" class="G"></path><path d="M376 367c1-8 1-17 3-25 0 10 1 20 0 29 0 3-1 6-2 9 0-4 1-10-1-13z" class="B"></path><path d="M768 430c2-1 4-2 7-2 0 2 0 2-1 4h0c-1 2-6 6-8 6h-1c-1 0-1-2-2-3h0c1-2 2-4 3-5h2z" class="M"></path><path d="M766 430h2v1s-1 1-1 2c-1 1-2 3-2 5-1 0-1-2-2-3h0c1-2 2-4 3-5z" class="O"></path><defs><linearGradient id="y" x1="258.589" y1="205.973" x2="255.344" y2="195.432" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#3f3e3e"></stop></linearGradient></defs><path fill="url(#y)" d="M250 192h1l1 2h1v-1c3 2 6 7 9 8 1 2-2 5-3 7l-9-16z"></path><defs><linearGradient id="z" x1="303.101" y1="531.645" x2="297.931" y2="536.462" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#acabab"></stop></linearGradient></defs><path fill="url(#z)" d="M293 542c2-6 6-10 11-13 1 2 2 4 2 5-3 2-5 4-8 7-2-1-4 0-5 1z"></path><path d="M303 258c0-9 0-20 3-29l1 26c-2 1-3 2-4 3z" class="K"></path><defs><linearGradient id="AA" x1="580.394" y1="659.787" x2="570.606" y2="642.213" xlink:href="#B"><stop offset="0" stop-color="#616060"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#AA)" d="M565 645l2-1s1 0 1 1l19 13-3 1c-7-4-13-9-19-14z"></path><defs><linearGradient id="AB" x1="713.337" y1="121.962" x2="707.163" y2="134.038" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#363738"></stop></linearGradient></defs><path fill="url(#AB)" d="M710 117h0c0 2 0 4 1 6l4 10-1 1v1c-2 2-1 3-3 5l-1-1v-2-3c1-2-1-5-1-6s0-2-1-3c0-3 0-4 1-6v1l1-3z"></path><path d="M710 137h1 1v-5l1-1 1 3v1c-2 2-1 3-3 5l-1-1v-2z" class="C"></path><defs><linearGradient id="AC" x1="787.413" y1="328.912" x2="797.524" y2="332.754" xlink:href="#B"><stop offset="0" stop-color="#222"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#AC)" d="M787 328c7 0 11 1 16 4-1 0-1 1-2 2h0l-1 1c-4-1-9-2-14-2 1-2 1-3 1-5z"></path><defs><linearGradient id="AD" x1="525.969" y1="843.485" x2="519.531" y2="832.515" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#514f50"></stop></linearGradient></defs><path fill="url(#AD)" d="M501 814h1c3 6 7 11 12 16 2 2 5 5 8 7s8 4 9 7h0c-13-4-23-18-30-30z"></path><path d="M288 154c4 2 9 4 13 7-6 0-12 0-18-1h-1 1v-2c1-1 1 0 1-1v-1c1 0 2-1 3-1l1-1z" class="H"></path><defs><linearGradient id="AE" x1="693.922" y1="579.878" x2="702.114" y2="585.314" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#7a7a79"></stop></linearGradient></defs><path fill="url(#AE)" d="M691 573c1 1 2 1 2 2 1 1 2 2 2 3 1 0 1-1 2-2v-1l5 14h-2l-1-2-1 1-1 1v1 1l-6-18z"></path><defs><linearGradient id="AF" x1="594.388" y1="195.46" x2="594.169" y2="207.041" xlink:href="#B"><stop offset="0" stop-color="#b0aeae"></stop><stop offset="1" stop-color="#d1d0d2"></stop></linearGradient></defs><path fill="url(#AF)" d="M596 192c3 7 0 12 0 19l-1-1-1-1c-1 1-2 3-2 5-2-7 1-11 2-17l2-5z"></path><defs><linearGradient id="AG" x1="292.596" y1="437.782" x2="283.591" y2="442.108" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#525151"></stop></linearGradient></defs><path fill="url(#AG)" d="M292 432l-1 3c1 1 2 2 2 3h1 0l-7 11-2-2-1-1h0 0l-2 1c3-5 7-10 10-15z"></path><defs><linearGradient id="AH" x1="833.694" y1="218.377" x2="833.142" y2="213.037" xlink:href="#B"><stop offset="0" stop-color="#4a4849"></stop><stop offset="1" stop-color="#646263"></stop></linearGradient></defs><path fill="url(#AH)" d="M838 206l1 1-1 1 3 3-1 1h0c-3 3-5 6-8 9l-2-2 4-8 1-2c0-1 1-2 3-3z"></path><path d="M835 209l2 2c0 2 1 1 0 3-1-1-2-1-3-3l1-2z" class="D"></path><path d="M838 206l1 1-1 1 3 3-1 1-3-1-2-2c0-1 1-2 3-3z" class="S"></path><defs><linearGradient id="AI" x1="447.833" y1="496.731" x2="438.36" y2="486.477" xlink:href="#B"><stop offset="0" stop-color="#878687"></stop><stop offset="1" stop-color="#b8b7b8"></stop></linearGradient></defs><path fill="url(#AI)" d="M437 476c2 3 3 6 5 9l6 12c-1 1-1 2-3 3l-11-23c3 2 4 6 5 8h1v-2l-1-1c-1-2-2-4-2-6z"></path><defs><linearGradient id="AJ" x1="599.908" y1="378.371" x2="590.045" y2="366.816" xlink:href="#B"><stop offset="0" stop-color="#2a2929"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#AJ)" d="M589 365c2 1 3 1 5 1 2 4 4 7 7 10l-3 7c-2-7-6-12-9-18z"></path><path d="M682 393c0-2 1-3 2-4v1h0c-1 3-1 5 0 8 0 2 1 3 1 5 1 1 2 3 3 3-1 1-1 2-2 2h-6v-2c-1-1-1-1 0-2 1-4 1-8 2-11z"></path><path d="M684 398c0 2 1 3 1 5 1 1 2 3 3 3-1 1-1 2-2 2h-6v-2c0-1 0-1 1-1l2-2c1-1 1-3 1-5z" class="C"></path><defs><linearGradient id="AK" x1="322.207" y1="497.867" x2="309.994" y2="504.761" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#2a292a"></stop></linearGradient></defs><path fill="url(#AK)" d="M308 503c2-1 13-7 14-6s1 1 1 3c-4 4-9 5-14 7v-1c1-2 0-2-1-3z"></path><defs><linearGradient id="AL" x1="789.208" y1="355.188" x2="798.518" y2="359.136" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#9d9d9d"></stop></linearGradient></defs><path fill="url(#AL)" d="M790 353l15 7h0c-2 0-2 1-2 2l-1 2-13-6c-1-2 0-3 1-5z"></path><defs><linearGradient id="AM" x1="228.496" y1="293.052" x2="211.142" y2="291.693" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#AM)" d="M213 292l15-3c1 2 2 3 3 5-2 0-5 1-7 1l-3 1h-2c-1-1-2-2-3-2s-3 1-5 0l2-2z"></path><path d="M221 293c1 0 1 0 2 1l1 1-3 1v-3z" class="K"></path><path d="M216 294c2-1 3-1 5-1v3h-2c-1-1-2-2-3-2z" class="M"></path><defs><linearGradient id="AN" x1="610.408" y1="768.509" x2="618.062" y2="776.48" xlink:href="#B"><stop offset="0" stop-color="#313031"></stop><stop offset="1" stop-color="#5f5f60"></stop></linearGradient></defs><path fill="url(#AN)" d="M609 768h1l1 1c1-2 2-2 2-4 3 5 6 11 7 17l-1-1h-3l-7-13z"></path><defs><linearGradient id="AO" x1="377.918" y1="524.933" x2="385.851" y2="528.462" xlink:href="#B"><stop offset="0" stop-color="#2f2d2e"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#AO)" d="M378 521l14 6c-1 1-1 1-3 1-1-1-1-1-2-1-1 1-2 2-2 4l1 1h1v1l-5-1c-1-1-2-4-3-6v-2l-1-1 1-1-1-1z"></path><defs><linearGradient id="AP" x1="415.701" y1="764.158" x2="408.973" y2="776.437" xlink:href="#B"><stop offset="0" stop-color="#1f1f1f"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#AP)" d="M414 757c0 2 0 4-1 6 1 0 2 1 4 2v-1c0-1 0-2 1-2l-5 16-3-1c-1 1 0 1-1 0l5-20z"></path><defs><linearGradient id="AQ" x1="627.493" y1="285.116" x2="620.873" y2="281.216" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#828182"></stop></linearGradient></defs><path fill="url(#AQ)" d="M620 280h2l-1-1c1-1 0-2 1-3 0-1 2-2 2-2l5 12c-1 1-1 2-2 3h0l-1 1-6-10z"></path><defs><linearGradient id="AR" x1="322.295" y1="561.739" x2="312.814" y2="569.176" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient></defs><path fill="url(#AR)" d="M306 570c5-4 11-7 16-10 0 1 1 2 2 3l1 1-9 6c-2 1-3 2-5 2v-1c1 0 2 0 2-1l-2-2c-1 1-3 2-5 2h0z"></path><defs><linearGradient id="AS" x1="274.797" y1="374.775" x2="260.659" y2="383.93" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#444343"></stop></linearGradient></defs><path fill="url(#AS)" d="M261 387c0-2 1-3 2-5 1-4 5-9 8-10l2-1 1 1c0 1-1 2-3 4-4 4-5 8-7 13h-1v-1l-1-2-1 1h0z"></path><defs><linearGradient id="AT" x1="591.634" y1="398.426" x2="585.573" y2="390.209" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#AT)" d="M582 389l2 2c2-1 4-2 6-2l3 6c-1 3-2 5-4 7-2-4-5-8-7-13z"></path><defs><linearGradient id="AU" x1="747.338" y1="460.493" x2="755.071" y2="461.122" xlink:href="#B"><stop offset="0" stop-color="#4c4b4c"></stop><stop offset="1" stop-color="#706f6f"></stop></linearGradient></defs><path fill="url(#AU)" d="M747 457c5 2 10 4 15 7-2 0-4 2-5 3h0l-3-1-7-3c0-1 0 0 1-1h0c0-1-1-2 0-4-1 0-1 0-1-1z"></path><path d="M754 466c1-2 1-3 2-4h1v5l-3-1z" class="D"></path><defs><linearGradient id="AV" x1="242.541" y1="294.955" x2="228.535" y2="299.116" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#AV)" d="M228 296l15-2 2 4-17 3c0-2 1-4 0-5z"></path><path d="M584 595h1c2 2 3 4 4 6 1 3 3 6 4 9s0 4-1 6h-1c-3-6-6-14-8-20l1-1z" class="O"></path><defs><linearGradient id="AW" x1="375.542" y1="631.474" x2="363.405" y2="640.958" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#AW)" d="M363 639h1c4-3 7-7 10-11 1 1 1 2 2 3-1 5-7 11-11 14 0-2 0-4-2-5v-1z"></path><defs><linearGradient id="AX" x1="234.841" y1="471.579" x2="220.589" y2="467.676" xlink:href="#B"><stop offset="0" stop-color="#4f4e4f"></stop><stop offset="1" stop-color="#848283"></stop></linearGradient></defs><path fill="url(#AX)" d="M219 468c1 0 3-2 4-2 4 1 7 3 11 2 1 2 1 2 3 3l1 1c-5 1-9-1-14-1l1 1h-1c-2 0-3 2-5 1l-1-1h2l1-1-2-3z"></path><defs><linearGradient id="AY" x1="361.649" y1="453.101" x2="365.999" y2="446.661" xlink:href="#B"><stop offset="0" stop-color="#1a1919"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#AY)" d="M368 441c1 1 0 2 0 4h1l3-6c0-2 1-2 2-3-2 5-5 10-8 15-1 2-1 3-3 4l-2-1c-1-3 1-7 3-9v-1h1c1-2 2-2 3-3z"></path><path d="M770 154h1c4 1 6 0 8-3l8-8c-2 7-4 12-9 16l-2 1c-1-2-3-3-5-4l-1-1v-1z" class="D"></path><defs><linearGradient id="AZ" x1="434.456" y1="818.609" x2="431.415" y2="831.338" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#AZ)" d="M433 817h1 1c1 1 1 1 2 1l-4 17c-1-1-2-1-3-1l-2 2 5-19z"></path><defs><linearGradient id="Aa" x1="372.512" y1="625.389" x2="362.818" y2="633.148" xlink:href="#B"><stop offset="0" stop-color="#0b0a0b"></stop><stop offset="1" stop-color="#2c2b2b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M360 636l11-13h1c1 1 1 2 0 3-1 4-4 7-6 10l-3 3v1h-2 0l1-1v-1l-4 2v-1l2-2v-1z"></path><defs><linearGradient id="Ab" x1="687.062" y1="565.693" x2="698.268" y2="573.04" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#484747"></stop></linearGradient></defs><path fill="url(#Ab)" d="M693 561l4 14v1c-1 1-1 2-2 2 0-1-1-2-2-3 0-1-1-1-2-2l-3-8c1-2 3-3 5-4z"></path><defs><linearGradient id="Ac" x1="323.57" y1="241.648" x2="312.27" y2="234.084" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#807f81"></stop></linearGradient></defs><path fill="url(#Ac)" d="M313 234v-1s0-1-1-2v-2-1h0c3 1 5 9 8 10l1-1 5 7h-1c-1 0-3 0-5 1h-1l-6-11z"></path><defs><linearGradient id="Ad" x1="334.173" y1="600.548" x2="331.848" y2="608.876" xlink:href="#B"><stop offset="0" stop-color="#464546"></stop><stop offset="1" stop-color="#616061"></stop></linearGradient></defs><path fill="url(#Ad)" d="M335 598v1l-1 1c2 2 4 2 6 2h1c-3 3-5 6-7 8l-1-1c-1 0-3 0-4-1-1 0-3 1-4 1l10-11z"></path><path d="M699 119v1c1-1 1-2 1-3l1-4 1 16v14l-3-6c0-2 0-7-1-9v-10l1 1z" class="G"></path><path d="M198 468v-1s1 0 2-1h0 7 0c2 0 3 1 4 1v1c1 0 1 0 3 1l4 3 1 1c2 1 3-1 5-1h1 0c0 2 1 2-1 3v1h-6c-3-1-8-4-10-6l1-1v-1c-3-1-7-1-9-1-1 1 0 1-2 1h0z" class="Y"></path><path d="M211 468c1 0 1 0 3 1l4 3 1 1c2 1 3-1 5-1h1 0c0 2 1 2-1 3v1h-6c-1-2 0-3-2-4s-4-2-5-4z" class="N"></path><path d="M603 347c1 2 2 4 2 6-1 4-6 8-8 10l-1-1h0c0-1 1-2 1-3l-2-1 2-1c2-3 2-5 1-9l5-1z" class="F"></path><defs><linearGradient id="Ae" x1="263.067" y1="348.621" x2="252.933" y2="359.379" xlink:href="#B"><stop offset="0" stop-color="#121211"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#Ae)" d="M250 356c4-2 9-6 14-7 1 1 2 1 2 3h0c-3 3-6 5-10 6-1 0-1 0-2 1-1 0-1 0-2-1-1 0-1-1-1-1l-1-1z"></path><path d="M739 205c1-5 2-14 6-17h1v1h2l-9 21c0-2-1-3 0-5z" class="E"></path><path d="M631 704h1l5 27 4 16c0 4 1 7 1 10-2-5-3-11-4-16-3-13-6-25-7-37z" class="B"></path><defs><linearGradient id="Af" x1="529.419" y1="546.969" x2="532.298" y2="532.973" xlink:href="#B"><stop offset="0" stop-color="#201e1f"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#Af)" d="M532 527c1 2 1 3 1 4 0 3-1 6-1 9 0 4 1 8 1 12-2-3-4-8-4-12-1-3 0-6 0-9 0 0-1-2-1-3l4-1z"></path><path d="M532 527c1 2 1 3 1 4l-1 2h-2v-2h-1s-1-2-1-3l4-1z" class="F"></path><defs><linearGradient id="Ag" x1="805.407" y1="333.973" x2="816.102" y2="340.329" xlink:href="#B"><stop offset="0" stop-color="#828182"></stop><stop offset="1" stop-color="#b5b5b5"></stop></linearGradient></defs><path fill="url(#Ag)" d="M803 332l18 8c-2 0-2 0-3-1-1 1-1 1-1 3 1 0 1 0 2 1l-19-8 1-1h0c1-1 1-2 2-2z"></path><defs><linearGradient id="Ah" x1="803.54" y1="291.745" x2="814.505" y2="293.469" xlink:href="#B"><stop offset="0" stop-color="#232223"></stop><stop offset="1" stop-color="#545354"></stop></linearGradient></defs><path fill="url(#Ah)" d="M803 290l17 2c0 1 1 1 0 2s0 1-1 2c-4 1-11-1-16-1v-1c1-2 1-3 0-4z"></path><defs><linearGradient id="Ai" x1="291.941" y1="451.125" x2="280.905" y2="457.185" xlink:href="#B"><stop offset="0" stop-color="#363637"></stop><stop offset="1" stop-color="#6f6e6d"></stop></linearGradient></defs><path fill="url(#Ai)" d="M289 448h1c1 1 1 2 1 4-3 4-7 11-11 14h-1v-1l3-2v-2-1c0-1 0-2 1-3-2 2-4 6-6 6l12-15z"></path><path d="M775 215h1c1 1 1 1 1 2v6c1 5 2 13 0 18-2-1-3-11-3-13-1-5 0-9 1-13z" class="R"></path><path d="M592 648c0-7 2-16 4-23l1-1c0-2 2-4 4-6-1 2-2 4-2 7l-3 14c-1 4-3 9-3 14-1 0-1 0-2-1l1-1v-3z" class="F"></path><defs><linearGradient id="Aj" x1="810.08" y1="299.124" x2="816.529" y2="299.499" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#727271"></stop></linearGradient></defs><path fill="url(#Aj)" d="M816 297h6l2 4 1 1h-19c1-1 0-1 1-2v-1c1-1 2-1 3-2h6z"></path><defs><linearGradient id="Ak" x1="471.885" y1="391.492" x2="472.089" y2="376.021" xlink:href="#B"><stop offset="0" stop-color="#504e4f"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#Ak)" d="M470 385c-1-5-1-10-1-14 1 1 0 0 2 1h1c0 5 0 8 2 12 0 1 1 3 1 4 1 1-2 5-2 6-1-1-3-3-3-4s1-3 0-5z"></path><defs><linearGradient id="Al" x1="226.637" y1="257.958" x2="210.948" y2="255.895" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#Al)" d="M211 254l12 1h3c1 0 1 1 1 1 1 1 1 2 1 3-2 2-12 1-15 1h0l2-1-4-2v-3z"></path><path d="M360 271c4 9 5 19 5 28 1 4 1 8 1 13-3-10-3-20-6-29 0-4-1-8 0-12z" class="J"></path><path d="M805 141c1 2 1 5 1 7v1c-1 5-2 9-5 12-3 6-9 14-15 16l-1-1v-1l8-6c8-8 12-17 12-28z" class="U"></path><path d="M493 650c3 5 7 33 5 38l-1 1c-2-13-4-26-4-39z" class="X"></path><path d="M355 451l2 1h-1v1l17 14c-9-3-16-7-22-13l4-3z" class="O"></path><path d="M309 150c4-6 9-11 15-16 1-2 3-4 5-4l1-1h1 2 0c-1 2-3 3-5 5-5 4-10 9-14 14h-1v-1c-1 1-3 3-4 3z" class="V"></path><path d="M348 439c2 0 2-1 4-2 3-3 6-6 9-10 0 2-1 3-2 4v3c-1 1-2 3-2 4-1 3-3 6-5 8l-5-4c0-1 0-1 1-2v-1z" class="I"></path><defs><linearGradient id="Am" x1="626.376" y1="762.878" x2="632.124" y2="766.122" xlink:href="#B"><stop offset="0" stop-color="#969696"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#Am)" d="M626 752c3 6 5 12 9 17-1 0-1 0-2-1v1c0 1 0 0-1 1l1 1-1 2c-4-5-6-10-9-16l3-5z"></path><path d="M605 573c-5 7-11 11-20 14l14-15c1 1 2 1 4 1h2z" class="N"></path><defs><linearGradient id="An" x1="615.662" y1="332.072" x2="603.755" y2="325.313" xlink:href="#B"><stop offset="0" stop-color="#1f1e1f"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#An)" d="M605 321l1 1 9 8c0 2 0 5 1 7l-1 1v1c-3-6-8-10-12-15l-2-2c2 0 3 0 4-1z"></path><path d="M605 321l1 1v1c-1 1-2 1-3 1h0l-2-2c2 0 3 0 4-1z" class="F"></path><defs><linearGradient id="Ao" x1="418.887" y1="254.471" x2="423.122" y2="237.903" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#Ao)" d="M423 230c1 0 2 0 2 1 1 0 2 2 3 2l-1 1v1l-8 24c0-2-2-7-1-8 0-2 2-5 3-7 1-3 2-7 2-10v-4z"></path><path d="M423 230c1 0 2 0 2 1 1 0 2 2 3 2l-1 1v1c-1 1-2 1-2 3h0-1c0-2 0-3-1-4v-4z" class="F"></path><defs><linearGradient id="Ap" x1="627.233" y1="307.067" x2="616.038" y2="299.784" xlink:href="#B"><stop offset="0" stop-color="#272727"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#Ap)" d="M619 294l9 12-3 8-2-6c-1-4-4-6-7-10 2-1 2-2 3-4z"></path><path d="M685 666l5-2c1 4 5 12 4 16h0l-2 1-2-2-5-13z" class="V"></path><defs><linearGradient id="Aq" x1="321.359" y1="554.971" x2="311.239" y2="561.279" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#Aq)" d="M321 552c0 2 0 3 2 4v-1c1 0 1 0 2 1-5 4-12 9-18 12v-1c1-1 2-1 3-2l2-1-1-3-1 1c-1 0-4 3-5 3h-1l17-13z"></path><defs><linearGradient id="Ar" x1="629.806" y1="751.764" x2="633.929" y2="762.598" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#abaaab"></stop></linearGradient></defs><path fill="url(#Ar)" d="M627 751l1-1c1-1 1-2 3-2 2 6 4 11 7 16-1 1-3 1-4 3-3-5-5-10-7-16z"></path><defs><linearGradient id="As" x1="388.466" y1="536.684" x2="394.519" y2="544.437" xlink:href="#B"><stop offset="0" stop-color="#5d5b5b"></stop><stop offset="1" stop-color="#757476"></stop></linearGradient></defs><path fill="url(#As)" d="M397 544l-13-1c-1-2-1-3-1-4l1-1 9 1c3 2 10 2 14 2l2 1c-4 2-8 0-12 2z"></path><path d="M834 259l-13 4-2-4 23-7v1h2c-3 2-10 2-11 5l1 1z" class="Q"></path><defs><linearGradient id="At" x1="619.547" y1="726.667" x2="629.009" y2="743.431" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#At)" d="M626 746l-4-13c0-2-1-4-1-5 0-2 1-4 2-6l6 22c-1 0-2 1-3 2z"></path><path d="M342 175c2 0 7-1 8 0l-3 1-1 4-6 1-18 2 1-1 2-1h1v-1h3c3-1 8-1 9-3 1-1 1-1 1-2h3z" class="K"></path><path d="M340 181l1-2c1-2 3-3 6-3l-1 4-6 1z" class="H"></path><defs><linearGradient id="Au" x1="763.128" y1="381.023" x2="769.408" y2="381.567" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#Au)" d="M775 387l-13-3c0-2 1-4 2-5 5-1 10 2 15 3-2 1-3 2-4 5z"></path><defs><linearGradient id="Av" x1="365.118" y1="668.834" x2="357.505" y2="679.007" xlink:href="#B"><stop offset="0" stop-color="#4a4a4a"></stop><stop offset="1" stop-color="#767576"></stop></linearGradient></defs><path fill="url(#Av)" d="M362 663c0 4 0 3 3 6v-1h1c-2 7-3 13-3 20v-5h-1c-1 0-2 0-4 1 1-7 2-14 4-21z"></path><defs><linearGradient id="Aw" x1="730.78" y1="462.402" x2="744.825" y2="467.625" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#Aw)" d="M747 471c-6-3-12-4-18-5 0-2 1-3 3-4 4-1 9 1 13 2v1 4h1c0 1 1 1 1 2z"></path><path d="M814 159c1 2-1 3-2 5v-1-1c-2-4 0-8-1-12l-1-1v-2-1c-1 0-1-1-1-1v-1c-1-1-1-2-2-3 0-1-1-2-2-3h0v-1-1c1-1 1-1 3 0l-1 1 1 1c6 5 5 14 6 21z" class="I"></path><path d="M329 134c2-1 4-3 7-4l3-1-15 14c-2 2-5 5-7 8h-2-1 0c4-7 10-12 15-17z" class="L"></path><path d="M315 151l2-2c1-2 3-5 4-6s2 0 3 0c-2 2-5 5-7 8h-2z" class="U"></path><defs><linearGradient id="Ax" x1="794.883" y1="364.859" x2="806.073" y2="369.94" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#cccbcb"></stop></linearGradient></defs><path fill="url(#Ax)" d="M793 362l2 1 16 7v1l-4-2c-1 1-1 2-1 3h0c1 0 1 1 2 1h1c0 1 1 2 1 2l-17-8h-1c0-2 0-4 1-5z"></path><path d="M793 362l2 1h0v1c0 1 0 2-1 2h-1v1h-1c0-2 0-4 1-5z" class="Y"></path><defs><linearGradient id="Ay" x1="486.999" y1="410.499" x2="494.524" y2="393.078" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#c9c7c8"></stop></linearGradient></defs><path fill="url(#Ay)" d="M485 408c2-7 5-11 10-15l2 1h1c0-1 1-1 1-1l-2 5-1 2h-2l-1-1v1c-3 3-4 7-5 12h-1c-1-1-2-3-2-4z"></path><path d="M493 399c1-1 1-2 2-3l2 2-1 2h-2l-1-1z" class="P"></path><path d="M534 776h1l1 1s0 2-1 3l-2 2c-2 2-5 4-7 6l-2 2-2-2c0-1 0-1-1-1l-1-1c3-4 10-8 14-10z" class="G"></path><path d="M414 757c1-3 2-9 5-11h3c0 1 0 2-1 3h0c1 4-1 9-3 13-1 0-1 1-1 2v1c-2-1-3-2-4-2 1-2 1-4 1-6z" class="R"></path><defs><linearGradient id="Az" x1="600.876" y1="811.098" x2="607.787" y2="826.771" xlink:href="#B"><stop offset="0" stop-color="#3c3c3c"></stop><stop offset="1" stop-color="#717171"></stop></linearGradient></defs><path fill="url(#Az)" d="M601 812c2-1 3-3 5-2 1 6 1 13 1 20 0 2 0 5-1 7v-8h-2v3 7l-1-1c1-9 0-18-2-26z"></path><defs><linearGradient id="BA" x1="683.646" y1="576.037" x2="693.576" y2="582.099" xlink:href="#B"><stop offset="0" stop-color="#1a1a19"></stop><stop offset="1" stop-color="#464546"></stop></linearGradient></defs><path fill="url(#BA)" d="M688 571c1 2 2 6 3 9 1 2 2 5 3 8-2 1-3 2-4 4-2-5-5-11-5-16 1-1 0-2 1-3l1-1 1-1z"></path><defs><linearGradient id="BB" x1="440.924" y1="800.055" x2="433.679" y2="816.708" xlink:href="#B"><stop offset="0" stop-color="#080707"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#BB)" d="M433 817c2-6 3-14 6-20l3 5-5 16c-1 0-1 0-2-1h-1-1z"></path><defs><linearGradient id="BC" x1="419.083" y1="258.071" x2="430.414" y2="240.935" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#504f4e"></stop></linearGradient></defs><path fill="url(#BC)" d="M426 240h4c0-1 1-2 1-3-1 7-3 14-6 20-1 3-2 5-4 7 0-5 1-9 2-14 1-3 3-7 3-10z"></path><path d="M455 276l2-2c0 1 1 2 2 2h0c1 2 2 4 2 6 1 2-1 6-2 8l-4 7c0-1 0-2 1-3l-1-2c-1 0 0-4 0-5 2-4 1-7 0-11z" class="S"></path><defs><linearGradient id="BD" x1="417.203" y1="777.426" x2="415.868" y2="791.813" xlink:href="#B"><stop offset="0" stop-color="#343333"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#BD)" d="M416 776c1 1 3 2 4 2h1l1-1v1l-4 11c0 1-1 3-1 3h-1v-1c-1 0-1 1-2 1 0 1-2 1-3 1l5-17z"></path><path d="M753 124h1c4-2 12-1 17-1 2 0 5-1 7-1-1 3-3 5-6 6-2-1-5-1-8-1-1-1-1 0-2 0s-8-2-9-3z" class="D"></path><defs><linearGradient id="BE" x1="823.447" y1="292.184" x2="836.312" y2="296.555" xlink:href="#B"><stop offset="0" stop-color="#949294"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#BE)" d="M820 292c8 0 16 2 24 1h-1c-2 1-3 1-6 1 0 1 0 2 1 2s2 1 3 0h5c-8 2-18 1-27 0 1-1 0-1 1-2s0-1 0-2z"></path><path d="M360 283c3 9 3 19 6 29 0 4 0 9 2 12l-1 1c-2-3-4-5-6-7l1-1c1 1 2 3 3 3-3-9-3-20-5-30v-7z" class="G"></path><defs><linearGradient id="BF" x1="208.215" y1="224.666" x2="194.834" y2="221.618" xlink:href="#B"><stop offset="0" stop-color="#2c2b2b"></stop><stop offset="1" stop-color="#5c5c5d"></stop></linearGradient></defs><path fill="url(#BF)" d="M195 216l1-3 1 2c2 1 3 2 5 4l6 7v1h0c0 2-1 4-1 5-5-5-8-11-13-16h1z"></path><path d="M195 216l1-3 1 2-2 1h0z" class="K"></path><path d="M677 671v-4h0l2-3c1 0 1 0 2 1 1 3 4 10 2 13-1 2-3 3-5 4h-1v-4-1c1-1 0-4 0-6z" class="Q"></path><defs><linearGradient id="BG" x1="776.214" y1="376.101" x2="784.625" y2="377.602" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#BG)" d="M776 374c5 1 10 1 15 2l-1 2c0 1 0 2-1 2v1l-14-2v-1c-1-2 0-3 1-4z"></path><defs><linearGradient id="BH" x1="374.052" y1="317.932" x2="387.022" y2="326.164" xlink:href="#B"><stop offset="0" stop-color="#b5b6b7"></stop><stop offset="1" stop-color="#ece8ea"></stop></linearGradient></defs><path fill="url(#BH)" d="M380 310l1 3 3 12 2 7-1-1h-1c0 1-1 2-1 3v3c-1-3-1-6-2-8l-4-16 2-1c0-1 0-1 1-2z"></path><path d="M437 517c-1-1-5-8-5-9-3-4-10-13-9-18 0-2 0-2 1-3 0 0 1 0 2 1s2 2 3 4v1 4c1 1 1 2 1 3 1 1 1 1 1 2v1h0c1 3 3 7 4 10l1 1 1 2v1z" class="J"></path><path d="M345 123c4 2 10 4 14 4 1 2 1 4 2 5-2 0-5-1-7-1-5-1-10-3-15-6 1-1 4-2 6-2z" class="N"></path><defs><linearGradient id="BI" x1="786.418" y1="322.558" x2="796.216" y2="324.235" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#BI)" d="M801 329c-6-2-11-3-16-3 1-3 2-5 5-6 5 1 10 4 15 6-2 1-3 1-4 2v1z"></path><defs><linearGradient id="BJ" x1="465.504" y1="296.747" x2="477.496" y2="296.753" xlink:href="#B"><stop offset="0" stop-color="#c4c1c1"></stop><stop offset="1" stop-color="#eaeaeb"></stop></linearGradient></defs><path fill="url(#BJ)" d="M464 304c3-2 6-6 9-9 6-9 7-19 5-29 4 6 2 17 0 24 0 2-1 5-2 7l-3 3c-2 2-3 2-3 4l-1 1h-2c-1 1 0 1-1 1 0-1-1-1-2-2z"></path><path d="M323 123h1l1 1-27 26h-1 0c2-7 19-24 26-27z" class="V"></path><defs><linearGradient id="BK" x1="251.178" y1="358.406" x2="238.016" y2="365.53" xlink:href="#B"><stop offset="0" stop-color="#4a4a4a"></stop><stop offset="1" stop-color="#868586"></stop></linearGradient></defs><path fill="url(#BK)" d="M234 367l16-11 1 1s0 1 1 1c1 1 1 1 2 1 1-1 1-1 2-1l-19 12-3-3z"></path><path d="M251 357s0 1 1 1c-1 0-1 0-1-1z" class="H"></path><defs><linearGradient id="BL" x1="298.502" y1="526.502" x2="287.723" y2="536.247" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#BL)" d="M285 538c4-5 7-11 13-14v1c1 2 1 2 2 3l1-1 1 1c-5 3-8 7-11 11-1 0-1-1-2-1 0-1-1-1-2-1 0 0-1 1-2 1h0z"></path><defs><linearGradient id="BM" x1="733.503" y1="455.341" x2="747.893" y2="459.703" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#4c4b4c"></stop></linearGradient></defs><path fill="url(#BM)" d="M747 463l-10-3c-2 0-4-1-5-2 1-2 2-3 3-4 4-1 8 1 12 3 0 1 0 1 1 1-1 2 0 3 0 4h0c-1 1-1 0-1 1z"></path><defs><linearGradient id="BN" x1="214.899" y1="218.419" x2="196.326" y2="211.479" xlink:href="#B"><stop offset="0" stop-color="#181918"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient></defs><path fill="url(#BN)" d="M198 204c4 5 9 9 13 14 2 3 4 6 5 9-5-4-10-8-15-13-1-2-2-4-4-5l1-5z"></path><path d="M555 648l22 21-2 2c-7-6-14-11-20-19v-4z" class="Y"></path><defs><linearGradient id="BO" x1="495.415" y1="656.53" x2="509.216" y2="679.689" xlink:href="#B"><stop offset="0" stop-color="#343433"></stop><stop offset="1" stop-color="#606062"></stop></linearGradient></defs><path fill="url(#BO)" d="M499 646h0 0c1 2 2 4 2 6 2 6 3 13 4 20 0 4 2 8 1 11-1 1-1 1-2 1-1-3-1-6-2-9l-3-29z"></path><defs><linearGradient id="BP" x1="305.085" y1="472.507" x2="293.722" y2="482.076" xlink:href="#B"><stop offset="0" stop-color="#181717"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#BP)" d="M304 470v2c1 0 2 1 3 1h0l-14 14-1 1c0-2-1-3-2-5l2-2 12-11z"></path><path d="M292 481c1 2 1 4 1 6l-1 1c0-2-1-3-2-5l2-2z" class="O"></path><defs><linearGradient id="BQ" x1="699.606" y1="541.425" x2="708.149" y2="549.833" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#BQ)" d="M705 560c0-3 0-6-1-9h0c-1-3-4-5-5-8 1-2 2-3 3-5 4 4 7 8 7 14v1c-1 0-1 1-2 2l-1 1-1 1v3z"></path><defs><linearGradient id="BR" x1="457.773" y1="518.451" x2="444.677" y2="499.243" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#BR)" d="M445 500c2-1 2-2 3-3l11 25c-1 3 0 4-1 7l-13-29z"></path><defs><linearGradient id="BS" x1="647.035" y1="655.043" x2="657.524" y2="663.29" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#494848"></stop></linearGradient></defs><path fill="url(#BS)" d="M652 671c0-2-1-5-2-7 0-3 0-5-1-7 0-4 1-6 3-9l4 19 1 2c-2 1-3 3-4 5l-1-3z"></path><path d="M652 671c0-1 1-2 1-2 2-2 2-2 3-2l1 2c-2 1-3 3-4 5l-1-3z" class="H"></path><defs><linearGradient id="BT" x1="306.17" y1="461.694" x2="293.947" y2="468.211" xlink:href="#B"><stop offset="0" stop-color="#0b0a0a"></stop><stop offset="1" stop-color="#353434"></stop></linearGradient></defs><path fill="url(#BT)" d="M295 468h1c2-3 4-8 7-11 1 0 3 1 3 3l1 2c-3 5-6 9-10 13 0-3 0-4-2-7z"></path><defs><linearGradient id="BU" x1="566.821" y1="611.031" x2="580.134" y2="616.575" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#787779"></stop></linearGradient></defs><path fill="url(#BU)" d="M569 601l11 20-5 1c-3-3-5-8-6-12-1-2-1-4-2-5l1-1c1-1 1-2 1-3z"></path><path d="M482 781l28 78h-1l-27-74h0l1 1c-1-2-1-3-1-5z" class="X"></path><defs><linearGradient id="BV" x1="319.184" y1="491.995" x2="308.316" y2="494.005" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#BV)" d="M301 499l11-7c1-1 3-3 5-4h1c1 2 2 2 2 4s-2 2-4 3c-5 4-9 7-14 9h-1c1 0 1-1 2-2s2 0 3-2v-1h-1l-1-1c-1 1-2 1-3 1z"></path><defs><linearGradient id="BW" x1="708.495" y1="556" x2="707.963" y2="566.473" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#949393"></stop></linearGradient></defs><path fill="url(#BW)" d="M705 557l1-1 1-1c1-1 1-2 2-2 1 4-1 12 2 15l1 1-1 1v5c-2-1-3-2-4-4-3-4-3-7-2-11v-3z"></path><path d="M705 557h3c-1 5-2 10-1 14-3-4-3-7-2-11v-3z" class="D"></path><defs><linearGradient id="BX" x1="653.265" y1="674.476" x2="661.42" y2="687.827" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#BX)" d="M653 674c1-2 2-4 4-5 2 7 4 15 5 23l-1-2v-1c-1 1-2 1-3 2v2l-5-19z"></path><path d="M718 247v4c-2 3-4 5-6 7-5 3-13 5-18 4l-1-1c1-1 3-1 5-2l9-3c4-2 7-6 11-9z" class="W"></path><defs><linearGradient id="BY" x1="645.174" y1="230.609" x2="636.729" y2="218.667" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#BY)" d="M638 210c4 5 7 10 7 16l-1 7h-2c-2 0-2-1-3-3 1-3 0-6 0-8l-1-3 1-2c0-3 0-5-1-7z"></path><defs><linearGradient id="BZ" x1="577.529" y1="832.436" x2="592.471" y2="856.564" xlink:href="#B"><stop offset="0" stop-color="#1d1d1e"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#BZ)" d="M585 823c2 4 2 10 3 15 0 11-2 22-4 33l-1 1c0-2 0-4 1-6v-13l1-30z"></path><defs><linearGradient id="Ba" x1="597.092" y1="789.973" x2="603.003" y2="810.354" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#Ba)" d="M601 812c-1-7-3-13-6-19l3-5c3 7 6 14 8 22-2-1-3 1-5 2z"></path><defs><linearGradient id="Bb" x1="772.586" y1="357.717" x2="780.399" y2="360.285" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#Bb)" d="M787 365c-2-1-5-2-8-2-2-1-6-1-7-1s-1-1-1-1l1-2c0-1 0-2 1-2 5-2 11 0 16 3l-2 5z"></path><path d="M327 149c5 2 9 5 14 8l7 4c1 0 3 1 3 2 1 0 1 0 1 1h-2c-8-1-16-6-22-11-1-2-1-3-1-4z" class="Q"></path><defs><linearGradient id="Bc" x1="599.757" y1="844.047" x2="606.277" y2="845.826" xlink:href="#B"><stop offset="0" stop-color="#747272"></stop><stop offset="1" stop-color="#a1a1a2"></stop></linearGradient></defs><path fill="url(#Bc)" d="M603 838l1 1v-7-3h2v8l-3 26c-1 0-2-1-3-2v-5c1-1 1-3 1-4 1-2 1-4 1-6 1-2 1-5 1-8z"></path><defs><linearGradient id="Bd" x1="621.716" y1="502.737" x2="620.115" y2="514.36" xlink:href="#B"><stop offset="0" stop-color="#7b7a7a"></stop><stop offset="1" stop-color="#a8a7a7"></stop></linearGradient></defs><path fill="url(#Bd)" d="M620 499h1v1c-1 1 0 2 0 3 1 0 2 1 4 1 0-1 0-3 1-4v-1-2 1l-4 23-1-2 1-1c0-1-1-1 0-2 0-1 0-1-1-1h0c-1-1-2 0-3 0-1 2-1 7-3 8l3-15c1-3 1-6 2-9z"></path><defs><linearGradient id="Be" x1="464.898" y1="756.974" x2="489.602" y2="776.526" xlink:href="#B"><stop offset="0" stop-color="#3a3935"></stop><stop offset="1" stop-color="#5f6165"></stop></linearGradient></defs><path fill="url(#Be)" d="M482 785c-4-11-9-23-11-35 0-2 0-4 1-6 1 1 1 5 2 6l5 18 3 13c0 2 0 3 1 5l-1-1h0z"></path><defs><linearGradient id="Bf" x1="644.916" y1="539.219" x2="636.298" y2="522.691" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#a6a6a6"></stop></linearGradient></defs><path fill="url(#Bf)" d="M640 544c-1-8-6-18-1-25v-1l1-1v1c0-1 1-2 1-3h0l2 18c-1-2-2-5-3-7-1 3 2 9 3 12-1 2-1 4-3 6z"></path><path d="M328 134h1c-5 5-11 10-15 17h0 1 2l-1 3c0 1-1 2-1 3l-1 2-1 1c-2-2-5-3-7-5 1-1 1-3 3-4v-1c1 0 3-2 4-3v1h1c4-5 9-10 14-14z" class="R"></path><path d="M321 215c-2 0-13-12-14-14-1-1 0-1 0-2 0-2-1-2-1-4l-1-2h1l4 4c0 3 4 6 6 8 0 2 2 4 4 5 2 3 5 6 8 9-3 0-4-3-7-4z" class="D"></path><defs><linearGradient id="Bg" x1="744.608" y1="466.809" x2="752.178" y2="469.076" xlink:href="#B"><stop offset="0" stop-color="#4a4949"></stop><stop offset="1" stop-color="#6f6e6f"></stop></linearGradient></defs><path fill="url(#Bg)" d="M747 471c0-1-1-1-1-2h-1v-4-1c7 2 13 5 18 9 0 1-1 2-1 4h0l-15-6z"></path><defs><linearGradient id="Bh" x1="340.635" y1="571.336" x2="327.535" y2="576.596" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#4d4c4c"></stop></linearGradient></defs><path fill="url(#Bh)" d="M340 567l1 1-1 1 1 3c1 0 1 0 2-1h1l-21 13 1-1v-1h1 0c2-1 3-1 4-3l-1-2h-2c1 1 0 2 0 4h-1v-3c-1 1-3 1-4 2l19-13z"></path><defs><linearGradient id="Bi" x1="627.39" y1="262.59" x2="617.81" y2="247.952" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#Bi)" d="M618 250l1-3h3l-1-1c0-1 0-2 1-4h0c1 8 3 13 7 20-1 1-3 3-5 2h-1l-5-14z"></path><defs><linearGradient id="Bj" x1="361.973" y1="685.767" x2="359.479" y2="699.496" xlink:href="#B"><stop offset="0" stop-color="#969697"></stop><stop offset="1" stop-color="#c0bfc0"></stop></linearGradient></defs><path fill="url(#Bj)" d="M358 684c2-1 3-1 4-1h1v5c1 7 1 14 3 21l-2-2v-1c-1-1-1-2-1-4v-2h-3v4c0 1 0 2 1 3h-1c-2-7-2-16-2-23z"></path><defs><linearGradient id="Bk" x1="429.786" y1="290.133" x2="439.872" y2="270.194" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#Bk)" d="M438 268l3 3h1c-4 8-9 15-10 24-1-2-3-7-3-9 0-1 2-3 2-4l7-14z"></path><defs><linearGradient id="Bl" x1="427.327" y1="820.248" x2="423.549" y2="837.833" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#Bl)" d="M427 816c2 1 3 1 4 3l-6 23c0-1 0-2-1-4h-3c1-2 1-5 2-7l4-15z"></path><path d="M614 795h4l1 6-5 1c1 4 3 8 5 12l-4 3c-1-3-2-6-3-8-1-3-2-6-2-9 1-1 3-1 4-2v-3z" class="Y"></path><defs><linearGradient id="Bm" x1="722.522" y1="482.967" x2="733.625" y2="486.581" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#Bm)" d="M741 495c-2-1-4-3-6-4-3-2-12-5-13-7 0-2 0-3 1-4 7 2 14 7 20 11-1 2-1 3-2 4z"></path><defs><linearGradient id="Bn" x1="720.928" y1="385.947" x2="708.258" y2="392.759" xlink:href="#B"><stop offset="0" stop-color="#111012"></stop><stop offset="1" stop-color="#414140"></stop></linearGradient></defs><path fill="url(#Bn)" d="M709 395c3-4 5-8 7-13 1-1 1-2 3-3h1c0 8-5 17-9 24-1 3-3 6-5 9h0c-1-1 0-1 0-2 0 0 0-1 1-1v-1l1-1c0-1 0-1 1-1v-1c0-1 1-1 1-2v-1c1-1 1-2 1-3h0c0-2-1-3-2-4z"></path><defs><linearGradient id="Bo" x1="565.033" y1="450.787" x2="558.232" y2="436.16" xlink:href="#B"><stop offset="0" stop-color="#4e4d4e"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#Bo)" d="M561 435l6 14c-1 2-1 4-4 6l-8-19 2 1c1-1 2-1 3-1h1v-1z"></path><path d="M310 336c0-2-1-5-1-8-1-6 1-12 2-18 2 3 2 9 3 12l1 3c-4 5-1 12-2 17l-1 1-2-7z" class="J"></path><path d="M310 336c1-4 1-9 2-13 1 0 1 1 2 1 0-1-1 0 0-1v-1l1 3c-4 5-1 12-2 17l-1 1-2-7z" class="a"></path><path d="M568 422c2 1 3 1 4 2l1 2c-2 1-4 2-5 3l-1-1c-2 0-5 2-7 3 0 1 1 3 1 4v1h-1c-1 0-2 0-3 1l-2-1-1-3c1-1 1-2 2-3s2-4 3-5c1 0 5 0 6-1h0 0c1-1 2-1 3-2z" class="Y"></path><defs><linearGradient id="Bp" x1="828.3" y1="262.237" x2="838.98" y2="264.056" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#Bp)" d="M818 266l22-7c0 1 1 2 0 3 0 1-1 1-2 2v1c-2 1-5 2-7 2-3 1-7 2-10 4-1-2-2-3-3-5z"></path><defs><linearGradient id="Bq" x1="817.759" y1="242.05" x2="836.628" y2="225.902" xlink:href="#B"><stop offset="0" stop-color="#040303"></stop><stop offset="1" stop-color="#504e4f"></stop></linearGradient></defs><path fill="url(#Bq)" d="M835 228c0 1-1 2-2 3l-7 9c-2 2-4 4-7 5l-1-1c4-8 11-15 17-22v6z"></path><path d="M202 248c5 0 11-1 16 0 2 0 4 0 6 1 0 1 1 2 1 3-3 1-6 1-8 1h-3-7l-2-1v-1l-3-3h0z" class="I"></path><path d="M212 248h3l-1 1 1 1c1 1 1 2 2 3h-3v-2c0-1-2-2-2-3z" class="a"></path><path d="M209 248h3c0 1 2 2 2 3v2h-7l2-1c1 0 2 1 3 0-1-2-2-3-3-4z" class="B"></path><path d="M202 248h3 4c1 1 2 2 3 4-1 1-2 0-3 0l-2 1-2-1v-1l-3-3z" class="T"></path><path d="M202 248h3l4 4-2 1-2-1v-1l-3-3z" class="H"></path><path d="M752 271c0-1-1-2-1-3 2-6 10-12 15-15 2-1 5-3 7-2v1c-2 2-4 5-7 6h0c-1 1-1 1-2 1l-2 2c-2 1-5 4-6 6-1 1-2 4-4 4z" class="T"></path><defs><linearGradient id="Br" x1="374.192" y1="300.119" x2="378.274" y2="310.701" xlink:href="#B"><stop offset="0" stop-color="#8b8a8b"></stop><stop offset="1" stop-color="#acaaac"></stop></linearGradient></defs><path fill="url(#Br)" d="M369 273c1 1 2 3 2 5l6 23c1 3 1 6 3 8v1c-1 1-1 1-1 2l-2 1-7-26 1-2-1-5c-1-3-1-5-1-7z"></path><path d="M384 875h0c2 0 3-2 3-3h1 1l-1 1-1 1c0 1-1 2-1 2 0 1 1 2 1 3-6 9-15 17-25 21-1 0-2 1-3 0 11-4 19-15 25-25z" class="S"></path><defs><linearGradient id="Bs" x1="453.267" y1="319.292" x2="465.113" y2="306.048" xlink:href="#B"><stop offset="0" stop-color="#7a7879"></stop><stop offset="1" stop-color="#b4b3b3"></stop></linearGradient></defs><path fill="url(#Bs)" d="M459 309c2-1 4-3 5-5 1 1 2 1 2 2 1 0 0 0 1-1h2l-12 16v1c-2 0-4 1-5 0v-1l7-12z"></path><path d="M452 321h5v1c-2 0-4 1-5 0v-1z" class="M"></path><defs><linearGradient id="Bt" x1="741.092" y1="486.501" x2="749.908" y2="490.499" xlink:href="#B"><stop offset="0" stop-color="#818181"></stop><stop offset="1" stop-color="#b1b0b1"></stop></linearGradient></defs><path fill="url(#Bt)" d="M737 479l16 13c-1 0-1 0-2-1v1 2 2l-17-12-1-1v-1c1 0 0 0 1-1l3-2z"></path><path d="M341 410h0l3-4c2 1 2 3 4 4h0c2 2 6 8 6 11-2 1-3 1-4 1-4-3-7-6-9-11v-1z" class="X"></path><defs><linearGradient id="Bu" x1="262.516" y1="344.568" x2="251.342" y2="347.121" xlink:href="#B"><stop offset="0" stop-color="#0f0e0f"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#Bu)" d="M249 348c4-2 8-5 12-8l3 5-13 8-4 3c0-2 1-4 0-6l2-2z"></path><path d="M249 348h2c-1 2 0 4 0 5l-4 3c0-2 1-4 0-6l2-2z" class="C"></path><defs><linearGradient id="Bv" x1="637.152" y1="284.185" x2="629.818" y2="269.33" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#706f6f"></stop></linearGradient></defs><path fill="url(#Bv)" d="M631 265c3 5 6 9 8 13l-3 9-9-15 1-1c1-1 2-1 3-2s0-3 0-4z"></path><path d="M515 796c-2-4-3-9-5-14-1-2-2-5-2-7 0-1 1-3 2-4 0 1 1 1 1 3l5 9v1 1c1 1 1 3 2 4v3l2 3-1 1c-2-2-4-5-5-7l2 7h-1z" class="X"></path><path d="M518 792c-3-3-5-7-6-11-1-2-2-5-1-7l5 9v1 1c1 1 1 3 2 4v3z" class="Z"></path><defs><linearGradient id="Bw" x1="422.274" y1="236.192" x2="430.226" y2="222.808" xlink:href="#B"><stop offset="0" stop-color="#5a5959"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#Bw)" d="M420 217l2-2c1 2 2 3 3 4 3 1 6 5 7 8 1 4 0 7-1 10 0 1-1 2-1 3h-4c1-2 1-4 1-6 1-1 1-1 1-2 1-6-5-10-8-14h0v-1z"></path><path d="M420 217l2-2c1 2 2 3 3 4h-2c-1-1-2-1-3-1v-1z" class="N"></path><defs><linearGradient id="Bx" x1="562.361" y1="389.244" x2="560.319" y2="371.336" xlink:href="#B"><stop offset="0" stop-color="#bdbcbc"></stop><stop offset="1" stop-color="#f0eff0"></stop></linearGradient></defs><path fill="url(#Bx)" d="M564 391c-1-8-6-16-13-20-2-2-5-3-7-4h1c12 3 18 10 24 20l-1 2c-1-1-1 0-1-1-1 1-1 1-1 2l-1-1-1 2z"></path><defs><linearGradient id="By" x1="626.518" y1="804.025" x2="620.482" y2="832.975" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#By)" d="M623 808c1-1 1-3 1-4 1 0 1 0 2 1 0 10 0 22-3 32v-1h0c-1-2-1-2-1-3v-2c-1-2-1-2-1-3v-2c-1-2-1-4-1-6l1-1v-2-1h1c1-3 1-6 1-8z"></path><path d="M427 841c1 0 3 0 4 1l-5 16 5 4v1h-2c-2 0-2 2-4 1l-2-4c0-1-1-1-1-2 0-2 1-5 2-7l3-10z" class="L"></path><defs><linearGradient id="Bz" x1="407.337" y1="718.458" x2="399.201" y2="743.392" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#Bz)" d="M396 740c3-6 6-11 9-16 1-2 2-4 4-6l2 2v2c0 2-3 5-4 7-3 4-5 9-8 14v-1 1h0-2c-1-1 0-2-1-3z"></path><defs><linearGradient id="CA" x1="588.311" y1="697.672" x2="573.932" y2="683.088" xlink:href="#B"><stop offset="0" stop-color="#4d4c4c"></stop><stop offset="1" stop-color="gray"></stop></linearGradient></defs><path fill="url(#CA)" d="M572 686c0-1 0 0-1-1v-1h1v-2c6 3 12 7 18 10 1 0 2 0 3 1h1c0 1-1 1-1 2-1 0-2-1-3-1 1 1 1 2 1 3l-1 1c-2-1-5-3-7-4-3-3-8-5-11-8z"></path><defs><linearGradient id="CB" x1="328.821" y1="520.061" x2="320.428" y2="527.712" xlink:href="#B"><stop offset="0" stop-color="#090808"></stop><stop offset="1" stop-color="#2e2e2f"></stop></linearGradient></defs><path fill="url(#CB)" d="M312 525c4-1 15-6 19-4 1 1 2 2 2 3-2 1-4 2-5 2-3 1-7 3-9 3l-3 1c-2-2-4-3-6-4l2-1z"></path><path d="M310 526l2-1c3 1 5 2 7 4l-3 1c-2-2-4-3-6-4z" class="H"></path><path d="M235 136h6c4 2 9 3 13 5h0c-2 0-3 0-5-1 0 2-1 3 0 4h0c1 0 1 0 2 1h0c-1 0-3 0-4-1-5-1-9-2-14-1v-1c-1-1-1-3-1-5l3-1z" class="Q"></path><path d="M558 681l10 5c1 1 3 2 4 3h0c1 2 2 3 3 5v2l-22-12c1-2 3-2 5-3z" class="P"></path><defs><linearGradient id="CC" x1="157.092" y1="221.445" x2="147.431" y2="208.449" xlink:href="#B"><stop offset="0" stop-color="#bebdbf"></stop><stop offset="1" stop-color="#eaeaec"></stop></linearGradient></defs><path fill="url(#CC)" d="M154 227c0-4-1-6-3-9-3-4-9-7-14-8-3 0-6 1-9 3v1c0-1 1-2 2-3 7-6 18 1 25 3 3 1 5 1 8 1-1 1-2 1-3 2h1v1c-3 0-5-1-8-2 2 3 4 5 4 9h0l-1-1v-1c-2 1-2 2-2 4z"></path><defs><linearGradient id="CD" x1="327.911" y1="513.813" x2="314.251" y2="519.352" xlink:href="#B"><stop offset="0" stop-color="#060505"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#CD)" d="M312 517c3-2 10-6 14-5 2 1 3 2 4 4l1 1c-5 1-10 3-15 4l-2 1-4-4 2-1z"></path><path d="M310 518l2-1c1 1 3 2 4 4l-2 1-4-4z" class="C"></path><defs><linearGradient id="CE" x1="223.247" y1="236.271" x2="206.613" y2="234.762" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#262526"></stop></linearGradient></defs><path fill="url(#CE)" d="M208 226c6 6 13 13 16 20-2 0-4-1-6-2-4-3-8-7-11-12h0c0-1 1-3 1-5h0v-1z"></path><defs><linearGradient id="CF" x1="404.62" y1="741.44" x2="396.691" y2="755.111" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#CF)" d="M402 740c2 1 3 2 4 4l-8 19h-1-4l9-23z"></path><defs><linearGradient id="CG" x1="363.962" y1="604.099" x2="352.981" y2="607.947" xlink:href="#B"><stop offset="0" stop-color="#0f0f10"></stop><stop offset="1" stop-color="#313031"></stop></linearGradient></defs><path fill="url(#CG)" d="M352 608l10-12c2 4 2 6 0 10l-6 13v-8l-2-1c-1 0-1-1-2-2z"></path><path d="M717 258c0 2 0 4-1 6l-2 2c-6 2-12 4-19 4h0c-1-1-2-1-2-1l-1-1c9-2 18-5 25-10z" class="L"></path><defs><linearGradient id="CH" x1="639.816" y1="254.199" x2="647.345" y2="234.889" xlink:href="#B"><stop offset="0" stop-color="#0d0c0d"></stop><stop offset="1" stop-color="#424241"></stop></linearGradient></defs><path fill="url(#CH)" d="M639 230c1 2 1 3 3 3h2c0 4 0 8 1 12 1 3 4 7 3 11v5c-1-2-2-4-2-5l-6-14c-1-1-1-2-1-3v-1-8z"></path><defs><linearGradient id="CI" x1="438.306" y1="790.81" x2="427.118" y2="811.243" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#323131"></stop></linearGradient></defs><path fill="url(#CI)" d="M428 812l6-24h1c2 1 3 3 3 4 0 3-2 7-2 9l-4 12-4-1z"></path><defs><linearGradient id="CJ" x1="415.081" y1="840.824" x2="420.872" y2="855.011" xlink:href="#B"><stop offset="0" stop-color="#a7a5a5"></stop><stop offset="1" stop-color="#d1d1d2"></stop></linearGradient></defs><path fill="url(#CJ)" d="M421 838h3c1 2 1 3 1 4-1 4-3 13-7 15l-3 2v1h-1c-1-1-1-2-2-3-1 0-3-1-3-2l7-1c3-3 4-12 5-16z"></path><defs><linearGradient id="CK" x1="694.562" y1="603.151" x2="699.834" y2="610.935" xlink:href="#B"><stop offset="0" stop-color="#9d9d9d"></stop><stop offset="1" stop-color="#cac9ca"></stop></linearGradient></defs><path fill="url(#CK)" d="M695 592l1 2 2 6v2c2 5 5 11 6 17l-1-1c-1 0-1-1-1-2s-1-3-2-5c-1 1-2 1-2 3v1h1l-1 1c0 1 1 2 1 3v2 1l-7-22-1-3c1-2 3-4 4-5z"></path><path d="M698 602c-1-1-1-1-2-1l2-1v2z" class="Z"></path><path d="M695 592l1 2c-2 2-3 4-4 6l-1-3c1-2 3-4 4-5z" class="D"></path><path d="M673 314h1l1 1h1l-1-1c0-1 1-4 0-5v-1c4-7 5-14 9-20 0 6-2 12-3 18-1 5-3 10-3 14l-5-6z" class="B"></path><defs><linearGradient id="CL" x1="244.551" y1="465.696" x2="251.855" y2="453.312" xlink:href="#B"><stop offset="0" stop-color="#5b595a"></stop><stop offset="1" stop-color="#a8a8a9"></stop></linearGradient></defs><path fill="url(#CL)" d="M240 449c1-1 2-1 3-2l6 14c1 2 1 4 3 5l3 1 1 1c2 1 4 1 6 2l-3 1h-8c-3-2-4-5-5-8l-6-14z"></path><path d="M507 489l1-1v-3c0-1-3-3-4-4-2-3-5-6-7-9-1-2-1-3 0-5 0 1 1 1 2 2 0 2 4 4 6 5 2 3 5 5 7 7 0 1 1 2 1 2 0 2-2 7-3 9l-3-3z" class="F"></path><defs><linearGradient id="CM" x1="340.213" y1="545.093" x2="320.27" y2="552.914" xlink:href="#B"><stop offset="0" stop-color="#030304"></stop><stop offset="1" stop-color="#403f3f"></stop></linearGradient></defs><path fill="url(#CM)" d="M321 552c5-4 11-8 18-11 1 1 2 2 2 3 0 0 0 1-1 1l-15 11c-1-1-1-1-2-1v1c-2-1-2-2-2-4z"></path><path d="M324 376c3 1 3 4 5 6s3 5 4 8c2 2 3 6 5 8 1 2 3 1 4 3 0 1 0 1 1 2l1-1 1 1-5 6-16-33z" class="C"></path><defs><linearGradient id="CN" x1="596.159" y1="308.64" x2="595.268" y2="277.833" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#e2e1e4"></stop></linearGradient></defs><path fill="url(#CN)" d="M588 275l2 2h1c1 6 3 12 5 18l3 6c1 1 2 2 2 3v1c0 1 1 2 1 3l-2 2c-7-11-11-22-12-35z"></path><defs><linearGradient id="CO" x1="226.539" y1="296.63" x2="203.986" y2="303.454" xlink:href="#B"><stop offset="0" stop-color="#5b595a"></stop><stop offset="1" stop-color="#b0b0b1"></stop></linearGradient></defs><path fill="url(#CO)" d="M223 296h5c1 1 0 3 0 5h-2l-22 4v-5l19-4z"></path><path d="M223 296h5c1 1 0 3 0 5h-2c0-2 1-3 1-4l-4-1z" class="F"></path><defs><linearGradient id="CP" x1="667.964" y1="649.94" x2="681.587" y2="659.253" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#CP)" d="M674 643l7 22c-1-1-1-1-2-1l-2 3h0v4l-7-23 1-2c0-1 2-2 3-3z"></path><defs><linearGradient id="CQ" x1="198.062" y1="207.776" x2="180.22" y2="215.157" xlink:href="#B"><stop offset="0" stop-color="#6c6a6b"></stop><stop offset="1" stop-color="#898889"></stop></linearGradient></defs><path fill="url(#CQ)" d="M194 216c-5-1-9-1-13-3-5-2-8-5-10-10-1-3-1-9 0-12h0c0 5 0 11 3 14 2 4 7 6 12 7s7-1 8-6c1-1 1-3 2-4l2 2-1 5-1 4-1 3h-1z"></path><path d="M384 363l1-1v-5-5h0c0 6 2 11 2 16 1 6 0 13 0 19-1 2 0 5-1 7v-7c0-1-1-2-1-3l-1 1c-1-2-1-4-3-5 0-2 1-5 1-7s0-4 1-7h1l1 1c0-1-1-3-1-4z" class="I"></path><path d="M383 366h1l1 1c0 5 1 11 0 16v1l-1 1c-1-2-1-4-3-5 0-2 1-5 1-7s0-4 1-7z" class="C"></path><path d="M382 373c1 2 1 3 2 5 1 1 1 3 1 5v1l-1 1c-1-2-1-4-3-5 0-2 1-5 1-7z" class="X"></path><defs><linearGradient id="CR" x1="808.899" y1="327.116" x2="819.854" y2="338.139" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#ccc"></stop></linearGradient></defs><path fill="url(#CR)" d="M805 326c4 2 25 11 27 13h-1l-3-1c-1-1-3-1-4-2l-2 2c1 0 2 1 3 1l4 2v1c-2 0-6-3-8-4l-20-9v-1c1-1 2-1 4-2z"></path><path d="M806 329h1l-1 1h-1l1-1z" class="N"></path><path d="M459 522l6 17 5 13c1 2 1 4 2 6l1 6c-1-2-2-4-2-5v4 1h-1l-12-35c1-3 0-4 1-7z" class="T"></path><path d="M465 539l5 13c1 2 1 4 2 6l1 6c-1-2-2-4-2-5-2-4-3-8-4-11-1-4-3-6-2-9z" class="B"></path><defs><linearGradient id="CS" x1="612.429" y1="787.998" x2="627.134" y2="800.224" xlink:href="#B"><stop offset="0" stop-color="#7c7b7d"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#CS)" d="M616 781h3l1 1c3 8 5 15 6 23-1-1-1-1-2-1 0 1 0 3-1 4v-2h-1v1c-1-1-1-2-1-3v5c0-10-2-18-5-28z"></path><path d="M575 326c2 0 4-1 5 0s2 2 2 4c2 3 4 7 5 11l-1 2c-6-3-10-8-14-14l2-1c0-1 0-1 1-2h0z" class="Q"></path><path d="M575 326c2 0 4-1 5 0s2 2 2 4c2 3 4 7 5 11l-12-15z"></path><path d="M691 290c1 0 1 0 1 1l-3 22c-1 3-1 8-2 11l-1-1c-1-3-1-8-1-12l3-13c0-2 0-5 1-6 0-1 1-1 2-2z" class="H"></path><defs><linearGradient id="CT" x1="611.514" y1="321.309" x2="604.056" y2="306.934" xlink:href="#B"><stop offset="0" stop-color="#4a494a"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#CT)" d="M601 304c6 7 13 12 19 19-1 0-2 0-2 1h-3l-2-1-2-2c-5-3-8-6-11-11l2-2c0-1-1-2-1-3v-1z"></path><path d="M611 321h4v1c-1 1-1 1-2 1l-2-2z" class="C"></path><defs><linearGradient id="CU" x1="290.263" y1="237.435" x2="298.237" y2="240.565" xlink:href="#B"><stop offset="0" stop-color="#525451"></stop><stop offset="1" stop-color="#69686b"></stop></linearGradient></defs><path fill="url(#CU)" d="M298 216h0c1 5 0 10-1 15-1 12-3 25-8 36h0l9-51z"></path><defs><linearGradient id="CV" x1="588.686" y1="802.373" x2="603.864" y2="819.082" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#CV)" d="M598 823l-2-9c-2-5-3-10-4-15l2-2c2 1 2 2 3 4 3 7 4 14 5 22l-1-1-2 2-1-1h0z"></path><defs><linearGradient id="CW" x1="719.354" y1="357.958" x2="710.723" y2="365.971" xlink:href="#B"><stop offset="0" stop-color="#565556"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#CW)" d="M713 354l2-1 3 4v3 7 1c1 1 2 1 3 2v2c0 1 0 3-1 4l-4-3-2-2h0c-3-3-2-4-2-8l1-9z"></path><path d="M712 363c1 2 0 2 1 4v1h0c1 1 1 2 1 3h0c-3-3-2-4-2-8z" class="D"></path><path d="M310 197l41 41h-1c-1 0-1-1-2-1h-2c0-2-2-3-3-3l-3-3c-3-4-8-9-12-12-3-3-6-6-8-9-2-1-4-3-4-5-2-2-6-5-6-8z" class="L"></path><defs><linearGradient id="CX" x1="777.052" y1="429.942" x2="790.989" y2="436.574" xlink:href="#B"><stop offset="0" stop-color="#939293"></stop><stop offset="1" stop-color="#cacaca"></stop></linearGradient></defs><path fill="url(#CX)" d="M775 428c6 0 11 3 17 6 2 1 5 2 6 4 2 1 3 4 3 5v1h-1v-1s0-1-1-1v-1c-1-2-1-2-2-3h-1c-1-1-2-2-3-2s-1 1-2 2h1 0l1 1c3 1 4 5 5 8h-1l-1-3c-2-6-8-6-14-8-3-1-5-3-8-4h0c1-2 1-2 1-4z"></path><defs><linearGradient id="CY" x1="524.532" y1="510.104" x2="520.323" y2="524.385" xlink:href="#B"><stop offset="0" stop-color="#646364"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#CY)" d="M513 505h1c2 4 5 8 8 11 4 4 8 7 10 11l-4 1c-5-4-13-10-14-16-1-2-1-5-1-7z"></path><path d="M804 128c0 2-1 3-2 4-1 2 0 4-1 4-2-1-4-2-7-3s-15-4-16-7v-1c2-1 3 0 5 1l14 5c1 1 3 2 4 2-2-2-5-4-8-5l2-1h1l1 1v-1c1-1 2-2 4-2 1 0 2-1 3 0l1 2-1 1z" class="V"></path><path d="M801 125c1 0 2-1 3 0l1 2-1 1c-1 0-3 0-4-1l1-2z" class="E"></path><defs><linearGradient id="CZ" x1="678.993" y1="640.825" x2="686.728" y2="664.765" xlink:href="#B"><stop offset="0" stop-color="#747373"></stop><stop offset="1" stop-color="#cfcecf"></stop></linearGradient></defs><path fill="url(#CZ)" d="M678 646l1-3v-1l2-2 9 24-5 2-7-20z"></path><path d="M391 465l2-3 1 3v-1c3 2 4 5 7 8v-1l2 1c1 2 3 3 4 4-1 1-2 2-3 4l-1 2-8-8c-2-1-4-3-6-4v-1l2-4z" class="O"></path><path d="M391 465c1 1 1 2 1 4h1c1 2 2 3 2 5-2-1-4-3-6-4v-1l2-4z" class="C"></path><path d="M394 465v-1c3 2 4 5 7 8v-1l2 1c1 2 3 3 4 4-1 1-2 2-3 4-2-2-5-6-6-8l-4-7z" class="D"></path><defs><linearGradient id="Ca" x1="540.073" y1="503.487" x2="554.322" y2="497.75" xlink:href="#B"><stop offset="0" stop-color="#4a4949"></stop><stop offset="1" stop-color="#797979"></stop></linearGradient></defs><path fill="url(#Ca)" d="M548 486l1 2 1 1 1 1 1 3-8 24c-1-5-2-9-4-13l2-2c4-3 6-11 6-16h0z"></path><defs><linearGradient id="Cb" x1="417.143" y1="226.025" x2="424.075" y2="210.35" xlink:href="#B"><stop offset="0" stop-color="#727172"></stop><stop offset="1" stop-color="#989897"></stop></linearGradient></defs><path fill="url(#Cb)" d="M415 206v1c1 0 1-1 2-1h1v5c0 4 0 6 2 9 3 4 7 8 8 13-1 0-2-2-3-2 0-1-1-1-2-1v-1c-2-3-6-5-9-6-2-6 0-12 1-17z"></path><defs><linearGradient id="Cc" x1="734.615" y1="532.262" x2="747.274" y2="545.611" xlink:href="#B"><stop offset="0" stop-color="#b5b5b5"></stop><stop offset="1" stop-color="#e3e2e3"></stop></linearGradient></defs><path fill="url(#Cc)" d="M738 529c2 3 5 7 8 10 4 4 10 7 12 12h0-1c-6-8-17-9-25-14 0-1 0-2 1-4 1 1 3 2 4 3h0l-1-2h0c0-2 1-3 2-5z"></path><defs><linearGradient id="Cd" x1="249.942" y1="312.37" x2="239.477" y2="317.064" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#Cd)" d="M229 321c3-3 7-4 11-6 3-2 6-4 10-5 1 1 1 2 1 4-1 3-16 10-20 12-1-2-1-3-2-5z"></path><defs><linearGradient id="Ce" x1="534.672" y1="803.001" x2="520.367" y2="804.032" xlink:href="#B"><stop offset="0" stop-color="#aeacaf"></stop><stop offset="1" stop-color="#dddddc"></stop></linearGradient></defs><path fill="url(#Ce)" d="M516 783l13 20c4 5 8 11 11 16 1 1 1 2 1 3-4-1-10-9-12-13 0-1-2-3-3-4l-6-10-2-3v-3c-1-1-1-3-2-4v-1-1z"></path><path d="M547 483c1 3 0 7-2 10-2 4-5 9-9 12l-2 1c-2 0-2-1-3-2 1-3 5-5 6-7 3-4 5-8 5-13 2 0 3 0 5-1z" class="N"></path><path d="M527 444l2 2h0c1 2 3 3 5 4 4 4 8 9 6 15 0 3-3 6-2 9v1c1 2 2 2 3 4h-2l-2-1-1 1c-1-1-3-3-3-4-1-3 2-6 3-9 5-9-6-13-9-21v-1z" class="L"></path><defs><linearGradient id="Cf" x1="746.108" y1="424.74" x2="765.336" y2="428.977" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#Cf)" d="M752 419c1 1 2 1 2 2l12 9c-1 1-2 3-3 5h0l-8-8c-4 1-8 2-11 4 1-3 2-6 3-8h1c1-2 2-3 4-4z"></path><defs><linearGradient id="Cg" x1="341.37" y1="548.226" x2="326.674" y2="561.734" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#363536"></stop></linearGradient></defs><path fill="url(#Cg)" d="M324 558l18-10c1 0 2 1 2 2v1c-3 3-7 6-11 8-1 1-5 3-6 4l-2 1-1-1c-1-1-2-2-2-3l2-2z"></path><path d="M322 560l2-2c1 2 3 4 3 5l-2 1-1-1c-1-1-2-2-2-3z" class="C"></path><path d="M521 751l1 2c-4 5-9 9-13 14-2 2-4 5-5 8l-1-3c-1-3-1-5 0-7l1-1c3-5 11-10 17-13z" class="G"></path><path d="M739 180l1 1c2 1 2 3 3 5 2 1 4 1 6 1l-1 2h-2v-1h-1c-4 3-5 12-6 17l-1-4-3-3-2 2h-1l7-20z" class="P"></path><defs><linearGradient id="Ch" x1="762.975" y1="408.41" x2="780.778" y2="424.933" xlink:href="#B"><stop offset="0" stop-color="#494848"></stop><stop offset="1" stop-color="#a4a4a4"></stop></linearGradient></defs><path fill="url(#Ch)" d="M761 408c8 1 17 9 24 14-1 0-2 1-3 2h0v1l-10-6c-1-1-2-1-2-1-4-2-7-4-11-5h0 2l1-3-1-2z"></path><defs><linearGradient id="Ci" x1="273.199" y1="367.685" x2="253.863" y2="384.068" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#686767"></stop></linearGradient></defs><path fill="url(#Ci)" d="M254 388c3-10 7-18 16-25h1c1 2 1 3 1 4-2 4-7 7-9 11-1 2-2 5-3 8l-1 3h-1v-1c-1-1-1-1-2-1-1 1-1 0-2 1h0z"></path><defs><linearGradient id="Cj" x1="624.28" y1="274.311" x2="614.803" y2="269.234" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#afaeaf"></stop></linearGradient></defs><path fill="url(#Cj)" d="M617 255l1 2 6 17s-2 1-2 2c-1 1 0 2-1 3l1 1h-2c-3-5-4-10-6-16l-1-3h1c0-2-1-2 0-4 1-1 2-2 3-2z"></path><path d="M617 255l1 2v5c1 1-1 4-2 5h0l-1-1v-2h-1l-1-3h1c0-2-1-2 0-4 1-1 2-2 3-2z" class="S"></path><defs><linearGradient id="Ck" x1="660.321" y1="624.924" x2="675.679" y2="641.076" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#Ck)" d="M670 648l-4-15c-1-2-3-6-3-8s2-6 3-7l8 25c-1 1-3 2-3 3l-1 2z"></path><defs><linearGradient id="Cl" x1="589.99" y1="846.451" x2="608.848" y2="858.446" xlink:href="#B"><stop offset="0" stop-color="#828781"></stop><stop offset="1" stop-color="#aaa5ad"></stop></linearGradient></defs><path fill="url(#Cl)" d="M599 841c2-3 0-8 1-10l1 2h0c0-1 0-2 1-3v7h0c1 5 0 10-1 15 0 1 0 3-1 4v5c0 3 1 8 0 10-2 2 0 7-2 9-3-13-1-26 1-39z"></path><path d="M759 168c2 0 3 1 5 3h1 2 0l2 2c5 4 11 7 16 12h0c-1 2-1 3-1 5 1 1 2 2 4 2v2c-2-2-4-3-6-5-3-2-6-4-9-7l-7-7c-2-1-6-6-7-7z" class="P"></path><path d="M759 168c2 0 3 1 5 3h1l5 5h-1l-2-2-1 1c-2-1-6-6-7-7z" class="Y"></path><path d="M773 182l1-3h1c2 1 4 4 7 4l1 1h0c0 2 1 3 0 5h-1c-3-2-6-4-9-7z" class="S"></path><path d="M649 377h1c2 1 4 2 6 4l8 5c-1 2-3 3-5 4l-6 1c-1 0-2 1-3 0h0v-1l1-1c0-1 0-1-1-2v-1c-1 0-1-1-2-2-1-2 0-5 1-7z" class="G"></path><defs><linearGradient id="Cm" x1="642.532" y1="665.172" x2="652.64" y2="676.094" xlink:href="#B"><stop offset="0" stop-color="#1d1c1c"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#Cm)" d="M649 688l-3-10c-1-3-2-7-2-11 0-3 2-6 3-8l7 26c-2 0-4 1-5 2v1z"></path><path d="M402 498l16-25c2-4 4-8 8-12v4c-1 4-4 8-7 12l-11 17-3 3c-1 1-2 1-3 1z" class="W"></path><path d="M409 183c1-6 4-12 7-18-3 10-3 20 1 29 2 3 5 4 7 6h0l1 2c0 1-1 1-2 2l-2-1c-5-4-8-10-10-16v-3h-1v3h0v-1l-1 1v-4z" class="U"></path><defs><linearGradient id="Cn" x1="560.58" y1="770.812" x2="554.427" y2="756.685" xlink:href="#B"><stop offset="0" stop-color="#292828"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#Cn)" d="M549 754l2 2v1c0 1 1 1 1 2 1-1 2-2 4-3 1 0 1 0 1-2 2 7 6 17 2 23v1l-3-3h0c1-3-3-8-5-10l1-1h0c0-3-1-4-2-5 0-2-1-4-1-5z"></path><path d="M549 754l2 2v1c0 1 1 1 1 2 2 4 6 10 6 14-2-2-4-7-6-9h0c0-3-1-4-2-5 0-2-1-4-1-5z" class="O"></path><defs><linearGradient id="Co" x1="713.267" y1="510.075" x2="724.042" y2="516.941" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#Co)" d="M723 528c-3-2-7-5-9-8-3-5 2-8 2-12 1-1 0-1 0-2 0-2 1-2 2-3h1c4 4 6 7 8 12l-1-1-3 1v-1-2l-2 1c-1-1-3 0-4 1v1c0 2 4 5 6 7h-2v3h0l2 3z"></path><defs><linearGradient id="Cp" x1="288.625" y1="478.125" x2="281.711" y2="478.893" xlink:href="#B"><stop offset="0" stop-color="#6c6b6c"></stop><stop offset="1" stop-color="#908f8e"></stop></linearGradient></defs><path fill="url(#Cp)" d="M295 468c2 3 2 4 2 7l-3 2-12 11-2 2-2-5 1-1c2-3 6-8 10-10h0c1-2 3-3 4-4l2-2z"></path><path d="M289 474h0l1 1h-1v-1z" class="K"></path><path d="M278 485l1-1c1 0 1 1 2 2 0 1 1 1 1 2l-2 2-2-5z" class="Q"></path><path d="M295 468c2 3 2 4 2 7l-3 2c1-1 1-2 1-4 0-1-1-2-2-3l2-2z" class="T"></path><defs><linearGradient id="Cq" x1="702.872" y1="539.666" x2="717.808" y2="556.081" xlink:href="#B"><stop offset="0" stop-color="#333232"></stop><stop offset="1" stop-color="#8e8e8e"></stop></linearGradient></defs><path fill="url(#Cq)" d="M708 533c4 7 6 14 7 21 0 2 0 6 1 7 0 1 1 2 1 2h-1l-4 1-1 1c-1-2 0-4 0-6 0-8-2-14-6-20 2-2 3-3 3-6z"></path><defs><linearGradient id="Cr" x1="633.991" y1="261.981" x2="644.796" y2="236.852" xlink:href="#B"><stop offset="0" stop-color="#030204"></stop><stop offset="1" stop-color="#464645"></stop></linearGradient></defs><path fill="url(#Cr)" d="M638 233c1 4 1 7 2 11s2 8 4 12c1 3 2 5 2 9 0 1 0 2-1 3h-1c-1-2-2-6-2-8-3-7-8-16-9-24v-8c0 2 0 3 1 4l2 2h1c1 0 0 0 1-1z"></path><path d="M533 764l7-3-1 1c-1 0-1 0-2 1h4l4 4c6 10 9 17 7 29-1 2-1 5-3 7h0l-1-1c1-2 1-4 1-6 1-5 1-9-1-14l-1-4c-1-6-4-10-10-13h0c-1-1-3-1-4-1h0z" class="R"></path><path d="M321 215c3 1 4 4 7 4 4 3 9 8 12 12l3 3c1 0 3 1 3 3h2c1 0 1 1 2 1h1c1 1 3 2 4 4h-1l-2-1c-1 1-1 2-1 4v1h-1 0c-3-2-5-6-7-8l-22-23z" class="P"></path><path d="M350 246c0-3-4-7-5-9h1 2c1 0 1 1 2 1h1c1 1 3 2 4 4h-1l-2-1c-1 1-1 2-1 4v1h-1 0z" class="C"></path><path d="M749 122c-1-2-2-2-1-3l23-1c2 0 6-1 8 1v1c-1 1-1 2-1 2-2 0-5 1-7 1-5 0-13-1-17 1h-1 0c-2 0-3-1-4-2z" class="T"></path><path d="M749 122c2-1 6-1 9-1l14-1c2 0 5-1 7 0-1 1-1 2-1 2-2 0-5 1-7 1-5 0-13-1-17 1h-1 0c-2 0-3-1-4-2z" class="O"></path><path d="M409 495c2-5 6-9 9-14 2-4 5-7 7-11 1-1 1-2 3-2v2c0 2-2 4-3 6l-11 18c-1 1-3 4-4 5l-3 6-4-2 5-8h1z" class="L"></path><path d="M408 495h1l-1 1c0 2-1 3-2 4 0 1 0 1 1 2l1-1c1-1 1-2 2-2l-3 6-4-2 5-8z" class="Q"></path><defs><linearGradient id="Cs" x1="380.101" y1="646.305" x2="370.898" y2="671.463" xlink:href="#B"><stop offset="0" stop-color="#020101"></stop><stop offset="1" stop-color="#363636"></stop></linearGradient></defs><path fill="url(#Cs)" d="M366 678c0-6 3-12 5-16 2-5 5-9 9-13l3-3v1c2 5-6 15-8 19-1 3-2 6-3 8v-2c-2-1-2-1-4-1v1c0 2-1 4-2 6z"></path><defs><linearGradient id="Ct" x1="490.727" y1="462.311" x2="505.703" y2="440.679" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#939192"></stop></linearGradient></defs><path fill="url(#Ct)" d="M497 467s3-5 2-5c0-1-1-2-2-2-1-3-3-5-3-8-2-8 2-17 7-23 0 2 0 4-1 7 0 1-1 2-1 4v2h-1c-1 4-2 10 1 14 2 2 5 5 4 8 0 2-3 4-4 5-1-1-2-1-2-2z"></path><defs><linearGradient id="Cu" x1="633.289" y1="501.787" x2="637.668" y2="520.249" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#808081"></stop></linearGradient></defs><path fill="url(#Cu)" d="M633 496v1c0 2 1 3 0 5 1 0 2 0 3-1h3v1l1 1c-1 7-1 14-5 20l-3-3c-3-4-1-8 0-12l1-12z"></path><defs><linearGradient id="Cv" x1="519.724" y1="550.368" x2="528.276" y2="530.132" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#3e3d3d"></stop></linearGradient></defs><path fill="url(#Cv)" d="M513 517c2 2 3 5 5 7s8 6 9 9v5c1 4 1 7 3 11v2c1 2 1 4 0 5-1-1-1-2-2-3-1-2-3-3-5-3l-1-1v-1c1-2 1-5 1-7 0-7-4-12-7-18-1-2-3-4-3-6z"></path><defs><linearGradient id="Cw" x1="608.522" y1="464.623" x2="621.978" y2="496.877" xlink:href="#B"><stop offset="0" stop-color="#2d2d2a"></stop><stop offset="1" stop-color="#5d5c5f"></stop></linearGradient></defs><path fill="url(#Cw)" d="M619 464l3-7c0 2 0 3 1 4v1c-1 5-11 39-13 40-1 1-2 1-3 2 1-2 1-3 1-5 1-4 3-9 4-13l7-22z"></path><defs><linearGradient id="Cx" x1="410.377" y1="796.398" x2="407.557" y2="827.324" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#dfdedf"></stop></linearGradient></defs><path fill="url(#Cx)" d="M411 796h4 0l-9 35v-2-1h-3l8-32z"></path><path d="M703 298h1c1-1 2-2 2-4v3c-1 0-1 1-1 2l2 2c-4 9-10 20-17 26h-1c0-6 5-12 7-16l7-13z" class="L"></path><path d="M425 543l13 7c-3 3-10 8-11 12 0 1 0 2 1 3-2 2-4 3-5 5l-2 1-2 3c0-3-1-6-1-9 1-1 2-3 3-4l1-2 3-2c1-1 2-1 3-2l3-3h0l-1-1 2-2v1c1 1 2 1 3 1l1-1-6-3-4-2-1-1v-1z" class="M"></path><path d="M431 552h0l2 1-4 4h-1v-2l3-3z" class="D"></path><path d="M422 559c2 1 2 1 2 3 0 3-2 5-1 7v1l-2 1h0c0-4 1-7 0-10l1-2z" class="F"></path><path d="M418 565c1-1 2-3 3-4 1 3 0 6 0 10h0l-2 3c0-3-1-6-1-9z" class="H"></path><path d="M710 204c1 1 2 2 2 4h0c0 2 1 5 1 8 0 10-1 21-9 29l-1 1c1-5 3-9 4-13h-1c0-2 1-4 1-6 1-4 2-8 2-12 0 0 0-4 1-5v-6z" class="Q"></path><path d="M710 204c1 1 2 2 2 4h0c0 2 1 5 1 8h0c-1 1-1 2-1 3h-1c-1 1-1 0-1 1 0 4-2 9-3 13h-1c0-2 1-4 1-6 1-4 2-8 2-12 0 0 0-4 1-5v-6z" class="R"></path><path d="M710 220c0-3 0-10 2-12 0 2 1 5 1 8h0c-1 1-1 2-1 3h-1c-1 1-1 0-1 1z" class="H"></path><path d="M580 179c1-1 2-2 3-1 7 0 13 6 17 11 2 3 4 6 3 10 0 2 0 4-1 5l-1 1c-1 0-2-1-3-2 1-3 1-6 0-9-1-4-5-9-9-11-3-1-5-1-8 0v-2c0-1 0-1-1-2z" class="P"></path><path d="M429 492c10 13 18 27 23 42 3 5 5 11 6 16v1c-1-2-2-3-3-5 0-1-1-2-1-3-1 0-1-1-2-2 0-2-1-3-1-4l-1-1-13-28c-3-5-5-10-8-15v-1z" class="W"></path><path d="M600 642c2 3 2 7 2 11 0 1-1 2 0 3 0 1 1 1 1 3-1 2-1 8-2 9l-2-1c-5-1-11-5-15-8l3-1c2 2 5 4 7 6h0l1-1c1-3 1-7 2-10 0-4 2-8 3-11z" class="F"></path><path d="M599 667h1 1v-3c1-1 0-2 0-3h0-1c1-2 1-4 2-5 0 1 1 1 1 3-1 2-1 8-2 9l-2-1z" class="T"></path><path d="M772 419l10 6 16 11c3 3 4 8 4 12h0l-1-4v-1c0-1-1-4-3-5-1-2-4-3-6-4-6-3-11-6-17-6-3 0-5 1-7 2h-2l-12-9 16 7 2-6v-3z"></path><path d="M801 120c9-1 20 1 27 8h1c-1 3-3 11-5 13-2 0-3 0-4-1v-2-3c0-2 0-5-1-7 0-2-3-4-5-5h-2c-4-2-7-2-11-2v-1z" class="K"></path><defs><linearGradient id="Cy" x1="393.981" y1="692.81" x2="378.634" y2="700.654" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#Cy)" d="M389 685c1 0 1 0 1 1 1 1 2 1 3 2-3 4-6 7-10 9l4 7 1 1v1c-1 0-2 1-2 2l-2 1c-4-3-6-9-9-13h0c3-3 6-4 9-6 2-1 3-4 5-5z"></path><path d="M346 304c-2-6-2-13-3-19 0-5-1-11 0-16 0-3 2-6 3-8l2 45-2-2z" class="N"></path><defs><linearGradient id="Cz" x1="553.993" y1="445.816" x2="550.217" y2="429.293" xlink:href="#B"><stop offset="0" stop-color="#9b9a9b"></stop><stop offset="1" stop-color="#cccacb"></stop></linearGradient></defs><path fill="url(#Cz)" d="M556 421c1 3 1 7 0 9-1 1-1 2-2 3-2 4-2 7 0 12l2 6c1 2 2 7 0 10h0c-3-6-7-13-8-19-1-5 1-8 3-12 0-1 1-3 1-4s-1-4-1-6c0 1 0 1 1 1 1 2 1 5 0 7v1h3c1-1 0-2 1-3-1-1-1-1-1-2v-2h0l1-1z"></path><defs><linearGradient id="DA" x1="578.738" y1="615.861" x2="583.378" y2="624.727" xlink:href="#B"><stop offset="0" stop-color="#69686a"></stop><stop offset="1" stop-color="#9b999b"></stop></linearGradient></defs><path fill="url(#DA)" d="M609 605h1v1c-4 5-10 10-15 14-9 5-18 7-28 8-4 1-9 1-14 0h-2c1-1 4-1 6-1 12-1 28-3 38-10 5-4 9-9 14-12h0z"></path><defs><linearGradient id="DB" x1="599.361" y1="305.161" x2="583.367" y2="297.942" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#b8b7b8"></stop></linearGradient></defs><path fill="url(#DB)" d="M595 313c-6-9-13-24-12-35h0c-1-6 0-13 1-19v14c2 14 8 27 14 38-1 1-2 2-3 2z"></path><path d="M229 138l3-1c0 2 0 4 1 5v1c-1 1-4 2-6 3 1 1 1 0 2 0 2 2 3 4 5 5-2 2-4 3-6 3-3-1-7-6-8-9l1-1h0v-1h-1l-1 1s-1 0-1-1c-2-1-2-2-3-3 2 0 4 0 6 2l3-2 4-3h0l1 1z" class="R"></path><path d="M229 138l3-1c0 2 0 4 1 5v1c-1 1-4 2-6 3-1 1-1 1-2 1l-2-4 6-5z" class="G"></path><defs><linearGradient id="DC" x1="316.59" y1="183.535" x2="315.985" y2="178.578" xlink:href="#B"><stop offset="0" stop-color="#9e9c9e"></stop><stop offset="1" stop-color="#c5c5c6"></stop></linearGradient></defs><path fill="url(#DC)" d="M339 175c0 1 0 1-1 2-1 2-6 2-9 3h-3v1h-1l-2 1-1 1-12 3-3-1c-2 0-5 1-6 2l-2-1v-1l2-2c2-1 5-2 8-2 10-3 20-5 30-6z"></path><path d="M307 185c6-2 13-4 19-5v1h-1l-2 1-1 1-12 3-3-1z" class="O"></path><path d="M299 202l22 35-1 1c-3-1-5-9-8-10h0v1 2c1 1 1 2 1 2v1l-18-28 1-1c0 1 1 1 1 1 1-1 1-3 2-4h0z" class="Q"></path><defs><linearGradient id="DD" x1="382.214" y1="644.687" x2="359.606" y2="663.053" xlink:href="#B"><stop offset="0" stop-color="#020202"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#DD)" d="M362 663c2-5 5-9 8-14 2-3 4-8 8-10 1 0 1 0 2 2v2c-2 4-5 7-8 11-2 5-4 9-6 14h-1v1c-3-3-3-2-3-6z"></path><defs><linearGradient id="DE" x1="269.119" y1="456.311" x2="273.374" y2="463.056" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#9d9c9d"></stop></linearGradient></defs><path fill="url(#DE)" d="M282 447l2-1h0 0l1 1 2 2-4 4c-6 8-11 14-21 17-2-1-4-1-6-2l-1-1h3c11-2 18-11 23-20h1z"></path><path d="M282 447l2-1h0 0l1 1 2 2-4 4 1-3h0l-3-3h1z" class="K"></path><defs><linearGradient id="DF" x1="720.434" y1="270.082" x2="710.066" y2="272.418" xlink:href="#B"><stop offset="0" stop-color="#3c393b"></stop><stop offset="1" stop-color="#696967"></stop></linearGradient></defs><path fill="url(#DF)" d="M719 254c0 1 0 3 1 4v3h0c0 3 0 6-1 9 0 3-2 7-1 10-3 5-4 11-7 15-2-2-1-5-3-7 0-3 3-7 4-9 2-5 4-10 5-14l-1-1c1-2 1-4 1-6 0-1 1-3 2-4z"></path><path d="M719 254c0 1 0 3 1 4v3h0l-1-1c-1 1-2 3-2 5l-1-1c1-2 1-4 1-6 0-1 1-3 2-4z" class="B"></path><path d="M409 183v4l1-1v1h0v-3h1v3c2 6 5 12 10 16l2 1c-2 4-5 8-4 13h1v1h0c3 4 9 8 8 14 0 1 0 1-1 2l1-1c-1-5-5-9-8-13-2-3-2-5-2-9v-5h-1c-1 0-1 1-2 1v-1c-1-2-3-4-4-6-3-4-3-12-2-17z" class="Q"></path><path d="M411 187c2 6 5 12 10 16l2 1c-2 4-5 8-4 13h1v1h0c3 4 9 8 8 14 0 1 0 1-1 2l1-1c-1-5-5-9-8-13-2-3-2-5-2-9l2-4c0-2-3-4-4-6-3-3-5-9-5-14z"></path><defs><linearGradient id="DG" x1="529.727" y1="462.828" x2="540.974" y2="451.474" xlink:href="#B"><stop offset="0" stop-color="#d3d2d2"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#DG)" d="M527 444c-1-4-2-9-1-13l1-1c0 6 0 10 5 14 5 5 13 6 14 15 0 3-1 6-2 9 0 1-1 2-1 3s1 2 2 2l-1 2h-4s-1-1-1-2c-1-1 2-6 2-8 1-1 1-3 0-4 0-8-7-11-12-15l-2-2z"></path><path d="M409 542l4 2 18 8-3 3c-1 1-2 1-3 2-7-5-15-8-22-11l-6-2c4-2 8 0 12-2z" class="Y"></path><path d="M409 542l4 2h-1-11l1 1c1 0 0 0 1 1l-6-2c4-2 8 0 12-2z" class="N"></path><defs><linearGradient id="DH" x1="688.021" y1="400.812" x2="684.56" y2="382.001" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#c2c1c2"></stop></linearGradient></defs><path fill="url(#DH)" d="M691 377l-1 2h1c1-1 2-1 3 0-1 3-5 7-5 11-2 5 0 11-1 16-1 0-2-2-3-3 0-2-1-3-1-5-1-3-1-5 0-8h0v-1c-1 1-2 2-2 4-3 0-3 0-6-1 1-2 3-3 5-5l10-10z"></path><defs><linearGradient id="DI" x1="389.577" y1="512.973" x2="400.998" y2="510.354" xlink:href="#B"><stop offset="0" stop-color="#242323"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#DI)" d="M402 498c1 0 2 0 3-1l3-3v1l-5 8 4 2-7 9c-1 2-3 4-4 6-2 0-5 0-7-2 1-4 5-8 8-11 1-3 3-6 5-9z"></path><path d="M403 503l4 2-7 9h-1c-1-3 3-8 4-11z" class="Y"></path><path d="M618 844c0-1 0-2 1-2 2 0 2 1 3 2-4 14-4 28 2 41 4 8 11 15 19 17 1 1 1 1 2 1h-1c-8-1-15-8-19-14-7-9-10-20-9-31 0-5 1-10 2-14z" class="E"></path><path d="M245 178h2 6c3 1 6 1 9 1h-1l-3 2h1c-1 1-2 1-2 2h-1l-2 1h0l5 8c1 2 3 5 3 7v2c-3-1-6-6-9-8v1h-1l-1-2h-1c-2-5-3-9-5-14z" class="N"></path><path d="M259 192l-1 1c-1-2-3-4-4-6-2-2-3-4-5-7l9 1h1c-1 1-2 1-2 2h-1l-2 1h0l5 8z" class="Q"></path><path d="M245 178h2c2 8 9 14 14 21h1v2c-3-1-6-6-9-8v1h-1l-1-2h-1c-2-5-3-9-5-14z" class="F"></path><path d="M181 472h0c-4-1-7-3-10-5 2 1 4 1 6 2 9 2 14-3 22-6 4-2 9-1 13 1l7 4 2 3-1 1h-2l-4-3c-2-1-2-1-3-1v-1c-1 0-2-1-4-1h0-7 0c-1 1-2 1-2 1v1c-2 1-4 2-6 2l-1 1c-1 0-2 0-2 1h-1c-2 1-5 0-7 0z" class="U"></path><path d="M211 467h0 1c-3-2-5-2-8-4 2 0 6 2 8 1l7 4 2 3-1 1h-2l-4-3c-2-1-2-1-3-1v-1z" class="Z"></path><defs><linearGradient id="DJ" x1="603.342" y1="480.945" x2="614.338" y2="501.458" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#DJ)" d="M618 461v1 2h1l-7 22c-1 4-3 9-4 13 0 2 0 3-1 5-1 4-4 10-3 14l-1 5c-2-3-1-7-1-11v-6c1-1 2-4 2-6 1-4 4-9 5-14l9-25z"></path><defs><linearGradient id="DK" x1="590.897" y1="384.892" x2="573.772" y2="371.145" xlink:href="#B"><stop offset="0" stop-color="#454445"></stop><stop offset="1" stop-color="#949394"></stop></linearGradient></defs><path fill="url(#DK)" d="M581 363c3 5 6 10 7 16 0 3 0 5 1 8v1l1 1c-2 0-4 1-6 2l-2-2c-1-7-2-14-5-21 1-2 3-3 4-5z"></path><defs><linearGradient id="DL" x1="460.964" y1="496.106" x2="441.436" y2="494.508" xlink:href="#B"><stop offset="0" stop-color="#1e1f1e"></stop><stop offset="1" stop-color="#535053"></stop></linearGradient></defs><path fill="url(#DL)" d="M440 468c7 12 12 25 18 38 1 4 3 8 4 12h0-1c0-1 0-2-1-3-1 0-1 0-2 1v-1c-1-2-2-3-3-5l-14-31 2 2c0-2-1-3-1-5l1 1c0-1 0-2-1-3v-1l-1-1c-1-2-1-2-1-4z"></path><path d="M403 828h3v1 2c-3 16-9 35-19 48 0-1-1-2-1-3 0 0 1-1 1-2l1-1 1-1c7-14 11-29 14-44z" class="L"></path><path d="M446 594c0-4-1-8-2-11v-1c-1-3 0-5-2-8 1 0 1 0 2-1 0 1 1 3 1 4 2 7 3 15 5 22 2 10 3 21 4 31h-1v1c1 2 0 5 0 8l-1 1-5-33h1c-1-1-1-1 0-2v-3l-2-8z" class="D"></path><defs><linearGradient id="DM" x1="188.717" y1="412.589" x2="181.435" y2="378.49" xlink:href="#B"><stop offset="0" stop-color="#dbd9db"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#DM)" d="M173 391c-1-3-5-7-8-8v-1h1l6 5h1c3 3 7 6 12 8 4 2 9 2 14 2 3 1 4 1 6 3h0c-3 1-6 2-9 1 1 2 4 5 5 7-1 2-1 3-2 4h-1c-3-3-3-7-7-10-6-5-13-5-18-11z"></path><defs><linearGradient id="DN" x1="724.323" y1="126.506" x2="718.662" y2="131.994" xlink:href="#B"><stop offset="0" stop-color="#4b4b4b"></stop><stop offset="1" stop-color="#666766"></stop></linearGradient></defs><path fill="url(#DN)" d="M710 117l-1-3c2-1 6-1 8 0 4 9 7 18 12 28 3 5 7 10 10 16l-1-1c-1-1-2-1-2-2h-1v-1l-2-2h0l-1-3c-2 0-7-9-9-11-2-6-6-11-9-17 0-1-1-1-1-2-1-1-2-3-3-4h0v2h0z"></path><defs><linearGradient id="DO" x1="428.802" y1="757.212" x2="415.3" y2="776.144" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#DO)" d="M416 776c2-7 4-17 9-24 2 4 4 7 5 11l-8 15v-1l-1 1h-1c-1 0-3-1-4-2z"></path><path d="M467 245c1-5 1-10 1-16 0-24-5-50 11-70h1c-13 19-12 41-9 63 1 5 3 10 3 15v1c-1-1-1-2-1-3s0-2-1-3c-1 0-2 0-3 1v1 2c-1 3 0 6-1 9h-1z" class="E"></path><defs><linearGradient id="DP" x1="612.726" y1="813.362" x2="628.353" y2="836.753" xlink:href="#B"><stop offset="0" stop-color="#bfbebf"></stop><stop offset="1" stop-color="#f4f3f5"></stop></linearGradient></defs><path fill="url(#DP)" d="M621 809v-5c0 1 0 2 1 3v-1h1v2c0 2 0 5-1 8h-1v1 2l-1 1c0 2 0 4 1 6v2c0 1 0 1 1 3v2c0 1 0 1 1 3h0v1l-1 7c-1-1-1-2-3-2-1 0-1 1-1 2 0-3 0-5-1-8 0-4-2-7-2-11-1-2-1-5 0-8l4-3c1-2 1-3 2-5z"></path><path d="M515 796h1l-2-7c1 2 3 5 5 7l1-1 6 10c1 1 3 3 3 4 0 2 2 5 4 7 0 1 1 4 1 4-1 1-1 2-1 4-1 0-2-1-3-1-6-9-11-17-15-27z" class="F"></path><path d="M543 653c0-1 1-2 1-3 1 1 2 2 2 4s-2 8-3 11-2 7-3 10c-2 0-2 1-3 2l-4 8c0 1-1 2-2 3v-3h0l2-2v-1c0-2 1-4-1-6l7-20c1 0 0 0 1-1h0 1l2-2z" class="B"></path><path d="M543 653c0-1 1-2 1-3 1 1 2 2 2 4s-2 8-3 11c-1 0-3 1-4 2v1h0c-1 1-1 2-1 3l-1 1h0c0-2 1-4 1-6l5-13z" class="Q"></path><path d="M537 672h0l1-1c0-1 0-2 1-3h0v-1c1-1 3-2 4-2-1 3-2 7-3 10-2 0-2 1-3 2l-4 8c0 1-1 2-2 3v-3h0l2-2 4-11z" class="S"></path><defs><linearGradient id="DQ" x1="663.779" y1="619.107" x2="684.787" y2="631.15" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#DQ)" d="M676 642l-6-18c-1-2-3-6-3-9 0-2 2-6 3-8l11 31v2l-2 2v1l-1 3-2-4z"></path><path d="M681 638v2l-2 2v1l-1 3-2-4 2-2h1 1l1-2z" class="M"></path><defs><linearGradient id="DR" x1="664.49" y1="363.396" x2="672.964" y2="373.668" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#DR)" d="M667 361c4 0 7 3 11 3l1 1c0 2-2 4-4 5l-9 12c-1-3-1-8-3-10-1-1-1-1-1-2v-2-1c2-2 3-4 5-6z"></path><path d="M631 568h1l-16 9c-17 10-33 18-53 19 0-1-1-1-1-1 1-2 18-5 22-7 11-3 21-8 32-13 5-2 10-4 15-7z" class="W"></path><defs><linearGradient id="DS" x1="313.27" y1="176.279" x2="312.353" y2="171.084" xlink:href="#B"><stop offset="0" stop-color="#3d3c3e"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#DS)" d="M333 168c4-1 9 0 13 1l-28 4c-8 2-16 4-24 7l-9 3-1-1 1-1c3-2 7-4 10-5 13-4 25-7 38-8z"></path><defs><linearGradient id="DT" x1="353.385" y1="577.925" x2="337.478" y2="602.433" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#DT)" d="M335 598l11-12c2-3 4-6 7-8 1 0 2 1 3 2v2c-1 3-3 6-5 8l-10 12h-1c-2 0-4 0-6-2l1-1v-1z"></path><defs><linearGradient id="DU" x1="374.854" y1="417.354" x2="379.561" y2="419.201" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#DU)" d="M386 387v7c-1 14-6 29-12 42-1 1-2 1-2 3l-3 6h-1c0-2 1-3 0-4 0-3 2-7 3-10l7-16c2-8 5-16 7-24 0-1 0-3 1-4z"></path><path d="M505 749l3-3c3-2 11-8 15-8 2 2 3 4 5 6l-13 8h-6c-3 1-7 3-9 2 2-2 3-4 5-5z" class="D"></path><path d="M505 749l3-3c3-2 11-8 15-8-1 3-10 9-14 11h-3-1z" class="U"></path><defs><linearGradient id="DV" x1="191.521" y1="306.149" x2="180.555" y2="266.911" xlink:href="#B"><stop offset="0" stop-color="#c2c1c2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#DV)" d="M161 263h2c1 2 2 2 4 4l2 1c0 1 1 1 2 1l2 2h0c1 0 2 0 3 1h0l3 2h1s1 1 2 1v1c2 1 4 3 5 4v2c2 2 2 5 3 7s2 5 4 6c1 3 3 4 5 5 1 1 2 1 3 1 0 2 0 2-1 3l-1-1v1h-2c-13-2-9-16-16-24-4-4-9-6-13-9a30.44 30.44 0 0 1-8-8z"></path><path d="M206 396c1-1 3-2 4-2 3 2 4 5 6 8v2h-1c-2-1-3-1-4 0l-1 1c4 3 7 6 11 7v1c-3-1-5-2-7-4-1 1-2 1-3 2l-1 1c-2-1-5 0-7 1-1 0-3 1-3 1l-1-1-1-1h1c1-1 1-2 2-4-1-2-4-5-5-7 3 1 6 0 9-1l4-1v-1l-3-2z" class="R"></path><path d="M201 408l1 1c2-1 4-3 6-3l6 3c-1 1-2 1-3 2l-1 1c-2-1-5 0-7 1-1 0-3 1-3 1l-1-1-1-1h1c1-1 1-2 2-4zm5-12c1-1 3-2 4-2 3 2 4 5 6 8v2h-1c-2-1-3-1-4 0l-1 1c-1 0-2-1-2-1-3 0-5 3-7 2 2-2 7-4 9-5 0-1 0-1-1-2v-1l-3-2z" class="P"></path><path d="M764 185c3 3 6 7 10 10 5 4 10 7 14 11 1 1 1 2 1 3-1 1-2 1-4 2l-1-1c-1-1-3-2-4-3-5-1-8-4-12-8l-5-5c0-1 0-2-1-3l1-1h-1c0-1 0-1 1-1l3 3v-1c0-2-1-3-2-4l-1-1 1-1z" class="Z"></path><path d="M763 190c1 1 1 2 2 3 1 2 3 3 4 4 3 2 5 5 7 6 1 2 3 2 4 4-5-1-8-4-12-8l-5-5c0-1 0-2-1-3l1-1z" class="M"></path><defs><linearGradient id="DW" x1="683.684" y1="276.52" x2="690.444" y2="278.424" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#2e2d2d"></stop></linearGradient></defs><path fill="url(#DW)" d="M688 266c1 0 2 1 3 1l-1 2c0 1 0 1 1 2h3c1 2 0 2 0 3 0 3 3 4 2 7l-3 1-1 2c0 1 1 1 1 1-1 0-2 1-3 1h-1-6-1v-2l-1 1c0 1 0 2-1 3v1l-1 2c-1 1 0 1-1 2v1h0c0 2-1 3-2 5h0c0 1-1 2-2 3v-1c0-2 2-5 3-8l11-27z"></path><path d="M690 286l-7-1 1-1c3-1 6-1 9-2l-1 2c0 1 1 1 1 1-1 0-2 1-3 1z" class="D"></path><path d="M690 269c0 1 0 1 1 2h3c-1 2-2 2-2 4v1 1c-1 1-1 1-1 2v1h0c-1 0-1 0-1-1v-3c0-3-2-5 0-7z" class="B"></path><defs><linearGradient id="DX" x1="259.972" y1="163.808" x2="253.811" y2="140.885" xlink:href="#B"><stop offset="0" stop-color="#58595a"></stop><stop offset="1" stop-color="#7d7b7b"></stop></linearGradient></defs><path fill="url(#DX)" d="M239 144c13 4 26 8 38 15 3 2 5 3 8 3h0c-3 0-5 0-8-1-2 0-5 0-7-1-10-3-20-8-31-10 0-1 0-2-1-3 0-1 1-2 1-3z"></path><defs><linearGradient id="DY" x1="176.817" y1="278.853" x2="182.349" y2="271.403" xlink:href="#B"><stop offset="0" stop-color="#d2d1d3"></stop><stop offset="1" stop-color="#fcfbfd"></stop></linearGradient></defs><path fill="url(#DY)" d="M162 258c3 3 6 5 9 7 5 3 11 5 16 8 4 4 6 9 8 14l1 3v1l-1-1c0 2 1 3 0 5h-1 0c-2-1-3-4-4-6s-1-5-3-7v-2c-1-1-3-3-5-4v-1c-1 0-2-1-2-1h-1l-3-2h0c-1-1-2-1-3-1h0l-2-2c-1 0-2 0-2-1l-2-1c-2-2-3-2-4-4l-2-4 1-1z"></path><path d="M195 287l1 3v1l-1-1c0 2 1 3 0 5h-1 0v-4c0-2 1-3 1-4z" class="U"></path><path d="M550 666c12 4 23 11 34 18 3 2 8 5 10 8v1h-1c-1-1-2-1-3-1-6-3-12-7-18-10-1-1-3-2-4-2l-10-6-6-3-5-3 3-2zm120 64l1 1c2 4 4 7 9 8 12 5 26 10 31 23 1 1 1 2 1 4l1 5h0v-2c-2-8-7-14-13-18-9-6-18-6-28-9l-4 9c0-5 0-9-2-14-1-1 0-3 0-5 1 0 0 0 1 1h0 1v-3h2z" class="E"></path><path d="M233 164l61 1c-3 2-7 2-10 2-6 1-13 1-19 1l-25-1c-2 0-4 0-5-1-2 0-2-1-2-2z" class="V"></path><defs><linearGradient id="DZ" x1="588.407" y1="330.636" x2="567.06" y2="316.027" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#e8e7e8"></stop></linearGradient></defs><path fill="url(#DZ)" d="M568 304c2 1 2 2 3 4v1c0 1 1 1 1 2 1 0 1 1 1 2l1 1v-1l1-4c1 2 3 5 4 7 1 3 2 5 4 7 3 3 6 6 9 10h-1-1c-2-2-4-3-5-4l-3 1c0-2-1-3-2-4s-3 0-5 0h0c-1 1-1 1-1 2l-2 1c-1-1-3-2-5-3 1-3 3-5 3-8 1-4-1-10-2-14z"></path><path d="M574 314l2 7c0 1-1 1-1 2h-2v-8c0-1 0 0 1-1z"></path><defs><linearGradient id="Da" x1="695.979" y1="274.811" x2="713.596" y2="272.849" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#7a7979"></stop></linearGradient></defs><path fill="url(#Da)" d="M692 268l1 1s1 0 2 1h0c7 0 13-2 19-4 0 4-2 12-4 15-2-1-3-1-5-1-3 1-6 1-8 2l-1-1c1-3-2-4-2-7 0-1 1-1 0-3h-3c-1-1-1-1-1-2l1-2 1 1z"></path><path d="M412 429c0 1 1 2 0 3l1 1v3l-4 9h0l-10 17h0 0v-2l1-1h-1-1c0 2-1 3 0 4v1c3 2 4 3 4 6l1 2-2-1v1c-3-3-4-6-7-8v1l-1-3 3-7 3-4 6-10c3-3 5-8 7-12z" class="V"></path><path d="M412 432l1 1v3l-4 9h0l-1-1s0-1 1-1v-1-1-2c1-1 1-3 2-4l1-3z" class="L"></path><path d="M399 451l6-10c0 3-1 4-2 7l-5 9v2c0 2-1 3 0 4v1c3 2 4 3 4 6l1 2-2-1v1c-3-3-4-6-7-8v1l-1-3 3-7 3-4z" class="X"></path><path d="M398 457v2c0 2-1 3 0 4v1c3 2 4 3 4 6l1 2-2-1c-1-2-5-5-5-8-1-2 1-4 2-6z" class="Q"></path><defs><linearGradient id="Db" x1="245.407" y1="410.854" x2="218.629" y2="393.804" xlink:href="#B"><stop offset="0" stop-color="#797879"></stop><stop offset="1" stop-color="#a6a4a4"></stop></linearGradient></defs><path fill="url(#Db)" d="M251 396c0 2 0 3 2 4 1-1 1-2 2-2-2 4-4 6-8 8-2 0-5 1-6 2-2 1-4 4-6 5-3 2-10 0-14-1s-7-4-11-7l1-1c1-1 2-1 4 0h1v-2c1 2 2 3 4 4 3 3 7 4 11 3 3-1 4-4 7-6s7-1 10-3c1-1 2-2 3-4z"></path><defs><linearGradient id="Dc" x1="325.231" y1="605.73" x2="314.448" y2="631.346" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#959595"></stop></linearGradient></defs><path fill="url(#Dc)" d="M325 609c1 0 3-1 4-1 1 1 3 1 4 1l1 1c-2 3-4 5-6 7l-10 12h-14c8-5 14-13 21-20z"></path><defs><linearGradient id="Dd" x1="190.292" y1="229.175" x2="169.815" y2="238.403" xlink:href="#B"><stop offset="0" stop-color="#767575"></stop><stop offset="1" stop-color="#8d8c8d"></stop></linearGradient></defs><path fill="url(#Dd)" d="M163 215h2c1 3 1 6 2 9 1 4 4 6 6 10 2 3 3 7 4 11 8-1 15-1 22 2l2 3-1 1c-3-1-8-2-12-3h-6c-2 0-4 1-6 0-3-2-3-4-4-6-1-6-2-9-6-13-1 0-2-1-2-2-1-2-2-5-2-7v-1l-1-1v-1h-1c1-1 2-1 3-2z"></path><defs><linearGradient id="De" x1="432.28" y1="631.101" x2="455.949" y2="609.187" xlink:href="#B"><stop offset="0" stop-color="#565557"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#De)" d="M437 594c1-1 3-2 4-3 3 14 6 28 8 42 0 3 1 7 1 9-1 2-1 4 0 6-1 0-1-1-2-1v1l-1-1-1-1v1c-1-4-1-8-1-11l-3-21c-1-7-3-14-5-21z"></path><defs><linearGradient id="Df" x1="624.239" y1="228.112" x2="635.614" y2="200.743" xlink:href="#B"><stop offset="0" stop-color="#4f4f4e"></stop><stop offset="1" stop-color="#a7a5a7"></stop></linearGradient></defs><path fill="url(#Df)" d="M633 228c1-2 1-5 0-7-2-10-10-18-11-28-1-3-1-11 1-14-1 3-1 7-1 9 1 11 9 16 16 22 1 2 1 4 1 7l-1 2-1-3v2c2 5 1 10 1 15-1 1 0 1-1 1h-1l-2-2c-1-1-1-2-1-4z"></path><path d="M651 697v-1l5-2c1 3 1 6 0 9-3 4-7 4-9 8l1 1c1 1 2 3 3 4h0v1l2 2 1 1-1-1-1 1c0 1 0 1-1 2 1 1 1 1 3 1h1 1l-1 1-1 9-15-24v-1c3-1 8-3 11-6 1-2 1-3 1-5z" class="S"></path><defs><linearGradient id="Dg" x1="373.803" y1="792.551" x2="385.343" y2="797.954" xlink:href="#B"><stop offset="0" stop-color="#d8d6d5"></stop><stop offset="1" stop-color="#fffeff"></stop></linearGradient></defs><path fill="url(#Dg)" d="M385 769c1-2 1-2 2-2l2 1c-1 2-1 3-1 5-2 14-3 28-9 41-1 1-1 1-1 2l-3 4c-1 2-3 3-4 4-1 0-2 0-2-1 3-4 6-8 8-13 3-8 4-17 5-25 0-5 2-11 3-16z"></path><defs><linearGradient id="Dh" x1="750.822" y1="413.497" x2="769.713" y2="424.639" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#Dh)" d="M752 419c-1 0-2-1-2-2 0-2 2-6 3-8 2-1 5-1 7-1h1l1 2-1 3h-2 0c4 1 7 3 11 5 0 0 1 0 2 1v3l-2 6-16-7c0-1-1-1-2-2z"></path><path d="M770 418s1 0 2 1v3l-1-1c-1 0-2-1-3-2l2-1z" class="X"></path><path d="M755 413h4c4 1 7 3 11 5l-2 1h-2c-4-2-8-4-11-6z"></path><path d="M760 408h1l1 2-1 3h-2 0-4l-1-1-1-2c2-2 5-1 7-2z" class="C"></path><defs><linearGradient id="Di" x1="722.832" y1="515.171" x2="732.404" y2="524.57" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#Di)" d="M721 513l2-1v2 1l3-1 1 1c2 5 5 8 9 12l2 2c-1 2-2 3-2 5h0l1 2h0c-1-1-3-2-4-3-1 2-1 3-1 4l-3-3-6-6-2-3h0v-3h2c-2-2-6-5-6-7v-1c1-1 3-2 4-1z"></path><path d="M736 527l2 2c-1 2-2 3-2 5h0l-2-1c1-2 1-4 2-6z" class="P"></path><path d="M723 522c-2-2-6-5-6-7v-1c1-1 3-2 4-1 1 2 2 5 3 8 3 4 6 8 10 12l2 1 1 2h0c-1-1-3-2-4-3l-2-2-8-9z"></path><defs><linearGradient id="Dj" x1="720.038" y1="523.806" x2="726.813" y2="527.413" xlink:href="#B"><stop offset="0" stop-color="#565555"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#Dj)" d="M723 528l-2-3h0v-3h2l8 9 2 2c-1 2-1 3-1 4l-3-3-6-6z"></path><path d="M729 534v-2l2-1 2 2c-1 2-1 3-1 4l-3-3z" class="Y"></path><path d="M383 337v-3c0-1 1-2 1-3h1l1 1 4 16c1 3 2 7 3 10 2 2 2 3 3 6v-1l2 3 3 7-2 5-1-3v3 1 3l-1-1c0-1 0-3-2-4-1-1-2-5-3-8-3-10-7-22-9-32z" class="W"></path><path d="M393 358c2 2 2 3 3 6v-1l2 3 3 7-2 5-1-3c-2-5-4-12-5-17z" class="K"></path><defs><linearGradient id="Dk" x1="603.438" y1="236.591" x2="615.256" y2="220.817" xlink:href="#B"><stop offset="0" stop-color="#c6c5c5"></stop><stop offset="1" stop-color="#fbfafc"></stop></linearGradient></defs><path fill="url(#Dk)" d="M597 210h4c1 3 2 4 5 6 7 3 13 4 16 11 1 5 0 10 0 15h0c-1 2-1 3-1 4l1 1h-3l-1 3v-1c-1-4-1-7-1-10s1-6 0-9c-1-6-10-8-15-11-3-2-5-5-5-9z"></path><path d="M533 764c1 0 3 0 4 1h0c6 3 9 7 10 13l1 4c2 5 2 9 1 14 0 2 0 4-1 6 0-1-1-3-1-4l-4-10-7-18-2-3-1-3z" class="a"></path><path d="M544 783c2 3 4 10 3 14v1l-4-10 1 1v1c1 0 1-1 1-1v-1-1c-1-2-1-2-1-4h0z" class="C"></path><path d="M533 764c1 0 3 0 4 1h0l2 3 1 3c-1 0-2 0-3-1h0-1l-2-3-1-3z" class="T"></path><path d="M533 764c1 0 3 0 4 1h0l2 3-3-2c-1 1-1 1-2 1l-1-3z" class="G"></path><path d="M536 770h1 0c1 1 2 1 3 1 1 3 4 9 4 12h0c0 2 0 2 1 4v1 1s0 1-1 1v-1l-1-1-7-18z" class="F"></path><path d="M838 206c4-4 10-7 14-12 2-4 3-10 2-14v-1c3 0 6 0 9-2s4-5 5-8c0 2 0 4-1 6-3 4-7 5-11 6 1 4 1 7 1 10s-1 5-2 8h0c-2 4-5 7-7 11h-1v2 1h0c-3-1-5-1-6-2l-3-3 1-1-1-1z" class="W"></path><path d="M839 207h3c5-2 9-7 13-12-2 5-7 9-11 13 1 1 2 1 3 2v2 1h0c-3-1-5-1-6-2l-3-3 1-1z"></path><path d="M847 212c-2-1-3-2-4-3l1-1c1 1 2 1 3 2v2z" class="U"></path><path d="M855 195v-2c0-1 1-1 2-2 0 3-1 5-2 8h0c-2 4-5 7-7 11h-1c-1-1-2-1-3-2 4-4 9-8 11-13z" class="L"></path><defs><linearGradient id="Dl" x1="787.29" y1="118.907" x2="796.511" y2="123.494" xlink:href="#B"><stop offset="0" stop-color="#424142"></stop><stop offset="1" stop-color="#5f5d5e"></stop></linearGradient></defs><path fill="url(#Dl)" d="M784 123c1-1 1-2 2-3h15v1c4 0 7 0 11 2h2c2 1 5 3 5 5 1 2 1 5 1 7v3 2c-4-3-4-6-6-10-1-2-3-3-4-4-2-1-3-1-5-1v2l-1-2c-1-1-2 0-3 0-2 0-3 1-4 2v1l-1-1h-1l-2 1c-3 0-7-2-9-4v-1z"></path><defs><linearGradient id="Dm" x1="786.125" y1="121.336" x2="796.375" y2="126.664" xlink:href="#B"><stop offset="0" stop-color="#545354"></stop><stop offset="1" stop-color="#6d6c6e"></stop></linearGradient></defs><path fill="url(#Dm)" d="M784 123h4 4c2-1 4-1 7-1 1 0 2-1 4 0 1 0 2 0 3 1 1 0 1 0 2 1 1 0 1 1 2 1v1c-2-1-3-1-5-1v2l-1-2c-1-1-2 0-3 0-2 0-3 1-4 2v1l-1-1h-1l-2 1c-3 0-7-2-9-4v-1z"></path><defs><linearGradient id="Dn" x1="720.631" y1="345.643" x2="713.891" y2="351.837" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#525151"></stop></linearGradient></defs><path fill="url(#Dn)" d="M713 354l4-22c1 0 1 1 0 2v1 3c-1 1 0 2-1 4h0c1 0 1 0 2 1h1c1 1 2 2 2 4h1 0c0-2 1-2 1-3 1 1 1 1 1 2h1 0l1-1v-3c2 2 1 6 1 9-1 4-2 9-3 13-1 3-1 5-3 8v-2c-1-1-2-1-3-2v-1-7-3l-3-4-2 1z"></path><path d="M515 682c1-1 2-2 4-1v1h1v1c-1 1-3 2-4 3l-7 4c-6 4-13 8-20 12l-15 12c-3 2-10 9-13 9 6-8 14-14 22-20 10-8 20-15 32-21z" class="E"></path><defs><linearGradient id="Do" x1="252.989" y1="477.305" x2="264.126" y2="449.611" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#919090"></stop></linearGradient></defs><path fill="url(#Do)" d="M239 460h1c-1-1-1-2-1-3s0-1 1-1l2 2v-1h0c2 5 5 14 9 16 5 1 13-2 18-4l8-6c2 0 4-4 6-6-1 1-1 2-1 3v1 2l-3 2v1h1c-3 3-7 6-11 8-5 3-11 3-17 3h-4c-4-2-7-13-9-17z"></path><defs><linearGradient id="Dp" x1="448.563" y1="458.163" x2="412.655" y2="448.629" xlink:href="#B"><stop offset="0" stop-color="#565756"></stop><stop offset="1" stop-color="#a9a7a9"></stop></linearGradient></defs><path fill="url(#Dp)" d="M417 428v-1l2 1h0c0-2-1-4 0-5l2 3c-1 1-1 1 0 2 0 0 0 1 1 1v2 1c1 2 2 3 4 4 2 9 8 18 12 26 0 2 2 4 2 6s0 2 1 4l1 1v1c1 1 1 2 1 3l-1-1c0 2 1 3 1 5l-2-2c-1-1-3-4-3-6l-10-20-11-25z"></path><defs><linearGradient id="Dq" x1="355.329" y1="623.63" x2="343.243" y2="640.345" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#a9a8a8"></stop></linearGradient></defs><path fill="url(#Dq)" d="M354 610l2 1v8c-1 4-2 8-4 11-1 2-3 5-2 6v1c3 1 5 1 8 0l2-1v1l-2 2v1l4-2v1l-1 1c-4 1-7 2-11 2-4-1-8-3-11-3-1 0-1 0-1-1 4-5 10-12 12-18v-1h0c1-3 3-6 4-9z"></path><path d="M360 636v1l-2 2v1c-1 0-3 1-4 1s-2-1-2-2c-2-1-1-1-2-2 3 1 5 1 8 0l2-1z" class="D"></path><defs><linearGradient id="Dr" x1="355.933" y1="615.607" x2="350.875" y2="620.118" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#Dr)" d="M354 610l2 1v8c-1 4-2 8-4 11v-2c-1-1 0-1 0-2l1-1v-1c0-2-1-2-2-3 0-1 0-1-1-2 1-3 3-6 4-9z"></path><defs><linearGradient id="Ds" x1="644.518" y1="410.195" x2="660.253" y2="431.003" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="gray"></stop></linearGradient></defs><path fill="url(#Ds)" d="M657 399s1 1 2 1c1 1 2-1 2 2v1l-9 36-9 4 14-43v-1z"></path><defs><linearGradient id="Dt" x1="250.817" y1="188.91" x2="238.775" y2="191.941" xlink:href="#B"><stop offset="0" stop-color="#2c2b2b"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#Dt)" d="M245 172h-4-1c2 7 3 13 6 19 3 4 6 8 8 12 2 3 4 7 3 10h-1l-2-1c-2-2-3-5-5-8-6-10-11-20-15-31-1-3-3-7-3-9h2c0 1 0 2 2 2 1 1 3 1 5 1v1h-3c1 1 2 1 3 1l-1 1c0 1 0 1 1 1 2 1 3 1 5 1z"></path><defs><linearGradient id="Du" x1="501.075" y1="463.444" x2="516.538" y2="454.631" xlink:href="#B"><stop offset="0" stop-color="#777677"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#Du)" d="M501 428l1 3h1v2c-1 7-3 14 2 21 1 2 4 5 4 8 1 2-1 5 0 7 2 5 11 7 12 13l-3 6c-2 0-2 0-4-1l2-6-7-6c-3-3-4-6-4-10v-3c0-1-2-2-2-3-1-2-3-3-4-5-1-4-1-8-1-12h1v-2c0-2 1-3 1-4 1-3 1-5 1-7v-1z"></path><path d="M376 367c2 3 1 9 1 13-2 11-5 22-9 33l-6 11c0 1-1 2-1 3-3 4-6 7-9 10-2 1-2 2-4 2 3-4 6-9 9-14 5-9 10-18 12-28 3-10 5-20 7-30z" class="X"></path><path d="M426 453h2l10 20c0 2 2 5 3 6l14 31c1 2 2 3 3 5v1c1-1 1-1 2-1 1 1 1 2 1 3h1 0l13 44c-1-1-2-3-3-4-1-2-1-4-2-6l-5-13-6-17-11-25-6-12c-2-3-3-6-5-9-2-8-7-16-11-23z" class="R"></path><defs><linearGradient id="Dv" x1="260.334" y1="406.781" x2="211.125" y2="411.738" xlink:href="#B"><stop offset="0" stop-color="#656464"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#Dv)" d="M256 400l2-2c1 0 1 0 2 1v1c-1 0-1 1-1 2-1 2-2 4-4 6-3 3-7 3-10 5s-6 5-9 7c-5 1-12 0-17-3-2-1-7-3-9-5l1-1c1-1 2-1 3-2 2 2 4 3 7 4 4 2 8 3 12 2 4 0 5-3 8-5 3-3 7-3 10-5 2-1 3-3 5-5z"></path><defs><linearGradient id="Dw" x1="278.951" y1="161.405" x2="254.165" y2="135.128" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#b9b6b6"></stop></linearGradient></defs><path fill="url(#Dw)" d="M251 145h0c-1-1-1-1-2-1h0c-1-1 0-2 0-4 2 1 3 1 5 1h0c4 1 7 2 10 4l16 5 2 1 6 3-1 1c-1 0-2 1-3 1v1c0 1 0 0-1 1v2h-1c-4 0-10-5-14-7l-17-8z"></path><defs><linearGradient id="Dx" x1="361.388" y1="270.318" x2="352.74" y2="275.4" xlink:href="#B"><stop offset="0" stop-color="#1a1919"></stop><stop offset="1" stop-color="#414042"></stop></linearGradient></defs><path fill="url(#Dx)" d="M356 289v-31c1 2 2 4 2 6 1 3 2 5 2 7-1 4 0 8 0 12v7c2 10 2 21 5 30-1 0-2-2-3-3-3-3-5-7-6-11v-17z"></path><path d="M356 289v-1c1 1 3 19 4 22h-1c-1-1-2-3-3-4v-17z" class="K"></path><defs><linearGradient id="Dy" x1="384.459" y1="531.894" x2="422.526" y2="539.009" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#Dy)" d="M392 527l6 2c8 4 16 8 23 12 1 1 3 2 4 2v1l1 1 4 2 6 3-1 1c-1 0-2 0-3-1v-1l-2 2-13-8c-1 0-8-3-10-4-6-3-13-4-20-6v-1h-1l-1-1c0-2 1-3 2-4 1 0 1 0 2 1 2 0 2 0 3-1z"></path><path d="M417 543c2 1 3 1 5 2v-1h1c2 2 6 4 9 5l-2 2-13-8z" class="D"></path><defs><linearGradient id="Dz" x1="409.916" y1="873.552" x2="429.029" y2="870.55" xlink:href="#B"><stop offset="0" stop-color="#908f90"></stop><stop offset="1" stop-color="#b3b2b2"></stop></linearGradient></defs><path fill="url(#Dz)" d="M431 863l-6 9c-5 7-11 16-14 25l2-17c0-4 0-8-1-11-1-4-3-8-6-12l1-1 2-1c0 1 2 2 3 2 1 1 1 2 2 3h1 0c2 5 3 11 2 16 3-4 5-8 8-12 2 1 2-1 4-1h2z"></path><defs><linearGradient id="EA" x1="630.401" y1="470.172" x2="649.979" y2="483.051" xlink:href="#B"><stop offset="0" stop-color="#1f1f1f"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#EA)" d="M633 496c2-18 6-36 13-53l-6 60-1-1v-1h-3c-1 1-2 1-3 1 1-2 0-3 0-5v-1z"></path><defs><linearGradient id="EB" x1="682.953" y1="409.273" x2="689.738" y2="416.737" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#a09fa0"></stop></linearGradient></defs><path fill="url(#EB)" d="M705 393l3 3c-3 5-6 10-9 14-8 9-21 14-32 18h-3l-1 1c-2-1-2-1-3-3v-1c2-2 5-2 8-3 4-1 8-3 11-4 4-2 7-4 10-6 7-4 12-12 16-19z"></path><path d="M660 426l2-1 1 3h1l-1 1c-2-1-2-1-3-3z" class="H"></path><path d="M769 173l-16-19c-7-7-11-15-15-24-1-2-2-5-2-8 2 6 5 12 9 18 9 13 21 25 33 36 6 4 11 8 16 13 1 0 2 0 2 1l-1 1h0c-1-1-2-1-3-1h-1l-1-1c-1 1-2 1-2 3h0c-2 0-3-1-4-2 0-2 0-3 1-5h0c-5-5-11-8-16-12z" class="E"></path><path d="M785 185l2 1h0 2l3 2c1 1 1 1 2 1h0c1 0 2 0 2 1l-1 1h0c-1-1-2-1-3-1h-1l-1-1c-1 1-2 1-2 3h0c-2 0-3-1-4-2 0-2 0-3 1-5z" class="Q"></path><path d="M709 182l7 9c3 7 8 14 10 21h0l-2 14c-1-2 0-5-1-7s-4-5-5-7c-3-4-7-9-8-13s-1-8-1-12v-5z" class="D"></path><defs><linearGradient id="EC" x1="200.599" y1="257.617" x2="150.917" y2="229.93" xlink:href="#B"><stop offset="0" stop-color="#707070"></stop><stop offset="1" stop-color="#b3b2b3"></stop></linearGradient></defs><path fill="url(#EC)" d="M154 227c0-2 0-3 2-4v1l1 1h0c1 2 1 3 4 4 4 2 8 5 10 10 1 3 1 8 4 9 3 2 10 0 14 1 2 0 6 2 8 2l1 1c1 1 2 1 4 2v2h-2-2c-1 0-3-1-4-1-2-1-5-2-8-2s-6 1-9 1c-5 0-9-6-9-10-1-2-1-5-2-6-2-2-4-3-6-4s-5-2-6-3v-4z"></path><path d="M741 170l3 3h1l2 3c3 5 8 9 12 12l3 3c1 1 1 2 1 3l5 5v2l3 2-1 1c-3-2-6-5-8-7-1 2 2 5 4 7-1 1-1 2-2 2h0c-2-4-6-5-7-8l-2-2c-1 0-2-1-2-2v-2h0v-1c-1-2-2-4-4-4s-4 0-6-1c-1-2-1-4-3-5l-1-1c0-1 1-1 1-2h4 0v-1l-1-2c-2-2-2-3-2-5z" class="K"></path><path d="M768 201h-1c-2-3-5-6-7-8s-5-4-7-6c-1-2-2-3-3-4v-1c4 4 9 8 13 12l5 5v2z" class="F"></path><path d="M744 178h0l1 2 3 3 8 9c2 1 5 3 6 5-1 2 2 5 4 7-1 1-1 2-2 2h0c-2-4-6-5-7-8l-2-2c-1 0-2-1-2-2v-2h0v-1c-1-2-2-4-4-4s-4 0-6-1c-1-2-1-4-3-5l-1-1c0-1 1-1 1-2h4z" class="B"></path><path d="M745 180l3 3c-2 0-2 0-3-1v-2z" class="I"></path><defs><linearGradient id="ED" x1="595.85" y1="243.421" x2="612.008" y2="222.232" xlink:href="#B"><stop offset="0" stop-color="#c4c3c4"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#ED)" d="M592 214c0-2 1-4 2-5l1 1 1 1c2 6 5 9 11 12 3 1 7 3 9 7s0 10 0 14 0 8 1 11c-1 0-2 1-3 2-1 2 0 2 0 4h-1c-1-4-2-10-2-15 0-4 1-11 0-14-2-4-10-7-14-10-3-3-4-5-5-8z"></path><defs><linearGradient id="EE" x1="506.429" y1="630.384" x2="494.394" y2="637.291" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#868687"></stop></linearGradient></defs><path fill="url(#EE)" d="M489 601c1 0 2 0 2 1 1 2 3 5 3 8l14 41c3 8 6 16 8 24 0 2 1 3 0 5 0 0-1 0-1 1-7-14-11-29-15-43-3-10-6-19-8-29l-3-8z"></path><path d="M489 601c1 0 2 0 2 1 1 2 3 5 3 8 0-1-1-2-2-3v-1l-1-1h0 0c1 1 1 2 1 3v1l-3-8z" class="C"></path><path d="M416 426l1 2 11 25h-2c4 7 9 15 11 23 0 2 1 4 2 6l1 1v2h-1c-1-2-2-6-5-8-3-4-4-10-7-13l-1 1v-4h0c-1-3-3-6-4-9l-9-16v-3l-1-1c1-1 0-2 0-3s1-2 1-2l1-1h2z" class="U"></path><path d="M416 426l1 2 11 25h-2c-1-1-2-4-3-5-3-7-7-14-10-21l1-1h2z" class="I"></path><defs><linearGradient id="EF" x1="592.612" y1="364.527" x2="591.172" y2="343.523" xlink:href="#B"><stop offset="0" stop-color="#5c5c5c"></stop><stop offset="1" stop-color="#7c7b7b"></stop></linearGradient></defs><path fill="url(#EF)" d="M582 330l3-1c1 1 3 2 5 4v2l6 10 2 3c1 4 1 6-1 9l-2 1 2 1c0 1-1 2-1 3h0l1 1-3 3c-2 0-3 0-5-1 0-2-1-4-1-6-1-2 0-4 0-6 1-2 1-4 1-7-1-1-1-2-3-3l1-2c-1-4-3-8-5-11z"></path><path d="M590 335l6 10 2 3c1 4 1 6-1 9l-2 1c0 1-1 1-1 1-1-3 0-6 0-8 0-3-1-6-1-9l-3-7z"></path><defs><linearGradient id="EG" x1="590.726" y1="348.188" x2="585.998" y2="329.619" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#aaa9a9"></stop></linearGradient></defs><path fill="url(#EG)" d="M582 330l3-1c1 1 3 2 5 4v2l3 7-1 1-2 6h-1v-3c-1-1-1-2-3-3l1-2c-1-4-3-8-5-11z"></path><defs><linearGradient id="EH" x1="742.102" y1="172.129" x2="750.945" y2="166.325" xlink:href="#B"><stop offset="0" stop-color="#6d6d6d"></stop><stop offset="1" stop-color="#918f90"></stop></linearGradient></defs><path fill="url(#EH)" d="M723 138c2 2 7 11 9 11l1 3h0l2 2v1h1c0 1 1 1 2 2l1 1c3 5 7 9 12 14l9 9 4 4-1 1 1 1c1 1 2 2 2 4v1l-3-3c-1 0-1 0-1 1h1l-1 1-3-3c-4-3-9-7-12-12l-2-3h-1l-3-3-3-3c0-3-2-4-3-6-2-2-3-5-5-7 0-1-2-3-2-4s1-1 1-2c-1 0-2-2-2-2-1-3-3-5-4-8z"></path><path d="M745 173l1-1c3 3 6 6 8 9 3 3 5 7 8 9h1l-1 1-3-3c-4-3-9-7-12-12l-2-3z" class="X"></path><defs><linearGradient id="EI" x1="389.264" y1="833.914" x2="401.056" y2="839.07" xlink:href="#B"><stop offset="0" stop-color="#c6c4c4"></stop><stop offset="1" stop-color="#fbfafd"></stop></linearGradient></defs><path fill="url(#EI)" d="M401 808l1-1c1-1 1-3 2-5h2c0 1 0 1-1 2 0 1 1 1 0 2 0 1 0 1-1 2v1 3c0 5-2 10-3 15-3 16-6 31-13 45h-1c0 1-1 3-3 3h0c6-15 9-32 13-48 1-6 2-13 4-19z"></path><defs><linearGradient id="EJ" x1="267.723" y1="431.817" x2="256.968" y2="427.423" xlink:href="#B"><stop offset="0" stop-color="#757475"></stop><stop offset="1" stop-color="#969696"></stop></linearGradient></defs><path fill="url(#EJ)" d="M275 408c1 2 1 4 2 6-5 1-11 4-13 8-1 2-2 5-2 7 2-5 4-9 10-12l1 5c-3 1-6 3-7 7-3 8-3 16-5 25-2-2-2-4-3-5 3-11-3-22 3-32 3-6 8-7 14-9z"></path><path d="M565 352h0c-9-8-20-16-23-27-2-8-1-16 4-22 2-3 6-6 9-9 3-2 7-5 9-9 1-2 1-5 0-7-2-5-9-6-13-8s-7-5-8-9c-2-4-1-9 1-13 0 4-2 6-1 10 2 12 12 10 20 15 2 2 3 4 3 7 1 10-13 17-19 24-3 4-4 9-4 15 2 14 15 21 25 30v1l3 3-2 1h0c-1-1-2-2-4-2z" class="E"></path><defs><linearGradient id="EK" x1="685.894" y1="412.213" x2="692.82" y2="422.618" xlink:href="#B"><stop offset="0" stop-color="#4b4a4b"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#EK)" d="M709 395c1 1 2 2 2 4h0c0 1 0 2-1 3v1c0 1-1 1-1 2v1c-1 0-1 0-1 1l-1 1v1c-1 0-1 1-1 1 0 1-1 1 0 2-7 11-18 15-30 19-1 0-4 1-4 2-3-1-6-1-9-1l-1-1c2-2 6-2 9-3 11-4 22-9 30-19 3-4 6-9 8-14z"></path><defs><linearGradient id="EL" x1="577.258" y1="565.102" x2="586.24" y2="570.359" xlink:href="#B"><stop offset="0" stop-color="#333232"></stop><stop offset="1" stop-color="#4f4e4e"></stop></linearGradient></defs><path fill="url(#EL)" d="M589 545h0c1 1 2 2 3 2 1 1 1 1 1 2-3 7-8 14-11 22-2 5-3 11-4 17h-2l-4 1-1-1c2-16 9-30 18-43z"></path><path d="M287 135c5-4 11-7 17-10 5-3 10-6 15-7-7 9-16 18-26 25-1 1-3 3-4 3-2 1-3 1-4 2l-1 1c0-2 0-3 1-5v-3c1-2 1-4 2-6z" class="E"></path><defs><linearGradient id="EM" x1="654.296" y1="708.644" x2="674.407" y2="718.96" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#f5f4f6"></stop></linearGradient></defs><path fill="url(#EM)" d="M659 698h4c2 11 4 21 7 32h-2v3h-1 0c-1-1 0-1-1-1 0-2 0-3-1-5s-7-3-9-4h-1-1c-2 0-2 0-3-1 1-1 1-1 1-2l1-1 1 1-1-1-2-2v-1h0l1-2h6l-3-6c2-3 3-5 4-8v-2z"></path><path d="M651 716l1-2h6l3 7c-3-1-7-3-10-5h0z" class="G"></path><path d="M576 178c2-1 2-1 3-3 1-1 1-1 1-2 1 1 0 1 0 2h0v1c-1 1-1 2-2 2v1 1l2-1c1 1 1 1 1 2v2l-6 3v1c-4 3-4 5-5 9-1 3-3 4-5 6-3 3-4 7-9 9-3 2-6 2-9 1 3 0 5 0 8-1 3-2 5-5 6-8 1-5 1-12-2-17h-1c5-4 12-7 18-8z" class="W"></path><path d="M580 179c1 1 1 1 1 2v2l-6 3 1-1 1-1v-1h-1c-1 0-3 0-4 1h-1c-1 1-3 3-3 4l-1 1c-1 1 0 2 0 4-1 1-1 1-1 2h-1c1-2 0-5 0-7 2-1 3-3 5-4l1-1c1-1 3-1 5-2 0-1 1-1 2-1l2-1z" class="V"></path><path d="M488 585c4 7 6 16 9 23l17 51 5 15c1 2 1 4 3 6 0 2 2 2 3 3l2 3h0l-2 1c-1-1-4-1-5-2-1 1-2 1-3 1h-1c1-1 3-2 4-3v-1h-1v-1c-2-1-3 0-4 1v-1c0-1 1-1 1-1 1-2 0-3 0-5-2-8-5-16-8-24l-14-41c0-3-2-6-3-8l1-2c1 1 1 1 1 2v1c0 1 1 2 1 2v1h1l-1-1v-2c0-1 0-1-1-2v-2l-3-6v-1-1c-1 0-1-1-1-1v-2l-1-2h-1l1-1z" class="B"></path><defs><linearGradient id="EN" x1="754.105" y1="131.687" x2="741.559" y2="159.826" xlink:href="#B"><stop offset="0" stop-color="#696867"></stop><stop offset="1" stop-color="#9a999a"></stop></linearGradient></defs><path fill="url(#EN)" d="M729 126v-3h5 1v-2h1v1c0 3 1 6 2 8 4 9 8 17 15 24l16 19-2-2h0-2-1c-2-2-3-3-5-3l-6-6c-10-11-20-22-24-36z"></path><path d="M450 536l1 1c0 1 1 2 1 4 1 1 1 2 2 2 0 1 1 2 1 3 1 2 2 3 3 5v-1c2 6 4 11 6 17 4 16 7 32 10 48 0 3 1 8 1 11-1 2 0 5-1 7h0v-2c-1-2-1-2-1-3-1-1 0-2 0-3-1-2-1-2-1-3v-3c-1-1-1-2-1-3v-2c-1-2-1-3-1-4v-2l-1-1v-3-1l-1-2c0-2-1-3-1-4l-11-41c-2-7-5-13-6-20z" class="U"></path><defs><linearGradient id="EO" x1="518.76" y1="759.348" x2="531.933" y2="768.115" xlink:href="#B"><stop offset="0" stop-color="#434243"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#EO)" d="M533 764c-6 2-13 8-17 13l-4-9c2-3 5-7 8-9 3-3 6-5 10-6 1 0 3-1 3-1 3 0 4 3 6 4h1c1 1 2 2 2 3l-2 2-7 3z"></path><defs><linearGradient id="EP" x1="484.88" y1="729.738" x2="493.183" y2="685.586" xlink:href="#B"><stop offset="0" stop-color="#626164"></stop><stop offset="1" stop-color="#bcbbb9"></stop></linearGradient></defs><path fill="url(#EP)" d="M509 690c3 1 3 1 5 0 0 1-2 2-2 3h0l-3 3-6 3c-13 8-29 19-39 31-2-1-4-2-6-2h0l-1-1c1-2 2-3 4-4 3 0 10-7 13-9l15-12c7-4 14-8 20-12z"></path><path d="M419 398l1-1h1v6c1 4 4 8 5 12s2 8 2 12h0l-2 9c-2-1-3-2-4-4v-1-2c-1 0-1-1-1-1-1-1-1-1 0-2l-2-3c-1 1 0 3 0 5h0l-2-1v1l-1-2h-2l-2-1v-3l1-3 1-3v-1c-1 1-2 2-2 3h-1l3-6c1-3 3-6 2-10 1-1 2-3 3-4z" class="G"></path><path d="M419 398l1 1-1 1c-1 2 1 8-1 9s-2 2-3 3h-1c1-3 3-6 2-10 1-1 2-3 3-4z" class="F"></path><path d="M414 412h1c1-1 1-2 3-3-1 3-1 4-1 6l2 8c-1 1 0 3 0 5h0l-2-1v1l-1-2h-2l-2-1v-3l1-3 1-3v-1c-1 1-2 2-2 3h-1l3-6z" class="D"></path><path d="M413 419c1 2 2 4 3 7h-2l-2-1v-3l1-3z" class="R"></path><path d="M419 417h0v-9l7 7c1 4 2 8 2 12h0l-2 9c-2-1-3-2-4-4v-1-2c-1 0-1-1-1-1-1-1-1-1 0-2 0-3-1-6-2-9z" class="N"></path><path d="M419 417h0v-9l7 7c1 4 2 8 2 12h0-1v1h0l-1-2c0-3-2-6-4-8l-1-1-1-1v-2 3h-1z" class="M"></path><path d="M398 529c-1-3-4-4-7-5h0c-1-1-1-2-2-2h-1-1l1-1h2 1c8 2 15 6 22 10 11 6 22 12 32 19l-17 15c-1-1-1-2-1-3 1-4 8-9 11-12l-13-7c-1 0-3-1-4-2-7-4-15-8-23-12z" class="W"></path><path d="M301 499c1 0 2 0 3-1l1 1h1v1c-1 2-2 1-3 2s-1 2-2 2l-21 12 3 2c5-5 9-7 15-10 1 0 3-1 4-1 0 0 1 1 1 2h-1c-1 0-1 1-2 2-6 3-11 6-15 11l-1 1c-4-1-8-2-12-2-5-1-10 0-16 0 16-6 30-14 45-22z" class="M"></path><defs><linearGradient id="EQ" x1="437.539" y1="270.879" x2="445.034" y2="227.896" xlink:href="#B"><stop offset="0" stop-color="#767575"></stop><stop offset="1" stop-color="#cfcfcf"></stop></linearGradient></defs><path fill="url(#EQ)" d="M430 268c3-5 6-10 8-15l4-12 6-13 3 1-2 18c0 4 0 8-1 12s-4 8-6 12h-1l-3-3c2-4 4-8 5-12l3-14h-1c-1 4-2 9-3 13-2 6-6 11-9 16l-3-3z"></path><defs><linearGradient id="ER" x1="225.046" y1="359.3" x2="205.941" y2="383.01" xlink:href="#B"><stop offset="0" stop-color="#c9c8c9"></stop><stop offset="1" stop-color="#f8f7f8"></stop></linearGradient></defs><path fill="url(#ER)" d="M229 376s-2 2-3 2l-9 7c-4 0-9-3-13-5-3-1-6-3-9-5-6-3-10-7-11-13-1-3-1-6 0-8 0 5 1 10 4 14s10 7 15 8c11 0 21-8 28-15l3 4c-3 2-8 6-10 9 1-1 2-1 3-2 1 0 2-1 3 0v2-1l-1 3z"></path><path d="M216 375h1l-2 6c-2 1-4 0-5-1l-3-1c3-2 6-3 9-4z"></path><defs><linearGradient id="ES" x1="844.816" y1="279.761" x2="865.053" y2="296.398" xlink:href="#B"><stop offset="0" stop-color="#c3c1c1"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#ES)" d="M844 293c4-1 6-2 9-6l1-2c1-1 5-1 6-1 5-1 9-1 13-3 5-2 8-6 12-9 3-3 7-5 11-6h1c2-1 8-1 10 0h0-1-6c-7 2-12 5-17 10-2 3-4 5-7 7s-12 3-14 5c-1 2-2 4-2 5-3 5-8 7-12 8-3 1-8 2-12 1h-5c-2-1-2-2-2-4 5 0 12 1 18-1l1-1h0c4-1 7-3 9-7h0l-1 2c-2 3-6 5-10 5h-5c-1 1-2 0-3 0s-1-1-1-2c3 0 4 0 6-1h1z"></path><defs><linearGradient id="ET" x1="391.398" y1="687.981" x2="366.421" y2="696.122" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#ET)" d="M366 678c1-2 2-4 2-6v-1c2 0 2 0 4 1v2c-1 7-1 13-1 20 1 4 1 7 4 11 1 2 5 6 8 6 1 0 1 0 2-1 0-1 0-1-1-1l2-1c0-1 1-2 2-2v-1l-1-1c1-1 1-1 1-2v-1c1-2 3-4 5-5l3 2c-3 5-4 10-6 15-2 3-5 3-8 4-4-2-8-6-11-10-4-3-4-8-4-12-1-6-1-12-1-17z"></path><path d="M398 366v-2c2 1 3 3 4 5 2 1 4 4 6 5 5 7 6 18 5 26-1 3-2 6-4 8h-1c-1-2-2-3-3-5s-2-4-2-5v-1c1-6-1-10-2-15l-2-4 2-5-3-7z" class="R"></path><path d="M407 376c2 2 0 5 1 8 1 2 1 5 1 8v9c-1-3-1-6-2-9 0-6-1-11 0-16z" class="I"></path><path d="M402 369c2 1 4 4 6 5l-1 1v1c-1 5 0 10 0 16-1-2-1-4-1-7 0-5-2-11-4-16z" class="B"></path><defs><linearGradient id="EU" x1="405.931" y1="376.125" x2="398.569" y2="372.875" xlink:href="#B"><stop offset="0" stop-color="#373837"></stop><stop offset="1" stop-color="#504f51"></stop></linearGradient></defs><path fill="url(#EU)" d="M398 366v-2c2 1 3 3 4 5 2 5 4 11 4 16l-2-2-2-6-1-4-3-7z"></path><path d="M401 373l1 4 2 6 2 2c0 3 0 5 1 7 1 3 1 6 2 9v7h-1c-1-2-2-3-3-5s-2-4-2-5v-1c1-6-1-10-2-15l-2-4 2-5z" class="C"></path><path d="M401 373l1 4 2 6c0 3 0 7 1 10v1c0 1 1 1 1 2v2 4l-1 1c-1-2-2-4-2-5v-1c1-6-1-10-2-15l-2-4 2-5z" class="X"></path><path d="M401 373l1 4-1 5-2-4 2-5z" class="M"></path><path d="M710 117v-2h0c1 1 2 3 3 4 0 1 1 1 1 2 3 6 7 11 9 17 1 3 3 5 4 8 0 0 1 2 2 2 0 1-1 1-1 2s2 3 2 4c2 2 3 5 5 7 1 2 3 3 3 6l3 3c0 2 0 3 2 5l1 2v1h0-4v-5l-1-1s-2 1-2 0h-1-2c-1-2-2-4-4-6s-4-4-5-8h0l-1-1c0-2 0-3 1-4l-10-20-4-10c-1-2-1-4-1-6z" class="V"></path><path d="M710 117v-2h0c1 1 2 3 3 4 0 1 1 1 1 2 3 6 7 11 9 17 1 3 3 5 4 8 0 0 1 2 2 2 0 1-1 1-1 2s2 3 2 4c2 2 3 5 5 7 1 2 3 3 3 6-2-3-4-6-7-10-3-6-8-13-11-20-2-4-3-9-5-12l-3-3-1 1c-1-2-1-4-1-6z" class="D"></path><path d="M725 153l6 8v-4c3 4 5 7 7 10l3 3c0 2 0 3 2 5l1 2v1h0-4v-5l-1-1s-2 1-2 0h-1-2c-1-2-2-4-4-6s-4-4-5-8h0l-1-1c0-2 0-3 1-4z" class="I"></path><path d="M731 157c3 4 5 7 7 10l3 3c0 2 0 3 2 5l1 2v1h0-4v-5l-1-1c-3-3-6-7-8-11v-4z" class="P"></path><path d="M740 173c1 2 3 3 4 5h-4v-5z" class="I"></path><defs><linearGradient id="EV" x1="367.985" y1="415.712" x2="374.426" y2="418.664" xlink:href="#B"><stop offset="0" stop-color="#858384"></stop><stop offset="1" stop-color="#b8b7b8"></stop></linearGradient></defs><path fill="url(#EV)" d="M381 380c2 1 2 3 3 5l1-1c0 1 1 2 1 3-1 1-1 3-1 4-2 8-5 16-7 24l-7 16c-1 3-3 7-3 10-1 1-2 1-3 3h-1v-1l-1 1c-1 0-3 1-4 1 9-18 17-38 21-58l1-7z"></path><path d="M381 380c2 1 2 3 3 5l1-1c0 1 1 2 1 3-1 1-1 3-1 4-2 8-5 16-7 24l-7 16c-1 3-3 7-3 10-1 1-2 1-3 3h-1v-1c1-3 4-7 5-11 6-10 11-24 13-35v-4-1h0l-1-2c-1-1-1-2-1-3l1-7z" class="J"></path><path d="M381 380c2 1 2 3 3 5 0 4-1 8-2 12v-4-1h0l-1-2c-1-1-1-2-1-3l1-7z" class="D"></path><defs><linearGradient id="EW" x1="469.988" y1="713.91" x2="501.571" y2="721.503" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#adacad"></stop></linearGradient></defs><path fill="url(#EW)" d="M509 696h2 2l-4 2c-2 3-4 6-5 9l1 1c-12 8-24 16-34 27l-3 3c-1-1-1-1-1-2-1-2-2-3-2-5l1-1c11-12 24-20 37-28v-3l6-3z"></path><path d="M466 730h0l2 6c1-1 2-1 3-1l-3 3c-1-1-1-1-1-2-1-2-2-3-2-5l1-1z" class="F"></path><path d="M509 696h2 2l-4 2-6 4v-3l6-3z" class="R"></path><path d="M389 775l1-1c1 0 1-1 1-2 2 0 2 0 3 1h0v1 1l-3 17c-3 8-4 17-9 25a57.31 57.31 0 0 1-11 11c-8 9-16 16-20 28-2 4-2 9-3 14-1-18 9-33 21-47h0c0 1 1 1 2 1 1-1 3-2 4-4l3-4c0-1 0-1 1-2 4-4 5-12 6-18l4-21z" class="W"></path><path d="M531 688c1-1 2-2 2-3l4-8c1-1 1-2 3-2l-6 19h-1c-8 3-15 7-22 10l-6 4-1-1c1-3 3-6 5-9l4-2h-2-2l3-3h0c0-1 2-2 2-3-2 1-2 1-5 0l7-4h1c1 0 2 0 3-1 1 1 4 1 5 2l2-1h0l-2-3 5 1h1v1 3z" class="U"></path><path d="M513 696c6-4 11-7 18-8v1c-1 1-2 2-4 2l-2 1c-2 1-4 2-7 3l-3 3c-2 1-4 3-4 5-1 1-1 1 0 1l-6 4-1-1c1-3 3-6 5-9l4-2z" class="P"></path><path d="M525 683l5 1h1v1 3c-7 1-12 4-18 8h-2-2l3-3h0c0-1 2-2 2-3-2 1-2 1-5 0l7-4h1c1 0 2 0 3-1 1 1 4 1 5 2l2-1h0l-2-3z" class="I"></path><path d="M516 686h1c1 0 2 0 3-1 1 1 4 1 5 2l-13 6h0c0-1 2-2 2-3-2 1-2 1-5 0l7-4z" class="P"></path><defs><linearGradient id="EX" x1="243.688" y1="125.53" x2="266.348" y2="139.411" xlink:href="#B"><stop offset="0" stop-color="#6f6e6d"></stop><stop offset="1" stop-color="#9c9b9e"></stop></linearGradient></defs><path fill="url(#EX)" d="M264 124l9 2c4 2 9 4 14 4l-2 1h-1c-1 1-2 2-4 3-2 3-5 7-7 10l-1 1c-1 1-1 0-1 0-1-3 3-7 5-10l2-2h-1c-4 0-8-1-12-1-8 0-14 3-22 4h-2-6l-3 1-3 1-1-1h1c6-5 14-9 21-11 4-1 9-1 14-2z"></path><path d="M264 124l9 2c4 2 9 4 14 4l-2 1c-2 0-3 0-5 1v-1h0c-2-1-4-2-7-2-8-2-15-3-23-1-6 2-10 5-15 8l-3 1-3 1-1-1h1c6-5 14-9 21-11 4-1 9-1 14-2z" class="I"></path><path d="M615 523c2-1 2-6 3-8 1 0 2-1 3 0h0c1 0 1 0 1 1-1 1 0 1 0 2l-1 1 1 2c-1 4-2 8-2 12-2 12-4 24-10 35h-4c-1 1-2 1-3 2h-1-1c1-1 2-2 2-4 5-10 7-21 10-33l-3-3c1 0 2 1 3 2 1-3 1-6 2-9z" class="L"></path><path d="M352 530c4 3 7 7 11 10s7 6 10 10c0 0 1 0 1 1v4l-2 22v2h-1l-19-49z" class="E"></path><defs><linearGradient id="EY" x1="293.675" y1="632.822" x2="280.104" y2="617.466" xlink:href="#B"><stop offset="0" stop-color="#646365"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#EY)" d="M322 610c-1 1-1 1-3 1l-3-3c-1-1-4-3-5-3-6 1-13 4-18 8-8 5-16 10-22 18-6 7-11 17-12 26 0 5 0 10 1 15-1-1-1-3-1-4-1-5-1-10-1-16h0l1-5c1-5 4-9 7-14 8-12 20-22 32-30 4-2 11-4 16-2 4 1 5 4 7 7l1 2z"></path><path d="M287 272l6 6c-4 16-14 31-20 46l-1 1h-1c-1-1-4-9-3-10 0-1 1-3 2-4l7-14c4-8 7-16 10-25z" class="E"></path><path d="M724 157l1 1h0c1 4 3 6 5 8s3 4 4 6h2 1l-1 3-5 14-2 3-1 5c-1-2-2-4-3-5-2-3-6-6-7-10l1-7 5-18z" class="Z"></path><path d="M731 189l-1-1c-1 0-1-1-1-2 0-2 2-8 4-10 1-1 2-1 3-1l-5 14z" class="L"></path><path d="M725 167h1c1 1 1 1 2 3 2 2 1 8 1 11h-1c-2-1-3-3-3-5v-1-1c-1-1-1-5 0-7z" class="S"></path><path d="M718 182l1-7 5 8c2 3 4 5 5 8v1l-1 5c-1-2-2-4-3-5-2-3-6-6-7-10z" class="K"></path><path d="M709 182c-1-2-1-4-1-7l18 23c10 14 17 32 18 49l-2-2c-1-5-4-11-7-17h-2c-1-5-4-11-7-16h0c-2-7-7-14-10-21l-7-9z" class="W"></path><path d="M716 191c2 1 2 3 3 5l7 12c1 0 2 1 3 2s1 3 1 5c1 1 2 3 2 4 2 3 3 6 3 9h-2c-1-5-4-11-7-16h0c-2-7-7-14-10-21z" class="V"></path><defs><linearGradient id="EZ" x1="298.445" y1="606.021" x2="290.871" y2="597.437" xlink:href="#B"><stop offset="0" stop-color="#6c6b6d"></stop><stop offset="1" stop-color="#838281"></stop></linearGradient></defs><path fill="url(#EZ)" d="M321 580c1-1 3-1 4-2v3h1c0-2 1-3 0-4h2l1 2c-1 2-2 2-4 3h0-1v1l-1 1c-1 2-16 10-18 11-6 4-13 8-19 13-8 6-15 15-21 24-2 3-3 6-5 9l1-3c3-13 12-26 22-35 6-4 13-8 20-12 6-4 12-8 18-11z"></path><defs><linearGradient id="Ea" x1="465.841" y1="282.52" x2="456.602" y2="234.684" xlink:href="#B"><stop offset="0" stop-color="#d2d0d1"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ea)" d="M467 245h1c1-3 0-6 1-9v-2-1c1-1 2-1 3-1 1 1 1 2 1 3s0 2 1 3c1 8-1 15-6 21-3 5-10 10-9 17h0c-1 0-2-1-2-2l-2 2v-5h0c-2 5 0 9 0 14-2 1-4 0-6-1 1-1 1-3 1-5 0-3-1-5 0-9 4-9 12-15 17-24v-1z"></path><defs><linearGradient id="Eb" x1="813.505" y1="353.892" x2="844.487" y2="382.418" xlink:href="#B"><stop offset="0" stop-color="#d6d6d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Eb)" d="M806 366v-1-1c0-1 0-1 1-2h3l17 8c4 2 9 5 14 5 2 1 4 0 6 0h0c4-1 7-3 10-6 0 1 0 0-1 1l-1 1c-1 2-2 3-4 4l-3 2s-1 1-2 1l1 1c-6 2-12 2-17 4-2 1-4 3-6 5-4-6-8-9-14-13 0 0-1-1-1-2h-1c-1 0-1-1-2-1h0c0-1 0-2 1-3l4 2v-1l9 5h0v-1h1c4 1 5 3 10 3-9-3-17-7-25-11z"></path><path d="M820 375h0v-1h1c4 1 5 3 10 3l7 1c-5 0-9 0-13 3l-5-6z" class="a"></path><defs><linearGradient id="Ec" x1="315.557" y1="360.184" x2="323.467" y2="359.79" xlink:href="#B"><stop offset="0" stop-color="#3f403e"></stop><stop offset="1" stop-color="#5e5b5e"></stop></linearGradient></defs><path fill="url(#Ec)" d="M312 343l1-1c1-5-2-12 2-17 4 18 10 35 17 51l8 17c1 3 3 6 4 9l-1 1c-1-1-1-1-1-2-1-2-3-1-4-3-2-2-3-6-5-8-1-3-2-6-4-8s-2-5-5-6l-12-33z"></path><defs><linearGradient id="Ed" x1="558.181" y1="758.164" x2="559.085" y2="726.815" xlink:href="#B"><stop offset="0" stop-color="#4e4d4e"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#Ed)" d="M544 735l-1-6c9-2 17-3 26-3h9c1 2 1 2 1 4-3 1-9 1-12 1l-14 3 4 20c0 2 0 2-1 2-2 1-3 2-4 3 0-1-1-1-1-2v-1l-2-2c0 1 1 3 1 5h-1c-2-6-3-12-6-18l-3-10h0c1 1 1 1 1 2l1-2 1 4h1 0z"></path><path d="M540 731h0c1 1 1 1 1 2l1-2 1 4h1 0c2 6 2 14 5 19 0 1 1 3 1 5h-1c-2-6-3-12-6-18l-3-10z" class="I"></path><defs><linearGradient id="Ee" x1="837.693" y1="252.109" x2="875.41" y2="258.065" xlink:href="#B"><stop offset="0" stop-color="#dad9da"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ee)" d="M874 244c3-1 3-4 5-6v1c-1 1-1 3-1 4-3 5-7 9-10 14-2 2-4 7-7 9s-7 4-11 5c-3 2-5 4-8 5l-1-9v-1l-1-1h-2v-1c1-1 2-1 2-2 1-1 0-2 0-3l2-1-1-1-7 2-1-1c1-3 8-3 11-5h-2v-1h3c4-1 11 6 14 9l15-17z"></path><path d="M842 258c2 1 3 2 3 4 1 2 2 3 2 5-1-1-2-1-2-2 0-2-1-5-3-6v3l-3 2 2 1c1 0 1 1 1 2h-1v-1l-1-1h-2v-1c1-1 2-1 2-2 1-1 0-2 0-3l2-1z" class="L"></path><path d="M841 257c1 0 3-1 5 0l11 6-9 5c-1 0-1 0-1-1 0-2-1-3-2-5 0-2-1-3-3-4l-1-1z" class="B"></path><defs><linearGradient id="Ef" x1="698.458" y1="328.211" x2="708.447" y2="332.61" xlink:href="#B"><stop offset="0" stop-color="#515050"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#Ef)" d="M713 303l2-2v5 2h1v1c0 1 0 1 1 2h-1c0 1 1 1 0 2v5c-8 17-20 31-34 45l31-60z"></path><g class="E"><path d="M824 113c11 3 21 7 29 15 2 2 5 5 5 9 0 1-3 4-4 5l-6 6c-5-4-8-9-11-15-2-5-4-11-7-15-2-2-4-4-6-5zM234 453c-10-6-21-6-32-10-4-2-5-8-10-10-7-2-15-1-21-6-8-4-11-12-13-20 2 2 3 6 4 8 3 6 8 11 14 12 5 2 10 2 15 2 2 0 4 1 6 0 2 0 3-1 4-1l1-1 1 3c1 3 2 5 5 6 5 2 10 3 15 4 3 0 7 1 10 0h0 3v2h0v1c0 1 0 2-1 2-3 1-9-1-11-2-6-1-16-2-21-5-2-1-3-3-4-5h0c0 2 1 4 3 5 2 2 5 3 8 4l14 3c4 1 7 3 11 4v3s0 1-1 1z"></path><path d="M222 133l13-8c12-6 27-8 40-3 3 1 12 4 13 8h-1c-5 0-10-2-14-4l-9-2c-5 1-10 1-14 2-7 2-15 6-21 11h-1 0l-4 3-3 2c-2-2-4-2-6-2 0-2 0-2 1-3h0c2-1 4-3 6-4z"></path></g><path d="M258 124c4-1 11-2 14 0 1 1 1 2 1 2l-9-2h-6z" class="W"></path><path d="M258 124h6c-5 1-10 1-14 2-7 2-15 6-21 11h-1 0l-4 3-3 2c-2-2-4-2-6-2 0-2 0-2 1-3h0c2-1 4-3 6-4h1c1 0 1-1 2-1 3 0 6-2 9-3 2-1 5-2 8-3 5-2 11-2 16-2z" class="M"></path><path d="M216 137c4 1 7-2 11-1l1 1-4 3c-1-1-1 0-1-1v-1c-2-1-5 1-7-1h0z" class="H"></path><path d="M215 140c0-2 0-2 1-3 2 2 5 0 7 1v1c0 1 0 0 1 1l-3 2c-2-2-4-2-6-2z" class="C"></path><path d="M409 591l36-31v1c1 9 5 19 7 29 4 21 7 43 8 64-2 1-2 0-3 0-1-2-1-4-1-5 0-2 0-5-1-6l-1-13c-1-10-2-21-4-31-2-7-3-15-5-22l-1-14-4 5c-4 4-26 24-30 24-1 0-1-1-1-1z" class="W"></path><defs><linearGradient id="Eg" x1="651.863" y1="450.423" x2="612.869" y2="456.957" xlink:href="#B"><stop offset="0" stop-color="#3d3e3f"></stop><stop offset="1" stop-color="#6b6868"></stop></linearGradient></defs><path fill="url(#Eg)" d="M620 499l10-45 5-22 4-20c1-3 2-6 4-9h2c1 4-9 48-10 56l-5 25c-1 5-2 10-4 14v-1 2 1c-1 1-1 3-1 4-2 0-3-1-4-1 0-1-1-2 0-3v-1h-1z"></path><defs><linearGradient id="Eh" x1="381.538" y1="298.156" x2="375.962" y2="300.344" xlink:href="#B"><stop offset="0" stop-color="#212121"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#Eh)" d="M367 267v-3-2c-1-2-2-4-2-6l1 2v1c1 1 0-1 1 1l1 2h0c1 1 2 3 2 5l5 13 7 21 8 20c2 3 3 6 4 9 2 8 3 17 2 24 1 4 1 7 2 10v2l-2-3v1c-1-3-1-4-3-6-1-3-2-7-3-10l-4-16-2-7-3-12-1-3v-1c-2-2-2-5-3-8l-6-23c0-2-1-4-2-5l-2-6z"></path><path d="M380 309h1l1 3s-1 0-1 1l-1-3v-1z" class="C"></path><path d="M377 301c1 1 1 2 2 2l2 6h-1c-2-2-2-5-3-8z" class="T"></path><path d="M394 348v-5-1-2c-1-1-1-1-1-2v-1c-1-1-1-3-1-4-1-1-1-1-1-3 0-1-1-3 0-4v3l1-3v1c0 2 1 2 2 3 2 8 3 17 2 24v-1c-1-2-1-3-2-5z" class="B"></path><path d="M381 313c0-1 1-1 1-1 2 5 4 11 5 16l7 20c1 2 1 3 2 5v1c1 4 1 7 2 10v2l-2-3v1c-1-3-1-4-3-6-1-3-2-7-3-10l-4-16-2-7-3-12z" class="M"></path><path d="M381 313c0-1 1-1 1-1 2 5 4 11 5 16-2-3-3-7-4-10 0 1 1 2 1 3v3 1l-3-12z" class="H"></path><defs><linearGradient id="Ei" x1="277.798" y1="177.638" x2="241.878" y2="181.958" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#Ei)" d="M294 165c3 0 6 1 10 1l-1 1h-1c-1 0-3 1-4 2-5 1-10 0-15 1h-3-1c-3 7-6 13-10 19-1 2-2 4-3 5v-10c-3 0-10-1-12 0h0l2-1h1c0-1 1-1 2-2h-1l3-2h1c-3 0-6 0-9-1h-6-2l-1-4 1-1h15 8c-2-1-5-1-7-1h-16c-2 0-3 0-5-1-1 0-1 0-1-1l1-1c-1 0-2 0-3-1h3v-1l25 1c6 0 13 0 19-1 3 0 7 0 10-2z"></path><path d="M262 179h5c0 1 0 3-1 4h-1c1 0 1-1 1-2-2 0-3-1-5-1v-1h1z" class="Y"></path><path d="M261 179v1c2 0 3 1 5 1 0 1 0 2-1 2h-9 1c0-1 1-1 2-2h-1l3-2z" class="Z"></path><path d="M240 167l25 1h-11c6 1 12 1 18 1 2 0 6-1 8 1h-1-29c-3 0-6-1-10-1-1 0-2 0-3-1h3v-1z" class="D"></path><path d="M294 165c3 0 6 1 10 1l-1 1h-1c-1 0-3 1-4 2-5 1-10 0-15 1h-3c-2-2-6-1-8-1-6 0-12 0-18-1h11c6 0 13 0 19-1 3 0 7 0 10-2z" class="H"></path><path d="M245 178l-1-4 1-1h15l8 1v1h-5c2 0 3 0 5 1v2h-15-6-2z" class="J"></path><path d="M245 173h15l8 1v1h-5-12c-2 0-5 0-6-2z" class="D"></path><path d="M540 756l-33-39h72l2 1s0 1-1 1c-3 2-8 2-11 1l-39 2c-4 0-9 0-14-1v1c3 3 6 6 9 10l10 12 6 8c1 1 2 3 3 3 2 1 3 3 5 5v-1h1c1 1 2 2 2 5h0l-1 1c2 2 6 7 5 10l-14-16c0-1-1-2-2-3z" class="E"></path><path d="M544 755c2 1 3 3 5 5v-1h1c1 1 2 2 2 5h0l-1 1c0-1-1-2-2-3l-5-7z" class="C"></path><path d="M472 364c2-2 4-5 6-7 3-2 6-4 8-7 1 0 4-4 5-5 0 0-1 0-1-1-1-1-3-2-4-4v-2c0-3 1-6 2-9 2-3 3-6 5-8 4-6 10-9 16-14 9-8 17-20 18-33 0-6-2-13-6-18-2-3-4-5-7-8-4-5-8-11-7-18 1 6 2 11 7 15 2 4 6 7 9 11 11 16 4 36-9 49-3 3-6 6-9 8-3 3-7 6-9 11-1 3-2 8-2 11l1 2c2 4 5 7 4 11-2 6-11 11-15 14h-1s-1 0-1 1l-2 1h0-1l3-3v-1c6-5 11-8 13-15-1-2-4-6-4-9-1 1-1 1-1 2 1 3 3 5 5 8-5 9-16 13-22 22 0 0-1 0-1-1 1-1 0-2 0-3zm360-25c1 1 3 2 5 3 3-2 10-5 13-8 3-4 3-9 8-13 1-1 3-2 5-2 4-1 9-1 14-1 8-1 18-5 27-3 2 1 3 2 5 3h-1 0c-9-5-19 0-27 2l-12 3c-3 1-5 2-7 5-2 4-2 8-4 11-2 5-7 7-11 9l-10 7-11-8c-1-2-2-2-4-3l-1-1 1-2 2 1c1 1 1 1 3 1h1 0c3 1 6 3 9 6v1c2-1 4-3 5-4 5-2 11-5 14-10 0-2 3-7 2-8-1 2-2 6-3 8-4 5-13 8-18 11-3-1-6-3-8-5v-1l-4-2c-1 0-2-1-3-1l2-2c1 1 3 1 4 2l3 1h1z" class="E"></path><defs><linearGradient id="Ej" x1="699.496" y1="140.47" x2="718.588" y2="155.13" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#Ej)" d="M705 133h1v-1c2 3 1 4 1 6l2 2v-2c0-2 0-3 1-4v3 2l1 1c2-2 1-3 3-5v-1l1-1 10 20c-1 1-1 2-1 4l-5 18-1 7v1h-1c-1-1-1-1-1-2-3-5-7-10-8-15v-3c0-3 2-5 4-7l-7-1v-16-6z"></path><path d="M710 134v3 2 1 2c0 1 0 1-1 1v-3-2c0-2 0-3 1-4z" class="K"></path><path d="M710 139l1 1c2-2 1-3 3-5 0 1 1 3 1 4-1 1-1 1-1 2h0l-1-1-2 2-1-2v-1z" class="F"></path><path d="M708 166c0-1 1-1 1-2l1-1h0l1-1h0c1-1 2-1 2-2h0c1-1 2-1 3-1l-1 1v5l1-2h1 0c1 2 0 4 2 6l-3 12c-3-5-7-10-8-15z" class="D"></path><path d="M716 163v7h-1v-5l1-2z" class="Z"></path><path d="M714 134l1-1 10 20c-1 1-1 2-1 4l-5 18-1 7v1h-1c-1-1-1-1-1-2l3-12c-2-2-1-4-2-6h0c1-7-1-17-2-24 0-1-1-3-1-4v-1z"></path><defs><linearGradient id="Ek" x1="722.937" y1="154.668" x2="714.063" y2="160.332" xlink:href="#B"><stop offset="0" stop-color="#696a69"></stop><stop offset="1" stop-color="#8d8b8d"></stop></linearGradient></defs><path fill="url(#Ek)" d="M717 163c0-2 1-4 1-6l1-14c0 3 2 6 2 9 0 5-1 11-2 17-2-2-1-4-2-6z"></path><defs><linearGradient id="El" x1="571.621" y1="395.876" x2="532.195" y2="387.228" xlink:href="#B"><stop offset="0" stop-color="#d7d5d7"></stop><stop offset="1" stop-color="#fffeff"></stop></linearGradient></defs><path fill="url(#El)" d="M543 352l1 2c-3 15-10 28-1 42 4 6 10 10 15 15 3 4 6 8 10 11-1 1-2 1-3 2h0 0c-1 1-5 1-6 1-1 1-2 4-3 5 1-2 1-6 0-9l-1 1h0v2c0 1 0 1 1 2-1 1 0 2-1 3h-3v-1c1-2 1-5 0-7-1 0-1 0-1-1h0l-6-9c-3-3-7-6-9-10-5-8-4-19-1-28 2-7 6-13 7-20l1-1z"></path><path d="M556 421h0c-2-9-9-12-14-18-3-4-5-7-6-11v-3c1 3 2 6 4 9 3 5 8 9 12 14 3 2 5 6 8 9 1 2 3 2 5 3h0c-1 1-5 1-6 1-1 1-2 4-3 5 1-2 1-6 0-9z"></path><defs><linearGradient id="Em" x1="291.88" y1="592.156" x2="275.68" y2="576.714" xlink:href="#B"><stop offset="0" stop-color="#8e8d8e"></stop><stop offset="1" stop-color="#b1afaf"></stop></linearGradient></defs><path fill="url(#Em)" d="M304 565h1c1 0 4-3 5-3l1-1 1 3-2 1c-1 1-2 1-3 2v1h0c-3 3-22 13-25 13h-1-4-1c1 2 1 3 3 5 1 0 4-3 5-4 5-3 10-5 14-7 2-1 6-4 8-5h0c2 0 4-1 5-2l2 2c0 1-1 1-2 1v1c-5 5-12 8-19 11-3 2-7 4-10 6-1 2 1 4 0 5h-1-1c-6-2-13 3-18 6-3 3-7 5-12 6-5 2-10 3-16 2 8 0 19-3 25-10 2-2 4-6 6-8 2-3 5-5 8-7v-5c11-2 21-7 31-13z"></path><path d="M581 718h2 0v1c-1 1-1 1-1 3-2 1-5-1-6 1h4c-7 2-15 2-23 2-3 1-7 1-9 1l-10 1c0 1 1 3 2 4l3 10c3 6 4 12 6 18v1c-2-2-3-4-5-5-1 0-2-2-3-3l-6-8-10-12c-3-4-6-7-9-10v-1c5 1 10 1 14 1l39-2c3 1 8 1 11-1 1 0 1-1 1-1z" class="M"></path><path d="M535 735c1 2 5 5 5 7-1 1 0 1-1 0-2 0-2-1-3-2l-3-4 2-1z" class="N"></path><path d="M533 736c-3-5-7-9-10-12v-1c2 0 3 2 4 4l1-1-2-3 1-1 2 2c0 2 1 3 2 5h0c1 1 0 1 1 2s2 3 3 4l-2 1z" class="Z"></path><path d="M581 718h2 0v1c-1 1-1 1-1 3-2 1-5-1-6 1-3 0-7 0-9-1-1 0-1 0-1-1l3-1c3 1 8 1 11-1 1 0 1-1 1-1z" class="H"></path><path d="M529 724v-1h18v1c-2 1-9-1-10 1 3 0 8-1 11 1l-10 1c0 1 1 3 2 4l3 10h-1s-1-1-1-2c-2-1-4-3-6-5-1-1-1-2-3-3-1-1 0-1-1-2h0c-1-2-2-3-2-5z" class="N"></path><path d="M702 129h1v-1-1c1-2 0-4 1-5 1 1 0 3 0 4v14l1-1v16l7 1c-2 2-4 4-4 7v3c1 5 5 10 8 15 0 1 0 1 1 2h1v-1c1 4 5 7 7 10 1 1 2 3 3 5l-2 1-18-23c0 3 0 5 1 7v5c0 2-1 3-1 4 1 2 0 5 1 7v3l1 3v6c-1 1-1 5-1 5 0 4-1 8-2 12 0 2-1 4-1 6h1c-1 4-3 8-4 13-1 1-3 2-4 4-2 1-3 2-5 2l-1 1c3-7 6-15 8-22 7-27 7-56-1-83 2-2 2-3 2-5v-14z" class="I"></path><path d="M708 207c0-1 0-3 1-5v-1h0l1 3v6c-1-2-1-2-2-3z" class="J"></path><path d="M708 207c1 1 1 1 2 3-1 1-1 5-1 5-2 0-1 0-2 1l1-9z" class="T"></path><path d="M707 216c1-1 0-1 2-1 0 4-1 8-2 12 0 2-1 4-1 6h1c-1 4-3 8-4 13-1 1-3 2-4 4-1-5 2-10 3-14 2-6 4-13 5-20z" class="O"></path><path d="M472 558c1 1 2 3 3 4 5 15 10 32 12 48l3 48-1 6h0v5h-1c0-2 1-4 0-6l-1-1v7 1c0 1 0 1-1 2v-1c-1-4-1-7-1-11v-16c0-8-2-16-3-25-2-18-7-37-12-55h1v-1-4c0 1 1 3 2 5l-1-6z" class="K"></path><path d="M485 644c0 5 0 9 1 13v-1-1-3l1-1c0 3-1 8 0 11v7 1c0 1 0 1-1 2v-1c-1-4-1-7-1-11v-16z" class="B"></path><path d="M486 671c0-2 0-4 1-6v4 1c0 1 0 1-1 2v-1z" class="I"></path><defs><linearGradient id="En" x1="484.827" y1="570.523" x2="471.703" y2="585.443" xlink:href="#B"><stop offset="0" stop-color="#1a1b1c"></stop><stop offset="1" stop-color="#41403f"></stop></linearGradient></defs><path fill="url(#En)" d="M472 558c1 1 2 3 3 4 5 15 10 32 12 48 0-1-1-2-1-2v-1c-1-1-1-2-1-3 0-2 0-3-1-5v-2c0-1-1-1-1-2-1 2 1 6 1 8 0 1 1 1 0 3l-4-15v-2l-3-6c-2-3-2-7-3-10l-2-6-1-3v-1-4c0 1 1 3 2 5l-1-6z"></path><path d="M472 567l3 1 2 8c-1-1-2-2-2-3v-1l-1 1-2-6z" class="T"></path><path d="M471 559c0 1 1 3 2 5l2 4-3-1-1-3v-1-4z" class="G"></path><path d="M474 573l1-1v1c0 1 1 2 2 3 1 5 3 10 3 15v-2l-3-6c-2-3-2-7-3-10z" class="H"></path><path d="M416 402c1 4-1 7-2 10l-3 6h1c0-1 1-2 2-3v1l-1 3-1 3v3l2 1-1 1s-1 1-1 2c-2 4-4 9-7 12l-6 10-3 4-3 7-2 3-2 4-1-1h-1l-2-2c-2-1-4 0-6 0 0-2 0-3 1-5h1c1-4 4-7 6-11l13-21 16-27z" class="W"></path><path d="M411 418h1c0-1 1-2 2-3v1l-1 3-1 3c-4 5-6 10-9 15-1 2-3 5-4 8h0-2l-1-1c0-1 1-1 1-2v-1c0-2 3-5 4-7l10-16z" class="N"></path><path d="M399 445c1-3 3-6 4-8 3-5 5-10 9-15v3l2 1-1 1s-1 1-1 2c-2 4-4 9-7 12l-6 10v-4l1-2h-1z" class="B"></path><defs><linearGradient id="Eo" x1="384.633" y1="457.96" x2="392.358" y2="455.756" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#Eo)" d="M397 441v1c0 1-1 1-1 2l1 1h2 0 1l-1 2v4l-3 4-3 7-2 3-2 4-1-1h-1l-2-2c-2-1-4 0-6 0 0-2 0-3 1-5h1 1c4-1 9-12 12-16 0-1 2-3 3-4z"></path><path d="M395 453l1 2-3 7-2 3-2 4-1-1 2-4c1-4 3-8 5-11z" class="I"></path><path d="M379 466c0-2 0-3 1-5l1 3c2 1 3-1 5-1s1 2 4 1l-2 4h-1l-2-2c-2-1-4 0-6 0z" class="B"></path><path d="M397 441v1c0 1-1 1-1 2l1 1h2 0 1l-1 2v4l-3 4-1-2v-1c-1 0-1 0-1 1l-1 1c0-3 1-6 1-9 0-1 2-3 3-4z" class="D"></path><path d="M399 445h1l-1 2v4l-3 4-1-2v-1c1-2 3-5 4-7h0z" class="R"></path><path d="M561 86h70l-13 9h0l11-5h0l-14 17-54-21zm213 385c7 3 13 6 16 13 1 3 1 5 2 8 2-2 6-6 8-7s4-1 6 0c9 1 16 6 25 8 4 1 9 1 13 0 8 0 18-3 24 4 1 2 1 3 1 5h0c-1-3-3-6-6-7-9-4-22 3-32 2-6 0-12-1-19-2-2-1-5-2-7-2-4 0-10 3-13 4l-6 3c-1-1-3-3-4-6 0-2 0-5-1-7-2-3-8-5-10-6-1-1-2-1-3-2-1 0 0 0-1-1l1-2 1 1h3 1c2 0 6 2 7 3 6 3 6 8 8 14v-1c0-2 0-4-1-6-3-8-12-10-19-13l1-1c2-1 3-1 5-2z" class="E"></path><path d="M440 568l4-5 1 14c0-1-1-3-1-4-1 1-1 1-2 1 2 3 1 5 2 8v1c1 3 2 7 2 11-1-1-1-2-2-3s-1-3-1-4c-1-3-1-6-3-9l-1 1h0c0 1 0 2-1 3v1l-1 1h2l1 1 1 4v1 1c-1 1-3 2-4 3l-6 3c-2 2-6 4-9 4-2 1-4 1-5 1h-1c-2 1-8 2-10 0-2-1-2-2-2-4v-3c2-1 3-2 5-4 0 0 0 1 1 1 4 0 26-20 30-24z" class="D"></path><path d="M404 595l1 1h1c1-1 3-1 4-2l1 1h0c-1 1-2 1-2 2-2 1-2 1-4 1h-1v-3z" class="X"></path><path d="M440 568l4-5 1 14c0-1-1-3-1-4-1 1-1 1-2 1 2 3 1 5 2 8v1c1 3 2 7 2 11-1-1-1-2-2-3s-1-3-1-4c-1-3-1-6-3-9l1-1c0-1 0-1-1-2-1 1-1 2-3 3-2 0-6 4-8 4v-1l11-10h-1l-1 1h0c1-1 2-2 2-4h0z" class="M"></path><path d="M439 579h0c0 1 0 2-1 3v1l-1 1h2l1 1c-2 0-4 1-5 2-5 2-10 4-14 7-2 1-5 1-7 3l1 4 2 1h-1c-2 1-8 2-10 0-2-1-2-2-2-4h1c2 0 2 0 4-1 3-1 6-3 8-4 6-3 11-6 16-10l5-3 1-1z" class="I"></path><path d="M433 583l5-3c0 1-1 3-2 4-1 0-2 0-3-1z" class="a"></path><path d="M435 587c1-1 3-2 5-2l1 4v1 1c-1 1-3 2-4 3l-6 3c-2 2-6 4-9 4-2 1-4 1-5 1l-2-1-1-4c2-2 5-2 7-3 4-3 9-5 14-7z" class="D"></path><path d="M435 587c1-1 3-2 5-2l1 4c-2-1-4-1-6-2z" class="M"></path><path d="M431 597v-1l1-1s1-1 2-1l2-2 1-1c1-1 2-1 4-1v1c-1 1-3 2-4 3l-6 3z" class="N"></path><path d="M754 498c1-1 1-3 3-4 3 2 7 5 9 8s4 6 6 8c10 9 22 12 31 22 1 1 2 2 3 2l-1 1c1 1 2 1 4 1-2 1-5 0-7 0h-3c-2 0-3-1-5 0-4 0-11-3-14-5-3 2-5 5-7 7 0-3-1-7-3-10-1-2-3-6-5-7-1-2-4-3-6-3 0-2 1-5 1-6-1-4-6-7-8-9l-1-1-3-3 1-1c1 0 2 0 3 1 0 1 1 1 2 1 5 2 8 4 10 10 0 2 0 4 1 6l7 2 3 12 2-8h1l4 2 1-1-11-7c-1-1-4-2-5-3-2-1-3-7-5-9s-5-4-8-6z" class="W"></path><path d="M783 523c6 4 11 6 16 10-6-1-12-3-17-7v-2l1-1z" class="J"></path><path d="M218 342c-4 3-8 6-12 7-12 5-23-18-33-23-2-2-6-3-9-3-5 0-10 1-16 0-3-1-6-4-9-6-4-2-8-4-12-4-8-1-13 2-18 6h-1c3-3 6-5 10-7 6-2 14-1 20 3 5 2 9 5 15 5 5 1 9 0 13-1 4 0 8 1 12 2 5 2 10 6 15 9 4 2 9 5 13 7l6-3h1c2 0 4-2 6-4 0 1 1 1 1 2l-2 2h-1c-1 1-2 2-2 3l-2 2h1l2-1h2c1 0 2-2 3-2l1 1v1l-2 1h0c-1 1-1 2-2 3h0z" class="E"></path><path d="M214 339c-3 3-7 5-11 6-4-2-7-5-10-7l-13-11v-1c4 3 7 6 11 8l15 9c3-1 5-3 7-4h1z" class="J"></path><defs><linearGradient id="Ep" x1="812.438" y1="370.096" x2="837.182" y2="408.666" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ep)" d="M798 379h0c2 1 5 4 6 6v4c-1 5-2 12 1 17 2 1 4 0 6 0 5-1 11-2 15-4 3-1 5-4 8-6 2-1 6-2 8-3 5-2 10-4 15-4v1c3-1 6-3 9-3l1 1c-8 3-15 5-22 8-2 1-5 2-6 4l2 9c-12 1-25 8-37 7-2 0-5 0-7-2-2-1-3-3-4-6l1-14c0-2 0-2-1-3 0-1 1-2 2-3l1 1h1c2 6-1 14 2 20 1 1 3 2 5 3h0 1c5 1 25-4 29-7 1-1 1-2 1-3l-1-1c0 1 0 2-1 2-1 2-3 2-4 3-5 2-11 4-17 5h-2c-3 0-6 1-9-2-4-3-2-13-2-18 0-3-1-4-4-7l3-5z"></path><path d="M429 493c3 5 5 10 8 15l13 28c1 7 4 13 6 20l11 41c0 1 1 2 1 4l1 2v1 3l1 1v2c0 1 0 2 1 4v2c0 1 0 2 1 3v3c0 1 0 1 1 3 0 1-1 2 0 3 0 1 0 1 1 3v2h0c0 2 0 3 1 5 0 1 0 3 1 4h0c1 3 1 6 1 9h0v16c-1-1-7-5-7-6-1-6-1-14-1-20l-1-18c-2-13-5-25-8-38-4-17-9-34-15-51l-8-17v-1l-1-2-1-1c-1-3-3-7-4-10h0v-1c0-1 0-1-1-2 0-1 0-2-1-3v-4z" class="B"></path><path d="M467 597c0 1 1 2 1 4l1 2v1 3l1 1v2c0 1 0 2 1 4v2c0 1 0 2 1 3v3c0 1 0 1 1 3 0 1-1 2 0 3 0 1 0 1 1 3v2h0c0 2 0 3 1 5 0 1 0 3 1 4h0c1 3 1 6 1 9l-1 11c-2-8-2-17-4-26l-5-39z" class="L"></path><path d="M326 244c6 0 12 0 17 4l2 2c1 3 1 6 0 9-1 5-3 9-5 13-6 12-3 22 6 32l2 2c4 4 9 7 13 12 2 2 4 4 6 7l1-1 2 3h1c0 1-1 3-1 4v1l2 11c1 20-5 38-19 53h0c-1 0-1 0-2 1v-2h0l1-1c10-11 15-27 16-42 1-14-4-26-15-36-4-5-9-8-13-13-3-3-6-8-7-13-2-9 2-17 5-26 2-2 3-5 3-9 0-1-1-3-2-4-2-3-8-3-12-3-8 0-16 5-22 11 0 0-2 1-2 2l-3 3h0l3-6c1-1 2-2 4-3 5-5 11-9 18-11h1z" class="E"></path><path d="M368 324l2 3h1c0 1-1 3-1 4v1l-3-7 1-1z" class="J"></path><path d="M641 767c4 2 9 5 10 11 3 11-7 21-4 32 1 4 4 7 6 10s2 7 4 10 4 5 6 7c6 6 8 13 7 22h0c0-8-3-14-9-20-8-8-26-11-26-26 0-5 1-11 3-16 1-5 4-11 3-16 0-2-1-3-2-4v-1l-7-3 1-2-1-1c1-1 1 0 1-1v-1c1 1 1 1 2 1 2 1 4 2 6 4l4 2c-1-2-4-3-6-4v-1-1l2-2z" class="W"></path><path d="M632 773l1-2-1-1c1-1 1 0 1-1v-1c1 1 1 1 2 1 2 1 4 2 6 4-1 0-2 0-3-1l-2-1h-1v3h1c1 0 2 1 3 2l-7-3z" class="U"></path><path d="M645 775v1c6 6-3 21-4 29 0 4 0 7 3 11 2 3 7 6 7 10-2-1-5-3-7-5-7-7-4-19-1-27 1-3 3-6 3-10v-1c0-3 0-5-1-8z"></path><path d="M361 707c-1-1-1-2-1-3v-4h3v2c0 2 0 3 1 4v1l2 2c1 3 3 7 4 11 0 4-1 7-1 11v3c0 1 4 2 4 3 0 0 0 2-1 3-1 4-2 7-3 12-4-1-7-2-11 1-3 2-6 6-8 9-8 8-16 20-18 32 0 5 0 9 1 13h-1c-4-11 0-23 5-34 4-8 11-14 11-23v-6c-1 0-1-2-1-2l2-2c8-8 13-15 12-26 0-1-1-6-1-7h1z" class="W"></path><path d="M360 707h1c0 1 0 3 1 4s1 3 2 5h-1c0-1-1-1-1-2h-1c0-1-1-6-1-7z" class="U"></path><path d="M365 719v1c1 1 1 4 0 6l-3 12c2 1 3 1 5 2v2c0 1-1 3-1 3l-4 1c-3 0-4 1-6 3h0c-1 1-2 2-3 2v-7c2-2 4-3 6-5 4-5 6-13 6-20z" class="H"></path><defs><linearGradient id="Eq" x1="689.108" y1="710.74" x2="705.001" y2="692.237" xlink:href="#B"><stop offset="0" stop-color="#686868"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#Eq)" d="M677 678v4h1-2c1 2 2 3 3 5 2 3 3 7 6 10l5-18 2 2 2-1 12 18c1 2 1 5 3 7 1 3 7 4 9 5l-3 27c-1-4-2-8-4-11-2-4-6-8-9-11-5-5-9-6-16-5l-16-29v-1l2-1 5-1z"></path><path d="M677 678v4-1c-2 0-4 0-5-2l5-1z" class="Z"></path><path d="M693 688c2 4 6 7 8 11 1 3 2 6 3 8s3 4 5 6c1 1 3 2 4 3v2l-1 1-1-1-9-9c-1-1-3-4-5-4-2-1-6-1-8-1l4-16z"></path><path d="M870 86h55c-2 2-5 4-7 5l-15 11-12 9c-1 1-4 3-6 4-3-4-6-9-10-13-4-6-9-11-15-16h10z" class="E"></path><path d="M884 96c3 0 5 0 8 1h1l-1 2h-5c-2 0-2 0-3-2v-1z" class="U"></path><path d="M785 422l12 7c1 1 4 2 5 3 5 5 5 17 7 24 0 1 1 3 2 4s2 1 3 1c8 0 17-4 25-7 1 3 2 7 4 8 3 2 7 1 10 0 7-2 16-8 24-4 2 1 4 3 5 5h0c-3-3-5-5-10-5-8 1-17 7-23 10s-11 5-16 8c0-2-1-5-2-8-8 1-20 6-27 0-4-5-7-15-7-21h1c-1-3-2-7-5-8l-1-1h0-1c1-1 1-2 2-2s2 1 3 2h1c1 1 1 1 2 3v1c1 0 1 1 1 1v1h1l1 4h0c0-4-1-9-4-12l-16-11v-1h0c1-1 2-2 3-2z" class="E"></path><defs><linearGradient id="Er" x1="782.683" y1="422.531" x2="791.757" y2="428.927" xlink:href="#B"><stop offset="0" stop-color="#a3a3a3"></stop><stop offset="1" stop-color="#cdcdcd"></stop></linearGradient></defs><path fill="url(#Er)" d="M785 422l12 7c1 1 4 2 5 3v1c-3-2-6-3-8-5h-1l-1 2c0 1 0 1 1 1l1 1h1l2 2h0c1 0 1 0 1 1v1l-16-11v-1h0c1-1 2-2 3-2z"></path><path d="M802 448h0c1 4 2 9 3 13 1 2 3 3 5 4s4 0 6 0c6 0 15-2 21-5h0l1 4c1 3 4 2 6 3l-8 2c0-1 0-3-1-4 0 0 0-1-1-1-1-1-4 0-5 0-6 1-16 4-22 1-4-3-5-12-5-17z" class="R"></path><path d="M642 312c7 3 12 7 17 11 4 3 24 25 24 28 0 2-2 7-3 8l-3-1-43-26 8-20zM278 543h2c1 0 2-2 3-2s3 1 4 1c-1 1-2 2-3 2v1h0l-5 3 1 1c4-1 8-3 12-2 1 0 3 1 3 2s1 2 1 3c-9-1-15 3-22 7l-14 8c-1-3-1-6-3-8-1-3-4-4-7-5-6-2-15-1-20 2-3 1-5 3-7 5-3 2-6 3-8 5-10 6-20 12-32 9 4 0 8 0 11-1 10-2 17-8 22-17 3-4 6-8 11-10 3-1 7-2 10-2 5-1 12-2 17-1 4 1 8 3 13 3 3 0 7-2 11-4z" class="E"></path><path d="M279 548l1 1-10 6c-1-1-3-1-4-1v-1c1 0 3-1 4-1l9-4z" class="G"></path><path d="M267 557c-1 1-2 3-4 3v-2c0-2-1-4-3-5-6-4-17-5-24-4l-3 1c-7 1-10 5-14 10v-1c2-3 4-5 7-7 7-5 20-5 28-3 4 1 8 3 12 4h0v1c1 0 3 0 4 1l-3 2z" class="B"></path><path d="M266 554c1 0 3 0 4 1l-3 2c-1-1-2-1-2-2l1-1z" class="T"></path><path d="M274 500l-1 1-19 12c-12 7-20 4-29-6-2-1-4-4-7-4-1-1-3 0-5 1-1 0-1 2-1 3-1 1-1 1-1 2-4 1-7 2-11 4-10 5-17 11-29 12-6 1-12 0-16-4l5 2c5 2 11 1 16 0 6-2 12-6 16-11 6-6 9-15 18-19 8-4 16 1 22 6 2 2 5 6 8 7 6 2 14-5 19-8l11-6h1l1 4-13 8c-7 4-13 8-21 6-10-3-16-19-28-13-3 2-5 7-9 10v1c1-1 4-2 6-3 1-1 1-3 3-4 2-3 6-4 10-3 10 4 14 18 28 12 7-2 13-7 19-10 1-1 3-3 5-3h0c2 0 3-1 5-2h1v2c-1 0-1 0-2 1h0l-1 1-1 1zm304-27l21 62-6 11-3-5c-6-12-16-22-26-32l14-36z" class="E"></path><defs><linearGradient id="Es" x1="748.063" y1="231.212" x2="764.795" y2="210.464" xlink:href="#B"><stop offset="0" stop-color="#898787"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#Es)" d="M753 194c0 1 1 2 2 2l2 2c1 3 5 4 7 8h0c1 0 1-1 2-2l4 4c1 4-1 11-1 15-1 8-1 17-4 24l-1 1c0 2-2 4-4 6-1 0-2 0-3 1-1 0-2 1-3 1l-1-1h-4-3v-1l-1-1 1-1-1-11-1-6c0-3 2-8 4-11l1-5c2-6 2-12 3-18l1-7z"></path><defs><linearGradient id="Et" x1="749.166" y1="249.754" x2="750.461" y2="236.163" xlink:href="#B"><stop offset="0" stop-color="#5b5a5b"></stop><stop offset="1" stop-color="#777574"></stop></linearGradient></defs><path fill="url(#Et)" d="M745 241c1 1 2 2 4 2 2-3 3-6 5-9v2h1v-1l1-1v1c0 1-1 2-1 4-2 2-5 7-4 10l-5 3-1-11z"></path><path d="M761 243c2-4 2-9 2-14l2-23 3 4h0l2-2c1 4-1 11-1 15-1 8-1 17-4 24l-1 1h0c-1 0-1 0-1 1h-1v-3h1c-1-1-1-2-2-3z" class="L"></path><path d="M756 246h0c0-2 1-3 2-5h0v-6h1v-7l2-10c1-3 1-6 1-9l1 1c0 9-1 17-2 26 0 3-1 6-1 9h1v-2c1 1 1 2 2 3h-1v3h1c0-1 0-1 1-1h0c0 2-2 4-4 6-1 0-2 0-3 1-1 0-2 1-3 1l-1-1h-4-3v-1l-1-1 1-1 5-3c2-1 3-1 5-3z" class="Q"></path><path d="M756 246l2 1v3c-2 2-4 4-7 5h-2-3v-1l-1-1 1-1 5-3c2-1 3-1 5-3zm-4-45v-1h1c1 1 2 1 2 1 4 6 1 16 0 23l-2 9-1 2c-1 2-1 2-2 3-2 1-3 1-4 0s-2-2-2-3c0-3 2-8 4-11l1-5c2-6 2-12 3-18z" class="E"></path><path d="M748 224l1-5c2 4 0 10 2 13 1 0 1 0 2 1l-1 2c-1 0-2 0-3-1 0-2-1-6 0-8h0l-1-2z" class="V"></path><path d="M748 224l1 2h0c-1 2 0 6 0 8 1 1 2 1 3 1-1 2-1 2-2 3-2 1-3 1-4 0s-2-2-2-3c0-3 2-8 4-11z" class="L"></path><path d="M705 616c2-2-1-6-1-8l2-2c1 2 2 6 3 7l1 1c1 3 3 8 3 12 1 3 0 7 0 10-1 7 0 15 2 23 2 4 4 8 7 11 3 4 7 7 11 10 9 8 16 19 12 31v-2c1-7-1-14-6-19s-10-8-16-10c-3-1-6-1-8-3-3-3-5-8-6-11l-24-44c3 0 11 1 14 0v-1-2c0-1-1-2-1-3l1-1h-1v-1c0-2 1-2 2-3 1 2 2 4 2 5s0 2 1 2l1 1c1 2 1 4 1 5 1 0 1-1 1-2l1 1h0c0-3-1-5-2-7z" class="W"></path><path d="M707 623c2 4 1 9 1 14 0 11 2 27 10 35 2 2 5 4 7 5-1 1-5-1-6-3-5-3-7-10-10-15-5-10-10-22-16-32h1 11c1-1 0-1 0-3 1 0 1-1 1-2l1 1h0z" class="J"></path><path d="M725 559c4-2 9-1 13 0s9 4 11 9c5 11-1 21-2 31 0 3 0 5 1 7 0 2 2 4 2 7 0 4-2 8-2 12-1 5 0 10 1 14 1 10 4 20 8 29h-1c-3-7-6-14-8-21-2-6-4-12-7-17-1-2-3-5-5-7-2-3-9-5-7-9 1-4 5-7 7-11 1-3 0-7 1-10 1-6 6-15 3-21-1-2-3-3-5-3-2-1-4-1-6 0h-2-2l-1 1c-1 0-2 1-3 1v1c0-1-1-2-1-2l-1-1c1-1 3-1 4-2v-1h1c5-3 11-3 16-1v-1c-4-1-8-2-12-1h-2 0-1l-1 1h-1c-2 1-1 0-2 1h0-1l-1 1v-3c1-1 5-2 6-3v-1z" class="E"></path><path d="M741 619c1 0 1 1 2 1v7l-3-7 1-1z" class="B"></path><path d="M740 620c-2-2-6-3-7-5 1-3 5-6 7-8h0c1 2 0 5 0 7v4s1 0 1 1l-1 1z" class="H"></path><path d="M740 564c3 2 5 4 6 7 2 9-3 19-4 28 0 7 1 14 1 21-1 0-1-1-2-1 0-1-1-1-1-1v-4c0-2 1-5 0-7 1-5 1-10 2-14 1-7 4-14 3-21-1-3-3-5-5-7v-1z" class="J"></path><path d="M342 618c2 0 5 1 7 3l1-2v1c-2 6-8 13-12 18 0 1 0 1 1 1 3 0 7 2 11 3 4 0 7-1 11-2h0 2c2 1 2 3 2 5l-4 2c-3 1-6 2-9 1-5 0-10-2-15-3-1 2-2 4-2 5 0 2-1 3-1 4-2 10-7 20-8 30l6 6c-3 5-5 9-6 15v1l-1-1v1 4s-1 1 0 2v7h0c-1-2-1-4-2-6l-2-6h0c-3-6-5-16-5-22 0-5 2-10 3-15 2-4 2-8 4-12h0c0-3 1-6 2-9 1-5 2-9 4-14 4-6 9-12 13-17z" class="W"></path><path d="M349 621l1-2v1c-2 6-8 13-12 18 0 1 0 1 1 1 3 0 7 2 11 3l-1 1c-4-2-8-3-13-4-5 6-7 14-9 22-1 3-1 5-2 8-1 5-3 10-3 15 0 4 1 5 4 7v1c-1 3-2 6-2 10-1-5-3-11-3-16s1-9 2-13l4-17c1-1 1-3 1-4 1-4 2-7 4-11 4-7 11-14 17-20z"></path><defs><linearGradient id="Eu" x1="345.156" y1="618.191" x2="326.891" y2="643.923" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#c3c2c3"></stop></linearGradient></defs><path fill="url(#Eu)" d="M342 618c2 0 5 1 7 3-6 6-13 13-17 20-2 4-3 7-4 11 0 1 0 3-1 4l-1-1h1v-3l1-1c0-1-1-1 0-2v-1l1-1v-2h-2l-4 13h0 0c0-3 1-6 2-9 1-5 2-9 4-14 4-6 9-12 13-17z"></path><defs><linearGradient id="Ev" x1="350.032" y1="640.955" x2="340.865" y2="647.73" xlink:href="#B"><stop offset="0" stop-color="#999797"></stop><stop offset="1" stop-color="#b9b9bb"></stop></linearGradient></defs><path fill="url(#Ev)" d="M327 661c2-8 4-16 9-22 5 1 9 2 13 4l1-1c4 0 7-1 11-2h0 2c2 1 2 3 2 5l-4 2c-3 1-6 2-9 1-5 0-10-2-15-3-1 2-2 4-2 5 0 2-1 3-1 4h-1 0v-2l1-1v-1l1-1-1-1h1v-1c0-1 0-2-1-2-3 4-4 9-5 14 0 1-1 1-2 2z"></path><path d="M363 640c2 1 2 3 2 5l-4 2c-1-1-1-2-2-3v-2c-3 1-6 1-9 1h-1l1-1c4 0 7-1 11-2h0 2z" class="C"></path><path d="M285 86h57c-6 5-30 26-33 27h-7l12-11c-2 0-4 2-6 3-3 2-8 7-12 8-2 1-7 0-9 0h-33-15l24-18c5-3 9-6 14-8 2-1 5-1 8-1zm-83 40h1c7 13 16 23 23 36l15 34 12 24c-5 6-11 12-18 17v-1c-1-1-2-4-3-5-1-3-3-6-3-9-1-4 0-8 0-12s0-9-1-13l-3 15c-2-4-4-7-5-11-1-2-3-4-3-6 0-3 1-6 1-9v-12h-1c0 5-2 10-3 15-2-3-11-17-11-20 0-1 1-3 1-5l2-9-5 11c-2-4-7-9-8-13-1-6 7-21 9-27zm361 387c8 6 14 13 20 21l-8 19-9-15-1 1c1 1 2 4 3 6l6 12-7 18c-1 3-2 6-4 9v1c-1 5-3 9-4 14l-7 16c-3-2-5-5-8-8l-13-13 12-30c2 3 17 19 19 20 0-2-15-21-17-24h-1l19-47zm-176 34c6-1 13 1 18 4 6 3 9 7 11 13-1 2-1 4-3 6-2 4-4 4-8 5-2 0-4 1-6 3-3 7-1 12 2 18l2 9h13c4-1 11-2 15 0 4 1 7 7 8 11-9 4-16 5-26 5 6 1 13 1 20 1l7-3c-2 7-4 11-10 15l-17-6c-2 4-5 8-7 12v1l-10-8c-2-1-4-2-5-4-2-2-4-7-5-11-4-11-9-22-13-33 0-1-1-3-1-4 0-2 1-4 2-6l3-25c3-2 6-3 10-3zM280 177v2l3 56c0 12 3 26 0 38-3 13-10 25-18 36l-2-4c-2-3-4-8-5-11-1-8 9-29 12-38l3-7h0l-19 35-13-30c-1-3-3-8-3-11 0-2 3-5 4-6 6-9 14-17 20-26 7-10 13-22 18-34z" class="E"></path><path d="M267 222h0l-16 34h0c-1-9 11-27 16-34z"></path><path d="M296 280c4 4 8 7 10 11 1 5 0 11-1 16l-3 21c0 3-1 9-1 11 1 3 3 6 4 8l21 54 4 10c1 1 1 3 1 3 0 1-1 3-1 4-1 4-3 8-4 12l-15-38h-1l5 12 6 15c2 5 4 9 4 14h-1c-3-2-6-4-10-6-1-1-3-2-4-3-2-3-3-8-4-11l-9-24-24-60-1-2c0-1 2-3 2-4l10-18 12-25zm37 137v2c2 8 7 17 10 25 1 3 6 17 7 18 2 2 6 3 9 4l16 6c5 1 9 3 13 5 4 3 7 7 7 11 1 4 1 8-2 11-1 2-2 3-5 4-2-1-2-5-2-7-3-7-6-12-11-17 5 8 6 15 8 24l-8 4c-3-10-6-18-12-25 3 7 6 14 8 21 0 1 1 4 1 5 0 2-2 4-3 5l11 32c0 1-3 2-4 3-4-4-26-21-27-24l-37-96c2 3 5 5 8 7 2 1 5 2 6 4 4 4 6 11 8 16l12 29h0l-13-35c-2-3-5-8-5-12 0-2 1-5 1-7 1-4 2-10 4-13zM774 86h20c4 0 8 0 12 1 9 2 16 16 21 24-2-8-8-18-13-24v-1h23c4 0 9 0 13 1 3 1 5 3 8 4 10 7 17 17 23 28-5 5-12 10-18 14-6-6-11-11-18-16-5-2-10-5-15-5-4-1-8-1-11-1h-20-62c-4-9-8-17-14-24l19-1c3 0 7 0 10 1 3 0 7 3 10 4l22 11c-1 0-4-2-5-3-5-4-11-8-17-13h12zM488 585l-5-16 10-13 12 7c2 1 5 3 7 5 1 1 2 3 2 5l9 23c1 3 3 6 4 10s2 7 3 11l6 18c0-2-1-5-2-7l-4-18c0-3-1-7 0-10 2 0 19 15 22 17l-10 28-3 11-7 20c2 2 1 4 1 6v1l-2 2h0v-1h-1l-5-1c-1-1-3-1-3-3-2-2-2-4-3-6l-5-15-17-51c-3-7-5-16-9-23z" class="E"></path><path d="M532 676c2 2 1 4 1 6v1l-2 2h0v-1h-1l2-8z" class="J"></path><path d="M115 86h152l-29 23c-1 1-4 3-5 3-2 1-5 0-7 0l-22 1c-2 2-3 5-4 8-3 6-5 13-9 19-7 1-43-29-50-34l-26-20zm517 250c7 6 14 12 22 17-1 4-3 8-4 12l-8 20-46 116c-1 2-2 5-4 7l-12-38c0-1 0-2 1-3l37-94 14-37z" class="E"></path><path d="M305 259c6-6 14-11 22-11 4 0 10 0 12 3 1 1 2 3 2 4 0 4-1 7-3 9-3 9-7 17-5 26 1 5 4 10 7 13 4 5 9 8 13 13 11 10 16 22 15 36-1 15-6 31-16 42l-1 1v-1c-1-3 0-5-2-8 0-1-1-2-1-4h-1c-1 0-1-1-2-2h0l-2 3-5-12-10-29h0c-3-7-5-14-7-20-3-11-5-21-8-32-3-8-8-14-10-23v-6c0-1 2-2 2-2z"></path><path d="M323 277c2 4 3 8 4 13l-6-10c1-1 1-2 2-3z" class="a"></path><path d="M337 362c0-1-1-2-1-3-2-5-2-10-4-15v-1l1-2 10 29h-1l-1-2h0-1c0-1-1-2-2-3l-1-3z" class="K"></path><path d="M337 362l1 3c1 1 2 2 2 3h1 0l1 2h1l3 8c1 2 1 3 2 4h-1c-1 0-1-1-2-2h0l-2 3-5-12h1c0-1 0-2-1-3h0v-2c-1-1-1-2-1-4z" class="C"></path><path d="M338 365c1 1 2 2 2 3h1 0l1 2h1l3 8v1c-1-1-1-2-2-2-3-3-5-7-6-12zm-13-116c1 0 2 0 2 1 5 1 7 5 9 8 1 3 1 5 0 8l-1 1c-4-5-8-11-11-16l1-2z" class="O"></path><path d="M321 280c-4-7-10-12-12-20h0l5 6c1-4 1-10 4-12h1c0 6 0 17 3 22l1 1c-1 1-1 2-2 3z" class="T"></path><path d="M328 342c1 0 1 0 2 1v-1c-1-3 0 2-1-2v-2l-1-3c-1-3-1-6-2-9l-2-9s-1-2-1-3l1-1 7 22c1 2 2 5 2 6l-1 2v1c2 5 2 10 4 15 0 1 1 2 1 3 0 2 0 3 1 4v2h0c1 1 1 2 1 3h-1l-10-29z" class="F"></path><defs><linearGradient id="Ew" x1="349.189" y1="352.995" x2="345.328" y2="355.293" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#7a7979"></stop></linearGradient></defs><path fill="url(#Ew)" d="M331 328c5 5 8 12 12 18 3-3 7-5 11-7-3 4-6 6-10 8l7 11c3-2 6-5 10-6h0c-1 3-6 5-9 7l10 13c0 1 0 2-1 3-4-4-8-9-12-14l-5 3 4-4c-7-10-13-20-17-32z"></path><defs><linearGradient id="Ex" x1="320.729" y1="301.987" x2="314.942" y2="303.802" xlink:href="#B"><stop offset="0" stop-color="#1e1d1e"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#Ex)" d="M305 259l3 8c2 6 6 11 8 17s3 12 5 18c1 4 2 8 3 11l-1 1c0 1 1 3 1 3l2 9c1 3 1 6 2 9l1 3v2c1 4 0-1 1 2v1c-1-1-1-1-2-1h0c-3-7-5-14-7-20-3-11-5-21-8-32-3-8-8-14-10-23v-6c0-1 2-2 2-2z"></path><path d="M305 259l3 8-1 1v-1c0-1-1-2-1-3-1 0-1 1-1 2 0 0-1 1-2 1v-6c0-1 2-2 2-2z" class="F"></path><path d="M363 86h122c-1 1-6 2-7 3l-22 8c-9 4-18 8-27 13-7 4-15 8-21 14-19 17-22 40-23 64-4-5-8-10-11-16 0-1-2-3-2-5-1-2 1-7 1-10v2l-1 5c-1 4-4 7-7 11l-9 11c1-16 3-33 9-48 2-7 5-14 10-19 3-3 8-5 12-7l25-11v-1c-8 3-17 7-25 10-4 2-10 5-15 6-6 1-12-2-18-4-2 0-4-1-6-2-3 0-7 1-10 1l-22 2c8-7 16-14 25-20 4-2 8-5 12-6 3-1 7-1 10-1zm64 280l13 9c1 0 4 1 5 2 1 2 2 6 2 8l11 30 33 93 14 38 6 19c-5-4-13-8-17-12-1-2-2-5-3-8l-4-12-16-41-2-6h0l23 66c-2 5-6 10-9 14-2-2-4-8-5-10l-9-30-22-60-29-81 9-19zm-56-195c3 6 6 12 9 17 2 3 4 6 5 8 1 5 1 11 2 16 1 10 4 19 7 28l49 132h-1c-1 0-3-1-5-2-3-2-8-4-10-6-1-1-2-4-3-5l-4-11-20-52h-1l2 3 11 30 9 27c1 2 3 6 3 9 1 2-6 14-7 16-8-18-14-39-21-58l-22-58c-6-18-13-35-16-54-1-5-1-10-2-16 0-1 0-3 1-4 0-1 2-3 3-5l11-15zm461-40h1c5 7 9 17 12 25l-8 15-6-12c1 6 4 12 3 17 0 1 0 2-1 2l-4 9-4-13v1c1 7 1 13-1 19-3-4-3-10-5-15 0 3 0 6 1 8 0 5 1 10 1 14 0 2-1 4-2 6-2 3-3 7-5 10l-4-10c0 4 1 10 1 14 0 2 1 4 0 5v1c-2 6-5 12-8 17-2-6-3-11-5-17-1 9 2 18 1 27-1 5-4 11-7 16l-17 42-16 38c-2 5-4 12-7 17-4 5-11 10-17 14-1-2-3-5-4-8 0-1-1-2-1-3 0 0 1-2 1-3 1-3 3-8 4-11 0-3-2-8-2-11l-4-21-3-52v-32c0-4 0-10 1-14 11 20 10 45 11 66l2 10c0 2-1 4 0 6l-2-57c4 12 6 25 8 38 2 9 4 18 5 27 1 1 1 2 1 3-1-22-7-43-11-64l20 39 35-90c3-6 4-13 7-19 3-7 8-13 12-19 6-11 11-23 17-35zM678 320l13 15-6 13c-9-9-16-18-24-27-5-5-10-9-15-13-1 0-2 0-2-1 0 0 1-3 2-4l4-11c7-17 15-33 20-50 8-26 11-53 4-80-1-7-4-15-9-22-3-4-8-8-13-11-10-8-22-14-34-20 5-7 11-15 17-19 3-2 5-3 8-3 9-2 20-1 30-1h26c5 0 10 0 15 1 10 2 16 16 20 24h-49c0 3 0 7 1 10 0 2 2 3 3 5l5 8c1 1 2 1 2 2l1-5c0 2 1 4 2 6l3 6c0 2 0 3-2 5 8 27 8 56 1 83-2 7-5 15-8 22l-5 13-11 27c-1 3-3 6-3 8-1 3-3 8-3 11l2 2 5 6z" class="E"></path><path d="M697 131c0 2 1 4 2 6l3 6c0 2 0 3-2 5l-6-14c1 1 2 1 2 2l1-5z" class="J"></path><path d="M643 98h2c2 0 4 2 6 2 10 5 19 11 24 20 12 19 17 46 17 68 0 4 1 8 0 12v-19c-1-16-5-32-12-46-7-16-20-31-37-37z"></path><path d="M631 602c-1 2-1 4-2 4-2 0-2 0-3-1 1-5 2-9 5-13 1 0 0 0 1-1-2 0-4 3-5 5-2 3-3 7-4 10-4-1-9-2-12-6-1-2-2-5-1-8 1-4 4-9 7-11 5-3 10-4 15-5 4-1 7-2 11-3 0 0 2 0 2-1 1-1 3-6 4-8l8-20c2-3 3-7 4-10 1-2 1-6-1-8-2-3-6-5-7-9v-1c-1-2-1-5 0-7 2-5 7-8 11-10 3 0 6-1 8 0 1 1 2 2 2 3v2c4-4 8-16 8-21l-3-6c4-10 10-19 18-26v-1c-11 7-16 14-22 24-6-1-18-3-21-9-2-3-3-8-1-12 2-7 9-12 16-15 5-2 10-3 16-4 5-2 12-2 17-5 3-1 4-4 6-7 3-6 6-13 9-20l11-30c2 4 5 10 5 14l-3 9c-1 2-6 13-5 13l6-13c1-3 2-6 4-8 2-4 6-6 9-9 2-2 4-5 6-6l-30 73-12 30c-2 4-4 9-6 13-2 3-4 6-6 8-5 4-10 9-14 14-5 6-7 16-10 23h1c2-5 4-12 7-17 2-3 5-6 8-9l11-12-58 139-17 42c-2 7-5 15-8 21l-6 6-9 7c-2 0-2-2-3-3l-1 1-3-5 2-2c-1-1-1-2-1-3 0-2 2-5 3-7l8-21c2-6 5-11 5-16 0-3 0-8-1-10 0-1-2-2-3-3-2-3-3-8-3-12 2-5 7-10 12-12 10-3 16 4 24 8-4-4-7-6-10-10 1-4 1-7 2-10 0-1-1-2-1-3z" class="E"></path><path d="M596 701c0 1 2 4 2 6l-1 1-3-5 2-2z" class="I"></path><path d="M631 602c2-5 5-9 8-14l1 1c-3 5-6 11-8 16 0-1-1-2-1-3z" class="R"></path><path d="M718 424v1c0 2-1 4-3 5-4 4-10 6-15 7-7 3-14 6-21 10-6 4-11 9-16 15l1-1c8-20 29-24 47-31 3-1 5-2 7-6z"></path><path d="M597 708c0 2 1 4 1 5 0 2-1 3-1 5-1 4-3 9-5 13 0 1-1 2 0 2l5-12c1-2 1-4 3-6 1-2 4-3 5-5a30.44 30.44 0 0 0 8-8l-99 254-122-323 9 8c2 1 5 3 6 5 4 4 6 11 8 17l14 34c0-3-4-10-5-13l-11-29-4-10-1-2c0-1 1-3 1-4l5-11c4 7 6 15 9 23 1 2 2 6 4 7 1 2 7 3 9 4l14 4c11 3 22 5 27 16 3 5 3 9 2 14-1 2-2 4-3 4-3 1-6 2-9 2-2-8-5-14-10-20-1-2-3-4-4-5h-1c3 3 5 6 7 10 2 5 3 11 4 16l-8 4c-3-9-7-17-13-25-1-1-2-3-3-4 0 3 3 5 4 8 4 6 7 13 8 20 1 4-2 7-2 10 0 1 0 3 1 3l16 42 30 80 13 35c2 5 5 9 6 14 1 4 0 8 1 12v14c1-9 1-18 3-27 1-4 3-9 5-13l11-27 28-73 15-38c2-5 5-10 6-15 1-1 2-5 3-6l5-12c0-1 1-2 2-3v1l3 5z" class="E"></path></svg>