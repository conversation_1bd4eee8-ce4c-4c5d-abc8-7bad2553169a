<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 84 808 876"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#373738}.C{fill:#2b2c2c}.D{fill:#434344}.E{fill:#323233}.F{fill:#262627}.G{fill:#3e3e3f}.H{fill:#1c1c1d}.I{fill:#232323}.J{fill:#484849}.K{fill:#4f4f50}.L{fill:#2f2f2f}.M{fill:#9c9b9c}.N{fill:#7f8080}.O{fill:#3b3b3b}.P{fill:#a7a7a7}.Q{fill:#5a5a5b}.R{fill:#545455}.S{fill:#1f1f20}.T{fill:#919192}.U{fill:#4b4b4c}.V{fill:#696969}.W{fill:#171717}.X{fill:#c2c1c2}.Y{fill:#b1b0b1}.Z{fill:#626263}.a{fill:#5d5e5e}.b{fill:#131313}.c{fill:#787778}.d{fill:#c5c3c4}.e{fill:#6f6f70}.f{fill:#b6b6b7}.g{fill:#cdcdce}.h{fill:#c9c8c8}.i{fill:#747374}.j{fill:#0e0e0f}.k{fill:#888}.l{fill:#8b8b8c}.m{fill:#b07d3b}.n{fill:#ebd3a0}.o{fill:#bc8946}.p{fill:#d3af69}.q{fill:#ebdebb}.r{fill:#9f6424}.s{fill:#e2c58a}.t{fill:#c58d3f}.u{fill:#e3d3a7}.v{fill:#e0be7a}</style><path d="M551 214l2 7v-1c-2 0-3 0-3-2l1-4z" class="b"></path><path d="M536 154c1 1 1 1 1 2-1 1-3 2-4 2v-1-1-1c1-1 2 0 3-1z" class="N"></path><path d="M544 213s1 0 1 1l-1 1 1 1h-8l6-3h1z" class="T"></path><path d="M141 184c1 0 2 1 2 1 1 1 1 1 0 2-1 0-3 1-4 1l-1 1-1-1 4-4z" class="g"></path><path d="M441 282l3 1v5l-2-1-1 1v-6z" class="b"></path><path d="M513 133c0-3 0-5-1-8 2 2 4 4 5 6h0-2v1 1h-2zm-389 86h0c-1 0-2-1-3-3h1 4c1 1 2 1 3 1l-2 1v1h-3z" class="X"></path><path d="M887 176h0 4c-1 3-3 5-5 8h0v-1-3c1-1 1-1 1-2v-2z" class="Y"></path><path d="M457 349h1v-2h1c1 2 1 4 2 5h1c0 2-1 3-1 5h0l-1-1-1-1c0-1-1-1-1-3 0-1-1-2-1-3z" class="C"></path><path d="M178 233c4 2 8 4 11 7l-2 1h-2-1c-1-3-3-5-6-6v-2z" class="X"></path><path d="M865 214l1-1c1 0 1 0 1-1h0c1 0 2 0 2 1v1c1 0 1 0 2 1l3 2h0c0 1 1 2 2 3l-1 1c-3-3-6-5-10-7z" class="f"></path><path d="M453 186c-5-1-9 0-14 2 3-3 6-4 9-6 1 1 2 1 4 2h1l1 1-1 1z" class="T"></path><path d="M549 205l1 4v1 2c-2 2-3 3-5 4l-1-1 1-1c0-1-1-1-1-1 2-3 4-5 5-8z" class="M"></path><path d="M442 205c2 1 3 1 4 2-2 1-5 2-7 3h-2l-3-1-1-1 4-2h3l2-1z" class="g"></path><path d="M352 540l-2-1c-1 0-2-1-3-2l1-1c3 1 8 4 11 4l-1 1-3 5c-1-2-1-3-1-5v-1h-2z" class="b"></path><path d="M480 453l3 8 1 2c-1 2-1 2-1 4l-1 2-4-12c1-1 1-3 2-4z" class="M"></path><path d="M466 218c2 2 2 3 2 5l1 1c-2 2-5 3-9 3h0v-1h1l-3-2h3c3-1 4-3 5-6z" class="T"></path><path d="M348 550l-4-8c-1-1-2-3-2-3v-10l3 7c1 2 1 4 1 6 0 1 0 1 1 2h0c0 1 0 2 1 2 0 1 1 2 0 4z" class="R"></path><path d="M467 230c3-3 7-4 11-7v-1c2-1 5-2 7-1h3 1c-8 2-14 7-22 9z" class="S"></path><path d="M425 284c2 1 2 3 3 4l1 3c1 3 5 6 8 7h1c1 1 2 1 2 3-4-1-9-4-12-7-2-3-3-7-3-10z" class="M"></path><path d="M582 478l9-22c0 3 0 5-1 7h0c1-2 1-3 3-4l-7 14c0 2-1 4-3 5h-1zm-345-90l1-3h1v1c0 1 0 2 1 3 0 2 0 3 1 5h0c1 0 2 1 3 1 1 1 2 3 3 3 2 1 4 1 6 1h0 1l-2 2c-2 0-4 0-6-1-5-3-7-7-9-12z" class="P"></path><path d="M126 216c2-4 2-7 1-12 1 1 2 3 4 5l2-3 1 2-2 4c-2 2-2 4-3 5-1 0-2 0-3-1z" class="g"></path><path d="M197 210h2c1 1 3 2 4 2v1c0 1-1 1-1 2s-1 2-1 4h1l-2 1c0-1 0-1-1-2-1-3-4-4-7-6 1-1 3-1 5-2z" class="d"></path><path d="M589 287c1 2 0 4 2 5 1 1 2 1 3 1 3 0 5-2 6-4 1-1 1-3 1-4 1-1 0-3 1-4 1 1 1 4 0 6 0 2-2 5-4 7-2 1-4 2-6 1-1 0-2-1-4-1 1-2 0-5 0-6l1-2v1z" class="T"></path><path d="M448 182c2-1 6-3 9-3 2 0 6 2 8 3v1 2l-1-1c-2-2-4-3-7-3 1 1 2 1 3 2h-1l-2 1h-4-1c-2-1-3-1-4-2z" class="P"></path><path d="M441 809l1-1c1 0 2 0 3 1h1c3 1 4 3 5 5h1v-3l2 2c-2 4-4 7-8 9h-1c3-2 4-4 3-7s-3-5-6-6h-1z" class="Y"></path><path d="M574 812c3-2 7-4 11-3l8 3-1 1c-3-1-7-3-10-2-2 0-3 2-4 3 0 2 0 3 1 5h0c-2-2-4-4-5-7z" class="c"></path><path d="M676 241v-1-7c1 2 2 5 4 8 1 2 4 4 5 7l6 5-2 1c-2-3-6-8-10-9-1-1-2-3-3-4z" class="g"></path><path d="M592 202c1 3 2 4 6 6-2 0-5 1-6 1-3 2-3 5-6 4 0-1 1-1 2-2h0c1 0 2-1 2-1v-2c-2-1-5-2-7-3l2-2 2 1h0c1 1 2 1 3 1 0-1 1-2 2-3z" class="h"></path><path d="M455 342h1 0c2-1 4-2 6-2h1c0 1 0 2-1 2l-2 2v1h1v1 2 4c-1-1-1-3-2-5h-1v2h-1c-1-1-1-2-1-3v-1c-2 1-2 1-4 0l1-2s1-1 2-1z" class="O"></path><path d="M444 342h1 0c0 1 1 1 1 2l3 9 4 16-1 2-8-29zm208 380c2 1 4 2 5 4h0-1l-2-2-4 2c0 3 1 5 2 7v1l-1-1h-1l1 1-1 1h0c0-1 0-1-1-1l-3-5c0-2 0-4 1-5 1 0 3-1 4-1l1-1z" class="X"></path><path d="M525 155v-1c2-2 3-2 5-2 3-1 4 1 6 2-1 1-2 0-3 1v1 1c-1 1-3 2-3 3l-1-1-2-1c-2-1-2-2-2-3z" class="d"></path><path d="M527 158c2-2 3-3 6-3v1 1c-1 1-3 2-3 3l-1-1-2-1z" class="e"></path><path d="M466 496l1 2h1c3 1 5 3 6 6l1 5c-1-3-3-6-6-6-2-1-4 0-6 1l-2 2-1-1c1-3 3-4 4-6h1l1-1v-2z" class="M"></path><path d="M160 224c-1-1-2-2-2-4 6 5 13 9 20 13v2c-7-2-12-6-18-11z" class="d"></path><defs><linearGradient id="A" x1="462.56" y1="511.269" x2="458.117" y2="517.698" xlink:href="#B"><stop offset="0" stop-color="#535353"></stop><stop offset="1" stop-color="#6b6b6c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M462 499h2c-1 2-3 3-4 6l1 1 2 14h-3c-1-3-4-8-2-11 1-2 1-3 0-5l1-2 3-3z"></path><path d="M441 448c1 2 1 5 0 7l5 5h0l-3-1-1 2c0 1 0 1-1 2l-1-3s-1-1-1-2l-4-9 1-1h4 1z" class="e"></path><path d="M441 448c1 2 1 5 0 7-2-2-4-4-5-7h4 1z" class="W"></path><path d="M404 252h3v1l1 1c2 4 5 8 6 12l2 4h7c0 1 0 2 1 3h-2c-1 0-2-1-3-1h-1v-1h-2c-1 1 0 2 0 3l-2-2c-2-3-2-7-4-9-1-4-4-7-6-11z" class="m"></path><path d="M260 331h1c0 1 0 2 1 2v1c1 1 2 4 2 5 1 2 1 3 2 4v-3h-1c-1-3-1-4-1-7l12 31-1 1-1-1-14-33z" class="M"></path><path d="M455 191l1 2c0 2-1 4-3 5-4 2-10 4-13 8h-3c1-2 2-5 4-6 1-1 2-2 4-2 4-2 8-3 10-7z" class="f"></path><path d="M478 212c1-2 1-5 1-7 1 1 2 3 3 4l1 1 4 4c-1 1-2 1-3 2-2 0-4-1-5-1-1 2-1 4-4 6-1 0-3 1-4 1h-1l5-6c2-1 2-2 3-4z" class="l"></path><path d="M461 829l3 1 3 8c1 1 4 4 4 5v4c0 2 0 3 1 5h-2 0c-2-4-5-9-7-13 0-1 1-1 1-1l1-1c-1-3-3-5-4-8z" class="N"></path><path d="M542 221c7 1 11 5 17 8 5 2 9 4 14 5h-1c-2 1-4 0-6-1h-2-4c-1 0-3-1-5-2-1-1-4-2-5-4h1c-2-3-7-4-10-4l1-2z" class="T"></path><path d="M496 501l3 15c1 6 2 14 5 19 0 2 1 5 0 7l-11-39h2l1-2z" class="M"></path><path d="M560 233h4 2c2 1 4 2 6 1h1c8 1 17 1 22 7 1 2 2 3 2 5v-1l-1-1c-5-7-14-5-21-6h-1l4-1c-1-1-6-1-8-2-4 0-7-1-10-2z" class="c"></path><path d="M650 735h0l1-1-1-1h1l1 1v-1c7 9 10 24 8 35v1c-1-2 0-3-1-5h0l-1 1-1-1 1-1h0v-3-2c-1-4-1-7-2-10s-2-5-3-8c-1-2-2-3-3-5z" class="f"></path><path d="M400 174l2 2c-3 3-5 7-7 11-3 6-3 17-10 20h-1c0-3 0-5-1-7l1-1 2 6c1-2 3-5 4-7 1-4 2-9 3-13 2-4 4-8 7-11z" class="X"></path><path d="M678 553c4-1 3-6 6-8-1 3-3 7-3 11-4 8-8 16-11 26-1-1-1-1-1-2s2-5 2-6c0-2 1-4 1-7 2-4 5-9 6-14z" class="f"></path><path d="M483 205c1 1 2 3 3 4 5 4 14 2 20 3l1 1v1 1 1h1c3 0 7 3 10 2l1-1c1 1 1 2 2 3h0c-7 1-15 0-23 0l2-1c2-1 5 0 8 0 1 1 8 0 10 0h-3c-2-1-1-1-2-1h-1c-1-1-2-1-3-1-2-1-4 0-6-1h-1-1v-1l-1-1 1-1c1-1 2-1 3-1h-2-5c-1 1-1 1-2 1-1-1-2-1-3-1h-1c-1 0-1 0-2-1h0c-2 0-2 0-3-1h0c-2-1-3-4-4-5h1z" class="j"></path><path d="M507 216c-1 0-3 0-5-1l-1-1 1-1c2 0 3 1 5 1v1 1z" class="H"></path><path d="M345 536l2 3h1c1 1 2 1 3 2l1-1h2v1c0 2 0 3 1 5l-4 10h-1v-1l-2-5c1-2 0-3 0-4-1 0-1-1-1-2h0c-1-1-1-1-1-2 0-2 0-4-1-6z" class="j"></path><path d="M345 536l2 3 3 6c1 2 0 5 1 7 0 2 0 2-1 3l-2-5c1-2 0-3 0-4-1 0-1-1-1-2h0c-1-1-1-1-1-2 0-2 0-4-1-6z" class="B"></path><defs><linearGradient id="C" x1="359.154" y1="273.048" x2="374.346" y2="281.452" xlink:href="#B"><stop offset="0" stop-color="#6e706c"></stop><stop offset="1" stop-color="#a2a0a6"></stop></linearGradient></defs><path fill="url(#C)" d="M361 264c0-1-1-2 0-3l16 35h-1l-3-5v-1c0-1-1-1-1-2-2 2 3 6 2 8v1c-2-3-3-6-4-9-4-8-8-15-9-24z"></path><path d="M124 219h3l2 1c1 0 1 0 1 1-1 8 0 16-1 25l-1 1c-1-5-2-10-2-14 0-2 0-4-1-6 0-1-2-2-2-4l3-1v-1l-2-2z" class="h"></path><path d="M404 252l-3-6 17 1h-6c0 1 1 2 2 2-2 0-4 0-5 1 0 1 1 3 2 4v1l6-1v1c-2 0-1 1-2 2-1 3 2 6-1 9-1-4-4-8-6-12l-1-1v-1h-3z" class="s"></path><path d="M365 691c2 1 3 2 4 4 1 1 4 2 6 3 6 6 13 10 22 12 0 1-2 2-2 2-3 0-7-1-9-3-4-2-8-5-12-7h-1c-3-2-7-7-8-11z" class="P"></path><path d="M618 246h12c-5 8-9 17-14 26h0v-4-1l1-2c0-2 1-3 2-5 0-1 0-2 1-3h1c1-2 1-4 1-6l-1-1v-1l-3-3z" class="n"></path><defs><linearGradient id="D" x1="425.368" y1="419.244" x2="422.383" y2="405.417" xlink:href="#B"><stop offset="0" stop-color="#626363"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#D)" d="M420 397c2 4 3 7 4 11l8 24 1 3c-1-1-2-2-3-2-2-3-3-6-5-9l-1-2c1-1 1-2 2-3l-8-19c2 1 2 4 3 6l1-1-1-1c0-3 1 0 0-2v-1c-1-1-2-2-1-4z"></path><path d="M426 419c1 2 2 4 3 5 1 3 1 6 3 8l1 3c-1-1-2-2-3-2-2-3-3-6-5-9l-1-2c1-1 1-2 2-3z" class="E"></path><path d="M507 550h3l1-1-1-1 1-1h1c0 1 0 2 1 3 1-2 0-9 1-12v22 11h1c0 2-1 4-1 5l-7-26z" class="m"></path><path d="M465 182c0-3-1-6-2-9 3 1 6 0 9 2l-2 2v2l2 2h0c1 1 1 2 3 3v-1l1 1c0 1 0 2 1 3-1 0-2 0-3 1 1 0 1 1 2 1h-5l-2-2c0-1 0-2-1-2h0c0 1 0 2 1 2l2 3-1 1c-2-2-4-6-5-8v-1z" class="e"></path><path d="M470 179l2 2h0c1 1 1 2 3 3v-1l1 1c0 1 0 2 1 3-1 0-2 0-3 1-3-2-3-6-4-9z" class="j"></path><path d="M570 191l1-1c2 1 0 3 2 4v-2l1-2v-2c1-1 2-2 4-3l-4 8c5 2 15 4 18 9-1 1-2 2-2 3-1 0-2 0-3-1h0l-2-1c-3-2-6-3-9-4l-2-2-1-1v-1h-1c-1-1-2-3-2-4z" class="Y"></path><path d="M573 195c4 1 12 4 14 7v2l-2-1c-3-2-6-3-9-4l-2-2-1-1v-1z" class="E"></path><path d="M460 219l1-1c1 0 2-1 2-1l2-2v1l-1 2-3 6h-3l3 2h-1v1h0c0 1-3 1-3 1-4 0-9 0-11-3-1-1-2-2-2-3 5-1 8 1 13-3h0 2l-1 1 1 1c0-1 1-2 1-2z" class="N"></path><path d="M460 219l1-1c1 0 2-1 2-1l2-2v1l-1 2-3 6h-3-8c3-1 5-1 8-3h1c0-1 1-2 1-2z" class="H"></path><path d="M574 803l1 1h0c-1 1-1 2-1 3v1l-1 3-19 39-1-1c0 1 0 1-1 1v1l16-36 6-12z" class="l"></path><defs><linearGradient id="E" x1="633.156" y1="707.492" x2="638.798" y2="726.541" xlink:href="#B"><stop offset="0" stop-color="#a8a8a8"></stop><stop offset="1" stop-color="#cac9c9"></stop></linearGradient></defs><path fill="url(#E)" d="M615 706h1c4 2 8 4 12 5 1 1 3 2 4 3 5 2 10 5 15 7 1 0 3 1 5 1l-1 1c-1 0-3 1-4 1-1 1-1 3-1 5-2-3-5-5-7-7-4-3-7-6-12-7v-1l-8-5c-2-1-3-2-5-3h1z"></path><path d="M566 534l1 4-18 40h-1c0 1 0 0-1 1 1-3 2-6 3-10 1-3 2-7 3-10 6-6 9-17 13-25z" class="X"></path><path d="M597 308l1 1-3 6c-1 4-2 11-6 14-5 2-8 1-10 7-2 3-1 7 0 11 1 3 4 5 6 7v1c-2 2-3 6-4 9l-3 10-1-2 6-17c-1-2-2-3-4-5l-1-2c-1-5-2-8-1-13 2-2 3-5 6-6h1l2-2 2-1c4-3 6-11 7-16l2-2z" class="T"></path><defs><linearGradient id="F" x1="362.531" y1="689.921" x2="373.332" y2="672.301" xlink:href="#B"><stop offset="0" stop-color="#b7b7b8"></stop><stop offset="1" stop-color="#dedddf"></stop></linearGradient></defs><path fill="url(#F)" d="M365 691c-1-2-2-5-2-7-1-8 1-16 5-22 2-3 5-6 8-7-7 7-10 12-10 22 0 8 4 15 9 21-2-1-5-2-6-3-1-2-2-3-4-4z"></path><defs><linearGradient id="G" x1="169.934" y1="146.204" x2="168.566" y2="138.796" xlink:href="#B"><stop offset="0" stop-color="#b5b2b3"></stop><stop offset="1" stop-color="#dddee2"></stop></linearGradient></defs><path fill="url(#G)" d="M148 144c0-1-1-1-2-1v-1c4-1 8 0 12 0h29c5 0 9-1 14 0-1 0-3 0-3 1-1 0-1 0-2 1-1 0-5 0-7 1h-1c-1-1-3-1-4-1h0c-2 0-6 2-8 2s-5-2-8-2c-6-1-13 0-20 0z"></path><path d="M187 142c5 0 9-1 14 0-1 0-3 0-3 1-1 0-1 0-2 1-1 0-5 0-7 1h-1c-1-1-3-1-4-1 1 0 1-1 2 0 1 0 1 0 2-1h1 1c1 0 1 0 2-1h-5z" class="f"></path><path d="M557 505l-2 2v1h-1c0-6 3-8 9-10 1 0 3-1 3-1 2-2 4-4 5-6 1-3 3-5 5-8v6l-1 1c-2 0-5 3-5 5-1 1-1 1-1 2h-1v1l1 1h-1-1-4v1h0c3 0 4 1 6 2h0c3 2 6 6 7 9-2-2-3-4-6-4h0-2c-3-2-7-2-11-2z" class="M"></path><path d="M557 505c2-2 3-3 5-3 3 1 5 2 7 4l1 1h-2c-3-2-7-2-11-2z" class="I"></path><path d="M261 370c2 0 4 0 5 1-1 1-1 1-2 1v1s1 1 2 1c-5-1-9-1-14 2-4 3-7 6-7 11-1 4 0 7 2 11-1 0-2-2-3-3-1 0-2-1-3-1-1-3 0-5 0-8 1-1 1-2 1-3a25.03 25.03 0 0 1 13-12c2-1 4-1 6-1z" class="h"></path><path d="M261 370c2 0 4 0 5 1-1 1-1 1-2 1v1c-2 0-4-1-6-1l-3-1c2-1 4-1 6-1z" class="P"></path><path d="M524 542l-9 29h-1v-11c2-4 0-8 1-12 1-3 2-3 4-6 2 1 4 0 5 0z" class="o"></path><path d="M150 146c1 0 1 0 2 1 4 6 5 12 3 18-1 5-5 10-9 14l-1 1v1l-1 1c1 1 1 2 3 2v1h-4s-1-1-2-1v-5c0-1-1-2-2-3l1-1 5-1c4-3 7-7 8-13 0-5 0-11-3-15z" class="X"></path><path d="M459 183h1c-1-1-2-1-3-2 3 0 5 1 7 3l1 1v-2c1 2 3 6 5 8l1-1-2-3c-1 0-1-1-1-2h0c1 0 1 1 1 2l2 2h5 7 7l1 1c-1 1-2 1-3 1l-1-1h-4-2c-2 0-3 2-4 3l-2 1h0c-3 1-10 1-12-1 0-1-1-2-1-3-1-2-2-4-5-6h0l2-1z" class="T"></path><path d="M459 183h1c-1-1-2-1-3-2 3 0 5 1 7 3l1 1 3 5v1h-1c-2-2-5-4-6-6h2c0-1-3-1-4-2z" class="i"></path><path d="M482 469l1-2c0-2 0-2 1-4l3 10c0 1 1 3 1 3l8 25-1 2h-2l-11-34z" class="f"></path><path d="M189 240c1 1 1 2 2 4-2 2-4 4-6 5-8 7-17 12-24 20l2 5h-1c-1-2-2-3-3-5v-1l-2 2c0-2 0-2 1-3s2-2 4-3c2-2 4-3 6-6 3-1 5-3 7-5 3-2 5-4 8-6 2-2 2-3 2-6h2l2-1z" class="g"></path><path d="M498 220c8 0 16 1 23 0 7 0 14 0 21 1l-1 2c-6-2-12-1-17 0-5 0-10 0-15-1-7 0-14-1-21 0-3 1-6 3-9 4h-1l-1 1-6 3-9 3c-1 0-2 0-3 1h-1c-1 0-2 0-4-1l13-3c8-2 14-7 22-9l9-1z" class="h"></path><path d="M520 139c3-2 5-2 8-2h4c1 1 1 2 1 3-1 1-2 1-4 2-4 1-6 6-5 10v3l2 7h0-1l-2-4v2c-2-4-4-9-5-13v-2c1-1 1-2 1-3s0-2 1-3h0z" class="d"></path><path d="M520 139l1 2h1v-2c2-1 3-1 4 0h1-1c-1 1-2 1-2 3v2 1c-3 4-2 7-1 12v1 2c-2-4-4-9-5-13v-2c1-1 1-2 1-3s0-2 1-3h0z" class="P"></path><path d="M397 710c2-1 4-1 7-2v1c-4 2-10 5-14 8-1 2-2 4-4 5-3 3-5 5-7 9l-6 11h-1-1c1-5 6-9 6-14l-1-1h-2c-4 1-6 5-8 8l1-4a10.85 10.85 0 0 1 8-8c2-1 5-1 7-2 4-3 9-6 13-9 0 0 2-1 2-2z" class="g"></path><defs><linearGradient id="H" x1="264.805" y1="377.409" x2="240.951" y2="370.216" xlink:href="#B"><stop offset="0" stop-color="#adafb4"></stop><stop offset="1" stop-color="#cfcdcd"></stop></linearGradient></defs><path fill="url(#H)" d="M237 388v-8c1-5 6-10 10-13 7-5 16-6 24-4-1 1-2 1-3 2-1 0-1 0-2 1-3-1-5 0-8 0l-1 1c-6 1-11 5-14 9-2 4-2 9-3 13-1-1-1-2-1-3v-1h-1l-1 3z"></path><path d="M855 142h29c-4 3-7 7-7 14 0 5 1 11 5 14l3 3c2 0 4-1 5 0s1 2 1 3h-4 0l-4 1c-1-1-1-1-2-1-4-3-7-11-8-16 0-6 2-11 6-15l-1-1h0l1-1h1 1 0 0c-3-1-5-1-8-1h-18z" class="X"></path><path d="M295 414c3 2 6 4 9 5-1 1-1 2-1 4 1 0 1 1 1 2l3 9c1-3 0-6-1-8h1c2 3 2 7 2 10v13h-1c-2-3-3-5-4-9-2-7-4-14-8-21v-2l-1-3z" class="U"></path><path d="M295 414c3 2 6 4 9 5-1 1-1 2-1 4 1 0 1 1 1 2-1 4 2 8 2 12-1-2-1-3-2-5l-3-6c-1-2-2-6-4-8l-1 1v-2l-1-3z" class="j"></path><defs><linearGradient id="I" x1="349.755" y1="566.937" x2="355.703" y2="574.843" xlink:href="#B"><stop offset="0" stop-color="#818483"></stop><stop offset="1" stop-color="#9d9a9c"></stop></linearGradient></defs><path fill="url(#I)" d="M359 540h2v1 1h1c1 2-2 5-3 6 2-1 4-4 6-5l1 1-7 7h-1l-3 7c-2 5-2 9-1 14h1c1 1 1 3 1 4l-1 2c-1 1-1 1-1 2-3-8-4-15-3-24l4-10 3-5 1-1z"></path><path d="M359 540h2v1 1h1c1 2-2 5-3 6 2-1 4-4 6-5l1 1-7 7h-1v-1c-3 2-5 8-6 11h0 0v-1c2-6 3-12 6-18h1l-1-1 1-1z" class="E"></path><path d="M152 147c2 1 5 1 7 1 4 0 9-1 14 1 1 1 3 1 4 1v1c-1 0-1 1-1 2h-1c-2-1-3 0-4-1-1 0-2 0-3-1h-2l-8 2c0 4 1 7 0 11h0c-1 5-3 8-5 12-1 1-3 3-3 5l-1 1c-2-1-2-1-4-1v-1l1-1c4-4 8-9 9-14 2-6 1-12-3-18z" class="B"></path><path d="M514 517l1 7-1 14c-1 3 0 10-1 12-1-1-1-2-1-3h-1l-1 1 1 1-1 1h-3l-3-8c1-2 0-5 0-7 1-3 4-5 5-8h0c1 1 1 2 2 2h0c2-4 3-8 3-12z" class="t"></path><path d="M514 509h3l-1 10 1 10c1 1 2 1 3 1l1-2c1 2 2 4 4 5 0 1 0 1 1 2l-2 7c-1 0-3 1-5 0-2 3-3 3-4 6-1 4 1 8-1 12v-22l1-14-1-7v-8z" class="p"></path><path d="M514 509h3l-1 10v1c0 5 0 11-1 17v-13l-1-7v-8z" class="u"></path><path d="M531 180c7-6 15-10 21-16 3-3 5-8 8-10 2 0 3 1 4 3 0 1 0 0-1 1-2 4-3 8-6 12l-3 4-4 5c0-2 2-4 4-6l3-6-3 3h-1l1-2c-5 1-8 5-12 8-2 2-4 3-6 5-2 1-3 4-6 4l3-3-1-1c-1 0-1-1-1-1z" class="h"></path><path d="M532 181c1-2 3-3 5-4 1-1 3-2 4-3 6-5 12-8 18-14-1 3-3 5-5 8-5 1-8 5-12 8-2 2-4 3-6 5-2 1-3 4-6 4l3-3-1-1z" class="J"></path><defs><linearGradient id="J" x1="557.307" y1="411.799" x2="569.073" y2="418.415" xlink:href="#B"><stop offset="0" stop-color="#6d6b6c"></stop><stop offset="1" stop-color="#959798"></stop></linearGradient></defs><path fill="url(#J)" d="M551 448l26-76 1 2-18 55-6 18c-1 4-2 8-4 12v-3l1-1v-1-1h0l1-1v-1h0v-1c1-1 1-1 1-2h-1c-1 3-1 7-3 9h-1l3-9z"></path><path d="M453 369l9 27c0 2 1 6 2 8l10 29c1 6 3 13 6 19v1c-1 1-1 3-2 4l-18-59c-3-9-6-18-8-27l1-2z" class="N"></path><path d="M271 363c1 0 2 1 3 1h0l1 1 6 23-1-1-3-7c0-2-1-3-1-5-1-1-2-1-3-1l-7-3c-1-1-3-1-5-1s-4 0-6 1a25.03 25.03 0 0 0-13 12c0 1 0 2-1 3 0 3-1 5 0 8h0c-1-2-1-3-1-5 1-4 1-9 3-13 3-4 8-8 14-9l1-1c3 0 5-1 8 0 1-1 1-1 2-1 1-1 2-1 3-2z" class="S"></path><path d="M271 363c1 0 2 1 3 1l1 2-1 1-8-1c1-1 1-1 2-1 1-1 2-1 3-2z" class="M"></path><path d="M261 370c3-2 6-2 9 0 2 1 3 1 4 3l-1 1-7-3c-1-1-3-1-5-1z" class="B"></path><path d="M524 155h1c0 1 0 2 2 3l2 1 1 1c0-1 2-2 3-3v1c-3 6 0 14-2 21v1s0 1 1 1l1 1-3 3-6 7c0-1 0-2-1-2v-1c1 0 1-1 2-1h0-4c-1 1-2 2-3 2h-2l-1-1c1-1 3-2 5-3s3-3 5-5c2-4 3-5 2-10-1-3-2-6-4-10v-1-2l2 4h1 0l-2-7z" class="M"></path><path d="M531 179v1s0 1 1 1l1 1-3 3-6 7c0-1 0-2-1-2v-1c1 0 1-1 2-1h0-4c4-3 7-5 10-9z" class="D"></path><path d="M524 155h1c0 1 0 2 2 3l2 1 1 1c0 6 1 11 0 16h-1v-2l-2-3c-1-3-2-6-4-10v-1-2l2 4h1 0l-2-7z" class="Z"></path><path d="M523 158l2 4h1 0c2 4 3 8 3 12l-2-3c-1-3-2-6-4-10v-1-2z" class="f"></path><path d="M266 371l7 3c1 0 2 0 3 1 0 2 1 3 1 5l3 7 1 1c2 2 4 9 7 10 1 3 4 6 6 8 6 7 11 13 19 16 1 0 3 1 4 2h-3c-1 0-3-1-3-1l-7-4c-3-1-6-3-9-5-11-10-15-22-20-35-3-2-6-3-9-5-1 0-2-1-2-1v-1c1 0 1 0 2-1z" class="M"></path><path d="M281 388c2 2 4 9 7 10 1 3 4 6 6 8 6 7 11 13 19 16-1 0-2 0-4-1-14-6-24-19-29-34l1 1z" class="C"></path><path d="M646 196c6-4 11-4 17-3 9 2 16 9 20 16l2 1c2 6 3 10 5 16l1 8c0 2 0 5-1 7 0 2-1 4-1 6-1 1-1 1-2 1-1-7 1-13 0-20v-1c-1-4-2-7-3-11-4-9-11-17-20-20-5-2-11-2-16 1l-2-1z" class="Y"></path><defs><linearGradient id="K" x1="151.881" y1="164.193" x2="159.619" y2="172.807" xlink:href="#B"><stop offset="0" stop-color="#616162"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#K)" d="M166 151c1 1 2 1 3 3l2 1v1 1l1 1h1c0 1 1 1 1 2v2 1c-1 1-1 2-3 3s-6 0-8 0c-1 0-2 0-2 1-1 1-1 0-1 1 0 5-4 8-7 11l-2 2h-1c0-2 2-4 3-5 2-4 4-7 5-12h0c1-4 0-7 0-11l8-2z"></path><path d="M163 165c-1-1-1-2-1-3 0-2 0-4 1-6v2h0c1 0 1-1 2-2s2-1 4 0c1 0 2 0 2 1l1 1h1c0 1 1 1 1 2v2 1c-1 1-1 2-3 3s-6 0-8 0v-1z" class="E"></path><path d="M169 156c1 0 2 0 2 1l1 1h1c0 1 1 1 1 2v2h-2l-1-1-1-2c-1 0-1 0-2-1h0-1v-1h2l-1-1h1z" class="I"></path><path d="M171 161l1 1h2v1c-1 1-1 2-3 3s-6 0-8 0v-1l1-1c0-1 1 0 0-1v-1c2 0 2 2 3 2h2c1-1 1-1 2-3z" class="C"></path><path d="M463 391l1 2c2 2 3 4 3 6 1 2 1 4 2 6h1l2-2 3-3c1 1 2 1 3 0h6v1c-3 0-5 0-7 2-1 1-2 1-2 2-1 1-2 2-2 3v2 1h-1c-1 2 1 3 1 5v4 4c1 0 2 0 3-1l1-1h3l2 1-3 2h-1l-3 3c0 4 1 6 2 10 1 2 2 5 2 8h1c0 1-1 2 0 3v3c-3-6-5-13-6-19l-10-29 2 2v-1l-1-3v-2c-1-3-2-5-2-9z" class="H"></path><path d="M464 393c2 2 3 4 3 6 1 2 1 4 2 6h1l2-2 3-3c1 1 2 1 3 0h6v1c-3 0-5 0-7 2-1 1-2 1-2 2-1 1-2 2-2 3v2 1h-1c-1 2 1 3 1 5v4l-3-12c0-1-2-2-3-2-1-4-2-8-3-13z" class="L"></path><defs><linearGradient id="L" x1="519.696" y1="882.117" x2="534.804" y2="923.383" xlink:href="#B"><stop offset="0" stop-color="#a2a19d"></stop><stop offset="1" stop-color="#c2c1c6"></stop></linearGradient></defs><path fill="url(#L)" d="M552 851v-1c1 0 1 0 1-1l1 1-30 62-6 14c-1 2-2 5-4 8l-4-9 1-3c1 2 2 4 2 5l39-76z"></path><defs><linearGradient id="M" x1="312.598" y1="495.58" x2="311.703" y2="457.437" xlink:href="#B"><stop offset="0" stop-color="#9d9d9d"></stop><stop offset="1" stop-color="#c0bfc0"></stop></linearGradient></defs><path fill="url(#M)" d="M309 451c0 1 0 2 1 3h0l1-2c1-1 1 0 2-1l1 1c0 1-1 3-1 5-3 15 0 30 7 43l-1 2c4 5 8 10 13 15v1 1c2 1 4 3 6 5 2 1 4 1 6 3h-2c-8-3-15-11-20-18-13-17-16-37-13-58z"></path><path d="M802 256v1c0 3-3 6-3 9l1-1 3-8 1 1-40 94-1-1 5-12c1-2 1-3 0-5 4-8 7-15 8-24 5-6 8-12 11-19s7-14 9-21l6-14z" class="Y"></path><path d="M160 224c6 5 11 9 18 11 3 1 5 3 6 6h1c0 3 0 4-2 6-3 2-5 4-8 6-2 2-4 4-7 5l2-1 4-4 2-2 2-2h1l4-4-1-1c1-2 0-2 1-4l-2-2-1 3c0 1 0 1-1 1-2-1-3 0-5-2 0-1-1-1-2-2 0 1 1 1 1 2v1h-1l-1-2h-1c-2 0-2-1-4-1v-3c-1-1-2-1-3-1h-1l-1 3c-1-1-2-1-2-1-1 0-2 1-2 2 0-2-2-3-3-4l-2-2 2-3h-3c1-1 2-2 4-3h1c2 0 3-1 4-2z" class="Q"></path><path d="M151 229c1-1 2-2 4-3h1c3 1 4 2 6 4-1 0-3-1-5 0h0c0 1 0 2-1 2l1 1c1 0 1 0 1 1h0c-2 0-2-2-3-1l-1 1-2-2 2-3h-3z" class="D"></path><path d="M160 224c6 5 11 9 18 11 3 1 5 3 6 6h1c0 3 0 4-2 6-3 2-5 4-8 6-2 2-4 4-7 5l2-1 4-4 2-2 2-2h1l4-4-1-1c1-2 0-2 1-4l-2-2c-2 0-2-1-3-2l-1 1h0c-1-2-2-2-3-3h-2c-1-1-2-2-3-2-2-1 0 1-2 0-1-1 0-1-1-1v1c-2-1-2-2-4-2-2-2-3-3-6-4 2 0 3-1 4-2z" class="C"></path><path d="M475 183c0-1 0-2-1-3l1-1c2 2 3 4 5 6 0 0 1 1 2 1l2 2c1 1 2 0 3 0v-2c2 1 3 2 5 3 5 4 10 5 13 11l2 3c-1 0-3 0-4 1-1 0-1 0-2-1l1-1-3-3-1-1c0-1 0-1-1-2l-3-2c-2-1-3-1-5-1h0c-1-1-1-1-2-1l-1 1h1l-2 2h0-1v1 1c1 2-1 4 0 6 0 1 2 3 3 5l-1 1c-1-1-2-3-3-4h-1v-1c0-1-1-1-1-2v-2c0 1 0 1-1 3 0 1 0 0 1 1v1c0 1 1 2 1 3l1 1v1l-1-1c-1-1-2-3-3-4 0 2 0 5-1 7-2-2-1-9-1-12v-2-5c1-1 2-3 4-3h2 4l1 1c1 0 2 0 3-1l-1-1h-7-7c-1 0-1-1-2-1 1-1 2-1 3-1-1-1-1-2-1-3l-1-1z" class="E"></path><path d="M499 199v-1c1-1 1-1 1-2l3 3c0 2 0 2-1 3l-3-3zm-23-15h1c1 0 1 1 1 1l2 2c1 1 2 1 3 2h-7c-1 0-1-1-2-1 1-1 2-1 3-1-1-1-1-2-1-3z" class="W"></path><path d="M483 205c0-2-1-4-2-6 0-1 1-3 1-4 1-2 1-3 3-4v1l-1 3v1 1c1 2-1 4 0 6 0 1 2 3 3 5l-1 1c-1-1-2-3-3-4z" class="a"></path><path d="M439 246l17 1v1 1 2 1c-1 1-1 1-2 1 1 0 2 0 2 1h-1-1-1c-2-1-3-1-6-1 0-1-1-1-2-2h-1c-3 1-8 0-11 1h-8-3l-5 2-6 1v-1c-1-1-2-3-2-4 1-1 3-1 5-1-1 0-2-1-2-2h6 14l2-1h5z" class="o"></path><path d="M456 251v1c-1 1-1 1-2 1 1 0 2 0 2 1h-1-1-1c-2-1-3-1-6-1 0-1-1-1-2-2h11z" class="I"></path><path d="M409 250l5-1c1 0 2 1 3 1l4 1 1 1-5 2-6 1v-1c-1-1-2-3-2-4z" class="r"></path><path d="M439 246l17 1v1 1h-1 0c-3 0-7-1-9 0h-6-14c4-1 9 0 12-1l1-2z" class="q"></path><path d="M434 246h5l-1 2c-3 1-8 0-12 1h-12c-1 0-2-1-2-2h6 14l2-1z" class="d"></path><path d="M484 195h1 0l2-2h-1l1-1c1 0 1 0 2 1h0c2 0 3 0 5 1l3 2c1 1 1 1 1 2l1 1 3 3-1 1c1 1 1 1 2 1v2c0 1 1 2 1 3s1 2 2 3c-6-1-15 1-20-3l1-1c-1-2-3-4-3-5-1-2 1-4 0-6v-1-1z" class="F"></path><path d="M497 196c1 1 1 1 1 2l1 1 3 3-1 1-1-1-3-3-1 1 3 3h-1c-1 0-1-1-2-1l-2-2v1l-1 1v1l1 2c2 0 2-1 4 1h0c-3 0-4 0-6-1-1-1-2-4-3-5 0-1 1-2 2-3h6v-1z" class="D"></path><path d="M484 195h1 0l2-2h-1l1-1c1 0 1 0 2 1h0c2 0 3 0 5 1l3 2v1l-6-3h-2c-2 2-3 3-3 5s0 4 1 6c3 2 8 4 12 4 1 0 2-1 3-2h0v-2l1 1c0 1 1 2 1 3s1 2 2 3c-6-1-15 1-20-3l1-1c-1-2-3-4-3-5-1-2 1-4 0-6v-1-1z" class="K"></path><path d="M423 270l5 2 1-1 9 3 8 2h2l6 2h1 2c-1 2-2 2-4 2h-1c-1 0-1 0-3 1 2 0 3 1 4 1s2 1 3 1h1c2 0 3 1 5 1v1h-2c0 1 0 1 1 2l-2 1-10-3-5-2-3-1c-4 0-7-2-10-3-5-1-11-3-15-5 0-1-1-2 0-3h2v1h1c1 0 2 1 3 1h2c-1-1-1-2-1-3z" class="r"></path><path d="M423 270l5 2 6 3h0l1 2-11-4c-1-1-1-2-1-3z" class="p"></path><path d="M453 282c1 0 2 1 3 1h1c2 0 3 1 5 1v1h-2c0 1 0 1 1 2l-2 1-10-3h1 1 2c-1-1-1-1-2-1l-1-1h3v-1z" class="m"></path><path d="M429 271l9 3 8 2h2l6 2h1 2c-1 2-2 2-4 2h-1c-1 0-1 0-3 1l-6-2-8-2-1-2h0l-6-3 1-1z" class="v"></path><path d="M454 278h1 2c-1 2-2 2-4 2h-1c-1 0-1 0-3 1l-6-2c2 0 3 0 5-1 1 0 2 0 3 1 2 0 2 0 3-1z" class="p"></path><defs><linearGradient id="N" x1="755.871" y1="368.797" x2="790.778" y2="387.932" xlink:href="#B"><stop offset="0" stop-color="#a1a0a1"></stop><stop offset="1" stop-color="#cfcecf"></stop></linearGradient></defs><path fill="url(#N)" d="M763 351l1 1-4 10c9-1 17 0 24 6 5 4 8 9 8 15 0 5-2 10-5 14-2 1-4 2-6 2-1 0-3 0-4-1v-1c1 0 1 0 2-1 2 0 3-1 4-2 2-2 2-5 1-8 1 1 1 2 2 4h0v-2c1-2 0-1 1-2v-1 3l1-1c0-2 0-4-1-7-1-5-5-9-10-12-5-2-12-3-17-1-2 0-4 1-6 2 0-2 1-3 2-4 0-1 1-1 1-1 2-1 2-3 3-5s2-5 3-8z"></path><defs><linearGradient id="O" x1="657.244" y1="700.426" x2="647.446" y2="672.182" xlink:href="#B"><stop offset="0" stop-color="#b7b5b5"></stop><stop offset="1" stop-color="#d4d5d8"></stop></linearGradient></defs><path fill="url(#O)" d="M632 710c10-3 19-10 24-20 5-8 5-20-1-28-1-2-4-4-4-6l2 1c5 5 9 12 10 19 1 9-1 17-7 25-2 3-5 5-8 7-2 3-8 5-11 5h-4l-1 1c-1-1-3-2-4-3h1c1-1 2-1 3-1z"></path><path d="M632 710h3c10-2 18-8 24-16h0c-2 3-5 7-7 10l-4 4h0c-2 3-8 5-11 5h-4l-1 1c-1-1-3-2-4-3h1c1-1 2-1 3-1z" class="I"></path><path d="M517 131c2 2 3 5 3 8-1 1-1 2-1 3s0 2-1 3v2c-1 0-1 0-2 2-1 0-1 0-2 1v1c-2 8-6 16-7 24h0c0-3 0-4-2-6l5-14c-2-1-3-1-5-1l1-2c1-1 0-3 0-5-2-1-5-2-6-5v-1c0-1 1-2 1-3 2 0 4 0 7 1 3-1 3-3 5-5v-1h2v-1-1h2 0z" class="D"></path><path d="M518 143v2 2c-1 0-1 0-2 2-1 0-1 0-2 1v-3c0-2 3-3 4-4z" class="Q"></path><path d="M506 147c2 1 4 1 5 1l-1 7c-2-1-3-1-5-1l1-2c1-1 0-3 0-5z" class="f"></path><path d="M517 131c2 2 3 5 3 8-1 1-1 2-1 3s0 2-1 3v-2l-1-7c-1-2-1-4-2-4v-1h2 0zm-16 10c2 1 3 2 5 2 3 1 5 1 7 1 0 2-1 3-2 4-1 0-3 0-5-1s-5-2-6-5l1-1z" class="g"></path><path d="M513 133h2c-1 2-1 7-2 9-3 1-5 1-7 0l-4-2c-1 0 0 0-1 1l-1 1v-1c0-1 1-2 1-3 2 0 4 0 7 1 3-1 3-3 5-5v-1z" class="Y"></path><path d="M579 351v-1c2 2 3 3 4 5l-6 17-26 76h-1l1-1v-1h0l1-1v-2h0v-1l1-1v-2h-1c-1 0-2 0-3-1l1-1c1 1 1 1 2 1 0-3-5-7-7-10l1-1v-1h1s1 1 1 2l2 1c1 1 2 1 3 1 2-2 3-6 4-8 0-2 1-3 2-5 0-3 1-5 2-7l5-16 3-6c1-3 1-6 2-9 1-2 2-4 3-7 0-2 1-5 1-8 1-1 1-3 1-4 1-2 2-4 2-6 1-1 1-2 1-3z" class="H"></path><path d="M148 144c7 0 14-1 20 0 3 0 6 2 8 2s6-2 8-2h0c1 0 3 0 4 1h0c1 1 1 2 2 2h1c-2 4-2 8-1 12l2 1v1h-1c0 1 0 1-1 2h-1l-1 1-1-1h-1v2c-1 1-2 1-3 1v-1c-1-1-2-3-2-4l-2-4h0v-5l1-2h0l1-1h-1l-3 1c-1 0-3 0-4-1-5-2-10-1-14-1-2 0-5 0-7-1-1-1-1-1-2-1l-2-2z" class="b"></path><path d="M179 157c2 1 3 2 4 3 2 1 4 1 6 3l-1 1-1-1h-1v2c-1 1-2 1-3 1v-1c-1-1-2-3-2-4l-2-4z" class="W"></path><path d="M179 157c2 1 3 2 4 3v5c-1-1-2-3-2-4l-2-4z" class="F"></path><path d="M191 161c-1-2-2-2-4-3-2-2-3-4-3-6 0-3 2-5 4-7 1 1 1 2 2 2h1c-2 4-2 8-1 12l2 1v1h-1z" class="Y"></path><path d="M166 238c2 0 2 1 4 1h1l1 2h1v-1c0-1-1-1-1-2 1 1 2 1 2 2 2 2 3 1 5 2 1 0 1 0 1-1l1-3 2 2c-1 2 0 2-1 4l1 1-4 4h-1l-2 2-2 2-4 4v-4h-1c-1 0-2 1-3 2l-2-1c-3 0-4-1-6-2-1-1-3-2-3-4 0-4 0-3 3-6 1-1 2-2 4-3 1-1 3-2 4-1z" class="R"></path><path d="M170 253v-1-1h2l-2-2c0 1-1 2-2 3h-1v-2h2v-1c-1 0-1 1-3 0 0 1 0 0-1 1 0 1 0 1-1 1l-2-1-1-1c-2-1-2-2-2-3 1-2 2-4 4-5 3 0 4 0 6 1 1 1 2 3 2 4l-2 2h0 2c1 0 1-2 3-2l1 1c1 0 2 1 3 2l-2 2-2 2-4 4v-4z" class="X"></path><path d="M855 262c-3-2-5-5-7-7-2-1-3-2-5-3-4-2-7-5-11-8l40-24 1 1c-2 2-6 4-8 6l-4 2 1 2c-4 2-9 5-13 7-2 2-4 3-5 5l1 1v1l1-2h0c1 1 1 2 1 3s1 2 2 2v1h-2l6 4 6 5 2 2 4-1h0v3c1 1 1 2 1 3 2 1 3 1 5 2l2 1v1h-1c-1-1-1-1-3-1l2 3-1 1c-1-3-3-4-5-5v1l-10-6z" class="h"></path><path d="M838 246l-1-1c1-3 8-6 10-7l14-9 1 2c-4 2-9 5-13 7-2 2-4 3-5 5l1 1v1l1-2h0c1 1 1 2 1 3s1 2 2 2v1h-2c-2 0-3 0-5 1-2-1-3-2-4-4z" class="K"></path><path d="M844 243l1 1v1l1-2h0c1 1 1 2 1 3s1 2 2 2v1h-2c-2 0-3 0-5 1-2-1-3-2-4-4 1 0 4 0 5 1l1 1 1-1c-1-1-1-2-1-3v-1z" class="N"></path><path d="M842 250c2-1 3-1 5-1l6 4 6 5 2 2 4-1h0v3c1 1 1 2 1 3 2 1 3 1 5 2l2 1v1h-1c-1-1-1-1-3-1l2 3-1 1c-1-3-3-4-5-5v1l-10-6v-1c0-2 1-3 0-4h-2c0-1-1-2-2-3l-1 1-8-5z" class="i"></path><path d="M865 259h0v3c1 1 1 2 1 3-2-1-4-3-5-5l4-1z" class="R"></path><path d="M855 261l2-2 1 1c0 1 1 1 2 2 1 0 2 1 3 2l2 3v1l-10-6v-1z" class="X"></path><path d="M440 460l1 3c1-1 1-1 1-2 1 2 2 5 3 6 2 3 6 6 9 8 6 5 10 11 12 17 1 2 2 4 2 6h-1l-1-2v2l-1 1h-1-2v-3c0-2-1-3-2-4h-5c-1 0-3-1-5-1 0-1-1-1-2-2v1c1 2 2 3 3 5h-1c-1-1-2-3-3-4l-1-1-2-2h4l1-2v-2c0-1-1-2-1-2-1-2-1-2-2-3-2-1-2-1-3-2s-2-1-2-1l-1-1h2 2l-1-1v-1-1-1c-1-1-1-2-1-3-2-3-3-5-2-8z" class="P"></path><path d="M450 488c0-2 1-3 2-4l1 1h1v3h-4z" class="D"></path><path d="M440 460l1 3 4 10c2 3 4 5 6 8 1 1 2 2 3 4h-1l-1-1c-1 1-2 2-2 4h-2l1-2v-2c0-1-1-2-1-2-1-2-1-2-2-3-2-1-2-1-3-2s-2-1-2-1l-1-1h2 2l-1-1v-1-1-1c-1-1-1-2-1-3-2-3-3-5-2-8z" class="I"></path><path d="M445 473c2 3 4 5 6 8-2-1-4-1-6-4-1-1-1-2 0-4z" class="B"></path><path d="M460 492v-1c-2-6-5-11-10-15-2-1-3-3-4-4v-1c2 2 4 4 6 5 1 1 3 2 4 3 5 5 8 11 10 17v2l-1 1h-1-2v-3c0-2-1-3-2-4z" class="J"></path><path d="M446 344c3 3 5 7 8 11 1 2 2 3 3 4 4 6 9 12 14 17-2 1-4 2-5 3l-4 4v1 1c0 2 1 4 1 6 0 4 1 6 2 9v2l1 3v1l-2-2c-1-2-2-6-2-8l-9-27-4-16-3-9z" class="L"></path><path d="M449 353v-4c0 1 1 2 1 3 1 2 3 4 4 7 1 1 1 3 1 5l7 19v1 1c0 2 1 4 1 6 0 4 1 6 2 9v2l1 3v1l-2-2c-1-2-2-6-2-8l-9-27-4-16z" class="b"></path><defs><linearGradient id="P" x1="392.763" y1="348.345" x2="400.902" y2="344.326" xlink:href="#B"><stop offset="0" stop-color="#9c9e9d"></stop><stop offset="1" stop-color="#c5c3c5"></stop></linearGradient></defs><path fill="url(#P)" d="M374 297v-1c1-2-4-6-2-8 0 1 1 1 1 2v1l3 5h1c1 2 3 4 5 6 0 1-1 1-1 2v3c4 10 9 19 13 28s7 18 11 26c1 3 4 5 5 8 1 1 0 3 1 4 0 2 0 4 1 6 2 6 6 12 8 18-1 2 0 3 1 4v1c1 2 0-1 0 2l1 1-1 1c-1-2-1-5-3-6-2-3-3-6-4-9l-9-22-6-13-6-14-15-36c-1-3-3-6-4-9z"></path><path d="M578 354c0 2-1 4-2 6 0 1 0 3-1 4 0 3-1 6-1 8-1 3-2 5-3 7-1 3-1 6-2 9l-3 6-5 16c-1 2-2 4-2 7-1 2-2 3-2 5-1 2-2 6-4 8-1 0-2 0-3-1l-2-1c0-1-1-2-1-2h-1v1l-1 1-1-1v-2l2 1c2 0 3-2 5-3s3-4 4-6h0c1-1 1-2 2-3 0-1 1-2 2-3 2-4 2-6 3-11h0l-1-1-1 1-2 2h-1c1-3 2-5 1-8 0 0-1 0-2-1h-2v-2h4c2-1 3-2 4-4s1-3 0-6c1-1 2-1 3-1 1-1 1-2 2-3l1 1c1-1 2-3 3-4 0-2 1-6 2-8l-1-1h-2c-2 1-3 2-4 3l-1-1c4-3 9-8 13-13z" class="O"></path><defs><linearGradient id="Q" x1="569.525" y1="384.956" x2="555.7" y2="389.637" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#656665"></stop></linearGradient></defs><path fill="url(#Q)" d="M567 377l1 1c1-1 2-3 3-4 0 3-2 6-3 8-1 5-2 10-4 15-1 1-1 2-2 3h0l-1-1-1 1-2 2h-1c1-3 2-5 1-8 0 0-1 0-2-1h-2v-2h4c2-1 3-2 4-4s1-3 0-6c1-1 2-1 3-1 1-1 1-2 2-3z"></path><path d="M581 305c0-2 1-3 2-4 1-2 2-4 4-4 3 4 7 6 9 10l1 1h0l-2 2c-1 5-3 13-7 16l-2 1-2 2h-1c-2-5-4-8-5-13 0-2 0-5 1-7l2-4z"></path><path d="M581 305l1 1c-2 5-3 11-1 16 1 2 3 3 5 5l-2 2h-1c-2-5-4-8-5-13 0-2 0-5 1-7l2-4z" class="M"></path><path d="M581 305c0-2 1-3 2-4 1-2 2-4 4-4 3 4 7 6 9 10l1 1h0l-2 2c-3-3-6-6-10-7-1 1-2 1-3 3l-1-1z" class="P"></path><path d="M157 238c0-1 1-2 2-2 0 0 1 0 2 1l1-3h1c1 0 2 0 3 1v3c-1-1-3 0-4 1-2 1-3 2-4 3-3 3-3 2-3 6 0 2 2 3 3 4 2 1 3 2 6 2l2 1c1-1 2-2 3-2h1v4l-2 1c-2 3-4 4-6 6-2 1-3 2-4 3-2-1-2 0-4 0l-1-1c-1-1-1-1-2-1h-1c-1-1-1-2-1-3-1-1 0-1-1-2v-1h-1c-1-2-2-3-3-4v-2 1h1l2 4h1l-1-1v-1-2c0-1 1-1 2-2-1-1-1-1-1-2s0-1-1-1v-6l2-1c1 1 0 1 1 0v-1c1 1 1 2 2 2l1 1v1-1l2-2v-2l-1-1 1-1 1 1 1-1z" class="O"></path><path d="M166 255c1-1 2-2 3-2h1v4l-2 1c-2 1-4 0-5-1 0-1 0-1 1-2h2 0z" class="g"></path><path d="M157 238c0-1 1-2 2-2 0 0 1 0 2 1l1-3h1c1 0 2 0 3 1v3c-1-1-3 0-4 1-2 1-3 2-4 3-3 3-3 2-3 6 0 2 2 3 3 4 2 1 3 2 6 2l2 1h0-2c-1 1-1 1-1 2-3-1-5-2-7-3l-1 2c-1 0-1 0-2 1 0 1-1 1-1 2h-1c0-2 3-3 3-5-1 0 0 0-1-1v-2l2 1v-1l-3-3-2-2c0-1 0-1 1-2l2 1v-1l2-2v-2l-1-1 1-1 1 1 1-1zm330-52c-7-6-13-13-17-21-1-2-2-4-2-6s0-4 2-5h0c2 3 4 7 7 9 5 6 13 10 19 16l-1 1 3 3c1 2 2 3 3 4h0c-1 0-2-1-3-2l-9-6c-3-2-5-4-8-4l10 8c5 3 14 8 16 14 0 1-1 3-2 3-3-6-8-7-13-11-2-1-3-2-5-3z" class="X"></path><path d="M492 189v-1c-1-1-1-1-2-1l-3-3-6-6c4 1 6 3 8 5h1 1c5 3 14 8 16 14 0 1-1 3-2 3-3-6-8-7-13-11z" class="M"></path><path d="M481 175c-5-5-9-11-12-18 3 3 5 6 7 9 6 5 13 9 19 14l3 3c1 2 2 3 3 4h0c-1 0-2-1-3-2l-9-6c-3-2-5-4-8-4z" class="S"></path><path d="M570 191c0 1 1 3 2 4h1v1l1 1 2 2c3 1 6 2 9 4l-2 2c2 1 5 2 7 3v2s-1 1-2 1h0c-1 1-2 1-2 2-1 0-3-1-4-2-3-1-5-1-8-2h-4c-1 2-1 3 0 6 0 2 1 3 3 5l-1 1-4-5c-1-2-2-5-3-7-1-1-1-2-1-2l-2-2v-1c-1 0-2-1-3-2s0-3-2-5h0l1-2 7-2 1-1 4-1z" class="l"></path><path d="M572 195h1v1l1 1 2 2-5-1-1 1h-6c-1-1-2-1-3-1l-1 1v-1-1c2 1 3 0 5 0h6v-1l1-1z" class="i"></path><path d="M570 191c0 1 1 3 2 4l-1 1v1h-6v-4l1-1 4-1z" class="Q"></path><path d="M566 192l1 1v2l1 1c1-1 2 0 3 0v1h-6v-4l1-1z" class="K"></path><path d="M572 206c2 0 4 0 6 1 4 1 7 2 10 4-1 1-2 1-2 2-1 0-3-1-4-2-3-1-5-1-8-2h1l-2-2-1-1z" class="V"></path><path d="M562 200l2 1 1-1v1c0 1 0 2 1 3h1s1 1 2 1h0l1-1-1-1c1 1 1 1 2 1 0 1 1 1 1 2l1 1 2 2h-1-4c-1 2-1 3 0 6 0 2 1 3 3 5l-1 1-4-5c0-3 0-7-2-9h0c-1-2-3-4-4-5v-2z" class="N"></path><path d="M565 193v4c-2 0-3 1-5 0v1 1l2 1v2c1 1 3 3 4 5h0c2 2 2 6 2 9-1-2-2-5-3-7-1-1-1-2-1-2l-2-2v-1c-1 0-2-1-3-2s0-3-2-5h0l1-2 7-2z" class="a"></path><path d="M478 425h1c0 2 0 4 1 6h4 2c-1 2-1 3-1 5 2 1 4 1 6 1l3 3v1l1 2-1 3-2 3v1c0 1 0 2-1 3h-1c-1 2-3 6-3 8l1 1 3-2c-1 3-7 3-5 6 0 1 0 2 1 3l1 3c0 1 0 1 1 3v1h-1s-1-2-1-3l-3-10-1-2-3-8v-1-3c-1-1 0-2 0-3h-1c0-3-1-6-2-8-1-4-2-6-2-10l3-3z" class="B"></path><path d="M480 443l2-2h1v1l2 1v1h-2v4c-1-1-2-2-3-4v-1z" class="C"></path><path d="M484 431h2c-1 2-1 3-1 5 2 1 4 1 6 1l3 3v1c-1 0-2-1-3-1v-1h-6c-1 0-2 1-2 1h-1c0-1 1-1 1-2v-3l-1 1-1-1c1-1 2-3 3-4z" class="U"></path><path d="M485 444l1 1h0c0-1 0-2 2-3v1h0c1 1 1 1 1 2h1l2-2c2 1 2 1 2 3l-2 3-2 1h-2c-3 0-3 0-5-2v-4h2z" class="I"></path><path d="M480 446h-1c0-3-1-6-2-8-1-4-2-6-2-10l3-3c0 2-1 4-1 6v1c0 1 1 2 1 4 1 2 1 5 2 7v1c1 2 2 3 3 4 2 2 2 2 5 2h2l2-1v1c0 1 0 2-1 3h-1c-1 2-3 6-3 8l1 1 3-2c-1 3-7 3-5 6 0 1 0 2 1 3l1 3c0 1 0 1 1 3v1h-1s-1-2-1-3l-3-10-1-2-3-8v-1-3c-1-1 0-2 0-3z" class="E"></path><path d="M490 450l2-1v1c0 1 0 2-1 3h-1c-1 2-3 6-3 8l1 1h-1 0c-1-1-1-7-1-8 0-2 3-3 4-4z" class="D"></path><path d="M480 446c2 3 3 5 3 8 1 2 1 3 1 5h0c1 1 1 2 2 3h1 1l3-2c-1 3-7 3-5 6 0 1 0 2 1 3l1 3c0 1 0 1 1 3v1h-1s-1-2-1-3l-3-10-1-2-3-8v-1-3c-1-1 0-2 0-3z" class="C"></path><path d="M557 175c2-1 6-2 9-2l1 1-1 1c0 2-1 3 0 5 2 1 4 0 7 0 4 0 13 2 16 6 1 0 1 1 1 1v1l-1-1c-4-2-7-1-11-3v1c-2 1-3 2-4 3v2l-1 2v2c-2-1 0-3-2-4l-1 1-4 1-1 1-7 2c-1 0-1 0-2-1h-2c0-1-1-2-1-3-1 0-2 0-3 1-1-1-2-2-2-3 3-1 6-2 7-4s0-4-1-6h-1v-1c1-1 2-2 3-2l1-1z" class="Y"></path><path d="M556 176c2 1 3 2 4 4 0 2 0 5 1 7 1-1 2-1 2-2 0-2 0-5-1-6 0-2 0-2 1-3l1-1v1c0 3 0 5 1 8-2 2-4 4-7 5-1 1 0 1-1 1-2 0-3 0-4 1-1 0-2 0-3 1-1-1-2-2-2-3 3-1 6-2 7-4s0-4-1-6h-1v-1c1-1 2-2 3-2z" class="F"></path><path d="M557 190c-1 0-1 1-1 2 2-2 6-2 8-3 3-2 3-4 6-5h1l1-1c2-1 4 0 6 1v1c-2 1-3 2-4 3v2l-1 2v2c-2-1 0-3-2-4l-1 1-4 1-1 1-7 2c-1 0-1 0-2-1h-2c0-1-1-2-1-3 1-1 2-1 4-1z" class="N"></path><path d="M498 177c1-5 1-10 1-15s-3-3-6-5v-2l3-3c1-1 4-1 6 0h3 1l-1 2c2 0 3 0 5 1l-5 14c2 2 2 3 2 6h0l2 4h0c2 2 2 2 3 4 0 2 1 2 0 3 1 1 2 1 3 3l1 1h2l-3 2h-1l-3-3c-2 0-3 0-5 1-1-2-3-3-4-4l-1 1c-1-1-2-2-3-4l-3-3 1-1 2-2z" class="Y"></path><path d="M496 179l2-2c0 2 2 4 4 6h-4l-3-3 1-1z" class="B"></path><path d="M502 183l9 6c-2 0-3 0-5 1-1-2-3-3-4-4l-1 1c-1-1-2-2-3-4h4z" class="E"></path><path d="M500 170c0-4 1-9-1-12-2-1-3-1-5-2 1-1 1-1 2-1l2-2c1 1 2 2 3 2v1c0 1 1 1 1 2 0 3 1 5-1 7-1 2 0 3-1 5z" class="N"></path><path d="M500 170c1-2 0-3 1-5 2-2 1-4 1-7 0-1-1-1-1-2v-1c2 1 3 2 4 4l-3 13c-1 2-1 4 0 6v2 1l-2-4v-5-2z" class="i"></path><path d="M505 169c2 2 2 3 2 6h0l2 4h0c2 2 2 2 3 4 0 2 1 2 0 3-2-1-5-3-6-5v-1l-1-1c-2-4-1-7 0-10z" class="E"></path><path d="M509 179h0c2 2 2 2 3 4-1 0-2 0-2-1-2 0-2-1-2-2l1-1z" class="I"></path><path d="M203 213c2 1 2 1 3 2 1 2 3 4 4 6s3 4 4 6c3 4 5 8 8 12l9 20h0l4 9 1 3 14 31 3 9c1 1 1 3 2 4s5 4 6 5l2 2c-1 0-2 0-2 1-1 3 2 7 3 10 0 3 0 4 1 7h1v3c-1-1-1-2-2-4 0-1-1-4-2-5v-1c-1 0-1-1-1-2h-1c-1-3-2-7-4-10 0-2-3-3-3-5-1-2-2-5-3-8l-25-53c-4-9-8-19-14-27-2-3-5-8-9-9h-1c0-2 1-3 1-4s1-1 1-2z" class="g"></path><path d="M672 567c0 3-1 5-1 7 0 1-2 5-2 6s0 1 1 2c-2 6-5 12-8 19l-13 29c-3 5-6 10-8 15l-3 9-5 12c-3 8-7 16-10 24-1 3-1 6-2 9s-3 5-5 6c0 0-1-1-1-2l3-8c2-7 3-13 7-19l47-109z" class="P"></path><defs><linearGradient id="R" x1="625.642" y1="685.196" x2="615.1" y2="695.558" xlink:href="#B"><stop offset="0" stop-color="#454748"></stop><stop offset="1" stop-color="#6d6d6c"></stop></linearGradient></defs><path fill="url(#R)" d="M615 703l3-8c2-7 3-13 7-19 0 5-2 9-4 14-1 4 0 10-3 13h-3z"></path><path d="M460 520h3l5 13c5 10 8 23 17 30 0 2 0 6 1 8 1 5 4 11 6 16 7 15 14 30 20 45-1 0-1-1-2-1h0c-1-1-2-2-3-4s-2-4-3-7l-7-14-31-70-6-16z" class="Y"></path><path d="M460 520h3l5 13c-1-1-1-1-1-2-1-1-1-2-1-2l-2-4c-1 2-1 2 0 3l1 3c1 2 1 3 1 5l-6-16z" class="N"></path><defs><linearGradient id="S" x1="790.248" y1="289.764" x2="778.221" y2="279.7" xlink:href="#B"><stop offset="0" stop-color="#333435"></stop><stop offset="1" stop-color="#575555"></stop></linearGradient></defs><path fill="url(#S)" d="M786 269c0-1 0-2 1-2h0v-1-2l-1-2 2 1c1 1 2 2 3 2 3 0 4-1 5-3 1 3-2 4 0 8-2 7-6 14-9 21s-6 13-11 19v-1l-1 1h-2l-1 2-2-1v1h-1c0-1 0-3 1-5s1-4 0-7l5-9 1-1c4-4 7-7 9-13v-1c0-2 0-2-1-4l-2 1-1-1h1 2 0c1-1 2-2 2-3z"></path><path d="M785 277l1 1c1 2 0 3 0 5-1 2-2 5-2 7 0 3-9 16-11 19l-3 2v1h-1c0-1 0-3 1-5s1-4 0-7l5-9 1-1c4-4 7-7 9-13z" class="b"></path><path d="M516 149c1-2 1-2 2-2 1 4 3 9 5 13v1c2 4 3 7 4 10 1 5 0 6-2 10-2 2-3 4-5 5s-4 2-5 3c-1-2-2-2-3-3 1-1 0-1 0-3-1-2-1-2-3-4h0l-2-4c1-8 5-16 7-24v-1c1-1 1-1 2-1z" class="k"></path><path d="M516 149c1-2 1-2 2-2 1 4 3 9 5 13v1 1 3c1 1 1 1 1 2-1 0-1 0-1 1v-1c-1-1-1-1-1-2h-1c-2-6-4-10-5-16z" class="J"></path><path d="M523 161c2 4 3 7 4 10 1 5 0 6-2 10-2 2-3 4-5 5v-2l3-3c0-1 0-3-1-4 0-1 0-2 1-3 0-3-1-6-2-9h1c0 1 0 1 1 2v1c0-1 0-1 1-1 0-1 0-1-1-2v-3-1z" class="K"></path><path d="M514 151c0 1 1 7 0 8-2 2-3 3-3 5v1 6c3 0 5-1 8 1 0 1 0 1 1 1l-1 1c-2 0-3-2-5-2-1 1-2 2-3 2v1c0 1-1 1-1 2v2h-1 0l-2-4c1-8 5-16 7-24z" class="N"></path><path d="M509 179h1v-2c0-1 1-1 1-2v-1c1 0 2-1 3-2 2 0 3 2 5 2l1-1v5c-1 1-1 2-2 2h-1v-2-1l-2 1v2l-1 1c-1-2-1-2-1-4h2v-1h-2c-1 0-1 0-1 2l1 3v1l4 1c1-2 3-4 5-6 1 1 1 3 1 4l-3 3v2c-2 1-4 2-5 3-1-2-2-2-3-3 1-1 0-1 0-3-1-2-1-2-3-4z" class="D"></path><path d="M513 182l4 1v1c-1 1-1 2-2 2-2-2-2-2-2-4z" class="E"></path><path d="M481 175c3 0 5 2 8 4l9 6c1 1 2 2 3 2h0l1-1c1 1 3 2 4 4 2-1 3-1 5-1l3 3h1l3-2c1 0 2-1 3-2h4 0c-1 0-1 1-2 1v1c1 0 1 1 1 2-1 1-2 3-3 5v1l-1 4 1 1 2-5c0-1 1-3 2-4l1 1-1 2v1c0 1-1 1-1 3l-1 1v2l-2 4v3c0 2-1 4-2 6l-1 1c-3 1-7-2-10-2h-1v-1-1-1l-1-1c-1-1-2-2-2-3s-1-2-1-3v-2c1-1 3-1 4-1l-2-3c1 0 2-2 2-3-2-6-11-11-16-14l-10-8z" class="O"></path><path d="M507 197c2 5 3 12 4 17h-1l-3-11-2-3c1 0 2-2 2-3z" class="P"></path><path d="M511 189l3 3c-1 2 1 2 2 4-1 0-2 0-3 1 0 0 0 1-1 1h0c-1-1-1-2-1-3l-1-1c-1-2-2-3-4-4 2-1 3-1 5-1z" class="C"></path><path d="M503 204c1-1 3-1 4-1l3 11v1l-3-1v-1l-1-1c-1-1-2-2-2-3s-1-2-1-3v-2z" class="S"></path><path d="M518 190c1 0 2-1 3-2h4 0c-1 0-1 1-2 1v1c1 0 1 1 1 2-1 1-2 3-3 5l-1 1c-1 3-2 6-2 10 0 1 0 3-1 4-1-1-1-2-2-3v-2l1-1v-3c0-1-1-1-1-2-1-1-1-2 0-3s1-1 2-1l1-1c0-2 1-2 0-4h0l-1 1c-1 0-1 0-2-1l3-2z" class="L"></path><path d="M521 198l-1 4 1 1 2-5c0-1 1-3 2-4l1 1-1 2v1c0 1-1 1-1 3l-1 1v2l-2 4v3c0 2-1 4-2 6l-1 1c-3 1-7-2-10-2h-1v-1-1l3 1v-1h1l1 1h1l2-1c0-2-1-4-1-6v-1l-2-5h0l1-1h0c0 1 0 1 1 2 0 2 1 3 1 4v2c1 1 1 2 2 3 1-1 1-3 1-4 0-4 1-7 2-10l1-1v1z" class="K"></path><path d="M550 192c1-1 2-1 3-1 0 1 1 2 1 3h2c1 1 1 1 2 1l-1 2h0c2 2 1 4 2 5s2 2 3 2v1l2 2s0 1 1 2c1 2 2 5 3 7l4 5 1-1h0c4 4 8 2 13 3 0 1-1 2-2 3-4 2-11 3-16 2s-10-4-15-7l-2-7v-1l-1-1v-2-1l-1-4h0c1-4 1-8 1-11v-2z" class="c"></path><path d="M558 217h1 1c2 2 4 3 5 5l-2 1c-2-2-4-3-5-4v-2z" class="E"></path><path d="M560 217l3-1 2 1c3 4 6 6 9 8-1 1-2 1-3 0h-1c-2 0-3-2-5-3-1-2-3-3-5-5z" class="k"></path><defs><linearGradient id="T" x1="560.323" y1="213.145" x2="576.177" y2="217.855" xlink:href="#B"><stop offset="0" stop-color="#1b191b"></stop><stop offset="1" stop-color="#424442"></stop></linearGradient></defs><path fill="url(#T)" d="M562 211c-1-2-1-4 0-6l2 2s0 1 1 2c1 2 2 5 3 7l4 5c2 1 4 2 7 3h0c1 1 1 1 2 1l-1 1-1-1h-4-1c-3-2-6-4-9-8l-3-6z"></path><path d="M550 210h1c1 0 1 0 2-1h1c0 1 0 1 1 1v-1c0-2 1-2 2-4l1-1 1 1v3h1c1 1 1 1 2 3l3 6-2-1-3 1h-1-1c0-1 0-2-1-3 0-1 0-1-1-1v2 3c0 1-1 1-1 2-2-1-1-2-1-3s0-2 1-3l-1-1-1 1-1-1v-2h-1v2l-1-1v-2z" class="Z"></path><path d="M559 217c0-2-1-3-1-4l1-1 4 4-3 1h-1z" class="N"></path><path d="M550 192c1-1 2-1 3-1 0 1 1 2 1 3h2c1 1 1 1 2 1l-1 2h0c2 2 1 4 2 5s2 2 3 2v1c-1 2-1 4 0 6-1-2-1-2-2-3h-1v-3l-1-1-1 1c-1 2-2 2-2 4v1c-1 0-1 0-1-1h-1c-1 1-1 1-2 1h-1v-1l-1-4h0c1-4 1-8 1-11v-2z" class="J"></path><path d="M550 194c1 0 2 1 3 2l-1 1 1 3c0 3-1 5-2 8l-1 1-1-4h0c1-4 1-8 1-11z" class="B"></path><path d="M177 150l3-1h1l-1 1h0l-1 2v5h0l2 4c-1 0-1 1-1 1 1 2 1 2 1 4-1-1-1-3-2-4h-2c1 3 3 5 3 7v2c-3 3-8 5-12 7l-5 2c-4 1-7 2-10 4l-20 28h-1l2-4-1-2c3-4 5-10 8-14 1-1 2-3 2-5 1-1 1-1 0-2h4v-1c-2 0-2-1-3-2l1-1c2 0 2 0 4 1l1-1h1l2-2c3-3 7-6 7-11 0-1 0 0 1-1 0-1 1-1 2-1 2 0 6 1 8 0s2-2 3-3v-1-2c0-1-1-1-1-2h-1l-1-1v-1-1l-2-1c-1-2-2-2-3-3h2c1 1 2 1 3 1 1 1 2 0 4 1h1c0-1 0-2 1-2v-1z" class="c"></path><path d="M145 181c2 0 2 0 4 1v1 1h-2c-2 0-2-1-3-2l1-1z" class="g"></path><path d="M143 185h4 2v1l-1 1h0c0 1-1 2-2 3-1 0-2 1-4 2h-1c1-1 2-3 2-5 1-1 1-1 0-2z" class="d"></path><path d="M141 192h1c2-1 3-2 4-2l-9 14-3 4-1-2c3-4 5-10 8-14z" class="f"></path><path d="M177 150l3-1h1l-1 1h0l-1 2v5h0l2 4c-1 0-1 1-1 1 1 2 1 2 1 4-1-1-1-3-2-4h-2c1 3 3 5 3 7v2c-3 3-8 5-12 7l-5 2c-4 1-7 2-10 4 1-3 4-3 5-5-3 1-5 3-7 5l-1-1c2-2 8-7 11-8h2l1-1c1-1 4-3 6-3 2-1 4-2 5-3h0c1-2 1-4 0-6h0l-1 1v-1-2c0-1-1-1-1-2h-1l-1-1v-1-1l-2-1c-1-2-2-2-3-3h2c1 1 2 1 3 1 1 1 2 0 4 1h1c0-1 0-2 1-2v-1z" class="D"></path><path d="M164 174c1-1 4-3 6-3 2-1 4-2 5-3v1c0 1 0 1 1 2-4 2-8 3-12 3z" class="R"></path><path d="M177 162c-1 0-1-2-1-3-1-1-1-2 0-3h1l1-2h0l1 3h0l2 4c-1 0-1 1-1 1 1 2 1 2 1 4-1-1-1-3-2-4h-2z" class="l"></path><path d="M158 179c0-1 2-2 3-2 2 0 3-2 5-2 5 0 9-5 14-4-3 3-8 5-12 7l-5 2c-4 1-7 2-10 4 1-3 4-3 5-5z" class="R"></path><path d="M433 252c3-1 8 0 11-1h1c1 1 2 1 2 2 1 2 11 7 13 8l-1 1-3-2c-1-1-3-1-4-2h-1c-1 0-1 0-2-1h-2l-9-3-1 1c1 1 1 1 2 1h1 1l1 1v1c-1 0-1 1-1 2s0 2 1 3l-1 1h0v2h-3l3 1-1 3-2 4-9-3-1 1-5-2h-7l-2-4c3-3 0-6 1-9 1-1 0-2 2-2v-1l5-2h3 8z" class="W"></path><path d="M435 258h3 2-1l-1 1c1 2 2 3 3 5v2h-3s-2-1-3-1l1-2h3v-1-1c-1-1-2-2-4-3z" class="I"></path><path d="M434 258h1c2 1 3 2 4 3v1 1h-3 0-1v1h-1c-1-1 0-3-1-4v-1l1-1z" class="B"></path><path d="M422 252h3 8 4l1 1h0c-5 1-10-1-15 0h-1c-1 0-3 1-4 2 0 2 0 4 1 6 1 1 1 0 0 1v1c-1-3-2-5-2-8v-1l5-2z" class="b"></path><path d="M440 270l-2-1c-1-1-3-1-4-2-2-1-2-1-3-3l-2-2c0-1 0-2 1-3l2-1h2l-1 1v1c1 1 0 3 1 4h1v-1h1 0l-1 2c1 0 3 1 3 1l3 1-1 3z" class="L"></path><path d="M414 266c3-3 0-6 1-9 1-1 0-2 2-2 0 3 1 5 2 8l3 6 7 2-1 1-5-2h-7l-2-4z" class="n"></path><path d="M442 263c-1-1-1-2-1-3s0-2 1-2v-1l-1-1h-1-1c-1 0-1 0-2-1l1-1 9 3h2c1 1 1 1 2 1h1c1 1 3 1 4 2l3 2 1-1c5 2 9 7 14 10v1 2h-1v1h1c1 0 1 0 2 1l-1 1-2-1h-2c0 1 2 0 2 2-1 0-1 1-2 1h0c1 0 3 1 4 1s2 0 2 1c-5 0-11-1-16-2-2 0-3-1-4-1h-2-1l-6-2h-2l-8-2 2-4 1-3-3-1h3v-2h0l1-1z" class="E"></path><path d="M448 273h1c1 0 1 0 2-1 0 1 1 2 2 3h0 1c1 1 1 1 1 2v1h-1l-6-2v-1-2z" class="F"></path><path d="M442 263c2 2 3 6 6 7v1 2 2 1h-2l2-2c-1 0-1 0-2-1v-1l-1-1v-2c-2-1-2-2-4-3v-2h0l1-1z" class="C"></path><path d="M441 266c2 1 2 2 4 3v2l1 1v1c1 1 1 1 2 1l-2 2-8-2 2-4 1-3-3-1h3z" class="B"></path><path d="M441 266c2 1 2 2 4 3v2l1 1c0 2 0 2-1 3l-2-2v-1c-1-2-1-3-2-5l-3-1h3z" class="J"></path><path d="M447 257h2c1 1 1 1 2 1h1c1 1 3 1 4 2l3 2 1-1c5 2 9 7 14 10v1 2h-1v1h1c1 0 1 0 2 1l-1 1-2-1h-2c0 1 2 0 2 2-1 0-1 1-2 1h0c1 0 3 1 4 1s2 0 2 1c-5 0-11-1-16-2l-2-2c1 0 2-1 4-1l-1-1s-1 0-1-1c-2 0-3-1-4-1l1-1c-1-1-1-1-1-2h0 1c0-1 1-1 1-2h0v-2h0c-1-2-5-3-6-5-1-1-2-1-3-2l-3-1c-1 0 0 0-1-1h1z" class="C"></path><path d="M447 257h2c1 1 1 1 2 1h1c1 1 3 1 4 2l3 2h1c1 1 0 1 1 1 2 1 4 2 6 4l1 1c1 1 1 0 1 2 1 0 1 1 2 2 0 0-1 1-1 2h-3c-1-1 0-2 0-3v-1c0-1 0-1-1-2-1 0-3 2-4 3l-1 1-1-1 1-1c0-1-1-2-2-2h0v-2h0c-1-2-5-3-6-5-1-1-2-1-3-2l-3-1c-1 0 0 0-1-1h1z" class="O"></path><defs><linearGradient id="U" x1="861.493" y1="140.54" x2="854.818" y2="163.002" xlink:href="#B"><stop offset="0" stop-color="#151314"></stop><stop offset="1" stop-color="#343535"></stop></linearGradient></defs><path fill="url(#U)" d="M838 141l12 1c2 0 4-1 5 0h18c3 0 5 0 8 1h0 0-1-1l-1 1h0l1 1c-4 4-6 9-6 15 1 5 4 13 8 16 1 0 1 0 2 1h1c-1 1-1 1-2 1-1 1-2 1-2 2h-1c-1-2-1-3-3-5-1-4-4-7-5-11-1-1-3-3-5-4v1l-1-3v-2c-2-2-4-2-7-2-3 1-5 2-6 5l-1 1-1 4v-3-1h-1c0-3 0-6-1-8-1-3-2-4-4-5l-2 1v2h-1v2l-1-2c0-2-2-4-4-6l-1-1h1l2-2z"></path><path d="M838 141l12 1c2 0 4-1 5 0h18c3 0 5 0 8 1h0 0-1-1l-1 1h0c-5 1-11 0-16 0l-26-1 2-2z" class="g"></path><path d="M857 147l15 1c-2 6-2 10-1 16-1-1-3-3-5-4v1l-1-3v-2c-2-2-4-2-7-2 1-1 3-1 4-1-1 0-1 0-1-1 2 1 4 1 6 1l-1-2c-1-2-3-2-5-2v-1c-2 0-2 0-4-1z" class="a"></path><defs><linearGradient id="V" x1="851.901" y1="144.27" x2="858.291" y2="155.946" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#6b6b6c"></stop></linearGradient></defs><path fill="url(#V)" d="M844 147c3-1 10 0 13 0 2 1 2 1 4 1v1c2 0 4 0 5 2l1 2c-2 0-4 0-6-1 0 1 0 1 1 1-1 0-3 0-4 1-3 1-5 2-6 5l-1 1-1 4v-3-1h-1c0-3 0-6-1-8-1-3-2-4-4-5z"></path><path d="M579 351c0 1 0 2-1 3-4 5-9 10-13 13l1 1c1-1 2-2 4-3h2l1 1c-1 2-2 6-2 8-1 1-2 3-3 4l-1-1c-1 1-1 2-2 3-1 0-2 0-3 1 1 3 1 4 0 6s-2 3-4 4h-4-1c-2 1-5 0-6 1-2 1-2 1-4 1-2 2-4 4-6 5-1 1-2 3-3 4l-2 2c-2 1-3 2-3 4-2 0-2 0-3 1l-3-2v-1l-1-1c3-1 5-4 7-6l12-12 1-1c3-2 5-5 8-8s6-5 9-8l6-6c1-1 3-3 5-4l9-9z" class="c"></path><path d="M564 372v3c-1 1-1 1-3 1v-2l3-2z" class="N"></path><defs><linearGradient id="W" x1="533.192" y1="406.782" x2="532.779" y2="392.205" xlink:href="#B"><stop offset="0" stop-color="#1c1e20"></stop><stop offset="1" stop-color="#3f3d3d"></stop></linearGradient></defs><path fill="url(#W)" d="M523 406l7-5 6-6c3-2 7-6 9-9 1-1 3-4 4-5 2-1 2 0 3 0l-6 6c-1 2-2 3-2 5l-1 1c-2 2-4 4-6 5-1 1-2 3-3 4l-2 2c-2 1-3 2-3 4-2 0-2 0-3 1l-3-2v-1z"></path><path d="M565 367l1 1c1-1 2-2 4-3h2l1 1c-1 2-2 6-2 8-1 1-2 3-3 4l-1-1c-1 1-1 2-2 3-1 0-2 0-3 1h0c0-1 1-1 0-2 0-2 1-1 2-2v-2-3l-3 2c-4 1-6 4-8 5h-1c2-2 4-4 6-5 2-2 4-5 7-7z" class="J"></path><path d="M564 372c1-2 3-3 5-4 0 3 0 6-2 9h0c-1 1-1 2-2 3-1 0-2 0-3 1h0c0-1 1-1 0-2 0-2 1-1 2-2v-2-3z" class="Z"></path><path d="M552 381c1-1 2-1 4-2h4l2 2h0c1 3 1 4 0 6s-2 3-4 4h-4-1c-2 1-5 0-6 1-2 1-2 1-4 1l1-1c0-2 1-3 2-5l6-6z" class="C"></path><path d="M544 392l4-4v-1h2v1h0c1 1 1 2 3 3h0c-2 1-5 0-6 1-2 1-2 1-4 1l1-1z" class="Z"></path><path d="M458 504c1 2 1 3 0 5-2 3 1 8 2 11l6 16 31 70 7 14c1 3 2 5 3 7s2 3 3 4h0l-9-6c0-1 0-1-1-2h-1l-3-6-33-77-7-19c0-1-2-7-2-8l4-9z" class="b"></path><path d="M866 174c4-1 7 4 10 6 1 1 1 1 3 0h1c0-1 1-1 2-2 1 0 1 0 2-1h-1l4-1v2c0 1 0 1-1 2v3 1h0c4 5 8 12 11 18 1 3 3 13 5 14l6-1v1l-7 3 1 1c1 6 2 12 1 18h0c-2 2-2 6-2 8l-1 2-3 11c0 2-2 6-3 7h-1-1c2-5 4-10 5-16 1-2 2-5 1-6v-9c1-4 0-9-1-13s-2-8-4-11l-2-2c-1-4-4-9-7-12-2-2-5-5-8-6h-3l-4 2v-1c1-2 3-2 5-3h2l4 2-5-4h0c-2-3-6-5-9-6-2 0-3-1-4-2h2l-3-3c3 0 8 3 10 5 1 0 1 0 2 1 1 0 3 1 4 2-3-4-7-8-11-10z" class="X"></path><path d="M887 176v2c0 1 0 1-1 2v3 1 1c1 3 7 11 6 13h0l-3-4c0-1-1-2-1-3l-2-2c0-1 0-1-1-1-2-1-2-3-3-4v-2l-1 1c-1-1-2-2-2-3h1c0-1 1-1 2-2 1 0 1 0 2-1h-1l4-1z" class="M"></path><path d="M866 174c4-1 7 4 10 6 1 1 1 1 3 0 0 1 1 2 2 3l1-1v2c1 1 1 3 3 4 1 0 1 0 1 1l2 2c-1 1-1 1-1 2l1 3h0-1c0-1 0-1-1-2l-2-2c-1-2-3-3-4-5l-3-3c-3-4-7-8-11-10z" class="Y"></path><path d="M861 176c3 0 8 3 10 5 1 0 1 0 2 1 1 0 3 1 4 2l3 3c1 2 3 3 4 5l2 2c1 1 1 1 1 2l-1 1c1 1 2 1 3 2s0 0 0 2h1c2 3 3 6 5 9l-1 1v-1l-1 1-2-2c-1-4-4-9-7-12-2-2-5-5-8-6h-3l-4 2v-1c1-2 3-2 5-3h2l4 2-5-4h0c-2-3-6-5-9-6-2 0-3-1-4-2h2l-3-3z" class="M"></path><defs><linearGradient id="X" x1="265.779" y1="301.837" x2="230.721" y2="286.163" xlink:href="#B"><stop offset="0" stop-color="#2e2e2c"></stop><stop offset="1" stop-color="#444449"></stop></linearGradient></defs><path fill="url(#X)" d="M237 262c1-1 1-2 3-3h4l2 3v1c0 1 1 1 1 1v1c1 1 1 0 2 0 1 1 1 0 2 0v1l1 2c2 1 3 0 4 2v1c0 2 1 3 2 4h-2l1 2-1 2c1 1 3 0 5 0-1 1-2 3-2 4s1 2 1 3c-1 2-3 3-3 5l-1 1h-1c-2 0-4-4-6-5h-1l2 5c3 4 4 10 6 14 2 5 5 10 5 14-1-1-5-4-6-5s-1-3-2-4l-3-9-14-31-1-3-4-9 1 1c1 1 1 1 3 2 0-1 0-2 1-2l1 2z"></path><path d="M235 268c1-1 1-2 1-3h1c0 1 1 1 1 2l1-1 2 1h0c-1 1-2 1-3 1l-2 3-1-3z" class="B"></path><path d="M231 259l1 1c1 1 1 1 3 2 0-1 0-2 1-2l1 2c0 2 0 2 1 3l1 1-1 1c0-1-1-1-1-2h-1c0 1 0 2-1 3l-4-9z" class="E"></path><path d="M246 276h-2c-3-2-4-4-5-7v-1h4v1c1 1 0 2 0 3l1 1h1c1 1 2 2 2 3l2 1c0 1 0 1 1 2h-1l-1 1c-1-2-1-3-2-4z" class="Z"></path><path d="M246 276c1 1 1 2 2 4l3 6c2 2 4 4 5 6h-1c-2 0-4-4-6-5h-1l2 5c-1-1-1-1-1-2-2-1-4-7-4-9 0-1 1-3 1-5z" class="C"></path><path d="M252 279h1 1v-4h2l1 2-1 2c1 1 3 0 5 0-1 1-2 3-2 4s1 2 1 3c-1 2-3 3-3 5l-1 1c-1-2-3-4-5-6l-3-6 1-1h1 2z" class="h"></path><path d="M252 279h1 1v-4h2l1 2-1 2c1 1 3 0 5 0-1 1-2 3-2 4-1 1-2 1-3 2-3-1-5-4-7-6h1 2z" class="U"></path><path d="M243 268l2-2c0-1 0-2 1-3 0 1 1 1 1 1v1c1 1 1 0 2 0 1 1 1 0 2 0v1l1 2c2 1 3 0 4 2v1c0 2 1 3 2 4h-2-2v4h-1-1-2c-1-1-1-1-1-2l-2-1c0-1-1-2-2-3h-1l-1-1c0-1 1-2 0-3v-1z" class="e"></path><path d="M252 268c2 1 3 0 4 2v1c0 2 1 3 2 4h-2-2v4h-1-1-2c-1-1-1-1-1-2s0-2-1-3l-1-1v-1c2-1 4 0 6 0h0l-2-2 1-2z" class="N"></path><path d="M248 274h3c1 1 1 3 1 5h-2c-1-1-1-1-1-2s0-2-1-3z" class="c"></path><path d="M486 399l6 5 2 3 5 2 6 6h-1-5c-1 0-3 0-4 1h0l1 1h0c1-1 1-1 2 0 2 1 4 0 6 1 0 0 0 1-1 2 0 1-1 2-2 2l-3 1v1l-1 1c1 1 1 1 1 2 1 0 1 0 1 1h-1c-2 0-5-1-7-1 0 0-1 1-1 2-1 0-3 1-4 2h-2-4c-1-2-1-4-1-6l3-2-2-1h-3l-1 1c-1 1-2 1-3 1v-4-4c0-2-2-3-1-5h1v-1-2c0-1 1-2 2-3 0-1 1-1 2-2 2-2 4-2 7-2v-1c1 0 1 0 2-1z" class="K"></path><path d="M491 408h0l2 2h1v1c-1 0-1 1-2 1-1-2-1-2-1-4z" class="O"></path><path d="M483 419l1 1h1 1l-1 1c-1 0-3 2-3 2l-2-1h-3c1-1 3-2 6-3z" class="F"></path><path d="M486 399l6 5 2 3v2c-2-2-5-6-7-6-1-1-2-1-3-1v-1-1c1 0 1 0 2-1z" class="S"></path><path d="M494 407l5 2 6 6h-1l-2-1c-1-1-1-1-3-1h0c-2-2-4-2-5-4v-2z" class="F"></path><path d="M502 414l2 1h-5c-1 0-3 0-4 1h0l1 1h0c1-1 1-1 2 0 2 1 4 0 6 1 0 0 0 1-1 2 0 1-1 2-2 2l-3 1h-2-3c-1-1-1-2-1-3h1l1-2c-3 0-5 1-8 2h0-1-1l-1-1 1-1c1-1 3-1 4-2h1c2-1 4-1 6-1s5 0 7-1z" class="C"></path><path d="M493 423c-1-1-1-2-1-3h1c1 1 2 1 3 1l2 1c1 0 2 0 3-1h1c1 0 0 0 1-1 0 1-1 2-2 2l-3 1h-2-3z" class="K"></path><path d="M486 420h0c3-1 5-2 8-2l-1 2h-1c0 1 0 2 1 3h3 2v1l-1 1c1 1 1 1 1 2 1 0 1 0 1 1h-1c-2 0-5-1-7-1 0 0-1 1-1 2-1 0-3 1-4 2h-2-4c-1-2-1-4-1-6l3-2s2-2 3-2l1-1z" class="D"></path><path d="M486 420h0c3-1 5-2 8-2l-1 2h-1c0 1 0 2 1 3h3l-1 2h-2-2l-2-1-5 4c-1-1-1-2-1-3 1-2 1-3 2-4h0l1-1z" class="R"></path><path d="M489 424c-1-1-2-1-3-2l1-1c1 0 2 1 3 1h1l1 1h1 3l-1 2h-2-2l-2-1z" class="Q"></path><path d="M490 422h1l1 1h1 3l-1 2h-2l-3-3z" class="J"></path><path d="M475 405l1 1 2-2c1 0 2-1 3-1h1c1 0 2 1 3 2s1 2 2 4c0 1-3 3-4 4s-1 1-2 1c-1 1-1 1-1 3-1 0-2 1-3 2s-1 2-2 3l1 1c-1 1-2 1-3 1v-4-4c0-2-2-3-1-5h1v-1-2c0-1 1-2 2-3z" class="B"></path><path d="M481 414s-1 0-2-1c-1 0-2-1-2-3-1-2 1-4 2-6 2 0 4 0 6 1 1 1 1 2 2 4 0 1-3 3-4 4s-1 1-2 1z" class="C"></path><path d="M858 154c3 0 5 0 7 2v2l1 3v-1c2 1 4 3 5 4 1 4 4 7 5 11 2 2 2 3 3 5-2 1-2 1-3 0-3-2-6-7-10-6 4 2 8 6 11 10-1-1-3-2-4-2-1-1-1-1-2-1-2-2-7-5-10-5l3 3h-2c1 1 2 2 4 2 3 1 7 3 9 6h0c-3-1-5-3-8-4-3-2-9-4-13-2h-2v-3l-1-1c-1-1-2-1-3-1-3 0-4 0-6 1l-1 1c-2 1-5 2-6 4-1 0-1-2-1-2 0-1 4-3 5-4v-2l-1-1 1-1c0-1 1-2 2-2 3-3 6-6 7-10h1 1v1 3l1-4 1-1c1-3 3-4 6-5z" class="B"></path><path d="M871 181c-1-2-2-3-4-4-2 0-3-1-4-2s-4-2-6-3c-1 0-2-1-4-2h-1c-1-1-2-1-3-1h-1c0-1 1-2 1-2v-1h1l2 1c1 1 3 3 5 3 2 1 7 4 9 4 4 2 8 6 11 10-1-1-3-2-4-2-1-1-1-1-2-1z" class="D"></path><path d="M855 158c1-1 1-2 3-2 2-1 3 0 5 2h2l1 3-1 3c-2 2-2 3-5 3-2 0-3 0-5-1l-2-1v-1-3l1-1v-2h1z" class="E"></path><path d="M854 160c0 1 1 3 2 3 2 0 3 0 5 1l3 1c-4 0-7 0-11-1v-3l1-1z" class="J"></path><path d="M855 158c1-1 1-2 3-2 2-1 3 0 5 2v1c0 1 0 2-1 3h-5-1c-1-2-1-2-1-4z" class="a"></path><path d="M866 161v-1c2 1 4 3 5 4 1 4 4 7 5 11 2 2 2 3 3 5-2 1-2 1-3 0-3-2-6-7-10-6-2 0-7-3-9-4-2 0-4-2-5-3l1-2 2 1c2 1 3 1 5 1 3 0 3-1 5-3l1-3z" class="N"></path><path d="M876 180h0c-2-3-5-6-8-7-1-1-3-2-5-3v-1h2c1 1 2 2 4 2 1 1 3 2 4 3s2 1 3 1c2 2 2 3 3 5-2 1-2 1-3 0z" class="M"></path><path d="M866 161v-1c2 1 4 3 5 4 1 4 4 7 5 11-1 0-2 0-3-1s-3-2-4-3c-2 0-3-1-4-2 2-1 0-1 0-2s1-2 1-2l-1-1 1-3z" class="V"></path><path d="M849 160h1v1 3c-3 5-8 7-11 12 4-3 6-6 12-5l4 1v1h-1c1 1 4 2 5 2h1l1 1 3 3h-2c1 1 2 2 4 2 3 1 7 3 9 6h0c-3-1-5-3-8-4-3-2-9-4-13-2h-2v-3l-1-1c-1-1-2-1-3-1-3 0-4 0-6 1l-1 1c-2 1-5 2-6 4-1 0-1-2-1-2 0-1 4-3 5-4v-2l-1-1 1-1c0-1 1-2 2-2 3-3 6-6 7-10h1z" class="P"></path><path d="M453 184h4 0c3 2 4 4 5 6 0 1 1 2 1 3 2 2 9 2 12 1h0l2-1v5 2c0 3-1 10 1 12-1 2-1 3-3 4l-5 6h1l-2 2-1-1c0-2 0-3-2-5-1 3-2 5-5 6l3-6 1-2v-1l-2 2s-1 1-2 1l-1 1s-1 1-1 2l-1-1 1-1h-2c2-2 3-4 4-6 1-1 1-2 1-4h-2c-6 2-12 4-19 4l-4-3h2c2-1 5-2 7-3-1-1-2-1-4-2l-2 1c3-4 9-6 13-8 2-1 3-3 3-5l-1-2c0-2-1-4-2-5l1-1-1-1z" class="V"></path><path d="M462 206v-1l3-3v4h-3z" class="i"></path><path d="M468 198c1 0 2 0 3 1 1 0 2-1 4 0v3c-1 1-2 1-3 1s-2-1-3-1-2 0-3-1v-1l2-2z" class="c"></path><path d="M454 185c1 0 2 0 4 2l2 4c0 1 0 2-1 3v1 2c-1 0-1 0-2-1 0-1 0-2-1-3l-1-2c0-2-1-4-2-5l1-1z" class="M"></path><path d="M470 222l-2 1c0-3 4-4 5-6 0-1 1-3 1-3 0-2 1-3 1-5v-6c1-1 2-2 2-3 0 3-1 10 1 12-1 2-1 3-3 4l-5 6z" class="U"></path><path d="M442 205c2-1 14-7 15-7 0 2 0 2-1 3-3 3-7 4-10 6-1-1-2-1-4-2z" class="P"></path><path d="M453 184h4 0c3 2 4 4 5 6 0 1 1 2 1 3 2 2 9 2 12 1 0 1-1 1-1 2h-2-8v1l-2-2h-2l1-1c0-1 0-2-1-3l-2-4c-2-2-3-2-4-2l-1-1z" class="J"></path><path d="M460 219c1-2 2-5 4-7 1-1 0-4 1-5 1 0 2-1 2-1v-2h1c1 2 2 6 1 9 0 1 0 1-1 2l-2 3c-1 3-2 5-5 6l3-6 1-2v-1l-2 2s-1 1-2 1l-1 1z" class="F"></path><path d="M457 198l2 2v-1c2-2 5-2 8-1h1l-2 2v1h0-1-1c0 1-1 1-2 1-2 0-3 1-5 2l-1 1c-3 0-6 2-8 3s-4 2-6 2c-1 0-2 1-3 1v-1c2-1 5-2 7-3 3-2 7-3 10-6 1-1 1-1 1-3z" class="e"></path><path d="M462 202c1 0 2 0 2-1h1 1l-1 1-3 3v1l-2 3c-6 2-12 4-19 4l-4-3h2v1c1 0 2-1 3-1 2 0 4-1 6-2s5-3 8-3l1-1c2-1 3-2 5-2z" class="N"></path><path d="M442 210c2 0 4-1 6-2s5-3 8-3l1-1c2-1 3-2 5-2 1 1 1 0 1 1-2 1-3 2-4 4-2 0-5 1-7 2-1 1-2 1-3 1h0c-2 0-2 0-2 1h-3-3l1-1z" class="k"></path><path d="M676 244v-1 1c1 5 4 8 5 12l1 1c0 4-2 7-3 10l-9 18c-2 3-4 7-6 10-3 7-5 14-8 21l-17 38-12 27-6 9c0 2-1 4-1 6-1 3-2 5-3 7l-14 30-10 26c-2 1-2 2-3 4h0c1-2 1-4 1-7 3-9 6-18 10-26 6-14 16-27 17-41 2-1 3-3 3-4 4-6 6-13 9-19l25-56c1-3 2-6 3-10 1-2 2-5 3-7s3-3 4-5c2-2 3-6 4-8l8-17c1-2 3-6 3-8 0-1-3-5-3-6-1-2-1-3-1-5h0z" class="X"></path><path d="M553 391h1v2h2c1 1 2 1 2 1 1 3 0 5-1 8h1l2-2 1-1 1 1h0c-1 5-1 7-3 11-1 1-2 2-2 3-1 1-1 2-2 3h0c-1 2-2 5-4 6s-3 3-5 3l-2-1v2l-18-18c1-1 1-1 3-1 0-2 1-3 3-4l2-2c1-1 2-3 3-4 2-1 4-3 6-5 2 0 2 0 4-1 1-1 4 0 6-1z" class="Q"></path><path d="M548 402l1 1c0 1 0 2-1 3 0 1 1 2 2 2l1 1 1-1h2l-2 2c-2 0-3 0-5-1l-2-2c1-2 2-3 3-5z" class="E"></path><path d="M534 402c1-1 2-3 3-4l2 2h0c2-1 3-1 5-1v1c0 1 1 2 2 3-1 0-1 1-2 2 0 1 0 0-1 1h-1l-1-1 1-1 1-1-2-2-2 2c-2 0-4 0-5-1z" class="V"></path><path d="M548 402c2-1 3-1 5-1 1 0 1 1 2 2l1 2c0 1-1 2-2 3h-2l-1 1-1-1c-1 0-2-1-2-2 1-1 1-2 1-3l-1-1h0z" class="G"></path><path d="M532 404h0l2 1 1-1v1 5c1 1 1 1 2 1v1h2v2h-1v1h1c1 0 0 0 1 1 0 1 0 1 1 2h0c1-1 1-1 2-1h1 1c1 0 1 1 2 1 0 2 0 2-1 4h-1l-1-2h-3c-2-2-3-4-5-6-1-1-4-2-4-3-1-1 0-3-1-4-1 1-1 0-2 1 0-2 1-3 3-4z" class="U"></path><path d="M529 408c1-1 1 0 2-1 1 1 0 3 1 4 0 1 3 2 4 3 2 2 3 4 5 6h3l1 2c0 1-1 2-1 3v2l-18-18c1-1 1-1 3-1z" class="H"></path><path d="M553 391h1v2h2c1 1 2 1 2 1 1 3 0 5-1 8l-2 1c-1-1-1-2-2-2-2 0-3 0-5 1h-1l-1 1c-1-1-2-2-2-3v-1c-2 0-3 0-5 1h0l-2-2c2-1 4-3 6-5 2 0 2 0 4-1 1-1 4 0 6-1z" class="i"></path><path d="M544 400c2-1 2-1 4 0v1l-1 1-1 1c-1-1-2-2-2-3zm6-8h2v1c-1 1-1 2-2 2h-3v-1l3-2z" class="N"></path><path d="M892 266h1 1c1-1 3-5 3-7l3-11 1-2c0-2 0-6 2-8-1 16-7 32-19 43-11 10-27 17-42 16-10 0-21-4-28-12v-1c3 1 5 3 7 5 8 5 18 4 27 1 8-3 15-9 18-16 1-1 1-2 1-3s-2-2-2-3v-1c2 1 4 2 5 5l1-1-2-3c2 0 2 0 3 1h1 2v-2l1 2h1c1-3 4-8 4-11h0l2 3c0-1 1-2 2-3l-1 2c0 2 1 3 0 4 0 3-2 8-4 11 1 0 2 1 2 1 2-1 3-3 4-5l1 2c-4 6-10 11-16 14-1 1-1 1-2 1 1 0 1 1 2 0 8-4 18-13 21-22z" class="g"></path><path d="M875 267l1 2h1c1-3 4-8 4-11h0l2 3c0-1 1-2 2-3l-1 2c0 2 1 3 0 4 0 3-2 8-4 11 1 0 2 1 2 1l-9 7c-1 2-4 3-5 4-2 1-4 2-6 2l-1-1-1 1v-1l6-6c2-3 7-9 6-13h1 2v-2z" class="X"></path><path d="M884 260c0 2 1 3 0 4 0 3-2 8-4 11 1 0 2 1 2 1l-9 7c5-7 9-14 11-23z" class="B"></path><path d="M875 267l1 2h1c1-3 4-8 4-11h0l2 3c0 1 0 1-1 2-5 9-9 17-17 22-1 1-3 2-4 3h-1l6-6c2-3 7-9 6-13h1 2v-2z" class="G"></path><path d="M554 168l-1 2h1l3-3-3 6c-2 2-4 4-4 6l4-5 3 1-1 1c-1 0-2 1-3 2v1h1c1 2 2 4 1 6s-4 3-7 4c0 1 1 2 2 3v2c0 3 0 7-1 11h0c-1 3-3 5-5 8h-1c0-1 1-2 0-3h-3c-4 1-8 2-11 1-2-1-3-1-4-2l-1-1h-1l-2 3v-3l2-4v-2l1-1c0-2 1-2 1-3v-1l1-2-1-1c-1 1-2 3-2 4l-2 5-1-1 1-4v-1c1-2 2-4 3-5l6-7c3 0 4-3 6-4 2-2 4-3 6-5 4-3 7-7 12-8z" class="c"></path><path d="M540 180l11-9c0 3-5 7-8 10l-3 2v-1l1-1h0-1v-1z" class="U"></path><path d="M540 180v1c-1 1-4 3-5 5 0 2-2 3-4 4-1 1-2 3-4 3l-1 1 1 1-2 3v-1l1-2-1-1c-1 1-2 3-2 4l-2 5-1-1 1-4 1-2c3-4 6-6 10-9 2-2 5-5 8-7z" class="J"></path><path d="M531 190h1c2 0 3-1 4-2h1c-1 1-1 2-2 2l-4 4-1 1c0 1 0 1 1 2h-1c-3 3-5 3-6 7-1 2-1 3-1 4l-2 3v-3l2-4v-2l1-1c0-2 1-2 1-3l2-3-1-1 1-1c2 0 3-2 4-3z" class="a"></path><path d="M533 194l1 1h1l-1 1-1 1h1v1h0-1c-2 0-3 1-5 3h1l1 1s1 0 2 1c-1 1-1 2-1 4 1-1 2-1 3-1v1h-4c-1 0-2 0-2-1h-1c-1 1-2 2-2 3l-1-1h-1c0-1 0-2 1-4 1-4 3-4 6-7h1l2-3z" class="I"></path><path d="M533 194l4-2c1 0 1 0 2 1s2 1 2 2c0 2 0 4-1 6l-6 3-2-1c-1-1-2-1-2-1l-1-1h-1c2-2 3-3 5-3h1 0v-1h-1l1-1 1-1h-1l-1-1z" class="W"></path><path d="M554 174l3 1-1 1c-1 0-2 1-3 2v1h1c1 2 2 4 1 6s-4 3-7 4c0 1 1 2 2 3v2c0 3 0 7-1 11h0c-1 3-3 5-5 8h-1c0-1 1-2 0-3h-3c-4 1-8 2-11 1-2-1-3-1-4-2 0-1 1-2 2-3h1c0 1 1 1 2 1h4v-1c-1 0-2 0-3 1 0-2 0-3 1-4l2 1 6-3c1-2 1-4 1-6 0-1-1-1-2-2h1l-1-2 1-1v-2l1-1c2 0 7-6 9-8l4-5z" class="D"></path><path d="M540 190c2 0 3 0 4 2 1 1 1 2 1 2v1 6c-1 0-1 0-2 1 0-2 1-3 1-5 0-1-2-4-4-4l-1-2 1-1z" class="E"></path><path d="M554 179c1 2 2 4 1 6s-4 3-7 4c-1-1-2-1-3-1v2h-1c0-1 0-1-1-2v-1c1-1 2-2 4-2h0c2 0 4-1 5-3 0-1 1-2 2-3z" class="M"></path><path d="M538 206s2-1 2-2v2c2-1 4-2 5-4h0l1-1c-1 4-2 7-6 9h0c-4 1-8 2-11 1-2-1-3-1-4-2 0-1 1-2 2-3h1c0 1 1 1 2 1h4c2 0 3-1 4-1z" class="K"></path><path d="M525 209c0-1 1-2 2-3h1c0 1 1 1 2 1h4c2 0 3-1 4-1 0 1 0 2-2 2-1 1-2 1-3 1h-2c-1 0-3-1-4-1v1c1 0 1 1 2 2-2-1-3-1-4-2z" class="a"></path><path d="M545 190v-2c1 0 2 0 3 1 0 1 1 2 2 3v2c0 3 0 7-1 11h0c-1 3-3 5-5 8h-1c0-1 1-2 0-3h-3 0c4-2 5-5 6-9 1-1 1-5 0-6 0-2 0-4-1-5zM250 292l-2-5h1c2 1 4 5 6 5l2 3 1-1 16 37c3 5 3 10 6 15 0 1-1 2-1 3 3 7 6 14 8 21l4 8c-2 2-3 3-6 3h0c-1 1-1 2-2 4l4 7h0v2c0 1 3 3 3 4h-2 0c-3-1-5-8-7-10l-6-23 1-1-12-31c-1-3-4-7-3-10 0-1 1-1 2-1l-2-2c0-4-3-9-5-14-2-4-3-10-6-14z" class="S"></path><path d="M287 370l4 8c-2 2-3 3-6 3h0l-1-1v-1-2l1-1 1 1h2c0-2 0-1-1-3v-3-1z" class="E"></path><path d="M274 331c3 5 3 10 6 15 0 1-1 2-1 3l-7-16 2-2z" class="d"></path><defs><linearGradient id="Y" x1="264.802" y1="305.667" x2="267.14" y2="325.34" xlink:href="#B"><stop offset="0" stop-color="#a09fa2"></stop><stop offset="1" stop-color="#c2c4c2"></stop></linearGradient></defs><path fill="url(#Y)" d="M258 294l16 37-2 2-15-38 1-1z"></path><defs><linearGradient id="Z" x1="284.317" y1="377.947" x2="278.183" y2="381.553" xlink:href="#B"><stop offset="0" stop-color="#908f91"></stop><stop offset="1" stop-color="#b0b1b2"></stop></linearGradient></defs><path fill="url(#Z)" d="M276 364h0c2-2 3-8 4-11 0 2-1 4-2 7h0c-1 6 1 13 3 19l1-1c1 1 1 2 2 2l1 1c-1 1-1 2-2 4l4 7h0v2c0 1 3 3 3 4h-2 0c-3-1-5-8-7-10l-6-23 1-1z"></path><path d="M281 379l1-1c1 1 1 2 2 2l1 1c-1 1-1 2-2 4l-2-6z" class="C"></path><path d="M359 273v-1-2c1-2 0-4 1-5 0-1 0 0 1-1 1 9 5 16 9 24 1 3 2 6 4 9 1 3 3 6 4 9l15 36 6 14 6 13 9 22c1 3 2 6 4 9l8 19c-1 1-1 2-2 3-1-2-2-4-4-6l-6-3h1c0-1 0-1-1-2v-2h0c0-3-3-9-4-11l-26-64-24-56c-1-2-1-3-1-5z" class="S"></path><path d="M801 202c1 0 2-1 2-1 1 0 2 1 2 1v-1h0c1-1 4 0 6 0h1c1 0 1 1 2 1h0c2 0 4 1 7 1 1 1 3 2 5 3h1v1l1 1v-2h2 1s1-1 2-1v-1l1 1s-1 1-1 2v3c2 0 3-1 5-2 1 1 1 2 2 3l-7 3c-4 1-7 3-9 7l-1 2h-1c-6 7-9 14-13 22-2 4-3 9-5 13l-1-1-3 8-1 1c0-3 3-6 3-9v-1l-6 14c-2-4 1-5 0-8 1-1 1-2 2-3v-3c0-1 1-2 2-3 1-2 2-3 2-6h0v-3c1 0 1-1 1-1l1-1c1-1 2-2 2-4h1l2-1 2-4-2-1 2-2h-1l-3-3c-2-2-4-5-5-7v-1c-1-1-1-1-1-2l-1 1h-1c-2-3-5-5-8-8 1-1 1-2 2-2 2-1 2-2 4-1l2-3 1-1 1-1z" class="h"></path><path d="M831 206s1-1 2-1l-2 2 1 1v3c-2 1-5 2-7 2-1-2-3-2-3-4 1 1 1 1 3 2l-1-1 1-1h2s1 1 2 1l-1-2v-2h2 1z" class="G"></path><path d="M803 243l1-1c1-1 2-2 2-4h1l2-1c-2 4-3 10-6 14l-1 5-6 14c-2-4 1-5 0-8 1-1 1-2 2-3v-3c0-1 1-2 2-3 1-2 2-3 2-6h0v-3c1 0 1-1 1-1z" class="B"></path><path d="M803 243l1-1c1-1 2-2 2-4h1l2-1c-2 4-3 10-6 14 0-1 0-2 1-4l2-4-4 4v-3c1 0 1-1 1-1z" class="G"></path><defs><linearGradient id="a" x1="815.416" y1="218.637" x2="805.271" y2="213.493" xlink:href="#B"><stop offset="0" stop-color="#555556"></stop><stop offset="1" stop-color="#6e6d6e"></stop></linearGradient></defs><path fill="url(#a)" d="M803 215l6-4c2-1 3 0 4 1 3 2 5 3 6 7 0 4-6 10-8 14l-2-1 2-2h-1l-3-3c-2-2-4-5-5-7 0-2-1-3 1-5h0z"></path><path d="M803 215v1 2 1c1 0 1 0 2-1l1 2v-1h1v3c1 1 1 2 3 2 1-1 2-3 3-3 0 0 1 0 1-1 1 0 2-1 2-2h1c0 1 1 1 2 1 0 4-6 10-8 14l-2-1 2-2h-1l-3-3c-2-2-4-5-5-7 0-2-1-3 1-5h0z" class="D"></path><path d="M801 202c1 0 2-1 2-1 1 0 2 1 2 1v-1h0c1-1 4 0 6 0h1c1 0 1 1 2 1h0c2 0 4 1 7 1 1 1 3 2 5 3h1v1l1 1 1 2c-1 0-2-1-2-1h-2l-1 1 1 1c-2-1-2-1-3-2 0 2 2 2 3 4-2 1-3 3-4 4l-6-6h-1l-1 1c-1-1-2-2-4-1l-6 4h0c-2 2-1 3-1 5v-1c-1-1-1-1-1-2l-1 1h-1c-2-3-5-5-8-8 1-1 1-2 2-2 2-1 2-2 4-1l2-3 1-1 1-1z" class="M"></path><path d="M813 205c2 0 3 0 5 1l2-1c2 1 4 1 6 1h1v1l1 1 1 2c-1 0-2-1-2-1h-2l-1 1 1 1c-2-1-2-1-3-2-3-3-4-1-6-2-1-1-2-2-3-2z" class="e"></path><path d="M801 208v-1l2-2c3-2 7-1 10 0 1 0 2 1 3 2 2 1 3-1 6 2 0 2 2 2 3 4-2 1-3 3-4 4l-6-6h-1l-1 1c-1-1-2-2-4-1l-6 4h0l-1-1c-1-1-2-2-2-4 0-1 0-1 1-2z" class="U"></path><path d="M802 214c-1-1-2-2-2-4 0-1 0-1 1-2 1 1 4 1 6 0 3 1 5 2 7 3l-1 1c-1-1-2-2-4-1l-6 4h0l-1-1z" class="N"></path><path d="M802 214c-1-1-2-2-2-4 0-1 0-1 1-2 1 1 4 1 6 0-1 2-4 4-5 6z" class="V"></path><path d="M885 212l2-2 1-3c1 1 1 1 1 2h2l2 2c2 3 3 7 4 11s2 9 1 13v9c1 1 0 4-1 6-1 6-3 11-5 16-3 9-13 18-21 22-1 1-1 0-2 0 1 0 1 0 2-1 6-3 12-8 16-14l-1-2c-1 2-2 4-4 5 0 0-1-1-2-1 2-3 4-8 4-11 1-1 0-2 0-4l1-2v-2h0c1-5 0-12-2-16l-3-6s0-1-1-1v-1c-3-1-2 1-4 1v-2-1l-1-2h-2c-2 0-3 0-4-1h1v-1c0-1 0 0 1-1 2 0 3 1 5 2 0 1 1 1 2 1v-1c0-2 0-2-2-2v-3-1l1-1c-1-1-2-2-2-3h0l-3-2 2-1s1 0 1 1h2v-1c2 0 3 1 4 2h1v1c0-1-1-2-1-2v-1h1v-1h2l2-1z" class="W"></path><path d="M885 235v-1-1c-1-1-1-2-1-3h1c0 2 1 2 2 3v1c1 1 1 1 1 2l1 1v1h0l1-1v-2c2 8 3 15 3 22-1 6-2 11-6 16l-1-2c1-2 2-3 2-5 2-8 2-16 0-24-1-2-2-5-3-7z" class="Y"></path><path d="M876 214c2 0 3 1 4 2h1v1c5 5 8 11 9 18v2l-1 1h0v-1l-1-1c0-1 0-1-1-2v-1c-1-1-2-1-2-3h-1c0 1 0 2 1 3v1 1-1c-3-4-6-9-10-12v-1l1-1c-1-1-2-2-2-3h0l-3-2 2-1s1 0 1 1h2v-1z" class="M"></path><path d="M874 217c1 0 3 1 4 3h0c1 3 4 4 5 6l1 1c0 1 1 2 2 3v1l1 3v-1c-1-1-2-1-2-3h-1c0 1 0 2 1 3v1 1-1c-3-4-6-9-10-12v-1l1-1c-1-1-2-2-2-3z" class="P"></path><path d="M885 212l2-2 1-3c1 1 1 1 1 2h2l2 2c2 3 3 7 4 11s2 9 1 13v9c1 1 0 4-1 6-1 6-3 11-5 16-3 9-13 18-21 22-1 1-1 0-2 0 1 0 1 0 2-1 6-3 12-8 16-14 4-5 5-10 6-16 0-7-1-14-3-22-1-7-4-13-9-18 0-1-1-2-1-2v-1h1v-1h2l2-1z" class="W"></path><path d="M893 218l1 1c0 1 1 1 1 2l1 1h1c1 4 2 9 1 13-2-6-3-12-6-17h1z" class="C"></path><path d="M890 224c1-1 1-3 1-4-1-2-1-2-1-4 4 4 4 14 5 19h-1 0l-1 1v-6l-1-1c0-2-1-3-2-5z" class="G"></path><path d="M885 212l2-2 1-3c1 1 1 1 1 2h2l2 2c2 3 3 7 4 11h-1l-1-1c0-1-1-1-1-2l-1-1h-1c0-1-1-2-1-4v-1h-1c-2-2-3-1-5-1z"></path><path d="M885 212l2-2 1-3c1 1 1 1 1 2l3 4c1 1 1 3 1 5h-1c0-1-1-2-1-4v-1h-1c-2-2-3-1-5-1z" class="D"></path><path d="M885 212c2 0 3-1 5 1v3c0 2 0 2 1 4 0 1 0 3-1 4 1 2 2 3 2 5l1 1v6l1-1h0 1c-1 5-1 10-2 15 0 2 1 5 0 7 0-7-1-14-3-22-1-7-4-13-9-18 0-1-1-2-1-2v-1h1v-1h2l2-1z" class="F"></path><path d="M885 212c2 0 3-1 5 1v3c0 2 0 2 1 4 0 1 0 3-1 4 0-1-1-2-1-3-1-2-4-4-4-6l-2-2 2-1z" class="J"></path><path d="M852 178v3h2c4-2 10 0 13 2 3 1 5 3 8 4l5 4-4-2h-2c-2 1-4 1-5 3v1l4-2h3c3 1 6 4 8 6 3 3 6 8 7 12h-2c0-1 0-1-1-2l-1 3-2 2-2 1h-2v1h-1v1s1 1 1 2v-1h-1c-1-1-2-2-4-2v1h-2c0-1-1-1-1-1l-2 1c-1-1-1-1-2-1v-1c0-1-1-1-2-1h0c0 1 0 1-1 1l-1 1-10-3 1-1h3c-2-2-4-3-6-4h-1c0-1 0-2-1-3l-2-2 3-6h0l1-1-1-1c-1 1-1 1-2 1-2-2-1-2-2-3 0-1-1-2-2-2v-1c1-2 1-3 3-4l-1-2 2 1h1c-1-1-1-2-1-3s1-1 2-2z" class="U"></path><path d="M873 191c1 1 1 2 2 3l-1 1 4 4c0 2 1 3 2 4l-3 3c-1 0-2 1-4 2l-3 1c-3 1-7 1-11 1-2-2-4-3-6-4h-1c0-1 0-2-1-3 0-1 0-2 1-3v2l1 1v1c1 2 4 3 6 4h1l1 1c1 0 2 0 4-1h0c3-1 4-2 5-4h2c0-1 1-1 1-2-1 0-2-1-2-1l-1-1h1c0-2-1-2-1-3 1 0 1 0 2-1-1-1-1-2-2-2h-1v-1l4-2z" class="B"></path><path d="M873 191c1 1 1 2 2 3l-1 1 4 4c0 2 1 3 2 4l-3 3c-1-2 0-3-1-4h-2 0c0-1 0-2 1-3l-1-2c-1 1-2 1-2 3h0-1c0-2-1-2-1-3 1 0 1 0 2-1-1-1-1-2-2-2h-1v-1l4-2z" class="C"></path><path d="M852 178v3h2c4-2 10 0 13 2 3 1 5 3 8 4l5 4-4-2h-2c-2 1-4 1-5 3v1 1h1c1 0 1 1 2 2-1 1-1 1-2 1 0 1 1 1 1 3h-1l-2 1c0-1-1-2-1-3h-1l1-1h1v-1c0-1-1-1-1-1h-2c-1 0-1 0-2-1v-3h0v-1h0c-1-1-2-2-2-3v-1-1h-1-1c-1 1-2 1-3 1 0 0-1-1-2-1-1-1-3 0-4-1h-1l-1-2 2 1h1c-1-1-1-2-1-3s1-1 2-2z" class="D"></path><path d="M869 192l-1 2-1-1c0-2 0-2 1-4 1 0 1 0 2-1l-1-1h-1c-2 0-3-1-5-2l1-1h1l1 1c2 0 5 2 8 4-2 1-4 1-5 3z" class="C"></path><path d="M873 191h3c3 1 6 4 8 6 3 3 6 8 7 12h-2c0-1 0-1-1-2l-1 3-2 2-2 1h-2v1h-1v1s1 1 1 2v-1h-1c-1-1-2-2-4-2v1h-2c0-1-1-1-1-1l-2 1c-1-1-1-1-2-1v-1c0-1-1-1-2-1h0c0 1 0 1-1 1l-1 1-10-3 1-1h3c4 0 8 0 11-1l3-1c2-1 3-2 4-2l3-3c-1-1-2-2-2-4l-4-4 1-1c-1-1-1-2-2-3z" class="Y"></path><path d="M880 203c0-2 0-3 2-4 1 2 1 2 2 3-1 1-1 2-2 2 1 1 3 2 3 3l-1 1h0c-1 0-2 0-3 1l1 1h0l-1-1c-2 1-5 1-7 2l-5 1c3-2 8-3 11-5 0-1 1-2 0-3h-1l1 2c-2 1-5 2-7 2 2-1 3-2 4-2l3-3z" class="J"></path><path d="M873 191h3c3 1 6 4 8 6 3 3 6 8 7 12h-2c0-1 0-1-1-2l-1 3-2 2-2 1h-2v1h-1v1s1 1 1 2v-1h-1c-1-1-2-2-4-2l-2-2c2 0 3-1 5-1h3c1 0 2-2 2-3l1-1c0-1-2-2-3-3 1 0 1-1 2-2-1-1-1-1-2-3-2 1-2 2-2 4-1-1-2-2-2-4l-4-4 1-1c-1-1-1-2-2-3z" class="W"></path><path d="M201 142h86 9 20 39 10l28 1c5 0 10 0 15-1h1c3 3 7 4 9 7 0 1-1 1-2 1-4 2-8 4-10 8 0 2-1 4-1 5v8l3 2h0c-2 1-3 1-4 1l-2 2-2-2-1-2h0c-2-2-4-3-5-6l-1-4 1-2c0-3 3-8 5-11 0-1 1-1 1-2v-1l-77-1h-63-48-16c-2 0-3 1-5 2h-1c-1 0-1-1-2-2h0 1c2-1 6-1 7-1 1-1 1-1 2-1 0-1 2-1 3-1z" class="X"></path><path d="M399 172h1c1 0 3 1 4 2l-2 2-2-2-1-2z" class="g"></path><path d="M405 171c-2-2-5-4-6-8-1-3 0-7 2-10 1-3 4-5 7-5 2-1 6-1 7 0-1 0-2 1-2 1-4 2-7 8-8 12l-1 2c0 2-1 4 0 5 0-1 1-3 1-5v8z" class="S"></path><path d="M479 226c3-1 6-3 9-4 7-1 14 0 21 0 5 1 10 1 15 1 5-1 11-2 17 0 3 0 8 1 10 4h-1c1 2 4 3 5 4 2 1 4 2 5 2 3 1 6 2 10 2 2 1 7 1 8 2l-4 1h1c7 1 16-1 21 6l1 1v1h0-15-4l-2-1h-1v-2h-4l-1-1-4-1-1-1c-1 0-2 0-3-1-2-1-6-3-8-3s-3 1-4 2v-2l-1-1c-1-2-2-1-3-1-2-1-2-2-3-3h-3-10c-4 1-7 1-10 0-2 0-2 0-4 1-3 0-8-1-11 0-1 0-2 0-4-1 0 0-1 0-1-1l3-1v-1c-6-1-12-1-18 0-4 1-7 3-11 5-5 2-12 3-17 3-2 1-5 1-6 1-6 1-13 2-17 8v1l-2 1c0-2 1-3 2-4s2-3 4-5c3-3 11-5 16-5 2 1 3 1 4 1h1c1-1 2-1 3-1l9-3 6-3 1-1h1z" class="f"></path><path d="M524 223c5-1 11-2 17 0 3 0 8 1 10 4h-1c-7-4-15-3-23-2-3-1-5 0-7-1 1 0 2 0 3-1h1z" class="G"></path><path d="M479 226c3-1 6-3 9-4 7-1 14 0 21 0 5 1 10 1 15 1h-1c-1 1-2 1-3 1 2 1 4 0 7 1-14 2-28-3-42 0h-1c-1 0-3 1-4 2l-1-1z" class="J"></path><path d="M454 233c2 1 3 1 4 1h1c1-1 2-1 3-1l9-3 6-3 1-1h1l1 1-9 4c-5 3-9 3-14 4-1 1-2 1-2 1h-2-2v1c-6 1-13 2-17 8v1l-2 1c0-2 1-3 2-4s2-3 4-5c3-3 11-5 16-5z" class="c"></path><defs><linearGradient id="b" x1="560.438" y1="219.701" x2="537.032" y2="253.128" xlink:href="#B"><stop offset="0" stop-color="#030203"></stop><stop offset="1" stop-color="#353535"></stop></linearGradient></defs><path fill="url(#b)" d="M503 228c5 0 9 1 14 1 9 0 20-4 29-1l1 1c3 1 6 3 10 4 5 2 11 4 17 5h1c7 1 16-1 21 6l1 1v1h0-15-4l-2-1h-1v-2h-4l-1-1-4-1-1-1c-1 0-2 0-3-1-2-1-6-3-8-3s-3 1-4 2v-2l-1-1c-1-2-2-1-3-1-2-1-2-2-3-3h-3-10c-4 1-7 1-10 0-2 0-2 0-4 1-3 0-8-1-11 0-1 0-2 0-4-1 0 0-1 0-1-1l3-1v-1z"></path><path d="M428 436c2-1 2-2 2-3 1 0 2 1 3 2 6 4 7 7 8 13h-1-4l-1 1 4 9c0 1 1 2 1 2-1 3 0 5 2 8 0 1 0 2 1 3v1 1 1l1 1h-2-2l1 1s1 0 2 1 1 1 3 2c1 1 1 1 2 3 0 0 1 1 1 2v2l-1 2h-4l-1 1c-2 2-3 4-4 7 0-1 0-2-1-3h-1l-1 2c0 3 1 6 1 9-3-4-2-8-5-12l1-1c0-2-2-4-2-6 0-1-1-2-2-3 0-1-1-2-2-3l-2-5c-1-2-3-4-4-6v-1c-1-2-2-4-2-6s0-5-2-6v-4l3-6 1-1 2-5h1c1-1 3-2 4-3z" class="b"></path><path d="M423 439h1l2 3c-4 4-5 9-6 14-3-4 2-8 1-12l2-5z" class="Y"></path><path d="M428 436c2-1 2-2 2-3 1 0 2 1 3 2 6 4 7 7 8 13h-1v-1c-2-3-3-6-7-7-2-1-5 0-7 2l-2-3c1-1 3-2 4-3z" class="h"></path><path d="M428 465l1-1 1 1v-3h-1l1-1h1l1-1 1 1c0-1 0-2-1-3v-3h1l2 7 3 10c1 1 1 2 2 3l1 1h-2v-1c-2 0-3 0-3-1-2-1-4-2-6-2 0-1 0-2-1-3 0-1-1-3-1-4z" class="G"></path><path d="M425 452c1-3 1-6 3-8h1c2 3 3 8 4 11h-1v3c1 1 1 2 1 3l-1-1-1 1h-1l-1 1h1v3l-1-1-1 1-3-7v-6z" class="E"></path><path d="M425 458v-6c1 1 3 1 3 2s1 2 2 3c-1 0-2 0-3-1-1 1-1 0-1 1-1 1 0 1-1 1z" class="I"></path><path d="M420 445l1-1c1 4-4 8-1 12 1 1 1 2 1 3 0 3 1 6 3 9 1 0 1 1 2 2l3 3c1 1 1 1 1 2 1 3 4 5 5 8l1 2c-1 3 0 4 1 7l-2-1 1 4c0 3 1 6 1 9-3-4-2-8-5-12l1-1c0-2-2-4-2-6 0-1-1-2-2-3 0-1-1-2-2-3l-2-5c-1-2-3-4-4-6v-1c-1-2-2-4-2-6s0-5-2-6v-4l3-6z" class="M"></path><path d="M424 468c1 0 1 1 2 2l3 3c1 1 1 1 1 2 1 3 4 5 5 8l1 2c-1 3 0 4 1 7l-2-1c0-1 0-1-1-2-2-8-6-14-10-21z" class="F"></path><path d="M430 472c2 0 4 1 6 2 0 1 1 1 3 1v1h2s1 0 2 1 1 1 3 2c1 1 1 1 2 3 0 0 1 1 1 2v2l-1 2h-4l-1 1c-2 2-3 4-4 7 0-1 0-2-1-3h-1l-1 2-1-4 2 1c-1-3-2-4-1-7l1-2c-1-1-1-2-1-3l-1-1-1-1c-1-1-1-1-1-2h1v-1c-2-1-3-2-4-3z" class="B"></path><path d="M437 483c0 1 0 2 2 3l2 1-1 5h-3c-1-3-2-4-1-7l1-2z" class="I"></path><path d="M430 472c2 0 4 1 6 2 0 1 1 1 3 1v1h2s1 0 2 1 1 1 3 2c1 1 1 1 2 3 0 0 1 1 1 2v2c0-1-1-1-1-2h-1c0-2-1-2-2-3h0c-2 1-4 0-6 0-2-1-2-1-3-1l-1-1-1-1c-1-1-1-1-1-2h1v-1c-2-1-3-2-4-3z" class="C"></path><defs><linearGradient id="c" x1="717.012" y1="413.116" x2="789.251" y2="389.406" xlink:href="#B"><stop offset="0" stop-color="#131314"></stop><stop offset="1" stop-color="#313030"></stop></linearGradient></defs><path fill="url(#c)" d="M751 363l1 1v2l-2 3c0 1-1 2-1 4h0c3 0 3-2 5-3h1c2-1 2-2 5-2v-1h0c5-2 12-1 17 1 5 3 9 7 10 12 1 3 1 5 1 7l-1 1v-3 1c-1 1 0 0-1 2v2h0c-1-2-1-3-2-4-2-5-5-8-11-10-4-1-9-1-13 1-7 4-11 16-13 23-3 12-11 21-15 32 0 1 0 3-1 4 0 1-1 2-1 3 0 2-1 3-2 4 0 2-1 3-2 4l-1-9 1-2v-5c-1-3 0-4 1-6-1 0-2 1-3 1 0-3 3-6 4-9 1-2 2-5 4-8 1-2 1-4 2-5l1-2 3-8c0-1 0-2 1-3s2-4 3-6l2-5v-1l2-4c1-2 1-3 2-5l2-5 1-2z"></path><path d="M748 387v-1l-1 1h-1c2-6 5-8 8-12 1-1 1-1 2-1h1 1 1v-1h2 1c-7 4-10 7-14 14z" class="G"></path><path d="M751 363l1 1v2l-2 3c0 1-1 2-1 4-2 2-3 5-4 7v1c-4 8-6 17-8 25l-2 5-2 3v1c-1 2-2 3-2 5l-5 11c-1-3 0-4 1-6-1 0-2 1-3 1 0-3 3-6 4-9 1-2 2-5 4-8 1-2 1-4 2-5l1-2 3-8c0-1 0-2 1-3s2-4 3-6l2-5v-1l2-4c1-2 1-3 2-5l2-5 1-2z" class="D"></path><path d="M762 373c6-1 12-1 17 2 3 2 7 5 8 9v1 1c-1 1 0 0-1 2v2h0c-1-2-1-3-2-4-2-5-5-8-11-10-4-1-9-1-13 1-7 4-11 16-13 23-3 12-11 21-15 32 0-3 1-8 2-10 1-3 2-5 3-7l4-8c3-7 5-13 7-20 4-7 7-10 14-14z" class="X"></path><path d="M869 226v1h-1c1 1 2 1 4 1h2l1 2v1 2c2 0 1-2 4-1v1c1 0 1 1 1 1l3 6c2 4 3 11 2 16h0v2c-1 1-2 2-2 3l-2-3h0c0 3-3 8-4 11h-1l-1-2v2h-2v-1l-2-1c-2-1-3-1-5-2 0-1 0-2-1-3v-3h0l-4 1-2-2-6-5-6-4h2v-1c-1 0-2-1-2-2s0-2-1-3h0l-1 2v-1l-1-1c1-2 3-3 5-5 4-2 9-5 13-7l-1-2 4-2c2 0 3-1 4-1z" class="N"></path><path d="M863 241h1c2 2 3 2 4 5h-2c0 1 0 0-1 1v-1c-1-1-2-1-2-2v-1h1c-1-1 0-1-1-2z" class="Y"></path><path d="M849 248h1c1-1 1-1 2-1l2-2c1-2 1-3 1-4h1c1-2 3-2 5-3h1v1c-2 1-2 1-3 3-2 2-4 3-4 7 1 2 2 3 3 4 2 1 3 1 5 1 1 0 1 0 2 1h2c-3 1-6 1-8-1-2 0-3-2-5-2h0v-1-2l-1 1h-2c-1 0-1-1-2-1v-1z" class="V"></path><path d="M847 249h2c1 0 1 1 2 1h2l1-1v2 1h0c2 0 3 2 5 2 2 2 5 2 8 1l2-1 1 1 2-2v1c-2 2-4 4-6 5h-1 0l-4 1-2-2-6-5-6-4z" class="g"></path><path d="M859 258c2-1 3-1 5-1 0 1 1 1 1 2l-4 1-2-2z" class="d"></path><path d="M869 226v1h-1c1 1 2 1 4 1h2l1 2v1 2c2 0 1-2 4-1l-1 1h-1c-1 0 0 0-1 1l-1 1 1 1c0 1 1 2 2 3h-1l2 2-1 2h2v1h-2-1v-1c-1 0-1 0-2-1h-1c-2-3-5-7-8-7v1c3 1 6 4 8 7v4h-1v-2c-1-2-3-5-5-7l-1 1h-4v1l-1-1v-1h-1c-2 1-4 1-5 3h-1c0 1 0 2-1 4l-2 2c-1 0-1 0-2 1h-1c-1 0-2-1-2-2s0-2-1-3h0l-1 2v-1l-1-1c1-2 3-3 5-5 4-2 9-5 13-7l-1-2 4-2c2 0 3-1 4-1z" class="M"></path><path d="M869 226v1h-1c1 1 2 1 4 1h2l1 2v1 2c2 0 1-2 4-1l-1 1h-1c-1 0 0 0-1 1l-1 1 1 1c0 1 1 2 2 3h-1l2 2-1 2h2v1h-2-1v-1c-1 0-1 0-2-1-1-2-2-3-4-5l-1-1c-1-2-3-3-5-3l-1-1c2 0 3 0 5 1v-1c-1-1-2-1-3-2h0l-1-1c-1 1-2 1-3 2l-1-2 4-2c2 0 3-1 4-1z" class="J"></path><path d="M876 234l-1 1 1 1c0 1 1 2 2 3h-1c-1 0-2-1-3-1-1-1-1-1-1-2 1-1 2-2 3-2z" class="F"></path><path d="M879 232v1c1 0 1 1 1 1l3 6c2 4 3 11 2 16h0v2c-1 1-2 2-2 3l-2-3h0c0 3-3 8-4 11h-1l-1-2v2h-2v-1l-2-1c-2-1-3-1-5-2 0-1 0-2-1-3v-3h1c2-1 4-3 6-5 1 0 1-1 2 0h1 0l-1-2v-1-1h1c0-2 1-2 2-3h1s1 0 1-1h-1-1l-1-1 1-1h1 2v-1h-2l1-2-2-2h1c-1-1-2-2-2-3l-1-1 1-1c1-1 0-1 1-1h1l1-1z" class="L"></path><path d="M876 236l2-2 1 1c1 1 0 2 0 3l-1 1c-1-1-2-2-2-3z" class="W"></path><path d="M874 251h1c1 1 2 2 4 2 0 1-1 2-1 2l1 2-1 1-1 2c-1 2-1 5-2 7v2h-2v-1l-2-1c-2-1-3-1-5-2 0-1 0-2-1-3v-3h1c2-1 4-3 6-5 1 0 1-1 2 0h1 0l-1-2v-1z" class="J"></path><path d="M868 262h1c1-1 2-2 4-1 0-1 1-1 1-2l1 1-1 3c-1 1-2 1-4 1 0-1-1-1-2-2z" class="d"></path><path d="M874 251h1c1 1 2 2 4 2 0 1-1 2-1 2-1 2-1 3-2 4s0 1-1 1l-1-1c0 1-1 1-1 2-2-1-3 0-4 1h-1 0c0-1-1-2-1-3h-1c2-1 4-3 6-5 1 0 1-1 2 0h1 0l-1-2v-1z" class="R"></path><path d="M365 543h2c1 0 1 0 2 1l-1 2v3c0 1 0 2 1 3 0 1 1 2 0 3 4 6 5 14 7 19v1l2 5v1c0 2 1 4 2 6v2 1h1c0 2 0 2 1 4l1 1c1 1 1 2 2 2l2 2 3 3 1 1s0 1 1 1c0 1 1 1 2 1l2 3-2 2c-4-2-9-4-13-7-2-1-4-2-5-4l-3-1c1 1 1 2 1 2 1 1 1 2 2 3h-2l-11-9c-1-1-2-3-3-4-2-3-4-7-6-10 0-1 0-1 1-2l1-2c0-1 0-3-1-4h-1c-1-5-1-9 1-14l3-7h1l7-7-1-1z" class="W"></path><path d="M370 592c0-1 0-1 1-2v-1l1 1h1c1 2 1 5 2 7 1 1 1 1 1 2l-3-1h1l-4-4v-2z" class="L"></path><path d="M365 543h2c1 0 1 0 2 1l-1 2-7 7v1c0 1 0 0-1 1 0 1 0 1-1 2h0v-3-1c-1 2-2 4-4 5l3-7h1l7-7-1-1z" class="H"></path><path d="M361 562l1 1v3c1 1 1 2 1 3 2 4 4 8 5 11 2 4 4 6 5 10h-1l-1-1v1c-1 1-1 1-1 2l-2-4v-2l-6-12-1-1v-1c-1-2-1-3 0-5h0l-1-1c0-1 1-2 1-4z" class="B"></path><path d="M357 569c1-2 1-3 2-5h0l2-2c0 2-1 3-1 4l1 1h0c-1 2-1 3 0 5v1l1 1 6 12v2l2 4v2l-5-5c-5-6-9-13-8-20z" class="j"></path><path d="M355 558c2-1 3-3 4-5v1 3h0c0 1-1 2-1 3v1c-1 3-2 4-1 8-1 7 3 14 8 20l5 5 4 4h-1c1 1 1 2 1 2 1 1 1 2 2 3h-2l-11-9c-1-1-2-3-3-4-2-3-4-7-6-10 0-1 0-1 1-2l1-2c0-1 0-3-1-4h-1c-1-5-1-9 1-14z" class="h"></path><path d="M356 576l3 10c0 1 1 2 1 4-2-3-4-7-6-10 0-1 0-1 1-2l1-2z" class="M"></path><path d="M355 558c2-1 3-3 4-5v1 3c-3 5-4 9-4 15h-1c-1-5-1-9 1-14z" class="W"></path><path d="M381 603l1-1v-1c-2-3-2-5-3-8-1-1-1-2-1-3v-1c-1-1-1-2-2-3l-1-4c-2-5-4-10-6-16v-1l-4-11 1-1v1l1 4c1 0 1 1 1 2 1 2 2 3 3 4h0c0-1-1-3-1-5-1-1-1-2-1-4 4 6 5 14 7 19v1l2 5v1c0 2 1 4 2 6v2 1h1c0 2 0 2 1 4l1 1c1 1 1 2 2 2l2 2 3 3 1 1s0 1 1 1c0 1 1 1 2 1l2 3-2 2c-4-2-9-4-13-7z" class="J"></path><path d="M444 342c-1-2-2-4-2-7-1-1-1-3 0-4l1 2c2 0 3 4 4 6 0 1 1 1 1 2l3 5 2 3 5 7 1 1v1l2 2v-3h0 2l4-1h0l2-1c1 0 3-1 5-1h1c1-1 2-1 3-1 1 1 1 1 1 2h1l1 2v1c2 0 2 1 3 3h0l-1 1 2 2 1 1c-1 1-2 1-3 1h0l-2 2 2 2c-2 1-4 2-5 3l-1 1c2 0 4-1 5-2l2 1c0 1-1 2-1 3-1 1-2 1-2 1-1 1-1 0-1 1l-1 2c0 1 4 5 5 6 2 2 4 5 5 6s4 4 4 5l-1 1 14 18c-1 0-1 0-1-1l-6-6-5-2-2-3-6-5c-1 1-1 1-2 1h-6c-1 1-2 1-3 0l-3 3-2 2h-1c-1-2-1-4-2-6 0-2-1-4-3-6l-1-2c0-2-1-4-1-6v-1-1l4-4c1-1 3-2 5-3-5-5-10-11-14-17-1-1-2-2-3-4-3-4-5-8-8-11 0-1-1-1-1-2h0-1z" class="P"></path><path d="M486 394c2 2 4 4 5 6 2 2 4 4 5 6l3 3-5-2-2-3-6-5v-1h0l1-1-1-3z" class="U"></path><path d="M492 404c1 0 2 0 3 1l1 1 3 3-5-2-2-3z" class="E"></path><path d="M482 372l2 1c0 1-1 2-1 3-1 1-2 1-2 1-1 1-1 0-1 1l-1 2c0 1 4 5 5 6 2 2 4 5 5 6s4 4 4 5l-1 1-20-25c2 1 2 1 5 1h0c2 0 4-1 5-2z" class="I"></path><path d="M474 380c4 4 8 9 12 14l1 3-1 1c-2-2-5-2-7-2l-3-4h0l-1-8c-1-1-1-2-1-4z" class="E"></path><defs><linearGradient id="d" x1="462.136" y1="386.088" x2="471.781" y2="388.41" xlink:href="#B"><stop offset="0" stop-color="#242425"></stop><stop offset="1" stop-color="#474848"></stop></linearGradient></defs><path fill="url(#d)" d="M466 379c2-1 3-1 6-1 0 1 1 1 2 2 0 2 0 3 1 4l1 8h0l3 4c2 0 5 0 7 2h0v1c-1 1-1 1-2 1h-6c-1 1-2 1-3 0l-3 3-2 2h-1c-1-2-1-4-2-6 0-2-1-4-3-6l-1-2c0-2-1-4-1-6v-1-1l4-4z"></path><defs><linearGradient id="e" x1="467.465" y1="392.517" x2="475.508" y2="394.59" xlink:href="#B"><stop offset="0" stop-color="#46494a"></stop><stop offset="1" stop-color="#676768"></stop></linearGradient></defs><path fill="url(#e)" d="M468 390v-2l1-1v1h3c1 1 1 1 2 3v3h0c1 2 1 2 2 3l1-1h-1v-4l3 4c2 0 5 0 7 2h0v1c-1 1-1 1-2 1h-6c-1 1-2 1-3 0l-3 3-2 2h-1c-1-2-1-4-2-6h0l1-1h0v-5-3z"></path><path d="M475 400l2-1c3-1 6-1 9-1v1c-1 1-1 1-2 1h-6c-1 1-2 1-3 0z" class="O"></path><path d="M468 390s0 1 1 2v1c2 3 2 7 3 10l-2 2h-1c-1-2-1-4-2-6h0l1-1h0v-5-3z" class="G"></path><path d="M443 333c2 0 3 4 4 6 0 1 1 1 1 2l3 5 2 3 5 7 1 1v1l2 2v-3h0 2l4-1h0l2-1c1 0 3-1 5-1h1c1-1 2-1 3-1 1 1 1 1 1 2h1l1 2v1c2 0 2 1 3 3h0l-1 1 2 2 1 1c-1 1-2 1-3 1h0l-2 2 2 2c-2 1-4 2-5 3l-1 1h0c-3 0-3 0-5-1-12-12-22-25-29-40z" class="H"></path><path d="M471 360v-1l3-1v2c-2 0-2 0-3 1 0 2 1 2 3 3 1 2 1 4 3 5 2 0 2 0 4-1l2 2c-2 1-4 2-5 3l-2-2h-2c-1-1-1-1-1-2s0-3-1-4-1-2-3-2h0l-1-1 3-2z" class="G"></path><path d="M478 353c1 1 1 1 1 2-1 2-3 2-5 3l-3 1v1l-1-2h-1c-1 1-2 2-2 3v1h-1l-1-1c0-2 0-2-1-3l-1-1 4-1h0l2-1c1 0 3-1 5-1h1c1-1 2-1 3-1z" class="F"></path><path d="M479 355h1l1 2v1c2 0 2 1 3 3h0l-1 1 2 2 1 1c-1 1-2 1-3 1h0l-2 2c-2 1-2 1-4 1-2-1-2-3-3-5-2-1-3-1-3-3 1-1 1-1 3-1v-2c2-1 4-1 5-3z" class="Q"></path><path d="M454 513c0 1 2 7 2 8l7 19 33 77 3 6h-1c-1 0-1-1-2-2h0-1c-2-1-3-3-4-5l-1-1c-1-1-4-3-4-5l-1-1h0v-1 4c1 2 2 5 2 7-1 0-2 0-3 1-4-2-8-3-11-4h-1-7c1 0 2-1 3-1h3c-1-1-2-1-3-1v-1l10 2c3 1 5 2 8 4-5-6-7-13-10-20l-6-13c-3-9-6-17-10-25 0-2-1-4-2-6-1-1-3-1-5-1l-1 1-1-1c2-1 3-1 4-3l-2-5-5-14-1-1v-1c-1 0-2 0-2 1-1 1-2 1-2 2h-3l2-2c4-3 6-7 9-11l3-7z" class="T"></path><path d="M491 616c1-1 1-2 1-4 1 1 2 3 3 5h1l3 6h-1c-1 0-1-1-2-2h0-1c-2-1-3-3-4-5z" class="V"></path><path d="M457 547l3-1h1c2 6 5 11 7 16l-3-3h-1c0-1 0 0-1-1-1 0-1-2-2-3l-4-8z" class="B"></path><path d="M463 558c1 1 1 0 1 1h1l3 3c1 2 3 5 2 6l8 17c2 6 6 12 8 19l4 11c-1-1-4-3-4-5l-1-1h0v-1 4c-2-4-3-9-5-13l-9-19-8-22z" class="L"></path><path d="M470 568l8 17c-2-1-2-2-3-4s-1-3-3-4h0c-1-3-1-6-2-9z" class="B"></path><path d="M454 513c0 1 2 7 2 8l7 19h-1c3 12 10 22 13 34h0c-1-2-2-5-3-7-3-7-6-15-10-20l-1-1h-1l-3 1-5-14v-2h1v-3l1-3h0c-1-1-1-2-2-2 1-1 2-2 2-3h-3l3-7z" class="Z"></path><g class="I"><path d="M454 513c0 1 2 7 2 8h0c-1 3 2 9 3 11v1 2h0c-1-1-1-2-2-3-1-3-1-5-3-8v1c-1-1-1-2-2-2 1-1 2-2 2-3h-3l3-7z"></path><path d="M454 525c1 2 1 4 2 6 1 5 3 10 4 15l-3 1-5-14v-2h1v-3l1-3z"></path></g><path d="M451 520h3c0 1-1 2-2 3 1 0 1 1 2 2h0l-1 3v3h-1v2l5 14 4 8c1 1 1 3 2 3l8 22 9 19c2 4 3 9 5 13 1 2 2 5 2 7-1 0-2 0-3 1-4-2-8-3-11-4h-1-7c1 0 2-1 3-1h3c-1-1-2-1-3-1v-1l10 2c3 1 5 2 8 4-5-6-7-13-10-20l-6-13c-3-9-6-17-10-25 0-2-1-4-2-6-1-1-3-1-5-1l-1 1-1-1c2-1 3-1 4-3l-2-5-5-14-1-1v-1c-1 0-2 0-2 1-1 1-2 1-2 2h-3l2-2c4-3 6-7 9-11z" class="P"></path><path d="M451 520h3c0 1-1 2-2 3 1 0 1 1 2 2h0l-1 3v3h-1v2c-1-2-1-3-2-4h-1c1 1 1 2 1 3l2 7-2-4c0-1-1-2-1-3h-1l-1-1v-1c-1 0-2 0-2 1-1 1-2 1-2 2h-3l2-2c4-3 6-7 9-11z" class="W"></path><path d="M453 528c-1-1-2-1-2-2h-1 0l1-3h1c1 0 1 1 2 2h0l-1 3z" class="j"></path><path d="M443 489l1-1 2 2 1 1c1 1 2 3 3 4h1c-1-2-2-3-3-5v-1c1 1 2 1 2 2 2 0 4 1 5 1h5c1 1 2 2 2 4v3l-3 3-1 2-4 9-3 7c-3 4-5 8-9 11l-2 2c-4 4-9 8-14 9-12 5-24 7-37 5-2 1-7-1-9-2l-1-1c-2 1-6-1-9-2h0l-9-4c-2-1-7-2-8-4-1 0-1-1-1-2l-10-5h2c11 6 25 11 38 11h2c14 1 25-3 35-12h0l5-7c2-4 4-7 2-11 0-1-2-3-4-3v-1c1 1 2 1 3 2h1c0-2-2-2-3-3h1c1 0 3-1 4 0 2 1 2 2 3 4h5c1 1 0 3 0 4h4c2-3 3-7 3-10-1-2-2-2-3-3l-1-1v-1c1-3 2-5 4-7z" class="G"></path><path d="M429 536h1 1 1 1 1c-2 2-5 3-7 4l-6 2-1-1c3-1 6-3 9-5z" class="M"></path><path d="M420 541l1 1 6-2-1 2c-12 5-24 7-37 5 6-1 13 0 19-2 4-1 8-2 11-4h1z" class="N"></path><path d="M443 489l1-1 2 2v1 4l-1 1-2-1h0c1 3 2 4 2 7h0c0 6-2 13-5 18l-1-1c0-1 1-2 1-4h1c0-1-1-2-1-3h-5l1-1h4c2-3 3-7 3-10-1-2-2-2-3-3l-1-1v-1c1-3 2-5 4-7z" class="Y"></path><path d="M443 489l1-1 2 2v1 4l-1 1-2-1c-1 0-2 0-3-1 1 0 1-1 2-1h1s1-1 1-2c-1 0-1-1-1-1v-1z" class="h"></path><path d="M448 502v3h1l2-3v7l-3 9c-1 2-3 4-4 7s-8 9-10 11h-1-1-1-1-1l7-5c8-9 11-17 12-29z" class="P"></path><path d="M446 490l1 1c1 1 2 3 3 4h1c-1-2-2-3-3-5v-1c1 1 2 1 2 2 2 0 4 1 5 1 1 2 2 3 1 5v1h0c0 1-1 2-1 2-1 2-1 4-2 6l-1-1h-1v4h0v-7l-2 3h-1v-3l-3-6 1-1v-4-1z" class="N"></path><path d="M446 491c3 4 5 6 5 11l-2 3h-1v-3l-3-6 1-1v-4zm-11 21h5c0 1 1 2 1 3h-1c0 2-1 3-1 4l1 1c-10 14-23 22-39 25-4 1-7 1-11 1-3-1-6-2-10-1l-1-1c1 0 1 0 1-1 2 0 6 1 8 1 4 0 9-1 14-1 9-2 18-7 25-13l6-6 4-6c1-1 2-2 2-4l-4-2h0z" class="f"></path><path d="M460 492c1 1 2 2 2 4v3l-3 3-1 2-4 9-3 7c-3 4-5 8-9 11l-2 2c-4 4-9 8-14 9l1-2c2-1 5-2 7-4s9-8 10-11 3-5 4-7l3-9h0v-4h1l1 1c1-2 1-4 2-6 0 0 1-1 1-2h0v-1c1-2 0-3-1-5h5z" class="V"></path><path d="M442 531c0-2 4-5 5-7l-1-1 3-6 8-17h1l1 2-1 2-4 9-3 7c-3 4-5 8-9 11z" class="T"></path><path d="M424 503c1 0 3-1 4 0 2 1 2 2 3 4h5c1 1 0 3 0 4l-1 1h0l4 2c0 2-1 3-2 4l-4 6-6 6c-7 6-16 11-25 13-5 0-10 1-14 1-2 0-6-1-8-1 0 1 0 1-1 1-2 1-6-1-9-2h0l-9-4c-2-1-7-2-8-4-1 0-1-1-1-2l-10-5h2c11 6 25 11 38 11h2c14 1 25-3 35-12h0l5-7c2-4 4-7 2-11 0-1-2-3-4-3v-1c1 1 2 1 3 2h1c0-2-2-2-3-3h1z" class="L"></path><path d="M427 530c1-4 3-7 4-11l2-2h1c1 1 2 1 3 1l-4 6-6 6z" class="K"></path><path d="M434 517c1 1 2 1 3 1l-4 6h-1c0-3 1-4 2-7z" class="D"></path><path d="M423 503h1c3 1 4 2 5 5 1 5 0 10-3 14v1h0c0-2 0-3-2-4 2-4 4-7 2-11 0-1-2-3-4-3v-1c1 1 2 1 3 2h1c0-2-2-2-3-3z" class="Y"></path><path d="M424 519c2 1 2 2 2 4h0c-4 6-7 9-13 12 2-3 6-6 6-9h0l5-7z" class="g"></path><defs><linearGradient id="f" x1="376.284" y1="547.618" x2="401.915" y2="519.52" xlink:href="#B"><stop offset="0" stop-color="#b3b1b4"></stop><stop offset="1" stop-color="#dfe1e0"></stop></linearGradient></defs><path fill="url(#f)" d="M382 538h2c14 1 25-3 35-12 0 3-4 6-6 9-12 7-24 7-38 5-1 1-2 1-3 1h-1l-1 1h0l-9-4c-2-1-7-2-8-4-1 0-1-1-1-2l-10-5h2c11 6 25 11 38 11z"></path><path d="M352 532c7 3 15 7 23 8-1 1-2 1-3 1h-1l-1 1h0l-9-4c-2-1-7-2-8-4-1 0-1-1-1-2z" class="S"></path><path d="M669 560l-1 4h0 1 0l-1 5v1c2-4 2-7 3-10 1-2 3-3 4-5v-1c1-1 1-2 3-3v2c-1 5-4 10-6 14l-47 109c-4 6-5 12-7 19l-3 8c0 1 1 2 1 2v1h-1c-4-3-8-4-11-7l1-1c1-1 2-2 2-3l1-2v-6c-1-2 0-4 1-6h-1c3-10 6-20 11-29 2-4 5-8 7-12 3-6 5-12 8-18l2-3c1-2 1-4 1-5 1-3 8-5 10-8h1c-1-3-1-5 0-9h1c1-2 2-4 4-6l1 1c3-6 5-13 8-19 2-5 5-10 6-15 1-2 1-4 2-5 0 1-1 5-1 7z" class="W"></path><path d="M653 591l1 1c3-6 5-13 8-19 2-5 5-10 6-15 1-2 1-4 2-5 0 1-1 5-1 7l-12 28c-2 4-5 8-7 12v2l1 2h0c-1 1-1 2-2 3 0-1-1-1-1-1-1-3-1-5 0-9h1c1-2 2-4 4-6z" class="Z"></path><path d="M648 606s1 0 1 1h0c-1 2-3 3-5 5l-2 1c-3 9-7 18-10 27l-12 33-7 17c-1 3-2 7-3 10h-1-1c0-2 0-4-1-5l1-2v-6c-1-2 0-4 1-6h-1c3-10 6-20 11-29 2-4 5-8 7-12 3-6 5-12 8-18l2-3c1-2 1-4 1-5 1-3 8-5 10-8h1z" class="B"></path><path d="M608 687c2-6 5-12 8-18-1 3-2 5-1 8l-7 16v-6z" class="P"></path><path d="M628 641c0-1 1-2 2-3h0c0 3-4 10-3 12l-12 27c-1-3 0-5 1-8l6-16c1-3 2-6 4-8l2-4z" class="X"></path><path d="M627 650c-2 7-5 16-8 22l1 1-7 17c-1 3-2 7-3 10h-1-1c0-2 0-4-1-5l1-2 7-16 12-27z" class="C"></path><path d="M613 690l-1-2c0-3 5-13 7-16l1 1-7 17z" class="U"></path><path d="M648 606s1 0 1 1h0c-1 2-3 3-5 5l-2 1c-3 9-7 18-10 27l-12 33-1-1c3-6 6-15 8-22-1-2 3-9 3-12h0c-1 1-2 2-2 3-2 1-2 3-3 4-4 6-6 11-9 17s-6 12-7 19h-1c3-10 6-20 11-29 2-4 5-8 7-12 3-6 5-12 8-18l2-3c1-2 1-4 1-5 1-3 8-5 10-8h1z" class="Y"></path><path d="M635 625v1c0 4-2 5-4 9h0l-1 1v-1l5-10z" class="P"></path><path d="M320 438c1 1 1 2 1 3 0 2 1 3 1 5l2 6 4 12v1 1c-1 2 1 6 2 8v1c1 2 2 3 4 5l3-3c2-2 4-3 6-5v-2h-1 0l3-1 1 1c-1 3-4 5-6 7l2 1 1-2h1l1 1c0 3-1 5 0 7h-1-2l7 17c1 4 1 8 3 11v1 3h0c1 1 1 2 2 3v2c1 1 1 1 1 2s1 2 2 3l2 2-2 1c0 1 0 0 1 1 3 2 8 2 12 3h1c6-1 9-4 13-8 2-2 2-3 3-5h1l-1 2 1 1c2-1 2-2 4-2 0 1 0 1-1 2v2c-1 3-4 6-4 9l-2 2c-1 0-2 0-3 1v1c-13 0-27-5-38-11-2-2-4-2-6-3-2-2-4-4-6-5v-1-1c-5-5-9-10-13-15l1-2c-7-13-10-28-7-43 0-2 1-4 1-5l1-1 5-13z" class="j"></path><path d="M315 458h2c1 2 2 4 2 6l-1-1c-2 0-2-1-3-2v-3z" class="E"></path><path d="M315 451c1 2 2 4 2 7h0-2l-2-1c0-2 1-4 1-5l1-1z" class="H"></path><path d="M384 525c2-2 2-3 3-5h1l-1 2 1 1c-1 4-4 7-8 9-3 1-6 2-9 2v-1c6-1 9-4 13-8z" class="Q"></path><path d="M332 518v-1c-5-5-9-10-13-15l1-2c5 7 11 14 18 19l3 1 6 3c4 2 7 5 10 8-9-3-17-7-25-13z" class="Y"></path><path d="M329 492l1-1v1l1-1c1 1 1 1 1 2 0 2 1 3 2 4h1l9 20c1 3 2 4 3 6l-6-3-3-1 1-1h1 0c-4-8-7-17-11-26z" class="L"></path><path d="M341 520c1-1 0-1 0-2l1-1h0 1 1c1 3 2 4 3 6l-6-3z" class="H"></path><path d="M319 464l1 1h0c0-3-1-5-1-8l16 40h-1c-1-1-2-2-2-4 0-1 0-1-1-2l-1 1v-1l-1 1c-1-2-2-6-3-7l-6-15-3-2c-1-2-2-4-2-7 1 1 1 2 3 2l1 1z" class="O"></path><path d="M342 470l3-1 1 1c-1 3-4 5-6 7l2 1 1-2h1l1 1c0 3-1 5 0 7h-1-2l7 17c1 4 1 8 3 11v1l-11-27h0c2 5 4 11 6 17 1 3 2 6 2 10h0l-4-11-3-8c0-1-1-1-1-2-1-1-1 0-1-1v-3l-3-6-2-2c1 3 2 5 3 7 0 1 0 1 1 2 0 1 1 1 1 2 0 2 1 4 2 5 2 5 3 11 6 17 1 3 3 8 3 11h0l-17-44 3-3c2-2 4-3 6-5v-2h-1 0z" class="Y"></path><path d="M343 476h1l1 1c0 3-1 5 0 7h-1-2l-2-4h0c-1-1 0-1 0-3l2 1 1-2z" class="F"></path><path d="M340 480c1 1 2 0 3 1s1 2 1 3h-2l-2-4z" class="E"></path><path d="M313 457l2 1v3c0 3 1 5 2 7l3 2 6 15c1 1 2 5 3 7 4 9 7 18 11 26h0-1l-1 1c-7-5-13-12-18-19-7-13-10-28-7-43z"></path><path d="M317 468l3 2 6 15c1 1 2 5 3 7 4 9 7 18 11 26h0-1c-3-3-4-6-5-10l-10-22c-3-6-5-12-7-18z" class="f"></path><path d="M834 173c1-1 2-2 3-2s1 0 2 1l-1 1 1 1v2c-1 1-5 3-5 4 0 0 0 2 1 2 1-2 4-3 6-4l1-1c2-1 3-1 6-1 1 0 2 0 3 1l1 1c-1 1-2 1-2 2s0 2 1 3h-1l-2-1 1 2c-2 1-2 2-3 4v1c1 0 2 1 2 2 1 1 0 1 2 3 1 0 1 0 2-1l1 1-1 1h0l-3 6 2 2c1 1 1 2 1 3h1c2 1 4 2 6 4h-3l-1 1c-5-1-10-2-15 0-1-1-1-2-2-3-2 1-3 2-5 2v-3c0-1 1-2 1-2l-1-1v1c-1 0-2 1-2 1h-1-2v2l-1-1v-1h-1c-2-1-4-2-5-3-3 0-5-1-7-1h0c-1 0-1-1-2-1h-1c-2 0-5-1-6 0h0v1s-1-1-2-1c0 0-1 1-2 1l-1 1-1 1-2 3c-2-1-2 0-4 1-1 0-1 1-2 2 3 3 6 5 8 8h1l1-1c0 1 0 1 1 2v1c1 2 3 5 5 7l3 3h1l-2 2v-1c-2-1-4-2-6-1h-3l-1-1c-1 1-2 2-4 2v-2l1-1c0-2 0-4-2-5h0c-1 0-1 0-2 1h1v1h0c-1 1-1 1-1 2v1 1h0l-2-2-1 1 1 1-3 3-1-1c1-1 2-2 2-3-1-2-2-3-3-4h0c-2-1-3-1-5-1v-1c-2 1-2 2-3 3h-1 0l1-2-1-1-2 2h-2c-2-1-2-1-2-3l-3 3 1-1-1-1c-1 1-1 1-3 1 3-3 5-6 8-10 2-3 4-7 7-10 3-2 7-4 10-6 2-1 4-3 6-4 4-2 8-5 12-6h1c5-3 12-6 18-8 2-2 6-3 9-5l-1-1z" class="l"></path><path d="M797 221l-2 1-1-1c-1-1-1-1-1-2-1-1-1-1-1-2-1 0-1-1-1-1v-2c-1-1-1-2-1-4h1c3 3 6 5 8 8h1l1-1c0 1 0 1 1 2v1c1 2 3 5 5 7l3 3h1l-2 2v-1c-2-1-4-2-6-1h-3l-1-1c-1 1-2 2-4 2v-2l1-1c0-2 0-4-2-5h0 3v-2z" class="c"></path><path d="M797 221v-1l1-1c0 1 1 1 1 2v2c1 1 4 3 5 4 0 0 1 1 2 1l1-1 3 3h1l-2 2v-1c-2-1-4-2-6-1h-3l-1-1c-1 1-2 2-4 2v-2l1-1c0-2 0-4-2-5h0 3v-2z" class="e"></path><path d="M834 173c1-1 2-2 3-2s1 0 2 1l-1 1 1 1v2c-1 1-5 3-5 4 0 0 0 2 1 2l-1 1h0c0 1-1 2-1 2l3 3v1c-1 0-1-1-2-1h-1-2c0-1 0-2-1-3 0 0 0-1 1-1l-3-2h-1c0 1-1 1-2 1v1h-1l-1 1c0 1 1 2 2 3v1h0l-2 1c1 0 1 1 1 2h-1c-4 1-3-3-6-5h-1 0-1l-1 1 2 2-1 1c-1-1-1-2-2-3-1 0-1 0-2 1 0 1 0 1 1 1v1c-1-1-3-1-3-2l-1 1h-2v1l1 1v2h-1v-2l-1-1-1 1h0c-1 0-1 1-2 1-1 1-1 1-3 2v1h1 0c1-1 1-1 2-1l1-1c2 0 2 1 4 1h1c-1 1-2 1-3 1l-1-1c-2 0-2 1-4 1l2 2h-2c0-1-1-1-2-1v2c0 1-2 2-3 3v-1c0-1 0 0-1-1v-1c-1-1-2-2-3-2v1h1c0 2 0 1 2 2l-1 1 1 2h-1c-1 0 0 0-1 1h1l3-1v1c-2 1-2 1-4 1-2 1-3 1-5 0h-1v-2c1 1 1 1 2 1v-1h1 1c-1-1-1 0-3-1l-1-1h-1v1c1 2-1 3 1 5h-1 0l-1 1h-1 0c-2 1-2 2-3 4l-1-1v1c-1-1-2-1-3-2l-1 1c1 1 1 1 1 2l3 2-3-1-2 2h-1v-2h-1v1c0 2 0 2-1 3h-2l-1 2h2c2 0 3 1 4 2v1l-2-2h-2l-3 3 1-1-1-1c-1 1-1 1-3 1 3-3 5-6 8-10 2-3 4-7 7-10 3-2 7-4 10-6 2-1 4-3 6-4 4-2 8-5 12-6h1c5-3 12-6 18-8 2-2 6-3 9-5l-1-1z" class="U"></path><path d="M816 187c1-1 2-1 3-1h0 1c0 1 1 2 1 2v-2l1-1v1c1 1 1 2 3 3h0l-2 1c1 0 1 1 1 2h-1c-4 1-3-3-6-5h-1 0z" class="f"></path><path d="M838 173l1 1v2c-1 1-5 3-5 4 0 0 0 2 1 2l-1 1h0c0 1-1 2-1 2l3 3v1c-1 0-1-1-2-1h-1-2c0-1 0-2-1-3 0 0 0-1 1-1 3-4 4-8 7-11z" class="Y"></path><path d="M848 176c1 0 2 0 3 1l1 1c-1 1-2 1-2 2s0 2 1 3h-1l-2-1 1 2c-2 1-2 2-3 4v1c1 0 2 1 2 2 1 1 0 1 2 3 1 0 1 0 2-1l1 1-1 1h0l-3 6 2 2c1 1 1 2 1 3h1c2 1 4 2 6 4h-3l-1 1c-5-1-10-2-15 0-1-1-1-2-2-3-2 1-3 2-5 2v-3c0-1 1-2 1-2l-1-1v1c-1 0-2 1-2 1h-1-2v2l-1-1v-1h-1c-2-1-4-2-5-3-3 0-5-1-7-1h0c-1 0-1-1-2-1h-1c-2 0-5-1-6 0h0v1s-1-1-2-1c0 0-1 1-2 1v-1c1 0 2-1 2-1l1-1h1 4c-1-1-1-1-1-2l1-1c2-2 2 1 4 2l1-1-1-1c2 0 2 1 3 3h3c1-2 0-2-1-4 2 1 2 1 4 0 0-1-1 0-2-1s-2-3-2-4c-1-1-1-2-2-3h1c3 2 2 6 6 5h1c0-1 0-2-1-2l2-1h0v-1c-1-1-2-2-2-3l1-1h1v-1c1 0 2 0 2-1h1l3 2c-1 0-1 1-1 1 1 1 1 2 1 3h2 1c1 0 1 1 2 1v-1l-3-3s1-1 1-2h0l1-1c1-2 4-3 6-4l1-1c2-1 3-1 6-1z" class="a"></path><path d="M824 184v2c1-1 1 0 2-1 1 1 2 1 2 3h0v2l-2 1h-1v-3c-1-1-2-2-2-3l1-1z" class="d"></path><path d="M838 208h0 3c4-1 11 0 15 2l-1 1c-5-1-10-2-15 0-1-1-1-2-2-3z" class="f"></path><path d="M848 176c1 0 2 0 3 1l1 1c-1 1-2 1-2 2s0 2 1 3h-1l-2-1c-1-1-1-1-2-1-1 1-2 2-3 2l-1-1v-1c-2 1-2 1-2 3v1c-1-1-1-1-2-1v-1l-1-1-1 1c0 1 0 2 1 3h-1v1 1l-3-3s1-1 1-2h0l1-1c1-2 4-3 6-4l1-1c2-1 3-1 6-1z" class="B"></path><path d="M848 176c1 0 2 0 3 1l1 1c-1 1-2 1-2 2s0 2 1 3h-1l-2-1c-1-1-1-1-2-1h-3c0-2 0-2 1-3h3v-1l1-1z" class="L"></path><path d="M822 195l-1 2h1l2-1v1s-1 0-1 1c-1 0-1 0-2 1 1 1 1 1 3 1v-1h1c1-1 1-1 1-2 1-1 1-1 2-1h0 2c-1 2-1 3-2 4l-1 1 1 1 1-1v1l1 1c1 0 1 0 1-1h0l1-1c0 1 0 1 1 2h-1c-1 1-1 2-1 3h0-1-2v2l-1-1v-1h-1c-2-1-4-2-5-3-3 0-5-1-7-1h0c-1 0-1-1-2-1h-1c-2 0-5-1-6 0h0v1s-1-1-2-1c0 0-1 1-2 1v-1c1 0 2-1 2-1l1-1h1 4c-1-1-1-1-1-2l1-1c2-2 2 1 4 2l1-1-1-1c2 0 2 1 3 3h3c1-2 0-2-1-4 2 1 2 1 4 0z" class="R"></path><path d="M821 203h3 2c1 1 1 1 1 2v1h-1c-2-1-4-2-5-3z" class="c"></path><path d="M441 288l1-1 2 1h1l6 8c1 1 4 5 6 5h0 3c-1 1-2 0-2 2 1 2 3 4 4 5l7 7c3 3 6 6 9 8l2 2h1 1v1l2-1 1 1c0 1 1 2 2 3 1 0 1 0 2-1l2 1c0 1-1 1-1 2 0 0 2 3 3 4h1c1 2 2 4 4 5l1 1c0 1 0 1 1 2h0-1-1l3 2c-2 1-3 1-4 1-2 1-4 1-5 2-3 2-6 3-9 3l-1 3c2 3 4 5 6 8 2 2 3 4 6 5h-2l-10 5c-1 1-3 2-5 2l1-1c1-1 3-2 5-3l-2-2 2-2h0c1 0 2 0 3-1l-1-1-2-2 1-1h0c-1-2-1-3-3-3v-1l-1-2h-1c0-1 0-1-1-2-1 0-2 0-3 1h-1c-2 0-4 1-5 1l-2 1h0l-4 1h-2c0-2 1-3 1-5h-1v-4-2-1h-1v-1l2-2c1 0 1-1 1-2h-1c-2 0-4 1-6 2h0-1c-1 0-2 1-2 1l-1 2v-4c-1-2-3-3-4-5l-2-5v-3c1-1 2-3 2-4s0-1-1-1l-1 1-1 1-1 1-2 2c-3 0-5-2-7-4-2-3-3-6-2-9 1-5 6-8 10-11 0-3-1-5-2-8-1-1-1-2 0-2 0 0 1 1 1 2s0 2 1 3h0 1 1c0 1 0 1 1 2 1-1 1 0 2-1-1-1-1-2-2-2 0-1 1-2 0-3 0-1-1-1-1-2 1 0 0 0 1-1h0c-1-2-2-3-5-4z" class="M"></path><path d="M465 335c3 0 4 1 5 3h1 1c1 1 1 3 1 4l-1 1 1 1h0l1-1c2 3 4 7 8 7-1 0-2 1-3 1-6-4-10-10-14-16z" class="O"></path><path d="M471 338h1c1 1 1 3 1 4h-2v-4z" class="H"></path><path d="M478 352c5 4 8 10 13 14l-6 3-2 1-2-2 2-2h0c1 0 2 0 3-1l-1-1-2-2 1-1h0c-1-2-1-3-3-3v-1l-1-2h-1c0-1 0-1-1-2v-1z" class="G"></path><path d="M481 368l2-2c0 1 1 2 2 3l-2 1-2-2z" class="J"></path><path d="M446 292l5 5c0 1 3 3 3 4-2 1-4 2-6 2l-4 1c0-2 0-3-1-5h1 1c0 1 0 1 1 2 1-1 1 0 2-1-1-1-1-2-2-2 0-1 1-2 0-3 0-1-1-1-1-2 1 0 0 0 1-1h0z" class="H"></path><path d="M462 331h0c1 0 2 1 3 1h1l1-1c2 2 3 2 5 2 1 0 1-1 2-1v1h3l1 2h0c-3 2-3 3-3 6h-1s-1 0-1 1c0-1 0-3-1-4h-1-1c-1-2-2-3-5-3l-3-4z" class="C"></path><path d="M454 324c-5-6-8-11-10-19 3-1 6-2 9-2-1 1-2 2-3 2h-1c1 1 0 1 1 1l-1 1c0 4 4 8 3 11l-1 2h1c1-1 2-1 3-2 1 2 2 3 3 4h0c-1 1-2 2-4 2z" class="H"></path><path d="M468 321l1 1h3l1 1c-2 0-3 0-4 1h-1c-1 1-2 1-2 2 1 1 1 2 3 2h1v1c0 1-2 2-3 2l-1 1h-1c-1 0-2-1-3-1h0c-1 0-2-1-2-2l-6-5c2 0 3-1 4-2l2 1h1l1 1c0-1 0-1 1-1h0c2 0 3-1 5-2z" class="D"></path><path d="M454 324c2 0 3-1 4-2l2 1 1 2c2 2 4 3 5 7h-1c-1 0-2-1-3-1h0c-1 0-2-1-2-2l-6-5z" class="F"></path><path d="M458 322l2 1 1 2-1 1h-1c-1-2-1-2-1-4z" class="H"></path><path d="M457 318l1-1c1-1 1-1 3-1l1-1h3 0 0 4 0c3 3 6 6 9 8l2 2h-3-1c-2 0-2 0-3-2l-1-1h-3l-1-1c-2 1-3 2-5 2h0c-1 0-1 0-1 1l-1-1h-1l-2-1h0c-1-1-2-2-3-4h2 0z" class="Q"></path><path d="M457 318c3-1 5-2 8-2v1l-4 5 7-1c-2 1-3 2-5 2h0c-1 0-1 0-1 1l-1-1h-1l-2-1h0c-1-1-2-2-3-4h2 0z" class="C"></path><path d="M457 318h0c1 1 1 2 2 2 0 1-1 1-1 2-1-1-2-2-3-4h2z" class="F"></path><path d="M467 331c1 0 3-1 3-2v-1h-1c-2 0-2-1-3-2 0-1 1-1 2-2h1c1-1 2-1 4-1 1 2 1 2 3 2h1 3 1 1v1l2-1 1 1c0 1 1 2 2 3l-1 1v1l-1-1h-4 0l1 1-1 1h0c-2-1-2-1-4-1v1h1l-1 1h-3v-1c-1 0-1 1-2 1-2 0-3 0-5-2z" class="R"></path><path d="M484 325l1 1c0 1 1 2 2 3l-1 1c-2-1-4-3-5-5h1v1l2-1z" class="O"></path><path d="M453 303c1-1 2-1 3-1l4 6c1 1 5 4 5 6v1h-3l-1 1c-2 0-2 0-3 1l-1 1h0-2c-1 1-2 1-3 2h-1l1-2c1-3-3-7-3-11l1-1c-1 0 0 0-1-1h1c1 0 2-1 3-2z" class="G"></path><path d="M453 303c1-1 2-1 3-1l4 6h0c-2-1-3-3-4-4-1 0-2 1-2 1v1h-1c-1 2-1 3 0 5 0 2 1 2 2 3h1l-1 1v1 1c-1 1-1 1-3 1 1-3-3-7-3-11l1-1c-1 0 0 0-1-1h1c1 0 2-1 3-2z" class="E"></path><path d="M443 326c-1 0-1 0-1-1-3 0-5-2-6-4-2-2-2-5-1-7 1-4 4-6 7-8 2 3 4 7 4 10v4c0 2-1 4-2 5l-1 1z"></path><path d="M446 316c5 7 11 12 16 18-3 0-7 0-9 3h0 1c2-1 4-1 6-2h0c2 1 2 1 3 2v1h-2c-1 1-1 1-2 1h0c-2 1-3 1-4 3-1 0-2 1-2 1l-1 2v-4c-1-2-3-3-4-5l-2-5v-3c1-1 2-3 2-4s0-1-1-1l-1 1-1 1-1 1h-1l1-1c1-1 2-3 2-5v-4z" class="L"></path><path d="M460 335c1 0 2 0 3-1 3 3 6 7 8 10 1 2 3 4 4 5s3 2 3 3v1c-1 0-2 0-3 1h-1c-2 0-4 1-5 1l-2 1h0l-4 1h-2c0-2 1-3 1-5h-1v-4-2-1h-1v-1l2-2c1 0 1-1 1-2h-1c-2 0-4 1-6 2h0-1c1-2 2-2 4-3h0c1 0 1 0 2-1h2v-1c-1-1-1-1-3-2z" class="K"></path><path d="M461 352v-4-2-1h-1v-1l4 1-1 1-1 1c0 2 0 3 2 4 1 0 2 0 3-1h5c-1 1 0 1-1 2l1 1c-1 0-2 0-3 1h-2v2h0l-4 1h-2c0-2 1-3 1-5h-1z" class="D"></path><path d="M462 352l1 1v1l2-1h1c0 1 0 2 1 3l-4 1h-2c0-2 1-3 1-5z" class="O"></path><path d="M489 328l2 1c0 1-1 1-1 2 0 0 2 3 3 4h1c1 2 2 4 4 5l1 1c0 1 0 1 1 2h0-1-1l3 2c-2 1-3 1-4 1-2 1-4 1-5 2-3 2-6 3-9 3l-1 3-3-3c1 0 2-1 3-1-4 0-6-4-8-7l-1 1h0l-1-1 1-1c0-1 1-1 1-1h1c0-3 0-4 3-6h0l-1-2 1-1h-1v-1c2 0 2 0 4 1h0l1-1-1-1h0 4l1 1v-1l1-1c1 0 1 0 2-1z" class="U"></path><path d="M480 337h2v3l-2 2h-3v-1l3-4z" class="C"></path><path d="M489 328l2 1c0 1-1 1-1 2 0 0 2 3 3 4h1c1 2 2 4 4 5l1 1c0 1 0 1 1 2h0-1-1-1c-1-1-2-3-3-4l-4-4-1-1v-1c0-1-2-2-3-2v-1l1-1c1 0 1 0 2-1z" class="B"></path><path d="M475 341h1c1 2 1 3 3 3s3-1 5-2v-2h0 1 2c0 2 0 2 1 3h1v1l2 1-1 1-3 1-1 1c-1 1-3 2-4 2-4 0-6-4-8-7l-1 1h0l-1-1 1-1c0-1 1-1 1-1h1z" class="S"></path><path d="M475 252v-3l1-1c3 1 5 3 8 4l1 2c1 0 3 1 4 2 4 2 8 4 12 5 5 1 8 1 12 1s7 1 10 0c2 0 4-1 6 0h0c1 0 2 0 3 1h0v1 2l2 5c2 1 4 2 6 2l4 1 1 1h0v1c-3 2-5 2-8 3s-4 3-7 2h-4l3 2c0 1 0 2-1 3h-3l-1 2s-1-1-2-1c-1-1-2-1-4-1-1-1-3-2-4-1-1 0-1 0-1 1-5 2-8 2-13 2h-14v1c-1 0-3 0-4 1h-3-1c0 1-1 1-1 1l-2 1h-2l-3-1-11-3 2-1c-1-1-1-1-1-2h2v-1c-2 0-3-1-5-1h-1c-1 0-2-1-3-1s-2-1-4-1c2-1 2-1 3-1h1c2 0 3 0 4-2 1 0 2 1 4 1 5 1 11 2 16 2 0-1-1-1-2-1s-3-1-4-1h0c1 0 1-1 2-1 0-2-2-1-2-2h2l2 1 1-1c-1-1-1-1-2-1h-1v-1h1v-2-1c-5-3-9-8-14-10-2-1-12-6-13-8 3 0 4 0 6 1h1 1l10 6h1 0c2-1 2-2 2-3-2 0-4-1-5-2h1c1-2 2-3 4-3l1 1c0-1 0-1 1-1 1-1 4 0 5 0h0z" class="H"></path><path d="M490 283c3-2 5-2 9-2l-1 2h-8z" class="C"></path><path d="M499 281h2c3-1 4-2 8-1l-2 1c-3 1-6 2-9 2l1-2zm41-8l4 1 1 1h0-1c-7 3-13 4-20 2l5-1c2 0 3 0 5-1h3c1-1 2-1 3-2z" class="L"></path><path d="M540 273l4 1 1 1h0-1-10 3c1-1 2-1 3-2z" class="B"></path><defs><linearGradient id="g" x1="488.319" y1="278.157" x2="503.56" y2="270.622" xlink:href="#B"><stop offset="0" stop-color="#2b2a2a"></stop><stop offset="1" stop-color="#525354"></stop></linearGradient></defs><path fill="url(#g)" d="M498 268c2 2 3 2 5 2h2 0c-1 2-1 2-3 3l-1 1 2 2c1 0 1 0 1-1l1 1c-4 2-9 3-13 2h-1 0v1h-3v-1c-2-1-4-2-5-3h2c1 1 0 1 1 1 0-1 1-1 1-1l1 1c1 0 3 0 4-1 2 0 5-4 6-6v-1z"></path><path d="M509 280l2-1c6-2 10 0 15 2l3 2c0 1 0 2-1 3h-3l-1 2s-1-1-2-1c-1-1-2-1-4-1-1-1-3-2-4-1-1 0-1 0-1 1-1-1-2-1-2-2l-2 2-1-1v-2l-1-2 2-1z" class="s"></path><path d="M508 283c3-2 4-2 7-3h2l1 2 4 1 1 2c0 1 0 1-1 2-1-1-2-1-4-1-1-1-3-2-4-1-1 0-1 0-1 1-1-1-2-1-2-2l-2 2-1-1v-2z" class="n"></path><defs><linearGradient id="h" x1="479.768" y1="274.903" x2="482.652" y2="290.931" xlink:href="#B"><stop offset="0" stop-color="#d5b57c"></stop><stop offset="1" stop-color="#f4dfaa"></stop></linearGradient></defs><path fill="url(#h)" d="M457 278c1 0 2 1 4 1 5 1 11 2 16 2l13 2h8c3 0 6-1 9-2l1 2v2l1 1 2-2c0 1 1 1 2 2-5 2-8 2-13 2h-14v1c-1 0-3 0-4 1h-3-1c0 1-1 1-1 1l-2 1h-2l-3-1-11-3 2-1c-1-1-1-1-1-2h2v-1c-2 0-3-1-5-1h-1c-1 0-2-1-3-1s-2-1-4-1c2-1 2-1 3-1h1c2 0 3 0 4-2z"></path><path d="M462 284l12 2 12 2v1c-1 0-3 0-4 1h-3-1c0 1-1 1-1 1l-2 1h-2l-3-1-11-3 2-1c-1-1-1-1-1-2h2v-1z" class="m"></path><path d="M462 284l12 2h0c-2 1-6 1-8 1l1 1h0c1 0 2 1 2 1l1 2-11-3 2-1c-1-1-1-1-1-2h2v-1z" class="o"></path><path d="M501 261c5 1 8 1 12 1s7 1 10 0c2 0 4-1 6 0h0c1 0 2 0 3 1h0v1 2l2 5c2 1 4 2 6 2-1 1-2 1-3 2h-3c-2 1-3 1-5 1l-5 1-6-2c-3 0-5-1-8 0-2 1-3 1-5 1l-1-1c0 1 0 1-1 1l-2-2 1-1c2-1 2-1 3-3h0 2c-1-1 0-1-1-2h0-1c-2-1-2-1-4-1l-1 1-1-2v-1h1v-1l2-1c0-1 0 0-1-1v-1z" class="e"></path><path d="M511 268c1 1 2 2 4 2-1 1-2 1-2 2h2c1-2 1-4 1-6l1 1s0 1 1 1v-3l1 1v4h-1v1h4l1 1c-1 2-2 2-3 2l-2 1c-3 0-5-1-8 0-2 1-3 1-5 1l-1-1c0 1 0 1-1 1l-2-2 1-1c2-1 2-1 3-3h0 2c3 0 2-1 4-2z" class="a"></path><path d="M507 270c3 0 2-1 4-2v3c-2 1-3 0-5 2 1 1 2 1 4 2-2 1-3 1-5 1l-1-1c0 1 0 1-1 1l-2-2 1-1c2-1 2-1 3-3h0 2z" class="R"></path><path d="M532 263v1 2l2 5c2 1 4 2 6 2-1 1-2 1-3 2h-3c-2 1-3 1-5 1l-5 1-6-2 2-1c1 0 2 0 3-2l-1-1h-4v-1h1 2v-1c2 0 2 0 3-1h1l1 1c2 0 4 0 6-1l-1-1-1 1 1-1-1-1c0-1 1-2 2-2v-1z" class="G"></path><defs><linearGradient id="i" x1="476.263" y1="272.829" x2="481.169" y2="255.862" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#444445"></stop></linearGradient></defs><path fill="url(#i)" d="M475 252v-3l1-1c3 1 5 3 8 4l1 2c1 0 3 1 4 2 4 2 8 4 12 5v1c1 1 1 0 1 1l-2 1v1h-1v1l1 2 1-1c2 0 2 0 4 1h1 0c1 1 0 1 1 2h-2-2c-2 0-3 0-5-2v1c-1 2-4 6-6 6-1 1-3 1-4 1l-1-1s-1 0-1 1c-1 0 0 0-1-1h-2c1 1 3 2 5 3v1l-5-3-1 1c-1 0-2-1-2-1-2-1-3-1-4-1s-2-1-2-1v-2-1c-5-3-9-8-14-10-2-1-12-6-13-8 3 0 4 0 6 1h1 1l10 6h1 0c2-1 2-2 2-3-2 0-4-1-5-2h1c1-2 2-3 4-3l1 1c0-1 0-1 1-1 1-1 4 0 5 0h0z"></path><path d="M475 252v-3l1-1c3 1 5 3 8 4l1 2c0 1 0 1 1 2-1 0-1 0-2-1h0c-1 1-2 1-3 1-1-1-1-1-2-1s-2-1-3-1v-1l-1-1z" class="B"></path><path d="M460 261c-2-1-12-6-13-8 3 0 4 0 6 1h1 1l10 6 18 15c1 1 3 2 5 3v1l-5-3-1 1c-1 0-2-1-2-1-2-1-3-1-4-1s-2-1-2-1v-2-1c-5-3-9-8-14-10z" class="V"></path><path d="M474 271v-1l-3-3c2 0 10 7 12 9l-1 1c-1 0-2-1-2-1-2-1-3-1-4-1s-2-1-2-1v-2-1z" class="G"></path><path d="M489 257v-1c4 2 8 4 12 5v1c1 1 1 0 1 1l-2 1v1h-1v1l1 2 1-1c2 0 2 0 4 1h1 0c1 1 0 1 1 2h-2-2c-2 0-3 0-5-2v1l-1-2h-1l-1 2c-1 1-3 3-4 3-4 1-5-1-8-3-1 0-2-1-3-2h1c1-1 0-1 1 0h1v-1-2c0-1 0-1 1-2h0v-1c1-2 3-2 5-2h1l-1-1v-1z" class="D"></path><path d="M484 262h0c1 1 1 1 2 1s1 0 2 1c1 0 1 0 2-1v3c-1 1-2 1-3 1s-2-1-3-2v-3h0z" class="C"></path><path d="M489 257v-1c4 2 8 4 12 5v1c1 1 1 0 1 1l-2 1v1h-1v1l1 2 1-1c2 0 2 0 4 1h1 0c1 1 0 1 1 2h-2-2c-2 0-3 0-5-2v-1l-1-1v-1l1-1-2-1c0-2-1-3-2-4h-1c2 3 2 4 2 8l-2 1h-1c0-2 1-3 2-4l-1-4c-1-2-2-2-4-3z" class="a"></path><defs><linearGradient id="j" x1="514.069" y1="487.393" x2="482.75" y2="482.816" xlink:href="#B"><stop offset="0" stop-color="#0b0b0a"></stop><stop offset="1" stop-color="#39393b"></stop></linearGradient></defs><path fill="url(#j)" d="M504 418h1v3l1 1c1 1 0 2 0 3h1c0 3 1 9 0 11-1 1-2 1-1 3v-1h1 1 1c0-1 1-1 1-2v-5c0 1 0 2 1 4 0-3 0-3 1-5 1 2 1 4 2 6v2l1-2h1l1 2v6h1l-1 65h-3v8c0 4-1 8-3 12h0c-1 0-1-1-2-2h0c-1 3-4 5-5 8-3-5-4-13-5-19l-3-15-8-25h1v-1c-1-2-1-2-1-3l-1-3c-1-1-1-2-1-3-2-3 4-3 5-6l-3 2-1-1c0-2 2-6 3-8h1c1-1 1-2 1-3v-1l2-3 1-3-1-2v-1l-3-3c-2 0-4 0-6-1 0-2 0-3 1-5 1-1 3-2 4-2 0-1 1-2 1-2 2 0 5 1 7 1h1c0-1 0-1-1-1 0-1 0-1-1-2l1-1v-1l3-1c1 0 2-1 2-2 1-1 1-2 1-2z"></path><path d="M504 483h1c0 4-5 6-7 9h0-1l-1-1c0-2 1-2 3-4h-1l6-3v-1z" class="O"></path><path d="M504 450c0 3 1 10-1 12v2h1l1 2-1 1h0-1l1-1h-1l-1 1h-1v-6h1v-3l-2-1-1-1c1-1 2-1 2-3l2-2 1-1z" class="F"></path><path d="M500 457l2 1v3h-1c-1 2-4 4-5 5s-2 2-3 2c-1 1-2 1-3 0l-1-1v-1c1-2 9-7 11-9z" class="K"></path><path d="M504 418h1v3l1 1c1 1 0 2 0 3 0 3 1 9-1 11v1c-1 0-1 1-2 1l2 2c-1 1-1 1-2 1h-1v1h3l-2 1v2l2 1v1h1c0 2-1 2-2 3l-1 1-2 2h-1l-2 2-7 5-3 2-1-1c0-2 2-6 3-8h1c1-1 1-2 1-3v-1l2-3 1-3-1-2v-1l-3-3c-2 0-4 0-6-1 0-2 0-3 1-5 1-1 3-2 4-2 0-1 1-2 1-2 2 0 5 1 7 1h1c0-1 0-1-1-1 0-1 0-1-1-2l1-1v-1l3-1c1 0 2-1 2-2 1-1 1-2 1-2z" class="L"></path><path d="M504 418h1v3l1 1c1 1 0 2 0 3 0 3 1 9-1 11l1-2-1-1c-2-2-2-4-4-5l-1-1v-1h1c-1-1-1-1-3-1v-1-1l3-1c1 0 2-1 2-2 1-1 1-2 1-2z" class="F"></path><path d="M499 435c1 1 1 2 1 3 0 3 1 5 0 7v3l-1 2 1 1h3l-2 2h-1l-2 2-7 5-3 2-1-1c0-2 2-6 3-8h1c1-1 1-2 1-3v-1l2-3 1-3h1c1 0 1 0 2-1 0-2 1-3 0-5h0l1-1v-1z" class="D"></path><path d="M495 443h1c1 0 1 0 2-1 0-2 1-3 0-5h0l1-1c0 5 1 10-2 14l-2 1c-1 1-1 2-2 3l-1-1 1-2-1-1v-1l2-3 1-3z" class="R"></path><path d="M492 450l1 1-1 2 1 1-1 1c2 0 2 0 3-1h1c1 0 1 0 2-1s1 0 2 0l-2 2-7 5-3 2-1-1c0-2 2-6 3-8h1c1-1 1-2 1-3z" class="U"></path><path d="M490 429c0-1 1-2 1-2 2 0 5 1 7 1h1l1 1-1 1v5 1l-1 1h0c1 2 0 3 0 5-1 1-1 1-2 1h-1l-1-2v-1l-3-3c-2 0-4 0-6-1 0-2 0-3 1-5 1-1 3-2 4-2z" class="Q"></path><path d="M490 429c0-1 1-2 1-2 2 0 5 1 7 1h1l1 1-1 1v5c-1 0-1 0-2-1v-2c-2-3-4-3-7-3z" class="U"></path><path d="M491 437c-2-1-3-1-4-2 0-2 0-2 2-4 1 0 3 0 5 1 1 1 1 2 2 3s1 1 1 2l1 1-2 2h-1-1l-3-3z" class="e"></path><path d="M510 431c0 1 0 2 1 4 0-3 0-3 1-5 1 2 1 4 2 6v2l1-2h1l1 2v6h1l-1 65h-3v8c0 4-1 8-3 12h0c-1 0-1-1-2-2h0c1-4 1-9 1-13v-21-57-5z" class="m"></path><path d="M514 438l1-2h1l1 2v6h1l-1 65h-3v-56-15z" class="q"></path><path d="M514 438l1-2h1l1 2v6c-2 5-1 12-1 18-1-3 0-6-2-9v-15z" class="d"></path><path d="M560 329c0 2 0 2 2 3h2 1v1 1h1c2 1 4 2 5 3l1-2h1 1 3c-1 5 0 8 1 13l1 2v1l-9 9c-2 1-4 3-5 4l-6 6c-3 3-6 5-9 8s-5 6-8 8l-1 1-12 12c-2 2-4 5-7 6 0 0-1 0-2-1l1-43v-1-17c1-3 0-6 2-9 1 0 2 1 3 1 2 2 3 3 5 3l2 2 3 1c1 0 3 0 4 1l1 1h2c1 1 2 1 3 1v1l2-2c1-2 2-3 3-5l4-4c0-1 1-2 2-2l3-3z" class="R"></path><path d="M537 356c2-3 6-6 8-9l8 4-7 1c-1 0-2 1-3 1 0 2 0 2-1 4-1 1-1 1-2 1 1-1 2-2 2-4l-1 1h-1l-3 1z" class="B"></path><path d="M548 345v-1c1 0 1-1 2-2h0c1 0 3-1 4-1l2 3h2v1c-1 1-1 2-3 3-2 0-5 0-7-1l-1-1 1-1z" class="E"></path><path d="M548 345v-1c1 0 1-1 2-2h0c1 0 3-1 4-1l2 3-8 1z" class="Z"></path><path d="M560 329c0 2 0 2 2 3h2 1v1 1h1c0 2 1 3 0 5v-1c-1-1-2-1-2-1-3 1-3 1-4 4v1l-1 1c-1 1-1-1-2-1s-2-1-3-1-3 1-4 1h0c-1 1-1 2-2 2v1l-1 1c-1 0-1-1-1-1l2-2c1-2 2-3 3-5l4-4c0-1 1-2 2-2l3-3z" class="D"></path><path d="M558 336c2 0 2 0 3-1l1 1-3 3c-2 0-3 0-5 1h-1l2-2c1 0 2 0 3-1v-1z" class="Q"></path><path d="M560 329c0 2 0 2 2 3h2 1v1 1h1c0 2 1 3 0 5v-1c-1-1-2-1-2-1-3 1-3 1-4 4v1c0-1-1-2-1-3l3-3-1-1c-1 1-1 1-3 1l2-2c0-1-1-1-1-1h-1v1c-1 1-2 2-3 2v-2c0-1 1-2 2-2l3-3z" class="J"></path><defs><linearGradient id="k" x1="531.871" y1="363.838" x2="552.308" y2="362.847" xlink:href="#B"><stop offset="0" stop-color="#282929"></stop><stop offset="1" stop-color="#5a5b5c"></stop></linearGradient></defs><path fill="url(#k)" d="M540 358c1 0 1 0 2-1 1-2 1-2 1-4 1 0 2-1 3-1l5 1-1 1h0l2-2h1v1l-1 2h0 2v7l-1-1c-1 0-2 0-3 1h0l1 2c0 1-1 2-2 3l2 2 1 1h0l-2 1c-1 1-2 2-4 3l-3-2s-1 0-1-1c-2-1-3-2-4-3s-2-1-3 0c2 0 3 2 4 3h1c2 1 3 2 4 4h0l-2-1-1-1c-1 0-1-1-2-1-1-1-1 0-1-1-1 0-1 0-1-1h-2v-1c-1 0-1 1-2 1s-1-1-2-2l-2-1v-1c1-1 0-2 1-3l2-1c0-1 4-5 5-6l3-1h1l1-1c0 2-1 3-2 4z"></path><path d="M550 362l1 2c0 1-1 2-2 3l-1 1h-1c0-1 1-2 1-2v-1h-2l4-3z" class="Z"></path><path d="M537 356l3-1h1l1-1c0 2-1 3-2 4l-1 1c-2 2-3 3-5 6h-1 0l-2 2-1-2 2-2v-1c0-1 4-5 5-6z" class="C"></path><defs><linearGradient id="l" x1="573.168" y1="359.037" x2="559.975" y2="352.342" xlink:href="#B"><stop offset="0" stop-color="#111012"></stop><stop offset="1" stop-color="#2d2e2e"></stop></linearGradient></defs><path fill="url(#l)" d="M574 335h3c-1 5 0 8 1 13l1 2v1l-9 9c-2 1-4 3-5 4l-6 6h-2c-2 1-4 3-6 4 0-1 0-1-1-2 1 0 0 0 1-1h-1l2-1c2-1 3-8 5-9l5-8c2-3 5-5 6-9 1-2 2-3 3-4l1-2-1-1 1-2h1 1z"></path><path d="M557 370h0c2-3 4-5 8-6l-6 6h-2z" class="C"></path><path d="M562 353c2-3 5-5 6-9 1-2 2-3 3-4 0 2 0 2-1 4v2l-1-1c-1 1-2 2-2 4s-2 3-3 4h-2z" class="L"></path><defs><linearGradient id="m" x1="519.39" y1="347.581" x2="538.523" y2="351.398" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#m)" d="M521 343c1-3 0-6 2-9 1 0 2 1 3 1 2 2 3 3 5 3l2 2 3 1c1 0 3 0 4 1l1 1h0l2 2 1 1-7 9c-2 2-7 7-7 8-1 1 0 2-1 3v1l-2-2-2-1-1-1c-1-1-2-1-3-2v-1-17z"></path><path d="M536 341c1 0 3 0 4 1l1 1h0l2 2 1 1-7 9-1-1c1-1 1-2 2-3 0-2 3-3 3-5-1-1 0-1-1-1s-2-1-3-1l-1-1h-3c-2 1-4 1-5 2h-4 0c2-1 3-1 5-1v-1c2 0 3-1 5-2 1 1 1 1 2 1v-1z" class="O"></path><path d="M521 361c1 1 2 1 3 2l1 1 2 1 2 2 2 1c1 1 1 2 2 2s1-1 2-1v1h2c0 1 0 1 1 1 0 1 0 0 1 1 1 0 1 1 2 1l1 1 2 1h0c-1-2-2-3-4-4h-1c-1-1-2-3-4-3 1-1 2-1 3 0s2 2 4 3c0 1 1 1 1 1l3 2c2-1 3-2 4-3h1c-1 1 0 1-1 1 1 1 1 1 1 2 2-1 4-3 6-4h2c-3 3-6 5-9 8s-5 6-8 8l-1 1-12 12c-2 2-4 5-7 6 0 0-1 0-2-1l1-43z" class="W"></path><path d="M533 370c1 0 1-1 2-1v1h2c0 1 0 1 1 1 0 1 0 0 1 1 1 0 1 1 2 1l1 1 2 1h0c-1-2-2-3-4-4h-1c-1-1-2-3-4-3 1-1 2-1 3 0s2 2 4 3c0 1 1 1 1 1l3 2c2-1 3-2 4-3h1c-1 1 0 1-1 1 1 1 1 1 1 2 2-1 4-3 6-4h2c-3 3-6 5-9 8s-5 6-8 8l-1 1v-1c1-1 3-2 3-3l-1-1 3-3h0c-1 1-3 2-4 3-2 2-5 4-7 5-3 1-5 1-8 0-1-1-2-1-2-2-1-4 0-7 2-10v-1l-1 1c-1 0-2 1-2 2h-1l1-1c2-3 4-5 7-8 1 1 1 2 2 2z" class="L"></path><path d="M531 368c1 1 1 2 2 2 1 1 1 1 1 2-1 1-2 1 0 2l2 1h-2c-1 1-1 2-1 3l1 1c0-1 0-1 1-1l1 1s0 1 0 0c2-1 4-1 6-1v1c-2 1-3 3-6 3v1c0 1 0 0-1 1h0c-1 1-1 1-2 1h0-2c-2-1-2-1-3 0s-1 1-1 2c-1-1-2-1-2-2-1-4 0-7 2-10v-1l-1 1c-1 0-2 1-2 2h-1l1-1c2-3 4-5 7-8z" class="F"></path><path d="M451 237c1 0 4 0 6-1 5 0 12-1 17-3 4-2 7-4 11-5 6-1 12-1 18 0v1l-3 1c0 1 1 1 1 1 2 1 3 1 4 1 3-1 8 0 11 0 2-1 2-1 4-1 3 1 6 1 10 0h10 3c1 1 1 2 3 3 1 0 2-1 3 1l1 1v2c0 1-9 8-10 9-2 1-3 1-4 2h3v1l-10 4c1 1 5 0 6 1l-3 2 1 1c0 2-1 2-3 3 0 0-1 0-1 1h0c-2-1-4 0-6 0-3 1-6 0-10 0s-7 0-12-1c-4-1-8-3-12-5-1-1-3-2-4-2l-1-2c-3-1-5-3-8-4l-1 1v3h0c-1 0-4-1-5 0-1 0-1 0-1 1l-1-1c-2 0-3 1-4 3h-1c1 1 3 2 5 2 0 1 0 2-2 3h0-1l-10-6h1c0-1-1-1-2-1 1 0 1 0 2-1v-1-2-1-1l-17-1h-5v-1c4-6 11-7 17-8z" class="N"></path><path d="M532 239h2c1 1 1 1 1 2l-1 1h-2c0-1 0-1-1-1 0-1 1-1 1-2z" class="K"></path><path d="M524 238h1c1 2 1 3 1 5h-3 0c0-1-1-2-2-3 2-1 3-1 3-2z" class="T"></path><path d="M518 240c0 1 1 2 1 2 0 2-2 3-3 4h-3c0-1-1-2-1-2 0-1 1-2 1-2 2 0 2 1 3 2l-1 1h1l2-5z" class="P"></path><path d="M511 251c3-1 6-1 8-1v1h-1v1c-3 1-6 1-9 0 1-1 2-1 2-1z" class="c"></path><path d="M517 236c2-2 2-2 5-2 0 2 1 3 2 4 0 1-1 1-3 2-1-2-2-3-4-4z" class="M"></path><path d="M532 236c1-1 1-2 3-1l1 1h1v2c1 2 1 3 4 4-1 2-2 3-4 3 1-1 1-1 1-3h-1c-1-1-1-1-1-2h-1c0-1 0-2-1-2 0-1-1-1-1-2h-1z" class="R"></path><path d="M530 231h10l-2 3c-1 0-1 1-2 2l-1-1c-2-1-2 0-3 1v1 2l-2-1c0-3 0-3-1-4l1-3z" class="U"></path><path d="M516 232c2-1 2-1 4-1 3 1 6 1 10 0l-1 3c1 1 1 1 1 4l-1 1h-1c-1 0-2-1-3-1h-1c-1-1-2-2-2-4l-1-1c-2 0-3 0-5-1z" class="Q"></path><path d="M523 243c-2 2-3 5-5 5-2 1-4 1-6 0-1-1-3-2-4-4v-5l4-3v-1l1 1 3-3v1c-1 1-1 1-1 2-1 2-3 4-3 6v2s1 1 1 2h3c1-1 3-2 3-4 0 0-1-1-1-2-1-1-1-1-1-2-1-1 0-1-1-1v-1h1c2 1 3 2 4 4 1 1 2 2 2 3z" class="K"></path><path d="M492 237h4l1 1c1 1 2 2 3 2l-1 3h-1l-1-1h-1c0 1 0 0 1 1 0 1 1 2 1 3 1 1 1 1 3 1h0c1 0 2 1 3 1l1-1 1 1c1 1 3 2 5 2v1s-1 0-2 1c-5-1-10-3-15-5-1-1-2-1-3-2l-2-2c0-2 1-2 2-4l1-2h0z" class="V"></path><path d="M491 245c2-1 2-2 4-2v2c-1 0-1 1-1 2-1-1-2-1-3-2z" class="Q"></path><path d="M486 232h1l1-1c3-1 6-1 9-1h3c0 1 1 1 1 1-1 1-2 2-2 3 1 1 2 2 2 4l-1 2c-1 0-2-1-3-2l-1-1h-4 0l-1 2-4-1v1h-2c-1-1-2-2-2-3v-2l3-2z" class="B"></path><path d="M500 230c0 1 1 1 1 1-1 1-2 2-2 3h-1-1c-1-1-2 0-3 0-1 1-1 2-2 3h0c0-2 0-4 1-6l4-1h3z" class="O"></path><path d="M486 232h2l2 2c0 1 0 1-1 2l-2 2v1h-2c-1-1-2-2-2-3v-2l3-2z" class="K"></path><path d="M492 237c1-1 1-2 2-3 1 0 2-1 3 0h1 1c1 1 2 2 2 4l-1 2c-1 0-2-1-3-2l-1-1h-4z" class="Z"></path><path d="M540 231h3c1 1 1 2 3 3 1 0 2-1 3 1l1 1v2c0 1-9 8-10 9-2 1-3 1-4 2-2 1-5 3-7 3 0-1 0-1 1-1v-1h0l-1-1c3-1 5-3 8-4 2 0 3-1 4-3-3-1-3-2-4-4v-2h-1c1-1 1-2 2-2l2-3z" class="C"></path><defs><linearGradient id="n" x1="495.018" y1="253.493" x2="511.202" y2="234.831" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#n)" d="M475 239c-1-1-3-1-4-1 1-2 3-1 4-2l4-2h1l1-1c1 0 2 0 2 1v2c0 1 1 2 2 3h2v-1l4 1c-1 2-2 2-2 4l2 2c1 1 2 1 3 2 5 2 10 4 15 5 3 1 6 1 9 0l11-3 1 1h0v1c-1 0-1 0-1 1 2 0 5-2 7-3h3v1l-10 4c1 1 5 0 6 1l-3 2 1 1c0 2-1 2-3 3 0 0-1 0-1 1h0c-2-1-4 0-6 0-3 1-6 0-10 0s-7 0-12-1c-4-1-8-3-12-5-1-1-3-2-4-2l-1-2h0c1 1 2 1 3 2h1l2 1c1 1 1 0 2 1 0 1 1 1 2 1l1-1-1-1h0c-3-2-6-4-10-6h3c1-1 1-1 2-1h0c-4-3-9-8-14-9z"></path><path d="M501 254c7 2 13 2 20 2-1 0-3 1-4 1-2 0-3 1-5 2-2 0-5-1-8-1l2-1c-2-1-3-2-4-2h-2l1-1z" class="T"></path><path d="M484 249h3c1-1 1-1 2-1l12 6-1 1h2c1 0 2 1 4 2l-2 1c-3-1-7-2-10-3-3-2-6-4-10-6z" class="N"></path><path d="M484 252h0c1 1 2 1 3 2h1l2 1c1 1 1 0 2 1 0 1 1 1 2 1l1-1-1-1h0c3 1 7 2 10 3 3 0 6 1 8 1 2-1 3-2 5-2 1 0 3-1 4-1 3 0 5-1 8-2 1 1 5 0 6 1l-3 2 1 1c0 2-1 2-3 3 0 0-1 0-1 1h0c-2-1-4 0-6 0-3 1-6 0-10 0s-7 0-12-1c-4-1-8-3-12-5-1-1-3-2-4-2l-1-2z" class="L"></path><path d="M521 256c3 0 5-1 8-2 1 1 5 0 6 1l-3 2-10 2h-10c2-1 3-2 5-2 1 0 3-1 4-1z" class="P"></path><defs><linearGradient id="o" x1="461.536" y1="226.597" x2="484.841" y2="262.304" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#o)" d="M451 237c1 0 4 0 6-1 5 0 12-1 17-3 4-2 7-4 11-5 6-1 12-1 18 0v1l-3 1h-3c-3 0-6 0-9 1l-1 1h-1l-3 2c0-1-1-1-2-1l-1 1h-1l-4 2c-1 1-3 0-4 2 1 0 3 0 4 1 5 1 10 6 14 9h0c-1 0-1 0-2 1h-3c4 2 7 4 10 6h0l1 1-1 1c-1 0-2 0-2-1-1-1-1 0-2-1l-2-1h-1c-1-1-2-1-3-2h0c-3-1-5-3-8-4l-1 1v3h0c-1 0-4-1-5 0-1 0-1 0-1 1l-1-1c-2 0-3 1-4 3h-1c1 1 3 2 5 2 0 1 0 2-2 3h0-1l-10-6h1c0-1-1-1-2-1 1 0 1 0 2-1v-1-2-1-1l-17-1h-5v-1c4-6 11-7 17-8z"></path><path d="M464 255l-1-2c0-1-1-2-2-2l-1-1 1-1h1 1v1c1 0 2-1 3 0 2 0 2 1 2 2-2 0-3 1-4 3z" class="F"></path><path d="M464 244c3-1 6-2 9-2h1c3 1 6 3 8 5l-1 1-6-3-2 1c-1-1-1-1 0-2l1-1 1 1h1l-1-1c-3 0-6 0-9 1h-2z" class="L"></path><path d="M475 239c5 1 10 6 14 9h0c-1 0-1 0-2 1h-3l-2-2c-2-2-5-4-8-5h-1c-3 0-6 1-9 2-1 0-1 0-2-1 2-2 4-2 6-3s5-1 7-1z" class="V"></path><path d="M418 444l2 1-3 6v4c2 1 2 4 2 6s1 4 2 6v1c1 2 3 4 4 6l2 5c1 1 2 2 2 3 1 1 2 2 2 3 0 2 2 4 2 6l-1 1c3 4 2 8 5 12 0-3-1-6-1-9l1-2h1c1 1 1 2 1 3v1l1 1c1 1 2 1 3 3 0 3-1 7-3 10h-4c0-1 1-3 0-4h-5c-1-2-1-3-3-4-1-1-3 0-4 0h-1c1 1 3 1 3 3h-1c-1-1-2-1-3-2v1c2 0 4 2 4 3 2 4 0 7-2 11l-5 7h0c-10 9-21 13-35 12h-2v-1c1-1 2-1 3-1l2-2c0-3 3-6 4-9 5-7 6-17 7-25-1-4-1-11-5-14-1-1-2-1-3-1l2 2h-1-1c0-2-2-3-3-5 3-2 5-5 7-8l1-1 13-16c2-2 3-4 4-7l2 1 4-7z" class="H"></path><path d="M412 508c1 1 1 2 1 3l-1 1s-1 0-2 1c-2 1-1 3-4 3l2-1c0-1 1-2 1-3 1-1 1 0 1-1 1-1 2-2 2-3zm-13-19c1 1 2 1 3 3v1c-1 4 0 9-1 13v1h-1l1-7c0-4-1-7-2-11z" class="F"></path><path d="M407 505h1c1 1 0 2 0 3-1 1-1 0 0 1l1-1c1-2 2-4 4-5v1c-1 0-1 1-1 2h0c2-2 4-2 6-3 1 0 1 0 2-1h1 1l1 1c1 1 3 1 3 3h-1c-1-1-2-1-3-2v1h-5c-2 1-3 1-5 3 0 1-1 2-2 3 0 1 0 0-1 1 0 1-1 2-1 3l-2 1c-1 2-3 4-5 4v-1-2c1-1 1-2 1-4 0-1 0-1 1-2v-1l-1-1 1-1 1 1 2 2v-1-1h0c0-2 0-3 1-4z" class="C"></path><path d="M398 500v-3h0c1-1 0-1 1-1v3l2 1-1 7c-2 10-5 23-13 29h-2l2-2c0-3 3-6 4-9 5-7 6-17 7-25z" class="M"></path><path d="M417 477l1-1v1l1-1h-1c-1-2-3-2-4-2l-1-1 2-1c1 1 2 1 3 2h2l2 2 3-2 2 5c1 1 2 2 2 3 1 1 2 2 2 3 0 2 2 4 2 6l-1 1c3 4 2 8 5 12 0-3-1-6-1-9l1-2h1c1 1 1 2 1 3v1l1 1c1 1 2 1 3 3 0 3-1 7-3 10h-4c0-1 1-3 0-4h-5c-1-2-1-3-3-4-1-1-3 0-4 0h-1l-1-1h-1-1c-1 1-1 1-2 1-2 1-4 1-6 3h0c0-1 0-2 1-2v-1c-2 1-3 3-4 5l-1 1c-1-1-1 0 0-1 0-1 1-2 0-3h-1c1-1 1-1 1-2 1-1 2-3 3-4v-2h0v-2h0v-2-1h-1v-1c1-1 0-1 0-2-1 2-3 5-5 6v-3c0-1 0-1 1-1 0-2 1-3 1-4l1-1 2-2v-2h-4l4-1 1-1c2 1 4 3 6 5v-1-2l-1-1c0-2 0-2 1-4z" class="I"></path><path d="M432 497h-2-1v-3c2 0 1 0 3 1v2z" class="H"></path><path d="M418 484h2c2 2 2 2 2 4l-1-1c-1 0-2 0-3 1v-4z" class="C"></path><path d="M432 492c3 4 2 8 5 12l-1 1-3-2v-2c0-2-1-1-1-2s1-1 0-2v-2-3z" class="L"></path><path d="M417 477l1-1v1l1-1h-1c-1-2-3-2-4-2l-1-1 2-1c1 1 2 1 3 2h2l2 2 3-2 2 5c1 1 2 2 2 3 1 1 2 2 2 3 0 2 2 4 2 6-1-1-2-3-3-4-2-3-7-7-11-7-1 1-1 2-1 4v4h0l-1-1c-2-3-4-5-7-6l1-1c2 1 4 3 6 5v-1-2l-1-1c0-2 0-2 1-4z" class="Y"></path><path d="M418 474h2l2 2 5 6h0c-1 0-1 0-2-1l-4-2v-1h0c-1-2-1-3-2-4h-1z" class="M"></path><path d="M422 476l3-2 2 5c1 1 2 2 2 3v1l-2-1-5-6z" class="X"></path><path d="M418 444l2 1-3 6v4c2 1 2 4 2 6s1 4 2 6v1c1 2 3 4 4 6l-3 2-2-2h-2c-1-1-2-1-3-2l-2 1 1 1c1 0 3 0 4 2h1l-1 1v-1l-1 1c-1 2-1 2-1 4l1 1v2 1c-2-2-4-4-6-5l-1 1-4 1h4v2l-2 2-1 1c0 1-1 2-1 4-1 0-1 0-1 1-1 1-2 1-3 1v-1c-1-2-2-2-3-3 1 4 2 7 2 11l-2-1v-3c-1 0 0 0-1 1h0v3c-1-4-1-11-5-14-1-1-2-1-3-1l2 2h-1-1c0-2-2-3-3-5 3-2 5-5 7-8l1-1 13-16c2-2 3-4 4-7l2 1 4-7z" class="T"></path><path d="M418 444l2 1-3 6c-2 4-5 8-8 11h-1c3-3 4-7 6-11l4-7z" class="R"></path><path d="M405 477c3-1 6-2 9-2l3 2c-1 2-1 2-1 4l1 1-2-1c0-1 0-2-1-3-4 0-9 1-13 3v1h-2c-1 0-2 1-2 1h-1-1c2-3 7-4 10-6z" class="G"></path><path d="M408 457c2-2 3-4 4-7l2 1c-2 4-3 8-6 11-3 2-5 4-7 6-1 2-2 4-4 5h-2l13-16z" class="M"></path><path d="M399 489l-3-4c4-2 7-3 10-3h4v2l-2 2-1 1c0 1-1 2-1 4-1 0-1 0-1 1-1 1-2 1-3 1v-1c-1-2-2-2-3-3z" class="b"></path><path d="M401 477c-3 2-7 7-9 7-1-1-2-1-3-2l9-6c5-5 9-11 14-16h1v1c-1 5-7 11-10 14l-2 2z" class="B"></path><path d="M401 477c-1 0-1 0-2-1h0 0l7-8h0c1-2 5-7 7-7-1 5-7 11-10 14l-2 2z" class="D"></path><path d="M417 455c2 1 2 4 2 6s1 4 2 6v1c1 2 3 4 4 6l-3 2-2-2h-2c-1-1-2-1-3-2l-2 1 1 1c1 0 3 0 4 2h1l-1 1v-1l-1 1-3-2c-3 0-6 1-9 2l-2-2c3-3 9-9 10-14v-1c2-1 3-3 4-5h0z" class="P"></path><path d="M418 472c-1-1 0-1-1-2-1 1-2 1-3 2-2 1-2 1-3 1l-1-1 4-6c2 0 3 0 5 1v2l-1 1v2z" class="Y"></path><path d="M417 455c2 1 2 4 2 6s1 4 2 6v1c1 2 3 4 4 6l-3 2-2-2-2-2v-2l1-1v-2c-2-1-3-1-5-1l3-4v-1l-1 1-1-1c2-2 2-3 2-6h0z" class="d"></path><path d="M153 184c3-2 6-3 10-4-1 1-2 1-3 2l-1 2h2c1 1 1 2 2 3-1 6-2 12 1 17 1 2 3 3 5 4h4c2 1 5 1 7 1v1c-9 1-17 3-24 9l2 1h0c0 2 1 3 2 4-1 1-2 2-4 2h-1c-2 1-3 2-4 3h3l-2 3 2 2c1 1 3 2 3 4l-1 1-1-1-1 1 1 1v2l-2 2v1-1l-1-1c-1 0-1-1-2-2v1c-1 1 0 1-1 0l-2 1v6c1 0 1 0 1 1s0 1 1 2c-1 1-2 1-2 2v2 1l1 1h-1l-2-4h-1v-1 2c1 1 2 2 3 4h1v1c1 1 0 1 1 2 0 1 0 2 1 3h1c1 0 1 0 2 1l1 1c2 0 2-1 4 0-1 1-1 1-1 3l2-2v1c1 2 2 3 3 5h1c2 2 3 4 5 6 4 4 9 7 15 10 9 3 17 3 26-2 2-1 4-3 7-4h-1c-4 6-12 10-19 12-14 4-29 0-40-7-16-10-24-24-28-42l1-1c1-9 0-17 1-25 0-1 0-1-1-1l-2-1v-1l2-1c1-1 1-3 3-5h1l20-28z" class="X"></path><path d="M154 267c2 0 2-1 4 0-1 1-1 1-1 3l2-2v1c1 2 2 3 3 5h1c2 2 3 4 5 6-2 1-2 2-4 2h0c3 4 5 6 9 8-4-1-9-2-11-6h-1c1-1 1-1 0-2 0-1-1-2-1-3-2-4-5-8-6-12z" class="Q"></path><path d="M157 270l2-2v1c1 2 2 3 3 5h1c2 2 3 4 5 6-2 1-2 2-4 2-3-4-5-8-7-12z" class="d"></path><path d="M144 255c1 1 2 2 3 4h1v1c1 1 0 1 1 2 0 1 0 2 1 3h1c1 0 1 0 2 1l1 1c1 4 4 8 6 12 0 1 1 2 1 3 1 1 1 1 0 2-3-3-7-8-9-12-1 0-1-1-2-1h-1c-3-6-4-10-5-16z" class="K"></path><path d="M146 221h1c1-1 2-2 4-3h0c1-1 2-1 3-2l1 1c-1 0-1 1-2 1-5 3-10 11-12 16v2 1c-2 6-2 11-2 17v6c1 2 3 7 2 9 0-2-1-3-1-4-1-1-1-2-2-2h0c-1-3-1-6-1-9-1-8 0-16 2-24 1-3 4-8 7-9z" class="M"></path><defs><linearGradient id="p" x1="134.444" y1="233.332" x2="151.222" y2="236.768" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#3c3b3d"></stop></linearGradient></defs><path fill="url(#p)" d="M160 208l2 2v1c-7 1-12 5-16 10-3 1-6 6-7 9-2 8-3 16-2 24 0 3 0 6 1 9h0l1 5c5 12 17 19 29 24l-1 1c-5-2-10-5-15-8-11-9-18-23-20-37v-6c1-2 1-5 2-8 2-7 6-16 13-20 2-1 4-3 7-4h5l1-1v-1z"></path><defs><linearGradient id="q" x1="146.652" y1="220.564" x2="153.492" y2="280.672" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#3a3a3c"></stop></linearGradient></defs><path fill="url(#q)" d="M156 219l2 1h0c0 2 1 3 2 4-1 1-2 2-4 2h-1c-2 1-3 2-4 3h3l-2 3 2 2c1 1 3 2 3 4l-1 1-1-1-1 1 1 1v2l-2 2v1-1l-1-1c-1 0-1-1-2-2v1c-1 1 0 1-1 0l-2 1v6c1 0 1 0 1 1s0 1 1 2c-1 1-2 1-2 2v2 1l1 1h-1l-2-4h-1v-1 2c1 6 2 10 5 16h1c3 5 8 11 12 16-5-4-11-7-15-12-7-9-8-21-6-32 1-10 7-18 15-24z"></path><path d="M149 242c-1 0-1 0-2-1 0-4 1-7 3-10l1-2h3l-2 3 2 2c1 1 3 2 3 4l-1 1-1-1-1 1 1 1v2l-2 2v1-1l-1-1c-1 0-1-1-2-2v1c-1 1 0 1-1 0z" class="E"></path><path d="M150 241l1-3h1c1 2 1 3 3 4l-2 2v1-1l-1-1c-1 0-1-1-2-2zm3-57c3-2 6-3 10-4-1 1-2 1-3 2l-1 2h2c1 1 1 2 2 3-1 6-2 12 1 17 1 2 3 3 5 4h-3c-2-1-4-2-6-4l-2-4-1 1c1 1 2 2 2 3l-4-3h0c1 3 3 5 5 7v1l-1 1h-5c-3 1-5 3-7 4-7 4-11 13-13 20-1 3-1 6-2 8v6c-1-4 0-9 0-13l-1-1-1-1v11 3l-1-1c1-9 0-17 1-25 0-1 0-1-1-1l-2-1v-1l2-1c1-1 1-3 3-5h1l20-28z" class="Q"></path><path d="M153 193h2l-1 3c0 2 0 3 1 5 1 3 3 5 5 7v1c-1-1-2-1-3-1-2 0-4-2-4-3-1-2-2-5-4-6 0-2 2-3 3-4 0-1 1-1 1-2z" class="B"></path><path d="M153 193h2l-1 3c-1 1-2 2-2 4l2 4-1 1c-1-2-2-5-4-6 0-2 2-3 3-4 0-1 1-1 1-2zm-12 10v2c0 1-1 1-1 3l1-1 3-3c-1 3-1 8-3 11-1 1-2 2-2 4-1 1-1 3-2 4-2 3-3 7-4 11h1c-1 3-1 6-2 8v6c-1-4 0-9 0-13l-1-1-1-1v11 3l-1-1c1-9 0-17 1-25 2-6 7-13 11-18z" class="L"></path><path d="M131 224l2 1-1 10-1-1-1-1c0-3 1-6 1-9z" class="f"></path><path d="M131 224c1-4 2-8 6-11v1h0l-1 2c-2 3-2 6-3 9l-2-1z" class="P"></path><path d="M153 184c3-2 6-3 10-4-1 1-2 1-3 2l-1 2h2c1 1 1 2 2 3-1 6-2 12 1 17 1 2 3 3 5 4h-3c-2-1-4-2-6-4l-2-4-1 1c1 1 2 2 2 3l-4-3h0c-1-2-1-3-1-5l1-3h-2c0 1-1 1-1 2-1 1-3 2-3 4l-5 5-3 3-1 1c0-2 1-2 1-3v-2c-4 5-9 12-11 18 0-1 0-1-1-1l-2-1v-1l2-1c1-1 1-3 3-5h1l20-28z" class="Y"></path><path d="M155 186h4c1 1 1 2 1 4v1c-1 2 0 6-1 7l-2 2v1c1 1 2 2 2 3l-4-3h0c-1-2-1-3-1-5l1-3c1-2 2-3 2-5v-1l-2-1z" class="Q"></path><path d="M155 186l2 1v1c0 2-1 3-2 5h-2c0 1-1 1-1 2-1 1-3 2-3 4l-5 5-3 3-1 1c0-2 1-2 1-3v-2l7-10c1-2 3-4 4-5s2-2 3-2z" class="H"></path><path d="M157 188c0 2-1 3-2 5h-2v-3c2-2 2-2 4-2z" class="b"></path><path d="M769 287c1 1 1 2 1 3h0c2 0 3 0 5 1l-5 9c1 3 1 5 0 7s-1 4-1 5h1v-1l2 1 1-2h2l1-1v1c-1 9-4 16-8 24 1 2 1 3 0 5l-5 12c-1 3-2 6-3 8s-1 4-3 5c0 0-1 0-1 1-1 1-2 2-2 4 2-1 4-2 6-2h0v1c-3 0-3 1-5 2h-1c-2 1-2 3-5 3h0c0-2 1-3 1-4l2-3v-2l-1-1-1 2-2 5c-1 2-1 3-2 5l-2 4v1l-2 5c-1 2-2 5-3 6s-1 2-1 3l-3 8-1 2c-1 1-1 3-2 5-2 3-3 6-4 8-1 3-4 6-4 9 1 0 2-1 3-1-1 2-2 3-1 6v5l-1 2c-3-12-8-22-17-31l-6-6h-1c0-1-1-1-1-2l1-1v-3l2-1c1 0 4 0 5-1 0 0-1 0-2-1s-2-3-3-5c1-2-1-6-2-8h1 1l1-1h-2l1-1h1l1-1c2 2 5 1 8 2h-1l2 2c1-1 2-1 3-1l1 1h-1v1c-1 1-1 2-2 2l1 3 2 1 1-1 3 1c3-2 4-3 6-6l2-6 4-9c0-1 1-2 2-4 1-1 1-3 2-5v-1l2-5c1-1 1-1 2-3 0-1 0-1 1-3h2l-1 1c1 0 1 0 2-1h0c2-2 3-5 4-8l1-3 3-9 3-7 2-4 2-8v-1c-1-2-1 0-2-1v-2c1 0 2 1 3 0 1 0 1-1 2-2-2 0-4 1-5 2l-2-6 1-1c1 0 2 1 3 2l1-1c4-2 5-5 7-9z" class="S"></path><path d="M760 354c0 2-1 3 0 5-1 2-1 4-3 5 0 0-1 0-1 1l4-11z" class="N"></path><path d="M751 363c0-1 0-1-1-2 0-3 2-7 3-10h1c1 0 1-1 2-1l1-1v2c0 2-1 3-2 5v2l-2 4-1 2-1-1z" class="O"></path><path d="M768 334c1 2 1 3 0 5l-5 12-3 8c-1-2 0-3 0-5l8-20z" class="c"></path><path d="M760 338c1-1 1-2 2-3h0 1c0 1-1 3-1 4-1 1-1 2-1 3l-2 3v2c-1 0-1 1-2 2v-1c-2 1-2 1-3 0-2 1-2 1-3 1l4-10h1 4v-1z" class="C"></path><path d="M755 339l15-39c1 3 1 5 0 7s-1 4-1 5h1v-1l2 1c-2 2-3 3-4 5v2h0l2-1v1c0 1-1 1-1 3l-1 1-2 5-1 2s0 1-1 1c0 2 0 3-1 4h-1 0c-1 1-1 2-2 3v1h-4-1z" class="I"></path><path d="M756 339l2-3h1c0 1 0 1 1 1v1 1h-4z" class="b"></path><path d="M769 287c1 1 1 2 1 3h0c2 0 3 0 5 1l-5 9-15 39-4 10-13 30h-1c-1 1-1 3-2 4h0l-2 2v1l-1 1v1h-1v-1-2l-2 3-2 1v1c-1 1 0 2-2 4-2 1-2 1-4 1l-1-1 1-1h0c1-3-2-5-3-6l1-1 3 1c3-2 4-3 6-6l2-6 4-9c0-1 1-2 2-4 1-1 1-3 2-5v-1l2-5c1-1 1-1 2-3 0-1 0-1 1-3h2l-1 1c1 0 1 0 2-1h0c2-2 3-5 4-8l1-3 3-9 3-7 2-4 2-8v-1c-1-2-1 0-2-1v-2c1 0 2 1 3 0 1 0 1-1 2-2-2 0-4 1-5 2l-2-6 1-1c1 0 2 1 3 2l1-1c4-2 5-5 7-9z" class="P"></path><path d="M765 298c-1 0-1 0-1-1 1 0 1 0 1-1 2 0 2 0 3 1h1c0 3-1 3-2 6h-1c-2 1-1 3-3 5 0 1-1 1-1 2-1 1-1 3-1 4h-2l2-8v-1c-1-2-1 0-2-1v-2c1 0 2 1 3 0 1 0 1-1 2-2l1-2z" class="f"></path><path d="M765 298c-1 0-1 0-1-1 1 0 1 0 1-1 2 0 2 0 3 1 0 0-1 1-1 2v2h-1l-1-1v-2z" class="d"></path><path d="M769 287c1 1 1 2 1 3h0c1 1 1 2 1 3v1l-2 3h-1c-1-1-1-1-3-1 0 1 0 1-1 1 0 1 0 1 1 1l-1 2c-2 0-4 1-5 2l-2-6 1-1c1 0 2 1 3 2l1-1c4-2 5-5 7-9z" class="g"></path><path d="M740 360l1 2 1-1c0-1 1-3 2-4v1l-2 6-3 8c0 1-1 2-1 3l-1 1c-1 2-2 4-2 7h0l-2 2v1l-1 1v1h-1v-1-2c0-2 2-5 2-6l5-14 2-5z" class="d"></path><path d="M750 337v1 1l-2 5v1l1 1v1l-2 5h-1c0 2-1 3-1 4l-1 2v-1c-1 1-2 3-2 4l-1 1-1-2-2 5c1-3 0-5 0-8v-1l2-5c1-1 1-1 2-3 0-1 0-1 1-3h2l-1 1c1 0 1 0 2-1h0c2-2 3-5 4-8z" class="X"></path><path d="M743 345h2l-1 1c1 0 1 0 2-1h0l-5 13-1 2-2 5c1-3 0-5 0-8v-1l2-5c1-1 1-1 2-3 0-1 0-1 1-3z" class="L"></path><path d="M742 348v3c-2 2-2 3-2 5l1 2-1 2-2 5c1-3 0-5 0-8v-1l2-5c1-1 1-1 2-3z" class="I"></path><path d="M738 357c0 3 1 5 0 8l-5 14c0 1-2 4-2 6l-2 3-2 1v1c-1 1 0 2-2 4-2 1-2 1-4 1l-1-1 1-1h0c1-3-2-5-3-6l1-1 3 1c3-2 4-3 6-6l2-6 4-9c0-1 1-2 2-4 1-1 1-3 2-5z" class="H"></path><path d="M727 389c1-1 1-1 1-2s0-2 1-3c1-2 2-3 2-5h2c0 1-2 4-2 6l-2 3-2 1z" class="L"></path><path d="M701 379h1 1l1-1h-2l1-1h1l1-1c2 2 5 1 8 2h-1l2 2c1-1 2-1 3-1l1 1h-1v1c-1 1-1 2-2 2l1 3 2 1c1 1 4 3 3 6h0l-1 1 1 1c2 0 2 0 4-1 2-2 1-3 2-4v-1l2-1 2-3v2 1h1v-1l1-1v-1l2-2h0c1-1 1-3 2-4h1l-6 18c-1 3-1 6-2 9v2l2 1c-2 3-3 6-4 8-1 3-4 6-4 9 1 0 2-1 3-1-1 2-2 3-1 6v5l-1 2c-3-12-8-22-17-31l-6-6h-1c0-1-1-1-1-2l1-1v-3l2-1c1 0 4 0 5-1 0 0-1 0-2-1s-2-3-3-5c1-2-1-6-2-8z" class="F"></path><path d="M704 400c1 0 1 0 2-1h1l1 1c0 1 0 1-1 2-1 0-2-1-3-2z" class="H"></path><path d="M704 400c1 1 2 2 3 2h2l1 1h-1v2 1c-1 1-1 0-1 1l-6-6c1 0 1 0 2-1z" class="L"></path><path d="M701 379h1 1l1-1h-2l1-1h1l1-1c2 2 5 1 8 2h-1l2 2c1-1 2-1 3-1l1 1h-1v1c-1 1-1 2-2 2l1 3 2 1c1 1 4 3 3 6h0c-2 0-2 1-3 2l-1-2c-1 0-2 0-3-1h-1c-1 0-1 0-2-1h3c1 0 1 1 3 0v-1l2 1v-1l-2-3c-1 0-3-1-4-1s-1 1-2 0c-2 0-3-1-5-2-2 1-2 1-3 3 1-2-1-6-2-8z" class="J"></path><path d="M712 381c1 1 2 1 3 2l1 3c-1-1-2-1-3-1h0l-2-2c0-1 0-1 1-2z" class="G"></path><path d="M714 380c1-1 2-1 3-1l1 1h-1v1c-1 1-1 2-2 2-1-1-2-1-3-2l-2-1h4 0z" class="B"></path><path d="M735 383h0c1-1 1-3 2-4h1l-6 18-9 23v1c-1-1-1-1-1-2 2-5 4-11 5-16h0c-1 2-3 10-5 11h-1l3-6c-1-2-1-4-1-6h-1c1-1 2-2 2-4-1 0-1 0-2-1l-1-1h-1c-1 0-2 0-2-1 1-1 1-2 3-2l-1 1 1 1c2 0 2 0 4-1 2-2 1-3 2-4v-1l2-1 2-3v2 1h1v-1l1-1v-1l2-2z" class="f"></path><path d="M727 389l2-1-5 20c-1-2-1-4-1-6h-1c1-1 2-2 2-4-1 0-1 0-2-1l-1-1h-1c-1 0-2 0-2-1 1-1 1-2 3-2l-1 1 1 1c2 0 2 0 4-1 2-2 1-3 2-4v-1z" class="B"></path><path d="M256 224h0 1v-1c-1 0-2-1-3-1l1-1h1l2 1 1-1 2 2-1 1c1 1 2 1 3 2h0v-1l2 1c-1 2-2 2-2 3h1l1-1c1 1 1 2 2 3 0 1 1 2 1 3 1 1 2 3 3 4l2 2c2 0 5 2 6 3l2-1v1c2 1 3 1 5 2v-1 1l-1 1h0c1 1 2 1 3 2h1l2 1c1 1 2 1 3 2 1 0 1 1 2 1h0c1 1 1 1 2 1l1 1h0c3 1 6 3 7 6v-1l1 1c3 1 4 3 7 4l2 1-1 2h-1v-2c-1 1-1 2-3 3h-1l-2 1h-2l-3 2c-1 0-1-1-2 0-2 1-5 1-8 1h-2v2c-1-1-1-1-1-2l-1 2-1 1c-1 0-2 0-2 1l-2 2v1h0-9l-9-1c0-1-2-1-3-1l-1 1-1 1c-2 0-4 1-5 0l1-2-1-2h2c-1-1-2-2-2-4v-1c-1-2-2-1-4-2l-1-2v-1c-1 0-1 1-2 0-1 0-1 1-2 0v-1s-1 0-1-1v-1l-2-3h-4c-2 1-2 2-3 3l-1-2c-1 0-1 1-1 2-2-1-2-1-3-2l-1-1h0l-9-20c-3-4-5-8-8-12 2 0 3 0 5 1l2 3h5c2 1 4 1 5 2h1c2 1 2 2 4 2l-1-1c0-2-1-2-2-4 2-2 3-4 4-6l3-1c0 1-2 3-2 3 0 3 0 6 2 8v1l2-2 1 1c1 0 2 1 3 1 1-1 2-1 4-3l3-3c-1-1-1-1-1-2l2-1h0v2c2-1 1-1 3-1 0 0 1-1 1-2l-2-1z" class="N"></path><path d="M237 224l3-1c0 1-2 3-2 3-1 2-2 3-1 5l1 3c1 1 1 2 1 3h0c-1-1-2-1-3-2l-1-1c0-2-1-2-2-4 2-2 3-4 4-6z" class="M"></path><path d="M276 245h1c0 1 0 0-1 1-2 1-3 4-5 5-1 1-2 1-3 3 0 2-2 4-4 6h-2c2-2 5-4 5-7-1-1-2-2-3-2h-1v1c1 0 1 0 2 1h-2v2l-1 1h-2c2-2 1-3 2-5 0-1 2-2 2-3l1-1c1 1 3 3 5 4 2-2 3-4 6-6z" class="O"></path><path d="M231 234c1 1 3 2 3 4v2c0 1-1 2-2 2-1 1-2 1-3 1l-2-1c1 0 1 0 1-1l-1-1v-2h-1l1-2c1-1 1-1 3-2h1z" class="C"></path><path d="M231 234c1 1 3 2 3 4v2l-2-1h-2c0-1-1-2-1-2-1 0-1 0-2-1 1-1 1-1 3-2h1z" class="B"></path><path d="M226 242h1l2 1 1 1h0l1-1 1 1c-1 0-2 1-2 2 1 1 2 0 3 2h0c0-1 0-2 1-2 0-1 0-1 1-2v1l2 2s0 1 1 1c-1 0-2 0-3-1v1c0 1 0 1 2 2h2v3c-1 1-3 1-4 2h2l3-1h1 1v1c-1 0-2-1-3 1 2 1 3 0 4 1h-3-1c-1 1-3 1-5 2v-1c0-4-3-6-4-9-1-2-4-5-4-7z" class="Z"></path><path d="M214 227c2 0 3 0 5 1l2 3h5c2 1 4 1 5 2v1h-1c-2 1-2 1-3 2l-1 2h1v2l1 1c0 1 0 1-1 1h-1c0 2 3 5 4 7 1 3 4 5 4 9v1c2-1 4-1 5-2h1c-1 1-2 2-4 3-1 0-1 1-1 2-2-1-2-1-3-2l-1-1h0l-9-20c-3-4-5-8-8-12z" class="B"></path><path d="M226 231c2 1 4 1 5 2v1h-1c-2 1-2 1-3 2l-1 2h1v2l1 1c0 1 0 1-1 1h-1l-2-3v-2-2c-1-1-2-2-3-4h5z" class="S"></path><path d="M226 231c2 1 4 1 5 2v1h-1c-2 0-3-1-5 0l-1 1c-1-1-2-2-3-4h5z" class="G"></path><path d="M256 224h0 1v-1c-1 0-2-1-3-1l1-1h1l2 1 1-1 2 2-1 1c1 1 2 1 3 2h0v-1l2 1c-1 2-2 2-2 3h1l1-1c1 1 1 2 2 3 0 1 1 2 1 3 1 1 2 3 3 4h-1c-1 0-1-1-1-1-1 1-2 1-2 2l-2 2v1c1 1 1 1 1 2 2 0 2-1 3-1s2 0 3-1l1 1v-1l-1-1 1-1c1 1 1 0 2 2v1h-1l1 1c0 1 0 0 1 1-3 2-4 4-6 6-2-1-4-3-5-4l-1 1c0 1-2 2-2 3-1 1-1 1-1 2v1h-1c0-1 0-1-1-2v-1l-1-1c0 1-1 2-2 2s-1-1-2-1-1 0-1 1c-1-1-3-2-4-4 0 0-1-1-1-2-1 0-1 0-2-1s-1-1-1-2l-1 1c-1-1-2-1-2-2v-1c-1-1-1-2-2-3l3-2v-2c1 0 2 1 3 1 1-1 2-1 4-3l3-3c-1-1-1-1-1-2l2-1h0v2c2-1 1-1 3-1 0 0 1-1 1-2l-2-1z" class="M"></path><path d="M264 248h-3c-1 1-2 1-4 1-1 0-2 0-2-1-2-1-3-3-3-4 0-2 1-3 2-4h5v-1h-1v-1c2 0 3 0 4 1h1l2 1 1-1c0 1-1 1-1 2l-1 1-2-1-1 1c-2-1-2-1-4-1l-1 1c0 2 0 2 1 3 2 1 5 1 8 1v1l-1 1z" class="U"></path><path d="M265 241v1c1 1 1 1 1 2 2 0 2-1 3-1s2 0 3-1l1 1v-1l-1-1 1-1c1 1 1 0 2 2v1h-1l1 1c0 1 0 0 1 1-3 2-4 4-6 6-2-1-4-3-5-4v-1c-3 0-6 0-8-1-1-1-1-1-1-3l1-1c2 0 2 0 4 1l1-1 2 1 1-1h0z" class="f"></path><path d="M265 241l2-2c0-1 1-1 2-2 0 0 0 1 1 1h1l2 2c2 0 5 2 6 3l2-1v1c2 1 3 1 5 2v-1 1l-1 1h0c1 1 2 1 3 2h1l2 1c1 1 2 1 3 2 1 0 1 1 2 1h0c1 1 1 1 2 1l1 1h0c3 1 6 3 7 6v-1l1 1c3 1 4 3 7 4l2 1-1 2h-1v-2c-1 1-1 2-3 3h-1l-2 1h-2l-3 2c-1 0-1-1-2 0-2 1-5 1-8 1h-2v2c-1-1-1-1-1-2l-1 2-1 1c-1 0-2 0-2 1l-2 2v1h0-9l-9-1c0-1-2-1-3-1l-1 1-1 1c-2 0-4 1-5 0l1-2-1-2h2c-1-1-2-2-2-4v-1c-1-2-2-1-4-2l-1-2 2 1 1-1v-3h-1c1-1 1-1 1-2 1-1 1-2 3-3l2-1c0 1 1 2 1 2l2 1h2c2-2 4-4 4-6 1-2 2-2 3-3 2-1 3-4 5-5 1-1 1 0 1-1h-1c-1-1-1 0-1-1l-1-1h1v-1c-1-2-1-1-2-2l-1 1 1 1v1l-1-1c-1 1-2 1-3 1s-1 1-3 1c0-1 0-1-1-2v-1z" class="Q"></path><path d="M276 261l1-1 7-1 1 1c-2 1-1 0-3 0s-3 4-4 5l-1-1v-3h-1z" class="V"></path><path d="M293 259h1l-1 1 1 1 1-1v1l-3 3h-1c-2-1-2-2-2-4l1-1 2 1 1-1z" class="T"></path><path d="M307 260c3 1 4 3 7 4l2 1-1 2h-1v-2c-1 1-1 2-3 3h-1v-2-1l-1 1-1-1v-1c0-1-1-2-1-3-1 0 0-1 0-1z" class="Z"></path><path d="M265 241l2-2c0-1 1-1 2-2 0 0 0 1 1 1h1l2 2c2 0 5 2 6 3l2-1v1c2 1 3 1 5 2v-1 1l-1 1h0c1 1 2 1 3 2h1l2 1c1 1 2 1 3 2-1 1-2 1-2 3h-3l-3 3h-1c-1-1-2-1-2-1-2 0-2 0-3-1l-2-2-1 2h0l1 1-1 1 1 1c-2 1-3 2-4 3h-1l1-2-3 3 1 1h-1c0 1 0 1-1 1 0 2 1 3 1 5-2 1-2 1-3 1-2-1-2-1-3-3 0-1 1-1 1-1l3-4-1-2c-2 0-2 1-4 1v-1c2-2 4-4 4-6 1-2 2-2 3-3 2-1 3-4 5-5 1-1 1 0 1-1h-1c-1-1-1 0-1-1l-1-1h1v-1c-1-2-1-1-2-2l-1 1 1 1v1l-1-1c-1 1-2 1-3 1s-1 1-3 1c0-1 0-1-1-2v-1z" class="c"></path><path d="M286 245v-1 1l-1 1h0c1 1 2 1 3 2h1l2 1c1 1 2 1 3 2-1 1-2 1-2 3h-3l-3 3h-1c-1-1-2-1-2-1-2 0-2 0-3-1l-2-2-1 2h0l1 1-1 1c-1-1-1-2-1-3 0-2-1-1 0-2h1c0 1 1 1 1 1 1-3 2-4 4-6h-1c-1 1-2 2-3 2 1-2 3-3 4-4 1 1 1 0 2 1l2-1z" class="K"></path><path d="M291 249l-1 2h-1c-1 1-1 1-2 0 0-1-1-1-2-2h0v-1h2c1 1 1 1 2 0l2 1z" class="J"></path><path d="M251 266l2 1 1-1v-3h-1c1-1 1-1 1-2 1-1 1-2 3-3l2-1c0 1 1 2 1 2l2 1h2v1c2 0 2-1 4-1l1 2-3 4s-1 0-1 1l-1 1c0 1 1 1 2 2 2 2 5 0 7 3h0 2v-2l-1-3c0-3 0-5 2-7h1v3l1 1c1 2 2 2 4 3h2v-1c1-1 1-2 1-3l-1-1 1-1s1 1 1 2 0 1-1 2l1 1 2-2v1l-3 4c2 2 4 0 5 2l-1 2-1 1c-1 0-2 0-2 1l-2 2v1h0-9l-9-1c0-1-2-1-3-1l-1 1-1 1c-2 0-4 1-5 0l1-2-1-2h2c-1-1-2-2-2-4v-1c-1-2-2-1-4-2l-1-2z" class="V"></path><path d="M274 275c-1 1-2 0-3 0-2 0-3-1-4-2h6 0l1 2z" class="E"></path><path d="M276 278c1-1 0-1 1 0h4l2-2c2-3 2-1 6-2l-1 1c-1 0-2 0-2 1l-2 2v1h0-9l1-1z" class="Q"></path><path d="M276 261h1v3l1 1c1 2 2 2 4 3h2v1h-3v1c2 1 3-1 4 2v1h-1l-2-2-1 1 1 1c-1 0-3 1-3 2-2 0-4-1-5 0l-1-2h2v-2l-1-3c0-3 0-5 2-7z" class="e"></path><path d="M275 271c2 0 2 0 4 1v1h-4v-2z" class="N"></path><path d="M251 266l2 1 1-1v-3h-1c1-1 1-1 1-2 1-1 1-2 3-3l2-1c0 1 1 2 1 2v2c-1 1-3 3-5 3v2c2 1 2 2 2 4 1 2 2 4 4 5s3 1 4 1 1 0 2 1c3 0 6 0 9 1l-1 1-9-1c0-1-2-1-3-1l-1 1-1 1c-2 0-4 1-5 0l1-2-1-2h2c-1-1-2-2-2-4v-1c-1-2-2-1-4-2l-1-2z" class="D"></path><defs><linearGradient id="r" x1="510.43" y1="471.145" x2="556.936" y2="462.284" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#313131"></stop></linearGradient></defs><path fill="url(#r)" d="M520 404c1 1 2 1 2 1l1 1v1l3 2 18 18 1 1c2 3 7 7 7 10-1 0-1 0-2-1l-1 1c1 1 2 1 3 1h1v2l-1 1v1h0v2l-1 1h0v1l-1 1h1l-3 9h1c2-2 2-6 3-9h1c0 1 0 1-1 2v1h0v1l-1 1h0v1 1l-1 1v3l-12 37-12 39c-1-1-1-1-1-2-2-1-3-3-4-5l-1 2c-1 0-2 0-3-1l-1-10 1-10 1-65 1 1c0 4-1 15 1 17v-37-21z"></path><path d="M528 468c-1 0-1 0-2-1v-1h1c1 1 1 1 1 2z" class="H"></path><path d="M524 416l1 1c2 2 4 4 5 7l-5-4c-1-1-1-2-1-4z" class="L"></path><path d="M529 460c1 1 1 2 0 3v1 5l-1-1c0-1 0-1-1-2l-1-3v-1c1-1 2-2 3-2z" class="O"></path><path d="M525 409l8 11 5 5c-2 0-4-2-5-3l-3-4v-1l-1 1c2 1 4 4 5 6v1c-2-3-5-7-8-9l-1 1-1-1h0c1-2 1-4 1-7z" class="G"></path><path d="M536 462l6-1v2s-1 0-2-1l-1 1 1 1v3c-1 1-1 2-3 2h3l-1 1h1l-1 1h-2-2c-2-2-2-3-3-5 1-2 2-3 4-4z" class="L"></path><path d="M523 407l3 2 18 18 1 1c2 3 7 7 7 10-1 0-1 0-2-1s-3-2-4-4l-8-8-5-5-8-11c-1-1-2-1-2-2z" class="P"></path><defs><linearGradient id="s" x1="527.169" y1="517.756" x2="535.489" y2="505.512" xlink:href="#B"><stop offset="0" stop-color="#7b7c7c"></stop><stop offset="1" stop-color="#a1a1a3"></stop></linearGradient></defs><path fill="url(#s)" d="M525 533c2-4 2-9 3-14 2-8 5-18 8-26v1c1 2-1 7-2 9h1v-1c0-1 1-2 1-2 1-2 1-3 2-5v1l-12 39c-1-1-1-1-1-2z"></path><path d="M548 457h1c2-2 2-6 3-9h1c0 1 0 1-1 2v1h0v1l-1 1h0v1 1l-1 1v3l-12 37v-1c-1 2-1 3-2 5 0 0-1 1-1 2v1h-1c1-2 3-7 2-9v-1c0-2 2-5 2-7l10-29z" class="d"></path><path d="M547 438l1 2c0 2-1 4-2 6-1 4 0 11-3 14l-1 1-6 1c-2 1-3 2-4 4h-2l-1-2v-1l5-5 1-1c0-2 1-4 2-6l2-1c1-1 2-2 2-3h2l1 1v1c-1 2-1 3-1 5 2-5 4-9 2-15h-1c1-1 2-1 3-1z" class="U"></path><path d="M542 455v3c-1 1-2 1-3 2l-3 2c-2 1-3 2-4 4h-2l-1-2v-1l5-5c1 1 1 1 2 1s3-1 4-2l2-2z" class="Q"></path><path d="M541 447h2l1 1v1c-1 2-1 3-1 5l-1 1-2 2c-1 1-3 2-4 2s-1 0-2-1l1-1c0-2 1-4 2-6l2-1c1-1 2-2 2-3z" class="V"></path><path d="M537 451l2-1c0 3-1 5-3 7h-1c0-2 1-4 2-6z" class="K"></path><path d="M525 417l1-1c3 2 6 6 8 9 4 5 9 9 13 13-1 0-2 0-3 1h1c2 6 0 10-2 15 0-2 0-3 1-5v-1l-1-1h-2v-3-1c-1-2-2-2-4-4v-1l-1 1c-1-1-2-1-4-1v-3h1l1-3h-1l-3 3h-1l3-4c0-3 0-5-2-7-1-3-3-5-5-7z" class="Q"></path><path d="M533 435h2v-2h1l1 1c0 2 2 3 3 5h-3v-1l-1 1c-1-1-2-1-4-1v-3h1z" class="R"></path><path d="M518 444l1 1c0 4-1 15 1 17v35c0 10-2 21 1 31l-1 2c-1 0-2 0-3-1l-1-10 1-10 1-65z" class="o"></path><path d="M529 469v-5l1 2h2c1 2 1 3 3 5h2c0 1 1 3 2 3l1-1v1c-1 3-1 5-3 7-3 2-3 7-4 9 0-1-1-1 0-2-1 0-1 0-2 1-1 0 0 0-1 1v1l-1 2c-1-1-1-1-1-2-1-2-2-3-4-4v-1-1c0-1 0-2-1-3h0l1-1c1 0 2 0 3 1h2c-1-1-1-2-2-2l-1-1v-1l-2-1v-1c2 0 3-1 5-2v-5z" class="B"></path><path d="M526 478l4 4 1 1h-1c-2 0-3 0-5 1l1 1c1 1 3 0 4 1l-1 1h-2c-2-1-2-1-3-1v-1c0-1 0-2-1-3h0l1-1c1 0 2 0 3 1h2c-1-1-1-2-2-2l-1-1v-1z" class="L"></path><path d="M529 469v-5l1 2h2c1 2 1 3 3 5 1 1 2 2 2 4v2 1l-1 2c-1 1-1 2-2 3v1c-1-2 1-2-1-3l-3-3c1-1 0-1 1-1 0-1 0-1-1-2-1 0 0 0-1-1v-5z" class="K"></path><path d="M529 435h1l3-3h1l-1 3h-1v3c2 0 3 0 4 1l1-1v1c2 2 3 2 4 4v1 3c0 1-1 2-2 3l-2 1c-1 2-2 4-2 6l-1 1-5 5c1-1 1-2 0-3l-3-6c-1-1-1-2-2-3l-1-2c0-2 0-4 1-5h0c0-1-1-2-1-3s0-2 1-4h0c2-1 3-2 5-2z" class="J"></path><path d="M524 437h0c2 2 3 2 5 1h3c1 1 1 1 1 3l-2 2v-1c-3 1-5-2-7-3v-2z" class="B"></path><path d="M524 437v2c2 1 4 4 7 3v1c2 1 2 2 4 2l1-1c0-1 0-2-1-2v-2c2 1 3 2 4 4 0 1 0 1-1 2v1s-1 1-2 1c-3 0-4-2-6-3l-3-1v1h-1l-2 2c0 1 0 1 1 2s1 3 1 5c-1-1-1-2-2-3l-1-2c0-2 0-4 1-5h0c0-1-1-2-1-3s0-2 1-4z" class="C"></path><path d="M526 454c0-2 0-4-1-5s-1-1-1-2l2-2h1v-1l3 1c2 1 3 3 6 3 1 0 2-1 2-1v1 1h-1-1v1c0 1-1 2-1 2v1c1-1 1-2 2-2-1 2-2 4-2 6l-1 1-5 5c1-1 1-2 0-3l-3-6z" class="D"></path><path d="M530 445c2 1 3 3 6 3 1 0 2-1 2-1v1 1h-1-1v1c0 1-1 2-1 2v1l-3 3v-3c-1-2-2-3-1-6h0c-1-1-1-1-1-2z" class="K"></path><path d="M186 173c2 0 2 1 3 3l2-2c3 2 6 4 9 5l12 5c8 3 17 7 24 12 2 1 3 2 5 3 3 2 8 4 10 6l-1 1v1l-1 1h-1l2 1h2l1 1-1 2 2 2h0l-1-1v3h0l1 1c1 0 1 0 2-1v1 2l2 2v1c-1-1-1-1-2-1h-1l-1 1c1 0 2 1 3 1v1h-1 0l2 1c0 1-1 2-1 2-2 0-1 0-3 1v-2h0l-2 1c0 1 0 1 1 2l-3 3c-2 2-3 2-4 3-1 0-2-1-3-1l-1-1-2 2v-1c-2-2-2-5-2-8 0 0 2-2 2-3l-3 1c-1 2-2 4-4 6 1 2 2 2 2 4l1 1c-2 0-2-1-4-2h-1c-1-1-3-1-5-2h-5l-2-3c-2-1-3-1-5-1-1-2-3-4-4-6s-3-4-4-6c-1-1-1-1-3-2v-1c-1 0-3-1-4-2h-2c-2 1-4 1-5 2-4-2-8-2-12-2v-1c-2 0-5 0-7-1h-4c-2-1-4-2-5-4-3-5-2-11-1-17-1-1-1-2-2-3h-2l1-2c1-1 2-1 3-2l5-2c5 1 7-1 11-2 1 0 3 1 4 1l1 1v-2l2-3z" class="l"></path><path d="M235 219h1c2-1 3-3 4-5l1 1c-1 2-3 3-4 5h0c-1 2-2 2-3 4 1 1 1 1 1 2h-1c-1-1-2-2-2-3 1-2 2-3 3-4zm13 6c1-1 1-2 2-2v-1h0l-2-2h1l1-1c0-1 0-1 1-1v3l1 1c0 1 0 2-1 3v1h1 1c1-1 1-1 2-1l1-1 2 1c0 1-1 2-1 2-2 0-1 0-3 1v-2h0l-2 1c0 1 0 1 1 2l-3 3c-1 0-2 0-2-1 1 0 2-2 3-3l-3-3z" class="N"></path><path d="M237 220c1 0 2-1 3-1-1 2-2 3-3 5s-2 4-4 6c1 2 2 2 2 4l1 1c-2 0-2-1-4-2h-1c-1-1-3-1-5-2 1-1 1-1 1-2l2-3 3-3h0c0 1 1 2 2 3h1c0-1 0-1-1-2 1-2 2-2 3-4z" class="i"></path><path d="M229 226c1 2 3 3 4 4 1 2 2 2 2 4l1 1c-2 0-2-1-4-2h-1c-1-1-3-1-5-2 1-1 1-1 1-2l2-3z" class="Z"></path><path d="M242 233c-1 0-1-1-2-2v-4c2-4 4-2 7-4l1 2 3 3c-1 1-2 3-3 3 0 1 1 1 2 1-2 2-3 2-4 3-1 0-2-1-3-1l-1-1z" class="G"></path><path d="M203 203c-1-1-1-2-2-3h0c0-1-1-2-1-3s0-1-1-1l1-1 1 1h4c1-1 2-1 3-1h1l-1 1v1 1c-1-1-1-2-2-2-1 1-1 1-1 2-1 0 0 0-1-1l-1 2c1 1 2 1 4 1h1c1-1 2-2 4-2s3-1 5-1h2c0 1 1 1 0 3h1l1-1v1c1 1 2 2 3 2v1 1h-4c-2 1-3 1-5 2h0c-4 2-6 6-9 9-1-1-1-1-3-2v-1c-1 0-3-1-4-2 2-2 3-3 4-7h0z" class="c"></path><path d="M203 203c2 1 3 2 3 5v1h0l-1 1-2 2c-1 0-3-1-4-2 2-2 3-3 4-7h0zm0-4c1 1 2 1 4 1h1c1-1 2-2 4-2s3-1 5-1h2c0 1 1 1 0 3l-2-1s-1 1-2 1v-1c-1 0-1 1-2 2h-1c-1-1-2 0-3 1l-1-1c-2 0-3 0-4 1l-2-2-1-3h1c0 1 0 1 1 2zm-14-23l2-2c3 2 6 4 9 5l12 5c8 3 17 7 24 12 2 1 3 2 5 3 3 2 8 4 10 6l-1 1v-1c-1 1-2 1-3 2h0v3h-1v-2c-1-1-1-1-1-2h1c-2-3-5-3-6-5h-1-2v-1h0c-2-1-2-2-3-3h1 1l-1-1c-1 1-1 1-2 1s-1 0-2 1h0c-2-1-2 0-3 0l-2-1-1 1-1-1c2-1 4 0 5-1l-2-2c-1 1-1 0-2 1h-1 0c1-1 2-1 3-2l-2-2v1c-1 0-3 1-4 1 1-1 1-1 1-2h-2-1c-1-2-2-2-3-2-2 0-3 0-4-1l-2-2h-1c-1-1-2-1-3-1h-1c-2 0-4 1-6 1-1 0-1 0-1 1l-1-1 1-1h3c1-1 1-1 2-1h1v-1c-2 0-3-1-5-2h-1c-2-1-3-1-5-1-1 0-1-1-1-1l-1 1v1c-1-1-1-1-1-2l-2-1 1-2z" class="O"></path><path d="M224 204c1 1 1 1 3 1 1-1 1-2 1-2l-1-1c2-1 2 0 3-1-2-2-4 0-6-2h1 1c1 0 3 0 4-1v1h3 1l1 1v1l-2 2h1c1 0 2 0 4-1 1 1 1 2 2 3h0c-1 1-1 1-2 1h-1l1-2h0-1l-1 1v-1h-3v1c-1-1-1-1-1-2h-1-2c-1 0-1 0-1 1s1 2 2 2c1 1 3 1 4 1 2 1 2 1 2 2 1 1 1 2 2 3h0l1 2c-1 1-1 2-2 3l-2 2c-1 1-2 2-3 4h0l-3 3-2 3c0 1 0 1-1 2h-5l-2-3c-2-1-3-1-5-1-1-2-3-4-4-6s-3-4-4-6c3-3 5-7 9-9h0c2-1 3-1 5-2h4z" class="Q"></path><path d="M217 211h6l3 3-1 1h-2l-1-1c-1 1-1 0-1 1h0c-1 0-1 0-2-1h0l-1-1-4 2h-1l4-4z" class="c"></path><path d="M206 215c3-3 5-7 9-9l-1 3v1h5-1l-1 1-4 4c-2 1-2 2-3 4v2c-1-2-3-4-4-6z" class="U"></path><defs><linearGradient id="t" x1="216.729" y1="224.155" x2="214.829" y2="214.634" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#t)" d="M213 215h1l4-2 1 1h0c1 1 1 1 2 1v2h-1-2l-1 1c1 1 1 1 2 1 0 1-1 1-1 2v3h-2c0 2 2 3 3 4-2-1-3-1-5-1-1-2-3-4-4-6v-2c1-2 1-3 3-4z"></path><path d="M228 215v-2-1c0-1 0-1-1-2 0-1-1-1-2-2h-2l1-1h1 5 1c2 0 3 0 4 1s1 2 1 3c-1 2-3 4-4 6-2 3-3 4-4 7-1-1-1-2-2-4 0-1 1 0 0-1l-1 1-2-1c-2 0-3 0-4-1l-1-1h2 1v-2h0c0-1 0 0 1-1l1 1h2l1-1 2 1z" class="i"></path><path d="M226 214l2 1v1c-1 1-2 1-3 2l-1-2 1-1 1-1z" class="Z"></path><path d="M236 209c1 1 1 2 2 3h0l1 2c-1 1-1 2-2 3l-2 2c-1 1-2 2-3 4h0l-3 3-2 3c0 1 0 1-1 2h-5l-2-3c-1-1-3-2-3-4h2v-3c0-1 1-1 1-2-1 0-1 0-2-1l1-1 1 1c1 1 2 1 4 1l2 1 1-1c1 1 0 0 0 1 1 2 1 3 2 4 1-3 2-4 4-7 1-2 3-4 4-6v-2z" class="a"></path><path d="M232 223l-1-1h-1v-1h1c1-1 1-3 2-4s2 0 4 0l-2 2c-1 1-2 2-3 4h0z" class="R"></path><path d="M218 224v-3c0-1 1-1 1-2-1 0-1 0-2-1l1-1 1 1c1 1 2 1 4 1l2 1 1-1c1 1 0 0 0 1 1 2 1 3 2 4 0 1-1 1-1 3l-1 1-2-1h-2v-2h-2l-2-1z" class="V"></path><path d="M224 227h0v-5h1c1 2 1 3 2 5l-1 1-2-1z" class="i"></path><path d="M186 173c2 0 2 1 3 3l-1 2 2 1c0 1 0 1 1 2v-1l1-1s0 1 1 1c2 0 3 0 5 1h1c2 1 3 2 5 2v1h-1c-1 0-1 0-2 1h-3l-1 1 1 1c0-1 0-1 1-1 2 0 4-1 6-1h1c1 0 2 0 3 1h1l2 2c1 1 2 1 4 1 1 0 2 0 3 2l-2 2s-1 0-1 1c-1-2-1-2-1-4h0-1c-1 2-2 2-3 2h0c-2 1-4 2-6 2v-1l-1 1c-1 1-2 1-4 1l-1 1c1 0 1 0 1 1s1 2 1 3h0c1 1 1 2 2 3h0c-1 4-2 5-4 7h-2c-2 1-4 1-5 2-4-2-8-2-12-2v-1c-2 0-5 0-7-1h-4c-2-1-4-2-5-4-3-5-2-11-1-17-1-1-1-2-2-3h-2l1-2c1-1 2-1 3-2l5-2c5 1 7-1 11-2 1 0 3 1 4 1l1 1v-2l2-3z" class="K"></path><path d="M173 208h3c-2-1-3-1-5-2 0 0 0-1 1-1l1 1h4l1-1h1c3-2 4-6 4-8v-1h1v2c-1 2-1 3-1 4l1 1-1 3-3 3h0c-2 0-5 0-7-1z" class="M"></path><path d="M179 197v-1l2 2-2 1c0 1 0 1 1 3l-1 2c-2 1-2 1-4 1h-1c-1-1-2-2-3-4 1-1 1-1 1-2 2-2 4-2 7-2z" class="N"></path><path d="M174 189h0c1-1 1-1 2-1h2c0 1 0 1-1 2l1 1h0c1-1 1-1 2-1l2 1v1c-1 0-2 1-3 2h1 0l-2 1h-2c-1 0-2 0-4 1h-2-1-3v-1c2 0 3 0 4-2h1 2v-2-1h0l1-1z" class="J"></path><path d="M174 189h0c1-1 1-1 2-1h2c0 1 0 1-1 2l1 1-1 1-1 1-1-1v-1l-1-1h0c-1 1 0 1-1 1v-1h0l1-1z" class="E"></path><path d="M163 187c0-2 0-3 2-4v2 2h0l1 1 4-1 1-1 1-1c2 0 4 0 6 1-2 1-3 1-4 3l-1 1h0v1 2h-2-1c-1 2-2 2-4 2v1c0 1 0 3-1 4l1 3h0l-2 1c-3-5-2-11-1-17z" class="C"></path><path d="M165 200c0-2 0-5 1-7 2-1 3-2 5-4h0l2 1v1 2h-2-1c-1 2-2 2-4 2v1c0 1 0 3-1 4z" class="O"></path><path d="M194 188l1 1v1c0 1 0 1 1 2h-1c0 2-2 3-1 5v3c-1 1-2 2-4 2h-1v1l3 1h0c-1 1-2 1-3 2l-1-1-1 1 1 2c-2 1-4 0-5 1h-3l3-3h2v-3l1-3s-1 0-1-1 0-3 1-4v-1c1-1 2-3 3-3l1-1h2l2-2z" class="Z"></path><path d="M193 180c2 0 3 0 5 1h1c2 1 3 2 5 2v1h-1c-1 0-1 0-2 1h-3l-1 1 1 1c0-1 0-1 1-1 2 0 4-1 6-1h1c1 0 2 0 3 1h1l2 2c1 1 2 1 4 1 1 0 2 0 3 2l-2 2s-1 0-1 1c-1-2-1-2-1-4h0-1c-1 2-2 2-3 2h0c-1 0-1 0-2-1h0v-4h-1v1c0 2 0 2-1 4-3 0-5 1-7-1l-1 1-2-2-1 1 2 2h0-1v2l-1 1c0 1 1 0 1 2-1 0-1 0-1-1l-1 1 1 1c-2 2-3 3-4 5l-3-1v-1h1c2 0 3-1 4-2v-3c-1-2 1-3 1-5h1c-1-1-1-1-1-2v-1l-1-1v-1l-1 1h-1v-1l-1-1v-1c0-1 0-2 1-3l1 1-1 1 3 1c0-1 0-1 1-1v1l2-2c-1-2-2-1-3-1-1-1-1-2-2-2z" class="P"></path><path d="M192 204c1-2 2-3 4-5l-1-1 1-1c0 1 0 1 1 1 0-2-1-1-1-2l1-1v-2h1 0l-2-2 1-1 2 2 1-1c2 2 4 1 7 1 1-2 1-2 1-4v-1h1v4h0c1 1 1 1 2 1-2 1-4 2-6 2v-1l-1 1c-1 1-2 1-4 1l-1 1c1 0 1 0 1 1s1 2 1 3h0c1 1 1 2 2 3h0c-1 4-2 5-4 7h-2c-2 1-4 1-5 2-4-2-8-2-12-2v-1h0 3c1-1 3 0 5-1l-1-2 1-1 1 1c1-1 2-1 3-2h0z" class="V"></path><path d="M192 204l2-1c0-1 1-1 2-2h0v3 1h0l-1-1-1 1v2l1 1c3 0 5-3 6-6l2 1c-1 4-2 5-4 7h-2c-2 1-4 1-5 2-4-2-8-2-12-2v-1h0 3c1-1 3 0 5-1l-1-2 1-1 1 1c1-1 2-1 3-2z" class="c"></path><path d="M180 209h3 8c2-1 4 0 6 1-2 1-4 1-5 2-4-2-8-2-12-2v-1h0z" class="Y"></path><path d="M168 178c5 1 7-1 11-2 1 0 3 1 4 1l1 1v4c0 2 1 4 2 5l-1 1h-1-2c0 1 0 0-1 1l-3-3c-2-1-4-1-6-1l-1 1-1 1-4 1-1-1h0v-2-2c-2 1-2 2-2 4-1-1-1-2-2-3h-2l1-2c1-1 2-1 3-2l5-2z" class="X"></path><path d="M175 182c1 0 4 0 5-1v-1h-2v-1h2c2 2 4 6 4 9h-2 0l-3-3 1-1h0v-1c-2 1-2 1-3 1-1-1-2-1-2-2z" class="O"></path><path d="M165 183c3-1 7-1 10-1 0 1 1 1 2 2 1 0 1 0 3-1v1h0l-1 1 3 3h0c0 1 0 0-1 1l-3-3c-2-1-4-1-6-1l-1 1-1 1-4 1-1-1h0v-2-2z" class="F"></path><path d="M165 185h4l1 1-1 1h-4v-2z" class="H"></path><path d="M360 248l3-3c1 2 1 6 1 8l-1 1v1c0 2-2 3-2 6-1 1 0 2 0 3-1 1-1 0-1 1-1 1 0 3-1 5v2 1c0 2 0 3 1 5l24 56 26 64c1 2 4 8 4 11h0v2c1 1 1 1 1 2h-1-5l-2 1 1 1c2 2 6 10 7 10l1 3-2 2-1 1c0-2-1-2-2-3 0-1 1-2 1-3v-1c-3-2-5-5-8-7h0l-2-2v-1c-1-1-2-1-2-2h-1v-1l-1-1v-1l-3-7-2-6-2-6-2-3h-1v-1l-1 1 1 2h-1v1h1v1h-1c-1 0-1 1-2 1h-2l-1-1c1 0 2-1 3-1v-3c0-1 0-1-1-2h0c0-1 0-2-1-3-1-3-2-6-4-9v-2l-1 1h-1c1-2 0-2 0-4 0 0-1 0-1-1v-1l-1-1-1-1v-1h-4l-2-1 1-2h1v-1l2-2c0-2-1-3-1-4-1 0-2-1-2-2l1-1c-1-1-1-2-2-4-1-1 0-1 0-2-1-1-1-1-1-2l-1-3h-1c0-1 0-2-1-3 0-1 0-1-1-2h-1-1-1v-1-2h1c0-2 0-2-1-3h0c0-1 0-1 1-2-1 0-2-1-2-1h-3-1c-1 0-2 0-3-1 2 0 2-1 3-2h1v-1h-3v2l-1-2c0 1-1 0-1 0 1 0 2-1 3-1h1c1-1 1-1 2-1s1 0 2-1v-2c-1 1-1 1-2 1l-1 1h-1c0-2 1-3-1-4h0-1c0-1-1-1-1-2-1-3-3-5-4-8-1 0-1-1-2-1l1-1v-1l-2-5h-1l-6-6c-1-1-1-1-2-1h0-1v-3-1l1 1 2-1c1 1 2 1 3 1l-1-1 2-2h0v1h1l-1 2h1l1-1h2 1l-2-3 1-1h1c0 1 0 2 1 3h1l-1-4-1-2c1 0 2-1 2-2 0-2-1-3-1-5l3 3c1-1 2 0 3-2h0s-1 0-1-1v-2l-1-1v-1h-1c-1-2 1-5 1-7 1-3 1-5 1-8 1-1 2-2 2-3 2-1 3-1 4-2l1 1z" class="R"></path><defs><linearGradient id="u" x1="346.112" y1="289.448" x2="340.563" y2="298.379" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#464949"></stop></linearGradient></defs><path fill="url(#u)" d="M335 289v-3-1l1 1h1c1 1 3 1 4 1 2 1 3 1 5 2 2 0 3 2 4 4v1l4 7-1 1v1 1c-1-1-1-1-2-3v1l-1 1 1 1h0-1l-1 1h1c1 0 1 1 2 2h0c-2-1-3-2-4-3-1 0-1-1-2-1l1-1v-1l-2-5h-1l-6-6c-1-1-1-1-2-1h0-1z"></path><path d="M338 290c3 0 5 2 7 4l-1 2-6-6z" class="G"></path><path d="M370 363c0-1 0-1 1-2 0-1 0-2 1-3v-1c1-2 0-2 0-3s0-2 1-3c1 3 2 5 2 7v1 1h3l1 4 3 5v1c1 1 1 3 2 4 0 2 2 5 3 7l2 6h-1v-1l-1 1 1 2h-1v1h1v1h-1c-1 0-1 1-2 1h-2l-1-1c1 0 2-1 3-1v-3c0-1 0-1-1-2h0c0-1 0-2-1-3-1-3-2-6-4-9v-2l-1 1h-1c1-2 0-2 0-4 0 0-1 0-1-1v-1l-1-1-1-1v-1h-4z" class="F"></path><path d="M348 304c1 1 2 2 4 3h0c-1-1-1-2-2-2h-1l1-1h1 0l-1-1 1-1v-1c1 2 1 2 2 3v-1-1l1-1 2 6 1 2 1 2c0 2 0 3 1 5l2 3c2 3 3 5 4 8 1 2 2 3 2 5l3 7v1c2 3 3 7 5 11v2l2 4c0 1 0 2 1 3h-3v-1-1c0-2-1-4-2-7-1 1-1 2-1 3s1 1 0 3v1c-1 1-1 2-1 3-1 1-1 1-1 2l-2-1 1-2h1v-1l2-2c0-2-1-3-1-4-1 0-2-1-2-2l1-1c-1-1-1-2-2-4-1-1 0-1 0-2-1-1-1-1-1-2l-1-3h-1c0-1 0-2-1-3 0-1 0-1-1-2h-1-1-1v-1-2h1c0-2 0-2-1-3h0c0-1 0-1 1-2-1 0-2-1-2-1h-3-1c-1 0-2 0-3-1 2 0 2-1 3-2h1v-1h-3v2l-1-2c0 1-1 0-1 0 1 0 2-1 3-1h1c1-1 1-1 2-1s1 0 2-1v-2c-1 1-1 1-2 1l-1 1h-1c0-2 1-3-1-4h0-1c0-1-1-1-1-2-1-3-3-5-4-8z" class="B"></path><path d="M357 302c1-2-1-5-2-7h1c1 1 1 3 2 4l1 1 25 58 1 4 20 50 2 2 1 1 1 2h-1l-3-2c-1-1-1-2-2-3-1-2-1-4-2-6-1-3-3-5-4-8s-2-7-3-10l-8-15-6-17-15-35c-2-6-5-12-8-19z" class="g"></path><path d="M360 248l3-3c1 2 1 6 1 8l-1 1v1c0 2-2 3-2 6-1 1 0 2 0 3-1 1-1 0-1 1-1 1 0 3-1 5v2 1l-1 1 1 1v5c2 3 3 6 3 10-1 0-2 0-2 1-1 1-1 0 0 1l-1 1c-1 1-1 0 0 1l-1 3 1 3-1-1c-1-1-1-3-2-4h-1c1 2 3 5 2 7-3-4-5-11-7-15l-1-3-1-4-1-2c1 0 2-1 2-2 0-2-1-3-1-5l3 3c1-1 2 0 3-2h0s-1 0-1-1v-2l-1-1v-1h-1c-1-2 1-5 1-7 1-3 1-5 1-8 1-1 2-2 2-3 2-1 3-1 4-2l1 1z" class="F"></path><path d="M353 269c1 0 3 1 3 2 0 2 0 4-1 6-2-1-3-2-4-3 1-1 2 0 3-2h0s-1 0-1-1v-2z" class="h"></path><path d="M355 249c2-1 3-1 4-2l1 1c-3 3-4 7-6 11 0-2-1-4-1-7 1-1 2-2 2-3z" class="g"></path><path d="M349 276c1 2 2 3 2 4l7 17 1 3-1-1c-1-1-1-3-2-4h-1c1 2 3 5 2 7-3-4-5-11-7-15l-1-3-1-4-1-2c1 0 2-1 2-2z" class="P"></path><path d="M349 276c1 2 2 3 2 4-1 3 0 4-1 7l-1-3-1-4-1-2c1 0 2-1 2-2z" class="l"></path><path d="M354 259c0 3-1 7-1 10 3-7 4-14 7-20 0 2-1 5-1 8-1 5-1 9-3 14 0-1-2-2-3-2l-1-1v-1h-1c-1-2 1-5 1-7 1-3 1-5 1-8 0 3 1 5 1 7z" class="f"></path><path d="M359 273c0 2 0 3 1 5l24 56 26 64c1 2 4 8 4 11h0v2c1 1 1 1 1 2h-1-5l-2 1-2-2-20-50-1-4-25-58-1-3 1-3c-1-1-1 0 0-1l1-1c-1-1-1 0 0-1 0-1 1-1 2-1 0-4-1-7-3-10v-5l-1-1 1-1z" class="X"></path><path d="M383 343l2-2s0 1 1 2v2c2 4 3 8 5 12 0 2 2 3 1 5h-3v1c0-1-1-2-1-2 0-2 0-2 1-3l-1-1-1 1-1-1 1-1v-1h-1v-2c-1 0-1 0-2-1v-2h-1c0-1-1-2-1-3 0-2 0-2 2-4h-1z" class="D"></path><path d="M389 363v-1l1 1c2 0 2 0 4 1 0 1 0 1-1 2l1 1 1-1 1 1-1 2v1c1 1 1 0 1 1 2 1 2 1 2 2l-1 2h2v1c1 2 2 3 2 6 1 4 3 9 4 12h1l-1 1c0 1 0 1-1 2h-1c1-1 0-1 1-2l-2-2h0c-1-2-1-3-1-4l-2-2v-1h0c0-2-1-2-2-4v-1c-1-2-3-5-3-7-1-1-1-2-1-3-1-1-2-3-3-4 0-2-1-3-1-4z" class="J"></path><path d="M387 361v1h1v-1s1 1 1 2 1 2 1 4c1 1 2 3 3 4 0 1 0 2 1 3 0 2 2 5 3 7v1c1 2 2 2 2 4h0v1l2 2c0 1 0 2 1 4h0l2 2c-1 1 0 1-1 2h1c1-1 1-1 1-2l1-1c0 1-1 2 0 3l1-1 1 1h0l-2 2 1 1c1 0 1 0 2-1v2s-1 0-2 1c2 1 3 1 4 2l1 2 1 1 1 2h0v2c1 1 1 1 1 2h-1-5l-2 1-2-2-20-50c0-1 1-1 2-1z" class="F"></path><path d="M405 406l-1-1v-2h2v2l-1 1z" class="b"></path><path d="M407 402c2 1 3 1 4 2l1 2 1 1 1 2h0v2c1 1 1 1 1 2h-1-5l-2 1-2-2h1 0l1-1v-2c-1-1-2-1-2-2v-1l1-1v-2c1 0 1 0 1-1z" class="H"></path><path d="M360 293l2-1 1 1h1c0 2-1 3-2 4h1c1 0 2 0 2-1v2h0 1c0 1-2 2-2 3h2l-1 1v1h1 1v1l1 1c0 1 0 1-1 1v1h2l1 1-1 1c1 1 1 0 2 1l-1 2h1l1-1c2 2 2 5 3 7 1 3 3 5 3 7v1h-3l1 1h0c1 0 1 0 2 1l1-2c1 1 0 1 1 2-1 1-3 2-3 3h1l2-1c1 0 1 1 2 2v1 2l1 1h-1-3v1h4c1 1 1 1 1 2-1 0-2-1-3-1l-1 1 1 1 1-1 1 1c0 1-1 1-1 2s0 1 1 1h1c-2 2-2 2-2 4 0 1 1 2 1 3h1v2c1 1 1 1 2 1v2h1v1l-1 1 1 1 1-1 1 1c-1 1-1 1-1 3v1h-1v-1c-1 0-2 0-2 1l-1-4-25-58-1-3 1-3c-1-1-1 0 0-1l1-1v1z" class="S"></path><path d="M359 294c-1-1-1 0 0-1l1-1v1l1 2h-1l-1-1z" class="H"></path><path d="M384 358h3v1 1 1c-1 0-2 0-2 1l-1-4z" class="C"></path><path d="M191 147c2-1 3-2 5-2h16 48 63l77 1v1c0 1-1 1-1 2-2 3-5 8-5 11l-1 2h-6c-7-1-14 0-20 0h-48-63-10-12c2-1 6-1 8-1h0c-2 0-3-1-5-1-13-1-28 2-41 0h-4l-2-1c-1-4-1-8 1-12z" class="E"></path><path d="M385 150l2-1v1c-1 0-1 0-1 1s0 1 1 2v1c1 0 1-1 2-1 0 1-1 2-2 3h-3l-1-5 2-1h0z" class="C"></path><path d="M345 157c2 0 3-2 5-4 1-1 1-1 3-1v2 2c-1 2-5 1-7 2h-4c1-1 1-1 3-1z" class="V"></path><path d="M285 151c2-2 5-1 7-1l1 1v2c1 1 1 3 3 3 1 1 2 1 3 1h1l1 1c-1 1-4 0-6 0 0 0-2 0-2-1 0-2-1-1-2-2 0-1 0-3-1-4h-4-1z" class="G"></path><path d="M260 149h10v3c-1 1-1 3-1 4l1 1h0-4v-3c-1-1-1-2-2-3h-3 0l-1-2z" class="D"></path><path d="M261 151h3c1 1 1 2 2 3v3c-1-1-2 0-3-1 1-2 1-2 1-4l-1 1c0 1 0 2-1 3-1 0-1 0-1 1h-1c-2 1-4 1-7 1l-1-1c-2-1-4-1-6-1v-4c1 0 2-1 3-1l1 1c1 3 1 4 3 5h4c1 0 2-1 2-1 2-2 2-3 2-5z" class="R"></path><path d="M293 157c-2 0-6-1-8-1h-2-4l1-1c1-3 1-4 4-6v1 1c0 2 0 3 1 4 1-2 0-3 0-4h1 4c1 1 1 3 1 4 1 1 2 0 2 2z" class="Q"></path><path d="M376 149l9 1h0l-2 1 1 5h3v1h-2c-2-1-5-1-7 0s-4 1-6 1c2-1 3-1 4-3v-4h-1v-1l1-1z" class="U"></path><path d="M384 156h-1c-1 0-2 0-3-1v-2c1-1 2-2 3-2l1 5zm-22-7l1 1v2l-1 1c1 2 1 2 3 4 2 0 4 0 7-1 1 0 2-1 2-2l1-3h1v4c-1 2-2 2-4 3h-7c-3-1-6-1-9-1-1 0 0 1-2 0l1-3 7-3v-1-1z" class="Z"></path><path d="M363 152l3-3h10l-1 1v1l-1 3c0 1-1 2-2 2-3 1-5 1-7 1-2-2-2-2-3-4l1-1zm-22-3h4c2-1 8 0 11 0h6v1 1l-7 3h-2v-2c-2 0-2 0-3 1-2 2-3 4-5 4v-1h-3l-2-2h0c-1-2-1-2-1-3l2-2z" class="B"></path><path d="M295 150c2-1 2-1 4-1h0c1 1 1 1 2 1l1-1c2 0 12-2 14 0v1h1c1 1 1 2 0 3l1 1h-1l-1-1c-1 2-1 2-2 3h-2-3 0-5-2c-1-1-1-2-1-3-1 1-2 2-3 2v-1c-1 0-1 0-2-1v-1l-1-1v-1z" class="G"></path><path d="M296 152v-2h3v3l-1 1c-1 0-1 0-2-1v-1z" class="C"></path><path d="M301 153l1-3 1 1h0l2 4-1 1h-2c-1-1-1-2-1-3z" class="U"></path><path d="M306 152c0-1 1-2 2-2h2l4 1c1 0 1 1 2 2-1 2-1 2-2 3h-2-3 0-5l1-1 1-3z" class="a"></path><path d="M306 152c0-1 1-2 2-2h2l4 1c1 0 1 1 2 2-1 2-1 2-2 3h-2l1-1c0-1 0-1-1-2l-6-1z" class="D"></path><path d="M237 160h99 41 17l-1 2h-6c-7-1-14 0-20 0h-48-63-10-12c2-1 6-1 8-1h0c-2 0-3-1-5-1z" class="g"></path><path d="M191 147c2-1 3-2 5-2h16 48 63l77 1v1c0 1-1 1-1 2-3 0-7 0-9 1h-3v-1l-2 1-9-1h-10l-3 3v-2l-1-1h-6c-3 0-9-1-11 0h-4-1-1c-3-2-7-1-10 0h-10s-1 1-2 1h-1v-1c-2-2-12 0-14 0l-1 1c-1 0-1 0-2-1h0c-2 0-2 0-4 1h-1c-2-1-3-1-4-3-2 0-5 0-8 1-1 0-1 1-2 2v1c0 3-2 4-3 5h-1v-1c2-1 3-2 3-4-1 0-1-1-2-1h0l-1-1c-2 0-2 0-3 1l-1 1h-1v-2h-1-10l1 2h0c0 2 0 3-2 5 0 0-1 1-2 1h-4c-2-1-2-2-3-5l-1-1c-1 0-2 1-3 1v4c2 0 4 0 6 1l1 1h-1l1 1h-7-7-19-2 0c-5 0-19-1-22 1h-4l-2-1c-1-4-1-8 1-12z" class="I"></path><path d="M257 157c1-1 1-2 1-3h0-2v1l-1-1c0-1 1-1 1-2-1-1-1-1-2-1l-1 1c0 1 0 1 1 2l-1 1h0c-1-1-2-1-2-3 0-1 1-2 2-2 3 0 3 0 5 2v-1-2h2l1 2h0c0 2 0 3-2 5 0 0-1 1-2 1z" class="B"></path><path d="M241 149h9v1 2l-1-1c-1 0-2 1-3 1v4c2 0 4 0 6 1l1 1h-1c-1 0-2 1-3 1s-1 0-2-1l2-1h-1c-1 0-2 1-3 0h-1v1c-1 1-2 1-3 1-1-1-7-1-9-1s-4 0-7-1l2-1c2 0 3 1 5 1h0c2 0 3-1 4-3l1-3v-2h4z" class="G"></path><path d="M237 151h4v2c-2 0-3 0-5 1l1-3z" class="J"></path><path d="M246 156h-2v-1c-1-2-1-2-1-3l5-2h2v2l-1-1c-1 0-2 1-3 1v4z" class="U"></path><path d="M236 154c2-1 3-1 5-1 0 2-1 3-2 4-2 1-5 1-7 1s-4 0-7-1l2-1c2 0 3 1 5 1h0c2 0 3-1 4-3z" class="a"></path><path d="M234 150l1-1h6-4v2l-1 3c-1 2-2 3-4 3h0c-2 0-3-1-5-1l-2 1c-2 1-5 1-7 0h-12c-1 0-3 0-4-1v-1c1 0 1-1 2-2 0-1 0-1 1-1l1-1h-2v-1c2-1 3 0 5-1 5-1 8 0 14 1h3c1 0 2 0 3-1h0c2 0 3 0 5 1z" class="O"></path><path d="M212 153c0-1 1-1 2-2 2 0 3 0 4 1l2 2c-2-1-3-1-5-1v2 1h-2c0-1 0-1-1-2v-1z" class="D"></path><path d="M234 150l1-1h6-4v2l-1 3c-1 2-2 3-4 3h0c-1-1-1 0-1-1s2-2 2-3h0c1-1 1-2 1-3z" class="C"></path><path d="M215 155v-2c2 0 3 0 5 1l-1 1c1 0 1-1 2-1 2 0 4 1 6 2h0l-2 1c-2 1-5 1-7 0 1-1 1-2 0-3-2 0-2 0-3 1z" class="K"></path><path d="M212 153v1c1 1 1 1 1 2h2v-1c1-1 1-1 3-1 1 1 1 2 0 3h-12c-1 0-3 0-4-1v-1h1c1 0 1-1 2-1 0 1 1 1 2 1s1-1 2-1l1 1h1l1-2z" class="Z"></path><defs><linearGradient id="v" x1="511.976" y1="811.992" x2="504.802" y2="923.473" xlink:href="#B"><stop offset="0" stop-color="#080908"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#v)" d="M570 803h2s1-1 2-1v1l-6 12-16 36-39 76c0-1-1-3-2-5l-1 3c-3-7-5-14-11-19h0c-3-2-4-5-6-9-3-9-8-19-12-27-2-3-5-4-6-6l-5-12h2c-1-2-1-3-1-5v-4c0-1-3-4-4-5-1-2-2-5-3-8l-3-1c-3-5-5-11-6-16h0l1-2c1 1 1 1 2 0l1-2h0c1 3 6 5 9 7l2 2-2 1c2 4 5 8 6 13 1 3 2 6 4 9l2 2c4 4 9 8 12 12 2 2 3 3 4 5h1 1v1 1h1v-1c1-2 0-2-1-4v-1-3l2 1 2-1c0 1 1 3 2 4l2 3c0 1 2 2 3 3h1 1c1 0 2 0 4-1h1 1v-2-1h1l1 1h1v1h1c0 1 0 2-1 3-1 4-2 6-3 9-1 1-1 0-1 1v2l6-11c1-2 3-4 4-6 0-4 2-6 5-9v-2c1-2 2-2 3-4 1-1 1-1 1-2h0c2-2 5-5 8-6 1 0 2-1 3-2h0c2-3 3-5 4-7 2-4 3-8 5-11s4-4 6-5h1c2-1 4-1 6-1l2-7z"></path><path d="M521 898l3-3c0 2-1 3-3 5-5 5-7 11-8 18 0-1 0-3-1-4l-3-8h1c2 0 2 0 4-2l3-3c1-1 2-1 4-3z" class="D"></path><path d="M499 899c4 2 5 3 7 7l4 13c0 1 1 3 1 3l-1 3c-3-7-5-14-11-19h2c0 1 1 1 2 2 0 1 0 1 1 1 0-1-1-3-1-4 0 1 0 1 1 1v-1c0-3-3-3-5-5v-1z" class="Y"></path><path d="M499 892c-1-2-3-5-4-7-2-2-2-5-3-8-2-3-6-8-6-12 0 1 1 2 2 3h0c2 1 3 4 5 6 1 2 2 3 4 5-2-3-3-5-5-8v-1c-1-1-1-1-1-2 1 1 2 3 3 5l6 10c-1 0-3-1-4 0 1 1 1 2 2 3 1 2 1 4 1 6z" class="K"></path><path d="M459 809h0c1 3 6 5 9 7l2 2-2 1c2 4 5 8 6 13l-4-3h-1c-1-1-1-3-2-4l-4-8-4-5c-2 0-2 0-3 1 0 3 2 6 3 8 1 1 1 2 1 2 2 2 3 5 4 7l-3-1c-3-5-5-11-6-16h0l1-2c1 1 1 1 2 0l1-2z" class="V"></path><path d="M463 817c2 1 3 4 5 5l-1-4c1 0 1 0 1 1 2 4 5 8 6 13l-4-3h-1c-1-1-1-3-2-4l-4-8z" class="k"></path><defs><linearGradient id="w" x1="539.647" y1="871.384" x2="530.238" y2="869.088" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#6b6b6c"></stop></linearGradient></defs><path fill="url(#w)" d="M546 850l1-1c0 1 0 2-1 3-3 4-5 9-7 13l-5 11c-1 3-2 5-3 8-2 4-4 7-7 11h0l-3 3c0-3 3-5 4-8h-1c3-5 6-9 8-14s4-10 6-14c1-4 3-8 6-11l2-1z"></path><path d="M499 892c0-2 0-4-1-6-1-1-1-2-2-3 1-1 3 0 4 0 3 6 7 11 13 14 2 0 4 0 6-2 2-1 3-3 5-5h1c-1 3-4 5-4 8-2 2-3 2-4 3l-3 3c-2 2-2 2-4 2h-1c-2-6-6-10-10-14z" class="Q"></path><path d="M514 904c-2 0-2-1-3-2s-1-2-2-2l-4-4c2 1 3 1 5 2 1 0 2 1 2 2h-1c2 1 4 1 6 1l-3 3z" class="V"></path><path d="M524 890h1c-1 3-4 5-4 8-2 2-3 2-4 3-2 0-4 0-6-1h1l2-2h0-1v-1c2 0 4 0 6-2 2-1 3-3 5-5z" class="N"></path><path d="M470 852h2c1 3 4 10 7 12l1 1c3 1 5 6 6 9 4 8 8 18 13 25v1c2 2 5 2 5 5v1c-1 0-1 0-1-1 0 1 1 3 1 4-1 0-1 0-1-1-1-1-2-1-2-2h-2 0c-3-2-4-5-6-9-3-9-8-19-12-27-2-3-5-4-6-6l-5-12z" class="P"></path><defs><linearGradient id="x" x1="533.908" y1="878.529" x2="517.745" y2="875.995" xlink:href="#B"><stop offset="0" stop-color="#292829"></stop><stop offset="1" stop-color="#484848"></stop></linearGradient></defs><path fill="url(#x)" d="M517 884l-2 9h-1c2-1 4-3 5-5 2-2 2-5 4-7 2-3 5-6 6-9 2-4 5-10 9-13 0-1 1-1 1-2s1-2 1-3l2-2c1-1 2-2 4-2l-2 1c-3 3-5 7-6 11-2 4-4 9-6 14s-5 9-8 14c-2 2-3 4-5 5-2 0-5 0-7-1h0l-1-1c-2-1-3-3-3-5v-1c1 1 1 2 2 3h0v-5-9l-1-1c-2 0-3 1-5-1v-1h4l-1-1 1-1 2 2h1v-1c1-1 1-1 2-1h1l2-2h0c0 2-1 3-1 4l1 1v2c0 1 0 2-1 3 0 2-1 3-1 5l1 1h0l2-1h0z"></path><path d="M513 871h1l2-2h0c0 2-1 3-1 4l1 1v2c0 1 0 2-1 3 0 2-1 3-1 5l1 1h0 0c0 2 0 5-1 6-1 0-1 0-1-1h-1c-1-2 0-3 0-4-1-2-1-5-1-6 1-1 1-3 1-4 0-2 1-3 1-5z" class="L"></path><path d="M469 829h1l4 3c1 3 2 6 4 9l2 2c4 4 9 8 12 12 2 2 3 3 4 5h1 1v1 1h1v-1c1-2 0-2-1-4v-1-3l2 1 2-1c0 1 1 3 2 4l2 3c0 1 2 2 3 3h1 1c1 0 2 0 4-1h1 1v-2-1h1l1 1h1v1h1c0 1 0 2-1 3-1 4-2 6-3 9-1 1-1 0-1 1l-1-1c0-1 1-2 1-4h0l-2 2h-1c-1 0-1 0-2 1v1h-1l-2-2-1 1 1 1h-4v1c2 2 3 1 5 1l1 1v9 5h0c-1-1-1-2-2-3v1c-2-4-5-8-7-12-4-6-7-11-12-16-4-5-9-9-12-14s-5-12-8-17z" class="P"></path><path d="M505 879c-5-6-9-14-14-20-4-5-9-9-13-14h0c7 7 13 13 19 20 2 4 4 8 6 11h1l-4-8c2-2 1-2 2-4h0l1 1v1c1 2 2 3 2 5h2v-2c1 1 1 1 1 2l-1 1 1 1h-4v1c2 2 3 1 5 1l1 1v9 5h0c-1-1-1-2-2-3l-3-8z" class="E"></path><path d="M509 875l1 1v9 5h0c-1-1-1-2-2-3l-3-8c0-1-1-2 0-3h3v6c0 1 0 1 1 2v-1-5-1h1c0-1-1-1-1-2z" class="I"></path><path d="M498 856v-3l2 1 2-1c0 1 1 3 2 4l2 3c0 1 2 2 3 3h1 1c1 0 2 0 4-1h1 1v-2-1h1l1 1h1v1h1c0 1 0 2-1 3-1 4-2 6-3 9-1 1-1 0-1 1l-1-1c0-1 1-2 1-4h0l-2 2h-1c-1 0-1 0-2 1v1h-1l-2-2c0-1 0-1-1-2v2h-2c0-2-1-3-2-5v-1l-1-1h0c-1 2 0 2-2 4-1-2-2-5-4-8h1 1v1 1h1v-1c1-2 0-2-1-4v-1z" class="W"></path><path d="M517 860v-1h1l1 1h1v1h1c0 1 0 2-1 3l-2 1c0-1 0-1-1-2-1 0-1 0-2 1h-1-1c-1 0-3 0-4-1h1 1c1 0 2 0 4-1h1 1v-2z" class="C"></path><path d="M518 865l2-1c-1 4-2 6-3 9-1 1-1 0-1 1l-1-1c0-1 1-2 1-4h0l-2 2h-1c-1 0-1 0-2 1h-1l-1-1 1-1h1l1-1c3-1 4-1 6-4z" class="O"></path><path d="M498 856h3l1 1c-1 1-1 1-2 1 0 1 1 1 1 2 2 2 2 3 2 6v-1l-1-1h0c-1 2 0 2-2 4-1-2-2-5-4-8h1 1v1 1h1v-1c1-2 0-2-1-4v-1z" class="C"></path><path d="M570 803h2s1-1 2-1v1l-6 12-1-1c-2 2-5 4-6 6-5 7-7 15-12 22-3 4-8 7-12 11-6 6-9 14-13 21-3 3-5 6-7 10h0l-2 1h0l-1-1c0-2 1-3 1-5 1-1 1-2 1-3l6-11c1-2 3-4 4-6 0-4 2-6 5-9v-2c1-2 2-2 3-4 1-1 1-1 1-2h0c2-2 5-5 8-6 1 0 2-1 3-2h0c2-3 3-5 4-7 2-4 3-8 5-11s4-4 6-5h1c2-1 4-1 6-1l2-7z" class="M"></path><defs><linearGradient id="y" x1="555.779" y1="808.505" x2="563.038" y2="831.419" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#3b3e3f"></stop></linearGradient></defs><path fill="url(#y)" d="M570 803h2s1-1 2-1v1l-6 12-1-1c-2 0-7 2-8 4-2 2-4 5-5 8-3 7-6 14-13 19v-1c1-2 4-4 5-6 1-1 0-1 0-2v-2h0c2-3 3-5 4-7 2-4 3-8 5-11s4-4 6-5h1c2-1 4-1 6-1l2-7z"></path><path d="M546 834v2c0 1 1 1 0 2-1 2-4 4-5 6v1c-3 2-6 4-8 7-4 6-8 12-11 18-2 5-4 9-5 14l-2 1h0l-1-1c0-2 1-3 1-5 1-1 1-2 1-3l6-11c1-2 3-4 4-6 0-4 2-6 5-9v-2c1-2 2-2 3-4 1-1 1-1 1-2h0c2-2 5-5 8-6 1 0 2-1 3-2z" class="O"></path><path d="M546 834v2c-3 4-7 7-11 10l-4 4v-2c1-2 2-2 3-4 1-1 1-1 1-2h0c2-2 5-5 8-6 1 0 2-1 3-2z" class="C"></path><path d="M624 162l-1-3c-3-6-8-8-14-10 3-3 7-5 9-8h1l2 1c4 1 8 1 12 1l16-1h56 93 27c4 0 9-1 13-1l-2 2h-1l1 1c2 2 4 4 4 6s0 2-1 3l-1 2h-1l-1 1c1 1 1 1 2 0 0 1-1 1-1 2-3 1-10 0-13 0l-16 1-4-1h9c-2 0-6 0-8-1l-2-1c-6-1-13 2-19 1l-1-1h-1v1c-2 0-2-1-3-2-3 2-8 1-11 1l-5 1-1 2h-5l-1 1h-50v1c1 1 5 0 5 1h0-56c-6 0-14-1-19 1-1 1-1 2-2 3-1 3-3 6-6 7v1l3 4c2 3 4 6 5 10 2 5 3 11 6 16 1-4 2-6 4-8l2 1-2 2c-1 1-3 4-2 5 0 1 1 1 1 2h-1c-2-1-4-2-5-4-2-2-3-6-3-9-2-7-6-13-10-19h-4l-1-1c1-2 2-2 4-3 0-3-1-6-1-8z" class="d"></path><path d="M624 162c0-5-3-9-6-12l-2-2h-1 0c1-1 3-2 4-1 4 0 7 3 9 6s2 7 1 10c0 3-3 5-4 7 0-3-1-6-1-8z" class="W"></path><defs><linearGradient id="z" x1="778.006" y1="145.819" x2="777.969" y2="155.991" xlink:href="#B"><stop offset="0" stop-color="#1c1b1c"></stop><stop offset="1" stop-color="#38393a"></stop></linearGradient></defs><path fill="url(#z)" d="M721 145h75 23c4 0 8 0 12 1 1 0 2 0 3 1 1 2 1 3 1 5l1 1 1-2c1 1 1 2 2 2l-1 2h-1l-1 1c1 1 1 1 2 0 0 1-1 1-1 2-3 1-10 0-13 0l-16 1-4-1h9c-2 0-6 0-8-1l-2-1c-6-1-13 2-19 1l-1-1h-1v1c-2 0-2-1-3-2-3 2-8 1-11 1l-5 1-1-1 1-1c1-1 1-2 2-4-1-1 0-1 0-2h3c-2 0-4-1-6 0h-1-3v1h-1-1l-1-1c-1-1-3-1-5-1l-9 1h-1l1 1s0 1 1 1v1 1c-1 1-2 2-3 2h0v-1c0-1 1-1 1-2v-2l-1-1h-4l-1 1c-4-2-7-1-11-1l-1-1-1-2v-1z"></path><path d="M811 152l2 3v-1h0c-1-2-1-2 0-3 0-1 1-2 2-2 2 1 2 1 3 3h0v2c-2 0-3 1-4 3h-9l-2-1 6-1 1-1 1-2z" class="B"></path><path d="M768 149s2 0 2 1c1 1 1 2 1 4v1h1c0-2 0-4 1-6 1 0 3 0 4 1s1 4 2 5c-3 2-8 1-11 1l-5 1-1-1 1-1c1-1 1-2 2-4-1-1 0-1 0-2h3z" class="Z"></path><path d="M768 149s2 0 2 1c1 1 1 2 1 4l-1-3h-5c-1-1 0-1 0-2h3z" class="U"></path><path d="M821 153v-3c2-1 5 0 7 0l2 2 1-1h1 2l1 1 1 1 1-2c1 1 1 2 2 2l-1 2h-1l-1 1c1 1 1 1 2 0 0 1-1 1-1 2-3 1-10 0-13 0l-16 1-4-1h9c-2 0-6 0-8-1h9c1-2 2-3 4-3l1 1 2-2z" class="J"></path><path d="M818 154l1 1-1 2h-4c1-2 2-3 4-3z" class="C"></path><path d="M821 153v-3c2-1 5 0 7 0l2 2c0 1-1 3-1 4h3c-2 1-4 1-6 1l1-2c0-1 0-1-1-1h-1c-1-1-1-1-3-2l-1 1z" class="L"></path><path d="M788 149c2-1 4 0 6 0h3c2 0 5-1 7 0l2 1 4 1c1 1 1 0 1 1l-1 2-1 1-6 1c-6-1-13 2-19 1l-1-1h-1c-1-1-1-3-1-4s1-2 2-3 3 0 5 0z" class="Q"></path><path d="M783 154v-1h1l1 1 2-1 1 1-2 2c-2 0-2 0-3-2z" class="C"></path><path d="M788 149c2-1 4 0 6 0h3c2 0 5-1 7 0l2 1h-4c-2 0-7-1-10 1h-2v1l-2 2-1-1-2 1-1-1h-1v1l-1-1c1-1 1-2 2-3 2 0 2 0 4 1v-2z" class="B"></path><path d="M790 152v-1h2c3-2 8-1 10-1h4l4 1c1 1 1 0 1 1l-1 2-1-2c-2 0-3 0-5 1v2c-1-1-1-2-2-3h-2 0l-2-1c-2 0-1 1-2 2-1 0-2 0-2-1l-1 1v1h0l-2-2h-1z" class="K"></path><path d="M630 149l-3-3 94-1v1l1 2 1 1c4 0 7-1 11 1l1-1h4l1 1v2c0 1-1 1-1 2v1h0c1 0 2-1 3-2v-1-1c-1 0-1-1-1-1l-1-1h1l9-1c2 0 4 0 5 1l1 1h1 1v-1h3 1c2-1 4 0 6 0h-3c0 1-1 1 0 2-1 2-1 3-2 4l-1 1 1 1-1 2h-5l-1 1h-50c-5-1-12 0-18 0l-54 1c1-6-1-8-4-12z" class="E"></path><path d="M686 151c-1 0-1-1-2-1v-1h3 5c2 1 4 0 5 1-3 0-3 0-5 2 0 2 0 3 2 6l-2-1h0c0-2-2-4-3-5s-2-1-3-1z" class="G"></path><path d="M686 151c1 0 2 0 3 1s3 3 3 5h0 0c-1 0-1-1-2-1l-1 1c-3 0-5 0-8 1h-1v-1h2c2-1 3-4 4-6z" class="Z"></path><path d="M697 150l1 1c1 1 1 4 2 5 0 1 1 1 2 2-3 0-6-1-8 0-2-3-2-4-2-6 2-2 2-2 5-2z" class="Q"></path><path d="M762 156l1 1-1 2h-5l-39-1c3-2 24-1 30-1h9c2 0 3 0 5-1z" class="K"></path><path d="M721 146l1 2 1 1c4 0 7-1 11 1l1-1h4l1 1v2c0 1-1 1-1 2v1h0c1 0 2-1 3-2v-1-1c-1 0-1-1-1-1l-1-1h1l9-1c2 0 4 0 5 1l1 1h1 1v-1h3 1c2-1 4 0 6 0h-3c0 1-1 1 0 2-1 2-1 3-2 4l-1 1c-2 1-3 1-5 1h-9c-6 0-27-1-30 1h-4v-1h-8c-2 0-2-1-4-2 0-2 0-3 1-4h1c0 1-1 2-1 2 1 1 1 2 2 3h2c1-1 2-2 2-3h0l-2 1-1-1 1-1h1v-1c-1-1 0-1-1-1v-1h3 0 3 3 5v-3z" class="B"></path><path d="M735 149h4l1 1v2c-1 0-2 0-2 1l-1 1-2-1v-4z" class="L"></path><path d="M748 152h0c1-2 1-2 3-2 1 0 3 0 4 1l1 4c-1 1-1 1-3 1h-4l-1-1v-3z" class="D"></path><path d="M748 152c2 0 2 0 4 1 1 1 1 2 1 3h-4l-1-1v-3z" class="a"></path><path d="M721 146l1 2v6l1 1c-1 1-4 1-5 1h-3c-1-1-2-2-2-3 1-2 1-2 3-3 1 0 2 1 2 2 1 1 1 2 3 2 0-1 1-3 0-4v-1-3z" class="V"></path><path d="M750 148c2 0 4 0 5 1l1 1h1 1v-1h3 1c2-1 4 0 6 0h-3c0 1-1 1 0 2-1 2-1 3-2 4l-1 1c-2 1-3 1-5 1h-9l-1-1h2 4c2 0 2 0 3-1l-1-4v-1c-1-1-3-2-5-2z" class="E"></path><defs><linearGradient id="AA" x1="674.744" y1="145.608" x2="674.834" y2="155.679" xlink:href="#B"><stop offset="0" stop-color="#181718"></stop><stop offset="1" stop-color="#3e3f40"></stop></linearGradient></defs><path fill="url(#AA)" d="M630 149l-3-3 94-1v1 3h-5-3-3 0-3-2c-3-1-5 0-8 0-3-1-7 0-10 0h-3v1c1 0 1 1 2 1-1 2-2 5-4 6h-2v1c-2-1-3-1-5-1h-8l-2-1-1 1c-3 0-7 1-10 0h-8-8l-1-2v-4h1v-1c1-1 2-1 4-1l5-1c-2 0-5 0-7 1h-10z"></path><path d="M638 151c1 1 2 1 2 3-1 1-1 1-3 1v-4h1z" class="J"></path><path d="M669 150h6 0v1 2c-3 0-4-1-6-3z" class="K"></path><path d="M659 152h-2v-3h10l2 2v1h-2v2 2c0-1-1-1-1-2s0-1-1-3c-2 0-4 0-6 1z" class="G"></path><path d="M667 149l2 1c2 2 3 3 6 3 1 2 1 3 3 4h2v1c-2-1-3-1-5-1h-8v-1-2-2h2v-1l-2-2z" class="Q"></path><path d="M667 154s1 1 1 2c2 0 4-1 5 0 1 0 2 1 2 1h-8v-1-2z" class="R"></path><path d="M659 152c2-1 4-1 6-1 1 2 1 2 1 3s1 1 1 2v1l-2-1-1 1c-3 0-7 1-10 0h-8l-2-1 1-1h0c4 1 7 1 11 1 2-1 2-2 3-4z" class="Q"></path><path d="M767 224l3-3c0 2 0 2 2 3h2l2-2 1 1-1 2h0 1c1-1 1-2 3-3v1c2 0 3 0 5 1h0c1 1 2 2 3 4 0 1-1 2-2 3l1 1 3-3-1-1 1-1 2 2h0v-1-1c0-1 0-1 1-2h0v-1h-1c1-1 1-1 2-1h0c2 1 2 3 2 5l-1 1v2c2 0 3-1 4-2l1 1h3c2-1 4 0 6 1v1l2 1-2 4-2 1h-1c0 2-1 3-2 4l-1 1s0 1-1 1v3h0c0 3-1 4-2 6-1 1-2 2-2 3v3c-1 1-1 2-2 3-1 2-2 3-5 3-1 0-2-1-3-2l-2-1 1 2v2 1h0c-1 0-1 1-1 2s-1 2-2 3h0-2-1l1 1 2-1c1 2 1 2 1 4v1c-2 6-5 9-9 13l-1 1c-2-1-3-1-5-1h0c0-1 0-2-1-3-2 4-3 7-7 9l-1 1c-1-1-2-2-3-2l-1 1v-2h2c-1-1-1-1-2-1-3 1-10 1-14-1h0-2-1s-1-2-1-3h-2c-7-2-12-6-17-10l-18-15h1l1-1h0v-1l-4-2 1-1c-1-1-3-2-4-3l-1-2v-1c1 0 2 0 3-1h0c2 3 4 4 7 7h4 0 0l2 1s1-1 2-1l1-1c3 1 5-2 8-3v-1l13-7 10-5c1 0 2-1 3-1 0-1 0-1 1-1 2-2 4-3 6-5s3-5 4-7 3-4 4-5c2 0 2 0 3-1l1 1-1 1z" class="T"></path><path d="M780 223c2 0 3 0 5 1h0c1 1 2 2 3 4 0 1-1 2-2 3-2 2-3 3-5 3-2-1-3-2-5-4 0-2 0-2 1-4l3-3z" class="J"></path><path d="M781 229h4c-1 1-2 2-3 2-2 0-3 0-4-1v-2c1 0 2 1 3 1z" class="G"></path><path d="M780 223c2 0 3 0 5 1h0v2 1c0 1 0 1-1 1h-1-1v-1c-1 1-1 1-1 2-1 0-2-1-3-1l1-1-2-1 3-3z" class="R"></path><path d="M787 232l3-3-1-1 1-1 2 2h0v-1-1c0-1 0-1 1-2h0v-1h-1c1-1 1-1 2-1h0c2 1 2 3 2 5l-1 1v2c2 0 3-1 4-2l1 1c-2 1-3 2-5 3l-2 1c0 1 0 1-1 2l-1 1c-1 1-1 2-2 3h-3v1c2 0 2 0 4-1v1c0 2-1 4-3 5-1 1-1 2-1 2l-2 1v1h-3c-1 0-2 1-3 2h0l2-3v-1l-2 1v-3h-1c0 2-2 4-3 5l-1-1c2-1 3-2 3-4v-1l-2 2c-1 1-2 2-5 2-1 0-3-2-4-3v-1c1-1 2-2 3-2 1 1 1 2 2 2l1 1v1h0c-2 0-2 0-3-1h0v-1h-2v1c2 2 3 2 6 2 1-1 2-2 3-4l-3-3 1-1c1 2 2 3 4 4v-1l1 1 1-1c1 0 1 0 2-1 1-2 1-2 1-4h-1c0 1 0 2-1 3-1 0-1 0-2 1h-1c-1-1-2-1-3-2l1-1c0-1 0 0 1-1h1 0l-1-2h0c1 0 2 1 3 2v-1c-1-2-3-3-4-4-1-2-1-3-1-4l1-1c0 3 0 3 2 5 1 2 3 2 5 2s3-1 5-3h0z" class="N"></path><path d="M800 230h3c2-1 4 0 6 1v1l2 1-2 4-2 1h-1c0 2-1 3-2 4l-1 1s0 1-1 1v3h0c0 3-1 4-2 6-1 1-2 2-2 3v3c-1 1-1 2-2 3-1 2-2 3-5 3-1 0-2-1-3-2l-2-1 1 2v2 1h0c-1 0-1 1-1 2s-1 2-2 3h0-2-1v2l-1-1c-1-2-1-3-1-6l-2-2c-1 0-2 0-3-1v-2-1c-1-1-2-3-2-4-1-2-2-3-3-4 0-1 0-1-1-2h1c1-1 1-1 2-1v1h-1v1h4l2-1c1 0 2-1 2-2l2-1v1l-2 3h0c1-1 2-2 3-2h3v-1l2-1s0-1 1-2c2-1 3-3 3-5v-1c-2 1-2 1-4 1v-1h3c1-1 1-2 2-3l1-1c1-1 1-1 1-2l2-1c2-1 3-2 5-3z" class="c"></path><path d="M772 257h0l6 8h-1c-1 0-2 0-3-1v-2-1c-1-1-2-3-2-4z" class="K"></path><path d="M787 256c1-2 3-4 5-5l1 1h2c0-1 0-1 1-1l1-1h0v-1c-1 1-1 0-2 1 0-2 2-2 3-4h2v-1h-2l-1-1v-1h1c2 1 3 1 4 1h0v3h0c0 3-1 4-2 6-1 1-2 2-2 3l-2-1-2-1c-4 1-5 1-7 4v1h-2v-1l2-2h0z" class="V"></path><path d="M794 254h1c1 0 1-1 2-1 1-3 2-4 5-6 0 3-1 4-2 6-1 1-2 2-2 3l-2-1-2-1z" class="U"></path><path d="M787 256h0l-2 2v1h2s0 2-1 3l1 2v2 1h0c-1 0-1 1-1 2s-1 2-2 3h0-2-1v2l-1-1c-1-2-1-3-1-6l-2-2h1l2-1 1-1s1-1 1-2c1 0 1-1 2-2s1-2 3-3z" class="K"></path><path d="M778 265h3s0-1 1 0v1 1h-3l-2-2h1zm4 7v-1c0-1-1-1-1-2l1-1 2 1h2c0 1-1 2-2 3h0-2z" class="J"></path><path d="M787 259v-1c2-3 3-3 7-4l2 1 2 1v3c-1 1-1 2-2 3-1 2-2 3-5 3-1 0-2-1-3-2l-2-1c1-1 1-3 1-3z" class="O"></path><path d="M796 255l2 1v3l-2-1-1 1c-1-1-1-2-1-3l2-1z" class="C"></path><path d="M795 259l1-1 2 1c-1 1-1 2-2 3-1 2-2 3-5 3-1 0-2-1-3-2h1c1-1 1-1 2-1l1 1c1-1 2-2 3-4z" class="I"></path><path d="M800 230h3c2-1 4 0 6 1v1l2 1-2 4-2 1h-1c0 2-1 3-2 4l-1 1h-3 0c-2-1-3-2-4-4l-1-1v-4-1c2-1 3-2 5-3z" class="D"></path><path d="M796 239v-1c1 0 1-1 1-2h1v1c0 1 2 2 3 3 0 1-1 1-1 2v1h0c-2-1-3-2-4-4z" class="O"></path><path d="M809 231v1l2 1-2 4-2 1h-1c0 2-1 3-2 4l-1 1h-3v-1c0-1 1-1 1-2l3-2c2-2 0-3 0-6h2c1 1 1 1 2 0l1-1z" class="F"></path><path d="M764 223c2 0 2 0 3-1l1 1-1 1-1 1c0 1-2 2-2 3l-2 1h0c0 1-1 0 0 1v2h-1v1c1 2 2 3 4 4 2 0 4 1 6 0 1-1 1-2 2-3v2c-1 1-2 2-4 2h0c-2-1-4 0-5 0 1 1 1 1 3 1l-1 1c-1 0-2-1-3-1-2 1-1 1-3 0-1-1-4-1-5-1 0 2 0 2-1 3l-1-1h-1l1 2-1 1-1-1h-1v1c1 2 1 3 2 4h1 2c1 0 2 1 3 1 1 2 3 2 5 2 1 0 1 0 2 1h0c-1 1-2 1-3 1l-1 1v1h1v3c-1 0-2-1-3-1h-1l-2-2-1 1v1 1l-1 1c-1 1-3 1-5 1h-3c-1 0-2 2-3 2h0c-1 0-1 3-1 4s2 2 3 2l1 1h-5v2 1 1h1c1 1 2 2 2 4h-1c-3 0-7-1-10-3l-1-1v-1c1-1 1-1 1-2v-1l-2 1c-1 0-1 0-2 1 0-2 0-2-1-3h-2c-1 0-2 0-3 1h0c-2 0-2-1-3-2h-1c0-1-1-2-2-3v-2h-1-4v1l-2-1v-2l2 1s1-1 2-1l1-1c3 1 5-2 8-3v-1l13-7 10-5c1 0 2-1 3-1 0-1 0-1 1-1 2-2 4-3 6-5s3-5 4-7 3-4 4-5z" class="i"></path><path d="M732 272l2-2 1-2v2h0 2 0v2h1l2-2h1v1 1h1c1 1 2 2 2 4h-1c-3 0-7-1-10-3l-1-1z" class="G"></path><path d="M720 266v-1h0c0-2 0-3 1-4 1 0 2-1 3-2 1 1 2 1 3 2 1 2 1 4 1 6h-2c-1 0-2 0-3 1h0c-2 0-2-1-3-2z" class="K"></path><path d="M752 252c2 1 3 1 4 2h0l-1 1v1 1l-1 1c-1 1-3 1-5 1h-3c-1 0-2 2-3 2h0c-1 0-1 3-1 4s2 2 3 2l1 1h-5v2h-1c0-2-1-3-2-4-2-1-2 0-3 0v-1h1 1c0-1-1-2-1-3-1-1-1-3-1-4s1-2 1-2c-1-1-2-1-1-2h0c1 0 2 1 3 1v2h3c1 1 1 1 2 1v-2h1c2 0 6 1 8-1v-2-1z" class="V"></path><path d="M738 266h0l2 1 1-1c-1-1-2-2-2-3l1-1-1-1h-1v-1h1c2 0 2-1 3-2h2c1-1 2 0 3-1h8l-1 1c-1 1-3 1-5 1h-3c-1 0-2 2-3 2h0c-1 0-1 3-1 4s2 2 3 2l1 1h-5v2h-1c0-2-1-3-2-4zm26-43c2 0 2 0 3-1l1 1-1 1-1 1c0 1-2 2-2 3l-2 1h0c0 1-1 0 0 1v2h-1v1c1 2 2 3 4 4 2 0 4 1 6 0 1-1 1-2 2-3v2c-1 1-2 2-4 2h0c-2-1-4 0-5 0 1 1 1 1 3 1l-1 1c-1 0-2-1-3-1-2 1-1 1-3 0-1-1-4-1-5-1 0 2 0 2-1 3l-1-1h-1l1 2-1 1-1-1h-1v1c1 2 1 3 2 4h1 2c1 0 2 1 3 1 1 2 3 2 5 2 1 0 1 0 2 1h0c-1 1-2 1-3 1l-1 1v1h1v3c-1 0-2-1-3-1h-1l-2-2h0c-1-1-2-1-4-2v1 2c-2 2-6 1-8 1h-1c-1 0-3-2-3-3h1l1-1-1-2h1c1-1 2-3 3-4v-1c-1 0-1 0-1 1-1 1-2 3-4 4h0-1c-1 0-2 1-2 1v-1c1-1 3-1 4-3h0c-3 1-5 2-7 2v1l1 1v1c-3 0-3-2-6 0l1 1c-1 1-3 3-3 4s1 1 0 2l-3-2v2c-1 1-2 2-3 2-1 1-1 2-1 4h0v1h-1c0-1-1-2-2-3v-2h-1-4v1l-2-1v-2l2 1s1-1 2-1l1-1c3 1 5-2 8-3v-1l13-7 10-5c1 0 2-1 3-1 0-1 0-1 1-1 2-2 4-3 6-5s3-5 4-7 3-4 4-5z" class="R"></path><path d="M717 261c1-2 4-3 6-5v1h1c0-1 0 0 1-1l-1 1v2c-1 1-2 2-3 2-1 1-1 2-1 4h0v1h-1c0-1-1-2-2-3v-2z" class="B"></path><path d="M752 247h1 2c1 0 2 1 3 1 1 2 3 2 5 2 1 0 1 0 2 1h0c-1 1-2 1-3 1l-1 1v1h1v3c-1 0-2-1-3-1h-1l-2-2h0c-1-1-2-1-4-2v1 2c-2 2-6 1-8 1v-3-1l1 1 1-1 1 1c1-1 2-1 2-1l-1-1h-2l-1-1 1-1h3c3 0 1 0 3-2z" class="J"></path><path d="M752 252c1-1 2-3 3-4l4 3 1 1-1 1h0l-1-1c-1 0-2 1-2 2-1-1-2-1-4-2z" class="k"></path><path d="M763 250c1 0 1 0 2 1h0c-1 1-2 1-3 1l-1 1v1h1v3c-1 0-2-1-3-1h-1l-2-2h0c0-1 1-2 2-2l1 1h0l1-1c1-1 2-1 3-2z" class="i"></path><path d="M762 254h-1v-1l1-1c1 0 2 0 3-1h0l4 2c1 1 2 2 3 4 0 1 1 3 2 4v1 2c1 1 2 1 3 1l2 2c0 3 0 4 1 6l1 1v-2l1 1 2-1c1 2 1 2 1 4v1c-2 6-5 9-9 13l-1 1c-2-1-3-1-5-1h0c0-1 0-2-1-3l-2-2c0-1 0-2 1-3l-1-1c-3-1-8-1-11-1 0 0 1-1 2-1h3c0-1-1-2-1-3h2 1c-1-1-2-1-2-1l-4-1c-1 0-1 0-2 1h-1c-1 1-2 1-3 1h-1-1l1 1-7-1h1c0-2-1-3-2-4h-1v-1-1-2h5l-1-1c-1 0-3-1-3-2s0-4 1-4h0c1 0 2-2 3-2h3c2 0 4 0 5-1l1-1v-1-1l1-1 2 2h1c1 0 2 1 3 1v-3z" class="Z"></path><path d="M753 264c1-2 2-2 4-2h5c-1 1-1 2-2 2h-1-6z" class="e"></path><path d="M764 266v-1c2 1 3 3 5 4l-4 3h-4v-1h1c2-1 3-1 3-3l-1-2z" class="L"></path><path d="M760 266c1 0 2-1 4 0l1 2c0 2-1 2-3 3h-1v1h0-1c-2-1-2-2-3-3l1-1 1-2h1z" class="M"></path><path d="M759 266h1c0 2 2 3 2 5h-1v1h0-1c-2-1-2-2-3-3l1-1 1-2z" class="E"></path><path d="M746 268l-1-1c-1 0-3-1-3-2s0-4 1-4h0c2 2 3 3 5 3h3 2c-1 2-3 3-5 4-1-1-2-1-2 0z" class="O"></path><path d="M772 268h0 1l1 1c-1 1-1 3-2 3-1 1-1 1-2 1-1 2-5 3-7 3-1-1-2-1-2-1l-4-1c1-1 2-1 3-2h1 0 4l-1 2h0 1c2 0 4-1 5-2v-3l2-1h0z" class="K"></path><path d="M760 272h1l1 2h0l-1 1-4-1c1-1 2-1 3-2zm-7-8h6 1l-1 2-1 2-1-1h-1c-1 0-1 1-2 1-2 1-4 2-7 2-1 0-2 0-3 1h-2 0-1v-1-2h5c0-1 1-1 2 0 2-1 4-2 5-4h0z" class="i"></path><path d="M753 264h6 1l-1 2-1 2-1-1c-2-1-3-1-5 0-1 0-2 1-4 1h0c2-1 4-2 5-4h0z" class="c"></path><path d="M757 267l1 1-1 1c1 1 1 2 3 3-1 1-2 1-3 2-1 0-1 0-2 1h-1c-1 1-2 1-3 1h-1-1l1 1-7-1h1c0-2-1-3-2-4h-1v-1h1 0 2c1-1 2-1 3-1 3 0 5-1 7-2 1 0 1-1 2-1h1z" class="Z"></path><path d="M746 272h2c0 2 1 2 0 3h-3c0-2 0-2 1-3z" class="V"></path><path d="M755 275h-4l-1-1v-1c1-2 4-3 6-5l1 1c1 1 1 2 3 3-1 1-2 1-3 2-1 0-1 0-2 1z" class="e"></path><path d="M762 254h-1v-1l1-1c1 0 2 0 3-1h0l4 2c1 1 2 2 3 4 0 1 1 3 2 4v1l-1 2h0c1 2 3 3 4 4l-1 1-1-1h-2-1 0-1s-1-1-1-2c-1-2-2-2-3-4l-3-3-2-2v-3z" class="Z"></path><path d="M762 254c2 0 3 1 4 2s2 1 2 1c2 2 4 4 5 6l-1 1-2-1c-2-2-3-2-3-5h-1l-2 1-2-2v-3z" class="Y"></path><path d="M764 259l2-1h1c0 3 1 3 3 5l2 1 1-1v1h0c1 2 3 3 4 4l-1 1-1-1h-2-1 0-1s-1-1-1-2c-1-2-2-2-3-4l-3-3z" class="e"></path><path d="M774 262v2c1 1 2 1 3 1l2 2c0 3 0 4 1 6l1 1v-2l1 1 2-1c1 2 1 2 1 4v1c-2 6-5 9-9 13l-1 1c-2-1-3-1-5-1h0c0-1 0-2-1-3l-2-2c0-1 0-2 1-3l-1-1c-3-1-8-1-11-1 0 0 1-1 2-1h3c0-1-1-2-1-3h2 1c2 0 6-1 7-3 1 0 1 0 2-1 1 0 1-2 2-3l-1-1h2l1 1 1-1c-1-1-3-2-4-4h0l1-2z" class="d"></path><path d="M763 276c2 0 6-1 7-3 1 0 1 0 2-1l-1 3c-1 1-1 0-2 2h1v1c-1 1-3 0-4 0h-3l-1-2h1z" class="g"></path><path d="M782 273l2-1c1 2 1 2 1 4v1c-2 6-5 9-9 13l-1 1c-2-1-3-1-5-1h0c0-1 0-2-1-3l-2-2c0-1 0-2 1-3 1 1 1 2 1 2 1 1 1 2 2 2v-2-1h0 0v-1l4 4c2 0 1 0 3-1v-1l2-2c1-1 2-3 3-5v-2l-1-1v-1z" class="Y"></path><path d="M768 282c1 1 1 2 1 2 1 1 1 2 2 2 1 2 3 3 5 4l-1 1c-2-1-3-1-5-1h0c0-1 0-2-1-3l-2-2c0-1 0-2 1-3z" class="K"></path><path d="M774 262v2c1 1 2 1 3 1l2 2c0 3 0 4 1 6l1 1v-2l1 1v1l1 1v2c-1 2-2 4-3 5l-2 2-2-1c-2-1-2-4-4-5-1-1-1-2-1-3l1-3c1 0 1-2 2-3l-1-1h2l1 1 1-1c-1-1-3-2-4-4h0l1-2z" class="a"></path><path d="M772 272c1 0 1-2 2-3 0 2 0 3 1 4 1 2 2 4 3 5 2 0 3-1 4-1h1c-1 2-2 4-3 5l-2 2-2-1c-2-1-2-4-4-5-1-1-1-2-1-3l1-3z" class="C"></path><path d="M772 272c1 0 1-2 2-3 0 2 0 3 1 4-1 2-2 2-2 4h0c2 1 2 2 3 3v3c-2-1-2-4-4-5-1-1-1-2-1-3l1-3z" class="O"></path><defs><linearGradient id="AB" x1="731.419" y1="270.874" x2="726.701" y2="279.709" xlink:href="#B"><stop offset="0" stop-color="#b0afb0"></stop><stop offset="1" stop-color="#d4d3d4"></stop></linearGradient></defs><path fill="url(#AB)" d="M699 252c2 3 4 4 7 7h4 0 0v2l2 1v-1h4 1v2c1 1 2 2 2 3h1c1 1 1 2 3 2h0c1-1 2-1 3-1h2c1 1 1 1 1 3 1-1 1-1 2-1l2-1v1c0 1 0 1-1 2v1l1 1c3 2 7 3 10 3l7 1-1-1h1 1c1 0 2 0 3-1h1c1-1 1-1 2-1l4 1s1 0 2 1h-1-2c0 1 1 2 1 3h-3c-1 0-2 1-2 1 3 0 8 0 11 1l1 1c-1 1-1 2-1 3l2 2c-2 4-3 7-7 9l-1 1c-1-1-2-2-3-2l-1 1v-2h2c-1-1-1-1-2-1-3 1-10 1-14-1h0-2-1s-1-2-1-3h-2c-7-2-12-6-17-10l-18-15h1l1-1h0v-1l-4-2 1-1c-1-1-3-2-4-3l-1-2v-1c1 0 2 0 3-1h0z"></path><path d="M746 279c3 1 7 2 10 1 3 0 8 0 11 1l1 1c-1 1-1 2-1 3l2 2c-2 4-3 7-7 9h0c1-2 3-3 3-5l1-2c1-2 0-4-1-6 0-1-4 0-5 0-8 1-15-1-23-3h5c2 0 3 0 4-1z" class="J"></path><path d="M720 279c2 0 3 0 5 1 2 2 3 2 6 2l17 4c3 1 9 1 13 0 1 0 3 0 4 1s1 1 1 2l-1 1v1c0 2-2 3-3 5h0l-1 1c-1-1-2-2-3-2l-1 1v-2h2c-1-1-1-1-2-1-3 1-10 1-14-1h0-2-1s-1-2-1-3h-2c-7-2-12-6-17-10z" class="k"></path><path d="M699 252c2 3 4 4 7 7h4 0 0v2l2 1v-1h4 1v2c1 1 2 2 2 3h1c1 1 1 2 3 2h0c1-1 2-1 3-1h2c1 1 1 1 1 3 1-1 1-1 2-1l2-1v1c0 1 0 1-1 2v1l1 1c3 2 7 3 10 3l7 1-1-1h1 1c1 0 2 0 3-1h1c1-1 1-1 2-1l4 1s1 0 2 1h-1-2c0 1 1 2 1 3h-3c-1 0-2 1-2 1-3 1-7 0-10-1-1 1-2 1-4 1h-5c-2-1-4-2-6-2-11-4-18-10-27-16l-4-2 1-1c-1-1-3-2-4-3l-1-2v-1c1 0 2 0 3-1h0z" class="h"></path><path d="M755 275c1-1 1-1 2-1l4 1s1 0 2 1h-1-2c-3 1-7 1-10 1l-1-1h1 1c1 0 2 0 3-1h1zm-49-16h4 0 0v2l2 1v-1h4 1v2c1 1 2 2 2 3h1c1 1 1 2 3 2h0c1-1 2-1 3-1h2c1 1 1 1 1 3 1-1 1-1 2-1l2-1v1c0 1 0 1-1 2v1l1 1c-10-3-20-8-27-14z" class="D"></path><path d="M712 261h4 1v2h-2c-1 0-2-1-3-2z" class="W"></path><path d="M701 259c3 2 7 5 10 7s7 3 10 5c8 4 16 7 25 8-1 1-2 1-4 1h-5c-2-1-4-2-6-2-11-4-18-10-27-16l-4-2 1-1z" class="V"></path><path d="M676 241c1 1 2 3 3 4 4 1 8 6 10 9l2-1c3 2 5 5 9 7l4 2v1h0l-1 1h-1l18 15c5 4 10 8 17 10h2c0 1 1 3 1 3l-1 1c-1-1-2-3-3-3h-5c-2 0-4-1-6-1l-10 1c-2-1-5-1-8 0-3 0-6 1-9 2-2 1-5 2-7 2-1 0-2 0-3 1h-1s0 1-1 1c-2 0-3 1-4 2l-2-1-12 27c-1 2-2 5-3 7l-45 101c0 1 1 2 1 2h0l-9-3-1 1c1 1 3 0 3 2v4c-1 0-1 1-1 2-2 5-2 10-2 15-1-1-1-1-2-1v1c1 2 1 4 1 6l-2 1 1 4h-2v-3l-1 1-8 17-6 15-1 5h0c-1 1-1 2-2 3v-4h-1l-2-4-3-3c-1 0-3-2-5-1-1 0-2 1-3 2v1h-3c1-2 2-4 3-5l1-1v-6l1-2c1 1 1 2 2 3 2-1 2-4 3-6h1c2-1 3-3 3-5l7-14 10-26 14-30c1-2 2-4 3-7 0-2 1-4 1-6l6-9 12-27 17-38c3-7 5-14 8-21 2-3 4-7 6-10l9-18c1-3 3-6 3-10l-1-1c-1-4-4-7-5-12v-1 1h0c-1-1-1-2 0-3z" class="I"></path><path d="M662 328h1l1-1h0c1-2 2-2 4-3-1 2-2 5-3 7h-4l2-2-1-1z" class="B"></path><path d="M576 483l1-2c1 1 1 2 2 3 1 3 4 5 6 7 3 3 3 5 4 9h-1l-2-4-3-3c-1 0-3-2-5-1-1 0-2 1-3 2v1h-3c1-2 2-4 3-5l1-1v-6z" class="h"></path><path d="M583 493l1-1c-1-1-3-1-5-2l1-1 4 2h1c3 3 3 5 4 9h-1l-2-4-3-3z" class="Y"></path><path d="M604 451l3-6c2-3 4-8 3-12-1 0-2-1-3-2l1-1h3l1 1-1 1c1 1 3 0 3 2v4c-1 0-1 1-1 2-2 5-2 10-2 15-1-1-1-1-2-1v1c1 2 1 4 1 6l-2 1v-9c0-2 1-2 0-3-1 0-2 1-4 1h0z" class="P"></path><path d="M604 451h0c2 0 3-1 4-1 1 1 0 1 0 3h-1c0 2-1 3-3 5h-1l1 1c-2 2-3 3-5 6l-2 3v1c-1 2-2 5-3 7l-1 4-1 1 1 1c1 0 1 0 2 1 0-1 1-1 2-1l1-1-6 15c-1-1-1-3-1-4l1-1c2-2 2-3 3-6l-1-1c-2 1-3 1-4 1 0 1-1 2-2 2h0c2-4 4-10 6-14 1-3 2-5 3-8 0-1 0-1 1-2h0l3-7 1-2c0-1 1-2 2-3z" class="L"></path><path d="M603 458h0l-1 1-1 1c0-1 0-2 1-2v-2c1-1 1-2 2-3h3c0 2-1 3-3 5h-1z" class="H"></path><path d="M641 365c2-10 8-19 13-28l15-35h2c0 1 0 1-1 2 0 1 0 1-1 2s-4 7-3 9c0-1 1-2 2-3-1 2-2 4-3 5l-1 1c0 4-3 8-5 11l1 1 2-2 1 1-2 2h-3c0 2-2 4-3 5-1 4-3 7-4 10h0c2-2 3-5 5-8h0c0 3-2 6-4 8l-9 17-2 2z" class="h"></path><path d="M608 453v9l1 4h-2v-3l-1 1-8 17-1 1c-1 0-2 0-2 1-1-1-1-1-2-1l-1-1 1-1 1-4c1-2 2-5 3-7v-1l2-3c2-3 3-4 5-6l-1-1h1c2-2 3-3 3-5h1z" class="F"></path><path d="M679 245c4 1 8 6 10 9l2-1c3 2 5 5 9 7l4 2v1h0l-1 1h-1-1c-1 2-2 4-2 7h-1 0c-2 1-3 3-4 4 1-2 3-5 2-7v-1h0l-1-1h0c-1-1-1-1-2-1l-1-1-1 1v-3h-1v-1h-1c0 1 0 1-1 2l-1 2c0 1-1 2-1 3l-9 18c1 0 1 1 2 2h-1c-2 0-3 1-3 2v1c-1 1-2 1-2 1-1 1-1 1-1 2-1 1-1 1-1 2 0-2 0-5 1-7 1-1 1-2 2-3 0-4 2-7 3-10v-2c2-5 7-14 6-18v-2l1-1c0 2 1 3 3 3h1v-1l-3-3c-3-2-5-4-6-7z" class="j"></path><path d="M679 245c4 1 8 6 10 9l2-1c3 2 5 5 9 7l4 2v1h0l-1 1h-1-1c-1 2-2 4-2 7h-1 0c-2 1-3 3-4 4 1-2 3-5 2-7v-1h0c0-1 1-2 2-3h0c-1-2-3-2-4-3l-6-6-3-3c-3-2-5-4-6-7z" class="U"></path><path d="M691 253c3 2 5 5 9 7l4 2v1h0l-1 1h-1-1c-3-3-9-6-12-10l2-1z" class="h"></path><path d="M701 264h1l18 15c5 4 10 8 17 10h2c0 1 1 3 1 3l-1 1c-1-1-2-3-3-3h-5c-2 0-4-1-6-1l-10 1c-2-1-5-1-8 0-3 0-6 1-9 2-2 1-5 2-7 2-1 0-2 0-3 1h-1s0 1-1 1c-2 0-3 1-4 2l-2-1 16-30v1c1 2-1 5-2 7 1-1 2-3 4-4h0 1c0-3 1-5 2-7z" class="C"></path><path d="M698 281l2-1h1c1-1 6-1 8-1v1h-4-3l-1 1c-1 2-3 4-5 5h-2l3-5h1z" class="G"></path><path d="M703 273l1 1c2 1 2 1 3 3h-2c-1 1-1 1-2 1s-3 1-4 1l-1 2h-1v-1c2-3 3-5 6-7z" class="B"></path><path d="M704 274c2 1 2 1 3 3h-2-2c0-1 0-2 1-3z" class="I"></path><path d="M694 286h2c2-1 4-3 5-5l1-1h3v2h1 1 2 0c0 1-1 1-2 1-1 1-2 1-3 2h-1c1 1 2 2 3 2s5-1 6 1c-1 0-2 0-3 1h-1l-1 1c-3 0-6 1-9 2-2 1-5 2-7 2-1 0-2 0-3 1h-1c2-2 5-2 7-2 3-1 6-3 9-4-1 0-1 0-2 1h-4l-2-1h-2l1-3z" class="B"></path><path d="M695 289c2-1 3-2 5-2v1c2 0 3 1 5 0h0l1 1h-3c-1 0-1 0-2 1h-4l-2-1z" class="J"></path><path d="M696 267v1c1 2-1 5-2 7 1-1 2-3 4-4h0 1 0 1 0 1l2 2c-3 2-4 4-6 7v1l-3 5-1 3h2l2 1h4c1-1 1-1 2-1-3 1-6 3-9 4-2 0-5 0-7 2 0 0 0 1-1 1-2 0-3 1-4 2l-2-1 16-30z" class="f"></path><path d="M695 289l2 1c-2 1-5 1-7 1 1 0 2-1 3-2h2z" class="R"></path><path d="M698 271h1 0 1 0 1l2 2c-3 2-4 4-6 7v1l-3 5-1 3c-1 1-2 2-3 2-1 1-1 1-2 0 2-7 7-13 10-20z" class="H"></path><path d="M701 264h1l18 15c5 4 10 8 17 10h2c0 1 1 3 1 3l-1 1c-1-1-2-3-3-3h-5c-2 0-4-1-6-1-2-2-4-1-6-1l-1-1h2v-1h-7c-3-1-5 0-8-1 2-1 4-1 6-2 1 0 2-1 2-2-2-1-3-3-6-4-1-2-1-2-3-3l-1-1-2-2h-1 0-1 0c0-3 1-5 2-7z" class="W"></path><path d="M643 363l9-17c2-2 4-5 4-8h0c-2 3-3 6-5 8h0c1-3 3-6 4-10 1-1 3-3 3-5h3 4l-45 101c0 1 1 2 1 2h0l-9-3-1-1h1l3-7c4-14 12-28 18-41l8-17 2-2z" class="F"></path><path d="M615 423h1c2-4 6-9 7-14 1-1 1-2 3-3-1 3-2 6-3 8-2 5-6 13-10 15l-1 1 3-7z" class="M"></path><path d="M643 363c2 0 3-3 4-3 0 3-2 4-3 7-2 3-4 7-6 11l-8 18c-1 2-4 8-4 10-2 1-2 2-3 3-1 5-5 10-7 14h-1c4-14 12-28 18-41l8-17 2-2z" class="f"></path><path d="M844 147c2 1 3 2 4 5 1 2 1 5 1 8h-1c-1 4-4 7-7 10-1 0-2 1-2 2-1-1-1-1-2-1s-2 1-3 2l1 1c-3 2-7 3-9 5-6 2-13 5-18 8h-1c-4 1-8 4-12 6-2 1-4 3-6 4-3 2-7 4-10 6-3 3-5 7-7 10-3 4-5 7-8 10-1 1-3 3-4 5s-2 5-4 7-4 3-6 5c-1 0-1 0-1 1-1 0-2 1-3 1l-10 5-13 7v1c-3 1-5 4-8 3l-1 1c-1 0-2 1-2 1l-2-1h0l3-1 4-2c5-1 10-5 15-8l9-5 6-3c1-1 3-2 3-3l2-2c1-1 3-3 4-5l-1-1c-1 2-3 3-4 4 0 1-1 2-2 2 1-1 1-2 2-3 0-5-1-7-3-11h1l1-1c0 1 1 1 2 2l1-2-1-1-1 1c-1 0-2-2-3-2h-1c-1 0-2 0-2-1h0l1-1h0l-1-1c1-2 1-2 1-4h0c0-1 0-2 1-3l-1-1c-1-2-6-4-8-4s-3-1-4-1c-4-1-7-1-10-3v-1-4c1-3 1-5 0-8 0-1 0-1 1-2l1 3h0v-2h0l1-1c1 0 1-1 2-2v-1h0-4l-1-2 1-1h0v1h1c0-1 1-2 1-3h1v-1c0-1 0-2 1-2l2 1c0-1 0-1-1-1l-1-2 1-1 1-1c1-1 1-1 1-2h-1l-1 1v-2c-1 1-1 1-1 2h-1l-2-2v1l-1-1c-1-1-1 1-2 1-1-1-1-2-1-3 1 0 2-1 3 0h1v-1l6-1h-21c0-1-4 0-5-1v-1h50l1-1h5l1-2 5-1c3 0 8 1 11-1 1 1 1 2 3 2v-1h1l1 1c6 1 13-2 19-1l2 1c2 1 6 1 8 1h-9l4 1 16-1c3 0 10 1 13 0 0-1 1-1 1-2-1 1-1 1-2 0l1-1h1l1-2c1-1 1-1 1-3l1 2v-2h1v-2l2-1z" class="K"></path><path d="M776 166h1v-1h1l-1-1 1-1 2 2-1 1h1c1-1 2-1 3-2h1c0 2-1 2-3 3 2 0 2 0 3-1l1 1-3 3h-1c-1-1 0-1-1-1 0 2 2 3 3 4h1v1c0 1 1 1 1 2 1-1 1-3 2-5l1 3h1v-2l1 1v1h1v-2l-1-1c1-1 1-1 2-1h1 0 1v5l1 1 3-3v-1l-1 1h-1-1c0-2 0-2 1-3 0 1 0 1 1 2v-2h1l1 1c1 0 1-1 2-1h1 0l-1-2 2-1c1 1 2 1 3 1-1-1-1-1-1-2l2 1 1-1c0 1 0 0 1 1l2-1c1 0 2-1 4 0v-1l2 1 1-2 1 1c-1 2-1 3-2 4h1c1-1 1-1 4-1h0c-1 1-2 1-3 1l-2 4v1h1l1-1v1l-2 1-1-1c0-1 1-1 1-2s0 0-1-1v-2c-1 1-1 0-2 1l-1-1-1 1 3 2h1s-1 1-1 2c0 2 0 3 1 5h1 0l2-2v1c-1 1-1 2-2 2 0 0-1 0-1 1l-2 1c-2-2-2-3-2-4-1 0-1 1-2 1l2-2c0-3-1-5-3-7-4 0-9 2-11 5l-2 2c-1 1-3 3-3 5v1 1s1 1 1 2c2 1 2-1 3 2-1 1-1 2-2 2h-1c1-1 1-2 1-2v-1c-2 1-3 3-3 5h-1c-1-1-2-2-2-4 0-1 0-1-1-2 0-1 0-2-1-3-1-5-4-8-8-11-1-1-1-2-3-2h-5-2l-4-1h-6v-1c1 0 1 0 2-1h1l1 1c1 0 2-1 3-1s1-1 1-1l2 1h1 1l1 1 1-1-1-1 1-1 1 1c-1 1-1 0-1 2h1l2-2z" class="M"></path><path d="M771 162c11-1 21 0 32 0h20l11-1c2 0 4 1 6 0l-1 3h-1 0c-2-1-6 0-8 0l-1 1h0l2 2v1h0c0 1-1 1-2 2-1-2-1-2-1-4l-2-1v-1c-2 2 1 2 1 4-1-1-2-1-3-2v-2h-2l-1 1v1l-1-1h-1 0l-1-1-1 2-2-1v1c-2-1-3 0-4 0l-2 1c-1-1-1 0-1-1l-1 1-2-1c0 1 0 1 1 2-1 0-2 0-3-1l-2 1 1 2h0-1c-1 0-1 1-2 1l-1-1h-1v2c-1-1-1-1-1-2-1 1-1 1-1 3h1 1l1-1v1l-3 3-1-1v-5h-1 0-1c-1 0-1 0-2 1l1 1v2h-1v-1l-1-1v2h-1l-1-3c-1 2-1 4-2 5 0-1-1-1-1-2v-1h-1c-1-1-3-2-3-4 1 0 0 0 1 1h1l3-3-1-1c-1 1-1 1-3 1 2-1 3-1 3-3h-1c-1 1-2 1-3 2h-1l1-1-2-2-1 1 1 1h-1v1h-1l-1-1c-1-1-1-1-3-1h-1v-2h0z" class="D"></path><path d="M792 170v-1h-2 0c-1 0-2 0-3 1l-1-2h0l1-1s1 1 2 1c-1-1-1-2-2-2v-1h0c0-1 0-1 1-2h1l-1 2h1c1-1 1-2 2-2h8 3s1 0 1 1c-2 1-1 0-3 1h1l-1 1v1c1 0 1 0 1 1l1 2h0-1c-1 0-1 1-2 1l-1-1h-1v2c-1-1-1-1-1-2-1 1-1 1-1 3h1 1l1-1v1l-3 3-1-1v-5h-1 0-1z" class="R"></path><path d="M844 147c2 1 3 2 4 5 1 2 1 5 1 8h-1c-1 4-4 7-7 10-1 0-2 1-2 2-1-1-1-1-2-1s-2 1-3 2l-6 3c1-1 2-2 3-2l2-2-1-1-1 1c-1 0-1 0-2 1v-2l1 1c0-1 1-1 2-2 0 0 1 0 1-1h0l-2-1h0v-1l-2-2h0l1-1c2 0 6-1 8 0h0 1l1-3c-2 1-4 0-6 0l-11 1h-20c-11 0-21-1-32 0h-39-21c0-1-4 0-5-1v-1h50l1-1h5l1-2 5-1c3 0 8 1 11-1 1 1 1 2 3 2v-1h1l1 1c6 1 13-2 19-1l2 1c2 1 6 1 8 1h-9l4 1 16-1c3 0 10 1 13 0 0-1 1-1 1-2-1 1-1 1-2 0l1-1h1l1-2c1-1 1-1 1-3l1 2v-2h1v-2l2-1z" class="X"></path><path d="M779 155c1 1 1 2 3 2v-1h1l1 1c6 1 13-2 19-1l2 1c2 1 6 1 8 1h-9l4 1h1 13c-4 2-11-1-16 1-3 1-8 0-11 0h-39l1-1h5l1-2 5-1c3 0 8 1 11-1z" class="G"></path><path d="M844 147c2 1 3 2 4 5 1 2 1 5 1 8h-1c-1 4-4 7-7 10-1 0-2 1-2 2-1-1-1-1-2-1s-2 1-3 2l-6 3c1-1 2-2 3-2l2-2-1-1-1 1c-1 0-1 0-2 1v-2l1 1c0-1 1-1 2-2 0 0 1 0 1-1h0l-2-1h0v-1l-2-2h0l1-1c2 0 6-1 8 0h0 1l1-3h-1c-2-1-6-1-9-1h-24c5-2 12 1 16-1h-13-1l16-1c3 0 10 1 13 0 0-1 1-1 1-2-1 1-1 1-2 0l1-1h1l1-2c1-1 1-1 1-3l1 2v-2h1v-2l2-1z" class="C"></path><path d="M839 164h1c1 1 1 1 1 3h-2-1c-1 0-1-1-1-2h-1-1c-1 1-2 0-4 0v2l-2-2h0l1-1c2 0 6-1 8 0h0 1z" class="S"></path><path d="M844 147c2 1 3 2 4 5 1 2 1 5 1 8h-1 0c-1 1-2 1-2 2-1 0-1 0-2-1h-1l-1 1h0-1c0-1 1-1 1-2 1-1 1-2 2-3 0-1 0-2-1-4l-1-3v-2l2-1z" class="H"></path><path d="M732 162h39 0v2h1c2 0 2 0 3 1l1 1-2 2h-1c0-2 0-1 1-2l-1-1-1 1 1 1-1 1-1-1h-1-1l-2-1s0 1-1 1-2 1-3 1l-1-1h-1c-1 1-1 1-2 1v1h0c-1 1-1 2-3 2-1 1-1 2-2 3l-1 1h-1v-1h-1l-1 2c0-1 0-2-1-2l-2 4h-3c0 1-1 1-1 2v3h-1v2l-1 1 1 1s-2 1-2 2v2c0 1-2 2-3 3-1 0-1 1-2 1s-2 0-3 1c-1 0-1 0-2 1h0 0l-1 1c-3-3-3-8-3-11h0v-2h0l1-1c1 0 1-1 2-2v-1h0-4l-1-2 1-1h0v1h1c0-1 1-2 1-3h1v-1c0-1 0-2 1-2l2 1c0-1 0-1-1-1l-1-2 1-1 1-1c1-1 1-1 1-2h-1l-1 1v-2c-1 1-1 1-1 2h-1l-2-2v1l-1-1c-1-1-1 1-2 1-1-1-1-2-1-3 1 0 2-1 3 0h1v-1l6-1z" class="V"></path><path d="M726 187c1 0 1-1 2 0 1 0 1 2 2 2l1-1v1h-1c1 1 2 1 3 1 1-1 1-2 2-2v2l1 1h-1 0l-1-1c-2 0-2 2-4 3h-1v3l1 1h0l-1 1c-3-3-3-8-3-11h0z" class="J"></path><path d="M732 162h39 0v2h1c2 0 2 0 3 1l1 1-2 2h-1c0-2 0-1 1-2l-1-1-1 1 1 1-1 1-1-1h-1-1l-2-1s0 1-1 1-2 1-3 1l-1-1h-1c-1 1-1 1-2 1v1h0c-1 1-1 2-3 2-1 1-1 2-2 3l-1 1h-1v-1h-1l-1 2c0-1 0-2-1-2 0-1 2-2 2-3-1 0-2-1-2-2l-1-1c-1 0-2-2-3-3h-5 0c0 2 1 3 1 4l1 1c0 1-2 2-4 3 1-2 2-3 3-4l-3-3h0c-1 1-1 1 0 1h-1c0-2 0-2-1-3h-1c0 1-1 1-2 1l1 2-3-2h-3-1l-1 1v1l-1-1c-1-1-1 1-2 1-1-1-1-2-1-3 1 0 2-1 3 0h1v-1l6-1z" class="B"></path><path d="M745 165l4 1 1-1 1 1h0c1-1 1-1 2-1 2 0 4 0 6-1 0 1 0 2-1 3 1 0 1 0 1-1 1-1 1-1 1-2h1l1 2c2 0 3 0 4 1-1 0-2 1-3 1l-1-1h-1c-1 1-1 1-2 1v1h0c-1 1-1 2-3 2-1 1-1 2-2 3l-1 1h-1v-1h-1l-1 2c0-1 0-2-1-2 0-1 2-2 2-3-1 0-2-1-2-2l-1-1c-1 0-2-2-3-3z" class="e"></path><path d="M748 168h1 0c1 1 2 2 3 2h0 2l2-1h0 3c-1 1-1 2-3 2-1 1-1 2-2 3l-1 1h-1v-1h-1l-1 2c0-1 0-2-1-2 0-1 2-2 2-3-1 0-2-1-2-2l-1-1z" class="K"></path><path d="M759 169h6l4 1h2 5c2 0 2 1 3 2-1 0-2 1-3 0l4 4 2 2c2 1 3 3 3 5v1l1 1s1 1 1 2v1c1 2 1 4 1 6l1 1h1c2-1 4-3 6-4l14-7c1-1 2-1 3-1l2-1h1c2-1 5-3 8-4 1-1 3-1 4-2l6-3 1 1c-3 2-7 3-9 5-6 2-13 5-18 8h-1c-4 1-8 4-12 6-2 1-4 3-6 4-3 2-7 4-10 6-3 3-5 7-7 10-3 4-5 7-8 10-1 1-3 3-4 5s-2 5-4 7-4 3-6 5c-1 0-1 0-1 1-1 0-2 1-3 1l-10 5-13 7v1c-3 1-5 4-8 3l-1 1c-1 0-2 1-2 1l-2-1h0l3-1 4-2c5-1 10-5 15-8l9-5 6-3c1-1 3-2 3-3l2-2c1-1 3-3 4-5l-1-1c-1 2-3 3-4 4 0 1-1 2-2 2 1-1 1-2 2-3 0-5-1-7-3-11h1l1-1c0 1 1 1 2 2l1-2-1-1-1 1c-1 0-2-2-3-2h-1c-1 0-2 0-2-1h0l1-1h0l-1-1c1-2 1-2 1-4h0c0-1 0-2 1-3l-1-1c-1-2-6-4-8-4s-3-1-4-1c-4-1-7-1-10-3v-1-4c1-3 1-5 0-8 0-1 0-1 1-2l1 3c0 3 0 8 3 11l1-1h0 0c1-1 1-1 2-1 1-1 2-1 3-1s1-1 2-1c1-1 3-2 3-3v-2c0-1 2-2 2-2l-1-1 1-1v-2h1v-3c0-1 1-1 1-2h3l2-4c1 0 1 1 1 2l1-2h1v1h1l1-1c1-1 1-2 2-3 2 0 2-1 3-2h0z" class="Z"></path><path d="M730 197c1 1 2 1 3 1l1-1 3 3h1l2-1v1c2 1 4 2 5 3v-5l1 2v1h1v1l-1 1c3 3 8 5 10 9v1c-1-1-2-2-3-2-1-2-3-4-5-5-1-1-6-4-8-4-1-1-1-1-2-1-3-1-6-1-9-3l1-1zm18-2c3 2 1 1 3 0 0 1 0 3 1 4 1 0 1 0 2 1 1 0 2 1 4 1l3-3v1h0c1 1 0 1 1 1s0 0 1 1h-1c-2 1-3 2-4 3h-1c-1 0-2-1-3-1s-2 0-3-1c0-1-1-2-2-3h0-2v-2h1v-2z" class="O"></path><path d="M724 199v-1-4c1-3 1-5 0-8 0-1 0-1 1-2l1 3c0 3 0 8 3 11 3 2 6 2 9 3 1 0 1 0 2 1 2 0 7 3 8 4 1 3 4 4 5 7 2 2 3 5 3 8v1c1 2 1 3 2 5l-1 1h-1v-2c0-3-1-6-2-9-2-4-4-7-7-9l-1-1c-1-2-6-4-8-4s-3-1-4-1c-4-1-7-1-10-3z" class="M"></path><path d="M747 208c3 2 5 5 7 9 1 3 2 6 2 9v2h1c0 1 0 1-1 1h-1c-1 2-3 3-4 4 0 1-1 2-2 2 1-1 1-2 2-3 0-5-1-7-3-11h1l1-1c0 1 1 1 2 2l1-2-1-1-1 1c-1 0-2-2-3-2h-1c-1 0-2 0-2-1h0l1-1h0l-1-1c1-2 1-2 1-4h0c0-1 0-2 1-3z" class="F"></path><path d="M759 169h6l4 1h2 5c2 0 2 1 3 2-1 0-2 1-3 0l4 4 2 2c2 1 3 3 3 5v1l1 1s1 1 1 2v1c1 2 1 4 1 6l1 1h1c2-1 4-3 6-4l14-7c1-1 2-1 3-1l2-1h1c2-1 5-3 8-4 1-1 3-1 4-2l6-3 1 1c-3 2-7 3-9 5-6 2-13 5-18 8h-1c-4 1-8 4-12 6-2 1-4 3-6 4-3 2-7 4-10 6-3 3-5 7-7 10-3 4-5 7-8 10-1 1-3 3-4 5s-2 5-4 7-4 3-6 5c-1 0-1 0-1 1-1 0-2 1-3 1l-10 5-13 7v1c-3 1-5 4-8 3l-1 1c-1 0-2 1-2 1l-2-1h0l3-1 4-2c5-1 10-5 15-8l9-5 6-3c1-1 3-2 3-3l2-2c1-1 3-3 4-5h1v1c1-1 0-1 1-2 0-1 2-2 3-3l7-9 7-10v-1c-2 0-2 0-3 1l-1-1c1-1 1-1 3-2l2 2c0-1 1-2 2-3h0c2-1 3-3 5-4h1l3-3c0-1 0-1-1-2 1-1 1-2 1-3s-1-2-2-3c0 1-1 1-1 2h-1v1 1c-2 2-3 4-6 5h-2l1 1c2 0 2-1 5-2-1 1-1 2-2 2l-3 2-1-1c-2-1-3-2-4-4l1-1v-1h-1-2c-1-1-2-1-3-2 0 0 0-1-1-1v1h0v1l-2-2-2-2c2-1 3-2 4-3h-1l-1 1h0v-2h-1c-2 2-3 4-5 6h-3l-1 1c-1 0-2-1-3-1v-1l-1 1v1l-1-1c-3-1-4-4-5-7v-3c0-1 1-1 1-2h3l2-4c1 0 1 1 1 2l1-2h1v1h1l1-1c1-1 1-2 2-3 2 0 2-1 3-2h0z" class="T"></path><path d="M750 176l1-2h1v1h1l1-1c1 1 2 1 4 1v1l1-1c1 1 2 1 2 2l1 2-3-1-1 1v1h3c-1 2-2 3-3 4h0c-1 0-1 0-2 1v1h-2-2l1-1h-1c-1 1-1 1-2 1-1-1-3-3-4-5h1v-2-1l2-4c1 0 1 1 1 2z" class="Q"></path><path d="M749 174c1 0 1 1 1 2l1 1-1 1h-2c-1 1-1 0-1 1v-1l2-4z" class="D"></path><path d="M759 169h6l4 1h2 5c2 0 2 1 3 2-1 0-2 1-3 0h-3-1l2 1h1c1 1 2 2 3 2 0 1 0 1 1 2l-2 1c0 1 1 1 0 2-1 0-2-1-2-2h0c-1 0-2 1-3 2 0 2-1 2-2 2h-1c-1 0-1 1-1 1 1-1 1-2 2-2l1-1c-1 0-2 0-2 1l-1-1c1-1 2-1 3-2 2 0 3-1 4-3v-1c-1 1-1 1-2 1-1 2-1 2-3 2v1h-1l1-1v-1h-1l-1 1c-1 1-3 1-3 3-1 0-2-1-3-1l-1-2c0-1-1-1-2-2l-1 1v-1c-2 0-3 0-4-1 1-1 1-2 2-3 2 0 2-1 3-2h0z" class="U"></path><path d="M759 169h6c-1 1-2 0-3 1 0 1-1 1-1 2v1h-1 0v1h-2l1 1-1 1v-1c-2 0-3 0-4-1 1-1 1-2 2-3 2 0 2-1 3-2h0z" class="a"></path><path d="M725 289c2 0 4 1 6 1h5c1 0 2 2 3 3l1-1h1 2 0c4 2 11 2 14 1 1 0 1 0 2 1h-2v2l2 6c1-1 3-2 5-2-1 1-1 2-2 2-1 1-2 0-3 0v2c1 1 1-1 2 1v1l-2 8-2 4-3 7-3 9-1 3c-1 3-2 6-4 8h0c-1 1-1 1-2 1l1-1h-2c-1 2-1 2-1 3-1 2-1 2-2 3l-2 5v1c-1 2-1 4-2 5-1 2-2 3-2 4l-4 9-2 6c-2 3-3 4-6 6l-3-1-1 1-2-1-1-3c1 0 1-1 2-2v-1h1l-1-1c-1 0-2 0-3 1l-2-2h1 5l-5-1 1-1h4-1l-2-2v-1l2 1 1-1-6-3c-3-2-5-5-8-7-1 0-1-1-2-1l1-1-1-1-3-3 4 1-3-3c0-1-1-2-2-3h-1c-1-1-1-1-2-1l-1-1s-1-1-2-1c-1-1-2-1-3-2l-3-2-2-1v-1c-1 0-2-1-3-1v-1l-1-1c-1 0-1 0-2-1l-4-3-4-2 1-1c1-1 2-1 3-2v-1c-1 1-2 1-3 2l-1-1 9-21c1-2 1-3 2-4v-1c1-3 6-6 10-8h2l-1 2 1 1c1 0 1 0 2-1 1 0 2 0 4-1s4-2 5-3v-1l1-1h0c1-1 2-1 3-2h0l7-1 10-1z" class="B"></path><path d="M691 297h2l-1 2 1 1-1 1 1 1v1c-1 0-1-1-1-2-2 1-2 1-3 2v1 2l-2-1h-1v1c1 1 2 1 3 1v1h0l-4-1h-1c-1-1-1-1-3-1h0v-1c1-3 6-6 10-8z" class="H"></path><path d="M704 298l1 1c-1 0 0 0-1 1h6c1 0 1-1 2 0-2 2-5 0-6 2 0 0 1 0 2 1l1-1 1 1h0 1c-2 1-2 1-4 3h1l1-1 1 1h0v1l1-1 1 1c-1 0-1 1-2 1h-1-2 0l2 2h0-3c0-1 0-2-1-3l-1 1-1-1-1 1c-1-1-1-1-2-1h0-1l1-1v-1c-1 1-2 1-3 2 0-2 2-3 3-4h1 0c-2 0-3 1-4 2h0-1l1-1 1-2h-1v-1h1l1-1c2 0 3-1 4-2h1z" class="a"></path><path d="M681 306c2 0 2 0 3 1h1l4 1c3 2 5 3 7 7v5c1-1 1-3 2-5h7c-2 3-3 5-4 8-1 1-1 3-2 4-1 3-4 5-6 9l-3 3h-1l2-4h0c-2 2-3 3-4 6-1 0-1 1-2 1 1 2 6 5 8 6l1 2s-1-1-2-1c-1-1-2-1-3-2l-3-2-2-1v-1c-1 0-2-1-3-1v-1l-1-1h1c1 0 1 1 3 1v-1c0-2 0-3 1-5v2c2-1 2-1 3-3v1c1-3 2-4 4-7 0-1 1-2 1-4 1 0 1 0 1-1v-3l1-1v-3c-1 0-1-1-1-2-1-2-4-4-5-4l-3-2c-2 0-3 0-5-2z" class="K"></path><path d="M685 335v2c2-1 2-1 3-3v1c-1 2-1 3-4 5 0-2 0-3 1-5z" class="B"></path><path d="M717 314h2v4l-1 2 1 1c0 1-1 2-2 3-1 3-2 6-4 8-2 4-5 8-7 12 0 2-1 3-1 5-1-1-1-1-1-2h0v-1l-2 2c-1-1 0-2 0-3l-2 2h-1v-2l-1 1-1-1c-1 0-1 1-3 1v-1c1-2 1-4 2-5l1-1h-1l-1 1c-1 2-1 3-2 5l-1-1 1-2c0-1 0-1 1-2l1-3 1-2c-2 1-2 4-4 6 0 0-1 1-1 2s-1 1-1 1l-2-1 3-3v-1l3-5c1-2 3-3 5-5 1-1 1-3 2-5h1v-3c1-2 2-3 3-5l1 1h-1v2c-2 2-1 4-3 6v1l-2 4c-1 1-1 1-1 2v1c-1 0-1 1-1 2h0l1-1c0-1 0-1 1-2 0-1 1-2 1-3l1-1 2-4v1 2s-1 1-1 2h1v-1c0-1 0-1 1-2 1-2 2-5 3-7 1-1 1-2 2-3h1v2c2-2 2-2 5-2v1c-1 1-3 2-3 4l3-3c1-1 1-2 1-4z" class="J"></path><path d="M681 306h0c2 2 3 2 5 2l3 2c1 0 4 2 5 4 0 1 0 2 1 2v3l-1 1v3c0 1 0 1-1 1 0 2-1 3-1 4-2 3-3 4-4 7v-1c-1 2-1 2-3 3v-2c-1 2-1 3-1 5v1c-2 0-2-1-3-1h-1c-1 0-1 0-2-1l-4-3-4-2 1-1c1-1 2-1 3-2v-1c-1 1-2 1-3 2l-1-1 9-21c1-2 1-3 2-4z" class="G"></path><path d="M689 314l1 1-1 3c-1 2-2 3-2 4l2-2v1 1c-2 1-3 2-2 5h-1l-3 3v1-1c-1 0-2 0-2 1-1-1-1-1-1-2 0-3 2-5 3-7 2-3 4-5 6-8z" class="B"></path><path d="M680 329c0 1 0 1 1 2 0-1 1-1 2-1v1-1l2 1c0 1 0 1 1 2l1-1h0c-1 1-1 2-2 3-1 2-1 3-1 5v1c-2 0-2-1-3-1h-1c-1 0-1 0-2-1l-4-3-4-2 1-1c1-1 2-1 3-2v-1l1-1v2c0 1 0 1 1 1 2-1 2-1 3-3h1z" class="C"></path><path d="M681 306h0c2 2 3 2 5 2l3 2v4c-2 3-4 5-6 8-1 2-3 4-3 7h-1c-1 2-1 2-3 3-1 0-1 0-1-1v-2l-1 1c-1 1-2 1-3 2l-1-1 9-21c1-2 1-3 2-4z" class="O"></path><path d="M675 329h1l2-2-1-1c1-1 0-1 1-1 1-1 1-1 2-1v-1l1 1c-1 1-3 3-2 5-1 2-1 2-3 3-1 0-1 0-1-1v-2z" class="E"></path><path d="M681 306h0c2 2 3 2 5 2l3 2v4l-1 1h-1v-1c1-1 1-2 1-4h-3l-3 3h0c-1-1-1-3-1-4l-2 1c1-2 1-3 2-4z" class="F"></path><path d="M681 309c1 0 2 0 4 1l-3 3h0c-1-1-1-3-1-4z" class="j"></path><path d="M679 310l2-1c0 1 0 3 1 4h0c-2 3-3 6-5 9-1 2-2 4-2 7l-1 1c-1 1-2 1-3 2l-1-1 9-21z" class="H"></path><path d="M725 289c2 0 4 1 6 1h5c1 0 2 2 3 3l1-1h1 2 0c4 2 11 2 14 1 1 0 1 0 2 1h-2v2l2 6c1-1 3-2 5-2-1 1-1 2-2 2-1 1-2 0-3 0v2c1 1 1-1 2 1v1l-2 8-2 4-3 7-2 1-1 2v-4c0-2 0-2-1-4 1-1 1-1 1-2-1-1-2-2-3-2s-3-1-4-2l2-1-1-1-1 1-2-1-5-1-3-1h-1c-2-1-3-1-5-1l-1 1c-1 1-2 1-2 2v1l1 1-1 1h0-1v-1l-1 1h-1-1v3c0 1-1 2-2 3l-1-1 1-2v-4h-2c-3 1-7 1-10 0h-8 0c-2 0-2 0-3-1h1v-1l1 1c1 0 9 0 10-1h0c1-1 0-1 1-2h0 0l-2-2h0 2 1c1 0 1-1 2-1l-1-1-1 1v-1h0l-1-1-1 1h-1c2-2 2-2 4-3h-1 0l-1-1-1 1c-1-1-2-1-2-1 1-2 4 0 6-2-1-1-1 0-2 0h-6c1-1 0-1 1-1l-1-1h-1l-1-1-3 1c2-1 4-2 5-3v-1l1-1h0c1-1 2-1 3-2h0l7-1 10-1z" class="B"></path><path d="M714 298l3-1c1 0 3 1 4 0 1 0 1-1 2 0h1l1-1h1v2h0c-1 1-1 0-2 1h-6-2-1l-1-1z" class="L"></path><path d="M752 303c2 1 4 1 5 3v1l-2-2h-1c0 1 0 3 1 4v1l-1 1v-1c-2 0-3-1-3-3h-1c-1 0-1-1-1-1 0-2 1-2 3-3z" class="C"></path><path d="M754 311l1-1v-1c-1-1-1-3-1-4h1l2 2h1l1-1h2l-2 8-2 4-3 7-2 1-1 2v-4c0-2 0-2-1-4 1-1 1-1 1-2-1-1-2-2-3-2s-3-1-4-2l2-1c0 1 1 2 2 2 2 1 3 2 5 3h1c-2-2-1-1-1-3 0-1-2-1-2-4 2 0 2 0 4 1h0l-1-1z" class="H"></path><path d="M752 326v-3l5-5-3 7-2 1z" class="C"></path><defs><linearGradient id="AC" x1="733.494" y1="287.972" x2="728.526" y2="304.72" xlink:href="#B"><stop offset="0" stop-color="#0d0c0c"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#AC)" d="M725 289c2 0 4 1 6 1h5c1 0 2 2 3 3l1-1h1 2 0c4 2 11 2 14 1 1 0 1 0 2 1h-2v2l2 6c1-1 3-2 5-2-1 1-1 2-2 2-1 1-2 0-3 0v2c1 1 1-1 2 1v1h-2l-1 1h-1v-1c-1-2-3-2-5-3l-6-2h0c-1-1-2-1-2-2h-2l-2-2c-1 0-1 0-2-1-1 0-2-1-4-1l-1-1c-2 0-5-1-8-1s-7 2-10 1v-1c-2 0-5 0-7 2-1 1-2 2-3 2l-1 1h-1l-1-1-3 1c2-1 4-2 5-3v-1l1-1h0c1-1 2-1 3-2h0l7-1 10-1z"></path><path d="M710 300h0c1-1 2-1 3-1v-1h-1l-3 1h-3l1-1c1 0 2-1 3-1h3l1-1 1 1-1 1 1 1h1 2 6l5 1h2c1 0 1 1 2 1h2c1 1 2 1 3 1v1h-2c-1 0-1-1-1-1-1 0-1 1-3 0v1c1 1 2 1 2 1h1 3c0 2 0 2 1 4h1c1 0 1 0 2 1v-1-1c1 0 1-1 2-1h0c0 2-1 2-2 4h-1l-2-1h-1v1l5 1c1 1 1 1 2 1l-1 1-2-1-5-1-3-1h-1c-2-1-3-1-5-1l-1 1c-1 1-2 1-2 2v1l1 1-1 1h0-1v-1l-1 1h-1-1v3c0 1-1 2-2 3l-1-1 1-2v-4h-2c-3 1-7 1-10 0h-8 0c-2 0-2 0-3-1h1v-1l1 1c1 0 9 0 10-1h0c1-1 0-1 1-2h0 0l-2-2h0 2 1c1 0 1-1 2-1l-1-1-1 1v-1h0l-1-1-1 1h-1c2-2 2-2 4-3h-1 0l-1-1-1 1c-1-1-2-1-2-1 1-2 4 0 6-2-1-1-1 0-2 0z" class="K"></path><path d="M727 310l1-1c2 0 3 0 5 1h1l3 1 5 1 2 1 1-1 1 1-2 1c1 1 3 2 4 2s2 1 3 2c0 1 0 1-1 2 1 2 1 2 1 4v4l1-2 2-1-3 9-1 3c-1 3-2 6-4 8h0c-1 1-1 1-2 1l1-1h-2c-1 2-1 2-1 3-1 2-1 2-2 3l-2 5v1c-1 2-1 4-2 5-1 2-2 3-2 4l-4 9-2 6c-2 3-3 4-6 6l-3-1-1 1-2-1-1-3c1 0 1-1 2-2v-1h1l-1-1c-1 0-2 0-3 1l-2-2h1 5l-5-1 1-1h4-1l-2-2v-1l2 1 1-1-6-3c-3-2-5-5-8-7-1 0-1-1-2-1l1-1-1-1-3-3 4 1-3-3c0-1-1-2-2-3h-1c-1-1-1-1-2-1l-1-1-1-2c2-1 3-1 4-3l1 1 1-1v2h1l2-2c0 1-1 2 0 3l2-2v1h0c0 1 0 1 1 2 0-2 1-3 1-5 2-4 5-8 7-12 2-2 3-5 4-8 1-1 2-2 2-3 1-1 2-2 2-3v-3h1 1l1-1v1h1 0l1-1-1-1v-1c0-1 1-1 2-2z" class="E"></path><path d="M718 376h1l2-1c0-1 0-1-1-1l1-1c1 2 3 3 4 5l1 1c-1 1-2 3-4 4v1c-1 0-2 1-3 1v1l-1 1-2-1-1-3c1 0 1-1 2-2v-1h1l-1-1c-1 0-2 0-3 1l-2-2h1 5l-5-1 1-1h4z" class="C"></path><path d="M730 332c0 1 0 1-1 3l-2 3v1c-1 1-1 1-1 2l-1 1-1 3c0 1-2 3-2 5h1l1 1-1 1h1c1-1 1-3 3-5 0 2-1 3-1 4s-1 2-1 3v1c2-2 3-4 3-7l1-1c1-1 2 0 3 0h4 1c1 0 1 0 2-1h2l2-1c-1 2-1 2-1 3-1 2-1 2-2 3l-2 5v1c-1 2-1 4-2 5-1 2-2 3-2 4l-4 9c-1 0-2-1-3-2l3-1 3-6-1-1c-2 1-3 5-4 6h-1c1-2 3-4 3-6 1-1 1-3 2-4 1-2 1-3 2-4 0-1 1-2 1-3 1-1 1-1 1-2l-2 2h-1c-1 3 2-1 0 2l-1 1c0 2-2 6-4 8v-1c0-1 2-3 2-4-2 1-3 5-5 7v-1-1l3-6h0c-2 2-3 4-4 6 0 1 0 1-1 2v1c-1 0-3-2-3-2 0-1 1-1 0-3l-1 2h-1l-2-1c0-2 2-4 3-6l1-1v-3c-2 2-2 4-4 6l-1-1c1-1 1-3 2-4l1-2c0-1 1-2 2-3h1c1-2 2-4 2-6 1-1 1 0 1-1v-1c1-1 1-1 1-2l1-1 4-7z" class="G"></path><path d="M723 367c0-2 1-3 1-4l-2 2-1-1h1c0-1 0-1 1-1 0-2 1-3 2-4h0l1 1c0-1 1-1 2-1-2 2-3 4-4 6 0 1 0 1-1 2zm4-57l1-1c2 0 3 0 5 1h1l3 1 5 1 2 1 1-1 1 1-2 1c1 1 3 2 4 2s2 1 3 2c0 1 0 1-1 2 1 2 1 2 1 4v4l1-2 2-1-3 9-1 3c-1 3-2 6-4 8h0c-1 1-1 1-2 1l1-1h-2l-2 1h-2c-1 1-1 1-2 1h-1-4c-1 0-2-1-3 0l-1 1c0 3-1 5-3 7v-1c0-1 1-2 1-3s1-2 1-4c-2 2-2 4-3 5h-1l1-1-1-1h-1c0-2 2-4 2-5l1-3 1-1c0-1 0-1 1-2v-1l2-3c1-2 1-2 1-3 3-4 4-7 7-11h0l-1-1c0 1-1 1-2 2 0 1-1 2-2 3h0l2-2v-2c1-1 2-1 3-2h1l2-1-1-1c0-1-1-1-2-1l1-1 3 1h1c-2-1-3-2-5-2v-1h2l-1-1h-2c-1-1-2-1-3-1h-1c-2 0-3 0-4-1h-1z" class="J"></path><path d="M727 310l1-1c2 0 3 0 5 1h1l3 1 5 1 2 1 1-1 1 1-2 1c1 1 3 2 4 2s2 1 3 2c0 1 0 1-1 2 1 2 1 2 1 4v4l1-2 2-1-3 9h-2c-1 1-1 1-1 3l-1 1c0 1-1 2-1 3s-1 1-1 2l-3 1s-1 0-1 1h-3c1 0 1-1 2-1v-1h0-1c1-2 3-4 4-6l-1-1v2c-1 1-3 2-4 4 0-1 3-5 4-7l1-2c0-1 1-1 1-2v-2h0c-1-1-2-2-4-3h1l-1-1 1-1s1-1 2 0l1 1c0-1 0-2 1-2-1-3-2-5-3-7-2-1-3-2-5-2v-1h2l-1-1h-2c-1-1-2-1-3-1h-1c-2 0-3 0-4-1h-1z" class="G"></path><path d="M748 332v-4c-1-1-1-3-1-4l1-1 2 1h1v4c0 1-1 3-1 5-1-1-1-1-2-1z" class="E"></path><path d="M752 326l2-1-3 9h-2c-1 1-1 1-1 3l-1 1c0 1-1 2-1 3s-1 1-1 2l-3 1v-1c0-1 0-2 1-2 1-2 2-2 2-4v-1l3-4c1 0 1 0 2 1 0-2 1-4 1-5l1-2z" class="B"></path><path d="M727 310h1c1 1 2 1 4 1h1c1 0 2 0 3 1h2l1 1h-2v1c2 0 3 1 5 2h-1l-3-1-1 1c1 0 2 0 2 1l1 1-2 1h-1c-1 1-2 1-3 2v2l-2 2h0c1-1 2-2 2-3 1-1 2-1 2-2l1 1h0c-3 4-4 7-7 11l-4 7-1 1c0 1 0 1-1 2v1c0 1 0 0-1 1 0 2-1 4-2 6h-1l2-5c-1 0-2 2-2 3-2 3-4 9-6 11l-1-1c1-3 4-6 5-9 1-4 3-7 4-10v-1l-6 13-2 3h-1l8-16c-1 0-1 1-2 2s-2 3-2 4l-1 2c-1 1 0 1-1 2-1 2-2 4-3 7-1 1-2 3-4 4 1 3 4 3 5 6 1 2 4 3 6 5h-1s-1 0-2-1l-2-2c-2-1-3-2-4-3-2-1-4-3-4-5-1 0-2-1-3-1l-3-3c0-1-1-2-2-3h-1c-1-1-1-1-2-1l-1-1-1-2c2-1 3-1 4-3l1 1 1-1v2h1l2-2c0 1-1 2 0 3l2-2v1h0c0 1 0 1 1 2 0-2 1-3 1-5 2-4 5-8 7-12 2-2 3-5 4-8 1-1 2-2 2-3 1-1 2-2 2-3v-3h1 1l1-1v1h1 0l1-1-1-1v-1c0-1 1-1 2-2z" class="Z"></path><path d="M722 315h1l1-1v1h1 0l1-1-1-1v-1l3 1h1l1 1c-1 1-1 3-2 4h-2 0-2v-1c-1 0-1-1-2-2z" class="J"></path><path d="M719 321c1-1 2-2 2-3v-3h1c1 1 1 2 2 2v1c-2 2-3 4-5 6h0c0 1 0 2-1 2-1 2-1 3-2 4-1 2-3 4-4 6-3 5-6 10-7 15l-2 2 1 1h3v1l-2 2-1-2c-1 0-2-1-2-2l1-1c1-1 1-2 2-3 0-2 1-3 1-5 2-4 5-8 7-12 2-2 3-5 4-8 1-1 2-2 2-3z" class="E"></path><path d="M705 351c1-1 2-2 3-4 3-4 4-9 8-13h0c-2 3-3 5-4 8v1c2-2 2-4 4-6 1-1 2-3 3-4l1-1h0c-3 4-6 8-7 13-1 1-1 3-1 4 1-1 1-2 2-3 2-7 7-12 11-18h1c-1 2-2 4-4 6h0c0 2-1 2-1 4-1 0-1 1-2 2s-2 3-2 4l-1 2c-1 1 0 1-1 2-1 2-2 4-3 7-1 1-2 3-4 4 1 3 4 3 5 6 1 2 4 3 6 5h-1s-1 0-2-1l-2-2c-2-1-3-2-4-3-2-1-4-3-4-5-1 0-2-1-3-1l-3-3c0-1-1-2-2-3h-1c-1-1-1-1-2-1l-1-1-1-2c2-1 3-1 4-3l1 1 1-1v2h1l2-2c0 1-1 2 0 3l2-2v1h0c0 1 0 1 1 2-1 1-1 2-2 3l-1 1c0 1 1 2 2 2l1 2 2-2v-1h-3l-1-1 2-2z" class="D"></path><path d="M444 283l5 2 10 3 11 3 3 1h2l2-1s1 0 1-1h1 3c1-1 3-1 4-1v-1h14l2 2c3 0 4 0 6-1 1 0 2 0 4 2h0l-1 1-1 1v3 1l1 1h-2c0 2 1 2 1 3h0c1 1 1 2 3 3h1l1-1 1 1h0l2-1v1h-1v4 2l2-2v1 3l1 1c0-1 0-1 1-2v-2h1v2c2 0 3 1 5 2 1 1 4 1 6 2h1l2 1c1 1 2 1 3 2v1c-1 0-1 1-1 2-1 1-1 0-1 1l-1 2h-1l-1 2-1 1-2 4h1 0c1 1 1 2 0 4 0 0-1 1-2 1s-2 0-4-1c-1 0-2-1-3-1-2 3-1 6-2 9v17 1l-1 43v21 37c-2-2-1-13-1-17l-1-1h-1v-6l-1-2h-1l-1 2v-2c-1-2-1-4-2-6-1 2-1 2-1 5-1-2-1-3-1-4v5c0 1-1 1-1 2h-1-1-1v1c-1-2 0-2 1-3 1-2 0-8 0-11h-1c0-1 1-2 0-3l-1-1v-3h-1c-2-1-4 0-6-1-1-1-1-1-2 0h0l-1-1h0c1-1 3-1 4-1h5 1c0 1 0 1 1 1l-14-18 1-1c0-1-3-4-4-5s-3-4-5-6c-1-1-5-5-5-6l1-2c0-1 0 0 1-1 0 0 1 0 2-1 0-1 1-2 1-3l-2-1 10-5h2c-3-1-4-3-6-5-2-3-4-5-6-8l1-3c3 0 6-1 9-3 1-1 3-1 5-2 1 0 2 0 4-1l-3-2h1 1 0c-1-1-1-1-1-2l-1-1c-2-1-3-3-4-5h-1c-1-1-3-4-3-4 0-1 1-1 1-2l-2-1c-1 1-1 1-2 1-1-1-2-2-2-3l-1-1-2 1v-1h-1-1l-2-2c-3-2-6-5-9-8l-7-7c-1-1-3-3-4-5 0-2 1-1 2-2h-3 0c-2 0-5-4-6-5l-6-8h-1v-5z" class="b"></path><path d="M481 299c1 1 2 1 2 2l1-1h0c1 1 1 0 2 0-1-1-2-1-3-1h3l2 1v1h-1l-1 1c0 1 1 1 2 2h1v2l-2 1-2-2c-1-2-3-4-4-6z" class="H"></path><path d="M489 304l1-1h1c1 0 2 0 3-1 3-1 6-1 8-1l1 1c-1 1-3 2-4 2h0-3c-1 0-1 1-2 1h-2c-1 1-2 1-3 1v-2z" class="F"></path><path d="M506 360v1c-1 2-2 3-4 4h-2c-1 0-1 1-2 2l1 1c0 1 1 1 2 2h1c1 1 1 3 1 4v2-1c-1-2-3-4-4-5-2-1-3-2-5-3 1-1 3-1 4-2 1 0 2-1 2-1l2-2h2c0-1 1-1 2-2z" class="S"></path><path d="M489 306c1 0 2 0 3-1h2c1 0 1-1 2-1h3c-3 2-6 2-9 4 1 1 4 3 5 4l2-1 2 2c0 2-1 3 0 5l-3-2-3-3c-2-1-4-4-6-6l2-1zm16 82h1c0 4-1 5-4 8l-2 2c-2 0-2-1-4-2v-1c1-1 1-2 2-3 2 0 0 0 2-1 0 0 1 0 1-1 2 0 3-1 4-2z" class="E"></path><defs><linearGradient id="AD" x1="499.144" y1="382.059" x2="495.019" y2="367.034" xlink:href="#B"><stop offset="0" stop-color="#161719"></stop><stop offset="1" stop-color="#363636"></stop></linearGradient></defs><path fill="url(#AD)" d="M494 367h0c2 1 3 2 5 3 1 1 3 3 4 5v1l3 3h0c-1 1-1 0-2 1 0 2 1 3 0 4h-1c-1-2-2-5-4-7s-5-5-7-6h-2c1-2 1-3 2-4h2z"></path><path d="M486 289l1 1h-3c2 1 4 3 6 3 1-1 1-1 2-1h0c3 2 9 3 11 6-2-1-4-2-7-3-2 0-7-1-10 1-2 0-2 0-3-1-3 0-5 0-7 1-2 0-4 1-6 2h-1 0c0-1 1-2 2-2h2c0-1 1-1 2-1 0-2-1-2-2-3h2l2-1s1 0 1-1h1 3c1-1 3-1 4-1z" class="p"></path><path d="M473 292h2c4 1 9 1 11 4h0c-2 0-2 0-3-1-3 0-5 0-7 1-2 0-4 1-6 2h-1 0c0-1 1-2 2-2h2c0-1 1-1 2-1 0-2-1-2-2-3z" class="C"></path><path d="M486 288h14l2 2c3 0 4 0 6-1 1 0 2 0 4 2h0l-1 1-1 1v3 1l1 1h-2c0 2 1 2 1 3h0l-1-1-1 1v1l-1-1-1-1h-2l-1-2c-2-3-8-4-11-6h0c-1 0-1 0-2 1-2 0-4-2-6-3h3l-1-1v-1z" class="t"></path><path d="M500 322h2c1 1 3 3 4 2h0l1 1c-2 2 0 2-1 4 0 1-1 1-1 3l1 1v3l-2 2h-1-5l-1-2v-1h-2 0-1-1c-1-1-3-4-3-4 0-1 1-1 1-2l-2-1h1c1-1 3-2 4-3v1h2 0c1-2 2-2 4-4z" class="C"></path><path d="M503 338c-1-1-1-2-1-3-1-1-1-1-1-2l1-1 1 1h1 1v2l1 1-2 2h-1z" class="F"></path><path d="M498 340c2 1 3 2 4 3l-1 2h3c0 4-1-1-1 2l1 2v3c-1 1 0 4 0 5v1 1h2v1c-1 1-2 1-2 2h-2l-2 2s-1 1-2 1c-1 1-3 1-4 2h0c-3-1-4-3-6-5-2-3-4-5-6-8l1-3c3 0 6-1 9-3 1-1 3-1 5-2 1 0 2 0 4-1l-3-2h1 1 0c-1-1-1-1-1-2l-1-1z" class="B"></path><g class="F"><path d="M498 340c2 1 3 2 4 3l-1 2h3c0 4-1-1-1 2l1 2v3c-1 1 0 4 0 5v1c-2-1-5-2-7-2l-1 1h-1l-1-1-1 1 1 1h-1-1-1v-1-3h1v1l1-1h0c2-3 5-2 8-4l2-1v-2l-2-2-3-2h1 1 0c-1-1-1-1-1-2l-1-1z"></path><path d="M493 358h1l-1-1 1-1 1 1h1l1-1c2 0 5 1 7 2v1h2v1c-1 1-2 1-2 2h-2l-2 2s-1 1-2 1c-1 1-3 1-4 2h0c-3-1-4-3-6-5l1-3h2c1 0 1 0 2-1z"></path></g><path d="M493 358h1l-1-1 1-1 1 1h1l1-1c2 0 5 1 7 2v1h2v1c-1 1-2 1-2 2h-2s1-1 1-2c-3-1-4 0-6 1h0-4 0c-1 0-2-1-2-2 1 0 1 0 2-1z" class="C"></path><path d="M492 367c-1 1-1 2-2 4h2c2 1 5 4 7 6s3 5 4 7h1v3l1 1c-1 1-2 2-4 2 0 1-1 1-1 1-2 1 0 1-2 1-1 1-1 2-2 3h-1 0c0-1 0-1-1-2v2l-1-1c-1-1 0-1-1-2v-1l-2-1c0-1-1-2-1-2-1-1-2-1-3-1 0-1-1-3-1-4-1-2-2-3-3-4l-2-1c0-1 0 0 1-1 0 0 1 0 2-1 0-1 1-2 1-3l-2-1 10-5z" class="E"></path><path d="M493 387c-1-2-2 0-3-1l1-1c2-2 4-2 6-1 2 0 2 0 3 1v1h-2-2s-1 0-2 1h-1z" class="O"></path><path d="M493 387h1c1-1 2-1 2-1h2c0 1-1 2-1 3-2 1-4 1-5 2l-2-1 3-3z" class="J"></path><path d="M503 384h1v3l1 1c-1 1-2 2-4 2 0 1-1 1-1 1-2 1 0 1-2 1-1 1-1 2-2 3h-1 0c0-1 0-1-1-2v2l-1-1c-1-1 0-1-1-2v-1c1-1 3-1 5-2l6-3v-2z" class="H"></path><path d="M492 367c-1 1-1 2-2 4h2c2 1 5 4 7 6l-1 1h-1c-2 1-4 3-5 4h-1c-2 1-4-1-5-2s-3-1-4-1l-2-1c0-1 0 0 1-1 0 0 1 0 2-1 0-1 1-2 1-3l-2-1 10-5z" class="G"></path><path d="M492 371c2 1 5 4 7 6l-1 1h-1c-2 1-4 3-5 4h-3v-1c2-3 4-2 7-4l-1-1h-3c-1 1-1 1-2 1v-1h-1-1c0-1 0-1 1-2h0c1-2 1-2 3-3z" class="D"></path><path d="M477 311h0c1-1 2-1 4-2h1 1c1-1 2-1 3-1-1-2-1-2-1-3l2 2c2 2 4 5 6 6l3 3 3 2 7 6h0c-1 1-3-1-4-2h-2c-2 2-3 2-4 4h0-2v-1c-1 1-3 2-4 3h-1c-1 1-1 1-2 1-1-1-2-2-2-3l-1-1-2 1v-1h-1-1l-2-2c-3-2-6-5-9-8l-7-7h2c2 0 3 4 5 4 0-1-1-2-1-3s-1-1-1-2l1-2c2 1 2 2 4 3h-1c-1 1-1 1-2 1 1 1 2 1 2 3 2 0 4 1 5 0l1-1z" class="K"></path><path d="M484 324h1c3-3 6-3 10-4 1 1 3 1 5 1-1-2-3-3-4-4v-1l3 2 7 6h0c-1 1-3-1-4-2h-2c-2 2-3 2-4 4h0-2v-1c-1 1-3 2-4 3h-1c-1 1-1 1-2 1-1-1-2-2-2-3l-1-1v-1z" class="Q"></path><path d="M488 325l2 3h-1c-1 1-1 1-2 1-1-1-2-2-2-3l3-1z" class="F"></path><path d="M488 325h1l6-2c2 0 3 0 5-1-2 2-3 2-4 4h0-2v-1c-1 1-3 2-4 3l-2-3zm-26-17h2c2 0 3 4 5 4 0-1-1-2-1-3s-1-1-1-2l1-2c2 1 2 2 4 3h-1c-1 1-1 1-2 1 1 1 2 1 2 3 2 0 4 1 5 0l1-1c4 0 7-1 11-1v1c-1 0-1 0-2 2l1 1c-2 1-6 0-8 1h-3l-1 1c1 2 3 2 4 4 2 1 3 3 5 4v1l-2 1v-1h-1-1l-2-2c-3-2-6-5-9-8l-7-7z" class="E"></path><path d="M444 283l5 2 10 3 11 3 3 1c1 1 2 1 2 3-1 0-2 0-2 1h-2c-1 0-2 1-2 2h0 1c2-1 4-2 6-2 1 1 1 1 2 1 1 1 2 1 3 2 1 2 3 4 4 6 0 1 0 1 1 3-1 0-2 0-3 1h-1-1c-2 1-3 1-4 2h0l-1 1c-1 1-3 0-5 0 0-2-1-2-2-3 1 0 1 0 2-1h1c-2-1-2-2-4-3l-1 2c0 1 1 1 1 2s1 2 1 3c-2 0-3-4-5-4h-2c-1-1-3-3-4-5 0-2 1-1 2-2h-3 0c-2 0-5-4-6-5l-6-8h-1v-5z" class="I"></path><path d="M469 304h-2v-1-1l1-1h-2l2-1c1 0 2-1 4-1v2l-3 3z" class="C"></path><path d="M472 301c2 0 3 0 5 1v1c1 2 1 2 0 3h-1c0 1-1 1-1 1h-1l-2 1c-2-1-2-2-4-3l1-1 3-3z" class="D"></path><path d="M478 297c1 1 2 1 3 2 1 2 3 4 4 6 0 1 0 1 1 3-1 0-2 0-3 1h-1-1c-2 1-3 1-4 2h0l-1 1c-1 1-3 0-5 0 0-2-1-2-2-3 1 0 1 0 2-1h1l2-1h1s1 0 1-1h1c1-1 1-1 0-3h1c1-1 2-2 2-3-1-1-1-2-2-3z" class="G"></path><path d="M480 300c1 2 3 2 2 4v2 1h-5v-1c1-1 1-1 0-3h1c1-1 2-2 2-3z" class="L"></path><path d="M444 283l5 2 10 3 11 3 3 1c1 1 2 1 2 3-1 0-2 0-2 1h-2c-1 0-1-1-2 0-1-1-2-1-3-1h-1c-1-1-1-1-2-1 0-1-2-2-3-2s-4 2-5 2h0c-1 0-1 0-1-1h-2c0 1 0 1-1 2v1l-6-8h-1v-5z" class="b"></path><path d="M444 283l5 2v3l-1 1-3-1h-1v-5z"></path><defs><linearGradient id="AE" x1="511.412" y1="375.657" x2="519.451" y2="376.375" xlink:href="#B"><stop offset="0" stop-color="#edeedf"></stop><stop offset="1" stop-color="#e9e7e8"></stop></linearGradient></defs><path fill="url(#AE)" d="M504 300h2l1 1 1 1v-1l1-1 1 1c1 1 1 2 3 3h1l1-1 1 1h0l2-1v1h-1v4 2l2-2v1 3l1 1c0-1 0-1 1-2v-2h1v2c2 0 3 1 5 2 1 1 4 1 6 2h1l2 1c1 1 2 1 3 2v1c-1 0-1 1-1 2-1 1-1 0-1 1l-1 2h-1l-1 2-1 1-2 4h1 0c1 1 1 2 0 4 0 0-1 1-2 1s-2 0-4-1c-1 0-2-1-3-1-2 3-1 6-2 9v17 1l-1 43v21 37c-2-2-1-13-1-17l-1-1h-1v-6l-1-2h-1l-1 2v-2c-1-2-1-4-2-6-1 2-1 2-1 5-1-2-1-3-1-4v-29-72c0-10 0-22-6-30z"></path><path d="M517 323l1-2v7c-1 3 0 6 0 9v11 18c0 6 1 12 0 18v3h-1v-64z" class="v"></path><path d="M518 321v-1h2c0-2 0-3 1-4v5 22 17h-1c0-2 1-9 0-10-2-2 0 1-1-1v-1h-1v-11c0-3-1-6 0-9v-7z" class="u"></path><path d="M514 307c0 1 0 2 1 3 0-3 0-4 2-6v4 2l2-2v1 3l1 1c0-1 0-1 1-2v-2h1v2l-1 10v-5c-1 1-1 2-1 4h-2v1l-1 2v-13h-1c-1 2 0 4 0 6l-1 13c0 3-1 6-1 9v20c-1-6 0-13-1-20 0-2 0-3-1-5l1-10 1 1v-5-12z" class="s"></path><path d="M518 348h1v1c1 2-1-1 1 1 1 1 0 8 0 10h1v1l-1 43v21c-2-3-1-11-1-15v-7-1-4c1-4 0-9-1-14 1-6 0-12 0-18v-18z" class="n"></path><path d="M519 348v1c1 2-1-1 1 1 1 1 0 8 0 10v1l-1-1v-12z" class="q"></path><path d="M504 300h2l1 1 1 1v-1l1-1 1 1c1 1 1 2 3 3h1l1-1 1 1h0l2-1v1h-1c-2 2-2 3-2 6-1-1-1-2-1-3v12 5l-1-1-1 10v-3c0 2 0 3-1 5 0-3 0-3-1-5 0-10 0-22-6-30z" class="m"></path><path d="M513 323l-1-8 1-8c0-1 0-2 1-2v2 12 5l-1-1z" class="u"></path><path d="M518 384c1 5 2 10 1 14v4 1 7c0 4-1 12 1 15v37c-2-2-1-13-1-17l-1-1h-1v-6-22-29h1v-3z" class="p"></path><path d="M522 311c2 0 3 1 5 2 1 1 4 1 6 2h1l2 1c1 1 2 1 3 2v1c-1 0-1 1-1 2-1 1-1 0-1 1l-1 2h-1l-1 2-1 1-2 4h1 0c1 1 1 2 0 4 0 0-1 1-2 1s-2 0-4-1c-1 0-2-1-3-1-2 3-1 6-2 9v-22l1-10z" class="H"></path><path d="M510 330c1 2 1 2 1 5 1-2 1-3 1-5v3c1 2 1 3 1 5 1 7 0 14 1 20l1 49v10h1l1-1v22l-1-2h-1l-1 2v-2c-1-2-1-4-2-6-1 2-1 2-1 5-1-2-1-3-1-4v-29-72z" class="t"></path><path d="M515 407v10h1l1-1v22l-1-2h-1l-1 2v-2l1-29z" class="q"></path><path d="M510 402h1c0-3-1-11 1-13 1 7 0 15 0 22 0 6 2 13 1 19h-1c-1 2-1 2-1 5-1-2-1-3-1-4v-29z" class="o"></path><path d="M196 160c13 2 28-1 41 0 2 0 3 1 5 1h0c-2 0-6 0-8 1h12 10 63 1c4 1 8 0 12 1h-1c-1 1-1 1-2 1v1l-1 1v2l-1-1c0-1-1-1-2-2l-1 1 4 4v1l-3-3-2-2-1 1c1 1 2 2 2 3s0 1 1 2h2 5v1h-1c-2 0-3 0-5 1-3 0-8-1-11 0v2l-3 1s-1 1-1 2c-1 0-1 1-2 1-1 2-1 2-2 3v1l-2 3 1 1 1-1 1-1c-1 3-2 7-1 10l3 3-1 1-2-2-1-2h0l1 2-4 1c0 1 0 3 1 4h-2l1 1h1v1l-1 1 1 2s-1 1-1 2c-1 0-1 1-2 1l-1 1h0c-1 1 0 1-1 2l1 1c-4 0-8 0-12 1-4 2-6 4-8 7-2 1-3 3-4 5l1 1v2h-1-1 0l1 1c1 0 1 1 2 2v1 1 1c2 2 5 3 8 5l1 1h1l8 5 4 2c2 1 4 3 7 4 3 2 10 5 12 8-1 0-6-2-7-3-9-5-17-11-26-15h0v1c-2-1-3-1-5-2v-1l-2 1c-1-1-4-3-6-3l-2-2c-1-1-2-3-3-4 0-1-1-2-1-3-1-1-1-2-2-3l-1 1h-1c0-1 1-1 2-3l-2-1v1h0c-1-1-2-1-3-2l1-1-2-2-1 1-2-1c1 0 1 0 2 1v-1l-2-2v-2-1c-1 1-1 1-2 1l-1-1h0v-3l1 1h0l-2-2 1-2-1-1h-2l-2-1h1l1-1v-1l1-1c-2-2-7-4-10-6-2-1-3-2-5-3-7-5-16-9-24-12l-12-5c-3-1-6-3-9-5l-2 2c-1-2-1-3-3-3l-2 3v2l-1-1c-1 0-3-1-4-1-4 1-6 3-11 2 4-2 9-4 12-7v-2c0-2-2-4-3-7h2c1 1 1 3 2 4 0-2 0-2-1-4 0 0 0-1 1-1 0 1 1 3 2 4v1c1 0 2 0 3-1v-2h1l1 1 1-1h1c1-1 1-1 1-2h1v-1h4z" class="c"></path><path d="M246 162h10v3h1 1l1 1h-1-1l-1 1c1 1 1 1 1 2h-1v-1c-1 0-1 1-2 1v-1c1-1 1 0 1-2l-1-1-1 1v2l-1 1c0-2-1-2-1-4l-1 1v1h-1v-1c-1 1-1 0-2 1v-1l-2-2h-1v1c1 0 1 0 2 1l-1 1v-1h-1l-1 2 1 1c0 1 0 1-1 2-1 0-1 0-2 1v1c1-1 2-1 2-1v1l-1 1 2 2h1v1c-1 0-2 0-2 1l-1 1c0-1 1-2 1-3-1-1-1-2-2-2l-2-2h1c1-1 2-2 2-3-2-1-4-2-5-4h0c1-1 1-1 2-1s1 1 2 1v-2h4l1-1z" class="J"></path><path d="M242 188c2 1 3 2 4 4h-1c2 2 3 3 5 4h0v-1c1 0 1 1 2 2v1h-1l-1 1c1 0 1 1 2 1s2 1 3 1v1c1 2 1 3 2 5h-1 0v2h2l1 1h0 2c-1 1-1 1-1 2l1 1h-1l-1-1-1 1c1 1 1 1 2 1s2 1 2 1c1 2 1 2 3 3h0v-2h0v-1c1-1 0-1 1-1s2-1 2-1l1-1h0l1 1c-1 1-1 1-2 1l-1 1 1 1 1-1c1 1 2 1 2 3-1 0-2 0-4-1v1c0 1 0 0 1 1 0 1 1 1 2 1v1l-1 1-3-2v1c0 1 1 1 2 2v1h0v1l-3-3c-1-2-2-3-4-4v-1h-1c-3-2-5-6-6-10-2-5-9-6-13-10h1v-1-2h-1v-1c1-2 1-4 1-5z" class="K"></path><path d="M287 186c0 1 0 1 1 2l1 1 2 2c1-1 0-3 1-3v-1l1 1h1 3l1 1h-3l2 2v1h-1l-1-1c1 2 2 2 2 4l1 1v1c-2 2-4 2-6 3h-3l-1-1v1h0c-1 1-1 1-2 1l-1 1h-1-3v-1l-1 1c-1-1-2-2-3-2v2h1c-1 1-2 1-3 1v-3h-1v4l-5 1c-2-2-4-3-6-5l-1-1-1 1c1 1 2 2 3 4h-1c-1-2-2-2-3-4v-2c0-1 0-1 1-2h0l1 1h1c-1-2 0-2-1-3l1-1c2-1 4-1 7-1h1 1l1 1h-1l5 5c2-1 5-3 6-5v-1c-1 1-1 1-2 1l-1-1-2 1h-1l-1-1v-1l2-1c1 1 2 1 3 1 2 0 4-4 6-5z" class="G"></path><path d="M196 160c13 2 28-1 41 0 2 0 3 1 5 1h0c-2 0-6 0-8 1h12l-1 1h-4v2c-1 0-1-1-2-1s-1 0-2 1h0-1l-1 1c1 1 2 2 4 3 1 0 0 0 1 1h-2l-4-4h-1l1 1c-1 0-1 0-2-1s-1-1-3-1c0 1 0 1-1 1l-1-1h-2l-2 2-2-1c-1 1-1 1-2 1s-1-1-1-2v-1-1l-1 1h-1l-1 1c1 0 1 1 1 2-1-1-1-2-2-2l-2 1v-2l-1 1h0l-1-1-1 1v1h0l-2-1h0c-1 2-1 1 0 3h-1l-2 2 2-2-1-1-1 1c-2-1-3-2-4-4l-1-1-2 1v2h-1l-2 2-2 2 2 1h1c-1 1-2 1-1 2l1 1c0-1 1-1 2-1h0l2-1c0 1-1 1-1 2v2c-1 0-2-1-3-1-6-2-8-4-12-9 1 0 2 0 3-1v-2h1l1 1 1-1h1c1-1 1-1 1-2h1v-1h4z" class="B"></path><path d="M192 170h0l-1 1c-1-1-2-2-3-4l1-1h2v-1c2-1 3-1 5-1l-1 2h1l-2 2-2 2z" class="G"></path><path d="M196 160c13 2 28-1 41 0 2 0 3 1 5 1h0c-2 0-6 0-8 1h-27c-5 0-10 0-15-1v-1h4z" class="h"></path><path d="M196 166h1v-2l2-1 1 1-1 1 2 2c0 1-1 1-1 1 0 1 1 1 1 2-1 1 0 0 0 1h1s0 1 1 1v1 2l-2 1c1 1 1 1 1 2l2-3c1 1 2 1 2 2s0 2 1 2h1c-1-2-1-2-1-4 1 1 1 0 1 2 0 1 1 1 1 2v1c1 0 2 0 3 1h1v-1c-1-1-2-1-3-1v-2h0v-2h1 0c1 2 2 2 4 2 1 3 1 6 4 7h0c0-1 1 0 0-1s-1-2-2-4c0-2 0-5 1-6 1-2 2-2 4-3 1 0 3 0 5 1 3 0 6 3 9 5v1l-2 1c1 1 0 1 1 2-1 2 0 3-2 5-1 1-2 3-3 4v1c1-1 1-1 2-1v1c1 0 1-1 2-2h0v1 1 2s1 0 2 1v-1l1 2 1-1h0l1-6 2-2v-2c1-2 3-4 4-6l1 1c-3 3-4 5-4 8-1 2-1 3-2 5 0 2-1 3 0 5v1c-2-2-3-2-5-3-6-4-14-8-21-11-2-1-5-2-8-3l-1-1c-1 0-2-1-3-1h-1l-3-1v-1-2c0-1 1-1 1-2l-2 1h0c-1 0-2 0-2 1l-1-1c-1-1 0-1 1-2h-1l-2-1 2-2 2-2z" class="J"></path><path d="M181 161c0 1 1 3 2 4v1c4 5 6 7 12 9 1 0 2 1 3 1v1l3 1h1c1 0 2 1 3 1l1 1c3 1 6 2 8 3 7 3 15 7 21 11 2 1 3 1 5 3v-1c-1-2 0-3 0-5 1-2 1-3 2-5v2c0 1 0 3-1 5v1h1v2 1h-1c4 4 11 5 13 10 1 4 3 8 6 10 2 3 4 5 6 8 4 4 7 9 12 12 2 2 5 3 8 5l1 1h1l8 5 4 2c2 1 4 3 7 4 3 2 10 5 12 8-1 0-6-2-7-3-9-5-17-11-26-15h0v1c-2-1-3-1-5-2v-1l-2 1c-1-1-4-3-6-3l-2-2c-1-1-2-3-3-4 0-1-1-2-1-3-1-1-1-2-2-3l-1 1h-1c0-1 1-1 2-3l-2-1v1h0c-1-1-2-1-3-2l1-1-2-2-1 1-2-1c1 0 1 0 2 1v-1l-2-2v-2-1c-1 1-1 1-2 1l-1-1h0v-3l1 1h0l-2-2 1-2-1-1h-2l-2-1h1l1-1v-1l1-1c-2-2-7-4-10-6-2-1-3-2-5-3-7-5-16-9-24-12l-12-5c-3-1-6-3-9-5l-2 2c-1-2-1-3-3-3l-2 3v2l-1-1c-1 0-3-1-4-1-4 1-6 3-11 2 4-2 9-4 12-7v-2c0-2-2-4-3-7h2c1 1 1 3 2 4 0-2 0-2-1-4 0 0 0-1 1-1z" class="P"></path><path d="M184 176l-2-3c0-1 0-1 2-2l2 2-2 3z" class="f"></path><path d="M251 205c2 3 3 6 5 9 1 2 3 4 4 6l7 7c2 3 4 8 8 10h0l7 5c1 0 2 0 2 1 1 0 2 0 2 1h0v1c-2-1-3-1-5-2v-1l-2 1c-1-1-4-3-6-3l-2-2c-1-1-2-3-3-4 0-1-1-2-1-3-1-1-1-2-2-3l-1 1h-1c0-1 1-1 2-3l-2-1v1h0c-1-1-2-1-3-2l1-1-2-2-1 1-2-1c1 0 1 0 2 1v-1l-2-2v-2-1c-1 1-1 1-2 1l-1-1h0v-3l1 1h0l-2-2 1-2-1-1h-2l-2-1h1l1-1v-1l1-1z" class="D"></path><path d="M270 166v1c1 0 1-1 2-1l1 1h0c0-1 1-1 1-2l1-1 1 1v1c-1 1-1 1-1 2h0l2-2v1 2h0l-1 1h2l-1-2h1c1-2 1-3 2-4h1c1 1 0 1 1 1l3 2h1l-1-1c-1 0-1 0-1-1h2l1 1h0c1 1 1 1 1 2s1 1 2 2h0c-1-2-2-2-2-5h2l2 1h0c1 1 1 1 2 1l-1-2h2c2 1 7-1 8 0-1 1-1 2-3 2 1 1 1 1 1 2s0 1-1 2h1c1 1 2 3 2 4l2 2-1 1c-2-1-1-1-3-2-1 1-1 1-2 1v1l-1 1h0c0 1-1 1 0 2h0c0 1 0 2 1 3h-1v2l-1 1h-2l-1 1h-1l-1-1v1c-1 0 0 2-1 3l-2-2-1-1c-1-1-1-1-1-2-1-1-1-2-1-3h-1l1-1-1-1c0-1 0-3 1-5l-1-2-2 1 1 2h0l-1 1c0-2 0-2-1-3h-2l1 1c1 1 1 3 1 5 0 1-2 2-2 3v-1h0l-1-1c-2 2-2 3-5 3v1l2 1c0-1 1-1 2-1l-1 1h-3v-1c-3-1-4-2-6-4v-3h-1l-1 1h0v1c0 1 1 2 1 3h1l-1 2 2 2h-1c-2-1-3-2-4-3v-1c0-2-1-2-1-3v-1c1-2 3-3 4-5 2 2 4 5 6 5 1 0 1 0 2 1h1l1-1v-1l-1 1 1-3c1-2-6-5-7-7v-4z" class="K"></path><path d="M280 207c1 0 2-2 2-2 3-1 5-2 7-3 1 0 1-1 2-1s3 0 4-1 5-2 6-4c2-2 3-8 4-11v-1c1-2 1-3 3-5 1-3 4-4 7-5v2l-3 1s-1 1-1 2c-1 0-1 1-2 1-1 2-1 2-2 3v1l-2 3 1 1 1-1 1-1c-1 3-2 7-1 10l3 3-1 1-2-2-1-2h0l1 2-4 1c0 1 0 3 1 4h-2l1 1h1v1l-1 1 1 2s-1 1-1 2c-1 0-1 1-2 1l-1 1h0c-1 1 0 1-1 2l1 1c-4 0-8 0-12 1-4 2-6 4-8 7-2 1-3 3-4 5l1 1v2h-1-1 0l1 1c1 0 1 1 2 2v1 1 1c-5-3-8-8-12-12-2-3-4-5-6-8h1v1c2 1 3 2 4 4l3 3v-1h0v-1c-1-1-2-1-2-2v-1l3 2 1-1v-1c-1 0-2 0-2-1h3l1 1v-1c1-1 0-1 0-3h1c0-4 3-7 5-10h1l1 1z" class="T"></path><path d="M278 206h1l1 1c-3 4-6 10-7 15v5c-1 1-1 2-2 2l1 1c1 0 1-2 1-2l3-9h2v1l1 1h0l1 1v1c-2 1-3 3-4 5l1 1v2h-1-1 0l1 1c1 0 1 1 2 2v1 1 1c-5-3-8-8-12-12-2-3-4-5-6-8h1v1c2 1 3 2 4 4l3 3v-1h0v-1c-1-1-2-1-2-2v-1l3 2 1-1v-1c-1 0-2 0-2-1h3l1 1v-1c1-1 0-1 0-3h1c0-4 3-7 5-10z" class="C"></path><path d="M276 219v-1c1-2 1-3 2-5 2-3 8-9 12-9l1-1c2-1 3-1 4-1 3-1 5-2 8-3 0 1 0 3 1 4h-2l1 1h1v1l-1 1 1 2s-1 1-1 2c-1 0-1 1-2 1l-1 1h0c-1 1 0 1-1 2l1 1c-4 0-8 0-12 1-4 2-6 4-8 7v-1l-1-1h0l-1-1v-1h-2z" class="B"></path><path d="M301 205c-1-1-1 0-1-1-1 0-1 0-1-1v-1h2l1 1 1 1-2 1z" class="F"></path><path d="M294 211c-1-2 0-2 0-4l-1-1h0v-1c1-1 2-1 3-2h1v1l-2 3c0 1 0 0 1 2v3l-2-1z" class="C"></path><path d="M301 205l2-1h1v1l-1 1 1 2s-1 1-1 2c-1 0-1 1-2 1l-1 1h0c-1 1 0 1-1 2l1 1c-4 0-8 0-12 1h-1-1-1v-1c-1-1-2-1-3-2l1-1c1-1 1-1 1-2h1c1 0 1 1 2 1h0l3 1c1 0 1 1 3 2 0-1 1-2 1-3l2 1v-3c-1-2-1-1-1-2l2-3c2 2 2 2 4 3l1-1h-1v-1z" class="G"></path><path d="M256 162h63 1c4 1 8 0 12 1h-1c-1 1-1 1-2 1v1l-1 1v2l-1-1c0-1-1-1-2-2l-1 1 4 4v1l-3-3-2-2-1 1c1 1 2 2 2 3s0 1 1 2h2 5v1h-1c-2 0-3 0-5 1-3 0-8-1-11 0s-6 2-7 5c-2 2-2 3-3 5v1c-1 3-2 9-4 11-1 2-5 3-6 4s-3 1-4 1-1 1-2 1c-2 1-4 2-7 3 0 0-1 2-2 2l-1-1h-1c1-2 3-3 6-4h1l1-1c1 0 1 0 2-1h0v-1l1 1h3c2-1 4-1 6-3v-1l-1-1c0-2-1-2-2-4l1 1h1v-1l-2-2h3l-1-1h-3l1-1h2l1-1v-2h1c-1-1-1-2-1-3h0c-1-1 0-1 0-2h0l1-1v-1c1 0 1 0 2-1 2 1 1 1 3 2l1-1-2-2c0-1-1-3-2-4h-1c1-1 1-1 1-2s0-1-1-2c2 0 2-1 3-2-1-1-6 1-8 0h-2l1 2c-1 0-1 0-2-1h0l-2-1h-2c0 3 1 3 2 5h0c-1-1-2-1-2-2s0-1-1-2h0l-1-1h-2c0 1 0 1 1 1l1 1h-1l-3-2c-1 0 0 0-1-1h-1c-1 1-1 2-2 4h-1l1 2h-2l1-1h0v-2-1l-2 2h0c0-1 0-1 1-2v-1l-1-1-1 1c0 1-1 1-1 2h0l-1-1c-1 0-1 1-2 1v-1h0-1v1h0c-2 0-1 0-2-1-1 1-1 1-1 3h-1v-2c1-1 0-1 0-2-2 0-2 0-4 2v-2l-1 1h-1l-1-1h-1-1v-3z" class="B"></path><path d="M498 768l7-1-1 1 1 1c3 0 6 0 8 1h0 2c1 1 1 2 2 2l2 6 1 1c0 1 0 1 1 2l1 5c-1 1-1 1-2 1h-1l-2-1h0l-1-1v1c0 1 1 1 1 3 3 2 6 2 10 2 2 0 4 0 6-1 1 0 2-1 3-1 1 1 2 3 3 4 4 3 10 6 13 10l1 2c1 1 1 3 1 5v1c0 1-1 2-1 3l-2 3v3l-1 1v3c-1 1-1 1 0 2v1c-1 2-2 4-4 7h0c-1 1-2 2-3 2-3 1-6 4-8 6h0c0 1 0 1-1 2-1 2-2 2-3 4v2c-3 3-5 5-5 9-1 2-3 4-4 6l-6 11v-2c0-1 0 0 1-1 1-3 2-5 3-9 1-1 1-2 1-3h-1v-1h-1l-1-1h-1v1 2h-1-1c-2 1-3 1-4 1h-1-1c-1-1-3-2-3-3l-2-3c-1-1-2-3-2-4l-2 1-2-1v3 1c1 2 2 2 1 4v1h-1v-1-1h-1-1c-1-2-2-3-4-5-3-4-8-8-12-12l-2-2c-2-3-3-6-4-9-1-5-4-9-6-13l2-1-2-2c-3-2-8-4-9-7h0c-1-2-2-3-2-5l-1-3h1l1-3h0c2-2 4-5 5-7 9-11 23-17 36-20 0-1-1-2-1-3z" class="W"></path><path d="M497 836c2 1 3 2 5 3h0c-1 0-1 0-2 1l-1-1c-2-1-2-1-2-3z" class="H"></path><path d="M497 843h6c1 1 1 2 2 3v1c-2-1-1 0-2-1s-3-1-4-1h0c-1 0-2-1-2-2z" class="I"></path><path d="M505 852c1 1 2 1 3 2 0 3 0 4 2 6 1 1 1 1 1 3h-1 0c-2-2-3-3-4-5l-1-6z" class="S"></path><path d="M481 817v-1h1 1l1-1c0-1 0 0 1-1 0 1 1 2 2 2 0 1 1 2 2 2v-1l1-2c1 5 1 8 3 13v1h-1c-2-3-2-6-4-9l-1-1c0-1-4-1-5-2h-1z" class="C"></path><path d="M481 817c-1 0-1 0-2-1 0-2 1-5 2-7v-2l3-3 2 2c1 2 1 5 3 7v4 1c-1 0-2-1-2-2-1 0-2-1-2-2-1 1-1 0-1 1l-1 1h-1-1v1z" class="S"></path><path d="M470 818h1v-1h0c1 0 1 1 2 2h0c2 3 2 6 4 8h0v-4l1-1c1 2 1 3 1 4h-1c0 2 0 2 1 3l1 2v-3h1c0 1 1 1 1 2-1 1-1 1-2 1v1c-2-1-2-1-2-2l-1-1v1l-2-1v-1c0 1 0 1 1 2 1 3 1 5 2 8l1 1 1 1h0l-2 1c-2-3-3-6-4-9-1-5-4-9-6-13l2-1z" class="J"></path><path d="M493 795c0 1 0 2 1 2l2 1c2 1 3 1 4 3h0-2v1l-2 1-1-1-2 2-1 2c-1 1-1 3-2 4v5l-1 2v-4c-2-2-2-5-3-7 0-2 1-4 2-6 2-2 3-4 5-5z" class="B"></path><path d="M494 797l2 1c-2 1-3 3-3 5v1l-1 2-2-2c0-3 2-5 4-7z" class="Z"></path><path d="M496 798c2 1 3 1 4 3h0-2v1l-2 1-1-1-2 2v-1c0-2 1-4 3-5z" class="N"></path><path d="M498 802v-1h2c1 2 0 5 0 8l-1 5c-1 0-1 1-2 1 0 1 1 2-1 2v-2l-1 1v3l-1-1h-1v-3c-1 0-1-1-1-1l-2-4c1-1 1-3 2-4l1-2 2-2 1 1 2-1z" class="M"></path><path d="M498 802c0 4-2 7-3 10v1l-2-2v1 6-3c-1 0-1-1-1-1l-2-4c1-1 1-3 2-4l1-2 2-2 1 1 2-1z" class="C"></path><path d="M483 833v-6c0-2-2-1-1-2v-2l1-1c2 0 2 1 4 2-1 1-2 1-2 1l-1 1h3c1 1 1 1 1 2-1-1-2-1-3-1l-1 1 1 2h1v-2c2 1 3 3 5 4v1c0 1 1 2 1 3l3 6c1 1 1 1 2 1 0 1 1 2 2 2l3 2c1 0 2 1 3 3v2l1 6c1 2 2 3 4 5h0-1c-1-1-3-2-3-3l-2-3c-1-1-2-3-2-4l-1-1c-2-1-3-1-4-3s-5-3-5-5c-1-2-2-5-4-7h-1l-1-2h0l2-2v-1c-2 0-3 0-5 1z" class="E"></path><path d="M471 804h1c1-1 1 0 1-1 2 1 2 2 2 4h0c0 1 0 2 1 3h0v7c0 2 2 3 2 5l-1 1v4h0c-2-2-2-5-4-8h0c-1-1-1-2-2-2h0v1h-1l-2-2c-3-2-8-4-9-7 1-1 1-1 2-1l2-1c1 0 1 0 2 1 1 0 0-1 1 0h1v-2-1l2 1c0-1 0-1 1-2h1z" class="e"></path><path d="M469 812c2 2 3 4 4 7-1-1-1-2-2-2h0v1h-1l-2-2v-2-1l1-1z" class="L"></path><path d="M459 809c1-1 1-1 2-1 3 1 6 1 8 4l-1 1v1 2c-3-2-8-4-9-7z" class="W"></path><path d="M468 814h0c-1 0-2-1-2-2h1l1 1v1z" class="j"></path><path d="M471 804h1c1-1 1 0 1-1 2 1 2 2 2 4h0c0 1 0 2 1 3h0v7c0 2 2 3 2 5l-1 1c-1-2-3-5-3-7-1-4-2-8-4-12h1z" class="F"></path><path d="M479 839l-1-1c-1-3-1-5-2-8-1-1-1-1-1-2v1l2 1 2 6v-1h2c0-1 2-2 2-2 2-1 3-1 5-1v1l-2 2h0l1 2h1c2 2 3 5 4 7 0 2 4 3 5 5s2 2 4 3l1 1-2 1-2-1v3 1c1 2 2 2 1 4v1h-1v-1-1h-1-1c-1-2-2-3-4-5-3-4-8-8-12-12l-2-2 2-1h0l-1-1z" class="I"></path><path d="M480 843h3c1 0 1 0 2 1 2 0 3 3 4 2v3l1 2 1-1v-1-1l1-1c0 1 1 2 1 3 2 0 3 1 3 3l1 1v1l-3-4h-1c-1 1-1 2-1 4-3-4-8-8-12-12z" class="B"></path><path d="M479 839l-1-1c-1-3-1-5-2-8-1-1-1-1-1-2v1l2 1 2 6v-1h2c0-1 2-2 2-2 2-1 3-1 5-1v1l-2 2h0l1 2h1c2 2 3 5 4 7 0 2 4 3 5 5-2-1-5-3-6-4-1-3-4-5-5-8v-1h-1v1 4l4 5h0c-1 1-2-2-4-2-1-1-1-1-2-1h-3l-2-2 2-1h0l-1-1z" class="N"></path><path d="M479 839c1-2 2-3 4-3l1 1h-1c0 3 1 5 2 7-1-1-1-1-2-1h-3l-2-2 2-1h0l-1-1z" class="C"></path><path d="M498 768l7-1-1 1 1 1c3 0 6 0 8 1h0 2c1 1 1 2 2 2l2 6 1 1c0 1 0 1 1 2l1 5c-1 1-1 1-2 1h-1l-2-1h0l-1-1v1c0 1 1 1 1 3-1-1-2-2-3-2 1 3 3 4 6 5l4 1v3l-1 1-1-1h-1-1l-1 1h-2v-3c-1 0-1 0-2-1v2h-1c0-1-1-2-1-3v-1h-1 0c0 2 1 3 1 5 1 2 0 3 0 4s-1 1-1 2v-1-2l-1 1v1 6l-1-1-3-3h-2l-1-1c-1 1-1 2-1 3-1 1-2 3-3 4 0-3 1-6 0-8h0c-1-2-2-2-4-3l-2-1c-1 0-1-1-1-2-2 1-3 3-5 5l1-2 1-1v-2-1l-4 4-1 1c-1 1-1 1-2 1-1 1-2 2-3 4l-1 1c-1 1-1 2-2 3l-1 2h0c-1-1-1-2-1-3h0c0-2 0-3-2-4 0 1 0 0-1 1h-1-1c-1 1-1 1-1 2l-2-1v1 2h-1c-1-1 0 0-1 0-1-1-1-1-2-1l-2 1c-1 0-1 0-2 1h0c-1-2-2-3-2-5l-1-3h1l1-3h0c2-2 4-5 5-7 9-11 23-17 36-20 0-1-1-2-1-3z" class="W"></path><path d="M506 784c0 1 0 1 1 2 0 1 1 1 1 2s-1 1-1 2l-1-1-1-1h-1c1-2 2-3 2-4z" class="I"></path><path d="M480 795l1-1c-1-2-1-2-2-3h-1c0-1 1-2 1-3h0c0-1 1-2 2-2 2-1 3-2 4-3 2 0 3 0 5-1l2-1c1 0 2 0 3-1h0 4l-3 2c0 2-1 2-2 3-2 1-3 2-5 4-1 1-1 2-1 4l-3 2h-1v-1c-2 1-3 1-5 2l1-1z" class="F"></path><path d="M494 785l-1 2h1c1-1 1-2 2-3l2-2c1 1 0 2 0 3l2 1 2-1v-2l1-1c0-1 0-1 1-2v-1c1-1 2-2 4-3l1 1c0 1-1 2-1 3h-1c0 2 0 3-1 4 0 1-1 2-2 4 0 0-1 1-2 1h-3c-1 0-2 0-3 1h-1-2v1c-1 0-2 0-3 1-3 2-5 5-8 7-2 2-3 4-4 6v1c-1 0-1 1-1 2l-1 2h0c-1-1-1-2-1-3h0c0-2 0-3-2-4 0 1 0 0-1 1h-1v-1l4-4c2-1 3-2 4-3 2-1 3-1 5-2v1h1l3-2c0-2 0-3 1-4 2-2 3-3 5-4z" class="L"></path><path d="M478 784c1 1 1 1 0 3h-2v1l1 1v1c-1 0-1 1-1 2 1 2 2 2 4 3l-1 1c-1 1-2 2-4 3l-4 4v1h-1c-1 1-1 1-1 2l-2-1v1 2h-1c-1-1 0 0-1 0-1-1-1-1-2-1l-2 1c-1 0-1 0-2 1h0c-1-2-2-3-2-5l-1-3h1l1-3h0c2-2 4-5 5-7l3 3-1 1 2 5c2-2 2-3 2-5 1-6 6-8 9-11z" class="H"></path><path d="M461 800c0-1 1-2 1-2v-2l1-2 1 1v-1h1v1h0c-1 1-1 2-2 4 1 1 0 1 0 2l2 2c1 0 1-1 2-1v1h1c1 0 2 1 2 1-1 1-1 1-1 2l-2-1v1 2h-1c-1-1 0 0-1 0-1-1-1-1-2-1l-2 1c-1 0-1 0-2 1h0c-1-2-2-3-2-5l-1-3h1c2-1 3-2 4-1z" class="C"></path><path d="M457 801c2-1 3-2 4-1l1 2-2 2h-3l-1-3h1z" class="b"></path><path d="M504 788h1l1 1 1 1v1l3 3c1 2 0 3-1 5l1 2h1v6l-1-1-3-3h-2l-1-1c-1 1-1 2-1 3-1 1-2 3-3 4 0-3 1-6 0-8h0c-1-2-2-2-4-3l-2-1c-1 0-1-1-1-2-2 1-3 3-5 5l1-2 1-1v-2-1l-4 4-1 1c-1 1-1 1-2 1-1 1-2 2-3 4l-1 1c-1 1-1 2-2 3 0-1 0-2 1-2v-1c1-2 2-4 4-6 3-2 5-5 8-7 1-1 2-1 3-1v-1h2 1c1-1 2-1 3-1h3c1 0 2-1 2-1z" class="S"></path><path d="M493 795c2-2 4-1 7-1 1 1 2 2 1 3v1c-1-1-2-2-3-2-2-1-3 0-4 1-1 0-1-1-1-2z" class="E"></path><path d="M494 797c1-1 2-2 4-1 1 0 2 1 3 2l1 1h1l1 1v2c-1 1-1 2-1 3-1 1-2 3-3 4 0-3 1-6 0-8h0c-1-2-2-2-4-3l-2-1z" class="O"></path><path d="M506 789l1 1v1l3 3c1 2 0 3-1 5-1 1-2 0-3 0 0-1-1-2-1-2-1-1-1 0-2-1v-3h1v-2-1h0l2-1z" class="C"></path><path d="M498 768l7-1-1 1 1 1c3 0 6 0 8 1h0 2c1 1 1 2 2 2l2 6 1 1c0 1 0 1 1 2l1 5c-1 1-1 1-2 1h-1l-2-1h0l-1-1v1c0 1 1 1 1 3-1-1-2-2-3-2 1 3 3 4 6 5l4 1v3l-1 1-1-1h-1-1l-1 1h-2v-3c-1 0-1 0-2-1v2h-1c0-1-1-2-1-3v-1h-1l-1-1s-1-1-1-2h0v-3h1l-1-1 1-2c-1-1-1-3-2-5h0c0-1 1-2 1-3h-1c-1-1-3 0-4 0l-1 1c-4-1-8 1-12 3-3 1-5 1-8 2-2 1-4 2-6 4-3 3-8 5-9 11 0 2 0 3-2 5l-2-5 1-1-3-3c9-11 23-17 36-20 0-1-1-2-1-3z" class="j"></path><path d="M498 768l7-1-1 1 1 1c3 0 6 0 8 1h0c-4 0-9 0-14 1 0-1-1-2-1-3z" class="P"></path><path d="M511 790v-5l2 2v-7c1-1 1-2 2-3 0-1-1 0 0-1 2 1 2 4 2 6 0 1 1 2 1 3 1 1 1 1 1 2l-2-1h0l-1-1v1c0 1 1 1 1 3-1-1-2-2-3-2 1 3 3 4 6 5l4 1v3l-1 1-1-1h-1-1l-1 1h-2v-3c-1 0-1 0-2-1v2h-1c0-1-1-2-1-3v-1h-1l-1-1z" class="S"></path><path d="M524 793l-4-1c-3-1-5-2-6-5 1 0 2 1 3 2 3 2 6 2 10 2 2 0 4 0 6-1 1 0 2-1 3-1 1 1 2 3 3 4 4 3 10 6 13 10l1 2c1 1 1 3 1 5v1c0 1-1 2-1 3l-2 3v3l-1 1v3c-1 1-1 1 0 2v1c-1 2-2 4-4 7h0c-1 1-2 2-3 2-3 1-6 4-8 6h0c0 1 0 1-1 2-1 2-2 2-3 4v2c-3 3-5 5-5 9-1 2-3 4-4 6l-6 11v-2c0-1 0 0 1-1 1-3 2-5 3-9 1-1 1-2 1-3h-1v-1h-1l-1-1h-1v1c-1 0-2-1-2-1v-2c1-2 1-4 1-6 1-2 2-3 2-4 1-2 1-2 2-3 0-1 0-2 1-2 1-1 1-2 2-3l-1-1c-3 1-5 4-8 4h0c-2 1-4 1-6 1-2-2-4-3-6-4h0c-2-1-3-2-5-3-1-2-4-4-5-7h1v-1c-2-5-2-8-3-13v-5l2 4s0 1 1 1v3h1l1 1v-3l1-1v2c2 0 1-1 1-2 1 0 1-1 2-1l1-5c1-1 2-3 3-4 0-1 0-2 1-3l1 1h2l3 3 1 1v-6-1l1-1v2 1c0-1 1-1 1-2s1-2 0-4c0-2-1-3-1-5h0 1v1c0 1 1 2 1 3h1v-2c1 1 1 1 2 1v3h2l1-1h1 1l1 1 1-1v-3z" class="D"></path><path d="M539 804c0 3-1 5-1 7l-3 3c1-1 1-2 0-3v-1l3-3v-1l1-2z" class="B"></path><path d="M524 817h1c1 0 1 0 2-1v-2h1c1 0 1 0 2 1-1 3-3 6-6 8v-6z" class="Z"></path><path d="M531 822l3-3-1 2c-2 3-4 5-5 8-2 2-3 4-5 5h-2l-1 1-1-1h1c1-1 4-4 4-5v-1c-1 2-2 2-3 3h-1l3-3 2-2c1 0 1 0 2-1 1-2 2-3 4-4l-4 5 1 1c1-2 2-3 3-5z" class="a"></path><path d="M539 804l2-1v6c-1 1-2 1-2 2 0 3-2 5-4 7l-1 1-3 3c0-1 1-2 1-3-1-1-1-1-1-2l1-2v1c1 0 2-1 3-2h0l3-3c0-2 1-4 1-7h0z" class="C"></path><path d="M519 824l1-2h1v1c1 1 1 1 1 2-1 3 0-1-1 1v1 1-1c2-1 2-2 3-3l1-1v1l-2 4-3 3h1c1-1 2-1 3-3v1c0 1-3 4-4 5h-1l1 1 1-1h2c-3 2-6 3-10 4-4-1-9-2-12-6l1-1c1 2 2 3 4 4h1c1-1 2 0 4 0h0 1c2 0 3-1 4-2l-1-1h1l-1-2c1 0 2-1 3-1v-1c1-2 0-3 1-4z" class="Z"></path><path d="M519 824l1-2h1v1c1 1 1 1 1 2-1 3 0-1-1 1v1 1c-2 2-3 3-5 4l-1-2c1 0 2-1 3-1v-1c1-2 0-3 1-4z" class="I"></path><path d="M519 797l1-1h1 1l1 1 1-1 2 1h0c2-1 4-1 6 0v1c-1 1-1 1-2 3 1 1 1 2 0 4 0 1-1 4-1 6 1 0 1 0 1-1 1-1 1-2 2-3h1c0 1-1 2-2 4-1 1-1 2-1 4-1-1-1-1-2-1h-1v2c-1 1-1 1-2 1h-1v-1l-1 1-1-1-2-5v-1-1h-1c0-2-1-3-2-4l-1-1c1-1 1-1 1-2 1 0 2 0 3 1l-1-6z" class="N"></path><path d="M521 796h1l1 1c1 1 2 1 3 1h1l1 1c0 2 0 4 1 5 0 1 0 1-1 2 0-2-2-4-3-6v-1h-2c-1-1-1-2-2-3z" class="J"></path><path d="M524 796l2 1h0c2-1 4-1 6 0v1c-1 1-1 1-2 3 0 1-1 2-1 3-1-1-1-3-1-5l-1-1h-1c-1 0-2 0-3-1l1-1z" class="C"></path><path d="M516 804c1-1 1-1 1-2 1 0 2 0 3 1l4 13-1 1-1-1-2-5v-1-1h-1c0-2-1-3-2-4l-1-1z" class="K"></path><path d="M524 793l-4-1c-3-1-5-2-6-5 1 0 2 1 3 2 3 2 6 2 10 2 2 0 4 0 6-1 1 0 2-1 3-1 1 1 2 3 3 4 4 3 10 6 13 10l-1 1c1 2 2 3 2 5l-2-1h-1c-1 0-1 0-1 1-1 0-2 1-3 1s-2 0-3-1-1-1-1-2c1-1 1-2 2-3l-2-1h-1l-2 1v-1h-1c-1 1-2 3-3 4v-1l1-2h0-1-1c-1 0-2 1-3 1h-1c1-2 1-3 0-4 1-2 1-2 2-3v-1c-2-1-4-1-6 0h0l-2-1v-3z" class="B"></path><path d="M545 801c2 1 2 2 3 4v1h-1c-2-2-4-3-6-5h4z" class="W"></path><path d="M534 798l-1-1c0-2 0-2 1-3h1l10 7h-4c-1 0-2-1-3-2-1 1-2 1-2 1l-2-2z" class="b"></path><path d="M535 794l2-2c5 4 10 8 14 12 1 2 2 3 2 5l-2-1h-1c-1 0-1 0-1 1-1-1-1-2-2-3h1v-1c-1-2-1-3-3-4l-10-7z" class="F"></path><path d="M524 793l-4-1c-3-1-5-2-6-5 1 0 2 1 3 2 3 2 6 2 10 2 2 0 4 0 6-1 1 0 2-1 3-1 1 1 2 3 3 4 4 3 10 6 13 10l-1 1c-4-4-9-8-14-12l-2 2h-1c-1 1-1 1-1 3l1 1-1 1-1-1v-1c-2-1-4-1-6 0h0l-2-1v-3z" class="e"></path><path d="M524 793c3 0 5 0 8-1h2l2-1 1 1-2 2h-1c-1 1-1 1-1 3l1 1-1 1-1-1v-1c-2-1-4-1-6 0h0l-2-1v-3z" class="W"></path><path d="M512 791h1v1c0 1 1 2 1 3h1v-2c1 1 1 1 2 1v3h2l1 6c-1-1-2-1-3-1 0 1 0 1-1 2l1 1c1 1 2 2 2 4h1v1l-1 1 1 1v3 5c1 1 1 1 1 3v-1h-1l-1 2c-1 1 0 2-1 4v1c-1 0-2 1-3 1l1 2h-1l1 1c-1 1-2 2-4 2h-1 0c-2 0-3-1-4 0h-1c-2-1-3-2-4-4l-1 1-2-1c-2-1-3-3-5-5 0-3-1-5 0-8l1 1v-3l1-1v2c2 0 1-1 1-2 1 0 1-1 2-1l1-5c1-1 2-3 3-4 0-1 0-2 1-3l1 1h2l3 3 1 1v-6-1l1-1v2 1c0-1 1-1 1-2s1-2 0-4c0-2-1-3-1-5h0z" class="B"></path><path d="M515 813v-3h0c1 1 2 3 2 5-1 1-1 2-1 4l1 1c0 2 0 6-1 8v1h2c-1 0-2 1-3 1v-7c0-3 1-7 0-10z" class="H"></path><path d="M517 815c1 3 2 5 2 8v1c-1 1 0 2-1 4v1h-2v-1c1-2 1-6 1-8l-1-1c0-2 0-3 1-4z" class="W"></path><path d="M511 801v-1l1-1v2 1l2 5v4 1l-2-2v2h-1v-1c0-2-1-3-1-5l1 1v-6z" class="L"></path><path d="M512 801v1l2 5v4 1l-2-2c0-2-1-7 0-9z" class="D"></path><path d="M512 820c1 2 1 5 2 7v2h-1c0 1 0 2 1 3l1 1h-1-3v-2-3-3c0-2 0-3 1-5z" class="a"></path><path d="M511 825l-7-22c5 5 7 10 8 17-1 2-1 3-1 5z" class="N"></path><path d="M502 810l1-1v-2h0c1 2 1 3 1 5 2 4 2 10 6 14 0 0 0 1 1 2v3 2c-1 0-2-1-3-1 0-1-2-2-3-3-2-2-1-5-2-7 0-4-1-8-1-12z" class="G"></path><path d="M504 812c2 4 2 10 6 14 0 0 0 1 1 2v3c-2 0-2 0-4-2l-2-11c-1-2-1-4-1-6z" class="I"></path><path d="M512 791h1v1c0 1 1 2 1 3h1v-2c1 1 1 1 2 1v3h2l1 6c-1-1-2-1-3-1 0 1 0 1-1 2l1 1c1 1 2 2 2 4h1v1l-1 1 1 1v3 5c1 1 1 1 1 3v-1h-1l-1 2v-1c0-3-1-5-2-8 0-2-1-4-2-5h0v3l-1-6-2-5c0-1 1-1 1-2s1-2 0-4c0-2-1-3-1-5h0z" class="C"></path><path d="M517 805c1 1 2 2 2 4h1v1l-1 1 1 1v3c-2-3-3-7-3-10z" class="J"></path><path d="M517 794v3h2l1 6c-1-1-2-1-3-1 0 1 0 1-1 2-1-2 0-6 0-9h0l1-1z" class="D"></path><path d="M499 816l2-2c0-2 0-3 1-4 0 4 1 8 1 12 1 2 0 5 2 7 1 1 3 2 3 3 1 0 2 1 3 1h3 1v-1l1 1c-1 1-2 2-4 2h-1 0c-2 0-3-1-4 0h-1c-2-1-3-2-4-4l-1 1-2-1c-2-1-3-3-5-5 0-3-1-5 0-8l1 1v-3l1-1v2c2 0 1-1 1-2 1 0 1-1 2-1v2z" class="F"></path><path d="M507 835l-2-2c-2-2-3-2-3-4l1-1c1 2 2 3 4 4h1c1 0 2 1 3 1h3 1v-1l1 1c-1 1-2 2-4 2h-1 0c-2 0-3-1-4 0z" class="N"></path><path d="M499 817c1 3 0 4 0 7 0-1 0-2 1-2 0 3 0 4 1 7 1 1 1 1 1 2l-1 1-2-1c-2-1-3-3-5-5h4l1-9z" class="C"></path><path d="M494 818l1 1v-3l1-1v2c2 0 1-1 1-2 1 0 1-1 2-1v2 1l-1 9h-4c0-3-1-5 0-8z" class="G"></path><path d="M541 803h1l2 1c-1 1-1 2-2 3 0 1 0 1 1 2s2 1 3 1 2-1 3-1c0-1 0-1 1-1h1l2 1c0-2-1-3-2-5l1-1 1 2c1 1 1 3 1 5v1c0 1-1 2-1 3l-2 3v3l-1 1v3c-1 1-1 1 0 2v1c-1 2-2 4-4 7h0c-1 1-2 2-3 2-3 1-6 4-8 6h0c0 1 0 1-1 2-1 2-2 2-3 4v2c-3 3-5 5-5 9-1 2-3 4-4 6l-6 11v-2c0-1 0 0 1-1 1-3 2-5 3-9 1-1 1-2 1-3h-1v-1h-1l-1-1h-1v1c-1 0-2-1-2-1v-2c1-2 1-4 1-6 1-2 2-3 2-4 1-2 1-2 2-3 0-1 0-2 1-2 1-1 1-2 2-3l-1-1c-3 1-5 4-8 4h0c-2 1-4 1-6 1-2-2-4-3-6-4h0c-2-1-3-2-5-3-1-2-4-4-5-7h1v-1c-2-5-2-8-3-13v-5l2 4s0 1 1 1v3h1c-1 3 0 5 0 8 2 2 3 4 5 5l2 1c3 4 8 5 12 6 4-1 7-2 10-4 2-1 3-3 5-5 1-3 3-5 5-8l1-2 1-1c2-2 4-4 4-7 0-1 1-1 2-2v-6z" class="I"></path><path d="M528 829c1-3 3-5 5-8l1 1h0c0 2-1 3-2 4-1 2-3 3-4 3z" class="C"></path><path d="M541 803h1l2 1c-1 1-1 2-2 3 0 1 0 1 1 2s2 1 3 1 2-1 3-1c0-1 0-1 1-1h1l2 1c0 1 0 2-1 3s-1 2-3 3c-2 0-2 1-3 2-1 0-1 0-2 1h-1c-2 1-3 4-4 6-1 1-2 1-3 2s-1 2-3 2l3-3 5-7v-1h-1 0c-1-1-1-2-1-3 1 0 1 0 2-1 0-1-1-1-2-2 0-1 1-1 2-2v-6z" class="H"></path><path d="M551 808l2 1c0 1 0 2-1 3-1 0-2 0-4-1v-1l3-2z" class="b"></path><path d="M552 803l1 2c1 1 1 3 1 5v1c0 1-1 2-1 3l-2 3v3l-1 1v3c-1 1-1 1 0 2v1c-1 2-2 4-4 7h0c-1 1-2 2-3 2-3 1-6 4-8 6h0l-8 8h-1c3-4 7-7 10-10 2-2 4-3 5-5-1 0-1 1-2 1-3 2-10 8-12 12-1 1-1 2-2 3l-1-1c1-1 0-1 1-2l1-1v-1c1-2 3-3 4-4h0l6-5c3-2 7-5 8-8l1-1 1-3v-1l-1 1h0c0-2 1-4 2-5h1l-2-3c1-1 1-2 3-2 2-1 2-2 3-3s1-2 1-3c0-2-1-3-2-5l1-1z" class="R"></path><path d="M533 828c2 0 2-1 3-2s2-1 3-2c1-2 2-5 4-6h1c1-1 1-1 2-1l2 3h-1c-1 1-2 3-2 5h0l1-1v1l-1 3-1 1c-1 3-5 6-8 8l-6 5h0c-1 1-3 2-4 4v1l-1 1c-1 1 0 1-1 2l-1-1h-1c0 2 0 2 1 3h1v1c-1 2-2 3-3 5h0c-1-1-1-2-3-3l-1-1v-1c0-1 1-1 1-2s0-2 1-3l1-4 2-2 1-1c4-4 7-8 10-13z" class="b"></path><path d="M530 842v-2c0-2 2-4 4-6h0c1 1 2 2 2 3l-6 5h0z" class="H"></path><defs><linearGradient id="AF" x1="360.859" y1="426.729" x2="385.025" y2="496.366" xlink:href="#B"><stop offset="0" stop-color="#181819"></stop><stop offset="1" stop-color="#3e3f41"></stop></linearGradient></defs><path fill="url(#AF)" d="M414 413l6 3c2 2 3 4 4 6l1 2c2 3 3 6 5 9 0 1 0 2-2 3-1 1-3 2-4 3h-1l-2 5-1 1-2-1-4 7-2-1c-1 3-2 5-4 7l-13 16-1 1c-2 3-4 6-7 8 1 2 3 3 3 5h1 1l-2-2c1 0 2 0 3 1 4 3 4 10 5 14-1 8-2 18-7 25v-2c1-1 1-1 1-2-2 0-2 1-4 2l-1-1 1-2h-1c-1 2-1 3-3 5-4 4-7 7-13 8h-1c-4-1-9-1-12-3-1-1-1 0-1-1l2-1-2-2c-1-1-2-2-2-3s0-1-1-2v-2c-1-1-1-2-2-3h0v-3-1c-2-3-2-7-3-11l-7-17h2 1c-1-2 0-4 0-7l-1-1h-1l-1 2-2-1c2-2 5-4 6-7l-1-1-3 1h0 1v2c-2 2-4 3-6 5l-3 3c-2-2-3-3-4-5v-1c-1-2-3-6-2-8v-1-1l-4-12h1v1c1 1 1 1 1 2h1l1-1h0 1c-1-2-1-3-2-4v-4h0c-1-2-2-4-2-6l1-1h2l1 1c4 0 7 1 11 2l3-2h2 3c1 1 0 1 1 0 6 0 13 1 18 0 2 0 3 0 4-1h3 2l7-2 3-1 7-2 6-3h1c2-1 2-3 3-5l1-1v1l1-1h3l2 3h1c1 1 2 1 2 3l1-1 2-2-1-3c-1 0-5-8-7-10l-1-1 2-1h5z"></path><path d="M327 448l2-2c1 1 1 2 1 3l1 1-1 2h0l-2-2-1-2z" class="F"></path><path d="M381 457v-1h-1v-1-1h1c0-1 1-1 1-1h1c1-1 1-1 2-1 1-1 1-2 2-2v-1c0 1 0 1-1 2-1 2-3 2-3 5l-1 1h-1z" class="E"></path><path d="M403 454l-3-3v-1h2c0-1 1-2 1-3s0-2 1-3h1v1c-1 1-1 2-1 3s1 1 2 2c-1 1-2 3-3 4zm-45 2l1-3v1c0 2 1 2 1 3 1-3 0-7 0-10h1c0 1 0 1 1 2v-1-1l1-1 1 3v1c-1 2 0 4-1 5-1-1-1-1-1-2l-1 4-1 1c-1 0-1-1-2-2z" class="L"></path><path d="M339 465h1l3 1h1c0-2 0-3-1-5h2v-3c1 1 1 3 1 5 1 1 1 2 1 3-1 1-1 1-2 1v1h1c1 2 1 2 0 4-1 1-3 2-3 4l-1 2-2-1c2-2 5-4 6-7l-1-1-3 1-1-3c0-1-1-1-2-2z" class="B"></path><path d="M345 477c0-1 1-3 2-4h1c0 1-2 1-1 2h1c1 1 1 2 1 4h0c0 3 0 5-1 8v1c-1-1-1-1-2-3l-1-1c-1-2 0-4 0-7z" class="D"></path><path d="M367 485h0c1 0 2-1 3-1s1-1 2-2v-1l1-1v4c-1 2-2 4-4 5h0l-1 2h-1 0v8h1c1-2 1-2 2-3v1c0 1-1 2-3 3v1l-2-2c2-4-1-11 2-14z" class="B"></path><path d="M355 458l-1 1h-1v2 2c1 1 1 5 0 6-1-1 0-6-1-8-1-1-1-3 0-4h0c0-2 1-2 0-3-1 1-1 2-2 4h0-1v-2l1-1c0-1 0-2 1-2l1-1h1c0-1 0-3 1-5h0l2 4c1 1 2 2 2 5-1-2-1-3-2-4-1 0-1 1-2 2h0c1 2 1 3 1 4z" class="L"></path><path d="M349 479h2l-1-2 1-1v-3c1-1 1-2 1-3 1 1 1 2 0 3v2 1 1 1c1 0 1 0 2 1v2 1c1-1 1-1 1-2l1 1 1-1c1 0 1 1 1 2h0l-1 1-1 1v1s1-1 2-1h0v1c-1 0-1 1-2 2l-1-1h0c1 1 1 1 0 2l-1 2-1-1h0c-2 0-2 4-4 5-1-1-1-1-1-2l1-2c-1-1-1-1-2-1-1-2-1-2-1-4 1 2 1 2 2 3v-1c1-3 1-5 1-8z" class="O"></path><path d="M331 450l2 2h0c0-1 1-1 1-2 2 2 3 6 4 9h0l2 3-2 2 1 1c1 1 2 1 2 2l1 3h0c-1 0-2 0-3 1-1 0-1 0-2-1-2-1-3-5-4-7l-5-13 2 2h0l1-2z" class="C"></path><path d="M337 462l-1-3h0 2l2 3-2 2 1 1c1 1 2 1 2 2l1 3h0c-1 0-2 0-3 1-1 0-1 0-2-1h0v-4l-1-1c0-1 0-2 1-3z" class="D"></path><path d="M337 470h0v-4l-1-1c0-1 0-2 1-3l2 6h0v3c-1 0-1 0-2-1z" class="F"></path><path d="M355 466v-5l1-1v-3c4 2 1 6 3 9 1-2 0-3 1-5 1 1 2 1 3 1h1v7l1 1c1 0 2-1 3-2l1 1c0 1 0 2-1 3l-1 1c0 1 0 1-1 2l-2-1c0 2 0 3 1 5l-1 2c-1-1-1-2-2-3h1 0c0-2 0-2-1-3l1-1c0-2 0-2-1-3h-1v1l-1 1h-1v-1c0-1 1-2 2-2h-1c-1 0-2 1-2 1-1 1 0 2-1 3l-2-1c-1 1-1 2-2 3-1-1 1-4 0-5v-1l2-1c-1-1-1-1 0-2l-1-1h1z" class="D"></path><path d="M382 457c0 2 0 3-1 4l1 1v-1l1-1v-1c1-1 1-1 2-1v-2l2-2c1 0 1 0 1 1v1l-3 5h0l-1-1c-2 4-4 7-7 10-1 2-1 3-3 5h0 1 1l-2 1c1 1 1 1 1 2s-1 1-2 2l-1 1v1c-1 1-1 2-2 2s-2 1-3 1h0 0c-1-2-1-3-1-4s0-2-1-2c-1-2-1-3-1-5l2 1v2h1 1 3v-1c1-2 0 0 0-2 1-2 3-2 4-4v-1h1l-1 3h1v-1-2c1-1 2-1 2-2h-1c1-2 1-3 2-4h-1l-2 1h0l1-1c0-1 0-1 1-2 1-2 2-3 3-4h1z" class="G"></path><path d="M364 450c0 2 0 3 1 5 2-1 2 0 3-1h1c1-1 1-1 2-1s1 0 2 1h1v1c-2 0-2-1-3-1l-2 1h0c1 2 1 3 1 4h1l1 2h-1v2c1 0 1-1 2-1v1l-3 3h-2 0v2c-1 1-2 2-3 2l-1-1v-7h-1c-1 0-2 0-3-1-1 2 0 3-1 5-2-3 1-7-3-9v3l-1 1v5c0-2-1-3-1-4l1-1c-1-1-1-2 0-3 0-1 0-2-1-4h0c1-1 1-2 2-2 1 1 1 2 2 4v1-1c1 1 1 2 2 2l1-1 1-4c0 1 0 1 1 2 1-1 0-3 1-5z" class="O"></path><path d="M364 462c2-1 3-1 4-1s2-1 2-1l1 1-1 2c-1 1-1 1 0 3h-2 0v2c-1 1-2 2-3 2l-1-1v-7z" class="K"></path><path d="M327 446v2l1 2 5 13c1 2 2 6 4 7 1 1 1 1 2 1 1-1 2-1 3-1h1v2c-2 2-4 3-6 5l-3 3c-2-2-3-3-4-5v-1c-1-2-3-6-2-8v-1-1l-4-12h1v1c1 1 1 1 1 2h1l1-1h0 1c-1-2-1-3-2-4v-4z" class="Z"></path><path d="M328 464l1 1 3 8c0 2 1 4 2 6 1-1 1-2 1-3 1 0 1 0 2 1l-3 3c-2-2-3-3-4-5v-1c-1-2-3-6-2-8v-1-1z" class="I"></path><path d="M327 446v2l1 2 5 13c1 2 2 6 4 7 1 1 1 1 2 1 1-1 2-1 3-1h1v2c-3 1-4 2-6 4-5-5-6-15-8-22-1-2-1-3-2-4v-4z" class="X"></path><path d="M414 413l6 3c2 2 3 4 4 6l1 2c2 3 3 6 5 9 0 1 0 2-2 3-1 1-3 2-4 3h-1l-2 5-1 1-2-1-4 7-2-1c-1 3-2 5-4 7l-13 16-1 1c-2 3-4 6-7 8 1 2 3 3 3 5h1 1l-2-2c1 0 2 0 3 1 4 3 4 10 5 14-1 8-2 18-7 25v-2c1-1 1-1 1-2-2 0-2 1-4 2l-1-1 1-2h-1c-1 2-1 3-3 5-4 4-7 7-13 8h-1c-4-1-9-1-12-3-1-1-1 0-1-1l2-1-2-2c-1-1-2-2-2-3s0-1-1-2v-2c-1-1-1-2-2-3h0v-3-1c-2-3-2-7-3-11h0l2 6 1 1c0-1 0-1 1-2h1v-1c1 0 1-1 2-1h1s0-1 2-1h0c1-1 1-1 2-1h1c1 0 3-1 5-1v-1c2-1 3-2 3-3 4-5 7-11 11-15 6-10 14-19 22-28 1-1 2-3 3-4-1-1-2-1-2-2s0-2 1-3v-1-3l-1-1v1h-1c0-1 1-1 1-2-1-2-1-3-1-6h0 0c-1 0-1 0-2 1-1 0-2 0-3-1-2 0-3 0-4 1h-1 0l6-3h1c2-1 2-3 3-5l1-1v1l1-1h3l2 3h1c1 1 2 1 2 3l1-1 2-2-1-3c-1 0-5-8-7-10l-1-1 2-1h5z" class="Y"></path><path d="M413 435l1-2h1c1 1 1 2 1 3v1l-2 8-1-1c0-2 1-5 1-7 0-1-1-2-1-2z" class="V"></path><path d="M413 444l1 1-2 5c-1 3-2 5-4 7 0-1 1-3 0-4 1-3 3-6 5-9z" class="K"></path><path d="M414 445l2-8c0 2 0 5 1 7h1l-4 7-2-1 2-5z" class="P"></path><path d="M390 503h1 1v1c-1 5-2 9-4 13v-1c-1-2-1-5-3-7h1 2l1-1h0c1-2 1-4 1-5z" class="C"></path><path d="M367 502c1 0 2 0 3 1h2c2 0 3 1 5 2l1 1h-1c-1 0 0 0-1 1h-1c-2-1-4-1-6-1h-4c1 0 2-1 3-2h-3l2-2z" class="T"></path><path d="M407 434c1 2 2 3 3 4v4c-1 3-2 5-4 8-1-1-2-1-2-2s0-2 1-3v-1-3c0-2 0-2 1-4 0-1 1-2 1-3z" class="H"></path><path d="M407 434c1 2 2 3 3 4v4l-4-5c0-1 1-2 1-3z" class="d"></path><path d="M373 521l-1-1h2c1-1 1-3 2-4s3-1 4-3h1v2h1v-1c0-2 0-3-1-4 0-1-1-1-1-2 1 1 2 1 3 2h1v2l-1 1c0 2 1 4 0 5s-1 2-2 3v1l-3 1h-1c1-2 1-2 1-4l-2 1s-1 0-2 1h-1z" class="Z"></path><path d="M392 487l-2-2c1 0 2 0 3 1 4 3 4 10 5 14-1 8-2 18-7 25v-2c1-1 1-1 1-2-2 0-2 1-4 2l3-6c4-7 7-16 5-24h0c-1-3-2-4-4-6z" class="E"></path><path d="M388 475h1 2c2 0 2-1 3-1-2 3-4 6-7 8-6 4-8 15-14 17l8-12c2-4 4-8 7-12z" class="J"></path><path d="M369 506c2 0 4 0 6 1l1 2s-1 1-1 2 0 2-1 3l-2 2c-1 1 0 2-2 2-1 0-2 0-3-1v-3c0-1-1-5 0-6 0 0 2-1 2-2z" class="g"></path><path d="M370 509c1 0 1-1 2 0l1 1s1 2 1 3h-1l-1 2c0-3-1-4-2-6z" class="X"></path><path d="M370 509c1 2 2 3 2 6v1h-2c-1-1-2-2-2-4 0-1 1-2 2-3z" class="d"></path><path d="M404 425v1c4 3 6 5 6 10v2c-1-1-2-2-3-4 0 1-1 2-1 3-1 2-1 2-1 4l-1-1v1h-1c0-1 1-1 1-2-1-2-1-3-1-6h0 0c-1 0-1 0-2 1-1 0-2 0-3-1-2 0-3 0-4 1h-1 0l6-3h1c2-1 2-3 3-5l1-1z"></path><path d="M404 440c0-2-1-5 1-7 1 0 1 1 2 1 0 1-1 2-1 3-1 2-1 2-1 4l-1-1z" class="f"></path><path d="M408 453c1 1 0 3 0 4l-13 16-1 1c-1 0-1 1-3 1h-2-1l20-22z" class="O"></path><path d="M387 482c1 2 3 3 3 5h1c1 2 2 3 2 6v7c0 1 0 3-1 4v-1h-1-1c0 1 0 3-1 5h0l-1 1h-2-1v-1c-3-3-6-6-11-8-1 0-1 0-2-1h1c6-2 8-13 14-17z" class="F"></path><path d="M390 501h0c1-2 2-1 3-1 0 1 0 3-1 4v-1h-1-1v-2zm0-14h1c1 2 2 3 2 6-1-1-1-1-2-1-2-1-2-1-3-2h1l1-3z" class="B"></path><path d="M389 495v-1c1 1 0 3-1 5l-2 1c1 0 3-1 4-1 1-1 1-2 1-3v3c-2 1-3 1-4 2-1 0-1 1-2 2h1l1 1v-1l2-2h1v2c0 1 0 3-1 5h0l-1 1h-2-1v-1c-3-3-6-6-11-8 2-1 2-1 4-1 2-2 3-4 5-5h1 2v1h3z" class="S"></path><path d="M386 509c0-1 0-2 1-3l2 2-1 1h-2z" class="j"></path><path d="M374 500c2-1 2-1 4-1 2-2 3-4 5-5h1 2v1h3l-3 2s-1 1-2 1l-2 2c-1 0-2 1-2 2l5 3c0 1 0 1 1 2-1 0-1 1-1 1-3-3-6-6-11-8z" class="L"></path><path d="M414 413l6 3c2 2 3 4 4 6l1 2c2 3 3 6 5 9 0 1 0 2-2 3-1 1-3 2-4 3h-1l-2 5-1 1-2-1h-1c-1-2-1-5-1-7v-1c0-1 0-2-1-3h-1l-1 2-1-1c0-2 1-2 1-3l1-1 2-2-1-3c-1 0-5-8-7-10l-1-1 2-1h5z" class="Q"></path><path d="M420 416c2 2 3 4 4 6l1 2-3 1v-1c-2-2-3-4-5-5v-1-1-1l2 1 1-1z" class="h"></path><path d="M425 424c2 3 3 6 5 9 0 1 0 2-2 3-2-3-4-8-6-11l3-1z" class="X"></path><path d="M414 413l6 3-1 1-2-1v1 1 1h-2l1 1v1c2 2 3 4 4 6l-1 2-2-1h0c0-1 1-1 0-2s-1-1-2-1-5-8-7-10l-1-1 2-1h5z" class="d"></path><path d="M414 413l6 3-1 1-2-1v1 1 1h-2l1 1v1c-2 0-2-1-3-2 0-2 1-2 2-4h-2l-1 2c-1-1-2-2-3-4h5zm1 12c1 0 1 0 2 1s0 1 0 2h0l2 1 1-2 2 5s1 1 1 2-1 4 0 5l-2 5-1 1-2-1h-1c-1-2-1-5-1-7v-1c0-1 0-2-1-3h-1l-1 2-1-1c0-2 1-2 1-3l1-1 2-2-1-3z" class="X"></path><path d="M417 428l2 1c0 2 0 4-1 5h-1v-6z" class="g"></path><path d="M416 428c1 3 1 5 0 8 0-1 0-2-1-3h-1l-1 2-1-1c0-2 1-2 1-3l1-1 2-2z" class="i"></path><path d="M422 432s1 1 1 2-1 4 0 5l-2 5-1 1-2-1c2-4 3-8 4-12z" class="a"></path><path d="M349 501h0l2 6 1 1c0-1 0-1 1-2h1v-1c1 0 1-1 2-1h1s0-1 2-1h0c1-1 1-1 2-1h1c-3 2-5 3-7 6 1-1 5-4 6-4 2-1 3-2 6-2l-2 2h3c-1 1-2 2-3 2 0 1-1 2-1 2-1 2-1 6-1 8 1 2 2 5 4 6 2 0 4 0 6-1h1c1-1 2-1 2-1l2-1c0 2 0 2-1 4h1l3-1v-1c1-1 1-2 2-3s0-3 0-5l1-1c2 4 2 8 0 12v1c-4 4-7 7-13 8h-1c-4-1-9-1-12-3-1-1-1 0-1-1l2-1-2-2c-1-1-2-2-2-3s0-1-1-2v-2c-1-1-1-2-2-3h0v-3-1c-2-3-2-7-3-11z" class="U"></path><path d="M355 508c1-1 5-4 6-4l3 1c-1 1-1 1-3 2-1 0-2 2-3 3h-1c-1 0-2 1-3 1l-1-1c1-1 1-2 2-2z" class="E"></path><path d="M361 526s-4-6-4-7l-1-1c0-1 0-1-1-2l2-5 2 7v1c0 1 0 1 1 2h0c1 2 2 4 4 4h1 1c3 1 7 0 9 1 1 0 2 1 2 1l-1 2c-2 1-5 2-8 1h-1c-3-1-4-2-6-4z" class="B"></path><path d="M349 501h0l2 6 1 1c0-1 0-1 1-2h1v-1c1 0 1-1 2-1h1s0-1 2-1h0c1-1 1-1 2-1h1c-3 2-5 3-7 6-1 0-1 1-2 2l1 1c1 0 2-1 3-1h1l-1 1-2 5c1 1 1 1 1 2l1 1c0 1 4 7 4 7v1l1 2v1l-3-2-2-2c-1-1-2-2-2-3s0-1-1-2v-2c-1-1-1-2-2-3h0v-3-1c-2-3-2-7-3-11z" class="H"></path><defs><linearGradient id="AG" x1="370.109" y1="533.325" x2="374.879" y2="517.713" xlink:href="#B"><stop offset="0" stop-color="#070606"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#AG)" d="M384 512c2 4 2 8 0 12v1c-4 4-7 7-13 8h-1c-4-1-9-1-12-3-1-1-1 0-1-1l2-1 3 2v-1l-1-2v-1c2 2 3 3 6 4h1c3 1 6 0 8-1l1-2c2-1 4-3 4-6 1-1 1-2 2-3s0-3 0-5l1-1z"></path><path d="M319 162h48c6 0 13-1 20 0h6l1 4c1 3 3 4 5 6h0l1 2c-3 3-5 7-7 11-1 4-2 9-3 13-1 2-3 5-4 7l-2-6-1 1c-1-2-2-4-5-5-5-2-9-1-14 2-9 5-14 13-17 23l-5 22c0 4 0 7 1 10l-2 2c-1-1-1-1-1-2l-2 1-2-1 1 1c0 1 0 1 1 2-1 0-2 0-2 1l-1-1h0-1l-1 1c-1 0-1 0-2 1h0-1c0-1 1-2 1-2-4 0-1-2-3-4l-1 1 1 1v3h-1-1c-2-1-3-2-4-4-2 2-2 3-2 5 2 1 3 0 4 1 0 1 1 1 1 1l1 1h-2l-1 1c0 1-1 2-2 2h-1-2v1l-1-1c-1 0-1 0-2-1l-2 1 1 1c-3-1-4-3-7-4l-1-1v1c-1-3-4-5-7-6h0l-1-1c-1 0-1 0-2-1h0c-1 0-1-1-2-1-1-1-2-1-3-2l-2-1h-1c-1-1-2-1-3-2h0l1-1v-1h0c9 4 17 10 26 15 1 1 6 3 7 3-2-3-9-6-12-8-3-1-5-3-7-4l-4-2-8-5h-1l-1-1c-3-2-6-3-8-5v-1-1-1c-1-1-1-2-2-2l-1-1h0 1 1v-2l-1-1c1-2 2-4 4-5 2-3 4-5 8-7 4-1 8-1 12-1l-1-1c1-1 0-1 1-2h0l1-1c1 0 1-1 2-1 0-1 1-2 1-2l-1-2 1-1v-1h-1l-1-1h2c-1-1-1-3-1-4l4-1-1-2h0l1 2 2 2 1-1-3-3c-1-3 0-7 1-10l-1 1-1 1-1-1 2-3v-1c1-1 1-1 2-3 1 0 1-1 2-1 0-1 1-2 1-2l3-1v-2c3-1 8 0 11 0 2-1 3-1 5-1h1v-1h-5-2c-1-1-1-1-1-2s-1-2-2-3l1-1 2 2 3 3v-1l-4-4 1-1c1 1 2 1 2 2l1 1v-2l1-1v-1c1 0 1 0 2-1h1c-4-1-8 0-12-1h-1z" class="E"></path><path d="M369 178v2h1c0 2 0 2-1 4h-1c0-2 0-2 1-3v-3z" class="C"></path><path d="M386 192l1 1h0c-1 2 0 1 1 3h-2v-1c0-1-2-2-3-3h-1l-1-1c0-1 1-2 1-2v-2c-1-2-2-1-4-2-2 0-3 0-4-2 0-1 1-1 2-1 2-1 4 1 5 2h2l1 1v1c-1 1-1 3-1 4 1 1 2 1 3 2z" class="I"></path><defs><linearGradient id="AH" x1="348.278" y1="184.956" x2="360.998" y2="164.405" xlink:href="#B"><stop offset="0" stop-color="#939596"></stop><stop offset="1" stop-color="#b5b2b3"></stop></linearGradient></defs><path fill="url(#AH)" d="M333 174h24c7 0 14-2 21-1 1 1 2 1 4 2 2 2 2 5 3 8v1h1 1l1 1v1h-1c0 1 0 0 1 1h1c1 2 1 2 1 4h-1c-1-1-2-1-3-2v-1l-1-1h0c0 1-1 1-1 2l2 3c-1-1-2-1-3-2 0-1 0-3 1-4v-1l-1-1v-2c-1-2-4-4-7-4v-1c-6-1-12-1-17 0l-28-1h-4l-1-1h2c2-1 3-1 5-1z"></path><path d="M340 240c0-9 3-20 6-28 4-8 9-15 18-19 4-2 10-2 15 0 2 1 4 3 5 6l-1 1c-1-2-2-4-5-5-5-2-9-1-14 2-9 5-14 13-17 23l-5 22c0 4 0 7 1 10l-2 2c-1-1-1-1-1-2 0-4-1-8 0-12z" class="Y"></path><path d="M340 240c0 2 1 5 1 7 1-1 0 0 0-1 1-2 1-3 1-4 0 4 0 7 1 10l-2 2c-1-1-1-1-1-2 0-4-1-8 0-12z" class="k"></path><path d="M347 187c0 1 0 1 1 2l1-1-1-1 1-1h0v2c2-1 4-4 5-6l-1-1h1 2v1c1 1 1 1 1 2v1c1 0 1 0 2-1l2-1v1l1 1 2-3v1 1l1-1 1 1c-1 1-3 3-4 5h-1v-1c-1-1-1-1-1-2-1 1-2 2-2 3h0v1c-1 0-2 0-3-1-1 1-2 1-3 2 0 2 0 3 1 4h1 0c2-1 3-2 4-3v1c-2 1-4 3-6 5-2 3-4 5-8 7-1 0-2 0-4-1l-1-2v-1c2 1 3 2 4 3l5-2c-1-1-1-2 0-3l-2-2h0v-1l1-1h-1l2-2h-1c-3 1-5 1-7 3v1c-2 1-2 3-3 5-1 5-5 12-9 14-3 2-6 2-10 2-6-1-12-2-19-2-4 0-7 1-10 2-4 1-5 2-7 5h0c-3 3-2 6-2 10 0 1 1 3 2 4h1v-1l1 1c0 1-1 1-1 1-2-1-3-2-4-4v3l-1-1v-1-1c-1-1-1-2-2-2l-1-1h0 1 1v-2l-1-1c1-2 2-4 4-5 2-3 4-5 8-7 4-1 8-1 12-1 8 0 19 2 26-1 7-2 9-8 11-14 1-3 2-5 5-6 1-1 4-2 5-4-2 1-4 2-6 2v-2-1h1v2h1v-1c3 0 3-2 4-3z" class="P"></path><path d="M348 193h1l3 3c0 3-3 5-4 6-1-1-1-2 0-3l-2-2h0v-1l1-1h-1l2-2z" class="C"></path><path d="M289 218c0 1 1 1 2 2h0c1-1 1 0 1 0l1 1c1-1 2-1 4-2h0l-1 2c1 0 1 1 2 1h1l-1-1 1-1c1 1 2 2 3 2 0 2 0 2-1 3h1 1 1 2l1 1h-1l-1 1c-1 0-3-1-3-1-1 1-2 1-2 3h1c2 1 3 1 4 2v1h-1l-1-1v2c-1-1-1-1-2 0-1 0-2 1-2 2h-1c-2 0-3 0-4 1h-1c-1 0-2 1-2 1-2 1-1 0-2 2 0 1-1 1-1 2v2h-1l-1-1c-3-2-6-3-8-5v-1l1 1v-3c1 2 2 3 4 4 0 0 1 0 1-1l-1-1v1h-1c-1-1-2-3-2-4 0-4-1-7 2-10h0c2-3 3-4 7-5z" class="O"></path><path d="M319 162h48c6 0 13-1 20 0h6l1 4c1 3 3 4 5 6-1 1-2 2-2 3l-1 1-1 1v1l-1 1-1 1v-1l1-1c-2-1-3 2-3 2l-2-2-1 1 2 2-1 1-2-1c-1 0-1 0-1 1l-1 1c-1-3-1-6-3-8-2-1-3-1-4-2-7-1-14 1-21 1h-24c-2-1-5 0-7 0 2-1 3-1 5-1h1v-1h-5-2c-1-1-1-1-1-2s-1-2-2-3l1-1 2 2 3 3v-1l-4-4 1-1c1 1 2 1 2 2l1 1v-2l1-1v-1c1 0 1 0 2-1h1c-4-1-8 0-12-1h-1z" class="D"></path><path d="M367 162c6 0 13-1 20 0l-4 1 1 1v1h-2v3c-2 2-3 4-7 4-1-1-2-1-2-2-1 0-2 1-2 1h-1v-1-2h1l-2-2h-2v-4z" class="K"></path><path d="M319 162h48v4h2l2 2h-1c-1 2-1 3-2 4h-3l1-2h-1c-1 0-2 0-4-1h0c-1 1-1 1-1 2l1-1c1 0 2 1 2 2h0c-2 0-4 0-5-1l-1-1v1l-1 1h-1-1c-1 0-1 0-2-1l-2-2-1 1h1l-1 1-1-1c-1 0-1 0-2 1-1 0-1 0-2-1-1 0-1 1-2 1h0-1l1-1v-1l1-2h0-1v1c-1 1-3 2-5 4-1 0-2 0-3-1h0c1 0 1 0 2-1h1c1 0 1-1 2-1l-1-1c0-1-1-2-2-3l-2 1h0l-1-1c1 0 1 0 1-1h-1c-1 0-2 1-3 2l-1-1v-1c1 0 1 0 2-1h1c-4-1-8 0-12-1h-1z" class="J"></path><path d="M357 170c-1-1-2-1-2-3l2-1 2 2v-1c-1-1-1-2-3-2 0 1-1 1-2 2l-1-1 1-1c1-1 2-1 3-1v1l5 2-1-1v-1h1c1 0 2 1 3 1l1-1v1h1 2l2 2h-1c-1 2-1 3-2 4h-3l1-2h-1c-1 0-2 0-4-1h0c-1 1-1 1-1 2l1-1c1 0 2 1 2 2h0c-2 0-4 0-5-1l-1-1z" class="e"></path><path d="M302 222l1 1c0-1-1-2-2-3h1 1 1v2c1-1 0-2 2-2v1l1 1h1l-1-2h1l1 1c1-1 1 0 2-1 1 0 1 1 2 2h1v-2h0l2 1c0 2 0 3-1 4v3c-1 1-2 2-1 4v-1l2-1 2-1v-1h1l2-2h1l1 1-1 1h1l2-3c0 1 0 1 1 2v-5h-1l1-1h1c1 2 1 4 1 6l-1 1v1h1c1 0 2 0 3 1h1c1 0 1 0 3 1h0c1 0 1 0 2 1v-1l1-1c1 2 0 4 0 6-1 0-1 0-2 1h-2c0 1 1 2 1 2s-1 1-1 2 1 0 0 2c2 0 2-1 3 0 1 0 1 1 1 3l-1 2c1 1 1 2 1 4v1l-2-1 1 1c0 1 0 1 1 2-1 0-2 0-2 1l-1-1h0-1l-1 1c-1 0-1 0-2 1h0-1c0-1 1-2 1-2-4 0-1-2-3-4l-1 1 1 1v3h-1-1c-2-1-3-2-4-4-2 2-2 3-2 5 2 1 3 0 4 1 0 1 1 1 1 1l1 1h-2l-1 1c0 1-1 2-2 2h-1-2v1l-1-1c-1 0-1 0-2-1l-2 1 1 1c-3-1-4-3-7-4l-1-1v1c-1-3-4-5-7-6h0l-1-1c-1 0-1 0-2-1h0c-1 0-1-1-2-1-1-1-2-1-3-2l-2-1h-1c-1-1-2-1-3-2h0l1-1v-1h0c9 4 17 10 26 15 1 1 6 3 7 3-2-3-9-6-12-8-3-1-5-3-7-4l-4-2-8-5v-2c0-1 1-1 1-2 1-2 0-1 2-2 0 0 1-1 2-1h1c1-1 2-1 4-1h1c0-1 1-2 2-2 1-1 1-1 2 0v-2l1 1h1v-1c-1-1-2-1-4-2h-1c0-2 1-2 2-3 0 0 2 1 3 1l1-1h1l-1-1h-2-1-1-1c1-1 1-1 1-3z" class="G"></path><path d="M332 230c1 0 1 0 3 1h0c1 0 1 0 2 1v-1l1-1c1 2 0 4 0 6-1 0-1 0-2 1h-2c0 1 1 2 1 2s-1 1-1 2 1 0 0 2c2 0 2-1 3 0 1 0 1 1 1 3l-1 2c1 1 1 2 1 4v1l-2-1 1 1c0 1 0 1 1 2-1 0-2 0-2 1l-1-1h0-1l-1 1c-1 0-1 0-2 1h0-1c0-1 1-2 1-2-4 0-1-2-3-4l-1 1 1 1v3h-1-1c-2-1-3-2-4-4l1-1h0v-1l1-1v-1c-1 0-1-1-2-1 0 1-1 2-1 2l-1 1v1l-1-1 1-1v-3l-1 1-1 2c-1 1-2 3-3 4-2 0-4-1-4-1-2-1-2-2-3-3h0v-5c0-1 1-1 1-2v-1c3 0 3-4 5-5s1 0 2-1 2-2 4-3h2c1-1 1-1 2-1 1-1 3 0 5 0h1c1 0 1 0 2-1z" class="B"></path><path d="M326 174c2 0 5-1 7 0-2 0-3 0-5 1h-2l1 1h4l28 1 2 1-1 1v3l-1 2c-1 1-1 1-2 1v-1c0-1 0-1-1-2v-1h-2-1l1 1c-1 2-3 5-5 6v-2h0l-1 1 1 1-1 1c-1-1-1-1-1-2-1 1-1 3-4 3v1h-1v-2h-1v1 2c2 0 4-1 6-2-1 2-4 3-5 4-3 1-4 3-5 6-2 6-4 12-11 14-7 3-18 1-26 1l-1-1c1-1 0-1 1-2h0l1-1c1 0 1-1 2-1 0-1 1-2 1-2l-1-2 1-1v-1h-1l-1-1h2c-1-1-1-3-1-4l4-1-1-2h0l1 2 2 2 1-1-3-3c-1-3 0-7 1-10l-1 1-1 1-1-1 2-3v-1c1-1 1-1 2-3 1 0 1-1 2-1 0-1 1-2 1-2l3-1v-2c3-1 8 0 11 0z" class="E"></path><path d="M303 199l4-1-1-2h0l1 2 2 2 1-1 3 3 1 2-1 1c1-1 2-1 2-2l2 2v-1h0l1-1 1 1h1l-1-2h1l1 1c1 0 2 1 2 2h-1 0 0c1 1 2 2 4 3l1-1v1l-3 3h-1v1h2c1-1 1-2 3-1h0c-2 2-4 2-6 3h-6-1c-1 0-6 1-7 0l-1-1c-1-1-1-1-2-1l-1 1v1c-2-1-2-2-3-3 1 0 1-1 2-1 0-1 1-2 1-2l-1-2 1-1v-1h-1l-1-1h2c-1-1-1-3-1-4z" class="J"></path><path d="M303 199l4-1-1-2h0l1 2 2 2 1-1 3 3c-1 1 0 1-1 1h-2c0-2 0-2-1-2h-1l1 1v1l-1-1c-1 0-2 0-3 1h-1c-1-1-1-3-1-4z" class="L"></path><path d="M326 174c2 0 5-1 7 0-2 0-3 0-5 1h-2l1 1h4l28 1 2 1-1 1v3l-1 2c-1 1-1 1-2 1v-1c0-1 0-1-1-2v-1h-2-1l1 1c-1 2-3 5-5 6v-2h0l-1 1 1 1-1 1c-1-1-1-1-1-2s-1-2-1-2v-1h-1v-1h-1v1c-2-1-2-1-2-2l-1 2h0-1l-1-2v-1c-1-1-2-1-2-1h-1-2 0-1l1 2v2c-2 0-2 2-3 2h-1l-1-1v-3l-1-1v-1c-1 0-2 1-2 1-2-1-3-1-5-1v1l-1-1h-1l-1 1 2 1h0c0 2 1 2 1 3 1 2 1 3 3 4-1 1-1 2-2 3h0c-1 1-1 2-3 2-1 0-1 0-1-1-1 1-1 1 0 2h-1c-1 1-1 1-1 2l-1 1c1 1 1 1 2 1h1l1 1c0 1 1 1 2 1v2l-1-1h-1l1 2h-1l-1-1-1 1h0v1l-2-2c0 1-1 1-2 2l1-1-1-2-3-3-3-3c-1-3 0-7 1-10l-1 1-1 1-1-1 2-3v-1c1-1 1-1 2-3 1 0 1-1 2-1 0-1 1-2 1-2l3-1v-2c3-1 8 0 11 0z" class="F"></path><path d="M326 174c2 0 5-1 7 0-2 0-3 0-5 1h-2l1 1h4c-6 0-14-1-19 4-2 2-3 4-4 6l-1 1-1 1-1-1 2-3v-1c1-1 1-1 2-3 1 0 1-1 2-1 0-1 1-2 1-2l3-1v-2c3-1 8 0 11 0z" class="N"></path><path d="M315 198c-2-1-3-2-4-4 0-1 0-3-1-4 0 0 0-1 1-1h2v-1c-1 0-1 0-2-1 1 0 1-1 1-2 0 0 1-1 1-2 2-1 3-1 6-1 0 1 1 2 2 3h0c1 2 1 3 3 4-1 1-1 2-2 3h0c-1 1-1 2-3 2-1 0-1 0-1-1-1 1-1 1 0 2h-1c-1 1-1 1-1 2l-1 1z" class="B"></path><defs><linearGradient id="AI" x1="402.81" y1="529.012" x2="419.601" y2="573.366" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#353536"></stop></linearGradient></defs><path fill="url(#AI)" d="M443 533c0-1 1-1 2-2 0-1 1-1 2-1v1l1 1 5 14 2 5c-1 2-2 2-4 3l1 1 1-1c2 0 4 0 5 1 1 2 2 4 2 6 4 8 7 16 10 25l6 13c3 7 5 14 10 20-3-2-5-3-8-4l-10-2c-10 0-20 2-29 2-7 1-14 1-20 0 0-1-2-3-2-4l-1-2c-2 1-3 2-5 2v1c0 1-1 1-1 2h0-1l-10-2-5-2 2-2-2-3c-1 0-2 0-2-1-1 0-1-1-1-1l-1-1-3-3-2-2c-1 0-1-1-2-2l-1-1c-1-2-1-2-1-4h-1v-1-2c-1-2-2-4-2-6v-1l-2-5v-1c-2-5-3-13-7-19 1-1 0-2 0-3-1-1-1-2-1-3l1-2v-2c1-1 0-2 0-3h1 0c3 1 7 3 9 2l1 1c2 1 7 3 9 2 13 2 25 0 37-5 5-1 10-5 14-9h3z"></path><path d="M443 533c0-1 1-1 2-2 0-1 1-1 2-1v1 3-1l-3 2c-1 0-1 0-2-1l1-1z" class="j"></path><path d="M420 570c2 1 3 1 4 2h0c1 0 2 1 2 2l-1 1v4c1 0 2 1 3 1h0c-1 1-1 1-1 2l-7-2-1-1h-3c-2-1-4-1-6-1h0 1v-1-3h1 7 0c1-1 0-1 1-1v-1l-1 1-1-1h1l1-1v-1z" class="J"></path><path d="M420 570c2 1 3 1 4 2h0c1 0 2 1 2 2l-1 1h-2c-1 0 0 0-1 1 0 1-1 1-1 2h-1l-1-4h0c1-1 0-1 1-1v-1l-1 1-1-1h1l1-1v-1z" class="E"></path><path d="M417 550c1-1 2-1 3-2h1l1-1h3l2-1c2-1 3-2 5-2-1 1-1 3-3 5h1l1 1 1-2c0 2 0 3 1 4v1c1 2 3 5 3 7v3c0 1 1 2 1 4v3c-1-1-2-2-2-3s1-2 1-2l-3-3-3-6v-1l-1-1v-2c-2-2-4-2-6-2-2-1-4 0-6 0z" class="H"></path><path d="M414 559l3-3v2c0 1 1 2 1 3v1h3c1 0 1 1 2 2v-1c-1-1-1-2-1-3l1 1 2 2c-1 1-1 1 0 3v2l-1-1-2 2c1 1 2 1 2 2v1h0c-1-1-2-1-4-2h-6c-1-1-2-2-2-3h-1c0-1 0-1-1-2 0-1 0-2 1-3h2c2 2 3 3 3 5l1 1h0v-1c0-1-1-1-1-2s-1-3-2-4v-1-1z" class="D"></path><path d="M422 560l1 1 2 2c-1 1-1 1 0 3v2l-1-1h-2-2c0-2 0-3-1-4l-1-1h3c1 0 1 1 2 2v-1c-1-1-1-2-1-3z" class="E"></path><path d="M417 550c2 0 4-1 6 0 2 0 4 0 6 2v2l1 1v1l-1 1 1 2v1-1c-1-1-1-2-2-3h-1c-2 1-4 1-6 2 0 1 1 1 1 2s0 2 1 3v1c-1-1-1-2-2-2h-3v-1c0-1-1-2-1-3v-2l-3 3c-1-1-1-2-1-3-1-1-1-2-1-3l5-2v-1z" class="I"></path><path d="M417 550c2 0 4-1 6 0l2 2-1 1h0l-1-1v-1l-1 1c-1 2-2 3-4 4 0 1 1 1 1 2v1l-2-1v-2l-3 3c-1-1-1-2-1-3-1-1-1-2-1-3l5-2v-1z" class="C"></path><path d="M413 556c-1-1-1-2-1-3l5-2c0 1 0 3-1 4-2 0-2 0-3 1z" class="F"></path><path d="M422 560c0-1-1-1-1-2 2-1 4-1 6-2h1c1 1 1 2 2 3v1-1c1 1 1 2 1 4 1 2 1 4 1 6l1 1v6c1 2 1 4 1 6 1 1 1 3 1 5-2-1-3-2-5-3-1-1-2-2-3-2 0-1 0-1 1-2h0c-1 0-2-1-3-1v-4l1-1c0-1-1-2-2-2v-1c0-1-1-1-2-2l2-2 1 1v-2c-1-2-1-2 0-3l-2-2-1-1z" class="B"></path><path d="M430 569c0-1 0-2 1-3 1 4 1 7 1 12 0 1 1 2 0 4l-1-1-1-1 1-1c-1-3-2-7-1-10z" class="Q"></path><path d="M423 561c2-1 3 0 4 0s2 0 3 1c0 1 1 2 1 4-1 1-1 2-1 3l-4-1c0-1 0-1-1-2-1-2-1-2 0-3l-2-2z" class="K"></path><path d="M425 566c1 1 1 1 1 2l4 1c-1 3 0 7 1 10l-1 1 1 1-1 1c-1 0-1-1-2-2h0c-1 0-2-1-3-1v-4l1-1c0-1-1-2-2-2v-1c0-1-1-1-2-2l2-2 1 1v-2z" class="G"></path><path d="M425 575l1-1c1 0 2 1 3 3h0c0 2 0 2 1 3l1 1-1 1c-1 0-1-1-2-2h0c-1 0-2-1-3-1v-4z" class="S"></path><path d="M430 559l-1-2 1-1 3 6 3 3s-1 1-1 2 1 2 2 3h0l1 1v1c0 1 0 2 1 3 1 2 1 4 2 6l1 1-1 3 1 1h3 0c1 1 1 0 2 1l1 2h2c0 2 0 2-1 4v2h0l1 2c-1 0-1 0-1 1s0 1-1 2h-2c-1-1-1-2-2-2-1-1-2-2-4-3l-5-4-2-1c-2-2-5-4-8-5l-1-1c-3-1-7-2-11-3s-7-1-12-1c-2 0-4-1-5 0h0c-2 1-3 1-4 2-4 1-6 2-8 5v1l-1 1v2c0 1 0 2 1 4l1 2c-1 0-1-1-2-2l-1-1c-1-2-1-2-1-4h-1v-1-2c-1-2-2-4-2-6v-1l-2-5v-1c1 2 1 3 2 4l1 2h0c0 1 1 1 2 2 4 0 6-2 10-3h0c1-1 2-1 4-1 4 0 10-1 15 0h0c2 0 4 0 6 1h3l1 1 7 2c1 0 2 1 3 2 2 1 3 2 5 3 0-2 0-4-1-5 0-2 0-4-1-6v-6l-1-1c0-2 0-4-1-6 0-2 0-3-1-4z" class="Q"></path><path d="M433 562l3 3s-1 1-1 2 1 2 2 3h0l-1 1v1l-2 1-1-11z" class="B"></path><path d="M437 570l1 1v1c0 1 0 2 1 3 1 2 1 4 2 6l1 1-1 3 1 1h3 0c1 1 1 0 2 1l1 2h2c0 2 0 2-1 4v2h0l-1 3h0c-3-1-5-2-7-4l-2-1-3-3h2l1-1c0-2-1-4 1-5l-1-1c-1 0-2 1-3 1l-1-5c0-2 0-4-1-6l2-1v-1l1-1z" class="L"></path><path d="M439 589l1-1 2 2v1l-1 1-2-1-1-1 1-1z" class="S"></path><path d="M441 594h0 1s1-1 1-3h0c0-1 0-1 1-2h1c0-1 1-1 2-2l1 2h2c0 2 0 2-1 4v2h0l-1 3h0c-3-1-5-2-7-4z" class="F"></path><path d="M432 548c0-2 1-2 3-3l1-1h0l2-2c1 0 1 0 2-1 0 0 2 0 3-1h0l1 1c3 2 4 4 6 7v1h2c0-1 0-1-1-2l2-1 2 5c-1 2-2 2-4 3l1 1v3c1 2 1 2 1 5h-1l-1-1v3l1 1v1h-1c0 1-1 1-1 2h2l1 1c-1 1-1 2-2 2v1c0 1-1 2-2 3l2 2c-1 1-1 2-2 3 0 1 0 1-1 2l-3 2c-1 0-1 0-1-1l-2-2-1-1c-1-2-1-4-2-6-1-1-1-2-1-3v-1l-1-1h0v-3c0-2-1-3-1-4v-3c0-2-2-5-3-7v-1c-1-1-1-2-1-4z" class="J"></path><path d="M443 578c1 0 1 0 2-1h3c-1 2-2 2-3 2l-1 1h-1v-2z" class="Q"></path><path d="M439 567v-2c0-1 1-2 2-2l1 1c1 0 1 0 2 1-1 0-2 1-2 2h-3z" class="V"></path><path d="M444 558h1v3c-1 1-1 1-3 1h-2 0l3-3 1-1z" class="B"></path><path d="M444 565l2-1c0-1 1-1 2-2v1l1 2v1c-2 0-3 1-4 3v-1c0-1 0-2-1-3h0z" class="G"></path><path d="M443 578v-2c0-2 1-3 2-3h2c1 1 2 2 2 4h-1-3c-1 1-1 1-2 1z" class="K"></path><path d="M437 567h2 3-1c0 1 0 1-1 1 0 2 0 3 1 5v8c-1-2-1-4-2-6-1-1-1-2-1-3v-1l-1-1h0v-3z" class="G"></path><path d="M432 548c0-2 1-2 3-3l1-1h0l2-2c1 0 1 0 2-1 0 0 2 0 3-1h0l1 1c3 2 4 4 6 7v1h2l1 2h-2l-4 2v1c-1 0-2 0-2 1v1h0c-1-1-1 0-1-1l-1-1c-2-1-4-2-6-4l-1-1-1 1c0 2 2 2 2 4h-1c-1-1-1-1-3-2h0c-1-1-1-2-1-4z" class="F"></path><path d="M450 549h-2l-3-3c0-1-1-2-1-2v-1c-1 0-1 0-2-1h1l1-1c3 2 4 4 6 7v1z" class="E"></path><path d="M437 550c2 0 2 0 4 1v1c1 1 3 1 5 1-1-1-1-1-2-1-1-1-1-1-1-2l-1-3 1-1c1 1 2 1 3 2v1l1 2v1 2c-1 0-2 0-2 1v1h0c-1-1-1 0-1-1l-1-1c-2-1-4-2-6-4z" class="L"></path><path d="M453 554c2 0 4 0 5 1 1 2 2 4 2 6 4 8 7 16 10 25l6 13c3 7 5 14 10 20-3-2-5-3-8-4l-10-2c-10 0-20 2-29 2-7 1-14 1-20 0 0-1-2-3-2-4l-1-2c-2 1-3 2-5 2v1c0 1-1 1-1 2h0-1l-10-2-5-2 2-2-2-3c-1 0-2 0-2-1-1 0-1-1-1-1l-1-1-3-3-2-2-1-2c-1-2-1-3-1-4v-2l1-1v-1c2-3 4-4 8-5 1-1 2-1 4-2h0c1-1 3 0 5 0 5 0 8 0 12 1s8 2 11 3l1 1c3 1 6 3 8 5l2 1 5 4c2 1 3 2 4 3 1 0 1 1 2 2h2c1-1 1-1 1-2s0-1 1-1l-1-2h0v-2c1-2 1-2 1-4h-2l-1-2c-1-1-1 0-2-1h0-3l-1-1 1-3 2 2c0 1 0 1 1 1l3-2c1-1 1-1 1-2 1-1 1-2 2-3l-2-2c1-1 2-2 2-3v-1c1 0 1-1 2-2l-1-1h-2c0-1 1-1 1-2h1v-1l-1-1v-3l1 1h1c0-3 0-3-1-5v-3l1-1z" class="E"></path><path d="M470 610h1v1l-1 1-1-1c0-1 0-1 1-1z" class="O"></path><path d="M383 589h2l1 1-2 2-1-1v-2z" class="C"></path><path d="M455 596c-1 0-1 1-2 2h-1v-1c0-1 1-2 1-3l2-2 1 2h1l-2 2z" class="J"></path><path d="M392 582c1-1 2-1 4-2h0c1-1 3 0 5 0 5 0 8 0 12 1-6 1-12 0-18 1-2 1-5 3-6 3v-1c1 0 2-1 3-2z" class="D"></path><path d="M453 554c2 0 4 0 5 1 1 2 2 4 2 6h0l-1 2 1 3v1c0 1 0 2-1 3h-1v-3c-2-3-1-5-1-8-1-2-2-1-3-1l-1-1v-3zm5 37c1 1 1 1 2 1l1-1v2c0 3 0 5-1 7h0l-2 4c-1 4-5 5-8 6h-1v-1c0-1 1-1 1-2l2-2c1-3 4-5 6-7h0v-1c-2 1-3 2-4 3h-1v-1c1-1 2-1 2-3h0l2-2h-1c1-1 1-1 2-1v-2z" class="C"></path><path d="M458 591c1 1 1 1 2 1l1-1v2c0 3 0 5-1 7h0v-3c0-1-1-2-1-3h-2-1c1-1 1-1 2-1v-2z" class="G"></path><path d="M461 590c1 0 2-1 4-1h0v-1-1c1 0 1 0 2 1 0 1 0 1 2 2h0c3 5 5 10 6 16v2l-1-2c-1 0-2 1-2 1l-1 1v-1-1c-2 0-2 0-3 1-2-1-5-1-7-1-1 1-3 2-3 3h-1c-3 0-5 5-8 4 0-2 0-2 1-3 3-1 7-2 8-6l2-4h0c1-2 1-4 1-7v-2l-1 1c-1 0-1 0-2-1l-1-1c0-1 0-1 2-1l1 1h1z" class="D"></path><path d="M461 593l2-1c2 0 3 1 4 2l1-1 1 1-2 2c0 1 0 1 1 3v1h0c0 1 1 2 1 3l-1 1h-1-2-1v1l-1-1c0-2 0-2 1-3l-1-2 1-1h-1l-1 1v2h-1l-1-1c1-2 1-4 1-7z" class="U"></path><defs><linearGradient id="AJ" x1="464.535" y1="596.827" x2="477.58" y2="586.292" xlink:href="#B"><stop offset="0" stop-color="#1a1b1f"></stop><stop offset="1" stop-color="#363632"></stop></linearGradient></defs><path fill="url(#AJ)" d="M460 561c4 8 7 16 10 25l6 13c3 7 5 14 10 20-3-2-5-3-8-4 0-3-1-4-2-7 0-1 0-2-1-2-1-6-3-11-6-16h0c-2-1-2-1-2-2-1-1-1-1-2-1v1 1h0c-2 0-3 1-4 1h-1l-1-1c-2 0-2 0-2 1l1 1v2c-1 0-1 0-2 1l-1-2v-2c0-2 1-4 1-5l1-1v-6c1 0 2-1 2-2 1-1 1-1 2-1v-3l-1 1h-1c1-2 1-2 0-3 1-1 1-2 1-3v-1l-1-3 1-2h0z"></path><path d="M460 566h1v1 2c1 2 1 3 1 4 1 2 2 4 2 6-1 0-1 0-2 1v1c-1-1-1-1-1-2l1-2v-1c-1 0-2 0-2 1l-1 3c0 2-1 3-2 4h0v-6c1 0 2-1 2-2 1-1 1-1 2-1v-3l-1 1h-1c1-2 1-2 0-3 1-1 1-2 1-3v-1z" class="a"></path><path d="M459 580v1c1 1 1 2 2 3v1c1-1 1-2 1-3s1-2 2-3c3 3 4 7 5 11h0c-2-1-2-1-2-2-1-1-1-1-2-1v1 1h0c-2 0-3 1-4 1h-1l-1-1c-2 0-2 0-2 1l1 1v2c-1 0-1 0-2 1l-1-2v-2c0-2 1-4 1-5l1-1h0c1-1 2-2 2-4z" class="K"></path><path d="M456 585l1 1h1c-1 2-2 3-3 4 0-2 1-4 1-5z" class="D"></path><path d="M459 589c1-2 2-3 4-3h0l-1 2h0c0 1 0 1-1 2h-1l-1-1z" class="a"></path><path d="M420 586c3 0 5 2 7 3v1 1h0c2 0 3 1 4 1 1 1 2 2 2 3h1c1 0 1 1 2 1 1 1 2 1 3 3h0c1 1 1 1 3 1h1c1 0 2 0 3 2 0 1 0 2-1 4v1 1h0v2c-1 1-2 2-2 3-1 1-2 1-3 1l-1 1c-7 1-14 1-20 0 0-1-2-3-2-4l-1-2c0-1 1-2 2-2l2-1 1-1 1-2c2 0 3-1 4-2l1-2v-1c0-2-1-5-2-6-1 0-1 0-2 1h-2-1-1-1l-2 1-1-1 1-1c1 0 1 0 1-1l1-1h2c-1-1-1-2-1-2l1-2z" class="D"></path><path d="M420 606c0 2 0 3-2 4-1 0-1 0-1 1l-1-2c0-1 1-2 2-2l2-1z" class="B"></path><path d="M423 606c2 0 3 0 4-2l1-1h1c1 1 2 2 3 4h1c0 1 0 1-1 2h0 2l1 1h-1c-1 1 0 1-1 2v1h-1l-1-2c-1 0-2 0-3 1h-1-1c1-1 1-1 1-2l-2 1h0l-2-1c0-1 0-2 1-3h-2l1-1z" class="R"></path><path d="M437 602v-1-2h-3-2l1-1h4l1 1h1c1 1 1 1 3 1h1c1 0 2 0 3 2 0 1 0 2-1 4v1 1h0c-1 2-2 3-4 4-1 1-2 1-3 0 0-3 1-5 0-8v-1-1h-1z" class="C"></path><path d="M420 586c3 0 5 2 7 3v1 1h0c2 0 3 1 4 1 1 1 2 2 2 3h1c1 0 1 1 2 1 1 1 2 1 3 3h0-1l-1-1h-4l-1 1h2 3v2 1h1v1l-1 2-3 3h0l2 1v2l-3 1c1-1 0-1 1-2h1l-1-1h-2 0c1-1 1-1 1-2h-1c-1-2-2-3-3-4h-1l-1 1c-1 2-2 2-4 2v-1h-2l1-2c2 0 3-1 4-2l1-2v-1c0-2-1-5-2-6-1 0-1 0-2 1h-2-1-1-1l-2 1-1-1 1-1c1 0 1 0 1-1l1-1h2c-1-1-1-2-1-2l1-2z" class="Q"></path><path d="M426 601h2l-4 4h-1-2l1-2c2 0 3-1 4-2z" class="K"></path><path d="M420 590h0c1-1 2-1 3-1h1c-1 2-2 3-3 4h-1-1-1l-2 1-1-1 1-1c1 0 1 0 1-1l1-1h2z" class="T"></path><path d="M431 592c1 1 2 2 2 3h1c1 0 1 1 2 1 1 1 2 1 3 3h0-1l-1-1h-4l-1 1h2 3v2 1h-4c-1-1-1-1-2-1s0 0-1-1l1-1c-1-1-2-2-4-3 1-3 2-3 4-4z" class="U"></path><path d="M389 596l-2-2c0-2-1-4 0-5 2-3 6-5 9-6 3 0 5 0 7 1h8c2 0 3 0 5 1h1l3 1-1 2s0 1 1 2h-2l-1 1c0 1 0 1-1 1l-1 1 1 1 2-1h1 1 1 2c1-1 1-1 2-1 1 1 2 4 2 6v1l-1 2c-1 1-2 2-4 2l-1 2-1 1-2 1c-1 0-2 1-2 2-2 1-3 2-5 2v1c0 1-1 1-1 2h0-1l-10-2-5-2 2-2-2-3c-1 0-2 0-2-1-1 0-1-1-1-1l-1-1-3-3 1-2c1 0 0 0 1-1z" class="Q"></path><path d="M404 597h1l1 1v1h-1c0 1-1 1-2 1v-1l1-2z" class="D"></path><path d="M411 593c0-1 1-2 2-3v1s0 1 1 2h0v2c-1 1-1 1-2 0l-1-2z" class="B"></path><path d="M417 585l3 1-1 2s0 1 1 2h-2l-1 1c0 1 0 1-1 1 1-3 0-4 1-7z" class="V"></path><path d="M402 604c2-1 6-3 7-5-1-1-1-2-2-2v-1h1c1 0 2 1 2 2v3c-2 2-3 3-5 4l-3-1z" class="G"></path><path d="M389 596c2 1 4 1 6 1 1 0 2 0 3 1h0l-2 2h3c2 0 3-1 4-1v1c-2 1-4 1-6 1l1 1c1 1 2 1 3 1v1h-1l1 1h-6 0-1c-1 0-2 0-2-1-1 0-1-1-1-1l-1-1-3-3 1-2c1 0 0 0 1-1z" class="B"></path><path d="M395 605l-3-2c0-1 1-2 2-2h2 1l1 1c1 1 2 1 3 1v1h-1l1 1h-6 0z" class="R"></path><defs><linearGradient id="AK" x1="394.151" y1="593.1" x2="407.98" y2="587.737" xlink:href="#B"><stop offset="0" stop-color="#4a4a4b"></stop><stop offset="1" stop-color="#636364"></stop></linearGradient></defs><path fill="url(#AK)" d="M411 584v1l-2 1c1 1 2 1 2 2s-1 1-1 2l-2 2c-1 1-2 3-3 4h-1v1l-1 2c-1 0-2 1-4 1h-3l2-2h0c0-1 1-1 1-1l-1-1h-2l-3-3h5c1-1 2-2 2-3l1-2-2-2v-1l4-1h8z"></path><path d="M389 596l-2-2c0-2-1-4 0-5 2-3 6-5 9-6 3 0 5 0 7 1l-4 1v1l2 2-1 2c0 1-1 2-2 3h-5l3 3h2l1 1s-1 0-1 1c-1-1-2-1-3-1-2 0-4 0-6-1z" class="D"></path><path d="M393 593l-1-2 1-1 2 2c1-1 1 0 2-1 1 0 1-2 2-2l1 1c0 1-1 2-2 3h-5z" class="O"></path><path d="M423 593c1-1 1-1 2-1 1 1 2 4 2 6v1l-1 2c-1 1-2 2-4 2l-1 2-1 1-2 1c-1 0-2 1-2 2-2 1-3 2-5 2v1c0 1-1 1-1 2h0-1l-10-2-5-2 2-2-2-3h1 0 6l1-1 3 1c2-1 3-2 5-4v-3c2-2 1-3 1-5l1 2c1 1 1 1 2 0v-2h0v-1l1 1 1 1 2-1h1 1 1 2z" class="D"></path><path d="M416 602c3-1 4 0 6 1l-1 2-1 1-2 1v-1l-1-1h-2l-1-1 2-2z" class="a"></path><path d="M414 593h0v-1l1 1 1 1c1 1 1 1 1 3l1 2c1 1 2 1 3 1h2l1-1v2h0l3-3v1l-1 2c-1 1-2 2-4 2-2-1-3-2-6-1l1-2v-1l-3 2c-1-1-1-2-1-3 1-2 1-1 1-2v-1-2z" class="Z"></path><path d="M423 593c1-1 1-1 2-1 1 1 2 4 2 6l-3 3h0v-2l-1 1h-2c-1 0-2 0-3-1l-1-2c0-2 0-2-1-3l2-1h1 1 1 2z" class="c"></path><path d="M416 594l2-1 1 1h1v1c-1 1-2 2-2 4l-1-2c0-2 0-2-1-3z" class="e"></path><path d="M423 593h1v3c1 1 0 0 1 2h-2-2v-1c1-1 1-2 1-3l1-1z" class="M"></path><path d="M410 601c0 2 0 4-1 5l-2 1 1 1v1l2-1v3l-1 1 1 2h0-1l-10-2-5-2 2-2-2-3h1 0 6l1-1 3 1c2-1 3-2 5-4z" class="F"></path><path d="M396 608h0c1 1 2 1 3 1v3l-5-2 2-2z" class="L"></path><path d="M402 604l3 1h0l-1 1c-2 1-4 1-6 1-1 0-2-1-3-2h6l1-1z" class="C"></path><path d="M409 614l-1-2c0-1-1-3-2-3s-1 1-2 0c0-1 1-1 2-2h1l1 1v1l2-1v3l-1 1 1 2h0-1z" class="B"></path><defs><linearGradient id="AL" x1="554.878" y1="242.063" x2="551.77" y2="263.896" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#38393a"></stop></linearGradient></defs><path fill="url(#AL)" d="M550 238c1-1 2-2 4-2s6 2 8 3c1 1 2 1 3 1l1 1 4 1 1 1h4v2h1l2 1h4 15 13 8l3 3v1l1 1c0 2 0 4-1 6h-1c-1 1-1 2-1 3-1 2-2 3-2 5l-1 2v1 4h0c0 2-1 2-3 2l-1 1-22 7c0 1 0 4-1 5v-1l-1 2c0 1 1 4 0 6 0 1 0 2-1 3-2 0-3 2-4 4-1 1-2 2-2 4l-2 4c-1 2-1 5-1 7 1 5 3 8 5 13-3 1-4 4-6 6h-3-1-1l-1 2c-1-1-3-2-5-3h-1v-1-1h-1-2c-2-1-2-1-2-3l-3 3c-1 0-2 1-2 2l-4 4c-1 2-2 3-3 5l-2 2v-1c-1 0-2 0-3-1h-2l-1-1c-1-1-3-1-4-1l-3-1-2-2c-2 0-3-1-5-3 2 1 3 1 4 1s2-1 2-1c1-2 1-3 0-4h0-1l2-4 1-1 1-2h1l1-2c0-1 0 0 1-1 0-1 0-2 1-2v-1c-1-1-2-1-3-2l-2-1h-1c-2-1-5-1-6-2-2-1-3-2-5-2v-2h-1v2c-1 1-1 1-1 2l-1-1v-3-1l-2 2v-2-4h1v-1l-2 1h0l-1-1-1 1h-1c-2-1-2-2-3-3h0c0-1-1-1-1-3h2l-1-1v-1-3l1-1 1-1h0c-2-2-3-2-4-2-2 1-3 1-6 1l-2-2c5 0 8 0 13-2 0-1 0-1 1-1 1-1 3 0 4 1 2 0 3 0 4 1 1 0 2 1 2 1l1-2h3c1-1 1-2 1-3l-3-2h4c3 1 4-1 7-2s5-1 8-3v-1h0l-1-1-4-1c-2 0-4-1-6-2l-2-5v-2-1h0c-1-1-2-1-3-1 0-1 1-1 1-1 2-1 3-1 3-3l-1-1 3-2c-1-1-5 0-6-1l10-4v-1h-3c1-1 2-1 4-2 1-1 10-8 10-9z"></path><path d="M546 264c-1-1-1-2-1-2 0-1 0-2-1-2v-1h2c2 2 4 3 6 5v1h-3v-1l-2 1v-1-1l-1 1z" class="G"></path><path d="M553 248c4-1 8-2 12 0 1 1 2 2 2 3l-1 1h0l-1-1c-1-1-2-1-4-1-3-2-5-2-8-2z" class="C"></path><path d="M539 250c1 0 3-1 4-2h1 0 3c-4 2-8 6-12 7-1-1-5 0-6-1l10-4z" class="l"></path><path d="M553 248c3 0 5 0 8 2 2 0 3 0 4 1l1 1h0c-1 2-2 3-5 3v-1h-1c-1 1-3 2-4 2h0l1-1c1 0 2-1 3-2h2l-3-3c-2-1-5 0-8 0 0 1-1 2-1 2l-3-1 6-3z" class="B"></path><path d="M539 249c4-2 7-4 11-7 3-1 6-3 9-2h1c3 1 6 3 9 4l2 2v1c-5-3-9-5-14-4-4 1-7 3-10 5h-3 0-1c-1 1-3 2-4 2v-1z" class="k"></path><path d="M550 238c1-1 2-2 4-2s6 2 8 3c1 1 2 1 3 1l1 1 4 1-1 2c-3-1-6-3-9-4h-1c-3-1-6 1-9 2-4 3-7 5-11 7h-3c1-1 2-1 4-2 1-1 10-8 10-9z" class="B"></path><path d="M545 269l1-1 1-1h1v1h1c2-2 4-2 6-4 3-1 5-3 8-5 0 0 1-1 2-1h0c2-2 3-3 5-4 0 1 0 1 1 2-2 1-4 2-5 3s-1 2-2 2l-4 2-3 3v1h-1l-1 1h-1c-2 1-6 3-8 5l-2 1-4-1c-2 0-4-1-6-2l-2-5v-2-1h0c1-1 1-2 2-2 1 1 0 3 1 4 2 2 2 4 5 5h1 2l2-1z" class="H"></path><path d="M547 251l3 1s1-1 1-2c3 0 6-1 8 0l3 3h-2c-1 1-2 2-3 2l-1 1h0l-3 1-2 1 1 1h-1c-2-1-3-1-4-2h-1v1 1h-2v1c1 0 1 1 1 2 0 0 0 1 1 2l1-1v1c-1 1-1 2-1 3l-2 2h1l-2 1h-2-1c-3-1-3-3-5-5-1-1 0-3-1-4l2-2c1 0 1 0 2 1l1-1s0-1 1-1l2-3c2-1 4-2 5-4z" class="J"></path><path d="M546 258c-1 0-2-1-3-2h2c1 0 1 0 2-1 0-1 0 0 1-1h1v2c1 1 2 1 3 1h1l-2 1 1 1h-1c-2-1-3-1-4-2h-1v1z" class="G"></path><path d="M534 261l2-2c1 0 1 0 2 1-1 1-1 2-1 5 1 1 1 1 2 1 2 1 2 1 5 1 1-1 1-1 2-3l1-1v1c-1 1-1 2-1 3l-2 2h1l-2 1h-2-1c-3-1-3-3-5-5-1-1 0-3-1-4z" class="B"></path><path d="M597 246h13 8l3 3v1l1 1c0 2 0 4-1 6h-1v-2l-1 1c-1-1-3-1-4-1l1-1v-1h-1-1c-8-1-16-2-23-1-3 1-6 1-9 2l-15 9c-1 1-1 1-2 1s-1 1-2 1h-2c-4 1-7 6-11 8h-1l-2 1c0-1 0-1-1-1 2-2 6-4 8-5h1l1-1h1v-1l3-3 4-2c1 0 1-1 2-2s3-2 5-3h0l-1-3c2-2 2-2 5-2h0v-5h3 4 15z" class="r"></path><path d="M610 246h8l3 3v1l-17-1h5c-1-1-1 0-1-1s1-1 2-2z" class="q"></path><path d="M575 251v-5h3 4c5 4 10 2 15 3-6 1-14 2-21 1l-1 1z" class="n"></path><path d="M597 246h13c-1 1-2 1-2 2s0 0 1 1h-5-7c-5-1-10 1-15-3h15z" class="d"></path><path d="M578 251h12c-4 2-8 2-11 3-6 2-14 7-18 11-4 1-7 6-11 8h-1l-2 1c0-1 0-1-1-1 2-2 6-4 8-5h1l1-1h1v-1l3-3 4-2c1 0 1-1 2-2s3-2 5-3h0l-1-3c2-2 2-2 5-2h3z" class="F"></path><path d="M575 251h3c0 1-1 2-2 3h-2c-1 0-1 1-2 1l-1 1-1-3c2-2 2-2 5-2z" class="W"></path><path d="M582 254c3-1 6-1 9-2 7-1 15 0 23 1h1 1v1l-1 1c1 0 3 0 4 1l1-1v2c-1 1-1 2-1 3-1 2-2 3-2 5l-1 2v1l-2 2-2 1h-2c-1 1-2 1-3 1 0-1-1-1-2-1l-10 2-42 8-20 2h-4l-3-2h4c3 1 4-1 7-2s5-1 8-3v-1h0l-1-1 2-1c1 0 1 0 1 1l2-1h1c4-2 7-7 11-8h2c1 0 1-1 2-1s1 0 2-1l15-9z" class="C"></path><path d="M559 270c1-1 1-1 2 0l-3 3h-2 0l3-3z" class="S"></path><path d="M572 264c2-2 4-3 6-4 2 0 4 0 6 1l-5 1h-3v1l-4 1z" class="G"></path><path d="M584 270c1 1 1 0 2 0s1 1 2 1l-12 3c-2 0-4 0-6 1-1 0-2 1-3 1v-1h0-1-2l-1-1c4 0 6-2 10-2 1 0 1-1 2-1h1 3c2-1 3-1 5-1z" class="O"></path><path d="M584 261c1 1 3 1 4 2s2 3 2 5v1l2-1 1 1v1c-2 0-3 1-5 1-1 0-1-1-2-1s-1 1-2 0c-2-1-3-1-5-1l1-3c-2-1-2-2-4-3v-1h3l5-1z" class="U"></path><path d="M584 261c1 1 3 1 4 2-1 1-1 1-2 1h0c-1 1 1 1 1 2l-1 1h-3c1-1 1-1 1-2l-1 1c-1 0-1 0-2-1l-2-1v-2l5-1z" class="R"></path><path d="M576 263c2 1 2 2 4 3l-1 3c2 0 3 0 5 1-2 0-3 0-5 1h-3-1c-1 0-1 1-2 1-4 0-6 2-10 2 0 0 0-1-1-1h-1c1-1 2-3 4-3 0 0 1 1 2 1v-1l2-3h0c1-2 2-2 3-3h0l4-1z" class="D"></path><path d="M569 267h0c1-2 2-2 3-3v1l2 2v2s-1 1-2 1c0 1 0 1-1 1l-1-1h0l1-1-2-2z" class="O"></path><path d="M572 265l2 2v2s-1 1-2 1c0 0 0-3-1-4l1-1z" class="B"></path><path d="M576 263c2 1 2 2 4 3l-1 3c-2 0-2 0-3-2h-2l-2-2v-1h0l4-1z" class="R"></path><path d="M582 254c3-1 6-1 9-2 7-1 15 0 23 1h1 1v1l-1 1c1 0 3 0 4 1l1-1v2c-1 1-1 2-1 3-1 2-2 3-2 5l-1 2v1l-2 2-2 1h-2c-1 1-2 1-3 1 0-1-1-1-2-1l2-1h-2c-2-1-3 0-5-1-2 0-5-1-7 0l-1-1c-1-1-1-2 0-4l-1-1c-1 0-2 0-3-1l-5-2v-1h0c1-1 2-1 4-1h3 0-1c-2-1-3-1-5-1h-1-1l1-1h-1 1l-1-2z" class="W"></path><path d="M598 264c-1-2-2-4-2-6 1-1 2-2 4-2 1 1 3 2 3 4 1 1 0 2 0 2-1 1-2 2-3 2h-2z" class="U"></path><path d="M598 264v1c1 0 1 0 2 1-1 0-4-1-5-1-1-1-2-2-3-4v-3l1-1c0-1 0-1 1-2h2c1 1 2 1 3 1h1c-2 0-3 1-4 2 0 2 1 4 2 6z" class="C"></path><path d="M619 256l1-1v2c-1 1-1 2-1 3-1 2-2 3-2 5l-1 2v1l-2 2-2 1h-2c-1 1-2 1-3 1 0-1-1-1-2-1l2-1 3-3c2-4 3-9 5-12 1 0 3 0 4 1z" class="v"></path><path d="M610 267l1 1c0 1 0 2-1 3s-2 1-3 1c0-1-1-1-2-1l2-1 3-3z" class="n"></path><path d="M619 256l1-1v2c-1 1-1 2-1 3-1 2-2 3-2 5l-1 2v1l-2 2s-1-1-2-1c0-2 2-4 3-6 1-3 2-5 4-7z" class="u"></path><path d="M616 268v4h0c0 2-1 2-3 2l-1 1-22 7-8 2-13 4-4 1v1c-2 1-4 3-6 4s-6 5-7 7l-1 2-10 14 2 2h1-1c-1 0-1 0-2-1l-1 1h0c-1 1-1 2-2 2 0-1 0-2 1-2v-1c-1-1-2-1-3-2l-2-1h-1c-2-1-5-1-6-2-2-1-3-2-5-2v-2h-1v2c-1 1-1 1-1 2l-1-1v-3-1l-2 2v-2-4h1v-1l-2 1h0l-1-1-1 1h-1c-2-1-2-2-3-3h0c0-1-1-1-1-3h2l-1-1v-1-3l1-1 1-1h0c-2-2-3-2-4-2-2 1-3 1-6 1l-2-2c5 0 8 0 13-2 0-1 0-1 1-1 1-1 3 0 4 1 2 0 3 0 4 1 1 0 2 1 2 1l1-2h3c1-1 1-2 1-3h4l20-2 42-8 10-2c1 0 2 0 2 1 1 0 2 0 3-1h2l2-1 2-2z" class="q"></path><path d="M529 283h4c2 2 4 2 7 2h-1c-3 0-7 0-11 2h-3v1h9l-5 1c-2 0-3-1-5-1l1-2h3c1-1 1-2 1-3z" class="n"></path><path d="M533 283l20-2c2 1 4 1 6 1l-4 1h0l-15 2c-3 0-5 0-7-2z" class="s"></path><path d="M553 281l42-8c-2 2-4 2-7 3-10 3-19 4-29 6-2 0-4 0-6-1z" class="v"></path><path d="M559 285l19-4c2 1 3 2 4 3h0l-13 4c-1 0-2-2-2-2-2-1-5 0-7 0l-1-1z" class="m"></path><path d="M539 288l20-3 1 1c2 0 5-1 7 0 0 0 1 2 2 2l-4 1c-5 1-14 2-18 6-1 0-1 0-2-1h-6l3-1c2-1 3-1 4-3-2 0-3 0-4 1l-2-2-1-1z" class="o"></path><path d="M616 268v4h0c0 2-1 2-3 2l-1 1-22 7-8 2h0c-1-1-2-2-4-3l28-8 1-1c1 0 2 0 3-1h2l2-1 2-2z" class="r"></path><path fill="#6c451d" d="M612 271h1s1 1 2 1h0c-1 1-2 2-3 2-2 0-4 0-6-1l1-1c1 0 2 0 3-1h2z"></path><path d="M518 286c2 0 3 0 4 1 1 0 2 1 2 1 2 0 3 1 5 1l5-1h5l1 1 2 2c1-1 2-1 4-1-1 2-2 2-4 3l-3 1c-5 1-9 2-12 6-2 2-4 9-5 9h-1v2c-1 1-1 1-1 2l-1-1v-3-1l-2 2v-2l4-4 2-4v-3c1-1 1-2 1-3l-1-1c-2-1-3-2-4-3l1-1h1l-1-2h-2v-1z" class="p"></path><path d="M534 288h5l1 1h-2c-2 1-3 2-4 3l-1-1c-3 1-3 2-4 4v1c-1 0-2 1-3 1v-1c1-1 2-4 2-5h-2l1-1h2v-1l5-1z" class="m"></path><path d="M518 286c2 0 3 0 4 1 1 0 2 1 2 1 2 0 3 1 5 1v1h-2l-1 1h2c0 1-1 4-2 5v1c-2 1-2 2-3 3v-3c1-1 1-2 1-3l-1-1c-2-1-3-2-4-3l1-1h1l-1-2h-2v-1z" class="t"></path><path d="M513 286c0-1 0-1 1-1 1-1 3 0 4 1v1h2l1 2h-1l-1 1c1 1 2 2 4 3l1 1c0 1 0 2-1 3v3l-2 4-4 4v-4h1v-1l-2 1h0l-1-1-1 1h-1c-2-1-2-2-3-3h0c0-1-1-1-1-3h2l-1-1v-1-3l1-1 1-1h0c-2-2-3-2-4-2-2 1-3 1-6 1l-2-2c5 0 8 0 13-2z" class="o"></path><path d="M510 301c2 0 4 1 6 1s3-2 4-3 1-3 1-5l-3-3-1-1h2c1 1 2 2 4 3l1 1c0 1 0 2-1 3v3l-2 4-4 4v-4h1v-1l-2 1h0l-1-1-1 1h-1c-2-1-2-2-3-3h0z" class="r"></path><path fill="#937e5c" d="M512 291c2 0 4-1 5 1 2 2 3 2 3 5l-4 4c-1 0-2 0-4-2l-1-1-1-1v-1-3l1-1 1-1z"></path><defs><linearGradient id="AM" x1="538.085" y1="293.646" x2="547.096" y2="311.073" xlink:href="#B"><stop offset="0" stop-color="#0c0b0a"></stop><stop offset="1" stop-color="#353638"></stop></linearGradient></defs><path fill="url(#AM)" d="M565 289v1c-2 1-4 3-6 4s-6 5-7 7l-1 2-10 14 2 2h1-1c-1 0-1 0-2-1l-1 1h0c-1 1-1 2-2 2 0-1 0-2 1-2v-1c-1-1-2-1-3-2l-2-1h-1c-2-1-5-1-6-2-2-1-3-2-5-2v-2c1 0 3-7 5-9 3-4 7-5 12-6h6c1 1 1 1 2 1 4-4 13-5 18-6z"></path><defs><linearGradient id="AN" x1="563.851" y1="285.945" x2="567.657" y2="317.802" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#3c3d3e"></stop></linearGradient></defs><path fill="url(#AN)" d="M582 284l8-2c0 1 0 4-1 5v-1l-1 2c0 1 1 4 0 6 0 1 0 2-1 3-2 0-3 2-4 4-1 1-2 2-2 4l-2 4c-1 2-1 5-1 7 1 5 3 8 5 13-3 1-4 4-6 6h-3-1-1l-1 2c-1-1-3-2-5-3h-1v-1-1h-1-2c-2-1-2-1-2-3l-3 3c-1 0-2 1-2 2l-4 4c-1 2-2 3-3 5l-2 2v-1c-1 0-2 0-3-1h-2l-1-1c-1-1-3-1-4-1l-3-1-2-2c-2 0-3-1-5-3 2 1 3 1 4 1s2-1 2-1c1-2 1-3 0-4h0-1l2-4 1-1 1-2h1l1-2c0-1 0 0 1-1 1 0 1-1 2-2h0l1-1c1 1 1 1 2 1h1-1l-2-2 10-14 1-2c1-2 5-6 7-7s4-3 6-4v-1l4-1 13-4z"></path><path d="M577 294c-1-2-2-3-2-4v-1l1 1c0 1 1 2 2 2s2 1 3 2v1c-1 0-1 1-2 1-1-1 0-1-1-2h0-1z" class="S"></path><path d="M549 311l1-1v-2c1-1 3-3 4-3 2-1 3-1 6 0 0 1 2 1 3 2s2 1 3 2v2c-2-1-4-1-6-2h-3v-1l-2 1c0 1-1 2-2 3l-1-1c-1 1 0 1-1 2l-2-2z" class="B"></path><path d="M552 311c1-1 2-1 2-2v-1c2 0 2-1 3-1 1-1 1-1 2-1s2 1 3 1h1c1 1 2 1 3 2v2c-2-1-4-1-6-2h-3v-1l-2 1c0 1-1 2-2 3l-1-1z" class="G"></path><path d="M559 298c2-1 4 1 7 1 2 1 3 1 5 2 1 2 2 2 3 3l1 1h-1v2h-1c-1-1-2-1-3-1s-2-1-3-1c-1-1-3-2-5-3-2 0-4-1-5-2v-1l2-1z" class="E"></path><path d="M555 309l2-1v1h3c2 1 4 1 6 2h1l1-1 1 1h-1v1l-3 3c0 1 0 1-1 2h-1c0 1 0 0-1 1 2 1 3 0 4 1 0 1-1 1-2 2h1c1 0 2-2 3-3s2-2 2-3c1-1 2-1 3-2h0c-1 1-2 3-3 4-2 3-4 5-7 8l-2 2s-1 0-2-1h-2l1 1c1 0 0 0 1 1l-1 1c-1 0-2 0-3 1l-2-1-1 1c-1 1-2 0-3 0 1 0 1 0 2-1h0v-1-2l1-1v-1l-2-2-3-1h-1c-1-1-2-1-2-2h-1l1-2c2 0 2 0 2-1 1-2 2-3 3-5l2 2c1-1 0-1 1-2l1 1c1-1 2-2 2-3z" class="K"></path><path d="M549 311l2 2c1-1 0-1 1-2l1 1h0v3h-1c-1 1-1 2-1 3h-1l-4-2c1-2 2-3 3-5z" class="U"></path><path d="M555 309l2-1v1h3c2 1 4 1 6 2h1l1-1 1 1h-1v1l-3 3c0 1 0 1-1 2h-1c0 1 0 0-1 1v1l-3-2c1-4-1-2-2-5l1-1h-3l3-1c-1 0-2-1-3-1z" class="J"></path><path d="M546 316l4 2h1c2 2 4 3 6 5l2 2 1-1h0c-1 1-3 1-3 2l1 1c1 0 0 0 1 1l-1 1c-1 0-2 0-3 1l-2-1-1 1c-1 1-2 0-3 0 1 0 1 0 2-1h0v-1-2l1-1v-1l-2-2-3-1h-1c-1-1-2-1-2-2h-1l1-2c2 0 2 0 2-1z" class="D"></path><path d="M552 324l2 1h0c-1 1-1 2 0 3h-3v-2l1-1v-1z" class="B"></path><path d="M546 316l4 2-2 2h-2c-1-1-2-1-2-1h-1l1-2c2 0 2 0 2-1z" class="O"></path><path d="M588 288c0 1 1 4 0 6 0 1 0 2-1 3-2 0-3 2-4 4-1 1-2 2-2 4l-2 4c-1 2-1 5-1 7 1 5 3 8 5 13-3 1-4 4-6 6h-3-1-1l-1 2c-1-1-3-2-5-3h-1v-1-1h-1-2c-2-1-2-1-2-3h2c2 1 3 2 5 1l-6-3 2-2c3-3 5-5 7-8 1-1 2-3 3-4s1-1 1-2h1v-1-1-1c1-1 1-1 1-2s1-2 2-3v-1c0-2 1-2 0-3v-2c0-1-1-2-1-3h0 1 0c1 1 0 1 1 2l1 5h0c1-1 2-2 2-3 1-1 1-2 2-3l2-1c0-1 1-2 0-4h0c1-2 1-2 2-2z" class="L"></path><path d="M570 322l1 2h0v3l-1 1c-1-1-2-1-3-2 1-1 2-2 3-4z" class="K"></path><path d="M570 322h0c1-1 3-4 5-5v2 1c-1 2-2 2-1 4l-2 3h-1v-3h0l-1-2z" class="D"></path><path d="M560 329h2c2 1 3 2 5 1 1 1 3 2 4 3v1l-2-1c-2 1-2 1-4 0v-1h-1-2c-2-1-2-1-2-3z" class="G"></path><path d="M572 327l2-3c-1-2 0-2 1-4 0 2 1 3 2 4l1 2v1l-1 1-2 2-1-1h-1c-1-1-1-1-1-2z" class="B"></path><path d="M575 319c1-2 2-2 3-3 1 5 3 8 5 13-3 1-4 4-6 6h-3c1-2 2-3 3-5h-2l2-2 1-1v-1l-1-2c-1-1-2-2-2-4v-1z" class="F"></path><defs><linearGradient id="AO" x1="533.934" y1="336.311" x2="548.272" y2="333.62" xlink:href="#B"><stop offset="0" stop-color="#3b3c3d"></stop><stop offset="1" stop-color="#535456"></stop></linearGradient></defs><path fill="url(#AO)" d="M538 321c1 0 1-1 2-2h0l1-1c1 1 1 1 2 1h1c0 1 1 1 2 2h1l3 1 2 2v1l-1 1v2 1h0c-1 1-1 1-2 1 1 0 2 1 3 0l1-1 2 1c1-1 2-1 3-1l1-1c-1-1 0-1-1-1l-1-1h2c1 1 2 1 2 1l6 3c-2 1-3 0-5-1h-2l-3 3c-1 0-2 1-2 2l-4 4c-1 2-2 3-3 5l-2 2v-1c-1 0-2 0-3-1h-2l-1-1c-1-1-3-1-4-1l-3-1-2-2c-2 0-3-1-5-3 2 1 3 1 4 1s2-1 2-1c1-2 1-3 0-4h0-1l2-4 1-1 1-2h1l1-2c0-1 0 0 1-1z"></path><path d="M549 330c1 0 2 1 3 0l1-1 2 1c1-1 2-1 3-1-2 1-3 3-5 5-1 2-2 2-3 3l-1-1 1-2-1-2-1-1c-1 1-1 2-2 2h-2l-1-2v-1c2 1 4 0 6 0z" class="Q"></path><path d="M526 335c2 1 3 1 4 1s2-1 2-1c1-2 1-3 0-4h0-1l2-4v5c1 1 1 0 1 1l-1 1v1c1 0 2 1 3 1l4 4c3 1 4-1 6 2v2c-1 0-2 0-3-1h-2l-1-1c-1-1-3-1-4-1l-3-1-2-2c-2 0-3-1-5-3z" class="C"></path><defs><linearGradient id="AP" x1="537.962" y1="322.14" x2="545.868" y2="332.805" xlink:href="#B"><stop offset="0" stop-color="#262527"></stop><stop offset="1" stop-color="#4c4e4e"></stop></linearGradient></defs><path fill="url(#AP)" d="M538 321c1 0 1-1 2-2h0l1-1c1 1 1 1 2 1h1c0 1 1 1 2 2h1l3 1 2 2v1l-1 1v2 1h0c-1 1-1 1-2 1-2 0-4 1-6 0h-2s-1 0-1 1c-1 0-2 0-2 1l-1-1c-1 1-2 2-3 2 0-1 0 0-1-1v-5l1-1 1-2h1l1-2c0-1 0 0 1-1z"></path><path d="M546 323c2 0 3 0 4 1l-1 1h-3v-2z" class="E"></path><path d="M655 162h56 0 21l-6 1v1h-1c-1-1-2 0-3 0 0 1 0 2 1 3 1 0 1-2 2-1l1 1v-1l2 2h1c0-1 0-1 1-2v2l1-1h1c0 1 0 1-1 2l-1 1-1 1 1 2c1 0 1 0 1 1l-2-1c-1 0-1 1-1 2v1h-1c0 1-1 2-1 3h-1v-1h0l-1 1 1 2h4 0v1c-1 1-1 2-2 2l-1 1h0v2h0l-1-3c-1 1-1 1-1 2 1 3 1 5 0 8v4 1c3 2 6 2 10 3 1 0 2 1 4 1s7 2 8 4l1 1c-1 1-1 2-1 3h0c0 2 0 2-1 4l1 1h0l-1 1h0c0 1 1 1 2 1h1c1 0 2 2 3 2l1-1 1 1-1 2c-1-1-2-1-2-2l-1 1h-1c2 4 3 6 3 11-1 1-1 2-2 3 1 0 2-1 2-2 1-1 3-2 4-4l1 1c-1 2-3 4-4 5l-2 2c0 1-2 2-3 3l-6 3-9 5c-5 3-10 7-15 8l-4 2-3 1h0-4c-3-3-5-4-7-7h0c-1 1-2 1-3 1v1l1 2c1 1 3 2 4 3l-1 1c-4-2-6-5-9-7l-6-5h2c1 0 1 0 2-1 0-2 1-4 1-6 1-2 1-5 1-7l-1-8c-2-6-3-10-5-16l-2-1c-4-7-11-14-20-16-6-1-11-1-17 3-2 2-3 4-4 8-3-5-4-11-6-16-1-4-3-7-5-10l-3-4v-1c3-1 5-4 6-7 1-1 1-2 2-3 5-2 13-1 19-1z" class="C"></path><path d="M676 177l16-1h-1c-1 0-1 1-2 1-2 1-6 1-7 1h-1c-1-1-3-1-5-1z" class="G"></path><path d="M718 179c2 3 3 4 4 8l1 1c0 3 0 7-2 10l-1 2-1-1c-1-1 0-3 0-4 1-1 1-1 1-2 0-2 1-1 2-3v-1c-1 0-2 1-4 1s-2 0-3-1l1-1h3c1-1 1-1 1-2-1-1-1-1-1-2 0-2-2-2-2-4h0l1-1z" class="E"></path><path d="M672 188v-2h4l4 3v-1c-2-1-4-2-5-4v1c-1-1-2-1-2-2h1 0 0c-1-1 0-1-1-1 1-1 2-1 3-1 0 2 0 2 2 4 1 2 4 3 6 5v1h3c1 2 2 3 4 4s2 3 2 5c2 3 3 8 6 11 1 2 5 3 8 4-1 1-1 1-1 2-2 0-3-1-5-1-4-4-5-9-8-14h0c-1-5-6-7-10-9h-3c-1 1-2 3-3 5h-1v-4c1 0 1 0 2-1 0 0 0-1 1-1v-1-1c-1-1-2-2-3-2-1-1-3-1-4 0z" class="P"></path><path d="M729 215l1-1c0-1 0-1-1-2h-1v1c-1-1-1-1-1-2-1 0-1 0 0-1l1-1c1 0 1-1 2-1s0 0 1-1v-1l2-1c-1-1-1-1 0-2 1 1 1 1 3 1h0l1 2h0v2h-2l1 1h2l1-1h4 0l1 1c-1 0-1 0-1 1v1c-1 0-1 0-2 1l1 1 1-1h2l1-1h0c0 2 0 2-1 4l1 1h0l-1 1h0c0 1 1 1 2 1h1c1 0 2 2 3 2l1-1 1 1-1 2c-1-1-2-1-2-2l-1 1h-1c-1 0-1-1-2-1-4-5-11-5-17-5z" class="I"></path><path d="M679 174c9-1 35-3 42 3 2 2 3 4 4 7-1 1-1 1-1 2 1 3 1 5 0 8v4 1h-2v2c-1-1-1 0-1-1l-1 1c1 1 2 2 2 3-1 3-6 7-8 9-1-1-3-2-3-3 1-1 2 0 3 1v-1c-1-1-2-2-3-2s-1 1-1 2 1 1 2 2l2 1c-2 0-4 0-5-2-1-1-1-1-1-3-1 1-2 1-2 2-3 0-4 0-6-2v-1c-1 0-1 0-1 1-1-1-1-2-1-3v-1h1v1l1-1c3 2 1 5 5 5 2-2 3-3 3-5v-1l1-1c-2 0-3 2-4 3v2l-1 1h-1v-1-1-1h1v-1l-1-1-1 1-1-1c1-1 2-1 3-2h0-1c-1 0-2 1-3 3-1-1-2-1-2-1h-1l-1-1c2-2 4-2 5-4-2 0-3 2-4 2h-1c2-2 4-4 7-4 1 2 0 4 4 5h2l1-1c-1-1-2-2-3-2v-1-1l2 1h0c0-2-1-3-1-5-2 0-2 2-4 2v-1c1-2-1-1 1-3h2c2 1 3 3 3 5 0 1 1 2 1 3v1l1 1-1 1c2 1 5 0 7 0l2-1 1-2c2-3 2-7 2-10l-1-1c-1-4-2-5-4-8-8-4-18-3-26-3l-16 1h-2c-6-1-13-1-19-1 3-1 6 1 9 0 2-1 2-1 4-1h13v-1h-2z" class="T"></path><path d="M655 176c6 0 13 0 19 1h-11c-1 1-1 1-1 2 1 0 1 0 2 1h2l1-1 1 2c0 2 0 2-1 3h1v1h0l-1 1h0c1 2 1 3 1 4l1 1v-1c1-1 2-1 3-2s3-1 4 0c1 0 2 1 3 2v1 1c-1 0-1 1-1 1-1 1-1 1-2 1v4h1c1-2 2-4 3-5h3c4 2 9 4 10 9h0v4l-1 2-1 1c-2 0-3 0-5-1v1l-1 1h0l-2-1c-4-7-11-14-20-16-6-1-11-1-17 3-2 2-3 4-4 8-3-5-4-11-6-16-1-4-3-7-5-10h1c1 1 2 2 3 4 0 1 1 2 2 4 1 4 2 8 4 12v1c0-1 1-1 1-2l-1-1v-2l-1-1c0-2-1-3-1-5 1 1 1 0 1 1h1 0v-2h1l1-1c-1-1 0-3 1-4l1-3 1-1 1 1h-1c-1 2-1 3-1 4v1c1-1 1-2 2-3h0c2-3 5-4 8-5z" class="L"></path><path d="M662 179c1 0 1 0 2 1l1 1c-2 1-3 0-4 2-1 1 0 1-2 2h-1c0-2 1-3 2-4h2 0v-2z" class="H"></path><defs><linearGradient id="AQ" x1="678.552" y1="198.105" x2="686.747" y2="193.999" xlink:href="#B"><stop offset="0" stop-color="#2b2c2c"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#AQ)" d="M677 198c1-2 2-4 3-5h3c4 2 9 4 10 9h0v4l-1 2-1-2v-1c-1 1-1 2-2 2h0l1-4h-2l-1 1c-1-1 1-3 0-4-1-2-1-2-2-2h-1v-1h-2v1l-2 1v1h0c-1 0-2-1-3-2z"></path><path d="M655 176c6 0 13 0 19 1h-11c-1 1-1 1-1 2v2h0-2c-1 1-2 2-2 4-1-1-1-1-2 0h-3c-1 0-2 1-2 1v3l-1 1c-1-1-2-2-3-2 0 0-1 1-1 2l-1-1-4 5 1 1v2l-1-1v-2l-1-1c0-2-1-3-1-5 1 1 1 0 1 1h1 0v-2h1l1-1c-1-1 0-3 1-4l1-3 1-1 1 1h-1c-1 2-1 3-1 4v1c1-1 1-2 2-3h0c2-3 5-4 8-5z" class="F"></path><g class="G"><path d="M655 176c6 0 13 0 19 1h-11c-1 1-1 1-1 2v2l-2-1c-1 0-1 1-2 1h-1l-1-1 1-1v-1l-1 1h-1c-1 1-2 1-3 1-2 1-4 3-5 4h0l1-2-1-1h0c2-3 5-4 8-5z"></path><path d="M655 162h56 0 21l-6 1v1h-1c-1-1-2 0-3 0 0 1 0 2 1 3 1 0 1-2 2-1l1 1v-1l2 2h1c0-1 0-1 1-2v2l1-1h1c0 1 0 1-1 2l-1 1-1 1 1 2c1 0 1 0 1 1l-2-1c-1 0-1 1-1 2v1h-1c0 1-1 2-1 3h-1v-1h0l-1 1 1 2h4 0v1c-1 1-1 2-2 2l-1 1h0v2h0l-1-3c-1-3-2-5-4-7-7-6-33-4-42-3h2v1h-13c-2 0-2 0-4 1-3 1-6-1-9 0s-6 2-8 5h0c-1 1-1 2-2 3v-1c0-1 0-2 1-4h1l-1-1-1 1-1 3c-1 1-2 3-1 4l-1 1h-1v2h0-1c0-1 0 0-1-1 0 2 1 3 1 5l1 1v2l1 1c0 1-1 1-1 2v-1c-2-4-3-8-4-12-1-2-2-3-2-4-1-2-2-3-3-4h-1l-3-4v-1c3-1 5-4 6-7 1-1 1-2 2-3 5-2 13-1 19-1z"></path></g><path d="M655 164c0 2 0 5 1 6 2-2-1-5 4-6v1h1 1c1 0 1 0 2-1v1h0v1l2 2c-2 0-2-1-3 0v1l-1 1c0 1 0 0-1 1-2 0-3 1-5 0l-2-1v1c-1 0 0 0-1 1-1-1-1-2-1-3l2-2-1-1c1-1 1-2 2-2z" class="N"></path><path d="M645 179h0c0-2 0-3 1-4 4-4 17-2 22-2l11 1h2v1h-13c-2 0-2 0-4 1-3 1-6-1-9 0s-6 2-8 5h0c-1 1-1 2-2 3v-1c0-1 0-2 1-4h1l-1-1-1 1z" class="f"></path><path d="M631 178l-3-4v-1c3-1 5-4 6-7 1-1 1-2 2-3 5-2 13-1 19-1v1 1c-1 0-1 1-2 2l1 1-2 2c0 1 0 2 1 3h-1-4v-1h0c-1-1-1-1-2-1-2 1-3 3-4 5 0 1-1 1-1 2l-1-1 1-2h-1v-1h-4c0 1 0 2 1 3v1l1 2h0l-1 1h2 0c1 1 1 1 1 2h0l-1 1 1 1 1 1 2 1-1 1h-1v2h0-1c0-1 0 0-1-1 0 2 1 3 1 5l1 1v2l1 1c0 1-1 1-1 2v-1c-2-4-3-8-4-12-1-2-2-3-2-4-1-2-2-3-3-4h-1z" class="D"></path><path d="M711 162h21l-6 1v1h-1c-1-1-2 0-3 0 0 1 0 2 1 3 1 0 1-2 2-1l1 1c0 1 1 2 1 3h0v1h0v1l-3 3c-1 0-8-1-9-2h-4c-2-1-3-1-5-1h0-3-2-6c-1-2-2-3-3-4 0-1 0-2 1-2 4-5 11-3 17-3l1-1h0z" class="K"></path><path d="M692 208l1-2v-4c3 5 4 10 8 14 2 0 3 1 5 1 0-1 0-1 1-2 7 0 15-1 22 0 6 0 13 0 17 5 1 0 1 1 2 1 2 4 3 6 3 11-1 1-1 2-2 3 1 0 2-1 2-2 1-1 3-2 4-4l1 1c-1 2-3 4-4 5l-2 2c0 1-2 2-3 3l-6 3-9 5c-5 3-10 7-15 8l-4 2-3 1h0-4c-3-3-5-4-7-7h0c-1 1-2 1-3 1v1l1 2c1 1 3 2 4 3l-1 1c-4-2-6-5-9-7l-6-5h2c1 0 1 0 2-1 0-2 1-4 1-6 1-2 1-5 1-7l-1-8c-2-6-3-10-5-16h0l1-1v-1c2 1 3 1 5 1l1-1z" class="e"></path><path d="M690 226h2c1 1 1 2 3 2-2 2-2 2-2 5h-1l-1 1-1-8z" class="C"></path><path d="M708 230h-1c-1 0 0 0-1-1l2-2c2-1 4-2 6-4l1 2c0-1 1-1 2-1-1 1-2 1-2 2h-1c-1 1-1 1-1 3v1h-1c0-1 0-1-1-2-1 1-2 1-3 2z" class="D"></path><path d="M708 230c1-1 2-1 3-2 1 1 1 1 1 2h1v-1c0 1 0 2 1 3h0c1 0 2-1 3-1h2c0 1-1 2-1 3h-1v-2h0c-1 1-1 2-1 3h-1 0v-2h-1 0c0 1-1 1-1 2v1 2l-2 2-1-1c-1 1-2 2-3 2l3-3h0c-1-2-1-3-2-3l-1-1h-3v-1l1-2h0c1 0 2 1 4 1h1l1 1h0c-1-2-1-2-3-3z" class="J"></path><path d="M692 208l1-2v-4c3 5 4 10 8 14 2 0 3 1 5 1h3c0 1-1 2-1 3-1 0-2 1-3 1v-1c-2 0-2 0-3 1h-2c0-1 0-1 1-1v-1c0-1 0-1 1-1l-1-1c-1 0-1 1-1 2-2 1-3 1-5 2h-1 0l-2 2h3v-1h2 0c-2 1-3 1-4 2v1h2v1h-2-1-2c-2-6-3-10-5-16h0l1-1v-1c2 1 3 1 5 1l1-1z" class="B"></path><path d="M685 210h0l1-1v-1c2 1 3 1 5 1h0l-2 2v2c1 1 4 0 5 0 0 1 0 2-1 3-1 0-2 0-3 1l2 1 3-2v1c0 1-2 3-1 4l-2 2h3v-1h2 0c-2 1-3 1-4 2v1h2v1h-2-1-2c-2-6-3-10-5-16z" class="F"></path><path d="M707 215c7 0 15-1 22 0 6 0 13 0 17 5 1 0 1 1 2 1 2 4 3 6 3 11-1 1-1 2-2 3h-1c-1 1-2 1-2 2l-1-1h-4v-1l-3 1c2-3 0-1 0-3l1-1c-1-1-1-1-1-2h-1c1-1 1-2 2-2l2-2h3v-1c-1-1-1 0-3-1-1 1-1 1-2 1h-1c1-1 3-2 4-3h2v-1c-1-1-1-1-2-1l-2 2h-1c1-1 1-1 1-2h-1l-1 1v2c-1 2-2 3-4 4h0l2-2c0-1 1-1 1-2l-1-1v2h-1v-1l3-3v-1h-1-1c-2 0-1 0-2 1l-2-1h-1c1 1 2 2 2 4-1-1-2-3-3-3h-1c-1-1-1 0-2 0h0l-1-1h-1c-1-1-1 0-2-1-1 0-1 1-2 1-3 3-2 1-5 1 1 1 1 1 2 1h0v1l-1 1-2-2-1 1v-1h0c-3 0-5 1-7 2l-1 1v-1c1-1 2-2 2-3h0c0-1 1-2 1-3h-3c0-1 0-1 1-2z" class="G"></path><path d="M707 215c7 0 15-1 22 0 6 0 13 0 17 5 1 0 1 1 2 1 2 4 3 6 3 11-1 1-1 2-2 3h-1c-1 1-2 1-2 2l-1-1c2-1 2-2 3-4 0-1 0-1 1-1h1v-1c0-2 0-3-1-4s-1-4-3-5c-10-7-26-4-37-4h-3c0-1 0-1 1-2z" class="X"></path><path d="M738 225h1c1 0 1 0 2-1 2 1 2 0 3 1v1h-3l-2 2c-1 0-1 1-2 2h1c0 1 0 1 1 2l-1 1c0 2 2 0 0 3l3-1v1h4l1 1c0-1 1-1 2-2h1c1 0 2-1 2-2 1-1 3-2 4-4l1 1c-1 2-3 4-4 5l-2 2c0 1-2 2-3 3l-6 3-9 5c-5 3-10 7-15 8l-4 2c0-1 0-1-1-1h-3v-2c0-1-1-1-1-2h1c0-1-1-1-1-2v-1l2 1c1-1 0-3 0-4h1v2c1 1 1 3 3 3 2 1 4 1 5 0 1 0 2-1 2-1 2 0 2-1 3-2 1 0 1-1 1-1-1-2-1-5-1-6 0-4-6-5-8-7 2 0 4-1 6 0h0l1-1 1-1c1-2 2-3 5-3v1c2 0 2 0 4-1 1 0 1-1 2-2s2-2 3-2v-1z" class="U"></path><path d="M693 233l3-2c3-1 5-1 9 0l-1 2v1h3l1 1c1 0 1 1 2 3h0l-3 3c1 0 2-1 3-2l1 1 2-2v-2 1h2c0 1 0 2-1 3h1c2-1 3 1 4 0l-2-1 1-1c2 0 3 1 4 3v1c1 2 0 3 1 5v2h-1c0-1 1-2 0-3-1 1-1 1-1 2s-1 2-2 3c-1-1-1 0-1-1l-2 1h-2c-1-1-1-2-1-4l-1-1c-1 1-1 2-1 3v-2h-1c0 1 1 3 0 4l-2-1v1c0 1 1 1 1 2h-1c0 1 1 1 1 2v2h3c1 0 1 0 1 1l-3 1h0-4c-3-3-5-4-7-7h0c-1 1-2 1-3 1v1l1 2c1 1 3 2 4 3l-1 1c-4-2-6-5-9-7l-6-5h2c1 0 1 0 2-1 0-2 1-4 1-6 1-2 1-5 1-7l1-1h1z" class="R"></path><path d="M696 247l4 4 2 2c1 0 1 0 2-1 2 1 2 3 5 3v2h3c1 0 1 0 1 1l-3 1h0-4c-3-3-5-4-7-7-1-2-3-3-3-5z" class="C"></path><path d="M693 233l3-2c3-1 5-1 9 0l-1 2v1h-2l-2 2c0 2-1 3 1 5h-1c0-1-1-2-1-2-1 0-2 1-3 0v1s0 1 1 1h1v1c-1 0-2 0-3-2h-1c0-1 0-1-1-1v-1-1c-1-1-1-2-1-4h1z" class="G"></path><path d="M691 234l1-1c0 2 0 3 1 4v1 1 5c1 1 3 2 3 3 0 2 2 3 3 5h0c-1 1-2 1-3 1v1l1 2c1 1 3 2 4 3l-1 1c-4-2-6-5-9-7l-6-5h2c1 0 1 0 2-1 0-2 1-4 1-6 1-2 1-5 1-7z" class="B"></path><path d="M610 708l1 1h4 4l8 5v1c5 1 8 4 12 7 2 2 5 4 7 7l3 5c1 0 1 0 1 1 1 2 2 3 3 5 1 3 2 5 3 8s1 6 2 10v2 3h0l-1 1 1 1 1-1h0c1 2 0 3 1 5-2 14-9 27-19 36-10 8-22 12-35 11-4 0-10-2-14-3l1-1-8-3c-4-1-8 1-11 3l-1-1 1-3v-1c0-1 0-2 1-3h0l-1-1v-1l1-1v-1c0-4 0-8 1-12l2 1v-14c-1-4-1-9-2-14-2-10-6-20-11-28l1-2-1-1c-1-2-2-3-3-4 0-1-1-1-1-2s0-2-1-3c2-2 1-1 3-1l2-1v-1c-1-2-4-1-6-1-2-1-4-2-5-4 2 0 2 0 4 1h0c1 0 3-1 4-1h0c1-1 2-1 3-1 2-1 3-1 5-1s4 1 7 1c1 1 3 2 5 3h1l1 1c1 0 3 1 4 2 1-1 2-3 3-4h3c1 1 1 1 1 3v1l-1 1c0 1 0 1 1 2 4-2 9-2 13-3 0-1-1-1-1-1l-2-2c-1-3-3-4-5-7l8 2 1-1 1-1z"></path><path d="M592 761h-1 0v-1l-1-1s-1-1-1-2l2-1 2-2h1c-1 2-1 5-2 7z" class="I"></path><path d="M637 744c2 1 5 3 6 5-2 1-4 0-6 0h-1c-1-2 0-2 0-4l1-1z" class="N"></path><path d="M605 771l-2-2c-1-1 0-2 0-3l1-1v1c1 4 2 5 5 7 1 1 1 3 1 4v1l-2-2s-1 0-2-1h-4c1-1 1-1 3-2l-1-1h1v-1z" class="c"></path><path d="M605 746l4-2c9-5 18-3 28 0l-1 1c-4-1-6-2-9-1-5-1-10 0-16 1l-5 2-1-1zm32 6l4 6-14 4-2-3-2-3h1c2 0 3 0 4-1h1 0c1 1 1 2 1 3 1 0 3-1 5-1v-1c2-2 1-2 2-4z" class="k"></path><path d="M628 755v2c-1 1-1 1-3 2l-2-3h1c2 0 3 0 4-1zm-12 5l1-1 6 7c-3 2-6 3-9 5v-1l-6-6 2-1 6-3z" class="T"></path><path d="M610 763l6-3 2 1c1 1 1 1 0 2v1l1 2h-2-1c-2 1-2 1-4 1-1-1-2-2-2-4z" class="N"></path><path d="M610 708l1 1h4 4l8 5v1l2 2c3 1 4 2 5 5 2 3 6 5 8 8 1 1 2 3 3 4h-2c-9-11-21-22-35-24l1-1 1-1z" class="k"></path><path d="M611 709h4 4l8 5v1c-1 0-1 0-2-1-2-1-5-1-7-2-1-1-2-1-2-1-2-1-2-1-3-1s-2-1-2-1z" class="N"></path><defs><linearGradient id="AR" x1="605.221" y1="726.801" x2="596.445" y2="717.05" xlink:href="#B"><stop offset="0" stop-color="#787879"></stop><stop offset="1" stop-color="#9b9b9b"></stop></linearGradient></defs><path fill="url(#AR)" d="M608 718c2 3 4 5 4 8 0 0-1 0-1-1-1-1-2-2-4-2s-5 0-7 1c0 1 0 0 1 1 1 0 1 1 2 2l1 5-2-1v2l-1-1-2-2h0c-1 0-1-1-2-1-1-2-3-4-4-5l-2-2v-1h3 1c4-2 9-2 13-3z"></path><path d="M603 727l1 5-2-1c-1-2-1-2-1-4h1 1z" class="D"></path><path d="M605 746l1 1h0c-2 2-3 3-5 4v1c-2 1-3 1-3 3l1 2-3 1h-1c-1 1-1 2-2 3 0 1 1 2 1 3 0 0 0 1 1 1-2 2-4 4-4 7v1l-4 9c-1 2-1 3-1 5h0-1v-1c-1 1-1 2-3 2 3-9 8-17 10-27 1-2 1-5 2-7v-2c0-2 0-2 1-3h2l1 1 7-4z" class="T"></path><path d="M606 747c-2 2-3 3-5 4v1c-2 1-3 1-3 3l1 2-3 1h-1l1-5c3-3 6-4 10-6z" class="G"></path><path d="M627 744c3-1 5 0 9 1 0 2-1 2 0 4h0l1 3c-1 2 0 2-2 4l-1-2-3-3c-3-2-5-2-9-2l-2-1-1-1h-2c-2 0-4 1-6 2h0c-4 2-8 4-12 8l-1-2c0-2 1-2 3-3v-1c2-1 3-2 5-4h0l5-2c6-1 11-2 16-1z" class="F"></path><path d="M633 748c1 0 2 0 3 1l1 3c-1 2 0 2-2 4l-1-2c0-2-1-4-1-6z" class="c"></path><path d="M627 744c3-1 5 0 9 1 0 2-1 2 0 4h0c-1-1-2-1-3-1l-1-1c-1-2-3-2-5-3z" class="e"></path><path d="M625 745c2 1 5 1 6 3 1 0 1 1 1 2 0 0 0 1-1 1-3-2-5-2-9-2l-2-1-1-1h-2c2-1 5-1 8-2z" class="D"></path><path d="M611 745c2 1 4 0 6 0 1 1 1 0 2 0h6c-3 1-6 1-8 2-2 0-4 1-6 2h0c-4 2-8 4-12 8l-1-2c0-2 1-2 3-3v-1c2-1 3-2 5-4h0l5-2z" class="E"></path><path d="M602 764h2v1l-1 1c0 1-1 2 0 3l2 2v1h-1l1 1c-2 1-2 1-3 2h4c-3 0-5 0-7 2-2 3-4 7-4 11 0 5 2 12 6 16 2 2 5 3 7 4 0 1-1 1-2 2-2 0-7-2-9-4l1-1-4-2c-5-4-7-10-8-16h0c0-2 0-3 1-5l4-9v-1c1-2 2-3 3-4 1 1 2 1 2 2l6-6z" class="H"></path><path d="M594 768c1 1 2 1 2 2-4 6-7 11-10 17 0-2 0-3 1-5l4-9v-1c1-2 2-3 3-4z" class="P"></path><path d="M605 771v1h-1l1 1c-2 1-2 1-3 2h4c-3 0-5 0-7 2-2 3-4 7-4 11 0 5 2 12 6 16 2 2 5 3 7 4 0 1-1 1-2 2-2 0-7-2-9-4l1-1h0c-2-1-3-2-5-4-1-1-2-2-1-3 1 2 3 3 5 5-3-6-6-14-4-21 1-3 5-8 8-10 1 0 2 0 3-1h1z" class="e"></path><path d="M617 747h2l1 1 2 1c4 0 6 0 9 2l3 3 1 2v1c-2 0-4 1-5 1 0-1 0-2-1-3h0-1c-1 1-2 1-4 1h-1l-7 1 1 2h0l-1 1-6 3-2 1-4 2v-1-1h-2l-6 6c0-1-1-1-2-2-1 1-2 2-3 4 0-3 2-5 4-7-1 0-1-1-1-1 0-1-1-2-1-3 1-1 1-2 2-3h1l3-1c4-4 8-6 12-8h0c2-1 4-2 6-2z" class="E"></path><path d="M617 747h2l1 1c-3 1-5 2-8 2l-1-1h0c2-1 4-2 6-2z" class="K"></path><path d="M611 749l1 1c-3 2-10 4-12 8-1 1-1 2-2 3-1 2-2 3-3 4-1 0-1-1-1-1 0-1-1-2-1-3 1-1 1-2 2-3h1l3-1c4-4 8-6 12-8z" class="c"></path><path d="M594 768v-1c3-4 10-10 15-12 2 0 3 0 5-1-2 2-3 2-5 3-2 0-6 4-7 6v1l-6 6c0-1-1-1-2-2z" class="M"></path><path d="M622 749c4 0 6 0 9 2l3 3 1 2v1c-2 0-4 1-5 1 0-1 0-2-1-3h0c-1-1-2-3-3-3-4-2-13 1-17 3v-1c4-3 8-4 13-5z" class="T"></path><path d="M614 754c3-1 9-2 12-1l2 2c-1 1-2 1-4 1h-1l-7 1 1 2h0l-1 1-6 3-2 1-4 2v-1-1h-2v-1c1-2 5-6 7-6 2-1 3-1 5-3z" class="N"></path><path d="M604 764h0c2-4 9-6 12-7l1 2h0l-1 1-6 3-2 1-4 2v-1-1z" class="b"></path><path d="M562 713c1-1 2-1 3-1 2-1 3-1 5-1s4 1 7 1c1 1 3 2 5 3h1l1 1c1 0 3 1 4 2 1-1 2-3 3-4h3c1 1 1 1 1 3v1l-1 1c0 1 0 1 1 2h-1-3v1l2 2c1 1 3 3 4 5 1 0 1 1 2 1h0l2 2 1 1v-2l2 1c1 2 2 5 3 7v2c-1 1-3 0-4-1h-1c-3-1-6-3-8-5s-4-4-5-6l-1-1-3-3-1-1c-1-1-2-2-4-2-4-2-11-1-16 1v2l-2 1c0-1-1-1-1-2s0-2-1-3c2-2 1-1 3-1l2-1v-1c-1-2-4-1-6-1-2-1-4-2-5-4 2 0 2 0 4 1h0c1 0 3-1 4-1h0z" class="N"></path><path d="M578 719l1-1c1 0 1 0 2 1h0v1h1c2 0 3 1 5 2l1 1v1l-1 1c-3-3-6-4-9-6z" class="e"></path><path d="M602 731l2 1c1 2 2 5 3 7l-3-1c-1-2-2-3-2-5v-2z" class="K"></path><path d="M591 714h3c1 1 1 1 1 3-1 1-2 1-3 1h-4c1-1 2-3 3-4z"></path><path d="M588 728l3-1c3 2 4 6 7 9l3 3h2l-1 1c-3-1-6-3-8-5s-4-4-5-6l-1-1z" class="T"></path><path d="M577 716c2 0 3 1 4 2v1c-1-1-1-1-2-1l-1 1c-2 0-5 0-7 1h-3c-2 1-3 1-4 2v1 2l-2 1c0-1-1-1-1-2s0-2-1-3c2-2 1-1 3-1l2-1 8-2c1 0 3 0 4-1z" class="K"></path><path d="M562 713c1-1 2-1 3-1 2-1 3-1 5-1s4 1 7 1c1 1 3 2 5 3h1l1 1c-3 1-3 0-6-1h0l-3 1h2c-1 1-3 1-4 1l-8 2v-1c-1-2-4-1-6-1-2-1-4-2-5-4 2 0 2 0 4 1h0c1 0 3-1 4-1h0z" class="F"></path><path d="M562 713h8c2 0 5 1 8 2l-3 1c-6-1-11 0-17-2 1 0 3-1 4-1h0z" class="P"></path><path d="M562 713c1-1 2-1 3-1 2-1 3-1 5-1s4 1 7 1c1 1 3 2 5 3h1l1 1c-3 1-3 0-6-1h0c-3-1-6-2-8-2h-8z" class="R"></path><path d="M564 723c5-2 12-3 16-1 2 0 3 1 4 2l1 1 3 3 1 1c1 2 3 4 5 6h-1c-2-1-4-5-6-5s-3 1-4 1l2 2c-1 2-3 4-6 5-1 0-2 1-3 2h1 0c1-1 0 0 2-1h0 1c1-1 3-3 5-3v1c-1 2-3 3-5 4l1 1c1-1 2-1 2-2 1-1 2-1 3-2l1 1h0c0 1 1 2 1 3 1 1 1 1 1 2 1 1 1 1 2 1l1 1c-1 0-3 2-3 3v1c0 2-2 5-3 7-1 4-2 7-4 11l-1 2-1-4h-1c1-1 1-1 1-2v-2l1-1v-2l2-2c0-1 1-2 0-3v-1c-1 0-1-1 0-1v-1l-3 3 1 1h0 1c-1 1-2 2-3 2h-1c-1-2-1-3 0-5h0l-1 2h-1c1-2 1-3 0-4-1-2-2-3-2-4 1 0 1 0 1 1h1c0-1 1-1 1-2-1 1-1 1-2 1l-1-2 1-1h-1c-3-1-1-1-3-3v1l5 13v2c0 2 1 4 1 6 0 1 0 1 1 2v7c1 1 1 3 0 4-1-4-1-9-2-14-2-10-6-20-11-28l1-2-1-1c-1-2-2-3-3-4l2-1v-2z" class="b"></path><path d="M585 725l3 3 1 1c-2 0-3 0-4-1h-2v-1c0-1 1-1 2-2zm-19 1h2l4 1 1 1v1l-1 1c-1 1-1 1-2 1v2h-1l-1-1v-3c0-1-1-1-2-2v-1z" class="H"></path><path d="M564 723c5-2 12-3 16-1 2 0 3 1 4 2h-1c-1 0-2 0-3-1-3-1-10 0-13 1l-2 1h-1v-2z" class="S"></path><path d="M583 757c1-1 2-4 4-6h1c-3 5-4 10-6 15v2l-1 2-1-4h-1c1-1 1-1 1-2v-2l1-1v-2l2-2z" class="C"></path><path d="M566 731c0 3 1 4 3 5 1 1 1 1 2 1s2-1 2-2l1 1 2-2-1-2 1-1h2 1l-2 2h0 2 1c-2 1-6 4-6 5l1 1h0l-1 1c-2 0-2 0-3-1l-1 1 1 1 5 13v2c0 2 1 4 1 6 0 1 0 1 1 2v7c1 1 1 3 0 4-1-4-1-9-2-14-2-10-6-20-11-28l1-2z" class="S"></path><defs><linearGradient id="AS" x1="628.324" y1="782.042" x2="649.78" y2="793.321" xlink:href="#B"><stop offset="0" stop-color="#99989b"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#AS)" d="M627 715c5 1 8 4 12 7 2 2 5 4 7 7l3 5c1 0 1 0 1 1 1 2 2 3 3 5 1 3 2 5 3 8s1 6 2 10v2 3h0l-1 1 1 1 1-1h0c1 2 0 3 1 5-2 14-9 27-19 36-10 8-22 12-35 11-4 0-10-2-14-3l1-1-8-3c-4-1-8 1-11 3l-1-1 1-3v-1c0-1 0-2 1-3h0l-1-1v-1l1-1v-1c0-4 0-8 1-12l2 1-2 10 5-10 1-1c2 0 2-1 3-2v1h1c1 6 3 12 8 16l4 2-1 1c2 2 7 4 9 4 1-1 2-1 2-2 10 2 19-1 27-6 10-5 16-15 19-26 3-15-3-29-11-42h2c-1-1-2-3-3-4-2-3-6-5-8-8-1-3-2-4-5-5l-2-2z"></path><path d="M627 715c5 1 8 4 12 7 2 2 5 4 7 7l3 5c1 0 1 0 1 1 1 2 2 3 3 5 1 3 2 5 3 8s1 6 2 10c-1-1-1-2-2-2v-1c-1-3-1-7-3-10-2-6-5-11-8-16l-1-1h-1c0 1 1 1 1 2 2 2 4 6 6 9l3 8-1 1-7-14c-1-1-2-3-3-4-2-3-6-5-8-8-1-3-2-4-5-5l-2-2z" class="e"></path><path d="M582 788c2 0 2-1 3-2v1h1c1 6 3 12 8 16l4 2-1 1c2 2 7 4 9 4h1c13 2 24-3 34-10-1 2-5 5-7 6-10 7-23 10-35 7l-6-1-8-3c-4-1-8 1-11 3l-1-1 1-3v-1c0-1 0-2 1-3h0l-1-1v-1l1-1v-1c0-4 0-8 1-12l2 1-2 10 5-10 1-1z" class="W"></path><path d="M592 803c-3-1-3-1-5-2-1-2-2-3-3-5h1l2 2 5 5z" class="S"></path><path d="M582 788c2 0 2-1 3-2v1h1c1 6 3 12 8 16l4 2-1 1c-2-1-4-2-5-3l-5-5c-1-3-2-6-2-9-3 3-4 8-6 11-1 3-3 6-5 8v-1c0-1 0-2 1-3h0l-1-1v-1l1-1v-1c0-4 0-8 1-12l2 1-2 10 5-10 1-1z" class="c"></path><defs><linearGradient id="AT" x1="632.329" y1="408.891" x2="661.624" y2="419.401" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#333435"></stop></linearGradient></defs><path fill="url(#AT)" d="M707 290c3-1 6-1 8 0l-7 1h0c-1 1-2 1-3 2h0l-1 1v1c-1 1-3 2-5 3s-3 1-4 1c-1 1-1 1-2 1l-1-1 1-2h-2c-4 2-9 5-10 8v1c-1 1-1 2-2 4l-9 21 1 1c1-1 2-1 3-2v1c-1 1-2 1-3 2l-1 1 4 2 4 3c1 1 1 1 2 1l1 1v1c1 0 2 1 3 1v1l2 1 3 2c1 1 2 1 3 2 1 0 2 1 2 1l1 1c1 0 1 0 2 1h1c1 1 2 2 2 3l3 3-4-1 3 3 1 1-1 1c1 0 1 1 2 1 3 2 5 5 8 7l6 3-1 1-2-1v1l2 2h1-4l-1 1 5 1h-5c-3-1-6 0-8-2l-1 1h-1l-1 1h2l-1 1h-1-1c1 2 3 6 2 8 1 2 2 4 3 5s2 1 2 1c-1 1-4 1-5 1l-2 1v3l-1 1c0 1 1 1 1 2v1l3 3v1c1 2 3 3 4 5h1c1 1 3 4 4 5l-2 2 2 2c-1 1-2 1-3 1 0 3 1 3-1 6h-2-1c-6-1-8 0-14 2 2 0 5 0 7 1h-4l-1 1c1 1 1 2 2 3h0c0 2 0 2 1 4-1 0-1 1-2 1-1 1-1 1-1 2v1h-3c0 1 1 1 1 1 1 1 0 2 2 3 1 0 3 0 4 1-4 0-7 0-11 2-4 1-7 4-9 7l1 1c-1 1-2 3-2 5-2 3-3 7-6 10-2 5-6 8-10 12l-19 14c-2 2-4 3-6 5l-1-1-2-3h0c-7-9-13-26-12-37v-1c1-4 2-6 4-9-1-1-2-1-3-1-1-1-2-2-3-4s-3-4-3-7v-2h-1c0-2 0-2-1-4 0-2-2-1-3-2l1-1 9 3h0s-1-1-1-2l45-101c1-2 2-5 3-7l12-27 2 1c1-1 2-2 4-2 1 0 1-1 1-1h1c1-1 2-1 3-1 2 0 5-1 7-2 3-1 6-2 9-2z"></path><path d="M670 342l-2-1v-3l2-1 1 3-1 2z" class="b"></path><path d="M677 345c0-1 0 0 1-1 3 0 5 1 7 3-1 0-3 0-4-1l-2 1-2-2z" class="C"></path><path d="M671 340c1 0 3 1 3 2v1h-2 0c1 1 1 1 2 1 0 1 1 1 1 1h-2c-1 0-2 0-3-1h-1l1-1h1v-1h-1l1-2z" class="I"></path><path d="M622 445c-2-2-2-3-2-5 1-1 2-1 3-1 0 1-1 1-1 3 0 0 1 1 1 2 1 1 1 1 1 2h1v1c-1 0-1 0-1 1l-2-3z" class="S"></path><path d="M693 352c2 1 5 3 7 3l3 3-4-1h-1l-4-2v-2l-2-1h1z" class="F"></path><path d="M622 445l2 3h1c1 1 2 2 3 2-1 1-1 1-3 2h0c-1-1-2-1-3-1-1-1-2-2-3-4l3-2z" class="P"></path><path d="M612 431l9 3c2 2 3 2 4 4-1 1-1 1-2 1s-2 0-3 1c0 2 0 3 2 5l-3 2c-1-2-3-4-3-7v-2h-1c0-2 0-2-1-4 0-2-2-1-3-2l1-1z" class="X"></path><path d="M658 370l-1-1v-1c1-1 0-2 1-3l2-2-1-1h-1v-1c2 0 2-1 3-1 1-1 3-1 3-1v3l3 3c-1 0-2-1-3 0 2 1 3 1 5 1h0c3 0 4 0 6 2-2 3-9 4-11 5v-2c1 0 2 0 2-1l-1-1-1 1-1-1h-3 0l-2 1z" class="C"></path><path d="M631 448h1c1-1 1-1 2-1h0l-1 2h1l1-1v1 1l3-1h0l-3 3-2 1h1 2l-1 3c1 1 1 1 3 1 0 1 1 1 1 1l-1 2h-3 0v2h-2v1c1 1 2 1 4 1v2c-2-1-4-2-6-2h-2-1c-2-1-2 0-4-1 1-2 1-2 2-3 1 0 0-2 0-3h2v-1c0-1 1-2 1-2 2-1 2-2 4-4h-1c0-1 0-1-1-2z" class="E"></path><path d="M635 460c-1-1-2-2-3-2s-1-1-1-1c1 0 0 0 1-1v-1h2l1 1c1 1 1 1 3 1 0 1 1 1 1 1l-1 2h-3 0z" class="C"></path><path d="M707 290c3-1 6-1 8 0l-7 1h0c-1 1-2 1-3 2h0l-1 1v1c-1 1-3 2-5 3s-3 1-4 1c-1 1-1 1-2 1l-1-1 1-2h-2c-4 2-9 5-10 8v1c-1 1-1 2-2 4l-9 21-13 28-22 48-9 20c-1 1-2 5-3 6s-1 1-2 1c0 0-1-1-1-2l45-101c1-2 2-5 3-7l12-27 2 1c1-1 2-2 4-2 1 0 1-1 1-1h1c1-1 2-1 3-1 2 0 5-1 7-2 3-1 6-2 9-2z" class="d"></path><path d="M691 297l2-1c4-3 10-5 15-5-1 1-2 1-3 2h0l-1 1-7 2-4 1h-2z" class="B"></path><path d="M704 294v1c-1 1-3 2-5 3s-3 1-4 1c-1 1-1 1-2 1l-1-1 1-2 4-1 7-2z" class="S"></path><path d="M697 296h0l-2 3c-1 1-1 1-2 1l-1-1 1-2 4-1z" class="b"></path><path d="M670 344c1 1 2 1 3 1h2 1 1l2 2 2-1c1 1 3 1 4 1 1 1 2 1 4 2v1h3l1 2h-1l2 1v2l4 2h1l3 3 1 1-1 1c1 0 1 1 2 1 3 2 5 5 8 7l6 3-1 1-2-1v1l2 2h1-4l-1 1 5 1h-5c-3-1-6 0-8-2l-1 1h-1l-1 1h2l-1 1h-1-1c-3-6-7-10-13-13h0c0 1 0 2 1 3v1l-1-1c-2-1-2-1-4-1l-1 1c0-1 0-1-1-2v1h-2c-2 0-3 0-4-1-3-1-4-2-7-2v1h0c-2 0-3 0-5-1 1-1 2 0 3 0l-3-3v-3c1-1 1 0 1-1v-1c1 0 1 0 2-1-1 0-1 1-1 0v-1-1s-1-1-1-2c1 0 1 1 3 1h0l-1-1v-1h1 1 0c-1-1-1-1-3-1v-1l1-1c3 2 6 1 9 2h1v-1h-2c-1 0-1 0-2-1v-1c2 1 4 1 6 1v-1h-2c-1 0-2 0-3-1-1 0-3 0-4-1v-1z" class="R"></path><path d="M679 347l2-1c1 1 3 1 4 1 1 1 2 1 4 2v1h3l1 2h-1l2 1v2c-3-1-5-3-8-4-1 0-1 0-1-1h2l-1-1c-3 0-4 0-7-2z" class="L"></path><path d="M698 357h1l3 3 1 1-1 1c1 0 1 1 2 1 3 2 5 5 8 7l6 3-1 1-2-1v1l2 2h1-4l-1 1h0c-1 0-2-1-3-1h-4l-1-1v-1c2 1 3 1 5 1v-1h-3c-1-1-2-1-3-1s-1 0-2-1c2-1 4 1 7 1v-1h-2l2-2h0l1-1c-2-1-4-1-6-1s-4 0-5-1v-1l2 1h3v-1h-2l-1-1h-1 0c1-1 2 0 4 0v-1l-8-3 1-1c0-1 0-1-1-1l-1-1 1-1c1 1 1 1 2 0z" class="G"></path><path d="M664 359c1-1 1 0 1-1v-1c1 0 1 0 2-1-1 0-1 1-1 0v-1-1s-1-1-1-2c1 0 1 1 3 1h0l-1-1v-1h1 1 0c-1-1-1-1-3-1v-1l1-1c3 2 6 1 9 2v1h0c2 1 2 2 3 2 2 1 3 2 5 2v1c-2-1-4-2-6-2-2-1-3-2-4-2h-2 0l1 1c1 0 2 0 2 1 1 0 3 1 4 2 1 0 2 1 3 1h2l-2 2c-2-1-3-3-5-2h0 1c1 1 2 2 3 2 0 1 1 1 2 2 1 0 4 2 5 4h0v1h0c0 1 0 2 1 3v1l-1-1c-2-1-2-1-4-1l-1 1c0-1 0-1-1-2v1h-2c-2 0-3 0-4-1-3-1-4-2-7-2v1h0c-2 0-3 0-5-1 1-1 2 0 3 0l-3-3v-3z" class="B"></path><path d="M681 364c0-1-3-4-3-5 3 2 7 5 10 6v1h0c0 1 0 2 1 3v1l-1-1c-2-1-2-1-4-1l-1 1c0-1 0-1-1-2l-1-3z" class="d"></path><path d="M669 365v-1h3l-2-2h-1 1 2c2 1 4 1 6 2h3l1 3v1h-2c-2 0-3 0-4-1-3-1-4-2-7-2z" class="F"></path><path d="M678 364h3l1 3v1h-2c0-2 0-3-2-4z" class="C"></path><path d="M678 456l1 1c-1 1-2 3-2 5-2 3-3 7-6 10-2 5-6 8-10 12l-19 14c-2 2-4 3-6 5l-1-1-2-3 1-1v-1l6-4c-2 0-2 1-4 1h-2-1l1-2v-1h-1c-4-2-5-8-7-12v-3c0-2-1-5-1-8l3-1 1-1h-2-1c0-1 1-1 1-2 1 1 2 1 3 1l-1-1h2c2 0 4 1 6 2v-2c-2 0-3 0-4-1v-1h2v-2h0 3 0c1 0 2-1 3-1 2 1 2 3 3 4l1 1c4 2 7 4 12 3v-1 4 1c0 1 1 1 2 1h0c3-4 6-5 10-7 3-2 6-4 8-7l1-2z" class="D"></path><path d="M634 492l3-1 1 1c3 0 5-3 8-4-1 2-4 3-6 5-2 0-2 1-4 1h-2-1l1-2z" class="B"></path><path d="M663 471c1 1 1 2 1 3l-6 6 1-3-2-1h2c1-1 2-1 2-3l2-2z" class="I"></path><path d="M642 494h1l2-1c0 1-1 2-3 4v1c-2 2-4 3-6 5l-1-1-2-3 1-1c3-1 5-2 7-4l1-1v1z" class="R"></path><path d="M634 498c3-1 5-2 7-4l1-1v1l-7 8-2-3 1-1z" class="I"></path><path d="M678 456l1 1c-1 1-2 3-2 5-2 3-3 7-6 10v-1h0l-1-1-1 1v-1c0-1 1-2 1-3l-3 3h-1l1-2h0c-1 1-2 2-2 3h-2l-2 2h-1l-1-1h0c3-4 6-5 10-7 3-2 6-4 8-7l1-2z" class="d"></path><defs><linearGradient id="AU" x1="645.425" y1="491.977" x2="660.335" y2="483.596" xlink:href="#B"><stop offset="0" stop-color="#717172"></stop><stop offset="1" stop-color="#969597"></stop></linearGradient></defs><path fill="url(#AU)" d="M645 493c4-3 8-6 11-9 3-2 7-5 9-9 1-1 2-3 4-5v1l1-1 1 1h0v1c-2 5-6 8-10 12l-19 14v-1c2-2 3-3 3-4z"></path><path d="M638 460h0c1 0 2-1 3-1 2 1 2 3 3 4l1 1c4 2 7 4 12 3v-1 4 1c0 1 1 1 2 1l1 1h1c0 2-1 2-2 3h-2l2 1-1 3c-1 0-2 1-4 1-1 2-4 5-6 6l-1-1c2-1 4-3 5-4l-6 3v-1h1l2-2c0-1-1-2 0-3h2v-2h0l-3-1v-1h-1l1-1h0l1-1c-1-1-2-1-3-2s-2-2-4-2c-2-1-4-2-5-3v-2c-2 0-3 0-4-1v-1h2v-2h0 3z" class="O"></path><path d="M654 481h0l1-1v-2-1c0-1 0-2 1-2v-1c1 1 1 1 1 2h0l2 1-1 3c-1 0-2 1-4 1z" class="W"></path><path d="M638 460h0c1 0 2-1 3-1 2 1 2 3 3 4l1 1c4 2 7 4 12 3v-1 4 1h-1c-1-1-1-1-2-1s-2 0-2-1l-2-1c-1-1-2-1-3-2h-1l-3-2c-1-1-2-1-3-2h-2c-1-1-2-1-3-2h3z" class="B"></path><path d="M637 466v-2c-2 0-3 0-4-1v-1h2 0c2 1 2 2 4 2s3 2 5 3l9 5v1h-2l1 1c-1 1-1 1-2 1 1 1 1 1 2 1h1l-1 1h-1l-3-1v-1h-1l1-1h0l1-1c-1-1-2-1-3-2s-2-2-4-2c-2-1-4-2-5-3z" class="Q"></path><defs><linearGradient id="AV" x1="624.26" y1="478.233" x2="646.087" y2="479.739" xlink:href="#B"><stop offset="0" stop-color="#313234"></stop><stop offset="1" stop-color="#636464"></stop></linearGradient></defs><path fill="url(#AV)" d="M629 464h2c2 0 4 1 6 2 1 1 3 2 5 3 2 0 3 1 4 2s2 1 3 2l-1 1h0l-1 1h1v1l3 1h0v2h-2c-1 1 0 2 0 3l-2 2h-1l-2-1-1 1h2c-1 1 0 1-1 1l-1-1h-3l3 2h-3l1 2c-1 1-3 2-4 3l-1-1-1 1h-1-1c-4-2-5-8-7-12v-3c0-2-1-5-1-8l3-1 1-1h-2-1c0-1 1-1 1-2 1 1 2 1 3 1l-1-1z"></path><path d="M643 484l-2-2 1-1c1 1 1 1 3 1l1-1a30.44 30.44 0 0 0-8-8v1c-1 0-2 0-3-1l-4-3 1-1 3 2h0c5 1 7 7 11 7h2v-1c-1 0-1 0-2-1h2l3 1h0v2h-2c-1 1 0 2 0 3l-2 2h-1l-2-1-1 1h2c-1 1 0 1-1 1l-1-1z" class="K"></path><path d="M631 464c2 0 4 1 6 2 1 1 3 2 5 3 2 0 3 1 4 2s2 1 3 2l-1 1h0l-1 1h1v1h-2c1 1 1 1 2 1v1h-2c-4 0-6-6-11-7l-1-1c-2-1-3-2-4-4 1 0 1 0 2 1h0v-1c-1-1-1-1-1-2z" class="a"></path><path d="M646 395c1 0 2 0 3 1h0c2 0 4 1 6 2l2 1 3 1h1c0 1 1 1 2 1h1v1c1 0 2 1 3 1l2 2c0 1 0 2 1 3 0 1 2 1 2 1l4-1-2 1v1l3 2-1 1c0 2 0 3 1 4l1 1h0c1-1 0-1 1-1l2 1c-1 1-1 1-1 2l-1 1v1h2c1 2 1 2 0 3 0 1 0 2-1 2 0 1-1 2-1 3-1 2-1 2-1 4l-2 2h0c1 1 1 1 0 2h0c-1 1-2 1-3 3h0c-1 1-1 1-1 2-1 0-2 1-2 2l-1 1h0c-1 2-1 2-1 4l-1 1v3 2l1 1h0c1 0 2 0 3 1 0 1-1 2-2 2h-1c-1 1-1 1-1 2v2c1 0 1 1 2 1-4 2-7 3-10 7h0c-1 0-2 0-2-1v-1-4 1c-5 1-8-1-12-3l-1-1c-1-1-1-3-3-4-1 0-2 1-3 1h0l1-2s-1 0-1-1c-2 0-2 0-3-1l1-3h-2-1l2-1 3-3h0l-3 1v-1-1l-1 1h-1l1-2h0c-1 0-1 0-2 1h-1c0-1-1-1-1-2v-3s-2-1-2-2c1-2 0-7 1-9l1-1 2-6v-1l6-12c1-3 1-5 3-8l2-3h3 0c0-1-1-1-2-1h-1c0-1 1-1 1-2s1-1 1-2l1-1z" class="Z"></path><path d="M641 457h0c1 0 2 0 3-1 0 1 0 2-1 3 1 0 3-2 4-3v1l-3 3v1l1-1c1 0 1-1 2-1l-2 2 1 1 2-1-3 3-1-1c-1-1-1-3-3-4-1 0-2 1-3 1h0l1-2s1-1 2-1z" class="K"></path><path d="M660 426l2-2 1 1c-1 1-3 2-3 3 0 3-3 6-5 7l-6 7c1-2 2-3 3-5 1-4 5-9 8-11z" class="N"></path><path d="M658 443h1l2-1h2v1c-1 0-1 1-2 1-2 1-2 2-2 4l-1 1 1 1v1h-1c-2 0-3 1-4 3h-1v1c1 1 0 2 0 3l-3 2-1 1h0l2-3h-1c-1 1-2 2-2 3l-2 1-1-1 2-2c1 0 2-2 3-3l5-6h0-1c2-2 3-5 4-7z" class="e"></path><path d="M641 444c1 0 3-2 4-2-1 2-2 3-3 5h0c2 0 4-4 6-5h1c0 2-1 3-3 4 0 3-2 4-4 6l1 1v1l-2 3c-1 0-2 1-2 1s-1 0-1-1c-2 0-2 0-3-1l1-3h-2-1l2-1 3-3h0l-3 1v-1c2-1 4-2 5-4 0 0 0-1 1-1z" class="a"></path><path d="M635 452h2c1-1 2-2 4-1l-2 1 1 1 1-1c0 1-1 2-2 3h-1v1c2 0 3-1 4-3h1v1l-2 3c-1 0-2 1-2 1s-1 0-1-1c-2 0-2 0-3-1l1-3h-2-1l2-1z" class="G"></path><path d="M658 449h5l-1 2v1l-2 2c0 2 1 3-1 4v1c0 2 0 3-1 4l-1 1v1 1 1c-5 1-8-1-12-3l3-3c0-1 1-2 2-3h1l-2 3h0l1-1 3-2c0-1 1-2 0-3v-1h1c1-2 2-3 4-3h1v-1l-1-1z" class="Q"></path><path d="M658 449h5l-1 2v1l-2 2c0 2 1 3-1 4v1c-1 1-2 1-2 2l-1-1h0c1-1 2-1 3-2l-1-1c-1 1-1 1-2 1-1 2-2 2-3 3l-2 1v-1l-1-1 3-2c0-1 1-2 0-3v-1h1c1-2 2-3 4-3h1v-1l-1-1z" class="Z"></path><path d="M658 449h5l-1 2v1l-2 2h-1-4l3-3h1v-1l-1-1z" class="c"></path><path d="M666 435h1l1 1 2 1h0l2-2 2 2 1 1 1-1v1c-1 1-2 1-3 3h0c-1 1-1 1-1 2-1 0-2 1-2 2l-1 1h0-1l-1-1c-2 1-3 2-4 4h-5l1-1c0-2 0-3 2-4 1 0 1-1 2-1v-1h-2l-2 1h-1 1l-1-2c1-3 2-4 4-6h4z" class="G"></path><path d="M672 435l2 2-1 1c-1 1-2 1-3 2 0 0-1-1 0-2v-1l2-2z" class="F"></path><path d="M663 442h1c1 0 4 0 5 1-1 0-1 0-1 1v2l-1-1c-2 1-3 2-4 4h-5l1-1c0-2 0-3 2-4 1 0 1-1 2-1v-1z" class="Q"></path><path d="M666 435h1l1 1 2 1c-2 0-4 1-5 1l1 2-2 2h-1-2l-2 1h-1 1l-1-2c1-3 2-4 4-6h4z" class="N"></path><path d="M661 442c1-1 2-3 4-4l1 2-2 2h-1-2z" class="U"></path><path d="M658 441c1-3 2-4 4-6h4c-1 1-3 3-4 5l-3 3-1-2z" class="e"></path><path d="M657 420h2v3l-1 1 1 1 2-2c0-1 1-1 2-2 0 2-2 3-4 5-1 1-2 2-3 4l-1 1-7 11c-2 1-4 5-6 5h0c1-2 2-3 3-5-1 0-3 2-4 2 1-1 1-1 1-2l-2 2v-1c2-2 3-5 4-8s3-6 5-8v-1c2-1 3-2 5-3l2-2 1-1z" class="N"></path><path d="M645 442h0c3-5 6-8 9-12l1 1-7 11c-2 1-4 5-6 5h0c1-2 2-3 3-5z" class="T"></path><path d="M657 420h2v3l-1 1c-1 1-3 2-4 4l-1 1c-1 3-3 6-5 8 0-2 1-3 2-4 0-1 0-2 1-3-1 2-3 4-4 4h-1 0c1-3 3-5 3-7v-1c2-1 3-2 5-3l2-2 1-1z" class="k"></path><path d="M649 427v-1c2-1 3-2 5-3-2 4-5 7-8 11h0c1-3 3-5 3-7z" class="c"></path><path d="M663 449c1-2 2-3 4-4l1 1h1c-1 2-1 2-1 4l-1 1v3 2l1 1h0c1 0 2 0 3 1 0 1-1 2-2 2h-1c-1 1-1 1-1 2v2c1 0 1 1 2 1-4 2-7 3-10 7h0c-1 0-2 0-2-1v-1-4-1-1l1-1c1-1 1-2 1-4v-1c2-1 1-2 1-4l2-2v-1l1-2z" class="B"></path><path d="M657 465h3v1c-1 1-1 2-1 3h0v3h0c-1 0-2 0-2-1v-1-4-1z" class="C"></path><path d="M663 401h1v1c1 0 2 1 3 1l2 2c0 1 0 2 1 3 0 1 2 1 2 1l4-1-2 1v1l3 2-1 1c0 2 0 3 1 4l1 1h0c1-1 0-1 1-1l2 1c-1 1-1 1-1 2l-1 1v1h2c1 2 1 2 0 3 0 1 0 2-1 2 0 1-1 2-1 3-1 2-1 2-1 4l-2 2h0c1 1 1 1 0 2h0v-1l-1 1-1-1-2-2-2 2h0l-2-1-1-1h-1-4c-2 2-3 3-4 6l-4 4c-2 3-4 8-8 10 2-2 3-4 4-6l1-2h0v-1c1-1 1-1 1-2l-1-1c0-1 2-3 3-4 2-2 2-2 1-4 2-1 5-4 5-7 0-1 2-2 3-3l-1-1-2 2h-1c2-2 4-3 4-5-1 1-2 1-2 2l-2 2-1-1 1-1v-3h-2v-1c1-2 0-3-1-5v-1l1 2 1-1c2-1 3-1 4-2h1 3c-1-1-8-1-9-2-4 0-10 0-13 1l-1 1-1-1v-1h2c2-2 3-3 5-3s2 0 4-1c0 0 0 1 1 1h3v1c2 0 3 0 5 1h3v-1h-1l-2-2h-2l1-1c-1-1 0-1-1-2l1-1h1l1-1z" class="Q"></path><path d="M663 412c0 2-2 6-3 8 2-2 4-5 6-7-1 2-6 8-5 10l-2 2-1-1 1-1v-3h-2v-1c1-2 0-3-1-5v-1l1 2 1-1c2-1 3-1 4-2h1z" class="e"></path><path d="M663 401h1v1c1 0 2 1 3 1l2 2c0 1 0 2 1 3 0 1 2 1 2 1h0l-1 1h-1c-4 1-9 0-12-1-2 0-4 0-6-1h1 4c2 0 3 0 5 1h3v-1h-1l-2-2h-2l1-1c-1-1 0-1-1-2l1-1h1l1-1z" class="E"></path><path d="M667 422c0 1 0 2-1 3 0 1 1 1 1 2-1 3-3 6-5 8s-3 3-4 6l-4 4c-2 3-4 8-8 10 2-2 3-4 4-6l1-2h0c1-2 3-3 3-5 1-3 4-6 5-8 3-4 5-8 8-12z" class="N"></path><path d="M674 410l3 2-1 1c0 2 0 3 1 4l1 1h0c1-1 0-1 1-1l2 1c-1 1-1 1-1 2l-1 1v1h2c1 2 1 2 0 3 0 1 0 2-1 2 0 1-1 2-1 3-1 2-1 2-1 4l-2 2h0c1 1 1 1 0 2h0v-1l-1 1-1-1-2-2-2 2h0l-2-1-1-1h-1-4c2-2 4-5 5-8 0-1-1-1-1-2 1-1 1-2 1-3h0c0-1 1-2 1-3-1 0-2 2-3 3h-1c1-3 3-4 5-5 1-1 1-1 2-1 0-1 1-1 1-2h0c1-2 1-3 2-4z" class="a"></path><path d="M668 436c0-2 0-3 1-4l3 3-2 2h0l-2-1z" class="E"></path><path d="M674 410l3 2-1 1c0 2 0 3 1 4-1 1-1 1-2 1l-1 2 1 2-2 6c0-1-1-2-1-2v-2l-2 1h0c0-1 0-2 1-3 1-2 0-3-1-5l1-1c0-1 1-1 1-2h0c1-2 1-3 2-4z" class="R"></path><path d="M674 410l3 2-1 1c0 2 0 3 1 4-1 1-1 1-2 1v-3h-1 0c-2 0-2 1-3 2h-1l1-1c0-1 1-1 1-2h0c1-2 1-3 2-4z" class="B"></path><path d="M677 417l1 1h0c1-1 0-1 1-1l2 1c-1 1-1 1-1 2l-1 1v1h2c1 2 1 2 0 3 0 1 0 2-1 2 0 1-1 2-1 3-1 2-1 2-1 4l-2 2h0c1 1 1 1 0 2h0v-1c-1-2-4-3-4-5l2 1 1-1 2 1v-1-1h-2v1h-1c-2-1-2-1-2-3h1v-1l2-6-1-2 1-2c1 0 1 0 2-1z" class="E"></path><path d="M677 417l1 1h0c1-1 0-1 1-1l2 1c-1 1-1 1-1 2l-1 1v1 2h-1v1c0 1-2 2-3 2v-3c1-1 1-1 1-2h-1l-1-2 1-2c1 0 1 0 2-1z" class="G"></path><path d="M646 395c1 0 2 0 3 1h0c2 0 4 1 6 2l2 1 3 1h1c0 1 1 1 2 1l-1 1h-1l-1 1c1 1 0 1 1 2l-1 1h2l2 2h1v1h-3c-2-1-3-1-5-1v-1h-3c-1 0-1-1-1-1-2 1-2 1-4 1s-3 1-5 3h-2v1l1 1 1-1c3-1 9-1 13-1 1 1 8 1 9 2h-3-1c-1 1-2 1-4 2l-1 1-1-2v1c1 2 2 3 1 5v1l-1 1-2 2c-2 1-3 2-5 3v1c-2 2-4 5-5 8s-2 6-4 8v1l2-2c0 1 0 1-1 2-1 0-1 1-1 1-1 2-3 3-5 4v-1l-1 1h-1l1-2h0c-1 0-1 0-2 1h-1c0-1-1-1-1-2v-3s-2-1-2-2c1-2 0-7 1-9l1-1 2-6v-1l6-12c1-3 1-5 3-8l2-3h3 0c0-1-1-1-2-1h-1c0-1 1-1 1-2s1-1 1-2l1-1z" class="a"></path><path d="M656 421c-3-2-2-4-3-6l2-1h1c1 2 2 3 1 5v1l-1 1z" class="O"></path><path d="M643 425c2-3 5-4 9-5h1c0 1 0 1-1 1-2 1-4 2-5 4-2 0-2 1-4 0z" class="k"></path><path d="M638 443c1-1 1-3 2-3 1-2 2-4 4-5-1 3-2 6-4 8v1h0l-3 3-1-1 2-2v-1z" class="R"></path><path d="M642 425h1c2 1 2 0 4 0v1h1l1 1c-1 0-1 0-2 1s-1 1-1 2l-3 3c0 1 0 2-1 2v-2l3-4v-1h-2-1c1-1 1-1 1-2h-2l1-1z" class="V"></path><path d="M640 444l2-2c0 1 0 1-1 2-1 0-1 1-1 1-1 2-3 3-5 4v-1l-1 1h-1l1-2h0c-1 0-1 0-2 1h-1c0-1-1-1-1-2l2-2c1 0 2-1 3-1h0c-1 1-2 2-3 4 2-1 4-3 5-4h1v1l-2 2 1 1 3-3h0z" class="D"></path><path d="M642 418l1 1c1 0 3-1 4-1h0l-1 1c-2 1-4 3-6 5h0 1l1 1-1 1h2c0 1 0 1-1 2l-1 1c-2 0-2 0-4 1l-2-1 1-1 2-2-1-1 1-1-1-1v-1l4-1v-2c-1 0 0 0 0-1h1z" class="J"></path><path d="M640 424h1l1 1-1 1h2c0 1 0 1-1 2l-1 1c-2 0-2 0-4 1 1-2 3-3 3-5h-1l1-1z" class="K"></path><defs><linearGradient id="AW" x1="629.351" y1="434.081" x2="637.546" y2="434.922" xlink:href="#B"><stop offset="0" stop-color="#2d2d2e"></stop><stop offset="1" stop-color="#46484a"></stop></linearGradient></defs><path fill="url(#AW)" d="M630 446v-3s-2-1-2-2c1-2 0-7 1-9l1-1 2-6v1 2l1-1v1l-1 1 1 1h0 1l1-1v-1h1l-1 1 2 1c2-1 2-1 4-1-1 2-3 3-4 5-2 3-5 6-6 9 4-3 6-7 9-11-2 5-6 8-8 12l-2 2z"></path><path d="M646 395c1 0 2 0 3 1h0c2 0 4 1 6 2l2 1 3 1h1c0 1 1 1 2 1l-1 1h-1l-1 1c1 1 0 1 1 2l-1 1h2l2 2h1v1h-3c-2-1-3-1-5-1v-1h-3c-1 0-1-1-1-1-2 1-2 1-4 1s-3 1-5 3h-2v1l1 1 1-1c3-1 9-1 13-1 1 1 8 1 9 2h-3-1c-1 1-2 1-4 2l-1 1-1-2-1-1c-1 0-1 0-1 1-2-1-1 0-2-1-1 0-1-1-2 0-1 0-5 3-5 2l-1-1c-1 0-2 1-3 2h3l-1 1h-1v1c2 0 3-1 4-2s3-1 4-1h1c-1 1-2 1-3 2l-1-1c-1 1-3 3-5 3h-1c0 1-1 1 0 1v2l-4 1v1l1 1-1 1 1 1-2 2h-1v1l-1 1h-1 0l-1-1 1-1v-1l-1 1v-2-1-1l6-12c1-3 1-5 3-8l2-3h3 0c0-1-1-1-2-1h-1c0-1 1-1 1-2s1-1 1-2l1-1z" class="G"></path><path d="M646 401c2 0 3-1 4 0 0 0 1 0 2 1h0l1 1c-2 0-5-1-6 0h-1v-2z" class="U"></path><path d="M641 404c2 2 4-1 6 1-1 1 0 1-1 1-3 1-5 3-6 5h-1v2c0 1 0 1 1 2-1 2-1 2-1 5h1l1-1v2l-4 1v1l1 1-1 1 1 1-2 2h-1v1l-1 1h-1 0l-1-1 1-1v-1l-1 1v-2-1-1l6-12c1-3 1-5 3-8z" class="E"></path><path d="M639 413c0 1 0 1 1 2-1 2-1 2-1 5h1l1-1v2l-4 1v1l1 1-1 1 1 1-2 2h-1v1l-1 1h-1 0l-1-1 1-1v-1l-1 1v-2c2-2 4-4 6-7h-1l1-2h0c-1-1 0-3 1-4z" class="B"></path><path d="M669 365c3 0 4 1 7 2 1 1 2 1 4 1h2v-1c1 1 1 1 1 2l1-1c2 0 2 0 4 1l1 1v-1c-1-1-1-2-1-3h0c6 3 10 7 13 13 1 2 3 6 2 8 1 2 2 4 3 5s2 1 2 1c-1 1-4 1-5 1l-2 1v3l-1 1c0 1 1 1 1 2v1l3 3v1c1 2 3 3 4 5h1c1 1 3 4 4 5l-2 2 2 2c-1 1-2 1-3 1 0 3 1 3-1 6h-2-1c-6-1-8 0-14 2 2 0 5 0 7 1h-4l-1 1c1 1 1 2 2 3h0c0 2 0 2 1 4-1 0-1 1-2 1-1 1-1 1-1 2v1h-3c0 1 1 1 1 1 1 1 0 2 2 3 1 0 3 0 4 1-4 0-7 0-11 2-4 1-7 4-9 7l-1 2c-2 3-5 5-8 7-1 0-1-1-2-1v-2c0-1 0-1 1-2h1c1 0 2-1 2-2-1-1-2-1-3-1h0l-1-1v-2-3l1-1c0-2 0-2 1-4h0l1-1c0-1 1-2 2-2 0-1 0-1 1-2h0c1-2 2-2 3-3h0c1-1 1-1 0-2h0l2-2c0-2 0-2 1-4 0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-1 0-3h-2v-1l1-1c0-1 0-1 1-2l-2-1c-1 0 0 0-1 1h0l-1-1c-1-1-1-2-1-4l1-1-3-2v-1l2-1-4 1s-2 0-2-1c-1-1-1-2-1-3l-2-2c-1 0-2-1-3-1v-1h-1c-1 0-2 0-2-1h-1l-3-1-2-1c-2-1-4-2-6-2h0c-1-1-2-1-3-1l1-2v-1l5-11c1-3 4-5 5-8 0-1 1-2 1-3l2-1h0 3l1 1 1-1 1 1c0 1-1 1-2 1v2c2-1 9-2 11-5-2-2-3-2-6-2v-1z" class="k"></path><path d="M685 377h-4c0-1 0-1 1-1-1-1-1-1-2-1h-1c1-2 3-1 5-1s6 2 8 3l1 2 1 1-1 3h1l-1 1-2-2c-1-1-1-2-2-2v-1c-1-1-3-1-4-2z" class="P"></path><path d="M693 383v-1c-1-2-2-3-4-4v-1h0c1 0 1 1 2 1l1-1 1 2 1 1-1 3z" class="M"></path><path d="M688 366c6 3 10 7 13 13 1 2 3 6 2 8 1 2 2 4 3 5-1 0-2 0-3-1v-3c-1-2-2-3-2-5 0-1 0-2-1-3l-2-2c-1-3-3-5-6-6l-1-1c-3-1-6-1-10-2h-1l2-1v-1c1 1 1 1 1 2l1-1c2 0 2 0 4 1l1 1v-1c-1-1-1-2-1-3h0z" class="g"></path><path d="M685 377c1 1 3 1 4 2-1 1-1 2-1 3h1v2h1c0 2 0 2-1 3l-2 2-2 1c-1 0-1 0-3-1h0l-2-2c1-5 2-7 5-10z" class="M"></path><path d="M682 389c0-2-1-3 1-4 0 0 0-1 1-1 2 1 2 3 3 5l-2 1c-1 0-1 0-3-1z" class="g"></path><path d="M672 381c1-4 3-5 6-7v1c-1 1-1 1-1 2h1 1l1 1v2c-1 2-1 5-1 8 2 2 3 4 6 5h1l1-2c1 0 1 0 2-1l3-3h1l1 1v-1c1-1 1-3 1-5 1 2 1 6 0 8l-1 1h-1c-2 1-4 3-6 3s-4 0-6-1c-2-2-4-6-5-9v-4l-3 1h-1z" class="i"></path><path d="M672 381h1l3-1v4c1 3 3 7 5 9 2 1 4 1 6 1s4-2 6-3h1l1-1h1l1 1h1c0 1 0 2-1 3-1 0-2 1-3 1-2 1-6 2-8 3-4 0-6-4-9-6h0c-1-2-1-3-2-4v-2c-1-1-1-2-1-3l-2 1-1-1v-1l1-1z" class="G"></path><path d="M693 379l1-1c2 1 2 1 3 3h-1c1 1 3 3 3 4 1 1 0 5 0 6 1-1 0-4 1-4 1 2 1 5 1 7h2l-2 1v3l-1 1c0 1 1 1 1 2v1l3 3v1c1 2 3 3 4 5h1c1 1 3 4 4 5l-2 2c0-1-1-1-1-2-1-1-1-2-2-3l-1-1c-2-4-5-4-8-7v-1h0v-3l-1-1c1-1 1-2 1-3 1-2 1-3 1-5l-1 2c-1 2-3 3-5 5h0c1-2 2-3 3-5h0c1-1 1-2 1-3h-1l-1-1h-1c1-2 1-6 0-8l-1-2-1-1z" class="a"></path><path d="M670 385l-1-1h1 1c1 1 1 0 2 2l2 2c0 2 0 2 1 3l1 1h0c3 2 5 6 9 6 2-1 6-2 8-3 1 0 2-1 3-1h0c-1 2-2 3-3 5h0c2-2 4-3 5-5l1-2c0 2 0 3-1 5 0 1 0 2-1 3l1 1v3h-1v1c-2 0-1-1-2-1s-4 1-5 1l-1 1h-2c1-1 2-1 3-2l1-1c1 0 1 0 1-1v-1l-2 1c-1 0-1 0-2 1-3 1-6 0-9 0l-1-1c-2 0-3-1-5-2-1 0-2 1-2 0h-1v1 1-1c-2-1-4-3-5-5v-1c0-2 0-4-1-6h0c1-1 1-2 2-2l1-1h0l1 1 1-1v-1z" class="F"></path><path d="M670 385c1 1 1 2 1 3-1 1-1 2-1 3l-1 1-1-5h-1l1-1h0l1 1 1-1v-1z" class="L"></path><path d="M667 387h1l1 5c0 3 3 6 5 8-1 0-2 1-2 0h-1v1 1-1c-2-1-4-3-5-5v-1c0-2 0-4-1-6h0c1-1 1-2 2-2z" class="H"></path><path d="M664 401v-1h-2v-3l2 1 2-2c1 2 3 4 5 5v1-1-1h1c0 1 1 0 2 0 2 1 3 2 5 2l1 1c3 0 6 1 9 0 1-1 1-1 2-1l2-1v1c0 1 0 1-1 1l-1 1c-1 1-2 1-3 2h2c2 0 4 1 6 2 0 1 0 1 1 2 2 1 4 2 4 3h-4c-2-1-3 0-5 0l-3 2-2 2h0c-2 1-2 2-3 4h-2l-1 1h-2v-1l1-1c0-1 0-1 1-2l-2-1c-1 0 0 0-1 1h0l-1-1c-1-1-1-2-1-4l1-1-3-2v-1l2-1-4 1s-2 0-2-1c-1-1-1-2-1-3l-2-2c-1 0-2-1-3-1v-1z" class="b"></path><path d="M689 415c0-1-1-2 0-4h1l2 2-3 2z" class="H"></path><path d="M676 408c1-1 1-1 3-1l-1 1 1 2v1c2-1 2-1 4 0 1 0 2 0 3 2h0l1 4h0c-2 1-2 2-3 4h-2l-1 1h-2v-1l1-1c0-1 0-1 1-2l-2-1c-1 0 0 0-1 1h0l-1-1c-1-1-1-2-1-4l1-1-3-2v-1l2-1z" class="F"></path><path d="M676 408c1-1 1-1 3-1l-1 1 1 2v1c2-1 2-1 4 0 1 0 2 0 3 2h0c-1 1-1 2-2 3h-1v-1c0-2-1-2-2-3h-2c-1-1-1-1-1-2v-1c-1 0-2 1-3 1l-1-1 2-1z" class="H"></path><path d="M669 365c3 0 4 1 7 2 1 1 2 1 4 1h2l-2 1h1c0 2 0 2-1 3h0l-1 1-1 1c-3 2-5 3-6 7l-1 1v1l1 1 2-1c0 1 0 2 1 3v2c1 1 1 2 2 4l-1-1c-1-1-1-1-1-3l-2-2c-1-2-1-1-2-2h-1-1l1 1v1l-1 1-1-1h0l-1 1c-1 0-1 1-2 2h0c1 2 1 4 1 6v1l-2 2-2-1v3h2v1h-1c-1 0-2 0-2-1h-1l-3-1-2-1c-2-1-4-2-6-2h0c-1-1-2-1-3-1l1-2v-1l5-11c1-3 4-5 5-8 0-1 1-2 1-3l2-1h0 3l1 1 1-1 1 1c0 1-1 1-2 1v2c2-1 9-2 11-5-2-2-3-2-6-2v-1z" class="E"></path><path d="M662 380l1 1c-1 1-1 2-1 2h-2v2 2h-1c0 1 1 2 2 2 0 0 1 0 1-1 1 1 1 1 0 2h-1c-1 0-2 1-3 2v1c1 0 2-1 2-1h2v1c-2 1-2 2-2 3-1 0-2-1-3-2 0-1 0-2-1-3 0-2 0-3-1-5h0 1v-3c1-1 1-1 0-2 3 1 4 0 6-1z" class="G"></path><path d="M669 365c3 0 4 1 7 2 1 1 2 1 4 1h2l-2 1h1c0 2 0 2-1 3h0l-1 1c-1-1-3-1-5 0-4 2-7 5-8 9-1 2-1 5-1 7h0c-1 0-1-1-1-1 1-2 1-5 1-7 1-3 4-7 7-9h0 0-1c-1 1-3 1-4 1l-5 1v-1h2c2-1 9-2 11-5-2-2-3-2-6-2v-1z" class="N"></path><path d="M665 389c0-2 0-5 1-7 1-4 4-7 8-9 2-1 4-1 5 0l-1 1c-3 2-5 3-6 7l-1 1v1l1 1 2-1c0 1 0 2 1 3v2c1 1 1 2 2 4l-1-1c-1-1-1-1-1-3l-2-2c-1-2-1-1-2-2h-1-1l1 1v1l-1 1-1-1h0l-1 1c-1 0-1 1-2 2z" class="E"></path><path d="M647 392h2l1-1c0 1 1 2 2 2h0v-2c0-3 1-2 2-3 0 0 0-2 1-2h0c1 2 1 3 1 5 1 1 1 2 1 3 1 1 2 2 3 2 0-1 0-2 2-3v-1h0 1v-4h1s0 1 1 1c1 2 1 4 1 6v1l-2 2-2-1v3h2v1h-1c-1 0-2 0-2-1h-1l-3-1-2-1c-2-1-4-2-6-2h0c-1-1-2-1-3-1l1-2v-1z" class="C"></path><path d="M647 393c1 0 1 0 3 1 0 1 2 1 3 1s3 0 3 1c1 1 1 2 1 3l-2-1c-2-1-4-2-6-2h0c-1-1-2-1-3-1l1-2z" class="B"></path><path d="M658 370l2-1h0 3l1 1 1-1 1 1c0 1-1 1-2 1v2h-2v1l5-1c-1 3-2 4-4 6l-1 1h0c-2 1-3 2-6 1 1 1 1 1 0 2v3h-1c-1 0-1 2-1 2-1 1-2 0-2 3v2h0c-1 0-2-1-2-2l-1 1h-2l5-11c1-3 4-5 5-8 0-1 1-2 1-3z" class="O"></path><path d="M662 374l5-1c-1 3-2 4-4 6l-1 1h0c-2 1-3 2-6 1h-1c1-1 1-1 1-2 1-2 3-3 4-4 0 0 1 0 1-1h1z" class="I"></path><path d="M699 404h0v1c3 3 6 3 8 7l1 1c1 1 1 2 2 3 0 1 1 1 1 2l2 2c-1 1-2 1-3 1 0 3 1 3-1 6h-2-1c-6-1-8 0-14 2 2 0 5 0 7 1h-4l-1 1c1 1 1 2 2 3h0c0 2 0 2 1 4-1 0-1 1-2 1-1 1-1 1-1 2v1h-3c0 1 1 1 1 1 1 1 0 2 2 3 1 0 3 0 4 1-4 0-7 0-11 2-4 1-7 4-9 7l-1 2c-2 3-5 5-8 7-1 0-1-1-2-1v-2c0-1 0-1 1-2h1c1 0 2-1 2-2-1-1-2-1-3-1h0l-1-1v-2-3l1-1c0-2 0-2 1-4h0l1-1c0-1 1-2 2-2 0-1 0-1 1-2h0c1-2 2-2 3-3h0c1-1 1-1 0-2h0l2-2c0-2 0-2 1-4 0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-1 0-3l1-1h2c1-2 1-3 3-4h0l2-2 3-2c2 0 3-1 5 0h4c0-1-2-2-4-3-1-1-1-1-1-2-2-1-4-2-6-2l1-1c1 0 4-1 5-1s0 1 2 1v-1h1z" class="W"></path><path d="M685 427v3h2 1l-1 1 2 2 1-1v-2h0c1-1 1-2 2-3h0c1-1 3-2 5-2 2-1 4 0 7-2 1 1 1 3 2 4-6-1-8 0-14 2 2 0 5 0 7 1h-4l-1 1c1 1 1 2 2 3h0c0 2 0 2 1 4-1 0-1 1-2 1-1 1-1 1-1 2h-2c0-1 0-2-1-2 0-1-1-2-1-3s0-1 1-3h0-3c1 1 1 1 1 2h-1c-1-1-1-2-2-3l-1 1-1 3v1c-1-1-1-1-1-2l-2 1 1-2c0-1 0-3 1-4 0-2 1-3 2-3z" class="C"></path><path d="M699 404h0v1c3 3 6 3 8 7l1 1c1 1 1 2 2 3 0 1 1 1 1 2l2 2c-1 1-2 1-3 1 0 3 1 3-1 6h-2c1-1 1-1 1-2l1-1-2-2c-1-3-3-4-4-6v-1h-1v1h-1c-2-1-2-1-4-1-1 2-4 3-7 4h0l-2-1 1-2c2-1 5-2 8-2h1l-1-1h4c0-1-2-2-4-3-1-1-1-1-1-2-2-1-4-2-6-2l1-1c1 0 4-1 5-1s0 1 2 1v-1h1z" class="F"></path><path d="M705 415l3-2c1 1 1 2 2 3 0 1 1 1 1 2l2 2c-1 1-2 1-3 1h0c-2-2-3-4-5-6z" class="f"></path><path d="M699 404h0v1c3 3 6 3 8 7l1 1-3 2c-3-4-5-6-9-7-2-1-4-2-6-2l1-1c1 0 4-1 5-1s0 1 2 1v-1h1z" class="M"></path><path d="M692 413c2 0 3-1 5 0l1 1h-1c-3 0-6 1-8 2l-1 2 2 1h0c2 1 2 0 3 1s2 1 3 1v1h-1c-3 1-6 4-10 5-1 0-2 1-2 3-1 1-1 3-1 4l-1 2v1c-1 2-2 3-3 4-2 1-2 2-3 3-2 2-5 4-6 6l-2 4v-3l1-1c0-2 0-2 1-4h0l1-1c0-1 1-2 2-2 0-1 0-1 1-2h0c1-2 2-2 3-3h0c1-1 1-1 0-2h0l2-2c0-2 0-2 1-4 0-1 1-2 1-3 1 0 1-1 1-2 1-1 1-1 0-3l1-1h2c1-2 1-3 3-4h0l2-2 3-2z" class="D"></path><path d="M690 419h0c2 1 2 0 3 1s2 1 3 1v1h-1-1c-1 0-1 0-2 1-2 1-3 2-5 2l2-2v-1c-1 1-1 1-2 1l3-3v-1z" class="I"></path><path d="M681 436l2-1c0 1 0 1 1 2v-1l1-3 1-1c1 1 1 2 2 3h1c0-1 0-1-1-2h3 0c-1 2-1 2-1 3s1 2 1 3c1 0 1 1 1 2h2v1h-3c0 1 1 1 1 1 1 1 0 2 2 3 1 0 3 0 4 1-4 0-7 0-11 2-4 1-7 4-9 7l-1 2c-2 3-5 5-8 7-1 0-1-1-2-1v-2c0-1 0-1 1-2h1c1 0 2-1 2-2-1-1-2-1-3-1h0l-1-1v-2l2-4c1-2 4-4 6-6 1-1 1-2 3-3 1-1 2-2 3-4v-1z" class="B"></path><path d="M682 439c1 0 2 1 3 2h1v-1-3h1c0 1 0 1 1 1v-1h1c0 1 0 1 1 3 0 2-1 3-3 4-2 0-2 0-4 1h0c-1 1-2 1-3 1 1-1 2-1 2-3-1 0-1 0-1-1v-2l1-1z" class="E"></path><path d="M681 437c0 1 0 1 1 2l-1 1v2c0 1 0 1 1 1 0 2-1 2-2 3l-1 2v1h1l-3 3c0 1 0 1-1 2 0 1-2 2-4 3l1 1v-1h2c1 0 1 1 2 1-2 3-5 5-8 7-1 0-1-1-2-1v-2c0-1 0-1 1-2h1c1 0 2-1 2-2-1-1-2-1-3-1h0l-1-1v-2l2-4c1-2 4-4 6-6 1-1 1-2 3-3 1-1 2-2 3-4z" class="C"></path><path d="M675 447l1-1v2l1 1-1 1h-2c0-1 0-1-1-2l2-1z" class="F"></path><path d="M681 440v2c0 1 0 1 1 1 0 2-1 2-2 3l-1 2h0l-1-2v-1c-1-1 2-4 3-5z" class="S"></path><path d="M713 416c-1-1-3-4-4-5h-1c-1-2-3-3-4-5v-1l-3-3v-1h1l6 6c9 9 14 19 17 31l1 9c1 8 1 16 0 24-4 26-17 47-38 62l-3 2c-8 5-15 9-24 12l-1-1-19 3-13-1c-4 0-8-1-12-2-4 0-6-3-9-4h-3c-4-1-10-7-13-10-5-4-9-10-12-16-2-3-3-6-4-9-1-2-3-5-4-8-1-1 0-2 1-4h3v-1c1-1 2-2 3-2 2-1 4 1 5 1l3 3 2 4h1v4c1-1 1-2 2-3h0l1-5 6-15 8-17 1-1v3h2l-1-4 2-1c0-2 0-4-1-6v-1c1 0 1 0 2 1 0-5 0-10 2-15 0-1 0-2 1-2v-4c1 2 1 2 1 4h1v2c0 3 2 5 3 7s2 3 3 4c1 0 2 0 3 1-2 3-3 5-4 9v1c-1 11 5 28 12 37h0l2 3 1 1c2-2 4-3 6-5l19-14c4-4 8-7 10-12 3-3 4-7 6-10 0-2 1-4 2-5l-1-1c2-3 5-6 9-7 4-2 7-2 11-2-1-1-3-1-4-1-2-1-1-2-2-3 0 0-1 0-1-1h3v-1c0-1 0-1 1-2 1 0 1-1 2-1-1-2-1-2-1-4h0c-1-1-1-2-2-3l1-1h4c-2-1-5-1-7-1 6-2 8-3 14-2h1 2c2-3 1-3 1-6 1 0 2 0 3-1l-2-2 2-2z" class="H"></path><g class="C"><path d="M603 478h0l4 2v1 1h1v1h-5-1c0-2 1-3 1-5z"></path><path d="M602 483v1c1 0 1 0 2 1-1 0-1 1-2 1v1h2l-3 4c-1 1-2 2-3 2 1-2 2-4 2-6 1-2 1-3 2-4z"></path></g><path d="M606 510l2-1 1-1c-1-1-1-2-2-2l2-2-1 2c1 0 2 0 2 1s1 2 2 3h1v1 1l-2-2-1 1v3 1c-2-2-3-3-4-5z" class="E"></path><path d="M601 522c2-1 4-2 6-1 1 1 2 1 2 3-1 2-1 2-3 3-2-1-3-3-5-5z" class="Q"></path><path d="M613 510l3-1h0l-1 2h2c-1 1-2 3-3 4l-1 1c0 1-1 1-1 3-1-2-2-3-2-4v-1-3l1-1 2 2v-1-1z" class="I"></path><path d="M609 524c1 0 1 1 1 2 2 5 9 8 13 10l-1 1c-2-1-4-2-7-3-1-1-2-2-3-2-2 0-2-1-3-1-1-2-2-3-3-4 2-1 2-1 3-3z" class="D"></path><path d="M595 517l2 2v1 1c1 0 2 1 3 2l1-1c2 2 3 4 5 5 1 1 2 2 3 4h-2l3 3h0-1c-7-4-12-10-14-17z" class="F"></path><path d="M668 500c1 1 2 1 3 2v-1l5 4c1 0 2 1 2 2h0c-1-1-2-1-3-2l-2-1v1h-3c-2 2-2 4-4 6-1 1-4 2-5 2l-1-1h0c1-2 4-5 6-7h0c1-2 2-3 2-5z" class="B"></path><path d="M668 500c1 1 2 1 3 2l-1 1h0c-1 0-1 0-1 1-2 3-5 5-7 7-1 1-1 1-2 1h0c1-2 4-5 6-7h0c1-2 2-3 2-5z" class="I"></path><path d="M598 481l8-17 1-1v3c-1 4-2 8-4 12 0 2-1 3-1 5-1 1-1 2-2 4 0 2-1 4-2 6l-3 6v-1c-1-1 0-4 0-6-1 2-2 7-4 9l1-5 6-15z" class="M"></path><path d="M666 497h0c1 1 1 0 1 1 1 0 1 0 2-1l1 1-2 2c0 2-1 3-2 5h0c-2 2-5 5-6 7l-1-1h0l-4 4h1l1 1 1-1 1 1h3l1-1h1 1c1-2 2-3 3-4 1 0 2 1 3 1h1l1-1h2v1c-2 0-3 1-4 1l-6 3c-7 1-12-1-18-4 1-1 2-1 3 0 3-1 5-5 7-7h1c1 0 6-4 6-5 1-1 0-1 1-3h1z" class="C"></path><path d="M665 516c1-2 2-3 4-4l1 1h1l-6 3z" class="B"></path><path d="M666 497h0c1 1 1 0 1 1 1 0 1 0 2-1l1 1-2 2-13 14-1-1s3-3 3-4c-2 2-4 4-7 3h0c3-1 5-5 7-7h1c1 0 6-4 6-5 1-1 0-1 1-3h1z" class="G"></path><defs><linearGradient id="AX" x1="622.77" y1="527.212" x2="619.358" y2="542.091" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#bbb"></stop></linearGradient></defs><path fill="url(#AX)" d="M591 501c2-2 3-7 4-9 0 2-1 5 0 6v1c-1 2-3 6-3 8l1 1c1-2 1-2 2-3 1 1 0 3 0 5-1 2-1 5 0 7 2 7 7 13 14 17h1c12 8 27 9 41 9h9l13-4h0c2-2 3-2 5-1-6 3-12 6-18 8l-19 3-13-1 2-1v-1h-2v-1c-13-3-26-9-34-20-2-4-4-7-6-11 0-3 1-6 0-9v-5h1v4c1-1 1-2 2-3h0z"></path><path d="M673 539h0c2-2 3-2 5-1-6 3-12 6-18 8l-19 3-13-1 2-1v-1h-2v-1c8 1 18 2 26 0 5 0 16-2 19-6z" class="G"></path><path d="M575 495v-1c1-1 2-2 3-2 2-1 4 1 5 1l3 3 2 4v5c1 3 0 6 0 9 2 4 4 7 6 11 8 11 21 17 34 20v1h2v1l-2 1c-4 0-8-1-12-2-4 0-6-3-9-4h-3c-4-1-10-7-13-10-5-4-9-10-12-16-2-3-3-6-4-9-1-2-3-5-4-8-1-1 0-2 1-4h3z" class="L"></path><path d="M575 495v-1c1-1 2-2 3-2 2-1 4 1 5 1l3 3c-1 1-2 2-3 2l-1-2h-2c-1 2-1 4-1 6l-1 1 1 2c0 2 0 3 1 4 0 1 1 2 1 3-3-3-3-7-5-10-1-2-1-4-1-7z" class="F"></path><path d="M579 502c0-2 0-4 1-6h2l1 2c1 0 2-1 3-2l2 4v5c1 3 0 6 0 9l-1 1c1 2 2 4 3 5 1 3 3 6 5 8-7-6-15-15-16-26h0z" class="l"></path><path d="M579 502c0-2 0-4 1-6h2l1 2 1 11c-1-1-3-2-3-4l1-2c0-2-1-3-2-4h0l-1 3h0z" class="c"></path><path d="M586 496l2 4v5c1 3 0 6 0 9l-1 1-3-6-1-11c1 0 2-1 3-2z" class="H"></path><path d="M591 532c-5-4-9-10-12-16-2-3-3-6-4-9-1-2-3-5-4-8-1-1 0-2 1-4h3c0 3 0 5 1 7 3 15 16 30 29 38l11 6c-4 0-6-3-9-4h-3c-4-1-10-7-13-10z" class="P"></path><path d="M613 440c0-1 0-2 1-2v-4c1 2 1 2 1 4h1v2c0 3 2 5 3 7s2 3 3 4c1 0 2 0 3 1-2 3-3 5-4 9v1c-1 11 5 28 12 37h0l2 3 1 1c2 3 6 6 9 8l2 1c6 3 11 5 18 4l6-3c1 0 2-1 4-1-3 3-6 5-9 7h-1c-2 1-8 2-11 1-5-1-10-3-15-6l-6-5h-1c-2-2-6-6-7-8-9-10-14-22-16-35l-1-4 2-1c0-2 0-4-1-6v-1c1 0 1 0 2 1 0-5 0-10 2-15z" class="B"></path><path d="M613 455c0 1 0 3 1 5 0 5 1 10 3 15v3l-1-1c-3-5-5-12-4-18l1-4z" class="D"></path><defs><linearGradient id="AY" x1="609.961" y1="461.039" x2="609.539" y2="471.461" xlink:href="#B"><stop offset="0" stop-color="#a6a5a7"></stop><stop offset="1" stop-color="#c4c3c4"></stop></linearGradient></defs><path fill="url(#AY)" d="M608 462l2-1c0-2 0-4-1-6v-1c1 0 1 0 2 1 0 14 2 24 8 37 2 3 5 6 6 9-9-10-14-22-16-35l-1-4z"></path><path d="M613 440c0-1 0-2 1-2v-4c1 2 1 2 1 4h1v2c0 3 2 5 3 7s2 3 3 4c-3 2-3 6-4 10-1 9 2 18 4 27 0-1-2-2-2-2-1-2-2-6-3-8v-3c-2-5-3-10-3-15-1-2-1-4-1-5 1-5 0-10 1-14h0l-1-1z" class="c"></path><path d="M614 460v-1c1-5 2-11 1-16h0 0c1 1 1 2 1 2v1c1 1 0 1 1 2 0 1 0 1 1 2v1c0 2 0 4-1 6 0 5-1 10 0 15v3c-2-5-3-10-3-15z" class="M"></path><defs><linearGradient id="AZ" x1="648.811" y1="482.017" x2="623.372" y2="489.063" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#cecdce"></stop></linearGradient></defs><path fill="url(#AZ)" d="M622 451c1 0 2 0 3 1-2 3-3 5-4 9v1c-1 11 5 28 12 37h0l2 3 1 1c2 3 6 6 9 8l2 1c6 3 11 5 18 4l6-3c1 0 2-1 4-1-3 3-6 5-9 7-7 0-13-1-18-4-11-5-22-16-26-27-2-9-5-18-4-27 1-4 1-8 4-10z"></path><path d="M713 416c-1-1-3-4-4-5h-1c-1-2-3-3-4-5v-1l-3-3v-1h1l6 6c9 9 14 19 17 31l1 9c1 8 1 16 0 24-4 26-17 47-38 62l-3 2c-8 5-15 9-24 12l-1-1c6-2 12-5 18-8 3-2 6-4 10-6 4-3 8-7 11-10 1-1 2-2 2-3-11 9-23 16-38 19-16 3-30 1-44-9-6-4-13-13-14-20l1 1c1 2 2 3 4 5 0 1 1 2 2 4 7 8 18 14 28 15 8 2 16 2 24 0 3 0 5-1 8-2l2-6 2 1-1 4h1c6-3 13-6 18-11-1-1-1-1-1-2l10-27c-1-1-1 0-1-1s0-3 1-4v-1l-1-1v-1c1-2 3-6 3-8l2-2v1h0c1-1 2-2 2-3l1-2c2-2 2-6 1-9v-1l-3-6h-1c-2-3-4-5-8-6h-1c-1-1-3-1-4-1-2-1-1-2-2-3 0 0-1 0-1-1h3v-1c0-1 0-1 1-2 1 0 1-1 2-1-1-2-1-2-1-4h0c-1-1-1-2-2-3l1-1h4c-2-1-5-1-7-1 6-2 8-3 14-2h1 2c2-3 1-3 1-6 1 0 2 0 3-1l-2-2 2-2z" class="d"></path><path d="M713 416c9 16 12 34 10 51 0 2 0 3-1 5-1-3 0-7 0-10 0-6-2-11-3-16-2-6-3-11-7-15l-1-1c2-2 3-5 4-8l-2-2-2-2 2-2z" class="F"></path><defs><linearGradient id="Aa" x1="716.433" y1="442.034" x2="724.573" y2="441.134" xlink:href="#B"><stop offset="0" stop-color="#adacad"></stop><stop offset="1" stop-color="#d1d1d2"></stop></linearGradient></defs><path fill="url(#Aa)" d="M713 416c-1-1-3-4-4-5h-1c-1-2-3-3-4-5v-1l-3-3v-1h1l6 6c9 9 14 19 17 31l1 9c1 8 1 16 0 24-1 0-1 1-2 1 0-1 1-2 0-3 0-1 0-1-1-2 2-17-1-35-10-51z"></path><path d="M699 430c5 1 10 3 12 7 4 5 6 11 7 16 4 14 3 32-4 44-4 8-9 14-15 19-2 1-4 3-5 4-1-1-1-1-1-2l10-27c-1-1-1 0-1-1s0-3 1-4v-1l-1-1v-1c1-2 3-6 3-8l2-2v1h0c1-1 2-2 2-3l1-2c2-2 2-6 1-9v-1l-3-6h-1c-2-3-4-5-8-6h-1c-1-1-3-1-4-1-2-1-1-2-2-3 0 0-1 0-1-1h3v-1c0-1 0-1 1-2 1 0 1-1 2-1-1-2-1-2-1-4h0c-1-1-1-2-2-3l1-1h4z"></path><path d="M707 482l2 1-3 7-12 30c-1-1-1-1-1-2l10-27 1-2 3-7z" class="T"></path><path d="M707 482l2 1-3 7v-2-2c0 1-1 2-2 2v1l3-7z" class="i"></path><path d="M699 430c5 1 10 3 12 7 4 5 6 11 7 16h-1c-2-4-2-9-5-13l-1 1v1c1 3-2 6-1 8 0 1-1 1 0 2l2 2h-1-1c1 2 2 5 2 8h1v-2h1v-3c0-1-1-1-1-2h0c-1-1-1-1 0-1v-1-2h0c1 1 1 2 1 3v1h0v1c2 3 1 9 0 11-1 6-3 11-5 16l-2-1-3 7-1 2c-1-1-1 0-1-1s0-3 1-4v-1l-1-1v-1c1-2 3-6 3-8l2-2v1h0c1-1 2-2 2-3l1-2c2-2 2-6 1-9v-1l-3-6h-1c-2-3-4-5-8-6h-1c-1-1-3-1-4-1-2-1-1-2-2-3 0 0-1 0-1-1h3v-1c0-1 0-1 1-2 1 0 1-1 2-1-1-2-1-2-1-4h0c-1-1-1-2-2-3l1-1h4z" class="b"></path><path d="M707 482c2-5 5-9 6-14h0l1-1c-1 6-3 11-5 16l-2-1z" class="Z"></path><path d="M702 441c0 1 2 3 2 4 1 1 1 2 1 3h1c1-1 1-2 2-3l1-2h1c1 3-2 7-2 10h-1c-2-3-4-5-8-6 1 0 2-1 3-1h1c0-2-1-2-2-3l1-2z" class="B"></path><path d="M696 434l1 3 1 1c1 0 2 1 2 1l2 2h0l-1 2c1 1 2 1 2 3h-1c-1 0-2 1-3 1h-1c-1-1-3-1-4-1-2-1-1-2-2-3 0 0-1 0-1-1h3v-1c0-1 0-1 1-2 1 0 1-1 2-1-1-2-1-2-1-4z" class="S"></path><defs><linearGradient id="Ab" x1="686.928" y1="465.508" x2="671.036" y2="528.242" xlink:href="#B"><stop offset="0" stop-color="#090808"></stop><stop offset="1" stop-color="#282829"></stop></linearGradient></defs><path fill="url(#Ab)" d="M687 449c4-2 7-2 11-2h1c4 1 6 3 8 6h1l3 6v1c1 3 1 7-1 9l-1 2c0 1-1 2-2 3h0v-1l-2 2c0 2-2 6-3 8v1l1 1v1c-1 1-1 3-1 4s0 0 1 1l-10 27c0 1 0 1 1 2-5 5-12 8-18 11h-1l1-4-2-1 6-17v-1h-2v-1h0c0-1-1-2-2-2l-5-4v1c-1-1-2-1-3-2l2-2-1-1c-1 1-1 1-2 1 0-1 0 0-1-1h0-1c-1 2 0 2-1 3 0 1-5 5-6 5h-1c-2 2-4 6-7 7-1-1-2-1-3 0l-2-1c-3-2-7-5-9-8 2-2 4-3 6-5l19-14c4-4 8-7 10-12 3-3 4-7 6-10 0-2 1-4 2-5l-1-1c2-3 5-6 9-7z"></path><path d="M681 454c1 1 1 2 2 2h1c0 1 0 0-1 1-1 2-2 3-2 4-2 1-3 2-3 3v4h-1v-6c0-2 1-4 2-5l2-3z" class="E"></path><path d="M673 494v1l5-5c0 2-1 3-2 4l-5 5v1h3c1 1 2 1 2 3v2l-5-4v1c-1-1-2-1-3-2l2-2c1-2 2-3 3-4zm25-1l1 1v1l-1 2v1c-1 2-1 4-2 6v3l-1 1c0 1 0 2-1 3l-1 3v-3c0-1-1-2-2-3 1-2 3-3 4-5l3-10z" class="L"></path><path d="M678 470c1 2 2 3 4 5-1 1-3 2-4 2h-1c-1 0-2 0-3 1-2 1-3 2-6 2l1-1c4-3 6-6 9-9z" class="C"></path><path d="M678 481h2v4l1-1h1c-2 2-3 4-5 5l1 1h0 1 0v1l2-1h1l-1 1c-1 1-4 6-5 6v-2-1c1-1 2-2 2-4l-5 5v-1l2-2c1 0 1-1 2-2-1-1-1-1-2-1h-2v-1l5-3c1-1 1-2 1-3l-1-1zm24 3l1 1v1c-1 1-1 3-1 4s0 0 1 1l-10 27v-2-2l1-3c1-1 1-2 1-3l1-1v-3c1-2 1-4 2-6v-1l1-2v-1l-1-1 2-5c1-2 1-3 2-4z" class="H"></path><path d="M671 472c3-3 4-7 6-10v6h1v2c-3 3-5 6-9 9l-1 1c-1 2-3 3-4 4h-3c4-4 8-7 10-12z" class="D"></path><path d="M690 492v-1c1 0 1-1 2-2 0-1 1-2 1-3l1 1h-1v4h1c1 2 0 4 0 6-1 3-2 5-2 7l-3 1v1c-1 3-3 6-4 8v1c-1-1-1 0-1-1 1-3 3-5 4-8l1-4v-3l1-1c0-1-1-1-1-2l1-4z" class="L"></path><path d="M680 509c3-8 7-17 11-24v1l-1 6-1 4c-1 5-4 10-6 15-2 6-5 11-7 16l-2-1 6-17z" class="Y"></path><path d="M678 481l1 1c0 1 0 2-1 3l-5 3v1h2c1 0 1 0 2 1-1 1-1 2-2 2l-2 2c-1 1-2 2-3 4l-1-1c-1 1-1 1-2 1 0-1 0 0-1-1h0-1c-1 2 0 2-1 3 0 1-5 5-6 5h-1c-2 2-4 6-7 7-1-1-2-1-3 0l-2-1c-3-2-7-5-9-8 2-2 4-3 6-5l19-14h3l-4 5c4-2 9-6 14-7 1 0 2 0 4-1z" class="F"></path><path d="M666 497c0-1 0 0 1-1s1-2 3-3c1 1 1 1 2 1l3-2-2 2c-1 1-2 2-3 4l-1-1c-1 1-1 1-2 1 0-1 0 0-1-1h0z" class="B"></path><path d="M646 510l-1-1h0c2-3 5-7 7-8l2-2c1-1 2-3 4-3v1l-6 6h-1l-3 3 1 1 2-3c4-2 6-7 9-8 1-1 2-2 3-2l1-1c1 0 1-1 3-2v1l-1 1-2 2h1l-1 1 1 1c-1 2 0 2-1 3 0 1-5 5-6 5h-1c-2 2-4 6-7 7-1-1-2-1-3 0l-2-1 1-1z" class="L"></path><path d="M646 510c1-1 2-1 3-2 1 0 1-1 2-1v2c3 0 4-4 6-4-2 2-4 6-7 7-1-1-2-1-3 0l-2-1 1-1z" class="H"></path><path d="M687 449c4-2 7-2 11-2h1c4 1 6 3 8 6h1l3 6v1c1 3 1 7-1 9l-1 2c0 1-1 2-2 3h0v-1l-2 2c-2 2-5 4-8 4h-1c-2 0-3 0-5-1l-4-1c-2-1-4-3-4-5-2-4-3-7-2-11 0-1 1-2 2-4 1-1 1 0 1-1h-1c-1 0-1-1-2-2l-2 3-1-1c2-3 5-6 9-7z" class="N"></path><path d="M687 449c4-2 7-2 11-2h1c4 1 6 3 8 6h0l-4-3c-3-2-5-1-9-1-1 0-3-1-5-1-1 1 0 1-1 1h-1z" class="h"></path><path d="M681 454c1-1 3-3 5-3h1 2 0c-1 1 0 1-1 1h2c0 1-1 2-2 2l-2 2c1 1 1 2 1 4h-1 0v-3h-2-1c1-1 1 0 1-1h-1c-1 0-1-1-2-2z" class="J"></path><path d="M703 456c0 1 1 2 2 3l-5 5c0 3 0 4-2 5-1 1-2 1-3 1l1-2c1-1 2-2 3-4s1-4 1-6h2l1-2z" class="P"></path><path d="M694 473h0 2c1-1 2-1 3-1l3-3c1 0 1 0 2-1s1-3 2-4h0v6h0c0 1 0 1-1 2-1 0-2 1-3 2-2 2-3 2-6 3h-1c-1 0-2 0-3-1h-1c-2-1-2-2-4-3l5 1v-1h2z" class="L"></path><path d="M690 468c-1-3-1-6-1-8v-3-1l3-3c1-1 5-1 7-1 1 0 2 3 4 4l-1 2h-2c0 2 0 4-1 6-1-1-1-1-1-2v-5h2v-1c-2-2-3-2-6-2-1 1-3 2-4 4 1 1 1 2 2 3-1 2-1 3-1 4l2 2 1 1h2l-1 2c-2 0-3-1-5-2h0z" class="f"></path><path d="M692 461c-1-1-1-2-2-3 1-2 3-3 4-4 3 0 4 0 6 2v1h-2v5c0 1 0 1 1 2-1 2-2 3-3 4h-2l-1-1-2-2c0-1 0-2 1-4z" class="X"></path><path d="M693 467c0-1 0-2 1-4h1l1 2c0 1 0 2-1 2 0 0-1 0-1 1l-1-1z" class="d"></path><path d="M692 461h1l1-2v-1c-1-1-1 0-1-1h4c0 1-1 2-1 3-1 1-1 2-1 3h-1c-1 2-1 3-1 4l-2-2c0-1 0-2 1-4z" class="h"></path><path d="M683 457h1 2v3c1 3 1 6 3 7l1 1h0c0 2 1 3 3 4l1 1h-2v1l-5-1c-2-2-3-4-4-7v-2l-1-1c-1 3 0 6 1 8v1c-2-4-3-7-2-11 0-1 1-2 2-4z" class="G"></path><path d="M683 466l2 1v1c0 1 2 2 3 3l3 3 2-2 1 1h-2v1l-5-1c-2-2-3-4-4-7z" class="B"></path><path d="M683 472v-1c-1-2-2-5-1-8l1 1v2c1 3 2 5 4 7 2 1 2 2 4 3h1c1 1 2 1 3 1h1c3-1 4-1 6-3 1-1 2-2 3-2 1-1 1-1 1-2l2-6h1c1 3 0 4 0 6v1c0 1-1 2-2 3h0v-1l-2 2c-2 2-5 4-8 4h-1c-2 0-3 0-5-1l-4-1c-2-1-4-3-4-5z" class="S"></path><defs><linearGradient id="Ac" x1="573.946" y1="647.566" x2="464.291" y2="699.35" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#302f30"></stop></linearGradient></defs><path fill="url(#Ac)" d="M363 594l11 9h2c-1-1-1-2-2-3 0 0 0-1-1-2l3 1c1 2 3 3 5 4 4 3 9 5 13 7l5 2 10 2h1 0c0-1 1-1 1-2v-1c2 0 3-1 5-2l1 2c0 1 2 3 2 4 6 1 13 1 20 0 9 0 19-2 29-2v1c1 0 2 0 3 1h-3c-1 0-2 1-3 1h7 1c3 1 7 2 11 4 1-1 2-1 3-1 0-2-1-5-2-7v-4 1h0l1 1c0 2 3 4 4 5l1 1c1 2 2 4 4 5h1 0c1 1 1 2 2 2h1 1c1 1 1 1 1 2l9 6c1 0 1 1 2 1 2 1 5 4 7 6l18 15c4 3 8 7 12 9l1-2c1 1 1 2 3 2h0c2 2 2 4 5 4h0l2 1v-2l1 1 1-2c0 4 1 6 3 10 0 1 1 1 2 2h1 0c7 6 15 11 23 15 1 1 3 3 4 3 3 2 6 4 9 5 3 3 7 4 11 7h-1c2 1 3 2 5 3h-4-4l-1-1-1 1-1 1-8-2c2 3 4 4 5 7l2 2s1 0 1 1c-4 1-9 1-13 3-1-1-1-1-1-2l1-1v-1c0-2 0-2-1-3h-3c-1 1-2 3-3 4-1-1-3-2-4-2l-1-1h-1c-2-1-4-2-5-3-3 0-5-1-7-1s-3 0-5 1c-1 0-2 0-3 1h0-5v-1c-1-1 0 0 0-1v-1c-1-1-2-2-4-3 0-2-1-2-2-3v-1-2l1-1h1c1 0 2-2 2-3-1-1-3-2-5-3h0-2c-1 0 0 0-1-1h-1l1-2-1-1h-3v-1c-3-1-6 0-8-3h-1l-1-1c0-1-1-2-1-3h-9-17c-5-1-11-1-16-1l-13 2c-4 0-8 1-11 2l-2 1-2-1-2 1-1 2h0l-5 4-3 2-6 5c-1 1-4 3-5 4-2 0-6-1-8-1h-3l-10 1c-3 0-6 1-9 2l1-1c-1-1-2-2-3-2-3-2-2-3-3-6l-1-3-17-42-17-41c-1 0-1-3-1-4l-1-2s0-1-1-2l-2-4c-1-2-1-3-2-4z"></path><path d="M445 645h1c1 1 0 0 1 2h-2c-1 0-1 0-1-1l1-1z" class="I"></path><path d="M463 662l-1-1c-1 0-1-1-2-1l1-1c1 0 2-1 3-2 1 1 1 2 1 3l-2 2z" class="O"></path><path d="M569 698c1 0 3 1 4 2-1 0-3 1-4 2h-1c0-1 0-3 1-4z" class="X"></path><path d="M444 643c2 1 3 0 5-1l4-1 1 2h-2c-2 0-4 2-6 2h-1c-1-1-2 0-3-1l1-2 1 1z" class="O"></path><path d="M500 644v-1c-1-3-3-6-3-10l3 5c2 4 3 7 4 11l-2-1c0-2 0-3-2-4z" class="a"></path><path d="M492 651l1 1c0 2 0 4 1 5 0 1 1 2 1 4-1 0-2 1-3 1l-1 1c-1-1-1-1-1-2v-1l1 1c0-2-1-4 0-6 1-1 1-2 1-4z" class="C"></path><path d="M493 644l2-1c1 5 4 11 4 17-1-1-3-2-3-3-1-2 1-4-1-7h-1c0-2 0-3-1-4l-1-1 1-1z" class="B"></path><path d="M493 644l1-2-1-1h-1 0c1 2 0 4-1 5 0 1 0 2-1 3v-1-1c1-1 0-2-1-3 1-2 0-2 0-3l1-1v-5l-1 2h-1v-1-2l2-1h0 1v1c0 2 1 2 2 3v-2h0l1 2c0 2 0 4 1 6l-2 1z" class="F"></path><path d="M480 643c0-2-1-2 1-4l1 3 1 2h1c0 1 0 2 1 3v2l1-1h2c1 1 1 2 1 4l2-2h1v1c0 2 0 3-1 4-1 2 0 4 0 6l-1-1c-1-1-3-2-5-4-1-3-2-5-3-7v-1c-1-1-2-3-2-5z" class="H"></path><path d="M480 643c0-2-1-2 1-4l1 3v6c-1-1-2-3-2-5z" class="B"></path><path d="M483 644h1c0 1 0 2 1 3v2l1-1h2c1 1 1 2 1 4-1 0-1 1-2 1-3-3-3-5-4-9zm-19 13l1-1c0-1-1-1-1-2l-3-3v-1c-2-2-3-2-5-3h-1c1-1 0-2 2-3 1 3 4 2 7 4 2 1 4 3 6 5v1c0 1 3 3 2 4 0 0-1 0-1-1h-4v1l1 2c-1 1-1 2-2 2l-1 1h-1c-1 0-1 0-1-1l2-2c0-1 0-2-1-3z" class="D"></path><path d="M495 643c-1-2-1-4-1-6l3 8c1 0 2 0 3 1h0c1 2 1 5 1 8h-1c0 3 1 5 1 8l-3 10h-1c0 1 3 2 5 2 1 0 3-1 5-1l2-1c1 0 2 1 3 1v1c-5 0-12 2-17-1h-1c0-1 2-4 2-4 1-3 3-6 3-9h0c0-6-3-12-4-17z" class="N"></path><path d="M497 645c1 0 2 0 3 1h0c1 2 1 5 1 8h-1l-3-9z" class="B"></path><path d="M496 679l36 1 3 6h-1l-1-1c0-1-1-2-1-3h-9-17c-5-1-11-1-16-1 1-1 3-1 4-1-2-1-3 0-5-1h7z" class="f"></path><path d="M462 641l1 1 3 2c1 1 2 1 2 2l2 1c1 0 2 1 3 2l3 2h1v1c2 1 3 1 5 2 1 0 1 0 2 1l1 1c2 2 4 3 5 4v1h-1l-1 1c-3-1-5-3-8-4h-2v-1c-1-1-2-1-3-2v-1c-2-1-4-3-6-4-1-2-3-4-5-5s-3-1-4-3v-1l1 1 1-1z" class="B"></path><path d="M571 706h1 0c1 0 2-1 3-1v1c3 1 6 0 9 2h1c2 0 3 1 4 2v1c-1-1-2-1-3-1h0v1l2 2v-1c1 1 1 1 0 2h1 2c-1 1-2 3-3 4-1-1-3-2-4-2l-1-1h-1c-2-1-4-2-5-3-1 0-2-1-2-2-1 0-2 0-4-1v-2-1z" class="j"></path><path d="M518 671c-3-5-4-10-6-16 0-2-1-4-1-6 1 3 2 5 4 8v-1c1 0 1 0 1 1 1 1 2 2 3 4 0 0 0 1 1 1v1c1-1 1-1 0-2h1c1 1 2 3 2 5 2 3 3 5 3 9l-6-1h0c0-1-1-2-1-3h-1z" class="N"></path><path d="M515 657v-1c1 0 1 0 1 1 1 1 2 2 3 4 0 0 0 1 1 1v1c1-1 1-1 0-2h1c1 1 2 3 2 5s2 4 1 7l-2-1c-4-4-4-10-7-15h0z" class="H"></path><path d="M470 653c3 2 6 5 9 8 1 2 3 3 4 4v3c-2 1-6 1-8 0l-6-3c-1 0-1-1-1-2v-3h0l-1-2v-1h4c0 1 1 1 1 1 1-1-2-3-2-4v-1z" class="U"></path><path d="M468 660c1 0 2 0 3 1v1c0 1 1 2 1 4h3c1-1 1-2 2-3h0v1 1l2 2c2 0 2-1 4-2v3c-2 1-6 1-8 0l-6-3c-1 0-1-1-1-2v-3h0z" class="G"></path><path d="M447 639h1c1-1 2-1 3-2h1l1-1c0-1 0-1-1-2-1 0-1-1-1-2l-7-7 1-1 1 1 2 2c2 0 3 1 5 2h0v-1h1l2 2v2 2l2 1c1 0 1 0 1 1 1 0 1 1 2 1l1 1c0 2 2 3 3 4l1 1h1l6 5 2 1c1 0 2 1 2 1l1 1h1l3 3c-2-1-3-1-5-2v-1h-1l-3-2c-1-1-2-2-3-2l-2-1c0-1-1-1-2-2l-3-2-1-1-1-1v-1h-3l-2 1c-1 1-1 1-2 1h-1l-4 1c-2 1-3 2-5 1l1-1c1-1 2-1 2-2-1 0-1 0-1-1h1z" class="D"></path><path d="M448 627c2 0 3 1 5 2h0v-1h1l2 2v2 2 1l1 1h-1l-4-4-4-5z" class="L"></path><path d="M460 624h1l2-2h1l-2 3h0l1 1v-1c0-1 1-1 2-2h1v1l-1 1 1 1-1 1 1 1v1h-1l1 1h1c1 0 1 0 2 1l1 1c1-1 2-1 3-1 0 0 0 1 1 2l-1 1h-4-1l-1-2h-1c0 1-1 2-1 3h-1v-1l-2 2h0l-1-1-2 1c0-1 0-1-1-1l-2-1v-2-2l-2-2h-1v1h0c-2-1-3-2-5-2l-2-2c1 0 2 0 3 1h1c0-1 0-1 1-1 4-1 5-2 9-1z" class="S"></path><path d="M506 644c2 1 3 2 4 4l1 1c0 2 1 4 1 6 2 6 3 11 6 16h1c0 1 1 2 1 3-2 0-5-1-8 0v-1c-1 0-2-1-3-1l-2 1-1-2 1-1v-5c1-3 1-7 0-10 0-1-1-2-1-3v-2c-1-1-1-2-1-3h1v-3z" class="I"></path><path d="M511 668h0c1 1 2 3 4 3h2 1 1c0 1 1 2 1 3-2 0-5-1-8 0v-1c-1 0-2-1-3-1l-2 1-1-2 1-1c0-1 1-1 2-2l1 2h0l1-2z" class="i"></path><path d="M511 668h0c1 1 2 3 4 3h2c-3 0-6 0-8 1l-2 1-1-2 1-1c0-1 1-1 2-2l1 2h0l1-2z" class="C"></path><path d="M507 655c0-1-1-2-1-3v-2c-1-1-1-2-1-3h1c2 7 4 14 5 21l-1 2h0l-1-2c-1 1-2 1-2 2v-5c1-3 1-7 0-10z" class="O"></path><path d="M500 638h1 1l4 6v3h-1c0 1 0 2 1 3v2c0 1 1 2 1 3 1 3 1 7 0 10v5l-1 1 1 2c-2 0-4 1-5 1-2 0-5-1-5-2h1l3-10c0-3-1-5-1-8h1c0-3 0-6-1-8v-2c2 1 2 2 2 4l2 1c-1-4-2-7-4-11z" class="E"></path><path d="M506 671c-1 0-2 1-3 0v-3c4-4 3-9 3-13h1c1 3 1 7 0 10v5l-1 1z" class="I"></path><defs><linearGradient id="Ad" x1="501.113" y1="658.863" x2="502.474" y2="650.073" xlink:href="#B"><stop offset="0" stop-color="#5a595a"></stop><stop offset="1" stop-color="#6f7070"></stop></linearGradient></defs><path fill="url(#Ad)" d="M500 644c2 1 2 2 2 4l2 1c0 7 0 11-1 18-1 1-2 2-2 3h-1c0-1 0-2 1-3h0c0-2 2-4 1-5h-1c0-3-1-5-1-8h1c0-3 0-6-1-8v-2z"></path><path d="M555 697h1c1 1 2 1 3 1l2 2c1 0 2 1 3 1 1 1 1 2 1 4h2s1 1 2 1h2v1 2c2 1 3 1 4 1 0 1 1 2 2 2-3 0-5-1-7-1s-3 0-5 1c-1 0-2 0-3 1h0-5v-1c-1-1 0 0 0-1v-1c-1-1-2-2-4-3 0-2-1-2-2-3v-1-2l1-1h1c1 0 2-2 2-3z" class="I"></path><path d="M555 697h1c1 1 2 1 3 1l2 2c1 0 2 1 3 1 1 1 1 2 1 4h2s1 1 2 1l-2 1h-1c-2 0-5 0-6-1-2-1-3-3-5-4-1 0-1 0-2-1h-2l1-1h1c1 0 2-2 2-3z" class="L"></path><path d="M569 698c-2-7-13-11-18-15-8-5-15-11-21-18-8-9-15-19-23-27h1 0l2 1c0 1 1 1 1 2 2 2 4 3 5 5l1 3h1c0 1 1 2 2 3l1 1c0 1 1 2 2 3 0 1 1 1 2 2 0 1 1 2 2 3l7 8h0c9 9 19 14 31 20 6 4 12 7 18 10 9 3 18 5 26 10l-1 1-8-2c-9-2-17-6-27-8-1-1-3-2-4-2z" class="P"></path><path d="M469 634l2 1h0 3l1-1c1 0 1 1 2 0l2-3 1-1v-1c1-2 1-3 1-4h1v5c0 1 0 1-1 2v2c1 1 0 1 0 2v3h0c-2 2-1 2-1 4s1 4 2 5v1c1 2 2 4 3 7l-1-1c-1-1-1-1-2-1l-3-3h-1l-1-1s-1-1-2-1l-2-1-6-5h-1l-1-1c-1-1-3-2-3-4l-1-1c-1 0-1-1-2-1l2-1 1 1h0l2-2v1h1c0-1 1-2 1-3h1l1 2h1 0z" class="C"></path><path d="M459 636l2-1 1 1h0l2-2v1h1c0-1 1-2 1-3h1l1 2h1 0v2h4v1c-1 1-2 0-4 1h-4-3l-1-1c-1 0-1-1-2-1z" class="B"></path><path d="M472 643c2-1 3-1 4 0h1 3c0 2 1 4 2 5v1c1 2 2 4 3 7l-1-1c-1-1-1-1-2-1l-3-3h-1l-1-1s-1-1-2-1l-2-1v-1-1h0c-1-2-2-2-3-3h0v-1l2 1z" class="F"></path><path d="M472 643c2-1 3-1 4 0h1 3c0 2 1 4 2 5v1h0-1c-1-1-2-3-4-4l-5-2z" class="K"></path><defs><linearGradient id="Ae" x1="570.175" y1="693.04" x2="572.023" y2="687.911" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#494849"></stop></linearGradient></defs><path fill="url(#Ae)" d="M534 669c3 0 3 1 5 2s3 4 5 5 3 2 5 3c4 2 9 3 13 5l-2-2-2-2v-1c2 1 4 3 5 3l3 2c1 2 4 3 6 5 1 1 2 2 3 2l20 9h0l5 2 1-1 1 1 1-1c3 0 5 1 7 3l4 2c2 1 3 2 5 3h-4-4l-1-1-1 1c-8-5-17-7-26-10-6-3-12-6-18-10-12-6-22-11-31-20z"></path><path d="M562 684l-2-2-2-2v-1c2 1 4 3 5 3l3 2c1 2 4 3 6 5-4-1-7-3-10-5z" class="k"></path><path d="M595 700l5 2 1-1 1 1 1-1c3 0 5 1 7 3l4 2c2 1 3 2 5 3h-4-4l-1-1c-2-1-4-2-7-3-3-2-6-2-8-5z" class="T"></path><path d="M603 701c3 0 5 1 7 3h-1l-1 1h0l-6-3 1-1z" class="k"></path><path d="M373 598l3 1c1 2 3 3 5 4 4 3 9 5 13 7l5 2 10 2h1 0c0-1 1-1 1-2v-1c2 0 3-1 5-2l1 2c0 1 2 3 2 4 6 1 13 1 20 0 9 0 19-2 29-2v1c1 0 2 0 3 1h-3c-1 0-2 1-3 1h7 1c3 1 7 2 11 4 1-1 2-1 3-1 0-2-1-5-2-7v-4 1h0l1 1c0 2 3 4 4 5l1 1c1 2 2 4 4 5h1 0c1 1 1 2 2 2h1 1c1 1 1 1 1 2l9 6c1 0 1 1 2 1 2 1 5 4 7 6l18 15c4 3 8 7 12 9l1-2c1 1 1 2 3 2h0c2 2 2 4 5 4h0l2 1v-2l1 1 1-2c0 4 1 6 3 10 0 1 1 1 2 2h1 0c7 6 15 11 23 15 1 1 3 3 4 3 3 2 6 4 9 5 3 3 7 4 11 7h-1l-4-2c-2-2-4-3-7-3l-1 1-1-1-1 1-5-2h0l-20-9c-1 0-2-1-3-2-2-2-5-3-6-5l-3-2c-1 0-3-2-5-3v1l2 2 2 2c-4-2-9-3-13-5-2-1-3-2-5-3s-3-4-5-5-2-2-5-2h0l-7-8c-1-1-2-2-2-3-1-1-2-1-2-2-1-1-2-2-2-3l-1-1c-1-1-2-2-2-3h-1l-1-3c-1-2-3-3-5-5 0-1-1-1-1-2l-2-1h0v-1c-1 0-1-1-2-1-3-2-5-4-8-6-11-8-23-13-36-11-2 1-4 1-6 1-12 2-23 5-35 3-13 0-38-10-47-20h2c-1-1-1-2-2-3 0 0 0-1-1-2z" class="V"></path><path d="M421 623l-2-1c-3-1-5-1-8-2v-1c14 2 27 3 40 1 2-1 3-1 5-1v1c-12 2-23 5-35 3z" class="X"></path><path d="M534 655l20 18 1 1c2 2 4 3 6 4l-1 1c2 1 3 1 4 2v1h-1c-1 0-3-2-5-3h-1c-9-7-17-14-25-21l1-1c0 1 1 1 2 2 0-2-1-3-1-4z" class="L"></path><path d="M532 658c-4-3-9-6-11-10-1-2-3-3-4-4-5-7-12-12-20-16l-1-1h1c13 7 26 17 37 28 0 1 1 2 1 4-1-1-2-1-2-2l-1 1z" class="G"></path><defs><linearGradient id="Af" x1="425.562" y1="602.521" x2="421.138" y2="619.455" xlink:href="#B"><stop offset="0" stop-color="#b6b6b7"></stop><stop offset="1" stop-color="#d9d9da"></stop></linearGradient></defs><path fill="url(#Af)" d="M373 598l3 1c1 2 3 3 5 4 4 3 9 5 13 7l5 2 10 2h1 0c0-1 1-1 1-2v-1c2 0 3-1 5-2l1 2c0 1 2 3 2 4 6 1 13 1 20 0 9 0 19-2 29-2v1c1 0 2 0 3 1h-3c-1 0-2 1-3 1l-19 3c-6 0-11 1-17 1-19 0-38-6-53-17-1-1-1-2-2-3 0 0 0-1-1-2z"></path><path d="M416 609l1 2c0 1 2 3 2 4l-9-1h0c0-1 1-1 1-2v-1c2 0 3-1 5-2z" class="I"></path><path d="M485 612v-4 1h0l1 1c0 2 3 4 4 5l1 1c1 2 2 4 4 5h1 0c1 1 1 2 2 2h1 1c1 1 1 1 1 2l9 6c1 0 1 1 2 1 2 1 5 4 7 6l18 15c4 3 8 7 12 9l1-2c1 1 1 2 3 2h0c2 2 2 4 5 4h0l2 1v-2l1 1 1-2c0 4 1 6 3 10 0 1 1 1 2 2h1 0c7 6 15 11 23 15 1 1 3 3 4 3 3 2 6 4 9 5 3 3 7 4 11 7h-1l-4-2c-2-2-4-3-7-3l-1 1-1-1-1 1-5-2h0l-20-9c-1 0-2-1-3-2-2-2-5-3-6-5l-3-2h1v-1c-1-1-2-1-4-2l1-1c-2-1-4-2-6-4l-1-1-20-18c-11-11-24-21-37-28-4-3-9-5-13-7 1-1 2-1 3-1 0-2-1-5-2-7z" class="g"></path><path d="M554 673l2-2c4 3 7 4 10 8 0 1 1 1 1 2l-12-7-1-1z" class="h"></path><path d="M550 660c1 1 1 2 3 2h0c2 2 2 4 5 4h0l2 1v-2l1 1 1-2c0 4 1 6 3 10l-16-12 1-2z" class="C"></path><path d="M485 612v-4 1h0l1 1c0 2 3 4 4 5l1 1c1 2 2 4 4 5h1 0c1 1 1 2 2 2h1 1c1 1 1 1 1 2l-14-6c0-2-1-5-2-7z" class="F"></path><path d="M555 674l12 7c11 8 24 14 36 20l-1 1-1-1-1 1-5-2h0l-20-9c-1 0-2-1-3-2-2-2-5-3-6-5l-3-2h1v-1c-1-1-2-1-4-2l1-1c-2-1-4-2-6-4z" class="B"></path><path d="M566 684l9 4v1h-1l1 1v1c-1 0-2-1-3-2-2-2-5-3-6-5z" class="T"></path><path d="M575 691v-1l-1-1h1l26 12-1 1-5-2h0l-20-9z" class="M"></path><path d="M363 594l11 9c9 10 34 20 47 20 12 2 23-1 35-3 2 0 4 0 6-1l3 1c-5 0-10 0-15 2 1 0 3 0 4-1h2v2h0c2 0 3 0 4 1-4-1-5 0-9 1-1 0-1 0-1 1h-1c-1-1-2-1-3-1l-1-1-1 1 7 7c0 1 0 2 1 2 1 1 1 1 1 2l-1 1h-1c-1 1-2 1-3 2h-1-1c0 1 0 1 1 1 0 1-1 1-2 2-2 0-4-1-6-2l-3-2-1 1c-4-1-8-3-12-2h-1c-4 1-9 4-11 7 0 1 0 1-1 2l1 1c-1 2-1 2-2 3-1 2-1 4-1 6l-1 1 5 12c-1 0-1-1-2-1v3l1 3c2 4 2 9 4 13 1 2 1 5 2 7s4 5 3 7h-1v2c-3 0-6 1-9 2l1-1c-1-1-2-2-3-2-3-2-2-3-3-6l-1-3-17-42-17-41c-1 0-1-3-1-4l-1-2s0-1-1-2l-2-4c-1-2-1-3-2-4z" class="j"></path><path d="M436 633c1-1 1 0 3 0h0l2 2h1l1 1h1c1 1 2 1 3 1h1l-1 2h-1c0 1 0 1 1 1 0 1-1 1-2 2-2 0-4-1-6-2l-3-2-1-1h-1c0-1-1-2-3-3h2l1 1 2-2z" class="I"></path><path d="M431 634h2l1 1 2-2c2 4 5 4 7 7h-4l-3-2-1-1h-1c0-1-1-2-3-3z" class="H"></path><path d="M409 650c0-1-1-3 0-4h0c0-1 0-2-1-3 2-1 3-3 5-4l4-2h1c-1-1 0-1-1-2h-1l1-2c2 1 3 1 4 2h1c0-1 0-1 1-2h0 3c1 0 1 1 2 1l1-1c1 1 1 1 2 1h0c2 1 3 2 3 3h1l1 1-1 1c-4-1-8-3-12-2h-1c-4 1-9 4-11 7 0 1 0 1-1 2l1 1c-1 2-1 2-2 3z" class="I"></path><defs><linearGradient id="Ag" x1="383.339" y1="615.064" x2="376.894" y2="626.837" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#323333"></stop></linearGradient></defs><path fill="url(#Ag)" d="M369 606l2 2h0c1 0 2 1 3 1 0 1 1 1 1 1 3 2 6 4 8 7l7 19c1 2 2 5 3 7h0c-2-1-6-8-7-10l-1 1c-1-1-2-3-2-4l-9-18h-1c-1-1-2-1-3-2-1 0-1-3-1-4z"></path><path d="M392 628l-3-8c0-1 0-2-1-2l1-1h0l1 1v-1-1s1-1 2-1c1 1 2 4 3 5l1 3 8 21-1 1v2l1 1c1 0 1 0 1 2s1 4 2 7l5 12c-1 0-1-1-2-1v3l1 3c2 4 2 9 4 13 1 2 1 5 2 7s4 5 3 7h-1c-1-5-3-8-5-13l-7-23c-1-4-3-7-4-10l-1-2-3-8s-1-1-1-2l-2-4-2-5c-1-2-1-4-2-6z" class="R"></path><path d="M392 628l-3-8c0-1 0-2-1-2l1-1h0l1 1v-1-1s1-1 2-1c1 1 2 4 3 5l1 3-1 1c2 6 5 12 7 19v1c-1-1-1-2-1-3l-1-1c-1-3-2-8-4-11l-1 1 3 6v2c0 1 1 2 1 3l1 1c0 2 2 3 2 5s1 3 2 4v2c-1-1-1-1-1-2-1-1-2-3-2-4s-1-2-1-3l-2-4s0-1-1-1v-2c-1-2-2-3-2-4l-1-4c-1-1 0-1-1-2l-1 1zm-22-18c1 1 2 1 3 2h1l9 18c0 1 1 3 2 4l1-1c1 2 5 9 7 10h0c2 1 3 2 3 4l-1 1 1 2 3 5c2 3 3 6 3 9 2 5 4 10 5 15l4 15c0 1 2 5 1 6v2l-1 2c-1-1-2-2-3-2-3-2-2-3-3-6l-1-3-17-42-17-41z" class="H"></path><path d="M406 689l2 8h0l-3-1-1-3c1-2 1-3 2-4z" class="T"></path><path d="M408 697c1 2 2 4 4 5l-1 2c-1-1-2-2-3-2-3-2-2-3-3-6l3 1h0z" class="e"></path><defs><linearGradient id="Ah" x1="391.642" y1="672.811" x2="401.845" y2="671.107" xlink:href="#B"><stop offset="0" stop-color="#8b8c8a"></stop><stop offset="1" stop-color="#bcbabe"></stop></linearGradient></defs><path fill="url(#Ah)" d="M387 647c6 15 14 27 19 42-1 1-1 2-2 4l-17-42v-4z"></path><path d="M370 610c1 1 2 1 3 2h1l9 18c0 1 1 3 2 4 5 8 11 16 15 25-6-6-11-14-16-21l3 9v4l-17-41z" class="Y"></path><path d="M410 646c1-1 1-1 1-2 2-3 7-6 11-7h1c4-1 8 1 12 2 1 1 2 1 3 2s2 2 3 4c1 1 2 2 2 4v1c1 3 1 7 2 10l3 3c6 2 11 4 17 6 6 1 13 2 19 4l1 1c1 0 3 1 4 1s2 1 3 1v1h1c0 1 1 1 2 1h1v1h-7c2 1 3 0 5 1-1 0-3 0-4 1l-13 2c-4 0-8 1-11 2l-2 1-2-1-2 1-1 2h0l-5 4-3 2-6 5c-1 1-4 3-5 4-2 0-6-1-8-1h-3l-10 1v-2h1c1-2-2-5-3-7s-1-5-2-7c-2-4-2-9-4-13l-1-3v-3c1 0 1 1 2 1l-5-12 1-1c0-2 0-4 1-6 1-1 1-1 2-3l-1-1z" class="H"></path><path d="M450 690h-1c-1-2-1-2-3-3h0c0-1 0-1 1-2h0l1-2v-1h5l-4 1c-1 1-1 4 0 6l1 1zm-19-1c1 2 2 4 2 5h-2l-1-1c0-3-1-4-2-7l2-1c1-1 2 0 4 0 1-1 0-1 1 0v1h0v1c-2 0-3 1-4 2z" class="C"></path><path d="M431 689l-1-1 1-1h-2v-1h6 0v1c-2 0-3 1-4 2z" class="B"></path><path d="M459 688c0-2 0-3-1-4 0-2-2-3-3-4-1 0-1 0-1-1l1-1c1 0 2 0 4 1 1 1 2 1 3 1s1 0 2 1h-2-1 1v4l-2 1-1 2h0z" class="h"></path><path d="M413 671c5 6 10 13 13 21l2 4 1 3h0c-2-1-2-2-2-3-2-5-5-10-7-15l-2-2c0 1 3 7 4 9h-1c-1-1-1-1-1-2h-1c0-1 0-1-1-2v-2c-1-1-1-2-3-3v-1c-1-1-1-2-1-3-1-2-1-3-1-4z" class="B"></path><path d="M445 699v-3-1c-1-1 0-3-1-4s-3-3-5-3l-2-1h2s0-2 1-3l1 1c1-1 3-2 3-3s-1-1 0-2c1 1 2 1 2 2v1h-2l-3 3 1 1h0 1l-1 1c1 1 3 3 3 4v3l2-3c0-1 0-2-1-3h1c1 2 2 3 3 4h1l-1-3-1-1c-1-2-1-5 0-6h0l1 2h0c0 2 0 4 2 6l2 1-3 2-6 5z" class="I"></path><path d="M462 681l27-2c2 1 3 0 5 1-1 0-3 0-4 1l-13 2c-4 0-8 1-11 2l-2 1-2-1v-4z" class="d"></path><path d="M411 674l-1-3v-3c1 0 1 1 2 1l1 2c0 1 0 2 1 4 0 1 0 2 1 3v1c2 1 2 2 3 3v2c1 1 1 1 1 2h1c0 1 0 1 1 2h1c1 1 1 1 1 2 1 2 2 4 2 7h0l1 1h0l1 1h-2v2c2 0 3 1 4 1l-10 1v-2h1c1-2-2-5-3-7s-1-5-2-7c-2-4-2-9-4-13z" class="b"></path><path d="M415 679c2 1 2 2 3 3v2c1 1 1 1 1 2h1c0 1 0 1 1 2h1c1 1 1 1 1 2 1 2 2 4 2 7h0l-4-5 2 5c1 1 1 2 1 4h0c-1-1-2-2-2-4-1-2-1-5-3-7h0c-2-1-1-3-2-5 0-2-1-4-2-6z" class="C"></path><path d="M411 674l-1-3v-3c1 0 1 1 2 1l1 2c0 1 0 2 1 4 0 1 0 2 1 3v1c1 2 2 4 2 6 1 2 0 4 2 5l2 8-5-11h-1c-2-4-2-9-4-13z" class="U"></path><path d="M411 674h1l2 5c1 3 3 6 2 8h-1c-2-4-2-9-4-13z" class="C"></path><path d="M440 661c1-1 2-2 2-3s1-1 1-2c0 2 0 5-1 7 1 2 2 2 4 2l1 1 1-2h0v-1c6 2 11 4 17 6 6 1 13 2 19 4l1 1c1 0 3 1 4 1s2 1 3 1v1h-1c-2-1-4 0-5-1h-3-2c-9-2-17-1-26-3-1-1-2 0-3-1h-3-2l-3-1-5-1c-1 0-3 1-5 0l1-1h2l2-2-4-1 1-1 4-3v-1z" class="B"></path><path d="M440 662c0 2 0 3-1 5h0l-4-1 1-1 4-3z" class="j"></path><path d="M461 671h-3c-3-1-7-1-10-2-1 0-6-4-7-5l1-1c1 2 2 2 4 2l1 1c4 1 9 3 13 3l1 2z" class="J"></path><path d="M448 663c6 2 11 4 17 6 6 1 13 2 19 4l1 1c-4 0-8-1-11-2-3 0-6 0-9-1h-4l-1-2c-4 0-9-2-13-3l1-2h0v-1z" class="X"></path><path d="M410 646c1-1 1-1 1-2 2-3 7-6 11-7h1c4-1 8 1 12 2 1 1 2 1 3 2s2 2 3 4c1 1 2 2 2 4v1c1 3 1 7 2 10l3 3v1h0l-1 2-1-1c-2 0-3 0-4-2 1-2 1-5 1-7 0 1-1 1-1 2s-1 2-2 3v1l-4 3-1 1-6 3h-1c-2 0-6 0-9-1h0c-4-2-6-3-7-6s-2-6-1-9v-6l-1-1z" class="V"></path><path d="M445 660l3 3v1h0l-1 2-1-1c-1 0-1-1-2-2-1 0-1 0-1-1l2-2z" class="P"></path><path d="M437 657c1 1 1 1 2 1l1-4v1 6 1l-4 3v-1c1-1 1 0 0-1l-1-1 1-3 1-2z" class="D"></path><path d="M416 644c1 0 1-1 2-2l1 1h0c1 1 1 0 2 0 0 1-1 1-1 2-2 1-2 2-3 3l1 1c0 2-1 3-1 4 0 2 1 3 1 5-1-2-3-5-2-7v-2c0-1 0-1-1-2l2-2-1-1z" class="G"></path><path d="M418 658v1c1 0 2 1 2 2 1 1 3 2 5 2 1 0 2 0 3-1h1l5-3v1 3l1-1 1 1c1 1 1 0 0 1v1l-1 1-6 3-1-1c1 0 2-1 3-1 2-1 3-1 4-2v-1c-2 2-4 2-6 2h-2c-3 0-4 0-6-3-2-1-3-2-3-5z" class="O"></path><path d="M427 643c4 0 6 1 9 4 0 1 1 1 1 2l1 2h0c-1 2-1 4-1 6l-1 2c-1-1-1-1-1-2v-1l-1-2h-1c-1 1-1 2-2 3 0 2-2 4-4 5h-2l1-1 2-1v-2c0-2 2-4 2-6s-1-3-3-5c0-1 0-1 1-3l-1-1z" class="k"></path><path d="M428 660c3-2 3-6 5-9l1 1 1 4-1-2h-1c-1 1-1 2-2 3 0 2-2 4-4 5h-2l1-1 2-1z" class="c"></path><path d="M427 643c4 0 6 1 9 4 0 1 1 1 1 2-1-1-2-1-3-1v1c-2 0-2-1-3 1h0c0 2 0 6-2 7l-1 1c0-2 2-4 2-6s-1-3-3-5c0-1 0-1 1-3l-1-1z" class="M"></path><path d="M425 662h0c-2-1-3-3-4-4s-1-2-2-3c0-1 1-6 1-7s2-2 2-3h0v-1c1-1 3-1 5-1l1 1c-1 2-1 2-1 3 2 2 3 3 3 5s-2 4-2 6v2l-2 1-1 1z" class="P"></path><path d="M424 649h2l1 6c0 2-1 2-2 3-1 0-2-1-3-2v-2c0-2 1-4 2-5z" class="d"></path><defs><linearGradient id="Ai" x1="416.962" y1="662.883" x2="410.878" y2="648.474" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#Ai)" d="M410 646c2-1 3-3 5-4s2-1 3-1c-1 1-3 2-3 3h1l1 1-2 2c1 1 1 1 1 2v2c-1 2 1 5 2 7h0c0 3 1 4 3 5 2 3 3 3 6 3h2c2 0 4 0 6-2v1c-1 1-2 1-4 2-1 0-2 1-3 1l1 1h-1c-2 0-6 0-9-1h0c-4-2-6-3-7-6s-2-6-1-9v-6l-1-1z"></path><path d="M415 658v-1c0-2-1-3-2-4 1-1 2-2 3-2-1 2 1 5 2 7h0c0 3 1 4 3 5-1 0-2 0-3-1-1-2-2-3-3-4z" class="E"></path><path d="M415 658c1 1 2 2 3 4 1 1 2 1 3 1 2 3 3 3 6 3h2c2 0 4 0 6-2v1c-1 1-2 1-4 2-1 0-2 1-3 1l1 1h-1c-2 0-6 0-9-1h0c-4-2-6-3-7-6l1-1c0 1 1 2 2 3s4 3 6 3h0c-4-3-5-4-6-8v-1z" class="F"></path><path d="M570 507c3 0 4 2 6 4-1-3-4-7-7-9h0c-2-1-3-2-6-2h0v-1h4 1 1l-1-1v-1h1c0-1 0-1 1-2 0-2 3-5 5-5-1 1-2 3-3 5s-2 3-1 4c1 3 3 6 4 8 1 3 2 6 4 9 3 6 7 12 12 16 3 3 9 9 13 10h3c3 1 5 4 9 4 4 1 8 2 12 2l13 1c7-1 13-2 19-3l1 1c9-3 16-7 24-12h2l1 1c0 1-3 8-4 9-3 2-2 7-6 8v-2c-2 1-2 2-3 3v1c-1 2-3 3-4 5-1 3-1 6-3 10v-1l1-5h0-1 0l1-4c0-2 1-6 1-7-1 1-1 3-2 5-1 5-4 10-6 15-3 6-5 13-8 19l-1-1c-2 2-3 4-4 6h-1c-1 4-1 6 0 9h-1c-2 3-9 5-10 8 0 1 0 3-1 5l-2 3c-3 6-5 12-8 18-2 4-5 8-7 12-5 9-8 19-11 29h1c-1 2-2 4-1 6v6l-1 2c0 1-1 2-2 3l-1 1c-3-1-6-3-9-5-1 0-3-2-4-3-8-4-16-9-23-15h0-1c-1-1-2-1-2-2-2-4-3-6-3-10l-1 2-1-1v2l-2-1h0c-3 0-3-2-5-4h0c-2 0-2-1-3-2l-1 2c-4-2-8-6-12-9l-18-15 1-1c2-2 3-6 4-9l4-7c0-3 2-6 3-8l8-18c3-5 5-11 8-16 1-1 1 0 1-1h1l18-40-1-4c0-1 1-3 1-4l7-13c0-1 0-2 1-3l-3-3c-1-2-1-3-2-4z" class="H"></path><path d="M520 637c2-2 3-6 4-9l3 2c-1 2-3 7-5 9l-2-2z" class="Z"></path><path d="M534 640s0 1 1 1h0c1 0 1 0 2 2 2 3 5 6 6 10v1c-1-1-2-2-2-3-2-2-4-3-5-6h0c-1-2-2-3-2-5z" class="L"></path><path d="M574 517v1h0v2c-2 6-5 12-7 18l-1-4c0-1 1-3 1-4l7-13z" class="V"></path><path d="M538 630c2 1 2 2 3 4l4 12h-2l-2-5c-1-1-1-3-2-5h0v-1c-1 1-2 1-3 1l-2-2 4-4z" class="L"></path><path d="M547 579c1-1 1 0 1-1h1l-14 33c-1 3-3 8-5 11l-3 8-3-2 4-7c0-3 2-6 3-8l8-18c3-5 5-11 8-16z" class="M"></path><path d="M524 628l4-7h0c0 2-1 3-1 4h1 0c1-2 1-2 2-3l-3 8-3-2z" class="c"></path><defs><linearGradient id="Aj" x1="532.576" y1="610.324" x2="563.715" y2="580.459" xlink:href="#B"><stop offset="0" stop-color="#787876"></stop><stop offset="1" stop-color="#a7a5a9"></stop></linearGradient></defs><path fill="url(#Aj)" d="M562 562h1l-3 8c-1 2-1 3-1 5l-24 52h-1l28-65z"></path><defs><linearGradient id="Ak" x1="585.562" y1="534.729" x2="565.125" y2="542.51" xlink:href="#B"><stop offset="0" stop-color="#070806"></stop><stop offset="1" stop-color="#272628"></stop></linearGradient></defs><path fill="url(#Ak)" d="M575 531c-1 1 0 0-1 0h0c1-1 1-1 1-2l1-1 2-6-1-1c0-1 0-2-1-4v-1c1 1 1 2 1 3 1 1 1 2 2 3 1 2 4 3 5 6v2c0 2-1 3-1 4-1 1-1 1-1 2-4 7-6 14-10 21 0 0-1 0-2-1h-2l-2 1v1h0c-1 1-1 1-2 1l-1 3h-1l13-31z"></path><path d="M571 545c2-1 2-2 2-3l1-2 5-10c1 1-7 19-9 22l-1 2c0 1 0 1 1 2h0-2v-1l-1-1c1-3 3-7 4-9z" class="c"></path><path d="M575 531c1-1 2-3 3-4-2 6-4 12-7 18-1 2-3 6-4 9l1 1v1l-2 1v1h0c-1 1-1 1-2 1l-1 3h-1l13-31z" class="P"></path><path d="M570 556c1 1 2 1 2 1l-9 20c-5 14-11 27-19 40-1 1-2 2-3 4-1 0 0 1-1 1-2 2-3 4-4 5h-1l24-52c0-2 0-3 1-5l3-8 1-3c1 0 1 0 2-1h0v-1l2-1h2z" class="I"></path><path d="M566 558l2 2-3 3-6 12c0-2 0-3 1-5l3-8 1-3c1 0 1 0 2-1h0z" class="M"></path><path d="M584 530c2 2 3 3 3 5l-3 8-1 1-3 9-5 13-14 31c-2 5-6 11-8 17-1 1-2 3-2 5 0 5 2 13 3 17l3 9c2 3 4 6 5 10 1 1 1 3 1 5 0 6 0 11 5 16h0-1c-1-1-2-1-2-2-2-4-3-6-3-10l-1 2-1-1v2l-2-1h0c-3 0-3-2-5-4h0c-2 0-2-1-3-2l-5-14-4-12c-1-2-1-3-3-4l-4 4v1h-1 0v-1h-1c-1-3 0-5 2-7h1 1c1-1 2-3 4-5 1 0 0-1 1-1 1-2 2-3 3-4 8-13 14-26 19-40l9-20c4-7 6-14 10-21 0-1 0-1 1-2 0-1 1-2 1-4z" class="f"></path><path d="M547 623c0-3 0-3 1-5l1 1c1 2 1 4 1 6 3 14 10 25 12 39l-1 2-1-1v2l-2-1c-1-2-2-6-2-8-2-7-5-13-7-20-1-3-2-6-2-9 1-2 0-4 0-6z" class="G"></path><path d="M547 623l2 5c1 1 1 2 1 3 1 4 8 24 7 25 0 1-1 1-1 2-2-7-5-13-7-20-1-3-2-6-2-9 1-2 0-4 0-6z" class="T"></path><path d="M547 623l2 5v1 1c1 1 0 4 0 6v2c-1-3-2-6-2-9 1-2 0-4 0-6z" class="P"></path><defs><linearGradient id="Al" x1="555.179" y1="665.505" x2="544.197" y2="625.945" xlink:href="#B"><stop offset="0" stop-color="#0a0909"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#Al)" d="M542 629c0-2 2-4 3-6l2 6c0 3 1 6 2 9 2 7 5 13 7 20 0 2 1 6 2 8h0c-3 0-3-2-5-4h0c-2 0-2-1-3-2l-5-14-4-12c0-1 1-2 1-3-1-1-1-1-1-2h1z"></path><defs><linearGradient id="Am" x1="548.487" y1="644.547" x2="540.013" y2="635.953" xlink:href="#B"><stop offset="0" stop-color="#919392"></stop><stop offset="1" stop-color="#b6b5b6"></stop></linearGradient></defs><path fill="url(#Am)" d="M541 634c0-1 1-2 1-3-1-1-1-1-1-2h1l11 33c-2 0-2-1-3-2l-5-14-4-12z"></path><path d="M584 530c2 2 3 3 3 5l-3 8-1 1c-3 1-3 8-6 10 0 1-1 2-1 3-2 4-5 9-6 13-2 4-4 8-5 12-1 0-1 1-1 2l-2 4c0 1-1 2-1 3s-1 3-1 4c-1 1-1 2-2 3v1c-1 1-1 2-2 3v2c-1 0-2 2-3 3h-1c-1 1-1 2-1 3l-4 5c-1 1-1 2-1 3l-3 4c-2 2-4 5-5 8l-4 4v1h-1 0v-1h-1c-1-3 0-5 2-7h1 1c1-1 2-3 4-5 1 0 0-1 1-1 1-2 2-3 3-4 8-13 14-26 19-40l9-20c4-7 6-14 10-21 0-1 0-1 1-2 0-1 1-2 1-4z" class="X"></path><path d="M535 627h1c1-1 2-3 4-5 1 0 0-1 1-1 1-2 2-3 3-4l-5 10c-2 2-5 5-5 8h-1 0v-1h-1c-1-3 0-5 2-7h1z" class="C"></path><path d="M584 530c2 2 3 3 3 5l-3 8-1 1c-3 1-3 8-6 10 1-3 2-7 4-10l1-1h-1c0-3 1-4 1-7 0-1 0-1 1-2 0-1 1-2 1-4z" class="F"></path><path d="M581 564c2-1 4 0 7 0l4 2c3 1 10 4 12 6 1 0 1 1 2 2l1-1c0-1 0-1-1-1l2-2c1 0 2-2 4-2-2 2-3 3-4 5l-1 1c-1 1-1 2-2 3l2 1h0c0 1-1 1-2 2l-1 1c0 2-2 2-3 4l1 2h1v1h1l-1 1c-2 1-3 3-4 4-2 0-4 1-5 2 0 1-1 2-2 3s-1 2-1 3h0l-2-1c-2 1-3 2-4 2v4 1h0-3v-3h-2l-8 10c-1 2-2 3-3 5-2 2-4 3-5 5-1 1-1 2-2 4l1 1c-1 1-2 1-3 1v1 1h-2c-1 1-2 1-3 2l-1 2c-1-4-3-12-3-17 0-2 1-4 2-5 2-6 6-12 8-17l14-31 2-2h4z" class="H"></path><path d="M577 595h3 1l1 2c-1 1-2 2-3 2-2 1-5 5-7 7l-1 2v1c-1 1-1 2-2 3-1 0-1 0-1 1l-2 1-1 1 1 1-2 2-1 1c0 1-1 1-1 2l-2 2-1-2c1-5 6-8 8-12 0-2 1-3 2-4 0 0 1-1 2-1v-1l1-1 1-1 3-2v1l1-1v-4z" class="I"></path><path d="M577 595h3 1l1 2c-1 1-2 2-3 2-2 1-5 5-7 7l-1 2v1c-1 1-1 2-2 3-1 0-1 0-1 1l-2 1 1-2c1-1 1-2 1-3 0-2 1-3 3-4 2-2 2-4 5-6v1l1-1v-4z" class="E"></path><path d="M571 585l3-6h2 1 1l1 1h-2 0c1 2 6 7 8 7 1 0 1 0 2 1 0 1 0 1-1 2l-1-1h-1c1 1 0 1 1 2h0-1-1c0 2 0 2 2 4-1 1-2 1-3 2h0l-1-2h-1-3-4c-2-1-2-2-3-3 0-1 0 0-1-1h0l2-5v-1z" class="B"></path><path d="M571 585l1 1h3l-1 1h-2v1 2h0l-2 2c0-1 0 0-1-1h0l2-5v-1z" class="C"></path><defs><linearGradient id="An" x1="555.062" y1="617.044" x2="588.407" y2="612.241" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#464849"></stop></linearGradient></defs><path fill="url(#An)" d="M604 581c0 2-2 2-3 4l1 2h1v1h1l-1 1c-2 1-3 3-4 4-2 0-4 1-5 2 0 1-1 2-2 3s-1 2-1 3h0l-2-1c-2 1-3 2-4 2v4 1h0-3v-3h-2l-8 10c-1 2-2 3-3 5-2 2-4 3-5 5-1 1-1 2-2 4l1 1c-1 1-2 1-3 1h-2l-1 1c0-2 0-3 1-4v-1c1-1 2-2 2-3l2-2c0-1 1-1 1-2l1-1 2-2-1-1 1-1 2-1c0-1 0-1 1-1 1-1 1-2 2-3v-1l1-2c2-2 5-6 7-7 1 0 2-1 3-2h0c1-1 2-1 3-2l2-2 11-7 3-3c1-1 2-1 3-2z"></path><path d="M582 599l1-1c1-1 2-2 3-2l2 1c1 0 2-1 3-2 0 2 0 2-1 3s-2 1-3 1l-1-1c-2 1-2 1-4 1z" class="J"></path><path d="M604 581c0 2-2 2-3 4l1 2h1v1h1l-1 1c-2 1-3 3-4 4-2 0-4 1-5 2 0 0-1-1-2-1l-1 1c-1 1-2 2-3 2l-2-1c-1 0-2 1-3 2l-1 1c-3 1-10 10-11 10v-1l1-2c2-2 5-6 7-7 1 0 2-1 3-2h0c1-1 2-1 3-2l2-2 11-7 3-3c1-1 2-1 3-2z" class="R"></path><path d="M601 585l1 2h1v1h1l-1 1h-2l-1 1v1l-1-1-1 1h0-1c-1 1-1 0-2 1v-3c1 0 2-1 3-2s2-1 3-2zm-20-21c2-1 4 0 7 0l4 2c3 1 10 4 12 6 1 0 1 1 2 2l1-1c0-1 0-1-1-1l2-2c1 0 2-2 4-2-2 2-3 3-4 5l-1 1c-1 1-1 2-2 3l2 1h0c0 1-1 1-2 2l-1 1c-1 1-2 1-3 2l-3 3-11 7-2 2c-2-2-2-2-2-4h1 1 0c-1-1 0-1-1-2h1l1 1c1-1 1-1 1-2-1-1-1-1-2-1-2 0-7-5-8-7h0 2l-1-1c-1 0-1-1-2-1l-1-1c0-1 0 0 1-1 1 1 2 2 4 2h-1c0-1 0-1-1-2s-1-1-1-2h1c0 1 1 1 2 1l-1-2c-2 0-2 0-3-2h-1c0-1 0-2 1-2 0-2 1-3 2-4h3v-1z" class="J"></path><path d="M604 572c1 0 1 1 2 2l1-1c0-1 0-1-1-1l2-2c1 0 2-2 4-2-2 2-3 3-4 5l-1 1c-1 1-1 2-2 3l2 1h0c0 1-1 1-2 2l-1 1c-1 1-2 1-3 2l-3 3-11 7-2 2c-2-2-2-2-2-4h1 1 0c-1-1 0-1-1-2h1l1 1c1-1 1-1 1-2-1-1-1-1-2-1 0-1-1-2-1-2 1 1 2 1 4 2h1l-3-3c1 0 2 1 3 1 0 1 0 1 1 0 0-1-3-2-4-3l1-1c1 2 2 3 5 3v-1h1c1 0 1-1 2-1l-1-1 1-1c0 1 1 1 2 1v-1c-2-1-4-2-5-3h1 0c1 1 4 2 5 2l1-1-3-1-2-1c-1 0-2-1-2-1l-1-1c3 1 6 2 9 4h0 0l1-1c-1 0-2-1-3-2h4 1c0-1 0-2 1-3h0z" class="L"></path><path d="M600 578l1-1c-1 0-2-1-3-2h4c1 1 2 1 2 2 1 0 1 1 1 2h-2l-3-1z" class="F"></path><path d="M581 564c2-1 4 0 7 0l4 2c3 1 10 4 12 6h0c-1 1-1 2-1 3h-1-4c1 1 2 2 3 2l-1 1h0 0c-3-2-6-3-9-4-2-1-3-2-5-4-1-1-3-1-4-1 1 1 3 2 4 3v1l-2-1-1-1-2 2 3 1v1l-4-2h-1c-2 0-2 0-3-2h-1c0-1 0-2 1-2 0-2 1-3 2-4h3v-1z" class="O"></path><path d="M578 565l3 2h-1l-2 2c0 1-1 1-1 2h-1-1c0-1 0-2 1-2 0-2 1-3 2-4z" class="b"></path><path d="M591 574c-2-3-5-4-7-7h0c1 0 1 1 2 1 0-1-1-1-1-2 1 0 2 1 4 1 0-1 0 0 1-1l3 1c1 1 3 2 4 3l1 1h2l4 1c-1 1-1 2-1 3h-1-4c1 1 2 2 3 2l-1 1h0 0c-3-2-6-3-9-4z" class="R"></path><defs><linearGradient id="Ao" x1="636.105" y1="526.61" x2="624.901" y2="588.862" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#2f2f30"></stop></linearGradient></defs><path fill="url(#Ao)" d="M584 528l1 2 2-1v1c1 1 2 2 4 2 3 3 9 9 13 10h3c3 1 5 4 9 4 4 1 8 2 12 2l13 1c7-1 13-2 19-3l1 1c9-3 16-7 24-12h2l1 1c0 1-3 8-4 9-3 2-2 7-6 8v-2c-2 1-2 2-3 3v1c-1 2-3 3-4 5-1 3-1 6-3 10v-1l1-5h0-1 0l1-4c0-2 1-6 1-7-1 1-1 3-2 5-1 5-4 10-6 15-3 6-5 13-8 19l-1-1c-2 2-3 4-4 6h-1l5-15-3 1h0c-1 0-1 0-2 1h-9v-1c-2 1-3 0-4 1h-1c-1 0-2 0-3 1h-2 0-1-1c-1 1-1 1-2 1h-1l-3 1h-2l-2 1-7 2c-2 0-3 1-5 0l-1-2h-1v-1h-1l-1-2c1-2 3-2 3-4l1-1c1-1 2-1 2-2h0l-2-1c1-1 1-2 2-3l1-1c1-2 2-3 4-5-2 0-3 2-4 2l-2 2c1 0 1 0 1 1l-1 1c-1-1-1-2-2-2-2-2-9-5-12-6l-4-2c-3 0-5-1-7 0h-4l-2 2 5-13 3-9 1-1c1-2 2-5 3-8 0-2-1-3-3-5v-2z"></path><path d="M620 564l3-1v2c0 1-1 2-2 3-1-2-1-3-1-4z" class="C"></path><path d="M687 535l1 1c0 1-3 8-4 9-3 2-2 7-6 8v-2l2-7c1-3 5-6 7-9z" class="D"></path><path d="M615 558c2 2 3 3 5 6 0 1 0 2 1 4-2 2-4 3-5 6h0-2c-2 1-3 2-5 3l-2 1h0v-1s0-1 1-2 1-2 2-3c2-3 5-4 8-6l-1-1c1 0 1 0 1-1l-1-2c-1-1-2-2-2-4z" class="B"></path><path d="M641 549c7-1 13-2 19-3l1 1c-4 2-7 2-10 3s-6 1-8 2l-1 1c-1 1-2 1-3 1s-1-1-1-2h0l-2-1h-5 0c2-2 7-2 10-2z" class="M"></path><path d="M636 551v-1l6-1c1 1 1 2 1 3l-1 1c-1 1-2 1-3 1s-1-1-1-2h0l-2-1z" class="P"></path><path d="M650 583l-3-1h-3c0-2-1-2 0-3h1l1-1c1-2 2-2 3-3s0-3 1-4 1-1 2-1c2 1 3 0 4 0h1v1l-4 11-3 1h0z" class="b"></path><path d="M604 542h3c3 1 5 4 9 4 4 1 8 2 12 2l13 1c-3 0-8 0-10 2h0c-3 1-5 2-7 6h-1c-1-2-2-4-3-5-4-4-11-6-16-10z" class="T"></path><path d="M657 571l1-1c-3-2-6-3-8-4l9 1 9-18c-1 7-4 14-6 20l-9 22c-2 2-3 4-4 6h-1l5-15 4-11z" class="d"></path><path d="M607 578l2-1c2-1 3-2 5-3h2v3l1 1c-2 3-5 0-7 4l-2 1v1 2 1h1c1 0 3-1 4-2 2 0 3 0 5 1 1 0 1 0 2-1h3c1-1 3-1 4-1l2-1h3l1-1c2 0 4 0 6 1-2 1-3 0-4 1h-1c-1 0-2 0-3 1h-2 0-1-1c-1 1-1 1-2 1h-1l-3 1h-2l-2 1-7 2c-2 0-3 1-5 0l-1-2h-1v-1h-1l-1-2c1-2 3-2 3-4l1-1c1-1 2-1 2-2z" class="D"></path><path d="M609 587c1 0 3-1 4-2 2 0 3 0 5 1-1 0-2 0-2 1h-2-1c-1 0-1 0-2 1-1 0-1 0-2-1z" class="B"></path><path d="M580 553c0 1 0 2 1 2 1 1 4 1 5 0 1 0 1 0 1-1-2 0-3-1-4-1h-1c1-2 1-3 2-3 1-1 1-1 2-1 0-2 1-3 1-4 1 0 1 0 2 1h1c1 0 1 1 2 1 0 1 2 2 3 3h4c1 0 2 0 3 1h1l1 1h1l3 1h1c2 1 3 2 4 3s1 1 2 1v1c0 2 1 3 2 4l1 2c0 1 0 1-1 1l1 1c-3 2-6 3-8 6-1 1-1 2-2 3s-1 2-1 2v1l-2-1c1-1 1-2 2-3l1-1c1-2 2-3 4-5-2 0-3 2-4 2l-2 2c1 0 1 0 1 1l-1 1c-1-1-1-2-2-2-2-2-9-5-12-6l-4-2c-3 0-5-1-7 0h-4l-2 2 5-13z" class="I"></path><path d="M578 562c-1-2-1-2 0-3 1-2 2-2 4-2v1c0 1-1 3-2 4h-2z" class="W"></path><path d="M595 550h4l-1 1h0-2c-1 1-1 2-1 3l-2-1c0-1 0 0-1-1-1 0-4-1-5 0h-1-2l1-1h2c1-1 1-1 3-1 1-1 3 0 5 0z" class="L"></path><path d="M590 559c2-1 3-3 5-4 1 0 1 0 2 1h1c1 0 1 0 1 1v1 1l-2 3c-1 0-3 0-4-1s-2-1-3-2z" class="G"></path><path d="M597 562c2 0 3 0 4 1v-1h0v-1c1-1 1-2 2-3v-1c1 1 1 1 1 3l-1 1v1h3c-1-1-1-1-1-2h0c1 1 2 1 2 3l-1 1h1l-1 1c-1 0-1-1-2-2h-1c2 2 3 3 5 4l1 1 3-3v-1l1 2v1h1c1 0 2-1 3-2l1 1c-3 2-6 3-8 6-1 1-1 2-2 3s-1 2-1 2v1l-2-1c1-1 1-2 2-3l1-1c1-2 2-3 4-5-2 0-3 2-4 2l-2 2c1 0 1 0 1 1l-1 1c-1-1-1-2-2-2-2-2-9-5-12-6l-4-2c-3 0-5-1-7 0h-4l1-2h2c2 0 6 0 8-1v-2h0l1-1 1 2v-1c1 1 2 1 3 2s3 1 4 1z" class="K"></path><path d="M650 583h0l3-1-5 15c-1 4-1 6 0 9h-1c-2 3-9 5-10 8 0 1 0 3-1 5l-2 3c-3 6-5 12-8 18-2 4-5 8-7 12-5 9-8 19-11 29h1c-1 2-2 4-1 6v6l-1 2c0 1-1 2-2 3l-1 1c-3-1-6-3-9-5-1 0-3-2-4-3-8-4-16-9-23-15-5-5-5-10-5-16 0-2 0-4-1-5-1-4-3-7-5-10l-3-9 1-2c1-1 2-1 3-2h2v-1-1c1 0 2 0 3-1l-1-1c1-2 1-3 2-4 1-2 3-3 5-5 1-2 2-3 3-5l8-10h2v3h3 0v-1-4c1 0 2-1 4-2l2 1h0c0-1 0-2 1-3s2-2 2-3c1-1 3-2 5-2 1-1 2-3 4-4l1-1 1 2c2 1 3 0 5 0l7-2 2-1h2l3-1h1c1 0 1 0 2-1h1 1 0 2c1-1 2-1 3-1h1c1-1 2 0 4-1v1h9c1-1 1-1 2-1z" class="e"></path><path d="M586 649c2-1 4-1 5 0l3 3-1 2c1 0 1 0 1 1l-3 1c-2-2-3-4-4-6l-1-1z" class="L"></path><path d="M585 661l5 1 1-1 4 4-1 2c-3 0-5 0-8-1l-3-3v-1l2-1z" class="I"></path><path d="M584 648c2 1 2 3 2 4 1 1 1 2 1 3v2h-1v3c-1 0-1 0-2 1h1 0l-2 1h-4l-4 1v-1l2-2-1-1 1-1c1-1 2-1 3-2l1-1c1-1 3-2 3-4 1-1 0-1 0-3z" class="O"></path><path d="M586 657c-2 0-2 0-3-1l1-2c1 0 2 1 3 1v2h-1z" class="C"></path><path d="M580 646c3 0 3 0 5-1 0 1-1 1-1 2l-1 1h1c0 2 1 2 0 3 0 2-2 3-3 4l-1 1c-1 1-2 1-3 2l-1 1c-1 0-2 1-3 0v-1c0-1 1-2 2-3h-1 0-1l-2 2c0-1 0-2-1-3 0-2 1-3 2-4h-1l2-2c1 0 2-1 3-2h1 0l-1 2 1 2c2 0 2-1 3-2v-2h0z" class="J"></path><path d="M583 638h4v1c-1 1-1 2-1 3-1 0-2 1-3 2h0l-2-1v1c-1 0-2 0-3 1h1 1v1h0v2c-1 1-1 2-3 2l-1-2 1-2h0-1c-1 1-2 2-3 2l-2 2h-2l-1-1h0c-1-2-1-3-1-5 1 0 2-1 3-2v-1h0c2-1 2-1 5 0l-1-1c2 0 2 0 3 1l1 1h1l3-3 1-1z" class="a"></path><path d="M574 640c2 0 2 0 3 1l1 1-1 1 1 1-1 1c-2 0-2 0-3-1l2-2h-1c-1 0-3 2-4 2l4-3-1-1z" class="N"></path><path d="M570 641h0c2-1 2-1 5 0l-4 3-3 3v2h0c-1-2-1-3-1-5 1 0 2-1 3-2v-1z" class="J"></path><path d="M598 626l1 1c1 1 2 2 4 3l1 1h-1l1 1 3-2 1 1-1 1c-1 0-1 1-2 2l-1 2h0 1v1c-1 0-1 1-2 2s-3 2-4 3h-2v-2h-1-1 0c1 1 0 2 0 2 1 1 1 2 2 2v1c-2 0-1 0-3-1h-3c1-1 1-2 1-3-2 1-3 3-5 4v-1c1 0 1-1 2-2-1 0-1 0-1-1l2-2h-2v-2h0c0-1 0-1 1-2l2-2h2c1 0 1 0 2-1 0-2 2-4 3-6z" class="N"></path><path d="M598 631l1-1v1l1 1c1 0 1 1 2 1-2 3-3 2-6 3-2 1-4 3-6 3h-2v-2h0c0-1 0-1 1-2l2-2h2c1 0 1 0 2-1s1-1 3-1z" class="R"></path><path d="M595 632c1-1 1-1 3-1v2l-2 1h2v1h-1c-1 0-1 1-2 1h-1l2-2h-1-1c-1 1-1 1-2 1l-2 1-1-1 2-2h2c1 0 1 0 2-1z" class="Q"></path><path d="M611 631l1-1 2 1-6 12v1l-5 7v3l-2 2c-2-1-4 0-7-1 0-1 0-1-1-1l1-2-3-3c-1-1-3-1-5 0 1-2 3-3 4-4 2 1 5 1 7 0v-1c-1 0-1-1-2-2 0 0 1-1 0-2h0 1 1v2h2c1-1 3-2 4-3s1-2 2-2v-1h-1 0l1-2c1 1 2 0 3-1s2-2 3-2z" class="a"></path><path d="M607 639h-1v-2c2-1 3-2 4-2 0 1 0 2-1 3l-2 1z" class="R"></path><path d="M604 643c1 3 1 4-1 7-1 1-2 1-3 2-2-1-2-1-3 0h-3l-3-3c2-1 3-1 5 1l2-2v2l1 1 1-1c0-3 1-2 3-4 0-1 1-2 1-3z" class="J"></path><path d="M611 631l1-1 2 1-6 12v1l-5 7v3l-2 2c-2-1-4 0-7-1 0-1 0-1-1-1l1-2h3c1-1 1-1 3 0 1-1 2-1 3-2 2-3 2-4 1-7l1-1 2 1 1-1c-1-1-1-1-1-2v-1l2-1c1-1 1-2 1-3v-2h1v-2z" class="G"></path><path d="M597 652c1-1 1-1 3 0v1 2c-2-1-3 0-4-2l1-1z" class="C"></path><path d="M576 629c1-1 3-1 5-1 1 0 2-1 3-1 2 0 1 0 3-1h2l1 1c1 1 3 0 5 0-2 0-2 1-3 2h-2c-1 1-1 2-2 2l-2-1c0 1 1 2 1 3l1-1 1 1c-2 2-5 3-6 5l-1 1-3 3h-1l-1-1c-1-1-1-1-3-1l1 1c-3-1-3-1-5 0h0c-2 1-2 1-3 1-2-1-2-1-3-3l1-2c1-1 1-2 2-3h1l4-1-1-2h1v-1c1-1 1-1 2-1h1 1z" class="U"></path><path d="M576 629c1-1 3-1 5-1 1 0 2-1 3-1 2 0 1 0 3-1h2l1 1-4 2c-2 1-5 1-7 2v-1h-2l-1 2h0-1c-1-1-2-1-3-1v-1c1-1 1-1 2-1h1 1z" class="F"></path><path d="M587 633l1-1 1 1c-2 2-5 3-6 5l-1 1-3 3h-1l-1-1c-1-1-1-1-3-1v-1-1h3c-1-2-1-2-2-3 1 0 2 1 3 1s3-1 5-2c1 0 2-1 4-1h0z" class="T"></path><path d="M568 634l4-1c1 1 2 1 3 1v1c1 1 1 1 2 3h-3v1 1l1 1c-3-1-3-1-5 0h0c-2 1-2 1-3 1-2-1-2-1-3-3l1-2c1-1 1-2 2-3h1z" class="K"></path><path d="M567 634h1c1 1 2 3 4 3h1v1 1h-1-3-1l-2 1 1 2c-2-1-2-1-3-3l1-2c1-1 1-2 2-3z" class="J"></path><path d="M569 619c1-2 2-3 3-5 1 1 2 1 3 1 0 1 0 1-1 2 1 0 2 0 3 1h1c2-1 2-2 3-2v2h1 1 2 2l1-1c1 1 2 1 3 1h0 1c2 0 4 0 5-1 2 0 4 1 6 1h2c3-1 8-1 11 0h-3c2 1 3 1 5 2h5l1 2c-1 0-2 1-3 2-1 0-2 0-4 1v1l1 1 1 1h-2 0c-2 0-2 2-3 3l-2-1-1 1c-1 0-2 1-3 2s-2 2-3 1c1-1 1-2 2-2l1-1-1-1-3 2-1-1h1l-1-1c-2-1-3-2-4-3l-1-1h-2l-1 1h0c-2 0-4 1-5 0l-1-1h-2c-2 1-1 1-3 1-1 0-2 1-3 1-2 0-4 0-5 1h-1-1c-1 0-1 0-2 1v1h-1c-1 0-1 0-2-2-1 0-2 0-3 1h-3c-1 1-2 1-3 1v-1c1 0 2 0 3-1l-1-1c1-2 1-3 2-4 1-2 3-3 5-5z" class="B"></path><path d="M618 620c-2 1-5 1-7 1 0-1-1-1-1-1h-1c-1 0-2-1-2-2h1 5c2 1 3 1 5 2zm-54 4h1c1 1 2 1 3 0v1c-1 1-2 2-1 3 1-1 2-1 3-2v1h0c-2 0-3 1-5 2-1 0-1 1-2 1-1 1-2 1-3 1v-1c1 0 2 0 3-1l-1-1c1-2 1-3 2-4z" class="C"></path><path d="M570 627l3-1 3-1h2 0l1 1c-2 1-3-1-3 3h-1-1c-1 0-1 0-2 1v1h-1c-1 0-1 0-2-2-1 0-2 0-3 1h-3c1 0 1-1 2-1 2-1 3-2 5-2z" class="D"></path><path d="M606 626h-3l2-2c1 0 2 0 2 1h2c1 0 2 0 3-1 1 0 2-1 3-1l1 1h1v1 1l1 1 1 1h-2 0c-2 0-2 2-3 3l-2-1c1-1 2-3 3-4h-1c-2 0-3 0-5 2h0l-2-2h-1z" class="C"></path><path d="M569 619c1-2 2-3 3-5 1 1 2 1 3 1 0 1 0 1-1 2 1 0 2 0 3 1h1c2-1 2-2 3-2v2h1 1 2 2l1-1c1 1 2 1 3 1h0c-2 1-3 1-4 1-3 2-9 2-12 1l-1-1h1 1v-1h-3l-1 1-1-1c-1 1-2 1-2 1zm45 7h1c-1 1-2 3-3 4l-1 1c-1 0-2 1-3 2s-2 2-3 1c1-1 1-2 2-2l1-1-1-1-3 2-1-1h1l-1-1c-2-1-3-2-4-3l7-1h1l2 2h0c2-2 3-2 5-2z" class="K"></path><path d="M614 626h1c-1 1-2 3-3 4l-1 1c-1 0-2 1-3 2s-2 2-3 1c1-1 1-2 2-2l1-1-1-1h1c1-1 1 0 2 0 1-2 2-3 4-4z" class="i"></path><path d="M624 622h0 3v-1c2-1 4-2 6-4h2l1 2-2 3c-3 6-5 12-8 18-2 4-5 8-7 12-5 9-8 19-11 29h1c-1 2-2 4-1 6v6l-1 2c0 1-1 2-2 3l-1 1c-3-1-6-3-9-5 1-1 1-1 3-2 1-2 2-3 2-5v-1l1 3v-1-1l1-1-1-1c0-1 0-1-1-2 0-1 1-2 1-3-1 0-2 0-3-1h1c1-2-1-5-1-7h1v1c1 1 2 1 3 2l1 1v-1c-1-2-5-6-5-9h1v-1c-1-1-2-2-3-2 0 1 0 0-1 1l-1-2-2-2h0v-1c1 0 2 0 3-1h0c3-2 9 1 12 2 1 0 1 1 2 1v-1c-2-2-5-3-8-4l2-2v-3l5-7v-1l6-12c1-1 1-3 3-3h0 2l-1-1-1-1v-1c2-1 3-1 4-1 1-1 2-2 3-2z" class="H"></path><path d="M621 629h4c0 2-1 2-3 3l1-2-2-1z" class="I"></path><path d="M609 681c-1 2-2 4-1 6v6l-1 2c0 1-1 2-2 3l-1 1v-2l4-16h1z" class="e"></path><path d="M599 665h0v-1l1-1c0 1 0 1 1 2 0 1 2 3 4 5 1 1 0 0 0 1l1 1v2l-1 1-1-1-1 1c-1-2-5-6-5-9h1v-1z" class="C"></path><path d="M621 624c3 0 7-1 10 0 0 1-1 3-1 5h-5-4l-2-1h0l-1-1-1-1v-1c2-1 3-1 4-1z" class="W"></path><path d="M614 631c1-1 1-3 3-3h0 2 0l2 1 2 1-1 2v2 1 1l-2-1c0 1 1 1 1 2v1h-2-3l-1 1h2 2v1c-1 0-3 1-5 1v1h6c-1 1-1 0-2 1-2 0-4 1-5 1s-2 1-3 1v-2c-1 1-1 1-2 1h0v-1l6-12z" class="Q"></path><path d="M619 628l2 1 2 1-1 2v2 1 1l-2-1c0 1 1 1 1 2v1h-2v-1c-1 0-2-1-3-2h3l-1-1h-1c0-2 0-1 1-2l-1-2 2-2z" class="C"></path><path d="M613 644c1 0 3-1 5-1-1 2-1 3-2 5v2c-2 1-3 3-5 4l-1 1c1 1 2 2 2 3l-1 1v-1l-1 1h-1v1c-2-2-5-3-8-4l2-2v-3l5-7h0c1 0 1 0 2-1v2c1 0 2-1 3-1z" class="E"></path><g class="C"><path d="M611 654h0c-1-1-1-1-1-2 1 0 2-1 2-2h1 3c-2 1-3 3-5 4z"></path><path d="M613 644c1 0 3-1 5-1-1 2-1 3-2 5h-1c-2 0-3 2-5 3l-1-1 1-4h0c1 0 2 0 3-1v-1z"></path></g><path d="M604 654l1 1c1 0 2-1 3-2h1c0 1 1 2 1 2 1 1 2 2 2 3l-1 1v-1l-1 1h-1v1c-2-2-5-3-8-4l2-2h1z" class="F"></path><path d="M608 644h0c1 0 1 0 2-1v2c1 0 2-1 3-1v1c-1 1-2 1-3 1h0l-1 4v1 1h-1 0-2l-2 2h-1v-3l5-7z" class="K"></path><path d="M606 652v-1c1-2 2-3 4-5l-1 4v1 1h-1 0-2z" class="D"></path><defs><linearGradient id="Ap" x1="559.545" y1="664.49" x2="596.611" y2="658.472" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#Ap)" d="M563 630h3c1-1 2-1 3-1 1 2 1 2 2 2l1 2-4 1h-1c-1 1-1 2-2 3l-1 2c1 2 1 2 3 3 1 0 1 0 3-1v1c-1 1-2 2-3 2 0 2 0 3 1 5h0l1 1h2 1c-1 1-2 2-2 4 1 1 1 2 1 3l2-2h1 0 1c-1 1-2 2-2 3v1c1 1 2 0 3 0l1 1-2 2v1l4-1h4v1l3 3c3 1 5 1 8 1l1-2 2 3 1 1c1 2 3 4 4 6-1-1-2-1-3-2v-1h-1c0 2 2 5 1 7h-1c1 1 2 1 3 1 0 1-1 2-1 3 1 1 1 1 1 2l1 1-1 1v1 1l-1-3v1c0 2-1 3-2 5-2 1-2 1-3 2-1 0-3-2-4-3-8-4-16-9-23-15-5-5-5-10-5-16 0-2 0-4-1-5-1-4-3-7-5-10l-3-9 1-2c1-1 2-1 3-2h2v-1c1 0 2 0 3-1z"></path><path d="M593 688c-1-2-2-3-2-4h1c1 1 2 2 2 3l1 1-1 1h-1v-1z" class="H"></path><path d="M569 658l-1 1c-2-1-2-1-3-2 0-1 0-2-1-3v-2h-1c0-2-1-4-2-5v-1c-1-1-1-1-1-2l-1-2v-3l2-2h0c0-1 1-1 2-1 0-1 0-1 1-2s2 0 3 0c-1 1-1 2-2 3-3 0-3 1-5 3 0 1 0 2 1 3 2 3 3 8 4 11 1 1 1 0 1 2 0 1 1 1 2 2h1 0z" class="F"></path><path d="M595 665l2 3h-2c-1 1-1 2-2 2h-3-3v1h-1l-1-1-1-1h-2v-1h0c-1 1-1 2-2 3 0 1 1 2 1 4-1-1-1-2-2-3 0-1 0-4 2-4v-1-1h1 4 0c3 1 5 1 8 1l1-2z" class="B"></path><path d="M582 669h2l1 1 1 1h1 0c2 0 3 0 4 1 0 1 0 1 1 2h1 2c1 0 1 1 2 2h-2l1 1-1 1h-3 0c-3 0-5-1-7-3l-2-2c-1 0-1 0-1-1v-3z" class="D"></path><path d="M586 671h1 0c2 0 3 0 4 1h0c-1 1-1 2-1 3h0c-2 0-2 0-3-1 0-1 0-1-1-2h0v-1z" class="K"></path><path d="M597 668l1 1c1 2 3 4 4 6-1-1-2-1-3-2v-1h-1c0 2 2 5 1 7h-1c1 1 2 1 3 1 0 1-1 2-1 3-2 0-4 0-6-1h-3c0 1 1 1 1 2h-1c0 1 1 2 2 4l-2-1h-2l-2-2c-1 0-2-1-2-1l-1-1 2-1h1c1-1 1-1 1-2 1-2 3-1 4-2h0 3l1-1-1-1h2c-1-1-1-2-2-2h-2-1c-1-1-1-1-1-2-1-1-2-1-4-1h0v-1h3 3c1 0 1-1 2-2h2z" class="E"></path><path d="M591 687l-2-2v-3h2c0 1 1 1 1 2h-1c0 1 1 2 2 4l-2-1z" class="I"></path><path d="M597 668l1 1c-1 1-1 2-1 3h-3c-1-1-1-1-2-1h-5 0v-1h3 3c1 0 1-1 2-2h2z" class="C"></path><path d="M569 658h0-1c-1-1-2-1-2-2 0-2 0-1-1-2-1-3-2-8-4-11-1-1-1-2-1-3 2-2 2-3 5-3l-1 2c1 2 1 2 3 3 1 0 1 0 3-1v1c-1 1-2 2-3 2 0 2 0 3 1 5h0l1 1h2 1c-1 1-2 2-2 4 1 1 1 2 1 3l2-2h1 0 1c-1 1-2 2-2 3v1c1 1 2 0 3 0l1 1-2 2v1c-1 1-5 3-6 4l-1-1 1-1c0-2 1-3 2-5v-1l-2-1z" class="B"></path><path d="M573 658v-1l-2 1-1-2c-1-1-2-1-2-2-1-2-2-1-3-3 1-2 1-4 1-6-1 0-1-1-2-2l1-1 2 2c0 2 0 3 1 5h0l1 1h2 1c-1 1-2 2-2 4 1 1 1 2 1 3l2-2h1 0 1c-1 1-2 2-2 3z" class="G"></path><path d="M650 583h0l3-1-5 15c-1 4-1 6 0 9h-1c-2 3-9 5-10 8 0 1 0 3-1 5l-1-2h-2c-2 2-4 3-6 4v1h-3 0l-1-2h-5c-2-1-3-1-5-2h3c-3-1-8-1-11 0h-2c-2 0-4-1-6-1-1 1-3 1-5 1h-1 0c-1 0-2 0-3-1l-1 1h-2-2-1-1v-2c-1 0-1 1-3 2h-1c-1-1-2-1-3-1 1-1 1-1 1-2-1 0-2 0-3-1l8-10h2v3h3 0v-1-4c1 0 2-1 4-2l2 1h0c0-1 0-2 1-3s2-2 2-3c1-1 3-2 5-2 1-1 2-3 4-4l1-1 1 2c2 1 3 0 5 0l7-2 2-1h2l3-1h1c1 0 1 0 2-1h1 1 0 2c1-1 2-1 3-1h1c1-1 2 0 4-1v1h9c1-1 1-1 2-1z" class="I"></path><path d="M631 612l1 3c0 1-1 1-2 1 0 0-1 0-1-1l2-3z" class="H"></path><path d="M626 617c1-2 3-4 3-6h0 1c2-1 3-2 4-4 0 2 0 4-2 5h-1l-2 3v-1l2-2c-2 0-2 2-4 3 0 1-1 1-1 2z" class="B"></path><path d="M636 588c2-1 5 0 6-1s0-2 2-1c1 1 1 1 2 1 1 1 1 0 2 0 0 2 0 1-1 3l-2-2h-1c0 1 1 1 2 2v4l-2 2h0c-2 1-3 1-4 1h-2l2 2h0c1 0 2 1 2 1 1 1 1 1 1 3-2 0-4 1-6 2l1-3 2-1h0c-1-2-2-2-3-2l-2-3 7-1v-1h-1-2v-1h1v-1c-2-1-3-1-5-1 1-1 1-1 2-3h-1z" class="E"></path><path d="M630 598l1-1v1l1-1v-1h3l2 3c1 0 2 0 3 2h0l-2 1-1 3h0l-3 2c-1 2-2 3-4 4h-1 0c0 2-2 4-3 6-3 2-6 0-10 1-3-1-8-1-11 0h-2c-2 0-4-1-6-1 5-1 10-1 15-1l-1-1c-1-2-2-2-2-4 1 0 1 0 2-1l3 2 1-1c-1 0-1-1-1-1l1-2 1 1c2-1 3-3 6-3 1 0 0 0 1-1-1 0-1 0-1-1h1c0-1 0-2 1-3v-1c1 0 2-1 3-2h1 2z" class="D"></path><path d="M636 602h2l-1 3h0-1l-1-1 1-2z" class="O"></path><path d="M630 598l1-1v1l1-1v-1h3l2 3c1 0 2 0 3 2h0l-2 1h-2-1v-1c-2 0-2 0-3-1l-3 3h2c1 1 1 2 2 2 0 1 0 1-1 1-1 1 0 1-1 1-1 1-1 1-2 1h0c0 2 0 2-1 3l-2-1-2 1c0-1 1-1 0-2h0c-2 1-4 0-6 0h0-2c2-1 3-3 6-3 1 0 0 0 1-1-1 0-1 0-1-1h1c0-1 0-2 1-3v-1c1 0 2-1 3-2h1 2z" class="K"></path><path d="M613 593c7-3 15-5 23-5h1c-1 2-1 2-2 3 2 0 3 0 5 1v1h-1v1h2 1v1l-7 1h-3v1l-1 1v-1l-1 1h-2-1c-1 1-2 2-3 2v1c-1 1-1 2-1 3h-1c0 1 0 1 1 1-1 1 0 1-1 1-3 0-4 2-6 3l-1-1-1 2s0 1 1 1l-1 1-3-2h0c2-1 2-2 3-3-1 0-1 1-2 1s-2-1-3-2l2-1c-1-1-1-2-2-3h-1 1l-1-1v-2c-2-1-3-2-4-3v-1c1-1 1-1 2-1 3 0 5-1 7-1z" class="R"></path><path d="M618 599h2l1 1h1c-1 1-3 2-3 3h-1l-2-1h-1c-2 1-3 2-4 3-1-1-1-2-2-3h-1 1 1c1-1 1-1 2-1 3 0 4-1 6-2z" class="V"></path><path d="M618 599h2l1 1h1c-1 1-3 2-3 3h-1l-2-1h-1v-1c1 0 1 0 2-1l1-1z" class="c"></path><path d="M615 602h1l2 1h1l1 2h-2l-1 2h-3c-1 0-1 1-2 1s-2-1-3-2l2-1c1-1 2-2 4-3zm5-3c2-2 4-3 6-3h1l2-2c1 0 2-1 3-2h0 1l1 1-2 1c-1 1-1 1-2 1v3h-2-1c-1 1-2 2-3 2h-2-1l-1-1z" class="k"></path><path d="M622 600h2v1c-1 1-1 2-1 3h-1c0 1 0 1 1 1-1 1 0 1-1 1-3 0-4 2-6 3l-1-1-1 2s0 1 1 1l-1 1-3-2h0c2-1 2-2 3-3h3l1-2h2l-1-2c0-1 2-2 3-3z" class="Q"></path><path d="M613 593c7-3 15-5 23-5h1c-1 2-1 2-2 3l-1-1v1h-1c-1-1-1-1-2-1h-1c0 1-1 1-2 1l-2 2h-1c-1 0-3 1-4 1-2 1-4 1-6 1v2h-2l-1-1 2-2-1-1z" class="J"></path><path d="M604 588l1 2c-2 0-2 1-3 2 1 2 3 1 4 2-1 0-1 0-2 1v1c1 1 2 2 4 3v2l1 1h-1 1c1 1 1 2 2 3l-2 1c1 1 2 2 3 2s1-1 2-1c-1 1-1 2-3 3h0c-1 1-1 1-2 1 0 2 1 2 2 4l1 1c-5 0-10 0-15 1-1 1-3 1-5 1h-1 0c-1 0-2 0-3-1l-1 1h-2-2-1-1v-2c-1 0-1 1-3 2h-1c-1-1-2-1-3-1 1-1 1-1 1-2-1 0-2 0-3-1l8-10h2v3h3 0v-1-4c1 0 2-1 4-2l2 1h0c0-1 0-2 1-3s2-2 2-3c1-1 3-2 5-2 1-1 2-3 4-4l1-1z" class="N"></path><path d="M580 604h2v3h3 0v-1-4c1 2 1 3 3 5l2 1c0 1-1 2-2 3h-3v-4l-1 1h-3v-3l-1-1z" class="J"></path><path d="M608 601l1 1h-1 1c1 1 1 2 2 3l-2 1v1l-2 1c-2 2-7 1-10 1h0c1-1 2-2 4-2h1c1-1 3-2 3-3l3-3z" class="Q"></path><path d="M580 604l1 1v3h3l1-1v4l-1-1v1l-1 1 1 1c1 0 1 0 2-1h0 2c1 1 2 2 3 2-1 1-2 1-3 2l-1-1c-1 0-1-1-2-1-1 1-1 1-1 2h-1-2c-1 0-1 1-3 2h-1c-1-1-2-1-3-1 1-1 1-1 1-2-1 0-2 0-3-1l8-10z" class="a"></path><path d="M607 611h2c0 2 1 2 2 4l1 1c-5 0-10 0-15 1-1 1-3 1-5 1h-1 0c-1 0-2 0-3-1l-1 1h-2-2-1-1v-2h2 1c0-1 0-1 1-2 1 0 1 1 2 1l1 1c1-1 2-1 3-2l2 2v-1-1l1-2h1c1 0 2-1 4-1h5 3z" class="i"></path><path d="M604 611h3l2 2-1 1c-2 0-2 0-4-1v-2zm-11 4v-1l1-2h1c1 0 2-1 4-1 2 1 3 1 4 1v1 1h-4-2l-1 1-1-1c-1 0-1 1-2 1z" class="G"></path><path d="M604 588l1 2c-2 0-2 1-3 2 1 2 3 1 4 2-1 0-1 0-2 1v1c1 1 2 2 4 3v2l-3 3c0 1-2 2-3 3h-1c-2 0-3 1-4 2l-1-1c-1 0-2 0-3-1-2-1-2-4-2-6h0c0-1 0-2 1-3s2-2 2-3c1-1 3-2 5-2 1-1 2-3 4-4l1-1z" class="E"></path><path d="M599 597l1-1c2 1 3 1 5 3 0 2 0 3-2 5-2 0-3 0-4-1s-3-2-3-4c1-1 2-2 3-2z" class="Q"></path><path d="M346 254c3-2 6-6 10-8 2-1 4-2 5-3 3-2 3-5 7-6 1 0 1-1 2 0l-1 1c-2 2-4 4-6 7l-3 3-1-1c-1 1-2 1-4 2 0 1-1 2-2 3 0 3 0 5-1 8 0 2-2 5-1 7h1v1l1 1v2c0 1 1 1 1 1h0c-1 2-2 1-3 2l-3-3c0 2 1 3 1 5 0 1-1 2-2 2l1 2 1 4h-1c-1-1-1-2-1-3h-1l-1 1 2 3h-1-2l-1 1h-1l1-2h-1v-1h0l-2 2 1 1c-1 0-2 0-3-1l-2 1-1-1v1 3h1 0c1 0 1 0 2 1l6 6h1l2 5v1l-1 1c1 0 1 1 2 1 1 3 3 5 4 8 0 1 1 1 1 2h1 0c2 1 1 2 1 4h1l1-1c1 0 1 0 2-1v2c-1 1-1 1-2 1s-1 0-2 1h-1c-1 0-2 1-3 1 0 0 1 1 1 0l1 2v-2h3v1h-1c-1 1-1 2-3 2 1 1 2 1 3 1h1 3s1 1 2 1c-1 1-1 1-1 2h0c1 1 1 1 1 3h-1v2 1h1 1 1c1 1 1 1 1 2 1 1 1 2 1 3h1l1 3c0 1 0 1 1 2 0 1-1 1 0 2 1 2 1 3 2 4l-1 1c0 1 1 2 2 2 0 1 1 2 1 4l-2 2v1h-1l-1 2 2 1h4v1l1 1 1 1v1c0 1 1 1 1 1 0 2 1 2 0 4h1l1-1v2c2 3 3 6 4 9 1 1 1 2 1 3h0c1 1 1 1 1 2v3c-1 0-2 1-3 1l1 1h2c1 0 1-1 2-1h1v-1h-1v-1h1l-1-2 1-1v1h1l2 3 2 6 2 6 3 7v1l1 1v1h1c0 1 1 1 2 2v1l2 2h0c3 2 5 5 8 7v1c0 1-1 2-1 3h-1l-2-3h-3l-1 1v-1l-1 1c-1 2-1 4-3 5h-1l-6 3-7 2-3 1-7 2h-2-3c-1 1-2 1-4 1-5 1-12 0-18 0-1 1 0 1-1 0h-3-2l-3 2c-4-1-7-2-11-2l-1-1h-2l-1 1c0 2 1 4 2 6h0v4c1 1 1 2 2 4h-1 0l-1 1h-1c0-1 0-1-1-2v-1h-1l-2-6c0-2-1-3-1-5 0-1 0-2-1-3l-5 13-1 1-1-1c-1 1-1 0-2 1l-1 2h0c-1-1-1-2-1-3 2-9 5-17 10-26h-1c-1 0-3 0-4-1h3c-1-1-3-2-4-2-8-3-13-9-19-16-2-2-5-5-6-8h0 2c0-1-3-3-3-4v-2h0l-4-7c1-2 1-3 2-4h0c3 0 4-1 6-3l-4-8c-2-7-5-14-8-21 0-1 1-2 1-3-3-5-3-10-6-15l-16-37-1 1-2-3h1l1-1c0-2 2-3 3-5 0-1-1-2-1-3s1-3 2-4l1-1 1-1c1 0 3 0 3 1l9 1h9 0v-1l2-2c0-1 1-1 2-1l1-1 1-2c0 1 0 1 1 2v-2h2c3 0 6 0 8-1 1-1 1 0 2 0l3-2h2l2-1h1c2-1 2-2 3-3v2h1l1-2-2-1-1-1 2-1c1 1 1 1 2 1l1 1v-1h2 1c1 0 2-1 2-2l1-1h2l-1-1s-1 0-1-1c-1-1-2 0-4-1 0-2 0-3 2-5 1 2 2 3 4 4h1 1v-3l-1-1 1-1c2 2-1 4 3 4 0 0-1 1-1 2h1 0c1-1 1-1 2-1l1-1h1 0l1 1c0-1 1-1 2-1-1-1-1-1-1-2l-1-1 2 1 2-1c0 1 0 1 1 2l2-2v1l3 1z" class="C"></path><path d="M281 307h0v10 1h-1c-1-1-1-2-1-4v-1-2c1-1 1-1 1-2l1-2z" class="F"></path><path d="M280 300h0c0 1 0 0 1 1-1 3-4 5-6 6v2 1c1 1 2 3 2 4h-1 0c0-2-1-2-1-3-2-2-1-3-1-6 1 0 2-4 4-5h2z" class="I"></path><path d="M313 367l1 1h0l-1 2-2-1v1 1c1 0 1 0 2 1h1v1h0-2l-1 3 1 1c1 0 1-1 2-2 0 2 0 2-1 3-2 0-2-1-3-2v-1h-1v-1h-1-1v-2-5-1h1 0v2h1 1v-2l2 1h1z" class="G"></path><path d="M271 301c1 0 2 0 4-1 1-1 1-2 2-3 0-1 0-1 1-1 1 1 2 1 2 2v2h-2c-2 1-3 5-4 5l-2 2c1 1 1 2 0 3 0-2-1-4-2-5 0 0-1-1-2-1-1-1-1-1-1-3h1 1 1 1z" class="j"></path><path d="M298 292c6-2 12-4 17-7v1l-6 3c-7 3-14 5-21 6-3 1-7 1-10 1-1 0-1 0-1 1-1 1-1 2-2 3-2 1-3 1-4 1l3-6v-1c9 1 16 0 24-2h0z" class="W"></path><path d="M345 270c2 0 2 0 3 1 0 2 1 3 1 5 0 1-1 2-2 2l1 2h-2c-3-2-5-1-8-2h5v-1h2 1l-1-2h-1l-1 1c-1 0-1-1-2-1l1-1h1c1 0 2 1 3 0-1-1-1-3-2-4-3 1-6 2-9 4l-10 7-1-1c5-4 14-11 21-10z" class="H"></path><path d="M345 270c2 0 2 0 3 1 0 2 1 3 1 5 0 1-1 2-2 2l-2-8z" class="T"></path><path d="M271 316l1-2h2c0 1 1 3 1 4l3 5c2 3 3 7 4 10 1 2 2 5 3 8 0 1 1 2 1 4h1v3l-2-2c-2-1-8-17-9-20l-5-10z" class="B"></path><path d="M325 415c2-3 3-7 4-10s1-6-1-8c-1-2-3-4-5-4-1-1-1 0-2-1l1-1c1-1 2-1 4-1 3 1 6 4 7 6 1 0 2 3 2 3 0 3 0 4-1 6-2 5-3 10-7 14h-1l-1 1v-4-1z" class="P"></path><path d="M342 264l5 1c1 1 1 0 1 1h1c1 1 2 1 3 2l1 1v2c0 1 1 1 1 1h0c-1 2-2 1-3 2l-3-3c-1-1-1-1-3-1-7-1-16 6-21 10-3 2-6 3-9 5-5 3-11 5-17 7h0c-8 2-15 3-24 2v1l-3 6h-1-1-1l-2-3c-1-2-1-4-3-5 1-2 0-3 2-4l1-1c2 0 0 0 2 1h9c2 0 4-1 6-1h-2c-2-1-7 0-9-2-1-1-1-1-1-3 22 3 41-8 61-17 2 0 4-1 6-1 1 0 2-1 4-1z" class="g"></path><path d="M283 288h1c12-1 23-4 35-8-2 1-2 2-4 3-6 2-10 5-17 7v2h0c-8 2-15 3-24 2v1l-3 6h-1-1-1l-2-3c-1-2-1-4-3-5 1-2 0-3 2-4l1-1c2 0 0 0 2 1h9c2 0 4-1 6-1z" class="i"></path><path d="M263 293c1-2 0-3 2-4l1-1v6h1v-1c0-1 0-1 1-2l1-1c8 1 15 2 23 1 2 0 4 0 6-1v2h0c-8 2-15 3-24 2v1l-3 6h-1-1-1l-2-3c-1-2-1-4-3-5z" class="d"></path><path d="M263 293c1-2 0-3 2-4l1-1v6h1v-1c0-1 0-1 1-2l1-1c0 2 0 3 1 5h1v-1l1-1v1 3c0 1-2 2-2 4h-1-1l-2-3c-1-2-1-4-3-5z" class="Y"></path><path d="M346 254c3-2 6-6 10-8 2-1 4-2 5-3 3-2 3-5 7-6 1 0 1-1 2 0l-1 1c-2 2-4 4-6 7l-3 3-1-1c-1 1-2 1-4 2 0 1-1 2-2 3 0 3 0 5-1 8 0 2-2 5-1 7h1v1c-1-1-2-1-3-2h-1c0-1 0 0-1-1l-5-1c-2 0-3 1-4 1-2 0-4 1-6 1-20 9-39 20-61 17l-6-1c-2 2-3 4-4 6v2h0l-1 1h-1v-1c-1 1-1 1-1 3h-1l1 1-1 1-2-3h1l1-1c0-2 2-3 3-5 0-1-1-2-1-3s1-3 2-4l1-1 1-1c1 0 3 0 3 1l9 1h9 0v-1l2-2c0-1 1-1 2-1l1-1 1-2c0 1 0 1 1 2v-2h2c3 0 6 0 8-1 1-1 1 0 2 0l3-2h2l2-1h1c2-1 2-2 3-3v2h1l1-2-2-1-1-1 2-1c1 1 1 1 2 1l1 1v-1h2 1c1 0 2-1 2-2l1-1h2l-1-1s-1 0-1-1c-1-1-2 0-4-1 0-2 0-3 2-5 1 2 2 3 4 4h1 1v-3l-1-1 1-1c2 2-1 4 3 4 0 0-1 1-1 2h1 0c1-1 1-1 2-1l1-1h1 0l1 1c0-1 1-1 2-1-1-1-1-1-1-2l-1-1 2 1 2-1c0 1 0 1 1 2l2-2v1l3 1z" class="d"></path><path d="M343 252v1l3 1c-2 1-3 2-5 3-1-1-2-1-3-2s-1-1-1-2l-1-1 2 1 2-1c0 1 0 1 1 2l2-2z" class="F"></path><path d="M314 264c2 0 3 1 4 2h1c1-1 2-1 4 0h0l-10 5v-3h-2c2-1 2-2 3-3v2h1l1-2-2-1z" class="J"></path><path d="M290 272c0 1 0 1 1 2v-2h2c0 1 1 3 3 4h0c-4 1-8 3-12 3v-1l2-2c0-1 1-1 2-1l1-1 1-2z" class="D"></path><path d="M311 268h2v3c-5 3-11 4-17 5h0c-2-1-3-3-3-4 3 0 6 0 8-1 1-1 1 0 2 0l3-2h2l2-1h1z" class="G"></path><path d="M331 265c5-4 12-5 18-9 3-2 3-6 6-7 0 1-1 2-2 3 0 3 0 5-1 8 0 2-2 5-1 7h1v1c-1-1-2-1-3-2h-1c0-1 0 0-1-1l-5-1c-2 0-3 1-4 1-2 0-4 1-6 1l-1-1z" class="i"></path><path d="M342 264c2-1 5-2 8-2 0 1-1 3-1 4h-1c0-1 0 0-1-1l-5-1z" class="P"></path><path d="M322 252c1 2 2 3 4 4h1 1v-3l-1-1 1-1c2 2-1 4 3 4 0 0-1 1-1 2h1 0c1-1 1-1 2-1l1-1h1 0l1 1c0-1 1-1 2-1 1 1 2 1 3 2l-18 9h0c-2-1-3-1-4 0h-1c-1-1-2-2-4-2l-1-1 2-1c1 1 1 1 2 1l1 1v-1h2 1c1 0 2-1 2-2l1-1h2l-1-1s-1 0-1-1c-1-1-2 0-4-1 0-2 0-3 2-5z" class="E"></path><path d="M331 265l1 1c-20 9-39 20-61 17l-6-1c-2 2-3 4-4 6v2h0l-1 1h-1v-1c-1 1-1 1-1 3h-1l1 1-1 1-2-3h1l1-1c0-2 2-3 3-5 2-2 3-5 6-6 0 1 1 1 1 1 23 4 44-7 64-16z" class="e"></path><path d="M258 293c0-2 0-2 1-3v1h1l1-1h0v-2c1-2 2-4 4-6l6 1c0 2 0 2 1 3 2 2 7 1 9 2h2c-2 0-4 1-6 1h-9c-2-1 0-1-2-1l-1 1c-2 1-1 2-2 4 2 1 2 3 3 5l2 3h-1c0 2 0 2 1 3 1 0 2 1 2 1 1 1 2 3 2 5 0 1 1 2 1 3 1 1 1 0 1 1h-2l-1 2 5 10c1 3 7 19 9 20l2 2v2l3 7c0 1 1 2 1 3l4 11 1 2v1c1 1 2 3 2 4l3 8c0 1 1 2 1 3l1 2v1c0 1 1 2 1 3l7 20c0 1 1 2 1 3l1-1-1-2h1v1c0 1 1 1 2 2v-2l1-1c1 0 2 1 3 1h3c2-2 2-4 3-6s1-2 2-3v1c-1 1-2 3-2 4v3 1l-6 9h-1c-1 0-3 0-4-1h3c-1-1-3-2-4-2-8-3-13-9-19-16-2-2-5-5-6-8h0 2c0-1-3-3-3-4v-2h0l-4-7c1-2 1-3 2-4h0c3 0 4-1 6-3l-4-8c-2-7-5-14-8-21 0-1 1-2 1-3-3-5-3-10-6-15l-16-37-1-1h1z" class="H"></path><path d="M288 398h0 2c0-1-3-3-3-4v-2l7 10-1 1h1v3c-2-2-5-5-6-8z" class="X"></path><path d="M294 402c5 6 10 11 17 16 1 1 7 5 7 6h-1c-1-1-3-2-4-2-8-3-13-9-19-16v-3h-1l1-1z" class="g"></path><path d="M280 346c2 3 4 7 5 11l8 20 7 20c1 4 3 7 3 11-3-4-4-10-6-15l-6-15-4-8c-2-7-5-14-8-21 0-1 1-2 1-3z" class="f"></path><path d="M258 293c0-2 0-2 1-3v1h1l1-1h0v-2c1-2 2-4 4-6l6 1c0 2 0 2 1 3 2 2 7 1 9 2h2c-2 0-4 1-6 1h-9c-2-1 0-1-2-1l-1 1c-2 1-1 2-2 4 2 1 2 3 3 5l2 3h-1c0 2 0 2 1 3 1 0 2 1 2 1 1 1 2 3 2 5 0 1 1 2 1 3 1 1 1 0 1 1h-2l-1 2 5 10c1 3 7 19 9 20l23 67s-1-1-1-2c-5-11-8-22-12-33l-17-41-5-13-15-31z" class="h"></path><path d="M271 316c-2-3-4-7-5-10-1-2-1-4-2-5v-1c0-1 0-3-1-5h-1c-1-1-1-3 0-4h0l1 2c2 1 2 3 3 5l2 3h-1c0 2 0 2 1 3 1 0 2 1 2 1 1 1 2 3 2 5 0 1 1 2 1 3 1 1 1 0 1 1h-2l-1 2z" class="G"></path><path d="M322 298v-4h2c1-1 1-1 1-2h-2 0c2-2 4-3 7-4h1 2l1-1 1 2h1 0c1 0 1 0 2 1l6 6h1l2 5v1l-1 1c1 0 1 1 2 1 1 3 3 5 4 8 0 1 1 1 1 2h1 0c2 1 1 2 1 4h1l1-1c1 0 1 0 2-1v2c-1 1-1 1-2 1s-1 0-2 1h-1c-1 0-2 1-3 1 0 0 1 1 1 0l1 2v-2h3v1h-1c-1 1-1 2-3 2 1 1 2 1 3 1h1 3s1 1 2 1c-1 1-1 1-1 2h0c1 1 1 1 1 3h-1v2 1h1 1 1c1 1 1 1 1 2 1 1 1 2 1 3h1l1 3c0 1 0 1 1 2 0 1-1 1 0 2 1 2 1 3 2 4l-1 1c0 1 1 2 2 2 0 1 1 2 1 4l-2 2v1h-1l-1 2 2 1h4v1l1 1 1 1v1c0 1 1 1 1 1 0 2 1 2 0 4h-3l1 1h-3l-1-1h0v-1l-1 1h-1 0l-1 1h2v2c0 1 1 1 2 1l-1 1h-2c-1 1-1 1-2 1v-1-5h-1v1h-2v-3-1l-1-1-2 1c-1 0-1 0-2-1v1h-1l-2-1h0v1h-1 0c-2-2-5-4-8-4-5 0-8-2-12 0l-1 1-1-1c-1 1-2 1-3 2-1 0-1 0-1-1l-2-3c-1 1-3 2-4 2-1 1-1 2-3 2h0l-2 1-1-1h-2c0-1 0-2-1-2-1 1-1 1-1 2h-1c0-1 0-2-1-3-1 0-1 0-2-1v-1c0-1 1-1 1-2s-1-2-1-3c-1 3-3 5-6 7l-1-1c1-1 1-1 2-1l3-6v-1c1-2 1-4 1-6-1-1-1-3-1-4h-2l-2 1h-1v-1c-1-1-1-2-1-3h3v1l-2-5 2-5c1-2 1-3 1-5v-2c-1-1 0-1 0-2v-2c-1-1-1-1-2-1 0-1 0-1-1-2h0v-1c0-3-1-4 1-6 0-1 0-1 1-2 0-2 1-3 1-4s1-1 1-2h1 1 1v1l1-1c1 0 1 0 2-1h1v1l3-4c1 0 2-1 3-1h1c1-1 1-1 2-1z" class="U"></path><path d="M331 357l1-2c0-1 0-1 1-2l1-1v3 1c-1 1-1 2-1 3l-2-2z" class="G"></path><path d="M374 364l1 1 1 1v1c0 1 1 1 1 1l-2 1c-1 0-2-2-3-2l1-1c0-1 1-1 1-2z" class="E"></path><path d="M362 364c2 0 3 0 5 1-1 1-1 2-3 3v-1h-5c1-1 1-1 1-2s0 0 1-1h0 1z" class="B"></path><path d="M302 345c2-1 4-1 6-1 0 1 0 2 1 3v-1c1 0 2 1 3 2 0 2-2 2-2 4h0l-1 2c-1-1 1-4 0-5h-1 0c-1-1-1-3-1-4h-2l-2 1h-1v-1z" class="D"></path><path d="M331 357l2 2 1 2 1-1h2l-3 5h-1c-1 1-2 1-3 2-1 0-1 0-1-1h1v-1h1c1-3 0-5 0-8h0z" class="B"></path><path d="M362 364c1-1 1-2 2-3h2 0l1 1-1 2h0l1-1 1-1 2 1h4v1c0 1-1 1-1 2l-1 1-2-1c0-1-1-1-1 0h-1l-1-1c-2-1-3-1-5-1z" class="M"></path><path d="M339 321h1v1h1v1h1c-1 3-4 4-5 7h-1v-1c-1 0 0 0-1 1h-1l-1-1c1-1 2-2 2-3v-1l4-4z" class="a"></path><path d="M348 332l1 1c-1 0 0 0-1 1l2 1 2-1c1 0 1 1 2 2-1 0-1 0-1 1s0 1-1 1v1l1 1-1 1-1-1-1-1c-1-1-2-2-3-2l-1 1h-1l-2-1c1-1 1-2 2-3l1 1c0-2 1-2 2-3z" class="Q"></path><path d="M327 294l1-1c1 0 2 1 3 2l3 3v1c-1 0-2 0-3 1v3-1l-1 1-1-2h-1-1c-1 0-1 0-2-1l1-1 1-2h-1v-1l1-2z" class="R"></path><path d="M335 360v-7c0-1 2-3 3-5 1-1 1-4 2-5h1c-1 3-3 6-5 9v5h1v-3h1c0 1 0 3 1 4v-2c0-2 0-2 1-3l3-7h1c0 1-1 2-1 3-1 1-1 2-2 4-1 1-1 2-1 3l1 1c0-1 0-2 1-3 0-1 1-1 1-2 1-1 0-1 1-1 0 1 0 2-1 3l1 1c-1 1-2 3-2 4h-1v-2h0l-2 2c0 1-1 1-2 1h-2z" class="D"></path><path d="M334 365c5-3 10-4 16-2 4 1 6 3 9 6h-1l-2-1h0v1h-1 0c-2-2-5-4-8-4-5 0-8-2-12 0l-1 1-1-1h1z" class="h"></path><path d="M322 298v-4h2c1-1 1-1 1-2h-2 0c2-2 4-3 7-4h1 2l1-1 1 2h1 0c1 0 1 0 2 1l6 6h1l2 5v1l-1 1c-1-2-4-4-6-6 0-2 0-1-1-2s-1-1-2-1h-1c3 2 4 5 7 7l-1 1c-2-4-5-6-8-9h-1l3 4h0-1l-1 1-3-3c-1-1-2-2-3-2l-1 1-1-1c-1 1-3 5-4 5zm43 41h1l1 3c0 1 0 1 1 2 0 1-1 1 0 2 1 2 1 3 2 4l-1 1c0 1 1 2 2 2 0 1 1 2 1 4l-2 2v1h-1l-1 2-1 1-1 1h0l1-2-1-1h0-2c-1 1-1 2-2 3h-1 0c-1 1-1 0-1 1s0 1-1 2c-2-2-2-4-3-6l-1 1h-1l-1-1c2-2 2-3 2-5l3-3-1-2h-2 3 0c1 1 1 2 2 3v-1-2h-1l-1-3v-2-3h2s0-1 1-1h1v-1c-1 0-2 0-2 1h-1-1l1-2c2 0 3-1 4-1h2 0z" class="D"></path><defs><linearGradient id="Aq" x1="334.221" y1="422.934" x2="396.721" y2="409.28" xlink:href="#B"><stop offset="0" stop-color="#29292a"></stop><stop offset="1" stop-color="#444647"></stop></linearGradient></defs><path fill="url(#Aq)" d="M333 365l1 1 1-1c4-2 7 0 12 0 3 0 6 2 8 4h0 1v-1h0l2 1h1v-1c1 1 1 1 2 1l2-1 1 1v1 3h2v-1h1v5 1c1 0 1 0 2-1h2l1-1c-1 0-2 0-2-1v-2h-2l1-1h0 1l1-1v1h0l1 1h3l-1-1h3 1l1-1v2c2 3 3 6 4 9 1 1 1 2 1 3h0c1 1 1 1 1 2v3c-1 0-2 1-3 1l1 1h2c1 0 1-1 2-1h1v-1h-1v-1h1l-1-2 1-1v1h1l2 3 2 6 2 6 3 7v1l1 1v1h1c0 1 1 1 2 2v1l2 2h0c3 2 5 5 8 7v1c0 1-1 2-1 3h-1l-2-3h-3l-1 1v-1l-1 1c-1 2-1 4-3 5h-1l-6 3-7 2-3 1-7 2h-2-3c-1 1-2 1-4 1-5 1-12 0-18 0-1 1 0 1-1 0h-3-2l-3 2c-4-1-7-2-11-2l-1-1h-2l-1 1c0 2 1 4 2 6h0v4c1 1 1 2 2 4h-1 0l-1 1h-1c0-1 0-1-1-2v-1h-1l-2-6c0-2-1-3-1-5 0-1 0-2-1-3l-5 13-1 1-1-1c-1 1-1 0-2 1l-1 2h0c-1-1-1-2-1-3 2-9 5-17 10-26l6-9v4l1-1h1c4-4 5-9 7-14 1-2 1-3 1-6 0 0-1-3-2-3h3c-1-1-2-3-4-4s-3-2-5-4v1l-1-1c-1-3-2-7 0-10 0-4 2-7 4-11 1-1 2-1 3-2z"></path><path d="M377 413h1v4c-1 2-1 3-3 5v-1l-1 1h-1l4-8v-1z" class="K"></path><path d="M390 417c2 1 3 2 3 4s-1 3-3 4c0 0-1 0-2 1h-2v1l-1 1-1-1c1-1 1-1 1-3l1 1h1c1 0 2 0 3-1 1 0 2-2 2-3h-2l-1-1v-1h2l-1-2z" class="C"></path><path d="M377 412c-4 5-5 10-8 14h-1l5-11 3-5h0l-4 6h-1c1-1 1-1 1-2h0c0-1 0-2 1-3h0v-1l2-1c1-2-1 0 0-1 3-2 4-5 7-7l1 1-1 2c-2 0-3 2-4 3v1l1-1v2c0 1-1 2-2 3z" class="J"></path><path d="M339 413h2c1 0 4 0 5 1-2 0-5-1-6 0l1 1h1l1 1v1-1h-1l-1 1 1 1h-1l-1-2c-1 3-2 5-2 8l-3 3h1l4 2h-1c-3 0-7-3-10-2l1 1c-1 0-3 1-4 0 4-5 8-11 13-15z" class="j"></path><path d="M387 391c0 2 0 2 1 3l1 1c0 2 0 3 1 5l-1 1c-1 2-1 2-1 4l-1 1c-1 1-1 2-3 3v1c-1-1-1 0-2-1l1-1v-2h0c-3 2-4 4-5 7h-1v-1c1-1 2-2 2-3v-2l-1 1v-1c1-1 2-3 4-3l1-2c1-1 0-2 0-4v-1c1-1 1-1 1-2s1-2 2-2l-1-1c1 0 1-1 2-1z" class="O"></path><path d="M388 394l1 1c0 2 0 3 1 5l-1 1-1 1h-1 0l1-1-1-2c1-1 1-1 1-2l-1-1c1-1 1-1 1-2z" class="L"></path><path d="M357 395c2-1 3-4 5-6h0v-1l1-1h0c1-1 1-2 1-3 0 2 0 3-1 5s-1 4-1 6l-1 3-1 1c-2 2-3 3-4 7 0 2-2 5-2 8 0 1-1 2-1 3h0v8h-1 0c-1 0-1 1-1 1h-1v-1c-1 1-1 0-1 1 0-1-1-2-1-3l1-2h-1v-1c1-1 0-2 0-3s0-1-1-1v-1c1 0 1 0 2-1 0-1 1-1 2-2v-1l-2-1v-1l-2-2c-2-1-2-2-4-1l-1-1c3-1 4 0 6 1 1 1 2 1 3 3h1l-1-3c2-2 3-5 4-7l2-4z" class="C"></path><defs><linearGradient id="Ar" x1="394.81" y1="401.577" x2="381.151" y2="405.171" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#3d3f40"></stop></linearGradient></defs><path fill="url(#Ar)" d="M387 391h1v-1h-1v-1h1l-1-2 1-1v1h1l2 3 2 6 2 6 3 7v1l1 1v1h1c0 1 1 1 2 2v1l2 2h0c3 2 5 5 8 7v1c0 1-1 2-1 3h-1l-2-3h-3l-1 1v-1h0c0-1-1-1-1-2s0-1-1-1v1c-1 2-7 4-10 5h-3-1l2-1h2l1-1h2c1-1 1-1 2-1v-1h-2c-1-2-1-2-1-3h1v1l3 1 1 1c1-1 1 0 1-1s-1-2-1-2h-2l-1-3c-2-2-5-2-7-3v1l1 1 1 2h-2v1l1 1h2c0 1-1 3-2 3-1 1-2 1-3 1 1-2 1-1 2-1s2-2 2-2c-1 0-2 0-3-1s-1-1-1-2c-1 1 0 1-1 1 0-1-1-1-1-2l-1 1-1-1c1-1 1-1 1-2h-1v-3-1l1-1v-1-1c2-1 2-2 3-3l1-1c0-2 0-2 1-4l1-1c-1-2-1-3-1-5l-1-1c-1-1-1-1-1-3z"></path><path d="M389 420c-1 0 0 0-1-1l1-1v-1l-2-1h1l1-1v1l1 1 1 2h-2v1z" class="L"></path><path d="M389 395h1c1 2 1 3 0 5h0c-1-2-1-3-1-5zm6 17l-2-2c0-1 0-1 1-2 0-1 0-1 1-1l1 3h2 0l1 1-2 1h-2z" class="S"></path><path d="M399 411v1h1c0 1 1 1 2 2v1c-4-1-8-2-11 0v-3h3 1 2l2-1z" class="E"></path><path d="M391 415c3-2 7-1 11 0l2 2h0c3 2 5 5 8 7v1c0 1-1 2-1 3h-1l-2-3h-3l-1 1v-1h0c0-1-1-1-1-2s0-1-1-1v1c-1-1-2-3-3-4-2-2-5-3-8-4z" class="N"></path><path d="M405 425l-1-1c0-1 0-2-1-4 0-1-2-2-2-3v-1c1 1 2 1 2 2 1 1 2 3 3 3 1-1-1-3-2-4 3 2 5 5 8 7v1c0 1-1 2-1 3h-1l-2-3h-3z" class="M"></path><path d="M333 396h3l2 2c2 0 4 1 6 2l1 2c2 0 4 2 5 3l1 1 1 3h-1c-1-2-2-2-3-3-2-1-3-2-6-1l-2 2c2 2 4 3 5 5h0l-4 1h-2c-5 4-9 10-13 15-1 3-5 7-6 10l-5 13-1 1-1-1c-1 1-1 0-2 1l-1 2h0c-1-1-1-2-1-3 2-9 5-17 10-26l6-9v4l1-1h1c4-4 5-9 7-14 1-2 1-3 1-6 0 0-1-3-2-3z" class="d"></path><path d="M335 399c0 2 1 3 1 4h0 0c1-1 1-1 1-2l1 1v1l-1 1h1l-1 1c-2 3-3 7-5 10-7 8-14 18-17 29-1 1-2 5-3 5a84.69 84.69 0 0 1 15-30c4-4 5-9 7-14 1-2 1-3 1-6z" class="Q"></path><defs><linearGradient id="As" x1="321.183" y1="433.83" x2="313.317" y2="432.67" xlink:href="#B"><stop offset="0" stop-color="#b3afb4"></stop><stop offset="1" stop-color="#d2d3d3"></stop></linearGradient></defs><path fill="url(#As)" d="M325 416v4l1-1h1a84.69 84.69 0 0 0-15 30l-1 3-1 2h0c-1-1-1-2-1-3 2-9 5-17 10-26l6-9z"></path><path d="M333 396h3l2 2c2 0 4 1 6 2l1 2c2 0 4 2 5 3l1 1 1 3h-1c-1-2-2-2-3-3-2-1-3-2-6-1l-2 2c2 2 4 3 5 5h0l-4 1h-2c-2 0-2 1-4 2v-1c0-1 1-2 2-3h-1v-1c-1 2-1 4-4 5 2-3 3-7 5-10l1-1h-1l1-1v-1l-1-1c0 1 0 1-1 2h0 0c0-1-1-2-1-4 0 0-1-3-2-3z" class="f"></path><path d="M333 396h3l2 2c2 0 4 1 6 2l1 2v1l-1-1-2-1-1 1c-1 1-2 1-3 1v-1l-1-1c0 1 0 1-1 2h0 0c0-1-1-2-1-4 0 0-1-3-2-3z" class="J"></path><path d="M338 398c2 0 4 1 6 2l1 2v1l-1-1-2-1-1 1-2-1h-1v-3h0z" class="I"></path><path d="M364 373h2v-1h1v5 1c1 0 1 0 2-1h2l1-1c-1 0-2 0-2-1v-2h-2l1-1h0 1l1-1v1h0l1 1h3l-1-1h3 1l1-1v2c2 3 3 6 4 9 1 1 1 2 1 3h0c1 1 1 1 1 2v3c-1 0-2 1-3 1l1 1h2l1 1c-1 0-2 1-2 2s0 1-1 2v1l-1-1 1-1-1-1v1c-2 3-5 5-7 8v-1h-2-1c-1 1-2 1-3 2h1c1 2 0 3-1 4h-1c-1 0-1 1-2 1h0c-1 1-3 1-4 1s-1 0-2-1h0-1-2c0-2 1-2 2-4h0c0-2 0-3 1-5 0-1 1-1 1-2v-1l1-3c0-2 0-4 1-6s1-3 1-5l1-1v-6-2c-1-1-1-1-1-2z" class="D"></path><path d="M372 385v-1c1-1 1-2 2-3l-1-1 1-1c0 1 1 1 1 2v1l1 1 1 2c0 1-1 1-1 2-2-1-2-1-2-3l-2 1z" class="R"></path><path d="M380 381h0c0 1 1 1 1 2s0 1-1 3v3 1l-1-2-1 1h-3c-1-1-2-2-3-4l2-1c0 2 0 2 2 3 0-1 1-1 1-2l-1-2h3 0l-1-1 2-1z" class="G"></path><path d="M377 372h1l1-1v2c2 3 3 6 4 9 1 1 1 2 1 3h0c1 1 1 1 1 2-1 0-2 1-2 1-1 0-2 1-3 1v-3c1-2 1-2 1-3s-1-1-1-2h0c0-1-1-2-2-3 0 0-1 0-1-1h1v-1l-1-1h-1-1v-1h1l-1-1-1-1h3z" class="B"></path><path d="M365 383v7c0 3-1 6-2 8l1 2h0c-1 2-2 3-2 5l-2 5h0-1-2c0-2 1-2 2-4h0c0-2 0-3 1-5 0-1 1-1 1-2v-1l1-3c0-2 0-4 1-6s1-3 1-5l1-1z" class="O"></path><defs><linearGradient id="At" x1="360.109" y1="424.888" x2="361.652" y2="438.397" xlink:href="#B"><stop offset="0" stop-color="#060707"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#At)" d="M402 423v-1c1 0 1 0 1 1s1 1 1 2h0l-1 1c-1 2-1 4-3 5h-1l-6 3-7 2-3 1-7 2h-2-3c-1 1-2 1-4 1-5 1-12 0-18 0-1 1 0 1-1 0h-3-2l-3 2c-4-1-7-2-11-2l-1-1h-2l-1 1c0 2 1 4 2 6h0v4c1 1 1 2 2 4h-1 0l-1 1h-1c0-1 0-1-1-2v-1h-1l-2-6c0-2-1-3-1-5 0-1 0-2-1-3 1-3 5-7 6-10 1 1 3 0 4 0l-1-1c3-1 7 2 10 2h1c2 1 5 2 7 2 14 3 29 2 42-3h3c3-1 9-3 10-5z"></path><path d="M324 449v-2c-1-1-1-1-1-2v-3-1l2 2c0 1 0 2 1 3v2h-1c-1 1 0 1-1 1z" class="C"></path><path d="M326 446h1 0v4c1 1 1 2 2 4h-1 0l-1 1h-1c0-1 0-1-1-2v-1c0-1-1-2-1-3 1 0 0 0 1-1h1v-2z" class="D"></path><path d="M325 440l-1-1c0-2 0-4 1-6 1-1 1-1 3-1 1-1 2 0 3 1h-2c0 1 1 1 2 2h1l4 2 3 1h2l2 1h3c1 0 2 1 3 1-1 1 0 1-1 0h-3-2l-3 2c-4-1-7-2-11-2l-1-1h-2l-1 1z"></path><defs><linearGradient id="Au" x1="367.393" y1="419.987" x2="364.579" y2="436.634" xlink:href="#B"><stop offset="0" stop-color="#9b9c9b"></stop><stop offset="1" stop-color="#c7c6c9"></stop></linearGradient></defs><path fill="url(#Au)" d="M402 423v-1c1 0 1 0 1 1s1 1 1 2h0l-1 1c-20 9-44 14-65 6-3-1-6-3-8-4l-1-1c3-1 7 2 10 2h1c2 1 5 2 7 2 14 3 29 2 42-3h3c3-1 9-3 10-5z"></path><defs><linearGradient id="Av" x1="345.356" y1="399.472" x2="345.628" y2="369.348" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#3c3e3f"></stop></linearGradient></defs><path fill="url(#Av)" d="M333 365l1 1 1-1c4-2 7 0 12 0 3 0 6 2 8 4h0 1v-1h0l2 1h1v-1c1 1 1 1 2 1l2-1 1 1v1 3c0 1 0 1 1 2v2 6l-1 1c0 1 0 2-1 3h0l-1 1v1h0c-2 2-3 5-5 6l-2 4c-1 2-2 5-4 7l-1-1c-1-1-3-3-5-3l-1-2c-2-1-4-2-6-2l-2-2c-1-1-2-3-4-4s-3-2-5-4v1l-1-1c-1-3-2-7 0-10 0-4 2-7 4-11 1-1 2-1 3-2z"></path><path d="M344 400c2 0 6 1 7 0 2 0 5-3 6-5l-2 4c-1 2-2 5-4 7l-1-1c-1-1-3-3-5-3l-1-2z" class="H"></path><path d="M355 369h1v-1h0l2 1h1v-1c3 5 4 10 3 15l-1 2v-1-7c-2-1-3-3-4-5h0c-1 0-2-2-2-2v-1z" class="M"></path><path d="M357 372h0c1 2 2 4 4 5v7 1c0 1-1 2-1 4v-5-1l-1 1h-1l-2 4-1-2v-1l1-1c1-1 0-2 1-3 2-2 0-7 0-9z" class="V"></path><path d="M333 372l2-1 1 1c1 6-3 11 2 16 3 1 7 2 10 0l1-1h2v2h0l2-1v1l1 1c0-1 1-1 2-2l2-4h1l1-1v1 5c-2 3-4 4-7 6h0c-3 3-7 2-10 2-5-2-7-4-9-9-1-1-2-2-3-2h0c-1-1-1-1-1-2s0-1-1-2c0-2 1-4 2-6 0-2 1-3 2-4z" class="K"></path><path d="M338 390c2 0 10-1 11 0l-1 1h-1v1c1 0 2 0 3-1l1 1h0l6-3 1-1v1c0 1-1 2-2 2-1 2-2 3-3 4h-2c-3 1-5 1-9 1-1-1-3-2-4-3l1-1c0-1 0-1-1-2z" class="G"></path><path d="M339 392h0l2 2c2 1 6 0 8 0 1-1 1-1 2-1 2 0 3-1 5-2-1 2-2 3-3 4h-2c-3 1-5 1-9 1-1-1-3-2-4-3l1-1z" class="H"></path><path d="M333 372l1 1-1 1c2 3 0 7 1 10 1 2 3 5 4 6s1 1 1 2l-1 1c1 1 3 2 4 3 4 0 6 0 9-1h2 0 0c-3 3-7 2-10 2-5-2-7-4-9-9-1-1-2-2-3-2h0c-1-1-1-1-1-2s0-1-1-2c0-2 1-4 2-6 0-2 1-3 2-4z" class="L"></path><path d="M331 376h1c0 4 0 8 2 12-1-1-2-2-3-2h0c-1-1-1-1-1-2s0-1-1-2c0-2 1-4 2-6z" class="I"></path><path d="M333 365l1 1 1-1c4-2 7 0 12 0 3 0 6 2 8 4h0v1s1 2 2 2c0 2 2 7 0 9-1 1 0 2-1 3l-1 1v1l1 2c-1 1-2 1-2 2l-1-1v-1l-2 1h0v-2h-2l-1 1c-3 2-7 1-10 0-5-5-1-10-2-16l-1-1-2 1c-1 1-2 2-2 4-1 2-2 4-2 6-2 0-2-1-2-2-1 2 0 6 0 8v1l-1-1c-1-3-2-7 0-10 0-4 2-7 4-11 1-1 2-1 3-2z" class="N"></path><path d="M347 365c3 0 6 2 8 4h0v1s1 2 2 2c0 2 2 7 0 9 0-4-1-5-2-8s-7-5-9-6l1-1v-1z" class="P"></path><path d="M326 378h0l1-2c1-2 1-5 4-6l2-1c1-1 2-1 4-2 1 0 2-1 3-1s3 0 3 1l-5 1c-4 2-6 3-8 6v1l1 1c-1 2-2 4-2 6-2 0-2-1-2-2-1 2 0 6 0 8v1l-1-1c-1-3-2-7 0-10z" class="D"></path><path d="M331 376l-1-1v-1c2-3 4-4 8-6 0 1 0 1 1 2l-2 2c0 1 0 1 1 2 0 0-1 0-1 1-1 2 0 4 0 6-1 1 0 2 0 3l1 2h8l1-1h0l1-1c1 0 3-1 3-3s0-2 2-4c0 1 0 1 1 1h1v4c1-1 1-4 1-5-1-2-1-2-1-4 1 3 2 4 2 8-1 1 0 2-1 3l-1 1v1l1 2c-1 1-2 1-2 2l-1-1v-1l-2 1h0v-2h-2l-1 1c-3 2-7 1-10 0-5-5-1-10-2-16l-1-1-2 1c-1 1-2 2-2 4z" class="a"></path><path d="M347 385l1-1c1 0 3-1 3-3s0-2 2-4c0 1 0 1 1 1h1v4c0 2-1 4-2 5-1-1-1 0 0-1 0-1 1-2 0-3h-1c-2 1-3 2-4 3h-1c-2 2-3 2-5 2l-1-1c-1 0-2-1-3-1h8l1-1h0z" class="i"></path><path d="M347 385h-2c-1 0-4 0-5-1-1 0-1-1-2-2 0-3 0-5 2-7h0l-1-1 1-1-1-2h1c4-1 6-3 10-1l-1 1c1 3 2 8 1 11-1 0-2 1-2 2l-1 1h0z" class="f"></path><path d="M344 372c1 0 1 0 1 1 1 2 3 3 2 5 0 2-1 2-2 3h-2c-1 0-1 0-2-1 0-3 2-6 3-8z" class="X"></path><path d="M490 681c5 0 11 0 16 1h17 9c0 1 1 2 1 3l1 1h1c2 3 5 2 8 3v1h3l1 1-1 2h1c1 1 0 1 1 1h2 0c2 1 4 2 5 3 0 1-1 3-2 3h-1l-1 1v2 1c1 1 2 1 2 3 2 1 3 2 4 3v1c0 1-1 0 0 1v1h5c-1 0-3 1-4 1h0c-2-1-2-1-4-1 1 2 3 3 5 4 2 0 5-1 6 1v1l-2 1c-2 0-1-1-3 1 1 1 1 2 1 3s1 1 1 2c1 1 2 2 3 4l1 1-1 2c5 8 9 18 11 28 1 5 1 10 2 14v14l-2-1c-1 4-1 8-1 12v1l-1 1c-1 0-2 1-2 1h-2l-2 7c-2 0-4 0-6 1h-1c-2 1-4 2-6 5s-3 7-5 11v-1c-1-1-1-1 0-2v-3l1-1v-3l2-3c0-1 1-2 1-3v-1c0-2 0-4-1-5l-1-2c-3-4-9-7-13-10-1-1-2-3-3-4-1 0-2 1-3 1-2 1-4 1-6 1-4 0-7 0-10-2 0-2-1-2-1-3v-1l1 1h0l2 1h1c1 0 1 0 2-1l-1-5c-1-1-1-1-1-2l-1-1-2-6c-1 0-1-1-2-2h-2 0c-2-1-5-1-8-1l-1-1 1-1-7 1c0 1 1 2 1 3-13 3-27 9-36 20-1 2-3 5-5 7h0l-1 3h-1l1 3c0 2 1 3 2 5l-1 2c-1 1-1 1-2 0l-1 2h0-1l-2-2v3h-1c-1-2-2-4-5-5h-1c-1-1-2-1-3-1l-1 1c-7 1-13 5-20 5l-6 1c-8 0-17-1-24-5-10-6-19-17-22-28-4-11-4-29 2-40h1 1l6-11c2-4 4-6 7-9 2-1 3-3 4-5 4-3 10-6 14-8v-1c2-2 4-2 6-3 3-1 6-2 9-2l10-1h3c2 0 6 1 8 1 1-1 4-3 5-4l6-5 3-2 5-4h0l1-2 2-1 2 1 2-1c3-1 7-2 11-2l13-2z" class="W"></path><path d="M491 731h-2-3 0c1-2 3-2 5-4h0v4z" class="I"></path><path d="M491 727c1-1 2-2 4-2v1h-1l1 1v2l-2 1c1 0 1 0 2 1-2-1-3 0-4 0v-4z" class="B"></path><path d="M489 738l1 1h0c2 1 3 2 5 3 2 0 4 1 6 1-2 1-4 1-6 1l1-1s-2-1-3-1-1 2-2 2l-1-1c-1-1-1-1-2-1 0-2 0-2 1-4z" class="C"></path><path d="M489 738l1 1v4c-1-1-1-1-2-1 0-2 0-2 1-4zm-8 14l-1-1c1 0 1-1 1-2l4-4h0c1 1 1 2 1 3h2c-2 1-2 1-2 3 1 0 1-1 2-1l-5 4c-1 0-1 0-1-1l-1-1z" class="I"></path><path d="M543 755v4h-3c-1 0-3 0-4 1l-1-2h-3c-3 1-7 1-10 0 7 0 14-1 21-3z" class="L"></path><path d="M469 748l1 1c0 1 0 1-1 2l-3 2v1c-3 2-4 4-5 7 0 2-2 4-2 6-1-1-1-2-1-3v-1c1-2 2-6 4-8 0-1 0-1 1-2 0-1 1-4 2-4 2-1 3 0 4-1z" class="I"></path><path d="M485 742c-1-1 0-1-1-1v-1h-1c-1 1-1 1-2 1l-1-1h-3 0-1v-1h1l-2-1 1-1h0c1-1 2-1 3-2h-2l2-2 1 1c1 1 1 2 1 3h0 1c1-1 2-2 3-2 2-2 5-2 7-1-2 1-6 1-7 3h2c0 1-1 1-1 2-1 1-1 1-1 3z" class="S"></path><path d="M456 782c1 1 1 2 1 2v1h1l1-2 1 2 1-1-1-1h0c1-1 1-1 1-2l4-2h0c1 0 2 0 2-1 1 0 1-1 2-1 1-1 4-3 5-3l-2 2-19 17c0-2-1-2-2-4h2c1-2 2-2 3-5-1 0 0-1 0-2z" class="F"></path><path d="M510 735c2-1 5 0 6 0 5-1 10 1 15 0h0l1 1c-3 1-5 1-7 1-1-1-2-1-3-1h-1c-2-1-4-1-6 0l-1 1c-1 1-2 1-3 1-2 0-4 1-6 2-1-1-1-2-1-3v-1l2-1h4z" class="D"></path><path d="M506 735h4-1c-1 1-1 1-2 3l-3-1v-1l2-1z" class="K"></path><path d="M492 734h9c1 0 3 0 5 1l-2 1v1c0 1 0 2 1 3h-1-4v1c1 1 1 0 0 1h-5c-2-1-3-2-5-3h0l-1-1c-1 2-1 2-1 4h-3c0-2 0-2 1-3 0-1 1-1 1-2h-2c1-2 5-2 7-3z" class="J"></path><path d="M501 734c1 0 3 0 5 1l-2 1c-1 1-1 1-2 1s-2-1-3-1c1 0 1-1 2-2z" class="O"></path><path d="M490 739l1-1 2 1s0-1 1 0h2c1 0 7 1 8 1h-4v1c1 1 1 0 0 1h-5c-2-1-3-2-5-3z" class="Z"></path><path d="M492 734h9c-1 1-1 2-2 2-1 1-2 1-2 2-2-1-1-2-3-2l-1 1c-2 1-2 1-4 0v1h0c-1 2-1 2-1 4h-3c0-2 0-2 1-3 0-1 1-1 1-2h-2c1-2 5-2 7-3z" class="E"></path><path d="M452 782c4-4 3-11 4-16 0-3 1-6 2-9 5-16 14-33 29-41l-1 1c0 1-1 1-2 2 0 1 0 2-1 2-1 3-3 4-5 6s-5 4-5 6l-3 3c-1 2-3 5-4 7l1 1 1-1h1c-1 2-3 3-3 5h3c-1 1-2 0-4 1-1 0-2 3-2 4-1 1-1 1-1 2-2 2-3 6-4 8v1c0 1 0 2 1 3-1 4-1 7-2 11 0 1 0 2-1 3l-4 3v-2z" class="B"></path><path d="M515 736c2-1 4-1 6 0h1c1 0 2 0 3 1 2 0 4 0 7-1v1 1h6c-1 1-4 2-5 4h1 3c-2 1-2 1-4 1-2 1-3 1-5 1-4-1-9-1-12-1h-15c-2 0-4-1-6-1h5c1-1 1 0 0-1v-1h4 1c2-1 4-2 6-2 1 0 2 0 3-1l1-1z" class="T"></path><path d="M514 737l2 1v1h5l1 1c-1 0-3 0-4 1h-1c-2-1-4-1-6 0l-1 1h-1 0c-1-1-2-1-3-1-2 1-3 1-6 1 1-1 1 0 0-1v-1h4 1c2-1 4-2 6-2 1 0 2 0 3-1z" class="N"></path><path d="M515 736c2-1 4-1 6 0h1c1 0 2 0 3 1 2 0 4 0 7-1v1 1h6c-1 1-4 2-5 4h1 3c-2 1-2 1-4 1h0-3 0l-1-1v-2h-1c-1 1-2 1-3 1-1-1-2-1-3-1l-1-1h-5v-1l-2-1 1-1z" class="Q"></path><path d="M515 736c2-1 4-1 6 0h1c1 0 2 0 3 1l-2 1h-2-2l-1-1c-1 0-2 0-3-1z" class="K"></path><path d="M520 715v-1l-1-1h3 5c1 0 2 1 3 2 1 0 1 0 1 1 1 1 2 2 2 3l2-1v1l-4 4h2v-1c1 0 2-1 3-1v1 2c1 0 2-1 3-1h0l1 1v1h1l2 1v1 1h-1c-1 1-1 1-2 1h-2c-1 1-1 0-3 0h-2c0-1 1-1 1-2h-2v-1c-2 0-4 1-6 1-1-1-2-1-4-1v-1h-1c-3-2-4-3-6-6 1 0 1 1 2 1v1h2l-2-2 1-1c1 0 2 1 2 2 1 1 1 0 2 1v-1c1 1 2 1 3 1-1-1-2-2-3-2h0c-1-1-3-4-3-5l1 1z" class="E"></path><path d="M539 723l1 1v1h1l2 1v1 1h-1-3l-1-1c-1 0-1 0-1-1 1 0 1-1 2-2v-1z" class="H"></path><path d="M520 715v-1l-1-1h3 5c1 0 2 1 3 2 1 0 1 0 1 1 1 1 2 2 2 3l-2 3c-2 1-3 2-5 2l1-1 1-1c0-1-1-2-1-3l-2 1c-2-2-4-3-5-5z" class="S"></path><path d="M509 720l3-3c1 0 1 1 2 1l1 1c2 3 3 4 6 6h1v1c2 0 3 0 4 1 2 0 4-1 6-1v1h2c0 1-1 1-1 2h2c2 0 2 1 3 0h2l-1 1h0c-2 1-3 1-4 1h-2v-1h-1c-4 0-8 1-12 1-8 1-16 0-24-1l-1 1c-1-1-1-1-2-1l2-1v-2h0c1-1 3-2 4-3 2 0 2 0 4-1 0-1 1 0 2 0 1-1 2-1 4-3z" class="k"></path><path d="M513 727c3-2 6-1 9-1 2 0 3 0 4 1-5 1-8 1-13 0z" class="i"></path><path d="M500 728c4-1 6-1 10-1l-2 1c1 1 3 1 5 1h-14c-1 0-2 1-3 1l-1 1c-1-1-1-1-2-1l2-1v-2h0 5v1z" class="Q"></path><path d="M495 727h5v1c-1 1-4 1-5 1v-2h0z" class="D"></path><path d="M509 720l3-3c1 0 1 1 2 1l1 1c2 3 3 4 6 6h1v1c-3 0-6-1-9 1h0-3c-4 0-6 0-10 1v-1h-5c1-1 3-2 4-3 2 0 2 0 4-1 0-1 1 0 2 0 1-1 2-1 4-3z" class="G"></path><path d="M513 727h0c-1-2-2-3-4-4h0l1-1 3 1c1 1 3 1 4 1 2 1 3 1 5 1v1c-3 0-6-1-9 1h0z" class="Q"></path><path d="M495 727c1-1 3-2 4-3 2 0 2 0 4-1 0-1 1 0 2 0v1h3c-1 1 0 1-1 1-3 0-5 1-7 2h-5z" class="C"></path><path d="M509 720l3-3c1 0 1 1 2 1l1 1c2 3 3 4 6 6h1c-2 0-3 0-5-1-1 0-3 0-4-1 0 0-2-2-2-3-1 0-1 1-2 0z" class="G"></path><path d="M509 720l3-3c1 0 1 1 2 1h0c-1 1-1 2-2 2 0 1 1 1 2 2h0c1 1 2 1 3 2-1 0-3 0-4-1 0 0-2-2-2-3-1 0-1 1-2 0z" class="U"></path><path d="M539 723c0-2 0-3-1-5l1-1 1 1 2 2h2c1 2 5 3 7 4l4 4 1 1c2 3 4 6 6 10-2 0-2-1-3 0h-3c1 1 1 1 1 2h-2v1 1 1c-2-1-3-2-4-3l-1 2h-1 0c-1-2-2-2-3-3-2 0-2 1-3 1h-2-1-2l-1 1h-3-1c1-2 4-3 5-4h-6v-1-1l-1-1h4c1-1 2-1 4-1v-3-1h0l1-1c1 0 1 0 2-1h1v-1-1l-2-1h-1v-1l-1-1h0z" class="E"></path><path d="M544 734s1 1 2 1 1 0 2 1c-1 1-2 1-3 2 0 1 0 1-1 1-1-2-1-3-3-3 1-1 2-2 3-2z" class="F"></path><path d="M548 731h4l2 2-5 2c-1 0 0 0-1 1-1-1-1-1-2-1s-2-1-2-1l4-2v-1z" class="H"></path><path d="M555 733c1-1 1-2 1-4 2 3 4 6 6 10-2 0-2-1-3 0h-3-2c-2 0-3-1-3-3h1c1-1 3 0 4-1 0-1 0-1-1-2z" class="j"></path><path d="M542 728h1c0 1-1 1 0 3l2 1 2-1h1v1l-4 2c-1 0-2 1-3 2h0c-1 1-1 1-1 2h-2-6v-1-1l-1-1h4c1-1 2-1 4-1v-3-1h0l1-1c1 0 1 0 2-1z" class="B"></path><path d="M542 728h1c0 1-1 1 0 3l2 1c-3 2-5 2-6 5h-1c0-1 0-2 1-3h0v-3-1h0l1-1c1 0 1 0 2-1z" class="I"></path><path d="M539 723c0-2 0-3-1-5l1-1 1 1 2 2h2c1 2 5 3 7 4l4 4 1 1c0 2 0 3-1 4h-1l-2-2h-4-1l-2 1-2-1c-1-2 0-2 0-3v-1-1l-2-1h-1v-1l-1-1h0z"></path><path d="M551 724l4 4h-6v-1l2-3zm-8 2h4l1 1-1 2 1 1-1 1-2 1-2-1c-1-2 0-2 0-3v-1-1z" class="b"></path><path d="M490 706c5-1 9-3 14-2v1h0 3l1 1h3 9v1 1c-7 0-13 0-19 2h-1c-5 1-10 4-13 6-15 8-24 25-29 41-1 3-2 6-2 9-1 5 0 12-4 16-1-8 1-17 2-24 4-12 8-21 16-30 2-4 5-6 9-9l2-2c-3 0-3 0-5 1h-3c0-1 0-2 1-3 1 0 3-1 4-1l-1-1 5-3 8-4z" class="Y"></path><defs><linearGradient id="Aw" x1="477.957" y1="720.185" x2="484.439" y2="707.01" xlink:href="#B"><stop offset="0" stop-color="#151717"></stop><stop offset="1" stop-color="#343232"></stop></linearGradient></defs><path fill="url(#Aw)" d="M490 706c5-1 9-3 14-2v1h0 3l1 1c-7 1-14 4-20 7-3 1-5 3-7 4-3 0-3 0-5 1h-3c0-1 0-2 1-3 1 0 3-1 4-1l-1-1 5-3 8-4z"></path><path d="M490 706c5-1 9-3 14-2v1h0c-1 1-4 2-6 2l-1-1c-1 0-4 1-5 2-5 1-9 4-14 6l-1-1 5-3 8-4z" class="V"></path><path d="M482 770l1-1c16-8 34-9 52-4 4 2 9 4 13 7 2 2 4 4 7 5 3 2 6 4 9 7-2 0-4-2-5-3l-8-4-2-1-11-5c-5-3-13-2-18-1h-7c-2-1-5-1-8-1l-1-1 1-1-7 1c0 1 1 2 1 3-13 3-27 9-36 20-1 2-3 5-5 7h0l-3-1 1-1h0c-3 1-4 2-7 1h-2c-1 1-2 1-3 1h-1c1-1 0-1 1-1l2-1h2l-1-3 1-2c1 0 2-2 3-2 1 2 2 2 2 4l19-17 2-2 8-4z" class="Q"></path><path d="M505 767c8-1 16-1 24 0l2 1h1c5 1 13 4 17 7v1l-11-5c-5-3-13-2-18-1h-7c-2-1-5-1-8-1l-1-1 1-1z" class="X"></path><path d="M498 768c0 1 1 2 1 3-13 3-27 9-36 20-1 2-3 5-5 7h0l-3-1 1-1c2-1 7-7 8-8 2-3 5-5 9-7 6-4 12-7 19-10-1 0-2 1-4 1-3 1-7 3-10 5-3 1-5 3-7 4v-1c8-5 17-10 27-12z" class="T"></path><path d="M474 774l8-4-2 2c1-1 2-1 3-1v1c-5 3-11 5-15 9-3 2-5 5-7 7-3 3-4 5-8 7-1 1-2 1-4 2h-2c-1 1-2 1-3 1h-1c1-1 0-1 1-1l2-1h2l-1-3 1-2c1 0 2-2 3-2 1 2 2 2 2 4l19-17 2-2z" class="N"></path><path d="M448 791c1 0 2-2 3-2 1 2 2 2 2 4l-5 3-1-3 1-2z" class="E"></path><path d="M482 770l1-1c16-8 34-9 52-4 4 2 9 4 13 7 2 2 4 4 7 5 3 2 6 4 9 7-2 0-4-2-5-3l-8-4c-1-2-4-4-6-6-4-2-8-4-12-5-6-1-13-1-19-1h-10c-8 1-14 4-21 6-1 0-2 0-3 1l2-2z" class="Y"></path><path d="M543 741c1 0 1-1 3-1 1 1 2 1 3 3h0 0c-1 2-2 2-3 2v1h1c1 0 1 0 2 1-2 2-3 3-5 4v1c1 0 1-1 2-1h2c2 0 4-2 6-1-4 2-7 3-11 5-7 2-14 3-21 3h-26c-3 0-8 0-11-1h-1c-1-1-2-2-4-3l1-2 1 1c0 1 0 1 1 1l5-4c3-1 5-2 8-3h1c1-1 2-1 3 0 0 1-1 1-2 1 1 0 2 1 3 1 0-1 0-1 1-1h1l2-1h0 4c2 0 2-1 3-1 1-1 2 0 3 0l1-1c1 1 2 1 3 1l1-1 3 1c0 2 1 3 2 3v-1c2-2 1 0 3-1 0 0 1-1 2-1h1l1 1 1-1-1-2c-2 1-2 1-4 0 2 0 3 0 5-1 2 0 2 0 4-1l1-1h2 1 2z" class="B"></path><path d="M543 741c1 0 1-1 3-1 1 1 2 1 3 3h0 0c-2 1-3 0-5 0l-2-1 1-1z" class="W"></path><path d="M507 755l-2-1 1-1c1 0 2 0 3 1 2-2 5-3 7-5l1 3h-1c0 1-1 1-1 2h-2-3l1 1-1 1-1-1h-2z" class="V"></path><path d="M488 750l8-3-1 1c0 1-1 1-2 1l-4 3v2h1l1-1c2-2 3-3 5-4l-4 5v1l8-4 2-1h0c-1 3-4 3-6 5l-3 1v1l3 1c-3 0-8 0-11-1h-1c-1-1-2-2-4-3l1-2 1 1c0 1 0 1 1 1l5-4z" class="Q"></path><path d="M533 750l1-1v-1c1-1 2-2 4-2 1 0 2-1 2-1l1-1 1 1h4v1h1c1 0 1 0 2 1-2 2-3 3-5 4v1c1 0 1-1 2-1h2c2 0 4-2 6-1-4 2-7 3-11 5-7 2-14 3-21 3h-26l-3-1v-1l3-1c2 0 3 0 4-1l1 1c2 0 2-1 3-1 0 0 2 1 3 1h2l1 1 1-1c3 0 4 0 7-2l3 2 3-2 1-1-2-2h1c1 0 1 0 2 1 2-1 2-1 4-1l1 1 2-2v1z" class="M"></path><path d="M531 751l2-2v1c0 1-1 2-1 3 1 1 2 1 4 1h0c2-2 2-2 4-2s3-1 4-1v1c1 0 1-1 2-1h2c-6 3-13 5-20 5l-1-1c-1 0-2 1-3 0l1-1-1-1 1-1-2-2h1c1 0 1 0 2 1 2-1 2-1 4-1l1 1z" class="G"></path><path d="M526 751c2-1 2-1 4-1l1 1c0 1 0 2-1 3h-2c-1-1-1-2-2-3z" class="F"></path><path d="M533 750l1-1v-1c1-1 2-2 4-2 1 0 2-1 2-1l1-1 1 1h4v1h1c1 0 1 0 2 1-2 2-3 3-5 4-1 0-2 1-4 1s-2 0-4 2h0c-2 0-3 0-4-1 0-1 1-2 1-3z" class="W"></path><path d="M556 739h3c1-1 1 0 3 0l5 12c0 1 1 2 1 3s1 2 1 4c2 3 3 8 3 11 0 1 0 1 1 2v4c1 2 0 4 1 6 0 1 0 1-1 3h0v2c-1 2-1 4-2 6-1-1-2-2-2-3l-1-1c-1-1-2-3-4-4-3-3-6-5-9-7-3-1-5-3-7-5-4-3-9-5-13-7 0 0 1-1 2-1h1c2 1 3-1 5-2v1h0 1c0-1 0-2 1-3h0v-1h-2v-4c4-2 7-3 11-5-2-1-4 1-6 1h-2c-1 0-1 1-2 1v-1c2-1 3-2 5-4-1-1-1-1-2-1h-1v-1c1 0 2 0 3-2h0 1l1-2c1 1 2 2 4 3v-1-1-1h2c0-1 0-1-1-2z" class="b"></path><path d="M561 762h2c2 0 3 1 4 3l-1 1c-1-1-1-1-3 0-1-1-2-2-2-4z" class="I"></path><path d="M554 776l-2-2c0-1 1-2 1-2 1-2 2-4 4-4 5 3 8 6 12 10h-1s-1-1-2-1v2h0l-2-2h-1l1 2h0l-1 1c2 4 7 4 6 9l-1-1c-1-1-2-3-4-4-3-3-6-5-9-7l-1-1z" class="S"></path><path d="M554 776l-2-2c0-1 1-2 1-2 1-2 2-4 4-4 5 3 8 6 12 10h-1s-1-1-2-1v-1c-1-2-5-4-7-5l-2 2c1 1 2 2 3 4-1 0-1 0-2 1l-2-2v-3h-1l-1 3z" class="D"></path><path d="M556 739h3c1-1 1 0 3 0l5 12c0 1 1 2 1 3h-1c0-1 0-2-1-3h-5l-1 1 2 2 1-1v3c-1 1-1 1-2 1l-1 1c-2 1-3 1-4 1v1 2c-1 2-4 2-2 4l1 1h1 0l1 1h0c-2 0-3 2-4 4 0 0-1 1-1 2l2 2 1 1c-3-1-5-3-7-5-4-3-9-5-13-7 0 0 1-1 2-1h1c2 1 3-1 5-2v1h0 1c0-1 0-2 1-3h0v-1h-2v-4c4-2 7-3 11-5-2-1-4 1-6 1h-2c-1 0-1 1-2 1v-1c2-1 3-2 5-4-1-1-1-1-2-1h-1v-1c1 0 2 0 3-2h0 1l1-2c1 1 2 2 4 3v-1-1-1h2c0-1 0-1-1-2z" class="F"></path><path d="M554 759c0-2-1-2 0-4l4-2v1l1 1-1 2h-2l-1 1 1 1v1h0l-2-1z" class="S"></path><path d="M554 759l2 1c-1 2-3 4-5 5-1 1-1 0-2 0 0-1 0-1-1-2h0v-1l-2-2 1-1h7z" class="B"></path><path d="M556 739h3c1-1 1 0 3 0l5 12c0 1 1 2 1 3h-1c0-1 0-2-1-3s-1-2-1-2l-2 1-3-1-3 3-2 1-1-1c0-1 1-2 1-3 1-1 1 0 0-1h-1v2c-2-1-4 1-6 1h-2c-1 0-1 1-2 1v-1c2-1 3-2 5-4-1-1-1-1-2-1h-1v-1c1 0 2 0 3-2h0 1l1-2c1 1 2 2 4 3v-1-1-1h2c0-1 0-1-1-2z" class="H"></path><path d="M557 741h1 3v2h-2l-2-2zm-8 2h1c1 0 2 1 2 2-1 0-2 1-3 2h0c-1-1-1-1-2-1h-1v-1c1 0 2 0 3-2h0z" class="b"></path><g class="B"><path d="M549 747c2 0 4 1 5 1v2c-2-1-4 1-6 1h-2c-1 0-1 1-2 1v-1c2-1 3-2 5-4h0z"></path><path d="M544 720c1 0 2 0 4 1h0c1 0 1 0 2 1h1l-1-1c0-1-1-1-1-2 4 2 7 5 9 9h2 0 1l4 5c5 8 9 18 11 28 1 5 1 10 2 14v14l-2-1c-1 4-1 8-1 12v1l-1 1c-1 0-2 1-2 1h-2l-2 7c-2 0-4 0-6 1h-1c-2 1-4 2-6 5s-3 7-5 11v-1c-1-1-1-1 0-2v-3l1-1v-3l2-3c0-1 1-2 1-3v-1c0-2 0-4-1-5l-1-2c-3-4-9-7-13-10-1-1-2-3-3-4-1 0-2 1-3 1-2 1-4 1-6 1-4 0-7 0-10-2 0-2-1-2-1-3v-1l1 1h0l2 1h1c1 0 1 0 2-1l-1-5c-1-1-1-1-1-2l-1-1-2-6c-1 0-1-1-2-2h-2 0 7c5-1 13-2 18 1l11 5 2 1 8 4c1 1 3 3 5 3 2 1 3 3 4 4l1 1c0 1 1 2 2 3 1-2 1-4 2-6v-2h0c1-2 1-2 1-3-1-2 0-4-1-6v-4c-1-1-1-1-1-2 0-3-1-8-3-11 0-2-1-3-1-4s-1-2-1-3l-5-12c-2-4-4-7-6-10l-1-1-4-4c-2-1-6-2-7-4z"></path></g><path d="M568 797h0c0-3-3-5-4-7l3 3v-1c0-1-1-2-2-3 2 0 2 1 3 2 1 2 2 3 3 5s1 2 3 3l-4 1c-1-1-1-2-2-3z" class="S"></path><defs><linearGradient id="Ax" x1="568.014" y1="753.346" x2="572.696" y2="751.468" xlink:href="#B"><stop offset="0" stop-color="#a09fa0"></stop><stop offset="1" stop-color="#c6c6c7"></stop></linearGradient></defs><path fill="url(#Ax)" d="M561 728l4 5c5 8 9 18 11 28 1 5 1 10 2 14v14l-2-1c0-2 1-5 0-7 0-3 0-6-1-9-1-10-4-21-8-30-3-5-6-10-9-14h2 0 1z"></path><path d="M520 770c5-1 13-2 18 1 1 3 9 5 12 7 2 1 3 2 4 3 2 2 4 2 4 5 3 4 7 8 10 11 1 1 1 2 2 3l4-1 1 2-1 1c-1 0-2 1-2 1h-2l-1-1v-1l-1-1h-1-3l-2-3c-1-3-5-5-7-8l-2-2h-2l-2-1v-1c-2-3-6-3-8-5-4-1-7-2-11-4-2 0-4 0-5-1l-1-3 5-1c-3-1-6-1-9-1z"></path><path d="M520 770c5-1 13-2 18 1 1 3 9 5 12 7 2 1 3 2 4 3 2 2 4 2 4 5-3-2-5-5-8-7-7-3-14-6-21-8-3-1-6-1-9-1z" class="a"></path><path d="M513 770h7c3 0 6 0 9 1l-5 1 1 3c1 1 3 1 5 1 4 2 7 3 11 4 2 2 6 2 8 5v1c0 1 0 1 1 2h0c-1 1-1 2 0 2-1 1-1 1-2 1-2 0-3-1-4-2h-1c-2-1-3-1-5-1v-1l-1-1h0l1 1c-1 1-1 2-2 2s-2 1-3 1c-2 1-4 1-6 1-4 0-7 0-10-2 0-2-1-2-1-3v-1l1 1h0l2 1h1c1 0 1 0 2-1l-1-5c-1-1-1-1-1-2l-1-1-2-6c-1 0-1-1-2-2h-2 0z" class="I"></path><path d="M525 775c1 1 3 1 5 1-1 1-2 2-3 2 0 1 2 2 3 3l1-1v4h0c-1 1-1 2-2 2v1h0c-1-1 0-2 0-3h-1c-1-2-3-2-4-4 0-1 0-1-1-2v-1l2-2z" class="b"></path><path d="M524 780c1 2 3 2 4 4h1c0 1-1 2 0 3h0v1c-1 0-2 1-3 1v-1h-3v1c1 1 2 1 4 2-4 0-7 0-10-2 0-2-1-2-1-3v-1l1 1h0l2 1h1c1 0 1 0 2-1l-1-5h1s1 1 1 2 0 2 1 3v-5-1z" class="B"></path><path d="M513 770h7c3 0 6 0 9 1l-5 1 1 3-2 2v1c1 1 1 1 1 2v1 5c-1-1-1-2-1-3s-1-2-1-2h-1c-1-1-1-1-1-2l-1-1-2-6c-1 0-1-1-2-2h-2 0z" class="I"></path><path d="M536 789c1 0 1-1 2-2l-1-1h0l1 1v1c2 0 3 0 5 1h1c1 1 2 2 4 2 1 0 1 0 2-1-1 0-1-1 0-2h0c-1-1-1-1-1-2l2 1h2l2 2c2 3 6 5 7 8l2 3h3 1l1 1v1l1 1-2 7c-2 0-4 0-6 1h-1c-2 1-4 2-6 5s-3 7-5 11v-1c-1-1-1-1 0-2v-3l1-1v-3l2-3c0-1 1-2 1-3v-1c0-2 0-4-1-5l-1-2c-3-4-9-7-13-10-1-1-2-3-3-4z" class="F"></path><path d="M552 795h0c2 0 3-1 5-1-1 2-2 2-2 4l1 1v1h-3v-1c-1-1-1-2-2-3l1-1z" class="D"></path><path d="M557 794c2 2 2 4 2 6l-1 1h-2v-1-1l-1-1c0-2 1-2 2-4z" class="E"></path><path d="M536 789c1 0 1-1 2-2l-1-1h0l1 1v1c2 0 3 0 5 1h1c1 1 2 2 4 2 1 0 1 0 2-1l2-2c1 2 1 3 0 5l-1 1 1 1-1 1h-2-4l-2-2c-1-1-2-1-4-1-1-1-2-3-3-4z" class="b"></path><path d="M536 789c1 0 1-1 2-2l-1-1h0l1 1v1l1 2c1 1 2 2 4 3 2 2 5 1 8 1l1 1-1 1h-2-4l-2-2c-1-1-2-1-4-1-1-1-2-3-3-4z" class="O"></path><path d="M567 800h1l1 1v1l1 1-2 7c-2 0-4 0-6 1h-1c-2 1-4 2-6 5s-3 7-5 11v-1c-1-1-1-1 0-2v-3l1-1v-3l2-3c0-1 1-2 1-3v-1c0-2 0-4-1-5l1-1h1l-1-2h1v1c2 1 5 1 7 0h1c0 2 0 3-1 5v1h2l-1-1c1-1 1-1 1-2l3-6z" class="a"></path><path d="M553 805l1-1h1l-1-2h1v1h1c0 1 0 2 1 3h-1l-2 5v-1c0-2 0-4-1-5z" class="B"></path><path d="M506 682h17 9c0 1 1 2 1 3l1 1h1c2 3 5 2 8 3v1h3l1 1-1 2h1c1 1 0 1 1 1h2 0c2 1 4 2 5 3 0 1-1 3-2 3h-1l-1 1v2 1c1 1 2 1 2 3 2 1 3 2 4 3v1c0 1-1 0 0 1v1h5c-1 0-3 1-4 1h0c-2-1-2-1-4-1 1 2 3 3 5 4 2 0 5-1 6 1v1l-2 1c-2 0-1-1-3 1 1 1 1 2 1 3s1 1 1 2c1 1 2 2 3 4l1 1-1 2-4-5h-1 0-2c-2-4-5-7-9-9h0c-9-7-17-10-29-11v-1-1h-9-3l-1-1h-3 0v-1c-5-1-9 1-14 2l-8 4v-1c1-1 1-1 2-1v-1c-2 0-3-1-5 0-4 1-8 2-13 1 2 0 3 0 4-1h3c1-1 2-1 4-1 1-1 2-2 3-2l2-2c1-2 2-2 4-3h1v-2l2-2c1-2 2-3 3-5h0c1-1 1-1 2-1 0-1 0 0 1-1s2-1 2-2h1 0-5-1c-1 1-4 1-6 1l8-3c4 0 9 0 12-2z" class="F"></path><path d="M510 703c3-1 9-1 13-1 1 1 3 0 5-1l1 1v1l1 1c-2 0-4-1-6-1h-14z" class="E"></path><path d="M510 703h14v2c-6-1-11-1-17 0h-3 0v-1l6-1z" class="i"></path><path d="M505 696c9-1 18-1 27 1 2 1 5 2 7 3-2 0-6 1-8 0-6-2-12-1-18-1l-9 1c-1-1-1-2-3-2v-1-1h4z" class="P"></path><path d="M501 696h4l-1 2c2 1 2 1 3 1l1-1h0c1 1 3 1 5 1l-9 1c-1-1-1-2-3-2v-1-1z" class="M"></path><path d="M530 704c5 0 9 0 14-1h2l1 1-1 1c1 1 2 2 3 2 0-1 0-1-1-2 1 0 2 0 3 1s2 2 2 3c-1 0-2-1-2-1h-1c2 1 2 2 4 2l2 1 1 1v1h5c-1 0-3 1-4 1h0c-2-1-2-1-4-1-3-1-6-4-10-4h0c-1-1-1-1-2-1-1-1-2-1-4-1v1c-2 0-7-2-10-2-1 0-1 0-2-1h-2v-2c2 0 4 1 6 1z" class="a"></path><path d="M489 695c2-1 3-1 5-1v1c1 0 0 1 1 0 1 1 1 1 2 1h3 1v1 1c2 0 2 1 3 2l-9 1-3 1-1 1c0 1-1 1-2 1 1 1 2 1 3 0h0l-2 1v1l-8 4v-1c1-1 1-1 2-1v-1c-2 0-3-1-5 0-4 1-8 2-13 1 2 0 3 0 4-1h3c1-1 2-1 4-1 1-1 2-2 3-2l2-2c1-2 2-2 4-3h1v-2l2-2z" class="C"></path><path d="M482 702h1v2h4c-3 2-5 2-8 2h-1-1c1-1 2-2 3-2l2-2z" class="e"></path><path d="M483 702c1-1 2-1 3-1h3c2-1 5 0 6 0l-3 1c-2 1-4 1-5 2h-4v-2z" class="k"></path><path d="M489 695c2-1 3-1 5-1v1c1 0 0 1 1 0 1 1 1 1 2 1h3 1v1 1c2 0 2 1 3 2l-9 1c-1 0-4-1-6 0h-3c-1 0-2 0-3 1h-1c1-2 2-2 4-3h1v-2l2-2z" class="T"></path><path d="M489 695c2-1 3-1 5-1v1c1 0 0 1 1 0 1 1 1 1 2 1h3l-5 1c-1 0-2 0-2 1-1 1-5 2-7 3-1 0-2 0-3 1h-1c1-2 2-2 4-3h1v-2l2-2z" class="D"></path><path d="M507 705c6-1 11-1 17 0h2c1 1 1 1 2 1 3 0 8 2 10 2v-1c2 0 3 0 4 1 1 0 1 0 2 1h0c4 0 7 3 10 4 1 2 3 3 5 4 2 0 5-1 6 1v1l-2 1c-2 0-1-1-3 1 1 1 1 2 1 3s1 1 1 2c1 1 2 2 3 4l1 1-1 2-4-5h-1 0-2c-2-4-5-7-9-9h0c-9-7-17-10-29-11v-1-1h-9-3l-1-1z" class="P"></path><path d="M546 714c4 0 5 1 8 3 1 0 3 1 5 1v1h-2c1 1 1 2 2 3l1-1c1 1 1 2 1 3s1 1 1 2c1 1 2 2 3 4l1 1-1 2-4-5c-4-6-9-10-15-14z" class="C"></path><path d="M507 705c6-1 11-1 17 0h2c1 1 1 1 2 1 3 0 8 2 10 2v-1c2 0 3 0 4 1 1 0 1 0 2 1h0c4 0 7 3 10 4 1 2 3 3 5 4 2 0 5-1 6 1v1l-2 1c-2 0-1-1-3 1l-1 1c-1-1-1-2-2-3h2v-1c-2 0-4-1-5-1-3-2-4-3-8-3-2-2-5-3-7-4-9-4-19-6-28-4h-3l-1-1z" class="H"></path><path d="M506 682h17 9c0 1 1 2 1 3l1 1h1c2 3 5 2 8 3v1h3l1 1-1 2h1c1 1 0 1 1 1h2 0c2 1 4 2 5 3 0 1-1 3-2 3h-1l-1 1v2 1c1 1 2 1 2 3l-3-3c-2-1-3-1-4-2h0-1c-1-1-2-1-3-1h-1c-1 0-1-1-2-1-2-1-5-2-7-3-9-2-18-2-27-1h-4-1-3c-1 0-1 0-2-1-1 1 0 0-1 0v-1c-2 0-3 0-5 1 1-2 2-3 3-5h0c1-1 1-1 2-1 0-1 0 0 1-1s2-1 2-2h1 0-5-1c-1 1-4 1-6 1l8-3c4 0 9 0 12-2z" class="I"></path><path d="M533 685l1 1h1c2 3 5 2 8 3l-1 1h-3-1c1 1 2 1 3 2h4v1h-2l-1 1c-7-1-15-1-22 0 0-1 1-1 1-1 0-1 0-1-1-1v-1h7l1-1c-2 0-3 0-4-1h0 10l-1-1-2-1-2-1h4v-1z" class="J"></path><path d="M543 689v1h3l1 1-1 2h1c1 1 0 1 1 1h2 0c2 1 4 2 5 3 0 1-1 3-2 3h-1l-1 1v2 1c1 1 2 1 2 3l-3-3c-2-1-3-1-4-2h0-1c-1-1-2-1-3-1h-1c-1 0-1-1-2-1-2-1-5-2-7-3 3-1 4-1 7 0h0l2-1 1-2 1-1h2v-1h-4c-1-1-2-1-3-2h1 3l1-1z" class="B"></path><path d="M548 694h2 0c2 1 4 2 5 3-1 0-2 0-3 1-1 0-3 1-4 1 0 0-1 0-1-1l1-1v-3z" class="L"></path><path d="M503 688h6l2 1h3 10 0c1 1 2 1 4 1l-1 1h-7v1c1 0 1 0 1 1 0 0-1 0-1 1l-13-1c-4 0-9-1-12 2h0c-1 1 0 0-1 0v-1c-2 0-3 0-5 1 1-2 2-3 3-5 2 0 4-2 5-1 2 0 4 0 6-1z" class="K"></path><path d="M492 690c2 0 4-2 5-1 2 0 4 0 6-1 0 1 1 1 2 2h3v1c-5 0-9 0-13 3h-1c-2 0-3 0-5 1 1-2 2-3 3-5z" class="E"></path><path d="M506 682h17 9c0 1 1 2 1 3v1h-4l2 1 2 1 1 1h-10-10-3l-2-1h-6c-2 1-4 1-6 1-1-1-3 1-5 1h0c1-1 1-1 2-1 0-1 0 0 1-1s2-1 2-2h1 0-5-1c-1 1-4 1-6 1l8-3c4 0 9 0 12-2z" class="F"></path><path d="M497 689c6-4 16-3 22-3 3 0 5-1 7 0h3l2 1 2 1 1 1h-10-10-3l-2-1h-6c-2 1-4 1-6 1z" class="V"></path><path d="M531 687l2 1 1 1h-10-10v-1c4 0 9-1 14 0 1 0 2 0 3-1z" class="e"></path><path d="M490 681c5 0 11 0 16 1-3 2-8 2-12 2l-8 3c2 0 5 0 6-1h1 5 0-1c0 1-1 1-2 2s-1 0-1 1c-1 0-1 0-2 1h0c-1 2-2 3-3 5l-2 2v2h-1c-2 1-3 1-4 3l-2 2c-1 0-2 1-3 2-2 0-3 0-4 1h-3c-1 1-2 1-4 1 5 1 9 0 13-1 2-1 3 0 5 0v1c-1 0-1 0-2 1v1l-5 3 1 1c-1 0-3 1-4 1-1 1-1 2-1 3h3c2-1 2-1 5-1l-2 2c-4 3-7 5-9 9-8 9-12 18-16 30-1 7-3 16-2 24v2l4-3v1c0 1-1 2 0 2-1 3-2 3-3 5h-2c-1 0-2 2-3 2l-1 2 1 3h-2l-2 1c-1 0 0 0-1 1h1c1 0 2 0 3-1h2c3 1 4 0 7-1h0l-1 1 3 1-1 3h-1l1 3c0 2 1 3 2 5l-1 2c-1 1-1 1-2 0l-1 2h0-1l-2-2v3h-1c-1-2-2-4-5-5h-1c-1-1-2-1-3-1l-1 1c-7 1-13 5-20 5l-6 1c-8 0-17-1-24-5-10-6-19-17-22-28-4-11-4-29 2-40h1 1l6-11c2-4 4-6 7-9 2-1 3-3 4-5 4-3 10-6 14-8v-1c2-2 4-2 6-3 3-1 6-2 9-2l10-1h3c2 0 6 1 8 1 1-1 4-3 5-4l6-5 3-2 5-4h0l1-2 2-1 2 1 2-1c3-1 7-2 11-2l13-2z"></path><path d="M392 746l1-1c1 1 2 2 2 4 0 1-1 2-1 3-1 1-2 2-2 4-2-2-4-3-6-3l-1-1c3 0 7-1 9-3-1-2-1-2-2-3zm18 21c3 0 3 0 5 2 1 0 1 1 2 1 0 1 1 2 2 3h4l2 2h-3v1 1l-1 1-10 1 6-5c-2-4-3-5-7-7z" class="M"></path><path d="M399 745h-1c2-2 4-2 6-2 4 0 9 2 13 4v1l-1 7-1-2c0 1 0 2-2 3-2-2-4-3-6-3s-3 1-4 2c0 2 0 3 1 4-2 1-8 0-9 0l-3-3c0-2 1-3 2-4 0-1 1-2 1-3h3v-2l1-2z" class="T"></path><path d="M399 745h-1c2-2 4-2 6-2 4 0 9 2 13 4v1l-1 7-1-2v-2c-3-3-6-3-10-4-2 2-5 7-7 8-1 0-2-1-2-2-1-1-1-1-2-1 0-1 1-2 1-3h3v-2l1-2z" class="H"></path><path d="M399 745l1 2v1h0 2v1h-1c-2 2-3 3-3 6-1 0-2-1-2-2-1-1-1-1-2-1 0-1 1-2 1-3h3v-2l1-2z" class="k"></path><path d="M422 712h1c2 1 3 3 3 5v1c1 0 2 1 3 1-3 4-5 9-7 13-1 2-1 6-2 7l-3 9v-1c-4-2-9-4-13-4-2 0-4 0-6 2h1l-1 2v2h-3c0-2-1-3-2-4l-1 1c-2 0-4-1-6-1-3 1-7 3-9 3l-1-1 1-1c2-2 6-4 8-4v-4c0-4 4-7 6-9 5-4 10-5 16-4 4 0 9 1 11 4h4c0-2 1-3 2-4s1-2 1-3v-1c-2-1-4 1-6-1l1-1c1 1 2 1 3 1l2-2c0-1 0-2-1-2v-1l-2-3z" class="B"></path><path d="M393 734c5-3 10-3 16-2l9 3c1 0 1 1 2 1-1 2 0 1-1 2h-1c-1-1-2-1-3-1-2-1-4-2-5-2h-1c-2-1-5-1-8-1-2 1-2 0-3 1h0 8v1c-6 0-11 1-16 3h-1v-1h0c2-1 3-2 4-4z" class="K"></path><path d="M385 742v-4c0-4 4-7 6-9 5-4 10-5 16-4 4 0 9 1 11 4l3 3h-1 0l-3-1c1 1 2 2 2 3h-1c-2-2-3-3-5-3-1 1-2 1-4 1-6-1-11-1-16 2l-2-1c-3 3-4 4-4 7v1l-2 1z" class="c"></path><path d="M391 733l3-2c4-3 11-4 16-3 2 1 4 2 7 3h0c1 1 2 2 2 3h-1c-2-2-3-3-5-3-1 1-2 1-4 1-6-1-11-1-16 2l-2-1z" class="I"></path><path d="M387 741c9-3 16-6 25-1 1 1 2 1 2 2 2 1 2 1 3 3v2c-4-2-9-4-13-4-2 0-4 0-6 2h1l-1 2v2h-3c0-2-1-3-2-4l-1 1c-2 0-4-1-6-1-3 1-7 3-9 3l-1-1 1-1c2-2 6-4 8-4l2-1z" class="T"></path><path d="M393 745h2c1 0 2 1 3 2v2h-3c0-2-1-3-2-4z" class="e"></path><path d="M414 742c0 1 1 2 1 3-2-1-3-2-4-3v1h-1c-1-1-2-1-3-1-3 0-8-1-10 2h-1l-2-1c0-1 2 0 3-2h-3c1-1 3-1 5-1h12 1c1 1 2 1 2 2z" class="Z"></path><path d="M373 742v1l-1 1 1 1v-1l4-6 4-7v-1l4-4c2-1 3-3 5-4h0c-3 4-7 8-10 12-6 9-10 23-10 34 1 11 6 23 14 30 6 5 13 9 20 10h1c7 1 15 0 20-5 3-2 4-4 6-7h0l-1-3c-2-3-1-6-3-9-1-2-2-3-4-4-1 0-1 0-1-1v-1h-1l1-1v-1-1h3c1 0 2 1 3 1l1-1c2 2 3 4 5 5l4 10c1 1 2 2 3 2 1 1 3 0 4 0s2 0 3-1l-1 2 1 3h-2l-2 1c-1 0 0 0-1 1h1c1 0 2 0 3-1h2c3 1 4 0 7-1h0l-1 1 3 1-1 3h-1l1 3c0 2 1 3 2 5l-1 2c-1 1-1 1-2 0l-1 2h0-1l-2-2v3h-1c-1-2-2-4-5-5h-1c-1-1-2-1-3-1l-1 1c-7 1-13 5-20 5l-6 1c-8 0-17-1-24-5-10-6-19-17-22-28-4-11-4-29 2-40h1 1z" class="X"></path><path d="M422 778c3 1 5 2 7 5s2 9 2 13l-1-3c-2-3-1-6-3-9-1-2-2-3-4-4-1 0-1 0-1-1v-1z" class="b"></path><path d="M433 786c2 4 2 9 0 13-1 2-2 4-3 7h-1c-2 1-5 3-7 3 0-1 1-1 0-2 6-4 10-9 10-16 0-2 0-3 1-5z" class="B"></path><path d="M429 775c2 2 3 4 5 5l4 10-1 3h0l-1 3c0 4-3 6-5 9l-1 1c1-3 2-5 3-7 2-4 2-9 0-13 0-1 0-2-1-3-2-4-6-5-10-6v-1-1h3c1 0 2 1 3 1l1-1z" class="F"></path><path d="M429 775c2 2 3 4 5 5l4 10-1 3h0c-1-4-2-8-5-11-2-3-6-5-10-6v-1h3c1 0 2 1 3 1l1-1z" class="P"></path><path d="M438 790c1 1 2 2 3 2 1 1 3 0 4 0s2 0 3-1l-1 2 1 3h-2l-2 1c-1 0 0 0-1 1h1c1 0 2 0 3-1 0 1-1 2-1 3-2-1-3-1-4-1l-2 1-1 1c-2 1-5 3-8 4 2-3 5-5 5-9l1-3h0l1-3z" class="J"></path><path d="M445 792c1 0 2 0 3-1l-1 2 1 3h-2-1c-2-1-2-1-4-1-1 1-1 0-3 0v-3l1 1h1c1 1 1 1 2 1 2 0 2 0 3-2z" class="O"></path><path d="M411 813c-2-1-4-1-7-1-10-2-21-10-27-18-1-1 0-1 0-2 5 7 10 11 17 14 9 4 19 4 28 1 1 1 0 1 0 2 2 0 5-2 7-3h1l1-1c3-1 6-3 8-4l2 1 1 1c-8 5-14 8-23 9l2 2h0l-6 1c-1-2-2-2-4-2z" class="j"></path><path d="M411 813l8-1 2 2h0l-6 1c-1-2-2-2-4-2z" class="P"></path><path d="M449 797c3 1 4 0 7-1h0l-1 1 3 1-1 3h-1l1 3c0 2 1 3 2 5l-1 2c-1 1-1 1-2 0l-1 2h0-1l-2-2v3h-1c-1-2-2-4-5-5h-1c-1-1-2-1-3-1l-1 1c-7 1-13 5-20 5h0l-2-2c9-1 15-4 23-9l-1-1-2-1 1-1 2-1c1 0 2 0 4 1 0-1 1-2 1-3h2z" class="N"></path><path d="M449 797c3 1 4 0 7-1h0l-1 1 3 1-1 3h-1-1c-1 1-1 1-1 2l1 1h0c-3 0-4-1-6 0-2 0-3 0-4-1v1c-2 0-2 0-3-1l-1-1-2-1 1-1 2-1c1 0 2 0 4 1 0-1 1-2 1-3h2z" class="P"></path><path d="M455 797l3 1-1 3h-1-1c-2-1-3 1-5 2 0-2 1-2 2-3 0 0 3-2 3-3z" class="M"></path><path d="M449 797c3 1 4 0 7-1h0l-1 1c0 1-3 3-3 3-4 1-7 2-10 3l-1-1-2-1 1-1 2-1c1 0 2 0 4 1 0-1 1-2 1-3h2z" class="E"></path><path d="M490 681c5 0 11 0 16 1-3 2-8 2-12 2l-8 3c2 0 5 0 6-1h1 5 0-1c0 1-1 1-2 2s-1 0-1 1c-1 0-1 0-2 1h0c-1 2-2 3-3 5l-2 2v2h-1c-2 1-3 1-4 3l-2 2c-1 0-2 1-3 2-2 0-3 0-4 1h-3c-1 1-2 1-4 1 5 1 9 0 13-1l1 1c-4 2-10 4-14 4-3 0-6-1-8-2-1 1-1 1-1 2-2 0-6-1-7-2s-1-1-2-1c-2 1-2 1-4 1h0c-5 5-9 9-13 15-6 15-12 28-6 44 1 2 2 5 4 6l-1 1c-1 0-2-1-3-1l-2-2h-4c-1-1-2-2-2-3-1 0-1-1-2-1-2-2-2-2-5-2h-2-1c1-3 2-5 4-8l2-3c2-1 2-2 2-3l1 2 1-7 3-9c1-1 1-5 2-7 2-4 4-9 7-13-1 0-2-1-3-1v-1c0-2-1-4-3-5h-1c0-2 0-1-1-2-3 1-5 1-7-1l-4 1c-4 2-9 4-12 7-3 1-5 4-8 5h0c-2 1-3 3-5 4l-4 4v1l-4 7-4 6v1l-1-1 1-1v-1l6-11c2-4 4-6 7-9 2-1 3-3 4-5 4-3 10-6 14-8v-1c2-2 4-2 6-3 3-1 6-2 9-2l10-1h3c2 0 6 1 8 1 1-1 4-3 5-4l6-5 3-2 5-4h0l1-2 2-1 2 1 2-1c3-1 7-2 11-2l13-2z" class="f"></path><path d="M462 685l2 1-3 2c-3 2-4 3-6 6h-2-2l3-2 5-4h0l1-2 2-1z" class="g"></path><path d="M428 707h10c-2 4-7 8-9 12-1 0-2-1-3-1v-1h1v-3h3l-1-2 2-1 1-1c1 0 2-2 2-3h-6z" class="D"></path><path d="M454 696c4-1 8-4 12-6l1 1c-5 2-10 5-14 8-3 2-4 4-6 5-1 1-2 1-3 2a30.44 30.44 0 0 0-8 8l-1-1c3-4 9-10 13-13l6-4z" class="V"></path><path d="M414 709c4-1 10-2 14-2h6c0 1-1 3-2 3l-1 1-2 1 1 2h-3v3h-1c0-2-1-4-3-5h-1c0-2 0-1-1-2-3 1-5 1-7-1z" class="H"></path><path d="M404 709h4l1-1h2l6-2 3-1h3 1l4-1h1 0c-1 2-3 2-4 2l-5 1c-1 0-3 0-4 1h-2l-2 1c-1 0-1 0-2 1h0c-4 2-9 4-12 7-3 1-5 4-8 5h0c-2 1-3 3-5 4l-4 4v1l-4 7-4 6v1l-1-1 1-1v-1l6-11c2-4 4-6 7-9 2-1 3-3 4-5 4-3 10-6 14-8z" class="l"></path><path d="M490 681c5 0 11 0 16 1-3 2-8 2-12 2h-2c-8 3-17 3-25 7l-1-1c-4 2-8 5-12 6 2-3 6-4 7-8l3-2 2-1c3-1 7-2 11-2l13-2z" class="T"></path><path d="M466 685c3-1 7-2 11-2l1 1c-3 2-6 3-9 3h-1l-2-2z" class="c"></path><path d="M490 681c5 0 11 0 16 1-3 2-8 2-12 2h-2c-8 3-17 3-25 7l-1-1c6-3 13-5 19-6 1 0 1 0 2-1-3 0-6 1-9 1l-1-1 13-2z" class="Q"></path><path d="M467 691c8-4 17-4 25-7h2l-8 3c2 0 5 0 6-1h1 5 0-1c0 1-1 1-2 2s-1 0-1 1c-1 0-1 0-2 1h0c-1 2-2 3-3 5l-2 2v2h-1c-2 1-3 1-4 3l-2 2c-1 0-2 1-3 2-2 0-3 0-4 1h-3c-1 1-2 1-4 1l-7-2-7-3c-1 1-1 1-2 1 1-1 2-3 3-4v-1c4-3 9-6 14-8z" class="W"></path><path d="M478 702h2v2c-1 0-2 1-3 2-2 0-3 0-4 1h-3c-1 1-2 1-4 1l-7-2h3l1-1v1h5c3-1 8-2 10-4zm-9-4l2-1c2-1 4-2 6-2l1 1c1 0 3 1 4 1 1-1 1-1 2-1h0l1-1c1 0 1 0 2 1h0l1-1s1-2 1-3h-1v-2h2l-1 1h1c1 0 1-1 2-1h0c-1 2-2 3-3 5l-2 2h-1-2c-2 1-4 1-5 1-2 1-6 1-8 1-1 0-2-1-2-1z" class="H"></path><path d="M469 698s1 1 2 1c2 0 6 0 8-1 1 0 3 0 5-1h2 1v2h-1c-2 1-3 1-4 3l-2 2v-2h-2l-1-1h0c-2 1-3 1-5 0-1 1-2 1-3 1v-1c-2 1-2 1-4 1 1-2 3-3 4-4z" class="L"></path><path d="M467 691c8-4 17-4 25-7h2l-8 3c-13 3-23 9-34 16-1 1-1 1-2 1 1-1 2-3 3-4v-1c4-3 9-6 14-8z" class="d"></path><path d="M436 714a30.44 30.44 0 0 1 8-8c1-1 2-1 3-2 2-1 3-3 6-5v1c-1 1-2 3-3 4 1 0 1 0 2-1l7 3 7 2c5 1 9 0 13-1l1 1c-4 2-10 4-14 4-3 0-6-1-8-2-1 1-1 1-1 2-2 0-6-1-7-2s-1-1-2-1c-2 1-2 1-4 1h0c-5 5-9 9-13 15-6 15-12 28-6 44 1 2 2 5 4 6l-1 1c-1 0-2-1-3-1l-2-2h-4c-1-1-2-2-2-3-1 0-1-1-2-1-2-2-2-2-5-2h-2-1c1-3 2-5 4-8l2-3c2-1 2-2 2-3l1 2 1-7 3-9v3c3-11 8-19 15-29l1 1z" class="k"></path><path d="M444 710l6-5h0l-1 2v1c3 0 6 1 9 2-1 1-1 1-1 2-2 0-6-1-7-2s-1-1-2-1c-2 1-2 1-4 1h0z" class="C"></path><path d="M420 762c1 4 2 7 3 11h-4c-1-2-1-4-2-6 1 0 1 0 1 1l1 1v-1c0-1 0-3 1-5v-1z" class="j"></path><path d="M435 713l1 1c-3 4-5 7-7 11-3 7-6 13-8 19-1 6-1 12-1 18v1c-1 2-1 4-1 5v1l-1-1c0-1 0-1-1-1h-1 1c2-2 1-11 1-14l2-10v-1c3-11 8-19 15-29z" class="W"></path><path d="M417 748l3-9v3 1l-2 10c0 3 1 12-1 14h-1 1c1 2 1 4 2 6-1-1-2-2-2-3-1 0-1-1-2-1-2-2-2-2-5-2h-2-1c1-3 2-5 4-8l2-3c2-1 2-2 2-3l1 2 1-7z" class="R"></path><path d="M413 756c2-1 2-2 2-3l1 2c1 2 0 3 0 5v3 1c-2-1-4-2-5-5l2-3z" class="D"></path><path d="M411 759c1 3 3 4 5 5v2l-4-2h0l4 3h1c1 2 1 4 2 6-1-1-2-2-2-3-1 0-1-1-2-1-2-2-2-2-5-2h-2-1c1-3 2-5 4-8z" class="T"></path><path d="M479 707c2-1 3 0 5 0v1c-1 0-1 0-2 1v1l-5 3 1 1c-1 0-3 1-4 1-1 1-1 2-1 3h3c2-1 2-1 5-1l-2 2c-4 3-7 5-9 9-8 9-12 18-16 30-1 7-3 16-2 24v2l4-3v1c0 1-1 2 0 2-1 3-2 3-3 5h-2c-1 0-2 2-3 2-1 1-2 1-3 1s-3 1-4 0c-1 0-2-1-3-2l-4-10c-2-1-3-3-5-5-2-1-3-4-4-6-6-16 0-29 6-44 4-6 8-10 13-15h0c2 0 2 0 4-1 1 0 1 0 2 1s5 2 7 2c0-1 0-1 1-2 2 1 5 2 8 2 4 0 10-2 14-4l-1-1z" class="b"></path><path d="M426 753l-1-3v-2c1 0 1 0 1 1 2 3 5 6 6 8l-2 3-4-7z" class="V"></path><path d="M429 737c1 0 0 0 1 1l2 4c1 5 4 11 6 16l-1-1c-4-4-8-14-8-20z" class="I"></path><path d="M435 734l1-1v-1-1s-1-1-1-2v-2l-1-1v-1h1c0 1 1 2 2 2 0 1 0 2 1 3 0 1 0 2-1 3h0c0 3 0 5 1 7l1 2h0c1 1 1 2 1 3l3 5c2 2 3 6 4 9 0 1 1 2 2 3v3h-1c-1-1-2-3-2-4v-2h-1v-2c0-1-1-3-2-5l-1-1c0-1-1-2-1-3l-4-7v-2c-1-2-2-3-2-5z" class="W"></path><path d="M426 753l4 7 2-3c4 7 6 13 8 20 0 2 1 7 2 9l1 1c3 0 5-1 7-2l2-1 4-3v1c0 1-1 2 0 2-1 3-2 3-3 5h-2c-1 0-2 2-3 2-1 1-2 1-3 1s-3 1-4 0c-1 0-2-1-3-2l-4-10h1c1-2-2-6-2-8l-6-13h0c-1-2-2-4-1-6z" class="C"></path><path d="M426 753l4 7c2 3 4 7 5 10h0c-3-2-3-4-4-6l-3-6-1 1h0c-1-2-2-4-1-6z" class="S"></path><path d="M432 757c4 7 6 13 8 20 0 2 1 7 2 9l1 1c3 0 5-1 7-2l2-1 4-3v1c0 1-1 2 0 2-2 1-4 3-6 4h-1-2c-3 1-5 1-8 0-1-1-2-2-3-4h1l3 2h0c-3-5-3-11-5-15v-1c-1-3-3-7-5-10l2-3z" class="l"></path><path d="M479 707c2-1 3 0 5 0v1c-1 0-1 0-2 1v1l-5 3 1 1c-1 0-3 1-4 1-1 1-1 2-1 3h3c2-1 2-1 5-1l-2 2c-4 3-7 5-9 9-8 9-12 18-16 30v-1-2c0-2 1-5 0-7v-7-2l-1-1v-2-1-1c-1-1-1-1-1-2h-3v2h-1v5h1c-2 2-1 4-3 5v-1l1-1h-2v-5c1-2 2-3 2-4-1-1-1 0-1-1l-1-1h0l-1 5 1 1v1l-1-1-1 1-1-1v-1 1h-1v-2c-1 0-1-1-1-2h-1c0-2-1-2-1-3-1-1-1-2-1-3-1 0-2-1-2-2h-1v1l1 1v2c0 1 1 2 1 2v1 1l-1 1v-1s0-1-1-2v-1-1c-1-2-1-2-3-4 4-6 8-10 13-15h0c2 0 2 0 4-1 1 0 1 0 2 1s5 2 7 2c0-1 0-1 1-2 2 1 5 2 8 2 4 0 10-2 14-4l-1-1z" class="H"></path><path d="M462 722h-2c-1-1-3-2-4-2-3-1-5-2-7-2-2-1-2-1-4-1h-2l-1-1c2-1 3-2 4-3 1 0 2 1 3 1 4 0 7 0 11 2h-1v1h-2c1 2 2 2 3 3 2 0 2 1 2 2z" class="D"></path><path d="M444 736c-1-3-1-6-1-10 0-1 0-4-1-6h0c2 1 2 4 2 6 1 2 2 3 2 5h1c1-3 2-5 5-7 1 0 3 0 4 1h2v2l-3 3v1l-1 1v3 4l-1-1v-2-1-1c-1-1-1-1-1-2h-3v2h-1v5h1c-2 2-1 4-3 5v-1l1-1h-2v-5c1-2 2-3 2-4-1-1-1 0-1-1l-1-1h0l-1 5z" class="F"></path><path d="M479 707c2-1 3 0 5 0v1c-1 0-1 0-2 1v1l-5 3 1 1c-1 0-3 1-4 1-2 1-4 1-7 2-2 0-5-1-7-1-4-2-7-2-11-2-1 0-2-1-3-1 4-2 7-1 11 0 1 0 1 0 2-1h-2c0-1 0-1 1-2 2 1 5 2 8 2 4 0 10-2 14-4l-1-1z" class="E"></path><path d="M446 713c4-2 7-1 11 0 7 1 13 2 20 0l1 1c-1 0-3 1-4 1-2 1-4 1-7 2-2 0-5-1-7-1-4-2-7-2-11-2-1 0-2-1-3-1z" class="M"></path><path d="M467 717c3-1 5-1 7-2-1 1-1 2-1 3h3c2-1 2-1 5-1l-2 2c-4 3-7 5-9 9-3-1-4-3-7-5-1 0 0 0-1-1 0-1 0-2-2-2-1-1-2-1-3-3h2v-1h1c2 0 5 1 7 1z" class="F"></path><path d="M460 716c2 0 5 1 7 1h-2v1h2v1c-1 0-2 0-2 1h-2c-1-2-2-3-4-4h1z" class="C"></path><path d="M459 716c2 1 3 2 4 4h2c2 1 1 1 2 3 1 0 1 0 2 1l1 1c1-2 1-2 2-3h1c0-1 0-2 1-3h5c-4 3-7 5-9 9-3-1-4-3-7-5-1 0 0 0-1-1 0-1 0-2-2-2-1-1-2-1-3-3h2v-1z" class="E"></path></svg>