<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="72 40 524 624"><!--oldViewBox="0 0 648 752"--><style>.B{fill:#2e2d2e}.C{fill:#403f40}.D{fill:#555454}.E{fill:#202020}.F{fill:#3a3a3a}.G{fill:#373636}.H{fill:#5b5a5a}.I{fill:#272627}.J{fill:#757474}.K{fill:#a9a8a8}.L{fill:#c1c0c1}.M{fill:#a1a0a0}.N{fill:#858485}.O{fill:#b4b3b3}.P{fill:#c0bebf}.Q{fill:#c5c4c5}.R{fill:#4e4d4e}.S{fill:#666565}.T{fill:#dcdbdc}.U{fill:#4a494a}.V{fill:#cecdcd}.W{fill:#464646}.X{fill:#292929}.Y{fill:#252525}.Z{fill:#979697}.a{fill:#1c1c1c}.b{fill:#333233}.c{fill:#6a6a6a}.d{fill:#605f60}.e{fill:#b9b8b8}.f{fill:#807f80}.g{fill:#7b7a7a}.h{fill:#e3e2e3}.i{fill:#d2d1d2}.j{fill:#f1f1f1}.k{fill:#8f8d8e}.l{fill:#9c9b9b}.m{fill:#454445}.n{fill:#6f6e6f}.o{fill:#929191}.p{fill:#8b898a}.q{fill:#181818}.r{fill:#141414}.s{fill:#f5f4f4}</style><path d="M88 326c1 0 1 1 1 1v1h-1l-1-1 1-1zm17 59h1v1l-1 1h-1v-1l1-1zm455-80l1-1 1 1v2h-1c-1-1-1-1-1-2z" class="a"></path><path d="M332 74c1 0 2 0 2 1h0c-1 0-1 0-2 1v-1-1zM121 375l1-1 1 1-1 1h-2l1-1z" class="E"></path><path d="M188 483c1-1 2-1 3-1v1s0 1-1 1c0 0-1 0-2-1z" class="X"></path><path d="M537 203l2 2h-1-1l-1-1 1-1z" class="a"></path><path d="M433 512h2v1 1l-2-2zm-255-47l1 1v1h-2-1l1-1 1-1z" class="E"></path><path d="M170 452v-1l1-1 1 1c0 1 0 1-1 1h-1z" class="X"></path><path d="M133 404h1c-1 1-1 2-1 2-1 0-1 0-1-1l1-1z" class="a"></path><path d="M225 499c1 1 1 0 2 1v1h-1c-1 0-1 0-1-2z" class="B"></path><path d="M100 386h1v2h0-1c-1 0-1 0-1-1l1-1z" class="E"></path><path d="M183 465h1v1c0 1 1 2 0 3l-1-1v-3zm27-5h1l1 3h-1 0c-1-1-1-2-1-3zm316-80h2l1 1h1c-1 1-2 0-3 1-1-1-1-1-1-2z" class="a"></path><path d="M465 467h1l1 1c-1 0-2 0-3 1h-1v-1c0-1 1-1 2-1zm-358-90v2c1 2 2 2 1 3-1 0-1-1-2-1v-1c1-1 1-2 1-3z" class="E"></path><path d="M144 403c1 0 3 1 3 1v1h-2l-1-1v-1z" class="a"></path><path d="M581 207l1-1c0 1 0 1 1 2l-1 1h-2v-1l1-1z" class="Q"></path><path d="M437 516v-3h1l1 2h0c-1 1-1 1-2 1z" class="I"></path><path d="M180 529l1-1 1 1-1 1-1 1c0-1 0-1-1-2h1z" class="V"></path><path d="M534 369c-1 1-2 1-3 1v-1-1h4c-1 0-1 1-1 1z" class="Y"></path><path d="M154 147h0c0 1 1 2 1 2v1h-1 0l-2-2c-1 0-1-1-1-1l2 1 1-1z" class="a"></path><path d="M438 500c0 1 1 1 1 2l-2 2-1-1c0-1 1-2 2-3z" class="G"></path><path d="M449 641h0v-1h2v1h3c-2 1-5 1-7 1l2-1z" class="E"></path><path d="M326 48h1c1 0 1 0 1 1v1c-1 1-1 1-2 0v-2z" class="T"></path><path d="M545 365h1 0c0 1-1 2-2 3-1 0-1-1-1-1 0-1 1-2 2-2z" class="Y"></path><path d="M211 451h1c1 0 1 1 2 1v1c-2 1-2 0-3-1v-1z" class="a"></path><path d="M229 493l2 2v1h-5l2-1 1-2zM81 336l1 1v1l-1 1c-1 0-1-1-2-1l2-2z" class="I"></path><path d="M120 648h1s1 1 1 2h-1c-1 1-1 1-2 0 0-1 0-1 1-2z" class="L"></path><path d="M446 169h1l3-1c-1 1-1 3-2 3-1-1-2-1-2-2z" class="a"></path><path d="M541 405h2v1 1h-2v-1-1z" class="F"></path><path d="M166 455c1 0 1 0 2 1s1 2 2 3h-1-1c0-1-1-2-2-4z" class="B"></path><path d="M369 388h1c1 1 1 1 1 2l-1 1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="P"></path><path d="M347 666h1l1 1-1 2h-1l-1-1c0-1 0-1 1-2z" class="K"></path><path d="M271 651c1 0 1-1 2 0s1 1 0 2h-1-1v-2z" class="V"></path><path d="M300 34l1-1c1 1 1 1 1 2v1h-2l-1-1 1-1z" class="h"></path><path d="M225 483l3-2h0v2 2c-1 0-2-2-3-2z" class="B"></path><path d="M551 347c1 0 2 0 3 1l-1 1h-1-2v-1l1-1z" class="E"></path><path d="M362 654h1 1c0 2 0 1-1 2h-1s-1 0-1-1l1-1z" class="l"></path><path d="M199 476h0c0-1-1-1-1-2-1 0-1 0-1-1h1 0l3 3v1c-1 0-1-1-2-1z" class="F"></path><path d="M511 416l1 1h-1c-1 1-1 3-2 5-1-1-1-2-1-3l3-3z" class="b"></path><path d="M234 571l3 3h-1c-2 0-2 0-3-1l1-2z" class="K"></path><path d="M384 51h1c1 1 1 1 1 2l-1 1c-1-1-1-1-2-1 0-1 0-1 1-2z" class="h"></path><path d="M420 442c1-1 1 0 1 0l1 1-1 1h-1c-1 0-1-1-1-2h1zm-66-61c1 2 1 2 0 3-1 0-1-1-2-1h-1v-1h1l2-1z" class="K"></path><path d="M144 430c0 1-1 2-2 2l-1-1h-1l-1-1v-1l1 1h1l1-1v1h2z" class="E"></path><path d="M525 215c2 0 2 1 3 2l-1 1c-1 0-1 0-2-1v-2z" class="B"></path><path d="M532 246h1c0 1 1 1 1 2l-1 1c-1 0-2 0-2-1v-1l1-1z" class="b"></path><path d="M137 533h1c1 1 1 1 1 2l-1 1c-1 0-1 0-2-1 0-1 0-1 1-2z" class="j"></path><path d="M431 569h1 2l1 2-3 1v-1h0c0-1 0-1-1-1v-1z" class="Y"></path><path d="M89 267c0 1 0 3-1 5h-1c0-1-1-1-1-2l3-3z" class="E"></path><path d="M196 663h1c1 1 1 1 1 2l-1 1h-1l-1-1c0-1 0-1 1-2z" class="V"></path><path d="M526 380c0 1 0 1 1 2l-1 1h-1-2s0-1 1-1c0-1 1-2 2-2z" class="Y"></path><path d="M196 590h3v2h-3-4c1-1 3 0 4-1v-1z" class="M"></path><path d="M535 330c1 0 2 1 2 2 1 1 1 0 2 1l-1 1h-1l-2-2v-2z" class="E"></path><path d="M482 409c2 0 3 1 5 1h0 0v1c-1 0-1 0-2 1h0-1v-1-1h-1l-1-1z" class="G"></path><path d="M409 509h0c1 1 2 1 3 1h0v1l-1 1-2 1-1-1 2-2h-2l1-1z" class="E"></path><path d="M542 157c1 0 1 0 2 1l1 1-1 1h-2l-1-1v-1l1-1z" class="O"></path><path d="M433 69h1c1 1 1 2 1 3l-2 1c-1-1-1-1-1-2s0-1 1-2z" class="h"></path><path d="M605 358h2l1 1v1l-1 1c-1 0-1 0-2-1v-2z" class="L"></path><path d="M430 556c1 2 1 4 3 6h-1-2v-2c-1-2-1-3 0-4z" class="U"></path><path d="M535 368v-1l1 1h2 0c0 1-1 2-1 2v1c-1 0-1 0-2 1l1-2c-1 0-1 0-2-1 0 0 0-1 1-1z" class="E"></path><path d="M462 671h1c0 1 1 1 2 2l-2 2h-1l-1-2h-1l2-2z" class="j"></path><path d="M148 420v-1c0-1 0-2 1-3l2 1h0v2c-1 0-2 0-3 1z" class="I"></path><path d="M113 335h0c-1 1-1 3-1 4-1 1-1 3-1 4h-1 0-1c1-1 1-2 1-3s1-4 3-5z" class="a"></path><path d="M533 378c1 2 1 2 1 4h0c-1 1-2 2-3 2v-1c1-2 2-3 2-5h0z" class="Y"></path><path d="M345 305c1 1 2 1 2 2h1c-1 1-3 2-5 2v-1c1-1 2-1 2-3z" class="F"></path><path d="M415 260h2c1 0 2 2 3 2-1 1-1 1-1 2 0 0-1 0-1-1-1 1-1 1-2 1h0c0-2 1-2 1-3-1 0-2 0-2-1z" class="I"></path><path d="M205 264h1c1 1 1 2 1 4h0l-2-1c0-1-1-2-1-3h1z" class="C"></path><path d="M353 279l6 3h0v1c-2 0-3-1-5-1-1-1-1-2-1-3z" class="B"></path><path d="M443 648h2l1 1c0 1 0 1-1 2h-2c0-1-1-1-1-2l1-1z" class="e"></path><path d="M593 302h0c1 0 1 0 2 1 0 1 0 1-1 2-1 0-1 0-2-1 0-1 0-1 1-2z" class="L"></path><path d="M234 571v-1h2c1 0 1 1 2 2h0l1 1h-1l-1 1-3-3z" class="P"></path><path d="M454 264c2 0 1 1 2 2 1 0 1 0 2-1h1v1l-1 1h-3c0-1-1 0-2-1v-1l1-1z" class="B"></path><path d="M292 652c2 0 2 0 2 1 1 1 0 1 0 2h-2c-1-1-1-1-1-2l1-1z" class="M"></path><path d="M554 236h1c1 3 1 5 2 7h-1c-1 0-2-3-2-4v-3zm-172-30c2 1 3 4 5 5 2 0 3 1 3 2h0c-1 0-2-1-3-1-1-1-1-1-2-1h0l-1-1c0-1 0-2-1-3-1 0-1 0-1-1z" class="I"></path><path d="M528 337c1 0 1 0 2 1v1l-1 1h-2 0l-1-1 2-2z" class="E"></path><path d="M118 423c1 0 1 0 2 2v-1c1 0 1 1 2 1h0v2 1h0v-1l-2-2-1 1c-1-1-2-1-2-2l1-1z" class="X"></path><path d="M460 205h0c0 1 0 1 1 2v1c0 1-1 2-1 3h0c-1-2-2-3-1-4l1-2z" class="a"></path><path d="M103 212v-2h1 0c1 0 1-1 1-1h2 0c0 1-1 2-1 3h-1-2z" class="q"></path><path d="M437 500h1c-1 1-2 2-2 3-1 1-2 0-3 0v-1c0-1 2-2 4-2z" class="F"></path><path d="M375 506v-6h1c1 2 1 5 1 7h-1-1v-1z" class="H"></path><path d="M481 638l1 1h2 1v1l-8 1c1-1 1-2 2-2s1 0 2-1z" class="C"></path><path d="M528 380l1-2v-1c1 0 1-1 1-1l1-1h0c0 1 0 2-1 2 1 1 1 2 1 3l-1 1h-1l-1-1z" class="X"></path><path d="M407 301c1-1 1 0 1 0 2 0 2 0 2 2l-1 1h-1l-1-1c-1-1 0-1 0-2z" class="d"></path><path d="M160 150h1c-1 1-3 2-4 3 0 0 1 1 0 2h-1c-1-1-1-1-1-2 1-1 3-2 5-3zm-41 224c0 1 0 2 1 2v1s1 1 2 1v1h-3c-1-1 0-1-1-3l-1-1c1 0 1 0 2-1z" class="E"></path><path d="M221 650h1c1 0 1 0 2 1 0 1 0 1-1 2l-1 1-1-1v-3z" class="Q"></path><path d="M367 78l5 2c2 0 3 0 5 1v1h0c-1 0-2-1-2-1-3 0-6 0-8-2v-1z" class="a"></path><path d="M241 389h1c0 1 1 1 1 2h0c0 1 1 1 1 2h-1-3v-2c0-1 0-1 1-2z" class="B"></path><path d="M466 598c2 0 2 0 3 1 0 1 0 1-1 2v1c-2-1-2-1-3-2 0-1 0-1 1-2z" class="M"></path><path d="M119 426l1-1 2 2v1h0l1 1v1h-2c-1-1-2-2-2-3h0v-1z" class="B"></path><path d="M463 612l1-6 1 8v1h-2v-3z" class="T"></path><path d="M164 641h4 1l-1 1 3 1c-1 0-5 0-7-1h-3l3-1z" class="B"></path><path d="M454 573h1c1 1 1 1 1 2s0 1-1 2h-1c-1 0-1-1-2-2l2-2z" class="e"></path><path d="M468 475h0c-1 2 0 3 0 4h-3c-1-1-1 0-2-1 2-1 3-2 5-3z" class="a"></path><path d="M374 199h1v1s0 1-1 1c0 0-1 0-1 1h0-3-1l-1-1v-1c2 1 4 0 6-1h0z" class="Y"></path><path d="M506 340h1c1 1 1 1 1 3-1 1-1 1-2 1h-1c-1-1-1-1-1-2s1-1 2-2z" class="E"></path><path d="M204 566h2c1 1 1 3 1 4v1c0 1-1 1-2 2v-2c0-2 1-4-1-5z" class="J"></path><path d="M468 465h1v1c0 1 1 1 1 2h2c0-1 0-1 1-2h0v1h0v1l-1 1c-1 1-2 1-3 1-1-1 0-2-1-4v-1z" class="E"></path><path d="M474 632c1-1 2-1 3-2 1 0 2 1 3 2l-2 2c-1-1-2-1-4-2z" class="R"></path><path d="M540 387h0c0 1 1 1 2 1-2 0-2 0-3 1 0 1 0 2-1 3h0-1l-1-1v-1c1-1 2-2 4-3z" class="Y"></path><path d="M163 439l2 2 1-1h2v1c-1 0-1 1-2 2h-1-1c-1 0-1-1-2-2h0 1v-2z" class="E"></path><path d="M240 438h1c1 0 1 0 1 1 0 2 0 2-1 3h-1c-1-1-2-1-2-2s1-2 2-2z" class="d"></path><path d="M437 500c1-1 3-3 4-3 0 0 0 1 1 1-1 1-2 3-3 4 0-1-1-1-1-2h-1z" class="I"></path><path d="M436 574c3 0 7 0 10 1 1 0 1 0 1 1h-1c-3-2-8 1-10-2z" class="b"></path><path d="M243 449h2v1c0 1-1 2-2 3h-1c-1-1-1-2-1-3v-1h2z" class="D"></path><path d="M412 501h1c0 1 1 2 1 3v1c-1 0-1-1-2-1s-2 0-3 1h0l-2 2-1-1 1-1c1-2 3-2 5-4z" class="B"></path><path d="M446 169c1-2 4-3 5-4h4c-1 2-4 2-5 3l-3 1h-1z" class="Y"></path><path d="M153 420c1 1 1 2 2 2-1 1-2 1-3 2h-3-1c0-1 0-1 1-2v-1c1 1 1 1 2 1s2-1 2-2z" class="X"></path><path d="M119 220h1v1 2c-1 1-2 3-3 4h-1 0c0-3 2-5 3-7z" class="E"></path><path d="M151 417c1 1 2 2 2 3s-1 2-2 2-1 0-2-1l-1-1c1-1 2-1 3-1v-2z" class="J"></path><path d="M609 471l1-1 2 2c-1 2-1 2-2 3h-1l-2-2c1-1 1-2 2-2z" class="M"></path><path d="M85 255h0c1 1 1 3 2 4h0v2c-1 1-1 2-1 3v-3h-1l-1-1v-1h0c0-2 0-2 1-4z" class="I"></path><path d="M336 250c2 0 7 1 9 2l-1 1c-3 0-5-1-8-1v-2z" class="c"></path><path d="M95 454h1l2 2c0 1 0 1-1 2h-2c-1 0-1 0-1-2 0-1 0-1 1-2z" class="K"></path><path d="M409 513h-2c-1 1-1 1-2 0v-2c1 0 1-1 2-1 0-1 0-1 1-2l1 1-1 1h2l-2 2 1 1z" class="q"></path><path d="M547 319l1-1 1 1c0 1 0 2 1 2v1c0 1 0 1-1 2h-1c-1 0-1 0-1-1v-4z" class="B"></path><path d="M523 383h2 1c1 1 2 1 3 2l-1 1h-1-3-1v-3z" class="E"></path><path d="M202 581v-3c1-1 1-2 2-2 1 1 1 1 1 2h1-1s0 1-1 1v2h3c-1 0-2 1-2 1v1c-1 0-1-1-1-1h-2v-1z" class="M"></path><path d="M192 592h4v2c-1 1-4 0-5 0s-1 0-2-1l1-1h1 1zm252-322h3l1 1c-1 1-1 2-2 3h0c-2 0-2-1-3-2 0-1 0-1 1-2z" class="F"></path><path d="M233 527c3 1 5 2 7 4l-1 1c-2 0-5-2-7-3l1-2z" class="B"></path><path d="M136 428h1c0 1 2 3 2 4l-2 1c0-1-1-1-1-1-1 0-2-1-2-1l2-3z" class="H"></path><path d="M429 251l1-1-1-1h1c1 0 2 1 2 2l2-1h1c0 1 0 2-1 3v1c-1-1-2-1-2-2h-2s0-1-1-1z" class="B"></path><path d="M164 563c1-1 2-1 2 0 1 1 2 1 2 2l-1 1c-1 1-1 1-2 1l-2-2c0-1 0-1 1-2z" class="O"></path><path d="M470 479v-1-1c-1-1-1-1-1-2s1-1 1-2c1-1 2-1 4-2l-2 2 1 1c-1 2-1 3-3 4v1z" class="E"></path><path d="M369 513c1 0 0 0 1-1v-9c0-1 0-3 1-3v-1l1 2c0 2 0 4-1 6v1 1h0v4h-2z" class="H"></path><path d="M454 203h-2l-4 1-1-3v-1l2-2 1 1c0 2 0 2 2 3 0 1 1 1 2 1z" class="E"></path><path d="M235 641c1-1 2 0 4-1 1 1 1 1 3 1 0 0 0 1 1 1-3 1-7 1-10 1l2-2z" class="R"></path><path d="M209 204c0 2-1 4-1 6l-3-3h-2l2-2h1c0 1 0 1 1 1l2-2z" class="D"></path><path d="M262 643c1-1 1-1 1-3h2c0 1 0 1 1 2l-1 1h0 0c1 0 2 0 2 1 1 0 2 0 2-1h2v1c-2 1-6 0-8 0l-1-1z" class="U"></path><path d="M190 604h6v1 2h-1-3c-1 0-1-1-1-1 0-1 0-2-1-2z" class="M"></path><path d="M524 386h3 1v1c-1 2-3 5-5 5h-1c1-1 2-2 2-3l1-2-1-1z" class="G"></path><path d="M377 493c2 1 6 3 8 5h0-6v-1h0c0-1-1-1-1-2s-1-1-1-2z" class="i"></path><path d="M128 412l-1-1h-2c0 1 1 1 1 1v1c-1-1-1-1-2-1v-1h-1 0c1 0 2 0 2-1h2 1c-1-1-1-1-2-1h-1-1v1c0-2 1-3 2-4 1 0 1 1 2 2s1 2 1 3l-1 1h0z" class="X"></path><path d="M566 347v2l-1 3c-1 2-1 3-2 4v-1h0v-1l-2 2c-1-2 0-3 0-5v2s0 1 1 2v-1-1-1s1-1 2-1h1c0-1 0-3 1-4z" class="b"></path><path d="M119 415c1 0 4 2 4 3v1 1c0 1 1 1 1 2v1 1c-1-1 0-2-1-3h-1v-1 1l-2 1v-1-1c0-1 1 0 2-1h-1v1c-1-2-1-3-1-5h-1z" class="I"></path><path d="M450 233l1-1v1c0 1 0 2-1 3 0 1-1 2-2 2l-1-1 1-1h-1l-1 1v-1c1-1 2-2 4-3z" class="b"></path><path d="M103 212h2l-3 8c-2 1-3 1-4 1h-1 2l1-1h-2c2 0 2 0 3-1v-2c1-1 0-1 0-1 0-1 2-1 2-2v-2z" class="E"></path><path d="M131 474c1-1 2-1 3 0 1 0 1 1 2 1l1 2 1 3h-1l-1-1h0c-2-1-4-4-5-5z" class="e"></path><path d="M111 394h2c1 2 0 5 1 7h1 1c1 1 0 1 0 2h0-2l-3-6h1v-3h-1z" class="E"></path><path d="M533 378c1-1 2-1 2-3l1 1h1c1 0 2 1 2 1-3 1-3 4-5 5h0c0-2 0-2-1-4z" class="B"></path><path d="M359 31h1v1c1 2 2 2 3 2h0l1 1h-3c-1 1-1 2-1 3v1l-1-1v-2c-1-1-1-1-2-1h0l-1-1h2c1-1 1-2 1-3zM208 49h1v2c1 2 2 2 3 3h0-1l-2 2h0l-1 1h0c0-2-1-2-3-3h0l2-2c1 0 1-1 1-3h0z" class="j"></path><path d="M428 639l1 1 3 3c-3 1-8 1-11 1h-1c0-1 1-1 2-1h0c1-1 2-1 2-2h0c2 1 2 1 2 2h2v-2-2z" class="X"></path><path d="M424 506c1 0 2 0 3 1 2 0 3 0 4-2h2v1c0 1-1 2-2 2h-1c0 1 0 1-1 2l-2-1h0v-1h-2l-1-2z" class="m"></path><path d="M395 642h0c2 0 3 1 4 1 0 1 1 1 1 2h0c-2 0-5-1-8 0-1 1-3 1-5 0h5c0-1 1-2 2-2v-1h1z" class="b"></path><path d="M485 455h1l1 1h1v1h1-1l-1 1c-1 0-2 0-2 1l-2 1h0l1-1v-1c-1 0 0 0-1 1h0-1l-1 1c-1 0-1-1-2-1 2-1 2-1 3-1l3-3z" class="a"></path><path d="M488 638l1 1 1-1c1 0 2 0 2 1l6 1c-4 0-9 1-13 0v-1c1 0 2 0 3-1z" class="F"></path><path d="M430 597c-1 0-1-1-1-2 2 0 4 0 6 1h-2-1v1 1h1 1l1 1c1 1 1 3 0 4-1-2-2-3-3-4s-2-1-2-2z" class="Y"></path><path d="M110 414h1 0c1 1 1 1 2 1v1c1 0 1 0 2 1l2-2v1h1v1c0 1 0 2 1 3 0 0 0 1 1 1h-1l-1-1c0 1 0 1-1 1l-1-1h0v1l-1-1c1-1 1-1 1-2h-2c0-1-1-2-2-2l-2-2z" class="I"></path><path d="M429 251c1 0 1 1 1 1 0 1-1 2 0 3l1 2c0 2-1 3-3 4l-1 2 1-2v-3l-1-1v-1c1-1 2-3 2-5z" class="R"></path><path d="M440 582h2c1 1 0 3 0 5h-1c-1 0-2-1-3-1v-3l2-1z" class="B"></path><path d="M472 492l2 1h1l-2 3v-1h-1-1c0 1-1 1-1 2h-2v1l-2 2v-2c0-1 2-3 3-3l3-3z" class="G"></path><path d="M442 639l1 1s1 1 1 2h1c-4 1-7 1-11 1l1-1 3-1 2-1 2-1z" class="B"></path><path d="M474 477l1 1c1 0 2 1 3 2 0 1-2 2-2 2 0 1 1 2 1 2v1c0 2-2 3-2 5-1 1 0 2 0 3h-1l-2-1 1-1c0-1 1-2 2-3 2-3 0-3 1-6v-1h1v-1c-1-1-1-1-2-1v1h1v1c-1 0-1 0-2-1 1-1 1-1 0-3z" class="I"></path><path d="M228 485h1 0v2h-5v-2 1h1c0-1 0-1 1-2l-1-1h-1 0-1v1h1l-1 1h-1v-2h0v-1c1 0 2 0 3 1h0c1 0 2 2 3 2z" class="a"></path><path d="M451 515c2-1 4-3 6-4 1-1 2-1 3-2 0-1 0-1 1-1h0l-1 1v1h1c-1 1-6 5-7 5s-2 0-3 1v-1z" class="F"></path><path d="M375 485h3v3 1l-1 1c-1 0-1 1-1 1v1l1 1h-1-1v-4-1h0-1v-1l1-2z" class="L"></path><path d="M171 640h6c0 1 1 1 1 2h-2c-1 0-3 1-5 1l-3-1 1-1h-1l3-1z" class="C"></path><path d="M169 641c1 0 3-1 4 0l1 1h2c-1 0-3 1-5 1l-3-1 1-1zm209 4c1-1 2-1 4-1 2-1 4 0 6-1h6c-1 0-2 1-2 2h-5c-3 0-6 1-9 0z" class="F"></path><path d="M557 342l1-3v-2-1h1c3 2 0 3 0 5h1 0c1-1 1-2 0-2 0-1 1-2 1-3l2 2c-1 1-2 1-2 2l1 1h-1v2c0 1-1 1-1 2h-1c-1-1 1-2 0-3h-2z" class="X"></path><path d="M159 211c0-1 0-1 1-2h1l2 3c1 1 1 2 3 3h-2-4c0-1-1-2-1-3v-1z" class="a"></path><path d="M550 280h1l3 3c0 2 0 6-1 8 0-1-1-2 0-3v-2l-3-3h0v-2-1z" class="E"></path><path d="M375 485l1-10v-1c1 1 1 1 1 3 1 2 0 5 1 8h-3z" class="P"></path><path d="M340 264h1 0c0-3 1-6 2-10h0l1 1c-1 2-1 4-1 6s1 2 0 4h0-1c-1 0-2-1-3 0h-1v-1h2z" class="m"></path><path d="M339 311c-1 0-3 1-4 1 0-1 1-1 1-2h5c-1 0-1 0-2 1h9 17c2 0 6-1 8 0h-22-12z" class="a"></path><path d="M413 269l1-1c0 1-1 2-2 3v4c-2 1-2 3-4 3l-2-2h0v-1c2 1 2 1 4 1v-2h0l-2-2c1-1 4-3 5-3z" class="B"></path><path d="M75 328v-6-6-11-4 10c0 1 0 3 1 4h0v-1h1v1l-1 1 1 1v1c0 2 1 4-1 5v1l-1 4z" class="Y"></path><path d="M384 664v1l1 1c0 1 1 2 2 2h2c-2 1-3 1-4 2-1 0-1 2-1 3v1h0c-1-1-1-2-1-4 0-1-2-1-3-2h1c2-1 2-2 3-4h0z" class="s"></path><path d="M339 265c1-1 2 0 3 0h1l8 3v1l-1 1h0v-1c-2-1-4-1-6-2v1 1h-2l-1-2-2-2z" class="J"></path><path d="M449 198c1 0 3-1 4-1s1 1 2 2c0 1 0 2-1 3v1c-1 0-2 0-2-1-2-1-2-1-2-3l-1-1z" class="C"></path><path d="M455 199c0 1 0 2-1 3v1c-1 0-2 0-2-1-2-1-2-1-2-3 1 1 1 2 3 1l1-1v1l1-1z" class="B"></path><path d="M395 570c2 0 4 0 5 1h2l-1 1h2l-2 1v1h0c-1 0-2 0-3 1-1 0-1 0-2-1v-1l-2-2 1-1z" class="C"></path><path d="M400 571h2l-1 1c-1 0-2 0-4 1v-2h0 3z" class="D"></path><path d="M397 573c2-1 3-1 4-1h2l-2 1v1h0c-1 0-2 0-3 1-1 0-1 0-2-1v-1h1z" class="X"></path><path d="M453 507c2-1 4-3 6-3 0 0-1 2-1 3-1 0-2 1-3 1v1h-1-1c-1 1-3 2-3 4h0c0 1 0 1 1 2l-1-1c-1 0-2 1-2 1l-2-1c1-1 4-3 6-4 1-1 1-2 2-3h-1z" class="B"></path><path d="M187 484l1-1c1 1 2 1 2 1l1 1h1c0-1 1-1 1-2v2h0c-1 0-2 0-2 1v1h-1c1 1 1 1 2 1 1-1 1-1 2-1 0-1 1-1 1-1l-1 1-1 1h-1-1-1 0-1c1 1 0 1 1 1l2 1h2 0 1 0c-1 0-1 1-2 1-3 0-6-3-8-6h2c1 0 1 1 2 1h1v-1h-2l-1-1z" class="I"></path><path d="M557 245h0c3 5 4 12 5 18h0l-1-1c-1-2-2-6-2-8v-2s-1-4-2-4c-2-1-2 0-4-1h1 2c0-1 0-2 1-2z" class="E"></path><path d="M240 562v-1-3h1 0v4h1 0l2 2c-2 0-5 0-6 1l-1 1s-1 1-1 2h0l-1 1c0-2 1-3 2-4l-2-1h0v-1h0l1-1c1 0 3 1 4 0z" class="h"></path><path d="M470 48h0v2c0 1 0 2 1 2 1 2 2 2 4 2h0c-1 0 0 0-1 1h-2c-1 1-2 2-2 3v2c0-1-1-3-1-4-2-1-3-1-5-2 2 0 3 0 4-1 2-1 2-3 2-4v-1z" class="s"></path><path d="M354 398h1c0-2-1-4 0-5 1 0 1-1 2 0v7 1 1c-1 0-2-1-2-2-1 0-1 0-2-1l-1-1h2z" class="F"></path><path d="M352 398h2c0 1 1 1 2 1l1 1v1 1c-1 0-2-1-2-2-1 0-1 0-2-1l-1-1z" class="U"></path><path d="M199 647h3c0 1 1 2 1 3s-1 2-2 2c-2 1-2 0-3-1s-1-2-1-3c1-1 1-1 2-1z" class="e"></path><path d="M112 371c0-1-1-2-1-2h2 0 1c1 0 1 1 2 1 0 1-1 2-1 3s1 1 2 2c-1 0-2 0-2-1h-1c0 1-1 1-1 1v1h2c-1 1 0 1-1 1h-1v1h0c-1 0 0 0-1-1s-2-1-3-2l-2 2 1-2c1-1 2 1 4 1v-2h2v-1l-1-1v-1h-1zm416-5h-1v3 2l-1-1v-1-4h0l-1-1v1l-1-1c0-2 3-4 3-6 1-1 1-1 1-2v3l1 1v1 1l-1-1h-1c0 2 1 3 1 5z" class="a"></path><path d="M401 574h4 1c1 0 1 1 2 2 0 0 0 1-1 1h-3v2h-3l-1-2h-2c2 0 5 0 6-1-1 0-2 0-2-1l-1-1-1 1h-2c1-1 2-1 3-1z" class="E"></path><path d="M400 577c1 0 3 1 4 0v2h-3l-1-2z" class="W"></path><path d="M225 499c2-1 3 1 4-2 2 0 1 2 2 0 1 0 1 0 2-1h1c1-1 0-1 2-1 1 0 1 1 1 1 0 1-1 2-1 2-2 1-3 1-4 1-2 1-3 1-5 2v-1c-1-1-1 0-2-1z" class="E"></path><path d="M151 147h-1v-1c1-2 3-3 4-4s4-2 5-1l1 1c-1 0-2 1-3 2-2 1-2 1-3 3l-1 1-2-1z" class="Y"></path><path d="M357 251h1v7h0v1l-5-7v-1h0 1 3z" class="M"></path><path d="M357 251v2h-1c-1 0-2 0-3-2h0 1 3z" class="O"></path><path d="M445 508l2-1h2l1-1h1 0v1h-1c-1 0-2 0-3 1h0l1 1 2-1c1 0 1-1 2-1l-5 4-4 2c1 0 1-1 2-2h1-2v-1-1l-2 2c0-1-1-1-1-1-1-1-1-1 0-1 0-1 1-1 1-1h0c1 1 1 0 2 0h1z" class="q"></path><path d="M445 508c1 1 1 1 1 2h-1c-1 0-1-1-1-2h1z" class="I"></path><path d="M145 435c1 1 1 1 2 1h2c1 1 2 1 3 2l1 1c0 1-1 1-1 2l-2 1-1-1c-1 0-1-1-2-1v-1c-1-1 0-1-1-1 0-1 0-2-1-3z" class="E"></path><path d="M149 441v-2c1 1 1 1 2 1l1 1-2 1-1-1zm263 200v1h2c0 1 0 1 1 2v1h-9-3l3-3h1c1 0 1 0 2 1h1v-1h1l1-1z" class="B"></path><path d="M412 641v1h2l-2 2c0-1-1-1-1-2l1-1z" class="E"></path><path d="M414 642c0 1 0 1 1 2v1h-9c2-1 5 0 7-1h0-1l2-2z" class="Y"></path><path d="M181 218s1 0 2-1c0 1 0 1 1 2h0l1-1h1v1c0 1 0 2 1 2l-2 1h-3c-2-2-2-1-4-1 0 0-1-1-2-1l1-1 1-1 2 2h0v-2h1z" class="E"></path><path d="M141 424h1l1 2h1l-1 1h-1c1 1 0 1 1 1 1-1 1-1 1-2v1h2v1l-2 2h-2v-1l-1 1h-1l-1-1 1-1c-1 0-1-1-2-1h0v-1h2c0-1 0-1 1-2z" class="Y"></path><path d="M141 424h1l1 2h-1-1v-2zm310 177v3h1v-3c1-2 1-4 2-5v5 2c0 1 1 1 1 1 1-2 0-3 0-5v-4h1v8 2c0 1 1 1 1 3h-2v-3h-2 0-2 0c-1-1-1-3 0-4z" class="B"></path><path d="M449 482h1 0c-2 2-3 2-4 4v1c-1 1-2 1-2 2-2 1-3 1-4 0l-2 1v1h-1v-1l6-4c1-2 3-3 6-4z" class="E"></path><path d="M108 345h0l1 1-1 1v1c-1 1-1 1 0 2h1l-1 2h-1v1c0 1 0 2-1 3l-1 1-1-1h0v-4c0-3 2-5 4-7z" class="X"></path><path d="M221 523h3s0 1 1 1h1 1l6 3-1 2c-1-1-2-1-3-1s-1 1-2 1v-1h0c0-1-1-1-1-2-2 0-3-1-4-2l-1-1h0z" class="G"></path><path d="M477 484l1-1h0c1-1 1-1 1-2h0v-1c1 1 2 1 3 1h0l-3 3c0 1 1 1 1 2 0 0-1 1-1 2v2l-3 1h0c0 1-1 1-1 2 0-1-1-2 0-3 0-2 2-3 2-5v-1z" class="S"></path><defs><linearGradient id="A" x1="147.18" y1="485.016" x2="137.716" y2="485.093" xlink:href="#B"><stop offset="0" stop-color="#ada7aa"></stop><stop offset="1" stop-color="#bec1be"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M136 479h0l1 1h1 1l8 8 1 1v1c-1 0-1 0-1 1h0l-11-12z"></path><path d="M418 512c0-1-1-2-1-2h-2v-1 1l8-1h4l2 1s-1 1-2 1h-1c0 1-1 1-1 1v1l-1-1v1h-1v-1h-1c-1 1-1 0-2 0l-1 1-1-1z" class="B"></path><path d="M430 147h3v1c-1 0-1 1-1 1l1 1h-1v2c0 1-1 0-2 1l-1 1h-1v-1c1 0 1 0 1-1h0v-1l-1-1-5 3-1-1c1 0 1-1 1-1 3-1 4-3 7-4z" class="q"></path><path d="M186 585c3-4 6-7 10-8l6 1h-5c-4 2-6 5-9 8l-2-1z" class="T"></path><path d="M203 207h2l3 3c-1 3-3 5-4 8 0-2 1-5 1-6h0c-1 0-2 0-3-1h-1v1c0-1 0-1-1-2v-2h1 1l1-1h0z" class="G"></path><path d="M203 207h2l-4 4v1c0-1 0-1-1-2v-2h1 1l1-1h0z" class="U"></path><path d="M477 382c-1-1-1-1-1-2s-1-2-1-3v-2-3-6s1-3 1-4c1-1 0-6 0-7l1-2h0v2 2 4l-1 5v7c0 2 2 7 1 9z" class="C"></path><path d="M463 612v3h2v-1l1 2v1c0 2 0 4 1 6v1l-1-1c-2 0-3 1-4-1 0-2 1-4 1-6 0-1-1-3 0-4z" class="e"></path><path d="M377 493h0c0 1 1 1 1 2s1 1 1 2h0v1h-11c2-2 4-4 8-5h1z" class="h"></path><path d="M471 495h1 1v1l-2 3v2c0 1-1 1-2 2s-2 2-3 2c-2 1-4 3-4 5h-1-1l1-1 3-3c0-1 1-2 1-3 0 0 1 0 1-1 1 0 0-1 0-2 1 0 2-2 3-2 1-1 1-1 2 0v-1l1-1-1-1z" class="b"></path><path d="M469 503v-2h-1v-1h0c1 0 1 0 2-1h1v2c0 1-1 1-2 2z" class="C"></path><path d="M184 592l-1-1 3-6 2 1-2 3c3 1 7 0 10 1v1c-1 1-3 0-4 1h-8z" class="L"></path><path d="M190 471l2 2c0-1 0-1 1-1h2c0 1 1 1 1 2h0l2 2h1c1 0 1 1 2 1-1 1-1 1-2 1l-1-2h-5l-1 1h-2v-1-1c-1 0-1 0-2-1h0l2-1h0v-2z" class="Y"></path><path d="M192 473c0-1 0-1 1-1h2c0 1 1 1 1 2h0-3 0v-1h-1z" class="F"></path><path d="M463 638h1c2 0 2 0 2 1h2v-1c1-1 1-1 2-1v1 1c1 0 2-1 4-1 1 1 2 1 4 1l1-1v1c-1 0-1 1-2 2 0 0-1-1-2-1-5 0-10 1-14 1v-1l2-2z" class="I"></path><path d="M453 507h1c-1 1-1 2-2 3-2 1-5 3-6 4-1 0-3 1-4 2-1 0 0 0-1 1h0l-1 1h0v-1l-1-1c-1 1-1 2-2 2l-1 1c1-1 1-1 1-2v-1c1 0 1 0 2-1 1 0 3-1 4-2l4-2 5-4h1z" class="C"></path><path d="M80 324c1 1 1 1 2 3v2h0c1-2 1-5 1-7h-1v-2h0c-1-1 0-2 0-3l-2-2v-3c-1 1-1 0-1 1h-1c0-1 1-2 1-3-1 0-1 1-1 2l-1 1v1l-1-1s0-1 1-1v-1c0-1 1-2 1-2h1 0c0 1 1 3 1 4 1 1 1 2 2 2v4c0 1 1 2 1 3 1 2 0 4 0 6 0 3 1 6 2 9v1h-1c0-1-1-2-1-3-1 0-2 0-2-1l1-1v-3c-1-1 0-1-1-2 0-1 0-3-1-4z" class="X"></path><path d="M538 368h0c2-2 3-4 4-6 0 2 0 4-1 5 1 2 1 2 1 3l-1 1h-1l-1 1v2l-1 1-1 1h-1l-1-1 1-1v-1h0-1v-1c1-1 1-1 2-1v-1s1-1 1-2h0z" class="I"></path><path d="M536 374s1 0 2 1l1-1-1 1-1 1h-1l-1-1 1-1z" class="G"></path><path d="M422 635v1 1c1 1 3 1 4 1h1s1 0 1 1v2 2h-2c0-1 0-1-2-2h0c-2-1-3-1-5-2l-2-1 1-1h2v1-2l2-1z" class="B"></path><path d="M427 638s1 0 1 1v2c-1-1-2-1-3-2l1-1h1z" class="a"></path><path d="M188 217l1-1h2c1 1 0 0 1-1h3 2 1c-1 2-2 2-3 3s-2 2-3 2h-1-1c0 1-1 1-2 0l-1 1c-1 0-1-1-1-2v-1h-1v-1l2 1 1-1h0z" class="B"></path><path d="M191 220c0-1 0-1 1-2h3 0c-1 1-2 2-3 2h-1z" class="I"></path><path d="M188 217l1-1h2c1 1 0 0 1-1h3 2c-1 1-2 1-3 2 0 0-1 0-1 1h-2v1c-1 0-1 0-1-1-1 0-1 0-2-1z" class="G"></path><path d="M562 331v-1c0-3 1-7 1-10h0 1v1 9c1 0 0 1 0 2 0 2 1 5 0 7 0 2 1 2 1 4l-2 1c0 1 1 1 0 2h1c0 1 0 1-1 1h0c-1 1-1 2-1 3v1c-1 1-1 1-1 2v-2-1c0-2 1-2 1-3s0-2 1-4l1-1h-1 0c0-1 1-2 1-2 0-1-1-1 0-2v-1c-1-2 0-4-1-6h-1z" class="B"></path><path d="M78 338c1 1 1 2 2 2v2c0 2 1 2 2 3 0 2-1 3-1 5h1c1 0 1 1 1 2 1 0 1 1 2 2h-1-1l1-1c-1 0-1-1-1-1s0-1-1-1c-1-1-1-1-2-1 0 1 1 2 0 3h0l1-1 1 1h0v1c0 1 0 1-1 2l2 2h0c0 1 1 1 1 1l-1 2-1-3c0-1 0-1-1-1l-1-1s1 0 1-1v-2c-1 1-1 1-1 2h-1c0-2 1-4 1-5-1-4-2-8-2-12z" class="I"></path><path d="M200 207h1v1h-1v2c1 1 1 1 1 2h0c-1 0-2 1-2 2-1 0-1 0-1 1h-1-2-3c-1 1 0 2-1 1h-2l-1 1h0-1l1-1 1-1c1-1 1-2 1-3h0c1 0 1 0 1 1h0 0c1 1 2 0 3 0h0c1-1 1-1 2-1h3v-1h-2c0-1 1-1 1-2 1 0 1-1 2-2z" class="H"></path><path d="M455 589c1 1 1 2 1 3h0c-1 1-3 1-5 1-4 0-8-1-12 0h0v-1-2c1-1 2-1 3-1v1c2 1 4 1 6 1h1c2-1 4-1 6-2z" class="F"></path><path d="M455 589c1 1 1 2 1 3h-3-3l-1-1c2-1 4-1 6-2z" class="W"></path><path d="M83 361c2 1 2 0 3 1v1c-1 0-1 0-1 1-1 1 0 2-1 2 1 1 0 1 1 1 1 1 2 1 2 2 0 0 1 1 1 2h1v-1l1-1c-1 0-1 0-2-1h0v-1c0-1 0-1 1-2h0l1 3h0c0 1 1 2 1 3 1 2 1 4 2 6h-1 0c-2 0-2 0-3-2h3l-1-1h-1l-2-2h0l-1-1c0 1 0 2-1 2 0-1 0-2-1-2v-1l1-1v-1h-2v-2-2l-1-3z" class="B"></path><path d="M544 370l2-1c0 1 1 2 1 3-1 0-2 1-3 2l-2 2v3 1h-1c-1-1-2-2-2-3 0 0-1-1-2-1l1-1 1-1v-2l1-1h1c1 0 2 0 3-1z" class="b"></path><path d="M539 372l1 1v1 3l2 2v1h-1c-1-1-2-2-2-3 0 0-1-1-2-1l1-1 1-1v-2zm5-2l2-1c0 1 1 2 1 3-1 0-2 1-3 2l-2 2c-1 0-1 0-1-1s1-2 1-3h1l1 1c0-1 0-1 1-2l-1-1z" class="C"></path><path d="M352 383c1 0 1 1 2 1h0v1c0 1 0 2 1 3 0 0 1 1 2 1h0c1 1 2 0 3 1v1c-2 1-9 0-12 0v-1c2 0 5 0 7-1-2-1-4 1-6 0l-1-1h0 2 2c-1-2 0-4 0-5z" class="p"></path><path d="M335 246c2 1 4 1 6 1h4v5c-2-1-7-2-9-2h-2l1-4h0zm145 386c2 1 4 4 6 5 1 0 2 1 2 1-1 1-2 1-3 1h-1-2l-1-1c-1 1-1 1-2 1v-1l-1 1c-2 0-3 0-4-1 0-1 1-1 2-1s1 0 2-1h3 0v-1h-2-1v-1l2-2z" class="B"></path><path d="M486 637c1 0 2 1 2 1-1 1-2 1-3 1h-1-2l-1-1h2c1 0 2-1 3-1z" class="R"></path><path d="M538 352c1-1 2-3 2-4-2-3-3-4-6-6h0l-2 1c-1-1-1-1-1-2 1-1 3-1 4-1s1 0 2-1c1 1 1 1 2 1l-1 1c0 1 0 1 1 2v1l1 2h2l-1 2c1 1 1 1 1 2h-1v2 1c-1-1-1-1-3-1z" class="I"></path><path d="M446 596c2-1 3 0 5 0h0v5c-1 1-1 3 0 4h-2-1l-1-1v-2l-1-1h0v-1l-1-1v-3h1z" class="B"></path><path d="M446 596c2-1 3 0 5 0h0-1c-1 3-1 5-1 8l-1-1v-6-1h-2z" class="q"></path><path d="M449 604c0-3 0-5 1-8h1v5c-1 1-1 3 0 4h-2v-1z" class="G"></path><path d="M128 412h0v1h0 2c1 2 2 2 2 4 0 1-1 1-1 2 1 0 1 1 1 1 0 1 0 2-1 3v1h0l-1-1c-1 1 0 3-1 3h-1c1-1 0-2 0-3l-2-2v-1h0 0 0c1-1 1-1 2-1v-1h1c-1-1-1-1-1-2s0-1-1-2c0-1 1-2 1-2z" class="I"></path><path d="M126 420h2 1c0 1 1 1 2 2h0l-3 1-2-2v-1z" class="X"></path><path d="M337 264h3-2v1h1l2 2 1 2-1 4c2 2 3 2 4 3-1 0-6-1-7-1-1-2-1-4-1-6h0v-5z" class="r"></path><path d="M337 264h3-2v1h1l2 2 1 2-1 4c-1-1-1-2-1-4l-1-1c-1 0-1 0-2 1h0v-5zm208 136l-1 1h0-1 0l-2-2v-2h1c0-1 1-2 1-2-1-1-1-1-1-2s0-1 1-1h1 1c-1-1-1 0-1-1h2l-1-1v-1l1-1h0c0-1 0-1 1-2h0c-1 2 0 1 0 3h-1l1 1h1 0l-1 1h-1c0 1-1 1-1 2v1h0 0c1-1 2 0 3 0v1l-1 1v1c-1 0-1 0-1 1 0 0 0 1-1 1v1z" class="a"></path><path d="M545 400h-1c-2 0-2 0-3-1 1-2 1-2 2-3 2 0 2-1 3-2h1v1c-1 1-1 3-2 4h0v1z" class="D"></path><path d="M552 356l1-1c1 2 0 2 0 4v1 1c-1 1-1 1-1 2-1 0-1 0-1 1h0v1c-1 0-1 0-1 1 0 0 0 1-1 1l1 1h0l-1 1-2 2v1c0-1-1-2-1-3l-2 1c-1 1-2 1-3 1l1-1c1 1 2-1 4-2h0l1-1c0-1 0-1 1-2v-2c1 0 2-1 2-2h-1-1v-1l1-1 2 1c0-2 0-2 1-4z" class="E"></path><path d="M552 356l1 1c-1 1-1 2-1 3l-1 1h-1l1-1c0-2 0-2 1-4z" class="X"></path><path d="M546 369c2-1 1-5 5-6-1 0-1 1-1 1v1c-1 1-1 2-1 2v1l-2 3v1c0-1-1-2-1-3z" class="B"></path><path d="M110 236h1 0c-1 1-2 2-2 4v4c1 0 2-1 2-2h0c1 0 1 0 2 1v1c-1 1-1 3-2 4s-3 2-4 3l-1-2c0-2 0-3 1-5h0c0-2 2-6 3-8z" class="X"></path><path d="M92 377h1l1 7v2h0v3s1 1 1 2v1h-1v-1c-1 0-1-1-2-1h0l-1 1c0-2 0-3-1-5h0c0-2-1-3-1-4v-2c2-1 3-1 3-3z" class="b"></path><path d="M90 386l1 2h1c1-1 1-1 1-2h1v3s1 1 1 2v1h-1v-1c-1 0-1-1-2-1h0l-1 1c0-2 0-3-1-5z" class="U"></path><path d="M226 526c0 1 1 1 1 2h0v1c1 0 1-1 2-1s2 0 3 1c2 1 5 3 7 3l-1 1c-2 0-4 0-5 1h-1c-2-1-5-2-7-4v-1-1c0-1 0-1 1-2z" class="q"></path><path d="M241 550h0c1 0 1 1 1 2 1 1 1 1 0 2h1 0v2c-2 0-2 0-3-1-1 2-1 2-1 4v2l1 1c-1 1-3 0-4 0-1-3 0-6 0-9l2-2h1c0 1 0 2 1 3h0c1-2 1-3 1-4z" class="M"></path><path d="M110 365c0-1 0-1 1-1l1 1v1c1 0 1 1 2 1v-1c-1-1-1-1-1-2v-3h0c1 1 1 1 1 2v1c0 1 0 2 1 2 0 1 1 1 1 1v1 1c1 0 2 1 2 1 1 0 1 0 1 1v3c-1 1-1 1-2 1-1-1-2-1-2-2s1-2 1-3c-1 0-1-1-2-1h-1 0-2s1 1 1 2l-2-2h-2s0-1-1-1l1-1 1 1c1-2 1-2 1-3z" class="Y"></path><path d="M448 612l1-1c0-1 0-1 1-1h7c1 1 0 4 0 5h-2-5-5v-1-1c1-1 0-1 0-2h1l1 2h1v-1z" class="a"></path><path d="M450 615c0-1 1-2 2-2l1 1h0c0 1 2 1 2 1h-5z" class="r"></path><path d="M497 441l4 3v-1 2c-1 1-2 1-3 1 0 1 1 1 2 1s2 0 3 1l-1 1-1 1v1 1 1l-1-1h-1l-1-1 1-1-2-1c0-1 0-2-1-2 0-1-1-1-2-1 0 0 0 1-1 1v1h0c-1-1-2-2-3-2l3-3c1 0 2 1 3 1h1v-2-1z" class="U"></path><path d="M500 447c1 0 2 0 3 1l-1 1-1 1h-1c0-1 0-1-1-1l-1-1 2-1z" class="B"></path><path d="M456 437h2c1-1 0 0 0-1h0-4v-1h0v1h-2v-1h1v-1h2 0 0 3 1v1-1h1 1 1 0 2 0c2 0 3 1 5 1 3-1 4 1 6 2h-7-12z" class="b"></path><path d="M78 323h0c1 0 1-1 1-1 1-1 1-1 1-2v-1h0 1 0v2c0 1-2 2-2 2l1 1c1 1 1 3 1 4 1 1 0 1 1 2v3l-1 1c0 1 1 1 2 1 0 1 1 2 1 3h1l-1 1c-1-1-1-2-2-3h-1c0-1 0-1-1-1l-1 1v1 1c0 1 1 2 1 2-1 0-1-1-2-2 0-2 1-3 0-5-1-3-1-2 0-4 1-1 1-2 1-2-1-1-1-1-1-2-1-1 0-1 0-2z" class="E"></path><path d="M499 322c1 0 2-1 3-1 1 1 2 1 3 2v1 3c-1 1-1 2-3 2 0 0 0 1-1 1 0 0-1 1-2 1-1-1-1-2-1-3 0 0 1-1 1-2s0-1-1-2l-1 1h-1c1-2 2-2 3-3z" class="I"></path><path d="M499 322c1 0 2-1 3-1 1 1 2 1 3 2v1l-1 1s-1 0-1 1h-2l-2-2v-2z" class="B"></path><path d="M427 256v1l1 1v3l-1 2-2 1h0c0-1 1-2 1-3h0c-1 1-2 1-2 1-3 0-6 5-8 5l3-3c0-1 0-1 1-2-1 0-2-2-3-2h-2 0-1-2s1-1 2-1h1c1 0 2-1 3-1h1c1 1 2 1 3 1 1-1 1 0 2 0l3-3z" class="N"></path><path d="M344 268v-1c2 1 4 1 6 2v1h-2c1 1 0 1 1 1v5h0c-1 1-2 1-3 1l-1-1c-1-1-2-1-4-3l1-4h2v-1z" class="c"></path><path d="M344 268v-1c2 1 4 1 6 2v1h-2c1 1 0 1 1 1 0 1 0 1-1 2-2 0-3-1-4-3v-2h0z" class="r"></path><path d="M402 571c1-1 2-1 3 0h2 1l2 2c0 1 1 2 1 3h1l1-1c1 0 1 0 1 1v1c-1 1-2 0-3 0v1c-1 1-1 2-2 4v-1c-1-1-1-2-1-2h-4v-2h3c1 0 1-1 1-1-1-1-1-2-2-2h-1-4 0v-1l2-1h-2l1-1z" class="U"></path><path d="M402 571c1-1 2-1 3 0h2 1l-1 1h-6l1-1z" class="d"></path><path d="M408 571l2 2c0 1 1 2 1 3h1l1-1c1 0 1 0 1 1v1c-1 1-2 0-3 0v1c-1 1-1 2-2 4v-1c-1-1-1-2-1-2l1-1v-1c0-1 0-2-1-4 0 0-1 0-1-1h0l1-1z" class="X"></path><path d="M415 509l2-2c2-1 2-1 2-3 3-5 1-10 3-15 0 5 0 9 1 13v1c1 1 1 2 1 3l1 2h2v1h0-4l-8 1v-1z" class="V"></path><path d="M423 503c1 1 1 2 1 3l1 2h2v1h0-4 2v-1c-1 0-2 0-3-1l-1-1c1-1 1-2 2-3z" class="K"></path><path d="M161 209c2 0 3 1 5 1v1 1l1-1c2 2 4 3 5 4h1c1 0 2 1 3 1v1c1 0 1 0 2-1v1c1 0 2 0 3 1h-1v2h0l-2-2-1 1-1 1c-1-1-3-2-5-2-1 0-2-1-2-2-2 0-2 0-3-1-2-1-2-2-3-3l-2-3h0z" class="B"></path><path d="M163 212h2v1h1l2 2 1 1c-2 0-2 0-3-1-2-1-2-2-3-3z" class="E"></path><path d="M184 592h8-1-1l-1 1-1 2v1c1 0 1-1 3 0v2c2 0 5-1 6 0v1 1l-1-1c-1 1-1 2-3 2h-2l-1-1h0v2 1 1h-2 0-3c0-1 1-1 2-1 0-2 0-3-1-5v-1-1c0-2-1-3-2-4z" class="C"></path><path d="M197 599v1l-1-1c-1 1-1 2-3 2h-2l-1-1h0v2 1 1h-2 0v-5c3 0 6 1 9 0h0z" class="E"></path><path d="M99 404v2 1 1h0v1l2 3 1 1h-1l2 2v-1h0-1c0-2-1-3 0-5h1c1 1 1 2 1 3l-1 1h0 2 0v-2h0c1 1 1 2 1 3 0-2 0-2 1-3h0v-1h0c1 1 1 1 1 2v1h0v2c1 0 1 0 1-1h1l2 2v1h0c1 1 1 1 2 3-1 0-1 0-2 1h-1c-1-2-2-3-3-4v-1l-1 1v1h0l-2-1h-1c0-1 1-1 1-2v-1c-1 0-2 2-2 2-3-3-4-7-6-10v-2h2z" class="X"></path><path d="M440 605v-10c1 0 3 0 4 1v7h1l1-2 1 1v2l1 1c-1 0-1 1-1 1-1 0-1 0-2 1h4 0-10c0-1 0-1 1-2z" class="U"></path><path d="M446 601l1 1v2l1 1c-1 0-1 1-1 1-1 0-1 0-2 1l-2-2v1h-1c-1-1-1-1-1-2l1-1h2 1l1-2z" class="b"></path><path d="M443 605h2l2-1 1 1c-1 0-1 1-1 1-1 0-1 0-2 1l-2-2z" class="n"></path><path d="M562 331h1c1 2 0 4 1 6v1c-1 1 0 1 0 2 0 0-1 1-1 2h0 1l-1 1c-1 2-1 3-1 4s-1 1-1 3v1c0 2-1 3 0 5l2-2v1h0v1 1h-1c-1-1-1-1-2-1 0-1 0-3-1-4l1-1-1-5-3 4c0-3 0-5 1-8h2c1 1-1 2 0 3h1c0-1 1-1 1-2v-2h1l-1-1c0-1 1-1 2-2l-2-2 1-5z" class="F"></path><path d="M229 634h2 0c-2 1-3 2-4 3h-1l-3 2v1h1 3 0v2h1c1-1 1-1 3-1h4l-2 2c-2-1-6 0-8 0-2-1-5-1-7-1 1 0 2-1 3-2-1 0-4 1-6 1l-1 1h-5c1 0 2-1 2-1h1s1 0 2-1c1 0 2-1 3-1 2-1 2-1 3-2 2 0 2-1 3-2 2 0 4 1 6-1z" class="D"></path><path d="M229 634h2 0c-2 1-3 2-4 3h-1c-1 0-2 1-4 1-1 0-1 0-2-1 2 0 2-1 3-2 2 0 4 1 6-1z" class="J"></path><path d="M391 540c1 1 1 1 1 2h-2l-1 1-2 1v-2l1-1h-6l1 1-1 1h1c1-1 2-1 3-1v2c0 1-2 1-2 2v1h2 0l1-1h-2l1-1 2 1v1h2 1l1 1c-1 0-1 1-1 1v1c-2-1-5-2-6-1h-6c0-1 1-1 1-2v-1l1-2c-1-1-1-2 0-3h3l-1-1h6 2z" class="X"></path><path d="M383 543h2l1 1-1 1h-1l-2-2h1z" class="r"></path><path d="M389 540h2v1h-7l-1-1h6z" class="c"></path><path d="M380 546h1 2 0c2 2 5 2 8 2v1 1c-2-1-5-2-6-1h-6c0-1 1-1 1-2v-1z" class="m"></path><path d="M407 635h2c0 1 1 1 1 2l2-1c2 0 3 1 5 2l2 1c2 1 3 1 5 2 0 1-1 1-2 2h0c-1 0-2 0-2 1l-5 1v-1c-1-1-1-1-1-2h-2v-1c-1 0-2-1-2-2h0l-1-1v1l-1-3-1-1z" class="F"></path><path d="M409 638h1c1 1 2 2 3 2h2v1c-1 0-2 0-3 1v-1c-1 0-2-1-2-2h0l-1-1z" class="B"></path><path d="M417 639h2c2 1 3 1 5 2 0 1-1 1-2 2h0v-1c-1-1-2-1-2-1-1-1-2-1-3-2z" class="a"></path><path d="M410 637l2-1c2 0 3 1 5 2l2 1h-2-3c-1 0-3-2-4-2z" class="I"></path><defs><linearGradient id="C" x1="402.704" y1="644.06" x2="401.041" y2="638.075" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#504f4f"></stop></linearGradient></defs><path fill="url(#C)" d="M408 636l1 3v-1l1 1h0c0 1 1 2 2 2l-1 1h-1v1h-1c-1-1-1-1-2-1h-1l-3 3h-3 0c0-1-1-1-1-2-1 0-2-1-4-1h0v-3s1-1 2-1v1l1 1c1 0 1-1 1-1h2l-1-2h2l-1 2h1 2 0l1 1 1-1v-1l1-2 1 1v-1z"></path><path d="M407 636l1 1v2h-2v-1l1-2z" class="D"></path><path d="M409 638l1 1h0c0 1 1 2 2 2l-1 1h-1c-1-1-1-1-1-3v-1z" class="I"></path><path d="M195 466c1 0 2 0 3 2 1 0 2 1 4 1l-1 2c1 0 1 0 2 1 1 0 3 2 4 3h2v1 1h2 1c0 1-1 1-1 1-1 0-2-1-3-1v-1l-1 1h-3l1-2-1-1c-1-1-3-2-5-2l-1 1h0-1c0 1 0 1 1 1 0 1 1 1 1 2h0-1l-2-2h0c0-1-1-1-1-2h-2c-1 0-1 0-1 1l-2-2h0l1-1v-1c2-1 3-2 4-3z" class="b"></path><path d="M195 467h1c0 2 0 1-1 2h-1c0-1 0-1 1-2z" class="X"></path><path d="M191 470h2v-1c1 0 1 1 2 1v2h-2c-1 0-1 0-1 1l-2-2h0l1-1z" class="C"></path><path d="M339 300h1v-2h1 2c2 1 2-1 4 1h1v-1c1 0 1 1 2 2 0 1 0 2-1 3v1c0 1 0 2-1 3h0-1c0-1-1-1-2-2h0c-4-1-8-1-12-1 2 0 5 0 6-1v-3z" class="H"></path><path d="M345 305c1 0 1-1 2-1h2c0 1 0 2-1 3h0-1c0-1-1-1-2-2h0z" class="D"></path><path d="M341 298h2c2 1 2-1 4 1h1v-1c1 0 1 1 2 2 0 1 0 2-1 3 0-1 0-2-1-3h-1v1 1l-1-1s0-1-1-1h0l-1 1h-1c-1-1 0-1 0-2v-1c-1 1-1 1-1 2-1 0-1-1-1-2z" class="G"></path><path d="M387 638h1 0c1 1 3 0 4 0l1 1h1 1v3h-1v1h-6c-2 1-4 0-6 1-2 0-3 0-4 1h-2s0-1 1-2h0c1 0 1-1 1-1 1 0 2-1 3-1l1-2-1-1h1c0 1 1 1 2 1s2 0 3-1z" class="W"></path><path d="M387 638h1 0c1 1 3 0 4 0l1 1h1 1v3h-1v-1c-3-2-5-1-8-1l-1 1s-1 0-1-1v-1c1 0 2 0 3-1z" class="C"></path><path d="M404 366c8 4 15 11 19 18-3-1-6-5-9-7-5-3-11-5-17-7 1-1 2-1 3-1 2 0 3 2 5 1h0l1-1h2l-2-1-1-1-1-1z" class="D"></path><defs><linearGradient id="D" x1="330.149" y1="270.143" x2="337.676" y2="262.278" xlink:href="#B"><stop offset="0" stop-color="#28292a"></stop><stop offset="1" stop-color="#444242"></stop></linearGradient></defs><path fill="url(#D)" d="M331 254v-1h1c0 1 0 3 1 4h1v1l1-1c0-1 0-2 1-3v-1l1 1h0v10 5h0c0 2 0 4 1 6-1 0-1-1-2 0h-3v-1-2-8c0-2 0-5-1-6h-1v-4z"></path><defs><linearGradient id="E" x1="339.331" y1="261.472" x2="332.353" y2="268.563" xlink:href="#B"><stop offset="0" stop-color="#686b6b"></stop><stop offset="1" stop-color="#837e80"></stop></linearGradient></defs><path fill="url(#E)" d="M335 257c0-1 0-2 1-3v-1l1 1h0v10 5h0c0 2 0 4 1 6-1 0-1-1-2 0 0-1 0-1-1-2 0-2-1-14 0-16z"></path><defs><linearGradient id="F" x1="206.853" y1="527.708" x2="199.59" y2="530.518" xlink:href="#B"><stop offset="0" stop-color="#5f5e5f"></stop><stop offset="1" stop-color="#767676"></stop></linearGradient></defs><path fill="url(#F)" d="M190 521c2 0 3 1 4 2h0l5 3c2 1 3 1 5 2h2l1 1c1 0 1 0 2 1 0 1 0 2 1 2h0 0c0-1 0-1 1-2h1l1 3v3l-1-2-1-1h0l-1 1h1c0 1 0 2-1 2v-2c-4-1-9-3-13-6l-5-3h1c-1-1-1-2-2-3l-1-1z"></path><path d="M190 521c2 0 3 1 4 2h0c2 2 4 3 4 5h-1l-5-3h1c-1-1-1-2-2-3l-1-1z" class="N"></path><path d="M493 456h0c0 2-1 2-2 3 0 1 0 1 1 2-2 1-4 3-6 4-2 0-4 2-6 3l-1 1v2 2h-4l-1 4c-1 1-2 1-3 2h-1v-1c2-1 2-2 3-4l-1-1 2-2 7-6c3-2 5-4 7-6 2-1 3-2 5-3z" class="B"></path><path d="M479 469v2 2h-4c1-1 2-3 4-4z" class="C"></path><path d="M466 616c0 1 1 2 1 3l2 2h0c2 2 4 4 5 6l1 1c-1 1-2 0-3 1-2 0-5 1-7-1h-2c-1-1-2-1-2-2l-2-1h2c0-1 0-1 1-2v-1c1 2 2 1 4 1l1 1v-1c-1-2-1-4-1-6v-1z" class="E"></path><path d="M469 621c2 2 4 4 5 6h-2c-1-1-2-2-2-3-1-2-1-2-1-3z" class="B"></path><path d="M100 360l1-1c0-2 1-4 2-6v2c-1 1-1 2-2 4h1 1l1-1s1 0 1 1h1 0 1l-1 1h1 2c0-2 0-2 1-3 0-1 0-1 1-2l1 1h0l-2 2c1 0 1 0 2-1h0c1 0 2 0 2 1v1c-1 0-1-2-2-1 0 0-1 0-1 1h-1 2c1 1 1 1 1 2s-1 1-1 1l1 1h-2v-1h-1 0c-1 1-1 2 0 3h0c0 1 0 1-1 3l-1-1-1 1h0c-2-1-2-3-4-4h-2-2l1-4z" class="q"></path><path d="M108 367c1-1 1-4 2-5-1 1-1 2 0 3h0c0 1 0 1-1 3l-1-1zm-8-7h3l1 1h0c-1 1-1 1-1 3h-2-2l1-4z" class="B"></path><path d="M477 355c0-1 0-1 1-2s0-10 0-12c1 3 1 7 1 10v28c0 5 0 10-1 16v-9c-1 0-1-1-1-2s-1-1 0-2c1-2-1-7-1-9v-7l1-5v-4-2z" class="U"></path><path d="M475 437c2 1 4 2 5 3h-19c-4 0-9 0-12-1h-1c-2-1-5-2-8-4h1 0c0 1 1 1 1 1l6 2h0c1-1 2-1 4 0v-1h0 1 3 12 7z" class="W"></path><path d="M77 318h1 0c-1-1-1-3 0-3h0c1 0 0 2 0 3 1 1 1 2 1 3h0l-1 2c0 1-1 1 0 2 0 1 0 1 1 2 0 0 0 1-1 2-1 2-1 1 0 4 1 2 0 3 0 5 0 4 1 8 2 12 0 1-1 3-1 5v1h0 0v3c-2-3-2-8-2-11l-2-20 1-4v-1c2-1 1-3 1-5z" class="B"></path><path d="M377 507h0c1-1 0-6 1-7 1 0 2 1 3 0 1 1 1 4 1 6l1 7v9h0 0c-1 0-1 0-2-1 1 0 1-1 1-1 0-3 0-8-1-11l-1 1h-1l-1-1v1h-2c-1 0-1 0-2-1-1 1-2 1-3 0h0v-1-1c1-2 1-4 1-6v2c1-1 0-2 1-3l1 1v5 1l1-1v1h1 1z" class="C"></path><path d="M371 508h3c2 0 6-1 8 1h0-1l-1 1h-1l-1-1v1h-2c-1 0-1 0-2-1-1 1-2 1-3 0h0v-1z" class="g"></path><path d="M217 514c1 0 1-1 2-2v1c1 1 2 1 2 3 1-1 1-1 2-1h1v-1l1 1v2c1 1 2 1 3 1 0-1 0-1 1-2v2 1h-1c-1 1-2 2-2 3v1c1 0 1 0 1 1h-1-1c-1 0-1-1-1-1h-3 0l1 1-2 2h0 0c-1-2-2-3-3-5 0 0 0-1-1-1v-4h0 1c1-1 1-1 1-2h-1 0z" class="Y"></path><path d="M217 514c1 0 1-1 2-2v1c1 1 2 1 2 3 1 1 1 2 2 2s1 0 2 1h0v1 1 1c-1-1-2-2-3-2 0 0-1 0-1 1-1-2-1-2-2-3l-2-2c1-1 1-1 1-2h-1 0z" class="F"></path><path d="M216 520v-4h0 1l2 2c1 1 1 1 2 3v2h0l1 1-2 2h0 0c-1-2-2-3-3-5 0 0 0-1-1-1z" class="D"></path><path d="M219 518c1 1 1 1 2 3v2h0l1 1-2 2h0 0c0-3 0-4-2-6l1-2z" class="B"></path><path d="M241 550c2 0 3-1 5-1 0 1 0 1 1 2h1c0 2 0 2 1 3-1 2-2 3-2 4-1 2 0 2-1 4h-1v1 1l2-1c0 2-1 3-1 4s1 1 0 2h-1 0c0-1 0-1-1-2h1c0-1-1-2-1-3l-2-2h0-1v-4h0-1v3 1l-1-1v-2c0-2 0-2 1-4 1 1 1 1 3 1v-2h0-1c1-1 1-1 0-2 0-1 0-2-1-2z" class="a"></path><path d="M247 551h1c0 2 0 2 1 3-1 2-2 3-2 4-1 2 0 2-1 4h-1v1 1l2-1c0 2-1 3-1 4s1 1 0 2h-1 0c0-1 0-1-1-2h1c0-1-1-2-1-3l-2-2c1-1 2-1 3-1 0 0 1 0 1-1v-2c-1-1-1-5 0-7h1z" class="I"></path><path d="M231 634v1h6c2 0 4-1 5 0h1c-1 1-1 2-2 2l-3 1h2c1 1 0 2 2 2v1c-2 0-2 0-3-1-2 1-3 0-4 1h-4c-2 0-2 0-3 1h-1v-2h0-3-1v-1l3-2h1c1-1 2-2 4-3z" class="K"></path><path d="M224 640c1 0 1-1 2-1 1-1 4-1 5-1 1-1 1-1 2-1h1v1 1c1-1 3 0 4-1h2c1 1 0 2 2 2v1c-2 0-2 0-3-1-2 1-3 0-4 1h-4c-2 0-2 0-3 1h-1v-2h0-3z" class="n"></path><path d="M337 233c2 1 2 2 3 3s2 1 3 2h0 1v1c1 2 1 3 2 5l-1-1v3 1h-4c-2 0-4 0-6-1h0 0c-1-1-1-1 0-2 1 0 1 0 2-1h1 1v-1h-3v-4h0c0-1 0-1 1-2v-3z" class="W"></path><path d="M341 241c-1 0-2 0-3-1h1c0-1 1-1 1-1l1 1v1z" class="U"></path><path d="M335 246v-2c1 0 2 1 3 1 2 0 4 0 6 1h1v1h-4c-2 0-4 0-6-1z" class="a"></path><path d="M340 239l1-1c1 0 2 0 3 1 1 2 1 3 2 5l-1-1h-1-2c0-1-1-1-1-1h-1v-1h1v-1l-1-1z" class="G"></path><path d="M566 347c-1-1 0-3 0-4h1l1 1v2h0c0 1 0 2-1 2v4h0c-1 1 0 3-1 4v1c0 1 0 1-1 1 0 1 0 2-1 2v3h0c0 1-1 3-1 3h0c0 1-1 2-1 3h-1l-1-1s1-1 0-2v-1-1-2c0-1 0-2-1-3-1 0-1 1-2 1h-1v-3c1-1 1-3 1-4v-1h1 0l-1 1c1 1 0 2 0 4 0 1 1 1 2 0v-1-1-3c1 1 1 3 1 4 1 0 1 0 2 1h1v-1c1-1 1-2 2-4l1-3v-2z" class="B"></path><path d="M566 349c1 0 0 2 0 3s0 2-1 3c0-1 1-2 0-3l1-3z" class="C"></path><path d="M561 362c1 1 1 2 2 3-1 1-1 2-2 3v-2-4z" class="H"></path><path d="M566 347c-1-1 0-3 0-4h1l1 1c-1 1-1 2-1 3h0v1c0 2 0 3-1 4 0-1 1-3 0-3v-2z" class="F"></path><path d="M560 356c1 0 1 0 2 1h1c0 1-1 2-1 3s0 1-1 1v1 4c-1-1 0-4-1-6v-4z" class="W"></path><defs><linearGradient id="G" x1="561.628" y1="356.639" x2="564.282" y2="362.527" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#616162"></stop></linearGradient></defs><path fill="url(#G)" d="M565 352c1 1 0 2 0 3l-2 10c-1-1-1-2-2-3v-1c1 0 1 0 1-1s1-2 1-3v-1c1-1 1-2 2-4z"></path><path d="M391 547h0 1 0v2c-1 1-1 1-2 1v1c1 0 1 0 2 1 0 1-1 1-2 2 0 0 0 2 1 3 0 1-1 3-1 3-1 1-1 0-1 2l-2-1h0v-2-1c0-1 1-1 1-1 1 0 1-1 2-1v-2h-1-1c-2 1-1 2-2 3l-1-1h0c0 1 1 3 1 4h-1 0c-1-1-1-2-2-3 0-1 0-2-1-3v-1l-2-2-2-1h-1v-1h2 6c1-1 4 0 6 1v-1s0-1 1-1l-1-1z" class="Y"></path><path d="M388 557h1v1s-1 1-1 2c-1 0-1 0-1-1v-1c0-1 1-1 1-1zm3-10h0 1 0v2c-1 1-1 1-2 1v1h0c0 1-1 2-2 2v-2c1 0 2-1 3-1v-1s0-1 1-1l-1-1z" class="a"></path><path d="M378 550c2 0 4 0 5 1s1 3 2 4v1h0c0 1 1 3 1 4h-1 0c-1-1-1-2-2-3 0-1 0-2-1-3v-1l-2-2-2-1z" class="F"></path><path d="M119 415c-1-1-1-2-1-3v-2c-1-1-1-2-1-3v-2c0-1 0-1 1-1h1c0 1 1 2 1 3v1 1c2 1 2 1 3 3v1c1 1 1 1 2 1l1-1h0c1 1 1 0 1 1 1 1 1 1 1 2s0 1 1 2h-1v1c-1 0-1 0-2 1h0v2h0c1 0 1 1 1 2h0v3h1v2h-1l-1-1c0-1 0-2-1-2 0-1-1-1-1-2v-1-1c0-1-1-1-1-2v-1-1c0-1-3-3-4-3z" class="B"></path><path d="M125 424c0-1-1-1 0-2h0c1 1 2 1 2 2l-1 2 1 1 1 2-2-2v-1c0-1-1-1-1-2z" class="F"></path><path d="M555 390c1-1 1-1 2-1h0c0 1 0 1 1 2-1 0-2 1-2 2v1l-2 4v1h2v1h-2l-1 1v1l-1 1-1-1-1 1h-1v2h-1c-2 1-2 0-3 2-1 0-1 1-1 1-1 0-5 2-6 2 0-1 0-1-1-1h-3l-1 1v-3 2h0c1 0 1 0 1-1l1-1 1-1h0c1 0 1 0 2 1v1c2 0 2 1 3 0l1-1h1c1 0 1 0 1-1v-3-1h1 0c0 1 1 1 1 2v1h2l1-2c0-1 1-1 2-1l-1-1h-1v-1l-2 1v1l-1-1c1-1 1-1 2-1 0-1 0 0 1-1h0 0c2-1 2-2 2-3 1-1 1-2 1-2 0-2 2-2 2-3h-1-2 0c-1 1-1 2-2 3h0 0v3 1 1h-1l-2-2h1s0-1 1-1h0v-1-1c1 0 1-1 1-1h0l1-2 1-1h2 0 1 1z" class="C"></path><path d="M206 577c1 0 1 1 1 1h5v1h-1-1l1 2h0 0c-1 2-1 3-1 4l1 4 1 1c-3 0-7 0-10-1h0l2-2v-2h0c-1 1-1 2-2 3-1 0-1-1-1-1 0-2 0-4 1-6v1h2s0 1 1 1v-1s1-1 2-1h-3v-2c1 0 1-1 1-1h1v-1z" class="N"></path><path d="M206 577c1 0 1 1 1 1h5v1h-1-1l1 2h0 0c-1 2-1 3-1 4v4h-1c0-1-1-3 0-4v-3l-2-1h-3v-2c1 0 1-1 1-1h1v-1z" class="Q"></path><path d="M241 550h-5 0c1-2 1-4 3-6 1-1 2-2 2-3h-2c0-2 2-3 3-4v-2c0-3 0-6 1-8h0l1 10h0c1 1 1 2 1 3-1 1-1 1-2 1l3 3v5c-2 0-3 1-5 1h0z" class="T"></path><path d="M162 447c0-1-1-2-2-3h2c1 0 1 1 2 1v2h2v1c1 1 1 1 2 1 0 0 1 0 1-1h1v1s-1 0-1 1c-1 1 0 1 0 2l-1 1 1 1 1-1v-1h1v1 1c1 0 1-1 2-2l2 4v1h-1l-1-1c-2 0-2 1-3 3-1-1-1-2-2-3s-1-1-2-1h-2c-1 1-2 2-3 2v-1c-1-1 0-2 0-3h-1l-1-2h0l2-2h0l1-2h0z" class="Y"></path><path d="M162 447l1 1v2h-1l-1-1 1-2h0z" class="G"></path><path d="M163 450c0 1 0 1 1 2-1 0-1 0-2 1-1-1 0-2 0-3h1z" class="B"></path><path d="M161 449h0l1 1c0 1-1 2 0 3l-1 1v-1h-1l-1-2h0l2-2z" class="Y"></path><path d="M164 452h2 1v1h-2l-1 2c-1 1-2 2-3 2v-1c-1-1 0-2 0-3v1l1-1c1-1 1-1 2-1z" class="G"></path><path d="M504 446h0c1 1 1 2 2 2v2c0 2 0 2-1 3v1c0 1-1 1-1 1-1 1-1 2-2 2h-1c0 1-1 2-1 2h-3l-1-1-4 3c-1-1-1-1-1-2 1-1 2-1 2-3h0c1-1 2-2 2-3v-1h-1v-1c1-1 1 0 2-1l1 1s0 1-1 1h0 1v-1l1-1-1-1h0l2 1-1 1 1 1h1l1 1v-1-1-1l1-1 1-1 1-2z" class="E"></path><path d="M505 453v1c0 1-1 1-1 1-1 1-1 2-2 2h-1c0 1-1 2-1 2h-3l-1-1c1 0 1-1 2-1s2-1 4-2c1-1 2-1 3-2z" class="H"></path><path d="M504 446h0c1 1 1 2 2 2v2c0 2 0 2-1 3s-2 1-3 2l-1-2c-1 0-1 0-2 1 0 1-1 1-1 1l-2-1v-1h2v-2l1 1h1l1 1v-1-1-1l1-1 1-1 1-2z" class="B"></path><path d="M502 449c1 1 1 1 2 1-1 1-1 2-3 2v-1-1l1-1z" class="H"></path><path d="M504 446h0c1 1 1 2 2 2v2h-1-1c-1 0-1 0-2-1l1-1 1-2z" class="S"></path><defs><linearGradient id="H" x1="115.296" y1="440.963" x2="108.836" y2="447.452" xlink:href="#B"><stop offset="0" stop-color="#6d6b6d"></stop><stop offset="1" stop-color="#878887"></stop></linearGradient></defs><path fill="url(#H)" d="M100 427c1 0 2 0 2 1l1-1v2h1 1 0l1 2 2 4c1 3 3 5 4 8 1 2 3 3 4 5s4 5 5 7c0 2 0 2 1 4l-1 2c-8-11-15-23-21-34z"></path><path d="M104 429h1 0v2h0c-1-1-1-1-1-2z" class="n"></path><path d="M225 530c2 2 5 3 7 4h1c1-1 3-1 5-1 0 1-1 1-1 3-1 0-2 1-3 1h-1c0-1 0-1 1-1v-1h1-1-1c-1 1-2 1-2 1-1 1-2 1-2 2 1 1 2 2 3 2h0c1 0 2 1 2 1v1h-1l-2-1h0l-1 1v1l-2-1c-1 0-2 0-3-1l-1-1c-1 1-1 2-1 3v-4c0-3-1-5-1-7h2l1-2z" class="E"></path><path d="M225 530c2 2 5 3 7 4h1c1-1 3-1 5-1 0 1-1 1-1 3-1 0-2 1-3 1h-1c0-1 0-1 1-1v-1h1-1-1c-1 1-2 1-2 1h-1l1-2h-1c-1 0-2-1-3-1v1c-2 0-2 0-2-1v-1l1-1h-1l-1 1 1-2z" class="X"></path><defs><linearGradient id="I" x1="514.384" y1="235.269" x2="515.44" y2="242.075" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#585757"></stop></linearGradient></defs><path fill="url(#I)" d="M510 233c1 0 3 1 4 1 4 1 8 5 11 8h-2-10c-2 0-3-2-5-3l-6-5h1 0l1 1h0 1l1 1h0v-1h-1v-1h4 0 1v-1z"></path><path d="M410 519s1-1 1-2h0-1 0v-1h2 0c1-1 2-2 3-2s1 0 2-1h0v12l-1-1-2 1h0-2l-1 1c-1 0-2 1-3 1l1-1-1-1c-1 0-1 0-1-1-1 0-2 0-2 1h-1s-1 0-1 1c-1 0-1 0-2-1v-1h0c1-2 4-2 5-4 1 0 1-1 2-1v1l2-1z" class="I"></path><path d="M412 522h2v-1h1c1 1 1 1 1 2h-1 0c-1 0-1 1-1 2h0-2l-1 1c-1 0-2 1-3 1l1-1-1-1c-1 0-1 0-1-1v-1h3l1-1h1z" class="C"></path><path d="M401 524c1-2 4-2 5-4 1 0 1-1 2-1v1l2-1 1 1h2 0v1l-1 1h-1l-1 1h-3v1c-1 0-2 0-2 1h-1s-1 0-1 1c-1 0-1 0-2-1v-1h0z" class="B"></path><path d="M405 525l-2-1 1-1h3v1c-1 0-2 0-2 1z" class="E"></path><path d="M411 520h2 0v1l-1 1h-1l-1 1h-2c0-2 2-2 3-3z" class="a"></path><path d="M385 556l1 1c1-1 0-2 2-3h1 1v2c-1 0-1 1-2 1 0 0-1 0-1 1v1 2h0l2 1c1 1 1 1 1 2v1c1 1 0 3 1 5v2c1 1 1 1 1 3-1 0-2 0-2-1-1 0-1-1-2-1s-1-1-2-1v1c-1 0-1 0-1-1h-3v1h1c-1 1-1 1-2 1v-2-1h1c1-1 2-1 3-2h-4 0v-4-1h3 0 5c-2-1-5-2-6-3v-1h2 1c0-1-1-3-1-4h0z" class="B"></path><path d="M381 569v-4-1h3l1 1 1 1v3h3c0 1 1 1 1 2v1c-1 0-1 0-1-1-1 1-1 1-1 2-1 0-1-1-2-1v1c-1 0-1 0-1-1h-3v1h1c-1 1-1 1-2 1v-2-1h1c1-1 2-1 3-2h-4 0z" class="S"></path><path d="M381 569v-4-1h3l1 1v3c-1 1-3 1-4 1z" class="Y"></path><path d="M186 202l2 1v1 1h2 0l1 1h0 2l1-1v-1c2 1 3 0 5 1v1h0l1 1c-1 1-1 2-2 2 0 1-1 1-1 2h2v1h-3c-1 0-1 0-2 1h0c-1 0-2 1-3 0h0 0c0-1 0-1-1-1v-1c-1 0-1-1-2-1h-1 0c-1 0-1-1-2-1v-1 1h-2v-1l-1-2h-1v-1h2v-1h1c1-1 2-1 2-2z" class="N"></path><path d="M192 208h-2c0-1-1-1-1-2l1-1 1 1h0l1 2z" class="n"></path><path d="M183 204h1v2 2h0-1l-1-2h-1v-1h2v-1z" class="l"></path><path d="M186 202l2 1v1 1h-2c-1 1-1 1-2 1v-2c1-1 2-1 2-2z" class="o"></path><path d="M194 204c2 1 3 0 5 1v1h-1l-1 1v1h-2v2c-1 0-1 1-2 1h-1v-1l2-2h0-2 0l-1-2h2l1-1v-1z" class="k"></path><path d="M191 206h2l1-1v1h1 1v1h0-2v1h-2 0l-1-2z" class="Z"></path><path d="M199 206h0l1 1c-1 1-1 2-2 2 0 1-1 1-1 2h2v1h-3c-1 0-1 0-2 1h0c-1 0-2 1-3 0h0 0c0-1 0-1-1-1v-1c-1 0-1-1-2-1h-1 0c1-1 1-1 2-1 1 1 1 1 2 0h1 0l-1 2h0 2c1 0 1-1 2-1v-2h2v-1l1-1h1z" class="J"></path><path d="M199 206h0l1 1c-1 1-1 2-2 2v-3h1z" class="W"></path><path d="M538 352c2 0 2 0 3 1 0 2 0 3-1 4v1c0 1-1 3-2 4l-1 1h-1v-1-2c-1 0-1 0-2 1h1v2c-1 1-2 2-4 2 0-1-1-1-1-1-1 0-1 1-2 2 0-2-1-3-1-5h1l1 1v-1-1l-1-1v-3c1-1 2-1 4-2 0 0 1 0 2-1 0 2-1 2 1 3l2-2s1-1 1-2z" class="B"></path><path d="M538 352v1c1 1 2 1 2 2h-2l-1-1h0s1-1 1-2z" class="Y"></path><path d="M537 354h0v1 1c-1 0-1 1-1 2-1 0-1 0-1 1l-1-2 1-1 2-2z" class="C"></path><path d="M532 354s1 0 2-1c0 2-1 2 1 3l-1 1 1 2c-2 1-2 2-4 2h-1v-1l2-2-2 1v-2l1-1s0-1 1-2z" class="F"></path><path d="M531 356h2l1 1v1h-2l-2 1v-2l1-1z" class="E"></path><path d="M83 377c2 1 3 2 4 4 0-1 0-1 1-2l1 3c0 1 1 2 1 4h0c1 2 1 3 1 5h0l1 1c0 2 1 7 2 8l-1 2h-1v-1l-1 4-1-3-7-25z" class="C"></path><path d="M90 402v-3h0v-2c1 2 1 2 2 3v1l-1 4-1-3z" class="U"></path><path d="M89 386h1 0c1 2 1 3 1 5h0v1l-1 1c0-3-1-5-1-7z" class="n"></path><path d="M87 381c0-1 0-1 1-2l1 3c0 1 1 2 1 4h-1c-1-1-1-2-2-2v-1-2z" class="d"></path><path d="M91 391l1 1c0 2 1 7 2 8l-1 2h-1v-1-1c-1-1-1-1-2-3l1-1c-1 0-1-1-2-2l1-1 1-1v-1z" class="H"></path><path d="M442 577c1-1 3-1 4 0h0l5 6c2 2 3 4 4 6-2 1-4 1-6 2h-1c-2 0-4 0-6-1v-1h0 2l-1-1v-6c1 0 2 0 3 1v2-1-2-1l-1-1v1c-1-1-2-1-3-1l2-2c-1 0-2-1-2-1z" class="q"></path><path d="M446 582h1l1 1v4l-1 1h0l-1-6z" class="J"></path><path d="M442 577c1-1 3-1 4 0h0l5 6h-1c-1-2-4-3-6-5-1 0-2-1-2-1z" class="K"></path><path d="M451 583c2 2 3 4 4 6-2 1-4 1-6 2h-1c-2 0-4 0-6-1v-1h0 12l-4-6h1z" class="D"></path><path d="M453 605h0 2v3h2c-2 1-7 0-9 0h-3l3 4v1h-1l-1-2h-1c0 1 1 1 0 2v1h-1s-1 1-2 1h-1l-3 2h-1c-1-2-1-4-1-6v-2-2-3 2h2l1 2c0-2 0-2 1-3-1 1-1 1-1 2h10 0-4c1-1 1-1 2-1 0 0 0-1 1-1h1 2 0 2z" class="I"></path><path d="M453 605h0 2v3l-6-1h0 3c1-1 1-1 1-2z" class="D"></path><path d="M449 605h2 0 2c0 1 0 1-1 2h-3-4c1-1 1-1 2-1 0 0 0-1 1-1h1z" class="S"></path><path d="M451 605h2c0 1 0 1-1 2-1-1-1-1-2-1v-1h1z" class="c"></path><path d="M436 604v2 1h1 1 0c0 1 0 2 1 2 1 1 1 0 2 0v1l2-2 1 1c0 1-2 2-1 3l1 1v1s-1 1-2 1h-1l-3 2h-1c-1-2-1-4-1-6v-2-2-3z" class="q"></path><path d="M439 610l1 2-3 1-1-2c2 0 2 0 3-1z" class="Y"></path><path d="M436 604v2 1h1 1 0c0 1 0 2 1 2 1 1 1 0 2 0v1 3h-1v-1l-1-2c-1 1-1 1-3 1h0v-2-2-3z" class="B"></path><path d="M436 609l1-1c0 1 1 2 2 2-1 1-1 1-3 1h0v-2z" class="r"></path><path d="M152 632c1 1 3 1 4 2s2 1 4 1v1l-1 1-1 2h0c1 0 1 1 1 1 2 1 3 0 5 1l-3 1c-2 1-8 0-11-1-3 0-6 1-8 0h-1c0-1 1-2 2-3s3-1 5-2 3-3 4-4z" class="C"></path><path d="M156 638c1 0 2-1 3-1l-1 2c-2 1-5 1-8 1 3-1 4-1 6-2z" class="n"></path><path d="M145 638c4 0 7-1 11 0-2 1-3 1-6 2h-2c-1 1-4 1-5 1s-1-1-1 0h-1c0-1 1-2 2-3 1 1 1 1 2 0z" class="g"></path><path d="M152 632c1 1 3 1 4 2s2 1 4 1v1l-1 1c-1 0-2 1-3 1-4-1-7 0-11 0-1 1-1 1-2 0 1-1 3-1 5-2s3-3 4-4z" class="L"></path><path d="M152 632c1 1 3 1 4 2-1 1-2 1-3 1s-1 1-1 1c-2 2-4 1-5 1l-2 1c-1 1-1 1-2 0 1-1 3-1 5-2s3-3 4-4z" class="O"></path><path d="M524 166v-6 16 31c-1-2 0-4 0-6 0-1-1-1-1-2h0v1h0c0-1 0-1-1-2h1c0-1-1-2-1-2v-1-1l-1-1v-1-1-1c0 1 0 1-1 2v4h0l-1-13c0-1 1-5 1-5h0c0-2-1-5 0-6v4c0 1 1 1 1 2v-2-1l1-1h0c0-2 0-2 1-3 0-2 0-3 1-4v-1z" class="C"></path><defs><linearGradient id="J" x1="520.93" y1="180.969" x2="524.234" y2="181.32" xlink:href="#B"><stop offset="0" stop-color="#3d3d3c"></stop><stop offset="1" stop-color="#575758"></stop></linearGradient></defs><path fill="url(#J)" d="M522 174h0c0-2 0-2 1-3 0-2 0-3 1-4v-1c0 3 0 25-1 26 0-1-1-2-1-3l-1-1v-4-1c0-3 1-6 1-9z"></path><path d="M103 364c2 1 2 3 4 4h0c1 0 1 1 1 1h2l2 2h1v1l1 1v1h-2v2c-2 0-3-2-4-1l-1 2c0 1 0 2-1 3 0-1 0-1-1-2 0-1-1-2-1-4 1-1 0-1 0-2-1-1-1-2-2-2h-1v1 1l-1 1v5h0c-1 0-1 0-1-1v-2c-1-2 0-3 0-5v-6h2 2z" class="X"></path><path d="M107 368h0c1 0 1 1 1 1h2l2 2h1c-1 1-2 1-3 1l-1 1 1 1h-1-1c0-1 1-3-1-4h-1c-1 0-1-1-2-1 1-1 2-1 3-1z" class="C"></path><path d="M103 364c2 1 2 3 4 4-1 0-2 0-3 1v-1h-1c0 1 0 1-1 1h0c-1 0-2 1-3 1v-6h2 2z" class="G"></path><path d="M111 421h1c1-1 1-1 2-1-1-2-1-2-2-3h0v-1c1 0 2 1 2 2h2c0 1 0 1-1 2l1 1c1 0 1 1 2 2l-1 1c0 1 1 1 2 2v1h0-2v1 3h0c-1 0-1-1-1-1v1h-2c-1-1-1-2-2-2h-1c-1-1-1-2-2-3 0-1-1-2-1-2h-1c1 1 1 1 1 2l-1 1c-1-1-1-2-1-4-1-1 0-2-1-3-1 0-1-1-1-1v-1-1h1l2 1h0v-1l1-1v1c1 1 2 2 3 4z" class="F"></path><path d="M116 424v1c-1 0-1 0-2 1h0-1l-1-1c1-1 1-1 1-2l2 1h1z" class="H"></path><path d="M116 430l-1-1s-1 0-1-1l1-1h1l1 1v3h0c-1 0-1-1-1-1z" class="D"></path><path d="M115 424v-4l1 1c1 0 1 1 2 2l-1 1h-1-1z" class="B"></path><path d="M105 417l2 1h0v-1l1-1v1c1 1 2 2 3 4l1 1v1c-1-1-1-1-2-1 0-1 0-1-1-1 1 2 1 3 2 5 0 0 0 1 1 1v2h-1c-1-1-1-2-2-3 0-1-1-2-1-2h-1c1 1 1 1 1 2l-1 1c-1-1-1-2-1-4-1-1 0-2-1-3-1 0-1-1-1-1v-1-1h1z" class="d"></path><path d="M104 418h1c1 1 2 1 3 1 0 1 0 1-1 2h0v2h-1c-1-1 0-2-1-3-1 0-1-1-1-1v-1z" class="J"></path><path d="M209 195v5 4l-2 2c-1 0-1 0-1-1h-1l-2 2h0l-1 1h-1v-1h-1l-1-1h0v-1c-2-1-3 0-5-1 1 0 1-1 2-2h0c1-1 1-1 2 0h1l1-1-2-2h-1c0-1 1-2 2-2s2 0 3 1c0-1 1-1 2-1l2-2h2 0 1z" class="N"></path><path d="M202 202c0-1 1-1 2-2h1v1 1s0 1 1 1v1h-1-1l-2-1h0v-1z" class="n"></path><path d="M206 203h1c0-2 1-3 2-3v4l-2 2c-1 0-1 0-1-1v-1-1z" class="f"></path><path d="M197 199c0-1 1-2 2-2s2 0 3 1c0 1 0 2-1 4 0-1-1-1-1-1l-2-2h-1z" class="k"></path><path d="M202 203l2 1h1 1v1h-1l-2 2h0l-1 1h-1v-1h-1l-1-1c1 0 2-1 2-2l1-1z" class="d"></path><path d="M199 206c1 0 2-1 2-2 1 1 1 2 2 3h0l-1 1h-1v-1h-1l-1-1z" class="J"></path><path d="M200 201s1 0 1 1h1v1h0l-1 1c0 1-1 2-2 2h0v-1c-2-1-3 0-5-1 1 0 1-1 2-2h0c1-1 1-1 2 0h1l1-1z" class="l"></path><path d="M199 205v-2h2v1l1-1h0l-1 1c0 1-1 2-2 2h0v-1z" class="g"></path><defs><linearGradient id="K" x1="148.914" y1="444.003" x2="154.123" y2="454.431" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#K)" d="M150 442l2-1v1h0l3 3s0-1 1 0c2 1 3 2 6 2l-1 2h0l-2 2h0-1c0 2 0 2-1 3-1 0-1-1-2-1h-1-2l-2 1c-1-1-1-2-2-3h-2-1v-1h1v-2c0-1 0-1-1-2l1-1h3c1 1 1 1 2 1v-1c0-1-1 0-2-1v-1s0-1 1-1z"></path><path d="M146 450h1 0c1-1 1-1 1-2v3h0-2-1v-1h1z" class="I"></path><path d="M148 448c1 0 2 0 3 1 1 0 2 1 2 2s0 1-1 2l-2 1c-1-1-1-2-2-3h0v-3z" class="a"></path><path d="M150 442l2-1v1h0l3 3s0-1 1 0c2 1 3 2 6 2l-1 2h0l-2 2h0-1c-1 0-1 0-2-1l1-1-1-2h-1v2h-1c0-1 0-2-1-3h-1v-2h-1-2v-1s0-1 1-1z" class="X"></path><path d="M150 442l2-1v1h-1v2h-2v-1s0-1 1-1z" class="E"></path><path d="M157 449h1c1 0 1-1 2-1l1 1-2 2h0-1c-1 0-1 0-2-1l1-1z" class="B"></path><path d="M146 451h2c1 1 1 2 2 3l2-1h2 1c1 0 1 1 2 1 1-1 1-1 1-3h1l1 2h1c0 1-1 2 0 3v1c-1 2-2 2-3 3-1 0-2 2-2 3l1 1h-1l-1-1h-1s-1 0-1-1v2c1 0 0 0 1 1l-2-2v-1c-1-1-2-1-2-2-1 0-1-1-1-1h-1-1v-1c0-1-1-1-1-2 1-1 1-1 0-2 0 1-1 1-1 2h0c-1-2 0-2 1-4v-1z" class="X"></path><path d="M150 454l2-1h2v1l1 1h-1c0 2-1 2 0 4 1 1 1 2 2 4l1 1h-1l-1-1c-1-1-2-1-2-2l-1-1c-1-2-2-2-3-4v-1l1-1z" class="R"></path><path d="M150 454l2-1h2v1h-1l-1 2h-1v-1h-1l-1 1v-1l1-1z" class="C"></path><path d="M158 451h1l1 2h1c0 1-1 2 0 3v1c-1 2-2 2-3 3-1 0-2 2-2 3-1-2-1-3-2-4-1-2 0-2 0-4h1l-1-1v-1h1c1 0 1 1 2 1 1-1 1-1 1-3z" class="F"></path><path d="M156 456h1c1 0 1 0 1 1v1h-3l1-2z" class="W"></path><path d="M160 453h1c0 1-1 2 0 3v1h-1v-1c-1-1-1-1-2-1 1-1 1-2 2-2z" class="C"></path><path d="M181 206h1l1 2v1h2v-1 1c1 0 1 1 2 1h0 1c1 0 1 1 2 1v1h0c0 1 0 2-1 3l-1 1-1 1h1l-1 1-2-1v1l-1 1h0c-1-1-1-1-1-2-1 1-2 1-2 1-1-1-2-1-3-1v-1c-1 1-1 1-2 1v-1h1l1-1-1-1c-1-1-2-2-3-2v-1c1-1 2-1 3-1v-1c-1 0-1 0-2-1h2c0-1 0-1 1-1s1 1 2 1c1-1 1-2 1-2z" class="d"></path><path d="M174 211c2 0 3 1 4 1v2c1-1 1-1 2-1s2 1 2 2h-2l-1-1-1 1-1-1c-1-1-2-2-3-2v-1z" class="D"></path><path d="M187 214c1 0 1 0 2 1l-1 1-1 1h1l-1 1-2-1v1l-1 1h0c-1-1-1-1-1-2-1 1-2 1-2 1-1-1-2-1-3-1v-1c-1 1-1 1-2 1v-1h1l1-1 1-1 1 1h2l2 2c1-1 2-1 2-2v-1h1z" class="G"></path><path d="M187 214c1 0 1 0 2 1l-1 1h-2v-2h1z" class="I"></path><path d="M181 206h1l1 2v1h2v-1 1c1 0 1 1 2 1h0 1c1 0 1 1 2 1v1h0c0 1 0 2-1 3-1-1-1-1-2-1h-1v1c-2-1-2-2-4-3h0c-2-1-4-1-5-2v-1c-1 0-1 0-2-1h2c0-1 0-1 1-1s1 1 2 1c1-1 1-2 1-2z" class="f"></path><path d="M188 210c1 0 1 1 2 1v1h0c0 1 0 2-1 3-1-1-1-1-2-1h-1v1c-2-1-2-2-4-3l3-1h0l1-1h1 1z" class="R"></path><path d="M187 210h1v1c-1 0-1 1-1 1l-1-1v-1h1z" class="W"></path><path d="M187 214l2-2h1c0 1 0 2-1 3-1-1-1-1-2-1z" class="G"></path><path d="M348 399l1 1c1 0 2-1 3-2h0l1 1c1 1 1 1 2 1 0 1 1 2 2 2v-1c0 1 0 2 2 3h0c0 1 1 2 0 3h0l-1 1h-6l-8-1-2 1c0-1 0-1 1-2 0-2-1-2-2-3l-3-1h-1 3v-1h4l1 1h2v-1h1v-1-1z" class="C"></path><path d="M340 401h4l1 1c-1 1-4 0-7 0h-1 3v-1z" class="d"></path><path d="M348 399l1 1c1 0 2-1 3-2h0l1 1c1 1 1 1 2 1-1 1-4 1-5 1v-1c-1 1-2 1-2 1v-1-1z" class="E"></path><path d="M351 405c2-1 6 0 8-1v3l-1 1c-1-1 0-1-1-1h-2-1c-1-1-2-1-3-2z" class="J"></path><path d="M350 405h1c1 1 2 1 3 2h1 2c1 0 0 0 1 1h-6l-8-1 1-1c1 0 3-1 5-1z" class="H"></path><path d="M341 403h10v1 1h-1c-2 0-4 1-5 1l-1 1-2 1c0-1 0-1 1-2 0-2-1-2-2-3z" class="I"></path><path d="M316 644c0-1 0-1 1-1 1-2 1-1 2 0h1v2s1-1 1 0h4c2-1 2-1 3-1h5 0 3l2 1c1 0 3 0 4 1 1 0 2 0 2 1-2 0-3 0-5-1h-2c-3 1-5 1-7 1h-4-1c-1 1-7 1-9 1 0-1-1-1-1-1-4 1-8 1-12 1-3 0-5 0-8-1 1 0 1 0 2-1 1 0 2-1 3-1h0l2-1h2l1 1 4-2h0c0 1-1 2-2 3v1h0c2-1 4 0 6-1 0-1 1-1 1-1 1 0 1-1 2-1z" class="b"></path><path d="M302 644h2l1 1c0 1-1 1-1 1-2 0-2 0-4-1h0l2-1z" class="m"></path><path d="M316 644c0-1 0-1 1-1 1-2 1-1 2 0h1v2s1-1 1 0h4c2-1 2-1 3-1h5c-3 2-6 2-8 2h-1c-1-1-2-1-3 0 0 0-1 0-1 1l-2-2-2 1-2-1c1 0 1-1 2-1z" class="C"></path><path d="M195 466v-1l1-3c0-1 0-2-1-4 1-1 2-1 1-2h-1l-1 1c-1 0-4-2-4-2v-1h1c2 0 3-1 5-2 1 2 3 4 4 5 0 1 0 1-1 1h-1-1v2h2l1-1c1 0 2 0 2 1 1 1 2 2 2 3h-1c-1 0-1-1-1-2h-2c-1 1-1 1-1 3 1 1 1 2 3 3 1-1 0-3 3-3v1l-2 2s0 1 1 1c1 1 1 3 3 4h2v-2l1-1c0 1 1 1 1 2s-1 1-2 1c0 1-1 1-1 2 1 1 2 1 3 0 0 0 0 1 1 1h0 1l-1 1h0-2l1 1h-2v-1-1h-2c-1-1-3-3-4-3-1-1-1-1-2-1l1-2c-2 0-3-1-4-1-1-2-2-2-3-2z" class="E"></path><path d="M202 469v1h2l1 1h-1 1c1 1 1 1 2 1l1 1c0 1-1 1-1 2-1-1-3-3-4-3-1-1-1-1-2-1l1-2z" class="B"></path><path d="M204 635c1 0 1-1 2-1s1 1 2 1l2-1h5s1 0 1 1h5 2c-1 1-1 2-3 2-1 1-1 1-3 2-1 0-2 1-3 1-1 1-2 1-2 1h-1s-1 1-2 1c-2 0-4 0-6-1h-2 0c0-1-1-1-1-2h-2l1-1c2-1 3-1 5-3h0z" class="o"></path><path d="M216 635h5c-2 1-3 1-5 1v-1h0z" class="f"></path><path d="M210 634h5s1 0 1 1h0v1c-2 1-4 1-6 2-1 1 0 1-1 1-2 0-4 1-5 2h-1-2l1-1h2c0-1 1-1 2-1 1-1 3-2 4-2l-1-1 1-2z" class="J"></path><path d="M210 634h5s1 0 1 1h0-4c0 2 0 2-2 2l-1-1 1-2z" class="M"></path><path d="M204 635c1 0 1-1 2-1s1 1 2 1l2-1-1 2 1 1c-1 0-3 1-4 2-1 0-2 0-2 1h-2l-1 1h0c0-1-1-1-1-2h-2l1-1c2-1 3-1 5-3h0z" class="p"></path><path d="M204 635c1 0 1-1 2-1s1 1 2 1l2-1-1 2c-1 0-3 1-4 1-1-1-1-1-1-2h0z" class="K"></path><path d="M484 480v-1l2 2 2 2h1l-1 1-2 1-2 3c-1 1-2 3-3 4v2s0 1-1 1c0 1-1 2-1 2-2 2-4 4-6 5-1 0-1-1-2-1h0v-2l2-3 2-3c0-1 1-1 1-2h0l3-1v-2c0-1 1-2 1-2 0-1-1-1-1-2l3-3h1c0-1 0-1 1-1z" class="f"></path><path d="M480 486h1l1 2s-1 1-2 1c0-1 0-1-1-1 0-1 1-2 1-2z" class="J"></path><path d="M480 495c-1 1-2 1-3 1 0 0 0-1 1-1 0-1 1-1 1-2v-2h1v1h1v2s0 1-1 1z" class="o"></path><path d="M484 480v-1l2 2 2 2h1l-1 1-2 1c-1 1-2 2-4 3l-1-2h-1c0-1-1-1-1-2l3-3h1c0-1 0-1 1-1z" class="c"></path><path d="M483 481c0-1 0-1 1-1l1 1c0 1-1 1-1 2l-3 3h-1c0-1-1-1-1-2l3-3h1z" class="f"></path><path d="M444 614h1v1h5 5 2v2h2c1 2 0 4 0 6-1 0-1 1-1 1h-1c1 1 1 1 2 1h0c-1 0-2 0-3 1h-1l-1-1-2 1h-3v-1h0v-1-1l1-1h-1l-1-1s0-1-1-1l-1-2h-2s0-1-1-1c0-1-1-1-2-2h1c1 0 2-1 2-1z" class="B"></path><path d="M445 615h5 5 2v2h2-7-3-1c-1 0-2 0-2-1h0l-1-1z" class="I"></path><path d="M449 622c-1-2-1-3-2-4l3 2s1 1 2 1 1 1 2 1c0 1 1 0 2 1h1l1 1h-1c1 1 1 1 2 1h0c-1 0-2 0-3 1h-1l-1-1-2 1h-3v-1h0v-1-1l1-1h-1z" class="a"></path><path d="M450 622c1 2 2 3 4 3l-2 1h-3v-1h0v-1-1l1-1z" class="W"></path><path d="M149 487h1c1 2 1 3 3 4h0c1 2 3 4 5 5l-1 1 4 4c2 2 4 3 7 5 1 1 3 3 5 4 3 2 6 5 8 7 2 1 3 2 4 3v1c-5-3-9-6-14-9-8-7-16-13-24-21h0c0-1 0-1 1-1v-1l-1-1c1 0 2 0 2-1z" class="L"></path><path d="M149 487h1c1 2 1 3 3 4h0c1 2 3 4 5 5l-1 1h-1v-1c-3-1-5-4-8-6v-1l-1-1c1 0 2 0 2-1z" class="o"></path><path d="M79 355h1c0-1 0-1 1-2v2c0 1-1 1-1 1l1 1c1 0 1 0 1 1l1 3h0l1 3v2 2h2v1l-1 1v1c1 0 1 1 1 2 1 0 1-1 1-2l1 1h0l2 2h1l1 1h-3c1 2 1 2 3 2h0c0 2-1 2-3 3v2l-1-3c-1 1-1 1-1 2-1-2-2-3-4-4-1-1-1-3-2-5l-2-13v-3h0 0v-1z" class="G"></path><path d="M90 374h-2l-1-1 1-1 2 2z" class="C"></path><path d="M82 364h2v2 2h-1c-1-1-1-2-1-4z" class="m"></path><path d="M82 364v-5-1l1 3h0l1 3h-2z" class="H"></path><path d="M81 372h1l2-1v1c0 1 1 2 1 3 1 2 0 2 1 3 1 0 1-1 2-1l-1-2h1 1 0c1 2 1 2 3 2h0c0 2-1 2-3 3v2l-1-3c-1 1-1 1-1 2-1-2-2-3-4-4-1-1-1-3-2-5z" class="W"></path><path d="M88 379v-1-1c1 1 1 2 1 3v2l-1-3z" class="C"></path><path d="M432 632h1v1h1c1 0 2 0 3 1h2v1l2 1h-1c1 1 2 1 3 1s2 1 3 1c-1 1-2 1-4 1h0l-2 1-2 1-3 1-1 1h-2l-3-3-1-1c0-1-1-1-1-1h-1c-1 0-3 0-4-1v-1c2 0 3-1 4-1s1 0 2-1h-2 0 1v-2h0 5z" class="W"></path><path d="M434 637h0l1 1c1 0 3 1 4 2l-3-1h-5c-1 1-1 1-2 0l1-1h2l2-1z" class="I"></path><g class="G"><path d="M427 638h0 2 3-2l-1 1c1 1 1 1 2 0 0 1 0 2 1 2h6l-3 1-1 1h-2l-3-3-1-1c0-1-1-1-1-1z"></path><path d="M432 632h1v1h1c0 1 1 1 2 2l1-1v1c0 1 0 1-1 1-1 1-1 1-1 2l-1-1h0l-2 1h-3-2 0-1c-1 0-3 0-4-1v-1c2 0 3-1 4-1s1 0 2-1h-2 0 1v-2h0 5z"></path></g><path d="M433 633h1c0 1 1 1 2 2h-8l5-2z" class="D"></path><path d="M426 635c1 0 1 0 2 1 1 0 2 0 3 1h1 2 0l-2 1h-3-2 0-1c-1 0-3 0-4-1v-1c2 0 3-1 4-1z" class="C"></path><path d="M159 211c-1-2-2-4-4-5l-1 1-1-1c0-1 0-1-1-2l-1 1c0 5 3 11 6 15 5 6 13 9 21 10h1c4 1 8-2 12-2h0c-6 3-13 3-19 2-8-2-15-7-19-14-4-6-6-13-4-20v3l2-2c0 1 1 2 2 2h1c0 1 0 1 1 2h1v1c0 1 0 2 2 2 1 0 1 0 1 1v1c1 1 2 1 2 3h0-1c-1 1-1 1-1 2z" class="X"></path><path d="M151 197c0 1 1 2 2 2h1c0 1 0 1 1 2h1v1c0 1 0 2 2 2 1 0 1 0 1 1v1c0-1 0-1-1-1h-2c0-1-1-2-2-3l-1 1c-1 0-1-1-1-2h0-1s-1 2-1 3v1l-1-1v-5l2-2z" class="F"></path><path d="M471 634h0l3-2c2 1 3 1 4 2v1h1 2v1h0-3c-1 1-1 1-2 1s-2 0-2 1c-2 0-3 1-4 1v-1-1c-1 0-1 0-2 1v1h-2c0-1 0-1-2-1h-1l-2 2v1l-3-1-3 1h-1-3v-1h-2v1h0l-2 1h-2-1c0-1-1-2-1-2l-1-1h0c2 0 3 0 4-1-1 0-2-1-3-1s-2 0-3-1h1c2 0 4 0 7-1v1l-1 1h2 3v-1h1v1c1 1 2 1 3 1s2 0 3-1h2 1c0-1 2-1 2-1v1l5-3h2z" class="B"></path><path d="M455 641v-2h1c1 1 1 1 2 1l-3 1z" class="I"></path><path d="M461 640c-1 0-1-1-1-1v-1h3l-2 2z" class="G"></path><path d="M446 638l3 3-2 1h-2-1c0-1-1-2-1-2l-1-1h0c2 0 3 0 4-1zm25-4h0l3-2c2 1 3 1 4 2v1h1 2v1h0-3c-1 1-1 1-2 1s-2 0-2 1c-2 0-3 1-4 1v-1c1 0 2-1 2-1 1 0 2-1 3-1s1 0 2-1h-1c-1 0-2-1-4 0l-1 1-2-2h2z" class="F"></path><path d="M125 430l-1-3h0v1c-1 0-1 0-2-1l1-1h0 1 0 0c1 0 1 1 1 1 1 1 0 1 0 2 1 1 0 0 1 0 0 1 2 1 2 2 1 0 1-1 2-1v1h1 1c0-1 0-2-1-2l2-2-1-1c1 0 1 0 2-1 1 1 1 1 1 2h-1v2l-1 1-1 2h-1c1 1 1 2 2 3h-3c0 1 0 1 1 2l-2 1c1 0 1 0 2 1v2c0 1-1 1-2 1-1-1 0 0 0-2l-1-1-2 1-2-2v2h-1l-1-1v-3h-1l-1 1c0-1 0-1-1-2h0v-2c-1 0-1-1-1-2h-1v-3-1h2c0 1 1 2 2 3h2 0 2z" class="Y"></path><path d="M118 431h2v3h-1v1h0v-2c-1 0-1-1-1-2z" class="X"></path><path d="M124 438c1-1 2-1 3-2 1 0 1 0 2 1-1 1-1 1-1 2l-2 1-2-2z" class="B"></path><path d="M123 430h2s1 0 1 1h1l-2 2v1 2h-1 0v-3l-1-1v-2z" class="b"></path><path d="M290 637h1v1h1l-1 2c1 0 1 1 1 1l-1 1v1h0c1 0 1 1 2 1h1v1l-1 1v1h0l-13-2c-2 0-5 1-7 0-1 0-1-1-2-1v-1l1-1h1c0-1 1-1 2-1l1-1c2-1 4-1 6-1h2l3-1c1-1 2-1 3-1z" class="D"></path><path d="M272 642s1 0 2 1h0l-1 1c1 1 2 0 3 1h4 0c2-1 4-1 5-1h3 0c1 1 3 1 4 1l1-1h1v1l-1 1v1h0l-13-2c-2 0-5 1-7 0-1 0-1-1-2-1v-1l1-1z" class="C"></path><path d="M290 637h1v1h1l-1 2c1 0 1 1 1 1l-1 1v1h0-2 0c-2 1-2 0-3 0s-3 1-5 1c-1-1 0 0-1 0s-3-1-3-1h-2v-1c2-1 3-2 5-2l2-1h2l3-1c1-1 2-1 3-1z" class="g"></path><path d="M277 643c2 0 5-1 8-1 1 0 2 0 3-1l1 1v1h0c-2 1-2 0-3 0s-3 1-5 1c-1-1 0 0-1 0s-3-1-3-1z" class="d"></path><path d="M290 637h1v1c0 1 0 1-1 1 0 1-1 1-2 2 0 0-1 0-1-1h-1c-1 1-4 1-5 1l-1-1 2-1h2l3-1c1-1 2-1 3-1z" class="k"></path><path d="M290 637h1v1c0 1 0 1-1 1h0-2c-1 0-3 1-4 0l3-1c1-1 2-1 3-1z" class="Z"></path><defs><linearGradient id="L" x1="332.298" y1="244.311" x2="321.232" y2="247.451" xlink:href="#B"><stop offset="0" stop-color="#353334"></stop><stop offset="1" stop-color="#626261"></stop></linearGradient></defs><path fill="url(#L)" d="M334 237v1 3s1 1 2 1h3v1h-1-1c-1 1-1 1-2 1-1 1-1 1 0 2h0l-1 4h2v2c-2 0-4 0-5-1h-4-5c1-1 2-1 3-1h2c-1 0-1-1-2-1s-1 0-2-1h-6l1-5h7 3v-2h3 0c0-1 0-1 1-2h-1c0-1 1-1 2-1h1v-1z"></path><path d="M323 248l9 1 1 1h0c-1 1-2 1-2 1h-4-5c1-1 2-1 3-1h2c-1 0-1-1-2-1s-1 0-2-1z" class="Q"></path><path d="M334 237v1 3s1 1 2 1h3v1h-1-1c-1 1-1 1-2 1-1 1-1 1 0 2h0l-1 4h2v2c-2 0-4 0-5-1 0 0 1 0 2-1h0l-1-1h1c1-2 0-4 0-6h-7-1 3v-2h3 0c0-1 0-1 1-2h-1c0-1 1-1 2-1h1v-1z" class="J"></path><path d="M328 241h3c1 0 2 0 3 1v1c-1 1-6 0-8 0h-1 3v-2z" class="Z"></path><path d="M217 514h1c0 1 0 1-1 2h-1 0v4c1 0 1 1 1 1 1 2 2 3 3 5h0v2 1l-2 1c-1-1-3 0-4 0h-2-1c-1 1-1 1-1 2h0 0c-1 0-1-1-1-2-1-1-1-1-2-1 1-2 1-2 2-2v-1h-1c-1 0-2-1-3-1v-2c1 0 1-1 2-1h0 0l2-2c-2 0-2 0-4-1h3 1v-1c-2 0-2 0-3-1l1-1c1 0 2 0 2 1 2 0 4 0 5-1 1 0 2 0 3-2z" class="U"></path><path d="M208 526v-1c0-1 0-1 1-2-1 0-1-1-1-1l1-2h0 1 2 1 1l-1 2 2 1-2 2c-1 1-1 3-2 4v1c-1 1-1 1-1 2h0 0c-1 0-1-1-1-2-1-1-1-1-2-1 1-2 1-2 2-2v-1h-1z" class="C"></path><path d="M213 525c0-1-1-1-1-2s0-1 1-2v1l2 1-2 2z" class="B"></path><path d="M216 520c1 0 1 1 1 1 1 2 2 3 3 5h0v2 1l-2 1c-1-1-3 0-4 0h-2-1v-1c1-1 1-3 2-4l2-2 1-3z" class="V"></path><path d="M211 529l2-1c1 0 2 0 3-1h1c2 0 2 0 3 1v1l-2 1c-1-1-3 0-4 0h-2-1v-1z" class="L"></path><path d="M448 515s1-1 2-1l1 1h0v1c-2 1-3 2-5 3l-2 2h0 0l-2 1-2 1h-2l-2 1c-1 1-2 0-3 2h-1l-3 2v-2h-1l-1-2v-1h-1c-1-1-1-1-1-3h1c0-2 0-2 1-3l-1 2h1 0 2s1-1 1-2h2c1 0 2-1 2-2h1v2h2c0 1 0 1-1 2l1-1c1 0 1-1 2-2l1 1v1h0l1-1h0c1-1 0-1 1-1 1-1 3-2 4-2l2 1z" class="E"></path><path d="M435 522h1l2-1v2l-2 1h-1v-1-1zm11-8l2 1c-2 2-3 2-6 2h-1c1-1 0-1 1-1 1-1 3-2 4-2z" class="X"></path><path d="M438 521h2l1 1c1-1 1-1 1-2h0l4-1-2 2h0 0l-2 1-2 1h-2v-2zm-6 3c0-1 2-1 3-2v1 1h1c-1 1-2 0-3 2h-1l-3 2v-2c1-2 2-2 3-2z" class="B"></path><path d="M432 524c0 1 0 1-1 2h1l-3 2v-2c1-2 2-2 3-2z" class="C"></path><path d="M435 517h2c0 1 0 1-1 2h0c-1 0-1 1-2 1h-1l-1 1c-1 1-1 1-2 1 0 1 0 1-1 1v-1l1-1c1-1 1-1 1-3h0 2c1-1 1-1 2-1z" class="G"></path><path d="M435 573l1 1c2 3 7 0 10 2h1l-1 1h0c-1-1-3-1-4 0 0 0 1 1 2 1l-2 2h0l-1 1h-3l-2-2h0v-1 3 6 5h-1v-2h-2v2h-2-3l-1-1h0 1l1-1v-3h1l2-2c-1-1-1-2-2-3v1l-2-1-1-1h2s0-1 1-1c0-1 0-1 1-2h-1v-1h-2v-2-2h4 3z" class="E"></path><path d="M430 577h4l-1 2h-1 0c-1 0-1-1-1-1h-1v-1z" class="m"></path><path d="M431 578s0 1 1 1h0l1 2h-4s0-1 1-1c0-1 0-1 1-2z" class="D"></path><path d="M428 575v-2h4v2c0 1-1 1-2 1v-1l-1-1-1 1z" class="I"></path><path d="M434 577h0v-1h1v4c0 1-1 1-2 1l-1-2h1l1-2z" class="C"></path><path d="M442 580h-1v-1c-1-1-2 0-3 0h-1c0-1-1-1-1-2 1-1 4 0 5 0h1s1 1 2 1l-2 2h0zm-10 5v1c1 1 2 0 3 0 0-2-1-3 0-4 1 1 0 3 1 5v5h-1v-2h-2v2h-2-3l-1-1h0 1l1-1v-3h1l2-2z" class="b"></path><path d="M432 589c0-1 0-1 1-2h1l1 3h-2l-1-1z" class="q"></path><path d="M429 590h2v-2h1v1l1 1v2h-2-3l-1-1h0 1l1-1z" class="C"></path><path d="M185 604h3 0 2c1 0 1 1 1 2 0 0 0 1 1 1h3 1v-2c1 1 1 1 2 1v-2h1l2 1-1 1h-1l-1 1h-1c-1 2-1 8 0 10h-1v-1c-3-1-5 0-8 0h-3l-3 1v-7c0-1 0-2-1-3h0c1-1 3-2 4-3h0z" class="m"></path><path d="M190 608h1v1c0 2 0 2-1 4h0c-1-1-1-4 0-5z" class="X"></path><path d="M187 614v-6h1v7h-1v-1z" class="q"></path><path d="M183 607h5v1h-1v6c0-1 0-4-1-5-2 0-3-1-4 1 0-1 0-2-1-3h2z" class="F"></path><path d="M185 604h3 0 2c1 0 1 1 1 2 0 0 0 1 1 1h-4-5-2 0c1-1 3-2 4-3h0z" class="K"></path><path d="M185 604h3 0 1c0 1 0 2-1 3h-2v-3h-1 0z" class="P"></path><path d="M181 607h0c1-1 3-2 4-3h1v3h-3-2z" class="T"></path><path d="M183 486v-1h0c1 0 1-1 2 0h0c2 3 5 6 8 6 1 0 1-1 2-1h1c2 0 4 0 5-1h1v1h0v1c1 0 2 0 2-1h0c3 0 2 0 3-2l1-1 1 2-1 1c0 1 0 1 1 1l1 2v1l2-1 1 1-2 2h-1c-2-1-6-4-8-3h0c0 2-1 2-2 3 0 1 0 1-1 1l1 1v1l-7-3v2l-1-1h-2v1h0c-1-1-2-2-3-2h0v-2c-1-1 0-1-1-2h0v-1-1l-1-1-1 1h-1c-1 0-1-1-2-1v-2c1-1 1-1 2-1z" class="C"></path><path d="M191 493c1-1 2-1 3 0l-1 1h-2v-1z" class="U"></path><path d="M190 497c-1-1-1-1 0-1 1-1 2 0 3 0v2l-1-1h-2z" class="Y"></path><path d="M188 490h1c1 1 1 1 1 2v1h-1c-1 0-1 0-1-1v-2z" class="D"></path><path d="M181 487c1-1 1-1 2-1l1 1v1h-1v1h1v1h-1c-1 0-1-1-2-1v-2z" class="B"></path><path d="M210 493v1l2-1 1 1-2 2h-1v-2l-2-1h0 1 1z" class="b"></path><path d="M324 258h2s1 0 1-1 0-1 1-1c1-1 2-1 3-2v4h1c1 1 1 4 1 6v8 2 1h-3-1c-2-1-4-1-5-1-1-1-1-1-2-1v-1l1-12s0-1 1-2h0z" class="Y"></path><path d="M324 258c1 5 0 11 0 16-1-1-1-1-2-1v-1l1-12s0-1 1-2z" class="k"></path><path d="M331 258h1c1 1 1 4 1 6v8 2 1h-3-1l2-1c1-1 0-5 0-7v-9z" class="c"></path><path d="M222 524c1 1 2 2 4 2-1 1-1 1-1 2v1 1l-1 2h-2c0 2 1 4 1 7v4 7c1 3 2 6 2 9-1 0-2-1-4 0h0-1c-1-1-1-1-2-1h-1v-4c2-1 4 0 6-1-1-2-4-1-6-2v-5h2v-1h-2 0v-9c1 0 1-5 1-6l2-1v-1-2h0l2-2z" class="F"></path><path d="M220 554h3v1c-2 0-2 0-3-1z" class="X"></path><path d="M222 524c1 1 2 2 4 2-1 1-1 1-1 2v1c-1 0-2-1-3-2-1 0-1-1-2-1l2-2z" class="I"></path><path d="M218 558h-1v-4h3c1 1 1 1 3 1v3l-2 1v-1c-1 0-1 0-1-1-1 0-2 1-2 1z" class="E"></path><defs><linearGradient id="M" x1="555.776" y1="382.553" x2="562.528" y2="397.354" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#4e4f4e"></stop></linearGradient></defs><path fill="url(#M)" d="M555 390v-3s-1 0-1-1h1 3v-1l-1-1h-1v1h0c0-1-1-1-2-2v2h-1v-1h0-2 0c-1 1-2 0-2 1h0 1l1 1c-1 1-1 1-1 2-1 0-1 0-2-1v-1-2h0 1 1 0 1c0-1 1 0 2 0h0v-3h0l1 1h1l1 1 1-1v-1h-1l-1-1-1-1h0l-1-1v-1l2-2v-5-1l2-1h-1c1-1 1-2 1-3 1 0 1-1 1-2h-1v-1l1-1c-2 0-2 0-2 1h-1l1-1c1 0 1 0 2-1h1c1 1 1 1 1 2-1 1-1 2-2 3h0v1c-1 1 0 2 0 3h-2v1c1 1 1 0 2 1v1c0 1 0 2-1 3h-1l-1 1s0 1-1 1v1s1 0 1-1h1c0 1 0 1 1 1v1h1v-2c1-1 1-1 2-1h0s0-1-1-1l1-2c0-1 1-2 2-3h0v1h0l-2 2c0 1 0 2 1 2v2h0v1 1h-1v2l2-2h1c1-1 1-2 1-2 0-1 0-2 1-3v-1-2c0-1 1-1 1-1v-1-1-1-1l1-1v-2-1c1-1 1-1 1-2v-1-1l1-2v-1-1-4h1c0 2 0 3-1 5 0 1 0 3-1 4l-2 10c-1 6-2 11-4 16 0 2-1 4-1 6-1 3-3 7-4 10h-1v-1-1h-2v-1l2-4v-1c0-1 1-2 2-2-1-1-1-1-1-2h0c-1 0-1 0-2 1z"></path><path d="M556 371h1c1 1 0 2 0 3h-1v-3z" class="W"></path><path d="M557 382h1c1 1 2 3 2 4h-1c-1-1-1-2-2-2v-2zm2-5c1 0 1 0 1 1s0 2-1 3c-1 0-1 0-1-1l1-1v-2z" class="C"></path><path d="M215 567h1c0 1 1 5 1 5 1 1 2 1 3 0h0c1 1 1 1 1 2h6v-1l1 1v1c1 1 1 0 2 0v1 1s-1 1-2 1c0 1 0 2 1 3 0 0 1 0 1 1l-1 1h-1 1v-1c0-1-1-1-2-2v1h-3-4-2-1c-1 0-1 0-1-1h0l-1 1h-4 0l-1-2h1 1v-1h-5s0-1-1-1v-2h0c0-1-1-1-2-1v-1h1c1-1 2-1 2-2h1v1c2 0 4 0 5 1 1-1 1-1 2-1h-1c-1-2-1-2-1-4h1v-1h1z" class="K"></path><path d="M216 579c1 0 2 0 3 1h2c0 1-1 1-1 1h-2-1c-1 0-1 0-1-1h0v-1z" class="P"></path><path d="M213 568h1v-1h1v5h-1c-1-2-1-2-1-4z" class="F"></path><path d="M212 579h1c1 0 2-1 2-1 1 1 1 0 1 1v1l-1 1h-4 0l-1-2h1 1z" class="i"></path><path d="M230 576v1s-1 1-2 1c0 1 0 2 1 3 0 0 1 0 1 1l-1 1h-1 1v-1c0-1-1-1-2-2-2 0-4 0-6-1h6v-1h-1 0c-1-1-2-1-2-2h0 3c1 1 1 1 2 1l1-1z" class="E"></path><path d="M227 573l1 1v1c1 1 1 0 2 0v1l-1 1c-1 0-1 0-2-1h-3 0c0 1 1 1 2 2h0-5c-1 0-2 0-3-1h-1v-3h1 3 0 6v-1z" class="B"></path><path d="M214 530c1 0 3-1 4 0 0 1 0 6-1 6v9h0 2v1h-2v5c2 1 5 0 6 2-2 1-4 0-6 1v4h1c1 0 1 0 2 1v1h-2l-1 1v-1h0l-1-1v-1h0-2v-1h-1c-1 0-1 0-1 1l-1-1v-1h0c0-1-1-1-1-2h-1v-2l1-16c1 0 1-1 1-2h-1l1-1h0l1 1 1 2v-3l-1-3h2z" class="L"></path><path d="M217 551c2 1 5 0 6 2h-7c1-1 1-1 1-2z" class="e"></path><path d="M209 552c2 0 3-1 5-1l1 1v1c-1 1-3 0-5 1h-1v-2z" class="T"></path><path d="M210 536c1 0 1-1 1-2h-1l1-1h0l1 1 1 2v9h-1-2v1l3 1h0 2 0v4h-1c-2 0-3 1-5 1l1-16z" class="f"></path><path d="M212 534l1 2v9h-1c-1-2 0-8 0-11z" class="S"></path><path d="M214 530c1 0 3-1 4 0 0 1 0 6-1 6v9h0c0-1 0-1-1-2h-1v2h-2v-9-3l-1-3h2z" class="d"></path><path d="M212 530h2v11 3s1 0 1 1h-2v-9-3l-1-3z" class="k"></path><path d="M215 543c1-2-1-7 1-9 0 1 0 1 1 1v1 9h0c0-1 0-1-1-2h-1z" class="N"></path><path d="M502 432c1-1 3-2 4-2l2 2h1 1c0 2-1 2 0 3 1 0 1 0 2-1h1v2l-1 1h1 2 2 0l-2 2 1 1c0 1 0 2 1 3l-2 2h-1-1-1c-1 0-2 0-3 1h0-4-1 0l-1 2c-1-1-2-1-3-1s-2 0-2-1c1 0 2 0 3-1v-2 1l-4-3c1-2 2-5 4-7 0 0 0-1 1-1v-1z" class="a"></path><path d="M504 446c-1-1-1-2-1-3 2-1 2-2 4-2-1 1-1 3-2 5h-1 0z" class="B"></path><g class="D"><path d="M502 433s1 1 1 2h0c-1 1-2 2-2 3-1 1-1 2-2 3l2 2v1l-4-3c1-2 2-5 4-7 0 0 0-1 1-1z"></path><path d="M502 432c1-1 3-2 4-2l2 2h1 1c0 2-1 2 0 3v1 1h-2c-1-2-1-2-2-3l-1 1h-2 0c0-1-1-2-1-2v-1z"></path></g><path d="M502 432c1 1 2 1 3 3h-2 0c0-1-1-2-1-2v-1z" class="d"></path><path d="M512 434h1v2l-1 1h1 2 2 0l-2 2 1 1c0 1 0 2 1 3l-2 2h-1-1-1c-1 0-2 0-3 1h0-4c1-2 1-4 2-5 0-1 1-1 1-2v-2h2v-1-1c1 0 1 0 2-1z" class="D"></path><path d="M508 437h2v1h1l2 2v1h1l-1 1-2 1-1-1h0l1-1-1-1c0-1 0-1-1-2h-1v1-2z" class="W"></path><path d="M515 439l1 1c0 1 0 2 1 3l-2 2h-1-1-1c-1 0-2 0-3 1 1-1 2-2 2-3l2-1 1-1 1-2z" class="d"></path><path d="M513 442h1c0 2-1 2-1 3h-1c-1 0-2 0-3 1 1-1 2-2 2-3l2-1z" class="c"></path><path d="M512 434h1v2l-1 1h1 2 2 0l-2 2-1 2h-1v-1l-2-2h-1v-1-1-1c1 0 1 0 2-1z" class="C"></path><defs><linearGradient id="N" x1="204.707" y1="515.488" x2="196.271" y2="522.052" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#N)" d="M186 507c1 1 1 1 2 1h1l1 2v1c1 1 1 1 2 1h0c1 0 2 1 2 2l2-1c1 1 2 2 3 2s2 2 4 2c1 0 2 1 2 2 2 1 2 1 4 1l-2 2h0 0c-1 0-1 1-2 1v2c1 0 2 1 3 1h1v1c-1 0-1 0-2 2l-1-1h-2c-2-1-3-1-5-2l-5-3h0c-1-1-2-2-4-2l-1-1c-1-1-1-1-1-2 0-2-1-2-1-3l-1-1c-1-1-3-2-3-4l-1-1h2 1c1-1 1-1 1-2z"></path><path d="M192 512c1 0 2 1 2 2l-1 1c-1 0-1-1-1-1h-1 0v-2h1z" class="H"></path><path d="M206 528v-1h0c0-1-1-1-2-2h1c1 0 2 1 3 1h1v1c-1 0-1 0-2 2l-1-1z" class="D"></path><path d="M186 507c1 1 1 1 2 1h1l1 2v1c1 1 1 1 2 1h0-1v2h0l-1 1 1 1h2v1h0v2h1 2c0 1 1 2 3 3h0c-1 1-1 1-1 2l1 2-5-3h0c-1-1-2-2-4-2l-1-1c-1-1-1-1-1-2 0-2-1-2-1-3l-1-1c-1-1-3-2-3-4l-1-1h2 1c1-1 1-1 1-2z" class="c"></path><path d="M187 515c1 0 1 0 2 1s2 2 2 3v1h1c1 1 2 2 2 3-1-1-2-2-4-2l-1-1c-1-1-1-1-1-2 0-2-1-2-1-3z" class="J"></path><path d="M186 507c1 1 1 1 2 1h1l1 2v1c1 1 1 1 2 1h0-1v2h-1c-1 0-2-1-2-1l-5-3-1-1h2 1c1-1 1-1 1-2z" class="Z"></path><path d="M188 513v-1c-1 0-1-1-1-2 1 0 2 0 3 1s1 1 2 1h0-1v2h-1c-1 0-2-1-2-1z" class="n"></path><path d="M204 503c0-1 1-1 1-1h0v-1l2-2c3 2 6 2 7 5 1 1 1 1 3 2 0 0 0 1 1 2l1-1h-1s-1-1-1-2v-1h1c1 1 3 3 5 3 1 1 1 1 1 3 2 0 3-2 4 1v2s-1-1-2 0c1 0 1 0 2 1v-1c0 2 1 2 1 3-1 1-1 1-1 2-1 0-2 0-3-1v-2l-1-1v1h-1c-1 0-1 0-2 1 0-2-1-2-2-3v-1c-1 1-1 2-2 2h0c-1 2-2 2-3 2l-3-2c-1-1-1-2-1-3-1-1-1-1-2-1h-1c-1-1-2-2-3-4v-3z" class="B"></path><path d="M219 513l1-1h2l1 1 1 1v1h-1c-1 0-1 0-2 1 0-2-1-2-2-3z" class="I"></path><path d="M204 506l1-1c1 0 2 1 2 2v1c1 0 2 0 2-1v-1c0-1-1-1-1-2 1 0 2-1 3-1v1 2s-1 0-1 1c0 0 1 0 1 1 0 0-1 1-2 1l-1 1h-1c-1-1-2-2-3-4zm7 8v-1l1-1h1c-1-1-2-1-2-1l-1-1h1c1 0 2 0 3 1h1l2 3h0c-1 2-2 2-3 2l-3-2z" class="E"></path><path d="M214 504c1 1 1 1 3 2 0 0 0 1 1 2l1-1h-1s-1-1-1-2v-1h1c1 1 3 3 5 3 1 1 1 1 1 3l-1-1c-2 0-2 1-3 1v-1-1c-2 0-2 1-4 0 1 0 1 0 1-1-1 0-1-1-2-1h0v2 1h-1-1v-4l1-1z" class="I"></path><path d="M368 630l2-1v1h2c-1 1-1 2-1 3l1 1h2 1c0 1-1 1-1 3l1-1c-1 2-2 1-2 3v-1h1l1-1h1 0l-4 4h1c2 0 3-1 4 0h4c-1 0-2 1-3 1 0 0 0 1-1 1h0l-2 1c-1 1-2 1-3 1h-4c-2 0-4 1-6 1-3 1-7 0-10 0h0 0v-2c1-1 1-1 2-1 0 0 0 1 1 1h2v-1l-1-1v-2l4-1 1-1c1-1 1-1 1-2h0c0-1 0-1 1-1v-1h-1v-1h1c1-1 1-1 2-1h0 3v-2z" class="F"></path><path d="M369 641v1c-1 1-2 1-3 2h-2c0-1 0-1 1-2h-4l1-1h6 1z" class="R"></path><path d="M352 646h0c2-1 1 0 1-1h1c1 0 1 0 2-1 1 1 2 1 3 0v-1h1l2 1v2c-3 1-7 0-10 0h0 0z" class="b"></path><path d="M372 641h1c2 0 3-1 4 0h4c-1 0-2 1-3 1 0 0 0 1-1 1h0l-2 1c-2-1-5 0-7 0 1-1 2-2 4-3z" class="D"></path><path d="M368 630l2-1v1h2c-1 1-1 2-1 3l1 1h2 1c0 1-1 1-1 3 0 0-1 0-1 1l-1 1h0-3v2h-1-6l-1 1c0 1 0 0-1 1l-2-2c1 0 1-1 2-2h0l1-1c1-1 1-1 1-2h0c0-1 0-1 1-1v-1h-1v-1h1c1-1 1-1 2-1h0 3v-2z" class="H"></path><path d="M361 639l2-1 2 1v1h-3l-1-1z" class="c"></path><path d="M366 636c2 0 3 0 5 1 0 0 1 0 2 1l-1 1c-2-1-3 0-4 0-2 0-3-1-3-2l1-1z" class="J"></path><path d="M366 636h2c1-1 1-2 2-3h1l1 1h2 1c0 1-1 1-1 3 0 0-1 0-1 1-1-1-2-1-2-1-2-1-3-1-5-1z" class="R"></path><path d="M368 630l2-1v1h2c-1 1-1 2-1 3h-1c-1 1-1 2-2 3h-2l-1 1-2 1-2 1h-1 0l1-1c1-1 1-1 1-2h0c0-1 0-1 1-1v-1h-1v-1h1c1-1 1-1 2-1h0 3v-2z" class="C"></path><path d="M363 633c1-1 1-1 2-1h0 2l-1 2h-2v-1h-1z" class="D"></path><path d="M368 630l2-1v1h-1 1v2c-1 1-2 0-3 0h-2 3v-2z" class="H"></path><defs><linearGradient id="O" x1="103.264" y1="404.087" x2="93.244" y2="416.933" xlink:href="#B"><stop offset="0" stop-color="#212221"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#O)" d="M91 391l1-1h0c1 0 1 1 2 1v1h1v-1 1c1 1 1 1 1 2v1h0l1 2c1 1 1 1 1 3h0v2s0 1-1 1l1 1h0 1-2v2c2 3 3 7 6 10 0 0 1-2 2-2v1c0 1-1 1-1 2h1-1v1 1s0 1 1 1c1 1 0 2 1 3 0 2 0 3 1 4v2c0 1 1 1 1 1 0 1 0 1 1 1l-1 1-2-1-1-2h0-1-1v-2l-1 1c0-1-1-1-2-1-1-2-2-5-3-7l-6-15 1-4v1h1l1-2c-1-1-2-6-2-8l-1-1h0z"></path><path d="M94 400c0 1 1 1 1 2h0c-1 1-1 1-1 2 0-1 0-1-1-2l1-2z" class="C"></path><path d="M102 425h2c1 1 1 2 1 3v1h-1-1v-2s-1-1-1-2z" class="R"></path><path d="M91 391l1-1h0c1 0 1 1 2 1v1h1v-1 1c1 1 1 1 1 2v1h0c-1 0-1 0-1 1 1 0 1 1 2 2v1c-2-1-3-3-4-4v-2l-1-1-1-1h0z" class="G"></path><path d="M103 421h-1c-1-1-1-2-2-3v-3l-1-1c0-1 0-2-1-3 0-2-1-3-1-5 2 3 3 7 6 10-1 1-1 2-1 3 0 0 1 1 1 2z" class="D"></path><path d="M103 416s1-2 2-2v1c0 1-1 1-1 2h1-1v1 1s0 1 1 1c1 1 0 2 1 3 0 2 0 3 1 4v2c0 1 1 1 1 1 0 1 0 1 1 1l-1 1-2-1-1-2h0v-1-1-1l-1-1v-1c0-1-1-2-1-3s-1-2-1-2c0-1 0-2 1-3z" class="F"></path><defs><linearGradient id="P" x1="101.48" y1="408.874" x2="92.6" y2="421.239" xlink:href="#B"><stop offset="0" stop-color="#505052"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#P)" d="M91 405l1-4v1h1c1 1 1 1 1 2l8 21c0 1 1 2 1 2l-1 1c0-1-1-1-2-1-1-2-2-5-3-7l-6-15z"></path><path d="M200 188c1 0 1 0 2-1v-2h1l2 2 1-1c1 2 2 6 3 9h-1 0-2l-2 2c-1 0-2 0-2 1-1-1-2-1-3-1s-2 1-2 2h1l2 2-1 1h-1c-1-1-1-1-2 0h0c-1 1-1 2-2 2v1l-1 1h-2 0l-1-1h0-2v-1-1l-2-1c1-1 1-2 1-2l1-1c1 0 1-1 2-1l-1-1h-1l-1 1h-1v-2c0-1 0-2 1-3l1 1h1c1-1 1-1 2-1h1c0-1 1-2 1-3 1 0 1-1 1-1 1 0 2 0 3-1l1-1v-1l1 1h0l1 1z" class="O"></path><path d="M199 187l1 1 1 1c0 1 0 1 1 1h-1c-1 0-2 1-3 0l1-1-1-1 1-1z" class="Q"></path><path d="M198 192h2c0 1 0 1 1 2v1l-1-1h-2-2l2-2z" class="T"></path><path d="M200 188c1 0 1 0 2-1v-2h1l2 2 1-1c1 2 2 6 3 9h-1 0l-1-3c-1-2-2-4-3-5 0 1-1 2-2 3h0c-1 0-1 0-1-1l-1-1z" class="L"></path><path d="M199 197v-1h2l3-3c0-1 0-2 1-3l1 1 1 1 1 3h-2l-2 2c-1 0-2 0-2 1-1-1-2-1-3-1z" class="Z"></path><path d="M188 194h1c1-1 1-1 2-1h1c0-1 1-2 1-3 1 0 1-1 1-1 1 0 2 0 3-1l1-1v-1l1 1h0l-1 1 1 1-1 1h-1c-1 1 0 1 0 2h1l-2 2v-1l-1 1c-1 1-1 1-1 3l1 1-1 2c-1-1-1-1-2-1 0 1 0 2-1 2v-2l-1-1 1-1v-1h0-1l-2-2z" class="V"></path><path d="M187 193l1 1 2 2h1 0v1l-1 1 1 1v2c1 0 1-1 1-2 1 0 1 0 2 1v1h1 2c-1-1-1-1-1-2h1 1l2 2-1 1h-1c-1-1-1-1-2 0h0c-1 1-1 2-2 2v1l-1 1h-2 0l-1-1h0-2v-1-1l-2-1c1-1 1-2 1-2l1-1c1 0 1-1 2-1l-1-1h-1l-1 1h-1v-2c0-1 0-2 1-3z" class="Q"></path><path d="M189 200c0 1 1 1 2 2 0 1-1 1 1 2h-1c-1 0-1 0-2-1v-3z" class="O"></path><path d="M186 202c1-1 1-2 1-2l1-1 1 1v3 2h2v1l-1-1h0-2v-1-1l-2-1z" class="Z"></path><path d="M194 201h1 2c-1-1-1-1-1-2h1 1l2 2-1 1h-1c-1-1-1-1-2 0h0c-1 1-1 2-2 2v1l-1 1h-2 0v-1h-2v-2c1 1 1 1 2 1h1c1-1 2-2 2-3z" class="K"></path><path d="M359 628l1 1v1c1 1 1 1 2 1 2 0 2 0 3 1h0c-1 0-1 0-2 1h-1v1h1v1c-1 0-1 0-1 1h0c0 1 0 1-1 2l-1 1-4 1v2l1 1v1h-2c-1 0-1-1-1-1-1 0-1 0-2 1v2h0 0c-3 1-5 1-8 1 0-1-1-1-2-1-1-1-3-1-4-1l-2-1 1-1h3 1 1l1-2 2-2h0l1-1v-1l2-2h-1-3 0v-1-2-1h3 3c1 0 2 1 3 1l1 1 1-1c1 0 2 0 3-1v-1c1 0 1-1 1-2z" class="N"></path><path d="M360 635c1 0 1 0 2 1h0c0 1 0 1-1 2h-3c0-1 1-1 1-2h2l-1-1z" class="J"></path><path d="M350 631c1 0 2 1 3 1-1 1-2 2-4 2h-1c1-1 1-2 2-3z" class="o"></path><path d="M347 631h3c-1 1-1 2-2 3l-1-2-3 2v-2-1h3z" class="L"></path><path d="M358 630l1 1c0 1-1 1-1 2h0c1 0 1 0 1 1h-1c-2 0-3 1-5 1h0c1 0 1-1 2-2h-1l1-1c1 0 2 0 3-1v-1z" class="C"></path><path d="M346 638c1-1 2 0 3-1v-2h1c1 1 1 1 1 2s-1 1-1 1h-1c1 1 3 1 4 1l-3 1v1h-4v-1c1-1 2-1 2-2-1 0-2 1-3 1l1-1z" class="n"></path><path d="M359 628l1 1v1c1 1 1 1 2 1 2 0 2 0 3 1h0c-1 0-1 0-2 1h-1v1h1v1c-1 0-1 0-1 1-1-1-1-1-2-1s-1-1-2-1h1c0-1 0-1-1-1h0c0-1 1-1 1-2l-1-1c1 0 1-1 1-2z" class="H"></path><path d="M358 638h3l-1 1-4 1v2l1 1v1h-2c-1 0-1-1-1-1-1 0-1 0-2 1v2h0 0c-3 1-5 1-8 1 0-1-1-1-2-1-1-1-3-1-4-1l-2-1 1-1h3 1 1l1-2 2-2h0c1 0 2-1 3-1 0 1-1 1-2 2v1h4v-1l3-1h0c2-1 3-1 5-1z" class="W"></path><path d="M342 646h1 1c2-1 5-1 7-1l1 1h0c-3 1-5 1-8 1 0-1-1-1-2-1z" class="F"></path><path d="M358 638h3l-1 1-4 1-3 1-1 1h-1v-1h-1v-1l3-1h0c2-1 3-1 5-1z" class="d"></path><path d="M326 304c2-1 5 0 7 0 4 0 8 0 12 1h0c0 2-1 2-2 3v1l-2 1h-5c0 1-1 1-1 2 1 0 3-1 4-1v1h-1v1c1 0 1 3 2 4v1c0 1 1 1 1 2h1c-1 0-3 1-4 0-4 0-3-3-5-5v2c-2-1-3-2-4-2l-1 1h-1v-1-2c-1 0-1 0-1-1l-1-1-1 1c-1-1-2-1-2-1-1-1-3-2-3-3h2v-1-1h1c-1-1-2-1-4-1 3-2 5-1 8-1z" class="E"></path><path d="M319 308h2l1 2h1v-1-1c1 1 1 1 1 2 1 0 1-1 2-1 0 0 0 3 1 4-1 0-1 0-1-1l-1-1-1 1c-1-1-2-1-2-1-1-1-3-2-3-3zm8 7h0c1-1 1-1 1-2 2-2 0-2 0-4h0l1 1c2-1 4-1 6 0h0c1-1 2-1 3-1h1c1-1 1-1 2 0 0 0 1 0 2-1v1l-2 1h-5c0 1-1 1-1 2 1 0 3-1 4-1v1h-1v1c1 0 1 3 2 4v1c0 1 1 1 1 2h1c-1 0-3 1-4 0-4 0-3-3-5-5v2c-2-1-3-2-4-2l-1 1h-1v-1z" class="G"></path><path d="M159 496l1-1c0-1 0-1-1-2v-1h1c1 1 2 2 3 4 1 0 1 1 2 1v1h0c0 1 0 0-1 1h2 1l2 2 1-1c1 1 1 2 2 2h3c0 1 1 1 2 2l4 2 5 1c0 1 0 1-1 2h-1-2l1 1c0 2 2 3 3 4l1 1c0 1 1 1 1 3 0 1 0 1 1 2l1 1 1 1c1 1 1 2 2 3h-1l-7-4v-1c-1-1-2-2-4-3-2-2-5-5-8-7-2-1-4-3-5-4-3-2-5-3-7-5l-4-4 1-1h1z" class="f"></path><path d="M170 500c1 1 1 2 2 2h3c0 1 1 1 2 2l4 2 5 1c0 1 0 1-1 2h-1-2l1 1c0 2 2 3 3 4-1 0-2-1-3-1-1-1-1-1-2-1l-1-1c-1-1-2-2-3-2-1-2-4-2-5-4-1-1-1-2-2-2v-1l-1-1 1-1z" class="J"></path><path d="M170 500c1 1 1 2 2 2h3c0 1 1 1 2 2-1 0-2 0-2 1-1-1-4-3-5-3l-1-1 1-1z" class="M"></path><path d="M177 504l4 2 5 1c0 1 0 1-1 2h-1-2c-3-1-5-3-7-4 0-1 1-1 2-1z" class="K"></path><path d="M158 496h1l2 1h0c0 1 4 5 5 6h1c1 1 2 3 4 3v1c1 0 1 1 2 1l2 1c0 1 1 1 1 1l3 3c3 2 6 5 10 7l1 1 1 1c1 1 1 2 2 3h-1l-7-4v-1c-1-1-2-2-4-3-2-2-5-5-8-7-2-1-4-3-5-4-3-2-5-3-7-5l-4-4 1-1z" class="M"></path><path d="M204 477h3l1-1v1c1 0 2 1 3 1 0 0 1 0 1-1h1c1 0 1 1 1 1 1 1 2 2 3 2l-1 1c1 1 1 1 2 1h1c1 0 1 1 1 1 0 2-1 1 0 3v1h0c1 0 1-1 1-1v-1h0v-1l2 2c0 1 0 2 1 3v1l1 1c1 1 2 1 3 2h1l-1 2-2 1c-4 0-6-4-9-5-1 1-1 2-2 3h-2l-1-1-2 1v-1l-1-2c-1 0-1 0-1-1l1-1-1-2-1 1c-1 2 0 2-3 2h0c0 1-1 1-2 1v-1h0v-1h-1c1 0 1-1 1-2v-1l-2-1v-1h1c1 0 1 1 2 1h1v-1c2-1 2-2 4-3v-1l-1-1h-2c-1-1-1-1-1-2z" class="a"></path><path d="M209 489l2 2h-1-1c-1 0-1 0-1-1l1-1z" class="I"></path><path d="M206 488h0v-1l-1-1-1-1 1-1c0 1 1 1 1 2 1 0 1 1 2 1h0l-1 1c-1 2 0 2-3 2l2-2z" class="B"></path><path d="M202 487c1 0 2 1 4 1l-2 2h0c0 1-1 1-2 1v-1h0v-1h-1c1 0 1-1 1-2z" class="G"></path><path d="M217 489c0 1 1 1 0 1v1c-1 1-1 2-2 3-2-1-3-2-4-3h1c2 0 3 0 5-2h0z" class="b"></path><path d="M211 480s0-1 1 0v1c0 1 0 1 1 2v-1-1h1l1 1c0 1 0 1 1 1h1 3c-1 1-2 1-3 1l-2 2h-1v-1-1h-2c0 1 0 1 1 2l-2 1c-1 0-2-2-3-2l1-1h1v-2c1-1 0-1 1-2zm6 9h0c0-1-1-2-1-2v-1h1 1s0-1 1-1c0 1 0 1 1 2v1h0c1 1 1 2 2 2v1c-1 0-1-1-2 0 1 1 2 0 3 1 0 0 0 1 1 1h2l-1-2c1 1 2 1 3 2h1l-1 2-2 1c-4 0-6-4-9-5v-1c1 0 0 0 0-1z" class="B"></path><defs><linearGradient id="Q" x1="214.073" y1="124.815" x2="216.975" y2="148.229" xlink:href="#B"><stop offset="0" stop-color="#434344"></stop><stop offset="1" stop-color="#5e5c5d"></stop></linearGradient></defs><path fill="url(#Q)" d="M243 149c-2-1-4-3-5-5-13-12-32-15-49-15h-16c-1-1-2-2-2-4 0 1 1 2 1 2v1h1c0 1 2 1 2 1 2-1 4 0 6 0 2-1 4-1 6-1h4 2c2-1 4-1 7-1h0-8l-1-1h-2 0 0l1-1h0 0 1 1 6 2 0 3c1 1 2 0 3 0 0 1 1 1 1 1h1 1 2c2 1 3 0 5 1h0 1 1c0 1 0 1 1 1h1l1 1h1 0c1 0 1 0 2 1h1l-1-1h1 0c1 0 1 1 1 1 1 0 2 1 3 1 0 1 0 1 1 1-1-1-3-2-4-3s-2-1-3-2v-1c9 5 16 14 20 23z"></path><path d="M174 468c1 0 3 0 4 1h0c1 0 2 0 3 1l1 1c1 1 1 2 1 4v1s1 0 1 1 0 2-1 3c1 0 1 1 2 2 0 1 1 1 1 1 1 0 1 0 1 1l1 1h2v1h-1c-1 0-1-1-2-1h-2 0c-1-1-1 0-2 0h0v1c-1 0-1 0-2 1v2s-1 0-1 1c0 0-1 0-1 1v-1l-1-2c-1 0-2 0-3-1h-1c-2-1-3-3-5-5h1l-1-1-1-1v-1-2-1c1-1 1-2 1-3v-1-1l2-2 1 1 2-2z" class="G"></path><path d="M180 485h-3c-1-1-1-1-1-2h1 1 2s1 1 2 1v1s-1 0-2 1v-1z" class="B"></path><path d="M178 488h0c1-1 1-1 1-2v-1h1v1l1 1v2s-1 0-1 1c0 0-1 0-1 1v-1l-1-2z" class="C"></path><path d="M182 471c1 1 1 2 1 4v1s1 0 1 1 0 2-1 3c-1-1-2-1-3-1h0c-1-2-1-2-1-4h1c1 1 1 1 2 1l1-1c-1-1-1-1-2-1 0-2 0-2 1-3z" class="I"></path><path d="M174 468c1 0 3 0 4 1h0c1 0 2 0 3 1l1 1c-1 1-1 1-1 3h-1c-1 0-1-1-3 0-1 0-1 1-3 1-1 0-1 0-2-1l-1 1h-1v-2h-1v-1-1l2-2 1 1 2-2z" class="X"></path><path d="M181 470l1 1c-1 1-1 1-1 3h-1c0-1 0-2-1-2l2-2zm-3-1h0v1c0 1-1 2-2 2h0c0-2 0-2 1-3h1z" class="b"></path><path d="M171 469l1 1h1c1 1 1 1 1 3h0-2v1l-1 1h-1v-2h-1v-1-1l2-2z" class="E"></path><path d="M171 469l1 1h1c-1 1-1 2-2 3h-1-1v-1-1l2-2z" class="q"></path><path d="M169 473h1v2h1l1-1c1 1 1 1 2 1l2 1 1 1h-1 0c0 1 1 2 1 3h1l1-1c1 0 1 1 2 1l1 1c0 1 0 1-1 1 0 0-1-1-2-1l-1-1-2 2h-2l-1 2c-1 0-1 0-2-1v-1l1-1h-3l-1-1v-1-2-1c1-1 1-2 1-3z" class="X"></path><path d="M168 480l1-1c1 0 2 0 3 1h1v1h-1-3l-1-1z" class="I"></path><path d="M169 473h1v2h1l1-1c1 1 1 1 2 1l2 1 1 1h-1 0c-1 0-2 0-3 1-1-1-2-1-4-1l-1 2v-2-1c1-1 1-2 1-3z" class="W"></path><path d="M322 251h5-3c0 2-1 5 0 7h0c-1 1-1 2-1 2l-1 12v1l-1 1c-2 1-7 0-9 0h-1v-15-6l1-1 1-1h-5 0 14z" class="X"></path><path d="M314 259v-1h1l1 1h-2z" class="I"></path><g class="F"><path d="M315 260h5l-2 1c-1 1-1 1 0 2h-3 0v-3z"></path><path d="M318 263h1c0-1 0-1 1-1v1c0 1 0 2 1 3l-2-1c-1 0-3 0-4 1v-3h3z"></path></g><path d="M315 263h3c-1 1-1 1-1 2h0 2c-1 0-3 0-4 1v-3z" class="m"></path><path d="M312 257l1 2h1 2c1-1 3-1 4-1 1 1 1 1 2 1-1 1-1 1-2 1h-5l-2 1v7 3c-1-1-1-12-1-14z" class="U"></path><path d="M315 260v3h0v3 1h3v1h0c-2 0-3 1-4 2v1c-1 1-1 2-1 3v-3-3-7l2-1z" class="I"></path><path d="M315 266c1-1 3-1 4-1l2 1v1l-1 1h-1l2 1c0 1 0 1-1 2h-3-3v-1c1-1 2-2 4-2h0v-1h-3v-1z" class="D"></path><path d="M311 259v-6l1-1v5h0c0 2 0 13 1 14v3c0-1 0-2 1-3h3c1 0 4 0 5 1v1l-1 1c-2 1-7 0-9 0h-1v-15z" class="K"></path><path d="M314 271h3c1 0 4 0 5 1v1l-1 1c-3-1-5 0-8 0 0-1 0-2 1-3zm8-20h5-3c0 2-1 5 0 7h0c-1 1-1 2-1 2 0-1 0-2-1-3h0l-1-1c-1 0-1-1-2-1l-1 1h0 0l-1-1 1-1c0-1-1-2-1-2-1 1 0 2-1 2-1 2-2 2-4 3v-5l1-1h-5 0 14z" class="r"></path><path d="M322 251h5-3c0 2-1 5 0 7h0c-1 1-1 2-1 2 0-1 0-2-1-3v-6h-9-5 0 14z" class="m"></path><path d="M209 554h1c0 1 1 1 1 2h0v1l1 1c0-1 0-1 1-1h1v1h2 0v1l1 1h0v1l1-1h2v-1h1 0c2-1 3 0 4 0v3l3 3v2h-2c0 3 1 2 0 4v2h1v1h-6c0-1 0-1-1-2h0c-1 1-2 1-3 0l-1-5h-1-1v1h-1c0 2 0 2 1 4h1c-1 0-1 0-2 1-1-1-3-1-5-1v-1h-1v-1c0-1 0-3-1-4h-2l1-1c0-1 1-2 2-2 0-3 2-6 2-9z" class="D"></path><path d="M218 560l2 1v1l1 1 1 1h0 2l1 1v1c-2 1-6 0-7 0h2v-2c0-2-2-1-3-2v-1l1-1z" class="N"></path><path d="M218 560l2 1v1c-1 1-2 1-3 0v-1l1-1z" class="E"></path><path d="M206 566h5 0-2c-1 1-1 3-1 5v1h1c0-2 0-4 1-5h0c1 1 1 1 1 2 2 0 1-1 2-1 0 2 0 2 1 4h1c-1 0-1 0-2 1-1-1-3-1-5-1v-1h-1v-1c0-1 0-3-1-4z" class="o"></path><path d="M228 565v2h-2c0 3 1 2 0 4v2h1-1l-1-1-1 1h-3v-1c0-2-1-4 0-5h1v4h1c1-1 0-3 1-4h0 3l1-2z" class="G"></path><path d="M209 554h1c0 1 1 1 1 2h0v1l1 1c0-1 0-1 1-1h1v1h2 0v1l1 1h0v1 1c1 1 3 0 3 2v2h-2-6-1 0-5-2l1-1c0-1 1-2 2-2 0-3 2-6 2-9z" class="P"></path><path d="M207 563l1-1c1 2 7 0 8 2v2c-2 0-3-1-4 0h-1 0-5-2l1-1c0-1 1-2 2-2z" class="L"></path><path d="M203 628c1 0 2-1 3-1v1h-2l1 2h3l-1 1h-2s-1 1-1 2l1 1h-1v1h0c-2 2-3 2-5 3l-1 1h2c0 1 1 1 1 2h-7c-2 1-4 1-7 1h0-3v-1h-3c-1 0-2 0-3 1 0-1-1-1-1-2h-6l-3 1h-4c-2-1-3 0-5-1 0 0 0-1-1-1h0l1-2 1-1v1l3-2c1 0 1-1 3-1l2 2h-1v1c1 0 1 1 2 1h1v-1h1 1c1 0 1 0 2-1 1 0 2 0 3-1h0l1 1h1c1 0 2-1 3-1h3 0c1 0 2-1 3-1 2 1 3 1 4 1h2v-2c3-1 6-3 9-5z" class="S"></path><path d="M194 641h-1c0-1 0-1 1-1 1-1 3-1 4-2h1l-1 1h2c0 1 1 1 1 2h-7zm-9-6h0c1 0 2-1 3-1 2 1 3 1 4 1l-1 1c-1 1-2 2-4 2l-2 1s-1 1-2 1c-1-1-2-1-3-1l1-1h0-3-2v-1l1-1h1 1c1 0 2-1 3-1h3z" class="f"></path><path d="M185 635h0c1 0 2-1 3-1 2 1 3 1 4 1l-1 1c-1 1-2 2-4 2l-2 1c0-1-1-1-2-1h-1l3-2v-1z" class="o"></path><path d="M163 635c1 0 1-1 3-1l2 2h-1c-1 1-1 1-1 2v1h0 1 3c1 0 1-1 2-1h0l-1 2-3 1h-4c-2-1-3 0-5-1 0 0 0-1-1-1h0l1-2 1-1v1l3-2z" class="U"></path><path d="M163 635c1 0 1-1 3-1l2 2h-1c-1 1-1 1-1 2h-3 0c-1 1-2 1-3 1h-1l1-2 3-2z" class="e"></path><path d="M203 628c1 0 2-1 3-1v1h-2l1 2h3l-1 1h-2s-1 1-1 2l1 1h-1-2c-1 0-1 1-2 1s-2 1-2 1c-1 1-2 1-4 1 0 1-1 1-2 2h0c-1 1-1 1-2 1h0-3v1l-1-1 6-3v-1h-1l1-1h2v-2c3-1 6-3 9-5z" class="N"></path><path d="M197 633c2 1 3 0 5 0h1 1l1 1h-1-2c-1 0-1 1-2 1h-7l4-2z" class="M"></path><path d="M197 633l7-5 1 2h3l-1 1h-2s-1 1-1 2h-1-1c-2 0-3 1-5 0z" class="L"></path><path d="M107 427l1-1c0-1 0-1-1-2h1s1 1 1 2c1 1 1 2 2 3h1c1 0 1 1 2 2h2v-1s0 1 1 1h0 1c0 1 0 2 1 2v2 1-1l-2 2c0 2 2 1 1 4 0 1 1 1 2 2-1 1-1 2-1 2l1 1 1-2 1 1v1l1 2 2 2h0c0 1 1 2 1 2v1h1l1-2v3l-1 1 1 2v2c1 1 1 1 2 1v1 1h0l-1 2c-1 0-1-1-2-1h0 0c-1 0-2 0-2-1-1 0-2-1-2-1h-1c1 1 1 1 1 2-1 0-1-2-2-2l1-2c-1-2-1-2-1-4-1-2-4-5-5-7s-3-3-4-5c-1-3-3-5-4-8l-2-4 2 1 1-1c-1 0-1 0-1-1 0 0-1 0-1-1v-2z" class="H"></path><path d="M119 445l1 1v2h-2-1l1-1s1-1 1-2z" class="U"></path><path d="M123 448s-1 1-1 2h0 0-1v-3l1-1 1 2zm2 2h0c0 1 1 2 1 2-1 1-2 1-3 1h0c0-1 1-3 2-3z" class="R"></path><path d="M109 426c1 1 1 2 2 3v2l-1 1c0-1-1-2-1-3-1-1 0-2 0-3z" class="F"></path><path d="M116 448c1 1 1 1 2 1h0c1 1 3 3 3 5h1l1-1c1 0 2 0 3-1v1h1l1-2v3l-1 1 1 2v2c1 1 1 1 2 1v1 1h0l-1 2c-1 0-1-1-2-1h0 0c-1 0-2 0-2-1-1 0-2-1-2-1h-1c1 1 1 1 1 2-1 0-1-2-2-2l1-2c-1-2-1-2-1-4-1-2-4-5-5-7z" class="S"></path><path d="M124 456c1-1 2-1 3-1l1 2c-2 0-2 0-4-1z" class="R"></path><path d="M128 451v3l-1 1c-1 0-2 0-3 1 0-1 0-2-1-3 1 0 2 0 3-1v1h1l1-2z" class="H"></path><path d="M121 455c1 1 2 2 4 3-1 1-1 2-2 2l-1-1c-1-2-1-2-1-4z" class="g"></path><path d="M125 459c2 1 4 1 5 3h0l-1 2c-1 0-1-1-2-1h0c0-2-1-3-2-4z" class="J"></path><path d="M125 458v1c1 1 2 2 2 4h0c-1 0-2 0-2-1-1 0-2-1-2-1h-1c1 1 1 1 1 2-1 0-1-2-2-2l1-2 1 1c1 0 1-1 2-2z" class="k"></path><path d="M112 429c1 0 1 1 2 2h2v-1s0 1 1 1h0 1c0 1 0 2 1 2v2 1-1l-2 2c0 2 2 1 1 4 0 1 1 1 2 2-1 1-1 2-1 2 0 1-1 2-1 2l-1-1c-2-2-4-4-5-7 0-1-1-3-2-4v-1-1-1l1-1v-2h1z" class="D"></path><path d="M110 433c1-1 1-1 2-1v1h0l-1 2h2l1 1h-1v2l1 1h-2c0-1-1-3-2-4v-1-1z" class="F"></path><path d="M112 429c1 0 1 1 2 2h2v-1s0 1 1 1h0 1c0 1 0 2 1 2v2 1-1l-2 2c0 2 2 1 1 4-2-3-3-5-4-7-1 0-1-1-2-1h0v-1c-1 0-1 0-2 1v-1l1-1v-2h1z" class="m"></path><path d="M114 434c1-1 1-2 3-3l2 4-2 2c0 2 2 1 1 4-2-3-3-5-4-7z" class="b"></path><path d="M170 625h1s1 0 1 1h0c1-1 3-1 4-1s1-1 2-1h0l-2 2h-1c1 1 1 2 2 2 2 1 4 1 6 0 1 0 3 0 4 1h0c0 1-1 1-1 1h1v1h-1v1c-1 0-1 1-1 1h2l1 1c-1 0-2 1-3 1h0-3c-1 0-2 1-3 1h-1l-1-1h0c-1 1-2 1-3 1-1 1-1 1-2 1h-1-1v1h-1c-1 0-1-1-2-1v-1h1l-2-2c-2 0-2 1-3 1l-3 2v-1-1c-2 0-3 0-4-1s-3-1-4-2l1-1 1-1 2-4c2 1 2 1 4 1l6-2h4z" class="M"></path><path d="M166 625h4l-1 1h0 2v1c-2 0-3 1-4 1l-1 1v-1c0-1 0-1 1-2h0l-1-1z" class="V"></path><path d="M167 628c1 0 1 1 2 1h0c1 0 3-1 4-2l1 1c-2 1-4 2-5 4h-2l1-1v-1h-3v1l-1-1 1-2h1v1l1-1z" class="o"></path><path d="M174 628c1 0 1 0 3 1h0l1 1h0 0c-1 1-2 1-2 1h-1c-1 0-1 1-2 1h-1-3c1-2 3-3 5-4z" class="V"></path><path d="M177 629c2 1 3 1 4 1h1 2 0 2 1v1h-1v1c-1 0-1 1-1 1h2l1 1c-1 0-2 1-3 1h0-3c-1 0-2 1-3 1h-1l1-1h0v-1l-1-1s-1 1-2 0v-1-1h-1 1s1 0 2-1h0 0l-1-1z" class="P"></path><path d="M176 631c2 0 5 1 6 0 0 2 0 2-2 3h-1l-1-1s-1 1-2 0v-1-1z" class="l"></path><path d="M182 631l4 1c-1 0-1 1-1 1h2l1 1c-1 0-2 1-3 1h0-3c-1 0-2 1-3 1h-1l1-1h0v-1h1c2-1 2-1 2-3z" class="M"></path><path d="M182 631l4 1c-1 0-1 1-1 1l-1 1-1-1-4 2h0v-1h1c2-1 2-1 2-3z" class="f"></path><path d="M165 630h3v1l-1 1h2 3 1c1 0 1-1 2-1h1v1 1c1 1 2 0 2 0l1 1v1h0l-1 1-1-1h0c-1 1-2 1-3 1-1 1-1 1-2 1h-1-1v1h-1c-1 0-1-1-2-1v-1h1l-2-2v-1h1c0-1-1-2-2-3z" class="M"></path><path d="M175 631h1v1 1c1 1 2 0 2 0l1 1v1h0l-1 1-1-1h0c-1 1-2 1-3 1-1 1-1 1-2 1h-1-1l-1-1 1-1h3c0-1 1-1 1-2-1 0-2-1-2-1h1c1 0 1-1 2-1z" class="L"></path><path d="M175 631h1v1 1c1 1 2 0 2 0l1 1v1h0l-1 1-1-1h0c-1 1-2 1-3 1l3-1-2-2h-1c-1 0-2-1-2-1h1c1 0 1-1 2-1z" class="N"></path><path d="M166 625l1 1h0c-1 1-1 1-1 2h-1l-1 2 1 1v-1c1 1 2 2 2 3h-1v1c-2 0-2 1-3 1l-3 2v-1-1c-2 0-3 0-4-1s-3-1-4-2l1-1 1-1 2-4c2 1 2 1 4 1l6-2z" class="l"></path><path d="M156 626c2 1 2 1 4 1h0c-2 1-4 3-5 3h-1l2-4z" class="Q"></path><path d="M164 630c-2 0-3 1-5 0v-1c2-1 4-2 5-2l1 1-1 2z" class="e"></path><path d="M153 631c1 1 2 1 3 1h6 2 0c1 1 1 1 2 1v1c-2 0-2 1-3 1l-3 2v-1-1c-2 0-3 0-4-1s-3-1-4-2l1-1z" class="g"></path><path d="M162 632h2 0c1 1 1 1 2 1v1c-2 0-2 1-3 1s-2-1-3-1h-2v-1h3c1 0 1 0 1-1z" class="k"></path><path d="M368 553v2c1-1 2-2 4-3h1c1 0 2-1 3-1v-2h1v1h1l2 1 2 2v1c1 1 1 2 1 3 1 1 1 2 2 3h0-2v1c1 1 4 2 6 3h-5 0-3v1 4h0 4c-1 1-2 1-3 2h-1v1h-1 0v2 1c-2 1-3 1-5 1h-1v-4h0-2l-1-1v-1c0-1-1-2 0-3 0-2-1-5 0-7h1c-1-1-2-1-3-2h-1v-2l-1 1h-1v-1h-1 0l2-1h-1l-2-1h0v-2l1 2h1l1-2 1 1z" class="G"></path><path d="M368 556s1 0 1-1l1 1v2h-1-1v-2z" class="I"></path><path d="M376 569v-1c1-1 1-1 1-2v-1h2v3c0 1-1 1-2 1h-1 0z" class="Y"></path><path d="M377 550h1l2 1 2 2v1c-1-1-3-1-4-1 0 1-1 1-1 1-1 0 0-1-1-1-3-1-3 5-6 5h0c0-1 1-1 1-2l1-1h0c0-1 0-1 1-2 2-1 3-1 4-2v-1z" class="W"></path><path d="M374 564h10 0-3v1 4h0 4c-1 1-2 1-3 2h-1v1h-1 0v2 1c-2 1-3 1-5 1h-1v-4h0-2l-1-1v-1h0c2 0 3-1 5-1h0 3c1-1 1-3 1-5-2 0-4 1-6 0h0z" class="M"></path><path d="M372 572h0c2 0 2 0 3-1 1 1 1 1 3 1h1 0l1 3c-2 1-3 1-5 1h-1v-4h0-2z" class="X"></path><path d="M377 554s1 0 1-1c1 0 3 0 4 1s1 2 1 3c1 1 1 2 2 3h0-2v1c1 1 4 2 6 3h-5-10c0-2 2-2 2-3 0 0-1-1-1-2s0-3 2-5z" class="Q"></path><path d="M326 631c1 0 2 1 4 1h0v2c-1 0-1 1-1 1h-4c-4 1-6 2-9 3 0 1-1 1-1 1l-3 3c-1 1-2 1-3 2l4-1h2 0l1 1c-1 0-1 1-2 1 0 0-1 0-1 1-2 1-4 0-6 1h0v-1c1-1 2-2 2-3h0l-4 2-1-1h-2l-2 1h0c-1 0-2 1-3 1-1 1-1 1-2 1h-2 0v-1l1-1v-1h-1c-1 0-1-1-2-1h0v-1l1-1s0-1-1-1l1-2h-1v-1h-1l-1-1h3l2-1 1-1h1c1 0 2 0 4-1 1 1 1 1 1 2h-1v1h3 1 0c2-1 4 0 6-1l1-1h8c1-1 3-1 4-2 1 0 2 0 3-1z" class="D"></path><path d="M305 642c-1 0-2 0-2-1l1-1h3l-2 2z" class="J"></path><path d="M298 642l1-1c1 1 2 2 3 2h1l1 1h-1-1l-2 1h0c-1-1-2-1-3-1 0-1 1-1 1-2zm9-2c1 0 2 0 3-1 2 0 4 0 6-1h0c0 1-1 1-1 1-2 0-3 1-5 2v1h-2-1-2l2-2z" class="S"></path><path d="M326 631c1 0 2 1 4 1h0v2-2c-1 1-2 1-3 2h-2l-1-1c-1 0-1 1-2 1h-3c1-1 3-1 4-2 1 0 2 0 3-1z" class="d"></path><path d="M303 636h1l2 1c0 1-4 3-5 3l-2 1-1 1-2-1h0c1-1 1-1 2-1 2-1 4-2 5-4z" class="J"></path><path d="M296 641l2 1c0 1-1 1-1 2 1 0 2 0 3 1h0 0c-1 0-2 1-3 1-1 1-1 1-2 1h-2 0v-1l1-1v-1l1-1 1-1v-1z" class="D"></path><path d="M295 643v1 1l2 1c-1 1-1 1-2 1h-2 0v-1l1-1v-1l1-1z" class="R"></path><path d="M304 640c1-1 3-3 4-3h7l1 1c-2 1-4 1-6 1-1 1-2 1-3 1h-3zm-4-4h3c-1 2-3 3-5 4-1 0-1 0-2 1h0v1l-1 1-1 1h-1c-1 0-1-1-2-1h0v-1l1-1 3-2c2-1 3-2 4-3h1z" class="N"></path><path d="M291 643c0-1 2-1 2-1 1 0 1 1 3 0l-1 1-1 1h-1c-1 0-1-1-2-1h0z" class="S"></path><path d="M296 634c1 0 2 0 4-1 1 1 1 1 1 2h-1v1h-1c-1 1-2 2-4 3l-3 2s0-1-1-1l1-2h-1v-1h-1l-1-1h3l2-1 1-1h1z" class="R"></path><path d="M292 638c2 0 2 0 3 1l-3 2s0-1-1-1l1-2z" class="C"></path><path d="M289 636h3 5c-1 0-1 1-2 1h-4-1l-1-1z" class="W"></path><path d="M296 634c1 0 2 0 4-1 1 1 1 1 1 2h-1v1h-1c-1 0-1 0-1-1l-1 1h-5l2-1 1-1h1z" class="H"></path><path d="M298 635s1-1 2-1v1 1h-1c-1 0-1 0-1-1z" class="W"></path><path d="M332 125l1-1 1 1h1 0c0 2 0 4 2 4v1h-1c0 3 2 6 3 9 2 9 4 18 5 28 0 3 1 7 1 10 0 1 0 3-1 4h0c1 2 0 3 1 5v1h-1v-3c-1-2 0-3-1-4 0-2 0-4-1-6l-1-12h0-1-3c0-1-1-1-2-1h-2l1-1v-2c0-3-1-6-1-10v-4c-1-2-1-3-1-4h0c0-1-1-1-1-2h0l1-1c0-1-1-2-1-3h1l-1-1c0-2 0-4 1-5v-1h-1v-1l1-1z" class="D"></path><path d="M333 138c0 1-1 0-1 0h0 1v-4h0 1c1 1 2 2 2 4h-3z" class="R"></path><path d="M333 138h3l5 24h0-1-3c0-1-1-1-2-1 0-5-1-11 0-16v-1c-1 0-1-1-1-2-1-1-1-1 0-3l-1-1z" class="F"></path><path d="M504 455c0 1 0 2 1 2l-2 1c1 1 0 1 1 1s1 1 2 1c-2 1-2 2-4 3-1 1-1 2-2 3h0l-1 1-1 1v1h-2v-1l-2 2v1h-1 0l-3 3v2c-2 1-3 3-4 5l-2-2v1c-1 0-1 0-1 1h-1 0c-1 0-2 0-3-1v1h0c0 1 0 1-1 2h0l-1 1s-1-1-1-2c0 0 2-1 2-2-1-1-2-2-3-2l-1-1 1-4h4v-2-2l1-1c2-1 4-3 6-3 2-1 4-3 6-4l4-3 1 1h3s1-1 1-2h1c1 0 1-1 2-2z" class="D"></path><path d="M490 467c1 1 1 1 2 3h-1v1c-1 0-2 1-3 1v-3l2-2z" class="F"></path><path d="M479 469l1-1c2-1 4-3 6-3v1c-2 0-2 1-3 2h-1l1 2c-1 0-1 0-1 1-1-1-1-1-2-1l-1 1v-2z" class="R"></path><path d="M492 465c1 0 3 0 3 1l1 1v1l-2 2v1h-1c-1 0-1 0-2-1h1c-1-2-1-2-2-3l2-1v-1h0z" class="W"></path><path d="M495 466l1 1v1l-2 2s-1-2 0-3c0 0 1 0 1-1z" class="R"></path><path d="M492 466h1v3l-1 1c-1-2-1-2-2-3l2-1z" class="b"></path><path d="M484 473v-1h3v1c1 0 1 0 1-1l1 1h0l1 1h0v2c-2 1-3 3-4 5l-2-2v1c-1 0-1 0-1 1l1-3c0-1 0 0-1-1h0v-3l1-1z" class="d"></path><path d="M484 473l1-1 1 2h-1v3h-1v1h0c0-1 0 0-1-1h0v-3l1-1z" class="c"></path><path d="M490 474h0v2c-2 1-3 3-4 5l-2-2v1c-1 0-1 0-1 1l1-3h0v-1h1c1 0 2-1 3-2v-1h2z" class="J"></path><path d="M492 461l4-3 1 1h3c-1 1-1 2-2 3h-1c-1 1-1 1-2 1l-1-1 1-1h-1l-2 4h0v1l-2 1-2 2-1 2h0-2c-1-2 2-3 3-4h-1-2l1-1v-1c2-1 4-3 6-4z" class="S"></path><path d="M497 459h3c-1 1-1 2-2 3 0-1-1-2-1-3zm-5 6h-1-1v-1h0l2-2h0c1-1 1-1 2-1l-2 4h0z" class="n"></path><path d="M479 473c1-1 2-2 3-2l1 1c0 1-2 1-1 2h1v3h0c1 1 1 0 1 1l-1 3h-1 0c-1 0-2 0-3-1v1h0c0 1 0 1-1 2h0l-1 1s-1-1-1-2c0 0 2-1 2-2-1-1-2-2-3-2l-1-1 1-4h4 0z" class="H"></path><path d="M475 473h4 0l-1 1s-2 0-3 1h3 0c0 1-1 1-1 2l-2-1v2l-1-1 1-4z" class="R"></path><path d="M504 455c0 1 0 2 1 2l-2 1c1 1 0 1 1 1s1 1 2 1c-2 1-2 2-4 3-1 1-1 2-2 3h0l-1 1-1 1v1h-2v-1-1l-1-1c0-1-2-1-3-1l2-4h1l-1 1 1 1c1 0 1 0 2-1h1c1-1 1-2 2-3 0 0 1-1 1-2h1c1 0 1-1 2-2z" class="g"></path><path d="M497 465v-2h1 1v2h-2z" class="N"></path><path d="M497 465h2v1h1l-1 1-1 1v1h-2v-1-1c0-1 0-1 1-1v-1z" class="p"></path><path d="M497 465h2v1l-1 1h-1v-1-1z" class="k"></path><path d="M430 147c-4-2-8-2-12-3-16-3-31-3-46-3-5 0-11 1-16 1-2-1-3-2-5-3h0 1 0l1 1h1 0 1v1c2 0 4-1 6-1h3 1 8c1-1 1 0 2 0l1-1h8c1 0 3 0 4 1h7 10 6 1 1l9 2c8 1 16 4 24 7 1 1 3 2 4 3h1c1 0 2 1 3 1l7 5c1 1 3 3 4 3h1c5 3 9 8 11 13 0-1-1-1-1-1-1 0-2-2-3-2l-6-6c-7-4-13-8-20-12-4-2-8-4-13-5 0 1-1 1-1 2l-1-1s0-1 1-1v-1h-3z" class="U"></path><path d="M326 629c2-1 3-1 5-1l-1 1v1h3v1 1c1-1 2-1 3-1h2c2-1 3 0 5 0v1h1v2 1h0 3 1l-2 2v1l-1 1h0l-2 2-1 2h-1-1-3l-1 1h-3 0-5c-1 0-1 0-3 1h-4c0-1-1 0-1 0v-2h-1c-1-1-1-2-2 0-1 0-1 0-1 1l-1-1h0-2l-4 1c1-1 2-1 3-2l3-3s1 0 1-1c3-1 5-2 9-3h4s0-1 1-1v-2h0c-2 0-3-1-4-1-1 1-2 1-3 1h0l1-2 2-1z" class="g"></path><path d="M324 630l2-1 1 1c0 1 0 1-1 1-1 1-2 1-3 1h0l1-2z" class="Z"></path><path d="M338 641h5 0l-1 2h-1-1c0-1-1-1-2-1h0v-1z" class="D"></path><path d="M343 632h1v2 1c-2 0-3 1-4 0l3-3zm-3 1l-2 2-1-1c-1 1-2 1-3 1s-2-1-2-1l1-2c-1 0-1 0-1-1h1v1 1c2 1 5 1 6 0h1z" class="M"></path><path d="M336 631h2l3 1-1 1h0-1c-1 1-4 1-6 0v-1c1-1 2-1 3-1z" class="K"></path><path d="M329 637c0-1 1-1 1-2 2 1 6 1 7 2v1c-1 0-2-1-3-1s-3 1-4 1l-1-1z" class="M"></path><path d="M329 641c0-1-1-1-1-2 1 0 1 1 2 1 2 0 4 0 6-1 0 1 0 1 1 2h1v1h0c1 0 2 0 2 1h-3l-1 1h-3l1-2h-1c0-1-1-1-1-2l-1 1h-2z" class="H"></path><path d="M312 642l1 1c1 0 2-1 3-2 1 0 4 1 5 0 1 0 2-1 2-2h0v-1h0c1-1 2-1 2-1h1 0c1 0 2-1 3-1v1l-1 1h-1c-1 0-2 1-3 2l-1 1v1c-1 0-2 1-3 1h-1c-1-1-1-2-2 0-1 0-1 0-1 1l-1-1h0-2l-4 1c1-1 2-1 3-2z" class="J"></path><path d="M340 635c1 1 2 0 4 0h0 3 1l-2 2v1l-1 1h0-5-2v-1c1-1 1-1 1-2 0 0 1 0 1-1z" class="Z"></path><path d="M340 639c1-1 0-1 0-2h0 1 1 1 3v1l-1 1h0-5z" class="p"></path><path d="M324 640c1-1 2-2 3-2h1c-1 1-1 2-2 3 1 0 1 0 2 1l1-1h0 2l1-1c0 1 1 1 1 2h1l-1 2h0-5c-1 0-1 0-3 1h-4c0-1-1 0-1 0v-2c1 0 2-1 3-1v-1l1-1z" class="S"></path><path d="M324 640l1 1c-1 1-3 2-4 4 0-1-1 0-1 0v-2c1 0 2-1 3-1v-1l1-1z" class="F"></path><path d="M333 642h1l-1 2h0-5c0-1 0-1 1-2h1c1 0 1 1 2 1l1-1z" class="R"></path><path d="M387 626h0c1 0 1 0 2 1h6 1v1h-1v1c4 1 6 2 9 4h1l2 2 1 1v1l-1-1-1 2v1l-1 1-1-1h0-2-1l1-2h-2l1 2h-2s0 1-1 1l-1-1v-1c-1 0-2 1-2 1h-1-1l-1-1c-1 0-3 1-4 0h0-1c-1 1-2 1-3 1s-2 0-2-1h-1l1 1-1 2h-4c-1-1-2 0-4 0h-1l4-4h0-1l-1 1h-1v1c0-2 1-1 2-3l-1 1c0-2 1-2 1-3h-1c0-1-1-1-2-1v-1c1-1 2-1 3-1s3 0 4-1c1 0 2-1 2-1 1-1 2-1 3-1 1-1 2-1 3-2z" class="g"></path><path d="M399 635c2 0 3 0 5 1h3l-1 2v1l-1 1-1-1v-3h-3l-2-1z" class="R"></path><path d="M394 635h-6 0c0-1 0-1 1-1h3c2-1 5 0 7 1l2 1h3v3h0-2-1l1-2h-2c-1-1-4-2-6-2z" class="C"></path><path d="M394 635c2 0 5 1 6 2l1 2h-2s0 1-1 1l-1-1v-1c-1 0-2 1-2 1h-1-1l-1-1c-1 0-3 1-4 0h0-1v-1c2 0 3 0 4-1 2 0 2 0 3-1z" class="H"></path><path d="M381 633l1-1c4-3 8-3 13-3 4 1 6 2 9 4-1 0-2 0-3-1h-1 0c-2 1-3 0-5-1 0 1-1 1-2 1h-5s-1 1-2 1h-2c-1 0-2 1-3 0z" class="P"></path><path d="M377 637c1-2 2-3 4-4 1 1 2 0 3 0l-2 2h2l2 2c1 0 1 0 2-1h3c-1 1-2 1-4 1v1c-1 1-2 1-3 1s-2 0-2-1h-1l1 1-1 2h-4c-1-1-2 0-4 0h-1l4-4h1z" class="S"></path><path d="M376 637h1 0 2v1h-3c0 1 1 1 1 2 1 1 1 0 0 1-1-1-2 0-4 0h-1l4-4z" class="J"></path><path d="M377 637c1-2 2-3 4-4 1 1 2 0 3 0l-2 2v1h-2v2h-1v-1h-2 0z" class="o"></path><path d="M387 626h0c1 0 1 0 2 1h6 1v1h-1v1c-5 0-9 0-13 3l-1 1c-2 1-3 2-4 4h-1 0-1l-1 1h-1v1c0-2 1-1 2-3l-1 1c0-2 1-2 1-3h-1c0-1-1-1-2-1v-1c1-1 2-1 3-1s3 0 4-1c1 0 2-1 2-1 1-1 2-1 3-1 1-1 2-1 3-2z" class="W"></path><path d="M375 631c1 0 3 0 4-1 1 0 2-1 2-1 1-1 2-1 3-1-2 1-4 3-6 4-1 1-2 1-3 2h-1c0-1-1-1-2-1v-1c1-1 2-1 3-1z" class="H"></path><path d="M157 464c0-1 2 0 3 0 1 1 2 1 3 1v-1-1h1c1-1 1 0 1-2-1 0-1 0-1-1h0-1l1-1c1 1 2 1 3 2v1s1 1 2 1l2-1v2 3l3 1-2 2-1-1-2 2v1 1c0 1 0 2-1 3v1 2 1l1 1 1 1h-1c2 2 3 4 5 5h1v1h-2s-1 1-2 1l-1-1-2-2-2-1c-2 0-3-1-4-1h0v-1c0-1 0-2-1-3v-1l-1-1h0v1l-1 1v-2h-1-2c-1 0-1-1-1-2-2 0-2 0-3-1 1-1 1-1 1-2s-1-1-1-1h-1-1v-2c1 0 1-1 1-1h2 0c0-1-1-2-2-2 1-1 2-1 3-2h0c-1-1 0-1-1-1v-2c0 1 1 1 1 1h1l1 1h1z" class="I"></path><path d="M154 465c1 1 1 1 1 2h-1s-1 1-1 2h0c0-1-1-2-2-2 1-1 2-1 3-2z" class="B"></path><path d="M167 462s1 1 2 1l2-1v2 3l3 1-2 2-1-1s-1 0-2-1c-1 1-1 1-2 1h0l1-2c-1-1-3-2-3-3 1-1 1-2 2-2z" class="d"></path><path d="M168 467c1 1 1 1 2 1v-2-2h1v3l3 1-2 2-1-1s-1 0-2-1c-1 1-1 1-2 1h0l1-2zm-8 1l2-2 2 1 1 1v1c-1 0-1 0-1-1-1 1-1 1-1 3h2v1h1l1-1c1 0 1 0 2 1h0 0c-1 1-2 1-3 1 0 1 0 3-2 3l-1 1-3-3c0-1 0-1-1-2h0c-1 0-1-1-2-2h1s1-1 1-2h1z" class="B"></path><path d="M161 469h1c0 1 0 2-1 2h-1v-1l1-1z" class="Y"></path><path d="M160 468l2-2 2 1-2 2h-1l-1-1z" class="E"></path><path d="M153 469h0c2 1 3 1 4 1 1 1 1 2 2 2h0c1 1 1 1 1 2h-2l2 2h-1v2h-1-2c-1 0-1-1-1-2-2 0-2 0-3-1 1-1 1-1 1-2s-1-1-1-1h-1-1v-2c1 0 1-1 1-1h2 0z" class="F"></path><path d="M150 470c1 0 1-1 1-1h2c1 1 2 2 2 3s-1 1-2 1c0-1-1-1-1-1h-1-1v-2z" class="G"></path><path d="M156 476c1-1 1-2 1-2h1l2 2h-1v2h-1-2c-1 0-1-1-1-2h1z" class="J"></path><path d="M156 476h1s0 1 1 2h-2c-1 0-1-1-1-2h1z" class="f"></path><path d="M163 477l1-1c2 0 2-2 2-3 1 0 2 0 3-1h0 0v1c0 1 0 2-1 3v1 2 1l1 1 1 1h-1c2 2 3 4 5 5h1v1h-2s-1 1-2 1l-1-1-2-2-2-1c-2 0-3-1-4-1h0v-1c0-1 0-2-1-3v-1l-1-1h0v1l-1 1v-2-2h1l-2-2h2l3 3z" class="n"></path><path d="M159 478v-2h1 0c1 1 2 1 3 2v1 1h2l-1 1h-1v1h2v2s1 0 2 1h-1c-2 0-3-1-4-1h0v-1c0-1 0-2-1-3v-1l-1-1h0v1l-1 1v-2z" class="f"></path><path d="M163 477l1-1c2 0 2-2 2-3 1 0 2 0 3-1h0 0v1c0 1 0 2-1 3v1 2 1l1 1 1 1h-1v1c0 1 1 1 1 2h-1c-1 0-1 0-2-1v-1-2c0-1-1-1-1-2s-1-1-1-1c-1 0-1-1-2-1z" class="D"></path><path d="M366 540c1-2 2-5 4-6 1-2 3-3 5-3v1l-1 1c1 0 2 1 2 2h0c1-1 1-2 1-3 3 0 7 2 9 3l3 2v1 2h-6l1 1h-3c-1 1-1 2 0 3l-1 2v1c0 1-1 1-1 2h-2-1v2c-1 0-2 1-3 1h-1c-2 1-3 2-4 3v-2l-1-1-1 2h-1l-1-2v2h0c-1 1-1 0-2 0 0-1 0-1-1-1v-2h2v-4c1-2 1-5 2-7l1-1v1z" class="E"></path><path d="M367 552l1-2 1 2-1 1-1-1zm1-6c2 0 3 0 4 1h-1-3l-1-1h1z" class="C"></path><path d="M365 540l1-1v1c-1 3-2 8-2 12v2h0c-1 1-1 0-2 0 0-1 0-1-1-1v-2h2v-4c1-2 1-5 2-7z" class="c"></path><path d="M370 542c2-1 6-2 7-1h1 1v2c-1 2-2 2-4 3l-1-1-1 1v1h0-1c-1-1-2-1-4-1l1-1h2c1-1 2 0 3 0l3-1v-1h-4c-2 0-2 0-3-1z" class="F"></path><path d="M375 546c2-1 3-1 4-3 0 1-1 2 0 3v1h1c0 1-1 1-1 2h-2-1v2c-1 0-2 1-3 1h-1c-2 1-3 2-4 3v-2l1-1-1-2 2-1 3-1v-1-1l1-1 1 1z" class="D"></path><path d="M368 550l2-1 1 1v1c-1 0-1 0-2 1l-1-2z" class="R"></path><path d="M373 547v-1l1-1 1 1v1h2v1h-4v-1z" class="W"></path><path d="M376 535h0c1-1 1-2 1-3 3 0 7 2 9 3l3 2v1 2h-6l1 1h-3c-1 1-1 2 0 3l-1 2v1h-1v-1c-1-1 0-2 0-3v-2h-1-1c-1-1-5 0-7 1-1 0-1-1-2-1h0c1-1 2-1 2-2h-1c0-1 0-2 1-2 1-2 2-4 4-4 1 0 2 1 2 2z" class="C"></path><path d="M368 541c1 0 1 0 1-1h6c3 0 5-1 8 0l1 1h-3c-1 1-1 2 0 3l-1 2v1h-1v-1c-1-1 0-2 0-3v-2h-1-1c-1-1-5 0-7 1-1 0-1-1-2-1h0z" class="d"></path><path d="M376 535h0c1-1 1-2 1-3 3 0 7 2 9 3l3 2-1 1h-2c-2 0-10 0-12-1 1 0 1 0 1-1 1 0 1 0 1-1z" class="i"></path><path d="M362 139v-1h4c1-1 3 0 5 0h1 2c-1 0 0 0 0-1h2c0 1 0 1 1 1v-1h2 1v1c1-1 1-1 2-1h2 0 1 2l1-1c1 0 1 1 1 1 1-1 1-1 2-1h7 0-8v-1h2l1-1h6v1-1c1 1 0 1 1 1v-1l1 1h0v1-1l1 1v-1h2 1 4v1h-1 1c1 1 1 0 2 1v-1l1 1v-1 1l1-1h0 1c1 1 1 0 2 1h-1 2 1 1s1 0 2 1h-1 1v1l1-1c1 0 1 0 2 1h-1l1 1h1c1 0 1 0 2 1h0 1c1 0 1 0 2 1h2 2l1 1h1 1 1 1 1 0c1 0 2 1 2 1l-5-3c-2-1-3-1-4-2-1 0-1 0-2-1h-1c-1 0-3-1-4-2h-1c15 4 31 14 41 25h-1c-1 0-3-2-4-3l-7-5c-1 0-2-1-3-1h-1c-1-1-3-2-4-3-8-3-16-6-24-7l-9-2h-1-1-6-10-7c-1-1-3-1-4-1h-8l-1 1c-1 0-1-1-2 0h-8c-1-1-5 0-6-1h0 1 1 1z" class="X"></path><path d="M362 139h0c9 0 17-1 26-1 11-1 21 0 32 2 3 0 6 1 9 2 9 2 18 5 25 11-1 0-2-1-3-1h-1c-1-1-3-2-4-3-8-3-16-6-24-7l-9-2h-1-1-6-10-7c-1-1-3-1-4-1h-8l-1 1c-1 0-1-1-2 0h-8c-1-1-5 0-6-1h0 1 1 1z" class="C"></path><path d="M333 161h2c1 0 2 0 2 1h3 1 0l1 12c1 2 1 4 1 6 1 1 0 2 1 4v3h1v-1c-1-2 0-3-1-5h0c1-1 1-3 1-4 0 4 0 9 1 13v9l-1-1h-1c-3-1-6-1-8-2-1-1 0-2 0-3v-6h-1c-1 0-2 0-3-1h-7l-7-1h5c3 1 6 0 8 1 2 0 2 0 3-1 0-2 1-9 0-11 0-1-1-2 0-3v-2c-1-1-1-2-1-2-1-1 0-4-1-5h-1c-1-1-3-1-4-1h6z" class="m"></path><path d="M335 187l-1-1h5 2v1h3v1c-1 0-3 0-4 1h1c1 0 3 2 4 3v-1l1-1v9l-1-1h-1c-3-1-6-1-8-2-1-1 0-2 0-3v-6h-1z" class="G"></path><path d="M333 161h2c1 0 2 0 2 1h3 1 0l1 12v7c1 2 2 4 1 5h-2l-3-1 1-4c-1-1-1-2-1-4-1-3-1-7-1-10 0-1 0-3-1-4l-4-1h-1c-1-1-3-1-4-1h6zm78 417l1 1v1h-1 0s0 1 1 1h12 0 3l1 1 2 1v-1c1 1 1 2 2 3l-2 2h-1v3l-1 1h-1 0l1 1h3 2v-2h2v2h1v3l-1 1c-2-1-4-1-6-1 0 1 0 2 1 2 0 1 1 1 2 2h-1l-3-1c-3-1-6 0-9 2h-1l-1-1c1 0 1-1 1-1l1-1h-2v1l-1-1c0-1 1-1 2-1h-3l-1 1-1-1 1-1h-1v-1h0-2s-1 0-1-1c-1 0-1 0-1-1h1l1-1c-2-1-2-2-2-4 1-2 1-3 0-5 1-2 1-3 2-4z" class="b"></path><path d="M429 587v3l-1 1h-1l1-3 1-1z" class="E"></path><path d="M430 583v-1c1 1 1 2 2 3l-2 2h-1l-1-1c0-2 0-2 2-3z" class="m"></path><path d="M413 594l-1-1c1-1 1-1 2-1 1-3 0-7 0-9l1-1 1 1v2c0 2-1 4 0 6h1v1c-2 0-2 0-4 1v1h0z" class="J"></path><g class="M"><path d="M420 591c0-2-1-6 0-8h2v1c0 2 0 4 1 7v1h-1v-1l-1-1-1 1z"></path><path d="M417 591v-8h1c1 1 1 3 1 5 0 1 0 2 1 3h0c0 1 0 1-1 1 0 0-1 0-1 1l-1-1c-1 0-2 1-3 2l1 1h0-2v-1-1c2-1 2-1 4-1v-1z"></path></g><path d="M420 591l1-1 1 1v1h1c2 1 4 0 5 2h0c-2 0-3 0-4 1h-9 0l-1-1c1-1 2-2 3-2l1 1c0-1 1-1 1-1 1 0 1 0 1-1z" class="K"></path><path d="M422 584h0c1-1 1-1 0-2h1 1l3 1v8l1 1h3c1 1 1 1 1 2-2 1-7 1-8 1 1-1 2-1 4-1h0c-1-2-3-1-5-2h-1 1v-1c-1-3-1-5-1-7z" class="p"></path><path d="M422 584h0c1-1 1-1 0-2h1 1v9l-1 1h0v-1c-1-3-1-5-1-7z" class="B"></path><path d="M435 590v2h1v3l-1 1c-2-1-4-1-6-1 0 1 0 2 1 2 0 1 1 1 2 2h-1l-3-1c-3-1-6 0-9 2h-1l-1-1c1 0 1-1 1-1l1-1h-2v1l-1-1c0-1 1-1 2-1h-3l-1 1-1-1 1-1h-1 2 9c1 0 6 0 8-1 0-1 0-1-1-2h2v-2h2z" class="W"></path><path d="M435 590v2h-2v-2h2z" class="E"></path><path d="M418 596h9l3 1c0 1 1 1 2 2h-1l-3-1c-3-1-6 0-9 2h-1l-1-1c1 0 1-1 1-1l1-1h-2v1l-1-1c0-1 1-1 2-1z" class="p"></path><path d="M418 596h9l-1 1h-4-2-1-2v1l-1-1c0-1 1-1 2-1z" class="Z"></path><path d="M318 119c2 0 4 0 5 1h1v1 3c1 0 1-1 1-1 1-1 2-1 3-1 2 1 3 2 4 3l-1 1v1h1v1c-1 1-1 3-1 5l1 1h-1c0 1 1 2 1 3l-1 1h0c0 1 1 1 1 2h0c0 1 0 2 1 4v4c0 4 1 7 1 10v2l-1 1h-6c1 0 3 0 4 1h1c1 1 0 4 1 5 0 0 0 1 1 2v2c-1 1 0 2 0 3 1 2 0 9 0 11-1 1-1 1-3 1-2-1-5 0-8-1h5v-15c0-3-1-5 0-8h0c-4-1-7-1-11-1v-1h2c4 1 9 1 12 0h0c0-1-1-1-1-2 0-2 0-4-1-6 0-3 1-6 0-8l-1-1c1-1 1-2 0-3 0-2-2-2-4-2l3-1s1 0 1-1c0-2-1-3-1-5v-1h-4 0-2 0-2v-1c0-3 0-7-1-10z" class="c"></path><path d="M323 120h1v1 3 2 1c1 0 2-1 2 0h3v2c0 1 1 2 1 3h0v1 4h-3s1 0 1-1c0-2-1-3-1-5v-1h-4 0v-1-8-1z" class="k"></path><path d="M318 119c2 0 4 0 5 1v1 8 1h-2 0-2v-1c0-3 0-7-1-10z" class="d"></path><path d="M321 130c0-2 1-4 1-6h0-2v-3-1l1 1c1 0 1 0 1-1h0l1 1v8 1h-2 0z" class="F"></path><path d="M376 616c2 1 3 1 4 0h1 1v1h1c1 0 1 0 2 1 0 1 0 1 1 2 2-1 4 0 6 1 1 1 1 1 1 2-1 2-3 2-5 3h-1 0c-1 1-2 1-3 2-1 0-2 0-3 1 0 0-1 1-2 1-1 1-3 1-4 1s-2 0-3 1v1c1 0 2 0 2 1h-2l-1-1c0-1 0-2 1-3h-2v-1l-2 1v2h-3 0 0c-1-1-1-1-3-1-1 0-1 0-2-1v-1l-1-1v-1h0l-1-1h-1c-1-1-1-1-1-2-1 0-2-1-2-1 0-1 0-1 1-2h2 1c1 0 2 1 2 1 2-1 2-1 3-2h2v1c1-1 1-1 1-2h1c0 1 0 1 1 2h0c1-1 2-2 3-2 1-1 2-1 2-2h2l1-1z" class="l"></path><path d="M375 622c1-1 2-1 4-1l1 1c0 1-1 1-2 1h0c-1 0-2-1-3-1z" class="N"></path><path d="M382 617h1l1 2c-1 0-1 1-2 1l-1-1h-4 2c1 0 2-1 3-2z" class="P"></path><path d="M373 617h2c-1 1-1 2-1 3h1l-1 1h-1-1c-1 1-1 0-2 1-1 0-2-1-2-1 1-1 2-2 3-2 1-1 2-1 2-2zm13 3c2-1 4 0 6 1-1 0-3 1-4 1-2 0-4 1-6 0v-1c1-1 2-1 3-1h1z" class="e"></path><path d="M376 616c2 1 3 1 4 0h1 1v1c-1 1-2 2-3 2h-2c-1 0-1 1-2 1h-1c0-1 0-2 1-3l1-1z" class="V"></path><path d="M375 622c1 0 2 1 3 1l-3 2s0 1-1 1h0-3s-1 0-1 1c-1-1-2-1-2-1h-1-2c-1-1-1-1-1-2l1-1c1 1 1 1 2 1h0 2 1v-1h0 2 1c1 0 1 0 1 1l1 1v-1l-1-1 1-1z" class="g"></path><path d="M365 623c1 1 1 1 2 1h0 2 1v-1l2 1v1h-2c-1 0-2 1-3 1h-2c-1-1-1-1-1-2l1-1z" class="N"></path><path d="M392 621c1 1 1 1 1 2-1 2-3 2-5 3h-1 0c-1 1-2 1-3 2-1 0-2 0-3 1 0 0-1 1-2 1-1 1-3 1-4 1s-2 0-3 1v1c1 0 2 0 2 1h-2l-1-1c0-1 0-2 1-3h0c0-1 1-3 2-4h0c1 0 1-1 1-1l3-2h0c2 0 3 0 4 1s1 1 1 2h0c2-1 0-1 0-2h1c1 0 2 1 2 1 1-1 2-1 2-1v-2c1 0 3-1 4-1z" class="J"></path><path d="M374 626h0c1 1 2 1 3 2h2l1-1v1c-1 1-1 0-2 1v1h-1l-2 1c-1 0-2 0-3 1v1c1 0 2 0 2 1h-2l-1-1c0-1 0-2 1-3h0c0-1 1-3 2-4z" class="N"></path><path d="M374 626h0c1 1 2 1 3 2-1 1-2 2-4 2h-1c0-1 1-3 2-4z" class="o"></path><path d="M378 623c2 0 3 0 4 1s1 1 1 2c-1 1-1 1-2 1 0 0-1-1-2-1h0c0 1 0 1 1 1l-1 1h-2c-1-1-2-1-3-2 1 0 1-1 1-1l3-2h0z" class="O"></path><path d="M365 620v1c1-1 1-1 1-2h1c0 1 0 1 1 2l-3 2-1 1c0 1 0 1 1 2h2 1s1 0 2 1c0-1 1-1 1-1h3c-1 1-2 3-2 4h0-2v-1l-2 1v2h-3 0 0c-1-1-1-1-3-1-1 0-1 0-2-1v-1l-1-1v-1h0l-1-1h-1c-1-1-1-1-1-2-1 0-2-1-2-1 0-1 0-1 1-2h2 1c1 0 2 1 2 1 2-1 2-1 3-2h2z" class="n"></path><path d="M362 631v-1l-1-1c3 0 5-1 7-1h0c-1 1-1 1-2 1v1h2v2h-3 0 0c-1-1-1-1-3-1z" class="S"></path><path d="M355 621h2 1c1 0 2 1 2 1 1 0 2 1 3 2l1 1c-1 0-1 1-2 1l-2 3-1-1v-1h0l-1-1h-1c-1-1-1-1-1-2-1 0-2-1-2-1 0-1 0-1 1-2z" class="H"></path><path d="M359 627v-1h3l-2 3-1-1v-1z" class="W"></path><path d="M137 455l2-1h1c1-1 1-1 2 0h2l-1 1v1l1 1h1v-1c0-1 1-1 1-2 1 1 1 1 0 2 0 1 1 1 1 2v1h1 1s0 1 1 1c0 1 1 1 2 2v1l2 2h0c-1 1-2 1-3 2 1 0 2 1 2 2h0-2s0 1-1 1v2h1 1s1 0 1 1 0 1-1 2l-1-1h-4l-1 1c1 1 1 2 2 3-2 0-3-2-3-3h-1-1v-1h-1-4s-1 1-2 1-1-1-2-1c-1-1-2-1-3 0-1-1-2-3-3-4-2-2-4-4-5-7 0-1 0-1-1-2h1s1 1 2 1c0 1 1 1 2 1h0 0c1 0 1 1 2 1l1-2 2 2h1c1 1 1 1 2 1v-1c-1 0-1 0-2-1v-1c1 0 2 1 4 0v-1-1h1c0 1 1 1 2 1l1-1h0v-1c-1-1-2-1-2-3 0 0-1-1-2-1z" class="D"></path><path d="M137 462l2 2h0c-1 1-1 1-2 1l-1-1v-1l1-1z" class="m"></path><path d="M134 467c2 1 2 1 3 0h1l2 1v-1c1 1 1 1 1 2l-2 2-1-1c-1-1-2-1-4-2v-1zm3-6v-1h1c0 1 1 1 2 1l1-1 1 1c-1 1-2 1-2 3h-1l-2-2v-1z" class="d"></path><defs><linearGradient id="R" x1="143.536" y1="466.501" x2="141.299" y2="473.707" xlink:href="#B"><stop offset="0" stop-color="#747374"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#R)" d="M140 467v-1c0-1 1-1 2-2v1c0 1 0 2 1 3v1h0c1 1 1 1 2 1l-1 1v1l1 1 1 2c1 1 1 2 2 3-2 0-3-2-3-3h-1-1v-1c-1-1-3-2-4-3h0l2-2c0-1 0-1-1-2z"></path><path d="M123 463c0-1 0-1-1-2h1s1 1 2 1c0 1 1 1 2 1h0 0c1 0 1 1 2 1l1-2 2 2-2 1v1c2 0 1-1 3-1 0 1 0 1 1 2v1c2 1 3 1 4 2l1 1h0c1 1 3 2 4 3h-1-4s-1 1-2 1-1-1-2-1c-1-1-2-1-3 0-1-1-2-3-3-4-2-2-4-4-5-7z" class="M"></path><path d="M130 468l4 2h0v-1-1c2 1 3 1 4 2l1 1h0l-3-1h-1l1 2h-1-1c-2-1-2-2-4-4z" class="o"></path><path d="M130 462l2 2-2 1v1c2 0 1-1 3-1 0 1 0 1 1 2v1 1 1h0l-4-2c0-1-1-1-1-2-1-1-1-1-1-2 0 0-1 0-1-1 1 0 1 1 2 1l1-2z" class="N"></path><path d="M137 455l2-1h1c1-1 1-1 2 0h2l-1 1v1l1 1h1v-1c0-1 1-1 1-2 1 1 1 1 0 2 0 1 1 1 1 2v1h1 1s0 1 1 1c0 1 1 1 2 2v1l2 2h0c-1 1-2 1-3 2 1 0 2 1 2 2h0-2s0 1-1 1v2h1 1s1 0 1 1 0 1-1 2l-1-1h-4l-1 1-1-2-1-1v-1l1-1c-1 0-1 0-2-1h0v-1-1l2 1h0v-1c-1-1-1-1-1-3 0-1-1 0-2-1v-3l-1-1c-1-1-2-1-2-3 0 0-1-1-2-1z" class="C"></path><path d="M151 467c1 0 2 1 2 2h0-2s0 1-1 1l-1-1 2-2z" class="W"></path><path d="M150 460c0 1 1 1 2 2h-1v1c0 1-1 1-1 1s0-1-1-1v-1s0-1 1-2z" class="G"></path><path d="M137 455l2-1h1c1-1 1-1 2 0v2c0 1 0 1 1 2h-2c0-1 1-2 0-3l-2 1s-1-1-2-1zm5 5s1 0 1-1h1 1v2h1c0 1 0 1 1 2h2c0 1-1 1-1 1l1 2-2 2c1 0 1 0 1 1s0 1 1 2h0c1 1 1 1 2 1h0 1s1 0 1 1 0 1-1 2l-1-1h-4l-1 1-1-2-1-1v-1l1-1c-1 0-1 0-2-1h0v-1-1l2 1h0v-1c-1-1-1-1-1-3 0-1-1 0-2-1v-3z" class="U"></path><path d="M144 464h2v1 1l-1 1c-1-1-1-1-1-3z" class="C"></path><path d="M312 74c1-3 1-8 4-10V47l1-13v-7c1 9 1 18 1 27v7 2c0 1 2 1 2 2v2c1 2 2 6 3 8 0 4 0 10 3 13h-1l1 8c-2 0-4-1-6-2v1h-2 0c-1 1-1 1-2 1h-2c-1 0-2 0-3-1h0l2-2v-1c1-1 1-2 1-3l-2-2c-1-1 0-1 0-2v-1-5c1-1 0-3 0-5z" class="j"></path><path d="M320 67c1 2 2 6 3 8 0 4 0 10 3 13h-1l1 8c-2 0-4-1-6-2l-1-1c1-1 0-2 1-3v-1h-1v-1c1-1 1-1 2-1V76c0-4-1-6-1-9z" class="H"></path><path d="M199 606h1 3c2 1 5-1 7 0 1 1 2 0 3 0 0 1-1 1-1 1h3c2 1 4 0 6 0 0 2 1 8 0 9l-3 1c-1 1-1 2-1 3 1 0 1 1 1 1h-4v1h-4c-1 0-1 0-2-1h-4c-3-1-6-1-9-2-5 0-10 0-15 1v-1c0-1 1-2 2-2l3-1h3c3 0 5-1 8 0v1h1c-1-2-1-8 0-10h1l1-1z" class="n"></path><path d="M199 606h1 3c2 1 5-1 7 0 1 1 2 0 3 0 0 1-1 1-1 1-4 1-9 1-13 0v-1z" class="i"></path><path d="M203 610c1 0 1 0 1 1 1 1 0 2 1 3v2c1-1 1-1 2-1 0 0 0 1 1 1 0-2-1-6 1-7 1 1-1 6 1 7 1-1 0-5 0-7h1c0 2-1 5 0 6h1v-1h1v1h1l1-2c0 1 0 2 1 3 1-1 1 0 2-1h1l1 1h1l-3 1h-2-15l-1-1c2-2 1-4 1-7h1v6h1c1-1 0-3 0-5z" class="k"></path><path d="M215 607c2 1 4 0 6 0 0 2 1 8 0 9h-1l-1-1h-1c-1 1-1 0-2 1-1-1-1-2-1-3l-1 2h-1v-1h-1v1h-1v-7h2 2v-1z" class="B"></path><path d="M197 617c-1-2-1-8 0-10h1l1 1h4v2c0 2 1 4 0 5h-1v-6h-1c0 3 1 5-1 7l1 1h15 2c-1 1-1 2-1 3 1 0 1 1 1 1h-4v1h-4c-1 0-1 0-2-1h-4c-3-1-6-1-9-2-5 0-10 0-15 1v-1c0-1 1-2 2-2l3-1h3c3 0 5-1 8 0v1h1z" class="F"></path><path d="M182 617l3-1v1 1l-5 1c0-1 1-2 2-2z" class="K"></path><path d="M185 616h3c3 0 5-1 8 0v1l-11 1v-1-1z" class="Z"></path><path d="M197 617c-1-2-1-8 0-10h1l1 1c1 1 1 2 0 4h0v2c1 1 1 2 1 2-1 1-2 1-3 1h0z" class="q"></path><path d="M411 565c0-1-1-3 0-4 2 1 4 0 6 0 1 1 2 1 3 1h3 0 7 2 1c0 1 1 1 1 3 0 1 1 2 0 3h-1c0-1 0-1-1-2h-1v3 1c1 0 1 0 1 1h0v1l3-1v2h-3-4v2 2h2v1h1c-1 1-1 1-1 2-1 0-1 1-1 1h-2-3 0-12c-1 0-1-1-1-1h0 1v-1l-1-1v-1c1 0 2 1 3 0v-1c0-1 0-1-1-1l-1 1h-1c0-1-1-2-1-3 0-2 0-4 1-5v-3z" class="Z"></path><path d="M419 578h-1c0-1 1-3 1-4h3v4h-3z" class="F"></path><path d="M419 566c1 0 0 0 1 1h0l1-1h1v5h-3v-5z" class="B"></path><path d="M414 566h0 3 1l-1 4h0c-2 0-1-1-2 0l-2-1c0-1 0-2 1-3z" class="R"></path><path d="M416 564v-1c1-1 2 0 4 0 1 0 2 0 3 1v1c-1 1-2 1-4 1 0-1-1-1-1-2 0 1-1 1-1 1v1h-3l2-1v-1z" class="T"></path><path d="M432 572l3-1v2h-3-4v2 2h2v1c-2 0-4-1-6 0h-1c0-1 0-3 1-4v1 2c1 0 2 0 2-1v-3h2 1c1 0 4 0 5-1h-2z" class="B"></path><path d="M424 578c2-1 4 0 6 0h1c-1 1-1 1-1 2-1 0-1 1-1 1h-2-3 0c0-1 1-2 0-2-1-1-1 0-1 0-1 0-1-1-1-1h-3 0 3 1 1z" class="n"></path><path d="M419 578h3 1 1 3v3h-3 0c0-1 1-2 0-2-1-1-1 0-1 0-1 0-1-1-1-1h-3 0z" class="M"></path><path d="M411 565l1 1v-1c1 0 1 1 2 1-1 1-1 2-1 3l2 1-1 1c-1 0-1-1-2 0l-1 1h1v1c-1 0 0 0-1 1h4c0 1-1 2-1 2 0-1 0-1-1-1l-1 1h-1c0-1-1-2-1-3 0-2 0-4 1-5v-3z" class="H"></path><path d="M412 565c1 0 1 1 2 1-1 1-1 2-1 3l2 1-1 1c-1 0-1-1-2 0v-2-3-1z" class="S"></path><path d="M411 565c0-1-1-3 0-4 2 1 4 0 6 0 1 1 2 1 3 1h3 0v1h1l-1 1c-1-1-2-1-3-1-2 0-3-1-4 0v1 1l-2 1h0c-1 0-1-1-2-1v1l-1-1z" class="J"></path><path d="M412 565c0-1 1-1 1-2 1 0 2 0 3 1v1l-2 1h0c-1 0-1-1-2-1z" class="h"></path><path d="M427 568v-2h6-1-1v3 1c1 0 1 0 1 1h0v1h2c-1 1-4 1-5 1h-1c0-1-1-2-1-2h-1c0-1-1-2-1-3h2z" class="H"></path><path d="M427 568v-2h6-1-1v3 1c1 0 1 0 1 1h0-1c-1 0-1-2-2-2s-1 0-1 1h-1v-2z" class="B"></path><path d="M433 562c0 1 1 1 1 3 0 1 1 2 0 3h-1c0-1 0-1-1-2h1-6v2h-2c-1 0-1 0-1 1-1-1-1-1-1-2 0-2 3-1 4-2-1-1-2-1-3-2h-1v-1h7 2 1z" class="C"></path><path d="M433 562c0 1 1 1 1 3 0 1 1 2 0 3h-1c0-1 0-1-1-2h1v-1c-2 0-2-1-3-1 0-1-2-1-2-1v-1h3 1 1z" class="E"></path><path d="M415 574l1 1 1-1v1l-1 1c1 1 1 1 1 2h2 3s0 1 1 1c0 0 0-1 1 0 1 0 0 1 0 2h-12c-1 0-1-1-1-1h0 1v-1l-1-1v-1c1 0 2 1 3 0v-1s1-1 1-2z" class="e"></path><path d="M415 574l1 1 1-1v1l-1 1c1 1 1 1 1 2h-3c-1 1-2 1-2 3-1 0-1-1-1-1h0 1v-1l-1-1v-1c1 0 2 1 3 0v-1s1-1 1-2z" class="g"></path><path d="M180 620c5-1 10-1 15-1 3 1 6 1 9 2h4c1 1 1 1 2 1h4l-2 2c-2 1-3 1-4 2s-1 1-2 1-2 1-3 1c-3 2-6 4-9 5v2h-2c-1 0-2 0-4-1l-1-1h-2s0-1 1-1v-1h1v-1h-1s1 0 1-1h0c-1-1-3-1-4-1-2 1-4 1-6 0-1 0-1-1-2-2h1l2-2h0c-1 0-1 1-2 1s-3 0-4 1h0c0-1-1-1-1-1h-1-4l-6 2c-2 0-2 0-4-1 3-4 9-3 14-4 3 0 7 0 10-2z" class="T"></path><path d="M195 619c3 1 6 1 9 2l-1 1-3 1h-2-1c-1 0-2-1-3-1v-1h1v-2z" class="V"></path><path d="M190 627c2-1 6-4 7-3h0l-3 3c-1 0-1 0-1 1h3-2c-1 0-2 0-3 1 0 1-1 1-1 2l-1 1h0c-1-1-2-1-3-1h1v-1h-1s1 0 1-1h0c-1-1-3-1-4-1h3c1-1 3-1 4-1z" class="O"></path><path d="M176 626h1 2l2-1h0 1c-1-2-2-1-2-2l1-1h0c2 1 3 0 5 0h0 1 4 0c-2 1-5 2-6 4 0 0 1 1 2 1h1 2c-1 0-3 0-4 1h-3c-2 1-4 1-6 0-1 0-1-1-2-2h1z" class="V"></path><path d="M208 621c1 1 1 1 2 1h4l-2 2c-2 1-3 1-4 2s-1 1-2 1-2 1-3 1c-3 2-6 4-9 5v2h-2c-1 0-2 0-4-1l-1-1h-2s0-1 1-1v-1c1 0 2 0 3 1h0l1-1c0-1 1-1 1-2 1-1 2-1 3-1h2l7-3c1-1 3-2 5-3h0v-1z" class="K"></path><path d="M190 631c1 0 3-1 4-1l1-1h1l-4 3h-3l-2 1h-2s0-1 1-1v-1c1 0 2 0 3 1h0l1-1z" class="S"></path><path d="M187 633l2-1h3c1 1 1 1 2 1v2h-2c-1 0-2 0-4-1l-1-1z" class="K"></path><path d="M199 627c1 1 2 1 4 1-3 2-6 4-9 5-1 0-1 0-2-1l4-3c1 0 2-1 3-2z" class="Q"></path><path d="M210 622h4l-2 2c-2 1-3 1-4 2s-1 1-2 1-2 1-3 1c-2 0-3 0-4-1l6-3c2 0 3-1 5-2z" class="L"></path><path d="M361 614h0c-1 1-1 1-1 2 1 1 1 1 2 1-1 1-1 1-1 2h-2 0-2c0 1 0 1 1 2h-1-2c-1 1-1 1-1 2 0 0 1 1 2 1 0 1 0 1 1 2h1l1 1h0v1c0 1 0 2-1 2v1c-1 1-2 1-3 1l-1 1-1-1c-1 0-2-1-3-1h-3-3v1h-1v-1c-2 0-3-1-5 0h-2c-1 0-2 0-3 1v-1-1h-3v-1l1-1c-2 0-3 0-5 1l-2 1v-1h1c0-1-1-1-1-1v-1l5-2 1-1h-1v-2l3-1 1-1h1c1-1 4-2 6-1 1-1 1-1 2-1h1c1-1 3-2 4-2h1c2-1 3-2 5-2h1c1 0 2 0 3 1v1c1 0 2 0 3-1l-1-1h2z" class="O"></path><path d="M344 620h1c0 1 0 1-1 2h-2c0-1 1-2 2-2z" class="P"></path><path d="M329 622l3-1 1 1c2 1 4-1 5 1h-5c-1 0-1 1-3 1 0 0 0 1-1 1h0l1-1h-1v-2z" class="K"></path><path d="M342 618h1c1-1 3-2 4-2l-1 2h-1l1 1c1-1 2-1 3-1v2s0 1-1 1h0c-1 0-2-1-3-1h-1c-1-1-3-1-4-1 1-1 1-1 2-1z" class="d"></path><path d="M349 618s1 1 2 1 2 1 3 2h1c-1 1-1 1-1 2-1 0-2-1-3-1s-2 0-3 1c-1 0-2 0-3 1-1 0-2 1-4 1v-1h2c2-1 4-1 5-3h0c1 0 1-1 1-1v-2z" class="c"></path><path d="M341 625c2 0 3-1 4-1 1-1 2-1 3-1 1-1 2-1 3-1 1 1 1 1 1 2-1 1-2 1-3 1s-2 0-3 1c-1 0-1 0-2-1 0 1-1 1-1 2-1 0-2 0-2-1v-1z" class="P"></path><path d="M331 628c1-1 4 0 6-2 0 0 1 0 1-1h2c0 1-1 2-2 2-1 1-2 2-3 2v1h1v1c-1 0-2 0-3 1v-1-1h-3v-1l1-1z" class="n"></path><path d="M361 614h0c-1 1-1 1-1 2 1 1 1 1 2 1-1 1-1 1-1 2h-2 0-2c0 1 0 1 1 2h-1-2-1c-1-1-2-2-3-2s-2-1-2-1c-1 0-2 0-3 1l-1-1h1l1-2h1c2-1 3-2 5-2h1c1 0 2 0 3 1v1c1 0 2 0 3-1l-1-1h2z" class="Z"></path><path d="M348 616c1 1 2 1 2 1-1 1-2 1-4 1l1-2h1z" class="M"></path><path d="M351 619c2-1 5 0 7-1l1 1h-2c0 1 0 1 1 2h-1-2-1c-1-1-2-2-3-2z" class="N"></path><path d="M354 614c1 0 2 0 3 1v1h-1c-2 1-3 1-6 1 0 0-1 0-2-1 2-1 3-2 5-2h1z" class="K"></path><path d="M354 614c1 0 2 0 3 1v1h-1-4v-1l1-1h1z" class="V"></path><path d="M351 622c1 0 2 1 3 1 0 0 1 1 2 1 0 1 0 1 1 2h1l1 1h0v1c0 1 0 2-1 2v1c-1 1-2 1-3 1l-1 1-1-1c-1 0-2-1-3-1h-3-3v1h-1v-1c-2 0-3-1-5 0h-2v-1h-1v-1c1 0 2-1 3-2v1c2 0 4 0 5-1 0-1 1-1 1-2 1 1 1 1 2 1 1-1 2-1 3-1s2 0 3-1c0-1 0-1-1-2z" class="N"></path><path d="M353 627l1-1v-1h0l2 2 2-1 1 1c-1 1-2 1-3 1h-1c-1 1-1 1-3 1 0-1 1-1 1-2z" class="O"></path><path d="M347 631c1-1 2-2 3-1l2 1c2 0 2-2 5-1v1h1c-1 1-2 1-3 1l-1 1-1-1c-1 0-2-1-3-1h-3z" class="c"></path><path d="M351 622c1 0 2 1 3 1 0 0 1 1 2 1 0 1 0 1 1 2h1l-2 1-2-2h0v1l-1 1h-1l-1 1c-1 0-1-1-2-1h-4 0c0 1-1 1-2 2-1 0-1 0-2-1v1c-1 0-2 1-3 1h0-2-1v-1c1 0 2-1 3-2v1c2 0 4 0 5-1 0-1 1-1 1-2 1 1 1 1 2 1 1-1 2-1 3-1s2 0 3-1c0-1 0-1-1-2z" class="M"></path><path d="M380 510l1-1c1 3 1 8 1 11 0 0 0 1-1 1 1 1 1 1 2 1h0 0c0 2 1 5 0 8 0 1 1 2 1 3h1 0 2c0 1 0 1-1 2-2-1-6-3-9-3 0 1 0 2-1 3h0c0-1-1-2-2-2l1-1v-1c-2 0-4 1-5 3-2 1-3 4-4 6v-1l-1 1v-1c1-3 4-6 7-8 1 0 1 0 2-1l-1-1h-1-2c-1 0-2 0-2-1h-2-2l-1-1c0-2-1-1-2-2h-1-3l7-1c0-1-1-1-2-1s-1 0-2-1h7c-1-2-1-1-3-1l-1-1h0-2v-1h-1-1v-4s1 0 2-1h0v-1-1h0 1v1h3 4 2v-4c1 1 2 1 3 0 1 1 1 1 2 1h2v-1l1 1h1z" class="R"></path><path d="M381 528l2 2-2 2h-1c0-2 0-3 1-4z" class="X"></path><path d="M377 523c1 0 1 0 2 1v3h-1c-1-1-1-2-1-4z" class="B"></path><path d="M383 522c0 2 1 5 0 8h0l-2-2h-1v-2c0-1 0-1 1-2l-1-1c1 0 2 1 3 0v-1h0z" class="b"></path><path d="M380 510l1-1c1 3 1 8 1 11 0 0 0 1-1 1 1 1 1 1 2 1l-1 1h-6-1c1-1 1-2 2-2 0 0 0 1 1 1h1v-1c1-1 0-2 0-4v-6-1h1z" class="D"></path><path d="M380 510l1-1c1 3 1 8 1 11 0 0 0 1-1 1h-1c1-3 1-8 0-11z" class="Y"></path><path d="M364 524h1c3 0 7 0 10-1 1 1 1 2 1 3s0 2-1 3c0 0 0 1-1 1h0l-1-1h-1-2c-1 0-2 0-2-1h-2-2l-1-1c0-2-1-1-2-2h-1-3l7-1z" class="E"></path><path d="M361 525h6l1 2v1h-2-2l-1-1c0-2-1-1-2-2z" class="k"></path><path d="M367 525h6l1 1h1v1h0c-1 1-1 2-1 3h0l-1-1h-1-2c-1 0-2 0-2-1h-2 2v-1l-1-2z" class="n"></path><path d="M370 520h2c1-2 0-6 0-9h1v11h-6c-1-2-1-1-3-1l-1-1h0-2v-1h-1-1v-4s1 0 2-1h0v-1-1h0 1v1h3 4 2v1l-1 1c-1 1-1 3 0 5z" class="N"></path><path d="M370 520h-1c-1-1-1-1-1-2s0-3 1-3h1c-1 1-1 3 0 5z" class="r"></path><path d="M363 520v-5h4v2 3h-1c0-1 0-1-1-2h0 0c0 1-1 2-1 2h-1z" class="B"></path><path d="M398 615l2 1h1c2 2 5 3 7 3 3 0 8-2 10-1 2 0 2 1 3 2-1 2-2 1-3 2h-1-2 0v2h2v1h2v1c1 1 1 1 3 1v1h-1l-4-2-4 3v1l2 1c0-1 0-1 1-1l1 1v-2h1 1l-1 1 1 1h2 1c2 0 3 0 4 1h1 0v2h-1 0 2c-1 1-1 1-2 1s-2 1-4 1v-1l-2 1v2-1h-2l-1 1c-2-1-3-2-5-2l-2 1c0-1-1-1-1-2h-2l-2-2h-1c-3-2-5-3-9-4v-1h1v-1h-1-6c-1-1-1-1-2-1h0 0 1c2-1 4-1 5-3 0-1 0-1-1-2-2-1-4-2-6-1-1-1-1-1-1-2 1 0 3 1 4 1 2 0 3 0 5-1 0 0 1 0 1-1 1-1 2-1 3-2z" class="D"></path><path d="M404 624v1h1c0-1 1-1 1-1l1 1c-1 1-2 0-4 2 0-1-1-1-1-2l1-1h1z" class="H"></path><path d="M402 622c0 1 0 2 1 2l-1 1h-5c1-1 3-1 4-2l1-1zm10-2h2l1 2c-2 2-3 2-5 3h-1l-1-1v-1c1-1 2 0 4-1v-2z" class="J"></path><path d="M398 615l2 1h1c2 2 5 3 7 3 3 0 8-2 10-1 2 0 2 1 3 2-1 2-2 1-3 2h-1-2 0l-1-2h-2c-1 0-1 1-3 2h-2c-1 1-2 2-3 2h-1c-1 0-1-1-1-2h1v-1l-6 2 3-2 3-3c-3 1-5 3-8 4-2-2-3-2-6-3h0c2 0 3 0 5-1 0 0 1 0 1-1 1-1 2-1 3-2z" class="N"></path><path d="M418 618c2 0 2 1 3 2-1 2-2 1-3 2l-2-1v-3h2z" class="Z"></path><path d="M398 615l2 1h1c-1 1-2 2-3 2-2 1-2 0-4 0 0 0 1 0 1-1 1-1 2-1 3-2z" class="L"></path><path d="M395 627c-1-1-2-1-2-2l1-1h2 1l-1 1v1c2 1 4 2 6 2 0-1 1 0 1 0h3c1-1 2-1 3-1 0-1 1-1 1-1 1 1-1 1-1 3 1 0 3-1 5-1 0-1 0-1 1-2l2-1h2v1c1 1 1 1 3 1v1h-1l-4-2-4 3v1l2 1c0-1 0-1 1-1l1 1v-2h1 1l-1 1 1 1h2 1c2 0 3 0 4 1h1 0v2h-1 0 2c-1 1-1 1-2 1s-2 1-4 1v-1l-2 1v2-1h-2l-1 1c-2-1-3-2-5-2l-2 1c0-1-1-1-1-2h-2l-2-2h-1c-3-2-5-3-9-4v-1h1v-1h-1z" class="F"></path><path d="M405 633c1-1 2-1 3-2v1h2l2 1h3 2c1 0 2 0 4 1v-1h1c1 1 2 1 4 1h0 2c-1 1-1 1-2 1s-2 1-4 1v-1l-2 1v2-1h-2l-1 1c-2-1-3-2-5-2l-2 1c0-1-1-1-1-2h-2l-2-2z" class="Y"></path><path d="M409 635h4-1v1l-2 1c0-1-1-1-1-2z" class="b"></path><path d="M416 635h6l-2 1-2 1c-1-1-1-1-2-1h0v-1z" class="m"></path><path d="M413 635h3v1h0c1 0 1 0 2 1l2-1v2-1h-2l-1 1c-2-1-3-2-5-2v-1h1z" class="F"></path><path d="M324 618c1 0 2 0 3-1 1 2 0 2 2 3l-2 1v1h2v2h1l-1 1-5 2v1s1 0 1 1h-1v1l-1 2h0c-1 1-3 1-4 2h-8l-1 1c-2 1-4 0-6 1h0-1-3v-1h1c0-1 0-1-1-2-2 1-3 1-4 1h-1l-1 1-2 1h-3l-2-1h-1c1-1 2-1 2-2h-1s0-1-1-1h1l1-1v-1l1-2h-1c0-1 2-1 2-1 1-1 2-1 2-2 0 0 1-1 2-1h1c2 0 5-1 7-3l1 1h-2l1 1h-1v1c2 1 4 1 6 0 1-1 3 0 4-1h1c1-1 2-1 3 0l3-1 2-2v-1c1 0 3 0 4 1v-2z" class="g"></path><path d="M313 631h1v1 1h-2v-1l1-1z" class="k"></path><path d="M321 628h1c1 0 1 0 2-1v1s1 0 1 1h-1v1l-1 2c-1-1-1-1-2-1-1 1-2 1-3 0v-1c1 0 2-1 3-2zm-20-1c3 0 5-1 7-1h0c1-1 2-1 3-1 0-1 1-1 1-1 1 1 2 2 1 3l1 1h-2-3c-2 2-3 0-5-1h-3z" class="M"></path><path d="M301 628c2 0 3 0 5 1v1c-2 1-4 1-7 1-2 0-5 1-7 1 2-1 4-3 6-3 0 0 1 0 1-1h2z" class="e"></path><path d="M292 625s1-1 2-1h1l-1 1c2 1 3 1 4 1 1 1 1 1 3 1v1h-2c-2 0-3 1-5 2h0-2c-1 0-1 1-2 1-1 1-2 1-3 1l1-1v-1l1-2h-1c0-1 2-1 2-1 1-1 2-1 2-2z" class="K"></path><path d="M290 627h4l-1 1c-1 1-1 1-2 1h0l-1-2z" class="P"></path><path d="M296 634c1-1 3-1 4-2h1l1 1c2-2 3-1 5-2h1c1-1 2-1 3-1v1h-1 1c1 1 0 2 0 3l-1 1c-2 1-4 0-6 1h0-1-3v-1h1c0-1 0-1-1-2-2 1-3 1-4 1z" class="d"></path><path d="M310 631h1c1 1 0 2 0 3l-1 1c-2 0-3-1-5-1l5-3z" class="N"></path><path d="M295 624c2 0 5-1 7-3l1 1h-2l1 1h-1v1c2 1 4 1 6 0 1-1 3 0 4-1h1c1-1 2-1 3 0l-3 1s-1 0-1 1c-1 0-2 0-3 1h0c-2 0-4 1-7 1h0c-2 0-2 0-3-1-1 0-2 0-4-1l1-1z" class="e"></path><path d="M324 618c1 0 2 0 3-1 1 2 0 2 2 3l-2 1v1h2v2h1l-1 1-5 2c-1 1-1 1-2 1h-1-2c-1 1-1 1-2 1h-2-2v1-1l1-1h0l-1-1c1-1 0-2-1-3l3-1 3-1 2-2v-1c1 0 3 0 4 1v-2z" class="Z"></path><path d="M320 619c1 0 3 0 4 1h1 1v1c-3 1-5 2-8 1l2-2v-1z" class="O"></path><path d="M175 487c1 1 2 1 3 1l1 2v1c0-1 1-1 1-1 0-1 1-1 1-1 1 0 1 1 2 1h1l1-1 1 1v1 1h0c1 1 0 1 1 2v2h0c1 0 2 1 3 2h0v-1h2l1 1v-2l7 3v-1l-1-1c1 0 1 0 1-1 1 1 1 1 2 1 0 0 1 0 1 1h3-1c0 1 0 1 1 1h0c1 0 0 0 1-1v1l-2 2v1h0s-1 0-1 1v3c1 2 2 3 3 4h1c1 0 1 0 2 1 0 1 0 2 1 3l3 2c-1 1-3 1-5 1 0-1-1-1-2-1l-1 1c1 1 1 1 3 1v1h-1-3c0-1-1-2-2-2-2 0-3-2-4-2s-2-1-3-2l-2 1c0-1-1-2-2-2h0c-1 0-1 0-2-1v-1l-1-2h-1c-1 0-1 0-2-1l-5-1c1 0 2 0 3-1h0c-2-3-2-5-3-7-2-2-3-4-5-5-1-1-2-1-2-1v-1l-2-1-1-1c1 0 2-1 2-1h2v-1z" class="S"></path><path d="M190 510c2 1 2 0 4 0 0 1 1 2 3 3h-1l-2 1c0-1-1-2-2-2h0c-1 0-1 0-2-1v-1z" class="N"></path><path d="M197 507h2l1-1c0 1 2 2 2 3v2 1 1l1 1h1c1 0 1 0 1 1h0v2l-1-1c-1 0-1 0-2-1-1 0-1-1-2-1 0-1 1-2 0-3h-1-3v-1h0 2c0-2 0-2-1-3z" class="U"></path><path d="M204 503v3c1 2 2 3 3 4h1c1 0 1 0 2 1 0 1 0 2 1 3l3 2c-1 1-3 1-5 1 0-1-1-1-2-1l-1 1h-1v-2h0c0-1 0-1-1-1h-1l-1-1v-1-1-2c0-1-2-2-2-3l-1 1h-2-1l1-1 2-2c1 1 1 2 2 2 1-1 1-2 2-3h1z" class="F"></path><path d="M205 515c1-2 1-2 2-3 1 0 1 0 2 1v1h-1 0l-2 2-1-1h0zm-3-6c1-1 1-1 2-1h0l1 1-1 1 1 1h0c-1 1-1 2-1 3h0-1l-1-1v-1-1-2z" class="B"></path><path d="M202 509c1-1 1-1 2-1h0c0 2 0 2-2 3v-2z" class="Y"></path><path d="M200 499v-1l-1-1c1 0 1 0 1-1 1 1 1 1 2 1 0 0 1 0 1 1h3-1c0 1 0 1 1 1h0c1 0 0 0 1-1v1l-2 2v1h0s-1 0-1 1h-1c-1 1-1 2-2 3-1 0-1-1-2-2l-2 2v-1h-2c-1 0-1-1-1-1h-3v-1c1 0 2-1 3-1v-1h-1v-1l-1-1h1v-1-2l7 3z" class="E"></path><path d="M200 499v-1l-1-1c1 0 1 0 1-1 1 1 1 1 2 1 0 0 1 0 1 1s-1 2-2 2l-1-1z" class="B"></path><path d="M193 499c1 0 2 1 3 1v1c2 0 4 0 4 1h0v1l-1 1-2 2v-1h-2c-1 0-1-1-1-1h-3v-1c1 0 2-1 3-1v-1h-1v-1l-1-1h1z" class="C"></path><path d="M175 487c1 1 2 1 3 1l1 2v1c0-1 1-1 1-1 0-1 1-1 1-1 1 0 1 1 2 1h1l1-1 1 1v1 1h0c1 1 0 1 1 2v2h0c1 0 2 1 3 2h0v-1h2l1 1v1h-1l1 1v1h1v1c-1 0-2 1-3 1h0l-1 2v1l-1 1c0 1 0 1 1 1h3v1l1 1c-2 0-2 1-4 0l-1-2h-1c-1 0-1 0-2-1l-5-1c1 0 2 0 3-1h0c-2-3-2-5-3-7-2-2-3-4-5-5-1-1-2-1-2-1v-1l-2-1-1-1c1 0 2-1 2-1h2v-1z" class="n"></path><path d="M186 500l2 2v1l-1 1-2-1 1-1v-2zm3 7h-1c0-1 0-2 1-3h1v2l-1 1z" class="g"></path><path d="M185 503l-1-1c0-1 0-1 1-2h1 0v2l-1 1z" class="p"></path><path d="M175 487c1 1 2 1 3 1l1 2v1c0-1 1-1 1-1 0-1 1-1 1-1 1 0 1 1 2 1h1l1-1 1 1v1 1h0c-1 1-1 1-3 1h-1v-1h-1v1c-2 1-5-2-6-3l-1 1-2-1-1-1c1 0 2-1 2-1h2v-1z" class="H"></path><path d="M182 492h0c1-1 3-1 4-1v1h0c-1 1-1 1-3 1h-1v-1z" class="W"></path><path d="M175 487c1 1 2 1 3 1l1 2v1h0 0c-2 0-3-2-4-3l-1 1 1 1-1 1-2-1-1-1c1 0 2-1 2-1h2v-1z" class="S"></path><path d="M186 492c1 1 0 1 1 2v2h0c1 0 2 1 3 2h0v-1h2l1 1v1h-1l1 1v1h1v1c-1 0-2 1-3 1h0c-1-1-2-1-3-1l-2-2h0c-1-1-2-1-2-2 1-1 2-1 3-2h-3c-1-1-1-2-2-3h1c2 0 2 0 3-1z" class="R"></path><path d="M188 499h1v1l-1 1c-1 0-1 0-1-1s0-1 1-1z" class="W"></path><path d="M190 497h2l1 1v1h-1l1 1v1h-2c0-1-1-2-1-3v-1z" class="b"></path><path d="M355 512h3 3v1 1h0c-1 1-2 1-2 1v4h1 1v1h2 0l1 1c2 0 2-1 3 1h-7c1 1 1 1 2 1s2 0 2 1l-7 1h3 1c1 1 2 0 2 2l1 1h-5l-2 1c-3-1-7 0-10 0h-6-7c-1 1-2 1-3 1v-1-5-1-1c0-1 1-1 0-2v-3-1-4 1 2c1 0 2 0 3-1l-1-1 1 1h1s0 1 1 1 0-1 2 1h-1v3h2 0 1 0 1c0-2 0-3-1-4h-2 0l1-2c1 0 3 0 4-1h1 3 0 8z" class="D"></path><path d="M333 513l1 1h1s0 1 1 1 0-1 2 1h-1v3h0c0 1-1 2-2 2h-1v1h3-4c1-2 0-4 1-5 0-1 1-2 1-3h-1l-1-1z" class="K"></path><path d="M352 525h8 1c1 1 2 0 2 2l1 1h-5l-2 1c-3-1-7 0-10 0h-6c-2-1-6 0-8-1v-2-1h5 14z" class="V"></path><path d="M352 525h8 1c1 1 2 0 2 2l1 1h-5l-1-2c-2-1-4-1-6-1z" class="l"></path><path d="M341 529c-2-1-6 0-8-1v-2-1h5c-1 0-2 1-4 1h0c1 0 1 0 2 1h2 0s1 0 1-1v1c3 1 6 0 8 2h-6z" class="L"></path><path d="M355 512h3 3v1 1h0c-1 1-2 1-2 1v4h1 1v1h2 0l1 1c2 0 2-1 3 1h-7-7-16-3v-1h1c1 0 2-1 2-2h0 2 0 1 0 1c0-2 0-3-1-4h-2 0l1-2c1 0 3 0 4-1h1 3 0 8z" class="K"></path><path d="M350 512h1c2 0 4 0 6 1v1c-1 1-3 0-4 0h1l-4-2z" class="P"></path><path d="M352 515h1v1c1 0 1 2 0 3l-2-1c-1-1-1-2-1-3h2z" class="C"></path><path d="M354 519v-1h0l1 1 1-1v-2c1 0 1 0 1 1v2h2 1 1v1h2 0l1 1c2 0 2-1 3 1h-7-7c1-1 1-2 1-3z" class="e"></path><path d="M344 512h3 3l4 2h-1l-1 1h-2l-1-1c-1 0-2 1-3 1s-3 0-4 1v3h1v-1l1-1v1c1 1 4 1 6 1h0c1 1 2 0 4 0 0 1 0 2-1 3h-16-3v-1h1c1 0 2-1 2-2h0 2 0 1 0 1c0-2 0-3-1-4h-2 0l1-2c1 0 3 0 4-1h1z" class="i"></path><path d="M154 185v-2c1 2 3 4 5 4h1 2 0l1 1h1 0l2 2h1l1-1 1 1v1l1 1 1-1 1 1c0 1 0 1-1 2v-1l-1 1c1 1 2 1 3 2l1 1c0 1 0 2 1 3h0c1 1 1 2 1 3h0v1h2l2 1v1h-2v1c-1 0-1 0-1 1h-2c1 1 1 1 2 1v1c-1 0-2 0-3 1v1c1 0 2 1 3 2l1 1-1 1h-1c-1 0-2-1-3-1h-1c-1-1-3-2-5-4l-1 1v-1-1c-2 0-3-1-5-1 0-2-1-2-2-3v-1c0-1 0-1-1-1-2 0-2-1-2-2v-1h-1c-1-1-1-1-1-2h-1c-1 0-2-1-2-2l-2 2v-3-2l1-3 1-2 1-3 2-1z" class="S"></path><path d="M156 198c2 1 2 0 3 1h0c0 1 1 1 1 1v2c-1-1-2-2-4-3h0v-1z" class="H"></path><path d="M165 204c-1 0-2-1-3-1v-2h0c1 0 1 1 2 1l1 1 1-1v2 1l-1-1z" class="D"></path><path d="M160 196c1 1 1 2 2 3l1-1 2 2v2c-2-1-1-1-2-2h-2c0-2-1-2-1-4z" class="n"></path><path d="M149 194l1 2c2 0 2-2 3-1s2 1 2 1c1 1 1 1 1 2v1h0c2 1 3 2 4 3l1 1v1l-2-2h-1c-1-1-1-1-2-1l-2-2s-1 0-2-1c0-1 0-1-1-1l-2 2v-3-2z" class="U"></path><path d="M151 189l2 1h2 1c1 0 1 1 1 1l2 2c0 1 1 1 3 2 1 0 1 1 2 1l1 1-2 1-1 1c-1-1-1-2-2-3h-2 0-1l1-2h-1 0c-2-1-3-2-6-2h0l-1-1 1-2z" class="N"></path><path d="M151 197c1 0 1 0 1 1 1 1 2 1 2 1l2 2c1 0 1 0 2 1h1l2 2v-1l4 3v-2l1 1v-1h1l2 1v2l1 1h2s1 1 1 2v1h1v1c1 0 2 1 3 2l1 1-1 1h-1c-1 0-2-1-3-1h-1c-1-1-3-2-5-4l-1 1v-1-1c-2 0-3-1-5-1 0-2-1-2-2-3v-1c0-1 0-1-1-1-2 0-2-1-2-2v-1h-1c-1-1-1-1-1-2h-1c-1 0-2-1-2-2z" class="C"></path><path d="M167 209h2v1l-2 1h-1l1-2z" class="U"></path><path d="M161 203l4 3v2h2 1s0 1 1 1h-2c-1-1-2-1-2-1h-1l-2-2h-1-1l1-2v-1z" class="D"></path><path d="M166 204h1l2 1v2l1 1h2s1 1 1 2v1h1v1h-1c-1-1-3-2-4-2v-1c-1 0-1-1-1-1h-1-2v-2-2l1 1v-1z" class="d"></path><path d="M166 204h1l2 1v2h-2c-1-1-1-1-1-2v-1z" class="J"></path><path d="M164 196l1-2h1l1 1h0c0 1-1 1-1 2h1 1 2s1 1 1 2v-1h2 0v2h2 0c1 1 1 2 1 3h0v1h2l2 1v1h-2v1c-1 0-1 0-1 1h-2c1 1 1 1 2 1v1c-1 0-2 0-3 1h-1v-1c0-1-1-2-1-2h-2l-1-1v-2-2c0-1-1-1-1-1 0-1-1-1-1-1 0-1-1-1-2-1l-2-2 2-1-1-1z" class="g"></path><path d="M173 210c0-1 1-1 1-1v-1h1c1 1 1 1 2 1v1c-1 0-2 0-3 1h-1v-1z" class="J"></path><path d="M171 199v-1h2 0v2h2 0c1 1 1 2 1 3h0v1h2l2 1v1h-2v1c-1 0-1 0-1 1h-2v-2l-1 1-1-1c-1-1-2-2-3-2h-1c0-1 1-1 0-2l1-1c1-1 1-2 1-2z" class="Z"></path><path d="M176 204h2l2 1v1h-2v1c-1 0-1 0-1 1-1-1-1-2-1-2l-1-1 1-1z" class="k"></path><path d="M154 185v-2c1 2 3 4 5 4h1 2 0l1 1h1 0l2 2h1l1-1 1 1v1l1 1 1-1 1 1c0 1 0 1-1 2v-1l-1 1c1 1 2 1 3 2l1 1c0 1 0 2 1 3h-2v-2h0-2v1c0-1-1-2-1-2h-2-1-1c0-1 1-1 1-2h0l-1-1h-1l-1 2c-1 0-1-1-2-1-2-1-3-1-3-2l-2-2s0-1-1-1h-1-2l-2-1 1-3 2-1z" class="M"></path><path d="M167 190l1-1 1 1v1l1 1-1 1h-1l-1-1c0-1 0-1-1-2h0 1z" class="K"></path><path d="M154 185v-2c1 2 3 4 5 4h1 2 0l1 1h1 0l2 2h1-1c-1 1-1 2-2 2h-2l-1 1h-1v-1h1v-1c-1 0-3-1-4-2-1 0-1-1-3-2 0 0-1 0-2-1l2-1z" class="O"></path><path d="M163 188h1 0c0 1 0 2-1 2h-1l1-1v-1z" class="M"></path><path d="M154 185v-2c1 2 3 4 5 4h1 2 0l1 1v1h-1v1h-2v-2s-2-1-3-1-3-1-3-2z" class="P"></path><path d="M349 271c-1 0 0 0-1-1h2 0c3 1 8 2 9 4 1 1 1 6 1 8h-1 0l-6-3c-8-2-16-3-25-3h0c3 1 6 1 8 1 4 1 7 1 11 3h2l1 1v1c-4-2-8-3-12-3h0c1 2 2 0 2 3l6 1v1c-3 0-5-1-8-1s-6 0-10-1h-2-2-15c-4 0-9 0-13 1h-3l-2 1-4-1v-1h1l-1-2c1-1 2-1 4-1 1-1 2-1 4-1 2-1 4-1 7-2h0-6v-1h0 1 1c1 0 1 0 2-1h1 3 2v-4l1 1v3h4 1c2 0 7 1 9 0l1-1c1 0 1 0 2 1 1 0 3 0 5 1h1 3 3c1-1 1 0 2 0s6 1 7 1l1 1c1 0 2 0 3-1h0v-5z" class="f"></path><path d="M297 279c1 0 3-1 5 0v1h-5-1l1-1z" class="S"></path><path d="M302 276l1 1v1c-1 1-4-1-6 1l-1 1h1 0l-1 1-1-2c-1 0-2 0-3 1l-1-1c1-1 2-1 4-1 2-1 4-1 7-2z" class="p"></path><path d="M313 279c3 0 8 0 12 1 2 0 4 1 6 2h0-3-2-2c-2-1-4-2-7-2-1 0-3 0-4-1z" class="h"></path><path d="M291 279l1 1c1-1 2-1 3-1l1 2 1-1-1 3h-3l-2 1-4-1v-1h1l-1-2c1-1 2-1 4-1z" class="C"></path><path d="M288 282h1c2-1 3-1 5-1l-3 1v1h2l-2 1-4-1v-1h1z" class="O"></path><path d="M296 281l1-1-1 3h-3-2v-1l3-1h2z" class="V"></path><path d="M336 277c4 1 7 1 11 3h2l1 1v1c-4-2-8-3-12-3h0c1 2 2 0 2 3l-4-1-1-1h-2c-2-1-6-1-7-2h3 10c-1 0-2-1-3-1z" class="U"></path><path d="M304 274h2v-4l1 1v3h4 1c2 0 7 1 9 0l1-1c1 0 1 0 2 1 1 0 3 0 5 1h1c-2 1-7 0-10 0-5 0-12 0-18 1h-6v-1h0 1 1c1 0 1 0 2-1h1 3z" class="e"></path><path d="M358 280l-8-3c0-1 1-3 0-4v-1h1c1 0 6 1 7 2h1 0c1 1 1 6 1 8h-1 0c1-1 1-2 1-3l-1-1v1h0l-1 1z" class="B"></path><path d="M359 274c1 1 1 6 1 8h-1 0c1-1 1-2 1-3l-1-1v1h0l-1 1v-1c-1-1-4-2-5-3l-2-2h1c1 0 2 0 2 1h4v-1h1 0z" class="d"></path><path d="M302 280c3-1 7-1 11-1 1 1 3 1 4 1 3 0 5 1 7 2h-15c-4 0-9 0-13 1l1-3h0 5z" class="j"></path><path d="M227 580c1 1 2 1 2 2v1h-1v3l2 1v1 2c1 0 1 1 2 2h-3-2l1 1c0 1-1 2-2 2v-1h-1-6v1h1 1c-1 1-2 0-2 1h0v7h3v2l-1 2c-2 0-4 1-6 0h-3s1 0 1-1c-1 0-2 1-3 0-2-1-5 1-7 0h-3l1-1-2-1h-1 1l1-1v-7h1v-1s-1 0-1-1 0-3 1-4l1-1c3 1 7 1 10 1l-1-1-1-4c0-1 0-2 1-4h0 4l1-1h0c0 1 0 1 1 1h1 2 4 3v-1z" class="V"></path><path d="M209 593l5 1h0-1c-2 1-4 1-6 1h-6c1-1 3 0 4-1h1c1-1 3-1 3-1z" class="O"></path><path d="M209 593h-3-1c2-1 6-1 8-1h2l-1 2-5-1z" class="j"></path><path d="M219 590h1 0 5c-1 1-3 1-3 2 1 0 2 0 2 1 1 0 1 0 1 1h-6c-4 1-7 1-12 1 2 0 4 0 6-1h1 3l1-1c-1-1-1-1-2-1h0v-1h1s1 0 2-1h-2-1 0 3z" class="K"></path><path d="M227 580c1 1 2 1 2 2v1h-1v3l2 1v1 2c1 0 1 1 2 2h-3-2l1 1c0 1-1 2-2 2v-1h-1c0-1 0-1-1-1 0-1-1-1-2-1 0-1 2-1 3-2h-5 0 2v-1c0-2 1-4 1-7 0 0 0-1 1-1h3v-1z" class="I"></path><path d="M223 582l1 1v5c-1 1-1 1-2 1 0-2 1-4 1-7z" class="E"></path><path d="M222 590h3c2-2 0-5 2-7v6c2 0 1-1 1-1 1-1 1-1 2-1v1 2c1 0 1 1 2 2h-3-2l1 1c0 1-1 2-2 2v-1h-1c0-1 0-1-1-1 0-1-1-1-2-1 0-1 2-1 3-2h-5 0 2z" class="U"></path><path d="M227 592v-1c0-1 1-1 2-1v-2h1v2c1 0 1 1 2 2h-3-2z" class="V"></path><path d="M216 580h0c0 1 0 1 1 1h1 2 4c-1 0-1 1-1 1 0 3-1 5-1 7v1h-2-1-3 0v-1l-1 1h-3l-1-1-1-4c0-1 0-2 1-4h0 4l1-1z" class="Z"></path><path d="M217 587c-1-1-1-4 0-5h1 0v1l1 1h-1c0 1 0 1 1 1l-2 2h0z" class="G"></path><path d="M219 585v1 4h-3c0-1 0-2 1-3h0l2-2z" class="a"></path><path d="M211 581l2 2 1-1h1v3l-1 1v1c1 1 1 1 1 3h-3l-1-1-1-4c0-1 0-2 1-4z" class="B"></path><path d="M220 581h4c-1 0-1 1-1 1 0 3-1 5-1 7v1h-2-1v-4-1c-1 0-1 0-1-1h1l-1-1v-1h0v-1h2z" class="F"></path><path d="M218 582h2c0 1 0 1-1 2l-1-1v-1z" class="X"></path><path d="M198 604h1l1-1v-7h1v-1s-1 0-1-1 0-3 1-4l-1 4 1 1h6c5 0 8 0 12-1v1h1 1c-1 1-2 0-2 1h0v7h3v2l-1 2c-2 0-4 1-6 0h-3s1 0 1-1c-1 0-2 1-3 0-2-1-5 1-7 0h-3l1-1-2-1h-1z" class="n"></path><path d="M211 603v-7h0 1c0 2 1 5 0 7h-1z" class="a"></path><path d="M213 596h2v6l-1 1h0l-1-7z" class="E"></path><path d="M216 603v-7h1 1v7h-2z" class="Y"></path><path d="M218 596h1v7h3v2l-1 2c-2 0-4 1-6 0h-3s1 0 1-1c-1 0-2 1-3 0-2-1-5 1-7 0h-3l1-1-2-1c1-1 2 0 3-1 2 0 3 1 4 0 1 0 1 0 2 1v-6h1c0 2-1 4 0 5h2 1c1 1 3 0 4 0h2v-7z" class="V"></path><path d="M199 604c1-1 2 0 3-1 2 0 3 1 4 0 1 0 1 0 2 1h2c-2 2-7 1-9 1l-2-1z" class="T"></path><path d="M218 596h1v7h3v2l-1 2c-2 0-4 1-6 0h3v-1h-3v-1c2 0 4 0 6-1h0l-3-1h0v-7z" class="g"></path><path d="M136 475c1 0 2-1 2-1h4 1v1h1 1c0 1 1 3 3 3-1-1-1-2-2-3l1-1h4l1 1c1 1 1 1 3 1 0 1 0 2 1 2h2 1v2l1-1v-1h0l1 1v1c1 1 1 2 1 3v1h0c1 0 2 1 4 1l2 1 2 2 1 1 1 1 2 1v1s1 0 2 1c2 1 3 3 5 5 1 2 1 4 3 7h0c-1 1-2 1-3 1l-4-2c-1-1-2-1-2-2h-3c-1 0-1-1-2-2l-1 1-2-2h-1-2c1-1 1 0 1-1h0v-1c-1 0-1-1-2-1-1-2-2-3-3-4h-1v1c1 1 1 1 1 2l-1 1h-1c-2-1-4-3-5-5h0c-2-1-2-2-3-4h-1c0 1-1 1-2 1l-8-8h-1l-1-3-1-2z" class="O"></path><path d="M160 488h0l1 1c1 0 1-1 2-1h0v2c1 0 2 2 3 2v2l2 1v1c2 2 2 1 3 1l1 1v2c1 0 2 1 3 2h-3c-1 0-1-1-2-2 0 0-1-1-2-1 0-1 0-2-1-3s-1 0-2-2h0c-2-1-2-2-3-3-1 0-1 0-2-1h0v-2z" class="n"></path><path d="M162 484c1 0 2 1 4 1l2 1 2 2 1 1 1 1 2 1v1s1 0 2 1h-2l1 1v1h1c-2 0-2-1-3 0-1 0-2 0-2 1-1-1-1-1-2-1l-1 1v-1l-2-1v-2c-1 0-2-2-3-2v-2h0c-1 0-1 1-2 1l-1-1h0c0-1 0-1 1-1l-1-1 2-2z" class="U"></path><path d="M168 486l2 2c-1 0-1 1-2 1s-1 0-2-1c1 0 1-1 2-1v-1z" class="G"></path><path d="M172 490l2 1v1h-1c0 1-1 1-2 1-1-1 0-1 0-2l1-1z" class="F"></path><path d="M168 496l1-1c1 0 1 0 2 1 0-1 1-1 2-1 1-1 1 0 3 0h-1v-1l-1-1h2c2 1 3 3 5 5 1 2 1 4 3 7h0c-1 1-2 1-3 1l-4-2c-1-1-2-1-2-2-1-1-2-2-3-2v-2l-1-1c-1 0-1 1-3-1z" class="C"></path><path d="M171 497c1 1 1 1 2 1l3 3c0 1 1 1 2 2l1-3h-1l3-2c1 2 1 4 3 7h0c-1 1-2 1-3 1l-4-2c-1-1-2-1-2-2-1-1-2-2-3-2v-2l-1-1z" class="U"></path><path d="M179 500c1 1 2 1 2 2l-1 1h-2l1-3z" class="G"></path><path d="M136 475c1 0 2-1 2-1h4 1v1h1 1c0 1 1 3 3 3-1-1-1-2-2-3l1-1h4l1 1c1 1 1 1 3 1 0 1 0 2 1 2h2 1v2l1-1v-1h0l1 1v1c1 1 1 2 1 3v1h0l-2 2 1 1c-1 0-1 0-1 1v2c-2-1-1-1-1-3h0-3l-3-3s-1-1-1-2h-1c-1 1-1 1-1 2l1 1c1 1 1 3 3 4h0c1 1 1 1 1 2h-2 0c-2-1-2-2-3-4h-1c0 1-1 1-2 1l-8-8h-1l-1-3-1-2z" class="l"></path><path d="M151 485v2h-1-1v-1l2-1z" class="N"></path><path d="M155 480l1 1v1c-1 0-1 1-2 1v-1l1-2z" class="p"></path><path d="M159 480l1-1v-1h0l1 1v1c1 1 1 2 1 3v1h0l-2 2 1 1c-1 0-1 0-1 1v2c-2-1-1-1-1-3 0-1 0-1-1-2h-2v-1c1 0 2 0 2-1l1 1h2 0l1-1-1-1-1 1c-1-1-1-1-1-2v-1z" class="o"></path><path d="M159 480l1-1v-1h0l1 1v1l-1-1c0 1 1 2 1 2v1c-1 0-1 0-1 1-1-1-1-1-1-2v-1z" class="k"></path><path d="M136 475c1 0 2-1 2-1h4 1v1h-1l-1 1h3l1 1v2c-2-1-3-1-5 0l-1 1h-1l-1-3-1-2z" class="K"></path><path d="M137 477h2l1 2-1 1h-1l-1-3z" class="M"></path><path d="M146 475l1-1h4l1 1c1 1 1 1 3 1 0 1 0 2 1 2-1 1-2 1-2 2h1l-1 2c-2-1-3-3-4-3s-2-1-2-1c-1-1-1-2-2-3z" class="c"></path><path d="M155 476c0 1 0 2 1 2-1 1-2 1-2 2-1 0-1-1-2-1 0-1 1-2 1-3h2z" class="o"></path><path d="M140 479c2-1 3-1 5 0s4 3 5 5l1 1-2 1v1c0 1-1 1-2 1l-8-8 1-1z" class="p"></path><path d="M132 432c1 0 1 1 2 2 1-1 3-1 4 0h1v1c-1 0 0 1 0 1h1v-2l-1-1h1 0c1 1 5 2 5 2 1 1 1 2 1 3 1 0 0 0 1 1v1c1 0 1 1 2 1l1 1c-1 0-1 1-1 1v1c1 1 2 0 2 1v1c-1 0-1 0-2-1h-3l-1 1c1 1 1 1 1 2v2h-1v1h1v1c-1 2-2 2-1 4h0v1h-1l-1-1v-1l1-1h-2c-1-1-1-1-2 0h-1l-2 1c1 0 2 1 2 1 0 2 1 2 2 3v1h0l-1 1c-1 0-2 0-2-1h-1v1 1c-2 1-3 0-4 0v1c1 1 1 1 2 1v1c-1 0-1 0-2-1h-1l-2-2h0v-1-1c-1 0-1 0-2-1v-2l-1-2 1-1v-3l-1 2h-1v-1s-1-1-1-2h0l-2-2-1-2v-1l-1-1-1 2-1-1s0-1 1-2c-1-1-2-1-2-2 1-3-1-2-1-4l2-2v1-1h0c1 1 1 1 1 2l1-1h1v3l1 1h1v-2l2 2 2-1 1 1c0 2-1 1 0 2 1 0 2 0 2-1v-2c-1-1-1-1-2-1l2-1c-1-1-1-1-1-2h3c-1-1-1-2-2-3h1z" class="F"></path><path d="M126 442h2v1 1h-1c-1-1-1-1-1-2h0z" class="B"></path><path d="M137 446v1l1 1 1 2s-1 0-1 1c-1-1-1-1-2-1l-1-1c1 0 1-1 0-2h-1c1 0 2-1 3-1z" class="b"></path><path d="M135 438h1c0-1-1-1-1-2s1-1 1-1h1v3h2c-1 1-1 2-2 2h-2l1-1-1-1z" class="I"></path><path d="M137 447l1-1h0c1-1 1-2 2-2l1 1-1 2h1 2 1v1l-1 1c-1 0-1-1-2-1h-3l-1-1z" class="R"></path><path d="M131 437c-1-1-1-1-1-2h3c0 1 1 2 2 3l1 1-1 1c-3 0-3-2-4-3z" class="a"></path><path d="M146 439h1v1c1 0 1 1 2 1l1 1c-1 0-1 1-1 1v1c1 1 2 0 2 1v1c-1 0-1 0-2-1h-3l-1 1c-1 0-1-1-2-2 1 0 1 0 1-1s-1-1-1-2c1 0 1-1 2-1s1-1 1-1z" class="I"></path><path d="M146 445c0-1 1-1 1-2l-2-1c0-1 1-1 2-1l2 2v1c1 1 2 0 2 1v1c-1 0-1 0-2-1h-3z" class="G"></path><path d="M131 441v-2c-1-1-1-1-2-1l2-1c1 1 1 3 4 3v1h1v1c0 1 1 2 1 4-1 0-2 1-3 1v-2c0-1 1-1 1-2-2 0-2 1-3 2l-2 1v-1l-1-1h0l1-1h0c1 0 2-1 3-1-1-1-1-1-2-1h0zm3-7c1-1 3-1 4 0h1v1c-1 0 0 1 0 1h1v-2l-1-1h1 0c1 1 5 2 5 2 1 1 1 2 1 3 1 0 0 0 1 1h-1 0c-1 0-1 0-2 1h-2c-1 1-1 1-2 1v-1c0-1 1-2 2-2v-1l-1-1c-1 0-1 1-2 1-2-1-1-1-2-3h-3z" class="B"></path><path d="M118 441c1-3-1-2-1-4l2-2v1-1h0c1 1 1 1 1 2l1-1h1v3l1 1h1v-2l2 2h1l-1 2h0 0v4 1h2c-1 1-1 2-1 2 0 1 0 2 1 2l-1 2h-1v-1s-1-1-1-2h0l-2-2-1-2v-1l-1-1-1 2-1-1s0-1 1-2c-1-1-2-1-2-2z" class="d"></path><path d="M124 438l2 2h1l-1 2h0 0l-1-1-1 1h-1v-1h1v-1-2z" class="H"></path><path d="M122 445v-1l1 1h2v2l1 1c0 1 0 1-1 2h0 0l-2-2-1-2v-1z" class="f"></path><path d="M118 441c1-3-1-2-1-4l2-2v1-1h0c1 1 1 1 1 2l1-1h1v3l1 1h1v1h-1l-1-1c-1 0-1 1-2 2 0 0 0 1 1 1v1l-1 2-1-1s0-1 1-2c-1-1-2-1-2-2z" class="C"></path><path d="M134 448h1v2c0 1 1 1 1 2h1v2 1c1 0 2 1 2 1 0 2 1 2 2 3v1h0l-1 1c-1 0-2 0-2-1h-1v1 1c-2 1-3 0-4 0v1c1 1 1 1 2 1v1c-1 0-1 0-2-1h-1l-2-2h0v-1-1c-1 0-1 0-2-1v-2l-1-2 1-1c1 0 2 0 3-1 0-1 0-1-1-1l1-1-1-2h1c1 0 1 1 1 2h1l1-1s-1-1-1-2h1z" class="R"></path><path d="M133 455h2c1 1 3 2 3 4h0c-2 1-2 0-3 0h-1c0-1 1-1 0-2 0 0-1 0-1-1v-1z" class="d"></path><path d="M128 454c1 0 2 0 3-1 0-1 0-1-1-1l1-1-1-2h1c1 0 1 1 1 2h1l1-1s-1-1-1-2h1 0v3h0-2c0 1 0 3 1 4v1c0 1 1 1 1 1 1 1 0 1 0 2h-2l-1-2h-1v1l1 1-1 1-2-1v-2l-1-2 1-1z" class="W"></path><path d="M418 512l1 1 1-1c1 0 1 1 2 0h1v1h1v-1l1 1v-1c1 2 2 3 2 5-1 1-1 1-1 3h-1c0 2 0 2 1 3h1v1c0 2 0 3 1 5 0 1-1 3 0 4 0 1-1 2-1 3v2 6c1 1 0 3 0 4v2l3 6c-1 1-1 2 0 4v2h-7 0-3c-1 0-2 0-3-1-2 0-4 1-6 0 2-4 5-8 6-13-1-2-1-4-1-7v-2l1-1h-1 0v-1c1-1 1-3 1-4v-4-4-12l1-1z" class="M"></path><path d="M418 533v-8h1l1 1v1c0 2 0 4-1 5l-1 1z" class="C"></path><path d="M425 538l1 1v3l-1 1-1-1c-3 0-1-1-2-3v1l-1 1c0-1 0-2 1-3h3z" class="Y"></path><path d="M420 526h0c1 0 1-1 1-1v6 5-1l-1-1c0 1 0 2-1 3l-1-1v-2-1l1-1c1-1 1-3 1-5v-1z" class="N"></path><path d="M419 547h-1l-1-3h1 5 0 3c1 1 1 2 1 4v2c0 1 0 2-1 3 0-2-2-3-2-5v-1h-2-3z" class="c"></path><path d="M423 544h3v3h-1-2v-2h-1v-1h1z" class="a"></path><path d="M424 523l1 1 1 10c0 1 0 2-1 3h1l1 1v6c1 1 0 3 0 4 0-2 0-3-1-4h-3 0l3-1h-1l1-1v-3l-1-1v-1c-2 0-2-1-4 0v-1-5-6h2c1-1 1-1 1-2z" class="H"></path><path d="M421 525h2 1v5 4 2c-1 0-1-1-1-2v-1c0-1 0-1-1-1l-1-1v-6z" class="B"></path><path d="M425 513v-1c1 2 2 3 2 5-1 1-1 1-1 3h-1c0 2 0 2 1 3h1v1c0 2 0 3 1 5 0 1-1 3 0 4 0 1-1 2-1 3v2l-1-1h-1c1-1 1-2 1-3l-1-10-1-1h-2v-1h-1v1c-1 0-1 0-1-1l-1 1h-1v-5-3h1v5h1v-5c1 0 2-1 3-1h2v-1z" class="F"></path><path d="M422 520c1-1 1-2 1-2l1 2v1h-2v-1z" class="E"></path><path d="M422 520v-5h2v1c0 1-1 1-1 2 0 0 0 1-1 2z" class="B"></path><path d="M424 516c1 1 1 2 2 4h-1-1l-1-2c0-1 1-1 1-2z" class="C"></path><path d="M419 547h3 2v1c0 2 2 3 2 5 1-1 1-2 1-3l3 6c-1 1-1 2 0 4v2h-7 0-3c-1 0-2 0-3-1-2 0-4 1-6 0 2-4 5-8 6-13h0c1 1 2 1 2 0h0v-1z" class="h"></path><path d="M422 547h2v1h-2c0 1 1 1 1 2v5c-1 2-1 4 0 6v1h-3l1-1c-1-1-1-2-1-2-1-1-1-3 0-4 0 0 1 0 1-1 1 0 0-3 0-4 1-1 1-2 1-3z" class="L"></path><defs><linearGradient id="S" x1="429.358" y1="556.175" x2="423.08" y2="555.426" xlink:href="#B"><stop offset="0" stop-color="#5e5e5e"></stop><stop offset="1" stop-color="#787677"></stop></linearGradient></defs><path fill="url(#S)" d="M423 562v-1c-1-2-1-4 0-6v-5c0-1-1-1-1-2h2c0 2 2 3 2 5 1-1 1-2 1-3l3 6c-1 1-1 2 0 4v2h-7 0z"></path><path d="M229 592c1 0 2 1 3 0l1-1h0l1 1 1 1c2-1 5-1 7-1h1v1c0 2 0 2 1 3l1 1v2 4l1 1h0-2v1l2 1c0 1 1 2 0 3h0v2l1 1c-2 1-3 1-3 3h-1 0c-3 1-5 2-7 1v-1c-2-1-3-1-4-1l-3 4c-3 1-5 2-7 2l-4 1s0-1-1-1c0-1 0-2 1-3l3-1c1-1 0-7 0-9l1-2v-2h-3v-7h0c0-1 1 0 2-1h-1-1v-1h6 1v1c1 0 2-1 2-2l-1-1h2z" class="Y"></path><path d="M244 596l1 1v2 4l1 1h0-2v1l2 1c0 1 1 2 0 3h0v2l1 1c-2 1-3 1-3 3h-1 0 0c-1-3 0-7 0-10 1-2 0-5 1-7v-2z" class="D"></path><path d="M244 596l1 1v2 4l1 1h0-2v1c-1-1 0-5 0-7v-2z" class="c"></path><path d="M236 606l5-1h0c-1 1-1 1-1 2-1 0-2 0-3 1 1 1 1 1 1 2h1 1c0 1 0 1-1 2s-2 1-3 2v1c-2-1-3-1-4-1l1-1-1-1h0v-1-1h0 2c0-1 1-1 1-2s0-1 1-2h0z" class="F"></path><path d="M232 612l1-1h0 6v1c-1 1-2 1-3 2v1c-2-1-3-1-4-1l1-1-1-1z" class="J"></path><path d="M229 592c1 0 2 1 3 0l1-1h0l1 1 1 1h-1c0 1 0 2 1 3l2-1c1 1 1 1 3 1 0 1-1 1-1 1l-5 1h-1-1 0c-2-1-3 0-5-1h-1l-1-1 1-1c1 0 2-1 2-2l-1-1h2z" class="k"></path><path d="M226 597v-1h1s1 0 2-1c1 0 2 1 3 1h1l-1 1h-5-1z" class="Q"></path><path d="M226 599h4 0c1 0 1 0 2-1h1v1h7v1h-4c-1 1-1 2-1 3l1 1h-3-3-5 0v-1-1c0-1 0-2 1-3z" class="K"></path><path d="M226 599c1 0 2 0 3 1v1h-1c-1 0-2-1-2-2z" class="P"></path><path d="M225 602c0-1 0-2 1-3 0 1 1 2 2 2h1 0v2h-1v-1l-1 1s-1 1-2 1h0v-1-1z" class="D"></path><path d="M233 604c-1-1-1 0-2-1v-2c1 0 2 0 2-1h1c0 1 0 2 1 3l1 1h-3z" class="U"></path><path d="M225 594h1v1l-1 1 1 1h1c2 1 3 0 5 1h0c-1 1-1 1-2 1h0-4c-1 1-1 2-1 3v1h-3-3v-7h0c0-1 1 0 2-1h-1-1v-1h6z" class="a"></path><path d="M225 594h1v1l-1 1 1 1h1c2 1 3 0 5 1h0c-1 1-1 1-2 1h0-4c-1 1-1 2-1 3v-3-1c-1-1 0-2-1-2h-5c0-1 1 0 2-1h-1-1v-1h6z" class="b"></path><path d="M222 603h3v1h0 5 3 3 4l1 1-5 1h0c-1 1-1 1-1 2s-1 1-1 2h-2 0v1 1h0l1 1-1 1-3 4c-3 1-5 2-7 2l-4 1s0-1-1-1c0-1 0-2 1-3l3-1c1-1 0-7 0-9l1-2v-2z" class="W"></path><path d="M231 611l-1-1c0-1 0-2 1-3h1v3h0l-1 1z" class="I"></path><path d="M227 611v-1c0-1-1-1-1-2 1-1 1-1 3-2v6c-1-1-1-1-2-1z" class="G"></path><path d="M236 604h4l1 1-5 1h-5-6v-2h0 5 3 3z" class="i"></path><path d="M231 611l1-1v1 1h0l1 1-1 1-3 4c-3 1-5 2-7 2 0-1 1-1 2-2h0v-1c3-1 1-4 3-6 1 0 1 0 2 1v1h0l2-2z" class="B"></path><path d="M222 605h2c1 3 1 9 0 12v1h0c-1 1-2 1-2 2l-4 1s0-1-1-1c0-1 0-2 1-3l3-1c1-1 0-7 0-9l1-2z" class="a"></path><path d="M345 198l1 1 13 31c2 6 5 12 6 18 0 2-1 5-2 7 0 2-1 4-1 6-1 0-1 1-1 1-1 0-2-1-2-1-1-1-1-1-1-2v-1h0v-7h-1-3-1c-2-1-4-3-5-5l-2-2c-1-2-1-3-2-5v-1c1-1 0-4 1-6 0 1 1 1 2 1-1 0-2-1-2-2v-3-1-4-11-12-2z" class="O"></path><path d="M353 228l2-1v1c0 1 1 1 1 2s1 2 1 2l1 2c0 1 0 1 1 2v2h0l1 1c0 1 0 1 1 2v1c1 1 1 1 1 2h-1 0-5-1l1 1c0 1 1 2 3 2h1c1 1 1 1 2 1v1h0-1-2c0 1-1 1-1 2h-1-3-1c-2-1-4-3-5-5l-2-2c-1-2-1-3-2-5v-1c1-1 0-4 1-6 0 1 1 1 2 1-1 0-2-1-2-2v-3h8z" class="l"></path><path d="M348 246h0v-2-4h1c0 1 1 1 0 1v5h-1z" class="P"></path><path d="M357 237l-1 1c-2 0-4-1-5-2v-1h0c2 1 4 1 6 1v1zm-6 5c0 1 1 2 1 2v3 1 1h-1c0-1 0-1-1-2 0-1 1-2 1-2 0-1-1-1 0-3z" class="V"></path><path d="M351 242v-1h1v1c0 1 1 1 1 2 1 0 1 0 1 1 1 1 2 2 3 4l-1 1h0-2-1 0c0-1 0-1-1-2v-1-3s-1-1-1-2zm2-14l2-1v1c0 1 1 1 1 2s1 2 1 2l1 2c0 1 0 1 1 2v2h0l1 1c0 1 0 1 1 2v1c1 1 1 1 1 2h-1 0-5-1v-1c1-1 4 1 5 0-1-2-3-1-5-1v-1c1 0 2 0 2-1h0l2 1v-1s0-1-1-1-2 0-2 1h-1s-1-1-2-1h0v-1h1c1 0 1 1 2 1 1-1 1-1 2-1l-1-1v-1h0c-1-1-4-1-6-2-1 0-1 1-2 1h-1s-1-1-1-2c-1 0-2-1-2-2v-3h8z" class="P"></path><path d="M353 228l1 1h-2v1c1 0 2 0 4 1h-2c-2 0-5-1-7 0 1 1 1 1 1 2v2s-1-1-1-2c-1 0-2-1-2-2v-3h8z" class="K"></path><path d="M352 230h-1c-2 0-4 0-5-1h1c1 0 3-1 5 0v1z" class="e"></path><path d="M531 396h1c1 0 1 1 2 2h0v2 2h1 0l1 1h2v-1-2h0l-2-2c0-1 0-1 1-2l1 1 1-1c0-1 0-1-1-1l1-1h0c0-1 1-2 1-2v1 1l-1 1h1l-1 3c-1 0-1 0-2-1v1h1c1 1 1 1 1 2 2 0 2 2 3 2s1 1 1 2h0-2v-2h-1v-1l-1 1 1 1h0-1c-1 1-1 1-3 1v1h-1l-2 2h0v3c0 1 0 2-1 2l-1 1c0-1-1-2-1-2-1 1-1 2-2 2h-1c0-1-1-1-2-1v2h-1l-1 1 1 1h1v2h1s1 0 1 1l1 1v1c-1 1-1 1-1 2l1 1-1 1c1 2 0 3 0 4s0 2-1 3h0l-2 2c0 1-1 2-1 3h-1 0l-1-1h-1c0 1-1 1-2 1h0-1 0 0-2-2-1l1-1v-2h-1c-1 1-1 1-2 1-1-1 0-1 0-3h-1-1l-2-2c1-1 2-3 2-4l2-3c0-1-1-1-1-1 1-2 1-4 2-5h1l-1-1c2-1 1-1 2-2l2-2v-2h-1l-3 2-1-1c1-1 1-2 3-2l3-3c1-1 1-3 3-4h0 1v-2l2-3c2 2 0 4 3 4l1-1 2-1v-1c1-1 2-2 3-2z" class="I"></path><path d="M515 412v1 2l-1 1v-1h-1v-1l2-2z" class="G"></path><path d="M520 400l1 1 1 1c0 1-1 2-1 3-1 0-2-1-2-1v-1h1v-1h0v-2z" class="b"></path><path d="M516 424v-1l-1-1 1-1-1-1h0-1l1-1-1-2h0l2 1c0 1 1 1 2 1 1 1 1 1 1 2s0 1-1 2-1 0-2 0v1zm10-24l2-1v-1c1-1 2-2 3-2l1 1c-1 1-1 2-2 3h0c1 0 2-1 3-1h0v2 2h0c-1 0-2 1-2 2 0 0-1 0-1 1l-1-1h-1-1v-1h0 1v-2c-1-1-1 1-2-2z" class="B"></path><path d="M522 412s0-1-1-1h0c1-2 2-3 3-4h4 0 3c0 1-1 2-1 3v1h0c-1 1-1 2-2 2h-1c0-1-1-1-2-1v2h-1l-1 1c-1 0-1 1-2 1l-2-1h1 0v-1l1-1 1-1z" class="b"></path><path d="M525 410c1-1 1-1 2-1 1 1 1 1 3 2h0c-1 1-1 2-2 2h-1c0-1-1-1-2-1 1-1 1-1 0-2z" class="C"></path><path d="M522 412l1-1h0c1 0 2 0 2-1 1 1 1 1 0 2v2h-1l-1 1c-1 0-1 1-2 1l-2-1h1 0v-1l1-1 1-1z" class="F"></path><path d="M521 413v1h0l2-1h0l1 1-1 1c-1 0-1 1-2 1l-2-1h1 0v-1l1-1z" class="B"></path><path d="M519 415l2 1c1 0 1-1 2-1l1 1h1v2h1s1 0 1 1l1 1v1c-1 1-1 1-1 2l1 1-1 1c1 2 0 3 0 4s0 2-1 3h0l-2 2c0 1-1 2-1 3h-1 0l-1-1h-1c0 1-1 1-2 1h0-1 0 0-2-2-1l1-1v-2h-1c-1 1-1 1-2 1-1-1 0-1 0-3h-1-1l-2-2c1-1 2-3 2-4l2-3h0c1 0 1 1 2 1v-2h-1c1-1 1-2 1-3h1c0 1-1 1-1 2l2 1v1 1 1c1-1 1-1 2-1v-1c1 0 1 1 2 0s1-1 1-2 0-1-1-2c0-1 0-2-1-3h0l1-1h1z" class="F"></path><path d="M510 432l2 2h0c-1 1-1 1-2 1-1-1 0-1 0-3z" class="X"></path><path d="M513 429v1l-1 1h0c-1-2-1-2 0-3l-1-2 1-1 1 1-1 1c1 1 1 1 1 2z" class="m"></path><path d="M513 426c1 0 2 0 3 1h1l-2 1-2 1c0-1 0-1-1-2l1-1z" class="B"></path><path d="M506 430c1-1 2-3 2-4l2 2c0 1 0 1-1 2v2h-1l-2-2z" class="C"></path><path d="M517 427v-1s1-1 2-1v1h1 2l-1 1h-2c0 1-1 1-1 2h0l-3 5h-1c0-2 1-3 2-5l-1-1 2-1z" class="m"></path><path d="M522 419l2 1 1 1-1 2s-1 1-1 2l-1 1h-2-1v-1-1c1-1 1-2 2-3 0-1 1-1 1-2z" class="d"></path><path d="M521 421h1l-1 2 2 2-1 1h-2-1v-1-1c1-1 1-2 2-3z" class="S"></path><path d="M524 416h1v2h1s1 0 1 1l1 1v1c-1 1-1 1-1 2h-3l1-2-1-1-2-1h0c0-1 0-1-1-2 1-1 2 0 3-1z" class="W"></path><path d="M525 421h0v-2h1v1h2v1c-1 1-1 1-1 2h-3l1-2z" class="C"></path><path d="M524 423h3l1 1-1 1c1 2 0 3 0 4s0 2-1 3h0l-2 2c0 1-1 2-1 3h-1 0l-1-1h-1c0 1-1 1-2 1h0-1 0 0-2-2-1l1-1v-2h1 1l3-5h0c0-1 1-1 1-2h2l1-1 1-1c0-1 1-2 1-2z" class="b"></path><path d="M524 434h-1v-3h-1v-1c1-1 1-1 2 0 1 0 1 1 2 2l-2 2z" class="B"></path><path d="M518 429l1 1v2l1 1c-1 1-1 0-2 0-1 1-3 3-3 4h-2-1l1-1v-2h1 1l3-5z" class="X"></path><path d="M406 588l3-1c0 2 0 3 2 4l-1 1h-1c0 1 0 1 1 1 0 1 1 1 1 1h2 0v1h1l-1 1 1 1 1-1h3c-1 0-2 0-2 1l1 1v-1h2l-1 1s0 1-1 1l1 1h1c3-2 6-3 9-2l3 1h1c1 1 2 2 3 4l1 1v3 2 2c0 2 0 4 1 6h1l-7 1-4 1c-2 0-3 0-5 1h-1 0c-1-1-1-2-3-2-2-1-7 1-10 1-2 0-5-1-7-3h-1c1-1 1-1 2-1 0-1 0-2 1-3v-5h-5v-1h6c-1-1-1-2-1-4 2 0 3 0 4-1h2v-1c0-1 0-3-1-4v-3-1-1c-1-2-1-2-2-3z" class="a"></path><path d="M410 606c1 2 0 8 0 11h-1l-1-1c0-2 0-6 1-8v-1h-2l-1-1h3 1zm2-1l1 1c0-1 1-2 2-2h0v1 1l2-1c0 1 0 1-1 2l-1 1v-1c-1 1-1 2-2 2v1h-1-1c0-2 0-4 1-5z" class="B"></path><path d="M415 608h1s1-1 2-1h0c-1 1-1 2-1 3l1 1v3h0c0 1 0 1 1 2h-2c-1-1-2-1-2-3-1-1-1-3 0-5z" class="G"></path><path d="M407 601h2l1 5h-1-3-2c-1-1-1-2-1-4 2 0 3 0 4-1z" class="C"></path><path d="M417 605v-1c1 0 3-2 4-2 0-1 1 0 2 0-1 0-1 1-2 2l2 1 1 1-1 1h-3s-1 1-2 1h0v3l-1-1c0-1 0-2 1-3h0c-1 0-2 1-2 1h-1 0l1-1c1-1 1-1 1-2z" class="m"></path><path d="M418 608v-1c1-1 2-3 3-3 0-1 0 0 0 0l2 1 1 1-1 1h-3s-1 1-2 1z" class="H"></path><path d="M411 594h2 0v1h1l-1 1 1 1 1-1h3c-1 0-2 0-2 1l1 1v-1h2l-1 1s0 1-1 1l1 1-3 2v2c-1 0-2 1-2 2l-1-1h0c0-1 0-2-1-3v-1c0-1 1-2 0-3 0-1-1-1-1-3l1-1z" class="X"></path><path d="M414 601l1 1v2c-1 0-2 1-2 2l-1-1h0c1-2 1-3 2-4z" class="H"></path><path d="M414 597l1-1h3c-1 0-2 0-2 1l1 1v-1h2l-1 1s0 1-1 1l1 1-3 2-1-1 3-3h-1c-1 0-1-1-2-1z" class="S"></path><path d="M418 600h1c3-2 6-3 9-2l3 1h1c1 1 2 2 3 4l1 1v3 2 2c0 2 0 4 1 6h1l-7 1-1-1h-3-5c-1-1-2-1-3-1-1-1-1-1-1-2h0v-3-3h0c1 0 2-1 2-1h3l1-1-1-1-2-1c1-1 1-2 2-2-1 0-2-1-2 0-1 0-3 2-4 2v1l-2 1v-1-1h0v-2l3-2z" class="D"></path><path d="M423 602h1v1c0 1 0 1-1 2h0l-2-1c1-1 1-2 2-2z" class="F"></path><path d="M431 605h1l2 1-1 1c-1 0-3 0-4-1 1 0 1 0 2-1z" class="H"></path><path d="M424 603c2 1 2 1 3 0 1 0 1-1 2 0h0v2c-2 1-3 0-5 0v-1-1z" class="S"></path><path d="M424 602c1-1 3-1 5 0h1l-1 1c-1-1-1 0-2 0-1 1-1 1-3 0h0v-1z" class="C"></path><path d="M426 614s-1-1-1-2 0-3 1-5h0c2 1 1 4 1 6h0s0 1-1 1z" class="E"></path><path d="M427 613c1-1 1-4 1-6h1v2h1c0-1 0-1 1-2v1 2c0 2 0 3 1 4h-6c1 0 1-1 1-1z" class="Y"></path><path d="M432 599c1 1 2 2 3 4l1 1v3c-1 0-1-1-1-2l-1 1-2-1h-1-1c-1-1-1-1-1-2h0l1-1c0 1 0 1 1 0h0c-1-1-2-2-3-2v-2l3 1h1z" class="B"></path><path d="M432 599c1 1 2 2 3 4l1 1v3c-1 0-1-1-1-2-1-2-2-4-4-6h1z" class="C"></path><path d="M420 607h3 0l1 1v6h-1-2-3v-3-3h0c1 0 2-1 2-1z" class="X"></path><path d="M420 607h3 0v1c0 1 0 1-1 2-1-1-1-2-2-3z" class="U"></path><path d="M418 608c1 2 1 3 1 4v1h1c1 0 1 0 1 1h-3v-3-3z" class="a"></path><path d="M419 612c2 0 2 0 3-1 1 0 1 1 1 1v2h-2c0-1 0-1-1-1h-1v-1z" class="C"></path><path d="M418 600h1c3-2 6-3 9-2v2c1 0 2 1 3 2h0c-1 1-1 1-1 0h-1c-2-1-4-1-5 0h-1c-1 0-2-1-2 0-1 0-3 2-4 2v1l-2 1v-1-1h0v-2l3-2z" class="H"></path><path d="M418 600h1c3-2 6-3 9-2v2c-2 0-6-1-7 0-2 0-4 3-6 4h0v-2l3-2z" class="G"></path><path d="M435 605c0 1 0 2 1 2v2 2c0 2 0 4 1 6h1l-7 1-1-1h-3c1-1 4-1 5-1h3v-1l-3-1c-1-1-1-2-1-4 1 0 1-1 1-2l1-1 1-1 1-1z" class="b"></path><path d="M441 615c1 1 2 1 2 2 1 0 1 1 1 1h2l1 2c1 0 1 1 1 1l1 1h1l-1 1v1 1h0v1h3l2-1 1 1h1c1-1 2-1 3-1l2 1c0 1 1 1 2 2h2c2 2 5 1 7 1 1-1 2 0 3-1l1 1c0 1 0 1-1 1s-3 1-4 2h0v2h-2l-5 3v-1s-2 0-2 1h-1-2c-1 1-2 1-3 1s-2 0-3-1v-1h-1v1h-3-2l1-1v-1c-3 1-5 1-7 1l-2-1v-1h-2c-1-1-2-1-3-1h-1v-1h-1-5-1c-1-1-2-1-4-1h-1-2l-1-1 1-1h-1-1v2l-1-1c-1 0-1 0-1 1l-2-1v-1l4-3 4 2h1v-1c-2 0-2 0-3-1v-1h-2v-1h-2v-2h0 2 1c1-1 2 0 3-2h0 1c2-1 3-1 5-1l4-1 7-1 3-2z" class="m"></path><path d="M458 629l2-1h0c1 1 1 2 0 3-1 0-1-1-2-1v-1z" class="H"></path><path d="M463 628h2c-1 1-1 2-2 2h-1c0-1 0-2 1-2z" class="D"></path><path d="M448 631h2v1h-2s-1 1-2 0v-1h2z" class="R"></path><path d="M438 623c2-1 3-1 5-1l-2 2c-2 0-2 0-3-1zm16 2l1 1c-1 1-2 2-4 2h0l1-2 2-1z" class="d"></path><path d="M441 620c1 1 2 1 3 2h-1c-2 0-3 0-5 1v-1c1-1 2-1 3-2z" class="g"></path><path d="M432 623c2-1 3-1 4 0 1 0 2 1 2 1l-2 1-1-1h-2l-1-1z" class="Z"></path><path d="M458 629v1h0l-1 1c-1 2-4 1-6 1l3-2c2 0 3 0 4-1z" class="R"></path><path d="M434 627c1 0 2 0 3-1 1 0 2 0 3 1h0c0 1-2 1-3 1h-1-1-1v-1z" class="H"></path><path d="M459 625l2 1c-2 1-3 3-5 3v-1h-1c1-1 1-1 1-2 1-1 2-1 3-1zm-10 1h3l-1 2h0c-1 1-1 1-1 2-1-1-2-1-2-1-1 0-1-1-1-1 0-1 1-1 2-2z" class="D"></path><path d="M449 626h3l-1 2h0c-1 0-1 0-2-1v-1z" class="J"></path><path d="M464 636l2-2c1 0 3-2 5-2v2h-2l-5 3v-1z" class="E"></path><path d="M444 618h2l1 2c-1 1-1 2-3 2h0c-1-1-2-1-3-2 1 0 2-1 3-2z" class="c"></path><path d="M442 628h3c0 1-2 1-2 2 0 0 1 0 1 1h-1-2-4 0 0c0-1 2-2 3-2s2 0 2-1z" class="D"></path><path d="M435 630h1l-2 1v1c1 0 2 0 3 1h3c1-1 1-1 3-1l1 1h-4c0 1-1 1-1 1h-2c-1-1-2-1-3-1h-1v-1h-1 0l3-2z" class="F"></path><path d="M444 633c1 0 2 0 3 1h3 0l-2 1c-3 1-5 1-7 1l-2-1v-1s1 0 1-1h4z" class="D"></path><path d="M448 621l1 1h1l-1 1v1 1h0-2c-1 0-1 1-2 0h-1c-1 1-2 0-3 0 0 1 0 1-1 1v-1s1-1 2-1 3-1 4-2c1 0 1 0 2-1h0z" class="d"></path><path d="M452 636v-1c1-2 3-1 4-2 2 0 3 0 5-1h2c1-1 3-1 5-2h0c-3 2-6 4-10 4h0c-1 1-2 1-2 1l-3 1h-1z" class="Y"></path><path d="M431 629c1-1 2-1 3-2v1h1v2l-3 2h0-5-1c-1-1-2-1-4-1v-1h2c0-1-1-1-2-1v-1h3 5-1c-1 1-3 1-4 1v1c1 0 1 1 3 0l3-1z" class="D"></path><path d="M431 629c1-1 2-1 3-2v1c0 1-1 2-2 2l-1-1z" class="S"></path><path d="M431 623h1l1 1h2l1 1-4 2-2 1h-5v-1h-1l-1-1c1 0 2 0 3-1 1 0 2-1 3-1l2-1z" class="N"></path><path d="M431 623h1l1 1h2l-1 1c-2 0-4 0-5-1l2-1z" class="o"></path><path d="M435 624l1 1-4 2c-1 0-1-1-2-1 1-1 2-1 4-1l1-1z" class="J"></path><path d="M430 626c1 0 1 1 2 1l-2 1h-5v-1h0c2 0 3 0 5-1z" class="c"></path><path d="M441 615c1 1 2 1 2 2v1l-1 1c-2 1-5 2-7 2h-3l-1 2-2 1c-1 0-2 1-3 1-1 1-2 1-3 1l1 1h1v1h-3v1c1 0 2 0 2 1h-2v1h-1-2l-1-1 1-1h-1-1v2l-1-1c-1 0-1 0-1 1l-2-1v-1l4-3 4 2h1v-1c-2 0-2 0-3-1v-1h-2v-1h-2v-2h0 2 1c1-1 2 0 3-2h0 1c2-1 3-1 5-1l4-1 7-1 3-2z" class="H"></path><path d="M423 621h2c1 1 1 0 2 1v1c-1 0-2-1-4-1v-1z" class="D"></path><path d="M417 624h0c2 0 3 0 4-2 1 0 2 1 3 1-1 1-2 2-3 2-1 1-1 1-2 1v-1h-2v-1z" class="U"></path><path d="M441 615c1 1 2 1 2 2v1l-1 1c-2 1-5 2-7 2h-4-1-3-1v-1l1-1 4-1 7-1 3-2z" class="k"></path><path d="M431 621l2-2c2 0 4 1 6 0v-1c1 0 2 0 3 1-2 1-5 2-7 2h-4z" class="N"></path><defs><linearGradient id="T" x1="507.633" y1="449.65" x2="514.191" y2="466.308" xlink:href="#B"><stop offset="0" stop-color="#585657"></stop><stop offset="1" stop-color="#797979"></stop></linearGradient></defs><path fill="url(#T)" d="M520 436h1l1 1h0 1l-1 2s1 0 1 1c1-1 2-1 2-2h1v2h2 0c-1 1 0 1-1 1v2h2l1-1 1 1-1 1v1h1 0l1-1h1l-2 2s1-1 2-1v3 1c-3 4-6 9-9 13-1 0-2 2-3 3 0-1-1-1-1-1l-2 2c0 1-1 1-2 2h-2c-1 1-1 1-2 1-1 1-3 2-4 3v1c-1 0-1 1-2 1h0c-1 1-2 1-3 1h-4v-1h-2v1h-2c0-1 1-1 1-2v-2l-2 1v-1-1l2-2v1h2v-1l1-1 1-1h0c1-1 1-2 2-3 2-1 2-2 4-3-1 0-1-1-2-1s0 0-1-1l2-1c-1 0-1-1-1-2 0 0 1 0 1-1v-1c1-1 1-1 1-3v-2c-1 0-1-1-2-2h1 4 0c1-1 2-1 3-1h1 1 1l2-2c-1-1-1-2-1-3l-1-1 2-2h0 1 0c1 0 2 0 2-1z"></path><path d="M513 462v-1c0-1-1-1-1-2l1-1 2 2c-1 1-2 1-2 2z" class="g"></path><path d="M519 446h2v1l-1 1h-1v-2z" class="U"></path><path d="M510 467l1-2c0-2-1-1-2-2 1-1 1-1 2-1h1c0 1-1 1 0 2v2c-1 0-1 0-2 1z" class="g"></path><path d="M508 458c0 1 1 2 1 3h-3c-1 1-2 2-2 3h-1l-1-1c2-1 2-2 4-3h0l2-2z" class="R"></path><path d="M511 453c1-1 2-3 4-4l1 1v1h-2c0 2-1 4-2 5l-1 1c0 1 0 1-1 2-1 0-1-1-2-1 1-2 2-3 3-5z" class="D"></path><path d="M522 447v2h1l2-2v1l1 1v1c-1 1-2 2-2 3h-1l-4 4v1c-1 0-2 0-3 1 1-2 2-3 3-4v-1h1c1-1 0-2 0-3l2-2v-2h0z" class="J"></path><path d="M499 467l1 2h1c0-1 0-2 1-3h1c1 0 1-1 2-1 1-1 2-2 3-2 0 1-1 2-2 3s-3 2-4 4h0l-3 3h-1-2v-2l-2 1v-1-1l2-2v1h2v-1l1-1z" class="H"></path><path d="M496 468v1h2v-1c0 1 1 2 1 3h1v-1h1 0 1l-3 3h-1-2v-2l-2 1v-1-1l2-2z" class="S"></path><path d="M497 470c1 1 1 1 1 3h-2v-2l1-1z" class="H"></path><path d="M496 468v1h2l-1 1h0l-1 1-2 1v-1-1l2-2z" class="f"></path><path d="M516 459c1-1 2-1 3-1l-3 3-1 1c-1 1-4 5-4 7-1 1-1 1-2 1l-2 2v1l-1 1h0c-1 1-2 1-3 1h-4v-1h-2v1h-2c0-1 1-1 1-2h2 1 1c1-1 2-2 4-3v1c2 0 2-2 4-2 1-1 1-2 2-2 1-1 1-1 2-1v-2c-1-1 0-1 0-2h1c0-1 1-1 2-2l1-1z" class="N"></path><path d="M509 446c1-1 2-1 3-1h1 1 1v3h0v1c-2 1-3 3-4 4-1 2-2 3-3 5h0l-2 2h0c-1 0-1-1-2-1s0 0-1-1l2-1c-1 0-1-1-1-2 0 0 1 0 1-1v-1c1-1 1-1 1-3v-2c-1 0-1-1-2-2h1 4 0z" class="g"></path><path d="M506 456h1 0v1c-1 0-1 0-1 1v2h0c-1 0-1-1-2-1s0 0-1-1l2-1 1-1z" class="N"></path><path d="M506 450h1 1l-1 1v1c0 1 0 2-1 3v1l-1 1c-1 0-1-1-1-2 0 0 1 0 1-1v-1c1-1 1-1 1-3z" class="n"></path><path d="M509 446c1-1 2-1 3-1h1 1 1v3h0v1c-2 1-3 3-4 4h0-1l-1-1c1-1 1-2 2-3h0v-1h-3-2 0c-1 0-1-1-2-2h1 4 0z" class="J"></path><path d="M515 445v3h0c-1 0-2 0-3 1v-1l2-3h1z" class="g"></path><path d="M520 436h1l1 1h0 1l-1 2s1 0 1 1c1-1 2-1 2-2h1v2h2 0c-1 1 0 1-1 1v2h2l-1 1v1l-3 3v-1l-2 2h-1v-2h0-1v-1h-2v-2l-3 2s-1 1-1 2v-3l2-2c-1-1-1-2-1-3l-1-1 2-2h0 1 0c1 0 2 0 2-1z" class="D"></path><path d="M523 440c1-1 2-1 2-2v3h-1 0-2l1-1z" class="R"></path><path d="M517 443c0-1 1-2 2-2v1h1 1v1h-1l-1 1-3 2s-1 1-1 2v-3l2-2z" class="J"></path><path d="M525 438h1v2h2 0c-1 1 0 1-1 1v2h2l-1 1v1l-3 3v-1l-2 2h-1v-2c1-1 2-2 2-3 1 0 1-1 1-2h1l-1-1v-3z" class="N"></path><path d="M520 436h1l1 1h0 1l-1 2s1 0 1 1l-1 1c-1 1-1 1-2 1h-1v-1c-1 0-2 1-2 2-1-1-1-2-1-3l-1-1 2-2h0 1 0c1 0 2 0 2-1z" class="F"></path><path d="M520 436h1v3c-1 1-1 2-1 3h-1v-1s0-1 1-2h-1l-1-2h0c1 0 2 0 2-1z" class="C"></path><path d="M520 436v2c0 1-1 1-1 1l-1-2h0c1 0 2 0 2-1z" class="F"></path><path d="M517 437h0 1l1 2h1c-1 1-1 2-1 2-1 0-2 1-2 2-1-1-1-2-1-3l-1-1 2-2z" class="D"></path><path d="M530 442l1 1-1 1v1h1 0l1-1h1l-2 2s1-1 2-1v3 1c-3 4-6 9-9 13-1 0-2 2-3 3 0-1-1-1-1-1l-2 2c0 1-1 1-2 2h-2c-1 1-1 1-2 1-1 1-3 2-4 3v1c-1 0-1 1-2 1l1-1v-1l2-2c1 0 1 0 2-1 0-2 3-6 4-7l1-1 3-3v-1l4-4h1c0-1 1-2 2-3v-1l-1-1 3-3v-1l1-1 1-1z" class="o"></path><path d="M523 454h1v1 2h-1-1c0-1 1-2 1-3z" class="Z"></path><path d="M518 461l1-1h1v1 1l-2-1z" class="f"></path><path d="M526 449c1 0 3 0 4 1h-1c0 1-1 2-1 3h-1-1v-3-1zm4-5v1h1 0l1-1h1l-2 2h0l-2 2h-2v-1l3-3z" class="N"></path><path d="M518 461l2 1h-1v2s-1 1-2 1h-1v-1c0-1 1-2 1-3h1z" class="p"></path><defs><linearGradient id="U" x1="536.099" y1="412.826" x2="550.774" y2="433.687" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#8a8a8c"></stop></linearGradient></defs><path fill="url(#U)" d="M556 400v1h1c-5 13-11 26-18 38-2 4-4 7-6 10v-1-3c-1 0-2 1-2 1l2-2h-1l-1 1h0-1v-1l1-1-1-1-1 1h-2v-2c1 0 0 0 1-1h0-2v-2h-1c0 1-1 1-2 2 0-1-1-1-1-1l1-2c0-1 1-2 1-3l2-2h0c1-1 1-2 1-3s1-2 0-4l1-1-1-1c0-1 0-1 1-2v-1l-1-1c0-1-1-1-1-1h-1v-2h-1l-1-1 1-1h1v-2c1 0 2 0 2 1h1c1 0 1-1 2-2 0 0 1 1 1 2l1-1c1 0 1-1 1-2l1-1h3c1 0 1 0 1 1 1 0 5-2 6-2 0 0 0-1 1-1 1-2 1-1 3-2h1v-2h1l1-1 1 1 1-1v-1l1-1h2z"></path><path d="M531 435h2v1l-1 1c-1-1-1-1-1-2z" class="S"></path><path d="M533 431l2 1c0 1 0 1-1 2-1-1-2-1-2-2l1-1zm11-13c-1 0-1 0-2-1h0 2v-1l2 1h0c-1 0-1 1-2 1z" class="H"></path><path d="M549 405v-2h1l1-1c0 1-1 1-1 2h0l2 1v1c-1 0-2 0-3-1z" class="U"></path><path d="M530 435v2 1 2h1v2h-1l-1 1h-2v-2c1 0 0 0 1-1h0c1-1 1-3 1-5h1z" class="g"></path><path d="M542 420l1 1v1c-1 0-1 1-2 2 0 0-1 2-2 2l-1 1c0 2-1 3-1 4-1 1-1 1-2 1h0l-2-1h0l2-3c1 0 1 0 1-1s0-2 1-3h1l3-3 1-1z" class="d"></path><path d="M538 427c-1 0-1 0-2-1h0c1-1 2-1 3-1v1l-1 1z" class="H"></path><path d="M529 428s1-1 2 0l-1 2h1 1 1v1h0l-1 1c-1 1-1 2-1 3h-1-1c0 2 0 4-1 5h-2v-2h-1c0 1-1 1-2 2 0-1-1-1-1-1l1-2c0-1 1-2 1-3l2-2h0c1-1 1-2 1-3h1l1-1z" class="Z"></path><path d="M527 429h1c0 2 0 3-1 4l-1-1c1-1 1-2 1-3z" class="R"></path><path d="M530 435c0-2-1-4 0-5h1 1 1v1h0l-1 1c-1 1-1 2-1 3h-1z" class="o"></path><path d="M526 432h0l1 1c0 2-1 4-1 5h-1c0 1-1 1-2 2 0-1-1-1-1-1l1-2c0-1 1-2 1-3l2-2z" class="m"></path><path d="M548 405c0 2 1 3 1 4-2 0-2 0-2 1 0 2 0 2-1 3 0 1-1 3-2 3v1h-2 0c1 1 1 1 2 1l-2 2h0l-1 1-3 3h-1c-1 1-1 2-1 3s0 1-1 1l-2 3v-1h-1-1-1l1-2c-1-1-2 0-2 0l-1 1h-1c0-1 1-2 0-4l1-1-1-1c0-1 0-1 1-2v-1l-1-1c0-1-1-1-1-1h-1v-2h-1l-1-1 1-1h1v-2c1 0 2 0 2 1h1c1 0 1-1 2-2 0 0 1 1 1 2l1-1c1 0 1-1 1-2l1-1h3c1 0 1 0 1 1 1 0 5-2 6-2 0 0 0-1 1-1 1-2 1-1 3-2z" class="S"></path><path d="M535 411l1 1c1 1 0 2 0 3l-3 3h0c0-1 0-2 1-3v-3l1-1z" class="F"></path><path d="M525 412c1 0 2 0 2 1h1l-1 1c0 1 1 2 1 4l-1 1c0-1-1-1-1-1h-1v-2h-1l-1-1 1-1h1v-2z" class="H"></path><path d="M525 412c1 0 2 0 2 1l-1 1h1c-2 1-2 1-4 1l1-1h1v-2zm8-2l1-1c0 1 0 1 1 2l-1 1v3h-1-1-1c-1 0-2-1-3-2 1 0 1-1 2-2 0 0 1 1 1 2l1-1c1 0 1-1 1-2z" class="D"></path><path d="M533 410l1-1c0 1 0 1 1 2l-1 1h-1l-1 1h-1l1-1c1 0 1-1 1-2z" class="W"></path><path d="M528 421c1-1 0-3 1-4h1v2 3c1 0 2-1 3-1v-1h-1l-1-1c1 0 1-1 2-1 1 1 2 1 2 2l-3 3 2 2v-1h1 2c-1 1-1 2-1 3s0 1-1 1l-2 3v-1h-1-1-1l1-2c-1-1-2 0-2 0l-1 1h-1c0-1 1-2 0-4l1-1-1-1c0-1 0-1 1-2z" class="n"></path><path d="M529 428l-1-2c1 0 1-1 2-1 1 1 1 2 1 3h0c-1-1-2 0-2 0z" class="f"></path><path d="M531 428h1l1-1v-1h2v2l-2 3v-1h-1-1-1l1-2h0z" class="N"></path><path d="M548 405c0 2 1 3 1 4-2 0-2 0-2 1 0 2 0 2-1 3 0 1-1 3-2 3v1h-2 0c1 1 1 1 2 1l-2 2h0l-1 1-3 3h-1-2-1v1l-2-2 3-3 1-1c-1 0-1 0-1-1 1 0 2 1 3 1h0l-1-2s0-1 1-1c0-2 0-3 1-4v-1h0-2l-1 1-1-1c-1-1-1-1-1-2h3c1 0 1 0 1 1 1 0 5-2 6-2 0 0 0-1 1-1 1-2 1-1 3-2z" class="R"></path><path d="M541 421c-1-1-1-1-1-2h1l1 1h0l-1 1z" class="H"></path><path d="M548 405c0 2 1 3 1 4-2 0-2 0-2 1 0 2 0 2-1 3h0l-2 3h-3v-1-2c0-1 0-2-1-3v1c-1-1-2-1-3-2 1 0 1 0 1 1 1 0 5-2 6-2 0 0 0-1 1-1 1-2 1-1 3-2z" class="W"></path><path d="M269 623l1 1c-1 1-2 1-3 2 0 1-1 0 0 1l1 1h0 1 2v-1c1 0 2 0 3-1h2v1c1 0 2 0 3-1 2-1 3-1 5-2 0 1 0 1 1 1l2-2c1 1 3 1 5 2h0c0 1-1 1-2 2 0 0-2 0-2 1h1l-1 2v1l-1 1h-1c1 0 1 1 1 1h1c0 1-1 1-2 2h1l2 1 1 1c-1 0-2 0-3 1l-3 1h-2c-2 0-4 0-6 1l-1 1c-1 0-2 0-2 1h-1l-1 1h-2c0 1-1 1-2 1 0-1-1-1-2-1h0 0l1-1c-1-1-1-1-1-2h-2c0 2 0 2-1 3h-1-3-2c-2 0-3 0-5-1h-1-1-6c-1 0-1-1-1-1v-1c-2 0-1-1-2-2h-2l3-1c1 0 1-1 2-2h-1c-1-1-3 0-5 0h-6v-1h0-2 0c2 0 3 0 4-2l2 1h1v-2h3v1c1 0 4-2 5-3s3-2 4-2l1 1c2 0 3-1 4-1 1-1 2-1 4-1h5c1-1 3-1 4-2h1c1 0 2 0 2-1z" class="N"></path><path d="M268 631l3-2v1c0 1 0 1-1 2-1 0-1-1-2-1z" class="J"></path><path d="M242 640h2 7l-2 2h-6c-1 0-1-1-1-1v-1z" class="H"></path><path d="M287 623c1 1 3 1 5 2-3 1-6 3-9 3 1-1 1-2 2-3l2-2z" class="L"></path><path d="M288 628h1l-1 2-4 2h-1v-1c-1 0-1 0-2 1-1 0-1 0-2-1 3-2 6-2 9-3z" class="O"></path><path d="M248 635l2-2c1 2 2 1 4 2h2c-1 0-1 0-1 1h-1c-1 1-2 1-3 1-2 0-2 1-3 1h-2l-1-1 2-1 1-1h0z" class="K"></path><path d="M248 635l2-2c1 2 2 1 4 2h-1c-2 1-3 1-5 1v-1h0z" class="e"></path><path d="M258 631c2 0 3 0 5 1h0c-3 1-5 2-7 3h-2c-2-1-3 0-4-2 2-1 5-1 7-2 1 1 1 0 1 0z" class="Q"></path><path d="M261 637c1-1 2-1 3-1h0l1-1h2l5 1c-1 1-2 1-2 2h-3c-1 1-1 1-2 1s-3 0-4 1h-3v-1c1-1 1-1 3-1v-1z" class="L"></path><path d="M261 637c1 1 2 1 4 1h1 0s0-1 1-1h1c1 0 1 0 2 1h-3c-1 1-1 1-2 1s-3 0-4 1h-3v-1c1-1 1-1 3-1v-1z" class="o"></path><path d="M288 630v1l-1 1h-1c1 0 1 1 1 1h1c0 1-1 1-2 2h1l2 1 1 1c-1 0-2 0-3 1l-2-1h-3-1l-1-1h-3c-1 1-2 0-3 1h0c-1 0-2 0-2-1l-5-1 1-1h2 3c2 0 3-1 5-1h4 0c1-1 1-1 2-1l4-2z" class="R"></path><path d="M282 637v-1c2-1 2-1 3 0v1h-3z" class="F"></path><path d="M288 630v1l-1 1h-1c1 0 1 1 1 1-1 1-2 1-4 1h-1c-1-1-1-1-2 0l-2-1h4 0c1-1 1-1 2-1l4-2z" class="S"></path><path d="M272 636c0 1 1 1 2 1h0c1-1 2 0 3-1h3l1 1h1 3l2 1-3 1h-2c-2 0-4 0-6 1l-1 1c-1 0-2 0-2 1h-1l-1 1h-2c0 1-1 1-2 1 0-1-1-1-2-1h0 0l1-1c-1-1-1-1-1-2h-2c0 2 0 2-1 3h-1-3-2c-2 0-3 0-5-1h-1-1l2-2 3-3h1c-1 2-3 3-3 4l4-1c0 1 0 1 1 1h0 2c1 0 2-1 2-1 1-1 3-1 4-1s1 0 2-1h3c0-1 1-1 2-2z" class="D"></path><path d="M285 637l2 1-3 1h-2c-2 0-4 0-6 1l-1 1c-1 0-2 0-2 1h-1l-1 1h-2c0 1-1 1-2 1 0-1-1-1-2-1h0 0l1-1c2-1 4-2 7-3 1 0 3 0 4-1 1 0 3 0 4-1h1 3z" class="N"></path><path d="M269 623l1 1c-1 1-2 1-3 2 0 1-1 0 0 1l1 1h0 1 2v-1c1 0 2 0 3-1h2v1c1 0 2 0 3-1 2-1 3-1 5-2 0 1 0 1 1 1-1 1-1 2-2 3h0l-3 1v1c-1 0-1-1-2 0h-1s0 1-1 1h-2-2l-1-1h0v-1l-3 2h0c-1 0-1 1-2 1-2 0-3-1-4-1-1-1-2-1-3-1l-1 1s0 1-1 0c-2 1-5 1-7 2l-2 2-4-1 1 1c0 1-1 2-2 2h-2c1 0 1-1 2-2h-1c-1-1-3 0-5 0h-6v-1h0-2 0c2 0 3 0 4-2l2 1h1v-2h3v1c1 0 4-2 5-3s3-2 4-2l1 1c2 0 3-1 4-1 1-1 2-1 4-1h5c1-1 3-1 4-2h1c1 0 2 0 2-1z" class="k"></path><path d="M262 631l9-3v1l-3 2h0c-1 0-1 1-2 1-2 0-3-1-4-1z" class="W"></path><path d="M244 634h-1c2-2 7-3 10-3h1 1v-1h1c0 1 1 1 1 1-2 1-5 1-7 2l-2 2-4-1z" class="G"></path><path d="M269 623l1 1c-1 1-2 1-3 2 0 1-1 0 0 1-2 1-4 1-6 1h-4l5-2c1-1 3-1 4-2h1c1 0 2 0 2-1z" class="l"></path><path d="M284 624c0 1 0 1 1 1-1 1-1 2-2 3h0l-3 1h-2-2l-1 1v-1h0l-1-1 2-2v1c1 0 2 0 3-1 2-1 3-1 5-2z" class="O"></path><path d="M284 624c0 1 0 1 1 1-1 1-1 2-2 3h0l-1-1c-1 1-2 1-3 1v-2c2-1 3-1 5-2z" class="e"></path><path d="M257 626h5l-5 2c-1 0-2 0-3 1-3 0-6 1-8 2h-1c-1 1-3 1-3 1l-1 2s-3 0-3-1c-1 0-1 1-2 1l-1-1h1v-2h3v1c1 0 4-2 5-3s3-2 4-2l1 1c2 0 3-1 4-1 1-1 2-1 4-1z" class="K"></path><path d="M257 626h5l-5 2c-1 0-2 0-3 1-2 0-4-1-6 0-1 1-3 1-4 0h0c1-1 3-2 4-2l1 1c2 0 3-1 4-1 1-1 2-1 4-1z" class="Q"></path><path d="M247 612c1 0 2 1 3 0h0v2l-1 1h2 2l2 1h1v-1c2 0 4-1 6-1h2v1l1 1 3-1c-1 0-1 1-2 1-2 1-3 3-6 4v1c1 1 1 0 2 0h1l2-1v1c-1 1-2 2-3 2v1c-1 1-2 1-3 1s-2 0-2 1c-2 0-3 0-4 1-1 0-2 1-4 1l-1-1c-1 0-3 1-4 2s-4 3-5 3v-1h-3v2h-1l-2-1c-1 2-2 2-4 2h0c-2 2-4 1-6 1h-2-5c0-1-1-1-1-1h-5l-2 1c-1 0-1-1-2-1s-1 1-2 1v-1h1l-1-1c0-1 1-2 1-2h2l1-1h-3l-1-2h2v-1c1 0 1 0 2-1s2-1 4-2l2-2v-1h4l4-1c2 0 4-1 7-2l3-4c1 0 2 0 4 1v1c2 1 4 0 7-1h0 1c0-2 1-2 3-3z" class="p"></path><path d="M212 630c-1 1-2 1-3 1v-1c1-1 2-1 3-2 1 1 2 1 3 1h-1l-1 1h-1z" class="e"></path><path d="M214 622v-1c1 1 2 1 3 1s1 0 2 1l-6 3c-2 1-4 2-5 4h-3l-1-2h2v-1c1 0 1 0 2-1s2-1 4-2l2-2z" class="T"></path><path d="M206 628h1 0c2-2 3-2 6-2-2 1-4 2-5 4h-3l-1-2h2z" class="P"></path><path d="M224 630h1c0 1 0 2 1 2v1c1 0 1-1 3-1h4c-1 2-2 2-4 2h0c-2 2-4 1-6 1h-2-5c0-1-1-1-1-1h-5l-2 1c-1 0-1-1-2-1 3-2 8-2 11-2 1-1 3-1 4-1 1-1 2-1 3-1z" class="Q"></path><path d="M217 632c1-1 3-1 4-1 1-1 2-1 3-1 0 2 0 2-1 3-2-1-3-1-4 0h0-2v-1z" class="i"></path><path d="M226 633c1 0 1-1 3-1h4c-1 2-2 2-4 2h0c-2 2-4 1-6 1h-2-5c1-1 1-1 2-1s1 0 2-1c0 1 1 1 1 1 2-1 3-1 5-1z" class="M"></path><path d="M221 623c1 1 3 0 4 0l1 2h-2l1 2h1c2 0 3 0 5-1 1 0 3 1 4 0 0 1-1 2-1 2h-3v1c-1 0-3 0-4-1 0 0 0-1-1 0s-3 1-4 1c-2 0-4 1-6 1-1 1-1 1-2 1s-1-1-2-1h0 1l1-1h1c-1 0-2 0-3-1l3-2c1 0 2-1 3-2 1 0 2 0 3-1z" class="L"></path><path d="M224 625l1 2c-2 0-4 1-7 1-1 0-2 1-3 1s-2 0-3-1l3-2c0 1 0 1 1 1 2 0 5-1 8-2z" class="Q"></path><path d="M221 623c1 1 3 0 4 0l1 2h-2c-3 1-6 2-8 2-1 0-1 0-1-1 1 0 2-1 3-2 1 0 2 0 3-1z" class="L"></path><path d="M237 626c1-1 2-1 4-1h1 0l1 1h-1v1c2 0 1-1 2-1 1-1 3-1 4-1-1 1-2 2-4 3l-5 3h-3v2h-1l-2-1h-4c-2 0-2 1-3 1v-1c-1 0-1-1-1-2h5 0l1-1v-1h3s1-1 1-2h1 1z" class="e"></path><path d="M225 630h5l1 1h0-4l-1 1c-1 0-1-1-1-2z" class="T"></path><path d="M235 626h1 1l1 1h2c-1 2-4 2-6 4h-2 0-1l-1-1h0l1-1v-1h3s1-1 1-2z" class="l"></path><path d="M235 620h1c1 0 2 0 3-1 1 0 2 0 3 1-1 1-2 1-3 2h4 0c2 0 4 1 6 1v1c0 1 0 1-1 1s-3 0-4 1c-1 0 0 1-2 1v-1h1l-1-1h0-1c-2 0-3 0-4 1h-1-1c-1 1-3 0-4 0-2 1-3 1-5 1h-1l-1-2h2l-1-2 3-1h2c1-1 2-1 2-1 1 0 2-1 3-1z" class="i"></path><path d="M235 620c1 0 2 0 3 1h1v1h-1-1c0 1 0 2-1 2h-1c-1 0-2 0-2 1h-1c-1 0-1 0-1 1-2 1-3 1-5 1h-1l-1-2h2l-1-2 3-1h2c1-1 2-1 2-1 1 0 2-1 3-1z" class="T"></path><path d="M227 625c1 0 3 0 4-1 1 0 2-1 3-1s2-1 3-1c0 1 0 2-1 2h-1c-1 0-2 0-2 1h-1c-1 0-1 0-1 1-2 1-3 1-5 1h-1l-1-2h2 1z" class="Q"></path><path d="M224 625h2 1c-1 1-1 1-1 2h-1l-1-2z" class="V"></path><path d="M247 612c1 0 2 1 3 0h0v2l-1 1h2 2l2 1h1v-1c2 0 4-1 6-1h2v1l1 1 3-1c-1 0-1 1-2 1-2 1-3 3-6 4v1c1 1 1 0 2 0h1l2-1v1c-1 1-2 2-3 2v1c-1 1-2 1-3 1s-2 0-2 1c-2 0-3 0-4 1-1 0-2 1-4 1l-1-1c-1 0-3 1-4 2s-4 3-5 3v-1l5-3c2-1 3-2 4-3 1 0 1 0 1-1v-1c-2 0-4-1-6-1h0-4c1-1 2-1 3-2-1-1-2-1-3-1-1 1-2 1-3 1h-1c-1 0-2 1-3 1 0 0-1 0-2 1h-2l-3 1c-1 0-3 1-4 0h-2c-1-1-1-1-2-1s-2 0-3-1h4l4-1c2 0 4-1 7-2l3-4c1 0 2 0 4 1v1c2 1 4 0 7-1h0 1c0-2 1-2 3-3z" class="Q"></path><path d="M243 622l1-1c1 0 5 1 7 1h1c-1 1-2 1-3 1-2 0-4-1-6-1z" class="o"></path><path d="M236 616c2 1 4 0 7-1h0 1c-1 1-2 2-2 3-3-1-4 1-7-1 1 0 1 0 1-1z" class="V"></path><path d="M232 614c1 0 2 0 4 1v1c0 1 0 1-1 1h-1c-1 0-2 1-3 2h0-2v-1l3-4z" class="T"></path><path d="M264 614v1l1 1 3-1c-1 0-1 1-2 1-2 1-3 3-6 4v-1c1-1 1-1 1-2h-2c-1 0-2 1-3 2 0 0-1 1-2 1s-2 0-2-1c1-1 3-1 4-1v-1l-1-1h1v-1c2 0 4-1 6-1h2z" class="L"></path><path d="M264 614v1l1 1h-9v-1c2 0 4-1 6-1h2z" class="B"></path><path d="M247 612c1 0 2 1 3 0h0v2l-1 1h2 2c1 1 1 1 1 2-2 1-6 1-9 0-1 0-2 1-3 1 0-1 1-2 2-3h-1 1c0-2 1-2 3-3z" class="j"></path><path d="M247 612c1 0 2 1 3 0h0v2l-1 1h2-7-1 1c0-2 1-2 3-3z" class="N"></path><path d="M251 622c1 0 1-1 2-1 1 1 1 1 2 1 1-1 1-2 2-3h1c0 1-2 2-2 3h1v1h5v1c-1 1-2 1-3 1s-2 0-2 1c-2 0-3 0-4 1-1 0-2 1-4 1l-1-1c-1 0-3 1-4 2s-4 3-5 3v-1l5-3c2-1 3-2 4-3 1 0 1 0 1-1v-1c1 0 2 0 3-1h-1z" class="l"></path><path d="M257 623h5v1c-1 1-2 1-3 1s-2 0-2 1c-2 0-3 0-4 1-1 0-2 1-4 1l-1-1c3-2 6-4 9-4z" class="h"></path><path d="M314 606c1 0 3-1 4 0 3 0 5 2 8 2l4 1h1c0 1 0 2-1 2v1 1h0-5l1 1-1 1-4 1v2h3v2c-1-1-3-1-4-1v1l-2 2-3 1c-1-1-2-1-3 0h-1c-1 1-3 0-4 1-2 1-4 1-6 0v-1h1l-1-1h2l-1-1c-2 2-5 3-7 3h-1c-1 0-2 1-2 1h0c-2-1-4-1-5-2l-2 2c-1 0-1 0-1-1-2 1-3 1-5 2-1 1-2 1-3 1v-1h-2c-1 1-2 1-3 1v1h-2-1 0l-1-1c-1-1 0 0 0-1 1-1 2-1 3-2l-1-1c0 1-1 1-2 1h-1c-1 1-3 1-4 2h-5c0-1 1-1 2-1s2 0 3-1v-1c1 0 2-1 3-2v-1l-2 1h-1c-1 0-1 1-2 0v-1c3-1 4-3 6-4 1 0 1-1 2-1h1l41-9h4z" class="T"></path><path d="M296 613c1-1 1-1 2-1l7-1h1c-1 1-1 1-2 1l-2 1h-1-3l-1 1v1h-1l-2-1 2-1h0z" class="L"></path><path d="M265 620h0s1 0 1-1c2 0 3-2 5-2 3-1 5-2 8-2 1-1 2-1 3-1h1 0c0-1 1-1 1-1 3 0 5-1 7-1l1 1v1c-2 0-5 0-7 1h-2 0c-2 1-4 0-6 1-4 1-9 3-12 5v-1z" class="i"></path><path d="M296 613h0l-2 1-5 4-3 3h1l1 1h1v1h-2l-2 2c-1 0-1 0-1-1v-1-2l-1-1h-2 0c2-1 3-1 5-2v-1h-2l1-1h4s2-1 3-2c1 0 2-1 4-1z" class="Q"></path><path d="M286 621h1l1 1h1v1h-2l-2 2c-1 0-1 0-1-1 1-1 1-2 2-3z" class="T"></path><path d="M279 621c1-1 1-1 2-1h0 2l1 1v2 1c-2 1-3 1-5 2-1 1-2 1-3 1v-1h-2c-1 1-2 1-3 1v1h-2-1 0l-1-1c-1-1 0 0 0-1 1-1 2-1 3-2l-1-1h1l1-1c1-1 2-1 3-1h0c-1 1-1 2-1 3h5c0-1 0-2 1-3z" class="P"></path><path d="M279 621c1-1 1-1 2-1h0 2l1 1v2h-1c-1-1-2-1-3 0-1-1 0-1-1-2z" class="V"></path><path d="M283 623h1v1c-2 1-3 1-5 2-1 1-2 1-3 1v-1h-2c-1 1-2 1-3 1v1h-2-1 0l1-1 1-1c1 0 1-1 2-1h3l1 1s1 0 1-1h1c3 0 3-1 5-2z" class="Z"></path><path d="M314 606c1 0 3-1 4 0 3 0 5 2 8 2l4 1h1c0 1 0 2-1 2v1 1h0-5l1 1-1 1-4 1v2h3v2c-1-1-3-1-4-1v1l-2 2-3 1c-1-1-2-1-3 0h-1c-1 1-3 0-4 1-2 1-4 1-6 0v-1h1l-1-1h2l-1-1c-2 2-5 3-7 3h-1c-1 0-2 1-2 1h0c-2-1-4-1-5-2h2v-1h-1l-1-1h-1l3-3 5-4 2 1h1v-1h0 1 1 1v2l1 1 2-2 1-1h1s0-1 1-1c2 0 3-1 4-1s1 0 2-1c2 0 4 0 6-1 0-1 1-1 1-2l-2-2h-3z" class="i"></path><path d="M330 609h1c0 1 0 2-1 2v1 1h0-5v-1c1 0 4-1 5-2v-1z" class="L"></path><path d="M322 610l1 1v1h2v1h5-5-5c1-1 2-2 2-3zm-33 13h0l1-1h1c2 0 3 0 5 1-1 0-2 0-2 1-1 0-2 1-2 1h0c-2-1-4-1-5-2h2z" class="P"></path><path d="M320 613h5l1 1-1 1-4 1c-1 0-2 1-3 1-1-1-1-1-1-2l3-2z" class="T"></path><path d="M297 614h0 1 1 1v2l-3 1c0 1-1 0-2 0v1c-1 1-3 1-5 2h-1v-2l5-4 2 1h1v-1z" class="h"></path><path d="M296 623c0-1 1-1 1-2 2 0 1-1 3-2l1 1h1c2-1 4-3 6-3l6-3h0 3v-1-1l2-2c1 0 2-1 3 0 0 1-1 2-2 3l-3 2c0 1 0 1 1 2h-2c-2 1-5 2-7 2l-6 3-1-1c-2 2-5 3-7 3h-1c0-1 1-1 2-1z" class="o"></path><path d="M302 621c3-2 7-4 11-5 1-1 2-1 4-1h0c0 1 0 1 1 2h-2c-2 1-5 2-7 2l-6 3-1-1z" class="i"></path><path d="M318 617c1 0 2-1 3-1v2h3v2c-1-1-3-1-4-1v1l-2 2-3 1c-1-1-2-1-3 0h-1c-1 1-3 0-4 1-2 1-4 1-6 0v-1h1l-1-1h2l6-3c2 0 5-1 7-2h2z" class="V"></path><path d="M318 617c1 0 2-1 3-1v2h3v2c-1-1-3-1-4-1h-7c-4 1-8 3-11 4l-1-1h2l6-3c2 0 5-1 7-2h2z" class="M"></path><path d="M395 570h2c2-1 5 0 8 0l-2-1s-1 0-1-1c-1 1-1 1-1 2-1 0-2 0-2-1h-2 0c-1 0-3 0-3-1s1-2 1-3 0-1-1-2v-4h-1v1 1h0c1 1 1 1 0 2 0-2-1-4-1-6 1-2 1-6 1-9v-4-1-1c1-1 1-3 2-3 0-1 1-1 2-2h-2 0-1c-1 1 0 1-1 2v-1-1c1-1 2-1 2-2s0-1 1-1v-3c2-1 0-7 1-9 1-1 0-2 1-4l1 1v5h2 0v1c1 1 1 1 2 1 0-1 1-1 1-1h1c0-1 1-1 2-1 0 1 0 1 1 1l1 1-1 1c1 0 2-1 3-1l1-1h2 0l2-1 1 1v4 4c0 1 0 3-1 4v1h0 1l-1 1v2c0 3 0 5 1 7-1 5-4 9-6 13-1 1 0 3 0 4v3c-1 1-1 3-1 5l-2-2h-1-2c-1-1-2-1-3 0h-2c-1-1-3-1-5-1z" class="Y"></path><path d="M394 544v1h1v2l-1 1h-1l1-4z" class="B"></path><path d="M395 547l1 1-1 1c1 1 1 2 1 2h-3v-3h1l1-1z" class="G"></path><path d="M393 551h3c1 2 0 4 0 5l-1 1h-1l-1 1c-1-2 0-6 0-7z" class="B"></path><path d="M398 527v-2h1v6c1 2 2 4 2 6h-3 0c-1 0-1 0-2-1 0-1 1-2 1-3 0-2 1-4 1-6z" class="i"></path><path d="M398 518l1 1v5h2 0v1c1 1 1 1 2 1 0-1 1-1 1-1h1c0-1 1-1 2-1 0 1 0 1 1 1l1 1-1 1c-2 1-4 2-7 2h-1c0 1 0 1-1 2v-6h-1v2c-1-2-1-7 0-9z" class="G"></path><path d="M408 525l1 1-1 1c-2 1-4 2-7 2l1-2c1 0 2 0 2-1 2 0 3 0 4-1h0z" class="B"></path><path d="M398 538c2 0 3 1 4 3v1h1c0 2 0 3 1 4l1 1v1h-4-3-2l-1-1v-2h-1v-1l1-1v-2h0v-1c1-1 2-2 3-2z" class="W"></path><path d="M395 541l1 1h2c-1 1-1 1-2 1 1 1 1 1 1 2v1s1 1 1 2h-2l-1-1v-2h-1v-1l1-1v-2z" class="F"></path><path d="M398 538c2 0 3 1 4 3v1h-6l-1-1h0v-1c1-1 2-2 3-2z" class="T"></path><path d="M398 548h3 4v1 1h-1l-2 2h0v4h-1v2c-1 0-2 1-4 0h0c-1 0-2 0-2 1l-2-1 1-1h1l1-1c0-1 1-3 0-5 0 0 0-1-1-2l1-1h2z" class="D"></path><path d="M400 551c1-1 3-1 4-1l-2 2h0v4h-1-1l-1-1 1-4z" class="G"></path><path d="M400 551c1-1 3-1 4-1l-2 2c-2 0-1 1-2 2v-3z" class="Y"></path><path d="M398 548h3 4v1c-1 1-5 1-7 1h-2l-1-1 1-1h2z" class="K"></path><path d="M395 549l1 1h2c-1 2-2 3-1 5 0 1 1 1 2 2v-1-1h0l1 1h1v2c-1 0-2 1-4 0h0c-1 0-2 0-2 1l-2-1 1-1h1l1-1c0-1 1-3 0-5 0 0 0-1-1-2z" class="W"></path><path d="M402 556v-4l1 4h2v7h-2v1h2v1l-2 2h0-1-1l-1 2c-1 0-2-1-2-1-1-1-1-3-1-4v-1h-1c-1-1-1-3-1-4h0c0-1 1-1 2-1h0c2 1 3 0 4 0v-2h1z" class="G"></path><path d="M402 556v-4l1 4h2v7h-2v1h2-3v1h-1-1c0-1 0 0-1 0-1-1-1-1-1-2h1 0l1-1c0-2 1-3 3-3v-1h-2v-2h1z" class="B"></path><path d="M401 556h1l2 1v1h-1-2v-2zm-2 7l1-1c0-2 1-3 3-3v4h-4z" class="C"></path><path d="M414 525l2-1 1 1v4 4c0 1 0 3-1 4v1h0 1l-1 1v2s-1 0-2 1c0-1-1-1-1-1-1 0-1 1-2 1-1 1-2 0-4 1-1 0-2 0-4-1h0-1v-1c-1-2-2-3-4-3v-1h3c0-2-1-4-2-6 1-1 1-1 1-2h1c3 0 5-1 7-2 1 0 2-1 3-1l1-1h2 0z" class="q"></path><path d="M403 531h2 3 0v1h-5v-1z" class="Y"></path><path d="M413 533c0-1-1-2-2-2l-1 1v-1c0-1 2-2 3-2h3 1v4l-2-1c-1 1-1 1-2 1z" class="E"></path><path d="M417 533c0 1 0 3-1 4v1h0 1l-1 1v2s-1 0-2 1c0-1-1-1-1-1-1 0-1 1-2 1-1 1-2 0-4 1-1 0-2 0-4-1h0-1v-1c-1-2-2-3-4-3v-1h3 0 5c1-1 2-1 3-2h0 1c1 0 2-1 3-2 1 0 1 0 2-1l2 1z" class="b"></path><path d="M417 533c0 1 0 3-1 4v1h0 1l-1 1v-1c-1 1-2 1-3 2l-1-1c-1 1-2 1-3 2h-1c-1-1 0-1-1-2v-2h-1c1-1 2-1 3-2h0 1c1 0 2-1 3-2 1 0 1 0 2-1l2 1z" class="C"></path><path d="M417 533c0 1 0 3-1 4v1h0 0c-1 0-1-1-1-1-1 0-1 0-2-1 0-1 1-2 2-4l2 1z" class="R"></path><path d="M403 542h0c2 1 3 1 4 1 2-1 3 0 4-1 1 0 1-1 2-1 0 0 1 0 1 1 1-1 2-1 2-1 0 3 0 5 1 7-1 5-4 9-6 13-1 1 0 3 0 4v3c-1 1-1 3-1 5l-2-2h-1c0-1-1-1-1-2v-1c0-2 0-2-1-4h-2v-1h2v-7h-2l-1-4h0l2-2h1v-1-1-1l-1-1c-1-1-1-2-1-4z" class="r"></path><path d="M159 178c6-5 13-8 21-8s15 4 21 10c2 1 3 4 5 6l-1 1-2-2h-1v2c-1 1-1 1-2 1l-1-1h0l-1-1v1l-1 1c-1 1-2 1-3 1 0 0 0 1-1 1 0 1-1 2-1 3h-1c-1 0-1 0-2 1h-1l-1-1c-1 1-1 2-1 3v2h1l1-1h1l1 1c-1 0-1 1-2 1l-1 1s0 1-1 2c0 1-1 1-2 2h-1v1h-2v1s0 1-1 2c-1 0-1-1-2-1v-1h2v-1l-2-1h-2v-1h0c0-1 0-2-1-3h0c-1-1-1-2-1-3l-1-1c-1-1-2-1-3-2l1-1v1c1-1 1-1 1-2l-1-1-1 1-1-1v-1l-1-1-1 1h-1l-2-2h0-1l-1-1h0-2-1c-2 0-4-2-5-4l5-5z" class="s"></path><path d="M191 177c1 0 1 0 2 1v1c-1 0-1 0-2-1v-1zm4 7h1v-2l1-1c1 1 1 3 2 4-1 0-1 0-2 1h-2v-2zm-11-8c1 1 2 3 3 4 1 0 1 1 1 1 0 1 0 2-1 3-2 0-2 0-3-1s-1-2-1-3l1-1c-1-1 0-2 0-3z" class="j"></path><path d="M158 182h1s1 0 1-1c1 0 0 0 0-1h1v1h0c-1 1-1 2-1 3v1l3-3h1c1 0 2-1 3 0h0 2 1v2l1 1s-1 0-1 1l-1 1h-1c-1-1-1-2-1-3v-1h-1-3l-1 1c-1 1-1 1-2 1-1-1-1-1-2-3z" class="T"></path><path d="M154 183l5-5h0c-1 2-1 2-2 3s0 1-1 1l1 1v-1h1 0c1 2 1 2 2 3 1 0 1 0 2-1l1-1h3c0 1-1 1-2 2l-2 2h0-2-1c-2 0-4-2-5-4z" class="Q"></path><path d="M175 188c0 1 0 1 1 2l2-2h1c1-1 1-1 2-1 2 0 3 0 5-1h0 2c1-1 1-1 2 0h2c1-1 1-1 1-2s0-1-1-2h1l2 2v2h2c1-1 1-1 2-1v2l-1-1v1l-1 1c-1 1-2 1-3 1 0 0 0 1-1 1 0 1-1 2-1 3h-1c-1 0-1 0-2 1h-1l-1-1c-1-1-1-2-3-3-1 0-1-1-2-1l-1 2h-1l2 2-1 1h-2l-1-1c0-1-1-2-2-2l-2 1v-1c0-2 0-1 1-3z" class="h"></path><path d="M188 190h2v1h0-2 0v-1zm-8 1h-1c-1-1-1-1-1-2 1-1 1-1 2-1l1 3h-1z" class="V"></path><path d="M166 183h1v1c0 1 0 2 1 3h1l1-1c0-1 1-1 1-1 1 1 1 2 1 3h3c-1 2-1 1-1 3v1l2-1c1 0 2 1 2 2l1 1h2l1-1-2-2h1l1-2c1 0 1 1 2 1 2 1 2 2 3 3-1 1-1 2-1 3v2h1l1-1h1l1 1c-1 0-1 1-2 1l-1 1s0 1-1 2c0 1-1 1-2 2h-1v1h-2v1s0 1-1 2c-1 0-1-1-2-1v-1h2v-1l-2-1h-2v-1h0c0-1 0-2-1-3h0c-1-1-1-2-1-3l-1-1c-1-1-2-1-3-2l1-1v1c1-1 1-1 1-2l-1-1-1 1-1-1v-1l-1-1-1 1h-1l-2-2h0-1l-1-1 2-2c1-1 2-1 2-2z" class="i"></path><path d="M180 195l-1 1-1-1c-1 0-1-1-1-2h1l1 1 1 1z" class="O"></path><path d="M172 188h3c-1 2-1 1-1 3v1l1 1-1-1v1 1c-1-1 0-1-1-2 0-1-1-1-1-1v-3z" class="T"></path><path d="M166 183h1v1 2l-2 1c0 1 1 2 1 3l-2-2h0-1l-1-1 2-2c1-1 2-1 2-2z" class="P"></path><path d="M164 185h1v2s-1 0-1 1h-1l-1-1 2-2z" class="O"></path><path d="M181 191l1-2c1 0 1 1 2 1l-1 2c0 1 1 0 1 1 1 0 1 1 1 1 0 1 0 1-1 2v1h-1v-1c-1-1-1-1-3-1h0l-1-1h2l1-1-2-2h1z" class="L"></path><path d="M186 198h1l1-1h1l1 1c-1 0-1 1-2 1l-1 1s0 1-1 2c0 1-1 1-2 2h-1v1h-2v1s0 1-1 2c-1 0-1-1-2-1v-1h2v-1l-2-1h-2v-1h0c0-1 0-2-1-3h0c-1-1-1-2-1-3 1 1 1 1 2 1h1l3 2c1 0 1 0 2-1h1c1 0 1 0 1 1h0c1 0 1-1 2-2z" class="K"></path><path d="M178 204c1 0 2-1 3 0h2v1h-2v1s0 1-1 2c-1 0-1-1-2-1v-1h2v-1l-2-1z" class="M"></path><path d="M174 197c1 1 1 1 2 1h1l3 2c1 0 1 0 2-1v1c0 1 0 1 1 2-1 0-1 1-2 1h-2-1c0-1-2-3-3-3h0c-1-1-1-2-1-3z" class="e"></path><defs><linearGradient id="V" x1="445.385" y1="523.892" x2="509.445" y2="476.416" xlink:href="#B"><stop offset="0" stop-color="#717172"></stop><stop offset="1" stop-color="#c0bfbf"></stop></linearGradient></defs><path fill="url(#V)" d="M508 473v-1c1-1 3-2 4-3 1 0 1 0 2-1h2c1-1 2-1 2-2l2-2s1 0 1 1l-5 6c0 1-1 2-2 2-4 5-8 10-13 14-17 18-38 32-60 43-2 1-5 3-8 4-2 1-4 2-6 2 0-1 1-2 1-3-1-1 0-3 0-4-1-2-1-3-1-5l1 2h1v2l3-2h1c1-2 2-1 3-2l2-1h2l2-1 2-1h0 0l2-2c2-1 3-2 5-3 1-1 2-1 3-1s6-4 7-5h1c0-2 2-4 4-5 1 0 2-1 3-2s2-1 2-2h0c1 0 1 1 2 1 2-1 4-3 6-5 0 0 1-1 1-2 1 0 1-1 1-1v-2c1-1 2-3 3-4l2-3 2-1 1-1h-1l-2-2c1-2 2-4 4-5v-2l3-3h0 1v1l2-1v2c0 1-1 1-1 2h2v-1h2v1h4c1 0 2 0 3-1h0c1 0 1-1 2-1z"></path><path d="M497 483c2-1 3-2 6-3v1c-1 0-2 1-2 2-1 1-1 2-1 3-1 1-2 1-3 2l-1 1-1-1 2-2c1-1 1-2 2-2h1v-1h-3z" class="M"></path><path d="M481 494c2-1 3-2 5-2v1h-1c0 1-1 1-1 2-1 1-2 1-2 2l-3 1v-1s1-1 1-2c1 0 1-1 1-1z" class="c"></path><path d="M482 497c0-1 1-1 2-2 0-1 1-1 1-2 0 2-1 3-1 5l1 1c-1 1-2 2-4 3v1l-1-3c1-1 1-1 2-3h0z" class="o"></path><path d="M445 524c9-4 16-10 24-15l1 1c-4 3-9 5-13 8l-6 4c-1 0-2 2-4 2 0 0 0 1-1 2s-4 2-5 3v1c-2 1-5 3-8 4-2 1-4 2-6 2 0-1 1-2 1-3-1-1 0-3 0-4-1-2-1-3-1-5l1 2h1v2l3-2h1c1-2 2-1 3-2l2-1h2l2-1v3c1-1 2-1 3-1z" class="B"></path><path d="M432 526h1v2c-1 1-3 1-3 2h-1v-2l3-2z" class="U"></path><path d="M440 523l2-1v3l-3 1-6 2v-2c1-2 2-1 3-2l2-1h2z" class="D"></path><path d="M440 523l2-1v3l-3 1v-1h0c0-1 0-2 1-2z" class="d"></path><path d="M428 533c2-1 5-3 7-3 1-1 1-1 2-1 3-2 7-4 10-5 0 0 0 1-1 2s-4 2-5 3v1c-2 1-5 3-8 4-2 1-4 2-6 2 0-1 1-2 1-3z" class="R"></path><path d="M479 498l3-1h0c-1 2-1 2-2 3l1 3-8 5c-1 0-2 1-3 2l-1-1c-8 5-15 11-24 15-1 0-2 0-3 1v-3l2-1h0 0l2-2c2-1 3-2 5-3 1-1 2-1 3-1s6-4 7-5h1c0-2 2-4 4-5 1 0 2-1 3-2s2-1 2-2h0c1 0 1 1 2 1 2-1 4-3 6-5v1z" class="J"></path><path d="M451 516c1-1 2-1 3-1-2 2-4 4-7 5h-1l-1 4c-1 0-2 0-3 1v-3l2-1h0 0l2-2c2-1 3-2 5-3z" class="W"></path><path d="M446 520l-1 4c-1 0-2 0-3 1v-3l2-1v2l1-1v-1l1-1z" class="c"></path><path d="M479 497v1c-3 3-5 6-9 8 0 1-1 1-2 1l-6 3c0-2 2-4 4-5 1 0 2-1 3-2s2-1 2-2h0c1 0 1 1 2 1 2-1 4-3 6-5z" class="H"></path><path d="M508 473v-1c1-1 3-2 4-3 1 0 1 0 2-1h2c1-1 2-1 2-2l2-2s1 0 1 1l-5 6c0 1-1 2-2 2l-1-1c0 1-1 2-2 3s-6 5-7 5h-1c-3 1-4 2-6 3l-3 3c-2 2-4 5-7 5v-1c0-1 0-1 1-2h-4l2-3 2-1 1-1h-1l-2-2c1-2 2-4 4-5v-2l3-3h0 1v1l2-1v2c0 1-1 1-1 2h2v-1h2v1h4c1 0 2 0 3-1h0c1 0 1-1 2-1z" class="l"></path><path d="M508 473l1 1v1s-1 0-1 1c-2 1-2 2-4 2h0l-1-1h1s1-1 1-2l1-1h0c1 0 1-1 2-1z" class="K"></path><path d="M494 479c2-1 4-2 7-3h1c-1 1-1 2-2 2 0 1-2 2-2 2-2 0-2 0-4 1h0 0v-1-1z" class="f"></path><path d="M493 471h1v1l2-1v2c0 1-1 1-1 2h2l-1 1h-1 0v1c0 1-1 1-1 2h0v1 1h0 0c2-1 2-1 4-1-2 1-2 1-3 2 0 1-2 2-2 3h-3v-1h-2l1-1h-1l-2-2c1-2 2-4 4-5v-2l3-3h0z" class="k"></path><path d="M490 477h1v1 1h-1v-2z" class="N"></path><path d="M489 483l1-1c1 0 1-1 2-1h1c-1 1-1 2-2 2l-1 1h-2l1-1z" class="Z"></path><path d="M493 471h1v1l2-1v2c0 1-1 1-1 2h-2l-3 1v-2l3-3h0z" class="J"></path><path d="M493 471v1 3l-3 1v-2l3-3z" class="f"></path><path d="M393 576c2 0 3 1 5 1h2l1 2h3 4s0 1 1 2v1c1 2 1 3 0 5l-3 1c1 1 1 1 2 3v1 1 3c1 1 1 3 1 4v1h-2c-1 1-2 1-4 1 0 2 0 3 1 4h-6v1h5v5c-1 1-1 2-1 3-1 0-1 0-2 1l-2-1c-1 1-2 1-3 2 0 1-1 1-1 1-2 1-3 1-5 1-1 0-3-1-4-1-1-1-1-1-2-1h-1v-1h-1-1c-1 1-2 1-4 0l-1 1h-2l1-1c1-1 1-2 2-2h-2c-2-1-3-2-4-3v-5-1l2-1h5 0v-3c1 0 1 0 1-1 0-2 0-4 1-5v-1h-4 7l3-1c1 0 2 0 3-1v-2h0 0 1l1-1-2-2h0l2-2h-2v-1c2 0 2 0 2-1l1-1-2-1 2-1v-1h-1 2 2c0-1-1-2-2-3h1z" class="q"></path><path d="M390 579h2c-1 1 0 4 0 6h-2-2v-1c2 0 2 0 2-1l1-1-2-1 2-1v-1h-1z" class="C"></path><path d="M391 580v1 1l-2-1 2-1z" class="W"></path><path d="M393 576c2 0 3 1 5 1h2l1 2h-7c0-1-1-2-2-3h1z" class="F"></path><path d="M398 606c-1 0-1 0-1-1h1c0-1-1-2 0-3h5 0c0 2 0 3 1 4h-6z" class="B"></path><path d="M398 587h0 2c1 1 0 2 0 3 1 1 2 1 3 2v1 6 1c-1-1-2-1-4-1-1-1-1-2-1-3 2-2 2-2 4-2h0c0-1 0-2-1-2-1-1 0-1-1-1h-1c-1 0-2-1-2-1 0-1 0-2 1-3z" class="Y"></path><path d="M390 585h2v2c0 1 0 2 1 3l1 1 1 1v1 1h-7-1-5l3-1c1 0 2 0 3-1v-2h0 0 1l1-1-2-2h0l2-2z" class="U"></path><path d="M390 585h2v2h-2v2l-2-2h0l2-2z" class="G"></path><path d="M388 590c1 1 2 0 2 1 1 1 1 1 2 1v1l-4 1h-1-5l3-1c1 0 2 0 3-1v-2h0z" class="J"></path><path d="M388 594h7l-2 1 2 1c0 1 0 1-1 2v3h0 0-1-1-2-1 0l-2-1v-4-1-1h1z" class="I"></path><path d="M389 601v-1c-1-1 0-3 0-4h1v1c0 1 1 2 1 3l-1 1h-1 0z" class="q"></path><path d="M393 595l2 1c0 1 0 1-1 2v3h0 0-1-1-2l1-1c0-1-1-2-1-3v-1h1c1 0 1-1 2-1z" class="X"></path><path d="M402 580h2c1 2 2 5 2 8 1 1 1 1 2 3v1 1 3c1 1 1 3 1 4v1h-2c-1 1-2 1-4 1v-2-1-6-1c-1-1-2-1-3-2 0-1 1-2 0-3h-2 0l1-4 3-3z" class="C"></path><path d="M405 599h2l2 1v1h-2-1l-1-2z" class="I"></path><path d="M403 600v-1-6l1 6h1l1 2h1c-1 1-2 1-4 1v-2z" class="B"></path><path d="M402 580h2v1c0 1-1 2-1 3v2h0l-2-2h0c0-1 0-2 1-2v-1h0v-1z" class="R"></path><path d="M398 587l1-4v3h2v2c1 0 2 0 3 1l-2 2 1 1c-1-1-2-1-3-2 0-1 1-2 0-3h-2 0zm-16 7h5v1 1 4l2 1h0 1 2 1 1c-1 2-1 4 0 5v5h-1 0-2c-1 1-1 1-2 1 0 0-1 0-1 1-1 0-2-1-3-1s-2 0-3 1h0s0 1-1 1h0 2 0c1 0 2-1 3-1h2 3c3 0 5 0 7 2-1 1-2 1-3 2 0 1-1 1-1 1-2 1-3 1-5 1-1 0-3-1-4-1-1-1-1-1-2-1h-1v-1h-1-1c-1 1-2 1-4 0l-1 1h-2l1-1c1-1 1-2 2-2h-2c-2-1-3-2-4-3v-5-1l2-1h5 0v-3c1 0 1 0 1-1 0-2 0-4 1-5v-1h-4 7z" class="B"></path><path d="M390 601h2 0c-1 2-1 3-1 4v1h-1v-5z" class="F"></path><path d="M392 601h1 1c-1 2-1 4 0 5h-1c-1-1-1-3-1-5h0zm-16 13v-1h3v1c-1 0-1-1-2 0 0 0 0 1 1 2h-2l-1 1h-2l1-1c1-1 1-2 2-2z" class="C"></path><path d="M387 600l2 1h0v6h-2v-1-2-4z" class="F"></path><path d="M386 607s0-1 1-1v1h2 2c1 1 1 1 2 1 0 1 1 1 1 2-1 1-2 1-3 1l-1-1h-1c-1 1-2 2-3 1h0c1-1 1-1 1-3l-1-1z" class="C"></path><path d="M382 602h1 0 1v2h2 0 1v2c-1 0-1 1-1 1l1 1c0 2 0 2-1 3-2 0-4 1-5 2h0v-1-2l1-2h0v-1-3-2z" class="c"></path><g class="D"><path d="M384 604h2 0 1v2c-1 0-1 1-1 1h0-3c0-1 1-2 2-2l-1-1z"></path><path d="M386 607l1 1c0 2 0 2-1 3-2 0-4 1-5 2h0v-1-1l2-2v-1h3v-1z"></path></g><path d="M391 613c3 0 5 0 7 2-1 1-2 1-3 2 0 1-1 1-1 1-2 1-3 1-5 1-1 0-3-1-4-1-1-1-1-1-2-1h-1v-1h-1-1c-1 1-2 1-4 0h2c2-1 4-1 6-2 2 0 5 0 7-1z" class="e"></path><path d="M391 613c3 0 5 0 7 2-1 1-2 1-3 2-1 0-1-1-2-1-1-1-2-1-4 0-1 1-2 1-4 0 0 0-1-1-1-2 2 0 5 0 7-1z" class="i"></path><path d="M382 594h5v1 1 4 4h-1 0-2v-2h-1 0-1v2h-1c-1-1-1-1-1-2l-1-1v-1h-1c0-2 0-4 1-5v-1h-4 7z" class="B"></path><path d="M382 598h0l1 1 1-1c0-1 1-1 1-2l1 3v2h-2l-1 1h0-1v-1-3z" class="E"></path><path d="M386 599v-4l1 1v4 4h-1 0-2v-2h-1l1-1h2v-2z" class="R"></path><path d="M379 595h3v3 3 1 2h-1c-1-1-1-1-1-2l-1-1v-1h-1c0-2 0-4 1-5z" class="J"></path><path d="M379 600v-2h1l1 1 1 2v1 2h-1c-1-1-1-1-1-2l-1-1v-1z" class="p"></path><path d="M378 600h1v1l1 1c0 1 0 1 1 2h1v3 1h0l-1 2v2 1l-1 1h-1v-1h-3v1h-2c-2-1-3-2-4-3v-5-1l2-1h5 0v-3c1 0 1 0 1-1z" class="g"></path><path d="M378 607h0l1 2c-2 0-3-1-5 0h0l-1-1v-1h5z" class="M"></path><path d="M370 606h2v2l1 1h0l1 1c1 1 3 0 4 1s1 1 1 2h-3v1h-2c-2-1-3-2-4-3v-5z" class="R"></path><path d="M378 600h1v1l1 1c0 1 0 1 1 2h1v3 1h0l-1 2c-2 0-2 0-2-1l-1-2h0v-1c-2 0-3-1-5 0 0 0-1 0-1-1v-1h5 0v-3c1 0 1 0 1-1z" class="Z"></path><path d="M378 600h1v1 3h-2 0 0v-3c1 0 1 0 1-1z" class="g"></path><path d="M378 607h3l1 1-1 2c-2 0-2 0-2-1l-1-2z" class="K"></path><path d="M358 556c1-1 3-1 4 0h1 2 0 1v1h1l1-1v2h1c1 1 2 1 3 2h-1c-1 2 0 5 0 7-1 1 0 2 0 3v1l1 1h2 0v4h1c2 0 3 0 5-1v-1-2h0 1v2c1 0 1 0 2-1h-1v-1h3c0 1 0 1 1 1v-1c1 0 1 1 2 1s1 1 2 1c0 1 1 1 2 1 0 0 0 1 1 1h-1c1 1 2 2 2 3h-2-2 1v1l-2 1 2 1-1 1c0 1 0 1-2 1v1h2l-2 2h0l2 2-1 1h-1 0 0v2c-1 1-2 1-3 1l-3 1h-7 4v1c-1 1-1 3-1 5 0 1 0 1-1 1v3h0-5l-2 1v-3c0 1-1 2 0 3-1 0-1 1-1 2 1 1 1 1 1 2l-2 2c0-1 1-3 0-4h0c-1-3 0-8-1-11v10l-3-9c1-2 0-4 0-6l-1-3-1-3h-1v-1-1-1l-1-1-1 1h0c-1-1-2-2-2-3-1 0-1-1-1-1 1-1 1-1 2-3v-2-1c0-1 1-2 1-2v-1c1 0 2-1 3-1h4v-2h-11-3c-1 0-2 0-3-1h1c1-1 0-6 0-8v-1h2 0 1 3 2z" class="a"></path><path d="M371 587h-1v-7h1 0c1 1 0 3 1 4h1c0-2 1-3 1-4 0 1 1 3 0 4s-2 1-2 1c-1 0-1 1-1 2h0z" class="b"></path><path d="M371 579l-1-1v-3-1c0-1 0-2 1-3l1 1h2 0v4h-1-2c-1 1 0 2 0 3z" class="Y"></path><path d="M375 579c1 1 1 5 1 6v2h1c1 1 2 1 2 2h0v1h-3v-1h-1 0v1l-2 1-1-1h2c1-1 0-1 0-2-1 0-2-1-3-1h0c0-1 0-2 1-2 0 0 1 0 2-1s0-3 0-4l1-1z" class="f"></path><path d="M379 589l1-1h1l1 1-1 1h2 3c0 1 1 1 2 0v2c-1 1-2 1-3 1l-3 1h-7c-1-1-4 0-5 0v-3s1 0 2-1l1 1 2-1v-1h0 1v1h3v-1z" class="e"></path><path d="M381 590h2 3c0 1 1 1 2 0v2c-1 1-2 1-3 1h-1c1-1 1-1 1-2l-3-1h-1z" class="Z"></path><path d="M370 591s1 0 2-1l1 1v1l2 1c2-1 3 1 5-1 1 0 2 0 3 1h1 1l-3 1h-7c-1-1-4 0-5 0v-3z" class="M"></path><path d="M370 591s1 0 2-1l1 1v1l2 1h-3l-1-1c-1 0-1 0-1-1z" class="K"></path><path d="M370 594c1 0 4-1 5 0h4v1c-1 1-1 3-1 5 0 1 0 1-1 1v3h0-5l-2 1v-3-7l2-1h-2 0z" class="I"></path><path d="M373 598c1-1 1-1 0-2v-1h1l1 1c1 1 0 2 0 3h0c-1-1-1-1-2-1z" class="R"></path><path d="M373 598c1 0 1 0 2 1 0 1 1 2 1 3v1h1v-2 3h0-5l-2 1v-3h1c0 1 0 2 1 2h1v-6z" class="S"></path><path d="M375 579h1l1 1h1v-1h2c1 1 1 1 1 2v2h0l1 1h1 1 2 2v1h2l-2 2h0l2 2-1 1h-1 0 0c-1 1-2 1-2 0h-3-2l1-1-1-1h-1l-1 1h0c0-1-1-1-2-2h-1v-2c0-1 0-5-1-6h0z" class="l"></path><path d="M381 583h0l1 1h1l1 1c1 1 1 1 1 2h-1-2 0 0c0-1 1-1 1-2h-2v-2z" class="d"></path><path d="M382 587h0 2 1v1 2h-2-2l1-1v-2zm-7-8h1l1 1h1v-1h2v6h-4c0-1 0-5-1-6h0z" class="E"></path><path d="M384 584h2 2v1h2l-2 2h0l2 2-1 1h-1 0 0c-1 1-2 1-2 0h-3 2v-2-1c0-1 0-1-1-2l-1-1h1z" class="R"></path><path d="M388 587h-2v-2h1 1 2l-2 2z" class="d"></path><path d="M388 590h-2c0-1 0-2 1-3h1l2 2-1 1h-1z" class="X"></path><path d="M381 574c1 0 1 0 2-1h-1v-1h3c0 1 0 1 1 1v-1c1 0 1 1 2 1s1 1 2 1c0 1 1 1 2 1 0 0 0 1 1 1h-1c1 1 2 2 2 3h-2-2 1v1l-2 1 2 1-1 1c0 1 0 1-2 1h-2-2-1-1l-1-1h0v-2c0-1 0-1-1-2h-2v1h-1l-1-1h-1-4c0-1-1-2 0-3h2 1 1c2 0 3 0 5-1v-1-2h0 1v2z" class="Q"></path><path d="M391 576h1c1 1 2 2 2 3h-2-2 0-1-8v2c0-1 0-1-1-2h-2v1h-1l-1-1c3-1 7 0 10-1 1-1 1-1 1-2h3 1z" class="c"></path><path d="M391 576h1c1 1 2 2 2 3h-2-2 0c0-1 0-1 1-2l-1-1h1z" class="R"></path><path d="M381 574c1 0 1 0 2-1h-1v-1h3c0 1 0 1 1 1v-1c1 0 1 1 2 1s1 1 2 1c0 1 1 1 2 1 0 0 0 1 1 1h-1-1-1-3c-2 0-4 0-6-1v-1z" class="I"></path><path d="M381 575h1c2 0 3 0 4-1 1 0 1 0 2 1v1h3-1-3c-2 0-4 0-6-1z" class="G"></path><path d="M390 579h0 1v1l-2 1 2 1-1 1c0 1 0 1-2 1h-2-2-1-1l-1-1h0v-2-2h8 1z" class="I"></path><path d="M381 583c1-1 0-2 0-3h1c2 0 2 0 3 1l-1 3h-1-1l-1-1z" class="a"></path><path d="M385 581c1-1 1-1 1 0 1-1 2-1 3 0h0l2 1-1 1c0 1 0 1-2 1h-2-2l1-3z" class="E"></path><path d="M385 581c1-1 1-1 1 0v3h-2l1-3zm-27-25c1-1 3-1 4 0h1 2 0 1v1h1l1-1v2c-1 3-1 7-1 10v1 4 15 8 10l-3-9c1-2 0-4 0-6l-1-3-1-3h-1v-1-1-1l-1-1-1 1h0c-1-1-2-2-2-3-1 0-1-1-1-1 1-1 1-1 2-3v-2-1c0-1 1-2 1-2v-1c1 0 2-1 3-1h4v-2h-11-3c-1 0-2 0-3-1h1c1-1 0-6 0-8v-1h2 0 1 3 2z" class="W"></path><path d="M363 556h2 0 1v1c-1 3 0 6 0 8 0 1 0 1-1 1h0l-1-1h-2l1-9z" class="X"></path><path d="M365 556h1v1c-1 3 0 6 0 8 0 1 0 1-1 1h0l-1-1c0-3-1-5 1-9z" class="c"></path><path d="M360 570h1c1 2 0 7 0 10 1 2 1 3 1 5h-1v-1-1-1l-1-1-1 1h0c-1-1-2-2-2-3-1 0-1-1-1-1 1-1 1-1 2-3v-2-1c0-1 1-2 1-2h1z" class="d"></path><path d="M358 575c0 1 1 2 1 3s-1 2-1 3c1 0 1 0 1 1-1-1-2-2-2-3-1 0-1-1-1-1 1-1 1-1 2-3z" class="U"></path><path d="M360 570h1c0 2 0 7-1 8h0l-1-3 1-5z" class="g"></path><path d="M360 570l-1 5v3c0-1-1-2-1-3v-2-1c0-1 1-2 1-2h1z" class="D"></path><path d="M363 581l-1-12c1-1 1-1 2 0l1 1c0 1 0 1 1 2v-1l1-1v3 15 8 10l-3-9c1-2 0-4 0-6 1 0 1 0 1-1-1-3-1-6-2-9z" class="N"></path><path d="M365 590c1 1 1 3 2 4v-6 8 10l-3-9c1-2 0-4 0-6 1 0 1 0 1-1z" class="c"></path><path d="M363 581l-1-12c1-1 1-1 2 0l1 1c0 4-1 8 0 11l2 1v2c-2 0-3-2-4-3z" class="Z"></path><path d="M358 556c1-1 3-1 4 0h1l-1 9h2l1 1h0-10-3c-1 0-2 0-3-1h1c1-1 0-6 0-8v-1h2 0 1 3 2z" class="I"></path><path d="M358 556h1v8 1h-1c0-3-1-6 0-9z" class="o"></path><path d="M358 556c1-1 3-1 4 0h1l-1 9h-1v-1h0c-1 1-1 1-2 0v-8h-1 0z" class="Y"></path><path d="M358 556c1-1 3-1 4 0h1l-1 9h-1v-1-8h0-2-1 0z" class="p"></path><path d="M350 556h2 0 1 3-1l-1 1h1v7c1 1 2 0 3 1h1v-1c1 1 1 1 2 0h0v1h1 2l1 1h0-10-3c-1 0-2 0-3-1h1c1-1 0-6 0-8v-1z" class="f"></path><path d="M352 556h1v7c1 1 0 1 0 1h-1-1c0-3 0-6 1-8z" class="E"></path><path d="M299 243c1 2 1 4 1 6h-4v3l12-1h5l-1 1-1 1v6 15h-4v-3l-1-1v4h-2-3-1c-1 1-1 1-2 1h-1-1 0v1h6 0c-3 1-5 1-7 2-2 0-3 0-4 1-2 0-3 0-4 1l1 2h-1c-1 1-2 1-4 1-1 1-2 1-3 1v-1-1-1-1l-2 1v-1c1-1 2-1 3-1l-1-1c0-1 1-4 1-5h-3-1 0l1-1c1-1 2-4 2-5 0-2 0-8-1-9l-4 4-2 2-1-2h-1c-1-3-2-6-2-9l-1-1h4l1-1v-3c1 1 2 1 3 2v3c1-1 1-2 1-3h1 0c1 0 3 1 4 1h0c0-1 1-1 1-1l2-2c1-1 1-2 2-3h0c1 0 2 0 4-1h1 2c1-1 3-1 5-1z" class="a"></path><path d="M292 265h1c1 2 0 3 0 5v4-4l-1 1v2-8z" class="Z"></path><path d="M292 273v-2l1-1v4 1h3v1l-10 1h0c1-1 4-1 5-2l1-2z" class="M"></path><path d="M282 255l2-1h1s0 14-1 15v1c0 1 0 1-1 2l1 1c0 2-1 2 0 4h1-2l-1-4s-1 0-1-1h-1c1-2 2-4 2-6s-1-5 0-7h0c1-2 1-3 0-4z" class="C"></path><path d="M299 243c1 2 1 4 1 6h-4v3h-1-1l-1 1v5-1l-1 1v2-7h-2c-1-1-1-2-1-3s0-2-1-4l-1-1h0c1 0 2 0 4-1h1 2c1-1 3-1 5-1z" class="D"></path><path d="M291 244c0 1 0 2 1 2h1l-1 1c-1 0-2 0-3 1v2c0-1 0-2-1-4l-1-1h0c1 0 2 0 4-1z" class="B"></path><path d="M299 243c1 2 1 4 1 6h-4v3h-1-2-1c-1 0-1 1-2 0l2-2c1-1 1-2 2-2 0-1 1-1 2-2 1 0 1-1 1-1-1-1-2-1-3-1 1-1 3-1 5-1z" class="S"></path><path d="M293 252h-2l1-1c1-1 3-1 4-2v3h-1-2z" class="O"></path><path d="M273 248c1 1 2 1 3 2v3c1-1 1-2 1-3h1 0c1 0 3 1 4 1 0 1 0 1-1 3h-1c-2 2-4 6-7 7l-1 1h-1c-1-3-2-6-2-9l-1-1h4l1-1v-3z" class="f"></path><path d="M269 253h2 4 1v3h-1c0-1 0-1-1-1h-1l1 1v1 1 1l-1 1v1l-1 1h-1c-1-3-2-6-2-9z" class="Q"></path><path d="M282 255c1 1 1 2 0 4h0c-1 2 0 5 0 7s-1 4-2 6h1c0 1 1 1 1 1l1 4h2 1 0l10-1h6 0c-3 1-5 1-7 2-2 0-3 0-4 1-2 0-3 0-4 1l1 2h-1c-1 1-2 1-4 1-1 1-2 1-3 1v-1-1-1-1l-2 1v-1c1-1 2-1 3-1l-1-1c0-1 1-4 1-5h-3-1 0l1-1c1-1 2-4 2-5 0-2 0-8-1-9l3-3z" class="R"></path><path d="M287 280l1 2h-1c-1 1-2 1-4 1h0c-1-1 0-2 0-3h4z" class="B"></path><path d="M296 252l12-1h5l-1 1-1 1v6 15h-4v-3l-1-1v4h-2-3-1c-1 1-1 1-2 1h-1-1 0-3v-1-4c0-2 1-3 0-5h-1v-5-2l1-1v1-5l1-1h1 1z" class="a"></path><path d="M304 262v-3c0-1-2-5-2-6 1 1 2 2 3 4 1 1 1 2 1 3h-1 1 0v3l-1-1-1 1v6-7z" class="H"></path><path d="M305 257l1-1v-3l1-1v1c0 1 0 5 1 6h0l2 1v1h0-3l-1-1h0-1 1c0-1 0-2-1-3z" class="C"></path><path d="M297 261c0-3 1-5 0-7 1-1 1-1 1-2l1 1c0 2 0 4 1 6v8c-1-2 0-5-1-6h-2z" class="S"></path><path d="M292 260v-2l1-1v1l1 3 1 1c0 1 0 2-1 3 0 1 0 3-1 5 0-2 1-3 0-5h-1v-5z" class="N"></path><path d="M297 261h2c1 1 0 4 1 6l-1 2v3 2h1c-1 1-1 1-2 1h-1-1c1-1 1-4 1-5v-9z" class="k"></path><path d="M297 275c0-2 0-5 1-6h1v3 2h1c-1 1-1 1-2 1h-1z" class="l"></path><path d="M306 260l1 1v5 8-3l-1-1v4h-2v-5-6l1-1 1 1v-3z" class="J"></path><path d="M300 259c1 0 1 0 2 1 0 0 0 1-1 1 0 1 0 4-1 5l1 1h1v-1c0-2 1-3 2-4v7 5h-3-1-1v-2-3l1-2v-8z" class="B"></path><path d="M299 272c1-2 1-2 2-3h1v5h-1-1-1v-2z" class="r"></path><path d="M308 259h2 0 1v15h-4v-8-5h3 0v-1l-2-1z" class="Y"></path><path d="M317 119h1 0c1 3 1 7 1 10v1h2 0 2 0 4v1c0 2 1 3 1 5 0 1-1 1-1 1l-3 1c2 0 4 0 4 2 1 1 1 2 0 3l1 1c1 2 0 5 0 8 1 2 1 4 1 6 0 1 1 1 1 2h0c-3 1-8 1-12 0h-2l-17 1h-2-1c-1-1-1-1-1-2 1-1 1-3 2-4 0-3 0-6 1-9 0-1 1-3 0-4h0c0-1 1-2 1-2l2-2c-1 0-1-1-1-1h-1v-6c0-1 2-2 3-2 0-1 1-1 1-2 2 1 1 1 1 2v-4h1c0-2 0-3-1-5h1l11-1z" class="s"></path><path d="M304 127c2 1 1 1 1 2l3 1h-3c-1 2-1 5-2 7 7 1 14 0 20 0 1-2 0-3 0-4-1-1-2-2-2-3h2 0 4v1c0 2 1 3 1 5 0 1-1 1-1 1l-3 1c2 0 4 0 4 2 1 1 1 2 0 3l1 1c1 2 0 5 0 8 1 2 1 4 1 6 0 1 1 1 1 2h0c-3 1-8 1-12 0h5v-7-5c0-4 0-7-1-10-7-1-14 0-20 1-2 5-2 10-2 16-1 1-2 3-2 5h1v1h-2-1c-1-1-1-1-1-2 1-1 1-3 2-4 0-3 0-6 1-9 0-1 1-3 0-4h0c0-1 1-2 1-2l2-2c-1 0-1-1-1-1h-1v-6c0-1 2-2 3-2 0-1 1-1 1-2z" class="Q"></path><path d="M317 119h1 0c1 3 1 7 1 10v1h2 0c0 1 1 2 2 3 0 1 1 2 0 4-6 0-13 1-20 0 1-2 1-5 2-7h3l-3-1v-4h1c0-2 0-3-1-5h1l11-1z" class="j"></path><path d="M317 119h1 0c1 3 1 7 1 10-1-1-1-3-1-4h-1v1 2 1c-3 1-6 0-9 1l-3-1v-4h1c0-2 0-3-1-5h1l11-1z" class="k"></path><path d="M305 125l4 1h0-1l-1 1c1 1 2 1 3 1 2 0 6 0 7 1-3 1-6 0-9 1l-3-1v-4z" class="H"></path><path d="M306 120l11-1v4h-1-3l-1 2-1-1v-1h0-1c-1 1-2 1-3 1s-1 0-1 1c0-2 0-3-1-5h1z" class="S"></path><defs><linearGradient id="W" x1="353.446" y1="528.622" x2="352.776" y2="552.636" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#313232"></stop></linearGradient></defs><path fill="url(#W)" d="M364 528h2 2c0 1 1 1 2 1h2 1l1 1c-1 1-1 1-2 1-3 2-6 5-7 8v1c-1 2-1 5-2 7v4h-2v2c1 0 1 0 1 1 1 0 1 1 2 0l2 1h1l-2 1h-2-1c-1-1-3-1-4 0h-2-3-1 0-2-4l-2-1h0v-1h0c1-2 0-7 0-9-2-1-3 0-5 0h-1-12-5-20v-1c-3 0-6 0-8-1l1-1h0l1-1c1-2 2-3 4-3 0-1 1-2 1-2 1 0 1 1 3-1h0 1s0 1 1 1v1h1 18v-2h-1l1-2h2c1 0 1 1 2 1h0 1v-3h1 1v-2 1c1 0 2 0 3-1h7 6c3 0 7-1 10 0l2-1h5z"></path><path d="M353 535c1-1 1-2 2-3h1c1 1 1 2 0 3h0-1-2z" class="I"></path><path d="M359 553c-1-1-1-2-1-4 1 1 2 1 2 1 0 2-1 3 1 3 1 0 1 0 1 1h0-3v-1z" class="F"></path><path d="M355 547c0 2 1 3 1 5h0v-1c0-1 0-2 1-3v-1c0 2-1 5 0 6h2v1h-4v-7z" class="C"></path><path d="M353 535h2 1c0 2 0 3-1 5v3c0-1 0-2-1-3l-1-1v-4z" class="B"></path><path d="M364 528h2 2c0 1 1 1 2 1l-10 1c-1 0-2 0-3-1l2-1h5z" class="U"></path><path d="M334 535l1 2h3s0-1 1-2c-1 0-1-1-1-2v-1l1 1v2 1h0 0c1 1 1 3 0 4h1v1c-1 0 0 0-1-1h-4 0l-1-5z" class="B"></path><path d="M357 547l1-4c0-3 1-7 3-10h0l-3 16c0 2 0 3 1 4h-2c-1-1 0-4 0-6z" class="H"></path><path d="M361 551h0c0-1-1-3-1-4 1-4 2-7 3-10 0-2 0-3 1-4 1 1 0 5-1 6 0 2 0 4-1 5v2l1 1v4h-2z" class="F"></path><path d="M345 555c0-1 1-1 1-1v-5-11c0-1 0-3 1-4h0c1 4 1 8 1 13 0 1-1 4 0 5 0 1 0 1 1 1h1v1h-3 0l-2 1z" class="U"></path><path d="M350 553c0-6-1-13 1-19h0c1 2 0 4 0 6v14c1 0 1 0 2-1v-14l1 1c1 1 1 2 1 3v4 7h4 3 0c1 0 1 1 2 0l2 1h1l-2 1h-2-1c-1-1-3-1-4 0h-2-3-1 0-2-4l-2-1h0 1l2-1h0 3v-1z" class="c"></path><path d="M347 554h4 11 0c1 0 1 1 2 0l2 1h1l-2 1h-2-1c-1-1-3-1-4 0h-2-3-1 0-2-4l-2-1h0 1l2-1z" class="M"></path><path d="M329 531h1 1v-2 1c1 0 2 0 3-1h7 6c3 0 7-1 10 0 1 1 2 1 3 1-3 0-8 1-10 0v-1c-2 1-1 1-2 2h-3c-2-1-4-1-6 0h0c-1-1-3-1-5-1v5l1 5h0 4c1 1 0 1 1 1l1 1h-7v-2h-2-1-2v-1h-1c0-1-1-1-1-1l-1-1h-2v-2h-1l1-2h2c1 0 1 1 2 1h0 1v-3z" class="C"></path><path d="M328 534h1c1 0 1-1 1-1l1-1v1 3h-1l1 1h-1c0 1 0 1-1 2h-1c0-1-1-1-1-1l-1-1h-2v-2h-1l1-2h2c1 0 1 1 2 1h0z" class="B"></path><path d="M324 535c1 0 2 0 3 1h2 1l1 1h-1c0 1 0 1-1 2h-1c0-1-1-1-1-1l-1-1h-2v-2z" class="m"></path><path d="M341 542h1v-8h0c1 2 1 7 1 9l-22 2h-20v-1c-3 0-6 0-8-1l1-1h30v-1h3c2 0 3 0 4-1h1 2v2h7z" class="J"></path><path d="M331 540h1 2v2h-10 0v-1h3c2 0 3 0 4-1z" class="W"></path><path d="M294 542h30 0l5 1c-6 0-11 1-16 1h-12c-3 0-6 0-8-1l1-1z" class="Q"></path><path d="M303 535h0 1s0 1 1 1v1h1 18 2l1 1s1 0 1 1h1v1h2c-1 1-2 1-4 1h-3v1h-30 0l1-1c1-2 2-3 4-3 0-1 1-2 1-2 1 0 1 1 3-1z" class="S"></path><path d="M303 535h0v2 1h-4c0-1 1-2 1-2 1 0 1 1 3-1z" class="F"></path><path d="M320 538h2c1 1 0 1 0 2h-1-1-1c0-1 0-1 1-2z" class="l"></path><path d="M316 538h4 0c-1 1-1 1-1 2h1l-1 1h-4c1-1 1-2 1-3z" class="o"></path><path d="M308 538h8c0 1 0 2-1 3h-4 0-3c-1-1 0-2 0-3z" class="M"></path><path d="M311 541l-1-1v-1c1-1 2-1 3-1v1h0c-1 0-1 1-2 2h0z" class="O"></path><path d="M306 537h18 2l1 1c-3 1-5-1-7 0h-4-8l-2 1v1h0-1v-3h1z" class="F"></path><defs><linearGradient id="X" x1="341.846" y1="460.639" x2="365.088" y2="463.46" xlink:href="#B"><stop offset="0" stop-color="#29282a"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#X)" d="M342 430h18 1l-2 1h0 0 2v1c-1 6 0 13 0 19v13 2c1 1 2 0 2 2h-2c-1 2 0 8 1 10 0 8 1 16 0 23v11c1 1 2 0 3 1h-3v-1h-1 0-3-3-8 0-3 0v-5-6h-1v-2l1-4 1-1v-1s0-1 1-2c1-2 2-4 2-6l3-21v-3-3h-2c-1-1-2-1-2-2 1-1 1-2 0-3v-1c1-2 1-3 1-5v-4-4h-1c-1-1 0-1 0-2v-1l-1-2v-2l3-1h-4-1l-1 1h-1v-2z"></path><path d="M358 503c0-2 0-6 1-9 1 1 1 1 1 2v2 6s-1 0-2-1z" class="U"></path><path d="M361 506v-12c1 2 1 5 1 7v11c1 1 2 0 3 1h-3v-1h-1c0-2-1-2-1-3 0 0 0-2 1-3z" class="S"></path><path d="M352 433c0-2 0-1 2-2l1 1v1l1-1c1 2 0 4 0 5l-1 1s-1 0-1 1v3h-2c0-1 0-2 1-3v-1l-1-1v-4z" class="U"></path><path d="M352 433c0-2 0-1 2-2l1 1v1c0 1 0 4-1 4 0 0-1 0-1 1l-1-1v-4z" class="Y"></path><path d="M354 439c0-1 1-1 1-1l1-1v6c1 2 1 5 1 7v6 1h0-1 0-2l-1 2s0 1-1 2h0-1v-3c0-1-1-2 0-4v-2-1c1-1 0-2 1-3 1-2 0-3 0-5v-1h2v-3z" class="K"></path><path d="M354 439c1 1 1 1 1 2v1l-1 1c1 1 1 2 1 3-2-1-2-1-3-3v-1h2v-3z" class="D"></path><path d="M356 443c1 2 1 5 1 7v6 1h0-1 0-2-1l-1-1 1-1h3v-12z" class="U"></path><path d="M352 443c1 2 1 2 3 3l-1 1h0c1 2 1 3 1 4-1 0-1 1-3 1v-1l1-2-1-1c1-2 0-3 0-5z" class="c"></path><path d="M351 454v-2-1c1-1 0-2 1-3l1 1-1 2v1c2 0 2-1 3-1v2h-1 0c-1 0-1 1-2 2h1l-1 1 1 1h1l-1 2s0 1-1 2h0-1v-3c0-1-1-2 0-4z" class="N"></path><path d="M352 456l1 1h1l-1 2s0 1-1 2h0v-5z" class="c"></path><defs><linearGradient id="Y" x1="344.515" y1="442.603" x2="353.26" y2="454.364" xlink:href="#B"><stop offset="0" stop-color="#b1afad"></stop><stop offset="1" stop-color="#e3e2e5"></stop></linearGradient></defs><path fill="url(#Y)" d="M349 431h0c1 1 2 1 3 2v4l1 1v1c-1 1-1 2-1 3v1c0 2 1 3 0 5-1 1 0 2-1 3v1 2c-1 2 0 3 0 4h-2c-1-1-2-1-2-2 1-1 1-2 0-3v-1c1-2 1-3 1-5v-4-4h-1c-1-1 0-1 0-2v-1l-1-2v-2l3-1z"></path><path d="M348 439v-2c0-1 0-2 1-3h0l1 3v1h-1c0 3 0 7-1 9v-4-4z" class="l"></path><path d="M349 431h0l1 2v4l-1-3h0c-1 1-1 2-1 3v2h-1c-1-1 0-1 0-2v-1l-1-2v-2l3-1z" class="g"></path><defs><linearGradient id="Z" x1="349.882" y1="434.851" x2="351.813" y2="441.866" xlink:href="#B"><stop offset="0" stop-color="#666766"></stop><stop offset="1" stop-color="#838283"></stop></linearGradient></defs><path fill="url(#Z)" d="M349 431c1 1 2 1 3 2v4l1 1v1c-1 1-1 2-1 3v1c0 2 1 3 0 5-1 1 0 2-1 3v1 2c-1-2-1-4-1-6v-10-1-4l-1-2z"></path><path d="M349 431c1 1 2 1 3 2v4c0-1 0-2-1-3l-1-1-1-2z" class="d"></path><path d="M354 457h2 0 1v11l1 18h-1-3-2l-1 1h-1l-1-2h-1l3-21v-3h1 0c1-1 1-2 1-2l1-2z" class="f"></path><path d="M354 457h2 0v3h-2l-1-1 1-2z" class="K"></path><path d="M356 457h1v11h-1-2v-7l1-1h1v-3z" class="J"></path><path d="M354 468v-7l1-1c1 3 0 5 0 8h-1z" class="C"></path><path d="M354 486l-1-4h-1v-6c0-2-1-4 0-6l1-2h1 2 1l1 18h-1-3z" class="p"></path><path d="M348 485h1l1 2h1l1-1h2 3l2 1v1l-1 1h-2c1 0 3 0 3 1v3h0v1c-1 3-1 7-1 9 1 1 2 1 2 1l1 2c-1 1-1 3-1 3 0 1 1 1 1 3h0-3-3-8 0-3 0v-5-6h-1v-2l1-4 1-1v-1s0-1 1-2c1-2 2-4 2-6z" class="g"></path><path d="M353 499l1-2h1v6h0c-2 0-1 0-2 1l-1-1h-1c0-1 0-3 1-4h0c1 1 0 3 1 4h0v-3-1z" class="o"></path><path d="M346 505c1-1 2-1 2-1 1-1 1-5 1-7 1 2 0 5 1 7l1-1h1l1 1c1-1 0-1 2-1h0c1 2 1 2 1 3-1 1-2 0-4 1h-6v-2z" class="O"></path><path d="M344 501c1-2 1-3 2-5 1-1 8 0 9 0l2 1c0 1 0 2-1 3v2l1 1h-2v-6h-1l-1 2h0c-1-1-1-1-1-2-1 1-1 2-2 1v-1h-1v2c-1 0-1-1-2-1h0c-1 2-1 4-1 7v2h1c0 1-1 1-1 2-1 0-2-1-2-2v-6z" class="C"></path><path d="M347 493h12v1c-1 3-1 7-1 9h-1l-1-1v-2c1-1 1-2 1-3l-2-1c-1 0-8-1-9 0-1 2-1 3-2 5h-1v-2l1-4 1-1c1 0 1 0 2-1z" class="E"></path><path d="M348 485h1l1 2h1l1-1h2 3l2 1v1l-1 1h-2c1 0 3 0 3 1v3h0-12c-1 1-1 1-2 1v-1s0-1 1-2c1-2 2-4 2-6z" class="j"></path><path d="M348 485h1l1 2h1l1-1h2 3l2 1v1l-1 1h-2c-2-1-4-1-6-1-2 2-3 3-4 5h1c-1 1-1 1-2 1v-1s0-1 1-2c1-2 2-4 2-6z" class="W"></path><path d="M358 503c1 1 2 1 2 1l1 2c-1 1-1 3-1 3 0 1 1 1 1 3h0-3-3-8 0-3 0v-5c0 1 1 2 2 2 0-1 1-1 1-2h-1 6c2-1 3 0 4-1 0-1 0-1-1-3h2 1z" class="D"></path><path d="M352 507h1l1 4h-1-3v-1c1-1 1-1 1-3h1z" class="B"></path><path d="M358 503c1 1 2 1 2 1l1 2c-1 1-1 3-1 3 0 1 1 1 1 3h0-3-3v-5h1-3-1c2-1 3 0 4-1 0-1 0-1-1-3h2 1z" class="a"></path><path d="M355 503h2l2 3h0c0 1-1 1-1 1-1 1 0 2-1 3 0-1 0-2-1-3h-3-1c2-1 3 0 4-1 0-1 0-1-1-3z" class="N"></path><path d="M358 503c1 1 2 1 2 1l1 2c-1 1-1 3-1 3 0 1 1 1 1 3h0-3l1-1c1 0 0-1 0-2 0 0 0-1 1-2l-1-1h0l-2-3h1z" class="C"></path><defs><linearGradient id="a" x1="316.877" y1="544.842" x2="316.822" y2="566.874" xlink:href="#B"><stop offset="0" stop-color="#161616"></stop><stop offset="1" stop-color="#333434"></stop></linearGradient></defs><path fill="url(#a)" d="M301 545h20 5 12 1c2 0 3-1 5 0 0 2 1 7 0 9h0v1h0l2 1h4v1c0 2 1 7 0 8h-1c1 1 2 1 3 1h-8v1h-2l2 1h-5-3 0-2-2-1-2-7l-1-1c-1 1-2 1-3 1h-1 0 0-13-1-2-2-9v1h-1l1-1v-2c0-1-3 0-4 0h4l1-1 1-1v-2h1v-2c-1 0-1 0-1-1h1 1c-1-3 0-5 1-8l-1-2v1 1 1s0 1-1 1v1c0 2 0 3-1 4s0 2 0 3h0l-1 1v-6c1-1 1-1 1-2-1-2-1 0-2-1 0-1 1-2 1-3h0v-1c1-1 1-1 1-2-1-1-1-1 0-2 2-1 6 0 9 0z"></path><defs><linearGradient id="b" x1="324.222" y1="550.778" x2="328.18" y2="557.253" xlink:href="#B"><stop offset="0" stop-color="#4a4948"></stop><stop offset="1" stop-color="#6b6b6d"></stop></linearGradient></defs><path fill="url(#b)" d="M325 555v-7h1s1 0 1 1v1l1 9v1l-1-1c-1 0-1 0-1 1-1-1-1-4-1-5z"></path><path d="M328 566v1l2-2v-1l-1-1c0-2 1-4 1-6v-6c1 1 1 4 1 5 0 4 1 8 1 12h0-1-2l-1-1v-1h0z" class="J"></path><path d="M298 568v-1c-1-2 1-18 2-20h1c0 5-1 10-1 14 0 2-1 5 0 6h1v1h-2-1z" class="S"></path><path d="M295 551v-3 1c1 4 0 8 0 12 0 3-1 5-1 7h4 1-9v1h-1l1-1v-2c0-1-3 0-4 0h4l1-1 1-1v-2h1v-2c-1 0-1 0-1-1h1 1c-1-3 0-5 1-8z" class="f"></path><path d="M291 565l1-1v-2h1c0 1-1 4 0 6h-3v-2c0-1-3 0-4 0h4l1-1z" class="E"></path><path d="M344 554v1h0l2 1h4v1c0 2 1 7 0 8h-1c1 1 2 1 3 1h-8c-1-3-1-7 0-9v-1l-1-1 1-1z" class="F"></path><path d="M347 564l1-1h1v-1c-1-1-1-1-2-3h0c1 0 2 0 2 1h1v4h-3z" class="Y"></path><path d="M344 554v1h0l2 1c-2 1-1 6-1 8l1 1c0-1 0-1 1-1h3v-4-3c0 2 1 7 0 8h-1c1 1 2 1 3 1h-8c-1-3-1-7 0-9v-1l-1-1 1-1z" class="W"></path><path d="M338 559c0-3-1-8 0-11 1-1 1-1 2 0 1 2 1 5 1 8v4 4c1 1 1 3 1 3l2 1h-5-1c-1-1-1-2-1-3v-5l1-1z" class="F"></path><path d="M338 559c0 2 1 4-1 6v-5l1-1z" class="X"></path><path d="M340 563v-7h1v4h0c-1 1-1 2-1 3z" class="W"></path><path d="M340 563c0-1 0-2 1-3h0v4 3h0l-1-1v-3z" class="R"></path><defs><linearGradient id="c" x1="337.279" y1="556.856" x2="332.502" y2="558.615" xlink:href="#B"><stop offset="0" stop-color="#29292a"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#c)" d="M333 556c-1-2-1-5 0-8h2c1 3 0 7 1 10l1 2v5c0 1 0 2 1 3h1-3 0-2-2 0l1-1c1-2 0-8 0-11z"></path><defs><linearGradient id="d" x1="332.095" y1="557.343" x2="335.647" y2="563.894" xlink:href="#B"><stop offset="0" stop-color="#464343"></stop><stop offset="1" stop-color="#69696c"></stop></linearGradient></defs><path fill="url(#d)" d="M333 556h1v-1c1 3 1 6 1 9v3c0 1 0 1 1 1h0-2-2 0l1-1c1-2 0-8 0-11z"></path><path d="M320 557c2 0 1-2 2-3h2 0v3l1-2c0 1 0 4 1 5 0-1 0-1 1-1l1 1v2 4h0v1l1 1h-7l-1-1v-1c-1-2-1-5-1-8v-1z" class="J"></path><path d="M325 555c0 1 0 4 1 5l-1 1c0 1 0 1-1 2v-4-1h0v-1l1-2z" class="H"></path><path d="M326 567c0-3 0-5 1-7l1 2v4h0-1l-1 1z" class="N"></path><path d="M322 568v-6c0-1 0-3 2-4h0v10c1 0 2 0 3-1h-1l1-1h1v1l1 1h-7z" class="Z"></path><defs><linearGradient id="e" x1="320.407" y1="558.834" x2="322.526" y2="566.312" xlink:href="#B"><stop offset="0" stop-color="#676766"></stop><stop offset="1" stop-color="#858586"></stop></linearGradient></defs><path fill="url(#e)" d="M320 557c2 0 1-2 2-3h2 0v3 1c-2 1-2 3-2 4v6l-1-1v-1c-1-2-1-5-1-8v-1z"></path><defs><linearGradient id="f" x1="320.472" y1="548.215" x2="321.194" y2="554.745" xlink:href="#B"><stop offset="0" stop-color="#272526"></stop><stop offset="1" stop-color="#434443"></stop></linearGradient></defs><path fill="url(#f)" d="M314 548l1-1c0 1 1 1 1 2 1 1 0 2 0 4 1-1 1-1 1-2s0-1 1-2v-1-1h1c1 1 3 1 4 1 1 1 1 1 0 2l1 4h0-2c-1 1 0 3-2 3v1c0 3 0 6 1 8v1c-1 1-2 1-3 1 1-1 1-2 1-3h-1v-3h-2v-3-1-2l-1-6v-2h-1z"></path><path d="M324 554l-1-1h-2v-1s1-2 2-2h0l1 4h0z" class="F"></path><path d="M316 556v1h1s0-2 1-3l1-1c0-1-1-2 0-3h0c1 1 1 2 1 3-2 2 0 7-3 7l-1-1v-1-2z" class="H"></path><path d="M316 559l1 1c3 0 1-5 3-7v4 1c0 3 0 6 1 8v1c-1 1-2 1-3 1 1-1 1-2 1-3h-1v-3h-2v-3z" class="J"></path><path d="M319 565v-8h1v1c0 3 0 6 1 8v1c-1 1-2 1-3 1 1-1 1-2 1-3z" class="M"></path><path d="M305 547l1-1c1 0 2 0 3 1h2s1 0 1 1c1 1 1 2 2 2h0v-2h1v2l1 6v2 1 3h2v3h1c0 1 0 2-1 3h-1 0 0-13-1-2v-1l1-9c0-4 1-8 2-12v10l1 1v-10z" class="c"></path><path d="M302 558v-1h1c1 1 0 8 0 11h-2v-1l1-9z" class="L"></path><path d="M305 566v-6c1-1 1-3 2-3 1 1 1 1 3 2 0 1 0 2 1 3v5h-1v-4l-1 1s0 1-1 2h0l-1 1h-2v-1z" class="M"></path><path d="M305 566h0c0-1 0-3 1-5h1c1 1 0 4 1 5l-1 1h-2v-1z" class="e"></path><path d="M311 557h1v3c1 0 2 0 2 2h0c1-1 1-3 2-4v1 3h2v3h1c0 1 0 2-1 3h-1 0 0-13l1-1h2l1-1h0c1-1 1-2 1-2l1-1v4h1v-5-4-1z" class="L"></path><path d="M316 562h2v3h1c0 1 0 2-1 3h-1v-1c0-1-1-3-1-5z" class="f"></path><defs><linearGradient id="g" x1="308.346" y1="547.003" x2="309.117" y2="554.604" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#484948"></stop></linearGradient></defs><path fill="url(#g)" d="M305 547l1-1c1 0 2 0 3 1h2s1 0 1 1c1 1 1 2 2 2h0v-2h1v2l1 6v2c-1 1-1 3-2 4h0c0-2-1-2-2-2v-3h-1v1h0c-1 0-1-3-1-4l-1-1c-1-1-1-3-1-3-1 1-1 5-2 6-1-1 0-6 0-8l-1-1z"></path><path d="M311 557v-1c0-2 1-4 1-5 1 1 1 4 2 5h0v-7l1 1 1 6v2c-1 1-1 3-2 4h0c0-2-1-2-2-2v-3h-1z" class="c"></path><path d="M303 401h14 7 4l9 1h1l3 1c1 1 2 1 2 3-1 1-1 1-1 2l2-1 8 1h6l1-1h0c0 3 1 6 0 9v2c0 1 0 1-1 2l3 1c1 1 1 3 0 4v1c0 1 1 1 1 2s1 2 0 3l-1-1h-1-18v2h-1l-1 1h0l-1-1c0-1 0-1-1 0-1 0-1 1-1 2h0c-1-1-1-2-2-2 0-1-1-1-1-1h-1v2c-1 0-1 0-2-1l1-1h-2v-2h-1-2 0v-3h-2l-2 1c0 1 0 2-1 2 0 2 0 3-1 4 0-1 1-4 0-6h0 0l1-1v-1l-2-1h-5 0-1v-2-1c0-1 0-4-1-6h-1 0l-1-1-6-3-2-1c-2-1-4-2-6-2-2-1-4-1-6-1h-2v-1h0-1v-1h3v-1h-3v-1l5-1 10-1z" class="E"></path><path d="M332 421h1 1l-1 1h-1 0v-1z" class="q"></path><path d="M329 413h0v1c-1 1-1 2-1 4 1 1 1 2 1 2l2 2h0-2c-2-2-2-5-2-8 1-1 1-1 2-1z" class="b"></path><path d="M330 425h1c1 1 1 3 1 4v1 1h-2v-2h-1-2 0v-3h3v-1z" class="G"></path><path d="M329 413c1 0 1 0 2 1 1 0 0 0 0 1l1 1h0s1 0 1-1c1 0 2-1 2-2h0c0 1-1 2-2 3h-1v1h0 2c-1 1-1 1-1 2-2 0-2 0-4-1h-1c0-2 0-3 1-4v-1h0z" class="q"></path><path d="M329 413c1 0 1 0 2 1 1 0 0 0 0 1v2h-1c-1-1 0-2-1-3v-1h0z" class="I"></path><path d="M320 424h1v-2c0 1 1 1 1 2h0 1l7 1v1h-3-2l-2 1c0 1 0 2-1 2 0 2 0 3-1 4 0-1 1-4 0-6h0 0l1-1v-1l-2-1z" class="M"></path><defs><linearGradient id="h" x1="334.711" y1="403.587" x2="322.447" y2="414.484" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#4a494a"></stop></linearGradient></defs><path fill="url(#h)" d="M322 411c2-1 5-2 7-3 0-1 1-1 2-1 0 0 1 0 1-1 1 0 1-1 2-1-1 0-1-1-1-1l1 1h0c1-1 1-1 2-1l1 1v1c-4 3-9 4-13 7-1 1-3 1-4 1v-1h0-1c1 0 2-1 3-2z"></path><path d="M316 404l1-2h1 1v1h1v8h1l1-1v-7h0c1 2 1 4 1 5s-1 2-1 3h0c-1 1-2 2-3 2h1 0v1 1c-2 0-4 0-5-1h-3c0-1-1-2-1-3h2 0v-1h1 0v2h2c1-1 1-2 1-3-1-2 0-3-1-5z" class="G"></path><path d="M318 402h1v1c0 3 0 6-1 9v-10z" class="q"></path><path d="M311 411h2c0 1 2 2 3 2h3 1 0v1 1c-2 0-4 0-5-1h-3c0-1-1-2-1-3z" class="D"></path><path d="M340 427h5 1 5 4 4v2h0l1 1h-18c-2 0-6 1-7 0 1-1 5-1 7-1-2-1-4-1-5-1h0l3-1z" class="O"></path><path d="M346 427h5 3l1 2c-1 1-8 0-10 0v-2h1z" class="Q"></path><path d="M338 420h0 5l-1 1h6 0v1c-1 1-1 2-1 3v1l-2 1h-5l-3 1h0-2v-2c0-2 0-2 2-3 0-1 0-1 1-2v-1z" class="D"></path><path d="M347 425h0c-1 0-1 0-2-1 0-1 1-2 1-3h1l1 1c-1 1-1 2-1 3z" class="F"></path><path d="M338 421h6v2h-1v-1h-2v1c1 0 1 0 1 1-1 2-1 2-2 3l-3 1h0-2v-2c0-2 0-2 2-3 0-1 0-1 1-2z" class="G"></path><path d="M315 414c1 1 3 1 5 1h0c2 0 2-1 3 0 0 1 0 2-1 3l1 6h-1 0c0-1-1-1-1-2v2h-1-5 0-1v-2-1c0-1 0-4-1-6 1 0 1 0 2-1z" class="r"></path><path d="M320 415c2 0 2-1 3 0 0 1 0 2-1 3h-1c-1 1-1 2-1 3v-6z" class="C"></path><path d="M320 421c0-1 0-2 1-3h1l1 6h-1 0c0-1-1-1-1-2v2h-1-5 0 5v-3z" class="U"></path><path d="M343 420h6 5 4 0l3 1c1 1 1 3 0 4v1c0 1 1 1 1 2s1 2 0 3l-1-1h-1l-1-1h0v-2h-4-4-5-1l2-1v-1c0-1 0-2 1-3v-1h0-6l1-1z" class="D"></path><path d="M358 422h1c0 1 0 3-1 5l-1-1v-1c0-2 0-2 1-3z" class="G"></path><path d="M343 420h6 5 4l1 1h0-10v5c1 0 1-1 1-1l1-2 1-1 1 2v1l1-2h1v4h-4-5-1l2-1v-1c0-1 0-2 1-3v-1h0-6l1-1z" class="Z"></path><path d="M303 401h14 7-9v6h1v-3c1 2 0 3 1 5 0 1 0 2-1 3h-2v-2h0-1v1h0-2c0 1 1 2 1 3h3c-1 1-1 1-2 1h-1 0l-1-1-6-3-2-1c-2-1-4-2-6-2-2-1-4-1-6-1h-2v-1h0-1v-1h3v-1h-3v-1l5-1 10-1z" class="B"></path><path d="M303 410c-1-2-1-4-2-6 1 2 3 4 4 4l2 1v1h-1l-1 1-2-1z" class="G"></path><path d="M315 403h0v5l-2-1h0-1c-1-1-1-2-1-3l1-1 1 1h1s0-1 1-1z" class="b"></path><path d="M288 403l5-1-1 1c0 1 3 3 5 4h-6-2v-1h0-1v-1h3v-1h-3v-1z" class="C"></path><path d="M305 408c0-1-1-2-1-3h0c2 2 3 4 7 5v1c0 1 1 2 1 3h3c-1 1-1 1-2 1h-1 0l-1-1-6-3 1-1h1v-1l-2-1z" class="W"></path><path d="M344 407l8 1h6l1-1h0c0 3 1 6 0 9v2c0 1 0 1-1 2h0-4-5-6-5 0c1 0 1-2 1-3l4-6c-1-1-1-1-1-2h0l-1-1h1l2-1z" class="q"></path><path d="M343 411v-1l1 1-1 6 4 1v1h1c1-2 0-4 1-5v6h-6-5 0c1 0 1-2 1-3l4-6z" class="G"></path><path d="M359 407h0c0 3 1 6 0 9v2c0 1 0 1-1 2h0-4-5v-6c0-1 0-3 1-3l2-1h1 0c1-1 2-1 3-1h0v-1h2-6 0 6l1-1z" class="B"></path><path d="M353 410c1 0 1 0 2 1h1c1 1 1 2 1 3-1 0-1 0-2 1s0 3-1 4v-1-8h-2 1z" class="C"></path><path d="M352 410h2v8 1 1h-5v-6c0-1 0-3 1-3l2-1z" class="o"></path><path d="M354 419h-3l-1-1c1-1 1-2 1-3v-2h1 0v2c0 2 1 2 2 3v1z" class="K"></path><path d="M300 161l17-1v1c4 0 7 0 11 1h0c-1 3 0 5 0 8v15h-5-5l7 1c1 0 2 0 3 1-1 0-1 1-1 2v5l-5 1c4 1 7 1 10 2h0c-1 1-2 0-3 1h-11l-1-1c-4 1-10 1-14 1l-1-1c-1 1-1 1-2 1h-1c-1 0-1 0-3-1h0c1-1 3-1 4-1-1 0-2-1-3 0-1 0-4 1-5 1l-1-1v-6h0c-1 1-2 1-3 1-1-2 0-3 0-4v-1c0-3 1-6 1-9l1-7c1-2 2-4 1-6v-2h0 2c1-1 1-1 2-1h3 2z" class="s"></path><path d="M297 186c1 0 3-1 4 0-1 0-2 0-4 1h-5v1l1 1-2 1c-1 1-2 1-3 1-1-2 0-3 0-4h5c1-1 3 0 4-1z" class="d"></path><path d="M291 190l2-1-1-1v-1h5v1h0l-1 1v1 4c1 1 2 1 3 1h2v1h-1c-1 0-2-1-3 0-1 0-4 1-5 1l-1-1v-6h0z" class="N"></path><defs><linearGradient id="i" x1="286.421" y1="174.003" x2="295.532" y2="170.898" xlink:href="#B"><stop offset="0" stop-color="#686769"></stop><stop offset="1" stop-color="#7f7e7d"></stop></linearGradient></defs><path fill="url(#i)" d="M300 161l17-1v1c-6 0-14 0-20 2h0v1c1 2 0 6 0 8-1 2-1 4-1 5 0 3 0 7 1 9-1 1-3 0-4 1h-5v-1c0-3 1-6 1-9l1-7c1-2 2-4 1-6v-2h0 2c1-1 1-1 2-1h3 2z"></path><defs><linearGradient id="j" x1="337.674" y1="393.048" x2="368.8" y2="344.162" xlink:href="#B"><stop offset="0" stop-color="#101110"></stop><stop offset="1" stop-color="#323031"></stop></linearGradient></defs><path fill="url(#j)" d="M337 357c2 1 3 1 4 1h18 11 3c9 0 17 2 25 5 2 1 5 2 6 3l1 1 1 1 2 1h-2l-1 1h0c-2 1-3-1-5-1-1 0-2 0-3 1-5-2-11-3-17-3h-5c-4 0-9 0-13-1-3 0-5-1-8-2v1 9 7l-2 1h-1v1h1c0 1-1 3 0 5h-2-2 0l1 1c2 1 4-1 6 0-2 1-5 1-7 1h-1 0c0 1 0 0-1 1v-2c-1-1-1-2 0-3 0 0 0-1 1-1l-1-1c-1-2-5-3-8-4h-1-1c-1-1-2-1-3-2h1 1l-6-1h-7 0 3v-1l-2-2c-1 0-2 0-2-1v-1-1h-1-2 0c0-1-1-1-1-2h0l-1 2h-1c-1-1 0-1-1-2h-1-1v2h-4c0-1 0-2-1-2-1 1-1 2-3 3h0c-1-1-1-2-1-3h0l-1 1c0 1 0 2-1 2h-3s-1 1-2 1v-1s-1-1-1-2c1 0 2 0 3 1v-1-1c-2 0-2-1-3-2-1 1-2 1-4 1h-1l1-1h-1c-1-1-1-1-1-2h1l7-1-1-1c0-1 1-2 2-2h1c1-1 2-1 4-1h3 2c1 0 2 0 3-1h0c1 1 2 1 3 1 1-1 1-1 2-1v-1h1v1c1 1 2 1 3 1l1-1 1 1c1 0 1-1 2-1h0v1c2 0 4 0 5 1h2 2c0-1 1-1 1-2l1 1h2v-3z"></path><path d="M343 372h0c1 1 1 2 1 3-1 0-1 0-2-1l1-2z" class="X"></path><path d="M343 377h1 0v1c1 1 1 2 1 3-1 0-2 0-3-1 1-1 0-1-1-2h2v-1z" class="C"></path><path d="M380 367c2-1 3-1 5 0h2 3 0l-3-1c-1 0-3 0-4-1 4 1 8 1 12 1v1h1c1 0 2 1 3 1s3 1 4 1h1c0 1 1 1 1 1h0c-2 1-3-1-5-1-1 0-2 0-3 1-5-2-11-3-17-3z" class="R"></path><path d="M352 382v-13c0-2 0-4 1-5h0l1 1v9 7l-2 1z" class="k"></path><path d="M325 370v-1h1v1h1l1-1 1 1h-1v1h2c1 1 1 1 2 1 0-1 0-1 1-2h0 1c0 1 1 2 1 3h0l2-2v1c0 1 0 1-1 2 2 1 4 2 7 3v1h-2c1 1 2 1 1 2l-8-2h1v-1c0-2-2-3-3-4h0c-2 0-3 0-5-1-2 0-1-1-2-2z" class="D"></path><path d="M320 370l1-2h1c0 1 0 2 1 3h0c1 0 1 0 2-1 1 1 0 2 2 2 2 1 3 1 5 1h0c1 1 3 2 3 4v1l-6-1h-7 0 3v-1l-2-2c-1 0-2 0-2-1v-1-1h-1-2 2v-1z" class="L"></path><path d="M300 367l3-1h9c1 0 3 0 4 1h-1-15v2h1 0l1-1h1c1 1 1 1 1 2h1v-1h1v-1c1 0 1 0 2-1l1 1 1 1h0 1 0l1-1c1 0 2 0 2-1l1 1h1 1 0c1 1 2 2 3 2v1h-2 0c0-1-1-1-1-2h0l-1 2h-1c-1-1 0-1-1-2h-1-1v2h-4c0-1 0-2-1-2-1 1-1 2-3 3h0c-1-1-1-2-1-3h0l-1 1c0 1 0 2-1 2h-3s-1 1-2 1v-1s-1-1-1-2c1 0 2 0 3 1v-1-1c-2 0-2-1-3-2h5z" class="C"></path><path d="M337 357c2 1 3 1 4 1h18 11 3-1c-2 1-6 1-9 1l-22-1c1 2 1 3 1 4 2 0 3 0 4 1s1 2 1 3-1 1-1 1c-1 1-2 1-2 2v1h-1l1-1h-2c-1-1-2-1-3-1h0l-4-1h1l-1-2c-2-1-4-1-5-1h0 4 2l1-1-1-1c-1-1-1-1-3-1 0-1 1-1 1-2l1 1h2v-3z" class="a"></path><path d="M335 365h4v1l-1 1v1h1 0l-4-1h1l-1-2z" class="D"></path><path d="M339 365c2 1 3 1 5 2h0v2h-2c-1-1-2-1-3-1h0 0-1v-1l1-1v-1z" class="F"></path><path d="M337 360l1 1c1-1 1-1 1-2h2c1 1 0 4 2 5h0l1 1c-2 0-4 0-5-1h-3l1-1-1-1c-1-1-1-1-3-1 0-1 1-1 1-2l1 1h2z" class="G"></path><path d="M314 360c1-1 1-1 2-1v-1h1v1c1 1 2 1 3 1l1-1 1 1c1 0 1-1 2-1h0v1c2 0 4 0 5 1h2 2c2 0 2 0 3 1l1 1-1 1h-2-4 0c1 0 3 0 5 1l1 2h-1-19c-1-1-3-1-4-1h-9l-3 1h-5c-1 1-2 1-4 1h-1l1-1h-1c-1-1-1-1-1-2h1l7-1-1-1c0-1 1-2 2-2h1c1-1 2-1 4-1h3 2c1 0 2 0 3-1h0c1 1 2 1 3 1z" class="U"></path><path d="M291 367c2-1 5-2 8-2l1 2h-5c-1 1-2 1-4 1h-1l1-1z" class="O"></path><path d="M305 364h9 15l1 1c0 1 0 1-1 1h-6l1-1c-2-1-3-1-5-1h-14z" class="M"></path><path d="M324 360c2 0 4 0 5 1h2 2c2 0 2 0 3 1l1 1-1 1h-2c-3-1-7-1-10-1 0-1 1-1 2-1h2v-1c-2 0-2 0-3-1h-1z" class="V"></path><defs><linearGradient id="k" x1="314.718" y1="370.063" x2="309.207" y2="360.186" xlink:href="#B"><stop offset="0" stop-color="#b3adae"></stop><stop offset="1" stop-color="#dce1e2"></stop></linearGradient></defs><path fill="url(#k)" d="M305 364h14c2 0 3 0 5 1l-1 1h6c1 0 1 0 1-1l-1-1h1c1 0 3 0 5 1l1 2h-1-19c-1-1-3-1-4-1h-9l-3 1-1-2 6-1z"></path><path d="M330 364c1 0 3 0 5 1l1 2h-1-19c-1-1-3-1-4-1h-9 20 6c1 0 1 0 1-1l-1-1h1z" class="S"></path><path d="M314 360c1-1 1-1 2-1v-1h1v1c1 1 2 1 3 1l1-1 1 1c1 0 1-1 2-1h0v1h1c1 1 1 1 3 1v1h-2c-1 0-2 0-2 1-9-1-19-1-27 1l-1-1c0-1 1-2 2-2h1c1-1 2-1 4-1h3 2c1 0 2 0 3-1h0c1 1 2 1 3 1z" class="j"></path><path d="M330 431h2l-1 1c1 1 1 1 2 1v-2h1s1 0 1 1c1 0 1 1 2 2h0c0-1 0-2 1-2 1-1 1-1 1 0l1 1h0l1-1h1 1l1-1h1 4l-3 1v2l1 2v1c0 1-1 1 0 2h1v4 4c0 2 0 3-1 5v1c1 1 1 2 0 3 0 1 1 1 2 2h2v3 3l-3 21c0 2-1 4-2 6-1 1-1 2-1 2v1l-1 1-1 4v2h1v6 5h0-1c-1 1-3 1-4 1l-1 2h0 2c1 1 1 2 1 4h-1 0-1 0-2v-3h1c-2-2-1-1-2-1s-1-1-1-1h-1l-1-1 1 1c-1 1-2 1-3 1v-2-1c0-1 0-1-1-1 0-2-1-2 0-4 0-1 0-1-1-1 0-1 0-1-1 0h-2 0v-2h-2c-1-3 0-7 0-11v-1c1 0 2 0 3 1h0c2-1 2-4 2-6h1v-1h-1 1c1-1 0-8 0-10v-9c0-1 0-2-1-3l-1 1-1-2 2-1h0c1 1 1 1 2 0l1-2v1l1-1 1 1v-11-3-4l-1-1c-1-2-2-3-2-5v-2h0 0-1c0-1-1-2-1-2v-1c0-1 0-1 1-1z" class="q"></path><path d="M336 499v1 9 4l2-1h5c-1 1-3 1-4 1l-1 2h0 2c1 1 1 2 1 4h-1 0-1 0-2v-3h1c-2-2-1-1-2-1s-1-1-1-1h-1l1-1 1-14z" class="e"></path><path d="M330 431h2l-1 1c1 1 1 1 2 1 1 1 1 8 1 10l-1-1c-1-2-2-3-2-5v-2h0 0-1c0-1-1-2-1-2v-1c0-1 0-1 1-1z" class="W"></path><path d="M329 462c1 1 1 1 2 0l1-2v1l1-1 1 1v9l2 29-1 14-1 1-1-1v-17c0-4 0-9-1-13v-4l-1-3v2s0-1-1-2v-9c0-1 0-2-1-3l-1 1-1-2 2-1h0z" class="L"></path><path d="M332 461l1-1 1 1v9c0-1 0-2-1-3v-2 2l-1 1v11l-1-3 1-15z" class="K"></path><path d="M329 462c1 1 1 1 2 0l1-2v1l-1 15v2s0-1-1-2v-9c0-1 0-2-1-3l-1 1-1-2 2-1h0z" class="D"></path><path d="M343 472v1c1 0 2 0 2 1l-1 5c0 3-1 5-2 8 0 2 0 4 1 6h2v1l-1 1-1 4v2h1v6 5h0-1-5c-1-3 0-7 0-10 0-4 0-8 1-12 1-5 3-9 3-14v-3l1-1z" class="d"></path><path d="M343 472v1c1 0 2 0 2 1l-1 5c0-1 1-2 0-3v-2h-1v2h-1v-3l1-1z" class="S"></path><path d="M342 487c0 2 0 4 1 6h2v1l-1 1-1 1h-1c-1-3-1-7 0-9z" class="J"></path><path d="M343 499v2h1v6 5c-1 0-1 0-2-1 0-4 0-8 1-12z" class="B"></path><defs><linearGradient id="l" x1="332.061" y1="500.106" x2="328.248" y2="500.189" xlink:href="#B"><stop offset="0" stop-color="#605f5f"></stop><stop offset="1" stop-color="#797879"></stop></linearGradient></defs><path fill="url(#l)" d="M330 476c1 1 1 2 1 2v-2l1 3v4c1 4 1 9 1 13v17l1 1c-1 1-2 1-3 1v-2-1c0-1 0-1-1-1 0-2-1-2 0-4 0-1 0-1-1-1 0-1 0-1-1 0h-2 0v-2h-2c-1-3 0-7 0-11v-1c1 0 2 0 3 1h0c2-1 2-4 2-6h1v-1h-1 1c1-1 0-8 0-10z"></path><path d="M330 476c1 1 1 2 1 2v-2l1 3v4 3l-1 1c-1 0-1 1-1 1v2 4h-2c-1 1 0 6 0 9 0 1 0 1-1 2h0l-1-1h0-2c-1-3 0-7 0-11v-1c1 0 2 0 3 1h0c2-1 2-4 2-6h1v-1h-1 1c1-1 0-8 0-10z" class="H"></path><path d="M324 493v-1c1 0 2 0 3 1 0 3 0 8-1 11h0-2c-1-3 0-7 0-11z" class="K"></path><path d="M349 458h2v3 3l-3 21c0 2-1 4-2 6-1 1-1 2-1 2h-2c-1-2-1-4-1-6 1-3 2-5 2-8l1-5c0-1-1-1-2-1v-1l-1 1c0-2-1-4-2-5s-2-3-3-3l-1-1h0l2-1c1 0 2-1 3-1s3-1 5-2c1 0 1 0 2-1h-1 0l2-1z" class="Z"></path><path d="M344 470h1c0-2 0-3 1-4h1c0 2-1 5-2 8h0c0-1-1-1-2-1v-1c1-1 1-1 1-2z" class="g"></path><path d="M344 463c1-1 2-1 3-1v4h-1c-1 1-1 2-1 4h-1l-1-1c0-1 0-1 1-1 0-1 0-1-1-1 0-1 0-1-1-2 1-1 2-1 3-1h0l-1-1z" class="N"></path><path d="M338 463c1 0 2-1 3-1-1 1-1 1 0 1h3l1 1h0c-1 0-2 0-3 1 1 1 1 1 1 2 1 0 1 0 1 1-1 0-1 0-1 1l1 1c0 1 0 1-1 2l-1 1c0-2-1-4-2-5s-2-3-3-3l-1-1h0l2-1z" class="D"></path><path d="M340 468c-1-1-2-3-3-3l-1-1h0l2-1c1 1 1 1 3 1h0v3l-1 1z" class="F"></path><path d="M349 458h2v3 3l-3 21c0 2-1 4-2 6-1-1-1-2-1-3 0-3 1-5 2-7 1-5 1-11 2-16v-3c-1-1-2 0-4 0l1-2c1 0 1 0 2-1h-1 0l2-1z" class="i"></path><path d="M349 458h2v3 3c0-2 0-4-1-5h1l-2 2v4-3c-1-1-2 0-4 0l1-2c1 0 1 0 2-1h-1 0l2-1z" class="L"></path><path d="M342 432h1l1-1h1 4l-3 1v2l1 2v1c0 1-1 1 0 2h1v4 4c0 2 0 3-1 5v1c1 1 1 2 0 3 0 1 1 1 2 2l-2 1h0l-3 1h-1-2c-2 1-4 1-6 0v-1s0-1 1-2c0-3 1-6 1-9 1-5 1-10 0-14 0-1 0-2 1-2 1-1 1-1 1 0l1 1h0l1-1h1z" class="D"></path><path d="M340 433h0l1-1v7c-1-2-1-4-1-6h0z" class="b"></path><path d="M344 439c1-1 1-2 1-3s1-1 1-2l1 2v1c0 1-1 1 0 2h1v4l-1-1v-1h-1l-1 1v2h0c-1 1-1 3-2 3l-1-1c0-1 1-1 1-2h1c1-1 1-1 1-2-1-1-1-1-1-2v-1z" class="S"></path><path d="M344 439c1-1 1-2 1-3s1-1 1-2l1 2c0 1 0 1-1 2v2c-1 0-1-1-2-1z" class="W"></path><path d="M337 434c0-1 0-2 1-2 1-1 1-1 1 0l1 1h0c0 2 0 4 1 6-1 3-2 7-2 11l-1-1c-1 1 0 3-1 4 0 1 1 1 1 1h0c0 1-1 2-2 3 0-3 1-6 1-9 1-5 1-10 0-14z" class="C"></path><path d="M343 447c1 0 1-2 2-3h0v-2l1-1h1v1l1 1v4c0 2 0 3-1 5v1c1 1 1 2 0 3 0 1 1 1 2 2l-2 1h0l-3 1h-1-2-4v-1c2-2 2-4 3-6l3-6z" class="f"></path><path d="M345 455c0 1-1 1-1 2l-1 2c1 0 1 0 1 1h-1l-5-1c2-1 5-2 7-4z" class="P"></path><path d="M347 452v1c1 1 1 2 0 3 0 1 1 1 2 2l-2 1h0l-3 1c0-1 0-1-1-1l1-2c0-1 1-1 1-2s2-2 2-3z" class="V"></path><path d="M340 282c0-3-1-1-2-3h0c4 0 8 1 12 3-1 1-1 1-3 1v1h1 3l1 1h1c1 0 1 1 2 1v2c-1 0-1 1-1 1v1 1l-1 1v3 1l-1 1v1l-1-1c-1 0-2 0-3-1v1h1c-1 1-3 0-5 0h-3c-1 0-1 0-2-1-1 0-2 1-3 0h-3v1h0v3h1v-3h1v2h1v-2h0 1v2h1v-2h1v3 3c-1 1-4 1-6 1s-5-1-7 0l-1-1h-5 0-2-9-4-1-2-1c-1 1-1 1-2 1v2h2c-1 1 0 1 0 2l-1 1h0l-4 2c-2-1-3 0-5-1h0c-2-1-3-1-4-1s-2 0-2-1c-2 0-2-1-2-2 0-2 0-2-1-3l-1 1h-2c1-1 2-1 2-2l-1-1h0c-1-1-1 0-2 0h-3 1l-1-2h2c0-2 1-2 2-3v-2h-1v-2l1-1v-1l-1-2 2-1-1-1 1-1c2 1 3 0 5-1h6 0l2-1h3c4-1 9-1 13-1h15 2 2c4 1 7 1 10 1s5 1 8 1v-1l-6-1z" class="q"></path><path d="M292 292l5-1-1 1v1h2l-7 1v-2h1z" class="L"></path><path d="M295 297h0 1v2h1v-2h0 1v2h1v-2h1v2l1-1c0 2 0 4 1 5h-1l-8 1h-1v-5h1l1 1h0l1-3z" class="l"></path><path d="M295 297l-1 3h0l-1-1h-1v5h1c-1 0-1 1-1 2h-6c0-1 0-1-1-1l1-5h1v-2h1c0 1 0 1 1 2h0c1-1 1-1 1-2h-1l6-1z" class="J"></path><path d="M286 300h1v1 3h5 1c-1 0-1 1-1 2h-6c0-1 0-1-1-1l1-5z" class="H"></path><path d="M279 296l1 2h0c1 0 3 1 4 1h1 0l1 1-1 5c1 0 1 0 1 1h0v1c1 1 3 2 5 3-2-1-3-1-4-1s-2 0-2-1c-2 0-2-1-2-2 0-2 0-2-1-3l-1 1h-2c1-1 2-1 2-2l-1-1h0c-1-1-1 0-2 0h-3 1l-1-2h2c0-2 1-2 2-3z" class="c"></path><path d="M279 296l1 2c-1 2-2 2-4 3l-1-2h2c0-2 1-2 2-3z" class="M"></path><path d="M317 290l3 1c1 0 2 0 3 1l-2 1h-23-2v-1l1-1 20-1z" class="T"></path><path d="M290 290h3l1 1h0l-2 1h-1v2l-5 1-5 2-1 1h0l-1-2v-2h0v-1l-1-1h1 1c2 0 4 0 6-1s3-1 4-1z" class="C"></path><path d="M281 297l-1-1h0c2-1 3-2 5-2v1h1l-5 2z" class="J"></path><path d="M287 293l4-1v2l-5 1h-1v-1l2-1z" class="Z"></path><path d="M290 290h3l1 1h0l-2 1h-1l-4 1-1-1h0c-1 0-2 1-3 1s-2 0-3-1c2 0 4 0 6-1s3-1 4-1z" class="G"></path><path d="M293 304l8-1c-1 1-1 1-2 1v2h2c-1 1 0 1 0 2l-1 1h0l-4 2c-2-1-3 0-5-1h0c-2-1-4-2-5-3v-1h0 6c0-1 0-2 1-2z" class="a"></path><path d="M293 304l8-1c-1 1-1 1-2 1v2h-13 0 6c0-1 0-2 1-2z" class="U"></path><path d="M300 297l4-1v1h1v-1h9l1-1 1 1h1 1 0l1-1 1 1c2 0 6-1 7 1v1h1v-2h1v1 3h1v-4l1 1v2h1v-2h1 0v3h1v-3h1v2h1v-2h0 1v2h1v-2h1v3 3c-1 1-4 1-6 1s-5-1-7 0l-1-1h-5 0-2-9-4-1-2c-1-1-1-3-1-5l-1 1v-2z" class="J"></path><path d="M301 298v-1c1 1 1 1 1 2h1v-1h1c0 2 1 3 0 5h-2c-1-1-1-3-1-5z" class="P"></path><path d="M305 303v-5l1-1v1c1 0 1 0 1-1h1v2l1 1v1c-1 1 0 1 0 2h-4z" class="V"></path><path d="M319 299h0 2l1-1h0 1v4h1v-2l1-1v4h-5 0l1-1-1-2-1-1z" class="L"></path><path d="M309 300l1-4v1l1 2h0 1c0-1 0-2 1-2h0c1 1 1 2 1 3l1-1v-2h1v2c1 0 1-2 1-3h1v1 2h1l1 1 1 2-1 1h-2-9c0-1-1-1 0-2v-1z" class="i"></path><path d="M318 297v2h1l1 1 1 2-1 1h-2v-2-4z" class="P"></path><path d="M324 282h2v1h0l1 1v4c-1 1-2 1-3 1h-2-5-1c-2 0-4 0-5 1h0 6l-20 1-5 1 2-1h0l-1-1h-3c-1 0-2 0-4 1s-4 1-6 1h-1-1l1 1v1h0-1v-2l1-1v-1l-1-2 2-1-1-1 1-1c2 1 3 0 5-1h6 0l2-1h3c4-1 9-1 13-1h15z" class="D"></path><path d="M305 283h2 2c0 1 1 5 0 6h-1-2c-1-1-1-2-1-3v-3z" class="j"></path><path d="M303 283h2v3c0 1 0 2 1 3h-5 0c0-2 1-4 0-6h2z" class="K"></path><path d="M301 283h2v5c-1 0-1 1-2 1 0-2 1-4 0-6z" class="o"></path><path d="M326 283l1 1v4c-1 1-2 1-3 1h-2v-1l-1-1h1l1-3v4c1-2 1-3 1-5h2z" class="e"></path><path d="M317 289v-6h2 4v1l-1 3h-1l1 1v1h-5z" class="h"></path><path d="M310 283h6v6h-6v-6z" class="j"></path><path d="M296 283c4-1 9-1 13-1l-2 1h0-2-2-2c1 2 0 4 0 6h0l-1-1c-1 1-2 1-3 1h-3v-1 1c-1 1-2 1-3 1h-1c-1 0-2 0-4 1s-4 1-6 1h-1-1l1 1v1h0-1v-2l1-1v-1l-1-2 2-1-1-1 1-1c2 1 3 0 5-1h6 0l2-1h3z" class="S"></path><path d="M288 286v-1h2c1 1 1 1 2 3h-1c-1 0-2-1-3-2z" class="N"></path><path d="M301 283c1 2 0 4 0 6h0l-1-1c-1 1-2 1-3 1 1-2 1-2 1-4v-1l1 1 1-1h0l1-1z" class="h"></path><path d="M291 284h1v1h-2-2v1 1h-2c0 1 0 2-1 2-1-1 0-2-1-2l-2 1h-1v1 1h-2l-1-2 2-1-1-1 1-1c2 1 3 0 5-1h6z" class="C"></path><path d="M340 282c0-3-1-1-2-3h0c4 0 8 1 12 3-1 1-1 1-3 1v1h1 3l1 1h1c1 0 1 1 2 1v2c-1 0-1 1-1 1v1 1l-1 1v3 1l-1 1v1l-1-1c-1 0-2 0-3-1-1 0-2 0-3-1-3 0-5 0-7-1l-6-1h-9-2l2-1c-1-1-2-1-3-1l-3-1h-6 0c1-1 3-1 5-1h1 5 2c1 0 2 0 3-1v-4l-1-1h0v-1h2c4 1 7 1 10 1s5 1 8 1v-1l-6-1z" class="F"></path><path d="M339 290h1v-2h0c1-1 1-2 1-3h1 0 0v3c0 1 0 2-1 2-1 1-2 1-3 1l1-1z" class="H"></path><path d="M335 284h5c0 1-1 3-1 4v2l-1 1-1-1h-1c0-2-1-4-1-6z" class="D"></path><path d="M337 290v-3l1-1c1 1 1 1 1 2v2l-1 1-1-1z" class="d"></path><path d="M320 291h6 8l-2 2h-9-2l2-1c-1-1-2-1-3-1z" class="k"></path><path d="M320 291h6 1v1c-1 1-3 1-4 1h-2l2-1c-1-1-2-1-3-1z" class="O"></path><path d="M327 284c1-1 2 0 3 0h1 1 1 1 1c0 2 1 4 1 6l-12-1c1 0 2 0 3-1v-4z" class="g"></path><path d="M340 292v-1h2c2 1 3 1 5 1h1v-2h0c0-1 0-2 1-3h0v-1h-2l-1 1v-1l1-1h3c1 1 2 1 3 1v-1c1 0 1 1 2 1v2c-1 0-1 1-1 1v1 1l-1 1v3 1l-1 1v1l-1-1c-1 0-2 0-3-1-1 0-2 0-3-1-3 0-5 0-7-1l-6-1 2-2 6 1z" class="B"></path><path d="M354 289c-1 0-1 0-1-1h0 2c-1 0-1 1-1 1z" class="G"></path><path d="M350 287h1v5h0l-1 1-1-1c0-2 1-3 1-5z" class="C"></path><path d="M334 291l6 1 10 2c1 0 2 0 3 1h-1c-1 0-3-1-4-1h-3c-3 0-5-1-7 0l-6-1 2-2z" class="S"></path><path d="M301 372c1 0 1-1 1-2l1-1h0c0 1 0 2 1 3h0c2-1 2-2 3-3 1 0 1 1 1 2h4v-2h1 1c1 1 0 1 1 2h1l1-2h0c0 1 1 1 1 2h0 2 1v1 1c0 1 1 1 2 1l2 2v1h-3 0 7l6 1h-1-1c1 1 2 1 3 2h1 1c3 1 7 2 8 4l1 1c-1 0-1 1-1 1-1 1-1 2 0 3v2l2 2v2c1-1 1-1 2-1 0 1 1 2 1 3l-1 1c-1 0-1 1-2 1v1 1h-1v1h-2l-1-1h-4v1h-3l-9-1h-4-7-14l-10 1-5 1v-1l-1-1s0-1-1-1v-1l-2-1v-1-1-2-1h-1c-1-1-1-1-1-2v-2h1l-1-2 1-3-1-1v-2h-1v-2h0c2-1 5 1 7 0h1c-1 0 0 0-1-1l6-4h0l2-1c1 0 2-1 2-1h3z" class="j"></path><path d="M296 373c1 0 2-1 2-1h3c-2 3-5 3-7 4-1 1-2 1-2 1-1 1-1 1 0 1l-3 1c-1 0 0 0-1-1l6-4h0l2-1z" class="Q"></path><defs><linearGradient id="m" x1="287.647" y1="378.099" x2="297.935" y2="380.529" xlink:href="#B"><stop offset="0" stop-color="#343534"></stop><stop offset="1" stop-color="#595559"></stop></linearGradient></defs><path fill="url(#m)" d="M292 378l8-1v2l-5 1-3 1c-2 0-2 0-3 1h-2c0-1 0-1-1-1h0v-1c1 0 2 0 2-1h1l3-1z"></path><path d="M322 377h0 7c-1 0-3 0-4 1l1 1h-4-2c-4 0-7-1-11-1-3 0-6 1-9 1v-2h5 17z" class="n"></path><path d="M322 377h7c-1 0-3 0-4 1l1 1h-4-2c-1-1-2-1-3-2h5z" class="U"></path><path d="M300 379c3 0 6-1 9-1 4 0 7 1 11 1h2c1 0 2 0 3 1s1 0 2 1v3 1c1 0 1 0 2 1-1 0-4 0-4 1-2 0-5 1-6 0 1-1 4 0 6-1l-1-1v-3c1 0 1 0 0-1s-3-1-5-1h-2-1-1v-1c-1 0-2 0-3 1h-2-2c-1 0-1 0-2-1-1 1-2 1-3 1s-2 0-4 1v2h1c-1 1-1 1-2 1s-1 0-2 1h-2-2l2-1h-1v-1c1-1 1-2 2-3l5-1z" class="K"></path><path d="M329 377l6 1h-1-1c1 1 2 1 3 2h1v2l1 1-1 1c1 1 0 1 1 2h1v1h-4-2c-1-1-1-1-2 0h0-4-2 0c0-1 3-1 4-1-1-1-1-1-2-1v-1-3c-1-1-1 0-2-1s-2-1-3-1h4l-1-1c1-1 3-1 4-1z" class="f"></path><path d="M322 379h4 1l2 2v1h1v1h0c-1 0-1 0-2 1h0-1v-3c-1-1-1 0-2-1s-2-1-3-1z" class="l"></path><path d="M329 377l6 1h-1-1c1 1 2 1 3 2h1v2l1 1-1 1c1 1 0 1 1 2h1v1h-4-2c1 0 2 0 2-1 0 0 0-1-1-1v-1l1-1c-1-1-1-1-3-1v-2l-3-1h-2-1l-1-1c1-1 3-1 4-1z" class="R"></path><path d="M329 377l6 1h-1-1c1 1 2 1 3 2-2 0-5-1-7-1h-2-1l-1-1c1-1 3-1 4-1z" class="G"></path><path d="M281 379c2-1 5 1 7 0 0 1-1 1-2 1v1h0c1 0 1 0 1 1h2c1-1 1-1 3-1l3-1c-1 1-1 2-2 3v1h1l-2 1h2 2l-1 1v3h2 3l1 1-1 1c0 1 1 1 2 1h-4l-6 1c-1 1-3 1-4 1-2 0-3 1-4 2h0v-2-1h-1c-1-1-1-1-1-2v-2h1l-1-2 1-3-1-1v-2h-1v-2h0z" class="Z"></path><path d="M297 389h3l1 1-1 1c0 1 1 1 2 1h-4-2v-1c1 0 1-1 2-1h2-3v-1z" class="K"></path><path d="M288 394h-1c0-2 0-3 1-4h0 1 2 0v1h-1l-1 1 1 1h2c-1 1-3 1-4 1z" class="N"></path><path d="M292 385h2 2l-1 1v3h2v1h-4-2 0c0-1 0-3 1-4v-1z" class="O"></path><path d="M292 386l2 2s-1 1-1 2h-2 0c0-1 0-3 1-4z" class="Q"></path><path d="M292 381l3-1c-1 1-1 2-2 3v1h1l-2 1v1c-1 1-1 3-1 4h-2-1 0c-1 1-1 2-1 4h1c-2 0-3 1-4 2h0v-2h0c1-1 2-2 2-3s0-2 1-2c0-2 0-3 1-4 0-1 0-2 1-3s1-1 3-1z" class="k"></path><path d="M289 382c1-1 1-1 3-1-1 1-2 3-3 5l-1-1c0-1 0-2 1-3z" class="c"></path><path d="M288 385l1 1c0 1-1 2-1 4-1 1-1 2-1 4h1c-2 0-3 1-4 2h0v-2h0c1-1 2-2 2-3s0-2 1-2c0-2 0-3 1-4z" class="g"></path><path d="M281 379c2-1 5 1 7 0 0 1-1 1-2 1v1h0c1 0 1 0 1 1h2c-1 1-1 2-1 3-1 1-1 2-1 4-1 0-1 1-1 2s-1 2-2 3h0v-1h-1c-1-1-1-1-1-2v-2h1l-1-2 1-3-1-1v-2h-1v-2h0z" class="D"></path><path d="M281 379h0c1 0 3 1 3 2l2 2v1l-1 2-2-2-1-1v-2h-1v-2z" class="e"></path><path d="M286 384v2c0 1 0 2 1 3-1 0-1 1-1 2s-1 2-2 3h0v-1h-1c-1-1-1-1-1-2v-2h1l-1-2 1-3 2 2 1-2z" class="H"></path><path d="M283 384l2 2h0c0 2 0 2-1 3h-1l-1-2 1-3z" class="R"></path><path d="M303 380c1 0 2 0 3-1 1 1 1 1 2 1h2 2c1-1 2-1 3-1v1h1 1 2c2 0 4 0 5 1s1 1 0 1v3l1 1c-2 1-5 0-6 1 1 1 4 0 6 0h0 2c0 1 0 3-1 4h-2-13-4l-5 1c-1 0-2 0-2-1l1-1-1-1h-3-2v-3l1-1c1-1 1-1 2-1s1 0 2-1h-1v-2c2-1 3-1 4-1z" class="V"></path><path d="M306 388l1 1c-1 1-3 0-4 2h0 4l-5 1c-1 0-2 0-2-1l1-1-1-1h2l4-1z" class="O"></path><path d="M315 380h1 1 2l-1 5c-1 1-2 0-3 0v-5z" class="T"></path><path d="M325 387h2c0 1 0 3-1 4h-2-13c3-1 9 0 12 0l1-3s1 0 1-1z" class="L"></path><path d="M303 380c1 0 2 0 3-1 1 1 1 1 2 1h2 2c1-1 2-1 3-1v1 5 3l-1 1c-1-1-1-2-2-2-2 1-3 1-4 1h-2 0l-4 1h-2-3-2v-3l1-1c1-1 1-1 2-1s1 0 2-1h-1v-2c2-1 3-1 4-1z" class="h"></path><path d="M298 384c1 0 1 0 2-1h-1v-2c2-1 3-1 4-1l-1 1 1 1h3l-1 1c-1 0-2-1-3 1v5h-2-3-2v-3l1-1c1-1 1-1 2-1z" class="T"></path><path d="M296 385c1-1 1-1 2-1l2 2h-1-4l1-1z" class="V"></path><path d="M337 380h1c3 1 7 2 8 4l1 1c-1 0-1 1-1 1-1 1-1 2 0 3v2l2 2v2c1-1 1-1 2-1 0 1 1 2 1 3l-1 1c-1 0-1 1-2 1v1 1h-1v1h-2l-1-1h-4v1h-3l-9-1h-4-7-14l-10 1-5 1v-1l-1-1s0-1-1-1v-1l-2-1v-1-1h0c1-1 2-2 4-2 1 0 3 0 4-1l6-1h4l5-1h4 13 2c1-1 1-3 1-4h4 0c1-1 1-1 2 0h2 4v-1h-1c-1-1 0-1-1-2l1-1-1-1v-2z" class="I"></path><path d="M347 401l-2-1 1-1h1l1 1v1h-1z" class="U"></path><path d="M344 393h4v2c-1 0-3-1-5-2h1z" class="C"></path><path d="M338 391c1 0 2 0 3 1h2l1 1h-1l-9-1h3l1-1z" class="D"></path><path d="M334 400v-1-1-1l1 1h1v-1h1v2h1v-1h1v1c1 0 1-1 2-1 0 0 1 0 2 1l-1 1c1 0 2 0 2 1h-4c-1-1-2 0-4-1h-2z" class="G"></path><path d="M326 393c7 0 14 1 21 3-3 1-5 0-8 0-3-1-7-1-11-1h1v-2h-3 0z" class="P"></path><path d="M331 387c2 0 3 0 4 1h0v3h1 2l-1 1h-3-9l-1-1h2c1-1 1-3 1-4h4z" class="f"></path><path d="M331 387l1 1c-1 1-3 2-5 3h0-1c1-1 1-3 1-4h4z" class="l"></path><path d="M308 397h2l1 1h1c1-1 1-1 2-1h1v1h1v-1h1v1c1-1 1-1 2-1v1h1l1-1c1 0 1 0 1 1l1-1c1 0 1 0 1 1 1 0 1-1 2-1v2l1-1v-1h1v2h1v-1h1l1 1h0l1-2h0v1l2 2h2c2 1 3 0 4 1v1h-3l-9-1c2 0 5 1 6 0v-1h-3c-1 0-2 0-3-1h-1-2c-4 0-7-1-10-1s-6 1-8 1c-1-1-2 0-3 0h0-2l-1-1 1-1c1 0 1 1 2 1v-1h4z" class="J"></path><defs><linearGradient id="n" x1="312.546" y1="402.476" x2="311.906" y2="398.001" xlink:href="#B"><stop offset="0" stop-color="#b7b4b7"></stop><stop offset="1" stop-color="#e0e0df"></stop></linearGradient></defs><path fill="url(#n)" d="M302 399h2 0c1 0 2-1 3 0 2 0 5-1 8-1s6 1 10 1h2 1c1 1 2 1 3 1h3v1c-1 1-4 0-6 0h-4-7-14c-1 0-5 1-7 0l1-1h1 1c1 0 1-1 2-1h1z"></path><path d="M337 380h1c3 1 7 2 8 4l1 1c-1 0-1 1-1 1-1 1-1 2 0 3v2l2 2h-4l-1-1h-2c-1-1-2-1-3-1h-2-1v-3h0c-1-1-2-1-4-1h0c1-1 1-1 2 0h2 4v-1h-1c-1-1 0-1-1-2l1-1-1-1v-2z" class="G"></path><path d="M346 389v2l2 2h-4l-1-1v-1h1 1c0-1 0-1 1-2z" class="B"></path><path d="M335 388h4l3 2h1 0-2v2c-1-1-2-1-3-1h-2-1v-3z" class="J"></path><path d="M342 390h1 0-2v2c-1-1-2-1-3-1h-2v-1c2-1 4 1 6 0z" class="C"></path><path d="M337 380h1v1 1h0c1 0 1 1 2 1 1 1 2 1 3 2v1c0 1 1 1 0 2h-4 0-4 0c-1-1-2-1-4-1h0c1-1 1-1 2 0h2 4v-1h-1c-1-1 0-1-1-2l1-1-1-1v-2z" class="m"></path><path d="M311 391h13l1 1s1 0 1 1h0 3v2h-1 0c-4 1-8 0-12 0l-1 2h-1c-1 0-1 0-2 1h-1l-1-1h-2-4v1c-1 0-1-1-2-1l-1 1 1 1h-1c-1 0-1 1-2 1h-1-1l-1 1c2 1 6 0 7 0l-10 1-5 1v-1l-1-1s0-1-1-1v-1l-2-1v-1-1h0c1-1 2-2 4-2 1 0 3 0 4-1l6-1h4l5-1h4z" class="G"></path><path d="M287 401c2-1 2 0 4-1 1 0 1 0 2-1l1-1v1 1h1v-2h1v1h1s0-1 1-1v1h1 0l1-1v-1l1 1 1 1h-1c-1 0-1 1-2 1h-1-1l-1 1c2 1 6 0 7 0l-10 1-5 1v-1l-1-1z" class="e"></path><path d="M300 395c-5 0-11 2-16 1 6-2 12-3 18-3 8-1 16-1 24 0h0 3v2h-1 0c-4 1-8 0-12 0l-1 2h-1c-1 0-1 0-2 1h-1l-1-1h-2 1l1-1h0l-10-1h0z" class="h"></path><path d="M300 395h7 22-1 0c-4 1-8 0-12 0l-1 2h-1c-1 0-1 0-2 1h-1l-1-1h-2 1l1-1h0l-10-1h0z" class="F"></path><path d="M309 303h9 2 0 5l1 1c-3 0-5-1-8 1 2 0 3 0 4 1h-1v1 1h-2c0 1 2 2 3 3 0 0 1 0 2 1l1-1 1 1c0 1 0 1 1 1v2 1h1l1-1c1 0 2 1 4 2v5l2 18h1l1-1v1c1 0 2-1 3-1 0 1 0 1 1 2s2 0 3 1c-2 1-5 1-6 3l1 4h-2 0l1 1h0c1 2 3 4 3 7h0v1c-1 0-2 0-4-1v3h-2l-1-1c0 1-1 1-1 2h-2-2c-1-1-3-1-5-1v-1h0c-1 0-1 1-2 1l-1-1-1 1c-1 0-2 0-3-1v-1h-1v1c-1 0-1 0-2 1-1 0-2 0-3-1h0c-1 1-2 1-3 1h-2-3 0v-3l-3-1c-1 0-1-1-2-1h-1l1-1v-6l2-17 1-7v-3c0-1 0-2 1-4-1-1-1-2-2-4h-1 0c-1-1-2-2-3-2l4-2h0l1-1c0-1-1-1 0-2h-2v-2c1 0 1 0 2-1h1 2 1 4z" class="b"></path><path d="M330 329c0 2 1 4 0 7v-1c0-1 0-3-1-4l-2-2 1-1c1 0 1 1 2 1z" class="W"></path><path d="M330 349v-3c1 1 1 3 1 5 1 0 1 1 1 1v1c1 1 2 0 3 1l-1 1c-2-1-4-1-6-1v-1h1l1-1v-3z" class="I"></path><path d="M335 340h1l1-1v1l-1 1c1 2 2 1 1 4v4h-1 0l-1-9z" class="E"></path><path d="M331 340v1c0 1 1 1 1 2 1 2 0 5 2 6l1 1 1-1h0v4h-3c0-2-1-2-1-3-1-3-1-7-1-10z" class="c"></path><path d="M327 333v-1c3 5 2 11 3 17v3l-1 1v-2l-1-7v-5c0-2-1-4-1-6z" class="o"></path><path d="M328 339v5l1 7-1-2h-1 0c-2-1-5-1-7-1v-1c2-1 4-2 6-2h1l-2-2h1l1 1c0-2 0-4 1-5z" class="B"></path><path d="M328 339v5c0 1 0 2-1 3h-3 0c0-1 0-1 1-2h1 1l-2-2h1l1 1c0-2 0-4 1-5z" class="I"></path><path d="M328 354c2 0 4 0 6 1 1 0 2 0 3 1-1 0-1 0-1 1h1v3h-2l-1-1c0 1-1 1-1 2h-2-2c-1-1-3-1-5-1v-1h0c-1 0-1 1-2 1 0-2-1-3 1-4 1 0 1-1 2-1h1c1 0 2 1 2 0v-1z" class="K"></path><path d="M331 361v-1-3h0v-1l1 1 1-1c1 1 2 1 3 1h1v3h-2l-1-1c0 1-1 1-1 2h-2z" class="I"></path><defs><linearGradient id="o" x1="328.41" y1="345.858" x2="338.437" y2="325.422" xlink:href="#B"><stop offset="0" stop-color="#7a7879"></stop><stop offset="1" stop-color="#b5b5b5"></stop></linearGradient></defs><path fill="url(#o)" d="M329 324c1 0 1 1 2 2l1-3 1-1 2 18 1 9-1 1-1-1c-2-1-1-4-2-6 0-1-1-1-1-2v-1l-1-4c1-3 0-5 0-7l-1-5z"></path><path d="M326 329v1c0 1 1 1 1 2v1c0 2 1 4 1 6-1 1-1 3-1 5l-1-1h-1l2 2h-1c-2 0-4 1-6 2v1h0-1v1c-2 0-7-1-9-1h-1-1v-2c1 0 0-1 0-1 0-1 0-2 1-2-1 0-1-1-2-1v-4l1-4v-1c1 0 2 0 3-1h4 4 3c1 0 1-2 1-2h1l2-1z" class="r"></path><path d="M326 329v1c0 1 1 1 1 2v1h0c-4-1-8-1-12-1h4 3c1 0 1-2 1-2h1l2-1z" class="f"></path><path d="M309 343c0-2 1-4 1-5v-1l1-1c1-1 2-1 4-1h1v1l-1 1c0 1 0 2 1 3h6v-4h1c1 1 0 4 1 6h0c0 1 0 1 1 1-5-1-11-1-16 0z" class="B"></path><path d="M309 343c5-1 11-1 16 0h0l2 2h-1c-2 0-4 1-6 2v1h0-1v1c-2 0-7-1-9-1h-1-1v-2c1 0 0-1 0-1 0-1 0-2 1-2h0z" class="f"></path><path d="M317 345c3 0 6-1 9 0-2 0-4 1-6 2v1h0l-1-1c0-1-1-1-2-1v-1z" class="F"></path><path d="M308 345c3 0 6-1 9 0v1c1 0 2 0 2 1l1 1h-1v1c-2 0-7-1-9-1h-1-1v-2c1 0 0-1 0-1z" class="W"></path><path d="M310 348c1-1 1-1 3-1 0 1 0 1 1 1s1 0 2-1h2c0 1 0 1 1 1v1c-2 0-7-1-9-1z" class="G"></path><path d="M307 333l1 1-1 4v4c1 0 1 1 2 1-1 0-1 1-1 2 0 0 1 1 0 1v2h1 1c2 0 7 1 9 1v-1h1 0c2 0 5 0 7 1h0 1l1 2v2h-1v1 1c0 1-1 0-2 0h-1c-1 0-1 1-2 1-2 1-1 2-1 4l-1-1-1 1c-1 0-2 0-3-1v-1h-1v1c-1 0-1 0-2 1-1 0-2 0-3-1h0c-1 1-2 1-3 1h-2-3 0v-3l-3-1c-1 0-1-1-2-1h-1l1-1v-6c1 1 1 1 1 2 1 0 1 0 2-1 1 0 1-2 1-3v-1h1l1-8 1-1v3c0-2 0-4 2-6z" class="T"></path><path d="M303 357h0l1 1v-1c2-1 5 0 7-1h0c0 1 0 0 1 1h0c1-1 1-1 2-1h0v4c-1 0-2 0-3-1h0c-1 1-2 1-3 1h-2-3 0v-3z" class="N"></path><path d="M320 348c2 0 5 0 7 1h0 1l1 2v2h-1-15c-2-1-4 0-7-1v-3l1-1h1 1 1c2 0 7 1 9 1v-1h1 0z" class="r"></path><defs><linearGradient id="p" x1="300.103" y1="346.075" x2="307.211" y2="343.053" xlink:href="#B"><stop offset="0" stop-color="#222622"></stop><stop offset="1" stop-color="#3d383c"></stop></linearGradient></defs><path fill="url(#p)" d="M307 333l1 1-1 4v4c1 0 1 1 2 1-1 0-1 1-1 2 0 0 1 1 0 1v2h1-1-1l-1 1v3c3 1 5 0 7 1-4 0-8 0-11 1l-4 1h-1l1-1v-6c1 1 1 1 1 2 1 0 1 0 2-1 1 0 1-2 1-3v-1h1l1-8 1-1v3c0-2 0-4 2-6z"></path><path d="M298 348c1 1 1 1 1 2 1 0 1 0 2-1 1 0 1-2 1-3v-1h1c0 3 0 5-2 8l1 1-4 1h-1l1-1v-6z" class="f"></path><path d="M307 333l1 1-1 4v4c1 0 1 1 2 1-1 0-1 1-1 2 0 0 1 1 0 1v2h1-1-1l-1 1h0c0 1 0 2-1 3h0c-1-2-1-3 0-4v-9c0-2 0-4 2-6z" class="i"></path><path d="M306 349c0-3 1-8 1-11v4c1 0 1 1 2 1-1 0-1 1-1 2 0 0 1 1 0 1v2h1-1-1l-1 1h0z" class="B"></path><path d="M309 303h9 2 0 5l1 1c-3 0-5-1-8 1 2 0 3 0 4 1h-1v1 1h-2c0 1 2 2 3 3 0 0 1 0 2 1l1-1 1 1c0 1 0 1 1 1v2 1h1l1-1c1 0 2 1 4 2v5l-1 1-1 3c-1-1-1-2-2-2l1 5c-1 0-1-1-2-1l-1 1-1 1v-1l-2 1h-1s0 2-1 2h-3-4-4c-1 1-2 1-3 1v1l-1-1c-2 2-2 4-2 6v-3l-1 1-1 8h-1v1c0 1 0 3-1 3-1 1-1 1-2 1 0-1 0-1-1-2l2-17 1-7v-3c0-1 0-2 1-4-1-1-1-2-2-4h-1 0c-1-1-2-2-3-2l4-2h0l1-1c0-1-1-1 0-2h-2v-2c1 0 1 0 2-1h1 2 1 4z" class="C"></path><path d="M308 323h1c1 0 2 0 3 1s1 1 1 2h-7l2-3z" class="i"></path><path d="M307 333l-1-1c0-1 0-2 1-3h3c1 1 1 2 1 3h0c-1 1-2 1-3 1v1l-1-1z" class="M"></path><path d="M315 329h2v-1h1c0 2 0 2 1 4h-4-4 0c0-1 0-2-1-3h2c2 0 1 0 2 1v-1h1z" class="Z"></path><defs><linearGradient id="q" x1="293.272" y1="343.95" x2="310.016" y2="321.015" xlink:href="#B"><stop offset="0" stop-color="#a5a4a7"></stop><stop offset="1" stop-color="#d8d6d5"></stop></linearGradient></defs><path fill="url(#q)" d="M302 317v-1h2 1v-1h1l-2 22-1 8h-1v1c0 1 0 3-1 3-1 1-1 1-2 1 0-1 0-1-1-2l2-17 1-7v-3c0-1 0-2 1-4z"></path><path d="M309 303h9 2 0 5l1 1c-3 0-5-1-8 1 2 0 3 0 4 1h-1v1 1h-2l-2-1v1c-2 0-3-1-4 0v2h-1c-1-1-1-1-1-2h0c-2 1-2 6-4 7h0l1-5-1-1c0 1-1 2-1 3l-1 1h0 0v1h0 1v1h-1v1h-1-2v1c-1-1-1-2-2-4h-1 0c-1-1-2-2-3-2l4-2h0l1-1c0-1-1-1 0-2h-2v-2c1 0 1 0 2-1h1 2 1 4z" class="q"></path><path d="M296 311l4-2h0l1-1c1 1 2 1 3 1h1 0v4 1h0 1v1h-1v1h-1-2v1c-1-1-1-2-2-4h-1 0c-1-1-2-2-3-2z" class="D"></path><path d="M296 311l4-2h0l1-1c1 1 2 1 3 1 0 1 0 2-1 2-1 1-3 1-4 2h0c-1-1-2-2-3-2z" class="F"></path><path d="M309 303h9 2 0 5l1 1c-3 0-5-1-8 1-1 0-5 1-6 0-2 0-5 0-7 1h-4-2v-2c1 0 1 0 2-1h1 2 1 4z" class="C"></path><path d="M320 303h5l1 1c-3 0-5-1-8 1-1 0-5 1-6 0h0 2v-2h6z" class="B"></path><path d="M317 308v-1l2 1c0 1 2 2 3 3 0 0 1 0 2 1l1-1 1 1c0 1 0 1 1 1v2 1h1l1-1c1 0 2 1 4 2v5l-1 1-1 3c-1-1-1-2-2-2l1 5c-1 0-1-1-2-1l-1 1-1 1v-1l-2 1h-1s0 2-1 2h-3c-1-2-1-2-1-4h-1v1h-2v-2l1-1h-3c0-1 0-1-1-2s-2-1-3-1h-1c1-1 1-1 1-2 0 0 0-1 1-1h2c0-1 0-2-1-4h0c0-1 0-2 1-3 0-2 1-3 3-4l2-1z" class="F"></path><path d="M326 329c-1-1-1-1-1-2h0 3v1l-1 1-1 1v-1z" class="I"></path><path d="M319 326h3c1 0 2 0 2 1v1c-1 1-2 0-3 1v1l-1-1c0-1 0-1-1-2h-1l1-1z" class="Y"></path><path d="M318 326h1l-1 1h1c1 1 1 1 1 2h-2v-1h-1v1h-2v-2l1-1h2z" class="B"></path><path d="M318 328v1h2l1 1v-1h2v1s0 2-1 2h-3c-1-2-1-2-1-4z" class="p"></path><path d="M320 322c2 1 2 2 4 3h2l1 1h0c-1 1-3 0-5 0h-3-1 1c0-1 0-1 1-2h0v-2z" class="e"></path><path d="M328 316l1-1c1 0 2 1 4 2v5l-1 1-1 3c-1-1-1-2-2-2 0-3 0-6-1-8z" class="O"></path><path d="M324 315c1 1 2 2 2 3l1 3h-7c1 1 3 1 5 2 1 1 0 0 0 1l1 1h-2c-2-1-2-2-4-3v2h0c-1 1-1 1-1 2h-1-2-3c0-1 0-1-1-2s-2-1-3-1h-1c1-1 1-1 1-2 0 0 0-1 1-1h2 14l-2-5z" class="c"></path><path d="M309 323c3-1 6 0 9-1h2v2h0c-1 1-1 1-1 2h-1-2-3c0-1 0-1-1-2s-2-1-3-1z" class="Q"></path><path d="M317 308v-1l2 1c0 1 2 2 3 3l2 4h0l2 5h-14c0-1 0-2-1-4h0c0-1 0-2 1-3 0-2 1-3 3-4l2-1z" class="E"></path><path d="M312 313v1c0 1-1 1 0 2l1-1 1 1h0c1-1 1-1 2-1-1-1-1-1-1-2l1-1h1v1h1c1 1 2 1 3 2l-1 1h0c-1-1-1-1-2-1l-1 1h-3v2h-1 0c0-1-1-1-1-1l-1-1h0c0-1 0-2 1-3z" class="G"></path><defs><linearGradient id="r" x1="281.423" y1="558.621" x2="267.528" y2="574.414" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#r)" d="M265 551c0-1 1 0 2-1v4l14-1c1 1 3 1 4 1h2c1 0 1 0 1 1h3c-1 3-1 7 0 10l-1 1h-4c1 0 4-1 4 0v2l-1 1c-1 0-2 0-2 1h-1c-1 1-1 2-2 3v3 2l-3 3v1h-1l-3 4c0-1-1-2-1-2l-3 6c-1 2-3 5-4 8l-2 4v5l2-1v3 3c0 1 0 2-1 3h1-1l-3 1-1-1v-1h-2c-2 0-4 1-6 1v1h-1l-2-1h-2-2l1-1v-2h0c-1 1-2 0-3 0l-1-1v-2h0c1-1 0-2 0-3l-2-1v-1h2 0l-1-1v-4-2l-1-1c-1-1-1-1-1-3v-1h-1c-2 0-5 0-7 1l-1-1-1-1h0l-1 1c-1 1-2 0-3 0h3c-1-1-1-2-2-2 1-1 1-2 1-3h0v-1c0-1 1-1 1-2v-1-6h1l1-1h4v-1-1s1 0 1-1h-1 1l-1-1h0c-1-1-1-2-2-2 1-1 1-2 2-2 0 0 1 0 2-1l-1-1c0-1 0-1 1-2 1 1 0 5 1 6 1 0 3 0 4-1h1c1-1 0-1 0-2s1-2 1-4l-2 1v-1-1h1c1-2 0-2 1-4v2h8 6s1-1 2-1h2c0-1 0-2-1-3v-3l1-2z"></path><path d="M264 564h1l1 1c0 1 0 1-1 1s-1-1-1-1v-1z" class="a"></path><path d="M275 570h2c1-1 0-1 2-1v3s-1 0-1 1v-1c0-1-1-1-1-2h-1-1z" class="B"></path><path d="M261 571c1 1 2 1 3 1 0 1 1 2 2 2v1l-3 1c-1-1-1-1-2-1l1-1c-1-1-1-2-1-3z" class="E"></path><path d="M267 572c1-2 0-3 1-4 2 0 5 1 7 1 0 3-1 5-2 7l-1 1v-3c0-2 1-3 0-4s-2-1-2-1h-1-1v3h-1z" class="S"></path><path d="M280 575c2-2 2-5 3-6h1v1 3h0v3 2l-3 3v1h-1v-1l2-4c-1 0-1-1-2-2z" class="G"></path><path d="M282 577c1-1 1-5 2-7v3h0v3 2l-3 3v1h-1v-1l2-4z" class="E"></path><path d="M280 575c1 1 1 2 2 2l-2 4v1l-3 4c0-1-1-2-1-2v-1c0-1 1-3 1-4l1-2h0 1l1-2z" class="R"></path><path d="M276 583c1-1 2-3 2-4 1 0 2 1 2 2v1l-3 4c0-1-1-2-1-2v-1z" class="k"></path><path d="M268 572v-3h1 1s1 0 2 1 0 2 0 4v3l1-1c1-2 2-4 2-7v1h1 1c0 1 1 1 1 2v1 4l-1 2c0 1-1 3-1 4v1l-3 6c-1 2-3 5-4 8l-2 4 1-25c-1-2 0-3 0-5z" class="Z"></path><path d="M268 572v-3h1 1c0 4-1 8-1 11 0 2 0 5-1 7h0v-10c-1-2 0-3 0-5z" class="O"></path><defs><linearGradient id="s" x1="273.51" y1="570.341" x2="266.758" y2="584.756" xlink:href="#B"><stop offset="0" stop-color="#6e6b6d"></stop><stop offset="1" stop-color="#858685"></stop></linearGradient></defs><path fill="url(#s)" d="M270 569s1 0 2 1 0 2 0 4v3c0 2 0 5-1 7 0 2-1 4-2 6v-10c0-3 1-7 1-11z"></path><path d="M275 569v1h1 1c0 1 1 1 1 2v1 4l-1 2c0 1-1 3-1 4v1l-3 6v-1c-1-1 0-3 1-4l-1-1c-1 2-2 6-4 7v-1c1-2 2-4 2-6 1-2 1-5 1-7l1-1c1-2 2-4 2-7z" class="W"></path><path d="M276 578h0c0-2-1-3-1-4l1-1v2h1v-3h1v1 4l-1 2v-1h-1z" class="c"></path><path d="M276 578h1v1c0 1-1 3-1 4v1l-3 6v-1c-1-1 0-3 1-4l-1-1 3-6z" class="g"></path><path d="M246 562c1-2 0-2 1-4v2h8 6l-1 1h0c2 1 5 0 6 1v1h-3-1s-1 0-1 1c0 0-1 1-1 2v-1c-1 1-1 2-2 2v2h5v1h-2l1 1h-1c0 1 0 2 1 3l-1 1c1 0 1 0 2 1v1c-1-2-4-1-5-1s-2 0-3-1v-1c0-1-1-1-1-2v2h-1c-1-1 0-1-1-1h-2l1-1h1l-1-1h-5 0-1v-1l1-1c1-1 0-1 0-2s1-2 1-4l-2 1v-1-1h1z" class="Q"></path><path d="M255 563h3 2v2c-1 1-1 2-2 2v2h-3v-1-5z" class="G"></path><path d="M258 563h2v2c-1 1-1 2-2 2v-4z" class="E"></path><path d="M255 569h-3c-1-1-2 0-2 0h-1c-1-2 0-2 0-3l1-1h0c1 0 1-1 2-2h3v5 1z" class="O"></path><path d="M254 572v-1h2c1-1 3-1 5-1l1 1h-1c0 1 0 2 1 3l-1 1c1 0 1 0 2 1v1c-1-2-4-1-5-1s-2 0-3-1v-1c0-1-1-1-1-2z" class="B"></path><path d="M246 562c1-2 0-2 1-4v2h8 6l-1 1h0c2 1 5 0 6 1v1h-3-1s-1 0-1 1c0 0-1 1-1 2v-1-2h-2l1-1h0c-3-1-9-1-13 0z" class="H"></path><path d="M266 574h1v-2h0 1c0 2-1 3 0 5l-1 25v5l2-1v3 3c0 1 0 2-1 3h1-1l-3 1-1-1v-1-1c1-2 1-3 0-5v-1h-2v-1c1-1 2 0 3-1l-1-1c-1-1-1-1-1-2s1-1 1-2c0-2-1-4-1-5-1 0-2 0-2-1l1-1h-1 1l-1-1 2-2c-2-1-5 1-7-1h0 0v-9-1h-2c1-1 1-2 2-3h2c1 0 4-1 5 1v-1l3-1v-1z" class="X"></path><path d="M267 607l2-1v3 3c0 1 0 2-1 3h1-1c-1-2-1-5-1-8z" class="Z"></path><path d="M258 576c1 0 4-1 5 1-2 1-4 1-7 2h0-2c1-1 1-2 2-3h2z" class="V"></path><path d="M260 580v-1h5v1h1c0 2-2 2-1 4h0c-1 1-1 2-2 3 0 1 0 2 1 2l1 1h1c0 1 0 2-1 3h-3-1 1l-1-1 2-2c-2-1-5 1-7-1h0c1 0 2 0 3-1 0-2-1-4 0-6 0-1 0-1 1-1v-1z" class="C"></path><path d="M260 587c0-1 0-2 1-3h2v1c-1 1-1 2-2 3 0 0-1 0-1-1z" class="B"></path><path d="M260 580v-1h5v1c0 1-1 1-1 3h-1v-1c-1-1-1-1-2-1l-1-1z" class="q"></path><path d="M256 589c1 0 2 0 3-1 0-2-1-4 0-6 0-1 0-1 1-1v2h3c-1 1-1 1-2 1-1 1-1 2-1 3v2c1 1 4 0 5 2v2h-3-1 1l-1-1 2-2c-2-1-5 1-7-1h0z" class="g"></path><path d="M265 551c0-1 1 0 2-1v4l14-1c1 1 3 1 4 1h2c1 0 1 0 1 1h3c-1 3-1 7 0 10l-1 1h-4l-11-1h-9 0l-1-1h-1l-1-1h3v-1c-1-1-4 0-6-1h0l1-1s1-1 2-1h2c0-1 0-2-1-3v-3l1-2z" class="T"></path><path d="M278 564h-1v-4c-1 2-1 3-1 4h-3c0-2-1-7 0-8h2 1 2c0 1 1 6 0 7v1zm-13-13c0-1 1 0 2-1v4 1c1 1 1 1 2 1h2c1 0 1 0 1 1-1 1 0 6-1 7h-2c-1-2 1-7-1-8-1 2 1 8 0 8s-1 1-2 1h0l-1-1h-1l-1-1h3v-1c-1-1-4 0-6-1h0l1-1s1-1 2-1h2c0-1 0-2-1-3v-3l1-2z" class="X"></path><path d="M278 556c1-1 4-1 5-1h5 3c-1 3-1 7 0 10l-1 1h-4l-11-1h6 2s1 0 1-1h-1v-4h0v-4h-1v8c-1 1-3 0-4 0v-1c1-1 0-6 0-7z" class="m"></path><path d="M281 556h0c1 1 1 6 0 8h0-1v-4-1c0-1 0-2 1-3z" class="q"></path><path d="M283 560v-4h1c1 0 1 0 1 1v3c-1 1 1 3-1 4h-1v-4z" class="E"></path><path d="M288 556h0c2 2 0 5 1 7h0l-1 1h-1l-1-1c0-1 0-3 1-5 0 0 0-1 1-2z" class="r"></path><path d="M261 593h1l-1 1c0 1 1 1 2 1 0 1 1 3 1 5 0 1-1 1-1 2s0 1 1 2l1 1c-1 1-2 0-3 1v1h2v1c1 2 1 3 0 5v1h-2c-2 0-4 1-6 1v1h-1l-2-1h-2-2l1-1v-2h0c-1 1-2 0-3 0l-1-1v-2h0c1-1 0-2 0-3l-2-1v-1h2 0l-1-1v-4-2l-1-1c-1-1-1-1-1-3v1h11 3l2-1h2z" class="U"></path><path d="M247 598v-2h1c1 2 0 6 1 7s1 1 2 1h-2-3 0l-1-1v-4l2-1z" class="p"></path><path d="M245 599l2-1c0 1 0 4-1 6l-1-1v-4z" class="D"></path><path d="M252 597v1h1v-2h2l1 1v7h-2v1c-2 1-3 0-5 0 0 0-3 0-3 1l-2-1v-1h2 3 2c0-2-1-5 0-7h1z" class="c"></path><path d="M253 604c1-1 1-4 1-5s1-2 2-2v7h-2-1z" class="M"></path><path d="M251 604c0-2-1-5 0-7h1c0 2-1 5 0 7h1 1v1c-2 1-3 0-5 0 0 0-3 0-3 1l-2-1v-1h2 3 2z" class="P"></path><path d="M261 593h1l-1 1c0 1 1 1 2 1 0 1 1 3 1 5 0 1-1 1-1 2s0 1 1 2h-2c-2-1 0-4-1-5h-1v4 1h-1-3v-7l-1-1 1-1v1l1-1v-1c-1 0-2 1-3 1v-1h0 3l2-1h2z" class="G"></path><path d="M254 594h3l1 2c0 1 1 3 0 4h0c0 1 1 1 1 3h1v1h-1-3v-7l-1-1 1-1v1l1-1v-1c-1 0-2 1-3 1v-1h0z" class="C"></path><path d="M258 600l-1-1v-3h1c0 1 1 3 0 4h0z" class="R"></path><path d="M260 603v-4h1c1 1-1 4 1 5h2l1 1c-1 1-2 0-3 1v1h2v1c1 2 1 3 0 5v1h-2c-2 0-4 1-6 1v1h-1l-2-1h-2-2l1-1v-2h0c-1 1-2 0-3 0l-1-1v-2h0c1-1 0-2 0-3s3-1 3-1c2 0 3 1 5 0v-1h2 3 1v-1z" class="H"></path><path d="M260 607h0c1 1 1 3 2 3-2 1-2 1-4 0 1-1 1-2 2-3z" class="n"></path><path d="M256 605h2l1 1 1 1c-1 1-1 2-2 3h-2c0-1 1-2 0-3v-2z" class="C"></path><path d="M264 607v1c1 2 1 3 0 5v1h-2v-1l-1-1h-1c-1 0-4 0-5-1l1-1h2c2 1 2 1 4 0h2v-3z" class="F"></path><path d="M250 614h4l1-1v-1-1c1 1 4 1 5 1h1l1 1v1c-2 0-4 1-6 1v1h-1l-2-1h-2-2l1-1z" class="J"></path><path d="M256 604h3 1 1v1h-3-2v2c1 1 0 2 0 3l-1 1v1 1l-1 1h-4v-2h0c-1 1-2 0-3 0l-1-1v-2h0c1-1 0-2 0-3s3-1 3-1c2 0 3 1 5 0v-1h2z" class="K"></path><path d="M252 610v-1c1-1 1-1 2-1l1 1v1c-1 1-2 0-3 0z" class="Q"></path><path d="M255 609v-3l1-1v2c1 1 0 2 0 3l-1 1v1 1l-1 1h-4v-2h0v-5c0 1 0 2 1 3h1c1 0 2 1 3 0v-1z" class="Z"></path><path d="M249 605h0l1 1v1 5h0 0c-1 1-2 0-3 0l-1-1v-2h0c1-1 0-2 0-3s3-1 3-1z" class="c"></path><path d="M246 609h3v2h-3v-2z" class="p"></path><path d="M249 605h0c-1 2 0 3 0 4h-3 0c1-1 0-2 0-3s3-1 3-1z" class="g"></path><path d="M236 570c1-1 1-2 2-2 0 0 1 0 2-1l-1-1c0-1 0-1 1-2 1 1 0 5 1 6 1 0 3 0 4-1h1l-1 1v1h1 0 5l1 1h-1l-1 1h2c1 0 0 0 1 1h1v-2c0 1 1 1 1 2v1c1 1 2 1 3 1h-2c-1 1-1 2-2 3h2v1 9h0 0c2 2 5 0 7 1l-2 2 1 1h-1-2l-2 1h-3-11v-1-1h-1c-2 0-5 0-7 1l-1-1-1-1h0l-1 1c-1 1-2 0-3 0h3c-1-1-1-2-2-2 1-1 1-2 1-3h0v-1c0-1 1-1 1-2v-1-6h1l1-1h4v-1-1s1 0 1-1h-1 1l-1-1h0c-1-1-1-2-2-2z" class="K"></path><path d="M232 577h1l1-1 4 1h0v2h-1-1l1 1v1h-2v-2h-2v-1l-1-1z" class="e"></path><path d="M233 578c0-1 1-1 2-1v2h-2v-1z" class="P"></path><path d="M238 575h2v-1-1l2-1h2l1 1c1 1 1 2 1 3h-8v-1z" class="Y"></path><path d="M253 579h-1-7c0-1-1-2 0-3h3 8c-1 1-1 2-2 3h-1z" class="h"></path><path d="M232 577l1 1v1h2v2h2v-1-1c2 0 5-1 7 0v7c0 1 0 3-1 4 0 1-1 1-2 1l1 1c-2 0-5 0-7 1l-1-1-1-1h0l-1 1c-1 1-2 0-3 0h3c-1-1-1-2-2-2 1-1 1-2 1-3h0v-1c0-1 1-1 1-2v-1-6z" class="C"></path><path d="M231 587c1 0 1 0 1 1 1 1-1 1 1 2 1 0 2 0 2-1l1-1v-2h0c1 1 0 3 1 4l4 1h0l1 1c-2 0-5 0-7 1l-1-1-1-1h0l-1 1c-1 1-2 0-3 0h3c-1-1-1-2-2-2 1-1 1-2 1-3z" class="P"></path><path d="M237 580v-1c2 0 5-1 7 0v7c0 1 0 3-1 4 0 1-1 1-2 1h0c-1-1-2-2-2-3s0-2 1-2l1-1h0v-1h-1c-1 0-1 0-2-1h1c1 0 2 0 2-1s-3 0-4 0v-1-1z" class="I"></path><path d="M254 579h2v1 9h0 0c2 2 5 0 7 1l-2 2 1 1h-1-2l-2 1h-3-11v-1-1h-1l-1-1c1 0 2 0 2-1 1-1 1-3 1-4v-7h9 1z" class="K"></path><path d="M243 594l1-3h4 11c1 0 1 1 2 1-1 1-1 1-2 1l-2 1h-3-11z" class="j"></path><path d="M254 579h2v1 9h-1c-1-1-1-1-1-2l-1-1-1 3h0-1-2c-1-2-1-7-1-9h-1 0c0 1 0 2-1 2 0 1-1 1-1 1 0 1-1 2-1 3v-7h9 1z" class="R"></path><path d="M249 583v-2h0 1v2h-1z" class="C"></path><path d="M249 583h1c0 2 0 4 1 5v1h-2v-6z" class="U"></path><path d="M251 588v-7l1-1c0 2 1 6 1 6l-1 3h0-1v-1z" class="e"></path><path d="M254 581c0-1 1-1 2-1v9h-1c-1-1-1-1-1-2l-1-1s-1-4-1-6c1 1 1 1 2 1z" class="P"></path><path d="M252 580c1 1 1 1 2 1v6l-1-1s-1-4-1-6z" class="J"></path><path d="M325 186h7c1 1 2 1 3 1h1v6c0 1-1 2 0 3 2 1 5 1 8 2h1v2 12 11 4 1 3c0 1 1 2 2 2-1 0-2 0-2-1-1 2 0 5-1 6h-1 0c-1-1-2-1-3-2s-1-2-3-3v3c-1 1-1 1-1 2h0v4c-1 0-2-1-2-1v-3-1 1h-1c-1 0-2 0-2 1h1c-1 1-1 1-1 2h0-3v2h-3-7l-1 5h6c1 1 1 1 2 1s1 1 2 1h-2c-1 0-2 0-3 1h-14 0l-12 1v-3h4c0-2 0-4-1-6h0-3 0l2-1 5-1c-1-1-3-1-4-1-1-1-1-1-1-2v-1c0-1 0-1-1-1 1-1 1-2 1-3l1-1c-1-1-1-3-1-5h2l-2-1c0-1-1-2-1-3 1-2 1-3 1-5l1-1h1l1-1c1-1 0-3 0-4-1-1-1-1-2-1-1-2 1-4 1-6 0-1 0-1-1-2 1-1 1 0 2-1h1 1-1c-1-1-1-3 0-4h1c4 0 10 0 14-1l1 1h11c1-1 2 0 3-1h0c-3-1-6-1-10-2l5-1v-5c0-1 0-2 1-2-1-1-2-1-3-1z" class="T"></path><path d="M319 205v-1c0-1 0-1 1-2v2c1 0 1 0 1 1 0 0 0 1 1 1v-1l2 1v1c0 1-1 1-1 2 1 0 1 0 1 1h-1l-1 1h-1l-2 1v2c1 0 1 0 1 1h1l1 1h0c-1 0-2 0-2-1l-1-1v1c0 1-1 1-1 2h-2 0c0-1-1-1-1-1 0-2 0-3 1-5h0 2v-2c1-2 1-2 1-4z" class="i"></path><path d="M316 211l1 1c1 1 1 2 1 4-1 1-1 1-2 1 0-1-1-1-1-1 0-2 0-3 1-5z" class="E"></path><path d="M322 206v-1l2 1v1c0 1-1 1-1 2 1 0 1 0 1 1h-1l-1 1h-1v-2c0-1 1-2 2-2l-1-1z" class="Q"></path><path d="M303 198c4 0 10 0 14-1v3l-1 1c-1 1-2 1-3 1h-10 0-1c-1-1-1-3 0-4h1z" class="H"></path><path d="M303 202h-1c-1-1-1-3 0-4v1h9 4c1 0 1 1 2 1l-1 1c-1 0 0 0-1-1h-6 0 0c-1 0-4 0-5 1-1 0-1 1-1 1h0z" class="N"></path><path d="M303 202s0-1 1-1c1-1 4-1 5-1h0 0 6c1 1 0 1 1 1-1 1-2 1-3 1h-10z" class="p"></path><defs><linearGradient id="t" x1="327.117" y1="203.96" x2="321.622" y2="203.997" xlink:href="#B"><stop offset="0" stop-color="#a5a4a5"></stop><stop offset="1" stop-color="#c5c3c4"></stop></linearGradient></defs><path fill="url(#t)" d="M318 198h11 4v3l-1 1c0 1 0 2-1 4h0v1c-1 1-3 1-4 1v1l-1-1h0c1-1 2-1 2-1 0-1-1-1-1-1 0 1 0 1-1 1h0 0c-1-1-1-1-2-1l-2-1v1c-1 0-1-1-1-1 0-1 0-1-1-1v-2c-1 1-1 1-1 2v1-2l-1-1v-1-3z"></path><path d="M326 202h6c0 1 0 2-1 4h0 0c-1-1-2-2-2-4h-3z" class="p"></path><defs><linearGradient id="u" x1="329.305" y1="195.836" x2="322.914" y2="201.72" xlink:href="#B"><stop offset="0" stop-color="#373435"></stop><stop offset="1" stop-color="#535252"></stop></linearGradient></defs><path fill="url(#u)" d="M318 198h11 4v3l-1 1h-6c-3-1-6 0-8-1v-3z"></path><path d="M300 227l-2-1c0-1-1-2-1-3 1-2 1-3 1-5l1-1h1c0 1-1 2-1 3v2 3c0 1 1 1 2 2v5l-2 2h0c1 1 3 1 4 1h-3l-1 1 1 2c1 1 4 0 6 0 4 0 9 1 13 0 1 0 1 0 1-1-1 0-1 0-1-1v-2c-1 0-1-1-1-1 0-1-1-1-2-1h0c-1-1-1-4 0-5 0-1 1-1 1-1h1v-1c0-1 1-1 2-2h0c-2 0-4 0-6-1 2 0 5 0 7-1l-2-2h0c1 0 2 1 3 1l1 2h-3c1 1 2 1 3 1-1 1-2 1-2 1v1 4h2v1h-1c0 1 1 1 1 1h0-1c-1 1-1 1-1 2 1 1 2 4 1 5h0v1 2h5 1v2h-3-7s4-1 5-1c-6-2-13-1-20-1-1-1-3-1-4-1-1-1-1-1-1-2v-1c0-1 0-1-1-1 1-1 1-2 1-3l1-1c-1-1-1-3-1-5h2z" class="V"></path><path d="M298 227h2c0 1 1 3 0 4v1h-1c-1-1-1-3-1-5z" class="r"></path><path d="M317 226c1 2 1 4 1 6h-2c-1-1-1-4 0-5 0-1 1-1 1-1z" class="G"></path><path d="M303 241c7 0 14-1 20 1-1 0-5 1-5 1l-1 5h6c1 1 1 1 2 1s1 1 2 1h-2c-1 0-2 0-3 1h-14 0l-12 1v-3h4c0-2 0-4-1-6h0-3 0l2-1 5-1z" class="j"></path><path d="M299 243h17-15c0 2 0 4 1 5h0v1h-1l-1 1c1 0 2 1 3 1h5 0l-12 1v-3h4c0-2 0-4-1-6h0z" class="i"></path><path d="M302 248h0c-1-1-1-3-1-5h15v4c-1 1-3 1-5 1h-9z" class="n"></path><defs><linearGradient id="v" x1="335.01" y1="225.679" x2="328.724" y2="224.977" xlink:href="#B"><stop offset="0" stop-color="#727172"></stop><stop offset="1" stop-color="#8a898a"></stop></linearGradient></defs><path fill="url(#v)" d="M324 206c1 0 1 0 2 1h0 0c1 0 1 0 1-1 0 0 1 0 1 1 0 0-1 0-2 1h0l1 1v-1c1 0 3 0 4-1l-1 2h0 0 0c0 1-1 2-1 3-1 0-1 1 0 1 0 1 0 1 1 2v1 6h-1v1h1 1 5c-1 1-1 2-2 2l2 2c0 1 0 1-1 1h0c-1 2-1 2-1 4h1c1 0 1 1 2 1h-1v3 1 1 4c-1 0-2-1-2-1v-3-1 1h-1c-1 0-2 0-2 1h1c-1 1-1 1-1 2h0-3-1-5v-2-1h0c1-1 0-4-1-5 0-1 0-1 1-2h1 0s-1 0-1-1h1v-1h-2v-4-1s1 0 2-1c-1 0-2 0-3-1h3l-1-2 1-1v-2-1h-1l-1-1h-1c0-1 0-1-1-1v-2l2-1h1l1-1h1c0-1 0-1-1-1 0-1 1-1 1-2v-1z"></path><path d="M334 237c0-1-1-3-1-5 0 0 1 0 1-1v-1l-2-1v-1h3c-1 2-1 2-1 4h1c1 0 1 1 2 1h-1v3 1 1 4c-1 0-2-1-2-1v-3-1z" class="S"></path><path d="M325 223h3c0 1-1 1-1 2v4h1 2v1 2 2h0v1h0v1c1 1 2 1 3 2h0c-1 0-2 0-2 1h1c-1 1-1 1-1 2h0-3-1-5v-2-1h0 3v-1l-1-1v-1-2-2c0-1 1-1 1-1v-1c-1-1-1-1-1-3v-1c1-1 1-1 1-2h0z" class="K"></path><path d="M328 229h2v1 2 2h0v1h0v1c1 1 2 1 3 2h0c-1 0-2 0-2 1h1c-1 1-1 1-1 2h0-3-1-1v-2c1-1 2-1 3-1l-1-1c-1-1 0-2 0-3-1-1-1-1 0-2v-3z" class="p"></path><path d="M324 206c1 0 1 0 2 1h0 0c1 0 1 0 1-1 0 0 1 0 1 1 0 0-1 0-2 1h0l1 1v2c-1 0-2 1-2 1 0 1 1 4 2 4v1 5h-2v1h0 0c0 1 0 1-1 2v1c0 2 0 2 1 3v1s-1 0-1 1v2 2 1l1 1v1h-3c1-1 0-4-1-5 0-1 0-1 1-2h1 0s-1 0-1-1h1v-1h-2v-4-1s1 0 2-1c-1 0-2 0-3-1h3l-1-2 1-1v-2-1h-1l-1-1h-1c0-1 0-1-1-1v-2l2-1h1l1-1h1c0-1 0-1-1-1 0-1 1-1 1-2v-1z" class="P"></path><path d="M326 207h0c1 0 1 0 1-1 0 0 1 0 1 1 0 0-1 0-2 1h0l1 1v2c-1 0-2 1-2 1 0 1 1 4 2 4v1 5h-2v1c-1 0-2 0-2-1h1c1-1 1-3 1-5h0l1-1c-2-1-2-2-3-3l-1-1c1-1 1-1 2-1s1-1 1-1c0-1-1-1-1-2l2-1z" class="K"></path><defs><linearGradient id="w" x1="344.775" y1="216.661" x2="335.463" y2="215.668" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#w)" d="M325 186h7c1 1 2 1 3 1h1v6c0 1-1 2 0 3 2 1 5 1 8 2h1v2 12 11 4 1 3c0 1 1 2 2 2-1 0-2 0-2-1-1 2 0 5-1 6h-1 0c-1-1-2-1-3-2s-1-2-3-3v3c-1 1-1 1-1 2h0v-1-1-3h1c-1 0-1-1-2-1h-1c0-2 0-2 1-4h0c1 0 1 0 1-1l-2-2c1 0 1-1 2-2h-5-1-1v-1h1v-6-1c-1-1-1-1-1-2-1 0-1-1 0-1 0-1 1-2 1-3h0 0 0l1-2v-1h0c1-2 1-3 1-4l1-1v-3h-4c1-1 2 0 3-1h0c-3-1-6-1-10-2l5-1v-5c0-1 0-2 1-2-1-1-2-1-3-1z"></path><path d="M335 228c1 1 1 2 1 3v1h-1-1c0-2 0-2 1-4h0z" class="B"></path><path d="M335 213h1v4l-1 1c-1-1-1-2-1-3l1-2z" class="G"></path><path d="M334 203h2v3c0 1-1 1-2 1v-2-2z" class="S"></path><path d="M334 201v-3c3 0 7 0 10 1v1 4h-1 0l-8-2h-1v-1z" class="I"></path><path d="M335 202c0-1 1-1 1-2 2 0 5 1 6 3h1 0v1l-8-2z" class="C"></path><path d="M338 229c2-2 5 0 7-2v1 3c0 1 1 2 2 2-1 0-2 0-2-1-1 2 0 5-1 6h-1 0c-1-1-2-1-3-2s-1-2-3-3v-1-1l1-1v-1z" class="F"></path><path d="M337 232v-1l1-1v-1 1c1 0 1 1 2 1s1 0 2 1l-1 1h-1c-1-1-2-1-3-1h0z" class="m"></path><path d="M325 186h7c1 1 2 1 3 1h1v6c0 1-1 2 0 3 2 1 5 1 8 2h1v2h-1v-1c-3-1-7-1-10-1v3 1 1 2 2c0 1 0 1 1 2l1 1-1 3h0l-1 2c0 1 0 2 1 3v1 1c0 1 0 2 1 3h-5-1-1v-1h1v-6-1c-1-1-1-1-1-2-1 0-1-1 0-1 0-1 1-2 1-3h0 0 0l1-2v-1h0c1-2 1-3 1-4l1-1v-3h-4c1-1 2 0 3-1h0c-3-1-6-1-10-2l5-1v-5c0-1 0-2 1-2-1-1-2-1-3-1z" class="f"></path><path d="M332 211h1l2 2h0l-1 2c0 1 0 2 1 3v1 1c0 1 0 2 1 3h-5-1c2-2 1-3 1-5h0l1-1h0l-1-1v-1h0v-3l1-1z" class="J"></path><path d="M331 223c1-1 1 0 1-1 2 0 2-1 3-2 0 1 0 2 1 3h-5zm1-12h1l2 2h0l-1 2v-2h0-2 0v-2z" class="c"></path><path d="M352 566h3 11v2h-4c-1 0-2 1-3 1v1s-1 1-1 2v1 2c-1 2-1 2-2 3 0 0 0 1 1 1 0 1 1 2 2 3h0l1-1 1 1v1 1 1h1l1 3 1 3c0 2 1 4 0 6l3 9v-10c1 3 0 8 1 11h0c1 1 0 3 0 4l2-2c0-1 0-1-1-2 0-1 0-2 1-2-1-1 0-2 0-3v3 1 5c1 1 2 2 4 3h2c-1 0-1 1-2 2l-1 1c0 1-1 1-2 2-1 0-2 1-3 2h0c-1-1-1-1-1-2h-1c0 1 0 1-1 2v-1h-2c-1 1-1 1-3 2 0 0-1-1-2-1h-1 1c-1-1-1-1-1-2h2 0 2c0-1 0-1 1-2-1 0-1 0-2-1 0-1 0-1 1-2h0-2l1 1c-1 1-2 1-3 1v-1c-1-1-2-1-3-1h-1c-2 0-3 1-5 2h-1c-1 0-3 1-4 2h-1c-1 0-1 0-2 1-2-1-5 0-6 1h-1l-1 1-3 1h-2v-1l2-1c-2-1-1-1-2-3-1 1-2 1-3 1h-3v-2l4-1 1-1-1-1h5 0v-1-1c1 0 1-1 1-2h-1l-4-1c-3 0-5-2-8-2-1-1-3 0-4 0h-4l-41 9h-1c1-1 1-2 1-3v-3-3l-2 1v-5l2-4c1-3 3-6 4-8l3-6s1 1 1 2l3-4h1v-1l3-3v-2-3c1-1 1-2 2-3h1c0-1 1-1 2-1h1v-1h9 2 2 1 13 0 0 1c1 0 2 0 3-1l1 1h7 2 1 2 2 0 3 5l-2-1h2v-1h8z" class="E"></path><path d="M316 599v-3l1-1v3h1c-1 0-1 0-2 1z" class="B"></path><path d="M320 598h0c-1-1-1-2-1-4h1v4z" class="b"></path><path d="M294 593c1-1 1-2 1-3 1 0 2 1 3 1l-1 2h-3z" class="N"></path><path d="M341 593h-2-1c1-2 0-3 1-5 1 1 1 3 1 4l1 1z" class="C"></path><path d="M325 600h2v1c-1 1-1 2-1 3v1h-1c-1-1-1-1-1-3 0 1 0 1 1 1v-3z" class="I"></path><path d="M336 574h1v7h-1v-1-6z" class="D"></path><path d="M308 591h1v-1h1v3c0 1 0 2-1 2h-1v-4z" class="F"></path><path d="M355 613l4 1 1 1c-1 1-2 1-3 1v-1c-1-1-2-1-3-1 0-1 1-1 1-1z" class="P"></path><path d="M342 574h0 1v7h-1c-1-2-1-5 0-7z" class="W"></path><path d="M333 610c0-1 0-2 1-3h1l2 2c0 1-1 2-1 2-1 0-2-1-3-1z" class="B"></path><path d="M320 594v-5h1 0 1c-1 1-1 5-1 7v2h-1v-4z" class="a"></path><path d="M329 603l2 2v4h0-1l-4-1 2 1c1-1 2-2 2-3-1-1-1-1-2-1h0v-1h1v-1z" class="b"></path><path d="M291 594c0-2 0-5 1-6h1v1 3h0c0 2 0 5-1 6h0-1v-4z" class="c"></path><path d="M337 581c0 1 0 1 1 2h1c0-1-1-2-1-3v-6h1 1v9h2c-3 1-6 0-9 0v-1l1 1h1c1-1 1-1 1-2h1z" class="m"></path><path d="M329 603c0-4-1-10 1-13h1v6 9l-2-2z" class="D"></path><path d="M310 593h1v3c-1 2-1 2-1 4 0 1 0 1-1 1h-3c-1 2-2 3-2 4-1 1-1 1-2 1v-6 3 2h1 1v-2s0-1 1-1c0-1 1-2 2-2h1v-1-4h1c1 0 1-1 1-2z" class="G"></path><path d="M308 595h1v1 3h-1v-4z" class="B"></path><path d="M302 600c0-2-1-8 0-10 0-1 1-1 1-1l1 1v1c0 4 0 8-1 12h-1v-3z" class="P"></path><path d="M294 593h3v10c0 1 0 3-1 4h0-1c-1-4 0-10-1-14z" class="C"></path><path d="M338 612l1-1v-3h0 0 4c1 0 1 0 2-1 1 2 0 3 1 4h3l-1 1c-1 0-4 0-5 1-2 0-4 0-5-1z" class="b"></path><path d="M333 589h1c1-1 1 0 2 0v16h-2-1v-3-13z" class="S"></path><path d="M311 593v-3h1 0c0 2 1 3 0 4v5c2 0 2-1 3 0h1c1-1 1-1 2-1 2 1 5 1 7 1v-1c-1-1-1-2-1-3 1-1 1-1 2-1v1h0c-1 1 0 3 0 4h2 0c0 1-1 1-1 1h-2-9c-2 0-3 1-5 0h-1c0-2 0-2 1-4v-3z" class="b"></path><path d="M311 593v-3h1 0c0 2 1 3 0 4v1 5h-1v-4-3z" class="C"></path><path d="M304 590c1 0 1 0 2 1h1l1-1v1 4 4 1h-1c-1 0-2 1-2 2-1 0-1 1-1 1v2h-1-1v-2h1c1-4 1-8 1-12v-1z" class="U"></path><path d="M304 590c1 0 1 0 2 1h1v8l-1 1-1-1v-3c0-1 1-2 1-3-1-1-1-2-2-2v-1zm36 2c1-1 2-3 2-5h1c0 2 0 3 1 4l1 1-1 3v6l-1 1v4 1c-2 0-2-1-3-2l-1 1h0l-1-1c0-2 0-8 1-11h1v3c0 2-1 7 1 8v-2-10h0l-1-1z" class="G"></path><path d="M302 583h13 10 1c1 1 2 1 3 2h-4-31l-3-1h0l2-1h7 2z" class="i"></path><path d="M331 609h0l2 1c1 0 2 1 3 1l2 1c1 1 3 1 5 1 1-1 4-1 5-1l1-1c1 1 2 1 3 1 1 1 2 1 3 1 0 0-1 0-1 1h-1c-2 0-3 1-5 2h-1c-1 0-3 1-4 2h-1c-1 0-1 0-2 1-2-1-5 0-6 1h-1l-1 1-3 1h-2v-1l2-1c-2-1-1-1-2-3-1 1-2 1-3 1h-3v-2l4-1 1-1-1-1h5 0v-1-1c1 0 1-1 1-2z" class="k"></path><path d="M348 612l1-1c1 1 2 1 3 1 0 1-1 2-2 2h-4l2-1v-1z" class="l"></path><path d="M330 613h2v2c-1 0-1 1-1 1h-3l-3-1 1-1-1-1h5 0z" class="V"></path><path d="M325 615l3 1h3c-1 0-1 1-1 1 0 1 0 2-1 3-2-1-1-1-2-3-1 1-2 1-3 1h-3v-2l4-1z" class="O"></path><path d="M329 620c3 0 2-2 5-3 0 1 0 1 1 2 0-1 0-1 1-1l-1-1v-1c1 0 3 0 4 1 1 0 2 0 3 1h0c-1 0-1 0-2 1-2-1-5 0-6 1h-1l-1 1-3 1h-2v-1l2-1z" class="S"></path><path d="M331 609h0l2 1c1 0 2 1 3 1l2 1c1 1 3 1 5 1 1-1 4-1 5-1v1l-2 1-1 1c-4 0-6 0-10-2-1-1-2-1-5-1v-1c1 0 1-1 1-2z" class="L"></path><path d="M331 609h0l2 1c1 0 2 1 3 1l2 1c1 1 3 1 5 1-1 1-2 1-3 0h-5c-1-1-2-1-5-1v-1c1 0 1-1 1-2z" class="K"></path><path d="M367 596c1 3 0 8 1 11h0c1 1 0 3 0 4l2-2c0-1 0-1-1-2 0-1 0-2 1-2-1-1 0-2 0-3v3 1 5c1 1 2 2 4 3h2c-1 0-1 1-2 2l-1 1c0 1-1 1-2 2-1 0-2 1-3 2h0c-1-1-1-1-1-2h-1c0 1 0 1-1 2v-1h-2c-1 1-1 1-3 2 0 0-1-1-2-1h-1 1c-1-1-1-1-1-2h2 0 2c0-1 0-1 1-2-1 0-1 0-2-1 0-1 0-1 1-2h0v-1c-1-1-1 0-2-1 0-1-1-1 0-2h1c0-1-1-1-2-2 0 1 0 1-1 1l-1-2h2c1 0 1 0 1 1h1c0 1 1 1 2 2v-2-1h-3v-1h-4l-1-1h-1c-2 0-5 0-6-1h0 3 0l2-1c0 1 1 1 1 1h4 7l1 2h1c0 1 0 1 1 1v-1-10z" class="I"></path><path d="M364 610c-1 0-1-1-2-1v-1c1-1 1-1 2-1v2 1z" class="m"></path><path d="M366 613h1c1 0 2 1 3 2 0 0-1 1-2 1h-1v-3h-1z" class="l"></path><path d="M361 614c2 0 3-1 5-1h1v3h-1 0c-1 0-1 0-2-1-1 1-2 1-2 2-1 0-1 0-2-1 0-1 0-1 1-2z" class="Q"></path><path d="M367 596c1 3 0 8 1 11h0c0 2 0 4-1 5 0 0-1-1-2-1l-1-1v-1-2l2-1c0 1 0 1 1 1v-1-10z" class="R"></path><path d="M364 609h3c1 1 0 1 0 2h-2l-1-1v-1z" class="d"></path><path d="M350 604h0l2-1c0 1 1 1 1 1h4 7l1 2c-3 0-7-1-11-1h-1c-2 0-5 0-6-1h0 3z" class="m"></path><path d="M370 615c2 0 3 0 4 1l-1 1c0 1-1 1-2 2-1 0-2 1-3 2h0c-1-1-1-1-1-2h-1c0 1 0 1-1 2v-1h-2c-1 1-1 1-3 2 0 0-1-1-2-1h-1 1c-1-1-1-1-1-2h2 0 2c0-1 0-1 1-2 0-1 1-1 2-2 1 1 1 1 2 1h0 1 1c1 0 2-1 2-1z" class="J"></path><path d="M362 617c0-1 1-1 2-2 1 1 1 1 2 1h0v1l-1 1h-2l-1 1h3v1h-2c-1 1-1 1-3 2 0 0-1-1-2-1h-1 1c-1-1-1-1-1-2h2 0 2c0-1 0-1 1-2z" class="N"></path><path d="M359 619h0c1 1 2 1 4 1-1 1-1 1-3 2 0 0-1-1-2-1h-1 1c-1-1-1-1-1-2h2z" class="J"></path><path d="M298 590l1-1v-1l1 1c0 4-1 9 0 14v4c1 1 3 0 5 0v-1c1 1 1 0 2 0h0 1 2l-41 9h-1c1-1 1-2 1-3v-3c1-1 1-1 1-2v-1h3c2-1 3-1 5-1h14c-1-1-1-3-1-5l1-2h0c1-1 1-4 1-6h0v-3c1 0 1 1 1 2-1 3-1 6-1 9v9h1 0 1 2c0-1 1-1 1-1v-1h0c0-1 0-2-1-3v4h-1v-1h0c1-1 1-3 1-4v-10l1-2v-1z" class="X"></path><path d="M292 598h0c1-1 1-4 1-6h0v11c0 2 0 4-1 6h0l-1-1c0-1 0-1 1-2v-1c-1-1-1-3-1-5l1-2z" class="D"></path><path d="M298 590l1-1v-1l1 1c0 4-1 9 0 14l-1 4h0c-1-2-1-13-1-17z" class="J"></path><defs><linearGradient id="x" x1="275.369" y1="583.583" x2="280.827" y2="604.809" xlink:href="#B"><stop offset="0" stop-color="#121212"></stop><stop offset="1" stop-color="#2d2c2d"></stop></linearGradient></defs><path fill="url(#x)" d="M280 582h1l1 2c1 0 2 1 2 1l1 1c1 0 1 0 2 1 0 1 1 1 1 2 1 1 2 3 2 5h1v4h1l-1 2c0 2 0 4 1 5h-14c-2 0-3 0-5 1h-3v1c0 1 0 1-1 2v-3l-2 1v-5l2-4c1-3 3-6 4-8l3-6s1 1 1 2l3-4z"></path><path d="M285 586c1 0 1 0 2 1 0 1 1 1 1 2 1 1 2 3 2 5h1v4c0-1-1-1-2-1-1-5-2-8-4-11z" class="F"></path><path d="M276 584s1 1 1 2c-3 6-5 12-7 18l-1 1v1l-2 1v-5l2-4c1-3 3-6 4-8l3-6z" class="P"></path><path d="M267 602l2-4v6 1 1l-2 1v-5z" class="O"></path><path d="M270 604l1 1c2-2 2-5 3-7l4-7c0 2-1 3-1 4-1 3-3 6-3 9h10c1-1 2-1 3 0h1c-1-3-1-6-1-9h1l1 2h0c1 0 2 0 2 1h1l-1 2c0 2 0 4 1 5h-14c-2 0-3 0-5 1h-3v1c0 1 0 1-1 2v-3-1l1-1z" class="C"></path><path d="M289 597c1 0 2 0 2 1h1l-1 2c0 1 1 3 0 4h-1c-1-2 0-5-1-7h0 0z" class="b"></path><defs><linearGradient id="y" x1="357.405" y1="590.878" x2="343.146" y2="596.943" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#2d2d2d"></stop></linearGradient></defs><path fill="url(#y)" d="M351 580v-1h1v1c0 1 0 2 1 2v1h1l1 1v-2h0c0-2 1-2 2-3 0 1 1 2 2 3h0l1-1 1 1v1 1 1h1l1 3 1 3c0 2 1 4 0 6l3 9v1c-1 0-1 0-1-1h-1l-1-2h-7-4s-1 0-1-1l-2 1h0-3 0c1 1 4 1 6 1h-3c-1 1-1 1-2 0-1 0-2 0-3 1h-1l-1-1c1-1 1-2 0-3h0l1-1v-6l1-3-1-1c-1-1-1-2-1-4v-1l1-1c0-1 1-1 1-1l-1-1 1-1h1 1c1-1 3 0 4-1v-1z"></path><path d="M353 598h1v1h-1v-1z" class="E"></path><path d="M346 589l1 1c0 1-1 2-1 4-1 2-1 5-1 7 0 1 1 3 0 4h-1v-4-6l1-3 1-3z" class="C"></path><path d="M351 580v-1h1v1c0 1 0 2 1 2v1c-2 2-4 5-6 7l-1-1-1 3-1-1c-1-1-1-2-1-4v-1l1-1c0-1 1-1 1-1l-1-1 1-1h1 1c1-1 3 0 4-1v-1z" class="b"></path><path d="M347 582c1 0 2 0 3 1l-1 1h-1-3l-1-1 1-1h1 1z" class="W"></path><path d="M345 584h3l1 1c0 1-2 3-3 4l-1 3-1-1c-1-1-1-2-1-4v-1l1-1c0-1 1-1 1-1z" class="r"></path><path d="M355 584v-2h0c0-2 1-2 2-3 0 1 1 2 2 3h0l1-1 1 1v1 1 1h1l1 3 1 3c0 2 1 4 0 6l3 9v1c-1 0-1 0-1-1h-1l-1-2h-7-4s-1 0-1-1l-2 1h0 0l1-1c-1-1-1-3 0-4h0l-1-1c0-3 1-7 2-9h1c0 1-1 3-1 5-1 2-1 5-1 8h1 0c1 0 2 0 2-1 1 0 1 0 1-1 0-3 0-7 1-10h0l1-2-1-2h0l-1-2z" class="I"></path><path d="M357 588l1 2c1 1 2 3 2 4h-1-1c0-1-1-2-1-3v-1h-1l1-2z" class="E"></path><path d="M360 594c1 2 2 4 3 7l1 3h-7v-1-3c1-2 0-4 0-6h1 0 1 1z" class="b"></path><path d="M360 594c1 2 2 4 3 7l-1 1-1-1v-1c-1-1-2-3-3-6h0 1 1z" class="Y"></path><path d="M355 584v-2h0c0-2 1-2 2-3 0 1 1 2 2 3h0l1-1 1 1v1 1 1h1l1 3 1 3c0 2 1 4 0 6l3 9v1c-1 0-1 0-1-1h-1l-1-2-1-3c-1-3-2-5-3-7 0-1-1-3-2-4l-1-2-1-2h0l-1-2z" class="J"></path><path d="M357 584h1c1 1 2 3 2 5-1 0-1 0-2 1l-1-2-1-2c1 0 1-1 1-2z" class="p"></path><path d="M355 584v-2h0c0-2 1-2 2-3 0 1 1 2 2 3h0l1-1 1 1v1 1 1h1l1 3-3-3v-1h0c-1 0-2-1-3-1v1c0 1 0 2-1 2h0l-1-2z" class="S"></path><path d="M358 590c1-1 1-1 2-1l4 8 3 9v1c-1 0-1 0-1-1h-1l-1-2-1-3c-1-3-2-5-3-7 0-1-1-3-2-4z" class="M"></path><path d="M352 566h3 11v2h-4c-1 0-2 1-3 1v1s-1 1-1 2v1 2c-1 2-1 2-2 3 0 0 0 1 1 1-1 1-2 1-2 3h0v2l-1-1h-1v-1c-1 0-1-1-1-2v-1h-1v1 1c-1 1-3 0-4 1h-1c-2-2 0-6-1-9h-2c-2 1-5 0-7 1v6 1c0 1 0 1-1 2h-1l-1-1v1c3 0 6 1 9 0 1 1 1 1 1 2h-18 4c-1-1-2-1-3-2h-1-10-13-2-7l-2 1h0l3 1h-3 0c-1 1-3 0-5 0l2 3v1c0-1-1-1-1-2-1-1-1-1-2-1l-1-1s-1-1-2-1l-1-2v-1l3-3v-2-3c1-1 1-2 2-3h1c0-1 1-1 2-1h1v-1h9 2 2 1 13 0 0 1c1 0 2 0 3-1l1 1h7 2 1 2 2 0 3 5l-2-1h2v-1h8z" class="q"></path><path d="M334 568h2 0l1 3h-6c1 0 2 0 3-1v-2z" class="f"></path><path d="M287 583h1v1c-1 1-3 1-4 1 0 0-1-1-2-1h2c1-1 2-1 3-1z" class="F"></path><path d="M331 568h1 2v2c-1 1-2 1-3 1h-4c1 0 2 0 3-1s1-1 1-2z" class="Z"></path><path d="M336 568c2 0 3 1 5 0v1l1 1 1 1h-6l-1-3z" class="n"></path><path d="M284 578c0 1 0 2 1 4 1 0 1 0 2 1-1 0-2 0-3 1h-2l-1-2v-1l3-3z" class="B"></path><path d="M281 581h1c1 1 1 2 2 3h-2l-1-2v-1z" class="C"></path><path d="M344 568h1l2 3h-4l-1-1-1-1v-1c-2 1-3 0-5 0h0 0 3 5z" class="D"></path><path d="M291 585h-1-2c0-1 1-1 1-2 0-2-1-5-1-6s2-3 2-3c0 3 1 7 0 10h0l1 1z" class="E"></path><path d="M290 574l1-1h1c1 2 0 7 0 9v1h1l-2 1h0l3 1h-3 0l-1-1h0c1-3 0-7 0-10z" class="g"></path><path d="M284 573c1-1 1-2 2-3h1c0-1 1-1 2-1h1v1c-1 0-1 0-2 1 2 1 19 0 23 0h0c-6 1-13 0-19 0-1 0-3 0-4 1-1 0-2 1-2 1l-2 3v-3z" class="B"></path><path d="M351 580l-1-1c-1-3 0-8 0-11 1 2 1 4 2 6h0l-1 1v1l1 1c1 2 1 3 2 5v1h-1v-1c-1 0-1-1-1-2v-1h-1v1z" class="X"></path><path d="M352 574c1-1 1-2 1-4 1-1 2 0 2 0 1 0 1 0 1-1l1 1v1 1h1v1 2c-1 2-1 2-2 3 0 0 0 1 1 1-1 1-2 1-2 3h0v2l-1-1v-1c-1-2-1-3-2-5l-1-1v-1l1-1h0z" class="m"></path><path d="M357 573h1v2c-1 2-1 2-2 3v-1-1c0-1 0-2 1-3z" class="F"></path><path d="M352 574l1 2h1l1-2c0 1 0 2-1 3h0-2l-1-1v-1l1-1z" class="G"></path><path d="M354 582v-3c1 0 1-1 1-1v-1h1v1s0 1 1 1c-1 1-2 1-2 3h0v2l-1-1v-1z" class="D"></path><path d="M352 574c1-1 1-2 1-4 1-1 2 0 2 0 1 0 1 0 1-1l1 1v1 1h1v1h-1l-1-3v1c-1 1-1 2-1 3l-1 2h-1l-1-2h0z" class="E"></path><path d="M294 574l1-1c2 0 5 1 6 0h0c2 1 4 0 6 1h-5v5l1 3-1 1h-2-7s1 0 1-1v-8z" class="X"></path><path d="M294 574h2 0v8h-2v-8z" class="Z"></path><path d="M300 582h-2c1-1 1-3 0-4h0c1-2 0-3 1-4h1c1 1 0 2 0 4 0 0 1 0 1 1h1l1 3-1 1h-2v-1z" class="P"></path><path d="M300 582v-2-1h1 1l1 3-1 1h-2v-1z" class="E"></path><path d="M312 574h1c0 1 0 2 1 3 0 1-1 4 0 4v1h1 1l-1 1h-13l1-1-1-3v-5h5 5z" class="I"></path><path d="M302 579v-5h5 5 0v8h-1-1v-7h-4c-1 2 1 6-1 7h0v-1-3h-1v1h0c1 1 1 2 1 2-1 1-1 1-2 1l-1-3z" class="P"></path><path d="M318 568c1 0 2 0 3-1l1 1h7 2c0 1 0 1-1 2s-2 1-3 1h-2-9-5c-4 0-21 1-23 0 1-1 1-1 2-1v-1-1h9 2 2 1 13 0 0 1z" class="T"></path><path d="M318 568c1 0 2 0 3-1l1 1h7 2c0 1 0 1-1 2s-2 1-3 1h-2c-1-1-2-2-2-3h0-6 0 1z" class="e"></path><path d="M322 574h3 4l1 1v1l-3-1c-1 1 0 6-1 8h1c1 0 2-1 2 0h3v-9h2c1 0 1 0 1 1v1h-1v-1h-1v7 1c3 0 6 1 9 0 1 1 1 1 1 2h-18 4c-1-1-2-1-3-2h-1-10l1-1h-1-1v-1c-1 0 0-3 0-4-1-1-1-2-1-3 3 0 6-1 9 0z" class="g"></path><path d="M322 575h0 3v8c-1 0-4 0-5-1 1-2 0-5 1-7h1z" class="r"></path><path d="M313 574c3 0 6-1 9 0v1h-1c-1 2 0 5-1 7h-1-1-1v-2 2h-1-1-1v-1c-1 0 0-3 0-4-1-1-1-2-1-3z" class="Z"></path><path d="M315 582v-7h2c1 2 0 3 0 5v2h-1-1z" class="r"></path><path d="M326 88h1 187v142h-12c-1 0-4 1-5 0 0-5 0-10-1-15-2-11-7-23-13-33-1-3-4-5-6-8-2-5-6-10-11-13-10-11-26-21-41-25-1-1-4-2-5-2-12-3-23-3-34-4h-49v-1c-2 0-2-2-2-4h0l-2-4 1-1c10 2 20 1 30 1 17 0 35 0 51 3 20 4 40 14 54 27l3 3c3 3 7 6 9 10 1 1 2 2 3 4 1 1 2 4 4 4 1 3 4 7 5 10 4 8 7 17 10 26 0 2 1 4 1 6v6l1 1v-1V96h-1-14-25-72-50-11-5-1l-1-8h1z" class="j"></path><path d="M320 94c2 1 4 2 6 2h1 5 11 50 72 25 14 1v124 1l-1-1v-6c0-2-1-4-1-6-3-9-6-18-10-26-1-3-4-7-5-10-2 0-3-3-4-4-1-2-2-3-3-4-2-4-6-7-9-10l-3-3c-14-13-34-23-54-27-16-3-34-3-51-3-10 0-20 1-30-1l-1 1 2 4h-1l-1-1-1 1c-1-1-2-2-4-3-1 0-2 0-3 1 0 0 0 1-1 1v-3-1h-1c-1-1-3-1-5-1h0-1l-11 1h-1v-3l1-1h0v-1-2h0c1 1 2 0 3 1h1l2-1c-2 0-2 0-4-1-1 0-1-1-2-1v-1h0c-1 0-1 0-2-1v-1-3c1-1 0-1 1-2l1-2h0 5l-1-1h0c0-1 0-2 1-2 0-1 1-1 2-1s1 0 1-1h2c1 0 1 0 2-1h0 2v-1z" class="s"></path><path d="M440 129l6 3c-1 0-2 0-3-1v1h0l-5-2 2-1z" class="V"></path><path d="M496 100c1 0 2-1 3 0 0 0 1 1 1 2v3c-1-2-2-4-4-5z" class="T"></path><path d="M443 104c0-1-1-1-1-2h1c1-1 3-2 5-1h-1c-1 1-2 2-3 2l-1 1z" class="L"></path><path d="M430 127l1-1 1 1 1-1 7 3-2 1-8-3z" class="T"></path><path d="M496 97h8v1c-1 2 0 5 0 7-2-1-2-4-2-6v-2h-5-1zm-110 16c2-1 4-2 6-2-2 1-3 3-6 4h-1c-1 1-3 1-4 2h-7c-1 0-1-1-2-1v-1h2c2 1 6 1 8 0h1c1 0 3-1 4-1v-1h-1z" class="h"></path><path d="M481 123c0-1 0-2 1-2 1-1 1-1 2 0l-1 1h1v6c-1 1-2 2-3 4l-1-1c1-1 1-1 1-2 0-2 0-2-1-3 0 0 0-1 1-1v-1-1z" class="V"></path><path d="M426 107h-1c-1-1-3-2-4-2l-1-1c-1-1-2-1-3-1 0 0-1-1-2-1l-1-1h1 0 2l1-2h0 1 1l1 1 2 2h1c1 1 1 2 2 2l2 2-1 1h-1z" class="h"></path><path d="M349 108s0-1-1 0c-1 0-1 0-1 1l-1 1c-2-1-2-1-3-1 0-2 0-2 1-3 0-1 2-3 3-3 2 0 5 1 7 2 1 1 2 2 3 4v2c-1 2-3 6-5 7h-1c1-1 3-3 4-5 0-1 0-3-1-5l-3-3h-1-1c-2 0-3 0-4 1v2h1c0-1 0-1 1-2 1 0 2 0 3 1l-1 1z" class="M"></path><path d="M421 97h15c-1 1-2 1-4 0v1h2c0 1 1 1 2 1l2 1h2 0c0 1-1 1-2 1v1l2 2h-1-1 0 0l-1 1h0c-1 0-1-1-2-1l-1-1c-1 0-1-1-2-2-1 0-1 0-2-1h0c-1-1-2-1-3-1v-1c-2 0-4 0-6-1z" class="h"></path><path d="M426 104l3 2v-1c-1-1-4-3-4-4l1-1v-1c1 1 0 2 1 3 0 0 2 2 3 2 3 6 9 9 12 15l5 5h-1l-3-2c-1-1-2-3-4-4l-3-3c-3-3-6-5-9-7l-1-1h1l1-1-2-2z" class="L"></path><path d="M350 107c-1-1-2-1-3-1-1 1-1 1-1 2h-1v-2c1-1 2-1 4-1h1 1l3 3c1 2 1 4 1 5-1 2-3 4-4 5-1 0-2 1-2 1-3 0-7 1-9-1h0 1v-2c1 0 2 0 3 1h2 1c0-1 1-1 1-1 1-1 2-1 3-2s1-3 1-4-1-3-2-3z" class="j"></path><path d="M457 105c2-1 5-2 8-2 3-1 5 0 8 0s5 0 7 2h1v1c-1-1-3-2-5-2-4-1-8 0-12 2-2 0-3 1-5 1-2 1-3 2-3 4v1c-1 2-1 5-1 8 1 2 2 4 4 5 0 1 1 3 2 3 3 2 6 4 10 5h0 5 1c1-1 2-1 3-2l1 1c-2 0-3 1-4 2h-1-5c-1 0-2 0-3-1h-1c-1-1-2-1-4-2l-3-3c-1-1-2-2-3-4h-1v-1c-1-1-1-2-2-3v-8h0v-1c1-1 2-3 3-4h1c1-1 4-2 6-2v-1h1c-3 0-5 1-7 1v1l-1-1z" class="O"></path><path d="M486 163c-2-5-2-9-1-14l1-1h0c1-3 3-6 5-8h1c0 1-1 2-2 3-1 2-3 4-3 6-2 7 0 12 4 17 1 3 3 5 5 7l3 9 1 2 1 3v1l1 1v1c1 1 1 1 1 2v1 1c-1-1-1-3-2-4 0-1-1-2-1-3v-1c-1-1-1-3-1-4l-3-6c-1 0-2-1-3-2-1-2-3-4-4-6s-3-3-3-5z" class="Q"></path><path d="M486 163l4 5c2 2 2 3 4 5 1 0 1 2 2 3-1 0-2-1-3-2-1-2-3-4-4-6s-3-3-3-5z" class="T"></path><path d="M350 107c1 0 2 2 2 3s0 3-1 4-2 1-3 2c0 0-1 0-1 1h-1-2c-1-1-2-1-3-1v2h-1-3c-1-1-2-1-4-2 0-1-1-2-1-2l-1-1v-2l-1-1h4l1-1s1 1 1 2 1 1 2 2l1-1c1 1 1 1 2 1 3 0 6 0 8-2h1v-1c0-1 0-1-1-2l1-1z" class="L"></path><path d="M332 114l2-3c1 2 2 4 4 5h3v2h-1-3c-1-1-2-1-4-2 0-1-1-2-1-2z" class="s"></path><path d="M350 107c1 0 2 2 2 3s0 3-1 4-2 1-3 2c0-2 1-2 2-3v-1l-2 1c-1 1-2 1-4 2-2-1-4-1-6-2l1-1c1 1 1 1 2 1 3 0 6 0 8-2h1v-1c0-1 0-1-1-2l1-1z" class="Q"></path><path d="M455 120l2-1v-1c0-1 0-1 1-2 1-2 2-5 5-5 0-1 1-1 1-1 0-1 1-1 2-1v-1c1 0 2 0 3-1h1 3 1 0c1 0 3 0 4 1h1c1 0 2 0 2 1h1l2 1v1c2 1 3 2 4 3l1 1c1 1 1 2 1 4 1 0 1 1 1 1v1c1 1 1 0 1 1 1 1 0 3 0 4 0 2 1 3 0 5v-5c-1-1-1-3-1-4-1-1-2-1-2-3-1-1-2-2-3-2l-3-3h1 0c-1-1-2-2-3-2-1-1-3-2-4-2h-2c-3-1-5-1-7-1-1 1-1 1-1 2 0 0-1 1-1 2l-2 2h0c0-1-1 0 0-1v-1c0-1 0-1 1-2l1-1-1 1h0c-3 1-4 3-6 5 0 1 0 3-1 5h0c1 1 1 3 1 4-2-1-3-3-4-5z" class="T"></path><path d="M500 105c2 7 1 12-2 20-1 1-1 3-1 5 0 1-1 2-1 3 2-1 3-4 5-6v1c-1 2-4 5-5 8-1 0-2 1-2 2h-2v-1l2-2c2-2 2-7 2-9-1-8-3-16-7-22l-5-5v-1c7 4 10 12 12 19 1 3 1 6 1 8l1-3c2-5 3-10 1-15h0v-1h0l-1 1c-1-1-1-2-2-3l-1 1c1 0 1 1 1 2s1 2 0 3l-1-3c-1-2-3-6-6-7-1 0-1-1-2-1h7l2 1c2 1 3 3 4 5z" class="l"></path><path d="M495 105c-1-2-2-3-4-5h0c2 0 4 0 5 1 2 2 3 3 3 6h0v-1h0l-1 1c-1-1-1-2-2-3l-1 1z" class="T"></path><path d="M448 101l4-2h1 1 1c0-1 1 0 2 0 2-1 6-1 8-1 1 1 2 0 3 1h3c1 0 1 1 2 1h1c1 0 1 0 2 1h-1c-1 0-2-1-2-1h-3-1c-1-1-2 0-3 0-3 0-7-1-9-1l-1 1h-2-2l-1 1c-1 0-2 0-2 1h-1c-2 1-6 4-7 7v1 1l1 1 4 6c0-3 0-8 3-11 1-1 3-2 5-3 1 0 2 0 3 1l1 1c-1 0-2 0-3 1h0c-1 1-2 1-3 2v1 1c0 1-1 2-1 2-1 0-1-1-1-1-1-1-1 0-2 0v6c1 2 2 3 3 4 0 1 0 2 1 3s2 2 4 3h-2c-2-1-4-3-5-5-2-2-4-5-6-7-1-2-3-4-3-6l-1-1v-1-1l1 1v-1l3-3 1-1c1 0 2-1 3-2h1z" class="Q"></path><path d="M448 112c0-2 1-4 2-5s2-1 4-1h0c-1 1-2 2-2 3h-1c0 1 0 1-1 2v1c-1-1-1 0-2 0zm19 34h0v-1h0l1 1 3-1-3 1 1 1c1 0 1 0 2-1 1 1 1 2 1 3v1h0v-1c1 0 1 0 1-1v-1c1-1 2-1 2-1h1l-1 5h-1v-2l1-1v-1h0c-1 0-1 1-1 2l-1 2h0l2 3h0l5 5 1 1h0 0v-1c1 0 1 0 1-1v3l2 2c1 0 0 0 1 1 0 1 1 1 1 2l1 1c0 1 0 1 1 2l2 2c1 1 2 3 2 4 2 2 3 3 4 5s1 3 1 5v2c-1-2-1-3-2-4-1-2-2-5-4-7-1-1-1-2-2-3v-1h-1c-2 0-3-3-4-4-1-2-2-3-3-4-2-4-6-7-9-10l-3-3h1c-2-1-5-4-7-6 1 0 2 1 2 1h2z" class="h"></path><path d="M463 145c1 0 2 1 2 1h2c3 2 6 5 7 8h-2l-3-3h1c-2-1-5-4-7-6z" class="V"></path><path d="M472 154h2c2 2 5 4 6 6 0 1 1 2 1 2 1 2 3 2 3 4 1 0 1 1 2 1v1c1 1 2 2 4 3 1 1 2 3 2 4 2 2 3 3 4 5s1 3 1 5v2c-1-2-1-3-2-4-1-2-2-5-4-7-1-1-1-2-2-3v-1h-1c-2 0-3-3-4-4-1-2-2-3-3-4-2-4-6-7-9-10z" class="Q"></path><path d="M386 108c2 0 3-1 5 0 0-1 0-1-1-2-1 0-2 0-3-1-1 0-1-1-2-1h-1l-2-1-1-1c0-1 1 0 1 0h1c1 1 3 2 4 2s2 1 2 1c2 0 4 1 5 1 2 0 3 0 4 1h0v1c-2 0-4 2-6 3-2 0-4 1-6 2-1 0-3 0-4 1-2 0-5 0-8-1-1 0-2-1-3-3v-2c1-2 2-2 3-3h1c1-1 3 0 5-1h1 1c0 1 1 1 2 1h1l-1 1c-2 0-4-1-6 0h-2-1v1h1 1 1c1 1 1 0 2 0h0c1 0 2 0 3 1h3z" class="P"></path><path d="M372 108v-1c1 0 2-1 3-1v1h1 1 1c1 1 1 0 2 0h0c1 0 2 0 3 1h3c-2 1-5 1-7 0h-2-1c0-1-1-1-2-1l-1 2-1-1z" class="h"></path><path d="M372 108l1 1h3c4 2 11 1 15-1-1 2-3 3-5 4h0-2c-3 1-5 1-8 0-2 0-3-1-4-2v-2z" class="j"></path><defs><linearGradient id="z" x1="466.215" y1="128.021" x2="468.283" y2="144.089" xlink:href="#B"><stop offset="0" stop-color="#c6c6c6"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#z)" d="M488 120l1-1c0 2 1 2 2 3 0 1 0 3 1 4v5c-1 2-1 3-2 5-2 3-9 7-13 8h-1-1c-1 1-1 0-2 1h-2l-3 1-1-1h0v1h0-2s-1-1-2-1l-7-5-13-8h0v-1c1 1 2 1 3 1 5 0 9 4 13 6 3 1 5 1 8 2 2 1 6 1 8 0l5-1h0l3-1s1 0 2-1l1-1h1v-1l1-1v-1-1-1c1-1 1-3 1-5l-3-3c-1 0-1 0-1-1h0-1-1l1-1c1 0 2 1 3 2 1 0 1 1 2 1h0v-1h0c0-1 0-2-1-3z"></path><path d="M456 140c1 0 2 1 4 2l1 1s1 0 1-1c-1 0-1-1-2-1h1c2 1 3 1 4 2 3 0 4 1 6 1 1 0 1 0 2 1h-2l-3 1-1-1h0v1h0-2s-1-1-2-1l-7-5z" class="T"></path><path d="M327 97c6 2 13 0 20 0h36 0c-7 1-14 0-21 1h0c1 1 3 0 5 0 1 1 3 0 4 1 6 1 12-1 17 0 2 0 3 0 5 1h1 0c-1 1 0 1-1 1-1-1-3-1-4-1h-4c-1-1-2 0-3 0h-3-7c-2-1-6 0-8 0 1 0 2 1 3 2 1 0 1 0 2 1h-1 0l-3-2h-2v-1c-2 0-3 0-4-1h-6v1h0c1 0 2 0 3 1 2 1 4 2 5 4h0c-1 0-1-1-2-1h0c-1-1-2-1-2-1-3-2-9-3-12-2h-1-1l-3 3c-1 2-1 3 0 4 0 1 1 1 2 2l3 1c-1 1-2 1-3 0-1 0-3-1-3-3-1 0-1-3 0-4 0-1 3-3 4-4h1 2l-1-1c-1 0-2 0-3 1-1 0-3 2-4 3s-2 3-2 5c1 2 1 3 3 4l-1 1c-1-1-2-1-2-2s-1-2-1-2l-1 1h-4v-2c0-1 0-2-1-3 0 0 0-2-1-3h0v-2c-1-1-1-2-1-3z" class="P"></path><path d="M331 100c3-1 7 0 10 0l-5 3c0-1-1-2-2-3h-3z" class="T"></path><path d="M328 100h3 3c1 1 2 2 2 3 0 2-1 4-1 6l-1 1h-4v-2c0-1 0-2-1-3 0 0 0-2-1-3h0v-2z" class="h"></path><path d="M331 101h1v1c1 1 0 2 0 2h-1s-1 0-1-1 1-1 1-2zm-2 4c2 0 2 1 3 1h1l1 2h-3-1c0-1 0-2-1-3z" class="j"></path><path d="M330 108h1 3v2h-4v-2z" class="s"></path><path d="M325 113c1 0 1 0 2 1h0c1 0 1 1 2 2h0l1-1h0l1-2 1 1s1 1 1 2c2 1 3 1 4 2h3 0c2 2 6 1 9 1 0 0 1-1 2-1h1l-1 1h0c4 0 9 0 13 1 19 0 37 0 55 3 4 1 8 3 11 4l8 3 5 2 13 8 7 5c2 2 5 5 7 6h-1c-14-13-34-23-54-27-16-3-34-3-51-3-10 0-20 1-30-1l-1 1 2 4h-1l-1-1-1 1c-1-1-2-2-4-3-1 0-2 0-3 1 0 0 0 1-1 1v-3-1h-1c-1-1-3-1-5-1h0 4 3v-1c-1 0-1-1-2-2l-2-1 4 1h0c1-1 1-2 0-3z" class="N"></path><path d="M324 121s0-1 1-1v1l1-1c1 0 2 0 3 1l-1 1c-1 0-2 0-3 1 0 0 0 1-1 1v-3z" class="H"></path><path d="M329 121h4l2 4h-1l-1-1-1 1c-1-1-2-2-4-3l1-1z" class="U"></path><path d="M325 113c1 0 1 0 2 1h0c1 0 1 1 2 2h0l1-1h0l3 4-4-2h-2c-1 0-2-1-2-1h0c1-1 1-2 0-3z" class="H"></path><path d="M333 116c2 1 3 1 4 2h3 0c2 2 6 1 9 1 0 0 1-1 2-1h1l-1 1h0c4 0 9 0 13 1h-22c-3 0-7 0-9-2v-2z" class="V"></path><path d="M504 105c0-2-1-5 0-7v94c-1-2 0-7 0-10-2-1-2-6-3-8 0-1-1-2-1-3l-3-3s-1-1-1-2h-1v-2l-1-1-1-1v-1h0v-5h0c0-2 1-3 2-4h0c0-1 1-1 1-1 1-1 1-2 3-2h1v-1h1c1 0 1 1 2 2v-1l1-3c-1 0-2-1-3-1l-1-1h0s1 0 1-1c1-2 1-2 1-4v-1c-1 2-2 3-4 4h-1c-1 1-3 4-5 5 2-3 4-7 7-10 1 0 1 0 1-1 1-2 2-4 2-7 0-1 1-1 1-2h0c0-1 0-2 1-3v-4-15z" class="i"></path><path d="M502 167c0 2 0 3 1 4 1 2 1 3 1 4l-1 1c0-1-1-1-1-2s-1-1-1-2c1-1 1-3 1-5z" class="h"></path><path d="M502 129c1 1 1 2 1 4 0 1-2 3-3 4h-1c1 0 1 0 1-1 1-2 2-4 2-7z" class="V"></path><path d="M501 172l-2-3c0-2 0-6 1-7l1-1h0c0-1 1-1 1-1v7c0 2 0 4-1 5z" class="s"></path><path d="M497 168s-1-1-1-2h-1v-2l-1-1-1-1v-1h0v-5h0c0-2 1-3 2-4h0c0-1 1-1 1-1 1-1 1-2 3-2l2 1v1h0l1 1c0 1-1 3-1 4h1v3h-1c-3 3-3 6-4 9z" class="h"></path><path d="M501 151l1 1c0 1-1 3-1 4-2 0-4 0-5 1s0 1-1 1c1-3 4-5 6-7z" class="K"></path><path d="M314 96h2c1 0 1 0 2-1h0l2 6h-3 0v1h2c1 0 2 0 4 1 2 0 3 1 5 0v-1c1 1 1 3 1 3 1 1 1 2 1 3v2l1 1v2l-1 2h0l-1 1h0c-1-1-1-2-2-2h0c-1-1-1-1-2-1 1 1 1 2 0 3h0l-4-1 2 1c1 1 1 2 2 2v1h-3-4-1l-11 1h-1v-3l1-1h0v-1-2h0c1 1 2 0 3 1h1l2-1c-2 0-2 0-4-1-1 0-1-1-2-1v-1h0c-1 0-1 0-2-1v-1-3c1-1 0-1 1-2l1-2h0 5l-1-1h0c0-1 0-2 1-2 0-1 1-1 2-1s1 0 1-1z" class="P"></path><path d="M316 115h-3v-2h4v2h-1z" class="U"></path><path d="M311 109h5 1c0 1 1 1 2 1l1 2h0c-1 1-1 1-2 1h-6 0c-2 0-2 0-4-1-1 0-1-1-2-1v-1l5-1z" class="h"></path><path d="M317 115l1-1h0v1l1 1 3 3h-4-1l-11 1h-1v-3l1-1c1 1 3 1 4 0 2-1 4 0 6-1h1z" class="j"></path><path d="M304 105c1-1 0-1 1-2l1 1 4-1h3c0 1 0 1 1 2h3 3v3c-2 1-7 0-9 0v1l-5 1h0c-1 0-1 0-2-1v-1-3z" class="s"></path><path d="M310 103h3c0 1 0 1 1 2-2 0-4 0-5 1v-1c0-1 1-2 1-2z" class="O"></path><path d="M304 105c1-1 0-1 1-2l1 1 4-1s-1 1-1 2v1c-2 0-3 0-4 2l1 1c1 0 3-1 5-1v1l-5 1h0c-1 0-1 0-2-1v-1-3z" class="M"></path><path d="M314 96h2c1 0 1 0 2-1h0l2 6h-3 0v1h2v1h2v1c-1 0-3 0-4 1h-3c-1-1-1-1-1-2h-3l-4 1-1-1 1-2h0 5l-1-1h0c0-1 0-2 1-2 0-1 1-1 2-1s1 0 1-1z" class="j"></path><path d="M311 101h3 0v1l-1 1h-3l-4 1-1-1 1-2h0 5z" class="q"></path><path d="M314 96h2c1 0 1 0 2-1h0l2 6h-3c-2-1-3-1-4-2v-2c1 0 1 0 1-1z" class="s"></path><path d="M319 102c1 0 2 0 4 1 2 0 3 1 5 0v-1c1 1 1 3 1 3 1 1 1 2 1 3v2l1 1v2l-1 2h0l-1 1h0c-1-1-1-2-2-2h0c-1-1-1-1-2-1 1 1 1 2 0 3h0l-4-1-1-1 1-1h2v-3c-1-1-1 0-2-2h1c1 0 2 0 3-1l-1-1-4-1h-3c1-1 3-1 4-1v-1h-2v-1z" class="g"></path><path d="M321 113c2 0 3 1 4 0 1 1 1 2 0 3h0l-4-1-1-1 1-1z" class="E"></path><path d="M327 114l1-1h0c0-1-1-2-1-2h-1v-1h-1c1-1 1-1 2-1v-1h2 0v1c0 1 1 2 2 2v2l-1 2h0l-1 1h0c-1-1-1-2-2-2h0z" class="J"></path><path d="M464 115h0l2-2c0-1 1-2 1-2 0-1 0-1 1-2 2 0 4 0 7 1h2c1 0 3 1 4 2 1 0 2 1 3 2h0-1l3 3c1 0 2 1 3 2l-1 1c1 1 1 2 1 3h0v1h0c-1 0-1-1-2-1-1-1-2-2-3-2-1-1-1-1-2 0-1 0-1 1-1 2v1 1c-1 0-1 1-1 1 1 1 1 1 1 3 0 1 0 1-1 2s-2 1-3 2h-1-5 0c-4-1-7-3-10-5-1 0-2-2-2-3s0-3-1-4h0c1-2 1-4 1-5 2-2 3-4 6-5h0l1-1-1 1c-1 1-1 1-1 2v1c-1 1 0 0 0 1z" class="j"></path><path d="M461 128v-2c1-1 1-2 1-2 0-1 0-1 1-1l1 1-1 1c0 1-1 2 0 3h2l1 1c2 1 4 2 6 1 3 0 6-1 8-4 1 1 1 1 1 3 0 1 0 1-1 2s-2 1-3 2h-1-5 0c-4-1-7-3-10-5z" class="T"></path><path d="M478 114h0l1-1h2l2 1 3 3c1 0 2 1 3 2l-1 1c1 1 1 2 1 3h0v1h0c-1 0-1-1-2-1-1-1-2-2-3-2-1-1-1-1-2 0-1 0-1 1-1 2l-1-1c0-1 1-1 1-2h3 0 0v-1c-1 0-1-1-2-1h0c-2 0-1 2-2 4l-1 1h0l-2 2v-1c1-1 1-1 1-2s-1-1-1-2v-2c1-2 1-3 1-4z" class="h"></path><path d="M477 118h1c0 1 0 0 1 1 0 0 0 1 1 1-1 1-1 2-1 3l-2 2v-1c1-1 1-1 1-2s-1-1-1-2v-2z" class="T"></path><path d="M481 113l2 1 3 3c1 0 2 1 3 2l-1 1h-2c-1 0-1-1-2-1v-1c-1 0-1 0-2-1h0 2 0c0-1-3-2-4-3l1-1z" class="V"></path><path d="M464 115h0l2-2c0-1 1-2 1-2 0-1 0-1 1-2 2 0 4 0 7 1h2c1 0 3 1 4 2 1 0 2 1 3 2h0-1l-2-1h-2l-1 1h0c0 1 0 2-1 4v2c0 1 1 1 1 2s0 1-1 2v1h-1-1c-1 1-1 1-2 1l-1-1c-2 0-3 0-4-1l-3-3h0c0-1-1-2-1-2v-1c-1-1 0-2 0-3z" class="h"></path><path d="M478 122l-1-1c-1 0-1 1-2 1h0c-1 0-2 1-2 0h-2l-3-3v-1-1c-1-1 0-2 0-3v-1c1 0 1-1 2-2v1c-1 1-1 2-1 4l1 1 1-1s1 0 1 1h2l4-3c0 1 0 2-1 4v2c0 1 1 1 1 2z" class="V"></path><path d="M475 110h2c1 0 3 1 4 2 1 0 2 1 3 2h0-1l-2-1h-2l-1 1h0l-4 3h-2c0-1-1-1-1-1l-1 1-1-1c0-2 0-3 1-4 2-1 3-2 5-2z" class="j"></path><path d="M320 94c2 1 4 2 6 2h1 5 11 50 72 25 14 1v124 1l-1-1v-6c0-2-1-4-1-6-3-9-6-18-10-26-1-3-4-7-5-10h1v1c1 1 1 2 2 3 2 2 3 5 4 7 1 1 1 2 2 4v-2c0-2 0-3-1-5s-2-3-4-5l1-1c1 1 2 2 3 2l3 6c0 1 0 3 1 4v1c0 1 1 2 1 3 1 1 1 3 2 4v-1l1 1v-2-94-1h-8-60-15-38-36c-7 0-14 2-20 0 0 1 0 2 1 3v2h0v1c-2 1-3 0-5 0-2-1-3-1-4-1h-2v-1h0 3l-2-6h2v-1z" class="k"></path><path d="M320 101s1 0 2-1c1 0 0-2 1-3 1 0 3-1 4 0 0 1 0 2 1 3v2h0v1c-2 1-3 0-5 0-2-1-3-1-4-1h-2v-1h0 3z" class="S"></path><path d="M317 101c4 0 7 0 11 1h0v1c-2 1-3 0-5 0-2-1-3-1-4-1h-2v-1z" class="I"></path><path d="M493 174c1 1 2 2 3 2l3 6c0 1 0 3 1 4v1c0 1 1 2 1 3 1 1 1 3 2 4v-1l1 1v2 10 1c0-1 0-2-1-2 0-2 0-3-1-4l-3-9c-1-1-1-3-2-5h0v-2c0-2 0-3-1-5s-2-3-4-5l1-1z" class="h"></path><path d="M373 311h17c7 0 15 0 22-2 8-2 16-8 22-13 8-8 13-16 16-27h18v163h-35c0-17 0-34-10-48-4-7-11-14-19-18-1-1-4-2-6-3-8-3-16-5-25-5h-3-11-18v-1h0c0-3-2-5-3-7h0l-1-1h0 2l-1-4c1-2 4-2 6-3-1-1-2 0-3-1s-1-1-1-2c-1 0-2 1-3 1v-1l-1 1h-1l-2-18v-5-2c2 2 1 5 5 5 1 1 3 0 4 0h-1c0-1-1-1-1-2v-1c-1-1-1-4-2-4v-1h1v-1h12 22z" class="s"></path><path d="M417 363h1c6 6 11 11 16 19 1 3 2 5 3 8 1 1 1 3 2 4 3 9 5 19 5 29l-1 1h0c0-1-1-1-1-1v-3l-1-9c-2-18-9-36-24-48z" class="n"></path><path d="M361 348c5-1 10 0 15 0h7c8 1 16 4 23 7l6 3 4 3 2 2h-1-1l-1-1c-10-8-24-12-37-13h-23c2-1 4 0 6-1z" class="Z"></path><path d="M457 278l1-2 1 1c1 1 0 3 0 5v10 28 26c0 3 1 8 0 12v-1-7h0v8c0 2 0 3-1 5v-20c1-4 0-9 0-13 0-2 1-9 0-10l-1 1h-1c0-1 1-1 2-2 0-3 1-39 0-41h0-1z" class="n"></path><path d="M458 363c1-2 1-3 1-5v-8h0v7 1 65 1l-1-1v-11-16l-4-1c-1 0-3-1-4-1-2-2-4-4-4-8 0 1 2 4 2 5 3 3 6 4 10 4v-25-4c-2 2-4 2-6 3h-3c-1 0-2-1-2-2 1 1 2 1 3 1h1c2 0 4-1 6-2l1-1v-2z" class="d"></path><path d="M457 278h1c-4 8-9 15-14 22-1 1-2 3-4 4 0 1-2 2-2 2-8 7-18 12-29 14-4 1-9 1-13 1h-15-12-7-4c-4-1-8 0-12 0l2-1h-6-1c0-1-1-1-1-2v-1c-1-1-1-4-2-4v-1h1v-1h12c0 1-1 1-2 1v1c0 3 1 4 1 7h30c12 0 25 1 36-3 8-3 17-7 23-13 7-7 13-17 18-26z" class="H"></path><path d="M359 324l-1-1v-1h3 1s0 1 1 1 2 2 3 2c1 1 1 1 1 2 1 3-1 6-3 9-1 2-4 4-6 6h2c0 1-1 1-1 2l-1-1-3 3s1 0 1 1h1c1 0 2 0 3 1h1c-2 1-4 0-6 1h-8c0 2 0 4 1 5s1 2 1 3c5 1 10 1 15 1h6-11-18v-1h0c0-3-2-5-3-7h0l-1-1h0 2l-1-4c1-2 4-2 6-3 1 0 3-1 3-2h1c1 0 3-1 3-1 2-1 4-3 6-4l2-4c1-2 1-2 1-5l-1-2z" class="f"></path><path d="M360 348c-2 0-5-1-8-1-1-1-1-1-1-2 1-1 5-2 7-3h2c0 1-1 1-1 2l-1-1-3 3s1 0 1 1h1c1 0 2 0 3 1z" class="L"></path><defs><linearGradient id="AA" x1="340.25" y1="347.774" x2="345.122" y2="346.433" xlink:href="#B"><stop offset="0" stop-color="#474747"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#AA)" d="M344 342c1 0 3-1 3-2 0 2 0 2-1 3h-1v1h2v1c0 1 0 1 1 2 1 0 1 0 2 1h-2 0 2c-2 1-5 1-7 1s-4 0-5 1h0l-1-1h0 2l-1-4c1-2 4-2 6-3z"></path><path d="M347 345c-1 0-1 1-1 2l-1-1c-1-1-1-1-1-3h1v1h2v1z" class="c"></path><defs><linearGradient id="AB" x1="347.192" y1="334.281" x2="366.185" y2="329.803" xlink:href="#B"><stop offset="0" stop-color="#acaaaa"></stop><stop offset="1" stop-color="#d8d8d8"></stop></linearGradient></defs><path fill="url(#AB)" d="M359 324l-1-1v-1h3 1s0 1 1 1 2 2 3 2c1 3 1 6-1 9l-1 1h0c-2 3-5 5-8 7l-4 1c-2 1-4 1-6 1v-1c1-1 1-1 1-3h1c1 0 3-1 3-1 2-1 4-3 6-4l2-4c1-2 1-2 1-5l-1-2z"></path><path d="M351 339c2 0 4-1 5 0-3 1-5 2-8 4 1 1 2 0 4 0-2 1-4 1-6 1v-1c1-1 1-1 1-3h1c1 0 3-1 3-1z" class="k"></path><path d="M357 335h0 1v1l2-3h1c0 2-1 3-3 5l-2 1h0c-1-1-3 0-5 0 2-1 4-3 6-4z" class="e"></path><defs><linearGradient id="AC" x1="338.688" y1="332.446" x2="344.002" y2="332.173" xlink:href="#B"><stop offset="0" stop-color="#3e3d3d"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#AC)" d="M333 317v-2c2 2 1 5 5 5 1 1 3 0 4 0h6l-2 1c4 0 8-1 12 0h4l-1 1h-3v1l1 1 1 2c0 3 0 3-1 5l-2 4c-2 1-4 3-6 4 0 0-2 1-3 1h-1c0 1-2 2-3 2-1-1-2 0-3-1s-1-1-1-2c-1 0-2 1-3 1v-1l-1 1h-1l-2-18v-5z"></path><path d="M358 321h4l-1 1h-3v1l1 1c-1 0-2-1-3-1-2-2-4-1-7-1v-1h9z" class="e"></path><path d="M347 334v-1h-2v-1-4c-1-1-2-3-2-4l1-1h2c0 1 0 3 1 4l1 1v1c-1 1-2 2-2 3l1 1v1z" class="S"></path><path d="M336 322h2c0 1 0 2 1 3 0 2 1 3 2 4v4c-1 1-3 2-4 3h2c0 1 0 1 1 2h0c1-1 2-1 3-1l-3 2c-1 0-2 1-3 1v-1h0 0l-1-1v-3c0-4 1-9 0-13z" class="B"></path><path d="M337 336h2c0 1 0 1 1 2h-3 0v-2z" class="C"></path><path d="M333 317v-2c2 2 1 5 5 5 1 1 3 0 4 0h6l-2 1c-2 0-10-1-11 0v1h1c1 4 0 9 0 13v3l1 1h0 0l-1 1h-1l-2-18v-5z" class="a"></path><path d="M346 323h4l1 1c-1 1-1 2-1 3l1 1v-1-2-1c1 0 1-1 2-1 3 0 3 1 5 2 1 1 1 1 1 2 1 0 1 0 1-1 0 3 0 3-1 5l-2 4c-2 1-4 3-6 4 0 0-2 1-3 1h-1c0 1-2 2-3 2-1-1-2 0-3-1s-1-1-1-2l3-2 1-1c0-1 0 0 1-1h0l2-1v-1l-1-1c0-1 1-2 2-3v-1l-1-1c-1-1-1-3-1-4z" class="f"></path><path d="M350 327h-1v-3l1-1 1 1c-1 1-1 2-1 3z" class="o"></path><path d="M353 323c0 2 1 2 2 4l-1 1h-1l-1 1c-1-1-1-3-1-5 1 0 1-1 2-1z" class="l"></path><path d="M350 338l-1-1c0-1 1-2 1-2 2-1 3-1 5-1 0 1-1 1 0 2-1 1-3 2-5 2z" class="k"></path><path d="M355 327l1-1c1 1 1 1 1 2l1 1-2 2h-2l-2-2 1-1h1l1-1h0z" class="P"></path><path d="M355 327h0v3c0 1 0 1 1 1h-2l-2-2 1-1h1l1-1z" class="K"></path><path d="M358 329l1-1v1 2h0c-1 2-2 3-4 5-1-1 0-1 0-2h0c-1-1-3-1-4-2 0 0 0-1 1-1 0 0 1 0 2 1h2c1-1 2-2 2-3z" class="e"></path><path d="M353 323c3 0 3 1 5 2 1 1 1 1 1 2 1 0 1 0 1-1 0 3 0 3-1 5h0 0v-2-1l-1 1h0l-1-1c0-1 0-1-1-2l-1 1h0c-1-2-2-2-2-4z" class="O"></path><path d="M347 334v-1l-1-1c0-1 1-2 2-3 0 1-1 1 0 2v2 1c-1 0 0 1-1 2v1l1 2s1 0 2-1c2 0 4-1 5-2 2-2 3-3 4-5h0l-2 4c-2 1-4 3-6 4 0 0-2 1-3 1h-1c0 1-2 2-3 2-1-1-2 0-3-1s-1-1-1-2l3-2 1-1c0-1 0 0 1-1h0l2-1z" class="n"></path><path d="M343 337l1-1c0-1 0 0 1-1-1 2-1 2-1 4l1 1h2 1-1c0 1-2 2-3 2-1-1-2 0-3-1s-1-1-1-2l3-2z" class="D"></path><path d="M458 278h0c1 2 0 38 0 41-1 1-2 1-2 2h1l1-1c1 1 0 8 0 10 0 4 1 9 0 13v20 2l-1 1c-2 1-4 2-6 2h-1c-1 0-2 0-3-1 0 1 1 2 2 2h3c2-1 4-1 6-3v4 25c-4 0-7-1-10-4 0-1-2-4-2-5 0 4 2 6 4 8 1 0 3 1 4 1l4 1v16 11l1 1h-16l1-1c0-10-2-20-5-29-1-1-1-3-2-4-1-3-2-5-3-8-5-8-10-13-16-19l-2-2-4-3-6-3c-7-3-15-6-23-7h-7c-5 0-10-1-15 0h-1c-1-1-2-1-3-1h-1c0-1-1-1-1-1l3-3 1 1c0-1 1-1 1-2h-2c2-2 5-4 6-6 2-3 4-6 3-9 0-1 0-1-1-2-1 0-2-2-3-2s-1-1-1-1h-1l1-1h7 12 15c4 0 9 0 13-1 11-2 21-7 29-14 0 0 2-1 2-2 2-1 3-3 4-4 5-7 10-14 14-22z" class="j"></path><path d="M374 324h1c1 0 1 0 1 1l-1 1v4l1 1c-1 0-1 0-1 1h-1c-1-1-1 0-2-1 1-1 1-1 1-2h0c0-3-1-3 1-5z" class="i"></path><path d="M454 334l1-1h1v2l-1 1c0 1 0 2-1 3-1-1-3-1-4-1-1 1-1 1-2 1 0-1-1-1-1-2 1-1 3 0 4-1s2-1 3-2zm-2 35c2-1 4-1 6-3v4c-1 0-2 1-3 2h-4l-1-1c-1 0-1 0-2-1h1c1-1 2-1 3-1z" class="s"></path><path d="M449 350c-2 1-3 1-4 2-7-2-11-2-16-7h1c2 1 2 2 3 3 1 0 2 1 3 0 0-1 1-2 1-3l1 1h0l-1 3v1h3v-1c0-1-1-1-1-2h1c1 1 1 1 2 3h1c2-1 3 0 6 0zm-70-26h2s1 0 1 1c2 2 2 2 2 5l-1 1h-1-1-1c-1 1-1 1-1 2l1 2h0-1s0-1-1-1v-1c0-1 0-2 1-2v-1c1-2 1-4 0-6z" class="T"></path><path d="M433 354c4 2 10 2 15 1l1-1h0a30.44 30.44 0 0 0 8-8v1c0 2-3 5-5 6-4 3-9 4-14 4-1 0-2-1-4-1-1-1-1-1-1-2z" class="e"></path><path d="M393 326l1-1c1 1 2 1 3 2h0-3c-1 0-2 1-3 3v4c-1 6-9 6-14 9v1h-2-1l2-2c4-2 9-2 12-6 1-1 3-6 2-8v-1s2-1 3-1z" class="T"></path><path d="M454 334h-1l4-4h1c0 4 1 9 0 13v-3h-1c-1 4-3 6-6 8h-1v-1c2-1 3-2 4-3l1-1s0-1 1-2c0-1 0-1-1-2 0 1 0 1-1 1h0 0v-1c1-1 1-2 1-3l1-1v-2h-1l-1 1zm-30 20c2 0 2 1 4 2 1 0 1 1 2 1 4 3 9 6 13 9 1 2 3 3 4 5v1l1 1v1l1 1v1l1 1v2 1l1 2h1c1 0 1 1 2 1l-1 1v-1h-2c-2-1-2-5-3-6v-1-1l-1-2s0-1-1-1v-1l-1-1c-2-2-3-4-5-5-1-1-2-1-3-2-1 0-2-1-3-2-4-1-7-4-10-7z" class="L"></path><path d="M360 348c-1-1-2-1-3-1h-1c0-1-1-1-1-1l3-3 1 1c3 1 7 0 10 0l-1 1c-2 1-4-1-6 1l1 1c1 0 3 0 4-1h1 1l-1-1c2 2 4 1 7 1 2 0 5-1 7 0h-1c-2 1-6 0-8 1h-1 0 2 0c2 1 5 0 6 1h3-7c-5 0-10-1-15 0h-1z" class="P"></path><path d="M418 357h0c-1 0-1-1-1-1v-1c-1 0-1-1-1-1v-1l1 1h1c-1-2-1-3-1-5-1-4-3-9-3-13l1 1 1 5c0 5 3 12 6 16 2 2 4 4 5 6v1l4 4c0 1 1 2 1 3h0c-1-1-1-2-2-3l-3-3c1 2 3 4 3 6 0 0-8-12-12-15z" class="T"></path><path d="M362 321h7 12 15-1c-5 1-12 1-18 1-1 0-4-1-5 0h-5-3 1l2 2v1c1 1 1 4 1 5v1c-1 2-3 4-3 6s-1 3-2 4h-1v1h1 1 3 1 0 3c1-1 2-1 3-1l-4 2-1 1c-3 0-7 1-10 0 0-1 1-1 1-2h-2c2-2 5-4 6-6 2-3 4-6 3-9 0-1 0-1-1-2-1 0-2-2-3-2s-1-1-1-1h-1l1-1z" class="P"></path><path d="M360 342c2 0 4 1 6 1h4l-1 1c-3 0-7 1-10 0 0-1 1-1 1-2z" class="M"></path><path d="M424 354h0c-3-3-6-9-6-14v-3h0 1l-1-1c0-1 0-2 1-3 0 1 0 2 1 3 1 8 6 13 13 18 0 1 0 1 1 2v1h-1c-1 0-2-1-3 0-1 0-1-1-2-1-2-1-2-2-4-2z" class="h"></path><path d="M430 357c1-1 2 0 3 0h1c3 2 11 3 15 2 2-1 4-1 6-1l2 2-2 2-3 2h-2 6 0v1l1 1c-2 1-4 2-6 2h-1c-1 0-2 0-3-1h0l-3-3-1 1h0v1c-4-3-9-6-13-9z" class="e"></path><path d="M452 359c1 0 2 0 3 1h-1c-2 1-4 4-6 3l-5-3c3 1 6 1 9-1h0z" class="j"></path><path d="M430 357c1-1 2 0 3 0 2 1 5 3 7 4 3 0 6 3 9 4h0 7l1 1c-2 1-4 2-6 2h-1c-1 0-2 0-3-1h0l-3-3-1 1h0v1c-4-3-9-6-13-9z" class="s"></path><path d="M416 361c0-2-2-4-2-6-1-1-1-2-1-3l-1-2c-1-3 0-5-1-7v-1h1c0 1 0 2 1 3v5c0 1 1 2 1 3 0 2 1 3 2 4v1c2 3 5 5 7 7-1-2-2-4-3-5s-1-2-2-3c4 3 12 15 12 15 0-2-2-4-3-6l3 3c1 1 1 2 2 3h0c0-1-1-2-1-3l-4-4v-1c4 4 8 9 11 14 1 3 0 6 0 8 0 1 0 2-1 4-1-3-2-5-3-8-5-8-10-13-16-19l-2-2z" class="V"></path><path d="M436 378h0c1 1 1 2 1 4h0c-1-1-1-3-1-4z" class="L"></path><path d="M382 346h5 7 4c1 0 2-1 3-1l4-4v-1s1 0 1-1 1-2 1-3 0-2 1-3c1 1 0 2 0 3l1 3v12l3 6v1l-6-3c-7-3-15-6-23-7h-3c-1-1-4 0-6-1h0-2 0 1c2-1 6 0 8-1h1z" class="T"></path><path d="M409 339v12l3 6h-2c0-2-1-4-2-6 0-3-1-9 1-12z" class="L"></path><path d="M439 394v-1-1c0-1 0-1 1-2v-1c0-1 0-2 1-2 1 2 0 6 0 9l1 1v1 1c1 1 1 2 1 4 1-2 0-2 0-3l1-1c1 1 0 2 1 4v1h0v-6l-1 1-1-1 1-1h1c0 1 1 1 1 1v1 3h0 0c1 1 1 2 2 2h1c1 0 1 2 2 0h1v-1c1 1 1 1 1 3-1 0 0 0-1 1 0 0 0 1-1 1h-2c-1-1-1-1-1-2h-1 0v2h0c1 1 1 2 2 3h1c1 1 1 2 2 2v-2c1 0 2 1 2 0v-1c1-1 1-2 1-3-1-1-1-3-1-3-1-2-2-5-3-5v-1h1c1-1 1-2 2-3l4 1v16 11l1 1h-16l1-1c0-10-2-20-5-29z" class="T"></path><path d="M444 423h5l1-1h-1c-1 0-2-1-2-2v-2h0 0c0 1 0 2 1 3 2 1 5 2 8 1l1-1v1 1h1l1 1h-16l1-1z" class="f"></path><path d="M458 278h0c1 2 0 38 0 41-1 1-2 1-2 2h-2l-1-1s-1 0-1-1c-1 0-1 1-2 1l-1 1-1-1c1 0 2-1 2-2l1-1v-3h-1c0-1 0-1-1-2v-1c-1-1-2-1-3-1-2 0-4 1-5 2h0v1c-1 1-2 2-2 3v1c-1 1 0 1 0 1 0 2 0 2 1 3h2c1 0 1 0 2-1v1l-1 1c-1 1-1 0-1 1s-1 2-1 3h0v5c1 0 1 0 1 1 1 1 1 2 1 3s0 2 1 3h0c0 1 1 2 1 3 1 0 1 1 2 1v1h3l1-1v1c-1 1-2 1-4 1-1-1-3-4-4-4v2c0 1 2 2 3 3h0c-2 0-4-2-4-3l-1-2h-1v3c2 2 3 4 6 4h0c2 1 2 0 4 1-1 1-1 1-1 2-3 0-4-1-6 0h-1c-1-2-1-2-2-3h-1c0 1 1 1 1 2v1h-3v-1l1-3h0l-1-1c1 0 1-1 1-1v-1h1c0-2 0-4-1-6-1 1-2 3-2 5v1c-1 1-2 1-2 2l-1-1 1-1c2-3 2-5 2-8v-1h-1v3c-1 0-1 1-1 2-1 0-2 2-2 2-1 0-1 1-2 1s-2 0-3-1h3c0-3 2-3 2-6 0-1 1-1 1-3h0c1-4 1-8 2-11 0-1-1-1 0-2 1 1 1 1 1 2v-1s0-1-1-1h0v-3h1c1-3 1-4 4-6h0c-2 0-3 2-4 4 0 0 0 1-1 1v1-2c0-3 1-4 3-6h-1l1-1v-1s2-1 2-2c2-1 3-3 4-4 5-7 10-14 14-22z" class="h"></path><path d="M453 304c1-2 1-2 2-3h1c0 2-1 3-1 4-1 0-1-1-2-1z" class="s"></path><path d="M455 305l-1 1h-1l-2-1h0c-1-1-1-1-1-2l1 1 1 1v-1-1l1 1h0c1 0 1 1 2 1z" class="j"></path><path d="M454 311l1 1-1 2h0c-2-4-4-4-7-7h1c2 1 4 3 6 4z" class="V"></path><path d="M448 307v-4h0v1c1 1 2 2 3 2l1 1h2l2-2 1 1c-1 1 0 3-1 4l-2 1c-2-1-4-3-6-4z" class="j"></path><path d="M312 74c0 2 1 4 0 5v5 1c0 1-1 1 0 2l2 2c0 1 0 2-1 3v1l-2 2h0c1 1 2 1 3 1 0 1 0 1-1 1s-2 0-2 1c-1 0-1 1-1 2h0l1 1h-5 0l-1 2c-1 1 0 1-1 2v3 1c1 1 1 1 2 1h0v1c1 0 1 1 2 1 2 1 2 1 4 1l-2 1h-1c-1-1-2 0-3-1h0v2 1h0l-1 1v3h1-1c1 2 1 3 1 5h-1v4c0-1 1-1-1-2 0 1-1 1-1 2-1 0-3 1-3 2v6h1s0 1 1 1l-2 2s-1 1-1 2h0c1 1 0 3 0 4-1 3-1 6-1 9-1 1-1 3-2 4 0 1 0 1 1 2h1-3c-1 0-1 0-2 1h-2 0v2c1 2 0 4-1 6l-1 7c0 3-1 6-1 9v1c0 1-1 2 0 4 1 0 2 0 3-1h0v6l1 1c1 0 4-1 5-1 1-1 2 0 3 0-1 0-3 0-4 1h0c2 1 2 1 3 1h1c1 0 1 0 2-1l1 1h-1c-1 1-1 3 0 4h1-1-1c-1 1-1 0-2 1 1 1 1 1 1 2 0 2-2 4-1 6 1 0 1 0 2 1 0 1 1 3 0 4l-1 1h-1l-1 1c0 2 0 3-1 5 0 1 1 2 1 3l2 1h-2c0 2 0 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 1 1v1c0 1 0 1 1 2 1 0 3 0 4 1l-5 1-2 1h0 3 0c-2 0-4 0-5 1h-2-1c-2 1-3 1-4 1h0c-1 1-1 2-2 3l-2 2s-1 0-1 1h0c-1 0-3-1-4-1h0-1c0 1 0 2-1 3v-3c-1-1-2-1-3-2v3l-1 1h-4l1 1c0 3 1 6 2 9h1l1 2 2-2 4-4c1 1 1 7 1 9 0 1-1 4-2 5l-1 1h0 1 3c0 1-1 4-1 5l1 1c-1 0-2 0-3 1v1l2-1v1 1 1 1c1 0 2 0 3-1 2 0 3 0 4-1v1l4 1h0-6c-2 1-3 2-5 1l-1 1 1 1-2 1 1 2v1l-1 1v2h1v2c-1 1-2 1-2 3h-2l1 2h-1 3c1 0 1-1 2 0h0l1 1c0 1-1 1-2 2h2l1-1c1 1 1 1 1 3 0 1 0 2 2 2 0 1 1 1 2 1s2 0 4 1h0c2 1 3 0 5 1 1 0 2 1 3 2h0 1c1 2 1 3 2 4-1 2-1 3-1 4v3l-1 7-2 17v6l-1 1h1c1 0 1 1 2 1l3 1v3h0c-2 0-3 0-4 1h-1c-1 0-2 1-2 2l1 1-7 1h-1c0 1 0 1 1 2h1l-1 1h1c2 0 3 0 4-1 1 1 1 2 3 2v1 1c-1-1-2-1-3-1 0 1 1 2 1 2v1l-2 1h0l-6 4c1 1 0 1 1 1h-1c-2 1-5-1-7 0h0v2h1v2l1 1-1 3 1 2h-1v2c0 1 0 1 1 2h1v1 2 1 1l2 1v1c1 0 1 1 1 1l1 1v1 1h3v1h-3v1h1 0v1h2c2 0 4 0 6 1 2 0 4 1 6 2l2 1 6 3 1 1h0 1c1 2 1 5 1 6v1 2h1 0 5l2 1v1l-1 1h0 0c1 2 0 5 0 6 1-1 1-2 1-4 1 0 1-1 1-2l2-1h2v3h0 2 1v2c-1 0-1 0-1 1v1s1 1 1 2h1 0 0v2c0 2 1 3 2 5l1 1v4 3 11l-1-1-1 1v-1l-1 2c-1 1-1 1-2 0h0l-2 1 1 2 1-1c1 1 1 2 1 3v9c0 2 1 9 0 10h-1 1v1h-1c0 2 0 5-2 6h0c-1-1-2-1-3-1v1c0 4-1 8 0 11h2v2h0 2c1-1 1-1 1 0 1 0 1 0 1 1-1 2 0 2 0 4 1 0 1 0 1 1v4 1 3c1 1 0 1 0 2v1 1 5 2h-1-1v3h-1 0c-1 0-1-1-2-1h-2l-1 2h1v2h-18-1v-1c-1 0-1-1-1-1h-1 0c-2 2-2 1-3 1 0 0-1 1-1 2-2 0-3 1-4 3l-1 1h0l-1 1c2 1 5 1 8 1v1c-3 0-7-1-9 0-1 1-1 1 0 2 0 1 0 1-1 2v1h0c0 1-1 2-1 3 1 1 1-1 2 1 0 1 0 1-1 2v6l1-1h0c0-1-1-2 0-3s1-2 1-4v-1c1 0 1-1 1-1v-1-1-1l1 2c-1 3-2 5-1 8h-1-1c0 1 0 1 1 1v2h-1v2l-1 1c-1-3-1-7 0-10h-3c0-1 0-1-1-1h-2c-1 0-3 0-4-1l-14 1v-4c-1 1-2 0-2 1l-1 2v3c1 1 1 2 1 3h-2c-1 0-2 1-2 1h-6-8v-2c0-1 1-2 2-4-1-1-1-1-1-3l1-3c-1-6-2-11-1-17v-30-5-12-36-187-57c0-12 0-23-1-35 0-7-2-13-4-20-4-9-11-18-20-23-2-2-5-3-8-4-15-6-31-5-46-5l-1 1v1c-2-1-2-3-3-4l-5-11h-1c-1-1-2-4-2-6-1-2 0-2 1-3l-5-7h155c2-4 3-9 4-14z" class="s"></path><path d="M158 95c2 3 4 7 5 10v1h-1l-1-2h-1 0 0-1c-1-1-2-4-2-6-1-2 0-2 1-3z" class="B"></path><path d="M160 104h0 0 1l1 2h1v-1c2 4 4 9 6 12l-1 1v1c-2-1-2-3-3-4l-5-11z" class="C"></path><path d="M257 399l1 30v9s1 1 2 1c0 1 1 1 1 1 2 1 2 2 5 2v-1l2-2c-1 0-2 1-3 1-2 0-2-1-4-2l1-1c1 1 2 2 3 2l2-2c0-1-1-2-1-2v-1c0-1 1-1 2-2h1 1c1 1 1 2 2 3-1 3-2 4-4 6-2 1-2 2-4 2-3-2-4-3-6-5v6c-1 6 0 12 0 17h0c-1-2-1-5-1-7v5 13c0 3 0 6-1 8v-80c1 2 0 6 1 7h0v-8z" class="f"></path><path d="M262 437c1 1 2 2 3 2l2-2c0-1-1-2-1-2v-1c0-1 1-1 2-2h0c1 1 2 1 3 3 0 2-1 3-3 5 0 0-1 1-2 1l2-2c-1 0-2 1-3 1-2 0-2-1-4-2l1-1z" class="e"></path><path d="M248 501c1 5-1 10 0 15 1 2 1 4 1 5v9h0v-14c2-1 0-3 1-5l1 1v2 6c1-2 1-3 2-4 0-1 2-3 2-5 1-1 0-2 0-3v-11c0-1 1-3 1-5v26 1 1l-3 9c-1 3-1 7-3 9l-1 1h-1c-1-2 0-6 0-8v-30z" class="Q"></path><path d="M171 100l1-1h0v2l2 3c0 2 1 3 2 4 15 0 29 0 43 5 5 2 9 4 13 7l4 3 3 3 4 5c2 3 4 5 6 8-1-8-2-18 3-26 5-7 11-11 19-13h7c4 1 8 2 11 4h-1c-5-2-10-3-17-2h0c-7 1-12 4-16 10-4 4-5 8-6 14v11c0 4 3 8 4 11 1 2 1 4 2 7 1 6 3 13 2 19v3 16c0 3 1 6 0 9v-4h0c-1 1 0 2-1 3v7s1 1 0 1v-5-11c0-11 1-24-1-35-2-7-5-14-9-20-2-5-5-8-8-12-17-17-41-17-63-17l-4-9z" class="N"></path><path d="M312 87l2 2c0 1 0 2-1 3v1l-2 2h0c1 1 2 1 3 1 0 1 0 1-1 1s-2 0-2 1c-1 0-1 1-1 2h0l1 1h-5 0-5-4c-2-1-4 0-6 0-2-1-4-1-6-1l1-1s-1 0-1-1h11 0 0l-35-1h-4-10-46-30l1 2-1 1-2-4h137 1v-4c0-1 1-3 2-4s2-1 3-1z" class="M"></path><path d="M296 98l7-1c1 0 2 0 3 1v2c-6 0-11 0-16-1-2 0-3 1-4 0 0 0-1 0-1-1h11 0z" class="s"></path><path d="M256 209c1 0 0-1 0-1v-7c1-1 0-2 1-3h0v4 6l1 69v26 3l1 1h0 1c0 3 0 6-2 9v4 18c-1 6-1 12 0 18v8l-1 35v8h0c-1-1 0-5-1-7v-4-9-38-92-48z" class="g"></path><path d="M258 303v3l1 1h0v2 1c-1 1-1 1-1 2v-9z" class="h"></path><path d="M259 307h1c0 3 0 6-2 9v4-8c0-1 0-1 1-2v-1-2z" class="T"></path><path d="M258 364v7c1-1 3-4 5-5h2c1 0 2 0 4 1h1 1l2-1c1 1 0 1 0 3 1 0 1 1 1 1h1 1c0-2-1-3-2-4 0-2-1-3-2-4 0-1-1-1-2-2v1c0 1 0 1 1 2h0v2c-1-1-1-1-2-1l-1-1c-1-1-2-1-3-2-1 0-1 0-2-1h-1c-1 1-1 2-2 3h0v-3c1-1 2-2 4-2h1l1-1c0 1 0 1 1 2 1 0 1 0 2-1 1 1 3 2 4 4l2 3c1 2 2 2 3 4 1 0 1 1 2 1v2h-1c0 1 0 3-1 4h-4-1-1-1 1c1-1 2-1 3-2-1-2 0-2-1-3v1 1h0c-1 0-1-1-1-1-1-1-2-3-3-3h-1v-1c-2-1-4-1-6-1-2 2-3 2-3 4l-2 2v5 9 6c1-2 0-4 2-6-1 5-2 11-1 16h1c1 5 3 9 7 11 3 2 6 2 9 2 1 0 1 0 1 1v1c-1 1-1 1-2 1-2-1-3-1-5-1l-1 1c-1 1-2 1-3 1-1 1-1 2-1 3s-1 1-2 2h0v1c0 1-1 2-1 3h-1c1-1 1-3 1-4-1 1-2 4-2 6s0 4 1 7c2 1 2 2 4 2 1 0 2-1 3-1l-2 2v1c-3 0-3-1-5-2 0 0-1 0-1-1-1 0-2-1-2-1v-9l-1-30 1-35z" class="i"></path><path d="M256 480c1-2 1-5 1-8v-13-5c0 2 0 5 1 7h0v28c0 3-1 8 0 11v4h2c0-1 0-3 1-4 1 1 0 3 1 4l1 2c0 1-1 1-1 1v4h0c-1 1-2 1-2 2v1 6 1c1 1 4 1 6 1h18-1-1c6 1 11 1 17 1v1h1v-1s1 0 1 1v1l1 1c-1 1-1 2-1 3h-1c-1 0-1 0-1-1h-2-3-1-3c-1 1-2 1-3 0h-6l-1 2h-4-2-6-1l-7-1v-1h3 0l-2-2v-1h2-2c-2 0-3-1-4-1v-6h-1v-26-12z" class="R"></path><path d="M274 525h10v3h-3 0c-1-2-5-3-7-3z" class="i"></path><path d="M258 500v4h2c0-1 0-3 1-4 1 1 0 3 1 4l1 2c0 1-1 1-1 1v4h0c-1 1-2 1-2 2-1 2-1 4-1 6h-1v-17-2z" class="l"></path><path d="M299 524h1v-1s1 0 1 1v1l1 1c-1 1-1 2-1 3h-1c-1 0-1 0-1-1h-2-3-1-3c-1 1-2 1-3 0h3c-1-2-2-2-4-3h3-3c4-1 9-1 13-1z" class="I"></path><path d="M301 525l1 1c-1 1-1 2-1 3h-1c-1 0-1 0-1-1h-2v-1h1v-1l-1-1c1 0 3 1 4 0z" class="H"></path><path d="M289 525h8l1 1v1h-1v1h-3-1-3c-1 1-2 1-3 0h3c-1-2-2-2-4-3h3 0z" class="p"></path><path d="M289 525h8l1 1v1h-1v1h-3-1l1-1 1-1c-2-1-4 0-5-1h-1z" class="S"></path><path d="M256 480c1-2 1-5 1-8v-13-5c0 2 0 5 1 7h0v28c0 3-1 8 0 11v2h-1v1 1 7c1 2 0 3 0 5v2-16h0v16h-1v-26-12z" class="d"></path><path d="M263 525h11c2 0 6 1 7 3h0 3v-3h2c2 1 3 1 4 3h-3-6l-1 2h-4-2-6-1l-7-1v-1h3 0l-2-2v-1h2z" class="j"></path><path d="M284 525h2c2 1 3 1 4 3h-3-6-18 0 18 3v-3z" class="K"></path><path d="M263 528h18l-1 2h-4-2-6-1l-7-1v-1h3z" class="c"></path><path d="M279 507h0c0 1 0 3 1 3v-2-1h1c1 1 1 3 1 4h0c1 1 1 1 2 1h0l10 1h2v-6l1 4v1c1 1 3 1 4 2v1 9c0-1-1-1-1-1v1h-1v-1c-6 0-11 0-17-1h1 1-18c-2 0-5 0-6-1v-1-6h1 0c1-2 3-2 4-2h7 2 0l-1-1v-3l1-1h0 4 0 0 0 1z" class="i"></path><path d="M262 514l2 1v4h-2v-5z" class="G"></path><path d="M260 514h1s0 1 1 0v5h2c-2 1-3 1-4 1v-6z" class="M"></path><defs><linearGradient id="AD" x1="278.973" y1="518.331" x2="270.086" y2="508.451" xlink:href="#B"><stop offset="0" stop-color="#d1cad0"></stop><stop offset="1" stop-color="#dfe3e2"></stop></linearGradient></defs><path fill="url(#AD)" d="M279 507h0c0 1 0 3 1 3v-2-1h1c1 1 1 3 1 4h0c1 1 1 1 2 1h0l10 1c-2 1-5 1-7 1h-4c-1 1-2 1-3 1l-1 1v2-2-1h-1v1s-1 0-1 1h-1l-1-1c-1 0 0-1-1-1h-2c0 1 0 1-1 1v-1s0-1-1-1c0 0-1 1-2 1h0-1-1l-1-1v-2h7 2 0l-1-1v-3l1-1h0 4 0 0 0 1z"></path><path d="M279 507h0c0 1 0 3 1 3v-2-1h1c1 1 1 3 1 4h0c1 1 1 1 2 1h0-10 0l-1-1v-3l1-1h0 4 0 0 0 1z" class="c"></path><path d="M280 507h1c1 1 1 3 1 4h0-2v-4zm-6 0c1 2 1 2 1 4 0 0-1 0-1 1l-1-1v-3l1-1z" class="a"></path><path d="M278 507l1 1v3c-1 1-2 0-3 0 0-1 1-2 2-4h0 0 0z" class="U"></path><path d="M296 507l1 4v1c1 1 3 1 4 2v1 9c0-1-1-1-1-1v1h-1v-1c-6 0-11 0-17-1h1 1v-1l1-1c-1-1-1-1-1-2h-1 0v1h-3l-1-1v-2l1-1c1 0 2 0 3-1h4c2 0 5 0 7-1h2v-6z" class="G"></path><path d="M292 519h0c2 1 4 1 5 0s0-3 1-3h1c0 2 1 4 0 6h-1c0-1 0-1-1-2h-3c-1 0-4 0-5-1h3z" class="H"></path><path d="M288 515h2v3h0l1-1v-2c2 0 2 0 3 1v2h-1 0v-1c-1 1-1 1-1 2h-3c0-1 0-2-1-4h0z" class="X"></path><path d="M286 519v-4h1 1 0c1 2 1 3 1 4 1 1 4 1 5 1h3c1 1 1 1 1 2h-8l1-1v-1h-4l-1-1z" class="N"></path><path d="M286 519v-4h1 1 0v4h-2z" class="B"></path><path d="M279 518v-2l1-1c1 0 2 0 3-1h4v1h-1v4l1 1h4v1l-1 1h-6v-1l1-1c-1-1-1-1-1-2h-1 0v1h-3l-1-1z" class="L"></path><path d="M261 384l1 1c-3 7-3 16 0 23 1 3 5 6 7 6l3 1h5v-6h1l1 1h2 1v3 6c1 1 2 1 3 0h0c1-1 1-4 1-6h3v1 6h2l1 1h-2c0 1 0 2-1 3l-1 2h2 1l1 1h0 0c1 1 1 2 2 3h-1-3 0-7-5-3-3v1l-1 1h-1-1-1c-1 1-2 1-2 2v1s1 1 1 2l-2 2c-1 0-2-1-3-2l-1 1c-1-3-1-5-1-7s1-5 2-6c0 1 0 3-1 4h1c0-1 1-2 1-3v-1h0c1-1 2-1 2-2s0-2 1-3c1 0 2 0 3-1l1-1c2 0 3 0 5 1 1 0 1 0 2-1v-1c0-1 0-1-1-1-3 0-6 0-9-2-4-2-6-6-7-11h-1c-1-5 0-11 1-16l1-3z" class="P"></path><path d="M269 427v-1h1c-1-1-1-1-1-2-1 0 0-1 0-2l2-2c1 0 2 1 3 1v1h2v2 1h-2v1l1 1c-2 1-3 1-4 1s-1-1-2-1z" class="L"></path><path d="M274 422h2v2 1h-2v1l1 1c-2 1-3 1-4 1v-2h1c2-2 2-2 2-4h0z" class="K"></path><path d="M274 422h2v2 1h-2v-3h0z" class="U"></path><path d="M261 384l1 1c-3 7-3 16 0 23 1 3 5 6 7 6l3 1h5v-6h1l1 1h2 1v3 6c1 1 2 1 3 0h0c1-1 1-4 1-6h3v1 6h2l1 1h-2-3-5v1l-2 1h0-1c-1-1-1-1-3-1h-2v-1h3 5l1-1h-2c1-2 0-5 0-7h0c-1-1-2-1-2-2v-1l-1 1c0 1-1 7 0 8h2v1h-1c-2 0-7 0-8-1h0 4c1 0 1 0 2-1v-1c0-1 0-1-1-1-3 0-6 0-9-2-4-2-6-6-7-11h-1c-1-5 0-11 1-16l1-3z" class="N"></path><path d="M274 421h3 5v1l-2 1h0-1c-1-1-1-1-3-1h-2v-1z" class="H"></path><defs><linearGradient id="AE" x1="279.493" y1="436.234" x2="265.915" y2="426.729" xlink:href="#B"><stop offset="0" stop-color="#c1bfc1"></stop><stop offset="1" stop-color="#e4e4e4"></stop></linearGradient></defs><path fill="url(#AE)" d="M290 421c0 1 0 2-1 3l-1 2h2 1l1 1h0 0c1 1 1 2 2 3h-1-3 0-7-5-3-3v1l-1 1h-1-1-1c-1 1-2 1-2 2v1s1 1 1 2l-2 2c-1 0-2-1-3-2v-2-2c0-1 1-3 1-3 2-1 4-3 6-3 1 0 1 1 2 1s2 0 4-1l-1-1v-1h2v-1-2c2 0 2 0 3 1h1 0l2-1v-1h5 3z"></path><path d="M290 421c0 1 0 2-1 3l-1 2h2 1l1 1h0 0c1 1 1 2 2 3h-1-3 0-7l3-1 1-1-3-2c-3 0-6 0-9 1l-1-1v-1h2v-1-2c2 0 2 0 3 1h1 0l2-1v-1h5 3z" class="d"></path><path d="M290 421c0 1 0 2-1 3v-1l-1 1c-1-1-1-2-1-3h3z" class="R"></path><path d="M285 425l1-2h0c1 1 1 2 2 3l4 1h0 0c1 1 1 2 2 3h-1-3 0v-1c0-1-2-2-3-3h0s0-1-1-1h-1z" class="f"></path><path d="M276 422c2 0 2 0 3 1h1 0c1 0 1 0 2 1 1 0 1-1 2-1v3h1v-1h1c1 0 1 1 1 1h0c1 1 3 2 3 3v1h-7l3-1 1-1-3-2c-3 0-6 0-9 1l-1-1v-1h2v-1-2z" class="Z"></path><path d="M276 422c2 0 2 0 3 1h1l-2 1c0 1 1 1 1 2h-2v-2h-1v-2z" class="J"></path><path d="M256 518h1v6c1 0 2 1 4 1h2-2v1l2 2h0-3v1c0 1 0 0 1 1 0 1 1 2 1 3s-1 2-1 2v3 4c1 0 2 1 3 1 0 1 0 1 1 1v1c-1 1-2 3-3 4h2c0 1 1 2 1 2l-1 2v3c1 1 1 2 1 3h-2c-1 0-2 1-2 1h-6-8v-2c0-1 1-2 2-4-1-1-1-1-1-3l1-3c-1-6-2-11-1-17 0 2-1 6 0 8h1l1-1c2-2 2-6 3-9l3-9v-1-1z" class="l"></path><path d="M258 530l1-1c2 2 0 4 2 6v3 4c0 1 0 1-1 2v-3-2-3c0-2 0-4-1-6h-1z" class="o"></path><path d="M261 542c1 0 2 1 3 1-1 1-3 3-3 5v3 7l1 1-1 1h-6l4-1h1c0-3 1-7 0-9v-3c0-1 1-2 1-2l-1-1c1-1 1-1 1-2z" class="Z"></path><path d="M264 543c0 1 0 1 1 1v1c-1 1-2 3-3 4h2c0 1 1 2 1 2l-1 2v3c1 1 1 2 1 3h-2c-1 0-2 1-2 1l1-1-1-1v-7-3c0-2 2-4 3-5z" class="S"></path><path d="M262 554v-1c0-1-1-2 0-4h2c0 1 1 2 1 2l-1 2c0-1-1-1-2-1v2z" class="R"></path><path d="M262 554v-2c1 0 2 0 2 1v3c1 1 1 2 1 3h-2 0c0-2 0-4-1-5z" class="m"></path><path d="M258 530h1c1 2 1 4 1 6-1 3-2 7-1 10-1 0-1 0-2-1 0-1-1 0-2 0v-2l3-13z" class="j"></path><path d="M255 545c1 0 2-1 2 0 1 1 1 1 2 1 1 2 0 5 0 7v6h0l-4 1h-8v-2c0-1 1-2 2-4l6-9z" class="s"></path><path d="M256 518h1v6c1 0 2 1 4 1h2-2v1l2 2h0-3v1c0 1 0 0 1 1 0 1 1 2 1 3s-1 2-1 2c-2-2 0-4-2-6l-1 1-3 13v2l-6 9c-1-1-1-1-1-3l1-3c-1-6-2-11-1-17 0 2-1 6 0 8h1l1-1c2-2 2-6 3-9l3-9v-1-1z" class="f"></path><path d="M249 548h1c2-3 3-7 4-10h0v3 1l1 1v2l-6 9c-1-1-1-1-1-3l1-3z" class="d"></path><path d="M256 518h1v6c1 0 2 1 4 1h2-2v1l2 2h0-3v1c0 1 0 0 1 1 0 1 1 2 1 3s-1 2-1 2c-2-2 0-4-2-6l-1 1-3 13-1-1v-1-3h0c1-4 2-7 2-10v-9-1z" class="H"></path><path d="M261 525h2-2v1l2 2h0-3-1v-2-1h2z" class="Q"></path><path d="M289 407h2c2 0 4 0 6 1 2 0 4 1 6 2l2 1 6 3 1 1h0 1c1 2 1 5 1 6v1 2h1 0-4l-6 1h-3v3 2h-1l-1 1h-2l-5-1h0 1c-1-1-1-2-2-3h0 0l-1-1h-1-2l1-2c1-1 1-2 1-3h2l-1-1h-2v-6-1h-3c0 2 0 5-1 6h0c-1 1-2 1-3 0v-6-3h-1-2l-1-1v-1c3 0 7-1 11-1z" class="B"></path><path d="M293 420c1 0 3 0 4 1v1h-2c0 1-1 1-1 2l-2-2h0l1-1v-1z" class="F"></path><path d="M292 412h2v1-1h1c0 1 1 3 1 4s0 1 1 2h0-1c-1-1-1-1-2-3 0-1 0-2-1-2l-1-1h0z" class="E"></path><path d="M295 422c0 1 1 1 2 1-1 0 0 0-1-1h1 0c1 1 1 1 1 2h0v1l-1 1h-1-1l-1-1h0-4v-2l1 1c1-1 1-1 1-2l2 2c0-1 1-1 1-2zm10 1h-3c0-2-2-5-1-7 1 0 2 2 2 3 1 0 2 1 2 1v3z" class="b"></path><path d="M291 420h0c1-1 1-1 0-1l1-1h1v2h0v1l-1 1h0c0 1 0 1-1 2l-1-1v2h4 0l1 1h1 1 2v1 1h0l2 2-1 1h-2l-5-1h0 1c-1-1-1-2-2-3h0 0l-1-1h-1-2l1-2c1-1 1-2 1-3h2l-1-1z" class="W"></path><path d="M292 427c2 0 4 1 6 1v2h3l-1 1h-2l-5-1h0 1c-1-1-1-2-2-3h0z" class="c"></path><path d="M311 414l1 1h0 1c1 2 1 5 1 6v1 2h1 0-4l-6 1h-3v3 2h-1l-2-2h0v-1-1-1c1-2 4-1 6-2h1-1v-3-3l2-2s1 0 1-1c1 1 2 1 3 0z" class="q"></path><path d="M305 420v-3l2-2c-1 2 0 6-1 8h-1v-3zm7-5h1c1 2 1 5 1 6v1 2h1 0-4c2-2 0-6 1-9z" class="D"></path><defs><linearGradient id="AF" x1="296.256" y1="407.542" x2="292.928" y2="416.85" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#343333"></stop></linearGradient></defs><path fill="url(#AF)" d="M289 407h2c2 0 4 0 6 1 2 0 4 1 6 2l2 1 6 3c-1 1-2 1-3 0h0-1 0c-4 0-5-2-8-4-2-1-2-1-3-1-1 1-1 1-2 3v1-1h-2l-2 1c-1-1-1-1-3-1h0c1 1 1 1 2 1h0-3c0 2 0 5-1 6h0c-1 1-2 1-3 0v-6-3h-1-2l-1-1v-1c3 0 7-1 11-1z"></path><path d="M283 415c1-1 1-1 2-1v1 4h-2c1-2 1-3 0-4z" class="B"></path><path d="M297 528h2c0 1 0 1 1 1h1v4c0 1 0 1-1 1l3 1c-2 2-2 1-3 1 0 0-1 1-1 2-2 0-3 1-4 3l-1 1h0l-1 1c2 1 5 1 8 1v1c-3 0-7-1-9 0-1 1-1 1 0 2 0 1 0 1-1 2v1h0c0 1-1 2-1 3 1 1 1-1 2 1 0 1 0 1-1 2v6l1-1h0c0-1-1-2 0-3s1-2 1-4v-1c1 0 1-1 1-1v-1-1-1l1 2c-1 3-2 5-1 8h-1-1c0 1 0 1 1 1v2h-1v2l-1 1c-1-3-1-7 0-10h-3c0-1 0-1-1-1h-2c-1 0-3 0-4-1l-14 1v-4c-1 1-2 0-2 1 0 0-1-1-1-2h-2c1-1 2-3 3-4v-1c-1 0-1 0-1-1-1 0-2-1-3-1v-4-3s1-1 1-2-1-2-1-3c-1-1-1 0-1-1l7 1h1 6 2 4l1-2h6c1 1 2 1 3 0h3 1 3z" class="q"></path><path d="M278 537v-3l1-1c1 1 1 3 1 4v1l-1-1-1 1v-1z" class="m"></path><path d="M295 536h0c0-1 0-2 1-3h1c1-1 1-1 2-1l1 1h1c0 1 0 1-1 1l3 1c-2 2-2 1-3 1 0 0-1 1-1 2-2 0-3 1-4 3v-5z" class="D"></path><path d="M295 536h0c0-1 0-2 1-3h1c1-1 1-1 2-1l1 1h1c0 1 0 1-1 1h-1 0c0 1-1 3-2 3 0-1 0-1-1-2-1 0-1 1-1 1z" class="Y"></path><path d="M297 528h2c0 1 0 1 1 1h-3c-2 1-5 1-7 1l-1 1-9-1 1-2h6c1 1 2 1 3 0h3 1 3z" class="W"></path><path d="M291 550l-3 3c0-4 2-9 2-13 0-2 0-5 1-6h1 1v8h1 0l-1 1c2 1 5 1 8 1v1c-3 0-7-1-9 0-1 1-1 1 0 2 0 1 0 1-1 2v1z" class="C"></path><path d="M282 535v-1h1c1 1 1 2 1 3v12 4h1v-5c0-4 0-11 1-14h1v12c0 2 0 5-1 7 1 1 5-1 5 1v1h-3c0-1 0-1-1-1h-2c-1 0-3 0-4-1 0-1 0-1-1-2h1v-4-1-1c0-3 1-7 1-10z" class="D"></path><path d="M282 535v5c1 3 0 6 1 9v1h-1l-1 1v-4-1-1c0-3 1-7 1-10z" class="U"></path><path d="M260 529l7 1h1 6c0 1 0 1-1 2l2 2h1 1c0 1 0 3 1 3h0v1l1-1 1 1v4 9c1 1 1 1 1 2l-14 1v-4c-1 1-2 0-2 1 0 0-1-1-1-2h-2c1-1 2-3 3-4v-1c-1 0-1 0-1-1-1 0-2-1-3-1v-4-3s1-1 1-2-1-2-1-3c-1-1-1 0-1-1z" class="o"></path><path d="M271 537h0l1 11c0 2 1 3 0 4l-1 1v-6c-1-2-1-5-2-7l1-1 1-2z" class="G"></path><path d="M267 537c1 3 1 6 2 9 0 2 1 3 0 5h0c-1 0-1-1-2-2v1h0c-1 1-2 0-2 1 0 0-1-1-1-2h-2c1-1 2-3 3-4h2 0c0-2-1-4-1-6 1-1 1-1 1-2z" class="b"></path><path d="M260 529l7 1h-3c0 1 0 1 1 1h0l1 2h0v1l1 3c0 1 0 1-1 2 0 2 1 4 1 6h0-2v-1c-1 0-1 0-1-1-1 0-2-1-3-1v-4-3s1-1 1-2-1-2-1-3c-1-1-1 0-1-1z" class="E"></path><path d="M261 538l1 1v2 1l2-1h1c0 1 1 2 1 2l-1 1c-1 0-1 0-1-1-1 0-2-1-3-1v-4z" class="G"></path><path d="M260 529l7 1h-3c0 1 0 1 1 1h0l1 2h0v1l-1-1h-1l-1 2v3s0 1-1 1h0l-1-1v-3s1-1 1-2-1-2-1-3c-1-1-1 0-1-1z" class="B"></path><defs><linearGradient id="AG" x1="273.646" y1="531.326" x2="267.21" y2="547.004" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#454446"></stop></linearGradient></defs><path fill="url(#AG)" d="M267 530h1 6c0 1 0 1-1 2l2 2h1 1c0 1 0 3 1 3h0v1l1-1 1 1v4c-1 1-1 3-1 4 0 2 0 4-1 6-1 0-1 1-2 1h0-2v-3c-1-5-1-10-4-15 0 1 0 1 1 2l-1 2-1 1-2-6-1-1h0l-1-2h0c-1 0-1 0-1-1h3z"></path><path d="M268 530h6c0 1 0 1-1 2l2 2-1 1c0-1-1-1-1-2s-1-2-1-2c-1-1-2-1-3-1h-1z" class="E"></path><path d="M267 534c1-1 1 0 2-1l-1-1h1c0 1 1 2 1 3h0c0 1 0 1 1 2l-1 2-1 1-2-6zm8 0h1 1c0 1 0 3 1 3h0v1l1-1 1 1v4c-1 1-1 3-1 4 0 2 0 4-1 6-1 0-1 1-2 1h0-2v-3s1 1 2 0c0-1-1-2 0-3v-1c0-3-1-7-1-10l-1-1 1-1z" class="X"></path><path d="M278 538l1-1 1 1v4c-1 1-1 3-1 4h0l-1-8z" class="R"></path><path d="M275 536h1c1 3 2 13 2 16-1 0-1 1-2 1h0-2v-3s1 1 2 0c0-1-1-2 0-3v-1c0-3-1-7-1-10z" class="K"></path><path d="M276 546v7h0-2v-3s1 1 2 0c0-1-1-2 0-3v-1z" class="m"></path><path d="M261 97l35 1h0 0-11c0 1 1 1 1 1l-1 1c2 0 4 0 6 1 2 0 4-1 6 0h4 5l-1 2c-1 1 0 1-1 2v3 1c1 1 1 1 2 1h0v1c1 0 1 1 2 1 2 1 2 1 4 1l-2 1h-1c-1-1-2 0-3-1h0v2 1h0l-1 1v3h1-1c-2 2 1 4-4 4h-2v-1c0-1 1-2 1-3s0-1 1-1c0-2-1-3-2-4-1-4-5-7-8-9l-3-2h1c-3-2-7-3-11-4h-7c-8 2-14 6-19 13-5 8-4 18-3 26-2-3-4-5-6-8l-4-5-3-3-4-3c0-1 0-2 1-3h0c0-1 0-1 1-1 1-1 1-2 2-3-1-1-2-1-2-1l-3-1h6 1l-2-2c1-1 1-2 2-4 0-1 1-2 1-2l1-2c1-1 3-2 5-2 0 0 2-1 3-1s2 0 3 1h2l1 1 1-1h-1l-1-1h0v-1h8z" class="j"></path><path d="M245 104c1-1 1-1 2-1h1c1 1 1 1 1 2-1 0-1 1-2 1s-1-1-1-1l-1-1h0z" class="h"></path><path d="M239 125l2 1h0c1 1 2 1 2 3v2l-4-5v-1z" class="T"></path><path d="M239 125v-2c0-3 0-5 2-7h1c0 1-1 1-1 2-1 2 0 5 0 8h0l-2-1z" class="Q"></path><path d="M239 103l1-2c1-1 3-2 5-2l-3 2c-2 1-3 4-3 5v3 2h0-1l-2-2c1-1 1-2 2-4 0-1 1-2 1-2z" class="V"></path><path d="M231 111h6c0 1 1 2 2 2-2 1-3 2-5 4h0c0 1-1 1-1 2 2 1 3 2 3 4h0l-4-3c0-1 0-2 1-3h0c0-1 0-1 1-1 1-1 1-2 2-3-1-1-2-1-2-1l-3-1z" class="h"></path><path d="M303 108c-2-1-3-1-4-2s-2-1-3-2h1c1 0 1 0 2 1 1 0 2 0 2 1 1 1 2 2 3 2h0v1c1 1 1 1 2 1h0v1c1 0 1 1 2 1 2 1 2 1 4 1l-2 1h-1c-1-1-2 0-3-1h0l-2 1v-1-2l-1-1v-1-1z" class="V"></path><path d="M291 101c2 0 4-1 6 0h4 5l-1 2c-1 1 0 1-1 2h-1 0v-1h0-1c-1-1-3-1-4-1s-2 1-3 0c-1 0-3-1-4-2z" class="T"></path><path d="M245 104l-1 1c0 1 0 1 1 2l2 1h0c-2 0-3 0-4-1v-3c1-1 2-2 4-2s4 0 6 2v2c1 1-1 3-2 4-2 2-5 2-7 3h-5c-1 0-2-1-2-2h1 1 0v-2l1 1c2 1 4 2 5 1 3 0 5-1 6-2 0-2-1-3-2-4 0-1 0-1-1-2h-1c-1 0-1 0-2 1z" class="e"></path><path d="M238 111h1 0l2 1c1 1 2 1 3 1h-5c-1 0-2-1-2-2h1z" class="Q"></path><path d="M289 104c3 1 6 3 8 5 1 0 2 1 3 2h0 1v-1l1-1 1-1v1 1l1 1v2 1l2-1v2 1h0l-1 1v3h1-1c-2 2 1 4-4 4h-2v-1c0-1 1-2 1-3s0-1 1-1c0-2-1-3-2-4-1-4-5-7-8-9l-3-2h1z" class="K"></path><path d="M305 117v3h-4l1-2c1 0 2-1 3-1z" class="i"></path><path d="M303 108v1 1c-1 2-1 3-2 4h-1c0-2-2-4-3-5 1 0 2 1 3 2h0 1v-1l1-1 1-1z" class="h"></path><path d="M306 113v2 1h0l-1 1c-1 0-2 1-3 1h0c0-1 0-3 1-4h1l2-1z" class="d"></path><path d="M201 97h46 10 4-8v1h0l1 1h1l-1 1-1-1h-2c-1-1-2-1-3-1s-3 1-3 1c-2 0-4 1-5 2l-1 2s-1 1-1 2c-1 2-1 3-2 4l2 2h-1-6l3 1s1 0 2 1c-1 1-1 2-2 3-1 0-1 0-1 1h0c-1 1-1 2-1 3-4-3-8-5-13-7-14-5-28-5-43-5-1-1-2-2-2-4l-2-3v-2h0l-1-2h30z" class="j"></path><path d="M172 99c1 0 1 0 2 1 2 0 3-1 4 1v2h-1c-1-1-3-2-4-2h-1v-2z" class="Q"></path><path d="M201 97h46 10 4-8v1h0l1 1h1l-1 1-1-1h-2c-1-1-2-1-3-1s-3 1-3 1c-2 0-4 1-5 2l-1 2c-1 0-2 1-3 2h-1-1l1-1h1s1-2 2-3c0 0 0-1 1-1h0v-1c-1-1-4 0-6 0v-1c2 0 5 0 6 1h1l1-1c-2-1-6 0-8 0-1 0-2-1-3-1h-19c-3 0-7 1-10 0z" class="h"></path><path d="M231 111c-5 0-10-4-15-6h0l-4-1h-1c-1 0-3-1-4-1h-1c-1 0-2 0-3-1h-1c-3 0-7 1-9-1 1-1 4 0 6 0v-1c1 0 2 1 2 1h1c1 0 1 1 2 1 3 0 8 0 11 2h0c1 0 3 1 4 2l6 2c3 2 8 2 11 1h0l2 2h-1-6z" class="e"></path><path d="M274 361c1 2 4 4 5 6 1 1 2 2 4 2l4 2c0-2-1-5 0-6v-1l1-1h0v1h1l1 1h-1c0 1 0 1 1 2h1l-1 1h1c2 0 3 0 4-1 1 1 1 2 3 2v1 1c-1-1-2-1-3-1 0 1 1 2 1 2v1l-2 1h0l-6 4c1 1 0 1 1 1h-1c-2 1-5-1-7 0h0v2h1v2l1 1-1 3 1 2h-1v2c0 1 0 1 1 2h1v1 2 1 1l2 1v1c1 0 1 1 1 1l1 1v1 1h3v1h-3v1h1 0v1c-4 0-8 1-11 1v1h-1v6h-5l-3-1c-2 0-6-3-7-6-3-7-3-16 0-23l-1-1-1 3c-2 2-1 4-2 6v-6-9-5l2-2c0-2 1-2 3-4 2 0 4 0 6 1v1h1c1 0 2 2 3 3 0 0 0 1 1 1h0v-1-1c1 1 0 1 1 3-1 1-2 1-3 2h-1 1 1 1 4c1-1 1-3 1-4h1v-2c-1 0-1-1-2-1-1-2-2-2-3-4l-2-3 1-1z" class="h"></path><path d="M266 375v-1c0-1-1-1-1-1-1 0-1 1-2 1v1l-1-1c1-1 2-1 3-2 1 0 2 0 3 2v2h0l1 1-2 2-1-1 1-2v-1h-1z" class="Q"></path><path d="M266 404c1 0 1 0 2 1h0c1 1 1 2 3 2 0 1 1 2 2 2l1 1-1 1c-3-2-5-3-7-7z" class="k"></path><path d="M266 375h1v1l-1 2 1 1c-2 2-4 4-5 6l-1-1h0c0-4 3-6 5-9z" class="L"></path><path d="M272 383c-2 0-4 2-6 3v1-1c1-2 2-3 4-4 3-2 5-2 8-1l4 2 1 1-1 3v-2c-1-1-2 0-3-1h1v-1c-3-1-5-1-8 0z" class="D"></path><path d="M272 383c3-1 5-1 8 0v1h-1c1 1 2 0 3 1v2l1 2h-1v2c-2-1-3-2-5-3h-2c0-1 1-1 2-1s1 0 2-1v-1l-1 1-1-1v-2h-5z" class="O"></path><path d="M275 388h2-2c1 1 0 1 1 0-1 2-1 2-3 3s-3 2-5 5v1c0 2 0 5 1 6l2 4c-2 0-2-1-3-2h0c-1-1-1-1-2-1h0c-1-3-1-7 0-10 1-1 1-2 1-2 2-3 5-4 8-4z" class="l"></path><path d="M275 388h2-2c1 1 0 1 1 0-1 2-1 2-3 3s-3 2-5 5v-1l2-5c-1 2-2 3-3 4h-1c1-1 1-2 1-2 2-3 5-4 8-4z" class="g"></path><path d="M274 361c1 2 4 4 5 6v1c2 2 4 2 6 3-1 2-2 5-3 6h-3l2 2v2h1v2l-4-2 1-1c-1-1-1-1-2-1l-1-1h-1c-3-1-4 0-7 1 1-1 2-2 3-2s2 0 3-1h4c1-1 1-3 1-4h1v-2c-1 0-1-1-2-1-1-2-2-2-3-4l-2-3 1-1z" class="Q"></path><path d="M288 363h0v1h1l1 1h-1c0 1 0 1 1 2h1l-1 1h1c2 0 3 0 4-1 1 1 1 2 3 2v1 1c-1-1-2-1-3-1 0 1 1 2 1 2v1l-2 1h0l-6 4c1 1 0 1 1 1h-1c-2 1-5-1-7 0h0l-2-2h3c1-1 2-4 3-6-2-1-4-1-6-3v-1c1 1 2 2 4 2l4 2c0-2-1-5 0-6v-1l1-1z" class="f"></path><path d="M291 368c2 0 3 0 4-1 1 1 1 2 3 2v1 1c-1-1-2-1-3-1 0 1 1 2 1 2v1l-2 1c0-1-1-2-1-3h1l-1-1c-1 2-1 2-1 4v1h-2c0-1-1-2-1-3h0c0-1 1-2 1-3 0 0 1 0 1-1h0z" class="Y"></path><path d="M277 388c2 1 3 2 5 3 0 1 0 1 1 2h1v1 2 1 1l2 1v1c1 0 1 1 1 1l1 1v1 1h3v1h-3v1h1 0v1c-4 0-8 1-11 1v1h-1v6h-5c1 0 2 0 3-1v-1c0-1-1-1-2-2l1-1-1-1c-1 0-2-1-2-2l-2-4c-1-1-1-4-1-6v-1c2-3 3-4 5-5s2-1 3-3c-1 1 0 1-1 0h2z" class="P"></path><path d="M272 405l1-2v1h0l2 2c0 1 0 1-1 1v2 1l-1-1c0-1 0-3-1-4z" class="T"></path><path d="M278 397h0l1 1v2c0 1-1 2-2 3 0-3 0-4 1-6zm-6 8c0-1-1-4-2-5h0v-2-2h2v1c0 1 1 2 2 2 0 1 1 1 2 1h0v1h-2 0l-1 3v-1l-1 2z" class="L"></path><path d="M282 399c0-1 1-1 1-2h1v1l2 1v1c1 0 1 1 1 1l1 1v1 1h3v1h-3v1h1 0v1c-4 0-8 1-11 1v1h-1v-1l2-1c1-1 2 0 3-1-1-1-6-1-8-1l3-2c1-1 2-2 2-3h1v1l2-2z" class="D"></path><path d="M282 399h2c1 1 0 1 0 2l-1 1v1 1h-4c0-2 1-2 1-3l2-2z" class="o"></path><path d="M282 399c0-1 1-1 1-2h1v1l2 1v1c1 0 1 1 1 1l1 1h-1l-1 1h-1-2v-1l1-1c0-1 1-1 0-2h-2z" class="f"></path><path d="M277 388c2 1 3 2 5 3 0 1 0 1 1 2h1v1 2 1h-1c0 1-1 1-1 2l-2 2v-1h-1v-2l-1-1h0-3c-1-1 0-3-1-3-1 1-2 1-2 3v-1h-2v2 2h0c1 1 2 4 2 5 1 1 1 3 1 4-1 0-2-1-2-2l-2-4c-1-1-1-4-1-6v-1c2-3 3-4 5-5s2-1 3-3c-1 1 0 1-1 0h2z" class="V"></path><path d="M277 388c2 1 3 2 5 3 0 1 0 1 1 2h1v1 2 1h-1c0 1-1 1-1 2l-2 2v-1h-1v-2l-1-1c1-1 1-1 1-3l-3-3h-3c2-1 2-1 3-3-1 1 0 1-1 0h2z" class="m"></path><path d="M282 394v1h0c0 1 0 2-1 3s-1 1-1 2h-1v-2-1c0-1 1-1 1-1l2-2z" class="c"></path><path d="M276 388c3 2 4 2 6 6l-2 2s-1 0-1 1v1l-1-1c1-1 1-1 1-3l-3-3h-3c2-1 2-1 3-3z" class="k"></path><defs><linearGradient id="AH" x1="271.89" y1="476.51" x2="250.633" y2="470.337" xlink:href="#B"><stop offset="0" stop-color="#acabac"></stop><stop offset="1" stop-color="#e3e2e3"></stop></linearGradient></defs><path fill="url(#AH)" d="M272 431v-1h3v1s1 0 1 1l1 1h1s0 1 1 1h-1c-1 1-1 5-1 7v2 2 7c0 2-1 5-1 7s-1 4-1 6c-1 3 0 4-1 7h0c-1 0-1 0-2-1v-3h-1v-5c-1 2-1 5-1 8s-1 6-2 9c1 2 1 4 3 6h0v1h0 3l-1 1v1h1c0 1-1 2-1 4h1l-1 1v1h1v2h0l1-1 1 1v7c2 1 3 1 3 2v1h-1 0 0 0-4 0l-1 1v3l1 1h0-2-7c-1 0-3 0-4 2h0-1v-1c0-1 1-1 2-2h0v-4s1 0 1-1l-1-2c-1-1 0-3-1-4-1 1-1 3-1 4h-2v-4c-1-3 0-8 0-11v-28c0-5-1-11 0-17v-6c2 2 3 3 6 5 2 0 2-1 4-2 2-2 3-3 4-6-1-1-1-2-2-3h1l1-1z"></path><path d="M263 471h1 0c1 1 2 1 4 1h1c0 2-1 5-2 5-1 1-2 1-3 1s-1-1-1-2c1 1 1 1 2 0s-1-4-2-5z" class="f"></path><path d="M269 463h0c1 3 0 6 0 9h-1c-2 0-3 0-4-1h0-1 0c0-1-1-3-1-4 2 1 5 0 7 0v-4z" class="M"></path><path d="M258 461c0-5-1-11 0-17v10l1 48h1s-1-2 0-2l1-2h0c1 2 1 4 1 6h0c-1-1 0-3-1-4-1 1-1 3-1 4h-2v-4c-1-3 0-8 0-11v-28z" class="L"></path><path d="M258 444v-6c2 2 3 3 6 5 2 0 2-1 4-2h1c0 1-1 2-1 3l-1 1c-1 0-1-1-2-1l-1 1h1c-1 1-2 1-2 2h0c-1 1-1 3-1 4h0v-3l-1-1c-1 1-1 2-1 2h-1 0c1-1 1-2 0-3h0c1-1 1-1 0-2h1v-1l-1-1v4c-1 1-1 3 0 5 0 1 0 2-1 3v-10z" class="i"></path><path d="M270 458h1c0 1-1 3 0 5-1 2-1 5-1 8s-1 6-2 9c1 2 1 4 3 6h0v1h0 3l-1 1v1h1c0 1-1 2-1 4h1l-1 1v1h1v2h0l1-1 1 1v7c2 1 3 1 3 2v1h-1 0 0 0-4 0l-1 1v3l1 1h0-2-7c-1 0-3 0-4 2h0-1v-1c0-1 1-1 2-2h0v-4s1 0 1-1c1 0 1-1 2-2v-1c-1-2-2-3-2-6v-1h1 1v-2l-1-2c0-1-1-2-2-4l1-1h2v-3h-1l1-1h0 1c0-1 0-2-1-3l-1-2c1 0 2 0 3-1 1 0 2-3 2-5 0-3 1-6 0-9h0l-1 1c-1 0-1 0-1-1-2-1-3-3-4-5h1c1 2 1 4 3 4h1 1c1-1 1-3 1-4z" class="S"></path><path d="M268 480c1 2 1 4 3 6h0v1h0v1 1c-1 1-1 2 0 2l-1 1h0-2c0-4-1-8 0-12z" class="O"></path><path d="M267 491c1 4-1 8 0 12-1 0-1-1-1-1-1-1-2-2-2-3s0-1-1-2v-1h1 1v-2l-1-2h3v-1z" class="g"></path><path d="M265 494c1 2 2 6 1 8-1-1-2-2-2-3s0-1-1-2v-1h1 1v-2zm6-7h3l-1 1v1h1c0 1-1 2-1 4h-2v2l-1 1h-2v-4h2 0l1-1c-1 0-1-1 0-2v-1-1z" class="p"></path><path d="M271 488l1 3-2 2s0 2 1 2l-1 1h-2v-4h2 0l1-1c-1 0-1-1 0-2v-1z" class="M"></path><path d="M265 480l1-1 1 1-1 1c0 3 1 6 1 10v1h-3c0-1-1-2-2-4l1-1h2v-3h-1l1-1h0 1c0-1 0-2-1-3z" class="p"></path><path d="M271 495v-2h2 1l-1 1c-2 4-2 9-2 13 0 2 0 3-1 4h-1v-5-5-2c-1-1-1-2-1-3h2l1-1z" class="g"></path><path d="M269 499l1-1c1 2 1 1 1 3h-2 0v-2z" class="p"></path><path d="M265 503v1c1 0 1 1 2 1 0 2-1 4 0 5v-4l1-1 1 1v5h1c1 1 1 1 2 1h-7c-1 0-3 0-4 2h0-1v-1c0-1 1-1 2-2h0v-4s1 0 1-1c1 0 1-1 2-2v-1z" class="D"></path><path d="M265 503v1c1 0 1 1 2 1 0 2-1 4 0 5v1h-2-1v-2l-2-2s1 0 1-1c1 0 1-1 2-2v-1z" class="c"></path><path d="M265 504c1 2 0 5 0 7h-1v-2l-2-2s1 0 1-1c1 0 1-1 2-2z" class="k"></path><path d="M273 494v1h1v2h0l1-1 1 1v7c2 1 3 1 3 2v1h-1 0 0 0-4 0l-1 1v3l1 1h0-2c-1 0-1 0-2-1 1-1 1-2 1-4 0-4 0-9 2-13z" class="D"></path><path d="M271 507h0v-2h1v2c1-1 1-2 1-2 0-1 1-1 1-1v3h0l-1 1v3l1 1h0-2c-1 0-1 0-2-1 1-1 1-2 1-4z" class="W"></path><path d="M274 497l1-1 1 1v7c2 1 3 1 3 2v1h-1 0 0 0-4v-3c1-1 0-6 0-7z" class="O"></path><path d="M278 507h-3v-1l1-1h2v2h0z" class="P"></path><path d="M272 431v-1h3v1s1 0 1 1l1 1h1s0 1 1 1h-1c-1 1-1 5-1 7v2 2 7c0 2-1 5-1 7s-1 4-1 6c-1 3 0 4-1 7h0c-1 0-1 0-2-1v-3h-1v-5c-1-2 0-4 0-5h-1c0 1 0 3-1 4h-1-1c-2 0-2-2-3-4h-1v-1c0-1-1-1-1-1 0-1 0-1 1-2 0 0 0 1 1 1v-2c-1 0-1-1-1-1l-1-1c0-1 0-3 1-4h0c0-1 1-1 2-2h-1l1-1c1 0 1 1 2 1l1-1c0-1 1-2 1-3h-1c2-2 3-3 4-6-1-1-1-2-2-3h1l1-1z" class="J"></path><path d="M272 468c0-2 1-4 1-6 1-2 1-4 2-5 0 2-1 4-1 6s-1 4 0 6l-2 2v-3z" class="V"></path><path d="M271 458c0-1 0-2 1-3 0-3 0-7 2-10h1v4 8c-1 1-1 3-2 5 0 2-1 4-1 6h-1v-5c-1-2 0-4 0-5z" class="T"></path><path d="M272 431v-1h3v1s1 0 1 1l1 1h1s0 1 1 1h-1c-1 1-1 5-1 7v2l-3-2h0v1h1c1 2 1 4 0 6v1-4h-1c-2 3-2 7-2 10-1 1-1 2-1 3h-1l1-10 1-3c1-1 1-1 1-2-1-1-1-2 0-3v-1c1-1 1-3 1-4s-2-3-2-4z" class="N"></path><path d="M272 431c0 1 2 3 2 4s0 3-1 4v1c-1 1-1 2 0 3 0 1 0 1-1 2l-1 3-1 10c0 1 0 3-1 4h-1-1c-2 0-2-2-3-4h-1v-1c0-1-1-1-1-1 0-1 0-1 1-2 0 0 0 1 1 1v-2c-1 0-1-1-1-1l-1-1c0-1 0-3 1-4h0c0-1 1-1 2-2h-1l1-1c1 0 1 1 2 1l1-1c0-1 1-2 1-3h-1c2-2 3-3 4-6-1-1-1-2-2-3h1l1-1z" class="P"></path><path d="M269 441h1c0 1 0 1-1 2 0 1 0 1-1 1v1c0 2-1 4-1 6h-1v-2c0-1 0-2 1-4l1-1c0-1 1-2 1-3z" class="O"></path><path d="M265 445h-1l1-1c1 0 1 1 2 1-1 2-1 3-1 4v1h-1l-2-1c0-1 0-1 1-2 0-1 0-1 1-2z" class="Q"></path><path d="M268 462v-1h-1c0-3 2-2 3-4 0-1-1-1-1-1v-1-3l1-1-1-2h0l2-1-1 10c0 1 0 3-1 4h-1z" class="O"></path><path d="M279 258c1 1 1 7 1 9 0 1-1 4-2 5l-1 1h0 1 3c0 1-1 4-1 5l1 1c-1 0-2 0-3 1v1l2-1v1 1 1 1c1 0 2 0 3-1 2 0 3 0 4-1v1l4 1h0-6c-2 1-3 2-5 1l-1 1 1 1-2 1 1 2v1l-1 1v2h1v2c-1 1-2 1-2 3h-2l1 2h-1 3v1l-1 1h0l-1-1h-1c-5 3-9 6-11 12l-1 1h0v2 1h-1l-1-1c-3 13-1 27 6 37l2 4c-1 1-1 1-2 1-1-1-1-1-1-2l-1 1h-1c-2 0-3 1-4 2v3h0c1-1 1-2 2-3h1c1 1 1 1 2 1 1 1 2 1 3 2l1 1c1 0 1 0 2 1v-2h0c-1-1-1-1-1-2v-1c1 1 2 1 2 2 1 1 2 2 2 4 1 1 2 2 2 4h-1-1s0-1-1-1c0-2 1-2 0-3l-2 1h-1-1c-2-1-3-1-4-1h-2c-2 1-4 4-5 5v-7-8c-1-6-1-12 0-18v-18-4c2-3 2-6 2-9h-1 0l-1-1v-3-26-5c0-2 1-3 1-4h0l1-1h-1c1-2 1-4 3-5 0-1 1-1 1-2v-1h1c0 2 1 2 2 3h1 0 1c1 2 2 2 3 3v-1c1 1 1 1 1 2l-1 2c1-1 2-3 2-4l2-2 4-4z" class="j"></path><path d="M258 338h0c0 1 0 1 1 2 0 1 0 3 1 4 0 2 1 5 2 8h0c0 1 0 2 1 2l-1 1c-1-2-1-4-2-6v-2c-1-1-1-2-2-3h0v12c-1-6-1-12 0-18zm15-44v2h1v-2h1v2c1 1 1 2 0 3l1 2h-1-1v-3l-1-1v1h-2v1l1 1c0 1-1 2-2 2h0c-1 0-1 0-2-1 0 1 0 1-1 2h0c-1-1-1-1 0-2 1 0 2-1 3-2 0-2-1-2-2-3h3l1-2h1z" class="L"></path><path d="M273 284c1 1 1 2 2 2-1 2-3 3-4 4 1 2 3 1 4 2-1 1-2 1-2 2h-1l-1 2h-3l-2-1 1-1c1-1 3-3 3-4l1-1h0c1 0 2-1 2-2h-1-1c-1 0-1 1-2 1h-1c2-2 4-3 5-4z" class="V"></path><path d="M267 294h0 0l3-2 2 2-1 2h-3l-2-1 1-1z" class="T"></path><path d="M270 302h0c1 0 2-1 2-2l-1-1v-1h2v-1l1 1v3h1 3v1l-1 1h0l-1-1h-1c-5 3-9 6-11 12l-1 1h0v2 1h-1l-1-1c1-4 3-7 5-10 1-1 1-2 1-2 0-1 2-2 3-3z" class="M"></path><path d="M278 273h3c0 1-1 4-1 5l1 1c-1 0-2 0-3 1v1h0c-1 1-4 3-5 3-1 1-3 2-5 4-4 4-6 8-7 14-1 1-1 3-1 4v1h-1 0c1-2 1-5 1-7 1-2 1-4 2-6 2-2 3-5 6-7 1-1 3-2 4-3l-1-2h1c1 0 2-1 2-1 0-1-1-2-1-3s2-3 4-4h-1l1-1h1z" class="n"></path><path d="M277 273h1 1c0 1 0 1-1 2-1 2-1 4-1 5s0 1-1 1-1 0-2-1v1c0-1-1-2-1-3s2-3 4-4h-1l1-1z" class="f"></path><path d="M278 281l2-1v1 1 1 1c1 0 2 0 3-1 2 0 3 0 4-1v1l4 1h0-6c-2 1-3 2-5 1l-1 1 1 1-2 1 1 2v1l-1 1v2h1v2c-1 1-2 1-2 3h-2c1-1 1-2 0-3v-2h-1v2h-1v-2c0-1 1-1 2-2-1-1-3 0-4-2 1-1 3-2 4-4-1 0-1-1-2-2 1 0 4-2 5-3h0z" class="N"></path><path d="M278 281v2c-1 2-1 2-1 4 0 0-1 1-1 2v2l1 1v1h-1s0-1-1-1c-1-1-3 0-4-2 1-1 3-2 4-4-1 0-1-1-2-2 1 0 4-2 5-3z" class="P"></path><path d="M279 258c1 1 1 7 1 9 0 1-1 4-2 5l-1 1h0l-1 1h1c-2 1-4 3-4 4s1 2 1 3c0 0-1 1-2 1h-1l1 2c-1 1-3 2-4 3-3 2-4 5-6 7-1 2-1 4-2 6 0 2 0 5-1 7l-1-1v-3-26-5c0-2 1-3 1-4h0l1-1h-1c1-2 1-4 3-5 0-1 1-1 1-2v-1h1c0 2 1 2 2 3h1 0 1c1 2 2 2 3 3v-1c1 1 1 1 1 2l-1 2c1-1 2-3 2-4l2-2 4-4z" class="K"></path><path d="M273 269l1 1v2h-2l-1-1 2-2z" class="V"></path><path d="M271 274h2c0 1-1 1-1 2l1 1-1 3v1h-1v-7z" class="i"></path><path d="M279 258c1 1 1 7 1 9 0 1-1 4-2 5l-1 1h0l-1 1-2 1v-1l2-2c0-3 1-5 0-8 0-1 0-2-1-2l4-4z" class="c"></path><path d="M262 281h1v-5h1c0-1 0-1 1-2h0c0-1 1-2 2-2h0c0 1-1 2-2 3v1h-1l1 1c0-1 0-1 1-1 0-2 2-4 3-5-1 2-1 4-2 5 0 1 0 1 1 2h-1v2l1 1h1 0c-1-2 0-3 0-5h1c0 2 0 4 1 6h0l1 2c-1 1-3 2-4 3-3 2-4 5-6 7h0-1c0-1 0-3 1-3v-2-1c1-1 1-1 1-2 1-1 2-2 2-3v-1l1-1h0v-1c-1-1 0-1 0-2v-1l-2 1v2 1h-1-1z" class="L"></path><path d="M263 260v-1h1c0 2 1 2 2 3h1 0 1c1 2 2 2 3 3l-1 1c-1 2 1 2-1 3s-4 2-5 4c-2 2-2 5-2 8h1 1v-1-2l2-1v1c0 1-1 1 0 2v1h0l-1 1v1c0 1-1 2-2 3 0 1 0 1-1 2v1 2c-1 0-1 2-1 3h1 0c-1 2-1 4-2 6 0 2 0 5-1 7l-1-1v-3-26-5c0-2 1-3 1-4h0l1-1h-1c1-2 1-4 3-5 0-1 1-1 1-2z" class="j"></path><path d="M263 260v-1h1c0 2 1 2 2 3-3 0-4 1-5 3l-1 2h-1c1-2 1-4 3-5 0-1 1-1 1-2z" class="O"></path><path d="M267 262h1c1 2 2 2 3 3l-1 1c-1 2 1 2-1 3s-4 2-5 4l-2-2c0-1 1-2 1-3v-1c0-1 1-1 2-2h1c1-1 1 0 2-1 0 0-1-1-1-2z" class="T"></path><path d="M278 301c1 0 1-1 2 0h0l1 1c0 1-1 1-2 2h2l1-1c1 1 1 1 1 3 0 1 0 2 2 2 0 1 1 1 2 1s2 0 4 1h0c2 1 3 0 5 1 1 0 2 1 3 2h0 1c1 2 1 3 2 4-1 2-1 3-1 4v3l-1 7-2 17v6l-1 1h1c1 0 1 1 2 1l3 1v3h0c-2 0-3 0-4 1h-1c-1 0-2 1-2 2l1 1-7 1-1-1h-1v-1h0l-1 1v1c-1 1 0 4 0 6l-4-2c-2 0-3-1-4-2-1-2-4-4-5-6l-1 1c-1-2-3-3-4-4l-2-4c-7-10-9-24-6-37l1 1h1v-1-2h0l1-1c2-6 6-9 11-12h1l1 1h0l1-1v-1z" class="h"></path><path d="M273 309l1 2-2 3c-1 0-1-1-1-1 0-1 2-3 2-4z" class="i"></path><path d="M278 301c1 0 1-1 2 0h0l1 1c0 1-1 1-2 2-1 0-1 0-2 1l-1 2h0l-1-1s-1 0-1 1c-2 1-4 4-5 6 0 2-1 3-2 4v-1c1-3 2-6 5-9 0 0 1-1 2-1 1-1 2-2 2-3l-1-1h1l1 1h0l1-1v-1z" class="Q"></path><path d="M276 315l1-1c1 0 3-2 4-3 1 0 1-1 2-1h2 4c0 1 0 1-1 1h-1c-2 1-4 2-5 3-2 2-4 3-5 5l-2 2c0-1 1-2 1-2 1-1 2-3 4-4 0 0 0-1 1-1l1-1v-1-1c-1 1-3 2-4 3-1 2-3 3-4 4l-3 3h0c0-2 3-4 4-5 0-1 1-1 1-1z" class="i"></path><path d="M279 304h2l1-1c1 1 1 1 1 3-1 0-2 0-2 1-2 1-4 2-5 3-3 4-5 8-7 12 0-3 1-5 3-8l2-3-1-2 3-2h0l1-2c1-1 1-1 2-1z" class="K"></path><path d="M276 307h0v2l-2 2-1-2 3-2z" class="P"></path><path d="M279 367h1c-1-3-3-6-5-9-3-5-4-11-6-17v-6h0c0-2 1-3 1-4v-1s1-1 1-2h1l-1 4-1 1v8c1 4 2 7 2 10 1 2 3 4 4 7l3 3c1-1 1-1 1-2 3 4 3 6 4 10h-1c-2 0-3-1-4-2z" class="Q"></path><path d="M264 314c-1 2 0 4-1 6v17c1 6 3 11 6 16 0 2 1 3 2 5l3 3-1 1c-1-2-3-3-4-4l-2-4c-7-10-9-24-6-37l1 1h1v-1-2h0l1-1z" class="L"></path><path d="M283 306c0 1 0 2 2 2 0 1 1 1 2 1s2 0 4 1h0c2 1 3 0 5 1 1 0 2 1 3 2v1h-1c-1 0-2 2-4 3h-5 1v-1c-1 0-2-1-2-2l-1-1c-1 0-1 0-2 1l-2 1h-1c-2 1-4 4-5 5l-1 1c-1 2-2 3-3 5l-1 2h-1l1-1c0-2 2-4 3-6l2-2c1-2 3-3 5-5 1-1 3-2 5-3h1c1 0 1 0 1-1h-4-2c-1 0-1 1-2 1-1 1-3 3-4 3l-1 1 2-3-2-2c1-1 3-2 5-3 0-1 1-1 2-1z" class="O"></path><path d="M281 307h0v2 1l-3 2-2-2c1-1 3-2 5-3z" class="T"></path><path d="M291 310c2 1 3 0 5 1 1 0 2 1 3 2v1h-1c-1 0-2 2-4 3h-5 1 1c1-1 1-3 2-4-1 0-2 0-3 1h-2v-1-1h1 1c0-1 1-2 1-2z" class="Z"></path><path d="M290 312c1-1 3-1 5-1h0c0 1 0 1-1 2h-1c-1 0-2 0-3 1h-2v-1-1h1 1z" class="P"></path><path d="M299 313h0 1c1 2 1 3 2 4-1 2-1 3-1 4v3c-1 0-1 0-2-1-2-2-5-2-8-2h0l-3 1c-1 1-3 3-4 3 0 1-1 2-1 3s0 1-1 1v1c0 1 1 2 1 3-1-1-1-1-2-1-2-2-4-2-6-4-1 2-1 2-1 3l1 1h1c1-1 4 1 6 1v1c-3 1-5 1-7 4-1 2-2 3-2 6h0c2 3 3 9 5 11 1 2 1 3 2 4 0 1 0 1-1 2l-3-3c-1-3-3-5-4-7 0-3-1-6-2-10v-8l1-1 1-4 1-2c1-2 2-3 3-5l1-1c1-1 3-4 5-5h1l2-1c1-1 1-1 2-1l1 1c0 1 1 2 2 2v1h-1 5c2-1 3-3 4-3h1v-1z" class="h"></path><path d="M288 314c0 1 1 2 2 2v1h-1l-3 1c0-1-1-1-1-2 1 0 2 0 2-1l1-1z" class="V"></path><path d="M285 319c1 0 2-1 4-1 0 0 1 0 1 1h0c1 1 1 1 1 2h0l-3 1c-1 1-3 3-4 3 0 1-1 2-1 3s0 1-1 1v1h0l-1-1 2-2c-1 0-2-1-3-1h0c-1-1-1-1 0-2h-1-1-1l1-1 2-2c1 0 1-1 2-1s2-1 3-1z" class="T"></path><path d="M285 319c1 0 2-1 4-1 0 0 1 0 1 1h0c1 1 1 1 1 2h0l-3 1c-1 1-3 3-4 3h0c0-2 1-3 3-4l2-2h-4z" class="e"></path><path d="M299 313h0 1c1 2 1 3 2 4-1 2-1 3-1 4v3c-1 0-1 0-2-1-2-2-5-2-8-2h0 0c0-1 0-1-1-2h0c0-1-1-1-1-1-2 0-3 1-4 1s-2 1-3 1l4-2 3-1h5c2-1 3-3 4-3h1v-1z" class="J"></path><path d="M300 318s0-1-1-1c0-1 1-3 1-4 1 2 1 3 2 4-1 2-1 3-1 4v-1-2h-1z" class="C"></path><path d="M299 323c0-1 1-2 1-3-2 0-2 0-3-2h3 1v2 1 3c-1 0-1 0-2-1z" class="G"></path><path d="M289 318c2 0 4 0 7 1v1 1h-5 0 0c0-1 0-1-1-2h0c0-1-1-1-1-1z" class="L"></path><path d="M291 321c3 0 6 0 8 2 1 1 1 1 2 1l-1 7-2 17v6l-1 1h1c1 0 1 1 2 1l3 1v3h0c-2 0-3 0-4 1h-1c-1 0-2 1-2 2l1 1-7 1-1-1h-1v-1h0l-1 1v1c-1 1 0 4 0 6l-4-2h1c-1-4-1-6-4-10-1-1-1-2-2-4-2-2-3-8-5-11h0c0-3 1-4 2-6 2-3 4-3 7-4v-1c-2 0-5-2-6-1h-1l-1-1c0-1 0-1 1-3 2 2 4 2 6 4 1 0 1 0 2 1 0-1-1-2-1-3v-1c1 0 1 0 1-1s1-2 1-3c1 0 3-2 4-3l3-1h0z" class="Z"></path><path d="M291 325c2 0 5 0 7 2-1 0-2 0-3 1l-1-1-2 1h-2l1-1h1v-1c-1 0-3 1-4 1h0l3-2z" class="G"></path><path d="M289 323c2 0 3-1 5 1 0 1-2 1-3 1l-3 2c-1 0-2 1-2 2l-1 1c-1 0-1-1-1-1 0-1 1-2 1-2v-1c1-1 2-2 4-3h0z" class="O"></path><path d="M291 321c3 0 6 0 8 2 1 1 1 1 2 1l-1 7v-2h-1l-1-2c-2-2-5-2-7-2 1 0 3 0 3-1-2-2-3-1-5-1h0l-1-1 3-1h0z" class="B"></path><path d="M289 323c2-1 3-1 5-1 2 1 4 2 5 3v4l-1-2c-2-2-5-2-7-2 1 0 3 0 3-1-2-2-3-1-5-1z" class="Z"></path><path d="M286 331c0-1 1-2 2-2h2 1c1 1 3 2 4 3s1 5 1 6-1 1-1 2c-1 2-1 3-4 4 0 1 0 1-1 1l1-1c-2-2-4-1-6-3l-1-2 1-1c-1 0-1-1-1-2l1-1 1 2h0v-1l1-1v1h1c1 1 2 1 3 0-1 0-4-1-5-2s0-2 0-3z" class="K"></path><path d="M286 331c1 0 3 0 4 1 1 0 2 1 3 2 0 1 0 1-1 2h-1c-1 0-4-1-5-2s0-2 0-3z" class="Q"></path><path d="M292 328l2-1 1 1c1-1 2-1 3-1l1 2h1v2l-2 17v6l-1 1h-1v-1c-1-2-1-1-3-2l1-2c0-1 0-1-1-2l-1 1h-2 0-2c-2 0-3 0-5-2s-3-4-4-6v-1c-1-1-1-2-1-4h1c0 1 0 2 1 2 0 1 1 1 1 2 1-1 1-1 3-1h0l1 2c2 2 4 1 6 3l-1 1c1 0 1 0 1-1 3-1 3-2 4-4 0-1 1-1 1-2 1-1 1-4 1-6-2-1-3-3-5-4z" class="d"></path><path d="M281 340c1-1 1-1 3-1h0l1 2c2 2 4 1 6 3l-1 1h-4c-2-1-3-2-4-4l-1-1z" class="Q"></path><path d="M281 340c1-1 1-1 3-1h0l1 2h-3l-1-1z" class="T"></path><path d="M295 328c1-1 2-1 3-1l1 2h1v2l-2 17v6l-1 1h-1v-1c-1-2-1-1-3-2l1-2c0-1 0-1-1-2l-1 1h-2 0v-1c2 0 3-1 4-1 1-1 2-3 3-4 1-4 0-7 1-11h1v-1h-1l-3-3z" class="I"></path><path d="M293 348h1c1 0 2-2 4-3-1 2-1 4-1 7l-1 2c-1-2-1-1-3-2l1-2c0-1 0-1-1-2z" class="J"></path><path d="M294 350l2-1v1c0 1 0 1 1 2l-1 2c-1-2-1-1-3-2l1-2z" class="H"></path><path d="M278 355c-2-2-3-8-5-11h0c0-3 1-4 2-6 2-3 4-3 7-4v1c1 1 1 2 2 4-2 0-2 0-3 1 0-1-1-1-1-2-1 0-1-1-1-2h-1c0 2 0 3 1 4v1c1 2 2 4 4 6s3 2 5 2h2 0 2l1-1c1 1 1 1 1 2l-1 2c2 1 2 0 3 2v1h1 1c1 0 1 1 2 1l3 1v3h0c-2 0-3 0-4 1h-1c-1 0-2 1-2 2l1 1-7 1-1-1h-1v-1h0l-1 1v1c-1 1 0 4 0 6l-4-2h1c-1-4-1-6-4-10-1-1-1-2-2-4z" class="Q"></path><path d="M278 355h1 0c0 2 2 4 3 5 1 2 2 2 3 3l1-1 2 1-1 1v1c-1 1 0 4 0 6l-4-2h1c-1-4-1-6-4-10-1-1-1-2-2-4z" class="K"></path><path d="M283 347c2 2 3 2 5 2h2 0 2l1-1c1 1 1 1 1 2l-1 2c-2 1-2 3-3 5l-2 2v1h-1-1c-1 0-1-1-2 0 0-1-1-2-1-3h0s1 0 1 1c1-1 1-3 1-4-1-1-1-1-1-2h3 1l1-1c0-1-1-1-2-1h-1-1v-1c-2 0-3 1-4 2v1h-1c1-2 1-3 2-3 1-1 1-1 1-2z" class="O"></path><path d="M287 360v-1c-1-2-1-3-1-5h1v3c1 0 1 1 1 2v1h-1z" class="Q"></path><path d="M293 348c1 1 1 1 1 2l-1 2c-2 1-2 3-3 5l-2 2c0-1 0-2-1-2 0-2 0-3 1-4h2c1-1 2-1 2-3v-1l1-1z" class="K"></path><path d="M280 352c-1 0-1 0-1 1h0-1l-4-10c1-3 2-5 3-7h1c0 2 0 3 1 4v1c1 2 2 4 4 6 0 1 0 1-1 2-1 0-1 1-2 3z" class="T"></path><path d="M290 357c1-2 1-4 3-5 2 1 2 0 3 2v1h1 1c1 0 1 1 2 1l3 1v3h0c-2 0-3 0-4 1h-1c-1 0-2 1-2 2l1 1-7 1-1-1h-1v-1h0l-2-1-2-2c1-1 1 0 2 0h1 1v-1l2-2z" class="S"></path><path d="M297 355h1c1 0 1 1 2 1-1 1-1 2-1 3h-1c0-1 0-1-1-2h-1-2c1-1 1-2 2-2h1z" class="O"></path><path d="M290 357v1 1c1 0 2 0 3-1h2 0c0 1 0 1-1 2l2 1h2c-1 0-2 1-2 2l1 1-7 1-1-1h-1v-1h0l-2-1-2-2c1-1 1 0 2 0h1 1v-1l2-2z" class="D"></path><path d="M284 360c1-1 1 0 2 0h1 1 1v2l-1 1h0l-2-1-2-2z" class="S"></path><path d="M296 361h2c-1 0-2 1-2 2l1 1-7 1-1-1c2-1 4-1 7-3z" class="V"></path><path d="M311 424h4 5l2 1v1l-1 1h0 0c1 2 0 5 0 6 1-1 1-2 1-4 1 0 1-1 1-2l2-1h2v3h0 2 1v2c-1 0-1 0-1 1v1s1 1 1 2h1 0 0v2c0 2 1 3 2 5l1 1v4 3 11l-1-1-1 1v-1l-1 2c-1 1-1 1-2 0h0l-2 1 1 2 1-1c1 1 1 2 1 3v9c0 2 1 9 0 10h-1 1v1h-1c0 2 0 5-2 6h0c-1-1-2-1-3-1v1c0 4-1 8 0 11h2v2h0 2c1-1 1-1 1 0 1 0 1 0 1 1-1 2 0 2 0 4 1 0 1 0 1 1v4 1 3c1 1 0 1 0 2v1 1 5 2h-1-1v3h-1 0c-1 0-1-1-2-1h-2l-1 2h1v2h-18-1v-1c-1 0-1-1-1-1h-1 0l-3-1c1 0 1 0 1-1v-4c0-1 0-2 1-3l-1-1v-1-9-1c-1-1-3-1-4-2v-1l-1-4v6h-2l-10-1h0c-1 0-1 0-2-1h0c0-1 0-3-1-4h-1v1 2c-1 0-1-2-1-3h0v-1c0-1-1-1-3-2v-7l-1-1-1 1h0v-2h-1v-1l1-1h-1c0-2 1-3 1-4h-1v-1l1-1h-3 0v-1h0c-2-2-2-4-3-6 1-3 2-6 2-9s0-6 1-8v5h1v3c1 1 1 1 2 1h0c1-3 0-4 1-7 0-2 1-4 1-6s1-5 1-7v-7-2-2c0-2 0-6 1-7h1c-1 0-1-1-1-1h-1l-1-1c0-1-1-1-1-1v-1h3 5 7 0 3 0l5 1h2l1-1h1v-2-3h3l6-1z" class="Y"></path><path d="M283 507h2c-1 1 0 3-1 4h-1v-4h0z" class="q"></path><path d="M274 493h6l-3 1v1l1 1 1-1h0c1 1 2 1 3 2l1 1v-1l1 1v6-1h-1v1c-2-1 0-5-2-7v1c-1-1-1-1-2-1h0c-1 0-2 0-2-1-1 0-1 0-2-1h0-1-1v-1l1-1z" class="U"></path><path d="M288 466h0c1 1 3 1 4 2v1h0c-1 1-2 2-2 3 0 4 0 8 2 11-1 1-1 2 0 3 0 1 0 3-1 4h0c-1 0-1-1 0-2 0 0 0-2-1-3v-1l-1-3h0c1-1 1-1 1-2l-2-1c1-1 0-3 0-4 0-3 1-5 0-8z" class="G"></path><path d="M280 486h1l-1 1 1 1 2 1h0c1 1 3 2 3 4h-5-1-6-1c0-2 1-3 1-4h-1v-1l1-1h2c1-1 3-1 4-1z" class="j"></path><path d="M280 486h1l-1 1 1 1h-2c-1 0-3 0-5 1h-1v-1l1-1h2c1-1 3-1 4-1z" class="D"></path><path d="M279 488h2l2 1h0c1 1 3 2 3 4h-5v-2c0-1-1-2-2-3z" class="i"></path><path d="M274 495h1 0c1 1 1 1 2 1 0 1 1 1 2 1h0c1 0 1 0 2 1v-1c2 2 0 6 2 7v-1h1v1l1 1 1 1-1 1h-2-2-1v1 2c-1 0-1-2-1-3h0v-1c0-1-1-1-3-2v-7l-1-1-1 1h0v-2z" class="c"></path><path d="M276 504h1v-6l1-1c0 2-1 7 0 7h1v-6c1 2 0 5 1 6h2s0 1 1 1h2l1 1-1 1h-2-2-1v1 2c-1 0-1-2-1-3h0v-1c0-1-1-1-3-2z" class="l"></path><path d="M282 460l1-2h0 2c1 0 2 1 3 1h1l3 1h0l-1 2c1 1 1 1 1 2l-1 1h-3v1c1 3 0 5 0 8 0 1 1 3 0 4h0v4c1 1 1 2 1 4 0 1 1 4 1 5h0c-2-3-1-7-3-10h0c-1-2-1-6-2-9v-4-1c0-2-1-5-1-7h1l-1-1v2h-1-1v-1z" class="R"></path><path d="M289 459l3 1h0l-1 2-3-1v-1l1-1z" class="b"></path><path d="M286 467l1 14h0c-1-2-1-6-2-9h1v-5z" class="f"></path><path d="M285 467c0-2-1-5-1-7h1 2v2c0 1 0 1 1 2h-1l-1 3v5h-1v-4-1z" class="Z"></path><path d="M285 468h0l1 1c1-2-1-5 1-7 0 1 0 1 1 2h-1l-1 3v5h-1v-4z" class="p"></path><path d="M284 471c0-2 0-3 1-4v1 4c1 3 1 7 2 9h0c2 3 1 7 3 10h0l1 2h-1-4 0c0-2-2-3-3-4h0l-2-1-1-1 1-1h0l1-1v-8c0-2 0-4 1-5 0-1 0-1 1-1z" class="H"></path><path d="M283 489h1v-2h2 0c0 2 1 2 2 4 0 1-1 1-2 2h0c0-2-2-3-3-4z" class="J"></path><path d="M285 478c0 3 1 6 1 9h-2v2h-1 0l-2-1-1-1 1-1h0 3c-1-2 0-6 1-8z" class="S"></path><path d="M284 486v2l-1 1-2-1-1-1 1-1h0 3z" class="C"></path><path d="M284 471c0 2 0 5 1 7-1 2-2 6-1 8h-3l1-1v-8c0-2 0-4 1-5 0-1 0-1 1-1z" class="g"></path><path d="M277 452v3c1 1 1 1 2 1s1 0 1-1h0l2 1v4 1h1 1v-2l1 1h-1c0 2 1 5 1 7-1 1-1 2-1 4-1 0-1 0-1 1-1 1-1 3-1 5v8l-1 1h0-1c-1 0-3 0-4 1h-2-3 0v-1h0c-2-2-2-4-3-6 1-3 2-6 2-9s0-6 1-8v5h1v3c1 1 1 1 2 1h0c1-3 0-4 1-7 0-2 1-4 1-6s1-5 1-7z" class="i"></path><path d="M271 487l1-1h0v-3c1-3 1-5 1-8v-1h1v2c-1 1 0 6 0 8v1l1 1 1 1h-2-3 0z" class="Q"></path><path d="M277 452v3c1 1 1 1 2 1s1 0 1-1h0l2 1v4 1h1 1v-2l1 1h-1c0 2 1 5 1 7-1 1-1 2-1 4-1 0-1 0-1 1-1 1-1 3-1 5v8l-1 1h0-1c1-1 1-3 1-5 0-4-1-8 0-12h0l-1-1h-1c-1-3 0-7 1-9-1-1-2-1-3-1l-1 1c0-2 1-5 1-7z" class="k"></path><path d="M280 459v-2h1v5 6h-2c-1-3 0-7 1-9z" class="C"></path><defs><linearGradient id="AI" x1="283.626" y1="463.069" x2="281.947" y2="470.19" xlink:href="#B"><stop offset="0" stop-color="#666465"></stop><stop offset="1" stop-color="#787978"></stop></linearGradient></defs><path fill="url(#AI)" d="M282 461h1 1v-2l1 1h-1c0 2 1 5 1 7-1 1-1 2-1 4-1 0-1 0-1 1-1 1-1 3-1 5v-16z"></path><path d="M283 430h7 0 3 0l5 1h4v3l-1 1c-1 3-2 5-2 8-2 8 0 17-1 25v-7h-1c-2-1-3-1-5-1h0l-3-1h-1c-1 0-2-1-3-1h-2 0l-1 2v-4l-2-1h0c0 1 0 1-1 1s-1 0-2-1v-3-7-2-2c0-2 0-6 1-7h1c-1 0-1-1-1-1h-1l-1-1c0-1-1-1-1-1v-1h3 5z" class="n"></path><path d="M283 448h2l1 3-2 1-1-4z" class="M"></path><path d="M284 452l2-1h0c1 2 3 3 4 4-2 0-3 0-5-1h-1c-1-1-1-1 0-2z" class="e"></path><path d="M288 456c1 0 3 0 4 1s1 1 0 2v1l-3-1h-1v-2-1h0z" class="l"></path><path d="M283 458c0-1 0-2 1-3 1 0 2 0 4 1h0v1 2c-1 0-2-1-3-1h-2z" class="i"></path><defs><linearGradient id="AJ" x1="287.023" y1="437.099" x2="280.419" y2="443.22" xlink:href="#B"><stop offset="0" stop-color="#7b7e7f"></stop><stop offset="1" stop-color="#9b9696"></stop></linearGradient></defs><path fill="url(#AJ)" d="M283 435v-2h2v6 9h-2v-4-9z"></path><path d="M275 430h3 7c-1 1-1 1-2 1v4h0v9h-1c0-3 0-5-1-7v-4c-2 0-1 0-2 1-1 0-1-1-1-1h-1l-1-1c0-1-1-1-1-1v-1z" class="b"></path><path d="M279 437l1 2 1 1 1 5v9h0c-1 0-1 0-1-1l-1 2h0c-1-1-1-1 0-2 0-1-1-3-1-4 1-4 1-8 0-12z" class="f"></path><path d="M278 434l1 3c1 4 1 8 0 12 0 1 1 3 1 4-1 1-1 1 0 2 0 1 0 1-1 1s-1 0-2-1v-3-7-2-2c0-2 0-6 1-7z" class="Q"></path><path d="M277 445c1 2-1 4 1 6h1v-2c0 1 1 3 1 4-1 1-1 1 0 2 0 1 0 1-1 1s-1 0-2-1v-3-7z" class="e"></path><path d="M283 430h7 0-4v2l1-1v4h1c1 1 0 3 1 4v1l1 3 1 1 1 2h0c0 1 1 2 1 3l-1 1-1 1c1 2 2 4 4 6v1c-1-1-3-1-3-2-1-2-2-3-2-5 1-1-3-6-3-7-1-2-2-3-2-5v-6h-2v2h0 0v-4c1 0 1 0 2-1h-7 5z" class="D"></path><path d="M287 435h1c1 1 0 3 1 4v1l1 3h0c-1 0-1-1-2-1v-1c-1-2-1-4-1-6z" class="J"></path><defs><linearGradient id="AK" x1="303.197" y1="443.298" x2="290.966" y2="445.51" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#AK)" d="M290 430h3 0l5 1h4v3l-1 1c-1 3-2 5-2 8-2 8 0 17-1 25v-7h-1v-1l-2-2v-1c-2-2-3-4-4-6l1-1 1-1c0-1-1-2-1-3h0l-1-2-1-1-1-3v-1c-1-1 0-3-1-4h-1v-4l-1 1v-2h4z"></path><path d="M290 430h3 0c0 1-1 1-1 1l-1 2c0 1 0 1-1 2 1 1 1 1 1 2 1 1 0 1 1 2v-1c-1-2 0-4 0-6h1 0c1 1 2 0 3 0s1 1 2 1l-1 1h-1c0 1 1 1 0 1v1c0 2-1 3-1 6h0c0-1 0-4-1-4v-2h1v-1c0-1 1-1 1-2h0-3c-1 3 0 6-1 9 0 1 1 1 1 2 0 0-1 1-1 2l-1-2-1-1-1-3v-1c-1-1 0-3-1-4h-1v-4l-1 1v-2h4z" class="B"></path><path d="M289 440l2-2c1 2 0 4 0 6l-1-1-1-3z" class="G"></path><path d="M289 439v-4l2 2v1l-2 2v-1z" class="C"></path><path d="M287 431h1 0l1 1 1-1 1 1c-1 1-1 2-2 3v4c-1-1 0-3-1-4h-1v-4z" class="d"></path><path d="M298 468c1-8-1-17 1-25h0c0 4-1 9 1 11h1c1-1 1-1 2-1l-1 1v4c1 1 1 1 1 2 1 1 2 1 2 2h2v1l1 1 1-1h1 1v1h0 1v7h0l1 1v1 1c-1 2-1 4-2 5s-2 1-3 2v1h-1v1l-1 1v4l1-2h1l-1 1h0v6l2 1c-1 0-1 0-1 2-1 1-1 7-1 8v1l-1-1c0 1 0 1-1 2h3c-1 0-1 0-2 1h1 2 0l1 1v3h0-5v2 1l1 5-1 9v7 1c-1 0-1-1-1-1h-1 0l-3-1c1 0 1 0 1-1v-4c0-1 0-2 1-3l-1-1v-1-9-1c-1-1-3-1-4-2v-1l-1-4c1-1 0-3 0-4v-9l2-26z" class="C"></path><path d="M302 458c1 1 1 1 1 2 1 1 2 1 2 2-1 2-1 4-1 6v4h0v-3l-1 1v4-10l-1-6zm0 68h0c1-2 0-4 1-6v1c1 0 0 2 0 3v11h0l-3-1c1 0 1 0 1-1v-4c0-1 0-2 1-3z" class="G"></path><path d="M305 502c1 1 0 2 0 4h3c-1 0-1 0-2 1h1 2 0l1 1v3h0-5v2 1h-1v-2c1-2 0-4 0-6 1-1 1-3 1-4z" class="N"></path><path d="M310 511h-4c0-1 1-1 1-2v-2h1v1l1-1 1 1v3z" class="V"></path><path d="M304 514h1l1 5-1 9v7 1c-1 0-1-1-1-1v-21z" class="L"></path><path d="M306 488l1-2h1l-1 1h0v6l2 1c-1 0-1 0-1 2-1 1-1 7-1 8v1l-1-1c0 1 0 1-1 2 0-2 1-3 0-4v-13h0 1v-1z" class="Z"></path><path d="M305 462h2v8 8 4 1l-1 1v4 1h-1 0c-1-5-1-9-1-14v-7c0-2 0-4 1-6z" class="i"></path><path d="M305 462h2v8 8 4 1l-1 1v-13l-1 1c0 1 0 2-1 3h0v-7c0-2 0-4 1-6z" class="O"></path><path d="M307 462v1l1 1 1-1h1 1v1h0 1v7h0l1 1v1 1c-1 2-1 4-2 5s-2 1-3 2v1h-1v-4-8-8z" class="S"></path><path d="M311 463v1h0l1 9h-1l-1-3h-1c1-1 1-2 2-3v-4z" class="G"></path><path d="M307 470v5c1-1 1-1 1-2 1 0 1-1 1-2l1-1 1 3h1c0 1-1 1-1 2v2c-1 0-1 0-2-1l-2 2v-8z" class="H"></path><path d="M307 462v1l1 1 1-1h1 1v4c-1 1-1 2-2 3h1l-1 1c0 1 0 2-1 2 0 1 0 1-1 2v-5-8z" class="C"></path><path d="M298 468c1-8-1-17 1-25h0c0 4-1 9 1 11h1c1-1 1-1 2-1l-1 1v4l1 6-1 29c1 1 1 2 1 2v19s0 1-1 1h-1v-1c-1-1-3-1-4-2v-1l-1-4c1-1 0-3 0-4v-9l2-26z" class="Q"></path><path d="M296 494h6c0 1 0 3-1 3-1 2-3 2-3 4 1 1 1 1 1 2v1 2c-1 1 0 2 0 3l-1 2h-1l-1-4c1-1 0-3 0-4v-9z" class="L"></path><path d="M324 504h2v2h0 2c1-1 1-1 1 0 1 0 1 0 1 1-1 2 0 2 0 4 1 0 1 0 1 1v4 1 3c1 1 0 1 0 2v1 1 5 2h-1-1v3h-1 0c-1 0-1-1-2-1h-2l-1 2h1v2h-18-1v-1-1-7l1-9-1-5v-1-2h5 0v-3l-1-1h0-2-1c1-1 1-1 2-1h-3c1-1 1-1 1-2l1 1h2l4-1c3 1 9 1 11 0z" class="l"></path><path d="M311 513h3v2 5c1 3 0 6 0 8v1 3h-1v-10c0-2 1-4 0-6-1 0-1 0-2 1-1-1-1-2-1-3l1-1z" class="V"></path><path d="M311 517c1-1 1-1 2-1v19l-7 1h0v1h-1v-1-1c2-1 4-1 6-1 1 0 0 0 1-1-1-1-1-1 0-3h0l1-10c-1-1-1-2-2-2h0v-1z" class="H"></path><path d="M314 528h4 1 3v4 2 1h-1-7v-1-5-1z" class="d"></path><path d="M315 533v-3-1c2 1 2 1 3 3-1 0-2 0-3 1z" class="U"></path><path d="M321 530h0l1 2v2 1h-1-7v-1l1-1c1-1 2-1 3-1h1v-1l2-1z" class="m"></path><path d="M321 530h0l1 2v2 1h-1c-1 0-1-1-1-2s0-2 1-3z" class="F"></path><path d="M315 520h2 1 4c0-2 0-3-1-5v-1h1 2c0 1 0 1-1 2v1 4 2 2 1 6c0 1 0 1-1 2v-2-4h-3-1-4c0-2 1-5 0-8h1z" class="J"></path><path d="M317 522h1v1c0 1 0 3-1 4v-2c-1-1 0-2 0-3z" class="k"></path><path d="M330 511c1 0 1 0 1 1v4h0c-1-1-1-1-1-2v-1-1h0-2v1h-1v1h-1l-1-1c0 1-1 1-1 1h-2-1v1c1 2 1 3 1 5h-4-1-2-1v-5-2h-3-1c-2 1-3 1-5 0v-2h5 9 1 6 4z" class="G"></path><path d="M318 515h1 1v2c1 1 1 1 1 2h-3v-4z" class="E"></path><path d="M314 515h2v-2l1 1c1 1 1 2 0 3h0l-2 2v1h-1v-5z" class="U"></path><path d="M305 513c2 1 3 1 5 0h1l-1 1c0 1 0 2 1 3v1h0c1 0 1 1 2 2l-1 10h0c-1 2-1 2 0 3-1 1 0 1-1 1-2 0-4 0-6 1v-7l1-9-1-5v-1z" class="o"></path><path d="M305 513c2 1 3 1 5 0v1c-1 1-1 2-1 4-1 0-1 1-2 1h-1l-1-5v-1z" class="n"></path><path d="M305 528h3l2-2h0c0 1 0 3 1 4h1 0c-1 2-1 2 0 3-1 1 0 1-1 1-2 0-4 0-6 1v-7zm19-24h2v2h0 2c1-1 1-1 1 0 1 0 1 0 1 1-1 2 0 2 0 4h-4-6-1-9 0v-3l-1-1h0-2-1c1-1 1-1 2-1h-3c1-1 1-1 1-2l1 1h2l4-1c3 1 9 1 11 0z" class="J"></path><path d="M318 508l1-1h1l1 1h0c1-1 1-1 2-1 0 1 0 1 1 2l2-1v1 2h-6-1l-1-3z" class="e"></path><path d="M318 508l1-1h1v4h-1l-1-3z" class="Q"></path><path d="M324 504h2v2h-18-3c1-1 1-1 1-2l1 1h2l4-1c3 1 9 1 11 0z" class="M"></path><path d="M309 507h5c2 0 3 0 4 1h0l1 3h-9 0v-3l-1-1h0z" class="O"></path><path d="M314 507c2 0 3 0 4 1h0l1 3c-2-1-4 0-5 0-1-2 0-3 0-4z" class="P"></path><path d="M328 513v-1h2 0v1 1c0 1 0 1 1 2h0v1 3c1 1 0 1 0 2v1 1 5 2h-1-1v3h-1 0c-1 0-1-1-2-1h-2l-1 2h1-2v-1c1-1 1-1 1-2v-6-1-2-2-4-1c1-1 1-1 1-2 0 0 1 0 1-1l1 1h1v-1h1z" class="W"></path><path d="M323 521c1 0 1-1 3 0h0c0 1 0 1 1 1 1 2 1 4 1 6v1c-1 1-2 1-3 1h-1l-1 2v-6-1-2-2z" class="H"></path><path d="M323 523c1 0 1 0 2 1v1h-2v-2z" class="D"></path><path d="M323 526c1 0 2 0 3 1l-2 2c0 1 1 1 1 1h-1l-1 2v-6z" class="R"></path><path d="M328 513v-1h2 0v1 1c0 1 0 1 1 2h0v1 3c1 1 0 1 0 2v1 1 5 2h-1-1v-2l1-1c0-2 0-7-2-8-1 0-1 0-2-1v-2c-1-1-1-2-1-4l1 1h1v-1h1z" class="B"></path><path d="M327 517l1 1-1 1h-1v-2h1z" class="E"></path><path d="M325 513l1 1h1v-1h1c0 2 0 2-1 4h-1c-1-1-1-2-1-4z" class="H"></path><path d="M317 460h4v2 1c0 1-1 2-1 3 0 0 1 0 1 1v-1h0c0-1 0-1 1-1h0l1 1h1 0c1-1 1-1 2-3h1l1 2 1-1c1 1 1 2 1 3v9c0 2 1 9 0 10h-1 1v1h-1c0 2 0 5-2 6h0c-1-1-2-1-3-1v1c0 4-1 8 0 11-2 1-8 1-11 0l-4 1h-2v-1c0-1 0-7 1-8 0-2 0-2 1-2l-2-1v-6h0l1-1h-1l-1 2v-4l1-1v-1h1v-1c1-1 2-1 3-2s1-3 2-5v-1-1-12h0 1 3z" class="b"></path><path d="M313 473l2-1v1 3h-1v-1c-1 0-1-1-1-1v-1z" class="Y"></path><path d="M314 464c1 0 2 1 3 1-1 1-1 1 0 2h0v4h-1l-2-1c-1-2 0-4 0-6z" class="E"></path><path d="M317 460h4v2c-1 1-1 1-2 1l-2 2c-1 0-2-1-3-1v-4h3z" class="r"></path><path d="M321 467v-1h0c0-1 0-1 1-1h0l1 1v2c-1 1 0 1 0 2 0 2 0 7 1 9l-1 2c-1 0-1 0-1 2h-5-1-1s0 1-1 2c0 0-1 0-2 1h-4-1l-1 2v-4l1-1v-1h1v-1c1-1 2-1 3-2h1c2-1 7-1 10-1 0-2 1-7-1-8 0-1 0-1-1-2l1-1h0z" class="H"></path><path d="M307 482l8 1s0 1-1 2c0 0-1 0-2 1h-4-1l-1 2v-4l1-1v-1z" class="P"></path><path d="M326 463h1l1 2 1-1c1 1 1 2 1 3v9c0 2 1 9 0 10h-1-2v-3h-1c-1-1-3 0-4 0 0-2 0-2 1-2l1-2c-1-2-1-7-1-9 0-1-1-1 0-2v-2h1 0c1-1 1-1 2-3z" class="C"></path><path d="M326 463h1l1 2c0 3-1 6-1 10 0 0 0 1-1 1l-1-1c1 0 1 0 1-1-1-1-1-5-1-7h0-1v-1c1-1 1-1 2-3z" class="X"></path><path d="M328 465l1-1c1 1 1 2 1 3v9c0 2 1 9 0 10h-1c-1-1-1-2-1-3-1-2-1-6-1-8 0-4 1-7 1-10z" class="M"></path><path d="M322 483c1 0 3-1 4 0h1v3h2 1v1h-1c0 2 0 5-2 6h0c-1-1-2-1-3-1v1c0 4-1 8 0 11-2 1-8 1-11 0l-4 1h-2v-1c0-1 0-7 1-8 0-2 0-2 1-2l-2-1v-6h0l1-1h4c1-1 2-1 2-1 1-1 1-2 1-2h1 1 5z" class="G"></path><path d="M313 496v-4h0v-1c0-1 0-2 1-3h0c1 2 1 3 1 4v3h-1l1 1h-2z" class="f"></path><path d="M313 496h2v1l-1 1c0 1 0 1 1 1v2 2l-1 1c-1-2-1-6-1-8z" class="p"></path><path d="M315 495h0c1-1 1-1 1-2h2 0l1 1-1 8v1h-1c-1 0-1-1-2-2v-2c-1 0-1 0-1-1l1-1v-1l-1-1h1z" class="J"></path><path d="M322 483c1 0 3-1 4 0h1v3h-15c1-1 2-1 2-1 1-1 1-2 1-2h1 1 5z" class="O"></path><path d="M316 483h1 2 1l-1 1h-3v-1zm10 0h1l-1 1h-1c0 1-1 1-2 1v-2h3z" class="P"></path><defs><linearGradient id="AL" x1="308.611" y1="501.737" x2="306.782" y2="496.621" xlink:href="#B"><stop offset="0" stop-color="#7b7a7c"></stop><stop offset="1" stop-color="#91908e"></stop></linearGradient></defs><path fill="url(#AL)" d="M310 494c1 0 1 0 1 1 1 2 1 5 0 7 0 1-1 1-1 2l-1 1h-2v-1c0-1 0-7 1-8 0-2 0-2 1-2h1z"></path><path d="M309 494h1v2l-1 1h-1v-1c0-2 0-2 1-2z" class="l"></path><path d="M310 494c1 0 1 0 1 1v4s-1 1-1 2l-1-1 1-4v-2z" class="f"></path><path d="M311 495c1 2 1 5 0 7 0 1-1 1-1 2-1 0-2 0-3-1 1 0 1 0 2-1 0 0 0-1 1-1 0-1 1-2 1-2v-4z" class="J"></path><path d="M320 495c0-2 0-2 2-4h0l1 2h1c0 4-1 8 0 11-2 1-8 1-11 0h1l1-1v-2c1 1 1 2 2 2h1v-1l1-8 1 1z" class="g"></path><path d="M320 495c0-2 0-2 2-4h0l1 2c-1 3 0 5 0 8l-1-6h-2 0z" class="S"></path><path d="M320 495h0v8l-2-1 1-8 1 1z" class="D"></path><path d="M323 493h1c0 4-1 8 0 11-2 1-8 1-11 0h1l1-1v-2c1 1 1 2 2 2h1v-1l2 1c1 0 1 0 2 1l1-1v-2c0-3-1-5 0-8z" class="C"></path><path d="M311 424h4 5l2 1v1l-1 1h0 0c1 2 0 5 0 6 1-1 1-2 1-4 1 0 1-1 1-2l2-1h2v3h0 2 1v2c-1 0-1 0-1 1v1s1 1 1 2h1 0 0v2c0 2 1 3 2 5l1 1v4 3 11l-1-1-1 1v-1l-1 2c-1 1-1 1-2 0h0l-2 1h-1c-1 2-1 2-2 3h0-1l-1-1h0c-1 0-1 0-1 1h0v1c0-1-1-1-1-1 0-1 1-2 1-3v-1-2h-4-3-1 0v12l-1-1h0v-7h-1 0v-1h-1-1l-1 1-1-1v-1h-2c0-1-1-1-2-2 0-1 0-1-1-2v-4l1-1c-1 0-1 0-2 1h-1c-2-2-1-7-1-11h0c0-3 1-5 2-8l1-1v-3h-4 2l1-1h1v-2-3h3l6-1z" class="d"></path><path d="M321 427h0c1 2 0 5 0 6 1-1 1-2 1-4 0 2 1 4 0 5 0 3 0 7 1 10v1c0 1 0 3-1 4-1-2 0-3 0-5-1-1 0-3-1-4-1-4 0-9 0-13z" class="J"></path><path d="M315 429h0c0-1 1-1 1-2h0c0 3-1 7 1 9v1c-1 2 0 4-1 6h-1v-2c-1-1 0-2 0-3v-1h-1l-2-1c1 0 2-1 3-1v-6z" class="a"></path><path d="M322 429c1 0 1-1 1-2l2-1c0 1 1 2 1 3-1 2 0 6-1 7l-2 1h0l-1-3c1-1 0-3 0-5z" class="E"></path><path d="M313 444h1 1 4v1 6h-1 0c-2 1-2 0-4 0-1-1-1-5-1-7z" class="C"></path><path d="M315 444h4v1 6h-1 0v-6h-1v1c0 2 0 4-1 5 0-1 0-5-1-6h-1v-1h1z" class="r"></path><path d="M311 424h4 5l2 1v1l-1 1h-1v4h-1v-4h-1c-1 1 0 3-1 4h0c0-1-1-3 0-4h-1-1v2h-1v-2h-1c-1 0-2-1-3-1-1 2-1 3-1 5h0 0v2 2h-1-4c-1 0-1 0-2-1h6v-1c0-2 1-5-1-7h-1l-1-1 6-1z" class="O"></path><path d="M308 435h1v-2-2h0 0c0-2 0-3 1-5 1 0 2 1 3 1h1v2h1v6c-1 0-2 1-3 1l2 1h1v1c0 1-1 2 0 3v2 1h-1-1c0 2 0 6 1 7v1c-2 0-5 0-7-1h1v-16z" class="Z"></path><path d="M313 444h-1c0 1 1 5 0 6h-1v-9-4h0v-1h0l1-1v1l2 1h1v1c0 1-1 2 0 3v2 1h-1-1z" class="k"></path><path d="M325 426h2v3h0 2 1v2c-1 0-1 0-1 1v1s1 1 1 2h1 0 0v2c0 2 1 3 2 5l1 1v4 3c0 2 0 2-1 3h-5v-1c-1 0-2 0-3-1l-1-2c1-1 1-2 1-3l1-1v-5-3l-1-1c1-1 0-5 1-7 0-1-1-2-1-3z" class="D"></path><path d="M327 442c0 3 1 7 0 9h-2l-1-2c1-1 1-2 1-3l1-1c0 1 0 1 1 1v-4z" class="F"></path><path d="M325 436c1-1 0-5 1-7 0 1-1 5 0 7 1 0 1 0 2-1h2 1 0c-1 1-3 1-4 2v5 4c-1 0-1 0-1-1v-5-3l-1-1z" class="I"></path><path d="M325 426h2v3h0 2 1v2c-1 0-1 0-1 1v1s1 1 1 2h-2c-1 1-1 1-2 1-1-2 0-6 0-7s-1-2-1-3z" class="D"></path><path d="M326 436v-5c1 1 1 1 1 2h1v-1h1v1s1 1 1 2h-2c-1 1-1 1-2 1z" class="H"></path><path d="M328 435h0c0-1 0-2 1-2 0 0 1 1 1 2h-2z" class="d"></path><path d="M331 437c0 2 1 3 2 5l1 1v4 3c0 2 0 2-1 3h-5v-1h1v-4c0-4 0-8 2-11z" class="N"></path><path d="M329 452h2c1-3 0-6 0-9l1 1c1 1 1 2 2 3v3c0 2 0 2-1 3h-5v-1h1z" class="K"></path><path d="M302 428v-3h3l1 1h1c2 2 1 5 1 7v1h-6c1 1 1 1 2 1h4v16h-1c2 1 5 1 7 1v-1c2 0 2 1 4 0h0l1 1h2l-2 1h-16c-1 0-1 0-2 1h-1c-2-2-1-7-1-11h0c0-3 1-5 2-8l1-1v-3h-4 2l1-1h1v-2z" class="V"></path><path d="M302 428v-3h3l1 1h1c2 2 1 5 1 7v1h-6c1 1 1 1 2 1h4v16h-1v-3-8c0-1 0-2-1-3v2 9 3h-1c-1-1 0-3 0-4v-10l-4-2 1-1v-3h-4 2l1-1h1v-2z" class="C"></path><path d="M302 428v-3h3l1 1h1c2 2 1 5 1 7v1l-1-2h0c-1 0 0-3-1-5-1 1-3 1-3 1h-1z" class="H"></path><path d="M334 450v11l-1-1-1 1v-1l-1 2c-1 1-1 1-2 0h0l-2 1h-1c-1 2-1 2-2 3h0-1l-1-1h0c-1 0-1 0-1 1h0v1c0-1-1-1-1-1 0-1 1-2 1-3v-1-2h-4-3-1 0v12l-1-1h0v-7h-1 0v-1h-1-1l-1 1-1-1v-1h-2c0-1-1-1-2-2 0-1 0-1-1-2v-4l1-1h16l2-1h-2l-1-1h1c2 0 3 1 5 0h1c1 1 2 1 3 1v1h5c1-1 1-1 1-3z" class="b"></path><path d="M305 455h3v2l1 1v-3h1 1v3s0-1 1-1l1 1v1l-1 1h-6-1v-1-4z" class="P"></path><path d="M312 457v-2s1-1 2-1v1c0 1 0 2 1 3l1-1v-2h1l1 2c-1 1-1 2-1 3h-3-1 0v12l-1-1h0v-7-4l1-1v-1l-1-1z" class="O"></path><path d="M302 454l3-1v2 4 1h1 6v4h-1 0v-1h-1-1l-1 1-1-1v-1h-2c0-1-1-1-2-2 0-1 0-1-1-2v-4z" class="I"></path><path d="M334 450v11l-1-1-1 1v-1c0-2-1-4 0-6h0v-1c-2 0-4 0-6 1l-21-1-3 1 1-1h16l2-1h-2l-1-1h1c2 0 3 1 5 0h1c1 1 2 1 3 1v1h5c1-1 1-1 1-3z" class="N"></path><path d="M319 451c2 0 3 1 5 0h1c1 1 2 1 3 1v1h-9l2-1h-2l-1-1h1z" class="Q"></path><path d="M326 454c2-1 4-1 6-1v1h0c-1 2 0 4 0 6l-1 2c-1 1-1 1-2 0h0l-2 1h-1c-1 0 0 0-1-1v-2h0l1-1h0v-5h0z" class="I"></path><path d="M325 460h1 0c1 1 4 0 5 1 0 1-1 1-2 1h0l-2 1h-1c-1 0 0 0-1-1v-2z" class="a"></path><path d="M326 459v-1l3-3h0c1 1 1 2 1 3v1 1h-4-1 0l1-1h0z" class="g"></path><path d="M318 457v-2h1v3h1c0-1 0-2 1-3h0v2h1c0-1 0-1 1-2h0l1 2h0c0-2 0-2 2-3v5h0l-1 1h0v2c1 1 0 1 1 1-1 2-1 2-2 3h0-1l-1-1h0c-1 0-1 0-1 1h0v1c0-1-1-1-1-1 0-1 1-2 1-3v-1-2h-4c0-1 0-2 1-3z" class="o"></path><path d="M321 463l2 1-1-4h3 0v2c1 1 0 1 1 1-1 2-1 2-2 3h0-1l-1-1h0c-1 0-1 0-1 1h0v1c0-1-1-1-1-1 0-1 1-2 1-3z" class="R"></path><path d="M255 155c-1-3-1-5-2-7-1-3-4-7-4-11v-11c1-6 2-10 6-14 4-6 9-9 16-10h0c7-1 12 0 17 2l3 2c3 2 7 5 8 9 1 1 2 2 2 4-1 0-1 0-1 1s-1 2-1 3v1h2c5 0 2-2 4-4 1 2 1 3 1 5h-1v4c0-1 1-1-1-2 0 1-1 1-1 2-1 0-3 1-3 2v6h1s0 1 1 1l-2 2s-1 1-1 2h0c1 1 0 3 0 4-1 3-1 6-1 9-1 1-1 3-2 4 0 1 0 1 1 2h1-3c-1 0-1 0-2 1h-2 0v2c1 2 0 4-1 6l-1 7c0 3-1 6-1 9v1c0 1-1 2 0 4 1 0 2 0 3-1h0v6l1 1c1 0 4-1 5-1 1-1 2 0 3 0-1 0-3 0-4 1h0c2 1 2 1 3 1h1c1 0 1 0 2-1l1 1h-1c-1 1-1 3 0 4h1-1-1c-1 1-1 0-2 1 1 1 1 1 1 2 0 2-2 4-1 6 1 0 1 0 2 1 0 1 1 3 0 4l-1 1h-1l-1 1c0 2 0 3-1 5 0 1 1 2 1 3l2 1h-2c0 2 0 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 1 1v1c0 1 0 1 1 2 1 0 3 0 4 1l-5 1-2 1h0 3 0c-2 0-4 0-5 1h-2-1c-2 1-3 1-4 1h0c-1 1-1 2-2 3l-2 2s-1 0-1 1h0c-1 0-3-1-4-1h0-1c0 1 0 2-1 3v-3c-1-1-2-1-3-2v3l-1 1h-4l1 1c0 3 1 6 2 9h1l1 2c0 1-1 3-2 4l1-2c0-1 0-1-1-2v1c-1-1-2-1-3-3h-1 0-1c-1-1-2-1-2-3h-1v1c0 1-1 1-1 2-2 1-2 3-3 5h1l-1 1h0c0 1-1 2-1 4v5l-1-69v-6c1-3 0-6 0-9v-16-3c1-6-1-13-2-19z" class="j"></path><path d="M272 140v2 1h-3l3-3z" class="h"></path><path d="M264 174v-1h2v2l-1 1c-1 0-1-1-1-2z" class="s"></path><path d="M294 122c1 2 2 3 2 6 0-1-1-2-2-3v-1-2h0z" class="T"></path><path d="M270 152v-1c1 1 1 2 1 2h1l2 3 1 3c-3-2-4-4-5-7z" class="L"></path><path d="M284 130l-1-2v-1h1v-2h1c0 1 1 1 1 2 1 1 1 2 1 3s-1 1-1 2h0c-1-2-1-2-2-2zm-12 10c0-1 0-1 1-2-1-1-2-1-3-1h-1 0c0-1 2-2 3-3l1 1c1 0 1 0 1 1 0 0-1 1-1 2l1 1-2 3v-2z" class="T"></path><path d="M285 149c1-2 2-3 4-4h1c0 1 0 2-1 3-1 2-1 3-3 4h0v-2l-1-1h0z" class="e"></path><path d="M284 130c1 0 1 0 2 2h0v1c2 1 3 0 5 0h0v3h-1-1-2c0-1-2-1-2-2-1-1-1-3-1-4z" class="V"></path><path d="M269 143h3c-1 3-1 6 0 10h-1s0-1-1-2v1c-1-1-1-2-1-3-1-2 0-4 0-6z" class="O"></path><path d="M285 149l1 1v2h0 0c-2 1-6 1-8 0v-1-1h3 1 0c1 1 3 0 3-1z" class="L"></path><path d="M278 151v-1h3 1c-1 1-3 1-4 1z" class="T"></path><path d="M258 155h1c0 1 1 2 2 3h0v1c1 2 1 3 1 5h-1v1h-1v1c-1-4-1-7-2-11z" class="h"></path><path d="M278 152h0c-2-1-2-3-2-4h0l-1-1c0-2 0-3 1-5h1v1l-1 2v2c2 2 7 2 9 2h0c0 1-2 2-3 1h0-1-3v1 1z" class="V"></path><path d="M255 155c1 1 1 1 1 2v1c1 1 1 1 1 2v3l1 6h0v2l2 2 1 1v3h0l-1-1c-1-1-2-1-3-2 1-6-1-13-2-19z" class="L"></path><path d="M291 125c1 1 1 2 2 3s1 4 3 5h0c0 2-1 4-1 6v2c-1 0-1 1-1 2s-1 2-2 3v-2c1-1 1-2 1-3l1-1v-2c0-1 0-2-1-3l-3 3c-1 0-2-1-3-2h2 1 1v-3c0-2 0-3-1-4v-1-1l1-2z" class="O"></path><path d="M273 129h1c3 0 7 2 9 5 1 0 1 1 2 1v8c-1 2-3 3-4 4 1-3 2-6 2-9s-3-4-5-6l-3-1c-2-1-5 0-7 1-1 2-3 4-3 6 0 1 0 1 1 2s1 1 2 0h0l-2 2-2-2c-1-1 0-2 0-4 1-2 3-5 6-6l3-1z" class="p"></path><path d="M260 166l1 2h4 0 0c-1 1-1 1-2 1-1 1-2 0-2 0-2-2-2-4-3-6-1-6-1-12-1-18 0-8 4-17 10-23 3-2 7-4 11-4 5 1 10 4 13 7l-1 2c-4-4-7-7-13-7-4 0-8 1-10 4-7 7-9 15-9 24v7c1 4 1 7 2 11z" class="Z"></path><path d="M288 160c1 0 1 0 1-1h-1-2v-1l3-3 1 1v-1l1 1c-1 1-1 2-1 3-1 4-4 9-6 13-1 3-3 6-5 9-5 8-11 16-13 26-1 7 1 14 4 19 1 1 1 2 2 3h0 0v2c-3-3-6-10-7-14-1-9 1-18 6-26 3-4 7-9 10-14 2-5 4-12 7-17z" class="c"></path><path d="M274 139c2-1 3-1 4-1s2 1 2 2h0-1c0-1 0-1-1-2-2 1-3 2-5 4-1 2-2 5-1 7 1 3 2 5 4 7 2 1 5 1 7 1 3-1 6-4 7-6 1-1 1-2 2-3l1-1v2c-1 2-1 5-2 7l-1-1v1l-1-1-3 3v1h2 1c0 1 0 1-1 1h-1c-2 1-4 1-7 1h0v-1c-2 0-4 0-5-1l-1-3-2-3c-1-4-1-7 0-10v-1l2-3z" class="g"></path><path d="M274 156c2 1 5 3 6 5h0v-1c-2 0-4 0-5-1l-1-3z" class="l"></path><path d="M265 192l2-5 3-7c1-4 1-9 1-14l-3-2c-3-2-5-8-5-11-1-3-2-7-1-10h4c0 2-1 2-1 3v1c0 4 1 9 2 13l3 3c2 2 2 2 2 5 0 6-1 13-4 19-2 6-5 11-7 17-1 6 0 13 0 20l-1 1v-5-4-11c1-1 0-2 1-3h0c0-1 1-2 1-3s1-2 1-3c1-1 1-2 2-3v-1z" class="e"></path><path d="M284 172h1c0 3-1 5-2 8-2 5-5 9-8 14 0 1 0 1-1 2v1c0 1-1 2-1 3l1 3h0v1 1l1 1c0 1 1 3 2 5l3 4-1 2h1c0 1-1 1-1 2l-1-1v1h0c-1 1-1 2-1 3l-1 1-2-2h0c0 1 2 3 2 4-1 0 0 0-1 1s-1 2-1 3h-1-1 0 0c-1-1-1-2-2-3-3-5-5-12-4-19 2-10 8-18 13-26 2-3 4-6 5-9z" class="j"></path><path d="M272 229h0 0c-1-1-1-2-2-3-3-5-5-12-4-19 0 3 0 5 1 7s1 5 2 7 2 3 3 5v1 1h1v-2c1-1 1-1 2 0-1 1-1 2-1 3h-1-1z" class="i"></path><path d="M271 209c1-1 1-3 2-4h0v-5l1 3h0v1 1l1 1c0 1 1 3 2 5l3 4-1 2h1c0 1-1 1-1 2l-1-1v1h0c-1 1-1 2-1 3l-1 1-2-2h0c-1-1-1-2-2-3h0c-1-3-2-6-1-9z" class="O"></path><path d="M271 209c1-1 1-3 2-4h0v-5l1 3h0v1 1l1 1c0 1 1 3 2 5l3 4-1 2h1c0 1-1 1-1 2l-1-1v1-1c0-1 0-1-1-2v-1l-6-6z" class="h"></path><path d="M274 203h0v1 1l1 1c0 1 1 3 2 5l3 4-1 2h1c0 1-1 1-1 2l-1-1v1-1c0-1 0-1-1-2v-1c0-4-3-7-3-12z" class="p"></path><path d="M268 109c-5 2-10 7-12 13v1l-1 4c0 2 0 3-1 4 0 2 1 4-1 5 0-1 0-6 1-7v-2l1-4c2-5 6-11 10-14h1l1-1c5-2 10-3 16-1 4 2 10 7 12 11 1 2 2 6 3 7 0-1 0-1 1-2v1h2c5 0 2-2 4-4 1 2 1 3 1 5h-1v4c0-1 1-1-1-2 0 1-1 1-1 2-1 0-3 1-3 2v6h1s0 1 1 1l-2 2s-1 1-1 2h0c1 1 0 3 0 4-1 3-1 6-1 9-1 1-1 3-2 4 0 1 0 1 1 2h1-3c-1 0-1 0-2 1h-2c1-2 1-3 1-5 1-3 2-5 2-8v-3h0c-1 1-1 2-1 3v-2l-1 1v-2c1-1 2-2 2-3s0-2 1-2v-2c0-2 1-4 1-6v-1-4c0-3-1-4-2-6v-1c-1-1-2-2-3-2-1-1-3-1-3-2-1 0-1-1-2-1-3-1-6-1-8 1h-1v-1l3-1c2-1 8-1 10 0h3l-4-2c-1 0-1 0-2-1-2-1-4 0-7 1 0 0-1 1-2 0h0l5-2c0-1-1-1-1-1-2-1-8-2-11-1h0-1-1-1z" class="O"></path><path d="M303 129h-1l-1-1v-1h2l1-1h0v1c0 1-1 1-1 2z" class="M"></path><path d="M286 116c2-1 3 0 4 0 2 2 3 2 4 5h0c-1-1-2-2-3-2-1-1-3-1-3-2-1 0-1-1-2-1zm-18-7c1-1 2-1 3-1h1c0-1 1-1 2-1h4l1 1c2 0 7 1 9 3v1c-1 0-1-1-2-1s-2-1-3 0h0c0-1-1-1-1-1-2-1-8-2-11-1h0-1-1-1z" class="L"></path><path d="M296 132s0-1 1-1c0 0 1 1 1 2h1v1 3 1c0 2-1 3-1 4v5c-1 2-1 4-1 7 0 2-2 5-2 6 1 1 1 1 2 1h1-3c-2-2 1-4 1-7h0v-3c1-2 0-4 1-7h0v-1-1c0-1 0-1 1-2h0l-1-1h-2c0-2 1-4 1-6v-1z" class="M"></path><path d="M295 139h2l1 1h0c-1 1-1 1-1 2v1 1h0c-1 3 0 5-1 7v3h0c0 3-3 5-1 7-1 0-1 0-2 1h-2c1-2 1-3 1-5 1-3 2-5 2-8v-3h0c-1 1-1 2-1 3v-2l-1 1v-2c1-1 2-2 2-3s0-2 1-2v-2z" class="Z"></path><path d="M295 139h2v1c-1 1-1 1-2 1v-2z" class="p"></path><path d="M293 149c0-1 0-2 1-3h0v3c0 3-1 5-2 8 0 2 0 3-1 5h0v2c1 2 0 4-1 6l-1 7c0 3-1 6-1 9v1c0 1-1 2 0 4l-1 2v1 2h0c0 1 0 1-1 2 0 1 0 1-1 2v1 2c-1 1-1 2-2 3v1h0-3-1c0 1 0 2-1 3l-1 1c-1-2-2-4-2-5l-1-1v-1-1h0l-1-3c0-1 1-2 1-3v-1c1-1 1-1 1-2 3-5 6-9 8-14 1-3 2-5 2-8h-1c2-4 5-9 6-13 0-1 0-2 1-3 1-2 1-5 2-7z" class="P"></path><path d="M284 187c-1-1-1-2-2-2v-1c1-2 1-3 2-4 0-1 0-1 1-1l1-5c1-2 1-3 1-5s1-4 2-5h1c0 2 0 5-1 6l-3 8c-1 3-1 6-2 9z" class="i"></path><path d="M290 164l1-2v2c1 2 0 4-1 6l-1 7c0 3-1 6-1 9v1c0 1-1 2 0 4l-1 2c0-1 0-1-1-1 0-1 0-1-1-1-1-1-1-2-2-2 0-1 0-2 1-2 1-3 1-6 2-9 1-2 2-5 3-8 1-1 1-4 1-6z" class="K"></path><path d="M275 194h1c2-2 2-5 4-7h1l-1 2h0c1 2 0 4 1 5 0 1 1 1 1 2h1c0-1 0-2-1-3l1-2v-2c1 0 1 1 2 2 1 0 1 0 1 1 1 0 1 0 1 1v1 2h0c0 1 0 1-1 2 0 1 0 1-1 2v1 2c-1 1-1 2-2 3v1h0-3-1c0 1 0 2-1 3l-1 1c-1-2-2-4-2-5l-1-1v-1-1h0l-1-3c0-1 1-2 1-3v-1c1-1 1-1 1-2z" class="O"></path><path d="M277 196l3 6h-1c-1 1-1-1-3 0v-1l1-1c-1 0-1-1-1-2l1-2z" class="Q"></path><path d="M283 189c1 0 1 1 2 2 1 0 1 0 1 1 1 0 1 0 1 1v1 2h0c0 1 0 1-1 2 0 1 0 1-1 2v1 2c-1 1-1 2-2 3 0-2 0-4 1-6v-2c1-3 1-5-1-7v-2z" class="Z"></path><path d="M275 194h1c2-2 2-5 4-7h1l-1 2h0c0 2-1 4 0 6l1 1c2 2 1 4 1 7-2-3-4-5-5-8h0v1l-1 2c0 1 0 2 1 2l-1 1v1c1 2 2 4 2 6v2l-1 1c-1-2-2-4-2-5l-1-1v-1-1h0l-1-3c0-1 1-2 1-3v-1c1-1 1-1 1-2z" class="L"></path><path d="M261 174h3c0 1 0 2 1 2l1-1c0 2-1 3-2 4h-4l-1-1s1 0 1-1c0 0-1-1-2-1v4h2l-1 1c0 3 1 5 3 8 0 1 1 2 1 3h0v2s-1 0-1 1v2l2-5h1v1c-1 1-1 2-2 3 0 1-1 2-1 3s-1 2-1 3h0c-1 1 0 2-1 3v11 4 5l1-1h0v2c-1 2 1 7 0 9-1 1-1 2-1 3 1 2 0 3 1 5v1h1c0-1 0-3 1-4 0 0-1-1 0-1v-1c1-2 1-3 2-4v-1c0-1 1-1 1-1h0 1l-1-1v-1h0 1 0c1 2 2 4 3 5h1c-1 2-2 4-2 6-1 3-2 5-3 7l-1 1v-1c-1 1-1 1-1 2 0 3-1 7-1 10 0 1-1 1-1 2-2 1-2 3-3 5h1l-1 1h0c0 1-1 2-1 4v5l-1-69v-6c1-3 0-6 0-9v-16-3c1 1 2 1 3 2l1 1h0v-3z" class="h"></path><path d="M257 177c1 5 2 9 4 13h0c-1-1-1-2-2-3l-1-2v15c0 3 0 6-1 8v-6c1-3 0-6 0-9v-16z" class="i"></path><path d="M291 190h0v6l1 1c1 0 4-1 5-1 1-1 2 0 3 0-1 0-3 0-4 1h0c2 1 2 1 3 1h1c1 0 1 0 2-1l1 1h-1c-1 1-1 3 0 4h1-1-1c-1 1-1 0-2 1 1 1 1 1 1 2 0 2-2 4-1 6 1 0 1 0 2 1 0 1 1 3 0 4l-1 1h-1l-1 1c0 2 0 3-1 5 0 1 1 2 1 3l2 1h-2c0 2 0 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 1 1v1c0 1 0 1 1 2 1 0 3 0 4 1l-5 1-2 1h0 3 0c-2 0-4 0-5 1h-2-1c-2 1-3 1-4 1h0c-1 1-1 2-2 3l-2 2s-1 0-1 1h0c-1 0-3-1-4-1h0-1c0 1 0 2-1 3v-3c-1-1-2-1-3-2v3l-1 1h-4l1 1c0 3 1 6 2 9h1l1 2c0 1-1 3-2 4l1-2c0-1 0-1-1-2v1c-1-1-2-1-3-3h-1 0-1c-1-1-2-1-2-3h-1v1c0-3 1-7 1-10 0-1 0-1 1-2v1l1-1c1-2 2-4 3-7 0-2 1-4 2-6l2-3-1-1v-2h1 1c0-1 0-2 1-3s0-1 1-1c0-1-2-3-2-4h0l2 2 1-1c0-1 0-2 1-3h0v-1l1 1c0-1 1-1 1-2h-1l1-2-3-4 1-1c1-1 1-2 1-3h1 3 0v-1c1-1 1-2 2-3v-2-1c1-1 1-1 1-2 1-1 1-1 1-2h0v-2-1l1-2c1 0 2 0 3-1z" class="K"></path><path d="M284 222h2v1h-2v-1z" class="O"></path><path d="M286 228v1c-1 0-2 0-3 1h-1c-2 0-3 1-5 0h1l3-1h2c0-1 0 0 1-1h2z" class="e"></path><path d="M288 205c1 2 0 3 0 5v5 4 5 7 2-5s-1 0-1-1h1v-3-1c-1-3-2-6-2-9h0l-1-2v-1l3-6z" class="N"></path><path d="M284 228c1-1 2-1 3-1v-3h1v3h-1c0 1 1 1 1 1v5l-3 3c-1 2-3 4-4 6l-2-2c1 0 1-1 2-1h1c0-1 0-1-1-1 1-2 1-2 2-3h0c1 0 2-1 2-2v-1h-1v-1h-2v-1h1c1-1 2-1 3-1v-1h-2z" class="Z"></path><path d="M286 228h1v1 1 2h-2 0-1v-1h-2v-1h1c1-1 2-1 3-1v-1z" class="L"></path><path d="M277 230c2 1 3 0 5 0v1h2v1h1v1c0 1-1 2-2 2h0c-1 1-1 1-2 3 1 0 1 0 1 1h-1c-1 0-1 1-2 1-1 1-2 1-3 1h0l-1-1c-1 0-1 0-2-1v-1c1-1 2-3 3-4 0-2 1-2 1-4z" class="P"></path><path d="M277 230c2 1 3 0 5 0v1c-2 1-3 1-5 1v1h2 3 1l-4 1v1h3c0 1 0 1-1 1l-2 1s-1 0-1-1c-1 1-3 1-3 2v2c-1 0-1 0-2-1v-1c1-1 2-3 3-4 0-2 1-2 1-4z" class="K"></path><path d="M279 235l-1 1c0-1 0-1-1-1v-1h2v1z" class="O"></path><path d="M290 199c1 0 2-1 3 0 0 0 0 1-1 2l-1 1h-1-1s-2 2-1 3l-3 6v1l-7 18h-1c0 2-1 2-1 4-1 1-2 3-3 4v1c1 1 1 1 2 1l1 1h0c1 0 2 0 3-1l2 2c1-2 3-4 4-6l3-3v-2l1-1h1v4c-1 0-1 0-2 1 0 1 1 2 0 4v1 2c-1 0-2 0-3 1h2v2h0c-1 1-1 2-2 3l-2 2s-1 0-1 1h0c-1 0-3-1-4-1h0-1c0 1 0 2-1 3v-3c-1-1-2-1-3-2-1 0-3-1-4-1v-1l8-18 3-8 6-15c1-1 1-3 2-5 1-1 1-1 2-1z" class="C"></path><path d="M271 244h2v1c2 0 3 1 4 2l-1 1c-1 0-2-1-4-1-1 0-1 0-2-1l1-2z" class="V"></path><path d="M288 235c0 1 1 2 0 4v1 2c-1 0-2 0-3 1h2-5l1-3 5-5z" class="Z"></path><path d="M273 239c1 1 1 1 2 1l1 1h0c1 0 2 0 3-1l2 2-3 3c0 1 0 1-1 2h0c-1-1-2-2-4-2v-1h-2c0-1 0-2 1-3l1-2z" class="L"></path><path d="M273 244s0-1 1-1l1-1h1c0 1 0 1-1 2-1 0-1 0-2 1v-1z" class="P"></path><path d="M273 239c1 1 1 1 2 1l1 1h0c-2 1-3 1-4 0l1-2z" class="Q"></path><path d="M283 250c-2 0-4 0-6-1 1-1 2-3 3-4h1v-2h1 5v2h0c-1 1-1 2-2 3l-2 2z" class="L"></path><path d="M291 190h0v6l1 1c1 0 4-1 5-1 1-1 2 0 3 0-1 0-3 0-4 1h0c2 1 2 1 3 1h1c1 0 1 0 2-1l1 1h-1c-1 1-1 3 0 4h1-1-1c-1 1-1 0-2 1 1 1 1 1 1 2 0 2-2 4-1 6 1 0 1 0 2 1 0 1 1 3 0 4l-1 1h-1l-1 1c0 2 0 3-1 5 0 1 1 2 1 3l2 1h-2-1c0-1-1-3-1-4 0-3 2-8 0-11h0c1-2 0-5 1-7 0-1 1-2 2-2v-1c-1 0-1-1-2-2l1-1 2-1c-2 0-8 0-10 1-1 0-1 0-2 1-1 2-1 4-2 5l-6 15c-1 2-2 5-3 8l-8 18v1c1 0 3 1 4 1v3l-1 1h-4l1 1c0 3 1 6 2 9h1l1 2c0 1-1 3-2 4l1-2c0-1 0-1-1-2v1c-1-1-2-1-3-3h-1 0-1c-1-1-2-1-2-3h-1v1c0-3 1-7 1-10 0-1 0-1 1-2v1l1-1c1-2 2-4 3-7 0-2 1-4 2-6l2-3-1-1v-2h1 1c0-1 0-2 1-3s0-1 1-1c0-1-2-3-2-4h0l2 2 1-1c0-1 0-2 1-3h0v-1l1 1c0-1 1-1 1-2h-1l1-2-3-4 1-1c1-1 1-2 1-3h1 3 0v-1c1-1 1-2 2-3v-2-1c1-1 1-1 1-2 1-1 1-1 1-2h0v-2-1l1-2c1 0 2 0 3-1z" class="P"></path><path d="M300 198c0 1 0 3-1 4h0c-1 0-1-1-2-2l1-1 2-1z" class="m"></path><path d="M269 247c1 0 3 1 4 1v3l-1 1-1-2h-1v-1c0-1-1-1-1-2z" class="Q"></path><path d="M299 217c0-2 0-4 1-5h1c0 1 1 3 0 4l-1 1h-1z" class="r"></path><path d="M291 190h0v6l1 1c-1 1-2 1-4 1v1c-1 2-2 3-2 5 0 1-6 12-6 13h-1l1-2-3-4 1-1c1-1 1-2 1-3h1 3 0v-1c1-1 1-2 2-3v-2-1c1-1 1-1 1-2 1-1 1-1 1-2h0v-2-1l1-2c1 0 2 0 3-1z" class="g"></path><path d="M278 210c1-1 1-2 1-3h1 3 0c0 1-1 3-2 4l-1 4-3-4 1-1z" class="l"></path><path d="M278 219v-1l1 1-2 5c-1 3-2 5-3 7-1 1-1 3-2 4-1 2-1 4-2 5-1 2-3 6-3 8h0c1 2 1 3 1 4l1 1c0 3 1 6 2 9h1l1 2c0 1-1 3-2 4l1-2c0-1 0-1-1-2v1c-1-1-2-1-3-3h-1 0-1c-1-1-2-1-2-3h-1v1c0-3 1-7 1-10 0-1 0-1 1-2v1l1-1c1-2 2-4 3-7 0-2 1-4 2-6l2-3-1-1v-2h1 1c0-1 0-2 1-3s0-1 1-1c0-1-2-3-2-4h0l2 2 1-1c0-1 0-2 1-3h0z" class="X"></path><path d="M269 261c2 1 3 3 3 5h0c0-1 0-1-1-2v1c-1-1-2-1-3-3v-1h1z" class="M"></path><path d="M263 260c0-3 1-7 1-10 0-1 0-1 1-2v1l1-1c0 3 1 6 2 8 0 2 1 3 1 5h-1v1h-1 0-1c-1-1-2-1-2-3h-1v1z" class="P"></path><path d="M268 256c0 2 1 3 1 5h-1v1h-1 0v-2c-1 0-1-1-1-1 0-1 1-2 2-3z" class="K"></path><path d="M290 199c2-1 8-1 10-1l-2 1-1 1c1 1 1 2 2 2v1c-1 0-2 1-2 2-1 2 0 5-1 7h0c2 3 0 8 0 11 0 1 1 3 1 4h1c0 2 0 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 1 1v1c0 1 0 1 1 2 1 0 3 0 4 1l-5 1-2 1h0 3 0c-2 0-4 0-5 1h-2-1c-2 1-3 1-4 1v-2h-2c1-1 2-1 3-1v-2-1c1-2 0-3 0-4 1-1 1-1 2-1v-4h-1l-1 1v-7-5-4-5c0-2 1-3 0-5-1-1 1-3 1-3h1 1l1-1c1-1 1-2 1-2-1-1-2 0-3 0z" class="M"></path><path d="M298 242v-2h-2c-1-1 0-1 0-2l1-2h-1c0-2 1-4 1-5 0-2-1-2-2-2v-4-15-6h0c1-2 2-1 4-1-1 0-2 1-2 2-1 2 0 5-1 7h0c2 3 0 8 0 11 0 1 1 3 1 4h1c0 2 0 4 1 5l-1 1c0 1 0 2-1 3 1 0 1 0 1 1v1c0 1 0 1 1 2 1 0 3 0 4 1l-5 1z" class="e"></path><path d="M290 199c2-1 8-1 10-1l-2 1-1 1v-1c-1 0-1 1-2 2s-1 2-2 3c1 2 0 3 0 5s0 4 1 6c0 1-1 4 0 5v1h0c-1 2-1 6 0 8 0 2-1 6 0 7 0 1 1 0 0 1v3 2c-1 1-1 1-3 1l1-1c-2-3 0-6 0-9v-13h0s-1-1-1-2v-4c0-2-1-4 0-5v-3c1 0 1 0 1-1h-3v1h1v1c-1 1-1 2-2 3 0-2 1-3 0-5-1-1 1-3 1-3h1 1l1-1c1-1 1-2 1-2-1-1-2 0-3 0z" class="c"></path><path d="M288 210c1-1 1-2 2-3v-1h-1v-1h3c0 1 0 1-1 1v3c-1 1 0 3 0 5v4c0 1 1 2 1 2h0v13c0 3-2 6 0 9l-1 1 1 1h0-1c-2 1-3 1-4 1v-2h-2c1-1 2-1 3-1v-2-1c1-2 0-3 0-4 1-1 1-1 2-1v-4h-1l-1 1v-7-5-4-5z" class="S"></path><path d="M288 219l1-1c1 1 1 1 2 1 1 2 0 15 0 18l-1 1v4s-1 2-2 2h0v-2-2-1c1-2 0-3 0-4 1-1 1-1 2-1v-4h-1l-1 1v-7-5z" class="D"></path><path d="M289 230v-1h0 0 1c1 3 0 6 0 9v4s-1 2-2 2h0v-2-2-1c1-2 0-3 0-4 1-1 1-1 2-1v-4h-1z" class="U"></path></svg>