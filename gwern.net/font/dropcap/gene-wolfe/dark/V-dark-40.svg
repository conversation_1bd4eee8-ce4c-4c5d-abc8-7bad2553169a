<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="114 66 789 872"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#6a6968}.C{fill:#414140}.D{fill:#363635}.E{fill:#5a5959}.F{fill:#4f4e4e}.G{fill:#959494}.H{fill:#838282}.I{fill:#1e1d1d}.J{fill:#4a4949}.K{fill:#302f2f}.L{fill:#2b2a2a}.M{fill:#b1afae}.N{fill:#bbb9b8}.O{fill:#3d3c3c}.P{fill:#777676}.Q{fill:#8d8c8b}.R{fill:#262626}.S{fill:#616060}.T{fill:#bab9b8}.U{fill:#cdcccb}.V{fill:#dededc}.W{fill:#c6c5c4}.X{fill:#b3b2b0}.Y{fill:#727171}.Z{fill:#a4a2a2}.a{fill:#d7d6d4}.b{fill:#232322}.c{fill:#7c7a7a}.d{fill:#e9e9e7}.e{fill:#545353}.f{fill:#191818}.g{fill:#e3e2e1}.h{fill:#111211}</style><path d="M723 478h1l1 1v1l-2 1-1-1c0-1 1-1 1-2z" class="b"></path><path d="M639 630c2 1 3 1 4 1-1 1-1 1-3 2-1 0-1 0-1-1v-2z" class="L"></path><path d="M642 566c-3 1-5 1-8 2 3-2 5-3 8-3l-3 1h3z" class="f"></path><path d="M423 399c0-1 1-2 2-2v3l-1 1-1-2z" class="I"></path><path d="M174 256c0-2 2-4 3-5l-3 7h0v-1-1z" class="f"></path><path d="M580 337c2 1 3 3 4 5h-1c-2-1-2-2-3-3v-2z" class="L"></path><path d="M783 627h3l-1 5-1-2c0-1-1-2-1-3z" class="M"></path><path d="M441 766c1 1 1 2 2 2v1c0 1 0 1-1 2l-3-4 2-1z" class="K"></path><path d="M606 435l1 1c0 2-1 3-2 4h-3c1-2 3-3 4-5z" class="L"></path><path d="M510 920c1-1 2-1 3-1v1 5c-1-2-2-3-3-5z" class="S"></path><path d="M305 580l-1-1c-1 0-2-1-2-2v-2c2 1 3 1 3 2v3z" class="U"></path><path d="M736 686c-1 2-3 4-5 5l3-7 1 1h0l1 1z" class="J"></path><path d="M233 505c-1 0-3 0-4-1 0-1 3-2 4-3l1 3-1 1z" class="G"></path><path d="M718 393c0 1 1 3 1 3l2 10v1l-1-1v1c-1-2-1-3-2-5 1-1 1-1 1-2v-3c-1-1-1-2-1-4z" class="I"></path><path d="M324 473l2 9h0c-1 0-1-2-2-3h-1c0-2 1-4 1-6z" class="H"></path><path d="M494 400l1 2 1-1 1-1c0 2 1 4 0 6-1-2-3-4-4-5l1-1z" class="J"></path><path d="M460 780l3 6c-1 0-1 0-1 1v1l-3-7 1-1z" class="L"></path><path d="M303 575h2c1 2 2 2 3 3l2 2h-1-4v-3c0-1-1-1-3-2h1z" class="N"></path><path d="M235 551l1-5h0c1 1 1 3 2 4h0c-1 1-1 2-1 3v1c-1 1-1 2-1 3l-1-6z" class="b"></path><path d="M144 314l1-1 1-1h0l2 6-3 1-1-5z" class="O"></path><path d="M325 589v-2-1l3-1c1 0 3 0 5 1-2 1-3 1-4 1l-4 2z" class="I"></path><path d="M481 528l2-2c0 1 0 3-1 4v5h-1c-1-2-1-2 0-3v-1l-2 2v-1c1-1 0-1 1-1l1-3z" class="L"></path><path d="M668 518c2 2 3 6 3 9l-1-1c0-1 0-1-1-2v-1 5l-1-7v-3z" class="H"></path><path d="M356 708c0-1 1-3 1-4l1-1c1 2 2 4 1 6h0l-3-1z" class="P"></path><path d="M632 342v-4h1l1 3 1 3-2-1h0c0 1 1 2 1 4h-1v-1c0-1 0-2-1-3v-1z" class="b"></path><path d="M539 879l-5 6c0-2 1-5 3-8 1 2 1 2 2 2z" class="C"></path><path d="M357 562h1c1 0 2 1 2 2 2 2 2 2 2 4h-1c-1-1-2-1-2-2l-1-1c0-1-1-2-1-3z" class="R"></path><path d="M739 681l-3 5-1-1h0l-1-1 1-3h1l1-1 2 1z" class="F"></path><path d="M745 659c2 3 0 6 0 8l-1 3c-2-2 1-6 0-8l-1-1h0c1 0 1 0 2-2z" class="N"></path><path d="M160 344v-3h1l3 4-2 2-2-3z" class="H"></path><path d="M735 574l4 1 1 3h-1c-2 0-4-1-5-2l1-2z" class="V"></path><path d="M359 654c1 1 3 1 4 1l-1 1c-4 1-5 0-9-1 2-1 4-1 6-1z" class="e"></path><path d="M661 630h0-1l-1-1c2-1 4-3 5-5l3-3v1c-1 1-3 3-3 5l-3 3h0z" class="R"></path><path d="M634 341v-3h1c1 1 0 2 1 3s1 2 2 4h-1v-1h-1c0 1 0 1 1 2h1-1c-1 0-1-1-2-2l-1-3zm-50 115c1 0 2 0 3 1v1h1l1-2v1 1l-1 1c1 1 2 2 3 2l1 1v1c-2-1-3-2-4-3s-1-1-2-1-2-1-2-2v-1z" class="K"></path><path d="M537 877l1-2c1 0 1 1 2 0 1 0 2 0 3 1l-4 3c-1 0-1 0-2-2z" class="F"></path><path d="M614 716c2 0 5 0 8 1 1 1 1 1 1 3 0 1 0 1-1 2 0-1 0-1-1-2-2-3-3-3-7-4z" class="O"></path><path d="M351 670c5-1 10 0 15 0l1 2c-2 0-2 0-4-1h0-5c-2-1-5-1-7-1z" class="M"></path><path d="M360 579c0-2-1-3-1-5v-1c1 2 3 4 4 5h1l1 1h0 1c-1 1-2 1-2 1h-2l-2-1z" class="X"></path><path d="M766 506v1h1 3c1 1 1 1 2 1-3 2-6 1-10 2l4-4z" class="B"></path><path d="M620 164h8c1 1 3 1 4 2h-13l-3-1c1 0 3-1 4-1z" class="Z"></path><path d="M565 517l2 2c1 1 1 3 2 5-3-2-5-4-6-7h2z" class="E"></path><path d="M702 506l1 1h2c1 1 1 2 1 3h-1-2l-3-1 2-3z" class="Y"></path><path d="M354 620c2 1 5 2 7 4l-10-2c2 0 2-1 3-2z" class="X"></path><path d="M375 610c1 1 3 4 5 4h1l3 3c-1 1-1 1-2 1l-1-1c-2-2-5-5-6-7z" class="R"></path><path d="M690 481l1 1v2h1 1l1 1h0-1v1l3 2h-1-1l-1 1v-1c-1 0-1-1-2-2l-1-1-1-2 1-1v-1z" class="b"></path><path d="M581 327l-2-5c-1-3-1-7-1-10 1 2 0 4 2 6 1 3 1 6 1 9z" class="O"></path><path d="M854 293c1 2 1 3 2 5-1 2-2 5-4 7 0-3 1-4 0-6 0-1 2-5 2-6z" class="B"></path><path d="M449 735l4 7c0 1-1 2-1 3l-3-4v-1-5z" class="e"></path><path d="M235 501c5-2 8 0 12 2h-4c-3-1-6 0-8-2z" class="E"></path><path d="M629 330c1 1 1 3 2 5 1 1 1 1 2 3h0-1v4l-2-3c0-1 0-2-1-3 0-2-1-5 0-6z" class="R"></path><path d="M283 500c1 2 2 5 4 6v2 1h0c-3-2-4-5-6-7l2-2z" class="F"></path><path d="M499 513c0-3-1-8 0-10h2v10h-2z" class="J"></path><path d="M530 654c1 0 1-2 2-3 1 4 1 7 0 10 0-1-1-1-1-2-1-2-1-3-1-5z" class="D"></path><path d="M164 345l3 4c0 1-1 2-1 3l-4-5 2-2z" class="G"></path><path d="M695 503v-1c-1-3-3-6-5-9h1c2 1 3 4 5 5 1 2 1 4 1 6l-1 1-1-2z" class="K"></path><path d="M685 499l-2-2v-1l4 2c2 0 2 1 3 2 0 1 2 1 3 1l1 2h-1c-2 0-5-1-6-3v-1h-2z" class="R"></path><path d="M566 125c1 1 4 0 5 0-1 3-4 3-6 3h-2v-3h3z" class="Q"></path><path d="M569 156c1 2 1 4 1 5v1c0 2-2 5-4 7 0 0-1 1-2 1 3-5 4-8 5-14z" class="I"></path><path d="M244 558c2 1 2 2 3 1v-2-1h0l5 8c-3-2-6-3-8-5v-1z" class="B"></path><path d="M342 538l-1 9c0 2 0 3 1 4-2-1-3-2-4-3l4-10z" class="R"></path><path d="M247 628v-3c0-1 0-3 1-3s3 4 4 5c-2 1-3 0-5 1z" class="N"></path><path d="M704 450h1 0c0 3-1 5-3 7h0-1v-4l3-3z" class="L"></path><path d="M574 534l2 1c0 1 0 0-1 1-1 3-1 6-2 8 0-1-1-1 0-2v-1c0-1 1-2 1-3s1-2 1-2c-1 0-1 0-2-1l-1 1v1l-1 1h0l-1 2c0-1 0-2-1-3h2c-1-1-1-2-2-3h2 2 1z" class="D"></path><path d="M696 516c-5 2-10 0-15-1 6-1 9 1 15-2h0s0 1-1 1c0 1-2 1-2 2h3z" class="J"></path><path d="M424 297c1-2 2-3 3-5h1c0 2 1 2 0 3v2c-1 0-1 1-1 2l1 1c-1 0-1 1-1 2 0-1 0-1-1 0h0l-1-1c0-1 1-2 0-4h-1z" class="D"></path><path d="M696 513c1-2 2-3 4-4l3 1c-3 2-4 5-7 6h-3c0-1 2-1 2-2 1 0 1-1 1-1z" class="F"></path><path d="M627 326h2c0 1 1 2 2 2h0c1 2 2 3 1 5l-1 2c-1-2-1-4-2-5l-2-4z" class="C"></path><path d="M454 162l3-1c2 1 3 1 4 3v1c-2 1-6 1-8 1h0c1 0 2-1 3 0 0-2 0-3-2-4z" class="J"></path><path d="M454 162l3-1c1 2 1 2 1 4-1 0-1 0-2 1 0-2 0-3-2-4z" class="F"></path><path d="M351 651l3 1 5 2c-2 0-4 0-6 1l-8-3 1-1h5z" class="C"></path><path d="M258 351l2 1c-3 4-7 6-11 9h-1l1-1h-1l2-2c3-1 6-4 8-7z" class="I"></path><path d="M414 697c3 2 9 7 10 10h0c-4-3-8-6-13-9 2 0 2 0 3 1h1 0l-1-1v-1z" class="V"></path><path d="M717 480l2 8c-2 0-3 0-4-1 0-1 0-2-1-3v-1c0-1 1-2 1-3l1 1 1-1z" class="G"></path><path d="M518 929c1 2 1 5 2 7 0 1 0 1-1 2-1 0-1 0-2-1-1-2-1-5 0-7l1-1z" class="e"></path><path d="M687 645c4-2 10-3 15-3-1 0-2 0-3 1v1h0l-1 1h-10-1 0z" class="O"></path><path d="M309 499l4 5h0l1-2c0 3 1 7 0 9-3-3-4-8-5-12z" class="F"></path><path d="M574 299l3-6c0 2-1 4 0 6 0 2-1 4-2 6-1-1-1-1-2-1l1-5z" class="B"></path><path d="M642 625h2v2l1 1 1-1v1 2h0l-3 1c-1 0-2 0-4-1 1-1 2-3 3-5z" class="D"></path><path d="M706 578c3 2 7-1 11-1-1 2-2 3-2 4h-2c-3 0-6-1-8-2v-1h1z" class="Q"></path><path d="M222 272c0-2 0-3 1-5v4c1 1 2 2 2 3v1c0 3 2 5 2 8h-2c-1-4-1-8-3-11z" class="F"></path><path d="M705 529c1 2 1 6 2 8-1 1-1 3-2 4h-2l2-12z" class="G"></path><path d="M166 121v-6c1-3 4-5 7-7 1-1 3-1 5 0h-1c0 1-1 1-2 1-2 0-3 0-4 1l1 1-1 1c-2 2-3 4-4 7-1 1-1 1-1 2z" class="C"></path><path d="M164 332c1 1 2 4 4 5l1 2v5c1 1 1 1 1 2-3-3-6-7-9-11h1c1 1 2 2 3 4l2 2c0 1 0 0 1 1v-1c-1-1 0-1-1-2 0-1-1-2-2-3l-1-3v-1z" class="O"></path><path d="M450 130c1-2 2-3 2-5 2 3 3 5 5 7l-2 2v-1h-1c-2-1-3-2-4-3z" class="M"></path><path d="M555 847h0c1 1 2-2 3-3h1l-3 5-1 2c-1 1-2 3-3 4v-1l-2 1h0c1-3 3-6 5-8zm52-126c2-3 4-3 7-5 4 1 5 1 7 4 1 1 1 1 1 2h-2 0c0-1 0-2-1-3s-3 0-5 0-3 0-5 1c-1 1-1 1-2 1z" class="J"></path><path d="M557 543c2 1 2 1 3 0l3 3v5c0 2 1 3 1 4v5h0c0-1 0-3-1-5v-1-1c-1-1-1 0-1-2 0-1 0-1-1-2l-1-1c-1-1-2-3-2-4h-1v-1z" class="L"></path><path d="M856 283c2 2 1 4 2 6l-2 9c-1-2-1-3-2-5l2-10z" class="E"></path><path d="M692 474c1-1 1-1 2-1l1 2v2c-1 1-3 0-4-1l-2 2-1 1c-1 0-1 0-1 1l-1 1v-2l1-1c0-1 0-2 1-2 0-2 3-2 4-2z" class="b"></path><path d="M814 260h0c3 7 3 13 2 20-1 1-1 2-1 4h-1c2-7 1-17 0-24z" class="D"></path><path d="M484 522l1 2c1 1 1 1 1 2 0 2-1 5-3 7h0c-1 1-1 1-1 2v-5c1-1 1-3 1-4l-2 2v-1c1-1 1-2 2-3h0l1-2z" class="K"></path><path d="M729 504v2c1 0 1 0 2 1l5-6c-1 3-2 6-4 9h-3c0-1-1-1-1-2 0-2 0-2-1-4h2z" class="e"></path><path d="M280 492v1l1 2h0l2 5-2 2-2-4v-1l-1-1-1-3c1-1 2-1 3-1z" class="B"></path><path d="M280 493l1 2c0 2-1 2-2 2l-1-1c1-1 2-2 2-3z" class="P"></path><path d="M280 492v1c0 1-1 2-2 3l-1-3c1-1 2-1 3-1z" class="c"></path><path d="M789 409l4-2c0 1-1 2-1 4-2 2-4 3-6 5l-1-1v-3c2-1 3-2 4-3z" class="E"></path><path d="M662 522c2-2 4-3 6-4v3c-3 1-6 6-8 8v-3c1-1 2-2 2-4z" class="L"></path><path d="M417 311c1 0 1 1 1 1-1 2-1 3 0 5l-1 1c-2 3-2 6-3 9 0-1-1-2-1-3 0-4 3-10 4-13z" class="E"></path><path d="M607 721c1 0 1 0 2-1 2-1 3-1 5-1l-6 5c-2 1-4 3-6 4 1-3 3-5 5-7z" class="e"></path><path d="M630 353c2 6 4 11 4 17-1-1-3-3-4-5 2-2 0-4 0-7v-5z" class="K"></path><path d="M449 252l2 2h1c-1-3-4-6-6-8h0c4 4 10 9 12 14h0c-1 0-2 0-3-1-2-1-5-5-6-7z" class="C"></path><path d="M706 526c2 1 2 0 3-1l1 2-1 2c0 2-1 5-2 8-1-2-1-6-2-8l1-5v2z" class="N"></path><path d="M706 526c2 1 2 0 3-1l1 2-1 2c-1 0-1 0-3-1v-2zm92-163l2 1-3 3c-1 1-3 2-4 3-2 1-5 3-7 3l12-10z" class="W"></path><path d="M308 505c2 3 3 5 6 8h-1c-1 0-2 0-2-1l-2-1c-1-1-5-3-5-6h2 2z" class="D"></path><path d="M252 627l2 2v1l-2-1c0 1 0 1 1 1v2h0-1v-1c-1 1 0 1-1 2l-2 2c-1-2-1-4-2-7 2-1 3 0 5-1z" class="T"></path><path d="M576 289v1 1l-1 2c-1 2-1 4-1 6l-1 5c-1-2-1-3-3-4h0l-2-3h-1v-2l4 4h1c0-1-1-1-1-2h1c1-2 2-6 4-8z" class="C"></path><path d="M447 779h2c4 6 7 12 9 18-4-5-8-12-11-18z" class="b"></path><path d="M759 327l-1-1v-1h-1v-3c-1-4-1-11 0-14 1-1 0-3 1-5h1c-1 8-1 16 0 24z" class="I"></path><path d="M420 342h6c2-1 4-3 5-5 0-1 1-1 1-2h1c-1 3-2 6-5 8s-6 2-9 1h-1l2-2z" class="M"></path><path d="M737 427c1 1 8 7 8 9-1 1-1 1 0 3l-2 1-3-6c0-1-2-2-2-3-1-1-1-3-1-4zm-14 182l8-6c1 1 2 3 3 4-1 1-5 1-6 2h-5z" class="T"></path><path d="M559 502c-3-5-2-12-2-18l3 14c-1 1 0 4 0 6 0-1-1-2-1-2z" class="B"></path><path d="M455 779l2 2 7 17h-1l-5-10c-1-3-3-6-4-8h0l1-1zM354 652c4 0 7 1 10 1 1 1 2 2 4 3v1h0c3 1 5 2 7 3h0 0c-4-2-8-3-12-4h-1l1-1c-1 0-3 0-4-1l-5-2z" class="D"></path><path d="M512 591c2 2 2 14 2 17h-1c-1 3 0 9 0 12-2-3-1-6-1-9h0v-4-15-1z" class="K"></path><path d="M856 283c1-4 0-7 0-11 1 1 1 2 1 3v1h1c-1-1-1-2-1-3v-1c1 1 1 2 1 3l1 1c0 1-1 3 0 5l1 1c0 1-2 6-2 7-1-2 0-4-2-6zM507 608c1 2 1 3 2 5 2-1 2-4 3-6v4h0l-1-1v3h0l1 1c0 2 0 3-1 5 0-2 0-3-1-4v4 2h-1c-2-4-2-9-2-13z" class="D"></path><path d="M244 549c0 2 1 3 2 5v-1c1 1 1 2 1 3h0v1 2c-1 1-1 0-3-1l-1-3c-2-2-2-3-2-5l2 1 1-2z" class="E"></path><path d="M439 725h1l3 4c1 1 3 2 4 4l2 2v5 1l-10-16z" class="S"></path><path d="M857 333c0 2 1 2 0 4h0c1-1 2-3 3-4l1-1 2-3v1l-7 10-2 1c-1 0-3 3-4 4h-1c2-4 5-9 8-12z" class="O"></path><path d="M308 432c2 4 3 7 1 11-1 3-3 6-4 8-1-3 1-6 2-9s1-7 1-10z" class="T"></path><path d="M682 598c1-1 1-2 2-2 1-2 2-3 3-4l2-2 2-2h2c0-1 0-1 1-1l1-1c2-1 4 0 6 0l1 1h1v2 1c-1 0-3 0-4-1l1-1v-1c-3-1-5 0-8 2-1 1-3 2-4 4l-5 5h-1z" class="I"></path><path d="M389 655l2 1c1 1 3 2 4 3v1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1-4-4-2-2-1-5z" class="E"></path><path d="M389 655l2 1c1 2 1 4 1 6l-2-2-1-5z" class="S"></path><path d="M244 549c-1-2-1-5-1-7 0-3 1-6 3-8l1 1c-2 6-2 12-1 18v1c-1-2-2-3-2-5z" class="U"></path><path d="M631 259c3-1 6-2 8-2h0c-1 1-1 1-2 1 2 0 3 0 5 1h4-1c-2 1 1 0-2 1h-2 0v1 1h6c-2 1-6 1-8 1-1-1-2-3-3-3 0-1-2 0-3 0s-1-1-2-1z" class="C"></path><path d="M765 505c1 0 1 0 1 1l-4 4h0c-1 0-3 1-4 0h-4v1l-1-1c2-2 3-3 6-4l-2 3h1c2-1 5-2 6-4h1z" class="b"></path><path d="M209 407c2 1 3 3 5 4h0c1 0 2 0 3 1v1c-1 0-1 0-1-1-1 0-1 0-2 1l-1 1c-3 0-7-4-9-7l4 1 1-1z" class="S"></path><path d="M305 397h1c2 2 6 3 8 5 0 2 0 1-1 3-1 1-3 2-4 2-1-2 0-2 1-5l-4-2c-1-1-1-2-1-3z" class="G"></path><path d="M645 665c4-2 7-5 12-6-3 3-7 4-10 7-1 1-2 2-2 3v1c-2 1-4 2-7 3l7-8z" class="D"></path><path d="M347 498c1 0 2 3 3 4 2 6 5 9 9 14h0 0l-3-3h0l-2-1c0-1-1-2-2-3 0 1 1 2 1 3l1 1v1l-4-7c-1-1-2-2-2-3-1-2-1-4-1-6z" class="I"></path><path d="M478 520h3v2 2c-1 1-1 1-1 2l-1-1c-1 1-2 3-3 4h-1c-1-3 0-4 2-6 0-1 1-2 1-3z" class="D"></path><path d="M478 520h3v2c-1 0-1 1-2 0h-1l-1 1h0c0-1 1-2 1-3z" class="J"></path><path d="M322 509c2 2 5 5 5 8 0 1 0 2-1 2l-2 2c-1-1-1-2-2-4v-1-5-2z" class="G"></path><path d="M838 333c1-1 3-3 5-4 1-2 3-5 5-7v3c-1 3-4 5-5 8l-3 3-1 1v-1-2l-1-1z" class="C"></path><path d="M685 499h2v1c1 2 4 3 6 3h1 1l1 2c0 2-1 2-2 3 0 0-1 0-1 1h-2v-1h1l1-1-1-1v1l-2-1c-1-3-3-5-5-7z" class="f"></path><path d="M518 920l5-1v2c0 1 0 2 1 4l-4 4v1 6c-1-2-1-5-2-7v-1l1-1v-1c1-2 2-4 1-6h-2 0z" class="F"></path><path d="M290 352l1 1h-1c0 1 0 2-1 3v1c-1 1-1 1-1 2h1 0c0 2-1 4-2 6l-3 4-5 5-1 1h0c6-7 9-15 12-23z" class="b"></path><path d="M343 569c-5-5-7-11-9-17 3 4 6 7 8 12 0 1 0 3 1 5z" class="F"></path><path d="M768 496l2-1c-1 3-6 6-6 9v1c-1 2-4 3-6 4h-1l2-3 1-2a30.44 30.44 0 0 0 8-8z" class="E"></path><defs><linearGradient id="A" x1="375.765" y1="669.514" x2="367.735" y2="672.486" xlink:href="#B"><stop offset="0" stop-color="#8e8c8d"></stop><stop offset="1" stop-color="#a8a7a6"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M366 670c5 0 11 0 17 2-2 1-5 0-8 1-2 0-4 0-6 1h0c0-1 0-1 1-2h-3l-1-2z"></path><path d="M743 661l1 1c1 2-2 6 0 8-1 4-3 7-5 11l-2-1-1 1 1-3 2-2 3-6c1-3 1-5 1-9z" class="E"></path><path d="M722 164h12c-1 1-2 1-4 2l-27-1 1-1h18z" class="U"></path><path d="M567 519l1-2c2 2 3 4 5 4l5 5h-1l-1-1-1-1c-1 0-1 0-2-1l-1 1v2 1l1-1h0 0 0v2h1c1 0 0 0 1 1-1 0-2-1-4-2 0 0-2-2-2-3-1-2-1-4-2-5z" class="C"></path><path d="M864 279v-7c-1-3-3-7-1-9l2 12v11c0 2 0 4-1 6 0 1 1 3 0 4v3h-1 0c1-6 1-13 1-20z" class="I"></path><path d="M721 508c2-2 4-3 6-4 1 2 1 2 1 4 0 1 1 1 1 2-2 0-5 1-7 1h-2c0-1 0-2 1-3z" class="O"></path><path d="M385 613l4 4h1v-2-1c1 1 1 1 1 2l1 1v1c1 1 1 3 1 4 1 1 2 2 3 4-4-4-9-8-12-12v-1h1z" class="R"></path><path d="M491 555l1-1c1 1 1 2 0 4l1 1-2 6v2c-1-1-1-4-1-6l-1 1-1-1c0-2 0-3 1-5 1 0 1 0 2-1z" class="K"></path><path d="M489 556c1 0 1 0 2-1 0 2-1 3-1 5-1 0-1 1-2 1 0-2 0-3 1-5zm2 9v-2c0-1 0-4 1-5l1 1-2 6z" class="D"></path><path d="M278 501c1 1 1 1 1 2l1 1 2 2c0 1 1 1 1 2h0c1 1 1 2 2 3l-1 1c-2-1-4-2-6-2v-1l-3-6h1l1 2 2-1-1-3z" class="R"></path><path d="M279 504c1 1 2 3 3 5h0c-2-1-4-3-5-4l2-1z" class="D"></path><path d="M254 300c2-3 5-6 7-9h0v1c1 0 2-1 3-2l1 1c-1 2-2 3-3 5l-1-1-8 9c-1 0-1 0-1-1 1-1 2-2 2-3z" class="C"></path><path d="M621 263l10-4c1 0 1 1 2 1s3-1 3 0c1 0 2 2 3 3h-3l-4-2-11 4v-2z" class="F"></path><path d="M740 562c3 3 5 6 6 10l-1 1c-1 0-2-1-3-2 0 0 0-1-1-1-1-2-2-3-2-4-1-2 0-3 1-4z" class="a"></path><path d="M201 399l8 8-1 1-4-1c-3-3-6-5-8-7 2 0 2 0 4 2 1 0 1 0 2-1-1-1-1-1-1-2z" class="W"></path><path d="M633 594c3 0 5 0 8-1 1 1 2 1 3 2h-1v2c-1 0-3 1-4 1h-4l-2-2 1-1c-1 0-1-1-1-1z" class="S"></path><path d="M678 671c7 1 13 3 19 3 0 1-1 1-2 2l-3-1-10-1c-2-1-3-1-5-1h-1l2-2z" class="a"></path><path d="M237 540l2 3 1-1 1 8c0 2 0 3 2 5l1 3v1l-6-9h0c-1-1-1-3-2-4 0-2 0-4 1-6z" class="Q"></path><path d="M237 540l2 3c-1 2-1 3-1 5v2c-1-1-1-3-2-4 0-2 0-4 1-6z" class="M"></path><path d="M580 282h2l1 1c-2 2-3 5-5 8 0 0-1 1-1 2l-3 6c0-2 0-4 1-6l1-2v-1-1c0-1 1-2 1-3 1-1 2-3 3-4z" class="F"></path><path d="M642 565l13-3c2-1 4-1 6-2l1 1-1 1c-1 1-3 1-5 2l-14 2h-3l3-1z" class="I"></path><path d="M627 692l4-2c2 1 3 1 5 1s6 0 7 1l1 1h-9l-4 1h-7c1-1 2-1 3-2z" class="P"></path><path d="M624 694c1-1 2-1 3-2 3 1 6-1 8 1l-4 1h-7z" class="B"></path><path d="M224 284l1 1h0c1 2 2 4 2 6l3 3c1 0 3 2 3 2 2 3 3 6 6 8h0-1c-8-4-12-12-14-20z" class="Q"></path><path d="M269 584c4-3 5-7 8-11 1 3 0 7 2 9h0l-1 1c-2 0-2 0-3 1h-6z" class="C"></path><path d="M597 370c-1-1-1-2-2-3v-4-1-2c0-2 1-3 2-4v1l5 9c-2-1-3-1-4-1h-1v5h1-1z" class="D"></path><path d="M660 529c-3 3-9 4-13 6 4-5 11-9 15-13 0 2-1 3-2 4v3z" class="I"></path><defs><linearGradient id="C" x1="869.283" y1="329.15" x2="865.217" y2="336.85" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#C)" d="M864 336c3-5 6-10 8-15v-1 1 2h0 0l1-1v-1h0l1-1c0 2-1 3-1 5l-5 10c0 1-1 2-1 3l-3-2z"></path><path d="M632 321c-2-2-3-7-4-10-1-1-1-1-1-2 2 2 3 6 5 6 2 4 4 9 4 13l2 10-3-9v-1s-1-1-1-2c0-2 0-3-1-5h-1z" class="O"></path><path d="M557 840c1-4 6-7 8-10h0v1l-5 11-1 2h-1c-1 1-2 4-3 3h0l3-6-1-1z" class="F"></path><path d="M467 149l-1 5v5l1 2c0 2 1 4 2 7v2c-4-6-6-11-6-17v-1h1c1 0 2-2 3-3z" class="N"></path><path d="M188 110c1 3 1 6-1 9-2 2-4 4-6 5v-1l1-1c-1-1-1-2-1-3h-1c1-1 1-2 2-3l1-1v-1l1 1c1 0 2 0 4-1v-4z" class="P"></path><path d="M675 559l1 1c2 0 4-1 6-2 3-1 5-2 8-4l1 1c-4 4-10 7-15 9v7c-2-3-1-8-1-12zM414 673l3 3h1c2 1 3 4 3 6 1 0 1 2 1 2l2 6-11-16 1-1z" class="R"></path><path d="M799 309v2c-2 5-2 12-1 17-1 1-1 1-3 1 0-4 0-9 1-14h0-1c1-3 2-5 4-6z" class="B"></path><path d="M443 768c2 1 5 4 6 6 1 0 0 0 1 1s2 2 3 4l-1-1c-1-1-2-2-3-4h-1c1 1 1 2 2 4h0 0l-1 1h-2 0c-1-1-1-3-2-4l-3-4c1-1 1-1 1-2v-1z" class="f"></path><path d="M443 769c3 3 5 6 6 9l-4-3-3-4c1-1 1-1 1-2z" class="L"></path><path d="M462 788v-1c0-1 0-1 1-1 4 7 7 17 10 25-4-5-6-12-9-18 0-1-2-3-2-5z" class="I"></path><path d="M704 434h1v1c2 4-1 9-3 12-1 1 0 1-1 2 0-3-1-7-1-9 1-2 3-4 4-6z" class="R"></path><path d="M305 462c-2 2-3 5-4 8h0c-1 5-1 9-1 13l-2-2v-1c0-4 0-7 1-10 0-2 0-3 1-4 1-2 2-4 5-4z" class="B"></path><path d="M595 777c0-2 1-4 2-5 3-4 6-10 10-12 1-1 4-1 6-1 0-1 1 0 2 0-1 1-3 1-4 2-8 3-10 10-16 16z" class="D"></path><path d="M585 280v3c-1 3-3 4-3 7l-5 9c-1-2 0-4 0-6 0-1 1-2 1-2 2-3 3-6 5-8l2-3z" class="c"></path><path d="M447 302c1 1 2 2 2 4v1 1 1 1h1v1 3c1 1 1 2 1 2v3 1h0v3 4 1 6l-1 1v2 2h-1v-2l-1-1c0-1 0-2 1-2h0c1-1 1-4 1-5v-6c0-7-1-14-4-19l1-2z" class="O"></path><path d="M731 640h-9v-2-1c3 0 7 1 10 0h1c2 0 3-1 5-2v2l1 1c-2 0-3 1-5 2h-3z" class="C"></path><path d="M314 589h1c3 1 8 6 10 9 1 2 2 3 2 6h0c-3-4-6-7-10-10-1-2-3-3-3-5z" class="L"></path><path d="M560 504c0-2-1-5 0-6 4 8 7 16 13 23-2 0-3-2-5-4-1-2-3-4-5-6-1-2-2-5-3-7z" class="Y"></path><path d="M618 307c4 5 6 13 9 19l2 4c-1 1 0 4 0 6v1c0 1 0 2 1 3 0 1 0 2 1 3v3c-1 0-1-1-1-1-1-6-2-11-4-16l-3-5c1-1 1-2 0-3h0v-1s-1-1-1-2h0l-1-1v-2c-1-1-1-1-1-2v-1h-1c0-1 0-1-1-2v-3z" class="D"></path><path d="M469 821c1 1 5 3 6 4s2 3 3 5h-2c-2-1-3-1-5-1l-1-1c-1-1-1-2-3-3l-1-2c1 0 2-1 3-2z" class="W"></path><path d="M688 593c1-2 3-3 4-4 3-2 5-3 8-2v1l-1 1-10 7c-1-1-1-2-1-3z" class="P"></path><path d="M252 303c0 1 0 1 1 1l8-9 1 1c-3 6-6 10-11 13l-1-1c1-1 1-1 1-2s1-2 1-3z" class="E"></path><path d="M778 506l3-2c1 1 3 1 4 1v1 3l-6 1c-2 1-4 2-5 1 0-1 0-1 1-1l1-1c-1 0-2 0-3-1 2-1 4-1 5-2z" class="D"></path><path d="M785 505v1 3l-6 1h-2c3-1 5-3 8-5z" class="c"></path><path d="M446 336c1 1 1 2 1 3l1 1h1 1l2-2-1-3h1v3 1c-1 2 0-1-1 1v1c0 1 0 0-1 1 0 1 0 2-1 2l-2 4h0l-1 1v2c-1 1-1 1-1 2v2c-1-1-1-2-1-4h0l1-1 1-8h0v-6z" class="O"></path><path d="M356 615l1-1c1 1 3 2 4 2 4 2 6 5 9 7 2 1 3 3 4 4-3-1-8-4-11-5h0c-2-1 0 0-1-1l-2-1v-1-1l-2-1-2-2z" class="b"></path><path d="M291 648l4 1c1 2 0 3 0 6l-2 3-2 2-1 1c-1-1-2-1-3-2h0c1-2 4-4 5-7 1-1 0-2-1-4z" class="d"></path><defs><linearGradient id="D" x1="303.551" y1="494.315" x2="297.854" y2="485.573" xlink:href="#B"><stop offset="0" stop-color="#686867"></stop><stop offset="1" stop-color="#7d7c7c"></stop></linearGradient></defs><path fill="url(#D)" d="M298 481l2 2c1 6 4 11 6 17h-1c-2-2-2-3-4-5l-3-8c1-2 0-4 0-6z"></path><path d="M247 532v2 1l-1-1c-2 2-3 5-3 8 0 2 0 5 1 7l-1 2-2-1-1-8c0-2 1-3 2-5s3-4 5-5z" class="F"></path><defs><linearGradient id="E" x1="257.618" y1="644.2" x2="249.354" y2="636.775" xlink:href="#B"><stop offset="0" stop-color="#6a6a69"></stop><stop offset="1" stop-color="#878586"></stop></linearGradient></defs><path fill="url(#E)" d="M249 635l2-2c1-1 0-1 1-2v1c0 2 1 4 2 6s2 5 3 7 2 4 1 5c-4-4-7-10-9-15z"></path><path d="M814 260c-3-4-3-11-4-15 1 2 2 4 4 6v4c3 6 4 13 3 19v5l-1 1c1-7 1-13-2-20h0z" class="f"></path><path d="M795 385c-1 2-1 3-1 5l1 1h0c1-1 2-1 4-1l-11 8v-7l5-5c1 0 2-1 2-1z" class="S"></path><path d="M783 413l2-1v3l1 1-3 3c-1 1-5 4-5 5l-1-8 6-3z" class="D"></path><path d="M783 413l2-1v3l1 1-3 3c-2-2-1-4 0-6z" class="O"></path><path d="M811 407c-5 4-12 10-19 13 6-6 12-11 18-17 0 1-1 2-1 4h1 0 1z" class="N"></path><path d="M424 754l4-2v1c0 1 1 1 1 1 4 1 10 9 12 12l-2 1c-4-5-10-9-15-13z" class="D"></path><path d="M491 474v1l1 2 1-1c1 1 1 1 2 1v2c-2 0-2 2-3 4l-1 3c1 2 2 3 3 4h0l-1 1v3h-1c0 2-1 7 0 9h1c-1 1-1 1-2 1v-1c-2-1-1-2-1-3 1-1 0-2 0-3 1-3 1-7 0-10v-1h1v-3c1-2 1-3 1-4h-1v-1-1-3z" class="C"></path><path d="M491 486c1 2 2 3 3 4h0l-1 1v3h-1l-1-8z" class="W"></path><path d="M292 353v-6c2 4 5 9 7 14 1 2 2 5 2 8 1 3 0 7 0 10v-1-2-4c-1-1-1-2-2-4h0c0-1 0-2-1-3-2-3-2-8-5-11v-2l-1 1z" class="C"></path><path d="M768 537c2 0 6 2 8 2l1 3h-1c-8-2-13-3-21-2v-1c2-1 5-1 7-2h1 0 5z" class="B"></path><path d="M181 124c-3 0-4 0-6-1-1-1-2-2-2-4s1-3 2-4c2 0 2-1 3 0v3 1h2 1c0 1 0 2 1 3l-1 1v1z" class="S"></path><path d="M559 441c0-2 1-4 1-6 4 3 7 6 11 7l1 1c2 2 6 4 7 5-6-2-12-3-17-7 0-1-1-1-1-2l-2 2z" class="Q"></path><path d="M479 805c1 0 1 0 2 1 0 1 0 1 1 2 2 3 3 7 4 11l1-1c1 2 2 4 2 6h0-1v-1c-2-2-2-3-3-5-1-1 0-1-1-1 0 1 0 2 1 3v1c0 1-1 2 0 3 0 1 0 1 1 2v1h0s1 1 1 2h0c1 2 1 3 1 5h0c-3-2-4-5-4-8h0v-5c-1-4-1-8-3-11 0-2-1-4-2-5z" class="L"></path><path d="M217 310v-1l-1-1c1 0 3 2 4 3s1 2 2 3c3 5 4 9 3 14-1 3-2 6-4 8 0-1 1-2 1-3 1-1 1-2 1-3s1-1 1-2v-3h-1c-1 1-1 2-1 4l-1 2c0 1-1 2-2 3l2-4c1-4 1-9 0-13v-1c-1-3-3-4-4-6z" class="O"></path><path d="M664 542h1v1h0 0v1c-1 2-3 5-6 7h0c-4 4-9 5-13 7-1 0-2 0-2-1h1c1-1 2-1 4-2l4-3c1 0 1-1 2-1 2-1 6-4 7-6h1c0-1 1-2 1-3z" class="I"></path><path d="M471 829c2 0 3 0 5 1h2l4 6v2c-1 1-2 1-2 1l-2-2h-1 0c-2-2-5-5-6-8z" class="B"></path><path d="M281 495h1c1 0 1-1 1-2v1c0 2 2 5 5 6v2c1 2 2 4 4 5v2c0 1 1 1 2 2l1-1c-1-1-1-1-1-2l1 1v1l1 1c-1 0-1 0-1 1l1 1h-1l-4-4-3-3-1 1v-1h0c-2-1-3-4-4-6l-2-5z" class="I"></path><path d="M460 232c3-3 6-5 9-8h1 1c0 2-2 5-4 7l-4 5-3-4z" class="E"></path><path d="M821 295c4-1 9-2 13 0 1 1 2 1 3 2h-2c-1 1-1 1-1 2 1 2 1 3 1 6-3-4-6-6-10-9-1-1-2-1-4-1z" class="Q"></path><path d="M834 295c1 1 2 1 3 2h-2c-1 1-1 1-1 2h-1c-1-1-1-2-3-3l-1-1h5z" class="P"></path><path d="M427 360c1-2 2-3 2-5v-1h1v1c0 3 0 5-1 7-1 0-8 14-10 16h-1v-4c2-2 9-11 9-13v-1zm226-63c1 3 2 7 3 10v4c1 1 1 2 2 2l-2 4c-1-1-1-1-2-3-1-4-3-9-5-14 1 1 2 2 2 3l1 1c1-2 1-5 1-7z" class="b"></path><path d="M754 579h0c-1-4-1-9 1-12l1-1c1 2 2 9 3 12v3 1c-2-1-3-2-4-4l-1 1zm-412-15c1 1 2 2 3 2 1 2 1 1 3 2l1-1c1 2 1 3 1 5 1 2 2 3 2 5v2l-2-2c0-2 0-1-1-2l-1 1-5-7c-1-2-1-4-1-5z" class="B"></path><path d="M730 573c2 0 4 0 5 1l-1 2c-3-1-5-2-7-1-5 2-6 7-12 6 0-1 1-2 2-4 2-1 5-2 8-3 0 0 3-1 4-1h1zM346 164h13v1l-1 1h-40c0-1 0-1-1-1h0l2-1h27z" class="U"></path><path d="M474 796h1c1 3 2 6 4 9 1 1 2 3 2 5 2 3 2 7 3 11v5h0c-3-10-6-20-10-30z" class="J"></path><path d="M786 405h1c1 0 2-1 3-2 2 0 1 1 2 2 0 1-1 1-1 2-1 0-2 1-2 2-1 1-2 2-4 3l-2 1-1-1-4 2c2-3 5-7 8-9z" class="R"></path><defs><linearGradient id="F" x1="800.402" y1="342.09" x2="800.086" y2="329.436" xlink:href="#B"><stop offset="0" stop-color="#313030"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#F)" d="M798 328c1 3 2 5 4 8 1 2 3 4 4 7h-3c-4-4-7-8-8-14 2 0 2 0 3-1z"></path><path d="M868 335c2-1 4-6 5-8 1 1 1 0 0 1l-5 11c-1 0-1 1-2 1v2l-1 1-1 2h0c-1 1-2 1-2 3h1c1-2 3-3 4-5 0 0 1-1 1-2l2-2 5-11c0 1-1 3-1 4l-6 12c-1 0-2 2-2 2-2 1-4 3-5 5-1 1-1 2-2 3l-1 2-2-1c4-5 7-11 11-17 0-1 1-2 1-3z" class="C"></path><path d="M441 402h-1v-1l-2 2c-1-2 1-1 1-3 1-1 2-2 2-3 1-2 2-3 3-5h0c0-2 0-3 1-4h1v-1c1-2 0-3 1-4 0-1 0-3 1-4v1 2c-2 2 0 6-1 9v1 1l-3 6-3 3z" class="f"></path><path d="M513 919l5 1h0 2c1 2 0 4-1 6v1l-1 1v1l-1 1-4-5v-5-1z" class="T"></path><path d="M515 922h1 1l1-1c0 1 0 4 1 6l-1 1h0c-2-2-3-3-3-6z" class="N"></path><path d="M513 919l5 1h0-3c-1 1-1 1 0 2 0 3 1 4 3 6h0v1l-1 1-4-5v-5-1z" class="B"></path><path d="M669 528v-5 1c1 1 1 1 1 2l1 1 3 13v7l-1-2c-1 1-1 4-1 5l-1-8-2-14z" class="G"></path><path d="M495 479c1 2 1 3 1 5s0 2 1 3c0 1 0 2 1 3h0v-2l1-1h0c0-2 0-1-1-2v-1-1c-1 0-1 0-1-1h0c1 0 1 0 2 1s1 2 1 4h1v-1l-1-1v-3h1l1 1c0 1 0 1 1 3v2 1h1c0 2 0 3 1 4-3-1-6 0-9-2-1-4-1-8-1-12z" class="I"></path><path d="M603 433c1 1 2 1 3 2-1 2-3 3-4 5h0v1c1 0 2 1 4 2 1 1 1 1 1 3h0l-13-5c3-2 7-3 9-6v-1-1z" class="D"></path><path d="M676 538c7-6 16-10 22-17-3 8-12 13-20 18-1 0 0 0-1 1h-2l1-2z" class="L"></path><path d="M279 582c3-2 6-6 9-9l-1 4h3c-3 2-16 13-18 13h-3l-1-1v-1c2-1 6-1 8-3l2-2 1-1z" class="M"></path><path d="M588 405h0c0-3-1-6-2-9l-1-3c-1-2-1-3-1-5l3-3c0-1 0-1 1-2l1 1 1 2c-1 1-1 2-2 4 1 3 2 6 4 9-1-1-2-2-3-1v1h-1 0v3 3z" class="D"></path><path d="M430 734v-1c-1-4-2-6-4-9s-5-4-9-5c-1 0-1 0-2-1v-1h6c3 0 6 3 8 6 1 1 1 2 2 3v1c0 2 1 4-1 7z" class="G"></path><path d="M738 647l1 1c-1 1-1 2-2 4 0 2 0 2 2 3 2 2 4 2 6 4-1 2-1 2-2 2l-5-2h-1c-2-2-4-3-4-6 1-2 2-4 4-5l1-1z" class="g"></path><path d="M269 584h6c1-1 1-1 3-1l-2 2c-2 2-6 2-8 3v1h-6l-1-1c-4 0-7-1-10-2 6-1 13 1 18-2z" class="P"></path><path d="M800 323v-4-2l1-1c0-2 0-3 1-5 2-4 6-6 11-8v1h-1v1c0 1-5 3-6 5 0-1 1-1 1-1l4-2h1l1-1c2-1 4 0 7 0v1c2 0 3 0 5 1h0-1-2-2c-1-1-1-1-2-1h-1c-4-1-9 2-12 4-4 4-4 7-5 12z" class="R"></path><path d="M619 166c-10 0-22 0-32-1-4 0-10 1-14 0l1-1h11 35c-1 0-3 1-4 1l3 1z" class="G"></path><path d="M563 781c1 0 0 0 1-1 0 2 0 4-1 6h0l3-3c0-1 1-2 2-2-1 2-6 14-7 15v-1c-2 3-3 7-6 9l2-8 2-2v-3l2-4c1-1 1-4 2-6z" class="S"></path><path d="M517 916h1l1 1h8c-1 3-1 5-3 8-1-2-1-3-1-4v-2l-5 1-5-1c-1 0-2 0-3 1h-1v-4h8 0z" class="C"></path><path d="M549 846h1c2-3 4-5 7-6-3 4-7 8-10 13-3 4-6 10-9 15v-2l1-1v-1h0l-3 2c4-6 8-15 13-20z" class="e"></path><path d="M636 691v-1c1-1 2-2 4-2h1c1-1 3-1 4-1l1 1h1c1 0 1 1 2 1s3 0 5 1l-1 1h-5-1c1 1 3 1 4 3v2c-2-1-5-2-7-3l-1-1c-1-1-5-1-7-1z" class="X"></path><path d="M741 547c5-2 13-3 18 0 1 0 1 1 2 2-1 1-6 1-8 1-2-1-5-1-7-1l-1-1h-1c-1-1-2-1-3-1zM459 261c2 5 4 12 2 18-1 1-2 3-4 4 0-1-1-2-2-3h2v-3c1 0 1 0 1-1 1-2 0-4-1-5 1-3 1-5 2-8-1-1-1-1 0-2z" class="G"></path><path d="M459 263c0 6 1 10 1 15-1 2-1 2-3 2v-3c1 0 1 0 1-1 1-2 0-4-1-5 1-3 1-5 2-8z" class="B"></path><path d="M670 552v1c1 4 0 8-1 13-2 7-4 15-10 20 0-2 1-3 2-5v-2c6-9 7-17 9-27z" class="N"></path><path d="M164 333c-1-1-1-1-1-2l-4-7v-2l-2-4c-1-2-1-4-1-6h-1c1-1 1-1 1-2-1-1-1-2-1-3h0v-1c-1-2-1-3-1-5v-2-2c1-1 1-2 1-3-1-2 0-3 0-4-1-2-1-3-1-4v-1c-1-3-1-9 0-12 0 6 0 11 1 16 0 2 0 5 1 7h1c0 1 0 2 1 3v-1h0c0-1 0-1-1-2 0-1 1-2 1-3h1c0 1 1 2 1 3l-1 1v5l-4-5c0 3 0 6 1 10 2 7 2 16 7 23l1 2v1z" class="R"></path><path d="M394 291c0 1-1 3-2 4v1c-1-1 0-1 0-2v-2c1-1 1-1 1-2h0 1v-3h1v-1-4c-1 3-4 8-4 11l-1 1v-1c1-3 9-22 11-24l2 1c-2 2-3 5-3 8-1 2-3 5-4 8 0 1-1 4-2 5z" class="O"></path><path d="M679 161h31c4 0 9 0 14 1h0 4v1h-6-43v-2z" class="T"></path><path d="M344 648c6 0 12 1 17 2 7 3 14 7 21 10v1l1 1c1 0 1 1 2 1 1 2-1 0 1 1l1 1h-2l-1-1c0-1-1-1-2-1v-1h-1l-1-1h-1c-2-1-2-2-3-3h-2c-1-1-2-2-4-3h-2l-2-2h-2c-3 0-6-1-10-1l-3-1c-1-1-2-1-4-2l-3-1z" class="L"></path><path d="M478 224l3 6c5 10 7 21 7 31l-1-2c-1-3-1-6-2-9l-1-4c-1-3-2-6-4-9 0-1 0-2-1-3h0c-1-2-1-3-2-4v-1c2 2 3 4 4 6 0 1 0 1 1 2 1 3 2 5 3 8v2c1 3 0-1 1 2v1-2l-1-1v-2l-1-3v-2l-2-4v-2h-1c0-3-4-7-3-10z" class="b"></path><path d="M706 524c-1-2-1-6 1-8s6-1 8 0l-6 9c-1 1-1 2-3 1v-2zM450 243c3 0 8 7 11 10 2 3 3 6 6 7 2 0 2 0 3-2l1-2 2-3v1c0 4 0 6-4 9h-3c-4-1-5-6-7-9-3-4-5-7-9-11h0z" class="a"></path><path d="M345 566c1-1 2-3 2-4 2-4 2-9 2-13 1 5 3 9 5 14l-1 3-1-2c-1-1-1-1-1-2-1 1-1 1-1 2s0 2-1 3h0l-1 1c-2-1-2 0-3-2z" class="Q"></path><path d="M461 145h1 1c1 1 0 1 1 1 1-1 1-2 3-3-2 3-4 6-4 9v1c0-2 0-2-1-3l-37 1c-2 1-5 0-7 0-1-1-11 0-13 0v-1c4-1 10 0 15 0h39c1-1 2-4 2-5z" class="b"></path><defs><linearGradient id="G" x1="612.7" y1="415.394" x2="604.3" y2="410.606" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#G)" d="M602 404l2 1c4 5 8 8 13 12h-1c-1 0-1 0-2-1v2h-2l3 3-1 1c-4-5-10-10-14-15 1 0 2 1 3 1 0 1 0 1 1 1l-1-1v-1h-1v-3z"></path><path d="M722 557c6-5 12-8 19-10 1 0 2 0 3 1h1c-4 2-8 3-12 5-3 1-5 3-8 4h-3z" class="M"></path><path d="M341 582l12 14c1 2 3 5 5 7l5 5v2c-8-5-12-13-18-20-2-3-4-5-4-8z" class="D"></path><path d="M852 299c1 2 0 3 0 6l-8 16h-2c0-3 1-5 1-7 3-5 7-10 9-15z" class="P"></path><path d="M736 492c2-5 3-11 5-16 0 8-2 18-5 25l-5 6c-1-1-1-1-2-1v-2c1-2 2-3 3-5 0 1 0 2 1 3h0c2-3 3-6 3-10z" class="S"></path><defs><linearGradient id="H" x1="421.599" y1="253.265" x2="427.953" y2="258.445" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#585857"></stop></linearGradient></defs><path fill="url(#H)" d="M429 261l-18-11 1-1c0 1 0 1 1 1l1 1h1c1 1 1 0 2 1h0 1l1 1h0c1 1 0 1 2 1l1-1v-1l-1-1c-1 1-1 0-2 1h-1l1-1h1v-1l-1-1h-1l-1-1h-1l-1-1h-1v-1c4 2 8 5 11 8 2 1 10 7 11 9h-1l-3-3c-1 0-1-1-2-1 0 1 0 1-1 2h0z"></path><path d="M233 296c2 2 4 3 7 4 2 1 5 2 7 3 1 2 2 3 3 5l1 1-2 1c-1-1-3-2-4-3-2-1-5-1-6-3h0c-3-2-4-5-6-8z" class="I"></path><path d="M588 254l1-2 2-1c1-1-1 1 1-1h1v-1c1 0 1-1 2-1 1-1 2-2 4-3l3-2 1 1c0 1 0 2-1 3h0v1l-1 1v1l-2 1c-2 3 3-3 0 1l-1 1h-1l-1 1c0-3 0-3-2-5-4 4-8 7-10 12-1 2-3 7-4 8v-1c1-5 4-10 8-14z" class="K"></path><path d="M594 249c3-1 4-3 6-2l1 1-4 5-1 1c0-3 0-3-2-5z" class="C"></path><path d="M746 549c2 0 5 0 7 1-4 2-9 3-12 5-2 1-4 3-6 4h-1c-1 0-1 1-2 1-1-1 1-1 1-3h-2c4-4 10-6 15-8z" class="M"></path><defs><linearGradient id="I" x1="389.256" y1="652.783" x2="372.835" y2="650.151" xlink:href="#B"><stop offset="0" stop-color="#6e6e6e"></stop><stop offset="1" stop-color="#9b9b99"></stop></linearGradient></defs><path fill="url(#I)" d="M374 643c5 4 10 9 15 12l1 5-17-13c1 0 1-1 1-2v-2z"></path><path d="M595 777c6-6 8-13 16-16-1 2-1 2-3 4l-12 15h-1c-1 1-1 1-2 3h-1c0 1-1 2-1 3l-1 1h0c-1 1-1 2-2 2h0c2-4 5-8 7-12z" class="N"></path><path d="M572 499l5 8h1v-1c1 1 1 2 1 3 1 4 2 6 2 10h-1c-3-1-5-5-7-7-1-2-3-3-3-5l-1-1c2 1 3 3 5 4 1 1 1 1 2 1h1 0c-1-4-4-8-6-11l1-1z" class="L"></path><path d="M441 402l3-3c0 2 0 3-1 4v6l1 1h0c-2 3-6 7-9 9h-1l-1-1c1-2 2-3 3-4s2-2 3-4c0 0 0-1 1-1l2-4v-2l-5 5-1 1c0 1-1 1-2 1l7-8z" class="K"></path><path d="M447 393c1-1 2-3 3-4 1 2 1 2 1 4-1 6-4 11-7 17l-1-1v-6c1-1 1-2 1-4l3-6z" class="B"></path><defs><linearGradient id="J" x1="696.557" y1="554.075" x2="691.172" y2="572.345" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#b3b2b2"></stop></linearGradient></defs><path fill="url(#J)" d="M699 551v3l1 2-3 7c-2 2-5 8-6 10l-1 1-2-1 1-1 1-1h-1l-1-1 11-19z"></path><path d="M826 266h0l1 1c1 1 1 2 1 3l4 4 3 5h-1c-1-1-1-2-2-3l-3-4c-1-1-2-2-2-4h0-1v7 3c-1 1-1 2-1 3l-1 2v3h0l2-1c2-1 3-1 4 0h1l2 1c1 0 2 1 2 1l2 2h1l2 1-1 1c-2-1-3-2-5-3-1 0-1 0-2-1-1 1-4 1-5 1h-1c-1 0-1-1-1-1-1 1-3 2-4 3v1l-1-1c2-2 3-4 3-7l1-4c1-1 1-2 1-4s-1-7 1-9z" class="I"></path><path d="M397 262h5c1-1 2-1 3-1 4 1 8 4 12 6 0 1 2 2 2 2 0 1 1 2 1 3v1 1c-6-5-15-10-23-12z" class="U"></path><path d="M734 164h51l2 2h-57c2-1 3-1 4-2z" class="Z"></path><defs><linearGradient id="K" x1="864.777" y1="284.597" x2="858.775" y2="294.318" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#3c3c3b"></stop></linearGradient></defs><path fill="url(#K)" d="M858 300c1-2 1-4 2-6 1-4 1-7 0-11h1c0-2 0-3 1-4v1c1 0 1 1 1 2h0c0-1 0-2 1-3 0 7 0 14-1 20-1 2-2 3-2 5-1 2-2 3-2 5v-2h0c0-1 0-2 1-3v-2c-1-1 0-2 0-4h0l-2 2z"></path><path d="M424 297h1c1 2 0 3 0 4l1 1h0c1-1 1-1 1 0 1 1 1 3 0 5l-1 1c-2 0-3-2-4 0h0c-1 2-3 8-4 9-1-2-1-3 0-5 0 0 0-1-1-1l7-14z" class="C"></path><path d="M636 604h-2c-1 1-3 2-4 4l-2 7v2l-1 2c0 1 0 4-1 4v-1-2-2c1-1 1-2 1-3v-1c1-1 1-1 1-2 1-1 1-3 1-4h0c0-1 1-2 1-2v-3h1v-2-1l1-1v-2c-1-1-1-1-1-2l-2-2v1h-1l1-3c1 1 3 1 4 3 0 0 0 1 1 1l-1 1 2 2h0c1 1 1 1 1 2h2v1c-1 1-2 2-2 3z" class="K"></path><path d="M337 641c4-1 8-1 12 0 4 0 8 2 12 2 1 0 1 0 2 1v1h-1l-21-1h0 1v-1l-7-1h6v-1h-4z" class="D"></path><path d="M225 566h1 1c0-2 0-2 1-3 3 3 6 6 11 6l1 1 1 2s0 1 1 2l1 1c1 1 1 2 1 3-2-1-3-2-5-3-4-3-10-5-14-9z" class="W"></path><path d="M664 558h1c0 2 1 4 0 5l-4 10c0 1 0 1-1 2h0v1l-6 5h0-2c6-7 8-15 12-23z" class="L"></path><path d="M424 285c1 1 1 1 1 3 0 1-1 2-1 3l-1 1-6 14-2 5c-1-1 0-5-1-6l4-9c1-3 3-9 6-11z" class="E"></path><path d="M418 296c-1 3-2 9-1 10l-2 5c-1-1 0-5-1-6l4-9z" class="J"></path><path d="M486 502c0 1 1 1 0 3 0 3-1 6 0 9h0c2-2 1-4 3-7 0 3 0 6-1 9s-1 6-2 10c0-1 0-1-1-2l-1-2-1 2h0l-1-1c-1 1-1 1-1 2-1 1-1 3-2 4 0-1 0-2 1-3 0-1 0-1 1-2v-2-2c3-6 4-11 5-18z" class="C"></path><path d="M484 522c0-2 0-2 1-3 1 1 1 3 0 5l-1-2z" class="D"></path><defs><linearGradient id="L" x1="857.853" y1="357.946" x2="845.234" y2="363.744" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#6c6b6c"></stop></linearGradient></defs><path fill="url(#L)" d="M856 355l2 1 1-2c1-1 1-2 2-3 1-2 3-4 5-5-1 5-6 9-9 12-5 5-10 12-17 15h0l-1-1c4-2 7-6 10-9 2-2 4-5 7-8z"></path><path d="M718 653c2-1 5 0 7 1 1 1 2 2 4 3 5 6 8 13 8 21l-1 3h-1c1-4 0-8-2-12-2-8-8-13-15-16z" class="V"></path><path d="M558 139h1c1 1 2 1 3 2v-1l3 4c3 4 4 8 4 12-1 6-2 9-5 14l-2 1c1-2 2-5 3-7 1-1 1-2 1-3l1-2c1-6-2-11-6-15-1-2-2-3-3-5z" class="a"></path><path d="M574 133c3-2 5-4 7-6l1-2 4 9c-2 2-8 2-11 2l-1-1v-2z" class="N"></path><path d="M684 522h8c0 1-1 1-2 2-4 3-8 4-11 7h-1l-3 3-1-1v-2c1-4 7-7 10-9z" class="R"></path><path d="M218 300c6 3 12 8 14 14-2-1-3-3-4-5v1 5l-2-1c0 2 1 3 0 5l-1-5c-2-3-3-6-5-8v-1c0-1-1-1-1-2-1-1-1-2-1-3z" class="M"></path><path d="M225 314v-2c1-3-2-6-4-9 2 1 5 4 7 6v1 5l-2-1c0 2 1 3 0 5l-1-5z" class="Y"></path><path d="M549 473v6c0 1 0 1 1 2v-5c0 5 0 11 1 16 1 12 3 22 11 32h-1c-2-2-4-5-5-7 0-1-1-2-1-3-3-6-5-11-5-18 0-1-1 0-1-1-1-1-1-4-1-5 0-5 0-11 1-17z" class="Q"></path><path d="M678 676c-1 0-2 0-3-1l1-1h6l10 1 3 1c-2 2-4 3-6 3-4 1-7 1-11 0l-1-1 1-2z" class="V"></path><path d="M692 675l3 1c-2 2-4 3-6 3l-5-1h3c2-1 3-1 5-3z" class="d"></path><path d="M678 676c2 1 4 1 6 2h0l5 1c-4 1-7 1-11 0l-1-1 1-2z" class="g"></path><path d="M495 163l1 2c1 0 1 0 2-1 0-1 0-1 1-2l1 1v1c2 0 2 0 3 1-10 1-20 6-28 12l-1-3h1c2-1 1-2 1-4h1 2 0l2 1c1-1 3-2 4-3l-1-1v-1c1 0 2 1 4 2 0 0 1 0 1-1l-1-1 1-2 1 1v1h2 1l-1-2h1 0l1 1 1-2z" class="K"></path><defs><linearGradient id="M" x1="660.725" y1="655.558" x2="666.555" y2="660.62" xlink:href="#B"><stop offset="0" stop-color="#4d4c4e"></stop><stop offset="1" stop-color="#686866"></stop></linearGradient></defs><path fill="url(#M)" d="M668 654c3 1 5 1 7 1h2c-8 3-15 5-23 8l-9 6c0-1 1-2 2-3 3-3 7-4 10-7l11-5z"></path><path d="M429 751c2 1 4 2 6 4 5 5 9 10 14 15l1 5c-1-1 0-1-1-1-1-2-4-5-6-6-1 0-1-1-2-2-2-3-8-11-12-12 0 0-1 0-1-1v-1h0l1-1z" class="L"></path><path d="M589 355s1 0 1-1v5h1c1-1 2-2 2-4 0 4 1 12-1 16 0 1-2 3-3 4-1-2-1-6-2-8 0-2-1-3-3-4h1c1 0 2 0 3 1h1v-6h0v-3z" class="E"></path><defs><linearGradient id="N" x1="374.12" y1="643.699" x2="359.587" y2="638.7" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#cdcccb"></stop></linearGradient></defs><path fill="url(#N)" d="M358 634c1 0 5 3 6 3l9 5 1 1v2c0 1 0 2-1 2-5-3-11-6-17-8h2 1c1-1 1-2 2-3-1 0-2-1-3-2h0z"></path><path d="M800 323c1-5 1-8 5-12 3-2 8-5 12-4v3 1c-6 2-11 2-14 8l-1 3-1 2-1-1z" class="C"></path><path d="M595 334c0 2 0 3 1 5v2 1c1 1 1 3 1 4-2 2-3 6-4 9 0 2-1 3-2 4h-1v-5c1-5 3-12 3-17l2-3z" class="U"></path><path d="M556 455l2 2h0l1 1 1-1v1l-1 1 5 5c0 2-1 2-2 4 0 1-1 2-1 3v1l-2 2v1 1c0 1-1 2-1 2h-2 0c0-1 0-1-1-1v-2c0-2 0-2-1-2v-1c-1 1-1 1-2 1 0 1 0 4-1 5 0 1 0 5 1 7v5c0 1-1 1-1 2-1-5-1-11-1-16l2-14c2 5 3 10 5 16l6-14-5-5h0c0-1-1-3-2-4z" class="R"></path><path d="M703 590h1 2v-1l-2-2v-1h-1c-1 0 0 0-1-1v-1l7-1c1 0 1 1 2 1 0 1 0 3-1 5 0 1 0 2-1 2s-2 1-3 0c-3 0-7 0-10 2h0c-4 2-7 5-10 7l-1-1 4-3 10-7c1 1 3 1 4 1z" class="D"></path><path d="M451 171l14 1c1 2 3 4 3 6-3-1-8 0-11 0 0-1-1-1-1-2-2-1-4-3-5-5z" class="C"></path><defs><linearGradient id="O" x1="480.51" y1="542.69" x2="494.99" y2="531.81" xlink:href="#B"><stop offset="0" stop-color="#383636"></stop><stop offset="1" stop-color="#676767"></stop></linearGradient></defs><path fill="url(#O)" d="M490 525l2-3 1 2h0l-1 9c-2 7-5 12-8 19h0c-1-4 1-8 2-11 2-5 3-11 4-16z"></path><path d="M351 517h2c1 0 2 3 3 3 4 3 8 7 12 9l4 1c1 0 1 1 2 1h-1v1c-2 0-6 0-8-1-4-2-11-6-13-11h0v-1l-1-2z" class="R"></path><defs><linearGradient id="P" x1="349.04" y1="618.418" x2="337.813" y2="610.821" xlink:href="#B"><stop offset="0" stop-color="#424040"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#P)" d="M331 614c1 0 1-1 2-1l1-1 1 1 1-1h3l13 3c2 1 4 2 6 2l2 1v1 1h0c-1 0-1 0-2-1h0l-4-2h-1l-1-1v1 1c1 1 1 1 2 1s2 1 3 2l2 1c1 0 0 0 2 1h1l-1 1c-2-2-5-3-7-4-8-3-15-5-23-6z"></path><defs><linearGradient id="Q" x1="143.492" y1="323.569" x2="161.389" y2="335.352" xlink:href="#B"><stop offset="0" stop-color="#41403e"></stop><stop offset="1" stop-color="#717073"></stop></linearGradient></defs><path fill="url(#Q)" d="M145 319l3-1c2 6 5 10 8 15 1 2 4 6 5 8h-1v3c-6-7-11-16-15-25z"></path><path d="M571 817v3c-1 2-3 8-6 10h0c-2 3-7 6-8 10-3 1-5 3-7 6h-1l9-12 13-17z" class="Y"></path><path d="M536 866l3-2h0v1l-1 1v2l-11 24c-1-1 0-6 0-9l9-17z" class="B"></path><defs><linearGradient id="R" x1="230.343" y1="549.932" x2="239.894" y2="556.736" xlink:href="#B"><stop offset="0" stop-color="#727271"></stop><stop offset="1" stop-color="#898787"></stop></linearGradient></defs><path fill="url(#R)" d="M233 543l1-2h1c1 3 0 7 0 10l1 6 7 18-1-1c-1-1-1-2-1-2l-1-2-1-1-1-1c0-3-2-4-3-6-3-6-4-12-2-19z"></path><path d="M661 579v2c-1 2-2 3-2 5l-5 5c-3 2-7 3-11 6v-2h1c-1-1-2-1-3-2 8-1 14-9 20-14z" class="a"></path><defs><linearGradient id="S" x1="451.196" y1="159.039" x2="441.481" y2="166.912" xlink:href="#B"><stop offset="0" stop-color="#5b5b5a"></stop><stop offset="1" stop-color="#7e7c7c"></stop></linearGradient></defs><path fill="url(#S)" d="M433 162h21c2 1 2 2 2 4-1-1-2 0-3 0l-4-1-10 1-2-2h-1v-1c-2 0-6 0-7-1h4z"></path><path d="M437 164c3 0 10 0 12 1h0l-10 1-2-2z" class="G"></path><path d="M305 575c5 1 10-1 15 1 3 1 8 0 10 2-4 0-9 2-13 2-2 1-6 1-8 0h1l-2-2c-1-1-2-1-3-3z" class="X"></path><path d="M654 591h1c1-1 2-1 3-2h1c-2 2-3 4-5 5s-4 2-5 4c-5 3-10 5-14 9v-1c1-1 1-1 1-2s1-2 2-3v-1h-2c0-1 0-1-1-2h0 4c1 0 3-1 4-1 4-3 8-4 11-6z" class="J"></path><defs><linearGradient id="T" x1="509.35" y1="611.008" x2="509.877" y2="589.009" xlink:href="#B"><stop offset="0" stop-color="#464545"></stop><stop offset="1" stop-color="#666665"></stop></linearGradient></defs><path fill="url(#T)" d="M508 592c1-1 1-2 2-3l1 1c0 1 0 1 1 2v15c-1 2-1 5-3 6-1-2-1-3-2-5l1-16z"></path><defs><linearGradient id="U" x1="508.56" y1="889.617" x2="494.94" y2="888.883" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#U)" d="M492 877h1c0 1 1 1 2 2 0-1 0-1 1-1v-1l2 4c5 8 10 15 13 24-7-8-13-18-19-28z"></path><path d="M481 463c0-1-1-2-1-3s0-2-1-2v-1l-2-2c-1 0-2-1-3-2h0-1c-2 0-2 2-3 3-2 3-4 6-6 8-2 3-5 5-8 8v-1l5-5c1-2 0 0 1-1l3-3v-1l1-1 1-1 1-1c0-2-1-1 1-2 0-1 0-1 1-2h-2-2l-11 10h-1l5-5c1-2 2-3 3-4 2-2 3-3 6-3l-2-2h0l-1-1c1-3 0-7 0-10l1-2c0 4-1 11 1 14 1 1 2 1 4 1 1-1 2-1 2-2 3 1 6 5 7 7l1 2v4z" class="R"></path><path d="M463 469h1c3 5 7 11 7 17 0 1 0 10-1 11 0-1-1-1 0-2v-2-1-4-3c0 1-1 1-1 2-1 0-1-1-2-2h0c0 1 0 1 1 2l-1 2-1-1h0v2c0 1 0 2 1 2v1 1 2h-1l-2-8c1-1 1-3 1-4h-1l1-2v-1h0c0-1 0-2 1-3 0-1-1-2-1-2l1-1c0-2-1-3-2-5h0-1l-1 2h-2l3-3z" class="C"></path><path d="M344 515l1 1h0v5 19c-2 2-1 11-1 15l-2-4c-1-1-1-2-1-4l1-9v-4c1-4 1-8 1-12 0-2 0-5 1-7z" class="O"></path><path d="M622 302l10 21v-2h1c1 2 1 3 1 5 0 1 1 2 1 2v1l-1 1h1v2c0 2 0 2 1 3l1 6h0l-1-1c0-1 0-2-1-3 0-1-1-2-1-3h0c0 1 0 1-1 2l-1-3c1-2 0-3-1-5l-10-24 1-2z" class="D"></path><path d="M275 643l14-1h1c2 1 3 1 5 3l-7 1h-2-1c-7 1-13 0-19 4 0-1 1-2 2-3h1c-1-1 0-1-1-2h0c2 0 4-1 7-2z" class="Y"></path><path d="M403 270l3 1c0 3-3 8-4 10l-8 17h0v-1l1-1c0-1 0-1 1-2l-2-2v-1c1-1 2-4 2-5 1-3 3-6 4-8 0-3 1-6 3-8z" class="J"></path><path d="M447 360l1 1h0l1-1c0-1 1-1 1-2 1 0 1 1 2 1l3 3c1 1 2 2 3 2l1 3c-1-1-2-2-4-3v1l1 1-1 1 1 1h0c0 1 1 2 1 2 1 2 1 2 1 4-1-2-2-3-3-5-1-1 0-1-1-2l-1 1h0v-1c-3 4-3 6-3 11v-6c-1-1-1-1-1-2l-1-1c-1-3-1-6-1-9z" class="Q"></path><path d="M448 369c1-1 1-5 2-7h1c0 1 0 3 1 4l1 1c-3 4-3 6-3 11v-6c-1-1-1-1-1-2l-1-1z" class="B"></path><path d="M786 373c2 0 5-2 7-3 1-1 3-2 4-3 0 1 0 3 1 4l-7 6-1 1c0-2 1-3 2-5-5 3-9 6-13 9-2 1-3 2-4 3l-1 1h-1v-1c2-3 7-6 9-8 1-2 3-3 4-4zm-324 35l-1-1v-1c4 0 6 2 8 4 1 1 0 0 0 2h1c1 1 1 2 1 3h1v-1c-1 0 0-1 0-1 2 0 2 1 4 1v1c2 0 3 1 4 2l1 1 1 1c0 1 0 0 1 1l1 1c0 1 0 1 1 2 0 4 0 6-2 10 0-3 0-6-1-9-1-2-1-6-4-7-1 0-1-1-2-1-1-1-2-1-3-2l1 4 1 4h0-1l-2-4c-1-2-4-4-6-6l1-1c-2-1-1-1-4-1 0-1-1-2-1-2z" class="O"></path><path d="M427 269l1-1c-1-1-2-2-2-4h0c-1 0-2-1-2-1l-1-1h-1l-3-3-1-1h-1l-1-1h-1v-1h-1c-2-2 0-1-1-1-1-1-1 0-1-1 2 0 3 2 5 3 5 3 10 6 13 10l-1 1c4 4 8 8 10 13v2c1 2 2 4 2 6-4-5-6-11-11-15l-3-5z" class="K"></path><path d="M501 386l1-1v-6c2 1 1 8 2 11v1 21c0 4 1 8 0 12-1-2-3-6-3-8h1c0-9 0-19-1-27v-3z" class="L"></path><defs><linearGradient id="V" x1="288.235" y1="525.536" x2="282.265" y2="511.964" xlink:href="#B"><stop offset="0" stop-color="#605f5f"></stop><stop offset="1" stop-color="#7b7b79"></stop></linearGradient></defs><path fill="url(#V)" d="M274 513l18 4v1l6 6c-1 1-1 1-2 1l1 1-3 1c-4-3-7-8-12-10-2 0-4-1-6-2l-2-2z"></path><path d="M555 804c3-2 4-6 6-9v1l-6 17c-3 8-4 17-8 24 2-11 4-22 8-33z" class="Y"></path><defs><linearGradient id="W" x1="255.395" y1="502.457" x2="236.105" y2="508.043" xlink:href="#B"><stop offset="0" stop-color="#4e4c4c"></stop><stop offset="1" stop-color="#7d7d7c"></stop></linearGradient></defs><path fill="url(#W)" d="M235 501c2 2 5 1 8 2h4l10 4h2 1l1 1c-6 3-13 0-19-2-3-1-6-1-9-1l1-1-1-3h2z"></path><path d="M661 598l1 1c-2 2-3 3-4 5l4-1-6 7c-1 2-3 5-4 6h-1l-5 4h0l2-4v-1l2-2 1-1c1-2 2-3 3-4l2-2c1-1 1-1 1-2l1-1h0l-2 1h0c-1 0-1 0-2 1l-5 5c-1 2-3 3-4 4 0 1 0 1-1 2 0 1-1 1-2 2 5-7 11-13 16-20v1l-1 2h0l4-3z" class="I"></path><path d="M658 604l4-1-6 7c-1 2-3 5-4 6h-1l-5 4h0l2-4h1c2-4 7-8 9-12z" class="S"></path><path d="M746 537c2-1 3-2 6-3h0c8-2 17-3 24 1 1 1 1 2 2 3v1h-2 0c-2 0-6-2-8-2-7-2-15-1-22 0z" class="R"></path><path d="M450 130c1 1 2 2 4 3h1v1l2-2c3 3 4 3 8 3h1 6c0 1-1 2-2 3 0 1 0 1 1 3 0 3-3 4-3 7l-1 1c-1 1-2 3-3 3h-1c0-3 2-6 4-9 0 0 0-1 1-2v-1l-1-1h1v-2h0l1-1c-2-1-5 0-8-1-3 0-8 2-12 0-1 0-1-1-2-1 1-1 2-3 3-4z" class="U"></path><path d="M708 475l3-8 6 13-1 1-1-1c0 1-1 2-1 3v1 3c-1-1-1-1-1-2v-1h-2l-2 2c-1-4-1-7-1-11z" class="Z"></path><path d="M709 486c-1-4-1-7-1-11 1 2 1 4 3 7h1v-2l1 4h-2l-2 2z" class="Q"></path><defs><linearGradient id="X" x1="380.215" y1="546.542" x2="369.5" y2="549.688" xlink:href="#B"><stop offset="0" stop-color="#1c1d1d"></stop><stop offset="1" stop-color="#3d3b3a"></stop></linearGradient></defs><path fill="url(#X)" d="M371 558c0-4 2-9 3-12 1-2 3-7 4-8 2 3 0 9 0 12l-1 7v2c-1-1-2-1-3-1-1 1-1 0-2 0h-1z"></path><path d="M372 558c2-1 3-2 4-4 0 0 0-1 1-1v4 2c-1-1-2-1-3-1-1 1-1 0-2 0z" class="C"></path><path d="M538 504l2-1 1 1c0 9 2 17 4 26v4h-1v3c-4-10-6-22-6-33z" class="G"></path><path d="M442 753h1l8 13v1l2 2c0 1 1 2 2 2v-1c2 3 4 7 5 10l-1 1-1-1-1 1-2-2-1 1h0l-1-1c-1-2-2-3-3-4l-1-5 3 3h0l-10-20z" class="I"></path><path d="M449 770l3 3h0c2 2 2 4 3 6l-1 1h0l-1-1c-1-2-2-3-3-4l-1-5z" class="C"></path><path d="M451 766v1l2 2c0 1 1 2 2 2v-1c2 3 4 7 5 10l-1 1-1-1c0-1-1-2-1-3-2-4-5-7-6-11z" class="D"></path><path d="M563 517c-5-6-7-12-8-20 0-2-1-10 1-12v4c0 3 0 7 1 10l1 3h1s1 1 1 2c1 2 2 5 3 7 2 2 4 4 5 6l-1 2-2-2h-2z" class="B"></path><path d="M559 502s1 1 1 2c1 2 2 5 3 7 2 2 4 4 5 6l-1 2-2-2c-2-3-4-7-5-10l-1-1-1-4h1z" class="e"></path><defs><linearGradient id="Y" x1="460.62" y1="219.893" x2="464.673" y2="226.729" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#484848"></stop></linearGradient></defs><path fill="url(#Y)" d="M460 232l-3-3 9-14c2 3 4 6 5 9h-1-1c-3 3-6 5-9 8z"></path><path d="M782 533c2 2 4 4 5 7v1l1 2h1v3c-1 5-2 10-4 14l-1 1c-1-1 0-3 0-4v-11c0-3-1-7-2-11v-2z" class="E"></path><path d="M786 546l1 1c0 1 0 3-1 5h0c-1-2 0-4 0-6h0z" class="F"></path><path d="M782 533c2 2 4 4 5 7v1c-1 1-1 2-1 5-1-1-3-9-4-11v-2z" class="B"></path><path d="M371 558c1 7-3 13-1 20v3c0 1-1 3-2 3h-1c0-1-1-3-1-5h-1 0l-1-1c1-4 1-8 2-12 1-2 3-5 5-8z" class="W"></path><path d="M363 608c3 2 6 5 9 8l8 7 2 2h1 2 1c2 1 5 5 7 6l-6-1c-1 0-3 0-4-1-3-2-6-5-9-8l-11-11v-2z" class="S"></path><path d="M649 598v2h0l-4 3c-3 2-6 5-8 7-1 2-3 5-4 6-3 5-3 6-2 12v1h0l-1 2h0-1v1c-1-2-1-9 0-10v-3l3-6c1-1 2-1 2-2 1-2 3-3 4-5v-1c-1 1-2 2-3 2 0 2-1 2-2 3h0c0-1 1-2 2-3 4-4 9-6 14-9z" class="b"></path><path d="M631 629h-1-1v-8c1-2 1-4 3-6l1 1c-3 5-3 6-2 12v1z" class="I"></path><path d="M377 565c1 7 3 13 6 19 1 3 3 6 5 8l-2 2h-1c-4-2-7-12-9-16l-1-6c0-2 1-5 2-7z" class="Q"></path><path d="M441 348c-1-1-3-2-3-3-1 0 0-2 0-3 0-3 0-6-1-9v-1c-1-9-5-14-11-21 1-1 1-1 3-1 3 4 5 8 8 13v1c1 2 2 5 2 7 1 5 0 12 3 16l-1 1z" class="W"></path><defs><linearGradient id="Z" x1="380.281" y1="624.554" x2="371.111" y2="608.508" xlink:href="#B"><stop offset="0" stop-color="#72706f"></stop><stop offset="1" stop-color="#9a9a99"></stop></linearGradient></defs><path fill="url(#Z)" d="M358 603h1 1l5 4v1l2 2 2-1-1-1c-1-1-2-2-2-4l20 21h-1-2-1l-2-2-8-7c-3-3-6-6-9-8l-5-5z"></path><path d="M471 368c1 1 3 3 4 5l1-1c0-1 0-1-1-1 0-1-1-2-2-3-1-3-3-5-3-8l10 17 4 7 3 7c-2-1-4-7-5-9v-1c-1-1-1-1-1-2-1-1-2-2-2-3l-1-1v2c0 1 1 2 2 3h0v1h-1l2 3h0l2 3c1 1 1 2 1 3v1l-1-2-6-4v-5c-1-1-1-1-1-3l-5-9z" class="O"></path><path d="M476 377c2 4 5 8 7 12h0l-6-4v-5c-1-1-1-1-1-3z" class="F"></path><path d="M707 400l-10 36v-10l3-14c1-2 1-6 2-8 1-1 4-3 5-4z" class="b"></path><path d="M556 517c1 2 3 5 5 7h1l9 8 3 2h-1-2-2c1 1 1 2 2 3h-2l-3-2c-1-1-2-1-3-2-1 0-1 0-1-1h1c-1-1-1-2-2-3l-4-5c-1-1-2-3-2-4-1-1 0-2 1-3z" class="E"></path><path d="M557 524c3 1 5 5 8 7 1 1 2-1 3 0s2 1 3 1l3 2h-1-2-2c1 1 1 2 2 3h-2l-3-2c-1-1-2-1-3-2-1 0-1 0-1-1h1c-1-1-1-2-2-3l-4-5z" class="O"></path><defs><linearGradient id="a" x1="795.482" y1="405.405" x2="800.781" y2="393.802" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#626261"></stop></linearGradient></defs><path fill="url(#a)" d="M794 398l1 1v-1c1 0 2 0 3-1h0 1c2 0 3-1 5-2l7-5v2l-18 15-4 2c0-1 1-2 2-2 0-1 1-1 1-2-1-1 0-2-2-2-1 1-2 2-3 2h-1l8-7z"></path><path d="M630 365l-1-3c-2-4-8-16-6-21l2-5 5 17v5c0 3 2 5 0 7z" class="C"></path><path d="M315 480c3-1 6-11 7-14l2 7c0 2-1 4-1 6 0 3-1 5-3 7l-3 4c-1-2 0-5-1-7-1-1-1-1-1-3z" class="M"></path><path d="M628 164h76l-1 1c-2 1-5 0-7 0l-14 1h-50c-1-1-3-1-4-2z" class="T"></path><path d="M222 272c2 3 2 7 3 11h2c2 6 6 12 11 15 4 2 11 4 15 2h1c0 1-1 2-2 3 0 1-1 2-1 3s0 1-1 2c-1-2-2-3-3-5-2-1-5-2-7-3-3-1-5-2-7-4 0 0-2-2-3-2l-3-3c0-2-1-4-2-6h0l-1-1h0c-2-4-2-8-2-12zm161 400l5 1c2 1 3 1 5 2-2 1-4 0-5 0l-1 1c-1 0-2 0-3-1h-3v2l-1 1h-1c-2 0-3 2-4 3l-7 2v-1c1-1 3-1 4-2l1-2c1-1 1 0 1-1l-4-1v-1l-5-1h4 0c2-1 4-1 6-1 3-1 6 0 8-1z" class="H"></path><path d="M388 673c2 1 3 1 5 2-2 1-4 0-5 0l-1 1c-1 0-2 0-3-1h-3v2h-1c-2-1-4-1-5-2 1-1 3-1 5-1h0c2-1 4-1 6 0 1 0 1 0 2-1z" class="Y"></path><path d="M494 866l19 23-1 7-7-9v-2l-1-1c0-1 1 0 0-1l-2-2v-1l-1-1-5-8c-2-2-2-3-2-5z" class="K"></path><defs><linearGradient id="b" x1="660.694" y1="670.5" x2="670.935" y2="679.491" xlink:href="#B"><stop offset="0" stop-color="#b2b1b1"></stop><stop offset="1" stop-color="#dad9d7"></stop></linearGradient></defs><path fill="url(#b)" d="M670 671h8l-2 2h1c2 0 3 0 5 1h-6l-1 1c1 1 2 1 3 1l-1 2c-2-1-5-1-7-1-3-1-12 0-14-1 1-1 1-2 2-3v-1l12-1z"></path><path d="M670 671h8l-2 2h-1-5l-1-1 1-1z" class="U"></path><path d="M179 316c0 2 0 3 2 4 1 3 2 6 4 8h1l12 14h0-2 0c1 1 1 1 1 2l1 1-1 2-1-1c0-1-2-3-3-4l-8-9c-3-4-6-9-8-14 1-1 1-2 2-3z" class="D"></path><path d="M186 328l12 14h0-2 0c1 1 1 1 1 2-5-3-9-11-12-16h1z" class="T"></path><path d="M760 638c8-2 15-5 21-11 1-2 2-4 4-5h1v5h-3c0 1 1 2 1 3l1 2-1 2c-1 0-1-1-1-1 0-2-1-1-1-2-2 0-2 1-2 2-2 1-3 2-5 4-1 1-3 2-5 2-1-1-3-1-5-1h-1c-2 0-3 1-4 0z" class="W"></path><path d="M783 627c0 1 1 2 1 3l1 2-1 2c-1 0-1-1-1-1 0-2-1-1-1-2-2 0-2 1-2 2-2 1-3 2-5 4-1 1-3 2-5 2-1-1-3-1-5-1l6-2c4-1 6-4 9-6l3-3z" class="Z"></path><path d="M178 108c1 0 2 1 3 2v1c-1 1-2 1-3 1-2 0-4 0-5 2-2 2-3 4-3 7 0 4 3 6 5 10-1 1-1 1-2 0-1 0-2-1-3-2h-1c0-1-1-2-2-3v1c-2-2-1-4-1-6 0-1 0-1 1-2 1-3 2-5 4-7l1-1-1-1c1-1 2-1 4-1 1 0 2 0 2-1h1z" class="O"></path><defs><linearGradient id="c" x1="414.53" y1="674.039" x2="396.724" y2="671.871" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#c)" d="M395 659c4 3 10 9 13 14l10 14h0c-1-1-1-1-2-1v1c-2-3-4-5-6-7l-14-14 1-1c0-1 0-2-1-3 0-1-1-1-1-2v-1z"></path><path d="M570 797h1s1 0 1-1c2-2 4-4 5-7 3-3 6-9 10-12-1 4-6 7-6 12l-17 24c-1 2-2 5-4 6 3-7 6-14 10-22z" class="R"></path><path d="M557 796v1l-8 20v-1h0l-1 2h0c0-5 3-10 5-15 5-14 8-28 15-40 1 2 0 5 0 8 0 1 0 1-1 2s-2 1-3 3c-1 1-2 3-1 5l-1 1-2 4s0 1 1 1l-2 4v3l-2 2z" class="E"></path><path d="M591 388c1-2 0-4 0-6 1-2 2-4 2-6l16 21-1 1c-1-1-1-2-2-3-2 0-4-3-4-5v-1l-1 1-3-4 1-1h-1v-1l-1-1h0l-1-1v-1h0l-1-2-1-1c-1 0-1 1-1 2l-1 1v1h2l1 1c1 1 2 2 2 3l1 1v1l2 2v1l4 4 1 1c1 1 0 0 0 1l2 3 1 1h-1c-1-1-2-2-2-3l-2-2c0-1 0 0-1-1 0-1-1-2-2-2v-2l-2-1h-1l-2-2v-1c-1-1-1-1-1-2l1-1c-1 0-1-1-1-1-1 0-1 1-2 2l1 2v1c0 1 2 2 2 4 0 0-1 0 0 1l1 1c1 2 2 3 3 5 2 2 4 4 5 6l-2-1c0-1-3-4-4-5l-7-11z" class="O"></path><defs><linearGradient id="d" x1="293.971" y1="482.542" x2="301.278" y2="464.854" xlink:href="#B"><stop offset="0" stop-color="#858585"></stop><stop offset="1" stop-color="#b0adac"></stop></linearGradient></defs><path fill="url(#d)" d="M300 457l2-4h1l2 4c1 2 0 3 0 5-3 0-4 2-5 4-1 1-1 2-1 4-1 3-1 6-1 10v1c0 2 1 4 0 6l-2-5-1-5c0-3 1-7 1-11 2-2 3-4 3-5l1-4z"></path><path d="M300 457l2-4h1l2 4c-1 1 0 1-2 2-1 0-2 1-3 2h-1l1-4z" class="a"></path><path d="M485 423s0-3-1-3c0-1-1-1-1-3h-1l-3-3h1l2 2h1l-1-1c-1-1 0 0-1-2l-3-3v-1c3 2 5 5 7 8 1 1 2 3 2 4 1 1 1 2 1 2v1 1h-1c-1 1-1 2-1 3v4c-2 1-2 2-3 4v2l-1 1c0 2 0 6 1 7v2 1l1 1v1l1 1h0v1c1 1 2 3 2 4l1 1s0 1 1 2h0c1 1 1 2 1 2l1 1c0 1 1 2 1 4 1 1 1 1 1 2s0 2 1 3v1h0v-4h1v1h0c0 2 0 2 1 4h0v3h-1c-1 0-1 0-2-1 1-8-5-15-8-22-2-3-3-6-4-10v-5l2-6c2-4 2-6 2-10z" class="K"></path><path d="M462 408s1 1 1 2c3 6 5 13 6 20 0 5-1 10-1 15 1 2 0 2 2 4 1 0 2-1 4-1 2 1 3 3 5 5l2 4v2l-1-2c-1-2-4-6-7-7 0 1-1 1-2 2-2 0-3 0-4-1-2-3-1-10-1-14 2-11-1-19-4-29z" class="H"></path><path d="M348 706l1-2c3-8 10-15 18-18-1 3-2 5-5 7h0c-2 0-5 3-6 6-3 4-4 9-5 14h0c-1-3-1-3 0-5v-1c0-1 1-2 1-2 1-2 1-3 2-4v-1l1-1c1-3 5-6 7-8h1l1-1h1s0-1 1-2c-3 0-8 4-10 7l-4 4v1c-1 1-1 2-2 3v1 1h-1c0 1-1 2 0 3l1 1c0 3-2 6 0 9s4 7 3 10c-1-2-2-3-3-5-3-6-4-11-2-17z" class="b"></path><defs><linearGradient id="e" x1="559.48" y1="547.44" x2="546.345" y2="537.798" xlink:href="#B"><stop offset="0" stop-color="#3b3c3c"></stop><stop offset="1" stop-color="#565353"></stop></linearGradient></defs><path fill="url(#e)" d="M549 530c1 3 2 5 4 8l4 5v1l-1-1-1 1c1 1 1 2 2 3v2l1 1c0 1 0-1 0 1l1 2s1 1 1 2c0 0 0 1 1 2h-1c-1-1-1-3-2-4l-1-1-1 1 2 2c0 1 1 2 1 3h0l-2-1h0c1 1 1 1 1 2l3 6 2 3v1 1c1 1 1 1 0 1v1h-1v-1c1-3-5-12-6-15v-1c0-1-1-2-1-4v-2c-4-6-6-12-7-18h1v-1z"></path><path d="M553 538l4 5v1l-1-1-1 1h0s0-1-1-2c-1-2-1-3-1-4z" class="C"></path><path d="M754 579l1-1c1 2 2 3 4 4v-1-3c4 5 11 8 17 10h1l1 1c-2 1-4 1-6 1l-6-1-9-3c-1-2-2-5-3-7z" class="W"></path><defs><linearGradient id="f" x1="580.625" y1="253.066" x2="582.369" y2="262.903" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#5e5e5d"></stop></linearGradient></defs><path fill="url(#f)" d="M575 261c4-10 13-16 21-21h0c0 1-1 1-1 2-1 0-2 1-2 1l-1 1h-1c-1 1-2 2-3 2l-2 2-1 1c-1 1-3 2-3 4h3l1-1 2 2c-4 4-7 9-8 14-1-1-1-2-1-3-2-1-3-1-5-1 1-1 1-2 1-3z"></path><path d="M566 535l3 2c1 1 1 2 1 3 0 2-1 4-1 6 0 1-1 1-1 2v1c0 1 0 1-1 2v1 1c-1 1-1 2-1 3v2h-1v-1c0-2 1-3 0-4v1 1c-1 2 0 4 0 6v8h0l-1-1v-1-7-5c0-1-1-2-1-4v-5c0-1 0-2 1-3 1-2 1-5 2-8z" class="I"></path><path d="M567 536c0 1 1 8 0 10-1 0-1 0-2 1v5l-1-1c0-3 0-5 1-8l2-7z" class="L"></path><defs><linearGradient id="g" x1="363.979" y1="698.886" x2="350.707" y2="718.4" xlink:href="#B"><stop offset="0" stop-color="#4f4e4e"></stop><stop offset="1" stop-color="#706f6e"></stop></linearGradient></defs><path fill="url(#g)" d="M351 713c1-5 2-10 5-14 1-3 4-6 6-6-2 3-3 6-4 10l-1 1c0 1-1 3-1 4l-1 1c-2 5 2 12 3 17-3-4-6-7-7-13h0z"></path><defs><linearGradient id="h" x1="499.653" y1="862.045" x2="480.33" y2="838.727" xlink:href="#B"><stop offset="0" stop-color="#313131"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#h)" d="M482 836c7 10 15 21 21 32l3 6-29-37h1l2 2s1 0 2-1v-2z"></path><path d="M746 537c7-1 15-2 22 0h-5 0-1c-2 1-5 1-7 2v1c-6 1-11 3-17 4-2 1-5 2-7 3 5-4 9-8 15-10z" class="E"></path><path d="M738 544v-1c2-3 14-3 17-4v1c-6 1-11 3-17 4z" class="P"></path><defs><linearGradient id="i" x1="501.027" y1="533.759" x2="517.702" y2="508.392" xlink:href="#B"><stop offset="0" stop-color="#bfc5c0"></stop><stop offset="1" stop-color="#ece4e6"></stop></linearGradient></defs><path fill="url(#i)" d="M507 505l1-1c1 0 1 0 2-1v1s0 1 1 1c1 7 1 14 1 20v8l-1 1v4l-2 1v-3c-1-10 0-22-2-31z"></path><path d="M592 399c-2-3-3-6-4-9 1-2 1-3 2-4l1 2 7 11c1 1 4 4 4 5v3h1v1l1 1c-1 0-1 0-1-1-1 0-2-1-3-1h0l-4-4c-1-1-2-1-3-2l-1 1h0l-3-3v-1c1-1 2 0 3 1z" class="Y"></path><path d="M589 399v-1c1-1 2 0 3 1 2 1 3 2 4 4-1-1-2-1-3-2l-1 1h0l-3-3z" class="R"></path><path d="M229 471c-6-5-13-11-16-18v-2c3 0 7 5 9 7 3 4 7 8 11 12-1 0-1 0-2 1 1 1 1 1 1 2l-3-2z" class="M"></path><defs><linearGradient id="j" x1="581.639" y1="431.408" x2="576.484" y2="439.163" xlink:href="#B"><stop offset="0" stop-color="#b4b3b3"></stop><stop offset="1" stop-color="#dad9d8"></stop></linearGradient></defs><path fill="url(#j)" d="M571 428v-3l2-2c2 8 5 13 12 17 3 2 6 4 8 5h0c-4 2-11-1-14-3-4-4-6-8-8-14z"></path><path d="M299 659c3-3 6-6 10-7 2 0 5-1 6 0-7 3-13 7-17 15-2 4-3 11-3 16l-1 1-1-10c1-2 0-4 1-7s3-6 5-8z" class="d"></path><path d="M339 624c3 0 7 2 10 3 1 0 1 1 2 1h-10-16c-1 1-3 1-4 2-4 0-6 1-9 3-2 1-4 1-7 1h0c3-1 3-1 6-3l4-2c4-2 10-3 15-4 3-1 7 0 9-1z" class="O"></path><defs><linearGradient id="k" x1="481.135" y1="497.538" x2="468.865" y2="484.962" xlink:href="#B"><stop offset="0" stop-color="#464444"></stop><stop offset="1" stop-color="#5d5d5e"></stop></linearGradient></defs><path fill="url(#k)" d="M474 481c1-1 1-1 1-2 1-1 1-2 2-3v6 14c0 4 0 12-3 16-1 2-3 4-5 5 1-4 3-7 4-11 2-8 1-17 1-25z"></path><path d="M601 356c-1-1-1-2 0-3 2-4 5-2 7-4 1-1 2-1 3-1h1c1 0 2 0 3-1h1 0 1c1-1 1-1 2-1 0 2 0 3-1 4l1 1v1c1 1 1 1 2 1v-4h1v2h1v-3 3c0 1 1 2 1 2 1 2 2 6 2 7h-1v2c-2-2-3-5-5-6l-2 1c0-3 0-4-1-7h0-1-2c-1 0-2 0-3 1h-1c-1 0-2 0-3 1-1 2-1 3-2 5 0-1-1-1-1-2h-2l-1 1z" class="K"></path><path d="M745 517c10-4 20-5 31-4l-2 2c-3 1-5-1-8 1-5 0-9 1-14 3-1 1-3 2-5 2h-1c-2 0-3 1-5 2-1 1-1 1-2 1v-1c2-1 4-3 7-4l-1-1-1-1h1z" class="Q"></path><path d="M333 586c2 0 4 1 6 2l6 6c1 0 2 1 2 2 3 4 7 8 8 12v1 1h-1l-10-13c-2-1-4-4-5-5-2-1-4-2-6-2-1-1-1-1-2-1h-5-1l4-2c1 0 2 0 4-1z" class="K"></path><path d="M313 485c1-2 2-2 1-5h1c0 2 0 2 1 3 1 2 0 5 1 7l-3 6v4 2l-1 2h0l-4-5-1-3c-1-3 0-4 1-6l4-5z" class="Y"></path><path d="M309 490l4-5c0 3 1 6 1 8v3 4c-3-3-1-5-2-8-1-1-2-2-3-2z" class="H"></path><path d="M313 485c1-2 2-2 1-5h1c0 2 0 2 1 3 1 2 0 5 1 7l-3 6v-3c0-2-1-5-1-8z" class="G"></path><path d="M798 477l8-5 1 1c1 0 3 1 4 2 5 0 11-2 15-3-1 2-3 4-5 6h0c-2-1-3-1-4 0h-1v1c-5-1-8-1-12 0l-1 1h-1c-1-1-3-1-4-1-1-1 0-1-1-1h-1l2-1z" class="V"></path><path d="M437 324l2 1c3 3 5 6 6 10 1 1 1 0 1 1v6h0l-1 8-1 1-3-3 1-1c-3-4-2-11-3-16 0-2-1-5-2-7z" class="F"></path><path d="M443 339c1 1 2 1 3 3h0l-1 8-1 1-3-3 1-1 2 2v-1c-1-3-1-6-1-9h0z" class="G"></path><path d="M439 325c3 3 5 6 6 10 1 1 1 0 1 1v6c-1-2-2-2-3-3 0-4-1-8-3-11-1-1-1-2-1-3z" class="U"></path><path d="M584 363c2 1 3 2 3 4 1 2 1 6 2 8-2 2-4 4-4 7v1 1h-1c-1 2-2 3-4 3h0l3-2-1-1v-2l-2-1c2-3 1-8 1-11 0-2 0-2 1-3l2 1c0-1 0-2-1-3h0l1-2z" class="R"></path><path d="M581 370c0-2 0-2 1-3l2 1c0 4 2 13-1 16v1l-1-1v-2l-2-1c2-3 1-8 1-11z" class="a"></path><defs><linearGradient id="l" x1="798.472" y1="394.427" x2="809.752" y2="390.167" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#939191"></stop></linearGradient></defs><path fill="url(#l)" d="M812 380h1v2s1 0 1 1l-1 1c0 1 1 1 1 1l-1 2c1 0 2-2 3-2l3-1c-3 2-5 4-8 6l-7 5c-2 1-3 2-5 2h-1 0c-1 1-2 1-3 1v1l-1-1c5-6 13-11 18-18z"></path><path d="M588 405v-3-3h0 1l3 3c2 4 5 8 8 11 1 1 6 5 6 6h-1l-4-4h-1c-1 1-1 1-2 1l-2 2h1c1 1-1 0 0 1 2 2 6 5 6 7-3-3-7-7-9-10-3-4-6-7-6-11zm-1-54c1-1 2-2 2-3h0c-1-2-1-3-1-5-1-3-1-6-1-9 1-4 3-9 4-12 1 1 1 2 3 3h-1l-1 1v3c0 2 0 6 1 8 0 5-2 12-3 17 0 1-1 1-1 1l-2-4z" class="C"></path><path d="M569 506c-1-2-2-3-3-5-2-3-3-6-5-9-2-4-1-10-1-14l1-1c0-2 0-2 1-3h0c0 2 0 3-1 4s-1 3-1 4l5 11 1 1h0c0 1 1 2 2 3h1l2 3c2 3 5 7 6 11h0-1c-1 0-1 0-2-1-2-1-3-3-5-4z" class="K"></path><path d="M622 302c-1-2-3-5-4-8s-4-9-2-12l16 33c-2 0-3-4-5-6 0 1 0 1 1 2 1 3 2 8 4 10v2l-10-21z" class="E"></path><path d="M502 217c-1-1-2-2-4-3v-1h2 34v1c-1 1-2 2-4 2l-28 1z" class="V"></path><path d="M864 299c0 1 0 2-1 4v1h1 0c0-2 1-3 1-4h0c1 2 0 3-1 5v1 1 1l-1 1v1l-1 2c-1 1 0 1-1 2h0v1c-1 1-1 1-1 2l-1 1c-1 2-4 10-6 10v1c-1 0-1 0-2 1l-3 3c0-1 1-2 1-3 2-1 4-7 5-8 1-4 4-9 5-13 0-2 1-3 2-5 0-2 1-3 2-5h0 1z" class="C"></path><path d="M854 322v1c2-1 4-8 6-11v1 3 1l-1 1c-1 2-4 10-6 10v1c-1 0-1 0-2 1l-3 3c0-1 1-2 1-3 2-1 4-7 5-8z" class="J"></path><defs><linearGradient id="m" x1="844.363" y1="310.602" x2="831.43" y2="304.297" xlink:href="#B"><stop offset="0" stop-color="#474746"></stop><stop offset="1" stop-color="#696768"></stop></linearGradient></defs><path fill="url(#m)" d="M834 299c0-1 0-1 1-2h2c2 3 3 7 4 10 1 2 1 5 2 7 0 2-1 4-1 7h2l-2 5-1-1v-6c-1-1-1-3-2-4l-2 4c0-5 0-10-2-14 0-3 0-4-1-6z"></path><path d="M594 249c2 2 2 2 2 5-2 2-3 3-4 5-1 1-2 1-2 0l-1-1-1 1v5 2h2l-5 5-1-1-2 2v-1c-1-1-1-1-2-1v-1c1-1 3-6 4-8 2-5 6-8 10-12z" class="E"></path><path d="M584 261l2-2v2h0l1-1h0v6h0 1 2l-5 5-1-1-2 2v-1c-1-1-1-1-2-1v-1c1-1 3-6 4-8z" class="B"></path><defs><linearGradient id="n" x1="563.919" y1="553.385" x2="540.598" y2="549.025" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#929190"></stop></linearGradient></defs><path fill="url(#n)" d="M545 530c3 9 6 18 11 26 1 3 7 12 6 15v1c-8-10-14-23-18-35v-3h1v-4z"></path><path d="M678 539c2 0 3 0 5-1 1 0 1-1 2-1l1-1c5-3 9-7 14-10-1 2-1 3-2 5-2 4-8 7-12 9-2 2-4 3-7 4-2 1-3 2-4 4l-1 3v-4-7h0v-1l2-1-1 2h2c1-1 0-1 1-1z" class="I"></path><path d="M714 570c2-1 4-2 6-2 1 0 2 1 4 2v1c1 0 3 0 4 1l1 1c-1 0-4 1-4 1l-8 3c-4 0-8 3-11 1h-1v1l-2-1 2-1c1-1 2-1 3-1 2-1 2-2 3-4h0v-1l3-1h0z" class="B"></path><g class="c"><path d="M724 571c1 0 3 0 4 1l1 1c-1 0-4 1-4 1-2-1-2-1-3-1l-1-1c2 0 2 0 3-1z"></path><path d="M714 570c2-1 4-2 6-2 1 0 2 1 4 2v1h-4c-1-1-1-1-2-1h-4z"></path></g><path d="M714 570v1h4c0 1 0 1-1 1l-2 2c-2 2-4 3-6 3-1 0-2 0-3 1h-1v1l-2-1 2-1c1-1 2-1 3-1 2-1 2-2 3-4h0v-1l3-1z" class="e"></path><path d="M541 577c0-3-1-5 1-7 1 6 3 12 3 18 2 3 1 9 1 12v1c0 1 0 6-1 8v4l-1 1v-1c0 1 0 2-1 3 0 2 1 3-1 5 0-1 0-1 1-2v-5h-1v3l-1-3c1-1 1-3 1-4 2-11 1-23-1-33z" class="I"></path><path d="M566 178c-1-3 2-6 4-9l17 1-4 1c-1 1-1 2-1 3s-1 2-1 4h-10-5z" class="D"></path><path d="M254 638c1 1 3 1 4 3 5 2 9 2 14 1l3 1c-3 1-5 2-7 2h0c1 1 0 1 1 2h-1c-1 1-2 2-2 3l-1 2c0 1-1 2-1 3-2 0-4-3-6-5 1-1 0-3-1-5s-2-5-3-7z" class="e"></path><path d="M254 638c1 1 3 1 4 3 0 1 0 3 1 4 2 1 3 3 4 3h3c-1 0-2 1-2 2l-1 1c-2-1-4-4-6-6-1-2-2-5-3-7z" class="D"></path><path d="M258 641c5 2 9 2 14 1l3 1c-3 1-5 2-7 2s-3 1-4 2c1 0 2-1 3 0l-1 1h-3c-1 0-2-2-4-3-1-1-1-3-1-4z" class="C"></path><path d="M458 374c0-2 0-2-1-4 0 0-1-1-1-2h0l-1-1 1-1-1-1v-1c2 1 3 2 4 3l2 2c0 1 1 2 2 2 1 1 0 0 0 2h1c2 2 3 5 6 6h1c0 1 2 1 3 1 0-2-1-3-2-4l-1-1c-2-2-3-6-4-9 1 0 1 1 3 2v-1l1 1 5 9c0 2 0 2 1 3v5c-6-4-14-6-19-11z" class="P"></path><path d="M784 501h0c9 0 16-3 23 3v1h-1l-6-1c-5 1-10 3-15 5v-3-1c-1 0-3 0-4-1l-3 2c0-2 4-4 5-5h1z" class="X"></path><path d="M781 504c4-2 12-4 16-3l2 1c1 0 1-1 1 0-5 0-10 1-15 4v-1c-1 0-3 0-4-1z" class="E"></path><defs><linearGradient id="o" x1="785.761" y1="505.604" x2="799.056" y2="503.329" xlink:href="#B"><stop offset="0" stop-color="#898888"></stop><stop offset="1" stop-color="#aaa8a8"></stop></linearGradient></defs><path fill="url(#o)" d="M785 506c5-3 10-4 15-4 1 1 2 1 3 2h-3c-5 1-10 3-15 5v-3z"></path><path d="M729 657c1 0 1 0 2 1h1 1c2 1 3 1 4 1h1l5 2h0c0 4 0 6-1 9l-3 6-2 2c0-8-3-15-8-21z" class="P"></path><path d="M738 659l5 2h0c0 4 0 6-1 9-1-1-1-1-1-2 0-3-1-6-3-9z" class="W"></path><path d="M819 343h2l-1 1c-1 1-2 2-3 4s-2 3-4 4l1 2-9 10-7 7c-1-1-1-3-1-4l3-3-2-1 21-20z" class="a"></path><defs><linearGradient id="p" x1="799.111" y1="366.615" x2="804.974" y2="360.973" xlink:href="#B"><stop offset="0" stop-color="#535252"></stop><stop offset="1" stop-color="#6d6c6b"></stop></linearGradient></defs><path fill="url(#p)" d="M800 364l13-12 1 2-9 10-7 7c-1-1-1-3-1-4l3-3z"></path><defs><linearGradient id="q" x1="743.975" y1="490.595" x2="763.025" y2="479.405" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#747373"></stop></linearGradient></defs><path fill="url(#q)" d="M757 464l1 1c0 2 0 2 1 3v1c0 3-1 7-2 10 1 1 1 1 3 2h0c-3 8-7 15-11 23h-1l3-14c3-8 6-17 6-26z"></path><path d="M757 479c1 1 1 1 3 2h0l-3 2c-1-2 0-3 0-4z" class="B"></path><path d="M568 763h0c5-12 11-25 19-35v3h-1v2c-1 2-2 1-1 4-3 6-6 11-9 16-1 2-3 4-3 6-1 2-2 8-3 9s-2 2-2 3c0-3 1-6 0-8z" class="F"></path><path d="M560 455l-1-1c-1-1-1-2-1-3h1c1 0 2-1 3-1h0l1-2v1c0 2 1 3 3 5v1c1 1 2 2 3 2 1 1 2 1 3 2l1 1 2 2 2 2c-1 1-1 2-2 3h-1c-1-1-1 0-2 0-2 1-1 1-3 0v-1l-1-1c0-1 0 0-1-1-1-2-3-3-4-5l-1-1-1-1h0l-1-2z" class="K"></path><path d="M565 459h3v2s-1 1-2 1l-1-3z" class="O"></path><path d="M562 457l6 1h1l-1 1h-3l-3-2z" class="e"></path><path d="M560 455l1-2c3 1 5 3 7 5l-6-1h-1l-1-2z" class="C"></path><defs><linearGradient id="r" x1="541.555" y1="520.931" x2="548.564" y2="518.12" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#52514f"></stop></linearGradient></defs><path fill="url(#r)" d="M543 502c2 10 2 19 5 29 1 6 3 12 7 18v2c0 2 1 3 1 4v1c-5-8-8-17-11-26-2-9-4-17-4-26l-1-1-2 1c0-1 0-1 1-2h4z"></path><path d="M652 616c1-1 3-4 4-6h2c1 2-1 2 0 4l-5 7 2 1c-1 1-4 5-4 7-2 1-3 1-5 1h0 0v-2-1l-1 1-1-1v-2h-2c1-2 2-3 4-5h0l5-4h1z" class="M"></path><path d="M653 621l2 1c-1 1-4 5-4 7-2 1-3 1-5 1h0 0c0-1 1-1 1-2l6-7z" class="L"></path><path d="M646 620h0l5-4h1c-1 1-2 2-2 4l-2 2c0 1-1 1-1 2 1 0 1 0 2-1h1l-3 4v1c0 1-1 1-1 2v-2-1l-1 1-1-1v-2h-2c1-2 2-3 4-5z" class="C"></path><defs><linearGradient id="s" x1="407.981" y1="342.458" x2="407.58" y2="309.065" xlink:href="#B"><stop offset="0" stop-color="#2b292a"></stop><stop offset="1" stop-color="#474745"></stop></linearGradient></defs><path fill="url(#s)" d="M414 305c1 1 0 5 1 6-3 8-7 17-9 26-1 3-1 6-1 8-1-2-3-6-3-9 1-2 1-4 1-5l1-1v-2l10-23z"></path><defs><linearGradient id="t" x1="156.565" y1="356.458" x2="166.435" y2="357.542" xlink:href="#B"><stop offset="0" stop-color="#3f403f"></stop><stop offset="1" stop-color="#5c5a5b"></stop></linearGradient></defs><path fill="url(#t)" d="M160 359c-6-5-11-12-15-19l-2-5v-1c1 1 1 1 1 2l2 2 3 5c0 1 1 1 1 2s1 1 1 2c1 1 1 2 2 3h1c-1-2-1-2-1-3l3 3c2 2 3 3 5 4h0 2c2 3 5 6 6 8v1c0 1 2 3 3 5l1 1-1 1-3-3h-1c-2-1-4-3-5-5-1 0-2-2-3-3z"></path><path d="M160 359c1-1 0-1 2-1l2 4h0-1c-1 0-2-2-3-3z" class="J"></path><path d="M556 849c1 2 1 4 1 6l-3 6-6 9c-1 2-4 4-5 6h0c-1-1-2-1-3-1-1 1-1 0-2 0l14-20c1-1 2-3 3-4l1-2z" class="P"></path><path d="M556 849c1 2 1 4 1 6l-3 6h-1v-1h1c-1-3 1-3 1-5v-4l1-2z" class="c"></path><defs><linearGradient id="u" x1="301.898" y1="506.003" x2="286.44" y2="493.192" xlink:href="#B"><stop offset="0" stop-color="#2f302f"></stop><stop offset="1" stop-color="#4e4d4d"></stop></linearGradient></defs><path fill="url(#u)" d="M286 481c1 0 1 0 1-1 1 1 1 2 1 4 1 1 1 2 2 2h0c1 2 2 4 2 6l4 10c1 1 2 4 3 5 1 2 3 4 4 7-2-2-5-3-7-5-1-1-2-2-2-3-1-1-1-1-1-2l-1 1 1 1-1 1c-2-1-3-3-4-5v-5l1-1 2 2v-1l-1-1c0-3-1-8-3-10l-1-5z"></path><path d="M288 502v-5l1-1 2 2c-1 3 3 6 5 9v2c-1-1-2-2-2-3-1-1-1-1-1-2l-1 1 1 1-1 1c-2-1-3-3-4-5z" class="L"></path><defs><linearGradient id="v" x1="481.495" y1="218.617" x2="493.722" y2="225.395" xlink:href="#B"><stop offset="0" stop-color="#848282"></stop><stop offset="1" stop-color="#a1a1a1"></stop></linearGradient></defs><path fill="url(#v)" d="M477 205c3 3 6 6 9 10 9 13 13 29 16 45v1c-1-1-1-3-2-4 0-4-1-8-2-12h0l-1-1v-3c-1 0-1 0-1-1-1 0-1-2-1-3l-2-2c0-2 0-2-1-3-1-2-1-3-3-3 0-1 0-2-1-3-1-2-2-5-3-7-2-3-4-5-5-8-1-2-3-3-3-6z"></path><path d="M466 154c2 0 3 1 5 2v-1c0 1 0 2 1 2l1 2c0 1 0 1 1 2 1 3 3 6 5 9h0-2-1c0 2 1 3-1 4h-1c-1 0-4-3-5-4v-2c-1-3-2-5-2-7l-1-2v-5z" class="B"></path><path d="M466 159c0-1 1-2 1-3 1 1 2 1 3 2 0 2 0 3-1 5h0c0-1-1-3-2-3v1l-1-2z" class="S"></path><path d="M469 163c1 4 4 7 7 9-2-3-3-6-5-9v-4h1v1c1 4 3 7 5 10h-1c0 2 1 3-1 4h-1c-1 0-4-3-5-4v-2c-1-3-2-5-2-7v-1c1 0 2 2 2 3z" class="J"></path><path d="M434 741c3 4 5 8 8 12h0l10 20h0l-3-3c-5-5-9-10-14-15-2-2-4-3-6-4 1-1 1-1 2-1 3 1 5 4 7 7h1v-1c-1-2-2-3-2-5-1-1-1-1-1-2l-1-1h1c-1-3-2-5-2-7z" class="S"></path><path d="M555 276c1 1 0 2 0 3-1-3-2-5-3-7-1-9-1-17 0-26 0-2 0-4 1-7 1 1 0 2 0 3v1h1v-1-1c1-1 1-2 1-3h1l-1 3c0 3-1 5 0 8 0 2 1 3 1 6l1 5c-1-1-1-1-1-2h-1c0 5-1 13 0 18z" class="D"></path><defs><linearGradient id="w" x1="814.025" y1="399.777" x2="810.44" y2="386.597" xlink:href="#B"><stop offset="0" stop-color="#b3b0b4"></stop><stop offset="1" stop-color="#d8d9d4"></stop></linearGradient></defs><path fill="url(#w)" d="M820 384l1 1c1-1 3-2 4-3h1c1 1 1 1 1 2h0l-35 27c0-2 1-3 1-4l18-15 9-8z"></path><path d="M391 746c1 0 3 1 4 1 4 1 7 1 11 1l2 1 4 4h-4l-1 1h-1 0c-1 1-1 0-2 1-1 0-2 1-3 1v1h-3c-1 0-7-6-8-7l1-1v-3z" class="d"></path><path d="M396 752c3 1 7 2 10 2h0c-1 1-1 0-2 1-1 0-2 1-3 1-2-1-3-2-5-4z" class="W"></path><path d="M391 749c2 1 3 2 5 3 2 2 3 3 5 4v1h-3c-1 0-7-6-8-7l1-1z" class="Q"></path><defs><linearGradient id="x" x1="663.411" y1="590.104" x2="654.752" y2="576.158" xlink:href="#B"><stop offset="0" stop-color="#2c2d2c"></stop><stop offset="1" stop-color="#4f4d4d"></stop></linearGradient></defs><path fill="url(#x)" d="M669 566c1 1 1 2 2 3-1 4-2 8-4 13-1 1-2 4-3 5l-4 6c-4 3-7 5-11 7h0v-2c1-2 3-3 5-4s3-3 5-5h-1c-1 1-2 1-3 2h-1l5-5c6-5 8-13 10-20z"></path><path d="M344 515c-3-8-2-16-4-24v-8c0 2 0 4 1 6v2c1-1 2-1 3-1v-1-12c1 3 0 7 1 10 0 7 0 15 2 22v1c1 3 2 5 4 7l1 2v1c-1-2-2-4-4-4l-1-2c-1 2-1 2-1 3v1h0c0 1-1 2-1 3v-5h0l-1-1z" class="C"></path><path d="M279 488c-1-2 0-5-1-7h0v-1l1 1 2 2v1h1 0l1-1s0-2 1-2c1 2 1 3 3 5s3 7 3 10l1 1v1l-2-2-1 1v5-2c-3-1-5-4-5-6v-1c0 1 0 2-1 2h-1 0l-1-2v-1l-1-4z" class="J"></path><path d="M282 484l1-1s0-2 1-2c1 2 1 3 3 5s3 7 3 10h-1c-1-5-5-8-7-12z" class="S"></path><path d="M279 488c-1-2 0-5-1-7h0v-1l1 1c0 2 1 3 1 4 3 4 6 10 8 15-3-1-5-4-5-6v-1c0 1 0 2-1 2h-1 0l-1-2v-1l-1-4z" class="R"></path><path d="M478 844l16 22c0 2 0 3 2 5l5 8 1 1v1l2 2c1 1 0 0 0 1l1 1v2l-6-7-1 1-2-4-16-27c-2-3-2-3-2-6z" class="D"></path><path d="M480 850l1-1v1l1 1c4 5 6 11 9 16 2 2 3 4 5 7 0 1 3 4 3 6l-1 1-2-4-16-27z" class="K"></path><defs><linearGradient id="y" x1="427.922" y1="384.701" x2="440.394" y2="387.261" xlink:href="#B"><stop offset="0" stop-color="#302e2e"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#y)" d="M425 397c2-5 6-9 9-13l9-13c0 2 1 5 1 7-1 1-2 3-3 4-2 4-5 7-8 11-2 2-5 5-8 7v-3z"></path><path d="M345 521c0-1 1-2 1-3h0v-1c0-1 0-1 1-3l1 2c1 1 2 3 2 5 1 5 0 11 0 16-1 2-1 5-1 7-1 1-1 2-1 2l-2 2-1-8v-19z" class="F"></path><defs><linearGradient id="z" x1="484.527" y1="547.847" x2="504.961" y2="533.234" xlink:href="#B"><stop offset="0" stop-color="#3e3d3c"></stop><stop offset="1" stop-color="#585858"></stop></linearGradient></defs><path fill="url(#z)" d="M499 513h2l-2 18c-1 4-2 9-1 13-1 5-3 10-5 15l-1-1c1-2 1-3 0-4l-1 1c-1 1-1 1-2 1l4-12c3-10 5-21 6-31z"></path><path d="M394 267h4l1 1c0 1-4 6-4 8-3 6-5 13-8 19v2h-1v-1c-2-2-1-6-1-8 1-4 3-8 5-12 0-2 2-4 2-6 1-1 1-2 2-3z" class="I"></path><path d="M858 300l2-2h0c0 2-1 3 0 4v2c-1 1-1 2-1 3h0v2c-1 4-4 9-5 13-1 1-3 7-5 8 0 0-1-1-2 0-1 0-3 2-4 3 1-3 4-5 5-8v-3c4-7 7-15 10-22z" class="E"></path><defs><linearGradient id="AA" x1="865.204" y1="337.131" x2="853.651" y2="355.097" xlink:href="#B"><stop offset="0" stop-color="#7f7e7f"></stop><stop offset="1" stop-color="#b7b5b5"></stop></linearGradient></defs><path fill="url(#AA)" d="M861 342c1-1 2-4 3-6l3 2c-4 6-7 12-11 17-3 3-5 6-7 8l-1-1 2-2 2-1c1-2-1 0 0-2h1c-4 0-5 5-9 6h0c2-2 4-4 5-6 0-2 2-4 3-5v-1l-3 3v-1l1-2v-1c1-1 2-3 3-4l1 1 4-4 1 1c0-1 1-1 2-2z"></path><path d="M859 344c0-1 1-1 2-2-3 6-7 10-12 15 0-2 2-4 3-5v-1l-3 3v-1l1-2v-1c1-1 2-3 3-4l1 1 4-4 1 1z" class="K"></path><path d="M853 346l1 1 4-4 1 1-3 2c-1 1-4 3-4 5l-3 3v-1l1-2v-1c1-1 2-3 3-4z" class="R"></path><defs><linearGradient id="AB" x1="821.609" y1="407.941" x2="823.283" y2="385.513" xlink:href="#B"><stop offset="0" stop-color="#cecbcb"></stop><stop offset="1" stop-color="#f7f7f6"></stop></linearGradient></defs><path fill="url(#AB)" d="M828 388h1c1 0 1-1 2-1h1 2c1-1 2 0 4 0l-27 20h-1 0-1c0-2 1-3 1-4 3-3 7-6 10-9 2-2 5-4 8-6z"></path><path d="M603 292l1-1c1-1 1-1 2 0v2c2 1 2 2 3 4 1 1 1 2 2 3l1 1 1 3c0 1 0 2-1 2l-1 1-1 2c-1 0-1 0-2-1v1c0 1 0 1-1 3h0c-1 1-1 2-2 2l-1-7c0-1 0-2-1-3 0-2 1-4 1-6l-4 1c1-2 3-4 3-7z" class="I"></path><path d="M603 292c1 0 1-1 2-1 1 2-1 5-1 7l-4 1c1-2 3-4 3-7z" class="C"></path><path d="M304 431l1-6h1l2 7c0 3 0 7-1 10s-3 6-2 9c-1 1-1 2-2 2h-1l-2 4-1 4c0 1-1 3-3 5 0-4 1-7 2-10l2-6 3-5c0-2 0-3-1-5l2-3 1-1-1-5z" class="U"></path><path d="M302 440l2-3 1-1c0 3 0 6-1 9h-1c0-2 0-3-1-5z" class="H"></path><path d="M303 445h1l-3 6c-1 2-1 4-1 6l-1 4c0 1-1 3-3 5 0-4 1-7 2-10l2-6 3-5z" class="X"></path><defs><linearGradient id="AC" x1="250.136" y1="483.191" x2="235.864" y2="480.809" xlink:href="#B"><stop offset="0" stop-color="#4d4c4c"></stop><stop offset="1" stop-color="#676767"></stop></linearGradient></defs><path fill="url(#AC)" d="M229 471l3 2c2 1 3 2 4 3h0l6 4h3v-2l2 1 1 1-1 1c1 2 3 3 3 5l1 2v1c-4-1-8-4-12-5-2-2-5-2-7-3 0-2-1-2-1-4l-2-2v-1c-1 0-3-1-4-1 1-1 2-1 4-2z"></path><path d="M242 480h3v-2l2 1 1 1-1 1c1 2 3 3 3 5-2-2-5-4-8-6zm-13-9l3 2c2 1 3 2 4 3h0v2l-1 3v1c0-3-2-2-2-4-1-3-2-3-4-4-1 0-3-1-4-1 1-1 2-1 4-2z" class="B"></path><path d="M411 333c0 5 1 7 5 10h-2 0v1c1 2 2 3 4 4-3 5-5 10-6 16-3-7-3-14-4-22 0-4 0-6 3-9z" class="L"></path><path d="M605 357c1-2 1-3 2-5 1-1 2-1 3-1h1c1-1 2-1 3-1h2 1 0c1 3 1 4 1 7-1 2-1 4-3 6l-1 1v1c-2-1-5-2-6-3-2-1-2-3-3-5z" class="I"></path><path d="M612 351h1c0 3 1 5 1 8-1 1-1 1-3 1 0 0-1 0-1-1-1-1-1-3-1-4 1-2 2-3 3-4z" class="E"></path><path d="M310 370c4 5 6 15 8 21 1 4 1 6-1 9v1l-1-1c-2-2-3-5-4-7-1-7-6-16-2-23z" class="f"></path><path d="M313 566h0l2 2c0 1-2 1-2 2h3v1h-3-1l-10 3c-4 1-8 1-12 3h-3l1-4c5-4 12-5 18-6l7-1z" class="V"></path><path d="M313 566h0l2 2c0 1-2 1-2 2h3v1h-3-1-5c1-1 2-1 3-1v-1c-1 0-3-1-4-2l7-1z" class="M"></path><defs><linearGradient id="AD" x1="717.386" y1="655.079" x2="734.2" y2="652.408" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#878685"></stop></linearGradient></defs><path fill="url(#AD)" d="M714 648c8 0 16-1 23 0-2 1-3 3-4 5 0 3 2 4 4 6-1 0-2 0-4-1h-1-1c-1-1-1-1-2-1-2-1-3-2-4-3-2-1-5-2-7-1-1 0-3-1-4-1 0-1 1-1 2-1h1c-1-1-2-1-3-2v-1z"></path><path d="M371 558h1c1 0 1 1 2 0 1 0 2 0 3 1v2 4c-1 2-2 5-2 7l1 6v5l4 10h-1c-2-1-3-7-5-9v-1c-1-2-1-5-1-7l-1 2h-1c0-2 0-4-1-5v5c-2-7 2-13 1-20h0z" class="F"></path><path d="M375 572c-1-1-1-2-1-3 0-2-1-6 1-8h2v4c-1 2-2 5-2 7z" class="B"></path><path d="M373 576v-3h1v1c0 3 1 6 2 9l4 10h-1c-2-1-3-7-5-9v-1c-1-2-1-5-1-7z" class="S"></path><path d="M722 423h1c1 0 3 2 3 3v4l-1 20c-1 3-1 5 0 8l-1 1c-2-1-3-3-6-4h-1l4-4v-4c-1-4 0-7 0-11 1-4 1-9 1-13z" class="N"></path><defs><linearGradient id="AE" x1="836.182" y1="123.406" x2="831.652" y2="103.137" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#AE)" d="M835 101v5c-1 1-2 0-1 2-1 2-2 5-1 8 0 1 2 2 3 3h2v-1c1-1 1-2 2-3l1 1c1 1 2 2 2 3 0 2-1 3-2 4s-3 1-5 1c-3 0-6-3-7-5-2-2-2-6-1-9 1-4 4-7 7-9z"></path><defs><linearGradient id="AF" x1="522.357" y1="397.546" x2="538.019" y2="385.053" xlink:href="#B"><stop offset="0" stop-color="#3f3f3b"></stop><stop offset="1" stop-color="#5c5a5d"></stop></linearGradient></defs><path fill="url(#AF)" d="M532 364v5l1 20h0v6c1 5-1 10-1 15h0l-1 1v1h-1c0-1 1-2 1-4l-1-1c0 2 0 7-1 9-1-2 0-19 0-22 1-2 1-5 1-8v-14c0-2-1-5 0-7l2-1z"></path><path d="M532 364v5l1 20h0l-1-2v-4c-1-6-1-12-2-18l2-1z" class="F"></path><defs><linearGradient id="AG" x1="594.979" y1="705.659" x2="614.337" y2="676.9" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#5b5a5a"></stop></linearGradient></defs><path fill="url(#AG)" d="M613 675c1 2 1 3 3 4l1-1-8 11c-3 4-6 9-10 13-1 1-2 3-4 4-2 2-3 4-4 6 0-1 0-2 1-2 0-1 1-2 1-3v-1l-1 2-1 1-2 1h0l2-2c1-1 3-4 4-6l18-27z"></path><path d="M533 567h0c0-2 0-4-1-5 0-1-1-1-2-2v24c0 3 1 7 0 10l-1-18v-37c0-7 0-14 1-21v1 6c0 2 1 5 1 7l1 11c1 3 2 7 2 11v12h-1v1z" class="C"></path><path d="M313 520c2 11 8 23 14 33 2 3 5 6 7 10-1 1-1 2-2 3-2-2-3-6-6-9l-6-9-2-6c-1-4-4-8-5-12l-3-6c1 1 1 1 2 1v-3c1-1 1-1 1-2z" class="Q"></path><path d="M571 442h2v1h2 2v-1h2c3 2 10 5 14 3h0c3 0 6 2 9 3 2 1 3 2 4 3-2 1-4 0-5 0h-1c-3-1-7-1-10-1h-3c-1 0-1 2-3 3-1-2-3-3-5-4v-1c-1-1-5-3-7-5l-1-1z" class="E"></path><path d="M591 449v-1c1 0 5-1 7 0h1 0c1 1 2 0 3 0 2 1 3 2 4 3-2 1-4 0-5 0h-1c-2-3-6-2-9-2z" class="J"></path><path d="M572 443c4 1 6 4 9 4 3 1 7 2 10 2s7-1 9 2c-3-1-7-1-10-1h-3c-1 0-1 2-3 3-1-2-3-3-5-4v-1c-1-1-5-3-7-5z" class="P"></path><path d="M530 518c0-2 1-4 2-6 1 1 1 2 1 3l4 17c3 12 7 22 12 33v1c-1-1-2-3-3-4 0-1-1-1-2-1l-9-25c0-2-1-5-1-6-2-4-1-8-4-11v-1z" class="Z"></path><path d="M530 518c0-2 1-4 2-6 1 1 1 2 1 3-1 3 0 6 1 9v6c-2-4-1-8-4-11v-1z" class="B"></path><path d="M553 459c0-2 0-3 1-4 1 0 1 0 0-1 0-1 0-1 1-2l1 3c1 1 2 3 2 4h0l5 5-6 14c-2-6-3-11-5-16l-2 14v5c-1-1-1-1-1-2v-6c0-3 0-8 2-10l2-4z" class="Z"></path><path d="M553 459c0-2 0-3 1-4 1 0 1 0 0-1 0-1 0-1 1-2l1 3c1 1 2 3 2 4h0c0 2 0 2 1 3-1 2-1 9-1 12-2-4-3-8-4-12l-1-1v-2z" class="J"></path><path d="M433 271c2 1 5 3 6 5 1 1 2 2 3 4 0 1 1 2 2 2h0v-1h0c1 0 1 1 2 2v1h-1c1 1 2 2 2 3s0 0 1 1c0 1 0 1 1 2l1 1 1 2c0 1 0 2 1 2l3 7c1 2 1 3 2 4 0 1 0 1 1 2v1 1h0c1 1 1 1 1 2l1 1h0c1 1 1 3 1 4s-1 3-2 4c0-1 0-1 1-2 0-2 1-2 0-4v-1c-1 1 0 3-1 4 0 1 0 1-1 3l1 1h-1c0 1-1 2-1 3-1 0-1 1-1 2-1 0-1 1-1 2h-1 0v-1-1c0-1 1-1 1-2l1-1h-2c0 1-1 1-1 2h0-1l1-1c0-2-1-3-1-5v-1-4-1l-1-4-1-4h-1v1-1c0-2-1-3-2-4l-6-13c0-2-1-4-2-6v-2l6 11c5 10 8 21 9 32 1-1 3-3 3-5l1-1v-2c0-4-2-9-4-13-2-5-4-10-7-15-4-6-9-12-14-17z" class="L"></path><defs><linearGradient id="AH" x1="475.044" y1="290.996" x2="465.991" y2="282.536" xlink:href="#B"><stop offset="0" stop-color="#484747"></stop><stop offset="1" stop-color="#5f5f5e"></stop></linearGradient></defs><path fill="url(#AH)" d="M474 272c1 3 5 8 4 12l-1 1v1c-2 4-5 8-7 12-1 2-2 4-3 5v1c0 1-2 3-3 3l-1 2v1l2-2v-1c2-1 3-3 5-3l-7 7-1-2v-1l3-3c1-2 1-4 1-6l3-8 2-8 1-1c1-3 1-4 1-7l1-3z"></path><defs><linearGradient id="AI" x1="786.362" y1="558.848" x2="795.175" y2="543.459" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#6a6968"></stop></linearGradient></defs><path fill="url(#AI)" d="M791 532c1 1 4 5 4 5l2 3c-1 2-2 3-2 5 1 1 0 2-1 3-1 5-3 12-8 14-1 0-1 0-2-1l1-1c2-4 3-9 4-14v-3l-1-7 1-2c1 0 1 0 2 1h1c0-1 0-2-1-3z"></path><path d="M789 546h0l2 2c-2 4-3 8-5 12v1l-1-1c2-4 3-9 4-14z" class="R"></path><path d="M791 532c1 1 4 5 4 5l2 3c-1 2-2 3-2 5 1 1 0 2-1 3 1-2 1-4 0-6v-1c0-1 0-2-1-3s-1-2-3-2c2 4 2 7 1 11v1l-2-2h0v-3l-1-7 1-2c1 0 1 0 2 1h1c0-1 0-2-1-3z" class="D"></path><path d="M814 251l3 7c1 3 2 5 3 7 3 9 0 17-3 25-2 0-3 1-5 2-1 0-2 1-3 2 1 1 1 1 2 0h2l-8 3c1-3 2-4 4-6l5-7h1c0-2 0-3 1-4l1-1v-5c1-6 0-13-3-19v-4z" class="R"></path><path d="M809 291l5-7h1c-1 3-2 5-4 8h-1l-1-1z" class="K"></path><defs><linearGradient id="AJ" x1="479.584" y1="495.451" x2="484.701" y2="495.581" xlink:href="#B"><stop offset="0" stop-color="#535252"></stop><stop offset="1" stop-color="#6b6a69"></stop></linearGradient></defs><path fill="url(#AJ)" d="M481 457l2 4v4l2 8c1 3 0 5 1 8v16c0 1-1 4 0 5-1 7-2 12-5 18h-3c4-13 5-24 5-37 0-5 0-11-1-17l-1-3v-4-2z"></path><defs><linearGradient id="AK" x1="519.939" y1="647.498" x2="533.061" y2="623.502" xlink:href="#B"><stop offset="0" stop-color="#4c4a4b"></stop><stop offset="1" stop-color="#676766"></stop></linearGradient></defs><path fill="url(#AK)" d="M527 616l4 30c1 2 1 3 1 5-1 1-1 3-2 3l-1-1-2-6-3-13-2-15 1-2c0 1 0 1 1 2s1 0 2 1v1-1c0-2 0-3 1-4z"></path><path d="M529 653h0 1c0-2 0-5 1-7 1 2 1 3 1 5-1 1-1 3-2 3l-1-1z" class="J"></path><path d="M349 567h0c1-1 1-2 1-3s0-1 1-2c0 1 0 1 1 2l1 2 1-3 5 10v1c0 2 1 3 1 5l2 1h2c0 1-1 2 0 3-1 1-1 2-1 4l-1 1 3 4v2l-10-15h-1c0-1-1-2-1-3l-1 1c0-2-1-3-2-5 0-2 0-3-1-5z" class="C"></path><path d="M360 579l2 1h2c0 1-1 2 0 3-1 1-1 2-1 4l-1 1-2-5v-4z" class="Q"></path><path d="M349 567h0c1-1 1-2 1-3s0-1 1-2c0 1 0 1 1 2 0 3-1 5 0 7l3 8h-1c0-1-1-2-1-3l-1 1c0-2-1-3-2-5 0-2 0-3-1-5z" class="H"></path><path d="M354 563l5 10v1c0 2 1 3 1 5v4l-7-17 1-3z" class="T"></path><path d="M499 371v-9c1 1 1 3 1 5l1 11c0 1-1 3 0 4v3 4h0c1 8 1 18 1 27h-1c-1-4-2-7-4-10 1-2 0-4 0-6-1-3 0-8 0-12h1v-16l1-1z" class="C"></path><path d="M498 372l1-1c1 8 0 15 0 22 0 3 1 7 0 10-2-2-1-12-1-15v-16z" class="B"></path><path d="M675 652c2-1 4-2 6-2 10-3 23-3 33-2v1c1 1 2 1 3 2h-1c-1 0-2 0-2 1-5-1-9-1-14-1-3 0-8 2-11 1l-12 3h-2c-2 0-4 0-7-1l7-2z" class="J"></path><path d="M675 652h6c1-1 6-1 8 0h0l-12 3h-2c-2 0-4 0-7-1l7-2z" class="E"></path><path d="M240 260h1c1 1 1 2 2 3 0 2 0 1 1 2 0 0 0 1 1 2 2 4 3 6 3 11l-1 1v2c-1 1-2 3-3 5v1h0c0 1 0 1-1 2v2h0-1c0-2 1-2 1-4 0-1 0-1 1-2 0-1 1-2 1-4v-2h0v-1c-1-1-1-2-2-2h-1c-1-1-1-1-2-1-2-1-4-3-6-3v2c-1 2-1 5-1 7h0v-7h0c1-4 2-7 4-10l1-1 2-3z" class="f"></path><path d="M174 256v1 1c-1 2-1 5-2 7-3 15 2 29 5 43-4-5-8-13-9-20-1-4-1-9-1-13 1-7 3-13 7-19z" class="I"></path><path d="M671 542l1 8c0-1 0-4 1-5l1 2v4l1 8c0 4-1 9 1 12 0 2 1 4 2 6h-1c0-1-1-1-1-2-1 1-1 1-1 2h-1-1l-1 3-1-1v-1c1-1 0-1 0-2-1 2-1 4-3 6h-1c2-5 3-9 4-13-1-1-1-2-2-3 1-5 2-9 1-13v-1l1-10z" class="M"></path><path d="M671 542l1 8-1 19c-1-1-1-2-2-3 1-5 2-9 1-13v-1l1-10z" class="F"></path><path d="M817 310c2 1 3 1 4 1h2l-1 1h-1-1l2 2c1 1 3 2 3 3l1 2h0l-1-1c-1 0-1 0-1 1h0c-1-1-1-1-2-3-2-1-3-1-4-1h-1c0-1-1 0-1 0h-1c-1 0-2 0-3 1-1 0-1 0-1 1-1 0-2 0-3 1h-1c-1 2-3 3-4 6v-1l-1 1v1c-1 1 0 3 0 4v1h0c0 2 0 1 1 2 0 2 0 3 2 5v2h1c0-1 0-2 1-4v-1-2c0-1 1-1 1-2v-1c1-2 2-3 4-4 3 0 3-1 5 2 1 0 1 0 1 1 1 1 1 1 1 2-2-1-2-3-4-4v1l2 2c-1 1 0 1-1 1h0c-1-2-1-3-2-3-2 0-3 1-3 2-1 1-1 2-2 3v3c0 1 0-1 0 1-1 0 0 1 0 1v1 1 2h1 1 0c1 0 2-1 3-1v1c-1 1-3 1-4 1-2 0-4-1-5-3-2-3-3-5-4-9 0-1 0-3-1-4l1-2 1-2 1-3c3-6 8-6 14-8v-1z" class="I"></path><defs><linearGradient id="AL" x1="471.008" y1="204.152" x2="490.143" y2="227.222" xlink:href="#B"><stop offset="0" stop-color="#474646"></stop><stop offset="1" stop-color="#828180"></stop></linearGradient></defs><path fill="url(#AL)" d="M471 205c1-2 1-3 2-4 1 1 4 3 4 4 0 3 2 4 3 6 1 3 3 5 5 8 1 2 2 5 3 7 1 1 1 2 1 3l2 6c-1-1-2-3-3-4-1-3-4-10-6-12h-2l-2-2-1-1v2h-1l-5-5-1 1c0-1-1-2-1-3-1 0 2-5 2-6z"></path><path d="M471 205c2 1 4 5 5 7 1 1 3 2 2 3v1 1l-1-1v2h-1l-5-5-1 1c0-1-1-2-1-3-1 0 2-5 2-6z" class="S"></path><defs><linearGradient id="AM" x1="712.466" y1="501.523" x2="704.718" y2="504.064" xlink:href="#B"><stop offset="0" stop-color="#4a4a49"></stop><stop offset="1" stop-color="#646263"></stop></linearGradient></defs><path fill="url(#AM)" d="M709 486c1 3 1 6 3 9h0v1 3c1 3 1 6 2 9 1 1 1 2 1 3v-1c-1 0-1-1-1-2h-1v3 1c-3 0-6-1-8-2h1c0-1 0-2-1-3h-2l-1-1 1-4h0 1c2-2 2-2 2-5v-7l1 1 1-1c0-1 0-2 1-4z"></path><path d="M706 490l1 1c0 2 0 2 1 3 0 2 0 5-1 7-1 1-1 2-1 3v-7-7z" class="B"></path><path d="M712 499c1 3 1 6 2 9 1 1 1 2 1 3v-1c-1 0-1-1-1-2h-1v3 1c-1-2-1-5-1-7-1-3-1-4 0-6z" class="L"></path><path d="M709 486c1 3 1 6 3 9h0v1h-2v-1-1l-1 1v-2l-1 1c-1-1-1-1-1-3l1-1c0-1 0-2 1-4z" class="e"></path><path d="M706 497v7c0 1 0 2-1 3h-2l-1-1 1-4h0 1c2-2 2-2 2-5z" class="H"></path><defs><linearGradient id="AN" x1="222.134" y1="258.326" x2="198.352" y2="281.687" xlink:href="#B"><stop offset="0" stop-color="#111011"></stop><stop offset="1" stop-color="#393938"></stop></linearGradient></defs><path fill="url(#AN)" d="M211 247l1 1v1h1v1c-2 1-2 3-3 4-4 13-5 26 1 38 0 1 1 1 1 2v1c-2-1-4-2-5-3-1-2-2-5-2-7-2-8-3-17 0-25 1-4 3-9 6-13z"></path><defs><linearGradient id="AO" x1="389.57" y1="260.368" x2="428.883" y2="267.881" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#AO)" d="M397 262c0-1 0-2 1-3v-1c-1-1-2-1-3-1h-1-1l-3-1v-1l3 1h5v-1h1c0 1 1 1 2 1h2l1 1h3v1l4 2h2 0c1 1 2 1 3 2l2 2h1l1-1 1 1c1 0 1 1 2 1l2 2c0 1 1 1 2 2l3 5c-2 0-3-1-5 0-2-2-4-3-6-5 0 0-2-1-2-2-4-2-8-5-12-6-1 0-2 0-3 1h-5z"></path><defs><linearGradient id="AP" x1="566.731" y1="215.782" x2="570.846" y2="234.312" xlink:href="#B"><stop offset="0" stop-color="#3b3b3a"></stop><stop offset="1" stop-color="#535352"></stop></linearGradient></defs><path fill="url(#AP)" d="M568 214c3 3 6 9 8 13-2 4-3 7-6 9-3-3-5-6-8-10 0-2 2-4 3-5l3-7z"></path><path d="M472 460v-1l3-2c1 0-1-1 1 0l1 1c1 1 1 2 1 3h0c0 4-1 8 0 12h1c0 3 0 6-2 9v-6c-1 1-1 2-2 3 0 1 0 1-1 2v-3l-2-3-5-10 5-5z" class="R"></path><g class="B"><path d="M474 478c0-4 1-8 2-12 0-2-1-5 0-7 3 3 1 13 1 17-1 1-1 2-2 3 0 1 0 1-1 2v-3z"></path><path d="M472 460v1c0 1-1 1-1 2v4h-1c1 1 1 2 2 3 1-2-1-7 1-8v2h1v-1c1 1 1 2 1 3l-3 9-5-10 5-5z"></path></g><path d="M783 342l5 9c1 2 3 5 4 7-1 2-1 2-3 3s-4 1-6-1h-1-1c-2-1-4-3-6-5 2-2 3-4 5-7 1-2 1-4 3-6z" class="R"></path><path d="M295 655v1c0 1-1 2-1 3v4h1v-1h1c0-1 0-1 1-2l1-1h1c-2 2-4 5-5 8s0 5-1 7l1 10 1-1v7c-3-4-5-8-6-12-1-5-3-10-3-14 0-1-1-3 0-3 0-1 0-1 1-2h0c1 1 2 1 3 2l1-1 2-2 2-3z" class="Q"></path><path d="M295 655v1c0 1-1 2-1 3v4h1v-1h1c0-1 0-1 1-2l1-1h1c-2 2-4 5-5 8s0 5-1 7l1 10v3-1c-1-1-1-3-2-5-1-6-3-12-1-18v-2-1l2-2 2-3z" class="J"></path><path d="M295 655v1c0 1-1 2-1 3v4h1v-1h1c0-1 0-1 1-2l1-1h1c-2 2-4 5-5 8s0 5-1 7v-4h-1c-1-4 1-7 1-11v-1l2-3z" class="c"></path><path d="M661 593l3-3c0-1 0-1 1-1l1 1-5 6 1 1-1 1-4 3h0l1-2v-1c-5 7-11 13-16 20l-4 7v-2h-1c0-1 0-1-1-1 1-1 1-1 1-3 2-1 2-3 3-5l1-1h-1c1-3 5-6 7-7l3-3c-2 0-3 1-5 0l4-3c4-2 7-4 11-7h1z" class="J"></path><path d="M649 600c4-2 7-4 11-7h1c-1 1-2 3-4 4-3 1-4 5-7 6-2 0-3 1-5 0l4-3z" class="E"></path><defs><linearGradient id="AQ" x1="518.019" y1="586.269" x2="501.816" y2="541.654" xlink:href="#B"><stop offset="0" stop-color="#656364"></stop><stop offset="1" stop-color="#bcbcb9"></stop></linearGradient></defs><path fill="url(#AQ)" d="M509 536v3l2-1v-4l1-1-1 12 1 30c0 5-1 11 0 16v1c-1-1-1-1-1-2l-1-1c-1 1-1 2-2 3l1-56z"></path><path d="M329 614h2c8 1 15 3 23 6-1 1-1 2-3 2h-1-1c-5 1-11-3-15-1-3 0-5-2-8-2-4-1-7-2-11-2 1 0 1 0 2-1l-1-1h2c2 0 5 1 6 0l1-1h4z" class="C"></path><defs><linearGradient id="AR" x1="342.23" y1="625.144" x2="334.974" y2="609.787" xlink:href="#B"><stop offset="0" stop-color="#b5b3b1"></stop><stop offset="1" stop-color="#e3e2e2"></stop></linearGradient></defs><path fill="url(#AR)" d="M329 614h2c8 1 15 3 23 6-1 1-1 2-3 2h-1l-26-7 1-1h4z"></path><path d="M450 243c-1 0-2-2-3-2-3-3-6-5-10-8l6 3c1 1 2 2 3 2 1 1 1 0 2 1 1 0 1 1 2 1 2 2 4 5 6 6l2 2c0 1 0 1 1 1 1 2 2 3 3 4v1h1v-1c-1-1-2-3-3-4s-1-2-2-3l-1-1c-1-1-1-3-3-4v-1l-1-1h0l1-1v1l1-1c1 0 1 0 2 1l-1-1-1-1-1-2h0c-1-2-2-1-3-2h0c1-1 0-1 0-3 2 1 2 2 3 3 5 5 10 13 12 19l1 3c1 2 0 2 1 3h2c-1 2-1 2-3 2-3-1-4-4-6-7-3-3-8-10-11-10z" class="O"></path><path d="M454 233c5 5 10 13 12 19l1 3v4l-1-1c0-1-1-3-1-5-3-4-7-7-8-12 0 0 1 0 2 1v-1c-1-3-4-5-5-8z" class="F"></path><path d="M620 688v1c-2 2-3 3-3 6l-4 4-23 31-5 7c-1-3 0-2 1-4v-2h1v-3c1-3 4-7 6-9 8-11 17-21 27-31z" class="c"></path><path d="M593 719v3c-1 2-4 6-3 8l-5 7c-1-3 0-2 1-4v-2h1v-3c1-3 4-7 6-9z" class="B"></path><defs><linearGradient id="AS" x1="804.248" y1="384.803" x2="808.692" y2="375.432" xlink:href="#B"><stop offset="0" stop-color="#9a9797"></stop><stop offset="1" stop-color="#bdbcbb"></stop></linearGradient></defs><path fill="url(#AS)" d="M807 373v3h1c2-1 4-1 6-1-1 1-1 1-1 2v1l1 1c-1 0-1 1-1 1h-1l-5 4-3 2-5 4c-2 0-3 0-4 1h0l-1-1c0-2 0-3 1-5 2-1 2-2 3-4l-1-1 3-3c2-2 3-3 6-3l1-1z"></path><path d="M807 373v3h1l1 1c-1 0-3 1-4 1 0-1-1-2-1-2l2-2 1-1z" class="N"></path><path d="M800 377c2-2 3-3 6-3l-2 2-3 4-3 2v-1l-1-1 3-3z" class="B"></path><path d="M798 382l3-2v3 1 1c1 0 2 0 3-1h3l-3 2h0c-2 0-3 0-5 1l-1 1v-1-2-3z" class="Q"></path><path d="M798 381v1 3 2 1l1-1c2-1 3-1 5-1h0l-5 4c-2 0-3 0-4 1h0l-1-1c0-2 0-3 1-5 2-1 2-2 3-4z" class="P"></path><defs><linearGradient id="AT" x1="779.702" y1="392.168" x2="780.266" y2="384.754" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#555353"></stop></linearGradient></defs><path fill="url(#AT)" d="M787 381c2 1 3 1 5 0 1 0 3-2 4-3v2h1l1 1c-1 2-1 3-3 4 0 0-1 1-2 1-2 0-5 3-7 5v1c-1 1-2 2-2 3l-4 4h0c-1 1-1 0-1 1h-1c1-4 6-8 9-12-3 1-8 8-9 8l2-3h-1c-1 1-2 3-3 5l-2 2c-1-1-1-1-1-3h0v-2c1-1 2-2 2-3 3-4 8-8 12-11z"></path><path d="M780 393h0c1-1 2-1 2-3h-1c1-2 2-2 3-3 4 0 4-2 6-3l1 1c-1 1-3 2-4 3h0c-3 1-8 8-9 8l2-3z" class="C"></path><path d="M765 468c0 3-1 5-2 7h1l1-1 1 1-1 2h0c1 1 0 1 1 2 0 0-1 1-1 2v1l-2 5-6 10-2 5-4 4v1c0 2-2 3-3 3-1 1-3 1-4 1 2-2 2-5 4-7h1c4-8 8-15 11-23h0l5-13z" class="I"></path><path d="M755 497h2l-2 5-4 4h-1c1-3 3-6 5-9z" class="C"></path><path d="M763 475h1l1-1 1 1-1 2h0c1 1 0 1 1 2 0 0-1 1-1 2v1l-2 5-6 10h-2l5-10c1-3 2-5 2-8l1-4z" class="F"></path><path d="M763 475h1l1-1 1 1-1 2h0c1 1 0 1 1 2 0 0-1 1-1 2v1l-2 5-1-2c1-1 2-2 2-3h-1c0 1-1 4-3 4v1c1-3 2-5 2-8l1-4z" class="E"></path><path d="M317 567c2-1 3-1 4-1l3 2 3 1v1l1 1c0 1 0 1-1 2h-1l-1 1h0c1 0 4 0 5-1h1c1 1 2 2 2 3l1 1v1h-4c-2-2-7-1-10-2-5-2-10 0-15-1h-2l-1-1 10-3h1 3v-1h-3c0-1 2-1 2-2l-2-2c2 0 3 1 4 1h0z" class="D"></path><path d="M313 566c2 0 3 1 4 1h0c3 1 4 1 6 4-2 1-7 0-10 0h3v-1h-3c0-1 2-1 2-2l-2-2z" class="H"></path><path d="M324 568l3 1v1l1 1c0 1 0 1-1 2h-1l-1 1h0c1 0 4 0 5-1h1c1 1 2 2 2 3h-1-1-4-2l-1-1h-2c-2-1-3-1-4-1 2-2 6-1 9-2v-1l-3-3z" class="L"></path><path d="M215 264l1-1h1c-1 14 1 26 10 37h0c-4-1-8-4-11-7-2-4-3-8-4-12v-12-3c0 1 1 2 1 2h1c0-2 0-2 1-4z" class="b"></path><path d="M212 269v-3c0 1 1 2 1 2h1c0-2 0-2 1-4v1c-2 7-1 14 1 20 0 1 1 3 1 4s-1 2-1 2l-2-9c-1-3-1-6-1-9 0-1 0-3-1-4zm7 65c0 1 0 2-1 3 0 1-1 1-1 2v1c-1 1-2 1-3 1-1 1 0 1-1 2l-2-1v-1c0-1 1-1 1-1 0-1 0-2 1-2 0-1 0-2 1-3 1-2 2-5 3-7v-4h0c-1-2-1-3-2-4v-1c-1-2-4-4-6-4v-1c0-1 1 1 3 0-1-1-2-1-3-1-1-1-2-1-3-1h-3 0c2-1 4-1 5 0h2c2 1 3 2 5 3 1 0 1 0 2 1 0 1-1-1 0 1l1 1h1 0c0-1 0-1-1-2h0c0-1 0-2-1-2l-1-2-2-2c-1 0 0 0-1-1h2 1l1 1c1 2 3 3 4 6v1c1 4 1 9 0 13l-2 4z" class="f"></path><defs><linearGradient id="AU" x1="639.529" y1="625.454" x2="638.07" y2="604.006" xlink:href="#B"><stop offset="0" stop-color="#2c2c2d"></stop><stop offset="1" stop-color="#5c5a5a"></stop></linearGradient></defs><path fill="url(#AU)" d="M645 603c2 1 3 0 5 0l-3 3c-2 1-6 4-7 7h1l-1 1c-1 2-1 4-3 5 0 2 0 2-1 3 1 0 1 0 1 1h1v2c-2 3-3 6-6 8-1 2-2 3-3 3h-1 1c0-2 0-3 1-4h2l-1 1c-1 0-1 0-1 2 1-2 2-2 2-3 1-1 0-3 0-4-1 0-1 0-1 1h0v-1c-1-6-1-7 2-12 1-1 3-4 4-6 2-2 5-5 8-7z"></path><path d="M632 633l1-4c1-2 1-3 3-4 0-1 1-2 2-2v2c-2 3-3 6-6 8z" class="D"></path><path d="M792 308h0c2-1 4-4 6-5 0 1-2 6-3 7l-2 9-1 1h1c0 2 0 3-1 5-1 5 0 11 3 16h-1 0-1c-1-1-2-4-2-5l-1-1h-1c0-1-1-2-1-2h-1l1 1 1 4v2 1h1v1 1h0c-3-5-5-14-4-20h0l1-3c1-4 2-9 5-12z" class="C"></path><path d="M787 320c1-1 1-3 3-4v3h1l-2 11-1-8-1 1h-1l1-3z" class="B"></path><path d="M792 308h0c2-1 4-4 6-5 0 1-2 6-3 7-3 2-4 6-4 9h-1v-3c-2 1-2 3-3 4 1-4 2-9 5-12z" class="G"></path><path d="M649 300l-14-31c1-1 3-1 4-1 2 0 9 17 10 20l4 9c0 2 0 5-1 7l-1-1c0-1-1-2-2-3z" class="C"></path><path d="M649 288l4 9c0 2 0 5-1 7l-1-1c0-1 1-4 0-5-1-2 0-4-1-5 0-2-1-4-1-5z" class="K"></path><path d="M414 327c1-3 1-6 3-9v3l-1 1c0 1-1 2-1 3v1c0 1 0 1-1 2v2h1 0v2c-1 1-1 1-1 2h1v-1-1c1-1 1-1 1-2v-2c0-1 0-2 1-3v-2c0-1 0 0 1-1h0c-1 4-3 9-1 13 1 1 2 3 4 3 1 1 2 1 4 1l1-1c2-1 3-3 3-5 1-1 1-2 2-3-2-2-2-4-3-6 2 2 3 4 5 6v4 1h-1c0 1-1 1-1 2-1 2-3 4-5 5h-6l-2 2-2-1c-4-3-5-5-5-10l2-9c0 1 1 2 1 3z" class="C"></path><path d="M431 330c0 3 0 5-2 8 0 1-1 1-2 1h-2l1-1c2-1 3-3 3-5 1-1 1-2 2-3z" class="J"></path><defs><linearGradient id="AV" x1="411.783" y1="330.917" x2="415.14" y2="342.398" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#a3a2a0"></stop></linearGradient></defs><path fill="url(#AV)" d="M413 324c0 1 1 2 1 3-1 3-1 7 0 10 2 2 3 4 6 5l-2 2-2-1c-4-3-5-5-5-10l2-9z"></path><path d="M430 355l1 3 1-2h1v3l1 1v3l1 1-1 1-1 1c0 1 0 2-1 3v1c2 0 1 0 2 1v1c1 1 1 1 2 1l1-1v-1c0-1-1-1 0-2s1-1 1-2l1 1s-1 1-1 2c1-1 1-1 1-2l1 1c1-1 1-2 1-3v-1c1-2 1-6 0-7v-5c1 2 1 11 1 14-1 2-4 5-5 7-3 4-6 11-10 13h0l2-2h0l1-1v-1-1h-1v-2c0-2 0-2-1-3v-2h0c-1-1-1-2-1-2-1-1-1-2-1-2l-1 2c0 1 0 1-1 2-1 0-1 1-1 2-1 1-1 1-1 2l-1 1c0-1 7-15 8-18 1-2 1-4 1-7h0z" class="O"></path><defs><linearGradient id="AW" x1="443.065" y1="281.584" x2="448.992" y2="277.771" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#91908f"></stop></linearGradient></defs><path fill="url(#AW)" d="M429 261h0c1-1 1-1 1-2 1 0 1 1 2 1l3 3h1c6 5 14 13 18 21l1 1c3 8 8 13 7 22l-1 1c-2-6-5-13-8-19-6-11-15-19-24-28z"></path><path d="M775 637c2-2 3-3 5-4 0-1 0-2 2-2 0 1 1 0 1 2 0 0 0 1 1 1-2 7-6 13-10 18-1 1-3 3-4 3v1h-2c0-3-1-6-3-8l1-1c1 0 3-2 3-3v-1-1c3 0 5-2 7-4l-1-1z" class="L"></path><path d="M776 638l1 1c-1 2-2 4-4 6-1 0-1 1-1 2-1 1-2 2-2 3v-4c0-1 0-1 1-2v-1-1h-2c3 0 5-2 7-4z" class="F"></path><path d="M769 642h2v1 1c-1 1-1 1-1 2v4 1 2c-1 1 0 2 0 3h-2c0-3-1-6-3-8l1-1c1 0 3-2 3-3v-1-1z" class="E"></path><defs><linearGradient id="AX" x1="774.369" y1="654.822" x2="778.779" y2="631.394" xlink:href="#B"><stop offset="0" stop-color="#605f5f"></stop><stop offset="1" stop-color="#a4a2a1"></stop></linearGradient></defs><path fill="url(#AX)" d="M782 631c0 1 1 0 1 2 0 0 0 1 1 1-2 7-6 13-10 18-1 1-3 3-4 3v1c0-1-1-2 0-3v-2c6-5 9-13 12-20z"></path><defs><linearGradient id="AY" x1="505.431" y1="297.117" x2="499.069" y2="296.383" xlink:href="#B"><stop offset="0" stop-color="#5e5d5a"></stop><stop offset="1" stop-color="#818383"></stop></linearGradient></defs><path fill="url(#AY)" d="M498 245c1 4 2 8 2 12 1 1 1 3 2 4v-1c1 6 2 12 2 18v61 12 5c-1-1 0-33-1-37v4c0-2 0-4-1-5-1-2 0-5-1-7l1-27c0-6 0-12-1-17 0-4-1-8-2-12l-1-5-1-4 1-1z"></path><path d="M582 468c-1-1-1-1-1-2-1-1-2-1-2-2h-1c0-1-1-2-2-3-3-4-6-9-8-13l4 3c4 1 7 4 9 7 5 4 9 9 13 13v1 1c1 1 2 2 2 4h-1 0c-1-1-3-2-4-3l-3-2c1 2 1 3 2 5h-1-1-2c1-1 1-3 1-4v-1h0c-1-2-3-4-5-4z" class="D"></path><path d="M197 344c0-1 0-1-1-2h0 2 0l35 32-1 1-3-2c-1 0-2-1-3-1l-2-1v2l-2-2-8-8c-5-6-12-10-17-16l1-2-1-1z" class="V"></path><path d="M198 345l31 28c-1 0-2-1-3-1l-2-1v2l-2-2-8-8c-5-6-12-10-17-16l1-2z" class="L"></path><path d="M740 638l5-1c1-1 3-2 5-2v2h0l10 1c1 1 2 0 4 0h1c2 0 4 0 5 1 2 0 4-1 5-2l1 1c-2 2-4 4-7 4v1h-18l-9-1c-3-1-6 0-9-1-1 0-1 0-2-1h3c2-1 3-2 5-2h1z" class="b"></path><path d="M739 638h1 1c1 1 1 1 2 1l2 2c-4 0-7-1-11-1 2-1 3-2 5-2z" class="B"></path><path d="M740 638l5-1c1-1 3-2 5-2v2h0l10 1c1 1 2 0 4 0h1c2 0 4 0 5 1-3 1-5 1-8 2-5 1-12 0-17 0l-2-2c-1 0-1 0-2-1h-1z" class="H"></path><path d="M750 637l10 1c1 1 2 0 4 0h1c2 0 4 0 5 1-3 1-5 1-8 2 0-1 0-1 1-2h-1c-2 0-11 0-13-1l1-1z" class="T"></path><defs><linearGradient id="AZ" x1="758.932" y1="494.903" x2="769.785" y2="484.333" xlink:href="#B"><stop offset="0" stop-color="#2f2f2f"></stop><stop offset="1" stop-color="#494948"></stop></linearGradient></defs><path fill="url(#AZ)" d="M779 456c0 1 1 1 0 2v1 1c-1 1-1 2-1 3-1 1-1 1-1 2v1h0c0 2-1 3-2 5l-3 6c-2 3-2 5-3 7-2 1-3 5-3 7v2c-1 1-2 3-2 4-1 0-1 0-1 1l4-3 1 1a30.44 30.44 0 0 1-8 8c0-1 1-1 1-2h1l-1-1v-2c1-1 1-1 1-2-1 0-2 1-3 2h-1c-1 1-1 2-3 2v1l2-5 6-10 2-5 3-4c0-1 1-2 1-3l5-8 2-4 3-7z"></path><path d="M774 467c0 5-4 8-6 13l-9 16 1 1 2-1h1l1 1c-1 0-1 0-1 1l4-3 1 1a30.44 30.44 0 0 1-8 8c0-1 1-1 1-2h1l-1-1v-2c1-1 1-1 1-2-1 0-2 1-3 2h-1c-1 1-1 2-3 2v1l2-5 6-10 2-5 3-4c0-1 1-2 1-3l5-8z" class="L"></path><defs><linearGradient id="Aa" x1="272.72" y1="375.648" x2="259.362" y2="398.485" xlink:href="#B"><stop offset="0" stop-color="#949494"></stop><stop offset="1" stop-color="#d6d5d2"></stop></linearGradient></defs><path fill="url(#Aa)" d="M261 374c-1-1-2-4-1-6 0-1 0 0 1-1 1 1 2 3 3 4 4 5 6 11 8 17 2 3 3 6 4 9 1 0 1 1 1 2-1 1-1 2-1 3h1l-1 2-1-2c-1 1-3 3-4 3h-1l-1 2v-1l1-5v-5h0c-1-1-1-2-1-4v-3l-3-6h0c-1-2-4-5-4-6s0-2-1-3z"></path><path d="M270 396v-1c1 1 2 1 2 2v4h-2v-5z" class="T"></path><path d="M495 825h1c2 4 3 9 5 13l9 23 4 10 6-2h1c1 0 1 0 1-1l1-1 1-3v-1-1c1-1 1-3 2-4 0-1 0-1 1-2h0v-1-1l1-1c1-4 3-8 4-11v-1-1c0-1 1-2 1-2l1-1v-3c1-1 1-1 1-2h0v-1c1-1 2-2 2-4h0v-1-1c1 0 1-1 1-1l1-3v-1l1-1v-1c1-2 0-1 1-2 0-1 1-2 1-3s1-1 1-2l-25 74-12-32c-4-9-8-19-11-28z" class="X"></path><path d="M398 637c4 1 9 2 13 4l3 8-1-5c1-1 0-1 1-1 1-1 3-2 5-1h1c2 5 1 12 2 17-1-1-2-1-3-3 0-1-1-1-2-2-1 0-2-1-3-2h0v5l-4-6c-1-3-3-4-5-6h0c-1 0-1-1-2-1l-2-2c-1-2 0-1-1-2 0-1-1-2-2-3z" class="F"></path><path d="M321 630c1-1 3-1 4-2h16c4 1 8 2 13 4 1 0 3 1 4 2h0c1 1 2 2 3 2-1 1-1 2-2 3h-1-2c-1 0-3-1-4-1l-8-3c-7-2-15-4-23-5z" class="a"></path><defs><linearGradient id="Ab" x1="442.941" y1="303.673" x2="451.947" y2="298.087" xlink:href="#B"><stop offset="0" stop-color="#474544"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#Ab)" d="M430 267c0 1 2 3 3 4 5 5 10 11 14 17 3 5 5 10 7 15 2 4 4 9 4 13v2l-1 1c0 2-2 4-3 5-1-11-4-22-9-32l-6-11c-2-5-6-9-10-13l1-1z"></path><path d="M431 726h0c1 3 2 5 2 7 1 1 2 2 2 4 0 1 0 3-1 4 0 2 1 4 2 7h-1l1 1c0 1 0 1 1 2 0 2 1 3 2 5v1h-1c-2-3-4-6-7-7-1 0-1 0-2 1l-1 1h0l-4 2c-2-1-4-3-5-5v-1l2-2c3-1 4-2 7-4v-1c1-2 2-5 2-7 2-3 1-5 1-7v-1z" class="B"></path><path d="M419 748h1c1 0 3 0 4 1s2 2 4 3h0l-4 2c-2-1-4-3-5-5v-1z" class="J"></path><path d="M431 726h0c1 3 2 5 2 7 1 1 2 2 2 4 0 1 0 3-1 4 0 2 1 4 2 7h-1l-3-6-2 6-1 1c0-2-1-2-1-4v-2-1-1c1-2 2-5 2-7 2-3 1-5 1-7v-1z" class="P"></path><path d="M432 742c1-3 1-6 1-9 1 1 2 2 2 4 0 1 0 3-1 4 0 2 1 4 2 7h-1l-3-6z" class="J"></path><path d="M867 370l1 1c-2 3-4 7-6 10s-4 5-4 8c0 0 0 1 1 1 0 1 1 1 3 1 1 0 1 0 2 1v1c-1 3-6 7-8 8-4 1-6 0-9-2h1l1 1h2l1 1h2 1c0-1 1-1 2-1l1-1h0v-1h-3c-1-1-3-3-3-4 0-2-1-3-1-5 1 0 0 1 1 1 0-1 0-2 1-3v-1-1c0-1 1-2 1-3 1-3 3-5 6-7 3-1 5-3 7-5z" class="D"></path><defs><linearGradient id="Ac" x1="528.054" y1="898.836" x2="513.457" y2="901.986" xlink:href="#B"><stop offset="0" stop-color="#565555"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#Ac)" d="M521 900c1-2 1-5 2-7 1-3 2-7 4-10 0 3-1 8 0 9-2 8-6 16-9 24h-1c-1-4-4-9-4-13 0-3 1-6 3-9l1-3c3 2 3 6 4 9z"></path><path d="M516 894l1-3c3 2 3 6 4 9 0 1 0 4-1 5-3-1-2-8-4-11z" class="H"></path><defs><linearGradient id="Ad" x1="543.726" y1="352.701" x2="515.857" y2="321.724" xlink:href="#B"><stop offset="0" stop-color="#4f4d4b"></stop><stop offset="1" stop-color="#7e7f81"></stop></linearGradient></defs><path fill="url(#Ad)" d="M530 289c1 4-1 18 0 20 1 0 1 0 1 1l1-16h0l1 34-1 19c-1 5 0 12 0 17l-2 1c-1 2 0 5 0 7v14c0 3 0 6-1 8l1-105z"></path><path d="M348 576l1-1c1 1 1 0 1 2l2 2v-2l1-1c0 1 1 2 1 3h1l10 15v-2c2 4 4 6 8 8 0 1 1 1 2 1l9 12v1h-3-1c-2 0-4-3-5-4h0c-3-3-6-6-8-9-3-4-7-7-10-11s-6-9-9-14z" class="P"></path><path d="M365 592c2 4 4 6 8 8 0 1 1 1 2 1l9 12v1h-3-1c-2 0-4-3-5-4h0v-1c0-1-1-2-2-2l-5-8c-1-1-2-3-3-5v-2z" class="J"></path><path d="M380 614c-1-2-2-3-2-6h1c1 2 3 4 5 6h-3-1z" class="I"></path><path d="M752 466c3-2 3-6 5-9l-1 7c0 5-1 9-3 14l-5 21v1h0c-2 2-2 5-4 7-1 2-2 4-3 5l-1-1v1l-1-1h0c1-1 1-1 1-2v-1c0-1 1-3 2-4 1-3 3-6 4-9v-3-1-1l1-1c0-3 0-5 1-8l3-9c0-2 1-3 1-5v-1z" class="B"></path><path d="M752 466c3-2 3-6 5-9l-1 7c-1 2-2 3-2 5h-1v2c-1 1 0 1-1 1h-1c0-2 1-3 1-5v-1z" class="Y"></path><path d="M746 495l6-18h0l-3 13c0 2-2 5-2 7 0 1 1 1 1 2v1h0c-2 2-2 5-4 7-1 2-2 4-3 5l-1-1v1l-1-1h0c1-1 1-1 1-2v-1c0-1 1-3 2-4 1-3 3-6 4-9z" class="I"></path><defs><linearGradient id="Ae" x1="601.646" y1="311.339" x2="594.555" y2="309.761" xlink:href="#B"><stop offset="0" stop-color="#444443"></stop><stop offset="1" stop-color="#626161"></stop></linearGradient></defs><path fill="url(#Ae)" d="M600 299l4-1c0 2-1 4-1 6 1 1 1 2 1 3l1 7c-1 1-2 3-3 4l-3 4-3 3h-3 1c-2-1-2-2-3-3l1-2c1-7 5-14 8-21z"></path><path d="M592 320s1 0 1 1l1 2v2c-2-1-2-2-3-3l1-2z" class="B"></path><path d="M599 322c0-2 0-3 1-4 1-4 2-7 4-11l1 7c-1 1-2 3-3 4l-3 4z" class="E"></path><path d="M431 726c1 1 2 1 3 1 0 1 1 3 2 3 3 5 5 10 8 16l10 20 1 4v1c-1 0-2-1-2-2l-2-2v-1l-8-13h-1 0c-3-4-5-8-8-12 1-1 1-3 1-4 0-2-1-3-2-4 0-2-1-4-2-7z" class="C"></path><path d="M654 690c2 0 4 1 6 1l-1 1 2 2c0 2 4 4 5 7 3 3 7 6 10 10-1 1-2 1-3 1-3 0-6-2-8-4-5-3-9-9-14-12v-2c-1-2-3-2-4-3h1 5l1-1z" class="W"></path><path d="M654 690c2 0 4 1 6 1l-1 1 2 2c0 2 4 4 5 7 3 3 7 6 10 10-1 1-2 1-3 1-3 0-6-2-8-4l1-1v-5c-3-5-7-10-13-11l1-1z" class="L"></path><path d="M666 702l6 9 1 1c-3 0-6-2-8-4l1-1v-5z" class="G"></path><defs><linearGradient id="Af" x1="760.79" y1="541.512" x2="766.917" y2="524.148" xlink:href="#B"><stop offset="0" stop-color="#c7c7c5"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#Af)" d="M768 525c6 1 15 3 18 8 1 1 2 2 2 3l1 7h-1l-1-2v-1c-1-3-3-5-5-7-7-5-17-6-25-4-9 2-16 6-24 11v-1l3-3v-1c4-5 12-8 18-9 5-1 10-1 14-1z"></path><defs><linearGradient id="Ag" x1="733.905" y1="493.194" x2="748.057" y2="491.81" xlink:href="#B"><stop offset="0" stop-color="#4c4c4b"></stop><stop offset="1" stop-color="#717070"></stop></linearGradient></defs><path fill="url(#Ag)" d="M743 468h0v5h1 0c1 1 1 2 1 3h0c1-1 1-2 0-4l1-1c0 2 0 4 1 6v1h0v-4c0 2 0 5 1 7-1 3-1 5-1 8l-1 1v1 1 3c-1 3-3 6-4 9-1 1-2 3-2 4-2 2-4 3-6 4l4-11c1-3 2-7 2-11l1-6 2-16z"></path><path d="M740 490l1-6c1 3 1 6 0 9v-1l-1-2z" class="S"></path><defs><linearGradient id="Ah" x1="612.821" y1="268.623" x2="584.178" y2="269.352" xlink:href="#B"><stop offset="0" stop-color="#616163"></stop><stop offset="1" stop-color="#888784"></stop></linearGradient></defs><path fill="url(#Ah)" d="M614 251l12-2c-18 10-33 24-44 41 0-3 2-4 3-7v-3c1-2 3-5 4-7 7-8 16-16 25-22z"></path><defs><linearGradient id="Ai" x1="298.366" y1="509.086" x2="302.515" y2="485.219" xlink:href="#B"><stop offset="0" stop-color="#2d2d2c"></stop><stop offset="1" stop-color="#595858"></stop></linearGradient></defs><path fill="url(#Ai)" d="M289 480l1 1h1v1c1 1 2 3 2 4 2-1 2-1 3-2v-1-1l2 5 3 8c2 2 2 3 4 5h1l2 5h-2-2c0 3 4 5 5 6l2 1h-4c-3-1-4-3-6-5h-2c-1-1-2-4-3-5l-4-10c0-2-1-4-2-6l-1-6z"></path><path d="M301 495c2 2 2 3 4 5h1l2 5h-2 0c-3-3-4-6-5-10z" class="E"></path><path d="M289 480l1 1h1c1 5 3 10 5 15 2 3 3 7 5 11h-2c-1-1-2-4-3-5l-4-10c0-2-1-4-2-6l-1-6z" class="I"></path><path d="M696 593h0c3-2 7-2 10-2 0 1-2 3-3 4-1 3-2 6-1 9l3 3-7 1h0-1v-4l-1-1-12 5 2-2c1-1 2-1 2-2l-1-1c-2 1-5 3-7 2 2-2 4-3 6-5 3-2 6-5 10-7z" class="B"></path><path d="M698 608l1-1v-7l3-6 1 1c-1 3-2 6-1 9l3 3-7 1h0z" class="X"></path><path d="M696 593l1 1-2 2h1l2-1c0 1 0 1-1 1-1 1-2 2-4 3s-4 3-6 4h0c-2 1-5 3-7 2 2-2 4-3 6-5 3-2 6-5 10-7z" class="P"></path><defs><linearGradient id="Aj" x1="188.469" y1="312.149" x2="212.85" y2="294.701" xlink:href="#B"><stop offset="0" stop-color="#9f9e9d"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#Aj)" d="M184 306c3-4 6-7 10-8 8-3 17-2 24 2 0 1 0 2 1 3 0 1 1 1 1 2v1c-5-5-11-6-18-6-6 1-11 4-15 8-3 4-5 8-5 13 1 2 2 5 4 7h-1c-2-2-3-5-4-8-2-1-2-2-2-4 1-4 2-7 5-10z"></path><path d="M179 316c1-4 2-7 5-10 0 4-2 10-3 14-2-1-2-2-2-4z" class="E"></path><path d="M594 471c1 2 2 3 3 5 0 1 0 1-1 3h-1l-1-1-1-1c-1 0-2-1-2-2 0 2 3 3 2 6v1l-1 1c0 1-1 2-1 3v1 1h-1l-1 4v2l-1 1h0c-1 0-3-2-5-3-1-1 1 0-1-1-1-1-4-4-4-5l-1-1 1-1h0l1-2v-2-1-1c1-1 3-1 5-1h2 2 1 1c-1-2-1-3-2-5l3 2c1 1 3 2 4 3h0 1c0-2-1-3-2-4v-1-1z" class="K"></path><path d="M579 478c1-1 3-1 5-1-2 2-3 2-4 4l-1-1v-1-1z" class="O"></path><path d="M579 480l1 1 2 5h-2l-1-4v-2z" class="J"></path><path d="M579 482l1 4h2c1 1 2 2 2 3l1 2h0c-3-1-5-4-8-6l1-1h0l1-2z" class="F"></path><path d="M764 593s1-1 1-2c0 6-3 9-6 13-2 2-4 3-6 5h-1c-1 1-3 1-4 1h-4-5c-2 0-5 0-7 1s-3 1-5 1h-6v-1l2-1 1 1c1 0 2-1 3-2h1c1-1 5-1 6-2 12-2 21-6 30-14z" class="U"></path><defs><linearGradient id="Ak" x1="751.031" y1="602.096" x2="752.555" y2="608.254" xlink:href="#B"><stop offset="0" stop-color="#646463"></stop><stop offset="1" stop-color="#858484"></stop></linearGradient></defs><path fill="url(#Ak)" d="M764 593s1-1 1-2c0 6-3 9-6 13-2 2-4 3-6 5h-1c-1 1-3 1-4 1h-4-5c6-2 12-4 17-8 2-1 7-6 8-9z"></path><path d="M841 99c5 1 9 2 13 5 4 5 4 9 4 15 0 7-5 15-10 20h-3l1-5c4-3 7-9 8-15 0-4-1-9-4-12-3-2-6-3-9-3h-1c0-2 1-3 1-5z" class="g"></path><path d="M745 548l1 1c-5 2-11 4-15 8h2c0 2-2 2-1 3 1 0 1-1 2-1h1l1 1c-1 1-2 1-3 2l-1 1v1h0c-1 1-3 1-5 1l-7 3c-2 0-4 1-6 2h0l-3 1v1h0c-1 2-1 3-3 4-1 0-2 0-3 1 3-4 5-9 9-11 2-4 5-7 8-9h3c3-1 5-3 8-4 4-2 8-3 12-5z" class="c"></path><path d="M714 566h1c0-1 1-1 2-2 2-1 4-3 6-3 1 0 2-1 3-1h1 1c-1 1-1 0-2 1l-2 2h2c2-1 3-1 5-1 1 0 1 0 1 1v1h0c-1 1-3 1-5 1l-7 3c-2 0-4 1-6 2h0l-3 1v1h0c-1 2-1 3-3 4-1 0-2 0-3 1 3-4 5-9 9-11z" class="J"></path><path d="M726 563c2-1 3-1 5-1 1 0 1 0 1 1v1h0c-1 1-3 1-5 1l-7 3c-2 0-4 1-6 2h0l-3 1 1-1c0-2 3-2 5-3 1-1 3-2 5-2 2-1 3-1 4-2z" class="O"></path><path d="M395 687h5c2 1 3 1 4 2 2 0 3 2 4 3v1l6 4v1l1 1h0-1c-1-1-1-1-3-1 0 0-2-1-3-1-3-1-7-3-10-4h-11 0c-2 1-4 1-5 1-2 1-3 4-6 5v-1c0-2 1-5 2-6 1-2 4-4 6-5 4 0 7 0 10 1l1-1z" class="g"></path><path d="M395 687h5c2 1 3 1 4 2 2 0 3 2 4 3v1c-5-2-9-4-14-5l1-1z" class="b"></path><defs><linearGradient id="Al" x1="711.977" y1="507.889" x2="720.604" y2="495.889" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#5a5958"></stop></linearGradient></defs><path fill="url(#Al)" d="M713 484v1c0 1 0 1 1 2v-3c1 1 1 2 1 3 1 1 2 1 4 1l1 4 1 7v9h0c-1 1-1 2-1 3h2v1c-2 1-3 1-4 1-2 0-2-1-3-2 0-1 0-2-1-3-1-3-1-6-2-9v-3-1h0c-2-3-2-6-3-9h0l2-2h2z"></path><path d="M713 484v1c-2 2-1 7-1 10h0c-2-3-2-6-3-9h0l2-2h2z" class="H"></path><path d="M715 487c1 1 2 1 4 1l1 4 1 7h-3c-2-2-2-6-3-9v-3z" class="Y"></path><path d="M715 487c1 1 2 1 4 1l1 4c0 1 0 1-1 2l-2-1-2-3v-3z" class="H"></path><path d="M837 319l2-4c1 1 1 3 2 4v6l1 1c-1 2-3 4-5 7h-1l-3 4c-1 0-1 1-2 2 0 0-2 3-3 3l-1-1v1c-1 1-2 1-4 2l-9 10-1-2c2-1 3-2 4-4s2-3 3-4l1-1h-2l16-19c0-1 0-1 1-2s1-2 1-3z" class="D"></path><path d="M823 344c1-3 9-10 12-12l1 1-3 4c-1 0-1 1-2 2 0 0-2 3-3 3l-1-1v1c-1 1-2 1-4 2z" class="S"></path><path d="M819 343l16-19c-1 6-14 21-18 24 1-2 2-3 3-4l1-1h-2z" class="U"></path><path d="M488 180l-2 3h0l1 2 1 1c2 2 7 5 8 8 1 4 5 5 7 9v1h0l-3-2h-2c0 1 1 2 1 3l1 1c1 1 2 2 2 4h-1c-3-1-7-7-9-10-5-5-10-9-15-14l11-6z" class="B"></path><defs><linearGradient id="Am" x1="725.118" y1="482.589" x2="738.272" y2="476.8" xlink:href="#B"><stop offset="0" stop-color="#7a7a7a"></stop><stop offset="1" stop-color="#aeacac"></stop></linearGradient></defs><path fill="url(#Am)" d="M725 450c1 5 6 8 9 12h0c1 3 2 5 3 9-1 7-1 14-1 21 0 4-1 7-3 10h0c-1-1-1-2-1-3 1-8 2-18 0-26h0c-1-3-2-5-4-8l-4-6 1-1c-1-3-1-5 0-8z"></path><path d="M725 450c1 5 6 8 9 12h0c1 3 2 5 3 9l-2-2c0-1 0 0-1-1 0-1-1-2-2-3 0-2-1-3-2-5h-1c0 2 0 3-1 5l-4-6 1-1c-1-3-1-5 0-8z" class="M"></path><path d="M703 502c-2-9-4-18-2-27 1-3 4-9 7-11l2 2 1 1c-1 2-2 5-3 8 0 4 0 7 1 11h0c-1 2-1 3-1 4l-1 1-1-1v7c0 3 0 3-2 5h-1 0z" class="T"></path><path d="M710 466l1 1c-1 2-2 5-3 8 0 4 0 7 1 11h0c-1 2-1 3-1 4l-1 1-1-1v-6c0-6 2-13 4-18z" class="E"></path><defs><linearGradient id="An" x1="760.084" y1="590.743" x2="763.609" y2="618.792" xlink:href="#B"><stop offset="0" stop-color="#212020"></stop><stop offset="1" stop-color="#464645"></stop></linearGradient></defs><path fill="url(#An)" d="M766 589l6 1c-1 7-10 26-16 30v-1l-2 1-1-2v-6h-2c1-1 1-2 1-3h1c2-2 4-3 6-5 3-4 6-7 6-13 0 0 0-1 1-2z"></path><path d="M752 609h1c2-2 4-3 6-5-1 3-2 4-3 7-1 1-1 3-2 5h0l-1 2v-6h-2c1-1 1-2 1-3z" class="J"></path><path d="M813 294l8 1c2 0 3 0 4 1 4 3 7 5 10 9 2 4 2 9 2 14 0 1 0 2-1 3 1-3 1-5 0-7 0-5-3-10-7-13s-10-4-16-3-10 5-14 10c-2 1-3 3-4 6-1 1-2 3-2 5h-1l1-1 2-9c1-1 3-6 3-7-2 1-4 4-6 5h0c2-5 8-9 13-11l8-3z" class="U"></path><defs><linearGradient id="Ao" x1="434.962" y1="254.772" x2="458.308" y2="258.268" xlink:href="#B"><stop offset="0" stop-color="#2a2b2a"></stop><stop offset="1" stop-color="#575454"></stop></linearGradient></defs><path fill="url(#Ao)" d="M436 253h0v-1h1l-1-1c-1-1-1-2-2-3h-1v-1l2-1h1l1 1-2-2 1-1c0-1 1-1 2 0 1 0 1 1 2 1s1 1 2 1c0 0 1 0 2 1v1h1c2 1 3 2 4 4s4 6 6 7c1 1 2 1 3 1h0l1 1c-1 1-1 1 0 2-1 3-1 5-2 8l-2-4-1 1c-2-3-4-5-6-8-2-1-3-2-4-3-2-1-3-2-4-4l-2-1-2 1z"></path><path d="M438 252c-1-1-1-2-2-3h1c1 0 2 1 3 1h0v1 2l-2-1z" class="C"></path><path d="M447 255h1l5 5c-2 0-2-1-3 0l-2-2c0-1-1-2-1-3z" class="O"></path><path d="M440 250h1l6 5c0 1 1 2 1 3l2 2 5 7-1 1c-2-3-4-5-6-8-2-1-3-2-4-3-2-1-3-2-4-4v-2-1z" class="K"></path><path d="M440 251l8 9c-2-1-3-2-4-3-2-1-3-2-4-4v-2z" class="F"></path><path d="M323 479h1c1 1 1 3 2 3h0c1 2 0 4 0 6-3 5-7 11-6 17 0 2 1 3 2 4v2 5 1l-1 1-1-2c-2-2-4-3-6-5 1-2 0-6 0-9v-2-4l3-6 3-4c2-2 3-4 3-7z" class="B"></path><path d="M320 486h0c0 2-1 3-2 4-3 5-3 10-1 15v1c1 3 2 3 3 5s1 4 2 5v1l-1 1-1-2c-2-2-4-3-6-5 1-2 0-6 0-9v-2-4l3-6 3-4z" class="D"></path><defs><linearGradient id="Ap" x1="427.531" y1="326.452" x2="416.609" y2="327.374" xlink:href="#B"><stop offset="0" stop-color="#848382"></stop><stop offset="1" stop-color="#9d9c9b"></stop></linearGradient></defs><path fill="url(#Ap)" d="M418 322l5-8 5 10c1 2 1 4 3 6-1 1-1 2-2 3 0 2-1 4-3 5l-1 1c-2 0-3 0-4-1-2 0-3-2-4-3-2-4 0-9 1-13z"></path><path d="M421 338c1-1 1-1 1-3h0l2-2 2 2-1 2 1 1-1 1c-2 0-3 0-4-1z" class="P"></path><path d="M672 580l1-3h1 1c0-1 0-1 1-2 0 1 1 1 1 2h1 1c1 1 1 1 3 2l-6 8-14 16-4 1c1-2 2-3 4-5l-1-1 1-1-1-1 5-6-1-1c-1 0-1 0-1 1l-3 3h-1l4-6c1-1 2-4 3-5h1c2-2 2-4 3-6 0 1 1 1 0 2v1l1 1z" class="L"></path><path d="M672 580l1-3h1 1c0-1 0-1 1-2 0 1 1 1 1 2h1 1l-6 6c-3 5-7 10-11 14l-1-1 5-6-1-1c-1 0-1 0-1 1l-3 3h-1l4-6c1-1 2-4 3-5h1c2-2 2-4 3-6 0 1 1 1 0 2v1l1 1z" class="B"></path><path d="M667 582h1c2-2 2-4 3-6 0 1 1 1 0 2v1l1 1c0 2-2 4-4 6h0c-1 1-1 1-1 2h-1v-1h-1-1c1-1 2-4 3-5z" class="H"></path><path d="M334 563l20 28 9 10c0 1 2 2 3 3 0 2 1 3 2 4l1 1-2 1-2-2v-1l-5-4h-1-1c-2-2-4-5-5-7l-12-14-3-5-6-11c1-1 1-2 2-3z" class="M"></path><defs><linearGradient id="Aq" x1="387.343" y1="666.792" x2="392.685" y2="645.092" xlink:href="#B"><stop offset="0" stop-color="#101211"></stop><stop offset="1" stop-color="#302e2f"></stop></linearGradient></defs><path fill="url(#Aq)" d="M373 642l-1-1v-1l2 1 11 6h0l-3-3c-1 0-2-1-2-2l7 5 4 3 5 6 2-2 2 1c1 2 4 4 5 6 1 1 2 2 2 3 3 3 5 6 7 9l-1 1-2-3-1 2h-1-1c-3-5-9-11-13-14-1-1-3-2-4-3l-2-1c-5-3-10-8-15-12l-1-1z"></path><defs><linearGradient id="Ar" x1="409.48" y1="666.294" x2="396.734" y2="655.943" xlink:href="#B"><stop offset="0" stop-color="#393837"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#Ar)" d="M398 654l2 1c1 2 4 4 5 6 1 1 2 2 2 3 3 3 5 6 7 9l-1 1-2-3-15-15 2-2z"></path><path d="M562 474c0-1 0-2 1-2v-1l1-1v-2c1-2 1-1 2-2l2 2c0 1 0 1-1 2v-2h-1v1c0 1 1 1 2 1 1-1 2-2 4-1h2l1 1h1c0 1-2 1-2 2l-1-1h0c-2 1-3 1-4 3h0c-1 1-2 1-2 3v1c1 4 2 7 2 11h1l2 4h0l6 13v1h-1l-5-8-1 1-2-3h-1c-1-1-2-2-2-3h0l-1-1-5-11c0-1 0-3 1-4s1-2 1-4h0z" class="I"></path><path d="M567 486c-1-2-2-5-2-7-1-2 1-4 2-5s4-4 6-4v1c-2 1-3 1-4 3h0c-1 1-2 1-2 3v1 5 3z" class="J"></path><path d="M567 478c1 4 2 7 2 11h1l2 4h0l6 13v1h-1l-5-8c-2-4-5-9-5-13v-3-5z" class="O"></path><path d="M562 474c0-1 0-2 1-2v-1l1-1v-2c1-2 1-1 2-2l2 2c0 1 0 1-1 2v-2h-1v1c0 1 1 1 2 1v1c0 1-1 2-2 3-3 2-3 5-3 8v3h1c0 5 3 8 5 12h-1c-1-1-2-2-2-3h0l-1-1-5-11c0-1 0-3 1-4s1-2 1-4h0z" class="D"></path><path d="M736 560h1l3 2c-1 1-2 2-1 4 0 1 1 2 2 4 1 0 1 1 1 1 1 1 2 2 3 2l1-1 9 14c-3-3-5-5-8-6l-2-1c-2 0-4-1-5-1l-1-3-4-1c-1-1-3-1-5-1h-1l-1-1c-1-1-3-1-4-1v-1c-2-1-3-2-4-2l7-3c2 0 4 0 5-1h0v-1l1-1c1-1 2-1 3-2z" class="T"></path><path d="M720 568l7-3-2 3c1 1 1 1 2 1s1 0 2 1h2v1 1h1l-2 1h-1l-1-1c-1-1-3-1-4-1v-1c-2-1-3-2-4-2z" class="G"></path><path d="M737 560l3 2c-1 1-2 2-1 4 0 1 1 2 2 4 1 0 1 1 1 1-2 0-4-1-6-3h-6l-1-1c2 0 3 0 4-1 2-1 3 1 4 0-1 0-1-1-2-1v-1c0-2 1-3 2-4z" class="N"></path><path d="M739 575h2l-8-5 1-1c1 0 2 1 3 1h1c3 1 5 4 8 6 0 1-1 2-1 3-2 0-4-1-5-1l-1-3z" class="M"></path><path d="M297 636h1v-2c1 0 1 1 2 2 0 0-1 0-1 1v1l25 2c4 0 9 1 13 1h4v1h-6l7 1v1h-1-32-1l-9 1h-4c-2-2-3-2-5-3h-1l-1-1h10v-1l-4-1c1-1 2-1 3-2h0v-1z" class="E"></path><path d="M290 642c3 0 6-1 9 0s6 1 9 1l3 1h0-2-1l-9 1h-4c-2-2-3-2-5-3z" class="B"></path><defs><linearGradient id="As" x1="633.104" y1="321.647" x2="606.252" y2="288.789" xlink:href="#B"><stop offset="0" stop-color="#575556"></stop><stop offset="1" stop-color="#777776"></stop></linearGradient></defs><path fill="url(#As)" d="M612 283l3-2h0l1 1c-2 3 1 9 2 12s3 6 4 8l-1 2 10 24h0c-1 0-2-1-2-2h-2c-3-6-5-14-9-19-1-3-3-5-5-8-2-4-3-8-5-12l4-4z"></path><path d="M612 283l3-2h0l1 1c-2 3 1 9 2 12s3 6 4 8l-1 2c-2-4-7-19-9-21zm16-11l1-1c1-1 1-1 3-1 4 6 7 13 10 20 3 6 6 11 7 17h0l-1-1v-2l-1-1h0c-1 1-2 2-3 2h0v2l1 1 1 3h-1c0-2-1-3-1-5l-2 1c-4-12-8-23-15-34l1-1z" class="J"></path><path d="M628 272c4 4 6 10 8 15 3 6 5 12 8 19l-2 1c-4-12-8-23-15-34l1-1z" class="I"></path><path d="M453 742c4 5 6 12 9 18l17 40c3 6 6 12 8 18l-1 1c-1-4-2-8-4-11-1-1-1-1-1-2-1-1-1-1-2-1-2-3-3-6-4-9h-1l-22-51c0-1 1-2 1-3z" class="C"></path><path d="M383 681l12 3c1 1 4 2 6 3h-1-5c-9-2-19-3-28-1-8 3-15 10-18 18l-1 2h-1c-1 1-2 2-2 4l-1 1-1-3 1-4c2-8 9-15 16-18l8-3 7-2h8z" class="d"></path><path d="M427 360c-1 1 0 1-1 2l-9 13-1-2c-1-2-1-4-2-6 0-5 2-13 5-16 0 1 1 2 2 2 2 1 3 0 5-1 1 0 2 0 3-1 3-2 4-2 8-2h2c1 1 1 2 1 4h0c1 1 1 4 1 6h-1c0 1 0 2-1 3h0c-1-1 0-9 0-11-2-1-3-1-4-1v1h-3c-1 1-1 1-1 2s0 2-1 2h0v-1h-1v1c0 2-1 3-2 5z" class="K"></path><path d="M777 478l-3 6h1 0c5-1 11-8 14-11l1 1c1 1 2 3 5 3-1 1-1 1-1 2l-6 4c-2 1-4 2-6 5v-2c-2 1-3 3-6 4-1 1-5 4-6 5l-2 1-1-1-4 3c0-1 0-1 1-1 0-1 1-3 2-4v-2c0-2 1-6 3-7 1-2 1-4 3-7v4c1 0 2-1 3-1 1-1 1-2 2-2z" class="E"></path><path d="M768 494l2-2c2-1 3-1 5-2h1c-1 1-5 4-6 5l-2 1-1-1 1-1z" class="O"></path><path d="M764 497c0-1 1-3 2-4v-2c0-2 1-6 3-7v1h2c0 1 0 1-1 3-1 1-2 3-3 5l1 1-1 1-4 3c0-1 0-1 1-1z" class="F"></path><defs><linearGradient id="At" x1="783.563" y1="485.556" x2="788.94" y2="474.97" xlink:href="#B"><stop offset="0" stop-color="#747271"></stop><stop offset="1" stop-color="#9f9e9d"></stop></linearGradient></defs><path fill="url(#At)" d="M790 474c1 1 2 3 5 3-1 1-1 1-1 2l-6 4c-2 1-4 2-6 5v-2-1c-1 0-1 1-2 1l-4 2c1-1 3-3 4-5 2-2 7-8 10-9z"></path><path d="M343 708l1 3 1-1c0-2 1-3 2-4h1c-2 6-1 11 2 17 1 2 2 3 3 5 3 4 8 7 11 12-3-2-5-3-7-5l-6-6c-6-2-14 0-19-4-2-1-1-4-3-5l-2-2v-4h1c1 1 2 3 3 5l2-2h1v-2l1 1h1c1 0 1 1 1 2 1 2 2 2 4 3l2 1c1 0 2 1 3 1l-2-4c0-4-1-7-1-11z" class="V"></path><path d="M334 715l1 1h1c1 0 1 1 1 2 1 2 2 2 4 3l2 1c1 0 2 1 3 1v2h0c-4 0-9-1-13-3-1 0-1 0-2-1v-2l2-2h1v-2z" class="Y"></path><path d="M331 719l2-2h1c-1 2-1 4-1 5h0c-1 0-1 0-2-1v-2z" class="B"></path><defs><linearGradient id="Au" x1="465.841" y1="428.678" x2="475.135" y2="427.992" xlink:href="#B"><stop offset="0" stop-color="#4b4a4a"></stop><stop offset="1" stop-color="#636362"></stop></linearGradient></defs><path fill="url(#Au)" d="M463 410c3 0 2 0 4 1l-1 1c2 2 5 4 6 6l2 4 2 13c0 4 0 7 1 11 1 2 2 4 2 7-2-2-3-4-5-5-2 0-3 1-4 1-2-2-1-2-2-4 0-5 1-10 1-15-1-7-3-14-6-20z"></path><path d="M468 445c1-1 1-3 2-4v3c0 2 0 2 1 4h1l1-1s1 0 1 1h0c-2 0-3 1-4 1-2-2-1-2-2-4z" class="C"></path><path d="M489 507v-14c1-3 0-6 1-9v10c-1 4-1 8 0 12l1-1h0 2 0l2-1 1-1c1 1 1 1 1 2v4c0 1-1 2-1 3v3 2c0 1 0 1-1 1v2 1c-1 1-1 2 0 3h-1v2 2 1l-1 1c0 1 0 1-1 3l1-9h0l-1-2-2 3c-1 5-2 11-4 16h0c-2 0-2 2-2 3h0v-1-1-2c1-1 1-1 1-2h0c1-1 1-1 0-2l-1 3c-1 1-1 1-1 2s0 1-1 2h-1l1-1v-1-1h0c1-2 1-3 2-4h0c0-1 1-2 1-3v-1-1l-1 2v1c-1 0-1 1-2 2v-1c0-1 0-1 1-2h0c2-2 3-5 3-7 1-4 1-7 2-10s1-6 1-9z" class="R"></path><path d="M490 519v-4c0-3 0-7 1-9 2 1 0 1 1 3v1l1 2c-1 2-1 3-2 5 0 0-1 1-1 2z" class="D"></path><path d="M492 510c1-3 1-3 3-5h1c0 6-1 13-3 19h0l-1-2-2 3v-6c0-1 1-2 1-2 1-2 1-3 2-5l-1-2z" class="P"></path><path d="M490 519c0-1 1-2 1-2 1-2 1-3 2-5 0 4-1 7-1 10l-2 3v-6z" class="J"></path><path d="M683 593h0v1c0 1-1 2-2 2-1 1 1 0-1 1 0 1-1 1-1 2l-1 2h0l-3 3v2l1-1h1v-1l3-3c0-1 1-2 2-3h1l5-5c0 1 0 2 1 3l-4 3 1 1c-2 2-4 3-6 5 2 1 5-1 7-2l1 1c0 1-1 1-2 2l-2 2-4 3-11 10-2 1v-1c1-1 2-3 4-4l-1-1 4-4 1-1 1-1c1 0 1 0 2-1h-4l2-2v-1c-2 1-2 2-4 3s-3 3-4 4v1c1 0 1-1 2-1h0l-3 3v-1c-2 2-4 3-5 5-1 1-3 5-4 5-1 1-2 1-3 1l-4 3c0-2 3-6 4-7l1-2 6-9c1 1 2 1 3 1h0 3c3-3 6-6 8-10 1-3 4-6 7-9z" class="L"></path><path d="M656 620c2 0 3-1 4-1-1 2-3 5-5 7l-4 3c0-2 3-6 4-7l1-2z" class="c"></path><path d="M656 620l6-9c1 1 2 1 3 1h0l-5 7c-1 0-2 1-4 1z" class="Z"></path><path d="M683 598l5-5c0 1 0 2 1 3l-4 3-7 5 5-6z" class="S"></path><path d="M687 603l1 1c0 1-1 1-2 2l-2 2-4 3c0-2 1-3 1-4h-3v-1l2-1c2 1 5-1 7-2z" class="F"></path><defs><linearGradient id="Av" x1="613.209" y1="381.259" x2="605.44" y2="385.429" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#575756"></stop></linearGradient></defs><path fill="url(#Av)" d="M597 370h1-1v-5h1c1 0 2 0 4 1l2 5c4 7 9 14 14 21 1 1 6 7 6 8v2c-1 1-1 1-1 2-4-3-8-7-11-10-6-7-12-15-15-24z"></path><path d="M598 365c1 0 2 0 4 1l2 5-2-1c-1-1-3-3-4-5z" class="C"></path><path d="M796 455h1c3 0 5-3 8 0 1 1 1 2 1 3-7 4-12 8-17 15-3 3-9 10-14 11h0-1l3-6c-1 0-1 1-2 2-1 0-2 1-3 1v-4l3-6c1 2 1 3 2 4 2 0 2-1 3-2l1-1 8-8h1 0c1-2 5-4 5-6l1-3z" class="V"></path><path d="M789 464h1l-9 10c-1 1-3 4-4 4s-1 1-2 2c-1 0-2 1-3 1v-4l3-6c1 2 1 3 2 4 2 0 2-1 3-2l1-1 8-8z" class="Y"></path><defs><linearGradient id="Aw" x1="242.038" y1="504.934" x2="229.921" y2="537.175" xlink:href="#B"><stop offset="0" stop-color="#cfcccb"></stop><stop offset="1" stop-color="#f1f1f1"></stop></linearGradient></defs><path fill="url(#Aw)" d="M222 532c-5 0-11 0-15-3h1c1-1 7-1 9-1s3-1 5-2c10-3 19-10 30-13h1c4-1 7-1 11-1 1 0 2 0 3 1-2 1-7 2-10 2-10 4-18 9-27 14l-8 3z"></path><path d="M253 632h0v-2c-1 0-1 0-1-1l2 1v-1c6 6 13 8 22 8 2 1 4 1 6 0h2c0-1 1-2 2-2h0v1c1 1 7 1 9 1l2-1v1h0c-1 1-2 1-3 2l4 1v1h-10l1 1-14 1-3-1c-5 1-9 1-14-1-1-2-3-2-4-3-1-2-2-4-2-6h1z" class="G"></path><path d="M294 639h0c-2 1-5 0-6-1-1 0-2 0-3-1h-1l2-1c1 1 7 1 9 1l2-1v1h0c-1 1-2 1-3 2z" class="H"></path><path d="M273 641h15l1 1-14 1-3-1 1-1z" class="K"></path><path d="M252 632h1c3 5 8 7 13 8l7 1-1 1c-5 1-9 1-14-1-1-2-3-2-4-3-1-2-2-4-2-6z" class="L"></path><defs><linearGradient id="Ax" x1="311.588" y1="549.502" x2="280.997" y2="564.131" xlink:href="#B"><stop offset="0" stop-color="#353534"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#Ax)" d="M313 566h2v-1c-2-2-4-5-7-6-4-4-12-5-17-6-6-1-12-3-18-2h-1l1-2c2-2 4-3 7-3 6 0 15 0 21 3 8 3 14 10 20 17-1 0-2 0-4 1h0c-1 0-2-1-4-1h0z"></path><defs><linearGradient id="Ay" x1="599.811" y1="267.39" x2="591.937" y2="261.419" xlink:href="#B"><stop offset="0" stop-color="#131315"></stop><stop offset="1" stop-color="#323230"></stop></linearGradient></defs><path fill="url(#Ay)" d="M612 249c1-1 3-1 4-2h0 1 1 0v1c-1 1-3 2-4 3-9 6-18 14-25 22l-1 1-1-1c-4 2-6 6-10 7 2-3 5-6 8-9l5-5h-2v-2-5l1-1 1 1c0 1 1 1 2 0 1-2 2-3 4-5l1-1h1 0c1-1 1-1 3-1l1 1-1 1c1 0 3 0 5-1h1l1-1 1-1h0l1-1s1 0 1-1h1 0z"></path><defs><linearGradient id="Az" x1="601.703" y1="251.887" x2="589.825" y2="264.799" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#5b5a5a"></stop></linearGradient></defs><path fill="url(#Az)" d="M598 253h0c1-1 1-1 3-1l1 1-1 1c1 0 3 0 5-1h1l1-1 1-1h0l1-1s1 0 1-1h1 0c-3 3-7 6-11 9-3 2-7 6-11 8h-2v-2-5l1-1 1 1c0 1 1 1 2 0 1-2 2-3 4-5l1-1h1z"></path><defs><linearGradient id="BA" x1="590.4" y1="783.211" x2="581.896" y2="766.386" xlink:href="#B"><stop offset="0" stop-color="#2d2e2d"></stop><stop offset="1" stop-color="#504e4e"></stop></linearGradient></defs><path fill="url(#BA)" d="M605 752c2 0 3 0 4-1 2-1 2-1 4-1-1 3-3 4-5 5l-1 1c-11 9-18 21-26 33 0-5 5-8 6-12-4 3-7 9-10 12-1 3-3 5-5 7 0 1-1 1-1 1h-1c2-4 5-8 7-12 5-8 9-16 15-24l1-1v1l1 1c1-1 2-2 3-4 2 0 4-2 7-3l1-3z"></path><path d="M605 752c2 0 3 0 4-1 2-1 2-1 4-1-1 3-3 4-5 5l-1 1v-1c-3 0-4 3-6 4 0-1 0 0 1-1 0-1 1-2 1-2l1-1 1-3z" class="C"></path><defs><linearGradient id="BB" x1="481.959" y1="142.349" x2="473.142" y2="137.728" xlink:href="#B"><stop offset="0" stop-color="#7d7c7c"></stop><stop offset="1" stop-color="#9e9c9b"></stop></linearGradient></defs><path fill="url(#BB)" d="M481 131l3-5h1c1 2 3 4 2 7v1c-1 4-3 7-6 9-4 3-6 4-9 9l1 7-1-2c-1 0-1-1-1-2v1c-2-1-3-2-5-2l1-5 1-1c0-3 3-4 3-7-1-2-1-2-1-3 1-1 2-2 2-3h-6-1c3 0 9 0 11-1 2 0 4-2 5-3z"></path><path d="M481 131l3-5h1c1 2 3 4 2 7v1c-1-1-1-3-1-5-1 2-1 6-2 8-1-1-1-1 0-2v-5c-1 0-2 0-3 1z" class="c"></path><path d="M468 148v3c1 0 2-1 3-1v3h0l1 2h0v-3l1 7-1-2c-1 0-1-1-1-2v1c-2-1-3-2-5-2l1-5 1-1z" class="P"></path><defs><linearGradient id="BC" x1="706.295" y1="657.547" x2="762.812" y2="630.208" xlink:href="#B"><stop offset="0" stop-color="#464849"></stop><stop offset="1" stop-color="#84807e"></stop></linearGradient></defs><path fill="url(#BC)" d="M702 642l31-1c3 1 6 0 9 1l9 1h18v1c0 1-2 3-3 3l-1 1-1-1c-5-1-12-1-18-1h-5l-2 2-1-1c1-1 1 0 2-1-14-2-28-1-42-1l1-1h0v-1c1-1 2-1 3-1z"></path><path d="M742 642l9 1h4c0 1 0 1-1 1-2 1-10 0-12-1v-1h0z" class="H"></path><path d="M228 309c1 2 2 4 4 5 1 2 1 4 1 6 1 2 0 4 0 6-1 8-3 16-7 22-1 2-3 4-5 6l-4-6c5-5 8-10 9-16v-2-11c1-2 0-3 0-5l2 1v-5-1z" class="K"></path><path d="M228 309c1 2 2 4 4 5 1 2 1 4 1 6h0c-1 0-1 0-1-1-1 0-1 1-1 2h-3c0 3 0 7-1 10v-1h-1v-11c1-2 0-3 0-5l2 1v-5-1z" class="J"></path><path d="M575 239h1c2 0 4-3 6-4 0 1 0 1-1 2l-1 1-1 1h3 0l-1-1 1-1h0c1 1 0 1 1 1l1-1h1c-1 1-2 2-1 3v-1c2-1 4-1 6-1 0 0 1-1 2-1h1c-1 2-5 4-7 5-3 3-7 6-10 10 0 0-2 1-2 2-3 4-7 9-12 10-2 0-2 0-3-1 1 0 1 0 2-1v-1h0c2 0 2 0 3-2 0-1-1-2-2-3v-2l1-1c2 0 2-1 3-3l6-6c1-2 2-4 3-5z" class="C"></path><path d="M570 255c2-3 5-5 8-8 1-1 6-5 8-5-3 3-7 6-10 10 0 0-2 1-2 2-3 4-7 9-12 10-2 0-2 0-3-1 1 0 1 0 2-1v-1h0c2 0 2 0 3-2 0-1-1-2-2-3 3-1 5-1 8-1z" class="M"></path><path d="M562 256c3-1 5-1 8-1-2 3-4 6-7 7-1 0-1 0-2-1 2 0 2 0 3-2 0-1-1-2-2-3z" class="L"></path><defs><linearGradient id="BD" x1="444.433" y1="687.597" x2="406.567" y2="664.903" xlink:href="#B"><stop offset="0" stop-color="#333334"></stop><stop offset="1" stop-color="#626260"></stop></linearGradient></defs><path fill="url(#BD)" d="M414 657v-5h0c1 1 2 2 3 2 1 1 2 1 2 2 1 2 2 2 3 3 3 17 11 33 20 47 1 1 0 1 0 2-2-1-2-3-4-4 0 1 1 2 1 3v1h1c1 1 1 2 2 4 1 0 1 1 2 2v1c1 0 1 1 1 2v1h0c-9-10-13-23-19-34-2-7-6-13-9-19 0-2-2-6-3-8z"></path><path d="M788 304l-2 1c-3 0-4 0-7-2-1 0-6 1-7 2-2 0-3 0-5-1-3-4-4-10-5-14-1-2-2-5-2-7 2 1 4 8 4 10v-2c-1-4-1-8 0-11l1 4c1 5 3 10 7 14 2 2 6 2 9 2 7 0 12-3 16-8 5-6 6-16 5-24l-1-4c2 3 2 7 2 11l1 6-1 1v2 1h-1v1 1c-1 0-1 1-1 2h0l-1 1-2 3c0 1-1 2-2 2l1 1h0l-1 1c-1 0-1 0-1 1l-2 1c-2 2-3 4-5 5z" class="H"></path><path d="M774 303c-2 0-3 1-5 0-3-2-4-6-4-9l6 5c1 2 1 2 3 3v1z" class="f"></path><path d="M771 299c2 1 5 2 7 2h2c4 1 9-2 13-4 7-6 9-13 10-22l1 6-1 1v2 1h-1v1 1c-1 0-1 1-1 2h0l-1 1-2 3c0 1-1 2-2 2l1 1h0l-1 1c-1 0-1 0-1 1l-2 1c-2 2-3 4-5 5-1-1-1-1 0-2v-1c-3 1-5 2-7 2-2-1-4-1-7 0v-1c-2-1-2-1-3-3z" class="L"></path><defs><linearGradient id="BE" x1="433.775" y1="767.763" x2="402.708" y2="761.318" xlink:href="#B"><stop offset="0" stop-color="#676666"></stop><stop offset="1" stop-color="#bebdbc"></stop></linearGradient></defs><path fill="url(#BE)" d="M412 753l23 19-1 1-1-1h-1v2l-1-1v-1h0c-1 2-2 1-2 3l2 2h0v1c-5-3-9-6-14-9 0-1-3-3-4-3l-10-7-2-2v-1c1 0 2-1 3-1 1-1 1 0 2-1h0 1l1-1h4z"></path><path d="M407 754c0 1-1 1-2 3v-1c-1 1-1 2-2 3l-2-2v-1c1 0 2-1 3-1 1-1 1 0 2-1h0 1z" class="N"></path><defs><linearGradient id="BF" x1="576.628" y1="758.729" x2="597.629" y2="755.389" xlink:href="#B"><stop offset="0" stop-color="#3a3937"></stop><stop offset="1" stop-color="#6b6a6b"></stop></linearGradient></defs><path fill="url(#BF)" d="M614 719c2 0 4-1 5 0s1 2 1 3h0c-4 0-9 0-13 3-2 3-3 5-4 8l-4 5-3 5v1c-1 1-2 4-3 5l-10 19-8 14h0v-2-1c1 0 2-1 2-2v-1h1v-1h0v-1l1-1c0-1 0-1-1-2 0 0-1 1-1 2s0 2-1 2v1c0 1 0 2-1 3h-1l-1 2c-1 1-1 3-2 4h-1c7-13 12-27 20-39 3-7 7-12 12-18 2-1 4-3 6-4l6-5z"></path><path d="M596 743v-1c0-2 1-3 2-5l1 1-3 5z" class="Y"></path><path d="M776 513l5 1c6 2 11 5 16 7 3 2 6 4 10 5 2 0 9 0 10 1-2 3-5 4-9 4l-1 1c-2 0-5 0-7-1-3 0-7-4-9-6v1l-17-8c-2-2-5-2-8-2 3-2 5 0 8-1l2-2z" class="V"></path><path d="M776 513l5 1-1 1v2h-7c6 2 13 4 18 8v1l-17-8c-2-2-5-2-8-2 3-2 5 0 8-1l2-2z" class="G"></path><path d="M621 649v1 1c1-1 2-2 2-3l2-2-1 2c0 3 0 6-2 8l-2 5h1c1-1 1 0 1 0-1 1-3 6-5 7l-4 7-18 27c-1 2-3 5-4 6-1 0-1 0-2 1v-1h-1 0c0-1 0-1 1-2 0-1 1-2 1-3h1v-1h1v-2l2-2v-1l2-2c8-11 13-25 20-37 2-3 3-6 5-9z" class="I"></path><path d="M624 648c0 3 0 6-2 8l-2 5h1c1-1 1 0 1 0-1 1-3 6-5 7h0c-2 1-5 7-7 8h0l14-28z" class="C"></path><path d="M402 336c0-1-1-1-1-1-2-5 1-13 3-17v-3h1v-1-2c1-1 1-2 1-2l1-1v-1-1l1-1v-2h1v-1h0v-1c1-1 1-2 1-2l1-1v-1-1l1-1v-2c1-1 1-1 1-2h1v-1c0-1 1-2 1-2v-2c1-1 1-1 1-2l2-4v-1-1l1 2 3 2 2 2c-3 2-5 8-6 11l-4 9-10 23v2l-1 1c0 1 0 3-1 5z" class="K"></path><path d="M419 281l3 2c0 1-1 1-1 2-3 9-7 18-12 27 0 2-2 6-3 7-1-1-1-2-1-3 1-3 12-33 14-35z" class="E"></path><path d="M559 270l5-3 2 6c1 4 3 10 7 12 1 1 2 1 4 1 0 1-1 2-1 3-2 2-3 6-4 8h-1c0 1 1 1 1 2h-1l-4-4c-3-2-8-9-10-12-1-4 1-9 2-13z" class="F"></path><path d="M572 297c-1-1-2-3-2-4v-4-3c-1 0-1-1-2-1-1-5-3-8-2-12 1 4 3 10 7 12 1 1 2 1 4 1 0 1-1 2-1 3-2 2-3 6-4 8z" class="E"></path><defs><linearGradient id="BG" x1="180.291" y1="123.283" x2="160.511" y2="109.662" xlink:href="#B"><stop offset="0" stop-color="#c3c2c1"></stop><stop offset="1" stop-color="#e9e7e7"></stop></linearGradient></defs><path fill="url(#BG)" d="M172 139h-2c-4-3-7-7-9-12-2-4-4-10-2-15v-1l1-2c2-4 6-7 11-9 3-1 7-1 10 1 4 2 6 5 7 9v4c-2 1-3 1-4 1l-1-1c0-1 1-3 0-4 0-2-2-4-4-5s-5-1-8 0-6 4-7 7c-2 4-1 10 1 14 1 4 4 6 7 9h0c0 1-1 3 0 4z"></path><path d="M309 515c1 0 2 0 3 1s1 3 1 4 0 1-1 2v3c-1 0-1 0-2-1l3 6c1 4 4 8 5 12l2 6-3-3-10-12-4-3c-2-1-4-4-5-6l-6-6v-1h4l3-1c3-1 6-1 10-1z" class="E"></path><path d="M292 518c2 0 3 1 4 2l3 2 3 4c2 2 4 4 5 7l-4-3c-2-1-4-4-5-6l-6-6z" class="L"></path><path d="M299 522l-1-1 2-1v1c1 0 3 1 4 1h0c2 2 4 5 7 7l2 1c1 4 4 8 5 12-2-1-3-3-4-5-1-1-2-3-4-4-2-4-4-6-8-7l-3-4z" class="B"></path><path d="M309 515c1 0 2 0 3 1s1 3 1 4 0 1-1 2v3c-1 0-1 0-2-1l3 6-2-1c-3-2-5-5-7-7h0c-1 0-3-1-4-1v-1l-2 1 1 1-3-2c-1-1-2-2-4-2v-1h4l3-1c3-1 6-1 10-1z" class="P"></path><path d="M292 517h4c1 1 2 1 2 3h-2c-1-1-2-2-4-2v-1z" class="c"></path><path d="M299 516c3-1 6-1 10-1-1 2-1 4-1 6-1-1-2-2-3-2-1 1-1 0-1 1-2-1-4-2-5-4z" class="Z"></path><path d="M309 515c1 0 2 0 3 1s1 3 1 4 0 1-1 2v3c-1 0-1 0-2-1s-2-2-2-3c0-2 0-4 1-6z" class="G"></path><defs><linearGradient id="BH" x1="411.183" y1="305.208" x2="393.714" y2="285.977" xlink:href="#B"><stop offset="0" stop-color="#232424"></stop><stop offset="1" stop-color="#5c5b5a"></stop></linearGradient></defs><path fill="url(#BH)" d="M408 272l5 3-16 44c0 1 0 0-1 1h-1c-1-3-2-6-2-9v-1c2-7 4-14 7-21l8-17z"></path><path d="M684 608l12-5 1 1v4h1 0l7-1c3 0 7 2 10 3-2 0-8 1-9 0l-1 1h-3c1 1 1 1 0 3h0 1l-2 1h-3c-7 1-14 3-20 5-1 0-2 0-2-1h-2c-2 1-3 2-5 2l11-10 4-3z" class="S"></path><path d="M698 611h4c1 1 1 1 0 3h0 1l-2 1h-3c-2 0-4 0-5-1v-1c2-1 4-1 5-2z" class="c"></path><path d="M693 613c2-1 4-1 5-2l-2 2 2 1 3 1h-3c-2 0-4 0-5-1v-1z" class="B"></path><defs><linearGradient id="BI" x1="682.966" y1="620.352" x2="689.853" y2="610.204" xlink:href="#B"><stop offset="0" stop-color="#424343"></stop><stop offset="1" stop-color="#5e5c5c"></stop></linearGradient></defs><path fill="url(#BI)" d="M676 619c5-2 10-5 16-7l1 1v1c1 1 3 1 5 1-7 1-14 3-20 5-1 0-2 0-2-1z"></path><path d="M684 608l12-5 1 1v4c-8 3-16 6-23 11-2 1-3 2-5 2l11-10 4-3z" class="d"></path><path d="M235 490c-5-3-12-5-18-3h-1c-4-3-7-5-10-9-2-3-4-5-4-8 0 0 0-1 1-1 0 0 4 3 6 4 5 1 11 1 16 0 1 0 3 1 4 1v1l2 2c0 2 1 2 1 4-7-2-15-3-22-5 5 3 10 4 15 6 3 1 5 2 8 3 1 0 3 1 4 2-1 1-1 2-2 3z" class="a"></path><defs><linearGradient id="BJ" x1="508.999" y1="360.926" x2="496.44" y2="356.238" xlink:href="#B"><stop offset="0" stop-color="#3e3a3a"></stop><stop offset="1" stop-color="#595c5b"></stop></linearGradient></defs><path fill="url(#BJ)" d="M501 311c1 2 0 5 1 7 1 1 1 3 1 5v-4c1 4 0 36 1 37v35-1c-1-3 0-10-2-11v6l-1 1v3h0v-4-3c-1-1 0-3 0-4l-1-11c0-2 0-4-1-5v9l-1 1v-26c0 1 0 3 1 4-1-2 0-4 0-7 0-6 1-13 2-20-1-3 0-9 0-12z"></path><path d="M501 323c1 7 0 15 0 22v31c1 3 0 7 0 10v3h0v-4-3c-1-1 0-3 0-4l-1-11c0-2 0-4-1-5v9l-1 1v-26c0 1 0 3 1 4-1-2 0-4 0-7 0-6 1-13 2-20z" class="P"></path><path d="M237 487c-1-1-3-2-4-2l-8-3c-5-2-10-3-15-6 7 2 15 3 22 5 2 1 5 1 7 3h0l1 1 6 3c9 4 17 13 22 20h-7l-1-1h-1c-8-6-15-13-24-17 1-1 1-2 2-3z" class="J"></path><defs><linearGradient id="BK" x1="258.982" y1="504.14" x2="254.399" y2="501.573" xlink:href="#B"><stop offset="0" stop-color="#636261"></stop><stop offset="1" stop-color="#787777"></stop></linearGradient></defs><path fill="url(#BK)" d="M237 487l4 1c2 1 3 2 5 3 1 1 2 2 4 3s3 3 6 4c2 1 4 5 5 7v1l-1 1h-1c-8-6-15-13-24-17 1-1 1-2 2-3z"></path><path d="M645 665c1-2 3-3 5-5 3-2 6-5 10-7 7-4 16-6 24-8h3 0 1 10c14 0 28-1 42 1-1 1-1 0-2 1l-1 1c-7-1-15 0-23 0-10-1-23-1-33 2-2 0-4 1-6 2l-7 2-11 5c-5 1-8 4-12 6z" class="I"></path><path d="M172 135c6 1 12 0 19 0h32c3 0 9-1 11 0h1c4 1 9-1 12 1v1c-1 1-1 1-1 2h-2c-2-1-6-1-8 0h0 7c-6 1-12 0-19 0h-32c-7 0-14-1-20 0-1-1 0-3 0-4z" class="N"></path><path d="M416 770l1-1c5 3 9 6 14 9v-1h0l-2-2c0-2 1-1 2-3h0v1l1 1v-2h1l1 1 1-1c10 8 16 19 21 31 2 3 5 8 5 13-2-2-5-3-7-4-4-11-8-22-17-29-7-5-14-8-21-13z" class="L"></path><defs><linearGradient id="BL" x1="454.994" y1="788.136" x2="427.68" y2="781.564" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#BL)" d="M431 778v-1h0l-2-2c0-2 1-1 2-3h0v1l1 1v-2h1l1 1 1-1c10 8 16 19 21 31h0l-1 1c-1-2-3-4-4-6-4-8-9-17-16-21l-1 1 2 2h-1c-1 0-3-1-4-2h0z"></path><defs><linearGradient id="BM" x1="436.147" y1="280.48" x2="420.997" y2="282.916" xlink:href="#B"><stop offset="0" stop-color="#a09c9a"></stop><stop offset="1" stop-color="#cacccb"></stop></linearGradient></defs><path fill="url(#BM)" d="M419 269c2 2 4 3 6 5 2-1 3 0 5 0 5 4 7 10 11 15l6 13-1 2c3 5 4 12 4 19v6c0 1 0 4-1 5h0c-1-4-2-8-2-11-2-20-13-37-27-49v-1-1c0-1-1-2-1-3z"></path><path d="M444 300l2 4c3 5 4 12 4 19v6c-1 0 0-1-1-2v-1-1c-1-3-1-6-1-9-1-5-3-10-5-15l1-1z" class="c"></path><path d="M425 274c2-1 3 0 5 0 5 4 7 10 11 15l6 13-1 2-2-4c-5-10-12-18-19-26z" class="F"></path><defs><linearGradient id="BN" x1="645.55" y1="314.26" x2="619.613" y2="275.771" xlink:href="#B"><stop offset="0" stop-color="#484848"></stop><stop offset="1" stop-color="#6f6e6d"></stop></linearGradient></defs><path fill="url(#BN)" d="M642 307l3 10 1 1v5h0v-2l-1-2c0-2-1-3-1-4v-1c0 1-1 1-1 1-1 2-2 3-3 4 1 1 1 2 1 3v3c-3-12-8-24-13-35-2-5-5-10-8-16h1c2-1 4-2 6-1 7 11 11 22 15 34z"></path><path d="M719 517c5-2 10-5 17-3l-1 1h-2c-2 1-5 3-7 5l-1 2h-2l-1 1h0c-2 3-5 5-6 8l-14 21c0 1-1 3-2 4l-1-2v-3c2-4 3-6 3-10h1 2c1-1 1-3 2-4 1-3 2-6 2-8l1-2-1-2 6-9c1 0 3 0 4 1z" class="G"></path><path d="M719 517c5-2 10-5 17-3l-1 1h-2c-2 1-5 3-7 5-1 0-2 0-3 1-1 0 0-1-1-1-2 1-2 2-4 2 0-1 1-2 2-2l-1-1-6 6c1-3 3-6 6-8z" class="a"></path><path d="M710 527l1-1v1c-1 5-3 10-5 15 3-3 5-7 7-10 1-3 2-5 4-7 1-1 3-2 5-2h0c-2 3-5 5-6 8l-14 21c0 1-1 3-2 4l-1-2v-3c2-4 3-6 3-10h1 2c1-1 1-3 2-4 1-3 2-6 2-8l1-2z" class="P"></path><path d="M702 541c2 2 2 2 3 4-2 3-3 5-3 7 0 1-1 3-2 4l-1-2v-3c2-4 3-6 3-10z" class="Y"></path><defs><linearGradient id="BO" x1="293.572" y1="602.997" x2="319.619" y2="614.69" xlink:href="#B"><stop offset="0" stop-color="#c3c1bf"></stop><stop offset="1" stop-color="#e7e6e7"></stop></linearGradient></defs><path fill="url(#BO)" d="M299 602l2-1c2 1 4 5 6 7l1 1h1c6-1 12 1 17-1 3 2 5 1 8 1l-1 1 6 2h-3l-1 1-1-1-1 1c-1 0-1 1-2 1h-2-4l-1 1c-1 1-4 0-6 0h-2l1 1c-1 1-1 1-2 1l-14-2h-1l-1-2h-4l-3-1 2-2h0c-1-1-3-2-4-3-1 0-2-1-3-1v-1l9 1c1-1 2-3 3-4z"></path><path d="M299 602l2-1c2 1 4 5 6 7-2 0-4 0-6-1v-4l-2-1z" class="M"></path><path d="M326 608c3 2 5 1 8 1l-1 1-8 2c-1-1-1-1-1-2l-15-1c6-1 12 1 17-1z" class="G"></path><path d="M333 610l6 2h-3l-1 1-1-1-1 1c-1 0-1 1-2 1h-2-4l-1 1c-1 1-4 0-6 0v-1l3-1v-1h4l8-2z" class="c"></path><path d="M321 613c3 1 5 0 8 1h-4l-1 1c-1 1-4 0-6 0v-1l3-1z" class="a"></path><path d="M294 610h5c1 0 1 0 2 1h1 1l4 1h14v1l-3 1v1h-2l1 1c-1 1-1 1-2 1l-14-2h-1l-1-2h-4l-3-1 2-2h0z" class="M"></path><path d="M299 613l17 2 1 1c-1 1-1 1-2 1l-14-2h-1l-1-2z" class="D"></path><path d="M582 468c2 0 4 2 5 4h0v1c0 1 0 3-1 4h-2c-2 0-4 0-5 1v1 1 2l-1 2h0-1l-1-1c-1-1-1-2-1-3-1-2-1-3 1-4l1-2c-1 0-1 0-2 1h0c-2 2-2 4-2 6s1 2 1 3v2c1 1 1 2 2 3l8 15c1 3 1 5 1 8h0c-1 0-3-2-4-3v-1h0c-3-4-5-10-7-15h-2 0l-2-4h-1c0-4-1-7-2-11v-1c0-2 1-2 2-3h0c1-2 2-2 4-3h0l1 1c0-1 2-1 2-2 1 0 2 0 2-1 3 0 2 1 4 3-1 0-1 0-1 1l1-1h2 1v-1l-3-3z" class="K"></path><path d="M580 474v1c0 1-1 1-2 1v1l1 1v1h-4c0-1 0-1 1-2 1-2 2-2 4-3z" class="S"></path><path d="M575 479h4v1 2l-1 2h0-1l-2-5z" class="E"></path><path d="M587 472v1c0 1 0 3-1 4h-2c-2 0-4 0-5 1l-1-1v-1c1 0 2 0 2-1v-1c1-1 5-2 7-2z" class="J"></path><path d="M454 437c-1 1-3 2-4 2 0 0-1 0-1 1h-1c-1 0-2 0-3-1-1 0-1-1 0-1s0 0 1 1c3-1 4-2 7-3h0l3-3h0c1-1 2-2 3-4v-2h0l-2 2c-1 1-5 3-6 4-2 0-2 0-3 1-2 0-2 0-3 1h-1v-1-1c1-1 2-1 3-1l1-1h0c1 0 2-1 3-1h1c1-1 1-1 2-1v-1c2-1 4-3 5-4l1-1v-1-1c-1-2-1-5-3-7-1 1-1 1-1 2-1 1-2 2-2 3l-3 3-1-1 3-3 1-1c0-1 1-1 2-2v-1c1-1 1-2 2-2v-1c1 1 1 2 1 3v1l1 1h0c0 2 1 3 1 5v1 1h1v2c1 0 1 0 2 1v6l-1 1v1 1c-1 0-1 1-2 1v1c-1 1-1 2-1 3l-1 1v1 1c0 1 0 1 1 1 1 1 2 2 2 3s0 1-1 2h1 0c1 1 1 2 1 3-1 1-1 2-3 2l-2 3-1-1 2-2c1 0 2-1 3-2v-2c-2 2-4 4-6 5-1 1-1 2-3 2-1 1-1 1-2 1v-2c-1-2 1-2 0-4 1-1 2-1 3-2s2-1 3-1h1v1l3-2c0-1-1-2-1-2-1 0-1-1-2 0s-2 1-4 3v-1-1s-1-1-1 0h0c0 1 0 1-1 1h-1 0v-1c-1 0-1 0-2 1-1 0-1 0-1-1-1-1-1-1 0-1 3-3 5-5 6-9z" class="f"></path><path d="M454 437h1c3-4 6-6 7-11h1c1 2 1 3 0 5-1 3-2 5-3 7-1 3-2 5-4 7-2 1-5 1-7 2l-1-1c3-3 5-5 6-9z" class="K"></path><path d="M458 285h1c6-3 6-8 6-13 0-2 0-4 1-5s2 0 4 0h1c1 2 2 3 3 5l-1 3c0 3 0 4-1 7l-1 1-2 8-3 8c0 2 0 4-1 6l-3 3v1l-1-1 1-1c1-9-4-14-7-22l-1-1c2 0 3 1 4 1z" class="S"></path><defs><linearGradient id="BP" x1="465.409" y1="279.514" x2="472.417" y2="277.782" xlink:href="#B"><stop offset="0" stop-color="#646362"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#BP)" d="M470 267h1c1 2 2 3 3 5l-1 3c0 3 0 4-1 7l-1 1-2 8c-1 0-1-1-2-1 3-8 2-14 0-21 1-2 1-2 3-2z"></path><path d="M471 267c1 2 2 3 3 5l-1 3-1-1-1-7z" class="S"></path><path d="M472 274l1 1c0 3 0 4-1 7l-1 1v-3c0-2 0-4 1-6z" class="B"></path><path d="M536 554v-2c-1-3-3-13-1-16l9 25c1 0 2 0 2 1 1 1 2 3 3 4v-1c2 2 3 4 4 6l5 9c1 0 1 1 2 2l-2 4h0c-1-2-1-2-3-3v-1c-1 2-1 7-1 9 0-2 1-6 0-8h-1-1-2c-1-1-2-3-2-5h-1c0-2 1-2 0-3v-1 4 3 1h-1 0v-3h-1v1 2h1v1c-1 2-1 3-1 5 0-6-2-12-3-18-1-2-2-4-2-6-1-4-2-7-3-10h-1z" class="O"></path><path d="M544 561c1 0 2 0 2 1 1 1 2 3 3 4v-1c2 2 3 4 4 6l5 9h-1l-1-1-2-1c-2 0-8-8-8-10l-2-7z" class="S"></path><path d="M460 826h0c0-2-3-4-3-5-1-1-3-3-3-4 1 1 2 2 3 2h1c2 2 6 3 7 5v1h2c2 1 2 2 3 3 0 2 1 4 2 5 2 4 4 8 6 11 0 3 0 3 2 6l16 27v1c-1 0-1 0-1 1-1-1-2-1-2-2h-1l-32-51z" class="P"></path><path d="M261 523c4 0 9 0 13 1 2 0 4 1 6 2l6 3c2 1 5 3 7 3 4 2 9 5 9 9v1c-9-7-19-11-30-14-9-2-17-1-25 4-2 1-4 3-5 5s-2 3-2 5l-1 1-2-3c-1 2-1 4-1 6h0l-1 5c0-3 1-7 0-10h-1l-1 2h-1c3-8 7-12 14-16 5-2 9-3 15-4z" class="d"></path><path d="M261 523c4 0 9 0 13 1 2 0 4 1 6 2-11-2-24-3-34 3l-6 6v2h2c-1 2-2 3-2 5l-1 1-2-3c-1 2-1 4-1 6h0l-1 5c0-3 1-7 0-10h-1l-1 2h-1c3-8 7-12 14-16 5-2 9-3 15-4z" class="C"></path><path d="M240 535v2h2c-1 2-2 3-2 5l-1 1-2-3c0-2 1-3 3-5z" class="W"></path><defs><linearGradient id="BQ" x1="842.587" y1="104.954" x2="843.075" y2="132.062" xlink:href="#B"><stop offset="0" stop-color="#090908"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#BQ)" d="M835 101c2-1 4-1 6-2 0 2-1 3-1 5h1c3 0 6 1 9 3 3 3 4 8 4 12-1 6-4 12-8 15h0v-1c-1-1-1-1-2-1l-1-1 1-1c1-1 2-4 3-6 0-3 0-5-1-8-2-2-3-3-6-4l-3 1c1 1 2 1 3 2-1 1-1 2-2 3v1h-2c-1-1-3-2-3-3-1-3 0-6 1-8-1-2 0-1 1-2v-5z"></path><path d="M839 108l-1 2 2 2-3 1-1-1c0-2 2-2 3-4z" class="D"></path><path d="M836 112l1 1c1 1 2 1 3 2-1 1-1 2-2 3v-1c-1 0-1-1-1-1-1-1-1-2-1-4z" class="K"></path><path d="M835 101c2-1 4-1 6-2 0 2-1 3-1 5h1-1c-2 1-4 2-6 4-1-2 0-1 1-2v-5z" class="T"></path><path d="M839 108c3 0 4-1 7 1 2 1 4 5 4 7 1 5 0 9-3 12 0 1-1 2-3 3v-1c1-1 2-4 3-6 0-3 0-5-1-8-2-2-3-3-6-4l-2-2 1-2z" class="C"></path><path d="M535 243h1c1-3 1-5 3-7l1-2v4c0 1 0 2-1 4v2c0 1 0 4-1 5 0 1 0 3 1 5v3c0 1 0 4-1 5-3 2-3 10-3 14v3c-1 4-1 9-1 14 0 3 0 7-1 10-1 2 0 5 0 7v8 10l-1-34h0l-1 16c0-1 0-1-1-1-1-2 1-16 0-20-1-2-1-5-1-7 1-13 2-27 6-39z" class="G"></path><path d="M574 164v-1c3-2 10-1 13-2 13 0 25 0 38 1l54-1v2h43v1h-18-76-8-35-11z" class="Q"></path><path d="M625 162l54-1v2h-55c1-1 5 0 7-1h-6z" class="Z"></path><defs><linearGradient id="BR" x1="776.046" y1="170.441" x2="773.558" y2="154.236" xlink:href="#B"><stop offset="0" stop-color="#70666a"></stop><stop offset="1" stop-color="#939993"></stop></linearGradient></defs><path fill="url(#BR)" d="M724 162l104-1v1l-1 2c-1 1-1 1-3 1l-37 1-2-2h-51-12v-1h6v-1h-4 0z"></path><path d="M785 164h32c2 0 8-1 10 0-1 1-1 1-3 1l-37 1-2-2z" class="G"></path><path d="M501 503c1 2 2 3 3 5 1 4 0 10 0 14l1 10-1 12 1 32c-1-2-1-4-1-6l1-12h-1c0 1 0 1-1 2v2h0l-1 4h0v2l-1 1-2 14c0-1 0-2 1-3v-4c0-2-1-3 1-5v-4h-1v1c0 1 0-1 0 1-1 1-1 2-1 3v1l-1 3v8h0c-1-8 1-16 3-23 0-2 1-5 0-7-1 4-1 8-2 12-1 2-1 5-2 6 0-1 0-2 1-3v-2c0-1 0-2 1-3v-2c0-1 0-2 1-3v-3c0-2-1-3 1-5 0-1 0-3-1-4v-4h-1v4l-1 1v3c-1 1 0 0-1 2l-1 2v3l-1 2v1l-1 2v2l-1 2v1l-1 1v-3l6-19v-3-1 1c-1-4 0-9 1-13l2-18v-10z" class="L"></path><path d="M579 232c1-1 2-3 4-4-1 2-3 4-4 6-1 1-3 3-4 5-1 1-2 3-3 5l-6 6c-1 2-1 3-3 3l-1 1v2c1 1 2 2 2 3-1 2-1 2-3 2h0v1c-1 1-1 1-2 1-1-1-1-2-2-3l-1-5c0-3-1-4-1-6-1-3 0-5 0-8l1-3h-1l1-1c0-2 1-5 2-6 0-1 1-1 2-2l1 1c1 2 4 5 5 7v1c1 1 1 2 1 3v2l-1 1v1c0 1-1 2-1 2v2l1-3c2-3 4-6 6-8v-1c2-2 4-5 7-5z" class="I"></path><path d="M557 255c2 1 3 1 4 2l-1 3c-1-1-2-3-3-5z" class="R"></path><path d="M556 255h1 0c1 2 2 4 3 5l1 1v1c-1 1-1 1-2 1-1-1-1-2-2-3l-1-5z" class="T"></path><path d="M579 232c1-1 2-3 4-4-1 2-3 4-4 6-1 1-3 3-4 5-1 1-2 3-3 5l-6 6c-1 2-1 3-3 3 3-8 10-15 16-21z" class="S"></path><path d="M556 238l3-8c3 3 6 6 8 10l-5 13-3-4c-1 1-2 2-2 3v3h-1c0-3-1-4-1-6-1-3 0-5 0-8l1-3z" class="J"></path><path d="M621 265l11-4 4 2c-16 4-30 16-38 29-6 11-9 23-13 34-1-1 0-3 0-4 0-8 2-15 5-22-1-2-1-3-1-5h0c2-4 4-8 8-11l1-1v1l1 1c6-8 13-16 22-20z" class="T"></path><path d="M597 284l1-1v1l1 1c-4 5-7 9-9 15-1-2-1-3-1-5h0c2-4 4-8 8-11z" class="D"></path><path d="M554 167c3-3 6-8 7-12 0-9-8-9-12-15-3-2-4-6-3-9 0-3 1-5 3-7 2 1 4 5 5 7 2 2 4 2 7 3h4l-3 2c-2 1-3 1-5 3h1c1 2 2 3 3 5 4 4 7 9 6 15l-1 2c0 1 0 2-1 3-2 0-4 2-6 3l-2 2c-1 0-2 0-3-1v-1z" class="H"></path><path d="M554 131c2 2 4 2 7 3h4l-3 2c-2 1-3 1-5 3h1c1 2 2 3 3 5v-1c-4-1-6-4-8-7-1-2-1-3-2-4v-1h3z" class="Z"></path><path d="M561 134h4l-3 2c-2 1-3 1-5 3-1-1-2-2-2-3v-1c2 0 4 0 5-1h1z" class="T"></path><defs><linearGradient id="BS" x1="508.643" y1="578.096" x2="538.355" y2="567.27" xlink:href="#B"><stop offset="0" stop-color="#646260"></stop><stop offset="1" stop-color="#bababb"></stop></linearGradient></defs><path fill="url(#BS)" d="M525 522v63l1 21c0 3 1 7 1 10-1 1-1 2-1 4v1-1c-1-1-1 0-2-1s-1-1-1-2l-1 2c-1-11-1-23-1-35v-37-12c1-3 1-6 1-10l2 1h0c0-1 0-2 1-4z"></path><path d="M471 256c-1-2-1-5-2-7-1-3-3-6-4-9 3-3 6-7 9-11l1 1c1 2 2 6 3 8 2 7 4 13 4 20 0 6-1 13-2 20l-5-11c-1-2-1-4 0-6 1 1 1 3 1 5h1v-14c-1-2-1-4-3-6v6 1l-1 1v-1l-2 3z" class="F"></path><path d="M301 615l14 2c4 0 7 1 11 2 3 0 5 2 8 2l2 1h-5l8 2c-2 1-6 0-9 1-5 1-11 2-15 4l-4 2c-3-1-5-3-8-4 0-1-1-2-2-2 0 0-1 0-1-1-2-1-2-1-4-1h0l-1-1h4 0v-2-1h4c-2-1-5 0-7-1l-1-1c2-1 4-1 6-2z" class="N"></path><path d="M295 622h4 0 9l-2 1c1 1 3 1 4 2h3v1h-2c0 1 1 2 1 3h3l-4 2c-3-1-5-3-8-4 0-1-1-2-2-2 0 0-1 0-1-1-2-1-2-1-4-1h0l-1-1z" class="X"></path><defs><linearGradient id="BT" x1="309.062" y1="622.847" x2="313.25" y2="613.568" xlink:href="#B"><stop offset="0" stop-color="#9b9998"></stop><stop offset="1" stop-color="#bbb9b9"></stop></linearGradient></defs><path fill="url(#BT)" d="M301 615l14 2c4 0 7 1 11 2 3 0 5 2 8 2l2 1h-5c-7 0-13-1-19 0h-4-9v-2-1h4c-2-1-5 0-7-1l-1-1c2-1 4-1 6-2z"></path><path d="M303 619l9 2v1h-4-9v-2-1h4z" class="Q"></path><path d="M599 738l4-5 2 1h3v1c0 2 0 3 1 5 1 3 5 6 4 10-2 0-2 0-4 1-1 1-2 1-4 1l-1 3c-3 1-5 3-7 3-1 2-2 3-3 4l-1-1v-1l-1 1h-1c-1 1-3 3-4 5s-3 4-4 6c-2 3-8 16-10 16 2-4 5-10 8-14 2-4 5-8 8-12 0-3 0-4 2-6 1-2 1-4 3-6 1-2 2-4 2-6v-1l3-5z" class="C"></path><path d="M591 756h2c-1 2-3 4-4 6 0-3 0-4 2-6z" class="K"></path><path d="M593 760c3-4 6-8 10-11 0 2-1 3-2 4-2 2-3 3-4 5s-2 3-3 4l-1-1v-1z" class="B"></path><path d="M603 749l1-1 1-1c1 0 1 1 2 1s1-1 2 0l-4 4-1 3c-3 1-5 3-7 3 1-2 2-3 4-5 1-1 2-2 2-4z" class="S"></path><path d="M605 734h3v1c0 2 0 3 1 5 1 3 5 6 4 10-2 0-2 0-4 1-1 1-2 1-4 1l4-4c-1-1-1 0-2 0s-1-1-2-1h2l1-1-2-2h-1c-2 1-3 2-4 2 1-3 5-4 5-7l1-1v-2h-1l-1-2z" class="F"></path><path d="M599 738l4-5 2 1 1 2h1v2l-1 1c0 3-4 4-5 7l-3 3c0 1-2 2-3 2h-1v-1c1-2 2-4 2-6v-1l3-5z" class="E"></path><path d="M370 578v-5c1 1 1 3 1 5h1l1-2c0 2 0 5 1 7v1c2 2 3 8 5 9h1c1 1 1 3 2 5h0l1 3c2 2 2 5 3 7 2 2 3 3 4 6v1 2h-1l-4-4h-1l-9-12c-1 0-2 0-2-1-4-2-6-4-8-8l-3-4 1-1c0-2 0-3 1-4-1-1 0-2 0-3 0 0 1 0 2-1 0 2 1 4 1 5h1c1 0 2-2 2-3v-3z" class="K"></path><path d="M386 608c2 2 3 3 4 6v1l-1-1c-2-1-2-4-3-6z" class="L"></path><path d="M382 598l1 3 1 4-1 1c-1-1-1-2-2-3 0-1 0 0 1-1v-1c-1-1 0-1 0-3z" class="C"></path><path d="M370 581l3 9 2 5-1 1-1-2c0-2-3-4-4-6-1-1-2-3-2-4h1c1 0 2-2 2-3z" class="T"></path><path d="M373 599l-1-5h1l1 2c1 3 3 5 5 7 2 3 4 7 6 10h-1l-9-12-2-2z" class="h"></path><path d="M374 583v1c2 2 3 8 5 9h1c1 1 1 3 2 5h0c0 2-1 2 0 3v1c-1 1-1 0-1 1-1-1-1-3-2-4v-1c-3-3-6-11-5-15z" class="J"></path><defs><linearGradient id="BU" x1="373.332" y1="591.033" x2="362.275" y2="585.261" xlink:href="#B"><stop offset="0" stop-color="#494847"></stop><stop offset="1" stop-color="#616060"></stop></linearGradient></defs><path fill="url(#BU)" d="M364 580s1 0 2-1c0 2 1 4 1 5s1 3 2 4c1 2 4 4 4 6h-1l1 5 2 2c-1 0-2 0-2-1-4-2-6-4-8-8l-3-4 1-1c0-2 0-3 1-4-1-1 0-2 0-3z"></path><path d="M364 583v2 1c0 1 1 2 1 3 2 4 4 6 8 10l2 2c-1 0-2 0-2-1-4-2-6-4-8-8l-3-4 1-1c0-2 0-3 1-4z" class="S"></path><defs><linearGradient id="BV" x1="280.094" y1="619.608" x2="289.924" y2="616.741" xlink:href="#B"><stop offset="0" stop-color="#545353"></stop><stop offset="1" stop-color="#747272"></stop></linearGradient></defs><path fill="url(#BV)" d="M287 612h5l3 1h4l1 2h1c-2 1-4 1-6 2l1 1c2 1 5 0 7 1h-4v1 2h0-4l1 1h0c2 0 2 0 4 1 0 1 1 1 1 1 1 0 2 1 2 2 3 1 5 3 8 4-3 2-3 2-6 3-3-2-7-3-9-4-3 0-6-2-8-3-4-1-7-1-9-4v-5-2h0c2-3 5-3 8-4h0z"></path><path d="M295 613h4l1 2c-3 0-6-1-8-1 1-1 2-1 3-1z" class="C"></path><path d="M287 612h5l3 1c-1 0-2 0-3 1h-8c-1 1-3 2-4 3v5c2 1 3 0 5 0 3-1 11-2 14 0h-4c-3 0-7 1-10 2 1 1 0 1 1 1 2 1 4 1 6 2v1c2 0 3 1 4 2-3 0-6-2-8-3-4-1-7-1-9-4v-5-2h0c2-3 5-3 8-4h0z" class="J"></path><path d="M296 630c-1-1-2-2-4-2v-1c-2-1-4-1-6-2-1 0 0 0-1-1 3-1 7-2 10-2l1 1h0c2 0 2 0 4 1 0 1 1 1 1 1 1 0 2 1 2 2 3 1 5 3 8 4-3 2-3 2-6 3-3-2-7-3-9-4z" class="B"></path><defs><linearGradient id="BW" x1="447.819" y1="256.259" x2="442.45" y2="271.612" xlink:href="#B"><stop offset="0" stop-color="#414141"></stop><stop offset="1" stop-color="#727170"></stop></linearGradient></defs><path fill="url(#BW)" d="M425 254l1-1v-1l-2-2c1 0 1 0 2 1l1 1h0 1 1v1h1l1 1 2-1v1h3v-1l2-1 2 1c1 2 2 3 4 4 1 1 2 2 4 3 2 3 4 5 6 8l1-1 2 4c1 1 2 3 1 5 0 1 0 1-1 1v3h-2c1 1 2 2 2 3s0 1 1 2c-1 0-2-1-4-1-4-8-12-16-18-21-1-2-9-8-11-9z"></path><path d="M431 254l2-1v1h3l1 1h0l-3 1h0c1 1 1 2 1 2-1-1-3-2-4-4zm24 13l2 4c1 1 2 3 1 5 0 1 0 1-1 1l-3-9 1-1z" class="O"></path><path d="M425 254l1-1v-1l-2-2c1 0 1 0 2 1l1 1h0 1 1v1h1l1 1c1 2 3 3 4 4 8 7 14 14 20 22 1 1 2 2 2 3s0 1 1 2c-1 0-2-1-4-1-4-8-12-16-18-21-1-2-9-8-11-9z" class="R"></path><defs><linearGradient id="BX" x1="334.375" y1="641.529" x2="324.744" y2="656.783" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#595757"></stop></linearGradient></defs><path fill="url(#BX)" d="M295 645h4l9-1v1c-3 1-6 0-8 1h2l1-1h0c1 1 2 0 3 0 4 0 7 1 11 1 5 0 10-1 15 0l12 2 3 1c2 1 3 1 4 2h-5l-1 1c-1 0-4-1-5-1-2 0-5-1-8-1-5 0-11 1-17 2-1-1-4 0-6 0-4 1-7 4-10 7h-1l-1 1c-1 1-1 1-1 2h-1v1h-1v-4c0-1 1-2 1-3v-1c0-3 1-4 0-6l-4-1-1-1c-1 0-3-1-4-1h2l7-1z"></path><path d="M295 645h4l9-1v1c-3 1-6 0-8 1h2l1-1h0c1 1 2 0 3 0 4 0 7 1 11 1 5 0 10-1 15 0l-32 1c-4 0-8 0-12-1l7-1z" class="R"></path><defs><linearGradient id="BY" x1="306.669" y1="645.228" x2="294.062" y2="663.044" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#918f8f"></stop></linearGradient></defs><path fill="url(#BY)" d="M295 649h8c2 0 3-1 6 0h3l-3 1c-2 0-2 1-3 2h-1l-2 2c-2 1-4 3-5 5l-1 1c-1 1-1 1-1 2h-1v1h-1v-4c0-1 1-2 1-3v-1c0-3 1-4 0-6z"></path><defs><linearGradient id="BZ" x1="861.823" y1="324.272" x2="845.327" y2="341.431" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#BZ)" d="M859 318c0 2-1 4-2 6 1 0 1-1 2-1h2l1-3 1-1 1-6 1-2v-1l1-3v-2c1 0 1-1 1-2l1-1v1 1 1c0 1 0 0-1 1v3h0c0 2 0 1-1 3v2c-1 1-1 1-1 2 1 0 1-1 1-2h0c0 2 0 3-1 4 0 1 0 2-1 3 0 1 0 2-1 3l-1-1v1c0 1-1 2-1 3h-1v1 1l-1 1c0 1 0 1-1 1l-1 2h0c-3 3-6 8-8 12h1c1-1 3-4 4-4l2-1v1h0c-1 1-1 3-1 4l-2 1c-1 1-2 3-3 4v1l-1 2v1c-1 1-2 3-3 3v-2s-1 1-2 1l-3 3c-2 3-4 6-7 9v-3c1-1 1-1 0-2l-1 1h-1 0v-1c-1 1-2 1-3 1l-3 2c3-4 7-7 11-11 2 0 8-9 9-11 0-1-1-1-2-2l-1 1 3-5c2-1 5-4 6-6l1-4c2 0 5-8 6-10z"></path><path d="M844 342c2-3 5-5 8-7-2 2-3 4-4 7-1 0-1 1-2 2 0-1-1-1-2-2z" class="P"></path><path d="M841 353c1-2 3-5 5-6 0 1-1 1-1 2h1 0v1 4l-2 1h0-2c0-1 0-1-1-2z" class="Y"></path><path d="M841 353c1 1 1 1 1 2h2 0v1l-3 3c-2-1-2-1-3 0-1 0-2-1-3-1 3-2 4-3 6-5z" class="T"></path><path d="M854 341l2-1v1h0c-1 1-1 3-1 4l-2 1c-1 1-2 3-3 4v1l-1 2v1c-1 1-2 3-3 3v-2s-1 1-2 1v-1l2-1 5-8c1-2 2-3 3-5z" class="L"></path><path d="M835 358c1 0 2 1 3 1 1-1 1-1 3 0-2 3-4 6-7 9v-3c1-1 1-1 0-2l-1 1h-1 0v-1c-1 1-2 1-3 1l6-6z" class="W"></path><defs><linearGradient id="Ba" x1="533.012" y1="876.84" x2="572.807" y2="849.118" xlink:href="#B"><stop offset="0" stop-color="#21201f"></stop><stop offset="1" stop-color="#636363"></stop></linearGradient></defs><path fill="url(#Ba)" d="M565 831l-1 5 1 1c1 0 1 0 2-1l2-4v1c1 2 1 5 0 7l-1 1c0 1 0 1 1 2h0c1-2 0-1 1-2 1-2 1-3 3-4-4 8-8 15-12 22s-8 15-13 20c-4 3-8 5-11 7h-1c0-3 8-8 7-10 1-2 4-4 5-6l6-9 3-6c0-2 0-4-1-6l3-5 1-2 5-11z"></path><path d="M560 842c0 1 0 4 1 5l-4 8c0-2 0-4-1-6l3-5 1-2z" class="Q"></path><path d="M565 831l-1 5 1 1c1 0 1 0 2-1l-6 11c-1-1-1-4-1-5l5-11z" class="M"></path><path d="M625 684v-1c6-6 15-9 24-10l2 1c-1 2-2 2-3 3-1 0-1 0-1 1-1 1-1 1-1 2h5v1c-1 0-2 0-3 1 2 0 4 0 5 1-3 0-6 0-7 2 2 0 5 0 7 1-3 2-4 1-8 1-1 0-3 0-4 1h-1c-2 0-3 1-4 2v1c-2 0-3 0-5-1l-4 2c-1 1-2 1-3 2-2 2-3 1-5 3l-4 4v-2h-1-1l4-4c0-3 1-4 3-6v-1c1-1 3-3 5-4z" class="D"></path><path d="M638 681h-1c1-1 1-1 3-1 1-1 1-1 2 0v1h2v-1h2 0 5v1c-1 0-2 0-3 1h-5c-7 0-13 5-18 9v-1c4-4 9-7 15-9h0-2z" class="B"></path><path d="M631 690c4-2 9-5 15-5 2 0 5 0 7 1-3 2-4 1-8 1-1 0-3 0-4 1h-1c-2 0-3 1-4 2v1c-2 0-3 0-5-1z" class="M"></path><defs><linearGradient id="Bb" x1="629.492" y1="677.499" x2="641.508" y2="686.501" xlink:href="#B"><stop offset="0" stop-color="#636262"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#Bb)" d="M625 684v-1c6-6 15-9 24-10l2 1c-1 2-2 2-3 3-1 0-1 0-1 1-1 1-1 1-1 2h0-2v1h-2v-1c-1-1-1-1-2 0-2 0-2 0-3 1h1c-3 1-6 2-8 4-2 1-6 4-8 5l-5 5c0-3 1-4 3-6v-1c1-1 3-3 5-4z"></path><path d="M625 684h0c-1 1-1 2-1 3-1 1-3 2-3 3h1 0l-5 5c0-3 1-4 3-6v-1c1-1 3-3 5-4z" class="Y"></path><defs><linearGradient id="Bc" x1="552.869" y1="473.184" x2="543.492" y2="472.631" xlink:href="#B"><stop offset="0" stop-color="#3d3d3c"></stop><stop offset="1" stop-color="#595958"></stop></linearGradient></defs><path fill="url(#Bc)" d="M546 458c1-2 3-3 4-5 2-2 2-5 3-7l2-4 1 3c-1 2-1 5-1 7-1 1-1 1-1 2 1 1 1 1 0 1-1 1-1 2-1 4l-2 4c-2 2-2 7-2 10-1 6-1 12-1 17v10 5h0-1 0v5c-1-2 0-5-2-7 1 9 2 18 4 27v1h-1c-3-10-3-19-5-29 0-2 0-3-2-4h1c0-2-1-3-1-5v-1c0-3 1-6 1-9 2-3 1-7 1-10l3-15z"></path><path d="M542 498c0-1 1-1 1-2-1-3-1-6 0-8v-2l1-1c1 3 0 12 1 15 0 1-1 3 0 3 1 9 2 18 4 27v1h-1c-3-10-3-19-5-29 0-2 0-3-2-4h1z" class="B"></path><path d="M250 540v-5c5-3 12-4 17-4l13 3h0c8 2 14 8 20 12v1c-8 0-15-4-22-5-3-1-7-1-10-1s-7-1-10 0h-1c-2 1-4 3-6 4h-1v-5z" class="b"></path><path d="M250 540c0-2 1-3 2-3s1 0 1 1h0c3 0 6 0 9 1 3 0 8-1 11 1-2 1-3 1-5 1-3 0-7-1-10 0h-1c-2 1-4 3-6 4h-1v-5z" class="K"></path><path d="M557 429l3-6c2-2 3-5 5-7 1 0 2-1 3-2s1-2 3-3v2c0-1 1-2 1-2l2-2h0l1-1-1 4h0l-1 11-2 2v3c2 6 4 10 8 14h-2v1h-2-2v-1h-2c-4-1-7-4-11-7 0 2-1 4-1 6-1 2-2 3-3 4l-1-3c1-4 1-9 2-13z" class="F"></path><path d="M571 428v-1c-1-5-1-11 2-15h1l-1 11-2 2v3z" class="T"></path><path d="M326 589h5c1 0 1 0 2 1 2 0 4 1 6 2 1 1 3 4 5 5l10 13h1l6 6c-1 0-3-1-4-2l-1 1 2 2c-2 0-4-1-6-2l-13-3-6-2 1-1c-3 0-5 1-8-1 4-3 6-5 7-10-1-2-1-3-3-5h0l-4-4z" class="S"></path><path d="M346 605l8 6v-1h1l6 6c-1 0-3-1-4-2l-1 1c-2 0-3-2-4-3-2-1-2-2-4-3-1-1-2-2-2-4z" class="D"></path><path d="M332 607l1-1c0-1 1-1 1-2 1-1 1-1 2-1l1 1c1 0 2-1 3-1 1 1 1 1 3 1-2-1-3-2-4-4 2 1 5 3 7 5 0 2 1 3 2 4 2 1 2 2 4 3l-6-3c-2-1-6-2-8-2h-5-1z" class="e"></path><path d="M330 593c2 0 3 1 4 2 2 1 2 2 2 4l-1 1v1c-2 2-4 4-5 6h2 1 5c2 0 6 1 8 2l6 3c1 1 2 3 4 3l2 2c-2 0-4-1-6-2l-13-3-6-2 1-1c-3 0-5 1-8-1 4-3 6-5 7-10-1-2-1-3-3-5z" class="Y"></path><path d="M334 609c6 0 12 2 17 4l1 2-13-3-6-2 1-1z" class="Z"></path><defs><linearGradient id="Bd" x1="564.943" y1="524.652" x2="545.802" y2="523.965" xlink:href="#B"><stop offset="0" stop-color="#323132"></stop><stop offset="1" stop-color="#5f5e5d"></stop></linearGradient></defs><path fill="url(#Bd)" d="M548 490c0 1 0 4 1 5 0 1 1 0 1 1 0 7 2 12 5 18 0 1 1 2 1 3-1 1-2 2-1 3 0 1 1 3 2 4l4 5c1 1 1 2 2 3h-1c0 1 0 1 1 1 1 1 2 1 3 2-1 3-1 6-2 8-1 1-1 2-1 3l-3-3c-1 1-1 1-3 0l-4-5c-2-3-3-5-4-8-2-9-3-18-4-27 2 2 1 5 2 7v-5h0 1 0v-5-10z"></path><path d="M555 526c1 1 3 3 6 3 1 1 1 2 2 3h-1c0 1 0 1 1 1 1 1 2 2 2 3h0c-3-1-4-4-7-5h-1c0-2-1-3-2-5z" class="J"></path><path d="M545 503c2 2 1 5 2 7 1 8 3 22 10 28 1 1 2 2 3 4v1c-1 1-1 1-3 0l-4-5c-2-3-3-5-4-8-2-9-3-18-4-27z" class="R"></path><path d="M548 490c0 1 0 4 1 5 0 1 1 0 1 1 0 7 2 12 5 18 0 1 1 2 1 3-1 1-2 2-1 3 0 1 1 3 2 4l4 5c-3 0-5-2-6-3l-1-1c-2-3-2-6-3-9-1-6-1-11-3-16v-10z" class="B"></path><path d="M298 524c1 2 3 5 5 6l4 3 10 12 3 3 6 9c3 3 4 7 6 9l6 11c-2-1-4-3-5-5l-31-30v-1c0-4-5-7-9-9 2 0 4 1 5 2s2 2 3 2h0c0-1-1-2-1-3-1-1-1-1-1-2l-1-1c-1-1-2-2-4-3l3-1-1-1c1 0 1 0 2-1z" class="H"></path><path d="M326 557c3 3 4 7 6 9l6 11c-2-1-4-3-5-5 0-2-5-9-7-11 1 0 1 1 2 1v1h1l-1-2s-1-1-1-2c-1-1-1-1-1-2z" class="D"></path><path d="M317 545l3 3 6 9c0 1 0 1 1 2 0 1 1 2 1 2l1 2h-1v-1c-1 0-1-1-2-1l-1-2-6-8v-1c-1-2-2-4-2-5z" class="K"></path><path d="M298 524c1 2 3 5 5 6l7 11c-6-2-7-8-12-11-1-1-2-2-4-3l3-1-1-1c1 0 1 0 2-1z" class="E"></path><path d="M303 530l4 3 10 12c0 1 1 3 2 5v1l-9-10-7-11z" class="I"></path><path d="M292 353l1-1v2c3 3 3 8 5 11 1 1 1 2 1 3h0c1 2 1 3 2 4v4l-2-2c-2 2-4 5-6 8l-2 3-3 6-1 2-3 5v1c-2 1-3 4-5 5l1-4h-1v-1l1-3v-1c-2 2-2 5-3 7h-1c0-1 0-2 1-3 0-1 0-2-1-2 1-4 3-6 4-8l5-11c2-6 5-11 6-17l1-8z" class="W"></path><path d="M288 384l3-4c0 3-1 5-2 8-1 2-2 3-2 5l-3 5v1c-2 1-3 4-5 5l1-4c1-2 1-3 2-5l6-11z" class="c"></path><path d="M282 395c2 0 2-2 4-2-1 1-1 1-1 2-1 1-1 2-1 3v1c-2 1-3 4-5 5l1-4c1-2 1-3 2-5z" class="Q"></path><path d="M292 353l1-1v2c1 4 1 9 3 13-1 5-3 9-5 13l-3 4c1-6 4-12 5-18 0-2 0-3-1-5h-1l1-8z" class="H"></path><path d="M293 354c3 3 3 8 5 11 1 1 1 2 1 3h0c1 2 1 3 2 4v4l-2-2c-2 2-4 5-6 8l-2 3-3 6-1 2c0-2 1-3 2-5 1-3 2-5 2-8 2-4 4-8 5-13-2-4-2-9-3-13z" class="E"></path><path d="M293 382c1-5 3-9 5-14h1c1 2 1 3 2 4v4l-2-2c-2 2-4 5-6 8z" class="Y"></path><defs><linearGradient id="Be" x1="531.483" y1="330.49" x2="538.517" y2="330.01" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#bbbab7"></stop></linearGradient></defs><path fill="url(#Be)" d="M535 279h1v10 77c0 4 0 9 1 13 0 10 0 20-3 29l-1-19-1-20v-5c0-5-1-12 0-17l1-19v-10-8c0-2-1-5 0-7 1-3 1-7 1-10 0-5 0-10 1-14z"></path><path d="M532 347h0l1-5v1h0c1 2 0 3 0 5 1 5 1 11 1 16v7c-1 3 0 6-1 9v-5c0-2 0-3-1-5v-1-5c0-5-1-12 0-17z" class="H"></path><path d="M536 366c0 4 0 9 1 13 0 10 0 20-3 29l-1-19-1-20v1c1 2 1 3 1 5v5c1-3 0-6 1-9 0 5 0 11 2 17 1-7 0-15 0-22z" class="Y"></path><defs><linearGradient id="Bf" x1="389.971" y1="638.305" x2="355.031" y2="636.165" xlink:href="#B"><stop offset="0" stop-color="#9a9897"></stop><stop offset="1" stop-color="#bfbebd"></stop></linearGradient></defs><path fill="url(#Bf)" d="M351 628h2c4-1 8 1 11 1 4 1 8 0 12 1 2 1 5 2 7 3 2 3 4 5 6 7 2 1 4 3 5 5v1 1l1 1c2 2 4 4 5 6v1l-2-1-2 2-5-6-4-3-7-5c0 1 1 2 2 2l3 3h0l-11-6-2-1v1l1 1-9-5c-1 0-5-3-6-3-1-1-3-2-4-2-5-2-9-3-13-4h10z"></path><path d="M387 647h3c1 1 1 1 2 1h1a57.31 57.31 0 0 1-11-11c2 1 4 3 6 3h1c2 1 4 3 5 5v1 1l1 1c2 2 4 4 5 6v1l-2-1-2 2-5-6-4-3z" class="H"></path><path d="M391 650c3 0 4 2 7 4l-2 2-5-6z" class="Y"></path><path d="M351 628h2c1 1 4 3 6 4 1 0 5 2 6 2h1c3 1 6 3 10 5 1 1 3 2 4 3 0 1 1 2 2 2l3 3h0l-11-6-2-1v1l1 1-9-5c-1 0-5-3-6-3-1-1-3-2-4-2-5-2-9-3-13-4h10z" class="E"></path><path d="M364 637l2-1c3 1 6 3 8 5l-2-1v1l1 1-9-5z" class="D"></path><defs><linearGradient id="Bg" x1="391.072" y1="160.387" x2="390.825" y2="165.907" xlink:href="#B"><stop offset="0" stop-color="#7e7d7d"></stop><stop offset="1" stop-color="#999797"></stop></linearGradient></defs><path fill="url(#Bg)" d="M326 162h107-4c1 1 5 1 7 1v1h1l2 2h-6-75l1-1v-1h-13l1-1c-2-1-40 1-47-1h0c8 0 17 1 25 0h1z"></path><path d="M436 164h1l2 2h-6c-4-1-9-1-13-1l1-1h15z" class="T"></path><path d="M376 578c2 4 5 14 9 16h1l2-2 5 3c3 1 4 1 6 1h2l1 3 1 4v2c1 3 0 8 0 12 1 1 1 2 1 3v1 2l1 2v1l1 2v1c0 2 1 5 2 7h0s-1 0-2-1h0c-1-1-2-3-3-5-2-6-5-11-8-17-1-2-2-5-4-7l-4-5c-1-2-2-4-3-5h-1l1 1c2 5 5 8 8 13 1 1 1 2 2 4-5-5-8-11-11-16h-1c-1-2-1-4-2-5l-4-10v-5z" class="L"></path><path d="M393 595c3 1 4 1 6 1h2l1 3-2 1-3-2h-1c-1-1-2-1-3-3z" class="b"></path><path d="M274 449c1-3 0-8 0-11 0 3 0 8 1 11 1 2 1 5 3 8v1h0 1v-2l1-3h0l1-1c1-3 2-8 4-12v5h0 1v7c1 10 2 19 4 29l-1-1 1 6h0c-1 0-1-1-2-2 0-2 0-3-1-4 0 1 0 1-1 1l1 5c-2-2-2-3-3-5-1 0-1 2-1 2l-1 1h0-1v-1l-2-2-1-1v1h0c1 2 0 5 1 7l-1-2-2-5h0c2-5-2-11 0-15 0-1-1-3-1-4 0-3-1-5-1-8v-5z" class="H"></path><path d="M280 479l2-2v-1h1c0 1 0 3 2 4 1-1 0-3 0-5l1 6 1 5c-2-2-2-3-3-5-1 0-1 2-1 2l-1 1h0l-2-5z" class="P"></path><path d="M286 464c1 5 1 11 3 16l1 6h0c-1 0-1-1-2-2 0-2 0-3-1-4 0 1 0 1-1 1l-1-6 1-11z" class="B"></path><path d="M276 481c2-5-2-11 0-15l4 13 2 5h-1v-1l-2-2-1-1v1h0c1 2 0 5 1 7l-1-2-2-5h0z" class="C"></path><path d="M285 445h1v7c1 10 2 19 4 29l-1-1c-2-5-2-11-3-16l-1-16v-2-1z" class="O"></path><path d="M280 467c2-3 0-8 2-11 2-4 1-7 3-10v2c-2 8-2 16-2 24v1c-1-1-1-3-1-4-1 1-1 3-1 4h-1v-1-5z" class="T"></path><path d="M274 449c1-3 0-8 0-11 0 3 0 8 1 11 1 2 1 5 3 8v1h0 1v-2l1-3h0l1-1c1-3 2-8 4-12v5h0v1c-2 3-1 6-3 10-2 3 0 8-2 11v5l-1-1-1-1v-3c0-2 0-5-1-6s-1-1-1-2-1-3-2-5v-5z" class="X"></path><path d="M278 467v-1c1-1 1-4 1-6l1 1v6 5l-1-1-1-1v-3z" class="G"></path><defs><linearGradient id="Bh" x1="744.319" y1="343.877" x2="724.662" y2="362.141" xlink:href="#B"><stop offset="0" stop-color="#7f7e7e"></stop><stop offset="1" stop-color="#9a9998"></stop></linearGradient></defs><path fill="url(#Bh)" d="M724 358c2-4 5-8 7-12s4-9 8-13l-2 28c3 9 7 15 12 22v2l-1 1-2-1h0l-1-1h-2c-1-1-4-5-5-6v1l-1-2-5-5c0-1-2-4-3-5-1-3-3-9-5-9z"></path><defs><linearGradient id="Bi" x1="739.87" y1="356.799" x2="741.378" y2="385.844" xlink:href="#B"><stop offset="0" stop-color="#afadad"></stop><stop offset="1" stop-color="#dedddc"></stop></linearGradient></defs><path fill="url(#Bi)" d="M738 378c0-2-2-4-2-5-2-4-3-8-4-13 1-1 3-3 5-4v5c3 9 7 15 12 22v2l-1 1-2-1h0l-1-1h-2c-1-1-4-5-5-6z"></path><path d="M489 229c2 0 2 1 3 3 1 1 1 1 1 3l2 2c0 1 0 3 1 3 0 1 0 1 1 1v3l1 1h0l-1 1 1 4 1 5c1 4 2 8 2 12 1 5 1 11 1 17l-1 27c0 3-1 9 0 12-1 7-2 14-2 20 0 3-1 5 0 7-1-1-1-3-1-4l-1-44c0-16 0-32-3-48 1-2 0-7-1-9v-4l-2-6-2-6z" class="Z"></path><path d="M489 229c2 0 2 1 3 3 1 1 1 1 1 3l2 2c0 1 0 3 1 3 0 1 0 1 1 1v3l1 1h0l-1 1 1 4 1 5v3c0-1 0-2-1-3v3c-1-5-2-9-3-13-1-2-1-3-2-4l-2-6-2-6z" class="X"></path><defs><linearGradient id="Bj" x1="261.103" y1="590.815" x2="282.551" y2="617.778" xlink:href="#B"><stop offset="0" stop-color="#262625"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#Bj)" d="M261 588l1 1c1 0 2 1 2 1 1 0 2 1 2 1h1c3 2 5 6 8 8 2 1 5 3 8 4 1 0 4 1 4 2v1c1 0 2 1 3 1 1 1 3 2 4 3h0l-2 2h-5 0c-3 1-6 1-8 4h0v2 5c-2-3-5-4-7-7-2-2-3-5-5-7-3-7-6-13-6-21z"></path><defs><linearGradient id="Bk" x1="266.606" y1="597.064" x2="276.917" y2="599.331" xlink:href="#B"><stop offset="0" stop-color="#787573"></stop><stop offset="1" stop-color="#8b8c8c"></stop></linearGradient></defs><path fill="url(#Bk)" d="M267 591c3 2 5 6 8 8 2 1 5 3 8 4 1 0 4 1 4 2v1c1 0 2 1 3 1 1 1 3 2 4 3h0l-2 2h-5l-3-2c-6-2-14-10-17-17-1 0 0-1 0-2z"></path><path d="M294 610h-5l-1-1h-1c-3-1-8-3-10-6 0-1-2-2-3-3l1-1c2 1 5 3 8 4 1 0 4 1 4 2v1c1 0 2 1 3 1 1 1 3 2 4 3h0z" class="N"></path><path d="M559 263c1 1 1 1 3 1 5-1 9-6 12-10 0 2-1 4 0 6l1 1c0 1 0 2-1 3 2 0 3 0 5 1 0 1 0 2 1 3v1 1c1 0 1 0 2 1v1l2-2 1 1c-3 3-6 6-8 9 4-1 6-5 10-7l1 1 1-1c-1 2-3 5-4 7l-2 3-1-1h-2c-1 1-2 3-3 4-2 0-3 0-4-1-4-2-6-8-7-12l-2-6-5 3v-1c0-1 1-1 1-2l-2-2c-1 2-1 4-2 6v1 2l-1 2c-1-5 0-13 0-18h1c0 1 0 1 1 2s1 2 2 3z" class="I"></path><path d="M588 274l1-1c-1 2-3 5-4 7l-2 3-1-1h-2c3-3 5-6 8-8z" class="e"></path><path d="M572 280h0c-2-3-2-12-1-16 0-1 1-2 1-3s0-1 1-2v2c0 5-2 13 0 17v1l-1 1z" class="H"></path><defs><linearGradient id="Bl" x1="578.336" y1="264.681" x2="576.644" y2="277.155" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="#7f7e7e"></stop></linearGradient></defs><path fill="url(#Bl)" d="M574 264c2 0 3 0 5 1 0 1 0 2 1 3v1 1c1 0 1 0 2 1v1l2-2 1 1c-3 3-6 6-8 9-1 1-2 1-3 1l-2-1 1-1h1v-2c-1-4 0-9 0-13z"></path><path d="M727 420c3 2 7 4 10 7 0 1 0 3 1 4 0 1 2 2 2 3l3 6 2-1c0 1 1 3 1 4l4 10c1 5 0 9 1 14h1c0 2-1 3-1 5l-3 9c-1-2-1-5-1-7v4h0v-1c-1-2-1-4-1-6l-1 1c1 2 1 3 0 4h0c0-1 0-2-1-3h0-1v-5h0l-2-11-1-5-2-8h-1c1-2-1-4-1-5l-1-1c-1-4-9-15-8-18z" class="Q"></path><path d="M740 452h2c1 2 2 4 2 6l-3-1-1-5z" class="H"></path><path d="M746 471v-4l1-10h0v2c1 5 1 10 0 15v4h0v-1c-1-2-1-4-1-6z" class="Y"></path><path d="M736 439h0c2 1 3 2 4 4l1-1v1l1 1v-1c-1-1-1-2-1-3 1 1 2 3 3 5-1 1-1 3-1 5h0v-1 2h-1c0-3-1-5-2-7h-2-1c1-2-1-4-1-5z" class="Z"></path><path d="M741 457l3 1 1 14c1 2 1 3 0 4h0c0-1 0-2-1-3h0-1v-5h0l-2-11z" class="B"></path><defs><linearGradient id="Bm" x1="743.086" y1="477.277" x2="753.914" y2="459.723" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#8d8b89"></stop></linearGradient></defs><path fill="url(#Bm)" d="M741 440c0-1-1-2-2-3l1-3 3 6 2-1c0 1 1 3 1 4l4 10c1 5 0 9 1 14h1c0 2-1 3-1 5l-3 9c-1-2-1-5-1-7 1-5 1-10 0-15l1 3 1-1c0-3 1-5 0-7-1-1-1-2-1-3h0v-1c-1-1-1-2-2-2h-1l-1-3c-1-2-2-4-3-5z"></path><path d="M741 440c0-1-1-2-2-3l1-3 3 6 3 8h-1l-1-3c-1-2-2-4-3-5z" class="M"></path><path d="M727 420c3 2 7 4 10 7 0 1 0 3 1 4 0 1 2 2 2 3l-1 3c1 1 2 2 2 3s0 2 1 3v1l-1-1v-1l-1 1c-1-2-2-3-4-4h0l-1-1c-1-4-9-15-8-18z" class="X"></path><defs><linearGradient id="Bn" x1="597.729" y1="664.976" x2="619.866" y2="666.987" xlink:href="#B"><stop offset="0" stop-color="#414040"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#Bn)" d="M596 695l1-2v-1l1-1h-2s0-1-1-1v2c-1 1-1 0-1 1l-2 4-2 3v1h-1c4-10 10-18 13-28l3-9 3-14c0-2 1-5 2-6 1-2 7-3 9-3s3 0 4 1c0 3-1 5-2 7-2 3-3 6-5 9-7 12-12 26-20 37z"></path><defs><linearGradient id="Bo" x1="424.98" y1="802.134" x2="434.02" y2="783.866" xlink:href="#B"><stop offset="0" stop-color="#898988"></stop><stop offset="1" stop-color="#b0afae"></stop></linearGradient></defs><path fill="url(#Bo)" d="M397 765c2 0 3 2 6 4l3 3v-2c-1-1-4-3-4-5 6 6 13 11 20 16 5 4 11 6 16 10 4 4 8 8 11 13 1 2 3 5 4 8h1c2 1 5 2 7 4l8 5c-1 1-2 2-3 2l1 2h-2v-1c-1-2-5-3-7-5h-1c-1 0-2-1-3-2 0 1 2 3 3 4 0 1 3 3 3 5h0c-8-10-16-19-25-28-12-11-27-21-38-33z"></path><path d="M454 812c2 1 5 2 7 4l8 5c-1 1-2 2-3 2-3-1-7-3-9-5-3-2-4-3-4-6h1z" class="d"></path><path d="M688 570l1 1h1l-1 1-1 1 2 1 1-1c0 2-3 7-3 8l1-1v1c-1 1-2 1-2 4h0l1 2-2 1c-1 1-2 3-3 5-3 3-6 6-7 9-2 4-5 7-8 10h-3 0c-1 0-2 0-3-1l-6 9-1 2-2-1 5-7c-1-2 1-2 0-4h-2l6-7 14-16 6-8 6-9z" class="U"></path><path d="M674 596v-2-1l1-1c1-1 1-2 2-3 2-2 4-2 6-5h1l-10 12z" class="V"></path><path d="M691 573c0 2-3 7-3 8l1-1v1c-1 1-2 1-2 4h0l-3 3h0l-2-1c-2 4-5 7-8 10h-1l1-1 10-12c2-1 3-4 4-6 1-1 1-2 2-4l1-1z" class="G"></path><path d="M673 597h1c-4 5-8 9-12 14l-6 9-1 2-2-1 5-7 15-17z" class="O"></path><path d="M688 570l1 1h1l-1 1-1 1 2 1c-1 2-1 3-2 4h-1l-3 2c-2 3-5 7-8 7l6-8 6-9z" class="M"></path><path d="M688 573l2 1c-1 2-1 3-2 4h-1l-3 2 4-7zm-1 12l1 2-2 1c-1 1-2 3-3 5-3 3-6 6-7 9-2 4-5 7-8 10h-3 0c-1 0-2 0-3-1 4-5 8-9 12-14 3-3 6-6 8-10l2 1h0l3-3z" class="N"></path><path d="M687 585l1 2-2 1c-1 1-2 3-3 5-3 3-6 6-7 9-2 4-5 7-8 10h-3 0l19-24h0l3-3z" class="J"></path><defs><linearGradient id="Bp" x1="543.64" y1="318.086" x2="532.408" y2="316.865" xlink:href="#B"><stop offset="0" stop-color="#3c3c3b"></stop><stop offset="1" stop-color="#575657"></stop></linearGradient></defs><path fill="url(#Bp)" d="M539 257c1 1 2 2 2 4l-2 109c-1 3 0 6 0 9v13h-1v-7c-1-1 0-4-1-6-1-4-1-9-1-13v-77-10h-1v-3c0-4 0-12 3-14 1-1 1-4 1-5z"></path><path d="M535 279v-3c0-4 0-12 3-14l-2 27v-10h-1z" class="N"></path><path d="M448 351v-3c1 0 0 0 1-1h0c1-2 2-4 3-5 1 0 2-1 2-2l1-1c1 3 3 6 5 8l10 13c0 3 2 5 3 8 1 1 2 2 2 3 1 0 1 0 1 1l-1 1c-1-2-3-4-4-5l-1-1v1c-2-1-2-2-3-2 1 3 2 7 4 9l1 1c1 1 2 2 2 4-1 0-3 0-3-1h-1c-3-1-4-4-6-6h-1c0-2 1-1 0-2-1 0-2-1-2-2l-2-2-1-3c-1 0-2-1-3-2l-3-3c-1 0-1-1-2-1 0 1-1 1-1 2l-1 1h0l-1-1c0-4 1-6 1-9z" class="G"></path><path d="M448 351v-3c1 0 0 0 1-1h0c1-2 2-4 3-5 1 0 2-1 2-2l1-1c1 3 3 6 5 8l10 13c0 3 2 5 3 8 1 1 2 2 2 3 1 0 1 0 1 1l-1 1c-1-2-3-4-4-5l-1-1c-4-9-12-17-17-25-2 3-3 5-5 9z" class="C"></path><path d="M576 421c0-2 2-8 2-10 1-1 1-2 2-3 2 1 3 2 4 3l12 14c2 2 6 6 7 8v1c-3 2-6 3-8 5l-1 1c-2-2-4-4-7-5 2 2 4 3 6 5-6-2-11-7-15-11-3-3-2-4-2-8z" class="O"></path><path d="M582 417l-3-2h1c4 2 8 6 11 10 0 1 1 2 0 2-1 2-3 2-4 3-2 0-4-1-5-1l-1-1c-1 0-2-1-3-2 0-2 1-3 2-3v1c0 1 0 1 1 3h3c3 1 3 1 6 0v-1c-1-3-6-7-8-9z" class="I"></path><path d="M576 421c0-2 2-8 2-10 1-1 1-2 2-3 2 1 3 2 4 3-1-1-2-1-4-1-1 1-1 2-1 4h1v1h-1l3 2c-1 1-1 1-1 2h-1l-2 1v2h1c1 0 0 0 1-1l1-1v1 1h0l1 1 1-1 1 1c-2 0-2 0-4 1v-1c-1 0-2 1-2 3-1-1-1-1-1-2s0-1-1-3z" class="K"></path><path d="M578 420v-1l1-1h1v1l-2 1z" class="L"></path><path d="M580 419h1c0-1 0-1 1-2 2 2 7 6 8 9v1c-3 1-3 1-6 0h-3c-1-2-1-2-1-3 2-1 2-1 4-1l-1-1-1 1-1-1h0v-1-1l-1 1c-1 1 0 1-1 1h-1v-2l2-1z" class="D"></path><path d="M852 351v1c-1 1-3 3-3 5-1 2-3 4-5 6h0c4-1 5-6 9-6h-1c-1 2 1 0 0 2l-2 1-2 2 1 1c-3 3-6 7-10 9l1 1c-4 4-8 8-13 11h0c0-1 0-1-1-2h-1c-1 1-3 2-4 3l-1-1-9 8v-2c3-2 5-4 8-6l-3 1c-1 0-2 2-3 2l1-2s-1 0-1-1l1-1c0-1-1-1-1-1v-2s0-1 1-1l1-1c4-3 8-8 11-12l3-2c1 0 2 0 3-1v1h0 1l1-1c1 1 1 1 0 2v3c3-3 5-6 7-9l3-3c1 0 2-1 2-1v2c1 0 2-2 3-3l3-3z" class="V"></path><path d="M826 382c1-1 3-2 4-3 3-2 6-6 9-7l1 1c-4 4-8 8-13 11h0c0-1 0-1-1-2z" class="U"></path><path d="M852 351v1c-1 1-3 3-3 5-1 2-3 4-5 6l-11 9c-5 4-9 8-13 12l-9 8v-2c3-2 5-4 8-6l15-16c3-3 5-6 7-9l3-3c1 0 2-1 2-1v2c1 0 2-2 3-3l3-3z" class="B"></path><defs><linearGradient id="Bq" x1="817.321" y1="389.011" x2="828.917" y2="361.214" xlink:href="#B"><stop offset="0" stop-color="#c2bfc0"></stop><stop offset="1" stop-color="#e8e8e6"></stop></linearGradient></defs><path fill="url(#Bq)" d="M829 364c1 0 2 0 3-1v1h0 1l1-1c1 1 1 1 0 2v3l-15 16-3 1c-1 0-2 2-3 2l1-2s-1 0-1-1l1-1c0-1-1-1-1-1v-2s0-1 1-1l1-1c4-3 8-8 11-12l3-2z"></path><defs><linearGradient id="Br" x1="611.347" y1="674.207" x2="642.402" y2="645.059" xlink:href="#B"><stop offset="0" stop-color="#5e5d5c"></stop><stop offset="1" stop-color="#acaaaa"></stop></linearGradient></defs><path fill="url(#Br)" d="M636 638l11-4c-1 1-2 2-4 3s-4 2-5 3l2 1 1-1c1 1-1 1 0 2l-2 3 1 1 1-1h2l-10 9h3l1-1v3c-1 1-2 2-2 4-1 1-2 2-4 2l-1 1c-1 2-4 6-6 7l-7 8-1 1c-2-1-2-2-3-4l4-7c2-1 4-6 5-7 0 0 0-1-1 0h-1l2-5c2-2 2-5 2-8l1-2c1-2 2-3 3-5 2-2 6-3 8-3z"></path><path d="M633 654h3l-5 5-4 2c2-2 4-5 6-7z" class="H"></path><path d="M627 661l4-2c-1 3-4 6-6 9h0c-1-1 1-2 1-3l-3 3c1-2 3-4 4-6v-1z" class="B"></path><path d="M636 654l1-1v3c-1 1-2 2-2 4-1 1-2 2-4 2l-1 1c-1 2-4 6-6 7l1-2h0c2-3 5-6 6-9l5-5z" class="F"></path><path d="M636 638l11-4c-1 1-2 2-4 3s-4 2-5 3-2 3-3 3c-3 3-5 6-7 9-2 2-4 7-6 9 0 0 0-1-1 0h-1l2-5c2-2 2-5 2-8l1-2c1-2 2-3 3-5 2-2 6-3 8-3z" class="S"></path><path d="M636 638l11-4c-1 1-2 2-4 3s-4 2-5 3-2 3-3 3h-1c-1 1-3 2-4 4 0 1-1 1-1 2l-1-1 2-2c2-3 5-5 6-8h0z" class="Y"></path><defs><linearGradient id="Bs" x1="586.935" y1="733.085" x2="596.841" y2="740.704" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#606060"></stop></linearGradient></defs><path fill="url(#Bs)" d="M624 694h7c-10 3-19 11-24 20l-3 3v1h-1c0 1 0 1-1 2l-3 4-10 15c-1 1-2 2-3 4-3 4-5 9-7 14l-11 24c-1 0-2 1-2 2l-3 3h0c1-2 1-4 1-6-1 1 0 1-1 1-1 2-1 5-2 6-1 0-1-1-1-1l2-4 1-1c-1-2 0-4 1-5 1-2 2-2 3-3s1-1 1-2 1-2 2-3 2-7 3-9c0-2 2-4 3-6 3-5 6-10 9-16l5-7 23-31h1 1v2l4-4c2-2 3-1 5-3z"></path><path d="M563 781v-1c1-2 2-3 3-4h1c-1 2-2 3-3 4s0 1-1 1z" class="E"></path><defs><linearGradient id="Bt" x1="563.597" y1="201.266" x2="534.898" y2="250.835" xlink:href="#B"><stop offset="0" stop-color="#4c4b4b"></stop><stop offset="1" stop-color="#8c8b89"></stop></linearGradient></defs><path fill="url(#Bt)" d="M535 243h0c1-4 3-8 4-11 3-7 6-15 11-20 3-4 7-7 11-9 1-1 2-2 4-2 1 1 1 1 1 3s0 4-1 6l-1 1c-5 2-9 10-12 15l-6 13v1l-3 8-1 4h0l-1-1-1 3h-1c-1-2-1-4-1-5 1-1 1-4 1-5v-2c1-2 1-3 1-4v-4l-1 2c-2 2-2 4-3 7h-1z"></path><path d="M543 248h0 0l-1-1v-2c0-2 2-10 4-12v3 1 1 1 1l-3 8z" class="H"></path><path d="M383 633l9 2c1 0 2 1 3 2h3c1 1 2 2 2 3 1 1 0 0 1 2l2 2c1 0 1 1 2 1h0c2 2 4 3 5 6l4 6c1 2 3 6 3 8 3 6 7 12 9 19h-1 0l1 4 1 1c0 1 1 2 1 3v1l1 1h-1c-1-1-2-2-2-3l-1-2v-2c-1-1-1-2-2-3h-1s0-2-1-2c0-2-1-5-3-6h-1l-3-3c-2-3-4-6-7-9 0-1-1-2-2-3-1-2-4-4-5-6v-1c-1-2-3-4-5-6l-1-1v-1-1c-1-2-3-4-5-5-2-2-4-4-6-7z" class="f"></path><path d="M412 659c1-2-2-6-2-8l4 6c1 2 3 6 3 8-1 2 0 3 0 6-1-4-5-9-5-12z" class="R"></path><path d="M395 637h3c1 1 2 2 2 3 1 1 0 0 1 2l2 2c1 0 1 1 2 1h0c2 2 4 3 5 6 0 2 3 6 2 8-2-5-6-11-9-14l-1 1c-2-2-5-7-7-9z" class="C"></path><path d="M402 646c3 2 4 6 6 9l10 21h-1l-3-3c-2-3-4-6-7-9 0-1-1-2-2-3h1l2 2h1c-2-4-4-8-5-11-1-1-1-2-1-3h0c0-1-1-2-1-3z" class="E"></path><path d="M383 633l9 2c1 0 2 1 3 2 2 2 5 7 7 9 0 1 1 2 1 3h0c0 1 0 2 1 3 1 3 3 7 5 11h-1l-2-2h-1c-1-2-4-4-5-6v-1c-1-2-3-4-5-6l-1-1v-1-1c-1-2-3-4-5-5-2-2-4-4-6-7z" class="B"></path><path d="M383 633l9 2h0c1 2 3 3 4 4 0 2 1 4 2 6 1 3 3 5 4 7l-6-5-1 1-1-1v-1-1c-1-2-3-4-5-5-2-2-4-4-6-7z" class="P"></path><defs><linearGradient id="Bu" x1="298.12" y1="380.032" x2="289.886" y2="411.337" xlink:href="#B"><stop offset="0" stop-color="#9d9c9a"></stop><stop offset="1" stop-color="#cfcecd"></stop></linearGradient></defs><path fill="url(#Bu)" d="M293 382c2-3 4-6 6-8l2 2v2 1l1 1c0 1 1 3 2 4v1c-2 3 0 7 0 10v2h-2-2v1c1 1 3 1 4 2v1c-1 1-2 1-4 2-3 1-5 3-8 5-1 0-4 3-4 4l-1-2c-2-1-4-1-6 0l-2 2 5-13v-1l3-5 1-2 3-6 2-3z"></path><path d="M291 385l1 1 1 1-3 3s-1 0-2 1l3-6z" class="Z"></path><path d="M300 397s-1-2-2-2c0-2 0-6 1-8 0 2 0 3 1 4 0 3 1 4 2 6h-2z" class="T"></path><path d="M293 382c2-3 4-6 6-8l2 2v2l-1 13c-1-1-1-2-1-4 1-2 0-5 0-7l1-1-1-1c-3 3-6 5-7 8l-1-1 2-3z" class="Q"></path><path d="M300 391l1-13v1l1 1c0 1 1 3 2 4v1c-2 3 0 7 0 10v2h-2c-1-2-2-3-2-6z" class="N"></path><path d="M608 267c0-1 1-2 2-2 1-3 3-4 5-6l1 1-1 1c-2 0-3 1-4 2 0 1 0 2-1 3h0 1 0l1-1c1-1 2-2 3-2h0l-3 3h-1c1 1 1 0 2 0 1-1 2-1 3-2l1 1c-1 0-1 1-2 1 0 1-1 1-1 1v1c2-1 3-2 5-3l-2-1h0c1 0 1 0 1-1h1 0 2v2c-9 4-16 12-22 20l-1-1v-1l-1 1c-4 3-6 7-8 11h0c0 2 0 3 1 5-3 7-5 14-5 22 0 1-1 3 0 4v1c0 4 0 7-1 10-1-3-3-7-3-10s0-6-1-9c-2-2-1-4-2-6l3-8v-1c-1 1-1 1-1 2s-1 2-1 3h0c-1 1 0 2-1 3h-1l-1-2 1-1v-2h1v-1c0-1 0-1 1-2 0-1 1-2 1-2 2-2 3-5 5-7 3-5 5-9 8-13 4-6 10-10 15-14z" class="R"></path><path d="M586 297c4-7 9-15 16-21 1-2 6-6 8-6h0c-5 4-9 9-13 14-4 3-6 7-8 11h0 0c-2 2-2 4-2 6-1 2-3 5-4 7 0 1 0 3-1 4-1-2-1-3 0-5l1-2c0-1 0-2 1-2v-1 3h0l1-3c1-1 1-2 1-3l1-1v-1h-1 0z" class="S"></path><path d="M608 267v1c-5 5-10 10-15 16-4 6-7 12-12 17h-1c2-2 3-5 5-7 3-5 5-9 8-13 4-6 10-10 15-14z" class="E"></path><defs><linearGradient id="Bv" x1="591.193" y1="318.046" x2="577.946" y2="310.996" xlink:href="#B"><stop offset="0" stop-color="#2f3030"></stop><stop offset="1" stop-color="#5b5a59"></stop></linearGradient></defs><path fill="url(#Bv)" d="M586 297h0 1v1l-1 1c0 1 0 2-1 3l-1 3h0v-3 1c-1 0-1 1-1 2l-1 2c-1 2-1 3 0 5 1-1 1-3 1-4 1-2 3-5 4-7 0-2 0-4 2-6h0c0 2 0 3 1 5-3 7-5 14-5 22 0 1-1 3 0 4v1c0 4 0 7-1 10-1-3-3-7-3-10s0-6-1-9c-2-2-1-4-2-6l3-8c1-3 3-5 5-7z"></path><defs><linearGradient id="Bw" x1="828.01" y1="112.429" x2="755.267" y2="160.232" xlink:href="#B"><stop offset="0" stop-color="#aca5a3"></stop><stop offset="1" stop-color="#dce0e1"></stop></linearGradient></defs><path fill="url(#Bw)" d="M846 134h0l-1 5h-72-19c-4 0-7 1-10 0-1 0-2-1-4-2v-2h69 25c4 0 8 0 12-1z"></path><defs><linearGradient id="Bx" x1="278.385" y1="502.96" x2="253.846" y2="495.3" xlink:href="#B"><stop offset="0" stop-color="#151314"></stop><stop offset="1" stop-color="#454645"></stop></linearGradient></defs><path fill="url(#Bx)" d="M249 471l6 9v1h-1l1 1 1-1 1-1c1 1 2 3 3 4h3c-1-1-2-3-3-4v-1l3 2h1l-1-3v-2h1v-2h0l2 5c1 1 1 1 1 2v1h1v-1c2 4 3 8 5 12 1 1 1 3 2 4 1 0 2 1 2 2v1l1 1 1 3-2 1-1-2h-1l3 6v1h-1c-1 0-5-1-6-1s-2-2-2-2l-7-8c-2-4-6-6-9-9l-2-1v-1l-1-2c0-2-2-3-3-5l1-1c2-1 3-1 4-2l-2-2c-1-1-1-2-2-4l1-1z"></path><path d="M260 484h3c1 2 2 4 4 5h1c1 1 2 2 1 3l-1 1c-1-1-1-2-3-3v1c1 1 1 2 1 3-1-1-2-2-3-4 0-1-1-2-2-3 0-1-1-2-1-3z" class="S"></path><path d="M256 481l1-1c1 1 2 3 3 4 0 1 1 2 1 3 1 1 2 2 2 3l-1 1h0v3l-3-3-3-2h0v-1l1-1c-1 0-1-1-1-2h0c1 1 3 2 4 3-1-2-2-5-4-7z" class="F"></path><path d="M259 491s1 1 2 1v1c0-2-1-4-3-5h2v-1-1l1 1c1 1 2 2 2 3l-1 1h0v3l-3-3z" class="J"></path><defs><linearGradient id="By" x1="251.291" y1="486.536" x2="251.214" y2="478.498" xlink:href="#B"><stop offset="0" stop-color="#555454"></stop><stop offset="1" stop-color="#6e6c6c"></stop></linearGradient></defs><path fill="url(#By)" d="M249 471l6 9v1h-1l1 1 1-1c2 2 3 5 4 7-1-1-3-2-4-3h0c0 1 0 2 1 2l-1 1v1h0l-1-1-1 1v1h-1l-2-1v-1l-1-2c0-2-2-3-3-5l1-1c2-1 3-1 4-2l-2-2c-1-1-1-2-2-4l1-1z"></path><path d="M251 488l2-2c1 0 1 1 2 2l-1 1v1h-1l-2-1v-1z" class="F"></path><defs><linearGradient id="Bz" x1="271.43" y1="501.487" x2="275.567" y2="489.012" xlink:href="#B"><stop offset="0" stop-color="#3c393b"></stop><stop offset="1" stop-color="#565755"></stop></linearGradient></defs><path fill="url(#Bz)" d="M264 474h0l2 5c1 1 1 1 1 2v1h1v-1c2 4 3 8 5 12 1 1 1 3 2 4 1 0 2 1 2 2v1l1 1 1 3-2 1-1-2h-1l3 6c-2-2-3-5-5-7l-5-9 1-1c1-1 0-2-1-3h-1c-2-1-3-3-4-5-1-1-2-3-3-4v-1l3 2h1l-1-3v-2h1v-2z"></path><path d="M264 476c1 4 2 9 4 13h-1c-2-1-3-3-4-5-1-1-2-3-3-4v-1l3 2h1l-1-3v-2h1z" class="T"></path><defs><linearGradient id="CA" x1="630.596" y1="373.693" x2="610.259" y2="376.5" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#40403f"></stop></linearGradient></defs><path fill="url(#CA)" d="M601 356l1-1h2c0 1 1 1 1 2 1 2 1 4 3 5 1 1 4 2 6 3v-1l1-1c2-2 2-4 3-6l2-1c2 1 3 4 5 6h1c3 6 6 10 6 17l-3 6h-2 0c-3 0-4-1-7-2l-1-1c-5-3-9-8-12-13-1-2-3-3-3-5h0c-2-3-3-5-3-8z"></path><path d="M618 363l3 2 1-1 2 2c2 5 2 8 2 14h0c-1-1-1-2-1-3v-2c-1-4-6-7-7-12z" class="O"></path><path d="M618 357l2-1c2 1 3 4 5 6h1l-1 2-1 2-2-2-1 1-3-2-2 3c-1-1-1-1-2-1v-1l1-1c2-2 2-4 3-6z" class="C"></path><path d="M619 361v-1-2c2 1 3 2 4 4-1 0-2-1-4-1z" class="K"></path><path d="M619 361c2 0 3 1 4 1l2 2-1 2-2-2-3-3z" class="R"></path><path d="M601 356l1-1h2c0 1 1 1 1 2 1 2 1 4 3 5 1 1 4 2 6 3 1 0 1 0 2 1l2-3c1 5 6 8 7 12 0 2 0 3-1 4h0c-2-1-4-2-5-3l-4-3c-1 0-1 0-2-1h0c-3-2-5-4-7-7h0l-2-2v1h0c-2-3-3-5-3-8z" class="O"></path><path d="M608 362c1 1 4 2 6 3 1 0 1 0 2 1l2-3c1 5 6 8 7 12 0 2 0 3-1 4h0c-2-1-4-2-5-3l-4-3c-1 0-1 0-2-1h0c-3-2-5-4-7-7 2 0 4 3 5 4l9 6c1 0 2 1 3 1-1-1-2-2-3-2-1-1-2-2-2-3v-1c1 1 1 1 2 0-2-1-3-3-5-3-1 0-1 0-2-1-3-1-4-1-5-4z" class="L"></path><defs><linearGradient id="CB" x1="174.427" y1="309.793" x2="169.899" y2="312.023" xlink:href="#B"><stop offset="0" stop-color="#454545"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#CB)" d="M163 301c-4-11-4-20-4-32h0l1 7h1v-4h0c1 2 0 4 0 6s0 4 1 7c2 13 9 23 15 34 2 5 5 10 8 14 1 3 2 4 4 7 1 2 2 5 3 8 2 3 4 5 6 9 1 2 6 6 8 8h-1c-4-3-8-7-12-11l-2-2c0-1-10-11-11-12 0-2-3-5-4-7v-1l-9-17-6-14v-2c0 1 1 1 2 2h0 0z"></path><path d="M161 299c0 1 1 1 2 2h0 0c2 3 4 7 6 10l8 15 9 14c1 3 4 5 5 8 1 2 2 4 2 6l-2-2c0-1-10-11-11-12 0-2-3-5-4-7v-1l-9-17-6-14v-2z" class="f"></path><defs><linearGradient id="CC" x1="410.897" y1="719.996" x2="416.389" y2="679.393" xlink:href="#B"><stop offset="0" stop-color="#50504f"></stop><stop offset="1" stop-color="#6b6a6a"></stop></linearGradient></defs><path fill="url(#CC)" d="M393 675c10 4 19 10 27 18 7 8 13 17 18 26 3 4 7 9 9 14-1-2-3-3-4-4l-3-4h-1c-1 0-9-14-11-16h-1c1 1 2 2 2 3v1c-2-2-3-4-5-6h0c-1-3-7-8-10-10l-6-4v-1c-1-1-2-3-4-3-1-1-2-1-4-2h1c-2-1-5-2-6-3l-12-3h-8c1-1 2-3 4-3h1l1-1v-2h3c1 1 2 1 3 1l1-1c1 0 3 1 5 0z"></path><path d="M381 677h6c-2 2-4 0-7 2l-1 1c2 1 3 1 4 1h-8c1-1 2-3 4-3h1l1-1z" class="P"></path><path d="M380 679c4 0 8-1 12 0 2 0 4 1 6 2l10 6h0v1h0l3 3c-2-1-4-2-5-3s-2-2-4-2c-2-1-5-3-7-2l-12-3c-1 0-2 0-4-1l1-1z" class="E"></path><path d="M395 684c2-1 5 1 7 2 2 0 3 1 4 2s3 2 5 3c3 2 7 5 9 8 2 1 2 3 4 5 1 1 3 3 4 5h-1c1 1 2 2 2 3v1c-2-2-3-4-5-6h0c-1-3-7-8-10-10l-6-4v-1c-1-1-2-3-4-3-1-1-2-1-4-2h1c-2-1-5-2-6-3z" class="O"></path><path d="M341 678l-9 1c-2 1-3 1-4 1 0-1 1-1 1-2-3 0-8-1-11-3 2-1 4 0 5 1 2 0 4-2 6-2s4 2 6 2c1 0 3-2 4-2 4-3 7-4 12-4 2 0 5 0 7 1h5 0c2 1 2 1 4 1h3c-1 1-1 1-1 2h-4l5 1v1l4 1c0 1 0 0-1 1l-1 2c-1 1-3 1-4 2v1l-8 3v-1c1-2 5-3 7-4v-1c1-1 1-2 1-2-1 0-1-1-2 0h-5c-6 0-14 7-18 10l-3 4-1-1c-3 4-6 8-8 13h-2v1l-2 2 1-3c1-11 7-17 16-24l-3-2z" class="V"></path><path d="M341 678l8-1-5 3-3-2z" class="b"></path><path d="M361 675l1-1h-3v-1-1l-6-1h0 10c2 1 2 1 4 1h3c-1 1-1 1-1 2h-4c-1 0-2 1-4 1z" class="U"></path><path d="M361 675c2 0 3-1 4-1l5 1v1l4 1c0 1 0 0-1 1l-1 2c-1 1-3 1-4 2v1l-8 3v-1c1-2 5-3 7-4v-1c1-1 1-2 1-2-1 0-1-1-2 0h-5c-6 0-14 7-18 10l-3 4-1-1c6-7 16-10 22-16z" class="N"></path><path d="M722 423c-1-3-1-7-1-11l6 8c-1 3 7 14 8 18l1 1c0 1 2 3 1 5 0 1 0 1 1 2v2c2 9 5 19 3 28-2 5-3 11-5 16 0-7 0-14 1-21-1-4-2-6-3-9h0c-3-4-8-7-9-12l1-20v-4c0-1-2-3-3-3h-1z" class="G"></path><path d="M735 438l1 1c0 1 2 3 1 5 0 1 0 1 1 2v2c2 9 5 19 3 28-2 5-3 11-5 16 0-7 0-14 1-21-1-4-2-6-3-9h1c1 0 2 1 3 2 1 3 1 6 0 8h0c0 4-1 9-1 14v-1c1-2 1-1 1-2v-3c1-2 1-3 1-4 1-2 0-7 0-9h0v-5l-3-15c0-2 0-5-1-6v-2-1z" class="H"></path><defs><linearGradient id="CD" x1="851.185" y1="327.151" x2="872.354" y2="367.888" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#f5f4f3"></stop></linearGradient></defs><path fill="url(#CD)" d="M887 313h1l1-5h3c1 0 1 1 1 2-3 15-8 28-17 41l4-2 1 1c-3 2-6 4-8 6l-12 12-16 13 9-4c-4 4-11 7-16 10h0c-2 0-3-1-4 0h-2-1c-1 0-1 1-2 1h-1c6-6 14-10 20-15 14-11 27-26 34-42 2-5 3-11 5-17v-1z"></path><path d="M272 332c0 2 2 2 3 4l1 1 2 2c1 2 2 4 4 5 1 0 1 1 2 2v2h1l-4 9c-2 5-5 9-8 13-1 2-2 3-4 4h1v-2h1c-1-1-3-3-3-5h-1c0-1 0-1-1-2-1-2-2-3-4-5h0l-1-1c-2-1-1-1-2-2h-2c8-7 13-14 15-25h0z" class="K"></path><path d="M474 422h1 0l-1-4-1-4c1 1 2 1 3 2 1 0 1 1 2 1 3 1 3 5 4 7 1 3 1 6 1 9l-2 6v5c1 4 2 7 4 10 3 7 9 14 8 22l-1 1-1-2v-1h0v7h-1c0 1 0 2 1 3h-1 0c-1 3 0 6-1 9v14c-2 3-1 5-3 7h0c-1-3 0-6 0-9 1-2 0-2 0-3-1-1 0-4 0-5v-16c-1-3 0-5-1-8l-2-8v-4l-2-4-2-4c0-3-1-5-2-7-1-4-1-7-1-11l-2-13z" class="J"></path><path d="M483 461l1 3c2 4 3 12 2 17-1-3 0-5-1-8l-2-8v-4z" class="e"></path><path d="M481 444c-2-3-2-8-3-12-1-2-1-4-2-7h1c0 2 1 3 1 4l3 9v1 5z" class="D"></path><path d="M552 226c3-5 7-13 12-15l-9 15c-8 17-11 34-4 52 3 8 9 16 15 22 2 3 4 5 6 7v1c-3 2-4 5-5 8-4 6-8 13-11 21l-9 23h0l-1 1v-5c1-2 2-6 3-9l6-17c3-7 8-14 10-22v-1c0-3-4-6-6-8-2-3-4-5-5-8-6-11-10-23-10-36 1-5 2-9 2-14v-1-1l6-13z" class="g"></path><path d="M552 226c3-5 7-13 12-15l-9 15v-2l1-1s0-1 1-1c0-1 0-1 1-2 0-1 0-1 1-2-2 1-4 6-6 7l-1 1z" class="N"></path><defs><linearGradient id="CE" x1="525.361" y1="605.547" x2="547.182" y2="533.814" xlink:href="#B"><stop offset="0" stop-color="#262524"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#CE)" d="M530 519c3 3 2 7 4 11 0 1 1 4 1 6-2 3 0 13 1 16v2h1c1 3 2 6 3 10 0 2 1 4 2 6-2 2-1 4-1 7 2 10 3 22 1 33 0 1 0 3-1 4l1 3c-1 1-1 0-1 1v-1h-1v-5c-1-2-2-5-2-7h0c-1-2-1-6-1-7v-1-1 15 2 1c0 2 1 4 1 5l1 2 1 1v2c-1-1-2-2-2-3h0c-1-1-1-2-1-3l-1-2v-1-3h-1v-3c-1-2 0-16 0-19-2-2-1-6-1-7-1-2 0-5 0-7-2-2-1-7-1-9v-1h1v-12c0-4-1-8-2-11l-1-11c0-2-1-5-1-7v-6z"></path><path d="M536 554h1c1 3 2 6 3 10 0 2 1 4 2 6-2 2-1 4-1 7-1-2-1-4-1-6l-1 1v-1l-3-17z" class="R"></path><path d="M539 572l1-1c0 2 0 4 1 6 2 10 3 22 1 33 0 1 0 3-1 4-1-1 0-4 0-6v-6c1-11-1-20-2-30z" class="h"></path><defs><linearGradient id="CF" x1="168.109" y1="361.213" x2="158.536" y2="370.494" xlink:href="#B"><stop offset="0" stop-color="#c9c8c7"></stop><stop offset="1" stop-color="#f9f8f8"></stop></linearGradient></defs><path fill="url(#CF)" d="M128 322h1l1 3v1l3-2v-1c-1-1-1-2-1-3h-1c1-1 0-1 0-2s0-2-1-2v-4c3 12 7 23 14 33 6 10 13 18 22 26l9 7 21 16 5 5c0 1 0 1 1 2-1 1-1 1-2 1-2-2-2-2-4-2l-25-19c-6-4-11-10-16-15-6-5-11-9-16-14-2-2-4-4-5-7 3 2 7 6 10 9l-6-9c-4-7-8-15-10-23z"></path><defs><linearGradient id="CG" x1="746.078" y1="628.807" x2="732.215" y2="617.125" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#8d8b8a"></stop></linearGradient></defs><path fill="url(#CG)" d="M748 610c1 0 3 0 4-1 0 1 0 2-1 3h2v6l1 2 2-1v1c-4 4-11 10-17 12-2 1-5 1-8 2-6 0-10 0-15-2l-3-2-4-1c2-1 4 0 6-1h6s1 0 1-1l3-3c1-1 2-1 2-2 2-1 4-2 7-2l3-1v-3l-2-1-4 1c-2-1-4 0-5-1l3-1 1-1 2-2c2-1 5-1 7-1h5 4z"></path><path d="M725 624l1 4c-2 2-4 2-6 2h-1l2-2s1 0 1-1l3-3z" class="G"></path><path d="M739 615l7-1 1 1c-3 3-6 4-10 4h0 0v-3l-2-1h0 4z" class="H"></path><path d="M735 615h4 3c-1 2-3 3-5 4h0v-3l-2-1h0z" class="Z"></path><path d="M716 632h6c6 1 9-1 13-3l2-1v-1c0-1 0-2 1-2l1-1v1l-1 1c-1 1 0 1 0 2s-2 2-2 2l-1 1c-3 0-3 1-4 3-6 0-10 0-15-2z" class="Y"></path><path d="M748 610c1 0 3 0 4-1 0 1 0 2-1 3l-5 2-7 1h-4 0l-4 1c-2-1-4 0-5-1l3-1 1-1 2-2c2-1 5-1 7-1h5 4z" class="H"></path><path d="M729 614l11-2c-1 2-2 2-4 2h-2v1h1 0l-4 1c-2-1-4 0-5-1l3-1z" class="C"></path><path d="M748 610c1 0 3 0 4-1 0 1 0 2-1 3l-5 2-7 1h-4-1v-1h2c2 0 3 0 4-2 1 0 6-1 8-2z" class="D"></path><path d="M791 525c2 2 6 6 9 6 2 1 5 1 7 1h-6l1 1c3 1 6 3 8 6 3 3 6 7 6 12-1 5-5 9-9 11-3 3-6 4-9 7-4 3-7 6-10 9 0-3 0-6 1-8 2-4 4-7 6-11-1 2-1 4-2 5 0 1-1 3-1 4 4-4 6-14 6-20 0-2 0-5-1-8l-2-3c2 2 2 3 3 4v-2c1 0 0 0 1-1v1h0c0 1 0 2 1 4h0v1l1 3h1c1-2 1-3 0-5-1-1-1-3-2-4s-1-1-1-2c-2-4-4-7-8-10v-1z" class="V"></path><path d="M799 536c4 3 7 8 9 12-2 2-2 8-4 10 0 1-2 1-3 2h0l-1-1c1-1 1-1 1-2v-1c0-2 1-5 0-8h0v-1h1c1-2 1-3 0-5-1-1-1-3-2-4s-1-1-1-2z" class="U"></path><path d="M791 525c2 2 6 6 9 6 2 1 5 1 7 1h-6l1 1c3 1 6 3 8 6v3 1 3c1 2 2 5 1 7-1 1-1 2-2 3 0-1 1-4 1-5l-2-3c-2-4-5-9-9-12-2-4-4-7-8-10v-1z" class="G"></path><path d="M791 525c2 2 6 6 9 6 2 1 5 1 7 1h-6l1 1-2 1c2 5 11 12 10 17h0l-2-3c-2-4-5-9-9-12-2-4-4-7-8-10v-1z" class="K"></path><defs><linearGradient id="CH" x1="559.068" y1="190.164" x2="567.231" y2="224.75" xlink:href="#B"><stop offset="0" stop-color="#1e1e1d"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#CH)" d="M565 189h14c1 3 2 8 3 11 2 4 4 8 5 12-1 4-4 8-6 11-6-9-9-19-11-29v-2h-1c-5 3-10 6-14 9-2 2-5 4-6 6l-5 6c-2 0-3-1-5-2 1-1 1-1 1-2 1-2 3-4 5-6 3-4 7-7 11-11 1-1 2-2 4-2l5-1z"></path><path d="M577 200h1l1 1c-1 2-1 3-3 4h0l-1-1 2-4z" class="R"></path><path d="M505 493c0 1 1 1 3 1h0c0-14-5-28-13-39-3-4-7-8-10-11 0-3 1-7 2-11l9 9c1 1 2 2 1 4v1c1 1 2 2 2 4 1 2 3 5 4 6l2-1v3l4 12 2 14v1 6c1 2 0 4 0 6v7c-1 0-1-1-1-1v-1c-1 1-1 1-2 1l-1 1c-1-2-2-3-3-4-1-2-3-3-6-3-2 1-3 3-5 5h0-1c-1-2 0-7 0-9h1v-3l1-1h0c-1-1-2-2-3-4l1-3c1-2 1-4 3-4h0c0 4 0 8 1 12 3 2 6 1 9 2z" class="g"></path><path d="M502 458l3 1 4 12c-3-2-3-4-4-6l-3-7z" class="Z"></path><path d="M494 490v1c0 1 1 3 2 3v1l-2 2c-1 1-1 4-1 5v1h0-1c-1-2 0-7 0-9h1v-3l1-1z" class="Q"></path><path d="M502 458c-4-6-10-10-13-16v-1c3 4 7 7 10 10 1 2 3 5 4 6l2-1v3l-3-1z" class="G"></path><path d="M766 516c3 0 6 0 8 2l17 8c4 3 6 6 8 10 0 1 0 1 1 2s1 3 2 4c1 2 1 3 0 5h-1l-1-3v-1h0c-1-2-1-3-1-4h0v-1c-1 1 0 1-1 1v2c-1-1-1-2-3-4 0 0-3-4-4-5 1 1 1 2 1 3h-1c-1-1-1-1-2-1l-1 2c0-1-1-2-2-3-3-5-12-7-18-8-4 0-9 0-14 1-6 1-14 4-18 9v1l-3 3v1l-4 3c0-1 1-2 2-3 0-1-1-1-2-2h0c1-4 6-9 9-11 2-3 5-4 8-6h1c2 0 4-1 5-2 5-2 9-3 14-3z" class="g"></path><path d="M765 522c4 0 11 0 15 3v1l-12-2c-4-1-9 0-12 0 2-2 6-2 9-2z" class="N"></path><path d="M768 524l12 2c4 1 8 3 11 6 1 1 1 2 1 3h-1c-1-1-1-1-2-1l-1 2c0-1-1-2-2-3-3-5-12-7-18-8v-1z" class="K"></path><path d="M738 527c2-3 5-4 8-6v1c-1 2-3 2-3 3-1 1-1 2-1 3 1-1 2-1 3-2h0v-1l2-2h3c4-1 9-1 13-1h2c-3 0-7 0-9 2-8 1-15 5-21 9l2-4c1 0 1-1 1-2z" class="U"></path><path d="M756 524c3 0 8-1 12 0v1c-4 0-9 0-14 1-6 1-14 4-18 9v1l-3 3v1l-4 3c0-1 1-2 2-3 0-1-1-1-2-2h0c1-4 6-9 9-11 0 1 0 2-1 2l-2 4c6-4 13-8 21-9z" class="H"></path><path d="M742 517c1-1 2-1 3 0h-1l1 1 1 1c-3 1-5 3-7 4v1c1 0 1 0 2-1 2-1 3-2 5-2h1-1c-3 2-6 3-8 6-3 2-8 7-9 11h0c1 1 2 1 2 2-1 1-2 2-2 3l-24 29c-2 2-9 8-9 9-1 2-3 4-5 6v-1l-2 2-1-1-3 4c0 1-1 2-2 2h0c1-2 2-4 3-5l2-1-1-2h0c2-2 3-4 5-7l9-12c9-13 18-26 29-38 3-3 6-7 10-9l2-2z" class="R"></path><path d="M725 539l9-12c1-1 4-3 5-5s3-3 5-5l1 1 1 1c-3 1-5 3-7 4v1c1 0 1 0 2-1 2-1 3-2 5-2h1-1c-3 2-6 3-8 6-3 2-8 7-9 11h0c0 1-1 2-2 3h-1l-1-2z" class="B"></path><defs><linearGradient id="CI" x1="709.564" y1="552.059" x2="717.517" y2="563.159" xlink:href="#B"><stop offset="0" stop-color="#636260"></stop><stop offset="1" stop-color="#7e7e7e"></stop></linearGradient></defs><path fill="url(#CI)" d="M729 538c1 1 2 1 2 2-1 1-2 2-2 3l-24 29c-2 2-9 8-9 9-1 0-1 0-2 1l1-2c3-5 6-8 9-12l21-29 1 2h1c1-1 2-2 2-3z"></path><path d="M163 330c-5-7-5-16-7-23-1-4-1-7-1-10l4 5v-5l2 2v2l6 14 9 17v1c1 2 4 5 4 7s2 2 3 4v5l1 1h-2c-1 3-1 6-1 9l-5-6-1-1c-1-1-3-2-4-4l-1-2c0-1 0-1-1-2v-5l-1-2c-2-1-3-4-4-5l-1-2z" class="C"></path><path d="M168 326c-2-4-4-8-5-13 0-2-1-5-2-7 0-1-1-3-1-4l1-1 6 14c-1 3 1 4 0 6 1 3 2 5 3 8l-2-3z" class="S"></path><path d="M163 330c1-3-1-6-2-10-1-2-2-4-1-6l10 21-1-5-1-4 2 3c1 3 4 6 5 10 1 1 1 5 1 7l-8-9h0c-2-1-3-4-4-5l-1-2z" class="e"></path><path d="M167 315l9 17v1c1 2 4 5 4 7s2 2 3 4v5l1 1h-2c-1 3-1 6-1 9l-5-6-1-1c-1-1-3-2-4-4l-1-2c0-1 0-1-1-2v-5l-1-2h0l8 9c0-2 0-6-1-7-1-4-4-7-5-10s-2-5-3-8c1-2-1-3 0-6z" class="B"></path><path d="M169 339c2 3 4 6 5 10 0 1 1 2 1 3-1-1-3-2-4-4l-1-2c0-1 0-1-1-2v-5z" class="J"></path><path d="M176 333c1 2 4 5 4 7s2 2 3 4v5l-4-5c-1-1-2-4-3-5l1-1c0 1 1 2 2 2-1-2-3-4-3-7z" class="Q"></path><path d="M179 344l4 5 1 1h-2c-1 3-1 6-1 9l-5-6 1-1c1 1 1 1 2 1-1-2-2-3-3-5h0l3 1h0v-5z" class="H"></path><defs><linearGradient id="CJ" x1="328.687" y1="713.641" x2="340.65" y2="704.447" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#898887"></stop></linearGradient></defs><path fill="url(#CJ)" d="M366 678c1-1 1 0 2 0 0 0 0 1-1 2v1c-2 1-6 2-7 4v1c-7 3-14 10-16 18l-1 4c0 4 1 7 1 11l2 4c-1 0-2-1-3-1l-2-1c-2-1-3-1-4-3 0-1 0-2-1-2h-1l-1-1v2h-1l-2 2c-1-2-2-4-3-5h-1v-7l2-2v-1h2c2-5 5-9 8-13l1 1 3-4c4-3 12-10 18-10h5z"></path><path d="M331 704l-3 10h-1v-7l2-2v-1h2z" class="N"></path><path d="M366 678c-1 2-3 2-5 3s-5 3-7 5h0c-4 2-7 5-11 7-2 2-3 3-6 4l-4 7c1-5 3-9 7-12l3-4c4-3 12-10 18-10h5z" class="g"></path><path d="M366 678c1-1 1 0 2 0 0 0 0 1-1 2v1c-2 1-6 2-7 4v1c-7 3-14 10-16 18l-1 4c0 4 1 7 1 11l2 4c-1 0-2-1-3-1l-2-1c-2-1-3-1-4-3 0-1 0-2-1-2h-1l-1-1c1-1 1-4 2-5 0-1 0-2 1-3h1c0-1 1-1 1-3 1-1 2-3 3-5s2-3 3-5l1 1s2-3 3-4c2-2 4-3 5-4v-1c2-2 5-4 7-5s4-1 5-3z" class="Q"></path><path d="M366 678c1-1 1 0 2 0 0 0 0 1-1 2v1c-2 1-6 2-7 4-5 3-11 7-14 12-2 3-4 9-7 11 2-5 4-9 7-13 0 0 2-3 3-4 2-2 4-3 5-4v-1c2-2 5-4 7-5s4-1 5-3z" class="U"></path><path d="M334 715c1-1 1-4 2-5 0-1 0-2 1-3h1c0-1 1-1 1-3 1-1 2-3 3-5s2-3 3-5l1 1c-3 4-5 8-7 13-1 2-1 3-1 5l1 1v-1c1-1 0-1 1-2v2 2l1 1c1 0 1 0 1 1l1-1v1c0 1 1 1 1 2l2 4c-1 0-2-1-3-1l-2-1c-2-1-3-1-4-3 0-1 0-2-1-2h-1l-1-1z" class="H"></path><defs><linearGradient id="CK" x1="242.469" y1="161.464" x2="242.514" y2="165.147" xlink:href="#B"><stop offset="0" stop-color="#7a7978"></stop><stop offset="1" stop-color="#929090"></stop></linearGradient></defs><path fill="url(#CK)" d="M287 165c-12 2-25 1-37 1l-59-1c-2-1-1-1-2-3 4-2 123 0 137 0h-1c-8 1-17 0-25 0h0c7 2 45 0 47 1l-1 1h-27l-2 1h0c1 0 1 0 1 1l-31-1z"></path><path d="M287 165h8l-10-1h34l-2 1h0c1 0 1 0 1 1l-31-1z" class="X"></path><defs><linearGradient id="CL" x1="700.81" y1="547.702" x2="711.763" y2="560.615" xlink:href="#B"><stop offset="0" stop-color="#706e6c"></stop><stop offset="1" stop-color="#8b8a8c"></stop></linearGradient></defs><path fill="url(#CL)" d="M736 514l1 1c2 1 3 1 5 2l-2 2c-4 2-7 6-10 9-11 12-20 25-29 38l-9 12c-2 3-3 5-5 7 0-3 1-3 2-4v-1l-1 1c0-1 3-6 3-8 1-2 4-8 6-10l3-7c1-1 2-3 2-4l14-21c1-3 4-5 6-8h0l1-1h2l1-2c2-2 5-4 7-5h2l1-1z"></path><path d="M722 523c1 1 2 1 3 1-2 2-3 4-4 6-3 3-5 6-7 9-5 8-10 18-16 24h-1l3-7c1-1 2-3 2-4l14-21c1-3 4-5 6-8z" class="D"></path><path d="M736 514l1 1c2 1 3 1 5 2l-2 2c-4 2-7 6-10 9l-1-2-6 6h-1v-1c-1 0-1 0-1-1 1-2 2-4 4-6-1 0-2 0-3-1h0l1-1h2l1-2c2-2 5-4 7-5h2l1-1z" class="Q"></path><path d="M725 524h0c0 3-1 5-3 7-1 0-1 0-1-1 1-2 2-4 4-6z" class="Y"></path><path d="M726 520c2 0 2 0 3 1-1 1-2 2-4 3h0c-1 0-2 0-3-1h0l1-1h2l1-2z" class="S"></path><path d="M736 514l1 1c2 1 3 1 5 2l-2 2c-4 2-7 6-10 9l-1-2c2-1 3-3 4-5l3-3h0v-1c-2-1-2 0-3 0-1 1-2 3-4 4-1-1-1-1-3-1 2-2 5-4 7-5h2l1-1z" class="B"></path><path d="M737 515c2 1 3 1 5 2l-2 2-2-1c-1-1 0-2-1-3z" class="S"></path><path d="M795 477h3l-2 1h1c1 0 0 0 1 1 1 0 3 0 4 1h1l1-1c4-1 7-1 12 0v-1h1c1-1 2-1 4 0l-6 6c-2 0-4-1-6 0-6 2-11 7-16 11l-9 6h-1c-1 1-5 3-5 5-1 1-3 1-5 2h-1c-1 0-1 0-2-1h-3-1v-1c0-1 0-1-1-1h-1v-1c0-3 5-6 6-9 1-1 5-4 6-5 3-1 4-3 6-4v2c2-3 4-4 6-5l6-4c0-1 0-1 1-2z" class="O"></path><path d="M782 488c2-3 4-4 6-5l1 1 1 1-10 6c1-1 2-2 2-3z" class="Z"></path><path d="M795 477h3l-2 1h1c1 0 0 0 1 1 1 0 3 0 4 1l-12 5-1-1-1-1 6-4c0-1 0-1 1-2z" class="N"></path><path d="M776 490c3-1 4-3 6-4v2c0 1-1 2-2 3s-3 2-4 3v1c0 1-1 2-2 3 1 0 2-1 3-2h0l-4 4-7 7v-1c0-1 0-1-1-1h-1v-1c0-3 5-6 6-9 1-1 5-4 6-5z" class="B"></path><path d="M776 494v1c0 1-1 2-2 3 1 0 2-1 3-2h0l-4 4-7 7v-1c0-1 0-1-1-1l11-11z" class="L"></path><path d="M777 496c8-5 16-11 25-14-3 3-7 4-10 6s-5 4-7 6-7 6-8 9h1l5-2c-1 1-5 3-5 5-1 1-3 1-5 2h-1c-1 0-1 0-2-1h-3-1l7-7 4-4z" class="M"></path><path d="M766 507l7-7c0 2-1 3-1 5 3 1 4-1 6-2h0l5-2c-1 1-5 3-5 5-1 1-3 1-5 2h-1c-1 0-1 0-2-1h-3-1z" class="P"></path><path d="M816 479v-1h1c1-1 2-1 4 0l-6 6c-2 0-4-1-6 0-6 2-11 7-16 11l-9 6h-1l-5 2h-1c1-3 6-7 8-9s4-4 7-6 7-3 10-6c2-1 4-2 6-2 2-1 5 0 8-1z" class="a"></path><path d="M378 736l1-2h1l5 6 6 6v3l-1 1c1 1 7 7 8 7h3l2 2 10 7c1 0 4 2 4 3l-1 1c7 5 14 8 21 13 9 7 13 18 17 29h-1c-1-3-3-6-4-8-3-5-7-9-11-13-5-4-11-6-16-10-7-5-14-10-20-16 0 2 3 4 4 5v2l-3-3c-3-2-4-4-6-4-4-4-7-7-10-11-4-5-10-13-9-18z" class="V"></path><path d="M378 736l1-2h1l5 6h-1v1 2l-1-2c-1-1-2-2-2-4h0c0-1-1-1-1-2l-2 1z" class="U"></path><path d="M385 740l6 6v3l-1 1c-2-1-4-6-6-7v-2-1h1z" class="T"></path><path d="M398 757h3l2 2 10 7c1 0 4 2 4 3l-1 1c-2 0-16-11-18-13z" class="e"></path><path d="M301 426l4-2 1 1h-1l-1 6 1 5-1 1-2 3c1 2 1 3 1 5l-3 5-2 6c-1 3-2 6-2 10s-1 8-1 11l1 5v1 1c-1 1-1 1-3 2 0-1-1-3-2-4v-1h-1c-2-10-3-19-4-29v-7h-1 0v-5c0-1 1-1 1-2h1c1-1 2-2 2-3 4-4 7-7 12-9z" class="G"></path><path d="M302 440c1 2 1 3 1 5l-3 5-1-2c0-2 2-6 3-8z" class="P"></path><path d="M299 448l1 2-2 6c-1 1-2 3-2 5-1 2-1 5-3 7 0-7 3-13 6-20z" class="B"></path><path d="M293 468c2-2 2-5 3-7 0-2 1-4 2-5-1 3-2 6-2 10s-1 8-1 11h0v-2h-1v3l-2-1v-3c-1-1 0-3-1-4v-1h1s0 3 1 4h0v-5h0z" class="c"></path><path d="M301 426l4-2 1 1h-1l-1 6v-5c-1 1-1 0-1 2-1 1-1 2-2 3-1 0-1 1-2 2-2 3-5 6-8 9h-1c0-1 1-2 1-3h0l-1-1h-1-1-1c1-1 2-2 2-3 4-4 7-7 12-9z" class="M"></path><path d="M289 435c4-4 7-7 12-9 0 1 0 3-1 4 0 1-2 1-3 2l-1 1c-2 2-4 2-7 2z" class="W"></path><defs><linearGradient id="CM" x1="298.437" y1="481.932" x2="282.455" y2="462.056" xlink:href="#B"><stop offset="0" stop-color="#5d5c5b"></stop><stop offset="1" stop-color="#8d8d8d"></stop></linearGradient></defs><path fill="url(#CM)" d="M286 452c1 0 2 0 3 1v1c0 2 0 4 1 6v3c0 2 1 4 2 6h-1v1c1 1 0 3 1 4v3l2 1v-3h1v2h0l1 5v1 1c-1 1-1 1-3 2 0-1-1-3-2-4v-1h-1c-2-10-3-19-4-29z"></path><path d="M292 477l2 1v-3h1v2 1c-1 1 0 2-1 3-1-2-2-2-2-4z" class="B"></path><path d="M304 395l1 2c0 1 0 2 1 3l4 2c-1 3-2 3-1 5l-10 4c-6 5-11 15-14 23 1 1 1 3 1 4s-1 1-1 2c-2 4-3 9-4 12l-1 1h0l-1 3v2h-1 0v-1c-2-3-2-6-3-8-1-3-1-8-1-11 1-9 2-18 5-26l2-2c2-1 4-1 6 0l1 2c0-1 3-4 4-4 3-2 5-4 8-5 2-1 3-1 4-2v-1c-1-1-3-1-4-2v-1h2 2v-2z" class="V"></path><path d="M285 434c1 1 1 3 1 4s-1 1-1 2c-2 4-3 9-4 12l-1 1h0c2-5 2-12 4-17 1-1 1 0 1-1v-1z" class="N"></path><path d="M304 395l1 2c0 1 0 2 1 3l4 2c-1 3-2 3-1 5l-10 4c1 0 1-2 1-2 3-2 6-2 6-5l-1-1c-5 0-13 5-16 9v1h-1c-2 3-4 6-5 10l-1 2-1 3c0-2 1-4 1-6 2-4 3-7 6-10 0-1 3-4 4-4 3-2 5-4 8-5 2-1 3-1 4-2v-1c-1-1-3-1-4-2v-1h2 2v-2z" class="X"></path><path d="M279 412l2-2c2-1 4-1 6 0l1 2c-3 3-4 6-6 10 0 2-1 4-1 6v2c0 1 0 1-1 2v2 1c0 1 0 1-1 2-1 4-1 11-1 15 1 1 0 2 0 3v2l1-1v2h-1 0v-1c-2-3-2-6-3-8-1-3-1-8-1-11 1-9 2-18 5-26z" class="a"></path><path d="M653 686c8 2 15 2 21 7 9 6 15 13 17 23l1 6h1 1v2c0 1-1 2 0 3l-13 2h0c-4 3-8 7-13 10l9-13v-3c1-4 2-8-1-12s-7-7-10-10c-1-3-5-5-5-7l-2-2 1-1c-2 0-4-1-6-1-2-1-4-1-5-1s-1-1-2-1h-1l-1-1c4 0 5 1 8-1z" class="d"></path><path d="M692 722h1 1v2c0 1-1 2 0 3l-13 2c1-1 1-2 2-3l2-1c1-1 2-1 3-1 0 1 0 0 1 1h3v-3z" class="W"></path><defs><linearGradient id="CN" x1="674.947" y1="694.535" x2="670.863" y2="718.518" xlink:href="#B"><stop offset="0" stop-color="#040302"></stop><stop offset="1" stop-color="#393a3b"></stop></linearGradient></defs><path fill="url(#CN)" d="M660 691c9 2 17 6 22 14 3 4 5 8 4 13 0 1-2 3-3 4l-3 1-3 3v-3c1-4 2-8-1-12s-7-7-10-10c-1-3-5-5-5-7l-2-2 1-1z"></path><path d="M661 694c6 4 20 11 21 19 1 2 0 4 0 6-1 1-2 3-2 4l-3 3v-3c1-4 2-8-1-12s-7-7-10-10c-1-3-5-5-5-7z" class="B"></path><path d="M140 221v1c-1 3-4 5-5 7-2 4-4 7-6 11 0 1-1 2 0 3h0c2 1 3 1 4 3v-1c0-1 1-2 2-3 1-5 4-10 7-13 1-1 2-1 2-1-2 3-5 7-7 10-3 6-5 12-6 19 4-5 9-11 14-15v1c-1 1-1 2-2 2l-9 9c1 3 1 4 4 5 2-2 3-4 5-6 2-1 3-3 4-3 1-1 1-1 2-1-12 11-20 24-21 41 0 5 0 10 1 15 0 1 2 7 1 7v4c1 0 1 1 1 2s1 1 0 2h1c0 1 0 2 1 3v1l-3 2v-1l-1-3h-1c0-1-1-3-1-4-3-11-4-23-5-34-1-22 2-46 18-63zm728-9c14 10 23 27 26 44s2 37-1 54c0-1 0-2-1-2h-3l-1 5h-1c2-9 4-18 2-28-2-14-7-24-17-34 1 0 1 1 2 1 3 3 5 6 7 9 2-2 3-4 4-5l-4-5c-2-3-6-5-7-8v-1c2 2 5 4 7 7v-2l-5-6c1-1 1-3 1-4l-2-4v-3h0c-3-3-6-5-8-8h0c4 1 9 8 11 11 1 2 2 3 3 5l2 5c2-1 3-3 4-4 0-1 0-2-1-2v-1l-2-4c0-1 0-1-1-1l-6-9c-3-3-7-6-9-9v-1z" class="M"></path><path d="M875 230c6 7 10 17 11 26l-5-7v-2l-5-6c1-1 1-3 1-4l-2-4v-3z" class="I"></path><path d="M877 237c2 3 5 7 4 10l-5-6c1-1 1-3 1-4z" class="c"></path><path d="M521 483v11c1 0 1-1 1-2v4h3c4 0 6-1 10-2 2 0 4-1 6-2v1c0 2 1 3 1 5h-1c-4-1-6-1-10 2-2 2-4 4-5 8h0c-1 5-1 10-1 14-1 2-1 3-1 4h0l-2-1c0 4 0 7-1 10v12c-3-2-5-4-8-3l-2 1 1-12v-8c0-6 0-13-1-20v-7c0-2 1-4 0-6v-6 1c1 2 0 5 1 6 1 0 2 1 2 1h1c1 2 1 4 3 5 1 1 0 1 2 1 1-3 0-7 0-11l1-6z" class="G"></path><defs><linearGradient id="CO" x1="536.25" y1="518.498" x2="525.414" y2="500.081" xlink:href="#B"><stop offset="0" stop-color="#bababa"></stop><stop offset="1" stop-color="#e6e5e4"></stop></linearGradient></defs><path fill="url(#CO)" d="M525 496c4 0 6-1 10-2 2 0 4-1 6-2v1c0 2 1 3 1 5h-1c-4-1-6-1-10 2-2 2-4 4-5 8h0c-1 5-1 10-1 14-1 2-1 3-1 4h0l-2-1c0 4 0 7-1 10 0-9 0-19 2-28l-1-11h3z"></path><path d="M525 496c4 0 6-1 10-2 2 0 4-1 6-2v1l-9 3c-4 1-7 6-9 10v1l-1-11h3z" class="X"></path><defs><linearGradient id="CP" x1="551.086" y1="147.283" x2="533.4" y2="122.585" xlink:href="#B"><stop offset="0" stop-color="#959493"></stop><stop offset="1" stop-color="#bbbab9"></stop></linearGradient></defs><path fill="url(#CP)" d="M536 118c3-4 6-7 11-9 2 1 4 2 7 2 3 1 6 2 8 6 1 2 0 6 1 8v3h-4c-2-1-2-1-2-3 0-3 0-6-3-8-2-1-4-1-6 0-3 0-6 3-8 6-2 6-3 14-1 19s4 7 7 10l3 2 4 1 2 1v1c-1 1-2 1-3 2l-1 1h-1c-1-1-2 0-3 0l-1 1h4 0l1 1c1-1 3-4 4-4 1-1 1-2 2-2h0c0 1 0 1-1 2l-3 4v4c0 1 0 1-1 2-2 0-2 0-4-2 0-1-1-1-2-1l-2-2h-1c-7-3-11-11-14-18v-6c0-6 2-11 4-17l3-4z"></path><path d="M532 138l2 3c0 1 0 3 1 5l2 2c2 2 4 4 6 5 2 0 2 1 3-1l3 2 1 2c-1 1-2 1-3 1-2-1-5-2-7-3l-1-1c0-1-1-2-2-3-3-3-5-7-5-12z" class="P"></path><path d="M554 111c3 1 6 2 8 6 1 2 0 6 1 8v3h-4c-2-1-2-1-2-3l1-1v-4c-1-2-3-4-4-5h-1c-4-2-7-1-11 0 3-3 7-4 12-4z" class="H"></path><path d="M536 118c3-4 6-7 11-9 2 1 4 2 7 2-5 0-9 1-12 4-7 7-10 13-10 23 0 5 2 9 5 12 1 1 2 2 2 3h0v1c1 2 2 3 3 4h-1l-3-3-1-2c-2-2-3-3-4-5l-2-5c-1-1-1-2-2-4h0c0-6 2-11 4-17l3-4z" class="D"></path><path d="M529 139h0c1 2 1 3 2 4l2 5c1 2 2 3 4 5l1 2 3 3h1c-1-1-2-2-3-4v-1h0l1 1c2 1 5 2 7 3 1 0 2 0 3-1l-1-2 4 1 2 1v1c-1 1-2 1-3 2l-1 1h-1c-1-1-2 0-3 0l-1 1h4 0l1 1c1-1 3-4 4-4 1-1 1-2 2-2h0c0 1 0 1-1 2l-3 4v4c0 1 0 1-1 2-2 0-2 0-4-2 0-1-1-1-2-1l-2-2h-1c-7-3-11-11-14-18v-6z" class="b"></path><path d="M549 154l4 1v2c-1 1-2 2-4 2-4 0-7-3-9-5 2 1 5 2 7 3 1 0 2 0 3-1l-1-2z" class="B"></path><defs><linearGradient id="CQ" x1="690.778" y1="173.261" x2="690.199" y2="185.738" xlink:href="#B"><stop offset="0" stop-color="#8b8988"></stop><stop offset="1" stop-color="#a8a8a9"></stop></linearGradient></defs><path fill="url(#CQ)" d="M581 178h171 51 9c2 0 3 0 5 1h-1v2H566v-3h5 10z"></path><path d="M608 309h1c0 1 0 2 1 2v1l3-3v-1l1-1c1 1 1 1 1 2 2 1 3 4 3 6 2 5 4 10 5 15 1 2 1 3 1 5l-1 1h0v2 1l-2 2v1c-2 2-4 2-5 3-2 0-2 0-3 1l-3-1h-1l-1-1c-2-1-4-3-6-5v-1-3-1h1c0-1 0-2 1-2 1-1 1-4 2-6v-1-1l-1-1 1-1 1 1c0-1 0-1 1-2h-1c-1 0-1-1-2-1h0v1c0 1-1 2-1 3v2h-1v2c0 1-1 2-2 3v1c-1 1-1 2-2 4h0c-1 2 0 4 0 6h0c0 1 0 1-1 2l-1 2c0-1 0-3-1-4v-1-2c-1-2-1-3-1-5l-2 3c-1-2-1-6-1-8v-3l1-1h3l3-3 3-4c1-1 2-3 3-4 1 0 1-1 2-2h0c1-2 1-2 1-3z" class="R"></path><path d="M615 309c2 1 3 4 3 6l-2 3c0-1-1-3-2-5h0l1-3v-1zm-7 14c2 4 1 11 1 15-2-1-3-3-4-5 0-3 2-6 3-10z" class="B"></path><path d="M608 309h1c0 1 0 2 1 2v1l3-3v-1l1-1c1 1 1 1 1 2v1c-3 0-5 2-6 4-1 1 0 1-1 1-2 1-4 4-5 6l-1-3c1-1 2-3 3-4 1 0 1-1 2-2h0c1-2 1-2 1-3z" class="f"></path><path d="M608 323l3-6h1v1 2c-1 1-2 2-2 3s0 1 1 2c1 3 2 5 1 8 0 1-1 3-2 4l2 2h0c-2 0-2-1-3-1 0-4 1-11-1-15z" class="c"></path><path d="M603 321c0 3-1 7-3 10 0 1-2 4-2 6-1 2 0 5 0 7l-1 2c0-1 0-3-1-4v-1-2c-1-2-1-3-1-5 3-4 5-9 8-13z" class="g"></path><path d="M620 334c0 1 1 2 2 3l-1 3c-1 2-4 4-7 4-2 1-4 0-6-1s-3-2-4-4c1 1 2 2 4 2 2 1 5 1 7 0 3-2 4-4 4-7h1z" class="W"></path><defs><linearGradient id="CR" x1="617.567" y1="317.631" x2="622.156" y2="332.802" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#CR)" d="M616 318l2-3c2 5 4 10 5 15 0 2 0 4-1 7-1-1-2-2-2-3 0-6-2-11-4-16z"></path><path d="M612 318c3 4 6 11 4 17 0 1-1 2-2 3l-2 1-2-2c1-1 2-3 2-4 1-3 0-5-1-8-1-1-1-1-1-2s1-2 2-3v-2z" class="G"></path><path d="M602 318l1 3h0c-3 4-5 9-8 13l-2 3c-1-2-1-6-1-8v-3l1-1h3l3-3 3-4z" class="I"></path><path d="M596 325c0 1-1 3-2 4h-2v-3l1-1h3z" class="K"></path><defs><linearGradient id="CS" x1="330.226" y1="158.224" x2="336.703" y2="199.82" xlink:href="#B"><stop offset="0" stop-color="#928d8c"></stop><stop offset="1" stop-color="#adb0b1"></stop></linearGradient></defs><path fill="url(#CS)" d="M199 177c1 0 2 1 3 1h10 24 79 95 47c3 0 8-1 11 0v2 1h-9-16-56-186c-2-1-2-2-2-4z"></path><defs><linearGradient id="CT" x1="500.203" y1="614.431" x2="535.178" y2="556.099" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#757472"></stop></linearGradient></defs><path fill="url(#CT)" d="M511 545l2-1c3-1 5 1 8 3v37c0 12 0 24 1 35l2 15 3 13h0l-1-1v4h1v3 3l1 1c0 1 0 1-1 2h0v-2l-1-1v-2-2c-1-1-1-2-1-3v-3c-1-2-1-3-1-4v-2c-1-2-1-6-1-8h0v-2c0-1-1-3-2-3-1-1-1-1-1-2-1 0-1-1-1-2h0c-1-1-2-1-2-2l-1-1c-1-2-1-3-1-5v5 3c0 3-1 8 0 11l1 3v3l3 6v1l1 2c0 1 0 1 1 3 0 1 1 2 1 3v1l1 1c0 1 1 2 1 3v1l1 3 1 1h-1l-8-21-3-9c-1-9 0-18-1-27h1c0-3 0-15-2-17-1-5 0-11 0-16l-1-30z"></path><path d="M527 653c-2-6-3-13-3-19h0l3 13h0l-1-1v4h1v3z" class="f"></path><path d="M472 126c-1 0-3-1-4-2-1-2-1-3-1-5 0-3 1-6 4-8s8-3 12-2c6 0 10 5 14 10 5 8 8 18 6 28l-1 1-1 3-1 3c0 2-1 5 0 7l-4 1h-2c-1 0-2 0-2 1-2 1-3 1-5 1s-3 1-4 1c-2 1-2 1-3 0v-1c-1-2-3-6-2-8 0-1 0-1 1-2h5c4-1 6-3 8-7 3-5 4-12 2-18h0c-1-3-2-5-3-7-2-3-4-5-7-5-2 0-4 0-5 1 0 1-1 2-1 3 0 2-1 4-2 5 0 1-2 2-2 2l-2-2z" class="I"></path><path d="M500 154c0 2-1 5 0 7l-4 1h-2c-1 0-2 0-2 1-2 1-3 1-5 1s-3 1-4 1h-2l1-1c3 0 6 0 9-2h0c4-2 6-5 9-8z" class="F"></path><path d="M479 154c0 1 1 2 2 3h5l1-1c7-2 8-6 11-11l2-3c0 2 0 3-1 4-2 6-3 10-9 14-3 1-7 3-10 4-1-2-3-6-2-8 0-1 0-1 1-2z" class="D"></path><defs><linearGradient id="CU" x1="472.798" y1="117.074" x2="485.842" y2="121.65" xlink:href="#B"><stop offset="0" stop-color="#787776"></stop><stop offset="1" stop-color="#908f8f"></stop></linearGradient></defs><path fill="url(#CU)" d="M472 126c0-3-1-9 1-12 2-2 4-3 7-3 5 0 8 2 12 6h-1-4c-3-1-6-3-9 1-1 0-1 1-1 2l1 1c0 2-1 4-2 5 0 1-2 2-2 2l-2-2z"></path><defs><linearGradient id="CV" x1="496.788" y1="119.086" x2="483.618" y2="153.664" xlink:href="#B"><stop offset="0" stop-color="#a2a1a0"></stop><stop offset="1" stop-color="#c6c5c4"></stop></linearGradient></defs><path fill="url(#CV)" d="M478 121l-1-1c0-1 0-2 1-2 3-4 6-2 9-1h4 1c6 7 8 15 8 24v1l-2 3c-3 5-4 9-11 11l-1 1h-5c-1-1-2-2-2-3h5c4-1 6-3 8-7 3-5 4-12 2-18h0c-1-3-2-5-3-7-2-3-4-5-7-5-2 0-4 0-5 1 0 1-1 2-1 3z"></path><path d="M759 303c2 1 2 4 4 5 4 4 14 4 19 5-2 10-2 19 1 29-2 2-2 4-3 6-2 3-3 5-5 7-8-8-12-17-16-28-1-8-1-16 0-24z" class="K"></path><defs><linearGradient id="CW" x1="251.107" y1="450.084" x2="214.888" y2="449.945" xlink:href="#B"><stop offset="0" stop-color="#787776"></stop><stop offset="1" stop-color="#a5a4a3"></stop></linearGradient></defs><path fill="url(#CW)" d="M214 413c1-1 1-1 2-1 0 1 0 1 1 1v-1h2 0c3 1 4 3 5 6l2 4 3 9c1 2 2 4 2 6 0 1 1 2 1 4 2 5 6 11 8 16 2 2 4 7 6 9 1 1 2 3 3 5l-1 1c1 2 1 3 2 4l2 2c-1 1-2 1-4 2l-1-1-2-1v2h-3l-6-4h0c-1-1-2-2-4-3 0-1 0-1-1-2 1-1 1-1 2-1-4-4-8-8-11-12v-1c4-9-3-17-5-26-2-5-3-10-4-16v-1l1-1z"></path><path d="M232 473c0-1 0-1-1-2 1-1 1-1 2-1l4 5-1 1c-1-1-2-2-4-3z" class="Z"></path><defs><linearGradient id="CX" x1="248.175" y1="443.135" x2="212.325" y2="438.365" xlink:href="#B"><stop offset="0" stop-color="#c7c4c5"></stop><stop offset="1" stop-color="#f9fbf8"></stop></linearGradient></defs><path fill="url(#CX)" d="M214 413c1-1 1-1 2-1 0 1 0 1 1 1v-1h2 0c3 1 4 3 5 6l2 4 3 9c1 2 2 4 2 6 0 1 1 2 1 4 2 5 6 11 8 16 2 2 4 7 6 9 1 1 2 3 3 5l-1 1c-2-2-4-4-6-7-3-3-5-7-7-11l-14-26c-3-4-5-9-8-13v-1l1-1z"></path><path d="M214 413c1-1 1-1 2-1 0 1 0 1 1 1v-1h2 0c3 1 4 3 5 6l2 4 3 9c1 2 2 4 2 6 0 1 1 2 1 4-1-1-2-2-2-3-2-7-6-14-10-20-2-2-4-5-6-5z" class="X"></path><path d="M219 412c3 1 4 3 5 6l2 4c-2 0-6-5-7-7v-3z" class="M"></path><path d="M574 133v2l1 1c3 0 9 0 11-2l154 1v2c2 1 3 2 4 2H621h-37c-6 0-13 0-18 1h-4v1c-1-1-2-1-3-2h-1-1c2-2 3-2 5-3l3-2c4 0 6 1 9-1z" class="V"></path><path d="M574 133v2c-1 0-4 0-5 2h0c-3 0-5 0-7-1l3-2c4 0 6 1 9-1z" class="W"></path><path d="M263 304c1 6 1 13 0 19l-1 4v1c-2 7-6 15-10 21-2 1-5 4-6 6h0 0c0-1-1-1-1-1-3-1-5-4-7-6l-1-1c-1-1-1-1-2-1v1h0c-1 1-1 1-1 2-1 2-3 5-5 7h0l3 1c1 0 2 1 3 2h0-1v-1c-3-1-4-1-6-1h-1c1-2 3-4 4-5 3-5 5-11 6-16 2-9 1-16 0-25 2 0 4 1 7 2 1 0 1 1 2 1 4 1 8-1 10-3h1c2-2 4-4 6-7z" class="K"></path><path d="M548 436l1-1 1 3c0-2 0-2 1-3-1-1-1-1-1-3 1 1 1 2 1 2l3 4c1-1 1-6 2-8l1-1c-1 4-1 9-2 13l-2 4c-1 2-1 5-3 7-1 2-3 3-4 5l-3 15c0 3 1 7-1 10 0 3-1 6-1 9-2 1-4 2-6 2-4 1-6 2-10 2h-3v-4c0 1 0 2-1 2v-11-5c0-1 1-1 1-2 1-3 1-5 4-7l-1 3c4-4 5-9 7-14 3-4 6-8 10-12h0c2-1 3-2 4-4h0c0-1 1-3 2-4v4l-1 1v1h0c0 2 0 2 1 3h0l1-1v-6l-1-4z" class="R"></path><path d="M549 440l3 2c-1 2-2 3-3 4v-6z" class="D"></path><path d="M543 473c0-1 0-2-1-2-1 1-2 2-2 3s0 1-1 2v-1l-1-1c0-6 4-12 8-16l-3 15z" class="C"></path><path d="M526 493c3 0 7-2 10-3 2-3 3-7 3-11 2 1 2 3 3 4 0 3-1 6-1 9-2 1-4 2-6 2-4 1-6 2-10 2-1-1-1-2-1-3l1-1 1 1z" class="V"></path><defs><linearGradient id="CY" x1="543.283" y1="475.144" x2="526.717" y2="457.356" xlink:href="#B"><stop offset="0" stop-color="#babab9"></stop><stop offset="1" stop-color="#e9e7e7"></stop></linearGradient></defs><path fill="url(#CY)" d="M542 446h0c2-1 3-2 4-4h0c0-1 1-3 2-4v4l-1 1v1h0c0 2 0 2 1 3h0c-11 6-16 19-20 30-1 5-3 11-2 16l-1-1-1 1c0 1 0 2 1 3h-3v-4c0 1 0 2-1 2v-11-5c0-1 1-1 1-2 1-3 1-5 4-7l-1 3c4-4 5-9 7-14 3-4 6-8 10-12z"></path><path d="M521 478c0-1 1-1 1-2 1-3 1-5 4-7l-1 3c-2 6-3 14-3 20 0 1 0 2-1 2v-11-5z" class="J"></path><defs><linearGradient id="CZ" x1="644.088" y1="655.833" x2="654.952" y2="641.453" xlink:href="#B"><stop offset="0" stop-color="#373836"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#CZ)" d="M668 630c6-1 11-2 16-1h2c3 0 6 0 9 1h0l1 1c0 1 1 1 2 1 3 0 5 0 8 1h1c1 1 2 1 2 2-15 2-28 5-42 10-13 7-25 15-36 24l-2 2-2 1h-1c1-2 1-2 1-3 1-2 6-6 7-7l1-1 1-1 2-2h1l1-1 3-3h-2l-2 1c-1 2 1 0-1 1l-3 4c0-2 1-3 2-4v-3l-1 1h-3l10-9h-2l-1 1-1-1 2-3c-1-1 1-1 0-2l-1 1-2-1c1-1 3-2 5-3s3-2 4-3c2-1 4-1 7-2h2l-3 2h3l1 2h1c1-1 2-1 3-2 0-1 1-2 3-2 1-1 3-1 4-2z"></path><path d="M637 653c3-3 6-5 10-7 2-1 3-2 5-1-1 1-1 1-3 2-4 2-8 6-12 9v-3z" class="E"></path><path d="M658 636v1c-2 1-3 2-5 4h1 0c2-1 5-2 8-2-4 2-7 4-10 6-2-1-3 0-5 1-4 2-7 4-10 7l-1 1h-3l10-9 6-5c3-1 6-2 8-4h1z" class="G"></path><path d="M654 632h2l-3 2h3l1 2c-2 2-5 3-8 4l-6 5h-2l-1 1-1-1 2-3c-1-1 1-1 0-2l-1 1-2-1c1-1 3-2 5-3s3-2 4-3c2-1 4-1 7-2z" class="M"></path><path d="M653 634h3l1 2c-2 2-5 3-8 4h-3v-1l4-4 3-1z" class="T"></path><defs><linearGradient id="Ca" x1="643.051" y1="650.997" x2="659.495" y2="652.507" xlink:href="#B"><stop offset="0" stop-color="#9f9d9b"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#Ca)" d="M665 642l-1 1c-1 1-2 2-3 2v2c2-1 3-2 6-2-13 7-25 15-36 24 0-1 0-3 1-4 10-9 20-17 33-23z"></path><path d="M696 631c0 1 1 1 2 1 3 0 5 0 8 1h1c1 1 2 1 2 2-15 2-28 5-42 10-3 0-4 1-6 2v-2c1 0 2-1 3-2l1-1c9-4 20-9 31-11z" class="V"></path><path d="M668 630c6-1 11-2 16-1h2c3 0 6 0 9 1h0l-6 1-4 2-8 2-7 2c-3 1-5 2-8 2s-6 1-8 2h0-1c2-2 3-3 5-4v-1c1-1 2-1 3-2 0-1 1-2 3-2 1-1 3-1 4-2z" class="M"></path><path d="M664 632c1-1 3-1 4-2 1 1 2 2 4 2l-1 1c-2 0-4-1-6 0l-1-1z" class="U"></path><g class="T"><path d="M672 632c4 0 9 0 13 1l-8 2-2-1c-2 0-5 1-7 2h0l-1-1c1-1 3-1 4-2l1-1z"></path><path d="M661 634c0-1 1-2 3-2l1 1c0 1-1 2-3 2h-1l-1 1v2c3 0 6-1 8-2l2 1c-3 1-5 2-8 2s-6 1-8 2h0-1c2-2 3-3 5-4v-1c1-1 2-1 3-2z"></path></g><path d="M668 630c6-1 11-2 16-1h2c3 0 6 0 9 1h0l-6 1-4 2c-4-1-9-1-13-1-2 0-3-1-4-2z" class="a"></path><path d="M686 629c3 0 6 0 9 1h0l-6 1c-2 0-4 0-7-1h3l1-1z" class="N"></path><path d="M655 673l3-1v1c-1 1-1 2-2 3 2 1 11 0 14 1 2 0 5 0 7 1l1 1v1l2 1 2 1c10 5 13 13 19 22 1 2 3 5 4 7 1 1 1 2 1 3-1 5-3 7-6 10l-6 3c-1-1 0-2 0-3v-2h-1-1l-1-6c-2-10-8-17-17-23-6-5-13-5-21-7-2-1-5-1-7-1 1-2 4-2 7-2-1-1-3-1-5-1 1-1 2-1 3-1v-1h-5c0-1 0-1 1-2 0-1 0-1 1-1 1-1 2-1 3-3l-2-1h6z" class="g"></path><path d="M664 682h4v-1c3-1 9 0 12 0l2 1-2 1c-6 1-10 0-16-1zm1 4c7 1 12 4 18 7 3 2 6 3 8 5-2-4-4-8-9-10l1-1c3 2 9 7 9 10 0 2 1 3 1 4-1 0 0 0-1-1-1 0-1-1-2-1l-5-2-2-1v-1l-2-1h0c-2-1-4-2-6-2-3-1-5-3-7-4-2 0-2-1-3-2z" class="W"></path><path d="M655 673l3-1v1c-1 1-1 2-2 3 2 1 11 0 14 1 2 0 5 0 7 1l1 1v1l2 1c-3 0-9-1-12 0v1h-4-5c2 1 5 1 7 2h-4-2c1 1 4 2 5 2h0c1 1 1 2 3 2 2 1 4 3 7 4l-1 1c-6-5-13-5-21-7-2-1-5-1-7-1 1-2 4-2 7-2-1-1-3-1-5-1 1-1 2-1 3-1v-1h-5c0-1 0-1 1-2 0-1 0-1 1-1 1-1 2-1 3-3l-2-1h6z" class="Z"></path><path d="M649 673h6c-1 1-1 2-2 3-1 0-2 1-2 2h-2l-1-1c1-1 2-1 3-3l-2-1z" class="G"></path><path d="M646 680c0-1 0-1 1-2 0-1 0-1 1-1l1 1h2 9c-3 1-6 1-8 3h-1v-1h-5z" class="H"></path><defs><linearGradient id="Cb" x1="699.968" y1="708.054" x2="679.3" y2="709.759" xlink:href="#B"><stop offset="0" stop-color="#9f9d9c"></stop><stop offset="1" stop-color="#c1c0be"></stop></linearGradient></defs><path fill="url(#Cb)" d="M675 692c2 0 4 1 6 2h0l2 1v1l2 1 5 2c1 0 1 1 2 1 1 1 0 1 1 1 0-1-1-2-1-4 2 2 5 5 5 7 2 2 6 9 7 10h2c-1 5-3 7-6 10l-6 3c-1-1 0-2 0-3v-2h-1-1l-1-6c-2-10-8-17-17-23l1-1z"></path><path d="M685 697l5 2c1 0 1 1 2 1 1 1 0 1 1 1h1v2h0c-3-2-6-3-9-6z" class="X"></path><path d="M700 724c0-2 1-3 1-5 1-2 1-4 0-5 0-4-4-6-4-10 2 2 6 9 7 10h2c-1 5-3 7-6 10z" class="H"></path><path d="M849 330c0 1-1 2-1 3l3-3c1-1 1-1 2-1v-1l-1 4c-1 2-4 5-6 6l-3 5 1-1c1 1 2 1 2 2-1 2-7 11-9 11-4 4-8 7-11 11s-7 9-11 12l-1 1-1-1v-1c0-1 0-1 1-2-2 0-4 0-6 1h-1v-3l-1 1c-3 0-4 1-6 3l-3 3h-1v-2c-1 1-3 3-4 3-2 1-3 1-5 0l3-3 1-1 7-6 7-7 9-10 9-10c2-1 3-1 4-2v-1l1 1c1 0 3-3 3-3 1-1 1-2 2-2h2l3-4 1 1v2 1l1-1 3-3c1-1 3-3 4-3 1-1 2 0 2 0z" class="V"></path><path d="M807 367c-1 1-2 3-3 4l7-5h0c0 2-1 2-1 4l-3 3-1 1c-3 0-4 1-6 3v-2l2-3c0-1 3-4 4-5h1z" class="M"></path><path d="M823 344c2-1 3-1 4-2v-1l1 1-4 4v1c-4 5-10 10-15 15h0l-1 1 1 1 2-2v1l-4 4h-1c-2 0-2 1-3 2v-1c1-1 1-3 2-4l9-10 9-10z" class="U"></path><defs><linearGradient id="Cc" x1="792.585" y1="375.864" x2="802.785" y2="369.201" xlink:href="#B"><stop offset="0" stop-color="#969494"></stop><stop offset="1" stop-color="#c2c1c0"></stop></linearGradient></defs><path fill="url(#Cc)" d="M805 364c-1 1-1 3-2 4v1c1-1 1-2 3-2-1 1-4 4-4 5l-2 3v2l-3 3h-1v-2c-1 1-3 3-4 3-2 1-3 1-5 0l3-3 1-1 7-6 7-7z"></path><path d="M802 372l-2 3v2l-3 3h-1v-2c-1 1-3 3-4 3-2 1-3 1-5 0l3-3 1-1 1 1c5 0 7-3 10-6z" class="H"></path><defs><linearGradient id="Cd" x1="819.908" y1="375.283" x2="818.109" y2="365.809" xlink:href="#B"><stop offset="0" stop-color="#c9c5c7"></stop><stop offset="1" stop-color="#e4e5e0"></stop></linearGradient></defs><path fill="url(#Cd)" d="M831 359c2-2 3-3 6-4-4 4-8 7-11 11s-7 9-11 12l-1 1-1-1v-1c0-1 0-1 1-2-2 0-4 0-6 1h-1v-3l3-3c0-2 1-2 1-4 1 3-1 4-1 7h1c1-1 4-1 6-2l1-1c2-1 3-3 5-4l3-2h1l2-3c1-1 2-1 2-2z"></path><path d="M843 343l1-1c1 1 2 1 2 2-1 2-7 11-9 11-3 1-4 2-6 4 0 1-1 1-2 2 1-1 1-3 2-4l-5 2c1-2 2-5 4-6 3-1 5-5 8-6h0c1 0 4-3 5-4z" class="U"></path><path d="M843 343l1-1c1 1 2 1 2 2-1 2-7 11-9 11-3 1-4 2-6 4 2-3 4-5 7-7l3-6-6 5h0l3-4c1 0 4-3 5-4z" class="H"></path><path d="M849 330c0 1-1 2-1 3l3-3c1-1 1-1 2-1v-1l-1 4c-1 2-4 5-6 6l-3 5c-1 1-4 4-5 4h0c-3 1-5 5-8 6l2-2c-1-1 0-1-1-1h-1l-1-1c-3 0-5 5-8 6l7-8c-1 0-2 1-3 1v-1h-1v-1l4-4c1 0 3-3 3-3 1-1 1-2 2-2h2l3-4 1 1v2 1l1-1 3-3c1-1 3-3 4-3 1-1 2 0 2 0z" class="X"></path><path d="M831 345l4-4c2 1 2 1 3 0 0 1 0 1-1 2-1 0-1 1-2 1-1 1-3 1-4 1z" class="G"></path><path d="M838 333l1 1v2 1l-2 3-2 1-4 4-1 1 3-6c-3 2-6 4-8 7h-1v-1l4-4c1 0 3-3 3-3 1-1 1-2 2-2h2l3-4z" class="E"></path><path d="M849 330c0 1-1 2-1 3l3-3c1-1 1-1 2-1v-1l-1 4c-1 2-4 5-6 6l-3 5c-1 1-4 4-5 4h0c-3 1-5 5-8 6l2-2h0c3-3 4-5 5-8 1-1 1-1 1-2-1 1-1 1-3 0l2-1 2-3 1-1 3-3c1-1 3-3 4-3 1-1 2 0 2 0z" class="B"></path><path d="M840 336h0c4 0 4-2 7-3-1 1-2 4-3 4h-1 0c-3 2-4 3-6 3l2-3 1-1z" class="P"></path><path d="M837 340c2 0 3-1 6-3h0 1l-3 6c1-1 3-4 5-5h0l-3 5c-1 1-4 4-5 4h0c-3 1-5 5-8 6l2-2h0c3-3 4-5 5-8 1-1 1-1 1-2-1 1-1 1-3 0l2-1z" class="Q"></path><defs><linearGradient id="Ce" x1="548.856" y1="412.126" x2="563.043" y2="426.727" xlink:href="#B"><stop offset="0" stop-color="#151514"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#Ce)" d="M562 403l16-4c0 1 1 1 1 2-1 2-3 5-4 7l-1 1h0l-2 2s-1 1-1 2v-2c-2 1-2 2-3 3s-2 2-3 2c-2 2-3 5-5 7l-3 6-1 1c-1 2-1 7-2 8l-3-4s0-1-1-2c0 2 0 2 1 3-1 1-1 1-1 3l-1-3-1 1 1 4v6l-1 1h0c-1-1-1-1-1-3h0v-1l1-1v-4c-1 1-2 3-2 4h0c-1 2-2 3-4 4h0c1-3 4-5 5-8l-1-1c-1 2-3 5-5 7-2 1-3 2-6 2-1 0-1-1-2-2-1-5 0-9 1-14 1-3 1-6 3-8l-1-1c1-3 2-6 4-9v1h1c1-1 2-2 4-2l6-6 6-2 2-1c1 0 2 1 3 1z"></path><path d="M557 403l2-1c1 0 2 1 3 1-7 4-13 7-18 14l-1-2v-3l2-1 6-6 6-2z" class="d"></path><path d="M540 412v1h1c1-1 2-2 4-2l-2 1v3l1 2c-5 6-11 16-10 25l1 1c3-1 7-6 10-9 1 0 1-1 2-1l1 3 1 4v6l-1 1h0c-1-1-1-1-1-3h0v-1l1-1v-4c-1 1-2 3-2 4h0c-1 2-2 3-4 4h0c1-3 4-5 5-8l-1-1c-1 2-3 5-5 7-2 1-3 2-6 2-1 0-1-1-2-2-1-5 0-9 1-14 1-3 1-6 3-8l-1-1c1-3 2-6 4-9z" class="a"></path><path d="M540 412v1h1c1-1 2-2 4-2l-2 1c-2 3-5 6-6 10l-1-1c1-3 2-6 4-9z" class="G"></path><defs><linearGradient id="Cf" x1="688.91" y1="613.808" x2="695.804" y2="626.072" xlink:href="#B"><stop offset="0" stop-color="#090809"></stop><stop offset="1" stop-color="#464643"></stop></linearGradient></defs><path fill="url(#Cf)" d="M723 609h5-1c-1 1-2 2-3 2l-1-1-2 1v1h6c2 0 3 0 5-1l-2 2-1 1-3 1c1 1 3 0 5 1l4-1 2 1v3l-3 1c-3 0-5 1-7 2 0 1-1 1-2 2l-3 3c0 1-1 1-1 1h-6c-2 1-4 0-6 1l4 1-4 2 3 2h0l-3 1c0-1-1-1-2-2h-1c-3-1-5-1-8-1-1 0-2 0-2-1l-1-1h0c-3-1-6-1-9-1h-2c-5-1-10 0-16 1-1 1-3 1-4 2-2 0-3 1-3 2-1 1-2 1-3 2h-1l-1-2h-3l3-2h-2c0-1 2 0 3-1h3l1-1h0l3-3c0-2 2-4 3-5l2-1c2 0 3-1 5-2h2c0 1 1 1 2 1 6-2 13-4 20-5h3l2-1h-1 0c1-2 1-2 0-3h3l1-1c1 1 7 0 9 0s6 0 8-1z"></path><path d="M674 619h2c0 1 1 1 2 1-4 2-7 4-11 5-1 0-2 1-3 2 0-2 2-4 3-5l2-1c2 0 3-1 5-2z" class="O"></path><path d="M681 623c8-3 15-5 23-6-2 1-3 2-4 3h4c-2 1-4 2-6 2-1-1-1-1-2-1h-3c-2 0-6 1-8 2h-4z" class="T"></path><path d="M681 623h4c2-1 6-2 8-2h3c1 0 1 0 2 1h-2l-8 3h0l3 1c0 1-1 1-2 2s-3 1-4 0l2-1v-1c-1 0-4 0-5-1 0 0 0-1-1-1s-4 1-5 0c2 0 3-1 5-1z" class="F"></path><path d="M718 616h9c-1 1-3 1-4 1v1h-3c-1 0-1 1-2 0h0-1c-1 0-3 1-4 1-3 1-6 1-9 1h-4c1-1 2-2 4-3l14-1z" class="N"></path><defs><linearGradient id="Cg" x1="702.465" y1="612.084" x2="719.478" y2="611.434" xlink:href="#B"><stop offset="0" stop-color="#868484"></stop><stop offset="1" stop-color="#b7b6b6"></stop></linearGradient></defs><path fill="url(#Cg)" d="M723 609h5-1c-1 1-2 2-3 2l-1-1-2 1v1h6c2 0 3 0 5-1l-2 2-1 1-3 1c1 1 3 0 5 1h0-4-9l-2-2c-4-1-9 0-13 0h-1 0c1-2 1-2 0-3h3l1-1c1 1 7 0 9 0s6 0 8-1z"></path><path d="M727 612c2 0 3 0 5-1l-2 2-1 1-3 1c-2-1-3-1-4-2l5-1z" class="P"></path><path d="M716 614c2-1 4 0 6-1 1 1 2 1 4 2 1 1 3 0 5 1h0-4-9l-2-2z" class="F"></path><path d="M667 625l2 1c1 0 2 0 4-1 1 0 1 0 2-1h1c1 1 4 0 5 0s1 1 1 1c1 1 4 1 5 1v1l-2 1-1 1c-5-1-10 0-16 1-1 1-3 1-4 2-2 0-3 1-3 2-1 1-2 1-3 2h-1l-1-2h-3l3-2h-2c0-1 2 0 3-1h3l1-1h0l3-3c1-1 2-2 3-2z" class="I"></path><path d="M656 632c1 1 3 1 5 2-1 1-2 1-3 2h-1l-1-2h-3l3-2z" class="X"></path><path d="M731 616l4-1 2 1v3l-3 1c-3 0-5 1-7 2 0 1-1 1-2 2l-3 3c0 1-1 1-1 1h-6c-2 1-4 0-6 1l4 1-4 2 3 2h0l-3 1c0-1-1-1-2-2h-1c-3-1-5-1-8-1-1 0-2 0-2-1l-1-1h0c-3-1-6-1-9-1h-2l1-1c1 1 3 1 4 0s2-1 2-2l-3-1h0l8-3h2c2 0 4-1 6-2 3 0 6 0 9-1 1 0 3-1 4-1h1 0c1 1 1 0 2 0h3v-1c1 0 3 0 4-1h4 0z" class="W"></path><path d="M731 616l4-1 2 1v3l-3 1c0-1 0-2-1-3s-1-1-2-1h0z" class="M"></path><path d="M691 626l18 3 4 1-4 2 3 2h0l-3 1c0-1-1-1-2-2h-1c-3-1-5-1-8-1-1 0-2 0-2-1l-1-1h0c-3-1-6-1-9-1h-2l1-1c1 1 3 1 4 0s2-1 2-2z" class="E"></path><path d="M695 630c4 0 9 0 13 1l1 1 3 2h0l-3 1c0-1-1-1-2-2h-1c-3-1-5-1-8-1-1 0-2 0-2-1l-1-1h0z" class="M"></path><path d="M713 619h1c3 0 13-1 14 0-2 3-5 2-8 3-2 1-4 1-6 2l9 3c-3 0-7 1-11 1-4-1-7-2-12-2h-1-3l-2-1h-1l1-1c1 0 2-1 2-2h2c2 0 4-1 6-2 3 0 6 0 9-1z" class="g"></path><path d="M461 323c1-1 1-2 2-2l1 1c2 2 3 4 5 7 4 5 7 12 10 18 1 1 2 2 2 3l2 3h0v-2l-1-1v-2h0c4 5 5 13 7 19l2 6 3 12c0 5 1 11 2 16l-1 1-1-2-1 1c-2-3-4-6-5-9l-1-1-3-7-4-7-10-17-10-13c-2-2-4-5-5-8 3-5 4-11 6-16z" class="M"></path><path d="M468 338c1 2 2 4 2 6 1 2 2 4 3 5 1 2 3 4 3 6-2-1-3-4-5-6h0c-2-2-3-8-3-11z" class="N"></path><defs><linearGradient id="Ch" x1="477.321" y1="352.591" x2="460.274" y2="356.902" xlink:href="#B"><stop offset="0" stop-color="#d0cecc"></stop><stop offset="1" stop-color="#fdfcfd"></stop></linearGradient></defs><path fill="url(#Ch)" d="M461 323c2 5-3 11-2 16 1 1 2 4 3 5 4 5 8 10 12 16 3 5 7 11 10 17l2 3h0c-1 1-1 3-2 4l-4-7-10-17-10-13c-2-2-4-5-5-8 3-5 4-11 6-16z"></path><path d="M484 377l2 3h0c-1 1-1 3-2 4l-4-7c2 1 2 2 3 4h1v-1-2-1zm-20-55c2 2 3 4 5 7 4 5 7 12 10 18l7 20v1l-2-2h-1c-2-2-2-7-4-10v1l-1-1c-1-2 1 0-1-2l-1 1c0-2-2-4-3-6-1-1-2-3-3-5 0-2-1-4-2-6h-1c-1-3-2-5-3-8 0-2-1-3 0-5h0v-3z" class="W"></path><path d="M464 322c2 2 3 4 5 7 0 2 1 4 1 6-3-2-4-6-6-10h0v-3z" class="Z"></path><path d="M469 329c4 5 7 12 10 18l7 20v1l-2-2h-1c-2-2-2-7-4-10-2-6-5-15-9-21 0-2-1-4-1-6z" class="Q"></path><path d="M479 347c1 1 2 2 2 3l2 3h0v-2l-1-1v-2h0c4 5 5 13 7 19l2 6 3 12c0 5 1 11 2 16l-1 1-1-2-1 1c-2-3-4-6-5-9l-1-1-3-7c1-1 1-3 2-4h0c0-3 0-7-1-10l-2-4h1l2 2v-1l-7-20z" class="c"></path><path d="M486 380c1 3 3 9 2 12l-1-1-3-7c1-1 1-3 2-4z" class="N"></path><path d="M491 381c0-3-2-6 0-8l3 12c0 5 1 11 2 16l-1 1-1-2c-2-6-2-13-3-19z" class="L"></path><path d="M479 347c1 1 2 2 2 3l2 3h0v-2l-1-1v-2h0c4 5 5 13 7 19l2 6c-2 2 0 5 0 8-1-1-2-3-2-5 0-3-1-6-3-9l-7-20z" class="J"></path><path d="M234 135h18 34l107-1h35c6 0 13 1 19 0 1 0 1 1 2 1 4 2 9 0 12 0 3 1 6 0 8 1l-1 1h0v2h-1l1 1v1c-2-1-3-1-5-1-8-1-16-1-24-1l-45 1-151-1h-7 0c2-1 6-1 8 0h2c0-1 0-1 1-2v-1c-3-2-8 0-12-1h-1z" class="g"></path><defs><linearGradient id="Ci" x1="711.694" y1="182.987" x2="698.997" y2="126.857" xlink:href="#B"><stop offset="0" stop-color="#8c9792"></stop><stop offset="1" stop-color="#baabaf"></stop></linearGradient></defs><path fill="url(#Ci)" d="M578 153c12-1 23 0 35 0h66 102 45c2 0 4 0 6 1 1 1 1 1 1 3h-2-50-95-72c-11 1-23 1-34 0h-3l1-2c-1 0-1 0-1-1l1-1z"></path><defs><linearGradient id="Cj" x1="540.782" y1="364.113" x2="566.836" y2="359.88" xlink:href="#B"><stop offset="0" stop-color="#888786"></stop><stop offset="1" stop-color="#aaa9a8"></stop></linearGradient></defs><path fill="url(#Cj)" d="M571 317l1-1s1 0 1 2l7 19v2c-1 2-2 4-4 6l-2 2c-1-1-1-3-3-4 0-1 0-1-1-2l-2 1c-1 5-2 9-4 13 0 2 0 4 1 5-3 5-7 9-10 14l-10 18c-1 3-3 5-4 7 0 1-2 3-3 4 0-4 1-8 1-11v-13c0-3-1-6 0-9l1 17c2-5 3-11 4-16l3-11 9-23c3-8 7-15 11-21h0c-1 2-1 2-1 4h-1c-1 3 0 0-1 2h0l-1 2c0 1-1 2-2 3v1l-2 4v1h1l3-6c1-2 3-4 4-5s1-2 1-3l2-2h1z"></path><path d="M545 379v1 3 5c0-2 0-4 1-6v-1 3 2l-1 6h0c-1 3-3 5-4 7v-4c1-5 2-10 4-16z" class="P"></path><defs><linearGradient id="Ck" x1="548.12" y1="374.402" x2="558.847" y2="375.096" xlink:href="#B"><stop offset="0" stop-color="#9b9b99"></stop><stop offset="1" stop-color="#b8b6b7"></stop></linearGradient></defs><path fill="url(#Ck)" d="M546 386c0-1 0 0 1-1 1-4 3-8 4-12 1-2 1-4 3-6h0c2-2 2-3 4-4 3-2 4-5 6-8 0 2 0 4 1 5-3 5-7 9-10 14l-10 18h0l1-6z"></path><path d="M547 360l9-23c3-8 7-15 11-21h0c-1 2-1 2-1 4h-1c-1 3 0 0-1 2h0l-1 2c0 1-1 2-2 3v1l-2 4v1h1l3-6c1-2 3-4 4-5s1-2 1-3l2-2h1c-2 4-5 7-7 11-6 11-9 22-13 34-2 6-5 11-6 17-2 6-3 11-4 16v4c0 1-2 3-3 4 0-4 1-8 1-11v-13c0-3-1-6 0-9l1 17c2-5 3-11 4-16l3-11z" class="J"></path><path d="M571 317l1-1s1 0 1 2l7 19v2c-1 2-2 4-4 6l-2 2c-1-1-1-3-3-4 0-1 0-1-1-2l-2 1h-1v1c-6 4-10 13-14 19 1-6 4-10 6-15l3-7c0-1 1-2 1-3v-1h-1c0-2 1-4 2-5v-3c2-4 5-7 7-11z" class="X"></path><path d="M571 317l1-1s1 0 1 2c-1 0-1 1-1 1-3 6-7 11-9 17h-1c0-2 1-4 2-5v-3c2-4 5-7 7-11z" class="G"></path><path d="M572 319s0-1 1-1l7 19v2c-1 2-2 4-4 6 0-3 1-5 1-8h0c-1-6-4-12-5-18zM264 512c3 0 7 0 10 1l2 2c2 1 4 2 6 2 5 2 8 7 12 10 2 1 3 2 4 3l1 1c0 1 0 1 1 2 0 1 1 2 1 3h0c-1 0-2-1-3-2s-3-2-5-2-5-2-7-3l-6-3c-2-1-4-2-6-2-4-1-9-1-13-1-6 1-10 2-15 4-7 4-11 8-14 16h1c-2 7-1 13 2 19 1 2 3 3 3 6l1 1c-5 0-8-3-11-6-1 1-1 1-1 3h-1-1c-4-4-10-5-12-11-1-5 0-9 3-13 1-2 2-3 4-5 0-1 1-3 2-5l8-3c9-5 17-10 27-14 3 0 8-1 10-2-1-1-2-1-3-1z" class="V"></path><path d="M276 515c2 1 4 2 6 2 1 1 1 3 1 4h0c-3-2-6-3-9-5l2-1z" class="X"></path><path d="M246 527h0l2-1h0l1-1 1-1c2-2 6-2 9-2h0c2 0 5 0 8 1h-6c-6 1-10 2-15 4z" class="a"></path><path d="M221 546l1-1c1-3 5-7 8-8-2 4-4 10-5 14h-1c1-2 1-4 2-6v-1h-1c-1 1-2 1-3 2h-1z" class="T"></path><path d="M264 512c3 0 7 0 10 1l2 2-2 1c-5-1-11-1-16 0l-1-1c3 0 8-1 10-2-1-1-2-1-3-1z" class="G"></path><path d="M238 568c-3-3-7-5-8-9-2-6-1-11 2-16h1c-2 7-1 13 2 19 1 2 3 3 3 6z" class="D"></path><path d="M257 515l1 1c-8 3-16 6-22 12l-6 9c-3 1-7 5-8 8l-1 1-2 3v-2c0-1 0-3 1-3 0-1 0-1 1-2l-2 1c0-1 0-1 1-1v-1h-1c-1 0-1 1-2 1h-1c1-2 2-3 4-5 0-1 1-3 2-5l8-3c9-5 17-10 27-14z" class="e"></path><path d="M222 532l8-3 1 1h-1c0 1-2 1-2 2 0 3-2 5-4 7s-3 5-5 8c0-1 0-3 1-3 0-1 0-1 1-2l-2 1c0-1 0-1 1-1v-1h-1c-1 0-1 1-2 1h-1c1-2 2-3 4-5 0-1 1-3 2-5z" class="W"></path><path d="M222 532l8-3 1 1h-1c0 1-2 1-2 2l-8 5c0-1 1-3 2-5z" class="D"></path><path d="M282 517c5 2 8 7 12 10 2 1 3 2 4 3l1 1c0 1 0 1 1 2 0 1 1 2 1 3h0c-1 0-2-1-3-2s-3-2-5-2-5-2-7-3l-6-3c-2-1-4-2-6-2-4-1-9-1-13-1h6c-3-1-6-1-8-1h0c6-1 11 0 16 0 2 0 5 1 7 1l1-2h0c0-1 0-3-1-4z" class="U"></path><path d="M282 517c5 2 8 7 12 10 2 1 3 2 4 3l1 1c0 1 0 1 1 2 0 1 1 2 1 3h0c-1 0-2-1-3-2-2-3-6-5-8-7 0-1 0-2-1-2 0-1-4-3-6-4 0-1 0-3-1-4z" class="Q"></path><path d="M216 542h1c1 0 1-1 2-1h1v1c-1 0-1 0-1 1l2-1c-1 1-1 1-1 2-1 0-1 2-1 3v2l2-3h1c1-1 2-1 3-2h1v1c-1 2-1 4-2 6h1l1 4c1 2 3 5 2 8-1 1-1 1-1 3h-1-1c-4-4-10-5-12-11-1-5 0-9 3-13z" class="U"></path><path d="M221 546h1c1-1 2-1 3-2h1v1c-1 2-1 4-2 6 0 3 0 6 1 8l1 1v2c-3-2-5-4-7-7-1-1 0-3 0-4v-2h0l2-3z" class="V"></path><defs><linearGradient id="Cl" x1="303.342" y1="200.222" x2="337.329" y2="109.654" xlink:href="#B"><stop offset="0" stop-color="#8d9d9b"></stop><stop offset="1" stop-color="#bba8a9"></stop></linearGradient></defs><path fill="url(#Cl)" d="M186 153h8 15 52 188c3 0 6 0 8 1 1 1 1 2 0 4H254l-46-1c-7 0-16 1-23 0l-1-1c1-1 1-2 2-3z"></path><path d="M257 415c0-2 1-2 1-4 1-5 3-9 5-14 1-2 1-4 2-6l1-1v-5h0v-1c0 2 1 4 2 5h1v3c0 2 0 3 1 4h0v5l-1 5v1l1-2h1c1 0 3-2 4-3l1 2 1-2c1-2 1-5 3-7v1l-1 3v1h1l-1 4c2-1 3-4 5-5l-5 13c-3 8-4 17-5 26 0 3 1 8 0 11v5c0 3 1 5 1 8 0 1 1 3 1 4-2 4 2 10 0 15l-1-4c0 2-1 3-2 5l-1-1h0l-3-9c0-1-1-2-1-3l-3-9c0-1-1-2-1-3-1-4 1-9 1-14-1 3-2 5-3 8-4-13-6-23-5-36z" class="V"></path><path d="M271 438c-1 5-1 10 0 14l1 11-2-1c-1-5-1-9 0-14 0-3 0-6 1-10z" class="W"></path><path d="M277 402c1-2 1-5 3-7v1l-1 3v1h1l-1 4c2-1 3-4 5-5l-5 13c-3 8-4 17-5 26 0 3 1 8 0 11v5c0 3 1 5 1 8 0 1 1 3 1 4-2 4 2 10 0 15l-1-4c0 2-1 3-2 5l-1-1h0l-3-9c0 1 1 1 2 1v-7l-1-4 2 1-1-11c-1-4-1-9 0-14l1-12c-1-7 3-15 4-22l1-2z" class="H"></path><path d="M277 402c1-2 1-5 3-7v1l-1 3v1h1l-1 4-7 22c-1-7 3-15 4-22l1-2z" class="Z"></path><path d="M271 452v-13c2 2 1 9 1 12 0 1 0 2 1 3 0-1 0-3 1-4v-1 5c0 3 1 5 1 8 0 1 1 3 1 4-2 4 2 10 0 15l-1-4c0 2-1 3-2 5l-1-1h0l-3-9c0 1 1 1 2 1v-7l-1-4 2 1-1-11z" class="F"></path><path d="M270 462l2 1 1 5-2-2-1-4z" class="N"></path><path d="M271 466l2 2c0 3 1 6 2 9 0 2-1 3-2 5l-1-1h0l-3-9c0 1 1 1 2 1v-7z" class="T"></path><path d="M453 367v1h0c1 5 2 11 5 15 3 0 6 0 9 1 8 3 15 8 20 14 10 13 16 30 19 45 1 1 2 2 3 2 1-1 1-1 2-1-1 1-1 1-1 2s0 2-1 2v6l1 2c2 3 3 8 3 11 0 2 0 4 1 5 1 2 0 4 0 5 1 3 1 6 1 8 1 2 0 3 1 5v2h1c0 2 0 3 1 5v1-1c0-2 0 0 1-2 0-1-1-3 0-4 1 1-1 7-1 8-2-1-2-3-3-5h-1s-1-1-2-1c-1-1 0-4-1-6v-1-1l-2-14-4-12v-3l-2 1c-1-1-3-4-4-6 0-2-1-3-2-4v-1c1-2 0-3-1-4h1c-1-14-6-25-15-34-9-6-18-7-28-6l1-16c-3-3-4-4-5-8 0-5 0-7 3-11z" class="d"></path><path d="M478 402l1-2c1-1 1-2 1-3-1-1-1 0-1-1v-1c4 3 8 7 10 12h0c-1-1-3-1-4-1-1-1-2-2-3-2-1-1-3-2-4-2z" class="G"></path><defs><linearGradient id="Cm" x1="479.729" y1="398.355" x2="455.965" y2="389.912" xlink:href="#B"><stop offset="0" stop-color="#a5a4a3"></stop><stop offset="1" stop-color="#cfcdcc"></stop></linearGradient></defs><path fill="url(#Cm)" d="M478 402l-13-4c-2 0-6 0-8-2s-1-7-1-10c9 2 16 3 23 9v1c0 1 0 0 1 1 0 1 0 2-1 3l-1 2z"></path><path d="M482 404c1 0 2 1 3 2 1 0 3 0 4 1h0c3 5 6 11 8 16l4 12c1 3 2 6 4 8h1c1 1 2 2 3 2 1-1 1-1 2-1-1 1-1 1-1 2s0 2-1 2v6l1 2c2 3 3 8 3 11 0 2 0 4 1 5 1 2 0 4 0 5 1 3 1 6 1 8 1 2 0 3 1 5v2h1c0 2 0 3 1 5v1-1c0-2 0 0 1-2 0-1-1-3 0-4 1 1-1 7-1 8-2-1-2-3-3-5h-1s-1-1-2-1c-1-1 0-4-1-6v-1-1l-2-14-4-12v-3l-2 1c-1-1-3-4-4-6 0-2-1-3-2-4v-1c1-2 0-3-1-4h1c-1-14-6-25-15-34h1 1v1c3 2 4 5 6 7 1 2 2 3 3 5v-1c-1-1-1-2-2-3-1-3-2-5-4-8-2-1-4-3-5-5h0z" class="Z"></path><path d="M482 404c1 0 2 1 3 2l4 4c5 7 10 25 10 33v1l-2-2h0c-1-14-6-25-15-34h1 1v1c3 2 4 5 6 7 1 2 2 3 3 5v-1c-1-1-1-2-2-3-1-3-2-5-4-8-2-1-4-3-5-5h0z" class="V"></path><defs><linearGradient id="Cn" x1="512.658" y1="463.707" x2="505.285" y2="467.96" xlink:href="#B"><stop offset="0" stop-color="#2e2d2d"></stop><stop offset="1" stop-color="#4d4d4d"></stop></linearGradient></defs><path fill="url(#Cn)" d="M505 456c1-1 1-1 3-2 5 4 5 16 5 22l1 10c1 2 1 6 1 8h-1s-1-1-2-1c-1-1 0-4-1-6v-1-1l-2-14-4-12v-3z"></path><path d="M511 485h0c1-2 1-6 1-8l1 3v5l1 1c1 2 1 6 1 8h-1s-1-1-2-1c-1-1 0-4-1-6v-1-1z" class="P"></path><path d="M474 128c-3 0-8 1-10-1s-3-4-3-7c0-4 1-8 5-12 6-6 18-7 26-2 4 2 7 7 11 7 1 0 2-1 2-2 2-2 1-7 1-10-2-6-6-10-11-13 10-7 16-14 22-24 6 10 12 16 21 23-3 1-5 2-7 6-4 4-5 11-3 17 0 2 1 3 3 4 3-1 5-4 8-6 2-2 4-3 6-4 6-2 12-2 17 1s8 7 10 12c1 3 1 6-1 8-1 0-4 1-5 0h-3c-1-2 0-6-1-8-2-4-5-5-8-6-3 0-5-1-7-2-5 2-8 5-11 9l-3 4-3-3c-2-1-3-2-5-4-2-4-3-9-2-13v-1c1-4 3-9 6-13h0c-5-3-8-7-12-12-1 1-1 2-2 3-2 2-4 5-6 6h-1v1l-1 2c1 2 2 1 1 3-2-1-2-2-4-3v2c3 3 4 6 6 10 1 3 1 7 1 10-2 3-4 6-7 7-2 1-4 0-6-1l-1 1 1 1-1 1c-4-5-8-10-14-10-4-1-9 0-12 2s-4 5-4 8c0 2 0 3 1 5 1 1 3 2 4 2l2 2z" class="g"></path><path d="M530 119c2-1 4-1 6-1l-3 4-3-3z" class="V"></path><path d="M504 90c-1 0-3-2-4-2v-1c2-2 2-2 4-2 0 1 1 2 2 2l2-2v1l-1 2c1 2 2 1 1 3-2-1-2-2-4-3v2z" class="W"></path><path d="M547 109c4-1 9-1 13 1 3 2 6 4 6 8 1 2 1 5 0 7h-3c-1-2 0-6-1-8-2-4-5-5-8-6-3 0-5-1-7-2z" class="h"></path><path d="M508 85h1c2-1 4-4 6-6 1-1 1-2 2-3 4 5 7 9 12 12h0c-3 4-5 9-6 13v1c-1 4 0 9 2 13 2 2 3 3 5 4l3 3c-2 6-4 11-4 17v6l1 1c-5-2-19 3-22 0h-1c-1 1-1 1-2 1s-2 1-2 1h-1l1-1c2-10-1-20-6-28l1-1-1-1 1-1c2 1 4 2 6 1 3-1 5-4 7-7 0-3 0-7-1-10-2-4-3-7-6-10v-2c2 1 2 2 4 3 1-2 0-1-1-3l1-2v-1z" class="X"></path><path d="M514 128c1-1 2-1 3 0 2 0 5 3 5 5 1 1 1 4 0 5-1 2-2 3-4 3h-2c-2 0-3-1-5-2-1-2-1-4-1-6 1-2 2-4 4-5z" class="D"></path><path d="M514 137c2 0 3-1 4-2h0 2c-1 2-1 2-2 3-2 0-2 0-4-1z" class="a"></path><path d="M514 137h0c-1-1-1-2-1-3 1-2 2-2 3-3 1 0 2 0 3 1s1 2 1 3h-2 0c-1 1-2 2-4 2z" class="V"></path><path d="M529 145c3 7 7 15 14 18h1l2 2c1 0 2 0 2 1 2 2 2 2 4 2 1-1 1-1 1-2l1 1v1c1 1 2 1 3 1l2-2c2-1 4-3 6-3-1 2-2 5-3 7-1 1-1 2-2 3-1 0-2 1-2 2 1 2 3 3 4 5-1 2-1 2-3 3h0c-1 1-2 1-3 2l-10-5c-4-2-9-4-14-5 0 0-2-1-3-1-4-1-7-1-11-1-1-1 0-3 0-4h-2c-2-1-4 0-6 0-8 0-16 2-23 6-4 2-8 6-13 7h-1c0-2-1-3 0-4l2-2c8-6 18-11 28-12-1-1-1-1-3-1v-1l-1-1c-1 1-1 1-1 2-1 1-1 1-2 1l-1-2-1-1h2l4-1c-1-2 0-5 0-7l1-3 1-3h1s1-1 2-1 1 0 2-1h1c3 3 17-2 22 0l-1-1z" class="d"></path><path d="M549 171c3 2 6 3 9 5 1 2 3 3 4 5-1 2-1 2-3 3h0-1v-3c-2-1-3-3-5-4s-3-2-5-3v-1h1v-2z" class="W"></path><path d="M554 168c1 1 2 1 3 1l2-2c2-1 4-3 6-3-1 2-2 5-3 7-1 1-1 2-2 3-1 0-2 1-2 2-3-2-6-3-9-5l-6-3c2 0 2 0 4 1 1 0 2 1 3 1 2 0 2 0 4-2z" class="I"></path><path d="M518 160l1-1c1 1 1 1 2 1l2-1c3 0 7 0 10 1 2 1 5 1 7 3l-1 2v1l2-2c2 0 3 0 5 1 1 0 2 0 2 1 2 2 2 2 4 2 1-1 1-1 1-2l1 1v1c-2 2-2 2-4 2-1 0-2-1-3-1-2-1-2-1-4-1l-4-1-15-3v-2c-1 0-1 0-2 1 1 0 1 0 1 1-1 0-3 1-5 0v-4z" class="f"></path><path d="M525 161h1 2l1 2h-2c-2-1-2-1-2-2zm-7 9c11 0 19 1 29 7l11 7h1c-1 1-2 1-3 2l-10-5c-4-2-9-4-14-5 0 0-2-1-3-1-4-1-7-1-11-1-1-1 0-3 0-4z" class="I"></path><defs><linearGradient id="Co" x1="519.028" y1="157.605" x2="519.339" y2="150.1" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#9a9998"></stop></linearGradient></defs><path fill="url(#Co)" d="M529 145c3 7 7 15 14 18h1l2 2c-2-1-3-1-5-1l-2 2v-1l1-2c-2-2-5-2-7-3-3-1-7-1-10-1l-2 1c-1 0-1 0-2-1l-1 1-1-1h0l-1 3v2h-7l-6 1c-1-1-1-1-3-1v-1l-1-1c-1 1-1 1-1 2-1 1-1 1-2 1l-1-2-1-1h2l4-1c-1-2 0-5 0-7l1-3 1-3h1s1-1 2-1 1 0 2-1h1c3 3 17-2 22 0l-1-1z"></path><path d="M505 150h1v1 1c-2 1-1 0-2 0h-1c1-1 1-2 2-2z" class="G"></path><path d="M502 148h1s1-1 2-1 1 0 2-1h1c3 3 17-2 22 0v2 1c-1 0-1 1-2 1-7 0-15-1-22 0h-1c-1 0-1 1-2 2h-1l-1-1 1-3z" class="B"></path><path d="M530 148v1c-1 0-1 1-2 1-7 0-15-1-22 0h-1l-1-1h0l19-1h7z" class="E"></path><path d="M530 148v1c-1 0-1 1-2 1-2-1-6 0-7-1l2-1h7z" class="F"></path><path d="M501 151l1 1h1 1c-1 2-1 4 0 5s5 2 6 2h5l1 1v2 2h-7l-6 1c-1-1-1-1-3-1v-1l-1-1c-1 1-1 1-1 2-1 1-1 1-2 1l-1-2-1-1h2l4-1c-1-2 0-5 0-7l1-3z" class="E"></path><path d="M515 159l1 1v2 2h-7 0c1-1 1-1 2-3l-1-1c1-1 2 0 3 0l2-1z" class="f"></path><path d="M511 161l-1-1c1-1 2 0 3 0l1 1c-1 1-1 1-2 1l-1-1z" class="h"></path><path d="M500 161s1 0 1 1c2 0 2 2 3 2l1-1c-1-1-2-1-3-1v-1c2 0 4-1 6-1v1c0 1 1 2 1 3h0l-6 1c-1-1-1-1-3-1v-1l-1-1c-1 1-1 1-1 2-1 1-1 1-2 1l-1-2-1-1h2l4-1z" class="I"></path><path d="M543 248l3-8v1c0 5-1 9-2 14 0 13 4 25 10 36 1 3 3 5 5 8 2 2 6 5 6 8v1c-2 8-7 15-10 22l-6 17c-1 3-2 7-3 9v5l1-1h0l-3 11c-1 5-2 11-4 16l-1-17 2-109c0-2-1-3-2-4v-3h1l1-3 1 1h0l1-4z" class="M"></path><path d="M539 254h1l1-3 1 1-1 9c0-2-1-3-2-4v-3z" class="P"></path><path d="M544 371l-1-2c0-3 2-9 3-13v5l1-1h0l-3 11z" class="N"></path><path d="M554 291c1 3 3 5 5 8 2 2 6 5 6 8v1l-1-2c-1 0-2 0-3-1h0c-2-2-3-4-4-6-1-1-2-3-2-4-1-2-1-3-1-4z" class="T"></path><path d="M548 301h1c0 1 0 2 1 2 1 2 1 5 2 7l1 1c0 1 0 5-1 7h0c0 1-1 2-1 3s-1 2-1 3h-1c-1-2-1-3-1-5 1-2 1-3 3-5 0-1 0-3-1-4l-2-6v-3z" class="G"></path><defs><linearGradient id="Cp" x1="556.632" y1="797.815" x2="657.59" y2="778.335" xlink:href="#B"><stop offset="0" stop-color="#cccac9"></stop><stop offset="1" stop-color="#efefee"></stop></linearGradient></defs><path fill="url(#Cp)" d="M615 759c12-6 26-12 36-22v1c1 3-1 6-2 9-3 6-7 12-13 17-1 2-3 3-4 4-1 2-1 4-2 6-2 4-8 7-12 8h1c3-4 7-9 9-13-4 3-7 8-12 10-2 1-5 2-7 3-2 2-3 4-5 5l-10 11-5 6-3 5c-4 7-6 14-10 22l-3 6c-2 1-2 2-3 4-1 1 0 0-1 2h0c-1-1-1-1-1-2l1-1c1-2 1-5 0-7v-1l-2 4c-1 1-1 1-2 1l-1-1 1-5v-1c3-2 5-8 6-10v-3c4-7 8-13 12-20 1-3 3-6 5-8h0c1 0 1-1 2-2h0l1-1c0-1 1-2 1-3h1c1-2 1-2 2-3h1l12-15c2-2 2-2 3-4 1-1 3-1 4-2z"></path><path d="M591 794l1 1-1 2 1 2c1-1-1 0 1-1h1l-5 6-3 5-3-2c1-1 1-1 1-2h0c2-4 5-8 7-11z" class="H"></path><path d="M589 804c-1 0-1-1-2-2 1-2 2-4 4-5l1 2c1-1-1 0 1-1h1l-5 6z" class="X"></path><path d="M588 789h0c1 0 1-1 2-2h0l1-1c0-1 1-2 1-3h1c1-2 1-2 2-3l-9 16-15 24v-3c4-7 8-13 12-20 1-3 3-6 5-8z" class="M"></path><path d="M608 773c6-8 17-13 26-18-4 4-9 7-13 11-3 3-5 6-8 8s-5 3-7 5c-6 4-10 10-14 16l-1-1 12-15c1-2 3-4 5-6z" class="e"></path><path d="M608 773h0c2-1 4-3 7-4 2-1 3-3 5-4-1 3-3 4-5 6-4 4-7 6-12 8 1-2 3-4 5-6z" class="H"></path><path d="M584 805h0c0 1 0 1-1 2l3 2c-4 7-6 14-10 22l-3 6c-2 1-2 2-3 4-1 1 0 0-1 2h0c-1-1-1-1-1-2l1-1c1-2 1-5 0-7v-1c0-2 2-5 3-6 4-8 7-15 12-21z" class="B"></path><path d="M763 371c2-1 3-2 4-2l-1 2c-1 3 0 7 1 10 0 3 2 6 4 8 0 10 2 20 4 30 0 5 0 11 1 17 0 1 1 1 2 2 0 1 1 1 2 2 3-1 8-10 11-13 5-5 10-10 17-15-2 12-5 22-8 33-1 3-2 6-3 8l-1 2-1 3c0 2-4 4-5 6h0-1l-8 8-1 1c-1 1-1 2-3 2-1-1-1-2-2-4 1-2 2-3 2-5h0v-1c0-1 0-1 1-2 0-1 0-2 1-3v-1-1c1-1 0-1 0-2l-3 7-2 4-5 8c0 1-1 2-1 3l-3 4v-1c0-1 1-2 1-2-1-1 0-1-1-2h0l1-2-1-1-1 1h-1c1-2 2-4 2-7-1-1 1-6 2-7 0-1 1-4 2-5l-1-1c2-1 2-4 3-6 0-1 0-3 1-4-1-2 0-4-1-6 0-2-1-4-1-6l-1-9v-2l-1-8c0-2 0-4-1-5 0-1-1-3-1-4v-2l-2-9-2-8v-1h1c0-4-2-8-1-12 1 0 1-1 1-2h0z" class="V"></path><path d="M796 428c2-4 6-7 9-10-1 3-4 5-5 7-2 2-2 5-3 7-3 5-6 8-9 12l-1 2v-3c0-1 2-2 2-3 2-3 4-7 6-10 1 0 1-1 1-2z" class="X"></path><path d="M796 428c0 1 0 2-1 2-2 3-4 7-6 10 0 1-2 2-2 3v3c-3 6-5 13-8 19-1 1 0 4 0 5l2 2-1 1c-1 1-1 2-3 2-1-1-1-2-2-4 1-2 2-3 2-5h0v-1c0-1 0-1 1-2 0-1 0-2 1-3v-1-1c1-1 0-1 0-2 4-10 11-20 17-28z" class="H"></path><path d="M789 464c-1-5 2-12 2-17 0-2 1-4 2-6h1v-1l2 1h0c0-2 1-4 2-5v-1c1-2 1-3 2-4v-2-1c1-1 2-1 2-2l1 1c-2 4-4 10-6 15v2c-1 1 0 1-1 2h0v2h-1v6s1 0 2-1l-1 2-1 3c0 2-4 4-5 6h0-1z" class="U"></path><path d="M797 453l-1 2-1 3h-2c0-1 1-3 2-4 0 0 1 0 2-1z" class="d"></path><path d="M762 385h1c2 5 3 10 4 16l4 32c0 3 1 6 2 9 0 2 0 4 1 6-1 2-1 5-1 7 0 1 1 2 1 2 0 2-1 4-1 5l1 3h0l1-1c0-1 0-1 1-1l-2 4-5 8c0 1-1 2-1 3l-3 4v-1c0-1 1-2 1-2-1-1 0-1-1-2h0l1-2-1-1-1 1h-1c1-2 2-4 2-7-1-1 1-6 2-7 0-1 1-4 2-5l-1-1c2-1 2-4 3-6 0-1 0-3 1-4-1-2 0-4-1-6 0-2-1-4-1-6l-1-9v-2l-1-8c0-2 0-4-1-5 0-1-1-3-1-4v-2l-2-9-2-8v-1z" class="N"></path><path d="M772 445v-1c1 5 0 9-1 13v-1l-1-1-1 1-1-1c2-1 2-4 3-6 0-1 0-3 1-4z" class="Q"></path><path d="M769 456l1-1 1 1v1l-2 12v1h1v1c-1 1-1 2-1 4 0 1-1 2-1 3l-3 4v-1c0-1 1-2 1-2-1-1 0-1-1-2h0l1-2-1-1-1 1h-1c1-2 2-4 2-7-1-1 1-6 2-7 0-1 1-4 2-5z" class="P"></path><path d="M767 461c0 1 1 1 1 2 0 4 0 6-1 10v1 2h0l1 2-3 4v-1c0-1 1-2 1-2-1-1 0-1-1-2h0l1-2-1-1-1 1h-1c1-2 2-4 2-7-1-1 1-6 2-7z" class="S"></path><path d="M516 170h2c0 1-1 3 0 4 4 0 7 0 11 1 1 0 3 1 3 1 5 1 10 3 14 5l10 5-21 22-1 1h-18c-4 0-8 1-12 0l-1-1c-1 1 0 1-1 2 0-2-1-3-2-4l-1-1c0-1-1-2-1-3h2l3 2h0v-1c-2-4-6-5-7-9-1-3-6-6-8-8l-1-1-1-2h0l2-3c1 0 3-1 4-1 3-2 8-3 11-4l4-1h7 2v-1-3z" class="P"></path><path d="M519 195v-5h1v3c1 2 1 5 1 7l2 3-1 2-3-3c-1-1-1-2-1-3 1-1 0-3 1-4z" class="F"></path><path d="M519 202v-2h1c-1-2-1-3-1-4l1-1c0 2 1 3 1 5l2 3-1 2-3-3z" class="K"></path><path d="M523 203h0c2-1 3-2 5-4 4-4 9-7 15-9-2 2-5 4-8 6-2 2-5 6-8 8-1 1-3 1-5 1h0l1-2z" class="O"></path><path d="M496 194l2 2 8 6c0 1 1 3 2 4 3 2 10 1 13 0h1c3 0 6 1 8 0l8-8h1c-2 2-5 4-6 7h1l2-2c0 2-1 3-2 4l1 1-1 1h-18c-4 0-8 1-12 0l-1-1c-1 1 0 1-1 2 0-2-1-3-2-4l-1-1c0-1-1-2-1-3h2l3 2h0v-1c-2-4-6-5-7-9z" class="E"></path><path d="M518 174c4 0 7 0 11 1 1 0 3 1 3 1 5 1 10 3 14 5v2h-3c-7 0-17 3-22 8-1-2-1-3-1-5v-6-5h-1l-1-1z" class="Z"></path><path d="M518 174c4 0 7 0 11 1-3 0-5 0-7 2-1 1-2 2-2 3v-5h-1l-1-1z" class="Q"></path><path d="M532 176c5 1 10 3 14 5v2h-3v-2c-2 0-3 0-4-1-1 0-1 1-1 0h1c-1-3-5-2-7-4z" class="T"></path><defs><linearGradient id="Cq" x1="496.41" y1="193.412" x2="502.977" y2="175.871" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#a4a3a1"></stop></linearGradient></defs><path fill="url(#Cq)" d="M516 170h2c0 1-1 3 0 4l1 1h1v5 6 4h-1v5c-1 1 0 3-1 4 0 1 0 2 1 3l3 3h0v1h-1c-3 1-10 2-13 0-1-1-2-3-2-4l-8-6-2-2c-1-3-6-6-8-8l-1-1-1-2h0l2-3c1 0 3-1 4-1 3-2 8-3 11-4l4-1h7 2v-1-3z"></path><path d="M499 192c2 0 3 2 5 2 1 0 2 0 3 1 1 0 1 1 2 1l1 2v3c-2-2-3-4-6-4l-1-1c-1-2-2-3-4-4z" class="B"></path><path d="M488 186c2 0 3 1 4 2 2 2 5 2 7 4 2 1 3 2 4 4-2 1-3 0-5 0l-2-2c-1-3-6-6-8-8z" class="Q"></path><path d="M507 174h7v13l-2 1-1 4s-1-1-1-2v-1l-1 2h-1c0-2 0-2 1-4h2 1v-9-3c-1-1-3-1-5-1z" class="H"></path><path d="M492 179c3-2 8-3 11-4v3c1 1 2 0 2 2l-1 1c-1-1-1-1-1-2-1 0-2-1-3 0h-2v2h-1c-2 0-4 2-6 2v-2-1h0l1-1z" class="G"></path><path d="M514 174h2v8 6h0-1c-1 3 0 7-1 10 0 1-2 1-1 2h1v1l-2 2-1-1 1-1c-1-1 0-1-1-2s0-5 0-7l1-4 2-1v-13z" class="B"></path><path d="M503 175l4-1c2 0 4 0 5 1v3h-1-1c0 1-1 1-1 2-2 1-3 2-4 3v1h-1c-2 0-3-1-5-2 0-2 0-2 1-3s2 0 3 0c0 1 0 1 1 2l1-1c0-2-1-1-2-2v-3z" class="Z"></path><path d="M516 170h2c0 1-1 3 0 4l1 1h1v5 6 4h-1v5h-1-2v-7h0v-6-8-1-3z" class="E"></path><path d="M518 189v6h-2v-7h0l2 1z" class="T"></path><path d="M516 182c4-1 0-3 2-5 1 4 0 8 0 12l-2-1v-6z" class="N"></path><path d="M516 170h2c0 1-1 3 0 4l1 1-1 2c-2 2 2 4-2 5v-8-1-3z" class="U"></path><path d="M511 202l1 1 2-2v-1h-1c-1-1 1-1 1-2 1-3 0-7 1-10h1v7h2 1c-1 1 0 3-1 4 0 1 0 2 1 3l3 3h0v1h-1c-3 1-10 2-13 0-1-1-2-3-2-4l-8-6c2 0 3 1 5 0l1 1c3 0 4 2 6 4l1 1z" class="F"></path><path d="M503 196l1 1 7 9h-2c-1-2-2-3-3-4h0l-8-6c2 0 3 1 5 0z" class="c"></path><path d="M516 195h2 1c-1 1 0 3-1 4 0 1 0 2 1 3l3 3h0v1h-1c-3 1-10 2-13 0-1-1-2-3-2-4h0c1 1 2 2 3 4h2c2-1 5-4 6-5-1-2-1-4-1-6z" class="B"></path><defs><linearGradient id="Cr" x1="280.814" y1="420.452" x2="242.755" y2="436.311" xlink:href="#B"><stop offset="0" stop-color="#c1bfbe"></stop><stop offset="1" stop-color="#e6e6e5"></stop></linearGradient></defs><path fill="url(#Cr)" d="M261 374c1 1 1 2 1 3s3 4 4 6h0l3 6h-1c-1-1-2-3-2-5v1h0v5l-1 1c-1 2-1 4-2 6-2 5-4 9-5 14 0 2-1 2-1 4-1 13 1 23 5 36 1-3 2-5 3-8 0 5-2 10-1 14 0 1 1 2 1 3l3 9c0 1 1 2 1 3l3 9h0l1 1c1-2 2-3 2-5l1 4h0l2 5 1 2 1 4c-1 0-2 0-3 1l1 3 1 1v1 1c-1-1-1-2-2-3-1-2-1-3-3-4l1 4v1c-1-1-1-3-2-4-2-4-3-8-5-12v1h-1v-1c0-1 0-1-1-2l-2-5h0v2h-1v2l1 3h-1l-3-2v1c1 1 2 3 3 4h-3c-1-1-2-3-3-4l-1 1-1 1-1-1h1v-1l-6-9c-1-2-2-4-3-5-2-2-4-7-6-9-2-5-6-11-8-16 0-2-1-3-1-4 2 0 2-1 3 0 2 0 5 0 7-1l1-1c-1-15 8-26 13-39v-3c3-6 5-12 6-19z"></path><path d="M260 467h1v-1h1v1c0 3 0 4 1 6l1 1v2h-1v2l1 3h-1l-3-2-2-3 1-1 3 4c0-1 0-1-1-2v-2c0-2-1-5-1-7v-1z" class="U"></path><path d="M245 435c-1-10 3-19 8-28h1v1c-3 5-4 12-5 18-1 2-2 7-4 9h0z" class="T"></path><path d="M260 467c-1-5-3-11-4-16-1-4-1-8-1-12-1-4-2-8-2-12 0-9 3-18 7-27 1-3 2-8 4-11 1 2-5 16-6 18-1 3-1 5-1 8-1 13 1 23 5 36 1-3 2-5 3-8 0 5-2 10-1 14 0 1 1 2 1 3l3 9c0 1 1 2 1 3l3 9h0l1 1c1-2 2-3 2-5l1 4h0l2 5 1 2 1 4c-1 0-2 0-3 1l1 3 1 1v1 1c-1-1-1-2-2-3-1-2-1-3-3-4l1 4v1c-1-1-1-3-2-4-2-4-3-8-5-12v1h-1v-1c0-1 0-1-1-2l-2-5h0l-1-1c-1-2-1-3-1-6v-1h-1v1h-1z" class="G"></path><path d="M262 466c0-1-1-4-1-5l-1-5h0c-1-2-1-4-1-5-1-2-1-3-1-5h0c1 2 2 4 3 5 0 1 1 2 1 2-1 5 2 10 1 15l-1-1v-1z" class="B"></path><path d="M262 453c1 3 1 5 2 7 1 3 1 7 3 9h1c0 1 1 2 1 3l3 9c-1 0-2 0-2-1l-3-6c-1-2-1-4-2-6-1 2 3 11 3 13v1h-1v-1c0-1 0-1-1-2l-2-5h0l-1-1c-1-2-1-3-1-6l1 1c1-5-2-10-1-15z" class="Y"></path><path d="M268 481c0-2-4-11-3-13 1 2 1 4 2 6l3 6c0 1 1 1 2 1h0l1 1c1-2 2-3 2-5l1 4h0l2 5 1 2 1 4c-1 0-2 0-3 1l1 3 1 1v1 1c-1-1-1-2-2-3-1-2-1-3-3-4l1 4v1c-1-1-1-3-2-4-2-4-3-8-5-12z" class="C"></path><path d="M270 480c0 1 1 1 2 1h0l2 5v1l-2-1c-2-2-2-3-2-6z" class="e"></path><path d="M275 477l1 4h0l2 5 1 2 1 4c-1 0-2 0-3 1l-2-4-1-2v-1l-2-5 1 1c1-2 2-3 2-5z" class="X"></path><path d="M276 481l2 5-3 3-1-2v-1h1c1-2 1-3 1-5z" class="T"></path><path d="M278 486l1 2 1 4c-1 0-2 0-3 1l-2-4 3-3z" class="G"></path><path d="M245 435h0c2-2 3-7 4-9 1 7 3 16 2 23-1 1 0 3 1 5 1 5 4 11 4 16l3 5-1 1 2 3v1c1 1 2 3 3 4h-3c-1-1-2-3-3-4l-1 1-1 1-1-1h1v-1l-6-9c-1-2-2-4-3-5-2-2-4-7-6-9-2-5-6-11-8-16 0-2-1-3-1-4 2 0 2-1 3 0 2 0 5 0 7-1l1-1c1 0 1 0 2 1l1-1z" class="V"></path><path d="M242 435c1 0 1 0 2 1 0 1-1 3-2 4-1 0-1 0-2 1h-2c-1 0-2 0-3-1 0-1-1-1-2-2v-1h1c2 0 5 0 7-1l1-1z" class="d"></path><path d="M231 437c2 0 2-1 3 0h-1v1c1 1 2 1 2 2 1 1 2 1 3 1 2 2 2 4 4 7 0 1 6 9 5 10 3 4 5 8 7 12h1 1l3 5-1 1 2 3v1c1 1 2 3 3 4h-3c-1-1-2-3-3-4l-1 1-1 1-1-1h1v-1l-6-9c-1-2-2-4-3-5-2-2-4-7-6-9-2-5-6-11-8-16 0-2-1-3-1-4z" class="H"></path><path d="M242 448c0 1 6 9 5 10 0 3 2 4 2 7l-7-11c0-3-1-4 0-6z" class="W"></path><path d="M249 465c0-3-2-4-2-7 3 4 5 8 7 12h1 1l3 5-1 1c-2 0-3-4-5-5-1-2-3-4-4-6z" class="N"></path><path d="M246 466c1 0 2 2 3 3 2 3 5 6 7 8 1 1 2 3 4 3 1 1 2 3 3 4h-3c-1-1-2-3-3-4l-1 1-1 1-1-1h1v-1l-6-9c-1-2-2-4-3-5z" class="B"></path><path d="M233 438c1 1 2 1 2 2 1 1 2 1 3 1 2 2 2 4 4 7-1 2 0 3 0 6-3-5-7-10-9-16z" class="U"></path><path d="M580 339c1 1 1 2 3 3h1l3 9 2 4v3h0v6h-1c-1-1-2-1-3-1h-1l-1 2h0c1 1 1 2 1 3l-2-1c-1 1-1 1-1 3 0 3 1 8-1 11l2 1v2l1 1-3 2h0c-1 1-2 2-2 3-1 2 1 4 2 6l1 2s-1 0-1 1h-2l-16 4c-1 0-2-1-3-1l-2 1-6 2-6 6c-2 0-3 1-4 2h-1v-1c-2 3-3 6-4 9-3 6-4 15-9 20v1l-1 1c-2 1-2 1-3 0-1-2-1-2 0-4l2-1v-75 1c1 5 0 11 0 16v35c0 4 0 9 1 12 0 2 0 10-1 12h0c-1 0-1 1-2 1 0 1 1 1 1 2h1 1v-4l1-1v-3-2h1c0-2 1-4 1-5l1-2v-1c0-1 0-2 1-2v-1l3-6v-1l1-1c1-1 1-2 1-3l1-1 2-3v-1h0c1-1 1-1 1-2 1-1 1-1 2-1 2-3 3-5 5-8 2-1 2-2 4-3l3-3h1l1-1 2-2c2-1 5-3 8-4l2-1 3-1h3v-1c2 0 3 0 4 1h3-1c0-2 1-4 0-6v-2c-1 1-2 2-3 2s0 0-1 1l-1-1c-1 0-2 1-3 1s-2 1-3 1c-1 1-3 1-4 2l-1 1-2 1c-2 2 1 0-1 1l-1 1-3 3c-1 0-2 1-2 1-1 2-3 4-5 5h0-1v-1-1l-1 2-2 2v2h-1v1l-1 1-1 2c-1 1-1 1-1 2l-2 2-1 1v1l-2-1v-5h1v-6h1c0 3-1 7-1 11 1-1 3-3 3-4 1-2 3-4 4-7l10-18c3-5 7-9 10-14-1-1-1-3-1-5 2-4 3-8 4-13l2-1c1 1 1 1 1 2 2 1 2 3 3 4l2-2c2-2 3-4 4-6z" class="K"></path><path d="M568 342l2-1c1 1 1 1 1 2 2 1 2 3 3 4-1 2-2 3-4 4-1 3-3 6-5 9-1-1-1-3-1-5 2-4 3-8 4-13z" class="V"></path><path d="M571 343c2 1 2 3 3 4-1 2-2 3-4 4 0-3 0-6 1-8z" class="S"></path><defs><linearGradient id="Cs" x1="566.763" y1="381.603" x2="564.659" y2="352.829" xlink:href="#B"><stop offset="0" stop-color="#767879"></stop><stop offset="1" stop-color="#a5a19f"></stop></linearGradient></defs><path fill="url(#Cs)" d="M573 354l3-2 2 2-1 1 1 1c-2 2-5 5-7 8h0v1c0 1-1 1-1 2v1c1 0 1 0 2 1l1-1 2-1c1 1-1 2 1 3 1-1 1-1 3-1-4 3-7 3-10 5-4 2-8 5-11 7-1 2-5 5-7 6 6-12 13-23 22-33z"></path><path d="M558 381s0-1 1-2h0v-1l1-1c2-1 3-2 5-4l1-1h1v-3c1-2 2-4 4-5v1c0 1-1 1-1 2v1c1 0 1 0 2 1l1-1 2-1c1 1-1 2 1 3 1-1 1-1 3-1-4 3-7 3-10 5-4 2-8 5-11 7z" class="P"></path><defs><linearGradient id="Ct" x1="582.043" y1="368.069" x2="579.054" y2="347.062" xlink:href="#B"><stop offset="0" stop-color="#777575"></stop><stop offset="1" stop-color="#8b8b8a"></stop></linearGradient></defs><path fill="url(#Ct)" d="M584 342l3 9 2 4v3h0v6h-1c-1-1-2-1-3-1h-1l-1 2h0c1 1 1 2 1 3l-2-1c-1 1-1 1-1 3l-1-1h-1c-2 0-2 0-3 1-2-1 0-2-1-3l-2 1-1 1c-1-1-1-1-2-1v-1c0-1 1-1 1-2v-1h0c2-3 5-6 7-8l-1-1 1-1-2-2-3 2c4-4 7-8 10-12h1z"></path><path d="M584 342l3 9 2 4v3c-2-2-2-4-3-6v-2c-1-1-1-3-2-5h-1c-1 4-3 7-5 10v1l-1-1 1-1-2-2-3 2c4-4 7-8 10-12h1z" class="T"></path><defs><linearGradient id="Cu" x1="559.527" y1="428.489" x2="548.158" y2="387.04" xlink:href="#B"><stop offset="0" stop-color="#cdcccb"></stop><stop offset="1" stop-color="#f3f1f0"></stop></linearGradient></defs><path fill="url(#Cu)" d="M527 441c0-7 3-15 6-21 6-16 17-32 34-38 2-1 7-3 9-3 1 0 3 1 4 2l2 1v2l1 1-3 2h0c-1 1-2 2-2 3-1 2 1 4 2 6l1 2s-1 0-1 1h-2l-16 4c-1 0-2-1-3-1l-2 1-6 2-6 6c-2 0-3 1-4 2h-1v-1c-2 3-3 6-4 9-3 6-4 15-9 20z"></path><path d="M571 386c4-2 7-2 11-2l1 1-3 2-1-1h-1-5v1l-2-1z" class="U"></path><path d="M554 396c0 1-1 2-2 3v1c0 2 0 4-1 5l-6 6c-2 0-3 1-4 2h-1v-1c3-7 8-12 14-16z" class="H"></path><defs><linearGradient id="Cv" x1="558.451" y1="393.124" x2="574.394" y2="394.364" xlink:href="#B"><stop offset="0" stop-color="#aeadab"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#Cv)" d="M558 392c4-3 9-5 13-6l2 1v-1h5 1l1 1h0c-1 1-2 2-2 3-1 2 1 4 2 6l1 2s-1 0-1 1h-2l-16 4c-1 0-2-1-3-1l-2 1-6 2c1-1 1-3 1-5v-1c1-1 2-2 2-3 1-2 3-3 4-4z"></path><path d="M554 396c1-2 3-3 4-4 1 1 1 1 1 2-1 3-2 6-2 9l-6 2c1-1 1-3 1-5v-1c1-1 2-2 2-3z" class="N"></path><path d="M578 386h1l1 1h0c-1 1-2 2-2 3-1 2 1 4 2 6l1 2s-1 0-1 1h-2l-16 4c-1 0-2-1-3-1 5-3 10-4 16-5l1-2v-5-3l1-1h1z" class="V"></path><path d="M587 170l128 1 65-1h24c3 1 9 0 12 1 1 2 1 5 1 7v1c-2-1-3-1-5-1h-9-51-171c0-2 1-3 1-4s0-2 1-3l4-1z" class="J"></path><defs><linearGradient id="Cw" x1="737.186" y1="201.767" x2="672.596" y2="87.069" xlink:href="#B"><stop offset="0" stop-color="#505153"></stop><stop offset="1" stop-color="#74716e"></stop></linearGradient></defs><path fill="url(#Cw)" d="M841 142v1c-4 2-7 3-10 7h0-91-107-36-16c-3 0-5 1-8 0-2-2-2-4-3-6 7-1 15-1 22-1h45 130 48l26-1z"></path><defs><linearGradient id="Cx" x1="317.945" y1="161.489" x2="320.867" y2="130.967" xlink:href="#B"><stop offset="0" stop-color="#515051"></stop><stop offset="1" stop-color="#757473"></stop></linearGradient></defs><path fill="url(#Cx)" d="M405 150h-69-109-30c-2 0-8 1-10 0 0 0-3-3-3-4-3-1-6-3-8-4 16 2 33 0 50 1h156 48 27c1 1 2 1 4 2 0 1-1 4-2 5h-39c-5 0-11-1-15 0z"></path><defs><linearGradient id="Cy" x1="324.58" y1="156.728" x2="328.533" y2="191.373" xlink:href="#B"><stop offset="0" stop-color="#3e3d3c"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#Cy)" d="M199 177v-7l252 1c1 2 3 4 5 5 0 1 1 1 1 2h-47-95-79-24-10c-1 0-2-1-3-1z"></path><path d="M724 358c2 0 4 6 5 9 1 1 3 4 3 5l5 5 1 2v-1c1 1 4 5 5 6h2l1 1h0l2 1 1-1v-2c3 3 6 6 8 9v-12c1-4 3-7 6-9h0c0 1 0 2-1 2-1 4 1 8 1 12h-1v1l2 8 2 9v2c0 1 1 3 1 4 1 1 1 3 1 5l1 8v2l1 9c0 2 1 4 1 6 1 2 0 4 1 6-1 1-1 3-1 4-1 2-1 5-3 6l1 1c-1 1-2 4-2 5-1 1-3 6-2 7l-5 13c-2-1-2-1-3-2 1-3 2-7 2-10v-1c-1-1-1-1-1-3l-1-1c0-3 2-6 0-9v2c-2 3-2 7-5 9v1h-1c-1-5 0-9-1-14l-4-10c0-1-1-3-1-4-1-2-1-2 0-3l2 2 1-1 1-1c0-3-2-7-3-10-2-2-3-5-5-8s-9-9-8-13l1-2c1-2-5-14-6-17s-1-5 0-9h0v-1c-1-6-4-13-4-18z" class="U"></path><path d="M754 418v-1c-1-4-5-7-8-9h0 0c2-1 4 1 5 2s1 1 2 1v-2c2 4 3 9 4 14l-3-5zm-22-46l5 5 1 2c3 4 8 10 9 15-3-3-6-7-10-10-1-2-3-4-4-6l-1-6z" class="M"></path><path d="M724 358c2 0 4 6 5 9 1 1 3 4 3 5l1 6v-1c-1-1-1-1-1-2h-1c-1 1 0 2 0 4v-1c1 1 1 2 1 4v1c1 1 2 2 2 4h0v2c-2-3-3-6-4-9 0-2 0-3-1-5 0 1 0 2-1 2h0v-1c-1-6-4-13-4-18z" class="W"></path><defs><linearGradient id="Cz" x1="762.112" y1="441.643" x2="748.888" y2="431.357" xlink:href="#B"><stop offset="0" stop-color="#a2a19d"></stop><stop offset="1" stop-color="#c4c3c5"></stop></linearGradient></defs><path fill="url(#Cz)" d="M754 418l3 5v4c1 9 1 17 0 25l-2 1v-11-12c-1-3-1-5-1-8h0v-4z"></path><path d="M738 379v-1c1 1 4 5 5 6 2 3 4 7 7 10 1 1 2 3 3 5-1 3 1 5 2 8s2 5 2 8l1 6v6h-1v-4c-1-5-2-10-4-14-1-2-1-4-1-6-1-3-3-6-5-9-1-5-6-11-9-15z" class="H"></path><defs><linearGradient id="DA" x1="749.823" y1="462.313" x2="751.339" y2="448.705" xlink:href="#B"><stop offset="0" stop-color="#989595"></stop><stop offset="1" stop-color="#bfbebd"></stop></linearGradient></defs><path fill="url(#DA)" d="M746 443c2 1 2 2 3 3 1 2 2 2 4 2 1 0 0 0 1-1v-4l1-1v11l2-1v3 2c-2 3-2 7-5 9v1h-1c-1-5 0-9-1-14l-4-10z"></path><path d="M755 453l2-1v3 2c-2 3-2 7-5 9l3-13z" class="H"></path><path d="M757 415c1 1 2 3 3 4l1 4v6l1 4v8c-2 2 0 6-2 9v8c1 2 1 4 1 6v5h-2v-1c-1-1-1-1-1-3l-1-1c0-3 2-6 0-9v-3c1-8 1-16 0-25h1v-6l-1-6z" class="B"></path><path d="M760 458c1 2 1 4 1 6v5h-2v-1c0-3 0-7 1-10z" class="G"></path><path d="M757 415c1 1 2 3 3 4l1 4v6l1 4v8c-2 2 0 6-2 9l-2-23v-6l-1-6z" class="M"></path><path d="M757 415c1 1 2 3 3 4l1 4v6l-1-2c0-2-1-4-2-6l-1-6z" class="X"></path><defs><linearGradient id="DB" x1="764.652" y1="465.634" x2="765.436" y2="450.123" xlink:href="#B"><stop offset="0" stop-color="#aca9a8"></stop><stop offset="1" stop-color="#d0cfce"></stop></linearGradient></defs><path fill="url(#DB)" d="M762 441v4l2 1v1c0 2 1 3 2 5h1c1-1 2-3 2-5 1 1 1 2 1 3v-1h1c-1 2-1 5-3 6l1 1c-1 1-2 4-2 5-1 1-3 6-2 7l-5 13c-2-1-2-1-3-2 1-3 2-7 2-10h2v-5c0-2 0-4-1-6v-8c2-3 0-7 2-9z"></path><path d="M762 441v4c0 7 0 12-1 19h0c0-2 0-4-1-6v-8c2-3 0-7 2-9z" class="T"></path><defs><linearGradient id="DC" x1="758.876" y1="479.23" x2="762.707" y2="464.66" xlink:href="#B"><stop offset="0" stop-color="#757474"></stop><stop offset="1" stop-color="#a3a2a1"></stop></linearGradient></defs><path fill="url(#DC)" d="M761 464c1 1 0 2 1 3 2-1 3-5 3-7l1-1 1-1v-1c0-1 1-1 1-2l1 1c-1 1-2 4-2 5-1 1-3 6-2 7l-5 13c-2-1-2-1-3-2 1-3 2-7 2-10h2v-5h0z"></path><path d="M728 377c1 0 1-1 1-2 1 2 1 3 1 5 1 3 2 6 4 9l7 15h0l-1 1c0 3 2 5 4 6h0c0 1-1 1-1 1-2-2-4-5-6-7l-1 1c2 3 5 6 7 9 2 5 6 9 6 15-1-1-2-3-3-4-2-2-3-5-5-8s-9-9-8-13l1-2c1-2-5-14-6-17s-1-5 0-9z" class="d"></path><path d="M757 392v-12c1-4 3-7 6-9h0c0 1 0 2-1 2-1 4 1 8 1 12h-1v1l2 8 2 9v2c0 1 1 3 1 4 1 1 1 3 1 5l1 8v2l1 9c0 2 1 4 1 6 1 2 0 4 1 6-1 1-1 3-1 4h-1v1c0-1 0-2-1-3 0 2-1 4-2 5h-1c-1-2-2-3-2-5v-1l-2-1v-4-8l-1-4v-6l-1-4c-1-1-2-3-3-4 0-3-1-5-2-8s-3-5-2-8c-1-2-2-4-3-5-3-3-5-7-7-10h2l1 1h0l2 1 1-1v-2c3 3 6 6 8 9z" class="V"></path><path d="M761 419h1c0 4 2 11 0 14l-1-4v-6-4zm3-25l2 9v2c0 1 1 3 1 4 1 1 1 3 1 5l1 8v2c-2-3-1-4-1-6l-1-1v-2-5c-1-1-1-1-1-2 0-2 0-4-1-5v-2h0c0 1-1 2 0 3v3c0 1 0 3 1 4 0 3-1 7 0 10v3h0l-1-13c0-5-3-12-1-17z" class="a"></path><path d="M757 392v-12c1-4 3-7 6-9h0c0 1 0 2-1 2-1 4 1 8 1 12h-1v1l-1-2c-1 3 0 7-2 10-1 3 1 10 1 14 1 3 2 7 2 11h-1v4l-1-4c-1-1-2-3-3-4 0-3-1-5-2-8s-3-5-2-8c-1-2-2-4-3-5-3-3-5-7-7-10h2l1 1h0l2 1 1-1v-2c3 3 6 6 8 9z" class="U"></path><path d="M757 392v-12c1-4 3-7 6-9h0c0 1 0 2-1 2-1 4 1 8 1 12h-1l-1-5h0c-2 3 0 7-2 10v1 1h-1v-1h0v1 1h0c-1 0-1-1-1-1z" class="T"></path><path d="M743 384h2l1 1h0l2 1 1-1c1 3 2 5 4 7 1 2 3 5 4 7 2 3 2 6 3 10 0 3 1 6 1 10v4l-1-4c-1-1-2-3-3-4 0-3-1-5-2-8s-3-5-2-8c-1-2-2-4-3-5-3-3-5-7-7-10z" class="V"></path><path d="M753 399c4 5 6 13 7 20-1-1-2-3-3-4 0-3-1-5-2-8s-3-5-2-8z" class="W"></path><path d="M470 214l1-1 5 5h1v-2l1 1 2 2h2c2 2 5 9 6 12 1 1 2 3 3 4l2 6v4c1 2 2 7 1 9 3 16 3 32 3 48l1 44v26 16h-1c0 4-1 9 0 12l-1 1c-1-5-2-11-2-16l-3-12-2-6c-2-6-3-14-7-19-1-5-4-10-7-15l-12-22 7-7c1-1 2-3 2-4 4-5 8-11 10-17 2-4 3-8 4-13l2-9c0-10-2-21-7-31l-3-6c-1-1-2-3-3-4-2-2-3-4-5-6z" class="N"></path><path d="M491 247l2-2h0c1 2 2 7 1 9s-1 4-1 6c-1-4 0-8-2-13z" class="P"></path><path d="M491 259h0v3c-1 5 1 11-1 15l-1-1v-7c1-3 0-7 2-10z" class="U"></path><path d="M481 316c0 3 2 4 3 6 1 3 1 6 1 9-2-4-4-9-6-13l2-2z" class="S"></path><path d="M485 309c0 4 1 10-1 13-1-2-3-3-3-6l4-7z" class="M"></path><path d="M479 318c0-6 4-13 7-18l-1 9-4 7-2 2z" class="F"></path><path d="M470 214l1-1 5 5h1v-2l1 1 2 2h2c2 2 5 9 6 12 1 1 2 3 3 4l2 6v4h0l-2 2c-1-2-2-5-3-8-1-4-3-8-5-12-1-2-2-4-4-5l-1-1c-1-1-1-1-3-1-2-2-3-4-5-6z" class="Q"></path><path d="M486 270c2 3 1 7 1 9l-1 1c0 2 0 3-1 5h0c-1 4-4 8-6 12l-9 12c-1 1-2 2-2 3 9 16 19 32 23 51v1h0c1 2 0 3 0 4 0 2 2 1 1 4v1c-1-2-1-4-3-6-2-6-3-14-7-19-1-5-4-10-7-15l-12-22 7-7c1-1 2-3 2-4 4-5 8-11 10-17 2-4 3-8 4-13z" class="d"></path><path d="M486 270c2 3 1 7 1 9l-1 1c0 2 0 3-1 5v-3-2c1-1 1-2 1-3-1 2-1 4-4 6 2-4 3-8 4-13z" class="U"></path><path d="M494 254c3 16 3 32 3 48l1 44v26 16h-1c0 4-1 9 0 12l-1 1c-1-5-2-11-2-16 2-3 0-10 0-13s0-5 1-7l-2-105c0-2 0-4 1-6z" class="D"></path><path d="M495 365c0 3-1 8 1 10v1c1 1 1 3 1 5v7c0 4-1 9 0 12l-1 1c-1-5-2-11-2-16 2-3 0-10 0-13s0-5 1-7z" class="B"></path><path d="M145 327v-1c-1-1 0-1-1-2 0-1 0 0-1-1v-1l-1-5c-1 0 0-1-1-2v-3l-1-2-1-1v-2-1l4 12v-3c-1 0-1 0 0-1v1h1l-2-5v-1c1 1 1 0 1 2h0l1 3 1 5c4 9 9 18 15 25l2 3 4 5c0-1 1-2 1-3l2 2c2 1 3 1 4 3h0v-1h0c0-1-1-3-2-5 1 2 3 3 4 4l1 1 5 6c0-3 0-6 1-9h2l-1-1v-5c-1-2-3-2-3-4 1 1 11 11 11 12l2 2c4 4 8 8 12 11h1c-2-2-7-6-8-8-2-4-4-6-6-9-1-3-2-6-3-8-2-3-3-4-4-7l8 9c1 1 3 3 3 4l1 1c5 6 12 10 17 16l8 8 2 2v-2l2 1c1 0 2 1 3 1l3 2 1-1 2 2c2 1 3 2 4 4 4 2 8 5 11 8 1 2 3 4 5 5v3c-5 13-14 24-13 39l-1 1c-2 1-5 1-7 1-1-1-1 0-3 0 0-2-1-4-2-6l-3-9-2-4c-1-3-2-5-5-6h0l1-1c-1-2-1-2-3-3-4-3-8-7-11-10-1-2-3-2-4-3-3-1-5-4-8-6l-5-4c-7-4-15-11-21-18h1l3 3 1-1-1-1c-1-2-3-4-3-5v-1c-1-2-4-5-6-8h-2 0c-2-1-3-2-5-4l-3-3c-1-1 0-1-1-2h0v-1l-1-1c0-1-1-1-1-3l-1-1c-1-1-2-2-2-4v-1c-1-1-1-2-1-3-1-1-1-1-1-2s-1-1-2-2l1-1 1 1z" class="b"></path><path d="M190 382l6 4-2 3-5-4v-2h0c0-1 0 0 1-1h0z" class="H"></path><path d="M156 350l-1-4v-1-1l8 10h-2 0c-2-1-3-2-5-4zm74 41c2 2 4 3 5 6v1c-1 0-1 0-2-1h-1v1h-1v1l1 1v1c-2-1-4-2-5-4h2c1-1 1-1 1-3l-1-1 1-2z" class="F"></path><path d="M220 396l8 7s-2-1-2 0c1 1 2 2 2 4l-9-7 1-4z" class="Q"></path><path d="M228 403c3 1 5 4 8 6v2c0 1 1 0 1 2l-9-6c0-2-1-3-2-4 0-1 2 0 2 0z" class="F"></path><path d="M196 386l12 11h0l-3-2c0 1 1 1 1 3-1-2-3-2-4-3-3-1-5-4-8-6l2-3z" class="E"></path><path d="M211 389l9 7-1 4-9-7 2-1-1-3z" class="X"></path><path d="M153 347c-1-1 0-1-1-2h0v-1l-1-1c0-1-1-1-1-3l-1-1c-1-1-2-2-2-4v-1c-1-1-1-2-1-3-1-1-1-1-1-2s-1-1-2-2l1-1 1 1c1 1 9 14 10 17v1 1l1 4-3-3z" class="O"></path><path d="M233 374l2 2c2 1 3 2 4 4 4 2 8 5 11 8 1 2 3 4 5 5v3l-1-1c-5-3-8-7-12-11l-4-4c-2-1-3-2-4-3-1 0-1-1-2-2l1-1z" class="K"></path><path d="M228 379c2 2 5 3 8 4 1 1 2 3 4 4l1-1c1 1 2 1 2 2 4 3 6 5 7 9-2-2-5-5-8-7-2-2-4-3-7-5 0 2 3 4 3 4l-4-3c-1 0-2 1-2 2s0 2 1 3h1l2 2c-2 0-7-4-8-6h0l-1-3c0-1 2 0 3 0 0-2-2-3-2-5z" class="F"></path><path d="M169 362l21 20h0c-1 1-1 0-1 1h0v2c-7-4-15-11-21-18h1l3 3 1-1-1-1c-1-2-3-4-3-5v-1z" class="Q"></path><path d="M184 366l23 20 4 3 1 3-2 1-30-26c1 1 1 0 2 1h0l2-1v-1z" class="V"></path><path d="M171 348c1 2 3 3 4 4l1 1 5 6 4 4v2l1 1 6 5 10 9 6 5-1 1-23-20v1l-2 1h0c-1-1-1 0-2-1-5-4-10-9-14-15 0-1 1-2 1-3l2 2c2 1 3 1 4 3h0v-1h0c0-1-1-3-2-5z" class="C"></path><defs><linearGradient id="DD" x1="172.043" y1="349.472" x2="178.653" y2="369.126" xlink:href="#B"><stop offset="0" stop-color="#aeacae"></stop><stop offset="1" stop-color="#e1e0dc"></stop></linearGradient></defs><path fill="url(#DD)" d="M167 349l2 2c4 5 10 10 15 15v1l-2 1h0c-1-1-1 0-2-1-5-4-10-9-14-15 0-1 1-2 1-3z"></path><defs><linearGradient id="DE" x1="221.009" y1="388.466" x2="208.503" y2="376.546" xlink:href="#B"><stop offset="0" stop-color="#818080"></stop><stop offset="1" stop-color="#cccac9"></stop></linearGradient></defs><path fill="url(#DE)" d="M202 380h0l1-1c2-1 2 0 4 0l1-1h1c0-1-1-2-1-2l-1-1 1-1h3c3-1 5 5 8 5 2 1 4 3 6 4v1l-3-2h-1l5 5c1 2 3 3 4 4l-1 2 1 1c0 2 0 2-1 3h-2l-3-2c-1-1-3-2-5-2-1-1-2-2-4-2-2-2-5-4-7-6l-6-5z"></path><path d="M211 379h1l-2 2v1h0l-2-1 1-1h1l1-1z" class="N"></path><defs><linearGradient id="DF" x1="229.055" y1="392.89" x2="220.072" y2="389.792" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#DF)" d="M224 395c0-1-1-1-1-2-1-1-1-2-2-2l-1-1c1 0 1-1 1-2 0 0-1-1-1-2h-1v-1c4-1 4 1 6 2h1c1 2 3 3 4 4l-1 2 1 1c0 2 0 2-1 3h-2l-3-2z"></path><path d="M185 333l8 9c1 1 3 3 3 4l1 1c5 6 12 10 17 16l8 8 2 2 17 13-1 1c-2-1-3-3-4-4-3-1-6-2-8-4 0 2 2 3 2 5-1 0-3-1-3 0l1 3h0l-3-3v-1c-2-1-4-3-6-4l-14-14h1c-2-2-7-6-8-8-2-4-4-6-6-9-1-3-2-6-3-8-2-3-3-4-4-7z" class="Q"></path><path d="M221 379v-1c-2-2-3-4-5-6 1 0 3 1 4 1 1 1 3 3 4 3 0 2 0 2 1 3-1 1-2 0-3 0h-1z" class="Y"></path><path d="M204 359h0c2 1 3 1 4 2 2 1 3 2 5 2h1l8 8c-2 0-5-1-7-2-4-3-8-6-11-10z" class="a"></path><path d="M206 365c5 4 9 11 15 14h1c1 0 2 1 3 0-1-1-1-1-1-3 1 1 2 2 4 3 0 2 2 3 2 5-1 0-3-1-3 0l1 3h0l-3-3v-1c-2-1-4-3-6-4l-14-14h1z" class="S"></path><path d="M215 369c2 1 5 2 7 2l2 2 17 13-1 1c-2-1-3-3-4-4-3-1-6-2-8-4-2-1-3-2-4-3-1 0-3-2-4-3l-5-4z" class="M"></path><path d="M196 346l1 1c5 6 12 10 17 16h-1c-2 0-3-1-5-2-1-1-2-1-4-2h0c-4-4-6-7-8-12v-1z" class="g"></path><path d="M180 340c1 1 11 11 11 12l2 2c4 4 8 8 12 11l14 14c-3 0-5-6-8-5h-3l-1 1 1 1s1 1 1 2h-1l-1 1c-2 0-2-1-4 0l-1 1h0l-10-9-6-5-1-1v-2l-4-4c0-3 0-6 1-9h2l-1-1v-5c-1-2-3-2-3-4z" class="a"></path><path d="M180 340c1 1 11 11 11 12l-1 1c2 2 3 4 4 6v1c-2 0-2-1-3-1-1-1 0-1-1-1l-2-2 1 1v2l-2 1 1 2c1 1 1 2 2 3l-1 1 3 3v2l-6-5-1-1v-2l-4-4c0-3 0-6 1-9h2l-1-1v-5c-1-2-3-2-3-4z" class="M"></path><path d="M183 344c2 3 3 6 5 10-1 0-2 0-3-1 0 2-1 4-1 5 1 2 2 3 3 4h-1c0 3 2 1 0 4l-1-1v-2l-4-4c0-3 0-6 1-9h2l-1-1v-5zm319-127l28-1c-1 2-4 5-5 7v5 33 102 75l-2 1c-1 2-1 2 0 4 1 1 1 1 3 0l1-1v-1c5-5 6-14 9-20l1 1c-2 2-2 5-3 8-1 5-2 9-1 14 1 1 1 2 2 2 3 0 4-1 6-2 2-2 4-5 5-7l1 1c-1 3-4 5-5 8-4 4-7 8-10 12-2 5-3 10-7 14l1-3c-3 2-3 4-4 7 0 1-1 1-1 2v5l-1 6c0 4 1 8 0 11-2 0-1 0-2-1 0-1 2-7 1-8-1 1 0 3 0 4-1 2-1 0-1 2v1-1c-1-2-1-3-1-5h-1v-2c-1-2 0-3-1-5 0-2 0-5-1-8 0-1 1-3 0-5-1-1-1-3-1-5 0-3-1-8-3-11l-1-2v-6c1 0 1-1 1-2s0-1 1-2v-3c-1-1-2-2-3-2l1-10v-54c-1-5 0-11-1-16l1-37c-1-2-1-5-1-7v-14l1-49c0-9 0-17-1-25 0-5-2-7-6-10z" class="T"></path><path d="M528 453v-2c1-1 1-4 1-5v-2h1v8h0v2h0c-1 0-1 0-1-1h-1z" class="G"></path><path d="M509 429h0v-15l1 2h2v-1c0 4 1 22-1 26-1-1-2-2-3-2l1-10z" class="g"></path><path d="M509 322c1 4 0 10 0 15v5c0 1 1 2 2 3 0 1 0 2 1 3v1c0 1 1 2 1 3v1c0 2 0 3 1 5 1 4-1 9-1 14h0l-1 2-2 2-1 13c-1-4 0-10 0-14-1-5 0-11-1-16l1-37z" class="a"></path><path d="M527 441c5-5 6-14 9-20l1 1c-2 2-2 5-3 8-1 5-2 9-1 14 1 1 1 2 2 2 3 0 4-1 6-2 2-2 4-5 5-7l1 1c-1 3-4 5-5 8-4 4-7 8-10 12-2 5-3 10-7 14l1-3c-3 2-3 4-4 7 0 1-1 1-1 2v5l-1 6s-1-11 0-12c0-2 1-3 0-5h0c1-1 1-1 1-2v-1c1-2 0-2 0-4 1-1 1-1 1-2v-2l1-1v-1-1h0v-1l1-1c0-1 0-2 1-2l1-1 1-1 1 1h1c0 1 0 1 1 1h0v-2h0c1-1 1-2 1-3v-2c1-2 0 0 0-1l1-1c0-1 0 0 1-1l-1-3v-6c1-1 2-3 1-5l-3 9-3 6h-3 2c1-1 1-1 1-3v-1z" class="Z"></path><path d="M521 478v-2c1-6 1-19 6-22h1c2 3-1 12-2 15-3 2-3 4-4 7 0 1-1 1-1 2z" class="L"></path><path d="M738 209c-1-1-1-4-2-6 15-1 30 0 45 0 11 0 23-1 35 1 2 0 3 1 5 2 1 1 1 1 2 1 1 1 2 0 3-1h0l2-2 18 6c1 1 3 2 5 3 5 2 10 5 16 9 2 3 5 5 8 8h0v3l2 4c0 1 0 3-1 4l5 6v2c-2-3-5-5-7-7v1c1 3 5 5 7 8l4 5c-1 1-2 3-4 5-2-3-4-6-7-9-1 0-1-1-2-1l-9-6c-14-9-33-12-50-10-24 3-49 14-65 33-18 23-26 53-36 80l-35 94-112 309-22 60c0 1-1 1-1 2s-1 2-1 3c-1 1 0 0-1 2v1l-1 1v1l-1 3s0 1-1 1v1 1h0c0 2-1 3-2 4v1h0c0 1 0 1-1 2v3l-1 1s-1 1-1 2 0-1 0 1v1c-1 3-3 7-4 11l-1 1v1 1h0c-1 1-1 1-1 2-1 1-1 3-2 4v1 1l-1 3-1 1c0 1 0 1-1 1h-1l-6 2-4-10-9-23c-2-4-3-9-5-13h-1c-3-6-5-12-7-18l-12-32-33-83-112-285-29-73-16-38c-3-8-7-15-11-22-14-20-35-32-59-37-16-3-34-4-49 2-6 3-12 6-18 10-1 0-1 0-2 1-1 0-2 2-4 3-2 2-3 4-5 6-3-1-3-2-4-5l9-9c1 0 1-1 2-2v-1c-5 4-10 10-14 15 1-7 3-13 6-19l2 2c8-10 15-18 27-24 7-4 15-6 23-8 3-1 7-3 10-3l-1 1 1 1c3 0 5-2 8-3 5-1 12-1 17-1h17 37c0 2 0 2-1 3l1 1-15 35c6 4 11 9 16 14s8 12 13 15v1c0 3 1 5 2 7l7 15 14 38c2 3 3 10 6 12h2l-1 1h0l-1 1c1 6 5 13 7 18 6-1 11-1 16 0-3 0-6 1-10 2-1 0-3 1-5 1l13 33c2 4 3 8 5 12l1 3c2 5 4 11 7 16 3 0 5-1 8 0l-8 1 9 23c1 4 3 12 6 14 2 2 3 2 5 3l-3 1c-1 3 7 19 8 23 1 2 2 5 3 7 1 1 2 2 2 4 2 2 2 5 3 8h6 2c2-1 4-1 6-1h3c1-1 2-1 3-1 1-1 2 0 4 0v-1l2 1h0 5l5-1c2-1 4-1 6-1l20-3c1 3 2 5 3 8 0 1-1 2 0 2 3 4 4 8 5 12l7 19 19 52 8 22c0-1 0-5 1-5v1h1l12 33h-1c-1-1-1-1-1-2 0 1-1 2 0 3 0 1 0 2 1 3h-1l1 4 1 3c3 4 4 10 6 15 5 14 10 29 17 42l13-34 8 2-8-4c2-5 12-36 14-38l4 1c2 0 2 0 3 1l2 1h1c1 0 0 0 1 1 2 0 3 1 4 1 2 0 2 1 4 1l1 1 1 1 9 2 5-14c1-2 2-4 2-6-1 0-2-1-2-1l2-1c2-2 10-24 11-29 1-1 1-2 1-3l3-6c-1-1 2-6 2-8l8-23c4-9 8-18 11-28 3-8 7-17 9-25 1-2 2-7 3-9l27-74c1-4 4-8 4-12v-1c3-3 4-7 5-11l6-15 25-69c5-14 9-29 16-42 7-12 16-26 27-34h1l-13-30c-1-1-1-2-2-4v-1l1-1z" class="g"></path><defs><linearGradient id="DG" x1="395.398" y1="529.052" x2="387.697" y2="531.276" xlink:href="#B"><stop offset="0" stop-color="#747271"></stop><stop offset="1" stop-color="#99999a"></stop></linearGradient></defs><path fill="url(#DG)" d="M376 492l2 1h0c1 2 0 1 1 2l1 1c1 2 2 5 3 7 1 1 2 2 2 4 2 2 2 5 3 8h6c-2 2-3 2-6 2l20 51h-1-1-1c-1-1-7-18-8-20l-15-40-6-16z"></path><path d="M139 240c8-10 15-18 27-24l-1 1c0 1-1 1-2 2v2c1 1 1 2 2 3l1 1c2-1 4-2 7-1l6-1h1l-4 1c1 1 1 1 2 1s2 0 3-1l1 1h0c-4 1-7 2-11 3-9 2-19 7-26 14-5 4-10 10-14 15 1-7 3-13 6-19l2 2z" class="M"></path><defs><linearGradient id="DH" x1="148.622" y1="230.016" x2="165.648" y2="241.331" xlink:href="#B"><stop offset="0" stop-color="#323233"></stop><stop offset="1" stop-color="#706e6c"></stop></linearGradient></defs><path fill="url(#DH)" d="M173 224l6-1h1l-4 1c1 1 1 1 2 1s2 0 3-1l1 1h0c-4 1-7 2-11 3-9 2-19 7-26 14-5 4-10 10-14 15 1-7 3-13 6-19l2 2-4 8c3-2 6-6 8-8 9-8 19-13 30-16z"></path><path d="M464 717h0l1 1v-1l1-1v1c1 1 1 3 3 4h2c0 1 1 1 1 1v2l-2 1c1 2 2 7 4 9 1 1 2 1 3 1s1 0 1 1h-3v1l5 13v1l1 3 4 9c1 4 10 28 11 29h2c1 1 1 1 0 2l-1 1 21 53 4-14s1-2 1-3h1l2-5 1 1c-1 2-2 4-2 7 0 0-1 4-2 5l-6 16c-2-3-3-9-5-13l-12-31-23-60-13-34z" class="D"></path><path d="M405 568h1 1 1 4l1 1v1l-1 1h2c-1 1-2 2-4 2 0 2 1 4 2 5l3 8 7 18c1 2 3 5 3 6l5 14 1 2 11 27c1 5 3 10 5 14 1 1 1 3 2 4h1 5 0 0c1 1 2 2 3 2 1 2 2 8 2 11v3h0l-2 1h7c-3 1-6 1-9 1l10 25 12 1c-4 1-8 1-12 2v-1l-1 1v1l-1-1h0c-2-2-3-8-4-10l-20-51-4-10-13-33c-1-1-2-4-3-6-2-6-5-12-7-19-1-2-2-4-3-7l-5-13z" class="F"></path><path d="M436 646c1 1 2 1 3 3 0 1 1 3 1 4h0v3l-4-10z" class="E"></path><path d="M455 671h0c1 1 2 2 3 2 1 2 2 8 2 11v3h0l-2 1c-1 0-2 0-2-1-1 0-2-2-2-3-2-4-4-8-5-12 2 0 4 0 6-1z" class="X"></path><path d="M405 568h1 1 1 4l1 1v1l-1 1h2c-1 1-2 2-4 2 0 2 1 4 2 5l3 8 7 18c1 2 3 5 3 6l-1 2 1 1c1 0 1 2 1 3s0 1 1 2v1h1l-1 1c1 1 2 1 1 3l1 1c1 2 2 3 3 5v2c0 1 0 1 1 1 0 1 0 2 1 3v1 1c1 1 2 2 1 3l1 2h-1 0v-1l-3-8h-1v-2c-1-1-1-2-2-3 0-1 0-2-1-3l-5-12h0c-1-1-2-4-3-6-2-6-5-12-7-19-1-2-2-4-3-7l-5-13z" class="E"></path><path d="M405 568h1 1 1 4l1 1v1l-1 1h2c-1 1-2 2-4 2 0 2 1 4 2 5l3 8h0c-1-1-1-2-2-3-1-2-1-4-3-6v-2c0-1-1-2-2-2l1 2c0 1 1 3 1 4 1 3 4 6 3 9-1-2-2-4-3-7l-5-13z" class="S"></path><path d="M408 568h4l1 1h-3c-1 1 0 2-1 3l-1-1v-3z" class="E"></path><defs><linearGradient id="DI" x1="541.81" y1="715.96" x2="587.19" y2="722.54" xlink:href="#B"><stop offset="0" stop-color="#2a2629"></stop><stop offset="1" stop-color="#656a65"></stop></linearGradient></defs><path fill="url(#DI)" d="M608 596v1c0 2-1 4-2 6l-3 8v2h0c1 0 1-1 1-1 0-1 1-2 1-3l1-1v-2c0-1 0 0 1-1v-1-1l1-1v-1c1-1 1-1 1-2v-1h1l-54 149-20 57-8 22c-1 2-2 5-3 8 0-3 1-5 2-7l-1-1-2 5h-1c1-5 4-11 6-16l12-35 9-23c0-2 2-7 2-8s-1-1-1-3c2 0 2-1 3-2 3-5 4-11 6-17l25-68-8-5 9 2 5-14c1-2 2-4 2-6-1 0-2-1-2-1l2-1c2-2 10-24 11-29 1-1 1-2 1-3l3-6z"></path><defs><linearGradient id="DJ" x1="877.417" y1="225.258" x2="827.663" y2="221.676" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#acabaa"></stop></linearGradient></defs><path fill="url(#DJ)" d="M828 204l18 6c1 1 3 2 5 3 5 2 10 5 16 9 2 3 5 5 8 8h0v3l2 4c0 1 0 3-1 4l5 6v2c-2-3-5-5-7-7h0c-11-10-26-15-41-17l-20-1c1 0 1-1 3 0l1-2-2-1c-2 1-3 1-5 1h0v-1h3l1-1h5 5 1 2l1 1c1-2 0-3 0-4s1-1 1-2v-3-4c-1-1-2-1-3-2h0l2-2z"></path><path d="M828 204l18 6c1 1 3 2 5 3l-2 1-15-6c-3-1-5-2-8-2l2-2zm-13 17c7 0 14 0 21 1h0v1h2l-1 1h-2l-2 1-20-1c1 0 1-1 3 0l1-2-2-1z" class="B"></path><path d="M851 213c5 2 10 5 16 9 2 3 5 5 8 8h0v3c-7-8-16-14-26-19l2-1z" class="F"></path><defs><linearGradient id="DK" x1="859.4" y1="245.368" x2="851.91" y2="219.476" xlink:href="#B"><stop offset="0" stop-color="#22201e"></stop><stop offset="1" stop-color="#5d5c5d"></stop></linearGradient></defs><path fill="url(#DK)" d="M836 222c15 3 30 9 40 19l5 6v2c-2-3-5-5-7-7h0c-11-10-26-15-41-17l2-1h2l1-1h-2v-1z"></path><defs><linearGradient id="DL" x1="276.323" y1="363.737" x2="326.198" y2="337.232" xlink:href="#B"><stop offset="0" stop-color="#91908f"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#DL)" d="M189 221h14c22 2 40 9 58 20l16-34v-1l1 1-15 35c6 4 11 9 16 14s8 12 13 15v1c0 3 1 5 2 7l7 15 14 38c2 3 3 10 6 12h2l-1 1h0l-1 1c1 6 5 13 7 18 6-1 11-1 16 0-3 0-6 1-10 2-1 0-3 1-5 1l13 33c2 4 3 8 5 12l1 3c2 5 4 11 7 16 3 0 5-1 8 0l-8 1 9 23c1 4 3 12 6 14 2 2 3 2 5 3l-3 1c-1 3 7 19 8 23l-1-1c-1-1 0 0-1-2h0l-2-1c-3-6-5-13-8-19l-11-29-37-93-16-40c-7-18-13-37-27-52-15-18-37-29-59-33-7-1-13-2-20-2l-16 1h0l-1-1c-1 1-2 1-3 1s-1 0-2-1l4-1h-1c1-1 8-2 10-2z"></path><path d="M179 223c1-1 8-2 10-2 3 1 6 1 8 1s3 0 5 1h-4v1l-16 1h0l-1-1c-1 1-2 1-3 1s-1 0-2-1l4-1h-1z" class="c"></path><defs><linearGradient id="DM" x1="725.608" y1="424.614" x2="670.424" y2="399.858" xlink:href="#B"><stop offset="0" stop-color="#646363"></stop><stop offset="1" stop-color="#bdbbba"></stop></linearGradient></defs><path fill="url(#DM)" d="M738 209c1 1 1 2 1 3 1 1 2 2 2 3s1 2 1 3l11 26c3-2 5-4 8-6 6-2 12-6 19-8 9-4 20-7 30-8 2 0 3 0 5-1l2 1-1 2c-2-1-2 0-3 0-29 4-57 16-76 40-10 14-16 29-22 46l-13 34-22 61-53 145c-11 33-17 48-17 48h-1v1c0 1 0 1-1 2v1l-1 1v1 1c-1 1-1 0-1 1v2l-1 1c0 1-1 2-1 3 0 0 0 1-1 1h0v-2l3-8c1-2 2-4 2-6v-1c-1-1 2-6 2-8l8-23c4-9 8-18 11-28 3-8 7-17 9-25 1-2 2-7 3-9l27-74c1-4 4-8 4-12v-1c3-3 4-7 5-11l6-15 25-69c5-14 9-29 16-42 7-12 16-26 27-34h1l-13-30c-1-1-1-2-2-4v-1l1-1z"></path><defs><linearGradient id="DN" x1="828.539" y1="207.716" x2="740.884" y2="223.634" xlink:href="#B"><stop offset="0" stop-color="#b0afad"></stop><stop offset="1" stop-color="#e7e6e4"></stop></linearGradient></defs><path fill="url(#DN)" d="M738 209c-1-1-1-4-2-6 15-1 30 0 45 0 11 0 23-1 35 1 2 0 3 1 5 2 1 1 1 1 2 1 1 1 2 0 3-1 1 1 2 1 3 2v4 3c0 1-1 1-1 2s1 2 0 4l-1-1h-2-1-5-5l-1 1h-3v1h0c-10 1-21 4-30 8-7 2-13 6-19 8-3 2-5 4-8 6l-11-26c0-1-1-2-1-3s-1-2-2-3c0-1 0-2-1-3z"></path><path d="M485 763h1v2l2 3v2l1 1v1l1 1c0 2 0 3 1 4v1h0l1 2c0 1 2 6 3 8l2 1c1 0 3-1 4-1v-1c-1-1-1-2-1-3v-1h0v-1h0c-1-2-2-4-2-6l-1-1c0-1 0-1-1-2l-4-11 5-1s1 1 2 1h0c2-1 4 1 6 1h1c1 0 1 0 2 1 4 1 7 2 10 2 2 1 3 2 4 2 6 3 12 6 18 8l2 1h-1v3l-12 35c-2 5-5 11-6 16 0 1-1 3-1 3l-4 14-21-53 1-1c1-1 1-1 0-2h-2c-1-1-10-25-11-29z" class="Q"></path><path d="M517 823h2l1 1c0 1-1 2-1 4h-1c-1-1-2-2-2-3l1-2z" class="O"></path><path d="M498 792c4-1 8-1 11-1 5 0 11-1 16 0l-27 3c1-1 1-1 0-2z" class="E"></path><defs><linearGradient id="DO" x1="162.534" y1="222.52" x2="277.276" y2="220.614" xlink:href="#B"><stop offset="0" stop-color="#c1bfbe"></stop><stop offset="1" stop-color="#f6f6f3"></stop></linearGradient></defs><path fill="url(#DO)" d="M241 203h37c0 2 0 2-1 3v1l-16 34c-18-11-36-18-58-20h-14c-2 0-9 1-10 2l-6 1c-3-1-5 0-7 1l-1-1c-1-1-1-2-2-3v-2c1-1 2-1 2-2l1-1c7-4 15-6 23-8 3-1 7-3 10-3l-1 1 1 1c3 0 5-2 8-3 5-1 12-1 17-1h17z"></path><defs><linearGradient id="DP" x1="481.417" y1="783.549" x2="529.228" y2="637.885" xlink:href="#B"><stop offset="0" stop-color="#8c8b8a"></stop><stop offset="1" stop-color="#c8c7c4"></stop></linearGradient></defs><path fill="url(#DP)" d="M506 663c3 4 4 10 6 15 5 14 10 29 17 42l13-34 8 2-8-4c2-5 12-36 14-38l4 1c2 0 2 0 3 1l2 1h1c1 0 0 0 1 1 2 0 3 1 4 1 2 0 2 1 4 1l1 1 1 1 8 5-25 68c-2 6-3 12-6 17-1 1-1 2-3 2 0 2 1 2 1 3s-2 6-2 8l-9 23v-3h1l-2-1c-6-2-12-5-18-8-1 0-2-1-4-2-3 0-6-1-10-2-1-1-1-1-2-1h-1c-2 0-4-2-6-1h0c-1 0-2-1-2-1l-5 1 4 11c1 1 1 1 1 2l1 1c0 2 1 4 2 6h0v1h0v1c0 1 0 2 1 3v1c-1 0-3 1-4 1l-2-1c-1-2-3-7-3-8l-1-2h0v-1c-1-1-1-2-1-4l-1-1v-1l-1-1v-2l-2-3v-2h-1l-4-9-1-3v-1l-5-13v-1h3c0-1 0-1-1-1s-2 0-3-1c-2-2-3-7-4-9l2-1v-2s-1 0-1-1h-2c-2-1-2-3-3-4 4-1 8-1 12-2l-12-1-10-25c3 0 6 0 9-1h-7l2-1h0v-3c0-3-1-9-2-11-1 0-2-1-3-2h0 0c1-1 1-1 1-2 4 0 10-1 14-2l28-5 6-1c1 0 1 0 1-1l1 3z"></path><path d="M501 751c3-1 6-1 8 0h0-3c-2 1-3 1-5 0h0z" class="Q"></path><path d="M501 751h0l-13 2c-2 0-5 0-7 1l-1-3h21z" class="F"></path><path d="M474 734c6-1 13-1 19 0l-10 1c-1 0-3 1-5 1 0-1 0-1-1-1s-2 0-3-1z" class="B"></path><path d="M518 731h0 6v1c-3 1-6 1-9 1h-9c4-1 8-2 12-2z" class="P"></path><path d="M505 660l1 3-51 8h0 0c1-1 1-1 1-2 4 0 10-1 14-2l28-5 6-1c1 0 1 0 1-1z" class="G"></path><defs><linearGradient id="DQ" x1="415.324" y1="648.517" x2="474.813" y2="521.047" xlink:href="#B"><stop offset="0" stop-color="#b6b4b2"></stop><stop offset="1" stop-color="#e9e9e6"></stop></linearGradient></defs><path fill="url(#DQ)" d="M414 513h0 5l5-1c2-1 4-1 6-1l20-3 3 8c0 1-1 2 0 2 3 4 4 8 5 12l7 19 19 52 8 22c0-1 0-5 1-5v1h1l12 33h-1c-1-1-1-1-1-2 0 1-1 2 0 3 0 1 0 2 1 3h-1l1 4c0 1 0 1-1 1l-6 1-28 5c-4 1-10 2-14 2 0 1 0 1-1 2h-5-1c-1-1-1-3-2-4-2-4-4-9-5-14l-11-27-1-2-5-14c0-1-2-4-3-6l-7-18-3-8c-1-1-2-3-2-5 2 0 3-1 4-2h-2l1-1v-1l-1-1h-4l-20-51c3 0 4 0 6-2h2c2-1 4-1 6-1h3c1-1 2-1 3-1 1-1 2 0 4 0v-1l2 1z"></path><path d="M430 624c3 0 10 0 13 1h-3l-8 1h-1l-1-2z" class="B"></path><path d="M431 626h1l8-1v3c1 4 3 8 5 12l7 18c1 4 2 8 3 11h-3c0-1-1-1-2-1h-1l-1-1h-1c-2-4-4-9-5-14l-11-27z" class="T"></path><path d="M498 662l-6-15c-1-2-4-7-4-10l3-1v-1c-2 0-2 1-4 1l-11-29c2-1 6-1 7-3-1-1-1 0-2 0l1-1 2-2h0l8 22c0-1 0-5 1-5v1h1l12 33h-1c-1-1-1-1-1-2 0 1-1 2 0 3 0 1 0 2 1 3h-1l1 4c0 1 0 1-1 1l-6 1z" class="U"></path><path d="M492 623c0-1 0-5 1-5v1h1l12 33h-1c-1-1-1-1-1-2 0 1-1 2 0 3 0 1 0 2 1 3h-1l-12-33z" class="J"></path><path d="M658 313c5-13 9-32 8-46-1-12-5-22-14-30-17-14-44-13-65-11l14-34h145 44c9 0 18-1 27 0 17 3 37 9 51 20v1c2 3 6 6 9 9l6 9c1 0 1 0 1 1l2 4v1c1 0 1 1 1 2-1 1-2 3-4 4l-2-5c-1-2-2-3-3-5-2-3-7-10-11-11h0c-6-4-11-7-16-9-2-1-4-2-5-3l-18-6-2 2h0c-1 1-2 2-3 1-1 0-1 0-2-1-2-1-3-2-5-2-12-2-24-1-35-1-15 0-30-1-45 0 1 2 1 5 2 6l-1 1v1c1 2 1 3 2 4l13 30h-1c-11 8-20 22-27 34-7 13-11 28-16 42l-25 69-6 15c-1 4-2 8-5 11v1c0 4-3 8-4 12l-27 74c-1 2-2 7-3 9-2 8-6 17-9 25-3 10-7 19-11 28l-8 23c0 2-3 7-2 8l-3 6c0 1 0 2-1 3-1 5-9 27-11 29l-2 1s1 1 2 1c0 2-1 4-2 6l-5 14-9-2-1-1-1-1c-2 0-2-1-4-1-1 0-2-1-4-1-1-1 0-1-1-1h-1l-2-1c-1-1-1-1-3-1l-4-1c-2 2-12 33-14 38l8 4-8-2-13 34c-7-13-12-28-17-42-2-5-3-11-6-15l-1-3-1-4h1c-1-1-1-2-1-3-1-1 0-2 0-3 0 1 0 1 1 2h1l-12-33h-1v-1c-1 0-1 4-1 5l-8-22-19-52-7-19c-1-4-2-8-5-12-1 0 0-1 0-2-1-3-2-5-3-8l-20 3c-2 0-4 0-6 1l-5 1h-5 0l-2-1v1c-2 0-3-1-4 0-1 0-2 0-3 1h-3c-2 0-4 0-6 1h-2-6c-1-3-1-6-3-8 0-2-1-3-2-4-1-2-2-5-3-7-1-4-9-20-8-23l3-1c-2-1-3-1-5-3-3-2-5-10-6-14-3-7-6-15-9-23l8-1c-3-1-5 0-8 0-3-5-5-11-7-16l-1-3c-2-4-3-8-5-12l-13-33c2 0 4-1 5-1 4-1 7-2 10-2-5-1-10-1-16 0-2-5-6-12-7-18l1-1h0l1-1h-2c-3-2-4-9-6-12l-14-38-7-15c-1-2-2-4-2-7v-1c-5-3-8-10-13-15s-10-10-16-14l15-35-1-1c1-1 1-1 1-3h-37-17c-5 0-12 0-17 1-3 1-5 3-8 3l-1-1 1-1c-3 0-7 2-10 3-8 2-16 4-23 8-12 6-19 14-27 24l-2-2c2-3 5-7 7-10 0 0-1 0-2 1-3 3-6 8-7 13-1 1-2 2-2 3v1c-1-2-2-2-4-3h0c-1-1 0-2 0-3 2-4 4-7 6-11 1-2 4-4 5-7v-1c15-17 41-27 64-29l231-1c1 4 14 34 14 34-20-1-44-2-60 13-9 8-12 20-12 33 0 4 0 9 1 13 4 14 11 27 16 41l29 74 1 2 104 284 128-368 2-4z" class="d"></path><path d="M213 200h15 23l-1 2h-9v1h-17c1 0 2-1 3-1s1 0 2-1h0l-16-1z" class="Z"></path><path d="M297 200h53l-1 3h-20c-3 0-7-1-9 0h-6-13c-1-4-2-1-4-3z" class="N"></path><path d="M251 200h46c2 2 3-1 4 3h-4c-3-1-16-1-17 1l-2 3-1-1c1-1 1-1 1-3h-37v-1h9l1-2zm360 0c4-1 8 0 11 0h21 49l-2 2c-4 0-15-1-18 1v1c-3 2-5 5-7 7h-1l7-8-44-1c-4-3-12 1-16-2z" class="M"></path><path d="M605 213c2 0 3-1 4-2h-1v-1c3 1 8 1 12 1 11 2 23 5 33 12 1 0 2 0 3 1l6 4 2 3 8 11h-1-1v1c-1-1-2-3-3-3-1-3-2-5-4-7-15-15-37-19-58-20z" class="N"></path><defs><linearGradient id="DR" x1="144.968" y1="242.77" x2="221.711" y2="190.332" xlink:href="#B"><stop offset="0" stop-color="#4f4e4d"></stop><stop offset="1" stop-color="#989696"></stop></linearGradient></defs><path fill="url(#DR)" d="M144 228c15-13 33-21 51-25l18-3 16 1h0c-1 1-1 1-2 1s-2 1-3 1c-5 0-12 0-17 1-3 1-5 3-8 3l-1-1 1-1c-3 0-7 2-10 3-8 2-16 4-23 8-12 6-19 14-27 24l-2-2c2-3 5-7 7-10z"></path><defs><linearGradient id="DS" x1="764.089" y1="218.13" x2="757.889" y2="187.762" xlink:href="#B"><stop offset="0" stop-color="#666665"></stop><stop offset="1" stop-color="#aca8a8"></stop></linearGradient></defs><path fill="url(#DS)" d="M696 200h73 25c6 0 13 0 19 1 5 0 10 2 15 3l-2 2h0c-1 1-2 2-3 1-1 0-1 0-2-1-2-1-3-2-5-2-12-2-24-1-35-1-15 0-30-1-45 0 1 2 1 5 2 6l-1 1c-1-2-2-5-3-7h-13c-3 0-5-1-8 0-3 6 1 9 2 15-1-1 0-1-1-2l-5-13h-14l1-3z"></path><defs><linearGradient id="DT" x1="505.043" y1="556.97" x2="426.711" y2="539.867" xlink:href="#B"><stop offset="0" stop-color="#554e4b"></stop><stop offset="1" stop-color="#a4a8ab"></stop></linearGradient></defs><path fill="url(#DT)" d="M434 465h1l2 7c1 2 2 5 3 7 1-1 1-1 1-2-1-1-1-2-1-3l-1-1h0v-1-1c-1-1-1-1-1-2h0v-1-2l56 153h-1v-1c-1 0-1 4-1 5l-8-22-19-52-7-19c-1-4-2-8-5-12-1 0 0-1 0-2-1-3-2-5-3-8l-20 3c-2 0-4 0-6 1l-5 1h-5 0l8-1c2-1 4-1 6-1l21-4c0-2-1-5-2-8l-13-34z"></path><path d="M605 213h-2c3-4 5-8 7-13h1c4 3 12-1 16 2l44 1-7 8-11 12c-10-7-22-10-33-12-4 0-9 0-12-1v1h1c-1 1-2 2-4 2z" class="d"></path><path d="M605 213h-2c3-4 5-8 7-13h1c4 3 12-1 16 2h-15l-4 8v1h1c-1 1-2 2-4 2z" class="X"></path><defs><linearGradient id="DU" x1="543.807" y1="718.268" x2="562.202" y2="488.947" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#8a8988"></stop></linearGradient></defs><path fill="url(#DU)" d="M609 484c0 3-1 6-2 8l1 1c0-1 1-2 1-2h0l-1 6c-1 1-1 2-1 3-4 7-6 16-9 24l-10 29 8 5v-1c-3 0-6-1-9-2l-12 35 8 5h0l-9-3c-1 4-4 9-4 12l5 3c-4 1-6 1-8 5-1 2-3 7-2 10v1c-1 1-1 1-2 1-1 2-2 4-3 7l-4 14 4 2-4-1c-2 2-12 33-14 38l8 4-8-2-13 34c-7-13-12-28-17-42-2-5-3-11-6-15l-1-3-1-4h1c-1-1-1-2-1-3-1-1 0-2 0-3 0 1 0 1 1 2h1l22 62 81-230z"></path><path d="M353 200h74c2 5 4 9 6 14-17-2-35 3-49 14-11 9-17 23-18 37 0 7 0 14 2 21s5 14 8 21l11 28 41 105 10 26v2 1h0c0 1 0 1 1 2v1 1h0l1 1c0 1 0 2 1 3 0 1 0 1-1 2-1-2-2-5-3-7l-2-7h-1c-2-5-4-11-6-16-1-3-3-6-4-9-1-4-2-7-4-11-2-3-3-7-4-11l-9-23-27-68-8-23c-4-9-7-18-8-28-1-16 2-32 12-44 14-16 34-21 54-22l-4-8-73 1v-3z" class="N"></path><path d="M583 595l-8-5 12-35c3 1 6 2 9 2v1h2 1c1 1 2 1 3 2 1 0 3 1 4 1 2 0 3 1 5 2 1 1 1 0 2 0 2 0 4 1 5 2h0l-8 23c0 2-3 7-2 8l-3 6-14-4c-1-1-2-1-3-2 0 0-1 0-2-1h0-3z" class="a"></path><path d="M560 647l-4-2 4-14c1-3 2-5 3-7 1 0 1 0 2-1v-1c-1-3 1-8 2-10 2-4 4-4 8-5l-5-3c0-3 3-8 4-12l9 3h0 3 0c1 1 2 1 2 1 1 1 2 1 3 2l14 4c0 1 0 2-1 3-1 5-9 27-11 29l-2 1s1 1 2 1c0 2-1 4-2 6l-5 14-9-2-1-1-1-1c-2 0-2-1-4-1-1 0-2-1-4-1-1-1 0-1-1-1h-1l-2-1c-1-1-1-1-3-1z" class="U"></path><path d="M591 598l14 4c0 1 0 2-1 3 0-1-2-1-3-2-3-1-8-3-10-5z" class="Q"></path><path d="M607 500l13 5c5 2 12 6 17 7h1c-2 8-6 17-9 25-3 10-7 19-11 28h0c-1-1-3-2-5-2-1 0-1 1-2 0-2-1-3-2-5-2-1 0-3-1-4-1-1-1-2-1-3-2h-1-2l-8-5 10-29c3-8 5-17 9-24z" class="V"></path><path d="M692 200h4l-1 3h14l5 13c1 1 0 1 1 2-1-6-5-9-2-15 3-1 5 0 8 0h13c1 2 2 5 3 7v1c1 2 1 3 2 4l13 30h-1c-11 8-20 22-27 34-7 13-11 28-16 42l-25 69-6 15c-1 4-2 8-5 11v1c0 4-3 8-4 12l-27 74c-1 2-2 7-3 9h-1c-5-1-12-5-17-7l-13-5c0-1 0-2 1-3l1-6h0s-1 1-1 2l-1-1c1-2 2-5 2-8 1-7 4-13 6-19l12-34 40-113c9-26 15-49 3-75v-1h1 1l-8-11-2-3-6-4c-1-1-2-1-3-1l11-12h1c2-2 4-5 7-7v-1c3-2 14-1 18-1l2-2z" class="d"></path><path d="M692 200h4l-1 3-5-1 2-2z" class="Q"></path><path d="M653 223l11-12h1c-3 4-7 8-9 13-1-1-2-1-3-1z" class="W"></path><path d="M608 497c2 4 17 10 22 11 2 1 6 2 7 4-5-1-12-5-17-7l-13-5c0-1 0-2 1-3z" class="X"></path><path d="M662 228h1c3-4 5-8 8-12 2-3 3-5 6-6 0 3-7 13-9 16-1 2-3 3-4 5l-2-3z" class="V"></path><defs><linearGradient id="DV" x1="618.124" y1="365.111" x2="670.129" y2="369.875" xlink:href="#B"><stop offset="0" stop-color="#a2a1a1"></stop><stop offset="1" stop-color="#c0bfbd"></stop></linearGradient></defs><path fill="url(#DV)" d="M670 243v-1h1 1c4 7 6 15 7 23 2 10 0 22-2 32-4 14-9 28-14 42l-24 68c-4 11-9 23-12 35 0 1-1 1 0 2l1 1v1l-2-1c-3 4-5 11-6 15l-9 26c0 1-1 5-2 5h0s-1 1-1 2l-1-1c1-2 2-5 2-8 1-7 4-13 6-19l12-34 40-113c9-26 15-49 3-75z"></path><path d="M350 200h3v3l73-1 4 8c-20 1-40 6-54 22-10 12-13 28-12 44 1 10 4 19 8 28l8 23 27 68 9 23c1 4 2 8 4 11 2 4 3 7 4 11 1 3 3 6 4 9 2 5 4 11 6 16l13 34c1 3 2 6 2 8l-21 4c-2 0-4 0-6 1l-8 1-2-1v1c-2 0-3-1-4 0-1 0-2 0-3 1h-3c-2 0-4 0-6 1h-2-6c-1-3-1-6-3-8 0-2-1-3-2-4-1-2-2-5-3-7-1-4-9-20-8-23l3-1c-2-1-3-1-5-3-3-2-5-10-6-14-3-7-6-15-9-23l8-1c-3-1-5 0-8 0-3-5-5-11-7-16l-1-3c-2-4-3-8-5-12l-13-33c2 0 4-1 5-1 4-1 7-2 10-2-5-1-10-1-16 0-2-5-6-12-7-18l1-1h0l1-1h-2c-3-2-4-9-6-12l-14-38-7-15c-1-2-2-4-2-7v-1c-5-3-8-10-13-15s-10-10-16-14l15-35 2-3c1-2 14-2 17-1h4 13 6c2-1 6 0 9 0h20l1-3z" class="d"></path><path d="M350 200h3v3h-4l1-3z" class="G"></path><path d="M370 469h13l-8 3c-2-1-3-1-5-3z" class="M"></path><path d="M347 412c7-1 13-1 20 0-6 1-13 1-19 3l-1-3z" class="W"></path><path d="M301 223l-3 8c-2 6-5 11-9 17 2-4 2-9 4-12 2-4 6-9 8-13z" class="a"></path><path d="M323 344c4-1 8-1 13-2 2 0 5 1 8 1h7l-19 2c-4 1-8 0-11 1l1-1h0l1-1z" class="U"></path></svg>