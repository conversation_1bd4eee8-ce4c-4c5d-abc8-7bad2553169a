<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="64 52 464 612"><!--oldViewBox="0 0 580 752"--><style>.B{fill:#252423}.C{fill:#47403a}.D{fill:#524b43}.E{fill:#1c1b1b}.F{fill:#5b534c}.G{fill:#433e38}.H{fill:#282624}.I{fill:#3d3832}.J{fill:#4e4943}.K{fill:#2f2d2c}.L{fill:#34302e}.M{fill:#383531}.N{fill:#665d56}.O{fill:#403b38}.P{fill:#544c45}.Q{fill:#595149}.R{fill:#45403c}.S{fill:#35322e}.T{fill:#201f1f}.U{fill:#2b2927}.V{fill:#2f2d2a}.W{fill:#c0b4ac}.X{fill:#9a8f87}.Y{fill:#90847a}.Z{fill:#181717}.a{fill:#786f64}.b{fill:#867c71}.c{fill:#82776e}.d{fill:#a3968d}.e{fill:#72675f}.f{fill:#3a3837}.g{fill:#b9ada2}.h{fill:#94877e}.i{fill:#6a5f55}.j{fill:#a99d93}.k{fill:#635d55}.l{fill:#897d74}.m{fill:#7f7167}.n{fill:#beb3ac}.o{fill:#70665a}.p{fill:#565046}.q{fill:#baafa7}.r{fill:#cec4be}.s{fill:#aba098}.t{fill:#a09285}.u{fill:#988878}.v{fill:#b2a499}.w{fill:#dfd8d4}.x{fill:#796e66}.y{fill:#c7bdb5}.z{fill:#d6ccc7}.AA{fill:#982b27}.AB{fill:#d95d4c}.AC{fill:#e87862}.AD{fill:#f8cdad}.AE{fill:#8a2a25}.AF{fill:#f5be99}.AG{fill:#ed9b7e}.AH{fill:#a32c27}.AI{fill:#ec8b6b}.AJ{fill:#e25b4a}.AK{fill:#e56a56}.AL{fill:#d7493b}.AM{fill:#9b2b27}.AN{fill:#e6dfdb}.AO{fill:#d03c33}.AP{fill:#f2a889}.AQ{fill:#ee8a6a}.AR{fill:#70211e}.AS{fill:#db604b}.AT{fill:#d4cec5}.AU{fill:#f0ad89}.AV{fill:#a72f2b}.AW{fill:#7f2622}.AX{fill:#b2312c}.AY{fill:#e58068}.AZ{fill:#eec3a6}.Aa{fill:#f7cca8}.Ab{fill:#c4352e}.Ac{fill:#131313}.Ad{fill:#ea846c}.Ae{fill:#ede8e6}.Af{fill:#8f2b26}.Ag{fill:#371a1a}.Ah{fill:#62211f}.Ai{fill:#efd0bd}</style><path d="M300 454h2v1h-1l-1-1z" class="T"></path><path d="M215 164l2-1v2h-2v-1z" class="M"></path><path d="M153 475s0 1 1 1l-1 2v-1c-1-1-1-1 0-2z" class="B"></path><path d="M401 151h2l-1 1h-1-1l1-1z" class="T"></path><path d="M300 588l1 2-1 1c0-1-1-1-1-1 1-1 1-1 1-2z" class="B"></path><path d="M357 389h1v3-1h-1v-2z" class="AE"></path><path d="M375 342l1 1v1c0 1 0 1-1 1h0v-3z" class="AV"></path><path d="M453 383l1-2s1 0 1 1v1h-2z" class="C"></path><path d="M207 520h2v1h-2v-1z" class="U"></path><path d="M407 155v1s0 1 1 1l-1 1-2-3h2z" class="E"></path><path d="M300 621l1 1c0 1 0 2-1 3l-1-1 1-3z" class="L"></path><path d="M186 199h2v1c-1 1-1 1-2 1v-2z" class="Z"></path><path d="M300 621l2-2h0v2l-1 1-1-1z" class="V"></path><path d="M396 139l1 2-1 1-2-1 2-2z" class="c"></path><path d="M206 612l1 1v2c0-1-1-1-2-1l1-2z" class="H"></path><path d="M195 187h2v1 2l-2-3zm246 239h1v3l-1-2v-1z" class="Q"></path><path d="M278 624l1-1h0c1 1 1 1 1 2h-1c0 1 0 1-1 2v-3z" class="E"></path><path d="M240 566l1-1c1 0 2 0 3 1l-1 1c-1 0-2-1-3-1z" class="H"></path><path d="M434 396c1 1 1 1 2 1l-1 3h0l-1-2v-2z" class="v"></path><path d="M388 553l1-2 1 3c-1 0-1 0-2 1v-2z" class="C"></path><path d="M434 396l1-3v2l1-1v3c-1 0-1 0-2-1z" class="W"></path><path d="M228 613h2 3l-4 1h-2l1-1z" class="Q"></path><path d="M399 187l1-1c1 0 1 1 2 2v1l-3-1v-1z" class="C"></path><path d="M452 396h1v5-2h-1v-3z" class="R"></path><path d="M445 356h1 2v1l-1 1-2-2z" class="G"></path><path d="M221 163c1-1 2-1 4-1v1c-1 1-2 1-4 1v-1z" class="E"></path><path d="M399 194l1-1h1c1 1 0 2 0 3-1 0-1-1-2-2z" class="a"></path><path d="M163 492l1 1v1c-1 0-2 1-3 1v-1l2-2z" class="k"></path><path d="M312 441l2 2v1c-1-1-2-1-3-1 0-1 1-1 1-2z" class="T"></path><path d="M321 459v-2l1-2h0c1 2 0 3 0 5l-1-1z" class="U"></path><path d="M129 455h0l2 2c-1 1-2 1-3 1l1-1v-2z" class="H"></path><path d="M452 392l1 1v3h-1v-2-2z" class="C"></path><path d="M391 294h3l-1 3h0v-1h-2l-1-1 1-1z" class="AW"></path><path d="M500 182s1 1 1 2h-1 0l-1 1-1-2 2-1z" class="r"></path><path d="M491 209c1 1 1 3 1 4-1 0-1-1-2-2v-1l1-1z" class="N"></path><path d="M373 391l2-1h1l2 1-1 1c-1 0-3-1-4-1z" class="C"></path><path d="M119 172l3-3-1 3h0-1-1z" class="s"></path><path d="M337 641h0c2 0 4 2 5 4h0c-2-1-4-3-5-4z" class="E"></path><path d="M482 301l3-3c-1 2-1 3-1 5h0-1c0-1 0-2-1-2z" class="Z"></path><path d="M221 163v1l-4 1v-2h4z" class="L"></path><path d="M320 297l1-1s1 0 1 1v2h0c-2-1-2-1-2-2z" class="H"></path><path d="M177 119v4l-1-1-1-1 2-2z" class="Y"></path><path d="M334 539h1c0 1 1 1 1 3h-1v-1l-1-1-1 1h-1c0-1 1-1 2-2z" class="T"></path><path d="M470 213v3l-1 2-1-4 2-1z" class="k"></path><path d="M197 168l-2-2c1-1 1-1 2-1s1-1 1-1c0-1 1-1 2-1l-3 3v2z" class="V"></path><path d="M162 475l-1-6h0l1 1v3c1 1 1 2 1 3l-1-1z" class="U"></path><path d="M304 424l1 2c-1 1-3 2-4 3 1-2 2-3 3-5z" class="V"></path><path d="M231 624h5 0l-1 1v1c-1 0-3-1-4-1v-1z" class="N"></path><path d="M400 305c2 0 4-2 6-1h-1 0v1h-2l-1 1-2-1zm19-122h4v1h-1c-1 1-2 0-3 0v-1z" class="H"></path><path d="M306 291l1-1c0 1 1 1 1 1 1 1 1 1 1 3h-1l-2-3z" class="E"></path><path d="M146 494h3l-1 4h-1l-1-2v-2z" class="D"></path><path d="M227 623c1 0 3 0 4 1v1l-4-1h0 0l1-1h-1 0z" class="J"></path><path d="M388 389c2-1 5-1 7 0v1c-2 0-4-1-6-1h-1z" class="S"></path><path d="M444 348h1v-3h1l1 4h-2l-1-1z" class="H"></path><path d="M370 164c2 0 4 0 5 1h1l-1 1-3-1h-2v-1z" class="F"></path><path d="M311 405l2-3h0v2c0 1-1 2-1 2v1l-1-2z" class="T"></path><path d="M157 462l3 1h0c0 1-1 1-1 1-2 0-3 0-5-1 1 0 2 0 3-1z" class="b"></path><path d="M487 282c0-2-1-3-1-4h1c1 0 1 1 1 2l1 2h-2z" class="c"></path><path d="M167 113c1 0 1 0 1 1s-1 3-1 4h-1 0l1-5z" class="h"></path><path d="M147 494c1-1 1-2 2-3h1l-1 3h-3 1z" class="e"></path><path d="M420 376c0 2 0 7 1 8l1 1c-1 1-1 1-2 1h-1s0-1 1-1v-1-8z" class="H"></path><path d="M382 503h3 1c-2 1-4 2-6 2l2-2z" class="M"></path><path d="M365 565c0-1 1-1 2 0h1c1 0 3 1 4 2-1 0-2 0-3-1-2-1-3 0-5-1h1z" class="H"></path><path d="M450 434v-7h0l2 1c-1 1-1 2-1 2h-1v4z" class="M"></path><path d="M477 455c1 0 1 0 1 1l-3 1-2-1h0l4-1z" class="g"></path><path d="M486 359v3 3c-1-1-1-3-2-4 0-1 1-1 2-1v-1z" class="G"></path><path d="M422 137c-1 2-2 4-3 5v-2l1-2 2-1z" class="C"></path><path d="M480 296v-1c-1-1-2-2-2-3s0-2-1-3h1 0c0 1 1 2 1 3l1 1c0 1 1 1 1 2h1l-2 1z" class="E"></path><path d="M470 216l2 5-2 1-1-4 1-2z" class="D"></path><path d="M369 388c1 1 2 1 4 1l2 1-2 1c-1-1-3-2-5-2l1-1z" class="I"></path><path d="M215 165c-1 1-3 1-5 2v-1c1-1 3-2 5-2v1z" class="O"></path><path d="M423 489c0 2 1 4 0 6v-1c-1 0-2-1-2-2l2-3z" class="Q"></path><path d="M314 510l4-4c1 0 1 0 1 1 0 0-1 0-1 1-1 1-1 1-1 2h-3z" class="E"></path><path d="M414 500c0-1 1-1 1-2l1 1v-1h0c0 1 0 1-1 2s-1 2-1 3v1c0 1-1 2-2 3 0-1 1-2 1-3h1c0-1 0-1-1-2h0l1-1v-1z" class="T"></path><path d="M161 494c0-2-1-5-2-7 2 1 3 4 4 5l-2 2z" class="K"></path><path d="M299 590s1 0 1 1l-2 3h-2l1-2 2-2z" class="E"></path><path d="M312 540h0c0 1 0 1-1 2h1c-1 1-2 2-3 2h-1v-1l4-3z" class="P"></path><path d="M392 487h2l-2 4h-2l2-4z" class="S"></path><path d="M400 506c1-1 2-2 4-2h0c-1 2-3 4-4 6v-4z" class="I"></path><path d="M288 639c0 1 2 4 3 5-1 0-1-1-2-1h0c-2-1-2-2-3-4h2z" class="B"></path><path d="M262 98c1 2 2 4 2 6v1l-1-2c0-1-1-3-2-4l1-1z" class="Q"></path><path d="M449 361h1c1 2 1 4 1 7l-1-1h-1l1-3h0l-1-3z" class="E"></path><path d="M452 410v-1h1v1-1c1 2 0 4 1 5h-2v-2-2z" class="S"></path><path d="M452 410v-1h1v1c0 1 0 1-1 2v-2z" class="D"></path><path d="M465 432c2 2 3 3 2 6v1h0-1c1-1 1-2 0-3l-2-3c0 1 1 1 2 1-1-1-1-1-1-2h0z" class="s"></path><path d="M402 306c-4 0-6 1-9 2h-1l1-1c2-1 4-2 7-2l2 1z" class="T"></path><path d="M354 623l1 1-1 1c-1 0-3 1-5 1v-2h0l5-1z" class="N"></path><path d="M485 306l3 3h1v1l-3-1h-1c-1 1-2 1-3 2 1-1 2-3 3-5z" class="S"></path><path d="M334 624l5 1 1 1h-2l-5-1c0-1 0-1 1-1z" class="t"></path><path d="M390 144l4-3 2 1-2 1-3 2-1-1z" class="m"></path><path d="M448 357l1 4 1 3h0l-3-6 1-1z" class="M"></path><path d="M421 475c1-1 2-5 3-6l-1 3c0 1 0 0 1 0 0 1 0 2-1 2v2c1 1 0 1 0 2v-1s-1-1-1-2h-1z" class="U"></path><path d="M387 175c2 0 4 1 5 1v2h-2c-2-1-2-2-3-3z" class="H"></path><path d="M190 571c0 1 0 2 1 3l-1 4c0 1 0 2-1 3h0l1-10z" class="l"></path><path d="M385 503c0-1 0-1 1-1 2 0 4-2 7-3-3 2-4 4-7 5v-1h-1z" class="S"></path><path d="M368 164c1 0 1 1 2 1h2l3 1 1-1h1c-1 1-1 1-2 1l-1 1c-2 0-4-2-6-3z" class="B"></path><path d="M368 164c-2 1-5-1-6-1 2 0 6 0 8 1v1h2-2c-1 0-1-1-2-1z" class="D"></path><path d="M444 383v3-3c1-1 1-2 1-3h0v2c0 2 1 4-1 6v1h0s0-1-1-1c0-1 1-1 0-2 0-1 1-2 1-3z" class="B"></path><path d="M387 216c2 2 3 5 4 8h-1c-1-2-2-6-4-7l1-1z" class="O"></path><path d="M355 295c1 2 2 5 2 8h-1v-2l-1-1c-1 1 0 2-1 3v-5l1-3z" class="Ab"></path><path d="M263 607l1-1 1 1c0 2 0 4-1 6v-4c0-1-1-1-1-2z" class="E"></path><path d="M422 464c1-1 1-2 2-2 2-1 3-2 4-3l1 1-1 1-2 1h1v1h-2l-3 1z" class="f"></path><path d="M95 307h0l2-2 2 1-3 2 1 1h-1c-1 1-2 0-3 0l1-1c1 0 1 0 2-1h-1z" class="T"></path><path d="M236 610c0 1-1 1-2 2h2 2c-2 0-3 1-5 1h-3l6-3z" class="D"></path><path d="M301 622l1-1c0 1-1 2 0 2v1 2c-1 1-1 2-1 4v-1c0-1-1-1-1-2v-2c1-1 1-2 1-3z" class="B"></path><path d="M262 612c0-2 0-5-1-7l-1-1v-1l2 1 1 3c0 1 1 1 1 2l-1 1-1 2z" class="k"></path><path d="M405 305c2 0 3 1 5 1v1c-1 0-1 0-1-1h-1v1h0c-2 0-4-1-5-2h2z" class="T"></path><path d="M490 211c1 1 1 2 2 2 1 1 0 4 0 5v2l-2-9z" class="P"></path><path d="M114 179l1 1v1c-1 1-1 2-1 2l-1 3c-1-1-1-1-1-2s1-3 2-5z" class="X"></path><path d="M300 585l1 1-1 2c0 1 0 1-1 2l-2 2v-1l3-6z" class="I"></path><path d="M118 433h3l-2 2c-1 1-1 3-1 5h0l-1-1c0-2 0-4 1-6z" class="u"></path><path d="M243 567l1-1c2 2 6 3 8 6l-6-3v-1l-3-1z" class="M"></path><path d="M466 172c1 1 2 3 3 5h-1l-1-1h-1s0-1-1-2c0 0 0-1 1-2z" class="j"></path><path d="M305 337c-1-1-3-3-2-5v1h0v-1h0c0-1-1-3-1-4 1 0 1 1 1 1 0 1 0 1 1 2h0v2h0c1 0 1 1 1 1v1h0v2h0z" class="T"></path><path d="M394 580h1l-1 11-1-3 1-8z" class="I"></path><path d="M416 343l-1-1h1 1c1 0 1-1 2-1h1l-1 1v1h1c1 0 3 1 4 2-2-1-3-1-4-1-2 0-2-1-4-1h0 0z" class="E"></path><path d="M112 184c0 1 0 1 1 2v2c-1 1-1 2-1 3h-1v-5l1-2z" class="m"></path><path d="M112 184c0 1 0 1 1 2v2l-1-1-1-1 1-2z" class="b"></path><path d="M258 97v-5l4 6-1 1c-1-1-2-1-3-2z" class="q"></path><path d="M283 578h0c0 2 2 2 3 4h-1l-1-1c-1 0-1 0-2 1l-1 1v-4h1l1-1z" class="V"></path><path d="M178 470l-1-1c-1-2-2-3-1-5 1 2 3 5 4 7-2 0-2-2-3-3 0 0 1 1 1 2h0 0z" class="E"></path><path d="M322 604v-4h0c1 0 2 1 2 1v1 1c1-1 1-1 2-1h1c-1 1-3 2-4 2h-1z" class="L"></path><path d="M282 624h0v-4c1 0 1 1 1 1 2 0 2 1 3 2h-1v1l-2-1-1 1z" class="B"></path><path d="M308 595l1 1c-1 2-2 5-3 6l-1-1 3-6z" class="F"></path><path d="M192 603h2c-1 1 0 2 1 3l4 4h0v1c-2-1-5-5-6-7v-1h-1z" class="B"></path><path d="M378 391l8 2-1 1c-3-1-5-1-8-2l1-1z" class="p"></path><path d="M83 191c1 0 1 1 1 2v7h0v-3c-1 0-1 1-1 1h-1l1-4v-3z" class="D"></path><path d="M264 560h1c1 1 2 2 4 3 1 1 3 2 4 3l-1 1v-1c-2 0-7-4-8-6z" class="E"></path><path d="M400 339h0 2 1c-1 1-2 2-2 3h-2 0v1h-1v-1c0-1 1-2 2-3z" class="Z"></path><path d="M442 445l1-1h0c0-1 1-2 1-2h0c0 1 0 2-1 2v1c1 0 1-1 2-1h1l1 1h5l1 1h-1-8l-2-1z" class="B"></path><path d="M395 482h3l-4 5h-2l3-5z" class="G"></path><path d="M390 491h2c-1 2-2 5-4 6h-2l4-6z" class="B"></path><path d="M388 553c-1-4-1-7-3-11 2 3 3 7 4 9l-1 2z" class="O"></path><path d="M396 204h1v1c-1 1-3 1-4 1-1 1-3 1-4 2l-1-1c3-2 5-2 8-3zM204 505c-1 0-2 0-3-1h-3c-2-1-3-3-4-4 1 1 3 1 4 2 2 1 3 2 5 2h0l1 1z" class="C"></path><path d="M419 140v2c0 3-2 5-2 7h-1c-1-2 1-4 2-6h-1c0-1 1-2 2-3z" class="I"></path><path d="M458 454l-1-1 12 3h-3l-1 1c-2 0-5-2-7-3z" class="t"></path><path d="M294 625h1v1l-1 1-3 3-1-2s-1-1-1-2h1c0 1 1 1 2 2 1-1 2-2 2-3z" class="r"></path><path d="M457 458h-1l-2-2 1-1c1 0 2 1 3 1h0l2 2h0-3z" class="I"></path><path d="M258 97c1 1 2 1 3 2s2 3 2 4h-1c-2-2-4-3-4-6z" class="P"></path><path d="M179 128c1 2 2 4 4 7l-2-1h0c-2-1-3-3-4-5 1 0 1 0 2 1h0v-2z" class="t"></path><path d="M498 183c-2-4-4-6-7-8 4 1 6 3 9 7l-2 1z" class="AT"></path><path d="M377 179h1 1v1l-1 1 4 3v1l-2-1c-1 0-1 0-2-1s-1-2-1-4zm-70 433h1c0 2 0 4-1 5 0 3 0 6-1 8 0-4 0-9 1-13z" class="M"></path><path d="M469 177c1 2 3 5 3 7l-1 1-3-8h1z" class="d"></path><path d="M398 187h1v1l3 1 1 1h0v1c1 0 2 1 3 2-2-1-3-2-4-3h-4v-3z" class="E"></path><path d="M174 114c0-1 0-1 1-1l1 1c1 1 2 2 4 3h-4-1l-1-3z" class="W"></path><path d="M248 560l-2-2h1 1c1 0 2 1 3 1 2 1 3 3 4 4-1 0-2-1-3-1-1-2-2-3-4-3v1z" class="B"></path><path d="M396 352h0 1 0c-1 2-2 4-4 6v-4h1l2-2z" class="Q"></path><path d="M411 187c2 2 3 5 6 7v2h0l-2-1c0-1 0-2-1-3 0 0 0-1-1-1s-1-1-2-1v-3z" class="H"></path><path d="M399 206s0-1 1-1v3 1l-2 2h0v-1c-1-1-2 0-3 0v-1l1-1c1-1 1 0 2 0l1-2z" class="k"></path><path d="M109 237c0 1-1 2-1 4h-1l-5 2h0l1-2c1 0 2-1 2-1l4-3z" class="e"></path><path d="M190 568l3-4h1c-2 3-3 6-3 10-1-1-1-2-1-3v-3z" class="Y"></path><path d="M105 339l-1-1c0-1-1-1-2-2h1c2 0 1 2 4 1 1 2 1 4 2 6l-1-1c-1-1-1-2-3-3z" class="K"></path><path d="M409 113c1 0 1 1 1 1 0 2 0 3-2 4-1 0-3-1-3-1 2-1 3-2 4-4z" class="w"></path><path d="M312 329l1-5v1h0 1v4s0 1-1 2h1c0 1-1 1-1 2-1-2-1-3-1-4z" class="d"></path><path d="M469 302h0c2-1 2-2 3-4h1c0 2-1 3 0 5-1 0-1 0-1 1-1 0-2 1-3 2 1-1 1-2 1-3l-1-1z" class="j"></path><path d="M194 183h5-2l-2 1v1l1 1 1 1h-2c-1-1-2-3-3-4h2z" class="k"></path><path d="M182 91c0 1-1 1-1 2l-4 5-2-1c2-3 5-4 7-6z" class="h"></path><path d="M289 561l2 3c-1 1-2 2-2 3l-2-4 2-2z" class="Y"></path><path d="M390 560l1-1c1 1 1 1 1 2s1 1 1 1l1 1v5c-1-2-3-3-3-5 0-1 0-2-1-3z" class="X"></path><path d="M189 581h0c1-1 1-2 1-3 0 4 0 7 1 11 0 1-1 2-1 2l-1-1v-9z" class="S"></path><path d="M131 388l1 5c-1 1-1 2-1 3 0 3 1 7 0 10-1-5 0-10-1-15 1-1 1-2 1-3z" class="K"></path><path d="M457 429l4 2h0c-1 0-1 1-1 1 1 2 1 3 1 5 0 1-1 1-1 2v-1c-1-1 0-4-1-5 0-1-1-3-2-4h0z" class="I"></path><path d="M327 569h1l1 1-7 9v-1-1c2-3 4-5 5-8z" class="J"></path><path d="M462 428c1 2 2 3 3 4h0c0 1 0 1 1 2-1 0-2 0-2-1l-3-2h0c1-1 1-1 1-3z" class="j"></path><path d="M490 285c2 0 4-1 6-3l1 1c-2 1-4 2-6 4 0 0-1 0-2 1l-1-2c1-1 1-1 2-1z" class="d"></path><path d="M359 622h4 0c-1 0-2 1-3 1-1 1-4 2-6 2l1-1-1-1c1 0 3-1 5-1z" class="D"></path><path d="M287 556l2 5-2 2-2-4c1-1 1-2 2-3z" class="m"></path><path d="M419 189c1 2 2 2 4 2h2c0 1 1 1 1 1h-4l-1 1c-1 1-1 1-1 2l-1-1v-5z" class="K"></path><path d="M146 492v-1c1-1 1-2 1-4 1-1 2-2 4-3v-1-1s1-1 1-2v2c-2 4-4 7-6 10z" class="E"></path><path d="M406 340l1-2c0-1 1-1 1-1v1 3c-1 1-2 2-3 4 0-2 0-3 1-4h0v-1z" class="K"></path><path d="M498 214l1 2-2 4v1l-1-1-1 2v-2c0-1 2-5 3-6z" class="S"></path><path d="M85 208l3 9h-2c0-1-1-3-1-4h0c-1-2-1-3 0-5z" class="G"></path><path d="M421 475h1c0 1 1 2 1 2v1c0 1 0 2-1 3l-1-1-1 1v-2c1-1 1-3 1-4z" class="R"></path><path d="M421 480c0-1 1-2 2-3v1c0 1 0 2-1 3l-1-1z" class="C"></path><path d="M322 604h1l-1 4c-1 1 0 2 0 2v1h-2v3c-1-2 0-5 1-8 0-1 1-1 1-2h0z" class="k"></path><path d="M448 340l3 3v1 1 3c-1 1-1 2-1 3v-2c-1-2-1-3-1-5h1v-1c0-1-1-2-2-2h0v-1z" class="U"></path><path d="M472 456h1 0l2 1h-2l-5 1-3-1 1-1h3 3z" class="d"></path><path d="M472 456h1 0l2 1h-2-3-1c1-1 2-1 3-1z" class="W"></path><path d="M472 221v3s0 1 1 2c-1 1-2 4-3 5h0v-1c1-2 1-6 0-8l2-1z" class="i"></path><path d="M445 438h1c-1 0-1 1-1 1-1 1-1 2-1 3 0 0-1 1-1 2h0l-1 1h-1c1-2 1-5 2-7h2z" class="V"></path><path d="M284 634h0c-2-1-3-2-3-4-1-1-1-2-1-4h1c0 1 0 1 1 2h1v4h0-1c1 1 1 1 2 1v1z" class="U"></path><path d="M319 313v1c-2 3-4 6-5 11h-1 0v-1-2c1-3 4-6 6-9z" class="W"></path><path d="M487 282h2l1 1v2c-1 0-1 0-2 1v1l-2-2 1-3z" class="l"></path><path d="M281 548c-1-1-3-2-3-4h0c2 1 5 3 7 5h-1v1c-1-1-2-1-3-2z" class="M"></path><path d="M243 145l6-8 1 2c-2 2-5 4-6 6h-1z" class="X"></path><path d="M459 217c1 0 2 0 3 1 0 1-1 3 0 3 0 2 0 4 1 5v1l-2-2c0-3-1-5-2-8z" class="C"></path><path d="M486 285l2 2v-1l1 2h0l1 1v1h-2-1c-1 0-1 0-2-1l1-4z" class="Y"></path><path d="M405 471h1c1 0 1-1 1-1 0-2 2-4 3-6v4c-1 1-1 1-1 2h0c-1 1-1 2-2 4v-3h0v1c-1 0-1 0-2-1z" class="E"></path><path d="M409 470v-2h1c-1 1-1 1-1 2z" class="B"></path><path d="M87 270c2 1 5 1 7 1l-1 1 1 2c-3-1-6-2-9-2v-1l2-1z" class="l"></path><path d="M418 117l2 8c0 1 0 1-1 2v-1l-2-6v-2l1-1z" class="u"></path><path d="M290 572l2 1 1 1c0 2 0 3 1 4h-3l-1-6z" class="m"></path><path d="M176 122l1 1 2 5v2h0c-1-1-1-1-2-1l-2-4h0l1-1v-2z" class="d"></path><path d="M252 562c1 0 2 1 3 1 1 2 3 5 4 7h-1v-1c-2 0-5-5-6-7z" class="U"></path><path d="M194 183c2-1 3-2 4-3 0-1-1-1-2-2l1-1h1c1 1 1 1 1 2h1c0 1-1 2 0 3l-1 1h-5z" class="H"></path><path d="M197 166c1-1 3-1 4-2h1l1 1h0c-2 1-3 3-4 5l-2-2v-2z" class="C"></path><path d="M481 352c2 2 3 4 4 6l1 2c-1 0-2 0-2 1l-1-3c-1-2-2-4-2-6z" class="f"></path><path d="M483 358h1l1 1v-1l1 2c-1 0-2 0-2 1l-1-3z" class="O"></path><path d="M453 383h2c0 4-1 7-2 10l-1-1c0-3 1-6 1-9z" class="I"></path><path d="M395 210h0c-1 1-6 3-7 3 1-1 3-4 5-5 1 0 1 1 2 1h0v1z" class="P"></path><path d="M339 625l10-1v2h-11 2l-1-1z" class="l"></path><path d="M452 399h1v2 8 1-1h-1v1l-1-1c2-2 1-5 1-7-1-1 0-2 0-3z" class="O"></path><path d="M262 612l1-2 1-1v4 3c-1 1-1 2-2 2l-1-1c1-1 1-2 2-3l-1-1-1 1 1-2h0z" class="e"></path><path d="M262 612l1-2 1-1v4 3c-1-1 0-3-1-4h-1 0z" class="N"></path><path d="M205 614h-1v-1l-3-3-3-3 1-1 7 6-1 2z" class="E"></path><path d="M427 463l8-2h3c-4 2-9 2-13 3v-1h2z" class="l"></path><path d="M480 329h1c4 1 6 2 10 4l-12-3v-1h1z" class="v"></path><path d="M398 201l2-1h0 1c0 1 0 2 1 4-1 0-2 1-2 1-1 0-1 1-1 1l-1-5z" class="R"></path><path d="M400 200h1c0 1 0 2 1 4-1 0-2 1-2 1v-5z" class="c"></path><path d="M208 512l3 1 3 3c0 1 0 1 1 2l-1 1c-2-2-4-5-6-7z" class="L"></path><path d="M478 301h0c1-1 2-2 3-2v3l-5 5-1-1c2-1 3-3 4-4l-1-1z" class="k"></path><path d="M183 500c-2-5-5-10-6-15l7 14h0l-1-1v2h0z" class="O"></path><path d="M365 474h0c2 3 5 7 4 10h0l-1-3c-1-1-1-2-2-2v2 2h0l-1-1c-1-2 0-4 1-5-1-1-1-2-1-3z" class="U"></path><path d="M266 111c1-3 3-6 5-8h1 1 1l-3 4c-1 1-2 1-2 2l-1-1-1 3h-1z" class="l"></path><path d="M284 76h1 1l-2 6c0 1-1 3-1 4h0c-1-1-1-2-1-2l2-8z" class="y"></path><path d="M422 464l3-1v1c-3 1-6 3-9 6h-1c2-3 5-5 7-6z" class="c"></path><path d="M182 152l1 1v1l-2 2v1h1v1s-1 2-2 2-1 0-2 1l-1-1h0c1 0 2-1 2-2l1-2c1-1 2-2 2-3v-1z" class="T"></path><path d="M342 566h0c2 0 3-1 4-2 1 0 3-2 4-2-1 2-4 5-6 6h-3-1l2-2z" class="M"></path><path d="M105 339c2 1 2 2 3 3l1 9-1-1v-1h-1v3h0v-4l-1-1 1-1c0-2-1-5-2-7z" class="n"></path><path d="M396 91c-5-3-8-4-14-4 5-1 9 0 14 1h-3c1 1 1 1 2 1 0 0 1 1 1 2z" class="v"></path><path d="M411 315h2l1 1h-2c-4 2-8 3-12 3 2-1 4-2 6-2l5-2z" class="o"></path><path d="M376 323c1 0 1 0 2-1v1l-1 6-2 2v-8h1z" class="AW"></path><path d="M435 336h2l-1 1 1 1c1 0 2 0 3-1h1 1v1h-1l1 1h1c1 1 1 1 1 2-3 0-7-2-10-3 1-1 1-1 1-2z" class="a"></path><path d="M278 624h0v3c2 3 3 6 4 9v1 1h-1c-1-1-4-11-4-13l1-1z" class="C"></path><path d="M172 101l3-4 2 1c-1 1-2 3-2 4v1c-1 0-1 0-2 1v-1-1l-1-1z" class="c"></path><path d="M244 625l2-1c1 1 1 1 1 2h-2-10v-1l1-1 8 1z" class="a"></path><path d="M244 625l2-1c1 1 1 1 1 2h-2-5v-1h4z" class="b"></path><path d="M407 181h0l2 1 6 3h-3 0-2c-2-1-3-1-5-2v-1h2v-1h0z" class="W"></path><path d="M407 181h0l2 1c-1 1-2 1-4 1v-1h2v-1h0z" class="g"></path><path d="M378 162l-12-6h0l3 1c1 0 3 1 4 2 1 0 2 0 3 1h1 0l2 2h-1z" class="M"></path><path d="M481 299l1-1c1-1 2-1 2-3 1 0 1 1 2 1l1 2v1h0l-1-2h-1v1l-3 3-1 1v-3z" class="p"></path><path d="M420 125l2 12-2 1c0-1 0-2-1-2 1-3 1-6 0-9 1-1 1-1 1-2z" class="i"></path><path d="M100 304c0 2 0 3 1 5 1 1 2 3 2 4 1 2 3 3 4 5h-1l-4-6c-1-1-1-3-3-4l-2 1-1-1 3-2 1-2zm316 11l2-1v2c1 0 1 1 2 1l-1 1h-1-1-3l-2-2h2s1-1 2-1z" class="M"></path><path d="M416 315v2l-2 1-2-2h2s1-1 2-1z" class="K"></path><path d="M220 175c2-4 5-7 7-11l1 1-4 7-3 3h-1z" class="D"></path><path d="M300 548l1 1-2 2-2 2c-1 1 0 1-1 1s-2 1-2 2c-1-1-1-1-1-2 1-2 5-4 7-6z" class="X"></path><path d="M398 201c0-2 0-2 1-4 0-1-1-1-2-1v-1h0-1v-1h3c1 1 1 2 2 2v1h0v3h-1 0l-2 1z" class="N"></path><path d="M478 326c-1-1-2-2-2-4h1l1 1c1 1 1 2 2 3v3h-1v1c-1 1-2 1-3 1v-1h0v-1c1 0 2 0 3-1v-1l-1-1z" class="d"></path><path d="M378 162h1c3 1 6 2 7 5 1 1 0 1 1 1l-1 1h-1c-1-1-2-3-3-5-1 0-3-1-4-2z" class="D"></path><path d="M328 566h1c0 1-1 1-1 1 0 1 0 2-1 2-1 3-3 5-5 8h-2c3-4 6-7 8-11z" class="O"></path><path d="M282 624l1-1 2 1v-1h1c1 2 1 4 1 6h0v1l-1-2h0l-1-1-1 1 1 1h-1 0c0-2-1-3-2-5z" class="U"></path><path d="M109 362h1c0 2 1 3 2 4l-2 1h-1c-1 3-2 5-2 8h-1c1-4 3-8 3-13z" class="d"></path><path d="M386 218v-1c2 1 3 5 4 7h-5v-1l1 1v-2-4z" class="I"></path><path d="M427 178c2 1 6 1 8 3l-1 1h1c-4-1-7-2-11-2h1v-1h1-2l3-1z" class="h"></path><path d="M114 179c1-3 3-5 5-7h1c-1 1-1 3-1 4-2 1-3 3-4 4l-1-1z" class="j"></path><path d="M284 629h1l-1-1 1-1 1 1h0c0 1-1 1-1 2 1 0 1 1 1 2h-1c1 3 1 5 3 7h-2c-1-1-1-3-2-5v-1-1h0v-1-2z" class="K"></path><path d="M450 392v4h1c-1 1-1 2 0 2v4 1-1-1-1-4h0c1-1 1-1 1-2v2 3c0 1-1 2 0 3 0 2 1 5-1 7v-2l-1-8v-7z" class="H"></path><path d="M444 446h8l-1 1-1 1h-4-4l1-1 1-1z" class="Y"></path><path d="M378 171l-13-3h0l12 2c3 0 6 1 9 2-2 0-4-1-5-1h-3 0z" class="g"></path><path d="M100 219l1-1c1 1 1 2 1 4s1 4 2 5v1c1 1 2 4 3 5s1 1 1 2h0c-5-4-7-9-8-16z" class="h"></path><path d="M458 454c2 1 5 3 7 3l3 1c-3 1-8 1-11 0h3 0l-2-2h0 2l1 1v-1c-2 0-2-1-3-2z" class="L"></path><path d="M106 200c0 3-3 6-3 8-1 5-1 9-1 14 0-2 0-3-1-4l-1 1c0-4 4-15 6-19z" class="c"></path><path d="M300 548h0c1-2 6-5 8-5v1c-2 1-4 3-6 5 0 1-1 2-2 2h-1l2-2-1-1z" class="S"></path><path d="M102 243l5-2v1h2v2c-2 0-4 1-5 2l-2 1v-1l1-1c-1-1-1-1-1-2z" class="j"></path><path d="M478 301l1 1c-1 1-2 3-4 4h-1l-1-1h-1v-1c0-1 0-1 1-1l1-1c1 0 1 0 2 1h0l2-2z" class="P"></path><path d="M474 302c1 0 1 0 2 1-1 1-2 2-3 2h-1v-1c0-1 0-1 1-1l1-1z" class="W"></path><path d="M250 578l1-1c1 1 2 3 4 2l1 2c1 1 3 3 5 4 0 1 0 1-1 2-3-3-7-6-10-9z" class="Q"></path><path d="M167 154h1 0c-1 3-3 4-5 5-1 1-2 1-4 2h0-1l-1-1c4-1 8-3 10-6zm262 261l2-5 1 1-3 10h-1-1l2-6z" class="t"></path><path d="M501 184c1 1 1 2 2 4v5l-1 1-2-5-1-4 1-1h0 1z" class="n"></path><path d="M195 194s1 1 1 2c1 1 2 1 2 3-2 1-3 0-4 0-1-1-1-1-1-2h-1c1-1 1-2 2-2 0 0 0-1 1-1z" class="B"></path><path d="M195 194s1 1 1 2l-1 1h-2-1c1-1 1-2 2-2 0 0 0-1 1-1z" class="K"></path><path d="M129 455v2l-1 1c-1 1-2 0-3 0h-6-8 4c2 0 4-1 6-1 2 1 5-1 8-2z" class="I"></path><path d="M129 457l-1 1c-1 1-2 0-3 0 1-1 2-1 4-1z" class="K"></path><path d="M146 496l1 2h1v5l3 3c1 2 3 3 6 3 0 0 1 1 2 1-2 0-4 0-6-1h-1c0-1-1-1-1-2-4-3-5-6-5-11z" class="F"></path><path d="M362 382h1l8-5h1v2c-3 2-6 3-10 4v-1z" class="c"></path><path d="M192 489c2 1 4 3 5 5 1 1 3 3 4 5h-1v1c-3-3-6-7-8-11z" class="Q"></path><path d="M106 375h1c-1 6-1 11 1 16v2c-3-6-4-12-2-18z" class="a"></path><path d="M104 329v-1h3v1c-1 0-1 0-1 1h-1l-4 1-8 2c3-2 6-3 9-4h2z" class="X"></path><path d="M251 129c-1-2-1-3-2-5-2-2-5-4-8-4h0 0 4c3 1 6 5 8 8v1h-1v1l-1-1z" class="r"></path><path d="M476 319c0 1 0 2 1 3h0c3-2 5-2 8-3-3 1-5 2-7 4l-1-1h-1c0 2 1 3 2 4l-3-1h-1v-2c0-1 1-1 1-1v-3h1z" class="b"></path><path d="M412 325l1-1h-1c-2 0-3-1-4-2h0 1 2v1h3 2 0 0l2-2h3l-1 1v1c-1 0-2 0-3 1-1 0-2 1-4 2l-1-1z" class="U"></path><path d="M435 443l3 1c1 0 2 1 3 1h1l2 1-1 1-1 1-9-3 2-2z" class="d"></path><path d="M111 456c5 0 9-1 13-2 2-1 4-2 5-2-2 2-5 4-8 5-2 0-4 1-6 1 0-1 1-1 1-1 1-1 2 0 3-1h0-2-6z" class="u"></path><path d="M382 557h-1c-2 0-3-1-5-2-1 0-1-1-2-2-1 0-1 0-1-1h0c1 0 3 0 4 1 2 0 4 1 5 2v2z" class="E"></path><path d="M325 93h1v4c-1 2-2 4-4 6h-1c0-3 3-7 4-10z" class="h"></path><path d="M451 414v-1l1 1h2l1 5c0 1 1 3 1 4h-1l-2-2h1v-1l-1-1c0-1-1-2-2-3v-2z" class="i"></path><path d="M452 414h2l1 5c-2-2-2-3-3-5z" class="G"></path><path d="M402 495c2-3 4-7 6-10-1 4-4 9-6 14 0 1-1 2-2 4 0 1-1 1-2 1 0-1 3-7 4-9z" class="J"></path><path d="M207 613c4 2 8 4 12 4 4 1 7 1 11 1-2 0-7-1-9 1-3 0-7-2-9-1h0v-1h-1 1v-1c-1 0-3-2-4-2l1 1v1c-1 0-1-1-2-1v-2z" class="V"></path><path d="M275 604c1 4 2 7 2 11l1 9-1 1-3-21h1z" class="U"></path><path d="M181 134h0l2 1c3 3 6 6 10 8v1c-2 0-3-1-4-2a30.44 30.44 0 0 1-8-8z" class="u"></path><path d="M460 438v1l-1 2c-1 2-3 4-4 6l-2 1-1 1-1-2 1-1h1l-1-1c4-1 6-4 8-7z" class="X"></path><path d="M452 446h1c1 0 1 0 2 1l-2 1-1 1-1-2 1-1z" class="j"></path><path d="M190 568c1-4 1-7 3-11 0 2 0 4 1 5 2-1 4-3 6-5-2 2-4 5-6 6v1h-1l-3 4z" class="O"></path><path d="M433 445c-2-2-5-3-7-5h3 1l-1-2h0c2 1 3 3 5 4h0l1 1h0l-2 2z" class="l"></path><path d="M388 195c1 1 3 1 4 1 0 1-1 1-2 2-1 0-2 2-3 2-1 1-2 1-2 1h-1v-1l4-5z" class="H"></path><path d="M168 111c0-4 2-7 4-10l1 1v1c-1 3-3 5-4 8h-1z" class="x"></path><path d="M446 336c1-1 1-1 2-1 1 1 1 1 2 1l1 1-2 2 2 2v2l-3-3c-1-1-2-3-4-3 0-1 2-1 2-1z" class="G"></path><path d="M446 336c1-1 1-1 2-1 1 1 1 1 2 1l1 1-2 2-3-3z" class="e"></path><path d="M456 388h0c0 5 3 11 5 15 1 1 2 2 2 4h0c-2-1-6-11-7-13 0-2 0-3-1-5 0-1 0-1 1-1z" class="S"></path><path d="M353 300v-11l2 6-1 3v5c0 2 1 7 0 9v-3h0v-7-2h-1z" class="AL"></path><path d="M168 114h1c-1 2-1 3-1 4l1 1c-2 2-4 5-4 9l-1-1c0-3 1-6 2-9h0 1c0-1 1-3 1-4z" class="Y"></path><path d="M189 188h1 1l2 2 1 2 1 2c-1 0-1 1-1 1l-1-1c-1 0-2-1-2-2l-1-1c-1 0-2-1-2-1l1-2z" class="k"></path><path d="M193 190l1 2-1 2c0-1-1-2-1-2v-1l1-1z" class="R"></path><path d="M194 192l1 2c-1 0-1 1-1 1l-1-1h0l1-2z" class="L"></path><path d="M189 188h1 1c-1 1-1 2-1 3-1 0-2-1-2-1l1-2z" class="J"></path><path d="M99 361c0 1-1 1-1 1-1-2 0-4 1-6 1-4 5-7 7-9l1 1c-3 1-6 6-7 9-1 1-1 3-1 4z" class="l"></path><path d="M402 91c4 2 8 6 10 10h0-1l-2-2v1h-1c-1-2-2-4-4-6l-1-1c0-1-1-1-1-2z" class="c"></path><path d="M287 66l4-16v6l-1 7v-1c0 2 0 4-1 6v-3h-1v1h-1z" class="o"></path><path d="M288 65c0-1 1-4 2-4v1c0 2 0 4-1 6v-3h-1z" class="N"></path><defs><linearGradient id="A" x1="437.527" y1="417.973" x2="443.385" y2="421.581" xlink:href="#B"><stop offset="0" stop-color="#453f3b"></stop><stop offset="1" stop-color="#5e5449"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M439 412c2 4 4 9 3 14h-1c0-2-1-3-1-4 0-2 0-4-1-5v-5z"></path><path d="M451 447l1 2-6 3c-1 1-3 2-4 2l-5 1-1-1 4-1c3-1 7-3 10-5l1-1z" class="W"></path><path d="M476 357c1 0 1 1 1 2 1-1 2-2 3-1-1 1-3 3-4 3h0-1c-1 1-3 1-4 1 0-1 1-1 1-2l1-1 1-1h2 0v-1h0z" class="F"></path><path d="M473 359l1-1h2 0 0c-1 1-2 2-4 2h0l1-1z" class="g"></path><path d="M448 198l1-1c2 2 5 4 7 7 2 2 3 5 3 8v1c-2-7-5-11-11-15z" class="C"></path><path d="M426 480s-1-1-1-2c0-3 0-10 2-12h0v1c-1 5 0 8 2 12-1 1-2 1-3 1z" class="X"></path><path d="M429 333h3 0l1 2h2v1c0 1 0 1-1 2l-4 3c1-2 2-4 4-6-4 0-6 0-9 2 1-1 3-2 3-3l1-1z" class="d"></path><path d="M436 493c1 1 1 2 2 2-1 6-1 9-5 13-1 0-2 1-4 1 2-1 4-3 5-5 3-3 2-6 1-10 1 0 1-1 1-1z" class="e"></path><path d="M244 572h2s1 1 1 2h2s1 1 1 2l1-1c2 1 2 2 3 3l1 1 1 2-1-2c-2 1-3-1-4-2l-1 1-4-3c-1-1-1-2-2-3z" class="G"></path><path d="M246 575l1-1c1 1 2 1 3 2l1 1-1 1-4-3z" class="D"></path><path d="M291 578h3c0 1 1 1 1 2s-2 2-2 3v2c-1-1-2-1-3-2 0-1-1-2-1-3l2-2z" class="w"></path><path d="M392 176l12 6c-1 0-1 0-2 1-2 1-5-2-6-3s-2-1-4-2v-2z" class="J"></path><path d="M379 309h1v5h-1 0v-1l-3 3-1-1v-3s1-1 2-1c0-1 1-2 2-2z" class="AM"></path><path d="M379 309h1v5h-1 0v-1h-1v-1c0-1 0-2 1-2v-1h0z" class="AH"></path><path d="M477 455c3-2 6-3 9-6 1-1 3-3 4-5-2 5-6 10-11 12h-1c0-1 0-1-1-1z" class="n"></path><path d="M244 615l1 1c-8 3-16 3-24 3 2-2 7-1 9-1 5 0 9-1 14-3z" class="J"></path><path d="M309 509l5 1h3l8 1c-2 0-3 1-5 1-3-1-7 0-11-1l1-1v1c0-1 0-1 1-1h0c-1 0-1-1-2-1h0 0z" class="AI"></path><path d="M410 185h2 0l6 9h1l1 1v1 1h0c-1-2-2-2-3-3h0c-3-2-4-5-6-7-1-1-1-1-1-2z" class="u"></path><path d="M285 549c2 2 5 4 7 6l1-1c0 1 0 1 1 2 0 1-1 2-2 3l-4-5c0-1-3-4-4-5h1z" class="Y"></path><path d="M454 367l1 2 1 2c0 2 1 5 0 7v2l-1-2h1c0-2 0-5-1-7v4h1v1c0 1-1 1-1 1l-1-1c0-1 0-2-1-3 0-1 0-1-1-2v-2h1c1-1 1 0 1-2z" class="E"></path><path d="M453 373v-1l1-1h0c0 1 0 2 1 3v3l-1-1c0-1 0-2-1-3z" class="V"></path><path d="M319 313v-1-1c0-1 0-1-1-2h-1l-1-1c-3-1-8 2-10 0v-1h4v-2h1v2c2 0 5 0 7 1 1 0 2 1 2 1v3c0 1 0 1-1 2v-1z" class="I"></path><path d="M452 449l1-1c-2 4-8 6-11 9l-1-1h0l1-2c1 0 3-1 4-2l6-3z" class="l"></path><path d="M368 500l-2-5c2 2 3 4 4 6-1 1-1 2-1 4h0l1 1h1v1c-1 1-1 2-2 3h0l-1 2c-1-1-1-1-1-2v-4c1-2 2-3 1-6z" class="P"></path><path d="M369 505l1 1h1v1c-1 1-1 2-2 3h0-1c0-1 0-1 1-1v-1-3z" class="x"></path><path d="M177 463l5 5 1 2-1 2-2-1h0c-1-2-3-5-4-7h0c1 0 1-1 1-1z" class="m"></path><path d="M450 352h2l1 3c0 2 0 4 1 6l-1 1h-1c-1 0-1-1-1-2v-2c-1-1-1-2-1-4v-2z" class="B"></path><path d="M451 358c1 1 1 1 1 2h-1v-2h0z" class="R"></path><path d="M450 354h1v4h0c-1-1-1-2-1-4z" class="I"></path><path d="M214 612c5 1 9 1 14 1l-1 1h2c-6 1-11 1-16-1l1-1z" class="p"></path><path d="M297 591v1l-1 2c-2 2-3 4-4 6-1-2-2-4-4-6h1 2 2c2-1 3-1 4-3z" class="AN"></path><path d="M162 137c2 0 3 2 4 3-1 2 0 2 0 3s1 2 1 2h1l1 3v4c0 1 0 1-1 2h-1c0-2 1-5 0-7-1-3-5-6-5-10z" class="O"></path><path d="M259 89c2 1 5 1 7 3 1 2 2 4 2 6 0 1-1 1-2 2v1h-1c0-2 1-5 0-7s-4-3-6-4v-1z" class="s"></path><path d="M198 172c8-2 16-3 24-4-4 2-9 2-13 3-2 0-4 1-7 2v-1h1c1 0 1 0 1-1h-1c-2 1-2 1-4 1h-1z" class="v"></path><path d="M271 103l8-14c0 1 0 2 1 3l-7 11h-1-1z" class="q"></path><path d="M79 197c0 2 0 3 1 5v6c0 4 2 8 2 11-1-2-3-5-3-8-1-4-1-10 0-14z" class="i"></path><path d="M432 402c0-1 0-4 1-5v2h1v-1h0l1 2h0l-3 11-1-1 2-6v-1h0l-1-1z" class="X"></path><path d="M432 402c0-1 0-4 1-5v2h1l-1 5v-1h0l-1-1z" class="E"></path><path d="M282 596l3 3c1 2 3 6 5 8 0 1 1 2 2 2h0c2-2 4-4 5-7h1c-2 3-4 7-6 11l-10-17z" class="AT"></path><path d="M446 333v1h1 1v1c-1 0-1 0-2 1 0 0-2 0-2 1h-1l-1 2-1-1h1v-1h-1-1c-1 1-2 1-3 1l-1-1 1-1h4s1 0 1-1c0 0-1 0-1-1l2-1h0c1 1 1 1 2 1l1-1z" class="N"></path><path d="M409 470c0 2-1 4-1 6v1h1c1-1 2-3 3-4l3-3h1l-2 2c-2 2-3 6-6 7-1 0-2 0-2-1-1 0-1 0-1-1v-1h1s1 0 1-1v-1c1-2 1-3 2-4z" class="O"></path><path d="M375 318v-1c1-1 3-2 4-3v5c0 1 0 3-1 4v-1c-1 1-1 1-2 1v-1c-1-2-1-3-1-4z" class="AH"></path><path d="M375 318l1 2h0l2-2 1 1h0c0 1 0 3-1 4v-1c-1 1-1 1-2 1v-1c-1-2-1-3-1-4z" class="AE"></path><path d="M297 645c1-2 3-4 4-7 1-2 2-6 4-7-1 6-4 11-8 16 0 1-1 2-2 3h-1c0-1 1-2 1-2 1-1 1-2 2-3z" class="o"></path><path d="M321 459l1 1c-1 3-2 8-4 11h-1l-5 7h0c0-2 2-4 3-6 3-4 4-8 6-13z" class="M"></path><path d="M435 181c4 1 7 3 11 5v2 1l-11-7h-1l1-1z" class="d"></path><path d="M87 282c-2-2-4-5-6-7h-1 0 4c3 1 5 2 7 3 1 0 2 1 3 2v1h-1c-1-2-4-3-6-4-2 0-3-1-5-2l6 6-1 1z" class="H"></path><path d="M282 596c-1 0-3-4-4-5l-4-7h0 0l2 2v1c1 1 2 1 2 2 1 1 1 2 2 3s2 3 3 3l1-1h1v1h0 0c0 1 1 3 1 4h-1l-3-3z" class="B"></path><path d="M465 203c2 3 4 6 5 10l-2 1c-1-3-3-6-5-9 0-1 1-2 2-2z" class="a"></path><path d="M321 585c1-1 2-1 4-1v1h1l-8 7c0-1 0-2-1-3l4-4z" class="C"></path><path d="M432 402l1 1h0v1l-2 6-2 5c0 1-1 1-1 2h-1l3-10c1-1 1-3 2-5z" class="Z"></path><path d="M288 65h1v3 4-1l-1 2c-1 1-1 2-2 3h-1-1c1-1 1-3 2-5 1-1 1-3 1-5h1v-1z" class="X"></path><path d="M288 65h1v3 4-1l-1 2v-7-1z" class="F"></path><path d="M427 421h1 1l-7 18c-1 0-1-1-1-1l6-17z" class="u"></path><path d="M175 125l2 4c1 2 2 4 4 5a30.44 30.44 0 0 0 8 8l-1 1v-1l-4-2-2-2-3-4-4-6-1-2h1v-1z" class="V"></path><path d="M191 589c0 4 2 9 3 13v1h-2c-2-4-2-9-3-13l1 1s1-1 1-2z" class="H"></path><path d="M331 131v1c1 0 2 0 2 1 2 3 4 7 6 10h-1c-2-3-5-5-7-8h1c0-1-1-2-2-2l1-2z" class="v"></path><path d="M143 456c3 1 6 4 9 5 1 1 3 1 5 1-1 1-2 1-3 1l-6-1c-2-1-4-3-5-6z" class="l"></path><path d="M314 329c1 6 2 10 5 16 1 1 2 3 3 4h1-1c-4-3-8-10-9-15v-1c0-1 1-1 1-2h-1c1-1 1-2 1-2z" class="t"></path><path d="M418 314h5c1 0 1 0 1 2h0l1 2c-1 0-1 1-2 1v1c-1-1-2-1-3-1-1-1-2-1-3-1h1 1l1-1c-1 0-1-1-2-1v-2z" class="B"></path><path d="M418 314h5c1 0 1 0 1 2-1-1-3-1-4-2l-2 2v-2z" class="K"></path><path d="M295 602h2c-1 3-3 5-5 7h0c-1 0-2-1-2-2v-2c1 0 1-1 1-2 1 0 2-1 2-1l1 1h1v-1z" class="G"></path><path d="M431 456l4-9c1 1 3 2 3 3v1 1h-2l-3 2h1 2c1-1 1-1 2-1h2l-4 1c-1 1-3 2-5 2z" class="L"></path><path d="M425 328l2 1h1v2l1 2-1 1c-1-2-3-1-5-2v-1c-1 0-2 0-4-1l1-1h4 1v-1z" class="Y"></path><path d="M425 328l2 1h1v2c-2 0-3-1-5-1v-1h2v-1z" class="W"></path><path d="M213 613c-7-2-12-6-15-13 5 5 10 10 16 12l-1 1z" class="Q"></path><path d="M430 481s1 0 1-1c3 3 7 9 7 13v2c-1 0-1-1-2-2-1-3-2-6-4-9-1-1-1-2-2-3z" class="k"></path><path d="M382 481c1-2 1-3 1-5 0-5-6-7-8-11-1-1-2-3-2-4 1 1 2 3 3 4 3 4 7 5 9 9 0 3-1 5-2 7h-1z" class="g"></path><path d="M322 302c0 2 0 3-1 5h0l-3-3c-1-1 0-2 1-4v-1h3v2 1z" class="H"></path><path d="M322 302c-1 1-1 1-2 0 1-1 1-1 2-1v1z" class="Z"></path><path d="M240 566c1 0 2 1 3 1l3 1v1c-1 0-1-1-2 0h0l1 1c1 0 2 1 3 2s2 1 2 2 1 1 1 1l-1 1c0-1-1-2-1-2h-2c0-1-1-2-1-2h-2 0c-1-1-2-2-3-4-1 0-2 0-2-1h1v-1z" class="E"></path><path d="M241 568c2 0 7 4 9 6 0 1 1 1 1 1l-1 1c0-1-1-2-1-2h-2c0-1-1-2-1-2h-2 0c-1-1-2-2-3-4z" class="V"></path><path d="M495 220v2l-4 10c-2 4-5 7-7 10h-2l3-3c1-2 2-3 3-5 3-4 4-9 7-14z" class="p"></path><path d="M200 193v-3c0-1 2-2 2-4 0 0 0-1 1-2s1-1 3-2v2h-2v2 1l1 2h0l-1-1h-1c-1 2 0 4-2 6v2h0l-1-3zm85 406h1c0-1-1-3-1-4h0 0l5 6c0 1 1 2 1 2 0 1 0 2-1 2v2c-2-2-4-6-5-8z" class="L"></path><path d="M104 359l1-1h0c1 1 2 2 3 2v-1c2 1 3 2 4 2h1 1v3h0c0 1-1 1-1 2h-1c-1-1-2-2-2-4h-1c-1 0-3-2-5-3z" class="Q"></path><path d="M110 362h2c1 1 1 2 1 3v1h-1c-1-1-2-2-2-4z" class="W"></path><path d="M414 455c1 3-3 6-4 9-1 2-3 4-3 6 0 0 0 1-1 1h-1c0-1 1-3 2-4l-1-1c1-2 3-4 4-6l4-5z" class="O"></path><path d="M353 300h1v2 7h0-1c-1 2-1 4-1 6h-1l1-1v-2c-1 0-1 1-1 1 0 2-1 4-2 5l-1 1c2-6 4-13 5-19z" class="AF"></path><path d="M187 157l1-1v1s-1 1-1 2h0c2-1 5-1 7-2h1c1 0 2 0 2 1h0c-1-1-2-1-3 0-1 0-1 1-2 1h-1 0-2c0 1-2 0-3 0h0-2c1 0 1 0 1 1v1 1c2 1 5 0 6 1h2l1 1c1-1 1-2 3-2l-2-2c0-1 0-1 1-2h0v2c1 0 1 1 2 1-1 1 0 1-1 1-1 1-2 1-3 2v1c-1-1-1-1-1-2l-1 1c-2-1-6-1-8-2 0 0 0-1 1-1v-1h-1c-1 1-1 1-2 1v-1s1-2 2-2 2 0 2-1h1z" class="T"></path><path d="M254 578c1 0 3-1 3-1l3 3c1 2 3 4 4 6-1 0-2-1-3-1-2-1-4-3-5-4l-1-2-1-1z" class="L"></path><path d="M317 589c1 1 1 2 1 3-2 2-4 3-5 6h-1c-2 5-3 9-4 14h-1c1-9 5-16 10-23z" class="G"></path><path d="M227 624c-3 1-16-5-18-7h0l2 1h1 0c4 1 7 2 11 3 1 1 3 1 4 2h0 1l-1 1h0 0z" class="S"></path><path d="M373 617h2v1c-5 2-10 5-15 5 1 0 2-1 3-1h0-4l8-2v-1-1l3-1h2 1z" class="M"></path><path d="M372 617h1l-6 3v-1-1l3-1h2z" class="E"></path><path d="M445 356c-2-1-4-3-5-5 2 1 3 2 4 3h0v-2-3l-1-1v-2c1 1 1 1 1 2l1 1h2c0 2 0 5 1 7h-2-1z" class="K"></path><path d="M490 210c-1-5-3-11-7-15-1-2-4-4-6-6h1 1c6 5 11 12 12 20l-1 1z" class="a"></path><path d="M198 172h1c2 0 2 0 4-1h1c0 1 0 1-1 1h-1v1l-7 2c-1 1-2 2-3 2-2-1-3 0-5-1l10-4h1z" class="n"></path><path d="M412 185h3l3 1c1 1 1 2 1 3v5h-1l-6-9z" class="AN"></path><path d="M431 456c2 0 4-1 5-2l1 1 5-1-1 2-9 3c-1 0-3 0-3 1l-1-1 3-3z" class="h"></path><path d="M453 163c5 2 9 5 13 9-1 1-1 2-1 2l-1 1c0-1 0-2-1-3v-2h-1l-1 1h-2 1c-1-4-5-4-6-6 0-1 0-2-1-2z" class="n"></path><path d="M421 438s0 1 1 1c-2 4-4 7-6 11 0 1-1 3-2 4 0 0-1 0-1-1l1-2h-1l1-3h0 1 0 0l6-10z" class="Y"></path><path d="M414 448h1 0 0l-1 3h-1l1-3h0z" class="L"></path><path d="M429 479l1 2c1 1 1 2 2 3 2 3 3 6 4 9 0 0 0 1-1 1-2-5-6-9-9-14 1 0 2 0 3-1z" class="j"></path><path d="M402 188c2 1 6 3 8 5h0c1 2 2 3 4 4l-1 1h-1c-2-2-4-3-6-5-1-1-2-2-3-2v-1h0l-1-1v-1z" class="B"></path><path d="M479 412c2 2 7 5 11 5l4-1c-3 2-6 3-10 2-2-1-6-2-8-4h2 1l-1-1 1-1z" class="q"></path><path d="M225 582c3 0 7 0 10 2-1 0-3-1-4-1h-5c-5 1-8 3-11 7-1 1-2 4-1 6v2c-1 0-1 0-1-1 0-3 0-6 2-8 2-4 6-6 10-7z" class="d"></path><path d="M183 507c2 2 3 4 5 5h1c1 1 5 4 5 4v1s0 1 1 2v2l-8-8c-1-2-3-3-4-6z" class="G"></path><path d="M236 610c3-2 6-5 7-8 1-1 1-2 2-3 0 4 0 7-2 9-2 1-3 3-5 4h-2-2c1-1 2-1 2-2z" class="i"></path><path d="M420 323h0v1l-5 3-1 1c-3 2-6 5-9 7 1-2 5-6 6-8v-2h1l1 1c2-1 3-2 4-2 1-1 2-1 3-1z" class="W"></path><path d="M200 163c3-1 6-3 10-3 2-1 6-4 8-3l-6 3-9 5-1-1h-1c-1 1-3 1-4 2l3-3z" class="S"></path><path d="M422 547h0c0-4-3-8-6-10-2-2-5-3-8-4h1c3 0 6 1 9 3 2 1 3 3 3 4h1c0 2 1 6 0 8v-1z" class="V"></path><path d="M82 198h1s0-1 1-1v3h0c0 2 0 6 1 8-1 2-1 3 0 5h-2v-2-1c0-1 0-3-2-4v-2-2l1-4zm396 34c1-1 2-3 3-4 0-2 1-3 1-5v-12h-1c0-1 1-1 1-2 1 8 3 15-2 23-1 2-3 3-5 4 1-1 2-3 3-4z" class="i"></path><path d="M450 333c1 0 2 0 3 1l1 1c1 1 2 1 3 2l-1 2c-1-1-1-2-2-2h0c-1 0-1 1-1 1l-1 1v1h0c0 1 0 1-1 1l-2-2 2-2-1-1c-1 0-1 0-2-1v-1c1 0 1 0 2-1z" class="k"></path><path d="M449 339l2-2c0 1 1 2 1 3h0c0 1 0 1-1 1l-2-2z" class="b"></path><path d="M450 333c1 0 2 0 3 1l1 1c-1 0-2-1-3-1 0 1 1 1 1 2h-2c-1 0-1 0-2-1v-1c1 0 1 0 2-1z" class="G"></path><path d="M291 50V36c0-3 0-6 1-8l1 20v4c0-1-1-2-1-3v-1h0c0 2-1 3-1 4v4-6z" class="z"></path><defs><linearGradient id="C" x1="203.227" y1="508.256" x2="206.376" y2="501.652" xlink:href="#B"><stop offset="0" stop-color="#473f3c"></stop><stop offset="1" stop-color="#625b4d"></stop></linearGradient></defs><path fill="url(#C)" d="M201 499c3 2 5 6 8 9 1 2 2 3 3 5h-1l-3-1c-1-2-3-5-4-7l-1-1c-1-2-2-3-3-4v-1h1z"></path><path d="M290 583c1 1 2 1 3 2v-2 1c1 1 1 1 2 1l1 1-3 4-1 1c-2-1-2-2-3-3v-2c0-1 1-2 1-3h0z" class="v"></path><path d="M289 588h1c0-1 0-2 1-2h3l-1 1c-1 0-1 0-2 1 0 1 1 1 1 2h1l-1 1c-2-1-2-2-3-3z" class="i"></path><path d="M422 547v1c0 1 0 3-1 5-2 3-4 5-7 6s-6 0-9-1h0c-3-2-5-4-6-7 2 2 4 5 6 6 4 1 8 2 11 0 4-2 5-6 6-10z" class="R"></path><path d="M200 179c2-2 4-2 6-4 1 0 1 1 2 1 1-1 3-2 5-2-2 2-5 2-6 5h0c1 1 1 1 1 2h-1v-3c-1 0-2 1-2 1-2 1-4 3-5 3-1-1 0-2 0-3z" class="V"></path><path d="M109 408v2l-2 3-5 4c-1 1-2 1-3 1h-3c-2 0-4-1-5-2h2c1 1 1 1 2 1 6-1 10-4 14-9z" class="n"></path><path d="M390 554l2 5c0-3-1-6-1-9 0-2 0-4-1-5-1-2-2-3-3-5 1 1 4 3 4 5 1 1 1 3 1 5 1 4 1 8 2 12v1l-1-1s-1 0-1-1 0-1-1-2l-1 1s-1-5-2-5c1-1 1-1 2-1z" class="N"></path><path d="M182 91c6-4 13-5 20-4-3 1-6 0-9 1-2 1-4 3-6 4l-1-1c-1 0-3 1-4 2h-1c0-1 1-1 1-2z" class="W"></path><path d="M95 288h0c0 1 1 1 1 2 1 1 1 2 2 3l1-1 1 1c0 1 0 1-1 2l2 1c0 1 1 2 2 2l1 2-2 1-2-2-3-3c-1-1-1-3-2-4v-1c-1-1-1-2 0-3z" class="p"></path><path d="M277 580h0c-1-1-3-4-3-5-1-1 0-3 0-3v-1c1 1 1 1 2 1 2 1 5 4 7 6l-1 1h-1c0-1-1-2-2-2-1-1-1-1-2-1v1 3z" class="M"></path><path d="M382 555c1 1 3 2 4 3 2 2 3 4 5 5 0 2 2 3 3 5 1 4 1 8 1 12h-1c0-4-1-8-2-11-2-5-5-10-10-12v-2z" class="g"></path><path d="M475 344c1-3 4-7 7-8l-4 4c-1 2-1 4-1 6 3 3 6 5 8 9 1 1 1 3 1 4v1l-1-2c-1-2-2-4-4-6-1-2-2-3-4-4v5-6c-1-1 0-1-1-3h-1 0z" class="b"></path><path d="M437 211c3 1 6 4 9 6 1 1 3 1 4 2h0-3v1c-2 0-3-2-4-2l-3-3h-1v1l-1 1v3-6h-1v-3z" class="J"></path><path d="M438 214s0-1 1-1c2 1 6 5 8 7-2 0-3-2-4-2l-3-3h-1v1l-1 1v3-6z" class="U"></path><path d="M112 341c0 1 0 2 1 3h0l1 1v2c-1 0-1 0-2 1 0 0-1 1-1 2-1 1-1 2-1 3l-1-1v-1h0l-1-9 1 1v1c2-1 2-2 3-3z" class="m"></path><path d="M112 341c0 1 0 2 1 3h0c-1 0-1 0-2 1h-1l-1-1c2-1 2-2 3-3z" class="l"></path><path d="M109 351c0-1 0-3 1-4s2-2 4-2v2c-1 0-1 0-2 1 0 0-1 1-1 2-1 1-1 2-1 3l-1-1v-1z" class="x"></path><path d="M109 352v-1l1-1v-2h2s-1 1-1 2c-1 1-1 2-1 3l-1-1z" class="i"></path><path d="M329 131c1-1 1-3 2-4 2-3 5-7 9-7h3c-2 0-3 1-5 2-3 1-4 3-5 6v1 4c0-1-1-1-2-1v-1l-1 2-1-1v-1z" class="n"></path><path d="M332 130c0-1 0 0 0 0h1v-1 4c0-1-1-1-2-1v-1-1h1z" class="s"></path><path d="M332 130v1l-1 1v-1-1h1z" class="j"></path><path d="M82 187c3-5 5-10 11-12h1c-2 1-3 2-5 3-2 3-3 7-5 10l-1 3v3l-2-1c1-1 1-2 1-4v-2z" class="r"></path><path d="M82 189c1 0 1-1 2-2v1l-1 3v3l-2-1c1-1 1-2 1-4z" class="z"></path><path d="M128 423l1 1c0 1-1 2-1 4l-2 1-2 2h0c-1 1-2 1-3 2h-3c3-4 7-7 10-10z" class="g"></path><path d="M428 191c6 2 14 5 18 9h-1 0c-1-1-2-2-3-2 0 0-1 1 0 2h0v1h-1l1 1 1-1h0l2-1v1c-1 0-2 1-2 1s0 1-1 1l-1-2c-4-4-6-7-12-8h0c-1 0-2 0-2-1l1-1z" class="f"></path><path d="M261 585c1 0 2 1 3 1 4 5 9 12 11 18h-1c-2-8-8-12-14-17 1-1 1-1 1-2z" class="J"></path><path d="M344 562c0 1 0 1 1 1-1 1-3 2-3 3l-2 2h1 3l-1 2h0c-2-1-2-1-4-1l-3 3-1-1 1-1h-2-1l11-8z" class="L"></path><path d="M336 570c1-1 1-1 3-2v1l-3 3-1-1 1-1z" class="T"></path><path d="M282 84s0 1 1 2h0 1v1l-1 3c-1 1-2 3-2 4l-7 9h-1l7-11c-1-1-1-2-1-3l3-5z" class="B"></path><path d="M283 86h1v1l-1 3c0-1 0-2-1-3l1-1h0z" class="C"></path><path d="M282 84s0 1 1 2l-1 1c-1 2-1 3-2 5-1-1-1-2-1-3l3-5z" class="g"></path><path d="M415 111h1l1-1 1 7-1 1v2l-1-1v1 1l-1-1v-1c0-1-1-2-2-3v1h-1-1c1-2 1-3 1-4h0l1-1c1 0 1 0 1 1v-1c1 0 1 0 1-1h0z" class="P"></path><path d="M415 111h1l1-1 1 7-1 1c0-2-1-4-2-7z" class="t"></path><path d="M412 113l1-1c1 0 1 0 1 1 1 2 2 4 1 6 0-1-1-2-2-3v1h-1-1c1-2 1-3 1-4h0z" class="W"></path><path d="M451 344c1 1 1 1 2 1l1 3c1 1 1 2 1 3h0c0 1 1 3 0 4h-1 0-1l-1-3h-2v-1c0-1 0-2 1-3v-3-1z" class="E"></path><path d="M452 351c1 1 2 2 2 4h-1l-1-3v-1z" class="J"></path><path d="M451 344c1 1 1 1 2 1l1 3h0-1c0-1 0-2-1-2l-1-1v-1z" class="L"></path><path d="M451 348l1 3v1h-2v-1c0-1 0-2 1-3z" class="M"></path><path d="M343 475h1c0 1 0 2-1 3h0c-3 5-9 7-13 10-2 1-3 3-5 5l-6 3v-1c0-1 0-1 1-2h3c1-1 3-3 5-4 2-2 5-4 8-6l1-1c2-1 5-5 6-7z" class="E"></path><path d="M177 498c0-1-1-3-1-4v-1l8 11c1 3 3 6 5 8h-1c-2-1-3-3-5-5-2-3-4-5-6-9z" class="F"></path><path d="M359 382h0 3v1c-1 0-2 1-3 1v1h0c2 0 3 0 4 1h3l8 1c1 0 3 0 4 1h-3-1 0l-1 1c-2 0-3 0-4-1l-1 1-9-4v-3z" class="H"></path><path d="M369 388h2 1 2l-1 1c-2 0-3 0-4-1z" class="E"></path><path d="M331 525c-1 3-14 14-17 16l-2 1h-1c1-1 1-1 1-2h0c1-1 4-3 5-4l14-11zm92-201v1h0v1h1l1 1v1 1h-1-4l-1 1-5-2 1-1 5-3 1 1 2-1z" class="R"></path><path d="M423 326h1l1 1-1 1-1-1v-1z" class="G"></path><path d="M414 328l1-1 2 1 7 1h-4l-1 1-5-2z" class="b"></path><path d="M423 324v1h0l-2 1c-1 1-1 1-2 1 0 1-1 1-2 1l-2-1 5-3 1 1 2-1z" class="f"></path><path d="M423 324v1h0l-2 1h-1l1-1 2-1z" class="B"></path><path d="M277 580v-3-1c1 0 1 0 2 1 1 0 2 1 2 2v4c2 2 3 4 4 6l4 5h-1c-3-2-4-5-6-8l-5-6z" class="G"></path><path d="M310 583l1 1c-3 4-6 9-9 13-1 1-3 5-4 5h-1-2c1-2 4-3 5-5 4-5 7-9 10-14z" class="D"></path><path d="M365 542l1 2-21 19c-1 0-1 0-1-1l10-9a57.31 57.31 0 0 0 11-11z" class="O"></path><path d="M387 355l1 1-6 9c-1 2-3 3-4 5v2c-1 2-4 4-6 5h-1c3-4 6-7 9-11 3-3 5-7 7-11z" class="k"></path><path d="M249 590c-2-1-2-4-3-6-2-3-3-5-5-7-1-2-4-4-5-6-2-1-2-3-4-4-2-3-4-5-6-8l4 3c2 2 6 5 7 8 1 2 3 5 5 7 2 1 3 3 5 4l1 1h0c-2 0-2-1-3-2h-1 0l5 9v1h0z" class="H"></path><path d="M448 198c-5-3-9-6-14-8-2-1-4-1-5-1h1 1 2c-2-1-3-1-4-1 2 0 6 0 7 1h1s1 0 1 1l2 1 1 1h1c1 1 3 2 5 3v-1c-1-1-3-2-4-3h0v-1h0c1 1 2 1 3 2 0 1 1 1 1 1 1 1 0 1 0 2 1 0 1 1 2 2l-1 1z" class="U"></path><path d="M388 389h0 1c2 0 4 1 6 1h1v1c-1 2-1 2-2 3h2v1c-4 1-8 0-11-1l1-1 5 1-2-3c-1-1-1-1-1-2z" class="Q"></path><path d="M389 389c2 0 4 1 6 1h1v1c-1 2-1 2-2 3-2-1-4-3-5-5z" class="Ac"></path><path d="M441 456h0l1 1c-1 1-3 4-4 4h-3l-8 2v-1h-1l2-1 1-1c0-1 2-1 3-1l9-3z" class="K"></path><path d="M435 461c2-2 4-4 6-5l1 1c-1 1-3 4-4 4h-3z" class="Y"></path><path d="M115 181l1 1v2h0c1 0 1-1 2-1v-1l1 1c0 2-1 4-2 5l2 1c-2 0-6 2-8 3v-1h1c0-1 0-2 1-3v-2l1-3s0-1 1-2z" class="T"></path><path d="M115 181l1 1v2h0c1 0 1-1 2-1v-1l1 1c0 2-1 4-2 5h-1c0 1 0 1-1 1h0v-1-1-4h-1s0-1 1-2z" class="e"></path><path d="M394 188h1c1-1 2-1 3-1v3l-4 4h-1l-1 2c-1 0-3 0-4-1l2-3 1-1 3-3z" class="S"></path><path d="M391 191c1 1 1 2 2 2l-2 1-1-2 1-1z" class="G"></path><path d="M394 188l1 1v2c-1 0-1 1-1 2h-1c-1 0-1-1-2-2l3-3z" class="e"></path><path d="M288 649c2 0 3 2 4 3l2-2h1c-2 3-3 9-3 12 0 2 1 5 0 7v8c-1-2-1-5-1-7l1-1c-1-7 0-13-4-20z" class="w"></path><path d="M405 155h-2v1c1 0 2 1 2 2h0c-1 0-1 0-2-1l-1 2c-1 0-1-1-2-1s-2 0-3-1-1-1-2-1h-1-1c0-1 0-2 1-3h0c1 1 2 2 4 2l1 1h1c-1-1-1-2-1-3h0 1c2 0 3 0 4 1 1 0 1-1 2-1 0 1 0 1 1 1v1h-2z" class="B"></path><path d="M472 184l1 2v3s1 2 0 3c2 3 5 6 6 10l-1-1-3-3v-1c-1-2-3-4-4-5-1 0-1 0-2-1l-2-1h1 0l2-4 1-1h0l1-1z" class="u"></path><path d="M472 184l1 2v3h-1c0 1 0 2 1 3-2 0-4-2-5-2h0l2-4 1-1h0l1-1z" class="H"></path><path d="M472 184l1 2c-1 0-1 1-2 1v-2h0l1-1z" class="b"></path><path d="M473 186v3h-1l-1-2c1 0 1-1 2-1z" class="m"></path><path d="M82 187v2c0 2 0 3-1 4l2 1-1 4-1 4v2h0v6 2c0-2 0-2-1-4v-6c-1-2-1-3-1-5 1-4 1-7 3-10z" class="X"></path><path d="M81 202v-1c-1-1 0-6 0-8l2 1-1 4-1 4z" class="r"></path><path d="M418 128c0-1 0-2 1-2v1c1 3 1 6 0 9 1 0 1 1 1 2l-1 2c-1 1-2 2-2 3-1 1-2 2-4 2h0l3-4-1-1c2-3 3-4 3-8v-4z" class="T"></path><path d="M419 136c1 0 1 1 1 2l-1 2c-1 1-2 2-2 3-1 1-2 2-4 2h0l3-4c2-2 3-3 3-5z" class="N"></path><path d="M261 614l1-1 1 1c-1 1-1 2-2 3l1 1c-4 5-9 7-15 8 0-1 0-1-1-2l4-1c5-1 9-4 11-9z" class="Y"></path><path d="M485 289c1 1 1 1 2 1h1 0v1c0 1-1 2-1 4l-1 1c-1 0-1-1-2-1 0 2-1 2-2 3l-1 1c-1 0-2 1-3 2h0l-2 2h0c-1-1-1-1-2-1 2-1 5-4 6-6l2-1c1-2 2-3 3-6z" class="j"></path><path d="M487 290h1 0v1c0 1-1 2-1 4-1-1-2-2-2-3l2-2z" class="b"></path><path d="M452 428c1 2 1 4 0 7v2c-1 2-2 5-4 7h-2-1c1-2 3-4 4-6l1-4v-4h1s0-1 1-2z" class="L"></path><path d="M450 434v-4h1c0 1 1 2 1 2 0 1-1 4-1 4 0 1-1 2-1 2h-1l1-4z" class="I"></path><path d="M111 458c-3-1-6-1-8-3-4-2-8-6-9-10 5 6 10 10 17 11h6 2 0c-1 1-2 0-3 1 0 0-1 0-1 1h-4z" class="j"></path><path d="M148 456c2 0 5 2 6 1 1 1 2 2 3 4 1 0 2 0 2 1l1 1-3-1c-2 0-4 0-5-1-3-1-6-4-9-5h-1c2 0 3 1 5 1l1-1z" class="B"></path><path d="M148 456c2 0 5 2 6 1 1 1 2 2 3 4-1 0-2-2-3-2-3 0-5-1-7-2l1-1z" class="a"></path><path d="M166 162c2 0 4 1 6 1 7 1 13 4 19 6 1 1 3 1 5 2l-1 1c-4-1-7-3-10-4s-6-2-9-2l-6-2h-2c-1 0-2 0-4-1l2-1z" class="Q"></path><path d="M469 289l4-4s0 1-1 2-2 3-3 5c1 0 1 0 1 1-1 1-3 3-4 5l-1 3c-1 0-1 0-2-1v2c1 1 2 1 3 1h0l-2 1-1-1c-1-2 1-5 1-7 2-2 3-5 5-7z" class="B"></path><path d="M480 326c4 0 7-2 11-2 4-1 8 0 11 1 2 1 4 2 5 3 3 3 6 6 6 10h0c-3-6-6-11-13-13-5-1-11 0-16 1-1 1-2 1-3 2v1h-1v-3z" class="G"></path><path d="M406 314l-2-1c-4 0-8-3-12-4 5 0 9 0 13 1h3 3 0v1 1h-2c-1 0-1-1-2-1l-1 1v2h0z" class="H"></path><path d="M411 310h0v1 1h-2c-1 0-1-1-2-1l-1 1c-1-1-2-1-3-2h0 2 3 3z" class="G"></path><path d="M282 638l6 8h0c1 0 2 0 2 1h2l1 1c1-1 2-3 4-3-1 1-1 2-2 3 0 0-1 1-1 2l-2 2c-1-1-2-3-4-3-1 0-2-2-2-3-1-2-3-3-3-5l-2-3h1z" class="J"></path><path d="M173 104c1-1 1-1 2-1l-1 7-1 3v1l-1-2c-1 1-1 3-1 4v-3c-1 0-1 4-2 5v1l-1-1c0-1 0-2 1-4h-1c0-1 0-1-1-1l1-2h1c1-3 3-5 4-8v1z" class="d"></path><path d="M168 111h1v3h-1c0-1 0-1-1-1l1-2z" class="u"></path><path d="M173 104c1-1 1-1 2-1l-1 7-1 3v1l-1-2c0-3 1-5 1-8z" class="o"></path><path d="M183 500h0v-2l1 1h0c2 1 3 3 4 5 0 3 1 4 3 6 0 1 0 2 1 3h0c2 1 2 2 3 4v2c-1-1-1-2-1-2v-1l-5-4c-2-2-4-5-5-8h0c0-2-1-2-1-4z" class="S"></path><path d="M406 340v1h0c-1 1-1 2-1 4-2 2-5 5-8 7h0-1 0l1-1c-1-4 2-4 2-6l-1-1v-1h1v1c1 0 1 0 1 1h0l2-2h0l3-3h1z" class="O"></path><path d="M399 343v1c1 0 1 0 1 1h0l2-2h0l3-3-8 11c-1-4 2-4 2-6l-1-1v-1h1z" class="T"></path><path d="M320 614v-3h2c0 4 3 7 6 10l6 3c-1 0-1 0-1 1-3 0-6-2-8-3-3-2-4-4-5-8z" class="h"></path><path d="M446 418c1 0 2 1 3 2l3 3v-1-1h1l2 2h1c1 2 4 4 6 5 0 2 0 2-1 3l-4-2c-4-4-8-7-11-11z" class="y"></path><path d="M452 422v-1h1l2 2 1 1v1l-1-1v1l-1-1c-1-1-1-1-2-1v-1z" class="z"></path><path d="M513 265c5 3 12 11 14 18 0 3 0 7-1 10 0-6 0-12-4-17h0c-3-4-8-6-11-9 1 0 2 1 3 2 1 0 2 1 3 1h0c0-1-1-2-2-2-1-1-1-2-2-3h0z" class="X"></path><path d="M361 554c2 0 4-3 6-3l-8 8c-5 5-9 10-14 14v-1l7-8c3-3 6-6 9-10z" class="Q"></path><path d="M434 325h2l1 2v2l2 2v1h1 2l1 1h0l-2 1c0 1 1 1 1 1 0 1-1 1-1 1h-4-2v-1h-2l-1-2c1 0 2 0 2-1 1 0 2 1 2 0h0l1-1-1-1c0-1 0-2-1-3h-1v-2z" class="m"></path><path d="M434 325h2l1 2v2c0-1-1-1-2-2h-1v-2z" class="p"></path><path d="M434 332c1 0 2 1 2 0 2 1 4 2 4 3 1 0 1 1 1 1h-4-2v-1h-2l-1-2c1 0 2 0 2-1z" class="r"></path><path d="M311 405l1 2c-3 5-7 9-10 14-1 1-2 3-3 4s-2 3-3 4h0c0-2 2-2 2-4l3-6c2-2 3-5 4-7 2-3 4-5 6-7z" class="U"></path><path d="M102 329v-1c-7-2-12-5-19-3-4 2-8 6-11 11-1 1-1 2-2 4v-1c0-1 0-2 1-4 1-3 5-7 9-9 6-3 10-3 17-1 2 1 6 2 8 1v1c-1 1-2 0-3 1h0c1 0 1 0 2 1h-2z" class="O"></path><path d="M305 601l1 1-9 15c-1 2-3 4-4 6v1 1l-2-2c0-1-1-2-1-3h1l1-1c0-1 0-1 1-2v1 3h-1 1 0c0-2 2-4 3-5 0-1 1-3 2-4 2-4 5-7 7-11z" class="N"></path><path d="M474 306h1l1 1-1 3 1 7c1 0 1 0 2-1v1c-1 0-1 1-2 2l-1-1c-1-1-2-1-2-3v-1h-1l-1-1v-1h-1v-2h0c0-1 0-1 1-1 0 0 1 0 1-1v2c1-1 1-1 1-2v-2h1z" class="c"></path><path d="M470 310h1c1 1 1 3 2 4h-1l-1-1v-1h-1v-2z" class="C"></path><path d="M476 317h-1c-1-1-1-1-1-2s0-1-1-2c1-1 1-1 1-2 1 0 1-1 1-1h0l1 7z" class="o"></path><path d="M461 225l2 2v-1c-1-1-1-3-1-5l2 2c1 0 1 0 2 1h2v-1h0l1 1c-1 1-1 2-2 3 0 2 1 5 2 7l-8-6-1-1h1 0 1c-1 0-1-1-1-2z" class="U"></path><path d="M281 94c-1 2-1 4 0 5-2 3-4 6-7 8h0c-2 0-2 0-3 1 0 1-1 2-2 3v-2c0-1 1-1 2-2l3-4 7-9z" class="X"></path><path d="M351 613c-4 0-7-2-10-5-2-3-1-7-1-10v2c0 1 1 2 1 2 2 6 8 8 13 10v1c1 0 2 0 4 1h7c1-1 2-1 2-1-1 0-1 1-1 1h-4-1-1c-1-1-4 0-5-1h-2-2z" class="i"></path><path d="M503 270c4 1 8 1 11 2 1 0 2 1 3 1v1c-7-2-15-3-23-1-3 1-5 2-8 3 1-2 2-2 3-3v-1c1-1 2-1 3-1 3-1 8-1 11-1z" class="m"></path><path d="M205 200c3-6 5-12 9-16 0 1 0 2-1 3l1 1h1c-1 2-1 2-2 3l-4 8h0l-1-1c0 1-1 3-2 3h0-1v-1z" class="R"></path><path d="M295 561c1 1 1 2 1 3-1 3-2 6-3 10l-1-1-2-1v-2c0-1-1-2-1-3s1-2 2-3c0 1 1 1 1 2l3-5z" class="X"></path><path d="M291 569l1-1h1v3l-1-1-1-1z" class="r"></path><path d="M290 570l1-1h0l1 1 1 1c-1 1-1 1-1 2l-2-1v-2z" class="j"></path><path d="M188 504c2 2 3 5 5 7 1 2 3 3 4 5 1 1 2 3 2 4s-1 2-2 3c-1 0-1-1-2-2v-2-2c-1-2-1-3-3-4h0c-1-1-1-2-1-3-2-2-3-3-3-6z" class="H"></path><path d="M413 117v-1c1 1 2 2 2 3v1l1 1v-1-1l1 1 2 6c-1 0-1 1-1 2v4l-1-1c-1-3-2-5-5-7 0-2 1-3 1-5 0 0 0-1-1-1l1-1z" class="i"></path><path d="M413 117v-1c1 1 2 2 2 3v1l1 1v-1-1l1 1 2 6c-1 0-1 1-1 2l-1-1c0-2-1-3-2-5 0-1-1-2-2-3 0 0 0-1-1-1l1-1z" class="L"></path><path d="M416 120v-1l1 1 2 6c-1 0-1 1-1 2l-1-1c1-2 0-5-1-7z" class="V"></path><path d="M428 326h3l1 1h2 1c1 1 1 2 1 3l1 1-1 1h0c0 1-1 0-2 0 0 1-1 1-2 1h0-3l-1-2v-2-1-2z" class="F"></path><path d="M432 327h2 1c1 1 1 2 1 3l-4-3z" class="h"></path><path d="M428 326h3c0 1 0 1-1 2h-1-1v-2z" class="f"></path><path d="M428 328h1l1 1c1 1 3 1 4 1l2 2h0c0 1-1 0-2 0 0 1-1 1-2 1h0-3l-1-2v-2-1z" class="n"></path><path d="M432 333v-1h2c0 1-1 1-2 1h0z" class="z"></path><path d="M186 205c2 0 6 1 7 2 1 0 2 1 3 2-1 1-1 2-1 3v1l-1-1-8-3h-2v-3h2v-1z" class="E"></path><path d="M193 207c1 0 2 1 3 2h-1c-1 0-2 0-3-1 0-1 0-1 1-1z" class="C"></path><path d="M186 205c2 0 6 1 7 2-1 0-1 0-1 1-1 0-1 0-1-1h0c-2-1-4-1-5-1h0v-1z" class="D"></path><path d="M184 206h2 0v3-1c2 0 3 1 5 2 1 0 3 1 4 2v1l-1-1-8-3h-2v-3z" class="l"></path><path d="M173 114v-1l1-3c0 1-1 3 0 4l1 3h1l1 2-2 2 1 1v2l-1 1h0v1h-1c-1 0-1-1-2-2 0-1-1-3-1-4h-1 0l1-1v-2-1c0-1 0-3 1-4l1 2z" class="v"></path><path d="M173 114v-1l1-3c0 1-1 3 0 4l1 3h1l1 2-2 2-1-4h0c-1-1-1-2-1-3z" class="u"></path><path d="M172 124c0-1-1-3-1-4h-1 0l1-1v-2l4 8h0v1h-1c-1 0-1-1-2-2z" class="B"></path><defs><linearGradient id="D" x1="326.66" y1="573.93" x2="329.016" y2="582.171" xlink:href="#B"><stop offset="0" stop-color="#1d1b1e"></stop><stop offset="1" stop-color="#46423b"></stop></linearGradient></defs><path fill="url(#D)" d="M321 585c2-4 7-9 11-13h1l1 1s0-1 1-1v-1h0l1 1c-1 0-2 1-2 2-2 1-7 5-7 7h0c2 0 2-1 4-1l-5 5h-1v-1c-2 0-3 0-4 1z"></path><path d="M243 145h1l-7 9v1h0c-1 2-3 4-4 6s-3 4-4 6c-2 1-3 3-4 5h-1l4-7-1-1 4-4c0-2 7-8 8-10l4-5z" class="C"></path><path d="M231 160h1c-1 2-2 4-4 5l-1-1 4-4z" class="F"></path><path d="M465 174c1 1 1 2 1 2h1l1 1 3 8h0l-1 1-2 4h0-1-1l1-1h0c-1-2-1-4-2-6 0-2 0-2 1-4l-1-4h-1l1-1z" class="o"></path><path d="M465 174c1 1 1 2 1 2h1l1 6c1 1 1 2 1 3l-1-1-2-5-1-4h-1l1-1z" class="v"></path><path d="M480 276c3-1 6-3 9-4v1c-1 1-2 1-3 3l-5 2c-1 2-3 3-4 4h0c-1 1-2 2-3 2l-1 1-4 4h-2c2-2 3-4 5-6l4-4 4-3z" class="b"></path><path d="M472 283c1 1 1 1 2 0s4-4 7-5c-1 2-3 3-4 4h0c-1 1-2 2-3 2l-1 1-4 4h-2c2-2 3-4 5-6z" class="p"></path><path d="M471 362c1 0 3 0 4-1h1l-1 2h-1l2 3c3 8 5 15 2 24l-1 3-1-1c2-6 3-11 2-17-1-4-2-6-4-8l-1 3c-1-1-1-4-1-5l-1-2v-1z" class="h"></path><path d="M473 364l1-1 2 3h-1c-1 0-1-1-2-2z" class="y"></path><path d="M473 364c-1 0-1-1-1-2h2l1 1h-1l-1 1z" class="W"></path><path d="M169 118c1-1 1-5 2-5v3 1 2l-1 1h0 1c0 1 1 3 1 4h-1 0c-1 1-1 1-1 2-2 1-3 3-3 5l-1 3v-1c-1-1-1-2-1-3l1-1c-1 0 0 0-1-1h0c0-4 2-7 4-9v-1z" class="U"></path><path d="M169 118c1-1 1-5 2-5v3 1 2l-1 1h0 1c0 1 1 3 1 4h-1 0c-1 1-1 1-1 2-2 1-3 3-3 5 0-2 0-4 1-6l1-1c0-2 0-4 1-5v-1h-1z" class="o"></path><path d="M142 160h6 9l1 1h1l7 1-2 1-6-1-1 1h1-1-3l1 2h0-6v-4h-2c-1 0-2-1-4-1h-1z" class="s"></path><path d="M143 160h9v1h-3-2c-1 0-2-1-4-1z" class="W"></path><path d="M152 161l2 1h2 2l-1 1h1-1-3l1 2h0-6v-4h3z" class="o"></path><path d="M154 162h2 2l-1 1h1-1-3c-2 0-4 0-5-1 1-1 3 0 5 0z" class="R"></path><path d="M156 162h2l-1 1h1-1-2v-1h1z" class="O"></path><path d="M305 337h1v-1h0c1 1 1 2 2 3v2c0 1 1 1 1 2 1 1 1 2 2 3 1 2 1 5 4 6h0c1 0 1 0 1 1l1-1h1c1 0 1-1 2-1 0-1-1-1-2-2v-1l-1 1s0-1-1-1h-1l-1-1h-2c0-1-1-2-1-3h-1v-1c2 2 2 3 4 3 1 0 1 1 2 1h0c0-1-1-3-2-4h0c-1-2-2-3-3-5 0-1-1-4-1-5l-1-1s0-1-1-2v-1h-1l1-1s2 2 2 3 1 1 1 2v1 1h1c0 1 0 2 1 3v2h0c1 1 2 2 2 3s1 1 1 2c2 2 3 6 6 6h0l-7 2h0c-3-3-5-6-6-10-2-2-3-4-4-6h0z" class="H"></path><path d="M339 569c2 0 2 0 4 1-3 3-6 5-9 8-1 1-2 2-3 2-2 0-2 1-4 1h0c0-2 5-6 7-7 0-1 1-2 2-2l3-3z" class="O"></path><path d="M409 100v-1l2 2h1 0c2 3 4 6 5 9l-1 1h-1 0c0 1 0 1-1 1v1c0-1 0-1-1-1l-1 1v-1h-1l-1-4v-4-2l-1-2z" class="e"></path><path d="M412 106c1 2 1 5 2 6v1c0-1 0-1-1-1l-1 1v-1-6z" class="n"></path><path d="M410 104c1 0 2 1 2 2v6h-1l-1-4v-4z" class="Y"></path><path d="M412 101c2 3 4 6 5 9l-1 1h-1 0v-3-1c-1 0-1 0-1-1-1-1-2-3-3-5h0 1 0z" class="a"></path><path d="M472 304v1h1l1 1h-1v2c0 1 0 1-1 2v-2c0 1-1 1-1 1-1 0-1 0-1 1h0v2c0 1 0 2-1 3h0l-2 1h0v-2h0-1l-2-1-1-1h2c-1-1-1-1-2-1l2-1 3-3s0-1 1-1c1-1 2-2 3-2z" class="Y"></path><path d="M471 309l-1-1v-1s1-1 2 0v1c0 1-1 1-1 1z" class="N"></path><path d="M465 310h2v1h-1v1h2 1c0 1-1 1-2 2h0-1l-2-1-1-1h2c-1-1-1-1-2-1l2-1z" class="x"></path><path d="M465 312l2 2h-1l-2-1-1-1h2z" class="t"></path><path d="M354 612l2 1c8 1 15 0 21-5 3-2 7-5 9-8 0 3-6 8-8 10-6 4-15 5-22 4-2 0-4 0-5-1h2 2c1 1 4 0 5 1h1 1 4s0-1 1-1c0 0-1 0-2 1h-7c-2-1-3-1-4-1v-1z" class="p"></path><path d="M374 388h1 3 3 3c1 1 3 0 4 1 0 1 0 1 1 2l2 3-5-1-8-2-2-1h-1l-2-1 1-1h0z" class="E"></path><path d="M381 388h3l-2 1h0c1 0 1 1 2 1l-5-1h0c0-1 1-1 2-1z" class="S"></path><path d="M384 388c1 1 3 0 4 1 0 1 0 1 1 2-2 0-4 0-5-1-1 0-1-1-2-1h0l2-1z" class="I"></path><path d="M374 388h1 3 3c-1 0-2 0-2 1h0 0l-3 1h-1l-2-1 1-1h0z" class="V"></path><path d="M374 388l5 1-3 1h-1l-2-1 1-1h0z" class="Ac"></path><path d="M320 577h2v1 1c-3 6-8 12-13 17l-1-1c3-7 8-12 12-18z" class="R"></path><path d="M313 106c1 1 1 1 3 2 1 1 3 4 4 6 3 4 6 9 10 12v1c-2 0-3-2-4-3-3-4-7-6-10-10h1c0-2-4-6-5-8h1z" class="l"></path><path d="M311 443c-3 2-5 8-9 10v-1c0-1 2-3 3-4 3-5 6-9 9-13 2-3 5-5 7-7 0 1-1 3-1 4-3 3-6 6-8 9 0 1-1 1-1 2z" class="U"></path><path d="M438 413h0c0 1-1 2-1 2-1 2 0 2-1 3l-1-1c0 1 1 1 1 2 2 2 1 7 0 9 0 2-1 3-1 5l1 1h0v3 2 1c0 1 0 2-1 3l-1-1v-1c1 1 0 1 1 1v-1h0v-2h0v-1h1c-1-1-1-1-1-2h0v-1c0-1-1-1-1-1 0-1 0-1-1-2l-1-1c1-1 1-2 1-3-1-2 0-5 1-7 1-1 1-1 2-1 0-2-1-1-2-3 1-1 2-3 4-4z" class="T"></path><path d="M417 349h4v1c2 0 5 3 6 4v1c-3 1-6 3-8 5 0-4-1-7-2-11z" class="y"></path><path d="M419 354c1 0 1 0 2 1v1l-1 1h0c-1-1-1-2-1-3z" class="r"></path><path d="M266 111h1l1-3 1 1v2c-1 1-3 3-4 5l1 1c-1 2-3 4-5 6-1 1-3 2-4 3l-3 3h-1v-1c2-3 5-6 7-8l6-9z" class="c"></path><path d="M205 179s1-1 2-1v3l-1 1c-2 1-2 1-3 2s-1 2-1 2c0 2-2 3-2 4v3l-3-3v-2-1l-1-1-1-1v-1l2-1h2l1-1c1 0 3-2 5-3z" class="F"></path><path d="M205 179v1 1c-1 1-3 2-4 3s-2 3-4 4v-1l-1-1-1-1v-1l2-1h2l1-1c1 0 3-2 5-3z" class="a"></path><path d="M197 183c1 0 2 1 3 0v1h0c-1 0-2 1-4 2l-1-1v-1l2-1z" class="h"></path><path d="M500 189l2 5 1 6v10l-1-1s0 1-1 2l-1 4v-1l-1 2-1-2c0-1 1-3 1-4 1-4 2-9 2-13-1-3-1-6-1-8z" class="D"></path><path d="M499 210h0c0 1 0 1 1 2v2l-1 2-1-2c0-1 1-3 1-4z" class="P"></path><path d="M501 299h1c1 0 1 0 2 1h0c1 0 2 0 3 1h0c3 2 4 4 4 7h0 0c-1-2-3-3-5-4l-1-1c-3-1-8 2-10 3-1 1-3 2-4 2s-1 0-2-1l4-1 12-5h-7l-8 2h0l-1-1h1 1c0-1 1-1 1-1l5-1h1c1-1 2-1 3-1z" class="G"></path><path d="M470 312h1v1l1 1h1v1c0 2 1 2 2 3l1 1h0-1v3s-1 0-1 1v2h-1 0v-1h0c0-1 0-2-1-2 0-1-1-1-2-1h0c-1 0-1 0-2-1l-1-2c-1 0-1-1-2-2 0-1 0-1 1-2h1 0v2h0l2-1h0c1-1 1-2 1-3z" class="i"></path><path d="M467 318v-1c1 1 2 2 3 2v1 1c-1 0-1 0-2-1l-1-2z" class="M"></path><path d="M470 312h1v1l1 1h1v1 2h-3c-1 0-1-1-1-2h0c1-1 1-2 1-3z" class="F"></path><path d="M63 275c-5 6-5 11-5 18-1-3-1-6-1-9 2-9 11-17 17-22 0 2 0 3-1 4v1c-3 3-7 5-10 8z" class="j"></path><path d="M380 184l2 1v-1c1 1 3 2 5 4 0 0 0 1 1 1l-1 2-3 3c-1 1-2 3-4 4v-3c1-2 0-4 0-6-1-2 0-3 0-4v-1z" class="H"></path><path d="M382 186l2 2c0 1 0 2-1 3h0v-1c-1-1-1-3-1-4z" class="L"></path><path d="M384 188c1 1 0 1 1 2 0 0 1 0 2 1l-3 3v-1h-1l-2-1h0l2-1h0c1-1 1-2 1-3z" class="I"></path><path d="M382 184c1 1 3 2 5 4 0 0 0 1 1 1l-1 2c-1-1-2-1-2-1-1-1 0-1-1-2l-2-2v-1-1z" class="P"></path><defs><linearGradient id="E" x1="392.727" y1="176.994" x2="393.791" y2="174.67" xlink:href="#B"><stop offset="0" stop-color="#bdb1a6"></stop><stop offset="1" stop-color="#d9d1ca"></stop></linearGradient></defs><path fill="url(#E)" d="M378 171h0 3c1 0 3 1 5 1h2l7 3c1 0 2 1 3 1s2 1 2 1l1 1c1 0 3 1 3 2h1 1c0 1 0 1 1 1v1h-2-1l-12-6c-1 0-3-1-5-1l-9-4z"></path><path d="M93 309c-1-1-11-7-12-7s-3 2-4 2c-3 1-3 2-4 4h0c0-3 1-5 4-7 1-1 4-2 6-2h0l4 1c1 1 1 0 2 1h6c-1 1-2 1-3 2-1-1-2 0-3-1h-2v-1h-1-3-1c1 2 6 3 8 5 2 1 3 1 5 1h1c-1 1-1 1-2 1l-1 1z" class="f"></path><path d="M248 560v-1c2 0 3 1 4 3s4 7 6 7v1h1l6 9 3 3c1 2 2 3 3 5-5-3-8-9-11-13-1-1-2-2-3-4 0 0 0-1-1-1h0v1l-8-10z" class="I"></path><path d="M437 327h2 0c2 0 3 1 5 1h3 0c1 0 1 1 1 1v2h2s0 1 1 1 1 1 2 2c-1-1-2-1-3-1-1 1-1 1-2 1h-1-1v-1l-1 1c-1 0-1 0-2-1h0 0l-1-1h-2-1v-1l-2-2v-2z" class="P"></path><path d="M447 328c1 0 1 1 1 1v2h-2v-3h1z" class="m"></path><path d="M442 330h1 1l-2 2h2 0l2 1-1 1c-1 0-1 0-2-1h0 0l-1-1h-2l1-1 1-1z" class="a"></path><path d="M448 331h2s0 1 1 1 1 1 2 2c-1-1-2-1-3-1-1 1-1 1-2 1h-1-1v-1l-2-1h0l-1-1h3 2z" class="T"></path><path d="M444 332c2 0 4 0 6 1-1 1-1 1-2 1h-1-1v-1l-2-1z" class="D"></path><path d="M437 327h2 0c2 0 3 1 5 1l-1 1h-2l1 1-1 1-1 1h-1v-1l-2-2v-2z" class="F"></path><path d="M437 327h2 0v1l1 1c0 1 0 1-1 2l-2-2v-2z" class="M"></path><defs><linearGradient id="F" x1="276.779" y1="545.457" x2="282.594" y2="557.059" xlink:href="#B"><stop offset="0" stop-color="#2f2c29"></stop><stop offset="1" stop-color="#564f48"></stop></linearGradient></defs><path fill="url(#F)" d="M285 559c-2-3-4-6-7-8-2-2-5-4-7-6 3 1 6 2 9 4l1-1c1 1 2 1 3 2v-1c1 1 4 4 4 5-1 0-1 0-1 1v1c-1 1-1 2-2 3z"></path><path d="M281 548c1 1 2 1 3 2v-1c1 1 4 4 4 5-1 0-1 0-1 1-1-2-5-5-7-6l1-1z" class="B"></path><path d="M397 284c1 1 2 2 2 3v1l4 4h-2v1c1 1 8 5 8 6h-1v4c-1-1-2-1-3-2-2-1-6-6-6-8l-4-5 1-1 1-3z" class="K"></path><path d="M395 288l1-1c1 0 1 1 2 2l2 3-1 1-4-5z" class="D"></path><defs><linearGradient id="G" x1="174.029" y1="102.79" x2="184.615" y2="102.443" xlink:href="#B"><stop offset="0" stop-color="#b5a9a1"></stop><stop offset="1" stop-color="#dbcac8"></stop></linearGradient></defs><path fill="url(#G)" d="M182 93c1-1 3-2 4-2l1 1-4 3c-4 5-8 12-8 18 0 0 1 0 1 1l-1-1c-1 0-1 0-1 1-1-1 0-3 0-4l1-7v-1c0-1 1-3 2-4l4-5h1z"></path><path d="M182 93c1-1 3-2 4-2l1 1-4 3c0-1 0-1 1-2v-1l-2 1z" class="r"></path><path d="M99 361c0-1 0-3 1-4 1-3 4-8 7-9v4h0l1 7v1c-1 0-2-1-3-2h0l-1 1h-2c-1 1-2 2-3 2h0z" class="Z"></path><path d="M107 352h0l1 7v1c-1 0-2-1-3-2h0l-1 1h-2c0-1 1-2 1-2 1-1 1-2 2-3 0-1 1-1 2-2z" class="E"></path><defs><linearGradient id="H" x1="170.735" y1="479.367" x2="165.518" y2="484.5" xlink:href="#B"><stop offset="0" stop-color="#2e292a"></stop><stop offset="1" stop-color="#4a463e"></stop></linearGradient></defs><path fill="url(#H)" d="M162 470l6 9c0 1 1 2 1 4l5 7c1 1 2 2 2 3v1c0 1 1 3 1 4-4-4-6-9-9-14-2-3-4-5-5-8 0-1 0-2-1-3v-3z"></path><path d="M95 277h1c1 0 1 0 1 1v2c0 2 0 4 1 6 0 2 2 6 4 8l-1 2-2-1c1-1 1-1 1-2l-1-1-1 1c-1-1-1-2-2-3 0-1-1-1-1-2h0c-3-2-5-4-8-6l1-1 3 3c1 0 1 0 1-1 1-1 3-2 4-4 0-1 0-1-1-2z" class="j"></path><path d="M95 277h1c1 0 1 0 1 1v2c0 2 0 4 1 6h-1c0 1 0 1 1 2v2h-1l-1-2v-3h1v-1c-2 0-2-1-3-1h0v2h0c-1 0-2 0-3-1 1 0 1 0 1-1 1-1 3-2 4-4 0-1 0-1-1-2z" class="D"></path><path d="M491 279c3-3 8-3 12-4-2 3-4 6-6 8l-1-1c-2 2-4 3-6 3v-2l-1-1-1-2h2l1-1z" class="B"></path><path d="M491 279h2c0 1-1 2-2 2s-1-1-1-1l1-1z" class="M"></path><path d="M493 279h2 0c-1 2-2 2-4 3v-1c1 0 2-1 2-2z" class="Z"></path><path d="M419 310c1 1 2 1 3 1 0 1-1 1-2 2h1 2v1h-5l-2 1c-1 0-2 1-2 1l-1-1h-2l-5 2v-3h0v-2l1-1c1 0 1 1 2 1h2v-1c3-1 5-1 8-1z" class="T"></path><path d="M411 315c0-1-1-1-1-2h0 1s1 0 2 1h-1l1 1h0-2z" class="D"></path><path d="M413 315c2-1 5-2 7-2h1 2v1h-5l-2 1c-1 0-2 1-2 1l-1-1h0z" class="k"></path><path d="M373 301c2 0 5-2 7-2v1 3 2 4h-1c-1 0-2 1-2 2-1 0-2 1-2 1 0-1-1-1 0-2 0-1-1-2-1-3l-1-6z" class="AA"></path><path d="M380 305v4h-1c-1 0-2 1-2 2-1 0-2 1-2 1 0-1-1-1 0-2l2-2c1 0 1-1 2-2h0l1-1z" class="AO"></path><path d="M373 301c2 0 5-2 7-2v1 1c-1 1-6 2-6 4v2l-1-6z" class="AE"></path><path d="M460 396l1-1h1c1 4 2 7 5 10l1-1 1 1c1 1 1 2 2 3l2-2h1l5 6h0l-1 1 1 1h-1-2c-1 0-2-1-2-1l-1-1c-1 0-2-1-3-2-4-4-8-9-10-14z" class="C"></path><path d="M468 404l1 1c1 1 1 2 2 3h0c1 0 1 1 1 1l2 2 1 1c1 0 1 0 2 1-1 0-2 0-2-1-3-2-6-4-8-7l1-1z" class="E"></path><path d="M473 406h1l5 6h0l-1 1 1 1h-1-2c-1 0-2-1-2-1l1-1c0 1 1 1 2 1-1-1-1-1-2-1l-1-1-2-2s0-1-1-1h0l2-2z" class="d"></path><path d="M471 408l2-2c0 1 2 3 3 5l-1 1-1-1-2-2s0-1-1-1h0z" class="G"></path><path d="M224 172h1c-1 1-2 2-2 3 0-1 1-1 1-1l1 1-8 12c-1 2-2 4-3 5l-1-1c1-1 1-1 2-3h-1l-1-1c1-1 1-2 1-3l6-9h1l3-3z" class="F"></path><path d="M218 180l1 1-4 7h-1l4-8z" class="V"></path><path d="M224 172h1c-1 1-2 2-2 3-2 2-2 4-4 6l-1-1 3-5 3-3z" class="M"></path><path d="M220 175h1l-3 5-4 8-1-1c1-1 1-2 1-3l6-9z" class="P"></path><path d="M339 543h2v1c-4 4-8 9-11 14-4 5-8 10-11 16-3 3-6 6-8 10l-1-1 29-40z" class="M"></path><path d="M304 424c3-7 8-13 12-19 2-2 3-5 5-6 0 1 0 3-1 4-2 5-5 9-8 13-3 3-5 7-7 10l-1-2z" class="I"></path><path d="M155 479c2-3 3-6 2-10v-1h1c0 3 1 5 1 8l-1 3c-1 4-5 8-8 12h-1c-1 1-1 2-2 3l-1-2c2-3 4-6 6-10v-2l1-1h2z" class="h"></path><path d="M152 480l1-1h2l-3 3v-2z" class="H"></path><path d="M164 127l1 1h0c1 1 0 1 1 1l-1 1c0 1 0 2 1 3v1l1 1v1c1 3 3 5 6 7v1l1 1h-1c-1 0-1 0-2-1l-1 1h-2 0-1s-1-1-1-2-1-1 0-3c-1-1-2-3-4-3 0-2 1-3 1-4 0-2 0-4 1-6z" class="o"></path><path d="M168 145h-1s-1-1-1-2-1-1 0-3c1 2 3 4 5 4l-1 1h-2 0z" class="L"></path><path d="M165 128c1 1 0 1 1 1l-1 1c0 1 0 2 1 3v1l1 1v1c1 3 3 5 6 7v1h-1c-2-1-4-3-6-5-2-3-2-7-1-11z" class="E"></path><path d="M347 402l2 7v3l1 3h-1-2c0 1-1 2-2 2-1-2-1-5-2-8-1-1-1-2-2-3l5-4h1z" class="AW"></path><defs><linearGradient id="I" x1="169.809" y1="166.198" x2="155.663" y2="167.959" xlink:href="#B"><stop offset="0" stop-color="#413833"></stop><stop offset="1" stop-color="#5a5148"></stop></linearGradient></defs><path fill="url(#I)" d="M158 162l6 1c2 1 3 1 4 1h2l-1 1v1c1 1 1 3 2 4v1l-16-6h0l-1-2h3 1-1l1-1z"></path><path d="M158 162l6 1c2 1 3 1 4 1-2 2-8 0-10-1h-1l1-1z" class="U"></path><path d="M371 511c2-2 5-4 6-6 1 1 2 1 3 1-4 6-8 11-13 16h-2l1-1c0-1 0-2-1-4h1l3-4h0l2-2z" class="R"></path><path d="M371 511c0 3-1 5-3 7-1 0-1-1-2-1l3-4h0l2-2z" class="Q"></path><path d="M390 350c2-1 2-3 3-4l3-5c1-2 3-6 5-8l1 1c0 1-1 4-2 5s-2 2-2 3v1 1l1 1c0 2-3 2-2 6l-1 1-2 2h-1c0-2-2-3-3-4z" class="H"></path><path d="M312 329v-2l-1-1v2 1h-2v-2l1-1v1-1l-4-3-1-1c-1-1-1-2-2-3h-1l2 3h-1c0-1-1-2-1-3 0-2-1-4-2-5-1-5-1-8-1-13h0c0 3 1 5 1 7 1-1-1-6 0-8 0 7 1 11 4 17 2 1 4 3 6 4l2 1h1v2l-1 5z" class="G"></path><path d="M310 321l2 1c-1 1-1 2-1 3h-2v-2c0-1 0-1 1-2z" class="e"></path><defs><linearGradient id="J" x1="306.808" y1="318.726" x2="306.093" y2="322.792" xlink:href="#B"><stop offset="0" stop-color="#5a504c"></stop><stop offset="1" stop-color="#6f6254"></stop></linearGradient></defs><path fill="url(#J)" d="M309 325v-1c-3-1-5-4-6-7h1c2 1 4 3 6 4-1 1-1 1-1 2v2z"></path><path d="M349 415h1l1 18c-1 3 0 7 0 10l1-5h0s0-1 1-1v-2l1-1c-1 5-2 10-4 15v-15c-1-5-3-10-4-15l-1-2c1 0 2-1 2-2h2z" class="AR"></path><path d="M345 417c1 0 2-1 2-2h2v1c0 1-2 2-3 3l-1-2z" class="Ah"></path><path d="M427 417h1c0-1 1-1 1-2l-2 6-6 17-6 10h0 0-1l1-1 1-4 2-5 2-4 1-2v-2c-1 0 0-1-1-2 0 0 1-1 1-2 1 1 1 1 2 1 1-3 2-7 4-10z" class="B"></path><path d="M420 428s1-1 1-2c1 1 1 1 2 1l-2 3c-1 0 0-1-1-2z" class="j"></path><path d="M440 160c4 0 9 1 13 3 1 0 1 1 1 2 1 2 5 2 6 6h-1 0c-1-1-2-1-3-1-5-3-10-5-15-6h-1l1-2h-1v-2z" class="r"></path><path d="M249 590h0v-1l-5-9h0 1c1 1 1 2 3 2h0c3 4 7 9 7 14 0 2-1 3-1 5s0 5-1 7v1c-1 2-5 5-7 6l-1 1-1-1 5-3c1-1 3-3 4-5 1-6-1-11-4-17z" class="L"></path><path d="M396 88l6 3c0 1 1 1 1 2l1 1c2 2 3 4 4 6h1l1 2v2 4 6s0-1-1-1c-1-1 0-4-1-5-1-4-4-10-7-13-1-1-3-3-5-4 0-1-1-2-1-2-1 0-1 0-2-1h3z" class="y"></path><path d="M408 100h1l1 2-1 1c-1 0-1-1-2-2 1 0 1 0 1-1z" class="r"></path><path d="M404 94c2 2 3 4 4 6 0 1 0 1-1 1l-4-5 1-1v-1z" class="z"></path><path d="M114 326h1v1 2 1h1 1c-1 2-1 2-1 3h0v2l-3 2-1-2h0c-2 0-3 0-5-1-1 0-1 0-2-1s-2-2-4-2l4-1h1c0-1 0-1 1-1s1 0 1-1l1 1c1-1 1 0 2 0s1 0 1-1v-1c1-1 1-1 2-1z" class="B"></path><path d="M106 330c1 0 2 1 3 2l-1 1h-1c-1-1-2-1-2-3h1z" class="Z"></path><path d="M107 329c1 0 1 0 1-1l1 1c1-1 1 0 2 0h0v2 1h1c0 1 0 1-1 1l-2-1c-1-1-2-2-3-2 0-1 0-1 1-1z" class="c"></path><path d="M109 329c1-1 1 0 2 0h0v2l-2-1v-1z" class="G"></path><path d="M111 329c1 1 1 2 2 2 0 0 0 1 1 1v1h0 2 0v2l-3 2-1-2-1-2c1 0 1 0 1-1h-1v-1-2z" class="a"></path><path d="M111 329c1 1 1 2 2 2 0 0 0 1 1 1v1h0l1 2h-1l-1-1c0-1 0-1-1-1v-1h-1v-1-2z" class="Q"></path><path d="M114 326h1v1 2 1h1 1c-1 2-1 2-1 3h-2 0v-1c-1 0-1-1-1-1-1 0-1-1-2-2h0c1 0 1 0 1-1v-1c1-1 1-1 2-1z" class="m"></path><path d="M113 328c1 0 1 0 2-1v2 1h-2v-2z" class="N"></path><path d="M114 326h1v1c-1 1-1 1-2 1h-1v-1c1-1 1-1 2-1z" class="J"></path><path d="M115 329v1h1 1c-1 2-1 2-1 3h-2 0c0-1 1-2 1-3v-1z" class="W"></path><path d="M301 580l-1-1c1-2 3-3 4-4l9-7c0 1-1 3-1 5-2 2-4 5-5 7-2 2-3 5-5 6h-1l-1-1 3-4-2-1z" class="V"></path><path d="M301 580c1-2 2-2 3-3l1 1-2 3-2-1z" class="T"></path><path d="M441 436c1-2 2-4 2-6 1-3 1-7 1-10 1 1 4 4 4 5v3c-1 2-1 6-3 8v2h-2c-1 2-1 5-2 7-1 0-2-1-3-1 2-2 3-5 3-8z" class="C"></path><path d="M443 438c0-2 2-3 2-5h0c0-1 1-3 2-4l1-1c-1 2-1 6-3 8v2h-2z" class="I"></path><path d="M124 438h1c1 3 3 6 6 7l2 1c0 1 1 2 2 2 2 1 3 3 5 3 4 2 11 3 14 6-1 1-4-1-6-1l-1 1c-2 0-3-1-5-1l-2-1c-7-3-13-10-16-17z" class="v"></path><path d="M140 455c3-1 5-1 7 0l1 1-1 1c-2 0-3-1-5-1l-2-1z" class="Y"></path><path d="M451 189h1l3 1c3 2 5 6 7 9l3 4c-1 0-2 1-2 2l-15-15h1c1 0 2 0 2-1z" class="e"></path><path d="M452 189l3 1c3 2 5 6 7 9h-1s-1 0-1-1c-1 0-2-2-3-3-1-2-3-3-5-6z" class="F"></path><path d="M475 344h1c1 2 0 2 1 3v6c0 1 0 2-1 4h0v1h0-2l-1 1-1-1-1 1-1-1h0v-3l-1-1-1 1v-3-1l3-1v-3h1l1 1 2-1c-1-2-1-2 0-3z" class="u"></path><path d="M473 348l2-1c0 1 1 2 1 3h-1l-1 2v-3l-1 1v-2z" class="a"></path><path d="M471 350v-3h1l1 1v2c0 2-1 3-1 5v1l-1-2h1c0-1 0-2-1-4z" class="P"></path><path d="M471 350c1 2 1 3 1 4h-1l1 2c-1 1-1 2-2 2h0v-3l-1-1-1 1v-3-1l3-1z" class="C"></path><path d="M475 344h1c1 2 0 2 1 3v6c0 1 0 2-1 4h0v1h0-2l-1 1-1-1c2-2 1-5 2-6l1-2h1c0-1-1-2-1-3-1-2-1-2 0-3z" class="u"></path><path d="M474 352l1-2h1v2c0 1-1 3-2 4l1 1h1v1h0-2l-1 1-1-1c2-2 1-5 2-6z" class="i"></path><path d="M421 480l1 1-1 2 1 2v1c-1-1-1-1-2-1h-1c0 1 0 1 1 2l-1 1c0 1 1 1 0 2l-1 1c0 1 0 1-1 2l-1 1c-1 0-1 0-2-1v1h0c0 1 1 1 1 2l-2 3-1-1c-2 2-4 5-7 5h0l-1 1h0l6-9 9-12 1-2 1-1z" class="H"></path><path d="M118 331c0-1 0-2 1-2 1-1 1-1 1-2 1 1 1 2 1 2 0 1-1 2-1 3 1-1 1-1 2 0h1c-3 2-5 5-6 8v4c0 1 0 2-1 3h0-1-1v-2l-1-1h0c-1-1-1-2-1-3s1-2 1-4l3-2v-2h0c0-1 0-1 1-3h0c1 0 1 0 1 1z" class="b"></path><path d="M117 337c0 1 0 1-1 2v-2l1-1v1z" class="F"></path><path d="M116 333l2 1v2l-1 1v-1h0l-1-1v-2z" class="k"></path><path d="M118 331c0-1 0-2 1-2 1-1 1-1 1-2 1 1 1 2 1 2 0 1-1 2-1 3v1h-1c-1-1-1-1-1-2zm-3 11c-1-1-1-1 0-3h1c0 1 0 1 1 1h0v4c0 1 0 2-1 3h0-1-1v-2l-1-1h0v-2h2z" class="F"></path><path d="M113 344v-2h2c0 2 0 3 1 5h-1-1v-2l-1-1h0z" class="Y"></path><path d="M499 258c-2-1-3-2-4-3s-2-1-3-2l1-1 2 1 2-1c1 1 3 2 4 3 2 2 5 3 6 5l6 5h0c1 1 1 2 2 3 1 0 2 1 2 2h0c-1 0-2-1-3-1-1-1-2-2-3-2l-3-2c-3-3-6-5-9-7z" class="q"></path><path d="M497 252c1 1 3 2 4 3 2 2 5 3 6 5-2-1-3-2-5-3-3-1-5-2-7-4l2-1z" class="b"></path><path d="M425 191h3l-1 1c0 1 1 1 2 1h0c0 1 1 2 2 4h0l1 1s0 1-1 1v1l-1 2c1 1 1 0 2 0l-1 1c-1 0-2-1-3-2h-2 0l-1 2c-2-2-3-5-5-7v-1c0-1 0-1 1-2l1-1h4s-1 0-1-1z" class="H"></path><path d="M425 191h3l-1 1c0 1 1 1 2 1h0c0 1 1 2 2 4l-1-1v1l-1-1-1-1-1-1-1 1-1-1c1 0 1 0 1-1v-1s-1 0-1-1z" class="M"></path><path d="M420 195c0-1 0-1 1-2l1-1c2 3 2 6 5 7v1c0 1-1 0-1 1h0l-1 2c-2-2-3-5-5-7v-1z" class="E"></path><path d="M417 155v-1c3 3 7 5 11 6 2 0 11-1 12 0v2h-2v1h-2l1 2h-4l-3 1-2-2v-1h0-1v-1c-1 0-2 1-4 1h-2v-1h1l1-1c-1 0-1-1-2-1l-2-2c-1-1-1-2-2-3z" class="j"></path><path d="M429 161l9 1v1h-2l1 2h-4 0l1-1c-1 0-1 0-1-1l-1-1-3-1z" class="i"></path><g class="F"><path d="M419 158c3 2 6 2 9 3l-1 1c-1 0-2 1-4 1h-2v-1h1l1-1c-1 0-1-1-2-1l-2-2z"></path><path d="M428 161h1l3 1 1 1c0 1 0 1 1 1l-1 1h0l-3 1-2-2v-1h0-1v-1l1-1z"></path></g><path d="M428 161h1l3 1v1h-4 0-1v-1l1-1z" class="R"></path><path d="M416 343h0 0c2 0 2 1 4 1 1 0 2 0 4 1h0c1 1 1 1 2 1 0 1 1 1 1 2 1 0 2-1 3 0l1 2h1c-1 2-3 4-5 5h0v-1c-1-1-4-4-6-4v-1h-4c-1-2-1-4-1-6z" class="U"></path><defs><linearGradient id="K" x1="433.736" y1="375.366" x2="437.9" y2="375.922" xlink:href="#B"><stop offset="0" stop-color="#ae9c94"></stop><stop offset="1" stop-color="#b8b2aa"></stop></linearGradient></defs><path fill="url(#K)" d="M435 389c1-3 0-5 1-8l-1-19c0-3-1-7 0-9l2 1 1 10c0 10 0 21-2 30l-1 1v-2-3-1z"></path><path d="M299 551h1v1c-1 1-2 2-2 3h1c0 1 0 2 1 3l-1 2-3 4c0-1 0-2-1-3l-3 5c0-1-1-1-1-2l-2-3-2-5v-1c0-1 0-1 1-1l4 5c1-1 2-2 2-3s1-2 2-2 0 0 1-1l2-2z" class="Z"></path><path d="M298 555h1c0 1 0 2 1 3l-1 2-3 4c0-1 0-2-1-3h0c0-1 1-3 2-4l1-2z" class="x"></path><path d="M298 555h1c0 1 0 2 1 3l-1 2c-1-1-1-1-1-2v-1h0 0v-1c0 1-1 1-1 1l1-2z" class="o"></path><path d="M456 204c4 2 6 5 8 8 1 2 3 5 4 7v4h0v1h-2c-1-1-1-1-2-1l-2-2c-1 0 0-2 0-3-1-1-2-1-3-1v-4-1c0-3-1-6-3-8z" class="L"></path><path d="M466 224c0-1 0-1 1-1h1v1h-2zm-7-12c1 1 1 2 1 3h0v1l1 1s1 0 1 1c-1-1-2-1-3-1v-4-1z" class="G"></path><path d="M430 313h2 0c-1 1-2 1-2 1l-1 1v2 4 1l-1 1h0l1 1s-1 1-1 2h0v2 1h-1l-2-1v-1l-1-1h-1v-1h0v-1l-2 1-1-1v-1h0v-1l1-1c1 0 1-1 2-1v-1c1 0 1-1 2-1l-1-2h0c0-2 0-2-1-2v-1h7z" class="f"></path><path d="M424 316h1v-2c1-1 1 0 2 0h1l-3 4-1-2z" class="K"></path><path d="M428 314l1 1c-1 2-3 3-4 4l-2 2c0 1-1 1-1 2 1 0 1 0 1 1l-2 1-1-1v-1h0v-1l1-1c1 0 1-1 2-1v-1c1 0 1-1 2-1l3-4z" class="G"></path><path d="M423 321c2 0 2 0 3-1v1h1 1 1v1l-1 1h0l1 1s-1 1-1 2h0v2 1h-1l-2-1v-1l-1-1h-1v-1h0v-1c0-1 0-1-1-1 0-1 1-1 1-2z" class="K"></path><path d="M423 325h1c1 0 1-1 2-1h0v1 1c0 1 1 1 1 3l-2-1v-1l-1-1h-1v-1h0z" class="J"></path><path d="M256 570v-1h0c1 0 1 1 1 1 1 2 2 3 3 4 3 4 6 10 11 13l17 28c1 0 1 0 2 1 0 1-1 1 0 2h0c1 0 1 1 1 1h1l-1 1h-1l-34-50z" class="D"></path><path d="M497 237c0-1 1-2 2-3s1-3 2-4l2-2c-1 5-4 11-7 17 0 1-1 2-2 3h-1v1l-4-2-1-1h0v-1c1-1 2-2 2-4l2-2v1l1-2c2 0 3 0 4-1z" class="V"></path><path d="M491 243v1l-1 1v1c1-1 1-1 3-1h-2c0 1-1 2-2 2l-1-1h0l3-3z" class="B"></path><path d="M493 238c2 0 3 0 4-1 0 2-2 5-3 6h-1l-1-2c0 1-1 1-1 1v1l-3 3v-1c1-1 2-2 2-4l2-2v1l1-2z" class="L"></path><path d="M417 143h1c-1 2-3 4-2 6h1v1 4h0v1c1 1 1 2 2 3l2 2h-4-4c-1 1-1 0-2 0l-1 1h0c-1-2 1-8 2-10 1-1 2-3 3-4v-1l1-1s1 0 1-1c-1 0-1 1-2 1l-1 1-1-1c2 0 3-1 4-2z" class="V"></path><path d="M416 155v-3c-1-1-1-1 0-2h1v4h0v1h-1z" class="C"></path><path d="M416 155h1c1 1 1 2 2 3l2 2h-4v-2c0-1-1-1-1-2v-1z" class="I"></path><path d="M100 299l2 2 1 1 2 2c1 1 2 2 2 3 1 1 2 3 3 3 0 2-1 3-1 4l-1 3-1 1c-1-2-3-3-4-5 0-1-1-3-2-4-1-2-1-3-1-5h-1l1-1v-4z" class="Z"></path><path d="M103 302l2 2c1 1 2 2 2 3 1 1 2 3 3 3 0 2-1 3-1 4 0-1 0-2-1-3 0-1-2-1-3-2-1 0-2-1-2-2v-5z" class="T"></path><path d="M429 193c6 1 8 4 12 8l1 2-1 1-1 1c-3 1-5-1-6 2-1-2-2-3-3-4l1-1c-1 0-1 1-2 0l1-2v-1c1 0 1-1 1-1l-1-1h0c-1-2-2-3-2-4z" class="Z"></path><path d="M432 202l1 1v1l1 1v-1c2-1 4 0 7-1v1l-1 1c-3 1-5-1-6 2-1-2-2-3-3-4l1-1z" class="I"></path><path d="M169 148c3 2 4 6 5 10v3l-1 1h-2l1 1c-2 0-4-1-6-1l-7-1h0c2-1 3-1 4-2 2-1 4-2 5-5h0c1-1 1-1 1-2v-4z" class="M"></path><path d="M163 159c2-1 4-2 5-5 1 2 1 2 0 4-1 1-2 1-3 2l-2-1z" class="C"></path><path d="M470 321c1 0 2 0 2 1 1 0 1 1 1 2h0v1h0 1 1l3 1 1 1v1c-1 1-2 1-3 1v1h0v1c-1 1-2 1-3 2 0 1-1 1-1 2l-2 1h-1l-1-1c0-1 0-1 1-2 0-1-1-3-1-4v-1l-1-1 1-1v-1s-1-1-1-2h1l3-2h-1z" class="e"></path><path d="M475 325l3 1 1 1v1c-1 1-2 1-3 1 1 0 1-1 1-2h0l-3 1v-2l1-1z" class="F"></path><path d="M472 328l1 1v1c1 1 2 0 3 0v1c-1 1-2 1-3 2l-1 1v-3-3z" class="j"></path><path d="M471 321v2s1 2 0 3v1c1 0 1 0 1-1 1 1 1 1 1 2h-1v3c-1 0-1 0-1 1l-2-1 1-5c-1-1-1-2-2-3l3-2z" class="Q"></path><path d="M471 321v2 2l-1 1c-1-1-1-2-2-3l3-2z" class="S"></path><path d="M467 323h1c1 1 1 2 2 3l-1 5 2 1c0-1 0-1 1-1v3l1-1c0 1-1 1-1 2l-2 1h-1l-1-1c0-1 0-1 1-2 0-1-1-3-1-4v-1l-1-1 1-1v-1s-1-1-1-2z" class="X"></path><path d="M469 331l2 1c0 1 0 1-1 1s-1 0-1-1v-1z" class="s"></path><path d="M467 323h1c1 1 1 2 2 3-1 0-1 0-2 1v1l-1-1 1-1v-1s-1-1-1-2z" class="c"></path><path d="M471 332c0-1 0-1 1-1v3l1-1c0 1-1 1-1 2l-2 1h-1c1-1 0-2 1-3 1 0 1 0 1-1z" class="i"></path><path d="M173 143c1 1 3 3 4 3 2 1 4 2 6 2 0 0 1 0 1-1l4 1h1l-2 1 7 2h-1-1c-1 1-2 2-4 2h-1l-3 3v-1c0-1 1-1 1-2l-1-1-1 1-1-1c-2 0-5-1-7-2l-3-2-4-3h2l1-1c1 1 1 1 2 1h1l-1-1v-1z" class="B"></path><path d="M174 148c1 0 2 0 3 1h-2 1l-1 1-3-2h2z" class="R"></path><path d="M168 145h2l4 3h-2l-4-3z" class="I"></path><path d="M184 147l4 1h1l-2 1-4-1s1 0 1-1z" class="X"></path><path d="M444 383v-5h0v-1c-2 0-2 1-3 2h0v-1c-1-1 0-2 0-4 0-3-1-10 0-13h2c1 1 3 2 3 3 1 1 0 5 1 6 0 1-1 2-1 2 0 1 0 1 1 1 0 2-1 5-2 7h0 0c0 1 0 2-1 3v3-3z" class="H"></path><path d="M469 302l1 1c0 1 0 2-1 3-1 0-1 1-1 1l-3 3-2 1c1 0 1 0 2 1h-2l1 1-3 1h-1l-1-3-2-3h-1l-1-1v-1c-1-1 0-2 0-2l1-1h0 1 5 0 1l1 1 2-1h0c1 0 2 0 3-1z" class="h"></path><path d="M455 306c-1-1 0-2 0-2l1-1c0 1 0 2 1 3v2h-1l-1-1v-1z" class="Ae"></path><path d="M462 303h0v1l-1 2 1 1h-2s-1 0-1 1c-1-1-1-2-1-3h0 2 0v-1h-2v-1h4z" class="w"></path><path d="M464 308c2 0 2-1 4-1l-3 3-2 1h-1-1c-1-1-2-1-2-2l1-1v-1h2v1h2z" class="y"></path><path d="M469 302l1 1c0 1 0 2-1 3-1 0-1 1-1 1-2 0-2 1-4 1h-2v-1l-1-1 1-2v-1h1l1 1 2-1h0c1 0 2 0 3-1z" class="W"></path><path d="M464 304l2-1v2h0-2v-1z" class="y"></path><path d="M469 302l1 1c0 1 0 2-1 3-1 0-1 1-1 1-2 0-2 1-4 1h-1c1-1 2-2 3-2h1v-2l-1-1h0c1 0 2 0 3-1z" class="s"></path><path d="M300 75c1 3 2 7 3 11 4 7 8 15 13 22-2-1-2-1-3-2h-1l-3-4-5-6-3-5-2-6 1-1-1-3c1 0 1-1 1-2s-1-2 0-4z" class="d"></path><path d="M299 85l1-1c3 8 8 15 13 22h-1l-3-4-5-6-3-5-2-6z" class="U"></path><path d="M428 201c1 1 2 2 3 2 1 1 2 2 3 4 1 1 2 2 3 4h0 0v3h1v6 2c0 1 0 1-1 1h0c-1 0-2-1-3-2l-1-4v-1h-1v-1l-1-2c0-1-1-3-2-4s-1-2-1-3h0c1-1 0-1 0-2h0v-3z" class="s"></path><path d="M428 206h0l1 1c1 0 1 0 1 1v1h1v2h1v-2l1 1c0 1 0 1 1 2 0 1 1 2 1 3-1 0-2 0-3 1v-1l-1-2c0-1-1-3-2-4s-1-2-1-3z" class="x"></path><path d="M437 214h1v6 2c0 1 0 1-1 1h0c-1 0-2-1-3-2l-1-4v-1h-1c1-1 2-1 3-1v1h1c1-1 1-1 1-2z" class="F"></path><path d="M435 215v1h1l1 2-1 1-1-2-1-1h-1-1c1-1 2-1 3-1z" class="j"></path><path d="M437 214h1v6 2c0 1 0 1-1 1h0l-1-4 1-1-1-2c1-1 1-1 1-2z" class="Y"></path><path d="M385 158c1-1 2-1 2-2 1 0 1 1 1 1h2 0c2 1 4 1 5 3h0l1-1c1 0 2 0 3 1 0 1 1 1 1 2v1h-1 0c-1-1-3 0-4 0-1-1-1-1-2-1h0v1 1l-4-1c-1 1 0 2 0 3-1 0-2 2-2 2-1 0 0 0-1-1-1-3-4-4-7-5l-2-2h1 0c2 0 4-1 6-2l1 1v-1z" class="B"></path><path d="M385 158c1-1 2-1 2-2 1 0 1 1 1 1h2c-1 0-1 0-1 1-1 1-1 1-1 2h-1c0-1 0-1-1-2h-1z" class="E"></path><path d="M413 198l1-1c-2-1-3-2-4-4h0 1l2 2h1c2 2 2 7 5 8 1-1 1-2 1-3s-2-3-3-4h0v-2h0c1 1 2 1 3 3h0v-1c2 2 3 5 5 7l1-2h0 2v3h0c0 1 1 1 0 2h0c0 1 0 2 1 3h0-2v1l-3-3-3-1c-2-1-2-1-4-1v1l-3-1v-2h-1 0l2-1v-1c-1-1-2-2-2-3z" class="L"></path><path d="M415 202c0 1 2 3 2 4l-3-1v-2h-1 0l2-1z" class="X"></path><path d="M417 194h0c1 1 2 1 3 3h0v-1c2 2 3 5 5 7l1-2h0 2v3h0c0 1 1 1 0 2h0-1s0 1-1 1c-1-1-1-1-1-2h-1 0c-2-1-2-2-3-3-1-2-2-5-4-6v-2z" class="F"></path><path d="M421 202h2c1 1 1 2 1 3h0c-2-1-2-2-3-3z" class="D"></path><path d="M426 201h2v3h0v1h-1c-1 0-2-1-2-2l1-2h0z" class="B"></path><path d="M426 201h2v3c-1-1-2-2-2-3h0z" class="K"></path><path d="M393 588l1 3c0 7-2 14-8 19-3 3-7 6-11 8v-1h-2-1-2l-1-1 3-1c2-1 5-2 8-4 9-6 12-13 13-23z" class="H"></path><path d="M372 615l1 1-1 1h-2l-1-1 3-1z" class="M"></path><path d="M417 160h4c1 0 1 1 2 1l-1 1h-1v1c-1 1-3 1-4 1-2 0-3 0-5 1h-1l-2 1c-1 0-3 0-4 1h1-1l1 1-10 4h-1l-1 1 4 2v1c-1 0-2-1-3-1l-7-3h1c7-3 14-7 21-8v-2-1l1-1c1 0 1 1 2 0h4z" class="Q"></path><defs><linearGradient id="L" x1="416.068" y1="162.096" x2="415.784" y2="159.796" xlink:href="#B"><stop offset="0" stop-color="#292627"></stop><stop offset="1" stop-color="#3c3830"></stop></linearGradient></defs><path fill="url(#L)" d="M417 160h4c1 0 1 1 2 1l-1 1-9 1-3 1v-2-1l1-1c1 0 1 1 2 0h4z"></path><path d="M410 162h3-1l1 1-3 1v-2z" class="B"></path><path d="M405 167h1-1l1 1-10 4h-1l-1 1 4 2v1c-1 0-2-1-3-1l-7-3h1 1c2 0 5-2 7-3l8-2z" class="T"></path><path d="M377 179v-1h1 0c-1-2-3-3-5-4-3-1-5-1-8-2v-1l11 3c4 2 8 4 11 6 2 1 4 2 6 2l1 1-2 2h0l-4 4c-1 0-1-1-1-1-2-2-4-3-5-4l-4-3 1-1v-1h-1-1z" class="V"></path><path d="M379 180l1 1h2l1 1c3 0 5 2 8 2 0 0 0 1 1 1l-4 4c-1 0-1-1-1-1-2-2-4-3-5-4l-4-3 1-1z" class="N"></path><path d="M379 180l1 1h0l1 1c3 1 4 3 6 4l1 1c-1 0-1 0-1 1-2-2-4-3-5-4l-4-3 1-1z" class="u"></path><path d="M367 184c0 1 1 1 1 2h1c-1-2-1-3-2-5-2-3-4-6-7-9l1-1c5 7 10 13 14 20 2 4 3 7 5 10 0 2 3 5 3 7h-1 0v-1c-1-1-1-2-1-3-1 0-1-1-2-2v2c-1 0-1-1-2-1l-10-18v-1z" class="P"></path><path d="M370 189h0 1l1 1c3 2 4 7 5 10h-1c-1-1-3-5-3-7h-1l-1-2c0-1-1-2-1-2z" class="f"></path><defs><linearGradient id="M" x1="391.569" y1="356.453" x2="381.257" y2="370.197" xlink:href="#B"><stop offset="0" stop-color="#756b64"></stop><stop offset="1" stop-color="#8b8077"></stop></linearGradient></defs><path fill="url(#M)" d="M390 350c1 1 3 2 3 4v4c-4 5-8 10-13 14-3 3-5 5-8 7v-2c2-1 5-3 6-5v-2c1-2 3-3 4-5l6-9-1-1c1-2 2-3 3-5z"></path><path d="M388 356c0-1 1-3 2-4 1 1 2 2 2 3-1 2-2 3-3 5-4 4-7 8-11 12v-2c1-2 3-3 4-5l6-9z" class="E"></path><path d="M395 389h7c4 1 11 3 14 6v3 1c-2 0-3-1-4-2-2 0-4-1-6-1-4-1-7-1-10-1v-1h-2c1-1 1-1 2-3v-1h-1v-1z" class="G"></path><path d="M395 389h7c-1 0-2 1-3 1h1c1 0 1 1 2 1l-6-1h-1v-1z" class="R"></path><path d="M396 390l6 1c2 1 4 1 6 2h-1 0-2c-3 0-6-1-9-2v-1z" class="T"></path><path d="M396 391c3 1 6 2 9 2h2c-1 1-1 2-1 3-4-1-7-1-10-1v-1h-2c1-1 1-1 2-3z" class="C"></path><path d="M367 618c-2 1-4 1-6 1-6 0-11 0-17-2-5-1-9-3-12-6-2-3-2-6-2-10-1-2-1-5-1-7 1-4 4-10 7-12 1-1 2-2 4-3-4 8-11 18-9 27h0v1c1 3 4 5 6 6 3 2 6 3 10 4 3 1 5 1 7 1 5 0 10 0 15-2l1 1-3 1z" class="S"></path><path d="M111 308l1-1v3c0 1-1 2-1 2 1 1 1 1 1 2h0l1 1v2h2 1c0 1-1 1-1 2l-1 1h0l-1 1v2c1 2 1 2 1 3-1 0-1 0-2 1v1c0 1 0 1-1 1s-1-1-2 0l-1-1c0 1 0 1-1 1v-1h-3v1c-1-1-1-1-2-1h0c1-1 2 0 3-1v-1s0-1-1-1v-1h1l3-3c0-2-1-2-2-3h1l1-1 1-3c0-1 1-2 1-4v-2h1z" class="h"></path><path d="M110 319l2 1v1c0 1 0 2-1 2h0-1l-1-2 1-1v-1z" class="a"></path><path d="M111 312c1 1 1 1 1 2h0c-1 2 0 3 0 5h0v1l-2-1c-1-2 0-5 1-7z" class="C"></path><path d="M112 314l1 1v2h2 1c0 1-1 1-1 2l-1 1h0l-1 1h-1v-1-1h0c0-2-1-3 0-5z" class="e"></path><path d="M113 317h2l-1 2h-1v-2z" class="F"></path><path d="M112 321h1v2c1 2 1 2 1 3-1 0-1 0-2 1v1c0 1 0 1-1 1s-1-1-2 0l-1-1c0 1 0 1-1 1v-1h-3v1c-1-1-1-1-2-1h0c1-1 2 0 3-1v-1s0-1-1-1v-1h1 1 2c0-1 0-1 1-1h1 1 0c1 0 1-1 1-2z" class="a"></path><path d="M112 321h1v2c0 2-1 3-3 4 0 0-1-1-2-1v-1c1-1 1 1 2 0h1l-2-2h1 1 0c1 0 1-1 1-2z" class="F"></path><path d="M105 324h1 2c0-1 0-1 1-1l2 2h-1c-1 1-1-1-2 0v1l-1 2h-3v1c-1-1-1-1-2-1h0c1-1 2 0 3-1v-1s0-1-1-1v-1h1z" class="b"></path><path d="M472 335c0 2 0 4 1 6h0c1 1 1 2 2 3h0c-1 1-1 1 0 3l-2 1-1-1h-1v3l-3 1v1 3 1l-1 1h-1-2c1-2 1-4 1-6l-1-1c1-1 1-2 1-3h1 0 0l1-1c0-1 0-3-1-5h0c0-1 0-2-1-2v-3l1 2h2v-1h0v-2l1 1h1l2-1z" class="R"></path><path d="M465 347h1c0 1 0 2 1 3v1h-1-1l-1-1c1-1 1-2 1-3z" class="Y"></path><path d="M465 351h1v2c1 1 1 1 1 2 0 0 0 1 1 1l-1 1h-1-2c1-2 1-4 1-6z" class="p"></path><path d="M466 353c1 1 1 1 1 2 0 0 0 1 1 1l-1 1h-1v-4z" class="O"></path><path d="M468 341v1l3 1v1 2l1 1h-1v3l-3 1v1h0-1c1-1 1-1 1-2l-1-3c0-2 1-3 0-5l1-1z" class="h"></path><path d="M471 346l1 1h-1v3l-3 1c1-1 1-1 1-2 1-1 2-2 2-3z" class="N"></path><path d="M468 341v1 1 1h1l1 1c-1 1-1 1-2 1l-1 1c0-2 1-3 0-5l1-1z" class="c"></path><path d="M472 335c0 2 0 4 1 6h0c1 1 1 2 2 3h0c-1 1-1 1 0 3l-2 1-1-1-1-1v-2-1l-3-1v-1l-1 1-1-4h2v-1h0v-2l1 1h1l2-1z" class="C"></path><path d="M468 337l2 1v1l-1 1h0l1 1s0 1 1 2l-3-1v-1l-1 1-1-4h2v-1z" class="a"></path><path d="M466 338h2v3l-1 1-1-4z" class="W"></path><path d="M470 341h1 1 1 0c1 1 1 2 2 3h0c-1 1-1 1 0 3l-2 1-1-1-1-1v-2-1c-1-1-1-2-1-2z" class="m"></path><path d="M470 341h1 1 1 0c1 1 1 2 2 3h0-2v-1l-1 1h-1v-1c-1-1-1-2-1-2z" class="x"></path><path d="M423 163c2 0 3-1 4-1v1h1 0v1l2 2-9 3-3 1-6 3c0-1-1-1-1-2-2-2-2-3-5-3l-1-1h1-1c1-1 3-1 4-1l2-1h1c2-1 3-1 5-1 1 0 3 0 4-1h2z" class="V"></path><path d="M413 170v-1h1 0v1h0-1z" class="G"></path><path d="M411 165h1v2h-1v-1-1z" class="B"></path><path d="M405 167c1-1 3-1 4-1 1 2 2 3 4 4h1 0v-1c1 0 2 1 3 1h1 0l-6 3c0-1-1-1-1-2-2-2-2-3-5-3l-1-1h1-1z" class="U"></path><path d="M423 163c2 0 3-1 4-1v1h1 0v1l2 2-9 3v-2-1h0c-1 1-2 1-3 1h-1l1-1c1-2 3-1 5-1v-2z" class="G"></path><path d="M495 222l1-2 1 1v1l1 1-1 2h1l-4 10c-1 2-2 3-2 4l-2 2c0 2-1 3-2 4v1h0-1l-6-4h1 2c2-3 5-6 7-10l4-10z" class="U"></path><path d="M497 222l1 1-1 2-1 2-1-2 2-3z" class="i"></path><path d="M490 235l1 1c0 1-1 2-2 3l-3 4h-1v-1l2-2c1-2 2-3 3-4v-1z" class="Q"></path><path d="M495 225l1 2c-1 3-4 6-5 9l-1-1 5-10z" class="N"></path><path d="M410 108l1 4h1v1h0c0 1 0 2-1 4h1 1l-1 1c1 0 1 1 1 1 0 2-1 3-1 5-4 5-6 10-11 15l-1 1-4 3-1 1-1-1 2-1 1-1-1-2c3-3 6-6 8-9l4-12c2-1 2-2 2-4v-6z" class="X"></path><path d="M404 130h0c-1 3-3 6-5 8 0 0 1 0 1-1h1l3-4c1 0 1-1 2-2-2 4-5 7-9 10l-1-2c3-3 6-6 8-9z" class="Y"></path><path d="M412 118c1 0 1 1 1 1 0 2-1 3-1 5-4 5-6 10-11 15l-1 1-4 3-1 1-1-1 2-1 1-1c4-3 7-6 9-10 3-4 5-9 6-13z" class="T"></path><defs><linearGradient id="N" x1="205.355" y1="217.752" x2="197.559" y2="204.516" xlink:href="#B"><stop offset="0" stop-color="#333330"></stop><stop offset="1" stop-color="#534942"></stop></linearGradient></defs><path fill="url(#N)" d="M206 201h0c1 0 2-2 2-3l1 1h0l4-8 1 1-6 11c-4 7-7 14-11 21h9c-3 1-6 0-9 0-5 0-10 1-14 0h0l1-4s0-1 1-2v6h8l11-22 1-2v1h1z"></path><path d="M205 200v1h1l-1 2-1-1h0l1-2z" class="P"></path><defs><linearGradient id="O" x1="406.471" y1="484.08" x2="414.248" y2="493.334" xlink:href="#B"><stop offset="0" stop-color="#232224"></stop><stop offset="1" stop-color="#4b4339"></stop></linearGradient></defs><path fill="url(#O)" d="M401 503c3-3 5-8 8-12 1-3 3-6 5-9 1-1 1-3 2-4l3-6 1 1c0 1-1 2-1 3h1s0-1 1-1c0 1 0 3-1 4v2l-1 2-9 12-6 9c-2 0-3 1-4 2l1-1h-1l1-2z"></path><path d="M420 479v2l-1 2h0-1l2-2h-1l1-2z" class="G"></path><path d="M438 413c-1-1-1-2-1-3h0c1 0 2 1 2 2h0v5c1 1 1 3 1 5 0 1 1 2 1 4v1l1 2c0 2-1 5-2 8l1-1c0 3-1 6-3 8l-3-1h0c1-1 1-2 1-3v-1-2-3h0l-1-1c0-2 1-3 1-5 1-2 2-7 0-9 0-1-1-1-1-2l1 1c1-1 0-1 1-3 0 0 1-1 1-2h0z" class="V"></path><path d="M438 438l1 1v1h-1v-2z" class="O"></path><path d="M439 417c1 1 1 3 1 5 0 1 1 2 1 4v1l-1 6-1 1v-1-4-12z" class="G"></path><defs><linearGradient id="P" x1="436.681" y1="436.891" x2="442.863" y2="430.406" xlink:href="#B"><stop offset="0" stop-color="#393430"></stop><stop offset="1" stop-color="#564f45"></stop></linearGradient></defs><path fill="url(#P)" d="M439 434l1-1 1-6 1 2c0 2-1 5-2 8l-1 2-1-1v-1c1-1 1-2 1-3z"></path><path d="M415 140l1 1-3 4h0l1 1c-3 2-5 3-8 4h-6c-3 1-6 2-9 2h0c-1-1-2-1-2-1-1 0-3 1-4 1v-1h0c0-1 0 0 1-1 2-3 6-4 9-6l1-1h1c0 1 0 1 1 1l1 1v2h1 2 1l3-1c2 0 3-1 5-2h1v-2c1-1 2-1 2-2h1z" class="f"></path><path d="M400 147h2 1l-14 4c-1 0-3 1-4 1v-1c5 0 10-3 14-4h1z" class="u"></path><path d="M395 144l1-1h1c0 1 0 1 1 1l1 1v2c-4 1-9 4-14 4h0c0-1 0 0 1-1 2-3 6-4 9-6z" class="P"></path><path d="M473 200l1-1v-1l1-1v1l3 3 1 1h1 0c0 2 1 3 1 5 1 1 1 1 1 2s-1 1-1 2h1v12c0 2-1 3-1 5-1 1-2 3-3 4v-1c-1 0-1 1-2 1h0v-1c1-1 1-2 1-2 1-1 1-1 1-2s0-1-1-1v1h-3v-1h-1c-1-1-1-2-1-2 1 1 1 0 2 0h1c1-1 2 1 3 1l1-1c0-2 0-3-1-5 0-1-1-2-1-2-1 0-1-1-1-1v-1c2 1 2 2 3 3h1v-5s-1 0-1-1c-1-1-1-2-1-3l-2-2v-1l-1-3-2-3z" class="H"></path><path d="M479 204h0v1h-1l1-1z" class="B"></path><path d="M473 200l1-1v-1l1-1v1l3 3h0c-1-1-2-1-2-2h-1c0 1 0 2 1 2 0 1 0 3 1 4l-1 1-1-3-2-3z" class="M"></path><path d="M473 200l1-1v-1l1-1v1s-1 1 0 2v3h0l-2-3z" class="P"></path><path d="M478 219h1l1 1v1c0 2 0 4-1 6 0 1-1 1-1 2s0 1-2 2c1-1 1-2 1-2 1-1 1-1 1-2s0-1-1-1v1h-3v-1h-1c-1-1-1-2-1-2 1 1 1 0 2 0h1c1-1 2 1 3 1l1-1c0-2 0-3-1-5z" class="f"></path><path d="M474 227c1-1 2-1 2-2h0l1 1v1h-3z" class="k"></path><defs><linearGradient id="Q" x1="202.841" y1="509.288" x2="207.564" y2="496.806" xlink:href="#B"><stop offset="0" stop-color="#554e49"></stop><stop offset="1" stop-color="#7b6b5f"></stop></linearGradient></defs><path fill="url(#Q)" d="M197 494l1-1 1 1c1 1 1 1 2 1l3 3v-2l2 1 2 3h2l1 2 1 1v2l4 4c0 2 0 2 2 3l2-2 1 1c0-1 1-1 1-1h1 0l-6 6h0l-2 2c-1-1-1-1-1-2l-3-3h1c-1-2-2-3-3-5-3-3-5-7-8-9-1-2-3-4-4-5z"></path><path d="M212 513h1l-1-1h1v-1h0c0 2 0 2 1 3v2l-3-3h1z" class="J"></path><path d="M215 511c1 2 2 3 2 5h0l-2 2c-1-1-1-1-1-2v-2c-1-1-1-1-1-3h2z" class="D"></path><path d="M214 514v-1l3 3-2 2c-1-1-1-1-1-2v-2z" class="B"></path><path d="M204 496l2 1 2 3h2l1 2 1 1v2l4 4c0 2 0 2 2 3l2-2 1 1c0-1 1-1 1-1h1 0l-6 6c0-2-1-3-2-5l-11-13v-2z" class="E"></path><path d="M208 500h2l1 2 1 1v2-1h-1c-1-1-2-3-3-4z" class="P"></path><path d="M290 62v1c1 7 1 15-1 23h1 3v1c-2 1-3 2-4 3l-5 6-3 3c-1-1-1-3 0-5 0-1 1-3 2-4l1-3v-1h-1c0-1 1-3 1-4l2-6c1-1 1-2 2-3l1-2v1-4c1-2 1-4 1-6z" class="g"></path><path d="M289 86h1 3v1c-2 1-3 2-4 3h-1c0-2 0-3 1-4z" class="AN"></path><path d="M288 73l1-2v1l-1 6h1c0 2 0 4-1 6s-1 3-1 5l-1 2-1-1s-1-2-1-3h0v-1h-1c0-1 1-3 1-4l2-6c1-1 1-2 2-3z" class="r"></path><path d="M284 82l2 2-2 3h0v-1h-1c0-1 1-3 1-4z" class="f"></path><path d="M288 73l1-2v1l-1 6c-1 2-1 4-2 6l-2-2 2-6c1-1 1-2 2-3z" class="R"></path><path d="M454 355h1c1-1 0-3 0-4h0l1 1 1 1 2 2 1 1c1 0 1 0 2 1h0l-1 1c0 1 0 1 1 1h1v1 1h-1v2h-1l-1-1h-1c-1 1 0 4-1 5 0 3 1 5 1 8 0 5-2 9 0 14h1v1h0c0 1 0 3-1 4v-4c-1-1-1-2-2-3 0-2 0-5-1-7v-2c1-2 0-5 0-7l-1-2-1-2h0c-1-2-1-3-2-5h1l1-1c-1-2-1-4-1-6h1 0z" class="M"></path><path d="M457 367v3l-1 1-1-2c0-1 1-1 1-2h1z" class="p"></path><path d="M454 355h1c1-1 0-3 0-4h0l1 1 1 1v3c1 1 0 2 0 3l-1 1-1-1c-1-2-1-3-1-4z" class="J"></path><path d="M456 352l1 1v3l-1-1v-3z" class="F"></path><path d="M459 355l1 1c1 0 1 0 2 1h0l-1 1c0 1 0 1 1 1h1v1 1h-1v2h-1l-1-1h-1c-1 1 0 4-1 5 0-2 0-4-1-5v-1-1l2-1-1-1 2-1h-1v-2z" class="C"></path><path d="M460 357v3l-1-1-1-1 2-1zm-7-2h1 0c0 1 0 2 1 4l1 1c0 2 0 4 1 7h-1c0 1-1 1-1 2l-1-2h0c-1-2-1-3-2-5h1l1-1c-1-2-1-4-1-6z" class="F"></path><path d="M454 363c1 1 1 2 2 4 0 1-1 1-1 2l-1-2h0v-4z" class="G"></path><path d="M454 361v2 4c-1-2-1-3-2-5h1l1-1zM299 624l1 1v2c0 1 1 1 1 2v1c-1 3-3 8-5 10l-1 1c-1 1-2 3-3 3h-1c-1-1-3-4-3-5-2-2-2-4-3-7h1c0-1 0-2-1-2 0-1 1-1 1-2l1 2v-1l2 1v2h0v1c1 0 1-1 3-1h4c0-3 2-5 3-8z" class="V"></path><path d="M299 624l1 1v2l-4 7v1h-1 0l1-3c0-3 2-5 3-8z" class="D"></path><path d="M287 629l2 1v2h0v1c1 0 1-1 3-1v1h2v2c-1 0-1 1-1 1v1s0 1-1 1h-1s-1-1-1-2-1-1-1-2c-1-2-2-3-2-4v-1z" class="B"></path><path d="M292 633h2v2c-1 0-1 1-1 1h-2c0-1 0-1-1-2 1-1 1-1 2-1z" class="w"></path><path d="M288 639c-2-2-2-4-3-7h1c0-1 0-2-1-2 0-1 1-1 1-2l1 2c0 1 1 2 2 4 0 1 1 1 1 2s1 2 1 2v1h0c-1 0-1-1-2-2h0v2l1 1v1c0 1 1 1 1 2 1-1 2-1 3-2l1-1h1l-1 1c-1 1-2 3-3 3h-1c-1-1-3-4-3-5z" class="R"></path><path d="M380 314v1-1l1 1v-1h2 0 4 1l-1 2-11 28v-1l-1-1v-11l2-2 1-6c1-1 1-3 1-4v-5h1z" class="AA"></path><path d="M383 314h4 1l-1 2c-3 0-4 0-6 2v-2c1 0 1-1 2-1v-1z" class="AV"></path><path d="M380 314v1-1l1 1v-1h2 0v1c-1 0-1 1-2 1v2c0 3-1 6-2 10 0 1 0 3-1 4h-1v-3l1-6c1-1 1-3 1-4v-5h1z" class="Ab"></path><path d="M391 145l3-2 1 1c-3 2-7 3-9 6-1 1-1 0-1 1h0v1c-2 1-4 1-6 1l-9 2h-7c-4 0-8 0-11-1l29-7 4-1 5-2 1 1z" class="L"></path><path d="M381 147l4-1 1 1-4 1-1-1z" class="P"></path><path d="M385 146l5-2 1 1c-2 1-4 2-5 2l-1-1zm0 5h0v1c-2 1-4 1-6 1l-9 2-8-1c4 0 9-1 14-1 3-1 6-2 9-2z" class="F"></path><defs><linearGradient id="R" x1="98.522" y1="239.63" x2="74.99" y2="227.585" xlink:href="#B"><stop offset="0" stop-color="#1d1c1f"></stop><stop offset="1" stop-color="#47433b"></stop></linearGradient></defs><path fill="url(#R)" d="M79 211c0 3 2 6 3 8 2 6 4 11 7 16 2 4 5 7 7 10v1l-6 4c0-2-1-3-2-5-2-4-3-7-4-10-3-8-5-16-5-24z"></path><path d="M408 303v-4h1c2 1 4 1 5 2h0c2 2 5 3 7 4s4 1 5 2v1c1 0 2 0 2-1 1 0 1 0 1 1h0l1 3v2h0 0-7-2-1c1-1 2-1 2-2-1 0-2 0-3-1-3 0-5 0-8 1v-1h0l2-1v-1h-3v-1-1c-2 0-3-1-5-1v-1h0 1c1 0 4 1 5 1 0-1 1-1 2-1v1-1h-1l-1-1h-3z" class="O"></path><path d="M417 307h2l1 1c1 1 1 1 2 1l-1 1 1 1c-1 0-2 0-3-1-1 0-1-2-2-3z" class="Y"></path><path d="M426 307c0 1-1 1-1 1-2 0-2-1-3-1 0-1-1-1-1-1-1 0-1 0-1-1-1 0-3-1-4-1l-1 1h-1c0-1-1-2-1-3l1-1c2 2 5 3 7 4s4 1 5 2z" class="F"></path><path d="M422 309l3 1v1h5v2h0 0-7-2-1c1-1 2-1 2-2l-1-1 1-1z" class="h"></path><path d="M425 311h5v2h0c-2 0-5 0-6-1v-1h1z" class="g"></path><path d="M410 306l1-1c2 0 4 1 6 2 1 1 1 3 2 3-3 0-5 0-8 1v-1h0l2-1v-1h-3v-1-1z" class="b"></path><path d="M410 306c2 0 4 0 5 2h0c0 1 0 1-1 1-1 1-2 1-3 1h0l2-1v-1h-3v-1-1z" class="K"></path><path d="M124 316h3v1h-1l1 1-1 1 1 1v1l2 1 1-1v1 2c-1 0-2 1-3 2s-2 2-2 3h-2l-1 3c-1-1-1-1-2 0 0-1 1-2 1-3 0 0 0-1-1-2 0 1 0 1-1 2-1 0-1 1-1 2 0-1 0-1-1-1h0-1-1v-1-2-1h-1c0-1 0-1-1-3v-2l1-1h0l1-1c0-1 1-1 1-2h3v1h1l1-1v-1c1 1 1 1 2 1v-1h1z" class="c"></path><path d="M119 322l1-1v4c-1 0-2-1-2-1 1-1 1-2 1-2z" class="G"></path><path d="M117 324h1s1 1 2 1c-1 1-1 1-1 2-1 0-1 0-1-1-1 0-1-1-1-2z" class="R"></path><path d="M118 321s1 0 1 1c0 0 0 1-1 2h-1l-1-2v-1h2z" class="p"></path><path d="M116 325v-3l1 2c0 1 0 2 1 2l-1 3-1 1h-1v-1-2-1l1-1z" class="q"></path><path d="M115 326l1-1c0 2 0 3 1 4l-1 1h-1v-1-2-1z" class="g"></path><path d="M114 320l2-1 2 2h-2v1 3l-1 1h-1c0-1 0-1-1-3v-2l1-1h0z" class="N"></path><path d="M114 320l2-1 2 2h-2-2v-1h0z" class="J"></path><path d="M116 317h3v1h1l1 1v1h0-1v1h0l-1 1c0-1-1-1-1-1l-2-2-2 1 1-1c0-1 1-1 1-2z" class="N"></path><path d="M116 319c1 0 1-1 3 0 0 0 1 0 1 1v1h0l-1 1c0-1-1-1-1-1l-2-2z" class="S"></path><path d="M124 316h3v1h-1l1 1-1 1 1 1-1 1h-2c0 1-1 2-2 3-1-1-1-1-1-2s0-1-1-1v-1h1 0v-1l-1-1 1-1v-1c1 1 1 1 2 1v-1h1z" class="F"></path><path d="M123 320h-1c0-1 0-1 1-2h1v1l-1 1z" class="G"></path><path d="M124 319c0 1 1 1 2 2h-2c0 1-1 2-2 3-1-1-1-1-1-2h1l1-2 1-1z" class="N"></path><path d="M127 320v1l2 1 1-1v1 2c-1 0-2 1-3 2s-2 2-2 3h-2l-1 3c-1-1-1-1-2 0 0-1 1-2 1-3 0 0 0-1-1-2 1 0 1-1 2-2l1-1 2-1h1c0-1-1-1-2-2h2l1-1z" class="x"></path><path d="M125 323l1 1c0 1-1 1-1 2h-1l-1-2 2-1z" class="J"></path><path d="M127 320v1l2 1h-1 0l-1 1h-1c0-1-1-1-2-2h2l1-1z" class="W"></path><path d="M125 326c-1 2-3 2-2 3l-1 3c-1-1-1-1-2 0 0-1 1-2 1-3 1 0 1-1 2-2l1-1h1z" class="l"></path><path d="M122 325l1-1 1 2-1 1c-1 1-1 2-2 2 0 0 0-1-1-2 1 0 1-1 2-2z" class="J"></path><path d="M122 325l1-1 1 2-1 1-1-2z" class="F"></path><path d="M452 340v-1l1-1s0-1 1-1h0c1 0 1 1 2 2l1-2c1 1 2 1 2 2 2 1 3 3 4 5 1 0 1 1 2 2v1c0 1 0 2-1 3l1 1c0 2 0 4-1 6l-1 1-1-1h0c-1-1-1-1-2-1l-1-1-2-2-1-1-1-1c0-1 0-2-1-3l-1-3c-1 0-1 0-2-1v-1-2c1 0 1 0 1-1h0z" class="Z"></path><path d="M460 356l1-1h-1v-2h1c0 1 1 2 1 4-1-1-1-1-2-1z" class="B"></path><path d="M452 340v-1l1-1s0-1 1-1h0c1 0 1 1 2 2l3 1v2h-1-2 0c0 1 0 1 1 1v1 1h-1c-1-1-1-3-1-4v-1c0-1 0-1-1-1v1c1 1 0 1-1 2v2 1c-1 0-1 0-2-1v-1-2c1 0 1 0 1-1h0z" class="K"></path><path d="M452 340c1 1 1 3 1 4v1c-1 0-1 0-2-1v-1-2c1 0 1 0 1-1z" class="a"></path><path d="M457 337c1 1 2 1 2 2 2 1 3 3 4 5 1 0 1 1 2 2v1c0 1 0 2-1 3-2-3-4-4-7-7-1 0-1 0-1-1h0 2 1v-2l-3-1 1-2z" class="X"></path><path d="M189 142c1 1 2 2 4 2v-1c2 1 4 1 5 2l15 6 13 2c1 1 3 1 5 1l-1 1c-6 0-13 0-19-1h0c-5 0-12-2-17-3l-7-2 2-1h-1v-1h3l2-1-5-3 1-1z" class="S"></path><path d="M189 142c1 1 2 2 4 2v-1c2 1 4 1 5 2-1 1-1 1-2 1h-2l1 1c1 0 1 0 2 1l-4-2-5-3 1-1z" class="f"></path><path d="M193 146l4 2h0c2 1 3 1 4 2l2 2 7 1 1 1c-5 0-12-2-17-3l-7-2 2-1h-1v-1h3l2-1z" class="h"></path><path d="M197 148c2 1 3 1 4 2l2 2c-3-1-5-2-8-2 1-1 1-1 2-1v-1z" class="C"></path><path d="M193 146l4 2h0v1c-1 0-1 0-2 1l-6-2h-1v-1h3l2-1z" class="P"></path><path d="M123 167c6-4 12-6 19-7h1c2 0 3 1 4 1h2v4h-6c-6 0-11 2-16 6-1 0-2 0-3 1l-3 4 1 2-3 5-1-1v1c-1 0-1 1-2 1h0v-2l-1-1v-1c1-1 2-3 4-4 0-1 0-3 1-4h1 0l1-3 1-2z" class="y"></path><path d="M143 165l2-1v-2c1-1 1-1 2-1h2v4h-6z" class="s"></path><path d="M122 169l1-2c0 2-1 4-2 6v2l1-2c1-1 2-1 2-1l-3 4c-1 2-2 4-3 5h0l2-5h-1c0-1 0-3 1-4h1 0l1-3z" class="W"></path><path d="M120 172h1 0c0 2-1 3-1 4h-1c0-1 0-3 1-4z" class="d"></path><path d="M119 176h1l-2 5h0c1-1 2-3 3-5l1 2-3 5-1-1v1c-1 0-1 1-2 1h0v-2l-1-1v-1c1-1 2-3 4-4z" class="m"></path><path d="M385 475c0 1 1 1 1 1 1-1 1-2 2-3 1 1 1 1 1 2s-1 2-1 3h2l-1 2v2c-2 2-4 4-6 7l-1 2v2l-3 3-3 4-5 7v-1h-1l-1-1h0c0-2 0-3 1-4l9-15c1-1 2-4 3-5h1c1-2 2-4 2-7v1z" class="e"></path><path d="M369 505c1-1 1-2 2-2l1 1-1 2h-1l-1-1h0z" class="m"></path><path d="M382 491v2l-3 3c0-1 1-1 1-2h0v-1l2-2z" class="D"></path><path d="M372 504v-1-3h4l-5 7v-1l1-2z" class="i"></path><path d="M461 228l8 6 11 7 1 1 6 4h1l1 1 4 2 4 3-2 1-2-1-1 1c1 1 2 1 3 2s2 2 4 3l-10-3h3c-1-1-2-1-2-2-1-1-4-2-6-3l-2-1c-1-1-2-1-3-2s-3-2-4-2l2-1-2-1-3-2-5-4-2-1v-2h-1v-1h0l-3-3v-1-1z" class="d"></path><path d="M465 234l2 2v1l-2-1v-2zm10 6c2 1 3 1 5 1l1 1 6 4c-2 0-2-1-4 0h0c-2-1-4-2-6-4h0l-2-2z" class="X"></path><path d="M483 246c2-1 2 0 4 0h1l1 1 4 2 4 3-2 1-2-1c-3-2-7-5-10-6z" class="a"></path><path d="M477 244c3 2 14 7 15 10-1 0-1-1-2-1-1-1-4-2-6-3l-2-1c-1-1-2-1-3-2s-3-2-4-2l2-1z" class="D"></path><path d="M461 228l8 6 11 7c-2 0-3 0-5-1-2 0-5-3-7-5-1 0-2-1-2-2h0-2 0l-3-3v-1-1z" class="l"></path><path d="M262 123h1c1 0 2-1 3-1l-3 3 1 1-7 8c-1 1-2 3-3 4h0c-10 12-21 23-29 37h0l-1-1s-1 0-1 1c0-1 1-2 2-3 1-2 2-4 4-5 1-2 3-4 4-6s3-4 4-6h0v-1l7-9c1-2 4-4 6-6l-1-2c1-2 1-4 2-6v-2l1 1v-1h1 1l1 2c1-3 5-6 7-8z" class="P"></path><path d="M250 139c3-2 4-5 6-7 2-3 5-5 7-7l1 1-7 8c-1 1-2 3-3 4-4 3-7 6-10 10-3 2-5 4-7 7h0 0v-1l7-9c1-2 4-4 6-6z" class="E"></path><path d="M262 123h1c1 0 2-1 3-1l-3 3c-2 2-5 4-7 7-2 2-3 5-6 7l-1-2c1-2 1-4 2-6v-2l1 1v-1h1 1l1 2c1-3 5-6 7-8z" class="s"></path><path d="M251 129l1 1v-1h1 1l1 2v1l-1 1s-2 1-2 2l-1-1v-3-2z" class="q"></path><path d="M252 130v-1h1 1l1 2v1l-1 1c-1-1-2-2-2-3z" class="z"></path><path d="M183 470l2 2v1c1 1 2 1 3 3h0l1-2-1-1c2 2 3 4 5 6 0 2 2 3 3 4s2 3 3 4l7 8v2l-2-1v2l-3-3c-1 0-1 0-2-1l-1-1-1 1c-1-2-3-4-5-5l-1-1-3-3-3-6h0l-2-3-3-5 2 1 1-2z" class="x"></path><path d="M189 474v2 1l-1-1 1-2z" class="U"></path><path d="M183 476l2 1 3 4h-1l-2-2h0l-2-3z" class="J"></path><path d="M185 479l2 2h1l3 7-3-3-3-6z" class="C"></path><path d="M189 478l5 7 2 2h-2c-1-1-3-4-4-6 0 0 0-1-1-1v-2z" class="F"></path><path d="M189 476l5 6s1 1 1 2l-1 1-5-7v-1-1z" class="B"></path><path d="M183 470l2 2v1h0c0 1 0 2 1 2v1l-1 1-2-1-3-5 2 1 1-2z" class="N"></path><path d="M183 470l2 2v1h0c0 1 0 2 1 2v1l-1 1c0-2-2-4-3-5l1-2z" class="c"></path><path d="M188 473c2 2 3 4 5 6 0 2 2 3 3 4s2 3 3 4l7 8v2l-2-1v2l-3-3-5-8-2-2 1-1c0-1-1-2-1-2l-5-6v-2l-1-1z" class="Q"></path><path d="M194 485l1-1 9 12v2l-3-3-5-8-2-2zm266-157c0-1 1-1 2-1h0c1 2 1 3 3 4l1-2 1 1 1-1c0 1 1 3 1 4-1 1-1 1-1 2v2h0v1h-2l-1-2v3c1 0 1 1 1 2h0c1 2 1 4 1 5l-1 1h0 0-1v-1c-1-1-1-2-2-2-1-2-2-4-4-5 0-1-1-1-2-2s-2-1-3-2l-1-1c-1-1-1-2-2-2s-1-1-1-1h-2v-2s0-1-1-1h6l1-1c1 1 2 1 3 1v-1c1 1 2 1 2 1h1z" class="Z"></path><path d="M459 339c1 0 3 0 3 1l2 3c1 1 1 2 1 3-1-1-1-2-2-2-1-2-2-4-4-5z" class="I"></path><path d="M454 327c1 1 2 1 3 1v1h-3l-4 2h-2v-2s0-1-1-1h6l1-1z" class="Q"></path><path d="M460 328c0-1 1-1 2-1h0c1 2 1 3 3 4l1-2 1 1 1-1c0 1 1 3 1 4-1 1-1 1-1 2v2h0v1h-2l-1-2v-1c-2-2-5-4-8-6v-1-1c1 1 2 1 2 1h1z" class="s"></path><path d="M464 332h1l1 2h-1l-1-2z" class="h"></path><path d="M460 328c0-1 1-1 2-1h0c1 2 1 3 3 4-1 0-1 0-2 1 0-1-1-2-1-2 0-1-1 0-1-1-1 0-1 0-2-1h1z" class="J"></path><path d="M466 329l1 1 1-1c0 1 1 3 1 4-1 1-1 1-1 2v2h0v1h-2l-1-2v-1-1h1l-1-2h-1-1c1-1 1-1 2-1l1-2z" class="Y"></path><path d="M465 332l2-1h0v3h-1l-1-2z" class="W"></path><path d="M466 334h1c0 1 0 2 1 3h0v1h-2l-1-2v-1-1h1z" class="t"></path><path d="M289 90c1-1 2-2 4-3 0 1 1 2 2 3l4 5c-1 0-2 0-2 1l-5-5c-4 4-7 8-11 12 0 1-3 3-3 3v1c0 1-1 2-1 2v1-1c1 0 1-1 2-1l-13 14c-1 0-2 1-3 1h-1c-2 2-6 5-7 8l-1-2 3-3c1-1 3-2 4-3 2-2 4-4 5-6l-1-1c1-2 3-4 4-5s2-2 2-3c1-1 1-1 3-1h0c3-2 5-5 7-8l3-3 5-6z" class="Z"></path><path d="M269 111c1-1 2-2 2-3 1-1 1-1 3-1h0l-8 10-1-1c1-2 3-4 4-5z" class="t"></path><path d="M262 123c3-3 5-5 8-7 3-3 5-7 8-10v1c0 1-1 2-1 2v1-1c1 0 1-1 2-1l-13 14c-1 0-2 1-3 1h-1z" class="j"></path><path d="M330 135h1c2 3 5 5 7 8h1c2 1 3 3 5 5 6 7 12 15 17 23h0l-1 1c3 3 5 6 7 9 1 2 1 3 2 5h-1c0-1-1-1-1-2v1c-7-9-13-19-20-28-5-6-11-12-17-18l1-1-1-1v-2z" class="o"></path><path d="M330 135h1c2 3 5 5 7 8s5 5 7 8c2 2 4 4 5 7 1 2 3 3 3 5-5-7-11-14-18-21-1-1-2-3-4-4l-1-1v-2z" class="B"></path><path d="M339 143c2 1 3 3 5 5 6 7 12 15 17 23h0l-1 1c3 3 5 6 7 9 1 2 1 3 2 5h-1c0-1-1-1-1-2l-3-6-11-15c0-2-2-3-3-5-1-3-3-5-5-7-2-3-5-5-7-8h1z" class="J"></path><path d="M380 290l1 1c0-1 0-1 1-1v3 1h1 8l-1 1 1 1h2v1l-2 6c0 1 0 2-1 3l-1 3v2l-1 2v1h-1-4 0-2v1l-1-1v1-1-5-4-2-3c1-2 0-7 0-10z" class="AV"></path><path d="M386 296v-1h0 4l1 1h-5z" class="AE"></path><path d="M383 294h8l-1 1h-4 0v1c-1 0-2-1-3-2z" class="AA"></path><path d="M388 313v1h-1-4 0v-1h5z" class="AR"></path><path d="M388 305c1 0 1 0 2 1l-1 3h-1v-1h0v-3z" class="AA"></path><path d="M388 309h1v2h-1 0c-2-1-3-1-4 0h-1v-1c1-1 3-1 5-1z" class="AE"></path><path d="M391 296h2v1l-2 6c0 1 0 2-1 3-1-1-1-1-2-1 0-1 1-2 2-3v-1 1l1-1v-1c0-1 1-1 1-2v-1h0l-1-1z" class="AM"></path><path d="M380 303l2 1-1 10v1l-1-1v1-1-5-4-2z" class="AJ"></path><path d="M380 290l1 1c0-1 0-1 1-1v3 1 10l-2-1v-3c1-2 0-7 0-10z" class="AB"></path><path d="M469 272c1 0 1 1 2 2h0c2 0 2 2 5 1 0 1 1 2 1 3 1-1 2-2 3-2h0l-4 3-4 4c-2 2-3 4-5 6h2c-2 2-3 5-5 7 0 2-2 5-1 7h-1 0-5l1-1c0-5 1-10 3-14l1-2c0-1 1-1 1-2l1-1 1-1 1-3 2-5 1-2z" class="H"></path><path d="M463 296c1-3 3-5 4-7h2c-2 2-3 5-5 7h-1z" class="D"></path><path d="M463 296h1c0 2-2 5-1 7h-1 0-5l1-1h1v-2c0 1 0 2 1 2 2-2 2-4 3-6z" class="I"></path><path d="M464 283v1l-3 6c-1 3-2 7-2 10v2h-1c0-5 1-10 3-14l1-2c0-1 1-1 1-2l1-1z" class="D"></path><path d="M469 272c1 0 1 1 2 2h0c2 0 2 2 5 1 0 1 1 2 1 3 1-1 2-2 3-2h0l-4 3c0-1 0-1-1-2h0 0 0c0 1-1 2-1 3h-1c-1 1-1 1-2 1h-1c-1 1-1 2-1 3h-1s-1 0-1-1v-1h-2l1-3 2-5 1-2z" class="I"></path><path d="M469 272c1 0 1 1 2 2h0-1v2h2v1c-2 1-2 0-2 0-1 0-1 1-2 1 0 1-1 1-2 1l2-5 1-2z" class="P"></path><path d="M469 272c1 0 1 1 2 2h0-1v2c-1-1-1 0-1-1l-1-1 1-2z" class="F"></path><defs><linearGradient id="S" x1="399.5" y1="472.719" x2="389.334" y2="460.337" xlink:href="#B"><stop offset="0" stop-color="#70635a"></stop><stop offset="1" stop-color="#918277"></stop></linearGradient></defs><path fill="url(#S)" d="M399 452h1 0v2c2 1 3 4 3 6 0 1 0 2 1 3-1 1-1 2 0 3l-2 2h-1s-1 1-1 2l-1-1c-1 1-2 3-3 4-1 3-3 5-4 7l-3 2v-2l1-2h-2c0-1 1-2 1-3s0-1-1-2c-1 1-1 2-2 3 0 0-1 0-1-1l6-9c2-2 3-4 4-6 2-2 3-6 4-8z"></path><path d="M400 454c2 1 3 4 3 6 0 1 0 2 1 3-1 1-1 2 0 3l-2 2h-1s-1 1-1 2l-1-1c3-5 3-10 1-15z" class="O"></path><path d="M368 500c1 3 0 4-1 6v4c0 1 0 1 1 2l-2 2c-2 4-5 6-7 9-1 1-2 2-3 4-3 4-7 8-10 12l-5 5v-1h-2c1-2 3-4 4-6l14-20 7-10c2-3 2-5 4-7z" class="K"></path><path d="M341 543c1-2 2-3 3-5l1 1v-1l1 1-5 5v-1z" class="R"></path><path d="M366 514h-1c-1-2 2-6 2-8v4c0 1 0 1 1 2l-2 2z" class="C"></path><path d="M503 188l1 3 1 2c1 5 1 9 1 13 0 8-1 14-3 22l-2 2c-1 1-1 3-2 4s-2 2-2 3c-1 1-2 1-4 1l-1 2v-1c0-1 1-2 2-4l4-10h-1l1-2-1-1v-1-1l2-4 1-2v1l1-4c1-1 1-2 1-2l1 1v-10l-1-6 1-1v-5z" class="t"></path><path d="M503 188l1 3c-1 3 0 6 0 9v3c-1-1-1-2-1-3l-1-6 1-1v-5z" class="q"></path><path d="M505 193c1 5 1 9 1 13-1 1 0 3-1 4 0 1-1 2-1 3 0-4 1-7 1-10 0-4-1-6 0-10z" class="b"></path><path d="M500 215l1-4c1-1 1-2 1-2l1 1c-1 5-2 11-5 15h0-1l1-2-1-1v-1-1l2-4 1-2v1z" class="L"></path><path d="M497 220h2l-1 3-1-1v-1-1z" class="o"></path><path d="M500 214v1l-1 5h-2l2-4 1-2z" class="N"></path><defs><linearGradient id="T" x1="502.27" y1="227.481" x2="497.174" y2="223.951" xlink:href="#B"><stop offset="0" stop-color="#47413d"></stop><stop offset="1" stop-color="#605950"></stop></linearGradient></defs><path fill="url(#T)" d="M504 213c0-1 1-2 1-3 1-1 0-3 1-4 0 8-1 14-3 22l-2 2c-1 1-1 3-2 4s-2 2-2 3c-1 1-2 1-4 1 2-3 3-6 5-9 2-5 4-11 6-16z"></path><path d="M170 164l6 2c3 0 6 1 9 2s6 3 10 4l1-1 1 1-10 4c2 1 3 0 5 1-1 0-2 1-3 1h-2 0c-2 1-3 3-5 2-2 0-3 1-4 1v-1-1h0v-2c-1 0-2-1-3-1v-1l1-1-2-1-3-2v-1c-1-1-1-3-2-4v-1l1-1z" class="E"></path><path d="M188 172h2l-1 1h-3l2-1h0z" class="B"></path><path d="M185 171l3 1h0l-2 1-2-1c1 0 1 0 1-1z" class="V"></path><path d="M178 177h4 1v1 1c-1 0-1 0-1 1-2 0-3 1-4 1v-1-1h0v-2z" class="W"></path><path d="M178 177h4l-1 2c-1 0-1-1-2-1l-1 1h0v-2z" class="g"></path><path d="M187 176c2 1 3 0 5 1-1 0-2 1-3 1h-2 0c-2 1-3 3-5 2 0-1 0-1 1-1v-1-1l4-1z" class="q"></path><path d="M176 167c3 1 6 2 9 4 0 1 0 1-1 1-3 2-4 2-7 1h0c0-1 1-1 1-2-1 0-1-1-1-1l-3-1v-2h2z" class="I"></path><path d="M170 164l6 2h-1l1 1h-2v2l3 1s0 1 1 1c0 1-1 1-1 2h0 0c0 1-1 2 0 3h-2v-1l1-1-2-1-3-2v-1c-1-1-1-3-2-4v-1l1-1z" class="B"></path><path d="M170 166h3v2c-2-1-2-1-3-2z" class="R"></path><path d="M177 170s0 1 1 1c0 1-1 1-1 2h0 0l-1-1c-1 0-1 0-1-1l2-1z" class="K"></path><path d="M173 170c1 1 1 1 1 2v1l-3-2v-1h2z" class="I"></path><path d="M169 166h1c1 1 1 1 3 2-1 0-1 1-1 1l1 1h-2c-1-1-1-3-2-4z" class="V"></path><path d="M129 381h0c1 1 1 2 1 3l1 2v2c0 1 0 2-1 3h0c-2 1-4 7-5 10 0 3-2 4-4 7-3 2-5 5-8 7h-1c-1 1-2 1-3 2-2 2-5 2-8 2h0c-1-1-2-1-2-1 1 0 2 0 3-1l5-4 2-3 3-3c1 0 2 1 3 2 1-2 2-3 3-5 1-1 2-3 3-4l3-6c1-1 1-2 1-3s0-2-1-3h1 1c0-2 0-4 1-5h0c1-1 1-1 1-2v3c0-1 0-2 1-3z" class="E"></path><path d="M125 388h1c-1 1-1 2-1 3 0-1 0-2-1-3h1z" class="B"></path><path d="M110 414h1c-1 1-1 2-2 2h0c-1 1-3 1-4 0l5-2z" class="Z"></path><path d="M107 413v1c2 0 3-1 4-2h1l-2 2-5 2-3 1 5-4z" class="N"></path><path d="M109 410l3-3c1 0 2 1 3 2-1 1-1 2-3 3h-1c-1 1-2 2-4 2v-1l2-3z" class="f"></path><path d="M129 381h0c1 1 1 2 1 3l1 2v2c0 1 0 2-1 3h0c-2 1-4 7-5 10v-1-1c0-1 0-1 1-2 1-3 1-5 2-8h0c0-2 1-3 0-5 0-1 0-2 1-3z" class="U"></path><path d="M131 386v2c0 1 0 2-1 3h0c-1-1-1-1-1-2 1-1 1-1 1-2h0l1-1z" class="S"></path><path d="M129 381h0c1 1 1 2 1 3l1 2-1 1-1-1v-5z" class="f"></path><path d="M129 386v-2h1l1 2-1 1-1-1z" class="D"></path><path d="M412 124c3 2 4 4 5 7l1 1c0 4-1 5-3 8h-1c0 1-1 1-2 2v2h-1c-2 1-3 2-5 2l-3 1h-1-2-1v-2l-1-1c-1 0-1 0-1-1h-1l4-3 1-1c5-5 7-10 11-15z" class="W"></path><path d="M412 132v1h0l1 1v-2l1 1v2l-1 1h-1v-4z" class="z"></path><path d="M417 131l1 1c0 4-1 5-3 8h-1-2l-1-1h-2 0c0-1 2-1 3-2v2c2 0 2-4 4-5 1-1 1-2 1-3z" class="d"></path><path d="M403 140l2-2h1v2h-1c1 1 1 1 2 1l2-2h0c0 1 0 2-1 3-1 0-1 1-2 2v2l-3 1h-1c1-1 2-1 3-2-1-1-1-2-1-4 0 0 0-1-1-1z" class="n"></path><path d="M409 139h2l1 1h2c0 1-1 1-2 2v2h-1c-2 1-3 2-5 2v-2c1-1 1-2 2-2 1-1 1-2 1-3z" class="h"></path><path d="M409 139h2l1 1-4 2c1-1 1-2 1-3zm-3 5h1 0c2 0 2-1 4-1v1c-2 1-3 2-5 2v-2z" class="v"></path><path d="M400 140l1-1c1 0 2 1 2 1 1 0 1 1 1 1 0 2 0 3 1 4-1 1-2 1-3 2h-2-1v-2l-1-1c-1 0-1 0-1-1h-1l4-3z" class="e"></path><path d="M400 140l1-1c1 0 2 1 2 1 1 0 1 1 1 1l-3 1c1 0 1 0 1-1-1-1-1-1-2-1z" class="m"></path><path d="M401 142l3-1c0 2 0 3 1 4-1 1-2 1-3 2h-2c1-1 1-2 1-3v-1-1z" class="X"></path><path d="M401 144c2 1 2 1 4 1-1 1-2 1-3 2h-2c1-1 1-2 1-3z" class="m"></path><path d="M114 305l-2-2 1-1h1l2 1h4 1 2 4 3c-1 1-1 1-1 2v2h1v1c1 1 2 2 2 3h0l-1 2-4 2v1h-3-1v1c-1 0-1 0-2-1v1l-1 1h-1v-1h-3-1-2v-2l-1-1h0c0-1 0-1-1-2 0 0 1-1 1-2v-3-1h1c0-1 1-1 1-1z" class="b"></path><path d="M123 306c1-1 1-1 3-1v1c-1 1-1 1-3 1h-1l1-1z" class="r"></path><path d="M127 305h2v2h1v1c-1 0-2 1-3 1l-1-1 1-1v-2z" class="q"></path><path d="M127 305h2v2h-2v-2z" class="z"></path><path d="M127 303h3c-1 1-1 1-1 2h-2s-1 0-1 1v-1c-2 0-2 0-3 1 0-1 0-1-1-2l1-1h4zm-5 5h2l1 1s0 1-1 2c0 0-1 1-2 1l-4-2c1-1 1-1 2-1h1l1-1z" class="g"></path><path d="M122 308l1 1v2c-1 0-1 0-2-1v-1l1-1z" class="AN"></path><path d="M127 309c1 0 2-1 3-1 1 1 2 2 2 3h0l-1 2-4 2v1h-3v-1l1-4 1-1v-2l1 1z" class="r"></path><path d="M127 309c1 0 2-1 3-1 1 1 2 2 2 3h0-1c-1 0-1 0-2 1h0-1v-3h-1z" class="y"></path><path d="M126 310l1 1h0c-1 2-1 3-1 4h1v1h-3v-1l1-4 1-1zm-12-5l-2-2 1-1h1l2 1h4 1 2l-1 1c1 1 1 1 1 2l-1 1c-1-1-1-1-2-1l-1 2v1h1c-1 0-1 0-2 1 0 0-1-1-2-1h0c-1 0-1-3-2-4z" class="W"></path><path d="M116 304c1 1 2 1 3 1v1c-1 1-2 1-3 1v-3z" class="y"></path><path d="M114 305l-2-2 1-1h1l2 1v1 3c1 1 1 1 0 2h0c-1 0-1-3-2-4z" class="j"></path><path d="M113 306c0-1 1-1 1-1 1 1 1 4 2 4h0c1 0 2 1 2 1l4 2c1 0 2-1 2-1h1l-1 4v1h-1v1c-1 0-1 0-2-1v1l-1 1h-1v-1h-3-1-2v-2l-1-1h0c0-1 0-1-1-2 0 0 1-1 1-2v-3-1h1z" class="m"></path><path d="M119 316v-3h1l1 1c0 1 0 2-1 2h-1z" class="W"></path><path d="M116 310h1c0 1 1 1 2 2l-1 1-1 1v-1c-1-1-1-2-1-3z" class="n"></path><path d="M113 306h1v1l-1 1v1h1v2h-1-1v-1-3-1h1z" class="C"></path><path d="M121 314h1l2 1v1h-1v1c-1 0-1 0-2-1v1c-1 0-1 0-2-1h0 1c1 0 1-1 1-2z" class="b"></path><path d="M122 314l2 1v1h-1 0c-1-1-1-1-1-2z" class="Y"></path><path d="M114 309v2c1 2 1 2 1 4-2-1-1 0-2 0l-1-1h0c0-1 0-1-1-2 0 0 1-1 1-2v1h1 1v-2z" class="k"></path><path d="M113 315c1 0 0-1 2 0 1 1 2 1 3 1h1c1 1 1 1 2 1l-1 1h-1v-1h-3-1-2v-2z" class="P"></path><path d="M438 313h1l1 1h1l3 1 1 1h0l3 3s-1 0-1 1c0 0-1 1-2 1l2 2 7 4-1 1h-6 0-3c-2 0-3-1-5-1h0-2l-1-2h-2v2h-2l-1-1h-3 0c0-1 1-2 1-2l-1-1h0l1-1v-1-4-2l1-1s1 0 2-1c1 1 2 1 3 1l1 1v-1s1 0 1-1h0 1z" class="T"></path><path d="M429 315l3 3v1l-1 1h0c1 0 1 0 2 1h-3c0-1 0-3-1-4v-2z" class="C"></path><path d="M430 321h3c1 1 2 2 3 4h-2l-1-1c0-1-2-2-3-3z" class="L"></path><path d="M447 323l7 4-1 1h-6 0c1 0 2 0 3-1v-1c-1 0-2-1-2-1-1-1-1-1-2-1v-1h1z" class="B"></path><path d="M429 317c1 1 1 3 1 4 1 1 3 2 3 3l1 1v2h-2l-1-1h-3 0c0-1 1-2 1-2l-1-1h0l1-1v-1-4z" class="b"></path><path d="M438 324l-4-4v-3c1 1 2 1 3 2 1 0 2 1 3 1 1 1 3 1 5 3h-2c-1 0-1 0-1 1l-1 1c-1 0-2-1-3-1z" class="AH"></path><path d="M438 324l1-1c1 0 1 0 2 1h1l-1 1c-1 0-2-1-3-1z" class="AO"></path><path d="M438 313h1l1 1h1l3 1 1 1h0l3 3s-1 0-1 1c0 0-1 1-2 1l2 2h-1-1c-2-2-4-2-5-3-1 0-2-1-3-1v-2c-1-1-2-1-2-3l1 1v-1s1 0 1-1h0 1z" class="V"></path><path d="M438 313h1v3h0c-1 0-2-1-3-1v-1s1 0 1-1h0 1z" class="n"></path><path d="M439 313l1 1h1l3 1 1 1h0l3 3s-1 0-1 1c0 0-1 1-2 1l-6-5h0v-3z" class="q"></path><path d="M445 316h0l3 3s-1 0-1 1c-1 0-2-1-2-2h-1c0-1 1-1 1-2z" class="k"></path><path d="M440 314h1l3 1 1 1c0 1-1 1-1 2-2-1-3-3-4-4z" class="Q"></path><path d="M402 495l-4 9c1 0 2 0 2-1h1l-1 2h1l-1 1v4c-1 2-3 3-4 4l-3 3-9 9c-1 1-2 3-4 4-2 2-4 5-6 7-3 2-5 4-8 7l-1-2 12-14c3-4 6-9 9-13 2-2 5-4 6-6 2-1 3-3 4-5 1-1 1-3 2-4 1 0 1-1 1-1 1-1 2-2 3-4z" class="V"></path><path d="M400 505h1l-1 1v4c-1 2-3 3-4 4l-3 3-9 9v-1l16-20z" class="C"></path><path d="M80 208c1 2 1 2 1 4v-2-6h0v2c2 1 2 3 2 4v1 2h2 0c0 1 1 3 1 4h2 0c1 2 2 5 2 7h0c1 1 1 2 2 3 2 3 4 8 7 11l3 3h1l-1 2h0c0 1 0 1 1 2l-1 1-2 1h-2-1v-1h-1v-1c-2-3-5-6-7-10-3-5-5-10-7-16 0-3-2-7-2-11z" class="T"></path><path d="M102 243h0c0 1 0 1 1 2l-1 1-2 1h-2-1v-1l5-3z" class="h"></path><g class="C"><path d="M88 217c1 2 2 5 2 7 0 1 0 2-1 2l-3-6h0 0v-1h1 1v-2z"></path><path d="M90 224h0c1 1 1 2 2 3 2 3 4 8 7 11l3 3h-1c-3-2-5-5-7-8-2-2-3-3-4-5l-1-2c1 0 1-1 1-2z"></path></g><path d="M90 224h0v1s0 1 1 2h-1v1l-1-2c1 0 1-1 1-2z" class="D"></path><path d="M85 213h0c0 1 1 3 1 4h2 0v2h-1-1v1h0c2 9 7 17 14 23h-1v1c-7-5-10-13-13-20-1-3-3-8-3-11h2z" class="P"></path><path d="M80 208c1 2 1 2 1 4v-2-6h0v2c2 1 2 3 2 4v1c-1 0-1 1-1 2 2 8 4 16 8 23l4 6c1 1 2 1 2 2v1c-2-3-5-6-7-10-3-5-5-10-7-16 0-3-2-7-2-11z" class="u"></path><path d="M81 206c2 1 2 3 2 4v1c-1 0-1 1-1 2 0-3-1-5-1-7z" class="P"></path><path d="M414 500v1l-1 1h0c1 1 1 1 1 2h-1c0 1-1 2-1 3l-11 10-12 13-18 18c-1 1-3 3-4 3-2 0-4 3-6 3 1-2 3-4 5-6l11-10c4-5 8-11 13-15l4-4v-1l-1-1 3-3c0 1 1 1 1 1h1l1-1c2-1 4-3 5-5 3-3 6-6 10-9z" class="K"></path><path d="M395 519h1c0 1-1 1-2 2 0-1 1-1 1-1-2 0-3 3-5 3l4-4h1z" class="R"></path><path d="M396 514c0 1 1 1 1 1v1s-1 0-1 1c-1 0-1 1-1 2h-1v-1l-1-1 3-3z" class="E"></path><defs><linearGradient id="U" x1="174.686" y1="139.052" x2="171.59" y2="127.839" xlink:href="#B"><stop offset="0" stop-color="#b8a69f"></stop><stop offset="1" stop-color="#d4c9c5"></stop></linearGradient></defs><path fill="url(#U)" d="M167 131c0-2 1-4 3-5 0-1 0-1 1-2h0 1c1 1 1 2 2 2l1 2 4 6 3 4 2 2 4 2v1l5 3-2 1h-3v1l-4-1c0 1-1 1-1 1-2 0-4-1-6-2-1 0-3-2-4-3-3-2-5-4-6-7v-1l-1-1 1-3z"></path><path d="M179 134l3 4v2l-1 3h-1l-1-1h-1c0 1 0 1 1 1l-2 2c0-1-1-1-2-1 0-2-2-3-4-4h0l1-1c1-1 1-1 1-2l1 1c1 0 1 1 2 2 1-1 1-1 2-1s1 0 1-1h0c-1-1-2-1-3-1l1-1c1 0 1-1 2-2z" class="s"></path><path d="M171 140h0l1-1c1 0 2 1 2 2 2-1 3-1 5-1l1 1-1 1h-1c0 1 0 1 1 1l-2 2c0-1-1-1-2-1 0-2-2-3-4-4z" class="g"></path><path d="M167 131c0-2 1-4 3-5 0-1 0-1 1-2h0 1c1 1 1 2 2 2l1 2c-2 0-3-1-5-1-1 1-1 3-2 5 0 2 0 3 1 5h1c0 1 0 2 1 3 2 1 4 2 4 4 1 0 2 0 2 1h3l-1 1c2 0 3 1 4 1h1c0 1-1 1-1 1-2 0-4-1-6-2-1 0-3-2-4-3-3-2-5-4-6-7v-1l-1-1 1-3z" class="v"></path><path d="M182 138l2 2 4 2v1l5 3-2 1h-3v1l-4-1h-1c-1 0-2-1-4-1l1-1h-3l2-2c-1 0-1 0-1-1h1l1 1h1l1-3v-2z" class="i"></path><path d="M188 147c-1-1-2-1-3-2v-1h4c0 1 1 2 2 3h-3z" class="F"></path><path d="M182 138l2 2 4 2-1 1c-1 1-2 0-3 1v3h-1v-5c0-1-1-1-1-2v-2z" class="a"></path><path d="M182 138l2 2-1 2c0-1-1-1-1-2v-2z" class="b"></path><path d="M179 142l1 1h1l1-3c0 1 1 1 1 2v5c-1 0-2-1-4-1l1-1h-3l2-2c-1 0-1 0-1-1h1z" class="Y"></path><path d="M179 143l1 1v1h-3l2-2z" class="W"></path><path d="M388 275h4c2 1 5 1 7 1v1l-2 6h-1l1 1-1 3-1 1-1 6h0-3-8-1v-1-3-2c0-1 0-3-1-4l-1-8h4l3 1c1 0 2-1 3-1l-2-1z" class="AO"></path><path d="M388 275h4c2 1 5 1 7 1v1l-2 6-1-1h-2-2l-1-1h3 1v-1c-1-2-8-2-11-3v-1l3 1c1 0 2-1 3-1l-2-1z" class="AV"></path><path d="M388 275h4c2 1 5 1 7 1v1h-2-10c1 0 2-1 3-1l-2-1z" class="AE"></path><path d="M382 288v-3c1-1 2 0 3-1h8c1 0 2 0 3-1l1 1-1 3-1 1-1 6h0-3-8-1v-1-3-2z" class="AX"></path><path d="M382 293c3 1 6 0 9 0 1 1 2 1 3 1h0-3-8-1v-1z" class="AO"></path><defs><linearGradient id="V" x1="342.229" y1="539.034" x2="350.871" y2="548.577" xlink:href="#B"><stop offset="0" stop-color="#212120"></stop><stop offset="1" stop-color="#4e4740"></stop></linearGradient></defs><path fill="url(#V)" d="M328 566h0v-1c2-5 6-9 10-13 1-2 2-3 3-5 3-4 7-9 11-13 5-6 8-11 13-17 1 2 1 3 1 4l-1 1h2l-38 48-1-1h-1c1 0 1-1 1-2 0 0 1 0 1-1h-1z"></path><path d="M162 475l1 1c1 3 3 5 5 8 3 5 5 10 9 14 2 4 4 6 6 9 1 3 3 4 4 6-2-1-6-4-7-6-1-3-3-4-5-6-1 0-2 0-2 1s2 2 2 3l15 15c2 2 4 3 5 5v1c-1 0-4-3-5-4h-2c-2-1-3-2-4-3-3-2-5-5-7-8-2-2-5-5-6-8-1-1-2-3-3-4 0-1-2-2-2-3 1 0 2 1 3 2v-1l-5-8c-2-3-3-7-5-9l-1-1 1-3c0 1 0 1 1 1s1-1 2-2z" class="H"></path><path d="M278 106s3-2 3-3c4-4 7-8 11-12l5 5c0-1 1-1 2-1l5 6h3l1 1h1l3 4c1 2 5 6 5 8h-1c3 4 7 6 10 10l-2 1 2 2c0 1-1 2-1 3l-15-16c-6-6-12-13-18-18l-12 12h-1c-1 0-1 1-2 1v1-1s1-1 1-2v-1z" class="v"></path><path d="M315 113l1 1c3 4 7 6 10 10l-2 1 2 2c0 1-1 2-1 3l-15-16h0c1 0 2 1 3 2l9 10c1-1 1-1 2-1-3-2-5-4-7-7-1-1-3-2-3-3h0v-1h1v-1z" class="d"></path><path d="M315 113l1 1c3 4 7 6 10 10l-2 1-10-10v-1h1v-1z" class="Z"></path><path d="M297 96c0-1 1-1 2-1l5 6h3l1 1h1l3 4c1 2 5 6 5 8h-1l-1-1v1h-1c-7-5-12-12-17-18z" class="E"></path><path d="M304 101h3l1 1c1 2 3 4 3 6-3-2-4-4-7-7z" class="u"></path><path d="M309 102l3 4c1 2 5 6 5 8h-1l-1-1-4-5c0-2-2-4-3-6h1z" class="v"></path><defs><linearGradient id="W" x1="333.221" y1="532.446" x2="320.318" y2="523.372" xlink:href="#B"><stop offset="0" stop-color="#282929"></stop><stop offset="1" stop-color="#605349"></stop></linearGradient></defs><path fill="url(#W)" d="M340 517l16-18c1-2 4-8 6-8l-6 11c-8 12-18 24-29 33-3 3-7 6-10 8-5 3-10 6-14 10-1 2-2 4-3 5-1-1-1-2-1-3h-1c0-1 1-2 2-3v-1c1 0 2-1 2-2 2-2 4-4 6-5h1c1 0 2-1 3-2l2-1c3-2 16-13 17-16v-1c0-1 2-1 2-2 2-2 5-4 7-5z"></path><path d="M300 552h0c0 1 0 1 1 1l-1 2h-1-1c0-1 1-2 2-3z" class="i"></path><path d="M331 525v-1c0-1 2-1 2-2 2-2 5-4 7-5-3 4-6 8-9 11-8 8-16 15-26 20 1-2 2-2 4-4 1 0 2-1 3-2l2-1c3-2 16-13 17-16z" class="E"></path><path d="M409 299c0-1-7-5-8-6v-1h2c1 1 3 2 4 2v-1l4 1 4 1h0c1 1 3 2 4 2l17 4 6 1h3c0 2-1 3-2 5-1 0-2 0-3-1h0c-1 0-2-1-3-1s-1-1-2-1v1h-1c1 2 2 2 3 3h0c1 2 2 2 3 3l-2 2h0-1 0c0 1-1 1-1 1v1l-1-1c-1 0-2 0-3-1h0-2 0 0v-2l-1-3h0c0-1 0-1-1-1 0 1-1 1-2 1v-1c-1-1-3-1-5-2s-5-2-7-4h0c-1-1-3-1-5-2z" class="Ae"></path><path d="M421 301c3-1 9 1 12 2h0l-1 1 1 1v1c-2-1-4-2-6-2h0c-1 0-2-1-3-1 0-1-1-1-2-1h0l-1-1z" class="X"></path><path d="M409 299c0-1-7-5-8-6v-1h2c1 1 3 2 4 2l8 5 6 2 1 1h0c1 0 2 0 2 1h-1l-2 2c-2-1-5-2-7-4h0c-1-1-3-1-5-2z" class="T"></path><path d="M415 299l6 2 1 1h0c1 0 2 0 2 1h-1c-2-1-3-1-5-1-1 0-2-2-3-3z" class="R"></path><path d="M424 303c1 0 2 1 3 1h0c2 0 4 1 6 2v-1l-1-1 1-1 2 1v1h-1c1 2 2 2 3 3h0c1 2 2 2 3 3l-2 2h0-1 0c0 1-1 1-1 1v1l-1-1c-1 0-2 0-3-1h0-2 0 0v-2l-1-3h0c0-1 0-1-1-1 0 1-1 1-2 1v-1c-1-1-3-1-5-2l2-2h1z" class="M"></path><path d="M434 310c1 1 3 2 4 3h0-1 0c0 1-1 1-1 1v1l-1-1c-1 0-2 0-3-1h0c1-1 1-1 1-2l1-1z" class="W"></path><path d="M430 311l-1-3h0l2 1 3 1-1 1c0 1 0 1-1 2h-2 0 0v-2z" class="n"></path><path d="M431 309l3 1-1 1h-3v-1l1-1z" class="AT"></path><defs><linearGradient id="X" x1="284.012" y1="63.123" x2="299.517" y2="70.823" xlink:href="#B"><stop offset="0" stop-color="#e4dbd7"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#X)" d="M291 56v-4c0-1 1-2 1-4h0v1c0 1 1 2 1 3v-4l3 15c1 4 2 8 4 12-1 2 0 3 0 4s0 2-1 2l1 3-1 1 2 6 3 5 5 6h-1l-1-1h-3l-5-6-4-5c-1-1-2-2-2-3v-1h-3-1c2-8 2-16 1-23l1-7z"></path><path d="M293 48l3 15c-1 1-1 2-1 4h0-1 0c-1-2-1-4-1-6v-9-4z" class="b"></path><path d="M293 61c1 2 2 4 1 6h0c-1-2-1-4-1-6z" class="c"></path><defs><linearGradient id="Y" x1="299.213" y1="67.027" x2="294.278" y2="74.649" xlink:href="#B"><stop offset="0" stop-color="#989286"></stop><stop offset="1" stop-color="#b09f9c"></stop></linearGradient></defs><path fill="url(#Y)" d="M295 67h0c0-2 0-3 1-4 1 4 2 8 4 12-1 2 0 3 0 4s0 2-1 2l1 3-1 1c-1-1-1-2-2-3-1-2-2-6-3-8v-7h0 1z"></path><path d="M298 79v-2h1c0 1 0 2 1 2 0 1 0 2-1 2l-1-2h0z" class="g"></path><path d="M294 67h0 1c0 4 1 8 3 12h0l1 2 1 3-1 1c-1-1-1-2-2-3-1-2-2-6-3-8v-7z" class="I"></path><path d="M298 79l1 2 1 3-1 1c-1-1-1-2-2-3v-1l1 1h0v-2h0v-1z" class="L"></path><path d="M294 74c1 2 2 6 3 8 1 1 1 2 2 3l2 6 3 5 5 6h-1l-1-1h-3l-5-6-4-5c-1-1-2-2-2-3v-1h-3l3-1c1-1 0-3 1-4v-7z" class="g"></path><path d="M301 91l3 5h0c-2-1-3-2-4-4l1-1z" class="r"></path><path d="M294 74c1 2 2 6 3 8 1 1 1 2 2 3l2 6-1 1-4-4c0-2-1-3-2-5h0v-1 6c1 0 1 0 1 1v1c-1-1-2-2-2-3v-1h-3l3-1c1-1 0-3 1-4v-7z" class="Ae"></path><path d="M468 355l1-1 1 1v3h0l1 1 1-1 1 1-1 1c0 1-1 1-1 2v1l1 2c0 1 0 4 1 5-2 6-6 8-9 13-2 2-3 5-4 7v-1h-1c-2-5 0-9 0-14 0-3-1-5-1-8 1-1 0-4 1-5h1l1 1h1v-2h1v-1-1h-1c-1 0-1 0-1-1l1-1 1 1 1-1h2 1l1-1v-1z" class="T"></path><path d="M472 358l1 1-1 1c0 1-1 1-1 2v1l1 2c0 1 0 4 1 5-2 6-6 8-9 13-2 2-3 5-4 7v-1c0-2 2-6 4-8l3-3c2-3 3-4 4-7v-2h-1l-1 1v-1h-1c0-1 0-2 1-3h-2-1v-1l2-2c0-1-1-1-1-1l1-1 1-1h0l1-2h0l1 1 1-1z" class="R"></path><path d="M469 361l2 2 1 2c-1-1-2-1-3-1v-3z" class="J"></path><path d="M468 361l1-1v1 3-1h-1 0c0-1-1-1-1-1l1-1z" class="D"></path><path d="M468 363h0l1 3h-2-1v-1l2-2z" class="o"></path><path d="M469 369v-4h0c1 1 1 2 2 4h1c0 1 0 1-1 2v-2h-1l-1 1v-1z" class="V"></path><path d="M472 358l1 1-1 1c0 1-1 1-1 2v1l-2-2v-1h0l1-2h0l1 1 1-1z" class="F"></path><path d="M472 358l1 1-1 1c0 1-1 1-1 2l-1-1c0-1 0-1 1-2l1-1z" class="J"></path><path d="M468 355l1-1 1 1v3l-1 2h0l-1 1-1 1s1 0 1 1l-2 2v1h1 2c-1 1-1 2-1 3h1v1c-1 3-2 5-4 7 1-2 1-4 1-5v-3-2c0-1-2-2-2-3s-1-2-1-3v-1-1h-1c-1 0-1 0-1-1l1-1 1 1 1-1h2 1l1-1v-1z" class="G"></path><path d="M467 366h2c-1 1-1 2-1 3v1l-1-1v-3z" class="Q"></path><path d="M465 360c1 0 1 1 2 2 0 0 1 0 1 1l-2 2v1c0-1 0-2-1-2v-4z" class="P"></path><path d="M468 355l1-1 1 1v3l-1 2h0l-1 1-1 1c-1-1-1-2-2-2l-2-2 1-1h2 1l1-1v-1z" class="E"></path><path d="M470 355v3l-1 2-1-1v-1c1-1 1-2 2-3z" class="D"></path><path d="M463 358l1-1h2 1c-1 1-1 1-1 3l2-1 1 1h0l-1 1-1 1c-1-1-1-2-2-2l-2-2z" class="C"></path><path d="M468 359l1 1h0l-1 1h-1s0-1-1-1l2-1z" class="B"></path><path d="M413 453c0 1 1 1 1 1v1l-4 5c-1 2-3 4-4 6s-2 4-3 7l-5 9h-3l-3 5-2 4-4 6-4 6-2 2v1c-1 0-2 0-3-1-1 2-4 4-6 6l-2 2h0l1-2c0-1-1-1-1-1 1-1 1-2 2-3l5-7 3-4 3-3v-2l1-2c2-3 4-5 6-7l3-2c1-2 3-4 4-7 1-1 2-3 3-4l1 1c0-1 1-2 1-2h1l2-2c2-4 5-7 7-11l1-1 1-1z" class="i"></path><path d="M413 453c0 1 1 1 1 1v1l-4 5v-1c1-2 2-3 2-5l1-1z" class="c"></path><path d="M403 473l-5 9h-3l5-8c1-1 1-1 3-1z" class="C"></path><path d="M376 503c3-5 7-9 10-13 2-2 4-6 6-8 0 0 1 0 0 1s-1 1-1 2l-2 2c0 1 0 2-1 2l-1 1c0 3-3 6-5 8-1 0-1 1-2 2 0 1-3 2-4 3z" class="F"></path><path d="M399 469l1 1-8 12c-2 2-4 6-6 8-3 4-7 8-10 13-1 2-3 4-5 6l-1 2c0-1-1-1-1-1 1-1 1-2 2-3l5-7 3-4 3-3v-2l1-2c2-3 4-5 6-7l3-2c1-2 3-4 4-7 1-1 2-3 3-4z" class="E"></path><path d="M383 489h1 1 1l-4 4v-2l1-2z" class="Q"></path><path d="M389 482l3-2-6 9h-1-1-1c2-3 4-5 6-7z" class="D"></path><path d="M460 187c2 0 5 1 7 2l-1 1h1l2 1c1 1 1 1 2 1 1 1 3 3 4 5l-1 1v1l-1 1 2 3 1 3v1l2 2c0 1 0 2 1 3 0 1 1 1 1 1v5h-1c-1-1-1-2-3-3v1s0 1 1 1c0 0 1 1 1 2 1 2 1 3 1 5l-1 1c-1 0-2-2-3-1h-1c-1 0-1 1-2 0v-3l-2-5v-3c-1-4-3-7-5-10l-3-4c-2-3-4-7-7-9v-2h0 4l1-1z" class="Ac"></path><path d="M470 206h1c1 0 0 1 1 2-1 0-1-1-2-1v-1zm-2-9h0l1 1v3h-1v-4z" class="B"></path><path d="M460 187c2 0 5 1 7 2l-1 1h1l2 1c1 1 1 1 2 1h-3v1h0c-1-1-2-1-2-3-3-2-8-2-11-2h0 4l1-1z" class="p"></path><path d="M468 193v-1h3c1 1 3 3 4 5l-1 1v1l-1 1c-1-3-3-5-5-7z" class="Q"></path><defs><linearGradient id="Z" x1="444.447" y1="384.97" x2="455" y2="388.337" xlink:href="#B"><stop offset="0" stop-color="#201f1e"></stop><stop offset="1" stop-color="#433d35"></stop></linearGradient></defs><path fill="url(#Z)" d="M449 367h1l1 1 1 1v2 7c0 3-1 6-2 10 0 1 1 2 0 4h0v7l1 8v2l1 1v2 2l-1-1v1 2c1 1 2 2 2 3l1 1v1h-1-1v1 1l-3-3c-1-1-2-2-3-2s-1 0-1-1c-2-2-3-4-4-6-1-3-3-5-4-7v-3-1c1 1 1 1 1 2h0l2 2v-1c1-1 1-3 1-5s0-3 1-4 1-5 1-6c1 0 1 1 1 1h0 1c0-1 1-2 1-3v-2-1-1c2-2 2-5 2-7 1-3 1-5 1-8z"></path><path d="M449 410v-4h1l1 1v2l1 1v2 2l-1-1v1l-1-1c-1-1-1-2-1-3z" class="F"></path><path d="M438 402c1 1 1 2 1 3l1 1v-1h0 0c2-2 2-4 2-6 0 0 0-1-1-1h1v-3h1 0v1h0v-1h0c1 1 1 1 1 2l1 1v2 1-3h0v-3-2c1 0 1 1 1 2v1h0c-1 3 1 7 1 10l2 4c0 1 0 2 1 3l1 1v2c1 1 2 2 2 3l1 1v1h-1-1v1 1l-3-3c-1-1-2-2-3-2s-1 0-1-1c-2-2-3-4-4-6-1-3-3-5-4-7v-3-1c1 1 1 1 1 2h0z" class="H"></path><path d="M447 406l2 4c0 1 0 2 1 3h-1v1c-1 0-1 0-1 1-1-1-2-3-2-5-1 0-1-1-1-2 1-1 1 0 2-1v-1z" class="M"></path><path d="M446 410v-2c1 1 2 4 3 5v1c-1 0-1 0-1 1-1-1-2-3-2-5z" class="G"></path><path d="M450 413l1 1v2c1 1 2 2 2 3l1 1v1h-1-1v1 1l-3-3 1-1v-1l-2-3c0-1 0-1 1-1v-1h1z" class="P"></path><path d="M450 413l1 1v2l-1 1c0-1-1-2-1-3v-1h1z" class="L"></path><path d="M473 370l1-3c2 2 3 4 4 8 1 6 0 11-2 17l1 1h-1c0 2-1 4-2 6-1 1-2 1-3 3 1 1 2 2 3 4h-1l-2 2c-1-1-1-2-2-3l-1-1-1 1c-3-3-4-6-5-10h-1l-1 1c0-1 0-2-1-2 1-1 1-3 1-4h0c1-2 2-5 4-7 3-5 7-7 9-13z" class="Z"></path><path d="M463 389h1c0 1-1 2-1 3h-1c0-2 0-2 1-3z" class="K"></path><path d="M460 390v2c1 1 1 2 2 3h-1l-1 1c0-1 0-2-1-2 1-1 1-3 1-4zm4 4c1 1 2 1 2 2l1 2 1 3c1 1 1 3 1 4l-1-1c-3-3-4-6-4-10z" class="H"></path><path d="M467 394c0-1 0-3-1-3 1-2 1-3 1-4l1 1c0 1-1 2-1 3 0 2 2 5 3 7-1 0-1 0-1 1h0c-1 0-1-1-2-1l-1-2c0-1-1-1-2-2 1-1 1-2 2-2 0 0 0 1 1 1v1z" class="G"></path><path d="M464 394c1-1 1-2 2-2 0 0 0 1 1 1v1h0c0 1 0 0 1 1v2h0-1v-1h-1c0-1-1-1-2-2z" class="f"></path><path d="M476 392l1 1h-1c0 2-1 4-2 6-1 1-2 1-3 3 1 1 2 2 3 4h-1l-2 2c-1-1-1-2-2-3 0-1 0-3-1-4l-1-3c1 0 1 1 2 1h0c0-1 0-1 1-1v2h1c2-2 3-5 5-8z" class="P"></path><path d="M344 383s1 0 2 1c1 0 2 2 3 2 1-1 3 0 5 0l4 1 1-2c0 1-1 2 0 3l-1 1h-1v2h1v1l-2 24c-1 6-1 12-2 18l-1 1v2c-1 0-1 1-1 1h0l-1 5c0-3-1-7 0-10l-1-18-1-3v-3l-2-7-4-16 1-1v-2z" class="AA"></path><path d="M354 388c1 1 2 1 3 1v2c-1-1-2-1-3-2v-1z" class="AW"></path><path d="M355 387c1 0 2 1 4 1l-1 1h-1c-1 0-2 0-3-1-1 0-2 1-3 0 1 0 2 0 3-1h1z" class="AH"></path><path d="M349 386c1-1 3 0 5 0l4 1 1-2c0 1-1 2 0 3-2 0-3-1-4-1l-4-1h-2z" class="AR"></path><path d="M350 411c2 5 2 10 2 15 0 2 0 5-1 7h0l-1-18-1-3 1 1h0v-2z" class="AL"></path><path d="M344 383s1 0 2 1c1 0 2 2 3 2h2c-2 1-2 1-4 0h-1 0 0 0c1 1 1 3 1 4 1 1 0 3 0 4v1 1l-2-6c0-2 0-4-1-5v-2z" class="AX"></path><path d="M344 385c1 1 1 3 1 5l2 6 3 15v2h0l-1-1v-3l-2-7-4-16 1-1z" class="AC"></path><path d="M376 259v3l1 5v1l1 2h1c0 1 0 2 1 3v3l1 8c1 1 1 3 1 4v2c-1 0-1 0-1 1l-1-1c0 3 1 8 0 10v-1c-2 0-5 2-7 2l-1-6c0-1-1-2-1-3l-2-8v-1c-1-2-2-5-3-7 0-2-2-4-2-6v-1h1v-1c1 0 1 0 1-1h1c1 0 1-1 2-2 0 0 1 0 1-1 1 0 1-1 2-1 1-1 2-1 2-2l2-2z" class="AA"></path><path d="M379 270c0 1 0 2 1 3v3l1 8c1 1 1 3 1 4v2c-1 0-1 0-1 1l-1-1h0v-2l-1-12c-1-2-1-4-1-5v-1h1z" class="AY"></path><path d="M381 284c1 1 1 3 1 4v2c-1 0-1 0-1 1l-1-1h0v-2c1-1 1-2 1-4z" class="AS"></path><path d="M380 290h0c0 3 1 8 0 10v-1c-2 0-5 2-7 2l-1-6h1c1 0 1-2 2-2 0 0 1 0 1-1 1-1 3-1 4-2z" class="AH"></path><path d="M376 259v3l1 5v1l1 2v1c0 1 0 3 1 5-1 0-2 1-2 2h0c-1 1-2 1-2 2-2 1-4 3-6 4v-1c-1-2-2-5-3-7 0-2-2-4-2-6v-1h1v-1c1 0 1 0 1-1h1c1 0 1-1 2-2 0 0 1 0 1-1 1 0 1-1 2-1 1-1 2-1 2-2l2-2z" class="AM"></path><path d="M376 262l1 5v1h-2 0 0c0-1 0-2 1-3h0v-3z" class="AV"></path><path d="M366 276l1-1c1 0 1-1 2-1 1-1 2-2 3-2l3-3 1 1-1 1v1l1-1 1 1h0l1-1c0 1 0 3 1 5-1 0-2 1-2 2h0c-1 1-2 1-2 2-2 1-4 3-6 4v-1c-1-2-2-5-3-7z" class="Af"></path><path d="M373 279h1l-4 4-1-1c1-1 2-2 4-3z" class="AV"></path><path d="M378 271c0 1 0 3 1 5-1 0-2 1-2 2-1 0-2 1-3 1h-1l3-3c0-1-1-1-1-2l2-2h0l1-1z" class="AH"></path><path d="M114 364v4c0 1 0 3 1 4h0c-1 2 0 3 1 5 0 0-1 0-1-1l9 14 1 1c0 1 0 2-1 3l-3 6c-1 1-2 3-3 4-1 2-2 3-3 5-1-1-2-2-3-2l-3 3v-2s1-1 1-2l3-5s-1-2-2-2c-1-1-3-5-3-6v-2c-2-5-2-10-1-16 0-3 1-5 2-8h1l2-1h1c0-1 1-1 1-2z" class="Z"></path><path d="M124 390l1 1c0 1 0 2-1 3l-1-1c1-1 1-2 1-3z" class="C"></path><path d="M123 393l1 1-3 6-1-1v-1c2-1 2-3 3-5z" class="B"></path><path d="M120 399l1 1c-1 1-2 3-3 4-1 2-2 3-3 5-1-1-2-2-3-2l2-2v2c3-2 5-6 6-8z" class="L"></path><path d="M114 364v4c0 1 0 3 1 4h0c-1 2 0 3 1 5 0 0-1 0-1-1-2-2-3-4-4-6 0-1-1-2-2-3h1l2-1h1c0-1 1-1 1-2z" class="G"></path><path d="M112 366h1c-1 1-1 3-2 4 0-1-1-2-2-3h1l2-1z" class="l"></path><path d="M108 391l1 1c1 3 3 6 5 8 0-1 1-3 1-4 1-2 1-3 2-4h0c0 2 0 3-1 5v2c1-2 1-3 2-4h1c-1 3-3 5-4 8 0 0-1 1-1 2l-2 2-3 3v-2s1-1 1-2l3-5s-1-2-2-2c-1-1-3-5-3-6v-2z" class="e"></path><path d="M453 300h1 1c0 2 0 2 1 3h0 0l-1 1s-1 1 0 2v1l1 1h1l2 3 1 3h1l3-1 2 1c-1 1-1 1-1 2 1 1 1 2 2 2l1 2c1 1 1 1 2 1h0 1l-3 2h-1c0 1 1 2 1 2v1l-1 1 1 1v1l-1 1-1-1-1 2c-2-1-2-2-3-4h0c-1 0-2 0-2 1h-1s-1 0-2-1v1c-1 0-2 0-3-1l-7-4-2-2c1 0 2-1 2-1 0-1 1-1 1-1l-3-3h0l-1-1-3-1h-1l-1-1h-1 0l2-2c-1-1-2-1-3-3h0c-1-1-2-1-3-3h1v-1c1 0 1 1 2 1s2 1 3 1h0c1 1 2 1 3 1 1-2 2-3 2-5h1 1l2 1h2 1 1v-3z" class="J"></path><path d="M460 318c1 1 1 1 2 1 0 1 0 1-1 1l-1 1v-3z" class="C"></path><path d="M460 314h1v1 1h-3l1-1 1-1z" class="t"></path><path d="M452 314h2c0 1 1 2 0 3v1l-1-2c0-1 0-1-1-2z" class="O"></path><path d="M455 319c1-1 1-1 2-1v-1h1l1 1c0 1-1 2-2 2l-2-1z" class="M"></path><path d="M460 318h0c1-1 3-1 4-1l1 1h-1c-1 0 0 0-1 1v-1c-1 0-1 1-1 1-1 0-1 0-2-1z" class="R"></path><path d="M461 320l2 2-1 2v-1l-2-1v-1l1-1z" class="D"></path><path d="M462 319s0-1 1-1v1h2v2l-2-1v2h0l-2-2c1 0 1 0 1-1z" class="S"></path><path d="M465 319c1 1 1 1 1 2s1 2 1 2c0 1 1 2 1 2v1c-2 0-1 0-3-1-1 0-1 0-2-1h-1l1-2h0v-2l2 1v-2z" class="p"></path><path d="M465 323h1c0 1-1 1-1 2-1 0-1 0-2-1l2-1z" class="Q"></path><path d="M463 322h0v-2l2 1h-1c0 1 0 1 1 2l-2 1h-1l1-2z" class="R"></path><path d="M451 316h1 1l1 2s1 0 1 1l2 1 3 2 2 1v1h-2c0-1-1-1-2-2h0c-1-1-1 0-3-1h-1v-1c0-1-2-1-3-1l-1-2 1-1z" class="g"></path><path d="M451 316h1c0 2 0 2-1 3l-1-2 1-1z" class="n"></path><path d="M464 313l2 1c-1 1-1 1-1 2 1 1 1 2 2 2l1 2c1 1 1 1 2 1h0 1l-3 2h-1s-1-1-1-2 0-1-1-2h-2c1-1 0-1 1-1h1l-1-1v-1c-1 1-2 1-3 0v-1-1l3-1z" class="b"></path><path d="M461 315l1-1h1l1 2c-1 1-2 1-3 0v-1z" class="Q"></path><path d="M459 325v-1c-1 0-1-1-2-2h1 0c1 1 2 1 2 2h2v-1 1h1c1 1 1 1 2 1 2 1 1 1 3 1l-1 1 1 1v1l-1 1-1-1-1 2c-2-1-2-2-3-4h0c-1 0-2 0-2 1 0-1-1-2-1-3z" class="l"></path><path d="M459 325h3 1l-1 1v1h0c-1 0-2 0-2 1 0-1-1-2-1-3z" class="Q"></path><path d="M462 327v-1l2 1c1-1 1-1 3 0l1 1v1l-1 1-1-1-1 2c-2-1-2-2-3-4z" class="h"></path><path d="M464 327c1-1 1-1 3 0l1 1v1l-1 1-1-1-2-2z" class="F"></path><path d="M449 318l1-1 1 2c1 0 3 0 3 1v1h1c2 1 2 0 3 1h-1c1 1 1 2 2 2v1c0 1 1 2 1 3h-1s-1 0-2-1v1c-1 0-2 0-3-1l-7-4-2-2c1 0 2-1 2-1 0-1 1-1 1-1h0l1-1z" class="t"></path><path d="M453 324c1-1 2-1 2-1h1 1v1s1 1 1 2v1h-1l-4-3z" class="N"></path><path d="M447 320c2 0 5 2 6 4l4 3h0v1c-1 0-2 0-3-1l-7-4-2-2c1 0 2-1 2-1zm6-20h1 1c0 2 0 2 1 3h0 0l-1 1s-1 1 0 2v1l1 1h1l2 3 1 3-1 1-1 1c-1-1-3-2-4-3-3-2-4-5-7-7 0 1 1 1 1 2v1h-1l1 1 1-1 1 1v3h2v1c1 1 1 1 1 2h-1-1l-1 1-1 1-1 1h0l-3-3h0l-1-1-3-1h-1l-1-1h-1 0l2-2c-1-1-2-1-3-3h0c-1-1-2-1-3-3h1v-1c1 0 1 1 2 1s2 1 3 1h0c1 1 2 1 3 1 1-2 2-3 2-5h1 1l2 1h2 1 1v-3z" class="w"></path><path d="M454 308l1-1 1 1h1l2 3 1 3-1 1v-1c-1-1-2-2-4-2h0l-1-2c-1-1-1-2-2-3 1 0 1 0 2 1z" class="W"></path><path d="M452 307c1 0 1 0 2 1l2 2h-1-1c-1-1-1-2-2-3z" class="AT"></path><path d="M457 308l2 3c-1 1-1 1-2 0h-1l1-2-1-1h1z" class="g"></path><path d="M437 308c2 1 3 1 4 2l1 1c1 0 2 0 4 1h0v1l-1 1h0l-1 1-3-1h-1l-1-1h-1 0l2-2c-1-1-2-1-3-3h0z" class="C"></path><path d="M441 314v-2c1 0 2 1 3 2h1 0l-1 1-3-1z" class="u"></path><path d="M453 300h1 1c0 2 0 2 1 3h0 0l-1 1s-1 1 0 2v1l-1 1c-1-1-1-1-2-1v-1c-1 0-2-1-3-1h-1l1-1v-1h2 1 1v-3z" class="y"></path><path d="M453 300h1 1c0 2 0 2 1 3h-1-2v-3z" class="Q"></path><path d="M449 304h1c1 0 1 2 3 2l1-1 1 1v1l-1 1c-1-1-1-1-2-1v-1c-1 0-2-1-3-1h-1l1-1z" class="q"></path><path d="M446 312l1 1 1-1 1 1h1 2v1c1 1 1 1 1 2h-1-1l-1 1-1 1-1 1h0l-3-3h0l-1-1 1-1h0l1-1v-1z" class="F"></path><path d="M448 315l1-1h3c1 1 1 1 1 2h-1-1c-1 0-2-1-3-1z" class="P"></path><path d="M445 314l3 1c1 0 2 1 3 1l-1 1-1 1-1 1h0l-3-3h0l-1-1 1-1z" class="d"></path><path d="M445 316h1 1c0 1 1 1 2 1v1l-1 1h0l-3-3z" class="s"></path><defs><linearGradient id="a" x1="335.237" y1="309.485" x2="335.072" y2="364.304" xlink:href="#B"><stop offset="0" stop-color="#f6c2a8"></stop><stop offset="1" stop-color="#feead5"></stop></linearGradient></defs><path fill="url(#a)" d="M348 319l1-1c1-1 2-3 2-5 0 0 0-1 1-1v2l-1 1h1c0-2 0-4 1-6h1v3c0 4-1 9-2 13-2 11-9 21-15 30-2 2-3 4-4 6 1 1 1 2 1 3s0 1-1 1h-1-1-1v-1h-1c-1-1-2-1-4-2-2 0-3 2-5 2h-1c0 1-1 0-2 0 2 0 3-1 4-2 1-2 3-4 3-6 1-2 1-4 1-6 0-1 2-2 3-2 2-2 3-3 5-4 4-4 7-9 10-14 1-4 3-7 5-11z"></path><path d="M331 359h0c1 1 1 2 2 2 1 1 1 2 1 3s0 1-1 1h-1-1-1v-1h-1c-1-1-2-1-4-2h3c1 0 1-1 1-2l1-1h1z" class="Ai"></path><path d="M328 362c1 0 1-1 1-2l1-1h1c-1 2-1 2 0 3v1h-1c-1 0-1 0-2-1z" class="w"></path><defs><linearGradient id="b" x1="396.231" y1="450.63" x2="422.338" y2="418.838" xlink:href="#B"><stop offset="0" stop-color="#988980"></stop><stop offset="1" stop-color="#bfafa4"></stop></linearGradient></defs><path fill="url(#b)" d="M414 409l1-1c2 5 4 9 5 14v2 4h0c1 1 0 2 1 2v2l-1 2-2 4-2 5-1 4-1 1h0l-1 3h1l-1 2-1 1-1 1c-2 4-5 7-7 11-1-1-1-2 0-3-1-1-1-2-1-3 0-2-1-5-3-6v-2h0-1l1-2c-1 0-1-1-2-2h1l1 1h1c2-5 4-10 5-15 3-8 7-16 8-25z"></path><path d="M405 457c0-2 3-6 5-8l-3 8h-1-1 0z" class="c"></path><path d="M399 452l1-2c-1 0-1-1-2-2h1l1 1h1c0 2 0 4 1 5 0 1 1 1 1 1 1 1 1 2 1 3h1v-1h0 1 1l-3 6c-1-1-1-2-1-3 0-2-1-5-3-6v-2h0-1z" class="m"></path><path d="M414 440l1 1v1l1 1-1 4-1 1h0l-1 3h1l-1 2-1 1-1 1c-2 4-5 7-7 11-1-1-1-2 0-3l3-6c1-3 2-5 3-8l4-9z" class="H"></path><path d="M413 445l2 2-1 1h0-2c1-1 1-2 1-3z" class="Q"></path><path d="M411 451l1-3h2l-1 3-1 1-1-1z" class="J"></path><path d="M415 442l1 1-1 4-2-2 2-3z" class="F"></path><path d="M411 451l1 1 1-1h1l-1 2-1 1-1 1v-4z" class="G"></path><path d="M414 409l1-1c2 5 4 9 5 14v2 4h0c1 1 0 2 1 2v2l-1 2-2 4-2 5-1-1v-1l-1-1c2-7 5-13 4-19 0-4-2-9-4-12z" class="V"></path><path d="M420 428h0c1 1 0 2 1 2v2c-1 0-1-1-2-1l1-3z" class="d"></path><path d="M419 431c1 0 1 1 2 1l-1 2c-1 0-1 0-1-1v-2z" class="u"></path><path d="M419 433c0 1 0 1 1 1l-2 4-1-1 2-4z" class="l"></path><path d="M415 441c1 0 1-3 2-4l1 1-2 5-1-1v-1z" class="a"></path><path d="M119 189c2-1 4-1 6-2 1 1 3 1 4 1h0v2h4l-2 2c-3 3-6 6-8 9l-2 1v1c-1 2-2 4-3 5 0 1-1 2-1 3h1c-1 2-3 4-3 6v1c-1 0-1-1-1-1-1 2-2 4-2 5s0 2-1 3c0 0-1 0-1 1h0c-1 1-1 0-2 1l1 4c-1-1-2-3-3-5-1-1-2-1-2-2l-1-1h0v1 1c1 1 1 2 1 2-1-1-2-3-2-5 0-5 0-9 1-14 0-2 3-5 3-8h-1c1-1 1-2 2-3h0 1c1-2 2-3 3-5 2-1 6-3 8-3z" class="Z"></path><path d="M118 205h0c0-1 1-3 2-3v1h1c-1 2-2 4-3 5v-3z" class="Q"></path><path d="M129 190h4l-2 2c-3 1-4 3-7 3l5-5z" class="R"></path><path d="M124 195c3 0 4-2 7-3-3 3-6 6-8 9l-2 1c0-3 2-5 3-7z" class="Q"></path><path d="M118 205v3c0 1-1 2-1 3h1c-1 2-3 4-3 6v1c-1 0-1-1-1-1v-1c1-4 2-7 4-11z" class="N"></path><path d="M114 216h1v1 1c-1 0-1-1-1-1v-1z" class="e"></path><path d="M105 219c1-2 2-4 5-5h2v1h-1c-2 1-3 2-4 3s-1 3-1 4 1 2 2 3h2v1h0c-1 1-1 0-2 1l1 4c-1-1-2-3-3-5-1-3-1-4-1-7z" class="m"></path><path d="M106 200h-1c1-1 1-2 2-3l1 1h1v1c-1 1 0 2 0 3 0 2 0 3-1 5 0-1 1-2 0-3s-1-2-1-3c-1 3-3 5-3 8 0 1-1 3-1 5s0 5 1 6v-1h1c0 3 0 4 1 7-1-1-2-1-2-2l-1-1h0v1 1c1 1 1 2 1 2-1-1-2-3-2-5 0-5 0-9 1-14 0-2 3-5 3-8z" class="B"></path><path d="M119 189c2-1 4-1 6-2 1 1 3 1 4 1h0c-4 0-8 1-11 3-1 0-3 0-4 1v1c1-1 2-1 3-2v1c-1 1-2 1-3 2-2 1-4 6-5 8 0-1-1-2 0-3v-1h-1l-1-1h0 1c1-2 2-3 3-5 2-1 6-3 8-3z" class="t"></path><path d="M94 271c3 2 6 3 9 5l2-1 1 1h5 3s1-1 2-1l2 2 2 6h1s1 1 1 2 1 2 1 3c2 4 4 10 4 15h-4-2-1-4l-2-1h-1l-1 1 2 2s-1 0-1 1h-1v1l-1 1h-1v2c-1 0-2-2-3-3 0-1-1-2-2-3l-2-2-1-1 2-1-1-2c-1 0-2-1-2-2l1-2c-2-2-4-6-4-8-1-2-1-4-1-6v-2c0-1 0-1-1-1h-1v-1h0 0v-2h-1l-1-2 1-1z" class="Z"></path><path d="M102 294c1 2 3 4 5 6h-1c-1-1-2-1-3-2-1 0-2-1-2-2l1-2z" class="g"></path><path d="M109 282c2 2 5 4 7 6l2 4v1h-1v1h0c-3-4-5-8-8-12z" class="I"></path><path d="M106 277l4 4 6 7h0c-2-2-5-4-7-6-2-1-5-3-7-5 1 0 2 1 4 0z" class="c"></path><path d="M94 271c3 2 6 3 9 5l3 1c-2 1-3 0-4 0l-7-3h-1l-1-2 1-1z" class="Y"></path><path d="M118 292c2 3 4 6 5 9l1 2h-1-2-1v-2c-2-1-2-3-2-5 0-1-1-1-1-2v-1h1v-1z" class="B"></path><path d="M117 294v-1h1c2 3 3 5 3 8v2h-1v-2c-2-1-2-3-2-5 0-1-1-1-1-2z" class="G"></path><path d="M103 298c1 1 2 1 3 2h1c1 0 2 1 3 1s1-2 2-2l1 3h1-1l-1 1 2 2s-1 0-1 1h-1v1l-1 1h-1v2c-1 0-2-2-3-3 0-1-1-2-2-3l-2-2-1-1 2-1-1-2z" class="l"></path><path d="M106 302c1 0 2-1 2 0 2 1 2 1 3 3l-3-1-2-2z" class="F"></path><path d="M104 300c0 1 1 2 2 2l2 2v1c-1-1-1-1-3-1l-2-2-1-1 2-1z" class="O"></path><path d="M105 304c2 0 2 0 3 1v-1l3 1v3h-1v2c-1 0-2-2-3-3 0-1-1-2-2-3z" class="d"></path><path d="M108 304l3 1v3h-1c0-1-1-2-2-3v-1z" class="J"></path><defs><linearGradient id="c" x1="127.696" y1="294.83" x2="108.066" y2="275.621" xlink:href="#B"><stop offset="0" stop-color="#1d1c1b"></stop><stop offset="1" stop-color="#443f39"></stop></linearGradient></defs><path fill="url(#c)" d="M103 276l2-1 1 1h5 3s1-1 2-1l2 2 2 6h1s1 1 1 2 1 2 1 3c2 4 4 10 4 15h-4 1l-1-2c-1-3-3-6-5-9l-2-4h0l-6-7-4-4-3-1z"></path><path d="M111 276h3s1-1 2-1l2 2h-3c-1-1-2 0-2 0l-1 1h0v-1c0-1-1-1-1-1z" class="C"></path><path d="M103 276l2-1 1 1c2 1 3 2 4 5l-4-4-3-1z" class="O"></path><path d="M116 288c2 1 3 3 4 5l5 7-2 1c-1-3-3-6-5-9l-2-4h0z" class="C"></path><path d="M121 283s1 1 1 2 1 2 1 3c2 4 4 10 4 15h-4 1l-1-2 2-1v2h1c0-2-1-4-1-6-1-2-1-4-2-6s-2-5-3-7h1z" class="O"></path><path d="M412 198h1c0 1 1 2 2 3v1l-2 1h0 1v2l3 1v-1c2 0 2 0 4 1l3 1 3 3v-1h2 0c1 1 2 3 2 4l1 2v1h1v1l1 4v3 6c0 2 1 3 0 5v2c-1 2-2 2-2 4 0 0 0 1-1 1 0-1 0-2-1-2h0c-2 1-3 2-5 4h0-1-1-1l-1 1h0c-1-1-1-1-2-1l3-3h1v-1c1 0 1-1 2-2h0c1 0 2-1 3-2h0v-1l2-2h0c0-1 1-1 1-2-1-1-1-1-1-2h-1l1-1c0-2 0-2-1-3l-1 1c-1 0-1 0-2-1-1 0-2-1-3-1h0c-1-1-1-1-2-1l-1 1h-2 1s0-1 1-1h-5v-1c-1 0-2 1-2 0-1 0-1-1-2-1-2 1-2 1-3 2l-1 1-1-1-2 1h-3-1-1 0c2-2 0-9 0-11v-1l-1-1 2-2v-1-3s1-1 2-1c-1-2-1-3-1-4v-3c0 1 1 2 1 3h0 1l-1 2v2c1 0 2 0 3-1 1 1 2 2 3 1l1-2h0l1-1c0-3 1-2 2-3z" class="T"></path><path d="M402 204c0 1 0 4-1 6v-2l-1 1v-1-3s1-1 2-1z" class="n"></path><path d="M400 209l1-1v2 3h-2v-1l-1-1 2-2z" class="y"></path><path d="M399 212c0-1 1-1 1-2 1 1 1 2 1 3h-2v-1z" class="g"></path><path d="M399 213h2v3c0 2-1 7 0 8h-1-1 0c2-2 0-9 0-11z" class="d"></path><path d="M429 209c1 1 2 3 2 4l1 2v1h1v1c-1-1-3-3-4-5v1c-1-1-2-2-2-3v-1h2 0z" class="R"></path><path d="M413 203h1v2h-1l-10 2 6-3h1l2-1h1z" class="g"></path><path d="M413 203h1v2h-1-3v-1l2-1h1z" class="y"></path><path d="M406 223h0c0-2 0-6-1-7s-1-1-1-2h0 1l1 3c0 1 1 2 2 2v1 1h3c-2 1-2 1-3 2l-1 1-1-1z" class="U"></path><path d="M412 198h1c0 1 1 2 2 3v1l-2 1h0-1l-2 1h-1v-2h0l1-1c0-3 1-2 2-3z" class="E"></path><path d="M409 202c1 0 1 0 2 1h1l-2 1h-1v-2h0zm-3 11l-2-2c2-1 4-2 6-2s3 1 5 0h2v1c1 1 3 1 4 2h2c1-1 0-1 2-1 0 1 1 2 1 3l3 3s0 1-1 1h0c0-1-1-1-1-2-1 0-1-1-2-1-1-1-2-1-3-2l-2-1h-3c-1-1-2-1-2-1 0 1 1 1 1 2 4 2 8 6 10 9l3 3-1 1c-1 0-1 0-2-1-1 0-2-1-3-1h0c-1-1-1-1-2-1l-1 1h-2 1s0-1 1-1h-5v-1c-1 0-2 1-2 0-1 0-1-1-2-1h-3v-1-1c-1 0-2-1-2-2l-1-3 1-1z" class="K"></path><path d="M411 218h2c1 0 1 0 2 1l-1 2-3-3z" class="I"></path><path d="M415 219c0 1 1 2 2 3 0 0 2 0 3 1h-5v-1s0-1-1-1l1-2z" class="O"></path><path d="M405 214l1-1 5 5 3 3c1 0 1 1 1 1-1 0-2 1-2 0-1 0-1-1-2-1h-3v-1-1c-1 0-2-1-2-2l-1-3z" class="Z"></path><path d="M143 165h6 6l16 6 3 2 2 1-1 1v1c1 0 2 1 3 1v2h-1c-3-1-5-1-8-1-4 0-9 0-14 1l-3 1-3 1h-2l-3 2-4 2c-2 2-5 4-7 5h-4v-2h0c-1 0-3 0-4-1-2 1-4 1-6 2l-2-1c1-1 2-3 2-5l3-5-1-2 3-4c1-1 2-1 3-1 5-4 10-6 16-6z" class="Ac"></path><path d="M124 172c1-1 2-1 3-1-1 2-3 4-5 7l-1-2 3-4z" class="X"></path><path d="M130 187c1-1 2 0 3 0 3-1 5-3 8-4 1-1 1-1 3 0l-4 2c-2 2-5 4-7 5h-4v-2h0c-1 0-3 0-4-1h5z" class="K"></path><path d="M125 187h5 2v1h-3c-1 0-3 0-4-1z" class="AT"></path><path d="M428 356h0l4-2 1-1 1 1c1-2 0-3 1-5v1s1 1 1 2c1 1 1 1 1 2l-2-1c-1 2 0 6 0 9l1 19c-1 3 0 5-1 8v1 3l-1 3v2h0v1h-1v-2c-1 1-1 4-1 5-1 2-1 4-2 5l-3 10c-2 3-3 7-4 10-1 0-1 0-2-1 0 1-1 2-1 2h0v-4-2c-1-5-3-9-5-14l2-9c1-4 2-8 2-13h1c1 0 1 0 2-1l-1-1c-1-1-1-6-1-8v-4c0-4-1-8 0-12 0 1 1 1 1 3h1v-2c0-2 4-3 5-4l1-1z" class="W"></path><path d="M424 412h1l-1 3h-1l1-3z" class="C"></path><path d="M422 417l1-2h1v3c-1 0-1-1-2-1z" class="S"></path><path d="M427 399h1v4c-1 0-1 0-2-1l1-3z" class="B"></path><path d="M426 402c1 1 1 1 2 1l-3 9h-1l2-10z" class="Ac"></path><path d="M433 397c0-3 1-7 1-9v2h1v3l-1 3v2h0v1h-1v-2h0z" class="B"></path><path d="M433 397l1-1v2 1h-1v-2h0z" class="Z"></path><path d="M422 385c3 3 5 5 6 10l-2-2c-1-1-1-1-1-2-1 0-1 0-2-1s-2-2-3-4c1 0 1 0 2-1z" class="K"></path><path d="M428 356c0 1 1 1 1 2l1-1 3-1c1 2 1 5 1 7l-1-1h0c-2 0-4-1-5-2-1 0-3 0-4 1s-1 1-1 2h-1v-2c0-2 4-3 5-4l1-1zm-4 62c1 2-1 4-1 6 0-1 1-1 1-1v-1-1l1-3 1-2h0l1-1v-2h0c0-1 1-1 1-1v-1c0-1 1-3 2-4l-3 10c-2 3-3 7-4 10-1 0-1 0-2-1 0 1-1 2-1 2h0v-4-2c1-1 1-3 2-5 1 0 1 1 2 1h0z" class="n"></path><path d="M420 422c1-1 1-3 2-5 1 0 1 1 2 1h0l-3 8c0 1-1 2-1 2h0v-4-2z" class="J"></path><path d="M420 376v-4c0-4-1-8 0-12 0 1 1 1 1 3h1 1v2c0 1 1 1 1 2v1c2 4 3 7 2 12h0c-1-1-1-2-1-4v-1c0-1-1-3-1-4-1-2-1-3-2-5h0c0 2-1 5 0 6h0c-1 2 0 4-1 5v7c-1-1-1-6-1-8z" class="g"></path><path d="M428 356h0l4-2 1-1 1 1c1-2 0-3 1-5v1s1 1 1 2c1 1 1 1 1 2l-2-1c-1 2 0 6 0 9l1 19c-1 3 0 5-1 8v1h-1v-2-2-23c0-2 0-5-1-7l-3 1-1 1c0-1-1-1-1-2z" class="K"></path><path d="M434 386v-2c1 1 1 3 1 5v1h-1v-2-2z" class="E"></path><path d="M419 386h1c1 2 2 3 3 4s1 1 2 1c0 1 0 1 1 2l2 2v4h-1l-1 3-2 10-1 3-1 2c-1 2-1 4-2 5-1-5-3-9-5-14l2-9c1-4 2-8 2-13z" class="W"></path><path d="M425 391c0 1 0 1 1 2l2 2v4h-1c0-3-1-5-2-8z" class="U"></path><path d="M419 386h1c1 2 2 3 3 4v2c-1 1-1 3-2 4 0 1 1 3 0 4-1-1-1-1-2-1h-2c1-4 2-8 2-13z" class="n"></path><path d="M274 452c1-1 1-1 2-1h-2v-3h1 3c1 0 3 0 5 1 2 0 5 0 7 1h3 1 0v24c0 7 1 14 2 21v1h-2c0 1-1 0-1 1h0 3v1c-1 0-1 1-2 1h-3l-2-2c-1 0-2 0-2-1s-1-1-1-1h-1-1c-1 0-2-1-2-1-2-1-5-1-7 0v-1-1h-2l-1 1-1-1 1-1h0l1-1v-1h-2v-1-1c1-1 1-2 2-3l3-2c1 0 1 0 1-1-1-1-1-1-1-3h-1c1 0 1-1 0-2-1-2-1-7-2-9v-4l1-1-1-9v-1h1z" class="Af"></path><path d="M292 459l-3-1h1l1-1h-2v-1l4 1-1 2z" class="AR"></path><path d="M274 462l1 5h-2v-4l1-1z" class="AI"></path><path d="M275 467c0 4 1 8 1 11h-1c1 0 1-1 0-2-1-2-1-7-2-9h2z" class="AS"></path><path d="M294 450h0v24c-1-3 0-7 0-11 0-1 0-3-1-4h-1l1-2h0c1-1 1-5 1-7z" class="AW"></path><path d="M272 491l1-1v-1h-2v-1-1c1-1 1-2 2-3v3c1 0 2-1 3 1-1 0-1 1-1 2h1 0c1 0 1 0 2 1 1 0 2 1 3 1 1 1 2 2 4 3h-1c-1 0-2-1-2-1-2-1-5-1-7 0v-1-1h-2l-1 1-1-1 1-1h0z" class="AX"></path><path d="M272 491h0c1 0 1 1 2 1h7c1 1 2 2 4 3h-1c-1 0-2-1-2-1-2-1-5-1-7 0v-1-1h-2l-1 1-1-1 1-1z" class="AB"></path><path d="M274 452c1-1 1-1 2-1h-2v-3h1 3c1 0 3 0 5 1 0 0 0 1 1 1l2 1h3c2 1 3 1 4 2v4c-1-1-2-1-4-2h0-1l-12-3h-2z" class="AA"></path><path d="M438 162h2 1l-1 2h1c5 1 10 3 15 6 1 0 2 0 3 1h0 2l1-1h1v2c1 1 1 2 1 3h1l1 4c-1 2-1 2-1 4 1 2 1 4 2 6h0c-2-1-5-2-7-2l-1 1h-4 0v2l-3-1h-1c0 1-1 1-2 1h-1l-2-1v-1-2c-4-2-7-4-11-5-2-2-6-2-8-3l-3 1h2-1v1h-1c-5 0-10-1-15 0-1 0-1 0-2 1h0 0c-1 0-1 0-1-1h-1-1c0-1-2-2-3-2l-1-1s-1-1-2-1v-1l-4-2 1-1h1l10-4c3 0 3 1 5 3 0 1 1 1 1 2l6-3 3-1 9-3 3-1h4l-1-2h2v-1z" class="Z"></path><path d="M407 174h2l1 1h-1c-1 0-1 0-2-1z" class="H"></path><path d="M456 170c1 0 2 0 3 1h0 2v2h-2l-3-3z" class="j"></path><path d="M438 162h2 1l-1 2h1l-4 1-1-2h2v-1z" class="X"></path><path d="M446 186l4 2 1 1c0 1-1 1-2 1h-1l-2-1v-1-2z" class="l"></path><path d="M395 172h1 2c1 1 3 0 5 0l7 1v1l-3-1h-1 0c-1 1-5 1-6 1s-1-1-2-1-2 0-3-1z" class="B"></path><path d="M460 175c2 0 2 0 4 1l1-1 1 4c-1 2-1 2-1 4l-5-8z" class="b"></path><path d="M452 187c3-1 5-1 8 0l-1 1h-4 0v2l-3-1h-1l-1-1c1 0 1-1 2-1z" class="q"></path><path d="M450 188c1 0 1-1 2-1v1h3 0v2l-3-1h-1l-1-1z" class="f"></path><path d="M461 171l1-1h1v2c1 1 1 2 1 3h1l-1 1c-2-1-2-1-4-1l-1-2h2v-2z" class="d"></path><path d="M420 174h7c9 0 20 3 26 10h-1-1c-2-2-5-3-7-5-8-4-17-5-25-4h-1v-1h2z" class="R"></path><path d="M400 177h6c1 0 1 1 1 2h1 1c1-1 3-1 4-1h14l-3 1h2-1v1h-1c-5 0-10-1-15 0-1 0-1 0-2 1h0 0c-1 0-1 0-1-1h-1-1c0-1-2-2-3-2l-1-1z" class="l"></path><path d="M401 178c2-1 3-1 5-1 1 1 0 2 1 4h0c-1 0-1 0-1-1h-1-1c0-1-2-2-3-2z" class="r"></path><path d="M406 168c3 0 3 1 5 3 0 1 1 1 1 2h-2l-7-1c-2 0-4 1-5 0h-2l10-4z" class="I"></path><defs><linearGradient id="d" x1="368.718" y1="346.494" x2="338.001" y2="328.307" xlink:href="#B"><stop offset="0" stop-color="#9a2824"></stop><stop offset="1" stop-color="#c93631"></stop></linearGradient></defs><path fill="url(#d)" d="M354 303c1-1 0-2 1-3l1 1v2h1c1 4 1 10 2 15 0 10 1 20 1 31v2 13l-1 10v8 3l-1 2-4-1c-2 0-4-1-5 0-1 0-2-2-3-2-1-1-2-1-2-1-2-2-3-4-5-7v-2l-1-1c0-2 0-3 1-5h-1-1-1c-2-1-3-1-5-2v-1h1 1c1 0 1 0 1-1s0-2-1-3c1-2 2-4 4-6 6-9 13-19 15-30 1-4 2-9 2-13 1-2 0-7 0-9z"></path><path d="M350 361c1-1 2-1 4-1h0v2c-2 0-2 0-4-1z" class="Ag"></path><path d="M353 350h-1c-1 0-2 0-3-1h1 6 1-4v1z" class="AA"></path><path d="M357 349h3v2l-7-1v-1h4z" class="AR"></path><path d="M336 368v-1c1-1 2-1 3-1 2 0 7-1 9-2 1-1 2-2 2-3 2 1 2 1 4 1l-1 1c-1 2-3 3-5 4h0c-4 1-7 1-11 1h-1z" class="Ah"></path><path d="M353 363l1-1h1c0 1 0 1 1 1 1 1 2 1 4 1l-1 10v8 3l-1 2-4-1c-2 0-4-1-5 0-1 0-2-2-3-2-1-1-2-1-2-1-2-2-3-4-5-7v-2l-1-1c0-2 0-3 1-5h-1-1c4 0 7 0 11-1h0c2-1 4-2 5-4z" class="AM"></path><path d="M359 374v8 3l-1 2-4-1c1 0 2-1 3 0l1-1v-1c-1 0-4-1-5-2 1-1 1-1 2-1h3v-5c1-1 0-1 0-1l-2-1h3z" class="AE"></path><defs><linearGradient id="e" x1="98.319" y1="254.664" x2="104.366" y2="274.88" xlink:href="#B"><stop offset="0" stop-color="#181819"></stop><stop offset="1" stop-color="#45403c"></stop></linearGradient></defs><path fill="url(#e)" d="M121 240h0l2 2h2 1c1 1 1 2 1 4h-1l2 2h3v1h1 1s0 1 1 1l-2 3 2 1v3s0 2-1 3c0 1-1 2-1 3s-1 2-1 3-1 2-1 2v1h-1l-1 2v2h0 0c-1 1-1 1-1 2v2c-1 2-1 3-1 4h1c1 1 1 2 2 3h-1-1v-1h-2l-1 3c-1 0-1 1-1 2 0-1-1-2-1-3s-1-2-1-2h-1l-2-6-2-2c-1 0-2 1-2 1h-3-5l-1-1-2 1c-3-2-6-3-9-5-2 0-5 0-7-1l-2 1v1h-6c-4 0-8 0-12 2-1 1-2 1-4 1 3-3 7-5 10-8v-1c1-1 1-2 1-4l7-5c2-3 6-5 9-7l6-4h1v1h1 2l2-1v1l2-1c1-1 3-2 5-2 1-1 2-1 3-2h0c3-1 6-1 9-2z"></path><path d="M78 270h9l-2 1v1h-6l-1-2z" class="x"></path><path d="M91 262h1c-1 0-1 1-1 1v1h-1c-1 1-2 1-2 3h3 1l-1 1h-3c-1 0-1 0-2-1l3-3 2-2z" class="K"></path><path d="M132 253l2 1v3s0 2-1 3c0 1-1 2-1 3h-1c1 0 1-1 1-1v-2c1-1 1-1 1-2h0l1-1c0-1 0-1-1-1h-2l-3-1h-1l1-1 4 1v-1-1z" class="H"></path><path d="M67 274s0-1 1-1c1-2 7-2 10-3l1 2c-4 0-8 0-12 2z" class="a"></path><path d="M131 259v2h1c0 1-1 1-1 2h1c0 1-1 2-1 3s-1 2-1 2v1h-1l-1 2-1-2c1 0 2-1 2-2v-1-1c0-1 1-1 1-2 1-1 1-3 1-4z" class="f"></path><path d="M124 257h3c1 0 1 0 2 1l2 1c0 1 0 3-1 4l-1-2h-1l-1 1c-1 0-1-1-2-1h0l-1-1h2s0-1 1-1h0c-1-2-2-2-3-2z" class="C"></path><path d="M127 257c1 0 1 0 2 1l2 1c0 1 0 3-1 4l-1-2 1-1v-1c-1 0-2-1-3-1v-1z" class="R"></path><path d="M124 260l1 1h0c1 0 1 1 2 1l1-1h1l1 2c0 1-1 1-1 2v1h-1c0-1-1-1-2-2l-1-1c-1 0-2 0-3 1h0v-2-1h0c1-1 2-1 2-1z" class="P"></path><path d="M124 260l1 1h0-1 0c-1 1-1 1-2 1v-1h0c1-1 2-1 2-1z" class="D"></path><path d="M100 251h8l2 1h0c-4 1-8 1-11 2s-5 2-7 2l-2 1-1-1 2-2 5-2 4-1z" class="n"></path><path d="M100 251l-1 1c-3 0-5 2-7 3v1l-2 1-1-1 2-2 5-2 4-1z" class="g"></path><defs><linearGradient id="f" x1="126.23" y1="248.678" x2="112.751" y2="255.116" xlink:href="#B"><stop offset="0" stop-color="#a69890"></stop><stop offset="1" stop-color="#c0b5ad"></stop></linearGradient></defs><path fill="url(#f)" d="M115 250h2l4 1 7 1h4v2 1l-4-1h0c-6-1-12-2-18-2h0l-2-1 7-1z"></path><path d="M100 271l-3-2c0-1 0-1-1-2l-1-1c2-1 0-2 1-4h0v-1c-1 1-1 2-3 2h0v-1h1c0-1 1-1 1-2 2-2 5-3 9-4l-1 1-4 3c0 2 1 1 1 3 0 1-1 1-1 3 1 0 2 2 2 2 0 1 0 2 1 4h0l-2-1z" class="J"></path><path d="M100 271s-1-1-1-2c-1 0-1 0-1-1h1c0-1-1-1-1-1-1-1-1-3-1-4s1-2 2-3c0 2 1 1 1 3 0 1-1 1-1 3 1 0 2 2 2 2 0 1 0 2 1 4h0l-2-1z" class="F"></path><path d="M106 247c2 0 4-1 6-1 1-1 2-2 4-2v2l2-1c1 1 1 1 1 2v1l-1 1-1 1h-2l-7 1h-8l-4 1c3-2 7-3 10-5h0z" class="c"></path><path d="M100 251h0c3-3 8-3 12-2 1 0 2-1 2-1h1v2l-7 1h-8z" class="X"></path><path d="M102 247l2-1c1 0 1 1 2 1-3 2-7 3-10 5l-5 2-2 2 1 1-4 1c-4 3-8 6-13 9v-1c1-1 1-2 1-4l7-5c2-3 6-5 9-7l6-4h1v1h1 2l2-1v1z" class="n"></path><path d="M86 258v-1l4-3h1l-2 2 1 1-4 1z" class="s"></path><path d="M102 247l2-1c1 0 1 1 2 1-3 2-7 3-10 5l-5 2h-1l12-7z" class="R"></path><path d="M96 246h1v1h1 2l-12 7c-2 1-4 2-7 3 2-3 6-5 9-7l6-4z" class="l"></path><path d="M121 240h0l2 2h2 1c1 1 1 2 1 4h-1l2 2h3v1h1 1s0 1 1 1l-2 3v1-2h-4l-7-1-4-1 1-1 1-1v-1c0-1 0-1-1-2l-2 1v-2c-2 0-3 1-4 2-2 0-4 1-6 1h0c-1 0-1-1-2-1 1-1 3-2 5-2 1-1 2-1 3-2h0c3-1 6-1 9-2z" class="D"></path><path d="M109 244c1-1 2-1 3-2v1h2c1 0 0 1 1 0h2 0 4c0 1 0 1 1 1l1-1 1 1v1 2c-1 1-1 1-1 2 1 1 2 1 3 1h1l1 2-7-1-4-1 1-1 1-1v-1c0-1 0-1-1-2l-2 1v-2c-2 0-3 1-4 2-2 0-4 1-6 1h0c-1 0-1-1-2-1 1-1 3-2 5-2z" class="N"></path><path d="M118 245h1c1 1 2 0 3 0-1 1-2 2-3 2 0-1 0-1-1-2z" class="e"></path><path d="M117 250l1-1 1-1c0 1 1 2 2 3l-4-1z" class="a"></path><path d="M109 244c1-1 2-1 3-2v1c-2 1-4 2-6 4h0c-1 0-1-1-2-1 1-1 3-2 5-2z" class="f"></path><path d="M104 256c4-1 10-2 14 0 2 1 5-1 6 1 1 0 2 0 3 2h0c-1 0-1 1-1 1h-2s-1 0-2 1h0v1 2h0c1-1 2-1 3-1l1 1c1 1 2 1 2 2l-2 2h-1s-1-1-2-1h-4v-1-2h-1c0 1-1 3-1 4h-1-1v-1h-1v-2 1h-1c0-2-1-3-1-5l-2-2h0v-1h-2 0l2-1c-2 0-4 0-6 1l-1-1 1-1z" class="Q"></path><path d="M112 257h2v4h1v-1 2l-1 3v1h-1c0-2-1-3-1-5l1-1h0c0-2 0-2-1-3z" class="N"></path><path d="M110 257c1 0 1-1 2 0s1 1 1 3h0l-1 1-2-2h0v-1h-2 0l2-1z" class="c"></path><path d="M119 257h2v1h-1c-1 1-2 2-2 3-1 1-2 2-3 2v-1-2c1-1 3-3 4-3z" class="q"></path><path d="M115 262v1c1 0 2-1 3-2v3h0c0 1-1 3-1 4h-1-1v-1h-1v-2l1-3z" class="X"></path><path d="M115 267l1-3h2 0c0 1-1 3-1 4h-1-1v-1z" class="e"></path><path d="M119 257c1 0 1-1 3 0v2 2 1 2h0c1-1 2-1 3-1l1 1c1 1 2 1 2 2l-2 2h-1s-1-1-2-1h-4v-1-2h-1 0v-3c0-1 1-2 2-3h1v-1h-2z" class="N"></path><path d="M126 268c-1-1-1-2-1-3 1 0 0 0 1-1 1 1 2 1 2 2l-2 2z" class="o"></path><path d="M118 264h1v2 1h4c1 0 2 1 2 1h1l2-2h1v1c0 1-1 2-2 2l1 2v2h0 0c-1 1-1 1-1 2v2c-1 2-1 3-1 4h1c1 1 1 2 2 3h-1-1v-1h-2l-1 3c-1 0-1 1-1 2 0-1-1-2-1-3s-1-2-1-2h-1l-2-6-2-2v-1l-1-2c0-1 0-1-2-2h0c1-2 1-3 0-4h1v-1 2h1v1h1 1c0-1 1-3 1-4z" class="C"></path><path d="M123 283l1-1h1v1l-1 3c-1-1-1-2-1-3z" class="P"></path><path d="M118 264h1v2 1h4c1 0 2 1 2 1 0 1 0 1-1 1s-1-1-2-2l-1 1v1c-1 0-1-1-1-1h-3c0-1 1-3 1-4z" class="p"></path><path d="M127 269l1 2v2c-2 0-2-2-5-1 0-1-1-1-1-2v-1c1 0 2 0 3 1 1 0 1-1 2-1zm-14-3h1v-1 2h1v1c1 2 1 3 1 4l1-1c1-1 1-1 2 0 1 2 1 4 2 6h0c1 2 1 4 2 6 0 1 0 2 1 3-1 0-1 1-1 2 0-1-1-2-1-3s-1-2-1-2h-1l-2-6-2-2v-1l-1-2c0-1 0-1-2-2h0c1-2 1-3 0-4z" class="I"></path><path d="M113 266h1v-1 2h1v1c1 2 1 3 1 4l5 11h-1l-2-6-2-2v-1l-1-2c0-1 0-1-2-2h0c1-2 1-3 0-4z" class="Y"></path><path d="M103 257l1 1c2-1 4-1 6-1l-2 1h0 2v1h0l2 2c0 2 1 3 1 5 1 1 1 2 0 4h0c2 1 2 1 2 2l1 2v1c-1 0-2 1-2 1h-3-5l-1-1-1-1-2-2h0c-1-2-1-3-1-4 0 0-1-2-2-2 0-2 1-2 1-3 0-2-1-1-1-3l4-3z" class="c"></path><path d="M107 264c1 1 2 1 3 1h1c0 1 0 1-1 2l2 1h-2c-1-1-2-1-3-2v-2z" class="h"></path><path d="M105 267h1 1c1 1 1 2 2 2l1 1c-1 1-1 1-1 2h1c-1 1-1 1-2 1h-2v-3-2h-1v-1z" class="o"></path><path d="M105 267h1 1c1 1 1 2 2 2-1 1-1 2-2 2h-1v-3l-1-1z" class="x"></path><path d="M103 257l1 1c2-1 4-1 6-1l-2 1h0-3v1c0 1 0 1-1 1v-1h-1l-1 3h3c-1 1-2 1-2 2h1v1c-1 2-1 2-1 4h1l1-1h1v2c-1 0-1-1-2 0h0c-1 0-2-2-3-2 0 0-1-2-2-2 0-2 1-2 1-3 0-2-1-1-1-3l4-3z" class="i"></path><path d="M103 264c-1 0-1 1-2 1v-3h1 3c-1 1-2 1-2 2z" class="x"></path><path d="M101 268c1 0 2 2 3 2h0c1-1 1 0 2 0v3h2c1 0 1 0 2-1l2-1c1 0 1 1 2 1h1l1 2v1c-1 0-2 1-2 1h-3-5l-1-1-1-1-2-2h0c-1-2-1-3-1-4z" class="N"></path><path d="M104 274h1 0c1 0 1 1 2 1 1-1 2 0 3-1 2 0 2-1 4-1 0 1 0 1 1 1h1v1c-1 0-2 1-2 1h-3-5l-1-1-1-1z" class="P"></path><path d="M108 258h2v1h0l2 2c0 2 1 3 1 5 1 1 1 2 0 4l-1-2-2-1c1-1 1-1 1-2h-1c-1 0-2 0-3-1h0c-1 0-2-1-2-2h-3l1-3h1v1c1 0 1 0 1-1v-1h3z" class="h"></path><path d="M107 264v-1l-2-2c2-1 3-1 4-2h1 0c-1 1-2 1-2 2s0 1 1 2v1l1 1c-1 0-2 0-3-1h0z" class="d"></path><path d="M110 265l-1-1v-1c-1-1-1-1-1-2s1-1 2-2l2 2c0 2 1 3 1 5 1 1 1 2 0 4l-1-2-2-1c1-1 1-1 1-2h-1z" class="v"></path><defs><linearGradient id="g" x1="331.163" y1="268.498" x2="323.203" y2="235.797" xlink:href="#B"><stop offset="0" stop-color="#942621"></stop><stop offset="1" stop-color="#c6322e"></stop></linearGradient></defs><path fill="url(#g)" d="M304 241l20-2h7c1 1 1 1 2 1v-1h2l22 3 9 3 4 3h0l2 2c1 3 3 6 4 9h0l-2 2c0 1-1 1-2 2-1 0-1 1-2 1 0 1-1 1-1 1-1 1-1 2-2 2h-1c0 1 0 1-1 1v1h-1c-1 0-4-4-4-5l3-3c-2 0-3 1-4 2-4-2-7-4-12-6 0-1 1-2 1-3-1 1-1 2-2 3h-4c-2-1-5-1-7-1-8-1-36 0-40 7-1 2-1 3-1 5l-1 4h-1l-4-4-2-2c-3-4-4-6-5-11v1l-1-1 1-6h1v-2h0l1-1c1 0 2 1 2 1 1-1 1-1 1-2 1 0 2-1 2-1 1 0 3-1 4-1 1-1 4-2 5-2v1c2 0 5-1 7-1z"></path><path d="M282 247c1 1 1 3 1 4s-1 2-1 2l-1 2v1l-1-1 1-6h1v-2z" class="AU"></path><path d="M286 262l5 5c1 1 2 1 3 1l-1 4h-1l-4-4c0-1-1-2-2-3v-3z" class="Ag"></path><path d="M292 272c0-2-1-3-1-4v-1c1 1 2 1 3 1l-1 4h-1z" class="T"></path><path d="M282 253c2 2 3 6 5 9h-1v3c1 1 2 2 2 3l-2-2c-3-4-4-6-5-11l1-2z" class="Ah"></path><path d="M370 248l2 2c1 3 3 6 4 9h0l-2 2c0 1-1 1-2 2-1 0-1 1-2 1 0 1-1 1-1 1-1 1-1 2-2 2h-1c0 1 0 1-1 1v1h-1c-1 0-4-4-4-5l3-3 3-3c1-1 1-2 2-3h0l-1 1h-2c1-1 3-2 4-4l2-2s-1-1-1-2z" class="AH"></path><path d="M146 401c1-1 1-3 1-4h1l2 8 3 11c1-1 1-1 1-2l1 1h1c2 4 3 8 5 12h0c1 1 1 1 1 2h0l1-1 1 3 1 2-2 1 1 2 1 2 8 14v-1l1 1h1l-1-3 2 1 2 5h0v2c1 2 2 3 2 5h0l1 2c1 1 1 2 1 4h0l-5-5s0 1-1 1h0c-1 2 0 3 1 5l1 1 1 7c1 1 0 1 0 2 0 0-1 0-2 1l-2-2h0s-1-1-1-2l-1-1c-3-5-7-8-12-10l-2-1s1 0 1-1h0l-1-1c0-1-1-1-2-1-1-2-2-3-3-4-3-3-10-4-14-6-2 0-3-2-5-3-1 0-2-1-2-2l-2-1c-3-1-5-4-6-7h-1 0c0-1-1-2 0-3v-2-2h0l2-2 2-1 4-3c3-2 5-4 7-7h1c3-5 5-11 6-17z" class="E"></path><path d="M160 463c1 0 2 1 3 1l-2 1-2-1s1 0 1-1z" class="c"></path><path d="M157 441c-1-1-1-1-2-1 1-2 1-2 1-3l1 1h2v1l-2 2z" class="B"></path><path d="M148 448h1c1 1 2 3 3 5h-1l-1-1c-1-1-2-2-4-2h0l1-1h0l1-1z" class="U"></path><path d="M124 431h0l2-2c0 2 0 3-1 5v3 1h-1 0c0-1-1-2 0-3v-2-2z" class="T"></path><path d="M137 446h2c1 1 1 1 2 1h1l-7 1c-1 0-2-1-2-2h4z" class="b"></path><path d="M163 464c5 2 9 8 12 12h0v2s-1-1-1-2l-1-1c-3-5-7-8-12-10l2-1z" class="x"></path><path d="M147 421c0 1 1 2 1 3 0 2 0 4-1 6v2c1 2 1 7 1 10l-1 1v-1-2c-1-1 0-2-1-3h0c1-1 1-2 1-3s0-1-1-2v-1l1-1v-1c1-3 0-6 0-8z" class="V"></path><path d="M132 435c1 2 1 3 3 4v1h1l3 5v1l-2-1c-1 0-1-1-2-1-2-2-3-6-3-9z" class="H"></path><path d="M135 426v1c0 1-1 4-2 5l3 8h-1v-1c-2-1-2-2-3-4l-1-3c0-1 0-2 1-3l3-3z" class="S"></path><path d="M132 429h1c0 1 1 1 1 2-1 1-2 1-3 1 0-1 0-2 1-3z" class="D"></path><path d="M169 453l6 9 1 2h0c-1 2 0 3 1 5l1 1 1 7c-1 0-2-2-3-3h1c0-2-1-4-1-5-2-5-7-11-7-16z" class="O"></path><defs><linearGradient id="h" x1="146.022" y1="442.577" x2="146.655" y2="445.394" xlink:href="#B"><stop offset="0" stop-color="#7b7066"></stop><stop offset="1" stop-color="#998e83"></stop></linearGradient></defs><path fill="url(#h)" d="M146 444c4-1 7-3 9-6 1-2 2-4 3-5h0c0 2-1 3-2 4 0 1 0 1-1 3 1 0 1 0 2 1-5 3-9 5-15 6h-1c-1 0-1 0-2-1l5-1 2-1z"></path><path d="M143 417l3-6v8 1l1 1c0 2 1 5 0 8v1l-1 1v1c1 1 1 1 1 2h-1 0l-1 1v1l-1-3h2c-1-1-1-3-1-4s-1-1-1-1c-1-1-1-3-2-3v2c1 1 1 3 1 4h0c-2-4-1-9-1-13l1-1z" class="I"></path><g class="C"><path d="M143 417l2-2v5 9c0-1-1-1-1-1-1-1-1-3-2-3v2c1 1 1 3 1 4h0c-2-4-1-9-1-13l1-1z"></path><path d="M140 421l2-3c0 4-1 9 1 13h0v-1l1-1h0s1 1 0 2c0 1-1 2 0 3h0v2l1 1c0 1 1 2 1 3v3 1h0l-2 1c0-2-1-3-2-5l-4-9h0c1-2 0-4 0-6v-2l2-2z"></path></g><path d="M140 421l2-3c0 4-1 9 1 13h0v-1l1-1h0s1 1 0 2c0 1-1 2 0 3h0v2l1 1c0 1 1 2 1 3v3c-2-3-2-6-4-9-2-4-1-8-2-13h0z" class="B"></path><path d="M154 414l1 1h1c2 4 3 8 5 12h0c1 1 1 1 1 2h0l1-1 1 3 1 2-2 1 1 2 1 2-1 1 4 8-1 1c-4-7-7-13-10-20l-4-12c1-1 1-1 1-2z" class="h"></path><path d="M155 415h1c2 4 3 8 5 12h0c1 1 1 1 1 2h0l1-1 1 3 1 2-2 1 1 2 1 2-1 1c-2-4-4-8-5-12l-4-12z" class="E"></path><path d="M161 427h0c1 1 1 1 1 2h0l1-1 1 3 1 2-2 1-2-7z" class="t"></path><path d="M135 426c0-1 2-2 3-3v2c0 2 1 4 0 6h0l4 9c1 2 2 3 2 5l-5 1h-2v-1l2 1v-1l-3-5-3-8c1-1 2-4 2-5v-1z" class="T"></path><path d="M135 426c0-1 2-2 3-3v2c0 2 1 4 0 6h0 0c-1 1-1 3-1 3 1 2 2 2 1 4h0c0-1-1-2-1-3-1-1-2-2-2-3s1-3 1-4c1-1 1-1 0-2l-1 1v-1z" class="V"></path><path d="M138 425c0 2 1 4 0 6 0-1-1-2-1-3s0-1 1-3z" class="S"></path><path d="M165 438l8 14v-1l1 1h1l-1-3 2 1 2 5h0v2c1 2 2 3 2 5h0l1 2c1 1 1 2 1 4h0l-5-5s0 1-1 1l-1-2-6-9-2-5 1-1-4-8 1-1z" class="B"></path><path d="M176 461l1 2s0 1-1 1l-1-2 1-1z" class="b"></path><path d="M173 451l1 1h1c0 1 1 1 1 3v1c-2-1-3-2-3-4v-1z" class="S"></path><path d="M176 455l2 2c1 2 2 3 2 5-2-2-3-4-4-6v-1z" class="C"></path><path d="M174 449l2 1 2 5h0v2l-2-2c0-2-1-2-1-3l-1-3z" class="b"></path><path d="M168 447c2 5 5 9 8 14l-1 1-6-9-2-5 1-1z" class="Y"></path><path d="M155 179c5-1 10-1 14-1 3 0 5 0 8 1h1 0v1c-2 2-7 4-7 4l1 1-3 3c0 2-2 4-3 5l-1-1v1l-1 1v2l-1 2h-1l-3 3h0-2-2v1h0l-1 1-2 2-2 2c-1 1-2 3-2 5l-1 1c-1 1 0 2 0 2l-1 3v1c-1 2 0 5 0 7h0l1 2v1l-1 2-2 7c0 1-1 3-1 4v2 1 3 4c-1-1-1-1-2-1l-1 1h-3v-1l-1 2c-1 0-1 0-2 1l-2-1 2-3c-1 0-1-1-1-1h-1-1v-1h-3l-2-2h1c0-2 0-3-1-4h-1-2l-2-2h0c-3 1-6 1-9 2h0c-1 1-2 1-3 2v-2h-2v-1h1c0-2 1-3 1-4 2 0 3-1 4-2 1-2 2-2 2-3-1-2-3-4-4-6h0-1 0c0-1 1-1 1-1 1-1 1-2 1-3s1-3 2-5c0 0 0 1 1 1v-1c0-2 2-4 3-6h-1c0-1 1-2 1-3 1-1 2-3 3-5v-1l2-1c2-3 5-6 8-9l2-2c2-1 5-3 7-5l4-2 3-2h2l3-1 3-1z" class="E"></path><path d="M160 192h2l-1 2-1-1v-1z" class="B"></path><path d="M155 192c2-1 5 0 7-1h0v1h-2-1 0-4z" class="K"></path><path d="M149 181s1 0 2 1l-3 1-1-2h2z" class="W"></path><path d="M165 193c-1-1-2-1-2-2 1-1 1-1 2-1v2 1z" class="K"></path><path d="M149 181l3-1 1 1-2 1c-1-1-2-1-2-1z" class="j"></path><path d="M122 218h2 0c0-1 0-2 1-2v-1c0 2 0 3-1 4v2c-1-1-1-1-2-1l-1-1 1-1z" class="U"></path><path d="M144 202l1 1c2 1 4 0 5 1 0 1 0 1-1 1s-4 0-5-1v-2z" class="G"></path><path d="M152 180l3-1h3v1l-5 1-1-1z" class="d"></path><path d="M160 193l1 1 1 1c1-1 1-1 2-1v2l-1 2h-1c0-1-1-2-2-3v-2z" class="L"></path><path d="M169 178c3 0 5 0 8 1l-1 1h-9l2-2z" class="u"></path><path d="M144 201h-2 0l3-3 1-1h1v1h2v1c-1 0-2 1-4 1h0l-1 1z" class="B"></path><path d="M144 183l3-2 1 2-5 3h-2-1v-1l4-2z" class="q"></path><path d="M123 201c0 1 0 1 1 1-2 3-5 6-6 9h-1c0-1 1-2 1-3 1-1 2-3 3-5v-1l2-1z" class="l"></path><path d="M155 179c5-1 10-1 14-1l-2 2h-9v-1h-3z" class="Y"></path><path d="M171 184l1 1-3 3c0 2-2 4-3 5l-1-1v-2c0-2 0-3 1-4 2 0 4-1 5-2z" class="r"></path><path d="M121 219l1 1c1 0 1 0 2 1l-3 6h0c-1 1-1 1-2 1v-2l-1-1h0c0-2 1-4 2-6l1 1v-1z" class="V"></path><path d="M119 226h1l1 1c-1 1-1 1-2 1v-2z" class="M"></path><path d="M120 219l1 1h0l1 1v1h-1-1c0 1-1 2-2 3h0c0-2 1-4 2-6z" class="O"></path><path d="M149 198h4v1l1 2 1 1-1 1-2 2-2-1c-1-1-3 0-5-1l-1-1v-1l1-1h0c2 0 3-1 4-1v-1z" class="Z"></path><path d="M149 198h4v1c-2 1-3 1-4 0v-1z" class="S"></path><defs><linearGradient id="i" x1="129.266" y1="192.49" x2="133.285" y2="195.42" xlink:href="#B"><stop offset="0" stop-color="#968172"></stop><stop offset="1" stop-color="#aaa6a5"></stop></linearGradient></defs><path fill="url(#i)" d="M133 190c2-1 5-3 7-5v1h1 2c-4 2-7 4-10 7l-9 9c-1 0-1 0-1-1 2-3 5-6 8-9l2-2z"></path><path d="M114 217s0 1 1 1v1c0 2 0 3-1 5v2l1-1v-2c1-1 1-2 1-3 0 0 0 2 1 2 0 0 0 1 1 1v2h0l1 1v2 1h-1c-1-1-1-1-2-1l-2 1 1 1v2c-1-2-3-4-4-6h0-1 0c0-1 1-1 1-1 1-1 1-2 1-3s1-3 2-5z" class="B"></path><path d="M115 225l1 1v2l-1-1v-2z" class="f"></path><path d="M114 217s0 1 1 1v1c-1 3-3 7-1 10l1 1v2c-1-2-3-4-4-6h0-1 0c0-1 1-1 1-1 1-1 1-2 1-3s1-3 2-5z" class="N"></path><path d="M144 212l1-1c1-1 3-4 5-4-1 1-2 3-2 5l-1 1c-1 1 0 2 0 2h-2v-1c-2 1-4 4-6 5h0c-1 1-3 2-4 3h-3 0v-1l-2 1c2-2 5-4 8-5l6-5z" class="Q"></path><path d="M137 219h2c-1 1-3 2-4 3h-2v-1l4-2z" class="J"></path><path d="M144 212l1-1c1-1 3-4 5-4-1 1-2 3-2 5l-1 1c-1 1 0 2 0 2h-2v-1c-2 1-4 4-6 5h0-2l1-1c2-1 6-4 6-6z" class="U"></path><path d="M139 219h0c2-1 4-4 6-5v1l-16 14-2 2h0c-1 0-1-1-2-1l-3 3v1c-1 1-3 2-4 3 0-1-1-1-2-2l3-2h0c0-1 8-7 10-8l-1-1 2-2 2-1v1h0 3c1-1 3-2 4-3z" class="k"></path><path d="M119 233l9-6h0l-3 3-3 3v1c-1 1-3 2-4 3 0-1-1-1-2-2l3-2z" class="X"></path><path d="M128 224l1 1c-2 1-10 7-10 8h0l-3 2c1 1 2 1 2 2l-2 1-5 3-2 1h-2v-1h1c0-2 1-3 1-4 2 0 3-1 4-2 1-2 2-2 2-3v-2l-1-1 2-1c1 0 1 0 2 1h1v-1c1 0 1 0 2-1h0l1 1 6-4z" class="c"></path><path d="M121 227h0l1 1c0 1-1 2-2 2l-1-1v-1c1 0 1 0 2-1z" class="K"></path><path d="M116 235c1 1 2 1 2 2l-2 1c0-1-1-1-2-1l2-2z" class="s"></path><path d="M115 230c1 1 2 2 3 2l-5 3c1-2 2-2 2-3v-2z" class="B"></path><path d="M116 228c1 0 1 0 2 1h1l1 1-2 2c-1 0-2-1-3-2l-1-1 2-1z" class="E"></path><path d="M114 237c1 0 2 0 2 1l-5 3-2 1h-2v-1h1c2-1 4-3 6-4z" class="W"></path><path d="M159 192h0 1v1 2c1 1 2 2 2 3l-3 3h0-2-2v1h0l-1-1-1-2v-1h-4-2v-1h-1c3-3 5-5 9-5h4z" class="O"></path><path d="M154 195l1-1h1v1l-1 3-1-1v-2z" class="b"></path><path d="M147 197l3-2c1-1 2-2 4-1v1 2l-1 1h-4-2v-1z" class="Z"></path><path d="M159 192h0 1v1 2l-1 1c0 1-1 1-2 1l-2 2v2 1h0l-1-1-1-2v-1l1-1 1 1 1-3c0-1 0-1 1-2h1l1-1z" class="T"></path><path d="M154 197l1 1c0 1-1 1 0 2h0l-1 1-1-2v-1l1-1z" class="a"></path><path d="M160 195c1 1 2 2 2 3l-3 3h0-2-2v-2l2-2c1 0 2 0 2-1l1-1z" class="Z"></path><path d="M116 220l2-4c2-4 5-8 8-11 1-1 3-2 4-4 5-4 11-8 18-11h0l-8 5c-2 2-6 4-8 6-4 4-6 10-7 14v1c-1 0-1 1-1 2h0-2l-1 1v1l-1-1c-1 2-2 4-2 6v-2c-1 0-1-1-1-1-1 0-1-2-1-2z" class="L"></path><path d="M120 219c0-1 3-5 4-6h0l-1 2v1c0 1 0 1-1 2l-1 1v1l-1-1z" class="f"></path><path d="M145 215h2l-1 3v1c-1 2 0 5 0 7h0l1 2v1l-1 2-2 7c0 1-1 3-1 4v2 1 3 4c-1-1-1-1-2-1l-1 1h-3v-1l-1 2c-1 0-1 0-2 1l-2-1 2-3c-1 0-1-1-1-1h-1-1v-1h-3l-2-2h1c0-2 0-3-1-4h-1-2l-2-2h0c-3 1-6 1-9 2h0c-1 1-2 1-3 2v-2l2-1 5-3 2-1c1-1 3-2 4-3v-1l3-3c1 0 1 1 2 1h0l2-2 16-14z" class="E"></path><path d="M146 226l1 2v1l-1 2s0-1-1-1c0-1 0-2 1-4z" class="B"></path><path d="M138 242l1-1 1 1v1l-1 2h0l-2-1c1-1 1-1 1-2z" class="C"></path><path d="M131 229l6-4v1l-2 1c-1 1-2 1-2 2v2h-2c1-1 0-1 0-2z" class="f"></path><path d="M126 240h9c-3 1-6 1-10 1v1h0-2l-2-2h5z" class="M"></path><path d="M133 231v-2c0-1 1-1 2-2l1 2c2 0 2 0 3 1h0c0 1 1 2 1 2 0 1-1 4-2 4h0v-1l1-1h0v-1l-1 1h0v-1-1c1-1 1 0 1-1-2-1-4-1-6 0z" class="U"></path><path d="M125 242h2v-1h4c0 1 1 1 2 1v-1c1 0 1 1 2 1h2 1c0 1 0 1-1 2-1 0-1 2-2 3 0 1-1 2-1 3-1 0-1-1-1-1h-1-1v-1h-3l-2-2h1c0-2 0-3-1-4h-1 0z" class="L"></path><path d="M126 242c2 0 5 0 6 2h1c-1 2-4 2-6 3h1 1 2c1-1 3 0 4 0 0 1-1 2-1 3-1 0-1-1-1-1h-1-1v-1h-3l-2-2h1c0-2 0-3-1-4z" class="C"></path><path d="M139 245l1-2 2 2 1-1v1 3 4c-1-1-1-1-2-1l-1 1h-3v-1l-1 2c-1 0-1 0-2 1l-2-1 2-3c0-1 1-2 1-3 1-1 1-3 2-3l2 1h0z" class="E"></path><path d="M136 253c0-2 0-3 1-5 1-1 2-1 4-1l-4 4-1 2z" class="K"></path><path d="M141 247v4l-1 1h-3v-1l4-4z" class="l"></path><path d="M139 245l1-2 2 2 1-1v1 3 4c-1-1-1-1-2-1v-4-1-1h-2z" class="M"></path><path d="M129 229h2c0 1 1 1 0 2h2c2-1 4-1 6 0 0 1 0 0-1 1v1 1h0l1-1v1h0l-1 1v1h0l-1 2v1l-2 1h-9-5 0c-3 1-6 1-9 2h0c-1 1-2 1-3 2v-2l2-1 5-3 2-1c1-1 3-2 4-3v-1l3-3c1 0 1 1 2 1h0l2-2z" class="I"></path><path d="M130 231l1 1h0l-2 1-1-1 2-1z" class="L"></path><path d="M131 231h2l1 1v1c-2 0-2 0-3-1h0v-1z" class="G"></path><path d="M129 229h2c0 1 1 1 0 2v1l-1-1v-1c-1 0-2 1-3 1l2-2z" class="V"></path><path d="M129 233v1c-1 0-1 1-2 1l-1-2v-1h1 1l1 1z" class="D"></path><path d="M122 234v-1l3-3c1 0 1 1 2 1-2 1-3 2-5 3z" class="c"></path><path d="M120 239c2-1 4-1 6-1v2h-5 0v-1h-1z" class="o"></path><path d="M111 241c2 0 3-1 5-1 1-1 3-1 4-1h1v1c-3 1-6 1-9 2h0c-1 1-2 1-3 2v-2l2-1z" class="b"></path><path d="M126 238h8 3v1l-2 1h-9v-2z" class="F"></path><defs><linearGradient id="j" x1="481.079" y1="226.618" x2="469.71" y2="275.948" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#302e2c"></stop></linearGradient></defs><path fill="url(#j)" d="M439 216v-1h1l3 3c1 0 2 2 4 2v-1h3 0c1 1 3 2 3 3 1 1 3 2 4 3 0 0 2 2 3 2l1 1v1 1l3 3h0v1h1v2l2 1 5 4 3 2 2 1-2 1c1 0 3 1 4 2s2 1 3 2l2 1c2 1 5 2 6 3 0 1 1 1 2 2h-3l10 3c3 2 6 4 9 7l3 2c3 3 8 5 11 9-1 0-2-1-3-1s-1 0-2-1v-1c-1 0-2-1-3-1-3-1-7-1-11-2-3 0-8 0-11 1-1 0-2 0-3 1-3 1-6 3-9 4h0c-1 0-2 1-3 2 0-1-1-2-1-3-3 1-3-1-5-1h0c-1-1-1-2-2-2l-1 2-2 5-1 3-1 1-1 1c0 1-1 1-1 2l-1 2c-2 4-3 9-3 14l-1 1h-1 0c-1-1-1-1-1-3h-1-1v3h-1-1-2l-2-1h-1v-1s0-1-1-1c-1-1-1-3-2-5-2-3-5-6-7-8s-3-4-5-5l1-2-1-1 1-8c-1-3-1-5-1-7h1c0-1-1-2-1-2h1v-3l1-1c0-1-1-2-1-3v-4-3l1-2h0c0-1 1-2 1-3h1c0-1-1-1-1-2 1-2 0-4 0-6 1-2 0-3 0-5v-6-3c1 1 2 2 3 2h0c1 0 1 0 1-1v-2-3l1-1z"></path><path d="M463 251h1v1h1l-4 1h-1v-1l3-1z" class="j"></path><path d="M446 250c1 0 1 0 1 1 1 1 2 2 2 4l3 3h2 0 0-2c0 1 1 1 1 1l-1 2-1-2c0-2-1-2-2-4l-1-1c-1-2-1-3-2-4z" class="B"></path><path d="M435 228v-2h1l1 1c1 3 2 6 2 9h0l-1-1-1 1 1-2-1-1c0-1-1-1-1-2s-1-2-1-2v-1z" class="D"></path><path d="M487 257c3 1 5 2 8 4v2h0c1 1 0 2 0 3-1 0-1 1-2 1 0-1 1-1 1-2v-2c0-1-2-2-3-2 0-1-1-2-1-2-1 0-2-1-3-2z" class="M"></path><path d="M471 273h2l2 1h1 1 5l-4 2h0c-1-1-1 0-2-1-3 1-3-1-5-1h0v-1z" class="J"></path><path d="M434 221c1 1 2 2 3 2h0c1 0 1 0 1-1 1 1 1 3 1 4v2c-1 0-1-1-2-1h0l-1-1h-1v2c0-1 0-3-1-4v-3z" class="L"></path><path d="M438 222c1 1 1 3 1 4l-1 1s-1-1-1-2v-2c1 0 1 0 1-1z" class="a"></path><path d="M434 221c1 1 2 2 3 2h0v2l-1-1v-1 3h-1v2c0-1 0-3-1-4v-3z" class="J"></path><path d="M486 269s1-1 2-1v1c-1 0-1 0-2 1h1 1c-1 1-1 1-2 1 0 1-1 1-1 1-1 1-2 1-3 2h-5-1c1 0 1-1 2-1h1c1 0 1 0 2-1h0c1-1 2-2 3-2s1-1 2-1z" class="F"></path><path d="M448 243h2 1 2 0c0 1 0 1-1 1s-1 0-2 1h-1c1 1 1 3 1 4h0v1s0-1-1-1v-1c-1-1-3-2-3-3l-1-2h0c1 0 1 1 2 1 0 1 0 0 1 0v-1z" class="H"></path><path d="M456 230c1 0 2-1 2-1h1 1v1h1l3 3h0v1h1v2l-3-2h0c-2-1-4-3-6-4z" class="b"></path><path d="M460 230h1l3 3h0v1h1v2l-3-2h0 0v-1l-2-2v-1z" class="Y"></path><path d="M455 227c0-1 1-2 2-2 0 0 2 2 3 2l1 1v1 1h-1v-1h-1-1s-1 1-2 1c-2-1-3-3-5-4h1c1 1 2 1 4 2 0 0 0-1-1-1z" class="e"></path><path d="M503 270h3 3 1 1l3 1 1 1h0l1-1c-1 0-2-1-3-2-1 0-1-1-2-1l-3-3h0 0l3 2c3 3 8 5 11 9-1 0-2-1-3-1s-1 0-2-1v-1c-1 0-2-1-3-1-3-1-7-1-11-2z" class="T"></path><path d="M434 224c1 1 1 3 1 4v1s1 1 1 2 1 1 1 2l1 1-1 2v1 1c0 1-1 1-1 1 0 1-1 2-1 3v1c0-1-1-1-1-2 1-2 0-4 0-6 1-2 0-3 0-5v-6z" class="P"></path><path d="M434 224c1 1 1 3 1 4v1 2c1 0 0 0 0 1s0 2 1 3v1c0 1 0 1-1 2v4 1c0-1-1-1-1-2 1-2 0-4 0-6 1-2 0-3 0-5v-6z" class="C"></path><path d="M446 236c-1-2-3-4-3-6v-1-2c1 0 1 0 2 1h2l1-1c2 0 3 2 4 3 1 0 1 0 2 1h1c1 1 2 1 3 2l3 3c-1 0-3-2-4-3s-3-1-3-1l-2-2c-1 1-4 1-6 1h0c-1 1-1 0-1 1 1 1 1 2 1 3v1z" class="H"></path><path d="M439 216v-1h1l3 3c1 0 2 2 4 2v-1h3 0c1 1 3 2 3 3 1 1 3 2 4 3-1 0-2 1-2 2 1 0 1 1 1 1-2-1-3-1-4-2h-1l-3-3-9-7z" class="P"></path><path d="M453 222c1 1 3 2 4 3-1 0-2 1-2 2-1-2-2-2-2-4v-1z" class="N"></path><path d="M480 257c1 0 2 0 4 1 1 0 2 0 3 1s1 1 2 1h1v1l2 2c0 2-1 3-2 4h0-2l1-1h1c-1-1-1-1-2-1l-1 1 1 1s0 1 1 2l-1 1h-2c1-1 1-1 2-1v-1c-1 0-2 1-2 1l-1-2v-1-2l-2-1c0-2 0-3-1-4l-1-1-1-1z" class="C"></path><path d="M485 264s0-1 1-1h0c0 1 0 1 1 2v1h-2v-2z" class="k"></path><path d="M485 266h2v1l1 1c-1 0-2 1-2 1l-1-2v-1z" class="Q"></path><path d="M485 266h2v1h-2v-1z" class="P"></path><path d="M481 258c1 0 2 0 3 1s2 1 3 1c0 1 0 1-1 1v1c-1-1-2-1-2-2l-1-1h-1l-1-1z" class="F"></path><path d="M482 259h1l1 1c0 1 1 1 2 2v1c-1 0-1 1-1 1l-2-1c0-2 0-3-1-4z" class="i"></path><path d="M483 263l2 1v2 1l1 2c-1 0-1 1-2 1s-2 1-3 2h0c-1 1-1 1-2 1h-1c-1 0-1 1-2 1h-1l-2-1h-2v1c-1-1-1-2-2-2l1-1v-3h1 0c0 1 1 2 2 2s1-1 2-1v-1h2 1c1-1 1-2 2-3v-1 1h2c1-1 1-1 1-2z" class="x"></path><path d="M475 269v-1h2 1c0 1-1 1-2 1 0 1 0 1-1 1v-1z" class="m"></path><path d="M478 273v-3c2 0 2 1 3 2-1 1-1 1-2 1h-1z" class="D"></path><path d="M473 272h1c2 0 1-1 2 0l2 1c-1 0-1 1-2 1h-1l-2-1h-2l1-2 1 1z" class="i"></path><path d="M475 274v-1l1-1 2 1c-1 0-1 1-2 1h-1z" class="N"></path><path d="M470 271v-3h1 0c0 1 1 2 2 2s1-1 2-1v1s-2 0-3 1l1 1-1-1-1 2v1c-1-1-1-2-2-2l1-1z" class="l"></path><path d="M472 271l1 1-1-1-1 2v1c-1-1-1-2-2-2l1-1h2z" class="e"></path><path d="M483 263l2 1v2 1l1 2c-1 0-1 1-2 1s-2 1-3 2c0-2 0-3 1-4h0c-1-1-2-2-2-3h0 2c1-1 1-1 1-2z" class="N"></path><path d="M483 263l2 1v2l-1 1s-1 0-1-1l-1-1c1-1 1-1 1-2z" class="l"></path><path d="M458 257c2-1 4-1 6-2h4 11c3 1 5 2 8 2 1 1 2 2 3 2 0 0 1 1 1 2 1 0 3 1 3 2v2c0 1-1 1-1 2l-1-1c1 0 1 0 2-1 0-1 0-1-1-2h-1l-2-2v-1h-1c-1 0-1 0-2-1s-2-1-3-1c-2-1-3-1-4-1h0-4 0c-1 0-2 1-3 0v2h0c-1 1-1 2-1 3v1l-1 2v1h0c-1-1-1-2-2-3 0-2-2-4-3-5l-1-1c-1 0-1-1-2-1-2 1-3 1-5 1z" class="O"></path><path d="M466 258c1-1 1-1 2 0h1c0-1 0-2 1-2 3 1 7-1 10 1h-4-3-3v3l1 1c-1 0-1 1-2 2 0-2-2-4-3-5z" class="F"></path><path d="M471 261l-1-1v-3h3 3 0c-1 0-2 1-3 0v2h0c-1 1-1 2-1 3v1l-1 2v1h0c-1-1-1-2-2-3 1-1 1-2 2-2z" class="i"></path><path d="M471 261v3 1 1h0c-1-1-1-2-2-3 1-1 1-2 2-2z" class="N"></path><path d="M473 245c2 1 4 2 6 2 1 1 2 1 3 2l2 1c2 1 5 2 6 3 0 1 1 1 2 2h-3c-8-2-16-3-24-3h-1v-1h1 1c1-1 3-1 4-1 1-1 1-1 1-3h0c1-1 2-1 2-2z" class="W"></path><path d="M470 250c2 0 2 0 4-1 1 0 2 0 4 1h-1c-1 0-1 0-2 1h-9c1-1 3-1 4-1z" class="X"></path><path d="M473 245c2 1 4 2 6 2 1 1 2 1 3 2l2 1h-6c-2-1-3-1-4-1-2 1-2 1-4 1 1-1 1-1 1-3h0c1-1 2-1 2-2z" class="Y"></path><path d="M473 245c2 1 4 2 6 2 1 1 2 1 3 2-3 0-5 0-7-2h-4c1-1 2-1 2-2z" class="e"></path><path d="M476 257h4 0l1 1 1 1c1 1 1 2 1 4 0 1 0 1-1 2h-2v-1 1c-1 1-1 2-2 3h-1-2v1c-1 0-1 1-2 1s-2-1-2-2h0v-2-1l1-2v-1c0-1 0-2 1-3h0v-2c1 1 2 0 3 0h0z" class="h"></path><path d="M473 259l1-1c1 0 1 1 2 0h1c1 1 0 2 0 3l-1 1c0-1 0-1-1-2v-1h-2 0z" class="d"></path><path d="M477 261v1 1 1h1c-1 0-1 1-2 1h-1v-2-3c1 1 1 1 1 2l1-1z" class="s"></path><path d="M477 261v1 1 1h1c-1 0-1 1-2 1 1-1 0-1 0-2v-1l1-1z" class="u"></path><path d="M473 259h2v1 3 2h-1v-2l-1-1h-1c0-1 0-2 1-3z" class="v"></path><path d="M478 262v-1h3l1 1c0 1 0 1-1 2l1 1h-2v-1l-1-1h0l-1-1z" class="b"></path><path d="M479 263c1 0 1 0 2 1l1 1h-2v-1l-1-1h0z" class="e"></path><path d="M478 262l1 1h0l1 1v1c-1 1-1 2-2 3h-1v-1-1l-1-1c1 0 1-1 2-1h-1v-1l1-1z" class="b"></path><path d="M478 262l1 1h0l-1 2v-1h-1v-1l1-1z" class="c"></path><path d="M478 268l1-1c-1 0-1-1-1-1h-1c1-1 2-1 3-1-1 1-1 2-2 3z" class="m"></path><path d="M472 262h1l1 1v2h1 1l1 1v1 1h-2v1c-1 0-1 1-2 1s-2-1-2-2h0v-2-1l1-2v-1z" class="t"></path><path d="M474 266v2h-2v-1l2-1z" class="g"></path><path d="M477 266v1 1h-2v1c-1 0-1 1-2 1s-2-1-2-2h1 2l1-1h1l1-1z" class="Y"></path><path d="M472 262h1l1 1v2 1l-2 1v1h0-1 0v-2-1l1-2v-1z" class="n"></path><path d="M471 265l1-2v4 1h0-1 0v-2-1z" class="q"></path><path d="M446 236v-1c0-1 0-2-1-3 0-1 0 0 1-1h0c2 0 5 0 6-1l2 2s2 0 3 1 3 3 4 3l1 1c1 0 2 1 3 1h0 2c-1 0-2-1-3-2-1 0-2 0-2-2l3 2 2 1 5 4 3 2c-4 0-8-2-12-3l1 1-1 1h-1-1l-1 1c-1 1-1 1-2 1h-1-2-1 0l-1-1h0-2-1-2c-1-1-1-2-1-3v-1l-1-3z" class="I"></path><path d="M453 238s1-1 1 0h7 2-7c1 1 2 1 2 2l-3-1-3 1-2-2h3z" class="D"></path><path d="M453 238v1h2l-3 1-2-2h3z" class="Q"></path><path d="M463 238c3 1 6 3 9 3h0l3 2c-4 0-8-2-12-3h-5c0-1-1-1-2-2h7z" class="N"></path><path d="M447 239h0c1-1 2-1 3-1l2 2 3-1 3 1h5l1 1-1 1h-1-1l-1 1c-1 1-1 1-2 1h-1-2-1 0l-1-1h0-2-1-2c-1-1-1-2-1-3v-1z" class="B"></path><path d="M451 242l2-1v2h-2v-1z" class="C"></path><path d="M447 239h0c1-1 2-1 3-1l2 2h-5 0v-1z" class="p"></path><path d="M447 240h0c1 1 2 2 4 2v1h-1-2c-1-1-1-2-1-3z" class="S"></path><path d="M453 241c3 0 5 0 7 1h1l-1 1c-1 1-1 1-2 1h-1-2-1 0l-1-1h0v-2z" class="I"></path><path d="M463 240c4 1 8 3 12 3l2 1-2 1c1 0 3 1 4 2-2 0-4-1-6-2 0 1-1 1-2 2h0c0 2 0 2-1 3-1 0-3 0-4 1h-1-1-1l-3 1v1h1l-3 1-5 2h-1l-1-2v-2s-1-1-1-2v-1h0c0-1 0-3-1-4h1c1-1 1-1 2-1s1 0 1-1l1 1h0 1 2 1c1 0 1 0 2-1l1-1h1 1l1-1-1-1z" class="Q"></path><path d="M469 247l3-3 1 1c0 1-1 1-2 2h0-2z" class="o"></path><path d="M457 252h3v1h1l-3 1h-1v-2z" class="X"></path><path d="M463 248l-1-1h3 4c-1 1-1 1-2 1h-1c-1-1 0 0-1 0h1-3z" class="o"></path><path d="M463 248h3c-1 2 0 2-1 3h-1-1-1l1-1v-2z" class="i"></path><path d="M460 243v2l1 2-2 2-1-2h0 0c0-1 0-1-1-1v-1h0v-1h1c1 0 1 0 2-1z" class="D"></path><path d="M457 245h2s0 1 1 1l-2 1h0c0-1 0-1-1-1v-1h0z" class="C"></path><path d="M455 252h2v2h1l-5 2v-2c0-1 1-1 2-2z" class="c"></path><path d="M461 242h1 1l4 1-1 1c-1 0-1 1-2 1h-1-3v-2l1-1z" class="C"></path><path d="M462 242h1l4 1-1 1c-1 0-1 0-2-1-1 0-1 0-2-1z" class="D"></path><path d="M466 248h-1c1 0 0-1 1 0h1c1 0 1 0 2-1h2c0 2 0 2-1 3-1 0-3 0-4 1h-1c1-1 0-1 1-3z" class="x"></path><path d="M458 247h0l1 2-1 1c-1 1-2 1-3 2s-2 1-2 2v2h-1l-1-2 1-2c1-1 1-1 2-1 1-1 2-1 3-1 0-1-1-1-1-2h1 0l1-1h0z" class="C"></path><path d="M458 247h0l1 2-1 1v-1h-2l1-1h0l1-1h0z" class="J"></path><path d="M458 247h0v1h-1 0l1-1h0z" class="F"></path><path d="M463 240c4 1 8 3 12 3l2 1-2 1v-1c-2 0-3-1-5-1l-1 1-2-1-4-1 1-1-1-1z" class="O"></path><path d="M453 243l1 1h0 1 2v1h0v1c1 0 1 0 1 1l-1 1h0-1c0 1 1 1 1 2-1 0-2 0-3 1-1 0-1 0-2 1l-1 2v-2s-1-1-1-2v-1h0c0-1 0-3-1-4h1c1-1 1-1 2-1s1 0 1-1z" class="G"></path><path d="M450 249h1c0 1 1 1 1 1 0 1-1 2-1 2s-1-1-1-2v-1z" class="I"></path><path d="M453 243l1 1h0 1l-1 1c0 1 0 1 1 2l-3 3s-1 0-1-1h-1 0c0-1 0-3-1-4h1c1-1 1-1 2-1s1 0 1-1z" class="V"></path><path d="M449 245h1c1-1 1-1 2-1v1c-1 1-1 1-2 0h-1z" class="R"></path><path d="M437 236l1-1 1 1h0l1 2c0 1 1 3 1 4v1c1 1 2 3 3 3v1c1 1 1 2 2 3h0c1 1 1 2 2 4l1 1c1 2 2 2 2 4l1 2 1-2s-1 0-1-1l4 1 1-1 1-1c2 0 3 0 5-1 1 0 1 1 2 1l1 1c1 1 3 3 3 5 1 1 1 2 2 3h0v2h-1v3l-1 1-1 2-2 5-1 3-1 1-1 1c0 1-1 1-1 2l-1 2c-2 4-3 9-3 14l-1 1h-1 0c-1-1-1-1-1-3h-1-1v3h-1-1-2l-2-1h-1v-1s0-1-1-1c-1-1-1-3-2-5-2-3-5-6-7-8s-3-4-5-5l1-2-1-1 1-8c-1-3-1-5-1-7h1c0-1-1-2-1-2h1v-3l1-1c0-1-1-2-1-3v-4-3l1-2h0c0-1 1-2 1-3h1v-1c0-1 1-2 1-3 0 0 1 0 1-1v-1-1z" class="E"></path><path d="M451 303c0-2-1-3 0-4 1 1 1 2 1 4h-1z" class="J"></path><path d="M449 258v1h2l1 2h0-3c0-1 0-1-1-2h0l1-1zm-5 38l1-1h1c0 1 1 2 0 4h0c-1-1-1-2-2-3z" class="U"></path><path d="M444 268c-2-1-3-2-4-4h1c1 1 2 2 3 2h1l1 1v2l-2-2v1z" class="C"></path><path d="M448 254l1 1c1 2 2 2 2 4h-2v-1l-2-2c0-1 0-2 1-2z" class="S"></path><path d="M445 266l-2-3h1l1 1 1-1 2 2v1c0 1 0 2 1 3h0c-1 2-1 3 0 4-1-1-1-2-1-2v-1c0-1 0-2-1-3v-1h-1v1l-1-1z" class="K"></path><path d="M432 280l1 1h0c2 1 3 2 5 4l-2 2c-2-2-3-4-5-5l1-2h0z" class="H"></path><path d="M444 247c1 1 1 2 2 3h0c1 1 1 2 2 4-1 0-1 1-1 2-1-1-2-1-2-2l-3-3c0-1 1-1 1-2h0c1 0 1-1 1-2z" class="F"></path><path d="M438 285s1 1 1 2c-1 0-1 0-1 1h1c2 2 4 5 5 8 1 1 1 2 2 3h0c1 1 1 1 1 2v1h-1v-1s0-1-1-1c-1-1-1-3-2-5-2-3-5-6-7-8l2-2z" class="L"></path><path d="M435 263l2 1-1 3v2l-4 11h0l-1-1 1-8c-1-3-1-5-1-7h1 1l1-1h1z" class="R"></path><path d="M432 268l1-3h1v2c0 1 0 2-1 2 0 0-1 0-1-1z" class="k"></path><path d="M435 263l2 1-1 3v2l-2-2v-2h-1l-1 3v3c-1-3-1-5-1-7h1 1l1-1h1z" class="O"></path><path d="M434 265h0c1 0 1 0 1 1 0 0 1 0 1 1v2l-2-2v-2z" class="S"></path><path d="M437 236l1-1 1 1h0l1 2c0 1 1 3 1 4v1c1 1 2 3 3 3v1c0 1 0 2-1 2h0c0 1-1 1-1 2-1 0-1 0-1 1l-1 1v1c0 2-1 3-2 5l-1 5-2-1h-1l-1 1h-1c0-1-1-2-1-2h1v-3l1-1c0-1-1-2-1-3v-4-3l1-2h0c0-1 1-2 1-3h1v-1c0-1 1-2 1-3 0 0 1 0 1-1v-1-1z" class="C"></path><path d="M432 255l2-2h0v2s0 1-1 1v2c0-1-1-2-1-3z" class="O"></path><path d="M431 262h1v-3 2h1c0 1 1 2 1 2l-1 1h-1c0-1-1-2-1-2z" class="B"></path><path d="M436 252c1 0 1 0 1 1l-1 3-1 3h-1v-3c1 0 1-1 1-2l1-2h0z" class="M"></path><path d="M435 259v1 1l2-2h1l-1 5-2-1v-2s-1 0-1-1v-1h1z" class="G"></path><path d="M438 255s1 0 1-1h0 1c0 2-1 3-2 5h-1l-2 2v-1-1l1-3c1 0 1 0 2-1z" class="F"></path><path d="M439 247l1-1 1 1v2c-1 1-1 2-2 3l1 1v1h-1 0c0 1-1 1-1 1-1 1-1 1-2 1l1-3h1c0-2 1-4 1-6z" class="a"></path><g class="P"><path d="M439 252l1 1v1h-1 0c0 1-1 1-1 1 0-2 0-2 1-3z"></path><path d="M441 242v1c1 1 2 3 3 3v1c0 1 0 2-1 2h0c0 1-1 1-1 2-1 0-1 0-1 1l-1 1-1-1c1-1 1-2 2-3v-2-3-2z"></path></g><path d="M439 252c1-1 1-2 2-3v3l-1 1-1-1z" class="N"></path><path d="M441 243c1 1 2 3 3 3l-1 2h-1c-1-1-1-4-1-5z" class="K"></path><path d="M434 243h1l1 1c-1 1-1 1 0 2h0l1-3c1 1 1 1 1 2v2h1c0 2-1 4-1 6h-1c0-1 0-1-1-1h0l-1-2h-1l-1 2h0s-1 0-1-1v-3l1-2h0c0-1 1-2 1-3z" class="Y"></path><path d="M433 246c1 0 1 1 2 2v1h-2c0-1-1-1-1-1l1-2h0z" class="W"></path><path d="M432 251v-3s1 0 1 1h1 1v1h-1l-1 2h0s-1 0-1-1z" class="c"></path><path d="M438 247h1c0 2-1 4-1 6h-1c0-1 0-1-1-1 1-2 1-4 2-5z" class="D"></path><path d="M437 236l1-1 1 1h0l1 2c0 1 1 3 1 4v2 3l-1-1-1 1h-1v-2c0-1 0-1-1-2l-1 3h0c-1-1-1-1 0-2l-1-1h-1 1v-1c0-1 1-2 1-3 0 0 1 0 1-1v-1-1z" class="e"></path><path d="M438 240c1 0 1 0 1-1v3 3h-1c0-1 0-1-1-2l1-3z" class="m"></path><path d="M440 238c0 1 1 3 1 4v2 3l-1-1-1 1h-1v-2h1v-3c0-1 0-3 1-4z" class="N"></path><path d="M441 244v3l-1-1-1 1h-1v-2h1v1l2-2z" class="o"></path><path d="M437 236c0 2 0 3 1 4h0l-1 3-1 3h0c-1-1-1-1 0-2l-1-1h-1 1v-1c0-1 1-2 1-3 0 0 1 0 1-1v-1-1z" class="D"></path><path d="M458 257c2 0 3 0 5-1 1 0 1 1 2 1l1 1c1 1 3 3 3 5 1 1 1 2 2 3h0v2h-1v3l-1 1-1 2-2 5-1 3-1 1-1 1c0 1-1 1-1 2l-1 2c-2 4-3 9-3 14l-1 1h-1 0c-1-1-1-1-1-3h-1-1v-7-2c-1-1 0-3 0-4-1-2-1-3-1-4-3-6-5-11-8-15v-1l2 2v-2-1h1v1c1 1 1 2 1 3v1s0 1 1 2l1 1h0v-2l-1-1v-2h0c1-1 0-6 0-8h3 0l1-2s-1 0-1-1l4 1 1-1 1-1z" class="I"></path><path d="M466 270v1 3l-1-1h0 0l1-3z" class="S"></path><path d="M456 276v-1c1-1 2-2 3-2v2h-1c-1 0-1 0-2 1zm2 3l1-1c0 1 1 2 2 3v1l-1 1-1-2c-1-1-1-1-1-2z" class="P"></path><path d="M467 267l2-1v4c-1-1-1-2-1-3h-1v1h-1v2l-1 3h-1v2l-2 3v-2c1 0 1-1 2-1l-1-1-3 1s2-1 3-2c-1 0-1-1-1-2l-1-1c1 0 2-2 2-3h0v2h1 0l1-1v-1h2z" class="C"></path><path d="M463 267v2h1-1v3h-1 0v-1l-1-1c1 0 2-2 2-3h0z" class="P"></path><path d="M465 257l1 1c1 1 3 3 3 5 1 1 1 2 2 3h0v2h-1v3l-1 1-1 2-2 5-1 3-1 1-1 1c2-4 4-10 6-14v-4-2c-1-1-2-1-3-1h-2v-2l-1-1c0-1 0-2 1-2v-1h1z" class="h"></path><path d="M463 260c0-1 0-2 1-2 0 0 0 1 1 1 0 1 1 2 1 2v2h-2v-2l-1-1z" class="e"></path><path d="M454 291h0c1-2 1-3 0-4v-1h1v1 2h1c0 1 0 2 1 4h0l1-1v-1h0c0-1 0-1 1-2h0c1-1 1-1 2-1-2 4-3 9-3 14l-1 1h-1 0c-1-1-1-1-1-3h-1-1v-7-2h1z" class="U"></path><path d="M454 291v2h-1v-2h1z" class="L"></path><path d="M455 293v2h0c1 0 1 0 2 1 0 1-2 2-2 4h-1c1-2 1-2 0-4 0-1 0-2 1-3z" class="M"></path><path d="M454 293h1c-1 1-1 2-1 3 1 2 1 2 0 4h-1v-7h1z" class="C"></path><path d="M458 257c2 0 3 0 5-1 1 0 1 1 2 1h-1v1c-1 0-1 1-1 2l1 1v2h2c1 0 2 0 3 1v2l-2 1h-2v1l-1 1h0-1v-2h0c0 1-1 3-2 3-1 1-2 2-2 3h0c-1 0-2 1-3 2v1h-1c0 4 1 8 1 11l-1 1v-3c-1 0-1-1-2-2h-1c-3-6-5-11-8-15v-1l2 2v-2-1h1v1c1 1 1 2 1 3v1s0 1 1 2l1 1h0v-2l-1-1v-2h0c1-1 0-6 0-8h3 0l1-2s-1 0-1-1l4 1 1-1 1-1z" class="G"></path><path d="M452 269c1 0 1 1 1 1v1l-1-1v-1z" class="I"></path><path d="M447 267c1 1 1 2 1 3-1-1-1-1-1-2v-1z" class="J"></path><path d="M452 262h1v1c1 1 1 0 1 2h0c-1 0-1 0-1-1l-1-2z" class="C"></path><path d="M450 264h0c1-1 2-1 3 0-1 1 0 1-1 2h-1c-1-1-1-1-1-2z" class="p"></path><path d="M458 266c-1 1-1 2-1 2l-1 1-1-1v-1l2-2s0 1 1 1z" class="D"></path><path d="M452 258l4 1h-2l-1 4v-1h-1v-1h0l1-2s-1 0-1-1z" class="L"></path><path d="M453 259v3h-1v-1h0l1-2z" class="E"></path><path d="M451 270l1 2h0v4l-1-1-1-3c1-1 1-1 1-2z" class="P"></path><path d="M459 261l1 1h0v2h0v3h1v-1h1v2h-1c-1 0-1 0-1 1h-1l-1-3c-1 0-1-1-1-1 0-1 1-2 1-3l1-1z" class="Q"></path><path d="M449 261h3v1l1 2c-1-1-2-1-3 0h0c-1 1 0 3 0 5h1c1 1 1 1 0 1 0 1 0 1-1 2l1 3v1c-1-1-2-2-2-3l1 1h0v-2l-1-1v-2h0c1-1 0-6 0-8z" class="I"></path><path d="M450 269h1c1 1 1 1 0 1 0 1 0 1-1 2v-3z" class="Q"></path><path d="M453 283v-8h0c1-2 1-4 1-6h2l1 1c-1 2-2 4-2 6 0 4 1 8 1 11l-1 1v-3c-1 0-1-1-2-2z" class="U"></path><path d="M458 257c2 0 3 0 5-1 1 0 1 1 2 1h-1v1c-1 0-1 1-1 2l-1-1c-1 1-1 1-1 2v2 3h0v1h-1v-3h0v-2h0l-1-1-1 1v-1-1l-2 1h-1c0-2 1-2 2-3l1-1z" class="i"></path><path d="M458 257c2 0 3 0 5-1 1 0 1 1 2 1h-1c-1 0-1 1-3 1-1 1-1 0-3 1 0 1 1 1 1 2l-1 1v-1-1l-2 1h-1c0-2 1-2 2-3l1-1z" class="J"></path><path d="M461 266h0v-3-2c0-1 0-1 1-2l1 1 1 1v2h2c1 0 2 0 3 1v2l-2 1h-2v1l-1 1h0-1v-2h0l-1 1h-1 1v-2h-1z" class="D"></path><path d="M462 266v-1h1v2h0l-1 1h-1 1v-2zm2 3s1-1 1-2v-2h1s0 1 1 2h-2v1l-1 1z" class="p"></path><path d="M411 221c1 0 1 1 2 1 0 1 1 0 2 0v1h5c-1 0-1 1-1 1h-1 2l1-1c1 0 1 0 2 1h0c1 0 2 1 3 1 1 1 1 1 2 1l1-1c1 1 1 1 1 3l-1 1h1c0 1 0 1 1 2 0 1-1 1-1 2h0l-2 2v1h0c-1 1-2 2-3 2h0c-1 1-1 2-2 2v1h-1l-3 3c1 0 1 0 2 1h0l1-1h1 1 1 0c2-2 3-3 5-4h0c1 0 1 1 1 2 1 0 1-1 1-1 0-2 1-2 2-4v-2c0 2 1 4 0 6 0 1 1 1 1 2h-1c0 1-1 2-1 3h0l-1 2v3 4c0 1 1 2 1 3l-1 1v3h-1s1 1 1 2h-1c0 2 0 4 1 7l-1 8 1 1-1 2c2 1 3 3 5 5s5 5 7 8c1 2 1 4 2 5 1 0 1 1 1 1v1h-1-3l-6-1-17-4c-1 0-3-1-4-2h0l-4-1-4-1v1c-1 0-3-1-4-2l-4-4v-1c0-1-1-2-2-3l-1-1h1l2-6v-1c-2 0-5 0-7-1h-4l2 1c-1 0-2 1-3 1l-3-1h-4v-3c-1-1-1-2-1-3h-1l-1-2v-1l-1-5v-3h0c-1-3-3-6-4-9l-2-2h0c1 0 1-1 1-2l1-1v1h1l1-3 4-4h0l3-1h3 0l-1-1c1-1 1-2 2-3h0l1-2c-1-1-1-2-2-3 0 0 1-1 1-2 0 0 1 0 1-1h-3 11 0l6-1v-1h1 3l2-1 1 1 1-1c1-1 1-1 3-2z" class="Z"></path><path d="M431 289l-1-4 3 3h-1l-1 1zm-17-20c-2-1-5-1-6-2 2-1 5 0 6 1v1z" class="T"></path><path d="M419 244c1 0 1 0 2 1-2 1-4 1-6 3h0-1c1-2 4-3 5-4z" class="C"></path><path d="M431 264h0c-1-2-1-4-1-6h1v4s1 1 1 2h-1z" class="V"></path><path d="M406 248c1 0 2-1 3-2l-2 7c-1 0-1-1-1-2h0l1-1-1-1v-1z" class="Ab"></path><path d="M423 263v-1l1 1h0c0 3 2 6 4 8h-1c-2-2-4-5-4-8z" class="O"></path><path d="M421 223c1 0 1 0 2 1h0c1 0 2 1 3 1 1 1 1 1 2 1l1-1c1 1 1 1 1 3l-1 1h-2c-1-1-2-1-3-2 1 0 1 0 2 1 0-1 0-1 1-1h0c-1-2-5-2-6-4z" class="H"></path><path d="M403 292l-4-4v-1c2 2 4 3 6 4l1 1 1 1v1c-1 0-3-1-4-2z" class="d"></path><path d="M421 245l1-1h1 1 1 0c1 0 2 1 2 1v2l1 2c-1 0-1 1-1 2h-1c-1-3-2-5-5-6z" class="K"></path><path d="M423 289l5 9-5-1v-1c-1-2-1-3-2-4l1-1c0-1 0-1 1-2z" class="h"></path><path d="M421 292l1-1c1 1 2 2 2 3v1l-1 1c-1-2-1-3-2-4z" class="d"></path><path d="M407 236c2-1 4-1 6-1-1 2-1 4-2 6h-1c-2-2-4-3-6-3l-5-1c2 0 5 0 8-1z" class="AR"></path><path d="M423 263c0 3 2 6 4 8h1c1 0 1 1 2 2v1c-1 0-2-1-3-2-1 0-1 0-1 1s1 2 2 3l-1-1h-1c-1-2-1-3-2-4s-1-2-3-2h-3c2-1 1-2 1-3l2 1 1-1c0-1 0-1 1-2v-1z" class="V"></path><path d="M431 289l1-1h1c1 3 3 8 6 11l3 3-6-1 1-1c-3-3-4-7-6-11z" class="H"></path><path d="M406 290c-1-2-2-4-2-5h1c1-1 1-2 1-2 2 0 1-1 3-2h0v-2h0v-1c1-1 0-1 1-2l1 1 2-1c0-1-1-1-1-2 1 1 2 2 3 2-2 1-3 2-3 3-1 2-1 4-3 5-1 1-3 4-3 5v1zm8-21v-1l4 1h3c2 0 2 1 3 2s1 2 2 4h1l1 1 3 3h0l1 1-1 2c-1-2-2-3-3-5-1-1-3-2-4-3-3-2-7-4-10-5z" class="S"></path><path d="M421 269c2 0 2 1 3 2-1 0-2-1-3-1v-1z" class="f"></path><path d="M434 235c0 2 1 4 0 6 0 1 1 1 1 2h-1c0 1-1 2-1 3h0v-2h0 0l1-1v-1h-1 0c0 1 0 2-1 2h-1c-1 1-1 1-1 2-1 0-1 1-1 1h-1v2l-1-2v-2s-1-1-2-1c2-2 3-3 5-4h0c1 0 1 1 1 2 1 0 1-1 1-1 0-2 1-2 2-4v-2z" class="M"></path><path d="M430 240v2 2l-2 3v2l-1-2v-2s-1-1-2-1c2-2 3-3 5-4z" class="d"></path><path d="M420 284c1 1 0 2 2 2l1 3c-1 1-1 1-1 2l-1 1c1 1 1 2 2 4v1l-1-1c-1 0-3-1-4-1v1h1v1c-1 0-3-1-4-2h0c0-1 1-1 1-2 1-1 1-2 2-3s2-4 2-6z" class="B"></path><path d="M422 286l1 3c-1 1-1 1-1 2l-1 1-1-2c0-1 1-3 2-4z" class="X"></path><path d="M415 295h1c1-1 2-3 4-5l1 2c1 1 1 2 2 4v1l-1-1c-1 0-3-1-4-1v1h1v1c-1 0-3-1-4-2z" class="Y"></path><path d="M401 234h8-2 3 2s0 1-1 1h-2-1c-1 1-1 0-2 0v1h-1 2c-3 1-6 1-8 1h-2l-13 1-1-1c1-1 1-2 2-3h0 16z" class="Ag"></path><path d="M385 234h16 0c-3 0-7 0-10 1h-2l-1 1h1 0 1 1c2 1 4 1 6 1l-13 1-1-1c1-1 1-2 2-3h0z" class="AE"></path><path d="M415 276l2 3c1 2 2 3 3 5h0c0 2-1 5-2 6s-1 2-2 3c0 1-1 1-1 2l-4-1-4-1-1-1-1-1h1v-1-1c0-1 2-4 3-5 2-1 2-3 3-5 0-1 1-2 3-3z" class="D"></path><path d="M406 292l1-1h1l2-2c0-1 1-1 1-2 2-2 1-4 3-6 1-1 1-2 3-2 1 2 2 3 3 5-1 0-2-1-3-1v-1h-1c-1 1-2 2-2 3 0 2 0 3-1 5l1 1-1 1-2 2-4-1-1-1z" class="e"></path><path d="M411 294l2-2 1-1-1-1c1-2 1-3 1-5 0-1 1-2 2-3h1v1c1 0 2 1 3 1h0c0 2-1 5-2 6s-1 2-2 3c0 1-1 1-1 2l-4-1z" class="h"></path><path d="M417 283c1 0 2 1 3 1h0c0 2-1 5-2 6-1 0-2 0-3-1 1-2 1-4 2-6z" class="d"></path><defs><linearGradient id="k" x1="406.263" y1="218.901" x2="393.792" y2="237.626" xlink:href="#B"><stop offset="0" stop-color="#d0332d"></stop><stop offset="1" stop-color="#e6755d"></stop></linearGradient></defs><path fill="url(#k)" d="M411 221c1 0 1 1 2 1 0 1 1 0 2 0v1h5c-1 0-1 1-1 1h-1c-2 2-4 9-5 9s-3 0-4 1h-8-16l1-2c-1-1-1-2-2-3 0 0 1-1 1-2 0 0 1 0 1-1h-3 11 0l6-1v-1h1 3l2-1 1 1 1-1c1-1 1-1 3-2z"></path><path d="M406 223l1 1s-1 0 0 1h1c-5 1-10 1-14 1h0l6-1v-1h1 3l2-1z" class="AD"></path><path d="M406 223l1 1s-1 0 0 1c-2 0-2 0-3-1l2-1z" class="AF"></path><path d="M411 221c1 0 1 1 2 1 0 1 1 0 2 0v1h5c-1 0-1 1-1 1l-11 1h-1c-1-1 0-1 0-1l1-1c1-1 1-1 3-2z" class="AZ"></path><path d="M411 221c1 0 1 1 2 1 0 1 1 0 2 0v1h-7c1-1 1-1 3-2z" class="K"></path><defs><linearGradient id="l" x1="403.863" y1="269.462" x2="380.802" y2="239.5" xlink:href="#B"><stop offset="0" stop-color="#ba2d24"></stop><stop offset="1" stop-color="#e66652"></stop></linearGradient></defs><path fill="url(#l)" d="M397 237h2l5 1c2 0 4 1 6 3h1l-1 2-1 3c-1 1-2 2-3 2v1l1 1-1 1h0c0 1 0 2 1 2l-6 18c-1 1-1 4-2 5-2 0-5 0-7-1h-4l2 1c-1 0-2 1-3 1l-3-1h-4v-3c-1-1-1-2-1-3h-1l-1-2v-1l-1-5v-3h0c-1-3-3-6-4-9l-2-2h0c1 0 1-1 1-2l1-1v1h1l1-3 4-4h0l3-1h3 0l13-1z"></path><path d="M378 239l3-1 1 2-3 1-1-2h0z" class="AP"></path><path d="M379 266v4h-1l-1-2v-1h1l1-1z" class="AC"></path><path d="M381 238h3l-1 1h0 2 1 0l-4 1-1-2z" class="AG"></path><path d="M374 243l4-4 1 2c-1 0-3 1-4 2h-1z" class="AU"></path><path d="M404 238c2 0 4 1 6 3h-1c-2-1-4-2-7-2l2-1z" class="AG"></path><path d="M376 258l3 8-1 1h-1l-1-5v-3h0v-1z" class="AY"></path><path d="M391 274h8v2c-2 0-5 0-7-1h-2 0 1v-1h0z" class="AL"></path><path d="M396 245c-2 0-8 0-9-1h3 6 2c-1-1-3-1-4-1h-1c3-1 6 0 8 0h6v1h-4c-2 0-5 0-7 1z" class="AB"></path><path d="M380 273c3 1 7 1 11 1h0v1h-1 0 2-4l2 1c-1 0-2 1-3 1l-3-1h-4v-3z" class="AJ"></path><path d="M380 276l1-1h7l2 1c-1 0-2 1-3 1l-3-1h-4z" class="AW"></path><path d="M374 243h1c-2 5-1 9 1 13v2 1c-1-3-3-6-4-9l-2-2h0c1 0 1-1 1-2l1-1v1h1l1-3z" class="AD"></path><path d="M371 246l1-1v1 4l-2-2h0c1 0 1-1 1-2z" class="AK"></path><path d="M397 237h2l5 1-2 1h-13-3 0-1-2 0l1-1h0l13-1z" class="Ad"></path><path d="M384 238c3 0 6 0 8 1h0-3-3 0-1-2 0l1-1z" class="AQ"></path><path d="M396 248h2 2 2 4v1l1 1-1 1h0-8c-1-1-2-1-4-1h-1c-1 0 0-1-1-1h-1 2v-1h3z" class="AJ"></path><path d="M396 248h2 2 2 4v1l1 1-1 1v-1c0-1-2-1-3-1h-10v-1h3z" class="AO"></path><path d="M407 243h3 0l-1 3c-1 1-2 2-3 2h-4-2-2-2 1 1c0-3-1-2-2-3 2-1 5-1 7-1h4v-1z" class="AL"></path><path d="M400 248l1-1s1 0 1 1h-2z" class="AJ"></path><path d="M407 243h3l-1 1h-2v-1z" class="AS"></path><path d="M272 491l-1 1 1 1 1-1h2v1 1c2-1 5-1 7 0 0 0 1 1 2 1h1 1s1 0 1 1 1 1 2 1l2 2h3c1 0 1-1 2-1v-1h-3 0c0-1 1 0 1-1h2v-1c1 4 2 8 5 10 2 2 4 2 7 3l1 1h0 0c1 0 1 1 2 1h0c-1 0-1 0-1 1v-1l-1 1c4 1 8 0 11 1-6 3-13 6-20 7l-3 1-9 2c-2 1-4 2-6 2 0-1-2-4-3-4 0 1 2 3 2 4l-17 6-15 4-13 5c-2 0-3 1-5 1l-1-1-1 1c0 1-2 1-3 1l-13 4-11 2-12 3h-4v-1c0-1-1-2-1-3-1 1 0 3 0 4-17 1-35 0-50-7-6-3-12-7-17-11 1 1 2 1 3 1 1 1 3 1 3 2 12 3 23 5 36 5 3-1 6-1 10-2 4 0 8-1 12-2s9-3 13-5 7-5 11-6l1 1h1l-1-1c1-2 4-4 6-5l1-1 1-1 2-2h0l6-6h0c0-1 1-1 1-2l1-2c3 2 5 5 8 7h1l3 3 1-2 2-1c1-1 3-2 3-3 4-2 5-8 8-10 0-1 2-1 3-1 1-2 5-3 7-4h1 0c2 0 3 0 4-1l6-3z" class="AR"></path><path d="M297 520v-3-1l1 1c0 1 1 1 2 2h0l-3 1z" class="Ah"></path><path d="M243 510c4-2 5-8 8-10 0-1 2-1 3-1h0l-1 1s0 1-1 2-3 3-4 5v1h-1 1c-1 1-2 1-2 1-1 1-1 2-3 3-1 2-1 2-1 4h0l-1-1c-2 0-3 1-4 2l-7 6h-1v-1l6-5 2-1 1-2 2-1c1-1 3-2 3-3z" class="AK"></path><path d="M266 494v2l-4 2h-1l-1 1s0 1-1 1c-1 1-2 1-2 2-1 1-2 2-2 3v1h0l-7 2h-1 1v-1c1-2 3-4 4-5s1-2 1-2l1-1h0c1-2 5-3 7-4h1 0c2 0 3 0 4-1z" class="AC"></path><defs><linearGradient id="m" x1="296.402" y1="497.341" x2="296.663" y2="510.656" xlink:href="#B"><stop offset="0" stop-color="#a0312c"></stop><stop offset="1" stop-color="#dd5340"></stop></linearGradient></defs><path fill="url(#m)" d="M285 495h1s1 0 1 1 1 1 2 1l2 2h3c1 0 1-1 2-1v-1h-3 0c0-1 1 0 1-1h2v-1c1 4 2 8 5 10 2 2 4 2 7 3l1 1h0 0c1 0 1 1 2 1h0c-1 0-1 0-1 1v-1l-1 1c-2 0-6-1-9-2-6-1-12-2-18-2l1-1h1c1-1 3-2 3-3v-1h1 0l-1-1h-1v-1c0-2-1-3-2-5h1z"></path><path d="M272 491l-1 1 1 1 1-1h2v1 1c2-1 5-1 7 0 0 0 1 1 2 1 1 2 2 3 2 5v1h1l1 1h0-1v1c0 1-2 2-3 3h-1l-1 1c-5 0-11-1-16-1h-11 0v-1c0-1 1-2 2-3 0-1 1-1 2-2 1 0 1-1 1-1l1-1h1l4-2v-2l6-3z" class="AL"></path><g class="AG"><path d="M266 496c2-1 3-2 5-3h2l-1 1-9 5s0-1-1-1l4-2z"></path><path d="M261 498h1c1 0 1 1 1 1l-2 1-6 6v-1c0-1 1-2 2-3 0-1 1-1 2-2 1 0 1-1 1-1l1-1z"></path></g><path d="M261 498h1c1 0 1 1 1 1l-2 1v-2z" class="AI"></path><path d="M272 491l-1 1 1 1 1-1h2v1c0 1-1 2-1 2h-2v-1l1-1h-2c-2 1-3 2-5 3v-2l6-3z" class="AK"></path><path d="M275 494c2-1 5-1 7 0 0 0 1 1 2 1 1 2 2 3 2 5v1l-1 2h1c0 1-1 1-1 1-1 2-3 1-5 1-1-1-2-1-3-2s-3-3-3-4c-1-1-1-2-2-3v-1h2s1-1 1-2v1z" class="AB"></path><path d="M281 497h0c1 0 2 0 3 1 1 2 1 2 2 2v1l-1 2h0-1s-1 1-2 1c-2-1-3-1-4-3h0c1-1 1-1 1-2l2-2z" class="AE"></path><path d="M281 497h0c0 2 0 2-1 4h-2c1-1 1-1 1-2l2-2z" class="Af"></path><g class="Ah"><path d="M284 498c1 2 1 2 2 2v1l-1 2h0l-1-1v-4z"></path><path d="M275 494c2-1 5-1 7 0 0 0 1 1 2 1 1 2 2 3 2 5-1 0-1 0-2-2-1-1-2-1-3-1h0l-2 2c-1-1-2-2-4-2h-1v-2s1-1 1-2v1z"></path></g><path d="M284 498l-1-1h0v-1l1 1h0l-1-1 1-1c1 2 2 3 2 5-1 0-1 0-2-2z" class="Ag"></path><path d="M275 493v1l1 2h1c2 0 3 0 4 1l-2 2c-1-1-2-2-4-2h-1v-2s1-1 1-2z" class="AA"></path><path d="M225 506c3 2 5 5 8 7h1l3 3-2 1-6 5v1h1c-6 4-13 8-19 11-13 5-28 8-42 9-3 0-6 1-9 0h0c-9 0-19-2-27-5-3-1-6-1-9-3h0c12 3 23 5 36 5 3-1 6-1 10-2 4 0 8-1 12-2s9-3 13-5 7-5 11-6l1 1h1l-1-1c1-2 4-4 6-5l1-1 1-1 2-2h0l6-6h0c0-1 1-1 1-2l1-2z" class="AB"></path><path d="M124 535c12 3 23 5 36 5h4-5c-2 1-7 1-8 0h-4v1s1 0 1 1h3 3 0 1c3 1 6 0 9 0-1 1-3 1-4 1-9 0-19-2-27-5-3-1-6-1-9-3h0z" class="AP"></path><path d="M213 520c1 0 1 1 2 2 0 1 0 1 1 1v1l1 1 3 3c-2 1-4 2-7 4 0 0-1 1-2 1l-1-1c-1 0-1 0-1-1-1-1-2-3-3-5-4 2-7 4-11 5 4-2 7-5 11-6l1 1h1l-1-1c1-2 4-4 6-5z" class="AM"></path><path d="M225 506c3 2 5 5 8 7h1l3 3-2 1-6 5-9 6-3-3-1-1v-1c-1 0-1 0-1-1-1-1-1-2-2-2l1-1 1-1 2-2h0l6-6h0c0-1 1-1 1-2l1-2z" class="AA"></path><path d="M229 513h1c1 0 1 2 2 2s1 1 2 2h-1c-1 0-3-3-4-4z" class="AE"></path><path d="M232 515l2-2 3 3-2 1h-1c-1-1-1-2-2-2z" class="AV"></path><path d="M225 506c3 2 5 5 8 7h1l-2 2c-1 0-1-2-2-2h-1 0l-1-1c-1-1-3-2-5-2h0c0-1 1-1 1-2l1-2z" class="Af"></path><path d="M224 508c2 1 3 2 4 4h0c-1-1-3-2-5-2h0c0-1 1-1 1-2z" class="AR"></path><defs><linearGradient id="n" x1="358.686" y1="373.92" x2="253.508" y2="319.442" xlink:href="#B"><stop offset="0" stop-color="#802621"></stop><stop offset="1" stop-color="#c83430"></stop></linearGradient></defs><path fill="url(#n)" d="M281 255c1 5 2 7 5 11l2 2 4 4h1v11 17c0 18 1 37 0 55l-10 10 1 1c2-3 5-5 9-7 2-2 6-2 9-3 4 0 9-1 13-3l7-2h0c1 0 2 0 3-1 0 2 0 4-1 6 0 2-2 4-3 6-1 1-2 2-4 2 1 0 2 1 2 0h1c2 0 3-2 5-2 2 1 3 1 4 2h1v1h1v1c2 1 3 1 5 2h1 1 1c-1 2-1 3-1 5l1 1v2c2 3 3 5 5 7v2l-1 1 4 16h-1-1l-4 3c-1-1-2-5-3-6-1-3-3-6-5-8-7-8-17-12-26-14-6-1-13-1-18-1 1 2 2 3 5 5v21 10c-3-2-6-2-9-2-1 0-3-1-4-1-2-1-4-1-6-1-1-2 0-5 0-7h0v-2-3-14c0-3 0-7-1-11v-5-8h0l-1 1h-1v-2-1c-2 2-5 2-8 3h0l-3 1-1-1v1 1h-1-1v-3h0v-1c0-2-1-4-2-6v-1c-1-1 0-3-1-3l-1-1h-4-10c0-1 0-4-1-5v-27l1 1v1h3c1 1 2 1 4 1 3 0 7-1 10 0l-1-1v-3h1c0-1 1-2 1-4l1 1c2-1 3-2 4-2l1-1v-1c1 0 2-1 3-1h2 0c2-1 1-1 2-2v1h0l2-10 1-5c0-3-1-5-2-8l1-1 1 2v-1c0-4 2-8 3-12s2-9 3-14l1 1v-1z"></path><path d="M275 399h2v1l-2 1h0v-2z" class="AO"></path><path d="M277 399l4 1v1h-1l-3-1v-1z" class="AL"></path><path d="M281 400h3l1 1-1 1-4-1h1v-1z" class="Ab"></path><path d="M284 400l6 1c1 1 2 1 3 1v1c-3 0-6-1-9-1l1-1-1-1z" class="AO"></path><path d="M321 362c-1 1-2 2-4 2 1 0 2 1 2 0h1c2 0 3-2 5-2 2 1 3 1 4 2h1c-6 0-11 1-16 2h-1l1-1 7-3z" class="AD"></path><path d="M274 334c1 2 1 3 2 5 0 1-1 3-1 5 0 4 0 7-1 11v3h0l-1 1h-1v-2-1-1-1l1-1c1-1 0-4 0-6 1-4 2-7 1-11v-2z" class="AU"></path><path d="M272 355l1 1 1 2-1 1h-1v-2-1-1z" class="AF"></path><path d="M330 365h1v1c2 1 3 1 5 2h1 1 1c-1 2-1 3-1 5l1 1v2c2 3 3 5 5 7v2l-1 1c-2-5-5-9-7-13-1-1-3-4-4-5h-1c-1-1-1-2-1-3z" class="AQ"></path><path d="M338 368h1c-1 2-1 3-1 5l1 1v2c-1-1-2-4-4-6l1-1h0c1 0 1 0 2-1z" class="AX"></path><path d="M277 400l3 1 4 1c3 0 6 1 9 1v-1h1v10c-3-2-6-2-9-2-1 0-3-1-4-1-2-1-4-1-6-1-1-2 0-5 0-7l2-1z" class="AM"></path><path d="M322 351c1 0 2 0 3-1 0 2 0 4-1 6 0 2-2 4-3 6l-7 3h-1c-1 0-2 1-3 1-2 1-5 1-7 1-8 0-16 0-23 2 1-1 3-2 4-3 2-3 5-5 9-7 2-2 6-2 9-3 4 0 9-1 13-3l7-2h0z" class="AD"></path><path d="M322 351c1 0 2 0 3-1 0 2 0 4-1 6 0 2-2 4-3 6l-7 3h-1c3-2 5-3 7-5s3-5 3-8l-1-1h0z" class="AQ"></path><path d="M281 255c1 5 2 7 5 11l-1 1c0 1 1 1 1 2h1-1l-1 1h-2c-1 1-2 0-2 1l-2-1v1c1 1 2 3 2 5h0c1 1 2 4 1 5v6c0 1 0 2-1 3 0 2-1 4-1 5s1 2 1 2c1 0 0 0 0 1s1 1 2 2c0 2 1 4 1 5v1s1 0 1 1 1 1 1 2-1 2-2 3-3 1-4 1-1 1-1 1l-1 1h-1v1h-2c0-1 1-2 1-3-1 0-2-1-3-3-2-3 0-10 1-14l-1-2 1-5c0-3-1-5-2-8l1-1 1 2v-1c0-4 2-8 3-12s2-9 3-14l1 1v-1z" class="Ab"></path><path d="M274 289v-2s1 0 1-1c0 4 0 7-1 10l-1-2 1-5z" class="AF"></path><path d="M273 280l1 2v-1h1v5c0 1-1 1-1 1v2c0-3-1-5-2-8l1-1z" class="AD"></path><path d="M280 255l1 1c-1 8-5 16-6 25h-1c0-4 2-8 3-12s2-9 3-14z" class="AF"></path><path d="M271 304h0l2-10 1 2c-1 4-3 11-1 14 1 2 2 3 3 3 0 1-1 2-1 3v9c0 3-1 6-1 9v2c1 4 0 7-1 11 0 2 1 5 0 6l-1 1v1 1c-2 2-5 2-8 3h0l-3 1-1-1v1 1h-1-1v-3h0v-1c0-2-1-4-2-6v-1c-1-1 0-3-1-3l-1-1h-4-10c0-1 0-4-1-5v-27l1 1v1h3c1 1 2 1 4 1 3 0 7-1 10 0l-1-1v-3h1c0-1 1-2 1-4l1 1c2-1 3-2 4-2l1-1v-1c1 0 2-1 3-1h2 0c2-1 1-1 2-2v1z" class="AL"></path><path d="M258 344h4 0c-1 1-3 2-4 3h-2v-3h2z" class="Aa"></path><path d="M269 305c2-1 1-1 2-2v1 4c-1 0-1 0-2-1h-1l-3 1v-1l-2 1 1-1v-1c1 0 2-1 3-1h2 0z" class="AS"></path><path d="M267 305h2 0v1h0c-2 0-3 1-4 1l-2 1 1-1v-1c1 0 2-1 3-1z" class="AC"></path><path d="M258 353c0-1 1-2 1-4 2 3 2 5 4 8h0c1 1 1 1 1 2h0l-3 1-1-1c0-2-1-4-2-6z" class="AJ"></path><path d="M256 347h2s1 1 1 2c0 2-1 3-1 4 1 2 2 4 2 6v1 1h-1-1v-3h0v-1c0-2-1-4-2-6v-1-3z" class="AF"></path><path d="M258 358c1 0 1 0 1 1l1 1v1h-1-1v-3z" class="AD"></path><path d="M256 347h2s1 1 1 2c0 2-1 3-1 4 0-1-1-4-2-4v2-1-3z" class="AG"></path><path d="M263 308l2-1v1c-2 1-4 1-5 2 1 1 1 1 1 2h1s1 0 1-1v-1h1v-1h1-1v1c0 2-2 3-3 4 0 2 1 4 1 5-1 3 1 4 0 7v-1h0c-1 2-1 3-1 5 0 1-1 2-2 3 0-1 1-3 1-4v-3c1-2 1-4 1-6 0-1 0-2-1-3 0-1-1-1-1-2s1-1 1-2 0-1-1-2v-1c2-1 3-2 4-2z" class="AK"></path><path d="M271 304h0l2-10 1 2c-1 4-3 11-1 14 1 2 2 3 3 3 0 1-1 2-1 3v9c0 3-1 6-1 9v2c-1-3 0-7 0-10v-10-3c-1-1-2-3-3-5v-4z" class="AP"></path><path d="M258 309l1 1v1c1 1 1 1 1 2s-1 1-1 2 1 1 1 2c1 1 1 2 1 3 0 2 0 4-1 6v3c-1 0-2 1-2 1l-1-1 1-6v-2l-1-4-1-1v-3h1c0-1 1-2 1-4z" class="Ad"></path><path d="M258 309l1 1v1 2h-2v1-1c0-1 1-2 1-4z" class="AI"></path><path d="M257 313v1c1 2 1 4 3 6l-1 2-1-1-1-4-1-1v-3h1z" class="Aa"></path><path d="M260 320v6 3c-1 0-2 1-2 1l-1-1 1-6v-2l1 1 1-2z" class="AD"></path><path d="M258 321l1 1v4c-1-1-1-2-1-3v-2z" class="AF"></path><path d="M239 314l1 1v1h3c1 1 2 1 4 1 3 0 7-1 10 0l1 4v2l-1 6 1 1s1-1 2-1c0 1-1 3-1 4l-1 11h-2v3 3c-1-1 0-3-1-3l-1-1h-4-10c0-1 0-4-1-5v-27z" class="AX"></path><path d="M240 316h3c1 1 2 1 4 1h-7v-1z" class="AO"></path><path d="M257 329l1 1s1-1 2-1c0 1-1 3-1 4l-1 11h-2l1-15z" class="AU"></path><path d="M239 341c1 1 1 4 1 5h10 4l1 1c1 0 0 2 1 3v1c1 2 2 4 2 6v1h0v3h1 1v-1-1l1 1 3-1h0c3-1 6-1 8-3v1 2h1l1-1h0v8 5c1 4 1 8 1 11v14 3 2h0c0 2-1 5 0 7 2 0 4 0 6 1 1 0 3 1 4 1 3 0 6 0 9 2v16 21 1h0-1-3c-2-1-5-1-7-1-2-1-4-1-5-1h-3-1v3h2c-1 0-1 0-2 1h-1v1l1 9-1 1v4c1 2 1 7 2 9 1 1 1 2 0 2h1c0 2 0 2 1 3 0 1 0 1-1 1l-3 2c-1 1-1 2-2 3v1 1h2v1l-1 1h0l-6 3c-1 1-2 1-4 1h0-1c-2 1-6 2-7 4-1 0-3 0-3 1-3 2-4 8-8 10 0 1-2 2-3 3l-2 1-1 2-3-3h-1c-3-2-5-5-8-7h0c4-7 7-16 10-24 4-18 4-37 4-56v-1-9h1v-32c2 0 4 0 6-1h0c1 0 2 0 3-1h-9c0-6 0-12-1-18h0v-23z" class="AO"></path><path d="M271 440v-1l2-1v2h-2z" class="AB"></path><path d="M262 403h3v1s0 1-1 1h-2v-1h-1v-1h1z" class="AI"></path><path d="M272 429h1l1 4h-1v2h0c0-2-1-4-1-6z" class="Ad"></path><path d="M260 410v-2c0-1 1-1 1-2 1 1 1 2 1 3v1c-1 1-1 2-1 4 0-1-1-2-1-3v-1h0z" class="AS"></path><path d="M272 429c-1-3-2-6-1-9l1 1c0 3 0 5 1 8h-1z" class="AY"></path><path d="M274 358h0v8c0 2 0 4-1 6l-1-13h1l1-1z" class="AG"></path><path d="M275 411l1 2c0 1 0 2-1 3 0 2-2 3-3 5l-1-1 3-3v-6h1z" class="Ad"></path><path d="M275 396v3 2h0c0 2-1 5 0 7v3h-1c0-2-1-3-1-4h0c0-2 1-4 1-5v-3h0l1-3z" class="AQ"></path><path d="M260 374h4 0c-1 0-2 1-4 0v2c1 1-1 4 1 5l1 1s0 1 1 1v1l-1-1-2 1-1-2v-4h0v-2h0v-2h1z" class="AJ"></path><path d="M260 384l2-1c-2 2-2 5-2 7 1 1 1 1 1 2s-1 2-1 3 1 2 1 3c1 0 2 1 2 2h0c-1-1-3-1-4-3 0 0 1-1 1-2-1 0-1 0-1-1v-1-7l1-2z" class="AB"></path><path d="M259 386v7 1c0 1 0 1 1 1 0 1-1 2-1 2 1 2 3 2 4 3l-1 3h-1v1c-1-1-1 0-3-1v-11c0-2 0-4 1-6z" class="AJ"></path><path d="M257 431v-2h1v3c1-1 2-1 3-1l1-1h1c-2 2-3 4-5 5l8 3h0-2c-2 0-5-1-7-2l1-1c-1-1-1-2-1-4z" class="AZ"></path><path d="M261 416l2 1h0v1 4h0v1c0 1 1 3 1 5 0 1 0 1-1 2h-1l-1 1c-1-2 0-3 0-5l1-2c0-3-1-5-1-8z" class="AB"></path><path d="M263 417h0v1h0-1l1-1z" class="AJ"></path><path d="M262 364s1 0 1-1c1-1 2-1 3-2-1 2-1 3-2 5h0c-1 1-1 1-1 2v1h2l1 1h0c0 1 0 1-1 2h-1v2h0-4l1-2h-1v-1c1-1 0-2 0-3l1-1 1-3z" class="AS"></path><path d="M262 364v1h1l1 1c-1 1-2 1-3 1l1-3z" class="AY"></path><path d="M261 372c1 0 1 0 2 1l1 1h-4l1-2z" class="AC"></path><path d="M274 366v5c1 4 1 8 1 11v14l-1 3h0c-1-8-1-16-1-24v-3c1-2 1-4 1-6z" class="AG"></path><path d="M274 366v5 9c-1-1 0-3-1-5v-3c1-2 1-4 1-6z" class="AP"></path><path d="M264 359c3-1 6-1 8-3v1c-2 1-4 4-6 4-1 1-2 1-3 2 0 1-1 1-1 1l-1 3-1 1c0 1 1 2 0 3h-1-1c0-2-1-4 0-6h-1l1-4h1 1v-1-1l1 1 3-1h0z" class="AI"></path><path d="M259 371v-2l1-1c0 1 1 2 0 3h-1zm1-12l1 1 3-1c-1 1-1 1-1 2h-2-1v-1-1z" class="AG"></path><path d="M258 361h1v1l1 1c0 1-1 2-2 2h-1l1-4z" class="Aa"></path><path d="M257 437v-1c2 1 5 2 7 2 1 1 3 0 4 1 1 0 2 0 3 1h0 2c0 2-1 3-1 4v1h0-1c-1 0-2 0-2 1h0c-1-1-3-1-4-1-2 0-3-1-5-2v-1l-1-1-1-1-1-1v-2z" class="AL"></path><path d="M271 440h2c0 2-1 3-1 4v1h0v-2c-1-1-1-2-1-3h0 0z" class="AS"></path><path d="M257 437l4 2-2 2-1-1-1-1v-2z" class="w"></path><path d="M264 441c2 0 5 0 6 2l1 1c-4 0-5-1-7-3z" class="AB"></path><path d="M261 439l2 1 1 1c2 2 3 3 7 3v1c-1 0-2 0-2 1h0c-1-1-3-1-4-1-2 0-3-1-5-2v-1l-1-1 2-2z" class="AG"></path><path d="M261 439l2 1v1c-1 1-2 1-3 1l-1-1 2-2z" class="Ai"></path><path d="M257 411c2-2 1-6 1-8 2 1 2 0 3 1h1v1l-1 1c0 1-1 1-1 2v2h0v1c0 1 1 2 1 3v2c0 3 1 5 1 8l-1 2c0 2-1 3 0 5-1 0-2 0-3 1v-3h-1v2c0-2 0-5 1-7l-1-13z" class="AG"></path><path d="M258 424c0-2-1-7 1-9 0 1-1 2 0 3v5 3l-1 1h0v-3z" class="AF"></path><path d="M258 403c2 1 2 0 3 1h1v1l-1 1c0 1-1 1-1 2v2c0 1-1 1-1 2-1-2-1-6-1-9z" class="AY"></path><path d="M259 423c2 1 2 2 2 3 0 2-1 3 0 5-1 0-2 0-3 1v-3h-1v2c0-2 0-5 1-7h0v3h0l1-1v-3z" class="AC"></path><path d="M260 410h0v1c0 1 1 2 1 3v2c0 3 1 5 1 8l-1 2c0-1 0-2-2-3v-5-6c0-1 1-1 1-2z" class="AK"></path><path d="M239 341c1 1 1 4 1 5h10 4l1 1c1 0 0 2 1 3v1c1 2 2 4 2 6v1h0v3l-1 4h1c-1 2 0 4 0 6h1 1v1h1l-1 2h-1v2h0v2h0v4l1 2-1 2c-1 2-1 4-1 6v11c0 2 1 6-1 8-1-4 0-9 0-12-1-2-1-5-1-6 0-2 1-3-1-5-1 1-3 1-5 1h-4c-2 0-4 0-5 1l-1-1v-4c2 0 4 0 6-1v-1c1 0 2 0 3-1h-9c0-6 0-12-1-18h0v-23z" class="AV"></path><path d="M255 387h-5v-1h0 5 1l-1 1z" class="Af"></path><path d="M257 365h1c-1 2 0 4 0 6h1 1v1h1l-1 2h-1v2h0v2h0v4l-1-1h-1c-1-4-1-9 0-14v-2z" class="AU"></path><path d="M257 365h1c-1 2 0 4 0 6v3h-1v-7-2z" class="AP"></path><path d="M259 371h1v1h1l-1 2h-1v2h0v2h0v4l-1-1c0-1 0-2-1-2 1-2 1-3 1-5v-3h1z" class="AQ"></path><path d="M260 372h1l-1 2h-1v-2h1z" class="AG"></path><path d="M257 381h1l1 1 1 2-1 2c-1 2-1 4-1 6v11c0 2 1 6-1 8-1-4 0-9 0-12v-12h-2 0l1-1c1 0 1-1 2-1-1-2-1-3-1-4z" class="AP"></path><path d="M275 408c2 0 4 0 6 1 1 0 3 1 4 1 3 0 6 0 9 2v16 21 1h0-1-3c-2-1-5-1-7-1-2-1-4-1-5-1h-3-1v3h2c-1 0-1 0-2 1h-1v1-1h-1-1l1-1h0v-3l-1-1-2 1c-2 0-2-1-3-2h3 0c0-1 1-1 2-1h1 0v-1c0-1 1-2 1-4v-2-2-1h0v-2h1l-1-4c-1-3-1-5-1-8 1-2 3-3 3-5 1-1 1-2 1-3l-1-2v-3z" class="AA"></path><path d="M273 435v-2h1l1 1v4l-2-2v-1h0z" class="AI"></path><path d="M273 435v-2h1l1 1-1 1h-1z" class="AQ"></path><path d="M273 436l2 2-1 2h1 0c1 1 1 1 1 2-1-1-1-1-1-2 0 1-1 3-1 4s1 1 2 2h1 1 2 1v1l9 1c0 1 1 1 1 1h3v1h0-1-3c-2-1-5-1-7-1-2-1-4-1-5-1h-3-1v3h2c-1 0-1 0-2 1h-1v1-1h-1-1l1-1h0v-3l-1-1-2 1c-2 0-2-1-3-2h3 0c0-1 1-1 2-1h1 0v-1c0-1 1-2 1-4v-2-2z" class="AX"></path><path d="M272 444h2v3l-1-1-1-1v-1z" class="AG"></path><path d="M271 447h2v3 2 1-1h-1-1l1-1h0v-3l-1-1z" class="AB"></path><path d="M273 436l2 2-1 2c0 1-1 3 0 4h-2c0-1 1-2 1-4v-2-2zm-4 10c0-1 1-1 2-1h1 0l1 1 1 1h-1-2l-2 1c-2 0-2-1-3-2h3 0z" class="AK"></path><path d="M269 446c0-1 1-1 2-1h1 0l1 1c-2 1-3 0-4 0z" class="AI"></path><defs><linearGradient id="o" x1="284.036" y1="416.014" x2="284.184" y2="408.847" xlink:href="#B"><stop offset="0" stop-color="#8b2824"></stop><stop offset="1" stop-color="#b4342d"></stop></linearGradient></defs><path fill="url(#o)" d="M275 408c2 0 4 0 6 1 1 0 3 1 4 1 3 0 6 0 9 2v16c-1-3-1-7-1-10-1-1-3-1-4-1-3 0-7 0-10-1-1 0-1 0-2-1h0c0-1-1-1-1-2l-1-2v-3z"></path><path d="M239 416h1v-32c2 0 4 0 6-1h0v1c-2 1-4 1-6 1v4l1 1c1-1 3-1 5-1h4c2 0 4 0 5-1 2 2 1 3 1 5 0 1 0 4 1 6 0 3-1 8 0 12l1 13c-1 2-1 5-1 7s0 3 1 4l-1 1v1 2 6h0v8c-1-2-1-3-1-5-1-1-6-2-7-2h-3-2c0-1-2 0-3-1h-1 0v-19h6 3l-4-1c-2 1-4 1-6 1v-1-9z" class="AA"></path><path d="M239 425h9-3c-2 1-4 1-6 1v-1z" class="AW"></path><path d="M241 390c1-1 3-1 5-1h4c2 0 4 0 5-1 2 2 1 3 1 5 0 1 0 4 1 6 0 3-1 8 0 12l1 13c-1 2-1 5-1 7s0 3 1 4l-1 1v1 2 6h0c0-1 0-4-1-5 0-1-2-1-3-1s-3-1-3-2h0c2 0 5 0 6-1v-1l-13-1v-1c2-1 5 0 7 0h4 1v-1c1 0 1-1 1-1v-1s1 0 0-1c0-2 0-4 1-6 0-1-1-2-1-2l1-1v-1c0-3-1-6-1-8h-4c-1 0-2-1-3-1h-9v-20h4-3z" class="AM"></path><path d="M239 426c2 0 4 0 6-1l4 1h-3-6v19h0 1c1 1 3 0 3 1h2 3c1 0 6 1 7 2 0 2 0 3 1 5v-8h0v-6l1 1 1 1 1 1v1c2 1 3 2 5 2 1 0 3 0 4 1h-3c1 1 1 2 3 2l2-1 1 1v3h0l-1 1h1 1v1l1 9-1 1v4c1 2 1 7 2 9 1 1 1 2 0 2h1c0 2 0 2 1 3 0 1 0 1-1 1l-3 2c-1 1-1 2-2 3v1 1h2v1l-1 1h0l-6 3c-1 1-2 1-4 1h0-1c-2 1-6 2-7 4-1 0-3 0-3 1-3 2-4 8-8 10 0 1-2 2-3 3l-2 1-1 2-3-3h-1c-3-2-5-5-8-7h0c4-7 7-16 10-24 4-18 4-37 4-56z" class="AE"></path><path d="M246 488l-3-1h-2v-1h1c1 0 3 0 4 1v1zm2 11h-2-1c-1 0-1-1-2-1h0c-2-1-4-2-6-2 3 0 5 1 7 1 1 0 1 0 1 1h1 2 0v1z" class="Af"></path><path d="M246 487c1 0 2 0 3 1h0 1l1 1 1-2v-1c1-1 1-1 1-2h0v-4c1-1 1-2 1-3v-3c0-1 0-5 1-6v-3c0-2 0-4 1-5v5l-1 8v5h0c0 1-1 2-1 3l-1 7c-1 2-1 1-2 2-2 0-4-1-5-2v-1z" class="AM"></path><path d="M251 496c1-1 3-1 4-1s3-1 4-1h4l-2 1c-2 1-6 2-7 4-1 0-3 0-3 1-3 2-4 8-8 10 0-1-1-1-1-1 2-2 3-4 5-6 0-1 1-2 2-3l-1-1v-1h2c1 0 1-1 1-2z" class="AD"></path><path d="M275 482h1l-3 2c-1 1-1 2-2 3v1 1h2v1l-1 1h0l-6 3c-1 1-2 1-4 1h0-1l2-1h-4c-1 0-3 1-4 1s-3 0-4 1l-1-2h2c0-1 0-2 1-3h0c2 1 3 0 5 0 4-1 7-3 10-5h1 2c1-2 2-3 4-4z" class="AO"></path><path d="M269 488h0c-2 3-4 5-6 6h-4c4-2 7-4 10-6z" class="AZ"></path><path d="M275 482h1l-3 2c-1 1-1 2-2 3v1 1h2v1l-1 1h0l-6 3c-1 1-2 1-4 1h0-1l2-1c2-1 4-3 6-6h0c0-1 1-2 2-2 1-2 2-3 4-4z" class="AQ"></path><path d="M235 482c0 1 0 3-1 4 0 1-2 5-2 6s2 2 2 3l-1 1h0l-3 3c1 1 1 3 2 3 1 2 2 3 4 4 1 2 2 3 4 5l-1 1c1 0 1 0 1 1l-2 1-1 2-3-3h-1c-3-2-5-5-8-7h0c4-7 7-16 10-24z" class="AR"></path><path d="M238 514l-1-3h1l1 1c1 0 1 0 1 1l-2 1z" class="AM"></path><path d="M260 477l3 1 1 2h0l1 3 1 2c1 0 1 0 2 1-3 2-6 4-10 5-2 0-3 1-5 0h0l-1-1h-1c1-1 1 0 2-2l1-7c0-1 1-2 1-3h1c1 0 2 1 3 1 0-1 1-1 1-2z" class="AU"></path><path d="M263 482c0-1 1-1 1-2l1 3v3c-1 0-1 0-2 1v-4-1z" class="AD"></path><path d="M263 483v4c0 1-2 2-3 2h-1l-1 1c-2 1-4-1-6 0h-1c1-1 1 0 2-2h1c2 0 4 0 5-1s1-1 2-1h2v-3h0z" class="Ad"></path><path d="M263 481v1 1h0v3h-2c-1 0-1 0-2 1s-3 1-5 1h-1l1-7h1c0 1 0 1 1 2s3 1 5 0c0 0 1 0 1-1l1-1z" class="AC"></path><path d="M260 477l3 1 1 2h0c0 1-1 1-1 2v-1l-1 1c0 1-1 1-1 1-2 1-4 1-5 0s-1-1-1-2h-1c0-1 1-2 1-3h1c1 0 2 1 3 1 0-1 1-1 1-2z" class="AJ"></path><path d="M260 477l3 1 1 2h0c0 1-1 1-1 2v-1c-1-1-2-2-4-2 0-1 1-1 1-2z" class="AP"></path><path d="M257 439l1 1 1 1 1 1v1c2 1 3 2 5 2 1 0 3 0 4 1h-3c1 1 1 2 3 2l2-1 1 1v3h0l-1 1h1 1v1l1 9-1 1v4c1 2 1 7 2 9 1 1 1 2 0 2h1c0 2 0 2 1 3 0 1 0 1-1 1h-1c-2 1-3 2-4 4h-2-1c-1-1-1-1-2-1l-1-2-1-3h0l-1-2-3-1c0 1-1 1-1 2-1 0-2-1-3-1h-1 0v-5l1-8v-5-2l1-5v-8h0v-6z" class="Ab"></path><path d="M272 452h1v1l1 9-1 1c-1-3-1-7-1-11z" class="AC"></path><path d="M258 452c1-1 1-3 2-4l2 1s1 0 2-1l1 2h-5c0 3 0 6-1 9l-1-3v-4z" class="AL"></path><path d="M269 486l1-1-1-1c-1 0-1 0-2 1-1-2-1-2 0-4 0 1 0 2 1 2h1v-1 1c2 0 4-1 6-1-2 1-3 2-4 4h-2z" class="AJ"></path><path d="M264 480h2c2-2 3-5 5-7h0c0 3-3 6-4 7v1c-1 2-1 2 0 4 1-1 1-1 2-1l1 1-1 1h-1c-1-1-1-1-2-1l-1-2-1-3h0z" class="AB"></path><path d="M258 456l1 3v5c0 3-2 10-1 12l1 1 1-1c1 0 1 0 1 1 0 0 1 1 2 1l-3-1c0 1-1 1-1 2-1 0-2-1-3-1h-1 0v-5l1-8v-5-2c1 0 1 1 1 2 1-1 1-3 1-4z" class="AS"></path><path d="M256 458c1 0 1 1 1 2v5h-1v-5-2z" class="AK"></path><path d="M256 465h1v6c0 1 0 2 1 2v4h2c0 1-1 1-1 2-1 0-2-1-3-1h-1 0v-5l1-8z" class="AB"></path><path d="M255 478v-2h1v1h2 2c0 1-1 1-1 2-1 0-2-1-3-1h-1 0z" class="AF"></path><path d="M257 439l1 1 1 1 1 1v1c2 1 3 2 5 2 1 0 3 0 4 1h-3c1 1 1 2 3 2l2-1 1 1v3h0l-1 1c-2 0-4-1-6-2l-1-2c-1 1-2 1-2 1l-2-1c-1 1-1 3-2 4v4c0 1 0 3-1 4 0-1 0-2-1-2l1-5v-8h0v-6z" class="AC"></path><path d="M258 440l1 1 1 1v1c-1 0-1 0-2-1v-2z" class="AQ"></path><path d="M258 448h0l2-1v-1l1-1c1 1 2 3 3 3-1 1-2 1-2 1l-2-1c-1 1-1 3-2 4v-4z" class="AB"></path><path d="M257 445h0l1 3h0v4 4c0 1 0 3-1 4 0-1 0-2-1-2l1-5v-8z" class="AI"></path><path d="M272 451c-1 0-2-1-2-1-3-1-5-2-7-4l1-1 2 1c1 1 1 2 3 2l2-1 1 1v3h0z" class="AJ"></path><defs><linearGradient id="p" x1="198.95" y1="349.26" x2="125.038" y2="401.525" xlink:href="#B"><stop offset="0" stop-color="#0c0c0d"></stop><stop offset="1" stop-color="#292725"></stop></linearGradient></defs><path fill="url(#p)" d="M147 307c2 0 3-1 5 0 1 0 1 0 2 1h0 0v1c0 1-1 1-1 2h0 3c1 1 2 1 2 2 1 0 2 0 3 1h0 1c1 0 1 0 2 1h4v1h-1 0c-2 0-5 0-6 1h-1v1 1c1 0 1 1 2 1v2h0c1 0 1 1 1 2l2 1h0l1 1h1c1-1 2-1 3-1l1 1 2 2c2 2 4 4 5 6 1 1 1 2 2 2l4 6-1 1-1 1 5 6h1c1 0 3 2 3 3h0 1 0c1 1 1 2 1 3 1 2 3 5 5 7l6 6c1 2 2 3 4 5 1 1 4 3 4 5l6 3c2 1 4 1 6 2v1h2l-12 5h0c-1-1-2-1-3-1-3 0-10 1-12 2l-2 1v1h-1l-1 1-1 1c-2 1-3 1-5 1v-1c-7-1-14 1-20 4l-2 1c1 2 3 7 2 9-2 3-4 6-4 10-1 2-1 3-1 5h0c0 1-1 2-1 2v2l-1 1h0c0-1 0-1-1-2h0c-2-4-3-8-5-12h-1l-1-1c0 1 0 1-1 2l-3-11-2-8h-1c0 1 0 3-1 4-1 6-3 12-6 17h-1c-2 3-4 5-7 7l-4 3c0-2 1-3 1-4l-1-1 2-3v-1h0v-4c1-3 0-6 1-9s0-7 0-10c0-1 0-2 1-3l-1-5v-2l-1-2c0-1 0-2-1-3h0c-1 1-1 2-1 3v-3c0 1 0 1-1 2h0c-1 1-1 3-1 5h-1-1c1 1 1 2 1 3l-1-1-9-14c0 1 1 1 1 1-1-2-2-3-1-5h0c-1-1-1-3-1-4v-4h0v-3h-1-1c-1 0-2-1-4-2l-1-7v-3h1v1l1 1h0v1l1 1c0-1 0-2 1-3 0-1 1-2 1-2 1-1 1-1 2-1h1 1 0c1-1 1-2 1-3v-4c1-3 3-6 6-8h-1l1-3h2c0-1 1-2 2-3s2-2 3-2v-2-1l-1 1-2-1v-1l-1-1 1-1-1-1h1v-1-1l4-2h2c1 0 2 0 2-1h3l1-1h0c1 0 2 0 2-1 1 0 2 0 3-1l2-1 1-1z"></path><path d="M132 372c1 0 1 1 1 1v1h-1v-2z" class="H"></path><path d="M182 386h1l-1 1-1 1-1-1 2-1z" class="E"></path><path d="M138 343l1-1 1 1c0 1-1 1-1 2-1-1-1-1-1-2z" class="B"></path><path d="M138 343c0 1 0 1 1 2v4h-1v-6z" class="T"></path><path d="M139 410l2-2v-1l1 1-2 3v-1h0-1 0z" class="H"></path><path d="M164 355l2-1c0 1 0 4-1 6h-1l-1-1c1-1 1-3 1-4z" class="d"></path><path d="M136 410v-2c0-1 0-1-1-2h0c1 0 1-1 1-1v-2c1-1 1-2 1-3v6c0 1 0 3-1 4z" class="G"></path><path d="M136 410c1-1 1-3 1-4 0 2 1 3 0 5s-2 6-3 7c0-1-1-1-1-1h0v-1c1-1 1-5 0-7h0v3c0-1 0-2-1-3h0c0-1 0-1 1-2 2 3 2 5 1 8l2-2v-1h0v-2z" class="I"></path><path d="M133 412v-3h0c1 2 1 6 0 7v1h0s1 0 1 1h0l-1 1c-1-1-1-1-2-1l-1 1h0v-4h1c1-1 1-2 1-3h1z" class="F"></path><path d="M130 419c1-1 1-2 1-3 0 0 1 0 1-1l1 1-1 1 2 1-1 1c-1-1-1-1-2-1l-1 1h0z" class="N"></path><path d="M177 334c2 2 5 6 6 9l-1 1c-1-1-1-2-2-2v-1l-3-3c0 2 2 3 2 4v1h0l-1-1v-1c-1-1-3-2-4-4v-1h1v-1c1 2 3 3 5 5 0-1-1-2-2-3v-1c-1 0-1-1-1-2h0z" class="U"></path><path d="M137 411h0l2-1h0 1 0v1c-1 3-3 6-5 9-1 1-3 3-5 3l3-4 1-1h0c1-1 2-5 3-7z" class="V"></path><path d="M170 325l1 1 2 2c2 2 4 4 5 6 1 1 1 2 2 2l4 6-1 1c-1-3-4-7-6-9-1-2-6-4-8-6 0-1-2-2-2-2 1-1 2-1 3-1z" class="v"></path><path d="M162 388l3-3 1 10-1-1c-1-1-3-2-6-2l1-1-2-1c1-1 1-2 2-2h2z" class="g"></path><path d="M160 388h2l-2 3-2-1c1-1 1-2 2-2z" class="S"></path><path d="M132 366l5-9v-1h0v-1c2-1 2-4 4-4l1 1v-1 2c-2 3-4 4-5 7-2 3-3 7-5 11l-1-1c0-1 1-2 1-4z" class="L"></path><path d="M135 420c1-1 2-1 3-2h1c-2 3-4 5-7 7l-4 3c0-2 1-3 1-4l-1-1 2-3v-1l1-1c1 0 1 0 2 1l-3 4c2 0 4-2 5-3z" class="b"></path><path d="M130 420l1 1c-1 1-1 2-1 3h0c1 0 1 0 2 1l-4 3c0-2 1-3 1-4l-1-1 2-3z" class="X"></path><path d="M157 329l2-1v1h1l1 1h0v2h-3c-1 1-1 1-3 1v1l2 2c-1 0-2-1-4-1v-1h0c0-1-1-2-1-3h-2v-1h1v1c2-1 3-2 5-2h1z" class="e"></path><path d="M159 329h1l1 1-1 1h-1v-1-1z" class="q"></path><path d="M156 329h2v1c-1 1-4 1-5 2 0 1 1 2 2 2l2 2c-1 0-2-1-4-1v-1h0c0-1-1-2-1-3h-2v-1h1v1c2-1 3-2 5-2z" class="W"></path><path d="M132 371v1c-2 5 0 12 1 17v6l-1 1h0 0v-3l-1-5v-2l-1-2c0-1 0-2-1-3h0c-1 1-1 2-1 3v-3c0 1 0 1-1 2h0c1-3 2-7 4-10l1-2z" class="Z"></path><path d="M142 408l4-11h0v4c-1 6-3 12-6 17h-1-1c-1 1-2 1-3 2 2-3 4-6 5-9l2-3z" class="G"></path><path d="M132 393v3h0 0l1-1v3l1-1v1 5h0v-3-1s0-1 1-2h0l1 1c-1 1-1 3-1 5 0 1-1 3-2 4s-1 1-1 2h0c1 1 1 2 1 3h-1c0 1 0 2-1 3h-1c1-3 0-6 1-9s0-7 0-10c0-1 0-2 1-3z" class="L"></path><path d="M133 395v3 3c-1-1-1-4-1-5h0 0l1-1z" class="E"></path><defs><linearGradient id="q" x1="191.872" y1="362.861" x2="203.482" y2="363.803" xlink:href="#B"><stop offset="0" stop-color="#57534e"></stop><stop offset="1" stop-color="#8a7a71"></stop></linearGradient></defs><path fill="url(#q)" d="M191 359c1-1 1-2 1-2v-1h1c1 2 3 5 5 7l6 6c1 2 2 3 4 5 1 1 4 3 4 5-3-2-6-4-8-7-5-4-9-9-13-13z"></path><path d="M150 331h2c0 1 1 2 1 3h0v1 1h-2v1l4 5c-2-1-4-3-6-4h-2c-1-1-1-1-2-1l-1-1-2-1c-1 0-1-1 0-2h3c1-1 3-1 5-2h0z" class="e"></path><path d="M142 333h3v1h3-4v1 1l-2-1c-1 0-1-1 0-2z" class="r"></path><path d="M150 331h2c0 1 1 2 1 3h0v1 1l-1-1c-1-1-2-1-4-1h-3v-1c1-1 3-1 5-2h0z" class="z"></path><path d="M179 343v-1c0-1-2-2-2-4l3 3v1c1 0 1 1 2 2l5 6h1c1 0 3 2 3 3h0 1 0c1 1 1 2 1 3h-1v1s0 1-1 2c-1-1-2-4-3-5-4-4-8-7-11-11v-1l3 3c0-1-1-1-1-2h0z" class="G"></path><path d="M188 350c1 0 3 2 3 3v1h-1l-3-4h1z" class="H"></path><path d="M155 353c1-1 4-2 5-4h0c1-1 3-2 5-2h2l-1 7-2 1c0 1 0 3-1 4l-1-2h-1l-1 1-4-1c0-1 0-2 1-2v-1l-2-1z" class="g"></path><path d="M163 353l1-1v3c0 1 0 3-1 4l-1-2h-1l-1 1-4-1c0-1 0-2 1-2v-1c0-1 4 0 5 0l1-1z" class="q"></path><path d="M157 355c1 0 3 1 4 2l-1 1-4-1c0-1 0-2 1-2z" class="Z"></path><path d="M163 353l1-1v3c0 1 0 3-1 4l-1-2v-2l1-2z" class="W"></path><path d="M155 353c-2 0-3-1-3-2-1-1-1-1-1-2h1 1c1 0 2-1 3-3l1-1h1c0-1 1-1 2-1 0-1 1 0 1 0 1-1 1-1 2-1 0-1 0-1 1-1h1v-1c1 0 2-1 2-1l1 2c0 1 0 3-1 4v1h-2c-2 0-4 1-5 2h0c-1 2-4 3-5 4z" class="B"></path><path d="M132 352l1-2c-1 1 0 3-1 4h0c1 1 1 2 1 3v2l-1 1c-1 1-1 2-1 4h0c0 1 0 2 1 3v-1h0c0 2-1 3-1 4l-1-1h0-1c-1 0-1 0-1 1l-1 1-1-1c1-2 1-3 1-4-1-1-1-2-1-3v-4h1v-1c0-2 1-3 1-4l1-1v-2h0l2 1h1z" class="I"></path><path d="M132 354c1 1 1 2 1 3l-2-1c0-1 1-1 1-2z" class="F"></path><path d="M129 357l1-1c1 1 1 1 1 2h-1c-1 0-1 0-1-1z" class="p"></path><path d="M129 351h0l2 1h1c-1 1-2 2-2 3v1l-1 1v-4-2z" class="F"></path><path d="M129 369c0-1 0-2-1-3v-1c0-1-1-2-2-4h1c1 1 1 1 1 2 1-1 2-1 2-2l1-1v1 3h0c0 1 0 2 1 3v-1h0c0 2-1 3-1 4l-1-1h0-1z" class="C"></path><path d="M131 364c0 1 0 2 1 3v-1h0c0 2-1 3-1 4l-1-1 1-5z" class="E"></path><path d="M124 357h1l1 1v1 4c0 1 0 2 1 3 0 1 0 2-1 4l1 1 1-1c0-1 0-1 1-1h1 0l1 1 1 1h0l-1 2c-2 3-3 7-4 10-1 1-1 3-1 5h-1v-3h-1v-2c0-2 1-3 1-5v-6c0-3-1-5-2-8 0-1 0-2-1-3-1 1-1 0-2 0l4-4z" class="B"></path><path d="M124 357h1l1 1v1 4l-1-2v-2h0-1c-1 0-1 1-2 2s-1 0-2 0l4-4zm5 12h1 0l1 1 1 1h0l-1 2c-2 3-3 7-4 10-1 1-1 3-1 5h-1v-3-4-1-2c1-1 2-4 2-5v-2l1-1c0-1 0-1 1-1z" class="S"></path><path d="M129 369h1 0l1 1 1 1h0l-1 2c-1 0-1-1-2-1v1h-1v-3c0-1 0-1 1-1z" class="F"></path><path d="M130 369h0l1 1 1 1h0l-1 2c-1 0-1-1-2-1l1-3z" class="H"></path><path d="M181 389h2 5c1 1 1 1 1 3v4-1c-7-1-14 1-20 4-1-1-1-3-1-4 1-1 3-2 4-3 2-1 5-2 7-2l2-1z" class="G"></path><path d="M181 389h2 5c1 1 1 1 1 3v4-1c-1 0-1-1-1-2s0-1-1-1h-7-1c-1 0-3 1-4 1l-1-1h0-2c2-1 5-2 7-2l2-1z" class="K"></path><path d="M179 390l2-1v2h-1c-1 0-1-1-1-1z" class="D"></path><path d="M172 392c2-1 5-2 7-2 0 0 0 1 1 1l-6 1h0-2z" class="I"></path><path d="M183 389h5c1 1 1 1 1 3v4-1c-1 0-1-1-1-2s0-1-1-1v-1c-2 0-3 0-4-1v-1z" class="C"></path><path d="M188 389c2-1 6-1 8-1h2c3 0 6 0 9-1 6-1 11-2 17-2h2l-12 5h0c-1-1-2-1-3-1-3 0-10 1-12 2l-2 1v1h-1l-1 1-1 1c-2 1-3 1-5 1v-4c0-2 0-2-1-3z" class="U"></path><path d="M195 391l1-2h1c0 1 0 1-1 2v1c-1 0-1-1-1-1h0z" class="Q"></path><path d="M189 392v-2h1c1-1 3-1 4 0l1 1h0c-2 1-2 2-4 2-1 0-1 0-2-1z" class="Ac"></path><path d="M189 392c1 1 1 1 2 1 2 0 2-1 4-2 0 0 0 1 1 1l-2 1v1h1l-1 1c-2 1-3 1-5 1v-4h0z" class="P"></path><path d="M127 345c1-1 2-2 3-2h1 1 0l2 2c-1 1-2 3-1 5l-1 2h-1l-2-1h0v2l-1 1c0 1-1 2-1 4v1h-1v-1l-1-1h-1l-1-1-2 3c-1-1-1-2-1-3l-1-1v-3-2c1 0 1-1 1-2 1 0 1-1 2-1v-1c1 0 2-1 3-2 1 0 1 1 2 1z" class="Z"></path><path d="M129 349v2 2l-1 1c0-1 0-1-1-2 1-1 1-2 2-3z" class="H"></path><path d="M131 345s1 1 1 2c-1 1-1 3-1 5l-2-1h0v-2c1-1 1-3 2-4z" class="V"></path><path d="M122 347v2l-1 1-1 1v1c1 1 1 2 1 3h-1v1l-1-1v-3-2c1 0 1-1 1-2 1 0 1-1 2-1z" class="K"></path><path d="M132 343h0l2 2c-1 1-2 3-1 5l-1 2h-1c0-2 0-4 1-5 0-1-1-2-1-2v-2h1z" class="C"></path><path d="M127 352c1 1 1 1 1 2s-1 2-1 4v1h-1v-1l-1-1h-1l-1-1 2-2 2-2z" class="p"></path><path d="M123 356l2-2 1 1v3l-1-1h-1l-1-1z" class="f"></path><path d="M127 352c1 1 1 1 1 2s-1 2-1 4v-3h-1l-1-1 2-2z" class="Q"></path><path d="M130 324v2 1h0 1v1 1l4 1-1 1c1 1 2 1 2 2h5 1 0c-1 1-1 2 0 2l2 1 1 1c1 0 1 0 2 1h2c-1 0-2 0-2 1-1 0-3 1-4 1h-3l-2-1c-1 2-3 3-3 5h-1v1l-2-2h0-1-1c-1 0-2 1-3 2-1 0-1-1-2-1-1 1-2 2-3 2h0c-1 0-2 0-2-1-1 1-2 2-2 3l-1 1-1-2c1-1 1-2 1-3v-4c1-3 3-6 6-8h-1l1-3h2c0-1 1-2 2-3s2-2 3-2z" class="k"></path><path d="M131 338l2-2h0v1l-1 2-1-1z" class="P"></path><path d="M133 337c0 1 1 1 1 2-1 0-1 0-1 1s0 1-1 2v1h0c-1-2-1-3 0-4l1-2z" class="Q"></path><path d="M140 336l1 1c0 1 2 1 1 2v1h1-3v-4z" class="C"></path><path d="M121 339l1-1 1 1h1v-1l1 1-1 1c-1 0-2 1-2 2-1-1-1-2-1-3z" class="O"></path><path d="M130 335h1c-1 1-3 3-5 3h0l-1 1-1-1c1-2 4-3 6-3z" class="G"></path><path d="M137 337h2c0 1-1 2-1 2-1 2-3 3-3 5h-1v-1-1c0-2 2-3 3-5z" class="J"></path><path d="M137 337h0-2v-2h3c1 0 1 1 2 1h0v4l-2-1s1-1 1-2h-2z" class="f"></path><path d="M131 338l1 1c-1 1-1 2 0 4h-1-1c-1 0-2 1-3 2-1-2 0-1 1-3l-1-1v-1l4-2z" class="B"></path><path d="M130 324v2 1h0 1v1 1l4 1-1 1c1 1 2 1 2 2l-5 2h-1c-2 0-5 1-6 3v1h-1l-1-1-1 1c0 1 0 2 1 3-1 1-2 2-2 3-1 1-2 2-2 3l-1 1-1-2c1-1 1-2 1-3v-4c1-3 3-6 6-8h-1l1-3h2c0-1 1-2 2-3s2-2 3-2z" class="Z"></path><path d="M123 329h2l1 1-3 2h-1l1-3z" class="s"></path><path d="M134 331c1 1 2 1 2 2l-5 2h-1c1-2 3-2 4-4z" class="M"></path><path d="M117 344c1 0 1 1 1 1v1h0 1c0-3 1-5 2-7 0 1 0 2 1 3-1 1-2 2-2 3-1 1-2 2-2 3l-1 1-1-2c1-1 1-2 1-3z" class="C"></path><path d="M130 324v2 1h0 1v1 1c-1 0-2-1-3-1-1 1-2 1-2 2l-1-1c0-1 1-2 2-3s2-2 3-2z" class="X"></path><path d="M117 349l1-1c0-1 1-2 2-3 0 1 1 1 2 1h0v1c-1 0-1 1-2 1 0 1 0 2-1 2v2 3l1 1c0 1 0 2 1 3l2-3 1 1-4 4c1 0 1 1 2 0 1 1 1 2 1 3 1 3 2 5 2 8v6c0 2-1 3-1 5v2h1v3h-1c1 1 1 2 1 3l-1-1-9-14c0 1 1 1 1 1-1-2-2-3-1-5h0c-1-1-1-3-1-4v-4h0v-3h-1-1c-1 0-2-1-4-2l-1-7v-3h1v1l1 1h0v1l1 1c0-1 0-2 1-3 0-1 1-2 1-2 1-1 1-1 2-1h1 1 0l1 2z" class="S"></path><path d="M111 350l1 1c0 2 1 3 1 4l-1-2-1 1 1 1v-1c0 1 0 2-1 3-1-1-1-2-1-4 0-1 0-2 1-3z" class="C"></path><path d="M107 352v-3h1v1l1 1h0v1l1 1c0 2 0 3 1 4s1 3 2 4h-1c-1 0-2-1-4-2l-1-7z" class="y"></path><path d="M123 356l1 1-4 4-1 5c-1 1-1 2-1 3h-2l-1-2c0-2 0-4 1-6 1 0 2-1 3-1s1-1 2-1l2-3zm-6-7l1-1c0-1 1-2 2-3 0 1 1 1 2 1h0v1c-1 0-1 1-2 1 0 1 0 2-1 2v2 3 3h0c-1 0-1-1-1-2h-2v1c0 1 0 1-1 1-1-1-1-2-2-3 0-1-1-2-1-4l-1-1c0-1 1-2 1-2 1-1 1-1 2-1h1 1 0l1 2z" class="P"></path><path d="M116 356c-1 0-1-1-1-2h0c1 0 1 0 1 1h0 1l1 1h-2z" class="C"></path><path d="M116 347h0l1 2v2c-1-1-1-1-2-1h0-1l1-2v-1h1z" class="b"></path><path d="M120 361c1 0 1 1 2 0 1 1 1 2 1 3 1 3 2 5 2 8v6c0 2-1 3-1 5v2h1v3h-1c1 1 1 2 1 3l-1-1-9-14c0 1 1 1 1 1-1-2-2-3-1-5h0v-1-8c0 2-1 4 0 7l1 1 1 2h1v-3c1-1 1-3 1-4l1-5z" class="E"></path><path d="M118 373c1 1 1 1 1 2h-1s-1-1-1-2h1z" class="H"></path><path d="M115 372c2 6 6 12 9 16 1 1 1 2 1 3l-1-1-9-14c0 1 1 1 1 1-1-2-2-3-1-5z" class="I"></path><path d="M147 307c2 0 3-1 5 0 1 0 1 0 2 1h0 0v1c0 1-1 1-1 2h0 3c1 1 2 1 2 2 1 0 2 0 3 1h0 1c1 0 1 0 2 1h4v1h-1 0c-2 0-5 0-6 1h-1v1 1c1 0 1 1 2 1v2h0c1 0 1 1 1 2l2 1h0l1 1h1s2 1 2 2l-2 1c-2 1-3 2-6 3v-2h0l-1-1h-1v-1l-2 1h-1c-2 0-3 1-5 2v-1h-1v1h0c-2 1-4 1-5 2h-3 0-1-5c0-1-1-1-2-2l1-1-4-1v-1-1h-1 0v-1-2-2-1l-1 1-2-1v-1l-1-1 1-1-1-1h1v-1-1l4-2h2c1 0 2 0 2-1h3l1-1h0c1 0 2 0 2-1 1 0 2 0 3-1l2-1 1-1z" class="E"></path><path d="M149 325l1-2h1v-1c0 1-1 1-1 0h0c0-1 0-1 1-1v-1l2 1c0 1 0 2-1 3-1 0-2 0-3 1z" class="B"></path><path d="M150 313c1-1 2-2 3-2h0 3c1 1 2 1 2 2-3 0-5 0-8 1v-1z" class="g"></path><path d="M149 325c1-1 2-1 3-1l-1 2h-1c-1 0-1 1-2 2l-2-1v1l1 1-1 1h-1 0v-1h-2 1v-1c-1 0-1 0-1-1h-1 1c0-1 0-1 1-1 0-1 0-1 1-1l1 2h1l2-2z" class="M"></path><path d="M136 327h6 1c0 1 0 1 1 1v1h-1c0 1-1 1-1 1l-5 1c0-1-1-1-2-1l-4-1v-1h2c1 0 1-1 3-1z" class="O"></path><path d="M131 328h2c2 1 2 0 4 1h3s1 0 2 1l-5 1c0-1-1-1-2-1l-4-1v-1z" class="c"></path><path d="M154 315h1c2 1 3 2 5 3v1c1 0 1 1 2 1v2h0c1 0 1 1 1 2-1-1-3-1-3-2l-2-2c0 1-1 2-1 3v-1c-1-1-2-2-3-2v-2-1-2z" class="o"></path><path d="M158 318l1 1c1 1 1 0 1 2v1l-2-2h-1l1-2z" class="p"></path><path d="M154 318v-1-2c1 2 3 2 4 3l-1 2h-1l-2-2z" class="K"></path><path d="M154 318l2 2h1 1c0 1-1 2-1 3v-1c-1-1-2-2-3-2v-2z" class="J"></path><path d="M145 330h0 1l1-1-1-1v-1l2 1c-1 1-1 1-1 2h1l1 1 1-1v1c-2 1-4 1-5 2h-3 0-1-5c0-1-1-1-2-2l1-1c1 0 2 0 2 1l5-1s1 0 1-1h2v1z" class="e"></path><path d="M143 329h2v1c-1 0-2 1-3 3h-1c0-1-1-1-1-2h-2-1l5-1s1 0 1-1z" class="f"></path><path d="M135 330c1 0 2 0 2 1h1 2c0 1 1 1 1 2h-5c0-1-1-1-2-2l1-1z" class="B"></path><path d="M154 320c1 0 2 1 3 2v1h-3v2c1 0 1-1 2-1l1 1h0v1h-1c0 1 0 1 1 2v1h-1c-2 0-3 1-5 2v-1h-1v1h0v-1l-1 1-1-1h-1c0-1 0-1 1-2s1-2 2-2h1l1-2c1-1 1-2 1-3l1-1z" class="h"></path><path d="M148 330c1-1 1-1 2-1v1l-1 1-1-1z" class="k"></path><path d="M157 325h0v1h-1c0 1 0 1 1 2v1h-1c-2 0-3 1-5 2 0-2 2-4 4-5 1-1 1 0 2-1z" class="P"></path><path d="M147 315h1c0 1 0 1-1 2 0 1 0 1 1 1v1c0 1 0 1-1 2 0 1 0 1-1 1s-1 1-3 2h0c-2 1-3 1-4 1s-1 0-1-1l-1 1h-1-2l6-4c1 0 3-3 4-3l1-2h2v-1z" class="M"></path><path d="M147 315h1c0 1 0 1-1 2 0 1 0 1 1 1v1l-1-1v1c-2 0-2-1-3-1l1-2h2v-1z" class="K"></path><path d="M158 320l2 2c0 1 2 1 3 2l2 1h0l1 1h1s2 1 2 2l-2 1c-2 1-3 2-6 3v-2h0l-1-1h-1v-1l-2 1v-1c-1-1-1-1-1-2h1v-1h0l-1-1c-1 0-1 1-2 1v-2h3c0-1 1-2 1-3z" class="E"></path><path d="M158 320l2 2c0 1 2 1 3 2l2 1-1 1c-2-1-4-1-5-2h-2v1h0l-1-1c-1 0-1 1-2 1v-2h3c0-1 1-2 1-3z" class="L"></path><path d="M166 326h1s2 1 2 2l-2 1c-2 1-3 2-6 3v-2h0l-1-1h-1v-1h4l3-1h0v-1z" class="Q"></path><path d="M159 328h4 2v1l-4 1h0l-1-1h-1v-1z" class="h"></path><path d="M166 326h1s2 1 2 2l-2 1h-2v-1h-2l3-1h0v-1z" class="D"></path><path d="M147 307c2 0 3-1 5 0 1 0 1 0 2 1h0 0v1c0 1-1 1-1 2-1 0-2 1-3 2v1c-1 0-2 1-2 1h-1v1h-2l-1 2c-1 0-3 3-4 3l-6 4h2l-1 1 1 1c-2 0-2 1-3 1h-2v-1h-1 0v-1-2-2-1l-1 1-2-1v-1l-1-1 1-1-1-1h1v-1-1l4-2h2c1 0 2 0 2-1h3l1-1h0c1 0 2 0 2-1 1 0 2 0 3-1l2-1 1-1z" class="d"></path><path d="M135 320v-1-2c1 0 1 0 1-1 1 1 1 1 2 1l-3 3h0z" class="F"></path><path d="M135 312h3v2h-1l-1 1c-1 0-1 1-2 0h0l-1 1h0c-1-1-1-1-1-2l1-1c1 0 2 0 2-1z" class="N"></path><path d="M133 316h2v1l-3 3c-1 0-1 0-1 1v1h-1v-1l-1 1-2-1v-1c1-1 2-1 3-2s2-1 3-2h0z" class="q"></path><path d="M130 321l-1-1c1 0 1 0 2-1v2 1h-1v-1z" class="n"></path><path d="M131 313h2l-1 1c0 1 0 1 1 2-1 1-2 1-3 2s-2 1-3 2l-1-1 1-1-1-1h1v-1-1l4-2z" class="D"></path><path d="M127 317l2-1v2h-2l-1-1h1z" class="G"></path><path d="M138 317c1 0 2-1 3-1 2 1 2 0 4 0l-1 2c-1 0-3 3-4 3v-1l-2 1-1-1-1 1h-1v-1h0l3-3z" class="Y"></path><path d="M138 321l2-1v1l-6 4h2l-1 1 1 1c-2 0-2 1-3 1h-2v-1h-1 0v-1-2-2h1c1 0 1 1 1 1h1 0c1-1 2-1 4-2h1z" class="w"></path><path d="M130 322h1c1 0 1 1 1 1-1 1-1 2-2 3v-2-2z" class="W"></path><path d="M134 325h2l-1 1 1 1c-2 0-2 1-3 1h-2v-1l3-2z" class="U"></path><path d="M147 307c2 0 3-1 5 0 1 0 1 0 2 1h0 0v1c0 1-1 1-1 2-1 0-2 1-3 2v1c-1 0-2 1-2 1h-1v1h-2c-2 0-2 1-4 0-1 0-2 1-3 1s-1 0-2-1c1 0 2-1 3-2 1 0 1 0 2-1-1 0-1 0-1-1l-1-1h0c1 0 2 0 2-1 1 0 2 0 3-1l2-1 1-1z" class="J"></path><path d="M139 311h0c1 0 2 0 2-1 0 1 0 2 1 3h-1c-1 0-1 0-1-1l-1-1z" class="X"></path><path d="M149 312v1h1v1c-1 0-2 1-2 1h-1v1h-2c-2 0-2 1-4 0 3-2 5-3 8-4z" class="AT"></path><path d="M147 316l-1-1v-1h1v1 1z" class="z"></path><path d="M147 307c2 0 3-1 5 0 1 0 1 0 2 1h0 0v1c0 1-1 1-1 2-1 0-2 1-3 2h-1v-1l2-2h-1c-2 0-3 1-4 2-1 0-1-1-2-2v-1l2-1 1-1z" class="O"></path><path d="M154 308v1c0 1-1 1-1 2-1 0-2 1-3 2h-1v-1l2-2c1-1 2-1 3-2z" class="z"></path><defs><linearGradient id="r" x1="164.895" y1="394.272" x2="148.361" y2="393.022" xlink:href="#B"><stop offset="0" stop-color="#b3a49a"></stop><stop offset="1" stop-color="#d2c8c4"></stop></linearGradient></defs><path fill="url(#r)" d="M148 353c0-1-1-1 0-1 0-1 0-2 1-3 0 1-1 4 1 4h1l2 2h1l1 1 1 1 4 1 1-1h1l1 2 1 1h0v1c1 2 1 4 1 7l-1 14v1c-1 1-2 3-4 5-1 0-1 1-2 2l2 1-1 1c3 0 5 1 6 2l1 1 1 5c1 2 3 7 2 9-2 3-4 6-4 10-1 2-1 3-1 5h0c0 1-1 2-1 2v2l-1 1h0c0-1 0-1-1-2h0c-2-4-3-8-5-12h-1l-1-1c0 1 0 1-1 2l-3-11-2-8-1-13c-1-11-1-21 1-31z"></path><path d="M162 421l2 3h0c0 1-1 2-1 2l-2-5h1z" class="Z"></path><path d="M160 371c1 0 1 1 0 1-1 2-1 5-1 7h-1c-1-1 1-6 1-8h1z" class="g"></path><path d="M154 409c1 2 2 4 2 6h-1l-1-1-1-4c0-1 1-1 1-1z" class="B"></path><path d="M151 403l1-1 2 7s-1 0-1 1l-2-7z" class="Z"></path><path d="M161 357h1l1 2 1 1h0v1l-1 2h-1v-1-1c0-1-1-2-2-3l1-1zm-11 39v-3l1-1 1 7v3l-1 1h0l-1-7z" class="E"></path><path d="M151 403h0l2 7 1 4c0 1 0 1-1 2l-3-11 1-1v-1z" class="t"></path><path d="M156 404l1-1 5 18h-1l-5-17z" class="E"></path><path d="M158 390l2 1-1 1c-1 2-2 4-2 7v1 3l-1 1v-1-3c-1-3 0-5 1-8l1-2z" class="G"></path><path d="M156 400v-3h1v2 1 3l-1 1v-1-3z" class="M"></path><path d="M156 403v-3h1v3l-1 1v-1z" class="H"></path><path d="M148 353c0-1-1-1 0-1 0-1 0-2 1-3 0 1-1 4 1 4h1l2 2h1l1 1h-4c-2 12-1 24 0 36l-1 1v3c-1-5-1-11-2-16h0v-7c0-4 2-18 0-20z" class="K"></path><path d="M148 353c2 2 0 16 0 20v7h0c1 5 1 11 2 16l1 7v1l-1 1-2-8-1-13c-1-11-1-21 1-31z" class="n"></path><path d="M148 380c1 5 1 11 2 16l1 7v1l-1 1-2-8-1-13h0c1 2 1 4 1 6v4-4-3-7z" class="v"></path><path d="M164 361c1 2 1 4 1 7l-1 14v1c-1 1-2 3-4 5-1 0-1 1-2 2l-1 2-1-1v-4c0-2 1-5 2-7l1-1c0-2 0-5 1-7 1 0 1-1 0-1h-1c1-1 1-1 1-2 1-2 1-5 2-7v1h1l1-2z" class="W"></path><path d="M158 380c1 1 0 3 0 5 0 1 0 1-1 2h-1c0-2 1-5 2-7z" class="z"></path><path d="M164 361c1 2 1 4 1 7l-1 14c0-1 0-3-1-4 0-1 1-1 1-1 0-1-1-1-1-1v-3-9c-1 2-1 3-2 4 0 1 0 2-1 3h-1c1-1 1-1 1-2 1-2 1-5 2-7v1h1l1-2z" class="u"></path><path d="M159 392c3 0 5 1 6 2l1 1 1 5c1 2 3 7 2 9-2 3-4 6-4 10-1 2-1 3-1 5l-2-3-5-18v-3-1c0-3 1-5 2-7z" class="W"></path><path d="M182 180c2 1 3-1 5-2h0 2c-1 2-3 3-5 5l1 4c2 0 3 1 4 1l-1 2s1 1 2 1l1 1c0 1 1 2 2 2l1 1c-1 0-1 1-2 2h1v1h-1c-1 0-1-1-2-1h0l-1-1h-3l-1 1 1 2v2 3 1 1h-2v3h2c0 1 0 3-1 4v5c-1 1-1 2-1 2l-1 4h0c4 1 9 0 14 0 3 0 6 1 9 0h1 7 16 5l2 2s0 1 1 1l1 2-1 1c2 2 4 4 5 6v1l1 1c-6 0-13-1-19 1h-3l-1 1c-2 0-3 1-5 1-1 2-2 3-4 5l-1 2c-1 5-4 10-5 16l-1 1v1l1 1h0c-1 2-1 3-1 5h0-1l-1 4v1c-3-1-6 0-8 0h-4-5c1 2 2 5 2 7l1 4-1 1c0 2 0 2-2 4l-2 3-1 3-1 1-2 2h-2l-3 2h-2c-2 1-3 2-5 2h0-2c-1 1-2 1-3 1-2 1-3 1-4 2l-1 1c-1 0-1-1-2-2h-2 0c-1-1-1-1-2-1-2-1-3 0-5 0l-1 1-2 1c-1 1-2 1-3 1 0 1-1 1-2 1h0l-1 1h-3c0 1-1 1-2 1h-2l1-2h0c0-1-1-2-2-3v-1h-1v-2c0-1 0-1 1-2h-3c0-5-2-11-4-15 0-1 0-2 1-2l1-3h2v1h1 1c-1-1-1-2-2-3h-1c0-1 0-2 1-4v-2c0-1 0-1 1-2h0 0v-2l1-2h1v-1s1-1 1-2 1-2 1-3 1-2 1-3c1-1 1-3 1-3v-3c1-1 1-1 2-1l1-2v1h3l1-1c1 0 1 0 2 1v-4-3-1-2c0-1 1-3 1-4l2-7 1-2v-1l-1-2h0c0-2-1-5 0-7v-1l1-3s-1-1 0-2l1-1c0-2 1-4 2-5l2-2 2-2 1-1h0v-1h2 2 0l3-3h1l1-2v-2l1-1v-1l1 1c1-1 3-3 3-5l3-3-1-1s5-2 7-4v1c1 0 2-1 4-1z" class="Z"></path><path d="M165 266v-1c0-1 1-1 1-2v3h-1 0z" class="M"></path><path d="M163 293h2l1 1v1c-1 0-1 0-2-1l-1-1z" class="t"></path><path d="M157 261h0l1 1c0 1 0 2-1 3l-1-1c1 0 1-1 1-2v-1z" class="K"></path><path d="M148 292h1c0 1-1 2-2 3v-1c0-1 1-2 1-2z" class="H"></path><path d="M164 290l1 3h-2v-2l1-1z" class="u"></path><path d="M162 289v-1c1 0 1 1 2 2l-1 1h-2c0-1 0-1 1-2z" class="W"></path><path d="M180 215h0l1 1c-1 2-2 3-3 4l-1-1c0-1 1-1 2-1 0-1 1-2 1-3z" class="K"></path><path d="M150 268c1 0 2 0 3-1 0 1 0 3-1 4l-1-1-1-2z" class="L"></path><path d="M170 268l1 1-6 2h0l1-1h0-1c1-1 2-1 3-2h2z" class="G"></path><path d="M160 238c3 0 4 1 7 1-3 1-5 1-7 1v-2z" class="H"></path><path d="M183 215l1-1 1-1v5c-1 1-1 2-1 2l-1-5z" class="W"></path><path d="M168 254h1c-1 2-3 4-4 5h-1c0-2 0-2 2-2 0-1 1-2 2-3z" class="T"></path><path d="M150 236c0 2 1 3 2 4l-1 1v-1h-1l-1-2 1-2z" class="D"></path><path d="M170 268c2-1 6-2 9-2-3 2-6 2-8 3l-1-1z" class="O"></path><path d="M164 196h3c-2 1-3 3-4 5h0v-3l1-2z" class="o"></path><path d="M162 198h1v3h0c0 1-1 4-2 5l-1-1c0-1 0-1 1-2s1-2 1-5z" class="F"></path><path d="M162 198c0 3 0 4-1 5s-1 1-1 2h0 0l-1-1c0-1 1-2 0-3l3-3zm-8 37l1 1h0l2 2h2 1v2l-1 1h-1c0-1 0-1-1-2l-1 1c0-2-2-3-2-5z" class="S"></path><path d="M158 241h1l1-1c0 2 2 1 2 4h1 0-2l-1 1h-1c-1-1-1-2-2-3 1 0 1 0 1-1z" class="U"></path><path d="M170 204c3-1 5 0 8 1h0v1c-3-1-7-1-10 0 0-1 1-2 2-2z" class="r"></path><path d="M157 269c2-2 3-3 3-5v-1h1c1 2 1 3 1 5h0-1-1l-2 2-1-1z" class="F"></path><path d="M153 288h0c0 1-1 6-2 7 0 2-1 4-2 4s-1 0-1-1c1-1 2-3 3-5 0-2 0-4 2-5z" class="L"></path><path d="M166 224l-2-1 1-1c3 0 6 1 10 1l1-1v2h-3v1c-3 0-5-1-7-1z" class="AF"></path><path d="M165 266h0v1l1 1h1v-1 1h1c-1 1-2 1-3 2l-5 1 1-1h-1c0-1 1-1 2-2h0 1l2-2z" class="K"></path><path d="M154 235l-1-1v-1-1c2 1 2 3 3 5v-1l1-2h2 0v4h-2l-2-2h0l-1-1z" class="U"></path><path d="M169 188l2 2-4 6h-3v-2l1-1v-1l1 1c1-1 3-3 3-5z" class="g"></path><path d="M151 251v1h1c1 1 1 3 1 4h0c-1 1-1 1-2 1h0-1c0-1 0-1-1-2v-1l1-1v-2h1z" class="O"></path><path d="M149 255l1-1c1 0 1 1 3 2h0c-1 1-1 1-2 1h0-1c0-1 0-1-1-2z" class="B"></path><path d="M163 244c2 1 3 1 4 2h-1c-1-1-2-1-4-1-2 1-3 4-5 5l-1-2-1-2h0c1 0 1 0 1-1h0 1v1 2c1 0 1-1 2-2h0v-1c1 0 2-1 4-1h0z" class="a"></path><path d="M155 201h2 2 0c1 1 0 2 0 3l1 1h0 0l1 1h0-2-1v1l-3 3c0-1 0-2 1-2 0-1 0-2 1-3h-1v-2h0l-1-1v-1z" class="J"></path><path d="M157 201h2c-1 1-1 2-1 3h-1 0c-1-1 0-2 0-3z" class="T"></path><path d="M182 205h1v3l1 1h2c0 1 0 3-1 4l-1 1-1 1c0-1 0-4-1-5v-1c-1-2-2-2-4-3v-1c1 1 3 2 4 2v-2z" class="j"></path><path d="M168 210h1 1l-1 1c1 0 2 0 3-1v1c-1 0-8 5-8 6h0l-1 1h-1 1v-1c-1 0-2 2-4 2h0c3-3 6-4 9-7-2 1-5 3-7 3h0c2-2 4-2 6-3 1-1 1-1 1-2z" class="B"></path><path d="M173 212c1-1 1 0 2 0h0c-1 2-1 3-3 3-2 1-3 3-5 4h-2v-1c2-1 3-3 5-4 0 0 1-1 2-1v-1h1z" class="O"></path><path d="M150 257h1 0c1 0 1 0 2-1 1 3 0 5 0 7v3 1c0-1-1-2-2-3l-1 1c0-1 0-1-1-2l1-1c1-1 1-3 1-3-1-1-1-1-1-2z" class="L"></path><path d="M149 263l1-1c1 0 1 2 1 2l-1 1c0-1 0-1-1-2z" class="O"></path><path d="M160 271l5-1h1 0l-1 1h0c-2 2-5 4-8 6-1 1-2 3-4 4v-2h1c1-1 3-2 4-3 0-2 1-3 2-5z" class="M"></path><path d="M152 240l2 2h0l1-2 2 2c1 1 1 2 2 3h1l1-1h2c-2 0-3 1-4 1v1h0c-1 1-1 2-2 2v-2-1h-1 0c0 1 0 1-1 1h0 0c-2-1-3-3-4-5l1-1z" class="Q"></path><path d="M162 289c-1 1-1 1-1 2h2v2l1 1c1 1 1 1 2 1-1 1-2 1-3 1l-3 1c-1 1-1 1-3 1v-1c1-3 3-6 5-8z" class="j"></path><path d="M161 291h2v2l1 1h-1c-1 0-1 0-2-1v-2z" class="q"></path><path d="M163 294h1c1 1 1 1 2 1-1 1-2 1-3 1s-1-1-1-2h1z" class="X"></path><path d="M168 210c0 1 0 1-1 2-2 1-4 1-6 3h0c-3 1-4 2-6 5-1 1-1 2-2 2h0c0-1 1-2 1-3 1-1 2-2 2-3s0-2 1-2c0-2 1-1 2-1 1-1 1-2 3-2l1 1c1 0 4-1 5-2z" class="O"></path><path d="M146 256c0-1 1-2 1-2h1v1s0 1 1 1v-2 1c1 1 1 1 1 2s0 1 1 2c0 0 0 2-1 3l-1 1c1 1 1 1 1 2l-1 1v-2h-1v-1c-1 0-1 0-1-1l-2-5 1-1h0z" class="k"></path><path d="M146 256l1-1c1 1 1 1 1 2 1 0 1 0 1 1l-1 1c-1-1-1-2-2-3h0z" class="L"></path><path d="M148 263h0v-2c-1-1 0-1 0-1h0 1v3c1 1 1 1 1 2l-1 1v-2h-1v-1z" class="M"></path><path d="M146 226c0-2-1-5 0-7v2h2 0 1l1 1c1 2 1 4 0 5v5 1h-1v-2c-1-1-1-1-1-2h0-1v-1l-1-2h0z" class="S"></path><path d="M146 226h1v1 1l-1-2h0z" class="K"></path><path d="M146 221h2 0c0 1-1 3-2 4v-4z" class="c"></path><path d="M147 227c1-1 1-2 2-3h1v3 5 1h-1v-2c-1-1-1-1-1-2h0-1v-1-1z" class="C"></path><path d="M148 215c1-1 1 0 2-1s1-2 2-3l2-1 1 2c-1 0-2 3-3 4v1c-1 2-1 3-2 5l-1-1h-1 0-2v-2-1h2c0-1 1-1 1-2l-1-1z" class="P"></path><path d="M149 216c0 1 1 1 1 2v1c-1 0-1 1-2 2h-2v-2-1h2c0-1 1-1 1-2z" class="h"></path><path d="M157 269l1 1 2-2h1 1c-1 1-2 1-2 2h1l-1 1c-1 2-2 3-2 5-1 1-3 2-4 3h-1c0-1 0-2-1-3h0c1-1 1-2 1-3h0 1c0-1 2-3 3-4z" class="T"></path><path d="M157 269l1 1 2-2h1 1c-1 1-2 1-2 2-2 1-4 2-5 4-1 0-1 0-1-1s2-3 3-4z" class="G"></path><path d="M170 276c0-1 2-3 3-3 2 0 3 0 4 3 1 1 1 2 1 3 1 2 3 4 3 6l-2 2c-1 0-1-1-1-3h0c0-1-1-3-2-4-1-2-1-3-3-5-1 1-2 1-3 1z" class="O"></path><path d="M182 180c2 1 3-1 5-2h0 2c-1 2-3 3-5 5h-1v-2c-2 0-5 2-6 3-2 1-4 3-6 6l-2-2 3-3-1-1s5-2 7-4v1c1 0 2-1 4-1z" class="v"></path><path d="M148 240l1-2 1 2h1v1c1 2 2 4 4 5h0l1 2c-2 0-3 0-4 1v3h-1v-1h-1l-1-1v-3c0-2-1-3-2-4 0-1 0-2 1-3z" class="o"></path><path d="M148 240l1-2 1 2 1 4-2 1v-1h0c0-2 0-3-1-4z" class="m"></path><path d="M149 245l2-1 1 5h0v3h-1v-1c0-2-1-4-2-6z" class="b"></path><path d="M150 240h1v1c1 2 2 4 4 5h0l1 2c-2 0-3 0-4 1h0l-1-5-1-4z" class="B"></path><path d="M147 229h1 0c0 1 0 1 1 2v2h1v3l-1 2-1 2c-1 1-1 2-1 3l-1 2-2-2h-1v-1c0-1 1-3 1-4l2-7 1-2z" class="P"></path><path d="M143 242h1l1 1h-1-1v-1z" class="N"></path><path d="M166 203c0-2 0-3 2-4v-1c2-3 5-5 8-7 1 1 1 2 1 3-2 2-6 5-7 8h1l-1 2c-1 0-2 1-2 2h-1-1v-1-2z" class="U"></path><path d="M166 203c1-1 1-1 2-1h1l-2 4h-1v-1-2z" class="R"></path><path d="M155 202l1 1h0v2h1c-1 1-1 2-1 3-1 0-1 1-1 2v2l-1-2-2 1c-1 1-1 2-2 3s-1 0-2 1l1 1c0 1-1 1-1 2h-2l1-3s-1-1 0-2l1-1c0-2 1-4 2-5l2-2 2-2 1-1h0z" class="t"></path><path d="M148 212h2l-1 1-1 1v1l1 1c0 1-1 1-1 2h-2l1-3s-1-1 0-2l1-1z" class="j"></path><path d="M148 212h2l-1 1c-1 0-1-1-1-1z" class="s"></path><path d="M155 202l1 1h0v2c-1 0-2 1-2 2l-1 1c0 1-2 2-2 2-1 1-1 1-1 2h-2c0-2 1-4 2-5l2-2 2-2 1-1h0z" class="v"></path><path d="M155 202l1 1h0v2c-1 0-2 1-2 2l-1 1c0 1-2 2-2 2v-1l1-1c1-2 2-2 2-5l1-1h0z" class="n"></path><path d="M143 244v-2 1h1l2 2 1-2c1 1 2 2 2 4v3l1 1v2l-1 1v2c-1 0-1-1-1-1v-1h-1s-1 1-1 2h0l-1 1c-1-1-1-3-2-5v-4-3-1z" class="Y"></path><path d="M147 243c1 1 2 2 2 4v1h-2v-2h0l-1-1 1-2z" class="d"></path><path d="M143 244v-2 1h1l2 2 1 1-1 3h0c1 1 1 2 1 4h0l-1-2h0l-1-1-2-2v-3-1z" class="J"></path><path d="M143 245c1 1 1 2 2 3v2l-2-2v-3z" class="a"></path><path d="M143 248l2 2 1 1h0l1 2h0v1c1 0 2-1 3-1l-1 1v2c-1 0-1-1-1-1v-1h-1s-1 1-1 2h0l-1 1c-1-1-1-3-2-5v-4z" class="b"></path><path d="M147 253l-1 1h-1v-2l1-1 1 2z" class="R"></path><path d="M151 270l1 1c1 1 1 1 1 2h0c0 1 0 2-1 3h0c1 1 1 2 1 3v2l-5 5-4 6c-2 2-5 7-4 9 1 1 2 0 3 0l-5 1h-1v-3s0-1 1-2c-1-2 0-4 0-5 1-1 1-1 1-2s1-2 1-3c1-3 3-7 5-10h1c0-1 1-2 2-3 0-1 0-2 1-2h0l2-2z" class="Z"></path><path d="M148 284v2l-4 6-1-1c1-2 3-5 5-7z" class="S"></path><path d="M138 297v2-1h1l4-7 1 1c-2 2-5 7-4 9 1 1 2 0 3 0l-5 1h-1v-3s0-1 1-2z" class="B"></path><path d="M151 270l1 1c1 1 1 1 1 2h0c0 1 0 2-1 3h0c1 1 1 2 1 3v2l-5 5v-2c1 0 1-1 2-2 0-1 1-1 1-2 0 0 1-1 0-1 0-1 0-2-1-3 0-1-1-3-1-4l2-2z" class="R"></path><path d="M170 276c1 0 2 0 3-1 2 2 2 3 3 5 1 1 2 3 2 4h0c0 2 0 3 1 3l-1 2h0v3h1c-2 1-3 2-5 2v-1c0-1-1-1-1-2h-2 0l-1 3h-1l-1 1c-1-3-3-6-4-9-1-1 0-1 0-3l2-2c1-1 1-2 2-2v-1c1-1 1-2 2-2z" class="m"></path><path d="M173 291h3c0-1 0-1 1-1 1 1 0 1 1 2h1c-2 1-3 2-5 2v-1c0-1-1-1-1-2z" class="c"></path><path d="M168 279h2c0-1 0-1 1-1 1 1 3 3 3 5 0 0 0 1 1 1h-1c-1 2 1 5-1 6h0c-1-2 0-3-1-4 0-2-1-3-1-5-1 0-1-1-2-1s-2 1-3 1c1-1 1-2 2-2z" class="l"></path><path d="M170 276c1 0 2 0 3-1 2 2 2 3 3 5 1 1 2 3 2 4h0c0 2 0 3 1 3l-1 2h0 0-1-1c0-3-1-7-2-9l-3-3c-1 0-2 0-3 1 1-1 1-2 2-2z" class="P"></path><path d="M166 281c1 0 2-1 3-1s1 1 2 1c0 2 1 3 1 5 1 1 0 2 1 4h0-1l-1 1h0v-1c-1-2-1-5-3-6h0l-2 1h-2v1c-1-1 0-1 0-3l2-2z" class="d"></path><path d="M164 283l4-1c2 2 3 5 4 7v1l-1 1h0v-1c-1-2-1-5-3-6h0l-2 1h-2v1c-1-1 0-1 0-3z" class="j"></path><path d="M164 286v-1h2l2-1h0c2 1 2 4 3 6v1l-1 3h-1l-1 1c-1-3-3-6-4-9z" class="n"></path><path d="M168 284c2 1 2 4 3 6v1l-1 3h-1v-2c0-2 1-4 0-6-1-1-1-1-1-2z" class="g"></path><path d="M185 187c2 0 3 1 4 1l-1 2s1 1 2 1l1 1c0 1 1 2 2 2l1 1c-1 0-1 1-2 2h1v1h-1c-1 0-1-1-2-1h0l-1-1h-3l-1 1 1 2v2 3 1 1h-2v3l-1-1v-3h-1v2c-1 0-3-1-4-2h0c-3-1-5-2-8-1l1-2h-1c1-3 5-6 7-8 0-1 0-2-1-3 4-1 6-3 9-4z" class="Z"></path><path d="M191 192c0 1 1 2 2 2l1 1c-1 0-1 1-2 2v-1h0c-1-1-1-2-1-4z" class="M"></path><path d="M184 197c-1 0-1 0-2-1 1-1 1-1 1-2h1l2 2-1 1h-1z" class="a"></path><path d="M171 202c1 0 2-1 3-1 2 1 4 2 4 3v1c-3-1-5-2-8-1l1-2z" class="f"></path><path d="M185 187c2 0 3 1 4 1l-1 2h-2c-1 0-1-1-2-1h0c-1 1-2 1-3 2h-1l-3 3c0-1 0-2-1-3 4-1 6-3 9-4z" class="O"></path><path d="M184 197h1l1 2v2 3 1 1h-2v3l-1-1v-3h-1v-2l1 1c0 1 1 1 0 2v1h1v-2c0-2 0-2-1-2v-3h1l-1-2 1-1z" class="F"></path><path d="M184 206l1-2h1v1 1h-2z" class="m"></path><path d="M188 284l1 4-1 1c0 2 0 2-2 4l-2 3-1 3-1 1-2 2h-2l-3 2h-2c-2 1-3 2-5 2h0-2c-1 1-2 1-3 1-2 1-3 1-4 2l-1 1c-1 0-1-1-2-2h-2 0c-1-1-1-1-2-1-2-1-3 0-5 0l-1 1-2 1c-1 1-2 1-3 1 0 1-1 1-2 1h0l-1 1h-3c0 1-1 1-2 1h-2l1-2h0c0-1-1-2-2-3v-1h-1v-2c0-1 0-1 1-2h1 1 3v-1h0 2 1l5-1c3 0 7-1 10-1l14-4 1-1 1-1h1l1-3h0 2c0 1 1 1 1 2v1c2 0 3-1 5-2 1-1 3-2 4-2v-1l5-5z" class="Ae"></path><path d="M161 301l5-1c-1 1 0 1-2 2l-3 1v-2z" class="F"></path><path d="M171 291h0 2c0 1 1 1 1 2v1l-1 1c-2 0-4 1-6 1l1-1 1-1h1l1-3z" class="d"></path><path d="M143 301c3 0 7-1 10-1-2 1-6 2-8 3-2 0-3 1-5 1 0-1-1-1-2-1v-1l5-1z" class="AN"></path><path d="M150 304l11-3v2c-2 1-3 2-5 2s-2 0-4 2c-2-1-3 0-5 0l4-3h-1z" class="e"></path><path d="M151 304c1 0 2 0 2 1h1l1-1 1 1c-2 0-2 0-4 2-2-1-3 0-5 0l4-3z" class="i"></path><path d="M161 303l3-1 1 1c-1 1-4 2-5 3v1l-2 2h1l-1 1c-1 0-1-1-2-2h-2 0c-1-1-1-1-2-1 2-2 2-2 4-2s3-1 5-2z" class="U"></path><path d="M160 306v1l-2 2h1l-1 1c-1 0-1-1-2-2h-2 0c1 0 2-2 4-2h2z" class="Q"></path><path d="M172 300c0 1 0 1-1 2 0 0-1 0-1 1h1 0c1 0 1 0 2 1-2 1-3 2-5 2h0-2c-1 1-2 1-3 1-2 1-3 1-4 2h-1l2-2v-1c1-1 4-2 5-3s3-1 4-2c1 0 2-1 3-1z" class="G"></path><path d="M171 303c1 0 1 0 2 1-2 1-3 2-5 2h0-2c-1 1-2 1-3 1 2-2 5-3 7-4h1 0z" class="P"></path><path d="M137 302h1v1l-1 2 1 1c3 0 5-1 8-1v-1h1 3 1l-4 3-1 1-2 1c-1 1-2 1-3 1 0 1-1 1-2 1h0l-1 1h-3c0 1-1 1-2 1h-2l1-2h0c0-1-1-2-2-3v-1h-1v-2c0-1 0-1 1-2h1 1 3v-1h0 2z" class="q"></path><path d="M145 306l1 1-3 1h-2v-1l4-1z" class="w"></path><path d="M132 303h3 1c0 1 0 2-1 3v1l-2-1h1c0-2-1-2-2-3zm9 5c0 1-1 1-1 2h-5v-1l6-2v1z" class="AN"></path><path d="M135 308c0 1-2 3 0 4h0c0 1-1 1-2 1h-2l1-2h0c1-1 1-1 1-2l2-1z" class="a"></path><path d="M132 308c0-1 1-2 1-2l2 1v1l-2 1c0 1 0 1-1 2 0-1-1-2-2-3v-1h1l1 1z" class="X"></path><path d="M132 308c0-1 1-2 1-2l2 1v1l-2 1-1-1z" class="Ae"></path><path d="M146 305v-1h1 3 1l-4 3-1 1h-3l3-1-1-1 1-1h0z" class="AT"></path><path d="M131 303h1c1 1 2 1 2 3h-1s-1 1-1 2l-1-1h-1-1v-2c0-1 0-1 1-2h1z" class="s"></path><path d="M130 303h1v4h-1-1v-2c0-1 0-1 1-2z" class="AN"></path><path d="M188 284l1 4-1 1c0 2 0 2-2 4l-2 3-1 3-1 1-2 2h-2l-3 2h-2c-1-1-1-1-2-1h0-1c0-1 1-1 1-1 1-1 1-1 1-2-1 0-2 1-3 1-1 1-3 1-4 2l-1-1c2-1 1-1 2-2 3-1 6-2 8-4 0-1-1-1-1-1l1-1c2 0 3-1 5-2 1-1 3-2 4-2v-1l5-5z" class="B"></path><path d="M184 296l-1-1 1-1 2-2v-1 2l-2 3z" class="V"></path><path d="M183 290c0 1-1 2-3 3-1 1-4 3-6 3 0-1-1-1-1-1l1-1c2 0 3-1 5-2 1-1 3-2 4-2z" class="AN"></path><path d="M172 300h0c3-1 5-2 7-3h1s0-1 1-1h1c0 1 0 1 1 2v1l-1 1-2 2h-2l-3 2h-2c-1-1-1-1-2-1h0-1c0-1 1-1 1-1 1-1 1-1 1-2z" class="L"></path><path d="M178 300h0c2 1 3 0 4 0l-2 2h-2c1-1 0-1 0-2z" class="S"></path><path d="M171 303c1-1 3-1 4-2l-1 1v1l1 1h-2c-1-1-1-1-2-1z" class="G"></path><path d="M178 300c0 1 1 1 0 2l-3 2-1-1v-1l1-1c1 0 2 0 3-1z" class="J"></path><defs><linearGradient id="s" x1="150.413" y1="268.638" x2="124.333" y2="281.338" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#35312e"></stop></linearGradient></defs><path fill="url(#s)" d="M141 251c1 0 1 0 2 1 1 2 1 4 2 5l2 5c0 1 0 1 1 1v1h1v2l1-1 1-1c1 1 2 2 2 3h0c-1 1-2 1-3 1l1 2-2 2h0c-1 0-1 1-1 2-1 1-2 2-2 3h-1c-2 3-4 7-5 10 0 1-1 2-1 3s0 1-1 2c0 1-1 3 0 5-1 1-1 2-1 2v3h-2 0v1h-3-1-1-3c0-5-2-11-4-15 0-1 0-2 1-2l1-3h2v1h1 1c-1-1-1-2-2-3h-1c0-1 0-2 1-4v-2c0-1 0-1 1-2h0 0v-2l1-2h1v-1s1-1 1-2 1-2 1-3 1-2 1-3c1-1 1-3 1-3v-3c1-1 1-1 2-1l1-2v1h3l1-1z"></path><path d="M128 273h0l2 4v2l-1-1v-1l-1-1v-3z" class="D"></path><path d="M141 256h1l1 1h-1-1c-1 1-2 2-3 2h-1 0c1 0 1-1 2-2 1 0 1 0 2-1h0z" class="U"></path><path d="M139 261c-1 1-2 1-2 3l-1 1v-1l-1-1v-2c1 0 1 1 2 1h0c1 0 1 0 2-1z" class="B"></path><path d="M128 273v3l1 1v1-1h-2v-2c0-1 0-1 1-2z" class="J"></path><path d="M136 253l1-2v1h3c-1 1-1 2-2 2l-1 1s-1-1-1-2h0z" class="N"></path><path d="M137 252h3c-1 1-1 2-2 2l-1-2z" class="k"></path><path d="M135 276h0c0 3-2 4-2 7h0v-2l-1 1-1-5h2v-1h2z" class="D"></path><path d="M134 254c1-1 1-1 2-1h0c0 1 1 2 1 2-2 2-2 3-2 6l-2-1c1-1 1-3 1-3v-3z" class="I"></path><path d="M134 254c1-1 1-1 2-1h0c-1 1-2 2-2 4v-3z" class="C"></path><path d="M127 277h2v1l1 1v4l-1 1c-1-1-1-2-2-3h-1c0-1 0-2 1-4z" class="Q"></path><path d="M148 264h1v2l1-1 1-1c1 1 2 2 2 3h0c-1 1-2 1-3 1l1 2-2 2h0c0-2 0-3-1-4h0v-1-3z" class="G"></path><path d="M148 268l1-1h0l1 1 1 2-2 2h0c0-2 0-3-1-4z" class="Q"></path><path d="M131 266v2h1c0 1 1 2 1 3l1 1h0c0 2 0 3 1 4h-2v1h-2v-2l-1-4v-2-1s1-1 1-2z" class="F"></path><path d="M130 268c1 1 1 1 1 2l-1 1v-2-1z" class="J"></path><path d="M130 271l1-1c1 2 1 2 1 4l-1 1-1-4z" class="p"></path><path d="M133 276c0-1-1-2 0-4h1c0 2 0 3 1 4h-2z" class="R"></path><path d="M132 299h1 0c1-3 2-5 3-7h0 0v1h0 1v-1h1c0 1-1 3 0 5-1 1-1 2-1 2v3h-2 0-1c-1 0-1-2-2-3z" class="T"></path><path d="M137 293v-1h1c0 1-1 3 0 5-1 1-1 2-1 2v3h-2c0-2 1-2 1-4s1-4 1-5z" class="K"></path><path d="M139 261c2 0 3 0 5 1 0 0 1 1 1 2h0c0 1 0 2-1 2 0 1-2 3-3 4h-2l-1 2c0-2 0-3-1-4 0-1 0-2-1-2v-1l1-1c0-2 1-2 2-3z" class="H"></path><path d="M137 264c1 0 1 0 3 1v4l-1 1-1 2c0-2 0-3-1-4 0-1 0-2-1-2v-1l1-1z" class="M"></path><path d="M133 260l2 1h0 0v2l1 1v1 1c1 0 1 1 1 2 1 1 1 2 1 4l-2 3-1 1h0c-1-1-1-2-1-4h0l-1-1c0-1-1-2-1-3h-1v-2c0-1 1-2 1-3s1-2 1-3z" class="S"></path><path d="M134 264c2 2 1 4 0 6v2l-1-1c0-1-1-2-1-3l1-1c1-1 1-1 1-3z" class="Q"></path><path d="M137 268c1 1 1 2 1 4l-2 3c-1 0-1 0-1-1s1-5 2-6z" class="D"></path><path d="M133 260l2 1h0 0c0 2-1 2-1 3 0 2 0 2-1 3l-1 1h-1v-2c0-1 1-2 1-3s1-2 1-3z" class="G"></path><path d="M131 266h1l1 1-1 1h-1v-2z" class="p"></path><defs><linearGradient id="t" x1="123.336" y1="295.248" x2="132.263" y2="289.759" xlink:href="#B"><stop offset="0" stop-color="#23211f"></stop><stop offset="1" stop-color="#48423a"></stop></linearGradient></defs><path fill="url(#t)" d="M125 283h2v1h1 1l1-1v3c1 0 1 1 1 3h0c1 2 2 2 1 4v1 5h0c1 1 1 3 2 3h1v1h-3-1-1-3c0-5-2-11-4-15 0-1 0-2 1-2l1-3z"></path><path d="M130 286c1 0 1 1 1 3h0c-1 4 0 7-1 10v-2c-1-1-1-1-1-2h0c1-1 1-4 1-5-1-2-1-3 0-4z" class="H"></path><path d="M131 289c1 2 2 2 1 4v1 5h0c1 1 1 3 2 3h1v1h-3-1-1v-4c1-3 0-6 1-10z" class="I"></path><path d="M176 224h7c4 1 9 0 14 0 3 0 6 1 9 0h1 7 16 5l2 2s0 1 1 1l1 2-1 1c2 2 4 4 5 6v1l1 1c-6 0-13-1-19 1h-3l-1 1c-2 0-3 1-5 1-1 2-2 3-4 5l-1 2c-1 5-4 10-5 16l-1 1v1l1 1h0c-1 2-1 3-1 5h0-1l-1 4v1c-3-1-6 0-8 0h-4-5c-2-3-3-8-4-12l-8-23v-1l-2-6-1-2c-1-3-3-7-5-9 2 0 4 1 7 1v-1h3z" class="Ab"></path><path d="M204 272l1-6 1 1h0c-1 2-1 3-1 5h0-1z" class="AK"></path><path d="M174 241c2-2 3-2 5-3h3l1 1c-4 0-6 2-9 3v-1z" class="AQ"></path><path d="M191 277l-2-1c-1 0-1 0-1-1l1-1v1c1 0 2 0 2-1l1 1h1 3 0l-1 2h-4z" class="AS"></path><path d="M190 271c1 0 3-1 4 0l2 2v2h-3-1l-1-1c0-1 0-1-1-1h-1 0v-1l1-1z" class="Ag"></path><path d="M191 274c0-1 0-1-1-1h-1 0v-1l1-1v1c2 0 2 0 3 1v2h0-1l-1-1z" class="AH"></path><path d="M211 246v2c-1 5-4 10-5 16l-1 1c0-2 0-3 1-4 1-4 2-7 3-10 0-2 0-3 1-5h1 0zm-27-9h16v1h-2l2 1h-15-2l-1-1h-3c2-1 4 0 5 0v-1z" class="AK"></path><path d="M179 238c2-1 4 0 5 0h2 0 0c0 1-1 1-1 1h-2l-1-1h-3z" class="AC"></path><defs><linearGradient id="u" x1="177.378" y1="244.013" x2="232.512" y2="216.514" xlink:href="#B"><stop offset="0" stop-color="#cc2d27"></stop><stop offset="1" stop-color="#e7715b"></stop></linearGradient></defs><path fill="url(#u)" d="M176 224h7c4 1 9 0 14 0 3 0 6 1 9 0h1 7 16 5l2 2s0 1 1 1l1 2-1 1c2 2 4 4 5 6v1l1 1c-6 0-13-1-19 1h-3l-1 1c-2 0-3 1-5 1-1 2-2 3-4 5l-1 2v-2h0-1c-1-1-1-2-2-3-2-3-5-4-8-4l-2-1h2v-1h-16v1c-1 0-3-1-5 0s-3 1-5 3l-2-6-1-2c-1-3-3-7-5-9 2 0 4 1 7 1v-1h3z"></path><path d="M220 238h2v1h0l-1 1-1-2z" class="AY"></path><path d="M216 241h0c1-1 2-2 4-3l1 2c-2 0-3 1-5 1z" class="AC"></path><path d="M230 224h5l2 2s0 1 1 1l1 2-1 1-1-2c-3-2-4-2-8-3 1 0 1 0 1-1z" class="Ai"></path><path d="M200 238c3 0 5 0 7 2s3 4 4 6h0-1c-1-1-1-2-2-3-2-3-5-4-8-4l-2-1h2z" class="AC"></path><path d="M237 228l1 2c2 2 4 4 5 6v1l1 1c-6 0-13-1-19 1h-3 0v-1c2-1 4-1 6-2h4 0 10l-2-3h0c-1-2-3-3-3-5h0z" class="Ad"></path><path d="M176 224h7c4 1 9 0 14 0 3 0 6 1 9 0h1 7 16c0 1 0 1-1 1-1 1-3 1-4 1h-11c-14-1-27 0-41-1v-1h3z" class="AD"></path><path d="M171 233c7 1 15 1 21 1h8l2 2h1l-1 1h-2-16v1c-1 0-3-1-5 0s-3 1-5 3l-2-6-1-2z" class="Ag"></path><path d="M172 235h0c1 1 2 1 2 1 2 0 4 1 6 1h4v1c-1 0-3-1-5 0s-3 1-5 3l-2-6z" class="AR"></path><path d="M279 108h1l12-12c6 5 12 12 18 18l15 16c0-1 1-2 1-3 1 1 2 1 2 3 0 1 0 0 1 1v1l1 1c1 0 2 1 2 2h-1-1v2l1 1-1 1c6 6 12 12 17 18 7 9 13 19 20 28l10 18c1 0 1 1 2 1v-2c1 1 1 2 2 2 0 1 0 2 1 3v1h0 1c2 2 3 5 4 8l-1 1v1 4 2l-1-1v1h5 1 8 0 1v1l-6 1h0-11 3c0 1-1 1-1 1 0 1-1 2-1 2 1 1 1 2 2 3l-1 2h0c-1 1-1 2-2 3l1 1h0-3l-3 1h0l-4 4-1 3h-1v-1l-1 1c0 1 0 2-1 2l-4-3-9-3-22-3h-2v1c-1 0-1 0-2-1h-7l-20 2c-2 0-5 1-7 1v-1c-1 0-4 1-5 2-1 0-3 1-4 1 0 0-1 1-2 1 0 1 0 1-1 2 0 0-1-1-2-1l-1 1h0v2h-1c-1-1 0-2-2-3l-18-6c-2-1-5-1-8-2v1l-3-1-2-1h-1v1h-1c0 1 0 1-1 2l-1-2-1-1v-1c-1-2-3-4-5-6l1-1-1-2c-1 0-1-1-1-1l-2-2h-5-16-7-1-9c4-7 7-14 11-21l6-11c1-1 2-3 3-5l8-12h0c8-14 19-25 29-37h0c1-1 2-3 3-4l7-8-1-1 3-3 13-14z" class="E"></path><path d="M268 134h1l-1 4-1-1 1-3z" class="S"></path><path d="M315 135c-1-2-1-3-1-4h1l1 3-1 1zm-52 2c1-1 1-3 2-4 0 1-1 3-1 5l-1-1z" class="U"></path><path d="M323 148l1-1 1 3h-2v-2z" class="P"></path><path d="M359 192v-3-1h1v1c-1 2 0 3 0 5h0v-1c-1 0-1-1-1-1z" class="H"></path><path d="M360 183c-1-1-1-2-2-3h1 0l2 3h-1zm-142 10c0-2 1-3 2-4v1c0 1-1 2-2 3h0z" class="B"></path><path d="M262 141h1v3h-1v-1l-1 1 1-3z" class="M"></path><path d="M335 154l-1-5v-1c1 1 1 2 1 3v3z" class="H"></path><path d="M263 137l1 1c0 1 0 2-1 3h-1c0-2 1-3 1-4z" class="L"></path><path d="M268 134l1-4h0v4h-1z" class="K"></path><path d="M323 150h2l1 3h-1l-2-3z" class="N"></path><path d="M315 135l1-1c0 1 1 3 1 4l-1 1-1-4z" class="I"></path><path d="M256 148l1 1-1 2v2c-1 0-1 0-2-1l2-4z" class="L"></path><path d="M360 189l2 6-1 1-1-2c0-2-1-3 0-5z" class="M"></path><path d="M352 175c0-3-2-6-2-9h1l2 6c0 1-1 2-1 3z" class="L"></path><path d="M344 191h2c1 2 2 4 1 5h1v2l-2-3-2-4z" class="H"></path><path d="M381 220c-2-2-3-3-4-5v-1h1c1 2 3 4 4 6h-1z" class="B"></path><path d="M346 164c1 1 1 3 2 4v3h-1l-1-5v-2z" class="K"></path><path d="M321 143h1l2 1v3l-1 1c-1-2-1-3-2-5z" class="C"></path><path d="M234 179l1-1h1l-1 3-1 2h0l-1-1 1-3z" class="F"></path><path d="M256 148l3-6v4l-1 1h0c0 1 0 1-1 2l-1-1z" class="U"></path><path d="M283 135h1c0 2-1 4 0 5l-1 3h-1v-3c1-2 1-3 1-5z" class="D"></path><path d="M218 193h0l-2 9v-1h-1l3-8z" class="S"></path><path d="M222 201l-1 1 1 1v-2c1 0 1 0 1-1l1-1v-1h1 0c-1 1-1 2-1 2-1 2-2 4-2 6h-2c1-2 1-3 1-5h0 1z" class="R"></path><path d="M223 187c0-3 1-7 3-9h0 0l-2 9h-1z" class="B"></path><path d="M344 165c0-2-1-5 0-7l2 6v2l-1 1h0 0l-1-2z" class="O"></path><path d="M256 164c0-3 1-6 2-8l1 2h-1v1 1c0 1 0 1-1 1v1 1c0-1 0-1 1-2v1 1h0c-1 1 0 1-1 1 0 1-1 2-1 3v-3z" class="K"></path><path d="M299 138l1-1 1 1v2c0 2 1 4 1 6v3h0c-2-3-2-8-3-11zm-16-3c1-5 2-11 3-15 0 5-1 10-2 15h-1z" class="M"></path><path d="M321 143c-1-2-2-7-2-10 2 4 3 7 5 11l-2-1h-1z" class="V"></path><path d="M346 195l2 3h0c0 2 3 4 3 5s-1 2-1 2l-1-1c0-1-2-3-3-5v-3-1z" class="N"></path><path d="M344 165c-1-2-2-4-2-6v-3h1l1 1v1c-1 2 0 5 0 7z" class="B"></path><path d="M360 183h1l4 9c1 3 1 5 3 7h-1l-7-16z" class="S"></path><path d="M267 137l1 1-1 3c-1 3-2 7-2 10h0v-1l-1-1c0-1 1-3 1-5l2-7z" class="J"></path><path d="M293 127v-3l2 17h0c-1-1-1-2-2-2v-3-9z" class="G"></path><path d="M365 192c3 3 7 9 6 13h0c-1-1-3-5-3-6-2-2-2-4-3-7z" class="U"></path><path d="M229 198c0-1 1-1 2-1 0-1 0-1 1-2 0 1 0 2-1 2v1l1 1c-2 0-2 0-3 1-1 0 0 1 0 1 0 1-1 2-1 3h0l-1-1c0 1-1 2-1 2l1-2c-1 0-1-1-1-1l1-3h1l1-1z" class="V"></path><path d="M226 202l1-3h1l-1 4c-1 0-1-1-1-1z" class="e"></path><path d="M380 210l3 5c1 1 2 2 2 3h1v4 2l-1-1c-1-4-4-8-5-13z" class="G"></path><path d="M299 138l-1-13c0-2-1-5 0-7v4l1 7c1 2 1 4 2 6v3l-1-1-1 1z" class="f"></path><path d="M316 139l1-1 3 12c0 1 1 2 1 3 0 0-1 0-1 1h0v2h0l-4-17z" class="k"></path><path d="M245 164c0-3 1-6 1-8h0c1-1 1-2 1-3 2-1 2-3 3-4 0 1 0 3-1 4h0 0l-2 8c0 1-1 2-1 3s0 2-1 3v-3z" class="V"></path><path d="M340 174c0 1 1 1 1 2 0 2 1 3 2 5l3 10h-2l-4-14v-2-1z" class="B"></path><path d="M377 203c1 0 1 1 2 1l3 6c1 2 1 3 2 4l-1 1-3-5-3-7z" class="D"></path><path d="M347 175l7 20c-1-1-2-1-2-2v-1l-1-2v-1-1c-1 0-1-1-1-2s-1-1-2-2h0l-1-1c0-1-1-4 0-5v-3z" class="L"></path><path d="M361 196l1-1 6 17h0c-2-2-4-3-4-6s-2-7-3-10z" class="C"></path><path d="M254 152c1 1 1 1 2 1v-2h1 0c0 1-1 3-1 4l-3 8c-1 0-1 1-3 1l1-3 3-7v-2z" class="K"></path><path d="M254 154c1 2-1 5-2 7 0 1 0 1-1 0l3-7z" class="f"></path><path d="M379 204v-2c1 1 1 2 2 2 0 1 0 2 1 3v1h0 1c2 2 3 5 4 8l-1 1v1h-1c0-1-1-2-2-3l1-1c-1-1-1-2-2-4l-3-6z" class="C"></path><path d="M290 145l1-27v1c0 5-1 10 0 15h0c2-2 1-5 2-7v9c-1 1-1 2-2 3v1c0 1 0 3-1 4v1z" class="n"></path><path d="M245 167c1-1 1-2 1-3s1-2 1-3l2-8h0l-1 8c0 2 0 4-1 6v4c-1 1-1 2-1 4v-4l-1 1h0c0 1 0 2-1 3l1-8z" class="Q"></path><path d="M261 144l1-1v1h1l-2 5c-1 2-1 5-3 6-1 1-1 2-1 3-1-1-1-2-1-3s1-3 1-4h0c2-2 2-4 3-5l1-2z" class="I"></path><path d="M257 151c2-2 2-4 3-5v3c-1 1-1 1-1 2v1h-1 0c0-1 0-1-1-1h0z" class="M"></path><path d="M216 211h1l1-2h0v2-1h1 0v1l1 1c-1 1-1 2-2 3h0v1h1c-2 1-4 3-6 3 1-3 1-6 3-8z" class="V"></path><path d="M218 210h1 0l-1 1h0v-1z" class="C"></path><path d="M340 177l4 14 2 4v1l-5-10c-1-2-1-5-2-6l-1-2h1l1-1z" class="a"></path><path d="M368 199c0 1 2 5 3 6l4 14v1c-3-2-3-6-4-9-1-4-3-8-4-12h1z" class="D"></path><path d="M271 163l3-13v-2c0-1 0-1 1-2v-1c0-3 2-6 2-8 1-1 0-2 0-3h1 0c0 3-1 7-2 10 0 2 0 4-1 6 0 3-1 5-2 8 0 2 0 4-1 5h0-1z" class="P"></path><path d="M223 187h1l-4 14h1c0 2 0 3-1 5v4h-1-1v1-2h0l-1 2h-1l3-7c-1-2 1-4 1-6 1-3 1-6 2-8 0-1 1-2 1-3z" class="V"></path><path d="M219 204h1v1c-1 1-2 3-2 5v1-2h0l-1 2h-1l3-7z" class="I"></path><path d="M221 201l5-12 5-15c1-2 2-5 3-7 0 2-1 4-1 6l-3 12v1l-1 1v1c0 1 0 1-1 2v-1h0v-1-1-1h0c1 0 1-1 1-1h0c1-1 1-2 1-3h-1c0 1 0 3-1 4-1 0-1 2-1 3-1 1-1 2-2 3l-1 3c-1 2-2 4-2 6h-1z" class="S"></path><path d="M265 151h0c0-3 1-7 2-10v4 1h0v1s-1 1-1 2v1c0 1 0 1-1 3v2 1 1c-1 1-1 1-1 2v2c0 1-1 1-1 2v1 1h0s1 1 0 1v2c-1 1-1 2-2 3h1l-1 1c0 1 0 1-1 2v1c0 1 0 1-1 2 0-2 1-3 1-4l1-3-1-1 2-5 1-3c-1 0-1-1-1-1v-3-1c1 0 1-1 1-1l1 1c1-2 1-3 1-5z" class="V"></path><path d="M260 169l2-5c0 3 0 4-1 6l-1-1z" class="f"></path><path d="M262 156c1 0 1-1 1-1l1 1c0 2-1 3-1 5-1 0-1-1-1-1v-3-1z" class="N"></path><path d="M322 156l2 2v1 2c1 1 1 2 2 3v1l1 2v3h0c1 1 1 2 1 3h1 0v2c0-1 1-2 0-2v-2l-1-1v-1-1h0v-2h0l-1-2v-2l3 6c1 2 1 4 2 6 0 1 0 1 1 2 0 1 0 1 1 2v1c-1 0-1-1-2-2h-1v1c-2-2-3-4-4-6-1-5-3-11-5-16z" class="K"></path><path d="M330 168c1 2 1 4 2 6 0 1 0 1 1 2 0 1 0 1 1 2v1c-1 0-1-1-2-2h-1c-1-2-1-6-1-9z" class="O"></path><path d="M271 163h1 0v1 2l-1 1v1c0 1-1 1-1 1v1 1 1s-1 1-1 2h1s1 1 1 2l-3 3-1 1c-1 0-2 0-3-1l7-16z" class="N"></path><path d="M268 179l-2-1v-1c0-1 2-3 3-4l3-9v2l-1 1v1c0 1-1 1-1 1v1 1 1s-1 1-1 2h1s1 1 1 2l-3 3z" class="G"></path><path d="M262 160s0 1 1 1l-1 3-2 5 1 1-1 3c0 1-1 2-1 4l-2 1-1-1-4 7v-1-1-2c1 0 1 0 1-1h1l1-3 2-3 5-13z" class="x"></path><path d="M259 170l1-1 1 1-1 3c0 1-1 2-1 4l-2 1-1-1 3-7z" class="J"></path><path d="M259 170l1-1 1 1-1 3-1-3z" class="R"></path><defs><linearGradient id="v" x1="244.409" y1="161.181" x2="231.516" y2="173.875" xlink:href="#B"><stop offset="0" stop-color="#212420"></stop><stop offset="1" stop-color="#514842"></stop></linearGradient></defs><path fill="url(#v)" d="M234 179c0-2 1-5 2-7l3-12c1-2 1-3 2-4h1c-1 8-3 16-6 22h-1l-1 1z"></path><path d="M235 181c1 3 0 4-1 7 0 2 0 2-1 4v1c-1 1-1 1-1 2-1 1-1 1-1 2-1 0-2 0-2 1l-1 1h-1c1-4 3-8 4-12l2-5 1 1h0l1-2z" class="L"></path><path d="M233 182l1 1-5 15-1 1h-1c1-4 3-8 4-12l2-5z" class="a"></path><defs><linearGradient id="w" x1="331.957" y1="162.147" x2="342.335" y2="166.244" xlink:href="#B"><stop offset="0" stop-color="#50403d"></stop><stop offset="1" stop-color="#75766a"></stop></linearGradient></defs><path fill="url(#w)" d="M335 151c3 7 4 15 5 23v1 2l-1 1h-1v-1c0-1 0-3-1-5-1-5-1-12-2-18v-3z"></path><path d="M338 177l1-1s0-1 1-1v2l-1 1h-1v-1z" class="c"></path><path d="M291 118c0-2-1-9 1-10 1 0 1 2 1 3v13 3c-1 2 0 5-2 7h0c-1-5 0-10 0-15v-1z" class="w"></path><path d="M320 156h0v-2h0c0-1 1-1 1-1 0 1 1 3 1 3 2 5 4 11 5 16v2l-1 1c-1-1-1-2-2-3l-3-9-1-7z" class="N"></path><path d="M250 164c2 0 2-1 3-1v3l-4 12-1 2c-1 1-1 2-2 2h0v-3h0v-4c0-2 0-3 1-4v-4s0 1 1 2h0c0 1 0 2-1 4h1c2-1 2-3 3-5v-2h-1v-2z" class="S"></path><path d="M247 171v2c0 1 0 1 1 1v3c-1 1-1 1-1 2s0 1 1 1c-1 1-1 2-2 2h0v-3h0v-4c0-2 0-3 1-4z" class="I"></path><path d="M235 196l4-8v-1c0-1 1-2 1-3 0 0 0-1 1-1v-2c1-2 0-3 0-5 1-3 2-4 3-7 0-1 1-3 1-5v3l-1 8c0 2-1 5-2 8v1c0 3-2 6-3 8-1 1-2 1-2 2s-1 1-2 2z" class="B"></path><path d="M345 167h0l1-1 1 5h1l1 4 1 2c1 2 1 3 1 4 1 1 2 2 2 4 0 0 0 1-1 2l3 8-1 1v-1l-7-20-2-8z" class="N"></path><path d="M349 175l1 2v3l-1-3v-2z" class="I"></path><path d="M347 171h1l1 4v2c-1-2-2-4-2-6z" class="G"></path><path d="M350 177c1 2 1 3 1 4 1 1 2 2 2 4 0 0 0 1-1 2l-2-7v-3z" class="S"></path><path d="M257 164v3h0l-1 1h1v-2h1v-1-1h0v-1c1 0 1-1 1-1 0-1 0-2 1-3v-1c1 0 1-1 1-1h0c0-3 1-6 2-8v-1c1-1 1-3 2-4 0 2-1 4-1 5l1 1v1c0 2 0 3-1 5l-1-1s0 1-1 1v1 3l-5 13-1-3v2c-1 0-1-1-2-1 1-1 2-3 2-4s1-2 1-3z" class="U"></path><path d="M264 149l1 1v1c0 2 0 3-1 5l-1-1s0 1-1 1l2-7z" class="F"></path><path d="M262 157v3l-5 13-1-3 6-13z" class="J"></path><path d="M257 178l2-1c0 4-3 7-5 11 1 0 1 1 1 0 1 0 2-1 3-1h1l-5 4-5 4v-1c-1-2 2-6 3-8v-2l4-7 1 1z" class="O"></path><path d="M255 181l1 1v1h-1v-1-1z" class="C"></path><path d="M256 177l1 1c0 1-1 2-1 3h-1v1l-1 1v-1c0 1-1 2-1 2 0 1-1 2-1 2v-2l4-7z" class="G"></path><path d="M293 136v3l1 7v2c0 2 1 5 2 7v1h-1c-1-2-2-3-3-5-1 2-3 4-4 6h-2c1-1 3-3 3-4v-3c1-2 0-3 1-5v-1c1-1 1-3 1-4v-1c1-1 1-2 2-3z" class="W"></path><path d="M291 140h1v9h0c-1 1-2 3-3 4v-3c1-2 0-3 1-5v-1c1-1 1-3 1-4z" class="f"></path><defs><linearGradient id="x" x1="350.606" y1="174.142" x2="358.976" y2="192.311" xlink:href="#B"><stop offset="0" stop-color="#2e2d2a"></stop><stop offset="1" stop-color="#514842"></stop></linearGradient></defs><path fill="url(#x)" d="M353 172l5 21 1 2-1-1c-1 0-1 1-1 2v1h-1c0-1 0-1-1-2l-3-8c1-1 1-2 1-2l1-1-2-9c0-1 1-2 1-3z"></path><path d="M354 184c2 3 3 8 3 12v1h-1c0-1 0-1-1-2l-3-8c1-1 1-2 1-2l1-1zm5 8s0 1 1 1v1h0l1 2c1 3 3 7 3 10s2 4 4 6h0c2 3 3 6 4 8-2 0-3-1-4-2 0-1-1-3-2-4-1 0-1 0-2-1l1-1-4-8c-1-2-2-5-2-7v-2l-1-2 1-1z" class="V"></path><path d="M365 212l1 2c-1 0-1 0-2-1l1-1z" class="D"></path><path d="M222 206h0c0-1 1-2 1-2 1-1 2-2 2-4h0c1-1 1-1 1-2s0-1 1-2h0c0-1 0-1 1-2h0v-1s1-1 1-2l1-2c0-1 0-1 1-2-1 4-3 8-4 12l-1 3s0 1 1 1l-1 2c-1 2-1 3-2 4h0v2l1 1c-2 1-4 2-6 4h0-1v-1h0c1-1 1-2 2-3l-1-1v-1h0 1v-4h2z" class="I"></path><path d="M220 210v1h-1v-1h0 1z" class="O"></path><path d="M221 212v-1c1 0 1-1 1-2h1v-2c0-1 1-2 1-3v4 1h0l-3 3z" class="C"></path><path d="M226 202s0 1 1 1l-1 2c-1 2-1 3-2 4v-1-4c1-1 1-2 2-2z" class="N"></path><path d="M224 209v2l1 1c-2 1-4 2-6 4h0-1v-1c2 0 3-2 3-3l3-3z" class="H"></path><path d="M215 201h1v1l-1 2c0 4-2 8-3 11 0 2-1 4-2 6h1l2-2c2 0 4-2 6-3h0 1c-2 2-4 3-6 4s-3 2-4 4h4 0-7s0-1 1-1c0-1 1-1 1-2 1-7 3-14 6-20h0z" class="M"></path><path d="M215 201h1v1l-1 2h0v-3h0z" class="O"></path><path d="M293 139c1 0 1 1 2 2h0c1 2 2 4 2 7 1 2 1 4 2 6 1 0 1 1 1 1 0 2 1 3 2 4v1c0 1 1 2 1 2 0 1 1 1 1 1v-1c1 1 1 2 1 3h-1l-8-10c-1-2-2-5-2-7v-2l-1-7z" class="B"></path><path d="M295 141h0c1 2 2 4 2 7 1 2 1 4 2 6 1 0 1 1 1 1h-1v-1l-2-2c0-1 0-1-1-2v-3c-1-1 0-2 0-3-1-1-1-2-1-3z" class="f"></path><path d="M311 149h0v-1h0v-2-1c0-1 1-2 1-2-1-1 0-1-1-2l-1-1v-1c1-1 0-2 0-3h1s1 0 1 1c1 1 3 10 4 11 0 3 1 8 1 11 0-1 0-1-1-2 0 0-1 0-1 1-1-2-2-3-2-5 0-1-1-2-2-4z" class="R"></path><path d="M381 220h1l1 2c1 1 1 1 1 2h1 5 1 8 0 1v1l-6 1h0-11-3-10c-1 0-2-1-3-1l2-1-3-3v-1h1l4 1h2c1 1 2 2 4 2v1h6c-1-1-1-3-2-4z" class="AZ"></path><path d="M381 220h1l1 2c1 1 1 1 1 2h1 5 1 8 0-23 1 6c-1-1-1-3-2-4z" class="D"></path><path d="M369 224l-3-3v-1h1l4 1h2c1 1 2 2 4 2v1h-1-6-1z" class="B"></path><path d="M373 221c1 1 2 2 4 2v1h-1-6 3c0-2-1-2-2-3h2z" class="G"></path><path d="M257 158c0-1 0-2 1-3v1c-1 2-2 5-2 8v3c0 1-1 3-2 4 1 0 1 1 2 1v-2l1 3-2 3-1 3-2 1-2-2h0-1l4-12v-3c1-2 2-5 3-8 0 1 0 2 1 3z" class="X"></path><path d="M253 172l3-8v3c0 1-1 3-2 4l-1 1z" class="f"></path><path d="M256 155c0 1 0 2 1 3l-4 8v-3c1-2 2-5 3-8z" class="P"></path><path d="M256 172v-2l1 3-2 3-1 3-2 1-2-2c2-2 2-4 3-6l1-1c1 0 1 1 2 1z" class="M"></path><path d="M256 172v-2l1 3-2 3c0-1 0-3 1-4z" class="D"></path><path d="M325 153h1c1 1 2 3 2 4 3 4 3 9 5 12 1 1 1 2 1 3 1 0 1 1 1 2 1 0 1 1 2 1v-1-2c1 2 1 4 1 5v1l1 2c-1 0-1 0-1 1h-1-1s0-1-1-1c0-1-1-1-1-1v-1c-1-1-1-1-1-2-1-1-1-1-1-2-1-2-1-4-2-6l-3-6-2-9z" class="F"></path><path d="M334 178h0v-3h0c1 1 2 2 2 3 0 0 0 1-1 2 0-1-1-1-1-1v-1z" class="J"></path><path d="M337 172c1 2 1 4 1 5v1l1 2c-1 0-1 0-1 1h-1c0-2 0-3-1-4 0-1-1-2-1-3 1 0 1 1 2 1v-1-2z" class="R"></path><path d="M325 153h1c1 1 2 3 2 4 0 2 0 3 1 5l1 3c1 1 1 2 1 3 1 2 2 5 2 7v1c-1-1-1-1-1-2-1-2-1-4-2-6l-3-6-2-9z" class="o"></path><path d="M339 180c1 1 1 4 2 6l5 10v3c1 2 3 4 3 5-1 0-2-1-3-2-2-1-3-2-5-3l1-1c-1-1-1-2-2-3-1-3-2-8-4-10l1-1-1-1v-2h1 1c0-1 0-1 1-1z" class="M"></path><path d="M339 180c1 1 1 4 2 6l-1 1h-1v2l3 6c1 1 1 3 1 4h0l-1-1c-1-1-1-2-2-3-1-3-2-8-4-10l1-1-1-1v-2h1 1c0-1 0-1 1-1z" class="b"></path><path d="M339 180c1 1 1 4 2 6l-1 1h-1v2c0-2 0-3-1-4v-4c0-1 0-1 1-1z" class="J"></path><path d="M249 178h1 0l2 2 2-1h-1c0 1 0 1-1 1v2 1 1 2c-1 2-4 6-3 8v1l-1 1-2 1h-1v1h-2v-1l2-5c1-1 1-4 1-6h-1l1-1v-3c1 0 1-1 2-2l1-2z" class="e"></path><path d="M250 178l2 2c-1 0-1 1-2 1h0c-1-1 0-2 0-3h0z" class="R"></path><path d="M246 185c1-1 3-2 4-3h1v2c-2 1-3 1-5 2h-1l1-1z" class="X"></path><path d="M251 184c0 1-1 2-1 3-1 3-2 7-4 10h0-1v1h-2v-1l2-5c1-1 1-4 1-6 2-1 3-1 5-2z" class="T"></path><path d="M284 140h0v3c1-1 0-1 0-2l1-1c1 1 1 2 1 3v1c0 1-1 2-1 3v2c-1 2-1 3-1 4v5l1 1 1-2h2c-2 3-4 6-6 8-1 0-1 1-1 1l-1-1c-1 2-3 3-3 4l-1-1c1-2 1-5 3-7h0l1-3s1-1 1-2l1-4v-2h0l1-7 1-3z" class="B"></path><path d="M285 144c0 1-1 2 0 3v2s-1 0-1-1 0-3 1-4z" class="K"></path><path d="M285 144v-1l1 1c0 1-1 2-1 3-1-1 0-2 0-3z" class="L"></path><path d="M282 152v1c1 1 0 3 0 5l-1-2-1 3v-1s1-1 1-2l1-4z" class="Z"></path><path d="M285 159l1-2h2c-2 3-4 6-6 8-1 0-1 1-1 1l-1-1c-1 2-3 3-3 4l-1-1c1-2 1-5 3-7h0c0 1-1 4-1 5 2 0 6-6 7-7z" class="k"></path><path d="M282 140v3h1l-1 7h0v2l-1 4c0 1-1 2-1 2l-1 3h0c-2 2-2 5-3 7l1 1-5 5-1 2c0-1-1-2-1-2h1c0-2 1-3 2-4v-1h0c0-1-1-2 0-4v-2c1 0 1-1 1-1v-2h1c0-1 0-2 1-3h0c0-1 0-1 1-1v-1-2h0v-3c1 1 1 3 1 4h0c1-1 1-2 2-3 0-1 0-1 1-1h-1v-1h0v-2-1l1-2v-2l1-2z" class="I"></path><path d="M282 143h1l-1 7-1-1c-1-1 1-5 1-6z" class="J"></path><path d="M276 168l1 1-5 5v-1c0-2 2-4 4-5z" class="R"></path><path d="M281 156c-1 0-1-1-2-1 0-1 1-3 2-4l1-1v2l-1 4z" class="D"></path><path d="M277 169c0-1 2-2 3-4l1 1-3 3c1 0 1 1 2 1l-1 1h-1v1l-1 1c0 1-2 2-2 2 0 1 0 2-1 2s-2 1-2 2h0l-3 3c-2 1-3 2-4 4h-1c0 1-1 2-2 3l-1 1 1-2h-2c-1 2-3 3-5 4 0 0-1 0-1-1l5-4h-1c2-2 4-4 5-6l1-2c1 1 2 1 3 1l1-1 3-3 1-2 5-5z" class="s"></path><path d="M260 188l4-3v1c0 1-1 2-2 3l-1 1 1-2h-2z" class="P"></path><path d="M275 173c1-2 2-3 3-4 1 0 1 1 2 1l-1 1h-1v1c-1 0-2 1-3 1zm-11 6c1 1 2 1 3 1-3 3-5 6-8 7h-1c2-2 4-4 5-6l1-2z" class="F"></path><path d="M275 173c1 0 2-1 3-1l-1 1c0 1-2 2-2 2 0 1 0 2-1 2s-2 1-2 2h0l-3 3c-2 1-3 2-4 4h-1v-1l3-3c2-3 5-6 8-9z" class="k"></path><path d="M327 172c1 2 2 4 4 6v-1h1c1 1 1 2 2 2 0 0 1 0 1 1 1 0 1 1 1 1v2l1 1-1 1c2 2 3 7 4 10h-2v1l-2-2-1 1-2-2c1 0 1 0 2-1l-3-3h0c-1-1-1-1-1-2-1-4-3-8-5-12l1-1v-2z" class="a"></path><path d="M332 189v-1c0-1-1-2-1-2l1-1c1 1 1 2 2 4v-1c1 2 1 3 2 5-1 0-1-1-1-1l-3-3h0z" class="N"></path><path d="M327 172c1 2 2 4 4 6v2 1 1h-1l-3-8v-2z" class="F"></path><path d="M331 177h1c1 1 1 2 2 2 0 0 1 0 1 1 1 0 1 1 1 1v2l1 1-1 1-3-1c-1-1-1-2-2-3v-1-2-1z" class="G"></path><path d="M331 180c2 1 3 2 5 3l1 1-1 1-3-1c-1-1-1-2-2-3v-1z" class="j"></path><path d="M333 184l3 1c2 2 3 7 4 10h-2v1l-2-2-1 1-2-2c1 0 1 0 2-1 0 0 0 1 1 1-1-2-1-3-2-5 0-1-1-2-1-4z" class="T"></path><path d="M335 192s0 1 1 1 1 1 2 2v1l-2-2-1 1-2-2c1 0 1 0 2-1z" class="G"></path><path d="M314 174s1 0 1 1c-1-2-1-2-2-2v-2h0v-3h0 0v-2c-1 0-1 0-1-1s0-1-1-2c0-1 1-1 0-2v-2-1c-1-2-1-4-2-6v-2c-1-1-1-3-1-4 0 0 0-1-1-2v-2l-1-2c-1-5-1-9-2-13h1c0 2 1 4 1 6 1 1 1 3 1 4 1 1 2 1 3 2v1c1 1 0 2 1 3h0l-1 1v-1c0-1-1-1-1-2h0v-1h0c0-1-1-1-1-2v4h1v2 1l1 1h-1l1 2v1h0c0 1 0 2 1 3v-1h0v-2c1 2 2 3 2 4h-2 0v3h0c1 2 1 3 2 5 0 1 0 2 1 4l1 4 1 3 1-1c1 1 2 3 2 5 1 0 1 1 1 2s1 3 2 5v2l-3-3c-1-1 0-1-1-3l-1-1c-1 0-1 0-1-1h-1c0-1-1-2-2-3h1z" class="O"></path><path d="M316 172l1-1c1 1 2 3 2 5 1 0 1 1 1 2h-2c0-2-1-4-2-6h0z" class="L"></path><path d="M244 175c1-1 1-2 1-3h0l1-1v4 4h0v3h0v3l-1 1h1c0 2 0 5-1 6l-2 5v1h2c-2 1-3 2-5 3v-2h-1-1c0 1-1 1-2 1-1 1-1 2-1 3l-2 2v-1c0-2 1-3 2-4v-4c1-1 2-1 2-2s1-1 2-2c1-2 3-5 3-8v-1c1-3 2-6 2-8z" class="i"></path><path d="M235 196c1-1 2-1 2-2s1-1 2-2c-1 3-3 5-4 8v-4z" class="V"></path><path d="M246 179h0v3h0v3l-1 1c-1 1-1 1-2 1 0-2 2-4 2-6v-2h1z" class="J"></path><path d="M243 187c1 0 1 0 2-1h1c0 2 0 5-1 6l-2 5v1h2c-2 1-3 2-5 3v-2h-1-1c0 1-1 1-2 1l7-13z" class="Q"></path><path d="M240 195l1 1c0 1-1 2-1 3h-1-1l1-2c0-1 1-1 1-2z" class="a"></path><path d="M240 195c1-2 2-3 3-5l1-3h0c0 2 0 4-1 5s-1 2-2 3v1h0l-1-1z" class="N"></path><path d="M243 192h2l-2 5v1h2c-2 1-3 2-5 3v-2c0-1 1-2 1-3h0v-1c1-1 1-2 2-3z" class="J"></path><path d="M241 195l1 1h0-1v-1z" class="F"></path><path d="M301 140l1 1h-1c0 1 1 1 1 1h0v-1l1-1v2-3h0v-3h0l1 6c0 2 2 3 2 5v2c0 1 1 2 1 3 1 1 0 4 1 5l1 3c0 1 0 2 1 3v2c1 1 1 1 1 2v1h-1v1c2 1 2 4 4 5h-1c1 1 2 2 2 3h1c0 1 0 1 1 1l1 1-1 1-10-12-3-3h1c0-1 0-2-1-3l-1-6c-1-2-1-4-2-6l1-1h0v-3c0-2-1-4-1-6z" class="S"></path><path d="M301 150h3c0 1-1 1-1 2s0 1 1 2c0 0 0 2 1 3l-1 1v-2h-1c-1-2-1-4-2-6z" class="C"></path><path d="M306 157l-1-1c1-1 1-2 2-2v3l3 9v1 1c-3-3-2-8-4-11z" class="O"></path><path d="M303 156h1v2l1-1h1c2 3 1 8 4 11v-1-1-1c1 1 1 1 1 2v1h-1v1c2 1 2 4 4 5h-1c1 1 2 2 2 3h1c0 1 0 1 1 1l1 1-1 1-10-12-3-3h1c0-1 0-2-1-3l-1-6z" class="J"></path><path d="M306 157c2 3 1 8 4 11v-1-1-1c1 1 1 1 1 2v1h-1v1c2 1 2 4 4 5h-1l-1-2c-2-2-5-4-5-6l-3-8 1-1h1z" class="U"></path><defs><linearGradient id="y" x1="374.988" y1="215.58" x2="351.354" y2="202.054" xlink:href="#B"><stop offset="0" stop-color="#443931"></stop><stop offset="1" stop-color="#57514b"></stop></linearGradient></defs><path fill="url(#y)" d="M357 196c0-1 0-2 1-2l1 1v2c0 2 1 5 2 7l4 8-1 1c1 1 1 1 2 1 1 1 2 3 2 4 1 1 2 2 4 2l1 1h-2l-4-1-4-2-3-2c-1-1-4-2-5-4h0c-3-2-6-4-8-6v-1h2c1 1 2 2 3 2h1c3 1 5 3 7 5-2-5-5-11-6-16l1-1c1 1 1 1 1 2h1v-1z"></path><path d="M357 196c0-1 0-2 1-2l1 1v2 1c-1 0-1 1-1 2h-1v-1c0-1-1-1-1-2h1v-1z" class="J"></path><path d="M368 218c-2-1-4-2-5-4v-1h1c1 1 1 1 2 1 1 1 2 3 2 4z" class="I"></path><path d="M260 188h2l-1 2-2 2c0 2 0 4-2 5 0 1-1 2 0 2v1l-1 1-7 5-3 2h-2v-2l-2 1v1c-1-1-2 0-2-1v-1h-1v-1c1 0 2-2 3-2 1-1 1-1 2-1 0-1 0-1 1-1 0 0 0-1 1-1l-1-1h1-1v-2h1l2-1 1-1 5-4c0 1 1 1 1 1 2-1 4-2 5-4z" class="h"></path><path d="M251 199l4-3v2c-1 3-6 4-7 7 0 1 0 1 1 1l-3 2h-2v-2c3-2 5-5 7-7z" class="o"></path><path d="M260 188h2l-1 2-2 2c-1 1-2 3-4 4l-4 3h-1l4-5c0-1 1-1 1-2 2-1 4-2 5-4z" class="O"></path><path d="M254 191c0 1 1 1 1 1 0 1-1 1-1 2l-4 5h1c-2 2-4 5-7 7l-2 1v1c-1-1-2 0-2-1v-1h-1v-1c1 0 2-2 3-2 1-1 1-1 2-1 0-1 0-1 1-1 0 0 0-1 1-1l-1-1h1-1v-2h1l2-1 1-1 5-4z" class="D"></path><path d="M248 196l1 1c-1 1-2 2-3 2h-1v-2h1l2-1z" class="Y"></path><path d="M254 191c0 1 1 1 1 1 0 1-1 1-1 2-3 0-4 1-5 3l-1-1 1-1 5-4z" class="j"></path><path d="M250 199h1c-2 2-4 5-7 7l-2 1v1c-1-1-2 0-2-1v-1l3-3c2 0 4-2 5-3 0 0 1-1 2-1z" class="G"></path><path d="M316 172l-1-3-1-4c-1-2-1-3-1-4-1-2-1-3-2-5h0v-3h0 2c0 2 1 3 2 5l1 2v1l1 2v1h0c0 1 1 3 1 4v1l1 1v1c1 1 1 2 2 3v1l1 1v1 1l1 1c0 1 0 1 1 2v-2c-1-1 0-2-1-3 0 0-1-1-1-2 1 1 1 1 1 2 1 0 1 1 1 1h1l-1-1 1-1c0-1-1-2-1-3s-1-2-1-3l-1-1v-1c-1-1-1-2-1-2h0v-2l3 9c1 1 1 2 2 3 2 4 4 8 5 12 0 1 0 1 1 2h0l-1 1-2 1-2-2-5-4v-2c-1-2-2-4-2-5s0-2-1-2c0-2-1-4-2-5l-1 1z" class="B"></path><path d="M322 183c1 0 1 1 2 1h1c1 1 1 2 1 2 1 1 1 2 1 3l-5-4v-2z" class="S"></path><path d="M264 126l16-14 9-9c1-1 2-3 3-3l38 37 1 1-1 1c-5-6-12-12-19-18-6-5-12-11-19-17-4 3-8 7-12 11-9 7-18 15-26 23h0c1-1 2-3 3-4l7-8z" class="q"></path><path d="M279 171s1 0 1 1 0 2 1 3h1 1c0 1-1 2-1 3l-1 1v2l-1 1h0l-2 2-1 2c0 1 0 1-2 2h0c-1 2-3 2-5 4v-1h-1l-4 3c-1 1-2 3-4 3l-4 3v-1c-1 0 0-1 0-2 2-1 2-3 2-5l2-2 1-1c1-1 2-2 2-3h1c1-2 2-3 4-4l3-3h0c0-1 1-2 2-2s1-1 1-2c0 0 2-1 2-2l1-1v-1h1z" class="r"></path><path d="M273 186l-1 1v-1c0-2 3-4 4-5v1c0 1-1 1-1 2-1 1-2 1-2 2zm-9 0h1v1h3 0l-2 3v-1c-1 1-2 1-3 1l-1-1c1-1 2-2 2-3z" class="n"></path><path d="M275 184h1v2h1c0 1 0 1-2 2h0c-1 2-3 2-5 4v-1h-1 0c1-2 3-2 4-4h1l-1-1c0-1 1-1 2-2z" class="W"></path><path d="M275 184h1v2c-1 0-1 1-2 1l-1-1c0-1 1-1 2-2z" class="y"></path><path d="M262 189l1 1c1 0 2 0 3-1v1c-3 2-6 4-7 7v1l2-1-4 3v-1c-1 0 0-1 0-2 2-1 2-3 2-5l2-2 1-1z" class="s"></path><path d="M279 171s1 0 1 1 0 2 1 3h1 1c0 1-1 2-1 3l-1 1v2l-1 1h0l-2 2-1 2h-1v-2h-1c0-1 1-1 1-2v-1c1-1 1-3 2-4-1-2 0-2 0-4h0-1l1-1v-1h1z" class="l"></path><path d="M278 180v-1l2-2 1 1v1 2l-1 1h0c-1-1-1-2-2-2z" class="s"></path><path d="M279 171s1 0 1 1 0 2 1 3h1c-1 1-1 2-2 2l-1-1c0-1 0-1 1-2v-1s-1 0-1-1l-1-1h1z" class="c"></path><path d="M278 180c1 0 1 1 2 2l-2 2-1 2h-1v-2h-1c0-1 1-1 1-2l2-2z" class="q"></path><path d="M279 108h1l12-12c6 5 12 12 18 18l15 16c0-1 1-2 1-3 1 1 2 1 2 3 0 1 0 0 1 1v1l1 1c1 0 2 1 2 2h-1-1v2l-38-37c-1 0-2 2-3 3l-9 9-16 14-1-1 3-3 13-14z" class="Ac"></path><path d="M326 127c1 1 2 1 2 3 0 1 0 0 1 1v1l1 1c1 0 2 1 2 2h-1-1c-1-2-3-3-5-5 0-1 1-2 1-3z" class="s"></path><path d="M245 197v2h1-1l1 1c-1 0-1 1-1 1-1 0-1 0-1 1-1 0-1 0-2 1-1 0-2 2-3 2v1h1v1c0 1 1 0 2 1v-1l2-1v2h2 0c-1 1-1 1-2 1l-1 1-6 4c-1 1-2 2-2 3l-1 2c-2 1-4 3-6 4 6-2 12-6 17-9 1 1 1 1 2 1l-1 1h0l-1 1c0 1 0 1 1 1 0 1 0 1 1 2l3 3v1h-3-3-6l-1 2-2-2h-5-16 0-4c1-2 2-3 4-4s4-2 6-4h-1c2-2 4-3 6-4l6-5 2-3v1l2-2c0-1 0-2 1-3 1 0 2 0 2-1h1 1v2c2-1 3-2 5-3v-1z" class="H"></path><path d="M225 215c1-1 2-2 4-3l1 1-4 3-1-1z" class="R"></path><path d="M224 215h1l1 1c-1 2-6 3-8 4v-1c1-2 4-2 6-3v-1z" class="M"></path><path d="M244 206v2h2 0c-1 1-1 1-2 1l-1 1-6 4s0-1 1-1h0l2-2c-2 1-4 2-6 2 1-1 2-2 4-3h0 0l1-1 1-1c-1 0-3 1-4 1l4-2c0 1 1 0 2 1v-1l2-1z" class="I"></path><path d="M244 206v2h2 0c-1 1-1 1-2 1h0c-2-1-4 1-5 2l3-3v-1l2-1z" class="Q"></path><path d="M244 206v2h-1l-1-1 2-1z" class="D"></path><path d="M245 197v2h1-1l1 1c-1 0-1 1-1 1-1 0-1 0-1 1-1 0-1 0-2 1-1 0-2 2-3 2v1h1v1l-4 2c-2 2-4 3-6 4l-1-1c-2 1-3 2-4 3h-1l-1-1-3 2h-1c2-2 4-3 6-4l6-5 2-3v1l2-2c0-1 0-2 1-3 1 0 2 0 2-1h1 1v2c2-1 3-2 5-3v-1z" class="J"></path><path d="M245 197v2h1-1c-2 2-4 3-6 4-1 1-3 2-4 3h-1c2-2 4-4 6-5s3-2 5-3v-1z" class="l"></path><path d="M240 199v2c-2 1-4 3-6 5-1 0-2 1-3 1l2-3v1l2-2c0-1 0-2 1-3 1 0 2 0 2-1h1 1z" class="O"></path><path d="M238 199h1c-1 2-2 3-4 4 0-1 0-2 1-3 1 0 2 0 2-1z" class="D"></path><path d="M231 207c1 0 2-1 3-1h1l-3 3v1l-3 2c-2 1-3 2-4 3h-1l-1-1-3 2h-1c2-2 4-3 6-4l6-5z" class="i"></path><path d="M232 209v1l-3 2c-2 1-3 2-4 3h-1l-1-1c2-1 4-3 6-4 1 0 2-1 3-1z" class="K"></path><path d="M296 155l8 10 3 3 10 12 1-1c1 2 0 2 1 3l3 3 5 4 2 2 2-1 1-1 3 3c-1 1-1 1-2 1l2 2v1c-2-1-2-2-4-2h0c1 1 2 2 2 3s3 3 3 3v1l-1 1h0l2 2v1l-1 1-2-2-1 1-1-1v-1c-1-1-2-1-3-2l-1-1c-1-1-2-1-3-2l-2-2c-2 0-2 0-3-1h-1c1 1 1 2 2 2l-1 1c-2-2-5-4-8-6l-3-3c-3-2-4-4-6-6h0c-1-1-2-2-2-3l-1-2h0l2-2-1-1h1 1s1 0 1-1l1 2 2 1c0-1 1-1 1-1h1c0 1 1 1 2 1h0 1l-16-21v-1z" class="j"></path><path d="M317 184l2 2h-3l-1-2h2z" class="AT"></path><path d="M319 186l1 1-1 1-1 1c-1-1-1-2-2-3h3z" class="W"></path><path d="M332 189l3 3c-1 1-1 1-2 1l-4-2 2-1 1-1z" class="S"></path><path d="M309 176c0 1 1 1 2 1h0c1 3 4 4 6 7h-2l-3-3-2-3h0l-1-2z" class="W"></path><path d="M307 177c0-1 1-1 1-1h1l1 2h0l2 3-1 1c1 1 2 2 2 3l-2-1c0-1-1-2-2-2l-1-2c0-1-1-2-1-3z" class="AT"></path><path d="M310 178l2 3-1 1c0-1-1-2-2-2l1-2z" class="w"></path><path d="M312 181l3 3 1 2c1 1 1 2 2 3 2 1 3 2 4 4 1 1 3 3 3 4v1l-2-2-1-1c-2-4-6-6-9-10 0-1-1-2-2-3l1-1z" class="AN"></path><path d="M311 184l2 1c3 4 7 6 9 10l1 1c-2 0-2 0-3-1h-1c-1 0-2-1-3-2l1-1c-2-2-4-5-6-8z" class="n"></path><path d="M316 193l1-1c2 1 3 2 4 3h1 0l1 1c-2 0-2 0-3-1h-1c-1 0-2-1-3-2z" class="h"></path><path d="M308 180l1 2c1 0 2 1 2 2 2 3 4 6 6 8l-1 1c-1-1-2-1-3-2-2-2-3-5-4-6l-2-2c1-1 1-2 1-3z" class="X"></path><path d="M308 180l1 2v3l-2-2c1-1 1-2 1-3z" class="Y"></path><path d="M330 198c-4-4-7-8-10-12 4 3 9 8 13 11 0 1 3 3 3 3v1l-1 1h0l2 2v1l-1 1-2-2-1 1-1-1v-1c-1-1-2-1-3-2l1-1v-2z" class="D"></path><path d="M330 198l1 1c1 1 3 2 4 3h0l2 2v1l-1 1-2-2-1 1-1-1v-1c-1-1-2-1-3-2l1-1v-2z" class="t"></path><path d="M333 203l1 1-1 1-1-1v-1h1z" class="m"></path><path d="M330 198l1 1v1c1 0 1 1 3 2l-1 1h-1c-1-1-2-1-3-2l1-1v-2z" class="d"></path><path d="M304 174l1 2 2 1c0 1 1 2 1 3s0 2-1 3l2 2c1 1 2 4 4 6 1 1 2 1 3 2s2 2 3 2c1 1 1 2 2 2l-1 1c-2-2-5-4-8-6l-3-3c-3-2-4-4-6-6h0c-1-1-2-2-2-3l-1-2h0l2-2-1-1h1 1s1 0 1-1z" class="q"></path><path d="M303 180h1c0 1 3 4 3 5 1 1 0 2 1 2l1 2c-3-2-4-4-6-6v-3z" class="t"></path><path d="M304 174l1 2 2 1c0 1 1 2 1 3s0 2-1 3l-2-3h-1-1v3h0c-1-1-2-2-2-3l-1-2h0l2-2-1-1h1 1s1 0 1-1z" class="j"></path><path d="M304 174l1 2h0c1 1 1 2 1 2 0 1 0 1-1 2 0-2-2-3-2-5 0 0 1 0 1-1z" class="N"></path><path d="M305 176l2 1c0 1 1 2 1 3s0 2-1 3l-2-3c1-1 1-1 1-2 0 0 0-1-1-2h0z" class="c"></path><path d="M300 178h0l2-2 1 4v3h0c-1-1-2-2-2-3l-1-2z" class="p"></path><path d="M288 157c1-2 3-4 4-6 1 2 2 3 3 5h1l16 21h-1 0c-1 0-2 0-2-1h-1s-1 0-1 1l-2-1-1-2c0 1-1 1-1 1h-1-1l1 1-2 2h0l1 2h-1c-1 0-1-1-2-1-1-2-1-2-3-2l-2-4h-1l-3 4c-1 1-3 4-5 5l-2 3-2-1v-2h0l1-1v-2l1-1c0-1 1-2 1-3h-1-1c-1-1-1-2-1-3s-1-1-1-1l1-1c-1 0-1-1-2-1l3-3s0-1 1-1c2-2 4-5 6-8z" class="K"></path><path d="M280 170c1-1 2-1 3-2l-1 3-2-1z" class="k"></path><path d="M305 176c0-1 1-2 1-2l1-1 4 4c-1 0-2 0-2-1h-1s-1 0-1 1l-2-1z" class="g"></path><path d="M283 168l6-7 1 1h-1c-1 2-3 4-4 5s-1 2-2 3v2h2l-2 3h-1-1c-1-1-1-2-1-3s-1-1-1-1l1-1h0l2 1 1-3z" class="x"></path><path d="M280 170h0l2 1v2h-1c0-1 0-1-1-1 0-1-1-1-1-1l1-1z" class="N"></path><path d="M294 160l6 6c2 2 3 5 6 6h0l1 1-1 1s-1 1-1 2l-1-2c0 1-1 1-1 1h-1c0-2-1-2-1-3l-1-1c0-1 0-1-1-2-1-2-2-4-4-6h-1l-2-2h1l1-1h0z" class="j"></path><path d="M295 163h0c4 3 7 7 9 11 0 1-1 1-1 1h-1c0-2-1-2-1-3l-1-1c0-1 0-1-1-2-1-2-2-4-4-6z" class="D"></path><path d="M289 161c1-1 1-3 3-3 1 0 2 1 2 2h0l-1 1h-1l2 2h1c2 2 3 4 4 6 0 1 0 1-1 1-1-2-2-3-3-5h0-1l1 2h-1 0-1l-1 1c-1-1-1-1-2-1l-2 3-3 2h-2v-2c1-1 1-2 2-3s3-3 4-5h1l-1-1z" class="i"></path><path d="M289 161c1-1 1-3 3-3 1 0 2 1 2 2h0l-1 1h-1l2 2h1c2 2 3 4 4 6 0 1 0 1-1 1-1-2-2-3-3-5h0-1l1 2h-1 0c-1-1-1-2-2-2v2h-1c0-1-1-2-2-2h0c0-1 2-2 2-3s1-1 0-2c0 0-1 1-1 2l-1-1z" class="w"></path><path d="M294 167h1l-1-2h1 0c1 2 2 3 3 5 1 0 1 0 1-1 1 1 1 1 1 2l1 1c0 1 1 1 1 3h-1l1 1-2 2h0l1 2h-1c-1 0-1-1-2-1-1-2-1-2-3-2l-2-4h-1l-3 4c-1 1-3 4-5 5l-2 3-2-1v-2h0l1-1v-2l1-1c0-1 1-2 1-3l2-3 3-2 2-3c1 0 1 0 2 1l1-1h1 0z" class="B"></path><path d="M288 170v2c-2 2-3 4-5 6h-1c0-1 1-2 1-3l2-3 3-2z" class="R"></path><path d="M294 167h0l1 2h-2c0 1 0 1-1 1l-2 1c-1 0-1 0-2 1v-2l2-3c1 0 1 0 2 1l1-1h1zm-10 13c1-1 2-2 3-4 2-1 3-2 5-3l-3 4c-1 1-3 4-5 5l-2 3-2-1v-2h0l1-1v-2l1-1h1l-1 2h1 0 1z" class="M"></path><path d="M280 182h0l1 1c1 0 1 0 2-1h1l-2 3-2-1v-2z" class="C"></path><path d="M282 178h1l-1 2h1 0 1l-2 2h-1v-1-2l1-1z" class="L"></path><path d="M294 167h1l-1-2h1 0c1 2 2 3 3 5 1 0 1 0 1-1 1 1 1 1 1 2l1 1c0 1 1 1 1 3h-1l1 1-2 2h0l-1-2c-1-1-2-3-2-4-1-1-1-2-2-3l-1-2z" class="Q"></path><path d="M301 172c0 1 1 1 1 3h-1l1 1-2 2h0l-1-2 1-1 1 1h0c0-1 0-2-1-3h0l1-1z" class="k"></path><path d="M312 192c3 2 6 4 8 6l1-1c-1 0-1-1-2-2h1c1 1 1 1 3 1l2 2c1 1 2 1 3 2l1 1c1 1 2 1 3 2v1l1 1 1-1 2 2 1-1v-1l-2-2h0l1-1v-1s-3-2-3-3-1-2-2-3h0c2 0 2 1 4 2v-1l1-1 2 2v-1h2c1 1 1 2 2 3l-1 1c2 1 3 2 5 3 1 1 2 2 3 2l1 1c1 0 2 1 3 2h-1c-1 0-2-1-3-2h-2v1c2 2 5 4 8 6h0c1 2 4 3 5 4l3 2 4 2h-1v1l3 3-2 1h0-1v-1h-1c-1-1-5 0-6 0v-1c-1-1-1-2-3-3h0 0c0 1 1 2 1 2h0-3c0 1-1 1-1 1l-1-1-20-12c-3-3-7-6-11-8-2-2-5-3-7-6 0-1-1-1-1-2l-2-1 1-1z" class="G"></path><path d="M331 204h1l1 1v1h-1c-1 0-1 0-2-1 0 0 0-1 1-1z" class="b"></path><path d="M333 205l1-1 2 2c0 1 1 1 2 3 1 1 3 2 4 3s4 2 5 4h-1c-2-1-4-3-6-4l-8-6h1v-1z" class="o"></path><path d="M319 195h1c1 1 1 1 3 1l2 2c1 1 2 1 3 2l1 1c1 1 2 1 3 2v1h-1c-1 0-1 1-1 1-3-2-7-4-10-7l1-1c-1 0-1-1-2-2z" class="X"></path><path d="M327 201l1-1 1 1c1 1 2 1 3 2v1h-1l-4-3z" class="a"></path><path d="M319 195h1c1 1 1 1 3 1l2 2c1 1 2 1 3 2l-1 1c-2 0-4-3-6-4-1 0-1-1-2-2z" class="b"></path><path d="M313 194l15 11c4 3 22 14 24 17h0l-20-12c-3-3-7-6-11-8-2-2-5-3-7-6 0-1-1-1-1-2z" class="h"></path><path d="M347 209l-4-3h0l7 4c1 1 1 1 2 1l3 1c1 2 4 3 5 4l3 2 4 2h-1v1l3 3-2 1h0-1v-1h-1c-1-1-5 0-6 0v-1c-1-1-1-2-3-3h0c-1-1-2-1-3-2-1 0-2-1-4-2 0 0-1-1-1-2l-3-3c1 0 2 0 3 1l1-1-2-2z" class="T"></path><path d="M363 218l4 2h-1v1l3 3-2 1h0-1v-1h1c1-2-4-4-4-6z" class="C"></path><path d="M347 209l-4-3h0l7 4c1 1 1 1 2 1l3 1c1 2 4 3 5 4l-1 1c1 0 3 1 3 2h0-1-1c-4-1-8-4-11-6-2 0-2-1-3-2 1 2 3 3 4 4l3 3c-1 0-2-1-4-2 0 0-1-1-1-2l-3-3c1 0 2 0 3 1l1-1-2-2z" class="S"></path><path d="M333 197c0-1-1-2-2-3h0c2 0 2 1 4 2v-1l1-1 2 2v-1h2c1 1 1 2 2 3l-1 1c2 1 3 2 5 3 1 1 2 2 3 2l1 1c1 0 2 1 3 2h-1c-1 0-2-1-3-2h-2v1c2 2 5 4 8 6h0l-3-1c-1 0-1 0-2-1l-7-4h0l4 3h-1l1 1-1 1c-1-1-3-1-4-3h-2-1 0l-1 1c-1-2-2-2-2-3l1-1v-1l-2-2h0l1-1v-1s-3-2-3-3z" class="X"></path><path d="M340 195c1 1 1 2 2 3l-1 1-3-3v-1h2z" class="L"></path><path d="M347 206c-2-1-3-2-5-4-2-1-4-2-5-5 1 1 4 4 6 5h0c2 0 4 2 4 3v1z" class="F"></path><path d="M336 201v-1l9 6c2 2 5 3 7 5-1 0-1 0-2-1l-7-4h0l4 3h-1l1 1-1 1c-1-1-3-1-4-3h-2-1 0l-1 1c-1-2-2-2-2-3l1-1v-1l-2-2h0l1-1z" class="M"></path><path d="M340 205c3 1 4 3 6 4l1 1-1 1c-1-1-3-1-4-3v-1c-1 0-1-1-2-2z" class="D"></path><path d="M335 202l1-1 4 4c1 1 1 2 2 2v1h-2-1 0l-1 1c-1-2-2-2-2-3l1-1v-1l-2-2h0z" class="m"></path><path d="M289 177h1c-1 1-1 2-2 3h1v2h-1v1l2 2v3c0-1 0-2 1-3l2 1 1 1h0c1 2 1 2 1 4h1 0v2c-1 1-1 2-2 3l-1-1h-1l-2 2 2 2v1h-1v1l1 1-12 15 1 1-6 6h0v1c-2 0-3 1-4 1h-1l10 17v1c0 1-1 1-1 2l-18-6c-2-1-5-1-8-2v1l-3-1-2-1h-1v1h-1c0 1 0 1-1 2l-1-2-1-1v-1c-1-2-3-4-5-6l1-1-1-2c-1 0-1-1-1-1l1-2h6 3 3v-1l-3-3c-1-1-1-1-1-2-1 0-1 0-1-1l1-1h0l1-1c-1 0-1 0-2-1-5 3-11 7-17 9 2-1 4-3 6-4l1-2c0-1 1-2 2-3l6-4 1-1c1 0 1 0 2-1h0l3-2 7-5 1-1 4-3c2 0 3-2 4-3l4-3h1v1c2-2 4-2 5-4h0c2-1 2-1 2-2l1-2 2-2v2l2 1 2-3c2-1 4-4 5-5z" class="y"></path><path d="M271 209h1v1h-1v-1z" class="r"></path><path d="M260 209c1-1 0-2 1-2h1c1 0 1 1 1 2l2 1v-1 2h0s-1 0-1 1h0l1 1 1 1s0 1 1 2c1 2 2 6 3 9h0c-2-3-2-5-4-8 0-1-1-2-2-3 0-2-3-3-4-5z" class="R"></path><path d="M272 199l8-3h1c1 1 2 1 3 2s1 3 2 4c0 0 1 1 0 2l-1 1-1-1h0c0-1 0-1-1-2-3-1-5-2-8 0-1 0-3 1-4 2s-2 4-3 5v2l-1 1h0l2 2c0 1 0 1-1 1 0 0-1 0-1 1-1-1-1-2-1-2l-1-1-1-1h0c0-1 1-1 1-1h0v-2c1-1 1-5 2-6 0-1 1-1 2-2s2-2 3-2z" class="F"></path><path d="M291 201l1 1-12 15 1 1-6 6h0v1c-2 0-3 1-4 1v-2h0v-2-2c1-1 2-1 3-2v-1h-1-1c-1 1-2 1-2 0-1 0-1-1-1-1l2-2v-3c3 0 2-2 4-4 1-1 2-2 4-2 1 0 4 0 5 1h0 1v-1l1-1 1 1 4-4z" class="w"></path><path d="M285 205l1-1 1 1-3 3c-1-1-3-2-4-2h-1-1c-2 1-4 4-5 6h-1-1v2l2 1c-1-1-1-1-1-2l2 2c-1 0-1 1-2 0h-1c-1 1-1 1-1 2-1 0-1-1-1-1l2-2v-3c3 0 2-2 4-4 1-1 2-2 4-2 1 0 4 0 5 1h0 1v-1z" class="AT"></path><path d="M291 201l1 1-12 15c-1 1-3 2-4 4-1 1-2 3-3 3h-1l12-16 3-3 4-4z" class="L"></path><path d="M258 207h1l1 1v1c1 2 4 3 4 5 1 1 2 2 2 3s-1 1-1 2c1 1 3 4 2 5h-2-18 3v-1l-3-3c-1-1-1-1-1-2-1 0-1 0-1-1l1-1h0l1-1h3v-1c1-2 3-2 5-3v-1l3-2v-1z" class="I"></path><path d="M261 224l1-2h0l1 2h-2z" class="P"></path><path d="M258 207h1l1 1h-1v2 4 1c0-1-1-2-1-2l-1-1s0-1 1-2v-2-1z" class="F"></path><path d="M259 216c1 0 2-1 2 0 1 1 1 2 1 4l1 1-2-1c-1 0-2-1-2-2-1-1 0-1 0-2z" class="J"></path><path d="M260 208v1c1 2 4 3 4 5 1 1 2 2 2 3s-1 1-1 2c1 1 3 4 2 5h-2-18 3 8 3 2 2v-1l-2-2-1-1c0-2 0-3-1-4 0-1-1 0-2 0v-1-1-4-2h1z" class="D"></path><path d="M263 216c0-1 1-1 1-2 1 1 2 2 2 3s-1 1-1 2c-1-1-2-3-2-3z" class="g"></path><path d="M260 208v1c1 2 4 3 4 5 0 1-1 1-1 2l-4-6v-2h1z" class="q"></path><path d="M255 211v-1 3l-1 1c-1 1-2 2-2 3 1 1 1 2 2 3l4 3v1h-8v-1l-3-3c-1-1-1-1-1-2-1 0-1 0-1-1l1-1h0l1-1h3v-1c1-2 3-2 5-3z" class="U"></path><path d="M250 215l-1 1h0c-1 1-1 1-1 2l-2-2h0l1-1h3z" class="M"></path><path d="M255 211v-1 3l-1 1c-1 1-2 2-2 3v-1c0-1 0-1-1-2h0c2-1 3-2 4-3z" class="V"></path><path d="M246 216l2 2c1 2 2 3 3 5h-1l-3-3c-1-1-1-1-1-2-1 0-1 0-1-1l1-1z" class="W"></path><path d="M288 183l2 2v3c0-1 0-2 1-3l2 1 1 1h0c1 2 1 2 1 4h1 0v2c-1 1-1 2-2 3l-1-1h-1l-2 2 2 2v1h-1v1l-4 4-1-1c1-1 0-2 0-2-1-1-1-3-2-4s-2-1-3-2h-1c-2 1-5 2-8 3-1 0-2 1-3 2s-2 1-2 2c-1 1-1 5-2 6v1l-2-1c0-1 0-2-1-2h-1c-1 0 0 1-1 2v-1l-1-1h-1v1l-1-1h-1c2-2 5-4 7-5l16-13c3 0 4-2 6-4h2c0-1 0-1 1-2h0z" class="T"></path><path d="M263 202c0 1-1 2-1 2 0 1-1 1-2 2h2 1v3c0-1 0-2-1-2h-1c-1 0 0 1-1 2v-1l-1-1h-1v1l-1-1h-1c2-2 5-4 7-5z" class="f"></path><path d="M280 193c2-2 5-5 9-6-1 1-1 2-2 3s-1 1-1 2h0c-2 0-4 1-6 1z" class="I"></path><path d="M280 193c2 0 4-1 6-1-1 1-3 3-5 4h-1c-2 1-5 2-8 3 3-2 5-4 8-6z" class="J"></path><path d="M290 185v3 2c0 2 0 4 1 5h1l-2 2c-1-2-1-2-3-3v-2h-1c0-1 0-1 1-2s1-2 2-3h1v-2z" class="N"></path><path d="M290 185v3 2c-1 1-1 1-2 1l-1-1c1-1 1-2 2-3h1v-2z" class="P"></path><path d="M290 188c0-1 0-2 1-3l2 1 1 1h0c1 2 1 2 1 4h1 0v2c-1 1-1 2-2 3l-1-1h-1-1c-1-1-1-3-1-5v-2z" class="w"></path><path d="M292 188l2 2h-1c0 1 0 1-1 1v-1-2z" class="AN"></path><path d="M294 193l1-2h1 0v2c-1 1-1 2-2 3l-1-1 1-2z" class="N"></path><path d="M294 193l1-2h1 0v1l-1 1h-1z" class="F"></path><path d="M286 192h1v2c2 1 2 1 3 3l2 2v1h-1v1l-4 4-1-1c1-1 0-2 0-2-1-1-1-3-2-4s-2-1-3-2c2-1 4-3 5-4h0z" class="AN"></path><path d="M286 197c-1 0-2 0-2-1h0 3v2l-1-1z" class="w"></path><path d="M286 197l1 1h0c2 0 3 1 5 1v1h-1-1c-1 0-1 1-2 1h-1c0-1 0-2-1-3v-1z" class="Ae"></path><path d="M287 194c2 1 2 1 3 3l2 2c-2 0-3-1-5-1h0v-2-1-1z" class="z"></path><path d="M289 177h1c-1 1-1 2-2 3h1v2h-1v1h0c-1 1-1 1-1 2h-2c-2 2-3 4-6 4l-16 13c-2 1-5 3-7 5h1l1 1-3 2v1c-2 1-4 1-5 3v1h-3c-1 0-1 0-2-1-5 3-11 7-17 9 2-1 4-3 6-4l1-2c0-1 1-2 2-3l6-4 1-1c1 0 1 0 2-1h0l3-2 7-5 1-1 4-3c2 0 3-2 4-3l4-3h1v1c2-2 4-2 5-4h0c2-1 2-1 2-2l1-2 2-2v2l2 1 2-3c2-1 4-4 5-5z" class="c"></path><path d="M280 182v2l2 1c-1 1-3 2-4 3v-2c1-1 0-2 0-2l2-2z" class="p"></path><path d="M278 184s1 1 0 2v2c-1 2-3 4-5 5h-1c2-1 3-2 4-3l-1-2h0c2-1 2-1 2-2l1-2z" class="a"></path><path d="M279 189l9-9h1v2h-1v1h0c-1 1-1 1-1 2h-2c-2 2-3 4-6 4z" class="J"></path><path d="M237 214l6-4 1 2-1 2c-3 1-6 4-9 5l1-2c0-1 1-2 2-3z" class="C"></path><path d="M256 207h1l1 1-3 2v1c-2 1-4 1-5 3v1h-3c-1 0-1 0-2-1l11-7z" class="T"></path><path d="M269 191h1v1c2-2 4-2 5-4l1 2c-1 1-2 2-4 3h1l-30 21 1-2-1-2 1-1c1 0 1 0 2-1h0l3-2 7-5 1-1 4-3c2 0 3-2 4-3l4-3z" class="F"></path><path d="M269 191h1v1c2-2 4-2 5-4l1 2c-1 1-2 2-4 3l-11 8-1-1c2-2 4-3 6-5l-1-1 4-3z" class="X"></path><path d="M265 194l1 1c-2 2-4 3-6 5l1 1c-4 2-8 6-12 8v-1h-1-2 0l3-2 7-5 1-1 4-3c2 0 3-2 4-3z" class="e"></path><path d="M256 201l1 1c-2 2-6 4-9 6h-2 0l3-2 7-5z" class="R"></path><path d="M237 226l1-2h6 3 18 2l1 1c1 0 1 1 2 2v1s0 1 1 1c1 2 2 5 4 8 1 2 3 4 5 7 0 1-1 1-1 2l-18-6c-2-1-5-1-8-2v1l-3-1-2-1h-1v1h-1c0 1 0 1-1 2l-1-2-1-1v-1c-1-2-3-4-5-6l1-1-1-2c-1 0-1-1-1-1z" class="AI"></path><path d="M238 227c2-1 2 0 4 0l1 1c-1 1-2 1-3 2l-1-1-1-2z" class="AC"></path><path d="M261 231l-1 1h0-3c-1 1-1 2-2 3h-2c-1 1-1 2-2 2s-1 1-1 1l-2-1h0c1 0 2 0 2-1h1 0l1-1c1-1 0-2 1-3h2 1c2 0 3 0 5-1z" class="AP"></path><path d="M239 229l1 1c2 2 4 5 7 6v1 1h-1c0 1 0 1-1 2l-1-2-1-1v-1c-1-2-3-4-5-6l1-1zm5-5h3 18v1 2l-3-1c-1 1-3 0-4 0-5-1-13 1-17-1l3-1z" class="AZ"></path><path d="M265 225v2l-3-1c1-1 2-1 3-1z" class="AF"></path><path d="M265 224h2l1 1c1 0 1 1 2 2v1s0 1 1 1c1 2 2 5 4 8 1 2 3 4 5 7 0 1-1 1-1 2l-18-6c-2-1-5-1-8-2v1l-3-1s0-1 1-1 1-1 2-2h2c1-1 1-2 2-3h3 0l1-1v-1h1 0c1 1 2 2 4 2h1 3 0l-1-2-4-3v-2-1z" class="AD"></path><path d="M265 224h2l1 1h0c0 1 0 2 1 2l1 1c-1 1-1 1-1 2l-4-3v-2-1z" class="Aa"></path><path d="M261 231v-1h1 0c1 1 2 2 4 2h1c-2 0-3 1-5-1h0c-1 0-1 1-1 1 0 1 0 1-1 2v1l-1 1v-1h0s1 0 1-1h0v-1h-3s-1 1-1 2h1v1h0c-1-1-1 0-2 0h0l-1 1h-2 0l1 1h0v1l-3-1s0-1 1-1 1-1 2-2h2c1-1 1-2 2-3h3 0l1-1z" class="AU"></path><path d="M292 173h1l2 4c2 0 2 0 3 2 1 0 1 1 2 1h1c0 1 1 2 2 3h0c2 2 3 4 6 6l3 3-1 1 2 1c0 1 1 1 1 2 2 3 5 4 7 6 4 2 8 5 11 8l20 12 1 1s1 0 1-1h3 0s-1-1-1-2h0 0c2 1 2 2 3 3v1c1 0 5-1 6 0h1v1h1 0c1 0 2 1 3 1h10 3 3c0 1-1 1-1 1 0 1-1 2-1 2 1 1 1 2 2 3l-1 2h0c-1 1-1 2-2 3l1 1h0-3l-3 1h0l-4 4-1 3h-1v-1l-1 1c0 1 0 2-1 2l-4-3-9-3-22-3h-2v1c-1 0-1 0-2-1h-7l-20 2c-2 0-5 1-7 1v-1c-1 0-4 1-5 2-1 0-3 1-4 1 0 0-1 1-2 1 0 1 0 1-1 2 0 0-1-1-2-1l-1 1h0v2h-1c-1-1 0-2-2-3 0-1 1-1 1-2v-1l-10-17h1c1 0 2-1 4-1v-1h0l6-6-1-1 12-15-1-1v-1h1v-1l-2-2 2-2h1l1 1c1-1 1-2 2-3v-2h0-1c0-2 0-2-1-4h0l-1-1-2-1c-1 1-1 2-1 3v-3l-2-2v-1h1v-2h-1c1-1 1-2 2-3h-1l3-4z" class="AF"></path><path d="M337 222l-1 1 1 1h1 0-4 0c0-1 1-1 2-2h1z" class="B"></path><path d="M280 243c1 1 2 3 2 4h0v2h-1c-1-1 0-2-2-3 0-1 1-1 1-2v-1z" class="AZ"></path><path d="M284 245v-1c1-1 1-2 2-2h1c1 0 2 0 3-1v1c1 0 2 0 2 1-1 0-3 1-4 1v-1h1-3c-1 0-1 1-2 2z" class="Aa"></path><path d="M366 243c1 1 2 2 4 3h1c0 1 0 2-1 2l-4-3h0v-2z" class="AP"></path><path d="M284 245c1-1 1-2 2-2h3-1v1s-1 1-2 1c0 1 0 1-1 2 0 0-1-1-2-1 0-1 0-1 1-1z" class="AZ"></path><path d="M357 242l2-1c0 1 1 1 2 1s1 1 2 1h0l-1-1s-2-1-2-2c-1-1-1-2-2-3 1 0 2 0 2 1 1 1 1 2 2 3 2 1 2 2 4 2v2h0l-9-3z" class="AG"></path><path d="M367 225c1 0 2 1 3 1h0c-1 0-2 0-3 1h-1c-1-1-2 0-4 0v2c1 0 2 1 3 1 0 1 0 1-1 2 1 1 2 2 2 3l-1 1c-1 0-1 1-1 2s-1 1-1 2h-1c1-1 1-1 1-2s-1-1-2-2h2c1 0 1-1 2-1l-2-2v-1h-2c0 1 0 2-1 3h-1c1-1 1-1 1-2h0v-1h1l-2-1c-1-1-1-2-1-4 1 0 0 2 0 2 0 1 1 1 1 1l2 1v-3-2h-4 9v-1h1 0z" class="AP"></path><path d="M356 220h0c2 1 2 2 3 3v1c1 0 5-1 6 0h1v1 1h-9 0c-1-1-2-1-4-1-4 1-8 1-12-1h-3 0 14l1-1s1 0 1-1h3 0s-1-1-1-2h0z" class="AD"></path><path d="M356 220h0c2 1 2 2 3 3v1c1 0 5-1 6 0h-24-3 0 14l1-1s1 0 1-1h3 0s-1-1-1-2h0z" class="L"></path><path d="M374 228c1 1 1 1 2 3 0 0 1 1 1 2h5c1 0 1 0 2 1h1c-1 1-1 2-2 3l1 1h0-3l-3 1h0l-4 4-1 3h-1v-1l-1 1h-1c1-1 1-1 1-2v-1c1-1 1-1 1-2l1-1 1-1h0c0-1 2-3 3-3h0c0-1 0-1 1-2-2-1-2-1-3-2 0-1-1-3-1-3l-1-1h1z" class="AQ"></path><path d="M382 233c1 0 1 0 2 1l-5 2-1-2 1-1c1 1 2 0 3 0z" class="AP"></path><path d="M384 234h1c-1 1-1 2-2 3l1 1h0-3l-3 1v-1l1-2 5-2z" class="AJ"></path><path d="M304 241h-3v-1c2 0 3 0 5-1 1 0 3 1 5 0h6c1-1 4 0 6 0 1-1 4-1 6-1h1l2 1h0c0-1-2-2-2-3-3-2-7-3-8-5h0 2c1 0 3 1 3 1h1 0c1 0 1 0 1 1h1c1 1 1 1 1 2l2 2v-1c0-1 0-2-1-3h-1c-1-1-3-2-5-2h-1c0-1-1-1-2-2l1-1h0c0-1 1-2 2-2h1 3v1h-5v1c1 2 2 2 4 3 1 0 2 2 4 2 1 1 1 1 1 2s0 2 1 3v1h-2v1c-1 0-1 0-2-1h-7l-20 2z" class="Aa"></path><path d="M370 226h10 3 3c0 1-1 1-1 1 0 1-1 2-1 2 1 1 1 2 2 3l-1 2h0-1c-1-1-1-1-2-1h-5c0-1-1-2-1-2-1-2-1-2-2-3l-1-2h-3 0z" class="AY"></path><path d="M316 220v-5l1-1c1 1 1 2 2 2h0v1h1 1l1 1 1-1h1c1 2 1 3 1 4-1 1-1 1-1 2v1h3 0v1h5-11c-3 0-5 0-7 1h0c0 3-1 5-1 7h0c-1 2-1 4-3 5h0v-2h0l2-6v-1c-1 0-2 3-3 4h-1c1-2 2-4 3-5v-1l-1 1h-1c0-1 1-1 1-2h2l1 2v-2h-1c-2-1-4-1-6-1h3l1-1h2c0 1 0 1 1 1h1c0-2 1-4 2-5z" class="Aa"></path><path d="M313 233l-2 2h0c0-1 1-2 1-3v-1c1 0 1 1 1 2h0z" class="AZ"></path><path d="M323 217h1c1 2 1 3 1 4-1 1-1 1-1 2v1h3-8-3c0-1 1-2 2-2 0-1 1-1 1-1 2 0 3-2 3-3l1-1z" class="F"></path><path d="M323 217h1c1 2 1 3 1 4-1 1-1 1-1 2v1h3-8s1 0 1-1c1 0 2-1 3-2s0-2 0-4z" class="o"></path><path d="M316 220v-5l1-1c1 1 1 2 2 2h0v1h1 1l1 1c0 1-1 3-3 3 0 0-1 0-1 1-1 0-2 1-2 2l-1 1h-1c0-2 1-4 2-5z" class="R"></path><path d="M317 219c0 1 1 1 1 2v1c-1 0-2 1-2 2l-1 1 2-6z" class="z"></path><path d="M319 217h1 1l1 1c0 1-1 3-3 3 0 0-1 0-1 1v-1c0-1-1-1-1-2 0 0 1-2 2-2z" class="b"></path><path d="M319 217h1l-2 4c0-1-1-1-1-2 0 0 1-2 2-2z" class="r"></path><path d="M321 202c4 2 8 5 11 8l20 12 1 1-1 1h-14-1l-1-1 1-1h-1c-1 1-2 1-2 2h0-1-6 0-3v-1c0-1 0-1 1-2 0-1 0-2-1-4h-1l-1 1-1-1h-1-1v-1h0c-1 0-1-1-2-2l3-3c1-2 1-2 1-4h0 0 1c1-2 0-3-1-5z" class="T"></path><path d="M338 217l2 1v1l-1 1c-1 0-1-1-2-1 1-1 1-1 1-2z" class="N"></path><path d="M330 218c1-1 0-3 1-4 1 0 1 1 2 1v1 1c-2 1-2 2-4 3v3c1 1 1 1 2 1h-3v-2h0c1-2 1-3 2-4z" class="H"></path><path d="M332 224c1-2 4-6 5-7h1c0 1 0 1-1 2 1 0 1 1 2 1l-2 2h-1c-1 1-2 1-2 2h0-1-1z" class="Y"></path><path d="M336 222c0-1 0-2 1-3 1 0 1 1 2 1l-2 2h-1z" class="a"></path><path d="M328 211c2 1 5 2 7 4 0 1-1 1-2 2v-1-1c-1 0-1-1-2-1-1 1 0 3-1 4l-1-1 1-1h0l-1-1h-1v-2-2h0z" class="I"></path><path d="M329 215l1 1h0l-1 1 1 1c-1 1-1 2-2 4h0v2h3 1 1-6 0-3v-1l2-2c1-1 3-4 3-6z" class="O"></path><path d="M329 217l1 1c-1 1-1 2-2 4 0-2 0-4 1-5z" class="R"></path><path d="M321 202c4 2 8 5 11 8l-1-1c-1 0-1 0-2-1l-3-2c-1 0-2-2-3-2v1l1 1c1 0 1 0 2 1v1c1 1 2 1 2 3h0v2 2h1c0 2-2 5-3 6l-2 2c0-1 0-1 1-2 0-1 0-2-1-4h-1l-1 1-1-1h-1-1v-1h0c-1 0-1-1-2-2l3-3c1-2 1-2 1-4h0 0 1c1-2 0-3-1-5z" class="K"></path><path d="M323 210c1-2 0-2 0-3v-1c2 1 2 2 3 3-1 1-1 1-1 2 1 1 0 1 0 2h-2c0-1 1-2 1-3h0-1 0z" class="c"></path><path d="M321 207l1 2 1 1h0 1 0c0 1-1 2-1 3l-1 3-1 1h-1-1v-1h0c-1 0-1-1-2-2l3-3c1-2 1-2 1-4z" class="D"></path><path d="M323 210h1 0c0 1-1 2-1 3l-1 3-1 1h-1-1v-1l4-6z" class="q"></path><path d="M326 209h0l2 2v2 2h1c0 2-2 5-3 6l-2 2c0-1 0-1 1-2 0-1 0-2-1-4h-1l-1 1-1-1 1-1 1-3h2c0-1 1-1 0-2 0-1 0-1 1-2z" class="N"></path><path d="M323 213h2v2h-1l-2 1h0l1-3z" class="a"></path><path d="M326 209l2 2v2 2h1c0 2-2 5-3 6l-2 2c0-1 0-1 1-2s1-1 2-3c-1-1-1-3-1-4v-1-3-1z" class="C"></path><path d="M292 202c1 1 3 3 4 5 2 3 5 5 6 8 3 3 6 6 8 9l-1 1v-1h-34 0l6-6-1-1 12-15z" class="B"></path><path d="M302 215c3 3 6 6 8 9l-1 1v-1c0-1-2-2-3-3l-6-6h2z" class="g"></path><defs><linearGradient id="z" x1="299.038" y1="217.562" x2="287.602" y2="207.845" xlink:href="#B"><stop offset="0" stop-color="#c1bdb3"></stop><stop offset="1" stop-color="#e5dedc"></stop></linearGradient></defs><path fill="url(#z)" d="M292 202c1 1 3 3 4 5 2 3 5 5 6 8h-2c-1-2-6-7-8-7-4 2-8 6-11 10l-1-1 12-15z"></path><path d="M292 173h1l2 4c2 0 2 0 3 2 1 0 1 1 2 1h1c0 1 1 2 2 3h0c2 2 3 4 6 6l3 3-1 1 2 1c0 1 1 1 1 2 2 3 5 4 7 6 1 2 2 3 1 5h-1 0 0c0 2 0 2-1 4l-3 3-1 1v5c-1 1-2 3-2 5h-1c-1 0-1 0-1-1h-2c-2-3-5-6-8-9-1-3-4-5-6-8-1-2-3-4-4-5l-1-1v-1h1v-1l-2-2 2-2h1l1 1c1-1 1-2 2-3v-2h0-1c0-2 0-2-1-4h0l-1-1-2-1c-1 1-1 2-1 3v-3l-2-2v-1h1v-2h-1c1-1 1-2 2-3h-1l3-4z" class="w"></path><path d="M292 195h1l1 1c0 2-1 3-1 4h0l2 3 5 6 12 15h-2c-2-3-5-6-8-9-1-3-4-5-6-8-1-2-3-4-4-5l-1-1v-1h1v-1l-2-2 2-2z" class="p"></path><path d="M297 201c1 1 1 1 1 2 2-1 1-1 2-2 0 0 1-1 2-1h1s1-1 2-1l1 1c2 0 4 1 5 2s3 3 3 4c2 1 2 3 3 6h0l1-1h0l1-1 1 1-3 3-1 1v5c-1-1-1-2-1-3l1-1c-2-1-1-3-2-5 0-1-1-2-1-3v-1c-3-1-6-5-9-6l-1 2c-1 0-2 0-2 1s-1 2-1 4h0v1l-5-6h1 0c1-1 1-1 1-2z" class="t"></path><path d="M296 203h1s1 1 2 1c1 1 1 1 2 0l-1-1v-1h1l1 1h1c-1 0-2 0-2 1s-1 2-1 4h0v1l-5-6h1 0z" class="z"></path><path d="M296 191c2 0 2 2 3 2 3 0 5 3 7 4 3 2 8 4 10 7 1 2 2 4 1 6l1 1h0l-1 1h0c-1-3-1-5-3-6 0-1-2-3-3-4s-3-2-5-2l-1-1c-1 0-2 1-2 1h-1c-1 0-2 1-2 1-1 1 0 1-2 2 0-1 0-1-1-2 0 1 0 1-1 2h0-1l-2-3h0c0-1 1-2 1-4 1-1 1-2 2-3v-2h0z" class="i"></path><path d="M296 191c2 0 2 2 3 2 1 1 2 2 4 3v1c-2 0-3 1-4 2 0-1 0-2 1-2v-1-1l-2-2h-2v-2h0z" class="n"></path><path d="M296 193h2l2 2v1 1c-1 0-1 1-1 2s-1 1-2 2c0 1 0 1-1 2h0-1l-2-3h0c0-1 1-2 1-4 1-1 1-2 2-3z" class="AN"></path><path d="M296 200v3h-1l-2-3h1 2 0z" class="w"></path><path d="M300 195v1 1c-1 0-1 1-1 2s-1 1-2 2c0 1 0 1-1 2h0v-3c0-1 1-1 1-1 0-1 0-2 1-3 1 0 1 0 2-1z" class="r"></path><path d="M294 187v-2h2l2 1c2 2 4 4 7 4h0l9 6c2 3 5 4 7 6 1 2 2 3 1 5h-1 0 0c0 2 0 2-1 4l-1-1-1 1-1-1c1-2 0-4-1-6-2-3-7-5-10-7-2-1-4-4-7-4-1 0-1-2-3-2h-1c0-2 0-2-1-4h0z" class="B"></path><path d="M294 187h1l2 2h1c1 1 2 3 3 3 2 2 4 3 6 4l6 3c4 2 6 5 8 8h0 0c0 2 0 2-1 4l-1-1-1 1-1-1c1-2 0-4-1-6-2-3-7-5-10-7-2-1-4-4-7-4-1 0-1-2-3-2h-1c0-2 0-2-1-4z" class="P"></path><path d="M313 199c4 2 6 5 8 8h0c-1 1-1 2-2 3-1-1-1-4-1-5-1-2-3-4-5-6z" class="L"></path><path d="M292 173h1l2 4c2 0 2 0 3 2 1 0 1 1 2 1h1c0 1 1 2 2 3h0c2 2 3 4 6 6l3 3-1 1 2 1c0 1 1 1 1 2l-9-6h0c-3 0-5-2-7-4l-2-1h-2v2l-1-1-2-1c-1 1-1 2-1 3v-3l-2-2v-1h1v-2h-1c1-1 1-2 2-3h-1l3-4z" class="f"></path><path d="M296 185h2 0v1l-2-1z" class="R"></path><path d="M289 180s0-1 1-1h3l2 1v1h-1l1 1c-1 1-1 2-2 2v2l-2-1c-1 1-1 2-1 3v-3l-2-2v-1h1v-2z" class="L"></path><path d="M290 182l1-1h3l1 1c-1 1-1 2-2 2s-1 0-2-1c-1 0-1 0-1-1z" class="q"></path><path d="M288 183v-1h1 1c0 1 0 1 1 1 1 1 1 1 2 1v2l-2-1c-1 1-1 2-1 3v-3l-2-2z" class="k"></path><path d="M292 173h1l2 4c2 0 2 0 3 2 1 0 1 1 2 1h1c0 1 1 2 2 3h0c2 2 3 4 6 6l3 3-1 1 2 1c0 1 1 1 1 2l-9-6c-2-2-6-5-8-9-1-1-2-3-4-4h-3-1l3-4z" class="r"></path><path d="M295 177c2 0 2 0 3 2 1 0 1 1 2 1h1c0 1 1 2 2 3l-2 1c-2-3-4-5-6-7z" class="f"></path><path d="M303 183h0c2 2 3 4 6 6l3 3-1 1c-4-2-7-6-10-9l2-1z" class="D"></path><path d="M247 237h1l2 1 3 1v-1c3 1 6 1 8 2l18 6c2 1 1 2 2 3l-1 6-3 14c-1 4-3 8-3 12v1l-1-2-1 1c1 3 2 5 2 8l-1 5-2 10h0v-1c-1 1 0 1-2 2h0-2c-1 0-2 1-3 1v1l-1 1c-1 0-2 1-4 2l-1-1c0 2-1 3-1 4h-1v3l1 1c-3-1-7 0-10 0-2 0-3 0-4-1h-3v-1l-1-1v27 23h0c1 6 1 12 1 18h9c-1 1-2 1-3 1h0c-2 1-4 1-6 1v32h-1v9 1c0 19 0 38-4 56-3 8-6 17-10 24h0l-1 2c0 1-1 1-1 2h-1s-1 0-1 1l-1-1-2 2c-2-1-2-1-2-3l-4-4v-2l-1-1-1-2h-2l-2-3v-2l-7-8c-1-1-2-3-3-4s-3-2-3-4c-2-2-3-4-5-6l1 1-1 2h0c-1-2-2-2-3-3v-1l-2-2-1-2h0c0-2 0-3-1-4l-1-2h0c0-2-1-3-2-5v-2h0l-2-5-2-1 1 3h-1l-1-1v1l-8-14-1-2-1-2 2-1-1-2-1-3v-2s1-1 1-2h0c0-2 0-3 1-5 0-4 2-7 4-10 1-2-1-7-2-9l2-1c6-3 13-5 20-4v1c2 0 3 0 5-1l1-1 1-1h1v-1l2-1c2-1 9-2 12-2 1 0 2 0 3 1h0l12-5h-2v-1c-2-1-4-1-6-2l-6-3c0-2-3-4-4-5-2-2-3-3-4-5l-6-6c-2-2-4-5-5-7 0-1 0-2-1-3h0-1 0c0-1-2-3-3-3h-1l-5-6 1-1 1-1-4-6c-1 0-1-1-2-2-1-2-3-4-5-6l-2-2-1-1c-1 0-2 0-3 1h-1l-1-1h0l-2-1c0-1 0-2-1-2h0v-2c-1 0-1-1-2-1v-1-1h1c1-1 4-1 6-1h0 1v-1h-4c-1-1-1-1-2-1h-1 0c-1-1-2-1-3-1 0-1-1-1-2-2h-3 0c0-1 1-1 1-2v-1h0 2c1 1 1 2 2 2l1-1c1-1 2-1 4-2 1 0 2 0 3-1h2 0c2 0 3-1 5-2h2l3-2h2l2-2 1-1 1-3 2-3c2-2 2-2 2-4l1-1-1-4c0-2-1-5-2-7h5 4c2 0 5-1 8 0v-1l1-4h1 0c0-2 0-3 1-5h0l-1-1v-1l1-1c1-6 4-11 5-16l1-2c2-2 3-3 4-5 2 0 3-1 5-1l1-1h3c6-2 13-1 19-1l1 2c1-1 1-1 1-2h1v-1z" class="Ac"></path><path d="M195 394l1-1 1 1-3 1 1-1z" class="p"></path><path d="M211 389c1 0 2 0 3 1l-5 1v-1l2-1z" class="C"></path><path d="M160 318v-1h1c1 1 2 2 2 4l-1-1c-1 0-1-1-2-1v-1z" class="H"></path><path d="M197 393c1 0 3-1 5-1l1 1-6 1-1-1h1z" class="F"></path><path d="M225 383h0 6c-2 1-4 1-5 2h-2v-1l1-1z" class="B"></path><path d="M219 381c2 1 4 2 6 2l-1 1c-2-1-4-1-6-2l1-1z" class="i"></path><path d="M169 324c2-2 4-1 6-2h1l-2 2h-2l-1 2-1-1c-1 0-1 0-1-1z" class="o"></path><path d="M182 334l2-2h1v1c-1 2 1 5 1 6l-2-2c-1-1-1-2-2-3z" class="E"></path><path d="M171 326l1-2h2 0l1 1 1 1h-2l-1 2-2-2z" class="K"></path><path d="M184 450h0c1-1 1-2 2-3h0c0 1 0 3-1 4l3 8-2-2c-1-2-1-5-1-7h-1 0z" class="e"></path><path d="M216 503c1 1 1 1 1 2 0 2 2 3 3 5l-2 2c-2-1-2-1-2-3 1-1 0-4 0-6z" class="K"></path><path d="M219 445h1c-2 2-4 5-5 7s-2 5-3 7v-2c1-2 2-5 3-8l4-4z" class="E"></path><path d="M162 320l1 1c1 0 3 1 4 2h1l1 1c0 1 0 1 1 1-1 0-2 0-3 1h-1l-1-1h0l-2-1c0-1 0-2-1-2h0v-2z" class="F"></path><path d="M168 323l1 1c0 1 0 1 1 1-1 0-2 0-3 1h-1l-1-1v-1l3-1z" class="c"></path><path d="M219 475h1l1 1-1 3c-1 1-1 1-2 1h0c0-1 1-2 1-3-2 1-3 3-3 4l-1 4s0 1-1 1h0c0-5 2-8 5-11z" class="E"></path><path d="M186 419h2c-5 3-7 8-9 14h-1 0v-4c1-4 6-8 8-10z" class="D"></path><path d="M212 501c1 1 2 1 3 2v-2l1 2c0 2 1 5 0 6l-4-4v-2l-1-1 1-1z" class="Q"></path><path d="M212 501c1 1 2 1 3 2 0 1 0 1-1 2-1-1-1-2-2-2l-1-1 1-1z" class="o"></path><path d="M208 374h3c2 3 5 5 8 7l-1 1-6-3c0-2-3-4-4-5z" class="c"></path><path d="M200 475c0-1 0-1 1-2 1-3 4-5 6-7s3-4 5-5c-1 2-2 3-3 5-2 2-5 4-6 6-2 2-2 5-2 8h-1v-3-2z" class="n"></path><path d="M239 364h0c1 6 1 12 1 18h9c-1 1-2 1-3 1h0c-2 1-4 1-6 1v32h-1v-7-19c0-3 1-5 0-7v-19z" class="AR"></path><path d="M197 393v-1l2-1c2-1 9-2 12-2l-2 1v1c-1 1-2 1-3 1-1 1-2 1-3 1l-1-1c-2 0-4 1-5 1z" class="Z"></path><path d="M202 392l3-1 1 1c-1 1-2 1-3 1l-1-1z" class="Q"></path><path d="M205 391l4-1v1c-1 1-2 1-3 1l-1-1z" class="D"></path><path d="M171 310h8c2-1 4-1 6-1h6c-4 2-10 5-14 4h-1c-1 0-2 0-3-1v-2h-2z" class="H"></path><path d="M179 311h3l-1 1h-3l1-1zm-1-9h2v1h1c2 2 4 2 6 3h-9c-3 1-6 2-9 2v1l-2 1-1-1v-1l2-2h0c2 0 3-1 5-2h2l3-2z" class="T"></path><path d="M168 306c3 2 7-1 10 0-3 1-6 2-9 2v1l-2 1-1-1v-1l2-2h0z" class="D"></path><path d="M200 477v3h1c1 0 1 1 1 2l2 4v3c-1-2-2-3-4-3l-1 1c-1-1-2-3-3-4s-3-2-3-4l2-1h1 1c1 0 2-1 3-1z" class="e"></path><path d="M200 477v3 2c-2-1-3-3-4-4h1c1 0 2-1 3-1z" class="m"></path><path d="M201 480c1 0 1 1 1 2s1 2 1 3h-1c-1-1-2-2-2-3v-2h1z" class="Y"></path><path d="M204 486l7 9c1 2 2 5 4 6v2c-1-1-2-1-3-2l-1 1-1-2h-2l-2-3v-2l-7-8 1-1c2 0 3 1 4 3v-3z" class="i"></path><path d="M210 498c1 1 1 2 2 3l-1 1-1-2-1-1 1-1z" class="a"></path><path d="M199 487l1-1c2 0 3 1 4 3l6 9-1 1 1 1h-2l-2-3v-2l-7-8z" class="m"></path><path d="M206 495l3 4 1 1h-2l-2-3v-2z" class="p"></path><path d="M189 397h1c2 4 6 9 9 12 4 5 9 8 13 12 2 1 3 3 3 4h-1c-5-3-9-7-13-11-1-1-3-2-4-4-2-2-4-5-6-8 0-1-2-3-2-4v-1z" class="E"></path><path d="M194 350c1 1 1 1 1 2 1 3 3 5 4 7s2 3 3 5c3 4 6 7 9 10h-3c-2-2-3-3-4-5l-6-6c-2-2-4-5-5-7 0-1 0-2-1-3h0-1c1-1 1-2 3-3z" class="k"></path><path d="M192 353l1-1c1 0 2 3 3 4 1 2 3 5 4 8 2 1 3 3 4 4v1h0l-6-6c-2-2-4-5-5-7 0-1 0-2-1-3z" class="E"></path><path d="M182 334c1 1 1 2 2 3l2 2 8 11h0c-2 1-2 2-3 3h0c0-1-2-3-3-3h-1l-5-6 1-1 1-1-4-6c1 1 1 1 2 1l-1-1 1-1v-1z" class="V"></path><path d="M182 334c1 1 1 2 2 3l-1 2c1 1 2 3 3 5v1h-2v1l4 4h-1l-5-6 1-1 1-1-4-6c1 1 1 1 2 1l-1-1 1-1v-1z" class="Z"></path><path d="M182 334c1 1 1 2 2 3l-1 2c0-2-1-3-1-4v-1z" class="I"></path><path d="M205 317c1 1 2 3 3 3h1v8 16 1c-1-1-2-3-2-4l-3-9c0-1 1-1 1-2 0-2-1-5-1-8h1v-3c-1-1 0-1 0-2z" class="AH"></path><path d="M205 317c1 1 2 3 3 3h1l-1 1c-1-1-2-1-3-1-1 2 2 9 0 10h0c0-2-1-5-1-8h1v-3c-1-1 0-1 0-2z" class="AX"></path><path d="M163 307c1 0 2 0 3-1h2l-2 2v1l1 1 2-1s1 1 2 1h2v2c1 1 2 1 3 1h1l-1 1v2c3 1 5 2 7 3-3 0-5-1-8-2-2 0-3 0-4-1h-1c0 1-2 1-3 2v-1-1h1v-1h-4c-1-1-1-1-2-1h-1 0c-1-1-2-1-3-1 0-1-1-1-2-2h-3 0c0-1 1-1 1-2v-1h0 2c1 1 1 2 2 2l1-1c1-1 2-1 4-2z" class="e"></path><path d="M166 309l1 1v1h0l-2-1h0l1-1z" class="b"></path><path d="M166 313c-1 0-1 0-2-1v-1h2v2z" class="H"></path><path d="M169 309s1 1 2 1h2v2c1 1 2 1 3 1h1l-1 1v2l-10-3v-2h1 0v-1l2-1z" class="E"></path><path d="M169 309s1 1 2 1h2v2c-2-1-4-1-6-1v-1l2-1z" class="a"></path><defs><linearGradient id="AA" x1="193.417" y1="317.111" x2="207.752" y2="305.805" xlink:href="#B"><stop offset="0" stop-color="#802925"></stop><stop offset="1" stop-color="#a92f2a"></stop></linearGradient></defs><path fill="url(#AA)" d="M194 294h8l-1 1c1 2 1 4 1 5v1 1 4 6h1c1 2 1 4 2 5 0 1-1 1 0 2v3h-1c0 3 1 6 1 8 0 1-1 1-1 2-2-3-3-6-4-10-4-9-7-18-9-27 0-1 1-1 1-1h2z"></path><path d="M194 294h8l-1 1h-2c-2 0-5 1-7 0v-1h2zm8 18h1c1 2 1 4 2 5 0 1-1 1 0 2v3h-1l-2-10z" class="Ab"></path><defs><linearGradient id="AB" x1="195.91" y1="478.789" x2="182.743" y2="454.303" xlink:href="#B"><stop offset="0" stop-color="#776b60"></stop><stop offset="1" stop-color="#8e7f74"></stop></linearGradient></defs><path fill="url(#AB)" d="M184 450h1c0 2 0 5 1 7l2 2 11 16h1v2c-1 0-2 1-3 1h-1-1l-2 1c-2-2-3-4-5-6l1 1-1 2h0c-1-2-2-2-3-3v-1l-2-2-1-2h0c0-2 0-3-1-4l-1-2h0c0-2-1-3-2-5v-2h0 1v-2-1c1 0 2 1 2 1h1 1l1 1h0v-4z"></path><path d="M182 457c0-1 1-1 1-1v7c-1-2 0-2-1-3s0-1 0-1v-2z" class="C"></path><path d="M182 460c1 1 0 1 1 3l1 3-3-2-1-2c1 0 1-1 2-2z" class="O"></path><path d="M181 464l3 2 4 7 1 1-1 2h0c-1-2-2-2-3-3v-1l-2-2-1-2h0c0-2 0-3-1-4z" class="S"></path><path d="M179 453v-1c1 0 2 1 2 1h1 1v3h0s-1 0-1 1v2s-1 0 0 1c-1 1-1 2-2 2h0c0-2-1-3-2-5v-2h0 1v-2z" class="Y"></path><path d="M179 453c0 2 1 3 3 4h0v2c-2-1-3-2-4-4h0 1v-2z" class="t"></path><path d="M179 453v-1c1 0 2 1 2 1h1 1v3h0s-1 0-1 1h0c-2-1-3-2-3-4z" class="l"></path><path d="M182 453h1v3l-1-1-1-2h1z" class="X"></path><path d="M212 246c2 0 2 1 3 2s2 2 2 3c1 0 1 1 2 2 2 3 4 5 6 8v1c-1 0-1 1-1 1l-3 6-4-3h1c0 2 2 2 3 3l-5 10h0c-1 0-1 0-2-1-2-1-5-2-7-4h0c-1-2-1-2-2-2 0-2 0-3 1-5h0l-1-1v-1l1-1c1-6 4-11 5-16l1-2z" class="AA"></path><path d="M205 265l1-1v3l-1-1v-1z" class="AC"></path><path d="M209 265h2v2h-1c0-1-1-1-1-2z" class="AE"></path><path d="M206 267c1 0 1-1 1-2h1c1 1 1 1 1 2 1 0 3 1 3 2h0v-2h0 3c1 1 1 0 1 0 0-1 0-2-1-3h0c1 0 1 0 1 1 1 0 1 0 1 1h1c0 2 2 2 3 3l-5 10h0c-1 0-1 0-2-1-2-1-5-2-7-4h0c-1-2-1-2-2-2 0-2 0-3 1-5z" class="AM"></path><path d="M210 272c1 0 3 1 4 2h-1l1 1c-2-1-3-2-4-3z" class="AW"></path><path d="M169 409h0 1l9 30 5 11h0v4h0l-1-1h-1-1s-1-1-2-1v1 2h-1l-2-5-2-1 1 3h-1l-1-1v1l-8-14-1-2-1-2 2-1-1-2-1-3v-2s1-1 1-2h0c0-2 0-3 1-5 0-4 2-7 4-10z" class="g"></path><path d="M175 448c1 1 2 2 2 3l1 1h0c0-1 1-2 1-2l1-1 1 1h-1v1c1 0 1 0 2 2h-1s-1-1-2-1v1 2h-1l-2-5-1-2z" class="d"></path><path d="M169 409h0c0 5-3 8-4 13v-3c0-4 2-7 4-10z" class="E"></path><path d="M168 435c3 4 5 9 7 13l1 2-2-1c-2-5-5-9-6-14z" class="Y"></path><path d="M164 424c0-2 0-3 1-5v3l3 11c-1-1-2-3-3-4l-1 2-1-3v-2s1-1 1-2h0z" class="D"></path><path d="M164 424l1 5-1 2-1-3v-2s1-1 1-2z" class="E"></path><path d="M179 439l5 11h0v4h0l-1-1h-1c-1-2-1-2-2-2v-1h1l1-1-1-1c-1-1-1-2-1-4-1-1-1-3-1-5z" class="t"></path><path d="M165 429c1 1 2 3 3 4v2c1 5 4 9 6 14l1 3h-1l-1-1v1l-8-14-1-2-1-2 2-1-1-2 1-2z" class="B"></path><path d="M165 433l1 3h-2l-1-2 2-1z" class="Y"></path><path d="M166 436l7 15v1l-8-14-1-2h2z" class="a"></path><path d="M204 272h1 0c1 0 1 0 2 2h0c2 2 5 3 7 4 1 1 1 1 2 1h0v2 1l-4 12-1 1 1 1v1l-3 13c1 2 0 3 0 5s0 3-1 5c-1 0-2-2-3-3s-1-3-2-5h-1v-6-4-1-1c0-1 0-3-1-5l1-1h-8-2s-1 0-1 1c0-1-1-3-1-4l-1-3-1-4c0-2-1-5-2-7h5 4c2 0 5-1 8 0v-1l1-4z" class="AM"></path><path d="M212 297h-2c-1-1-2-1-4-2h3c-1 0-2-1-3-1 1-1 2 0 3 0h0c0 1 1 1 1 1h1l1 1v1z" class="AH"></path><path d="M203 304l4 4-1 1-3-2h0v-3z" class="AO"></path><path d="M202 282c0 1 1 1 1 1v8c0-1 0-2-1-3v-6z" class="AS"></path><path d="M203 307h0v1c1 1 1 1 2 1 1 1 1 2 2 3 0 1 0 1-1 1s-1-1-2-1c0-1-1-1-1-2v-3z" class="AX"></path><path d="M207 312h1s0-1-1-1v-1h2c1 2 0 3 0 5-1 0-2-1-3-2 1 0 1 0 1-1z" class="AH"></path><path d="M202 301h1v3 3 3c0 1 1 1 1 2h-1-1v-6-4-1z" class="AL"></path><path d="M202 288c1 1 1 2 1 3v10h-1v-1c0-1 0-3-1-5l1-1v-6z" class="AB"></path><path d="M204 312c1 0 1 1 2 1 1 1 2 2 3 2 0 2 0 3-1 5-1 0-2-2-3-3s-1-3-2-5h1z" class="AM"></path><path d="M204 272h1 0c1 0 1 0 2 2h0c2 2 5 3 7 4 1 1 1 1 2 1h0v2 1c-1 0-2 0-2-1-3-1-7-3-9-5-1 2-1 4-1 6l-1 1h0s-1 0-1-1l1-5v-1l1-4z" class="AH"></path><path d="M204 272h1v2l-1 4c-1 0-1-1-1-2l1-4z" class="AS"></path><path d="M203 276c0 1 0 2 1 2l-1 5h0s-1 0-1-1l1-5v-1z" class="AB"></path><path d="M205 272c1 0 1 0 2 2h0c2 2 5 3 7 4 1 1 1 1 2 1h0v2c-1-1-2-1-3-2l-8-5v-2h0z" class="AE"></path><path d="M195 277c2 0 5-1 8 0l-1 5v6 6h-8-2s-1 0-1 1c0-1-1-3-1-4l-1-3-1-4c0-2-1-5-2-7h5 4z" class="AV"></path><path d="M190 291l2 1c1 0 1 1 2 2h0-2s-1 0-1 1c0-1-1-3-1-4z" class="AA"></path><defs><linearGradient id="AC" x1="211.903" y1="272.644" x2="259.617" y2="279.347" xlink:href="#B"><stop offset="0" stop-color="#962925"></stop><stop offset="1" stop-color="#c7322d"></stop></linearGradient></defs><path fill="url(#AC)" d="M247 237h1l2 1 3 1v-1c3 1 6 1 8 2l18 6c2 1 1 2 2 3l-1 6-3 14c-1 4-3 8-3 12v1l-1-2-1 1c1 3 2 5 2 8l-1 5-2 10h0v-1c-1 1 0 1-2 2h0-2c-1 0-2 1-3 1v1l-1 1c-1 0-2 1-4 2l-1-1c0 2-1 3-1 4h-1v3l1 1c-3-1-7 0-10 0-2 0-3 0-4-1h-3v-1l-1-1v-61-1c-4 2-8 4-11 7-1 1-1 2-2 2h-1c-2-3-4-5-6-8-1-1-1-2-2-2 0-1-1-2-2-3s-1-2-3-2c2-2 3-3 4-5 2 0 3-1 5-1l1-1h3c6-2 13-1 19-1l1 2c1-1 1-1 1-2h1v-1z"></path><path d="M256 267l2-1v2l1 1-1 1c-1 1-1 1-1 2l-1 1v-6z" class="AD"></path><path d="M243 316c2 0 12 0 12-1 1-1 0-2 1-3v4l1 1c-3-1-7 0-10 0-2 0-3 0-4-1z" class="AL"></path><path d="M229 256c-1 0-1-1-1-1l-1-1-2-4c0-1 0-1 1-1 2 1 2 3 4 5 0-2-2-3-2-4h0l2 1 1 3-1 1c-1 0-1-1-2-2v1c0 1 1 1 1 2z" class="AA"></path><path d="M222 239h3 0c-2 1-4 1-6 2-1 2-1 2-3 4h-1-1c1 1 1 2 2 2l-1 1c-1-1-1-2-3-2 2-2 3-3 4-5 2 0 3-1 5-1l1-1z" class="AX"></path><path d="M230 251h0 0c0-1 0-1-1-1l1-1c1 2 1 3 2 5 1 0 1 0 1 1 1-1 0-2 0-3 1 1 1 1 2 1h0 0c1 0 2-1 2-1h2c-4 2-8 4-11 7h0c-1-2-3-3-4-5 2 1 3 3 5 4v-2c0-1-1-1-1-2v-1c1 1 1 2 2 2l1-1-1-3z" class="AE"></path><path d="M216 247h1c2 2 4 5 7 7 1 2 3 3 4 5h0c-1 1-1 2-2 2h-1c-2-3-4-5-6-8-1-1-1-2-2-2 0-1-1-2-2-3l1-1h0z" class="AH"></path><path d="M216 247h1c2 2 4 5 7 7 1 2 3 3 4 5h0c-1 1-1 2-2 2 0-2-2-4-3-6l-7-8z" class="AW"></path><path d="M255 251l1 2h2c2 1 3 3 5 4l1 1c0 1 1 2 1 2v1c1 0 1 1 2 2l-3 1h-2l-1 2-1 1v1l-1 1-1-1v-2l-2 1v-8l-1-8z" class="Aa"></path><path d="M263 257l1 1c-2 1-2 2-3 3s-2 3-2 5h-1l-2 1v-8c1 2 0 5 0 7h2c0-1 1-1 1-2v-1c0-1-1-2-1-3s1-1 2-2c1 0 2 0 3-1z" class="AZ"></path><path d="M264 258c0 1 1 2 1 2v1c1 0 1 1 2 2l-3 1h-2l-1 2-1 1v1l-1 1-1-1v-2h1c0-2 1-4 2-5s1-2 3-3z" class="AG"></path><path d="M262 264l-1-1c0-1 1-1 1-2 1-1 2 0 3 0s1 1 2 2l-3 1h-2z" class="AQ"></path><path d="M262 264h2l3-1 1 3v3c0 2 0 7 1 8-1 1-2 2-4 3h0l-1 1c-1 1-2 1-3 2 0 1 0 1 1 2-1 0-2 0-3 1v-2h-1-2c-1-1 0-1 0-2 1-1 0-5 0-6v-3l1-1c0-1 0-1 1-2l1-1 1-1v-1l1-1 1-2z" class="AK"></path><path d="M261 266c1 1 1 0 2 1s1 2 3 3v1c-1 0-2 1-3 2-2 0-2-1-4 0 0 1 0 2 1 3h-1v2c1 1 1 2 2 2 0 1 1 1 1 1h0-2l-1-2v-3c0-1 0-1-1-2v-2h-1c0-1 0-1 1-2l1-1 1-1v-1l1-1z" class="AI"></path><path d="M260 267c1 1 2 1 3 2 0 1-1 1-2 2-1-1-1-1-1-3v-1z" class="AK"></path><path d="M260 268c0 2 0 2 1 3v1h-1-1-1-1c0-1 0-1 1-2l1-1 1-1z" class="AY"></path><path d="M257 272c0-1 0-1 1-2 1 1 1 1 1 2h-1-1z" class="AU"></path><path d="M257 272h1v2c1 1 1 1 1 2v3l1 2h2 0l1-1s1-1 2-1v1l-1 1c-1 1-2 1-3 2 0 1 0 1 1 2-1 0-2 0-3 1v-2h-1-2c-1-1 0-1 0-2 1-1 0-5 0-6v-3l1-1z" class="AD"></path><path d="M257 272h1v2c1 1 1 1 1 2-1 2-1 3-1 5-1-2-1-4-1-6l-1 1v-3l1-1z" class="AG"></path><path d="M256 273l1-1v3l-1 1v-3z" class="AU"></path><path d="M259 276v3l1 2h2 0l1-1s1-1 2-1v1l-1 1c-1 1-2 1-3 2 0 1 0 1 1 2-1 0-2 0-3 1v-2l1-1c0-1-1-1-2 0v-2c0-2 0-3 1-5z" class="AC"></path><path d="M269 277l3 4c1 3 2 5 2 8l-1 5-2 10h0v-1c-1 1 0 1-2 2h0-2c-1 0-2 1-3 1v1l-1 1c-1 0-2 1-4 2l-1-1c0 2-1 3-1 4h-1v3-4-17c0-3 1-7 0-11h2 1v2c1-1 2-1 3-1-1-1-1-1-1-2 1-1 2-1 3-2l1-1h0c2-1 3-2 4-3z" class="AJ"></path><path d="M264 281l-2 2v3c-1 1-1-1-2 1l1 1h2 0c0 1-1 2-1 3v2h-1-1l1-1h0c0-1-1-2-1-2v-2-1c-1 0-1-1-1-1 1-1 2-1 3-1-1-1-1-1-1-2 1-1 2-1 3-2z" class="AK"></path><path d="M259 294v-4h1s1 1 1 2h0l-1 1h1c1 1 1 1 2 1 0 1 0 2 1 2h3v1s-1 0-2 1v-1h-1c-2 0-4 0-5-1v-2z" class="AI"></path><path d="M258 284h1v2s0 1 1 1v1 2h-1v4 2c-1 0-2 0-3-1 0-3 1-7 0-11h2z" class="AF"></path><path d="M258 284h1v2s0 1 1 1v1 2h-1v4l-2-1 1-1c1-2 0-5 0-8z" class="Ad"></path><path d="M259 300v-2h6 0 0c-1 1-1 2-2 2v1c0 1 0 1-1 2s-1 1-2 1h3c1-1 1 0 2 0 1-1 1-1 2-1 0 1 1 1 1 1 0 1 1 1 1 1h-2c-1 0-2 1-3 1s-2-1-3-1h-1v-1-3l-1-1z" class="AK"></path><path d="M260 301h2c-1 1-1 2-2 3v-3z" class="Ad"></path><path d="M261 305c2-1 3-1 4-1l2 1c-1 0-2 1-3 1s-2-1-3-1z" class="AI"></path><path d="M256 295c1 1 2 1 3 1 1 1 3 1 5 1h1v1h0 0-6v2l1 1v3 1h1c1 0 2 1 3 1v1l-1 1c-1 0-2 1-4 2l-1-1c0 2-1 3-1 4h-1v3-4-17z" class="AU"></path><path d="M259 300l1 1v3h-1c0-1 0-1-1-1 0-1 1-1 1-3z" class="AQ"></path><path d="M259 304h1v1h1c1 0 2 1 3 1v1l-1 1c-1 0-2 1-4 2l-1-1v-1-1l1-3z" class="AP"></path><path d="M259 304h1v1h1c1 0 2 1 3 1v1h-3v-1c-2 0-1 1-3 2v-1l1-3z" class="AY"></path><path d="M247 237h1l2 1 3 1v-1c3 1 6 1 8 2l18 6c2 1 1 2 2 3l-1 6-3 14c-1 4-3 8-3 12v1l-1-2-1 1-3-4c-1-1-1-6-1-8v-3l-1-3c-1-1-1-2-2-2v-1s-1-1-1-2l-1-1c-2-1-3-3-5-4h-2l-1-2c-1-6-3-9-8-13v-1z" class="AO"></path><path d="M265 256c4 3 6 9 7 13v3h-1v3l2 5-1 1-3-4c-1-1-1-6-1-8v-3l-1-3c-1-1-1-2-2-2v-1c1 0 1 1 2 1h0c-1-1-1-2-1-3l-1-2z" class="AL"></path><path d="M266 258l3 6-1 2-1-3c-1-1-1-2-2-2v-1c1 0 1 1 2 1h0c-1-1-1-2-1-3z" class="Ai"></path><path d="M269 264l1 4 1 7 2 5-1 1-3-4c-1-1-1-6-1-8v-3l1-2z" class="AF"></path><path d="M269 264l1 4v3l-2-2v-3l1-2z" class="Aa"></path><path d="M247 237h1l2 1 3 1c4 6 9 11 12 17l1 2c0 1 0 2 1 3h0c-1 0-1-1-2-1 0 0-1-1-1-2l-1-1c-2-1-3-3-5-4h-2l-1-2c-1-6-3-9-8-13v-1z" class="AD"></path></svg>