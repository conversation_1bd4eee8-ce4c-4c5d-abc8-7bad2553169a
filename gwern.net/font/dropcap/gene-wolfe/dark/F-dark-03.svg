<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="65 28 324 668"><!--oldViewBox="0 0 440 752"--><style>.B{fill:#7b7a7a}.C{fill:#4a494a}.D{fill:#868686}.E{fill:#676768}.F{fill:#595858}.G{fill:#a1a0a1}.H{fill:#fdfdfd}.I{fill:#232323}.J{fill:#121111}</style><path d="M218 179v-4h3v4h-3z" class="C"></path><path d="M362 203c1 2 2 3 3 4v1c-2 1-2 0-4 0-1-1-2-2-4-3 2-1 3-1 5-2z" class="D"></path><path d="M218 334v-9h3v10-1h-3z" class="E"></path><path d="M99 273c-1 0-2-1-3-2 1-1 2-1 3-1 1-2 0-2 0-3v-1h0c2 0 4 1 6 2-1 1-2 2-4 3l-1 1-1 1z" class="F"></path><path d="M113 210l-1 1c-5 5-5 11-4 18-2-4-2-8-2-12 1-2 2-4 2-5 1-2 1-3 3-4 1 0 1 1 2 2z" class="G"></path><path d="M344 194c6 1 13 5 18 9-2 1-3 1-5 2-3-2-6-4-10-6-2 0-3-1-5-1h-1l-1-1v-1c1 0 2 1 3 1l9 4c1 0 1 0 1 1h3 3v1c0-2-5-4-6-5-2-1-4-1-5-2-1 0-2-1-4-1v-1z" class="B"></path><path d="M324 210h1c1-1 0-4 1-5 0 2 0 2 1 3 3 3 5 6 5 10 0 2 0 8-1 10 0-3 1-7 0-10-1-2-3-4-4-5l-3-3z" class="G"></path><defs><linearGradient id="A" x1="219.059" y1="179.902" x2="219.513" y2="192.044" xlink:href="#B"><stop offset="0" stop-color="#5e5d5e"></stop><stop offset="1" stop-color="#939393"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M218 179h3v27c-1-1-1-3-1-4v-1-7h-1c0 3 1 9-1 12v-27z"></path><path d="M141 116l-1-3c-2-6-2-12 1-17 2-2 4-5 6-5 2-1 5-1 7 1 1 0 2 2 3 3 0 2 0 4-1 5h-1v-4c-1-2-2-3-3-3-2-1-5 0-6 1-2 1-4 4-5 7-1 5 0 10 3 15h-1c-1-1-1-3-2-4h0c0-1 0-1-1-2v-2c0-1 0-6 1-7h0v-1c0-1 2-4 2-5-3 3-3 5-3 9-1 1-1 4 0 6 0 2 0 3 1 4 0 0 0 1 1 1v1h-1zm154 0c3-5 4-9 3-16-1-2-3-5-5-7-2-1-4-1-6 0-1 0-2 1-3 3v4h-1c-1-1-1-3-1-5 1-1 2-3 3-4 2-1 5-1 7 0 3 0 5 3 6 6 3 4 3 11 1 16 0 2-1 4-2 6h-1c0-1 0-1 1-2v-2c1-1 1-2 2-3s0-2 1-3v-6l-1-1v-1c0-2-1-3-2-4h0c1 2 1 3 2 5v4c0 4-1 7-3 10h-1z" class="H"></path><defs><linearGradient id="C" x1="311.625" y1="193.047" x2="305.789" y2="196.717" xlink:href="#B"><stop offset="0" stop-color="#272627"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#C)" d="M311 190l3 8c-3-1-8 1-12 2l2-2-2-2c0-1 0-3-1-4 3-1 6 0 9-1l1-1z"></path><defs><linearGradient id="D" x1="329.984" y1="201.507" x2="340.997" y2="221.475" xlink:href="#B"><stop offset="0" stop-color="#6e6c6d"></stop><stop offset="1" stop-color="#848686"></stop></linearGradient></defs><path fill="url(#D)" d="M326 200h0v3c1 0 4 1 6 1 2 2 6 4 8 6 4 4 6 12 7 17v2c-1-1-1-4-2-5-3-7-8-13-14-17-1 0-4-2-5-2v-5z"></path><path d="M109 203s1 2 1 3h0l-9 6c-6 5-7 13-10 19 0-4 1-8 3-12 2-6 7-13 13-15l1-1h1z" class="B"></path><path d="M292 206c0-1 1-2 2-2 1-1 8-3 9-3 2 1 5-1 8 0 1 0 1 2 2 3l-1 1h-1c-2 0-4 0-5 1l-4 2c-1-1-1-1-1-2-4 0-7 0-10 1l1-1z" class="I"></path><path d="M301 203c1-1 3-1 5 0h1c2 0 3 1 4 1v1c-2 0-4 0-5 1 0-1 0-1-1-1l-4-2z" class="C"></path><path d="M301 203l4 2c1 0 1 0 1 1l-4 2c-1-1-1-1-1-2-4 0-7 0-10 1l1-1c1-1 3-2 4-2 2-1 3-1 5-1z" class="E"></path><defs><linearGradient id="E" x1="205.764" y1="180.428" x2="206.414" y2="186.935" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#5c5c5c"></stop></linearGradient></defs><path fill="url(#E)" d="M199 202v-22h14v20-1-12h-4-1c-1 1-4 1-5 0h-1-1s-1 0-1 1c-1 1-1 2-1 4l1 8c0 1 0 2-1 2z"></path><defs><linearGradient id="F" x1="205.915" y1="186.971" x2="206.108" y2="195.122" xlink:href="#B"><stop offset="0" stop-color="#5f5f5f"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#F)" d="M200 200l-1-8c0-2 0-3 1-4 0-1 1-1 1-1h1 1c1 1 4 1 5 0h1 4v12h0c-1-1-1-2-1-3h-12v3h2l-1 1h-1z"></path><path d="M226 345v-19h15v94 30 14-117-10h0l-1-7c-4 0-10 0-14 2v13z" class="C"></path><path d="M112 190l21 2h-3c-1 1-3 2-4 3-3 2-8 4-9 7-1-1-2-1-2-2-3-3-3-6-3-10z" class="F"></path><path d="M115 200l1-1c1 0 2 0 3-1 2-1 5-3 7-3-3 2-8 4-9 7-1-1-2-1-2-2z" class="C"></path><defs><linearGradient id="G" x1="232.884" y1="180.846" x2="233.597" y2="188.575" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#G)" d="M226 190v-9h15v16 8c-1-2-1-3-1-5v-11c-3-1-10-1-14 1z"></path><defs><linearGradient id="H" x1="100.716" y1="439.407" x2="106.046" y2="452.451" xlink:href="#B"><stop offset="0" stop-color="#a3a3a3"></stop><stop offset="1" stop-color="#d7d6d6"></stop></linearGradient></defs><path fill="url(#H)" d="M99 442h0c0-1 0-1 1-2l6-2v6c1 4 1 9 3 13 0 2 1 3 2 4h-1c-1-1-2-3-3-5 0 0 1 0 0-1v-2c-2 0-4 1-6 2 1 1 1 3 2 4 1 2 2 5 2 6h0c-4-5-7-17-6-23z"></path><path d="M98 249c-2 1-3 2-5 3l3 4c-3 2-6 3-8 3-1-1-2-1-2-2-3-5-2-20 0-25v6c1 3 3 9 5 11 2 0 3 0 5-1h0l2 1z" class="B"></path><defs><linearGradient id="I" x1="101.544" y1="415.906" x2="102.343" y2="432.022" xlink:href="#B"><stop offset="0" stop-color="#4d4d4e"></stop><stop offset="1" stop-color="#807f80"></stop></linearGradient></defs><path fill="url(#I)" d="M99 441c-1-2-1-4-1-6l1-16c0-3-1-5-1-8 1 3 3 7 5 11 2 3 4 6 4 11l-1 11v-6l-6 2c-1 1-1 1-1 2h0v-1z"></path><defs><linearGradient id="J" x1="101.102" y1="431.69" x2="103.264" y2="438.612" xlink:href="#B"><stop offset="0" stop-color="#828181"></stop><stop offset="1" stop-color="#a0a0a0"></stop></linearGradient></defs><path fill="url(#J)" d="M99 441v-9l6-1 1-1 1 3-1 11v-6l-6 2c-1 1-1 1-1 2h0v-1z"></path><path d="M117 202c1 0 2-1 3-2 3-1 8-1 10 0h0c5 2 10 1 14 3 2 1 3 2 4 4h-8c-1 0-2-1-3 0h-1c-2 0-4-1-6-2-4 0-9 1-13 2l1-2c0-1 0-2-1-2v-1z" class="F"></path><path d="M117 202c1 0 2-1 3-2 3-1 8-1 10 0h0c5 2 10 1 14 3-6 0-13-1-19 0-3 0-4 1-6 2h-1c0-1 0-2-1-2v-1z" class="I"></path><defs><linearGradient id="K" x1="233.093" y1="189.621" x2="233.845" y2="199.418" xlink:href="#B"><stop offset="0" stop-color="#6f6f6f"></stop><stop offset="1" stop-color="#a1a1a1"></stop></linearGradient></defs><path fill="url(#K)" d="M226 190c4-2 11-2 14-1v11c-2-1-6 0-9 0h-4l-1 2v-12z"></path><defs><linearGradient id="L" x1="232.953" y1="336.283" x2="233.07" y2="341.404" xlink:href="#B"><stop offset="0" stop-color="#a1a0a0"></stop><stop offset="1" stop-color="#bfbfbf"></stop></linearGradient></defs><path fill="url(#L)" d="M226 345v-13c4-2 10-2 14-2l1 7h0v10c-2-1-1-2-1-4h-2c-1 1-3 1-4 0h-2-4c-1 1-1 2-1 3 0 0 0 1-1 1v-2z"></path><path d="M241 337c-2 0-3 1-4 1-1-1-1-1-2-1h1s1 0 1-1h1l1 1h2 0z" class="G"></path><defs><linearGradient id="M" x1="205.113" y1="336.344" x2="205.127" y2="341.47" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#M)" d="M199 335v-9l14-1v23c-1-1-1-3-1-5h0-1-3-1-4c-1 0-2 0-2 1-2 0-1 2-1 3 0 0-1 0-1 1v-6-7z"></path><path d="M212 339h0 1c-1 1-1 1-2 1 0 0-1 0-1-1h2zm-5-2h2l-1 1h-1v-1h0zm-8-2c3 0 7 0 10 1-2 1-7 0-10 0v6-7z" class="G"></path><defs><linearGradient id="N" x1="288.347" y1="251.048" x2="281.638" y2="250.201" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#666"></stop></linearGradient></defs><path fill="url(#N)" d="M284 275h0c-2-2-2-5-2-7-1-8-1-16 0-24 1-5 3-9 5-13 1-1 1-3 2-4h1c1 3 0 11 0 14-1 2-2 4-1 6 1 1 0 3 0 5 0 1 1 2 0 4v-2c-1 1-1 5-1 6 0 5-1 11-4 15z"></path><path d="M136 207h1c1-1 2 0 3 0h8c2 4 2 11 1 16-1 4-2 7-5 9l-4 3h0c-2 1-4 2-6 2-3 0-6-2-7-3-3-2-4-5-4-8 0-1 0-3 1-5 2-1 3-2 5-2s3 1 4 2h0c-1 0-2-1-3-1-2 0-2 1-3 1-1 2-1 3-1 4 0 2 2 4 3 5s4 2 5 1c4 0 6-2 8-5 2-2 3-7 2-10-1-4-5-7-8-9zm155 0c3-1 6-1 10-1 0 1 0 1 1 2-3 1-5 3-6 6-2 2-2 6-1 8s1 4 2 6c3 2 5 3 8 4 3-1 6-2 7-5 1-1 1-3 1-5l-2-2h-3l-2 1s0-1 1-2h4c2 1 3 1 4 3h0c1 1 1 5 1 6-1 3-3 6-5 7-3 1-7 2-10 1l-3-1c0-1-4-3-4-4l-4-4c-1-2-1-4-1-6v-10c0-2 1-3 2-4z" class="D"></path><path fill="#e9e8e8" d="M218 206c2-3 1-9 1-12h1v7 1c0 1 0 3 1 4v7 14 52 13c0 2 1 4 0 5h-3v-91z"></path><defs><linearGradient id="O" x1="320.33" y1="243.782" x2="309.18" y2="269.082" xlink:href="#B"><stop offset="0" stop-color="#444343"></stop><stop offset="1" stop-color="#818181"></stop></linearGradient></defs><path fill="url(#O)" d="M294 231c0 1 4 3 4 4 0 9 6 21 12 28 4 4 13 5 19 5 0 0 2 1 2 0 2-1 4-1 6-2 1 2 4 5 7 5h0c-5 3-11 3-16 3h-5s-1 1-2 1c-5-2-9-6-14-9v-2-1c-1 0-1-1-2-1l-3-4c-5-7-9-18-8-27z"></path><path d="M133 192c7 0 15 1 22 0 0 9 1 16 4 25l-10-11c-1-1-2-3-4-4h0c-5 0-10-2-15-2-2-1-7-1-10 0-1 1-2 2-3 2h0c1-3 6-5 9-7 1-1 3-2 4-3h3z" class="B"></path><path d="M152 72c4 0 9 1 13 4s7 8 8 14c1 5-1 11-4 15-2 3-5 5-9 6-5 0-7-1-10-3 1 0 3 1 4 1 3 1 6 1 9-1s7-7 8-11 0-9-3-13c-2-4-6-7-12-8-5-1-10 0-14 3-7 4-11 12-12 20-1 7-1 14 1 21 4 10 12 18 21 22 3 2 5 2 8 3-6 1-11-1-16-5-8-6-15-16-17-26-1-12 0-23 8-33 4-5 10-8 17-9zm133 0c6 0 12 3 17 7 7 6 10 15 11 24 1 11-3 22-10 30-4 5-10 10-16 12-3 1-6 0-9 1 9-4 16-6 22-14 7-7 11-18 10-28 0-8-3-16-9-22-4-4-10-7-16-7-5 1-9 2-12 6-4 4-5 9-5 15 1 4 3 7 6 10s7 4 11 3c2 0 3-1 5-1l-6 3c-4 0-8-1-11-3-4-3-6-8-7-12-1-6 1-12 4-17 4-4 9-6 15-7z" class="H"></path><path d="M283 228h2c0 4-7 8-10 12s-5 9-7 14c-3 10-4 19-3 29 1 3 1 7 2 10-3 3-7 3-10 4h-2c0-2 2-4 2-6 1-4 2-8 2-12 1-6 2-13 2-20 1-9 1-20 6-27l1-1s1-1 1-2c1 0 5 3 7 3s4-4 7-4z" class="D"></path><path d="M218 334h3v1 15 32 105c0 13 1 26 0 38 0 4 0 8-1 11-1-5-2-10-2-16-1-7 0-15 0-23v-33-95-35z" class="H"></path><path d="M291 271v5c1 1 1 2 2 3 2 3 5 4 9 5 11 2 21 1 31-5l1-1c-4-1-8-1-13-3 1 0 2-1 2-1 9 4 18-1 27-1h1c-1 1-1 2-2 2-3 2-6 3-9 4-4 3-8 6-13 8-10 4-20 2-30 2 8 6 15 8 24 8l-16 4h0c4 2 7 6 11 9 4 2 9 5 13 5h9c3 0 7 1 10 2-3 1-6 3-9 4-5 1-12-2-17-4-6-2-11-6-16-9l11 19c-3-2-5-5-7-7-8-8-13-17-18-27-1-2-2-4-2-5-1-1-1-2-1-3 0-2 1-5 1-7 1-2 1-5 1-7z" class="D"></path><path d="M290 227l4 4c-1 9 3 20 8 27l3 4c1 0 1 1 2 1v1 2c5 3 9 7 14 9s9 2 13 3l-1 1c-10 6-20 7-31 5-4-1-7-2-9-5-1-1-1-2-2-3v-5l-2-15c1-2 0-3 0-4 0-2 1-4 0-5-1-2 0-4 1-6 0-3 1-11 0-14z" class="J"></path><path d="M295 255c-2-2-4-6-5-9v-5l5 12v2z" class="B"></path><path d="M295 253c2 2 5 4 7 7 1 0 2 2 3 2h0c1 0 1 1 2 1v1 2c-4-3-9-6-12-11v-2z" class="E"></path><path d="M302 200c-2 0-4 1-7 1l-16 14c-2 2-4 5-7 7l16-23c-3 0-7 0-9 1-3 1-5 4-6 5-7 7-12 15-16 24-2 3-3 6-5 9 0 2-1 3-2 3-1 1-2 1-2 0-1 0-2-1-3-3 0-1 0-3 1-5 0-2 2-4 3-5l7-10 9-14c-6 4-11 10-16 16 3-7 6-13 8-20 1-3 1-6 1-9l43 1c1 1 1 3 1 4l2 2-2 2z" class="B"></path><defs><linearGradient id="P" x1="230.25" y1="200.265" x2="236.584" y2="296.143" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#cfcece"></stop></linearGradient></defs><path fill="url(#P)" d="M226 202l1-2h4c3 0 7-1 9 0 0 2 0 3 1 5v7 19 66h-15v-95z"></path><defs><linearGradient id="Q" x1="216.745" y1="196.314" x2="195.282" y2="294.89" xlink:href="#B"><stop offset="0" stop-color="#a4a4a3"></stop><stop offset="1" stop-color="#cfcdce"></stop></linearGradient></defs><path fill="url(#Q)" d="M200 200h1l1-1h-2v-3h12c0 1 0 2 1 3h0v1 97h-14v-64-20-11c1 0 1-1 1-2z"></path><path d="M96 248c5-2 8-5 11-10 1-1 2-3 3-4h1v1 6c-1 6-2 12-6 17-5 6-12 9-20 10v3c0 11 5 21 14 28 6 6 13 8 21 7h0c-2 1-6 1-8 1-8 0-17-5-22-11-6-7-9-15-12-24-1-2-1-4-2-7-2-5-5-10-5-16h0c-1 1-2 4-2 6-2 5-3 11-1 17 1 3 2 5 1 9 0 1-1 1-2 1s-2 0-2-1c-2-1-2-4-2-6 0-6 0-13 1-19 1-12 6-23 11-34 2-4 5-9 8-13a30.44 30.44 0 0 1 8-8c2-2 4-3 5-4-4 2-9 4-13 6l-4 4c-2 1-3 2-6 1h0c1-1 1-2 1-3 5-6 14-9 22-12 3-1 7-2 11-2 0 2 1 5 1 8-5 1-9 4-12 7-6 5-11 10-14 17-4 10-6 22-2 32 1 3 3 5 5 6 3 1 6 0 9-1 5-2 9-6 11-10l1-4-8 3-2-1zm230-97c1 2 0 7 0 9v30c6 1 13 2 18 4v1c2 0 3 1 4 1 1 1 3 1 5 2 1 1 6 3 6 5v-1h-3-3c0-1 0-1-1-1l-9-4c-1 0-2-1-3-1v1l1 1h1c4 2 8 5 11 8 2 2 3 3 4 5 2 2 4 5 5 8 5 10 10 20 12 31 1 5 4 26 0 31 0 1-1 1-2 1s-2-1-2-2c-2-2 1-9 2-12 0-7-2-14-4-21 0 3 0 6-1 8-1 3-3 6-4 9-2 5-3 10-5 16-3 10-11 19-20 24-6 3-13 4-19 2h-1c8 0 15-1 21-6 9-8 14-18 15-30v-2c-8-1-14-3-20-9-4-5-5-11-6-18v-6-1h1c2 2 3 5 4 7 3 4 8 7 13 8 2-1 4-3 5-5 1-4 1-8 2-12 0 7 1 14 0 21 0 2-1 3-1 5h-1c-3 0-5 0-7-1l-1-1c1-2 1-3 1-5-3-2-7-3-11-4 2 6 4 9 10 12 3 2 6 3 10 2 2-1 4-3 5-5 4-8 3-20 1-28-4-11-14-21-23-26l-9-4h-1v2h0v-49z" class="D"></path><defs><linearGradient id="R" x1="111.301" y1="244.537" x2="137.776" y2="315.951" xlink:href="#B"><stop offset="0" stop-color="#626263"></stop><stop offset="1" stop-color="#8e8d8d"></stop></linearGradient></defs><path fill="url(#R)" d="M144 232c3-2 4-5 5-9 1 1 1 1 1 2l2 7c1 2 1 3 1 4 2 4 2 7 3 11 0 6 0 12-1 18-1 8-4 17-8 24-5 12-11 23-20 33-2 2-4 4-6 5l4-7 3-6c2-2 4-4 4-6-7 6-15 10-25 12-2 1-5 2-7 1-3 0-6-3-9-4 3 0 5-1 8-2h8c7 0 13-3 17-7 3-2 6-5 9-7-5-1-10-1-15-3 8-1 16-3 23-9h0c-11 0-21 2-31-4l-11-6c-3-1-8-2-10-4-1-1-1-2-1-2 1-1 4 0 6 1 3 0 6 1 10 1 4 1 9 0 13-2-6 1-12 2-18 0l1-1 1-1c2-1 3-2 4-3 1 0 2 1 3 1 2 0 4 0 6-1 4 0 9-1 13-3 1-2 2-4 4-5 5-8 8-16 9-25h0l4-3z"></path><path d="M149 237c0-1 0-2 1-2v-1s1-1 1-2h1c1 2 1 3 1 4h0c-1-1-1-2-1-3l-1 1v2c1 1-1 7-2 9v-1h-1c0-3 1-5 1-7z" class="F"></path><path d="M144 232c3-2 4-5 5-9 1 1 1 1 1 2l2 7h-1c0 1-1 2-1 2v1c-1 0-1 1-1 2 0 2-1 4-1 7-2 3-3 7-6 10-2 2-3 4-5 6-1 0-2 2-2 2-1 0-2 1-2 1 5-8 12-16 12-26 0-3 0-3-1-5z" class="I"></path><path d="M149 237c0-3-1-9 1-12l2 7h-1c0 1-1 2-1 2v1c-1 0-1 1-1 2z" class="C"></path><path d="M148 244h1v1c1 10 0 21-2 31 0 1 0 4-1 5s-3 1-4 2c-4 1-10 2-15 2-7 0-15-2-21-7 6-1 12-2 17-6 3-1 5-3 7-5 2-1 4-2 6-4h0-1v-1s1-2 2-2c2-2 3-4 5-6 3-3 4-7 6-10z" class="J"></path><path d="M148 244h1c-2 7-4 11-9 16-1 1-2 2-4 3h0 0-1v-1s1-2 2-2c2-2 3-4 5-6 3-3 4-7 6-10z" class="E"></path><path fill="#e9e8e8" d="M144 116c4 4 9 6 16 6s12-2 17-7c4-4 6-9 8-13 3-10 5-19 9-29 0 7 0 15-1 22-1 3-2 5-3 8 4-6 8-12 11-18s5-13 7-19c4-11 7-22 9-34 1-7 2-14 2-21 2 6 2 12 3 18 2 13 5 26 9 38 2 8 5 16 10 23 2 4 5 8 8 12-1-2-2-4-2-6-1-4-1-9-1-13 0-3-1-7-1-10l5 16c3 8 5 16 9 23 3 3 6 6 9 8 5 2 13 3 18 1 4-1 6-3 9-5h1c2-3 3-6 3-10v-4c-1-2-1-3-2-5h0c1 1 2 2 2 4v1l1 1v6c-1 1 0 2-1 3s-1 2-2 3v2c-1 1-1 1-1 2h1l-3 3c-3 3-7 6-11 7-9 2-18-3-27-4-4 0-9 1-12 5-4 3-3 10-3 15h-15v-10c1-6 4-12 9-16 4-3 10-5 15-5h2c-4-2-7-6-10-9-2 3-4 3-6 4h-3c-5 6-9 11-11 19-1 4-1 7-1 11 0 1 1 5 0 6h-3c-1-2 0-12 0-15-1-2-1-4-2-7-3-8-10-15-14-22l-15 14c8 0 15 1 20 7 6 6 7 14 6 23h-14c0-5 1-11-3-15-3-3-7-5-12-5-13 1-26 9-38-2-2-2-4-4-5-7h1v-1c-1 0-1-1-1-1-1-1-1-2-1-4-1-2-1-5 0-6 0-4 0-6 3-9 0 1-2 4-2 5v1h0c-1 1-1 6-1 7v2c1 1 1 1 1 2h0c1 1 1 3 2 4h1z"></path><path d="M233 109c2-3 3-6 4-9l5 5c-2 3-4 3-6 4h-3z" class="C"></path><defs><linearGradient id="S" x1="230.08" y1="81.472" x2="221.145" y2="106.446" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#818081"></stop></linearGradient></defs><path fill="url(#S)" d="M224 77l9 19c-2 2-4 4-5 7-2 2-4 5-5 8-1-3 0-5 0-8V84v-5s1-1 1-2z"></path><defs><linearGradient id="T" x1="221.732" y1="80.349" x2="209.571" y2="104.773" xlink:href="#B"><stop offset="0" stop-color="#626163"></stop><stop offset="1" stop-color="#858585"></stop></linearGradient></defs><path fill="url(#T)" d="M216 75c2 2 1 24 1 29v7l-11-16c1-3 4-6 5-9l5-11z"></path><path d="M216 75l4-9 4 11c0 1-1 2-1 2v5 19c0 3-1 5 0 8l-3 7-3-7v-7c0-5 1-27-1-29z" class="J"></path><defs><linearGradient id="U" x1="145.054" y1="379.042" x2="301.995" y2="619.439" xlink:href="#B"><stop offset="0" stop-color="#dad9d9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#U)" d="M199 348c0-1 1-1 1-1 0-1-1-3 1-3 0-1 1-1 2-1h4 1 3 1 0c0 2 0 4 1 5 1 4 0 8 0 12v28 82 33c0 7 0 14 1 21 0 8 1 15 2 23 1 7 2 15 4 22 1-5 1-10 2-15l2-22c1-4 2-9 2-13v-24-35-113c1 0 1-1 1-1 0-1 0-2 1-3h4 2c1 1 3 1 4 0h2c0 2-1 3 1 4v117c-1 3 0 6 0 9v17 48 23c-1 3-1 6-2 8l-5 32-9 48-4 18c0 4-1 8-2 12 0-8-2-15-4-23l-10-55-4-25c0-4-1-8-2-12v-22-47-147z"></path><path d="M107 191c-1-7 0-13 2-19 3-6 7-11 12-16 0 0 1-2 2-2h7 23 106 32 23c4-1 8-1 12-3v49 5c-1 1 0 4-1 5h-1c-2 3-5 6-7 8 0-7-1-13-3-20-1-3-2-5-3-8-4-7-9-12-17-15s-18-2-27-2h-27-49v134h45 15c4 0 9 0 13-1s8-4 11-7c5-5 10-12 10-19 0-1-1-3-1-5 3-4 4-10 4-15 0-1 0-5 1-6v2l2 15c0 2 0 5-1 7 0 2-1 5-1 7-2 9-2 19-2 29v35 10 4c-1 1-1 2-2 2-2 4-5 6-8 9 4-10 2-22-3-31-3-9-11-17-20-21-10-4-21-3-31-3l-18 1h-14v57 25c0 9 0 18-3 28-2 12-9 25-18 34-9 10-22 14-35 14-9 0-20-2-26-9l-4-4c0-1-1-4-2-6-1-1-1-3-2-4 2-1 4-2 6-2v2c1 1 0 1 0 1 1 2 2 4 3 5h1c2 3 4 5 7 7-4-5-6-9-7-16 0-5 1-10 4-14 3-3 5-5 10-5 2 0 4 0 6 2 3 2 4 5 4 9 0 3 0 6-1 7h-1c0-2-1-4-2-6-1-1-2-1-4-1s-4 0-5 1c-2 2-3 4-2 6 0 4 2 9 5 12s8 5 12 5c6-1 12-4 16-8 6-7 10-14 12-23 2-13 1-27 1-41v-59-105-58c-12 0-24-1-36 2-4 1-10 3-13 7-3 2-4 4-5 7 0 4 0 7 3 10 0 1 1 1 2 2h0v1c1 0 1 1 1 2l-1 2c-1 1-3 2-4 3-1-1-1-2-2-2v-1l-1-1c0-1-1-3-1-3l-1-4c0-3-1-6-1-8z" class="H"></path><path d="M111 207l6-4c1 0 1 1 1 2l-1 2c-1 1-3 2-4 3-1-1-1-2-2-2v-1z" class="B"></path></svg>