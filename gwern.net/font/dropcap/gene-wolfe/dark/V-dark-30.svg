<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="96 18 850 980"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#151111}.C{fill:#bfbcba}.D{fill:#e6e4e0}.E{fill:#942e29}.F{fill:#bf302b}.G{fill:#ddd5d2}.H{fill:#881515}.I{fill:#e2ddd9}.J{fill:#0e0e0e}.K{fill:#5b0b0b}.L{fill:#d1cecb}.M{fill:#201f20}.N{fill:#230706}.O{fill:#7a1817}.P{fill:#720e0e}.Q{fill:#5e0d0d}.R{fill:#3b0f10}.S{fill:#a12925}.T{fill:#222021}.U{fill:#bd2f2c}.V{fill:#383738}.W{fill:#400808}.X{fill:#a8a6a5}.Y{fill:#474748}.Z{fill:#7e1c1b}.a{fill:#4f1011}.b{fill:#421111}.c{fill:#edecea}.d{fill:#511111}.e{fill:#921312}.f{fill:#2a2a2a}.g{fill:#403f40}.h{fill:#330808}.i{fill:#cac7c4}.j{fill:#7b7a7b}.k{fill:#621918}.l{fill:#c2c0bd}.m{fill:#380808}.n{fill:#8a1514}.o{fill:#8e1918}.p{fill:#d7514b}.q{fill:#ba4039}.r{fill:#9a9898}.s{fill:#292728}.t{fill:#5a5a5b}.u{fill:#f7f6f5}.v{fill:#d26c67}.w{fill:#d5312c}.x{fill:#2f0c0c}.y{fill:#828182}.z{fill:#636469}.AA{fill:#480d0e}.AB{fill:#b91b18}.AC{fill:#2e2e2e}.AD{fill:#e69f9b}.AE{fill:#6e6e70}.AF{fill:#515052}.AG{fill:#040404}.AH{fill:#ca8a84}.AI{fill:#bd413e}.AJ{fill:#e2b2af}.AK{fill:#090707}.AL{fill:#dc5d55}.AM{fill:#f2cdca}.AN{fill:#e8c9c8}.AO{fill:#e4afac}.AP{fill:#b41715}.AQ{fill:#e38582}.AR{fill:#f2cecb}.AS{fill:#ee8d89}.AT{fill:#f4b6b3}.AU{fill:#d6bcb6}.AV{fill:#e87876}.AW{fill:#7d7c7c}.AX{fill:#955855}.AY{fill:#6a4340}.AZ{fill:#77544f}</style><path d="M576 804v-2c-1-1-1-2 0-3h1l1 2c-1 1-1 1-2 3z" class="T"></path><path d="M503 184h1c0 2 2 3 3 5l-1 1c-2-2-3-3-3-6z" class="B"></path><path d="M808 246h5l-2 1v1h-5l2-2z" class="r"></path><path d="M327 748c0-1 1-2 1-3 2 0 2 0 4 1-2 2-3 2-5 2zm171-565h3v3c-1 1-1 2-1 3v1c-1-2-1-4-1-6-1 0 0 0-1 1v-1-1zm174 572v-1c1 0 1-1 1-2l2 1v1 1 1l-3 2v-1-1-1z" class="B"></path><path d="M371 766l8 6-9-4 1-2z" class="s"></path><path d="M576 804c1-2 1-2 2-3 1 1 2 0 3 1l1 2-1 2c-1-2-1-2-2-3l-2 2-1-1z" class="f"></path><path d="M282 701l9 3c-2 1-4 1-6 1h0c-1-1-2-2-3-4z" class="t"></path><path d="M747 635h1v-1c1 0 1 0 1 1l1 1v1l-1 2-2-1-2 2v1c0-2 1-4 2-6z" class="M"></path><path d="M632 794h-2c-2-1-5-1-7-2h1c1-1 3-1 5-2v2h1c1 0 1 1 2 2h0z" class="V"></path><path d="M498 184v1h0v4 1l-1-1s-1-1-2-1-1 0-2 1h0c-1 1-1 1-1 2v-4l4-2 2-1z" class="f"></path><path d="M291 704h3l1 1h-2l1 1h0c0 1-1 1-2 1h-1c-2 0-4-1-6-2 2 0 4 0 6-1z" class="s"></path><path d="M265 423c0 2-1 3-1 4-1 1-1 1-1 2h1l-1 1c-2 1-4 2-6 2 3-2 5-5 8-9z" class="AV"></path><path d="M651 794c2-1 3 0 5 1 1 1 4 3 4 5-3-2-7-4-10-5 0-1 1-1 1-1z" class="t"></path><path d="M275 582l3 3c1 1 1 2 2 3l1 4s0-1-1-1v2l-5-11z" class="v"></path><path d="M292 696h1c0 2-1 3-1 4-2 1-2 1-4 1-2 1-2 1-3 0l-1-1h1l1-1h0v-1l2 2c1-1 2-3 4-4z" class="B"></path><path d="M601 99l-14-3c3-1 8 0 11 0 1 2 3 1 4 2-1 1 0 1-1 1z" class="D"></path><path d="M483 918h0c2 6 2 14 1 20v-2c-1-1-1-3-1-5 0-3-1-10 0-13z" class="M"></path><path d="M699 735c2 6 5 10 7 16-3-3-5-6-8-9l1-2c0-2-1-3 0-5z" class="F"></path><path d="M752 576c-1 3 0 8-2 10 0 2 0 6-1 7l-1-3c-1-2 0-5 1-8v-1c1-1 2-3 3-5z" class="M"></path><path d="M691 706l1-7h1c-1 8 0 15 1 22h-1c-1-2-1-3-1-5l-1-7v-3z" class="AP"></path><path d="M204 337c3-2 10-1 14 0h0c1 1 3 1 4 3l-18-3h0z" class="AH"></path><path d="M664 758h-1l-4 3h-1c3-3 6-8 9-10 2 0 2 0 3 1s1 2 1 2v1h-1v-1-1l-3 1c0 1-1 1-1 1l-1 1v1l-1 1z" class="B"></path><path d="M763 417c-1 4-3 8-4 11-1-6 1-13 3-19 1 3 1 5 0 7l1 1z" class="f"></path><path d="M582 804l1 1c2 0 3-3 4-2h5c0 1-1 2-2 3h0-1c-1 1-2 1-3 2h-1-1l-3-2 1-2z" class="T"></path><path d="M370 768l-3-2c-2-1-9-6-9-8 4 1 9 5 13 8l-1 2z" class="V"></path><path d="M379 792c1 0 2 0 2 1-5 1-10 3-16 5l-2-1 6-3c3-1 6-1 10-2z" class="j"></path><path d="M766 392c-2-2-3-2-4-5 1-1 1-1 1 0 1 0 2 1 3 1 4-2 4-6 7-8l-3 6c-2 3-2 6-2 9h-1s0-1-1-2v-1z" class="X"></path><path d="M356 766c2 0 2 0 4 1 1 1 0 0 2 0l5 3c2 1 4 3 6 5l5 4c-2 0-5-1-8-1l3-1-3-3h0c-2-1-3-2-5-3-1 0-1-1-2-2h-2c-2-1-3-2-4-2l-1-1z" class="s"></path><path d="M646 191h3c2 1 4 3 5 5 3 1 5 3 8 5l-7-1-4-4c-1-2-3-4-5-5z" class="M"></path><path d="M445 226v1l7-4c-3 3-6 4-10 6 0 1-1 1-1 2-1 1-2 1-3 2-2-1-5 1-7 0-1 0-2 0-2-1 2 0 4 0 5-1 3-2 6-2 9-3l2-2z" class="N"></path><path d="M665 756l1-1s1 0 1-1l3-1v1 1h1 1v1 1 1l-3 1-4 4v-6-1z" class="f"></path><path d="M665 756l1-1s1 0 1-1l3-1v1 1h1 1v1 1 1l-3 1h0c0-1 0-2 1-3l-1-1-4 1z" class="T"></path><path d="M576 191v3h3c3 0 5 1 8 0l5-1h0c1-1 2-1 3-2h1c-2 4-5 4-8 4l-3 1h-2c-2 1-5 1-7 0v3-8z" class="M"></path><path d="M556 870c2 7 2 14 3 20h0v7h0c-1-2-2-3-2-4-1-6-2-13-1-18v-5z" class="B"></path><path d="M266 567h1l4 5c2 1 4 3 5 5l2 4c1 1 1 1 1 2l-1 2-3-3-6-11c0-1-2-3-3-4z" class="AH"></path><path d="M251 555h3c0 1 0 1 1 2 3 1 7 4 9 7l2 3c1 1 3 3 3 4-1 0-2 0-3-1-1-4-3-5-6-7l4 8c-1-1-2-2-3-4s-4-7-7-9h0c0-1 0 0 1-1-2 0-3-1-4-2z" class="B"></path><path d="M807 506c11 2 19 1 30-3v1c-8 5-22 5-30 4v-2z" class="AW"></path><path d="M555 850c2 0 3 1 4 3l2 11c-2-1-2-2-3-4l-1 1v-1c0 1 0 1-1 2v-2c0-2-1-8-1-10z" class="Y"></path><path d="M758 633h2 1v6c-2 5-5 11-8 16 0-1 1-3 1-4 1-5 3-10 5-14l-1-4z" class="f"></path><path d="M758 633h2 1l-2 4-1-4z" class="M"></path><path d="M389 799l1 1c-2 4-2 9-2 14-1 4 0 9 0 14-1-4-3-9-3-13 1-2 1-4 1-7h0c0-2 0-2 1-3s2-4 2-6z" class="AH"></path><path d="M283 605l1 1c1 3 1 6 2 10 1 0 1 1 2 2l-1 7c-1 2-1 4-1 6h-1c0-1-1-1-2-1l1-2c1-8 0-15-1-23z" class="AQ"></path><path d="M286 616c1 0 1 1 2 2l-1 7-1-9z" class="v"></path><path d="M258 492l2-1v5 3c-1 1-2 3-2 4v1l-3 5-1-2c1-2 1-3 1-5 1-4 2-7 3-10z" class="AM"></path><path d="M237 445c2-1 2-1 3 0h1c1 1 3 1 4 1 2 0 4 1 6 3l2 1c1 1 2 1 2 2l-7-2c2 1 3 3 5 5v1c1 0 1 1 1 2h-1l-1-2c-4-5-9-9-15-11z" class="k"></path><path d="M766 392v1c1 1 1 2 1 2h1c-1 8-3 15-5 22l-1-1c1-2 1-4 0-7 1 0 2-4 2-5 1-4 2-8 2-12z" class="AE"></path><path d="M253 715l-1 1 1 1c1-1 1-2 3-2v-1c2-1 4-2 7-2h1l1-1h2l2-1c1 0 1 0 2 1h0c-1 1-1 1-2 1h-2c-1 0-1 0-2 1h-2l-2 1c-1 0-3 1-5 1-2 1-4 3-5 4-3 2-5 5-7 6 1-1 2-3 2-5l1-1 3-3h0l3-1zm408-508c-3-3-4-1-7-2-5 0-10 0-15-2h-2-1c-1 0-2-1-2-1h-2s-1-1-2-1h0c-1 0-2 0-2-1l-5-2v-1c9 4 18 6 27 7 3 0 11 0 13 1v1l-2 1z" class="M"></path><path d="M642 780v-2h0l1-1c1-1 1-2 1-3h-2l8-6v1 2h4c-2 1-4 3-5 5l-4 2-3 2z" class="B"></path><path d="M642 780l3-2c0 1 0 2-1 3h2c2 3 4 7 6 11-3-1-5-3-7-5h2v-1h-3v-1h1l1-1-1-1h-6-6c3-1 5-1 8-3h1z" class="g"></path><path d="M779 489c4 5 9 9 15 12-1 1-2 1-2 1-2 0-4-1-5-2l-1-1c-2-2 1 1-1-1-2-1-3-3-5-3l-1 1c-1-1-2-1-2-2 0-3 0-3 2-5z" class="t"></path><path d="M385 794c1-2 1-3 1-5h0 0l2 2h0l1 1c-1 4-2 8-2 13-1 1-1 1-1 3h0c0 3 0 5-1 7-1-6-1-16 0-21z" class="AS"></path><path d="M280 593v-2c1 0 1 1 1 1 1 2 1 3 2 5l1 3 2 8c0 2 0 3 2 5v2 3c-1-1-1-2-2-2-1-4-1-7-2-10l-1-1-3-12z" class="p"></path><path d="M495 168h1l3 3v2a30.44 30.44 0 0 0 8 8h-1 0c-1 1-2 1-2 1l-3 1h-3l2-2-2-1c-1-3 0-5-1-8 0-1-1-2-1-3l-1-1z" class="AC"></path><path d="M253 458h1c4 6 6 12 7 19v11l-1 3-2 1c0-2 0-5 1-7 0-9-1-19-6-27z" class="AN"></path><path d="M259 485v1c1 1 1 1 1 2h0v-3l1 3-1 3-2 1c0-2 0-5 1-7z" class="G"></path><path d="M758 623c0 2 1 2 3 3h2l1 1c0 1 1 2 1 2v2c-1 1-1 2-2 4l-2 4v-6h-1-2c0-2 0-4-1-7l1-3z" class="g"></path><path d="M757 626c2 1 3 3 5 4l1 1c-1 1-1 2-2 2h-1-2c0-2 0-4-1-7z" class="f"></path><path d="M459 191l10 1v1c0 1 0 1-1 2-1 0-2 0-3 1h-1l3 2-1 1h-1 0l1 2h-1l-6-4h-2c1-1 1-1 2-1v-1h-1c0-1 1-1 2-3l-1-1z" class="M"></path><path d="M477 201l-2 4h0c-2 2-5 4-5 6v1h0c-3 1-6 2-10 2h-3l-1 2c0-2 1-2 1-4h0l3-1h0 1c1 0 3 0 5-1 3-1 8-7 11-9z" class="Z"></path><path d="M283 630c1 0 2 0 2 1l-1 3c-4 8-8 12-14 18l-1 1c-2 1-4 3-6 4-1 1-2 3-3 4l-1-1 8-7c8-7 13-14 16-23z" class="AD"></path><path d="M270 413v7l1 2c0 1 0 2 1 2-2 3-4 5-6 7-2 0-2 0-3-1l1-1h-1c0-1 0-1 1-2 0-1 1-2 1-4l5-10z" class="v"></path><path d="M270 420l1 2c0 1 0 2 1 2-2 3-4 5-6 7-2 0-2 0-3-1l1-1c2-2 5-6 6-9z" class="G"></path><path d="M254 619c1-1 2-1 2-2 4-3 4-7 6-9v1c0 1 0 1 1 2h0c0-1 1-1 2-2l1 1h1l-1 4v1c-1 1-1 0-1 1h-1v-2h-1l-2 6-1 2h-1c1-2 1-3 1-4l-1-1c0 2-2 2-2 4-1 1-1 1-2 1v-2l-1-1z" class="T"></path><path d="M255 502c0 2 0 3-1 5l1 2-1 1-8 11h0c-1-1-2-1-3-1l12-18z" class="AN"></path><path d="M785 443c14 2 29 5 43 10-3 0-6-1-9-2-4-1-9-2-13-2l-14-3h-5c1-1 2-1 3-1h2-1 0c-2 0-4 0-6-1v-1z" class="G"></path><path d="M695 218l-3-2v-1l-6-3h1l24 9-1 3v-1c-1-1-2 1-4 1 0 0-2-1-2-2-3-1-6-3-9-4z" class="T"></path><path d="M769 621c2 1 3 3 5 5-1 1-1 0-1 1-1 0-2 1-2 2h-1c-2 0-5 4-7 6 1-2 1-3 2-4v-2s-1-1-1-2l-1-1h0v-1h0 2c1-2 0-2 0-4h2l1 1 1-1z" class="Y"></path><path d="M765 621h2l1 1-1 1h2v2c-1 0-2 1-3 1h-3v-1h0 2c1-2 0-2 0-4z" class="AE"></path><path d="M574 193l1-13 1 11v8 1l1 5c2 7 6 10 11 14-2 0-3-1-4-2l-7-7c-1 2-2 5-4 7h-1l2-4h0v-5-4-3-8z" class="AO"></path><path d="M377 200c-2-3-4-5-4-9-1-4 2-5 4-8 1-1 2-2 1-3 0-5-9-10-13-13 6 3 12 6 15 12l1 1v1c-2 2-6 8-6 11 0 1 0 2 1 3 1 3 3 6 4 9h-1l-2-4z" class="X"></path><path d="M664 758l1-1v6l-1 1 2 1-2 4c1 0 1 0 2 1h-2v-1l-1-1c-1 0-2 2-3 4l-3 1-1-1c-2 2-4 3-7 4 1-2 3-4 5-5 1-1 2-2 4-2h0c1-1 2-1 3-2l-2-1c2 0 2 0 3-1-1-3 0-4 2-7z" class="T"></path><path d="M656 772c1 0 2-1 3-2 2-2 3-4 5-6l2 1-2 4c1 0 1 0 2 1h-2v-1l-1-1c-1 0-2 2-3 4l-3 1-1-1z" class="Y"></path><path d="M716 698l1-1c1-3 4-5 6-7l2-2h2c1 0 3-2 4-3l2 1-2 3-1 2c-1 1-3 2-5 3l-2 2c-2 0-4 2-7 2z" class="M"></path><defs><linearGradient id="A" x1="732.324" y1="677.376" x2="734.421" y2="692.309" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M733 686c1-1 1-2 1-4v-1c1-2 1-3 1-4h1l2-1c2 2 2 2 2 5l-1 1v-1c-3 1-2 1-3 3 0 2 0 2 1 4h0c-2 2-3 4-5 5l-2 1h-1-4c2-1 4-2 5-3l1-2 2-3z"></path><path d="M481 100v-1c1-2 1-5 0-8-1-1-3-3-2-4 1 1 4 5 4 7 2 5 1 12 1 17l-1-2h0c-1 1-1 3-1 4-1-3-4-7-5-10 2-1 2-1 3-3h1z" class="Y"></path><path d="M792 336c12-2 27-1 39 3-6 0-12-1-18-1-5-1-11 0-16 0h-4s-1-1-1-2z" class="y"></path><path d="M779 496l1-1c2 0 3 2 5 3 2 2-1-1 1 1l1 1c1 1 3 2 5 2 0 0 1 0 2-1 4 2 8 4 13 5v2l-14-3-6-2h-1l-3-3-4-4z" class="j"></path><path d="M748 660l5 2 8 5 2-2c7 7 11 12 14 21l-2-2c-5-10-16-19-27-23v-1z" class="E"></path><path d="M356 803c5 1 9-5 13-4h0c-4 2-9 4-13 6-6 5-10 13-16 18-2 3-4 4-7 6 2-4 5-7 7-9l8-9c1-2 3-3 5-5l3-3z" class="T"></path><path d="M543 199c4-10 6-20 7-31l1-10c0-3 0-5 1-7 1 10 0 20 0 30v1c-2 4-4 6-5 10-1 1-1 2-2 4l-1 2-1 1z" class="L"></path><path d="M769 612c2 2 5 5 8 6 15 10 34 14 48 24-2 0-6-2-7-3-6-2-11-4-17-6 0-1 0-1-1-2s-6-2-7-3c-6-3-12-5-17-9-1 0-2-1-3-1-1-2-4-4-4-6zM577 805l2-2c1 1 1 1 2 3l3 2h1 1c1-1 2-1 3-2h1l-4 6c3 2 8 5 10 9-2-2-4-4-7-5-4-2-8-3-13-1l-1-1c2-1 4-2 6-2h2l-1-1c-1-1-4-2-4-3v-1c0-1-1-2-1-2z" class="z"></path><path d="M558 871l3 4h0c1 2 1 3 1 4v1l-1-1v3c0 2 0 3 1 4 1 2 0 5 0 8 0 0 1 1 1 2 1 4 1 15 0 19l-3-27c0-2 0-4-1-6l-1-11z" class="s"></path><path d="M559 890c0-1 0-1 1-2l3 27c0 6-1 11-2 17 0 2-1 5-2 7 0-4 1-9 1-14 0-9 0-18-1-28v-7z" class="t"></path><path d="M327 748c2 0 3 0 5-2 3 2 8 4 10 7h-3c-1-1-6-4-8-4-1 0-1 1-2 1v1l-2 1c2 1 5 3 7 3v1l8 5c-2 0-6-3-9-3-2 0-5-2-7-3h1c-1-2-2-3-5-3h0c1-1 2-2 2-3v-1h3z" class="T"></path><path d="M646 191c-1 0-3 0-4-1l1-1h5v-9c-2-1-4-2-5-4 2 0 4 2 6 1 1 0 2-2 3-3 3-3 6-5 9-6-4 3-11 7-11 13-1 3-1 7 1 10 3 4 10 9 15 11h0v1l-4-2c-3-2-5-4-8-5-1-2-3-4-5-5h-3z" class="L"></path><path d="M424 232l4-1c1 0 6-1 7-1v-2l3-3c1-1 5-1 7-1 2-1 4-3 6-4-2 2-4 4-6 5v1l-2 2c-3 1-6 1-9 3-1 1-3 1-5 1 0 1 1 1 2 1 2 1 5-1 7 0-4 1-7 3-11 3-1-2-2-1-4-1l1-1v-2z" class="AA"></path><path d="M686 697c1 0 1-1 2-1h1l4-5c0-1 0-1 1-2 0-1 0-1 1-1 0 3-1 7-1 10 0 0 0 1-1 1h-1l-1 7-1 1h-4l-1 1c-1 1-2 1-2 2 0-2 0-3 1-4s2-2 3-4v-1c0-1 0-2 1-3l-2-1z" class="Q"></path><path d="M687 701c1 0 1 1 2 1v-1 1c1 0 1 1 1 2l-1 2c-1-2-1-3-2-4v-1z" class="d"></path><path d="M688 698h3c-1 1-1 2-2 3v1c-1 0-1-1-2-1 0-1 0-2 1-3z" class="b"></path><path d="M506 106c5-4 11-7 17-9 9-3 22-5 30-1h-2c-15-2-27 1-38 9-3 1-4 1-6 2l-1-1z" class="X"></path><path d="M675 756l5-1c0 1 1 1 1 2-4 3-8 6-11 9-3 1-3 1-4 4-1-1-1-1-2-1l2-4-2-1 1-1 4-4 3-1 3-2z" class="t"></path><path d="M656 772l1 1c-3 4-6 11-5 16v2l1 1h-1 0c-2-4-4-8-6-11h-2c1-1 1-2 1-3l4-2c3-1 5-2 7-4z" class="y"></path><path d="M334 755c-2 0-5-2-7-3l2-1v-1c1 0 1-1 2-1 2 0 7 3 8 4l1 1c2 3 5 5 7 9h-1c-1-1-2-2-4-2l-8-5v-1z" class="j"></path><path d="M334 755v-1c2-1 2-1 3 0l1 1c-1 1-2 0-4 1v-1z" class="y"></path><path d="M501 171c1 0 1 1 3 1 0 1 1 1 2 1l1 1h0l1 1c8 7 21 6 31 13-8-2-15-6-22-7 1 2 9 4 12 5h0c-4-1-8-1-12-3-2-1-5-3-7-4-4-2-7-5-10-7l1-1z" class="V"></path><path d="M249 675v1l-2 2c1 0 1-1 2-1s1-1 2-1c3 0 6-3 9-2-8 3-16 7-23 12-3 3-6 6-9 8 2-5 9-12 13-16 3 1 5-1 8-3z" class="E"></path><defs><linearGradient id="C" x1="747.584" y1="671.278" x2="749.706" y2="689.746" xlink:href="#B"><stop offset="0" stop-color="#0a0b0b"></stop><stop offset="1" stop-color="#29292a"></stop></linearGradient></defs><path fill="url(#C)" d="M745 674v-1c1-1 4-3 6-3h1c1 1 1 0 1 2l-1 1v-1-1l-1 1 1 1c-1 1-1 2-1 3s0 2-1 3v4 9l-1-3v2l-1-1c-1 1-1 3-1 4l-1-7c0-2 0-3-1-5 1 0 1-1 1-2h1v-7h-1l-1 1z"></path><path d="M275 697v-2l1 1h0l2-1h0v3h0l2-2c0 1-1 2-1 4l3 1c1 2 2 3 3 4-4 0-9 1-13 0v-1h-3c-1 1-2 1-3 1l-1-1c0-1 1-1 2-2h1c2-1 5-1 7-1v-1h-1l1-2v-1z" class="z"></path><path d="M275 698l1 1v2h1c1 0 2 1 3 1v1c-5 0-9 0-14 2l-1-1c0-1 1-1 2-2h1c2-1 5-1 7-1v-1h-1l1-2z" class="y"></path><path d="M270 382h0c3 6 4 14 3 21 0 5 0 9-1 13v2c1-1 2-2 2-3h1l-1 3h2c0 1-1 1-1 2l-3 4c-1 0-1-1-1-2l-1-2v-7c3-10 2-21 0-31z" class="AQ"></path><path d="M813 246h1c-3-2-7-3-11-3-1-1-2-1-3-1h0c8 0 18 2 24 8 2 3 5 6 6 10-1-1-2-2-2-3v1c1 3 2 6 3 10v4c-1-6-3-12-6-17-3-4-8-6-14-7v-1l2-1z" class="X"></path><path d="M284 634c1 3 0 5 0 8v2l-3 3-7 6-5 4-1-2 1-2 1-1c6-6 10-10 14-18z" class="F"></path><path d="M270 652c2 0 3 0 4-2s4-5 7-6h0c-1 1 0 2 0 3l-7 6-5 4-1-2 1-2 1-1z" class="U"></path><defs><linearGradient id="D" x1="782.492" y1="250.357" x2="805.486" y2="253.79" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#8c8b93"></stop></linearGradient></defs><path fill="url(#D)" d="M806 248c-9 1-16 5-23 10-2 1-4 3-6 4 1-2 2-3 3-4 4-5 12-10 18-12h10l-2 2z"></path><path d="M218 337c6 1 12 1 18 3 0 2 3 4 4 5h1l2 1h0c2 1 3 2 5 3 0 1 1 2 2 2l1 1c1 1 4 4 4 5-3-3-7-6-10-8-8-4-15-6-23-9-1-2-3-2-4-3z" class="E"></path><path d="M711 712c8-2 20-5 28-3l3 1 3 1c1 0 2 1 4 2h-1l-3-1h-1c-1-1-2-1-3-1-8-3-24-1-31 3-4 2-7 5-11 6-1-1-1-2 0-4 2-1 3-2 5-3h1c2-1 4-1 6-1z" class="s"></path><path d="M705 713c2-1 4-1 6-1-2 1-3 2-5 2-1 0-1 0-1-1z" class="M"></path><path d="M556 870l-2-13h0l2 3v2c1-1 1-1 1-2v1l1-1c1 2 1 3 3 4v5c1 2 0 4 0 6l-3-4 1 11c1 2 1 4 1 6-1 1-1 1-1 2h0c-1-6-1-13-3-20z" class="AF"></path><path d="M558 871l-1-8 4 6h0c1 2 0 4 0 6l-3-4z" class="M"></path><path d="M278 585l1-2 2 4 3-2c0 1 1 2 1 3l2 1 1 3 2 4c-2-1-3-1-5 0 0 1 1 3 1 4v2l2 11c-2-2-2-3-2-5l-2-8-1-3c-1-2-1-3-2-5l-1-4c-1-1-1-2-2-3z" class="AQ"></path><path d="M281 587l3-2c0 1 1 2 1 3s-1 2-2 3l-2-4z" class="q"></path><path d="M285 588l2 1 1 3 2 4c-2-1-3-1-5 0l-2-5c1-1 2-2 2-3z" class="F"></path><path d="M745 674l1-1h1v7h-1c0 1 0 2-1 2l-1 4-1 5h0c-1-1-2-1-3-2h0l-1-1h-2 0c-1-2-1-2-1-4 1-2 0-2 3-3v1l1-1h1 0l4-7z" class="B"></path><path d="M736 684c1-2 0-2 3-3v1 3l1 1c0-1 1-2 2-3 1 1 1 1 1 2l1 1-1 5h0c-1-1-2-1-3-2h0l-1-1h-2 0c-1-2-1-2-1-4z" class="f"></path><path d="M736 684c1-2 0-2 3-3v1 3l1 1-1 1h-1c0-2 0-2-1-3h-1z" class="T"></path><path d="M793 505c-2 0-2 0-3 1v2c0 1-1 2-2 3-1-1-2-2-2-3l-1-1v-2h-1c0-1-1-1-2-1 0-1 0 0-1-1 1 1 1 1 1 2-3-3-4-8-7-12-1-1-1-2-1-4 0-1 1-2 2-3l3 3c-2 2-2 2-2 5 0 1 1 1 2 2l4 4 3 3h1l6 2z" class="V"></path><path d="M762 606v-1l7 7c0 2 3 4 4 6 1 0 2 1 3 1 5 4 11 6 17 9 1 1 6 2 7 3s1 1 1 2c-14-4-28-10-37-23-1-2-1-3-2-4z" class="AF"></path><path d="M757 601h-1c-1-1-2-3-3-4-1-4-2-9-1-13v-1c2-4 3-8 5-12h0c-3 11-4 19 2 28l3 6v1c1 1 1 2 2 4-2-1-4-5-6-6-1-1-1-2-1-3z" class="z"></path><path d="M757 601l-2-7v-1 1c2 4 4 8 7 12 1 1 1 2 2 4-2-1-4-5-6-6-1-1-1-2-1-3z" class="B"></path><defs><linearGradient id="E" x1="244.892" y1="296.624" x2="233.635" y2="303.27" xlink:href="#B"><stop offset="0" stop-color="#737375"></stop><stop offset="1" stop-color="#9f9e9f"></stop></linearGradient></defs><path fill="url(#E)" d="M254 296l-1 2c-2 1-5 2-7 3 0 1 0 1 1 2h0c-9-2-20-2-29 3-4 3-7 6-10 10l-1-1c11-17 30-15 47-19z"></path><path d="M255 712v-1c8-6 12-13 14-22l1-1 1 4c1 2 2 4 3 5h1v1l-1 2h1v1c-2 0-5 0-7 1h-1c-1 1-2 1-2 2l1 1c1 0 2 0 3-1h3v1c-6 1-12 4-17 7z" class="AW"></path><path d="M267 702c1-2 2-4 2-5l2-2c0 1 1 1 1 2h2 1v1l-1 2h1v1c-2 0-5 0-7 1h-1z" class="j"></path><path d="M636 789h2c1-1 2-1 3-1 1-1 2 0 4-1 2 2 4 4 7 5h0l-1 1v1s-1 0-1 1l-11-2-7 1h0c-1-1-1-2-2-2h-1v-2l7-1z" class="Y"></path><path d="M636 789h2c1-1 2-1 3-1 1-1 2 0 4-1 2 2 4 4 7 5h0l-1 1-10-2c1-1 3-1 4-1v-1c-2-1-6 0-9 0z" class="f"></path><path d="M641 791l10 2v1s-1 0-1 1l-11-2h-5v-1c2-1 4-1 6-1h1z" class="j"></path><path d="M555 191l1-1c2 1 4 2 5 2l4 1c1 1 3 1 5 1h1l3-1v8 3c0-1 0-2-1-3s-2-1-3-1l-2-1h-3-1l-2-1-2-2c-2-1-4-2-5-5z" class="B"></path><path d="M570 194h-1c-1 0-2 0-3 1h0l-1-1h-2v2h-1c-1-1-1-2-2-3l1-1 4 1c1 1 3 1 5 1z" class="s"></path><path d="M574 193v8c0-1 0-2-2-3 0 0-1-1-2-1l1-1-1-1 1-1 3-1z" class="f"></path><path d="M357 767c1 0 2 1 4 2h2c1 1 1 2 2 2 2 1 3 2 5 3h0l3 3-3 1h0c-1 1 0 1-1 2h0c1 1 1 2 1 3l1-1 1 2c-1 2-2 3-4 4h0l-1 1v-1-1c0-1-1-1-1-2s0-1-1-2v-3l1-1-1-1c-1 0-1 0-2-1v-1c-1-2-1-2-2-3l1-2c-2 0-4-3-5-4z" class="AC"></path><path d="M365 780h1c1 1 1 1 2 3 0 1 0 2-1 4v1-1c0-1-1-1-1-2s0-1-1-2v-3z" class="g"></path><path d="M363 776c1-1 2-2 3-2l5 2-5 3-1-1c-1 0-1 0-2-1v-1z" class="AK"></path><path d="M254 619l1 1v2c1 0 1 0 2-1 0-2 2-2 2-4l1 1c0 1 0 2-1 4 0 2-1 4-2 7-1 4 2 11 3 15-2-3-3-10-5-12h-1 0c-2 1-2 3-2 5 1 2 2 5 3 7v5c-2-4-3-8-4-12-1-1-1-2-1-3s1-3 1-4c2-3 4-7 3-10v-1z" class="AC"></path><path d="M273 403c1 2 1 3 1 5l1 1v3c1-1 1 0 2-1h1c0-2 0-3 1-4l1 1c-1 2-1 3-1 5-1 1-1 2-1 3l1 1c0 1-2 4-3 6 0 0 1 1 1 2-1 2-4 3-4 5-3 1-5 1-7 1 2-2 4-4 6-7l3-4c0-1 1-1 1-2h-2l1-3h-1c0 1-1 2-2 3v-2c1-4 1-8 1-13z" class="H"></path><path d="M482 193l2-2h0c0 2-2 5-1 7h1v-1l3 1v-1h3c-1 1-2 4-4 5-1 0-2 1-2 2h-1v-1l-1-1c-1 1-3 3-4 3l-2 2c0 1 0 1 1 2v1c-2 1-3 2-5 2h-1-1 0v-1c0-2 3-4 5-6h0l2-4 5-8z" class="T"></path><path d="M484 197l3 1-1 2h-1-1c-2 1-2 1-4 1 0-1 1-2 2-2 0 1 0 0 1 1l1-2v-1z" class="M"></path><path d="M482 193l2-2h0c0 2-2 5-1 7h1l-1 2c-1-1-1 0-1-1-1 0-2 1-2 2-1 1-2 3-5 4l2-4 5-8z" class="Y"></path><path d="M798 540c7 2 15 4 21 7-3 1-6 0-9-1l-11-1c-7-1-15-2-22 0l-8 2h-1c1-1 3-2 4-2h1l3-1c1-1 2 0 4-1 4-1 9 0 14-1 1 0 3-1 4-2z" class="R"></path><path d="M752 576c2-6 5-11 10-16l-5 11h0c-2 4-3 8-5 12v1c-1 4 0 9 1 13 1 1 2 3 3 4h1c0 1 0 2 1 3 1 4 3 7 6 10l-2-1c-1 0-1 0-2-1-1 0-1 0-2-1s0-1 0-1l-2-2-1 1h0c-1-2-1-6-2-9 0-1 0-3-1-4-1-3-1-6-2-10 2-2 1-7 2-10z" class="g"></path><path d="M831 272v-4c-1-4-2-7-3-10v-1c0 1 1 2 2 3 5 9 4 22 1 31-3 11-10 22-18 29l-1-1c9-9 15-19 18-31 1-6 1-11 1-16z" class="C"></path><defs><linearGradient id="F" x1="725.947" y1="697.818" x2="742.607" y2="695.173" xlink:href="#B"><stop offset="0" stop-color="#3b3a3c"></stop><stop offset="1" stop-color="#525254"></stop></linearGradient></defs><path fill="url(#F)" d="M737 688h2l1 1h0c1 1 2 1 3 2h0v2l1 3c-1 1 0 2 0 4h-2 0c-1-1-3-1-4-2-5-2-9-2-15-2l2-2h4 1l2-1c2-1 3-3 5-5z"></path><path d="M737 688h2l1 1h0c1 1 2 1 3 2h0v2c-1 1-2 0-3 0-2 1-3 2-5 2-2-1-3-1-5-1l2-1c2-1 3-3 5-5z" class="g"></path><path d="M737 688h2l1 1h0-1c0 1 0 1-1 1-1 2-2 2-4 3h-2c2-1 3-3 5-5z" class="M"></path><path d="M254 458c0-1 0-2-1-2v-1c-2-2-3-4-5-5l7 2v2c2-1 2 0 4 1h1c2 5 3 12 3 17v2c0 1-1 2-1 4l-1-1h0c-1-7-3-13-7-19z" class="b"></path><path d="M255 454c2-1 2 0 4 1v4s-1-1-1-2h-1c-1-1-1-2-2-3z" class="m"></path><path d="M254 620c1 3-1 7-3 10 0 1-1 3-1 4s0 2 1 3c1 4 2 8 4 12 0 2 2 10 3 11l-1 1c-2-2-2-5-2-8-2-4-3-9-5-13-3-5-6-6-11-8 6-3 10-7 15-12z" class="z"></path><path d="M428 226c5-1 11-1 16-4 4-2 7-4 11-7 1 0 1 0 2 1v5h-1v-3h-1-1l-3 2c-2 1-4 3-6 4-2 0-6 0-7 1l-3 3v2c-1 0-6 1-7 1l-4 1h0l-3-1v-1c-1-1-1-1-1-2 2-1 4 0 6-1 0 0 1-1 2-1z" class="K"></path><path d="M258 660h1l1 1c-2 1-5 3-6 5h0c-1 1-1 1-1 2h1c-5 3-9 6-13 10s-11 11-13 16c-1 0-2 2-2 2-2 2-4 5-6 7 1-4 4-7 6-10 6-9 13-16 20-23l11-9 1-1z" class="AO"></path><path d="M767 516v-1c4 8 10 16 19 19 14 5 29 8 41 16v1l-8-4c-6-3-14-5-21-7l-16-4v-1c-1-1-1-1-2-1v-1l-4-4c-4-4-7-8-9-13z" class="I"></path><path d="M755 609l1-1 2 2s-1 0 0 1 1 1 2 1c1 1 1 1 2 1l2 1c2 2 4 4 5 7l-1 1-1-1h-2c0 2 1 2 0 4h-2 0v1h0-2c-2-1-3-1-3-3l-1-4v-1c-1-3-1-6-2-9z" class="t"></path><path d="M755 609l1-1 2 2s-1 0 0 1 1 1 2 1c1 1 1 1 2 1l2 1c2 2 4 4 5 7-5 0-7-8-11-9-1 1-1 4-1 6-1-3-1-6-2-9z" class="Y"></path><path d="M761 626l-1-1c-1-2-1-4-2-7l2-2c1 1 1 2 2 3v1l1 2h0v-1h2c0 2 1 2 0 4h-2 0v1h0-2z" class="AW"></path><path d="M763 625c-1 0-1-1-2-1 0-2 0-3 1-4l1 2h0v-1h2c0 2 1 2 0 4h-2 0z" class="y"></path><path d="M693 735h0l5 7c3 3 5 6 8 9 6 11 13 22 18 34 0 2 2 5 2 7l-6-10c0-4-2-7-4-10-4-7-8-13-12-19l-12-17 1-1z" class="AO"></path><path d="M409 231l4 1h2c3 1 5 1 9 0h0v2l-1 1c2 0 3-1 4 1h-1c-1 1-3 1-5 2h-3c0 1-1 1-2 1v1h1c1 0 1 1 3 1h0l2 2c-1 0-3 0-5-1h-3-1c-1-1-2-2-3-2v-4l1-1v-1l-5-2c1 0 2-1 3-1z" class="h"></path><path d="M410 236h1c2 1 4 1 6 1h0c-2 1-3 0-5 2l1 2h1v1h-1c-1-1-2-2-3-2v-4z" class="b"></path><path d="M418 234h2c2 2 2 1 4 2-2 1-5 1-7 1h0c-2 0-4 0-6-1 2 0 5 0 7-1v-1z" class="K"></path><path d="M415 232c3 1 5 1 9 0h0v2l-1 1c2 0 3-1 4 1h-3c-2-1-2 0-4-2h-2l-2-1-1 1-1-1 1-1z" class="Q"></path><path d="M409 231l4 1h2l-1 1 1 1 1-1 2 1v1c-2 1-5 1-7 1h-1l1-1v-1l-5-2c1 0 2-1 3-1z" class="Z"></path><path d="M347 768h1c5 6 8 11 10 18 2 6-1 10-2 15h0c0 1 1 0 2 0 1-1 1-2 1-3l1-5 3 1-3 4v1c1-1 2-1 3-2l2 1c-3 2-6 3-9 5l-3 3c-2 2-4 3-5 5l-8 9c-2 2-5 5-7 9l-3 3c1-3 4-6 5-8 8-9 20-20 22-32 1-10-4-17-10-24z" class="y"></path><defs><linearGradient id="G" x1="407.523" y1="200.006" x2="417.977" y2="190.994" xlink:href="#B"><stop offset="0" stop-color="#5f5f60"></stop><stop offset="1" stop-color="#747377"></stop></linearGradient></defs><path fill="url(#G)" d="M381 180l6 6c9 6 18 10 29 8 3 0 7-2 9-5 1-1 2-3 2-5h1v1c-2 5-2 9-8 11-2 1-5 2-8 2-5-1-11 0-17 1v-1c3 0 6 0 8-2v-1c-6-2-11-4-16-8-2-1-5-4-6-6v-1z"></path><defs><linearGradient id="H" x1="559" y1="829.957" x2="567.382" y2="835.625" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#H)" d="M555 850c-1-4-1-9 0-12s2-5 3-7 2-2 2-4c1 0 2-1 2-2 1-2 3-3 4-4 2-3 5-6 9-7l1 1c-4 2-7 5-9 8-2 1-2 2-3 3-1 2-2 2-3 4v1c-3 6-2 15-2 22-1-2-2-3-4-3z"></path><path d="M261 620l2-6h1v2h1c0-1 0 0 1-1v-1l-2 14 1 25-1 1-2-14c-1-5-3-12-2-18l1-2z" class="V"></path><path d="M261 620l2-6h1v2h1c0-1 0 0 1-1v-1l-2 14v3h0c-3-2-2-5-2-8 0-1-1-2-1-3z" class="Y"></path><path d="M217 548v2c1 1 1 1 3 1l1-1c2 0 5 1 7 0h10c1 0 1 1 1 1-14 0-25 4-37 11-2 1-5 3-7 4 3-5 9-9 13-12l9-6z" class="E"></path><path d="M217 548v2c1 1 1 1 3 1-4 2-7 4-12 3l9-6z" class="m"></path><path d="M201 337c-5 0-10 1-15 2h0v-1l15-3c0-7 2-14 6-20l1 1c-3 6-4 12-5 19 14 0 28 1 41 6 4 2 7 4 11 5h-4 0c-4-3-10-5-15-6-6-2-12-2-18-3h0c-4-1-11-2-14 0h-3z" class="L"></path><path d="M507 107c2-1 3-1 6-2-10 9-18 19-19 32 0 5 0 11 3 16l3 6-4-4v-1c-2-4-3-9-3-14h-1l-1-1c0-3 1-6 0-9 2-5 5-10 9-14 1-2 2-3 3-5 1-1 2-3 4-4z" class="r"></path><path d="M500 116c0 2-3 5-4 7-2 6-3 11-3 17h-1l-1-1c0-3 1-6 0-9 2-5 5-10 9-14z" class="V"></path><path d="M371 782c1 1 2 1 3 2h-1v1 1c1-1 1-1 3-1l1 3c1 0 0 1 1 0 1 1 1 0 2 1h-1l-1 1h1l4 2h0s-1 1-2 1c0-1-1-1-2-1-4 1-7 1-10 2l-6 3c-1 1-2 1-3 2v-1l3-4h0v-5l1-3 1-1v-2c1 1 1 1 1 2s1 1 1 2v1 1l1-1h0c2-1 3-2 4-4l-1-2z" class="B"></path><path d="M371 788h1v1l-1 1h1l1 1c1 1 4 1 6 1-4 1-7 1-10 2 0-1 1-1 2-2l-1-1h-1l2-2v-1z" class="f"></path><path d="M365 783c1 1 1 1 1 2s1 1 1 2v1 1l1-1h0 3v1l-2 2h1l1 1c-1 1-2 1-2 2l-6 3c-1 1-2 1-3 2v-1l3-4h0v-5l1-3 1-1v-2z" class="V"></path><path d="M365 783c1 1 1 1 1 2s1 1 1 2v1 1l1-1v2c-1 2-3 3-5 4v-5l1-3 1-1v-2z" class="Y"></path><path d="M427 184c0-1 1-3 1-5 0-7-5-17-7-24-1-3-2-5-2-8v-3l-2-8v-3c-1-1-1-2-1-3v-6-1c2 2 1 5 2 7v3c0 1 0 2 1 4v1l2 8 2 5c1 4 3 8 6 12 4 6 7 12 13 17 1 1 2 3 3 4 1 0 2 1 2 1v1h0l-2-2c-3-1-8-6-10-9 0-1 0 0-1-1l-1-2-4-6-4-8-1 1c1 6 4 12 5 18v4l-1 3h-1z" class="g"></path><path d="M792 446l14 3c4 0 9 1 13 2 3 1 6 2 9 2 4 0 9 3 12 4v1l-13-4c-2-1-5-1-8-1-9-1-18-3-27-1s-18 6-23 15c-2 2-3 5-4 8l-1-1c2-3 3-6 5-9 3-4 6-7 10-10 3-1 7-3 10-3 2-1 3-2 5-3v-1h1c-1-1-1 0-2-1l-1-1z" class="E"></path><path d="M792 446l14 3c-2 1-8 1-11 2l-6 1c2-1 3-2 5-3v-1h1c-1-1-1 0-2-1l-1-1z" class="d"></path><path d="M488 143v-3c1-2 0-5 2-8 0 3 0 4 1 7l1 1 1 11c1 2 1 4 2 6 0 6 8 13 12 17l-1-1c-1 0-2 0-2-1-2 0-2-1-3-1l-1 1-1-1-3-3h-1c-2-1-3-8-4-11-2-5-3-9-3-14z" class="AF"></path><path d="M501 171l-2-1c-1-2-2-3-3-4-2-4-3-8-4-12 0-2-1-5-1-7v1c1 1 1 2 1 3h1c1 2 1 4 2 6 0 6 8 13 12 17l-1-1c-1 0-2 0-2-1-2 0-2-1-3-1z" class="f"></path><defs><linearGradient id="I" x1="670.203" y1="776.417" x2="656.045" y2="788.865" xlink:href="#B"><stop offset="0" stop-color="#454545"></stop><stop offset="1" stop-color="#636365"></stop></linearGradient></defs><path fill="url(#I)" d="M666 770c1-3 1-3 4-4-6 8-9 15-8 25 1 9 7 15 12 21s11 12 16 19h0l-10-11c-2-3-5-5-8-8-4-5-8-11-12-17-1-5-1-9-1-13v-1c0-2 1-6 1-9h0c1-2 2-4 3-4l1 1v1h2z"></path><path d="M660 772c1-2 2-4 3-4l1 1-5 12c0-2 1-6 1-9h0z" class="s"></path><path d="M254 610l1 1v1h-1l1 2-2 3c-3 4-9 10-13 13l-1 1c-3 2-6 3-10 5-1-1-2-1-3 0-1 0-1 0-2 1l-5 2-5 2-2 1-5 1c-3 1-7 3-10 5h-1c2-2 5-3 7-4 1-1 2-1 3-1l16-7c1-1 2-1 3-1 2-1 5-3 8-4l3-2 1-1 1-1 1-1c3-2 8-6 10-9l4-4 1-3z" class="AC"></path><path d="M709 664c5-3 10-5 15-6 7-1 18-2 24 2v1c-3-1-7-2-11-3-9-1-19 1-27 7-7 6-12 14-15 23-1 0-1 0-1 1-1 1-1 1-1 2l-4 5h-1c-1 0-1 1-2 1h-1l-2-2 1-3c1-1 0-2 1-4l1 1v5c1 0 2 1 3 1v-1c1-5 4-11 7-16 1-1 2-3 3-4 4-3 7-7 10-10z" class="a"></path><path d="M554 193l1-2c1 3 3 4 5 5l2 2 2 1h1 3l2 1c1 0 2 0 3 1s1 2 1 3v4c-4 1-7 1-11 1-1-2-3-3-4-5-3-3-4-7-5-11z" class="AG"></path><path d="M683 731c2 1 3 2 5 3 1-1 0 0 1-2l3 4 12 17c4 6 8 12 12 19 2 3 4 6 4 10-4-6-7-12-11-17 0-2-4-6-5-8l-1-1c-7-10-16-18-27-24 1 0 2 0 4 1h0c1-1 2-2 3-2z" class="K"></path><defs><linearGradient id="J" x1="258.575" y1="692.725" x2="265.266" y2="703.24" xlink:href="#B"><stop offset="0" stop-color="#171818"></stop><stop offset="1" stop-color="#373638"></stop></linearGradient></defs><path fill="url(#J)" d="M244 725c-2 2-3 4-4 6h-1l3-5 3-5v-1l11-14c1-2 1-3 2-4 0-1 0-1 1-2l1-3 1-1v-1-2c1-1 1-1 1-3h0l1-1c0 1 0 1 1 1v-2l-1-1c0-1 0-1 1-2h1c0 1 0 2 1 2s1 1 2 2c0-2 0-2 1-3h1v2l-1 1c-2 9-6 16-14 22v1l-2 3-3 1h0l-3 3-1 1c0 2-1 4-2 5z"></path><path d="M725 634l2 2 1-1h0l1-1-1-3v-1h1c0 1 1 2 1 3 6 13 17 19 28 26 11 8 20 19 22 33l-3-6c-3-9-7-14-14-21l-2 2-8-5 1-1-2-3-15-10v-1l-1-2-4-3-6-4c0-1-1-3-1-4z" class="AT"></path><path d="M752 658c4 2 8 4 11 7l-2 2-8-5 1-1-2-3z" class="d"></path><path d="M725 634l2 2 1-1h0l1-1-1-3v-1h1c0 1 1 2 1 3-1 5 6 8 6 12l-4-3-6-4c0-1-1-3-1-4z" class="H"></path><path d="M688 675c0 4-2 6-2 10l1-2c1 0 2-1 3-2 1-2 1-4 2-6 3-4 8-10 14-10l3-1c-3 3-6 7-10 10-1 1-2 3-3 4-3 5-6 11-7 16v1c-1 0-2-1-3-1v-5l-1-1c-1 2 0 3-1 4l-1-1 1-1-2-1v-2l6-12z" class="R"></path><path d="M261 453c3 5 4 12 4 18l1-1 1-2v9c0 2-1 4 0 6v3c0 1-1 3 0 5-1 2-2 3-2 5-2 3-2 5-5 7h0v-4-3-5l1-3v-11h0l1 1c0-2 1-3 1-4v-2c0-5-1-12-3-17l1-2z" class="F"></path><path d="M267 468v9c0 2-1 4 0 6v3l-1-1c-1-1 0-2-1-4 0 2 0 3-1 4l-1 1c2-5 2-10 2-15l1-1 1-2z" class="AM"></path><path d="M263 486l1-1c1-1 1-2 1-4 1 2 0 3 1 4l1 1c0 1-1 3 0 5-1 2-2 3-2 5-2 3-2 5-5 7h0v-4-3l3-10z" class="AN"></path><path d="M660 772h0c0 3-1 7-1 9v1c0 4 0 8 1 13 4 6 8 12 12 17 3 3 6 5 8 8-1 0-2 0-3-1v-1c-3-2-5-4-7-6-2-5-6-8-10-12 0-2-3-4-4-5-2-1-3-2-5-1v-1l1-1h1l-1-1v-2c-1-5 2-12 5-16l3-1z" class="g"></path><path d="M657 788l-2 1v-1c0-2 1-3 1-5 1-4 0-5 3-9-1 5-2 9-2 14z" class="V"></path><path d="M659 774l1-2c0 3-1 7-1 9v1c0 4 0 8 1 13h-1l-2-4h0v-3c0-5 1-9 2-14z" class="AC"></path><path d="M669 721c1-2 2-3 4-4 2 0 3 2 5 3h1c4 4 7 8 10 12-1 2 0 1-1 2-2-1-3-2-5-3-1 0-2 1-3 2h0c-2-1-3-1-4-1l-3-1c-1 1-2 2-3 2l-1-1 1-1h1c0-1 0-1-1-1 0-2 0-2 1-4-1-1-2-1-2-2h-1c0-1 1-2 1-3z" class="J"></path><path d="M669 724c2 0 4 0 6 1-2 1-3 1-4 1-1-1-2-1-2-2z" class="N"></path><path d="M675 725h0 3l1 1 4 5c-1 0-2 1-3 2h0c-2-1-3-1-4-1l-3-1c-1 1-2 2-3 2l-1-1 1-1h1c0-1 0-1-1-1 0-2 0-2 1-4 1 0 2 0 4-1z" class="W"></path><path d="M675 725h0 3l1 1c-2 0-3 1-5 1l-2 3 1 1c-1 1-2 2-3 2l-1-1 1-1h1c0-1 0-1-1-1 0-2 0-2 1-4 1 0 2 0 4-1z" class="R"></path><path d="M506 106l1 1c-2 1-3 3-4 4-1 2-2 3-3 5-4 4-7 9-9 14 1 3 0 6 0 9-1-3-1-4-1-7-2 3-1 6-2 8v3c-1-10-2-20-6-30 0-1 0-3 1-4h0l1 2c0 1 1 2 0 3 2 1 4 2 6 2 6 0 11-6 16-10z" class="t"></path><path d="M506 106l1 1c-2 1-3 3-4 4-1 2-2 3-3 5-4 4-7 9-9 14h0 0c-1-3 2-9 4-12 1 0 1-1 1-2-3 2-6 1-9 1-2-1-2-1-3-3 2 1 4 2 6 2 6 0 11-6 16-10z" class="L"></path><path d="M745 682c1 2 1 3 1 5l1 7c0-1 0-3 1-4l1 1v-2l1 3c0 3 1 6 2 9 5 12 16 21 21 33l-7-8c-2-2-3-4-5-6-4-5-9-10-14-14h1v-1l-2-4-2-1c0-2-1-3 0-4l-1-3v-2l1-5 1-4z" class="AW"></path><path d="M747 694c0-1 0-3 1-4l1 1c0 3 1 5 1 8-1 0-1-1-2-1l-1-4z" class="s"></path><path d="M746 701c2 2 5 5 8 6h0c1 2 2 3 2 5-3-2-5-4-8-6v-1l-2-4z" class="Y"></path><path d="M744 700c0-2-1-3 0-4 1 1 3 3 5 4l-1-2c1 0 1 1 2 1l4 8c-3-1-6-4-8-6l-2-1z" class="V"></path><path d="M745 682c1 2 1 3 1 5l1 7 1 4 1 2c-2-1-4-3-5-4l-1-3v-2l1-5 1-4z" class="j"></path><path d="M597 191l8-20c-2 2-5 5-6 7a30.44 30.44 0 0 1-8 8l-1 1c-3 2-8 5-11 4h0c4-2 7-4 10-6 3-1 5-3 7-5 7-8 14-19 18-30 2-5 3-10 4-15 1-2 1-8 2-10h1c0 3-1 5-1 8-1 5-2 12-4 17l-1 2-3 6-3 6h0c0 6-6 12-7 18 0 3 1 5 3 7l1 1h-2 0c0 1 0 2 1 3-2 1-3 2-4 3-2-1-3 0-5-1h0c-1 1-1 1-2 1h0c1-2 1-4 3-5z" class="t"></path><path d="M602 190v1c-1 1-2 1-3 1h0l2-3h1v1z" class="z"></path><path d="M602 189c1 0 2 0 2 1h0c0 1 0 2 1 3-2 1-3 2-4 3-2-1-3 0-5-1h0c-1 1-1 1-2 1h0c1-2 1-4 3-5v1l-1 1 2 2 1-1h-1v-1h1c1 0 2 0 3-1v-2-1z" class="AF"></path><path d="M428 184l1-3v-4c-1-6-4-12-5-18l1-1 4 8 4 6h-2l5 11v4c0 2 1 2 1 4h-1c-1 1-1 2-2 4h0c-1 1-2 1-3 2l-1-1c-2 2-5 3-8 4 1-1 3-2 5-3l1-1c1 0 1-1 1-1 1-1 0-1 1-2 0-1 0-1 1-2l-3 4c-1 1-3 2-4 2h-2c-1 1-2 1-4 1-1 1-2 1-2 1-2-1-3-1-4-1 3 0 6-1 8-2 6-2 6-6 8-11v-1z" class="s"></path><path d="M433 185c0 3-1 9 1 10-1 1-2 1-3 2l-1-1v-2c2-3 3-5 3-9z" class="V"></path><path d="M433 185c-1-3-1-7-2-10v-1c-1-1-1-2 0-3v1l5 11v4c0 2 1 2 1 4h-1c-1 1-1 2-2 4h0c-2-1-1-7-1-10z" class="t"></path><path d="M277 371v3c1 8 3 15 3 22v12h0l-1-1c-1 1-1 2-1 4h-1c-1 1-1 0-2 1v-3l-1-1c0-2 0-3-1-5 1-7 0-15-3-21h0c-1-2-1-3-1-4-1 0-1 0-1-1-1-1-2-1-3-1l4-1c2 1 3 1 5 1 2-2 2-3 3-5z" class="R"></path><path d="M277 374c1 8 3 15 3 22v12h0l-1-1c-1 1-1 2-1 4h-1c-1 1-1 0-2 1v-3l-1-1c0-2 0-3-1-5 1-7 0-15-3-21h0c-1-2-1-3-1-4l1 1v1h2v2c1 1 1 1 1 2 0 5 2 9 2 14h0l2 2c1 1 0 2 0 3l2-2c0-4 0-9-1-12v-2c0-3-1-5-2-8 0-1 0-2 1-4v-1z" class="k"></path><defs><linearGradient id="K" x1="469.81" y1="150.294" x2="495.425" y2="125.448" xlink:href="#B"><stop offset="0" stop-color="#a5a49a"></stop><stop offset="1" stop-color="#cfccd3"></stop></linearGradient></defs><path fill="url(#K)" d="M486 167v-4c0-11-1-22-3-33-1-9-4-18-7-26-5-8-10-15-17-21v-1c7 5 12 13 17 19 2 0 3 0 4-1h1-1c-1 2-1 2-3 3 1 3 4 7 5 10 4 10 5 20 6 30 0 5 1 9 3 14l-2 1v-2h0v7 5c-1 1-2 1-3 1v-2z"></path><path d="M489 163v5c-1 1-2 1-3 1v-2c1 1 1 1 2 1l1-5z" class="r"></path><path d="M201 337h3 0v1c-1 1 0 4 0 5 2 8 4 16 9 24 7 11 18 23 31 26 6 1 13 0 18-3 1 0 1-1 2-1-1 2-3 3-5 4h-1c-1 0-1 1-2 1-9 1-18 0-25-6-1-1-3-2-4-3 6 8 16 13 25 17 2 1 5 1 7 3h-1c-13-5-26-12-36-23-3-3-6-7-9-10h0v2c-2-2-3-5-5-8-4-9-7-19-7-29z" class="X"></path><path d="M750 586c1 4 1 7 2 10 1 1 1 3 1 4 1 3 1 7 2 9h0c1 3 1 6 2 9v1l-2-2h-1c1 2 1 5 0 7 0 4-1 8-2 11l-1 2h0v-4h-1v3l-1-1c0-1 0-1-1-1v1h-1l1-5h0c0-4 1-11 1-15-1-3-1-6-1-9v-16l1 3c1-1 1-5 1-7z" class="Y"></path><path d="M748 630l1 1c1-2 1-2 1-3v-2-2c2 2 0 9 2 11l-1 2h0v-4h-1v3l-1-1c0-1 0-1-1-1v1h-1l1-5zm5-30c1 3 1 7 2 9h0c1 3 1 6 2 9v1l-2-2h-1v-5l-1-4c0-2-1-3-1-5l1 1v-4z" class="f"></path><path d="M750 586c1 4 1 7 2 10 1 1 1 3 1 4v4l-1-1-1-1h-1v-5c-1-2-1-3-1-4 1-1 1-5 1-7z" class="T"></path><path d="M243 520c1 0 2 0 3 1l-2 3 1 1c-2 2-3 3-3 5l-1 2c-3 2-6 3-9 5 1 0 3 0 3 1 1 0 1 1 1 1-6 2-14 5-19 9l-9 6c-4 3-10 7-13 12l-6 5c9-13 24-23 36-33l18-18z" class="AR"></path><path d="M232 537l-2 1c1-1 0-1 1-1a79.93 79.93 0 0 0 13-13l1 1c-2 2-3 3-3 5l-1 2c-3 2-6 3-9 5z" class="p"></path><defs><linearGradient id="L" x1="444.015" y1="182.367" x2="432.113" y2="196.999" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#363537"></stop></linearGradient></defs><path fill="url(#L)" d="M433 172l1 2c1 1 1 0 1 1 2 3 7 8 10 9l2 2h0c1 0 2 1 3 1l2 2c1 0 2 1 3 1 1 2 1 3 1 4l-1 5-1-1v-1c-5 0-9-1-14-1l-5 1-2 1h-1l-1-1c1-1 2-1 3-2h0c1-2 1-3 2-4h1c0-2-1-2-1-4v-4l-5-11h2z"></path><path d="M436 183v1c2 2 3 4 5 6h0c-3 1-4 3-7 5 1-2 1-3 2-4h1c0-2-1-2-1-4v-4z" class="Y"></path><path d="M435 197c3-2 5-2 7-2 5-1 9-1 14-1l-1 5-1-1v-1c-5 0-9-1-14-1l-5 1z" class="V"></path><path d="M253 435h4c-2 0-3 1-4 2h-1v1h0l-3 2v1l5 3 1 1c3 2 5 5 7 7l3 9 2 7-1 2-1 1c0-6-1-13-4-18l-1 2h-1c-2-1-2-2-4-1v-2c0-1-1-1-2-2l-2-1c-2-2-4-3-6-3-1 0-3 0-4-1h-1c-1-1-1-1-3 0-5-1-9-3-14-2h-1v-2l-17 2c2-1 3-1 5-1 4-1 8-1 12-3h3c4 1 8 1 11 1h6c4-1 7-3 11-5z" class="I"></path><path d="M255 446c2 2 4 4 6 7l-1 2h-1c-2-1-2-2-4-1v-2c0-1-1-1-2-2 2-1 1-2 2-4z" class="R"></path><path d="M242 442c4 1 9 2 13 4-1 2 0 3-2 4l-2-1c-2-2-4-3-6-3-2-1-2-2-3-4z" class="b"></path><path d="M223 443c0-1 1-2 1-3l18 2c1 2 1 3 3 4-1 0-3 0-4-1h-1c-1-1-1-1-3 0-5-1-9-3-14-2z" class="a"></path><defs><linearGradient id="M" x1="279.904" y1="664.115" x2="271.798" y2="676.335" xlink:href="#B"><stop offset="0" stop-color="#200"></stop><stop offset="1" stop-color="#400c0b"></stop></linearGradient></defs><path fill="url(#M)" d="M258 670c4-2 7-4 11-5h1c4-1 10-1 14 1 3 1 7 4 10 7v1l3 3-4 1-1-1v1c-4-2-9-5-13-6h0c-7-2-13 0-19 2-3-1-6 2-9 2-1 0-1 1-2 1s-1 1-2 1l2-2v-1c1-2 7-4 9-5z"></path><path d="M285 673c2 0 3-1 4 0h5v1l3 3-4 1-1-1c-2-2-4-3-7-4z" class="b"></path><path d="M258 670c3 2 3 2 6 1 4 0 7-1 10 0 2 0 3 0 5 1l6 1c3 1 5 2 7 4v1c-4-2-9-5-13-6h0c-7-2-13 0-19 2-3-1-6 2-9 2-1 0-1 1-2 1s-1 1-2 1l2-2v-1c1-2 7-4 9-5z" class="O"></path><path d="M732 572v4c1 0 2 0 3-1h2c-2 5-5 11-7 16-3 12-4 27-1 39h-1v1l1 3-1 1h0l-1 1-2-2-1-1 3 1v-1-1c-1-5-2-8-3-13 0-4 1-9 0-13l-1-3v-8c-1-2-1-4-1-6h2c0-1 1-1 2-2h0c2-2 2-9 4-11 1-1 1-3 2-4z" class="n"></path><path d="M722 589h2c0-1 1-1 2-2 0 2 0 4-1 5v2 1l-1-1-1 1c-1-2-1-4-1-6z" class="Q"></path><path d="M723 595l1-1 1 1c0 2 0 3 1 4l-1 1v2c1 2-1 4-1 6v11c0-4 1-9 0-13l-1-3v-8z" class="o"></path><path d="M607 191c2 1 3 2 5 3l1-1-3-3v-1c0-2-3-4-1-5h0c3 7 8 10 14 13v1l5 2c0 1 1 1 2 1h0c1 0 2 1 2 1h2s1 1 2 1h1 2c5 2 10 2 15 2 3 1 4-1 7 2-2 1-3 1-4 1-2 1-4 0-6 0-6 0-11-2-16-3-5-2-10-4-14-4h-1-2c-3 1-5 1-8 0-1-1-3 0-5-1l-1-1c-1-1-2 0-3 0l-1-1c-2 0-3 0-5 1h-3c-2 1-4 2-7 1l-1 1 1-1c3-1 6-2 9-4h0c1 0 1 0 2-1h0c2 1 3 0 5 1 1-1 2-2 4-3-1-1-1-2-1-3h0 2l1 1z" class="s"></path><path d="M606 190l1 1c0 1-1 2-1 3 1 1 3 2 4 3-2 1-4 0-6 1-1 0-2-1-3-1-1 1-1 0-2 0s-2 0-3 1c-2 0-3 0-4 1-2 1-4 2-7 1l-1 1 1-1c3-1 6-2 9-4h0c1 0 1 0 2-1h0c2 1 3 0 5 1 1-1 2-2 4-3-1-1-1-2-1-3h0 2z" class="V"></path><path d="M592 199h3c2-1 3-1 5-1l1 1c1 0 2-1 3 0l1 1c2 1 4 0 5 1 3 1 5 1 8 0h2 1c4 0 9 2 14 4 5 1 10 3 16 3 2 0 4 1 6 0 1 0 2 0 4-1l2-1c-2 3-5 4-8 5h-1c-3 0-4 1-6 0h-2l-1 1-2-1c-3-1-4-1-7-1l-18-6c-2 0-5 0-7-1-1 0-2 0-3 1-2 0-4-1-6-1-8-1-17 1-24 1l-1 1-1-5c3 1 6 0 9 0l-1 1 1-1c3 1 5 0 7-1z" class="B"></path><path d="M236 340c5 1 11 3 15 6h0 4c2 1 4 2 7 3l3 3c2 1 3 2 3 4 2 1 2 4 3 6h0c0 1 0 3-1 4v2l-3-3c-1 2-1 3-1 4s1 1 1 2l-1 1c-3-6-8-10-11-15 0-1-3-4-4-5l-1-1c-1 0-2-1-2-2-2-1-3-2-5-3h0l-2-1h-1c-1-1-4-3-4-5z" class="Z"></path><path d="M261 360l2 1c1 2 2 3 3 4h1c-1 2-1 3-1 4l-4-5c0-2-1-3-1-4z" class="k"></path><path d="M251 346h4c2 1 4 2 7 3l3 3h-1-1c-2-1-3-1-4-1l-1-1h-2c0 3 4 6 5 8h1c0 1 0 2 1 3l-2-1c-3-4-7-9-11-11-1-1-1-1-1-2s1-1 2-1z" class="K"></path><path d="M263 361c-1-1-1-2-1-3h-1c-1-2-5-5-5-8h2l1 1c1 0 2 0 4 1h1 1c2 1 3 2 3 4 2 1 2 4 3 6h0c0 1 0 3-1 4v2l-3-3h-1c-1-1-2-2-3-4z" class="N"></path><path d="M263 361c-1-1-1-2-1-3h-1c-1-2-5-5-5-8h2l1 1c1 0 2 0 4 1h1 1c2 1 3 2 3 4l-1-1c-1-1-2-1-4-1l-1-1h-1c0 2 0 2 1 3v1c1 2 3 3 4 5 2 1 2 3 4 4v2l-3-3h-1c-1-1-2-2-3-4z" class="W"></path><path d="M342 753l7 7c2 2 4 3 7 6l1 1c1 1 3 4 5 4l-1 2c1 1 1 1 2 3v1c1 1 1 1 2 1l1 1-1 1v3 2l-1 1-1 3v5h0l-3-1-1 5c0 1 0 2-1 3-1 0-2 1-2 0h0c1-5 4-9 2-15-2-7-5-12-10-18h-1c-1 0-4-4-5-5-3-2-6-4-9-5 3 0 7 3 9 3s3 1 4 2h1c-2-4-5-6-7-9l-1-1h3z" class="t"></path><path d="M362 787v-2c0-1 0-2 1-2l1 3-1 3c0-1-1-1-1-2z" class="AE"></path><path d="M360 793c1-5 0-9-2-13 1 1 2 2 2 3 1 1 1 3 2 4 0 1 1 1 1 2v5h0l-3-1z" class="j"></path><path d="M349 760c2 2 4 3 7 6l1 1c1 1 3 4 5 4l-1 2c1 1 1 1 2 3v1c1 1 1 1 2 1l1 1-1 1v3 2l-1 1-1-3c-1-1-1-1-1-2s0-1-1-1c-4-2-7-9-10-13h0l-2-2v-1l2 1h0c-1-2-2-3-2-5z" class="V"></path><path d="M357 770c1 1 3 2 4 3s1 1 2 3v1c1 1 1 1 2 1l1 1-1 1v3h-1c0-2-1-3-1-4v-1c-1 0-2 0-2-1-2-2-4-4-4-7z" class="s"></path><path d="M349 760c2 2 4 3 7 6l1 1c1 1 3 4 5 4l-1 2c-1-1-3-2-4-3h0c-2-2-3-3-5-3h-1 0l-2-2v-1l2 1h0c-1-2-2-3-2-5z" class="Y"></path><defs><linearGradient id="N" x1="281.393" y1="660.503" x2="276.017" y2="679.963" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b100e"></stop></linearGradient></defs><path fill="url(#N)" d="M254 668c9-5 20-10 31-7 5 1 9 3 13 6l1 1c1 2 3 3 5 6l4 5-2 2-5-3h0c-1-2-2-2-3-3-2 0-2 0-4-1h0v-1c-3-3-7-6-10-7-4-2-10-2-14-1h-1c-4 1-7 3-11 5-2 1-8 3-9 5-3 2-5 4-8 3 4-4 8-7 13-10z"></path><path d="M267 491h1v2c1 4-1 7-1 11l-1 3c-1 1-1 1-1 2s0 2-1 3h0c-1 2-2 3-3 5h0c-1 1-1 2-1 3-6 6-12 9-19 12l1-2c0-2 1-3 3-5l-1-1 2-3h0l8-11 1-1 3-5v-1c0-1 1-3 2-4v4h0c3-2 3-4 5-7 0-2 1-3 2-5z" class="AI"></path><path d="M245 525l8-9h1l-1 2 1 1h1c1 1 1 1 1 2-3 4-7 5-11 8-1 1-2 1-3 1 0-2 1-3 3-5z" class="U"></path><path d="M267 491h1v2c1 4-1 7-1 11l-1 3c-1 1-1 1-1 2s0 2-1 3h0c-1 2-2 3-3 5 0-2 0-4 1-5-1-1-1-1-1-2v-1c-2 1-1 2-3 3h-1l-1 1h-1v-2l-1-1 1-1 3-5v-1c0-1 1-3 2-4v4h0c3-2 3-4 5-7 0-2 1-3 2-5z" class="AS"></path><path d="M267 491h1v2c1 4-1 7-1 11l-1-4v-1l-3 3c-1 2-2 3-4 5 0 1 0 0-1 1v-4-1c0-1 1-3 2-4v4h0c3-2 3-4 5-7 0-2 1-3 2-5z" class="AO"></path><defs><linearGradient id="O" x1="483.775" y1="195.222" x2="490.438" y2="184.731" xlink:href="#B"><stop offset="0" stop-color="#403e40"></stop><stop offset="1" stop-color="#5a5a5a"></stop></linearGradient></defs><path fill="url(#O)" d="M489 163v-7h0v2l2-1c1 3 2 10 4 11l1 1c0 1 1 2 1 3 1 3 0 5 1 8l2 1-2 2v1l-2 1-4 2v4l-2 6h-3v1l-3-1v1h-1c-1-2 1-5 1-7h0l-2 2c3-8 4-15 4-24 1 0 2 0 3-1v-5z"></path><path d="M490 186v-5h1v7l-1 2 2 1-2 6h-3c2-3 3-7 3-11z" class="AC"></path><path d="M489 163v-7h0v2l2 13c0 3 1 7 0 10h-1v5c-1-2-1-5-1-8v-10-5z" class="AF"></path><path d="M491 157c1 3 2 10 4 11l1 1-1 4c-2 0-2-1-3-2h-1l-2-13 2-1z" class="X"></path><defs><linearGradient id="P" x1="483.242" y1="187.118" x2="488.823" y2="168.75" xlink:href="#B"><stop offset="0" stop-color="#6b6b6c"></stop><stop offset="1" stop-color="#989798"></stop></linearGradient></defs><path fill="url(#P)" d="M486 169c1 0 2 0 3-1v10c-1 4-1 8-2 12-1 3-2 5-3 7v1h-1c-1-2 1-5 1-7h0l-2 2c3-8 4-15 4-24z"></path><path d="M496 169c0 1 1 2 1 3 1 3 0 5 1 8l2 1-2 2v1l-2 1-4 2v4l-2-1 1-2v-7c1-3 0-7 0-10h1c1 1 1 2 3 2l1-4z" class="z"></path><path d="M497 172c1 3 0 5 1 8l-1 1h-1l-4 3v-3l3-2c0-1 1-2 2-4v-3z" class="Y"></path><path d="M491 171h1c1 1 1 2 3 2l-1 3c-2 2-2 3-2 5v3l-1 4h0v-7c1-3 0-7 0-10z" class="AE"></path><path d="M492 171c1 1 1 2 3 2l-1 3c-1 0-1 0-2-1s0-2 0-4z" class="y"></path><path d="M498 180l2 1-2 2v1l-2 1-4 2v4l-2-1 1-2h0l1-4 4-3h1l1-1z" class="V"></path><path d="M659 705c4 0 7 5 10 4l6 3v-1h1l3-1c2 0 2 0 3 1l1-1c0-1 1-1 2-2l1-1h4l1-1v3l1 7c0 2 0 3 1 5h1c1 4 2 7 3 10 0 1 2 4 2 4-1 2 0 3 0 5l-1 2-5-7h0l-1 1-3-4c-3-4-6-8-10-12-3-3-5-5-9-7l-4-3-4-2-3-3z" class="e"></path><path d="M690 725c-1-1-1 0-1-1l-1-3v-1c0-1-1-2-2-2v-2c0-2 0-3 1-4 1 2 2 4 2 6v1c1 1 1 3 2 4v2h-1z" class="AP"></path><path d="M683 715h0s1 0 1-1v-3h1 0c0 4 2 8 1 12l-1 1-4-5c1-2 1-3 2-4z" class="H"></path><path d="M685 708l1-1h4l1-1v3c-1 3-1 6-1 9l-1 1v-1c0-2-1-4-2-6l-1-3-1 1v-2z" class="P"></path><path d="M691 709l1 7c0 2 0 3 1 5l2 8-4-4v-2c-1-1-1-3-2-4l1-1c0-3 0-6 1-9z" class="e"></path><path d="M693 721h1c1 4 2 7 3 10 0 1 2 4 2 4-1 2 0 3 0 5l-1 2-5-7h0c-1-1-2-2-3-4 1 0 2-1 2-2s-1-3-2-4h1l4 4-2-8z" class="AB"></path><path d="M692 729l1 1c0 1 1 2 1 2 0 1 0 1-1 2v1h0c-1-1-2-2-3-4 1 0 2-1 2-2z" class="E"></path><path d="M659 705c4 0 7 5 10 4l6 3v-1h1l3-1c2 0 2 0 3 1l1-1c0-1 1-1 2-2v2 1h-1v3c0 1-1 1-1 1h0c-1 1-1 2-2 4l4 5 5 7c1 2 2 3 3 4l-1 1-3-4c-3-4-6-8-10-12-3-3-5-5-9-7l-4-3-4-2-3-3z" class="AN"></path><path d="M675 711h1c1 1 3 3 4 3 0 1 2 1 3 1-1 1-1 2-2 4-2-3-4-5-6-7v-1z" class="P"></path><path d="M685 708v2 1h-1v3c0 1-1 1-1 1h0c-1 0-3 0-3-1-1 0-3-2-4-3l3-1c2 0 2 0 3 1l1-1c0-1 1-1 2-2z" class="K"></path><path d="M552 181c0 2 0 5 1 6 0 2 1 5 1 6 1 4 2 8 5 11 1 2 3 3 4 5 4 0 7 0 11-1v5h0l-2 4v1l-2-1c-1 0-3 1-4 1-2 0-3-1-5-1h0c-3-1-8-3-9-4s-1-1-3-1h-2c1-1 2-1 3-2l-3-3v-1-1l-1-1c-1-2-1-3-2-6l1-2c1-2 1-3 2-4 1-4 3-6 5-10v-1z" class="M"></path><path d="M563 213c-3-1-5-2-8-4v-2h0c-1-2-1-2-1-4h1l6 6 1 1c1 1 1 1 1 2v1z" class="AK"></path><path d="M552 181c0 2 0 5 1 6-1 2-2 4-3 5-1 0-1 1-1 2-2 3-3 5-2 9v2h0l-1-1c-1-2-1-3-2-6l1-2c1-2 1-3 2-4 1-4 3-6 5-10v-1z" class="AE"></path><defs><linearGradient id="Q" x1="556.71" y1="215.176" x2="557.849" y2="211.319" xlink:href="#B"><stop offset="0" stop-color="#eacbc6"></stop><stop offset="1" stop-color="#d6d1cd"></stop></linearGradient></defs><path fill="url(#Q)" d="M547 205h0c5 4 10 7 15 9l8 3c-1 0-3 1-4 1-2 0-3-1-5-1h0c-3-1-8-3-9-4s-1-1-3-1h-2c1-1 2-1 3-2l-3-3v-1-1z"></path><path d="M563 209c4 0 7 0 11-1v5h0l-2 4v1l-2-1-8-3 1-1v-1c0-1 0-1-1-2 0-1 0-1 1-1z" class="R"></path><path d="M563 209c4 0 7 0 11-1v5h0c-1 0-1 0-1-1h-2c-2 1-5 0-6-1s-2-1-3-1c0-1 0-1 1-1z" class="E"></path><path d="M265 461c1-2 2-4 3-5s1-2 2-2c1 1 1 2 1 3l1 1 1 2h2l2 4h0c1 5 1 12 0 16 0 3 0 5-1 8 0 1-1 3 0 4l-1 2-3 8h-1l-1-2-2 5c-1 0-1 1-1 2h-1l1-3c0-4 2-7 1-11v-2h-1c-1-2 0-4 0-5v-3c-1-2 0-4 0-6v-9l-2-7z" class="AM"></path><path d="M267 486c1-1 1-3 2-4v6c0 2-1 3-1 5v-2h-1c-1-2 0-4 0-5z" class="AT"></path><path d="M269 473c0 3-1 4 0 8v1c-1 1-1 3-2 4v-3c-1-2 0-4 0-6l2-4z" class="AJ"></path><path d="M265 461c1-2 2-4 3-5s1-2 2-2c1 1 1 2 1 3l1 1c-1 0-1 0-1 1-1 1-1 3-1 4 0 3-2 6-2 9l1 1-2 4v-9l-2-7z" class="v"></path><path d="M269 488l1-5c1-4 1-7 2-10 1-1 1-1 1-2s1-2 2-3h0s0 1 1 2c1 2 1 4 1 7v3h0c0 3 0 5-1 8 0 1-1 3 0 4l-1 2-3 8h-1l-1-2-2 5c-1 0-1 1-1 2h-1l1-3c0-4 2-7 1-11 0-2 1-3 1-5z" class="D"></path><path d="M270 500c1-2 1-5 3-7l2 1-3 8h-1l-1-2z" class="AR"></path><path d="M265 437h1l3 3h0c2 0 4 2 5 4 1 1 2 3 2 4 1 1 1 2 1 3h1c2 3 1 6 3 8 1 1 1 3 1 4-1 1-1 2-1 3l1 3c0 2 0 4-1 6-1 1-1 2-1 3 0-4 0-8-1-12 0 1 0 0-1 0 0 0 0-1-1-1v-1h0l-2-4h-2l-1-2-1-1c0-1 0-2-1-3-1 0-1 1-2 2s-2 3-3 5l-3-9c-2-2-4-5-7-7l-1-1-5-3v-1l3-2 2-1 2 2h1c2 0 3-1 4-1l4-1z" class="Z"></path><path d="M266 441v-1c2 0 2 1 3 2h0l1 3h-2c0 1 1 2 2 3h0l-2-1c-2 1-2 1-4 1v-1l1-1c0-1-1-1-1-2h1l1-1h1l-1-2z" class="k"></path><path d="M255 445c4 1 5 3 7 5h2c3 0 5 0 7 3h-4v1h-1l-3-1s-1 0-1-1c-2-2-4-5-7-7z" class="E"></path><path d="M256 444l-1-2v-1c3-1 4-1 6 0 2 0 3 1 5 2l-1 1h-1c0 1 1 1 1 2l-1 1h-1c-2-2-4-3-7-3z" class="R"></path><path d="M254 437l2 2h1c3 0 4 0 6 1h1l2 1 1 2h-1c-2-1-3-2-5-2-2-1-3-1-6 0v1l1 2h-2l-5-3v-1l3-2 2-1z" class="a"></path><path d="M265 437h1l3 3h0c2 0 4 2 5 4 1 1 2 3 2 4l-2 1-3-4c0-1-1-2-2-3h0c-1-1-1-2-3-2v1l-2-1h-1c-2-1-3-1-6-1 2 0 3-1 4-1l4-1z" class="b"></path><path d="M271 445l3 4 2-1c1 1 1 2 1 3h1c2 3 1 6 3 8 1 1 1 3 1 4-1 1-1 2-1 3l1 3c0 2 0 4-1 6-1 1-1 2-1 3 0-4 0-8-1-12 0 1 0 0-1 0 0 0 0-1-1-1v-1h0l-2-4h-2l-1-2-1-1c0-1 0-2-1-3-1 0-1 1-2 2s-2 3-3 5l-3-9c0 1 1 1 1 1l3 1h1v-1h4l1 1 2-1c0-1 0-3-1-4s-2-2-2-3v-1z" class="q"></path><path d="M274 449l2-1c1 1 1 2 1 3h1c2 3 1 6 3 8 1 1 1 3 1 4-1 1-1 2-1 3l1 3c0 2 0 4-1 6-1 1-1 2-1 3 0-4 0-8-1-12v-4c0-1-1-3-1-5h-1c0-3-2-6-3-8z" class="a"></path><path d="M284 644l6-6h1 0c0 1 0 2-1 2l1 1v1c2 0 3-1 4-3v5h1l-2 2h1l2-1 1 1c0 1-1 1-1 2h-2c0 1 0 0-1 1 0 1-1 1-1 1l-1 1h1 1 1l-4 3v1h0 3 1c-2 1-4 2-5 3v1l3 3c3 2 5 4 8 6l-1 1-1-1-1-1c-4-3-8-5-13-6-11-3-22 2-31 7h-1c0-1 0-1 1-2h0c1-2 4-4 6-5 1-1 2-3 3-4 2-1 4-3 6-4l-1 2 1 2 5-4 7-6 3-3z" class="AN"></path><path d="M269 653l-1 2 1 2-15 9h0c1-2 4-4 6-5 1-1 2-3 3-4 2-1 4-3 6-4z" class="w"></path><path d="M294 646h1l2-1 1 1c0 1-1 1-1 2h-2c0 1 0 0-1 1 0 1-1 1-1 1l-1 1h1 1 1l-4 3v1h0 3 1c-2 1-4 2-5 3v1l3 3-9-3c2-1 4-2 6-4h-1c-1 0-2 1-3 1-1 1-2 2-4 2 1-3 4-2 6-4v-1c-1 1-1 1-2 1a30.44 30.44 0 0 0 8-8z" class="S"></path><path d="M284 644l6-6h1 0c0 1 0 2-1 2l1 1v1c2 0 3-1 4-3v5h-1l-5 5c-7 6-14 9-23 11l10-6-2-1 7-6 3-3z" class="w"></path><path d="M284 644l6-6h1 0c0 1 0 2-1 2l1 1v1c-4 4-7 7-12 10-1 0-2 1-3 2l-2-1 7-6 3-3z" class="AJ"></path><path d="M379 206c1 2 2 4 3 7h0c1 1 1 3 1 4l1 2 1 2c1 1 2 2 3 4-3 2-1 7-3 8h0-2c0 1 1 2 0 3v1l-1 4-3 8c-1 1-2 2-3 4-2 5-4 10-8 14l-1 1-1 2c-1 1-2 2-4 3l-3-9 1-1h1l3-6h1l7-14c4-12 8-24 7-37z" class="O"></path><path d="M384 219l1 2c1 1 2 2 3 4-3 2-1 7-3 8h0-2 0v-1l1-1h0c1-2 1-7 1-9-1-1-1-2-1-3zm-12 24v1 4c0 1-1 1-1 2v2c-1 2-3 4-5 5h-1l7-14z" class="H"></path><path d="M364 257v3c2-1 4-2 5-1l2 1v-2l1 1c-1 2-5 6-4 8l-1 1h0c0-2 0-3-1-4h-1c-1 0-3 0-4-1l3-6z" class="b"></path><path d="M360 263h1c1 1 3 1 4 1h1c1 1 1 2 1 4h0l-1 2c-1 1-2 2-4 3l-3-9 1-1z" class="N"></path><path d="M360 263h1c1 1 3 1 4 1h1c1 1 1 2 1 4h0l-1 2h-1c0-1 0-2 1-2-1-2-3-3-4-4-1 0-1 0-2-1z" class="h"></path><path d="M723 696c6 0 10 0 15 2 1 1 3 1 4 2h0 2l2 1 2 4v1h-1c5 4 10 9 14 14-2 0-3-1-4-2l-2-2h-1c-1-1-2-1-2-2-2 0-2-1-3-1-2-1-3-2-4-2l-3-1-3-1c-8-2-20 1-28 3-2 0-4 0-6 1h-1v-1l2-1c-1-2-1-3-3-4h0c-1-2 0-3 0-4l2-2c1 0 3 1 4 0h1c1 1 4-1 7 0h0 1c-1-1-1-1-1-2 1 0 2 0 2 1l1-1v-1c-1 1-2 1-4 0h0c3 0 5-2 7-2z" class="AG"></path><path d="M742 700h0 2l2 1 2 4v1h-1c-3-1-5-3-8-4l3-2z" class="AE"></path><path d="M742 700h2l2 1 2 4c-3-1-4-2-6-5z" class="AF"></path><path d="M729 705c4 0 7 1 11 2h-3c-5-1-11-1-16 0l-4 1-7 2h-1-1l2-3 1 1c2-1 3-1 4-2l14-1z" class="M"></path><path d="M723 696c6 0 10 0 15 2 1 1 3 1 4 2l-3 2c-7-2-13-4-20-2l1-1v-1c-1 1-2 1-4 0h0c3 0 5-2 7-2z" class="j"></path><path d="M276 492l1 1c0 2 0 4-1 5v3c-1 1-1 1-1 2l1 1c-1 2-2 5-3 7v3c-1 1-1 2-1 3l1-1c0-1 1-2 1-3l1 2h2c0-1 0-1 1-2l1 1h2l-1 2-2 7c-2 2-4 5-7 5-1 1-2 1-2 1-1 2-1 2-3 3h-3c-1 1-2 1-2 1-2 0-2 0-3 1h-2c-2 0-2 0-2 1l-2-1-1 1-1-3c-4 2-8 3-12 4l-3 2c0-1-2-1-3-1 3-2 6-3 9-5 7-3 13-6 19-12 0-1 0-2 1-3h0c1-2 2-3 3-5h0c1-1 1-2 1-3s0-1 1-2h1c0-1 0-2 1-2l2-5 1 2h1l3-8 1-2z" class="AT"></path><path d="M259 524c2-2 3-4 5-6 1-2 2-4 4-5l-1 4c0 1-1 2-2 3v1c0 2-1 3-3 5 0-1-1-1-1-1-1 0-2-1-2-1z" class="p"></path><path d="M259 524s1 1 2 1c0 0 1 0 1 1-2 1-5 3-8 4-1 1-3 1-4 2-4 2-8 3-12 4h0l10-5v1c2 0 3-2 4-3 3-1 5-3 7-5z" class="AL"></path><path d="M276 492l1 1c0 2 0 4-1 5v3c-1 1-1 1-1 2l1 1c-1 2-2 5-3 7s-4 9-6 10h-2v-1c1-1 2-2 2-3l1-4h0c1-1 3-5 3-7 1-1 1-2 1-4l3-8 1-2z" class="U"></path><path d="M273 511v3c-1 1-1 2-1 3-1 2-2 3-3 4s-1 1-1 2c-1 2-7 6-6 8 2 0 4-1 7-2-1 2-1 2-3 3h-3c-1 1-2 1-2 1-2 0-2 0-3 1h-2c-2 0-2 0-2 1l-2-1-1 1-1-3c1-1 3-1 4-2 3-1 6-3 8-4 2-2 3-3 3-5h2c2-1 5-8 6-10z" class="P"></path><path d="M265 521h2c-4 6-9 9-15 13l-1 1-1-3c1-1 3-1 4-2 3-1 6-3 8-4 2-2 3-3 3-5z" class="I"></path><path d="M274 513l1 2h2c0-1 0-1 1-2l1 1h2l-1 2-2 7c-2 2-4 5-7 5-1 1-2 1-2 1-3 1-5 2-7 2-1-2 5-6 6-8 0-1 0-1 1-2s2-2 3-4l1-1c0-1 1-2 1-3z" class="J"></path><path d="M273 516l1 1 2-1v1c0 1-1 2-2 2-2 1-3 1-4 2h-1c1-1 2-2 3-4l1-1z" class="N"></path><path d="M427 768v-2h1c1 1 2 1 3 2 0 1 1 2 1 3l2 2c1 3 1 5 2 8l2 4c0 2 1 3 1 4h2v1 1c0 1 1 4 1 5v1l-1-1v-2l-1-1h0c-3 0-4-1-5-2l-3-2c-2-1-4-2-6-2l-2-1-3-1h-3c-1-1-2-1-4-1-1 0-3 0-4 1-4 0-7 1-10 4h-1c-2 1-4 3-5 5v1c-2 1-2 3-3 4 0 1-1 2-1 3v2c-1 0-1 2-1 3v4c-1 1 0 3 0 5l-1 1v-3c0-5 0-10 2-14l-1-1c0 2-1 5-2 6 0-5 1-9 2-13 2-5 4-9 8-12h0c3-3 6-4 10-5 4-2 9-2 13-2l3 2c1 0 2 1 3 0 2 1 3 2 5 3v-1h0v-1l-4-8z" class="AG"></path><path d="M402 783c2-1 6-3 9-2h1c2 1 5 1 7 1 6 1 15 5 19 9l-1 1c-6-4-11-7-18-8h-1l-1-1c-3-1-6-1-8 0l-5 1-2-1z" class="AA"></path><path d="M427 768v-2h1c1 1 2 1 3 2 0 1 1 2 1 3l2 2c1 3 1 5 2 8-3-1-3-3-6-3s-8-2-11-3c-2-1-4 0-7 0h-1-4 0c4-2 9-2 13-2l3 2c1 0 2 1 3 0 2 1 3 2 5 3v-1h0v-1l-4-8z" class="B"></path><path d="M389 792c2-5 4-9 8-12v1c-1 1-1 2-1 2-1 1-1 2-1 2 1 1 1 1 2 1l5-3 2 1 5-1c2-1 5-1 8 0l1 1c-8-1-15 0-21 6-3 3-5 6-7 10l-1-1c0 2-1 5-2 6 0-5 1-9 2-13z" class="Z"></path><path d="M389 792c2-5 4-9 8-12v1c-1 1-1 2-1 2-1 1-1 2-1 2 1 1 1 1 2 1-4 3-7 8-8 13 0 2-1 5-2 6 0-5 1-9 2-13z" class="J"></path><path d="M283 458c1-2 0-4 0-6h1c0 1 0 2 1 3 0 1 0 1 1 1h0l1 2v2l1 3v2 1c1 1 0 3 0 4 1 2 1 4 0 6h0v3h1v-2h0c0 1 0 1 1 1 0 2-1 4 0 5v2c0 1 0 2-1 4h1c0 1 0 2-1 3v2c0 1-1 2-1 2 0 2 0 3-1 5 0 1 0 2-1 3l-2 9c0 3-1 6-2 9l-1-1-1 3h-1l-1-1 2-7 1-2h-2l-1-1c-1 1-1 1-1 2h-2l-1-2c0 1-1 2-1 3l-1 1c0-1 0-2 1-3v-3c1-2 2-5 3-7l-1-1c0-1 0-1 1-2v-3c1-1 1-3 1-5l-1-1c-1-1 0-3 0-4 1-3 1-5 1-8 1-4 1-11 0-16v1c1 0 1 1 1 1 1 0 1 1 1 0 1 4 1 8 1 12 0-1 0-2 1-3 1-2 1-4 1-6l-1-3c0-1 0-2 1-3h1 0c-1-2-1-4 0-5z" class="W"></path><path d="M284 479c2-2 2-2 4-3v3 2c-1 1-1 1-1 2h-1v-1h-1l-1 1-1-3c1-1 1 0 1-1z" class="R"></path><path d="M277 506c1-3 3-7 5-10v1c0 3 3 4 1 6v3c0 1-1 2-2 3v-2c0-1-1-1-1-2v-1h0c-2 1-2 1-3 2z" class="x"></path><path d="M277 506c1-1 1-1 3-2h0v1c0 1 1 1 1 2v2c0 2 1 3 0 4v1h0-2l-1-1c-1 1-1 1-1 2h-2l-1-2c1-1 1-3 2-4s1-2 1-3z" class="N"></path><path d="M280 516c3-2 3-3 4-6v-1c0-1 1-2 1-3v-3l2-6 1-1c0 2 0 3-1 5 0 1 0 2-1 3l-2 9c0 3-1 6-2 9l-1-1-1 3h-1l-1-1 2-7z" class="Z"></path><path d="M277 464v1c1 0 1 1 1 1 1 0 1 1 1 0 1 4 1 8 1 12v9c-1 5-2 12-4 17l-1-1c0-1 0-1 1-2v-3c1-1 1-3 1-5l-1-1c-1-1 0-3 0-4 1-3 1-5 1-8 1-4 1-11 0-16z" class="p"></path><path d="M283 458c1-2 0-4 0-6h1c0 1 0 2 1 3 0 1 0 1 1 1h0l1 2v2l1 3v2 1c1 1 0 3 0 4 1 2 1 4 0 6h0c-2 1-2 1-4 3 0 1 0 0-1 1-1 0-1 0-1 1v2h0l-1-2c0 2-1 4-1 6v-9c0-1 0-2 1-3 1-2 1-4 1-6l-1-3c0-1 0-2 1-3h1 0c-1-2-1-4 0-5z" class="N"></path><path d="M282 463h1c0 3 0 6 1 10v6c0 1 0 0-1 1-1 0-1 0-1 1v2h0l-1-2c0 2-1 4-1 6v-9c0-1 0-2 1-3 1-2 1-4 1-6l-1-3c0-1 0-2 1-3z" class="AA"></path><path d="M280 478c0-1 0-2 1-3 1-2 1-4 1-6v6 3c-1 1 0 2-1 3 0 2-1 4-1 6v-9z" class="d"></path><path d="M738 298c2 0 6 0 8-2h0c1 1 2 1 3 0 1 0 5-2 6-2l-8 20c-1 4-3 7-4 11h-1v2c-4 1-5 4-8 5-2 1-6 1-7 3v1l1 2h-1l-3-3h-2v1 1l-1-1c-1-1 2-8 3-10 1-3 5-8 5-11v-1c2-1 3-1 4-3 0-2 0-2-1-3l1-2-1 1-1-1c2-2 3-4 4-7 0-1 1-2 2-3l1 2z" class="y"></path><path d="M735 299c0-1 1-2 2-3l1 2h-2v1 2c-1 1-1 1-1 2v1l1-1c0 2 0 5-1 6v1c-1 1-2 4-3 5h-2c-1 1-1 3-1 4v1l1-1v1l-3 6h-3c1-3 5-8 5-11v-1c2-1 3-1 4-3 0-2 0-2-1-3l1-2-1 1-1-1c2-2 3-4 4-7z" class="j"></path><path d="M380 204c-1-3-3-6-4-9-1-1-1-2-1-3 0-3 4-9 6-11 1 2 4 5 6 6 5 4 10 6 16 8v1c-2 2-5 2-8 2v1c6-1 12-2 17-1 1 0 2 0 4 1 0 0 1 0 2-1 2 0 3 0 4-1h2c1 0 3-1 4-2l3-4c-1 1-1 1-1 2-1 1 0 1-1 2 0 0 0 1-1 1l-1 1c-2 1-4 2-5 3l-21 7v1l-6 2v3l-4 1 2 1-1 1c-2-1-4-3-6-5l-6-7z" class="AK"></path><path d="M386 211h1c4-1 9-3 14-4v1l-6 2v3l-4 1 2 1-1 1c-2-1-4-3-6-5z" class="M"></path><path d="M395 210v3l-4 1v-2c1-1 2-1 4-2z" class="B"></path><path d="M414 749h0c4 1 6 2 8 5 1 2 1 4 1 6 1 3 3 6 4 8l4 8v1h0v1c-2-1-3-2-5-3-1 1-2 0-3 0l-3-2c-4 0-9 0-13 2-4 1-7 2-10 5h0c-4 3-6 7-8 12l-1-1 1-6 2-8c1-2 0-5 0-8h1v1l1 2v4c3-5 7-12 9-17 1-2 1-3 2-4l1-1h0c0 2 0 2 2 3h1c0-2 1-3 2-4 0-1 1-2 1-2 1-2 2-2 3-2z" class="F"></path><path d="M410 765h2l1 3v1c1-1 1 0 2-1h0v3h-2c-2 0-3-2-4-3l1-3z" class="E"></path><path d="M403 765l1-1c1 0 2-1 3-2s2-2 3-2c2 2 3 1 3 5l1 1-1 2-1-3h-2l-1-1c-1 1-2 2-2 3l-1-1h0c-2 0-4 3-5 3 0-1 0-1 1-2h-1s-1 1-2 1l2-3h2z" class="AB"></path><path d="M410 753c2 0 3 1 5 2 0 1-1 1 0 3l-1 1-1-1c1 2 2 3 3 5l-3 2c0-4-1-3-3-5-1 0-2 1-3 2s-2 2-3 2l-1 1c0-2 1-3 2-4l-1-5 1-2h0c0 2 0 2 2 3h1c0-2 1-3 2-4z" class="e"></path><path d="M410 753c2 0 3 1 5 2 0 1-1 1 0 3l-1 1-1-1h-1c-1 0-1 0-2-1v-1h-1l-1 1c-1 2-2 3-3 4l-1-5 1-2h0c0 2 0 2 2 3h1c0-2 1-3 2-4z" class="H"></path><path d="M404 755l1-1-1 2 1 5c-1 1-2 2-2 4h-2l-2 3c-2 5-4 9-6 13 7-6 14-9 24-9l3 1h0c-4 0-9 0-13 2-4 1-7 2-10 5h0c-4 3-6 7-8 12l-1-1 1-6 2-8c1-2 0-5 0-8h1v1l1 2v4c3-5 7-12 9-17 1-2 1-3 2-4z" class="I"></path><path d="M404 756l1 5c-1 1-2 2-2 4h-2l3-9z" class="n"></path><path d="M414 749h0c4 1 6 2 8 5 1 2 1 4 1 6 1 3 3 6 4 8l4 8v1h0v1c-2-1-3-2-5-3-1 1-2 0-3 0l-3-2h0l-3-1-2-1v-3h0c-1 1-1 0-2 1v-1l1-2-1-1 3-2c-1-2-2-3-3-5l1 1 1-1c-1-2 0-2 0-3-2-1-3-2-5-2 0-1 1-2 1-2 1-2 2-2 3-2z" class="Q"></path><path d="M414 749c4 1 6 2 8 5 1 2 1 4 1 6 1 3 3 6 4 8l4 8c-4-1-5-4-7-7-1-1-1-2-2-3 1-1 1-1 1-2s-1-4-2-5h0c-1-1 0-1-1-2s-1-1-1-2h0-2c0-2-2-4-3-6z" class="W"></path><path d="M415 758h1c1 1 1 1 1 2-1 1 0 2 0 3v1c2 2 4 3 4 6l3 3c2 1 0-1 2 1l1 1h-1c-1 1-2 0-3 0l-3-2h0l-3-1-2-1v-3h0c-1 1-1 0-2 1v-1l1-2-1-1 3-2c-1-2-2-3-3-5l1 1 1-1z" class="P"></path><path d="M416 763c0 1 1 2 1 3 0 0-1 0-1 1 1 0 2 2 2 2-1 0-2 0-3-1h0c-1 1-1 0-2 1v-1l1-2-1-1 3-2z" class="O"></path><path d="M415 768c1 1 2 1 3 1 2 2 5 2 5 6l-3-2h0l-3-1-2-1v-3z" class="e"></path><path d="M455 190h0l1-7h0v5l1 3h2l1 1c-1 2-2 2-2 3h1v1c-1 0-1 0-2 1v2c0 3-1 9 2 11l1 1-3 1h0c0 2-1 2-1 4h1c-1-1-1-1-2-1-4 3-7 5-11 7-5 3-11 3-16 4-1 0-2 1-2 1-2 1-4 0-6 1 0 1 0 1 1 2v1l3 1c-4 1-6 1-9 0h-2l-4-1c-1 0-2 1-3 1l5 2v1l-1 1v4c1 0 2 1 3 2-2 0-4-1-6-2h0c-1 0-3-1-4-1s-3-1-4-3h-1c-4-3-7-7-10-11-1-2-2-3-3-4l-1-2-1-2c0-1 0-3-1-4h0c-1-3-2-5-3-7l-3-3c0-1 0-2 1-3l2 4h1l6 7c2 2 4 4 6 5 12 8 26 10 40 8h1c6-1 12-3 17-8s5-10 5-17l1-5c0-1 0-2-1-4z" class="AU"></path><path d="M456 188l1 3h2l1 1c-1 2-2 2-2 3h1v1c-1 0-1 0-2 1v2l-1-11z" class="V"></path><path d="M377 200l2 4c1 2 2 3 4 5l1 1c2 3 5 5 7 7 5 4 11 6 17 8l-2 1c-4-1-9-3-13-5h0l-1 1c-1 0-1 0-1 1h-3c1 1 2 2 2 4h1c0 1 1 2 2 2 1 1 0 0 0 2 1 1 2 1 3 2 2 1 2 2 3 3h1-1-1c-4-3-7-7-10-11-1-2-2-3-3-4l-1-2-1-2c0-1 0-3-1-4h0c-1-3-2-5-3-7l-3-3c0-1 0-2 1-3z" class="U"></path><path d="M377 200l2 4c1 2 2 3 4 5l1 1c2 3 5 5 7 7-1 1-2 1-3 2-2-1-2-2-2-3-1-1-1-2-2-3h0c0 2 2 5 2 6s0 1-1 2l-1-2-1-2c0-1 0-3-1-4h0c-1-3-2-5-3-7l-3-3c0-1 0-2 1-3z" class="F"></path><path d="M399 236h1-1c-1-1-1-2-3-3-1-1-2-1-3-2 0-2 1-1 0-2-1 0-2-1-2-2h-1c0-2-1-3-2-4h3c2 2 5 4 9 5 3 0 6 2 9 3-1 0-2 1-3 1l5 2v1l-1 1v4c1 0 2 1 3 2-2 0-4-1-6-2h0c-1 0-3-1-4-1s-3-1-4-3z" class="e"></path><path d="M402 234s-1-1-2-1v-2l3 1c1 0 1 1 2 1l1-1 5 2v1h-1c-2 0-5 0-8-1z" class="H"></path><path d="M402 234c3 1 6 1 8 1h1l-1 1v4c1 0 2 1 3 2-2 0-4-1-6-2h0c-1 0-3-1-4-1h1l-1-1h0c-1-1-1-3-1-4z" class="Q"></path><path d="M402 234c3 1 6 1 8 1l-1 1c-1 1-2 1-3 1 0 2 0 2 1 3-1 0-3-1-4-1h1l-1-1h0c-1-1-1-3-1-4z" class="o"></path><path d="M392 222l1-1h0c4 2 9 4 13 5l2-1c6 1 13 1 20 1-1 0-2 1-2 1-2 1-4 0-6 1 0 1 0 1 1 2v1l3 1c-4 1-6 1-9 0h-2l-4-1c-3-1-6-3-9-3-4-1-7-3-9-5 0-1 0-1 1-1z" class="n"></path><path d="M392 222c7 4 14 6 21 9v1l-4-1c-3-1-6-3-9-3-4-1-7-3-9-5 0-1 0-1 1-1z" class="q"></path><defs><linearGradient id="R" x1="246.146" y1="539.294" x2="242.086" y2="555.706" xlink:href="#B"><stop offset="0" stop-color="#020403"></stop><stop offset="1" stop-color="#291110"></stop></linearGradient></defs><path fill="url(#R)" d="M238 536c4-1 8-2 12-4l1 3-2 1v1c1 0 3 1 4 2v1c8 3 14 7 20 13v3c-1 0-3-2-4-2l-1-1-2 1v2c1 1 1 3 0 4l-1 1h1c0 1 1 2 1 3l2 2v1h-2-1l-2-3c-2-3-6-6-9-7-1-1-1-1-1-2h-3c-3-2-8-3-12-4 0 0 0-1-1-1h-10c-2 1-5 0-7 0l-1 1c-2 0-2 0-3-1v-2c5-4 13-7 19-9 0 0 0-1-1-1l3-2z"></path><path d="M259 555c1 0 2 0 3 1h0c1 1 2 1 3 1v1-3h-1c-1-2-2-3-3-5 3 0 4 2 7 3l-2 1v2c1 1 1 3 0 4-1-2-5-4-7-5z" class="x"></path><path d="M238 536c4-1 8-2 12-4l1 3-2 1v1c1 0 3 1 4 2v1c-5-2-11-2-17-1 0 0 0-1-1-1l3-2z" class="D"></path><path d="M221 550h0l2-1h1c1-1 3-1 5-1 6-1 12 0 18 2l1 2h1c1 1 3 2 5 3h-3c-3-2-8-3-12-4 0 0 0-1-1-1h-10c-2 1-5 0-7 0z" class="k"></path><path d="M247 550c5 1 8 3 12 5 2 1 6 3 7 5l-1 1h1c0 1 1 2 1 3l2 2v1h-2-1l-2-3c-2-3-6-6-9-7-1-1-1-1-1-2-2-1-4-2-5-3h-1l-1-2z" class="AA"></path><path d="M718 639h1c3 2 6 3 9 4l3 1 1-2 4 3 1 2v1l15 10 2 3-1 1-5-2c-6-4-17-3-24-2-5 1-10 3-15 6l-3 1c-6 0-11 6-14 10-1 2-1 4-2 6-1 1-2 2-3 2l-1 2c0-4 2-6 2-10 4-10 13-17 21-22l-2-1c1-1 3-2 4-3h1l-1-1c-2 1-4 2-6 2l-1-1 5-4h0 2c1 0 2-1 2-2 2-2 4-1 6-1l1-1-2-2z" class="B"></path><path d="M732 642l4 3 1 2v1c-2-1-4-1-6-2h0v-2l1-2z" class="G"></path><path d="M728 643l3 1v2h0c-8 1-15 3-22 7l-2-1c1-1 3-2 4-3h1l-1-1h0c4-2 9-4 14-4 1 0 2 0 3-1z" class="AO"></path><path d="M718 639h1c3 2 6 3 9 4-1 1-2 1-3 1-5 0-10 2-14 4h0c-2 1-4 2-6 2l-1-1 5-4h0 2c1 0 2-1 2-2 2-2 4-1 6-1l1-1-2-2z" class="F"></path><path d="M652 733c3-1 6-1 9-2 2 0 5 1 7 0v-2l2 2-1 1 1 1c1 0 2-1 3-2l3 1c11 6 20 14 27 24l1 1c1 2 5 6 5 8h-1v-1c0-1-1-1-1-2l-3-4-1-1c-2-1 0 0-1-2-1-1-2-1-2-2l-1-1-1-1-2-2c-1-1 0 0-1-2-1-1-1-1-2-1-2-2-3-4-6-5-1-1-2-3-4-3l-3-3c-2-1-5-3-8-3-1 1-2 2-2 4l1 1-1 1h-1v-1h0v-2l-1-3c-2 0-7-1-8 1h-4c-1 0-2 0-3 1h-4l-2 1h-1c-2 1-4 2-6 4-3 1-5 3-8 6h-1c-1 1-2 2-2 3-1 2-3 2-4 4 0 1-1 2-2 3l-1 1-1 2-2 2-4 7-1 2-3 7c-1 1-1 1-1 2-1 3-2 5-3 8h0c-1 1-1 2-1 3l-1 2-1 3v3c-1 1-1 2-1 3v3c-1 1-1 2-1 3v3c-1 1-1 2-1 3v3c-1 1-1 2-1 3v4c-1 1-1 2-1 3v1c0 1 0 1-1 2h-1 0v-1-4h0c-1-1-2-1-3-2s-1-2-3-3h0c-2-1-2-2-4-2l-1-1h-7v1h-1c-1 0-2 0-2 1-1 0-2 0-2 1h-1c-2 1-4 5-6 5 2-3 5-6 9-8 5-2 9-1 13 1 3 1 5 3 7 5v1h1l1-1h-1c0-4 1-8 1-12 0-1 0-2-1-3v-1-2c-1-1-1-3-1-4l-2-4c0-2-1-4-3-5-1-1 0 0 0-2-2-2-6-5-8-6-1-1-2-2-3-2-2-1-4-1-6-2h-1v1c0 1-1 2-1 3l-1 2h0c0 1-1 2-1 4l-4 11c0 1-1 2-1 3l-3 10c-1 1-1 2-2 3l-3 10c0 1-1 1-1 2v1h0c-1 1-1 1-1 2l-3 8v3l-1 3h0l-3 6-3 10c0 1-1 2-1 3l-16 47v2c-1 1 0-1-1 1v1-1c0-3 1-7 2-10l8-23 39-113c1 1 2 1 3 1s2 1 3 1c2 1 4 2 6 4 2 0 4 2 5 4 1 0 1 1 1 2l2 2c1 1 2 3 2 5h1c0-1 0-1 1-3h0 3l1-3 2-5h1v3 2l4-9c2-3 4-7 6-10 1 0 3-4 4-4 1-3 3-5 5-8 2-2 5-4 7-6s5-4 8-6 7-3 11-4z" class="AG"></path><path d="M611 771c2-3 4-7 6-10-4 8-8 15-11 23-2 7-4 15-5 23 0 2-2 15-3 15 1-10 1-22-5-31v-2c2 2 4 4 5 6l-1-4c-1-7-4-14-9-18 2 0 4 2 5 4 1 0 1 1 1 2l2 2c1 1 2 3 2 5h1c0-1 0-1 1-3h0 3l1-3 2-5h1v3 2l4-9z" class="AQ"></path><path d="M606 775h1v3 2l-3 8-2-2 1-3 1-3 2-5z" class="U"></path><path d="M600 783h3l-1 3 2 2-3 11-1-5c0-2 0-5-1-7v-1c0-1 0-1 1-3h0z" class="F"></path><path d="M600 783h3l-1 3c0 1 0 2-1 3l-1-1v-5h0z" class="e"></path><defs><linearGradient id="S" x1="627.33" y1="723.668" x2="641.05" y2="763.112" xlink:href="#B"><stop offset="0" stop-color="#040000"></stop><stop offset="1" stop-color="#5d0c0c"></stop></linearGradient></defs><path fill="url(#S)" d="M670 713c4 2 6 4 9 7h-1c-2-1-3-3-5-3-2 1-3 2-4 4 0 1-1 2-1 3h1c0 1 1 1 2 2-1 2-1 2-1 4 1 0 1 0 1 1h-1l-2-2v2c-2 1-5 0-7 0-3 1-6 1-9 2-4 1-8 2-11 4s-6 4-8 6-5 4-7 6c-2 3-4 5-5 8-1 0-3 4-4 4-2 3-4 7-6 10l-4 9v-2-3h-1l-2 5c-1-1-1-1-1-2s-1-2-1-3l4-11 2-5c1-1 3-6 4-7 3-7 7-13 12-19 10-12 23-19 38-20h0c2-1 5 0 8 0z"></path><path d="M670 713c4 2 6 4 9 7h-1c-2-1-3-3-5-3-2 1-3 2-4 4l-1-1v-1l-1 1-2-2h4v-1c0-1 1-2 1-2l-1-2h-7 0c2-1 5 0 8 0z" class="H"></path><path d="M636 728l2-2c7-5 18-9 27-8l2 2 1-1v1l1 1c0 1-1 2-1 3h-1v-3c-2-1-10 1-13 1h-4c-2 0-4 1-6 2l-1 1s-1 0-1 1l-1-1-2 2c-1 0-2 0-3 1z" class="m"></path><path d="M634 730l1 1c-1 1-1 2-2 3v1l2-1 4-3c-1 2-3 4-5 5l-8 8c-2 0-4 3-7 4 2-5 6-9 10-13 1-2 3-4 5-5z" class="Z"></path><path d="M634 730l1 1c-1 1-1 2-2 3v1l2-1-9 8c1-3 2-5 3-7h0c1-2 3-4 5-5z" class="Q"></path><path d="M619 748c3-1 5-4 7-4-2 3-6 6-7 9v1l-6 12c-1 2-3 4-2 5l-4 9v-2-3h-1c3-10 8-18 13-27z" class="E"></path><defs><linearGradient id="T" x1="659.014" y1="715.914" x2="641.803" y2="737.44" xlink:href="#B"><stop offset="0" stop-color="#180101"></stop><stop offset="1" stop-color="#3d0706"></stop></linearGradient></defs><path fill="url(#T)" d="M636 728c1-1 2-1 3-1l2-2 1 1c0-1 1-1 1-1l1-1c2-1 4-2 6-2h4c3 0 11-2 13-1v3 1l-2-2c-1-1-11 2-13 2-2 1-4 2-5 2-2 0-6 2-8 3v1l-4 3-2 1v-1c1-1 1-2 2-3l-1-1 2-2z"></path><path d="M639 731v-1c2-1 6-3 8-3 1 0 3-1 5-2 2 0 12-3 13-2l2 2v-1h1 1c0 1 1 1 2 2-1 2-1 2-1 4 1 0 1 0 1 1h-1l-2-2v2c-2 1-5 0-7 0-3 1-6 1-9 2-4 1-8 2-11 4s-6 4-8 6-5 4-7 6c-2 3-4 5-5 8-1 0-3 4-4 4-2 3-4 7-6 10-1-1 1-3 2-5l6-12v-1c1-3 5-6 7-9l8-8c2-1 4-3 5-5z" class="W"></path><path d="M633 743c0-1 1-2 2-3l1-1s1-1 2-1c1-1 1 0 1-1 1-1 2-2 3-2s2-1 2-1c2-2 2-2 4-3 2 0 2 0 4 2-4 1-8 2-11 4s-6 4-8 6z" class="d"></path><defs><linearGradient id="U" x1="753.116" y1="255.009" x2="769.09" y2="263.084" xlink:href="#B"><stop offset="0" stop-color="#7c7b7c"></stop><stop offset="1" stop-color="#949394"></stop></linearGradient></defs><path fill="url(#U)" d="M774 229h1c4-1 8-4 11-7l-28 65c3 0 7 0 9-2v-1c-1-3-1-5 0-7h1v1c0 2 0 5 1 7-1 2-2 2-4 3s-4 0-6 1-2 2-3 3-3 1-4 1v1c-4 1-10 1-14 2 1-1 1-1 1-2 1-2 1-3 2-5l2-8 12-30c4-9 10-17 11-26v-1l4 3c1 2 2 1 4 2z"></path><path d="M761 259l1-1c0 2-1 3-1 5 0 0-1 1-1 2v1l-1 2-1 1v-4h1l-1-1c0-2 1-4 3-5z" class="y"></path><path d="M435 197l5-1c5 0 9 1 14 1v1l1 1c0 7 0 12-5 17s-11 7-17 8h-1c-14 2-28 0-40-8l1-1-2-1 4-1v-3l6-2v-1l21-7c3-1 6-2 8-4l1 1 1 1h1l2-1z" class="AG"></path><path d="M439 210h1c1 2 1 3 2 5h0l-1 1h0-1c-1-1 0-1 0-2s-1-3-1-4z" class="J"></path><defs><linearGradient id="V" x1="424.501" y1="194.737" x2="422.97" y2="215.148" xlink:href="#B"><stop offset="0" stop-color="#4c4a4b"></stop><stop offset="1" stop-color="#616365"></stop></linearGradient></defs><path fill="url(#V)" d="M435 197l5-1c5 0 9 1 14 1v1c-7 2-14 3-21 5-12 4-23 10-35 12-2 1-3 1-5 0l-2-1 4-1v-3l6-2v-1l21-7c3-1 6-2 8-4l1 1 1 1h1l2-1z"></path><path d="M395 213c1 0 3-1 5-1l-1 1c0 1-1 1-2 1l1 1c-2 1-3 1-5 0l-2-1 4-1z" class="t"></path><path d="M401 208l3-1h3c1-1 2-1 4-1l3-1c1-1 3-1 4-1l-1 1-1 1c-5 2-11 4-16 6-2 0-4 1-5 1v-3l6-2z" class="f"></path><path d="M430 196l1 1 1 1h1c-1 1-2 2-4 3-1 0-2 1-4 1-3 1-6 3-9 4l1-1 1-1c-1 0-3 0-4 1l-3 1c-2 0-3 0-4 1h-3l-3 1v-1l21-7c3-1 6-2 8-4z" class="g"></path><path d="M634 690l1 1c4 0 7 1 11 3h1c1 0 1 0 2 1s2 1 3 0l6 5 3 3c3 2 6 3 8 6-3 1-6-4-10-4l3 3 4 2 4 3c-3 0-6-1-8 0h0c-15 1-28 8-38 20-5 6-9 12-12 19-1 1-3 6-4 7l-2 5-4 11c0 1 1 2 1 3s0 1 1 2l-1 3h-3 0c-1 2-1 2-1 3h-1c0-2-1-4-2-5 0-5-1-10-1-14l1-1c-2-5 1-11-1-15v-2c1-1 1-5 1-7 1-2 1-4 2-6 2-3 3-6 6-8l2-2v-2c2-2 4-3 4-5 2-4 7-6 10-9 0-1 1-2 1-4 0-1 1-1 2-2 2-1 3-2 4-3l3-5 2-4h2 1l-1-1v-1z" class="F"></path><path d="M653 705l1 1c2 0 3 1 5 2 1 1 1 1 2 1s1-1 1-1l4 2c-1 1-3 1-4 1l-2-1c-2-1-7 1-10 1v-1h-1c1-1 1-1 2-1-1 0-1 0-2-1h-1l1-1h3l1 1v-1l-1-1 1-1z" class="E"></path><path d="M613 728l7-7 1-2h1c-2 5-6 9-9 13s-5 8-7 12v-2c1-1 1-2 1-3 0-2 3-7 4-8l1-2 1-1z" class="S"></path><path d="M641 702c2-1 2-1 3 0l1 1h3 2c1 1 2 2 3 2l-1 1 1 1v1l-1-1h-3l-1 1h1c1 1 1 1 2 1-1 0-1 0-2 1h1v1l-9 2c-1 0 0 0 0 0-2-1-3-2-4-1l-1 1-2-1c2 0 2 0 4-2l-1-1c-2 1-5 2-6 4h0v1h-1 0c-2 0-4 1-6 2 3-3 8-5 12-7h-2-1-2-1l2-2 4-2c3 0 3 0 6-2l-1-1z" class="AB"></path><path d="M641 702c2-1 2-1 3 0l1 1h3 2c1 1 2 2 3 2l-1 1c-6-1-11 0-16 3h-2-1-2-1l2-2 4-2c3 0 3 0 6-2l-1-1z" class="P"></path><path d="M628 708c2-1 5-3 8-3l-4 2-2 2h1 2 1 2c-4 2-9 4-12 7l-1 1-1 2h-1l-1 2-7 7-1 1-1 2c-1 1-4 6-4 8l-2 3c-1 0-1 1-2 1v1 2c-1 1-1 2-2 3 0-5 3-9 5-13l1-2c1-2 1-4 1-5l-1-1-1 1h-1c1-1 1-1 1-2v-1h0v-2c2-2 4-3 4-5 2-4 7-6 10-9h0v2c1 0 3-1 4-2 1 0 3-1 4-2z" class="O"></path><path d="M613 728c0-2 3-7 5-8s2 0 3-2c1 0 1-1 2-1l-1 2h-1l-1 2-7 7z" class="e"></path><path d="M610 722l1 1 2-2v2c-1 1-1 1-1 2 0 2-1 3-2 5-1 1-2 2-3 4 1-2 1-4 1-5l-1-1-1 1h-1c1-1 1-1 1-2v-1c2-1 3-3 4-4z" class="K"></path><path d="M628 708c2-1 5-3 8-3l-4 2c-3 2-7 4-9 7-1 0-3 3-4 3s-1-1-2 0-2 2-3 2l6-7c1 0 3-1 4-2 1 0 3-1 4-2z" class="H"></path><path d="M620 710h0v2l-6 7-4 3c-1 1-2 3-4 4h0v-2c2-2 4-3 4-5 2-4 7-6 10-9z" class="a"></path><path d="M634 690l1 1c4 0 7 1 11 3h1c1 0 1 0 2 1s2 1 3 0l6 5 3 3c3 2 6 3 8 6-3 1-6-4-10-4l3 3s0 1-1 1-1 0-2-1c-2-1-3-2-5-2l-1-1c-1 0-2-1-3-2h-2-3l-1-1c-1-1-1-1-3 0l1 1c-3 2-3 2-6 2s-6 2-8 3c-1 1-3 2-4 2-1 1-3 2-4 2v-2h0c0-1 1-2 1-4 0-1 1-1 2-2 2-1 3-2 4-3l3-5 2-4h2 1l-1-1v-1z" class="AO"></path><path d="M648 697c4 2 7 6 11 8l3 3s0 1-1 1-1 0-2-1c-2-1-3-2-5-2l-1-1c-1 0-2-1-3-2l-4-2c2 0 3 0 5 1v-1c-1-1-2-3-3-4z" class="m"></path><path d="M630 696l2-4h2 1c5 0 9 2 13 5 1 1 2 3 3 4v1c-2-1-3-1-5-1-2-2-4-3-6-4-3-1-5 0-7 1-2 2-3 3-6 3l3-5z" class="B"></path><path d="M633 698c2-1 4-2 7-1 2 1 4 2 6 4l4 2h-2-3l-1-1c-1-1-1-1-3 0l1 1c-3 2-3 2-6 2s-6 2-8 3c-1 1-3 2-4 2-1 1-3 2-4 2v-2h0c0-1 1-2 1-4 0-1 1-1 2-2 2-1 3-2 4-3 3 0 4-1 6-3z" class="O"></path><path d="M624 710l1-1c1-1 2-3 4-3l-1 2h0c-1 1-3 2-4 2z" class="e"></path><path d="M627 701c3 0 4-1 6-3l-1 2-1 1c-4 2-7 7-11 9h0c0-1 1-2 1-4 0-1 1-1 2-2 2-1 3-2 4-3z" class="W"></path><path d="M633 698c2-1 4-2 7-1 2 1 4 2 6 4l4 2h-2-3l-1-1c-1-1-1-1-3 0-1 0-3 1-4 0h-1c-1 0-3 0-4 1h-1 0l1-2h-1l1-1 1-2z" class="Q"></path><path d="M633 698c2-1 4-2 7-1-2 1-3 2-5 4h-1v-1h-2l1-2z" class="K"></path><path d="M606 726v1c0 1 0 1-1 2h1l1-1 1 1c0 1 0 3-1 5l-1 2c-2 4-5 8-5 13 1-1 1-2 2-3v-2-1c1 0 1-1 2-1l2-3c0 1 0 2-1 3v2c-1 2-1 3-2 4l2 1c-1 3-2 5-2 8 0 2 0 3 1 4h1l1-2h1l-2 5-4 11c0 1 1 2 1 3s0 1 1 2l-1 3h-3 0c-1 2-1 2-1 3h-1c0-2-1-4-2-5 0-5-1-10-1-14l1-1c-2-5 1-11-1-15v-2c1-1 1-5 1-7 1-2 1-4 2-6 2-3 3-6 6-8l2-2h0z" class="R"></path><path d="M606 726v1c0 1 0 1-1 2h1l1-1 1 1c0 1 0 3-1 5l-1 2v-6s-1 1-1 2c-1 1-1 1-2 1h-1c0-2 1-2 1-3 1-1 1-1 1-2l2-2h0z" class="Q"></path><path d="M607 739c0 1 0 2-1 3v2c-1 2-1 3-2 4l2 1c-1 3-2 5-2 8-3 4-2 7-4 11-1 1-1 2-1 3v-11c1-4 2-7 2-11 1-1 1-2 2-3v-2-1c1 0 1-1 2-1l2-3z" class="E"></path><path d="M599 771c0-1 0-2 1-3 2-4 1-7 4-11 0 2 0 3 1 4h1l1-2h1l-2 5-4 11c0 1 1 2 1 3s0 1 1 2l-1 3h-3 0c-1 2-1 2-1 3h-1c0-2-1-4-2-5 0-5-1-10-1-14l1-1c0 3 0 7 1 10v1h1c1-1 1-4 1-6z" class="q"></path><path d="M602 775c0 1 1 2 1 3s0 1 1 2l-1 3h-3l2-8z" class="P"></path><defs><linearGradient id="W" x1="301.51" y1="422.242" x2="270.714" y2="434.569" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#220907"></stop></linearGradient></defs><path fill="url(#W)" d="M279 367c1-3 1-4 1-7h0c2 4 1 6 1 10 7 3 11 5 15 10s9 11 10 18c1 1 1 3 1 5h0l-1-1-1-1h-1l-1-2c-1 4-3 6-5 10l-1 2 2 7h-2c-1 1-1 2-1 4-1 4-5 20-3 24v2c1 2 1 5 1 7s0 3 1 5v1c-1 1-1 1-1 2l1 2c1 3 1 5 1 8l3 6 1 2c-1 1-1 3 0 5l-2 2h-1-2 0-1c-1 0-2 0-3-1v-2c0-2 0-4-1-6v-1c-1 0-1 0-1-1h0v2h-1v-3h0c1-2 1-4 0-6 0-1 1-3 0-4v-1-2l-1-3v-2l-1-2h0c-1 0-1 0-1-1-1-1-1-2-1-3h-1c0 2 1 4 0 6-1 1-1 3 0 5h0-1c0-1 0-3-1-4-2-2-1-5-3-8h-1c0-1 0-2-1-3 0-1-1-3-2-4-1-2-3-4-5-4h0l-3-3h-1l-4 1c-1 0-2 1-4 1h-1l-2-2-2 1h0v-1h1c1-1 2-2 4-2h-4l4-3c2 0 4-1 6-2 1 1 1 1 3 1s4 0 7-1c0-2 3-3 4-5 0-1-1-2-1-2 1-2 3-5 3-6l-1-1c0-1 0-2 1-3 0-2 0-3 1-5h0v-12c0-7-2-14-3-22v-3c1-1 1-3 2-4z"></path><path d="M284 374c1 1 3 3 5 4 0 2-1 4 0 6l-1 2-4-12z" class="y"></path><path d="M296 406l1 5 2 7h-2c-1 1-1 2-1 4l-1-1v-1c0-2 1-4 1-6 0-3-2-5 0-8z" class="J"></path><path d="M284 413c1-1 1-2 1-3h1l1-5c1-2 1-3 1-5v-2h1l1 2v2 4c-1 2-2 3-2 5-2 0-3 1-4 2z" class="B"></path><path d="M280 396h0c1 3 1 6 2 9v2c0 2 1 3 1 5l1 1c1-1 2-2 4-2-1 1-1 2-2 3h-1l-2 1v-1c-1-1-1-1-2-1-1-2-1-3-1-5v-12z" class="N"></path><path d="M288 411c0-2 1-3 2-5-1 2-1 8-3 10h0c-1 1-3 0-3 1-2 3-5 6-7 8 0-1-1-2-1-2 1-2 3-5 3-6l-1-1c0-1 0-2 1-3 0-2 0-3 1-5h0c0 2 0 3 1 5 1 0 1 0 2 1v1l2-1h1c1-1 1-2 2-3zm7 10l1 1c-1 4-5 20-3 24v2c1 2 1 5 1 7s0 3 1 5v1c-1 1-1 1-1 2 0 0-1 0-2 1-1-2-1-6-1-8-1-2-1-4-1-6 0-10 4-19 5-29z" class="h"></path><path d="M291 456v-4c0-2 1-3 2-4 1 2 1 5 1 7s0 3 1 5v1c-1 1-1 1-1 2 0 0-1 0-2 1-1-2-1-6-1-8z" class="b"></path><path d="M292 464v-2c1-2 1-2 3-2v1c-1 1-1 1-1 2 0 0-1 0-2 1z" class="a"></path><path d="M292 464c1-1 2-1 2-1l1 2c1 3 1 5 1 8l3 6 1 2c-1 1-1 3 0 5l-2 2h-1-2 0v-4c-1-2-1-4-1-6s-2-4-1-6c0-2 0-3-1-4v-4z" class="m"></path><path d="M292 464c1-1 2-1 2-1l1 2h-1v3h-2v-4z" class="b"></path><path d="M277 425c2-2 5-5 7-8 0-1 2 0 3-1h0c0 1-1 5-2 6-2 3-5 6-9 9h0 1v3l-1 1c-3 2-7 1-11 2h0l-4 1c-1 0-2 1-4 1h-1l-2-2-2 1h0v-1h1c1-1 2-2 4-2h-4l4-3c2 0 4-1 6-2 1 1 1 1 3 1s4 0 7-1c0-2 3-3 4-5z" class="AN"></path><path d="M254 437c4-2 7-4 12-5l-1 1c-1 1-1 2-2 2v1l2 1h0l-4 1c-1 0-2 1-4 1h-1l-2-2z" class="J"></path><path d="M277 425c2-2 5-5 7-8 0-1 2 0 3-1h0c0 1-1 5-2 6-2 0-3 3-5 4-2 2-5 4-7 4 0-2 3-3 4-5z" class="E"></path><path d="M266 432c3 0 7 0 10-1h1v3l-1 1c-3 2-7 1-11 2l-2-1v-1c1 0 1-1 2-2l1-1z" class="B"></path><path d="M277 431c1 0 1 0 2 1 0 1 1 2 1 3s1 2 1 3h0l1 4 2 5c1 3 2 6 2 9-1 0-1 0-1-1-1-1-1-2-1-3h-1c0 2 1 4 0 6-1 1-1 3 0 5h0-1c0-1 0-3-1-4-2-2-1-5-3-8h-1c0-1 0-2-1-3 0-1-1-3-2-4-1-2-3-4-5-4h0l-3-3h-1 0c4-1 8 0 11-2l1-1v-3z" class="x"></path><path d="M265 437c4-1 8 0 11-2 1 3 2 4 1 7 0-2 0-3-2-4-2 0-3 0-4-1h-5-1 0z" class="N"></path><path d="M279 436c0-1-1-1-1-2v-1h0l1-1c0 1 1 2 1 3s1 2 1 3h0l1 4 2 5c1 3 2 6 2 9-1 0-1 0-1-1-1-1-1-2-1-3h-1c0 2 1 4 0 6l-3-13c-1-3-1-5-1-9z" class="W"></path><path d="M279 436v1c1 1 1 2 2 3 0 2 1 3 1 6l-2-1h0c-1-3-1-5-1-9z" class="m"></path><defs><linearGradient id="X" x1="287.782" y1="380.143" x2="301.498" y2="406.126" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#c3c1c0"></stop></linearGradient></defs><path fill="url(#X)" d="M281 370c7 3 11 5 15 10s9 11 10 18c1 1 1 3 1 5h0l-1-1-1-1h-1l-1-2c-1 4-3 6-5 10l-1 2-1-5-8-20 1-2c-1-2 0-4 0-6-2-1-4-3-5-4-1-2-2-2-3-4z"></path><path d="M281 370c7 3 11 5 15 10s9 11 10 18c1 1 1 3 1 5h0l-1-1-1-1h-1l-1-2c1-3 0-6-1-9 0-1-1-3-3-4 0 1 0 1 1 2 1 3 2 4 2 7h-1c0-7-7-13-12-17-2-1-4-3-5-4-1-2-2-2-3-4z" class="Z"></path><path d="M208 316c3-4 6-7 10-10 9-5 20-5 29-3 3 2 7 3 10 5v1h1l2 2 3 9h0c3 9 7 18 10 26 2 6 5 11 6 17 0 1-1 2-1 3-2 2-4 3-6 4l-2-2v-2c1-1 1-3 1-4h0c-1-2-1-5-3-6 0-2-1-3-3-4l-3-3c-3-1-5-2-7-3-4-1-7-3-11-5-13-5-27-6-41-6 1-7 2-13 5-19z" class="AK"></path><path d="M258 309l2 2c-3 1-7 2-10 1 3-1 6-2 8-2v-1z" class="s"></path><defs><linearGradient id="Y" x1="765.471" y1="452.114" x2="780.718" y2="475.999" xlink:href="#B"><stop offset="0" stop-color="#140403"></stop><stop offset="1" stop-color="#5a1516"></stop></linearGradient></defs><path fill="url(#Y)" d="M763 436c7 4 14 6 22 7v1c2 1 4 1 6 1h0 1-2c-1 0-2 0-3 1h5l1 1c1 1 1 0 2 1h-1v1c-2 1-3 2-5 3-3 0-7 2-10 3-4 3-7 6-10 10-2 3-3 6-5 9l1 1c-3 10-3 22 0 32 0 2 2 6 2 8v1c2 5 5 9 9 13l4 4c-4 0-7-2-11-3-3 1-4 0-7-1h-1l-7-2c-2-1-5-1-7-3-4-1-5-4-6-7-1-1-1-4-1-6-1 0 0 0-1-1v-1h0c-1-2-1-5-1-8v-1c-2-3-1-7-2-10v-7h1v-10c0-3 0-9 1-12 1-2 1-2 1-4v-5-1c1-2 0-5-1-6 0-1-1-2-1-3 1-1 1-1 2-1h1 1 2v1c1 0 2 1 2 2 1 2 0 3 1 4h0l-1 1c0 2 1 3 2 4 1 0 1 0 2-1h0 1c1 0 1 0 2-1h7l-3 2v3l5-4h1c2-1 4-3 6-4s5-3 7-4l-4-1-1-1-1-1c-2-1-5-3-6-5z"></path><path d="M756 471c1 1 0 2 0 4v1c1-1 1-2 1-3s0-1 1-2c0 3-2 6-2 10l-1 1-1-3 2-8z" class="R"></path><path d="M764 468l1-3c1 0 1-1 2-2 3-4 6-6 12-8-4 3-7 6-10 10-2 1-3 1-5 3z" class="Q"></path><path d="M769 465c-2 3-3 6-5 9l-2 10c-1-1-2-3-2-4 1-2 1-5 2-7s2-3 2-5c2-2 3-2 5-3z" class="k"></path><path d="M764 474l1 1c-3 10-3 22 0 32 0 2 2 6 2 8v1c-1-1-1-2-2-3v-1c0-1-1-2-1-3-1-1-1-2-1-4-2-6-2-14-1-21l2-10z" class="F"></path><path d="M763 436c7 4 14 6 22 7v1c2 1 4 1 6 1h0 1-2c-1 0-2 0-3 1-3-1-6-1-9 0h-1c-7 2-12 5-16 11-2 3-4 6-5 9l-1-1c2-4 2-7 4-10 1-1 2-2 2-3h1c2-1 4-3 6-4s5-3 7-4l-4-1-1-1-1-1c-2-1-5-3-6-5z" class="I"></path><path d="M756 481l1-1c1 3-1 6 0 8 1 1 0 1 1 2v1l1-1h1v-1-1c1 1 1 1 1 2-2 6 0 13 1 19 1 1 1 3 2 5-1 1-2 1-3 2l-1 1h1c1 1 1 1 1 2h-1-1c0-1-1-1-1-2-2-2-3-4-3-6l1-1h1v-2c0-4-3-9-3-13 1-3 1-7 0-10 1-1 0-2 0-3l1-1z" class="Q"></path><path d="M753 500c1 1 1 3 2 5l1 6c0 2 1 4 3 6 0 1 1 1 1 2h1 1c0-1 0-1-1-2h-1l1-1c1-1 2-1 3-2 1 0 2 4 3 6l6 9h3l4 4c-4 0-7-2-11-3-3 1-4 0-7-1h-1c-1-2-2-4-4-5-3-6-5-14-6-21l1-1 1-2z" class="e"></path><path d="M762 525c2 2 5 4 7 5-3 1-4 0-7-1v-4z" class="AR"></path><path d="M764 514c1 0 2 4 3 6-1 1-2 2-2 3v1c-1 0-2-1-3-2-2-2-2-3-3-5 0 1 1 1 1 2h1 1c0-1 0-1-1-2h-1l1-1c1-1 2-1 3-2z" class="P"></path><path d="M752 502c1 7 3 13 7 19l3 4v4h-1c-1-2-2-4-4-5-3-6-5-14-6-21l1-1z" class="AJ"></path><path d="M752 451h7l-3 2v3l5-4c0 1-1 2-2 3-2 3-2 6-4 10l1 1v1c0 1 1 2 0 3v1l-2 8 1 3c0 1 1 2 0 3 1 3 1 7 0 10 0 4 3 9 3 13v2h-1l-1 1-1-6c-1-2-1-4-2-5l-1 2-1 1v-2c-1-1-2-2-3-2s0-3-1-4c0-1-1-1-1-2v-2-4-5h0v-1c-2-3-1-7-1-10v-3l2-9 1-1c0-2 0-4 1-6h1c1 0 1 0 2-1z" class="AV"></path><path d="M747 478c1 3 1 6 1 8l-2 1v-5l1-4z" class="AL"></path><path d="M749 470v6c1 0 1 0 1-1l1 1v10c-1-2-2-3-2-5-1-3 0-5-1-8l1-3z" class="AQ"></path><path d="M746 487l2-1v6l2-2c1 3 1 7 1 11-1-1-2-2-3-2s0-3-1-4c0-1-1-1-1-2v-2-4z" class="p"></path><path d="M751 476c1-1 2-7 3-8l1-3 1 1v1c-3 6-3 13-4 19 0 2 0 4-1 5v-5-10z" class="AD"></path><path d="M756 456l5-4c0 1-1 2-2 3-2 3-2 6-4 10l-1 3c-1 1-2 7-3 8l-1-1c0 1 0 1-1 1v-6c0-1 1-2 1-3l2-3c0-3 2-4 3-7l1-1z" class="AS"></path><path d="M752 464c1 2 0 3-1 6v1c-1-1-1-2-1-4l2-3z" class="AD"></path><path d="M752 486c1-6 1-13 4-19 0 1 1 2 0 3v1l-2 8 1 3c0 1 1 2 0 3 1 3 1 7 0 10 0 4 3 9 3 13v2h-1l-1 1-1-6c-1-2-1-4-2-5-1-5-1-9-1-14z" class="a"></path><path d="M754 479l1 3c0 1 1 2 0 3v9h-1v-15z" class="h"></path><path d="M752 451h7l-3 2v3l-1 1c-1 3-3 4-3 7l-2 3c0 1-1 2-1 3l-1 3-1 5-1 4h0v-1c-2-3-1-7-1-10v-3l2-9 1-1c0-2 0-4 1-6h1c1 0 1 0 2-1z" class="E"></path><path d="M752 451h7l-3 2c-2 1-3 2-4 2-2 1-3 2-4 3 0-2 0-4 1-6h1c1 0 1 0 2-1z" class="o"></path><path d="M745 468c5-2 5-8 10-11-1 3-3 4-3 7l-2 3c0 1-1 2-1 3l-1 3-1 5-1 4h0v-1c-2-3-1-7-1-10v-3z" class="AI"></path><defs><linearGradient id="Z" x1="735.55" y1="466.012" x2="748.589" y2="480.93" xlink:href="#B"><stop offset="0" stop-color="#600f11"></stop><stop offset="1" stop-color="#8e2220"></stop></linearGradient></defs><path fill="url(#Z)" d="M739 441h1 1 2v1c1 0 2 1 2 2 1 2 0 3 1 4h0l-1 1c0 2 1 3 2 4 1 0 1 0 2-1h0c-1 2-1 4-1 6l-1 1-2 9v3c0 3-1 7 1 10v1h0v5 4 2c0 1 1 1 1 2 1 1 0 4 1 4s2 1 3 2v2c1 7 3 15 6 21 2 1 3 3 4 5l-7-2c-2-1-5-1-7-3-4-1-5-4-6-7-1-1-1-4-1-6-1 0 0 0-1-1v-1h0c-1-2-1-5-1-8v-1c-2-3-1-7-2-10v-7h1v-10c0-3 0-9 1-12 1-2 1-2 1-4v-5-1c1-2 0-5-1-6 0-1-1-2-1-3 1-1 1-1 2-1z"></path><path d="M738 486h2c0 2 0 3-1 5h1s0-1 1-1h0l2-8v10 6c-1 2 0 4-1 5-2 1-2 1-2 3v5c-1 0 0 0-1-1v-1h0c-1-2-1-5-1-8v-1c0-3-1-7 0-9v-5z" class="H"></path><path d="M739 452l2-1-1 3c-1 0-1 1 0 2v1c1-1 1-1 1-2v-1 3 1l-1 3c0 1 0 2 1 2-1 2-1 3-1 4v4c-1 2 0 3-1 5v1 6c-1 1-1 2-1 3v5c-1 2 0 6 0 9-2-3-1-7-2-10v-7h1v-10c0-3 0-9 1-12 1-2 1-2 1-4v-5z" class="R"></path><path d="M739 441h1 1 2v1c1 0 2 1 2 2 1 2 0 3 1 4h0l-1 1c0 2 1 3 2 4 1 0 1 0 2-1h0c-1 2-1 4-1 6l-1 1-1 1c-2-1-3-1-5-1v4c-1 0-1-1-1-2l1-3v-1-3 1c0 1 0 1-1 2v-1c-1-1-1-2 0-2l1-3-2 1v-1c1-2 0-5-1-6 0-1-1-2-1-3 1-1 1-1 2-1z" class="m"></path><path d="M745 444c1 2 0 3 1 4h0l-1 1v1l-1 3s0 1-1 1v-5l-1-1v-1l1-1c1-1 2-1 2-2z" class="b"></path><path d="M745 449c0 2 1 3 2 4 1 0 1 0 2-1h0c-1 2-1 4-1 6l-1 1-1 1c-2-1-3-1-5-1l1-2c0-2 1-2 1-3 1 0 1-1 1-1l1-3v-1z" class="d"></path><path d="M739 441h1 1 2v1c1 0 2 1 2 2s-1 1-2 2l-1 1v1c0 1-1 3-1 4l-1 4c-1-1-1-2 0-2l1-3-2 1v-1c1-2 0-5-1-6 0-1-1-2-1-3 1-1 1-1 2-1z" class="J"></path><path d="M741 441h2v1c1 0 2 1 2 2s-1 1-2 2l-1 1s-1 0-1-1l1-1-1-4z" class="m"></path><path d="M745 471c0 3-1 7 1 10v1h0v5 4 2c0 1 1 1 1 2 1 1 0 4 1 4s2 1 3 2v2c1 7 3 15 6 21 2 1 3 3 4 5l-7-2c-2-1-5-1-7-3-4-1-5-4-6-7-1-1-1-4-1-6v-5c0-2 0-2 2-3 1-1 0-3 1-5v-6-10c0-4 0-7 2-11z" class="w"></path><path d="M745 471c0 3-1 7 1 10v1h0v5 4 2c0 2-1 4 0 6l1 1v8c1 1 1 1 1 2l-2-3v-3c0-1-1-1-2-2s-1-3-1-4v-6-10c0-4 0-7 2-11z" class="F"></path><path d="M745 471c0 3-1 7 1 10v1h0v5 4-1c-1-2-1-6-1-8v-1c-1 4-1 8-2 11v-10c0-4 0-7 2-11z" class="q"></path><path d="M743 498c0 1 0 3 1 4s2 1 2 2v3l2 3v1c1 3 2 6 4 9 1 2 3 4 3 6l-1 1c-2-1-5-1-7-3-4-1-5-4-6-7-1-1-1-4-1-6v-5c0-2 0-2 2-3 1-1 0-3 1-5z" class="AP"></path><path d="M740 511v-5c0-2 0-2 2-3 0 2 1 3 1 4l-1 1v-1h-1c0 1 0 3-1 4z" class="E"></path><path d="M748 511c1 3 2 6 4 9 1 2 3 4 3 6l-1 1c-2-1-5-1-7-3-4-1-5-4-6-7h2v1l3 3h0c1-1 2 0 3-1-1-3-1-6-1-9z" class="F"></path><path d="M577 205l1-1c7 0 16-2 24-1 2 0 4 1 6 1 1-1 2-1 3-1 2 1 5 1 7 1l18 6c3 0 4 0 7 1l2 1c1 0 3 1 4 1 0 1 1 3 2 4 0 1 0 1-1 1v2c3-2 6-4 8-6l1 1h-1l-1 2 1 1v1c1-1 1-1 3-1l1 1c-1 1-1 2-1 3h-1v1 2h0c1 0 2-1 2-1v-2 4c-1 1-1 2-2 3 0 1 0 2-1 3l-1 2c0 2 0 3-1 4h-1c-1 1-2 1-2 2 0 2-1 3-2 4v-2l1-1-1-1-6 3c-6 1-13 3-19 2-2 0-3 0-4 1-5-2-9-6-13-8-1 0-2-1-2-2l-7-3-29-15v-1h1c2-2 3-5 4-7l7 7c1 1 2 2 4 2-5-4-9-7-11-14z" class="AG"></path><path d="M608 204c1-1 2-1 3-1 2 1 5 1 7 1l18 6c3 0 4 0 7 1l2 1c1 0 3 1 4 1 0 1 1 3 2 4 0 1 0 1-1 1l-1-1c-6-3-13-4-20-7l-21-6z" class="g"></path><path d="M636 210c3 0 4 0 7 1l2 1c1 0 3 1 4 1 0 1 1 3 2 4 0 1 0 1-1 1l-1-1h0c-1-1-2-2-4-2-3-2-6-3-9-5z" class="T"></path><defs><linearGradient id="a" x1="605.961" y1="237.848" x2="631.978" y2="219.267" xlink:href="#B"><stop offset="0" stop-color="#530c0b"></stop><stop offset="1" stop-color="#a51d1d"></stop></linearGradient></defs><path fill="url(#a)" d="M572 217h1c2-2 3-5 4-7l7 7c1 1 2 2 4 2v1c1 0 2 0 3 1 2 1 5 2 8 3l2 1 11 2h16 0l6-1c6-1 11-3 16-6 3-2 6-4 8-6l1 1h-1l-1 2 1 1v1c1-1 1-1 3-1l1 1c-1 1-1 2-1 3h-1v1 2h0c1 0 2-1 2-1v-2 4c-1 1-1 2-2 3 0 1 0 2-1 3l-1 2c0 2 0 3-1 4h-1c-1 1-2 1-2 2 0 2-1 3-2 4v-2l1-1-1-1-6 3c-6 1-13 3-19 2-2 0-3 0-4 1-5-2-9-6-13-8-1 0-2-1-2-2l-7-3-29-15v-1z"></path><defs><linearGradient id="b" x1="601.479" y1="232.475" x2="605.99" y2="223.969" xlink:href="#B"><stop offset="0" stop-color="#2c0706"></stop><stop offset="1" stop-color="#541716"></stop></linearGradient></defs><path fill="url(#b)" d="M572 217h1c2-2 3-5 4-7l7 7v1 1l2 2h0c1 1 3 2 4 2 2 2 5 3 7 4 4 2 8 5 13 7 1 1 4 1 6 1 4 1 9 1 14 1h0c3 1 5 1 8 0 1 0 2 0 3 1v1h-1c-4 0-7 1-10 1-5-1-12 1-16-1h-4c-1 0-2-1-2-2l-7-3-29-15v-1z"></path><path d="M647 231c1 0 2 0 3 1l1-1c1 0 0 0 1-1l1 1c-2 1-4 2-6 4h2 0l-1 1v1c2 0 4-1 5-2 0 2-1 3-1 5l-6 3c-6 1-13 3-19 2-2 0-3 0-4 1-5-2-9-6-13-8h4c4 2 11 0 16 1 3 0 6-1 10-1h1v-1c-1-1-2-1-3-1-3 1-5 1-8 0h0c1-1 2-1 4-1 3 0 6-2 8-1 1 1 2 0 3 0 0-2 1-2 2-3z" class="d"></path><path d="M653 235c0 2-1 3-1 5l-6 3c-4-3-6 0-10-1l1-1h1c1 0 1-1 2-1h3l1-1h2l2-2c2 0 4-1 5-2z" class="P"></path><path d="M647 231c1 0 2 0 3 1l1-1c1 0 0 0 1-1l1 1c-2 1-4 2-6 4h2 0l-1 1v1l-2 2h-2l-1 1h-3v-1c-2 0-3 1-5 1h-1c-2 1-6 1-8 1-1 0-4 0-5-1v-1h8 1c3 0 6-1 10-1h1v-1c-1-1-2-1-3-1-3 1-5 1-8 0h0c1-1 2-1 4-1 3 0 6-2 8-1 1 1 2 0 3 0 0-2 1-2 2-3z" class="H"></path><path d="M658 214l1 1h-1l-1 2 1 1v1c1-1 1-1 3-1l1 1c-1 1-1 2-1 3h-1v1 2h0c1 0 2-1 2-1v-2 4c-1 1-1 2-2 3 0 1 0 2-1 3l-1 2c0 2 0 3-1 4h-1c-1 1-2 1-2 2 0 2-1 3-2 4v-2l1-1-1-1c0-2 1-3 1-5-1 1-3 2-5 2v-1l1-1h0-2c2-2 4-3 6-4l-1-1c-1 1 0 1-1 1l-1 1c-1-1-2-1-3-1-1 1-2 1-2 3-1 0-2 1-3 0-2-1-5 1-8 1l1-1h1c1-1 2-1 3-1 0-1 1-2 1-2l-1-1c-3 0-7 1-10 0v-1c-4-1-10 1-13-1h-1-3v-1h16 0l6-1c6-1 11-3 16-6 3-2 6-4 8-6z" class="S"></path><path d="M628 227l6-1c3 2 6 1 9 1-2 1-6 3-8 2l-1-1h-5l-1-1z" class="F"></path><path d="M639 230c6-2 11-3 16-6 1-1 4-2 4-4l1-1h1c-1 1-1 3-1 4v2h0c1 0 2-1 2-1v-2 4c-1 1-1 2-2 3l-1-1 1-2-5 5-1-1 3-3 1-1c-1-1-2-1-2-1l-6 2c-1 1-2 1-4 2s-3 2-6 2l-1-1z" class="o"></path><path d="M640 231c3 0 4-1 6-2s3-1 4-2l6-2s1 0 2 1l-1 1c-1-1-2 0-3 0-2 1-3 2-4 3-1 0-2 0-3 1s-2 1-2 3c-1 0-2 1-3 0-2-1-5 1-8 1l1-1h1c1-1 2-1 3-1 0-1 1-2 1-2z" class="n"></path><path d="M658 214l1 1h-1l-1 2 1 1v1c-5 4-10 6-15 8-3 0-6 1-9-1 6-1 11-3 16-6 3-2 6-4 8-6z" class="AI"></path><path d="M657 227l-3 3 1 1 5-5-1 2 1 1c0 1 0 2-1 3l-1 2c0 2 0 3-1 4h-1c-1 1-2 1-2 2 0 2-1 3-2 4v-2l1-1-1-1c0-2 1-3 1-5-1 1-3 2-5 2v-1l1-1h0-2c2-2 4-3 6-4l-1-1c-1 1 0 1-1 1l-1 1c-1-1-2-1-3-1 1-1 2-1 3-1 1-1 2-2 4-3 1 0 2-1 3 0z" class="O"></path><path d="M653 235c2-1 3-2 4-3v4l-1 1v1c-1 1-2 1-2 2 0 2-1 3-2 4v-2l1-1-1-1c0-2 1-3 1-5z" class="k"></path><path d="M714 637c1 1 3 1 4 2l2 2-1 1c-2 0-4-1-6 1 0 1-1 2-2 2h-2 0l-5 4 1 1c2 0 4-1 6-2l1 1h-1c-1 1-3 2-4 3l2 1c-8 5-17 12-21 22l-6 12v2l2 1-1 1 1 1-1 3 2 2h1l2 1c-1 1-1 2-1 3v1c-1 2-2 3-3 4s-1 2-1 4l-1 1c-1-1-1-1-3-1l-3 1h-1v1l-6-3c-2-3-5-4-8-6l-3-3-6-5c-1 1-2 1-3 0s-1-1-2-1h-1c-4-2-7-3-11-3l-1-1v1l1 1h-1-2l-2 4v-3s1-1 1-2v-1c2-4 4-8 5-12l1-1-2-2c2-1 4-4 6-5h0l-1 3 1 1h0c0-1 1-2 1-3l1 1c1 0 4-2 5-3 0-1 1-1 2-2h0 1c3-2 6-5 10-7 3-3 7-5 11-8l5-5 1 1v-1l1 1c1 0 2 0 3 1l4-4 3-3c2-2 4-1 7-3 1 0 1-1 2-1 3 0 5 0 7 1 3-1 6 0 9 0v-2z" class="AS"></path><path d="M687 663c1-1 2-2 3-2h2c2 0 1 1 2 2 0 2-1 2-2 4l-1-2c0-1 0-1-1-2-1 0-1 0-2 1l-1-1z" class="v"></path><path d="M688 664c1-1 1-1 2-1 1 1 1 1 1 2l1 2-1 1c-1-1-1-1-3-2-1 1-2 2-4 2h0l4-4z" class="AL"></path><path d="M711 648l1 1h-1c-1 1-3 2-4 3-2 1-4 2-5 3-2 2-5 3-7 4l-2-1 1-1c4-2 6-6 10-8l1 1c2 0 4-1 6-2z" class="AT"></path><path d="M703 647c2-1 4-2 6-2l-5 4c-4 2-6 6-10 8l-1 1c-2 1-3 2-5 3v-1c0-1 1-2 2-3-2 0-3 2-4 3h-1l1-2c-1 0-2 1-2 1-1 1-1 1-2 1 1-2 2-3 4-4 0-1 6-4 7-4 2-2 7-4 10-5z" class="G"></path><path d="M703 647c2-1 4-2 6-2l-5 4c-4 2-6 6-10 8-1 0-1 0-2-1h0c1-2 3-2 4-3 3-2 5-4 7-6z" class="D"></path><path d="M673 667c1-1 2-2 2-3l1-1c1-2 4-5 7-6l3-1c-2 1-3 2-4 4 1 0 1 0 2-1 0 0 1-1 2-1l-1 2h1c1-1 2-3 4-3-1 1-2 2-2 3l-2 2c1 1 0 1 1 1l1 1-4 4c-2 2-3 4-4 5l-1 1c0 1-1 2-2 2l-1-1 1-2h0-1l-1-1 2-3h-1l-3-1h0 0v-1z" class="AV"></path><path d="M673 667c3 0 5-3 8-5 0 1 0 2 1 3l-3 3 5-3c-2 2-5 5-5 8h1l-1 1c0 1-1 2-2 2l-1-1 1-2h0-1l-1-1 2-3h-1l-3-1h0 0v-1z" class="AL"></path><path d="M682 687v2l2 1-1 1 1 1-1 3 2 2h1l2 1c-1 1-1 2-1 3v1c-1 2-2 3-3 4s-1 2-1 4l-1 1c-1-1-1-1-3-1l-3 1h-1l-1-2c2-7 4-14 8-22z" class="m"></path><path d="M679 697l2-1 1 1 1 3v2c-1-1-1 0-2 0l-2-2v-3z" class="J"></path><path d="M682 689l2 1-1 1 1 1-1 3s0 1-1 2l-1-1-2 1c1-1 1-1 1-2 1-2 1-4 2-6z" class="N"></path><path d="M684 668h0c2 0 3-1 4-2 2 1 2 1 3 2l-7 12c-1 2-2 6-4 7l-6 16 1-5c-1-1-1-2-1-2-1-2-1-3-1-4 0-2 1-4 0-6v2l-1-2c-1 0-1 1-3 1l1-1c1-2 2-4 2-6h-1c0-3 3-5 3-7v-1h1l1 1h1 0l-1 2 1 1c1 0 2-1 2-2l1-1c1-1 2-3 4-5z" class="w"></path><path d="M672 680h1c1 1 1 2 1 3s0 2-1 3v2l-1-2c-1 0-1 1-3 1l1-1c1-2 2-4 2-6z" class="AB"></path><path d="M684 668h0c2 0 3-1 4-2 2 1 2 1 3 2l-7 12c-1 2-2 6-4 7l-1-2 2-2c0-2 1-3 2-5 2-3 3-6 4-10-3 2-4 5-7 6h-1l1-1c1-1 2-3 4-5z" class="p"></path><path d="M665 683c1-1 1-2 2-2 1 1 1 1 1 3h0l1-1h0v2h0l1 1-1 1c2 0 2-1 3-1l1 2v-2c1 2 0 4 0 6 0 1 0 2 1 4 0 0 0 1 1 2l-1 5-1 7h1v-1l1 2v1l-6-3c-2-3-5-4-8-6l-3-3h2l-1-1c2-1 1-3 2-5v1c1-1 1-2 2-2 0-2 0-4-1-5v-1l1-1h1c1-1 1-2 1-3z" class="H"></path><path d="M665 683c1-1 1-2 2-2 1 1 1 1 1 3h0l1-1h0v2h0l1 1-1 1c0 3 0 3-1 4h-1l-1-1c1-1 2-3 2-4s-2-2-3-3z" class="AP"></path><path d="M666 697c1 0 1 0 2 1v-1l-1-1c0-2 1-1 2-2v-2h1c0 2 0 2 1 4h0v2l-2 1v1h-1v1h-1l-1-4z" class="O"></path><path d="M663 693v2l1 1h1v1h1l1 4v3h-1c-2-1-3-1-5-1l-3-3h2l-1-1c2-1 1-3 2-5v1c1-1 1-2 2-2z" class="e"></path><path d="M663 693v2 5 1c-1 0-2 0-3-1l-1-1c2-1 1-3 2-5v1c1-1 1-2 2-2z" class="P"></path><path d="M673 688v-2c1 2 0 4 0 6 0 1 0 2 1 4 0 0 0 1 1 2l-1 5-1 7h1v-1l1 2v1l-6-3c-2-3-5-4-8-6 2 0 3 0 5 1h1v-3h1v-1h1v-1l2-1v-2c1-2 1-5 2-8z" class="F"></path><path d="M671 698l-1 6h-1-2v-3h1v-1h1v-1l2-1z" class="n"></path><path d="M714 637c1 1 3 1 4 2l2 2-1 1c-2 0-4-1-6 1 0 1-1 2-2 2h-2 0c-2 0-4 1-6 2-3 1-8 3-10 5-1 0-7 3-7 4l-3 1c-3 1-6 4-7 6l-1 1c0 1-1 2-2 3v1h0 0l3 1h1l-2 3h-1v1c0 2-3 4-3 7h1c0 2-1 4-2 6l-1-1h0v-2h0l-1 1h0c0-2 0-2-1-3-1 0-1 1-2 2 0 1 0 2-1 3h-1c0-2 0-3 1-4 0-2 1-3 1-5s-1-3-1-4v-1c1-2 3-3 4-4l3-4-1-1h-1c-2-1-2-1-4-1h0l-3 2c1-2 1-2 2-3l1-1c1-2 2-2 3-3h0c-1 0-2 1-3 2-2 0-2 1-4 1h0c3-3 7-5 11-8l5-5 1 1v-1l1 1c1 0 2 0 3 1l4-4 3-3c2-2 4-1 7-3 1 0 1-1 2-1 3 0 5 0 7 1 3-1 6 0 9 0v-2z" class="p"></path><path d="M698 641c2 0 3 1 5 1-3 1-6 2-9 4v-1c0-1 0 0-1-1h0l5-3z" class="F"></path><path d="M693 644c1 1 1 0 1 1v1c-2 2-4 3-6 4-1 1-2 2-4 3-3 2-5 5-8 6v-1c1-2 6-7 8-8 3-1 6-4 9-6zm21-7c1 1 3 1 4 2l2 2-1 1c-2 0-4-1-6 1 0 1-1 2-2 2h-2 0c-2 0-4 1-6 2-3 1-8 3-10 5-1-1-2-1-2 0-1 0-1 0-2 1h0-1c0-1 0-1 1-1l1-1 3-2c1-1 2-2 4-2l1-1 1-1c2 0 4-1 5-2v-1h-1c-2 0-3-1-5-1 2-1 5-2 7-2 3-1 6 0 9 0v-2z" class="U"></path><path d="M714 637c1 1 3 1 4 2l2 2-1 1c-2 0-4-1-6 1h-3v-1c2 0 2 0 4-1l-2-1c-2-1-6 1-8 2h-1c-2 0-3-1-5-1 2-1 5-2 7-2 3-1 6 0 9 0v-2z" class="AB"></path><path d="M671 664l3-1v1c-1 1-4 3-4 5 0 1 0 2-1 2l-1 3h1c1-1 4-4 4-6l3 1h1l-2 3h-1v1c0 2-3 4-3 7h1c0 2-1 4-2 6l-1-1h0v-2h0l-1 1h0c0-2 0-2-1-3-1 0-1 1-2 2 0 1 0 2-1 3h-1c0-2 0-3 1-4 0-2 1-3 1-5s-1-3-1-4v-1c1-2 3-3 4-4l3-4h0z" class="F"></path><path d="M668 668v2l-3 6v1c0-2-1-3-1-4v-1c1-2 3-3 4-4z" class="H"></path><path d="M698 638c3 0 5 0 7 1-2 0-5 1-7 2l-5 3h0c-3 2-6 5-9 6-2 1-7 6-8 8v1c-2 2-4 3-5 5h0l-1-1h-1c-2-1-2-1-4-1h0l-3 2c1-2 1-2 2-3l1-1c1-2 2-2 3-3h0c-1 0-2 1-3 2-2 0-2 1-4 1h0c3-3 7-5 11-8l5-5 1 1v-1l1 1c1 0 2 0 3 1l4-4 3-3c2-2 4-1 7-3 1 0 1-1 2-1z" class="Z"></path><path d="M677 647l1 1v-1l1 1c1 0 2 0 3 1-3 2-5 4-8 6-1 1-3 2-3 3h-2c-1 0-4 3-4 4l-3 2c1-2 1-2 2-3l1-1c1-2 2-2 3-3h0c-1 0-2 1-3 2-2 0-2 1-4 1h0c3-3 7-5 11-8l5-5z" class="Q"></path><path d="M661 660h0c2 0 2-1 4-1 1-1 2-2 3-2h0c-1 1-2 1-3 3l-1 1c-1 1-1 1-2 3l3-2h0c2 0 2 0 4 1h1l1 1-3 4c-1 1-3 2-4 4v1c0 1 1 2 1 4s-1 3-1 5c-1 1-1 2-1 4l-1 1v1c1 1 1 3 1 5-1 0-1 1-2 2v-1c-1 2 0 4-2 5l1 1h-2l-6-5c-1 1-2 1-3 0s-1-1-2-1h-1c-4-2-7-3-11-3l-1-1v1l1 1h-1-2l-2 4v-3s1-1 1-2v-1c2-4 4-8 5-12l1-1-2-2c2-1 4-4 6-5h0l-1 3 1 1h0c0-1 1-2 1-3l1 1c1 0 4-2 5-3 0-1 1-1 2-2h0 1c3-2 6-5 10-7z" class="o"></path><path d="M665 662c2 0 2 0 4 1l-2 3c-1-2-1-2-2-3v-1z" class="n"></path><path d="M669 663h1l1 1-3 4c-1 1-3 2-4 4h0-2l-2 3h0-1v-2c2-2 5-5 8-7l2-3z" class="d"></path><path d="M659 673v2h1 0c0 3-1 3-3 5 0 0 0 1-1 2 0 0 0 1-1 1h-1c-1 1-2 2-2 3v2 3h-1l-1-1 1-1-1-1c1-2 1-3 2-4 0-2 1-3 2-4 1-2 3-5 5-7z" class="K"></path><path d="M660 675l2-3h2 0v1c0 1 1 2 1 4s-1 3-1 5c-1 1-1 2-1 4l-1 1v1c1 1 1 3 1 5-1 0-1 1-2 2v-1h-1v-1l-1-2-1-2-1 1c-2-1-2-1-3 0l-2 1v-3-2c0-1 1-2 2-3h1c1 0 1-1 1-1 1-1 1-2 1-2 2-2 3-2 3-5z" class="e"></path><path d="M660 688c1 2 1 3 1 5h-1l-1-2 1-3z" class="O"></path><path d="M660 675l2-3h2 0v1c-1 2-2 2-2 4 0 1-1 2-1 3h-1-3c2-2 3-2 3-5z" class="P"></path><path d="M652 691v-3-2c0-1 1-2 2-3h1c1 0 1-1 1-1 1-1 1-2 1-2h3 1v3c0 1 0 2-1 3v2l-1 3-1-2-1 1c-2-1-2-1-3 0l-2 1z" class="H"></path><path d="M658 689c0-2 0-3 1-5l1 2v2l-1 3-1-2z" class="o"></path><path d="M660 680h1v3c0 1 0 2-1 3l-1-2c1-1 1-3 1-4zm-13 3c2 0 2 0 3 1l-1 3 1 1-1 1c0 1 0 1 1 1l1 1h1l2-1c1-1 1-1 3 0l1-1 1 2 1 2v1h1c-1 2 0 4-2 5l1 1h-2l-6-5c-1 1-2 1-3 0s-1-1-2-1h-1c-2-3-3-6-4-9v-2h0 3c0 1 0 2 2 3h0v-3z" class="n"></path><path d="M642 683h0l2 3c3 3 5 7 8 9-1 1-2 1-3 0s-1-1-2-1h-1c-2-3-3-6-4-9v-2z" class="AJ"></path><path d="M657 690l1-1 1 2 1 2v1h1c-1 2 0 4-2 5h-1l-4-4v-2c1-1 0-2 0-3 1-1 1-1 3 0z" class="K"></path><path d="M657 690l1-1 1 2 1 2v1h-1c-2-1-2-2-2-4z" class="P"></path><path d="M661 660h0c2 0 2-1 4-1 1-1 2-2 3-2h0c-1 1-2 1-3 3l-1 1c-1 1-1 1-2 3h-1c-1 2-1 3-3 4v1c-1 3-5 6-7 9v1l-4 4v3h0c-2-1-2-2-2-3h-3 0v-1c0-1-1-1-1-2 0-4 8-10 10-13 3-2 6-5 10-7z" class="m"></path><path d="M642 682h3c2-1 3-3 4-5 0-1 1-2 1-3 1 0 1-1 2-1 0-1 0-2 1-2 3-2 5-5 8-7-1 2-1 3-3 4v1c-1 3-5 6-7 9v1l-4 4v3h0c-2-1-2-2-2-3h-3 0v-1z" class="P"></path><path d="M641 670h0l-1 3 1 1h0c0-1 1-2 1-3l1 1c1 0 4-2 5-3 0-1 1-1 2-2h0 1c-2 3-10 9-10 13 0 1 1 1 1 2v1 2c1 3 2 6 4 9-4-2-7-3-11-3l-1-1v1l1 1h-1-2l-2 4v-3s1-1 1-2v-1c2-4 4-8 5-12l1-1-2-2c2-1 4-4 6-5z" class="o"></path><path d="M634 690h-1v-1l4-11c2 1 2 3 3 4-1 1-1 1-2 3h0c-1 3-1 4-3 6l-1-1z" class="J"></path><path d="M640 682l2 3c1 3 2 6 4 9-4-2-7-3-11-3 2-2 2-3 3-6h0c1-2 1-2 2-3z" class="N"></path><path d="M441 791c1-1 2-1 3-1h2c1 0 3 1 4 2l2 3c2-1 2-1 3-1l1 1c0 1 1 2 2 3l1 2 7 15c2 2 3 4 5 6v1c0 1 1 3 1 4l4 13 18 54c3 11 8 22 10 33 1 4 1 9 1 14 1 10 2 20 1 30v1-1l1-1v5c0 1-1 3-1 5-1-1-2-5-2-6l-12-34-42-123c-3-6-5-13-8-20 0-1-1-4-1-5z" class="l"></path><path d="M441 791c1-1 2-1 3-1h2c1 0 3 1 4 2l2 3 2 2-1 1-3-4c-1 0-2 1-4 1-1 0-2 0-3 1 1 1 1 2 1 3 2 5 6 13 6 17-3-6-5-13-8-20 0-1-1-4-1-5z" class="j"></path><path d="M452 795c2-1 2-1 3-1l1 1c0 1 1 2 2 3l1 2 7 15c-1-1-2-1-2-2v-1h0v-1c-2 0-3 0-4 1l1 1v1h-1c-2-3-1-6-3-8v-1c0-2-1-3-1-4l-3-3 1-1-2-2z" class="r"></path><path d="M452 795c2-1 2-1 3-1l1 1c0 1 1 2 2 3l1 2c-1 0 0 0-1 1h-1c-1-1-2-2-3-4l-2-2z" class="AE"></path><defs><linearGradient id="c" x1="748.051" y1="543.169" x2="758.173" y2="561.257" xlink:href="#B"><stop offset="0" stop-color="#0b0202"></stop><stop offset="1" stop-color="#441311"></stop></linearGradient></defs><path fill="url(#c)" d="M739 509h0v1c1 1 0 1 1 1 0 2 0 5 1 6 1 3 2 6 6 7 2 2 5 2 7 3l7 2h1c3 1 4 2 7 1 4 1 7 3 11 3v1c1 0 1 0 2 1v1l16 4c-1 1-3 2-4 2-5 1-10 0-14 1-2 1-3 0-4 1l-3 1h-1c-1 0-3 1-4 2h1c-1 1-8 4-9 4l-9 6c-2 1-4 4-6 6-1 2-3 4-4 6l-4 6h-2c-1 1-2 1-3 1v-4c-1 1-1 3-2 4-2 2-2 9-4 11h0c-1 1-2 1-2 2h-2c0 2 0 4 1 6v8l1 3c1 4 0 9 0 13 1 5 2 8 3 13v1 1l-3-1 1 1c0 1 1 3 1 4l6 4-1 2-3-1c-3-1-6-2-9-4 0-1 0-1-1-1v-1c-1 0-2-1-3-1-5-2-7-6-10-10l1-1 5 6 1-1c1-2 0-4 0-6 1-3 0-7 0-10v-6c1-1 1-2 1-4v-2c1-2 1-2 1-3l-1-1c0-3 0-8 1-10v-2l-1-2 1-1c0-2-1-3-1-4l2-8c1-2 1-3 2-4h0c0-2 0-3 1-5v-4c1-1 2-1 2-2l5-9v-1l3-4h0c-2 0-2-1-3-2l2-2v-2c2-2 5-4 7-5l6-2h-1c-1 0-3 0-4-1h-2c-2 0-3 0-3-1l-2-1c-1 0-2 1-2 2-1 0-2-1-3-1h-2v-1h1c1 0 1-1 2-1l2-1h1v1c2-1 4-2 6-4l-1-2 2 1h1 1c1 1 1 1 2 1v-1c0-1-2-3-2-3l1-2c0-1 0-2-1-3h1l2-2v-1z"></path><path d="M735 558c2-2 3-2 7-3v2c-1 0-1 0-2 1s-2 1-3 2c-1-1-2-1-2-2z" class="W"></path><path d="M745 552l1-1c1-2 3-3 6-3 1 1 2 0 3 0l1 1c-2 1-4 0-5 1-2 1-4 2-6 2z" class="N"></path><path d="M763 533c2 0 2 0 3 1h1c2 0 3 0 4 1-3 0-7 1-11 1-1 0-3 0-5-1 3-1 5-1 8-2z" class="G"></path><path d="M755 535c2 1 4 1 5 1-7 3-12 6-18 10-1-1-1-1-1-2l14-9h0z" class="I"></path><path d="M719 592c2-4 2-8 3-12v2l1-1c1-1 1-3 2-4v-1h1c-1 2-2 5-3 7v1 3c-1 1-1 1-1 2 0 2 0 4 1 6v8l1 3c1 4 0 9 0 13 1 5 2 8 3 13v1 1l-3-1 1 1c0 1 1 3 1 4-2-2-3-3-5-6v-7c0-2-1-3-2-5h0c1-1 1-2 1-2 1-2-1-9-1-10-1-2-1-4-1-5 1-2 1-4 1-6v-5z" class="K"></path><path d="M721 625l3 8 1 1c0 1 1 3 1 4-2-2-3-3-5-6v-7z" class="q"></path><path d="M719 592c2-4 2-8 3-12v2l1-1c1-1 1-3 2-4v-1h1c-1 2-2 5-3 7v1 3c-1 1-1 1-1 2 0 2 0 4 1 6v8l-1 1v-1c1-2 0-4 0-6-1 0-1 1-2 1 0 2 1 4 1 6v-1c-1-2-2-4-1-6 1 0 1 0 2-1h0l-2-1-1-3z" class="a"></path><path d="M725 562c4-7 9-13 16-18 0 1 0 1 1 2-16 13-23 30-25 51l-1 10h0-1v-4l1-9-2-6v-2l-1-2 1-1c0-2-1-3-1-4l2-8c1 1 1 3 2 5 0-1 1-3 2-5h0c0-3 1-5 3-6l1-1 2-2z" class="G"></path><path d="M713 579l2-8c1 1 1 3 2 5h-1c0 3 0 5 2 7l-2 11-2-6v-2l-1-2 1-1c0-2-1-3-1-4z" class="AL"></path><path d="M713 579l2-8c1 1 1 3 2 5h-1c-1 2-1 4-2 6 1 2 1 2 0 4l-1-2 1-1c0-2-1-3-1-4z" class="p"></path><path d="M717 576c0-1 1-3 2-5h0c0-3 1-5 3-6l1-1 2-2c-2 7-6 14-7 21-2-2-2-4-2-7h1z" class="v"></path><defs><linearGradient id="d" x1="726.502" y1="549.836" x2="734.311" y2="560.707" xlink:href="#B"><stop offset="0" stop-color="#dd4341"></stop><stop offset="1" stop-color="#ea6461"></stop></linearGradient></defs><path fill="url(#d)" d="M737 542c1-1 2-2 3-2 5-3 9-4 15-5l-14 9c-7 5-12 11-16 18l-2 2-1 1c-2 1-3 3-3 6h0c-1 2-2 4-2 5-1-2-1-4-2-5 1-2 1-3 2-4h0c0-2 0-3 1-5v-4c1-1 2-1 2-2l5-9h0c1 0 2 1 2 2h1l1-1c0-1 2-2 2-4l2-1 4-1z"></path><path d="M733 543l4-1c-2 3-3 4-5 6h0-3c0-1 2-2 2-4l2-1zm-8 4h0c1 0 2 1 2 2h1c0 1-1 2-2 3s0 1-1 2c-1 2-3 3-4 4s-1 2-2 3c0 1 0 1-1 1v-4c1-1 2-1 2-2l5-9z" class="w"></path><path d="M712 614v-6c1-1 1-2 1-4v-2c1-2 1-2 1-3l-1-1c0-3 0-8 1-10l2 6-1 9v4h1 0c1 1 1 3 1 5 1 3 1 6 2 8s2 3 2 5v7c2 3 3 4 5 6l6 4-1 2-3-1c-3-1-6-2-9-4 0-1 0-1-1-1v-1c-1 0-2-1-3-1-5-2-7-6-10-10l1-1 5 6 1-1c1-2 0-4 0-6 1-3 0-7 0-10z" class="p"></path><path d="M715 619c0 4 1 8 2 12l1 1c0 1 1 2 2 3s1 1 1 2l-6-4v-3-4s-1-1-1-2c-1-2-1-3 1-5z" class="v"></path><path d="M715 603v4h1 0c1 1 1 3 1 5 1 3 1 6 2 8s2 3 2 5v7c-1-1-2-3-2-5-1 2-1 3-2 4-1-4-2-8-2-12-1-5-1-11 0-16z" class="AN"></path><path d="M717 612c1 3 1 6 2 8s2 3 2 5v7c-1-1-2-3-2-5l-2-8c0-2-1-3-1-4s1-2 1-3z" class="AI"></path><path d="M705 626l1-1 5 6 4 2 6 4c0-1 0-1-1-2s-2-2-2-3l-1-1c1-1 1-2 2-4 0 2 1 4 2 5 2 3 3 4 5 6l6 4-1 2-3-1c-3-1-6-2-9-4 0-1 0-1-1-1v-1c-1 0-2-1-3-1-5-2-7-6-10-10z" class="I"></path><path d="M745 552c2 0 4-1 6-2 1-1 3 0 5-1v2h1 0 3l-9 6c-2 1-4 4-6 6-1 2-3 4-4 6l-4 6h-2c-1 1-2 1-3 1v-4c-1 1-1 3-2 4-2 2-2 9-4 11h0c-1 1-2 1-2 2h-2c0-1 0-1 1-2v-3-1c1-2 2-5 3-7l5-14 4-4c0 1 1 1 2 2 1-1 2-1 3-2s1-1 2-1v-2h0l3-3z" class="K"></path><path d="M735 558c0 1 1 1 2 2l-2 2-2-1-2 1 4-4z" class="a"></path><path d="M742 557l1 1 1-2v1c0 1-1 2-2 3-4 1-6 9-10 11v1c-1 1-1 3-2 4 0-3 1-5 2-7h1c1-3 2-5 5-7 1-1 1-2 2-4 1-1 1-1 2-1z" class="O"></path><path d="M747 557h0c0 1-1 2-1 3h0l-3 2c1 0 1 1 2 1-1 2-3 4-4 6l-4 6h-2c-1 1-2 1-3 1v-4-1c4-2 6-10 10-11l1 1c1-1 2-3 4-4z" class="P"></path><path d="M745 552c2 0 4-1 6-2 1-1 3 0 5-1v2h1 0 3l-9 6c-2 1-4 4-6 6-1 0-1-1-2-1l3-2h0c0-1 1-2 1-3h0c-2 1-3 3-4 4l-1-1c1-1 2-2 2-3v-1l-1 2-1-1v-2h0l3-3z" class="R"></path><path d="M742 555c1 0 2 1 3 0 2-2 3-3 5-3v1l-3 4h0c-2 1-3 3-4 4l-1-1c1-1 2-2 2-3v-1l-1 2-1-1v-2h0z" class="a"></path><path d="M739 509h0v1c1 1 0 1 1 1 0 2 0 5 1 6 1 3 2 6 6 7 2 2 5 2 7 3l7 2h1c3 1 4 2 7 1 4 1 7 3 11 3v1c1 0 1 0 2 1v1l-11-1c-1-1-2-1-4-1h-1c-1-1-1-1-3-1-3 1-5 1-8 2h0c-6 1-10 2-15 5-1 0-2 1-3 2l-4 1-2 1c0 2-2 3-2 4l-1 1h-1c0-1-1-2-2-2h0v-1l3-4h0c-2 0-2-1-3-2l2-2v-2c2-2 5-4 7-5l6-2h-1c-1 0-3 0-4-1h-2c-2 0-3 0-3-1l-2-1c-1 0-2 1-2 2-1 0-2-1-3-1h-2v-1h1c1 0 1-1 2-1l2-1h1v1c2-1 4-2 6-4l-1-2 2 1h1 1c1 1 1 1 2 1v-1c0-1-2-3-2-3l1-2c0-1 0-2-1-3h1l2-2v-1z" class="c"></path><path d="M739 510c1 3 0 7 1 10s3 4 5 6c-3-1-8-3-11-6h1 1c1 1 1 1 2 1v-1c0-1-2-3-2-3l1-2c0-1 0-2-1-3h1l2-2z" class="H"></path><path d="M733 521l9 6 2 1c-1 1-2 1-4 1h-1c-1 0-3 0-4-1h-2c-2 0-3 0-3-1l-2-1c-1 0-2 1-2 2-1 0-2-1-3-1h-2v-1h1c1 0 1-1 2-1l2-1h1v1c2-1 4-2 6-4z" class="T"></path><path d="M730 527l2-3c1 0 1 1 2 1h1c2 1 3 1 4 3v1c-1 0-3 0-4-1h-2c-2 0-3 0-3-1z" class="B"></path><path d="M732 534l1-1c7-4 16-3 23-1l7 1c-3 1-5 1-8 2h0c-6 1-10 2-15 5-1 0-2 1-3 2l-4 1-2 1c0 2-2 3-2 4l-1 1h-1c0-1-1-2-2-2h0v-1l3-4h0c-2 0-2-1-3-2l2-2c2-2 4-3 5-4z" class="N"></path><path d="M727 538c2-2 4-3 5-4l-1 4c1 0 2 0 3-1l-2 2-3 3h-1 0c-2 0-2-1-3-2l2-2z" class="W"></path><path d="M734 537c2-1 5-2 7-2 3-1 5-1 8-1-1 1-3 1-4 2-1 0-2 0-3 1 0 1-1 1-2 1-2 0-3 1-4 2l-1 1c-1-1-1-2-3-2l2-2z" class="E"></path><path d="M732 539c2 0 2 1 3 2l-2 2-2 1c0 2-2 3-2 4l-1 1h-1c0-1-1-2-2-2h0v-1l3-4h1l3-3z" class="F"></path><path d="M732 539c2 0 2 1 3 2l-2 2-2 1v-1l1-2v-1l-2 1-1 1 3-3z" class="AP"></path><path d="M756 532l7 1c-3 1-5 1-8 2h0c-6 1-10 2-15 5-1 0-2 1-3 2l-4 1 2-2 1-1c1-1 2-2 4-2 1 0 2 0 2-1 1-1 2-1 3-1 1-1 3-1 4-2 3 0 5 0 7-2z" class="F"></path><path d="M252 534l2 1c0-1 0-1 2-1h2c1-1 1-1 3-1v1c-2 1-6 1-8 3 1 0 3 1 4 1h3c4 0 8 0 11-1h2c2 1 3 1 4 3 4 0 4 3 7 4l4 2v2h1c1 1 1 2 1 3l1 1 1 1h-1c0 2 1 3 1 4h0c1 2 2 3 2 5l1 1c0 1 0 2 1 3 1 0 3 1 4 0v-4c0 2 1 4 2 6 0-2 0-2 1-3l2 6 1 5h0l1 2v2l1 2 2 5v1c1 2 1 3 2 5s2 4 2 7l4 11c2 1 2 2 3 4h1v1c-1 1 0 2 0 3 1 1 2 1 3 1l1 2-1 1c-1 0-1 1-2 1l-1 1-1 1h0c-3 4-7 6-10 10l-5 7c-4 3-7 6-11 8h-1-1-1l1-1s1 0 1-1c1-1 1 0 1-1h2c0-1 1-1 1-2l-1-1-2 1h-1l2-2h-1v-5c-1 2-2 3-4 3v-1l-1-1c1 0 1-1 1-2h0-1l-6 6v-2c0-3 1-5 0-8l1-3h1c0-2 0-4 1-6l1-7v-3-2l-2-11v-2c0-1-1-3-1-4 2-1 3-1 5 0l-2-4-1-3-2-1c0-1-1-2-1-3l-3 2-2-4c0-1 0-1-1-2l-2-4c-1-2-3-4-5-5l-4-5h2v-1l-2-2c0-1-1-2-1-3h-1l1-1c1-1 1-3 0-4v-2l2-1 1 1c1 0 3 2 4 2v-3c-6-6-12-10-20-13v-1c-1-1-3-2-4-2v-1l2-1 1-1z" class="AD"></path><path d="M294 619c1 0 2 0 3 1h2c-1 1-1 2-2 3-2-1-2-3-3-3v-1z" class="AS"></path><path d="M299 598h1v1c0 1 0 2 2 3h0l1 5h-2l-2-9z" class="F"></path><path d="M275 556c2 1 4 4 5 6 0 1-1 1-2 1l-3-3c1 0 1 0 1-1l-1-2v-1z" class="a"></path><path d="M300 613c0 2 1 6 0 8l-1-1h-2c-1-1-2-1-3-1 1-1 1-2 2-2h1c1-2 2-3 3-4z" class="AV"></path><path d="M294 583l2-2c1 0 2 1 2 2l2 6c-1 1-2 1-3 2l-3-8z" class="F"></path><path d="M291 635c1-2 3-4 4-6v-1c2 0 2-1 3-2h1c0 2-1 3-2 5s-2 3-3 5v1l-3 4-1-1c1 0 1-1 1-2h0-1l1-3z" class="AM"></path><path d="M271 546h2c1 2 3 3 4 5 2 3 3 5 5 7 1 2 2 4 3 5h-1-1c-1 0-3-3-4-4-2-3-5-6-8-9v-4z" class="O"></path><path d="M252 534l2 1c0-1 0-1 2-1h2c1-1 1-1 3-1v1c-2 1-6 1-8 3 1 0 3 1 4 1 4 1 8 2 10 5l4 3v4c-2-3-5-5-9-7l-9-4c-1-1-3-2-4-2v-1l2-1 1-1z" class="E"></path><path d="M301 607h2c2 5 1 11 0 15v3c0 2-1 4-1 6-1 2-2 2-2 5 0 2-1 3-2 5l-2 3h-1v-5c1-1 1-2 2-3 0-1 1-1 2-2v-1c1-1 1-1 1-2 2-9 2-16 1-24z" class="q"></path><path d="M285 570c3 5 7 11 9 17l3 9c-1 1-2 2-3 2l-1-1c0-2 0-2 1-4h0c-1-1-3-1-4-2v-1c0-3-3-7-4-10-1-2-2-4-3-7l-1-2v-1h2 1z" class="F"></path><path d="M294 593c0 1 1 1 1 2s0 0 1 1l-1-1c0-1 0-2-1-3v-2c-1-1-1-2-1-3h1l3 9c-1 1-2 2-3 2l-1-1c0-2 0-2 1-4z" class="AI"></path><path d="M297 591c1-1 2-1 3-2 1 7 4 12 5 19l1-1h1 0c0 2 1 4 1 6l4 5v4l-3-2v3c0 1-1 3-2 4 0 2 0 4-2 5 1-2 1-5 1-7l-1-1-2 1v-3c1-4 2-10 0-15l-1-5h0c-2-1-2-2-2-3v-1h-1c-1-2-1-5-2-7z" class="U"></path><path d="M302 602c1 1 1 2 1 3l1 1c0 2 1 4 1 6 1 4 1 9 1 13l-1-1-2 1v-3c1-4 2-10 0-15l-1-5z" class="Z"></path><path d="M305 608l1-1h1 0c0 2 1 4 1 6l4 5v4l-3-2v3c0 1-1 3-2 4 0-6 0-13-2-19z" class="O"></path><path d="M308 613l4 5v4l-3-2c-1-2-1-5-1-7z" class="n"></path><path d="M273 553c0 1 2 2 2 3v1l1 2c0 1 0 1-1 1l3 3c1 0 2 0 2-1l4 7 1 1h-1-2v1l1 2c1 3 2 5 3 7 1 3 4 7 4 10l-2 2-1-3-2-1c0-1-1-2-1-3l-3 2-2-4c0-1 0-1-1-2l-2-4c-1-2-3-4-5-5l-4-5h2v-1l-2-2c0-1-1-2-1-3h-1l1-1c1-1 1-3 0-4v-2l2-1 1 1c1 0 3 2 4 2v-3z" class="k"></path><path d="M267 567h2v-1l-2-2c0-1-1-2-1-3h-1l1-1c1 1 2 2 3 2l1 1 1 3h0-1c0 1 1 2 1 3 1 0 0 2 0 3l-4-5z" class="a"></path><path d="M275 571l-1-1c-1-1-1-1 0-2h2l1-1c0-1-1-2 0-2 1 1 3 2 4 4h3l1 1h-1-2v1l1 2c-1 1-1 1-2 1v1s-1 0-1-1c-2-2-3-2-5-3z" class="AX"></path><path d="M281 574l-2-2v-1l2-1 1 1 1 2c-1 1-1 1-2 1z" class="q"></path><path d="M273 553c0 1 2 2 2 3v1l1 2c0 1 0 1-1 1l-2-2h-2c0 1 0 1 1 2v1l1 2v2l-3-2-1-1c-1 0-2-1-3-2 1-1 1-3 0-4v-2l2-1 1 1c1 0 3 2 4 2v-3z" class="R"></path><path d="M268 553l1 1-1 1v1c1 1 1 1 1 2l1 3c0 1 0 1-1 1s-2-1-3-2c1-1 1-3 0-4v-2l2-1z" class="b"></path><path d="M276 577l1-1-6-6c2 1 3 2 4 3h1l-1-2c2 1 3 1 5 3 0 1 1 1 1 1v-1c1 0 1 0 2-1 1 3 2 5 3 7 1 3 4 7 4 10l-2 2-1-3-2-1c0-1-1-2-1-3l-3 2-2-4c0-1 0-1-1-2l-2-4z" class="v"></path><path d="M284 585l-1-1c0-2-1-3-3-5l-1-1 1-1c2 1 4 5 6 7 1 2 1 3 1 4v1l-2-1c0-1-1-2-1-3z" class="AH"></path><path d="M290 590v1c1 1 3 1 4 2h0c-1 2-1 2-1 4l1 1c1 0 2-1 3-2l1 7 2 10c-1 1-2 2-3 4h-1c-1 0-1 1-2 2v1c-1 4-3 11-3 15l-1 3-6 6v-2c0-3 1-5 0-8l1-3h1c0-2 0-4 1-6l1-7v-3-2l-2-11v-2c0-1-1-3-1-4 2-1 3-1 5 0l-2-4 2-2z" class="AL"></path><path d="M288 615c1-1 2-1 2-2 1 2 0 2 0 3-1 2 0 5-1 7v6c-1 4 0 7-2 10h-1l-2 3c0-3 1-5 0-8l1-3h1c0-2 0-4 1-6l1-7v-3z" class="w"></path><path d="M286 631l1 1h0c-1 3-1 5-1 7l-2 3c0-3 1-5 0-8l1-3h1z" class="U"></path><path d="M290 590v1c1 1 3 1 4 2h0c-1 2-1 2-1 4l1 1c1 0 2-1 3-2l1 7c-2 3-4 5-4 9-1 1 0 3-1 5l-1-1c0-2 0-3-1-5-1-3-3-6-5-9v-2c0-1-1-3-1-4 2-1 3-1 5 0l-2-4 2-2z" class="p"></path><path d="M285 596c2-1 3-1 5 0 1 1 1 3 2 5l-1 2c1 1 1 2 0 3l-1-1h0c0-2-1-3-1-4l-3-1c0-1-1-3-1-4z" class="U"></path><path d="M260 538c4 0 8 0 11-1h2c2 1 3 1 4 3 4 0 4 3 7 4l4 2v2h1c1 1 1 2 1 3l1 1 1 1h-1c0 2 1 3 1 4h0v5 2h0l1 1v1h-1l1 3c0 1 1 1 1 2l-2-1-1 1c2 2 3 5 2 7 0 1 0 1-1 1l-9-16h1 1c-1-1-2-3-3-5-2-2-3-4-5-7-1-2-3-3-4-5h-2l-4-3c-2-3-6-4-10-5h3z" class="K"></path><path d="M286 554l6 12 1 3c0 1 1 1 1 2l-2-1-1 1c2 2 3 5 2 7 0 1 0 1-1 1l-9-16h1 1l5 8-1-5c-1-1-1-2-1-3v-2l-1-1-2-4 1-2z" class="e"></path><path d="M277 540c4 0 4 3 7 4l4 2v2h1c1 1 1 2 1 3l1 1 1 1h-1c0 2 1 3 1 4h0v5 2h0l1 1v1h-1l-6-12c0-2-2-3-3-5-1-3-3-6-6-8v-1z" class="W"></path><path d="M260 538c4 0 8 0 11-1h2c2 1 3 1 4 3v1 1c0 1 2 3 3 4 1 2 2 3 2 5v1c1 1 1 1 1 2 0 0 0 1 1 1h-1c-2-1-3-3-6-4-1-2-3-3-4-5h-2l-4-3c-2-3-6-4-10-5h3z" class="a"></path><path d="M267 543c2 0 3 0 5 1l1 2h-2l-4-3z" class="K"></path><path d="M260 538c4 0 8 0 11-1h2v1c1 1 1 0 1 1v1 1c-1 1 0 1-1 1l-3-3h0-4c-2-1-4-1-6-1z" class="h"></path><path d="M292 557c1 2 2 3 2 5l1 1c0 1 0 2 1 3 1 0 3 1 4 0v-4c0 2 1 4 2 6 0-2 0-2 1-3l2 6 1 5h0l1 2v2l1 2 2 5v1c1 2 1 3 2 5s2 4 2 7l4 11c2 1 2 2 3 4h1v1c-1 1 0 2 0 3 1 1 2 1 3 1l1 2-1 1c-1 0-1 1-2 1l-1 1-1 1h0c-3 4-7 6-10 10l-5 7c-4 3-7 6-11 8h-1-1-1l1-1s1 0 1-1c1-1 1 0 1-1h2c0-1 1-1 1-2l-1-1-2 1h-1l2-2 2-3c1-2 2-3 2-5 0-3 1-3 2-5 0-2 1-4 1-6l2-1 1 1c0 2 0 5-1 7 2-1 2-3 2-5 1-1 2-3 2-4v-3l3 2v-4l-4-5c0-2-1-4-1-6h0-1l-1 1c-1-7-4-12-5-19l-2-6c0-1-1-2-2-2l-2 2-2-4c1 0 1 0 1-1 1-2 0-5-2-7l1-1 2 1c0-1-1-1-1-2l-1-3h1v-1l-1-1h0v-2-5z" class="v"></path><path d="M309 620l3 2c0 7-2 12-7 16v1 2h-3v-1c2-3 2-6 3-8 2-1 2-3 2-5 1-1 2-3 2-4v-3z" class="Z"></path><path d="M303 625l2-1 1 1c0 2 0 5-1 7s-1 5-3 8v1c-1 1-1 2-2 3v2l-6 5h-1-1l1-1s1 0 1-1c1-1 1 0 1-1h2c0-1 1-1 1-2l-1-1-2 1h-1l2-2 2-3c1-2 2-3 2-5 0-3 1-3 2-5 0-2 1-4 1-6z" class="d"></path><path d="M309 597c1 2 2 3 4 5v-1c0-2-1-5-2-7l1-1c1 2 2 4 2 7l4 11c-1 0-2 1-3 2 1 3 2 9 1 12l-1-1v-3h-2v-7l-4-17z" class="n"></path><path d="M313 614c2 2 2 3 2 7h0-2v-7z" class="E"></path><path d="M318 611c2 1 2 2 3 4h1v1c-1 1 0 2 0 3 1 1 2 1 3 1l1 2-1 1c-1 0-1 1-2 1l-1 1-1 1h0s-1 0-2 1h-1c-2 2-3 3-5 4v-10h2v3l1 1c1-3 0-9-1-12 1-1 2-2 3-2z" class="H"></path><path d="M296 566c1 0 3 1 4 0v-4c0 2 1 4 2 6 0-2 0-2 1-3l2 6 1 5h0l1 2v2l1 2 2 5v1c1 2 1 3 2 5l-1 1c1 2 2 5 2 7v1c-2-2-3-3-4-5h0c-2-5-4-10-6-16-1-4-3-9-5-12 0-1-1-1-1-2l-1-1z" class="k"></path><defs><linearGradient id="e" x1="294.058" y1="589.562" x2="309.483" y2="588.936" xlink:href="#B"><stop offset="0" stop-color="#640204"></stop><stop offset="1" stop-color="#8d1d1b"></stop></linearGradient></defs><path fill="url(#e)" d="M292 557c1 2 2 3 2 5l1 1c0 1 0 2 1 3l1 1c6 16 13 34 15 51l-4-5c0-2-1-4-1-6h0-1l-1 1c-1-7-4-12-5-19l-2-6c0-1-1-2-2-2l-2 2-2-4c1 0 1 0 1-1 1-2 0-5-2-7l1-1 2 1c0-1-1-1-1-2l-1-3h1v-1l-1-1h0v-2-5z"></path><path d="M292 579c1 0 1 0 1-1 1-2 0-5-2-7l1-1 2 1c1 4 4 8 4 12 0-1-1-2-2-2l-2 2-2-4z" class="S"></path><path d="M363 695l1-1h1 2 2l4 1c0 1 0 2-1 3l1 1c2 1 3 3 5 4h1 1l2-1c2 1 3 2 4 3 1 0 3 1 4 1l1 1v-1c2 2 4 3 6 5 2 3 4 4 7 5l7 3 3 1 1 3 3 8 2 7 7 16 1 2c-1 2-1 4 0 6l2 2c2 3 3 6 4 9l-2-2c0-1-1-2-1-3-1-1-2-1-3-2h-1v2c-1-2-3-5-4-8 0-2 0-4-1-6-2-3-4-4-8-5h0c-1 0-2 0-3 2 0 0-1 1-1 2-1 1-2 2-2 4h-1c-2-1-2-1-2-3h0l-1 1c-1 1-1 2-2 4-2 5-6 12-9 17v-4l-1-2v-1h-1c0 3 1 6 0 8l-2 8-1 6h0l-2-2h0 0c0 2 0 3-1 5v-2c-1-4 0-8 0-11-1-3 0-6 0-9-1-2-1-3-1-5l-1-1c1-1 1-1 0-2v-2h0c-1-1 0-1-1-2v-1h0v-1c-1-2-1 0-1-2v-1h-1c0-2 1 0 0-2 0-1 0-2-1-3v-1h-1v-1h0l-1-2v-1c-1-1-1-1-1-2l-1-2v-1c-1-1-2-1-2-3-1 0 0 0-1-1v-1c-1-1-5-4-6-5l-3-2c-1-1-2-1-3-1h-1l-1-1h-1-1-2c-1-1-2-1-2-1h-1c-3-1-7 0-10 0h-1-2l-1 1h-1-1 0-1c-1 1-2 1-2 1-1 0-1 1-2 1h0-2v1h-1c-2 1-3 1-4 2h-1v1l-2 1-1 1h-1c-1 1-1 2-2 2-3 2-6 6-9 8l-1 1c-1 1 1 0-1 1 0 1-1 1-1 2s-1 1-1 2c-1 1-2 1-2 2-1 1 0 1-1 2h0v1c-1 0-1 1-1 2l-3 4v1c-1 1-1 1-1 3-1 0 0 1-1 2v1c-1 1 0 0 0 2h-1v2h-1c-2 8-2 16-2 24v5c-1-2-2-6-2-8-4-12 0-29 6-41l2-5c1 1 1 1 2 1v-1h1c0-1 1-2 2-3v1h2c2-1 4-5 6-6 2-2 3-3 5-4l1-2c1-1 2-3 3-4l-1 1-1-1c1-3 3-4 5-6v-1c1-1 3-2 4-3l3-1c5-3 10-5 16-6l-1-1c-2 1-4 1-6 2l-3-3c1-1 2-2 4-3l2-2s1 0 2-1 2-1 3-2h1l1-1c1 0 3-1 4-1s2-1 3-2c1 0 2-1 3-1s2-1 2-2l2-1c1 0 1 0 2 1z" class="B"></path><path d="M345 722l1-2 1-1c1 1 1 3 2 3 1 1 2 0 2 2-1 0-3 0-5-1l-1-1z" class="W"></path><path d="M384 765l2 1c1 4 2 8 2 12h-2v5l-2-18z" class="v"></path><path d="M335 725v-1h1l2-2c1-1 2-1 3-2 2 0 3 1 4 2l1 1c-4 1-8 2-11 2z" class="Q"></path><path d="M386 783v-5h2c0 2 0 5 1 7l-1 6h0l-2-2h0 0c0 2 0 3-1 5v-2c0-3 0-6 1-9z" class="AQ"></path><path d="M382 754c-3-3-4-9-6-12 0-1-1-3-2-5l-1-2-3-3v-2s-1-1-2-1-1-1-2-1c0-1 0-1-1-2-1 0-1 0-2-1h0l-2-1 1-1c2 1 3 2 5 3 3 2 5 6 9 7 2 0 4 3 5 4l2 5-4-1-1-3h-1c1 2 1 3 2 4 2 3 2 8 3 12z" class="x"></path><path d="M382 754c-1-4-1-9-3-12-1-1-1-2-2-4h1l1 3 4 1s1 2 1 3c1 0 1 1 1 1h1v-1h0c-1-2 0-5 0-6l1-1 2 8c-1 2-2 4-2 6h0l-1-1h-1c1 2 1 4 2 6v4l-1-1h0v6l-2-1-2-11z" class="AA"></path><path d="M382 754c-1-4-1-9-3-12-1-1-1-2-2-4h1l1 3c3 6 6 12 7 19v6l-2-1-2-11z" class="p"></path><path d="M389 746l2 31-2 8c-1-2-1-5-1-7 0-4-1-8-2-12v-6h0l1 1v-4c-1-2-1-4-2-6h1l1 1h0c0-2 1-4 2-6z" class="U"></path><defs><linearGradient id="f" x1="337.127" y1="714.435" x2="330.888" y2="730.46" xlink:href="#B"><stop offset="0" stop-color="#3a0203"></stop><stop offset="1" stop-color="#5d0f0e"></stop></linearGradient></defs><path fill="url(#f)" d="M328 722c6-3 12-6 18-5 1 1 1 1 1 2l-1 1-1 2c-1-1-2-2-4-2-1 1-2 1-3 2l-2 2h-1v1h-1-1c-3 1-6 2-8 3s-4 3-6 3c-2 1-2 2-4 2 1-1 2-3 3-4l-1 1-1-1c1-3 3-4 5-6v-1c1-1 3-2 4-3l3-1-2 2 1 1 1 1z"></path><path d="M325 719l3-1-2 2 1 1 1 1-4 2-6 5-1 1-1-1c1-3 3-4 5-6v-1c1-1 3-2 4-3z" class="h"></path><path d="M325 719l3-1-2 2 1 1 1 1-4 2-1-1c1-1 2-2 2-4z" class="N"></path><path d="M315 733c2 0 2-1 4-2 2 0 4-2 6-3s5-2 8-3h1c-15 5-28 17-36 30-2 4-6 12-6 17-2 8-2 16-2 24v5c-1-2-2-6-2-8-4-12 0-29 6-41l2-5c1 1 1 1 2 1v-1h1c0-1 1-2 2-3v1h2c2-1 4-5 6-6 2-2 3-3 5-4l1-2z" class="S"></path><path d="M296 747c1 1 1 1 2 1v-1h1l-3 6-2-1 2-5z" class="AO"></path><path d="M288 793c-4-12 0-29 6-41l2 1c-6 13-10 26-8 40h0z" class="AU"></path><path d="M350 718l2-2c5-2 11-2 15 0 1 1 2 1 3 1 2 2 4 3 6 5s5 5 6 8h1l4 8-1 1c0 1-1 4 0 6h0v1h-1s0-1-1-1c0-1-1-3-1-3l-2-5c-1-1-3-4-5-4-4-1-6-5-9-7-2-1-3-2-5-3-1-1-2-1-3-2h-1-2l-1-1c-2 0-3 0-4 1-1 0-2-1-3-1l2-2z" class="AG"></path><path d="M382 730h1l4 8-1 1c0 1-1 4 0 6h0v1h-1s0-1-1-1c0-1-1-3-1-3l-2-5c2 2 2 3 3 5 0-1 0-2-1-2-1-2 0-4 0-6-1-1-1-2-1-3v-1z" class="h"></path><path d="M363 695l1-1h1 2 2l4 1c0 1 0 2-1 3l1 1-4 4v1s-1 0-1 1l3 6c-1 2-2 3-4 5-4-2-10-2-15 0l-2 2v-2c1-2 1-3 1-5l-7 1-1-1c-2 1-4 1-6 2l-3-3c1-1 2-2 4-3l2-2s1 0 2-1 2-1 3-2h1l1-1c1 0 3-1 4-1s2-1 3-2c1 0 2-1 3-1s2-1 2-2l2-1c1 0 1 0 2 1z" class="U"></path><path d="M351 711c4 0 8-1 12 1h1c-2 2-6 1-8 1s-4 2-6 3c1-2 1-3 1-5z" class="AA"></path><path d="M354 698l1 3h-2-1 0c-1 1-1 2-2 2 0 1 0 2-2 2 1 1 1 1 2 1-1 1-2 1-3 1 1 1 2 1 3 1h-1l1 1h-1-1 0l-2-1c-1 1-1 2-3 3s-4 1-6 2l-3-3c1-1 2-2 4-3l2-2s1 0 2-1 2-1 3-2h1l1-1c1 0 3-1 4-1s2-1 3-2z" class="F"></path><path d="M354 698l1 3h-2-1 0c-1 1-1 2-2 2l-5 3c-1 0-2 1-2 1h-2v-1h1c-1-1-1 0-2 1h-2l2-2s1 0 2-1 2-1 3-2h1l1-1c1 0 3-1 4-1s2-1 3-2z" class="AB"></path><path d="M363 695l1-1h1 2 2l4 1c0 1 0 2-1 3l1 1-4 4v1s-1 0-1 1h0 0-1v2l-1 1v1h-1c-1-1-1-1-1-2h-1c-1-1-2-1-2-2-2 1-5 1-7 0-1 0-2 0-3-1 2 0 3-1 5-1 1 0 1 0 2-1-2 0-4 0-5-1h2l-1-3c1 0 2-1 3-1s2-1 2-2l2-1c1 0 1 0 2 1z" class="F"></path><path d="M363 702c2-1 2-2 4-2h3 0c-1 1-2 2-3 2s-2 1-2 1h-1l-1-1z" class="S"></path><path d="M363 695l1-1h1 2 2l4 1c0 1 0 2-1 3l-2 2h0-3c-2 0-2 1-4 2l-1-1h-3l2-2c1 0 1-1 2-1l-1-1c-1 1-1 1-2 1h-1l1-1-1-2 2-1c1 0 1 0 2 1z" class="n"></path><path d="M361 694c1 0 1 0 2 1h1c1 1 3 0 4 1-2 2-3 1-5 2l-1-1c-1 1-1 1-2 1h-1l1-1-1-2 2-1z" class="E"></path><path d="M381 720h1c2 1 4 2 6 2 1 0 3 1 4 2h1c1 2 1 3 2 4l2 4 1 2c0 1 1 2 1 3 1 1 1 1 1 2s0 1 1 2v2l1 1v2c-1 1-1 1-1 2 1 1 2 1 1 2v3c1 1 1 2 2 2-1 1-1 2-2 4-2 5-6 12-9 17v-4l-1-2v-1h-1c0 3 1 6 0 8l-2-31-2-8-4-8h-1c-1-3-4-6-6-8l1-1 2 2 1 2c1 0 2-1 3-1v-1c-1-1-1-1-1-2l-1-1z" class="S"></path><path d="M383 730h1c2 4 4 7 5 10 3 10 4 22 4 32l-1-2v-1h-1c0 3 1 6 0 8l-2-31-2-8-4-8z" class="AN"></path><path d="M382 720c2 1 4 2 6 2 1 0 3 1 4 2h1c1 2 1 3 2 4l2 4 1 2c0 1 1 2 1 3 1 1 1 1 1 2s0 1 1 2v2l1 1v2c-1 1-1 1-1 2 1 1 2 1 1 2v3c1 1 1 2 2 2-1 1-1 2-2 4-1 0-2 1-2 2v1l-2-1c0-1 1-1 1-2l1-1-1-2v-1c0-1 0-2-1-3 0-1 1-3 0-4-1-2-1-5-3-7h-1c-1-2-2-3-3-4v-2l-3-5-1-2c0-1-1-2-1-3l-1-1h0c-1-2-2-3-3-4z" class="e"></path><path d="M392 729c1 0 2 0 3 2 0 0 0 1 1 1h-1l-1-1-1 1h-1v-3z" class="o"></path><path d="M382 720c2 1 4 2 6 2 1 0 3 1 4 2h1c1 2 1 3 2 4l2 4 1 2c0 1 1 2 1 3 1 1 1 1 1 2s0 1 1 2v2l1 1v2c-2-2-3-3-3-6v-2h-1c0-1-2-3-2-3 0-2 1-1 0-3-1 0-1-1-1-1-1-2-2-2-3-2-3-1-4-4-7-5h0c-1-2-2-3-3-4z" class="P"></path><path d="M373 699c2 1 3 3 5 4h1 1l2-1c2 1 3 2 4 3 1 0 3 1 4 1l1 1v-1c2 2 4 3 6 5 2 3 4 4 7 5l7 3 3 1 1 3 3 8h-1c-2 0-3-1-4-2 0-1-1-2-1-2-2-1-3-3-6-4v1c-1-1-2-1-3-2-1 0-1 0-2-1h-3c1 1 2 3 3 4-1-1-2-1-3-2l-2-3h-1c-1 1 0 3-2 4h-1c-1-1-3-2-4-2-2 0-4-1-6-2h-1l1 1c0 1 0 1 1 2v1c-1 0-2 1-3 1l-1-2-2-2-1 1c-2-2-4-3-6-5-1 0-2 0-3-1 2-2 3-3 4-5l-3-6c0-1 1-1 1-1v-1l4-4z" class="AA"></path><path d="M373 699c2 1 3 3 5 4v1l-3-1v1l1 2c-1-1-2-1-3-2-1 0-1 0-2-1-1 0-1 0-2 1v-1l4-4z" class="m"></path><path d="M376 714l3 1h2c3 1 4 2 6 4l1 1 1 1c1 1 2 1 3 3-1-1-3-2-4-2-2 0-4-1-6-2h-1c0-1-2-2-3-4l-2-2z" class="Q"></path><path d="M371 716c1 0 1-1 2-2-1 0-1-1-1-2h1c1 0 2 1 3 2l2 2c1 2 3 3 3 4l1 1c0 1 0 1 1 2v1c-1 0-2 1-3 1l-1-2-2-2-1 1c-2-2-4-3-6-5l1-1z" class="n"></path><path d="M371 716c1 0 1-1 2-2-1 0-1-1-1-2h1c1 0 2 1 3 2l2 2v1c0 2 0 2-1 2l-3-2c-1-1-2-1-3-1z" class="O"></path><path d="M382 702c2 1 3 2 4 3 1 0 3 1 4 1l1 1v-1c2 2 4 3 6 5 2 3 4 4 7 5l7 3 3 1 1 3 3 8h-1c-2 0-3-1-4-2 0-1-1-2-1-2-2-1-3-3-6-4v1c-1-1-2-1-3-2s-2-2-4-3h0c-1-1-2-1-3-1-2-1-6-2-8-4l-3-2h-1l-3-3-5-3h0l-1-2v-1l3 1v-1h1 1l2-1z" class="x"></path><path d="M391 712l2-1h4c2 3 4 4 7 5l7 3v2l-6-3-14-6z" class="P"></path><path d="M382 702c2 1 3 2 4 3 1 0 3 1 4 1l1 1v-1c2 2 4 3 6 5h-4l-2 1c-2-2-5-3-8-5l-1-1c-2-1-3-1-4-2v-1h1 1l2-1z" class="H"></path><path d="M406 723c-1-1-5-4-6-5l1-1c1 1 2 1 4 1h0l6 3v-2l3 1 1 3 3 8h-1c-2 0-3-1-4-2 0-1-1-2-1-2-2-1-3-3-6-4z" class="B"></path><path d="M411 719l3 1 1 3c-2-1-3-2-4-2v-2z" class="N"></path><path d="M393 724c2-1 1-3 2-4h1l2 3c1 1 2 1 3 2-1-1-2-3-3-4h3c1 1 1 1 2 1 1 1 2 1 3 2v-1c3 1 4 3 6 4 0 0 1 1 1 2 1 1 2 2 4 2h1l2 7 7 16 1 2c-1 2-1 4 0 6l2 2c2 3 3 6 4 9l-2-2c0-1-1-2-1-3-1-1-2-1-3-2h-1v2c-1-2-3-5-4-8 0-2 0-4-1-6-2-3-4-4-8-5h0c-1 0-2 0-3 2 0 0-1 1-1 2-1 1-2 2-2 4h-1c-2-1-2-1-2-3h0l-1 1c-1 0-1-1-2-2v-3c1-1 0-1-1-2 0-1 0-1 1-2v-2l-1-1v-2c-1-1-1-1-1-2s0-1-1-2c0-1-1-2-1-3l-1-2-2-4c-1-1-1-2-2-4z" class="J"></path><path d="M413 736h0c1 1 2 2 2 3v5l-3-5-1-2v-1h2z" class="R"></path><path d="M406 723c3 1 4 3 6 4 0 0 1 1 1 2 1 1 2 2 4 2h1l2 7-2-2c-1-2-4-3-5-5 0-1-1-1-1-2l-1-1c-1 0-1 0-1-1l-1-1-3-2v-1z" class="N"></path><path d="M408 740v-1h4l3 5h1c1 1 2 1 3 3-3 0-6-4-9-3l-1 2c1 1 2 1 4 2l1 1c-1 0-2 0-3 2 0 0-1 1-1 2-1 1-2 2-2 4h-1c-2-1-2-1-2-3h0l1-2c0-1 1-2 1-4h0c-1-1 0-1 0-2v-3c0-1 0-2 1-3z" class="W"></path><path d="M408 740c1 0 2 0 3 2v1h-2c-1 1-1 0-1 1s0 4-1 4h0c-1-1 0-1 0-2v-3c0-1 0-2 1-3z" class="H"></path><path d="M393 724c2-1 1-3 2-4h1l2 3c1 1 2 1 3 2 2 1 3 3 5 4 1 1 2 3 3 4h1l3 3h-2v1l1 2h-4v1c-1 1-1 2-1 3v3c0 1-1 1 0 2h0c0 2-1 3-1 4l-1 2h0 0l-1 1c-1 0-1-1-2-2v-3c1-1 0-1-1-2 0-1 0-1 1-2v-2l-1-1v-2c-1-1-1-1-1-2s0-1-1-2c0-1-1-2-1-3l-1-2-2-4c-1-1-1-2-2-4z" class="P"></path><path d="M406 737c-1 0-1-1-2-1 0-1-1-2-2-3 0-1 0-1 1-2 2-1 3 2 6 2h1l3 3h-2v1l-1 1c-2 0-3 0-4-1z" class="K"></path><path d="M410 733l3 3h-2v1l-1 1c-2 0-3 0-4-1v-3h4v-1z" class="W"></path><path d="M393 724c2-1 1-3 2-4h1l-1 1c1 1 1 2 2 3 2 3 4 7 5 10l1 2v1 1l1 3 1 1v1 2c-1 1-1 2-1 3h0c-1-1-2-1-2-2v-2l-1-1v-2c-1-1-1-1-1-2s0-1-1-2c0-1-1-2-1-3l-1-2-2-4c-1-1-1-2-2-4z" class="K"></path><path d="M666 202l1-1c2-3 3-9 6-10 0 1-1 3-2 5l-3 6c2 1 3 2 4 4 1 1 2 1 3 1l4 3h2v1c2 3 10 6 14 7 3 1 6 3 9 4 0 1 2 2 2 2 2 0 3-2 4-1v1c-2 15-5 29-10 43-1 3-2 7-3 10s-3 7-3 10l-1 3-3 10c-1 1-1 3-2 4l-1-1-7-6-7-5c-1-1-2-1-3-2l-1-1c1-1 1-1 0-3v-3l2 1 1-3c-2-1-3-2-5-3-1 1-3 0-5 0-1 0-2-1-3-1-1-1-2-1-3-2-2 0-4-1-6-1h-1v-1h-6l-1-1c2-1 3-1 5-1 1 0 1 0 2-1h1c1-1 1-1 2-1h0l1-1 1-1c-6-6-12-10-19-14l-1-1c-1-1-3-2-4-2l-7-4c1-1 2-1 4-1 6 1 13-1 19-2l6-3 1 1-1 1v2c1-1 2-2 2-4 0-1 1-1 2-2h1c1-1 1-2 1-4l1-2c1-1 1-2 1-3 1-1 1-2 2-3v-4 2s-1 1-2 1h0v-2-1h1c0-1 0-2 1-3l-1-1c-2 0-2 0-3 1v-1l-1-1 1-2h1l-1-1 3-3c2-3 5-6 5-8v-1h0z" class="AG"></path><path d="M667 210c0 5 0 10 1 15 1 8 3 15 6 23-1-1-2-2-2-3l-5-17-1 1-1 1c0-1-1-2-2-3v-1l1-1c0-3 1-7 1-10 1-2 1-3 2-5z" class="o"></path><path d="M663 227c1 1 2 2 2 3l1-1 1-1 5 17c0 1 1 2 2 3 2 3 3 6 4 8 3 5 6 9 9 13-1 0-1-1-2-1h-1c-6-3-8-8-11-13-3-2-4-5-5-8l-1-2v-1l-1-1 1-2c-1-3 0-8-1-10h-3v-4z" class="n"></path><path d="M666 251c0-3-1-5-1-7h2v1l1 2c1 3 2 6 5 8 3 5 5 10 11 13h1c1 0 1 1 2 1l9 8h1c-1 3-3 7-3 10l-2 1h-1l-1-1 1-1 2-1h0c-1-1-2-1-2-2-1-1-2-2-2-3-3 0-7-5-9-7-1-1-2-2-3-4-1-1-2-1-2-3-1-1 0 1-1-1-1-1-2-2-2-3 0-2-1-3-2-4l-1-1c0-1 0-2-1-2-1-2-1-3-2-4z" class="Q"></path><path d="M689 280l-5-5c0-1 0-2-1-4v-2c4 0 6 7 10 8 1 0 1 0 2 1h0l1-1h1c-1 3-3 7-3 10l-2 1h-1l-1-1 1-1 2-1h0c-1-1-2-1-2-2-1-1-2-2-2-3z" class="h"></path><path d="M666 202l1-1c2-3 3-9 6-10 0 1-1 3-2 5l-3 6c2 1 3 2 4 4-1-1-2-1-3-2-1 2-1 4-2 6s-1 3-2 5c0 3-1 7-1 10l-1 1v1 4h3c1 2 0 7 1 10l-1 2 1 1h-2c0 2 1 4 1 7 1 1 1 2 2 4 1 0 1 1 1 2l1 1c1 1 2 2 2 4-2-1-2-2-3-4-2-3-3-5-4-8s-2-5-2-7l-3-3v1l-1 1-1 2-2-1 1-5c1-1 1-2 1-4l1-2c1-1 1-2 1-3 1-1 1-2 2-3v-4 2s-1 1-2 1h0v-2-1h1c0-1 0-2 1-3l-1-1c-2 0-2 0-3 1v-1l-1-1 1-2h1l-1-1 3-3c2-3 5-6 5-8v-1h0z" class="U"></path><path d="M663 231h3c1 2 0 7 1 10l-1 2 1 1h-2c0 2 1 4 1 7l-1-3v-1l-1-1v-2c0-1 0-2-1-3v-10z" class="P"></path><path d="M663 231h3c-1 1-1 2-1 3v4l-1-1-1 4v-10z" class="O"></path><path d="M660 229c1-1 1-2 2-3 0 4-1 10 0 14l1 3-3-3v1l-1 1-1 2-2-1 1-5c1-1 1-2 1-4l1-2c1-1 1-2 1-3z" class="d"></path><path d="M658 234l2 2v3 1 1l-1 1-1 2-2-1 1-5c1-1 1-2 1-4z" class="x"></path><defs><linearGradient id="g" x1="678.81" y1="258.708" x2="667.777" y2="271.051" xlink:href="#B"><stop offset="0" stop-color="#230100"></stop><stop offset="1" stop-color="#430a0a"></stop></linearGradient></defs><path fill="url(#g)" d="M656 238h1l-1 5 2 1 1-2 1-1v-1l3 3c0 2 1 4 2 7s2 5 4 8c1 2 1 3 3 4 0 1 1 2 2 3 1 2 0 0 1 1 0 2 1 2 2 3 1 2 2 3 3 4 2 2 6 7 9 7 0 1 1 2 2 3 0 1 1 1 2 2h0l-2 1-1 1 1 1h1l2-1-1 3-3 10c-1 1-1 3-2 4l-1-1-7-6-7-5c-1-1-2-1-3-2l-1-1c1-1 1-1 0-3v-3l2 1 1-3c-2-1-3-2-5-3-1 1-3 0-5 0-1 0-2-1-3-1-1-1-2-1-3-2-2 0-4-1-6-1h-1v-1h-6l-1-1c2-1 3-1 5-1 1 0 1 0 2-1h1c1-1 1-1 2-1h0l1-1 1-1c-6-6-12-10-19-14l-1-1c-1-1-3-2-4-2l-7-4c1-1 2-1 4-1 6 1 13-1 19-2l6-3 1 1-1 1v2c1-1 2-2 2-4 0-1 1-1 2-2z"></path><path d="M664 255h0c2 2 4 4 4 7l-1 1c-1-2-4-5-4-8h1z" class="N"></path><path d="M660 240l3 3c0 2 1 4 2 7-1-1-2-1-3-2v-1c-2-2-2-4-2-6v-1z" class="R"></path><path d="M656 243l2 1c0 1 1 2 0 3l-2 1c1 1 1 1 2 1v1h-2c0 1 1 2 2 3l-1 1-1-1c-1-2-2-3-1-5l1-5z" class="b"></path><path d="M656 238h1l-1 5-1 5c-1 2 0 3 1 5l1 1c0 2 1 3 2 5l1 2c1 1 2 3 3 4l1 2c1 0 2 1 2 2-1 0-2-1-3-2 0-1-1-2-2-2-1-4-4-7-6-10 0-1-1-1-1-2h0c-1-1-2 0-2 0h-3s1-1 2-1c1-1 1-1 2-1v-2-1l-1-1c-1 0-1-1-2-2l2-1c1-1 2-2 2-4 0-1 1-1 2-2z" class="n"></path><path d="M663 267c1 1 2 2 3 2l6 6c3 3 6 5 9 9-1 0-2-1-3-1 0-1 0-1-1-1 1 1 2 2 2 3-3 0-5-2-7-4-2-1-3-2-5-3-1-1-2-2-3-2 0-1-1-2-1-3v-2-1h0v-3z" class="P"></path><path d="M652 253s1-1 2 0h0c0 1 1 1 1 2 2 3 5 6 6 10 1 0 2 1 2 2v3h0v1 2c0 1 1 2 1 3 1 0 2 1 3 2-1 1-3 0-5 0-1 0-2-1-3-1-1-1-2-1-3-2-2 0-4-1-6-1h-1v-1h-6l-1-1c2-1 3-1 5-1 1 0 1 0 2-1h1c1-1 1-1 2-1h0l1-1 1-1 3 4 1-1v-4l1-2c0-1-3-5-3-5-1-1-2-2-2-4h0c-1-2-1-2-2-2z" class="b"></path><path d="M661 265c1 0 2 1 2 2v3h0v1 2c0 1 1 2 1 3l-2-2h0c0-3-1-4-3-6l2-1v-2z" class="K"></path><path d="M654 267l3 4s1 1 0 1v1h-8-6l-1-1c2-1 3-1 5-1 1 0 1 0 2-1h1c1-1 1-1 2-1h0l1-1 1-1z" class="N"></path><path d="M654 267l3 4s1 1 0 1l-7-2c1-1 1-1 2-1h0l1-1 1-1z" class="D"></path><path d="M635 253c2-1 6-1 8-2l2 1h0v2c1 0 3 0 4-1h3c1 0 1 0 2 2h0c0 2 1 3 2 4 0 0 3 4 3 5l-1 2v4l-1 1-3-4c-6-6-12-10-19-14z" class="B"></path><path d="M652 240l1 1-1 1v2l-2 1c1 1 1 2 2 2l1 1v1 2c-1 0-1 0-2 1-1 0-2 1-2 1-1 1-3 1-4 1v-2h0l-2-1c-2 1-6 1-8 2l-1-1c-1-1-3-2-4-2l-7-4c1-1 2-1 4-1 6 1 13-1 19-2l6-3z" class="J"></path><path d="M634 252h0l1-1s1 0 2-1l6-1 2 1-2 1c-2 1-6 1-8 2l-1-1z" class="H"></path><path d="M643 249c3-1 5-2 7-4 1 1 1 2 2 2l1 1v1c-2 0-2-1-4 0-1 0-3 1-4 1l-2-1z" class="Z"></path><path d="M645 250c1 0 3-1 4-1 2-1 2 0 4 0v2c-1 0-1 0-2 1-1 0-2 1-2 1-1 1-3 1-4 1v-2h0l-2-1 2-1z" class="h"></path><path d="M672 281c2 2 4 4 7 4 0-1-1-2-2-3 1 0 1 0 1 1 1 0 2 1 3 1h0l1 1v1l2 3h2l-2-3c3 0 3 0 6 1l1 1h1l2-1-1 3-3 10c-1 1-1 3-2 4l-1-1-7-6-7-5c-1-1-2-1-3-2l-1-1c1-1 1-1 0-3v-3l2 1 1-3z" class="P"></path><path d="M682 291l2 2c2 1 3 1 3 3 1 1 2 3 3 4-1 1-1 3-2 4l-1-1 1-1c-1-4-3-6-6-8v-3z" class="AA"></path><path d="M672 281c2 2 4 4 7 4 0-1-1-2-2-3 1 0 1 0 1 1 1 0 2 1 3 1h0l1 1v1l2 3h-3l1 2v3l-4-2c0-1 0 0-1-1-1-2-3-3-5-5h-3v-3l2 1 1-3z" class="a"></path><path d="M669 283l2 1c2 3 7 3 7 8 0-1 0 0-1-1-1-2-3-3-5-5h-3v-3z" class="K"></path><path d="M682 291c-3-1-6-4-8-6 1 0 2 0 3 1s1 1 2 1h2l1 1v-2l2 3h-3l1 2z" class="W"></path><path d="M684 289h2l-2-3c3 0 3 0 6 1l1 1h1l2-1-1 3-3 10c-1-1-2-3-3-4 0-2-1-2-3-3l-2-2-1-2h3z" class="N"></path><path d="M684 289h2l-2-3c3 0 3 0 6 1l1 1h1l2-1-1 3h-1c-1-1-2-1-3-2l-2 1c0 1 1 1 1 2h0l-2-1-2 1v1 1l-2-2-1-2h3z" class="R"></path><path d="M334 613l3 5c1 0 4-1 6-1-3 2-5 4-6 6v1l-2 1c-2 2-4 4-5 7-1 1-2 3-2 5h2l2 1c3 1 9 4 12 7l-2-1-2 1 2 1v2l-1 1 1 1c1 1 2 2 4 3l2 2c0 1 1 1 2 2l1 1 5 6h0l4 7v2h-1l2 4 1 4v1l2 3c-1 1-1 2-2 2l3 3-1 1h-2l-1 1 1 1-1 1-2 1c0 1-1 2-2 2s-2 1-3 1c-1 1-2 2-3 2s-3 1-4 1l-1 1h-1c-1 1-2 1-3 2s-2 1-2 1l-2 2c-2 1-3 2-4 3l3 3c2-1 4-1 6-2l1 1c-6 1-11 3-16 6l-3 1c-1 1-3 2-4 3v1c-2 2-4 3-5 6l1 1 1-1c-1 1-2 3-3 4l-1 2c-2 1-3 2-5 4-2 1-4 5-6 6h-2v-1c-1 1-2 2-2 3h-1v1c-1 0-1 0-2-1 6-13 12-26 11-40-1-12-6-22-15-29v-1l1 1 4-1-3-3h0c2 1 2 1 4 1 1 1 2 1 3 3h0l5 3 2-2-4-5c-2-3-4-4-5-6l1 1 1-1c-3-2-5-4-8-6l-3-3v-1c1-1 3-2 5-3h-1-3 0v-1l4-3c4-2 7-5 11-8l5-7c3-4 7-6 10-10h0l1-1 1-1c1 0 1-1 2-1l1-1-1-2c-1 0-2 0-3-1 0-1-1-2 0-3 0 1 1 2 2 2l1 1c1 1 2 1 3 1l1 1h2c2 0 3-1 4-1 0-2-1-4-1-7h0z" class="AJ"></path><path d="M316 710c1 1 2 3 1 4 0 4-1 4-4 6l3-10z" class="F"></path><path d="M326 665c3 4 5 7 7 11-2 0-3-1-4-2-1-2-2-4-3-5v-4z" class="Z"></path><path d="M293 678l4-1 4 6c1 1 1 2 1 4l1 1c1 0 1 1 1 2l-1 1c-1-2-1-4-2-5a30.44 30.44 0 0 0-8-8z" class="Q"></path><path d="M324 643c1-2 2-5 4-5h1 2c3 1 8 4 11 6l-2 1c-2-1-3-2-4-2h0c-1-1-1-1-2-1-1-1-3-1-4-1v-1h-3v2c-1 1-1 0-3 1z" class="b"></path><path d="M317 714h1l1 3 2-1c-1 2-4 5-5 7s-3 4-5 5c-1 2-3 3-4 5 2-4 5-8 6-13 3-2 4-2 4-6z" class="AB"></path><path d="M299 668l1 1c5 4 9 8 12 14h-1v5 1l-3-3-1 1c-1-2-2-3-3-5 2 0 3 1 5 1v-1l-1-3-4-5c-2-3-4-4-5-6z" class="K"></path><path d="M337 706l3-1-2 2c-2 1-3 2-4 3l3 3c-6 2-12 5-18 9 1-2 4-7 6-8 5 0 9-5 12-8z" class="AI"></path><path d="M322 660l4 5v4c1 1 2 3 3 5l-1 1h-2 0l-5-3c0-1 0-2-1-3l1-1 1-1 1 1h0 0c0-2-1-2-1-3s0-1 1-1c-1-2-1-3-1-4h0z" class="K"></path><path d="M324 644c0 1 1 2 1 3h0l-1 1c1 2 2 3 2 5v1l-2-1v1c0 1 1 1 2 2h1c-1 1-1 2-2 2s-1 0-2 1c-2-2-4-4-5-6 3-2 4-6 6-9z" class="J"></path><path d="M331 661l1 2c1-1 1-1 2-1 0 2 0 2 1 3l-2 2 3 2v1c-1 1-1 1 0 2 0 3 0 5-1 7-2-6-5-11-8-16h1c1 0 2-2 3-2z" class="H"></path><path d="M332 663c1-1 1-1 2-1 0 2 0 2 1 3l-2 2c-1-1-1-2-1-4z" class="S"></path><path d="M324 643c2-1 2 0 3-1v-2h3v1c1 0 3 0 4 1 1 0 1 0 2 1h0l-2 4h-3l-3 2c-1 0-2-1-3-2h0c0-1-1-2-1-3v-1z" class="AK"></path><path d="M325 647c1-1 2-2 3-1s1 1 3 1l-3 2c-1 0-2-1-3-2h0z" class="N"></path><path d="M335 694v2l1 1c1 1 2 1 3 3h0c-1 2-1 3-2 5v1c-3 3-7 8-12 8 4-6 8-12 10-20z" class="e"></path><path d="M321 722v1c-2 2-4 3-5 6l1 1 1-1c-1 1-2 3-3 4l-1 2c-2 1-3 2-5 4-2 1-4 5-6 6h-2v-1c3-6 7-12 13-16 0-2 6-5 7-6z" class="Z"></path><path d="M321 722v1c-2 2-4 3-5 6l1 1 1-1c-1 1-2 3-3 4l-1 2c-2 0-2 0-3 1-2 1-3 1-5 2 2-2 3-4 5-5 1-1 2-2 2-3 1 0 1-2 1-2 0-2 6-5 7-6z" class="a"></path><path d="M334 647l2-4c1 0 2 1 4 2l2 1v2l-1 1 1 1c1 1 2 2 4 3l-1 1 1 2c-1 0-1 0-1 1-2 0-3-1-4-3-1-1-1-2-2-2-2 0-3 0-5 1-1-1-2-2-3-2l-2-1h-1 0c1 1 1 2 1 2h-1l-1-1v1 2 1c-1 0-1 0-1 1-1-1-2-1-2-2v-1l2 1v-1c0-2-1-3-2-5l1-1c1 1 2 2 3 2l3-2h3z" class="b"></path><path d="M334 647c1 0 1 0 3 1 1 0 2 1 3 1h-3l-2 2-1-1c-2-1-4-1-6-1l3-2h3z" class="x"></path><defs><linearGradient id="h" x1="336.597" y1="648.075" x2="340.403" y2="645.425" xlink:href="#B"><stop offset="0" stop-color="#190404"></stop><stop offset="1" stop-color="#2d0706"></stop></linearGradient></defs><path fill="url(#h)" d="M334 647l2-4c1 0 2 1 4 2l2 1v2l-1 1h-1c-1 0-2-1-3-1-2-1-2-1-3-1z"></path><path d="M326 656c0-1 0-1 1-1v-1-2-1l1 1h1s0-1-1-2h0 1l2 1c1 0 2 1 3 2 3 2 3 3 3 6 1 0 1 0 2 1v1c-1 1-1 1-1 2l-1 1-3-2c-1 0-1 0-2 1l-1-2c-1 0-2 2-3 2h-1l-2-1-2-3c1-1 1-1 2-1s1-1 2-2h-1z" class="n"></path><path d="M331 651c1 0 2 1 3 2 3 2 3 3 3 6-1-1-2-1-3-3l-1-2c0-1-1-2-2-3z" class="P"></path><path d="M325 662c2-1 2-1 3-2l-1-1 1-1v-1l3 3v1c-1 0-2 2-3 2h-1l-2-1z" class="e"></path><path d="M294 674h0c2 1 2 1 4 1 1 1 2 1 3 3h0l5 3 2-2 1 3v1c-2 0-3-1-5-1 1 2 2 3 3 5l1-1 3 3v-1-5h1c0 1 1 3 1 4-1 2-1 5-2 7v7h0l-1 3h-2c-1-5-2-9-5-13l1-1c0-1 0-2-1-2l-1-1c0-2 0-3-1-4l-4-6-3-3z" class="a"></path><path d="M294 674h0c2 1 2 1 4 1 1 1 2 1 3 3h0c0 2 2 4 2 6h-1l-1-1-4-6-3-3z" class="R"></path><path d="M312 683c0 1 1 3 1 4-1 2-1 5-2 7v7h0l-1 3h-2c-1-5-2-9-5-13l1-1 1 2c1 0 1 1 2 2 0 1 0 2 1 3 2 2 1 2 1 5 0-1 1-2 1-2v-1-1l-1-1c1-3 0-8-2-10l1-1 3 3v-1-5h1z" class="P"></path><path d="M303 654c4-1 7-2 11-1 3 2 6 4 8 7h0c0 1 0 2 1 4-1 0-1 0-1 1s1 1 1 3h0 0l-1-1-1 1-1 1c-1 0-1-1-2-1-1-2-4-4-5-5h0c-2-2-5-4-7-4l-3-2h-2l-1-1c2 0 2-1 3-2z" class="J"></path><path d="M303 657c2 0 5 0 6 1 1 0 2 1 2 1 3 3 7 5 11 6 0 1 1 1 1 3h0 0l-1-1-1 1-1 1c-1 0-1-1-2-1-1-2-4-4-5-5h0c-2-2-5-4-7-4l-3-2z" class="Q"></path><path d="M311 701v-7c1-2 1-5 2-7 1 2 3 5 2 8h1c2 12-4 27-11 37h0c0-1 0-2 1-3 1-3 1-6 2-9v-12-4h2l1-3h0z" class="S"></path><path d="M311 701v-7c1-2 1-5 2-7 1 2 3 5 2 8h1c-1 2 0 5-1 7-1 1-2 4-2 5v1c-1 1-1 2-1 4h-1v-1h-1l-1 1v-4h-1v-4h2l1-3h0z" class="o"></path><g class="Z"><path d="M308 704h2l1-3c0 2 0 6-1 7h-1-1v-4z"></path><path d="M311 701v-7c1-2 1-5 2-7 1 2 3 5 2 8-1 2 0 3 0 5-1-1-1-2-1-4v2h0l-1-1c-1 1 0 2-2 4z"></path></g><path d="M295 655c2-1 5-1 8-1-1 1-1 2-3 2l1 1h2l3 2c2 0 5 2 7 4h0v1c0 1 1 2 2 3h-1c0 1 0 2 1 3h0l-1 1c-2 0-4-1-5-3l-2 1v1 1 1c2 1 2 2 3 4l-9-8c-3-2-5-4-8-6l-3-3v-1c1-1 3-2 5-3z" class="K"></path><path d="M295 655c2-1 5-1 8-1-1 1-1 2-3 2l1 1h2-2v1c1 1 0 1 1 1 2 0 4 1 6 2-1 1-2 1-3 2-1 0-3-1-4-1l-7-2-4-1v-1c1-1 3-2 5-3z" class="d"></path><path d="M290 659l4 1 7 2s1 1 2 1c1 1 1 1 2 1 1 1 2 3 4 4l-2 1v1 1 1c2 1 2 2 3 4l-9-8c-3-2-5-4-8-6l-3-3z" class="o"></path><path d="M294 660l7 2s1 1 2 1c1 1 1 1 2 1 1 1 2 3 4 4l-2 1-1-1v-1c-1-1-2-2-4-3l-1 1c-3-1-5-3-7-5z" class="Z"></path><path d="M334 613l3 5c1 0 4-1 6-1-3 2-5 4-6 6v1l-2 1-1-1-2 2c-2 4-4 8-7 12-2 6-3 10-9 13-3 0-7 0-10 1l-7 1c-1 1-3 1-5 2h-3 0v-1l4-3c4-2 7-5 11-8l5-7c3-4 7-6 10-10h0l1-1 1-1c1 0 1-1 2-1l1-1-1-2c-1 0-2 0-3-1 0-1-1-2 0-3 0 1 1 2 2 2l1 1c1 1 2 1 3 1l1 1h2c2 0 3-1 4-1 0-2-1-4-1-7h0z" class="J"></path><path d="M316 642l-1 1-1-1 1-2c1-1 1-2 2-2 2-1 3-1 5 0l1-1v2h-1c-2 0-3 1-5 2l-1 1z" class="m"></path><path d="M306 643c0 1 1 1 0 2s-2 1-2 2c-1 1-2 1-4 2l-1 2h1c1 0 2 0 3-1h1l2 2-7 1c-1 1-3 1-5 2h-3 0v-1l4-3c4-2 7-5 11-8z" class="Z"></path><path d="M323 637c1 0 4-5 4-5v-2c2-2 3-3 5-4-2 4-4 8-7 12-2 6-3 10-9 13-3 0-7 0-10 1l-2-2c2 0 4-1 6-1 2-1 4 0 6 0 3-2 5-5 6-8h-3c-1 1-2 1-3 1l1-1c2-1 3-2 5-2h1v-2z" class="k"></path><path d="M334 613l3 5c1 0 4-1 6-1-3 2-5 4-6 6v1l-2 1-1-1 3-2v-1c-2 1-3 3-5 3-1 0-1 0-2 1s-2 1-4 1-5 2-6 3c0 1 0 1-1 1l-3 3-3 3h-2c3-4 7-6 10-10h0l1-1 1-1c1 0 1-1 2-1l1-1-1-2c-1 0-2 0-3-1 0-1-1-2 0-3 0 1 1 2 2 2l1 1c1 1 2 1 3 1l1 1h2c2 0 3-1 4-1 0-2-1-4-1-7h0z" class="U"></path><path d="M313 663c1 1 4 3 5 5 1 0 1 1 2 1 1 1 1 2 1 3l5 3h0 2l1-1c1 1 2 2 4 2v2c3 7 1 16-2 23-2 6-5 10-10 15l-2 1-1-3h-1c1-1 0-3-1-4l1-6c1-4 0-8 0-11l-3-8c-1-3-3-6-4-9-1-2-1-3-3-4v-1-1-1l2-1c1 2 3 3 5 3l1-1h0c-1-1-1-2-1-3h1c-1-1-2-2-2-3v-1z" class="e"></path><path d="M317 704l2-2h0v6c0-1-1-1-1-2-1-1 0-2-1-2z" class="S"></path><path d="M316 710l1-6c1 0 0 1 1 2 0 1 1 1 1 2-1 2-1 4-1 6h-1c1-1 0-3-1-4z" class="U"></path><path d="M324 680s1 0 2 1c1 2 0 3 0 5h-1c0-1 0-1-1-2 0 1 0 2-1 3l-1-2h1v-2h-1v-2h1v1l1 1v-3zm-15-12c1 2 3 3 5 3 0 2 1 2 0 3v-2h-1-2c-1-1-2-1-3-1l-1-1v-1l2-1z" class="O"></path><path d="M315 682c1 2 1 4 2 5 1 2 1 2 1 4l1 2h-2l-3-8 1-3z" class="H"></path><path d="M317 693h2v9h0l-2 2c1-4 0-8 0-11z" class="E"></path><path d="M318 668c1 0 1 1 2 1 1 1 1 2 1 3 1 2 2 2 2 4 1 0 1 1 2 2s1 1 1 3c-1-1-2-1-2-1l-6-12z" class="Z"></path><path d="M310 676c-1-2-1-3-3-4v-1-1l1 1 4 4c2 3 2 4 3 7l-1 3c-1-3-3-6-4-9z" class="U"></path><path d="M321 672l5 3h0 2l1-1c1 1 2 2 4 2v2h0c-1 2-1 3-1 5 1 2 0 3 0 5v2c-1 2-1 3-2 5l-1-1v-1h-1c-1 1-1 2-1 3h-1v-3l1-1c-2-2-2-3-2-6h1c0-2 1-3 0-5 0-2 0-2-1-3s-1-2-2-2c0-2-1-2-2-4z" class="P"></path><path d="M334 653c2-1 3-1 5-1 1 0 1 1 2 2 1 2 2 3 4 3 0-1 0-1 1-1l-1-2 1-1 2 2c0 1 1 1 2 2l1 1 5 6h0l4 7v2h-1l2 4 1 4v1l2 3c-1 1-1 2-2 2l3 3-1 1h-2l-1 1 1 1-1 1-2 1c0 1-1 2-2 2s-2 1-3 1c-1 1-2 2-3 2s-3 1-4 1l-1 1h-1c-1 1-2 1-3 2s-2 1-2 1l-3 1v-1c1-2 1-3 2-5h0c-1-2-2-2-3-3l-1-1v-2c0-3 1-7 1-11l-1-4c1-2 1-4 1-7-1-1-1-1 0-2v-1l-3-2 2-2c-1-1-1-1-1-3l3 2 1-1c0-1 0-1 1-2v-1c-1-1-1-1-2-1 0-3 0-4-3-6z" class="AP"></path><path d="M349 681l2 1 1-1 1 1-1 1 1 1v1h-2s-1-1-2-1h0l-1-1 1-2h0z" class="F"></path><path d="M336 697v-2-1c0-1 0 0 1-1h1l1 2c1 1 2 3 3 4l-3 1h0c-1-2-2-2-3-3z" class="H"></path><path d="M341 688h1v1h2c0 2-1 2-2 3v1 1h-1c-2-1-1-2-2-3 0-2 1-2 2-3zm17 3c0-1-2-1-2-1l-3-3c1-1 2-1 4-2 1 2 2 4 4 6h-3z" class="F"></path><path d="M347 677c1-1 2-1 3-2 0 2 0 3 1 4l-2 2h0c-1-1-1-1-1-3h-1v2c-1 1-1 1-3 1l-1-1c1-1 2-2 4-3z" class="H"></path><path d="M351 671c1 1 1 2 2 3 0 1 0 3 1 4v2h-1l-2-1c-1-1-1-2-1-4-1 1-2 1-3 2l-1-2 1-2v-2c1 1 1 1 2 1s1-1 2-1z" class="K"></path><path d="M347 673h2v1c0 1 0 0 1 1-1 1-2 1-3 2l-1-2 1-2z" class="P"></path><path d="M343 666h0v-1h1l2 4 1 2v2l-1 2 1 2c-2 1-3 2-4 3h0-1l1-2v-1c-1 0-1-1-2-1l1-2-3-4 1-1 3-3z" class="H"></path><path d="M343 666h0v-1h1l2 4-1 1c-1-1-3-1-5-1l3-3zm0 11s0-1 1-2c0-1 1-1 2-2v2l1 2c-2 1-3 2-4 3h0-1l1-2v-1z" class="S"></path><path d="M353 674l1 1 1-1c1 1 1 2 2 4l3 3c1-1 1-2 1-4l1 4v1l2 3c-1 1-1 2-2 2l3 3-1 1h-2l-1 1v-1c-2-2-3-4-4-6l-4-5h1v-2c-1-1-1-3-1-4z" class="O"></path><path d="M354 675l1-1c1 1 1 2 2 4l3 3c1-1 1-2 1-4l1 4v1l2 3c-1 1-1 2-2 2l-8-12z" class="S"></path><path d="M358 691h3v1l1 1-1 1-2 1c0 1-1 2-2 2s-2 1-3 1c-1 1-2 2-3 2s-3 1-4 1l-1 1h-1c-1 1-2 1-3 2s-2 1-2 1l-3 1v-1c1-2 1-3 2-5l3-1h2l3-3v-1-2c2 0 2 1 3 2l1-1c2 0 2 0 3-1h4v-2h0z" class="q"></path><path d="M334 653c2-1 3-1 5-1 1 0 1 1 2 2 1 2 2 3 4 3 0-1 0-1 1-1l-1-2 1-1 2 2c0 1 1 1 2 2l1 1 5 6h0l4 7v2h-1l2 4c0 2 0 3-1 4l-3-3c-1-2-1-3-2-4l-1 1-1-1c-1-1-1-2-2-3-1 0-1 1-2 1s-1 0-2-1l-1-2-2-4h-1v1h0l-3 3-1 1c0-1-1-2-2-4h0l-2-1c-1-1-1-1-1-3l3 2 1-1c0-1 0-1 1-2v-1c-1-1-1-1-2-1 0-3 0-4-3-6z" class="K"></path><path d="M341 661l-1-1 1-1c0-1 0-1-1-2l1-1 2 2c1 1 2 3 3 5h0-1l-1 1-1-1-2-2z" class="Q"></path><path d="M344 664l1-1h1c1 2 1 3 3 4h0l2 4c-1 0-1 1-2 1s-1 0-2-1l-1-2-2-4v-1z" class="d"></path><path d="M341 661l2 2 1 1v1h-1v1h0l-3 3-1 1c0-1-1-2-2-4h0l-2-1c-1-1-1-1-1-3l3 2 1-1h3v-2z" class="e"></path><path d="M337 666l1-1c2 0 3 0 5 1l-3 3-1 1c0-1-1-2-2-4z" class="n"></path><path d="M349 667h1v1h3v-3c0-2-1-2-2-3h-1v-2-1l1-1 5 6h0l4 7v2h-1l2 4c0 2 0 3-1 4l-3-3c-1-2-1-3-2-4l-1 1-1-1c-1-1-1-2-2-3l-2-4z" class="o"></path><path d="M359 673v-1l-3-3v-2c0-1-1-1-1-2l1-1 4 7v2h-1z" class="E"></path><defs><linearGradient id="i" x1="819.326" y1="134.559" x2="855.007" y2="169.32" xlink:href="#B"><stop offset="0" stop-color="#707274"></stop><stop offset="1" stop-color="#989796"></stop></linearGradient></defs><path fill="url(#i)" d="M834 97v-1l1-1 78 1h16 5c-1 1-3 1-4 2-45 15-88 38-117 76-7 10-13 20-18 30l-4 9c-1 1-2 5-3 7-1 1-3 2-4 2-2 2-5 5-8 6h-2v1c-2-1-3 0-4-2l-4-3v1c-1 9-7 17-11 26v-2l1-1v-1c1-1 1-1 0-2 0 1-1 2-2 2h-1c1-1 1-2 1-3 1-1 1-1 1-2l2-4c0-2 1-3 1-4h1v-1l1-2v-2h1v-2c1-1 1-2 1-3 1-2 1-3 3-5h0c0-2 0-3-1-4v-3c-1-3-2-6-2-8 0-1 1-3 2-4 1-3 1-6 2-9 2-3 3-6 4-8 2-4 3-9 5-13 1-1 2-1 3-2 1-4 1-6 3-9l1-1v-2l14-14h-3c4-6 8-12 10-18h0c6-10 11-14 20-19 3-2 6-3 8-5l3-3z"></path><path d="M787 215c1-2 2-3 4-4v2c-1 1-2 5-3 7-1 1-3 2-4 2-2 2-5 5-8 6h-2v-1c1 0 2-1 4-2 1 0 1 0 1-1h-1v-1c2-2 4-5 6-6l3-2z" class="r"></path><path d="M787 215l1 3c-1 1-1 1-3 1l-1-2 3-2z" class="X"></path><path d="M834 97v-1l1-1 78 1h16 5c-1 1-3 1-4 2-5-1-11 0-16 0-31 2-63 10-88 30-12 9-23 23-30 36-7 12-12 25-18 37l-6 14c-1 2-2 4-3 7 0 2 0 3 2 5h-1l-4-3v1c-1 9-7 17-11 26v-2l1-1v-1c1-1 1-1 0-2 0 1-1 2-2 2h-1c1-1 1-2 1-3 1-1 1-1 1-2l2-4c0-2 1-3 1-4h1v-1l1-2v-2h1v-2c1-1 1-2 1-3 1-2 1-3 3-5h0c0-2 0-3-1-4v-3c-1-3-2-6-2-8 0-1 1-3 2-4 1-3 1-6 2-9 2-3 3-6 4-8 2-4 3-9 5-13 1-1 2-1 3-2 1-4 1-6 3-9l1-1v-2l14-14h-3c4-6 8-12 10-18h0c6-10 11-14 20-19 3-2 6-3 8-5l3-3z" class="D"></path><path d="M826 115h1v2c-2 1-4 3-5 5v1-1c0-3 2-5 4-7z" class="L"></path><path d="M843 100l17 1-11 1h-7l1-2z" class="X"></path><path d="M831 100l3-3-2 3c3 1 8 1 11 0l-1 2c-4 0-8 0-12 1 1-1 1-2 1-3z" class="r"></path><path d="M787 163l1 2c-2 5-5 11-6 16 0 2-2 4-3 5l-1-3c0-2 0-3 1-4 3-3 5-10 7-14l1-2z" class="c"></path><defs><linearGradient id="j" x1="783.025" y1="149.119" x2="793.953" y2="168.106" xlink:href="#B"><stop offset="0" stop-color="#c9c7c5"></stop><stop offset="1" stop-color="#eeece9"></stop></linearGradient></defs><path fill="url(#j)" d="M796 142c1-1 2-2 3-4 0 2 1 3 2 5-1 3-3 7-5 10l-1-1 1-1c0-1 1-2 1-4-4 4-10 10-10 15v1l-1 2c-2 4-4 11-7 14-1 1-1 2-1 4l-1-2c0-3 0-5 1-8v-1-4c1-4 1-6 3-9l1-1v-2l14-14z"></path><path d="M779 179c2-5 3-10 6-14h1c-2 4-4 11-7 14z" class="I"></path><path d="M831 100c0 1 0 2-1 3 4-1 8-1 12-1h7c-3 0-5 1-7 1-2 1-4 1-5 2h-1c-5 2-8 6-12 9-4 2-7 4-10 7v-1c1-1 2-3 4-3 1-1 4-3 4-5h0 1l1-1s1-1 2-1h1l1-1-1-3c-1 0-2 1-3 1-3 2-6 3-9 5-2 2-3 4-5 6s-3 5-5 8h0c1 3-1 5 0 7 0 2-3 8-4 10-1-2-2-3-2-5-1 2-2 3-3 4h-3c4-6 8-12 10-18h0c6-10 11-14 20-19 3-2 6-3 8-5z" class="C"></path><path d="M831 100c0 1 0 2-1 3h-1c-7 5-15 8-21 15-2 2-3 5-4 8-2 3-4 7-5 10 0 1-1 1 0 2-1 2-2 3-3 4h-3c4-6 8-12 10-18h0c6-10 11-14 20-19 3-2 6-3 8-5z" class="S"></path><path d="M775 170c1-1 2-1 3-2v4 1c-1 3-1 5-1 8l1 2 1 3-11 29c0 1 0 2-1 3l-1-1v-1 3h-1 0 0c0-2 0-3-1-4v-3c-1-3-2-6-2-8 0-1 1-3 2-4 1-3 1-6 2-9 2-3 3-6 4-8 2-4 3-9 5-13z" class="G"></path><path d="M766 216s0-1-1-2h0c0-1-1-2 0-3h0 1l2 4c0 1 0 2-1 3l-1-1v-1z" class="c"></path><path d="M777 181l1 2 1 3-11 29-2-4c1-2 1-5 2-7 2-8 6-15 9-23z" class="u"></path><path d="M577 760c0-1 1-1 2-2v2l-3 7-39 113-8 23c-1 3-2 7-2 10l-20 61v-5l-1 1v1-1c1-10 0-20-1-30 0-5 0-10-1-14-2-11-7-22-10-33l-18-54-4-13c0-1-1-3-1-4v-1l3 2 2 1c1 0 2 0 2 1 0 2 1 3 2 4v1c1 2 2 3 2 5 2 3 1 6 3 9l1-1c0-1 0-2 1-3v-1c2-3 4-4 7-5l3-1h4l1 1 1-2v-2h-2c0-1-2-2-3-3l10-26 2-1c1-1 1-2 3-3 4-4 9-7 14-10 2-1 3-2 5-2h1 1c2-4 4-6 7-8l4-2h0c3-1 6-3 8-3l1 2c1 0 1-2 2-2l1-1-1-2 1 1h3c2-2 2-3 3-6-1 0-1 1-2 0l1-1 1-1 1-1v1l1-1 4-2h1l1 1h6z" class="z"></path><path d="M541 813l1 3h1 1c0-1 0-1 1-2-1 3-1 4-2 6l-4 2h-2c1-2 2-4 3-5l1-4z" class="AR"></path><path d="M569 759h1l1 1h6c-3 1-6 2-9 2-1 1-4 1-4 1-1 0-1 1-1 1-1 0-1 1-2 0l1-1 1-1 1-1v1l1-1 4-2z" class="AH"></path><path d="M535 846c2 1 3 1 5 0v1c2 0 3-1 4 0s1 0 2 1h0l1 1-1 1h-1c-2 0-9 0-10-1v-3z" class="B"></path><path d="M540 822h1c-1 8-5 16-8 24l-3 10-2-2c0-1 1-4 1-5v-1l2-1h1v-2l1-2c0-1 0-3 1-3 0-1 1-2 1-3 1-2 1-2 0-4 1-2 2-4 2-6 1-1 1-2 1-3s1-1 2-2z" class="C"></path><path d="M541 813l8-25c1-2 2-7 4-10l3 1c0 2-1 5-2 7l-5 14-4 14c-1 1-1 1-1 2h-1-1l-1-3z" class="I"></path><path d="M557 771h2c0 2-1 5-2 7-2 0-3 0-4-1-1 1-1 0-1 1l-13 38c-1 4-3 7-7 8-2 1-3 1-4 1 1-1 1-3 1-4h1v-1c4-5 6-13 7-19 1-4 3-7 3-11 2-5 2-10 5-15h0c3-1 6-3 8-3l1 2c1 0 1-2 2-2l1-1z" class="D"></path><path d="M557 771h2c0 2-1 5-2 7-2 0-3 0-4-1-1 1-1 0-1 1l-2-3c-2 0-3 2-4 4l-1 1h0v-2c1-2 1-2 0-3 3-1 6-3 8-3l1 2c1 0 1-2 2-2l1-1z" class="G"></path><path d="M545 775c-3 5-3 10-5 15 0 4-2 7-3 11-1 6-3 14-7 19v1h-1v-1s-1-1-2-1c-3-1-5-1-8-1-3-2-4-4-5-7 1-1 0-2 0-4h-1v-10c4-4 9-7 14-10 2-1 3-2 5-2h1 1c2-4 4-6 7-8l4-2z" class="L"></path><path d="M545 775c-3 5-3 10-5 15-1-2 0-4 0-6h0c1 1 0 1 1 1h0c0-2 0-1 1-2v-1-1-3c-1 0-3 1-3 3h0c0 3-1 4-3 6h0l-3 3-9 6c-3 2-7 3-9 5l-1 6h0-1v-10c4-4 9-7 14-10 2-1 3-2 5-2h1 1c2-4 4-6 7-8l4-2z" class="i"></path><path d="M513 797v10h1c0 2 1 3 0 4 1 3 2 5 5 7 3 0 5 0 8 1 1 0 2 1 2 1v1c0 1 0 3-1 4 1 0 2 0 4-1 4-1 6-4 7-8l1 1c-1 1-2 3-3 5h2 1c-1 1-2 1-2 2s0 2-1 3c0 2-1 4-2 6 1 2 1 2 0 4 0 1-1 2-1 3-1 0-1 2-1 3l-1 2v2c-1-3-1-3-3-4h0l-1-1c-1-1-2-2-5-2s-4 0-6 1l-4-4s-1-1-2-1c-2-2-5-4-7-6h-1-2c0-1-2-2-3-3l10-26 2-1c1-1 1-2 3-3z" class="G"></path><path d="M503 828c2 1 3 2 4 1h1l1 1h1v1c1 2 2 2 4 3 1 1 2 1 3 1v2 1h-1c-1-1-2-1-3-1 0 0-1-1-2-1-2-2-5-4-7-6l-1-2z" class="c"></path><path d="M505 813v1 3h0 2v-2c0 1 0 0 1 2l1-1v3c1 2 2 3 4 3s2 0 3 1c0 0-1 0-2 1-1 0-1 1-2 2l-1 1h-2-1l-1 2h1-1c-1 1-2 0-4-1h0 0c-1-3 0-8 1-11 0-1 1-3 1-4z" class="D"></path><path d="M503 828c1-1 2-1 3-1s1 0 1 2h1-1c-1 1-2 0-4-1h0z" class="u"></path><path d="M505 813v1 3h0 2v-2c0 1 0 0 1 2-1 1-2 1-1 2 0 2 1 2 1 3-1 1-3 2-3 3v1h-1v-3h0l1-1c0-2 0-3-1-5 0-1 1-3 1-4z" class="G"></path><path d="M513 797v10h1c0 2 1 3 0 4 1 3 2 5 5 7 3 0 5 0 8 1 1 0 2 1 2 1v1c-3-1-5-2-9-2v1l-1 1h-1c-4-1-6-5-8-8 0 1-1 2-1 3l-1 1c-1-2-1-1-1-2v2h-2 0v-3-1c0 1-1 3-1 4-1 3-2 8-1 11h0 0l1 2h-1-2c0-1-2-2-3-3l10-26 2-1c1-1 1-2 3-3z" class="p"></path><path d="M513 797v10h1c0 2 1 3 0 4-2-3-3-7-4-11 1-1 1-2 3-3z" class="D"></path><path d="M505 813c1-3 2-8 4-9 2 2 3 4 4 7l3 6c1 2 3 2 4 2v1l-1 1h-1c-4-1-6-5-8-8 0 1-1 2-1 3l-1 1c-1-2-1-1-1-2v2h-2 0v-3-1z" class="C"></path><path d="M510 813c-1-1 0-1 0-3l1-1c1 2 1 4 2 6l3 3v-1c1 2 3 2 4 2v1l-1 1h-1c-4-1-6-5-8-8z" class="L"></path><path d="M539 816l1 1c-1 1-2 3-3 5h2 1c-1 1-2 1-2 2s0 2-1 3c0 2-1 4-2 6 1 2 1 2 0 4 0 1-1 2-1 3-1 0-1 2-1 3l-1 2v2c-1-3-1-3-3-4h0l-1-1c-1-1-2-2-5-2s-4 0-6 1l-4-4c1 0 2 0 3 1h1v-1-2h1c2 1 2 1 4 1 1-1 3-1 4-1 1-1 2-2 2-3v-1c0-1 1-1 1-2h1l-2-2-2 2c-3 0-4-1-5-2l-1 1h-3c-1 0-1-1-2-1h-1v-1l3-1h3l1-1 1-1c-1-1-2-1-3-2l1-1v-1c4 0 6 1 9 2 0 1 0 3-1 4 1 0 2 0 4-1 4-1 6-4 7-8z" class="i"></path><path d="M539 816l1 1c-1 1-2 3-3 5h2 1c-1 1-2 1-2 2s0 2-1 3c0 2-1 4-2 6h-1c1-1 1-1 1-2 1-2 2-4 1-6-1 1-1 1-3 1-3 0-5 0-7-2-3-1-2 0-5 0l1-1c-1-1-2-1-3-2l1-1v-1c4 0 6 1 9 2 0 1 0 3-1 4 1 0 2 0 4-1 4-1 6-4 7-8z" class="q"></path><path d="M520 819c4 0 6 1 9 2 0 1 0 3-1 4-2-2-6-4-8-5v-1z" class="G"></path><path d="M528 832c1-1 2-2 3-4 1 0 1-1 2-1h1v3c-1 1-2 2-2 4v1 1c-2 2-2 4-2 6l-1 1h0l-1-1c-1-1-2-2-5-2s-4 0-6 1l-4-4c1 0 2 0 3 1h1v-1-2h1c2 1 2 1 4 1 1-1 3-1 4-1 1-1 2-2 2-3z" class="I"></path><path d="M507 969l-1 1v1-1c1-10 0-20-1-30 0-5 0-10-1-14-2-11-7-22-10-33l-18-54-4-13c0-1-1-3-1-4v-1l3 2 2 1c1 0 2 0 2 1 0 2 1 3 2 4v1c1 2 2 3 2 5 2 3 1 6 3 9l1-1c0-1 0-2 1-3v-1c2-3 4-4 7-5l3-1h4l1 1 1-2v-2h1c2 2 5 4 7 6 1 0 2 1 2 1l4 4c2-1 3-1 6-1s4 1 5 2l1 1h0c2 1 2 1 3 4h-1l-2 1v1c0 1-1 4-1 5l2 2c-6 19-13 39-17 59-2 6-3 12-4 18-2 12-1 24-2 36z" class="C"></path><path d="M520 844c3-1 5-1 7 1 1 0 1 0 1 1h0-3l-1 1c-1 0-3-1-5-2l1-1z" class="i"></path><path d="M505 849h6c-1 1-3 2-5 3s-3 3-5 3l1-3 3-3z" class="u"></path><path d="M528 846c1 0 2 1 3 1l-2 1v1c0 1-1 4-1 5l-19 60h-1c1-1 1-2 1-3l19-65h0z" class="r"></path><path d="M503 830h1c2 2 5 4 7 6 1 0 2 1 2 1l4 4c2-1 3-1 6-1s4 1 5 2l1 1h0c2 1 2 1 3 4h-1c-1 0-2-1-3-1 0-1 0-1-1-1-2-2-4-2-7-1l-1 1v3l-1 1h-2l-10-1c-1-2-1-2-2-3l1-1c2 0 2 0 3-1-1-1-2-2-3-2h4c-1-1-1 0-2-1-1 0-2-1-3-2s-1-1-3-2c-1-1-1-1-1-2l1-1 1 1 1-2v-2z" class="L"></path><path d="M505 841h4l2 1c2 0 3 1 4 2v1h-3l-1 1c2 1 3 2 5 3l-10-1c-1-2-1-2-2-3l1-1c2 0 2 0 3-1-1-1-2-2-3-2z" class="l"></path><path d="M505 841h4l2 1c0 1-1 2-3 2-1 1-2 0-3 0 2 0 2 0 3-1-1-1-2-2-3-2z" class="G"></path><path d="M503 830h1c2 2 5 4 7 6 1 0 2 1 2 1l4 4c2-1 3-1 6-1s4 1 5 2l1 1h0c2 1 2 1 3 4h-1c-1 0-2-1-3-1 0-1 0-1-1-1-2-2-4-2-7-1h-1-1c-3-4-9-5-13-9-1-1-2-1-3-1l1-2v-2z" class="F"></path><path d="M500 834c0 1 0 1 1 2 2 1 2 1 3 2s2 2 3 2c1 1 1 0 2 1h-4c1 0 2 1 3 2-1 1-1 1-3 1l-1 1c1 1 1 1 2 3l-1 1-3 3-1 3v1h-1c-1 1 0 2 0 4 1 4 6 16 3 20-1-3-1-7-3-9l-6-16c-1-3-3-6-4-9s-1-4 1-8v-1c2-2 6-3 9-3z" class="D"></path><path d="M495 841c-1 0 0 0-1-1 1-1 1-1 3-2 1 1 2 1 4 1-3 0-4 1-6 2z" class="i"></path><path d="M500 860c-1-3-2-6-1-9l1 1 1-1s0 1 1 1l-1 3v1h-1c-1 1 0 2 0 4z" class="L"></path><path d="M504 845c1 1 1 1 2 3l-1 1-3 3c-1 0-1-1-1-1l-1 1-1-1v-2c1-2 3-3 5-4z" class="X"></path><path d="M500 834c0 1 0 1 1 2 2 1 2 1 3 2s2 2 3 2c1 1 1 0 2 1h-4v-1c-2-1-3-1-4-1-2 0-3 0-4-1-2 1-2 1-3 2 1 1 0 1 1 1l-1 2-1-1h0c0-1 0-1-1-2-1 1-1 1-1 2 0 2 0 3 1 5 2 2 2 5 2 8-1-3-3-6-4-9s-1-4 1-8v-1c2-2 6-3 9-3z" class="G"></path><path d="M500 834c0 1 0 1 1 2 2 1 2 1 3 2s2 2 3 2c1 1 1 0 2 1h-4v-1c-2-1-3-1-4-1-2 0-3 0-4-1 1 0 2-1 2-2-2 0-6 2-8 2v-1c2-2 6-3 9-3z" class="l"></path><path d="M497 833h4l-1 1c-3 0-7 1-9 3v1c-2 4-2 5-1 8s3 6 4 9l6 16c2 2 2 6 3 9 1 2 1 6 2 8 1 1 0 4 1 6v3c1 1 1 3 1 5v9l-1 1v6h1v-2h1v-2h1v2l-2 10c-1-3-1-6-2-9v-4h-1l1-1c-1-1-1 0-1-1v-4-1c0-1 0-5-1-6 0-1-1-2-1-3l1 1v-2c-1-2-1-5-2-8h-1c0-2 0-4-1-5s-1-2-2-3v-1l-12-35 1-1c0-1 0-2 1-3v-1c2-3 4-4 7-5l3-1z" class="i"></path><path d="M494 859l-1 2-6-14v-1l1-1c2 5 5 9 6 14z" class="G"></path><path d="M497 833h4l-1 1c-3 0-7 1-9 3v1c-2 4-2 5-1 8s3 6 4 9l6 16c-3-4-3-8-6-12-1-5-4-9-6-14-1-2-1-3-1-5v-1c2-3 4-4 7-5l3-1z" class="j"></path><path d="M507 969l-1 1v1-1c1-10 0-20-1-30 0-5 0-10-1-14-2-11-7-22-10-33l-18-54-4-13c0-1-1-3-1-4v-1l3 2 2 1c1 0 2 0 2 1 0 2 1 3 2 4v1c1 2 2 3 2 5 2 3 1 6 3 9l12 35v1c1 1 1 2 2 3s1 3 1 5h1c1 3 1 6 2 8v2l-1-1c0 1 1 2 1 3 1 1 1 5 1 6v1 4c0 1 0 0 1 1l-1 1h1v4c1 3 1 6 2 9l2-10v-2l19-60 2 2c-6 19-13 39-17 59-2 6-3 12-4 18-2 12-1 24-2 36z" class="c"></path><g class="D"><path d="M474 823l2 1c1 0 2 0 2 1 0 2 1 3 2 4v1c1 2 2 3 2 5 2 3 1 6 3 9l12 35v1c1 1 1 2 2 3s1 3 1 5h1c1 3 1 6 2 8v2l-1-1c0 1 1 2 1 3 1 1 1 5 1 6v1h-1v-5c0-1-1-1-1-2l-1-1-1-3-12-33-1-1c0-3-3-6-3-10-1-4-3-9-5-14-1-4-2-9-4-13 0 0-1-1-1-2h0z"></path><path d="M474 662c1-1 1-4 1-5-1-1-1-1-1-2 0-2-1-3 0-5h1c0 1 1 2 2 2h0c1-1 2-1 3-1 0 1 1 3 2 4 2 3 1 3 4 4h0c3 2 6 3 8 6 0 1 0 1 1 2v2 1c0 1 1 2 1 3 3 5 4 10 5 16v2l17 43c1 0 1 1 2 1 1-1 3-2 4-3 1-3 4-6 7-8 1-1 3-3 3-5 0-1 0-3-1-4v-1l-1-1v-1-1c-1-1-1-2-1-3l7 20v1l5-19c3 2 5 3 7 6v1c1 2 1 4 2 6 1 3 2 4 4 6l1 2c3 3 5 7 8 11 0 1-1 2 0 3l1-1h3l-3 8c0 3-1 6-2 9l-1 1-1 1-1 1c1 1 1 0 2 0-1 3-1 4-3 6h-3l-1-1 1 2-1 1c-1 0-1 2-2 2l-1-2c-2 0-5 2-8 3h0l-4 2c-3 2-5 4-7 8h-1-1c-2 0-3 1-5 2-5 3-10 6-14 10-2 1-2 2-3 3l-2 1-10 26c1 1 3 2 3 3h2v2l-1 2-1-1h-4l-3 1c-3 1-5 2-7 5v1c-1 1-1 2-1 3l-1 1c-2-3-1-6-3-9 0-2-1-3-2-5v-1c-1-1-2-2-2-4 0-1-1-1-2-1l-2-1-3-2c-2-2-3-4-5-6l-7-15-1-2c-1-1-2-2-2-3l-1-1c-1 0-1 0-3 1l-2-3c-1-1-3-2-4-2h-2c-1 0-2 0-3 1v-1-1h-2c0-1-1-2-1-4l-2-4c-1-3-1-5-2-8s-2-6-4-9l-2-2c-1-2-1-4 0-6l-1-2 1-11 3 2v-14c-1-1-1-1-1-2 0-3-1-6-1-9h1l-1-2v-8c0-1 0-1 1-2 0-1 1-2 1-3 2-1 2 0 4 0 0 0 1 1 2 1l1 1h1l1-2c2-5 9-11 13-14 3-2 6-3 8-5v-3c1 0 1-1 1-2s0-2 1-2c0-2 1-3 3-3l2-3 5-4c2-2 4-3 5-6h0-1c-1 0-2 0-3-1z"></path></g><path d="M496 782h1l3 3h1l-2-3c2 2 4 4 5 7l-1 1c-1 0-2-2-3-3s-4-4-4-5z" class="i"></path><path d="M494 829l7 1h2v2l-1 2-1-1h-4l-4-2 1-2z" class="k"></path><path d="M488 773c1 0 2 1 3 1 0 1 1 1 2 2 2 1 4 3 6 6l2 3h-1l-3-3h-1c-3-2-6-6-8-8v-1z" class="l"></path><path d="M554 750c2 0 3 1 5 2v2l-2-2c-3 0-6 3-8 5l-4 4c0-1 0 0 1-1 2-4 4-9 8-10z" class="AD"></path><path d="M545 764v1c0 2-2 2-2 5-1 0-1 1-2 1-3 1-5 3-7 5h-1-1c1-4 5-5 8-7 2-1 3-3 5-5z" class="G"></path><path d="M541 775h0c5-4 9-5 15-6l1 2-1 1c-1 0-1 2-2 2l-1-2c-2 0-5 2-8 3h0l-4 2h0l1-1-1-1z" class="S"></path><path d="M478 668c1 1 1 1 2 1h0c0-1 0-1 1-2l2 2 1-1c1 1 1 1 1 2-2 0-2 0-4-1v2c-1 1-2 2-4 2h-1c-1 1-3 2-4 3h0v-5c1-1 2-1 3-2 1 0 2 0 3-1z" class="G"></path><path d="M485 667c2 1 3 2 4 4 0 0 1-1 1-2l1 3c0 4 2 8 2 13 0-1 0-1-1-2 0-1-1-2-2-4-2-2-4-6-5-9 0-1 0-1-1-2l1-1z" class="C"></path><path d="M493 669v-2c1 0 1 1 2 2v1c0 1 1 2 1 3 3 5 4 10 5 16v2l-8-22z" class="AX"></path><path d="M475 669l5-5v-1c1-1 3 0 4 1 3 2 5 3 6 5 0 1-1 2-1 2-1-2-2-3-4-4l-1 1-1 1-2-2c-1 1-1 1-1 2h0c-1 0-1 0-2-1-1 1-2 1-3 1z" class="X"></path><path d="M478 668l3-3c0-1 0 0 1-1 1 1 1 2 3 3h0l-1 1-1 1-2-2c-1 1-1 1-1 2h0c-1 0-1 0-2-1z" class="l"></path><path d="M476 820c3-1 5 0 8 1 4 2 6 6 10 8l-1 2-3-2c0-1-1-1-2-2s-5-4-7-4h-3-1 0l-1-3z" class="S"></path><path d="M478 823h3c2 0 6 3 7 4s2 1 2 2l3 2 4 2-3 1c0-1-2-2-3-2-2-1-4 0-6-1-1 0-2-2-2-2l-1-2-2 2c-1-1-2-2-2-4 0-1-1-1-2-1l1-1h1z" class="l"></path><path d="M478 823h3c2 0 6 3 7 4s2 1 2 2c-1 0-4-3-6-3h-2c-2 0-3-1-4-3z" class="C"></path><path d="M478 663c1-2-1-4 1-5 4 1 8 3 12 5v1 1h0v4l1 3h-1l-1-3c-1-2-3-3-6-5-1-1-3-2-4-1v1l-5 5c-1 1-2 1-3 2-2 1-3 2-4 4h0c-1 0-2 1-2 1l2-3 5-4c2-2 4-3 5-6z" class="E"></path><path d="M480 829l2-2 1 2s1 2 2 2c2 1 4 0 6 1 1 0 3 1 3 2-3 1-5 2-7 5v1c-1 1-1 2-1 3l-1 1c-2-3-1-6-3-9 0-2-1-3-2-5v-1z" class="L"></path><path d="M487 833h1l1 1v1l-2 1-1-1 1-2z" class="I"></path><path d="M480 829l2-2 1 2v1 3h0l-3-3v-1z" class="C"></path><path d="M484 713h0c2 7 16 44 15 48h-1l-13-38c0-2-1-4-1-5l-1-1v-2h0l1-2z" class="L"></path><path d="M456 795h1l2 3c4 7 10 17 16 22h1l1 3h0l-1 1-2-1-3-2c-2-2-3-4-5-6l-7-15-1-2c-1-1-2-2-2-3z" class="E"></path><path d="M474 662c1-1 1-4 1-5-1-1-1-1-1-2 0-2-1-3 0-5h1c0 1 1 2 2 2h0c1-1 2-1 3-1 0 1 1 3 2 4 2 3 1 3 4 4h0c3 2 6 3 8 6 0 1 0 1 1 2v2c-1-1-1-2-2-2v2h-2v-4h0v-1-1c-4-2-8-4-12-5-2 1 0 3-1 5h0-1c-1 0-2 0-3-1z" class="C"></path><path d="M541 775l1 1-1 1h0c-3 2-5 4-7 8h-1-1c-2 0-3 1-5 2-5 3-10 6-14 10-2 1-2 2-3 3l-2 1c3-9 13-13 20-17 4-3 8-7 13-9z" class="F"></path><path d="M488 773l-1-1 1-1h0l-4-2c0 1 1 1 2 2l-1 1c1 1 1 2 2 3l1 1c3 4 8 9 9 15v-1l-7-9c-5-6-9-11-12-18l6 5c8 5 17 10 21 19h0c1 2 0 5 0 7l-1 2c0-1 0-1 1-3l-1-3h-1l1-1c-1-3-3-5-5-7-2-3-4-5-6-6-1-1-2-1-2-2-1 0-2-1-3-1z" class="C"></path><path d="M550 717c1 2 1 4 2 6 1 3 2 4 4 6l1 2c0 2 2 4 3 7 1 2 4 4 3 7h0c-2 0-3 0-5-1 0-1-1-2-1-3-2-2-4-5-5-7-1-3-1-6-2-8 1-1 1-1 1-2l-1-2v-1-4z" class="L"></path><path d="M556 729l1 2c0 2 2 4 3 7 1 2 4 4 3 7-1 0-1-1-2-1-2-5-5-10-5-15z" class="C"></path><path d="M466 676s1-1 2-1h0c1-2 2-3 4-4v5h0v3-1l-6 6c-1 1-2 3-2 4-3 3-6 6-6 10l-1-1-1 3h0v-3c1-2 1-3 1-4s0-1 1-2v-1h-1l-3 1h-1c3-2 6-3 8-5v-3c1 0 1-1 1-2s0-2 1-2c0-2 1-3 3-3z" class="i"></path><path d="M473 768v-1c1-2 0-7 0-10l1 1c0 1 1 3 2 4s1 2 1 4h1c0 2 0 4-1 5v3c-1 5-4 10-6 15-1 2-3 3-3 5h-1-1l-1-1c-1-3 5-14 6-18 1-2 1-5 2-7z" class="c"></path><path d="M491 672h1c3 12 8 23 12 35l25 67-1 1-35-90c0-5-2-9-2-13z" class="r"></path><defs><linearGradient id="k" x1="541.465" y1="749.073" x2="521.697" y2="733.364" xlink:href="#B"><stop offset="0" stop-color="#6c6d6e"></stop><stop offset="1" stop-color="#828182"></stop></linearGradient></defs><path fill="url(#k)" d="M531 708l7 20v1l-7 38-13-33c1 0 1 1 2 1 1-1 3-2 4-3 1-3 4-6 7-8 1-1 3-3 3-5 0-1 0-3-1-4v-1l-1-1v-1-1c-1-1-1-2-1-3z"></path><defs><linearGradient id="l" x1="433.851" y1="764.132" x2="445.74" y2="759.24" xlink:href="#B"><stop offset="0" stop-color="#888485"></stop><stop offset="1" stop-color="#bcbab8"></stop></linearGradient></defs><path fill="url(#l)" d="M436 734c2 4 3 8 5 11l11 30c1 4 2 8 4 12 1 1 1 2 1 3l3 3h-1c-1-1-2-1-3-1l1 1c0 1 0 1 1 2s1 1 1 3l-2-3h-1l-1-1c-1 0-1 0-3 1l-2-3c-1-1-3-2-4-2h-2c-1 0-2 0-3 1v-1-1h-2c0-1-1-2-1-4l-2-4c-1-3-1-5-2-8s-2-6-4-9v-1c0-1-1-2-2-3l1-1v1h1 1c1 0 2-1 2-2 3-3 2-13 2-17 0-2 1-4 0-6h0l1-1z"></path><path d="M430 764v-1c0-1-1-2-2-3l1-1v1h1 1l1 1c3 4 4 9 5 15h1c5 2 10 4 14 8v1c-5-2-9-4-13-5h-1v4 1l-2-4c-1-3-1-5-2-8s-2-6-4-9z" class="c"></path><path d="M438 785v-1-4h1c4 1 8 3 13 5l5 5 3 3h-1c-1-1-2-1-3-1l1 1c0 1 0 1 1 2s1 1 1 3l-2-3h-1l-1-1c-1 0-1 0-3 1l-2-3c-1-1-3-2-4-2h-2c-1 0-2 0-3 1v-1-1h-2c0-1-1-2-1-4z" class="z"></path><path d="M454 691l3-1h1v1c-1 1-1 1-1 2s0 2-1 4v3c-2 6-1 14-1 20v-1c-1-2 0-6-1-8v7h-1-1v1c0 2-1 4 0 6l1 1-3-1v2h-2l-1 1h-1l-3 2-3 2h-4v2l-1 1h0c1 2 0 4 0 6 0 4 1 14-2 17 0 1-1 2-2 2h-1-1v-1l-1 1c1 1 2 2 2 3v1l-2-2c-1-2-1-4 0-6l-1-2 1-11 3 2v-14c-1-1-1-1-1-2 0-3-1-6-1-9h1l-1-2v-8c0-1 0-1 1-2 0-1 1-2 1-3 2-1 2 0 4 0 0 0 1 1 2 1l1 1h1l1-2c2-5 9-11 13-14h1z" class="I"></path><path d="M435 720l2 2v1l-2 2h-1v-4l1-1z" class="u"></path><path d="M434 718v2h-1c0-3 1-2 1-4v-3l1-1 3 3h0c-2 1-3 2-4 3z" class="D"></path><path d="M428 743l3 2-3 11-1-2 1-11z" class="S"></path><path d="M429 710h3c-2 6 1 14-1 21-1-1-1-1-1-2 0-3-1-6-1-9h1l-1-2v-8z" class="U"></path><path d="M432 737h2l1 4c0 4 1 14-2 17 0 1-1 2-2 2h-1c2-8 2-15 2-23z" class="j"></path><path d="M446 723v-1l1-1v2l1 1 4 1 1 1-3-1v2h-2l-1 1h-1l-3 2-3 2h-4v2l-1 1h0c1 2 0 4 0 6l-1-4h-2c0-1 0 0 1-2h0l-1-2c0-1 0-2 1-3 0-3 3-4 5-5 1-1 3-1 5-2h3z" class="C"></path><path d="M436 732l-1-2h0c1-2 2-3 4-4h0l1 2h-1l-1 1v1h2 0c1-2 3-2 5-2 0-1 0 0 1 0l-3 2-3 2h-4z" class="i"></path><path d="M446 723v1l-7 2c-2 1-3 2-4 4h0l1 2v2l-1 1h0c1 2 0 4 0 6l-1-4h-2c0-1 0 0 1-2h0l-1-2c0-1 0-2 1-3 0-3 3-4 5-5 1-1 3-1 5-2h3z" class="y"></path><path d="M429 710c0-1 0-1 1-2 0-1 1-2 1-3 2-1 2 0 4 0 0 0 1 1 2 1l1 1h1l1-2 6 6c2 2 3 4 4 7-1 1-1 2-2 3h-1l-1 1v1h-3c-2 1-4 1-5 2l-1-2v-1l-2-2-1-2c1-1 2-2 4-3h0l1-7-7 2h-3z" class="C"></path><path d="M442 715c1 2 1 4 0 6 0 1-1 1-1 1h-1v-1l1-2v-2c1 0 1-1 1-2z" class="D"></path><path d="M440 710l1-1v2l1 1v3c0 1 0 2-1 2v2l-1 2-1-2c0-3 0-6 1-9z" class="I"></path><path d="M443 709c2 4 4 8 4 12l-1 1v1h-3-1c1-1 1-1 2-1h0c1-2 0-7-1-9v-1-1c0-1-1-1 0-2z" class="L"></path><path d="M440 705l6 6c2 2 3 4 4 7-1 1-1 2-2 3h-1c0-4-2-8-4-12h0c-1-1-2-1-3-2h-1l1-2z" class="AH"></path><path d="M429 710c0-1 0-1 1-2 0-1 1-2 1-3 2-1 2 0 4 0 0 0 1 1 2 1l1 1h1 1c0 1-1 2 0 3-1 3-1 6-1 9v3l-1 1v-8h0l1-7-7 2h-3z" class="F"></path><path d="M454 691l3-1h1v1c-1 1-1 1-1 2s0 2-1 4v3c-2 6-1 14-1 20v-1c-1-2 0-6-1-8v7h-1-1v1c0 2-1 4 0 6l-4-1-1-1v-2h1c1-1 1-2 2-3-1-3-2-5-4-7l-6-6c2-5 9-11 13-14h1z" class="I"></path><path d="M440 705c2-5 9-11 13-14h1c-2 2-6 5-7 7l2 1-3 6v6l-6-6z" class="G"></path><path d="M446 705l-1-1c0-2 1-4 2-5h2l-3 6z" class="i"></path><path d="M456 700h0l1-3 1 1c1 5 1 12 2 17 2 13 6 25 12 37v2h0v1l1 1h0c-1 2-1 5 0 7v5c-1 2-1 5-2 7-1 4-7 15-6 18l1 1h1c0 1-1 1-1 2h0v3c-1-1-3-2-3-3-1-2 0-4 0-6-3-7-3-17-6-25v-2l-2-7v-1c-1-3-2-5-3-7v-1l-2-4c-1-1-1 0-1-1v-1c-1-1-1-2-2-3v-2c-1-2-2-4-3-5l-1-1 3-2h1l1-1h2v-2l3 1-1-1c-1-2 0-4 0-6v-1h1 1v-7c1 2 0 6 1 8v1c0-6-1-14 1-20z" class="L"></path><path d="M452 725c-1-2 0-4 0-6v-1h1 1v-7c1 2 0 6 1 8v1c0 3 0 6 1 8 2 8 6 16 8 24l3 14c0 3 1 8 0 11v1c0 2 0 0-1 2v5h-1v-1l-1-1c-4-4-6-23-8-30-3-9-6-17-9-25l1-1h2v-2l3 1-1-1z" class="D"></path><path d="M448 727h2v-2l3 1h-1c0 2 1 4 2 5s1 3 2 5c2 6 4 13 6 20 1 5 3 10 3 15h-1c0-2 0-4-1-6-2-8-8-34-15-38z" class="I"></path><path d="M598 96h21 37 108 60c3 0 8 0 10-1 1 0 2-2 4-2l-3 2-1 1v1l-3 3c-2 2-5 3-8 5-9 5-14 9-20 19h0c-2 6-6 12-10 18h3l-14 14v2l-1 1c-2 3-2 5-3 9-1 1-2 1-3 2-2 4-3 9-5 13-1 2-2 5-4 8-1 3-1 6-2 9-1 1-2 3-2 4 0 2 1 5 2 8v3c1 1 1 2 1 4h0c-2 2-2 3-3 5 0 1 0 2-1 3v2h-1v2l-1 2v1h-1c0 1-1 2-1 4l-2 4c0 1 0 1-1 2 0 1 0 2-1 3h1c1 0 2-1 2-2 1 1 1 1 0 2v1l-1 1v2l-12 30-2 8c-1 2-1 3-2 5 0 1 0 1-1 2h-1c-1 1-2 2-2 3-1 3-2 5-4 7l1 1 1-1-1 2c1 1 1 1 1 3-1 2-2 2-4 3v1c0 3-4 8-5 11-1 2-4 9-3 10l-5 5h0c1 1 1 0 1 1v1c-2 0-3 1-4 2l-4 8c1 0 0 1 0 1h0-1l1-1v-3l-1-1c-1 1-2 1-3 1-1 1-1 1-2 1h0c-2 0-3 0-4 1h0c-1 0-2 0-2 1l-1 1v1c-1 3-2 7-1 9 0 3 0 6-1 9-2 3-4 5-6 8s-2 6-5 8v1l-4 3-3 2-1-1-5 7-3 4c-3 2-6 6-9 8 0 1-1 2-1 4-3 1-5 3-8 3l-1-1 1-2 24-70 2-4 1-3 6-16 5-18 1-2c1-1 1-3 2-4l3-10 1-3c0-3 2-7 3-10s2-7 3-10c5-14 8-28 10-43l1-3c3-23 1-46-8-67l-3-4c-18-30-67-43-99-51 1 0 0 0 1-1-1-1-3 0-4-2z" class="c"></path><path d="M797 130h-1c-4-3-5-7-6-12l-1-1c2 2 3 4 4 6v1l-1 1c1 1 3 4 4 5h1z" class="I"></path><path d="M793 124l5 3c0 1-1 2-1 3h-1c-1-1-3-4-4-5l1-1z" class="C"></path><path d="M733 295h0c0-1 1-2 2-3v2h3 1c0 1 0 1-1 2h-1c-1 1-2 2-2 3-1-1-1-2-2-4z" class="D"></path><path d="M733 295c1 2 1 3 2 4-1 3-2 5-4 7h-1v-3-1l2-3 1-4z" class="L"></path><path d="M649 417h0c3 0 7-2 9-4 0 1-1 2-1 4-3 1-5 3-8 3l-1-1 1-2z" class="q"></path><path d="M675 394c2-4 0-8-2-12 3-2 6-3 8-5h0 1c-2 2-5 4-7 6v3l2 4c2 0 2 0 4-1l2 1-4 3-3 2-1-1zm73-157c1-3 3-5 5-7-2 5-4 9-6 14-3 7-5 14-8 20-3 3-5 7-9 9 0 1 0 1-1 2v-1c0-1 1-2 2-2 2-3 3-5 5-8 2-4 5-7 6-12v-1c4-2 4-10 6-13v-1z" class="AI"></path><path d="M739 264c-2 3-3 7-4 10s-1 6-2 9c0 2-2 4-3 6-1 1-1 2-2 3-1-1-1-1-2-1l1-2c1 0 1-1 1-2 2-4 3-8 1-12 1-1 1-1 1-2 4-2 6-6 9-9z" class="C"></path><path d="M689 374v-2c1-2 1-5 1-7 0-8 3-16 5-23 1-3 2-6 3-8v-1c1-2 1-4 3-6-1 10-5 21-7 31-1 3-1 7-3 11 0 2-1 4-2 5z" class="I"></path><path d="M701 327c0-1 1-3 2-4l10-13c1-2 3-5 6-7 2-2 5-3 8-5-1-3-3-6-4-9 2 0 2 3 4 4h2v1h-1c0 1 1 2 1 3s-2 3-3 4c-2 1-3 2-5 3l-1 1c-1 1-2 3-3 4v-1-1c-3 3-5 6-7 10-2 3-5 5-6 8-2 3-2 8-3 11l-4 13 14-6c1 0 2-1 3-1l2-1h0c1 1 1 0 1 1v1c-2 0-3 1-4 2l-4 8c1 0 0 1 0 1h0-1l1-1v-3l-1-1c-1 1-2 1-3 1-1 1-1 1-2 1h0c-2 0-3 0-4 1h0c-1 0-2 0-2 1l-1 1v1c-1 3-2 7-1 9 0 3 0 6-1 9-2 3-4 5-6 8s-2 6-5 8v1l-2-1c3-3 3-6 4-10 1-1 3-3 4-5 1-1 2-3 2-5 2-4 2-8 3-11 2-10 6-21 7-31z" class="p"></path><path d="M697 353c2-5 10-6 14-8 0 2-1 5-2 7v1c1 0 0 1 0 1h0-1l1-1v-3l-1-1c-1 1-2 1-3 1-1 1-1 1-2 1h0c-2 0-3 0-4 1h0c-1 0-2 0-2 1zm23-48c0 2 0 2-1 4s-1 4-2 6c0 2-1 5-2 7-1 4-1 8-3 11 0 3-2 6-2 9l1 1-14 6 4-13c1-3 1-8 3-11 1-3 4-5 6-8 2-4 4-7 7-10v1 1c1-1 2-3 3-4z" class="D"></path><path d="M703 344l-2 2h-1c1-2 1-3 2-5h2l-1 3z" class="u"></path><path d="M710 342l-1 1v-2c1-7 4-12 5-19v-1 2c1 0 0 0 1-1-1 4-1 8-3 11 0 3-2 6-2 9z" class="I"></path><path d="M704 341h0c1-3 2-6 2-9 1-2 1-5 2-7 2-4 5-8 6-12v-1h1c0-2 1-3 2-4v1c-1 3-2 4-3 7-2 6-5 12-7 19-1 3-1 6-3 10l-1-1 1-3z" class="C"></path><path d="M726 301h2c1-2 3-3 4-5l-2-4c0-1 0 0 1 0v1l1 2v4l-2 3v1 3h1l1 1 1-1-1 2c1 1 1 1 1 3-1 2-2 2-4 3v1c0 3-4 8-5 11-1 2-4 9-3 10l-5 5-2 1c-1 0-2 1-3 1l-1-1c0-3 2-6 2-9 2-3 2-7 3-11 1-2 2-5 2-7 1-2 1-4 2-6s1-2 1-4l1-1c2-1 3-2 5-3z" class="u"></path><path d="M730 302v1 3h1l1 1 1-1-1 2c1 1 1 1 1 3-1 2-2 2-4 3v1l-2 3c0 1-1 1-1 2l-1-1 1-1-1-1h-1-1l1-2 3-6c1-2 2-4 3-7z" class="G"></path><path d="M732 308c1 1 1 1 1 3-1 2-2 2-4 3v1l-2 3v-1c0-2 3-7 5-9z" class="AE"></path><path d="M729 315c0 3-4 8-5 11-1 2-4 9-3 10l-5 5-2 1c0-1 1-2 1-3 1-3 2-5 3-8 0-1 0 0 1-1l1-5c1-1 1-2 1-3h1c0-2 1-3 1-5h1 1l1 1-1 1 1 1c0-1 1-1 1-2l2-3z" class="I"></path><path d="M665 100h10l27 1 81-1 27 1h12c1 0 3-1 4-1h2 0l-1 1c-1 0-2 0-3 1h0-1c-2 1-4 0-6 1h-12-10l-29 1-38-1h-39c-6 0-12-1-18-2-2 0-4 0-6-1z" class="l"></path><defs><linearGradient id="m" x1="668.546" y1="107.428" x2="661.467" y2="143.558" xlink:href="#B"><stop offset="0" stop-color="#7c7c7e"></stop><stop offset="1" stop-color="#a3a2a1"></stop></linearGradient></defs><path fill="url(#m)" d="M602 98c12 2 25 2 37 3 4 1 8 1 12 2l11 3c15 4 33 9 45 18 10 6 15 16 19 27-6-2-12-4-18-1-2 1-4 2-5 4l-3-4c-18-30-67-43-99-51 1 0 0 0 1-1z"></path><path d="M651 103l11 3v2l-8-3h0c-1 0-2-1-3-2z" class="j"></path><path d="M662 106c15 4 33 9 45 18-1 0-2-1-3-1-1-1-2-1-3-1-12-6-26-12-39-14v-2z" class="r"></path><path d="M730 145c0-3 0-6 1-9 3-7 11-11 18-14 9-4 22-5 32-1h1c2 1 4 2 6 4l6 6h1c1 0 1 1 3 1h0c0-1 1-2 1-2 1-2 1 0 1-2 0-1 0 0 1-1v-1c1-1 1-2 2-2-2 6-6 12-10 18h3l-14 14v2l-1 1c-2 3-2 5-3 9-1 1-2 1-3 2-2 4-3 9-5 13-1 2-2 5-4 8-1 3-1 6-2 9-1 1-2 3-2 4 0 2 1 5 2 8-1-1-1-3-2-4-1 1-1 1-1 2 0 2-1 5-2 7-2 4-3 8-6 12h0c-3 1-4 3-5 5l-1 2 1 1v1c-2 3-2 11-6 13v1c-1 5-4 8-6 12-2 3-3 5-5 8v-4c0-5 1-10 1-15 0-3 0-7 1-11 1-3 1-7 1-11 1-8 2-17 2-25 0-7 0-13-1-19 0-2 1-4 0-6s0-5-1-8v-8l-3-13c-1-3-1-5-1-7z" class="I"></path><path d="M782 137h0c1 2 2 4 2 7h-1c-1-2-2-5-1-7z" class="c"></path><path d="M759 125c2 0 4 2 6 3v2c-1 0-5-2-6-3l-1-1 1-1z" class="G"></path><path d="M731 152c0-2 0-4 1-5 1 0 1-1 2-2 0 7 2 13 1 19h0v-7c-1 0-1 0-1 1-1 0-1 0-1 1h0v2l1 2v2h0l-3-13z" class="l"></path><path d="M773 155c1 2 1 3 0 5v3c1-1 2-2 3-2l1-1v-2h1 0c-2 5-5 11-6 16 0 1-1 1-1 2v1c-1 0-2-1-2-1 0-2 0-3 1-5l1-4v-3l1-2v-4c1-1 1-2 1-3z" class="G"></path><path d="M793 142h3l-14 14v2l-1 1c-2 3-2 5-3 9-1 1-2 1-3 2-2 4-3 9-5 13-1 2-2 5-4 8-1 3-1 6-2 9-1 1-2 3-2 4 0 2 1 5 2 8-1-1-1-3-2-4-1 1-1 1-1 2 0 2-1 5-2 7-2 4-3 8-6 12h0c-3 1-4 3-5 5l-1 2-5 13c2-6 4-12 7-18 1-2 3-4 4-7 1-2 1-4 2-6 2-4 4-9 5-13 0-4 1-9 2-13s4-8 5-12l1-1h0c-1 5-4 9-5 13-2 4-2 8-3 13h1c2-3 3-10 4-13 2-5 6-11 6-15v-1c0-1 1-1 1-2 1-5 4-11 6-16l15-16z" class="F"></path><path d="M782 156v2l-1 1c-2 3-2 5-3 9-1 1-2 1-3 2 1-5 3-9 7-14z" class="i"></path><path d="M770 171c-1-1-1-1-1-3 1-2 1-3 1-5 1-3 1-5 1-8l-1-1c0-3-1-6-3-9s-4-5-8-5-8 1-11 4c-7 6-9 20-5 28 1 2 4 4 5 6 0 2-1 5-1 7-2-4-6-9-8-14l-1-3h0c-1-1 0-3 0-4-1-7 0-13 2-19h0c1-1 1-2 1-2 3-4 8-8 13-8l1-1h2c5 0 9 3 12 7 1 1 2 2 3 4h0v4c1 2 0 4 1 6h0c0 1 0 2-1 3v4l-1 2v3l-1 4z" class="u"></path><defs><linearGradient id="n" x1="746.873" y1="120.17" x2="777.548" y2="152.63" xlink:href="#B"><stop offset="0" stop-color="#989696"></stop><stop offset="1" stop-color="#c5c4c1"></stop></linearGradient></defs><path fill="url(#n)" d="M730 145c0-3 0-6 1-9 3-7 11-11 18-14 9-4 22-5 32-1h1c2 1 4 2 6 4l6 6h1c1 1 1 2 2 3-2 2-2 5-4 6h-1c-1-3-1-4-3-6l-5-5c-5-3-13-4-19-5h-8c1 1 0 1 2 1l-1 1c-7 0-14 5-20 10v1h0c-2 3-3 5-4 8-1 1-1 2-2 2-1 1-1 3-1 5-1-3-1-5-1-7z"></path><path d="M788 125l6 6v1h-1c-1 0-1 0-2-1v-1c-2-1-3-3-3-5z" class="i"></path><path d="M730 145l1-1c3-3 4-8 7-11 1-2 2-2 3-3-1 2-3 4-5 7h0 1 1c-2 3-3 5-4 8-1 1-1 2-2 2-1 1-1 3-1 5-1-3-1-5-1-7z" class="X"></path><path d="M741 130c5-3 10-6 15-7h1c2-1 3-1 5 0 1 0 2 1 3 1h-8c1 1 0 1 2 1l-1 1c-7 0-14 5-20 10v1h0-1-1 0c2-3 4-5 5-7z" class="C"></path><path d="M296 380l1-1c3 3 5 6 7 10h1v-1l4 9c1 1 1 2 2 3v1c2 1 2 2 3 4 0 0 0-1 1-1l17 45h0l1 2c-1 0-2 0-3 1 1 1 2 2 3 2 0 1-1 1 0 3 0 0 1 1 1 2l-1 3c-1 2-2 5-2 7 0 1 0 1 1 2v2 8c0 1 0 3 1 4v1c0-1 0-4 1-5 0-1 1-2 2-3 1-2 0-2 1-4v-4h1v-1h1 1v1l1 3c0 1 1 2 1 3 1 3 3 7 4 10 2 2 3 5 4 8l17 47 6 13c0 2 0 3 2 4v1l-1 1 18 46-2 2c0 1 0 3-1 4l1 1c1 4 0 17 2 19 1-2 0-5 0-8 0-4-1-10 1-13v-1c1-3 4-5 6-7 1-1 3-2 3-3 3-2 6-3 8-6 0-1 1-2 1-4h1 2c2-1 6-3 7-5v-1c5 0 8 1 13 4h0c-2 0-5-2-7-2-1-1-1-1-2-1l1 1c2 0 5 5 7 7 2 4 3 9 5 13 6 13 11 26 16 40 2 5 4 10 4 15l1 1h2 0c-1 6-2 12-1 18l1 3v3c-2 2-5 3-8 5-4 3-11 9-13 14l-1 2h-1l-1-1c-1 0-2-1-2-1-2 0-2-1-4 0 0 1-1 2-1 3-1 1-1 1-1 2v8l1 2h-1c0 3 1 6 1 9 0 1 0 1 1 2v14l-3-2-1 11-7-16-2-7-3-8-1-3-3-1-7-3c-3-1-5-2-7-5-2-2-4-3-6-5v1l-1-1c-1 0-3-1-4-1-1-1-2-2-4-3l-2 1h-1-1c-2-1-3-3-5-4l-1-1c1-1 1-2 1-3l-4-1h-2-2-1l-1 1c-1-1-1-1-2-1l1-1-1-1 1-1h2l1-1-3-3c1 0 1-1 2-2l-2-3v-1l-1-4-2-4h1v-2l-4-7h0l-5-6-1-1c-1-1-2-1-2-2l-2-2c-2-1-3-2-4-3l-1-1 1-1v-2l-2-1 2-1 2 1c-3-3-9-6-12-7l-2-1h-2c0-2 1-4 2-5 1-3 3-5 5-7l2-1v-1c1-2 3-4 6-6-2 0-5 1-6 1l-3-5h0c0 3 1 5 1 7-1 0-2 1-4 1h-2l-1-1c-1 0-2 0-3-1l-1-1c-1 0-2-1-2-2v-1h-1c-1-2-1-3-3-4l-4-11c0-3-1-5-2-7s-1-3-2-5v-1l-2-5-1-2v-2l-1-2h0l-1-5-2-6c-1 1-1 1-1 3-1-2-2-4-2-6v4c-1 1-3 0-4 0-1-1-1-2-1-3l-1-1c0-2-1-3-2-5h0c0-1-1-2-1-4h1l-1-1-1-1c0-1 0-2-1-3h-1v-2l-4-2c-3-1-3-4-7-4-1-2-2-2-4-3h-2c-3 1-7 1-11 1h-3c-1 0-3-1-4-1 2-2 6-2 8-3v-1s1 0 2-1h3c2-1 2-1 3-3 0 0 1 0 2-1 3 0 5-3 7-5l1 1h1l1-3 1 1c1-3 2-6 2-9l2-9c1-1 1-2 1-3 1-2 1-3 1-5 0 0 1-1 1-2v-2c1-1 1-2 1-3h-1c1-2 1-3 1-4v-2c-1-1 0-3 0-5v1c1 2 1 4 1 6v2c1 1 2 1 3 1h1 0 2 1l2-2c-1-2-1-4 0-5l-1-2-3-6c0-3 0-5-1-8l-1-2c0-1 0-1 1-2v-1c-1-2-1-3-1-5s0-5-1-7v-2c-2-4 2-20 3-24 0-2 0-3 1-4h2l-2-7 1-2c2-4 4-6 5-10l1 2h1l1 1 1 1h0c0-2 0-4-1-5-1-7-6-13-10-18z" class="AK"></path><path d="M375 635l-1 2c-1 1-2 1-2 2h-1c1-2 1-3 2-5h1l1 1z" class="B"></path><path d="M374 634h1l1-1c-1-1-1-1-2-1 1-1 2-1 3-2l1 1c0 2-1 3-3 4h0l-1-1z" class="M"></path><path d="M326 541c1 1 2 2 3 4v2l-3-2c-1 2-1 3-1 4-1-3 0-5 1-8z" class="B"></path><path d="M339 635c1-3 0-4-1-6 0-2 0-2 1-3 1 2 3 5 4 7-1 1-2 1-3 1l-1 1z" class="N"></path><path d="M334 557v-1c1-2 2-4 4-5 1 0 2 1 3 2-3 0-3 1-4 3-2 1-2 4-2 6l-1-5z" class="J"></path><path d="M338 549l2 2 5 7h0c-1 0-2-1-3-2 0-1-1-2-1-3-1-1-2-2-3-2-2 1-3 3-4 5v1l-1-3c1-1 1-2 3-3 1-1 1 0 2-1v-1z" class="M"></path><path d="M293 446c1 1 1 3 1 5l1 1v1h1c1 0 1 0 2-1v4 1c-1 1-1 1-2 0h-1l-1-2c0-2 0-5-1-7v-2z" class="N"></path><path d="M335 625l2-1c0 2-1 2-3 4-1 1-1 3-1 4h-1c-1 1-3 3-3 4l1 1h-2c0-2 1-4 2-5 1-3 3-5 5-7z" class="T"></path><path d="M286 504c2 1 2 1 3 3v1c1 2 1 2 2 3v1c0 1 1 1 2 2-2 0-3 0-4-1s-1-3-1-4c0-2 0-2-1-3v1h0c0 2-1 5-1 8l-2-2 2-9z" class="J"></path><path d="M339 626h0c2 1 3 3 4 5 2 3 4 6 7 8 0 2 0 3-1 4l-3-1h0l-1-2v-1s1 0 1-1c0-2-2-4-3-5-1-2-3-5-4-7z" class="K"></path><path d="M343 633c1 1 3 3 3 5 0 1-1 1-1 1v1h-2c-1 0-3-1-4-2s-1-1-2-1c1-1 2 0 3-1l-1-1 1-1c1 0 2 0 3-1z" class="h"></path><path d="M339 638c1 0 2-1 3-1l2 2h1v1h-2c-1 0-3-1-4-2z" class="Q"></path><path d="M308 476c-1-3-1-5-2-8 0-2 0-3-1-5l2 1c4 2 4 4 5 8l-1-2-1 1h0c-1 2-1 4-2 5z" class="J"></path><path d="M346 642l3 1c1-1 1-2 1-4l11 14c-2 0-3-2-5-4-1 0-2 0-3-1 0-1-1-1-2-2h-1 0c-2-1-3-3-4-4z" class="Q"></path><path d="M378 679l2-1 8 9 3 3s-1 1-2 1c0-1-1-1-2-1-2-1-2-3-4-4s-2-2-4-4h1l-2-3z" class="h"></path><path d="M298 452c0-1 0-2 1-4 0-2 0-6 1-7 1 2 0 5 0 7 0 3 0 8-1 11l-1 1h0v6c-1 0-1-1-1-2-1 0-1-1-2-1v-1-1-1c-1-2-1-3-1-5l1 2h1c1 1 1 1 2 0v-1-4z" class="x"></path><path d="M295 461v1 1c1 0 1 1 2 1 0 1 0 2 1 2v-6h0l1-1v12h0l1 7-1 1-3-6c0-3 0-5-1-8l-1-2c0-1 0-1 1-2z" class="R"></path><path d="M299 479l1-1-1-7h0c1 4 2 7 5 10 2 1 4 3 7 4 2 1 3 2 5 2 2 1 5 2 6 3-5 1-18-6-22-9l-1-2z" class="v"></path><path d="M329 528c4 5 8 12 11 18 1 1 1 1 1 3l-1 2-2-2h-1c0-4-3-6-4-10 0-3-2-5-3-7l-1-4z" class="B"></path><path d="M334 575v1c0-5-3-11-4-15h1c0 2 1 4 2 6 1 3 2 5 2 8 2 4 2 7 5 11h-1v3h0c1 2 1 4 1 6-2-2-6-17-6-20z" class="J"></path><path d="M329 542l1 1 1 3-1 2v1c0 1-2 3-2 4v4l1 1-1 2c-1-1-2-3-3-4 0-2-2-4-2-6v-2-1h0c0 1 0 1 1 2v2h1v-2c0-1 0-2 1-4l3 2v-2-3z" class="T"></path><path d="M329 542l1 1 1 3-1 2v1c0 1-2 3-2 4v4c-1 0-1-1-1-2s0-2 1-4c1-1 0-1 0-2s1-1 1-2v-2-3z" class="M"></path><path d="M308 476c1-1 1-3 2-5h0l1-1 1 2c1 6 2 11 4 15-2 0-3-1-5-2 0-1-1-2-1-4-1-1-1-3-2-5z" class="T"></path><path d="M391 690c4 3 7 6 10 9 1 0 1 1 1 2h1c1 2 2 3 3 4v1c-2-1-3-1-3-1h-2v-1l-5-5s0-1-1-1c-1-1-2-1-2-2-1-2-2-3-4-5 1 0 2-1 2-1z" class="R"></path><path d="M330 532c1 2 3 4 3 7 1 4 4 6 4 10-1 0-2 1-4 1l1-1c0-2 0-2-1-3h0-2l-1-3-1-1c0-2 0-4-1-6v-2l1 1c1-1 1-2 1-3z" class="f"></path><path d="M329 542c0-2 0-4-1-6v-2l1 1c1 3 2 6 4 9v2h0-2l-1-3-1-1z" class="g"></path><path d="M330 543h1 1l1 1v2h0-2l-1-3z" class="Y"></path><path d="M389 643c1-1 1-1 3-1 1 3 1 7 1 10 0 2 1 5 3 6 1 2 3 1 3 2 0 2-1 3-2 3-1 1-2 1-3 0-4-2-7-6-8-10 1 0 1-1 2-1 0 1 2 4 3 6v1c1 2 5 3 7 4-1-2-3-4-4-6-3-4-4-9-5-14z" class="G"></path><path d="M333 546c1 1 1 1 1 3l-1 1c2 0 3-1 4-1h1v1c-1 1-1 0-2 1-2 1-2 2-3 3-1 3-1 4-1 7 1 2 1 4 1 6-1-2-2-4-2-6h-1c1 4 4 10 4 15v-1l-6-15 1-2-1-1v-4c0-1 2-3 2-4v-1l1-2h2 0z" class="V"></path><path d="M330 549h2v3c0 2-2 5-3 6l-1-1v-4c0-1 2-3 2-4z" class="Y"></path><path d="M331 469c0 1 0 1 1 2v2 8c0 1 0 3 1 4l-1 1v9 9l-1-1-1-1h0c-1 1-1 2-2 3l-1-1-1 1c0 2 1 3 2 6v2l2 2v1c0 1 1 1 1 2v2 1c1 1 1 2 1 3h0c-1 0-1-1-1-1l-1-1c0-1 0-1-1-2l-1-6c-1-1-2-2-2-4l-1-1-1-1v-2-1-1l1 1 4-6c3-3 2-12 1-16 0-4-1-10 1-14z" class="B"></path><path d="M332 471v2 8c0 1 0 3 1 4l-1 1c-1-5-3-11 0-15z" class="I"></path><path d="M333 554l1 3 1 5c0 2 1 5 2 7 0 2 1 3 2 5s1 5 3 8c1 3 3 6 4 9-1-1-2-3-2-4h-1v2l1 2c-1 0-1-1-1-2v-1l-1-1c-1 1-2 1-3 2h0v-3h1c-3-4-3-7-5-11 0-3-1-5-2-8 0-2 0-4-1-6 0-3 0-4 1-7z" class="T"></path><path d="M364 656l6 4 12-6c-1 2-3 4-5 5-2 2-4 3-6 4-3 2-6 3-6 6-1-1-1-1-2-1l-1-1h-1c0 1-1 2-1 4l-4-7h0c1 0 1-1 2 0h1c0-2 1-2 2-3 0 0-1-1-1-2h-1l1-2c2-1 2 0 4 1v-1h0v-1z" class="O"></path><path d="M356 664c1 0 1-1 2 0h1c0-2 1-2 2-3l1 1c-1 1-1 1-1 2l-2 3h2c0 1-1 2-1 4l-4-7h0z" class="H"></path><path d="M360 657c2-1 2 0 4 1s3 1 4 2c-1 1-2 1-3 1-2 0-3-1-5-2h-1l1-2z" class="h"></path><defs><linearGradient id="o" x1="314.587" y1="517.023" x2="328.353" y2="516.413" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#302e2f"></stop></linearGradient></defs><path fill="url(#o)" d="M326 541c-1-3-3-9-6-10h0c-1-1-1-1-2-3l-1-1h-1v-2h-2v-1h2c1 1 1 2 3 3v-8c-1-10-4-20-7-30l3 4c2 1 2 3 3 4 2 7 4 15 3 22 1 1 2 1 3 1v-2h1c1 4 2 6 4 10l1 4c0 1 0 2-1 3l-1-1v2c1 2 1 4 1 6v3c-1-2-2-3-3-4z"></path><path d="M332 638c0-1 0-2 1-3l2 2h-1l1 1h1l1-1c1 0 1 0 2 1s3 2 4 2h2l1 2h0c1 1 2 3 4 4h0 1c1 1 2 1 2 2 1 1 2 1 3 1 2 2 3 4 5 4l3 3v1h0v1c-2-1-2-2-4-1l-1 2h1c0 1 1 2 1 2-1 1-2 1-2 3h-1c-1-1-1 0-2 0l-5-6-1-1c-1-1-2-1-2-2l-2-2c-2-1-3-2-4-3l-1-1 1-1v-2l-2-1 2-1 2 1c-3-3-9-6-12-7z" class="k"></path><path d="M342 644l2 1c1 1 2 2 3 2 1 1 1 2 2 3s0 0 2 0l1 2s1 1 2 1v1c1 0 1 0 2 1 1 0 1 1 2 1-3 1-7-2-9-3l-1 2-2-2c-2-1-3-2-4-3l-1-1 1-1v-2l-2-1 2-1z" class="d"></path><path d="M342 646l3 3-3 1-1-1 1-1v-2z" class="x"></path><path d="M345 649l4 4-1 2-2-2c-2-1-3-2-4-3l3-1z" class="m"></path><path d="M349 653c2 1 6 4 9 3l1 1h1l-1 2h1c0 1 1 2 1 2-1 1-2 1-2 3h-1c-1-1-1 0-2 0l-5-6-1-1c-1-1-2-1-2-2l1-2z" class="AA"></path><path d="M350 657c3 0 4 1 6 4 0 0 1 1 2 1s1 0 2-1h-1l1-2c0 1 1 2 1 2-1 1-2 1-2 3h-1c-1-1-1 0-2 0l-5-6-1-1z" class="Q"></path><path d="M387 690c1 0 2 0 2 1 2 2 3 3 4 5 0 1 1 1 2 2 1 0 1 1 1 1l5 5v1h2s1 0 3 1v-1c-1-1-2-2-3-4h-1l3 2 1 1c1 0 1 0 2-1h0l1 2 5 15-3-1-7-3c-3-1-5-2-7-5-2-2-4-3-6-5v1l-1-1c-1 0-3-1-4-1-1-1-2-2-4-3l-2-1c0-1 1-2 1-2 2 0 4 1 6 1l1-1-1-1c-1-2-1-3-1-5h1 1l-1-3z" class="W"></path><path d="M399 705c-3-2-6-6-9-9 2 0 4 2 6 3l5 5-1 1h-1z" class="N"></path><path d="M380 701c0-1 1-2 1-2 2 0 4 1 6 1l1-1c1 1 1 2 2 3v1h-2l2 2 1 1v1l-1-1c-1 0-3-1-4-1-1-1-2-2-4-3l-2-1z" class="d"></path><path d="M390 702v1h-2l2 2 1 1v1l-1-1c-1-1-2-2-4-2v-1h0c1-1 2 0 4-1z" class="a"></path><path d="M402 701l3 2 1 1c1 0 1 0 2-1h0l1 2 5 15-3-1-7-3c-1-1-2-3-3-4-2-1-3-3-5-4h0-1v-2h3l3 2 1-1-3-2h1l1-1v1h2s1 0 3 1v-1c-1-1-2-2-3-4h-1z" class="J"></path><path d="M402 707h0c1 0 2 1 3 1 1 1 2 1 3 3h-1c-3-1-4-2-6-3l1-1z" class="N"></path><path d="M290 478v1c1 2 1 4 1 6v2c1 1 2 1 3 1h1 0 2 1l2-2c0 7-1 29 3 34 3 2 6 3 9 4h-2-1c-1 0-1 0-2-1h-1l-1-1h-1c-2 0-4-2-6-3h-1l-3-3-1-2c-1-1-2-1-2-2v-1c-1-1-1-1-2-3v-1c-1-2-1-2-3-3 1-1 1-2 1-3 1-2 1-3 1-5 0 0 1-1 1-2v-2c1-1 1-2 1-3h-1c1-2 1-3 1-4v-2c-1-1 0-3 0-5z" class="Q"></path><path d="M290 478v1c1 2 1 4 1 6v2c1 1 2 1 3 1h1 0c0 8 0 16 1 24v3 1h0l-1-1-1 1-1-2c-1-1-2-1-2-2v-1c-1-1-1-1-2-3v-1c-1-2-1-2-3-3 1-1 1-2 1-3 1-2 1-3 1-5 0 0 1-1 1-2v-2c1-1 1-2 1-3h-1c1-2 1-3 1-4v-2c-1-1 0-3 0-5z" class="h"></path><path d="M365 669c0-3 3-4 6-6v3c3 4 5 8 9 12l-2 1 2 3h-1c2 2 2 3 4 4s2 3 4 4l1 3h-1-1c0 2 0 3 1 5l1 1-1 1c-2 0-4-1-6-1 0 0-1 1-1 2l2 1-2 1h-1-1c-2-1-3-3-5-4l-1-1c1-1 1-2 1-3l-4-1h-2-2-1l-1 1c-1-1-1-1-2-1l1-1-1-1 1-1h2l1-1-3-3c1 0 1-1 2-2l-2-3v-1l-1-4-2-4h1v-2c0-2 1-3 1-4h1l1 1c1 0 1 0 2 1z" class="O"></path><path d="M371 673c1 3 2 5 3 7h0l-2 2v1c-1-2-2-7-1-10z" class="E"></path><path d="M364 673l1 1s1 0 1 1h1l1-3c0 3 0 6 1 9v3 1c-2-1-1-4-2-5-1 0-1 0-2-1l-1-6z" class="R"></path><path d="M365 669c0-3 3-4 6-6v3 1c-1 1-2 2-2 3-1 0-1 2-1 2l-1 3h-1c0-1-1-1-1-1l-1-1v-3l1-1z" class="N"></path><path d="M371 666c3 4 5 8 9 12l-2 1h-1v2 1l-3-2c-1-2-2-4-3-7h0l-1-3h-1c0-1 1-2 2-3v-1z" class="W"></path><path d="M371 667c1 2 2 4 2 6h-2l-1-3h-1c0-1 1-2 2-3z" class="h"></path><path d="M361 667h1l1 1c1 0 1 0 2 1l-1 1v3l1 6c1 5 4 9 7 13l2 2c0 1 0 1-1 1l-4-1h-2-2-1l-1 1c-1-1-1-1-2-1l1-1-1-1 1-1h2l1-1-3-3c1 0 1-1 2-2l-2-3v-1l-1-4-2-4h1v-2c0-2 1-3 1-4z" class="U"></path><path d="M365 690l1 2 1-1c1 1 2 2 2 3h-2-2-1l-1 1c-1-1-1-1-2-1l1-1-1-1 1-1h2l1-1z" class="S"></path><path d="M361 667h1l1 1c1 0 1 0 2 1l-1 1v-1c-1 2 0 6-2 6-1 0-1-1-2-2v-2c0-2 1-3 1-4z" class="n"></path><path d="M374 680l3 2v-1-2h1l2 3h-1c2 2 2 3 4 4s2 3 4 4l1 3h-1-1c0 2 0 3 1 5l1 1-1 1c-2 0-4-1-6-1 0 0-1 1-1 2l2 1-2 1h-1-1c-2-1-3-3-5-4l-1-1c1-1 1-2 1-3 1 0 1 0 1-1l-2-2c1-1 1-1 1-2s0-3-1-5h-1v-1h-1 1l1-1v-1l2-2h0z" class="K"></path><path d="M374 680l3 2v-1-2h1l2 3h-1l-1 1 1 2-3 4h-1v-5c0-2 0-3-1-4h0z" class="a"></path><path d="M377 690v-1l2-1c0 1 1 2 2 2h2c1 1 2 2 2 3h0v4c1 0 1 1 2 1l1 1-1 1c-2 0-4-1-6-1l-1-1-2-3c-1-1-1-2-1-4v-1z" class="P"></path><path d="M377 691c1 1 1 2 2 3l1 1c1 0 1 1 2 2h3c1 0 1 1 2 1l1 1-1 1c-2 0-4-1-6-1l-1-1-2-3c-1-1-1-2-1-4z" class="O"></path><path d="M372 683v-1l2-2c1 1 1 2 1 4-1 2-1 6-1 8l1 1h1l1-3v1c0 2 0 3 1 4l2 3 1 1s-1 1-1 2l2 1-2 1h-1-1c-2-1-3-3-5-4l-1-1c1-1 1-2 1-3 1 0 1 0 1-1l-2-2c1-1 1-1 1-2s0-3-1-5h-1v-1h-1 1l1-1z" class="AB"></path><path d="M377 699l3-1 1 1s-1 1-1 2l-3-2z" class="K"></path><path d="M374 692l1 1h1l1-3v1c0 2 0 3 1 4l2 3-3 1c0-1-1-2-2-2l-1-1v-4z" class="Q"></path><path d="M301 423h1c3 4 6 8 9 11 4 4 8 7 11 11a30.44 30.44 0 0 1 8 8c2 2 2 5 3 9-1 2-2 5-2 7-2 4-1 10-1 14 1 4 2 13-1 16l-4 6-1-1c1-2 3-3 3-5 1-2 1-5 1-7-2-5-4-10-7-15l-10-26-10-28z" class="l"></path><path d="M317 443l5 4c1 0 2 1 2 2 1 2 3 2 4 4 0 0 0 2 1 2v1c0 1 0 2 1 3h0v3c1 1 0 2 0 3l-1-1c0-1 0-1-1-3-1-3-2-6-4-9-1-2-3-3-5-5-1-1-1-2-2-4z" class="X"></path><path d="M311 434c4 4 8 7 11 11a30.44 30.44 0 0 1 8 8c-1 1 0 4 0 6-1-1-1-2-1-3v-1c-1 0-1-2-1-2-1-2-3-2-4-4 0-1-1-2-2-2l-5-4c-2-3-5-4-7-7-1-1-1-1-2-1h1 1l1-1z" class="r"></path><defs><linearGradient id="p" x1="312.542" y1="430.805" x2="306.905" y2="444.197" xlink:href="#B"><stop offset="0" stop-color="#9c9796"></stop><stop offset="1" stop-color="#b8b9b9"></stop></linearGradient></defs><path fill="url(#p)" d="M301 423h1c3 4 6 8 9 11l-1 1h-1-1c1 0 1 0 2 1 2 3 5 4 7 7 1 2 1 3 2 4h-1c-3-1-5-5-8-7l2 9h0l1 1-2 1-10-28z"></path><path d="M296 380l1-1c3 3 5 6 7 10h1v-1l4 9c1 1 1 2 2 3v1c2 1 2 2 3 4 0 0 0-1 1-1l17 45h0l1 2c-1 0-2 0-3 1 1 1 2 2 3 2 0 1-1 1 0 3 0 0 1 1 1 2l-1 3c-1-4-1-7-3-9a30.44 30.44 0 0 0-8-8c-3-4-7-7-11-11-3-3-6-7-9-11h-1v-3l-2-2-2-7 1-2c2-4 4-6 5-10l1 2h1l1 1 1 1h0c0-2 0-4-1-5-1-7-6-13-10-18z" class="C"></path><path d="M305 419l1-2c0-1 0-2 1-3s1-1 2-1v1l3 3c-2 1-3 1-5 1 0 1-1 1-1 2h0l-1-1z" class="l"></path><path d="M303 399l1 2h1v1c0 1-1 1-2 2 0 1-1 1-1 2 0 5 1 9 3 13l1 1-1 1c-4-3-4-8-6-12h-1c2-4 4-6 5-10z" class="E"></path><path d="M297 411l1-2h1c2 4 2 9 6 12l1-1h0c1 2 2 2 4 3-1 1-2 1-3 1v1l-1-1c-1-1-3-2-5-4l-2-2-2-7z" class="X"></path><path d="M306 420c0-1 1-1 1-2 2 0 3 0 5-1l2 3c3 3 4 7 7 11v3h-1v-1l-5-7-2-1h-1l1-1v-1l-2-1-1 1c-2-1-3-1-4-3z" class="i"></path><path d="M306 420c0-1 1-1 1-2 2 0 3 0 5-1l2 3-1 1-1-1s0-1-1-1h-1l-1 1c1 1 2 1 3 1l-1 1h0l-1 1c-2-1-3-1-4-3z" class="I"></path><path d="M301 420c2 2 4 3 5 4l1 1v-1c1 0 2 0 3-1l1-1 2 1v1l-1 1h1l2 1-1 2c0 1 1 2 1 3h0c0 1 0 2 1 3 1 0 1 1 1 1 5 5 11 10 13 17 1 1 2 2 3 2 0 1-1 1 0 3 0 0 1 1 1 2l-1 3c-1-4-1-7-3-9a30.44 30.44 0 0 0-8-8c-3-4-7-7-11-11-3-3-6-7-9-11h-1v-3z" class="E"></path><path d="M308 426l1-1v2h4c1 1 1 3 2 4 0 1 0 2 1 3 1 0 1 1 1 1-4-3-7-6-9-9z" class="I"></path><path d="M311 422l2 1v1l-1 1h1l2 1-1 2c0 1 1 2 1 3h0c-1-1-1-3-2-4h-4v-2l-1 1-1-1v-1c1 0 2 0 3-1l1-1z" class="L"></path><path d="M399 660c1 1 2 1 2 1 2 1 3 4 4 6l11 14 2 5c0 1 0 2 1 3v1c1 2 3 2 2 5 2 6 5 12 6 18 1 1 1 2 1 3v2c1 1 1 1 1 2 0 3 1 6 1 9 0 1 0 1 1 2v14l-3-2-1 11-7-16-2-7-3-8-1-3-5-15-1-2h0l-14-36c-1-2-1-2 0-4 1 1 2 1 3 0 1 0 2-1 2-3z" class="C"></path><path d="M408 703c2-2 5-1 8-2h3l1 1c-2 2-5 3-6 4-2-1-4-1-5-1l-1-2z" class="r"></path><path d="M428 718c1 1 1 1 1 2 0 3 1 6 1 9 0 1 0 1 1 2v14l-3-2c1-8 1-16 0-25zm-29-58c1 1 2 1 2 1 2 1 3 4 4 6l11 14 2 5c0 1 0 2 1 3v1c1 2 3 2 2 5l-6-9c-2-4-3-7-6-10-2-2-7-8-9-8-3 0-4 0-5-1h-1c-1-2-1-2 0-4 1 1 2 1 3 0 1 0 2-1 2-3z" class="E"></path><path d="M284 513l2 2c0 1 0 3-1 4s-2 4-3 6c0 1-1 2-1 3l-1 1 3 1h3l1 1c0-1 1-2 2-2l-1-1v-1c-1-1-1-1-1-2 1-1 1-2 1-3v-7h2c0 1 1 2 1 3s1 2 2 2c1 1 2 1 3 2 3 1 5 2 8 2h0c-1 3-1 5 0 8l3 12c4 13 8 27 13 40 4 10 8 20 14 29h0c0 3 1 5 1 7-1 0-2 1-4 1h-2l-1-1c-1 0-2 0-3-1l-1-1c-1 0-2-1-2-2v-1h-1c-1-2-1-3-3-4l-4-11c0-3-1-5-2-7s-1-3-2-5v-1l-2-5-1-2v-2l-1-2h0l-1-5-2-6c-1 1-1 1-1 3-1-2-2-4-2-6v4c-1 1-3 0-4 0-1-1-1-2-1-3l-1-1c0-2-1-3-2-5h0c0-1-1-2-1-4h1l-1-1-1-1c0-1 0-2-1-3h-1v-2l-4-2c-3-1-3-4-7-4-1-2-2-2-4-3h-2c-3 1-7 1-11 1h-3c-1 0-3-1-4-1 2-2 6-2 8-3v-1s1 0 2-1h3c2-1 2-1 3-3 0 0 1 0 2-1 3 0 5-3 7-5l1 1h1l1-3 1 1c1-3 2-6 2-9z" class="m"></path><path d="M293 520c1 1 2 1 3 2 1 2 1 3 0 4l-1-1c-1-2-2-3-2-5z" class="W"></path><path d="M301 545l2-5 1 7 1 2-2 3-2-7z" class="O"></path><path d="M288 522v-7h2c0 1 1 2 1 3v1c-1 1-1 2 0 3 0 2-1 2-1 4 0-1-1-1-1-2h0 0l-1-2z" class="h"></path><path d="M298 532v-2l1-1 1 1c0 2 0 3 1 5h1c0 2 1 3 1 5l-2 5c-1-3-1-5-2-8-1-1-1-3-1-5z" class="P"></path><path d="M303 552l2-3 1 4c0 5 3 9 4 14l-1 1c0 1 1 2 0 3h0l-6-19z" class="Z"></path><path d="M310 567l7 19-3 1-5-16c1-1 0-2 0-3l1-1z" class="o"></path><path d="M296 522c3 1 5 2 8 2h0c-1 3-1 5 0 8l3 12-1 1h0c0 1-1 1-2 2l-1-7c0-2-1-3-1-5h-1c-1-2-1-3-1-5l-1-1-1 1v2c-1-1-1-4-2-6 1-1 1-2 0-4z" class="K"></path><path d="M300 530v-2h1c1 1 1 3 2 5l2 10 1 2c0 1-1 1-2 2l-1-7c0-2-1-3-1-5h-1c-1-2-1-3-1-5z" class="b"></path><path d="M288 522l1 2h0c0 4 0 6 2 9v1c0 1 0 1 1 1 1 2 1 3 1 4l1-2 1-1c1 2 1 5 1 7 1 4 2 8 4 12v2l2 7 1 1c-1 1-1 1-1 3-1-2-2-4-2-6v4c-1 1-3 0-4 0-1-1-1-2-1-3l-1-1c0-2-1-3-2-5h0c0-1-1-2-1-4h1c1 2 2 5 4 8 0 0 1 1 1 2h1c1-2-2-7-2-10-1-3-2-5-3-7l-7-9 1-1v-1c-1-2-3-4-4-5h3l1 1c0-1 1-2 2-2l-1-1v-1c-1-1-1-1-1-2 1-1 1-2 1-3z" class="P"></path><path d="M288 522l1 2h0c0 4 0 6 2 9v1c0 1 0 1 1 1 1 2 1 3 1 4 1 2 1 3 2 4v1c0 1 1 3 1 4-2-2-4-4-5-6 0-1-1-2-2-3l-2-3v-1c-1-2-3-4-4-5h3l1 1c0-1 1-2 2-2l-1-1v-1c-1-1-1-1-1-2 1-1 1-2 1-3z" class="J"></path><path d="M284 513l2 2c0 1 0 3-1 4s-2 4-3 6c0 1-1 2-1 3l-1 1 3 1c1 1 3 3 4 5v1l-1 1 7 9c1 2 2 4 3 7 0 3 3 8 2 10h-1c0-1-1-2-1-2-2-3-3-6-4-8l-1-1-1-1c0-1 0-2-1-3h-1v-2l-4-2c-3-1-3-4-7-4-1-2-2-2-4-3h-2c-3 1-7 1-11 1h-3c-1 0-3-1-4-1 2-2 6-2 8-3v-1s1 0 2-1h3c2-1 2-1 3-3 0 0 1 0 2-1 3 0 5-3 7-5l1 1h1l1-3 1 1c1-3 2-6 2-9z" class="N"></path><path d="M284 513l2 2c0 1 0 3-1 4s-2 4-3 6c0 1-1 2-1 3l-1 1 3 1c1 1 3 3 4 5v1l-1 1v-1c-2-1-4-2-7-3-4-2-8 0-12 0l-6 1v-1s1 0 2-1h3c2-1 2-1 3-3 0 0 1 0 2-1 3 0 5-3 7-5l1 1h1l1-3 1 1c1-3 2-6 2-9z" class="S"></path><path d="M284 513l2 2c0 1 0 3-1 4s-2 4-3 6c0 1-1 2-1 3l-1 1 3 1c1 1 3 3 4 5v1l-1 1v-1c-1-2-3-3-5-4h0c-1-1-1-1-2-1s-1-1-2-1h1c2-2 3-5 3-7l1-1c1-3 2-6 2-9z" class="T"></path><path d="M306 545l1-1c4 13 8 27 13 40 4 10 8 20 14 29h0c0 3 1 5 1 7-1 0-2 1-4 1l1-1-1-1c-3-2-5-5-7-8-5-7-7-16-10-24l3-1-7-19c-1-5-4-9-4-14l-1-4-1-2c1-1 2-1 2-2h0z" class="e"></path><path d="M306 545h0l3 11-3-3-1-4-1-2c1-1 2-1 2-2z" class="W"></path><path d="M306 553l3 3 15 41c2 3 3 8 5 11v2c1 1 1 0 1 2 1 1 2 3 2 4h-1c-2-1-4-5-5-7h0c-4-8-6-15-9-23l-7-19c-1-5-4-9-4-14z" class="AA"></path><path d="M324 597c2 3 3 8 5 11v2c1 1 1 0 1 2-1-2-2-4-3-5-1-2-2-3-3-5l-1-1c0-1 0-1-1-2l2-2z" class="K"></path><path d="M338 469h1 1v1l1 3c0 1 1 2 1 3 1 3 3 7 4 10 2 2 3 5 4 8l17 47 6 13c0 2 0 3 2 4v1l-1 1 18 46-2 2c0 1 0 3-1 4l1 1c1 4 0 17 2 19v2h1c1-4 1-10 0-13v-9h0l1-1h0c0 9 5 17 8 26l7 20c2 5 4 9 5 14 0 1-1 2 0 3s1 3 2 4v3l-11-14c-1-2-2-5-4-6 0 0-1 0-2-1 0-1-2 0-3-2-2-1-3-4-3-6 0-3 0-7-1-10-2 0-2 0-3 1v-1-2h-1v-1c-2 2-3 3-4 5 0-6-3-12-5-17l-9-24-13-33c2-1 4-4 6-4 1 1 0 2 1 2v-4c-2 0-3 2-5 3-4 2-2-1-5-3-2-1-4-1-6-2h0l6 1-9-26-8-20c-1-4-3-8-4-12l-1-1v-9-9l1-1v1c0-1 0-4 1-5 0-1 1-2 2-3 1-2 0-2 1-4v-4h1v-1z" class="C"></path><path d="M389 612v4h0-1c0-2 0-5-1-7l-1-1 1-1h3v1c0 1 0 3-1 4z" class="X"></path><path d="M360 549l1-2h0l1 1v1c0 1 1 1 2 1v2h0l2 2v4 5l-2 1c1-2 0-3 0-4-1-4-3-8-4-11z" class="AJ"></path><path d="M360 549c-2-3-5-7-6-10v-1h1 0c1 3 2 4 5 6 0 1 1 1 2 2 2 3 4 6 7 10h-1l-2-2-2-2h0v-2c-1 0-2 0-2-1v-1l-1-1h0l-1 2z" class="i"></path><path d="M367 578c2 1 3 4 4 6 6 7 11 15 14 23v1c-1 1-1 1-2 1 0-6-5-15-9-20-3-4-6-7-7-11z" class="L"></path><path d="M351 517c1 2 3 6 3 8l3 7c1 2 0 3-1 4l-2 1c0-2 0-4-1-6l-3-12 1-2z" class="F"></path><path d="M357 532l-1 3c-1-1-1-1-1-2h0l-2-6h0l1-2 3 7z" class="v"></path><path d="M333 485v1c0-1 0-4 1-5 0-1 1-2 2-3 1-2 0-2 1-4 1 4 1 8 2 12 1 3 2 5 2 8h0c-2-2-2-3-3-5v-1l-1-1h1v-1-1c-1-1 0-1-1-2v-3c-1 0-2 1-3 3 0 1 0 3 1 5v2h0c1 3 0 5 0 7 0 3 1 6 1 9l-1 1-1-1v-2l-1 1-1-1v-9-9l1-1z" class="X"></path><path d="M338 469h1 1v1l1 3-1 1c0 1 2 5 2 6s-1 1-1 2l6 19v3c1 4 2 9 4 13l-1 2c-1-3-2-5-3-8-3-5-6-11-6-17 0-3-1-5-2-8-1-4-1-8-2-12v-4h1v-1z" class="E"></path><path d="M338 469h1 1v1l1 3-1 1c0 1 2 5 2 6s-1 1-1 2l6 19v3c-4-11-8-22-9-34v-1zm83 115c5 0 8 1 13 4h0c-2 0-5-2-7-2-1-1-1-1-2-1l1 1c2 0 5 5 7 7 2 4 3 9 5 13 6 13 11 26 16 40 2 5 4 10 4 15l1 1h2 0c-1 6-2 12-1 18l1 3v3c-2 2-5 3-8 5-4 3-11 9-13 14l-1 2h-1l-1-1c-1 0-2-1-2-1-2 0-2-1-4 0 0 1-1 2-1 3-1 1-1 1-1 2v8l1 2h-1c0-1 0-1-1-2v-2c0-1 0-2-1-3-1-6-4-12-6-18 1-3-1-3-2-5v-1c-1-1-1-2-1-3l-2-5v-3c-1-1-1-3-2-4s0-2 0-3c-1-5-3-9-5-14l-7-20c-3-9-8-17-8-26h0l-1 1h0v9c1 3 1 9 0 13h-1v-2c1-2 0-5 0-8 0-4-1-10 1-13v-1c1-3 4-5 6-7 1-1 3-2 3-3 3-2 6-3 8-6 0-1 1-2 1-4h1 2c2-1 6-3 7-5v-1z" class="D"></path><path d="M459 662h2 0c-1 6-2 12-1 18l1 3v3l-1-1h0c-1 0-1-1-1-1-1-2-1-4-2-6 0-2 1-4 1-5 1-4 0-7 1-11z" class="AD"></path><path d="M399 603c1-1 3-2 3-3 3-2 6-3 8-6 1 2 0 4 0 6-3 2-7 6-10 7l-1-1c0-1 1-2 0-3z" class="C"></path><path d="M460 685h0l1 1c-2 2-5 3-8 5-4 3-11 9-13 14l-1 2h-1l-1-1 2-3c6-8 11-14 21-18z" class="q"></path><path d="M421 584c5 0 8 1 13 4h0c-2 0-5-2-7-2-1-1-1-1-2-1l1 1c2 0 5 5 7 7 2 4 3 9 5 13 6 13 11 26 16 40 2 5 4 10 4 15h0c-3-3-4-10-6-14-7-18-16-35-22-54-1-2-2-3-3-4h-4c-2 2-5 2-7 4 0 2 0 4 1 6h1-1c-1-1-1-2-1-3l-1-1v-3c2-1 3-2 4-2h1 1c2-1 2-1 3-2 1 0 2 1 3 0 0-1-1-1-2-1-1 1-2 1-4 2-2 0-3 0-4 1-1 0-2 0-3 1h0v1c0 2 0 3 1 5v2c-1-3-1-6-2-9h-1 2c2-1 6-3 7-5v-1z" class="C"></path><path d="M416 670c2 0 3-1 5-3v-2l1-2c0-2 0-4 2-5 0-1 0-1 1-2h0 1l4 17c0 2 1 3 1 5l1 1 1 3v1c2 4 3 9 4 13 1 2 0 5 2 7l-2 3c-1 0-2-1-2-1-2 0-2-1-4 0 0 1-1 2-1 3-1 1-1 1-1 2v8l1 2h-1c0-1 0-1-1-2v-2c0-1 0-2-1-3-1-6-4-12-6-18 1-3-1-3-2-5v-1c-1-1-1-2-1-3l-2-5v-3c-1-1-1-3-2-4s0-2 0-3c0 0 1-1 2-1z" class="L"></path><path d="M416 670c2 0 3-1 5-3v-2l1-2c0-2 0-4 2-5 0-1 0-1 1-2h0 1v1c-1 3 0 4-1 7h0v-2h-1l-1 4c-1 4-3 5-3 10v2c-1-1-1-1-2-1s-1 0-2 1c-1-1-1-3-2-4s0-2 0-3c0 0 1-1 2-1z" class="C"></path><path d="M414 671s1-1 2-1l-1 2v1h1c1 1 2 1 3 3h1v2c-1-1-1-1-2-1s-1 0-2 1c-1-1-1-3-2-4s0-2 0-3z" class="I"></path><path d="M420 676l1 1h0c1 4 3 12 6 15 1-1 0-2 1-3s1-1 2 0c2 2 6 10 5 13v3c-2 0-2-1-4 0 0 1-1 2-1 3-1 1-1 1-1 2v8l1 2h-1c0-1 0-1-1-2v-2c0-1 0-2-1-3-1-6-4-12-6-18 1-3-1-3-2-5v-1c-1-1-1-2-1-3l-2-5v-3c1-1 1-1 2-1s1 0 2 1v-2z" class="u"></path><path d="M418 686c2 2 4 4 5 7 2 4 4 11 5 17 0 3 0 6 1 8l1 2h-1c0-1 0-1-1-2v-2c0-1 0-2-1-3-1-6-4-12-6-18 1-3-1-3-2-5v-1c-1-1-1-2-1-3z" class="S"></path><path d="M743 325c4-1 8-2 11-2 8-1 15 2 22 5 5 2 9 4 13 6l3 2c0 1 1 2 1 2 4 5 9 13 8 19v1c-3-7-4-12-10-18h-2c-14 6-24 19-30 32-4 11-5 21-4 33 1 1 1 2 1 3 1 4 1 8 1 11v2c0 2 0 5 1 7 0 2 0 2-1 3 2 2 5 4 6 5 1 2 4 4 6 5l1 1 1 1 4 1c-2 1-5 3-7 4s-4 3-6 4h-1l-5 4v-3l3-2h-7c-1 1-1 1-2 1h-1 0c-1 1-1 1-2 1-1-1-2-2-2-4l1-1h0c-1-1 0-2-1-4 0-1-1-2-2-2v-1h-2-1-1c-1 0-1 0-2 1 0 1 1 2 1 3 1 1 2 4 1 6v1 5c0 2 0 2-1 4-1 3-1 9-1 12v10h-1v7c1 3 0 7 2 10v1c0 3 0 6 1 8v1l-2 2h-1c1 1 1 2 1 3l-1 2s2 2 2 3v1c-1 0-1 0-2-1h-1-1l-2-1 1 2c-2 2-4 3-6 4v-1h-1l-2 1c-1 0-1 1-2 1h-1v1h2c1 0 2 1 3 1 0-1 1-2 2-2l2 1c0 1 1 1 3 1h2c1 1 3 1 4 1h1l-6 2c-2 1-5 3-7 5v2l-2 2c1 1 1 2 3 2h0l-3 4v1l-5 9c0 1-1 1-2 2v4c-1 2-1 3-1 5h0c-1 1-1 2-2 4l-2 8c0 1 1 2 1 4l-1 1 1 2v2c-1 2-1 7-1 10l1 1c0 1 0 1-1 3v2c0 2 0 3-1 4v6c0 3 1 7 0 10 0 2 1 4 0 6l-1 1-5-6-1 1c3 4 5 8 10 10 1 0 2 1 3 1v1c1 0 1 0 1 1h-1c-1-1-3-1-4-2v2c-3 0-6-1-9 0-2-1-4-1-7-1-1 0-1 1-2 1-3 2-5 1-7 3l-3 3-4 4c-1-1-2-1-3-1l-1-1v1l-1-1-5 5c-4 3-8 5-11 8-4 2-7 5-10 7h-1 0c-1 1-2 1-2 2-1 1-4 3-5 3l-1-1c0 1-1 2-1 3h0l-1-1 1-3h0c-2 1-4 4-6 5l2 2-1 1c-1 4-3 8-5 12v1c0 1-1 2-1 2v3l-3 5c-1 1-2 2-4 3-1 1-2 1-2 2 0 2-1 3-1 4-3 3-8 5-10 9 0 2-2 3-4 5v2l-2 2c-3 2-4 5-6 8-1 2-1 4-2 6 0 2 0 6-1 7v2c2 4-1 10 1 15l-1 1c0 4 1 9 1 14l-2-2c0-1 0-2-1-2-1-2-3-4-5-4-2-2-4-3-6-4-1 0-2-1-3-1s-2 0-3-1l3-7v-2c-1 1-2 1-2 2h-6l-1-1h-1l-4 2-1 1v-1c1-3 2-6 2-9l3-8h-3l-1 1c-1-1 0-2 0-3-3-4-5-8-8-11l-1-2c-2-2-3-3-4-6v-1c0-2-1-5 0-8 0 2 1 2 2 4 0-5-1-9-2-13v-3c0-2-1-2-2-4h-1l1-7c1-5 4-10 6-15l12-35 53-146 18-49 5-13c1-3 2-7 3-9l1-2-1-1c-1 1-3 1-5 1s-4-1-6-3c-1-1-2-3-2-5 0 1 1 2 1 3 1 2 3 3 5 4h1 2c2 0 3-1 5-2l1 1c3 0 5-2 8-3 0-2 1-3 1-4 3-2 6-6 9-8l3-4 5-7 1 1 3-2 4-3v-1c3-2 3-5 5-8s4-5 6-8c1-3 1-6 1-9-1-2 0-6 1-9v-1l1-1c0-1 1-1 2-1h0c1-1 2-1 4-1h0c1 0 1 0 2-1 1 0 2 0 3-1l1 1v3l-1 1h1 0s1-1 0-1l4-8c1-1 2-2 4-2v-1c0-1 0 0-1-1h0l5-5 1 1v-1-1h2l3 3h1l-1-2v-1c1-2 5-2 7-3 3-1 4-4 8-5v-2h1z" class="AG"></path><path d="M736 336l2 2-2 3-1-2 1-3z" class="AZ"></path><path d="M709 461c1 2 2 3 1 4 0 1-1 2-2 3v-2c0-2 1-3 1-5z" class="B"></path><path d="M683 498c1 0 1-1 1-2 0-2 1-4 1-7 1 2 1 3 1 4 0 2 1 4 0 5h-3z" class="AC"></path><path d="M676 551h1 1l-3 3c0 1 0 1-1 2 0 0 0 1-1 1h0c-1-1-1-1-1-2h0l-1 1h-1v-1l1-2v1h1l2-1h0l2-1v-1z" class="J"></path><path d="M691 514l2 1-1 1h0c-1 2-1 3-2 4h-1l1-2h-1c-1 1-2 2-2 3v1l1-1v1h-1l-2-1h0c2-3 4-5 6-7z" class="M"></path><path d="M735 339l1 2-1 2-1 2c-1-1 0-1-1-1l-1 1-1-1v-3c1-1 2-1 3-1l1-1z" class="AZ"></path><path d="M735 339l1 2-1 2v-1h-1v-2l1-1z" class="AY"></path><path d="M677 537s1 1 1 2h0l1-1 1-1c1 2 0 3 2 5 0-1 0-1 1-1l1 1-1 1-3-1-1 1h-1-1c0-2 1-2 1-3l-1-1v-2z" class="J"></path><path d="M712 441c1-4 2-7 3-11v-1c1 4 1 7 1 10h-1-1l-1 2h-1z" class="M"></path><path d="M683 524c-1 2-3 6-5 7h-1c0 2-3 4-4 6l3-5 3-7 2-3 2 2z" class="s"></path><path d="M693 515c1 1 1 1 1 3v-1h2l-1 2c-3 1-3 1-4 3-2 2-4 4-6 5h-1c1-2 1-2 2-3h1c1 0 2-2 3-3v-1c1-1 1-2 2-4h0l1-1z" class="AK"></path><path d="M719 460c1 1 1 2 1 3v1c1 2 0 10 1 11l1 1-2 1h0c0 2 0 3-1 4v-21z" class="AA"></path><path d="M631 669l-10-12c3 3 8 9 12 10h1v1s-1 1-2 1h-1z" class="b"></path><path d="M712 441h1c-1 3-3 6-4 9l-2 3c-1 1-1 1-3 1h-2v-1c1-1 3-3 4-3 3-3 5-6 6-9z" class="AF"></path><path d="M665 587v-2h0l1-2 1-1h0c0 2 0 3 1 4 2 2 5 5 8 6v-1h1v1l2 1v1l-2 1-1-1c-3-3-8-4-11-7z" class="g"></path><path d="M665 587c-1-1-2-1-2-3 1-1 2-3 3-4l2 2c2 0 2 0 4-1 1-1 1-2 3-2l1 1c-1 1-2 1-3 2v1h2v1h-1-1c-1-1-1-1-2-1l-1 1c-1 0-2-1-2 1v1c-1-1-1-2-1-4h0l-1 1-1 2h0v2z" class="T"></path><path d="M672 576v-1l-1-1-1 1v2h-1c-1-1-1-2-2-2l-2-1h0-1-1-1l-8 1c1-2 4-3 7-4 2 1 4 0 6 1 0 0 0 1 1 1h3 0l2 2-1 1z" class="J"></path><path d="M688 480v-1c0-2 2-5 5-7l5-3c-1 1-1 3-2 4l1 3-1 3v1l-2-2c-2 0-2 2-3 1l-2-1-1 2h0z" class="B"></path><path d="M722 413h0l-1-4c-1-3-1-5-1-8l2 9c2-4 3-8 5-12v4h0 0c-1 2-1 3 0 6v3h0l-1 3v-1h-1v3h0l-1-1v2l-1-2c0-1-1-1-1-2z" class="N"></path><path d="M621 706c0 2-1 3-1 4-3 3-8 5-10 9 0 2-2 3-4 5 0-1 1-3 1-4l4-4-1-1c-1 1-3 1-4 2-3 1-5 3-7 3 2-2 6-4 9-6l2-2 1 1c1-1 2-1 4-2 1-2 4-3 6-5z" class="x"></path><path d="M727 398v-1c-1-2-1-12 0-13 0 4 1 7 2 10 0 1 0 2 1 2h0l-1-2v-2h1l1-1h-1 0l1-1c1-3 1-5 2-8h0v2c1 2 1 3 1 5l-1 3v-3h-1v4h-1-1c0 2 1 2 1 3-1 2-1 4 0 6h0v9c0-2 0-3-1-4l-1-3v-2c0-2 0-3-1-4h-1z" class="N"></path><path d="M661 571l11-2 2 1-1 1h1 0c1-1 1-1 2-1 0 2 0 3 2 4 1 1 1 2 1 2h1v1h-1-4-2l-1-1 1-1-2-2h0-3c-1 0-1-1-1-1-2-1-4 0-6-1z" class="Y"></path><path d="M671 573c3 0 6 2 8 4l1-1v1h-1-4-2l-1-1 1-1-2-2z" class="B"></path><path d="M731 402h0c-1-2-1-4 0-6 0-1-1-1-1-3h1 1v-4h1v3l1 3c2 2 2 3 2 6 1 2 1 4 2 6l1-1v-1-1c-1-1-1-3-1-5-1-2-1-6-1-9h0l3 17-2 1h-1v-2c-1-1-1-2-2-4v-2l-1 1c0-2 0-1-1-2v3c-1-1-1-1-1-3h-1v2 1z" class="R"></path><path d="M610 715l1 1-4 4c0 1-1 3-1 4v2l-2 2c-3 2-4 5-6 8-1 2-1 4-2 6h0v-1-4h-4 0c1-1 3-1 4-2l1-1 1 1c1-1 0-1 1-2l-2 1h-1l-1 1c-1 1-3 1-4 1l2-1 1-1h0c0-1 1-1 2-1l1-1c1-3 4-5 6-7 0-1 1-2 1-3s1-1 2-2c0-1 0-1 1-1l1-1 2-2v-1zm82-179h0c0 2 1 3 1 4h2-1l1-1c1-2 2-3 4-5l1 1h0l1 1c0-1 1-1 2-2s4-3 5-5c0-1-1-3 0-4l1-1h3 0c0 1-1 1-2 1v5l-1 2-1-1c-1 2-3 3-4 4l-1 1-2 2c-4 2-9 5-11 8l-1-1 1-1c0-3 1-6 1-8h1z" class="B"></path><path d="M742 327h1c-1 1-2 1-2 2l-3 9-2-2-1 3-1 1c-1 0-2 0-3 1v-2-1l-1 1v1c-1-1-2-1-2-2l-1-2v-1c1-2 5-2 7-3 3-1 4-4 8-5z" class="AY"></path><path d="M727 336h1l1 1c1 0 1 0 2-1 2-2 3-3 6-2 0 1 0 1-1 2l-1 3-1 1c-1 0-2 0-3 1v-2-1l-1 1v1c-1-1-2-1-2-2l-1-2z" class="AX"></path><path d="M615 700c1-1 3-2 4-3l4-2c1-1 1-1 3-1 0-1 0-1 1-1-1 0-3 1-5 1-2 1-4 1-6 2l3-3h1c0-1 1-1 2-2h1c1-1 1-1 3-1v-1h-1c0-1 1-3 3-3 1 0 0 1 2 0l1-1c-1-1-1-2-1-2 0-2 1-3 2-3 0 0 1 1 1 2h1v-2s2-1 2-2c-1 4-3 8-5 12v1c0 1-1 2-1 2-1 1-2 1-3 1l-1 1c-4 0-8 4-11 5z" class="x"></path><path d="M615 700c3-1 7-5 11-5l1-1c1 0 2 0 3-1v3l-3 5c-1 1-2 2-4 3-1 1-2 1-2 2-2 2-5 3-6 5-2 1-3 1-4 2l-1-1c2-1 5-3 5-5 1-1 2-2 3-4h0c1-2 2-3 4-3v1l-2 3h0l2-1v-1c3-1 4-2 5-5-1 0-2 1-3 1-2 1-5 3-7 3-3 1-6 3-9 5l7-6z" class="R"></path><path d="M713 441l1-2h1 1v6c-1 3-3 7-5 9v2h-1c0 2-1 4-1 5 0 2-1 3-1 5v2c-1 3-2 5-3 8l-1-2h1v-2c0-1 0-3 1-3 0-1 1-1 1-2v-5c0-1-1-2 0-3v-6l2-3c1-3 3-6 4-9z" class="f"></path><path d="M709 450l2 2-4 10c0-1-1-2 0-3v-6l2-3z" class="J"></path><path d="M713 441l1-2h1c0 3-1 4-2 7l-1 2h1 0c0 2-1 3-2 4h0l-2-2c1-3 3-6 4-9z" class="B"></path><path d="M651 659c-1 1-1 3-2 4-2 1-8 4-8 7-2 1-4 4-6 5l-1-2-3-4h1c1 0 2-1 2-1v-1l8-8 2 2h1v-1l1 2c1-1 1-1 2-1s2-2 3-2z" class="h"></path><path d="M651 659c-1 1-1 3-2 4-2 1-8 4-8 7-2 1-4 4-6 5l-1-2-3-4h1c2 0 3 0 3 1h1l4-3c3-2 5-4 8-6 1 0 2-2 3-2z" class="H"></path><path d="M632 669c2 0 3 0 3 1h1l4-3v1c-1 0-1 1-1 2l-3 3-2-1v1l-3-4h1z" class="AA"></path><path d="M697 502l1 1 1-1 1 1v2l1 1h0c0 1 0 2 1 2 0 1 1 2 1 3 3 1 5 2 8 2l-1 1-9-2c0 2-1 2-2 4h-2-1v1h-2v1c0-2 0-2-1-3l-2-1h0l-1-1 2-2v-1c0-1 1-1 1-2h3l-2-3 1-2c1 0 2 0 2-1z" class="V"></path><path d="M691 514c1 0 3-2 3-3l2-2c1 1 2 1 3 2-1 1-2 1-4 2h0l-1 1 1 1h2c0 1 0 1-1 1v1h-2v1c0-2 0-2-1-3l-2-1h0z" class="T"></path><path d="M699 511c1 0 2 0 2 1 0 2-1 2-2 4h-2-1c1 0 1 0 1-1h-2l-1-1 1-1h0c2-1 3-1 4-2z" class="B"></path><path d="M697 502l1 1 1-1 1 1v2l1 1h0c0 1 0 2 1 2 0 1 1 2 1 3-1 0-2-1-3-2s-3-1-4-1l-2-3 1-2c1 0 2 0 2-1z" class="AC"></path><path d="M697 502l1 1 1-1 1 1v2l1 1h0l-1 1-1-2h-1-2l-1-2c1 0 2 0 2-1z" class="B"></path><path d="M704 454c2 0 2 0 3-1v6c-1 1 0 2 0 3v5c0 1-1 1-1 2-1 0-1 2-1 3v2h-1l1 2c1 1 1 1 0 2l-1 1c0-3 0-4-2-5l-1-1-1 1h-1l-2 2-1-3c1-1 1-3 2-4 2-3 3-7 5-10 1-1 2-2 2-4l-1-1z" class="T"></path><path d="M698 473h0c1-2 1-2 3-3 0 1 1 1 2 2h0c-2 0-3 0-5 1z" class="M"></path><path d="M698 473c2-1 3-1 5-1l-1 2-1-1-1 1h-1l-1-1zm5-12h2c0 2-1 4 0 6h0c-1 1-1 0-1 1l-1 1h-1v-3h0v-1-1c0-1 1-2 1-3z" class="B"></path><path d="M682 564l-1-3c1-1 1-1 3-1v-2h1v-2l1-1v-2l1-1v-1-1l1-1h-1l2-2-2-2v-2h0c-1 0-1 1-2 1v-1l-1-1 1-1h0l1-1c0-1 1-3 2-3s1 1 2 0l-1-1v-1l1 1 1-1v-1c1-1 0 0 1-2h0v-1c0-1 0-2 1-3h0v-1l1-1c-1-2-3 0-4-1 0-1 0-1 1-1v-2c1 0 3 0 4-1h0l1 1 1-1c1 0 1 2 1 4 1 0 1 1 1 2h1v4c-1 0-2 1-3 2l-1-1 1-1-1-1 1-1v1h1c0-1 0-1-1-2h-1c-1 0-2 1-2 2v3l-2 3h-1c0 2-1 5-1 8l-1 1 1 1c-3 6-3 14-8 18z" class="J"></path><path d="M688 480h0l1-2 2 1c1 1 1-1 3-1l2 2c-2 2-3 4-2 6h0c0 2 1 3 1 4l1 1h0l-1 1c0 2-2 3-1 6h-2v-1l-2-1v1c-1 0-2 0-3 1v-5h-1c0-1 0-2-1-4l1-2 2-7z" class="f"></path><path d="M694 486c0 2 1 3 1 4l1 1h0l-2 1h-2c0-2-1-3 0-4 1 0 1-1 2-2z" class="g"></path><path d="M690 496v-1l-1-1c-1-1-1-2 0-3v3c2 0 2 0 3-1v-1h2l2-1-1 1c0 2-2 3-1 6h-2v-1l-2-1z" class="Y"></path><path d="M686 487l3-2v-2h1v5c-1 1-1 1 0 2h-1v1c-1 1-1 2 0 3l1 1v1 1c-1 0-2 0-3 1v-5h-1c0-1 0-2-1-4l1-2z" class="g"></path><path d="M687 493c0-1 0-3 2-4v1 1c-1 1-1 2 0 3l1 1v1 1c-1 0-2 0-3 1v-5z" class="z"></path><path d="M642 659c2-3 5-5 8-8 2-2 6-9 9-10l1 1s1 0 1 1c1-1 1-1 1-2 1 2 1 4 2 7l-1 1h0c-1 0-2 0-3 1s-1 2-2 3l-7 6c-1 0-2 2-3 2s-1 0-2 1l-1-2v1h-1l-2-2z" class="a"></path><path d="M662 641c1 2 1 4 2 7l-1 1h0c-1 0-2 0-3 1h-1c0-2 2-4 2-5s-1-3-1-3 1 0 1 1c1-1 1-1 1-2z" class="B"></path><path d="M686 493h1v5c1-1 2-1 3-1v-1l2 1v1h2 0c1 1 1 1 1 2s1 2 2 2c0 1-1 1-2 1l-1 2 2 3h-3c0 1-1 1-1 2v1l-2 2 1 1h0c-2 2-4 4-6 7h0l-1 1-1 2-2-2c1-2 2-4 2-7 1-1 1-2 1-3h1c-1-1-2-1-3-2h0v-1c1-1 1-1 3-1v-4l-2-2v-3-1h3c1-1 0-3 0-5z" class="V"></path><path d="M686 493h1v5c0 1 0 3 1 4 1 2 2 3 2 5v2-1h-1-1c0-1 1-2 0-3 0-1-1-1-2-2 0-1 0-2-1-3 0-1-1-1-2-1v-1h3c1-1 0-3 0-5z" class="Y"></path><path d="M692 510v1l-2 2 1 1h0c-2 2-4 4-6 7h0l-1 1v-1c-1-2 0-3 1-5h0l3-5 1 1c1-1 2-1 3-2z" class="g"></path><path d="M688 511l1 1v1c-1 1-2 2-2 3h-2l3-5z" class="Y"></path><path d="M690 496l2 1v1h2 0c1 1 1 1 1 2s1 2 2 2c0 1-1 1-2 1l-1 2 2 3h-3c0 1-1 1-1 2-1 1-2 1-3 2l-1-1 2-2v-2c0-2-1-3-2-5-1-1-1-3-1-4 1-1 2-1 3-1v-1z" class="AE"></path><path d="M690 496l2 1v1c1 2 2 4 1 6-2-2-2-4-3-7v-1z" class="AF"></path><path d="M694 498h0c1 1 1 1 1 2s1 2 2 2c0 1-1 1-2 1l-1 2-1-1c1-2 0-4-1-6h2z" class="M"></path><path d="M693 504l1 1 2 3h-3c0 1-1 1-1 2-1 1-2 1-3 2l-1-1 2-2v-2h3v-3z" class="AF"></path><path d="M722 476c1-1 1-1 2 0v9c0 2-1 7 0 9-1 3-1 6-2 9l-1 11h-1 0c-1 1-2 2-2 3h-1c0-1 0-1 1-2l-2-2c-1 0-2 0-3-1 5-5 6-24 6-31 1-1 1-2 1-4h0l2-1z" class="k"></path><path d="M721 492h2v3h-1c-1-1-1-2-1-3z" class="O"></path><path d="M743 325c4-1 8-2 11-2 8-1 15 2 22 5 5 2 9 4 13 6l3 2c0 1 1 2 1 2 4 5 9 13 8 19v1c-3-7-4-12-10-18h-2l-1-1c0-1-1-1-2-2-6-3-12-3-19-2-5 2-10 4-14 7h-1c6-5 14-9 21-9 2 0 4-1 6-1-7-1-15 0-22 1-2 1-5 2-7 2 5-3 11-4 17-5 2-1 4-1 7-1h1 0 1c-8-3-15-6-25-5h0c-3 1-6 2-8 3h-1v-2h1z" class="AH"></path><path d="M700 474l1-1 1 1c2 1 2 2 2 5l1-1c0 2 0 7 1 8-1 2-1 2-1 4l1 1-1 1c-1 1-1 2 0 3l1-1h1 0v1h0c-1 1-2 2-2 3s0 1 1 2v1c-1 0-1 1-2 1 0 1 0 1-1 2s-1 1-1 2h-1l-1-1v-2l-1-1-1 1-1-1c-1 0-2-1-2-2s0-1-1-2h0c-1-3 1-4 1-6l1-1h0l-1-1c0-1-1-2-1-4h0c-1-2 0-4 2-6v-1l1-3 2-2h1z" class="J"></path><path d="M699 474h1c-1 2-2 3-4 5l1-3 2-2z" class="AC"></path><path d="M704 479l1-1c0 2 0 7 1 8-1 2-1 2-1 4l1 1-1 1-1 1-1 1v-2-1c0-1-1-1-1-2v-2h1v-4c0-2 0-3 1-4z" class="B"></path><defs><linearGradient id="q" x1="585.074" y1="754.58" x2="591.752" y2="758.174" xlink:href="#B"><stop offset="0" stop-color="#150606"></stop><stop offset="1" stop-color="#320d0c"></stop></linearGradient></defs><path fill="url(#q)" d="M596 742h0c0 2 0 6-1 7v2c2 4-1 10 1 15l-1 1c0 4 1 9 1 14l-2-2c0-1 0-2-1-2-1-2-3-4-5-4-2-2-4-3-6-4-1 0-2-1-3-1s-2 0-3-1l3-7c0 2-1 4-1 6 2-2 5 0 7-1v-3h0c1 1 1 2 2 3v-1c0-1 0-4-1-5v-1c0-1 0-1-1-2v-2-1-1l2-3h1l2 2 2-1h1l2-2-1-1v-1c-1 1-2 1-3 2 0 0 0 1-1 1v-1c3-2 5-3 6-6z"></path><path d="M595 751c2 4-1 10 1 15l-1 1c0 4 1 9 1 14l-2-2c0-1 0-2-1-2-1-2-3-4-5-4-2-2-4-3-6-4 0-1 0-1 1-1 2-1 4-1 6 1h1 0l1-4h1c1 0 1-2 2-3v-6c1-1 1-3 1-5h0z" class="H"></path><path d="M724 494v4h2 0v3h1c2-1 2-1 3-1l2-1h0c1 1 2 1 2 1l1 1c0 1 1 2 1 3h0 1c0 1 0 1 1 2v-5c0 3 0 6 1 8v1l-2 2h-1c1 1 1 2 1 3l-1 2s2 2 2 3v1c-1 0-1 0-2-1h-1-1l-2-1 1 2c-2 2-4 3-6 4v-1h-1l-2 1c-1 0-1 1-2 1h-1v1h2c-1 2-2 4-2 6s0 2-1 3h0c-1 2-1 4-2 5l-1 2-1-2c0-1 1-2 1-4l1-3h0c1-2 2-3 2-5h-1l-1 1c-2 2-2 2-4 2l-1-1h-1v-1c-1-1-1-1-1-3h4s0-1-1-1h-1c-1-1-1-1-3-1 1 0 2 0 2-1h1c0-2 1-3 1-4 0-3-3-4-4-6l1-1 2-1c1 1 2 1 3 1l2 2c-1 1-1 1-1 2h1c0-1 1-2 2-3h0 1l1-11c1-3 1-6 2-9z" class="W"></path><path d="M713 531c0-1 1-1 2-2h0c1 0 1 0 2-1l1 2c-2 2-2 2-4 2l-1-1z" class="h"></path><path d="M713 524c0 1 0 0 1 1 0 0 1 0 1-1v-1c1-2 3-4 4-5 0 2 0 3-2 4l-1 2 2-1v1c0 1 0 2 1 2l-1 1h-2l-1 1v-1s0-1-1-1h-1c-1-1-1-1-3-1 1 0 2 0 2-1h1z" class="k"></path><path d="M711 513l2-1c1 1 2 1 3 1l2 2c-1 1-1 1-1 2h1c0-1 1-2 2-3h0c0 1-1 3-1 4-1 1-3 3-4 5v1c0 1-1 1-1 1-1-1-1 0-1-1 0-2 1-3 1-4 0-3-3-4-4-6l1-1z" class="O"></path><path d="M730 500l2-1h0c1 1 2 1 2 1l1 1c0 1 1 2 1 3h0 1c0 1 0 1 1 2v-5c0 3 0 6 1 8v1l-2 2h-1c1 1 1 2 1 3l-1 2s2 2 2 3v1c-1 0-1 0-2-1h-1-1l-2-1 1 2c-2 2-4 3-6 4v-1h-1l-2 1c-1 0-1 1-2 1h-1v1c-1-1-1-2-1-2 2-2 3-1 6-2h0c1-1 2-1 2-1 0-1 0 0-1-1h0c1 0 1 0 2-1h1l-1-2h-3c1-1 0-1 1-1 0-1 1-2 1-3v-2c0-1 2-1 1-3l-1-1v-3c-1-2-2-2-2-4h1c2-1 2-1 3-1z" class="d"></path><path d="M730 500l2-1h0c1 1 2 1 2 1l1 1-1 3v2l-1 1h-1c-1 0-1 0-2-1v-4c1-1 1-1 0-2z" class="Q"></path><path d="M732 507v-1l2-2v2l-1 1h-1z" class="a"></path><path d="M735 501c0 1 1 2 1 3h0 1c0 1 0 1 1 2v-5c0 3 0 6 1 8v1l-2 2h-1c1 1 1 2 1 3l-1 2s2 2 2 3v1c-1 0-1 0-2-1h-1-1l-2-1-2-2v-4h0l1-1h0v3c2 0 2 1 3 1l1-1v-1l-3-2v-3c1 1 1 1 2 0l1-1-1-2v-2l1-3z" class="k"></path><path d="M738 501c0 3 0 6 1 8v1l-2 2h-1 0v-8h1c0 1 0 1 1 2v-5z" class="o"></path><path d="M677 631h4 4 0c0 1 0 1 1 2 0 2 0 2-1 3s-2 1-3 2c-2 1-1 2-2 3l-1 4h0 0l-2 2-5 5c-4 3-8 5-11 8-4 2-7 5-10 7h-1 0c-1 1-2 1-2 2-1 1-4 3-5 3l-1-1c0 1-1 2-1 3h0l-1-1 1-3h0c0-3 6-6 8-7 1-1 1-3 2-4l7-6c1-1 1-2 2-3s2-1 3-1h0l1-1c-1-3-1-5-2-7v-1c-1 0-4-4-4-5h0l2 1v-1l1-1h3c1 0 2-1 3-1 2-1 4 0 6 0 1-1 1-1 1 0 1 0 2-1 3-1v-1h0z" class="S"></path><path d="M674 644c0-1 1-2 2-3h3 1l-1 4h0 0l-2 2-5 5-1-1c1 0 2-1 2-2l-1-1 1-1v-3h1z" class="U"></path><path d="M674 644c0-1 1-2 2-3h3c-1 2-2 3-3 4l-2-1z" class="E"></path><path d="M661 634h3l1 2-1 1v3l3 1h0c1 1 2 1 3 1 0 1 1 1 2 1-1 2-2 2-2 3-2 1-4 3-4 5v1h-1c0-2 0-3-1-5 0-2 0-5-2-7h0c-1 0-4-4-4-5h0l2 1v-1l1-1z" class="O"></path><path d="M664 648c0 2-1 5 0 7h0c-1 1-1 1-1 2h0c-4 3-10 6-13 10h0c-1 1-2 1-2 2-1 1-4 3-5 3l-1-1c0 1-1 2-1 3h0l-1-1 1-3h0c0-3 6-6 8-7 1-1 1-3 2-4l7-6c1-1 1-2 2-3s2-1 3-1h0l1-1z" class="P"></path><path d="M660 650c1-1 2-1 3-1-1 2-1 3-2 4h0c-2 2-2 3-4 4l-1 1-1-1c2-1 2-2 3-4 1-1 1-2 2-3z" class="O"></path><path d="M677 631h4 4 0c0 1 0 1 1 2 0 2 0 2-1 3s-2 1-3 2c-2 1-1 2-2 3h-1-3c-1 1-2 2-2 3h-1l-1 1v1h-2c0-1 1-1 2-3-1 0-2 0-2-1-1 0-2 0-3-1h0l-3-1v-3l1-1-1-2c1 0 2-1 3-1 2-1 4 0 6 0 1-1 1-1 1 0 1 0 2-1 3-1v-1h0z" class="e"></path><path d="M677 631h4 4l-1 2h-2c-1 0-2 1-4 2h0c-1-2 0-2-1-4h0z" class="Q"></path><path d="M677 631c1 2 0 2 1 4-1 2-4 3-6 4h-1-1c-1 1-2 1-3 2l-3-1v-3l1-1-1-2c1 0 2-1 3-1 2-1 4 0 6 0 1-1 1-1 1 0 1 0 2-1 3-1v-1z" class="E"></path><path d="M664 634c1 0 2-1 3-1 2-1 4 0 6 0 1-1 1-1 1 0 1 0 2-1 3-1l-2 2-9 5-1-1 1-1-1-1-1-2z" class="P"></path><defs><linearGradient id="r" x1="729.802" y1="460.698" x2="717.502" y2="463.052" xlink:href="#B"><stop offset="0" stop-color="#220907"></stop><stop offset="1" stop-color="#470a0b"></stop></linearGradient></defs><path fill="url(#r)" d="M723 420l1 7s1 1 1 2v2c1 0 2 1 2 2s-1 1 0 2c1 2 2 3 2 6v2c0 1 1 2 1 3h0v-3l2 10v5 9c0 2 1 2 0 4-1 3 0 7-1 10h0c1 1 1 2 1 3v-2l1-1v-3l1 1c0 1 0 1 1 2h1v2 7c1 3 0 7 2 10v1 5c-1-1-1-1-1-2h-1 0c0-1-1-2-1-3l-1-1s-1 0-2-1h0l-2 1c-1 0-1 0-3 1h-1v-3h0-2v-4c-1-2 0-7 0-9v-9c-1-1-1-1-2 0l-1-1c-1-1 0-9-1-11v-1c0-1 0-2-1-3l-1-20c0-4-2-9-2-13l1 1 2-1c1 0 1 1 2 1l1-2 1 1v-3c-1-1 0-3 0-4z"></path><path d="M721 475l2-1h1v-1l-1-1 1-1c0-1 0-1 1 0v-1c1 2 0 12-1 15v-9c-1-1-1-1-2 0l-1-1z" class="a"></path><path d="M725 431c1 0 2 1 2 2s-1 1 0 2c1 2 2 3 2 6v2c0 1 1 2 1 3h0v-3l2 10c0 1-1 1-1 2-2-1-1-1-1-2l-2-2h0v-2c-1-3 0-7-1-9-1-1-1-2-1-3 0-2-1-4-1-6z" class="d"></path><path d="M728 451l2 2c0 1-1 1 1 2 0-1 1-1 1-2v5 9c0 2 1 2 0 4-1 3 0 7-1 10h0c1 1 1 2 1 3v-2l1-1v-3l1 1c0 1 0 1 1 2h1v2 7c1 3 0 7 2 10v1 5c-1-1-1-1-1-2h-1 0c0-1-1-2-1-3l-1-1s-1 0-2-1h0l-2 1c-1 0-1 0-3 1h-1v-3c2-3 1-7 2-11v-9l1-17-1-10z" class="K"></path><path d="M732 467c0 2 1 2 0 4-1 3 0 7-1 10h0c1 1 1 2 1 3v-2c1 2 0 4 0 6-1 0 0 1-1 2l-1 4c-1 1 0 1 0 3l-1-1c0-2 0-3 1-5v-9c1-2 0-4 0-6 0-3 1-6 2-9z" class="R"></path><path d="M733 481v-3l1 1c0 1 0 1 1 2h1v2 7c1 3 0 7 2 10v1 5c-1-1-1-1-1-2h-1 0c0-1-1-2-1-3l-1-1c1-2-1-3-1-5v-1c-1-1-1-3-1-4 1-3 1-6 1-9z" class="O"></path><path d="M701 538l2-2 1-1c1-1 3-2 4-4l1 1-20 55-4 8-3 1-3-2h0v-1l-2-1v-1h-1v1c-3-1-6-4-8-6v-1c0-2 1-1 2-1l1-1c1 0 1 0 2 1h1 1v-1h-2v-1c1-1 2-1 3-2l1 1c1-1 1-2 2-4h1v-1h-1s0-1-1-2c-2-1-2-2-2-4-1 0-1 0-2 1h0-1l1-1-2-1 5-3 5-2c5-4 5-12 8-18 2-3 7-6 11-8z" class="AK"></path><path d="M692 555v-2c1-2 5-3 5-6 1-1 2-2 3-1 0 1-2 3-3 4l-2 3v2c-1 0-1 0-1 1l-2-1z" class="B"></path><path d="M692 555l2-3 1 3c-1 0-1 0-1 1l-2-1zm-3 5c0-3 1-6 2-9v4c-1 1-1 4 0 5 0 1 0 1 1 1l1-1v1l3-1v1h2l-2 3h0c1 1 1 1 0 2h-2c-1 3-1 3-4 4-1 0-1-1-2-2h-2v2c-2 0-4 0-5-1l1-1h2l1-1c1 0 2-1 4-1-1-1-1-2-1-3l1-3z" class="M"></path><path d="M689 560l2 2h1 3v1h0c-3 1-4 5-7 5h-2v2c-2 0-4 0-5-1l1-1h2l1-1c1 0 2-1 4-1-1-1-1-2-1-3l1-3z" class="AC"></path><defs><linearGradient id="s" x1="683.885" y1="550.379" x2="690.489" y2="559.879" xlink:href="#B"><stop offset="0" stop-color="#555557"></stop><stop offset="1" stop-color="#7c7b7c"></stop></linearGradient></defs><path fill="url(#s)" d="M690 546c2-3 7-6 11-8-3 3-9 9-10 13-1 3-2 6-2 9l-1 3c0 1 0 2 1 3-2 0-3 1-4 1l-1 1h-2l-1 1-2 1c-1-1-2 0-3 0h0c-1 0-1 0-2 1h0-1l1-1-2-1 5-3 5-2c5-4 5-12 8-18z"></path><path d="M677 566c0 1 1 1 1 1 5 0 7-2 10-4 0 1 0 2 1 3-2 0-3 1-4 1l-1 1h-2l-1 1-2 1c-1-1-2 0-3 0h0c-1 0-1 0-2 1h0-1l1-1-2-1 5-3z" class="z"></path><path d="M679 594l3 2 3-1c0 1-1 2-1 3v1l-1 1h1c0 5 5 13 8 17h1l2 2c0 1 1 1 2 2h0l2 2 4 4c-1-3-1-5-2-7 0-1 1-2 1-2h0c1 2 1 4 2 6 1 1 1 1 1 2 3 4 5 8 10 10 1 0 2 1 3 1v1c1 0 1 0 1 1h-1c-1-1-3-1-4-2v2c-3 0-6-1-9 0-2-1-4-1-7-1-1 0-1 1-2 1-3 2-5 1-7 3l-3 3-4 4c-1-1-2-1-3-1l-1-1v1l-1-1 2-2h0 0l1-4c1-1 0-2 2-3 1-1 2-1 3-2s1-1 1-3c-1-1-1-1-1-2h0-4-4 0v1c-1 0-2 1-3 1 0-1 0-1-1 0-2 0-4-1-6 0-1 0-2 1-3 1h-3v-1c-1-3-3-1-5-3h0 5l1-1c5-4 9-11 12-16l2-3 3-6 1-1c0-2-1-3-2-5 1-2 1-2 1-3v-1z" class="H"></path><path d="M678 614l1-2 1 1v1 2c0 2-1 3-2 5-1 0-1 1-2 1 1-2 2-4 3-7l-1-1z" class="K"></path><path d="M676 622c1 0 1-1 2-1h2 1 3c0 1 0 1-1 1-2 0-1 1-2 1-2 0-3 1-4 2l-2-1c0-1 0-1 1-2z" class="d"></path><path d="M675 628c1 0 2-1 3-1h1c2-1 3 0 5-1 1 0 2-1 3 0h2c0 1-1 1-2 2l-1-1h0c-1 1-2 0-3 0l-2 2v2h-4l-1-2-2 2h-1l2-3z" class="R"></path><path d="M676 629h2 3v2h-4l-1-2z" class="AA"></path><path d="M685 621c2-3 3 0 6 0l1-1 5 8-2 1c-1-1-2-1-2-2-1-1-2-1-3-2h0l-1-1v-1c-1 0-1-1-2-1s-1 0-2-1h0 0z" class="a"></path><path d="M675 628l-2 3h1l2-2 1 2h0v1c-1 0-2 1-3 1 0-1 0-1-1 0-2 0-4-1-6 0-1 0-2 1-3 1h-3v-1c-1-3-3-1-5-3h0 5c1 1 1 2 3 3 2-1 3-1 5-3l6-2z" class="d"></path><path d="M674 613l2-1c1 1 0 1 0 2s1 1 1 2c-1 2-3 4-4 6l-3 6 1 1h-1l-1 1c-2 2-3 2-5 3-2-1-2-2-3-3l1-1c5-4 9-11 12-16z" class="Q"></path><path d="M679 594l3 2h-2c0 2 0 1 1 2 2 6 5 10 7 15l4 7-1 1c-3 0-4-3-6 0h0-1-3-1-2c1-2 2-3 2-5v-2-1l-1-1-1 2-1 2c0-1-1-1-1-2s1-1 0-2l-2 1 2-3 3-6 1-1c0-2-1-3-2-5 1-2 1-2 1-3v-1z" class="m"></path><path d="M688 613l4 7-1 1c-3 0-4-3-6 0 0-2 0-1 1-2 1 0 2 0 3-1l-1-5z" class="b"></path><path d="M676 610l3-6c2 2 2 2 3 5v1h-1 0v2l-1 4v-2-1l-1-1-1 2-1 2c0-1-1-1-1-2s1-1 0-2l-2 1 2-3z" class="a"></path><path d="M676 610l3-6c2 2 2 2 3 5v1h-1v-2h-1c-1 2-1 3-3 3l-1-1z" class="W"></path><path d="M685 595c0 1-1 2-1 3v1l-1 1h1c0 5 5 13 8 17h1l2 2c0 1 1 1 2 2h0l2 2 4 4c-1-3-1-5-2-7 0-1 1-2 1-2h0c1 2 1 4 2 6 1 1 1 1 1 2 3 4 5 8 10 10 1 0 2 1 3 1v1c1 0 1 0 1 1h-1c-1-1-3-1-4-2v2c-3 0-6-1-9 0-2-1-4-1-7-1-1 0-1 1-2 1-3 2-5 1-7 3l-3 3-4 4c-1-1-2-1-3-1l-1-1v1l-1-1 2-2h0 0l1-4c1-1 0-2 2-3 1-1 2-1 3-2s1-1 1-3c-1-1-1-1-1-2h0-4v-2l2-2c1 0 2 1 3 0h0l1 1c1-1 2-1 2-2 1 1 3 1 4 1 0 1 1 1 2 2l2-1-5-8-4-7c-2-5-5-9-7-15-1-1-1 0-1-2h2l3-1z" class="C"></path><path d="M684 641c1 1 1 3 2 4l-4 4c-1-1-2-1-3-1l-1-1v1l-1-1 2-2 5-4z" class="a"></path><path d="M685 631l1-1 1 1v2h1l1-1v-2h2l2 2c-6 3-9 8-14 13l1-4c1-1 0-2 2-3 1-1 2-1 3-2s1-1 1-3c-1-1-1-1-1-2z" class="E"></path><path d="M701 632c3 2 9 4 13 5v2c-3 0-6-1-9 0-2-1-4-1-7-1 0 0 1-1 2-1s2 0 3-2v-1h-3v-1l1-1z" class="d"></path><path d="M689 626c1 1 3 1 4 1 0 1 1 1 2 2l2-1 1 2c-1 1-4 1-5 2l-2-2h-2v2l-1 1h-1v-2l-1-1-1 1h0-4v-2l2-2c1 0 2 1 3 0h0l1 1c1-1 2-1 2-2z" class="h"></path><path d="M693 617l2 2c0 1 1 1 2 2h0l2 2 4 4c-1-3-1-5-2-7 0-1 1-2 1-2h0c1 2 1 4 2 6 1 1 1 1 1 2 3 4 5 8 10 10h-2c-9-4-16-10-21-19h1z" class="e"></path><path d="M692 633l1-1c3-1 5-1 8 0l-1 1v1h3v1c-1 2-2 2-3 2s-2 1-2 1c-1 0-1 1-2 1-3 2-5 1-7 3l-3 3c-1-1-1-3-2-4l8-8z" class="b"></path><path d="M696 634c1 0 1 1 2 1-2 2-3 3-5 3l-2-2 2-1c1 0 2-1 3-1z" class="h"></path><path d="M692 633l1-1c3-1 5-1 8 0l-1 1v1l-2 1c-1 0-1-1-2-1s-2 1-3 1l-1-2z" class="x"></path><path d="M696 634c1-1 1-2 2-2l2 1v1l-2 1c-1 0-1-1-2-1z" class="N"></path><path d="M727 398h1c1 1 1 2 1 4v2l1 3c1 1 1 2 1 4v-9-1-2h1c0 2 0 2 1 3v-3c1 1 1 0 1 2l1-1v2c1 2 1 3 2 4v2h1l2-1c2 3 3 7 4 11l6 14c1 1 1 2 2 3h1c2 2 3 5 5 7h6v-2l2 1 2 1h2l1 1 4 1c-2 1-5 3-7 4s-4 3-6 4h-1l-5 4v-3l3-2h-7c-1 1-1 1-2 1h-1 0c-1 1-1 1-2 1-1-1-2-2-2-4l1-1h0c-1-1 0-2-1-4 0-1-1-2-2-2v-1h-2-1-1c-1 0-1 0-2 1 0 1 1 2 1 3 1 1 2 4 1 6v1 5c0 2 0 2-1 4-1 3-1 9-1 12v10h-1v-2h-1c-1-1-1-1-1-2l-1-1v3l-1 1v2c0-1 0-2-1-3h0c1-3 0-7 1-10 1-2 0-2 0-4v-9-5l-2-10v3h0c0-1-1-2-1-3v-2c0-3-1-4-2-6-1-1 0-1 0-2s-1-2-2-2v-2c0-1-1-2-1-2l-1-7c-1-2-1-5-1-7 0 1 1 1 1 2l1 2v-2l1 1h0v-3h1v1l1-3h0v-3c-1-3-1-4 0-6h0 0v-4h0z" class="b"></path><path d="M729 432c-1-1-1-2-1-4h0c-1-2-1-4-1-6h1c1 1 1 3 1 5 0 1 1 1 1 2v2 1h-1 0z" class="R"></path><path d="M728 422h1v-1c2 1 2 4 3 5 0 3 3 7 2 9h-1l-1-1-2-3v-2c0-1-1-1-1-2 0-2 0-4-1-5z" class="m"></path><path d="M727 398h1c1 1 1 2 1 4v2l-1 2h1c0 2 0 3 1 5v1 1c1 1 1 1 1 2l-2-3c-1 2 0 5 1 7h0c-1-1-1-2-1-4l-1-1-1-3h0v-3c-1-3-1-4 0-6h0 0v-4h0z" class="x"></path><path d="M738 439v-1c-1-1-1-3-2-4s-1-2-1-3h0v-1-3-1c0-1-1-2-1-3h0c-1-2-1-1-1-2l1-1c0 2 0 3 1 4h1c1 4 3 8 5 12 0 1 1 2 2 3h0c-1 0-2 1-3 2h-1l-1-2z" class="h"></path><path d="M736 424v1c1 1 1 1 2 3l1 1 1 2h1v-1h1c0 1 1 2 2 3h0c1-1 0-2 0-3 1 1 2 1 3 3l1 1h0c0 1 1 1 1 2h1v1c0 1 0 1 1 2l1 4h0c-1 0-1 0-1-1l-1 1h-1l-1-1h-1c-1-1-2-3-4-3h0c-1-1-2-2-2-3-2-4-4-8-5-12z" class="k"></path><path d="M752 435h1c2 2 3 5 5 7 2 0 3 1 4 2h-4l1 2h-8-4l-1 2c-1-1 0-2-1-4 0-1-1-2-2-2v-1h-2-1c1-1 2-2 3-2 2 0 3 2 4 3h1l1 1h1l1-1c0 1 0 1 1 1h0l-1-4c-1-1-1-1-1-2v-1h0l1 1c1 1 1 2 2 2h0c0-2-1-3-1-4z" class="E"></path><path d="M744 430c-3-4-4-8-6-12l1-1c1 0 2 0 3 1h2l6 14c1 1 1 2 2 3 0 1 1 2 1 4h0c-1 0-1-1-2-2l-1-1h0-1c0-1-1-1-1-2h0l-1-1c-1-2-2-2-3-3z" class="K"></path><path d="M764 440l2 1 2 1h2l1 1 4 1c-2 1-5 3-7 4s-4 3-6 4h-1l-5 4v-3l3-2h-7c-1 1-1 1-2 1h-1 0c-1 1-1 1-2 1-1-1-2-2-2-4l1-1h0l1-2h4 8l-1-2h4c-1-1-2-2-4-2h6v-2z" class="U"></path><path d="M764 440l2 1 2 1h2l1 1v1c-3 0-7-1-9 0-1-1-2-2-4-2h6v-2z" class="n"></path><path d="M759 446h6c-2 1-5 3-6 5h-7c-1 1-1 1-2 1h-1 0c-1 1-1 1-2 1-1-1-2-2-2-4l1-1h0l1-2h4 8z" class="Q"></path><path d="M746 448h2l2-1 1 2h-1c0 1-1 2-1 3-1 1-1 1-2 1-1-1-2-2-2-4l1-1z" class="h"></path><path d="M759 446h6c-2 1-5 3-6 5h-7c2-1 3-2 5-2h2c-1-1-1-1-1-3h-6-1 8z" class="O"></path><path d="M730 431l2 3v1c0 1 0 2-1 3v4c1-2 1-2 1-4l1 1v4h0l1-4v2l1 1v2h0v-4-2l1-1 2 2 1 2c-1 0-1 0-2 1 0 1 1 2 1 3 1 1 2 4 1 6v1 5c0 2 0 2-1 4-1 3-1 9-1 12v10h-1v-2h-1c-1-1-1-1-1-2l-1-1v3l-1 1v2c0-1 0-2-1-3h0c1-3 0-7 1-10 1-2 0-2 0-4v-9-5l-2-10v-1c1-1 0-2 0-3v-1h1v-1c0-1 0-1 1-2l-1-1h-1v-1h-1v-1h0 1v-1z" class="AA"></path><path d="M735 440v-2l1-1 2 2 1 2c-1 0-1 0-2 1 0 1 1 2 1 3 1 1 2 4 1 6v1 5c0 2 0 2-1 4v-3-2-2h-3c0-4 0-6 2-10h0l-2-4z" class="x"></path><path d="M737 444c1 4 2 10 1 14v-2-2h-3c0-4 0-6 2-10z" class="K"></path><path d="M735 454h3v2 2 3c-1 3-1 9-1 12v10h-1v-2h-1c-1-1-1-1-1-2l-1-1 1-7c0-3-1-8 0-10 0-1 1-2 1-2v-5z" class="H"></path><path d="M735 454h3v2l-1-1c-2 1-1 3-1 5h0c0 3 0 6-1 8h0c0 1-1 2-1 3 0-3-1-8 0-10 0-1 1-2 1-2v-5z" class="k"></path><path d="M753 342c4-3 9-5 14-7 7-1 13-1 19 2 1 1 2 1 2 2l1 1c-14 6-24 19-30 32-4 11-5 21-4 33 1 1 1 2 1 3 1 4 1 8 1 11v2c0 2 0 5 1 7 0 2 0 2-1 3 2 2 5 4 6 5 1 2 4 4 6 5l1 1h-2l-2-1-2-1v2h-6c-2-2-3-5-5-7h-1c-1-1-1-2-2-3l-6-14c-1-4-2-8-4-11l-3-17v-14c1-13 5-26 15-34h1z" class="AG"></path><path d="M737 390v-14c1-13 5-26 15-34h1c-10 10-14 20-14 34 0 17 3 35 13 49 2 2 3 5 5 6 2 2 5 4 6 5 1 2 4 4 6 5l1 1h-2l-2-1-2-1v2h-6c-2-2-3-5-5-7h-1c-1-1-1-2-2-3l-6-14c-1-4-2-8-4-11l-3-17z" class="o"></path><path d="M752 429c0-1 0-1-1-2 0-1-1-2-1-3l2 1c2 2 3 5 5 6 2 2 5 4 6 5 1 2 4 4 6 5l1 1h-2l-2-1-2-1v2h-6c-2-2-3-5-5-7-1-3-3-6-3-9l2 3z" class="H"></path><path d="M753 435c-1-3-3-6-3-9l2 3c3 4 7 9 12 11v2h-6c-2-2-3-5-5-7z" class="d"></path><path d="M757 421c-1-2-1-4-2-6l-1-2c-3-7-9-30-6-37 2 1 3 4 4 6 0-3 0-6 1-9 2-10 8-19 14-27 2-2 3-3 6-3 0-1 1-1 1-1l8-4c2 0 3 0 4-1 1 1 2 1 2 2l1 1c-14 6-24 19-30 32-4 11-5 21-4 33 1 1 1 2 1 3 1 4 1 8 1 11v2z" class="T"></path><path d="M755 405c0-1 0-1-1-2-1-3-1-6-1-9 0-9 2-16 6-24 6-14 15-26 29-31l1 1c-14 6-24 19-30 32-4 11-5 21-4 33z" class="j"></path><defs><linearGradient id="t" x1="699.412" y1="593.76" x2="720.916" y2="587.577" xlink:href="#B"><stop offset="0" stop-color="#b21412"></stop><stop offset="1" stop-color="#d82c29"></stop></linearGradient></defs><path fill="url(#t)" d="M710 525c2 0 2 0 3 1h1c1 0 1 1 1 1h-4c0 2 0 2 1 3v1h1l1 1c2 0 2 0 4-2l1-1h1c0 2-1 3-2 5h0l-1 3c0 2-1 3-1 4l1 2 1-2c1-1 1-3 2-5h0c1-1 1-1 1-3s1-4 2-6c1 0 2 1 3 1 0-1 1-2 2-2l2 1c0 1 1 1 3 1h2c1 1 3 1 4 1h1l-6 2c-2 1-5 3-7 5v2l-2 2c1 1 1 2 3 2h0l-3 4v1l-5 9c0 1-1 1-2 2v4c-1 2-1 3-1 5h0c-1 1-1 2-2 4l-2 8c0 1 1 2 1 4l-1 1 1 2v2c-1 2-1 7-1 10l1 1c0 1 0 1-1 3v2c0 2 0 3-1 4v6c0 3 1 7 0 10 0 2 1 4 0 6l-1 1-5-6-1 1c0-1 0-1-1-2-1-2-1-4-2-6h0s-1 1-1 2c1 2 1 4 2 7l-4-4-2-2h0c-1-1-2-1-2-2l-2-2h-1c-3-4-8-12-8-17h-1l1-1v-1c0-1 1-2 1-3l4-8 20-55 1-2v-5z"></path><path d="M725 540c1 1 1 2 3 2h0l-3 4h-2-1v-1c1-2 2-3 3-5z" class="d"></path><path d="M703 605h1c2 2 0 4 2 6 1 2 1 5 1 6l2 2v1c0 1-1 2-2 3l-2-6c0-2 0-4-1-6-1-1-1-1-1-2v-4z" class="AB"></path><path d="M722 545v1h1 2v1l-5 9c0 1-1 1-2 2l-1 1v1c-3 3-4 7-6 11 0-1 1-3 1-4l2-4v-2c1-1 1-1 1-2l3-6c1-3 2-5 4-8z" class="k"></path><path d="M702 583c1-1 2-3 3-4v2h1c-1 3-3 6-2 8 0 2-1 6-2 7v2l-2 2c0-1 1-3 1-4-1 0-1-1-2-2v-1c-1 1-2 2-2 3l-1-1c0-1 0-1 1-2 0-1 1-3 2-4 0-1 0-2 1-3 0-1 0-2 1-3h1z" class="H"></path><path d="M700 586h0l2-2v2c0 1 0 1-1 2 1 4-4 4 1 8v2l-2 2c0-1 1-3 1-4-1 0-1-1-2-2v-1c-1 1-2 2-2 3l-1-1c0-1 0-1 1-2 0-1 1-3 2-4 0-1 0-2 1-3z" class="o"></path><path d="M700 600l2-2c0 1 0 2-1 3v6c1 0 1-1 2-2v4c0 1 0 1 1 2 1 2 1 4 1 6l-1 2h1l-1 1-1-1-1-1h0s-1 1-1 2c-2-3-2-7-3-11 0-1 1-2 1-4 1 0 0-2-1-2h2v-3z" class="AP"></path><path d="M698 603h2c0 5 0 9 2 15 0 0-1 1-1 2-2-3-2-7-3-11 0-1 1-2 1-4 1 0 0-2-1-2z" class="H"></path><path d="M711 553l1 3-2 3c1 1 3 1 3 2l-7 20h-1v-2c-1 1-2 3-3 4h-1v-1c0-1 1-2 1-3l1-1v-1c0-1 1-2 1-3 1-3 2-4 2-7l1-2c1-4 2-8 4-12z" class="O"></path><path d="M711 553l1 3-2 3-7 20v2c-1 0-1 1-1 2h-1v-1c0-1 1-2 1-3l1-1v-1c0-1 1-2 1-3 1-3 2-4 2-7l1-2c1-4 2-8 4-12z" class="S"></path><path d="M697 596c0-1 1-2 2-3v1c1 1 1 2 2 2 0 1-1 3-1 4v3h-2c1 0 2 2 1 2 0 2-1 3-1 4 1 4 1 8 3 11 1 2 1 4 2 7l-4-4-2-2h0c-1-1-2-1-2-2l-2-2-1-3 1-1 1 1 1-1v-1l-1-1-2-2v-3l1-1c0-1 0-2 1-2h0l1-2s1-1 1-2l1-3z" class="Z"></path><path d="M696 614v-5c0-2 1-4 1-5v-1h1c1 0 2 2 1 2 0 2-1 3-1 4l-2 1v1 3z" class="Q"></path><path d="M696 614v-3-1l2-1c1 4 1 8 3 11 1 2 1 4 2 7l-4-4-1-2v-1c-1-2-1-4-2-6z" class="K"></path><path d="M723 527c1 0 2 1 3 1 0-1 1-2 2-2l2 1c0 1 1 1 3 1h2c1 1 3 1 4 1h1l-6 2c-2 1-5 3-7 5-7 7-10 16-14 25 0-1-2-1-3-2l2-3c1-2 2-5 3-8v1-1c1-2 1-3 2-5l1-2c1-1 1-3 2-5h0c1-1 1-1 1-3s1-4 2-6z" class="k"></path><path d="M717 543l1-2c1-1 1-3 2-5v4-1h2l-3 3c-1 2-2 6-4 7h0v-1c1-2 1-3 2-5z" class="m"></path><path d="M723 527c1 0 2 1 3 1 0-1 1-2 2-2l2 1c0 1 1 1 3 1h2c-1 1-1 2-2 3-2-1-2-1-3 0-1 0-1 1-2 1 0 2-1 2-3 3 0 1-2 3-3 4h-2v1-4h0c1-1 1-1 1-3s1-4 2-6z" class="N"></path><path d="M708 595c1-2 2-4 3-5 0-3-1-6 1-8l1-3c0 1 1 2 1 4l-1 1 1 2v2c-1 2-1 7-1 10l1 1c0 1 0 1-1 3v2c0 2 0 3-1 4v6c0 3 1 7 0 10 0 2 1 4 0 6l-1 1-5-6-1 1c0-1 0-1-1-2-1-2-1-4-2-6l1 1 1 1 1-1h-1l1-2 2 6c1-1 2-2 2-3v-1c-1-3-1-6-2-9-2-4 0-11 1-15z" class="F"></path><path d="M709 620c1 2 2 4 2 6l-1 1c-1-1-3-2-3-4 1-1 2-2 2-3z" class="AP"></path><path d="M708 595c1-2 2-4 3-5 0-3-1-6 1-8l1-3c0 1 1 2 1 4l-1 1 1 2v2c-1 2-1 7-1 10l1 1c0 1 0 1-1 3v2c0 2 0 3-1 4v6l-1-3c-1-3-1-8 0-10 1-3 1-6 0-8v-1c-2 2 0 4-2 6l-1-3z" class="U"></path><path d="M710 525c2 0 2 0 3 1h1c1 0 1 1 1 1h-4c0 2 0 2 1 3v1h1l1 1c2 0 2 0 4-2l1-1h1c0 2-1 3-2 5h0l-1 3c0 2-1 3-1 4l1 2c-1 2-1 3-2 5v1-1l-3 8-1-3c-2 4-3 8-4 12l-1 2c0 3-1 4-2 7 0 1-1 2-1 3v1l-1 1c0 1-1 2-1 3v1c-1 1-1 2-1 3-1 1-1 2-1 3-1 1-2 3-2 4-1 1-1 1-1 2l1 1-1 3c0 1-1 2-1 2l-1 2h0c-1 0-1 1-1 2l-1 1v3l2 2 1 1v1l-1 1-1-1-1 1 1 3h-1c-3-4-8-12-8-17h-1l1-1v-1c0-1 1-2 1-3l4-8 20-55 1-2v-5z" class="H"></path><path d="M718 530l1-1h1c0 2-1 3-2 5h0l-1 3c0 2-1 3-1 4l1 2c-1 2-1 3-2 5v1-1l-3 8-1-3c-2 4-3 8-4 12h-1v1h-1c-2 1-3 4-3 6-1 0-1 0-1 1 0-3 1-6 3-9v-3-1l6-18c1-3 3-8 2-11h1l1 1c2 0 2 0 4-2z" class="K"></path><path d="M713 543c1 2 1 4 2 5l-3 8-1-3c0-3 1-7 2-10z" class="n"></path><path d="M713 543c1-3 2-7 4-10l1 1-1 3c0 2-1 3-1 4l1 2c-1 2-1 3-2 5v1-1c-1-1-1-3-2-5z" class="H"></path><path d="M690 591c1-2 2-3 3-4 2-3 3-8 5-11v-2c1-1 1-1 2-1 1-2 1-5 2-7s1-3 2-5v3c-2 3-3 6-3 9 0-1 0-1 1-1 0-2 1-5 3-6h1v-1h1l-1 2c0 3-1 4-2 7 0 1-1 2-1 3v1l-1 1c0 1-1 2-1 3v1c-1 1-1 2-1 3-1 1-1 2-1 3-1 1-2 3-2 4-1 1-1 1-1 2l1 1-1 3c0 1-1 2-1 2l-1 2h0c-1 0-1 1-1 2l-1 1v3l2 2 1 1v1l-1 1-1-1-1 1 1 3h-1c-3-4-8-12-8-17h-1l1-1v-1c0-1 1-2 1-3l4-8c1 1 1 2 1 4z" class="P"></path><path d="M689 587c1 1 1 2 1 4-1 3-2 5-4 8h-1l-1-1v1-1c0-1 1-2 1-3l4-8z" class="S"></path><path d="M684 600c1 1 1 2 2 4 1 1 1 2 2 3s1 2 1 3l1 1h0l2 2c0-1 1-1 1-1l-1-3 2 2 1 1v1l-1 1-1-1-1 1 1 3h-1c-3-4-8-12-8-17z" class="O"></path><path d="M694 603v-4c-2 2-2 3-2 5v1c-2-5 2-11 4-15 1-4 3-8 4-12v-1c2-2 3-5 4-8 0-1 1-1 2-2 0 3-1 4-2 7 0 1-1 2-1 3v1l-1 1c0 1-1 2-1 3v1c-1 1-1 2-1 3-1 1-1 2-1 3-1 1-2 3-2 4-1 1-1 1-1 2l1 1-1 3c0 1-1 2-1 2l-1 2h0z" class="n"></path><defs><linearGradient id="u" x1="636.314" y1="541.955" x2="664.6" y2="551.332" xlink:href="#B"><stop offset="0" stop-color="#4f4f50"></stop><stop offset="1" stop-color="#7d8085"></stop></linearGradient></defs><path fill="url(#u)" d="M721 336l1 1v-1-1h2l3 3h1c0 1 1 1 2 2v-1l1-1v1 2 3l1 1 1-1c1 0 0 0 1 1l-98 258-37 102-14 38c-1 3-2 7-4 10 0 2-1 4-2 5s-2 1-2 2h-6l-1-1h-1l-4 2-1 1v-1c1-3 2-6 2-9l3-8 1-2c0-1 1-4 2-5l5-16 94-256 27-71 19-51v-1c0-1 0 0-1-1h0l5-5z"></path><path d="M699 422v1c-1 3-3 5-4 8s0 6-2 9c-2 1-3 3-3 5 0 3-2 4-3 7-1 1-1 3-1 4l-4 4s-1 0-2 1h0-2v1l-1 1c1-6 3-12 6-17l5-12 2 1 9-13z" class="AW"></path><path d="M581 727l26-79 2-4 34-91 2-4h0l-7 18-11 32 2 2h1c1 0 2 0 2 1h-1-3 0c1 1 2 1 4 1 0 2 0 3-1 4h1 1s1-1 1-2v-1h1l1-1-37 102-14 38c-1 3-2 7-4 10 0 2-1 4-2 5s-2 1-2 2h-6l-1-1h-1l-4 2-1 1v-1c1-3 2-6 2-9l3-8 1-2-1 4v1 1c-1 1-1 0-1 1l2 1c1-1 1-2 3-3 2-3 3-9 4-13 1-2 3-4 4-7z" class="AE"></path><path d="M570 742l-1 4v1 1c-1 1-1 0-1 1l2 1h-2v1h3v1l-2 2-2-1-1-1 3-8 1-2zm61-135h-1c-1 0-3 0-4-1v-1-2l1-1 1-1v1c1 1 2 1 4 1 0 2 0 3-1 4z" class="t"></path><path d="M569 759c0-1 0-1 1-2h1 3c2-1 4-3 5-5v-1 1c0 3-2 4-4 6h1 1 1c1-1 1-2 2-3 0-1 0-1 1-2h0c0 2-1 4-2 5s-2 1-2 2h-6l-1-1h-1zm12-32c1 1 1 1 2 1-1 0-2 0-3 1l1 1c2 1 3 2 5 4h0c-2 1-4 2-4 3-2 3-1 6-3 9-1 1-3 0-4 0-1 1-1 1-1 2l-1-1c2-3 3-9 4-13 1-2 3-4 4-7z" class="j"></path><path d="M709 353l4-8c1-1 2-2 4-2l-19 51-27 71-94 256-5 16c-1 1-2 4-2 5l-1 2h-3l-1 1c-1-1 0-2 0-3-3-4-5-8-8-11l-1-2c-2-2-3-3-4-6v-1c0-2-1-5 0-8 0 2 1 2 2 4 0-5-1-9-2-13v-3c0-2-1-2-2-4h-1l1-7c1-5 4-10 6-15l12-35 53-146 18-49 5-13c1-3 2-7 3-9l1-2-1-1c-1 1-3 1-5 1s-4-1-6-3c-1-1-2-3-2-5 0 1 1 2 1 3 1 2 3 3 5 4h1 2c2 0 3-1 5-2l1 1c3 0 5-2 8-3 0-2 1-3 1-4 3-2 6-6 9-8l3-4 5-7 1 1 3-2 4-3v-1c3-2 3-5 5-8s4-5 6-8c1-3 1-6 1-9-1-2 0-6 1-9v-1l1-1c0-1 1-1 2-1h0c1-1 2-1 4-1h0c1 0 1 0 2-1 1 0 2 0 3-1l1 1v3l-1 1h1 0s1-1 0-1z" class="u"></path><path d="M559 730l2 1c1-1 0-2 0-4v-2h1c1 1 1 2 1 3 0 2-1 3-1 4s1 2 1 3v2l-4-7z" class="L"></path><path d="M697 353c0-1 1-1 2-1h0c1-1 2-1 4-1-1 1-1 1-1 2-1 2-2 3-3 5-1 3-1 7-2 10h-1c0-1 0-2-1-4s0-6 1-9v-1l1-1z" class="G"></path><path d="M563 737c1 1 2 3 2 4l1-1c1-6 4-12 6-18l30-83 63-170 11-28c0-2 2-8 3-9l1 1h-1l-21 58-14 36-8 22-15 39-30 83-13 38-7 20c-2 4-4 10-5 15l-1 1c-1-1 0-2 0-3-3-4-5-8-8-11l-1-2c-2-2-3-3-4-6v-1c0-2-1-5 0-8 0 2 1 2 2 4 1 4 3 8 5 12l4 7z" class="AH"></path><path d="M695 364c1 2 1 3 1 4h1c0 3-1 5-2 8-1 1-2 2-2 4-1 5-7 9-6 14l-1 1v1c-1 0-1 1-2 3-1 4-3 7-4 11 0-1 0-1 1-2v-1-1c0-1 1-3 1-4 1-1 0-1 1-2 0-1 1-2 1-4l-2 2c0 2-1 4-2 6h0v-1h-1c-2 2-4 5-7 6-3 4-7 8-11 10-1 1-3 2-5 2-3 2-5 3-9 3l1-2-1-1c-1 1-3 1-5 1s-4-1-6-3c-1-1-2-3-2-5 0 1 1 2 1 3 1 2 3 3 5 4h1 2c2 0 3-1 5-2l1 1c3 0 5-2 8-3 0-2 1-3 1-4 3-2 6-6 9-8l3-4 5-7 1 1 3-2 4-3v-1c3-2 3-5 5-8s4-5 6-8c1-3 1-6 1-9z" class="D"></path><path d="M670 401l4-1c-1 1-2 3-4 5h-3l3-4z" class="AL"></path><path d="M675 394l1 1 3-2c-2 2-3 5-5 7l-4 1 5-7z" class="q"></path><path d="M684 391h0 1c-1 4-3 7-7 9-1 1 0 1-1 0l4-6 3-3z" class="u"></path><path d="M661 419v-1l14-15c0 1-1 3-2 4 0 1 0 1-1 2-3 4-7 8-11 10z" class="L"></path><path d="M667 405h3c-3 4-8 9-13 12 0-2 1-3 1-4 3-2 6-6 9-8z" class="v"></path><path d="M103 98c-3 0-6-1-8-2h30 49 147 89 24 13l-14 3c-24 6-48 14-68 27-9 6-18 13-23 21-3 4-4 9-6 13-3 11-5 21-2 33 1 4 2 9 3 13v1c1 0 1 1 1 2v2h1l20 53 3 9 25 63 10 25c1 2 3 6 3 8h0v9h-1v1c0 1 0 2-1 3l-2 7-1 16v1c-1 1-1 3-2 3h-1c1 5 14 36 16 37 1 1 3 1 4 3 1 3 0 8 1 11 3 11 7 21 11 31 2 3 3 7 5 10 1-3 3-6 5-9 1-1 6-4 7-6h0c2-4 0-9 1-13h0c2-3 2-7 2-11h1v5c0 2-1 4-1 7l-1 2c0 4 4 8 4 11l1 3 19 49c1 2 2 4 2 7v8c0 3 1 6 1 8v1c2-3 0-6 1-8v3l1 21 1 11 1 1v6h-1l-1 1c-1 0-1 0-2-1l-3-1v1 2h0c-1 0-2-1-2-1 0 3 0 4 1 7h0c-1-1-1-1-2-3h-1c1 2 1 3 1 5l4 10v2l2 5 1 4 1 6c1 2 1 3 2 5h0c0 2-1 5 1 7 1-1 0-3 0-4h1v3c0 2 1 3-1 5h-1c-1 2 0 3 0 5 0 1 0 1 1 2 0 1 0 4-1 5 1 1 2 1 3 1h1 0c-1 3-3 4-5 6l-5 4-2 3c-2 0-3 1-3 3-1 0-1 1-1 2s0 2-1 2l-1-3c-1-6 0-12 1-18h0-2l-1-1c0-5-2-10-4-15-5-14-10-27-16-40-2-4-3-9-5-13-2-2-5-7-7-7l-1-1c1 0 1 0 2 1 2 0 5 2 7 2h0c-5-3-8-4-13-4v1c-1 2-5 4-7 5h-2-1c0 2-1 3-1 4-2 3-5 4-8 6 0 1-2 2-3 3-2 2-5 4-6 7v1c-2 3-1 9-1 13 0 3 1 6 0 8-2-2-1-15-2-19l-1-1c1-1 1-3 1-4l2-2-18-46 1-1v-1c-2-1-2-2-2-4l-6-13-17-47c-1-3-2-6-4-8-1-3-3-7-4-10 0-1-1-2-1-3l-1-3v-1h-1-1v1h-1v4c-1 2 0 2-1 4-1 1-2 2-2 3-1 1-1 4-1 5v-1c-1-1-1-3-1-4v-8-2c-1-1-1-1-1-2 0-2 1-5 2-7l1-3c0-1-1-2-1-2-1-2 0-2 0-3-1 0-2-1-3-2 1-1 2-1 3-1l-1-2h0l-17-45c-1 0-1 1-1 1-1-2-1-3-3-4v-1c-1-1-1-2-2-3l-4-9v1h-1c-2-4-4-7-7-10l-1 1c-4-5-8-7-15-10 0-4 1-6-1-10h0c0 3 0 4-1 7-1 1-1 3-2 4-1 2-1 3-3 5-2 0-3 0-5-1l-4 1c-2-1-4-2-6-4l-14-6c7 2 13 5 20 6h1l1-1c0-1-1-1-1-2s0-2 1-4l3 3 2 2c2-1 4-2 6-4 0-1 1-2 1-3-1-6-4-11-6-17-3-8-7-17-10-26h0l-3-9-2-2h-1v-1c-3-2-7-3-10-5h0c-1-1-1-1-1-2 2-1 5-2 7-3l1-2h0l-14-39c-1-2-2-6-4-8-1-1-4-2-5-3v-1c1 1 2 2 4 2h1c-3-5-5-12-7-18l-10-26-8-17c-11-23-26-44-46-59-10-7-21-13-33-18-9-4-19-7-29-11z" class="c"></path><path d="M341 304c0 1 2 4 2 5-1 1-1 2-1 3h0-1v-2c-1-2 0-4 0-6z" class="D"></path><path d="M285 301h0c1 3 2 5 2 7l-1 1c0-1-1-1-1-2s0-2-1-4c0-1 0-1 1-2z" class="u"></path><path d="M347 101c2-1 7-1 9-1v1c-2 0-3 0-5 1h-5l1-1z" class="C"></path><path d="M321 289c1 4 3 8 2 12-2-5-2-8-2-12z" class="I"></path><path d="M356 100h1c3-1 10-1 13 0-6 2-13 3-19 2 2-1 3-1 5-1v-1z" class="G"></path><path d="M408 448h1s1 0 1 1c1 1 1 3 1 4 0 3 0 6 1 9-3-3-4-10-4-14z" class="C"></path><path d="M336 438c1 3 2 7 3 11l-1 1h-1l-2 1 1-3c-1-3-1-6-1-10h1z" class="AH"></path><path d="M340 469c1-1 1-1 1-2 1-2 0-8 0-10-1-2-1-5 0-6 2 2 2 10 2 13h0c1 0 2 1 3 1-1 1 0 1-1 1l-2 2h-1l-2 2v-1z" class="L"></path><path d="M225 163l1-1c2 1 4 6 5 9 0 1 1 3 1 4v2h0l2 7h-1c-1-4-4-8-5-12 0-3-2-7-3-9zm108 253l1-1v-3h0l1-1v2c0 1 1 1 1 2v1 1c0 1 0 1 1 2v2c0 1 0 1 1 2v1 1h-1c-1 1-1 2-1 3s0 0-1 2v2l-2-16zm-9-104l1-1c1 3 1 7 2 10l2 18c-1 1 0 3-1 4l-4-31z" class="D"></path><path d="M321 364c0 8-1 16 0 23 1 2 0 2 0 4-1 1-1 2-1 3h3l-1 1h-1-1c-2-2-2-10-3-13h1l1 1v-9l1-1v-2-4c0-1 0-1 1-3z" class="AQ"></path><path d="M359 383l12 26h0-1 0v1l1 1s0 1-1 2c0-4-3-10-5-14-3-5-6-10-6-16z" class="I"></path><path d="M340 438c1 0 1-1 2 0 0 0 1 0 2 1h1 2v1h0v1h2l-1 1c0 3 3 4 4 6v1c0 1 1 2 1 3l-1 1-1-3v-1c-1-2-1-2-2-3s-1-2-2-3h-1v1l1 2 1 1c0 1 0 1 1 2l-1 1v-1c-1-1-1-2-2-3l-1-2-1-1-1-1 1-1h1s1 0 1-1h-1-1-1c-1 0-1-1-2-1v1c0 1 1 1 1 2 0 0 0 1 1 2 1 0 0 0 1 1l1 2v1c1 1 2 3 3 5v1c1 1 1 2 2 3v2l-1-2h-1v-2c-3-6-7-11-8-17z" class="D"></path><path d="M236 174l9 22h0l-1-1h0v3h1-1s-1-1-1-2v1s0 1 1 2v1c0 1 0 1 1 2v1l1 1-1 1-10-23c1 1 2 2 2 3h1v-3c-1-2-1-3-2-5v-3z" class="C"></path><path d="M378 399l17 38h-1c-5-3-8-15-10-21-1-2-3-4-3-7l-1-2c-1-2-2-5-3-7l1-1zm-19-54c0 1 1 1 1 1 1 5 3 9 5 14l14 35v-1c-3-2-4-7-6-10l-9-21c-1-4-3-7-4-11-1-2-1-4-1-7z" class="I"></path><path d="M324 312l-12-40 6 9c2 3 2 5 3 8 0 4 0 7 2 12l2 10-1 1z" class="C"></path><path d="M348 321c1 0 1 1 1 1l6 16c1 3 1 6 2 9 1 5 3 10 5 14l1 3v1 1c0 1 0 1 1 2v2h0l-1-2c-3-5-5-10-6-15-1-2-2-5-2-8s-1-6-2-10l-3-6h0l-3-8h1z" class="L"></path><path d="M376 399c-3-8-8-15-13-22-2-4-5-8-7-12-1-2-3-5-4-8s-3-6-3-9h0l1-1c3 7 5 14 9 20 1 3 4 6 6 8 5 8 10 16 13 24l-1 1-1-1z" class="C"></path><path d="M220 154c-3-3-6-9-8-13h1v1c1 1 1 2 2 3h1v-6-2c1-3 2-4 3-6s2-3 4-5h0c-1 1-1 1-2 1h0c3-3 7-6 12-8 2-1 3-1 5-2h4c-1 0-2 1-4 1l-7 4-2 1c-5 3-10 7-12 13v1 2c-1 1-1 1-1 2l1 1h-1l1 2c1-2 1-4 2-6l1-1c0-2 1-2 3-4-2 3-3 5-4 7v1c-1 4 0 9 1 13z" class="D"></path><path d="M279 167h0c1 1 1 1 1 3 0 1 1 2 2 3 0 4 1 8 2 12 1 5 0 9 1 13 0 1 0 2-1 4l-3-15c-2-4-3-10-3-15l1-5zm45 70c1 4 1 8 0 12l1 1v1c-1 0-2 0-3 1l-1 2-1-1c-2 0-3 2-4 4 1 1 1 2 1 3l-1 1c-1 1-1 1-1 2l-2 2v3h-1c-1-2-1-4-2-6 1-4 3-8 7-11 1 0 2-1 3-2 0-1 1-1 2-1 2-3 1-7 2-11h0z" class="C"></path><path d="M324 237c1 4 1 8 0 12l1 1v1c-1 0-2 0-3 1l-1 2-1-1c-2 0-3 2-4 4s-1 4-2 5h-1-1c2-4 3-7 6-10l4-4h0c2-3 1-7 2-11h0z" class="i"></path><path d="M321 254l1-2c1-1 2-1 3-1-1 1-1 2-2 4 0 1 2 6 3 8 3 9 7 18 11 27 2 5 6 10 7 16l3 12 1 3h-1l-1-4c-2-3-3-5-3-8 0-1-2-4-2-5l-20-50z" class="G"></path><path d="M315 320h0c2-3 4-5 5-8h0c1 5-4 9-3 14l3 16c1 6 1 11 1 17v5c-1 2-1 2-1 3v4 2l-1 1c-2-6-2-12 0-18 1-3 1-5 1-8v-1c-1-2-1-5-1-7s-1-5-2-7c0-3-1-5-2-7-1-1-3-1-4-1l1-2h1v1h0 1c1-1 1-2 1-4z" class="AH"></path><path d="M400 448h1c3 5 6 9 8 14 3 5 5 11 7 16 3 8 6 15 8 23h0c-7-10-10-22-15-32-2-7-6-15-9-21z" class="C"></path><path d="M329 360l1 1 1 7c0 1 0 1 1 2 1 6 4 11 6 16h-1l-1-2-1-2c0-1-1-2-1-3-1-1-1-2-1-3l-1-1v-2l-1-1v3c1 1 1 1 1 2v1 1l1 1v2l1 1c0 1 0 3 1 3l1-1v1c2 3 3 7 5 11l6 9c2 3 4 7 6 10v1c1 2 0 4 0 6v-1-1l-2-2c-4-10-11-18-15-28l-2-4c-4-7-5-19-5-27z" class="L"></path><path d="M320 395h1l2 5 3-1c1 2 1 5 2 7l1 3c2 2 3 0 4 3v4l2 16 1 6h-1l-1-1c0-1-1-3-1-5 0-4 0-9-1-13h0c-2-4-5-7-7-10-3-3-5-7-6-12l1-2z" class="AL"></path><path d="M323 400l3-1c1 2 1 5 2 7l1 3c-1 0-2-1-3-2h0c-2-1-3-2-3-4h0c1 1 2 3 4 4-2-2-3-4-4-7z" class="I"></path><path d="M332 449l1 2h2l2-1h1l-3 3v1c1 1 1 3 1 5 1 3 2 6 2 10v1h-1v4c-1 2 0 2-1 4-1 1-2 2-2 3-1 1-1 4-1 5v-1c-1-1-1-3-1-4v-8-2c-1-1-1-1-1-2 0-2 1-5 2-7l1-3c0-1-1-2-1-2-1-2 0-2 0-3-1 0-2-1-3-2 1-1 2-1 3-1l-1-2h0z" class="v"></path><path d="M332 473l1-3c0-1 1-2 2-3 0 2 0 4-1 6-1 0-1 0-1 1-1 2 0 5 0 7h-1 0v-8z" class="q"></path><path d="M334 473c2-1 1 0 2-1v-8-1c-1-1-1-2-2-4l1-1 1 1h0c1 3 2 6 2 10v1h-1v4c-1 2 0 2-1 4-1 1-2 2-2 3-1 1-1 4-1 5v-1c-1-1-1-3-1-4h0 1c0-2-1-5 0-7 0-1 0-1 1-1z" class="AH"></path><path d="M231 148l-1-1c0-1 0-1 1-1l-1-1 1-1v-1h0c0-2 0-2 1-3v-1c1-3 5-6 7-8h1c1-1 1-1 2-1 4-2 7-3 11-4 4-2 8-1 12 0h1c4 1 7 2 11 4 1 0 4 1 5 2s2 3 2 4c0 11-4 20-5 31l-1 5c-1-1-1-3-1-5 1-1 1-3 1-4v-3h0c1-1 1-2 1-3h-1l1-7c1-1 1-1 1-2 0-2 1-5 0-7-1-3-6-8-9-9-1-1-2-1-3-2h0c2 0 3 1 5 2 3 1 5 4 6 6l1 1c2 3 1 5 2 9v-2c1-1 0-1 0-2 1-1 1-1 1-2 1-2 0-4 0-6-2-3-3-4-6-5l-1-1h-2c-1-1-2-1-3-2h0-1-1c-1 0-1 0-2-1h-3c-2-1-6-1-8-1s-3 1-5 1c-2 1-5 2-7 3l-2 1c-3 1-6 6-8 8l1 1c-1 1-2 2-3 4h0c-1 1-1 1-1 2v1 1z" class="L"></path><path d="M315 320c0 2 0 3-1 4h-1 0v-1h-1l-1 2c1 0 3 0 4 1 1 2 2 4 2 7 1 2 2 5 2 7s0 5 1 7l-1-1c0-4-2-8-5-11-1 1-2 0-4 0-3-1-7-1-10-3-1-3-2-4-4-5v-1l2 1 2-2h2c5 0 9-2 13-5z" class="c"></path><path d="M315 320c0 2 0 3-1 4h-1 0v-1h-1l-1 2h-1c-3 1-8 1-11 1l4 4c3 3 8 2 11 5-1 1-2 0-4 0-3-1-7-1-10-3-1-3-2-4-4-5v-1l2 1 2-2h2c5 0 9-2 13-5z" class="S"></path><path d="M314 335c3 3 5 7 5 11l1 1v1c0 3 0 5-1 8-2 6-2 12 0 18v9l-1-1v-2c-1-4-1-7-2-10 0-2-1-4-1-6l-2-13c0-4-2-9-3-13v-3h0c2 0 3 1 4 0z" class="c"></path><path d="M316 346h3l1 1v1c-1 1-1 2-1 3l-1 6-2-11z" class="AD"></path><path d="M314 335c3 3 5 7 5 11h-3c-1-4-2-8-6-11h0c2 0 3 1 4 0z" class="v"></path><path d="M347 355c4 3 5 11 7 14 6 10 13 18 19 27v1c-3-2-6-7-8-10l-8-11 2 7c0 6 3 11 6 16 2 4 5 10 5 14v1l-1-1c-1 4 1 8 0 11l-2 16c-1-7-1-13-1-20v-2l-3-16c-6-15-12-30-16-47z" class="G"></path><path d="M363 402v1c1 1 1 2 2 3 2 2 1 9 1 12v2-2l-3-16z" class="I"></path><defs><linearGradient id="v" x1="371.197" y1="337.74" x2="344.803" y2="374.26" xlink:href="#B"><stop offset="0" stop-color="#cdc5c2"></stop><stop offset="1" stop-color="#f5f6f4"></stop></linearGradient></defs><path fill="url(#v)" d="M341 312h1l3 9c0 4 0 8 2 11 2 4 2 10 3 15h0l-1 1h0c0 3 2 6 3 9s3 6 4 8c2 4 5 8 7 12 5 7 10 14 13 22l-3-3c-6-9-13-17-19-27-2-3-3-11-7-14h0c-2-9-5-20-5-30-1-4 0-9-1-13z"></path><path d="M346 102l-111 1c-24 0-48 1-71-1-5-1-10-1-15-2l198 1-1 1z" class="l"></path><path d="M392 460h0c-4-4-5-11-9-14-1-1-1-2-2-2 6 0 13 1 17 6 3 4 4 11 6 16l7 16c3 7 5 14 9 21-1 1-1 0-2 0-2-2-4-4-5-6l-2-2c-7-11-15-22-19-35z" class="I"></path><path d="M393 452h1 1c1 3 3 5 4 8l6 15c1 3 2 7 4 11v2c-2-6-6-11-9-17s-4-12-7-19z" class="c"></path><path d="M392 460l-2-10h0l1 1c1 0 2 1 2 1 3 7 4 13 7 19s7 11 9 17c0 2 1 3 2 4v3c-7-11-15-22-19-35z" class="G"></path><defs><linearGradient id="w" x1="252.852" y1="119.51" x2="253.256" y2="184.402" xlink:href="#B"><stop offset="0" stop-color="#aeabaa"></stop><stop offset="1" stop-color="#dcd8d3"></stop></linearGradient></defs><path fill="url(#w)" d="M252 119c10-1 22 1 31 7 2 2 4 4 5 7h0l-3-3-1 1h1c0 1 1 2 1 3l1 1c2 7-1 12-2 18l-3 13c-1 2 0 5 0 7-1-1-2-2-2-3 0-2 0-2-1-3h0c1-11 5-20 5-31 0-1-1-3-2-4s-4-2-5-2c-4-2-7-3-11-4h-1c-4-1-8-2-12 0-4 1-7 2-11 4-1 0-1 0-2 1h-1c-2 2-6 5-7 8v1c-1 1-1 1-1 3h0v1l-1 1 1 1c-1 0-1 0-1 1l1 1c-1 6 0 10 1 16 1 2 2 5 3 8l1 2v3c1 2 1 3 2 5v3h-1c0-1-1-2-2-3s-2-5-3-7c0-1-1-3-1-4-1-3-3-8-5-9l-1 1c1 2 3 6 3 9l-3-6-5-12c-1-4-2-9-1-13v-1c1-2 2-4 4-7 8-8 17-12 29-14z"></path><path d="M223 133c8-8 17-12 29-14l2 1h-2c-1 0-3 1-4 1-10 1-19 7-25 14-3 4-4 7-3 12v1 2c1-1 0-3 1-5 0-1 0-2 1-3v-2h0l-1-1 2-2h0l1-1v-1c1-1 2-2 3-2 2-2 2-3 5-4 1-1 2-1 3-2s3-1 4-2c1 0 1-1 2-1h1l1-1h2 1l2-1h2 2c1-1 2-1 3-1h0c1 0 1 0 2 1h-7c-8 2-15 4-20 12-5 6-6 15-4 23v1 1l3 3c1 2 2 4 3 7h-1v2c-1-3-3-8-5-9l-1 1c1 2 3 6 3 9l-3-6-5-12c-1-4-2-9-1-13v-1c1-2 2-4 4-7z" class="r"></path><path d="M225 163h0c-2-4-5-12-4-16l2-7v4c0 4 1 10 3 14v1l3 3c1 2 2 4 3 7h-1v2c-1-3-3-8-5-9l-1 1z" class="l"></path><defs><linearGradient id="x" x1="319.85" y1="152.405" x2="280.722" y2="234.998" xlink:href="#B"><stop offset="0" stop-color="#cbc9c4"></stop><stop offset="1" stop-color="#f5f3ee"></stop></linearGradient></defs><path fill="url(#x)" d="M287 135l-1-1c0-1-1-2-1-3h-1l1-1 3 3h0c1 1 2 2 3 4 5 10 3 21 3 32 1 7 1 13 2 19 1 5 3 9 4 13l12 32c1 1 1 2 1 2v1h0c0 2-1-1 0 2 0 2 2 5 2 8l-2 2h1l1-1 1 1c-1 0-2 1-2 1-1 0-1 0-2-1h-3 0-2l-1-2v1c-2-1-3-2-4-4l-1-1c-1-1-2-2-2-4h0c-1-1-1-2-2-3h0l-2-5-1 1s-1-1-1-2v-1l-1-1v-1c-3-8-6-16-8-24h0c1-2 1-3 1-4-1-4 0-8-1-13-1-4-2-8-2-12 0-2-1-5 0-7l3-13c1-6 4-11 2-18z"></path><path d="M287 135l-1-1c0-1-1-2-1-3h-1l1-1 3 3h0c1 1 2 2 3 4-1 3 0 7 0 10 1 3 1 6 1 9v-1c-2-1-2-1-3-2v1c0-4 1-15-2-19h0z" class="l"></path><defs><linearGradient id="y" x1="305.119" y1="176.98" x2="342.745" y2="206.854" xlink:href="#B"><stop offset="0" stop-color="#a2a19f"></stop><stop offset="1" stop-color="#d5d2cf"></stop></linearGradient></defs><path fill="url(#y)" d="M300 185c3-1 4-2 4-5 0-1 0-2 1-3 2-2 4-3 6-5 2-1 3-2 5-3h1c0-1 1-2 1-3 1-1 1-2 1-3h1c1-2 1-2 2-3 1-2 3-4 6-5s4-2 7-1c0 1 0 1 1 2h0v1 2 1c-3 11-5 21-2 33 1 4 2 9 3 13v1c1 0 1 1 1 2v2h1c-3 0-4 1-6 2-2 2-3 6-4 9v3 1h-1 0l-3 1c-1 3-1 7-1 10h0c-1 4 0 8-2 11-1 0-2 0-2 1-1 1-2 2-3 2 1-1 3-3 4-5 0-2-1-6-1-7l-20-54z"></path><path d="M320 203h2c1 3 0 4-1 7h0c0-1-1-2-1-3h0l1-1c0-1 0-2-1-3z" class="l"></path><path d="M337 206v1c1 0 1 1 1 2v2h1c-3 0-4 1-6 2-2 2-3 6-4 9v3 1h-1 0l-3 1c-1 3-1 7-1 10h0c-1 4 0 8-2 11-1 0-2 0-2 1-1 1-2 2-3 2 1-1 3-3 4-5 0-2-1-6-1-7 2-4 1-10 1-13 1-4 2-7 4-10 2-4 4-7 8-8 1 0 2-1 3-1l1-1z" class="L"></path><path d="M333 210c2-1 3-1 5-1v2h1c-3 0-4 1-6 2-2 2-3 6-4 9v3 1h-1c-1-2-1-3-2-4v-1c1-4 4-8 7-11z" class="r"></path><path d="M333 210c2-1 3-1 5-1v2c-2 0-3 0-5-1z" class="y"></path><path d="M230 185l33 85 9 22c1 4 3 7 4 11-2 0-5 0-7 1v1c-1 0-1 0-1 1h0c2-1 5-2 8-2 2 3 4 6 7 8 1 0 2 0 3-1v1l-1 1h2v-1-1h1c1 1 1 1 1 3 1 2 0 5 1 7 3 2 6 3 10 4l-2 2-2-1h-2c-3 0-4-2-5-4-1-3-2-6-4-8-2-1-2-1-4 0h-2l-1-1h-1 0-3 0l-4-3v1c-2 0-4 1-6 0l-1-5h-2 0c1 2 1 3 2 5 1 1 1 3 0 4h0c0-1-1-2-1-4h-1v1c1 3 2 5 2 8l-3-9-2-2h-1v-1c-3-2-7-3-10-5h0c-1-1-1-1-1-2 2-1 5-2 7-3l1-2h0l-14-39c-1-2-2-6-4-8-1-1-4-2-5-3v-1c1 1 2 2 4 2h1c-3-5-5-12-7-18l-10-26 1-2h8c4 1 3 4 5 8 0 1 1 2 1 3v1h1v1l1-1v-1l1-1c-1-7-5-14-7-20-1-2-1-3 0-5v-1z" class="l"></path><path d="M240 257c-1-2-2-6-4-8-1-1-4-2-5-3v-1c1 1 2 2 4 2h1c2 2 4 5 7 7l2 2h1c0-2 0-3-1-4v-3l1-1v3l1 3c0 1 1 3 1 4-2 0-4-2-5-3h-3v1l1 1v1l-1-1z" class="C"></path><path d="M241 257h0c3 2 5 3 7 3l2 1c1 1 1 3 1 4l5 13c1 3 1 6 3 8 1 2 1 4 1 7 1 3 2 7 3 11 0 1 0 4 2 5h0l1-1 2-2h0c2-1 5-2 8-2 2 3 4 6 7 8 1 0 2 0 3-1v1l-1 1h2v-1-1h1c1 1 1 1 1 3 1 2 0 5 1 7 3 2 6 3 10 4l-2 2-2-1h-2c-3 0-4-2-5-4-1-3-2-6-4-8-2-1-2-1-4 0h-2l-1-1h-1 0-3 0l-4-3v1c-2 0-4 1-6 0l-1-5-5-19c-1-5-5-20-9-23-2-2-5-3-7-6h-1v-1z" class="E"></path><path d="M274 313c-1-2-3-3-4-6 1 0 1-1 2-1s2 1 2 1c1 1 2 2 2 3l1 3h-3z" class="X"></path><defs><linearGradient id="z" x1="246.123" y1="289.072" x2="259.061" y2="280.283" xlink:href="#B"><stop offset="0" stop-color="#9a9896"></stop><stop offset="1" stop-color="#b9b7b7"></stop></linearGradient></defs><path fill="url(#z)" d="M240 257l1 1h1c2 3 5 4 7 6 4 3 8 18 9 23l5 19h-2 0c1 2 1 3 2 5 1 1 1 3 0 4h0c0-1-1-2-1-4h-1v1c1 3 2 5 2 8l-3-9-2-2h-1v-1c-3-2-7-3-10-5h0c-1-1-1-1-1-2 2-1 5-2 7-3l1-2h0l-14-39z"></path><path d="M258 287l5 19h-2 0c0-1-1-3-1-4s0-2-1-2c0-2-1-2-2-3 1-1 1-1 1-2v-1-3-4z" class="X"></path><path d="M254 296c2 4 3 8 4 13h-1v-1c-3-2-7-3-10-5h0c-1-1-1-1-1-2 2-1 5-2 7-3l1-2h0z" class="T"></path><path d="M254 296c2 4 3 8 4 13h-1v-1c0-2-1-2-2-3s-3-2-4-3c-1-2 1-1 2-3v-1l1-2h0z" class="B"></path><defs><linearGradient id="AA" x1="186.614" y1="120.774" x2="160.611" y2="181.808" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#c4c1bf"></stop></linearGradient></defs><path fill="url(#AA)" d="M103 98c3-1 6 0 9 1 20 1 40 5 57 14 20 9 36 22 47 42 5 10 9 20 14 30v1c-1 2-1 3 0 5 2 6 6 13 7 20l-1 1v1l-1 1v-1h-1v-1c0-1-1-2-1-3-2-4-1-7-5-8h-8l-1 2-8-17c-11-23-26-44-46-59-10-7-21-13-33-18-9-4-19-7-29-11z"></path><path d="M221 181h0c3 2 4 4 5 7-1 2-2 2-4 2s-3 0-4-1c-2 0-2 0-2-2 0-1 0-3 1-5h3l1-1z" class="i"></path><defs><linearGradient id="AB" x1="348.351" y1="109.485" x2="368.456" y2="153.362" xlink:href="#B"><stop offset="0" stop-color="#5b5f65"></stop><stop offset="1" stop-color="#9b9896"></stop></linearGradient></defs><path fill="url(#AB)" d="M385 101c10-2 21-1 32-2 4 0 8-1 12 0h4c-24 6-48 14-68 27-9 6-18 13-23 21-3 4-4 9-6 13v-1-2-1h0c-1-1-1-1-1-2-3-1-4 0-7 1s-5 3-6 5c-1 1-1 1-2 3h-1c0 1 0 2-1 3 0 1-1 2-1 3h-1c-2 1-3 2-5 3-2 2-4 3-6 5-1 1-1 2-1 3 0 3-1 4-4 5l-1-3c-1-7-1-16 1-23 3-16 10-29 25-38 4-4 9-7 14-9 15-6 31-10 46-11z"></path><path d="M325 121c4-4 9-7 14-9 15-6 31-10 46-11-1 1-1 1-2 1-2 0-3 0-5 1h-4c-1 0-2 0-3 1-2 0-4 0-6 1l-14 4h1 0c-3 2-7 3-11 5-3 1-9 5-12 5h0c-2 1-3 2-4 2z" class="j"></path><path d="M261 306h2l1 5c2 1 4 0 6 0v-1l4 3h0 3 0 1l1 1h2c2-1 2-1 4 0 2 2 3 5 4 8 1 2 2 4 5 4h2v1c2 1 3 2 4 5 3 2 7 2 10 3h0v3c1 4 3 9 3 13l2 13c0 2 1 4 1 6 1 3 1 6 2 10v2h-1c1 3 1 11 3 13l-1 2c1 5 3 9 6 12 2 3 5 6 7 10h0c1 4 1 9 1 13 0 2 1 4 1 5l1 1c0 4 0 7 1 10l-1 3h-2l-1-2-17-45c-1 0-1 1-1 1-1-2-1-3-3-4v-1c-1-1-1-2-2-3l-4-9v1h-1c-2-4-4-7-7-10l-1 1c-4-5-8-7-15-10 0-4 1-6-1-10h0c0 3 0 4-1 7-1 1-1 3-2 4-1 2-1 3-3 5-2 0-3 0-5-1l-4 1c-2-1-4-2-6-4l-14-6c7 2 13 5 20 6h1l1-1c0-1-1-1-1-2s0-2 1-4l3 3 2 2c2-1 4-2 6-4 0-1 1-2 1-3-1-6-4-11-6-17-3-8-7-17-10-26h0c0-3-1-5-2-8v-1h1c0 2 1 3 1 4h0c1-1 1-3 0-4-1-2-1-3-2-5h0z" class="u"></path><path d="M289 322c1 2 2 4 5 4h2v1c2 1 3 2 4 5 3 2 7 2 10 3h0v3c1 4 3 9 3 13l2 13c0 2 1 4 1 6 1 3 1 6 2 10v2h-1c0-1-1-3-1-4v-1c-1-3-1-6-1-9l-1-3c-3-9-2-20-9-28l-1-1c-1-2-3-2-5-4h0-1c-1 1 0 1-1 1 2 2 3 4 5 6-2-2-5-4-6-6v-5c-2-1-3-1-4-1l2 3-1 2v1l1 1 1 1c1 1 2 2 2 3-2-1-5-5-6-7-1-3-2-6-2-9z" class="I"></path><path d="M334 437l-1 1c-1-1-1-3-1-5-1-1-2-1-2-2-1-3-1-5-3-7-1-3-2-6-5-9-2-1-1-5-2-7v-1l-1-1c0-1-1-1-1-3h0v-1c-1-2 0-4 1-5 1 5 3 9 6 12 2 3 5 6 7 10h0c1 4 1 9 1 13 0 2 1 4 1 5z" class="c"></path><defs><linearGradient id="AC" x1="249.173" y1="343.341" x2="325.513" y2="369.67" xlink:href="#B"><stop offset="0" stop-color="#acaaac"></stop><stop offset="1" stop-color="#c9c6c1"></stop></linearGradient></defs><path fill="url(#AC)" d="M261 306h2l1 5c2 1 4 0 6 0v-1l4 3h0 3 0 1l1 1h2v1c1 6 3 12 5 18l13 34c5 12 11 24 16 37-1 0-1 1-1 1-1-2-1-3-3-4v-1c-1-1-1-2-2-3l-4-9v1h-1c-2-4-4-7-7-10l-1 1c-4-5-8-7-15-10 0-4 1-6-1-10h0c0 3 0 4-1 7-1 1-1 3-2 4-1 2-1 3-3 5-2 0-3 0-5-1l-4 1c-2-1-4-2-6-4l-14-6c7 2 13 5 20 6h1l1-1c0-1-1-1-1-2s0-2 1-4l3 3 2 2c2-1 4-2 6-4 0-1 1-2 1-3-1-6-4-11-6-17-3-8-7-17-10-26h0c0-3-1-5-2-8v-1h1c0 2 1 3 1 4h0c1-1 1-3 0-4-1-2-1-3-2-5h0z"></path><path d="M267 365l3 3 2 2c-2 1-3 1-5 1 0-1-1-1-1-2s0-2 1-4z" class="K"></path><path d="M279 367c-1 1-1 3-2 4-1 2-1 3-3 5-2 0-3 0-5-1l-4 1c-2-1-4-2-6-4 3 1 8 2 10 1h1c4 0 7-3 9-6z" class="AY"></path><path d="M270 310l4 3h0 3 0 1l1 1h2v1h-1-2c-2 0-4-1-5 1 0 1-1 2 0 3v1 1 1h0-1l-1-1c0-1-1-2-1-2 0-1 0-2-1-3h0c0-3 0-3 1-6z" class="C"></path><path d="M328 226h1v-1-3c1-3 2-7 4-9 2-1 3-2 6-2l20 53 3 9 25 63 10 25c1 2 3 6 3 8h0v9h-1v1c0 1 0 2-1 3l-2 7-1 16h-1l-1 1c-2-1-3-2-5-4-2-3-3-7-5-11l-9-23-8-18c-2-3-3-4-5-6-1 0 0 0 0-1 1-2 2-3 4-4l5 7c0 1 1 1 2 1l-6-9c-1-1-3 0-5 0l-33-84-2-4h-1l-1-1c1-4 1-8 0-12 0-3 0-7 1-10l3-1h0z" class="l"></path><path d="M385 362c2 3 3 7 4 10l-4-5c0-2-1-2-2-3 1-1 1-2 2-2z" class="G"></path><path d="M376 365h0-1c3-2 6-3 9-4l1 1c-1 0-1 1-2 2v1h-7z" class="D"></path><path d="M383 364c1 1 2 1 2 3-2 1-3 2-6 1-1 0-3-1-4-2l1-1h7v-1z" class="X"></path><path d="M394 405v-6c-1-3-2-6-2-9 1-1 1-2 3-2l1 1-1 16h-1z" class="D"></path><path d="M328 226v1c-3 5 0 15 0 20 0 3 1 4 0 7l-2-4h-1l-1-1c1-4 1-8 0-12 0-3 0-7 1-10l3-1z" class="r"></path><path d="M363 445c1-1 2-2 3-2 2-1 3 0 5-2 1-1 2-2 4-2l1-1c3-1 6-1 9-1h1c4 1 6 2 9 4 1 1 2 3 4 4l-1 1c-2-3-6-6-9-6-5 0-9 0-12 3v1c0 1-1 3 0 5 0 1 0 1 1 2 0 1 1 2 2 3 0-1-1-1-1-2v-2h-1v-2-1c0-2 1-2 2-4v1 1c-1 1 0 2 0 3l36 60 2 2c3-3 8-5 11-9 1-3 3-6 5-9 1-1 6-4 7-6h0c2-4 0-9 1-13h0c2-3 2-7 2-11h1v5c0 2-1 4-1 7l-1 2c0 4 4 8 4 11l-9 10c-2 2-2 3-2 5-1 2-1 2-2 3l1 1c0-1 1-1 1-1 1-1 1-2 2-2l2-1h0 1 0l-2 3c-3 1-6 3-7 6l-1 1-2-2c0 2 1 3 1 5-1-2-1-3-2-5 0-1 0-1-1-2l-1 1h-1v-2c-1 1-2 1-3 2v1c-2 1-7 4-8 6 0 1 0 4-1 5-1 4-1 9-1 13 0 8 0 16 2 23 1 4 2 7 4 11v1c1 2 2 3 3 5 0 1 0 1 1 2 0 1 1 2 0 3 0 1-1 3-2 4l1 1v1c-1 2-5 4-7 5h-2-1c0 2-1 3-1 4-2 3-5 4-8 6 0 1-2 2-3 3-2 2-5 4-6 7v1c-2 3-1 9-1 13 0 3 1 6 0 8-2-2-1-15-2-19l-1-1c1-1 1-3 1-4l2-2c0-1 0-1 1-1 2-4-2-16-4-20l-1-8-2-9c-1-3-2-5-2-8h-3c0-1 2-2 2-3h1 3l2-1 1-1 2-2h1v-1c-2-1-3-4-3-6-3-5-4-12-6-17l-11-29c-1-3-2-5-3-8l-7-22-1-4c-1-4-2-7-3-11 0-3 0-5 1-7l3-3z" class="D"></path><path d="M415 570l1 1c2 4 2 5 1 9h-1c-1-3-1-7-1-10z" class="L"></path><path d="M422 509v1c-2 1-7 4-8 6 0 1 0 4-1 5-1 4-1 9-1 13v1 8c-1-1-1-4-1-6v-17h-1c-1 1-1 3-2 4l-1-1-3 3c-1 2-1 4-1 6 0 1 0 1-1 2h0c1 2 1 2 0 4v3s-1 1-1 2h0c0 1 0 2-1 3 0-3 1-5 1-7l1-10c0-1 1-3 1-3 1-2 4-5 6-6 2-2 4-5 7-7 2-2 4-3 6-4z" class="I"></path><path d="M422 579c0 1-1 3-2 4l1 1v1c-1 2-5 4-7 5h-2-1c0 2-1 3-1 4-2 3-5 4-8 6 0 1-2 2-3 3-2 2-5 4-6 7v1c-2 3-1 9-1 13 0 3 1 6 0 8-2-2-1-15-2-19 1-1 1-2 1-4 2-4 7-9 11-11l1-1-1-3 2 2c3-1 5-4 6-6 0-1 0-2 1-3l1 1c2 0 3 0 4-1s1-2 1-4c0-1 0 0 1-1 0 0 0 1 2 1 0-2 1-3 2-4z" class="q"></path><path d="M363 445c1-1 2-2 3-2 2-1 3 0 5-2 1-1 2-2 4-2l1-1c3-1 6-1 9-1h1c4 1 6 2 9 4 1 1 2 3 4 4l-1 1c-2-3-6-6-9-6-5 0-9 0-12 3v1c-2 2-2 2-1 5 2 3 3 6 5 10 3 4 5 9 7 14 3 5 7 10 8 16h0l-8-14-1 1h0l-2-2 16 31v1-1l-1 1v-1c-4-12-12-22-17-34l-1-1h0c0-1-1-2-2-3v-1c-1-1 0-1-1-2s-1-1-1-2l-1-1c0-1-1-2-1-3l-1-1c-1-2-4-6-4-8-1-1 0-5 1-6-1 1-4 3-4 5-1 1 0 3 0 5-1-2-1-3 0-4-2 1-2 1-4 0h0c2-1 3-2 5-3v-1c0-1 1-1 2-2-3 0-5 1-7 2h-1z" class="L"></path><path d="M442 473c2-3 2-7 2-11h1v5c0 2-1 4-1 7l-1 2c0 4 4 8 4 11l-9 10c-2 2-2 3-2 5-1 2-1 2-2 3l1 1c0-1 1-1 1-1 1-1 1-2 2-2l2-1h0 1 0l-2 3c-3 1-6 3-7 6l-1 1-2-2c0 2 1 3 1 5-1-2-1-3-2-5 0-1 0-1-1-2l-1 1h-1v-2c-1 1-2 1-3 2-2 1-4 2-6 4-3 2-5 5-7 7-2 1-5 4-6 6 0 0-1 2-1 3l-1 10c0 2-1 4-1 7-1 1-1 3-2 5l-1-1v-1h-1c4-4 4-12 5-17 0-2 0-6 1-8s4-3 5-5c4-3 7-6 11-9 3-3 8-5 11-9 1-3 3-6 5-9 1-1 6-4 7-6h0c2-4 0-9 1-13h0z" class="r"></path><path d="M425 507c1-1 1-1 3-1l3 6-2-2c0 2 1 3 1 5-1-2-1-3-2-5 0-1 0-1-1-2l-1 1h-1v-2z" class="D"></path><path d="M442 488h1v1c-1 2-7 7-9 8h0c-1 2-1 7-1 9s0 2-1 4h-1v-1c-1-1-1-6 0-8v-1c1-1 1-2 2-4 1-1 1-2 3-3s4-3 6-5z" class="X"></path><path d="M447 487l1 3 19 49c1 2 2 4 2 7v8c0 3 1 6 1 8v1c2-3 0-6 1-8v3l1 21 1 11 1 1v6h-1l-1 1c-1 0-1 0-2-1l-3-1v1 2h0c-1 0-2-1-2-1 0 3 0 4 1 7h0c-1-1-1-1-2-3h-1c1 2 1 3 1 5l4 10v2l2 5 1 4 1 6c1 2 1 3 2 5h0c0 2-1 5 1 7 1-1 0-3 0-4h1v3c0 2 1 3-1 5h-1c-1 2 0 3 0 5 0 1 0 1 1 2 0 1 0 4-1 5 1 1 2 1 3 1h1 0c-1 3-3 4-5 6l-5 4-2 3c-2 0-3 1-3 3-1 0-1 1-1 2s0 2-1 2l-1-3c-1-6 0-12 1-18h0-2l-1-1c0-5-2-10-4-15-5-14-10-27-16-40-2-4-3-9-5-13-2-2-5-7-7-7l-1-1c1 0 1 0 2 1 2 0 5 2 7 2h0c-5-3-8-4-13-4l-1-1c1-1 2-3 2-4 1-1 0-2 0-3-1-1-1-1-1-2-1-2-2-3-3-5v-1c-2-4-3-7-4-11-2-7-2-15-2-23 0-4 0-9 1-13 1-1 1-4 1-5 1-2 6-5 8-6v-1c1-1 2-1 3-2v2h1l1-1c1 1 1 1 1 2 1 2 1 3 2 5 0-2-1-3-1-5l2 2 1-1c1-3 4-5 7-6l2-3h0-1 0l-2 1c-1 0-1 1-2 2 0 0-1 0-1 1l-1-1c1-1 1-1 2-3 0-2 0-3 2-5l9-10z" class="c"></path><path d="M470 666l2-2h2l-2 4h0l-2-2z" class="C"></path><path d="M477 663h1 0c-1 3-3 4-5 6l-1-1h0l2-4c2 0 2 0 3-1z" class="G"></path><path d="M468 673c0-1-1-1-2-2h0v-1l1-1h0l1-2c1 0 1 0 1-1h1l2 2 1 1-5 4z" class="D"></path><path d="M423 545l-2-9v-7c0-1 0-2 1-3h0c1 3 0 7 1 11v1 1c0 1 0 2 1 3h-1v3z" class="I"></path><path d="M432 540l14 32-1 1-2-4h0v2 1l-7-15-5-15 1-2z" class="i"></path><path d="M456 619c-1-3-2-5-3-7l-1-2v-1c-1 0-1-1-2-2-1-2-2-3-3-5l1-1v-1c3 1 5 6 7 10 0 1 1 1 1 2v1c1 3 3 6 4 9 1 2 2 4 2 6-2-3-3-6-6-9zm-36-106l1 2c-1 0-1 1-1 2-3 16-2 35 5 50 1 2 2 6 3 7v1h0-3c-4-9-7-19-7-29-1-5 0-11 0-16s0-11 2-17zm6-4l1-1c1 1 1 1 1 2 1 2 1 3 2 5l31 81 3 6h-1s-1-1-1-2l-12-29-12-31c-4-10-9-20-12-31z" class="C"></path><path d="M443 572v-1-2h0l2 4 1-1c5 10 9 21 13 32 1 3 4 6 4 10l-1 1-1-1c0-2-2-4-3-5-4-6-8-11-12-16-2-2-4-4-6-7l-1-1h1c1 1 1 2 2 2h1l3 4v1l3 3c1 0 1 0 1-1s-1-1-1-2c-1 0-1-1-2-2l1-1c1 1 2 1 3 2h0c0-3-2-4-3-7-2-4-4-8-5-12zm13 40l1-1-1-2h0l5 9c1 2 3 4 4 6 0 1 1 2 1 3l2 4 1 1v2c1 1 1 2 1 3 1 1 1 4 3 5 0-1 0-1 1-3 0 2-1 5 1 7 1-1 0-3 0-4h1v3c0 2 1 3-1 5h-1c-1 2 0 3 0 5 0 1 0 1 1 2 0 1 0 4-1 5-1-1-2-2-2-3v-1c-2-2-2-5-3-8-1-2-2-4-3-7l-5-13c-1-1-1-2-2-3 0-1 0-1-1-2-1-2-1-4-2-6 3 3 4 6 6 9 0-2-1-4-2-6-1-3-3-6-4-9v-1z" class="L"></path><path d="M425 507v2c-1 4-1 8 1 12l6 19-1 2 5 15 7 15c1 4 3 8 5 12 1 3 3 4 3 7h0c-1-1-2-1-3-2l-1 1c-1-2-2-3-3-5l-3-6c-1-2-1-4-2-6l-7-21c-2-5-3-10-5-15l-5-17c1-2 0-8 0-10v-1c1-1 2-1 3-2z" class="C"></path><path d="M434 557l-5-17c-1-5-3-9-4-13v-1-2 1c1 1 1 2 2 3l1 5 3 9 5 15c-1-1 0-1-1-1l-1 1z" class="c"></path><path d="M434 557l1-1c1 0 0 0 1 1l7 15c1 4 3 8 5 12 1 3 3 4 3 7h0c-1-1-2-1-3-2-6-10-10-21-14-32z" class="D"></path><path d="M430 515c0-2-1-3-1-5l2 2 1-1 2 2c-1 2-1 2 0 4l19 49c5 10 9 21 12 32 0 3 0 4 1 7h0c-1-1-1-1-2-3l-3-6-31-81z" class="c"></path><path d="M423 538c0-1 1-3 0-4v-7l2 6c0 1 1 2 1 3l1 1c2 5 3 10 5 15l7 21c1 2 1 4 2 6l3 6c1 2 2 3 3 5 1 1 1 2 2 2 0 1 1 1 1 2s0 1-1 1l-3-3v-1l-3-4h-1c-1 0-1-1-2-2h-1l-1-2c-1-1-3-2-3-4-1-1-2-2-2-3l-2-4c-2-4-4-7-5-11s-2-7-3-11v-5-3h1c-1-1-1-2-1-3v-1z" class="D"></path><path d="M447 487l1 3 19 49c1 2 2 4 2 7v8c0 3 1 6 1 8v1c2-3 0-6 1-8v3l1 21 1 11 1 1v6h-1l-1 1c-1 0-1 0-2-1l-3-1v1 2h0c-1 0-2-1-2-1-3-11-7-22-12-32l-19-49c-1-2-1-2 0-4l-2-2c1-3 4-5 7-6l2-3h0-1 0l-2 1c-1 0-1 1-2 2 0 0-1 0-1 1l-1-1c1-1 1-1 2-3 0-2 0-3 2-5l9-10z" class="X"></path><path d="M469 588l4 2 1 1v6h-1l-1 1c-1 0-1 0-2-1l-3-1c-1-2 1-4 2-7v-1z" class="E"></path><path d="M469 588l4 2 1 1v6h-1 0c0-4-2-5-4-8v-1z" class="C"></path><path d="M469 554c0 3 1 6 1 8v1c2-3 0-6 1-8v3l1 21 1 11-4-2v-7-27z" class="AI"></path><path d="M447 487l1 3 19 49-1 6c-1 3-5 8-8 9-2 1-3 1-5 0-3-1-3-4-4-6-2-4 0-9 1-12 0-1 1-2 1-3s-1-3-1-5c-1-1-1-2-2-4l-9-19 2-3h0-1 0l-2 1c-1 0-1 1-2 2 0 0-1 0-1 1l-1-1c1-1 1-1 2-3 0-2 0-3 2-5l9-10z" class="L"></path><path d="M513 192c2-1 3-2 4-3l3 5c2 1 5 1 7 1 0 1 1 3 2 4 0 0 3 1 4 1h4 1 4l1-1 1-1c1 3 1 4 2 6l1 1v1 1l3 3c-1 1-2 1-3 2h2c2 0 2 0 3 1s6 3 9 4h0c2 0 3 1 5 1 1 0 3-1 4-1l2 1 29 15 7 3c0 1 1 2 2 2 4 2 8 6 13 8l7 4c1 0 3 1 4 2l1 1c7 4 13 8 19 14l-1 1-1 1h0c-1 0-1 0-2 1h-1c-1 1-1 1-2 1-2 0-3 0-5 1l1 1h6v1h1c2 0 4 1 6 1 1 1 2 1 3 2 1 0 2 1 3 1 2 0 4 1 5 0 2 1 3 2 5 3l-1 3-2-1v3c1 2 1 2 0 3l1 1c1 1 2 1 3 2l7 5 7 6 1 1-1 2-5 18-6 16-1 3-2 4-24 70-1 2c-2 1-3 2-5 2h-2-1c-2-1-4-2-5-4 0-1-1-2-1-3 0 2 1 4 2 5 2 2 4 3 6 3s4 0 5-1l1 1-1 2c-1 2-2 6-3 9l-5 13-18 49-53 146-12 35c-2 5-5 10-6 15l-1 7h1c1 2 2 2 2 4v3c1 4 2 8 2 13-1-2-2-2-2-4-1 3 0 6 0 8v1c-1-2-1-4-2-6v-1c-2-3-4-4-7-6l-5 19v-1l-7-20c0 1 0 2 1 3v1 1l1 1v1c1 1 1 3 1 4 0 2-2 4-3 5-3 2-6 5-7 8-1 1-3 2-4 3-1 0-1-1-2-1l-17-43v-2c-1-6-2-11-5-16 0-1-1-2-1-3v-1-2c-1-1-1-1-1-2-2-3-5-4-8-6h0c-3-1-2-1-4-4-1-1-2-3-2-4-1 0-2 0-3 1h0c-1 0-2-1-2-2 2-2 1-3 1-5v-3h-1c0 1 1 3 0 4-2-2-1-5-1-7h0c-1-2-1-3-2-5l-1-6-1-4-2-5v-2l-4-10c0-2 0-3-1-5h1c1 2 1 2 2 3h0c-1-3-1-4-1-7 0 0 1 1 2 1h0v-2-1l3 1c1 1 1 1 2 1l1-1h1v-6l-1-1-1-11-1-21v-3c-1 2 1 5-1 8v-1c0-2-1-5-1-8v-8c0-3-1-5-2-7l-19-49-1-3c0-3-4-7-4-11l1-2c0-3 1-5 1-7v-5h-1c0 4 0 8-2 11h0c-1 4 1 9-1 13h0c-1 2-6 5-7 6-2 3-4 6-5 9-2-3-3-7-5-10-4-10-8-20-11-31-1-3 0-8-1-11-1-2-3-2-4-3-2-1-15-32-16-37h1c1 0 1-2 2-3v-1l1-16 2-7c1-1 1-2 1-3v-1h1v-9h0c0-2-2-6-3-8l-10-25-25-63c2-1 3-2 4-3l1-2 1-1c4-4 6-9 8-14 1-2 2-3 3-4l3-8 1-4v-1c1-1 0-2 0-3h2 0c2-1 0-6 3-8 3 4 6 8 10 11h1c1 2 3 3 4 3s3 1 4 1h0c2 1 4 2 6 2h1 3c2 1 4 1 5 1 2 0 3 0 5 1l86-52z" class="AG"></path><path d="M604 425h1c1 0 1 0 2 2h-2c-1-1-1-1-1-2zm-9 58h1 1l-1 2-1 1-1-1 1-2z" class="J"></path><path d="M603 410l1 1v1l-2 1-1 1c0-2 1-3 2-4z" class="B"></path><path d="M576 409h2l1 1v2h-2l-1-1v-2z" class="M"></path><path d="M616 391h1v1c1 1 1 2 1 3l-1 1-2-1v-3l1-1zm-15-86c1 2 1 4 2 6 0 4 1 7-1 10 0-5-1-9-3-13 2 1 2 3 3 4v-2c-1-2-1-3-1-5zm-19 98h1 1 1 0v3l-3 1-1 1v1 1l-1-1-1-1v-1c0-1 1-2 2-2 1-1 1-1 2-1l-1-1zm-19 26h1c0 1-1 1-1 2v1h1v1h1l-2 2h-1c-1-1-3-2-2-3v-3h2v1l1-1z" class="B"></path><path d="M595 291c0 1 0 1 1 2v3 1c0 1 1 2 1 2 1 2 1 4 1 6-2-3-5-7-5-11 1-1 1-2 2-3h0z" class="M"></path><path d="M637 268l12 2c-1 1-1 1-2 1-2 0-3 0-5 1l-1 1c-4 0-8-1-12-1-3 0-6 0-8 1 3-2 6-3 10-2 2 1 4 1 6 1 1-1 2-1 3-1h1c1 0 1 0 2-1-1-1-2-1-4-1-1 0-1 0-2-1z" class="J"></path><path d="M586 281l3 1c-1 2-2 8-4 10 0 0-2 1-3 1-3 2-7 3-10 6 1-3 3-3 6-4 1-1 3-2 4-3h1v-4h0v-1l2-2c0-1 1-3 1-4z" class="X"></path><path d="M585 285c1 2 1 4 0 5l-2 2v-4h0v-1l2-2z" class="y"></path><path d="M559 321l2-1v10l-1 14h0l-1 1c-1-2 0-8 0-10 0-1-1-2-2-3 1-3 0-9 2-11z" class="z"></path><path d="M646 327v-1c3 6 2 12 1 18-1 2-2 4-3 5l-3 3c-1 3-5 9-8 10 2-5 6-8 8-13 5-7 7-14 5-22z" class="X"></path><path d="M569 318l1-2c0-1 0 0 1-1v9l-1 15v52c0 6 1 14 0 20-1-2-1-7-1-10v-25-37-21z" class="AL"></path><path d="M624 262c0-1 1-1 2-1v4l5 1c2 0 4 0 5 1h0c-15-1-30-1-42 9l-3 3-3-2c3-3 6-5 9-7 3-1 5-2 8-3 2-1 3-1 5-2 2 0 5-1 7-1h4c1-1 2-2 3-2z" class="i"></path><path d="M624 262c0-1 1-1 2-1v4l-21 2c2-1 3-1 5-2 2 0 5-1 7-1h4c1-1 2-2 3-2z" class="AW"></path><path d="M633 309l1-1c0 2 1 4 0 6 0 3-1 4-2 6l1 2c1-1 2-1 4-1-2 1-3 2-4 3s-4 6-5 7c-2 5-5 11-7 17 0 2-1 6-2 8 0-5 1-10 2-14 1-3 2-7 3-10 0-2 1-4 2-6h-1l-1-1c3 0 3-1 5-3h-1v-2c0-1-1-3-1-4h1v1c0 2 1 2 2 3h1c2-3 3-7 2-11z" class="j"></path><path d="M632 320l1 2c1-1 2-1 4-1-2 1-3 2-4 3s-4 6-5 7h0c-1 0-2 1-2 2l-1-1c1-1 1-2 2-3 0-1 0-1 1-2v-2l2-2c0-1 1-2 2-3z" class="z"></path><path d="M633 257c7 3 12 6 17 9 1 1 2 1 2 2h1l-1 1h0c-1 0-1 0-2 1h-1l-12-2-1-1h0c-1-1-3-1-5-1l-5-1v-4c2-1 2-1 4 0v-1c-1-1-2-1-3-1l-1-1c3 0 5 0 7-1z" class="X"></path><path d="M626 261c2-1 2-1 4 0 0 1 1 2 1 4v1l-5-1v-4z" class="E"></path><path d="M636 267c4 0 8 1 12 0h3l1 1h1l-1 1h0c-1 0-1 0-2 1h-1l-12-2-1-1h0z" class="G"></path><path d="M633 257c7 3 12 6 17 9h-1c-3-1-5-1-8-2-4-1-7-3-11-4-1-1-2-1-3-1l-1-1c3 0 5 0 7-1z" class="AY"></path><path d="M666 340l9 3-2 4-1-1h-3c-3-1-6-1-9 0-14 4-23 14-30 26-3 6-6 12-8 19v-1c3-9 5-20 11-28 3-1 7-7 8-10l3-3 1 1 6-5c4-2 8-3 12-3 1 0 2 0 2 1 1 0 1 0 2-1l-2-2h1z" class="y"></path><path d="M666 340l9 3-2 4-1-1h-3v-1c-2-1-5-2-8-1h-1c2-1 3-1 5-1 1 0 1 0 2-1l-2-2h1z" class="f"></path><path d="M562 283h1c1 1 1 2 2 2l-1-1v-4l1-1v2h1c1 0 2 1 2 1h1v-2h2v2h1v4c0 2 0 4 1 6 2-1 3-1 5-3 0 1 0 0 1 1l-1 1h0c1 0 2-1 3-2l1 1v2c-1 1-3 2-4 3-3 1-5 1-6 4l-1 1c-1 5 0 10 0 15-1 1-1 0-1 1l-1 2v-4s-1-1 0-2v-14c-2 0-3 0-4-1-1-2-1-3 0-5l-1-2c0-1-2-3-3-4 0-1 0-2 1-3z" class="p"></path><path d="M571 282h1v4c0 2 0 4 1 6 2-1 3-1 5-3 0 1 0 0 1 1l-1 1h0c1 0 2-1 3-2l1 1v2c-1 1-3 2-4 3-3 1-5 1-6 4l-1 1v-18z" class="k"></path><path d="M578 291h0c1 0 2-1 3-2l1 1v2c-1 1-3 2-4 3-2-1-2-1-3-1v-1c1 0 1-1 2-2h1zm-16-8h1c1 1 1 2 2 2l-1-1v-4l1-1v2h1c1 0 2 1 2 1h1v11 5c-2 0-3 0-4-1-1-2-1-3 0-5l-1-2c0-1-2-3-3-4 0-1 0-2 1-3z" class="a"></path><path d="M565 297l1-1v-1c1-1 2-2 3-2v5c-2 0-3 0-4-1z" class="m"></path><path d="M554 402v53c1 8 0 16 0 25 0 4 0 8 1 13l-1 1h-1c-1-3 0-6-1-8v-1-3c0 4 0 7-1 11 0 4 0 8-1 12v-23c1-2 1-4 1-6v-11l1-52c0-2-1-5 1-7v-4l1 2v-2z" class="V"></path><path d="M554 402v53l-1 4v-53-4l1 2v-2z" class="r"></path><defs><linearGradient id="AD" x1="594.371" y1="281.378" x2="614.725" y2="307.045" xlink:href="#B"><stop offset="0" stop-color="#171617"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#AD)" d="M613 275l6 1h0c-6 1-10 1-15 3-1 0-3 1-3 1l-1 1c0 1 0 2 1 3h0c0 2 2 5 4 6 3 3 9 2 12 3h1c2 2 5 3 6 4 2 1 2 2 3 3 2 2 4 3 6 5v1s1 1 1 2l-1 1c-1-4-3-6-6-9-6-3-15-5-21-3-3 0-4 1-5 3s-1 3 0 5c0 2 0 3 1 5v2c-1-1-1-3-3-4l-1-3c0-2 0-4-1-6 0 0-1-1-1-2v-1-3c-1-1-1-1-1-2 0-2-1-3-2-3v-1c2-3 2-7 5-9h1c1-1 2-1 4-2 0 1 1 1 2 0h4c1-1 3-1 4-1z"></path><path d="M557 265v1h1v1l1 1c0 3 1 5 1 8l1 1h0c1 1 1 2 1 3v1 2c-1 1-1 2-1 3 1 1 3 3 3 4l1 2c-1-1-1-1-3-2-1 1 0 4 0 5v2c0 2 0 4-1 6l-1 1v1h2c2 2 3 3 4 6l1 1v2l-1 1c0 1 1 1 1 2v3l-1 1-2-1h-1l-2-2v2l-2 1c-1-1-2-1-2-2-1-1-1-2 0-4l-1-1c0-1 0-2 1-3l1-1c0-3 0-5-1-8l-2-1v1l-2-2c0-2 1-4 1-6-1-1-1-2-1-3l1-1-1-1 1-1h1v-2c0-3-1-4 1-6l-1-1h0c0-3 1-5 1-8 0-2-1-4 1-6z" class="O"></path><path d="M557 290l1-1v-3h1l1 3c-1 2-2 3-1 5v1c0 1 0 1-1 1 0 0-1-1-1-2v-4z" class="H"></path><path d="M557 315c1 0 3 0 4 1h1c1 1 2 2 2 4h-1l-2-2v2l-2 1c-1-1-2-1-2-2-1-1-1-2 0-4z" class="J"></path><path d="M555 290l2-2v2 4 8l-2-1v1l-2-2c0-2 1-4 1-6-1-1-1-2-1-3l1-1-1-1 1-1h1v2z" class="AB"></path><path d="M555 290l2-2v2 4 8l-2-1v-3c1-1 1-5 1-7-1 0-1-1-1-1z" class="P"></path><path d="M557 265v1h1v1l1 1c0 3 1 5 1 8l1 1h0c1 1 1 2 1 3v1 2c-1 1-1 2-1 3 1 1 3 3 3 4l1 2c-1-1-1-1-3-2-1 1 0 4 0 5v2c0 2 0 4-1 6l-1 1v-4-11l-1-3h-1v3l-1 1v-2l-2 2v-2-2c0-3-1-4 1-6l-1-1h0c0-3 1-5 1-8 0-2-1-4 1-6z" class="d"></path><path d="M557 265v1c0 3-1 10 0 14h0c-1 2 0 6 0 8l-2 2v-2-2c0-3-1-4 1-6l-1-1h0c0-3 1-5 1-8 0-2-1-4 1-6z" class="O"></path><path d="M560 240c2 0 3 0 5-1l2 2h0c2 1 2 1 3 2l1 1h2c1 1 0 2 2 1l-1 1v1h5v1h2l1 1s-1 1-1 2h0c1 1 1 0 2 1h2l1 1-6 1-5 2-1 3-1 1h0l-1 2c-1 2 0 4 0 7l-1 7 1 6h-1v-2h-2v2h-1s-1-1-2-1h-1v-2l-1 1v4l1 1c-1 0-1-1-2-2h-1v-2-1c0-1 0-2-1-3h0l-1-1c0-3-1-5-1-8l-1-1v-1h-1v-1c-2 2-1 4-1 6h-1v-8l2-9v-3l1-1-1-1h-1-2c-2 0-1 0-2-1v-1-1-2l2-2c2-2 4-2 6-2z" class="w"></path><path d="M557 265l1-1c0-2 1-2 2-4 0 2-1 9 0 10l1-3v1 3c-1 2-1 3 0 5v1h0l-1-1c0-3-1-5-1-8l-1-1v-1h-1v-1z" class="H"></path><path d="M574 248v1c-2 3-4 5-4 8l-1 1c-3 2-5 5-6 9h-1l1-2c0-2 1-4 2-6v-1l-1 1-1-1c1-1 2-3 2-4v-1l-1 1h-1v-1c1-1 3-2 4-3l1 1c1 0 2-1 3-1 0-1 2-2 3-2z" class="C"></path><path d="M567 250l1 1c1 0 2-1 3-1l-1 2c-3 1-4 4-5 7v-1l-1 1-1-1c1-1 2-3 2-4v-1l-1 1h-1v-1c1-1 3-2 4-3z" class="p"></path><path d="M569 258v1c-1 1-2 2-2 3v1l-2 4v1c-1 2-1 3 0 6h0v4 1l-1 1v4l1 1c-1 0-1-1-2-2h-1v-2-1c0-1 0-2-1-3v-1l2-9c1-4 3-7 6-9z" class="AA"></path><path d="M565 278c-1-1-1-1-2-1v-6c0-2 1-3 2-4v1c-1 2-1 3 0 6h0v4z" class="W"></path><path d="M557 254h2c1-1 1-2 1-3l2-1h0c1 0 0-1 1-1h2l1-1 1 1v1c-1 1-3 2-4 3v1c-1 1-2 6-3 6-1 2-2 2-2 4l-1 1c-2 2-1 4-1 6h-1v-8l2-9z" class="S"></path><path d="M557 265l-1-2c1-2 2-5 3-6 2-1 2-2 3-4h0 1v1c-1 1-2 6-3 6-1 2-2 2-2 4l-1 1z" class="Z"></path><path d="M560 240c2 0 3 0 5-1l2 2h0c2 1 2 1 3 2l1 1h2c1 1 0 2 2 1l-1 1v1h-1l1 1c-1 0-3 1-3 2-1 0-2 1-3 1l-1-1v-1l-1-1-1 1h-2c-1 0 0 1-1 1h0l-2 1c0 1 0 2-1 3h-2v-3l1-1-1-1h-1-2c-2 0-1 0-2-1v-1-1-2l2-2c2-2 4-2 6-2z" class="w"></path><path d="M552 244l2-2c2-2 4-2 6-2v2h1c2 2 2 2 2 5h-1v1c-1 0-1-1-2-1-1-1-1-2-1-3-2-1-2-1-3-1l-2 2c-1 0-1 0-2-1z" class="AD"></path><path d="M562 247l-1-1v-2c-1-1-1-1-1-2h1c2 2 2 2 2 5h-1z" class="G"></path><path d="M560 240c2 0 3 0 5-1l2 2h0c2 1 2 1 3 2l1 1h2c1 1 0 2 2 1l-1 1v1h-1-2-8c0-3 0-3-2-5h-1v-2z" class="D"></path><path d="M561 242l2-1c1 1 1 2 2 3 2 1 4 1 6 3h-8c0-3 0-3-2-5z" class="I"></path><path d="M574 247h5v1h2l1 1s-1 1-1 2h0c1 1 1 0 2 1h2l1 1-6 1-5 2-1 3-1 1h0l-1 2c-1 2 0 4 0 7l-1 7 1 6h-1v-2h-2v2h-1s-1-1-2-1h-1v-2-1-4h0c-1-3-1-4 0-6v-1l2-4v-1c0-1 1-2 2-3v-1l1-1c0-3 2-5 4-8v-1l-1-1h1z" class="AI"></path><path d="M569 274h1v-3c0 1 0 1 1 3v6h-2v-4-2z" class="w"></path><path d="M569 259c1 5 0 11 0 15v2l-1-11c0-1-1-1-1-2v-1c0-1 1-2 2-3z" class="x"></path><path d="M579 248h2l1 1s-1 1-1 2h0c1 1 1 0 2 1h2l1 1-6 1-5 2-1 3-1 1h0v-2-1c-1-2 0-4 1-6 2-2 3-3 5-3z" class="V"></path><path d="M581 251h0c1 1 1 0 2 1h2l1 1-6 1-5 2c0-1 0-2 2-3 1-1 2-1 3-2h1z" class="AF"></path><path d="M567 263c0 1 1 1 1 2l1 11v4 2h-1s-1-1-2-1h-1v-2-1-4h0c-1-3-1-4 0-6v-1l2-4z" class="N"></path><path d="M565 268l1 2-1 4h0c-1-3-1-4 0-6z" class="m"></path><path d="M580 254h9l3 1c-1 1-1 1-3 2h6c1 0 3 1 4 1h3v1h0c4 1 9 0 14 0h11c1 0 2 0 3 1v1c-2-1-2-1-4 0-1 0-2 0-2 1-1 0-2 1-3 2h-4c-2 0-5 1-7 1-2 1-3 1-5 2-3 1-5 2-8 3-3 2-6 4-9 7l3 2-2 3-3-1c0 1-1 3-1 4l-2 2v1h0v4h-1v-2l-1-1c-1 1-2 2-3 2h0l1-1c-1-1-1 0-1-1-2 2-3 2-5 3-1-2-1-4-1-6v-4l-1-6 1-7c0-3-1-5 0-7l1-2h0l1-1 1-3 5-2z" class="z"></path><path d="M579 288c-1-1-1-1-1-2h0c1 0 1 0 3 1 1-1 1-2 2-4v3c0 1-2 1-1 2h1v4h-1v-2l-1-1c-1 1-2 2-3 2h0l1-1 1-1-1-1z" class="AE"></path><path d="M583 283v-1c1-1 1-3 3-4v-1c3-4 6-5 11-7-3 2-6 4-9 7l3 2-2 3-3-1c0 1-1 3-1 4l-2 2v1h0-1c-1-1 1-1 1-2v-3z" class="j"></path><path d="M586 281l2-4 3 2-2 3-3-1z" class="C"></path><path d="M581 275c1-1 2-1 4-2 1-1 1-1 2-1-1 2-3 3-5 5-2 4-5 6-7 9v2h4l1 1-1 1c-1-1-1 0-1-1-2 2-3 2-5 3-1-2-1-4-1-6v-3c3-3 5-6 9-8z" class="Y"></path><defs><linearGradient id="AE" x1="594.963" y1="259.262" x2="601.241" y2="267.355" xlink:href="#B"><stop offset="0" stop-color="#333233"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#AE)" d="M616 259h11c1 0 2 0 3 1v1c-2-1-2-1-4 0-1 0-2 0-2 1-1 0-2 1-3 2h-4c-2 0-5 1-7 1l-2-1c-7 2-14 4-21 8-1 0-1 0-2 1-2 1-3 1-4 2l7-6c1-1 1-1 1-2h0v-4l1-1c2-1 4-1 6-2 1 0 4-1 6-1 4 1 9 0 14 0z"></path><path d="M590 262c2-1 4-1 6-2-1 1-2 2-3 2-1 1-2 1-2 2v1h-1v-3z" class="M"></path><path d="M616 259h11c1 0 2 0 3 1v1c-2-1-2-1-4 0-1 0-2 0-2 1l-12 1 4-4z" class="z"></path><path d="M580 254h9l3 1c-1 1-1 1-3 2h6c1 0 3 1 4 1h3v1h0c-2 0-5 1-6 1-2 1-4 1-6 2l-1 1v4h0c0 1 0 1-1 2l-7 6c-4 2-6 5-9 8v3-4l-1-6 1-7c0-3-1-5 0-7l1-2h0l1-1 1-3 5-2z" class="B"></path><path d="M572 269c0-3-1-5 0-7 1 1 3 1 5 1h0 1l-6 2v4z" class="b"></path><path d="M571 276c1 2 1 2 2 2 2-2 7-7 10-8 1-1 1 0 2-1h1l1-1h1c0-1 0-2-1-3h0l2-2v4h0c0 1 0 1-1 2l-7 6c-4 2-6 5-9 8v3-4l-1-6z" class="f"></path><path d="M580 254h9l3 1c-1 1-1 1-3 2h6c1 0 3 1 4 1-2 0-4 1-7 1-2-1-3 0-4 0-2 1-2 1-3 1-2 0-4 0-4 1l-1 1-2 1h-1 0c-2 0-4 0-5-1l1-2h0l1-1 1-3 5-2z" class="R"></path><path d="M580 254h9l3 1c-1 1-1 1-3 2-5 1-11 1-15 2l1-3 5-2z" class="L"></path><path d="M549 280h1l1 1v5c1 2 1 3 1 5h1c0 1 0 2 1 3 0 2-1 4-1 6l2 2v-1l2 1c1 3 1 5 1 8l-1 1c-1 1-1 2-1 3l1 1c-1 2-1 3 0 4 0 1 1 1 2 2-2 2-1 8-2 11 1 1 2 2 2 3 0 2-1 8 0 10l1-1h0c0 1 0 9-1 9l-3 26-1 14v8l-1 1v2l-1-2v4c-2 2-1 5-1 7l-1 52v11c0 2 0 4-1 6v-64l-1 1v-1l1-1c-1-2 0-3 0-4 0-2 0-2-1-3-1 3 0 7-1 10v-6-3l-2-1h0c1-3 1-5 1-7-1-1-1-5-1-7l-1 4v-14c0-5-2-12-1-16 1-8 0-17-1-25v-8l1-3 4 1v-11-23c0-1-1-1-1-1 0-1 0-3 1-4l1 1h0v-1c-1-2 0-6 0-7 1-2 0-7 0-9z" class="p"></path><path d="M551 343l-1 47v13c-2-3-1-10-1-13v-36c1-3 0-8 2-11z" class="AB"></path><path d="M553 347c1 6-1 14 1 21v4c-2 7-1 16-1 23v7 4c-2 2-1 5-1 7-2-13 0-27 0-40v-16c0-3 0-7 1-10z" class="Y"></path><path d="M544 334l4 1-1 12 1 67v-3l-2-1h0c1-3 1-5 1-7-1-1-1-5-1-7l-1 4v-14c0-5-2-12-1-16 1-8 0-17-1-25v-8l1-3z" class="E"></path><path d="M546 348v11h0l-1-7v-3l1-1z" class="o"></path><path d="M544 334l4 1-1 12-1 1-1 1v3h0c0-3 0-4-2-7v-8l1-3z" class="S"></path><path d="M554 329c0 1 1 1 1 2v2c1 1 0 3 1 4l1-1v-4c1 1 2 2 2 3 0 2-1 8 0 10l1-1h0c0 1 0 9-1 9l-3 26-1 14v8l-1 1v2l-1-2v-7c0-7-1-16 1-23v-4c-2-7 0-15-1-21v-1l1-17z" class="AF"></path><path d="M557 332c1 1 2 2 2 3 0 2-1 8 0 10l1-1h0c0 1 0 9-1 9v-2h0c-2 2-1 3-2 6v-4c-1-2 0-10 0-13v-4-4z" class="t"></path><path d="M554 329c0 1 1 1 1 2v2c1 1 0 3 1 4l1-1v4c-1 7 0 15-3 21h0c0-4 1-12-1-15l1-17z" class="s"></path><path d="M557 357c1-3 0-4 2-6h0v2l-3 26-1 14v8l-1 1v2l-1-2v-7c0-7-1-16 1-23v-2l1 1-1 5v5h1c0-5 1-10 2-15 0-2-1-5 0-7v-2z" class="j"></path><path d="M553 395l1-2c1 2 0 6 1 8l-1 1v2l-1-2v-7z" class="y"></path><path d="M549 280h1l1 1v5c1 2 1 3 1 5h1c0 1 0 2 1 3 0 2-1 4-1 6l2 2v-1l2 1c1 3 1 5 1 8l-1 1c-1 1-1 2-1 3l1 1c-1 2-1 3 0 4 0 1 1 1 2 2-2 2-1 8-2 11v4l-1 1c-1-1 0-3-1-4v-2c0-1-1-1-1-2v-3c-3 3-2 8-2 12 0 1 0 1-1 2v3c-2 3-1 8-2 11-1-2-1-7-1-9 1-7 1-14 2-21 0-5-1-9-1-14 0-2 1-6 0-7 0-1-1-2-1-2 0-1-1-1-1-1 0-1 0-3 1-4l1 1h0v-1c-1-2 0-6 0-7 1-2 0-7 0-9z" class="U"></path><path d="M551 286c1 2 1 3 1 5h1c0 1 0 2 1 3 0 2-1 4-1 6 0 5 1 11-1 15v-11c0-2 0-5-1-6h0l1-1v-2l-1-1v-8z" class="H"></path><path d="M553 300l2 2v-1l2 1c1 3 1 5 1 8l-1 1c-1 1-1 2-1 3l1 1c-1 2-1 3 0 4 0 1 1 1 2 2-2 2-1 8-2 11v4l-1 1c-1-1 0-3-1-4v-2c0-1-1-1-1-2v-3c-3 3-2 8-2 12 0 0-1 0-1-1v-8c1-1 1-2 1-3v-11c2-4 1-10 1-15z" class="AP"></path><path d="M555 301l2 1c1 3 1 5 1 8l-1 1c-1 1-1 2-1 3l-1 1-1 3 1-16v-1z" class="n"></path><path d="M554 318l1-3 1-1 1 1c-1 2-1 3 0 4 0 1 1 1 2 2-2 2-1 8-2 11v4l-1 1c-1-1 0-3-1-4v-2c0-1-1-1-1-2v-3-8z" class="T"></path><path d="M570 217l2 1 29 15 7 3c0 1 1 2 2 2 4 2 8 6 13 8l7 4c1 0 3 1 4 2l1 1c7 4 13 8 19 14l-1 1h-1c0-1-1-1-2-2-5-3-10-6-17-9-2 1-4 1-7 1l1 1h-11c-5 0-10 1-14 0h0v-1h-3c-1 0-3-1-4-1h-6c2-1 2-1 3-2l-3-1h-9l6-1-1-1h-2c-1-1-1 0-2-1h0c0-1 1-2 1-2l-1-1h-2v-1h-5v-1l1-1c-2 1-1 0-2-1h-2l-1-1c-1-1-1-1-3-2h0 1c1-1 2-1 3-1h2l1-2c-1-1-2-1-3-1l1-1h4l-2-1v-1l-6-3c1 0 2-1 3-1h1c1 1 3 2 5 2 3-2 6 2 9 2-1-1-3-2-4-3h-1l-2-1-2 1-1-1c-2-1-4-2-7-4l-1 1-3-3 3 1v-1h0c0-1-1-3-2-4l-5-3c2 0 3 1 5 1 1 0 3-1 4-1z" class="c"></path><path d="M568 231c1 0 2-1 3-1h1c1 1 3 2 5 2l4 4c1 3 5 5 8 7-3 0-7-4-10-5-1-1-2-1-3-2l-2-1v-1l-6-3z" class="AU"></path><path d="M566 220l8 4c2 1 7 3 8 4h-1v2 1l-2-1-2 1-1-1c-2-1-4-2-7-4l-1 1-3-3 3 1v-1h0c0-1-1-3-2-4z" class="C"></path><path d="M566 220l8 4v2h-2-1l-1-1-1 1-1 1-3-3 3 1v-1h0c0-1-1-3-2-4z" class="AU"></path><path d="M576 236c1 1 2 1 3 2 3 1 7 5 10 5l1 1h-1c1 1 0 1 1 1h2c1 1 2 1 3 1h0 2l-1 1h1c1 1 2 1 3 2h1l-1 1c-3 0-6 1-9 0-2-1-4-1-6-1-1-1-2 0-3 0l-1-1h-2v-1h-5v-1l1-1c-2 1-1 0-2-1h-2l-1-1c-1-1-1-1-3-2h0 1c1-1 2-1 3-1h2l1-2c-1-1-2-1-3-1l1-1h4z" class="D"></path><path d="M574 246h2v-1l-1-2v-1c2 2 3 3 6 3 2 0 4 1 6 2h-8-5v-1z" class="AM"></path><path d="M573 240v1h5v-1h1l-1 1h-3v1 1l1 2v1h-2l1-1c-2 1-1 0-2-1h-2l-1-1c-1-1-1-1-3-2h0 1c1-1 2-1 3-1h2z" class="AR"></path><path d="M587 247c4 1 9 2 13 2h1l-1 1c-3 0-6 1-9 0-2-1-4-1-6-1-1-1-2 0-3 0l-1-1h-2v-1h8z" class="AF"></path><path d="M582 249c1 0 2-1 3 0 2 0 4 0 6 1 3 1 6 0 9 0l1-1 21 4 11 4c-2 1-4 1-7 1l1 1h-11c-5 0-10 1-14 0h0v-1h-3c-1 0-3-1-4-1h-6c2-1 2-1 3-2l-3-1h-9l6-1-1-1h-2c-1-1-1 0-2-1h0c0-1 1-2 1-2z" class="Y"></path><path d="M586 253c1-1 3-1 5-1 6 0 12 1 18 2h2l2 1h-21l-3-1h-9l6-1z" class="j"></path><path d="M622 253l11 4c-2 1-4 1-7 1l1 1h-11c-5 0-10 1-14 0h0v-1h-3c-1 0-3-1-4-1h-6c2-1 2-1 3-2h21 1 4 1c1-1 2-1 3-2z" class="AD"></path><path d="M622 253l11 4c-2 1-4 1-7 1h0c-2-2-9-2-12-3h4 1c1-1 2-1 3-2z" class="AZ"></path><path d="M595 257c7-1 14-1 21 0 4 0 7 1 10 1h0l1 1h-11c-5 0-10 1-14 0h0v-1h-3c-1 0-3-1-4-1z" class="b"></path><path d="M532 240l1-2 1 1v-1l2 1v-1h1c1 1 1 1 3 2 0 1 1 1 0 2 2 1 4 0 5 0h3v1c-2 0-2 1-2 2h3l1-1v2 1h2v1c1 1 0 1 2 1h2 1l1 1-1 1v3l-2 9v8h1c0 3-1 5-1 8h0l1 1c-2 2-1 3-1 6v2h-1l-1 1 1 1-1 1h-1c0-2 0-3-1-5v-5l-1-1h-1c0 2 1 7 0 9 0 1-1 5 0 7v1h0l-1-1c-1 1-1 3-1 4 0 0 1 0 1 1v23 11l-4-1-1 3v-11l-2 1h0c0-3-1-4-2-6 0-1-1-2-1-2h-1c0 2-1 3-1 4v3c-1 1-1 1-1 2h-1 0c-1-3-3-4-4-6l-2-1c-2 0-3 0-4 1-2 2-4 3-5 5l-1 1v-1-7-1c1-4 0-7 1-12h1v-3-3-5-5-1l1-1 1-1v1l2-2c0-1 1-1 2-3l1-5 4-14-1-1c1-1 1-1 1-2l1-3 2-2v-5c0-1-1-1-1-2l3-2v-1h0l-6 1c1-1 2-1 3-1v-3l-1-1c1-1 0-1 0-2v-1z" class="p"></path><path d="M539 309c0-2 0-3 1-4h1c0 2 0 3 1 5s1 7 1 9h-1l1-2h-1v-4-1-1-1l-2 1-1-2z" class="H"></path><path d="M541 320c1 0 2 0 2 1 1 1 0 4 0 5h0l-2 1h0c0-3-1-4-2-6l2-1z" class="B"></path><path d="M541 283h1c0 4 1 10 1 15l-2-2c0-1-1-2-1-2l-1 1h0c1 1 1 2 2 3l-2 2v1-1-3c-1 0-2 0-3-1s-1-1 0-1c0-2 0-4-1-6v-1c1 0 2 0 3 1l2 2h0l1-2v-2c-1-1-1-3 0-4z" class="o"></path><path d="M539 301v-1l2-2c2 3 2 7 2 11l-1 1c-1-2-1-3-1-5h-1c-1 1-1 2-1 4-2 1-3 3-4 3h-1v-3-1h1c0-1 0-2 1-3v-1c1-1 1-2 3-3z" class="S"></path><path d="M539 309l1 2 2-1v1 1 1c-2 2-1 4-2 6l1 1-2 1c0-1-1-2-1-2h-1c0 2-1 3-1 4v3c-1 1-1 1-1 2h-1c0-1 0-2-1-3v-6l1-1h1l1 1h0c0-1 1-3 1-5l-1 1-1-1v-2h-1 1c1 0 2-2 4-3z" class="E"></path><path d="M532 290l3-1c1 2 1 4 1 6-1 0-1 0 0 1s2 1 3 1v3 1c-2 1-2 2-3 3v1c-1 1-1 2-1 3h-1v1 3h1v2l1 1 1-1c0 2-1 4-1 5h0l-1-1h-1l-1 1-1-4c-1-2-1-3-1-5-1-2-1-3-1-5v-5c1-2 1-3 1-4l1-1-1-1 1-4z" class="Z"></path><path d="M532 290l3-1c1 2 1 4 1 6-1 0-1 0 0 1s2 1 3 1v3 1c-2 1-2 2-3 3v1-2c1-2 0-2 0-3 0 0 1-1 2-1l-2-2-1 1c-3 1-3 4-4 6l-1 1v-5c1-2 1-3 1-4l1-1-1-1 1-4z" class="O"></path><path d="M546 262c0-2-1-8 0-10l-1-1h1c2 0 2-1 4 0v1c-1 2-1 5-1 8v14c0 2 1 4 0 6 0 2 1 7 0 9 0 1-1 5 0 7v1h0l-1-1c-1 1-1 3-1 4 0 0 1 0 1 1v23 11l-4-1v-56h1c0 1 0 2 1 3v-1-9-5c-1-1 0-3 0-4z" class="AB"></path><path d="M532 240l1-2 1 1v-1l2 1v-1h1c1 1 1 1 3 2 0 1 1 1 0 2 2 1 4 0 5 0h3v1c-2 0-2 1-2 2h3l1-1v2 1h2v1c1 1 0 1 2 1h2 1l1 1-1 1v3l-2 9v8h1c0 3-1 5-1 8h0l1 1c-2 2-1 3-1 6v2h-1l-1 1 1 1-1 1h-1c0-2 0-3-1-5v-5l-1-1h-1c1-2 0-4 0-6v-14c0-3 0-6 1-8v-1c-2-1-2 0-4 0h-1l1 1c-1 2 0 8 0 10-3-3-1-8-3-12-1 0-2 0-3-1 1 0 1-1 1-1-1-1-1-2-2-3-1 1-2 1-3 1v-1h-1l1 2-6 1c1-1 2-1 3-1v-3l-1-1c1-1 0-1 0-2v-1z" class="v"></path><path d="M550 247s-1 1-1 2c-1 1-2 1-3 0v-1-1c2-1 2-1 4-1v1z" class="w"></path><path d="M545 242h3v1c-2 0-2 1-2 2h-1c-1 0-2 1-3 0-1 0-4-1-5-1 1-2 2-2 3-2 2 1 4 0 5 0z" class="G"></path><path d="M545 242h3v1c-2 0-2 1-2 2h-1-2c1-2 1-2 2-3z" class="D"></path><path d="M551 273v-13-7l1-1v-1c1 0 2 0 4 1v1l1 1-2 9v8h1c0 3-1 5-1 8h0l1 1c-2 2-1 3-1 6v2h-1l-1 1 1 1-1 1h-1c0-2 0-3-1-5v-5l-1-1 1-7z" class="F"></path><path d="M551 273c0-1 1-1 1-2s0-2 1-2v-4h1v8c0 2 0 5 1 6l1 1c-2 2-1 3-1 6v2h-1l-1 1 1 1-1 1h-1c0-2 0-3-1-5v-5l-1-1 1-7z" class="H"></path><path d="M554 273c0 2 0 5 1 6l1 1c-2 2-1 3-1 6-1-1-1-1-2-1v-2c0-4 1-7 1-10z" class="AB"></path><path d="M551 281h1l1 2v2c1 0 1 0 2 1v2h-1l-1 1 1 1-1 1h-1c0-2 0-3-1-5v-5z" class="e"></path><path d="M530 284c1-1 2-1 3-2 1 2 0 6-1 8l-1 4 1 1-1 1c0 1 0 2-1 4v5c0 2 0 3 1 5 0 2 0 3 1 5l1 4v6c1 1 1 2 1 3h0c-1-3-3-4-4-6l-2-1c-2 0-3 0-4 1-2 2-4 3-5 5l-1 1v-1-7-1c1-4 0-7 1-12h1v-3-3-5-5-1l1-1 1-1v1l2-2h4c1-1 2-2 2-3z" class="p"></path><path d="M525 300h0c0 3-1 3-2 6 0 1 0 1-1 2 0-2 0-4-1-5 1-1 3-2 4-3z" class="AL"></path><path d="M524 287h4l-3 4c-1 2-2 3-4 5v-1c0-1 0-3-1-4v-1l1-1 1-1v1l2-2z" class="Z"></path><path d="M531 294l1 1-1 1c0 1 0 2-1 4v5c0 2 0 3 1 5 0 2 0 3 1 5l1 4v6-2c-1-1-1-2-2-4 0-1-1-2-1-3-1-1-1-4-1-6v-2h-1v3h-1v-1h-1l-1-1c0-2 0-3 1-4 0-3 1-5 3-8 1-1 1-2 2-3z" class="F"></path><path d="M520 307l1 2c1 2 2 3 4 4h1c1 1 1 1 1 2 0 2 1 3 2 4s1 2 1 3l-2-1c-2 0-3 0-4 1-2 2-4 3-5 5l-1 1v-1-7-1c1-4 0-7 1-12h1z" class="u"></path><path d="M520 307l1 2-2 2c0 2 0 4 1 6s0 3 0 5h1 3c-2 2-4 3-5 5l-1 1v-1-7-1c1-4 0-7 1-12h1z" class="D"></path><path d="M536 247l-1-2h1v1c1 0 2 0 3-1 1 1 1 2 2 3 0 0 0 1-1 1 1 1 2 1 3 1 2 4 0 9 3 12 0 1-1 3 0 4v5 9 1c-1-1-1-2-1-3h-1-1c0 2 0 3-1 5h-1c-1 1-1 3 0 4v2l-1 2h0l-2-2c-1-1-2-1-3-1v1l-3 1c1-2 2-6 1-8-1 1-2 1-3 2 0 1-1 2-2 3h-4c0-1 1-1 2-3l1-5 4-14-1-1c1-1 1-1 1-2l1-3 2-2v-5c0-1-1-1-1-2l3-2v-1h0z" class="AB"></path><path d="M536 247l-1-2h1v1c1 0 2 0 3-1 1 1 1 2 2 3 0 0 0 1-1 1 1 1 2 1 3 1 2 4 0 9 3 12 0 1-1 3 0 4v5 9 1c-1-1-1-2-1-3h-1-1c-1-5 2-13-1-18v-2c0-3-1-4-1-6l-1 1v1h-1v-4h0v-2h-2-1v-1h0z" class="F"></path><path d="M536 269l1-4c1 0 1 1 3 1 2 5 1 12 1 17-1 1-1 3 0 4v2l-1 2h0l-2-2c0-2 0-5-2-6v-1c0-1-1-2-2-3 0-4 1-6 2-10z" class="J"></path><path d="M536 248h1 2v2h0v4l-3 10c0 1-1 3-1 4s1 1 1 1c-1 4-2 6-2 10 1 1 2 2 2 3v1c2 1 2 4 2 6-1-1-2-1-3-1v1l-3 1c1-2 2-6 1-8-1 1-2 1-3 2 0 1-1 2-2 3h-4c0-1 1-1 2-3l1-5 4-14-1-1c1-1 1-1 1-2l1-3 2-2v-5c0-1-1-1-1-2l3-2z" class="Z"></path><path d="M532 259l2-2c0 3 0 4-2 7h-2c1-1 1-1 1-2l1-3z" class="o"></path><path d="M531 265c1 2 1 3 2 5 0 1 0 1-1 3 0 1-1 3-1 4v2c-1 2-1 3-1 5 0 1-1 2-2 3h-4c0-1 1-1 2-3l1-5 4-14z" class="d"></path><path d="M531 265c1 2 1 3 2 5 0 1 0 1-1 3v-3h-1c-1 1-2 5-2 7l-2 2 4-14z" class="P"></path><path d="M621 273c2-1 5-1 8-1 4 0 8 1 12 1l1-1 1 1h6v1h1c2 0 4 1 6 1 1 1 2 1 3 2 1 0 2 1 3 1 2 0 4 1 5 0 2 1 3 2 5 3l-1 3-2-1v3c1 2 1 2 0 3l1 1c1 1 2 1 3 2l7 5 7 6 1 1-1 2-5 18-6 16-1 3-9-3h-1l2 2c-1 1-1 1-2 1 0-1-1-1-2-1-4 0-8 1-12 3l-6 5-1-1c1-1 2-3 3-5 1-6 2-12-1-18v1h0c-2-3-4-4-6-6-2 1-2 0-3 0-2 0-3 0-4 1l-1-2c1-2 2-3 2-6 1-2 0-4 0-6 0-1-1-2-1-2v-1c-2-2-4-3-6-5-1-1-1-2-3-3-1-1-4-2-6-4h-1c-3-1-9 0-12-3-2-1-4-4-4-6h0c-1-1-1-2-1-3l1-1s2-1 3-1c5-2 9-2 15-3h0l-6-1h5c0-1 1-2 2-2h1z" class="AG"></path><path d="M666 320h2c1 1 2 2 2 3v1c-1 1-1 1-2 1-1-1-1-1-1-2s-1-2-1-3z" class="J"></path><path d="M649 310c0-2 0-2 1-3l6 3-1 1-6-1zm10 6h0c1-1 1-1 1-2 2-1 1-3 2-5 1 1 0 2 0 4h0c-1 1 0 7 0 8 1 0 1 1 2 2h1c1 4 1 5 3 9 0 1 0 1 2 3 0 1 1 1 2 2 2 1 2 2 4 3l-1 3-9-3c-1-2-1-2-1-3-1-2-2-2-2-3l1-2h-1c-1-1-1-2-1-3s-1-2-1-3c-1-3-1-6-3-8l1-2z" class="B"></path><path d="M621 273c2-1 5-1 8-1 4 0 8 1 12 1l1-1 1 1h6v1h1c2 0 4 1 6 1 1 1 2 1 3 2 1 0 2 1 3 1 2 0 4 1 5 0 2 1 3 2 5 3l-1 3-2-1v3c1 2 1 2 0 3-3-1-5-3-8-4l-4-1-11-3c-9-2-18-4-27-5h0l-6-1h5c0-1 1-2 2-2h1z" class="H"></path><path d="M667 278c2 1 3 2 5 3l-1 3-2-1-7-5c2 0 4 1 5 0z" class="W"></path><path d="M643 273h6v1h1c2 0 4 1 6 1h-7l-1 1 3 2h-1c-1 0-3 0-5-1h-2l-1-1 1-1v-1-1z" class="K"></path><path d="M621 273c2-1 5-1 8-1 4 0 8 1 12 1l1-1 1 1v1 1l-1 1 1 1h2c2 1 4 1 5 1h1 1c2 1 5 0 7 2l-1 1h-4l-1-1h-1 0c-2 0 1 0-1 0-2-1-3 0-4 0l-1 1c-9-2-18-4-27-5h0l-6-1h5c0-1 1-2 2-2h1z" class="Q"></path><path d="M621 273c2-1 5-1 8-1 4 0 8 1 12 1l1-1 1 1v1 1l-1 1c-3-2-8-1-11-1-2-1-3-1-5 0 2 0 7 1 9 2h0l-16-1-6-1h5c0-1 1-2 2-2h1z" class="R"></path><path d="M642 272l1 1v1 1l-1 1c-3-2-8-1-11-1-2-1-3-1-5 0-1 0-1-1-2-1v-1c2-1 6 0 8 0 3 1 6 1 9 0l1-1z" class="W"></path><path d="M657 295l-9-7v-1l1 1h3c2 0 3 1 5 1 1 0 4 0 5 1h1c1 0 2 0 3 1h0c2 1 7 4 8 6 3 2 4 4 7 5 1 2 4 5 4 7-1 1-1 2-1 3h1v-1c0-2 1-3 2-5l-5 18-5-7-7-10c-4-5-8-9-13-12z" class="b"></path><path d="M663 294c3 1 5 2 8 4 1 1 1 1 3 1h0c1 3 2 5 4 8-3-2-5-5-7-6-1-1-2-3-4-3-1-1-3-2-4-4z" class="K"></path><path d="M657 295l3-1c0-1-1 0 0-1 2 0 2 0 3 1 1 2 3 3 4 4l-2-1c1 2 3 5 5 6 1 0 1 1 1 1l1 1c2 1 5 6 5 9h0l1 1-1 2-7-10c-4-5-8-9-13-12z" class="W"></path><defs><linearGradient id="AF" x1="644.165" y1="297.761" x2="628.442" y2="313.598" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#AF)" d="M617 293c0-2-4-4-6-5s-5-3-8-3l-1-1h1 2 1c2-1 3-1 5-1l24 14 11 7c0 1 4 2 4 3-1 1-1 1-1 3l6 1 1-1c2 0 2 0 3 2v4l-1 2c2 2 2 5 3 8 0 1 1 2 1 3s0 2 1 3h1l-1 2c0 1 1 1 2 3 0 1 0 1 1 3h-1l2 2c-1 1-1 1-2 1 0-1-1-1-2-1-4 0-8 1-12 3l-6 5-1-1c1-1 2-3 3-5 1-6 2-12-1-18v1h0c-2-3-4-4-6-6-2 1-2 0-3 0-2 0-3 0-4 1l-1-2c1-2 2-3 2-6 1-2 0-4 0-6 0-1-1-2-1-2v-1c-2-2-4-3-6-5-1-1-1-2-3-3-1-1-4-2-6-4h-1z"></path><path d="M661 326v1 1c-1 1-2 1-3 1h-1v-2c0-2 0-7 1-9 2 2 2 5 3 8z" class="f"></path><path d="M663 334c0 1 1 1 2 3 0 1 0 1 1 3h-1-1c-1 0-1-1-2 0-1 0-2 0-3-1 0-1-1-2-1-3 2-2 3-2 5-2z" class="M"></path><path d="M655 311c1 0 2 1 3 1v1c-1 2-2 2-4 2-3-1-5-3-7-5l1-1 1 1 6 1z" class="J"></path><path d="M650 333v2h1 0v-2c1-1 1-1 1-2h0c1 1 2 3 2 4l1 1-1 1c1 1 2 2 3 2h1v2h2c2-1 3-1 4-1h1l2 2c-1 1-1 1-2 1 0-1-1-1-2-1-4 0-8 1-12 3v-1-1l1-2c1-2 2-4 1-7h-1l-1 3h-1c-1-1 0-3 0-4z" class="V"></path><path d="M634 314c2 0 3-1 4-1v-2l1-1v-1h1v-1-2l2-1c1 1 1 2 1 3l3 6c-1 0-3-2-3-2-1-1 1-2-1-3-1 2-1 5-1 7l-1 1h1l2 2h-1-1l-1 1h0v1c-2 1-2 0-3 0-2 0-3 0-4 1l-1-2c1-2 2-3 2-6z" class="g"></path><path d="M641 317h-1l1-1c0-2 0-5 1-7 2 1 0 2 1 3 0 0 2 2 3 2 3 6 5 13 4 19 0 1-1 3 0 4h1l1-3h1c1 3 0 5-1 7l-1 2v1 1l-6 5-1-1c1-1 2-3 3-5 1-6 2-12-1-18v1h0c-2-3-4-4-6-6v-1h0l1-1h1 1l-2-2z" class="AF"></path><path d="M641 317l1-1c1 1 1 2 2 2l2 5c-1-1-3-3-5-3l1-1h1l-2-2z" class="t"></path><defs><linearGradient id="AG" x1="438.508" y1="678.938" x2="584.737" y2="363.093" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272626"></stop></linearGradient></defs><path fill="url(#AG)" d="M488 310l2 2c0 1 0 1-1 2h1c1 1 0 1 1 1l-1 1v1c2 1 1 8 3 9v-1c2-2 4-4 7-6 2 1 3 1 5 2l3 3c1 1 1 2 2 2 0-1 1-1 1-2h0c1-2 2-5 4-6l2 1h1v1 7 1l1-1c1-2 3-3 5-5 1-1 2-1 4-1l2 1c1 2 3 3 4 6h0 1c0-1 0-1 1-2v-3c0-1 1-2 1-4h1s1 1 1 2c1 2 2 3 2 6h0l2-1v11 8c1 8 2 17 1 25-1 4 1 11 1 16v14l1-4c0 2 0 6 1 7 0 2 0 4-1 7h0l2 1v3 6c1-3 0-7 1-10 1 1 1 1 1 3 0 1-1 2 0 4l-1 1v1l1-1v64 23 54l-1 48c1 9-1 81 1 84l-1 7h1c1 2 2 2 2 4v3c1 4 2 8 2 13-1-2-2-2-2-4-1 3 0 6 0 8v1c-1-2-1-4-2-6v-1c-2-3-4-4-7-6l-5 19v-1l-7-20c0 1 0 2 1 3v1 1l1 1v1c1 1 1 3 1 4 0 2-2 4-3 5-3 2-6 5-7 8-1 1-3 2-4 3-1 0-1-1-2-1l-17-43v-2c-1-6-2-11-5-16 0-1-1-2-1-3v-1-2c-1-1-1-1-1-2-2-3-5-4-8-6h0c-3-1-2-1-4-4-1-1-2-3-2-4-1 0-2 0-3 1h0c-1 0-2-1-2-2 2-2 1-3 1-5v-3h-1c0 1 1 3 0 4-2-2-1-5-1-7h0c-1-2-1-3-2-5l-1-6-1-4-2-5v-2l-4-10c0-2 0-3-1-5h1c1 2 1 2 2 3h0c-1-3-1-4-1-7 0 0 1 1 2 1h0v-2-1l3 1c1 1 1 1 2 1l1-1h1v-6l-1-1-1-11-1-21v-3c1-2 1-7 1-9h1v-19c0-1 0-1-1-2v-8-14l-2-2v-6-18h-1l-1-12c0-3 0-4-1-6 1-4-1-10 1-12v-1-8c-1-3-1-5-1-8 1-1 1-2 2-3l-1-9c1-2 0-4 1-6l1 1c0-1 1-1 2-1h0v-5l1 2v-29h0v-2c2-2 1-7 1-10v-27l1-4c0-5-1-10 1-14l1-1c2 0 2 0 4 1h3c1-2 2-4 2-7l2-6z"></path><path d="M487 460h1v1h-1v-1zm0 4h2v2h-1-1v-2zm-6-47c1 0 1 0 2 1v2c1 1 1 1 2 1 0 1-1 1-1 2h-2c0 2 1 1 2 2v2l-1 1c0 1-1 1-2 2 0-1 0-1-1-2l1-1h0v-2c-1-2-1-2 0-4 0-1-1-2-1-2l1-2zm-7 45c-1-13 0-27 0-41 0-10-1-22 0-33 0 4 0 8 1 12v2h1v-1 2 1c1 0 0 1 0 2v6l-1-4c-1 1 0 7 0 9v34h0c1 0 0 0 1-1h0v-1h1c0 2-2 2-2 4v2c0 2 0 5-1 7z" class="AC"></path><path d="M488 310l2 2c0 1 0 1-1 2h1c1 1 0 1 1 1l-1 1v1c2 1 1 8 3 9v-1c1 1 2 1 2 2-3 6 2 18 2 24v1c0-1-1-2-1-3v-2-2c0-1 0-1-1-2h0v-2-3c-1-1-1 0-1-1v-2-3c-1-1-1-1-1-2-1 0 0-1 0-1-1-1-2-1-2-2v-3-1c-1-1-2-1-2-2 1 0 1 0 2-1-1-1-1-1-2-1h-1v1 2c-1 1 0 1 0 2 1 0 1 5 1 5-1 2-3 3-3 4v5 1c1 3 0 8 0 11h-1c0-2 1-5 0-6v-3l-2 1v-2c0-5 1-9 1-13h-2 0c0-2 1-3 2-4 1-2 2-4 2-7l2-6z" class="B"></path><path d="M485 336v5l-2 1v-2c0-2 1-3 2-4z" class="M"></path><path d="M484 327l1-1 1 1-1 9c-1 1-2 2-2 4 0-5 1-9 1-13z" class="g"></path><path d="M547 544v-1c0-1 1-1 1-2v11 5l1 1h0v-5c0 2 0 5 1 6l-1 48h0v31h0c-1-1-1-2-1-3v-2c1-4 0-6 0-9v-12h0c0 2 0 4-1 6-1-7 0-16 0-23v-51z" class="U"></path><path d="M477 322c2 0 2 0 4 1h3c-1 1-2 2-2 4h0 2c0 4-1 8-1 13v2l-2 43c-1-6 0-13-1-19l-1-23-1-6c-1 0-1 0-2-1l-1 1h0c0-5-1-10 1-14l1-1z" class="l"></path><path d="M478 337c0-4-1-8 0-11 1-1 3 0 4 1h0c0 4 1 7-1 10 0 3-1 4-2 6l-1-6z" class="G"></path><path d="M477 322c2 0 2 0 4 1h3c-1 1-2 2-2 4-1-1-3-2-4-1-1 3 0 7 0 11-1 0-1 0-2-1l-1 1h0c0-5-1-10 1-14l1-1z" class="T"></path><path d="M547 618c1-2 1-4 1-6h0v12c0 3 1 5 0 9v2c0 1 0 2 1 3h0v-31h0c1 9-1 81 1 84l-1 7h1c1 2 2 2 2 4v3c1 4 2 8 2 13-1-2-2-2-2-4-1 3 0 6 0 8v1c-1-2-1-4-2-6v-1c0-1-1-2-1-3l-1-1h1v-1h0-1-1c-2-1-4-2-7-3 2-2 3-4 4-6 1-1 3-3 3-4 1-1 1-3 1-4v-10l-1-66z" class="w"></path><path d="M552 714c-1-1-1-3-2-4 0-2-1-7 0-9 1 1 1 2 1 3h0l1 1c1 4 2 8 2 13-1-2-2-2-2-4z" class="C"></path><path d="M475 337h0l-1 51c-1 11 0 23 0 33 0 14-1 28 0 41l-1 65c0-1 0-1-1-2v-8-14l-2-2v-6-18h-1l-1-12c0-3 0-4-1-6 1-4-1-10 1-12v-1-8c-1-3-1-5-1-8 1-1 1-2 2-3l-1-9c1-2 0-4 1-6l1 1c0-1 1-1 2-1h0v-5l1 2v-29h0v-2c2-2 1-7 1-10v-27l1-4z" class="AX"></path><path d="M472 407l1 2v19c0 4-1 7 0 10l-2 1v-12-6c1-3 1-6 1-9h0v-5z" class="U"></path><path d="M471 439l2-1v29h-1c0-2 0-7-1-9l-1-11c1-2 1-5 1-8z" class="S"></path><path d="M469 412l1 1c0-1 1-1 2-1 0 3 0 6-1 9v6 12c0 3 0 6-1 8l-1-18v-2l-1-9c1-2 0-4 1-6z" class="F"></path><path d="M467 430c1-1 1-2 2-3v2l1 18 1 11 1 45-2-2v-6-18h-1l-1-12c0-3 0-4-1-6 1-4-1-10 1-12v-1-8c-1-3-1-5-1-8z" class="E"></path><path d="M467 430c1-1 1-2 2-3v2c-1 3-1 6-1 9-1-3-1-5-1-8z" class="Z"></path><path d="M468 447c0 1 0 2 1 4h0c0 1 1 2 1 3v8 15h-1l-1-12c0-3 0-4-1-6 1-4-1-10 1-12z" class="F"></path><path d="M468 447c0 1 0 2 1 4v2c0 4 0 8-1 12 0-3 0-4-1-6 1-4-1-10 1-12z" class="e"></path><path d="M545 400l1-4c0 2 0 6 1 7 0 2 0 4-1 7h0l2 1v3 6c1-3 0-7 1-10 1 1 1 1 1 3 0 1-1 2 0 4l-1 1v1l1-1v64 23 54c-1-1-1-4-1-6v5h0l-1-1v-5-11c0 1-1 1-1 2v1l-1-63c0-1 0-1-1-2-1-2 0-10 0-12l-2-2v-1c1 0 1 0 2-1v-3h0c0-1-1-2-1-3v-1c0-1-1-2-1-3 1 0 1 0 2-1v-3h1l-1-27v-22z" class="F"></path><path d="M543 453c1 0 1 0 2-1v-3h1v32c0-1 0-1-1-2-1-2 0-10 0-12l-2-2v-1c1 0 1 0 2-1v-3h0c0-1-1-2-1-3v-1c0-1-1-2-1-3z" class="s"></path><path d="M471 555c1-2 1-7 1-9h1v8c1 3 3 7 4 10l9 24 12 31s-1 0-2-1c-1 2-1 3-1 5v2l-1 1v2c-1 2-1 4-2 5l-2 5c-1 1-1 3-2 4 0 3-2 6-3 9h-1v-1c-2 2-2 4-2 5-1-1-2-3-2-4-1 0-2 0-3 1h0c-1 0-2-1-2-2 2-2 1-3 1-5v-3h-1c0 1 1 3 0 4-2-2-1-5-1-7h0c-1-2-1-3-2-5l-1-6-1-4-2-5v-2l-4-10c0-2 0-3-1-5h1c1 2 1 2 2 3h0c-1-3-1-4-1-7 0 0 1 1 2 1h0v-2-1l3 1c1 1 1 1 2 1l1-1h1v-6l-1-1-1-11-1-21v-3z" class="X"></path><path d="M480 651l-1-2-2-7c0-2-1-4 0-6 0-1 0-2 1-3v-1c0-1 1-2 1-3l1-1v1 2 3c-1 6 2 11 4 16-2 2-2 4-2 5-1-1-2-3-2-4z" class="G"></path><path d="M474 590l1-2h0c1 3 1 5 0 8v6c-1 1-1 4-1 6 0 5 1 9 1 15 0-1-1-2-1-2h0c0 2 2 6 1 7-2-2-3-6-4-9v-2c-1-2-1-3-1-5l2-1-1-1c0-1 0-3 1-5 0 3-1 5 0 8h0c0 1 0 2 1 2 2-1 0-6 0-9 1-1 1-3 0-4v1h0c0-2 0-3-1-5l1-1h1v-6-1z" class="AZ"></path><path d="M467 596l3 1c1 1 1 1 2 1 1 2 1 3 1 5h0v-1c1 1 1 3 0 4 0 3 2 8 0 9-1 0-1-1-1-2h0c-1-3 0-5 0-8-1 2-1 4-1 5l1 1-2 1-1-2c0-1 0-1-1-2h0c-1-1-1-1-1-2l-1-1h0c-1-3-1-4-1-7 0 0 1 1 2 1h0v-2-1z" class="AX"></path><path d="M470 597c1 1 1 1 2 1 1 2 1 3 1 5h0-1-1v-2c-1-1-1-1-2-3l1-1z" class="AY"></path><path d="M467 596l3 1-1 1v12c0-1 0-1-1-2h0c-1-1-1-1-1-2l-1-1h0c-1-3-1-4-1-7 0 0 1 1 2 1h0v-2-1z" class="C"></path><path d="M463 602h1c1 2 1 2 2 3l1 1c0 1 0 1 1 2h0c1 1 1 1 1 2l1 2c0 2 0 3 1 5v2c1 3 2 7 4 9l1 1h1c0 2 0 2-1 4s-1 4-2 6c-1-2-1-3-2-5l-1-6-1-4-2-5v-2l-4-10c0-2 0-3-1-5z" class="l"></path><path d="M472 634h2l-2-4-1-5v-2c-1-1-1-2-1-3l1-1c1 3 2 7 4 9l1 1h1c0 2 0 2-1 4s-1 4-2 6c-1-2-1-3-2-5z" class="D"></path><path d="M471 558c2 2 2 4 4 6 1 4 2 7 3 11 2 8 1 16 3 24h-1c-1-1-1-3-3-4 0-1 1-1 1-2 0-2 0-4-1-6-1-1-2-1-3 0v3 1l-1-1-1-11-1-21z" class="l"></path><path d="M471 558c2 2 2 4 4 6-2 3-1 9-1 13 0 1 1 3 0 4-1-2-1-3-1-5h-1v3l-1-21z" class="C"></path><path d="M500 370v1c1 2 1 3 1 5s1 3 1 5c0-3-1-6 0-9 2 9 0 19 2 29v18c0 2 0 5-1 7 0 1 0 1-1 3h1c1 3-1 17 1 18 1-1 0-4 1-6 0-5-2-14 0-18 1 2 1 3 1 4 1 1 1 2 1 3v1c2-2 2-3 2-5 1-1 3 0 3-1v-2-2c1 0 2 0 3 1v-1c1-1 0-1 1-1h1l1-1c1 1 2 1 3 1v5 4c0 5 0 10 1 15v5l1 44c0-1 0-3 1-5 1 4 0 8 0 12v16c0 1 0 2-1 4-1-1 0-6 0-7 0-3-1-6-1-8v-12c0-2 1-3 0-5s0-6 0-8c-1-1-2-2-3-2h-1c1-1 0-1 1-1h1c0-1 0-2-1-3l-1-1h0c-1-1-2-1-2-1-1-1-1-2-2-3 0 1-1 1-1 1l-3-1 1 3c-2 1-1 0-3-1h-5l-1 1c-3-1-2-2-4-4-1 0-1 0-2 1 0-1-1-2-1-2h-1c0-1-1-2-1-3v-1h-3l-2-2 3-3h-1-4c-1-2-1-2 0-4l-2 2h-1l-1 1h-1c0-1 0-1-1-2l1-1c1-1 1-1 3-1v-1l1 1v-3h0l-1-1-1 2h-1l-2-1c0-1 0-2 1-3l-1-1v-1c1-2 1-4 0-5v-1c1-1 1-2 2-2h1v-2c-1-2-1-2 0-4l4-2v-4c-1 0-2-1-2-2 1 0 1 0 2-1v-1c0-3 0-4 2-6-1 0-1 0-1-1 0-2 0-3 1-5l1 1h1v-2c2-1 3-1 4-3h0c0-1 1-1 1-2v-1l1-1c2-1 1-3 1-4 1-1 2-2 2-3 0-2 0-4 1-6l-1-18z" class="V"></path><path d="M500 370v1c1 2 1 3 1 5s1 3 1 5c0-3-1-6 0-9 2 9 0 19 2 29v18c0 2 0 5-1 7-1-3 0-7-1-10 0-9 0-19-1-28l-1-18z" class="L"></path><defs><linearGradient id="AH" x1="527.751" y1="732.008" x2="485.33" y2="622.119" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#bdbbb9"></stop></linearGradient></defs><path fill="url(#AH)" d="M484 651h1c1-3 3-6 3-9 1-1 1-3 2-4l2-5c1-1 1-3 2-5v-2l1-1v-2c0-2 0-3 1-5 1 1 2 1 2 1l29 77 3 8c1 1 1 3 1 4s0 2 1 3v1 1l1 1v1c1 1 1 3 1 4 0 2-2 4-3 5-3 2-6 5-7 8-1 1-3 2-4 3-1 0-1-1-2-1l-17-43v-2c-1-6-2-11-5-16 0-1-1-2-1-3v-1-2c-1-1-1-1-1-2-2-3-5-4-8-6h0c-3-1-2-1-4-4 0-1 0-3 2-5v1z"></path><path d="M482 655c0-1 0-3 2-5v1l2 8c-3-1-2-1-4-4z" class="D"></path><defs><linearGradient id="AI" x1="498.788" y1="324.343" x2="523.751" y2="426.403" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#AI)" d="M511 324c1-2 2-5 4-6l2 1h1v1 7 1l1-1c1 1 1 1 1 3-1 2 0 5 0 7l1 9 1 13v55c0 12 1 23 0 35v-5c-1-5-1-10-1-15v-4-5c-1 0-2 0-3-1l-1 1h-1c-1 0 0 0-1 1v1c-1-1-2-1-3-1v2 2c0 1-2 0-3 1 0 2 0 3-2 5v-1c0-1 0-2-1-3 0-1 0-2-1-4-2 4 0 13 0 18-1 2 0 5-1 6-2-1 0-15-1-18h-1c1-2 1-2 1-3 1-2 1-5 1-7v-18c-2-10 0-20-2-29-1 3 0 6 0 9 0-2-1-3-1-5s0-3-1-5v-1l-3-19c0-6-5-18-2-24 0-1-1-1-2-2 2-2 4-4 7-6 2 1 3 1 5 2l3 3c1 1 1 2 2 2 0-1 1-1 1-2h0z"></path><path d="M500 319c2 1 3 1 5 2l3 3c1 1 1 2 2 2 0-1 1-1 1-2h0l1 1h-1v1c-2 2-2 2-3 5l-1 7-1 7v-3h0l-1-1v-4c-1-3-1-7-1-10 0-2-1-2-2-3s-1-1-2-1c-2 1-3 2-5 4 0-1-1-1-2-2 2-2 4-4 7-6z" class="t"></path><path d="M503 323h2v1c1 2 2 3 3 4v3l-1 7c-2-5-1-10-4-15z" class="Y"></path><path d="M500 319c2 1 3 1 5 2l3 3c1 1 1 2 2 2 0-1 1-1 1-2h0l1 1h-1v1c-2 2-2 2-3 5v-3c-1-1-2-2-3-4v-1h-2c-1 0-1-1-3 0s-3 2-5 4c0-1-1-1-2-2 2-2 4-4 7-6z" class="s"></path><path d="M502 324c1 1 2 1 2 3 0 3 0 7 1 10v4l1 1h0v3l-2 56c-2-10 0-20-2-29v-28c0-6 1-13 0-20z" class="AE"></path><path d="M500 323c1 0 1 0 2 1 1 7 0 14 0 20v28c-1 3 0 6 0 9 0-2-1-3-1-5s0-3-1-5v-1l-3-19c0-6-5-18-2-24 2-2 3-3 5-4z" class="u"></path><defs><linearGradient id="AJ" x1="545.354" y1="321.743" x2="530.574" y2="468.063" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3e3d3f"></stop></linearGradient></defs><path fill="url(#AJ)" d="M536 323c0-1 1-2 1-4h1s1 1 1 2c1 2 2 3 2 6h0l2-1v11 8c1 8 2 17 1 25-1 4 1 11 1 16v14 22l1 27h-1v3c-1 1-1 1-2 1v1c-2 1-3 1-5 2l-1-1c0 2 0 2-1 3s-2 1-3 1l-1 1v2l-2 2c-1 1-3 2-4 3-1 2-1 3-1 4-1 3 0 6-1 8 0 3-1 7 0 9-1 2-1 4-1 5l-1-44c1-12 0-23 0-35v-55l-1-13-1-9c0-2-1-5 0-7 0-2 0-2-1-3 1-2 3-3 5-5 1-1 2-1 4-1l2 1c1 2 3 3 4 6h0 1c0-1 0-1 1-2v-3z"></path><path d="M536 323c0-1 1-2 1-4h1s1 1 1 2c1 2 2 3 2 6h0c1 3 1 4 0 6l-2-6v-1h1l1-1c-2-1-2-2-3-3v-1l-2 2h0z" class="J"></path><path d="M526 389l1-1c1 4 2 14 0 17v2 1h-1v-10-9z" class="V"></path><path d="M541 327l2-1v11 8c1 8 2 17 1 25-1 4 1 11 1 16l-2-2v-1h-1v-1h-1l-1-1c0-2 1-3 1-5h0c1-3-1-6 0-8l1-1c0-2-1-4 0-5v-2h-1c2-2 1-5 0-7v-3c0-3-1-7-1-11v-2c0-2 0-2 1-4s1-3 0-6z" class="T"></path><path d="M524 453l2-55v10h1c1 2 1 4 1 6v4c1 1 1 1 1 2 0 3 1 4 1 7l1 1s0 1 1 1v2c-1 1-2 3-2 4l1 1h1v1l-1 1c0 2 0 4-1 5-1 2-1 3-1 5s-3 5-4 7l-1-2z" class="g"></path><path d="M524 322c1-1 2-1 4-1l2 1c1 2 3 3 4 6-1 0-1 0-2 1v1c1 3 0 4 0 6-1 2-1 5-1 7-1 6-2 12-2 18-1 9-2 18-2 27l-1 1v9l-2 55v26c0 3-1 7 0 9-1 2-1 4-1 5l-1-44c1-12 0-23 0-35v-55l-1-13-1-9c0-2-1-5 0-7 0-2 0-2-1-3 1-2 3-3 5-5z" class="c"></path><defs><linearGradient id="AK" x1="533.107" y1="356.481" x2="521.34" y2="358.741" xlink:href="#B"><stop offset="0" stop-color="#696866"></stop><stop offset="1" stop-color="#878689"></stop></linearGradient></defs><path fill="url(#AK)" d="M524 322c1-1 2-1 4-1l2 1c1 2 3 3 4 6-1 0-1 0-2 1v1c1 3 0 4 0 6-1 2-1 5-1 7-1 6-2 12-2 18-1 9-2 18-2 27l-1 1c-1-3-1-8-1-12l1-22v-19c0-2 1-5-1-8l-1-1c-1 1-3 2-4 3 0-2 0-2-1-3 1-2 3-3 5-5z"></path><path d="M530 333l1 1s0 1 1 2c-1 2-1 5-1 7-2-3-1-7-1-10z" class="g"></path><path d="M529 327c1 1 1 2 2 3h1c1 3 0 4 0 6-1-1-1-2-1-2l-1-1-1-6z" class="Y"></path><path d="M528 321l2 1c1 2 3 3 4 6-1 0-1 0-2 1v1h-1c-1-1-1-2-2-3-1 0-3-1-4-1 2-2 2-2 3-5z" class="V"></path><path d="M524 322c1-1 2-1 4-1-1 3-1 3-3 5l-1 1c-1 1-3 2-4 3 0-2 0-2-1-3 1-2 3-3 5-5z" class="f"></path><path d="M513 192c2-1 3-2 4-3l3 5c2 1 5 1 7 1 0 1 1 3 2 4 0 0 3 1 4 1h4 1 4l1-1 1-1c1 3 1 4 2 6l1 1v1 1l3 3c-1 1-2 1-3 2h2c2 0 2 0 3 1s6 3 9 4h0l5 3c1 1 2 3 2 4h0v1l-3-1 3 3 1-1c3 2 5 3 7 4l1 1 2-1 2 1h1c1 1 3 2 4 3-3 0-6-4-9-2-2 0-4-1-5-2h-1c-1 0-2 1-3 1l6 3v1l2 1h-4l-1 1c1 0 2 0 3 1l-1 2h-2c-1 0-2 0-3 1h-1l-2-2c-2 1-3 1-5 1s-4 0-6 2l-2 2v2 1h-2v-1-2l-1 1h-3c0-1 0-2 2-2v-1h-3c-1 0-3 1-5 0 1-1 0-1 0-2-2-1-2-1-3-2h-1v1l-2-1v1l-1-1-1 2v1c0 1 1 1 0 2l1 1v3c-1 0-2 0-3 1l6-1h0v1l-3 2c0 1 1 1 1 2v5l-2 2-1 3c0 1 0 1-1 2l1 1-4 14-1 5c-1 2-2 2-2 3l-2 2v-1l-1 1-1 1v1 5 5 3 3h-1c-1 5 0 8-1 12h-1l-2-1c-2 1-3 4-4 6h0c0 1-1 1-1 2-1 0-1-1-2-2l-3-3c-2-1-3-1-5-2-3 2-5 4-7 6v1c-2-1-1-8-3-9v-1l1-1c-1 0 0 0-1-1h-1c1-1 1-1 1-2l-2-2-2 6c0 3-1 5-2 7h-3c-2-1-2-1-4-1l-1 1c-2 4-1 9-1 14l-1 4v27c0 3 1 8-1 10v2h0v29l-1-2v5h0c-1 0-2 0-2 1l-1-1c-1 2 0 4-1 6l1 9c-1 1-1 2-2 3 0 3 0 5 1 8v8 1c-2 2 0 8-1 12 1 2 1 3 1 6l1 12h1v18 6l2 2v14 8c1 1 1 1 1 2v19h-1c0 2 0 7-1 9s1 5-1 8v-1c0-2-1-5-1-8v-8c0-3-1-5-2-7l-19-49-1-3c0-3-4-7-4-11l1-2c0-3 1-5 1-7v-5h-1c0 4 0 8-2 11h0c-1 4 1 9-1 13h0c-1 2-6 5-7 6-2 3-4 6-5 9-2-3-3-7-5-10-4-10-8-20-11-31-1-3 0-8-1-11-1-2-3-2-4-3-2-1-15-32-16-37h1c1 0 1-2 2-3v-1l1-16 2-7c1-1 1-2 1-3v-1h1v-9h0c0-2-2-6-3-8l-10-25-25-63c2-1 3-2 4-3l1-2 1-1c4-4 6-9 8-14 1-2 2-3 3-4l3-8 1-4v-1c1-1 0-2 0-3h2 0c2-1 0-6 3-8 3 4 6 8 10 11h1c1 2 3 3 4 3s3 1 4 1h0c2 1 4 2 6 2h1 3c2 1 4 1 5 1 2 0 3 0 5 1l86-52z" class="AK"></path><path d="M445 435h1l1 1-1 2c-1-1-1-2-2-2l1-1zm5 17c1 0 1 1 2 1-2 2-3 2-5 2h-2v-1h1c2-1 3-1 4-1v-1z" class="J"></path><path d="M398 308h1 0c1 2 2 4 1 7l-2-1 1-1c-1-2-2 0-3-2 1-1 1-2 2-3h0z" class="B"></path><path d="M379 259l1 1c0 1-1 2-1 3-1 2-3 3-4 5h-1-1 0l3-5c1-1 2-3 3-4z" class="W"></path><path d="M373 273l1 1 1-1h1c0 2 1 3 0 4l-1 1v1 1c0-1 0 0-1-1 0-1 0-1-1-1h-4l2-2 1-1c0-1 1-1 1-2z" class="h"></path><path d="M430 300h0l1 1-5 1c1 1 2 1 3 1-4 0-7 1-10 3 1-2 1-3 2-4h1l-2-1 3-1h2 5z" class="AC"></path><path d="M369 278c-1-1-1-2-2-2l1-1c0-2 0-4 2-5 0-1 2-2 3-3v-1c1-2 1-2 3-3l-3 5h0v1c-1 1-1 2-3 3v2l2-1c0-1 0 0 1 0 0 1-1 1-1 2l-1 1-2 2h0z" class="R"></path><path d="M425 283c-2 4-3 7-5 11v-1l-2 2 1-2c0-2 0-3-2-5h0v-1l2 1c1-2 4-4 6-5z" class="T"></path><path d="M445 467c1 2 1 3 0 5 0 2-1 4-1 6h1c2 1 4 1 5 3v1h-3l-1 1h0c2 3 2 5 2 7l-1-3c0-3-4-7-4-11l1-2c0-3 1-5 1-7z" class="J"></path><path d="M379 259c1-2 2-3 4-4l1 2h1v-1l1 2h0c0 1-1 2 0 4h-1l-1-2 1-1h-1l-1 2h1c0 2-2 3-3 4l1 2h-1c-1-1-1-1-1-2s1-2 2-2v-1c-2 1-2 1-3 1 0-1 1-2 1-3l-1-1z" class="x"></path><path d="M411 357l-20-14c4 1 9 3 12 5 1 1 2 1 4 2 2 2 3 3 5 6l-1 1z" class="t"></path><path d="M398 308c-2-2-4-2-7-1-1 0-2 1-3 2h0v-1c2-2 4-4 7-3 2 0 4 1 5 2s1 2 2 3l1 2h0c0 1 1 1 1 2v4l-1 4v1h-1-1c1-1 1-3 1-4-1-2-1 0-1-2l2-1v-2l-1-1c0-2-1-4-3-5h0-1z" class="T"></path><path d="M381 296c0 1-4 4-5 5h-1c0-2-1-3-1-5v-2c1-2 3-3 5-4l-1 2c1 0 1 0 1 1 2-1 3-1 5-1v1l-3 3h0z" class="R"></path><path d="M399 243l-6-6v-3c2 1 2 2 5 2h1c1 2 3 3 4 3s3 1 4 1h0c-2 1-3 1-5 1v-1c-1-1-2-1-3-1l6 6-6-2z" class="W"></path><path d="M435 275c1 0 2 0 3 2 1 0 1 1 2 2l1 1h0c2 3 1 7 2 10 0 2 0 2 1 3l1 1v2h1v1c-3-1-5-2-7-4s1-6 1-9c0-4-3-6-5-9z" class="i"></path><path d="M389 278c2-1 5-2 8-3 1-1 3-2 5-2h2v1c-1 2-4 3-6 4l-11 4-11 6v-1c2-2 5-4 8-5 2-1 3-2 5-4z" class="O"></path><path d="M381 278c1 1 3 1 4 1l1-1h3c-2 2-3 3-5 4-3 1-6 3-8 5v1h0c-1 1-2 1-4 2h-1c2-2 2-3 3-5 0-1 1-2 1-3-1 1-2 1-4 1 0 1 0 1-1 1v-2l-1-1h1 2c-1 1-1 1 0 1 1 1 2 0 2-1 3 0 5-1 7-3z" class="N"></path><path d="M420 301l2 1h-1c-1 1-1 2-2 4-6 5-9 10-11 17h-1v1l-3-2c0-1 0 0 1-1 1 0 1-1 1-1 1-2 1-3 2-5 2-5 6-11 12-14z" class="g"></path><path d="M405 321c1 0 1-1 1-1 1-2 1-3 2-5 0 2 0 4-1 6v2 1l-3-2c0-1 0 0 1-1z" class="AC"></path><path d="M375 280h0c2 0 2 0 3-1 1 0 2-1 3-1v-1l1-1h1v-1c-1 0-3 1-4 1l1-1h1l2-2 2-2c0-2 1-2 1-4-1 0-1-1-2-1l-1 1h-1l1-2h1 3c0 1 0 1 1 2l1-1v-1c0-1 1-1 1-2h0 1v1c-1 1-1 1-1 3-1 1 0 1 1 1-2 2-4 4-5 6-1 1-3 3-5 4-2 2-4 3-7 3 0 1-1 2-2 1-1 0-1 0 0-1h-2-1-2c0-1 1-2 2-3h0 4c1 0 1 0 1 1 1 1 1 0 1 1z" class="x"></path><path d="M403 322h1l3 2v-1h1c-3 12 2 22 8 33v1c-3-2-3-5-5-7l-5-10c-1-5-2-9-2-15 0 0-1-1-1-2v-1z" class="t"></path><path d="M403 322h1l3 2-2 7v-6h-1s-1-1-1-2v-1z" class="g"></path><path d="M389 242c2 0 3 0 4 1 2 1 4 0 6 0l6 2 1 1c2 0 4 1 6 1 1 1 2 3 3 4-3 0-8-1-12-2-1-1-2-1-3-1s-2 0-3-1l-4-1c-2-1-3-2-4-4z" class="K"></path><path d="M391 268l3-2 1 2-1 1-1 1 3 1v1c3 0 4 1 6 0v1c-2 0-4 1-5 2l-8 3h-3l-1 1c-1 0-3 0-4-1 2-1 4-3 5-4 1-2 3-4 5-6z" class="J"></path><path d="M391 268l3-2 1 2-1 1-1 1 3 1v1c-3 0-7 0-10 2 1-2 3-4 5-6z" class="D"></path><defs><linearGradient id="AL" x1="405.848" y1="343.378" x2="418.922" y2="365.916" xlink:href="#B"><stop offset="0" stop-color="#3b3a3a"></stop><stop offset="1" stop-color="#656769"></stop></linearGradient></defs><path fill="url(#AL)" d="M406 340l5 10c2 2 2 5 5 7v-1c4 5 9 12 10 18-2-2-3-5-5-7-3-4-6-7-10-10l1-1c-2-3-3-4-5-6-1-3-1-5-1-7v-3z"></path><path d="M388 225c3 4 6 8 10 11-3 0-3-1-5-2v3l6 6c-2 0-4 1-6 0-1-1-2-1-4-1l-1-2h0c-2-3-2-4-3-7 2-1 0-6 3-8z" class="a"></path><path d="M388 225c3 4 6 8 10 11-3 0-3-1-5-2-1-2-2-4-4-4l-1 1v2l-1 1c0 2 0 2 1 3v1 1 1h0c-2-3-2-4-3-7 2-1 0-6 3-8z" class="P"></path><path d="M403 312v-1-2c-1-1 0-1 1-2s2-2 4-2v-1c2-1 3-2 5-2 1-1 1-1 3-1h3c1-1 1-1 2-1h2c1-1 1-1 2-1h1 7c-1 1-1 1-2 1h-1-5-2l-3 1c-6 3-10 9-12 14-1 2-1 3-2 5 0 0 0 1-1 1-1 1-1 0-1 1h-1l1-4v-4c0-1-1-1-1-2h0z" class="f"></path><path d="M404 318c0-1 1-5 1-6-1-1-1-1-1-2l2-1 1 2c0 3-1 7-2 10-1 1-1 0-1 1h-1l1-4z" class="V"></path><path d="M379 290c7-4 15-6 23-9 4-1 7-3 11-3 2 0 4-1 6 0-10 3-19 7-27 12-4 2-7 5-11 6h0l3-3v-1c-2 0-3 0-5 1 0-1 0-1-1-1l1-2z" class="m"></path><path d="M451 302c3 0 4 1 7 3v4c1 1 1 2 1 3 1 0 2 1 2 2-1 2-3 5-3 7h-1v3c-1 0-1 1-2 1l-3-3c-1-1-1-2-2-2l-1 2h-1c-1-1-1-3-1-4 1-5 3-9 4-14v-1-1z" class="D"></path><path d="M451 302c3 0 4 1 7 3v4c1 1 1 2 1 3l-1-1-2-2c-2-3-1-4-5-6v-1z" class="C"></path><path d="M450 320c0-2 1-2 1-4h1l5 5v3c-1 0-1 1-2 1l-3-3c-1-1-1-2-2-2z" class="AE"></path><path d="M469 477h1v18 6l2 2v14 8c1 1 1 1 1 2v19h-1c0 2 0 7-1 9s1 5-1 8v-1c0-2-1-5-1-8v-8c1-3 0-6 0-9v-15l-1-40 1-5z" class="q"></path><defs><linearGradient id="AM" x1="395.369" y1="336.606" x2="405.711" y2="316.399" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#2f2e2f"></stop></linearGradient></defs><path fill="url(#AM)" d="M399 308c2 1 3 3 3 5l1 1v2l-2 1c0 2 0 0 1 2 0 1 0 3-1 4h1 1c0 1 1 2 1 2 0 6 1 10 2 15v3c0 2 0 4 1 7-2-1-3-1-4-2 1-2 1-3 0-5-1-1-2-3-3-4-1-3-3-5-6-6-1-1-1-2-1-2 0-2 0-2-1-3 0-3 1-4 2-6h0v-1c1-1 1-1 1-2 1-1 2-3 3-5l2 1c1-3 0-5-1-7z"></path><path d="M398 314l2 1c0 1-1 4-3 4 0 1-1 1-1 1l-2 2h0v-1c1-1 1-1 1-2 1-1 2-3 3-5z" class="M"></path><path d="M402 323h1c0 1 1 2 1 2 0 6 1 10 2 15v3c0 2 0 4 1 7-2-1-3-1-4-2 1-2 1-3 0-5v-3c0-3-1-5-2-7 0-4 0-7 1-10z" class="AC"></path><path d="M467 411v4l1 1h0v2l1 9c-1 1-1 2-2 3 0 3 0 5 1 8v8 1c-2 2 0 8-1 12 1 2 1 3 1 6l1 12-1 5h0v-3-6c-1-2 0-3-1-5h0 0c0-3 1-4-1-6v4l-1 1c-1-1-1-2-1-4v-1-1-1-2c1-1 0-2 1-3h-1c0 1 1 3 0 4h-1v-4-1h0l2-2s1-1 0-1v-4h0l-1 1h0-1l1-1h-1c-1 0-1 1-1 2v1c0 2 0 2-1 4l-1-1 1-1v-1h0v-3l1-1c0-1-1-2-1-3l1-1c-1-1-1-1-1-2s1-2 1-3c-1-2-1-1 0-2v-1l-1-1c0-1 1-2 1-3v-2c1-1 0-3 0-5l1-1-2-1 1-1c2 0 2 0 4-1v-9h1z" class="B"></path><path d="M466 435c1-2-1-3 1-5 0 3 0 5 1 8v8 1c-2 2 0 8-1 12-1-3-1-7-1-10 0-4-1-9 0-14z" class="o"></path><path d="M467 411v4l1 1h0v2l1 9c-1 1-1 2-2 3-2 2 0 3-1 5h0l-1-7c0-1-1-1-2-2 1 0 1 0 2-1l-3-1 1-1-2-1 1-1c2 0 2 0 4-1v-9h1z" class="E"></path><path d="M462 421c2 0 2 0 4-1v5c0 1 0 2-1 3 0-1-1-1-2-2 1 0 1 0 2-1l-3-1 1-1-2-1 1-1z" class="M"></path><defs><linearGradient id="AN" x1="436.917" y1="376.83" x2="472.499" y2="348.439" xlink:href="#B"><stop offset="0" stop-color="#d2d4d6"></stop><stop offset="1" stop-color="#f8f3f0"></stop></linearGradient></defs><path fill="url(#AN)" d="M452 322l3 3c1 6 0 12 1 18l3 45-1 20h-1v-3l-1-19c-1-12-2-23-4-34-1-8-3-16-2-23 0-3 1-5 2-7z"></path><path d="M461 314c1 1 1 2 2 4h0c1 2 1 3 1 4 0 2 0 6-1 8h-1c-2 2-2 4-3 7l1 14 1 19v26c-2 3-1 7-1 10 0 7 0 16-1 23-1-6 0-12 0-18v-6c-1-1 0-1-1-2 2-3 1-11 1-15l-3-45c-1-6 0-12-1-18 1 0 1-1 2-1v-3h1c0-2 2-5 3-7z" class="r"></path><path d="M459 337h0l1 14c0-1-1-1-1-2-1-3 0-9 0-12z" class="j"></path><path d="M461 314c1 1 1 2 2 4h0c1 2 1 3 1 4 0 2 0 6-1 8h-1c-2 2-2 4-3 7h0c0-5 0-11-1-16 0-2 2-5 3-7z" class="M"></path><path d="M463 318h0c1 2 1 3 1 4 0 2 0 6-1 8-1-3-2-5-2-8 0-2 0-3 2-4z" class="AO"></path><path d="M456 386l1 19v3h1l1-20c0 4 1 12-1 15 1 1 0 1 1 2v6c0 6-1 12 0 18 1-7 1-16 1-23 0-3-1-7 1-10 0 1 1 2 1 2 1 2 0 5 1 7l-1 1 1 1-1 1h2v1h-1c-1 1 0 2 0 3v1l-1 1 1 1c-1 1 0 1-1 3h1 0v1c-1 1-1 1-1 2l-1 1 2 1-1 1c0 2 1 4 0 5v2c0 1-1 2-1 3l1 1v1c-1 1-1 0 0 2 0 1-1 2-1 3s0 1 1 2l-1 1c0 1 1 2 1 3l-1-1-1 1c0 5 1 13-1 18-1 4 0 9 0 13h0c0 2 0 6-1 7l-1-1v-2c-1-4 0-8 0-11-1-1-1-2-1-3 1-2 1-5 0-7h0v-4-2c0-1 1-2 0-3v-2c-1 0-1 0-2-1h-1l1-2c1 0 2-1 2-1 0-3 1-6 0-8v-3-1c-1-2 0-26 0-31-1-1-1-1-2-1h-1c1-1 1-1 2 0 0-3 1-8 0-10v-2c0-1 0-3 1-4z" class="M"></path><path d="M459 388c0 4 1 12-1 15 1 1 0 1 1 2v6c0 6-1 12 0 18l-1 32h0l-1-56v3h1l1-20z" class="C"></path><path d="M459 429c1-7 1-16 1-23 0-3-1-7 1-10 0 1 1 2 1 2 1 2 0 5 1 7l-1 1 1 1-1 1h2v1h-1c-1 1 0 2 0 3v1l-1 1 1 1c-1 1 0 1-1 3h1 0v1c-1 1-1 1-1 2l-1 1 2 1-1 1c0 2 1 4 0 5v2c0 1-1 2-1 3l1 1v1c-1 1-1 0 0 2 0 1-1 2-1 3s0 1 1 2l-1 1c0 1 1 2 1 3l-1-1-1 1c0 5 1 13-1 18-1 4 0 9 0 13l-2-2c0-4 0-10 1-15h0l1-32z" class="s"></path><path d="M464 322c2 1 2 2 3 3v10 41 35h-1v9c-2 1-2 1-4 1 0-1 0-1 1-2v-1h0-1c1-2 0-2 1-3l-1-1 1-1v-1c0-1-1-2 0-3h1v-1h-2l1-1-1-1 1-1c-1-2 0-5-1-7 0 0-1-1-1-2v-26l-1-19-1-14c1-3 1-5 3-7h1c1-2 1-6 1-8z" class="J"></path><path d="M464 368l1 28v9 1l1 1-1 1c1 1 0 1 1 3v9c-2 1-2 1-4 1 0-1 0-1 1-2v-1h0-1c1-2 0-2 1-3l-1-1 1-1v-1c0-1-1-2 0-3h1v-1h-2l1-1-1-1 1-1c-1-2 0-5-1-7 0 0-1-1-1-2v-26c1 1 1 3 1 4v14c0 2 0 4 1 6l1-1v-4-2c0-2-1-4 0-6v-2-10-1z" class="B"></path><path d="M464 322c2 1 2 2 3 3v10 41 35h-1c-1-2 0-2-1-3l1-1-1-1v-1-9l-1-28 1-27c0-4 0-8-2-11 1-2 1-6 1-8z" class="H"></path><path d="M465 396l1-1v-4-24-18c0-4-1-11 1-14v41 35h-1c-1-2 0-2-1-3l1-1-1-1v-1-9z" class="k"></path><path d="M400 369l42 104h0c-1 4 1 9-1 13h0c-1 2-6 5-7 6-2 3-4 6-5 9-2-3-3-7-5-10-4-10-8-20-11-31-1-3 0-8-1-11-1-2-3-2-4-3-2-1-15-32-16-37h1c1 0 1-2 2-3v-1l1-16 2-7c1-1 1-2 1-3v-1h1v-9h0z" class="C"></path><path d="M426 467v-2c1-2 1-3 3-4v3l-3 3z" class="i"></path><path d="M426 467l-2 1c-1 5 2 14 4 19 1 2 2 3 2 5h-1 0c-2-3-7-21-6-24l3-6c1-2 1-6 0-9h0c-1-3-2-5-3-8v-4c-1-1-1-1 0-2l5 18c1 1 1 3 1 4-2 1-2 2-3 4v2z" class="G"></path><path d="M513 192c2-1 3-2 4-3l3 5c2 1 5 1 7 1 0 1 1 3 2 4 0 0 3 1 4 1h4 1 4l1-1 1-1c1 3 1 4 2 6l1 1v1 1l3 3c-1 1-2 1-3 2h2c2 0 2 0 3 1s6 3 9 4h0l5 3c1 1 2 3 2 4h0v1l-3-1 3 3 1-1c3 2 5 3 7 4l1 1 2-1 2 1h1c1 1 3 2 4 3-3 0-6-4-9-2-2 0-4-1-5-2h-1c-1 0-2 1-3 1l6 3v1l2 1h-4l-1 1c1 0 2 0 3 1l-1 2h-2c-1 0-2 0-3 1h-1l-2-2c-2 1-3 1-5 1s-4 0-6 2l-2 2v2 1h-2v-1-2l-1 1h-3c0-1 0-2 2-2v-1h-3c-1 0-3 1-5 0 1-1 0-1 0-2-2-1-2-1-3-2h-1v1l-2-1v1l-1-1-1 2v1c0 1 1 1 0 2l1 1v3c-1 0-2 0-3 1l6-1h0v1l-3 2c0 1 1 1 1 2v5l-2 2-1 3c0 1 0 1-1 2l1 1-4 14-1 5c-1 2-2 2-2 3l-2 2v-1l-1 1-1 1v1 5 5 3 3h-1c-1 5 0 8-1 12h-1l-2-1c-2 1-3 4-4 6h0c0 1-1 1-1 2-1 0-1-1-2-2l-3-3c-2-1-3-1-5-2-3 2-5 4-7 6v1c-2-1-1-8-3-9v-1l1-1c-1 0 0 0-1-1h-1c1-1 1-1 1-2l-2-2-2 6c0 3-1 5-2 7h-3c-2-1-2-1-4-1l-1 1c-2 4-1 9-1 14l-1 4v27c0 3 1 8-1 10v2h0v29l-1-2v5h0c-1 0-2 0-2 1l-1-1c-1 2 0 4-1 6v-2h0l-1-1v-4-35-41-10c-1-1-1-2-3-3 0-1 0-2-1-4h0c-1-2-1-3-2-4 0-1-1-2-2-2 0-1 0-2-1-3v-4c-3-2-4-3-7-3-1-3-3-4-5-5v-1h-1v-2l-1-1c-1-1-1-1-1-3-1-3 0-7-2-10h0l-1-1c-1-1-1-2-2-2-1-2-2-2-3-2-9-4-18-3-27-3h-6c-2 1-3 0-6 0v-1l-3-1 1-1 1-1-1-2c5-4 10-8 16-11l17-11 86-52z" class="u"></path><path d="M478 227l1-2v-1c3-2 6-4 9-5l1 1-1 1-10 6z" class="c"></path><path d="M547 215c-1-1-2-2-2-3l1-1 1 1h2-1v2c1 1 3 2 4 3 4 2 8 5 12 7h1l3 3c-1-1-2-1-3-2-4-1-7-3-10-5s-6-3-8-5z" class="AM"></path><path d="M502 234l1-1h1c1 2 0 2-1 4 0 1-1 2-2 3v2h-1 0v-1c-1-1-1-2-2-2l-2-2c1-1 2-1 3-1 1-1 2-1 3-2z" class="D"></path><path d="M502 234l1 1c-1 2-3 3-5 4l-2-2c1-1 2-1 3-1 1-1 2-1 3-2z" class="c"></path><path d="M524 224h0c0-3-2-5-2-7 3 4 5 9 8 13 0 1 2 3 2 5h-2c-3-3-3-7-6-11z" class="AQ"></path><path d="M472 230l2 1c3 0 7-3 9-4v1l-4 4-3 3c1-1 1-2 2-3v-1c-2 0-6 2-8 3h0c-1 0-2-1-3-1 1-2 4-3 5-3z" class="AM"></path><path d="M543 199l1-1c1 3 1 4 2 6l1 1v1 1l3 3c-1 1-2 1-3 2l-1-1-1 1c0 1 1 2 2 3l-2-2c-1-1-1-2-1-3v-1-1l1-1c0-2-2-5-3-7l1-1z" class="I"></path><path d="M496 250c4-2 8-3 13-4 1 1 2 2 3 2h4v1h-4c-3 1-5 1-7 2h0l-2-1-1 1c-3 0-5 0-7 3l-2-1 3-3z" class="F"></path><path d="M488 221h2 2v1c-2 2-4 4-7 5 0 0-1 1-2 1v-1c-2 1-6 4-9 4l-2-1 6-3 10-6zm2 13c2-4 6-7 8-10 1-2 2-3 3-4-2 5-5 10-6 15l1 2 2 2c1 0 1 1 2 2v1c-1 0-2 0-3 1v-3h-1l-2-2-2 2v-3c-1-1-1-2-2-3z" class="AR"></path><path d="M492 237c0-1 1-1 2-2v3l-2 2v-3z" class="AJ"></path><path d="M538 211c1 1 2 1 3 1 1 2 2 2 4 3l20 13-1-2c2 0 3 3 5 2h1l-2-1 1-1c3 2 5 3 7 4l1 1 2-1 2 1h1c1 1 3 2 4 3-3 0-6-4-9-2-2 0-4-1-5-2h-1c-1 0-2 1-3 1-10-6-20-13-31-18v-1l1-1z" class="G"></path><path d="M490 234c1 1 1 2 2 3v3l2-2 2 2h1v3h-5-1c-1 0-2 1-2 2-1 0-1 1-2 1h-3l-2-2c1 0 2 0 3-1 1-3 3-6 5-9z" class="AN"></path><path d="M494 238l2 2h0c-1 2-2 2-3 2l-1-1c0 1 0 1-1 1 0-1 0-2 1-2l2-2z" class="AD"></path><path d="M529 217c-1-1-3-2-4-3 0 0-1 0-1-1-1-2-3-3-3-5 0 1 1 2 2 2h0c0-1-1-1-1-2l2 1c1 0 3 1 4 1 1 1 1 2 3 2-1-1-2-1-2-2h3 1l2 1h2 1l-1 1c-2 0-3-1-5-1h0c1 1 2 2 3 4l-1 1h0l-3 1-2-1c2 2 5 4 6 6l-2-1h-1c-1-1-3-2-3-4z" class="D"></path><path d="M529 216c-2-1-3-2-5-4v-1c4 0 7 3 10 5l-3 1-2-1z" class="AU"></path><path d="M488 219l23-14-14 13c-2 1-3 4-5 4h0v-1h-2-2l1-1-1-1z" class="C"></path><path d="M489 220c2 0 4-2 6-3l2 1c-2 1-3 4-5 4h0v-1h-2-2l1-1z" class="AU"></path><path d="M549 212c2 0 2 0 3 1s6 3 9 4h0l5 3c1 1 2 3 2 4h0v1l-3-1h-1c-4-2-8-5-12-7-1-1-3-2-4-3v-2h1z" class="AJ"></path><path d="M513 192c2-1 3-2 4-3l3 5c2 1 5 1 7 1 0 1 1 3 2 4 0 0 3 1 4 1h4 1c-2 1-3 0-4 1l-1 2c2 1 3 2 4 3v1l-2-2c1 2 1 2 0 3l-1 1h-1c-1 1-2 0-3 0-2 0-2-2-4-3s-7-5-7-6c-1-1-1-2-2-2l-1-1v-1c-1-3-1-3-3-4zm11 32c3 4 3 8 6 11l2 5v1c0 1 1 1 0 2l1 1v3c-1 0-2 0-3 1h0c-2 0-3 0-4-1h-1c-1 0-2 0-2-1h-1c-2-1-2-1-3-2 0 1-1 1-2 1v-3c0-2 2-8 3-9l2-2h3v-2c-1-2-1-3-1-5z" class="G"></path><path d="M520 233l2-2v2 1c1 1 1 0 2 1h1v1c1 1 3 2 4 2v1c-1 1-3 1-5 1h-4l1-6-1-1z" class="D"></path><path d="M520 233l1 1-1 6h4c3 1 3 1 5 3-3 0-7-1-10 1h0c0 1-1 1-2 1v-3c0-2 2-8 3-9z" class="AR"></path><path d="M532 241c0 1 1 1 0 2l1 1v3c-1 0-2 0-3 1h0c-2 0-3 0-4-1h-1c-1 0-2 0-2-1h-1c-2-1-2-1-3-2h0c3-2 7-1 10-1 1-1 2-1 3-2z" class="AS"></path><path d="M523 246l1-1c1 0 1 0 3-1l1 1h3l1 1c0-1 0-1 1-1v-1 3c-1 0-2 0-3 1h0c-2 0-3 0-4-1h-1c-1 0-2 0-2-1z" class="G"></path><path d="M529 217c0 2 2 3 3 4h1l2 1 4 5h1c2 1 4 3 5 5h1l2 2-2 1 1 1 2 2c0 1-1 1-1 2l1 1-1 1h-3c-1 0-3 1-5 0 1-1 0-1 0-2-2-1-2-1-3-2h-1v1l-2-1v1l-1-1-1 2-2-5h2c0-2-2-4-2-5 0 1 1 2 2 2 0-1 0-2-1-3l-3-8h0l-2-2h1l2-2z" class="c"></path><path d="M539 227h1c2 1 4 3 5 5h1l2 2-2 1c-1-1-3-2-3-3s0-2-1-2l-3-3z" class="D"></path><path d="M528 221c3 2 4 6 5 9h1c2 1 3 3 3 5 1 1 1 2 1 3 1 0 4 1 5 0h2 2v-2l2 2c0 1-1 1-1 2l1 1-1 1h-3c-1 0-3 1-5 0 1-1 0-1 0-2-2-1-2-1-3-2h-1v1l-2-1v1l-1-1-1 2-2-5h2c0-2-2-4-2-5 0 1 1 2 2 2 0-1 0-2-1-3-1-3-2-5-3-8z" class="AJ"></path><path d="M540 240c2-1 3-1 5-1v1 2c-1 0-3 1-5 0 1-1 0-1 0-2z" class="AD"></path><path d="M532 235l3 3v-1c-1-1-1-2-1-3l3 3v1h-1v1l-2-1v1l-1-1-1 2-2-5h2z" class="AV"></path><path d="M492 222h0v1c-1 1-1 3-2 4l1 1c-1 2-2 4-4 5v1c-1 2-2 3-3 4l-3 3c2 2 2 2 4 2-1 1-2 1-3 1l2 2h-3l-1 1 4 1h0v1c-1 1-2 2-2 3 0 0 0 1-1 1-1 2-1 4-3 5l1-4-3 2c-1 3-1 5-2 8l-2-2-1 2v5l-1 2-1-2h0l-1-1v-1c0-1 0-1-1-2h0c0-2 0-4-1-6v2h0c-1-3-1-6 0-8v-1c1 0 1-1 2-2v-1h0l-1-1h0l-1-1 1-1v-1c-1-1-2-1-2-2l-1-1 6-3c0-1 1-1 2-2 2-1 3-1 4-2l3-3 4-4c1 0 2-1 2-1 3-1 5-3 7-5z" class="G"></path><path d="M482 244s-1 1-2 1h0c-1-1-1-2-2-2 1-2 2-4 3-5s2 0 3 0l-3 3c2 2 2 2 4 2-1 1-2 1-3 1z" class="D"></path><path d="M464 242l6-3 2 2-1 1c-1 0-2 1-3 1l1 1h0c1 0 2 1 3 1h-1-2l-2 3h0l-1-1 1-1v-1c-1-1-2-1-2-2l-1-1z" class="AO"></path><path d="M492 222h0v1c-3 4-6 8-10 10l-3-1 4-4c1 0 2-1 2-1 3-1 5-3 7-5z" class="AS"></path><path d="M476 235l3-3 3 1-2 2-3 4c-1 1-1 1-1 2h-1v-1-1c-1 1-2 2-3 2l-2-2c0-1 1-1 2-2 2-1 3-1 4-2z" class="AT"></path><path d="M476 235l3-3 3 1-2 2c-1 0-2 1-3 1h-1v-1z" class="AD"></path><path d="M472 245l1-1 1 1 1-1v2h1l1 1h0l1-1 1 1v1l1 1h1v1c-1 1-1 3-2 4l-3 2c-1 3-1 5-2 8l-2-2-1 2v5l-1 2-1-2h0l-1-1v-1c0-1 0-1-1-2h0c0-2 0-4-1-6v2h0c-1-3-1-6 0-8v-1c1 0 1-1 2-2v-1h0l-1-1 2-3h2 1z" class="w"></path><path d="M472 245l1-1 1 1 1-1v2h1l1 1h0l-1 2h0c-1 5-4 9-4 13l-1 2c0-2 1-8 1-9 1-1 0-1 1-1 0-1 1-2 1-2v-3c-3-1-4 0-6 1v-1h0l-1-1 2-3h2 1z" class="I"></path><path d="M477 247l1-1 1 1v1l1 1h1v1c-1 1-1 3-2 4l-3 2c-1 3-1 5-2 8l-2-2c0-4 3-8 4-13h0l1-2z" class="w"></path><path d="M480 249h1v1c-1 1-1 3-2 4l-3 2 1-1v-4h0c1-1 1-2 3-2z" class="E"></path><path d="M534 216h0l1-1c-1-2-2-3-3-4h0c2 0 3 1 5 1v1c11 5 21 12 31 18l6 3v1l2 1h-4l-1 1c1 0 2 0 3 1l-1 2h-2c-1 0-2 0-3 1h-1l-2-2c-2 1-3 1-5 1s-4 0-6 2l-2 2v2 1h-2v-1-2l-1 1h-3c0-1 0-2 2-2v-1l1-1-1-1c0-1 1-1 1-2l-2-2-1-1 2-1-2-2h-1c-1-2-3-4-5-5h-1l-4-5c-1-2-4-4-6-6l2 1 3-1z" class="AR"></path><path d="M546 232h1l1-2h2v2l-2 2-2-2z" class="AJ"></path><g class="AM"><path d="M540 225l4 1v1h2 0v1c0 1 0 2-1 2v2c-1-2-3-4-5-5v-2z"></path><path d="M540 221c1 0 3 1 4 2 0 1 1 1 1 2h1c-1 1 0 1-2 1h0l-4-1v-1l1-2-1-1z"></path></g><path d="M541 222c1 1 2 3 3 4h0l-4-1v-1l1-2z" class="I"></path><path d="M529 216l2 1c2 1 4 1 5 3 1 0 2 1 4 1h0l1 1-1 2v1 2h-1l-4-5c-1-2-4-4-6-6z" class="AN"></path><path d="M550 230h1c0-2-2-1-3-3 1 0 1-1 2-1 1 1 4 3 5 4 3 2 7 4 10 6l-2 1c-1 1-1 2-2 2-2 0-3-1-5 0h0l-1-1-1-1v-2l-1-1h-1l-2-2v-2z" class="AO"></path><path d="M567 233c-9-5-17-10-26-15-1-1-5-3-5-5h1c11 5 21 12 31 18l6 3v1c-2 0-5-1-7-2z" class="AQ"></path><path d="M550 232l2 2h1l1 1v2l1 1 1 1h0c2-1 3 0 5 0 1 0 1-1 2-2l2-1h3 1c-1-2-1-1-2-2v-1c2 1 5 2 7 2l2 1h-4l-1 1c1 0 2 0 3 1l-1 2h-2c-1 0-2 0-3 1h-1l-2-2c-2 1-3 1-5 1s-4 0-6 2l-2 2v2 1h-2v-1-2l-1 1h-3c0-1 0-2 2-2v-1l1-1-1-1c0-1 1-1 1-2l-2-2-1-1 2-1 2-2z" class="AM"></path><path d="M565 236h3l1 2v1c-2 0-3-1-5-1l-1-1 2-1z" class="AJ"></path><path d="M550 232l2 2h1l-1 1v1 3 1 1h-3l-1-1c0-1 1-1 1-2l-2-2-1-1 2-1 2-2z" class="AT"></path><path d="M455 249h1c-1 0-2 0-2-1l2-1c1 0 2 0 2-1 1-1 2 0 3-1 1 0 2-2 3-3l1 1c0 1 1 1 2 2v1l-1 1 1 1h0l1 1h0v1c-1 1-1 2-2 2v1c-1 2-1 5 0 8h0l-1 2h0c-1 1-1 3-1 5v1h0c1 4 1 8 0 11l-1 1c-1 4 2 10 0 14h-1c-2-1-2-1-4-3-1-1-1-7-1-9v-3-4c-1-1-2-1-2-3 0-1-1-2-1-4l-1 1c0 1 1 1 1 3h-1l-1-1v-1c0-1-1-2-2-3-1 0-2-1-3-2l-1-2c-1-1-3-1-4-1h-5l-1-1c2 0 3-1 4-1h0l-1-1 1-1c1 0 1-1 2-1h1c-5-2-11-2-16-2l1-1c3-1 6-1 9-1 2-1 6-3 8-3 1 0 3-1 5-1l-1 1 5 3v-1c-1-1-2-2-2-3v-1h3z" class="l"></path><path d="M437 254c2-1 6-3 8-3l2 1c0 1 1 1 2 2l-1 1 2 1-1 1-2-1c-1-1-1-1-2-1v1l-1-1h-2-2c-1 0-2-1-3-1z" class="i"></path><path d="M445 251c1 0 3-1 5-1l-1 1 5 3v1c1 1 1 2 1 2 0 1 1 2 1 3l1 2v-1-3h1v1l2 2c0 4 1 8 1 12v-1c0-1 0-2-1-2 0-2-2-2-2-4 0-1 0-1-1-2v3c-1-1-1-2-1-4s-2-5-3-7c-1-3-4-3-6-4l-2-1z" class="e"></path><path d="M427 256l1-1c3-1 6-1 9-1 1 0 2 1 3 1h2 2l1 1h0c2 1 4 2 5 4 1 0 1 1 2 1v1c-1 1-1-1-3-2s-4-2-6-2c-5-2-11-2-16-2z" class="G"></path><path d="M443 258c2 0 4 1 6 2s2 3 3 2v-1h1l3 2c0 2 0 3 1 4v2h0c0 3 1 5 0 7-1-1-2-1-2-3 0-1-1-2-1-4l-1 1c0 1 1 1 1 3h-1l-1-1v-1c0-1-1-2-2-3-1 0-2-1-3-2l-1-2c-1-1-3-1-4-1h-5l-1-1c2 0 3-1 4-1h0l-1-1 1-1c1 0 1-1 2-1h1z" class="z"></path><path d="M440 261c1 0 4 1 6 1l1 1-1 1c-1-1-3-1-4-1h-5l-1-1c2 0 3-1 4-1z" class="L"></path><path d="M452 261h1l3 2c0 2 0 3 1 4v2h0c0 3 1 5 0 7-1-1-2-1-2-3 0-1-1-2-1-4l-1 1c0 1 1 1 1 3h-1l-1-1v-1c0-1-1-2-2-3v-2c0-2 0-2-1-3v-1c2 1 3 3 4 4h0 0c1 0 1-1 0-1v-1-1l-1-1v-1z" class="V"></path><path d="M460 261h1c1 2 0 4 2 5v2 2l1-1c1 4 1 8 0 11l-1 1c-1 4 2 10 0 14h-1c-2-1-2-1-4-3-1-1-1-7-1-9v-3-4c1-2 0-4 0-7h0v-2-3c1 1 1 1 1 2 0 2 2 2 2 4 1 0 1 1 1 2v1c0-4-1-8-1-12z" class="U"></path><path d="M457 283c1 1 2 1 3 2l-1 2h2c1 1 0 2 0 4h-2l-1 1c-1-1-1-7-1-9z" class="AB"></path><path d="M457 267v-3c1 1 1 1 1 2 0 2 2 2 2 4 1 0 1 1 1 2v1c0 4 0 9-1 12-1-1-2-1-3-2v-3-4c1-2 0-4 0-7h0v-2z" class="Q"></path><path d="M457 269c2 3 2 4 2 7v2l-2 2v-4c1-2 0-4 0-7z" class="b"></path><path d="M455 249h1c-1 0-2 0-2-1l2-1c1 0 2 0 2-1 1-1 2 0 3-1 1 0 2-2 3-3l1 1c0 1 1 1 2 2v1l-1 1 1 1h0l1 1h0v1c-1 1-1 2-2 2v1c-1 2-1 5 0 8h0l-1 2h0c-1 1-1 3-1 5v1h0l-1 1v-2-2c-2-1-1-3-2-5h-1l-2-2v-1h-1v3 1l-1-2c0-1-1-2-1-3 0 0 0-1-1-2v-1-1c-1-1-2-2-2-3v-1h3z" class="w"></path><path d="M455 249h1c-1 0-2 0-2-1l2-1c1 0 2 0 2-1 1-1 2 0 3-1 1 0 2-2 3-3l1 1c0 1 1 1 2 2v1l-1 1 1 1h0l1 1h0v1c-1 1-1 2-2 2v1c-1 2-1 5 0 8h0l-1 2h0v-3c-1-2-1-3-1-5l1-3-2-1c-3 0-5 1-8-1v-1z" class="AT"></path><path d="M427 256c5 0 11 0 16 2h-1c-1 0-1 1-2 1l-1 1 1 1h0c-1 0-2 1-4 1l1 1h5c1 0 3 0 4 1l1 2c1 1 2 2 3 2 1 1 2 2 2 3v1c-2 2-2 7-3 10-1 2-2 5-3 7l-1 2v1h4 2v1l-1 2-3-2v1h-2l-1-1c-1-1-1-1-1-3-1-3 0-7-2-10h0l-1-1c-1-1-1-2-2-2-1-2-2-2-3-2-9-4-18-3-27-3h-6c-2 1-3 0-6 0v-1l-3-1 1-1 1-1c10-6 20-10 32-12z" class="L"></path><path d="M396 271h1c3 0 5-1 8-1h2 3l1 1c-1 0-2 0-3 1h-6c-2 1-3 0-6 0v-1z" class="C"></path><path d="M411 268l12-2c3 2 7 0 10 1 2 0 3 0 5 1h0c1 0 5 3 6 5v1h0c1 1 1 1 1 2h0v2l1 1v1 1c0 1 1 4 0 5v3l-1 2v1h4 2v1l-1 2-3-2v1h-2l-1-1c-1-1-1-1-1-3 1-4 1-7-1-11-4-9-15-10-24-10h-6l-1-1z" class="t"></path><path d="M442 263c1 0 3 0 4 1l1 2c1 1 2 2 3 2 1 1 2 2 2 3v1c-2 2-2 7-3 10-1 2-2 5-3 7v-3c1-1 0-4 0-5v-1-1l-1-1v-2h0c0-1 0-1-1-2h0v-1c-1-2-5-5-6-5h0c-2-1-3-1-5-1-3-1-7 1-10-1 5-2 11-2 16-3h3z" class="Y"></path><path d="M446 264l1 2c-1 0-3 0-4-1l1-1h2z" class="V"></path><defs><linearGradient id="AO" x1="418.415" y1="257.82" x2="420.414" y2="264.452" xlink:href="#B"><stop offset="0" stop-color="#3e3c3d"></stop><stop offset="1" stop-color="#606161"></stop></linearGradient></defs><path fill="url(#AO)" d="M427 256c5 0 11 0 16 2h-1c-1 0-1 1-2 1l-1 1 1 1h0c-1 0-2 1-4 1l1 1h5-3c-5 1-11 1-16 3l-12 2h-2l-1-1c2 0 3 0 5-1h0l1-1 14-3c3-1 7-1 9-2-9 0-19 2-28 5-3 1-6 2-8 3-2 0-6 2-7 1l1-1c10-6 20-10 32-12z"></path><path d="M413 266c8-2 16-4 23-4l1 1h5-3c-5 1-11 1-16 3l-12 2h-2l-1-1c2 0 3 0 5-1h0z" class="G"></path><path d="M466 261v-2c1 2 1 4 1 6h0c1 1 1 1 1 2v1l1 1h0l1 2c1 2 1 4 1 7v18h-1v4h-1c0 2-1 3 0 5 1 1 1 2 1 3l1 1c0 4-2 7 0 12v1l-1 1v6h-1c0 11-1 23 0 34 0 2-1 4-1 6v11c-1-1-1-2-1-4v-41-10c-1-1-1-2-3-3 0-1 0-2-1-4h0c-1-2-1-3-2-4 0-1-1-2-2-2 0-1 0-2-1-3v-4c-3-2-4-3-7-3-1-3-3-4-5-5v-1h-1v-2h2v-1l3 2 1-2v-1h-2-4v-1l1-2c1-2 2-5 3-7 1-3 1-8 3-10l1 1h1c0-2-1-2-1-3l1-1c0 2 1 3 1 4 0 2 1 2 2 3v4 3c0 2 0 8 1 9 2 2 2 2 4 3h1c2-4-1-10 0-14l1-1c1-3 1-7 0-11h0v-1c0-2 0-4 1-5h0l1-2z" class="v"></path><path d="M469 305c1 1 1 2 1 3l1 1c0 4-2 7 0 12v1l-1 1v6h-1c-1-2 0-4-1-6 0-6 1-12 1-18z" class="U"></path><path d="M467 302v10 13c-1-1-1-2-3-3 0-1 0-2-1-4 1-1 0-1 0-3 1-1 1 0 1-2-1 0-1 0-2-1l2-1v-1-2c0-1 0-1 1-1v-3h0c-1-1 0-1 0-2h2z" class="E"></path><path d="M466 261v-2c1 2 1 4 1 6h0c1 1 1 1 1 2v1l1 1h0l1 2c1 2 1 4 1 7v18h-1v4h-1v-25c0-1 0-2-1-3v4c-1-1-2-1-2-3v-3-1-1c0-2-1-3-1-5h0l1-2z" class="U"></path><path d="M464 269h0v-1c0-2 0-4 1-5 0 2 1 3 1 5v1 1 3c0 2 1 2 2 3 0 3 0 9-1 12v14h-2c0 1-1 1 0 2h0v3c-1 0-1 0-1 1v2l-2 2h-1c0-2 0-2-1-3 1-2 1-6 1-8h-2 0c1-2 1-3 3-4h2c0-1-1-1-2-2h1c2-4-1-10 0-14l1-1c1-3 1-7 0-11z" class="F"></path><path d="M453 273h1c0-2-1-2-1-3l1-1c0 2 1 3 1 4 0 2 1 2 2 3v4 3c0 2 0 8 1 9 2 2 2 2 4 3 1 1 2 1 2 2h-2c-2 1-2 2-3 4h0 2c0 2 0 6-1 8 1 1 1 1 1 3h1l2-2v1l-2 1c1 1 1 1 2 1 0 2 0 1-1 2 0 2 1 2 0 3h0c-1-2-1-3-2-4 0-1-1-2-2-2 0-1 0-2-1-3v-4c-3-2-4-3-7-3-1-3-3-4-5-5v-1h-1v-2h2v-1l3 2 1-2v-1h-2-4v-1l1-2c1-2 2-5 3-7 1-3 1-8 3-10l1 1z" class="X"></path><path d="M445 294h2v-1l3 2 4 2c0 2 0 2 2 4h0c1 1 1 2 2 4-3-2-4-3-7-3-1-3-3-4-5-5v-1h-1v-2z" class="L"></path><path d="M453 273h1c0-2-1-2-1-3l1-1c0 2 1 3 1 4 0 2 1 2 2 3v4c-2 1-3 7-4 10 1 2 1 4 3 6v1h-2l-4-2 1-2v-1h-2-4v-1l1-2c1-2 2-5 3-7 1-3 1-8 3-10l1 1z" class="g"></path><path d="M446 289c1-2 2-5 3-7 1-3 1-8 3-10l1 1c0 6 0 10-2 15v1c-1 1-1 2-2 3h-4v-1l1-2z" class="X"></path><path d="M487 246c1 0 1-1 2-1 0-1 1-2 2-2h1v2l1 1c1 1 1 3 2 3l1 1-3 3-3 7 1 2h-1c0-1-1-2-1-2h-1v10 2c1 3 1 7 2 10l2 7 3 6c1 2 2 3 2 6-2 3-3 6-5 9v3l-1 1h0v1c-1 0 0 0-1-1h-1c1-1 1-1 1-2l-2-2-2 6c0 3-1 5-2 7h-3c-2-1-2-1-4-1l-1 1c-2 4-1 9-1 14l-1 4v27c0 3 1 8-1 10v2h0v29l-1-2v5h0c-1 0-2 0-2 1l-1-1c-1 2 0 4-1 6v-2h0l-1-1v-4-35c0 2 0 3 1 4v-11c0-2 1-4 1-6-1-11 0-23 0-34h1v-6l1-1v-1c-2-5 0-8 0-12l-1-1c0-1 0-2-1-3-1-2 0-3 0-5h1v-4h1v-18c0-3 0-5-1-7l1-2v-5l1-2 2 2c1-3 1-5 2-8l3-2-1 4c2-1 2-3 3-5 1 0 1-1 1-1 0-1 1-2 2-3v-1h0l-4-1 1-1h3 3z" class="q"></path><path d="M478 306h0c1 0 2 0 3-1v3 2c-1 0-2 0-3 1 0 1-1 2-1 3l-1 2c0-4 0-7 2-10z" class="D"></path><path d="M476 316l1-2c0-1 1-2 1-3 1-1 2-1 3-1-1 4-1 6-1 10-1 1-2 1-3 2l-1 1v-7z" class="u"></path><path d="M472 347v6 17c0 2 0 5 1 7v-1c-1-8 0-16 0-25v-12c0-2 0-4 1-6h0v2c1 2 1 4 0 6v27c0 3 1 8-1 10v2h0v29l-1-2v-1-6-13c0-1-1-2-1-3v-17c0-5-1-11 0-16 1-1 1-3 1-4z" class="E"></path><path d="M484 303v1l2-1h0l1 2h0l1 4v1l-2 6c0 3-1 5-2 7h-3c-2-1-2-1-4-1 1-1 2-1 3-2 0-4 0-6 1-10v-2l1 1h0l1-1v-4c0-1 0-1 1-1z" class="L"></path><path d="M482 313c1-1 1-1 3 0l1 3h-2c-1 1-2 1-3 0l1-3z" class="u"></path><path d="M484 303v1l2-1h0l1 2h0l1 4v1l-2 6h0l-1-3c-2-1-2-1-3 0v-1c1-1 1-3 1-4v-4c0-1 0-1 1-1z" class="D"></path><path d="M486 303l1 2h0l1 4v1l-2 6h0l-1-3c0-2 1-3 1-5-1-1 0-3 0-5z" class="I"></path><path d="M476 256l3-2-1 4v4l-1 1h1 0v2 2l-1 1h0l-1 3h0c0 3 1 8-1 10 0 1 0 1 1 2 0 1 0 2-1 4-1 1 0 3 0 5-1 7 0 13-1 20v1h-1v-18c-1 2-1 5-1 7 0 0 0 1 1 1h0c-2 5-2 10-2 15 0 1 1 2 1 4v25c0 1 0 3-1 4v-29-1c-2-5 0-8 0-12l-1-1c0-1 0-2-1-3-1-2 0-3 0-5h1v-4h1v-18c0-3 0-5-1-7l1-2v-5l1-2 2 2c1-3 1-5 2-8z" class="S"></path><path d="M469 300h1v-4h1v13l-1-1c0-1 0-2-1-3-1-2 0-3 0-5z" class="w"></path><path d="M471 264l1-2 2 2c0 3-1 6-1 9v8c-1 0-1-1-1-2s0-1-1-1c0-3 0-5-1-7l1-2v-5z" class="F"></path><path d="M470 329v-6l1-1v29c-1 5 0 11 0 16v17c0 1 1 2 1 3v13 6 1 5h0c-1 0-2 0-2 1l-1-1c-1 2 0 4-1 6v-2h0l-1-1v-4-35c0 2 0 3 1 4v-11c0-2 1-4 1-6-1-11 0-23 0-34h1z" class="H"></path><path d="M469 389c2 3 1 5 1 8 1 2 0 3 1 5 0 1 0 3 1 4v1 5h0c-1 0-2 0-2 1l-1-1v-23z" class="AI"></path><path d="M470 329v-6l1-1v29c-1 5 0 11 0 16v17c0 1 1 2 1 3v13 6c-1-1-1-3-1-4-1-2 0-3-1-5 0-3 1-5-1-8 1-4 0-8 0-12l1-48z" class="F"></path><path d="M483 277v3c2 0 2 0 4 1v-1l1 2h2l2 7 3 6c1 2 2 3 2 6-2 3-3 6-5 9v3l-1 1h0v1c-1 0 0 0-1-1h-1c1-1 1-1 1-2l-2-2v-1l-1-4h0l-1-2h0l-2 1v-1c-1 0-1 0-1 1v4l-1 1h0l-1-1v-3c-1 1-2 1-3 1h0l1-6-2-2h0v-2c0-1-1-3 0-4 2-1 2-2 3-3 0 0 2-6 2-7v-1c0-1 1-2 1-4z" class="AI"></path><path d="M482 281c1 2 1 1 1 2 0 3 0 7-1 10-1-1-1-3-2-4 0 0 2-6 2-7v-1z" class="AU"></path><path d="M487 280l1 2v6l-1 1c0-2-1-2-2-3 0-1 0-2-1-3h0l-1-3c2 0 2 0 4 1v-1z" class="O"></path><path d="M480 289c1 1 1 3 2 4h0c1 2 1 4 0 6h0v2c2-2 2-6 3-8 1-1 1-2 2-3v5 1c-1 1-2 3-3 5h0v2c-1 0-1 0-1 1v4l-1 1h0l-1-1v-3c-1 1-2 1-3 1h0l1-6-2-2h0v-2c0-1-1-3 0-4 2-1 2-2 3-3z" class="AR"></path><path d="M480 289c1 1 1 3 2 4h0v1c-1 1-1 2-1 3-2 0-2-1-4-1 0-1-1-3 0-4 2-1 2-2 3-3z" class="I"></path><path d="M486 303l1-2c1 0 0 0 1-1l-1-1c-1-1 1-4 2-6v-1l1 2c2-1 0-2 1-3 0-1 0-1 1-2l3 6c1 2 2 3 2 6-2 3-3 6-5 9v3l-1 1h0v1c-1 0 0 0-1-1h-1c1-1 1-1 1-2l-2-2v-1l-1-4h0l-1-2h0z" class="AD"></path><path d="M486 303l1-2c1 0 0 0 1-1l-1-1c-1-1 1-4 2-6v-1l1 2c2-1 0-2 1-3 0-1 0-1 1-2l3 6c-1 1-2 2-3 2 0 0-1-1-1-2-1 2-3 4-2 6v1c0 2 0 4 1 6-1 1-1 1-2 1l-1-4h0l-1-2h0z" class="AL"></path><path d="M487 246c1 0 1-1 2-1 0-1 1-2 2-2h1v2l1 1c1 1 1 3 2 3l1 1-3 3-3 7 1 2h-1c0-1-1-2-1-2h-1v10 2c1 3 1 7 2 10h-2l-1-2v1c-2-1-2-1-4-1v-3c0 2-1 3-1 4v1c0 1-2 7-2 7-1 1-1 2-3 3-1-4 0-8 1-13l-1-11 1-1v-2-2h0-1l1-1v-4c2-1 2-3 3-5 1 0 1-1 1-1 0-1 1-2 2-3v-1h0l-4-1 1-1h3 3z" class="w"></path><path d="M487 246c1 0 1-1 2-1 0-1 1-2 2-2h1v2l1 1c1 1 1 3 2 3l1 1-3 3-3 7c0-3 2-9 4-13l-7-1z" class="D"></path><path d="M483 260v1c1-1 1-1 0-2v-3h1v4c1 1 1 4 1 6 1 1 0 1 1 2s1 3 2 4c1 3 1 7 2 10h-2l-1-2-1-7c0-3-1-5-2-7l-1-2v-4z" class="E"></path><path d="M478 263h1c1-1 2-2 4-3v4l1 2c1 2 2 4 2 7l1 7v1c-2-1-2-1-4-1v-3c0 2-1 3-1 4v1c0 1-2 7-2 7-1 1-1 2-3 3-1-4 0-8 1-13l-1-11 1-1v-2-2z" class="b"></path><path d="M479 270h0c1-2 1-3 1-4 1 1 1 2 0 3v2c0 1 1 2 2 3-1 2-1 4 0 6l-1 2h-1-1c-1-2-1-4-1-5 1-3 1-4 1-7z" class="J"></path><path d="M478 263h1c1-1 2-2 4-3v4c-1 3-2 6-1 10-1-1-2-2-2-3v-2c1-1 1-2 0-3 0 1 0 2-1 4h0c-1-1-1-2-1-3v-2-2z" class="N"></path><path d="M483 264l1 2c1 2 2 4 2 7l1 7v1c-2-1-2-1-4-1v-3c0 2-1 3-1 4v1h-1l1-2c-1-2-1-4 0-6-1-4 0-7 1-10z" class="Q"></path><path d="M486 273l1 7v1c-2-1-2-1-4-1v-3-1 1c1 0 1 0 2 1l1-1v-4z" class="P"></path><path d="M483 264l1 2v6c0 2 0 3-1 4v1c0 2-1 3-1 4v1h-1l1-2c-1-2-1-4 0-6-1-4 0-7 1-10z" class="W"></path><path d="M519 244c1 1 1 1 3 2h1c0 1 1 1 2 1h1c1 1 2 1 4 1h0l6-1h0v1l-3 2c0 1 1 1 1 2v5l-2 2-1 3c0 1 0 1-1 2l1 1-4 14-1 5c-1 2-2 2-2 3l-2 2v-1l-1 1-1 1v1 5 5 3 3h-1c-1 5 0 8-1 12h-1l-2-1c-2 1-3 4-4 6h0c0 1-1 1-1 2-1 0-1-1-2-2l-3-3c-2-1-3-1-5-2-3 2-5 4-7 6v1c-2-1-1-8-3-9v-1l1-1v-1h0l1-1v-3c2-3 3-6 5-9 0-3-1-4-2-6l-3-6-2-7c-1-3-1-7-2-10v-2-10h1s1 1 1 2h1l-1-2 3-7 2 1c2-3 4-3 7-3l1-1 2 1h0c2-1 4-1 7-2h4v-1h-4c-1 0-2-1-3-2 3 0 5 0 8-1 1 0 2 0 2-1z" class="AG"></path><path d="M513 278h3c1 2 1 6 1 9v3h0c-1-1 0-2 0-3-1 0-1-1-2-1-1-3 0-4 0-6l-2-2z" class="AK"></path><path d="M505 251h0c2-1 4-1 7-2v1c-6 2-8 4-12 7h-1l-2 2v-3c3-2 5-4 8-5z" class="l"></path><path d="M522 255c1 1 2 2 3 4-1 3-1 3-2 5h-4c-1 1-5 1-6 2-1 0-3 2-4 3h-1c0 1-1 2-1 2 0-1 0-2 1-3 1-2 3-4 5-5h6 1 2 1c0-1 1-2 1-2l-1-1v-1c-1-1-1-2-1-4h0z" class="AK"></path><path d="M512 249h4c5 1 6 4 9 6 3 4 2 6 2 9l-1 3-1-1-1 1c1-3 1-5 1-8-1-2-2-3-3-4l-1-1c-3-3-6-4-9-4v-1zm-15 10l2-2h1c-2 5-3 10-1 15 2 2 5 4 5 8h-1l-1 1c0-1 0-3-1-3h-2v1l-1 1-2-2v-3-1c-1 0-2-1-3-2h1l2 1h1v-4c-1-3-1-7 0-10z" class="C"></path><path d="M496 275c1 1 3 2 3 4l-1 1-2-2v-3z" class="U"></path><path d="M525 259c0 3 0 5-1 8l-1 1c-1 2-2 4-4 5s-5 3-7 5h-1-2c-1 0-2 0-2-1 0-2 1-4 2-6v-2h0c1-1 3-3 4-3 1-1 5-1 6-2h4c1-2 1-2 2-5z" class="B"></path><path d="M509 276c0-1 1-2 1-2 1-2 2-4 3-7l6-1c-1 2-2 2-4 3s-3 4-4 5l-2 2z" class="s"></path><path d="M519 266h2l1 1 1 1c-1 2-2 4-4 5s-5 3-7 5h-1-2v-2l2-2c1-1 2-4 4-5s3-1 4-3z" class="f"></path><path d="M511 278v-2c1-1 2-2 3-4h0c1-2 1-2 3-2 1 1 1 2 2 3-2 1-5 3-7 5h-1z" class="g"></path><path d="M527 264c1 2 1 4 1 6l-1 2v1c-1 1-1 2-2 4v1 3h-1c0 1 0 0-1 1l2 2h1c-1 2-2 2-2 3l-2 2v-1l-1 1-1 1-1-5c0 1-2 2-2 2 0-3 0-7-1-9h-3-1c2-2 5-4 7-5s3-3 4-5l1-1 1-1 1 1 1-3z" class="i"></path><path d="M519 278s-1 1 0 2h3c1 1 1 1 1 2l2 2h1c-1 2-2 2-2 3l-2 2v-1l-1 1-1 1-1-5-1-7h1z" class="O"></path><path d="M527 264c1 2 1 4 1 6l-1 2v1c-1 1-1 2-2 4v1 3h-1c0 1 0 0-1 1 0-1 0-1-1-2h-3c-1-1 0-2 0-2v-1c3-1 6-7 7-10l1-3z" class="E"></path><path d="M502 251l1-1 2 1c-3 1-5 3-8 5v3c-1 3-1 7 0 10v4h-1l-2-1h-1c1 1 2 2 3 2v1 3c-1 1-1 2-2 2s-2 0-2 1c-2-4-3-8-4-11v-10h1s1 1 1 2h1l-1-2 3-7 2 1c2-3 4-3 7-3z" class="q"></path><path d="M502 251l1-1 2 1c-3 1-5 3-8 5-1 1-2 2-2 3 0 3-1 4 0 6v4l-1 1h-2c-1-2-1-5-1-7v-1l-1-2 3-7 2 1c2-3 4-3 7-3z" class="Z"></path><path d="M493 253l2 1c2-3 4-3 7-3l-5 2c0 1 0 2-1 3s-2 2-3 4l-2 3v-1l-1-2 3-7zm26-9c1 1 1 1 3 2h1c0 1 1 1 2 1h1c1 1 2 1 4 1h0l6-1h0v1l-3 2c0 1 1 1 1 2v5l-2 2-1 3c0 1 0 1-1 2l1 1-4 14-1 5h-1l-2-2c1-1 1 0 1-1h1v-3-1c1-2 1-3 2-4v-1l1-2c0-2 0-4-1-6 0-3 1-5-2-9-3-2-4-5-9-6v-1h-4c-1 0-2-1-3-2 3 0 5 0 8-1 1 0 2 0 2-1z" class="U"></path><path d="M532 259v-10c1-1 2-1 4-2v1l-3 2c0 1 1 1 1 2v5l-2 2z" class="S"></path><path d="M519 244c1 1 1 1 3 2h1c0 1 1 1 2 1h1c1 1 2 1 4 1h-2v1l-1 1h-1c0-1 0-1-2-2l-2 1c0 2 2 2 3 4v2c-3-2-4-5-9-6v-1h-4c-1 0-2-1-3-2 3 0 5 0 8-1 1 0 2 0 2-1z" class="q"></path><path d="M488 270c1 3 2 7 4 11 0-1 1-1 2-1s1-1 2-2l2 2 1-1v-1h2c1 0 1 2 1 3l1-1h1c0 1 0 2-1 3v3l1 6 4 9v1l-1-1-1-1h-1v1c0 1-1 1-1 2v2l-1-1c0-1 0-1-1-2-1 0-2-1-3-2l-1 1v-1l-1 1c0-3-1-4-2-6l-3-6-2-7c-1-3-1-7-2-10v-2z" class="AV"></path><path d="M499 279v-1l1 5v3h-2c-1 1 0 1 0 2l-1 1v-5s1-1 1-2v-1-1l1-1z" class="p"></path><path d="M499 278h2c1 0 1 2 1 3l1-1h1c0 1 0 2-1 3v3l-3 1v-4l-1-5z" class="I"></path><path d="M496 278l2 2v1 1c0 1-1 2-1 2v5h-2l-3-8c0-1 1-1 2-1s1-1 2-2z" class="AI"></path><path d="M496 278l2 2v1c-1 1-2 1-3 1l-1-2c1 0 1-1 2-2z" class="w"></path><path d="M488 270c1 3 2 7 4 11l3 8 3 6c0 2 0 3 1 5l-1 1v-1l-1 1c0-3-1-4-2-6l-3-6-2-7c-1-3-1-7-2-10v-2z" class="G"></path><path d="M500 287l3-1 1 6 4 9v1l-1-1-1-1h-1v1c0 1-1 1-1 2v2l-1-1c0-1 0-1-1-2-1 0-2-1-3-2-1-2-1-3-1-5l1-1c1-1 1-4 1-6v-1z" class="c"></path><path d="M504 292l4 9v1l-1-1-1-1h-1v1c0 1-1 1-1 2v-3c0-1-1-1-1-2 1-2 1-4 1-6z" class="D"></path><path d="M500 288c0 2 1 4 1 6s0 5 1 8c-1 0-2-1-3-2-1-2-1-3-1-5l1-1c1-1 1-4 1-6z" class="AJ"></path><path d="M519 285l1 5v1 5 5 3 3h-1c-1 5 0 8-1 12h-1l-2-1c-2 1-3 4-4 6h0c0 1-1 1-1 2-1 0-1-1-2-2l-3-3c-2-1-3-1-5-2-3 2-5 4-7 6v1c-2-1-1-8-3-9v-1l1-1v-1h0l1-1v-3c2-3 3-6 5-9l1-1v1l1-1c1 1 2 2 3 2 1 1 1 1 1 2l1 1v-2c0-1 1-1 1-2v-1h1l1 1 1 1v-1l2 1c1-1 2-2 3-2 3-3 3-6 4-10h0v-3s2-1 2-2z" class="r"></path><path d="M506 317c1-1 2-1 3-2 1 0 1 0 2-1v5h-1c-1 1-1 3-1 4s0 1-1 1l-3-3c0-1 1-3 1-4z" class="i"></path><path d="M514 306c1 0 1 0 1-1 1 1 1 2 1 4 1-1 1-1 2-1l1-1c-1 5 0 8-1 12h-1v-5c-1-1-1-1-3-1v-2-3l-1-1 1-1z" class="AD"></path><path d="M504 303c0-1 1-1 1-2v-1h1l1 1-1 1c0 3 1 6 2 9h1l-1-4h1l1 1v1c0 2 1 3 1 5-1 1-1 1-2 1-1 1-2 1-3 2 0 1-1 3-1 4-2-1-3-1-5-2h1c2-1 2-3 3-5 0-3 0-5-3-8l2-2 1 1v-2z" class="I"></path><path d="M504 314l2-1h1c0 2 0 3-1 4 0 1-1 3-1 4-2-1-3-1-5-2h1c2-1 2-3 3-5z" class="c"></path><path d="M519 285l1 5v1 5 5 3 3h-1l-1 1c-1 0-1 0-2 1 0-2 0-3-1-4 0 1 0 1-1 1l-1 1-1-2c1 0 0 0 1-1-1-1-2-1-4-2h1c1-1 2-2 3-2 3-3 3-6 4-10h0v-3s2-1 2-2z" class="D"></path><path d="M520 296v5 3 3h-1l-1 1c-1 0-1 0-2 1 0-2 0-3-1-4 0 1 0 1-1 1v-2c4-1 4-4 6-8z" class="AH"></path><path d="M497 301l1-1v1l1-1c1 1 2 2 3 2 1 1 1 1 1 2l-2 2c3 3 3 5 3 8-1 2-1 4-3 5h-1c-3 2-5 4-7 6v1c-2-1-1-8-3-9v-1l1-1v-1h0l1-1v-3c2-3 3-6 5-9z" class="u"></path><path d="M499 300c1 1 2 2 3 2 1 1 1 1 1 2l-2 2c-1-2-2-3-3-5l1-1z" class="AM"></path></svg>