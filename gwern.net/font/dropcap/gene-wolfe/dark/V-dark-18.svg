<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="144 78 724 896"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#6b6a6a}.C{fill:#9b9a9a}.D{fill:#b6b5b4}.E{fill:#575757}.F{fill:#bdbcbb}.G{fill:#9b9a99}.H{fill:#212121}.I{fill:#a5a5a4}.J{fill:#807f7f}.K{fill:#333}.L{fill:#cecdcc}.M{fill:#eceae6}.N{fill:#c3c2c1}.O{fill:#e2e0de}.P{fill:#090909}.Q{fill:#434343}.R{fill:#b1b0af}.S{fill:#161516}.T{fill:#010201}</style><path d="M691 263l2 2-2 2-2-2 2-2z" class="B"></path><path d="M331 571v6l-3 1v-3l3-4z" class="J"></path><path d="M328 578l3-1-2 8c-1-2-1-5-1-7z" class="B"></path><path d="M609 178c4 0 9 0 14 1l-11 1h-2-1l1-1-1-1z" class="E"></path><path d="M742 544l1 8-1 3c-1-2-1-3-1-5l-1-6h2z" class="J"></path><path d="M254 436c4-2 7-3 11-3-4 2-7 3-10 6h-1l1-1c0-1 0-1-1-2z" class="F"></path><path d="M216 590l1 5v1 3l1 1h0c-1 0-2-1-3-2-1-2-1-4-2-6 2-1 2-1 3-2z" class="D"></path><path d="M389 849c5 0 12 0 17 1h3c-6 2-14 1-20-1z" class="C"></path><path d="M642 317c-1 1-3 3-4 5-1 1-1 3-3 5v-1s-1 0-1-1c1-4 5-8 7-11 0 2-1 3-2 4 1 0 2-1 3-1z" class="B"></path><path d="M633 287c2 1 4 3 6 3s3 0 4-2h1c0 1 0 1 1 2-2 1-4 3-6 2-3 0-5-1-7-3 0-1 1-2 1-2z" class="G"></path><defs><linearGradient id="A" x1="607.311" y1="179.168" x2="600.438" y2="180.729" xlink:href="#B"><stop offset="0" stop-color="#626262"></stop><stop offset="1" stop-color="#7e7d7e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M599 180l10-2 1 1-1 1h1 2l-13 3v-3z"></path><path d="M655 333c-1-3-1-5 1-8 1-1 3-2 4-2 2 0 5 0 6 1l-1 1c-2 0-5 0-7 2s-1 3-1 5l-2 1z" class="J"></path><path d="M588 857c-4-1-11-5-14-8h1c1 1 2 0 3 0 1 1 4 1 6 1 3 1 6 2 9 4h-1c-3 0-5-3-8-3v2h-2l2 2c2 0 3 1 4 2z" class="H"></path><path d="M672 318c1 3 1 6-1 9-1 2-3 5-6 5-1 1-3 1-5 0-1-1-1-2-1-3h1l2 2c3 0 4-1 6-3 2-3 2-5 2-8l2-2z" class="B"></path><path d="M250 521c7-4 15-5 23-5-2 1-4 1-5 1h0c1 0 2 0 3 1l3 1c-3 0-8-1-9 0l1 1h-7c-3 1-6 2-9 1z" class="F"></path><defs><linearGradient id="C" x1="597.407" y1="181.178" x2="590.55" y2="183.172" xlink:href="#B"><stop offset="0" stop-color="#929293"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#C)" d="M576 187c7-3 15-5 23-7v3c-8 2-15 5-23 9 2-2 3-2 5-3l2-1 5-2 1-1c1 0 1 0 2-1l-1-1-13 5-1-1z"></path><path d="M743 552l10 33c-1-1-1-2-2-3 0-1 0-2-1-2 0-2 0-2-1-3l-2-2c0-2-1-4-2-6l-3-14 1-3z" class="B"></path><defs><linearGradient id="D" x1="438.891" y1="838.399" x2="437.474" y2="846.912" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#363636"></stop></linearGradient></defs><path fill="url(#D)" d="M431 845c3-2 9-5 9-7h1l1-1h1l3 2c-4 5-9 10-15 13l1-1 1-1 1-1c1 0 1-1 2-2-1 0-3-1-4-2h-1z"></path><defs><linearGradient id="E" x1="331.856" y1="569.633" x2="326.144" y2="559.367" xlink:href="#B"><stop offset="0" stop-color="#9c9c9b"></stop><stop offset="1" stop-color="#c3c2c2"></stop></linearGradient></defs><path fill="url(#E)" d="M327 546c4 8 5 17 4 25l-3 4c0-4 0-8-1-12-1-5-3-10-5-15l2 2v1c1 2 2 4 2 6 1 1 1 2 2 2l2-2c-1-1-1-1-1-2v-2l-1-1v-2c-1-2-1-2-1-4z"></path><defs><linearGradient id="F" x1="622.877" y1="565.821" x2="604.564" y2="563.071" xlink:href="#B"><stop offset="0" stop-color="#373737"></stop><stop offset="1" stop-color="#595959"></stop></linearGradient></defs><path fill="url(#F)" d="M596 565c2-1 3-1 5-2 8-1 16 0 24 2l-1 2c-2-1-4-1-6-1-7-1-13-1-19 1-1 0-8 3-9 3v-1s1-1 2-1h1c2-1 3-1 5-2h1 1c1 0 1-1 2-1h2v-1l-7 1h-1z"></path><defs><linearGradient id="G" x1="305.048" y1="606.013" x2="289.036" y2="598.8" xlink:href="#B"><stop offset="0" stop-color="#434344"></stop><stop offset="1" stop-color="#767775"></stop></linearGradient></defs><path fill="url(#G)" d="M292 587c6 5 10 14 10 21 1 4 0 8-1 12-1-12-2-21-10-30 1-1 1 0 2 0h0l-1-2v-1z"></path><path d="M642 286c-1 0-1 0-3-1-1-1-1-3-1-5 1-1 2-3 4-4 1-1 2 0 3 0l1 1v3h0c1 3 1 6 0 8l-1 2c-1-1-1-1-1-2h-1 0c0-1 0-2-1-2z" class="I"></path><path d="M643 281c1 2 2 2 2 4 0 1-1 2-1 3h-1 0c0-1 0-2-1-2v-3l1-2z" class="P"></path><path d="M642 283c0 1 0 1-1 1h-1v-2c0-2 1-3 2-5 1 0 1 0 3 1v1l-2 2h0l-1 2z" class="S"></path><defs><linearGradient id="H" x1="551.885" y1="223.008" x2="544.745" y2="232.814" xlink:href="#B"><stop offset="0" stop-color="#9b9999"></stop><stop offset="1" stop-color="#bbbab9"></stop></linearGradient></defs><path fill="url(#H)" d="M539 248c1-4 3-9 4-13 4-8 8-18 15-24-1 3-3 5-4 8-3 5-5 12-7 18-1 4-2 9-4 13v-4c1-1 1-1 1-2v-1-1c1-1 1-1 1-2v-2h1v-1-1-1l1-1h-1c-3 2-5 10-6 14h-1z"></path><defs><linearGradient id="I" x1="726.568" y1="389.649" x2="734.883" y2="398.301" xlink:href="#B"><stop offset="0" stop-color="#222223"></stop><stop offset="1" stop-color="#5e5f5e"></stop></linearGradient></defs><path fill="url(#I)" d="M717 398h1l6-6c7-3 14-5 21-3v1h-1l-1 2c-9-1-16 1-23 7l-3 2h-2l2-3z"></path><path d="M641 314c5-4 11-5 17-5 4 0 9 2 11 5l3 4-2 2-1-2c-2-3-6-6-10-6-6-1-12 1-17 5-1 0-2 1-3 1 1-1 2-2 2-4z" class="J"></path><path d="M657 409h1c-2 2-3 4-4 6-1 1 0 0 0 1 3 1 7 0 10-1h-1c-3 3-7 4-10 5-5 5-5 10-5 17l2 9c-1-1-2-2-2-3-2-5-3-9-3-14l1-1c0-4 3-8 4-12 2-3 3-5 7-7z" class="K"></path><path d="M325 559c-2-3-3-7-5-10-2-4-4-8-6-11v-1l8 11c2 5 4 10 5 15 1 4 1 8 1 12v3c0 2 0 5 1 7-1 2-1 4-3 5h0c-2-1-1-4-3-5 0-1 0-5-1-6 1-1 1-1 2-1v-1-3c-1-1-1-2-1-3h1c2-2 1-10 1-12z" class="E"></path><path d="M325 559c2 6 3 18 1 24h-1l-1-5v-1-3c-1-1-1-2-1-3h1c2-2 1-10 1-12z" class="J"></path><defs><linearGradient id="J" x1="421.825" y1="850.171" x2="395.924" y2="855.638" xlink:href="#B"><stop offset="0" stop-color="#676766"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#J)" d="M378 846h1c3 1 6 3 10 4 13 5 26 4 38-2-1 3-4 4-7 6l-3 1h-1c-2 1-5 1-8 2 0 0-2 0-3 1-7 0-15 0-21-3h0 2 0 2l1 1h3c1 1 2 0 4 0 0-1 0-2-1-3-2 0-3-1-5-1-4-1-7-3-11-4 1 1 0 0 0 1-1-1-1-2-1-3z"></path><path d="M525 890c1 0 3-1 4-1l2 1-8 21c-2 0-5 1-7 2l3-9c2-3 3-10 6-12v-1-1z" class="E"></path><defs><linearGradient id="K" x1="321.689" y1="605.576" x2="316.811" y2="601.924" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#6b6a6c"></stop></linearGradient></defs><path fill="url(#K)" d="M322 579h0c1 1 1 5 1 6 1 5 1 10 0 15-1 6-3 11-5 17 0-4 1-7 1-10 0-7-2-14-4-22l3-1c2-1 4-3 4-5z"></path><defs><linearGradient id="L" x1="766.601" y1="609.301" x2="750.444" y2="606.442" xlink:href="#B"><stop offset="0" stop-color="#6e716f"></stop><stop offset="1" stop-color="#878486"></stop></linearGradient></defs><path fill="url(#L)" d="M748 583c1 2 2 3 2 4h1 1c0 1 0 2 1 2 0 2 1 3 3 4l8 26c1 5 3 9 4 14v1l-1 1c-4-12-8-25-15-36 1 0 1-1 1-1v-2c-2-4-4-8-5-13z"></path><defs><linearGradient id="M" x1="674.711" y1="219.718" x2="695.013" y2="199.521" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#abaaa9"></stop></linearGradient></defs><path fill="url(#M)" d="M689 200c1-3 1-7 1-10-1-1-1-2-1-4h0l2 4h0 1c1 1 2 2 2 3 1 7 1 14-3 19-3 5-8 7-13 8-2 0-4 1-5-1h-1c1-1 0-1 1-1 2-1 3-1 5-2l1-1c2 0 3-1 5-2 3-3 5-8 5-13z"></path><path d="M475 878l7 6c0 1 1 1 1 2 5 9 6 19 11 27-2-1-6-2-8-2l-7-20c-2-4-5-9-4-13z" class="E"></path><path d="M449 339c2-1 6-1 8 0s4 3 5 6c2 8-4 10-7 16-1 2-1 5 0 8v1c1 0 1 1 1 2 4 4 9 5 14 6h0c-2 1-5 1-7 1l-1-1c-2 0-4-1-5-2-3-3-5-6-5-10-1-3 0-5 1-7 2-3 5-5 6-9 1-2 1-4 0-6-1-1-3-3-5-3s-5 0-7 1c-1 2-2 3-2 5s0 3 1 4h3v-1l-2-2 1-1h1c1 0 1 0 2 2 0 2-1 2-2 4-1 0-2 1-3 0-2-1-3-1-3-3-1-2-1-5 0-7s4-3 6-4z" class="Q"></path><defs><linearGradient id="N" x1="739.069" y1="412.227" x2="764.786" y2="413.582" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#888"></stop></linearGradient></defs><path fill="url(#N)" d="M745 389c7 2 13 6 17 12s5 13 3 20c-1 5-5 10-9 13-4 2-9 3-14 2-3-1-5-3-7-5v-1c1 0 2 2 3 2h0l-1-2v-1c2 2 4 3 6 4 3 1 7 0 10-1 4-2 8-6 9-11 2-5 1-11-2-16-3-7-9-11-17-13l1-2h1v-1z"></path><defs><linearGradient id="O" x1="285.158" y1="603.446" x2="252.275" y2="584.954" xlink:href="#B"><stop offset="0" stop-color="#7a7a79"></stop><stop offset="1" stop-color="#abaaaa"></stop></linearGradient></defs><path fill="url(#O)" d="M267 599c0 2 0 4-1 5-1 3-4 4-7 5-3-1-6-1-9-4-2-2-3-5-3-8 0-5 2-9 5-12 5-4 12-7 18-7 9 0 16 4 22 9v1l1 2h0c-1 0-1-1-2 0-5-5-11-8-17-9h-1c-6 0-13 2-18 6-3 2-5 5-5 9 0 2 0 5 2 7 2 1 5 3 7 3 3 0 5-1 6-3 1-1 1-3 0-4h2z"></path><path d="M573 148c3-2 9-1 12-1 4 1 9 1 13 0 3 1 5 1 8 1-3 1-7 2-10 3s-7 2-10 2c-3-1-12-1-16 0-3 1-7 3-11 4-3 2-7 6-11 6 0 1-1 0-2 0 8-6 17-13 27-15z" class="D"></path><path d="M573 148c3-2 9-1 12-1 4 1 9 1 13 0 3 1 5 1 8 1-3 1-7 2-10 3l-1-1h-2c-5 0-9 0-13-1-3 0-6 1-9 1l1-1h1v-1z" class="J"></path><defs><linearGradient id="P" x1="619.998" y1="172.589" x2="588.977" y2="165.519" xlink:href="#B"><stop offset="0" stop-color="#bbb"></stop><stop offset="1" stop-color="#e8e6e4"></stop></linearGradient></defs><path fill="url(#P)" d="M584 172c9-5 21-8 31-9 4-1 8-1 12 0h1v1h-2v1h1c-7 4-17 5-25 7l-18 6h-1c2-1 2-2 4-2 2-2 0-2 1-5-1 0-2 1-4 1z"></path><defs><linearGradient id="Q" x1="672.271" y1="283.471" x2="639.429" y2="255.831" xlink:href="#B"><stop offset="0" stop-color="#767675"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#Q)" d="M632 289c-2-3-4-7-4-11 0-6 3-13 7-18 7-7 16-11 25-11 12 0 22 5 31 14l-2 2c-8-8-18-13-29-13-9 0-17 4-23 10-4 4-7 10-6 17 0 3 1 5 2 8 0 0-1 1-1 2z"></path><defs><linearGradient id="R" x1="654.187" y1="214.991" x2="641.889" y2="192.02" xlink:href="#B"><stop offset="0" stop-color="#858484"></stop><stop offset="1" stop-color="#bdbdbc"></stop></linearGradient></defs><path fill="url(#R)" d="M641 168c4 3 7 11 9 16 0 1 1 4 1 4 1 1 2 0 3 2v1h1c0-2-1-3 0-6l1 1c1 4 1 7 2 10v7c0-1 1-1 1-1l2 1 2 2c-4 6-9 13-16 15-4 2-8 1-12-1h0c3 0 7 1 11-1 3-2 5-4 6-7 3-15-4-31-11-43z"></path><path d="M584 172c2 0 3-1 4-1-1 3 1 3-1 5-2 0-2 1-4 2h1c-18 8-33 19-45 34-3 4-6 9-10 12 9-17 24-33 41-44 4-3 9-6 14-8z" class="M"></path><defs><linearGradient id="S" x1="268.66" y1="616.826" x2="222.194" y2="591.113" xlink:href="#B"><stop offset="0" stop-color="#868686"></stop><stop offset="1" stop-color="#b2b1b0"></stop></linearGradient></defs><path fill="url(#S)" d="M265 599l-1-2h-2l-1 2 1 1c-1 1-1 1-2 1s-1 0-2-1c0-2 1-3 2-5 2-1 4-1 7-1 2 1 4 3 5 5 2 3 3 6 2 10-1 5-5 9-9 11-6 4-15 5-22 3h0c-2 0-4-1-6-2-10-4-18-12-22-23 1 1 2 2 3 2h0l-1-1v-3-1c4 11 11 20 22 24 7 3 15 3 22 0 4-2 8-5 10-9 1-3 1-7-1-9-1-2-3-4-5-5 1 1 1 2 2 3h-2z"></path><defs><linearGradient id="T" x1="699.552" y1="312.571" x2="674.47" y2="301.218" xlink:href="#B"><stop offset="0" stop-color="#404040"></stop><stop offset="1" stop-color="#727272"></stop></linearGradient></defs><path fill="url(#T)" d="M693 265c8 10 11 22 10 35s-7 24-16 32c-6 5-14 10-22 9-3 0-6-2-8-5l-2-2h0v-1l2-1v2c2 2 4 4 8 4 6 1 14-3 19-7 9-7 15-18 16-30 2-13-2-24-9-34l2-2z"></path><path d="M598 147h4 1 0 2c1 1 2 0 4 1h0c1 0 2 0 3 1h-1v2h1c1 0 2 0 3 1s3 2 4 3v1c-1 1-3 1-4 1s-2 0-3 1h-3c-1 1-2 1-3 1-1 1-3 1-5 1l-11 4c-1 0-1 1-2 1l-1 1c-4 1-8 3-12 6-1 0-2 0-2 1-1 1-2 1-3 1l-2 2-3 3-2 1-3 2v-1c0-1 2-2 3-3 6-5 13-8 21-12 0-1 1-2 2-3 1 0 2 0 3-1 3-1 5-1 7-3v-2-3h-2c-1 0-2 1-3 0h-1c-1 0-3 1-4 1l-11 4c1 0 1-1 1-1 3-2 7-3 10-5 3 0 7-1 10-2s7-2 10-3c-3 0-5 0-8-1z" class="T"></path><path d="M590 154c6-2 13-2 19-2l3 4-28 10c0-1 1-2 2-3 1 0 2 0 3-1 3-1 5-1 7-3v-2-3h-2c-1 0-2 1-3 0h-1z" class="D"></path><defs><linearGradient id="U" x1="650.599" y1="168.753" x2="663.696" y2="170.966" xlink:href="#B"><stop offset="0" stop-color="#3a3a3a"></stop><stop offset="1" stop-color="#5c5b5b"></stop></linearGradient></defs><path fill="url(#U)" d="M650 152c7 8 11 17 13 27l1 1v4l2 2 1-2c1 0 1 0 2 1h0v1l-1-1c-1 1-1 2 0 3 0 5-2 10-4 15l-1 2-2-2-2-1s-1 0-1 1v-7c-1-3-1-6-2-10v-3h1c0 1 0 2 1 3h1v-7l-1-3h0c-2-9-5-15-9-23l1-1z"></path><path d="M659 179c0 5 0 11 2 17v4 1 2l-2-1s-1 0-1 1v-7c-1-3-1-6-2-10v-3h1c0 1 0 2 1 3h1v-7z" class="C"></path><defs><linearGradient id="V" x1="661.407" y1="200.607" x2="666.32" y2="186.935" xlink:href="#B"><stop offset="0" stop-color="#aeacac"></stop><stop offset="1" stop-color="#d2d1d0"></stop></linearGradient></defs><path fill="url(#V)" d="M663 179l1 1v4l2 2 1-2c1 0 1 0 2 1h0v1l-1-1c-1 1-1 2 0 3 0 5-2 10-4 15l-1 2-2-2v-2-1c2-7 3-13 2-21z"></path><path d="M661 201v-1l3 3-1 2-2-2v-2z" class="I"></path><defs><linearGradient id="W" x1="446.579" y1="828.736" x2="387.141" y2="846.285" xlink:href="#B"><stop offset="0" stop-color="#505050"></stop><stop offset="1" stop-color="#a6a5a5"></stop></linearGradient></defs><path fill="url(#W)" d="M387 844c9 3 17 3 26 0 15-4 25-13 32-27v3c0 2 1 4 0 6-1 3-4 6-7 9-6 7-18 14-28 15h-1-3c-5-1-12-1-17-1l-3-1c1 0 4-1 4-2-2-1-2-1-3-2z"></path><defs><linearGradient id="X" x1="634.495" y1="389.498" x2="647.423" y2="398.113" xlink:href="#B"><stop offset="0" stop-color="#838383"></stop><stop offset="1" stop-color="#c8c6c5"></stop></linearGradient></defs><path fill="url(#X)" d="M651 368c4-2 9-2 13-1 1 0 2 0 3 1h0c-5 0-8 1-11 4-4 4-4 8-5 13-2 14-6 24-19 30-7 3-15 5-22 5h-1c0-1 1-2 2-3 4 0 13-2 17-4 1-1 1-2 1-3 5-2 10-5 13-10 3-3 5-8 5-12 1-1 1-4 1-5h-1c0-5 5-9 4-15z"></path><defs><linearGradient id="Y" x1="568.291" y1="856.776" x2="542.722" y2="856.374" xlink:href="#B"><stop offset="0" stop-color="#878888"></stop><stop offset="1" stop-color="#b1afae"></stop></linearGradient></defs><path fill="url(#Y)" d="M557 834h0v-2c3 3 5 6 9 9 1 0 5 2 5 3-4 1-6 1-9 0-2 14-6 27-18 36l-1 1v-1l1-1c-1-1 0-3-1-5v-4l4-6c6-8 11-20 10-30z"></path><defs><linearGradient id="Z" x1="320.243" y1="881.265" x2="309.902" y2="840.33" xlink:href="#B"><stop offset="0" stop-color="#7e807f"></stop><stop offset="1" stop-color="#bcb8b8"></stop></linearGradient></defs><path fill="url(#Z)" d="M279 841c11 9 21 18 35 23 7 3 15 4 22 6 1 1 3 1 5 1l9 1h7 0 0c-2 1-2 1-3 1-2 1-3 1-4 1h-1l-2 1c-23-2-44-8-62-22-5-3-10-7-13-11 1 1 2 1 3 2l6 5c1 0 2-1 3-2l-2-2-3-3v-1z"></path><defs><linearGradient id="a" x1="723.699" y1="841.201" x2="763.918" y2="808.705" xlink:href="#B"><stop offset="0" stop-color="#7f7e7d"></stop><stop offset="1" stop-color="#b0afae"></stop></linearGradient></defs><path fill="url(#a)" d="M758 796v1c1 1 1 2 0 3 0 1 0 1 1 2 0 1 1 2 1 3l3-3c-6 16-16 31-27 43-3 3-6 6-9 8 0-1 1-1 1-2l2-2c0-1-1-2-2-3h-1l-4 4h-1c9-8 16-16 23-26 1-3 3-6 4-9l1-2h0c0-1 1-1 1-2l4-8c1-1 1-2 1-3l1-1v-1l1-2z"></path><defs><linearGradient id="b" x1="286.591" y1="454.54" x2="246.596" y2="455.731" xlink:href="#B"><stop offset="0" stop-color="#929292"></stop><stop offset="1" stop-color="#b8b7b7"></stop></linearGradient></defs><path fill="url(#b)" d="M254 439l-1 2c-3 5-5 11-4 16s5 10 9 12c4 3 10 3 14 2 5-1 9-4 11-8s2-8 1-12c-1-3-3-6-6-7-2-1-6 0-8 1s-3 2-3 4c-1 1-1 3 0 4s2 1 4 2c0-1 1-2 1-3s0-1-1-1h-1l-1-1v-1h2c1 0 1 0 2 1 0 1 1 2 0 3 0 2-1 3-3 4-1 0-3 0-4-1-2-1-2-4-2-6-1-2 1-5 2-6 2-2 6-4 9-3 4 0 7 2 9 4 3 3 4 8 4 12-1 5-3 10-7 13-5 4-11 6-17 5-5-1-10-4-14-8-3-5-5-12-4-17v-1c1-6 4-9 8-13 1 1 1 1 1 2l-1 1z"></path><path d="M590 570c-5 3-13 6-19 7h0l-2 1c-9 1-19 0-26-5-5-4-9-10-10-16-1-5 0-11 3-15 2-3 5-5 8-5s5 1 7 2c3 3 4 6 4 10 1 2 0 5-2 7-1 1-3 2-4 2-2 0-3 0-5-2-1-2-1-5-1-7 1-1 1-2 3-2 1 0 1 0 2 1l-1 1-1-1-1 1h1v2c0 2 0 3 1 4 2 1 2 1 3 0s2-1 2-2c1-3 0-7-1-9s-3-3-5-4c-2 0-4 0-5 1-2 2-4 4-4 7l-1 1v9c2 6 5 10 10 13 9 6 21 5 31 1 6-2 12-6 19-7h1l7-1v1h-2c-1 0-1 1-2 1h-1-1c-2 1-3 1-5 2h-1c-1 0-2 1-2 1v1z" class="B"></path><defs><linearGradient id="c" x1="480.191" y1="454.176" x2="484.693" y2="415.157" xlink:href="#B"><stop offset="0" stop-color="#6e6e6e"></stop><stop offset="1" stop-color="#a5a4a3"></stop></linearGradient></defs><path fill="url(#c)" d="M486 403c0 3 1 5 3 8h0 2l2 1 1 4c0 2 1 3 0 5v-1h-2-1c-1 1-1 1-2 1-3 2-4 4-5 7-4 12-2 24 4 35-12-15-13-32-14-50h0c1 2 1 4 1 6l2 1h2c0-2 0-8 1-9h0l1-1c2-2 4-4 5-7z"></path><path d="M489 411h2l2 1 1 4h-2-2c-2 0-4 1-6 0l5-5z" class="I"></path><path d="M486 403c0 3 1 5 3 8h0l-5 5c-1 1-3 3-5 4 0-2 0-8 1-9h0l1-1c2-2 4-4 5-7zm7 9c6 20 13 39 25 56 2 4 5 8 8 11 3 4 6 8 9 11 5 5 11 11 15 17l-1 2-23-22c-11-11-19-23-25-37-3-4-5-9-6-14-2-5-3-11-6-15 1 0 1 0 2-1h1 2v1c1-2 0-3 0-5l-1-4h0z" class="C"></path><defs><linearGradient id="d" x1="391.925" y1="851.738" x2="394.661" y2="869.317" xlink:href="#B"><stop offset="0" stop-color="#3d3c3c"></stop><stop offset="1" stop-color="#8a8a8a"></stop></linearGradient></defs><path fill="url(#d)" d="M431 845h1c1 1 3 2 4 2-1 1-1 2-2 2l-1 1-1 1-1 1c-24 16-55 24-84 23l2-1h1c1 0 2 0 4-1 1 0 1 0 3-1h0 0-7l-9-1c1-1 3-1 4-3-2-1-3-1-5-2l1-1c9 4 19 5 29 4-1-1-4-1-6-1 1-1 2-1 2-3h3c2 1 2 0 4 0h0c10-1 20-3 30-6 1-1 1-1 2-1 1-1 3-1 3-1 3-1 6-1 8-2h1l3-1c3-2 6-3 7-6 1-1 4-2 4-3z"></path><defs><linearGradient id="e" x1="359.191" y1="872.968" x2="347.809" y2="866.032" xlink:href="#B"><stop offset="0" stop-color="#322f31"></stop><stop offset="1" stop-color="#4b4c4a"></stop></linearGradient></defs><path fill="url(#e)" d="M341 865c9 4 19 5 29 4l10-1h0c-5 2-11 2-16 3s-10 1-14 1l-9-1c1-1 3-1 4-3-2-1-3-1-5-2l1-1z"></path><path d="M403 859c-1 1-2 1-3 2-2 0-4 2-6 2l-4 2h-1l-3 1c-1 1-2 1-2 1-2 0-3 0-4 1l-10 1c-1-1-4-1-6-1 1-1 2-1 2-3h3c2 1 2 0 4 0h0c10-1 20-3 30-6z" class="G"></path><path d="M273 516c17 0 33 5 46 17 2 3 8 10 8 13 0 2 0 2 1 4v2l1 1v2c0 1 0 1 1 2l-2 2c-1 0-1-1-2-2 0-2-1-4-2-6v-1l-2-2-8-11v1c2 3 4 7 6 11 2 3 3 7 5 10 0 2 1 10-1 12h-1c0-2-1-3-2-5v-1c-1-3-3-6-4-9-7-14-16-26-30-33-2-1-4-2-7-3-2-1-4-1-6-1l-3-1c-1-1-2-1-3-1h0c1 0 3 0 5-1zm243 397c2-1 5-2 7-2l-17 50-20-50c2 0 6 1 8 2h1c5 5 8 12 10 18 2-1 2-3 3-5 2-5 3-10 8-13z" class="M"></path><path d="M482 884c15 7 26 9 43 6v1 1c-3 2-4 9-6 12l-3 9c-5 3-6 8-8 13-1 2-1 4-3 5-2-6-5-13-10-18h-1c-5-8-6-18-11-27 0-1-1-1-1-2z" class="S"></path><defs><linearGradient id="f" x1="569.722" y1="522.981" x2="567.515" y2="479.527" xlink:href="#B"><stop offset="0" stop-color="#8d8d8d"></stop><stop offset="1" stop-color="#d2d1cf"></stop></linearGradient></defs><path fill="url(#f)" d="M526 479h1l6 7 1-1v-3h2 0l2-1v-2c-1-1-1-1-1-2l-1-1c1 0 1 1 2 1 2 2 4 6 6 7l1 2c2 1 4 1 7 0l2 2c0-1 0 0 1-1 0 0 1 0 2-1h0 1 1c1-1 1 0 2-1h1c2 1 5 0 7 0h9c1 1 2 1 3 2 3 2 6 3 10 5 1 1 2 1 3 2h2v1c3 2 7 5 10 7h0l-1 1c1 0 1 1 2 1 4 2 7 4 10 7v1c-1-2-2-3-3-4h-1c-2-1-4-2-5-3h-1-2-2 0 1c2 1 3 3 5 4h1c1 1 1 1 2 1 2 0 2 1 3 2h-1c-2-1-1 0-2 0-1-1-1-1-2-1-1-2-4-3-6-4-7-5-15-11-24-13-5-2-10-1-15 1-12 9-5 27-1 38 1 1 2 5 2 6-5-10-10-21-17-30l1-2c-4-6-10-12-15-17-3-3-6-7-9-11z"></path><path d="M578 485c1 1 2 1 3 2h-3c-7-1-15 0-21 3-3 2-5 4-7 6l1-4c2-4 7-5 11-7 2 1 5 0 7 0h9z" class="J"></path><defs><linearGradient id="g" x1="609.339" y1="522.409" x2="612.418" y2="492.31" xlink:href="#B"><stop offset="0" stop-color="#949494"></stop><stop offset="1" stop-color="#dedcdb"></stop></linearGradient></defs><path fill="url(#g)" d="M599 486l29 19c3 2 6 4 10 6 1 0 3 1 4 2l-2 7-4 13-2-1c-2-1-5-2-7-4l-2-1c-8-3-15-8-23-10h-1c-2 0-4-1-6-1-3 0-6 1-8 3l-2 2v-1c0-2 1-3 2-4 4-5 15-6 21-6l2 1c1 0 1 0 2 1 1 0 0-1 2 0h1c-1-1-1-2-3-2-1 0-1 0-2-1h-1c-2-1-3-3-5-4h-1 0 2 2 1c1 1 3 2 5 3h1c1 1 2 2 3 4v-1c-3-3-6-5-10-7-1 0-1-1-2-1l1-1h0c-3-2-7-5-10-7v-1h0c2 1 3 2 5 3v-1c1-1 2-3 3-4v-1c-1-1-4-2-5-4v-1z"></path><defs><linearGradient id="h" x1="635.973" y1="524.298" x2="616.919" y2="518.366" xlink:href="#B"><stop offset="0" stop-color="#3d3d3d"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#h)" d="M638 511c1 0 3 1 4 2l-2 7-4 13-2-1c-2-1-5-2-7-4l-2-1c-3-5-9-9-14-12 3 0 5 0 8 1l9 3h8c1-2 1-5 2-8h0z"></path><path d="M627 528c1 0 3-1 5-1h1c2-1 3-2 3-4 1-1 2-3 4-5l-1 1 1 1-4 13-2-1c-2-1-5-2-7-4z" class="K"></path><defs><linearGradient id="i" x1="507.411" y1="846.281" x2="481.754" y2="882.183" xlink:href="#B"><stop offset="0" stop-color="#bcbab9"></stop><stop offset="1" stop-color="#f7f6f4"></stop></linearGradient></defs><path fill="url(#i)" d="M453 837l1-1c0 1 0 1 1 2l1 3 1 2c1-1 1-1 1-2h1c7 16 19 30 36 37 10 4 24 5 34 0 5-2 10-5 14-8v4c1 2 0 4 1 5l-1 1v1c-4 4-9 7-14 8-1 0-3 1-4 1-17 3-28 1-43-6l-7-6c-9-8-15-16-19-28-2-4-3-8-3-13z"></path><defs><linearGradient id="j" x1="326.194" y1="451.551" x2="253.211" y2="440.858" xlink:href="#B"><stop offset="0" stop-color="#cdccc9"></stop><stop offset="1" stop-color="#faf8f9"></stop></linearGradient></defs><path fill="url(#j)" d="M321 489c-4-6-9-12-15-16-2-2-5-3-8-4v-1c3-3 8-4 10-7v-7c-2-10-10-21-19-26-6-4-14-7-22-6-3 0-6 1-9 2l9-5c2-1 4-2 5-4v-1c-1-1-3-1-4-1-7 0-12 4-17 9l3-5c3-4 8-8 14-9h1c7-2 16 0 23 4l4 2c1 0 2 1 3 2h1c2 3 3 7 4 10l7 19 15 40c-1-1-2-3-3-4 0 1 0 2-1 3h0c0 2 0 3-1 5z"></path><path d="M296 414c1 0 2 1 3 2l1 2v1c-2 0-3 0-4 1 0 2 0 2-1 3-1-1-2-1-3-3l1-1c1-2 2-3 3-5z" class="N"></path><path d="M300 416c2 3 3 7 4 10-1 1-1 2-2 2-2-1-3-1-4-2-1 0-3-2-3-3 1-1 1-1 1-3 1-1 2-1 4-1v-1l-1-2h1z" class="C"></path><defs><linearGradient id="k" x1="307.101" y1="438.458" x2="303.802" y2="430.583" xlink:href="#B"><stop offset="0" stop-color="#646465"></stop><stop offset="1" stop-color="#7b7b7b"></stop></linearGradient></defs><path fill="url(#k)" d="M298 426c1 1 2 1 4 2 1 0 1-1 2-2l7 19h-1v2c-2-7-7-15-12-21z"></path><path d="M310 447v-2h1l15 40c-1-1-2-3-3-4l-11-12h0 2c2 0 2 0 3-1 0-2-1-3-2-5h-1c-1 0-1-1-2-1v-5c-1-3-1-6-2-10z" class="Q"></path><path d="M323 481c1 1 2 3 3 4 3 4 4 11 6 16l11 31h0c0 5 1 10 2 15 1 1 3 2 4 3h1c1 2 1 3 1 5l10 25c-1 2-3 3-5 4v-1c-4 0-7 4-11 5-1 2-1 3-2 5 0 1 0 1-1 2v-1c-2 5-3 9-5 13v-2h0v-1l1-1v-1h0v-1l1-1v-1h0v-1l1-1v-1h0v-1l1-1v-1h0v-2h0c0-1 0-2 1-2h0 0l1-1-2-2c-1 1-1 2-1 4-1 3-3 11-5 14h-1c-1 2-6 14-8 15-2 2-4 4-6 7-3 4-7 9-11 12-1 1-2 2-3 2h-1l-7 6-1-1 3-5a79.93 79.93 0 0 0-13 13h-1c4-6 12-13 18-16 1 0 2-1 4-2s4-4 6-6c10-12 17-26 21-41 3-10 4-19 4-29 0-6 1-11 0-16-1-20-8-37-18-54 1-2 1-3 1-5h0c1-1 1-2 1-3z" class="C"></path><defs><linearGradient id="l" x1="354.797" y1="558.245" x2="333.986" y2="576.507" xlink:href="#B"><stop offset="0" stop-color="#7c7c7c"></stop><stop offset="1" stop-color="#adabaa"></stop></linearGradient></defs><path fill="url(#l)" d="M345 547c1 1 3 2 4 3h1c1 2 1 3 1 5l-9 39c-2 5-3 9-5 13v-2h0v-1l1-1v-1h0v-1l1-1v-1h0v-1l1-1v-1h0v-1l1-1v-1h0v-2h0c0-1 0-2 1-2h0 0l1-1-2-2c-1 1-1 2-1 4-1 3-3 11-5 14h-1c2-7 6-15 7-22l1-5c1-1 1-1 1-3h0v-3c1-2 1-3 1-4v-1c0-2 1-3-1-4 1-2 1-3 1-5 0-3 0-7 1-10z"></path><path d="M342 594l9-39 10 25c-1 2-3 3-5 4v-1c-4 0-7 4-11 5-1 2-1 3-2 5 0 1 0 1-1 2v-1z" class="T"></path><path d="M345 588c0-1 0-2 1-3 2-2 9-5 12-5h1c-1 2-2 2-3 3-4 0-7 4-11 5z" class="H"></path><defs><linearGradient id="m" x1="652.639" y1="836.785" x2="661.028" y2="877.001" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#m)" d="M748 817l1-2c-1 3-3 6-4 9-7 10-14 18-23 26h1l4-4h1c1 1 2 2 2 3l-2 2c0 1-1 1-1 2-3 3-7 6-11 8-16 10-34 14-52 14-11 0-21-2-31-4-16-3-31-8-45-14-1-1-2-2-4-2l-2-2h2v-2c3 0 5 3 8 3h1l19 7h0c13 3 26 6 39 5l15-1c12-1 23-4 34-9 6-2 14-7 18-12h0c1-1 2-2 3-2 1-1 1-2 2-2l1-1 16-16c3-2 4-5 8-6z"></path><path d="M651 866c2 1 4 0 6 1h-7c0 1-1 1-1 1-1 0-2 0-2-1-2 1-2 1-4 1h-2c-1 1-1 1-2 1-3 0-8 1-10 0l-1-1h-1c-1-1-1-1-1-3v-1h-2c-1 0-3-1-5-1l-7-2h0c13 3 26 6 39 5z" class="J"></path><path d="M748 817l1-2c-1 3-3 6-4 9-7 10-14 18-23 26-9 6-18 12-28 15s-21 5-31 5h0c7-1 15-3 23-5 1 0 5-1 6-2h0c15-4 27-11 37-22 5-5 9-10 13-15-1-1-1-2-2-3 3-2 4-5 8-6z" class="H"></path><path d="M740 823c3-2 4-5 8-6l-6 9c-1-1-1-2-2-3z" class="I"></path><defs><linearGradient id="n" x1="724.385" y1="851.3" x2="718.192" y2="831.996" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#9c9c9a"></stop></linearGradient></defs><path fill="url(#n)" d="M740 823c1 1 1 2 2 3-4 5-8 10-13 15-10 11-22 18-37 22h0 0l6-2 1-1h1c1 0 1-1 2-1h1v-1l-3-2c6-2 14-7 18-12h0c1-1 2-2 3-2 1-1 1-2 2-2l1-1 16-16z"></path><path d="M715 401h2l3-2c7-6 14-8 23-7 8 2 14 6 17 13 3 5 4 11 2 16-1 5-5 9-9 11-3 1-7 2-10 1-2-1-4-2-6-4v1l1 2h0c-1 0-2-2-3-2v1l-1 1v-2c-1 0 0 0-1-1 0-2 0-1-1-2 0-2 0-4 1-5v-1c1-2 2-4 4-5 1-1 2 0 3-1h1c0-1-1-2-2-3h0l-1-1h-1 0c-2-2-4-2-7-2-1-1-3 0-4 0s-2 0-4 1h-2-1c-2 1-3 2-4 2-1 1-1 1-2 1s-1 1-1 1c0 2-1 2-1 4l-16 47c0 1-1 2-1 3l-3 8c0 1-1 2 0 3h1c1 1 3 3 4 5 0 1 1 1 2 2l1 2 1 1 1 2 3 4 5 7c1 1 1 2 2 3 0 1 1 1 1 1v-2-4c0-2 0-6-1-7v-5c-1-2-1-3-2-5v-2l-1-1c-1-1-2-3-4-3 0-1-1-1-2-2v-1c0-1 0-1 1-1h3c1-1 2-1 3-1 4-1 7-2 11-4v-1c3-2 4-4 5-7 1-1 1-1 1-2l1-1v-3c1 0 1-1 1-2s0 1 0-1l1-2c1 0 1 0 2 1v1h-1-1c-1 2-1 5-2 7-3 9-9 12-17 14l-8 2c5 3 7 5 9 10 2 9 1 18 3 27l-18-26c-2 2-2 4-5 6-1-2-3-4-4-5l2-7 26-78z" class="P"></path><path d="M735 430h0c-1-1-2-4-2-5 0-4 1-6 4-8 1 1 2 1 3 2-2 1-4 2-4 4s0 4 1 6v1l1 2h0c-1 0-2-2-3-2z" class="E"></path><path d="M687 486l2-7c2 1 6 3 7 6-2 2-2 4-5 6-1-2-3-4-4-5z" class="J"></path><path d="M742 413c-1-1-1-2-3-3l1-1c1 1 2 1 3 2s4 8 4 8c2 1 6-1 5 2-1 1-4 0-5 0s-1 2-2 2c-1-1-1-2-1-4-2-1-3 0-4 0-1-1-2-1-3-2 2-1 3-1 5-1h1c0-1-1-2-1-3z" class="B"></path><defs><linearGradient id="o" x1="723.383" y1="404.587" x2="728.39" y2="413.394" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#4a4b4b"></stop></linearGradient></defs><path fill="url(#o)" d="M742 413c-2-2-4-3-7-4-7-3-15 0-21 3l-1-1 2-2c4-3 11-4 16-4 5 1 9 2 12 6-1-1-2-1-3-2l-1 1c2 1 2 2 3 3z"></path><defs><linearGradient id="p" x1="328.843" y1="735.974" x2="249.746" y2="703.524" xlink:href="#B"><stop offset="0" stop-color="#c5c4c3"></stop><stop offset="1" stop-color="#f3f1ef"></stop></linearGradient></defs><path fill="url(#p)" d="M272 842h-1c-14-13-24-31-29-49-2-7-3-13-4-20-1-6-1-14-4-20 0-1-2-3-3-4l-1-1h0c2-1 4-1 6 0s3 3 4 5h0c1-5 1-11 2-17 2-9 6-19 10-28 5-13 13-25 21-37 5-10 11-19 13-29 1-5 1-9 1-14l5 10c6-6 10-14 13-21 2-4 4-8 5-13 1-4 1-8 1-12 3 7 4 13 3 21v1c-1 9-4 16-10 23-6 3-14 10-18 16h1a79.93 79.93 0 0 1 13-13l-3 5 1 1 7-6h1l-21 22v2c-1 2-4 2-2 4-6 7-11 14-16 21-3 4-5 10-8 12-1 1-1 2-2 4l1 2c-13 26-18 60-8 89 1 2 2 5 4 8 0 3 3 9 5 11l8 12c1 1 2 3 3 4l9 10h0v1l3 3 2 2c-1 1-2 2-3 2l-6-5c-1-1-2-1-3-2z"></path><path d="M297 645l1 1c-4 5-9 10-14 15v-1c4-5 7-11 13-15z" class="G"></path><path d="M285 662v2c-1 2-4 2-2 4-6 7-11 14-16 21-3 4-5 10-8 12 1-2 3-5 4-7 5-10 10-18 17-25 2-3 3-5 5-7z" class="E"></path><path d="M454 804c1-3 2-6 4-9 2-4 4-7 8-9 2 0 3 0 5 1 1 2-1 6-1 9 1 2 1 6 2 9v1c1 3 2 7 4 10 1 3 4 8 4 11h1c1 2 1 4 2 6-1 3-1 7 0 10 0 2 1 4 0 6h0v2 1l2 3v1c0 1 0 1 1 2v1c2 2 4 4 7 6h1c3 3 6 5 10 6h0c4 1 8 2 13 1 2 0 4-1 6-1 1 0 2-1 3-1l8-5c2-2 6-5 8-7 0-2 1-3 2-4h1c4-6 5-12 8-19h0 0l2-5c1 0 1 0 2 1h0l-1 1v3h0 0l1-1c1 10-4 22-10 30l-4 6c-4 3-9 6-14 8-10 5-24 4-34 0-17-7-29-21-36-37l-2-8c-2-5-3-11-3-16-1-4 0-8 0-13z" class="P"></path><path d="M461 816h2v1c1 5 3 10 5 15-2-1-2-4-5-4-1 0-1 1-1 2v9c-3-6-2-16-1-23z" class="C"></path><path d="M454 804l2 2v2h1v-1c0-1 0-1 1-2l1 1v1c-1 3-1 6-1 9h0c-1 1-1 1-1 2s-1 4 0 6c0 1 0 1 1 2v3h-1 0v4c-2-5-3-11-3-16-1-4 0-8 0-13z" class="S"></path><defs><linearGradient id="q" x1="466.857" y1="819.137" x2="466.143" y2="828.863" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#383838"></stop></linearGradient></defs><path fill="url(#q)" d="M461 816v-2l1-1c1 1 1 1 1 2l2 1v3-3c0-1 0-2 1-2v-1c1 2 1 2 1 4l1 1h0v2 2l2 5v1l1 3c1 1 2 3 2 4l7 15c0-1-1-1-2-1s-3-5-3-6c-2-4-5-8-7-11-2-5-4-10-5-15v-1h-2z"></path><path d="M470 796c1 2 1 6 2 9v1c1 3 2 7 4 10 1 3 4 8 4 11h1c1 2 1 4 2 6-1 3-1 7 0 10 0 2 1 4 0 6h0v2 1l-1-1c-2-4-3-7-4-11-4-11-8-23-9-35 0-3 0-6 1-9z" class="D"></path><defs><linearGradient id="r" x1="518.802" y1="812.653" x2="491.003" y2="871.557" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#c4c2c1"></stop></linearGradient></defs><path fill="url(#r)" d="M463 828c3 0 3 3 5 4 2 3 5 7 7 11 0 1 2 6 3 6s2 0 2 1c1 2 3 4 5 6 0 1 0 1 1 2v1c2 2 4 4 7 6h1c3 3 6 5 10 6h0c4 1 8 2 13 1 2 0 4-1 6-1 1 0 2-1 3-1l8-5c2-2 6-5 8-7 0-2 1-3 2-4h1c4-6 5-12 8-19h0 0l2-5c1 0 1 0 2 1h0l-1 1v3h0 0c-4 15-8 30-23 38-3 2-7 3-10 4h-1c-11 3-23 0-33-5-12-7-23-19-27-33h0v-9c0-1 0-2 1-2z"></path><path d="M463 828c3 0 3 3 5 4 2 3 5 7 7 11-2-1-3-2-4-4v-1h-1c-2-2-3-3-4-6-1-1-2-3-3-4z" class="G"></path><defs><linearGradient id="s" x1="255.506" y1="604.913" x2="282.916" y2="525.03" xlink:href="#B"><stop offset="0" stop-color="#c0bfbf"></stop><stop offset="1" stop-color="#f3f1ed"></stop></linearGradient></defs><path fill="url(#s)" d="M274 519c2 0 4 0 6 1 3 1 5 2 7 3 14 7 23 19 30 33 1 3 3 6 4 9v1c1 2 2 3 2 5 0 1 0 2 1 3v3 1c-1 0-1 0-2 1h0c0 2-2 4-4 5l-3 1c-6-17-16-32-33-40-12-6-26-6-39-2-10 4-19 13-23 23-2 4-3 8-4 12v1c-1 4-1 8 0 11-1 1-1 1-3 2-3-13-2-27 4-39 1-5 5-11 9-15 6-8 15-13 24-17 3 1 6 0 9-1h7l-1-1c1-1 6 0 9 0z"></path><path d="M321 566c1 2 2 3 2 5 0 1 0 2 1 3v3 1c-1 0-1 0-2 1h0c0-4-2-7-3-11h0l1 1 2-1-2-1 1-1z" class="B"></path><path d="M306 552c-7-5-13-9-21-12h1c6 2 13 3 19 7h1 1l1 1v1c-2-1-3-1-4-2h-1 0c1 2 1 2 1 3l1 1 1 1z" class="D"></path><path d="M226 538l1 1c14-9 31-14 48-10l2 1 4 1c-17-2-37-2-52 10-4 3-8 7-10 11l-2 1c1-5 5-11 9-15z" class="G"></path><defs><linearGradient id="t" x1="325.999" y1="554.547" x2="269.632" y2="533.6" xlink:href="#B"><stop offset="0" stop-color="#8a8a8a"></stop><stop offset="1" stop-color="#bdbcba"></stop></linearGradient></defs><path fill="url(#t)" d="M274 519c2 0 4 0 6 1 3 1 5 2 7 3 14 7 23 19 30 33 1 3 3 6 4 9v1l-1 1 2 1-2 1-1-1h0c-1-1-2-4-3-5-3-4-6-8-10-11l-1-1-1-1c0-1 0-1-1-3h0 1c1 1 2 1 4 2v-1l-1-1c-6-13-18-20-31-25-3-1-6-2-10-2l-1-1c1-1 6 0 9 0z"></path><path d="M274 519c2 0 4 0 6 1 3 1 5 2 7 3-2 1-4-1-6-2h-6 1v1c-3-1-6-2-10-2l-1-1c1-1 6 0 9 0z" class="C"></path><path d="M487 253c1-1 2-1 3-2v3 1c1 1 1 2 1 3l-1 1v2c0 1 0 3-1 4 1 2-2 10-3 12-1 1-1 2-2 3v-1l-1 5-1 2c0 2 0 3-1 4l2-13c-5 3-13 7-15 14-1 2-1 5 1 7l1 2c2 2 2 3 1 5-1 7-5 14-11 18-5 4-10 5-16 5h-4l-3-1c-3-1-4-2-6-3l-1-1c-2-1-4-3-5-4v-1c-2-1-2-3-3-4 0-1-1-1-1-1 0-1 0-2-1-2v-1c0-1-1-2-1-3v-1-3c-1-2 0-3 0-6v-1-3l1-1c0-1 0-1 1-2v-1c1-2 2-4 4-5h1v-1h-1c-2 0-3-1-4-1-3-1-5-2-7-4-1 0-1 0-2-1v-1c-1-1-2-1-2-2v-1c-1-1-1-1-1-2-1-3-2-4-1-7l1-1 1 1v1c1 1 2 2 2 3h0c2 1 3 2 5 3h1c1 1 0 1 1 1h1v1h-1c-5-2-8-4-10-8h-1c0 4 1 7 4 10 0 1 0 1 1 1 6 6 14 7 22 7 0-2 0-2 2-3h1c2 0 4 0 6-1 3-2 7-3 10-5 11-5 19-12 27-20h4 1l1-1z" class="P"></path><path d="M444 328c-4-1-7-2-10-3-7-4-12-10-14-17v-1c-1-3-1-7-1-11h1c0-4 2-8 5-11 1-1 2-1 4-1l-1 1c-1 1-2 2-3 4-3 5-4 12-3 18 2 7 7 12 13 15s12 3 18 1c7-2 12-8 14-14 1-2 1-5 2-7 1-1 1-1 0-1l1-1c2 2 2 3 1 5-1 7-5 14-11 18-5 4-10 5-16 5z" class="K"></path><defs><linearGradient id="u" x1="470.336" y1="293.901" x2="456.591" y2="268.777" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#efeeec"></stop></linearGradient></defs><path fill="url(#u)" d="M487 253c1-1 2-1 3-2v3 1c1 1 1 2 1 3l-1 1v2c0 1 0 3-1 4 1 2-2 10-3 12-1 1-1 2-2 3v-1l-1 5-1 2c0 2 0 3-1 4l2-13c-5 3-13 7-15 14-1 2-1 5 1 7l1 2-1 1c-1-2-2-3-4-4-2-4-3-8-3-12v-1c-7 4-16 6-23 11-2 4-3 7-2 10 1 4 3 4 5 7-2 0-4 0-6-2l-1-1c-1-1-2-3-3-5-2-4-2-11 0-15 1-2 3-3 4-5l-1-1c0-2 0-2 2-3h1c2 0 4 0 6-1 3-2 7-3 10-5 11-5 19-12 27-20h4 1l1-1z"></path><path d="M435 309c1-1 0-3-1-5v-5c0-5 3-7 6-10h1c-2 2-3 3-3 4l-1 1v1c-1 2-2 5-1 7v-2c0-1 0-2 1-3l1-1v-1h1c-2 4-3 7-2 10 1 4 3 4 5 7-2 0-4 0-6-2l-1-1z" class="F"></path><defs><linearGradient id="v" x1="477.989" y1="266.601" x2="463.188" y2="260.472" xlink:href="#B"><stop offset="0" stop-color="#0c0a0a"></stop><stop offset="1" stop-color="#363838"></stop></linearGradient></defs><path fill="url(#v)" d="M481 254h4 1c-12 12-25 21-42 25 3-2 7-3 10-5 11-5 19-12 27-20z"></path><defs><linearGradient id="w" x1="613.215" y1="859.479" x2="681.187" y2="765.286" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#bab9b8"></stop></linearGradient></defs><path fill="url(#w)" d="M544 772l5-2c1 1 1 3 2 4l5 9 3 6c3 5 6 11 9 17s6 11 10 17c2 0 3 1 4 3h0 1c1-1 2-2 2-3l-2-4v-1c-1-2-1-2-1-4v-2l1-3v-1c0-1 0-2 1-3 0 2 0 3 1 5 1 6 3 12 7 16 0-1 0-1-1-2l1-1c1-1 1-3 3-3 8 14 19 23 35 27 5 2 16 5 21 2 2-1 4-4 4-7v-1c1-1 1-3 2-4 5 3 9 5 15 7v1c3 1 4 1 8 0 0 1 2 2 2 2 3 0 6-2 8 0l1 1c1 1 1 0 3-1h-1v-2l1 1c1 0 3-1 4-2 2-1 4-3 6-4 2-2 3-4 5-5 2-2 6-2 8-3 5-3 10-6 14-10l1 1c1 1 3 1 4 0 1 0 2-2 2-2 4-3 7-7 12-8l-1 2-1 2c-4 1-5 4-8 6l-16 16-1 1c-1 0-1 1-2 2-1 0-2 1-3 2h0c-4 3-8 6-13 8-14 7-29 10-45 12h0c-3 1-8 2-10 0-4 1-7 1-10 0-18 0-39-9-51-22l-6-6c-8-8-15-17-19-26-3-5-4-10-7-15v1c-1-1-1-3-2-3-1-3-3-5-4-7 0-2-2-5-3-7-2-2-3-5-4-7z"></path><path d="M654 850c-1 2-2 3-4 4-6 5-14 4-22 2 1 0 1 0 1-1h0v-1c3 1 7 2 10 2 6 0 11-2 15-6z" class="T"></path><path d="M655 841c1-1 1-3 2-4 5 3 9 5 15 7v1l-3-1c-2 0-2 1-3 2l-2 3c-2-2-4-3-7-4-1 1-1 2-1 3s-1 2-1 3v-1h-1l1-2 1-1c0-2 0-4-1-6z" class="D"></path><path d="M544 772l5-2c1 1 1 3 2 4l5 9 3 6c0 1-1 1-1 2s3 8 3 9c1 1 1 2 2 3 3 3 5 9 7 13s5 8 8 11c2 3 5 6 8 9 0 1 2 2 3 4v2l-6-6c-8-8-15-17-19-26-3-5-4-10-7-15v1c-1-1-1-3-2-3-1-3-3-5-4-7 0-2-2-5-3-7-2-2-3-5-4-7z" class="B"></path><defs><linearGradient id="x" x1="601.028" y1="841.883" x2="609.448" y2="827.686" xlink:href="#B"><stop offset="0" stop-color="#010000"></stop><stop offset="1" stop-color="#2e2f2e"></stop></linearGradient></defs><path fill="url(#x)" d="M584 805c0 2 0 3 1 5 1 6 3 12 7 16 5 11 17 20 28 25l9 3v1h0c0 1 0 1-1 1-13-4-25-11-34-21h-2c-5-2-11-8-14-12 2 0 3 1 4 3h0 1c1-1 2-2 2-3l-2-4v-1c-1-2-1-2-1-4v-2l1-3v-1c0-1 0-2 1-3z"></path><path d="M582 814c3 8 7 14 12 21h-2c-5-2-11-8-14-12 2 0 3 1 4 3h0 1c1-1 2-2 2-3l-2-4v-1c-1-2-1-2-1-4z" class="D"></path><defs><linearGradient id="y" x1="672.664" y1="859.844" x2="669.111" y2="849.979" xlink:href="#B"><stop offset="0" stop-color="#424242"></stop><stop offset="1" stop-color="#5c5b5a"></stop></linearGradient></defs><path fill="url(#y)" d="M685 849h1l3 5c8-3 15-7 21-13l1 1c-1 3-5 5-7 6-1 1-2 2-3 2-11 6-22 8-34 11-3 1-6 2-8 2v1h1 0c-3 1-8 2-10 0l1-1 3-1c5-2 8-6 9-11l1-1c1 2 3 4 5 5 3 1 6 0 8-1l8-5z"></path><path d="M685 849c-1 3-3 4-5 5-1 1-2 0-3 0l8-5z" class="E"></path><defs><linearGradient id="z" x1="618.044" y1="850.493" x2="623.826" y2="827.501" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#bcbbba"></stop></linearGradient></defs><path fill="url(#z)" d="M592 826c0-1 0-1-1-2l1-1c1-1 1-3 3-3 8 14 19 23 35 27 5 2 16 5 21 2 2-1 4-4 4-7v-1c1 2 1 4 1 6l-1 1-1 2h0c-4 4-9 6-15 6-3 0-7-1-10-2l-9-3c-11-5-23-14-28-25z"></path><defs><linearGradient id="AA" x1="745.281" y1="559.011" x2="745.594" y2="451.932" xlink:href="#B"><stop offset="0" stop-color="#cecdcd"></stop><stop offset="1" stop-color="#f2f1ed"></stop></linearGradient></defs><path fill="url(#AA)" d="M696 485l18 26c-2-9-1-18-3-27-2-5-4-7-9-10l8-2c8-2 14-5 17-14 1-2 1-5 2-7h1v1c0 4 3 8 3 12 0 5-4 11-6 16-3 5-5 12-5 18l1 5v1c1-1 1-1 1-2v-5h1l3-13c4-12 13-25 24-30 8-4 18-4 26 0 9 3 16 10 20 19 2 4 2 7 2 11-2-5-3-10-8-13s-13-3-18-2c-11 3-19 12-24 21-7 13-9 27-10 41l2 13h-2l1 6c0 2 0 3 1 5l3 14c1 2 2 4 2 6l2 2c1 1 1 1 1 3 1 0 1 1 1 2 1 1 1 2 2 3l3 8c-2-1-3-2-3-4-1 0-1-1-1-2h-1-1c0-1-1-2-2-4h0c0-3-2-5-3-7-2-5-4-10-7-14l-1-2-4-8-3-4-2-3-1-1-2-2c0-4-2-6-3-10l-8-11-14-18c-2-2-4-5-6-7l-2-3-1-2c3-2 3-4 5-6z"></path><path d="M740 544c-1-4-1-10 0-13l2 13h-2z" class="I"></path><path d="M735 496c2 0 3 0 4 1l-2 8c-1-1-2-1-4-2 1-2 2-4 2-5v-2z" class="H"></path><defs><linearGradient id="AB" x1="746.478" y1="487.151" x2="742.707" y2="479.238" xlink:href="#B"><stop offset="0" stop-color="#595a5b"></stop><stop offset="1" stop-color="#7d7b7a"></stop></linearGradient></defs><path fill="url(#AB)" d="M736 496c7-15 15-26 31-31 2-1 5-2 7-1-4 1-9 2-13 4-7 4-13 12-17 18l-5 11c-1-1-2-1-4-1h1z"></path><path d="M733 503c2 1 3 1 4 2-2 12-3 23-3 35-3-3-6-7-7-11v-8c1-6 4-12 6-18z" class="T"></path><path d="M722 532c4 4 6 8 10 11l1 1h-1c-1 0-1 0-2-1 0-1-1-1-2-2v2c1 3 4 5 6 8 2 1 2 2 3 3 3 1 3 3 5 5h0v-1c-1-1-1-2-1-3v-1l-1-1-1-2c0-1-1-2 0-3l1 2h1c0 2 0 3 1 5l3 14c1 2 2 4 2 6l2 2c1 1 1 1 1 3 1 0 1 1 1 2 1 1 1 2 2 3l3 8c-2-1-3-2-3-4-1 0-1-1-1-2h-1-1c0-1-1-2-2-4h0c0-3-2-5-3-7-2-5-4-10-7-14l-1-2-4-8-3-4-2-3-1-1-2-2c0-4-2-6-3-10z" class="R"></path><path d="M747 575l2 2c1 1 1 1 1 3 1 0 1 1 1 2 1 1 1 2 2 3l3 8c-2-1-3-2-3-4-1 0-1-1-1-2h-1l-4-12z" class="J"></path><defs><linearGradient id="AC" x1="720.567" y1="522.501" x2="730.357" y2="503.174" xlink:href="#B"><stop offset="0" stop-color="#333334"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#AC)" d="M736 496h-1v2c0 1-1 3-2 5-2 6-5 12-6 18v8c-2-3-4-6-4-8-1-3-1-6-1-9v-14l1 5v1c1-1 1-1 1-2v-5h1c3 1 7-1 11-1z"></path><path d="M736 496h-1v2c-3 2-5 4-9 4-1 0-1-1-1-1l-1-4h1c3 1 7-1 11-1z" class="C"></path><defs><linearGradient id="AD" x1="695.578" y1="670.825" x2="769.176" y2="638.498" xlink:href="#B"><stop offset="0" stop-color="#c6c6c5"></stop><stop offset="1" stop-color="#f4f2f0"></stop></linearGradient></defs><path fill="url(#AD)" d="M687 486c1 1 3 3 4 5l1 2 2 3c2 2 4 5 6 7l14 18 8 11c1 4 3 6 3 10l2 2 1 1 2 3 3 4 4 8 1 2c3 4 5 9 7 14 1 2 3 4 3 7h0c1 5 3 9 5 13v2s0 1-1 1c7 11 11 24 15 36 5 17 8 34 10 51 3 35 1 69-10 103-1 4-2 9-4 13l-3 3c0-1-1-2-1-3-1-1-1-1-1-2 1-1 1-2 0-3v-1l-1 2c-2 1-5 0-7 0 16-28 22-60 22-92-1-20-4-40-10-59s-13-39-23-56c-15-26-33-48-54-69-3-2-6-4-8-7l2-7 3-8 3-8 2-6z"></path><path d="M758 796l1-1c1-3 2-7 4-10h0v5h-1c1 1 2 1 3 2v1c1-2 1-3 2-4-1 4-2 9-4 13l-3 3c0-1-1-2-1-3-1-1-1-1-1-2 1-1 1-2 0-3v-1z" class="F"></path><path d="M714 521l8 11c1 4 3 6 3 10l2 2 1 1 2 3 3 4 4 8 1 2c3 4 5 9 7 14 1 2 3 4 3 7h0c1 5 3 9 5 13v2s0 1-1 1c-6-12-13-24-20-35-7-10-14-19-22-28l1-1c0-1-1-2-2-3 2 0 2 2 3 2h3 2l1-2-2-1c0-2-1-3-3-4l1-1c1 1 2 2 3 2h0l-3-4c-1-1 0-1 0-3z" class="I"></path><defs><linearGradient id="AE" x1="691.148" y1="493.321" x2="704.913" y2="535.315" xlink:href="#B"><stop offset="0" stop-color="#373636"></stop><stop offset="1" stop-color="#9fa09f"></stop></linearGradient></defs><path fill="url(#AE)" d="M687 486c1 1 3 3 4 5l1 2 2 3c2 2 4 5 6 7l14 18c0 2-1 2 0 3l3 4h0c-1 0-2-1-3-2l-1 1c2 1 3 2 3 4l2 1-1 2h-2-3c-1 0-1-2-3-2 1 1 2 2 2 3l-1 1c-1-1-2-3-3-4-5-6-12-12-18-17-2 1-3 3-4 5v2c-3-2-6-4-8-7l2-7 3-8 3-8 2-6z"></path><path d="M687 486c1 1 3 3 4 5l1 2h0c-1-1-2-2-4-2h0l-1-1-1 1v1c1 1 2 1 2 3h1l1 2v2h-1c-2-3-3-4-4-7l2-6z" class="H"></path><path d="M713 527c0-1-3-3-4-4 0-2-2-3-3-4-4-5-7-9-10-14-1-1-3-3-4-5 0-2-1-4 0-5 1 1 1 1 2 1h0c2 2 4 5 6 7l14 18c0 2-1 2 0 3l3 4h0c-1 0-2-1-3-2l-1 1z" class="G"></path><path d="M682 500c5 3 7 8 11 12l14 19v1c-5-6-12-12-18-17-2 1-3 3-4 5v2c-3-2-6-4-8-7l2-7 3-8z" class="T"></path><path d="M677 515l2-7c4 2 7 4 10 7-2 1-3 3-4 5v2c-3-2-6-4-8-7z" class="B"></path><path d="M677 515c2 3 5 5 8 7 21 21 39 43 54 69 10 17 17 37 23 56s9 39 10 59c0 32-6 64-22 92 2 0 5 1 7 0v1l-1 1c0 1 0 2-1 3l-4 8c0 1-1 1-1 2h0c-5 1-8 5-12 8 0 0-1 2-2 2-1 1-3 1-4 0l-1-1c-4 4-9 7-14 10-2 1-6 1-8 3-2 1-3 3-5 5-2 1-4 3-6 4-1 1-3 2-4 2l-1-1c2-6 3-12 3-18 1-2 0-3 1-4 10-1 20-4 28-10 23-16 33-46 38-73 7-44-5-96-30-132h0c-5-7-12-14-18-20-3-4-8-8-12-11-3-1-10-6-14-6l-2-1c-1 0-1 0-2-1-2-1-4-2-5-4l1-3h0l2-1c-2-1-3-2-4-4v-1c2-1 2-2 3-4-1-1-1-3-1-4l-1-2h0l1-1h-1c-1-1-2-2-2-3l-3-3c-1-2-1-3 0-4l-2-2c-1 1-1 2-2 3h0-1l3-8 4-13z" class="P"></path><path d="M673 528c3 1 5 2 8 4h0c-1 3-1 4 1 6h0v1l-7-4-2-2c-1 1-1 2-2 3h0-1l3-8z" class="S"></path><path d="M681 532c8 7 15 14 23 22 1 2 4 4 5 7-4-2-8-6-12-9-5-5-11-8-15-13v-1h0c-2-2-2-3-1-6z" class="B"></path><path d="M731 822c7-7 13-15 19-24 2 0 5 1 7 0v1l-1 1c0 1 0 2-1 3l-4 8c0 1-1 1-1 2h0c-5 1-8 5-12 8 0 0-1 2-2 2-1 1-3 1-4 0l-1-1z" class="F"></path><defs><linearGradient id="AF" x1="701.067" y1="568.547" x2="707.726" y2="559.558" xlink:href="#B"><stop offset="0" stop-color="#9b9b9b"></stop><stop offset="1" stop-color="#bab8b7"></stop></linearGradient></defs><path fill="url(#AF)" d="M675 535l7 4c4 5 10 8 15 13 4 3 8 7 12 9 10 10 17 22 24 34-11-12-24-25-37-35h0-1c-1-1-2-2-2-3-2-2-4-4-6-5l-2-2-1-1-1-1c-1-1-2-1-3-2l1-1h-1c-1-1-2-2-2-3l-3-3c-1-2-1-3 0-4z"></path><path d="M681 548l-1-2h0c1 1 2 1 3 2l1 1 1 1 2 2c2 1 4 3 6 5 0 1 1 2 2 3h1l3 3c5 4 10 10 15 14 6 7 13 13 18 20 18 23 30 54 34 83 7 42 1 90-24 125-9 12-22 20-38 23-2 0-5 1-7 0l-1-1c1-2 0-3 1-4 10-1 20-4 28-10 23-16 33-46 38-73 7-44-5-96-30-132h0c-5-7-12-14-18-20-3-4-8-8-12-11-3-1-10-6-14-6l-2-1c-1 0-1 0-2-1-2-1-4-2-5-4l1-3h0l2-1c-2-1-3-2-4-4v-1c2-1 2-2 3-4-1-1-1-3-1-4z" class="O"></path><path d="M681 548l-1-2h0c1 1 2 1 3 2l1 1 1 1 2 2c2 1 4 3 6 5 0 1 1 2 2 3h1l3 3h-1c-3-1-5-4-7-6h0c0 4 4 5 6 8 0 1 2 2 3 3l7 7v1c-1-1-2-2-3-2h0l1 1-3-2-1 1c1 1 2 2 2 3-3-1-10-6-14-6l-2-1c-1 0-1 0-2-1-2-1-4-2-5-4l1-3h0l2-1c-2-1-3-2-4-4v-1c2-1 2-2 3-4-1-1-1-3-1-4z" class="D"></path><path d="M681 548c2 3 4 6 7 9 2 3 5 6 8 9l-7-3c-1-1-2-1-2-2-2 1-4 1-6 1h0l2-1c-2-1-3-2-4-4v-1c2-1 2-2 3-4-1-1-1-3-1-4z" class="B"></path><path d="M497 241l1-1c0-2 2-4 5-5l-3 8c0 1-1 3-1 4-1 2-1 3-2 4 0 1-1 2-2 4-6 19-10 40-10 60v12l1 1 2-2s0-1 1-2l1 17h1l3 18c5 23 12 46 25 66l-1 3c2 2 10 13 12 14 1 0 1 0 2 1v-1c0-1-1-1-1-2v-1l10 8 7 6c1 1 2 1 3 1 3 4 7 8 11 12 10 10 21 19 32 28-1-1-2-1-3-2-4-2-7-3-10-5-1-1-2-1-3-2h-9c-2 0-5 1-7 0h-1c-1 1-1 0-2 1h-1-1 0c-1 1-2 1-2 1-1 1-1 0-1 1l-2-2c-3 1-5 1-7 0l-1-2c-2-1-4-5-6-7-1 0-1-1-2-1l1 1c0 1 0 1 1 2v2l-2 1h0-2v3l-1 1-6-7h-1c-3-3-6-7-8-11-12-17-19-36-25-56h0l-2-1h-2 0c-2-3-3-5-3-8-1 3-3 5-5 7l-1 1h0c-1 1-1 7-1 9h-2l-2-1c0-2 0-4-1-6h0l-1-20c-1-34 1-69 8-103 1-1 1-2 1-4l1-2 1-5v1c1-1 1-2 2-3 1-2 4-10 3-12 1-1 1-3 1-4v-2l1-1c0-1 0-2-1-3v-1-3c-1 1-2 1-3 2 3-4 7-9 10-12z" class="O"></path><path d="M553 483c1-2 1-3 2-4h1l2 2c-1 0-2 1-3 1 0 1-1 1-2 1z" class="L"></path><path d="M553 483c1 0 2 0 2-1 1 0 2-1 3-1v1c-1 2-3 3-5 4l-1-1c0-1 0-1 1-2z" class="N"></path><path d="M481 366c0-4-1-9 0-13 2 5 3 10 3 15-1 2-1 3-2 5l-1-7z" class="G"></path><path d="M497 241l1-1c0-2 2-4 5-5l-3 8c0 1-1 3-1 4-1 2-1 3-2 4 0 1-1 2-2 4 0-3 0-7 1-10 1-1 1-3 1-4z" class="H"></path><path d="M485 315v12l1 1 2-2-1 14v8 1c-1-2-1-5-1-8-1-8-2-18-1-26z" class="Q"></path><path d="M511 435c0-2-2-6-3-9 0-2 1-7 2-9l1 1v1c-1 10 3 15 7 24 0 1 0 3 1 4v1 1c-3-4-6-9-8-14z" class="J"></path><path d="M488 326s0-1 1-2l1 17c0 2 1 4 0 6h-1c-1 4 0 8 0 11v3c1 3 1 4 0 6-1-6-3-12-2-18v-1-8l1-14zm30 117c2 3 4 7 7 10 6 8 13 14 20 21-3-1-5-3-8-5-7-6-13-12-18-20v-1-1c-1-1-1-3-1-4z" class="C"></path><path d="M481 366l1 7 3 22c0 2 0 7 1 8-1 3-3 5-5 7-1-3-1-8-2-12l-1 9h0v-4-4c-1-1-1-3-1-5-1-1 0-7 0-9v-12h0l1 12c-1 2-1 4 0 6v3l1 1 1 3h0c2 2 3 4 3 6v1h1c1-2 1-5 0-8v-3-1-1l-1-4v-2c0-1 0-1-1-2 1-1 1-2 0-2v-1-3h-1c0-1 1-2 0-3v-2-2-5z" class="F"></path><path d="M473 393c1 3 2 5 2 8l3 2v4h0l1-9c1 4 1 9 2 12l-1 1h0c-1 1-1 7-1 9h-2l-2-1c0-2 0-4-1-6h0l-1-20z" class="D"></path><path d="M489 367c1-2 1-3 0-6v-3c0-3-1-7 0-11h1l3 15c0 1 1 4 1 5-1 1-1 2-2 3h1c-2 2 0 4-2 6h1c-1 4 0 9 1 13 0 2 2 7 1 8-2-7-3-14-4-21-1-3-1-6-1-9z" class="F"></path><defs><linearGradient id="AG" x1="496.301" y1="392.428" x2="478.392" y2="387.534" xlink:href="#B"><stop offset="0" stop-color="#737474"></stop><stop offset="1" stop-color="#959493"></stop></linearGradient></defs><path fill="url(#AG)" d="M482 373c1-2 1-3 2-5l9 44h0l-2-1h-2 0c-2-3-3-5-3-8-1-1-1-6-1-8l-3-22z"></path><defs><linearGradient id="AH" x1="557.082" y1="484.756" x2="560.5" y2="445.295" xlink:href="#B"><stop offset="0" stop-color="#444645"></stop><stop offset="1" stop-color="#636061"></stop></linearGradient></defs><path fill="url(#AH)" d="M531 439l10 8 7 6c1 1 2 1 3 1 3 4 7 8 11 12 10 10 21 19 32 28-1-1-2-1-3-2-4-2-7-3-10-5-1-1-2-1-3-2-5-5-11-9-16-13-12-9-23-19-32-30 1 0 1 0 2 1v-1c0-1-1-1-1-2v-1z"></path><defs><linearGradient id="AI" x1="510.086" y1="380.242" x2="499.664" y2="422.306" xlink:href="#B"><stop offset="0" stop-color="#393938"></stop><stop offset="1" stop-color="#5a5959"></stop></linearGradient></defs><path fill="url(#AI)" d="M491 341l3 18c5 23 12 46 25 66l-1 3-7-10-1-1c-1 2-2 7-2 9 1 3 3 7 3 9-4-7-7-15-9-23-2-9-3-17-4-26l-4-19c0-1-1-4-1-5l-3-15c1-2 0-4 0-6h1z"></path><path d="M546 163c1 0 2 1 2 0 4 0 8-4 11-6 4-1 8-3 11-4 4-1 13-1 16 0-3 2-7 3-10 5 0 0 0 1-1 1l11-4c1 0 3-1 4-1h1c1 1 2 0 3 0h2v3 2c-2 2-4 2-7 3-1 1-2 1-3 1-1 1-2 2-2 3-8 4-15 7-21 12-1 1-3 2-3 3-5 4-10 8-14 12-23 23-36 54-42 85-3 16-4 32-3 48l1 17c2 26 11 53 23 76 1 4 3 7 6 11l7 11c1 2 3 4 3 6l-10-8v1c0 1 1 1 1 2v1c-1-1-1-1-2-1-2-1-10-12-12-14l1-3c-13-20-20-43-25-66l-3-18h-1l-1-17c-1 1-1 2-1 2l-2 2-1-1v-12c0-20 4-41 10-60 1-2 2-3 2-4 1-1 1-2 2-4 0-1 1-3 1-4l3-8c-3 1-5 3-5 5l-1 1c-3 3-7 8-10 12l-1 1h-1-4c-8 8-16 15-27 20-3 2-7 3-10 5-2 1-4 1-6 1h-1c-2 1-2 1-2 3-8 0-16-1-22-7-1 0-1 0-1-1-3-3-4-6-4-10h1c2 4 5 6 10 8h1 0c2 1 5 2 8 2 9 0 18-4 26-8 23-13 38-35 53-56 7-9 13-18 21-25 4-4 6-7 12-7-1 0-1 0-2-1l-1 1-1-1h-1c-5 1-12-1-15-4-3-2-4-4-5-7h1c2 2 3 4 6 4 9 2 17-3 24-8z" class="M"></path><path d="M519 425c2 3 5 6 7 9l5 5v1c0 1 1 1 1 2v1c-1-1-1-1-2-1-2-1-10-12-12-14l1-3z" class="B"></path><path d="M413 276c1 0 2 1 3 1v-1c3 1 7 3 11 3 3 1 7 0 10 0 1 0 3-1 3 0l-3 1c-2 1-2 1-2 3-8 0-16-1-22-7z" class="L"></path><defs><linearGradient id="AJ" x1="545.124" y1="179.759" x2="548.376" y2="164.741" xlink:href="#B"><stop offset="0" stop-color="#0f0d11"></stop><stop offset="1" stop-color="#2b2f29"></stop></linearGradient></defs><path fill="url(#AJ)" d="M536 178c11-5 19-14 30-18-4 4-8 7-12 10-5 4-9 7-14 10v-1c-1 0-1 0-2-1l-1 1-1-1z"></path><path d="M481 254l31-37h0l-9 18c-3 1-5 3-5 5l-1 1c-3 3-7 8-10 12l-1 1h-1-4zm18-7l9-18v1c-11 25-17 54-18 82 0 10 0 19 1 29h-1l-1-17c-1 1-1 2-1 2l-2 2-1-1v-12c0-20 4-41 10-60 1-2 2-3 2-4 1-1 1-2 2-4z" class="P"></path><path d="M498 686c1 0 1-4 1-5 0 3 0 6 1 9v1c1 3 0 6 0 9 0 2 1 3 0 4v1l-1 1v5 12 20c0 4 0 9 1 13l-1 1c1 5 1 12 0 17l1 1c0 1 0 2-1 3 1 2 1 6 1 8 1 6 1 12 0 18-4 1-9 8-12 12-2 5-5 11-5 17-1-2-1-4-2-6h-1c0-3-3-8-4-11-2-3-3-7-4-10v-1c-1-3-1-7-2-9 0-3 2-7 1-9-2-1-3-1-5-1-4 2-6 5-8 9-2 3-3 6-4 9 0 5-1 9 0 13 0 5 1 11 3 16l2 8h-1c0 1 0 1-1 2l-1-2-1-3c-1-1-1-1-1-2l-1 1-1-5c0-2 0-3-1-5v5c1 4 1 10-2 13h-1-1c3-9 1-17-1-25v-5s-1-6-1-8c-1-4-4-9-5-13l-1-4 3-3c1-2 2-6 1-7l-1-4h1v-4-1c1 1 1 2 2 3 0-2 1-5 2-7 1-1 2-3 2-4 1-2-8-11-9-15 0-4 3-4 4-6v-4c1-6 1-13 5-18 1 0 1-1 2-1 0 1 1 3 1 3 1 1 3 1 4 1l2-1v1c1-1 2-2 3-2l1-1 7-9v-1c2-8 11-16 17-20 2 1 4 2 7 2 2 0 4-2 5-4v-2z" class="M"></path><path d="M478 743l-9 1c2-1 4-3 6-4l6 1-1 2h-2z" class="C"></path><path d="M477 776c1 1 2 1 3 2l-1 1c-5 0-10-1-14-1l-1 1c-2 0-4 1-5 2v-1c4-4 12-3 17-4h1z" class="I"></path><path d="M499 774l1 1c0 1 0 2-1 3-5 8-9 17-17 23 2-6 6-10 10-15 2-4 5-8 7-12z" class="Q"></path><path d="M498 686c1 0 1-4 1-5 0 3 0 6 1 9v1c1 3 0 6 0 9 0 2 1 3 0 4v1l-1 1c-3 2-5 5-7 7-2 3-4 6-5 9l-1-2c2-7 7-13 13-18h0v-1c1-3 1-11-1-13v-2z" class="L"></path><path d="M469 711c3-3 5-6 10-7h0c-2 3-4 5-6 8-5 4-9 9-11 15l-1-1c0-2 0-3 1-6l7-9z" class="G"></path><path d="M454 817c0 5 1 11 3 16l2 8h-1c0 1 0 1-1 2l-1-2-1-3c-1-1-1-1-1-2l-1 1-1-5c0-2 0-3-1-5h0c-1-1-1 0-1-1v-3c1 0 1 1 1 1l1 1h0c0-1 0-2 1-2 1-1 0-4 1-6z" class="L"></path><path d="M499 706v5l-10 14c0 1-1 2-1 3l-1-1c-1-2-1-3 0-5 1-3 3-6 5-9 2-2 4-5 7-7z" class="B"></path><path d="M492 713c0 2 0 3-1 5s-3 4-2 7c0 1-1 2-1 3l-1-1c-1-2-1-3 0-5 1-3 3-6 5-9z" class="E"></path><path d="M475 728c4 0 9 0 12 4 1 0 2 1 2 2-3 0-6 0-9-1-4 0-7 1-10 2h-3c0-1 0-2 1-3 1-3 4-3 7-4z" class="R"></path><path d="M500 786c1 6 1 12 0 18-4 1-9 8-12 12-2 5-5 11-5 17-1-2-1-4-2-6l1-4c3-9 9-19 18-23h0v-14z" class="I"></path><defs><linearGradient id="AK" x1="495.535" y1="713.708" x2="492.29" y2="735.523" xlink:href="#B"><stop offset="0" stop-color="#767675"></stop><stop offset="1" stop-color="#aaaaa9"></stop></linearGradient></defs><path fill="url(#AK)" d="M499 711v12h-1c0 3 1 11-1 14l-1 1-1-1h0c-2-3-5-6-7-9 0-1 1-2 1-3l10-14z"></path><defs><linearGradient id="AL" x1="500.813" y1="750.26" x2="484.633" y2="736.603" xlink:href="#B"><stop offset="0" stop-color="#5d5d5d"></stop><stop offset="1" stop-color="#8c8b8a"></stop></linearGradient></defs><path fill="url(#AL)" d="M496 738l1-1c2-3 1-11 1-14h1v20c0 4 0 9 1 13l-1 1c0-2-2-4-3-6-4-5-11-7-18-8h2l1-2-6-1c4-1 8 0 12 1 1 0 3 1 5 0 2 0 3-1 4-3z"></path><path d="M467 763c1-1 2-3 4-3h2c1 0 2-1 2-2 1-2 1-2 2-3 3 0 6 1 8 4 3 3 4 5 4 9-4 3-11-4-13 5 0 1 0 2 1 3h-1c-5 1-13 0-17 4v1c-2 1-4 4-7 5l3-7s-1 0-2 1h-2l2-4 1-1c2-5 6-9 10-13l3 1z" class="F"></path><path d="M464 762l3 1v1c-5 4-8 9-12 15 0 0-1 0-2 1h-2l2-4 1-1c2-5 6-9 10-13z" class="Q"></path><defs><linearGradient id="AM" x1="463.948" y1="757.124" x2="436.927" y2="738.924" xlink:href="#B"><stop offset="0" stop-color="#818180"></stop><stop offset="1" stop-color="#c0bfbe"></stop></linearGradient></defs><path fill="url(#AM)" d="M449 720c1 0 1-1 2-1 0 1 1 3 1 3 1 1 3 1 4 1l2-1v1c1-1 2-2 3-2l1-1c-1 3-1 4-1 6l1 1c-4 8-5 16-6 25l1 2c1 4 3 6 6 8h1c-4 4-8 8-10 13l-1 1-2 4h2c1-1 2-1 2-1l-3 7c-4 10-6 18-6 29 0 0-1-6-1-8-1-4-4-9-5-13l-1-4 3-3c1-2 2-6 1-7l-1-4h1v-4-1c1 1 1 2 2 3 0-2 1-5 2-7 1-1 2-3 2-4 1-2-8-11-9-15 0-4 3-4 4-6v-4c1-6 1-13 5-18z"></path><path d="M457 754l-9-3 8-5v6l1 2z" class="S"></path><path d="M463 762h1c-4 4-8 8-10 13l-1 1c-1 0-2 1-3 1v-2l3-6c2-3 6-6 10-7z" class="J"></path><path d="M445 778c-1-1-1-1-1-2v-1l1 1h1s0 1 1 2c1-1 2-1 3-3v2c1 0 2-1 3-1l-2 4h2c1-1 2-1 2-1l-3 7c-4 10-6 18-6 29 0 0-1-6-1-8-1-4-4-9-5-13h0l1-1s1-1 1-2l2-2 1-3v-3-1-4z" class="H"></path><defs><linearGradient id="AN" x1="451.212" y1="783.926" x2="446.788" y2="777.074" xlink:href="#B"><stop offset="0" stop-color="#6b6a6c"></stop><stop offset="1" stop-color="#828181"></stop></linearGradient></defs><path fill="url(#AN)" d="M445 778c-1-1-1-1-1-2v-1l1 1h1s0 1 1 2c1-1 2-1 3-3v2c1 0 2-1 3-1l-2 4c0 3-2 7-3 9-1-3-1-8-3-11h0z"></path><path d="M445 783c1 3 2 6 2 9 1 5-1 11-2 15-1-4-4-9-5-13h0l1-1s1-1 1-2l2-2 1-3v-3z" class="J"></path><path d="M576 187l1 1 13-5 1 1c-1 1-1 1-2 1l-1 1-5 2-2 1c-2 1-3 1-5 3-4 3-8 5-12 8-11 9-21 20-27 33-2 4-4 9-5 13-4 15-6 29-7 44-1 10 0 19 0 29 1 9 2 17 4 26 10 43 33 89 69 116l3 2c4 5 11 8 15 12v1c2 2 8 6 10 7 0 2-1 4-2 6-2 0-4-2-5-3h-1c-1-1-2-1-3-1 2 1 3 1 4 2l6 4c0 1 1 1 2 2h0v1c1 0 1 0 2 1h1c2 1 3 2 5 3-1 1-1 2-3 2-1 0-1 0-2 1 2 2 6 3 9 5 2 1 3 1 5 1l-2 6c-1-1-3-2-4-2-4-2-7-4-10-6l-29-19v1c1 2 4 3 5 4v1c-1 1-2 3-3 4v1c-2-1-3-2-5-3h0-2c-11-9-22-18-32-28-4-4-8-8-11-12-1 0-2 0-3-1l-7-6c0-2-2-4-3-6l-7-11c-3-4-5-7-6-11-12-23-21-50-23-76h1v-2l-1-2v-13c1-24 6-49 15-72 5-12 10-23 19-33 10-14 24-26 40-34z" class="M"></path><path d="M538 417c8 12 15 23 24 34 1 1 2 4 4 5l7 8c1 0 2 2 3 3l-1 1 2 2v1l-9-9c-12-12-23-28-31-44v-1h1 0z" class="R"></path><path d="M525 319c-2-3 0-6-1-10h-1c0-2 1-4 0-5 0-3 0-6 1-9v-9 1c-1 2-1 5-1 7v14 4c-1 2-1 2-2 3 0-3 0-6-1-10 0-14 1-26 5-40 1-1 1-1 1-2v-2c1-2 1-3 1-5l2-6c1-3 1-3 3-4-4 15-6 29-7 44-1 10 0 19 0 29z" class="N"></path><path d="M538 417c-9-16-15-34-19-52-2-7-3-14-4-22h0c1 1 1 2 1 3l3 16c7 27 20 55 38 77l12 17h0c-3-1-4-4-5-6-1-1-2-2-3-2 0 2 4 6 5 7v1c-2-1-3-4-4-5-9-11-16-22-24-34z" class="K"></path><defs><linearGradient id="AO" x1="585.253" y1="481.26" x2="609.747" y2="466.74" xlink:href="#B"><stop offset="0" stop-color="#9a9b9a"></stop><stop offset="1" stop-color="#b8b5b4"></stop></linearGradient></defs><path fill="url(#AO)" d="M557 439c1 1 2 1 3 2l6 6c3 1 6 5 9 8 2 2 5 4 8 6 7 7 16 13 24 18 3 2 6 4 8 6 2 1 3 1 4 2l6 4c0 1 1 1 2 2h0v1c1 0 1 0 2 1h1c2 1 3 2 5 3-1 1-1 2-3 2-1 0-1 0-2 1 2 2 6 3 9 5 2 1 3 1 5 1l-2 6c-1-1-3-2-4-2-4-2-7-4-10-6l-29-19c-1 0-4-3-5-3-6-5-13-10-18-16-1-1-2-3-3-3l-7-8v-1c-1-1-5-5-5-7 1 0 2 1 3 2 1 2 2 5 5 6h0l-12-17z"></path><path d="M566 456v-1c-1-1-5-5-5-7 1 0 2 1 3 2 1 2 2 5 5 6h0c4 3 7 7 10 10 16 14 33 25 51 35 2 2 6 3 9 5 2 1 3 1 5 1l-2 6c-1-1-3-2-4-2-4-2-7-4-10-6l-29-19c-1 0-4-3-5-3-6-5-13-10-18-16-1-1-2-3-3-3l-7-8z" class="P"></path><defs><linearGradient id="AP" x1="547.569" y1="341.604" x2="502.806" y2="341.163" xlink:href="#B"><stop offset="0" stop-color="#8c8c8b"></stop><stop offset="1" stop-color="#afaead"></stop></linearGradient></defs><path fill="url(#AP)" d="M502 343h1v-2l-1-2v-13c1-24 6-49 15-72 5-12 10-23 19-33v1c0 3-4 6-5 9-2 4-4 8-5 11l-6 15c-4 12-7 24-9 37-4 26-4 53 2 79 6 29 20 57 38 81-1 0-2 0-3-1l-7-6c0-2-2-4-3-6l-7-11c-3-4-5-7-6-11-12-23-21-50-23-76z"></path><path d="M539 248h1c1-4 3-12 6-14h1l-1 1v1 1 1h-1v2c0 1 0 1-1 2v1 1c0 1 0 1-1 2v4l-3 23c-3 24-2 49 7 72 3 7 6 14 11 20 6 9 15 16 26 18 8 1 16-2 22-7 6-4 10-11 11-18 1-9-5-21-10-28-4-5-9-8-15-8-4-1-7 1-10 3h-1c0-1 0-2 1-3v-3-1c0-4-1-9-1-13-1-13 2-26 11-35 5-5 11-8 18-8 2 1 3 1 5 1-7 1-12 3-16 9-5 6-5 14-3 22 1 7 4 14 11 18 1 1 2 1 3 1h1c-1-5-6-7-5-13h0c1 1 2 3 4 4 4 4 9 7 14 11 11 11 19 26 22 41 1 5 1 10 2 15l3-3c1 6-4 10-4 15h1c0 1 0 4-1 5 0 4-2 9-5 12-3 5-8 8-13 10 0 1 0 2-1 3-4 2-13 4-17 4-1 1-2 2-2 3h-4l35 29 17 15h1l-2 9-6 17-1 3-1 4-1 1-3 9c-2 0-3 0-5-1-3-2-7-3-9-5 1-1 1-1 2-1 2 0 2-1 3-2-2-1-3-2-5-3h-1c-1-1-1-1-2-1v-1h0c-1-1-2-1-2-2l-6-4c-1-1-2-1-4-2 1 0 2 0 3 1h1c1 1 3 3 5 3 1-2 2-4 2-6-2-1-8-5-10-7v-1c-4-4-11-7-15-12h0c1-1 0-2-1-3-6-5-12-11-17-17-16-17-28-36-37-58-16-35-19-73-14-111 2-8 3-18 7-26z" class="O"></path><path d="M584 316c2-1 4-3 7-3l1 1-1 2h1v2c-4 1-7 1-10 4v-3c1-1 1-2 2-3z" class="H"></path><path d="M585 310l4-3 4 4 5-1 1 2c-3 1-5 0-7 2l-1-1c-3 0-5 2-7 3v-1l1-5z" class="Q"></path><path d="M628 353h2l2-2c0-6 0-11-2-17h0c3 5 4 10 5 16 1 3 1 6 1 9 1 3 2 5 1 8l-1-4-1-8c-1 2-4 5-5 6v4c0 5 0 9-1 13-1-3 1-7-1-11v-14z" class="B"></path><defs><linearGradient id="AQ" x1="589.742" y1="303.697" x2="585.945" y2="288.123" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#898989"></stop></linearGradient></defs><path fill="url(#AQ)" d="M585 309c0-10 1-17 5-26l1-3c-1 2-1 5-1 7-1 8 3 16 7 22l1 1-5 1-4-4-4 3v-1z"></path><path d="M585 309c1-2 2-4 3-5h1 0 1c1 2 4 5 7 5h0l1 1-5 1-4-4-4 3v-1zm43 58c2 4 0 8 1 11 1 6-1 14-3 20l1 1c2 1 4 1 6 0 6-2 12-11 14-16h1c0 1 0 4-1 5 0 4-2 9-5 12-1 1-3 2-5 3h0l-5 2c-3 0-6 0-8-1-2-2-2-4-2-6 0-3 2-7 3-10 2-7 3-13 3-21z" class="E"></path><path d="M647 383h1c0 1 0 4-1 5h-1 0c-1 3-1 7-4 8-2 2-4 2-5 3-3 2-5 4-9 4l-2-2v-3l1 1c2 1 4 1 6 0 6-2 12-11 14-16z" class="B"></path><defs><linearGradient id="AR" x1="647.687" y1="493.317" x2="611.096" y2="472.056" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#30302f"></stop></linearGradient></defs><path fill="url(#AR)" d="M600 460l24 17h1c2 1 2 1 3 2l2 1h0c1 0 1 1 2 1s1-1 2-1c-3-2-5-3-7-6h0c2 1 5 4 7 5l3 2 4 3h1c3 2 5 4 8 6l-1 3-1 4-1-1c-3-1-6-4-9-6-7-5-14-10-22-15-4-4-11-7-15-12h0c1-1 0-2-1-3z"></path><path d="M641 484h1c3 2 5 4 8 6l-1 3c-3-2-7-4-10-6 1-1 2-2 2-3z" class="C"></path><path d="M627 474c2 1 5 4 7 5l3 2 4 3c0 1-1 2-2 3l-15-10h1c2 1 2 1 3 2l2 1h0c1 0 1 1 2 1s1-1 2-1c-3-2-5-3-7-6h0z" class="R"></path><defs><linearGradient id="AS" x1="639.264" y1="493.059" x2="624.612" y2="486.868" xlink:href="#B"><stop offset="0" stop-color="#919090"></stop><stop offset="1" stop-color="#c3c2c1"></stop></linearGradient></defs><path fill="url(#AS)" d="M616 475c8 5 15 10 22 15 3 2 6 5 9 6l1 1-1 1-3 9c-2 0-3 0-5-1-3-2-7-3-9-5 1-1 1-1 2-1 2 0 2-1 3-2-2-1-3-2-5-3h-1c-1-1-1-1-2-1v-1h0c-1-1-2-1-2-2l-6-4c-1-1-2-1-4-2 1 0 2 0 3 1h1c1 1 3 3 5 3 1-2 2-4 2-6-2-1-8-5-10-7v-1z"></path><path d="M630 495c4 1 8 3 12 4 2 0 3 0 5-1l-3 9c-2 0-3 0-5-1h1c2-1 2-1 3-3 0-1 0-1-1-1-1-1-5-4-7-4-2-1-3-2-5-3z" class="H"></path><path d="M635 498c2 0 6 3 7 4 1 0 1 0 1 1-1 2-1 2-3 3h-1c-3-2-7-3-9-5 1-1 1-1 2-1 2 0 2-1 3-2z" class="K"></path><defs><linearGradient id="AT" x1="593.802" y1="374.042" x2="635.531" y2="372.708" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#262626"></stop></linearGradient></defs><path fill="url(#AT)" d="M600 416l-7-5c7-2 14-4 19-10 8-8 12-21 12-33 0-15-4-30-14-43-2-3-5-5-8-8v-1c13 8 20 23 26 37v14c0 8-1 14-3 21-1 3-3 7-3 10 0 2 0 4 2 6 2 1 5 1 8 1l5-2h0c2-1 4-2 5-3-3 5-8 8-13 10 0 1 0 2-1 3-4 2-13 4-17 4-1 1-2 2-2 3h-4l-5-4z"></path><path d="M600 416h1c10 0 18-2 28-6 0 1 0 2-1 3-4 2-13 4-17 4-1 1-2 2-2 3h-4l-5-4z" class="B"></path><path d="M580 429c-17-21-29-44-37-70-5-15-9-30-9-46h0l3 17c4 14 9 28 18 40 7 9 17 15 29 16 9 1 17 0 24-6s9-12 10-21c2 3 1 10 1 14v1c-2 8-4 15-11 20l-1 1c-8 6-22 3-31 2l-11-3 1 1c2 2 3 3 3 6h1c2 2 3 3 6 3 0 1 3 1 4 2v1 4c0 2 1 4 2 5 4 6 38 27 38 28l8 5c1 0 3 2 4 2s2 1 3 2h1l4-4 17 15h1l-2 9-6 17c-3-2-5-4-8-6h-1l-4-3-3-2c-2-1-5-4-7-5-6-4-12-9-17-14-2-2-4-3-5-5l-2-2a57.31 57.31 0 0 1-11-11c-4-4-8-8-12-13z" class="P"></path><path d="M561 393c3 2 5 6 8 8h1c2 2 3 3 6 3 0 1 3 1 4 2v1 4c-1-1-1-2-1-3h-1c0 1 0 1-1 2l-1-1c-1 0-2 0-3-1s-2-3-4-3l3 3h-1c-1-1-3-3-4-5s-3-4-4-7l-2-3z" class="K"></path><path d="M611 385h1c1-2 3-4 4-6-1 3-2 6-4 9-2 2-4 4-7 6l2 1c-8 6-22 3-31 2l-11-3 1 1c2 2 3 3 3 6-3-2-5-6-8-8l-5-9c1 0 4-1 5-1 2 0 5 2 7 3 8 2 15 4 22 4 8 0 14-3 21-5z" class="C"></path><path d="M611 385h1c1-2 3-4 4-6-1 3-2 6-4 9-2 2-4 4-7 6-1 0-1 0-2 1-2 0-4 1-5 1-10 3-25 0-34-4-1-1-1-1-1-2v-1c3 0 6 0 9 1l8 1c8 2 17 1 25-2h1c2-1 3-2 5-4z" class="G"></path><path d="M595 394c1-1 3-1 5-1 2-1 3-2 5-2 3-1 4-3 7-3-2 2-4 4-7 6-1 0-1 0-2 1-2-2-6-1-8-1z" class="L"></path><path d="M564 392l1-1c5 0 10 2 16 2 4 1 9 1 14 1 2 0 6-1 8 1-2 0-4 1-5 1-10 3-25 0-34-4z" class="O"></path><path d="M620 444l8 5c1 0 3 2 4 2s2 1 3 2h1l4-4 17 15h1l-2 9-6 17c-3-2-5-4-8-6h-1l-4-3c1-1 0-1 1-1 2 0 3 0 4-1s1-2 1-4v-1c-2-6-6-10-9-15-2-2-4-4-6-5-3-3-7-5-10-7 2 0 3 1 5 2l1 1c2 1 3 2 5 3 1 1 2 3 3 3s2 0 3 1l3 1c1 1 0 1 1 1l-9-8c-3-2-8-4-9-7h-1z" class="H"></path><path d="M657 464h1l-2 9-7-7h3c1-1 3-1 4-1h1v-1z" class="C"></path><defs><linearGradient id="AU" x1="648.797" y1="465.711" x2="640.899" y2="449.097" xlink:href="#B"><stop offset="0" stop-color="#acaba9"></stop><stop offset="1" stop-color="#dfdede"></stop></linearGradient></defs><path fill="url(#AU)" d="M640 449l17 15v1h-1c-1 0-3 0-4 1h-3l-17-15c1 0 2 1 3 2h1l4-4z"></path><defs><linearGradient id="AV" x1="650.504" y1="474.082" x2="637.891" y2="473.957" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#505050"></stop></linearGradient></defs><path fill="url(#AV)" d="M634 459c4 1 8 4 12 7 1 1 3 2 4 3s1 1 1 2h0c2 4 0 11-2 15v1c-2-1-5-3-7-3h-1l-4-3c1-1 0-1 1-1 2 0 3 0 4-1s1-2 1-4v-1c-2-6-6-10-9-15z"></path><defs><linearGradient id="AW" x1="618.889" y1="446.078" x2="612.393" y2="459.283" xlink:href="#B"><stop offset="0" stop-color="#898989"></stop><stop offset="1" stop-color="#c3c1c0"></stop></linearGradient></defs><path fill="url(#AW)" d="M580 429v-1c0-1-1-2-2-3h1 0c5 4 11 6 17 9s13 6 19 10l3 3c3 2 7 4 10 7 2 1 4 3 6 5 3 5 7 9 9 15v1c0 2 0 3-1 4s-2 1-4 1c-1 0 0 0-1 1l-3-2c-2-1-5-4-7-5-6-4-12-9-17-14-2-2-4-3-5-5l-2-2a57.31 57.31 0 0 1-11-11c-4-4-8-8-12-13z"></path><path d="M628 454c2 1 4 3 6 5 3 5 7 9 9 15-1 0-1 0-2-1-4-2-9-3-13-6 1 0 2 1 3 1s3 2 4 2v-2l1 1v-3c-1-3-9-7-12-10h0c1 0 2 1 3 1 1 1 2 1 3 2h0l1-1-3-3v-1z" class="C"></path><defs><linearGradient id="AX" x1="636.676" y1="480.703" x2="622.748" y2="459.69" xlink:href="#B"><stop offset="0" stop-color="#5a5a5a"></stop><stop offset="1" stop-color="#90908f"></stop></linearGradient></defs><path fill="url(#AX)" d="M605 455h1l2 2h3c5 5 10 8 17 10 4 3 9 4 13 6 1 1 1 1 2 1v1c0 2 0 3-1 4s-2 1-4 1c-1 0 0 0-1 1l-3-2c-2-1-5-4-7-5-6-4-12-9-17-14-2-2-4-3-5-5z"></path><path d="M634 479c2-1 3 1 6 0 1-1 2-2 2-3h0l1-1c0 2 0 3-1 4s-2 1-4 1c-1 0 0 0-1 1l-3-2z" class="E"></path><defs><linearGradient id="AY" x1="594.56" y1="432.544" x2="595.714" y2="442.271" xlink:href="#B"><stop offset="0" stop-color="#7a7a79"></stop><stop offset="1" stop-color="#929291"></stop></linearGradient></defs><path fill="url(#AY)" d="M580 429v-1c0-1-1-2-2-3h1 0c5 4 11 6 17 9s13 6 19 10h-1c-2 0-5-2-7-3-1-1 0-1-1-1-1-1-1-1-2-1h-1c-1 2-3 3-5 4l-6-3 13 13v-1l6 5h-3l-2-2h-1l-2-2a57.31 57.31 0 0 1-11-11c-4-4-8-8-12-13z"></path><path d="M641 168c-10-14-21-22-38-25-9-2-18-1-27 0 5-5 12-7 18-9 19-7 39-10 59-9 34 1 65 13 88 38l5 6 9-20h102v24c-23 0-45 0-63 18-7 7-11 15-15 24-6 12-9 25-13 37l-20 58-19 56c-3 11-7 21-10 32l-2 3-26 78-2 7-2 6-3 8-3 8-2 7-4 13-3 8-3 9-45 137-2 5-18 52c-4 13-10 28-12 42-1 10-1 19 1 28l4 11c-2 0-2 2-3 3l-1 1c1 1 1 1 1 2-4-4-6-10-7-16-1-2-1-3-1-5-1 1-1 2-1 3v1l-1 3-1-5c-1-3-1-7-1-10l-1-6c1-2 0-5 1-7 0-1-1-2-1-4-1-2-3-6-2-9l3 8c1-4 2-9 3-13 5-20 12-40 19-59l24-73 52-157 21-60 47-141 13-39c4-13 8-26 15-39 6-10 14-20 24-26 6-4 12-7 18-10l-52-1-6 12c-2 2-3 6-4 8-2 4-4 7-5 11h0c-1 2-1 2-3 3l-11-17c-14-17-36-32-58-35h-1l-9-1c-1-1-3-1-5 0-2-1-5-2-7-2-3 3-4 6-8 7h-1 0l4 5-1 1c4 8 7 14 9 23h0l1 3v7h-1c-1-1-1-2-1-3h-1v3l-1-1c-1 3 0 4 0 6h-1v-1c-1-2-2-1-3-2 0 0-1-3-1-4-2-5-5-13-9-16z" class="M"></path><path d="M654 190v-2l-1-1v-2c-1-1-1-2 0-4l1 1h0l-1-1v-3c-1-1-2-3-3-5v1c-1-1 0-2 0-4 2 4 4 9 6 13v3l-1-1c-1 3 0 4 0 6h-1v-1zm-63 619l4 11c-2 0-2 2-3 3l-1 1c1 1 1 1 1 2-4-4-6-10-7-16 1 1 1 2 2 4 1 3 2 5 3 8l3-3v-2l-1-1v-1c0-1-1-2-1-2v-2s0-1-1-1l1-1z" class="F"></path><path d="M627 144c-3-1-7-3-10-4-5-2-10-3-15-4 3 0 7-1 10-1h0c4 1 10 2 13 5 1 0 1 0 1 1h0c1 1 2 2 3 2 1 1 3 2 4 4h0l-6-3z" class="C"></path><path d="M577 771l3 8 1 3c1 0 1-1 2-1l-1 15v1l2 8c-1 1-1 2-1 3v1l-1 3-1-5c-1-3-1-7-1-10l-1-6c1-2 0-5 1-7 0-1-1-2-1-4-1-2-3-6-2-9z" class="G"></path><path d="M580 797c2-3 0-6 2-9v8h0v1l2 8c-1 1-1 2-1 3v1l-1 3-1-5c-1-3-1-7-1-10z" class="E"></path><path d="M582 797l2 8c-1 1-1 2-1 3v1l-1 3-1-5c2-3 0-6 1-10z" class="K"></path><path d="M626 141c12 6 21 17 27 30 1 3 2 7 4 10 1-2 1-3 1-5l1 3v7h-1c-1-1-1-2-1-3h-1c-2-4-4-9-6-13-6-10-13-19-23-26l6 3h0c-1-2-3-3-4-4-1 0-2-1-3-2h0z" class="G"></path><path d="M649 153c-7-8-14-14-23-19 8-1 17-1 26 0-1 1-3 2-4 3-2 2-4 2-4 5 0 2 0 3 1 5h1l4 5-1 1z" class="H"></path><defs><linearGradient id="AZ" x1="649.813" y1="156.154" x2="749.263" y2="168.836" xlink:href="#B"><stop offset="0" stop-color="#787777"></stop><stop offset="1" stop-color="#9f9e9e"></stop></linearGradient></defs><path fill="url(#AZ)" d="M652 134h8c28 3 56 14 74 36 5 5 9 11 12 17 1 1 2 1 2 2v2l1 1c-1 2-1 2-3 3l-11-17c-14-17-36-32-58-35h-1l-9-1c-1-1-3-1-5 0-2-1-5-2-7-2-3 3-4 6-8 7h-1 0-1c-1-2-1-3-1-5 0-3 2-3 4-5 1-1 3-2 4-3z"></path><path d="M652 134h8c-1 1-2 1-4 1-2 1-3 3-4 5h1 0c-1 0-2 0-3 1v1l-1 1c-1 1-1 2-2 3l-2 1c-1-2-1-3-1-5 0-3 2-3 4-5 1-1 3-2 4-3z" class="E"></path><path d="M746 187l15-29 43 1c10-1 20-1 30 0 3 1 5 1 8 3l2 1c-23 1-41 6-56 23-8 8-14 19-18 29l-8 23-13 40-25 75-116 344-19 60c-2 4-3 8-4 12l-1 9s0 2-1 3c-1 0-1 1-2 1l-1-3c1-4 2-9 3-13 5-20 12-40 19-59l24-73 52-157 21-60 47-141 13-39c4-13 8-26 15-39 6-10 14-20 24-26 6-4 12-7 18-10l-52-1-6 12c-2 2-3 6-4 8-2 4-4 7-5 11h0l-1-1v-2c0-1-1-1-2-2z" class="I"></path><path d="M461 574c-3-19-6-39-6-58-1-11 0-22 0-33l1 1 1 1c3 4 4 10 6 15l11 29 31 83c1 3 2 5 2 8 1 3 1 6 2 9h0c3-2 7-5 11-5 1 1 1 2 1 3-4 4-8 7-11 12 2 0 4-1 5-2 3-1 6-2 10-1h0 1l1 2h0c1 2 2 4 2 6l1 2 3 3c1 2 2 8 4 8 5 11 8 22 13 33l11 28c0 2 1 3 0 5-1 3-2 5-4 7 3 2 5 3 7 6s2 7 4 10c2 8 6 17 9 25h0c-1 3 1 7 2 9 0 2 1 3 1 4-1 2 0 5-1 7l1 6c0 3 0 7 1 10l1 5v2c0 2 0 2 1 4v1l2 4c0 1-1 2-2 3h-1 0c-1-2-2-3-4-3-4-6-7-11-10-17s-6-12-9-17l-3-6-5-9c-1-1-1-3-2-4l-5 2c1 2 2 5 4 7 1 2 3 5 3 7 1 2 3 4 4 7 1 0 1 2 2 3 3 7 4 14 3 22-1 3-1 7-2 10 0 1 0 3-1 4v2h0l-1 1h0 0v-3l1-1h0c-1-1-1-1-2-1l-2 5h0 0c-3 7-4 13-8 19h-1c-1 1-2 2-2 4-2 2-6 5-8 7l-8 5c-1 0-2 1-3 1-2 0-4 1-6 1-5 1-9 0-13-1h0c-4-1-7-3-10-6h-1c-3-2-5-4-7-6v-1c-1-1-1-1-1-2v-1l-2-3v-1-2h0c1-2 0-4 0-6-1-3-1-7 0-10 0-6 3-12 5-17 3-4 8-11 12-12 1-6 1-12 0-18 0-2 0-6-1-8 1-1 1-2 1-3l-1-1c1-5 1-12 0-17l1-1c-1-4-1-9-1-13v-20-12-5l1-1v-1c1-1 0-2 0-4 0-3 1-6 0-9v-1c-1-3-1-6-1-9 0 1 0 5-1 5v-4-8c-1-2 0-5-1-8v7c-1 0-2 1-2 1-3 0-4 0-5-1-2-2-1-2-1-4h-2s-1 1-1 2c0-2 0-4-1-7-3-2-6-2-9-2 0-1 1-2 1-4-1-1-2-4-4-5-2 0-4 0-5 1s-2 1-3 2l-2-1v-3c1-4 1-10 3-14v-1c-2-3-1-7-1-10l-2-24v-11-3l-1-1c-1-1 0-3 0-4l-1-10z" class="M"></path><path d="M521 685l2-1c1-1 0-2-1-4h0c2 2 4 5 5 8 0 1-1 1-1 1-2 1-4 0-6-1l1-3z" class="I"></path><path d="M554 811l1 3c1 4 0 8 1 11 0 1 1 1 1 2l1-1v-1-2c0-1 0 0 1-1l-1-1c0-2 1-2 2-3-1 3-1 7-2 10 0 1 0 3-1 4v2h0l-1 1h0 0v-3l1-1h0c-1-1-1-1-2-1l-2 5h0c1-4 1-7 2-10 0-5 0-9-1-14z" class="F"></path><path d="M553 758h1c-1 2-3 5-5 6s-4 0-6-1c0-1-1-1-1-2v-2c3-1 7 1 10 0l1-1z" class="S"></path><path d="M517 786l1-1 3 1c-1 2-1 4-1 6 0 4 1 12-1 15v7c-1-9-1-19-2-28z" class="O"></path><path d="M544 772c1 2 2 5 4 7 1 2 3 5 3 7-3-3-6-5-9-7-2-2-1-3-2-4 1-2 2-2 4-3z" class="H"></path><path d="M511 684l10 1-1 3c-3 1-6 3-8 6v1c0-4-1-7-1-11z" class="B"></path><path d="M526 737h2c2 1 4 3 5 6v1c2 5 1 13 0 17-1-2 0-7-1-10-1-5-4-8-6-12-1-1 0-1 0-2z" class="G"></path><path d="M542 816c1 6 1 13 0 18h-1c-1-2-2-3-3-4-3-3-7-6-11-8 1 0 2 0 3 1l2 1c1 0 1 0 2 1v1c2 0 1 0 2-1 1 0 2-1 4-1h2v-2c-1-2-1-3 0-5v-1z" class="D"></path><path d="M514 785c1 0 2 3 3 3v-2c1 9 1 19 2 28v4h-2l-1 2v-4c-1-10-3-21-2-31z" class="Q"></path><defs><linearGradient id="Aa" x1="516.946" y1="772.457" x2="528.093" y2="802.154" xlink:href="#B"><stop offset="0" stop-color="#0a0a09"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#Aa)" d="M516 771c1 1 2 2 3 2 3 5 6 10 8 15 3 4 6 9 7 14 1 1 1 1 0 1-4-2-7-10-10-14-1-1-2-3-3-3l-3-1-1 1v2c-1 0-2-3-3-3l1-10s-1-2 0-2c0-1 0-1 1-2z"></path><path d="M530 646l3 3c1 2 2 8 4 8 5 11 8 22 13 33l11 28c0 2 1 3 0 5-1 3-2 5-4 7 3 2 5 3 7 6s2 7 4 10c2 8 6 17 9 25h0c-1 3 1 7 2 9 0 2 1 3 1 4-1 2 0 5-1 7v-6c-1-3-3-7-4-10l-10-27c-2-3-2-9-4-11s-6-4-7-5l2-3c0-1 2-4 2-6 1-3-22-60-25-68l-3-9z" class="I"></path><path d="M526 636l1 2h0c1 2 2 4 2 6l-1 2c2 2 3 7 2 10 0 2-3 5-4 7-6-3-10-7-16-9l2 7h0l-1-3c-2-1-3 0-4 0v-2c-1-3-2-6-2-8-1-2 0-4 1-6 1-1 2 0 3 0v-1l1-2c2 0 4-1 5-2 3-1 6-2 10-1h0 1z" class="R"></path><path d="M524 639c1 0 2-1 3-1h0c1 2 2 4 2 6l-1 2c-1-3-2-5-4-7h0z" class="L"></path><path d="M505 648c-1-2 0-4 1-6 1-1 2 0 3 0l3 3 1 2c-1 1-2 2-4 2-1-1-3-1-4-1zm17-8c2 1 2 2 2 4 1 2 1 3 0 4v1c-2 0-5-1-7-2v-2c1-3 3-4 5-5z" class="H"></path><path d="M526 636l1 2c-1 0-2 1-3 1h0c-1 0-1 0-2 1-2 1-4 2-5 5h-5l-3-3v-1l1-2c2 0 4-1 5-2 3-1 6-2 10-1h0 1z" class="O"></path><path d="M509 641c3 0 9-1 11-2l1-1c1 0 2 0 3 1h0c-1 0-1 0-2 1-2 1-4 2-5 5h-5l-3-3v-1z" class="N"></path><path d="M552 816c0-7 0-14-4-21h0v-1c2 3 4 6 5 9l1 8c1 5 1 9 1 14-1 3-1 6-2 10h0c-3 7-4 13-8 19h-1c-1 1-2 2-2 4-2 2-6 5-8 7l-8 5c-1 0-2 1-3 1-2 0-4 1-6 1-5 1-9 0-13-1h0c-4-1-7-3-10-6h-1c-3-2-5-4-7-6v-1c-1-1-1-1-1-2v-1c6 8 14 14 24 16h8v-1c4 0 8-2 10-4v-2c1 0 2-1 3-1h0c4-3 7-7 9-11 0-2 0-3 1-5v-6c0-1 0-3-1-4l1-1 1-2h1c1-5 1-12 0-18l-1-7c1 3 2 5 2 7l8 2c1-1 1-1 1-2z" class="B"></path><path d="M552 816l1-1c0 3 0 6-1 9v1 2 2l-1 1v2 1l-2 5c-1 1-1 2-1 3h0l-1 1c-1 0-1 1-2 2l-1 2 1-4c2-3 5-13 4-17l-2-1c-2-1-3-3-3-5l-1-3 8 2c1-1 1-1 1-2z" class="E"></path><defs><linearGradient id="Ab" x1="541.309" y1="831.029" x2="548.089" y2="830.892" xlink:href="#B"><stop offset="0" stop-color="#363536"></stop><stop offset="1" stop-color="#525251"></stop></linearGradient></defs><path fill="url(#Ab)" d="M542 816l-1-7c1 3 2 5 2 7l1 3c0 2 1 4 3 5l2 1c1 4-2 14-4 17l-1 4c-2 2-2 5-5 6 0-2 0-3 1-5v-6c0-1 0-3-1-4l1-1 1-2h1c1-5 1-12 0-18z"></path><path d="M540 841c1 2 1 2 1 5l-1 1v-6z" class="K"></path><path d="M457 485c3 4 4 10 6 15l11 29 31 83c1 3 2 5 2 8 1 3 1 6 2 9h0c3-2 7-5 11-5 1 1 1 2 1 3-4 4-8 7-11 12l-1 2v1c-1 0-2-1-3 0-1 2-2 4-1 6 0 2 1 5 2 8v2c0 2 1 3 1 5 1 1 2 1 3 1l2 1h1 0 2l1 1c2 0 3 0 5 1l14 1c-2 2-4 3-7 4l12 33 21 63 5 13c2 4 3 8 4 12-7-11-11-24-16-37-1-1-1-3-2-5l-6-21c-3-11-8-22-12-34-3-6-4-12-7-18-1-2-2-3-4-4h-8c2 2 5 4 6 6h0c1 2 2 3 1 4l-2 1-10-1h-1-2c-1 1-1 8-2 8v-3c0-8 0-17-2-24-2 3-1 8-1 12-1 7-1 16 0 23h-1 0v-2-3-1-6-5-11-4-1l1-1v-2h2c1 2 1 4 1 6v1l2-1c-1-1-1-1-1-2v-2-2-1c-2-2 0-7-2-10h0c0-1 0-2-1-3-1-2-1-3-1-5l1-1v-1c-1-1-1-3 0-5v-2h1c1-1 2-3 2-4 1 0 1-1 1-2 0-4-2-8-3-12l-6-15-25-67c-7-17-15-34-18-52l1 1z" class="F"></path><path d="M508 664c2 1 3 2 5 3 1 0 1 1 1 3v1l-5 1-1-8z" class="H"></path><path d="M483 833c0-6 3-12 5-17 3-4 8-11 12-12l1 12h-1l1 2v7c-3 2-5 7-5 11-1 4 0 6 1 9 1 4 4 10 8 12l2 1h1c0 1 0 1 2 2l1 1 1-1v-5h1c2-3 6-3 9-6 2-3 2-9 1-12 0-4-4-7-7-9 0-4-1-8 0-12v4l1-2h2c2 1 6 4 8 4 4 2 8 5 11 8 1 1 2 2 3 4l-1 2-1 1c1 1 1 3 1 4v6c-1 2-1 3-1 5-2 4-5 8-9 11h0c-1 0-2 1-3 1v2c-2 2-6 4-10 4v1h-8c-10-2-18-8-24-16l-2-3v-1-2h0c1-2 0-4 0-6-1-3-1-7 0-10z" class="S"></path><path d="M538 830c1 1 2 2 3 4l-1 2-1 1c0-3-1-4-1-7z" class="K"></path><defs><linearGradient id="Ac" x1="511.886" y1="830.696" x2="494.131" y2="850.339" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#404040"></stop></linearGradient></defs><path fill="url(#Ac)" d="M501 818v7c-3 2-5 7-5 11-1 4 0 6 1 9 1 4 4 10 8 12l2 1h1c0 1 0 1 2 2l1 1h-1c-2 1-6-1-7-2-5-4-8-7-9-13-1-4-1-8-1-12 1-4 2-8 4-12h0c1-2 2-3 4-4z"></path><path d="M516 816v4 1c1 3 3 5 6 6 4 3 8 8 9 13s-1 10-4 14-6 6-11 6h-4v-5h1c2-3 6-3 9-6 2-3 2-9 1-12 0-4-4-7-7-9 0-4-1-8 0-12z" class="J"></path><path d="M483 833c0-6 3-12 5-17 3-4 8-11 12-12l1 12h-1c-2 0-3 0-4 2-6 6-8 16-7 23 0 8 3 15 9 20 4 5 11 7 17 6 4 0 8-1 12-3v2c-2 2-6 4-10 4v1h-8c-10-2-18-8-24-16l-2-3v-1-2h0c1-2 0-4 0-6-1-3-1-7 0-10z" class="M"></path><defs><linearGradient id="Ad" x1="504.321" y1="767.819" x2="539.875" y2="768.861" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#d6d6d3"></stop></linearGradient></defs><path fill="url(#Ad)" d="M503 677c0-4-1-9 1-12 2 7 2 16 2 24v3c1 0 1-7 2-8h2 1c0 4 1 7 1 11-1 4-1 8 0 12 4 0 7 0 11 1l1 1c6 1 11 7 14 13 2 3 4 7 4 10-1 0-2-1-3-1h0c1 2 1 6 1 9-4-6-8-10-14-13h0c-3 3-5 6-8 9-1 2-4 4-5 6-1 1-1 2-1 3 2 3 6 2 8 3 3 1 4 3 4 5v1c-2 1-7 1-8 4-2 3-2 9-2 13h2c-1 1-1 1-1 2-1 0 0 2 0 2l-1 10c-1 10 1 21 2 31-1 4 0 8 0 12 3 2 7 5 7 9 1 3 1 9-1 12-3 3-7 3-9 6h-1v5l-1 1-1-1c-2-1-2-1-2-2h-1l-2-1c-4-2-7-8-8-12-1-3-2-5-1-9 0-4 2-9 5-11v-7l-1-2h1l-1-12c1-6 1-12 0-18 0-2 0-6-1-8 1-1 1-2 1-3l-1-1c1-5 1-12 0-17l1-1c-1-4-1-9-1-13v-20-12-5l1-1v6c0 1 1 1 2 1v-3-2-7h1c-1-7-1-16 0-23z"></path><path d="M513 725v-3c0-3 0-7 2-8 5-1 8 1 12 2h-3c-1 0-3 1-4 2-2 2-2 5-4 7h-3z" class="S"></path><defs><linearGradient id="Ae" x1="533.297" y1="721.534" x2="528.462" y2="731.749" xlink:href="#B"><stop offset="0" stop-color="#8d8c8c"></stop><stop offset="1" stop-color="#acadac"></stop></linearGradient></defs><path fill="url(#Ae)" d="M527 716c5 2 8 7 11 12 0 0 0 2 1 3 1 2 1 6 1 9-4-6-8-10-14-13h0-1c-2 0-4-1-7-1-2 0-3 0-5-1h3c2-2 2-5 4-7 1-1 3-2 4-2h3z"></path><path d="M503 677c0-4-1-9 1-12 2 7 2 16 2 24l6 166v5l-1 1-1-1c-2-1-2-1-2-2h-1l-2-1c-4-2-7-8-8-12-1-3-2-5-1-9 0-4 2-9 5-11v-7l-1-2h1l-1-12c1-6 1-12 0-18 0-2 0-6-1-8 1-1 1-2 1-3l-1-1c1-5 1-12 0-17l1-1c-1-4-1-9-1-13v-20-12-5l1-1v6c0 1 1 1 2 1v-3-2-7h1c-1-7-1-16 0-23z" class="D"></path><path d="M501 825c-1 3-2 6-2 9-1 5 0 14 3 18 2 2 4 3 5 5l1-8v9h-1l-2-1c-4-2-7-8-8-12-1-3-2-5-1-9 0-4 2-9 5-11z" class="Q"></path><path d="M499 757l1-1c1 7 1 14 1 20l1 43h0l-1-3-1-12c1-6 1-12 0-18 0-2 0-6-1-8 1-1 1-2 1-3l-1-1c1-5 1-12 0-17z" class="J"></path><path d="M503 677c0-4-1-9 1-12 2 7 2 16 2 24l6 166v5l-1 1-1-1c-2-1-2-1-2-2v-9l-5-172z" class="P"></path><path d="M461 574c-3-19-6-39-6-58-1-11 0-22 0-33l1 1c3 18 11 35 18 52l25 67 6 15c1 4 3 8 3 12 0 1 0 2-1 2 0 1-1 3-2 4h-1v2c-1 2-1 4 0 5v1l-1 1c0 2 0 3 1 5 1 1 1 2 1 3h0c2 3 0 8 2 10v1c0 2 0-1 0 2v2c0 1 0 1 1 2l-2 1v-1c0-2 0-4-1-6h-2v2l-1 1v1 4 11 5 6 1 3 2h0v7 2 3c-1 0-2 0-2-1v-6-1c1-1 0-2 0-4 0-3 1-6 0-9v-1c-1-3-1-6-1-9 0 1 0 5-1 5v-4-8c-1-2 0-5-1-8v7c-1 0-2 1-2 1-3 0-4 0-5-1-2-2-1-2-1-4h-2s-1 1-1 2c0-2 0-4-1-7-3-2-6-2-9-2 0-1 1-2 1-4-1-1-2-4-4-5-2 0-4 0-5 1s-2 1-3 2l-2-1v-3c1-4 1-10 3-14v-1c-2-3-1-7-1-10l-2-24v-11-3l-1-1c-1-1 0-3 0-4l-1-10z" class="M"></path><path d="M469 635c0 2-1 4-1 6 0-1 1-2 1-3h1l-7 14c1-4 1-10 3-14 0 1 0 1 1 1 1-1 1-3 2-4z" class="O"></path><path d="M465 627v-1c2-3 3-7 4-9 0 1-2 8-3 10h0v3l1 1v-1c0-2 1-3 1-5l1-1v-1l1-1v-1c0-1 0 0 1-1 0-1 0-2 1-3v1c-3 6-4 12-6 19-2-3-1-7-1-10z" class="F"></path><path d="M491 652c-4-3-10-2-13-6l1-1h6c1 2 4 3 6 5v2z" class="O"></path><path d="M469 635c1-1 1-2 2-2 3-3 9-2 12-2l1 3h2-1c-7 2-10-1-15 4h-1c0 1-1 2-1 3 0-2 1-4 1-6z" class="L"></path><path d="M474 593c2 0 2-1 3 0 2 1 2 3 2 5-1 2-2 2-4 3l-1 1c-1 0-2-1-3-2 0-2 0-4 1-5 0-1 1-2 2-2z" class="N"></path><path d="M483 631c2-2 0-9 1-12 1-2 4-5 5-6l-1 8c0 3 1 11-1 13h-1-2l-1-3z" class="F"></path><path d="M461 574v-1c1-1 1-3 2-5 1-4 2-7 4-11 1 6-2 12-3 18-1 9 0 18-1 28v-11-3l-1-1c-1-1 0-3 0-4l-1-10z" class="L"></path><path d="M475 581h3c2 0 5 2 6 4 3 4 5 10 4 15 0 4-3 7-6 9 0 1-1 1-1 1 1-5 2-9 2-13s-1-7-4-9c-2-2-4-2-7-2h0v-1c0-2 2-3 3-4zm15 59c1-2 2-5 3-7 1-3 1-6 2-10 0-1 0-3 1-5h2c1 1 2 1 2 3 1 4 0 8-2 12 0 2 0 4-1 5-1 3-1 6 0 9 0 1 1 3 2 5v9h-1v-1c0-1 0-2-1-3v-1c-2-3-4-4-6-6s-5-3-6-5c2-1 3-4 5-5z" class="C"></path><path d="M490 640c3 3 5 7 6 12 1 1 1 2 1 3v1c-2-3-4-4-6-6s-5-3-6-5c2-1 3-4 5-5z" class="K"></path><path d="M497 638c0 2 1 3 1 5 0 1 0 1 1 2 0 1 1 3 0 4 0 1 1 2 1 3v1h1v-4h0c2 4 1 11 1 15l-1 1h-1v1c0 1 0 2-1 3 0 2-1 5 0 7 1 1 0 3 0 5 0 1 0 5-1 5v-4-8c-1-2 0-5-1-8v7c-1 0-2 1-2 1-3 0-4 0-5-1-2-2-1-2-1-4h-2s-1 1-1 2c0-2 0-4-1-7-3-2-6-2-9-2 0-1 1-2 1-4 3 1 9-1 11-3l3-3v-2c2 2 4 3 6 6v1c1 1 1 2 1 3v1h1v-9c-1-2-2-4-2-5-1-3-1-6 0-9z" class="D"></path><path d="M489 669c2-1 6-4 8-5 0 1-1 1 0 2v7c-1 0-2 1-2 1-3 0-4 0-5-1-2-2-1-2-1-4z" class="K"></path><path d="M670 536h1 0c1-1 1-2 2-3l2 2c-1 1-1 2 0 4l3 3c0 1 1 2 2 3h1l-1 1h0l1 2c0 1 0 3 1 4-1 2-1 3-3 4v1c1 2 2 3 4 4l-2 1h0l-1 3c1 2 3 3 5 4 1 1 1 1 2 1l2 1c4 0 11 5 14 6 4 3 9 7 12 11 6 6 13 13 18 20h0c25 36 37 88 30 132-5 27-15 57-38 73-8 6-18 9-28 10-1 1 0 2-1 4 0 6-1 12-3 18v2h1c-2 1-2 2-3 1l-1-1c-2-2-5 0-8 0 0 0-2-1-2-2-4 1-5 1-8 0v-1c-6-2-10-4-15-7-1 1-1 3-2 4v1c0 3-2 6-4 7-5 3-16 0-21-2-16-4-27-13-35-27l-4-11c-2-9-2-18-1-28 2-14 8-29 12-42l18-52 2-5 45-137 3-9z" class="P"></path><path d="M610 773c1 1 2 2 1 4 0 2-1 5-3 7l-1-1c2-2 2-3 3-5v-5z" class="K"></path><path d="M603 773c1-1 2-2 4-2 1 1 2 1 3 2v5c-1-2-1-3-3-4-2 0-2 0-4 1v-2z" class="H"></path><path d="M709 764l3-2h2c1 0 2-2 3-3 2-3 4-5 6-8 2-2 3-6 5-9 0-1 1-2 2-3-5 12-11 21-21 29l1-2-1-2z" class="C"></path><path d="M696 613h1c1 0 1 0 1 1l-1 6c0 4 0 7 2 9 0 1 0 2 1 3l-1 1c-2-1-3-4-4-6-1-4-1-10 1-14z" class="K"></path><path d="M628 819c1-1 3-1 4-1 1 1 1 1 1 2-1-1-3-1-4 0-2 1-2 2-3 4h1c2 0 5-1 7-3h0 1l-2 4c-1 0-8 2-9 3 0 1 0 3-1 4l-1-2c-1-1 0-3 1-5 1-3 2-5 5-6zm37-246c2-1 2-1 3-3 2-1 4-2 6-2 4-1 8 0 11 3l-5-1-1 1c1 1 1 1 2 1l2 2c-1 0-1 0-1 1h0c-4-2-8-4-12-2h0c-3 1-4 3-6 5 0-2 0-3 1-5z" class="Q"></path><path d="M734 743c3-8 6-17 8-26 2-5 2-11 4-16 0 2 0 3-1 5 0 2 0 5-1 8-1 11-4 22-9 32l-1-3z" class="O"></path><defs><linearGradient id="Af" x1="668.577" y1="569.15" x2="675.255" y2="563.224" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#7b7b7a"></stop></linearGradient></defs><path fill="url(#Af)" d="M665 573c0-2 1-3 2-5 4-4 9-6 14-6h0l-1 3c1 2 3 3 5 4 1 1 1 1 2 1l2 1h-2c-1 1-2 0-2 0-3-3-7-4-11-3-2 0-4 1-6 2-1 2-1 2-3 3z"></path><defs><linearGradient id="Ag" x1="667.788" y1="543.288" x2="682.384" y2="550.817" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#4b4b4b"></stop></linearGradient></defs><path fill="url(#Ag)" d="M670 536h1 0c1-1 1-2 2-3l2 2c-1 1-1 2 0 4l3 3c0 1 1 2 2 3h1l-1 1h0l1 2c0 1 0 3 1 4-1 2-1 3-3 4v1c-4-4-7-8-12-12l3-9z"></path><path d="M687 676c1-1 2-3 3-4h0 1c1 3 2 8 1 11s-3 5-6 6c-3 2-7 2-11 1-2-1-5-4-6-7-1-4-1-8 0-11s3-5 6-6c2-1 5-1 7 0 1 1 3 2 3 4v1h-1l-1 1v-1l-3-3c-1 0-3 0-4 1-2 1-4 4-5 6 0 3 0 7 2 9 2 3 4 4 6 4 3 0 7-1 9-3s2-3 2-5 0-2-1-3c-1 0-1-1-2-1z" class="E"></path><defs><linearGradient id="Ah" x1="596.073" y1="785.24" x2="628.342" y2="808.132" xlink:href="#B"><stop offset="0" stop-color="#2e2f2f"></stop><stop offset="1" stop-color="#4f4f4f"></stop></linearGradient></defs><path fill="url(#Ah)" d="M603 773v2c2-1 2-1 4-1 2 1 2 2 3 4-1 2-1 3-3 5l1 1c-1 1-2 1-3 2h-2c0 5 2 10 5 14 7 8 16 6 25 6-4 3-11 3-16 2-5 0-9-3-12-7-4-6-6-16-5-23v-1c1-2 2-3 3-4z"></path><path d="M603 775c2-1 2-1 4-1 2 1 2 2 3 4-1 2-1 3-3 5-1 1-2 1-3 1-1-1-2-1-2-2-1-3 0-5 1-7z" class="T"></path><path d="M660 802c1 0 2 0 3 1-3 1-8 2-10 6 0 1 0 4 1 5 2 5 8 7 12 9 5 1 13 2 19 1h2l1-2h1c1 1 1 2 1 4 0 1 0 1-1 2-9 2-24 0-32-5-4-2-6-6-7-10-1-2 0-4 1-6 3-3 6-5 9-5z" class="D"></path><defs><linearGradient id="Ai" x1="688.488" y1="772.275" x2="690.633" y2="778.454" xlink:href="#B"><stop offset="0" stop-color="#9b9a9a"></stop><stop offset="1" stop-color="#bcbbba"></stop></linearGradient></defs><path fill="url(#Ai)" d="M709 764l1 2-1 2c-11 10-25 16-40 15-5-1-10-3-14-6l9 2c4 0 8 0 12-1h2 1l6-2c4 0 8-2 11-4v-1c1-1 2-1 3-1h2l4-3c1-1 2-2 4-3z"></path><defs><linearGradient id="Aj" x1="675.13" y1="838.789" x2="693.68" y2="822.565" xlink:href="#B"><stop offset="0" stop-color="#b7b6b4"></stop><stop offset="1" stop-color="#dfdddc"></stop></linearGradient></defs><path fill="url(#Aj)" d="M670 815l1-2c2 0 2-1 4 0h7c5 1 7 2 10 6l1 1c1 0 1 0 1 1h1 1c0 1 0 1 1 2h0c-1 1 0 2-1 4 0 6-1 12-3 18v2h1c-2 1-2 2-3 1l-1-1c-2-2-5 0-8 0 0 0-2-1-2-2-4 1-5 1-8 0v-1c1 1 2 1 4 1 4 0 10-4 13-8 3-3 4-7 3-11 0-3-1-4-3-6-1-1-2-2-3-2h-2c-4-2-9-1-12-3h-2z"></path><path d="M670 815l1-2c2 0 2-1 4 0h7c5 1 7 2 10 6-1-1-3-3-5-3v-1c-5-1-10 0-15 0h-2z" class="K"></path><path d="M689 677c1 1 1 1 1 3s0 3-2 5-6 3-9 3c-2 0-4-1-6-4-2-2-2-6-2-9 1-2 3-5 5-6 1-1 3-1 4-1l3 3v1l1-1h1v1c0 1 0 3-1 4h3c1 0 1 1 2 1z" class="P"></path><path d="M683 672l1-1h1v1c0 1 0 3-1 4h3c1 0 1 1 2 1-2 1-4 1-5 2h-2c-1 1-2 1-4 0v-3l1-1c1 1 0 1 1 2h0l2-1c1-2 1-2 1-4z" class="B"></path><defs><linearGradient id="Ak" x1="656.123" y1="806.093" x2="681.503" y2="827.696" xlink:href="#B"><stop offset="0" stop-color="#070707"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#Ak)" d="M662 801c1 0 2 0 3 2h0c2 2 5 5 9 5 1 0 2 0 3 1h7c3 0 3 0 5 2-2 0-5 1-7 1v1h-7c-2-1-2 0-4 0l-1 2h2c3 2 8 1 12 3h2c1 0 2 1 3 2h-1l1 2h-1l-1 2h-2c-6 1-14 0-19-1-4-2-10-4-12-9-1-1-1-4-1-5 2-4 7-5 10-6-1-1-2-1-3-1l2-1h0z"></path><path d="M671 810c-5 0-9-1-14-2 1-1 3-2 4-3 1 1 1 2 3 2h0c1 1 1 1 2 1l5 2z" class="E"></path><path d="M670 815h2c3 2 8 1 12 3h2c-4 1-8 1-11 0-5 0-9-1-14-3h9z" class="D"></path><path d="M675 818c3 1 7 1 11 0 1 0 2 1 3 2h-1l1 2h-1l-1 2h-2-4l4-1c-1 0-3 0-4-1s0-1-2-1l-2-1c-1 0-2-1-2-2z" class="Q"></path><path d="M662 801c1 0 2 0 3 2h0c2 2 5 5 9 5 1 0 2 0 3 1h7c3 0 3 0 5 2-2 0-5 1-7 1v1h-7l4-1v-1c-3-1-5-1-8-1l-5-2c-1 0-1 0-2-1h0c-2 0-2-1-3-2 1 0 2-1 3-2h-1c-1-1-2-1-3-1l2-1h0z" class="B"></path><defs><linearGradient id="Al" x1="638.319" y1="634.663" x2="671.081" y2="646.537" xlink:href="#B"><stop offset="0" stop-color="#1d1c1d"></stop><stop offset="1" stop-color="#565555"></stop></linearGradient></defs><path fill="url(#Al)" d="M622 682h1 0c4-1 8-2 11-3 15-6 26-16 32-30 6-13 7-30 2-43-2-5-5-10-8-14-1-2-3-3-4-5v-3c1-1 1-1 2 0 3 2 5 4 7 7 5 7 8 15 10 23 3 15 0 31-8 44-10 15-25 25-43 29h-4l2-5z"></path><path d="M623 775c-2-6-3-13-3-20 0-15 6-29 17-39 8-8 18-12 29-12-5 1-9 2-12 7h1c4-1 8 0 12 1 6 1 10 5 14 11-4-4-7-6-12-7-7-2-13 0-19 3-6 4-10 11-11 18v1c-1 2-1 4-1 6 1 9 4 17 11 23 6 6 14 8 22 7 17-1 32-10 42-22 14-16 21-37 21-57 1-10 0-20-1-30h0c5 13 7 27 5 40-1 8-3 16-5 23l1 2c-1 2-2 8-4 9-1 1-2 2-2 3-2 3-3 7-5 9-2 3-4 5-6 8-1 1-2 3-3 3h-2l-3 2c-2 1-3 2-4 3l-4 3h-2c-1 0-2 0-3 1-3 2-6 3-10 4-11 4-21 4-32-1-7-4-12-10-15-17-2-4-2-8-3-12-2 7-2 12-1 18 3 9 9 16 16 20 11 6 25 6 37 3 21-7 36-24 46-43l1 3c-1 1-1 2-2 2l1 1c6-11 9-22 11-35 1-7 2-14 4-21 2 9 3 18 3 27h0c0 8-2 15-3 23-6 21-15 40-32 55l-3 2c-7 5-16 10-25 11-2-2-2-2-5-2h-7c-1-1-2-1-3-1-4 0-7-3-9-5h0c-1-2-2-2-3-2-7-2-12-4-18-8-1 1-1 1-3 1-1-1-2-2-2-3h0l-2-4-2 1c0 1 3 5 5 7 0 1 1 1 1 2l-1 1 7 9-6-4c-9-6-16-18-18-28z" class="M"></path><defs><linearGradient id="Am" x1="720.089" y1="748.085" x2="715.205" y2="760.957" xlink:href="#B"><stop offset="0" stop-color="#4a494c"></stop><stop offset="1" stop-color="#717270"></stop></linearGradient></defs><path fill="url(#Am)" d="M699 770c16-11 26-24 34-42l1 2c-1 2-2 8-4 9-1 1-2 2-2 3-2 3-3 7-5 9-2 3-4 5-6 8-1 1-2 3-3 3h-2l-3 2c-2 1-3 2-4 3l-4 3h-2z"></path><defs><linearGradient id="An" x1="638.471" y1="782.398" x2="626.042" y2="774.911" xlink:href="#B"><stop offset="0" stop-color="#302c2c"></stop><stop offset="1" stop-color="#626465"></stop></linearGradient></defs><path fill="url(#An)" d="M628 769c-1-8-2-16 0-23 1-5 2-9 4-14l1 1c-2 5-3 10-4 16-2 12 1 28 9 38 1 2 4 5 6 6-1 1-1 1-3 1-1-1-2-2-2-3h0l-2-4-2 1c0 1 3 5 5 7 0 1 1 1 1 2l-1 1c-6-9-10-19-12-29z"></path><defs><linearGradient id="Ao" x1="637.609" y1="795.025" x2="628.357" y2="784.505" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#bcbaba"></stop></linearGradient></defs><path fill="url(#Ao)" d="M628 769c2 10 6 20 12 29l7 9-6-4c-9-6-16-18-18-28h1l4 10c1-1 2-1 3-2-1-1-1-2-1-3l-1-1-1-3v-2c-1-1-1-2-1-2v-3h1z"></path><path d="M734 749c-1 3-3 6-5 9-4 6-8 13-14 18-10 11-25 20-40 20 4-2 9-2 13-4 4-1 8-4 12-6 9-7 18-14 25-24 3-4 5-9 8-14l1 1z" class="G"></path><defs><linearGradient id="Ap" x1="713.046" y1="778.289" x2="725.382" y2="788.149" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#bab9b8"></stop></linearGradient></defs><path fill="url(#Ap)" d="M752 720c0 8-2 15-3 23-6 21-15 40-32 55l-3 2c-7 5-16 10-25 11-2-2-2-2-5-2 7-1 14-3 21-7 17-10 28-27 37-45 2-6 5-11 6-17 2-7 3-13 4-20h0z"></path><path d="M682 575c0-1 0-1 1-1l-2-2c-1 0-1 0-2-1l1-1 5 1s1 1 2 0h2c4 0 11 5 14 6 4 3 9 7 12 11 6 6 13 13 18 20h0c25 36 37 88 30 132-5 27-15 57-38 73-8 6-18 9-28 10h0c-1-1-1-1-1-2h-1-1c0-1 0-1-1-1l-1-1c-3-4-5-5-10-6v-1c2 0 5-1 7-1 9-1 18-6 25-11l3-2c17-15 26-34 32-55 1-8 3-15 3-23 0-7 1-15 0-22-2-18-8-39-23-50-5-4-11-6-18-5-5 1-9 4-12 8-4 6-3 12-2 19-1-2-3-5-3-8h-1v-3c-1-9 2-18 7-25 6-8 15-10 26-11-5-4-10-6-16-8-11-3-22-4-29-13-2-1-3-3-3-4l5-2c4-1 6-3 8-6 0-5-1-7-4-11-1-2-3-3-5-4z" class="M"></path><path d="M712 636c-1 0-5 1-6 1 3-2 5-4 9-5 3-1 9-1 13-1v1h2l-2 1-3 1c-3-1-10-1-13 1v1zm-31-34c3 1 5 3 9 5 3 1 7 2 11 3 2 0 5 1 7 2 1 0 1 1 2 1v2c-11-3-22-4-29-13z" class="C"></path><path d="M720 598l2 2c2 3 6 7 6 10l1 2h-1l-4-2c-6-5-14-7-21-9 6 0 10 0 16 2h1c1-1 0-2 0-4v-1z" class="F"></path><path d="M695 821v-1c0-1 0-2 1-3 0 0 8-3 10-4 9-5 18-11 25-19 1-1 1-1 2-1-6 10-15 19-26 23-3 1-7 2-10 3v4c-1-1-1-1-1-2h-1z" class="G"></path><path d="M682 575c0-1 0-1 1-1l-2-2c-1 0-1 0-2-1l1-1 5 1s1 1 2 0h2c4 0 11 5 14 6 4 3 9 7 12 11 0 3 4 6 6 9v1c1 0 1 1 1 2l-2-2-10-9h-6c-2 0-4 0-7 1-1 0-2 0-4 1h-1 0-1v-1h0c0-5-1-7-4-11-1-2-3-3-5-4z" class="C"></path><path d="M687 579v-1c0-1 0-2-1-3l1-1 3 1h2c6 4 13 9 18 14h-6c-2 0-4 0-7 1-1 0-2 0-4 1h-1 0-1v-1h0c0-5-1-7-4-11z" class="L"></path><path d="M715 588c6 6 13 13 18 20h0c25 36 37 88 30 132-5 27-15 57-38 73-8 6-18 9-28 10h0v-4c3-1 7-2 10-3 11-4 20-13 26-23 5-5 9-12 12-18 11-21 15-45 14-68-1-21-7-49-23-64-7-6-14-8-24-7v-1c3-2 10-2 13-1l3-1 2-1h-2v-1c3 0 5 1 8 1l1 1c1-1 1-2 2-4-3-6-6-11-11-17h1l-1-2c0-3-4-7-6-10 0-1 0-2-1-2v-1c-2-3-6-6-6-9z" class="T"></path><path d="M740 632c2 1 2 1 3 3v1h1c2 3 3 10 3 14-1-1-4-4-5-6 1-3 0-6-1-9l-1-3z" class="H"></path><path d="M739 629c0 1 1 2 1 3l1 3c1 3 2 6 1 9-4-5-11-8-17-10l3-1 2-1h-2v-1c3 0 5 1 8 1l1 1c1-1 1-2 2-4z" class="J"></path><path d="M739 629c0 1 1 2 1 3l1 3-1 1c-4 0-8-2-12-3l2-1h-2v-1c3 0 5 1 8 1l1 1c1-1 1-2 2-4z" class="G"></path><path d="M715 588c6 6 13 13 18 20h0-1-1v-1l-1-1-1-1c0 1 1 1 1 2 1 2 1 4 2 6l6 9c3 4 5 9 6 14h-1v-1c-1-2-1-2-3-3 0-1-1-2-1-3-3-6-6-11-11-17h1l-1-2c0-3-4-7-6-10 0-1 0-2-1-2v-1c-2-3-6-6-6-9z" class="E"></path><path d="M361 580l19 53 1 4 24 62v1l34 90 1 4c1 4 4 9 5 13 0 2 1 8 1 8v5h-1v-3c-7 14-17 23-32 27-9 3-17 3-26 0 1 1 1 1 3 2 0 1-3 2-4 2-2-1-6-3-8-2h0c0 1 0 2 1 3 0-1 1 0 0-1 4 1 7 3 11 4 2 0 3 1 5 1 1 1 1 2 1 3-2 0-3 1-4 0h-3l-1-1h-2 0-2 0c6 3 14 3 21 3-1 0-1 0-2 1-10 3-20 5-30 6h0c-2 0-2 1-4 0h-3c0 2-1 2-2 3 2 0 5 0 6 1-10 1-20 0-29-4l-1 1c2 1 3 1 5 2-1 2-3 2-4 3-2 0-4 0-5-1-7-2-15-3-22-6-14-5-24-14-35-23h0l-9-10c-1-1-2-3-3-4l-8-12c-2-2-5-8-5-11-2-3-3-6-4-8-10-29-5-63 8-89l-1-2c1-2 1-3 2-4 3-2 5-8 8-12 5-7 10-14 16-21-2-2 1-2 2-4v-2l21-22c1 0 2-1 3-2 4-3 8-8 11-12 2-3 4-5 6-7 2-1 7-13 8-15h1c2-3 4-11 5-14 0-2 0-3 1-4l2 2-1 1h0 0c-1 0-1 1-1 2h0v2h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v2c2-4 3-8 5-13v1c1-1 1-1 1-2 1-2 1-3 2-5 4-1 7-5 11-5v1c2-1 4-2 5-4z" class="P"></path><path d="M367 766v-1c0-4-1-7-2-10h0c2 3 3 7 4 11h-1-1z" class="N"></path><path d="M352 599c2-2 5-3 8-1 1 0 1 0 2 1h-1c-2-1-3 0-5 0l-4 1v-1z" class="H"></path><g class="E"><path d="M421 827l2-2c2-2 5-3 8-4-3 3-5 6-8 7-1 0-2 0-2-1z"></path><path d="M412 818c5 1 9 3 12 7l-12-5-2-1h0c1 0 1-1 2-1z"></path></g><path d="M344 795c0 2 1 4 2 6 0 3-1 7-1 10-3-5-3-11-1-16z" class="B"></path><path d="M352 599v1l-1 1c-1 1-1 2 0 4 1 1 1 1 2 1 3 0 4-2 6-4 2-1 2-1 2-3h1c0 3-1 5-3 6-1 2-3 4-5 3-2 0-3 0-4-2-1-1-1-2-1-3 0-2 1-3 3-4z" class="K"></path><path d="M352 600l4-1c2 0 3-1 5 0 0 2 0 2-2 3-2 2-3 4-6 4-1 0-1 0-2-1-1-2-1-3 0-4l1-1z" class="T"></path><path d="M398 821l13-10-1 3 1-1h1c-1 1-3 2-4 4h0l4 1c-1 0-1 1-2 1-2 0-3 0-4 1v4c3 3 6 4 10 4 2 0 3 0 5-1 0 1 1 1 2 1-3 2-9 2-12 1-5-1-7-5-8-9l-4 3-1-2z" class="B"></path><defs><linearGradient id="Aq" x1="437.489" y1="804.111" x2="408.813" y2="808.754" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#5b5a5b"></stop></linearGradient></defs><path fill="url(#Aq)" d="M411 811c4-2 8-3 12-2 1 1 3 1 4 1 4 0 7-7 9-10 0-3 1-6 2-9 0 8 0 14-6 19-1 2-3 3-6 3-1 0-2 0-3-1-4-1-7 0-11 1h-1l-1 1 1-3z"></path><path d="M399 823c-2 3-20 9-24 10 7-5 10-8 13-16 2-4 2-8 1-11-1-1-1-1-1-2 2 1 3 2 3 4 3 6-2 13-4 18l11-5 1 2z" class="Q"></path><defs><linearGradient id="Ar" x1="359.163" y1="793.316" x2="355.677" y2="768.137" xlink:href="#B"><stop offset="0" stop-color="#8b8b8b"></stop><stop offset="1" stop-color="#c1bfbd"></stop></linearGradient></defs><path fill="url(#Ar)" d="M344 795c2-4 3-7 6-9 4-4 9-7 13-11 2-3 3-6 4-9h1 1l1 2c-1 1-1 2-1 4 0 1-1 1-1 2l-2 2c-4 8-12 11-16 17-2 2-3 5-4 7v1c-1-2-2-4-2-6z"></path><path d="M346 801v-1c1-2 2-5 4-7 4-6 12-9 16-17l2-2c-1 4-1 7-3 11-1 3-3 7-6 10s-7 5-10 9c-1 3-2 6-4 7h0c0-3 1-7 1-10z" class="H"></path><path d="M368 774c0-1 1-1 1-2 0-2 0-3 1-4l2 8v1c-1 9-2 21-8 28v2l-2 2c0-1 0-1-1-1 0-1-2-6-2-7s1-4 2-5h-1l-1-1c3-3 5-7 6-10 2-4 2-7 3-11z" class="L"></path><path d="M365 785c1 1 1 2 1 4-2 4-4 12-2 16v2l-2 2c0-1 0-1-1-1 0-1-2-6-2-7s1-4 2-5h-1l-1-1c3-3 5-7 6-10z" class="I"></path><defs><linearGradient id="As" x1="360.494" y1="653.633" x2="348.457" y2="655.623" xlink:href="#B"><stop offset="0" stop-color="#737373"></stop><stop offset="1" stop-color="#aaa8a7"></stop></linearGradient></defs><path fill="url(#As)" d="M353 646c0-7 2-13 7-18l1-1c-4 8-5 15-3 25 1 6 2 11 1 16v1c-1 3-2 7-5 9-1-1-2-3-3-4h-1v-1l-1-1 2-3c4-5 4-8 3-14v1c-1-4 1-7-1-10z"></path><defs><linearGradient id="At" x1="317.467" y1="866.523" x2="319.687" y2="847.672" xlink:href="#B"><stop offset="0" stop-color="#888988"></stop><stop offset="1" stop-color="#d3d0cf"></stop></linearGradient></defs><path fill="url(#At)" d="M289 845c2 0 3 0 4 2h2l2-2-4-4v-1c9 9 19 16 30 21 5 2 9 4 13 5h4c2 1 3 1 5 2-1 2-3 2-4 3-2 0-4 0-5-1-2-2-9-3-13-5-11-4-25-11-34-20z"></path><path d="M273 812c1-2 2-5 5-6 0 1-1 3-2 4-1 6 12 24 16 28l1 2v1l4 4-2 2h-2c-1-2-2-2-4-2v-1c-2-1-4-3-5-4-6-7-10-13-12-22 1-1 1-4 1-6z" class="O"></path><path d="M270 831v-1-1c1-3 0-7 1-9l1-2c2 9 6 15 12 22 1 1 3 3 5 4v1c9 9 23 16 34 20 4 2 11 3 13 5-7-2-15-3-22-6-14-5-24-14-35-23h0l-9-10z" class="S"></path><path d="M360 796h1c-1 1-2 4-2 5s2 6 2 7c1 0 1 0 1 1l2-2h1c-1 1-1 2-1 3l2 1c2 0 3 0 4-2l1-1 1-2c1-2 3-5 5-5h0c1 1 1 1 1 2s1 2 1 3c-2 5-5 10-7 15-2 2-3 5-5 7l-1-1c-4-6-7-12-8-19l-1-1c-1-4 1-8 3-11z" class="B"></path><path d="M358 808v-3-1 1c0 3 2 6 4 9 2 0 3 1 4 1 3-2 4-3 5-5l1-1h1c-1 2-2 4-3 5-1 4-4 8-4 13-4-6-7-12-8-19z" class="K"></path><path d="M362 814c2 0 3 1 4 1 3-2 4-3 5-5l1-1h1c-1 2-2 4-3 5-2 1-2 5-5 6-2-1-2-4-3-6z" class="Q"></path><path d="M377 801c1 1 1 1 1 2s1 2 1 3c-2 5-5 10-7 15-2 2-3 5-5 7l-1-1c0-5 3-9 4-13 1-1 2-3 3-5h0c2-2 3-5 4-8h0z" class="D"></path><defs><linearGradient id="Au" x1="379.259" y1="789.234" x2="371.231" y2="791.37" xlink:href="#B"><stop offset="0" stop-color="#575756"></stop><stop offset="1" stop-color="#787877"></stop></linearGradient></defs><path fill="url(#Au)" d="M378 771l1 9c1 7-1 14-2 21h0c-2 0-4 3-5 5l-1 2-1 1c-1 2-2 2-4 2l-2-1c0-1 0-2 1-3h-1v-2c6-7 7-19 8-28v-1s0 1 1 1l2-1c0-1 1-2 1-3s1-1 2-2z"></path><path d="M378 771l1 9h-6l-1 1v-4-1s0 1 1 1l2-1c0-1 1-2 1-3s1-1 2-2z" class="K"></path><path d="M365 807v-1l5-11c1 0 2 1 2 3 1 2 0 4 0 7l-1 3-1 1c-1 2-2 2-4 2l-2-1c0-1 0-2 1-3z" class="D"></path><defs><linearGradient id="Av" x1="396.436" y1="664.592" x2="372.183" y2="679.128" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#Av)" d="M380 633l1 4c-6 13-10 28-5 42 3 8 9 16 16 19 4 2 8 2 13 1v1c-1 0-1 1-1 1-3 2-8 2-11 1-8-3-14-10-18-17-5-11-7-25-3-37 2-5 4-11 8-15z"></path><path d="M318 828c-2-8-3-17-2-26 1-7 3-14 7-21s8-11 16-13c-1 1-3 2-4 3-4 2-5 6-7 10 0 1 0 2-1 3 1 2 1 3 2 4-3 4-3 10-4 14-1 2-2 2-4 2h-1l-1 1c0 6 0 12 1 18 0 2 1 4 1 6v1c-1-1-2-1-3-2z" class="C"></path><path d="M324 786l1-3c1-2 1-2 3-2 0 1 0 2-1 3l-3 2z" class="B"></path><defs><linearGradient id="Aw" x1="321.961" y1="803.693" x2="324.453" y2="784.796" xlink:href="#B"><stop offset="0" stop-color="#3e3e3f"></stop><stop offset="1" stop-color="#727171"></stop></linearGradient></defs><path fill="url(#Aw)" d="M324 786l3-2c1 2 1 3 2 4-3 4-3 10-4 14-1 2-2 2-4 2h-1l-1 1c1-7 3-13 5-19z"></path><defs><linearGradient id="Ax" x1="269.081" y1="806.367" x2="261.107" y2="794.849" xlink:href="#B"><stop offset="0" stop-color="#929191"></stop><stop offset="1" stop-color="#c3c2c1"></stop></linearGradient></defs><path fill="url(#Ax)" d="M255 789c2 2 2 5 3 8 1 0 2-1 3-1h1c1-1 2-2 4-3v-3c-1-1-1-3-1-4v-1-5h0c1 9 2 19 6 27l1 1c0 1 0 3 1 4 0 2 0 5-1 6l-1 2c-1 2 0 6-1 9v1 1c-1-1-2-3-3-4l-8-12c1 0 2 1 2 2h1c0-1-2-5-3-6 1-4-1-7-1-10 1-1-3-11-3-12z"></path><path d="M259 815c1 0 2 1 2 2h1c0-1-2-5-3-6 1-4-1-7-1-10 3 4 5 8 8 12 1 0 2 0 2-1 2 0 3-3 3-5l1 1c0 1 0 3 1 4 0 2 0 5-1 6l-1 2c-1 2 0 6-1 9v1 1c-1-1-2-3-3-4l-8-12z" class="E"></path><path d="M271 807l1 1c0 1 0 3 1 4 0 2 0 5-1 6l-1 2c-1 2 0 6-1 9v1 1c-1-1-2-3-3-4 0-4 1-8 0-12-1-1-1-1-1-2 1 0 2 0 2-1 2 0 3-3 3-5z" class="H"></path><path d="M271 807l1 1c0 3-1 7-1 10l-5-5c1 0 2 0 2-1 2 0 3-3 3-5z" class="B"></path><path d="M381 637l24 62c-5 1-9 1-13-1-7-3-13-11-16-19-5-14-1-29 5-42z" class="P"></path><defs><linearGradient id="Ay" x1="362.422" y1="809.235" x2="333.104" y2="719.198" xlink:href="#B"><stop offset="0" stop-color="#c3c2c1"></stop><stop offset="1" stop-color="#f1efee"></stop></linearGradient></defs><path fill="url(#Ay)" d="M378 771c-3-14-10-25-23-33-10-6-25-10-37-7-6 1-12 4-16 10-1 3-3 6-2 10 0 0 1 1 1 2h0c-1 0-2-1-2-2-1-3 0-8 1-11 4-9 13-15 22-18 12-4 25-3 36 2 6 3 11 7 15 13-1-4-1-7 1-10v-1c1 3 1 6 2 8l6 15c7 19 5 39-3 57 0-1-1-2-1-3s0-1-1-2c1-7 3-14 2-21l-1-9z"></path><path d="M344 725h1c2 1 4 2 6 2 9 4 19 10 24 18 3 5 8 14 7 20l-1-2c-1-2-1-5-2-7-4-7-9-15-15-20h-1l-5-3c1 1 1 1 2 0-4-3-11-6-16-8z" class="B"></path><path d="M334 604h1c2-3 4-11 5-14 0-2 0-3 1-4l2 2-1 1h0 0c-1 0-1 1-1 2h0v2h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v1l-1 1v1h0v2c-8 22-24 39-40 54l-9 9c-2 1-3 2-4 4 4-3 9-6 15-8 6-3 13-5 20-8 5-3 11-7 17-8 3-1 6-1 8-1l-4 19c3-2 6-4 8-7 2-5 4-10 5-15 2 3 0 6 1 10-1 2-2 5-4 8-7 11-19 17-32 22l-18 10c-1-1 0-1 0-2l-1-1v-3l1-1c0-2 1-2 2-4l-2-2c2-2 4-4 6-5l8-9v-1c0-1 6-3 6-5-6 3-13 4-19 6-12 5-25 14-34 24-3 4-6 9-9 14l-1-2c1-2 1-3 2-4 3-2 5-8 8-12 5-7 10-14 16-21-2-2 1-2 2-4v-2l21-22c1 0 2-1 3-2 4-3 8-8 11-12 2-3 4-5 6-7 2-1 7-13 8-15z" class="O"></path><path d="M353 646c2 3 0 6 1 10-1 2-2 5-4 8 0-2 0-3 1-4h0v-1h1v-1-1-1c1-1 1-1 1-2l-1-1c-1 2-2 6-4 8h0c2-5 4-10 5-15z" class="R"></path><path d="M306 640c1 0 2-1 3-2 4-3 8-8 11-12 2-3 4-5 6-7-11 19-29 33-43 49-2-2 1-2 2-4v-2l21-22z" class="K"></path><path d="M306 678c2 0 2 0 3-1 3-2 10-6 14-7l-6 4c-1 1-2 3-3 4v-1c-1 1-2 1-2 2l-1 1c-1 1-2 0-3 2 0 2 2 5 4 6h1 0l1-1h1l3-1h0l-18 10c-1-1 0-1 0-2l-1-1v-3l1-1c0-2 1-2 2-4l-2-2c2-2 4-4 6-5z" class="C"></path><path d="M306 678c2 0 2 0 3-1 3-2 10-6 14-7l-6 4c-6 3-11 7-15 11l-2-2c2-2 4-4 6-5z" class="E"></path><path d="M320 663c5-3 11-6 17-8v1h0l-1 1c-1 5-9 11-13 13-4 1-11 5-14 7-1 1-1 1-3 1l8-9v-1c0-1 6-3 6-5z" class="C"></path><defs><linearGradient id="Az" x1="382.89" y1="868.278" x2="317.551" y2="801.174" xlink:href="#B"><stop offset="0" stop-color="#bdbcbb"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#Az)" d="M329 788c3 9 8 17 15 24l1-1c2-1 3-4 4-7 3-4 7-6 10-9l1 1c-2 3-4 7-3 11l1 1c1 7 4 13 8 19l1 1c3 4 6 7 9 10l6 3 5 3c1 1 1 1 3 2 0 1-3 2-4 2-2-1-6-3-8-2h0c0 1 0 2 1 3 0-1 1 0 0-1 4 1 7 3 11 4 2 0 3 1 5 1 1 1 1 2 1 3-2 0-3 1-4 0h-3l-1-1h-2 0-2 0c6 3 14 3 21 3-1 0-1 0-2 1-10 3-20 5-30 6h0c-2 0-2 1-4 0h-3c0 2-1 2-2 3 2 0 5 0 6 1-10 1-20 0-29-4-13-4-28-16-34-28-2-2-3-5-4-8 2 0 7 3 9 2h1c2-1 3-1 5-3 1 1 2 1 3 2v-1c0-2-1-4-1-6-1-6-1-12-1-18l1-1h1c2 0 3 0 4-2 1-4 1-10 4-14z"></path><path d="M346 851c-4-3-7-8-9-12l9 8v4z" class="J"></path><defs><linearGradient id="BA" x1="356.155" y1="856.289" x2="346.232" y2="850.92" xlink:href="#B"><stop offset="0" stop-color="#4e4e4e"></stop><stop offset="1" stop-color="#727272"></stop></linearGradient></defs><path fill="url(#BA)" d="M346 847c4 4 9 6 14 9-1 0-2 0-3-1-1 1-1 2 0 3 0 1 1 2 1 2l-3-1-9-8v-4z"></path><path d="M378 846h-1c-2-1-5-4-7-6-1-2-2-3-4-4l1-1h1c0 1 1 1 2 1 2 1 3 4 6 2l6 3 5 3c1 1 1 1 3 2 0 1-3 2-4 2-2-1-6-3-8-2z" class="C"></path><path d="M368 835c0 1 1 1 2 1 2 1 3 4 6 2l6 3-1 1c-4 2-6-1-9-3-2-1-3-2-4-4z" class="L"></path><path d="M357 843c-12-8-18-21-23-34 4 6 8 13 13 19 3 4 7 8 10 12l5 5c-2 0-3-1-5-2h0z" class="C"></path><defs><linearGradient id="BB" x1="330.761" y1="850.922" x2="322.373" y2="803.287" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#BB)" d="M319 805l1-1h1c2 0 3 0 4-2 0 7 0 14 1 20v1c2 11 9 20 12 31-5-4-9-7-12-12-2-4-4-8-5-12v-1c0-2-1-4-1-6-1-6-1-12-1-18z"></path><path d="M321 829c1 1 1 1 1 3 1-1 1-1 2-1 1 1 3 6 3 8l-1 3c-2-4-4-8-5-12v-1z" class="H"></path><defs><linearGradient id="BC" x1="393.924" y1="867.899" x2="360.581" y2="848.121" xlink:href="#B"><stop offset="0" stop-color="#050506"></stop><stop offset="1" stop-color="#4b4a4a"></stop></linearGradient></defs><path fill="url(#BC)" d="M357 840c8 7 16 13 27 15 6 3 14 3 21 3-1 0-1 0-2 1-10 3-20 5-30 6-5-1-10-2-15-5 0 0-1-1-1-2-1-1-1-2 0-3 1 1 2 1 3 1h3l1-1 1 1h3c1 0 2 1 3 1v-1l-1-1h-1c-1-1-2-1-3-2v-1l-1 1c-1-2-1-3-1-4-1-2-6-4-7-6h0c2 1 3 2 5 2l-5-5z"></path><path d="M357 843h0c2 1 3 2 5 2l4 3c3 3 9 6 13 9-5 1-12 2-16-1l1-1 1 1h3c1 0 2 1 3 1v-1l-1-1h-1c-1-1-2-1-3-2v-1l-1 1c-1-2-1-3-1-4-1-2-6-4-7-6z" class="G"></path><defs><linearGradient id="BD" x1="366.702" y1="855.29" x2="304.358" y2="847.953" xlink:href="#B"><stop offset="0" stop-color="#9e9d9c"></stop><stop offset="1" stop-color="#f9f8f6"></stop></linearGradient></defs><path fill="url(#BD)" d="M307 837l-1-3c3 0 3 2 5 3 3 3 7 7 11 9 1 1 2 2 4 3 2 2 5 4 7 5 1 1 3 2 4 3s3 1 4 1c4 2 7 3 11 3 1 0 1 1 3 1 0 0 1 0 2-1h0 0c-1-1-2-1-2-2l3 1c5 3 10 4 15 5h0c-2 0-2 1-4 0h-3c0 2-1 2-2 3 2 0 5 0 6 1-10 1-20 0-29-4-13-4-28-16-34-28z"></path><path d="M354 656v-1c1 6 1 9-3 14l-2 3 1 1v1h1c1 1 2 3 3 4-2 2-5 5-8 6v1c-9 5-19 8-28 13-24 13-44 34-51 61-1 7-2 14-2 21h0v5 1c0 1 0 3 1 4v3c-2 1-3 2-4 3h-1c-1 0-2 1-3 1-1-3-1-6-3-8 0 1 4 11 3 12 0 3 2 6 1 10 1 1 3 5 3 6h-1c0-1-1-2-2-2-2-2-5-8-5-11-2-3-3-6-4-8-10-29-5-63 8-89 3-5 6-10 9-14 9-10 22-19 34-24 6-2 13-3 19-6 0 2-6 4-6 5v1l-8 9c-2 1-4 3-6 5l2 2c-1 2-2 2-2 4l-1 1v3l1 1c0 1-1 1 0 2l18-10c13-5 25-11 32-22 2-3 3-6 4-8z" class="O"></path><path d="M349 672l1 1v1h1c1 1 2 3 3 4-2 2-5 5-8 6 1-1 1-2 2-3h1c0-1-1-3-2-4v-1h-3c1-1 3-3 5-4z" class="D"></path><path d="M314 668v1l-8 9c-2 1-4 3-6 5l-4 3v-3s1-1 2-1c2-4 6-5 8-8v-2c2-2 6-3 8-4z" class="L"></path><defs><linearGradient id="BE" x1="267.901" y1="716.616" x2="254.535" y2="714.875" xlink:href="#B"><stop offset="0" stop-color="#909091"></stop><stop offset="1" stop-color="#aeadaa"></stop></linearGradient></defs><path fill="url(#BE)" d="M274 696h0c2-2 4-4 6-5h1c-7 8-13 15-18 25l-3 7c-1 1-1 2-2 3-3 7-5 14-6 21v8h-2c0-2 1-5 0-7-1-3 1-7 0-10 0-4 2-8 3-12 5-11 11-23 21-30z"></path><path d="M252 747l-1-2c1 0 1-1 1-2-1-2-1-3-1-5 1-3 2-6 4-9l1-1c0-1 1-1 2-2-3 7-5 14-6 21z" class="I"></path><path d="M320 663c0 2-6 4-6 5-2 1-6 2-8 4-12 6-23 14-32 24-10 7-16 19-21 30-1 4-3 8-3 12 1 3-1 7 0 10 1 2 0 5 0 7h2v20l3 14c0 1 4 11 3 12 0 3 2 6 1 10 1 1 3 5 3 6h-1c0-1-1-2-2-2-2-2-5-8-5-11-2-3-3-6-4-8-10-29-5-63 8-89 3-5 6-10 9-14 9-10 22-19 34-24 6-2 13-3 19-6z" class="P"></path><path d="M250 738c1 3-1 7 0 10 1 2 0 5 0 7h2v20l3 14c0 1 4 11 3 12 0 3 2 6 1 10 1 1 3 5 3 6h-1c0-1-1-2-2-2-2-2-5-8-5-11-1-2-1-4-2-5l-2-8c-1-4-2-9-2-12-2-14-2-28 2-41z" class="F"></path><path d="M250 755h2v20c-1 1-1 2-1 4 1 3 1 5 1 8s1 6 1 10l-1 2-2-8c0-1 1-2 1-3 0-3-1-5-1-8v-25z" class="I"></path><path d="M252 799l1-2c0-4-1-7-1-10s0-5-1-8c0-2 0-3 1-4l3 14c0 1 4 11 3 12 0 3 2 6 1 10 1 1 3 5 3 6h-1c0-1-1-2-2-2-2-2-5-8-5-11-1-2-1-4-2-5z" class="G"></path><path d="M354 656v-1c1 6 1 9-3 14l-2 3c-2 1-4 3-5 4-4 3-8 6-13 8-7 4-15 7-22 11-23 14-42 34-48 60-2 8-3 17-3 26-1-3-1-6-1-9-1-16 2-30 9-44l3-7h0c6-14 16-25 27-35l4-3 2 2c-1 2-2 2-2 4l-1 1v3l1 1c0 1-1 1 0 2l18-10c13-5 25-11 32-22 2-3 3-6 4-8z" class="P"></path><defs><linearGradient id="BF" x1="279.361" y1="697.997" x2="302.555" y2="692.15" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#BF)" d="M300 683l2 2c-1 2-2 2-2 4l-1 1v3l1 1c0 1-1 1 0 2-5 3-10 6-14 9-3 3-5 6-8 7h-1c2-1 3-2 4-3l1-1c0-2-1-3-1-4-3 3-5 6-7 10-1 2-2 5-5 7 6-14 16-25 27-35l4-3z"></path><path d="M646 147h0 1c4-1 5-4 8-7 2 0 5 1 7 2 2-1 4-1 5 0l9 1h1c22 3 44 18 58 35l11 17c2-1 2-1 3-3h0c1-4 3-7 5-11 1-2 2-6 4-8l6-12 52 1c-6 3-12 6-18 10-10 6-18 16-24 26-7 13-11 26-15 39l-13 39-47 141-21 60-52 157-24 73c-7 19-14 39-19 59-1 4-2 9-3 13l-3-8h0c-3-8-7-17-9-25-2-3-2-7-4-10s-4-4-7-6c2-2 3-4 4-7 1-2 0-3 0-5l-11-28c-5-11-8-22-13-33-2 0-3-6-4-8l-3-3-1-2c0-2-1-4-2-6h0l-1-2-9-22-24-63-88-235-31-83c-3-8-8-16-9-25-1-4 2-10 4-14 7-14 20-26 35-31l4-1-156-1h-41c-9 0-20-1-30 1 6 2 11 4 16 7 20 10 32 26 40 46l20 53 30 81 49 133 40 107 51 137 8 22 6 16c1 2 2 5 2 7v1 4h-1l1 4c1 1 0 5-1 7l-3 3-34-90v-1l-24-62-1-4-19-53-10-25c0-2 0-3-1-5h-1c-1-1-3-2-4-3-1-5-2-10-2-15h0l-11-31c-2-5-3-12-6-16l-15-40-7-19c-1-3-2-7-4-10l-52-141-13-35c-4-10-7-20-12-29-4-9-10-16-17-22-17-16-39-17-62-17h0v-23h310v23c-22 0-49-2-66 14-6 6-10 14-12 22l167 443 1-1c0-4 2-10 5-13 2-4 5-5 10-6 3 0 6 1 9 2-2-4-6-8-10-10-2-1-5-1-7 0-1 1-1 1-2 1 1-2 3-4 3-6 1-3 3-11 1-14 0-1-2-3-3-3-3-1-5 1-7 2 3-3 6-6 11-6 4 0 8 1 11 1 15 1 26 0 38 11l3 5c1 2 1 4 1 6l17-53 1-2 11-32 4-13 2-7 2-6 3-9 1-1 1-4 1-3 6-17 2-9c4-9 7-18 10-28l21-63 23-70c5-15 11-30 14-45 2-11 3-22 1-33-6-28-25-47-48-63l13 22c4 6 10 13 13 20l-11-11c0-1-1-2-2-3h-1 0l-2-4h0c0 2 0 3 1 4 0 3 0 7-1 10 0 5-2 10-5 13-2 1-3 2-5 2 2-6 0-12-2-18-2-5-4-7-8-11v-1h0c-1-1-1-1-2-1l-1 2-2-2v-4l-1-1c-2-10-6-19-13-27l-4-5z" class="M"></path><path d="M345 547c-1-5-2-10-2-15l7 18h-1c-1-1-3-2-4-3z" class="H"></path><path d="M528 633l9 24c-2 0-3-6-4-8l-3-3-1-2c0-2-1-4-2-6l1-1v-4z" class="G"></path><path d="M662 142c2-1 4-1 5 0l9 1h1v1l1 1 10 4v1c-6-3-11-5-16-4l-2 1c0 1 2 2 3 3l20 10v1c-7-4-15-6-22-11-3-2-5-6-9-8z" class="C"></path><path d="M677 197l1 1c2 1 2 5 2 8 0 1 0 0 1 1v4h0 4v-1-1-3-4-1h0c0-2 0-3-1-4v-1-1c0-2 0-2-1-4l-2-5-3-6 1-1c1 1 2 1 2 3 0 0 1 1 1 2 1 2 2 4 2 7l2 3v2 1l1 1c0 2 1 3 1 6l1-1-1-1 1-2c0 5-2 10-5 13-2 1-3 2-5 2 2-6 0-12-2-18z" class="F"></path><path d="M749 192h0c1-4 3-7 5-11 1-2 2-6 4-8h1 1c4-1 8 0 11 0s7-1 9 0c-13 11-20 25-28 40-1-5-2-10-4-15l-2-3c2-1 2-1 3-3z" class="N"></path><path d="M757 194h1l1 1c-2 2-4 3-6 5h-1l-1-1v-3c2-1 4-1 6-2z" class="I"></path><path d="M757 181h11l-3 1c-2 1-4 1-5 1 1 0 2 1 4 1 1-1 0-1 1 0l1 1-4 5v1l-1 1h-2c-2 1-4 1-7 0v-1h1 2c1 0 4 0 6-2h0l-8 1c1-3 2-7 4-9z" class="D"></path><path d="M693 160c21 13 38 33 44 57 4 17 3 35-2 52-3 13-8 27-13 41l-18 54-64 192-46 138-9 29c-2 6-4 12-7 17h0c-3-4 12-44 14-51l54-162 43-128 28-85c7-19 14-39 18-59 3-17 1-36-7-52-7-14-15-24-27-34-2-2-4-5-7-7l-1-1v-1z" class="I"></path><path d="M559 631c3 0 6 1 9 2-2-4-6-8-10-10-2-1-5-1-7 0-1 1-1 1-2 1 1-2 3-4 3-6 1-3 3-11 1-14 0-1-2-3-3-3-3-1-5 1-7 2 3-3 6-6 11-6 4 0 8 1 11 1 15 1 26 0 38 11l3 5c1 2 1 4 1 6l-2 6-7 19-17 51c-3 10-6 21-10 30v-1l-3-9-3-7-22-58 1-1c0-4 2-10 5-13 2-4 5-5 10-6z" class="O"></path><path d="M569 610l2-1c3-1 6-1 9-2 1 1 2 1 3 1l-2 1c-2 1-3 1-5 2h0c-2-1-3-1-5-1h-2z" class="B"></path><path d="M580 607h10l6 3c1 0 3 1 4 1s2-1 3-2l3 5-5 1c-6-5-11-5-18-7-1 0-2 0-3-1z" class="C"></path><path d="M578 628c2 0 3 1 4 2s2 1 3 2c1 0 1 0 1 1v1l1 1c1-1 1-1 2-1v-1c0-1 0-1-1-2-1-3-4-7-5-10h0l-1 1c-1-4-6-8-10-10-1 0-3-1-5-2h2 2c2 0 3 0 5 1h0c7 5 13 16 14 24 0 2 0 2-2 2-3 0-4-2-7-4l-1-2-2-3zm-4 0c1 2 3 4 3 6 0 4 1 7 0 10h-1 0c-2-2-4-4-7-5h-2c-5-2-9-4-14-1-2 0-4 2-5 3v1c0 1-1 2-1 4-1 1-2 2-3 4 0-4 2-10 5-13 2-4 5-5 10-6-1 1-2 2-4 2 0 0-1 1-2 1-1 1-4 3-4 5l1-1h1c2-1 2-2 4-2 1-1 4 0 5 0v-2c5 0 6 3 9 4s4 1 6 4h1c-1-1-1-2-1-2v-2-1c-1-3-1-6-1-9z" class="N"></path><path d="M574 628l-1-1c-1-3-3-6-7-8v-1c-2-2-4-2-7-3 3-1 6 1 9 2 5 3 7 7 10 11l2 3c-1 1-1 2-1 4 1 7 5 13 1 20-1-1-2-4-4-5-2-3-3-5-6-7 0-1-2-2-2-2-3-2-9-4-13-3-3 1-4 2-7 4v-1c1-1 3-3 5-3 5-3 9-1 14 1h2c3 1 5 3 7 5h0 1c1-3 0-6 0-10 0-2-2-4-3-6z" class="B"></path><path d="M577 634c2 4 3 9 2 13 0 1-1 1-1 2-3-1-4-4-5-5-2-2-4-4-6-5h2c3 1 5 3 7 5h0 1c1-3 0-6 0-10z" class="I"></path><path d="M581 609c4 2 7 7 9 12l2 6c0 1 1 3 1 4 1 6 2 14 0 20l-2 1h-2c-2-2-2-4-3-7l-3-7c0-1-1-3-2-5 3 2 4 4 7 4 2 0 2 0 2-2-1-8-7-19-14-24 2-1 3-1 5-2z" class="J"></path><path d="M592 627c0 1 1 3 1 4 1 6 2 14 0 20l-2 1h-2c-2-2-2-4-3-7h3c1-1 2-1 2-3 1-1 1-4 0-6h0c2-3 0-6 1-9z" class="E"></path><defs><linearGradient id="BG" x1="579.832" y1="621.247" x2="602.823" y2="627.447" xlink:href="#B"><stop offset="0" stop-color="#242424"></stop><stop offset="1" stop-color="#525252"></stop></linearGradient></defs><path fill="url(#BG)" d="M583 608c7 2 12 2 18 7l5-1c1 2 1 4 1 6l-2 6-7 19c-2-4 0-10-3-15 0 1 0 2-1 3v-1l-1-1c0-1-1-3-1-4l-2-6c-2-5-5-10-9-12l2-1z"></path><path d="M603 625c-1 0 0 1-1 2-2-3 1-6-1-9h0l1-1c1 3 2 5 1 8z" class="Q"></path><path d="M606 614c1 2 1 4 1 6l-2 6-2-1c1-3 0-5-1-8l-1-2 5-1z" class="J"></path><defs><linearGradient id="BH" x1="571.09" y1="676.844" x2="594.746" y2="678.175" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#242423"></stop></linearGradient></defs><path fill="url(#BH)" d="M593 631l1 1v1c1-1 1-2 1-3 3 5 1 11 3 15l-17 51c-3 10-6 21-10 30v-1l-3-9c5-11 12-22 12-34 2-11 0-22-4-32 2 1 3 4 4 5 4-7 0-13-1-20 0-2 0-3 1-4l1 2c1 2 2 4 2 5l3 7c1 3 1 5 3 7h2l2-1c2-6 1-14 0-20z"></path><path d="M580 655c4-7 0-13-1-20 0-2 0-3 1-4l1 2c1 2 2 4 2 5l3 7c1 3 1 5 3 7h2v3h-1-1c-1-1-2-1-3-3 0 0 0-1 1-2h-1v1h-1v2 12c-1-1-4-4-4-5l-1-5z" class="H"></path><path d="M580 655c4-7 0-13-1-20 0-2 0-3 1-4l1 2c1 2 2 4 2 5v1c0 3 2 6 2 8l-2-2h0c0 5 1 9-1 13l-1 2-1-5z" class="K"></path><path d="M548 642c3-2 4-3 7-4 4-1 10 1 13 3 0 0 2 1 2 2 3 2 4 4 6 7 4 10 6 21 4 32 0 12-7 23-12 34l-3-7-22-58 1-1c1-2 2-3 3-4 0-2 1-3 1-4z" class="T"></path><path d="M548 642c3-2 4-3 7-4 4-1 10 1 13 3 0 0 2 1 2 2 1 3 4 7 2 11l-2-4c-2-4-6-7-11-8s-8 1-12 4c0-2 1-3 1-4z" class="L"></path><defs><linearGradient id="BI" x1="579.204" y1="674.082" x2="571.609" y2="646.747" xlink:href="#B"><stop offset="0" stop-color="#959595"></stop><stop offset="1" stop-color="#c5c4c3"></stop></linearGradient></defs><path fill="url(#BI)" d="M570 643c3 2 4 4 6 7 4 10 6 21 4 32v-7h-5c1-7 0-14-3-21 2-4-1-8-2-11z"></path><defs><linearGradient id="BJ" x1="579.953" y1="706.879" x2="566.366" y2="679.235" xlink:href="#B"><stop offset="0" stop-color="#5f6060"></stop><stop offset="1" stop-color="#908f8e"></stop></linearGradient></defs><path fill="url(#BJ)" d="M575 675h5v7c0 12-7 23-12 34l-3-7c1-4 3-8 5-11 3-8 4-15 5-23z"></path><path d="M442 776l-17-45-45-121-56-154-26-67-33-91-13-35-10-27c-6-16-12-33-23-46-12-14-30-23-48-26-4-1-13 0-16-3 2-2 8-2 11-2 16-1 33-1 49 0l76-1h88l50 1c3 0 8 0 11 2l-1 1c-3 1-7 1-11 1-11 0-22 2-32 8-12 6-23 18-27 31v1c-1 2-1 5-1 7 2 6 5 11 7 16l10 30 28 73 84 222 18 48 13 34v4l-1 1h0l-1-2-9-22-24-63-88-235-31-83c-3-8-8-16-9-25-1-4 2-10 4-14 7-14 20-26 35-31l4-1-156-1h-41c-9 0-20-1-30 1 6 2 11 4 16 7 20 10 32 26 40 46l20 53 30 81 49 133 40 107 51 137 8 22 6 16c1 2 2 5 2 7v1 4h-1z" class="I"></path><path d="M443 771c0-2-1-5-2-7l-6-16-8-22-51-137-40-107-49-133-30-81-20-53c-8-20-20-36-40-46-5-3-10-5-16-7 10-2 21-1 30-1h41l156 1-4 1c-15 5-28 17-35 31-2 4-5 10-4 14 1 9 6 17 9 25l31 83 88 235 24 63 9 22h-1 0c-4-1-7 0-10 1-1 1-3 2-5 2 3-5 7-8 11-12 0-1 0-2-1-3-4 0-8 3-11 5h0c-1-3-1-6-2-9 0-3-1-5-2-8l-31-83-11-29c-2-5-3-11-6-15l-1-1-1-1c0 11-1 22 0 33 0 19 3 39 6 58l1 10c0 1-1 3 0 4l1 1v3 11l2 24c0 3-1 7 1 10v1c-2 4-2 10-3 14v3l2 1c1-1 2-1 3-2s3-1 5-1c2 1 3 4 4 5 0 2-1 3-1 4 3 0 6 0 9 2 1 3 1 5 1 7 0-1 1-2 1-2h2c0 2-1 2 1 4 1 1 2 1 5 1 0 0 1-1 2-1v-7c1 3 0 6 1 8v8 4 2c-1 2-3 4-5 4-3 0-5-1-7-2-6 4-15 12-17 20v1l-7 9-1 1c-1 0-2 1-3 2v-1l-2 1c-1 0-3 0-4-1 0 0-1-2-1-3-1 0-1 1-2 1-4 5-4 12-5 18v4c-1 2-4 2-4 6 1 4 10 13 9 15 0 1-1 3-2 4-1 2-2 5-2 7-1-1-1-2-2-3z" class="M"></path><path d="M352 348c1 0 2-1 3-1h1c1 1 1 3 1 5v1c-1 0-2 0-2-1-2 0-3-1-4-2l1-2z" class="N"></path><path d="M352 348h2c1 1 0 3 1 4-2 0-3-1-4-2l1-2z" class="L"></path><path d="M416 370c3 5 5 11 7 16l-1 1v-1-1h-1 0 0v4c-2-6-3-11-6-17 1-1 1-1 1-2z" class="C"></path><path d="M382 399c2 2 4 7 5 10l6 12c1 2 2 5 3 8-7-9-11-20-14-30z" class="D"></path><path d="M347 362c1 0 1 0 2 1 0 2-3 4-4 6-2 4-3 8-4 12-1 0-1 0-2-1-1 0-1 0-1-1 1-1 2-3 2-5 2-4 4-9 7-12z" class="F"></path><path d="M371 512h0c-1-4 0-7 2-11 1-2 3-3 5-4 3 0 6 1 8 3l-2 1c-2-1-4-1-6 0-3 1-4 4-5 7-1 2-1 6 0 9l-1-4-1-1z" class="N"></path><defs><linearGradient id="BK" x1="362.093" y1="370.69" x2="370.034" y2="365.552" xlink:href="#B"><stop offset="0" stop-color="#7c7a7b"></stop><stop offset="1" stop-color="#90918f"></stop></linearGradient></defs><path fill="url(#BK)" d="M364 356l2-1c-1 4 0 7 0 11 1 3 2 9 3 12v1c0 1 0 1-2 1v-1c-5-6-4-15-3-23z"></path><path d="M382 364c1 1 1 3 1 4v3l-1 1c-2 5-1 11 0 17-3-5-4-10-5-16 1-4 2-6 5-9z" class="E"></path><path d="M406 448c1-1 3-1 4 0 3 2 5 6 5 9 1 2 2 6 1 8-1 0-1 0-1-1-1-2-2-4-4-6-1-2-6-5-6-7s0-2 1-3z" class="I"></path><defs><linearGradient id="BL" x1="349.721" y1="376.311" x2="362.258" y2="387.537" xlink:href="#B"><stop offset="0" stop-color="#6c6b6b"></stop><stop offset="1" stop-color="#a1a09f"></stop></linearGradient></defs><path fill="url(#BL)" d="M361 391c-3-3-5-8-9-11l-3-2v-2c1-1 1-2 2-2 4 2 8 6 11 9l-1 1v1c1 2 1 4 0 6h0z"></path><path d="M339 354l2-4v-2c1-1 1-1 1-2h1 0c0 2 0 4 1 5 0 1 1 2 1 3s-1 2-1 2c-1 4-2 7-3 9h-1 0l1-2v-1h-4v1c-1 1-1 3-3 4h-1c1-1 3-2 3-4-1-1-1-2-1-2l-1-1 2-2h0v2 1c1-2 1-4 2-7 0-2 0-3-1-6 2 2 2 4 2 6l-1 1v3c0-1 0-1 1-2v-2z" class="O"></path><defs><linearGradient id="BM" x1="337.031" y1="412.499" x2="328.748" y2="420.241" xlink:href="#B"><stop offset="0" stop-color="#939291"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#BM)" d="M336 430c-1-1-1-2-2-3-2-5-3-10-5-16-1-2-2-4-1-6 2-1 2 0 4 0 0 3 1 5 3 7h3v-1l1 1h5 1l-11 6c1 2 3 5 4 7 0 2 0 3-2 5z"></path><path d="M361 391h0c1-2 1-4 0-6v-1l1-1 2 2c1 2 2 5 3 8 1 8 1 16 2 24 1 5 1 13 0 18l-3 17h0c-1-4 1-10 1-13 1-7 2-15 1-21-1-7-2-14-4-20l-3-7z" class="F"></path><path d="M361 391h0c1-2 1-4 0-6v-1l1-1 2 2c-3 4 1 8 0 13l-3-7z" class="C"></path><path d="M371 512l1 1 1 4c0 3 1 7 4 10 2 1 4 1 6 2 0 9-1 17-1 26l-2-1c1-4-1-9-2-12l-5-17c-1-4-2-8-2-13zm-14-115l3 4c5 12 5 25 3 37 0 9-2 16-2 25 0 5 0 11 1 16 1 4 3 9 4 13 1 2 2 4 1 6-2-7-5-15-7-22-3-11-1-23 0-35 2-8 3-15 2-22l-5-22zm-12 13v1h1l2-4 1-1c1 8 2 17-3 24-1 1-2 2-3 2-2 0-4 0-6-1l-1-1c2-2 2-3 2-5-1-2-3-5-4-7l11-6h-1l1-2z" class="D"></path><path d="M345 412c0 3-1 7-1 10 0 2 0 4-1 6h-3c-1-1-1-2-2-3-1-2-3-5-4-7l11-6z" class="O"></path><path d="M421 389v-4h0 0 1v1 1l1-1c1 3 2 7 3 10l4 16 16 41c2 7 6 14 6 21l1-1c0 2 0 4-1 5v-2c-6-12-10-25-15-37l-9-26c-3-8-6-16-7-24z" class="F"></path><path d="M337 384c3 1 5 3 7 5 2 3 2 7 3 11 1 2 2 4 2 6l-1 1-2 4h-1v-1l-1 2h-5l-1-1v1h-3c-2-2-3-4-3-7 0-2-1-6-1-8-1-3-1-7 1-10 1-2 3-3 5-3z" class="L"></path><path d="M335 403c1-1 2-1 3-1 2 0 3 1 4 2 1 2 2 3 2 4l1 2-1 2h-5l-1-1c0-3-1-6-3-8z" class="E"></path><path d="M335 403c1-1 2-1 3-1v2c1 2 1 4 2 5 2 0 3 0 4-1l1 2-1 2h-5l-1-1c0-3-1-6-3-8z" class="B"></path><path d="M332 405c0-2-1-6-1-8-1-3-1-7 1-10 1-2 3-3 5-3h0l-2 2c-2 2-3 6-3 9 0 2 0 6 2 7l1 1h0c2 2 3 5 3 8v1h-3c-2-2-3-4-3-7zm21-200l51 138-2 2c0-3-2-6-3-9l-5-13-22-57-13-36c-2-5-4-10-5-15 0-2 0-4-1-6v-1h0c-1-2-1-2 0-3z" class="F"></path><path d="M389 463l-8-5-10-56 1-1 1 1 1 1 2-2c2 2 5 19 6 23 2 7 5 14 9 21h6c1 6 1 12 4 17h0-1-3 0-2c-1 1-1 1-1 2l-1 1c-1 0-2-2-3-3l-1 1z" class="D"></path><defs><linearGradient id="BN" x1="288.721" y1="316.93" x2="322.186" y2="305.739" xlink:href="#B"><stop offset="0" stop-color="#999998"></stop><stop offset="1" stop-color="#c6c5c4"></stop></linearGradient></defs><path fill="url(#BN)" d="M264 243c8 6 15 13 20 21l12 26 11 28 7 19c3 8 5 16 8 24 1 2 3 5 6 7 1 0 3 0 5-1h1c2-1 2-3 3-4v-1h4v1l-1 2h0c-3 7-7 7-13 9-2 2-4 5-5 8h0c-1 4-2 7-2 10-2-7 0-13 3-20-4-9-7-18-11-27l-9-25c-7-21-14-41-26-59-4-6-9-13-16-18h3z"></path><defs><linearGradient id="BO" x1="402.171" y1="487.062" x2="369.463" y2="480.176" xlink:href="#B"><stop offset="0" stop-color="#999998"></stop><stop offset="1" stop-color="#bdbcbb"></stop></linearGradient></defs><path fill="url(#BO)" d="M389 463l1-1c1 1 2 3 3 3l1-1c0-1 0-1 1-2h2 0 3l-3 1c-1 2-1 2-1 4 1 0 1 1 2 1v1c0 6 1 13 2 18 1 3 2 7 1 10-1 5-5 8-6 13l-1 1v-1c0-3-1-6-2-8s-2-6-3-7c-2-2-7-3-10-3l-2-1c-1 0-2-4-3-5s-3-2-4-4c-1 0-1-1-1-2 0-2 2-3 4-4l2 1c2-3 3-9 5-12 1 0 2 1 3 2 1 0 4 3 4 4s-1 3-2 4v5l3 3c2 0 3 0 4-1 2-2 2-4 3-7 0-5-2-8-6-12z"></path><defs><linearGradient id="BP" x1="431.441" y1="417.41" x2="396.877" y2="418.69" xlink:href="#B"><stop offset="0" stop-color="#7d7d7d"></stop><stop offset="1" stop-color="#a09f9e"></stop></linearGradient></defs><path fill="url(#BP)" d="M431 462l-1-3c-1-12-5-26-10-38-3-6-7-12-10-19l-5-8c0-1-1-2-1-3-2-1-5-1-7-3-1-2-2-6-2-8s0-3 2-5c2-1 3-1 5-1 2 2 3 4 4 7l3-1c1 0 4-1 6 0 2 2 4 9 4 13-1 2-1 5-4 7 0 0-1 1-1 2l1 1 4 4v-1-2h0c2 2 3 4 5 7 6 14 7 31 10 46-1 1-1 3-1 4l-2 1h0z"></path><path d="M415 403l4 4c0 1 0 1 1 1l3 8 1 5c-1 0-1 0-1-1h0v-2c-1-1-1-3-2-4v1h-1c-1-2-2-2-2-4l2-1c0-3-4-4-5-7z" class="B"></path><path d="M497 666c1 3 0 6 1 8v8 4 2c-1 2-3 4-5 4-3 0-5-1-7-2-6 4-15 12-17 20v1l-7 9-1 1c-1 0-2 1-3 2v-1l-2 1c-1 0-3 0-4-1 0 0-1-2-1-3-1 0-1 1-2 1 1-4 3-7 4-11 2-3 4-5 6-7 3-4 5-9 9-12 6-4 13-7 17-15l1-4c0-1 1-2 1-2h2c0 2-1 2 1 4 1 1 2 1 5 1 0 0 1-1 2-1v-7z" class="N"></path><path d="M486 671c0-1 1-2 1-2h2c0 2-1 2 1 4-2 0-2 0-3 1s-2 1-2 1l1-4z" class="R"></path><path d="M451 719c2 0 4-1 6 0l1 1v2l-2 1c-1 0-3 0-4-1 0 0-1-2-1-3z" class="B"></path><path d="M497 666c1 3 0 6 1 8v8 4 2c-1 2-3 4-5 4-3 0-5-1-7-2s-4-2-5-3-1-1-1-2l1-1c2 1 5 2 8 2l2-1c3-3 5-7 5-11h-1s1-1 2-1v-7z" class="G"></path><path d="M380 570L262 249c4 5 7 12 9 18l8 22 24 63 16 44c4 12 8 24 14 35 1 2 2 5 4 7 1 1 2 2 4 2 4-1 7-6 10-9 0 6-2 12-2 18-1 9 0 20 2 30 1 6 3 12 6 19l7 20c4 11 8 22 13 33 0 2 0 3 1 4v1c0 1 0 2 1 4s2 7 2 9l-1 1z" class="C"></path><path d="M235 188c-4-7-10-11-16-15l153-1c-5 4-9 9-12 14l-3 6c-5-3-9-7-14-10-12-7-26-8-39-7-15 1-32 8-42 19-5 6-8 12-12 19l-8-14-7-11z" class="N"></path><path d="M235 188l2-1 2 3v1c1 0 1 0 1 1l3 3h-1l-1-1v1h0c1 1 1 2 1 4l-7-11z" class="L"></path><path d="M234 180c4 0 24 0 27 1-5 1-23 2-27-1z" class="G"></path><path d="M453 473h1l3 12-1-1-1-1c0 11-1 22 0 33 0 19 3 39 6 58l1 10c0 1-1 3 0 4l1 1v3 11l2 24c0 3-1 7 1 10v1c-2 4-2 10-3 14v3l2 1c1-1 2-1 3-2s3-1 5-1c2 1 3 4 4 5 0 2-1 3-1 4-2 3-4 7-6 10-3 3-7 5-10 8-3 4-6 9-9 13-2 2-5 4-7 7s-4 11-7 13l-2-1c-3 2-5 6-6 9-1-9 2-16-1-24l1-1c1 0 3-8 7-11v1c5 1 13-1 17-3l2-2-1-2c-3-7-2-16-2-24v-6l1-2c0-1 1-2 1-4h0l1-1c0-3 0-8-1-11-1-6-2-12-4-17l1-1 4 19c6-29 3-59-1-88-2-13-4-26-4-39 0-9 1-18 2-27 1-1 1-3 1-5z" class="I"></path><path d="M350 215v-2c2 0 3 1 4 2 1 5 3 10 5 15l13 36 22 57 5 13c1 3 3 6 3 9l2-2c3 7 6 15 10 23 0 1 2 3 2 4s0 1-1 2c-1-2-3-4-5-5-4-4-10-5-15-5-5 1-9 5-12 9v-3c0-1 0-3-1-4-3 3-4 5-5 9-1 3-2 4-5 5l-2-1-1 1c-1-3-2-9-3-12 0-4-1-7 0-11l-2 1c-1-2-4-4-5-6-3-3-2-8-6-10-4 0-6 1-10 4 0-2 1-4 1-6 2-2 4-3 7-4 2 0 5 0 6-2 1-5-1-10-3-15l-3-12c-1-1-2-4-2-5-3-10-5-20-8-31l-3-11c2-2 3-2 3-5s-1-6-1-9v-1c-1-4-1-8-1-12-1-4-1-13 2-16h4l4 4 1-4z" class="M"></path><path d="M361 311v-1l1 1c0-2-1-3-1-5h0l4 8c-1 2 1 5 0 6l-4-9z" class="I"></path><path d="M375 361l6 1 1 2c-3 3-4 5-5 9-1 3-2 4-5 5l-2-1-1 1c-1-3-2-9-3-12l2 2 1 8c1 0 2 1 4 2v-1l2-1c0-1 1-2 1-3v-1l1 1v-3-1h0c1-2 1-2 1-3h1l-1-1v-1c1-1 1-1 1-2-2 0-2 0-4-1z" class="N"></path><path d="M375 361l-1-1c0-2 0-2 1-4 1-1 1-1 3-1 2 1 4 2 5 3 0 2 0 2-2 4l-6-1zm-14-50c-2-4-3-7-3-10-2-7-3-15 1-21-1 9 0 17 2 26h0c0 2 1 3 1 5l-1-1v1z" class="C"></path><path d="M341 253l11 50v3l-1-1h0c-1-1-2-4-2-5-3-10-5-20-8-31l-3-11c2-2 3-2 3-5z" class="F"></path><defs><linearGradient id="BQ" x1="339.866" y1="225.719" x2="357.08" y2="252.1" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#919190"></stop></linearGradient></defs><path fill="url(#BQ)" d="M352 254c-4-10-10-20-10-30v-1h2c6 3 13 22 15 28-2 2-5 1-7 3z"></path><defs><linearGradient id="BR" x1="371.571" y1="254.378" x2="360.797" y2="313.598" xlink:href="#B"><stop offset="0" stop-color="#8e8d8c"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#BR)" d="M352 254c2-2 5-1 7-3l18 55 9 23 1 1-1 1c-2-3-3-6-5-9-2-5-5-10-7-15-2-6-3-11-6-16l-9-19-7-18z"></path><path d="M352 303c2 7 4 13 7 19 2 4 11 23 11 25 0 1 0 1-1 2l-3 6h0l-2 1c-1-2-4-4-5-6-3-3-2-8-6-10-4 0-6 1-10 4 0-2 1-4 1-6 2-2 4-3 7-4 2 0 5 0 6-2 1-5-1-10-3-15l-3-12h0l1 1v-3z" class="C"></path><path d="M365 314v-1c0-4-2-9-1-12 2 3 2 9 2 12 1 8 7 18 14 23l2 1 4 4c5 3 11 5 16 4l2-2c3 7 6 15 10 23 0 1 2 3 2 4s0 1-1 2c-1-2-3-4-5-5-4-4-10-5-15-5-5 1-9 5-12 9v-3c0-1 0-3-1-4l-1-2c2-2 2-2 2-4l1 1h0c6-3 10-3 16-3-5-2-9-4-14-7-9-7-17-19-21-29 1-1-1-4 0-6z" class="R"></path><defs><linearGradient id="BS" x1="300.867" y1="187.298" x2="326.077" y2="367.549" xlink:href="#B"><stop offset="0" stop-color="#c3c2c1"></stop><stop offset="1" stop-color="#fefbf5"></stop></linearGradient></defs><path fill="url(#BS)" d="M261 243c-1 0-1-1-2-1-1-2 0-5 0-7 0-5 1-10 3-15 5-13 15-24 28-30s29-5 42 0c6 3 18 9 21 15-1 1-1 1 0 3h0v1c1 2 1 4 1 6-1-1-2-2-4-2v2l-1 4-4-4h-4c-3 3-3 12-2 16 0 4 0 8 1 12v1c0 3 1 6 1 9s-1 3-3 5l3 11c3 11 5 21 8 31 0 1 1 4 2 5l3 12c2 5 4 10 3 15-1 2-4 2-6 2-3 1-5 2-7 4l-1 2v1h-1v1 2 1h-1v2 1c-1 1-1 2-1 3l-1 1v2 2c-1 1-1 1-1 2v-3l1-1c0-2 0-4-2-6 1 3 1 4 1 6-1 3-1 5-2 7v-1-2h0l-2 2 1 1s0 1 1 2c0 2-2 3-3 4-2 1-4 1-5 1-3-2-5-5-6-7-3-8-5-16-8-24l-7-19-11-28-12-26c-5-8-12-15-20-21h-3z"></path><path d="M298 228c2 0 2 0 3 1v3h-3c-1-1-1-2-1-2l1-2zm28 8l-4-4c0-2 0-2 1-3s2-1 3-1c0 1-1 2-1 3s1 3 1 5zm-50-9c1-2 2-3 3-4 2 1 2 1 3 2 0 2 0 2-1 4l-3 1-2-3zm69-12l-1-2c1-1 1-2 3-2h1c2 2 2 2 2 4l-1 4-4-4z" class="E"></path><path d="M298 252h2v2l-1 1c-1 0-2 0-2 1-1 0-2 1-2 2v4 11l-3-10h0l1 1 1 4v1-4-1c0-3 0-10 1-12l2 1v-1h1z" class="O"></path><path d="M298 203h2c2 2 3 5 3 8h-1c-1-1-3-1-4-3-1-1-2-2-2-4l2-1zm-14 13c1 0 2 0 3-1 2 2 2 5 2 8h-1s-1 0-1-1c-2-1-4-2-4-5l1-1z" class="K"></path><path d="M298 252c-1-3-2-6-2-9l1-1c2 0 2 0 3 1 1 2 2 5 1 7l-1 1v1h-2z" class="E"></path><path d="M313 199c1-1 2-1 3-1 2 2 1 6 1 9l1 1h-1c-1-1-2-1-3-1-1-2-2-3-2-5s0-2 1-3z" class="H"></path><path d="M316 291c3 4 4 11 6 16 3 9 7 16 10 24-10-12-13-25-16-40z" class="R"></path><path d="M328 254h1c9 21 15 43 16 66v-2l-17-64z" class="D"></path><path d="M328 356v-6l-1-1c0-2 1-3 2-4s2-2 5-2c2 1 2 3 3 5 1 3 1 4 1 6-1 3-1 5-2 7v-1-2h0l-2 2h-3c-1-1-2-2-3-4z" class="N"></path><path d="M330 348c1-1 1-2 3-2 1 1 2 2 2 4v4 2c0-2-1-5-2-6-1-2-2-2-3-2z" class="D"></path><path d="M330 348c1 0 2 0 3 2 1 1 2 4 2 6 0 0 0 1 1 2l-2 2h-3c-1-1-2-2-3-4 0-3 0-5 2-8z" class="B"></path><path d="M308 255v10l1 17c0 6 2 12 3 18 2 8 3 15 6 22 1 5 4 9 6 13l-1 1c-1-1-2-3-3-5-8-12-10-27-12-41-2-12-3-23 0-35z" class="D"></path><path d="M326 228c4 1 6 5 8 8s3 6 6 8c0 3 1 6 1 9s-1 3-3 5c-3-8-7-15-12-22 0-2-1-4-1-5s1-2 1-3z" class="G"></path><path d="M264 243v-1c0-4 5-11 8-14 2-1 3-2 4-1l2 3v1c1 1 1 2 1 3h0l-1-1c0 2-1 4-1 6l2 1-2-1c1 1 1 2 2 3 1 2 1 8 1 11 1 2 2 4 3 7 0 0 0 1 1 2v2c-5-8-12-15-20-21z" class="M"></path><path d="M280 253c-2-3-5-5-5-9-1-1 0-3 1-4 0-1 1-1 1-1 1 1 1 2 2 3 1 2 1 8 1 11z" class="B"></path><defs><linearGradient id="BT" x1="319.061" y1="275.667" x2="330.488" y2="274.981" xlink:href="#B"><stop offset="0" stop-color="#90908f"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient></defs><path fill="url(#BT)" d="M320 240c1 0 1 0 2 1-1 6-1 11-1 17 0 5 2 10 3 16l12 44v3l-1-4c-9-21-18-44-20-68 0-2-1-5 0-7l5-2z"></path><path d="M434 457c4 19 3 38 5 57l5 37c1 6 2 12 2 18 0 3-1 14 0 15 2 2 3 3 4 5v1c1 3 0 8-2 11h-1-1l1 1c1 3 3 8 4 11l-1 1c2 5 3 11 4 17 1 3 1 8 1 11l-1 1h0c0 2-1 3-1 4l-1 2v6c0 8-1 17 2 24l1 2-2 2c-4 2-12 4-17 3v-1c-4 3-6 11-7 11l-1 1-48-127 1-1c0-2-1-7-2-9s-1-3-1-4v-1c-1-1-1-2-1-4v1c2 1 2 4 3 6v1c1-1 1-1 1-2l-1-1v-2l2 1c0-9 1-17 1-26-2-1-4-1-6-2-3-3-4-7-4-10-1-3-1-7 0-9 1-3 2-6 5-7 2-1 4-1 6 0l2-1c3 3 5 5 7 10h1v1l1-1c1-5 5-8 6-13 1-3 0-7-1-10-1-5-2-12-2-18v-1c-1 0-1-1-2-1 0-2 0-2 1-4l3-1h1c4 3 6 7 10 9h2c2-3 9-5 12-6 2 0 4 1 6 1v-4h0l2-1c0-1 0-3 1-4z" class="M"></path><path d="M426 646h2c1 0 1 0 1 2l-2 1-1 1h-1c0-2 1-3 1-4z" class="N"></path><path d="M429 634h0c2 0 4 1 5 2s1 1 1 2l-2 1-6-2v-1l2-2z" class="L"></path><path d="M444 665l2 2c0 3 1 7-2 9v1c-1-1-1-2-2-3 1-1 1-2 1-4 1-1 1-3 1-5z" class="G"></path><path d="M429 619h1c1 1 1 0 0 2 0 3-3 5-5 7h-1c-1-1-1-1 0-1 0-3 3-7 5-8z" class="L"></path><path d="M413 544h1c0 1 1 1 1 2 5 9 6 20 3 29v1c0-1-1-1 0-2 1-10-1-20-5-30z" class="I"></path><path d="M440 602c1-1 3-2 4-3 0-1 0-1 1-2 1 2 1 3 2 4h-1l1 1c1 3 3 8 4 11l-1 1c-2-6-4-9-10-12z" class="L"></path><path d="M432 611l1-1c2 0 6 1 8 2h0v1c4 2 7 6 8 10-1-1-3-3-5-4-2-2-4-3-6-4s-5-2-6-4z" class="R"></path><path d="M430 663l2 1c1 3 1 6 4 8 2 1 3 2 5 2h1c1 1 1 2 2 3 0 0-1 1-2 1h-5c-1-1-4-3-4-5l-3-10z" class="F"></path><path d="M430 663c0-2-1-7 1-9 1-2 3-2 5-2s5 2 6 4c3 3 4 7 4 11l-2-2-1-3c-1-2-2-5-4-7h-1l-1-1c-2-1-2-1-4 0v1l-1-1c-1 1-1 1-1 2l1 8-2-1zm-2-148v1c1 1 1 3 2 4l1 2c3 11 4 22 4 33h0l-3-20-5-19 1-1z" class="R"></path><path d="M379 509c1-1 2 0 3 0 2 1 3 2 3 5s0 5-2 7l-1 1-1-1c-2-2-3-6-3-9 0-1 0-2 1-3z" class="B"></path><path d="M425 602c-2 8-5 16-6 24v14 8l-1-1c-1-3-1-7-1-10 0-5-1-11 0-16 1-6 5-13 8-19z" class="N"></path><path d="M452 655c0-2 0-4-1-6-1-5-2-9-6-13h0c-3-4-9-7-10-11 2 0 4 1 5 2 3 2 5 4 7 7 2 2 5 9 7 9 0 2-1 3-1 4l-1 2v6z" class="F"></path><path d="M433 655v-1c2-1 2-1 4 0l1 1h1c2 2 3 5 4 7l1 3c0 2 0 4-1 5-4-4-9-8-10-15z" class="J"></path><path d="M432 664l-1-8c0-1 0-1 1-2l1 1c1 7 6 11 10 15 0 2 0 3-1 4h-1c-2 0-3-1-5-2-3-2-3-5-4-8z" class="O"></path><path d="M384 501l2-1c3 3 5 5 7 10h1v1l1-1h0c0 3-1 6-1 9l-1 16v13c-1 1-1 3-2 4v-37c0-6-3-11-7-14z" class="D"></path><path d="M415 490l1 1c1 3 2 5 4 7 3 2 6 2 9 1l4 1c0 2-1 4-1 6h0l-1 1c-1 2-1 5-2 7l-2-1c0 1 0 1 1 2l-1 1c-2-6-6-10-8-15-2-4-3-7-4-11z" class="B"></path><path d="M425 598l-6-1h-1c1-5 7-23 12-26 3-2 9-1 12 0 0 2 0 7-2 8-3 2-3 3-5 5v1h-1 0c-1 1-3 2-4 2-1 3-1 5-1 8l1 1c0 1-1 2-2 3l-3-1z" class="L"></path><path d="M425 598c1-2 1-3 3-4l1 1 1 1c0 1-1 2-2 3l-3-1z" class="G"></path><defs><linearGradient id="BU" x1="405.214" y1="474.088" x2="429.555" y2="533.213" xlink:href="#B"><stop offset="0" stop-color="#7d7d7c"></stop><stop offset="1" stop-color="#c0c0bf"></stop></linearGradient></defs><path fill="url(#BU)" d="M401 462c4 3 6 7 10 9h2c1 3-1 7-2 11v6 8c2 9 7 15 10 23 2 5 3 10 4 14 1 5 1 10 1 15 0 2 0 4-1 5-1-1 0-10-1-13-2-12-6-22-12-33-2-3-5-6-6-9-1-4-1-9-2-13 0-3-1-6-3-9-1-3-2-5-3-8-1 0-1-1-2-1 0-2 0-2 1-4l3-1h1z"></path><path d="M401 462c4 3 6 7 10 9 0 3 0 5-2 8l-5 2-1-4-2-1c-1-3-2-5-3-8-1 0-1-1-2-1 0-2 0-2 1-4l3-1h1z" class="J"></path><defs><linearGradient id="BV" x1="415.326" y1="624.967" x2="391.407" y2="629.261" xlink:href="#B"><stop offset="0" stop-color="#93908f"></stop><stop offset="1" stop-color="#bcbdbd"></stop></linearGradient></defs><path fill="url(#BV)" d="M377 551v1c2 1 2 4 3 6v1c1-1 1-1 1-2l-1-1v-2l2 1c1 8 3 17 5 26 3 10 8 20 11 30l7 18c1 2 2 5 3 8v-3c-3-8-4-19-4-26h1c1 8 2 16 4 24 2 10 5 19 9 29 3 12 10 23 11 35l-1 1-48-127 1-1c0-2-1-7-2-9s-1-3-1-4v-1c-1-1-1-2-1-4z"></path><path d="M431 462h0c0 1 1 2 1 3 0 2 1 3 1 5v7c1 5 2 9 2 14l-1 2c1 2 0 5-1 7l-4-1c-3 1-6 1-9-1-2-2-3-4-4-7l-1-1v-1c0-2-1-3-1-4v-8-3c-1 3-2 7-2 9s0 3-1 5h0v-6c1-4 3-8 2-11 2-3 9-5 12-6 2 0 4 1 6 1v-4z" class="M"></path><path d="M434 493c1 2 0 5-1 7l-4-1h1c2-2 3-4 4-6z" class="J"></path><path d="M419 478c0 3 0 4 3 5 2 1 3 1 5 0l1-1v-1l1 1c0 1 0 3-1 4-1 2-2 4-4 5-1-1-2-3-3-5l-3-3c0-1 1-4 1-5z" class="N"></path><path d="M419 478c1-1 1-2 3-3 2 1 3 1 4 3 1 1 2 2 2 3v1l-1 1c-2 1-3 1-5 0-3-1-3-2-3-5z" class="B"></path><path d="M431 462h0c0 1 1 2 1 3 0 2 1 3 1 5v7c-1-3-4-5-7-6s-5-1-7 0-2 3-3 4v5 11l-1-1v-1c0-2-1-3-1-4v-8-3c-1 3-2 7-2 9s0 3-1 5h0v-6c1-4 3-8 2-11 2-3 9-5 12-6 2 0 4 1 6 1v-4z" class="F"></path><path d="M401 554l1 17c2-6 2-13 2-20 0-11-2-22-2-32 0-2 0-6 1-7 1 3 1 6 1 9 2 12 3 25 3 38l1 10c0 1-1 3 0 4 0 1 2 3 3 5 2 4 3 23 2 26-1 1-1 2-2 2-2 0-5-5-6-7l-2-4h0v1c1 4 2 8 2 12h-1c-1-3-1-6-2-9v-5c0-3-2-6-2-9-2-3-2-7-3-11-1-6-2-15-1-21h0c1-2 1-4 2-5 0-1 0-1 1-2 1 2 0 5 1 7v-2c1 1 1 2 1 3h0z" class="D"></path><path d="M398 548c0-1 0-1 1-2 1 2 0 5 1 7v-2c1 1 1 2 1 3h0c-2 3-1 12-1 16l1 1c-4-6-5-13-4-20l1-3z" class="O"></path><defs><linearGradient id="BW" x1="445.675" y1="529.941" x2="430.993" y2="530.74" xlink:href="#B"><stop offset="0" stop-color="#a5a4a4"></stop><stop offset="1" stop-color="#cecdcb"></stop></linearGradient></defs><path fill="url(#BW)" d="M434 457c4 19 3 38 5 57l5 37c1 6 2 12 2 18 0 3-1 14 0 15 2 2 3 3 4 5v1c1 3 0 8-2 11h-1c-1-1-1-2-2-4-1 1-1 1-1 2-1 1-3 2-4 3h-2c-2-2-6-2-10-3 1-1 2-2 2-3l-1-1c0-3 0-5 1-8 1 0 3-1 4-2h0 1v-1c2-2 2-3 5-5 2-1 2-6 2-8 0-17-2-34-5-50v1h-1l-1-1c0-1-3-3-4-3v4l-1-2c-1-1-1-3-2-4v-1c-1-1-1-1-1-2l2 1c1-2 1-5 2-7l1-1h0c0-2 1-4 1-6 1-2 2-5 1-7l1-2c0-5-1-9-2-14v-7c0-2-1-3-1-5 0-1-1-2-1-3l2-1c0-1 0-3 1-4z"></path><path d="M428 515c-1-1-1-1-1-2l2 1c1-2 1-5 2-7l1-1c0 2 0 3-1 5 0 0-1 1-1 2l1 1-1 1v5c-1-1-1-3-2-4v-1z" class="N"></path><path d="M440 579l2 1c0 2-1 3 1 5 0 1 1 1 1 1v1c0 2 0 3-2 6 0 0 1 0 1 1s-1 1-1 2v-2l-1-1c0-4 0-9-1-14z" class="G"></path><path d="M440 579c1 5 1 10 1 14-3-3-5-5-6-8v-1c2-2 2-3 5-5z" class="Q"></path><path d="M429 595c0-3 0-5 1-8 1 0 3-1 4-2h0 1c1 3 3 5 6 8l1 1c-1 1-1 3-1 4h1c-1 2-3 2-4 4-2-2-6-2-10-3 1-1 2-2 2-3l-1-1z" class="M"></path><path d="M430 596c4 1 7 2 11 2h1c-1 2-3 2-4 4-2-2-6-2-10-3 1-1 2-2 2-3z" class="I"></path></svg>