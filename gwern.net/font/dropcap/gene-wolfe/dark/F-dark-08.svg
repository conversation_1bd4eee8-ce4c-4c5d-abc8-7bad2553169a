<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="312 85 544 856"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#111110}.C{fill:#1a1918}.D{fill:#282725}.E{fill:#20201f}.F{fill:#413e3a}.G{fill:#0a0909}.H{fill:#2a2825}.I{fill:#ccc5bc}.J{fill:#b8b3ad}.K{fill:#393734}.L{fill:#65625c}.M{fill:#161512}.N{fill:#9d978f}.O{fill:#7f7a72}.P{fill:#484744}.Q{fill:#595651}.R{fill:#eee3d3}.S{fill:#33322f}.T{fill:#6d6a64}.U{fill:#504e48}.V{fill:#5c5a57}.W{fill:#ddd1be}.X{fill:#a7a39d}.Y{fill:#8f8d8a}.Z{fill:#7d7b78}.a{fill:#7c776e}.b{fill:#928d83}.c{fill:#b7afa3}.d{fill:#c5c1be}.e{fill:#898379}.f{fill:#716f6c}.g{fill:#8a8884}.h{fill:#e0ddd8}</style><path d="M628 212h-3 0v-6l1 1c0 1 0 0 1 1v3l1 1z" class="E"></path><path d="M453 561v8l-1-1c0-1 0 0-1-1v-4l2-2z" class="B"></path><path d="M596 781l1-1v1h0v1h1c-1 1-1 1-1 2v2l-1 1-2-1c1-1 1-3 2-5z" class="M"></path><path d="M733 240h2c2 1 4 3 6 5 0 0-1 1-1 2l-2-2c-1-1-3-2-5-2 1-1 0-2 0-3z" class="H"></path><path d="M598 327l2 1 1 1v1h-2c0 1-1 3-1 4h-4l2-6c1 0 1 0 2-1z" class="d"></path><path d="M569 559h1c2-1 3-1 4-1l-1 2c0 1 0 1 1 2h1 8c-4 0-11 0-14 1-1-1-1-3 0-4z" class="J"></path><path d="M643 215c3-1 5 1 8 1l1-1v2 1l-3 1c-1-1-2-1-4 0-1 0-1-1-2-1v-3z" class="D"></path><path d="M584 299c4 0 7 0 10 2v1l-5-1c-2 0-5 1-7 0-2 0-4 0-5-1l2-1h5zM478 872h1c0 3 1 6-1 9h1l1 1c4 3 9 5 13 8l-1 2c-4-5-10-7-15-11l1-9z" class="N"></path><path d="M793 303h1c2 2 3 6 4 8 1 1 1 2 1 3s-1 1-1 2h-1-1v-5h0v-1h-2c-1-1-1-2-1-4l-1 1-1-1c1-1 1-1 2-3z" class="E"></path><path d="M713 237c-5-2-10-4-14-7v-2h2c2 1 5 2 6 3l6 4v2z" class="H"></path><path d="M582 566h3v7c-1 1-1 5-1 6h0c-1-1-2-1-3-1l1-1v-11h0z" class="E"></path><path d="M582 577v-11h0c1 3 1 7 1 11h-1 0z" class="Q"></path><path d="M504 933l-3-9c-1-3-4-10-2-12 0 0 1 0 2 1h1c1 7 1 13 2 19v1z" class="g"></path><path d="M594 786l2 1c0 1-1 2-1 2-1 1-1 1-1 2-2 3-5 5-7 8-2 1-3 3-5 4v1l-1-1c-1 1 0 1-1 2h-3c3-2 6-5 9-8s6-7 8-11zM437 424v-3-1h1 1c1 0 3 0 4-1h1l1 1v2c-1 2-1 2-2 3 0 1-1 1-1 2-2 1 0 5-1 6h-1v-6-4l-3 1z" class="B"></path><path d="M433 418h11 5c-2 1-3 2-3 5l-1-1v-2l-1-1h-1c-1 1-3 1-4 1h-1-1v1 3h-4v-6z" class="H"></path><path d="M451 580h2c1 1 1 1 1 2l2 1h2 1v1 1h-2v1 1h-1l-1 3c-2 0 0-2-1-3h-1v1c-1-1-2-2-4-3h-1l1-1-1-2 1-1c3 0 1 1 3 2l-1-3z" class="W"></path><path d="M541 870l1 12c-3 2-7 4-10 5 1-1 4-3 5-4 3-2 0-9 1-12 0-1 3-1 3-1z" class="J"></path><path d="M780 291h1c0 1 0 1 1 2 1-1 1-1 3-1v-1h1l1 1c1 1 1 2 1 3l-1 1-1-1c-1 1 0 1 0 2h1l1-1h1l1 1 2 2 1 2h-1 1v1 1l-2-1c-2 0-4-1-6-2v-7h-1c-1 0-2 0-2 1l-2-1v-2z" class="B"></path><path d="M583 659h1 3 1c0 2-1 10 0 10 0 1 1 1 1 1 0 1-1 1-1 1-2-1-3-1-4 0-2-3-1-9-1-12z" class="I"></path><path d="M585 640h1v3 14c0 1 0 2 1 2h-3v-1c-1 0-1 0-2-1l1-2 1-1-1-1v-3c0-3-1-5 0-7 0-1 0-1 1-1h1v-2z" class="c"></path><path d="M585 640h1v3 1c-1 3-1 6-1 9h0c-1-2-1-2-2-3 0-3-1-5 0-7 0-1 0-1 1-1h1v-2z" class="I"></path><path d="M426 476c2-1 4 0 6 0l13-1c2 1 4 1 6 1v1l-2 2h-1c-1 1-1 1-2 1l-1 1h-1l-1 1-3 1 3-5c-2-1-4 1-6-1 1 0 1 0 0-1h-11 0z" class="B"></path><path d="M713 235l16 9 1 1c1 0 2 1 3 1 3 3 7 6 10 8l-1 1c-1 0-4-3-5-4h-1c-2-1-4-2-5-3-4-2-7-4-10-6s-6-3-8-5v-2z" class="C"></path><path d="M433 377v-4c2-2 5-1 7-1-1 1-2 1-2 2l-1 1c0 2-1 4 0 6l1 1v1h-2l-7 1c1-3 2-5 4-7z" class="h"></path><path d="M435 279c0-2 1-4 2-6l-2-2v-5c1 0 2-1 3-1h1l1-1c1 2 0 3 1 4v1l-2 1 2 2h0l1 1v1 2l-4 1-3 2z" class="d"></path><path d="M567 546c6-2 16-2 21 1 2 1 4 2 5 3l-8-1c-1-1-2-1-4-1h-12c-1 0-1-1-2-2z" class="R"></path><path d="M444 656h-1c-1-1-2-1-3-2 2-3 0-6 1-8h1c1 1 1 1 3 2l3-1 1 1v7 1h-5z" class="X"></path><path d="M624 221c3-1 25 0 30 1l7 1h3c1 1 1 1 1 2 1 0 2 1 3 2l-2-1h-1c-3 0-10-1-12-2-1-1-2-1-3-1h-3c-4-1-7-1-11-1h-8-2 0l-2 1v-1-1z" class="C"></path><path d="M433 658v-49l1 33 1 1h1c1 0 1 1 2 1v2l1-1h0 1v7h-1-1c-1-2 0-2 0-3v-1c-1 2-1 6-2 8v1l-3 1z" class="W"></path><path d="M434 586l-1-20 1-1c1 2 0 5 1 7v-6h1 0 2c0 4 1 10 0 14v1l1 1c-2 2-1 4-1 6-4-3-1-6-3-9-1 2 1 6 0 8l-1-1z" class="V"></path><path d="M585 566l1-1c1 2 1 7 1 9l-1 26v40h-1v-67-7z" class="L"></path><path d="M430 413h1c2-1 5-1 7-2h1c1 1 2 1 4 1 1 1 2 0 3 0h1v2h1v1h-1c-1 1-2 1-3 3h-11v-1c-2-1-5 0-7 0l4-4z" class="R"></path><path d="M425 748c0 1 0 6 1 7-1 3-1 6-1 9 0 2 0 5 1 7 0 2 0 4 1 6h-2c0 2 1 5 1 8l-3-6h-3-1l3-3v-4c0-3-1-6 0-9 0-5 1-10 3-15z" class="c"></path><path d="M443 233c0 1 0 2 1 2h0 3c-5 9-3 16-2 25-1 1-1 2-1 3l-2 2c-1-3-1-4-1-6-2-6-1-13 0-18l2-8z" class="I"></path><path d="M441 241h0 2c0 2-1 3-1 5 0 1 0 3-1 5v8c-2-6-1-13 0-18z" class="J"></path><path d="M425 748c1-5 5-10 7-15 1 2 0 7-1 10 0 1-1 4 0 5v1l1 1h4l-1 1-1 1 1 2c1 0 1 0 1 1h0c-1 0-2 1-3 1l1 1c-2 0-4 0-6-1-1 1-1 1-1 2v1h0c-1-1 0-2-1-3v-1c-1-1-1-6-1-7z" class="O"></path><path d="M432 750h4l-1 1-1 1 1 2c1 0 1 0 1 1h0c-1 0-2 1-3 1l1 1c-2 0-4 0-6-1v-1c0-2 2-3 3-4h1v-1z" class="T"></path><path d="M450 475c2-3 1-7 3-10v15c-2 0-3 0-4 1h-1c-1 1-2 1-2 3h0c-1 1-1 1-2 1l-1-1-8 8c0 1-2 2-2 3-1 1-2 2-2 3v1 1c-1 1-2 1-2 0h-1c1-2 2-4 3-5h-1 0-2a34.47 34.47 0 0 1 12-12l3-1 1-1h1l1-1c1 0 1 0 2-1h1l2-2v-1c-2 0-4 0-6-1h5z" class="D"></path><path d="M443 482c-1 1-1 2-2 2l-2 2c-2 3-6 7-8 9h-1 0-2a34.47 34.47 0 0 1 12-12l3-1z" class="X"></path><path d="M627 205h16l1 1 3 1c0 1 1 2 1 3 1 0 2 0 3 1h-1-2c-2-1-5 0-7 0h-12l-1 1-1-1v-3c-1-1-1 0-1-1l-1-1 1-1h-3 4z" class="D"></path><path d="M627 205h16l1 1-1 3h0l-2-1c-2 0-4-1-6 0l-2 1-1-1h-2l-1-1h-1c0-1 0-1-1-2h0z"></path><path d="M582 412v-1h-1c1-2 1-5 1-7v-9c0-1-1-3 0-4 1 1 1 5 1 7v1 10l1 1c4 1 7 3 9 7v1c-1 0-1 0-1-1l-2-2 1 3h-4l-1 1c-1 0-1-1-1-1-2 1-3 0-5 0h-9c-1-1-1-1-2-1h0v-2-1-1h16c2 1 3 1 4 1h0c-2-2-4-2-7-2z" class="N"></path><path d="M575 414c1 0 2-1 3 0h-1l1 1-1 1c-1 0-1 1-1 1-2-1-2-1-3-1l2-2z" class="J"></path><path d="M585 550c2 0 2 0 3 2 2 3 2 7 4 11l-9-1h-8-1c-1-1-1-1-1-2l1-2 1-1c0-2 0-5 1-7h1c1 3 1 6 1 8h0c2 1 3 0 4 0v-8h3z" class="I"></path><path d="M668 224c1 0 1-1 1-1 2 0 3 1 4 2h0l-1 1-1-2-1 1c1 0 1 0 2 1 0 1 0 1 1 1 1-1 1 0 3 0h3l16 6c4 1 6 3 9 5l17 9c2 1 4 3 7 3 1 0 1 1 2 1l11 6v1l-1-1c-1 0-2-1-3-1v1c2 0 2 1 3 2 1 0 2 1 2 2 2 0 1 0 2 1 1 0 2 1 2 2-4-3-8-7-12-9l-12-6-9-5-10-6c-3-1-6-3-10-4l-2-1h-1l-3-2-4-1h0l-2-1h-1-1c-3 0-7-1-10-2 0-1-1-2-1-3z" class="B"></path><path d="M729 244h0c0-1-1-2-1-2-2-1-4-3-6-4v-1h0 1s1 0 2 1c1 0 1 0 2 1s1 0 2 1v-3h-1-1l-1-1h0 0v-1h-1l-1-1 1-1c1 1 1 1 2 1s2 1 2 2c1 1 1 0 2 1 1 0 1 1 2 1v2c0 1 1 2 0 3 2 0 4 1 5 2l2 2 4 3c1 1 1 1 2 1l2 2c0 1 0 0 1 1l5 2c-2 0-4 0-6-1v1l-2-1c1 1 1 2 2 2v1h0l-1 1v-1c-1 0-1 0-2-1-1 0-2-1-3-2l1-1c-3-2-7-5-10-8-1 0-2-1-3-1l-1-1z" class="G"></path><path d="M733 243c2 0 4 1 5 2 0 1 0 1-1 2-2-2-3-2-4-4z" class="B"></path><path d="M432 550h4 1v3 4h2v1c2 0 3-1 5 0h3 3c1 1 1 3 1 5h-23l4-13z" class="I"></path><path d="M502 913v1c1 0 2 0 3-1 1 0 2 0 4 1v9c0 8-2 17 0 25 0 1 0 3 1 4h0 1l-1 5c-3-8-5-16-6-24v-1c-1-6-1-12-2-19z" class="V"></path><path d="M566 547l1-1c1 1 1 2 2 2h12c2 0 3 0 4 1h0v1h-3v8c-1 0-2 1-4 0h0c0-2 0-5-1-8h-1c-1 2-1 5-1 7l-1 1c-1 0-2 0-4 1h-1c-1 1-1 3 0 4l-1 2h-2v-18z" class="T"></path><path d="M578 558c1-2 0-5 1-8h2l-1 1c1 2 1 4 1 6l1 1c-1 0-2 1-4 0z" class="J"></path><defs><linearGradient id="A" x1="568.451" y1="555.759" x2="564.901" y2="556.653" xlink:href="#B"><stop offset="0" stop-color="#16150e"></stop><stop offset="1" stop-color="#241e21"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M566 547l1-1c1 1 1 2 2 2h1c1 1 4 1 6 1h-5c-1 0-1 0-2 1 0 2-1 7 0 9-1 1-1 3 0 4l-1 2h-2v-18z"></path><path d="M429 208l-6-7c-1 2-1 6-2 8l2 2h0-1c-1-2-7-7-7-9h1v1c1 1 3 4 4 4 0-2 1-3 0-4-1-2 0 1 0-2h1l1-1-7-6v3c-1 1-1 1-1 2s0 2-1 3c-1-1-1-2-2-2-2-3-6-5-8-7-1-2 1-2 1-4v-1h2l1 1c-1 1 0 1 0 3h-1l-1-1h0v1l3 3h1 1c2 1 1 0 3-1v-1c-1-2-2-2-3-3l2-1c2 1 4 3 6 5l1 1h1l-3-3h1c1 1 6 5 7 5h0l-3-4v-1h3c1 1 2 3 2 4v2h-1c1 2 5 5 4 7v1h0l-1 2z" class="e"></path><path d="M668 208l4 1h2 2c0 1 1 1 2 1l2 1 6 2c1 0 2 0 3 1l5 1 7 3 3 1 16 9 3 2 4 2c10 5 20 13 27 20-2-1-3-1-4-3h-1l-4-4c-2-1-3-2-4-3-3-1-5-3-7-5-2-1-3-1-4-2s-2-1-2-2c-1 0-2 0-2-1h-1 0c-1-1-2-1-2-2-2 0-4-2-5-2l-1-1c-1 0-2-1-3-1v-1h-1c-1 0-1 0-1-1h-2v-1l-2-1-2-1h-2l-1-1-4-2c-1 0-2 0-3-1h-2c-1-1-2-1-3-2-1 0-2 0-3-1h0-2c-1 0-1 0-2-1h-3v2 1 1c-2-1-3-1-4-2-2 0-5 0-7-1s-2-2-4-1h-3l-2-2 1-3c1 0 2 1 3 1s2 0 3-1z" class="H"></path><path d="M562 544v-11c0-1 0-3 1-5 1 0 1 0 3 1v2 16 18h2c1 2 1 5-1 7v1l-2 1h-1l-1-1v-3c-2-8-1-18-1-26z" class="I"></path><path d="M566 565h2c1 2 1 5-1 7v1l-2 1-1-3v-3h1l1 1v-4z" class="D"></path><defs><linearGradient id="C" x1="509.295" y1="929.031" x2="515.814" y2="930.245" xlink:href="#B"><stop offset="0" stop-color="#4f4d4c"></stop><stop offset="1" stop-color="#75726e"></stop></linearGradient></defs><path fill="url(#C)" d="M510 952l1-39h6l5-1-11 40h-1z"></path><path d="M426 785c0-3-1-6-1-8h2c1 5 3 9 6 13h2c1 2 3 5 5 6 3 1 5 4 8 5 1 1 3 2 5 3 0-1 0-1 1-1 1 2 3 4 5 5h0l2 1 5 1h1 1v1c-1 1-2 0-4 0-4 0-8-1-12-3h-2c0 1 4 2 6 3h-2c-4-1-7-2-10-5h0c-3-2-7-5-9-8l-2-2c-3-3-6-7-7-11z" class="U"></path><path d="M426 785c0-3-1-6-1-8h2c1 5 3 9 6 13v1l-5-4c1 2 2 4 4 6l4 5h-1l-2-2c-3-3-6-7-7-11z" class="g"></path><path d="M584 522c1 1 3 1 4 1 12 2 25-1 37 1h1c3 0 6 1 9 3 3 1 5 2 8 2 1 1 4 1 4 2 3 2 7 3 8 6-5-2-10-5-15-6-17-5-35-5-53-2v-1h-2c-1 1-1 1-2 1l1-1-2-2 1-1 1 2h0v-5z" class="K"></path><path d="M443 425h0c1 3 1 5 1 8v2l1 1h0c1 0 2-1 3-1s1 0 2-1c1 2 1 3 0 5v1c2 2 0 4 1 6 0 1 1 2 1 2l-1 1c-1 0-2-1-2-1h-1c0 1-1 1-1 1-1 2 0 4-1 5v2l3 1c1 1 1 1 0 3h-5c-4 0-10 0-14 1l-9 1 1-1c6-5 12-6 19-5h1v-29c0-1 1-1 1-2z" class="R"></path><path d="M448 435c1 0 1 0 2-1 1 2 1 3 0 5v1c2 2 0 4 1 6 0 1 1 2 1 2l-1 1c-1 0-2-1-2-1h-1c0 1-1 1-1 1-1 2 0 4-1 5v2h-1c0-3-1-6 1-8h1l1-1h-3v-9h1v1 1c0 1 0 2 1 3h1v-1c-1 0-1 0-1-1l1-1c-1-1-1-1-1-2s0-1 1-2v-1z" class="D"></path><path d="M542 506v13l1 1c1 0 1 1 3 1h1c2 0 2 0 3-1h1 1 9 2c2 1 4 2 7 2l-2 1c2 1 4 2 4 4 1 3 1 6 0 10h1l-1 1c0 1 0 2-1 2h0l-1-2v-4c-1-1 0-2-1-3 0-1-1-1-2-2l-1 2v-2c-2-1-2-1-3-1-1 2-1 4-1 5v11c-2 2 0 5-1 7l-1-1v-3-7l-1-1c0-3-1-9 1-12v-1c-2-1-3 1-5 0h-3l-2-1c-2 0-1 0-2 1s-2 1-4 2h4 0l-3 1s-1 0-2-1c-1 0-1-1-2-1 1-7 0-14 1-21z" class="H"></path><path d="M559 522c2 0 5-1 7 0l2 1c2 1 4 2 4 4 1 3 1 6 0 10v-1c-1-2-1-4-1-6-2-3-5-4-8-5-1-1-1-1-3-2l-1-1z" class="I"></path><path d="M542 506v13l1 1c1 0 1 1 3 1h1c2 0 2 0 3-1h1 1 9 2c2 1 4 2 7 2l-2 1-2-1c-2-1-5 0-7 0l1 1c2 1 2 1 3 2l-4-1h-1c-3 0-5 0-8 1-2 0-1 0-2 1s-2 1-4 2h4 0l-3 1s-1 0-2-1c-1 0-1-1-2-1 1-7 0-14 1-21z" class="C"></path><path d="M544 528h-1v-1c0-1 0-2 1-2 1-2 3-3 6-3h0c3-1 6-1 9 0l1 1c2 1 2 1 3 2l-4-1h-1c-3 0-5 0-8 1-2 0-1 0-2 1s-2 1-4 2z" class="N"></path><path d="M565 276l1-1c1-1 2-3 3-3l1-1 1-1v1l1 1 1 1 1-1c0-1 0-1 1-1l1 1 1 2h2l1-2c1 1 3 2 4 3 0 2 1 3 0 5l-1-1-1 1h-2l1 2c1 1 0 4 1 6v3h0l-1 1c0 1 0 1-1 2 1 0 1 1 2 2l1-2h0c1 1 1 2 1 3v1h0v1h-5-5 0v-3-1-1c-1-2 0-4-1-5 0-1-1-2-1-2 0-2 0-3-1-4l-1-1 1-1-1-1v1c-1 0-1 0-2-1v-1h-1c-1-2 0-2-2-3h0z" class="C"></path><path d="M574 296l2-1v-1h1l1-1 1 1v1h1l2 2h1l-1-1 1-2h0c1 1 1 2 1 3v1h0v1h-5-5 0v-3z" class="B"></path><path d="M573 273l1-1c0-1 0-1 1-1l1 1 1 2h2l1-2c1 1 3 2 4 3 0 2 1 3 0 5l-1-1-2-1c-3-2-5-3-8-3l-2-1 2-1z" class="d"></path><path d="M565 276l1-1c1-1 2-3 3-3l1-1 1-1v1l1 1 1 1-2 1 2 1h0l2 2c0 1 0 1-1 2-1-2-1-2-2-2-1 1 0 1-1 2s-1 0-1 1v1c-1 0-1 0-2-1v-1h-1c-1-2 0-2-2-3h0zm-77 590v-2h1c1 1 1 1 1 2v6h3l1-2v3h-1v4c0-2 0-3 1-4l2-1v1 5c0 4 1 9 0 12-2 1-1 2-4 2h0l1-2c-4-3-9-5-13-8l-1-1h-1c2-3 1-6 1-9h-1l-1-2 1-1h5v-1c1 0 1 1 2 1v1c1-1 2-2 2-3l1-1h0z" class="F"></path><path d="M485 870c1-1 2-2 2-3l1-1h0v7h0l-3-2v-1z" class="B"></path><path d="M490 872h3l1-2v3h-1v4 13c-4-3-9-5-13-8 2 0 3 1 4 2l1 1c2 0 4 0 6-1 1-2 1-6 1-9-1-1-2-1-2-3h0z" class="D"></path><path d="M493 877c0-2 0-3 1-4l2-1v1 5c0 4 1 9 0 12-2 1-1 2-4 2h0l1-2v-13z" class="X"></path><path d="M477 870l7 1c0 4 0 7 1 11 0 1 0 1-1 2-1-1-2-2-4-2l-1-1h-1c2-3 1-6 1-9h-1l-1-2z" class="Y"></path><path d="M446 274c1 0 2 0 2 2v1l1 2c-1 0-1 0-1 1h0 0 2c1 1 1 2 2 3s1 1 2 1v1c0 1 0 2-1 3v1l-3 6v1h-1v-1h-3v1h-1-1l-1 1 1 1h1l1 1c-2 1-3 0-5 1h-3-1l-1 1h-2-1c-1 0-2 0-3 1s-3 0-5 0v-1c3-2 7-2 10-3 2-4 3-9 3-13 0-3-1-4-3-6l3-2 4-1 4-2z" class="P"></path><path d="M446 274c1 0 2 0 2 2v1c-1 0-1 0-2 1-1 0-1 0-1-1l-2 1-1-1h-1v1l-3-1 4-1 4-2z" class="F"></path><path d="M443 278l2-1c0 1 0 1 1 1 1-1 1-1 2-1l1 2c-1 0-1 0-1 1h0 0 2c1 1 1 2 2 3s1 1 2 1v1c0 1 0 2-1 3v1l-3 6v1h-1v-1h-3v1h-1-1l-1 1-1-3v-2h1v3c2-1 4 0 5-1l-1-1h-2v-1h0 2v-1l1 1h1c-1-2-1-3-1-4-1 0-1 0-2 1h-1-3c0-1 0-1 1-2 0 1 1 1 2 2 0-1 1-1 2-1 0-1-2-3-2-5h1c0-1-1-2-1-3l-2 1v-1-2z" class="G"></path><path d="M453 230l2 1v6 1 28l-1-2v-2c-1-1-1-2-2-3h-1c-1-1-1-1-2-1l-1 1 2 1c0 1 0 1-1 2v5c-1 2-1 3-2 5l-1 2-4 2v-2-1l-1-1h0 0v-1c2-2 1-4 1-6l2-2c0-1 0-2 1-3-1-9-3-16 2-25l1-1 1-2v1l1 1v3l1 1 1-3v-2c0-1 1-2 1-3z" class="e"></path><path d="M449 258c0-3-1-6 0-9h0v-1h2l1 1c-1 2 0 5 0 7 0 1 0 1 1 2l-1 1h-1c-1-1-1-1-2-1z" class="O"></path><path d="M444 263l1 2 1-1c1 1 1 2 1 4 0 1-1 1-1 2l1 2-1 2-4 2v-2-1l-1-1h0 0v-1c2-2 1-4 1-6l2-2z" class="J"></path><path d="M448 234v4c-1 2-2 1-1 3v2 8c0 1 1 5 0 6v1c-1 1 0 2 0 3h-1l-1-1c-1-9-3-16 2-25l1-1z" class="U"></path><path d="M453 230l2 1v6 1 28l-1-2v-2c-1-1-1-2-2-3l1-1c-1-1-1-1-1-2 0-2-1-5 0-7l-1-1h0c2 0 1 0 2-1l-1-1v-3-1c0 1 1 1 2 1v-2h-1-1v-3h-1l1-3v-2c0-1 1-2 1-3zm0 346c6-1 14-2 20 0l3 1h1v16h-3-1-1c-4-1-8-1-11-1-1-1-1-1-2-1-2 0-2 0-4-1l1-3h1v-1-1h2v-1-1h-1-2l-2-1c0-1 0-1-1-2h-2c-1 0-2 1-3 0 0-2-1-2 0-3 2-1 3-1 5-1z" class="F"></path><path d="M453 576c6-1 14-2 20 0l3 1v3c-1 1-1 1-2 1l-2-2h0c-1 1-2 1-3 1l1-2v-1h0c-1 0-1 0-2-1-2-1-6 0-9 0v3c-2 0-4 1-6 1h-2c-1 0-2 1-3 0 0-2-1-2 0-3 2-1 3-1 5-1z" class="h"></path><path d="M459 579h9v1h0c0 1 0 1 1 1h0l2 2 1-1c1 0 2 0 3 1h1c1 0 0 0 1 1v3c-1 0-1 0-2 1l-1-1c-4-1-6 1-9-2h-4l-1-1h-1v-1h-1-2l-2-1c0-1 0-1-1-2 2 0 4-1 6-1z" class="Q"></path><path d="M459 579h9v1h0c0 1 0 1 1 1h0c-3 2-11 1-15 1 0-1 0-1-1-2 2 0 4-1 6-1z" class="M"></path><path d="M446 536c1-1 1-2 2-3v-1l1-1c0-2 1-3 2-4 1 1 2 1 2 3v2 29l-2 2c0-2 0-4-1-5h-3-3c-2-1-3 0-5 0v-1h-2v-4-3h-1-4l-1-1h-3-1c3-3 6-3 10-4-1-3-3-4-5-6 2 0 2 0 2 1 1 1 2 1 3 1v1h5l4 1v-7z" class="D"></path><path d="M437 545h13c0 1 1 1 1 2-2 1-5 1-7 1-4 0-9 0-13 1h-3-1c3-3 6-3 10-4z" class="R"></path><path d="M436 550h11v7 1h-3c-2-1-3 0-5 0v-1h-2v-4-3h-1z" class="J"></path><path d="M446 536c1-1 1-2 2-3v-1l1-1c0-2 1-3 2-4 1 1 2 1 2 3v2 1c-1 2 0 9-1 10l-1 1h-5 0v-1-7z" class="H"></path><path d="M569 417h0c1 0 1 0 2 1h9l-1 1 1 2c1 1 2 1 3 1l1 1-1 1h-1c0-1-1-1-1-1-1 1-1 2-1 3v8c1 7 2 14 1 21 1 1 4 0 4 1h-14-1-1v-3c1-1 1-4 1-5l1-1h4c-1-1-2-1-3-1s-2-1-2-1v-2h2v-1h3l-1-1h-3-2v-6l2-1v-1c0-1-1-1-2-2v-8c1-2 0-1 0-3v-3z" class="Q"></path><path d="M569 456v-3c1-1 1-4 1-5l1-1v1h3l-2 2 1 1 2-2h0v2 3 1h-1c-1 0-2 0-3 1h-1-1zm0-39h0c1 0 1 0 2 1h9l-1 1c0 1-1 2-1 3h0l-1-1c-1 0-2 1-3 1v-1h-1l-1 1c0 1 0 1 2 2 1 0 2 0 3 1l-1 1-2-1-2 1h1v3h-1v2c1 0 1 0 2 1h-2c0 1 0 1 1 2h0c-1 1-1 3-1 5l-1 2h0-2v-6l2-1v-1c0-1-1-1-2-2v-8c1-2 0-1 0-3v-3z" class="C"></path><path d="M565 393h2c0-2-1-4 0-6 1 1 2 1 3 2l-1 1c0 1 0 1 1 1 1 4-1 9 0 13v1c0 1 0 1-1 2 2 2 0 0 2 1v1 1c2 1 6 1 8 1 1 1 2 1 3 1 3 0 5 0 7 2h0c-1 0-2 0-4-1h-16v1 1 2 3c0 2 1 1 0 3v8c1 1 2 1 2 2v1l-2 1v6h2 3l1 1h-3v1h-2v2s1 1 2 1 2 0 3 1h-4l-1 1c0 1 0 4-1 5v3h-3v-7h0l-1-41-1-2-1 1-1 2c-1-1-1-2-1-2v-1-1c-1-1-1-2-1-3-1 1-1 1-1 2v1c-1 1-1 1-1 2-1-1-1-1-1-2v-2-1-1c0-2 1-3 2-4 0-2 0-5 1-7 1-1 1-2 2-2 0 1 0 3 1 4h1l1 1z" class="G"></path><path d="M560 402l6 1-1 5-1-2-1 1-1 2c-1-1-1-2-1-2v-1-1c-1-1-1-2-1-3z" class="d"></path><path d="M560 390c1-1 1-2 2-2 0 1 0 3 1 4h1l1 1-1 1h0c0 2 0 3-1 4v2 1c-2-2-1-4-4-4 0-2 0-5 1-7z" class="B"></path><path d="M449 258c1 0 1 0 2 1h1c1 1 1 2 2 3v2l1 2 1 5c0 1 1 1 2 1-1 1-2 1-2 3 1 0 3 0 4 1h1s0-1 1-1l1 2c0-1 0-1 1-2h0 1v1h2v-1h1v-1l2 1h0c1 0 1 0 2-1v2h1v-1c0-2 1-3 2-4 1 0 0 0 1 1 0 1-1 2-2 4h1 2v1h1v16l-6 3-1-1v-7h0v-1c-1-1-1-2-2-2h0c-2-1-2-1-3-1s-1 0-2 1c-1-2-1-2-3-3v1c-1 1-1 1 0 2l-1 1c-1-1-1-1-1-2h-1c0-2-1-1-2-2l-2 3v-1c-1 0-1 0-2-1s-1-2-2-3h-2 0 0c0-1 0-1 1-1l-1-2v-1c0-2-1-2-2-2l1-2c1-2 1-3 2-5v-5c1-1 1-1 1-2l-2-1 1-1z" class="V"></path><path d="M471 287l2 3h3 1v-4c0-1 0-1-1-2h-2v-2l-1-1h0l1-2v-1l1-1 1 1 1-1h1v16l-6 3-1-1v-7h0v-1z" class="G"></path><path d="M449 258c1 0 1 0 2 1h1c1 1 1 2 2 3v2l1 2 1 5c0 1 1 1 2 1-1 1-2 1-2 3-1 0-1 1-2 2 0 1 0 1 1 3v1h6v-1c2-1 5-1 7 0s3 2 3 4v3 1-1c-1-1-1-2-2-2h0c-2-1-2-1-3-1s-1 0-2 1c-1-2-1-2-3-3v1c-1 1-1 1 0 2l-1 1c-1-1-1-1-1-2h-1c0-2-1-1-2-2l-2 3v-1c-1 0-1 0-2-1s-1-2-2-3h-2 0 0c0-1 0-1 1-1l-1-2v-1c0-2-1-2-2-2l1-2c1-2 1-3 2-5v-5c1-1 1-1 1-2l-2-1 1-1z" class="M"></path><path d="M449 258c1 0 1 0 2 1-1 4 1 7 1 12v4h1c0-1 0-1 1-2v2c-1 1-2 2-2 3l1 1h0l-2-1v1l3 3c1 1 0 1 0 2-1 0-1 0-2-1s-1-2-2-3h-2 0 0c0-1 0-1 1-1l-1-2v-1c0-2-1-2-2-2l1-2c1-2 1-3 2-5v-5c1-1 1-1 1-2l-2-1 1-1z" class="Q"></path><path d="M452 448l1-1c1 2 0 15 0 18-2 3-1 7-3 10h-5l-13 1c-2 0-4-1-6 0h-1c0-2 1-4 2-5 0-3 1-7 3-9v-1c4-1 10-1 14-1h5c1-2 1-2 0-3l-3-1v-2c1-1 0-3 1-5 0 0 1 0 1-1h1s1 1 2 1l1-1z" class="K"></path><path d="M449 468v-4-1h3v1c-2 2-1 3-2 6l-1-2z" class="U"></path><path d="M437 469c1-1 0-1 1-2 0-1 1-3 2-4 0 2 0 4 1 5h1 0c1-2 1-4 2-5 0 2 0 4 1 5h4l1 2c-1 1-1 1-1 3l1 1h-2c-2 0-2-1-3-2-4 0-5 0-8-3z" class="I"></path><path d="M430 462c1 2 1 5 1 8l2-6c1 0 2-1 3-1v5l1 1c3 3 4 3 8 3 1 1 1 2 3 2h2v1h-5l-13 1c-2 0-4-1-6 0h-1c0-2 1-4 2-5 0-3 1-7 3-9z" class="W"></path><path d="M426 755v1c1 1 0 2 1 3h0v-1c0-1 0-1 1-2 2 1 4 1 6 1h0c-1 2-2 3-2 6l1 5v3l1-1 1 5c0 1 1 3 2 4l-1 1c2 3 3 5 4 7h1l10 8 2-2h0v2l1 1s1 1 1 2h0l2-2 5 3v2l-1 1c0-1 0-1-1-2 0 0-1-1-2-1h-1l-1 1v1l-1-1c-1 1-1 1-1 2l1 1h-1c-1 0-1 0-1 1-2-1-4-2-5-3-3-1-5-4-8-5-2-1-4-4-5-6h-2c-3-4-5-8-6-13-1-2-1-4-1-6-1-2-1-5-1-7 0-3 0-6 1-9z" class="L"></path><path d="M426 755v1c1 1 0 2 1 3h0v-1c0-1 0-1 1-2 2 1 4 1 6 1h0c-1 2-2 3-2 6 0 1-1 2-2 3h-4l1-1h1v-1h-3c0-3 0-6 1-9z" class="a"></path><path d="M426 771h1v1 3l1 1h2l-2 1 1 1h3l-1 2 3 3 1-1h0l3 3c1 3 6 6 5 8-1 0 0-1-2 0l3 3h-1c-1 0-1-1-2-1-2-2-4-5-6-6v1h-2c-3-4-5-8-6-13-1-2-1-4-1-6z" class="V"></path><defs><linearGradient id="D" x1="564.841" y1="834.537" x2="572.762" y2="805.196" xlink:href="#B"><stop offset="0" stop-color="#0f0f0f"></stop><stop offset="1" stop-color="#3e3d3c"></stop></linearGradient></defs><path fill="url(#D)" d="M583 807v2 1l2-1h0c2 0 5 0 6-1h1l1-1h3c1 1 2 1 3 3-1 0 0 0-1-1h-3c-1 0-1-1-1-1-2 1-3 3-5 4-1 1-3 2-3 3-1 1-1 1-2 1h0c-1 1-2 1-3 1v1l-2 1-1-1-1 1c-1 0-1 1-2 1l-4 2-1 1c1 1 1 2 1 3-1 1-2 2-2 4l-2 2c-1 1-1 2-1 4l2 4-1-1c-1 0-2-2-2-3l-2-2h-1v1c1 1 1 1 1 2h1c0 1 1 2 1 3 1 1 0-1 1 1l1 1v1h-1c-2-4-5-8-8-11l-10-16c2 1 5 2 7 1h4c1-1 3-1 5-1l4-2 4-1c2 0 3-1 5-2s4-2 6-4z"></path><path d="M570 823c1 1 1 2 1 3-1 1-2 2-2 4l-2 2c-1 1-1 2-1 4-1-2-1-3-1-5h0v2h-1-1l-1-1v-1c0-2 1-4 2-5 2 0 3 0 4-1 1 0 1-1 2-2h0z" class="C"></path><path d="M427 347c1-1 2-1 3-1h1c0-1-2-2-1-3h5 0c1 1 2 1 3 2l-1 1 1 1c1-1 1-2 2-3l2 1h0 2l1-1v1h-1 0l-1 1h3 4 0v2 4c0 4-1 10 1 13l2 1v2l-1 1c-1 1-1 2-1 3v7c0 1 0 2 1 3l1 2h-6v-2h2v-1h-1v-1l-2-3v-2l1-2h0v-1h-2-5c-2 0-5-1-7 1v4l-1-4-1-1h0c1-2 3-2 4-3 2-1 1-2 1-4 1-1 1-2 1-3 0-2 1-5 0-7-1-3-3-6-5-8h-3l-1 1-1-1z" class="F"></path><path d="M450 346v2 4c0 4-1 10 1 13l2 1v2l-1 1-1-1h-2-3-2c0-1 1-2 2-3l-1-1h-1v-1-1l1-1v-1h-2v-1l2-1h1l-2-1v-4h0l1-1c1 0 2 0 3-1l-2-2h2s1-1 1-2l1-1h0z" class="G"></path><path d="M440 372h5 2v1h0l-1 2v2l2 3v1h1v1h-2v2 1h1l1 1v1 7 12 5h-3v1c-1 0-2 1-3 0-2 0-3 0-4-1h-1c-2 1-5 1-7 2h-1c1-1 2-2 4-3 1 0 1 0 2-1v-25-1h2v-1l-1-1c-1-2 0-4 0-6l1-1c0-1 1-1 2-2z" class="U"></path><path d="M443 411c-1-2 0-4 0-6 0-4 0-9-1-14 0-1 0-1 1-2l2 2c-1 1 0 3 0 4v9l-1 6-1 1z" class="c"></path><path d="M443 411v-7-4h1l1 4-1 6-1 1z" class="I"></path><path d="M440 372h5 2v1h0l-1 2v2l2 3-2 2-1-2c-1 1-1 2-1 3h0l2-1v1c-1 1-1 1-2 1l-2-1v-2h0c-1 1-1 2-1 3-1 0-2 0-3-1v-1l-1-1c-1-2 0-4 0-6l1-1c0-1 1-1 2-2z" class="R"></path><path d="M572 287s1 1 1 2c1 1 0 3 1 5v1 1 3h0 5l-2 1c1 1 3 1 5 1 2 1 5 0 7 0l-1 1c1 1 0 1 1 1 5 3 9 9 10 15 1 3 1 7 1 10l-2-1h-3-1-2c-2-1-2-1-3-2v2c-1-1-1-3-2-4h-1-4c0-2-1-2-2-4-1 0-3 0-3-1l2-2v-1h-3 0-1c1-1 1-2 2-2 0-2-1-2-2-4h0v-2c-1 0-2 0-3 1-2-2-4-2-6-4v-1-1c1-3 1-5 1-8l1-1c1 1 1 1 3 2 0-1 1-3 1-4v-4z" class="T"></path><path d="M578 306l2 1v-1h0l1-1c1 1 3 2 4 4-2 1-4 1-5 3v1 1l-1-1h-2c0-2-1-2-2-4h0v-2c1 0 2-1 3-1z" class="B"></path><path d="M575 307c1 0 2-1 3-1 0 2 1 4 1 6h1v1 1l-1-1h-2c0-2-1-2-2-4h0v-2z" class="L"></path><path d="M580 319s1 0 2 1l1-2c2 1 2 1 3 2l1-1c0-1 0 0 1-1h1l4 1v3 1h0c1 1 1 1 1 2h-1-1l-2-2c1-1 1-1 1-2h-1c-1 2-1 2-1 4v2c-1-1-1-3-2-4h-1-4c0-2-1-2-2-4z" class="a"></path><path d="M589 303c5 3 9 9 10 15 1 3 1 7 1 10l-2-1h-3-1-2c-2-1-2-1-3-2 0-2 0-2 1-4h1c0 1 0 1-1 2l2 2h1 1s1 1 2 1h0c0-6-2-11-4-16-1-1-1-2-1-4 0-1-2-1-2-3z" class="I"></path><path d="M572 287s1 1 1 2c1 1 0 3 1 5v1 1 3h0c-1 0-3 0-4 2 3 1 6-1 8 1-1 1-2 1-4 2l-2 1v1c1 0 1 0 1-1 1 0 3-1 4-1h1v2c-1 0-2 1-3 1s-2 0-3 1c-2-2-4-2-6-4v-1-1c1-3 1-5 1-8l1-1c1 1 1 1 3 2 0-1 1-3 1-4v-4z" class="M"></path><path d="M567 294l1-1c1 1 1 1 3 2v1l-2 1v4c-1 1-2 1-3 1 1-3 1-5 1-8z" class="H"></path><defs><linearGradient id="E" x1="611.101" y1="524.62" x2="608.436" y2="507.391" xlink:href="#B"><stop offset="0" stop-color="#4d4d4e"></stop><stop offset="1" stop-color="#89837a"></stop></linearGradient></defs><path fill="url(#E)" d="M582 513h9c3-1 5-1 7-2 3-1 5-3 8-3 0 1 1 2 2 3 0 0 1 1 2 1 3 1 9-1 11 1h5c1 0 2 1 3 2l4 3 1-1v1 1h-1c1 1 2 1 2 2v1l-1-1h-2 0c1 1 3 2 3 2 2 3 5 4 8 6-3 0-5-1-8-2-3-2-6-3-9-3h-1c-12-2-25 1-37-1-1 0-3 0-4-1l-1-6-1-1v-2z"></path><path d="M583 516c6 4 15 3 22 3 2 0 5-1 7 0l1-1c1 1 1 1 2 1-2 0-3 1-4 1h-1-1c-1 0-2 0-3 1h-3c-2 0-3 0-4 1h-2c-2 0-4 0-5 1h-4c-1 0-3 0-4-1l-1-6z" class="V"></path><path d="M573 373c2 0 3-1 4-1h2 2c2 0 4 0 6 1 1 2 1 5 2 7 0 2 3 3 4 4-3 0-6 1-9 0h-1l-1 1c1 1 0 2 1 3v1c1 1 0 8 0 10v-1c0-2 0-6-1-7-1 1 0 3 0 4v9c0 2 0 5-1 7h1v1c-1 0-2 0-3-1-2 0-6 0-8-1v-1-1c-2-1 0 1-2-1 1-1 1-1 1-2v-1c-1-4 1-9 0-13-1 0-1 0-1-1l1-1c-1-1-2-1-3-2-1 2 0 4 0 6h-2l-1-1c1 0 0-1 0-2l2 1v-2c-1-3 1-6 1-10 1-1 0-2 0-3s1-2 2-3 2-1 4 0z" class="O"></path><path d="M573 384c1 0 2 0 3-1v1h4l1-1v2h1c1 1 0 2 1 3v1c1 1 0 8 0 10v-1c0-2 0-6-1-7-1 1 0 3 0 4v9c0 2 0 5-1 7h1v1c-1 0-2 0-3-1h1v-5c1-3 0-6 0-9s1-6 1-8h-1c-1 2 0 4-1 6v14h-1c0-6 0-12 1-18 0-1-1-1-1-2h-1l-1-1-1 1c0 2 0 1-1 2v-1h-1l-1 1-1-2v-2l-1 1c-1-2-2-2-2-4h3 0 2z" class="C"></path><path d="M564 390l2 1v-2c-1-3 1-6 1-10 1-1 0-2 0-3s1-2 2-3 2-1 4 0v1h0c1 0 2 1 3 1 0 2 0 4-1 6-1-1-1-2-1-2-1 1-1 3-1 5h-2 0-3c0 2 1 2 2 4l1-1v2h-1 1c0 1 1 3 1 3 0 2-1 11 0 12h1c0 1 0 1-1 2h0c0 1 0 2-1 3v-1c-2-1 0 1-2-1 1-1 1-1 1-2v-1c-1-4 1-9 0-13-1 0-1 0-1-1l1-1c-1-1-2-1-3-2-1 2 0 4 0 6h-2l-1-1c1 0 0-1 0-2z" class="E"></path><path d="M573 373c2 0 3-1 4-1h2 2c2 0 4 0 6 1 1 2 1 5 2 7 0 2 3 3 4 4-3 0-6 1-9 0h-1l-1 1h-1v-2l-1 1h-4v-1c-1 1-2 1-3 1 0-2 0-4 1-5 0 0 0 1 1 2 1-2 1-4 1-6-1 0-2-1-3-1h0v-1z" class="W"></path><path d="M433 189l3-3 1 1h0c-1 2 0 3 0 5 1 2 2 5 4 6 1 0 1-1 2-1 2 2 4 3 6 4h1c3 2 7 1 11 0l2-1c0 1-1 2-2 3h2 0l1 1c-1 1-1 1-1 2-1 1-1 2-3 2h-1l-6 3c1 1 2 1 3 2h0-1c-1 2-2 3-2 4-1 4-2 7-5 10v1c2 1 2 3 4 5v2l-1 3-1-1v-3l-1-1v-1l-1 2-1 1h-3 0c-1 0-1-1-1-2l-1 1-1-1c1-5-1-12-4-16l-8-9 1-2h0v-1h3c1 2 2 2 4 3-1-2-2-4-2-6l2-1 1 1h0v-4l-3-6-1-1-1-2z" class="e"></path><path d="M453 211h0c1 1 2 1 3 2h0-1c-1 2-2 3-2 4-1 4-2 7-5 10h0l1-2h0l-1-2h1v-1c-1 1-1 0-2 0v-1l1-1 2 1c1 0 0 0 1-1-2-1-2 0-4-1 2-4 3-6 6-8z" class="U"></path><path d="M438 217h0c2 2 3 4 3 7 0 0 1 3 1 4h1c1-1 1-3 1-4 1 1 1 3 1 4h0c1-1 1-2 1-3h1c0 1 1 2 0 3l-3 3 1 1 1-2 2-2c2 1 2 3 4 5v2l-1 3-1-1v-3l-1-1v-1l-1 2-1 1h-3 0c-1 0-1-1-1-2l-1 1-1-1c1-5-1-12-4-16h1z" class="W"></path><path d="M448 228c2 1 2 3 4 5v2l-1-1c-2-2-3-2-5-4l2-2z" class="h"></path><path d="M444 202c2 1 4 3 7 4s8 1 12 0c-1 1-1 2-3 2h-1l-6 3h0l-1-1-2 2-1 1c-1 1-1 2-1 3l-1-1c1-3 0-5-1-7-1-1-1-2-2-3v-3z" class="O"></path><path d="M451 206c3 1 8 1 12 0-1 1-1 2-3 2h-1l-6 3h0l-1-1-2 2-1 1c0-1 1-3 3-4h0v-1l-1 1c-2-1-2-2-3-3h2 1z" class="T"></path><path d="M441 198c1 0 1-1 2-1 2 2 4 3 6 4h1c3 2 7 1 11 0l2-1c0 1-1 2-2 3h2 0l1 1c-1 1-1 1-1 2-4 1-9 1-12 0s-5-3-7-4l-3-4z" class="D"></path><path d="M437 208c-1-2-2-4-2-6l2-1 1 1h0v-4c1 3 3 9 5 11v-2-1c2 2 2 4 2 7v1l-3-4h0l1 2v2h1v3c0 2-1 5 0 7 0 1 0 3-1 4h-1c0-1-1-4-1-4 0-3-1-5-3-7h0-1l-8-9 1-2h0v-1h3c1 2 2 2 4 3z" class="T"></path><path d="M430 205h3c1 2 2 2 4 3 1 1 1 2 1 4l1 1c1 1 1 2 1 3 1 1 2 3 3 4s0 3 0 4v1c-1-1-1-1-1-2 0-3-1-4-2-7l-2-2-1 1 1 2h-1l-8-9 1-2h0v-1z" class="O"></path><defs><linearGradient id="F" x1="428.157" y1="184.46" x2="419.684" y2="193.943" xlink:href="#B"><stop offset="0" stop-color="#6c675f"></stop><stop offset="1" stop-color="#817c77"></stop></linearGradient></defs><path fill="url(#F)" d="M411 177h0v-3h3l3 2c3 2 7-2 10 0h4l-1 1h1l1 1c0 1 0 1 1 1l4-1 1 4c-1 2-2 3-3 5h-1c-1 1-1 2-1 3v-1l1 2 1 1 3 6v4h0l-1-1-2 1c0 2 1 4 2 6-2-1-3-1-4-3h-3c1-2-3-5-4-7h1v-2c0-1-1-3-2-4h-3v1l3 4h0c-1 0-6-4-7-5h-1l3 3h-1l-1-1c-2-2-4-4-6-5l-2 1c-6-6-14-10-20-15 2 0 2 0 4 1 0 1 1 1 2 1h0 2l1-1h1 2c1-1 3 0 4-1l1 1c-1 1-2 1-3 1l-1 1h1v1l1-1h2 1 2l1-1z"></path><path d="M433 179l4-1 1 4c-1 2-2 3-3 5h-1l-1-1c-5 0-8-2-12-4 2-2 4-1 7-2 2 0 3-1 5-1z" class="a"></path><path d="M411 177h0v-3h3l3 2c3 2 7-2 10 0h4l-1 1h1l1 1c0 1 0 1 1 1-2 0-3 1-5 1-3 1-5 0-7 2l-1-1c-3-1-6-1-9-2v-1h3 0c-1-1-2-1-3-1z" class="e"></path><path d="M438 588c0 1 0 2-1 3h1l-1 1v1l1-1 1 1h1c0-2 1-9-1-11l1-1v-5c-1-3-1-7 0-10h0c1 4 1 9 1 13v4c1-1 1-1 1-2v-3c0-4-1-8 0-12 1-1 2-1 3-1 0 2 1 10 0 12l1 1h-1v3c0 2-1 2-2 3s-1 3-1 4c-1 8-1 16-1 24v25c0 3 1 6 1 9h-1c-1 2 1 5-1 8 1 1 2 1 3 2h1-1c-1 1-2 1-3 1l-1 1 2 1 1 1v1l1-2 2-1c0 2 0 2-1 4h0v1 4 2h-1v-3h-1c-1 2-1 4-3 6h-1c-2-1-3-1-5-1h-1 0-1c-1-1 1-1 1-2 0-4-1-6-1-9 1-1 1-1 2-1v-1l3-1v-1c1-2 1-6 2-8v1c0 1-1 1 0 3h1 1v-7h-1 0l-1 1v-2c-1 0-1-1-2-1h-1l-1-1h1v-30c0-5-1-10 0-15l-1-4v-7l1 1c1-2-1-6 0-8 2 3-1 6 3 9z" class="U"></path><path d="M442 660v1l1-2 2-1c0 2 0 2-1 4h0v1 4 2h-1v-3h-1c-1 2-1 4-3 6h-1c-2-1-3-1-5-1h-1 0-1c-1-1 1-1 1-2 0-4-1-6-1-9 1-1 1-1 2-1s3-1 4 0v4c0 2-1 5 0 7 1-1 1-8 1-10h0l1-1 1 1h1 1z" class="c"></path><path d="M433 659c1 0 3-1 4 0v4c-1 1-1 1-1 2v5h-1 0v-1l-1-1-1-1v3l-1 1h-1c-1-1 1-1 1-2 0-4-1-6-1-9 1-1 1-1 2-1z" class="d"></path><path d="M433 659c1 0 3-1 4 0v4c-1 1-1 1-1 2l-1 1-1-1-1-3-2-2c1-1 1-1 2-1z" class="R"></path><path d="M435 597h1v9c1-1 1-2 1-3 0-2 0-1 1-2v-1h0v7c0 1 1 1 1 2v21c0 4-1 10 1 13v2h-1 0l-1 1v-2c-1 0-1-1-2-1h-1l-1-1h1v-30c0-5-1-10 0-15z" class="Q"></path><path d="M347 154c5 0 10 1 15 2 18 3 36 7 54 11l8 3c1 0 3 0 4 1h3l1-1 2 1c0 1 0 1-1 2 0 1-1 2-2 3h-4c-3-2-7 2-10 0l-3-2h-3v3h0l-1 1h-2-1-2l-1 1v-1h-1l1-1c1 0 2 0 3-1l-1-1c-1 1-3 0-4 1h-2-1l-1 1h-2 0c-1 0-2 0-2-1-2-1-2-1-4-1-5-2-9-6-14-9-9-5-19-9-29-12z" class="T"></path><path d="M416 167l8 3c1 0 3 0 4 1h3l1-1 2 1c0 1 0 1-1 2h-1l-1-1-2 1h-4-4 1v-1c-2-1-5-1-7-2h1v-1h-1l1-2z" class="Z"></path><path d="M425 173h4l2-1 1 1h1c0 1-1 2-2 3h-4c-3-2-7 2-10 0l-3-2c3-1 7 0 11-1z" class="E"></path><path d="M576 724l9 5h1v2c1 1 2 2 3 4h0c6 9 11 22 10 33-1 4-1 8-2 12l-1 1c-1 2-1 4-2 5-2 4-5 8-8 11l-2-2 5-7-1-1 1-2-1-1-1-1-1 1 1 1h-1l-1 1c1 0 1 0 2 1h0-1c-1-1-1 0-1 0l-2 1h-1l2-4c2-2 4-6 4-9 3-7 2-15 0-22h-3l-2-2v2h-1l-1-2v-1c-1-2-2-2-2-3l-2 2v1l-1-2c0-2-1-3-2-4l1-1-1-1c1-3 0-6 2-8l1-1c0-3-1-4-2-6 0-1 0-2 1-3z" class="b"></path><path d="M588 784l2-1v-1-1c1 0 2 0 3-1h0c-1 0 0-1-1 0h0l-1-1h-1v-2h2l-1-2c1 0 1 1 2 1l1-1h0l1-1-1-1h-2l1-1v-1h-1c1-2 2-1 3-2l-1-1h1v-1l1-1c-1-1-1-2-1-2h-1c1-1 2-1 3-1 0 2 0 3 1 4l1 1c-1 4-1 8-2 12l-1 1c-1 2-1 4-2 5-2 4-5 8-8 11l-2-2 5-7-1-1 1-2-1-1z" class="X"></path><path d="M589 788l2-2 1 1 1-1v-2h1v-1h-1v-1c1 0 2-1 3-1-1 2-1 4-2 5-2 4-5 8-8 11l-2-2 5-7z" class="J"></path><path d="M576 724l9 5h1v2c1 1 2 2 3 4h0v1c0 3 2 5 3 8l1 2h-1c0 1 1 3 1 4l-1 1v-2c-1 0-1 0-1 1-1 0-2 0-3 1h-1c1 1 1 1 1 2h-3l-2-2v2h-1l-1-2v-1c-1-2-2-2-2-3l-2 2v1l-1-2c0-2-1-3-2-4l1-1-1-1c1-3 0-6 2-8l1-1c0-3-1-4-2-6 0-1 0-2 1-3z" class="X"></path><path d="M586 729v2c1 5 2 11 2 16l-1-1c-1-2-2-5-1-8v-2l-2-3c0-2 0-2 2-4z" class="I"></path><path d="M586 731c1 1 2 2 3 4h0v1c0 3 2 5 3 8l1 2h-1c0 1 1 3 1 4l-1 1v-2c-1 0-1 0-1 1-1 0-2 0-3 1h-1c1 1 1 1 1 2h-3l-2-2v2h-1l-1-2v-1c-1-2-2-2-2-3l-2 2v1l-1-2c0-2-1-3-2-4l1-1h0c4 1 8 5 12 5l1-1c0-5-1-11-2-16z" class="F"></path><path d="M625 458c6 0 12-2 18-3 23-4 45-12 65-27 2-2 5-3 7-5 1 0 1 0 2 1 0 3-1 5-2 7h0c-2 3-4 6-5 9 0 1 1 1 1 2-1 2-3 3-5 5-4 2-7 8-12 9l-6 6-3 3 1-2v-1-3-1c1-1 1-3 0-4-2 1-4 2-7 2-2 1-5 3-8 2l-1-1v1l-1 1h0l-1-1c0-2-1-2-2-3-2-1-2 0-4 0h0-1l2-1c1-1 1 0 2-1h0l-2-1-14 3-18 3c-2 0-4 1-6 0h0z" class="N"></path><path d="M698 441h1c2 0 6-4 8-5l2 1-6 5-2 2c-1-2-2-1-3-3z" class="e"></path><path d="M688 444l6-1c1 0 1 0 2-1h2v-1c1 2 2 1 3 3h-1c-1 1-1 1-1 2l-3 1h-1s-1 1-1 0h-1v-1l-5 1c1-1 1-1 2-1v-1l-2-1z" class="O"></path><path d="M709 437l2-2v-1c-1-1-1-1-2-3 0-2 1-2 2-4 1 1 0 6 0 7l3-3h1c-2 3-4 6-5 9 0 1 1 1 1 2-1 2-3 3-5 5-4 2-7 8-12 9 1-2 4-5 6-7 1 0 2-2 3-2h0l1-1c-1-1-3-1-4-2h1l2-2 6-5z" class="K"></path><path d="M700 444c1 1 3 1 4 2l-1 1h0c-1 0-2 2-3 2-2 2-5 5-6 7l-6 6-3 3 1-2v-1-3-1c1-1 1-3 0-4l3-2c1-1 3-1 4-2l1-3c0 1 1 0 1 0h1l3-1c0-1 0-1 1-2z" class="E"></path><path d="M700 444c1 1 3 1 4 2l-1 1h0c-1 0-2 2-3 2-2 2-5 5-6 7l-6 6v-2c0-1 1-1 2-2 0-1-1 0 0-1 2-2 5-7 8-8 2 0 3-2 4-3h-1c-1 0-1 1-1 1h-4l3-1c0-1 0-1 1-2z" class="H"></path><defs><linearGradient id="G" x1="675.275" y1="457.135" x2="676.83" y2="446.658" xlink:href="#B"><stop offset="0" stop-color="#484645"></stop><stop offset="1" stop-color="#716d69"></stop></linearGradient></defs><path fill="url(#G)" d="M677 447l12-6 1 1c-2 1-5 2-7 3l-1 1c2 0 4-1 6-2l2 1v1c-1 0-1 0-2 1l5-1v1h1l-1 3c-1 1-3 1-4 2l-3 2c-2 1-4 2-7 2-2 1-5 3-8 2l-1-1v1l-1 1h0l-1-1c0-2-1-2-2-3-2-1-2 0-4 0h0-1l2-1c1-1 1 0 2-1h0l-2-1 10-4 4-1z"></path><path d="M688 447l5-1v1h1l-1 3c-1 1-3 1-4 2l-1-1h1-1c-1-1-1-2-2-3l2-1zm-11 0h3c0 1 0 0-1 1-1 0-2 1-2 2-3 1-6 1-8 2h-3l-1 1-2-1 10-4 4-1z" class="f"></path><defs><linearGradient id="H" x1="594.368" y1="800.242" x2="628.699" y2="818.976" xlink:href="#B"><stop offset="0" stop-color="#595752"></stop><stop offset="1" stop-color="#948c80"></stop></linearGradient></defs><path fill="url(#H)" d="M596 795l9-14v1c3 15 13 29 22 41 0 0-1 0-2 1l-2 4c-1 3-2 4-2 8l-26-26-6 3c-1 1-2 1-3 2h0c0-1 2-2 3-3 2-1 3-3 5-4 0 0 0 1 1 1h3c1 1 0 1 1 1-1-2-2-2-3-3h-3l-1 1h-1c-1 1-4 1-6 1h0l-2 1v-1-2l2-1c1 0 2-1 3-2 3-3 6-6 8-9z"></path><defs><linearGradient id="I" x1="586.975" y1="809.914" x2="595.255" y2="803.533" xlink:href="#B"><stop offset="0" stop-color="#413f40"></stop><stop offset="1" stop-color="#595853"></stop></linearGradient></defs><path fill="url(#I)" d="M596 795h1c-1 2-2 3-3 4l-1 2h0c1 1 1 1 2 1 0 0 1 0 1 1h1c1 0 2 0 3 1l2 1 1 1v1l-1-1h-1c-1 0-1 0-1-1-1 0-2 0-3-1l-1 1 1 1-1 1h-3l-1 1h-1c-1 1-4 1-6 1h0l-2 1v-1-2l2-1c1 0 2-1 3-2 3-3 6-6 8-9z"></path><defs><linearGradient id="J" x1="785.135" y1="208.944" x2="774.141" y2="182.687" xlink:href="#B"><stop offset="0" stop-color="#5e5c59"></stop><stop offset="1" stop-color="#868278"></stop></linearGradient></defs><path fill="url(#J)" d="M789 180l5-9 5 31h-1l-1 1c-7 5-15 8-23 10-2 0-3 1-5 1l-2 1 1 2h-1c-5-4-5-12-7-17 4-4 9-8 13-12h0c1 0 4-3 5-4v-1c1-1 2-2 3-2 3-1 5 0 8-1z"></path><path d="M778 183c1-1 2-2 3-2 3-1 5 0 8-1v1c-3 2-7 2-11 3h0v-1z" class="b"></path><path d="M477 769c1 3 1 5 1 8h-1l-1 6 1 1c-1 6 0 12-1 19l1 1h1l-2 6v1c-1 3-4 5-5 8l-2 1v-1c1-1 1 0 1-1h-3c-3-1-5 0-7-2h10v-1c-5 0-11-2-16-4h2c-2-1-6-2-6-3h2c4 2 8 3 12 3 2 0 3 1 4 0v-1h-1-1l-5-1-2-1h0c-2-1-4-3-5-5h1l-1-1c0-1 0-1 1-2l1 1v-1l1-1h1c1 0 2 1 2 1 1 1 1 1 1 2l1-1v-2l-5-3-2 2h0c0-1-1-2-1-2l-1-1v-2h0l-2 2-10-8v-1c4-2 10-7 13-11 1-1 1 0 2-1 1 2 2 3 2 5 2 0 3 0 5 1h1 1c1 0 2 1 2 1 1 0 2 0 3-1v1 1c1-1 1-1 1-2h1 1v-1-1c0-1 1-1 1-2s-1-1-1-2h-1-1v-1l1-2h0l1-1 1-1h3 0z" class="B"></path><path d="M462 801v-1l2-2h1c0 1 1 1 2 1s1 1 2 1c0 3 0 7-1 10h-1-1l-5-1-2-1h0c-2-1-4-3-5-5h1l-1-1c0-1 0-1 1-2l1 1v-1l1-1h1c1 0 2 1 2 1 1 1 1 1 1 2l1-1z" class="Q"></path><path d="M477 769c1 3 1 5 1 8h-1l-1 6 1 1-2 1-1 1c-2 1-2 1-3 1l-1-1c-2 0-4 0-6 2h1v1h-1v-1l-1 1v3-3c-1-2-1 0-1-2h1 1c0-2-2-3-3-4l-1-1-2-3c2 0 3 0 5 1h1 1c1 0 2 1 2 1 1 0 2 0 3-1v1 1c1-1 1-1 1-2h1 1v-1-1c0-1 1-1 1-2s-1-1-1-2h-1-1v-1l1-2h0l1-1 1-1h3 0z" class="C"></path><path d="M474 781c1 1 2 0 2 2l1 1-2 1-1 1c-1-2 0-3 0-5z" class="D"></path><path d="M472 771h0l1-1 1-1h3v1 2c0 1 0 1-1 2l1 2h-1v-1h-1v3l-2 1v-1c0-1 1-1 1-2s-1-1-1-2h-1-1v-1l1-2z" class="U"></path><path d="M475 785l2-1c-1 6 0 12-1 19l1 1h1l-2 6v1c-1 3-4 5-5 8l-2 1v-1c1-1 1 0 1-1h-3c-3-1-5 0-7-2h10v-1c-5 0-11-2-16-4h2v-1c1 1 1 1 2 1h1 0c3 1 7 2 10 2 1-4 1-9 2-13v-13c1 0 1 0 3-1l1-1z" class="K"></path><path d="M475 785l2-1c-1 6 0 12-1 19l1 1h1l-2 6h-1c0 2-1 2-2 3-1-3 2-6 2-9 0-6 1-13 0-19z" class="C"></path><path d="M444 662l1 1v2 1h1 0 2c1 2 0 5 1 7h0c-1 1-1 1-2 1l-1 1c1 1 1 1 2 1l5-1c-1 4-1 7-1 11v4 27 3 4l-2 2-2 1-1-5v-2c-5 3-9 6-13 9v-1l3-3c2-1 3-1 4-2h0-3c-2 0-3 2-5 3h-1l-1-1c0-1 1-2 1-3v-13c0-3 1-6-1-9 1-3 1-7 1-10v-19h1c2 0 3 0 5 1h1c2-2 2-4 3-6h1v3h1v-2-4-1z" class="B"></path><path d="M437 714l1-2 1-1c1 0-1 5 1 7 1-1 1-2 2-4 1 1 2 1 3 2 1-1 1-1 1-2v-2c0-1 0-2 1-3 1 1 0 3 1 4v1c1 4 0 9 2 12l-2 1-1-5v-2c-5 3-9 6-13 9v-1l3-3c2-1 3-1 4-2h0l6-5v-1h-1c-3 1-6 2-9 4v-7z" class="E"></path><path d="M432 671h1l3 2 1 17c0 2 0 7-1 8 0 1-2 2-3 2h-2c1-3 1-7 1-10v-19z" class="J"></path><path d="M444 662l1 1v2 1h1 0 2c1 2 0 5 1 7h0c-1 1-1 1-2 1l-1 1c1 1 1 1 2 1v1h-2c1 2 0 4 0 5-1 1-1 1-3 1-1-2-1-6-1-9h0c0 2-1 5 0 7v2c-2 0-3 0-4-1s-1-3 0-4c0-1-1-4 0-5s2-1 2-1h-2 1c2-2 2-4 3-6h1v3h1v-2-4-1z" class="a"></path><path d="M431 700h2c1 1 3 1 4 2 1 2 0 9 0 12v7c3-2 6-3 9-4h1v1l-6 5h-3c-2 0-3 2-5 3h-1l-1-1c0-1 1-2 1-3v-13c0-3 1-6-1-9z" class="d"></path><path d="M453 675c-1 4-1 7-1 11v4 27 3 4l-2 2c-2-3-1-8-2-12v-1-24-12-1l5-1z" class="I"></path><path d="M453 675c-1 4-1 7-1 11h-1v-1c1-2 0-4 0-6v-1h0c0 2-1 5 0 6l-1 1-1-1c0 2 1 8 0 10 0-2 0-4-1-5v-12-1l5-1z" class="R"></path><defs><linearGradient id="K" x1="461.277" y1="256.138" x2="466.813" y2="254.95" xlink:href="#B"><stop offset="0" stop-color="#53514c"></stop><stop offset="1" stop-color="#6b6a66"></stop></linearGradient></defs><path fill="url(#K)" d="M453 211l6-3 1 1-1 1 1 2v3h1c1 2 2 3 4 5l-2 1c3 3 7 7 9 11h0c2 1 3 4 3 6v1 15c1 2 0 3-1 5l-2 2h-1c0 4 0 9-1 14h0l-2-1v1h-1v1h-2v-1h-1 0c-1 1-1 1-1 2l-1-2c-1 0-1 1-1 1h-1c-1-1-3-1-4-1 0-2 1-2 2-3-1 0-2 0-2-1l-1-5v-28-1-6l-2-1c0 1-1 2-1 3-2-2-2-4-4-5v-1c3-3 4-6 5-10 0-1 1-2 2-4h1 0c-1-1-2-1-3-2z"></path><path d="M468 244c0-2 0-5 1-6v9l2 14c0 4 0 9-1 14h0l-2-1-1-1c1-3 0-6 0-9l-1-19 2-1z" class="C"></path><path d="M461 230l3 3v3h1v-2l1-1 2 2c1 1 2 3 2 4 0 2 0 6-1 8v-9c-1 1-1 4-1 6h-3c1-1 1-2 1-3v-1c-2 1-1 3-3 4v-3l-1-1v-1h-2c-2-1-2-2-2-4v-4h2l1-1z" class="O"></path><path d="M458 235v-4h2c2 1 2 1 3 3 0 2 0 4-1 5h-2c-2-1-2-2-2-4z" class="D"></path><path d="M472 238l-2-5 1 1h1c1 2 2 3 3 5v15c1 2 0 3-1 5l-2 2h-1l-2-14c1-2 1-6 1-8 0-1-1-3-2-4 2 0 3 2 4 3h0z" class="B"></path><path d="M468 235c2 0 3 2 4 3h0l1 1c-1 1-1 1-1 2 1 5 0 11 0 17 1 0 1 1 2 1l-2 2h-1l-2-14c1-2 1-6 1-8 0-1-1-3-2-4z" class="Q"></path><path d="M455 237c1 0 1 0 2-1 0-1 0 0 1-1 0 2 0 3 2 4h2v1c0 1-1 2 0 3v18l-1 10c-1 1-2 1-3 1s-2 0-2-1l-1-5v-28-1z" class="h"></path><path d="M462 243v18l-1 3-2-3c1-1 1-1 1-2v-1c0-1 1-2 1-3-1-1-1-2 0-4v-3l1-5z" class="d"></path><path d="M455 238l1 2c1 3 0 8 0 11 1 4 2 8 1 12l1 1c1 0 1 0 2 1h1v-1l1-3-1 10c-1 1-2 1-3 1s-2 0-2-1l-1-5v-28z" class="c"></path><path d="M453 211l6-3 1 1-1 1 1 2v3h1c1 2 2 3 4 5l-2 1c3 3 7 7 9 11h0c2 1 3 4 3 6v1c-1-2-2-3-3-5h-1l-1-1 2 5h0c-1-1-2-3-4-3l-2-2-1 1v2h-1v-3l-3-3-1 1h-2v4c-1 1-1 0-1 1-1 1-1 1-2 1v-6l-2-1c0 1-1 2-1 3-2-2-2-4-4-5v-1c3-3 4-6 5-10 0-1 1-2 2-4h1 0c-1-1-2-1-3-2z" class="b"></path><path d="M453 211l6-3 1 1-1 1 1 2v3h1c1 2 2 3 4 5l-2 1-4-4-1 1 1 1h-1c-1-1-1-1-3-2 0-1 0-2 1-4h0c-1-1-2-1-3-2z" class="H"></path><path d="M453 217l1 1v7h1l1-1 2 2h0l-1-1-1 1h-2l-1 2h1c1-1 1-1 2-1-1 1 0 1-1 2 0 0 1 0 1 1h-3c0 1-1 2-1 3-2-2-2-4-4-5v-1c3-3 4-6 5-10z" class="I"></path><path d="M461 230c-1-1-2-1-2-2s1-1 2-1c2 0 4 1 6 1-2-2-5-4-6-6h1a30.44 30.44 0 0 1 8 8c0 1 1 1 2 2h0c2 1 3 4 3 6v1c-1-2-2-3-3-5h-1l-1-1 2 5h0c-1-1-2-3-4-3l-2-2-1 1v2h-1v-3l-3-3z" class="a"></path><path d="M572 492c1 1 5-1 6-1 3-1 6-1 8-1 3-1 7 0 10-1 2-1 3-1 4-1l-1 1 3 2c4 3 8 5 11 8l8 8c2 1 3 2 4 4l1 2h-5c-2-2-8 0-11-1-1 0-2-1-2-1-1-1-2-2-2-3-3 0-5 2-8 3-2 1-4 1-7 2h-9v2l1 1 1 6v5h0l-1-2-1 1 2 2-1 1c1 0 1 0 2-1h2v1h0c-2 1-5 1-8 1h0l1-1 2-1h-1c-5-3-6-6-8-10 0-2 0-3-1-5-1-1-2-2-3-2h0c-1-2-2-3-1-5 0-4 0-8 1-11 1-2 2-2 3-3z" class="K"></path><path d="M581 502l-4 2c0-4 1-7 3-10h2l-1 1-1 2h1c1 1 1 3 2 4l-1 1h-1 0z" class="b"></path><path d="M583 501l4-4c1 1 2 3 3 4l1 1v2c-3 2-7 3-10 5h-1c-1-1 0-1-1-1s-2-1-2-1c0-2 3-3 4-5h0 1l1-1z" class="e"></path><path d="M583 501l4-4c1 1 2 3 3 4l1 1-1 1c-1 0-2 0-3-1h-1-3 0-1l1-1z" class="N"></path><path d="M582 513l-1-1h-1l-1-1c2 0 3-1 4-1l9-3h1c4 0 8-1 12 0l1 1c-3 0-5 2-8 3-2 1-4 1-7 2h-9z" class="O"></path><path d="M615 503c3 2 6 6 10 8l1 2h-5c-2-2-8 0-11-1-1 0-2-1-2-1 0-1 0-2-1-3s-2-2-4-3h2c3-1 7-1 10-2z" class="T"></path><path d="M581 528c-5-3-6-6-8-10 0-2 0-3-1-5-1-1-2-2-3-2h0c-1-2-2-3-1-5 0-4 0-8 1-11 1-2 2-2 3-3-3 5-3 12-2 18l3-2c1-1 2-1 3-2v1c-2 1-1 1-2 2 1 2 1 1 2 3l-1 1c0 2 1 3 3 5l1 1h0c1 1 2 1 2 2l2 2c1-1 0-2 0-4h0c0-2 0-2-1-3v-1l1 1 1 6v5h0l-1-2-1 1 2 2-1 1c1 0 1 0 2-1h2v1h0c-2 1-5 1-8 1h0l1-1 2-1h-1z" class="C"></path><defs><linearGradient id="L" x1="603.657" y1="507.287" x2="594.042" y2="490.298" xlink:href="#B"><stop offset="0" stop-color="#79746d"></stop><stop offset="1" stop-color="#a59b8c"></stop></linearGradient></defs><path fill="url(#L)" d="M582 494c5-1 12-2 17-1h1c5 1 11 6 15 10-3 1-7 1-10 2h-2c-5-1-10 1-15 2l1-1 4-2c0-1 0-2-1-3 0-2-2-3-4-4h-1l-4 4c-1-1-1-3-2-4h-1l1-2 1-1z"></path><path d="M581 495h2v1c0 1 1 1 2 2v-1h1 0c1-1 0 0 2-1h0 0v1h-1l-4 4c-1-1-1-3-2-4h-1l1-2z" class="J"></path><defs><linearGradient id="M" x1="717.992" y1="539.104" x2="680.003" y2="545.4" xlink:href="#B"><stop offset="0" stop-color="#494846"></stop><stop offset="1" stop-color="#62605e"></stop></linearGradient></defs><path fill="url(#M)" d="M685 520c1 0 2 0 4-1h0c7-2 12-6 18-9 1-1 2-1 3-1h1c1 1 1 1 2 1h1c1-1 1-1 2-1l-21 50-8 20-4-12v-1l1-1h-1c0-1-1-2-1-2v-1c-2-7 0-15-1-22-1-2-1-3-1-5v-1h1v2c1-1 1-1 1-2h0c1-2 0-3 0-4l1-1v-2-2c1-2 1-3 2-5z"></path><path d="M680 535c1 1 1 2 1 3l1 1c1 4 0 9 0 14l1 12c0-1-1-2-1-2v-1c-2-7 0-15-1-22-1-2-1-3-1-5z" class="P"></path><path d="M578 578l1-1c1-3-1-7 0-11h1c1 2 0 7 0 9 0 1 0 2 1 3h0c1 0 2 0 3 1h0c0-1 0-5 1-6v67 2h-1c-1 0-1 0-1 1-1 2 0 4 0 7v3l1 1-1 1-1 2c1 1 1 1 2 1v1h-1c-4 0-8-1-12 0v7c0-1-1-1-2-1v-1c-1-2 0-6-1-9v-1-7-6-19c0-4 1-8 0-12 1-3 0-7 0-11 1 0 2-1 2-2 1-1 1-2 1-3s0-1-1-2c-1-2 0-4 0-6 1 2 4 4 5 6v4h1c0-2 0-2 2-3v1c0-3 1-8 0-11v-1l1-1c-1-1 0-2-1-3z" class="K"></path><path d="M579 644c1-6-1-13 0-19 0 0 1 0 1-1-1-8-1-17-1-25 1-2 1-4 1-6 1-4 0-9 0-13h1v2c0 1 0 1-1 2v3l2 1 1-1v6l-2 1c-1 1-1 3-1 4 1 3 0 7 0 10v20c0 5 1 11 0 16v2h-1v-2z"></path><path d="M579 644v2h1v-2c1-5 0-11 0-16v-20c0-3 1-7 0-10 0-1 0-3 1-4l2-1v3c-1 0-1-1-2-1l1 1-1 2h0c1 2 0 6 0 8v23c0 5 0 10 1 15v7 1c-2-1-1-1-2-1s-2-1-2-1h0l-1 2h-1c-1-1 0 0-1 0l-1 1v3 1h0-1v-5l-2-1v-2c4-1 6-1 7-5h1z" class="I"></path><path d="M568 647l2 2h1v2l2 1v5h1 0v-1-3l1-1c1 0 0-1 1 0h1l1-2v1c1 1 3 2 5 2l1 1-1 1-1 2c1 1 1 1 2 1v1h-1c-4 0-8-1-12 0v7c0-1-1-1-2-1v-1c-1-2 0-6-1-9v-1-7z" class="S"></path><path d="M568 647l2 2h1v2 3 1 1c-2-1-2-1-3-2v-7z" class="C"></path><path d="M574 653l1-1c1 0 0-1 1 0h1l1-2v1c1 1 3 2 5 2l1 1-1 1c-2 2-2 2-5 2-1-1 0-2-2-4v1l-1 1-1-2z" class="J"></path><path d="M570 586c1 2 4 4 5 6v4h1c0-2 0-2 2-3v1 50h0c-1 4-3 4-7 5h-1l-2-2v-6-19c0-4 1-8 0-12 1-3 0-7 0-11 1 0 2-1 2-2 1-1 1-2 1-3s0-1-1-2c-1-2 0-4 0-6z"></path><path d="M544 528c2-1 3-1 4-2s0-1 2-1l2 1h3c2 1 3-1 5 0v1c-2 3-1 9-1 12l1 1v7 3l1 1c1-2-1-5 1-7 0 8-1 18 1 26v3l1 1h1l2-1v1c2 0 3 1 5 0v-1c-1-1 0-1 0-2v-1c0-2 1-2 2-3 0-1 0-1 1-1v-1l1 1-1 1c0 1 0 1 1 2v10h1c0-2 0-6-1-8 0-1 0-2 1-3v-1h0c1 1 1 2 1 3v1 7c1 1 0 2 1 3l-1 1v1c1 3 0 8 0 11v-1c-2 1-2 1-2 3h-1v-4c-1-2-4-4-5-6 0 2-1 4 0 6l-1 1h-8-2c-4-1-7 0-11 0l-1 1-1 1 1 2h-1l-3-1v-1-2l-1-1 1-1v-2c0-3 0-7-1-10l1-3c2 0 5-1 7-1l-1-1c1-1 1-1 1-2-1-1-1-3-1-5 0-1-1-2-2-3 1-1 1-2 1-2 0-2-1-3 0-5 0-2 0-4 1-6l-1-1c0-1-1-2-1-4 1-3 1-8 1-12v-4c1-1 1-1 0-2h0-4z" class="G"></path><path d="M552 534v3c1-1 0-5 1-6v-3h1v10 2 4c1-1 0-3 0-5 0-3 1-6 1-9 0-1 0-2 1-2 1 2 0 11 0 14 0 2-1 4 0 7l1 8v-9c-1-2 0-6 0-9v-10l1-1c1 2 1 28 1 32-1 4 0 9-2 12h0c-1-1 0-1 0-2-1-2-1-4-1-5l1-1v-4c-1 1 0 2-1 3v2h0c-1-1-1-3-1-4 0-3 1-9 0-12-1 1-1 16-1 18l1 1h0c-1 2-1 4-1 5h-1l-1-1v-1l-2 1c-1-1-1-3-1-5 0-1-1-2-2-3 1-1 1-2 1-2 0-2-1-3 0-5 0-2 0-4 1-6l-1-1c0-1-1-2-1-4 1-3 1-8 1-12v-4c1-1 1-1 0-2h0l4 1v5h0z" class="T"></path><path d="M548 557c0 2 1 3 2 5v4c1 0 2 1 2 1 0 3 1 3 0 5v-1l-2 1c-1-1-1-3-1-5 0-1-1-2-2-3 1-1 1-2 1-2 0-2-1-3 0-5z" class="P"></path><path d="M548 528l4 1v5h0l-1-1v-1c-1 2 0 4-1 5-1 4 1 12-1 14l-1-1c0-1-1-2-1-4 1-3 1-8 1-12v-4c1-1 1-1 0-2h0zm2 47h9c2 1 5 0 7 1 3 0 4 1 6 3h0-2c0 2-1 5 0 7 0 2-1 4 0 6l-1 1h-8-2c-4-1-7 0-11 0l-1 1-1 1 1 2h-1l-3-1v-1-2l-1-1 1-1v-2c0-3 0-7-1-10l1-3c2 0 5-1 7-1z" class="V"></path><path d="M552 578c1 0 4 0 5 1 1 0 1 0 2 1h-2l-1 2c-1 0-2 0-3 1l-1-1v-1h1v-1h-3c0 1 0 1 1 2l-1 1 1 1c1 0 3 0 4-1h1c2 3 0 6 0 8v1l-2-1c0-1 0-1 1-1l-2-2h1l-1-1v-2l-3-1-1 1-1-3c0-1-1-1-2-1v-1c2-1 4-1 6-2z" class="F"></path><path d="M550 575h9c2 1 5 0 7 1-2 0-3 0-5 1h0c-1-1-1-1-2 0-1 0-1-1-2 0s-2 1-3 1h-1v-1h-1v1c-2 1-4 1-6 2-1 2-1 2-2 3l1 1 1-2v1l2 1v1c-2 0-3 0-4 1l1 1v-1c1 0 2 0 3 1-1 1-1 2-2 3h-2v1h2c1 0 2 1 2 1v1l-1 1-1 1 1 2h-1l-3-1v-1-2l-1-1 1-1v-2c0-3 0-7-1-10l1-3c2 0 5-1 7-1z" class="X"></path><path d="M566 576c3 0 4 1 6 3h0-2c0 2-1 5 0 7 0 2-1 4 0 6l-1 1h-8l1-1c1-1 2-1 3-2h1c0-1 0-2-1-3h-1s-1 1-1 2 1 1 0 2h-1l-1-2v-1l-1-2c0-1 1-2 1-4l1 1 1 1 1-1v-2h1v3h0v1c1-1 1-3 1-5h-2-2s-1 0-2-1l-1-2c1-1 1-1 2 0h0c2-1 3-1 5-1z" class="I"></path><path d="M444 485c1 0 1 0 2-1h0c0-2 1-2 2-3h1c1-1 2-1 4-1 0 4-1 9 0 13v20l1-1c0 1 0 2 2 3l-2 6v1h1l2 1c-3 1-3 1-4 3l-2 1c-1 1-2 2-2 4l-1 1v1c-1 1-1 2-2 3v7l-4-1h-5v-1c-1 0-2 0-3-1 0-1 0-1-2-1-1-2-4-5-5-7-1-3-3-7-4-11-2-8 1-18 5-26h2 0 1c-1 1-2 3-3 5h1c0 1 1 1 2 0v-1-1c0-1 1-2 2-3 0-1 2-2 2-3l8-8 1 1z" class="M"></path><path d="M446 530l-1-1-1 1c0-3 2-4 1-7h-1c0-2-1-4-2-5l-3-8c-1-3 0-6 1-9v-2h1c0-2 1-4 2-6l2-4h3c1 1 0 4 1 6v2c-1 2-1 5 0 7 0 2 1 4 0 6 0 1 0 0-1 1l-1-1c0 2 2 3 4 4h1l1-1 1-1c0 1 0 2 2 3l-2 6v1h1l2 1c-3 1-3 1-4 3l-2 1c-1 1-2 2-2 4l-1 1v1c-1 1-1 2-2 3v-6z" class="K"></path><path d="M453 513l1-1c0 1 0 2 2 3l-2 6v1h1l2 1c-3 1-3 1-4 3l-2 1c-1 1-2 2-2 4l-1 1v1c-1 1-1 2-2 3v-6h0c1-3 1-5 4-7l-1 1v-9l2-1h1l1-1z" class="W"></path><defs><linearGradient id="N" x1="439.977" y1="543.52" x2="435.023" y2="529.98" xlink:href="#B"><stop offset="0" stop-color="#080601"></stop><stop offset="1" stop-color="#2f2d2c"></stop></linearGradient></defs><path fill="url(#N)" d="M433 495c0-1 2-2 2-3l8-8 1 1c-2 1-3 2-4 4l-2 2h1c1-1 2-3 4-4-2 2-3 5-5 7l1 1c1-2 2-4 3-5 0 3-2 4-4 6 0 1-1 2-1 2-1 1-1 2-2 3v2h-2v1h1c1 2 0 2 0 3l-1 1-1-1-1 1v1h3c0 2-1 3 0 4v2c0 1 2 2 2 4-1 1-2 1-3 1l-1 1h-2v1c2 0 3 0 5-1h1l1 2-1 1 1 2c2 1 2 1 3 2h-2-1 0l2 2c0 1 1 1 1 2h1l-1-1v-1c1 0 1 1 2 3v1c1 2 1 3 2 4 0 1 0 1-1 1 2 1 2 1 3 2v1h-4-5v-1c-1 0-2 0-3-1 0-1 0-1-2-1-1-2-4-5-5-7-1-3-3-7-4-11-2-8 1-18 5-26h2 0 1c-1 1-2 3-3 5h1c0 1 1 1 2 0v-1-1c0-1 1-2 2-3z"></path><path d="M428 495h2 0 1c-1 1-2 3-3 5h1c0 1 1 1 2 0v-1-1c0-1 1-2 2-3v1l-1 2h1c0 2-1 2 0 3v1h-2c-2 0-2 0-3 1l-3 1c0 3-1 6-1 10h0c0 4 0 8 1 12 1 1 2 4 2 6-1-3-3-7-4-11-2-8 1-18 5-26z" class="c"></path><path d="M437 526c2 1 2 1 3 2h-2-1 0l2 2c0 1 1 1 1 2h1l-1-1v-1c1 0 1 1 2 3v1c1 2 1 3 2 4 0 1 0 1-1 1l-1-1c-1 0-2-1-2-1h-1v-1c-2-1-3-1-5-2s-3-1-4-3l-1-2c-1-1-1-1-1-2h1l2 2c0-1 0 0 1-1v1l1 1v-1c1-2 3-2 4-3z" class="F"></path><path d="M562 213c0-1-1-2-1-3h0l3 2c1 1 1 2 1 4 0 1 1 1 1 2l2-3c0 2 3 8 4 10l5 9c1 3 3 5 3 8 1 10-2 19-2 28l2 2-1 2h-2l-1-2-1-1c-1 0-1 0-1 1l-1 1-1-1-1-1v-1l-1 1-1 1c-1 0-2 2-3 3l-1 1c-1-1-1-2 0-3v-4l-4-1h0-3l-1 1-1 1h0l1-2h-1v-1c-1-1 0-2-1-3h0v-3l-2-1h-1l1-2c-1 0-2-1-3-2l-1-8v-1-2c-1-1-2-1-3-2 0-1 1-2 1-4h-1c1-5 2-7 5-11 0-1 1-2 1-3-1 0-2-1-4-1 2-1 4 0 6-1 3-3 5-6 6-11l2 1z" class="d"></path><path d="M572 272l1-1v-1-1c1-3 0-6-1-8l1-2h2v1c1 1 1 1 1 2h1l-1 5 2 3 2 2-1 2h-2l-1-2-1-1c-1 0-1 0-1 1l-1 1-1-1z" class="I"></path><path d="M580 242c1 10-2 19-2 28l-2-3 1-5v-4c1-4 0-7 1-10 0-1 1-1 1-1l1-5zm-14-24l2-3c0 2 3 8 4 10l-2 3c-1-1 0-1-1-2-2-1-5-1-6 0l-1 1 1 1v-1h2 2l1 1-1 1c-1 2 0 5-1 7-1-1-1-2-1-2 0-2 0-3-1-4h-3-1c0-1 0-1-1-3h-1l2-2h0l6-7z" class="X"></path><path d="M568 228c0 1 2 4 2 4 1 1 2 2 2 3h0c1 1 3 3 3 5 2 3 1 9 1 13 0 2-1 3-2 4-1 0-1 1-2 1v-1l-1-1v1 1c1 1 0 3 0 4v9-1l-1 1-1 1c-1 0-2 2-3 3l-1 1c-1-1-1-2 0-3v-4c1-3 0-8 0-11v-15c0-1 1-4 0-5 0-1-1-2-1-2l-3-3v-1h1l-1-2h3c1 1 1 2 1 4 0 0 0 1 1 2 1-2 0-5 1-7l1-1z" class="P"></path><path d="M570 232c1 1 2 2 2 3h0 0c0 2 0 4 1 6v8c0 2 2 5 1 7h-1l-1-1-1-1-1-2 1-1c0-1-1-2-1-3v-16z" class="J"></path><path d="M562 213c0-1-1-2-1-3h0l3 2c1 1 1 2 1 4 0 1 1 1 1 2l-6 7h0l-2 2h1c1 2 1 2 1 3h1l1 2h-1v1l3 3s1 1 1 2c1 1 0 4 0 5v15c0 3 1 8 0 11l-4-1h0-3l-1 1-1 1h0l1-2h-1v-1c-1-1 0-2-1-3h0v-3l-2-1h-1l1-2c-1 0-2-1-3-2l-1-8v-1-2c-1-1-2-1-3-2 0-1 1-2 1-4h-1c1-5 2-7 5-11 0-1 1-2 1-3-1 0-2-1-4-1 2-1 4 0 6-1 3-3 5-6 6-11l2 1z" class="e"></path><path d="M553 258h0c-1-2 0-4-1-6h0l1-1 1 1v-1l-1-1c1-1 1-2 1-2 1-1 0-1 1-1 0 5 1 12 0 17h0v-3l-2-1h-1l1-2z" class="D"></path><path d="M561 248c1 3 2 5 2 8v2-1l1 2 1-1c0 3 1 8 0 11l-4-1v-1-2-17z" class="d"></path><path d="M549 245c1 1 1 1 2 1l1 1 1-1h1v-1h0-1v-2l1-1 1 1 2-2c-1 2-1 4-2 6-1 0 0 0-1 1 0 0 0 1-1 2l1 1v1l-1-1-1 1h0c1 2 0 4 1 6h0c-1 0-2-1-3-2l-1-8v-1-2z" class="T"></path><path d="M561 248v-10l1-1 2-1s1 1 1 2c1 1 0 4 0 5v15l-1 1-1-2v1-2c0-3-1-5-2-8z" class="I"></path><path d="M562 213c0-1-1-2-1-3h0l3 2c1 1 1 2 1 4 0 1 1 1 1 2l-6 7h0l-2 2h1c1 2 1 2 1 3-1 0-2 0-2 1-1 1-1 3-1 4l-1-1-2 1h1c0 1 1 2 2 2v-1l1 1v1h-2l1 1v2l-2 2-1-1-1 1v2h1 0v1h-1l-1 1-1-1c-1 0-1 0-2-1s-2-1-3-2c0-1 1-2 1-4h-1c1-5 2-7 5-11 0-1 1-2 1-3-1 0-2-1-4-1 2-1 4 0 6-1 3-3 5-6 6-11l2 1z" class="Z"></path><path d="M547 239l2-3v1h0v8c-1-1-2-1-3-2 0-1 1-2 1-4z" class="F"></path><path d="M559 227c1 2 1 2 1 3-1 0-2 0-2 1-1 1-1 3-1 4l-1-1-2 1h1c0 1 1 2 2 2v-1l1 1v1h-2l-2-2h0c-1 1-1 1-2 1v-2h-1l-1-1c3-1 4-4 7-5l1-1 1-1z" class="I"></path><path d="M562 213c0-1-1-2-1-3h0l3 2c1 1 1 2 1 4 0 1 1 1 1 2l-6 7h0-2l-2 2h0v-2c-1 0-1 0-1-1 2-2 5-4 6-6 0-1 0-2 1-2 0-1 1-1 1-2l-1-1z" class="O"></path><path d="M542 806l1 1-1 1 1 1v1c0 2 1 2 1 3l1 1c5 6 10 14 15 21 1 2 3 6 6 8h1c1 2 2 4 4 6l9 13v1l1 1 3 4v1l-1-1h-6c-2 1-6 0-9 0-8 0-15 0-22-1-2 0-2-1-4-1l-1 4s-3 0-3 1c-1 3 2 10-1 12 0-1-1-2-1-3-1-2-2-7 0-9h1v-1c-3-1-1-4-3-6l1-2c0-1 0-2 1-3 1-3 1-5 1-8 0-1 0-3 1-4v-9l1-5c1-2 1-5 1-8v-18h2v-1z" class="D"></path><path d="M541 851c1-1 1-2 2-3 0-1 0-3 1-4 1-4 3-11 2-15 1 0 2 1 3 1 0 2-1 4-1 6l-5 17c4 0 23 0 24 1-2 2-21 0-25 1h0c-1-1-1-2-1-4z" class="G"></path><path d="M541 844c1 2 1 5 0 7 0 2 0 3 1 4h0c0 2 0 3 1 4l17 1c3 0 8-1 11 0-6 1-25-2-28 1v3h1 2c3 1 11 0 13 2-3 0-9 1-12 0-1-1-2 0-3 0 1 1 1 1 2 1 2 0 4-1 6 0 1 0 4 1 5 0 1 0 6-1 8 0 4 0 7 0 11 1h1 0c-2 1-6 0-9 0-8 0-15 0-22-1-2 0-2-1-4-1h-1c-1-6-1-16 0-22z" class="E"></path><path d="M537 851l1 2c2 0 2-11 3-13v4c-1 6-1 16 0 22h1l-1 4s-3 0-3 1c-1 3 2 10-1 12 0-1-1-2-1-3-1-2-2-7 0-9h1v-1c-3-1-1-4-3-6l1-2c0-1 0-2 1-3 1-3 1-5 1-8z" class="f"></path><defs><linearGradient id="O" x1="549.35" y1="838.719" x2="529.15" y2="820.781" xlink:href="#B"><stop offset="0" stop-color="#474d48"></stop><stop offset="1" stop-color="#645b5d"></stop></linearGradient></defs><path fill="url(#O)" d="M542 806l1 1-1 1 1 1v1c0 2 1 2 1 3l1 1c-2 2-1 4-1 6 0 3-1 6-1 9 1 2 0 6 0 8l-1 1v2h0l-1-1v1c-1 2-1 13-3 13l-1-2c0-1 0-3 1-4v-9l1-5c1-2 1-5 1-8v-18h2v-1z"></path><path d="M542 806l1 1-1 1 1 1v1c0 2 1 2 1 3l1 1c-2 2-1 4-1 6 0 3-1 6-1 9 1 2 0 6 0 8l-1 1v2h0l-1-1 1-32v-1z" class="B"></path><path d="M663 452l2 1h0c-1 1-1 0-2 1l-2 1h1 0c2 0 2-1 4 0 1 1 2 1 2 3l1 1h0l1-1v-1l1 1c3 1 6-1 8-2 3 0 5-1 7-2 1 1 1 3 0 4v1 3 1l-1 2c-3 5-10 11-15 13 1-1 2-3 4-3 1-1 1-1 1-2-2 1-3 1-5 1h-1c-7 1-14 1-22 1h-11-27c-3 0-8 1-10-1-1-1-1-3-1-5s0-3 2-4l-1-1h-1l-1-2v-1l-1-1c1-1 2-2 3-1l24-1c1-1 1-1 2 0h0c2 1 4 0 6 0l18-3 14-3z" class="f"></path><path d="M627 462l10-1v4h-2l-2 3h-1c-1-1-1-2-1-4v-1c-1 1-1 3-1 5h-1v-3-1l-1-1c-1 0 0-1-1-1z" class="C"></path><path d="M614 463h3v1l1 1c1 0 2-1 3-1h1c0 1 0 3 1 4h4v-1l1 1h0c1 1 3 1 5 0 1 0 3 1 5 1s5 0 8 1c4 1 10 0 14 4-4 1-10 1-15 0h3l-11-3c-4 0-8-1-12-1-1 0-1-1-2-1-2 0-4 1-6 0v-3c-1 1-1 2-2 2-1 1-1 1-2 0h1v-5z" class="N"></path><path d="M604 469c1-2 1-4 2-6h1c0 1 0 2 1 2v1l1 1c0-1 0-1 1-2l1 1v-1c1 1 1 2 2 3s1 1 2 0c1 0 1-1 2-2v3c2 1 4 0 6 0 1 0 1 1 2 1 4 0 8 1 12 1l11 3h-3c-3 1-6 0-9 1h-27c-3 0-8 1-10-1-1-1-1-3-1-5s0-3 2-4v4h4z" class="J"></path><path d="M604 469c1-2 1-4 2-6h1c0 1 0 2 1 2v1l1 1c0-1 0-1 1-2l1 1v-1c1 1 1 2 2 3s1 1 2 0c1 0 1-1 2-2v3c2 1 4 0 6 0 1 0 1 1 2 1-4 1-11 2-15 1-1-1 0-1-1-1-2 0-3 0-5-1z" class="c"></path><defs><linearGradient id="P" x1="617.199" y1="471.811" x2="629.613" y2="447.58" xlink:href="#B"><stop offset="0" stop-color="#0f0e09"></stop><stop offset="1" stop-color="#383539"></stop></linearGradient></defs><path fill="url(#P)" d="M649 455l1 1h-2v1c1 1 1 2 1 3v1h0v1h-1l-1 2h-2l-1 1v-3h0v-1h-7l-10 1c1 0 0 1 1 1l1 1h-3v2c-1 1-1 1-1 2h-2l-1-4h-1c-1 0-2 1-3 1l-1-1v-1h-3v5h-1c-1-1-1-2-2-3v1l-1-1c-1 1-1 1-1 2l-1-1v-1c-1 0-1-1-1-2h-1c-1 2-1 4-2 6h-4v-4l-1-1h-1l-1-2v-1l-1-1c1-1 2-2 3-1l24-1c1-1 1-1 2 0h0c2 1 4 0 6 0l18-3z"></path><path d="M611 465v-3c1-1 2 0 4 0l-1 1v5h-1c-1-1-1-2-2-3z" class="B"></path><path d="M599 459l24-1-2 1c-2 1-5 0-7 1h-10c-2 1-3 0-5 0v-1z" class="D"></path><path d="M615 462h1l-2-1h2c4 0 8 0 11 1 1 0 0 1 1 1l1 1h-3v2c-1 1-1 1-1 2h-2l-1-4h-1c-1 0-2 1-3 1l-1-1v-1h-3l1-1z" class="E"></path><path d="M663 452l2 1h0c-1 1-1 0-2 1l-2 1h1 0c2 0 2-1 4 0 1 1 2 1 2 3l1 1h0l1-1v-1l1 1c3 1 6-1 8-2 3 0 5-1 7-2 1 1 1 3 0 4v1 3 1l-1 2c-3 5-10 11-15 13 1-1 2-3 4-3 1-1 1-1 1-2-2 1-3 1-5 1h-1l4-1v-1l-1 1c-1 0-4 0-5-1h2l-1-1c-4 0-8 0-12-1-1 0-2-1-3-1l-3-1-1-1 1-1-1-1c-3 0-4 0-6 2l-2-2h0-1-3v-4h7v1h0v3l1-1h2l1-2h1v-1h0v-1c0-1 0-2-1-3v-1h2l-1-1 14-3z" class="B"></path><path d="M664 466h0v-2c1 0 2-1 3-1l1 1-2 1c0 2 2 2 2 3-2 0-3-1-4-2z" class="C"></path><path d="M652 467c1 0 1-1 2-2h2c2 1 2 1 4 1h4c1 1 2 2 4 2v1c-1 0-4 0-5-1h-5c-2 0-4-1-6-1z" class="K"></path><path d="M668 464h1-1v-2h1c1 1 1 1 1 2l1 2h1c0 1 1 2 2 2v2h-2l-4-1v-1c0-1-2-1-2-3l2-1z" class="D"></path><path d="M663 452l2 1h0c-1 1-1 0-2 1l-2 1h1 0c2 0 2-1 4 0 1 1 2 1 2 3-3 2-6 2-10 3-1 0-2 0-3 1-2-1-2-1-3 0h-2-1v-1h0v-1c0-1 0-2-1-3v-1h2l-1-1 14-3zm11 16c-1 0-2-3-1-3 1-2 3-3 5-4h3c1 1 1 2 1 3s-1 4-2 5h-1c-1 1-2 1-2 2-1 1-2 1-4 2v-1l-1 1c-1 0-4 0-5-1h2l-1-1c-4 0-8 0-12-1-1 0-2-1-3-1l-3-1-1-1c1 0 2 0 3 1v-1c2 0 4 1 6 1h5c1 1 4 1 5 1l4 1h2v-2z" class="F"></path><path d="M476 811l2-1v5 9h0c1-2 1-4 1-6 0 2 0 3 1 5 0 1 0 3 1 4v-1c2 2 1 8 1 11h1l2 4 1 8c0 4 1 10-1 14v6c-1 0-1-1-2-1v1h-5l-1-1h-9c-10 0-20 1-29 0h-5 0c1-1 2-2 2-3l2-2 2-3c1-1 1-2 2-2l1-2c1-1 1-2 2-2l1-2c1-1 1-2 2-2 0-1 0-1 1-2s2-3 3-4l4-5 5-7v-1l4-5 2-3c1-1 1-2 2-3l2-1c1-3 4-5 5-8z" class="E"></path><path d="M469 831c4 2 6 9 7 13 1-2 1-4 1-7v-12c0-2-1-5 0-7l1 9 1 33h-1c-1-1 0-3-1-5-8-1-17 0-26-1l1-1c8 1 17 0 25 1-1-4-2-7-3-11s-3-8-5-12z" class="G"></path><defs><linearGradient id="Q" x1="484.282" y1="839.029" x2="473.964" y2="841.866" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#74706e"></stop></linearGradient></defs><path fill="url(#Q)" d="M478 824h0c1-2 1-4 1-6 0 2 0 3 1 5 0 1 0 3 1 4v-1c2 2 1 8 1 11h1l2 4 1 8c0 4 1 10-1 14v6c-1 0-1-1-2-1v1h-5l-1-1 1-3 2-1c-1 0-1 0-1-1v-3l-1-33v-3z"></path><path d="M482 837h1l2 4 1 8c-1-2-1-5-2-7s-1-3-2-5z" class="K"></path><path d="M452 853l-1-1c1-1 1 0 1-1l9-13c1-3 4-5 4-8h0c1-1 2-2 3-2h1l1 1h0-1v1 1c2 4 4 8 5 12s2 7 3 11c-8-1-17 0-25-1z" class="H"></path><path d="M347 154c-7-1-14-2-21-2-8 0-15 0-23-3 3-1 9-1 12-1h29 93l-2 1c1 1 2 1 3 1h0c3 2 5 2 7 3v1c1 1-1 3-1 5-1 4-1 8-2 11 0 1-1 1-1 1h-1c-1 1-2 1-3 3 1 2 1 2 0 4l-4 1c-1 0-1 0-1-1l-1-1h-1l1-1c1-1 2-2 2-3 1-1 1-1 1-2l-2-1-1 1h-3c-1-1-3-1-4-1l-8-3c-18-4-36-8-54-11-5-1-10-2-15-2z" class="R"></path><path d="M428 171c2-1 2-2 5-2l1 1 1-3c1-1 2 0 4 0-1 1-1 2-2 4l-2 3h0 1 1c1 2 1 2 0 4l-4 1c-1 0-1 0-1-1l-1-1h-1l1-1c1-1 2-2 2-3 1-1 1-1 1-2l-2-1-1 1h-3z" class="N"></path><path d="M445 154c1 1-1 3-1 5-1 4-1 8-2 11 0 1-1 1-1 1h-1c-1 1-2 1-3 3h-1-1 0l2-3c1-2 1-3 2-4 1-3 2-6 4-9 1-1 1-3 2-4z" class="b"></path><defs><linearGradient id="R" x1="654.753" y1="186.223" x2="656.509" y2="212.659" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#R)" d="M615 183h1 1v-1-1l1 3s0 1 1 2c3 1 7-1 10 0 1 1 4 1 5 1l23 1 5 1 22 5c9 4 19 7 28 12h-1-3c-1-1-1-1-2 0-2 1-6 2-8 2-1 0-2 1-4 2-2 0-4 1-6 1 0 1 0 1 1 1 1 1 0 1 1 1l-1 1c-1-1-2-1-3-1l-6-2-2-1c-1 0-2 0-2-1h-2-2l-4-1c-1 1-2 1-3 1s-2-1-3-1l-1 3h0l-1 1-5-1h-3-1c-1-1-2-1-3-1 0-1-1-2-1-3l-3-1-1-1h-16-4l-9 1c-2 0-4 0-6 1h-6c0-1 1-2 2-3s2-2 4-3l6-9c1-3 1-6 1-9z"></path><path d="M661 206c2 1 3 1 4 1l3 1c-1 1-2 1-3 1s-2-1-3-1c0 0-1-1-1-2z" class="D"></path><path d="M618 184s0 1 1 2c3 1 7-1 10 0 1 1 4 1 5 1-3 1-6 1-9 1h-7v-4z" class="W"></path><path d="M643 205c5 0 11 0 16 1h2c0 1 1 2 1 2l-1 3h0l-1 1-5-1h-3-1c-1-1-2-1-3-1 0-1-1-2-1-3l-3-1-1-1z" class="B"></path><path d="M652 211c0-1 0-1-1-2-1 0 0 0-1-1h2s1 0 2 1l1 1 1-2h3c1 1 1 1 2 3l-1 1-5-1h-3z" class="K"></path><path d="M449 585c2 1 3 2 4 3v-1h1c1 1-1 3 1 3 2 1 2 1 4 1 1 0 1 0 2 1 3 0 7 0 11 1h1 1 3v3 3l-1 2c-1 2-1 5-1 8l1 15-1 4h0l-2 2 1 2v1h-4c0 2-1 5 0 6l-1 2c0 1 0 3 1 4l-1 1 1 1c-1 0-1 1-1 2h-2v1 11 2h0v4c1 1 1 1 3 1l1-1-1 2 4-1 1 1v4h-3c-1 0-2-1-3-1l1 2h0-1-5v-1h4v-1h-5 0c-2 1-4 2-7 2-1 0-2 1-3 1l-5 1c-1 0-1 0-2-1l1-1c1 0 1 0 2-1h0c-1-2 0-5-1-7h-2 0-1v-1-2l-1-1h0c1-2 1-2 1-4l-2 1-1 2v-1l-1-1-2-1 1-1c1 0 2 0 3-1h1 5v-1l2-1v-15-27-14h1l2-1h-3l-1 1-1-1c-1-3 0-5 0-8 1-1 0-2 0-4z" class="f"></path><path d="M454 640l4-1v6h-1c-1-1-2-1-4 0v-1c0-2 1-3 1-4z" class="W"></path><path d="M451 656h2c0 1-1 3-1 5h0 1c2-1 2-3 4-3l1 3c-1 1-2 1-3 1l-2 2h-1c-1-1 0-1-1-1v-7z" class="J"></path><path d="M465 635c0 5-1 10 0 15v10c-1 2-1 3-1 5l-1 1-1-1v-3c1-2 2-9 1-10v-1c0-2 1-3 0-5 0-4 1-7 2-11z" class="V"></path><path d="M465 650l1-7h0c0 2 0 5 1 7v11 2h0v4c1 1 1 1 3 1l1-1-1 2c-2 0-6 0-8 1h0l-1-1 3-1h0v-3c0-2 0-3 1-5v-10z" class="D"></path><path d="M460 600v31c-1 4 0 11 0 15v23h-1v-43c0 1-1 1-1 2h-4v-1c-1-1 0-2 1-3s1-1 2-1h1c1-2 1-6 0-8v-1c1-1 1-2 1-3v-2c-1-2-1-4-1-5 1-2 1-2 1-3l1-1z" class="I"></path><path d="M449 655l2-1v2 7 9c-1 1-1 1-2 1-1-2 0-5-1-7h-2 0-1v-1-2l-1-1h0c1-2 1-2 1-4l-2 1-1 2v-1l-1-1-2-1 1-1c1 0 2 0 3-1h1 5v-1z" class="D"></path><path d="M445 658c1 0 2 0 4 1v1c-1 1-1 1-1 3l1 1-1 2h-2 0-1v-1-2l-1-1h0c1-2 1-2 1-4z" class="e"></path><path d="M449 585c2 1 3 2 4 3v-1h1c1 1-1 3 1 3 2 1 2 1 4 1 1 0 1 0 2 1 3 0 7 0 11 1h1 1 3v3l-1-2h-6-2v2h1 1 0l-3 3h0v26 4 20 1c-1-2-1-5-1-7h0l-1 7c-1-5 0-10 0-15-1-10 1-21-1-31h-1 0v2l-2 1c0 8 1 17 0 24h-1v-31-2h-3v-1c-2 1-3 1-5 1l2-1h-3l-1 1-1-1c-1-3 0-5 0-8 1-1 0-2 0-4z" class="S"></path><path d="M461 607v-7-1c0-1 0 0 1-1h3l-1 1 1 1c-1 1-1 2-1 4h-1 0v2l-2 1z" class="a"></path><g class="d"><path d="M449 585c2 1 3 2 4 3v-1h1c1 1-1 3 1 3 2 1 2 1 4 1 1 0 1 0 2 1-2 0-4 0-6 1h-1-3l-1 1 2 2h1l-1-1c1-1 1-1 2-1l1-1c1 1 1 2 2 2l1 1 1-1h-1l1-1 1 1v1c-1 1-2 1-4 1v-2c-1 1-1 2-2 2h-3l-1 1-1-1c-1-3 0-5 0-8 1-1 0-2 0-4z"></path><path d="M452 598c2 0 3 0 5-1v1h3v2l-1 1c0 1 0 1-1 3 0 1 0 3 1 5v2c0 1 0 2-1 3v1c1 2 1 6 0 8h-1c-1 0-1 0-2 1s-2 2-1 3v1c0 2-1 6 0 8v-2c1-1 1-2 1-3h2v4h0l-3 3v2c0 1-1 2-1 4v1c0 1 0 2 1 3 2 0 2-1 4-1v4 4c-2 1-2 0-3 0s-1 1-2 1h-2v-2-15-27-14h1z"></path></g><path d="M454 616l-2-1c1-2 1-3 2-4l3 1c1 0 1 1 1 2v1c-1 0-3 1-4 1z" class="h"></path><path d="M458 615c1 2 1 6 0 8h-1c-1 0-1 0-2 1-1-2 0-6-1-8 1 0 3-1 4-1z" class="V"></path><path d="M467 625v-26h0l3-3h0-1-1v-2h2 6l1 2v3l-1 2c-1 2-1 5-1 8l1 15-1 4h0l-2 2 1 2v1h-4c0 2-1 5 0 6l-1 2c0 1 0 3 1 4l-1 1 1 1c-1 0-1 1-1 2h-2v-20-4z" class="g"></path><path d="M473 607h1v10l-1 1-1-2h-1c0-1 0-2-1-2v-1l1-3c0-1 1 0 2-1v-2z" class="e"></path><path d="M467 629h0c1-1 1-2 1-3h1 0c2-1 2-1 4-1l1 1c-1 2-2 3-4 4-1 1-1 2 0 3 0 2-1 5 0 6l-1 2c0 1 0 3 1 4l-1 1 1 1c-1 0-1 1-1 2h-2v-20zm0-4v-26h0l3-3h0-1-1v-2h2 6l1 2v3l-1-1-1 1h0-2v1c0 2-1 4-1 6l-1 1-1-1 1-1v-3l-1-1-1 1v1 4 1c1 1 0 2 0 4v7c2 0 3-1 4 1h1l-1 1h-1-2 0l-1 1c-1 1-1 2-2 3z" class="a"></path><path d="M571 666v-7c4-1 8 0 12 0 0 3-1 9 1 12 1-1 2-1 4 0l-1 1v21c0 3 1 7 0 10 0 6-1 14 0 20l2 2-1 1-3 3-9-5h-1l-2-1c-1 1-1 2-2 3 0 0-1 1-1 2v-1h-1c-1-1-1-2-2-2l-3-1c3-3 1-6 2-10v-1h-1c-2-2-4-5-6-6l-1-1v-1-2l1-1 1 1c0-2 0-4 1-6h1c1-1 2-2 3-4 0-1 0-1 1-1v-2c-1 0-3 0-5 1s-5 1-7 1c-2 1-3 0-4 0l-2 1v-1c-2-2-4-5-4-8v-3-11l1-1c4 0 8 1 12 1 1 0 4 1 5 0l2 1v-1h0v-1c1 1 1 1 2 1h1 2l-1-5h1c1 0 2 0 2 1z" class="B"></path><path d="M573 698h5l1 1c-1 1-2 3-2 5l-4-2v-4z" class="F"></path><path d="M579 716c2-2 1-13 1-16 1-2 1-2 1-3s1-2 0-3v-4l1-1v-4 9 5l2-1 1 2v1h-2v-1h-1c0 6-1 13 0 18-1-1-1-1-3-2h0z" class="E"></path><path d="M582 685c1-3 0-10 2-13h3v21c0 3 1 7 0 10l-2-2v-1l-1-2-2 1v-5-9zm-11-19v-7c4-1 8 0 12 0 0 3-1 9 1 12h-2c0 3 1 10-1 12-1 0-2 0-3-1v-1c-1-3 2-10 0-11v3h-2c2 3 0 6 1 9l-4 1c-1-2 0-5 0-8v-2-1c-2-1-2-4-2-6z" class="J"></path><path d="M567 689c2 0 2 0 4 1 1 4 0 9 0 14h2v-2l4 2h1 0c-1 1-1 2-1 3s1 3 1 5v2l1 2h0c2 1 2 1 3 2-1-5 0-12 0-18h1v1h2l2 2c0 6-1 14 0 20l2 2-1 1-3 3-9-5h-1l-2-1c-1 1-1 2-2 3 0 0-1 1-1 2v-1h-1c-1-1-1-2-2-2l-3-1c3-3 1-6 2-10v-1h-1c-2-2-4-5-6-6l-1-1v-1-2l1-1 1 1c0-2 0-4 1-6h1c1-1 2-2 3-4 0-1 0-1 1-1v-2l1-1h0z" class="I"></path><path d="M571 716l2-2v-1c1 0 1 1 2 2s3 1 4 1c2 1 2 1 3 2v2c-3 0-6-3-10-3h-1v-1z" class="H"></path><path d="M569 725l2-1v-6c1 0 1 1 2 2 0 0 2 1 3 1 3 2 6 4 9 4 2 1 0 0 2 0l1 1-3 3-9-5h-1l-2-1c-1 1-1 2-2 3 0 0-1 1-1 2v-1c0-1 0-1-1-2z" class="U"></path><path d="M573 702l4 2h1 0c-1 1-1 2-1 3s1 3 1 5v2l1 2h0c-1 0-3 0-4-1s-1-2-2-2v1l-2 2v-12h2v-2z" class="K"></path><path d="M574 710c-1-1-1-1-1-3 1-1 2-1 3-1v1 2 1h-2z" class="F"></path><path d="M573 702l4 2h1 0c-1 1-1 2-1 3l-1-1c-1-1-2-1-3-2v-2z" class="C"></path><path d="M567 689h0v18c0 5-1 14 2 18h0c1 1 1 1 1 2h-1c-1-1-1-2-2-2l-3-1c3-3 1-6 2-10v-1h-1c-2-2-4-5-6-6l-1-1v-1-2l1-1 1 1c0-2 0-4 1-6h1c1-1 2-2 3-4 0-1 0-1 1-1v-2l1-1z" class="F"></path><path d="M562 697c2 0 2 0 3 1 0 1-1 0-1 2h0c1 0 1 0 2 1v1h-2c-1 0-2 0-3 1h-1c0-2 0-4 1-6h1z" class="T"></path><path d="M568 665h1c1 0 2 0 2 1 0 2 0 5 2 6v1 2c0 3-1 6 0 8v1c0 4-2 11 0 14v4 2h-2c0-5 1-10 0-14-2-1-2-1-4-1h0l-1 1c-1 0-3 0-5 1s-5 1-7 1c-2 1-3 0-4 0l-2 1v-1c-2-2-4-5-4-8v-3-11l1-1c4 0 8 1 12 1 1 0 4 1 5 0l2 1v-1h0v-1c1 1 1 1 2 1h1 2l-1-5z" class="Y"></path><path d="M568 665h1c1 0 2 0 2 1 0 2 0 5 2 6v1c-1 0-1 0-2 1h-1c-1-1-1-2-1-4l-1-5z" class="D"></path><path d="M567 687c-1 0-1 0-1 1l-2-1 1-1v-4c-1-1-1-1-1-2v-1l-2-2c-2-1-4-2-4-4 1 1 1 1 2 1h2c1 1 1 1 2 1h1 0l2 2v6 1 3z" class="c"></path><path d="M552 683l-1-1c0-1 0-2 1-2 1-2 2-2 4-3 2 1 2 2 4 3l-1 2c0 1-1 2-2 3s-3 1-4 0c0 0-1-1-1-2z" class="D"></path><path d="M552 683c0-1 1-1 2-2 0 1 0 1 1 1s1-1 3-1h0 1v1c0 1-1 2-2 3s-3 1-4 0c0 0-1-1-1-2z" class="E"></path><path d="M565 675c1-1 2-1 3-1l5 1c0 3-1 6 0 8v1c0 4-2 11 0 14v4 2h-2c0-5 1-10 0-14-2-1-2-1-4-1h0v-2-3-1-6l-2-2h0z" class="I"></path><path d="M567 677c2 1 3 1 3 3s-2 2-3 3v-6z" class="h"></path><path d="M567 684l2 2h1v-1h1v2 3c-2-1-2-1-4-1h0v-2-3z" class="R"></path><path d="M565 675c1-1 2-1 3-1l5 1c0 3-1 6 0 8v1c0 4-2 11 0 14v4 2h-2c0-5 1-10 0-14v-3l1-11-7-1h0z" class="D"></path><path d="M544 670c2 1 4 1 5 2 1 0 2 1 4 1l-1 1c-1 0-2 0-3-1 0 1 0 2 1 2h1 5v1c-2 0-3 0-5 1-1 2-1 4-1 6 1 2 2 3 4 4 1 1 1 1 3 1 2-1 4-3 5-5v-1l1 1c0 1 0 2-1 3s-1 1-2 1l-1 1c-1 1-3 1-5 1v1l1 1h-2l1 1c-2 1-3 0-4 0l-2 1v-1c-2-2-4-5-4-8v-3-11z" class="K"></path><path d="M627 498c10 0 18 0 28-2 2 1 3 2 5 3 3 3 6 6 9 7l1 1 2 2c1 1 2 2 3 2 3 3 7 6 10 9h0c-1 2-1 3-2 5v2 2l-1 1c0 1 1 2 0 4h0c0 1 0 1-1 2v-2h-1v1c0 2 0 3 1 5 1 7-1 15 1 22v1s1 1 1 2h1l-1 1v1l-3-7-5-6-5-6c-5-4-10-8-15-11-1-3-5-4-8-6 0-1-3-1-4-2-3-2-6-3-8-6 0 0-2-1-3-2h0 2l1 1v-1c0-1-1-1-2-2h1v-1-1l-1 1-4-3c-1-1-2-2-3-2l-1-2c-1-2-2-3-4-4l-8-8c1-1 3-1 4-1 2 1 4 1 6 1l1-1-3-1h1l5 1z" class="V"></path><path d="M674 549c4 3 6 6 6 11l-5-6v-2l-1-3z" class="U"></path><path d="M668 543c2 1 4 3 6 6l1 3v2l-5-6c0-1 0-2-1-3-1 0-1-2-1-2z" class="F"></path><path d="M635 523c3 2 7 3 10 4 1 0 2 1 2 1 4 1 7 3 9 5s3 3 5 4c-5-2-9-5-14-6 0-1-3-1-4-2-3-2-6-3-8-6z" class="P"></path><path d="M647 531c5 1 9 4 14 6l7 6s0 2 1 2c1 1 1 2 1 3-5-4-10-8-15-11-1-3-5-4-8-6z" class="S"></path><defs><linearGradient id="S" x1="668.372" y1="504.738" x2="652.139" y2="520.83" xlink:href="#B"><stop offset="0" stop-color="#615d5a"></stop><stop offset="1" stop-color="#7c7b75"></stop></linearGradient></defs><path fill="url(#S)" d="M627 498c10 0 18 0 28-2 2 1 3 2 5 3 3 3 6 6 9 7l1 1 2 2c1 1 2 2 3 2 3 3 7 6 10 9h0c-1 2-1 3-2 5v2 2l-1 1c0 1 1 2 0 4h0c0 1 0 1-1 2v-2h-1c-2-4-7-7-11-10h-1l-1-1-2-2-10-6c-9-6-19-11-28-17z"></path><path d="M715 423c9-8 17-16 24-25l-3 14c-3 8-5 16-8 23-10 22-26 40-47 51-8 5-17 8-26 10-10 2-18 2-28 2l-5-1h-1l3 1-1 1c-2 0-4 0-6-1-1 0-3 0-4 1-3-3-7-5-11-8h2c1-1 2-2 4-2v-1c1 0 2 2 2 2h5l8 1 3-1h4 5l8-2c9-2 19-4 27-10 5-2 12-8 15-13l3-3 6-6c5-1 8-7 12-9 2-2 4-3 5-5 0-1-1-1-1-2 1-3 3-6 5-9h0c1-2 2-4 2-7-1-1-1-1-2-1z" class="R"></path><path d="M608 488c1 0 2 2 2 2h5l8 1c-4 0-10 0-13 1-1 1-5 0-6-1 1-1 2-2 4-2v-1z" class="N"></path><path d="M602 491h2c1 1 5 2 6 1l6 3 6 2h-1l3 1-1 1c-2 0-4 0-6-1-1 0-3 0-4 1-3-3-7-5-11-8z" class="a"></path><path d="M621 497c0 1-1 1-2 1-1-1-3-1-5-1-1 0-2-1-3-2 1-1 2 0 4 0v1l1-1 6 2h-1z" class="O"></path><path d="M571 456h14c4 0 8 1 11 4l1 1v1l1 2h1l1 1c-2 1-2 2-2 4s0 4 1 5c2 2 7 1 10 1h27 11c8 0 15 0 22-1h1c2 0 3 0 5-1 0 1 0 1-1 2-2 0-3 2-4 3-8 6-18 8-27 10l-8 2h-5-4l-3 1-8-1h-5s-1-2-2-2v1c-2 0-3 1-4 2h-2l-3-2 1-1c-1 0-2 0-4 1-3 1-7 0-10 1-2 0-5 0-8 1-1 0-5 2-6 1-1 1-2 1-3 3-1 3-1 7-1 11-1 2 0 3 1 5h0c1 0 2 1 3 2 1 2 1 3 1 5 2 4 3 7 8 10h-2v1c-1-1-2-1-2-1-2-1-4-7-4-9-1-1 0-1-1-2l-1-4h-1v2h1v2h-1c-1 0-1-1-1-2-1-1-2-1-3-1v-11-19-9h2c1-1 1-2 1-3h0c1-2 1-6 0-9v-3c0-1 0-3 1-4h1z" class="G"></path><path d="M600 488c3 0 5 0 8 1-2 0-3 1-4 2h-2l-3-2 1-1z" class="e"></path><path d="M635 487v1c2 0 2-1 3-2 0-1 0-4 1-5 1 2 2 2 2 4s1 2 2 3l-8 2c0-2-1-2 0-3z" class="E"></path><path d="M630 490c1-2 0-5 1-7 1-1 2-1 3-1 1 2 1 3 1 5-1 1 0 1 0 3h-5z" class="K"></path><path d="M608 488v-7l1-1h3c1 2 0 4 0 6v3c1 0 1 0 2-1 0 1 0 1 1 2h0-5s-1-2-2-2z" class="H"></path><path d="M615 490c0-3 0-7 1-10 1 1 1 3 1 5v3l2 2c1 0 1 0 1-1 2-3 0-6 2-9 1 1 1 2 1 3 0 2 0 4 1 7h2l-3 1-8-1h0z" class="P"></path><path d="M571 456h14c4 0 8 1 11 4l1 1v1l1 2h1l1 1c-2 1-2 2-2 4s0 4 1 5h-3l-1 1h-2-20-5c1-1 1-2 1-3h0c1-2 1-6 0-9v-3c0-1 0-3 1-4h1z" class="I"></path><path d="M582 471l11 1 1 2v1h-11l-1-4z" class="R"></path><path d="M573 475c-1 0-1-1 0-2v-1-1c1 0 1-1 2-1 0 0 0 1 1 1 1 1 5 0 6 0l1 4h11-1-20z" class="W"></path><path d="M597 461v1l1 2h1l1 1c-2 1-2 2-2 4s0 4 1 5h-3c-1-2-1-4-1-6h-1l-1 1h0l-2-3c1-1 0-3-1-4l1-1h6z" class="C"></path><path d="M597 462l1 2c-1 1-2 1-3 1l-2-2c2-1 3-1 4-1z" class="G"></path><path d="M597 461v1c-1 0-2 0-4 1 0 1 0 2-1 2l-1 1c1-1 0-3-1-4l1-1h6z" class="E"></path><path d="M571 456h14c4 0 8 1 11 4l1 1h-6l-1 1h-3v6c-1 0-2 1-3 0 1-1 1-1 0-2v2l-2 1c-1-2 0-6-1-9-1 3-1 5-2 8l-1 1c-1-2-1-6-1-9-1 3-1 6-1 8-1 1-1 1-3 1h-1c-1 2-1 2-3 3h0c1-2 1-6 0-9v-3c0-1 0-3 1-4h1z" class="K"></path><path d="M569 463c2-1 3-1 4-1l1 1h-1c0 2 0 3 1 5l-1 1h-1c-1 2-1 2-3 3h0c1-2 1-6 0-9z" class="U"></path><path d="M571 456h14c4 0 8 1 11 4l1 1h-6c-7-1-14-1-22-1 0-1 0-3 1-4h1z" class="R"></path><path d="M570 592c1 1 1 1 1 2s0 2-1 3c0 1-1 2-2 2 0 4 1 8 0 11 1 4 0 8 0 12v19 6 7 1c1 3 0 7 1 9v1h-1l1 5h-2-1c-1 0-1 0-2-1v1h0v1l-2-1c-1 1-4 0-5 0-4 0-8-1-12-1l-1 1v11 3c0 3 2 6 4 8v1h0c-1-1-2-1-3-2v1l-2 1v2h-1l-1-11c1-2 0-4 0-6 0-5 0-11-1-15 0-1-1-1-2-2h-1v-1s1-1 2-1l1 1h1c0-1 1-3 1-4 0-2-1-9 0-11 2-3 0-9 1-13 1-2 1-5 1-8h0c-1-4 0-8 0-12v-3-8l-3-4c1-1 1-1 1-2h1v1l3 1h1l-1-2 1-1 1-1c4 0 7-1 11 0h2 8l1-1z" class="g"></path><path d="M567 670l-1-1c-1-1-1-2-1-4h3l1 5h-2z" class="J"></path><path d="M565 640c0-2 0-2-1-3h-1c-1-2 0-5 0-7l1-1 1 1h1v5l-1 1v4z" class="d"></path><path d="M557 611c1 2 1 4 1 7l1 20c0 2-1 3-1 5 0 4 1 8 0 11l-1-4c-1-5 0-10 0-15v-14-10z" class="N"></path><path d="M564 645c2-1 2-3 3-4v-2c1 0 1 1 1 2v6 7 1h-3-2v-8c1 0 2-1 3-1l-1-1h-1z" class="R"></path><path d="M553 621l3-1 1 1v14l-1 32c1 1 2 2 3 2 1-1 1 0 2-1h0c1 1 2 1 3 2h0v1l-2-1c-1 1-4 0-5 0-4 0-8-1-12-1 0-1 1-1 1-2 2 1 3 1 5 1h2v-2c-1-3 0-10 1-13v-6c0-1-1-1-1-2 0-2 1-6 1-9l-1-11v-4z" class="S"></path><path d="M553 666c-1-3 0-10 1-13 1 3 1 5 1 8h0c-1 1 0 1-1 2 0 1-1 2-1 3z" class="P"></path><path d="M568 610c1 4 0 8 0 12v19c0-1 0-2-1-2v2c-1 1-1 3-3 4l-2-2c0-1 0-3 1-4l1 1h1v-4l1-1v-5-2h-2c-1-1-2-1-3 0v12c0 3 1 17-1 20v-45l1-1c1 1 3 1 5 1h2v-5z" class="W"></path><path d="M551 620l1 1v4h1l1 11c0 3-1 7-1 9 0 1 1 1 1 2v6c-1 3-2 10-1 13v2h-2c-2 0-3 0-5-1 0 1-1 1-1 2l-1 1v11 3c0 3 2 6 4 8v1h0c-1-1-2-1-3-2v1l-2 1v2h-1l-1-11c1-2 0-4 0-6 0-5 0-11-1-15 0-1-1-1-2-2h-1v-1s1-1 2-1l1 1h1c0-1 1-3 1-4 0-2-1-9 0-11 2-3 0-9 1-13 1-2 1-5 1-8h0v-1l2-2c1 1 3 1 5 2v-3z" class="f"></path><path d="M549 648l1 1v6h-3l-1-1v-4c1-1 2-1 3-2z" class="g"></path><path d="M544 624v-1l2-2c1 1 3 1 5 2l-1 3v7s-1 0-1 1c0-3 1-7-1-9h0c-2-1-3-1-4-1h0z" class="Y"></path><path d="M550 633c1 5 0 10 0 16l-1-1-1-1h-4v-3l1-1 1-1h2c-1-1-1-2-3-2h0v-1h1c-1-2 0-2-1-3v-1c0-1 0-1-1-2 2 0 3 1 5 1h0c0-1 1-1 1-1z" class="O"></path><path d="M539 659l1 1h1c0-1 1-3 1-4 0-2-1-9 0-11 2-3 0-9 1-13l1 18v7l-1 1v5 3c1 1 2 1 3 1 0 1-1 1-1 2l-1 1v11 3c0 3 2 6 4 8v1h0c-1-1-2-1-3-2v1l-2 1v2h-1l-1-11c1-2 0-4 0-6 0-5 0-11-1-15 0-1-1-1-2-2h-1v-1s1-1 2-1z" class="G"></path><path d="M551 620l1 1v4h1l1 11c0 3-1 7-1 9 0 1 1 1 1 2v6c-1 3-2 10-1 13v2h-2c-1-1-1-2-2-3 1-1 1-1 1-2l-1-1c-1 0-1 0-2-1h-1l1-1h1v-1-2h0c0-1 0-1-1-2h3v-6c0-6 1-11 0-16v-7l1-3v-3z" class="L"></path><path d="M570 592c1 1 1 1 1 2s0 2-1 3c0 1-1 2-2 2 0 4 1 8 0 11v5h-2c-2 0-4 0-5-1l-1 1c0-2 0-2-1-3-1 1-1 4-1 6 0-3 0-5-1-7v10l-1-1-3 1v4h-1v-4l-1-1v3c-2-1-4-1-5-2l-2 2v1c-1-4 0-8 0-12v-3-8l-3-4c1-1 1-1 1-2h1v1l3 1h1l-1-2 1-1 1-1c4 0 7-1 11 0h2 8l1-1z" class="Z"></path><path d="M544 609c1 1 2 2 3 2v1c0 1-1 1-2 2h1c0 1-1 1-1 2 1 0 2 0 2 1v2c-1-1-1-1-3-2v-5-3z" class="Y"></path><path d="M544 601c1-1 1-1 3-1 0 4 1 8 0 11-1 0-2-1-3-2v-8z" class="N"></path><path d="M544 612v5c2 1 2 1 3 2l1 1h0l1-1h0c0-1 0-2 1-3h0c0 1 0 3 1 4h0v3c-2-1-4-1-5-2l-2 2v1c-1-4 0-8 0-12z" class="O"></path><path d="M551 595h2l1 1-1 1h1l1-1c1-1 1-2 3-2 0 0 1 0 2 1-1 1-1 1-2 1 0 1 0 1 1 1l-2 2c-1 0 0 11 0 12v10l-1-1-3 1v4h-1v-4l-1-1h0v-18-3h-2v-1c1 0 1 0 2-1h-2v-1h2v-1z" class="K"></path><path d="M551 599h2c1 2 0 5 0 7v15 4h-1v-4l-1-1h0v-18-3z" class="f"></path><path d="M570 592c1 1 1 1 1 2s0 2-1 3c0 1-1 2-2 2 0 4 1 8 0 11v5h-2c-2 0-4 0-5-1l-1 1c0-2 0-2-1-3-1 1-1 4-1 6 0-3 0-5-1-7 0-1-1-12 0-12l2-2c-1 0-1 0-1-1 1 0 1 0 2-1-1-1-2-1-2-1-2 0-2 1-3 2l-1 1h-1l1-1-1-1h-2l-4-1 1-1c4 0 7-1 11 0h2 8l1-1z" class="Y"></path><path d="M548 593c4 0 7-1 11 0h2 8c-1 1-3 1-4 1h-2l-1 1h1 1l1 2v1l-1-1v-1c-1 1-3 0-3 1h-1l1-2c-1-1 0 0-1 0-1-1-2-1-2-1-2 0-2 1-3 2l-1 1h-1l1-1-1-1h-2l-4-1 1-1z" class="C"></path><path d="M561 614c0-2 1-2 2-3v-8l1-1c-1-1-1-1-1-2l5-1c0 4 1 8 0 11v5h-2c-2 0-4 0-5-1zM509 63c0-1 0-3 1-4 2 4 2 9 3 13 1 7 1 15 3 22 2 8 6 16 9 24 3 10 6 21 8 31l1 3-2-2c0-2 0-2-1-3v-1c-1-1-1-2-1-3-2 1-3 1-4 2h-2l-4 2c-1 1-1 1-2 1s-2 1-4 2c-1 0-2 1-3 2 0 2 1 7-1 9v9c-1-2 0-3-2-4-1 0-2 0-3 1-1-1-2-2-4-2h-1v-1h-4v-1c-2 0-2-1-3 0-2 0-4-1-5-1-2 1-2 1-4 0 1-1 0-2 0-3h0c0-4 1-8 2-12l9-31c3-7 7-14 8-21 1-4 2-7 2-11l4-21z" class="R"></path><defs><linearGradient id="T" x1="526.146" y1="144.243" x2="495.963" y2="74.509" xlink:href="#B"><stop offset="0" stop-color="#69655f"></stop><stop offset="1" stop-color="#cabfb1"></stop></linearGradient></defs><path fill="url(#T)" d="M509 63c0-1 0-3 1-4 2 4 2 9 3 13 1 7 1 15 3 22 2 8 6 16 9 24 3 10 6 21 8 31l1 3-2-2c0-2 0-2-1-3v-1c-1-1-1-2-1-3-2 1-3 1-4 2h-2l-4 2c-1 1-1 1-2 1s-2 1-4 2c-1 0-2 1-3 2 0 2 1 7-1 9V64l-1-1z"></path><path d="M462 726c2 1 4 1 6 1h1c4-1 6-4 8-7v-1 1l1 6c0 2 0 2-1 4h0l1 1-2 1-2 2h-1l1 1h1c-2 1-3 0-4 0h-2c1 1 1 2 2 3h3c0 1 1 1 2 2 0 0 1 1 1 2 0 0-1 1 0 3h-1l2 1v15 4 4h-1 0-3l-1 1-1 1h0l-1 2v1h1 1c0 1 1 1 1 2s-1 1-1 2v1 1h-1-1c0 1 0 1-1 2v-1-1c-1 1-2 1-3 1 0 0-1-1-2-1h-1-1c-2-1-3-1-5-1 0-2-1-3-2-5-1 1-1 0-2 1-3 4-9 9-13 11v1h-1c-1-2-2-4-4-7l1-1c-1-1-2-3-2-4l-1-5-1 1v-3l-1-5c0-3 1-4 2-6h0l-1-1c1 0 2-1 3-1h0c0-1 0-1-1-1l-1-2 1-1 1-1h-4l-1-1v-1c-1-1 0-4 0-5 1-3 2-8 1-10 0-1 1-2 2-3-1-2-1-3-2-4h1c2-1 3-3 5-3h3 0c-1 1-2 1-4 2l-3 3v1c4-3 8-6 13-9v2l1 5 2-1 2-2v-4h1v2h2c2 3 4 2 7 4z" class="K"></path><path d="M453 749h1c1-1 2-2 2-4h1v1 3l1 1v1h-7l-1-1 3-1z" class="a"></path><path d="M454 738c4 0 8 0 11 2h3l-1 1h-4v1c-1 0-1 0-2 1h1c0 1 1 1 1 1-1 2-2 3-4 4v-3l1-1-1-1c0-1 0-1-1-2v-1c-1 0-2-1-4-2z" class="B"></path><path d="M457 749c2 0 4 0 5 2h0l-1 1h0l3-1v1l-1 1c1 1 1 1 1 2l-5 2c-2 2-4 1-7 1l-1 1h-1 0l-1-1c-2 0-2-1-2-2 1-1 1-1 1-2s0-1 1-1l-2-2h0 4 7v-1l-1-1z" class="P"></path><path d="M454 756l1-2h1 1 0 3v1c-1 1-1 1-1 2-2 2-4 1-7 1v-3c1 0 1 0 1 1h1z" class="T"></path><path d="M449 753h2l1-1v1l2 1c0 1-1 1 0 2h-1c0-1 0-1-1-1v3l-1 1h-1 0l-1-1c-2 0-2-1-2-2 1-1 1-1 1-2s0-1 1-1z" class="O"></path><path d="M463 742v-1h4l1-1c3 2 6 3 8 5l2 1v15c-1 0 0 0-1-1v-4c-1 0-1-1-2-1-1-2-4-3-6-3-1 0 0 1-1 0 1-1 0-1 1-2v-1l-1 1-1-1c-1-1 0-2 1-3v-1h-1l-1 1-1 1-1-1 2-1-3-3z"></path><path d="M464 755h2v1h-1v1c1 0 2 0 3 1l2-1c1 1 0 1 0 2s2 2 2 2l4 4-1 1c0-1-1-1-1-2-2-1-2-1-4-1h-2v1c1 0 0 0 1 1 1 0 2 1 3 2h-1 0l-2-1c-1 0-1 1-2 2h1l-2 1s-1 1-1 2h1 0c2 0 3 0 4 1v-1l1-1h1v1l-1 2v1h1 1c0 1 1 1 1 2s-1 1-1 2v1 1h-1-1c0 1 0 1-1 2v-1-1c-1 1-2 1-3 1 0 0-1-1-2-1h-1-1c-2-1-3-1-5-1 0-2-1-3-2-5h1l-2-2c2 0 1 0 3-1l-1-1-1-1h-3v-1l-1 1v2l-1-1h-1l-1-3-1-2 1-1 1 1v-1c2 0 1 1 2 1h2c0-1 1-2 0-4h-1v-2h-2l1-1c3 0 5 1 7-1l5-2z" class="Q"></path><path d="M464 755h2v1h-1v1c1 0 2 0 3 1l1 1-1 1v-1l-1-1v2l-1-1c-1 1-2 2-3 2 0-1-1-1-1-2v-1l-1 1-2-2 5-2z" class="B"></path><path d="M458 771h0c1 0 1-1 1-1l1-1c1 1 0 1 1 3 0 1 0 1 1 2v1l2 2h0l2 2-1 1h-1-1c-2-1-3-1-5-1 0-2-1-3-2-5h1l-2-2c2 0 1 0 3-1z" class="L"></path><path d="M442 747c1 2 3 2 5 3 1 1 2 0 3 0l1 1h-4 0l2 2c-1 0-1 0-1 1s0 1-1 2c0 1 0 2 2 2l1 1h0 1 2v2h1c1 2 0 3 0 4h-2c-1 0 0-1-2-1v1l-1-1-1 1 1 2 1 3h1l1 1v-2l1-1v1h3l1 1 1 1c-2 1-1 1-3 1l2 2h-1c-1 1-1 0-2 1-3 4-9 9-13 11v1h-1c-1-2-2-4-4-7l1-1c-1-1-2-3-2-4l-1-5-1 1v-3l-1-5c0-3 1-4 2-6h0l-1-1c1 0 2-1 3-1h0c0-1 0-1-1-1l-1-2 1-1 1-1c2 0 3 0 4-2l2-1z" class="S"></path><path d="M434 770v-1h1 1v1 5l1 1 2-1v4h2v1c1 0 1-1 2 0h2c-1 1-2 2-4 3-1 1-1 1 0 2v1 1h-1c-1-2-2-4-4-7l1-1c-1-1-2-3-2-4l-1-5z" class="C"></path><path d="M445 763l2 3 1-1 1 2 1 3c0 1 0 3 1 4v1c-2 0-3 0-5-1h0c-1 0-1 0-2 1v-2h-1c1-1 1-1 1-2v-2c1-1 0-1 0-2h1c0-1-1-2-1-2l1-2z" class="D"></path><path d="M449 767l1 3c0 1 0 3 1 4v1c-2 0-3 0-5-1 1-2 2-3 1-5 0-1 1-2 2-2z" class="C"></path><path d="M450 770h1l1 1v-2l1-1v1h3l1 1 1 1c-2 1-1 1-3 1l2 2h-1c-1 1-1 0-2 1-3 4-9 9-13 11v-1c-1-1-1-1 0-2 2-1 3-2 4-3 2-1 4-3 6-5v-1c-1-1-1-3-1-4z" class="T"></path><path d="M442 747c1 2 3 2 5 3 1 1 2 0 3 0l1 1h-4 0l2 2c-1 0-1 0-1 1s0 1-1 2c0 1 0 2 2 2l1 1h0 1 2v2h1c1 2 0 3 0 4h-2c-1 0 0-1-2-1v1l-1-1-1 1-1 1-2-3c-1-1-1-2-2-2-1-1-2-1-3-2l-1-1c-2 1-4 3-5 4l2 2v1l-3 3-1-5c0-3 1-4 2-6h0l-1-1c1 0 2-1 3-1h0c0-1 0-1-1-1l-1-2 1-1 1-1c2 0 3 0 4-2l2-1z" class="C"></path><path d="M442 747c1 2 3 2 5 3 1 1 2 0 3 0l1 1h-4 0l2 2c-1 0-1 0-1 1s0 1-1 2c0 1 0 2 2 2l1 1h0 0-2v1h-2 0 0l1-1-1-2h-2c-1 0-1-1-2-2h-3v-1c0-1 0-1-1-3-1 1-1 0-2 1l1 3v1h0l-1-1h0c0-1 0-1-1-1l-1-2 1-1 1-1c2 0 3 0 4-2l2-1z" class="Q"></path><path d="M462 726c2 1 4 1 6 1h1c4-1 6-4 8-7v-1 1l1 6c0 2 0 2-1 4h0l1 1-2 1-2 2h-1l1 1h1c-2 1-3 0-4 0h-2c1 1 1 2 2 3h3c0 1 1 1 2 2 0 0 1 1 1 2 0 0-1 1 0 3h-1c-2-2-5-3-8-5h-3c-3-2-7-2-11-2h-2l-1 1h3c1 0 2 1 2 2 1 1 1 1 1 2-1-1-2-2-3-2v1h-1c0-1-1-1-2-2 0 1-1 1-2 2 0 1 0 2 1 4h1l1-1c1 0 1 0 2 1l-2 2-1-1h-1c-1 0-1-1-2-1v1s0 1 1 1c1 1 2 1 4 1l-3 1c-1 0-2 1-3 0-2-1-4-1-5-3l-2 1c-1 2-2 2-4 2h-4l-1-1v-1c-1-1 0-4 0-5 1-3 2-8 1-10 0-1 1-2 2-3-1-2-1-3-2-4h1c2-1 3-3 5-3h3 0c-1 1-2 1-4 2l-3 3v1c4-3 8-6 13-9v2l1 5 2-1 2-2v-4h1v2h2c2 3 4 2 7 4z" class="O"></path><path d="M474 738c-1 1 0 1-1 2l-2-1h-1c-2-1-3-2-5-3v-1c2 1 3 0 4 0 1 1 1 2 2 3h3z" class="J"></path><path d="M462 726c2 1 4 1 6 1h1c4-1 6-4 8-7v-1 1l1 6c0 2 0 2-1 4h0l1 1-2 1-2 2c-2-2-3-1-5-4-1-1-1-2-2-3l-1 1c1 2 0 5-1 6 0 1-3 0-3 0l-1 1h1c0 1-1 1-1 1-1-1-2-1-3-1l-1-1c-2-1 0 0-1-1l-1-1-1 1v1l-1 1h-1c0-1 1-4 0-4-1-1-1 0-3-2l-1-1v-1l2-1 2-2v-4h1v2h2c2 3 4 2 7 4z" class="C"></path><path d="M449 729c1-1 1-2 2-2s1 1 3 2 6 0 8-1h2v3c-1 0-1 0-2 1h0c-2-1-5-1-7-1-1 1-1 0-1 2v1l-1 1h-1c0-1 1-4 0-4-1-1-1 0-3-2z" class="G"></path><path d="M452 720h1v2h2c2 3 4 2 7 4l-1 1 1 1c-2 1-6 2-8 1s-2-2-3-2-1 1-2 2l-1-1v-1l2-1 2-2v-4z" class="M"></path><path d="M441 723h0c-1 1-2 1-4 2l-3 3v1c4-3 8-6 13-9v2 4c-1 0-1-1-2-1v-1l-1 1s0 1 1 1c1 1 1 2 2 3l-1 1-1-1c-1 1-1 1-1 2-1 3-3 5-4 8h1 0c0-1 0-1 1-2 2-1 2-2 2-4h1 3v1l-1 1-1-1c-1 1-1 1-1 2s-2 2-2 3h1 0c2-1 5-1 6-1-2 2-3 3-6 4v1h-3c-1 0-2 0-2 1l-4 1h-1c3-1 5-1 7 0 1 0 2 0 3 1h-2v1l-2 1c-1 2-2 2-4 2h-4l-1-1v-1c-1-1 0-4 0-5 1-3 2-8 1-10 0-1 1-2 2-3-1-2-1-3-2-4h1c2-1 3-3 5-3h3z" class="c"></path><path d="M434 730l1-1 1 1c1 0 1 0 2-1v1c0 1-1 3-2 5-1 1-1 1-1 3l-1 1v2c2-1 1-2 3-1 0 2-1 2-2 3v2h-1c3-1 5-1 7 0 1 0 2 0 3 1h-2v1l-2 1c-1 2-2 2-4 2h-4l-1-1v-1c-1-1 0-4 0-5 1-3 2-8 1-10 0-1 1-2 2-3z" class="J"></path><path d="M441 745c1 0 2 0 3 1h-2v1l-2 1c-1 2-2 2-4 2h-4l-1-1v-1l10-3z" class="E"></path><path d="M436 750c0-1 0-1 1-2 1 0 2-1 3 0-1 2-2 2-4 2z" class="C"></path><defs><linearGradient id="U" x1="751.821" y1="440.001" x2="671.755" y2="509.599" xlink:href="#B"><stop offset="0" stop-color="#222121"></stop><stop offset="1" stop-color="#464545"></stop></linearGradient></defs><path fill="url(#U)" d="M736 412c1 3 0 6-1 9v2c-4 24-7 47-14 70-2 5-3 11-5 16-1 0-1 0-2 1h-1c-1 0-1 0-2-1h-1c-1 0-2 0-3 1-6 3-11 7-18 9h0c-2 1-3 1-4 1h0c-3-3-7-6-10-9-1 0-2-1-3-2l-2-2-1-1c-3-1-6-4-9-7-2-1-3-2-5-3 9-2 18-5 26-10 21-11 37-29 47-51 3-7 5-15 8-23z"></path><defs><linearGradient id="V" x1="817.566" y1="306.217" x2="768.806" y2="208.609" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#5a5957"></stop></linearGradient></defs><path fill="url(#V)" d="M799 202l13 73c2 11 5 23 7 34v3c-11-25-27-48-47-66-7-7-15-13-23-19l-4-3h-1l-2-2 1-1c1 0 2 1 3 1 3 1 10 1 14 0h1c2 0 6-3 8-1 0 1 1 1 2 2 1 0 2 2 3 3 1 0 2 1 3 2h0l3 2h1l-4-4-10-9h1l-1-2 2-1c2 0 3-1 5-1 8-2 16-5 23-10l1-1h1z"></path><path d="M485 148l1-1c-1 4-2 8-2 12-3 14-6 30-6 45 0 7 1 16 0 24-1 0-1 0-2 1l-1 1c-1 0-1-1-2-1l-1 3c-2-4-6-8-9-11l2-1c-2-2-3-3-4-5h-1v-3l-1-2 1-1-1-1h1c2 0 2-1 3-2 0-1 0-1 1-2l-1-1h0-2c1-1 2-2 2-3l-2 1c-4 1-8 2-11 0h-1c-2-1-4-2-6-4-1 0-1 1-2 1-2-1-3-4-4-6 0-2-1-3 0-5h0l-1-1-3 3v1c0-1 0-2 1-3h1c1-2 2-3 3-5l-1-4c1-2 1-2 0-4 1-2 2-2 3-3h1s1 0 1-1c1-3 1-7 2-11 0-2 2-4 1-5v-1c-2-1-4-1-7-3h0c-1 0-2 0-3-1l2-1h47 1z" class="M"></path><path d="M463 200c2-1 4-2 5-4 1 2 1 3 0 4-2 0-2 1-3 2v1l-1 1-1-1h0-2c1-1 2-2 2-3z" class="E"></path><path d="M470 201l4-7-1 13c-2-2-2-4-3-6z" class="U"></path><path d="M475 220c1 2 1 6 1 9l-1 1c-1 0-1-1-2-1-1-3 1-7 2-9z" class="G"></path><path d="M465 220c1 1 2 2 3 2h1 1c1 0 2-1 3-2h1 1c-1 2-3 6-2 9l-1 3c-2-4-6-8-9-11l2-1z" class="S"></path><path d="M465 203c1-1 3-1 5-2 1 2 1 4 3 6 0 2 0 3-1 4v2 1l-1 1c0 1-2 3-4 4-2-2-4-4-5-7 1-1 1-1 3-1v-1c-1 0-3 1-4 2 1 1 1 2 0 3h-1v-3l-1-2 1-1-1-1h1c2 0 2-1 3-2 0-1 0-1 1-2l1-1z" class="K"></path><defs><linearGradient id="W" x1="478.713" y1="170.573" x2="465.106" y2="169.072" xlink:href="#B"><stop offset="0" stop-color="#6e675d"></stop><stop offset="1" stop-color="#9b9587"></stop></linearGradient></defs><path fill="url(#W)" d="M468 170c-1-1-1-2-3-3-2 0-3-1-5-2h0l4 1-1-1 1-1 1 1h0v-1c-2-2-5-2-7-4h-1v-1-1l1 1h2c1 1 3 2 4 3h1c1 1 2 2 4 2 3 2 8 5 11 9 0 2 0 5-1 7l-4 4c0-1-1-2-1-3-1-2-3-4-4-6h0c-1-1-2-2-4-2 1-1 1-2 2-3z"></path><path d="M468 170c1-1 1-1 1-2v1c2 2 2 3 4 5l1 1c-1 1-1 1-1 2l-1-1c-1 0-1-1-2-1h0c-1-1-2-2-4-2 1-1 1-2 2-3z" class="b"></path><path d="M445 153c1 1 2 1 3 1 2 1 4 2 6 2s4 2 6 2c5 2 10 5 14 8v1c-3-2-6-4-10-5-1-1-3-2-4-3h-2l-1-1v1 1h1c2 2 5 2 7 4v1h0l-1-1-1 1 1 1-4-1h0c2 1 3 2 5 2 2 1 2 2 3 3-1 1-1 2-2 3-4-2-8-2-13-2-2 0-4 1-6 2h0c-2 2-3 3-4 5h0c0 1-1 1-2 1v-2h-1c-1 2-1 4-2 5l-1-4c1-2 1-2 0-4 1-2 2-2 3-3h1s1 0 1-1c1-3 1-7 2-11 0-2 2-4 1-5v-1z" class="c"></path><path d="M445 172l1 1c0-1 1-1 2-2h2v-1c1 0 2 1 3 1-2 0-4 1-6 2h0c-2 2-3 3-4 5h0c0 1-1 1-2 1v-2h-1c-1 2-1 4-2 5l-1-4c1-2 1-2 0-4 1-2 2-2 3-3h1v2 2h1l3-3z" class="I"></path><path d="M437 174c1-2 2-2 3-3h1v2c-1 1-1 3-2 4l1 1v-1c-1 2-1 4-2 5l-1-4c1-2 1-2 0-4z" class="g"></path><path d="M445 153c1 1 2 1 3 1 0 3-1 6-2 8v3 2c-2 2-1 2-1 5l-3 3h-1v-2-2s1 0 1-1c1-3 1-7 2-11 0-2 2-4 1-5v-1z" class="W"></path><defs><linearGradient id="X" x1="471.042" y1="169.47" x2="463.542" y2="146.847" xlink:href="#B"><stop offset="0" stop-color="#4e4d4e"></stop><stop offset="1" stop-color="#908b84"></stop></linearGradient></defs><path fill="url(#X)" d="M484 148h1l-4 21v2h0l-7-4h0v-1c-4-3-9-6-14-8-2 0-4-2-6-2s-4-1-6-2c-1 0-2 0-3-1-2-1-4-1-7-3h0c-1 0-2 0-3-1l2-1h47z"></path><path d="M437 148h47c-1 1-1 1-3 2-5 1-11 0-17 0-6 1-13 2-19 1h-1 0-2l-1-1h-3 0c-1 0-2 0-3-1l2-1z" class="I"></path><path d="M447 174c5-2 10-3 16-1 3 1 6 5 7 8 2 3 2 8 0 12l-2 3c-1 2-3 3-5 4l-2 1c-4 1-8 2-11 0h-1c-2-1-4-2-6-4-1 0-1 1-2 1-2-1-3-4-4-6 0-2-1-3 0-5h0l-1-1-3 3v1c0-1 0-2 1-3h1c1-2 2-3 3-5 1-1 1-3 2-5h1v2c1 0 2 0 2-1h0c1-2 2-3 4-5v1z" class="F"></path><path d="M453 176c3-1 8 0 11 2 2 4 3 7 3 12l-2 2c0-2 0-2-2-3 2 0 2 0 3-1 0-1 0-2-1-3s-2-1-4-1h-2c0-1 0-3-1-4 0-1-2-1-3-1 0-1 0 0-1-1l-1 1c-1 0-1-1-2-2 1 0 1 0 2-1h0z" class="N"></path><path d="M446 192c-2-2-2-5-2-7s1-5 2-6c2-2 2-2 4-2 1-1 2-1 3-1h0c-1 1-1 1-2 1 1 1 1 2 2 2l1-1c1 1 1 0 1 1v5c-2 1-5 0-7 1 0 2 1 2 2 4h2 0c-1 1-1 1-1 3-2-1-3-2-3-4h-1v1c0 2 0 2-1 3z" class="I"></path><path d="M446 192c1-1 1-1 1-3v-1h1c0 2 1 3 3 4 0-2 0-2 1-3h3c0 2 0 3 2 4h1l2-2 1-2h2c2 1 2 1 2 3-2 3-5 6-10 6-1 0-2 0-4-1h0c-2 0-4-3-5-5z" class="W"></path><path d="M455 179c1 0 3 0 3 1 1 1 1 3 1 4h2c2 0 3 0 4 1s1 2 1 3c-1 1-1 1-3 1h-2l-1 2-2 2h-1c-2-1-2-2-2-4h-3 0-2c-1-2-2-2-2-4 2-1 5 0 7-1v-5z" class="M"></path><path d="M447 174c5-2 10-3 16-1 3 1 6 5 7 8 2 3 2 8 0 12l-2 3c-1 2-3 3-5 4l-2 1c-4 1-8 2-11 0h-1c-2-1-4-2-6-4-1 0-1 1-2 1-2-1-3-4-4-6 0-2-1-3 0-5h0l-1-1-3 3v1c0-1 0-2 1-3h1c1-2 2-3 3-5 1-1 1-3 2-5h1v2c1 0 2 0 2-1h1c0 1 0 1-1 2 0 3-1 5-1 8 1 3 3 8 5 10 1 0 2 0 3 1h0c1 0 2 0 2 1h5v-1l3-1 1 1v-1c3-1 5-3 6-5l1-1c1 0 1-1 1-2 1-2 1-6 0-8s-2-3-3-5h-1l-2-2-3-1c-2-1-3 0-5 0s-2-1-3 0c-2 0-5 2-7 3v-1l2-2z" class="d"></path><path d="M440 177h1v2c-2 5-3 9 0 14l2 4c-1 0-1 1-2 1-2-1-3-4-4-6 0-2-1-3 0-5h0l-1-1-3 3v1c0-1 0-2 1-3h1c1-2 2-3 3-5 1-1 1-3 2-5z" class="F"></path><path d="M485 650v1c1 1 0 2 0 3 0 4 0 6 2 10h-1v144c-1 7 0 14 0 21 0 4 0 8 1 12 1 2 0 4 1 6 0 1 1 3 1 4 0 4-1 9 1 13h0l1 1-1 1c0-1 0-1-1-2h-1v2h0l-1 1c0 1-1 2-2 3v-1-6c2-4 1-10 1-14l-1-8-2-4h-1c0-3 1-9-1-11v1c-1-1-1-3-1-4-1-2-1-3-1-5 0 2 0 4-1 6h0v-9-5l-2 1v-1l2-6h-1l-1-1c1-7 0-13 1-19l-1-1 1-6h1c0-3 0-5-1-8h1v-4-4-15l-2-1h1c-1-2 0-3 0-3 0-1-1-2-1-2-1-1-2-1-2-2h-3c-1-1-1-2-2-3h2c1 0 2 1 4 0h-1l-1-1h1l2-2 2-1-1-1h0c1-2 1-2 1-4l-1-6v-1 1c-2 3-4 6-8 7h-1c-2 0-4 0-6-1-3-2-5-1-7-4h-2v-2h-1v-3-27-4c0-4 0-7 1-11 1 0 2-1 3-1 3 0 5-1 7-2h0 5v1h-4v1h5 1 0l-1-2c1 0 2 1 3 1h3v-4l-1-1-4 1 1-2h1c1-1 3-1 5-1 1 4-1 10 1 13v-4-9c0-1 1-3 0-4h0l1-1h0l1 1 2 1c1-1 0-1 1-1l-3-3 1-2v1l4-8z" class="C"></path><path d="M482 663c1 0 2 0 3-1v3l-1 38 1 136c-1-4-2-9-2-12-2-18 0-37-1-55v-30c0-5 0-10 1-14v-5c1-4 0-9 0-14v-23-11c0-2 1-4 0-6v-1h0c0-1 0-3 1-3v-1l-2-1z" class="D"></path><path d="M478 675v-9c0-1 1-3 0-4h0l1-1h0l1 1 2 1h0l2 1v1c-1 0-1 2-1 3h0v1c1 2 0 4 0 6v11 23c0 5 1 10 0 14v5c-1 4-1 9-1 14v30c1 18-1 37 1 55 0 3 1 8 2 12v2l-2-4h-1c0-3 1-9-1-11 0-4-1-10-1-14v-4c1-2 1-5 1-7v-15-45c0-3-2-9 0-11v-6-21h-1v-14c-1-2-1-7-1-9s0-3-1-5z" class="H"></path><path d="M478 675v-9c0-1 1-3 0-4h0l1-1h0l1 1c1 11 2 21 2 31v15c0 6-1 13-1 19 1 5 1 9 0 14 0-3-2-9 0-11v-6-21h-1v-14c-1-2-1-7-1-9s0-3-1-5z" class="S"></path><path d="M480 703h1v21 6c-2 2 0 8 0 11v45 15c0 2 0 5-1 7v4c0 4 1 10 1 14v1c-1-1-1-3-1-4-1-2-1-3-1-5 0 2 0 4-1 6h0v-9-5l-2 1v-1l2-6h-1l-1-1c1-7 0-13 1-19l-1-1 1-6h1c0-3 0-5-1-8h1v-4-4-15l-2-1h1c-1-2 0-3 0-3 0-1-1-2-1-2-1-1-2-1-2-2h-3c-1-1-1-2-2-3h2c1 0 2 1 4 0h-1l-1-1h1l2-2 2-1-1-1h0c1-2 1-2 1-4l1 4h0v-2-8c2-4 1-12 1-17z" class="F"></path><path d="M476 732h2v2 2 1h-1c-2-1-3 0-4 0h-1l-1 1c-1-1-1-2-2-3h2c1 0 2 1 4 0h-1l-1-1h1l2-2z" class="e"></path><path d="M476 783l1-6h1v15 11 1h-1l-1-1c1-7 0-13 1-19l-1-1z" class="G"></path><path d="M476 740c0-1 0-1 1-2v1c1 0 1 0 1-1h1c1 1 0 3 1 5 1 1 0 6 0 8-1 2 0 7-1 8-1 3 0 8-1 11v-1-4-4-15l-2-1h1c-1-2 0-3 0-3 0-1-1-2-1-2z" class="P"></path><path d="M471 667h1c1-1 3-1 5-1 1 4-1 10 1 13v-4c1 2 1 3 1 5s0 7 1 9v14c0 5 1 13-1 17v8 2h0l-1-4-1-6v-1 1c-2 3-4 6-8 7h-1c-2 0-4 0-6-1-3-2-5-1-7-4h-2v-2h-1v-3-27-4c0-4 0-7 1-11 1 0 2-1 3-1 3 0 5-1 7-2h0 5v1h-4v1h5 1 0l-1-2c1 0 2 1 3 1h3v-4l-1-1-4 1 1-2z" class="Q"></path><path d="M464 695h1c1 0 1 0 3-1l1-1c0 1-1 2-2 3h0c0 2-1 3-1 5l1 2-1 1-2-2v1c-1 1-1 1-2 1l-2 1v-4c0-3 0-4 1-6h3z" class="F"></path><path d="M460 701c0-3 0-4 1-6h3l-1 5h-2v1h2v1c-1 1-1 0-2 1l1 1h0l-2 1v-4z" class="B"></path><path d="M471 667h1c1-1 3-1 5-1 1 4-1 10 1 13 0 7 1 15 0 22h-1v3h-1c0 1 0 1-1 1l-2-2h0v1h-1l1-2c-1-1-1-2-2-2v3c-2-1-1-3-1-4l-3-3c1-1 2-2 2-3l-1 1c-2 1-2 1-3 1h-1-3c-1 2-1 3-1 6l-1-3c0-2-3-1-4-1 0-2-1-3-1-5h-1l-1-2c2 0 3 1 5 1 4 2 6 2 10 1 1 0 2-1 3-1h-4v-1l2-1c1-1 2-1 3-2h1c1-1 1-3 2-3h1v-1h-2l1-2h2 0l-1-8v-4l-1-1-4 1 1-2z" class="E"></path><path d="M471 695c2-1 4-3 6-4-1-1-1-1-1-2h1v5s-1 1-1 2 1 1 0 2c-1-1-1-1-2-1 0-2-2-2-3-2z" class="L"></path><path d="M471 687h1c1-1 1-3 2-3h1c0 3-1 5-3 6v1l-1-1 1-1c-1 0-2 1-2 2h-4v-1l2-1c1-1 2-1 3-2z" class="P"></path><path d="M471 695c1 0 3 0 3 2 1 0 1 0 2 1 1-1 0-1 0-2s1-2 1-2v7 3h-1c-2-3-5-5-6-8l1-1z" class="T"></path><path d="M456 674c3 0 5-1 7-2h0 5v1h-4v1h5 1 0l-1-2c1 0 2 1 3 1h3l1 8h0-2l-1 2h2v1h-1c-1 0-1 2-2 3h-1c-1 1-2 1-3 2l-2 1v1h4c-1 0-2 1-3 1-4 1-6 1-10-1-2 0-3-1-5-1h0v-4c0-4 0-7 1-11 1 0 2-1 3-1z" class="N"></path><path d="M456 674v1 1c-2 3-1 9-1 12v1l2 2c-2 0-3-1-5-1h0v-4c0-4 0-7 1-11 1 0 2-1 3-1z" class="J"></path><path d="M462 688c-2-1-4-1-5-3-1-1-1-2 0-3 0 0 1 1 1 2s0 1 1 1l1 1c2 1 3 1 5 0 0 0 1 0 1-1h-2l-3-3c1-2 1-3 2-4l1 1v1h1s0-1 1-1l-1-1 1-1 2 2c1 1 1 3 1 5-1 1-2 3-4 4h-3z" class="B"></path><path d="M469 678l2 2h0 1c-1 2-2 5-3 7l1-1 1 1c-1 1-2 1-3 2l-2 1v1h4c-1 0-2 1-3 1-4 1-6 1-10-1l-2-2 1-2 1 1v1h1l2 1 2-2h3c2-1 3-3 4-4 0-2 0-4-1-5l1-1z" class="L"></path><path d="M469 674h1 0l-1-2c1 0 2 1 3 1h3l1 8h0-2l-1 2h2v1h-1c-1 0-1 2-2 3h-1l-1-1-1 1c1-2 2-5 3-7h-1 0l-2-2c-2-2-2-2-5-2v-1h4c1 1 1 1 2 1l-1-2z" class="F"></path><path d="M454 718v-2c0-2 1-2 1-3 1-3 4-5 7-6 3-2 4-2 8-1 3 1 4 4 6 7l3 7v8 2h0l-1-4-1-6v-1 1c-2 3-4 6-8 7h-1c-2 0-4 0-6-1-3-2-5-1-7-4 0-1-1-2-1-4z" class="a"></path><path d="M459 718v-2c1-2 3-5 5-6 3-1 4 0 6 1 3 2 4 4 4 8 0 1-1 1-1 2h-1l-1-1 2-2c-1-2-1-2-2-3h0 0c-1 0-2 0-2-2l-2 1v1h-2v1l2 1c0 1 1 1 1 2l-2 2c0-1 0-1-1-1l-1-1h3c-1-2-3-3-4-4l-1 1c-1-1-1 0-1-1-1 1-1 2-1 3h-1z" class="G"></path><path d="M454 718v-2c0-2 1-2 1-3 1-3 4-5 7-6 3-2 4-2 8-1 3 1 4 4 6 7-2-1-3-3-5-4l-1 2c-2-1-3-2-6-1-2 1-4 4-5 6v2 3c-1 1-1 1-2 1 0-2 2-3 0-5v1h-2-1z" class="Y"></path><path d="M562 738h3c2 0 3 1 4 1h1c1 1 2 3 4 3l1 1-1 1c1 1 2 2 2 4l1 2v-1l2-2c0 1 1 1 2 3v1l1 2h1v-2l2 2h3c2 7 3 15 0 22 0 3-2 7-4 9l-2 4h1l2-1s0-1 1 0h1 0c-1-1-1-1-2-1l1-1h1l-1-1 1-1 1 1 1 1-1 2 1 1-5 7 2 2c-3 3-6 6-9 8-8 4-15 7-23 7-2 1-5 1-7 0-1-1-1-1-2-1l-1-1c1 2 2 4 4 6l10 16c3 3 6 7 8 11-3-2-5-6-6-8-5-7-10-15-15-21l-1-1c0-1-1-1-1-3v-1l-1-1 1-1-1-1v-31-23c0-1 0-2 1-3 0 1 0 1 1 1l1-1c2-1 3-2 5-4 1 0 3-1 4-2 1 0 2-1 4-2h2l1-1h0 1v-2z"></path><path d="M582 788h1l2-1s0-1 1 0h1 0c-1-1-1-1-2-1l1-1h1l-1-1 1-1 1 1 1 1-1 2 1 1-5 7 2 2c-3 3-6 6-9 8-8 4-15 7-23 7-2 1-5 1-7 0-1-1-1-1-2-1l-1-1c1 2 2 4 4 6l10 16c3 3 6 7 8 11-3-2-5-6-6-8-5-7-10-15-15-21l-1-1c0-1-1-1-1-3v-1l-1-1 1-1v1h8c1-1 1-2 1-3h-3v1 1l-1 1c0-1 0-2-1-3h-4 0 0c1-1 3-1 5-1 3 0 7 0 10-1s7-2 10-3l2-1c2-1 4-2 6-4l4-4c0-2-1-3-2-4 1-1 1-2 1-2 1 1 2 2 2 3h0 1z" class="a"></path><path d="M560 804l1 2h0l-1-1-1 1c1 1 1 1 0 2-1 0-4 1-6 0v-2-1c3 0 5 0 7-1z"></path><defs><linearGradient id="Y" x1="572.756" y1="799.651" x2="583.774" y2="800.385" xlink:href="#B"><stop offset="0" stop-color="#928e88"></stop><stop offset="1" stop-color="#aeaba9"></stop></linearGradient></defs><path fill="url(#Y)" d="M584 795l2 2c-3 3-6 6-9 8-8 4-15 7-23 7-2 1-5 1-7 0-1-1-1-1-2-1l-1-1c1 2 2 4 4 6l10 16c3 3 6 7 8 11-3-2-5-6-6-8-5-7-10-15-15-21l-1-1c0-1-1-1-1-3v-1l-1-1 1-1v1l1 1h0 3c1 1 3 1 4 1 2 1 3 0 5 0 1 0 3 0 4-1l-1-1c1-1 1-1 0-2l1-1 1 1h0l-1-2 2-1c0 1 1 2 1 3h1c2-1 4-1 6-2l1-1 2-1c1 0 1 0 2-1 2 0 3-2 5-3 1-1 3-2 4-3z"></path><path d="M582 753h1v-2l2 2h3c2 7 3 15 0 22 0 3-2 7-4 9l-2 4h-1 0c0-1-1-2-2-3 0 0 0 1-1 2 1 1 2 2 2 4l-4 4-3-3h-2-1 0c-2 0-2-1-3-2-1 1-1 2-1 3l2 1h0c-1 1-2 2-3 2l-1-1h-2c0-2 0-2-1-3l-1 1c-1 0-1 0-2-1l-2-1c-1 0-2 0-3 1v-1c-3 0-6 0-8-1l-1-1c1 0 2-1 2-2 1 0 1 0 2-1 0 1 0 2 1 2l1-1c1 0 0 1 1 0 0-1 0-2-1-3l1-1 1-1 1-1c1-1 2-2 3-2l-2 3h1 0 0l-1 1v1c1 0 2-1 3-2v-1c1 1 2 2 2 3l1-1v-1c-1-1-1 0-1-1h1c1 0 1 1 1 1 1 0 2-1 3-1l3-3c0-1 0-2 1-2l1-1v-1c1-1 1-3 1-5 1-1 1-2 2-3v-1-1h1l2-3 1-1c2-2 4-5 6-7z" class="G"></path><path d="M567 790v-1h1c1 0 1-1 2-1h1l-1-1v-3l1-2h0l-1-1v-1l-1-1c0-1 1-2 1-2v-1c0-1 0-1 1-2l1 1c1 1 1 2 2 3l2 1-1 1c0 1 0 1 1 2 0-1 0-1 1-1l2 2h1c1 1 0 0 2 0 1 0 2 1 2 1l-2 4h-1 0c0-1-1-2-2-3 0 0 0 1-1 2 1 1 2 2 2 4l-4 4-3-3h-2-1 0c-2 0-2-1-3-2z" class="B"></path><path d="M582 753h1v-2l2 2h3c2 7 3 15 0 22 0 3-2 7-4 9 0 0-1-1-2-1-2 0-1 1-2 0 2-1 3-2 5-4-1-1-2 0-3-1l1-1c-1-1-1-1-1-2v-1c-1-1-1 0-2-1l1 2h-1c-2-2-4-4-5-6l2-2h1v-2c1-1 1-2 1-3l1-1v-3l-3 3h-2l1-1c2-2 4-5 6-7z" class="E"></path><path d="M582 753h1v-2l2 2h3c2 7 3 15 0 22l-1-1h-1l1-2 2-2-1-1h-1-1v-1l-1-2h0 2c1-1 0-1 1-2h-2c-1-1-2-1-2-2h-1l1 2-2-1c-1 0-2-1-3-1l1-1v-3l-3 3h-2l1-1c2-2 4-5 6-7z" class="D"></path><path d="M584 762c0-1 0-1 1-2l1 1c1 0 2 0 3 2-1 0-1 1-1 1h-2c-1-1-2-1-2-2z" class="B"></path><path d="M582 753h1v-2l2 2-2 2 2 2c1 0 2 0 2 1s0 1-1 3l-1-1c-1 1-1 1-1 2h-1l1 2-2-1c-1 0-2-1-3-1l1-1v-3l-3 3h-2l1-1c2-2 4-5 6-7z" class="C"></path><path d="M562 738h3c2 0 3 1 4 1h1c1 1 2 3 4 3l1 1-1 1c1 1 2 2 2 4l1 2v-1l2-2c0 1 1 1 2 3v1l1 2c-2 2-4 5-6 7l-1 1-2 3h-1v1 1c-1 1-1 2-2 3 0 2 0 4-1 5v1l-1 1c-1 0-1 1-1 2l-3 3c-1 0-2 1-3 1 0 0 0-1-1-1h-1c0 1 0 0 1 1v1l-1 1c0-1-1-2-2-3v1c-1 1-2 2-3 2v-1l1-1h0 0-1l2-3c-1 0-2 1-3 2l-1 1-1-3-1 1v1l-1-1v-1c-1 1-2 1-2 1l-1-1v-1h-1v2h-1c0-1-1-1-1-2v-2-3-2h-1v3 1-23c0-1 0-2 1-3 0 1 0 1 1 1l1-1c2-1 3-2 5-4 1 0 3-1 4-2 1 0 2-1 4-2h2l1-1h0 1v-2z" class="U"></path><path d="M564 749c1 0 2-1 2 0 2 0 2 1 3 2l-2 1-1 1c-1 0-1-1-2-1-1-1-1-2-1-3h1z" class="O"></path><path d="M562 738h3c2 0 3 1 4 1-2 2-4 3-7 4v1-2-1l-1-1h1v-2z" class="C"></path><path d="M562 738h3c0 2 0 3-3 3l-1-1h1v-2z" class="D"></path><path d="M574 749c0-1 0-3-1-4s-2-1-2-2v-1c1 0 2 1 3 2s2 2 2 4l1 2v1h-1l-2-2z" class="T"></path><path d="M551 757c3-1 6-1 9-1l2 2c-1 0-2 1-3 1v1h-1v-1-1c-1 1-1 0-2 1l-1-1v1 1h-2l1-1v-1c-1 0-2 0-3-1z" class="H"></path><path d="M557 750c-1-1 0-1 0-1l-1-2c1-1 1-1 2-1l-1-2c1-1 2-1 3-2h2v2c-2 1-2 2-2 4v6l-3-4h0z" class="M"></path><path d="M574 749l2 2h1v5c-3 3-4 4-7 4-1 0-1 0-2-1l-1-1c1-1 2 0 4-2h-2v-1h2v1l2-2c1-2 1-3 1-5z" class="L"></path><path d="M550 745c1 0 3-1 4-2 1 0 2-1 4-2h2l1-1h0l1 1v1h-2c-1 1-2 1-3 2l1 2c-1 0-1 0-2 1l1 2s-1 0 0 1c-1 1-2 1-3 2v-2l-1-1c-2 0-3 1-4 2l-1 1c1 1 3 1 4 2l-1 1v-1h-4-1v1l-1 1v-1c-2 2-2 5-2 7v8h1l1 1c2 2 6 6 6 8l-1 1v1l-1-1v-1c-1 1-2 1-2 1l-1-1v-1h-1v2h-1c0-1-1-1-1-2v-2-3-2h-1v3 1-23c0-1 0-2 1-3 0 1 0 1 1 1l1-1c2-1 3-2 5-4z" class="G"></path><path d="M545 771l-1-1h-1v-8c0-2 0-5 2-7v1l1-1v2l1 1s3-1 4-1c1 1 2 1 3 1v1l-1 1h2v1l3 3c2 2 3 3 3 6v3 1c-1 2-3 3-5 4v1c-1 0-2 1-3 2l-1 1-1-3c0-2-4-6-6-8z" class="D"></path><path d="M545 771c1-2 2-3 4-4v1l-2 2-1 1c1 0 1 1 1 1 3 3 4 2 7 2 1 0 1 0 2-1 0-1 1-3 0-5 0-1-2-3-3-3l-3-2h-1-1 0c1-1 0-1 1-1-1-1 0-1-1-2h3c1 1 2 1 4 1l3 3c2 2 3 3 3 6v3 1c-1 2-3 3-5 4v1c-1 0-2 1-3 2l-1 1-1-3c0-2-4-6-6-8z" class="a"></path><path d="M577 750v-1l2-2c0 1 1 1 2 3v1l1 2c-2 2-4 5-6 7l-1 1-2 3h-1v1 1c-1 1-1 2-2 3 0 2 0 4-1 5v1l-1 1c-1 0-1 1-1 2l-3 3c-1 0-2 1-3 1 0 0 0-1-1-1h-1c0 1 0 0 1 1v1l-1 1c0-1-1-2-2-3v1c-1 1-2 2-3 2v-1l1-1h0 0-1l2-3v-1c2-1 4-2 5-4v-1-3c0-3-1-4-3-6l-3-3v-1-1-1l1 1c1-1 1 0 2-1v1 1h1v-1c1 0 2-1 3-1h3c1 1 2 1 3 1 1 1 1 1 2 1 3 0 4-1 7-4v-5-1z" class="K"></path><path d="M577 750v-1l2-2c0 1 1 1 2 3v1l1 2c-2 2-4 5-6 7l-1 1-2 3-1-3c2-1 5-3 6-4l-1-1v-5-1z" class="P"></path><path d="M555 760v-1-1l1 1c1-1 1 0 2-1v1 1h1v-1h3 0l-2 2c0 1 1 2 1 3 2 0 3 1 4 2v2 3 1c0 1 0 1-1 2l1 1c-1 1-3 3-3 5l-1 1c-1 0-1-1-2 0 0 1 0 0 1 1v1l-1 1c0-1-1-2-2-3v1c-1 1-2 2-3 2v-1l1-1h0 0-1l2-3v-1c2-1 4-2 5-4v-1-3c0-3-1-4-3-6l-3-3v-1z" class="F"></path><defs><linearGradient id="Z" x1="674.029" y1="246.467" x2="843.966" y2="318.2" xlink:href="#B"><stop offset="0" stop-color="#464241"></stop><stop offset="1" stop-color="#999389"></stop></linearGradient></defs><path fill="url(#Z)" d="M689 214l1-1c-1 0 0 0-1-1-1 0-1 0-1-1 2 0 4-1 6-1 2-1 3-2 4-2 2 0 6-1 8-2 1-1 1-1 2 0h3 1l9 4c2 2 6 4 8 5 3 2 8 5 12 7 1 1 2 2 3 2h1l4 3c8 6 16 12 23 19 20 18 36 41 47 66-1 1 1 6 1 7l4 23c1 11 2 23 2 35-3-10-5-20-9-30-12-36-33-71-63-95-7-7-17-15-27-20l-4-2-3-2-16-9-3-1-7-3-5-1z"></path><path d="M454 285l2-3c1 1 2 0 2 2h1c0 1 0 1 1 2l1-1c-1-1-1-1 0-2v-1c2 1 2 1 3 3 1-1 1-1 2-1s1 0 3 1h0c1 0 1 1 2 2v1h0v7l1 1 6-3v2 48c0 8-1 16 0 23v24c-1-1-2-2-2-3 0-2-1-4-1-5 0-3 0-8-1-10v-1c0-1-1-2-1-2l-1-1 1-1c-1-1-5-3-6-4-3-2-4-4-6-6l-1 1h-1l-1 1c0 1-1 2-2 3h-1l1-2v-4c0-1-1-1-1-2h-1c0 1 0 2-1 3v-1l-1-2c1-1 1-1 1-2-1-2-1-3-1-5 0-1 0-2-2-3v2h0-4-3l1-1h0 1v-1l-1 1h-2 0l-2-1c-1 1-1 2-2 3l-1-1 1-1c-1-1-2-1-3-2h0-5c-1 1 1 2 1 3h-1c-1 0-2 0-3 1-1-1-1-2-1-4-4-3-5-8-6-12l-1-1h-2c1-1 2-2 2-3 1-1 1-3 1-5 0-5 2-9 5-13 1-2 4-6 6-7s7-1 10-1l-3-1h3c2-1 3 0 5-1l-1-1h-1l-1-1 1-1h1 1v-1h3v1h1v-1l3-6v-1c1-1 1-2 1-3z" class="H"></path><path d="M450 332c1-2 4-3 6-4 0 2 0 3-1 5h-1c-2-1-2 0-4-1z" class="Z"></path><path d="M447 311c0-1 0-2-1-3v1h-4v-1-1-1h2c1-1 2-3 4-4h0v1 1c0 2-1 4-1 7z" class="P"></path><defs><linearGradient id="a" x1="425.212" y1="340.174" x2="421.562" y2="332.201" xlink:href="#B"><stop offset="0" stop-color="#6b6a69"></stop><stop offset="1" stop-color="#84827f"></stop></linearGradient></defs><path fill="url(#a)" d="M420 331c1-1 2-2 4-1h0c0 3 0 6 1 9 1 1 1 2 1 3v1c-4-3-5-8-6-12z"></path><path d="M448 304h6v1 1c-1 0-2 1-2 2 0 0 0 1-1 2v3 1c1 1 1 1 2 3-1 0-1 0-2 1l-1 1-3-8c0-3 1-5 1-7z" class="a"></path><path d="M443 325l1-1h2c1-1 1-2 1-4 2 0 1 1 3 1l1 2h0c-2 0-2 0-2-1l-1 1 1 1h0c-1 1-1 2-1 2-1 2-3 4-5 5-1 0-1-1-2 1h1l-1 1c0 1-1 1-1 2h-1v-3c-1 0-2 1-2 1-1 0-1 0-2-1l4-4h1v-1l-1 1-2-1-2-2 1-1c0 1 1 2 3 2 1 0 1 0 2 1 1 0 1-1 2-1h0v-1z" class="L"></path><defs><linearGradient id="b" x1="429.363" y1="307.902" x2="419.919" y2="328.195" xlink:href="#B"><stop offset="0" stop-color="#a5a29f"></stop><stop offset="1" stop-color="#c9c6c5"></stop></linearGradient></defs><path fill="url(#b)" d="M441 301l1-1h3c2 0 3 0 5-1h1v1c-1 0-2 0-3 1h-2c-2 0-4 1-6 1s-4 1-6 1c-1 1-1 0-2 2-4 5-8 13-9 20h1 1c1-1 1-1 2 0h0c-2 3-4 3-7 4l-1 1h-2c1-1 2-2 2-3 1-1 1-3 1-5 0-5 2-9 5-13 1-2 4-6 6-7s7-1 10-1z"></path><path d="M455 303c-1-2-2-3-3-5 1-5 2-6 6-9 2 0 6-1 8-2l4 3s0-1 1-1v-1 7l1 1c-1 1-2 1-3 2l-1 1v1l-1 2 1 1v1-1c-2-1-3-1-5-1-1 1-1 1-1 2-1 0-1 0-2 1-1 0-1 0-1-1-1 0-1 1-2 1-1 1-1 1-2 1l-1-1 1-1v-1z" class="N"></path><path d="M468 299c-1-1-1-2-1-3h-1l-1 1c-1 0 0 0-1 1v1h0l-1-1 1-1-1-1v-3h1l-1-1 1-2h1v3c1 1 2 2 4 2v-1c-1 0-2-1-3-1v-4c1 1 4 2 4 3s0 3 1 4v-1l1 1c-1 1-2 1-3 2l-1 1z" class="D"></path><path d="M434 303c2 0 4-1 6-1v2h1c0 2-1 2-2 4h1v2l1 1v1c3 0 5 1 6 3l1 1v1c-1 1 0 1 0 2-4 0-4-6-9-1 0 1-1 2-1 3 0 2 2 3 3 4h2v1h0c-1 0-1 1-2 1-1-1-1-1-2-1-2 0-3-1-3-2l-1 1 2 2 2 1 1-1v1h-1l-4 4c1 1 1 1 2 1v1c-1 1-2 1-3 2v1c-1-1-1 0-1-1l-1-1v2l4 4c2 0 4 1 6 0 2 1 4 0 7 1-6 1-13 1-19-1-1-1-2-2-2-4h1c-2-2-2-5-2-8l1-3c1-1 1-3 1-4l-1 1h-1 1l-2-1c1-2 3-4 3-6v-1c-1-2 0-3 1-4h1v-1c-1-1 1-3 2-4v-1h-1c1-2 1-1 2-2z" class="a"></path><path d="M434 303c2 0 4-1 6-1v2c0 1 0 2-2 2h-2l1-2h-1l-2-1z" class="B"></path><path d="M478 293v2 48c0 8-1 16 0 23v24c-1-1-2-2-2-3 0-2-1-4-1-5 0-3 0-8-1-10v-1c0-1-1-2-1-2l-1-1 1-1c-1-1-5-3-6-4-3-2-4-4-6-6-6-8-11-14-12-24l1-1c2 1 2 0 4 1h1c1-2 1-3 1-5l1-1c-3-2-5-4-7-8l1-1c1-1 1-1 2-1-1-2-1-2-2-3v-1-3c1-1 1-2 1-2 0-1 1-2 2-2v-1-1l1-1v1l-1 1 1 1c1 0 1 0 2-1 1 0 1-1 2-1 0 1 0 1 1 1 1-1 1-1 2-1 0-1 0-1 1-2 2 0 3 0 5 1v1-1l-1-1 1-2v-1l1-1c1-1 2-1 3-2l6-3z" class="C"></path><path d="M469 325l4-5v7h-1v-2h-1-2z" class="E"></path><path d="M478 293v2l-2 2v3c-1 1-1 6-1 8l-1-1v1h-1l-5-5-1-1 1-2v-1l1-1c1-1 2-1 3-2l6-3z" class="B"></path><path d="M478 293v2l-2 2c-1 1-3 1-4 3h1c0 1 1 2 0 4-1-1-1-2-1-3s0 0-1-1h0l-3 3-1-1 1-2v-1l1-1c1-1 2-1 3-2l6-3z" class="f"></path><path d="M450 332c2 1 2 0 4 1l-1 2c0 1 1 1 2 1l1 2v1c-1-1-1-2-2-2v1c1 2 0 5 3 7h1 1l1-1c1 0 2-1 3-1h0l2 1c1 0 1 1 2 2v-1-1-1l4-4h1 1 1l1 1s-1 1-1 2h0c1 1 1 4 1 5-1 2 0 5 0 7-1 3 0 8 1 12v2 1h-1l-2-2c-1-1-5-3-6-4-3-2-4-4-6-6-6-8-11-14-12-24l1-1z" class="L"></path><path d="M462 350h1c0 2-1 4-1 5 1 0 2 1 3 1l-1 1 1-1h1l1 1 1-2 1 1c-1 0-1 1-1 2 1 1 1 2 2 3l1-1v-1c-1-1-1-1-1-2v-2h1c1 1 1 3 1 4 2 2 2 3 2 5 0 1 0 1 1 2h1v2 1h-1v-1l-10-9-1-1c-1-1-3-3-3-4s1-2 1-3v-1z" class="K"></path><path d="M463 343l2 1c1 0 1 1 2 2v-1-1-1l4-4h1 1 1l1 1s-1 1-1 2h0c1 1 1 4 1 5l-1-1-2-2-1-1h0l1 4v1h-1v-1c-1-1-1-1-1-2-1 0-1 1-1 1-1 1-3 1-3 1v3h0v1c0 2-1 3-1 5-1 0-2-1-3-1 0-1 1-3 1-5v-2c0-1 0-2 1-3h0l-1-2h0z" class="E"></path><path d="M450 332c2 1 2 0 4 1l-1 2c0 1 1 1 2 1l1 2v1c-1-1-1-2-2-2v1c1 2 0 5 3 7h1 1l1-1c1 0 2-1 3-1l1 2h0c-1 1-1 2-1 3v2h-1v1c0 1-1 2-1 3s2 3 3 4l1 1 10 9v1l-2-2c-1-1-5-3-6-4-3-2-4-4-6-6-6-8-11-14-12-24l1-1z" class="X"></path><path d="M458 345h1l1-1c1 0 2-1 3-1l1 2h0c-1 1-1 2-1 3v2h-1l-1-3h-1l-1 4h-1v-3c-1-1-1-1-1-2v-1h1z" class="F"></path><path d="M460 305c1-1 1-1 2-1 0-1 0-1 1-2 2 0 3 0 5 1v1-1l5 5c2 3 2 5 2 8s0 2-2 4l-4 5h-1v1 3l1 2c0 2-1 4-1 5-1 1-2 1-3 1l-1-1-1 1 2 1c-1 1 0 1-1 2h1c0 1-1 2-2 3h0c-1 0-2 1-3 1l-1 1h-1-1c-3-2-2-5-3-7v-1c1 0 1 1 2 2v-1l-1-2c-1 0-2 0-2-1l1-2h1c1-2 1-3 1-5l1-1c-3-2-5-4-7-8l1-1c1-1 1-1 2-1-1-2-1-2-2-3v-1-3c1-1 1-2 1-2 0-1 1-2 2-2v-1-1l1-1v1l-1 1 1 1c1 0 1 0 2-1 1 0 1-1 2-1 0 1 0 1 1 1z" class="f"></path><path d="M455 303v1l-1 1 1 1c1 0 1 0 2-1 1 0 1-1 2-1 0 1 0 1 1 1l-6 5 1 1v1h-2l-2-2c1-1 1-2 1-2 0-1 1-2 2-2v-1-1l1-1z" class="Z"></path><path d="M455 312c2-1 3-4 6-4-1 2-2 2-3 3l1 1c0 1-1 2-1 3h0c0 1 0 2-1 3l-1 1h1l1 2 1 1 2 2v1l-1 1c0 1-2 1-3 1-3-2-5-4-7-8l1-1c1-1 1-1 2-1-1-2-1-2-2-3v-1-3l2 2h2z" class="O"></path><path d="M459 322c1-1 2-1 3 0v-1h2l1 1v-1l1 1-1 1h1c1-1 1-1 3-1v1 1c-1 1-1 0-1 1v1 3l1 2c0 2-1 4-1 5-1 1-2 1-3 1l-1-1-1 1 2 1c-1 1 0 1-1 2h1c0 1-1 2-2 3h0c-1 0-2 1-3 1l-1 1h-1-1c-3-2-2-5-3-7v-1c1 0 1 1 2 2v-1l-1-2c-1 0-2 0-2-1l1-2h1c1-2 1-3 1-5l1-1c1 0 3 0 3-1l1-1v-1l-2-2z" class="T"></path><path d="M458 345v-2s-1 0-1-1c0-2 2-5 3-7h1l-1-2h0 1c1 1 2 1 3 3l-1 1 2 1c-1 1 0 1-1 2h1c0 1-1 2-2 3h0c-1 0-2 1-3 1l-1 1h-1z" class="U"></path><defs><linearGradient id="c" x1="635.363" y1="879.129" x2="619.599" y2="805.518" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#2e2d2d"></stop></linearGradient></defs><path fill="url(#c)" d="M586 815h0c1-1 2-1 3-2l6-3 26 26c0-4 1-5 2-8l2-4c1-1 2-1 2-1l12 12c14 13 29 21 46 27 3 1 7 3 10 4 2 0 4 1 6 1v1c-3 1-7 0-10 0h-32-71l-20-28-2-4c0-2 0-3 1-4l2-2c0-2 1-3 2-4 0-1 0-2-1-3l1-1 4-2c1 0 1-1 2-1l1-1 1 1 2-1v-1c1 0 2 0 3-1h0c1 0 1 0 2-1z"></path><defs><linearGradient id="d" x1="621.877" y1="828.608" x2="636.59" y2="838.58" xlink:href="#B"><stop offset="0" stop-color="#928a7f"></stop><stop offset="1" stop-color="#afa593"></stop></linearGradient></defs><path fill="url(#d)" d="M627 823l12 12v1c-1 2-2 3-4 4l-3 3c2 1 4 2 6 4-6-3-12-7-17-11 0-4 1-5 2-8l2-4c1-1 2-1 2-1z"></path><defs><linearGradient id="e" x1="652.826" y1="832.07" x2="662.97" y2="869.044" xlink:href="#B"><stop offset="0" stop-color="#b4a18a"></stop><stop offset="1" stop-color="#f1ecd9"></stop></linearGradient></defs><path fill="url(#e)" d="M639 835c14 13 29 21 46 27 3 1 7 3 10 4-2 0-6-1-8-1-17-3-35-8-49-18-2-2-4-3-6-4l3-3c2-1 3-2 4-4v-1z"></path><path d="M475 520l2 2c1 0 1-1 2-1h1v-1c1 0 2-1 3-1 1 1 2 1 4 1l1-1c0 1 1 1 2 2h0 2l-1 1c-1 0-2 0-2 1l-1-1-3-1-1 1 1 2c-1 1-1 2-2 3l1 1h1 0l1-1v1h0c1 1 1 2 1 3v1c0 1-1 1-1 3v1l2 2v-1l-1-1 1-1 1 1c1-1 1-1 1-2-1-1-1-1-2-1 0-2-1-3 0-4l-1-1 1-3h2v1c-1 1-1 1-1 2h1 1c1 1 2 2 3 2h1v-1c1 0 2 1 3 2l-1 2 2 1c1 1 1 3 1 4 0 2 0 3-1 4v6 5 1h0l1-1v1c1 2 5 1 7 1 4-1 8 0 12 0v1 2h-2c-3-1-5 0-8 0h0-4l-1 1v1l1-1c2 3 0 11 1 15v3 3l-4 24h-1c-1 3-1 6-1 9 0 1 0 3 1 5h0c-1 2-1 3-1 4v1l-1 1h-1 0c-1 3-5 7-7 10l-5 14-1 2-4 8v-1l-1 2 3 3c-1 0 0 0-1 1l-2-1-1-1h0l-1 1h0c1 1 0 3 0 4v9 4c-2-3 0-9-1-13-2 0-4 0-5 1h-1l-1 1c-2 0-2 0-3-1v-4h0v-2-11-1h2c0-1 0-2 1-2l-1-1 1-1c-1-1-1-3-1-4l1-2c-1-1 0-4 0-6h4v-1l-1-2 2-2h0l1-4-1-15c0-3 0-6 1-8l1-2v-3-3-16h-1l-3-1c-6-2-14-1-20 0v-1-6-8-29-2c0-2-1-2-2-3l2-1c1-2 1-2 4-3l-2-1h-1v-1l3-1h3 0 7 8z" class="G"></path><path d="M491 561c1 0 2 0 2 1 1 2 0 5 1 7l-1 12c-1 0-1 0-2 1h0l-1-1h-1-2 0c-2-1-1 0-2-2l2-1h1 4l-2-2c-3 0-5 0-7 1h-1v-1h1v-2h1v1c2-1 3 0 5 0 1-1 2-1 3-1-1-3 0-5 0-7h-2v-1h-2l2-1c1-1 1-3 1-4z" class="M"></path><path d="M476 601c1 1 1 1 1 2l1 2 1-1c1 0 2 1 3 2 0 4 0 10-1 14s-2 7-2 11c0 1 0 0-1 2v4 1 1l-1 1c-2 2 1 12-1 13v-17-12l-1-15c0-3 0-6 1-8z" class="C"></path><path d="M476 624v12 17 8 1h0c0 2 0 3 1 4-2 0-4 0-5 1h-1l-1 1c-2 0-2 0-3-1v-4h0v-2-11-1h2c0-1 0-2 1-2l-1-1 1-1c-1-1-1-3-1-4l1-2c-1-1 0-4 0-6h4v-1l-1-2 2-2h0l1-4z" class="T"></path><path d="M470 639h2 0 2v4 1 2c-2 0-2 0-4 2l1 3-1 1c-1-1-1-2-1-3s0-2 1-2l-1-1 1-1c-1-1-1-3-1-4l1-2zm24-90v-6c0-3-1-6 0-9l2 15v19c1 7 0 15-1 22v2h0v5c1 1 1 1 0 2 0 3 1 6 0 9l-1 3c0 1-1 3 0 4l-3 4v1 1l1 1c0 1-1 2-1 3l1 1v2l-1 1 1 1 1-2v1c-1 2-2 2-2 5l-5 14-1 2-4 8v-1l4-23 8-53 1-12c-1-2 0-5-1-7 0-1-1-1-2-1s-1-1-2-1l1-1v-1-1h2v-2l-2 1v-1c1-1 1-1 1-2l-1-1v-2c0-1 1-2 2-2s1 1 2 1z" class="Y"></path><path d="M492 548c1 0 1 1 2 1 0 6 1 14 0 20-1-2 0-5-1-7 0-1-1-1-2-1s-1-1-2-1l1-1v-1-1h2v-2l-2 1v-1c1-1 1-1 1-2l-1-1v-2c0-1 1-2 2-2z" class="H"></path><path d="M495 597c1 1 1 1 0 2 0 3 1 6 0 9l-1 3c0 1-1 3 0 4l-3 4v1 1l1 1c0 1-1 2-1 3-1 2-3 13-4 13v-6h0l2-10 1-4c0-1 1-3 1-5 1-3 2-5 3-8 0-3 0-6 1-8z" class="J"></path><path d="M491 625l1 1v2l-1 1 1 1 1-2v1c-1 2-2 2-2 5l-5 14-1 2-4 8v-1l4-23h1c0-1 0-1 1-1v-1 6c1 0 3-11 4-13z" class="g"></path><path d="M498 548v-1c1 2 1 4 1 6v1h0l1-1v1c1 2 5 1 7 1 4-1 8 0 12 0v1 2h-2c-3-1-5 0-8 0h0-4l-1 1v1l1-1c2 3 0 11 1 15v3 3l-4 24h-1c-1 3-1 6-1 9 0 1 0 3 1 5h0c-1 2-1 3-1 4v1l-1 1h-1 0c-1 3-5 7-7 10 0-3 1-3 2-5v-1l-1 2-1-1 1-1v-2l-1-1c0-1 1-2 1-3l-1-1v-1-1l3-4c-1-1 0-3 0-4l1-3c1-3 0-6 0-9 1-1 1-1 0-2v-5h0v-2c1-7 2-15 1-22v-19l2-1z" class="Y"></path><path d="M505 569c-1 4-1 7-2 10v4c-1 2-1 4-2 6v-1-2l1-2v-11l3-4z" class="K"></path><path d="M497 579h1v1 5h1v-4h1 0c0 1 0 2 1 2v3 2c-1 2-1 4-1 6l-3 14c0 2 0 3-1 5h0c0 1 0 2-1 3v1l-1 1v1s-1 1-1 2l-1 1-1-1v-1-1l3-4c-1-1 0-3 0-4l1-3c1-3 0-6 0-9 1-1 1-1 0-2v-5l2-13z" class="c"></path><path d="M497 579h1v1 5h1c0 2-1 4-1 6-1 3 0 5-1 8v7l-1 4s-1 1-1 2l-1 3h0c-1-1 0-3 0-4l1-3c1-3 0-6 0-9 1-1 1-1 0-2v-5l2-13z" class="N"></path><path d="M498 548v-1c1 2 1 4 1 6v1h0l1-1v1c1 2 5 1 7 1 4-1 8 0 12 0v1 2h-2c-3-1-5 0-8 0h0-4l-1 1v1l1-1v8 2l-3 4v11l-1 2v-3c-1 0-1-1-1-2h0-1v4h-1v-5-1h-1l-2 13h0v-2c1-7 2-15 1-22v-19l2-1z" class="P"></path><path d="M497 574l2-13c-1-1-1-2-1-3l1-1v1l1 1c1 3 0 6 0 10h-1c-1 2-1 3-2 5z" class="V"></path><path d="M504 558l-3-1h0c5-2 12-1 17-1l1 1-1 1h-1c-3-1-5 0-8 0h-5z" class="d"></path><path d="M504 558h5 0-4l-1 1v1l1-1v8 2l-3 4v11l-1 2v-3c-1 0-1-1-1-2h0-1v4h-1v-5-1h-1v-5c1-2 1-3 2-5h1c0-4 1-7 0-10 1-1 0-1 2 0l2-1z" class="Z"></path><path d="M502 563l1 2h1 0v2h1v2l-3 4v-10z" class="S"></path><path d="M504 558h5 0-4l-1 1v1l1-1v8h-1v-2h0-1l-1-2v-4l2-1z" class="D"></path><path d="M475 520l2 2c1 0 1-1 2-1h1v-1c1 0 2-1 3-1 1 1 2 1 4 1l1-1c0 1 1 1 2 2h0 2l-1 1c-1 0-2 0-2 1l-1-1-3-1-1 1 1 2c-1 1-1 2-2 3l1 1h1 0l1-1v1h0c1 1 1 2 1 3v1c0 1-1 1-1 3v1l2 2v-1l-1-1 1-1 1 1c1-1 1-1 1-2-1-1-1-1-2-1 0-2-1-3 0-4l-1-1 1-3h2v1c-1 1-1 1-1 2h1 1c1 1 2 2 3 2h1v-1c1 0 2 1 3 2l-1 2 2 1c1 1 1 3 1 4 0 2 0 3-1 4v6 5c0-2 0-4-1-6v1l-2 1-2-15c-1 3 0 6 0 9v6c-1 0-1-1-2-1s-2 1-2 2v2l1 1c0 1 0 1-1 2v1l2-1v2h-2v1 1l-1 1c1 0 1 1 2 1 0 1 0 3-1 4l-2 1-1 1h0c-1-1-1 0-1-1v-2l-1-1-1 1v3h0c-1-1-1 0-1-1h-1-2c0 1 0 1-1 2-1 0-1 0-2-1 0-1 0 0-1-1-1 2-1 3-1 6l-1 1c1 1 0 1 2 2h1c0 1 1 1 0 2h-1l-3-1c-6-2-14-1-20 0v-1-6-8-29-2c0-2-1-2-2-3l2-1c1-2 1-2 4-3l-2-1h-1v-1l3-1h3 0 7 8z" class="E"></path><path d="M470 569c2-2 2-3 3-5v10h-2s-1-1-2-1l1-4z" class="B"></path><path d="M453 526c2-1 4-1 5-1l1 1v4c1 1 1 2 1 3 1 2-1 5 1 8v2c0 3 0 10-1 12v13 1c-2-2-1-5-2-6v5h0 0l-1-3c-1-3 0-5 0-8v-11l-1-7c-1 1 0 4 0 6 0 4 1 10 0 14l-1 4 1 1v5l-1-1v-3h0c-1 3-1 6-1 9l-1 1v-6-8-29-2c0-2-1-2-2-3l2-1z" class="J"></path><path d="M456 559c-1-1 0-3-1-4s-1-8 0-10c0-3-1-8 1-10 1 1 1 2 1 3 1 2 0 5 0 8l-1-7c-1 1 0 4 0 6 0 4 1 10 0 14z" class="c"></path><path d="M453 526c2-1 4-1 5-1l1 1v4c1 1 1 2 1 3 1 2-1 5 1 8v2c0 3 0 10-1 12v13 1c-2-2-1-5-2-6-1-3 0-7 0-10 0-7 1-17-1-24h-1c-1-1-1-1-2-1l-1 2c0-2-1-2-2-3l2-1z" class="K"></path><path d="M459 526c2-1 5 0 7 0h2c1 0 3 0 4 1h1c1 0 2 1 3 2h-3v-1c-2 2-1 9-2 12v3c0 6 0 12-1 18v6 2l-1 4c-1 0-1 0-1-1h-2v-5l-1-1v5h-1c0-2 0-4-1-6 0 1 0 1-1 2 0 0-1 0-1-1v-3c0-4 1-17 0-20v-2c-2-3 0-6-1-8 0-1 0-2-1-3v-4z" class="X"></path><path d="M467 550c0 3-1 7 2 10 0-1 0-3-1-4-1-5 1-10 1-14 0-2 0-5 1-7 0 3 0 5 1 8 0 6 0 12-1 18v6c-1 1 0 1-2 1v-1c-2-4-1-12-1-17z" class="g"></path><path d="M463 565c1-3 0-7 0-10 2-2 1-10 1-13-1-2-1-6-1-7v-1c0-2-1-4 1-5l1 1 1-1 1 21c0 5-1 13 1 17v1c2 0 1 0 2-1v2l-1 4c-1 0-1 0-1-1h-2v-5l-1-1v5h-1c0-2 0-4-1-6z" class="K"></path><path d="M459 526c2-1 5 0 7 0h2c1 0 3 0 4 1h1c1 0 2 1 3 2h-3v-1c-2 2-1 9-2 12v3c-1-3-1-5-1-8 0-2 1-5 0-7h-4v1l-1 1-1-1c-2 1-1 3-1 5v1c0 1 0 5 1 7 0 3 1 11-1 13 0 3 1 7 0 10 0 1 0 1-1 2 0 0-1 0-1-1v-3c0-4 1-17 0-20v-2c-2-3 0-6-1-8 0-1 0-2-1-3v-4z" class="H"></path><path d="M475 520l2 2c1 0 1-1 2-1h1v-1c1 0 2-1 3-1 1 1 2 1 4 1l1-1c0 1 1 1 2 2h0 2l-1 1c-1 0-2 0-2 1l-1-1-3-1-1 1 1 2c-1 1-1 2-2 3l1 1h1 0l1-1v1h0c1 1 1 2 1 3v1c0 1-1 1-1 3v1l2 2v-1l-1-1 1-1 1 1c1-1 1-1 1-2-1-1-1-1-2-1 0-2-1-3 0-4l-1-1 1-3h2v1c-1 1-1 1-1 2h1 1c1 1 2 2 3 2h1v-1c1 0 2 1 3 2l-1 2 2 1c1 1 1 3 1 4 0 2 0 3-1 4v6 5c0-2 0-4-1-6v1l-2 1-2-15c-1 3 0 6 0 9v6c-1 0-1-1-2-1s-2 1-2 2c-1-1-1-1-1-2l-1-1c-1 1-1 1-2 1v1h-2 0l1-1-1-2h-3c-1 1-1 2-1 3l-1 1c-1 0-1 0-1-1h-1c-1 2-2 2-3 3-1 3 0 5 0 8 0 1-1 2-1 4-1 2-1 3-3 5v-2-6c1-6 1-12 1-18v-3c1-3 0-10 2-12v1h3c-1-1-2-2-3-2h-1c-1-1-3-1-4-1h-2c-2 0-5-1-7 0l-1-1c-1 0-3 0-5 1 1-2 1-2 4-3l-2-1h-1v-1l3-1h3 0 7 8z" class="M"></path><path d="M489 548v-4l2-1 1 1v4c-1 0-2 1-2 2-1-1-1-1-1-2z" class="G"></path><path d="M497 533l2 1c1 1 1 3 1 4 0 2 0 3-1 4v6 5c0-2 0-4-1-6v1c0-5 0-10-1-15z" class="H"></path><path d="M475 520l2 2c1 0 1-1 2-1h1v-1c1 0 2-1 3-1 1 1 2 1 4 1l1-1c0 1 1 1 2 2h0 2l-1 1c-1 0-2 0-2 1l-1-1-3-1-1 1 1 2c-1 1-1 2-2 3l1 1h1 0v2c-2 1-1-1-2 2 0 1-1 1-1 1h-1l-1 1c0 1 0 3 1 5v3h-1c-1 1-1 1-2 1l-1-1h0v-3-8-2h-1c-1-1-2-2-3-2h-1c-1-1-3-1-4-1h-2c-2 0-5-1-7 0l-1-1c-1 0-3 0-5 1 1-2 1-2 4-3l-2-1h-1v-1l3-1h3 0 7 8z" class="E"></path><path d="M455 522c4-2 14-2 18 0 2 1 3 1 4 3 0 2 1 2 0 4h-1c-1-1-2-2-3-2h-1c-1-1-3-1-4-1h-2c-2 0-5-1-7 0l-1-1c-1 0-3 0-5 1 1-2 1-2 4-3l-2-1z" class="Q"></path><path d="M499 624l1-1c0 1 0 2 1 4l-1 3h0c1-1 2-1 2-2v19 21c-1 5 0 12 0 18v12l-1 18v6 22 13 28c0 6 1 13 1 19 1 4 1 7 1 11 1 4 2 8 3 13v28c0 4-1 7 0 11h6l-2 1 2 1 1-1c2 1 2 1 2 3-1 0-1 1-2 0h-2c-2 2-1 7-1 9v1l2-1c0 1 1 2 2 2 0 2 0 3 1 4v1 22c1 1 2 0 3 0h3v1h2-1c-4 2-23 2-27 0l-1-1c-2-5-2-11-2-17 3 0 2-1 4-2 1-3 0-8 0-12v-5-1l-2 1c-1 1-1 2-1 4v-4h1v-3l-1 2h-3v-6l1-1-1-1h0c-2-4-1-9-1-13 0-1-1-3-1-4-1-2 0-4-1-6-1-4-1-8-1-12 0-7-1-14 0-21V664h1c-2-4-2-6-2-10 0-1 1-2 0-3v-1l1-2 5-14c2-3 6-7 7-10h0 1z" class="B"></path><path d="M497 850v14l-1 8-2 1v-3-5l1 1v3l1-1c0-6 0-12 1-18z" class="H"></path><path d="M486 648c1-1 1-1 2-1h0v2c0 2 0 12-1 15-2-4-2-6-2-10 0-1 1-2 0-3v-1l1-2z" class="D"></path><path d="M486 808V664h1c1 1 1 3 1 5 1 10 1 20 1 30v47 97c-2-7-3-15-3-23v-12z"></path><path d="M499 624l1-1c0 1 0 2 1 4l-1 3h0c1-1 2-1 2-2v19 21c-1 5 0 12 0 18v12l-1 18v6 22 13 28c0 6 1 13 1 19 1 4 1 7 1 11 1 4 2 8 3 13v28c0 4-1 7 0 11h6l-2 1 2 1 1-1c2 1 2 1 2 3-1 0-1 1-2 0h-2c-2 2-1 7-1 9v1l2-1c0 1 1 2 2 2 0 2 0 3 1 4v1 22c1 1 2 0 3 0h3v1h2-1c-4 2-23 2-27 0l-1-1c-2-5-2-11-2-17 3 0 2-1 4-2 1-3 0-8 0-12v-5-1l-2 1c-1 1-1 2-1 4v-4h1l2-1 1-8v-14l1-157v-45c0-7-1-15 0-22l1-2z" class="Y"></path><path d="M499 624l1-1c0 1 0 2 1 4l-1 3c-1 1-1 3-1 5l-1-9 1-2z" class="N"></path><path d="M501 838c1 3 1 6 1 9v13 3c0 1-1 2-2 4l1-29z" class="P"></path><path d="M506 867h6l-2 1 2 1 1-1c2 1 2 1 2 3-1 0-1 1-2 0h-2c-2 2-1 7-1 9h-1c0-2 1-5 0-7v-1-1c-1 1-1 0-1 1h-1-1v-5z" class="Q"></path><path d="M497 864c0 2-1 8 0 9 2-2 1-6 1-9l1 1c0 2-1 6 1 9l-1 9v8c-2-3 0-9-3-13v-5-1l-2 1c-1 1-1 2-1 4v-4h1l2-1 1-8z" class="Z"></path><path d="M501 838c0-4 0-7 2-10v-3h1v24c-1 4-1 8-1 12s1 8 0 11h0c-1-4-1-8-1-12v-13c0-3 0-6-1-9z" class="f"></path><path d="M503 872c1-3 0-7 0-11s0-8 1-12v1c1 6 0 12 0 18v5s1 1 2 1v3h0c1 0 1 0 1-2l1 1v5h1v-1h1v1l2-1c0 1 1 2 2 2 0 2 0 3 1 4v1 22c1 1 2 0 3 0h3v1h2-1c-4 2-23 2-27 0l-1-1c-2-5-2-11-2-17 3 0 2-1 4-2 1-3 0-8 0-12 3 4 1 10 3 13v-8l1-9v-7c1-2 2-3 2-4v-3c0 4 0 8 1 12h0z" class="T"></path><path d="M492 892c3 0 2-1 4-2v11c1 2 1 6 1 7-1 1-1 1-1 2h-1l-1-1c-2-5-2-11-2-17z" class="J"></path><path d="M509 880h1v1c1 3 1 6 1 9 0 6 1 13 1 20h-11l-1-7h1c0 2 0 5 2 6h0v-11l1 2c0 2-1 8 1 9h4v-18c0-3-1-7 0-9v-1-1z" class="S"></path><path d="M500 867c1-2 2-3 2-4v-3c0 4 0 8 1 12h0l1 28-1-2v11h0c-2-1-2-4-2-6h-1c-1-1-1-17-1-20l1-9v-7z" class="U"></path><defs><linearGradient id="f" x1="499.151" y1="894.141" x2="510.766" y2="889.239" xlink:href="#B"><stop offset="0" stop-color="#625f5c"></stop><stop offset="1" stop-color="#7b7a75"></stop></linearGradient></defs><path fill="url(#f)" d="M503 872c1-3 0-7 0-11s0-8 1-12v1c1 6 0 12 0 18v5s1 1 2 1v3h0c1 0 1 0 1-2l1 1v5h1v1c-1 2 0 6 0 9v18h-4c-2-1-1-7-1-9l-1-28z"></path><path d="M780 148h8c1 1 2 2 2 4 1 9 5 19-2 27v1h-1c-2-1-4-1-6-1-2 1-3 3-3 4v1c-1 1-4 4-5 4h0c-4 4-9 8-13 12 2 5 2 13 7 17l10 9 4 4h-1l-3-2h0c-1-1-2-2-3-2-1-1-2-3-3-3-1-1-2-1-2-2-2-2-6 1-8 1h-1c-4 1-11 1-14 0-1 0-2-1-3-1l-1 1 2 2c-1 0-2-1-3-2-4-2-9-5-12-7-2-1-6-3-8-5l-9-4c-9-5-19-8-28-12l-22-5-5-1 1-1c-1-7-1-15-1-23h2l1-1h22 1 15c2 0 5-1 7 0l1 1 1-1h7 20c0-1 1-1 2 0h11 16c2 2 1 10 0 13h0v-11l-1-1-1 1v16l6-9 1 2c4-5 5-10 8-15 0-1 1-3 2-5l2-5v-1z" class="D"></path><path d="M734 163c0-1 1-1 2 0l-1 20c0 6 1 13 0 19l-1 1h0v-40z" class="R"></path><defs><linearGradient id="g" x1="749.308" y1="223.916" x2="745.329" y2="203.699" xlink:href="#B"><stop offset="0" stop-color="#5f5c59"></stop><stop offset="1" stop-color="#868179"></stop></linearGradient></defs><path fill="url(#g)" d="M743 205l16-5c1 5 2 10 4 14h1c2 6 9 9 13 14h0c-1-1-2-2-3-2-1-1-2-3-3-3-1-1-2-1-2-2-2-2-6 1-8 1h-1c-4 1-11 1-14 0-1 0-2-1-3-1l-1 1 2 2c-1 0-2-1-3-2-4-2-9-5-12-7-2-1-6-3-8-5l1-1c5 3 16-2 21-4z"></path><path d="M722 209c5 3 16-2 21-4-1 2-2 2-3 2l1 1c-4 2-10 4-15 4 1 0 3 2 3 3-2-1-6-3-8-5l1-1z" class="b"></path><path d="M737 164h23v19l-9 8c-4 4-10 7-14 11v-38z" class="W"></path><defs><linearGradient id="h" x1="769.497" y1="191.53" x2="757.546" y2="168.515" xlink:href="#B"><stop offset="0" stop-color="#5a5856"></stop><stop offset="1" stop-color="#7c7770"></stop></linearGradient></defs><path fill="url(#h)" d="M780 148h8c1 1 2 2 2 4 1 9 5 19-2 27v1h-1c-2-1-4-1-6-1-2 1-3 3-3 4v1c-1 1-4 4-5 4h-1c-4 2-7 7-11 9-5 3-11 5-16 7-7 2-13 4-20 5 4-2 8-3 11-5 4-3 8-6 13-9 1 0 3-2 4-2 3-3 9-7 11-11 1-3 3-6 4-8 4-5 5-10 8-15 0-1 1-3 2-5l2-5v-1z"></path><defs><linearGradient id="i" x1="777.308" y1="162.457" x2="782.456" y2="160.44" xlink:href="#B"><stop offset="0" stop-color="#6d685e"></stop><stop offset="1" stop-color="#807c75"></stop></linearGradient></defs><path fill="url(#i)" d="M780 148h8c1 1 2 2 2 4l-3 3-1-1v1l-1 1v-2c-1 1-2 3-2 5-1 1-1 3-2 4 0 1-1 2 0 3l-2 3h-1-1-1c1-2 1-3 1-4v-1h1v-1-1-3h-1l-1 1v-1c0-1 1-3 2-5l2-5v-1z"></path><path d="M780 148h8c0 2-2 3-4 4s-3 1-5 2h-1l2-5v-1z" class="W"></path><path d="M776 159v1l1-1h1v3 1 1h-1v1c0 1 0 2-1 4h1 1 1c-1 2-3 3-4 5-3 4-7 8-11 12l-6 6-1-1-3 3h-1v-1c3-3 9-7 11-11 1-3 3-6 4-8 4-5 5-10 8-15z" class="V"></path><path d="M660 163h22 1 15c2 0 5-1 7 0l1 1 1-1v29l1 11h1v-39h23v40l-10 5-1 1-9-4c-9-5-19-8-28-12l-22-5-5-1 1-1c-1-7-1-15-1-23h2l1-1z" class="W"></path><path d="M707 163v29-1-1c-1-2-1-3-1-5v-11-2c-1-2 0-6 0-8l1-1zm-47 0h22 1v17c0 4 0 8 1 12v2l-22-5-5-1 1-1c-1-7-1-15-1-23h2l1-1z" class="R"></path><path d="M660 163h22 0c0 2 0 2-1 4 0 3 1 6 1 9l-1 1c-2-2-1-9-1-11 0-1 1-1 0-2h-7-11v19c0 2 1 4 0 6l-5-1 1-1c-1-7-1-15-1-23h2l1-1z" class="I"></path><path d="M657 164h2c0 7 1 16 0 23h-1c-1-7-1-15-1-23z" class="L"></path><path d="M521 488c1 0 2-1 2-1v-6c0-8-1-16 0-23 0-2 0-4 1-5 0 1 0 2 1 4v-1-1l1 1h1 1 2 1c1 0 4-1 5 1h-5-4c-1 4 0 10 0 14l1 2v-14l1-1c1 3 0 7 0 10v21l3 1v1l1 1-1 1v1h1l3 3v3 2 5 1 1c-1 1 0 3 0 5v13 1 1l1 1 1-1h-1l1-1 1 1v1l1 1v-1-1l1-2c1 0 1 1 2 1 1 1 2 1 2 1l3-1c1 1 1 1 0 2v4c0 4 0 9-1 12 0 2 1 3 1 4l1 1c-1 2-1 4-1 6-1 2 0 3 0 5 0 0 0 1-1 2 1 1 2 2 2 3 0 2 0 4 1 5 0 1 0 1-1 2l1 1c-2 0-5 1-7 1l-1 3c1 3 1 7 1 10v2l-1 1 1 1v2h-1c0 1 0 1-1 2l3 4v8 3c0 4-1 8 0 12h0c0 3 0 6-1 8-1 4 1 10-1 13-1 2 0 9 0 11 0 1-1 3-1 4h-1l-1-1v-1c-4-4-6-10-8-15-1-5-2-9-5-14v-3h-1l-2-5-1 1v1h0l-1-1c-1 2-1 4-1 6h-2 0v12 5 10h-1-3c-2 2-6 0-8 1h-1 0 0l-2-1c1 0 1 0 1-1h2l-1-1-2-2v-1l-1-3v-19c0 1-1 1-2 2h0l1-3c-1-2-1-3-1-4v-1c0-1 0-2 1-4h0c-1-2-1-4-1-5 0-3 0-6 1-9h1l4-24v-3-3c-1-4 1-12-1-15l-1 1v-1l1-1h4 0c3 0 5-1 8 0h2v-2-1c-4 0-8-1-12 0-2 0-6 1-7-1v-1l-1 1h0v-1-5c0 2 0 3 2 4 1 0 1-2 3-3l1 2h1v-1l-1-1c0-2-1-6 0-7 1 0 1 0 1 1h0 1v-1-1-3h-1l1-2h1l-1-1 1-1c1-1 0-1 1-1s2 0 3-1v-1c0-1 0-1 1-1s2 0 3 1h1c1 0 1-1 3-1v-1-4-7h0v-3c1-2 1-4 1-6v-8c1-2 1-3 1-4-1-2-1-7-1-9z"></path><path d="M547 546c0 2 1 3 1 4l1 1c-1 2-1 4-1 6-1 2 0 3 0 5 0 0 0 1-1 2 1 1 2 2 2 3 0 2 0 4 1 5 0 1 0 1-1 2h-2c0-4-1-8 0-11 0-5-1-11-1-17h1z" class="K"></path><path d="M546 546v-1l1-1c0-2-1-3-2-4-1 1 0 2 0 3s-1 1-2 2c-1 0-3 0-4-1h-1c-1 0 0 0-1-1 1-2 1-3 3-5l1 1v-1c1-1 2-1 4-1l1 1v-1-4h1l1 1c0 4 0 9-1 12h-1z" class="B"></path><path d="M529 604l1 1 9 53c-4-4-6-10-8-15-1-5-2-9-5-14v-3h-1c1-3 0-3-1-5l1-1 1 1c0 1 0 1 1 2h0c1 2 2 3 3 4 1 3 2 7 2 10l1-1v-1h1l-1-6c-1-3-1-7-2-11 0-2 0-4-1-5 0-3-1-6-1-9z" class="N"></path><path d="M542 579v-1c-1 1-4 1-5 1-1-1-1-1-1-2h1v-1h0-1c-1 1-1 2-2 3-1 0-2 0-4-1v-2-3c1-1-1-3 0-4v-3c-1-1-1-3-1-4l2-2c-1 0 0 0-1-1h-1c0-2 0-2 1-4 1 1 1 1 1 3h1c0-1 0-1 1-2h1v1 1 2h1v-1-2l1-1h0v2h1c0-1 0-2 1-2h0 0c0 1 1 2 1 2v3-1l1-1-1-2h0l1-1 1 2v1c0 1-1 2-2 3s-1 1-1 2c1 0 1 0 1 1v1h1l1 1c1-1 1 0 1-1h1v1c0 1 1 5 0 7v2l-1 3z" class="C"></path><path d="M521 488c1 0 2-1 2-1v-6c0-8-1-16 0-23 0-2 0-4 1-5 0 1 0 2 1 4v-1-1l1 1h1 1 2 1c1 0 4-1 5 1h-5-4c-1 4 0 10 0 14l1 2v-14l1-1c1 3 0 7 0 10v21 5l1 1-1 1v1c2 0 2 0 4 1v1h0c-2-1-2-1-3-1-2 3-1 10-1 12h1 1v2h-2l1 1h1l-1 1h-1c1 1 0 1 1 1-1 2-1 4-1 7 1 0 1-1 2 1l-1 1s0 1 1 1l-1 1v2h-1v-3l-1 1v4h1l2 3h-1 0l-2 2-1-1v4l1-2h0c1 1 1 1 1 2l1 1-2 1-1-2v1 3 1l1 1h-1l1 2h-1c-2 1-1 5-1 7l1 2-1 1-1 3 5 46-1-1-2-8-2-10c-2-6-2-12-2-18-1-4-1-8-2-12l1-4c-1 0-1-1-2-1l-1-2s0-1 1-1l1-1v-10-6c-1-1-1-1-1-2v-4-7h0v-3c1-2 1-4 1-6v-8c1-2 1-3 1-4-1-2-1-7-1-9z" class="K"></path><path d="M528 456h2 1c1 0 4-1 5 1h-5-4c-1 4 0 10 0 14v23h-1l-1-28c0-3 0-7 1-10h1 1z" class="L"></path><path d="M522 552l1 1-1 1 3 18v2-3c-1-4-1-8-1-12 0-3 1-6-1-9h1c2 3 1 6 1 9l5 46-1-1-2-8-2-10c-2-6-2-12-2-18-1-4-1-8-2-12l1-4z" class="Y"></path><path d="M520 529c0 1 0 1 1 2v6 10l-1 1c-1 0-1 1-1 1l1 2c1 0 1 1 2 1l-1 4c1 4 1 8 2 12 0 6 0 12 2 18l2 10 2 8c0 3 1 6 1 9 1 1 1 3 1 5 1 4 1 8 2 11l1 6h-1v1l-1 1c0-3-1-7-2-10-1-1-2-2-3-4-1-4-3-7-4-11l-1-1c-2-6-2-13-3-19 0-2 0-2-1-4v-1h-1v1c1 3 1 6 1 9v1l-4-23v-2l-1 1h-7 0c-1-4 1-12-1-15l-1 1v-1l1-1h4 0c3 0 5-1 8 0h2v-2-1c-4 0-8-1-12 0-2 0-6 1-7-1v-1l-1 1h0v-1-5c0 2 0 3 2 4 1 0 1-2 3-3l1 2h1v-1l-1-1c0-2-1-6 0-7 1 0 1 0 1 1h0 1v-1-1-3h-1l1-2h1l-1-1 1-1c1-1 0-1 1-1s2 0 3-1v-1c0-1 0-1 1-1s2 0 3 1h1c1 0 1-1 3-1v-1z" class="L"></path><path d="M515 566c1 1 1 1 2 1 0 0 1 1 2 1 1 5 0 10 0 14l-3-6v-1l-1-9z" class="D"></path><path d="M509 558c3 0 5-1 8 0h2c-1 3-1 7 0 10-1 0-2-1-2-1-1 0-1 0-2-1v-6l-1 1h-4l-1-3h0z" class="C"></path><path d="M507 542c1-1 2-1 3-2h1 0l-1 2-1 1v2l-1 1h0c-1-1-1-1-2 0v3c2 1 6 0 9 0h1v2h4c1 0 1 1 2 1l-1 4h0v3c-1-2-1-4 0-6h-21l-1 1h0v-1-5c0 2 0 3 2 4 1 0 1-2 3-3l1 2h1v-1l-1-1c0-2-1-6 0-7 1 0 1 0 1 1h0 1v-1z" class="E"></path><path d="M514 561l1-1v6l1 9v1c0 2 0 3 1 5 0 1 0 0 1 1 0 2 1 4 1 6 2-5 2-11 2-17 1 4 1 7 2 10-1 1-1 2-1 2 1 1 0 2 0 3v8c-1-2-1-3-2-4h-1v2c0-2 0-2-1-4v-1h-1v1c1 3 1 6 1 9v1l-4-23v-2-12z" class="N"></path><path d="M509 558l1 3h4v12l-1 1h-7 0c-1-4 1-12-1-15l-1 1v-1l1-1h4z"></path><path d="M509 558l1 3c-1 0-2 1-3 2 0 1 0 2 1 4 0 1 0 3-1 4v1l-1 2h0c-1-4 1-12-1-15l-1 1v-1l1-1h4z" class="B"></path><path d="M523 581l6 30v5l4 19v1l-1 1c0-3-1-7-2-10-1-1-2-2-3-4-1-4-3-7-4-11l-1-1c-2-6-2-13-3-19v-2h1c1 1 1 2 2 4v-8c0-1 1-2 0-3 0 0 0-1 1-2z" class="Y"></path><path d="M519 592v-2h1c1 1 1 2 2 4 0 2 1 4 0 7v2c1 1 0 2 0 3h0c2 3 2 6 3 9l2 4c0-3-1-6-2-10h1c1 1 1 1 1 2 0 2 1 3 1 4l1 1 4 19v1l-1 1c0-3-1-7-2-10-1-1-2-2-3-4-1-4-3-7-4-11l-1-1c-2-6-2-13-3-19z" class="c"></path><path d="M520 529c0 1 0 1 1 2v6 10l-1 1c-1 0-1 1-1 1l1 2h-4v-2h-1c-3 0-7 1-9 0v-3c1-1 1-1 2 0h0l1-1v-2l1-1 1-2h0-1c-1 1-2 1-3 2v-1-3h-1l1-2h1l-1-1 1-1c1-1 0-1 1-1s2 0 3-1v-1c0-1 0-1 1-1s2 0 3 1h1c1 0 1-1 3-1v-1z" class="C"></path><path d="M512 532v-1c0-1 0-1 1-1s2 0 3 1v1c1 0 1 1 2 1 0 1 0 1 1 2l-2 2c-2-1-3-3-5-5z" class="G"></path><path d="M510 542l1 1c0-1 1-2 2-2h3 1l1-1h0l-1-1v1c-2 0-2 0-3-2l1-1 2 1c1 0 2-1 3-2v7 5c-1 0-1 1-1 1l1 2h-4v-2h-1c-3 0-7 1-9 0v-3c1-1 1-1 2 0h0l1-1v-2l1-1z" class="B"></path><path d="M514 573v2l4 23v-1c0-3 0-6-1-9v-1h1v1c1 2 1 2 1 4 1 6 1 13 3 19l1 1c1 4 3 7 4 11h0c-1-1-1-1-1-2l-1-1-1 1c1 2 2 2 1 5l-2-5-1 1v1h0l-1-1c-1 2-1 4-1 6h-2 0v12 5 10h-1-3c-2 2-6 0-8 1h-1 0 0l-2-1c1 0 1 0 1-1h2l-1-1-2-2v-1l-1-3v-19c0 1-1 1-2 2h0l1-3c-1-2-1-3-1-4v-1c0-1 0-2 1-4h0c-1-2-1-4-1-5 0-3 0-6 1-9h1l4-24v-3-3h0 7l1-1z"></path><path d="M514 573v2 1c-1 0-1 1-1 2v1h0c-2 0-2-1-3-2h-1l1 2h0c-1 0-3-1-4-2v-3h0 7l1-1z" class="F"></path><path d="M513 630h-1-2-1c-2 0-4 0-6-1v-1c2 0 3-1 4-1s2 1 3 1h5l2 1-1 1c-1 1-2 1-3 0z" class="Z"></path><path d="M500 623v-1c0-1 0-2 1-4h0c-1-2-1-4-1-5 0-3 0-6 1-9h1v9 15c0 1-1 1-2 2h0l1-3c-1-2-1-3-1-4z" class="O"></path><path d="M504 641c2 0 3 0 4 1 2-1 6 0 8 0l1 1v1 3l-1 1h-12l-1-1c1-3 4-1 6-2v-1h-1-1c-2 0-3-1-4-2l1-1z" class="S"></path><path d="M518 598v-1c0-3 0-6-1-9v-1h1v1c1 2 1 2 1 4 1 6 1 13 3 19l1 1c1 4 3 7 4 11h0c-1-1-1-1-1-2l-1-1-1 1c1 2 2 2 1 5l-2-5-1 1v1h0l-1-1c-1 2-1 4-1 6h-2 0v-1-12c-1 1-3 1-5 0 1 0 1-1 1-2h-1v-1c1 1 1 0 1 1h1c1 0 1 0 2 1h1 0v-16z" class="g"></path><path d="M518 628c1-2 0-5 1-7v6-7c1-1 1 0 1-1v-4h1v1c0 1 1 3 1 3v2h1l-1 1v1h0l-1-1c-1 2-1 4-1 6h-2z" class="N"></path><path d="M515 628l3-1v1 12 5 10h-1-3c-2 2-6 0-8 1h-1 0 0l-2-1c1 0 1 0 1-1h2l-1-1-2-2v-1c4 0 11 1 14 0v-2h-1l1-1v-3-1l-1-1c-2 0-6-1-8 0-1-1-2-1-4-1h2 2l-1-1h-1c-2 0-2-1-3-2 1-1 2-1 3-1h0c0-2-3-3-4-3 2-2 3-3 5-3v1h1l4-1 1-1c1 1 2 1 3 0l1-1-2-1z" class="E"></path><path d="M512 631v2h4c0 1 1 1 0 2l-3 1c-1 0-1-1-2-1-1-1-1-1-2-1l-1 3c1 0 2 1 3 0 2 0 4 0 6 1v1h-5c-2 0-3 0-5 1h0-1c-2 0-2-1-3-2 1-1 2-1 3-1h0c0-2-3-3-4-3 2-2 3-3 5-3v1h1l4-1z" class="Z"></path><path d="M374 832c16-13 30-31 37-49l1-1c6 12 16 21 28 27 6 3 13 6 20 7h0c2 2 4 1 7 2h3c0 1 0 0-1 1v1c-1 1-1 2-2 3l-2 3-4 5v1l-5 7-4 5 1-2c0-2 0-2-1-3l-10 13-8 11c-2 2-3 4-4 5h-35-55-16l-11 1c-2-1-3 0-5-1-1-1-2 0-3 0l-1-1c2 0 3-1 4-1 25-6 47-18 66-34z" class="E"></path><path d="M374 832c5 0 9 0 12 2 2 1 3 3 5 3-2 2-6 3-8 5l1 1c-18 12-40 18-61 22-5 0-10 2-15 1 25-6 47-18 66-34z" class="R"></path><defs><linearGradient id="j" x1="455.219" y1="827.511" x2="437.862" y2="797.922" xlink:href="#B"><stop offset="0" stop-color="#22201e"></stop><stop offset="1" stop-color="#6e6d69"></stop></linearGradient></defs><path fill="url(#j)" d="M401 828c0-1 1-2 2-2 4-4 6-8 10-11 2-4 7-7 11-9l1-1c2-1 0-2 0-4 3 1 6 4 9 6 2 1 3 2 6 2 6 3 13 6 20 7h0c2 2 4 1 7 2h3c0 1 0 0-1 1v1c-1 1-1 2-2 3l-2 3-4 5v1l-5 7-4 5 1-2c0-2 0-2-1-3l1-1c0-1 0-2-1-2v-1c0-1 0-1-1-2v-1c0-1-1-2-1-3-1 0-1-1-1-1 0-2 0-3 1-4-1 0-4-2-5-3-1 0-1-1-2-1s0 0-1-1l-3-2c-4-2-9-4-11-7-3 0-14 11-17 14-4 3-9 7-14 10 0-3 2-3 4-6z"></path><path d="M450 824l3 1 1 3c-2 1-3 1-4 1s-1-1-1-1c0-2 0-3 1-4z" class="B"></path><defs><linearGradient id="k" x1="411.194" y1="811.556" x2="413.299" y2="828.884" xlink:href="#B"><stop offset="0" stop-color="#c4c8c1"></stop><stop offset="1" stop-color="#e6dcd9"></stop></linearGradient></defs><path fill="url(#k)" d="M401 828c5-3 10-8 15-13v-1c-1 1-2 1-3 2 2-2 4-5 7-6h0l1-1c2-1 4-2 5-3 1 1 2 2 2 3v1c-3 0-14 11-17 14-4 3-9 7-14 10 0-3 2-3 4-6z"></path><path d="M454 828c-1 1-1 2-1 4h1 1v-4-1h0c1-1 2 1 4 1h0l2-2-1-2c1-1 2-2 4-3 0 0 1 0 2-1l3-2v1 1c-1 1-1 2-2 3l-2 3-4 5v1l-5 7-4 5 1-2c0-2 0-2-1-3l1-1c0-1 0-2-1-2v-1c0-1 0-1-1-2v-1c0-1-1-2-1-3 1 0 2 0 4-1z" class="M"></path><defs><linearGradient id="l" x1="424.337" y1="802.989" x2="372.804" y2="819.374" xlink:href="#B"><stop offset="0" stop-color="#a69b89"></stop><stop offset="1" stop-color="#f0e2c9"></stop></linearGradient></defs><path fill="url(#l)" d="M374 832c16-13 30-31 37-49l1-1c6 12 16 21 28 27-3 0-4-1-6-2-3-2-6-5-9-6 0 2 2 3 0 4l-1 1c-4 2-9 5-11 9-4 3-6 7-10 11-1 0-2 1-2 2-2 3-4 3-4 6-3 3-7 5-10 7-1 1-2 2-3 2l-1-1c2-2 6-3 8-5-2 0-3-2-5-3-3-2-7-2-12-2z"></path><path d="M450 346v-2c2 1 2 2 2 3 0 2 0 3 1 5 0 1 0 1-1 2l1 2v1c1-1 1-2 1-3h1c0 1 1 1 1 2v4l-1 2h1c1-1 2-2 2-3l1-1h1l1-1c2 2 3 4 6 6 1 1 5 3 6 4l-1 1 1 1s1 1 1 2v1c1 2 1 7 1 10 0 1 1 3 1 5 0 1 1 2 2 3v20h0l-1 24 4-9 6-11 1 1 1-1h0l1-2c0-1 1-1 2-2v1l1-2 1-1c0-1 0-1 1-2 1 1 1 2 2 3v-1c0-1 0-2 1-4 1-1 2-2 3-2h2v2c-1 4-2 7-2 11-1 2-1 4-1 6-2 3-2 5-3 9l1 9v3l1 1 1 21v4 9h1c1 3-1 9-1 12v18 5c0 1 1 1 1 1h-1c0 1-1 1-1 1v4 14 2l-2-1 1-2c-1-1-2-2-3-2v1h-1c-1 0-2-1-3-2h-1-1c0-1 0-1 1-2v-1h-2l-1 3 1 1c-1 1 0 2 0 4 1 0 1 0 2 1 0 1 0 1-1 2l-1-1-1 1 1 1v1l-2-2v-1c0-2 1-2 1-3v-1c0-1 0-2-1-3h0v-1l-1 1h0-1l-1-1c1-1 1-2 2-3l-1-2 1-1 3 1 1 1c0-1 1-1 2-1l1-1h-2 0c-1-1-2-1-2-2l-1 1c-2 0-3 0-4-1-1 0-2 1-3 1v1h-1c-1 0-1 1-2 1l-2-2h-8-7 0-3l-3 1 2-6c-2-1-2-2-2-3l-1 1v-20c-1-4 0-9 0-13v-15c0-3 1-16 0-18l-1 1s-1-1-1-2c-1-2 1-4-1-6v-1c1-2 1-3 0-5-1 1-1 1-2 1s-2 1-3 1h0l-1-1v-2c0-3 0-5-1-8h0c1-1 1-1 2-3l1 1c0-3 1-4 3-5h-5c1-2 2-2 3-3h1v-1h-1v-2h-1v-1h3v-5-12-7-1l-1-1h-1v-1h6l-1-2c-1-1-1-2-1-3v-7c0-1 0-2 1-3l1-1v-2l-2-1c-2-3-1-9-1-13v-4-2z" class="G"></path><path d="M462 388h0c1 2 2 2 3 3l1 1h0-1c-1 0-2-1-2-1-2-2-5-6-7-6h-1c-1-2-1-3-1-5 0-1 0-3 1-4h1v-2c0 2 0 7 1 9 1 1 2 3 3 3l2 2z" class="C"></path><path d="M456 374l1-2 2-1 2 2v3c0 2 0 3 2 5l4 3 1 1c0 1 1 1 2 2l1 1 2-2v-1l1 1c0 1 0 3-1 4s-2 1-3 1c1-1 1-1 2-1-1-1-1-1-2-1h-2c-1 0-2-1-2-1l-4-2h0c1 1 4 3 5 5l-5-3-2-2c-1 0-2-2-3-3-1-2-1-7-1-9z" class="T"></path><path d="M470 376c2 0 2 0 4 1v2l1 3c0 1 1 3 1 5 0 1 1 2 2 3v20h0c-1-2-1-4-1-5 1-5 1-9 0-13h-1c-1 0-1 1-2 1-3 0-5 0-7-2h0c-1-2-4-4-5-5h0l4 2s1 1 2 1h2c1 0 1 0 2 1-1 0-1 0-2 1 1 0 2 0 3-1s1-3 1-4l-1-1v1l-2 2-1-1c-1-1-2-1-2-2l-1-1h1c-1-1-1-1-1-2v-1h-1-1l-1-1 3-1c2 1 0 3 2 4 1-1 1-5 1-7z" class="P"></path><path d="M490 412c0-1 1-1 2-2v1l2 20-1 9c-3 0-9 1-12-1h0c0-3-1-4 1-7 1 2-1 5 1 7v-3-1c0-2 0-3 1-4v8h1c0-1 0-3 1-4v4h1c0-1 0-3 1-4v4h1v-1-1c-1-4 0-10 0-14v-5c0-1 1-2 1-2v-4z" class="C"></path><path d="M450 346v-2c2 1 2 2 2 3 0 2 0 3 1 5 0 1 0 1-1 2l1 2v1c1-1 1-2 1-3h1c0 1 1 1 1 2v4l-1 2h1c1-1 2-2 2-3l1-1h1l1-1c2 2 3 4 6 6 1 1 5 3 6 4l-1 1 1 1s1 1 1 2v1c1 2 1 7 1 10l-1-3v-2c-2-1-2-1-4-1 0-2 0-4 1-6l-1-1-2 1c-1 1-3 2-4 2v-2l-1 1h-1s0-1-1-1-2 0-3 1c-2 0-5 0-7 1 0-1 0-2 1-3l1-1v-2l-2-1c-2-3-1-9-1-13v-4-2z" class="L"></path><path d="M461 357c2 2 3 4 6 6 1 1 5 3 6 4l-1 1 1 1s1 1 1 2v1c1 2 1 7 1 10l-1-3v-2c-2-1-2-1-4-1 0-2 0-4 1-6l-1-1-2 1c-1 0-1 0-2 1l-1-1v-1l-2-2c1-1 1-1 3-1v-1l-2-1v-1c-3-4-3-1-6-1 1-1 2-2 2-3v-1l1-1z" class="K"></path><path d="M495 406c1 1 1 2 2 3v-1c0-1 0-2 1-4 1-1 2-2 3-2h2v2c-1 4-2 7-2 11-1 2-1 4-1 6-2 3-2 5-3 9l1 9v3l1 1 1 21v4 9h1c1 3-1 9-1 12v18 5c0 1 1 1 1 1h-1c0 1-1 1-1 1v4 14 2l-2-1 1-2c-1-1-2-2-3-2v1h-1c-1 0-2-1-3-2h-1-1c0-1 0-1 1-2v-1h-2l-1 3 1 1c-1 1 0 2 0 4 1 0 1 0 2 1 0 1 0 1-1 2l-1-1-1 1 1 1v1l-2-2v-1c0-2 1-2 1-3v-1c0-1 0-2-1-3h0v-1l-1 1h0-1l-1-1c1-1 1-2 2-3l-1-2 1-1 3 1 1 1c0-1 1-1 2-1l1-1h-2v-1l1-2c1-2 1-1 0-3l-2 1c-1 0-2 0-2-1l-1 2-1-1v-1-1h-1-2l-1 1-1-1c-1 1-1 1-2 1h-1v-4-9l-1-17v-2c1-3 1-7 1-11l-1-18v-8c0-3 0-6 1-8v-1l1 2-1 1 1 1c0-1 0-2 1-2h2 0c3 2 9 1 12 1l1-9-2-20 1-2 1-1c0-1 0-1 1-2z" class="H"></path><path d="M488 441h3c0 3 1 5 0 7l-2-1c-1-1 0-2 0-3 1-1 0-1 1-1-1-1-1-2-2-2z" class="G"></path><path d="M481 448c2 0 2 0 3 1h0c0 1 0 2 1 3v2c-2 0-2 0-4 1v-3-4z" class="C"></path><path d="M489 498h1c-1 1-1 1-1 2l1 1 1 3c-1 1-2 1-3 0h-2 1v-1c0-1-1-2-1-4 1 0 2-1 3-1z" class="B"></path><path d="M477 511h3v-1h-1v1l-1-1c0-2-1 0 0-2v-3c1-1 1-1 2-1 0-1 1-2 1-3l2 1 1-1v-2l1-1c0 2 0 3-1 5v2c-2 0-2 0-3 1v1l-1 1c1 1 9 0 11 0v1h-2v1h2v1l-1 2h-1l1-1c-1-1-2-1-3-1h0l-8 1-1 1h2c1 0 1 0 2 1l-1 1-1-1c-1 1-1 1-2 1h-1v-4z" class="C"></path><path d="M494 483c1 1 1 1 1 2h1v-3 1 14c0 2-1 4 0 6l-1 20v6 1h-1c-2-4-1-12 0-17-1-3-1-8 0-11v-19z" class="d"></path><path d="M494 513v-1c0-1 0-2 1-3 1 4-1 9 0 14v6 1h-1c-2-4-1-12 0-17z" class="I"></path><path d="M481 448v-7h7c1 0 1 1 2 2-1 0 0 0-1 1 0 1-1 2 0 3l2 1-1 1c0 1 0 1 1 2l1 1h-2l1 1h0c0 1 0 1-1 2v2 2h-1l-1-1h-1v-1h2c-1-1-1-1-2-1v-1h1v-1h-2v-12c-2 2-1 8-1 10-1-1-1-2-1-3h0c-1-1-1-1-3-1z" class="M"></path><path d="M496 437v18 28h0v-1 3h-1c0-1 0-1-1-2-1-4 0-9 0-13v-30c0 1 1 1 1 1l1-4z" class="X"></path><path d="M496 429v1 3c1-1 1-2 1-3h0l1 9v3l1 1 1 21-1-2c-1 1-1 3-1 4v12 3c0 1-1 1-2 2v-28-18-8z" class="P"></path><path d="M495 406c1 1 1 2 2 3v-1c0-1 0-2 1-4 1-1 2-2 3-2h2v2c-1 4-2 7-2 11-1 2-1 4-1 6-2 3-2 5-3 9h0c0 1 0 2-1 3v-3-1 8l-1 4s-1 0-1-1v-9l-2-20 1-2 1-1c0-1 0-1 1-2z" class="C"></path><path d="M494 412c1 1 2 0 3 1 0 2-1 4 1 5h0v-5l1-1c0 1 0 2 1 3-1 1-1 2-2 3v2l-1 2-2 1-1-11z" class="H"></path><path d="M500 415l1-10 2-1c-1 4-2 7-2 11-1 2-1 4-1 6-2 3-2 5-3 9h0c0 1 0 2-1 3v-3-1l-1-6 2-1 1-2v-2c1-1 1-2 2-3z" class="S"></path><path d="M492 411l1-2 1-1v4l1 11 1 6v8l-1 4s-1 0-1-1v-9l-2-20z" class="J"></path><path d="M485 460l3-1v1h4c0 4 1 15-1 17v1c2 4 0 6 0 10l1 1c0 2-1 3 0 5h-1l-1-1c0-1 1-2 0-2v-1l-1 2h-2c-1-1-1-1-1-3v-17-6c0-1-1-1-1-2v-4z" class="C"></path><path d="M488 460h4c0 4 1 15-1 17h-1l-1 2v3h0-1v-1-2-2c0-1 1-1 0-2v-2c1-5-1-9 0-13z" class="B"></path><path d="M496 483c1-1 2-1 2-2v-3-12c0-1 0-3 1-4l1 2v4 9h1c1 3-1 9-1 12v18 5c0 1 1 1 1 1h-1c0 1-1 1-1 1v4 14 2l-2-1 1-2c-1-1-2-2-3-2v-6l1-20c-1-2 0-4 0-6v-14h0z" class="Q"></path><path d="M496 503v-1-4c2 2 1 11 1 15 1 5 2 12 1 17v1c-1-1-2-2-3-2v-6l1-20z" class="H"></path><path d="M476 454h4l-1-2h2v3h-1-1l-1 1 1 1 1-1h1v3h4v1 4 14 8 6h-3v1h1 1v1 1 2h-3v-5-1h-2c-1-1-1-2 0-3h2v-2-1c-1 1-1 1-1 2h-1v-1-1h-3v-2c1-3 1-7 1-11l-1-18z" class="B"></path><path d="M476 454h4l-1-2h2v3h-1-1l-1 1 1 1 1-1h1v3l-1 1v-1c-1 0-1 1-2 2l1 1c0 3-1 8 0 12 1 2 0 6 1 9l-1 1c-1 0-2 0-3-1 1-3 1-7 1-11l-1-18z" class="K"></path><path d="M453 408v-4c2-1 4-2 6-2l1 1 1 1h5v5c-1 0-1 1-2 1l-1-1c-2 0-4 1-5 1v1h1 6c2 0 3 1 5 0 1 2 0 10 0 13 1 6 0 13 0 19l1 39 1 36v1l1-1c1 1 1 2 2 2h-8-7 0-3l-3 1 2-6c-2-1-2-2-2-3l-1 1v-20c-1-4 0-9 0-13v-15c0-3 1-16 0-18l-1 1s-1-1-1-2c-1-2 1-4-1-6v-1c1-2 1-3 0-5-1 1-1 1-2 1s-2 1-3 1h0l-1-1v-2c0-3 0-5-1-8h0c1-1 1-1 2-3l1 1c0-3 1-4 3-5h-5c1-2 2-2 3-3h1v-1h-1v-2c1 0 2 0 2 1s0 1 1 2h1c0-2 0-2-1-3l1-3h2v-1z" class="J"></path><path d="M458 411c-1 0-1 0-2 1-2-1-2-5-2-7l1-1h3v6 1z" class="W"></path><path d="M468 462h0v13c1-4 1-7 2-11 0 6 1 17-3 21l1-23z" class="X"></path><path d="M450 434l1-1h0l1 1c0 2 0 4 1 5v-6-1 61c-1-4 0-9 0-13v-15c0-3 1-16 0-18l-1 1s-1-1-1-2c-1-2 1-4-1-6v-1c1-2 1-3 0-5z" class="F"></path><path d="M470 424c1 6 0 13 0 19l1 39-1 7-1 3c-1 1-1 3-1 5h-1l-1-2v-4l1-6c4-4 3-15 3-21v-27c0-4-1-9 0-13z" class="N"></path><path d="M447 412c1 0 2 0 2 1s0 1 1 2h1c0-2 0-2-1-3l1-3h2v-1 24 1 6c-1-1-1-3-1-5l-1-1h0l-1 1c-1 1-1 1-2 1s-2 1-3 1h0l-1-1v-2c0-3 0-5-1-8h0c1-1 1-1 2-3l1 1c0-3 1-4 3-5h-5c1-2 2-2 3-3h1v-1h-1v-2z" class="S"></path><path d="M449 418l1-1h1c0 2-1 3-1 5 0 1 0 1 1 2h-1c-1-1-1-1-3-1v1l-1-1c0-3 1-4 3-5z" class="M"></path><path d="M471 482l1 36v1l1-1c1 1 1 2 2 2h-8-7 0-3l-3 1 2-6c1-4 1-8 1-12v-12h3 0 3 3v4l1 2h1c0-2 0-4 1-5l1-3 1-7z" class="U"></path><path d="M460 520c0-2 1-3 2-4h1v3h0c2 0 3 1 4 1h-7z" class="F"></path><path d="M457 491h3l1 1h0c-4 8 0 18-3 26v1c1 0 2 0 2 1h-3l-3 1 2-6c1-4 1-8 1-12v-12z" class="Z"></path><path d="M457 503c0 3 0 6 1 9 0 3-1 6-1 8l-3 1 2-6c1-4 1-8 1-12z" class="Y"></path><path d="M470 489l-1 11v18c-1 1-2 1-3 1-1-1-1-4-1-6 0-7 0-13-1-19 0-2-1-2-1-3h3v4l1 2h1c0-2 0-4 1-5l1-3z" class="O"></path><path d="M470 489l-1 11v4h-1c-1-3-2-6-2-9l1 2h1c0-2 0-4 1-5l1-3z" class="Y"></path><path d="M457 491v-77h4 2c2 0 2 0 4-1l1 1v48l-1 23-1 6h-3-3 0-3z" class="H"></path><path d="M460 425l1-10h1 1c1 2 1 3 2 5-1 1-1 2-1 4-1 1-2 1-4 1z" class="Z"></path><path d="M460 425c2 0 3 0 4-1 0-2 0-3 1-4 0 3-1 8 0 12h-1c0 1 0 1 1 2l-1 1 1 1-1 1 1 1-1 2 1 1v1c0 1-1 1-1 2h-1c1 1 1 1 1 2 1 1 1 2 1 2 0 2-1 3-1 4v1h0-1-2-1v-28z" class="T"></path><path d="M460 453h1 2 1 0v-1c0-1 1-2 1-4 0 2 0 7-1 9l1 1-1 1 1 1h-1v1h1v1c-1 1 0 1 0 2v8 3l-1 1c2 2 1 5 1 8-1 1-1 2-3 2h-1c-2-3-1-8-1-11v-22z" class="L"></path><defs><linearGradient id="m" x1="453.179" y1="299.316" x2="548.459" y2="207.405" xlink:href="#B"><stop offset="0" stop-color="#92887a"></stop><stop offset="1" stop-color="#fcebcc"></stop></linearGradient></defs><path fill="url(#m)" d="M484 159h0c0 1 1 2 0 3 2 1 2 1 4 0 1 0 3 1 5 1 1-1 1 0 3 0v1h4v1h1c2 0 3 1 4 2 1-1 2-1 3-1 2 1 1 2 2 4v13 2l-1 8c-1 7 0 16 0 23v46 45 14c0 2-1 5 0 7s0 4 1 5v18l1 2v30c0 3 0 8 1 10h1c3 1 8 5 10 8l-10-7c-7 0-10 1-15 6l-2 5-1 1c-1 1-1 1-1 2l-1 1-1 2v-1c-1 1-2 1-2 2l-1 2h0l-1 1-1-1-6 11-4 9 1-24h0v-20-24c-1-7 0-15 0-23v-48-2-16h-1v-1h-2-1c1-2 2-3 2-4-1-1 0-1-1-1-1 1-2 2-2 4v1h-1v-2c-1 1-1 1-2 1h0 0c1-5 1-10 1-14h1l2-2c1-2 2-3 1-5v-15-1c0-2-1-5-3-6h0l1-3c1 0 1 1 2 1l1-1c1-1 1-1 2-1 1-8 0-17 0-24 0-15 3-31 6-45z"></path><defs><linearGradient id="n" x1="468.747" y1="235.391" x2="484.731" y2="252.109" xlink:href="#B"><stop offset="0" stop-color="#181a14"></stop><stop offset="1" stop-color="#433e44"></stop></linearGradient></defs><path fill="url(#n)" d="M476 229c1-1 1-1 2-1v34c0 5 1 10 0 15h-1v-1h-2-1c1-2 2-3 2-4-1-1 0-1-1-1-1 1-2 2-2 4v1h-1v-2c-1 1-1 1-2 1h0 0c1-5 1-10 1-14h1l2-2c1-2 2-3 1-5v-15-1c0-2-1-5-3-6h0l1-3c1 0 1 1 2 1l1-1z"></path><path d="M471 261h1l1 1-1 1v1l1 1h1l2-1h1v4 8h-2-1c1-2 2-3 2-4-1-1 0-1-1-1-1 1-2 2-2 4v1h-1v-2c-1 1-1 1-2 1h0 0c1-5 1-10 1-14z" class="T"></path><path d="M474 265l2-1h1v4c-1 0-1 1-2 1h-1c0-1 0-2 1-3h0l-1-1z" class="Q"></path><defs><linearGradient id="o" x1="460.078" y1="370.098" x2="505.632" y2="348.908" xlink:href="#B"><stop offset="0" stop-color="#5f5b56"></stop><stop offset="1" stop-color="#9b9284"></stop></linearGradient></defs><path fill="url(#o)" d="M478 366c1-1 0-2 1-3 1-5 0-11 0-16v-31c0-3-1-7 0-10h1l1 2c0 1 1 1 1 2s0 2 1 2c2 3 4 8 5 11l3 6 3 6c1 3 3 6 5 9l3 5c1 1 2 3 2 3 2 1 2 0 3 2h1l1 22c2 3-1 11 1 15v-1-39l1 2v30c0 3 0 8 1 10h1c3 1 8 5 10 8l-10-7c-7 0-10 1-15 6l-2 5-1 1c-1 1-1 1-1 2l-1 1-1 2v-1c-1 1-2 1-2 2l-1 2h0l-1 1-1-1-6 11-4 9 1-24h0v-20-24z"></path><path d="M498 400l-2 5-1 1c-1 1-1 1-1 2l-1 1-1 2v-1c-1 1-2 1-2 2l-1 2h0l-1 1-1-1c2-6 7-10 11-14z" class="S"></path><defs><linearGradient id="p" x1="673.48" y1="134.039" x2="663.079" y2="205.655" xlink:href="#B"><stop offset="0" stop-color="#dac8b2"></stop><stop offset="1" stop-color="#f4ebd5"></stop></linearGradient></defs><path fill="url(#p)" d="M534 148c4-1 9 0 13 0h25 35 18 116 29c2 0 8-1 10 0v1l-2 5c-1 2-2 4-2 5-3 5-4 10-8 15l-1-2-6 9v-16l1-1 1 1v11h0c1-3 2-11 0-13h-16-11c-1-1-2-1-2 0h-20-7l-1 1-1-1c-2-1-5 0-7 0h-15-1-22l-1 1h-2c0 8 0 16 1 23l-1 1-23-1c-1 0-4 0-5-1-3-1-7 1-10 0-1-1-1-2-1-2l-1-3v1 1h-1-1c0 3 0 6-1 9l-1-2h0c0 2-2 3-3 4-1 2-4 6-6 7l-6 2 1 2-1 1 2-1v1c-7 5-12 14-15 21-1 2-2 6-3 7 0-3 0-6 2-8l-1-1v-1-3c-2 1-2 1-3 3 0 1 0 3-1 4l-1 5-1 1-5-9c-1-2-4-8-4-10l-2 3c0-1-1-1-1-2 0-2 0-3-1-4l-3-2h0c0 1 1 2 1 3l-2-1c-1 5-3 8-6 11-1-3-1-3-2-5h-2c-2-2-2-4-2-6l1-2c-2-3-3-4-4-7v-1l-5-24v-4c-1-6-3-13-4-20 0-2-1-4-2-6z"></path><path d="M579 173v1c-1 4-1 8-1 12-1 1-2 2-2 3-1-6-1-11 3-16z" class="V"></path><path d="M576 189c0-1 1-2 2-3l1 5v1c0 2 2 3 3 5 1 1 1 2 2 3h2l1 1-3 1c-4-3-7-9-8-13z" class="Q"></path><path d="M767 172l9-19c1-2 2-3 4-4l-2 5c-1 2-2 4-2 5-3 5-4 10-8 15l-1-2z" class="H"></path><path d="M553 168c3-1 6-2 9-4l27-11c3-1 7-3 11-3l-38 17 1 12c0 2 0 5 1 7 0 2 2 3 3 5l11 8c1 1 1 2 1 3h0c-6-4-11-7-15-13-1-3-2-5-2-7 0-4 1-10-1-14-1 1-2 1-4 2l1 1c0 1-2 5-3 6l-2 2h-4v-2l-1 1c0 1 0 2 1 4v1c0 1 1 2 1 3l3 4-1 1-1-1c0-2-2-3-2-5 0-1-1-1-1-2-1 2-2 3-3 4v1-4c0-2 0-2 2-3v-3c1-3 4-8 6-10z" class="F"></path><defs><linearGradient id="q" x1="566.299" y1="205.08" x2="554.934" y2="171.199" xlink:href="#B"><stop offset="0" stop-color="#787268"></stop><stop offset="1" stop-color="#b6aa98"></stop></linearGradient></defs><path fill="url(#q)" d="M553 179l2-2c1-1 3-5 3-6l-1-1c2-1 3-1 4-2 2 4 1 10 1 14 0 2 1 4 2 7 4 6 9 9 15 13h0c1 1 3 2 4 3l-2 1c-1 0-2 0-3-1l-1-1c-1 0-1 0-2-1s-1-1-2-1c1 1 1 1 1 2-1 1-1 1-3 1h-2l-1-1h-2c-1 0-3 0-4-1-2 0-3-1-4-3l-5-10-3-4c0-1-1-2-1-3v-1c-1-2-1-3-1-4l1-1v2h4z"></path><path d="M550 186c0-1-1-2-1-3v-1c-1-2-1-3-1-4l1-1v2h4c0 1 0 1 1 2v1 1c1 1 3 2 1 3l-3-1c0-1 0-1-1-2-1 4 1-2 0 2l-1 1z" class="J"></path><path d="M564 189c-1 0-2 0-3-1-2-1-2-2-3-4 0-2 0-5 2-7h1c0 2 0 3 1 5 0 2 1 4 2 7z" class="c"></path><path d="M553 190l5 10c1 2 2 3 4 3 1 1 3 1 4 1h2l1 1h2c2 0 2 0 3-1 0-1 0-1-1-2 1 0 1 0 2 1s1 1 2 1l1 1c1 1 2 1 3 1l2-1c-1-1-3-2-4-3 0-1 0-2-1-3l1-1c2 1 2 2 4 4 0 0 1 0 1 1v-1c1 0 1 0 2 1 5 1 8 1 13-1 2-1 3-2 5-1l-6 2 1 2-1 1 2-1v1c-7 5-12 14-15 21-1 2-2 6-3 7 0-3 0-6 2-8l-1-1v-1-3h0l1-1c-2 0-3-1-4-2-1-2-1-4-2-7v-1c1-1 1-1 1-2-5 0-10-2-15-3-1 0-4-1-6-1-1 1-2 2-4 3h-1v-4h-1c-1 0-2-1-2-2-1 0-1-1-1-1l-1-2 1-1c1 0 1 0 2-1v-3c1-1 1-2 1-2l1-1z" class="V"></path><path d="M553 207h1c2-1 3-2 4-3 2 0 5 1 6 1 5 1 10 3 15 3 0 1 0 1-1 2v1c1 3 1 5 2 7 1 1 2 2 4 2l-1 1h0c-2 1-2 1-3 3 0 1 0 3-1 4l-1 5-1 1-5-9c-1-2-4-8-4-10l-2 3c0-1-1-1-1-2 0-2 0-3-1-4l-3-2h0c0 1 1 2 1 3l-2-1c-1 5-3 8-6 11-1-3-1-3-2-5h-2c-2-2-2-4-2-6l1-2c1-2 3-2 4-3z" class="b"></path><path d="M552 218c2 0 2-1 4-3l1-1h-1l1-1-1-1c0-1 0-1-1-1v-1l1-1h1l3 3c-1 5-3 8-6 11-1-3-1-3-2-5z" class="S"></path><path d="M568 215c-3-3-6-6-8-9l2 1c2 1 4 2 6 4 3 0 4-1 6 1l2 2c2 1 2 3 4 4 1 1 2 2 4 2l-1 1h0c-2 1-2 1-3 3 0 1 0 3-1 4l-1 5-1 1-5-9c-1-2-4-8-4-10z" class="L"></path><path d="M572 215h1c1 2 2 4 4 5 1 1 1 2 1 4 1 1 1 2 1 4l-1 5c-1-1-2-2-2-3s0-2 1-2c0-2-1-4-1-5-1-1-1-2-2-3h0c-1-2-1-3-2-5z" class="O"></path><path d="M568 211c3 0 4-1 6 1l2 2c2 1 2 3 4 4 1 1 2 2 4 2l-1 1h0c-2 1-2 1-3 3 0 1 0 3-1 4 0-2 0-3-1-4 0-2 0-3-1-4-2-1-3-3-4-5h-1l-4-4z" class="a"></path><defs><linearGradient id="r" x1="683.509" y1="173.942" x2="684.571" y2="154.677" xlink:href="#B"><stop offset="0" stop-color="#e8dec9"></stop><stop offset="1" stop-color="#dbd9db"></stop></linearGradient></defs><path fill="url(#r)" d="M601 165c0-2-1-2 0-4s4-3 6-4v1l157 1c0 1 0 2-1 4h-16-11c-1-1-2-1-2 0h-20-7l-1 1-1-1c-2-1-5 0-7 0h-15-1-22-47v12l-2-3-2-3c-2-2-4-3-8-4z"></path><path d="M601 165c0-2-1-2 0-4s4-3 6-4v1 1h-1c-1 0 0 0-1 1v1h0c3 3 4 5 4 8-2-2-4-3-8-4z" class="I"></path><path d="M579 173c1-2 2-3 4-5 5-4 11-4 18-3 4 1 6 2 8 4l2 3h0v1h0c-1 0-2-1-3-2h0l-1-1 1 2c3 5 4 8 4 14-1 3-2 5-3 7l1 1c-1 2-4 6-6 7-2-1-3 0-5 1-5 2-8 2-13 1-1-1-1-1-2-1h0l3-1-1-1h-2c-1-1-1-2-2-3-1-2-3-3-3-5v-1l-1-5c0-4 0-8 1-12v-1z" class="Z"></path><path d="M590 170c6 0 10 0 14 3l1 1c-2-1-4-1-6-1l-3 2v1 1h-1l-2-1h-3v-4-2z" class="h"></path><path d="M590 172h3v4h-3v-4z" class="R"></path><path d="M581 186c-1-4 0-6 2-9 1-4 4-5 7-7v2 4h-1-2c-2 2-3 4-3 7v2c-1 1-1 1-3 1z" class="W"></path><path d="M581 186c2 0 2 0 3-1l1 3 3 1v-1l-1-1 1-1 4 4v3c2 0 3 1 4 2h-6c-1 0-2-1-3-2-3-1-5-3-6-7z" class="R"></path><path d="M591 184c2 1 3 1 4 1 2-1 2-2 2-3h1l1 2h1v-1h3 1c-1 3-1 4-2 6l-3 3c-2 1-5 0-7-2l-4-4-1-1h3l1-1z" class="F"></path><path d="M591 184c2 1 3 1 4 1 2-1 2-2 2-3h1l1 2h1v-1h3v1h-1c0 1 0 2-1 2-2 2-7-1-9 2l-1-1c0-1-1-1-1-2l1-1z" class="H"></path><path d="M590 176h3l2 1h1v-1c1 1 1 4 0 5-1 2-3 3-5 3l-1 1h-3l1 1-1 1 1 1v1l-3-1-1-3v-2c0-3 1-5 3-7h2 1z" class="J"></path><path d="M596 176v-1l3-2c2 0 4 0 6 1 1 2 1 4 3 7l1-1c0 2 1 3 0 4v1c0 2 0 2-1 3h-1-2v-5c-2-1-3-1-5-1v1 1h-1l-1-2h-1c0 1 0 2-2 3-1 0-2 0-4-1 2 0 4-1 5-3 1-1 1-4 0-5z" class="U"></path><path d="M600 182c-1-2 0-2 0-4 1-1 2-1 3-1 1 1 2 1 2 3v3c-2-1-3-1-5-1z" class="J"></path><path d="M579 173c1-2 2-3 4-5 5-4 11-4 18-3 4 1 6 2 8 4l2 3h0v1h0c-1 0-2-1-3-2h0l-1-1v1c-1 0-1 0-2-1-4-2-10-3-15-2-3 1-7 2-9 5l-1 2v1c-2 3-1 8 0 12l1 1c1 3 0 4 3 5l2 2c2 1 5 2 8 2l-1-1h3 1c1 0 1-1 2-1s3 0 3-1c1-1 1 0 1-1v-2l2-4h2 1c1-1 1-1 1-3v-1c1-1 0-2 0-4-1-2-1-3 0-5l-2-2 1-1c3 5 4 8 4 14-1 3-2 5-3 7l1 1c-1 2-4 6-6 7-2-1-3 0-5 1-5 2-8 2-13 1-1-1-1-1-2-1h0l3-1-1-1h-2c-1-1-1-2-2-3-1-2-3-3-3-5v-1l-1-5c0-4 0-8 1-12v-1z" class="I"></path><path d="M609 175c2 5 2 10 0 15-1 3-2 5-5 6s-6 2-10 2h0l-1-1h3 1c1 0 1-1 2-1s3 0 3-1c1-1 1 0 1-1v-2l2-4h2 1c1-1 1-1 1-3v-1c1-1 0-2 0-4-1-2-1-3 0-5z" class="P"></path><path d="M579 173c1-2 2-3 4-5 5-4 11-4 18-3 4 1 6 2 8 4l2 3h0v1h0c-1 0-2-1-3-2h0l-1-1v1c-3-3-7-5-10-5h-4c-5 0-9 2-12 5-1 1-1 2-2 3v-1z" class="S"></path><path d="M584 202l3-1-1-1h-2c-1-1-1-2-2-3-1-2-3-3-3-5v-1c2 3 3 4 6 6l1 1c2 2 5 3 8 3 6-1 11-4 15-8l1 1c-1 2-4 6-6 7-2-1-3 0-5 1-5 2-8 2-13 1-1-1-1-1-2-1h0z" class="H"></path><path d="M611 172l2 3v-12h47l-1 1h-2c0 8 0 16 1 23l-1 1-23-1c-1 0-4 0-5-1-3-1-7 1-10 0-1-1-1-2-1-2l-1-3v1 1h-1-1c0 3 0 6-1 9l-1-2h0c0 2-2 3-3 4l-1-1c1-2 2-4 3-7 0-6-1-9-4-14l-1-2 1 1h0c1 1 2 2 3 2h0v-1h0z" class="R"></path><path d="M611 172l2 3v-12h47l-1 1h-2-18v21 1h-4v-9-13h-18v5 12 1 1h-1-1c0 3 0 6-1 9l-1-2h0c0 2-2 3-3 4l-1-1c1-2 2-4 3-7 0-6-1-9-4-14l-1-2 1 1h0c1 1 2 2 3 2h0v-1h0z" class="H"></path><path d="M635 186l2-1h0c-1-3-2-19-1-21h2v5c0 5-1 12 1 17h-4z" class="h"></path><path d="M612 186v-4c1-2 2-2 1-4v-1-1h0c1 0 1 1 2 2v1 4c0 3 0 6-1 9l-1-2h0c0 2-2 3-3 4l-1-1c1-2 2-4 3-7z" class="M"></path><defs><linearGradient id="s" x1="570.796" y1="165.063" x2="566.357" y2="142.758" xlink:href="#B"><stop offset="0" stop-color="#aca28f"></stop><stop offset="1" stop-color="#d2c1ab"></stop></linearGradient></defs><path fill="url(#s)" d="M534 148c4-1 9 0 13 0h25 35 18c-7 1-14-1-20 1-2 1-4 1-5 1-4 0-8 2-11 3l-27 11c-3 2-6 3-9 4-2 2-5 7-6 10v3c-2 1-2 1-2 3v4-1c1-1 2-2 3-4 0 1 1 1 1 2 0 2 2 3 2 5l1 1s0 1-1 2v3c-1 1-1 1-2 1l-1 1 1 2s0 1 1 1c0 1 1 2 2 2h1v4c-1 1-3 1-4 3-2-3-3-4-4-7v-1l-5-24v-4c-1-6-3-13-4-20 0-2-1-4-2-6z"></path><path d="M546 168h1v2c0 1 0 2-1 2l1 1c1-1 2-2 2-3 1-2 3-3 5-4v1c-1 1-1 0-1 1-2 2-5 7-6 10h-2 0-1c0-2-1-3 0-5 1-1 2-3 2-4v-1z" class="N"></path><defs><linearGradient id="t" x1="541.137" y1="192.869" x2="548.314" y2="188.347" xlink:href="#B"><stop offset="0" stop-color="#363431"></stop><stop offset="1" stop-color="#51504c"></stop></linearGradient></defs><path fill="url(#t)" d="M536 154l1 1c2 4 3 8 4 12 0 2 0 4 1 6 1 0 1 0 1-1 1-1 2-3 3-4v1c0 1-1 3-2 4-1 2 0 3 0 5h1 0 2v3c-2 1-2 1-2 3v4-1c1-1 2-2 3-4 0 1 1 1 1 2 0 2 2 3 2 5l1 1s0 1-1 2v3c-1 1-1 1-2 1l-1 1 1 2s0 1 1 1c0 1 1 2 2 2h1v4c-1 1-3 1-4 3-2-3-3-4-4-7v-1l-5-24v-4c-1-6-3-13-4-20z"></path><path d="M551 190l1 1s0 1-1 2v3c-1 1-1 1-2 1 0-2 1-3 0-4v-1c1-1 1-2 2-2z" class="L"></path><path d="M545 202l1-3h0c0 1 1 1 1 2h1c0 1 0 1 1 2v-1l-1-2h1s0 1 1 1c0 1 1 2 2 2h1v4c-1 1-3 1-4 3-2-3-3-4-4-7v-1z" class="K"></path><defs><linearGradient id="u" x1="583.448" y1="396.515" x2="479.064" y2="192.29" xlink:href="#B"><stop offset="0" stop-color="#1d1d1c"></stop><stop offset="1" stop-color="#414040"></stop></linearGradient></defs><path fill="url(#u)" d="M510 161c2-2 1-7 1-9 1-1 2-2 3-2 2-1 3-2 4-2s1 0 2-1l4-2h2c1-1 2-1 4-2 0 1 0 2 1 3v1c1 1 1 1 1 3l2 2-1-3c1 0 1 0 1-1 1 2 2 4 2 6 1 7 3 14 4 20v4l5 24v1c1 3 2 4 4 7l-1 2c0 2 0 4 2 6h2c1 2 1 2 2 5-2 1-4 0-6 1 2 0 3 1 4 1 0 1-1 2-1 3-3 4-4 6-5 11-1 1-1 2-1 3v1l-1 3c-1 3-1 6-1 9 1 2 1 4 1 6-2 3-1 10-2 14v3l-1 7v23l1 121c0 1 0 2-1 4 0-1 0-1-1-2v-1-1l-2-4-1-2-2-4-1-1c0-1 0-1-1-2h0c-1-3-3-6-5-8-1-3-3-4-5-6v-1c-2-3-7-7-10-8h-1c-1-2-1-7-1-10v-30l-1-2v-18c-1-1 0-3-1-5s0-5 0-7v-14-45-46c0-7-1-16 0-23l1-8v-2-13-9z"></path><path d="M540 296v1l1 21c0-3-1-7 0-10l1 121c-1-2-1-2-1-4-1-6-1-12-1-17v-21-56-35z" class="F"></path><path d="M510 351v-18c-1-1 0-3-1-5s0-5 0-7v-14-45-46c0-7-1-16 0-23l1-8c0 1 0 3 1 4v164l-1-2z" class="N"></path><path d="M510 161c2-2 1-7 1-9 1-1 2-2 3-2 2-1 3-2 4-2s1 0 2-1l4-2h2c1-1 2-1 4-2 0 1 0 2 1 3v1c1 1 1 1 1 3l2 2-1-3c1 0 1 0 1-1 1 2 2 4 2 6 1 7 3 14 4 20v4l5 24v1c1 3 2 4 4 7l-1 2c0 2 0 4 2 6h2c1 2 1 2 2 5-2 1-4 0-6 1 2 0 3 1 4 1 0 1-1 2-1 3-3 4-4 6-5 11-1 1-1 2-1 3v1l-1 3c-1 3-1 6-1 9 1 2 1 4 1 6-2 3-1 10-2 14v3l-1 7v23c-1 3 0 7 0 10l-1-21v-1-42c0-9-1-20 0-29 1-10 1-20 0-30-1-4-3-24-5-26 0-1-1-2-1-3-2-1-7 3-10 4-2 1-3 3-5 4-3 1-5 1-8 3 0 2 0 3-1 5v1-13-9z" class="V"></path><path d="M540 296v-42c0-9-1-20 0-29l1 53c0 4-2 10 0 14v-7h0v23c-1 3 0 7 0 10l-1-21v-1z" class="U"></path><path d="M533 149c1 0 1 0 1-1 1 2 2 4 2 6 1 7 3 14 4 20v4l-1 1-4-20c0-3-1-5-1-7l-1-3z" class="D"></path><path d="M539 179l1-1 5 24v1c1 3 2 4 4 7l-1 2c0 2 0 4 2 6h2c1 2 1 2 2 5-2 1-4 0-6 1 2 0 3 1 4 1 0 1-1 2-1 3-3 4-4 6-5 11-1 1-1 2-1 3v1l-1 3c-1 3-1 6-1 9 1 2 1 4 1 6-2 3-1 10-2 14v3c-2-33 2-66-3-99z" class="M"></path><path d="M545 203c1 3 2 4 4 7l-1 2c-1 0-2-1-2-1l-1-8z" class="D"></path><path d="M518 628h2c0-2 0-4 1-6l1 1h0v-1l1-1 2 5h1v3c3 5 4 9 5 14 2 5 4 11 8 15v1c-1 0-2 1-2 1v1h1c1 1 2 1 2 2 1 4 1 10 1 15 0 2 1 4 0 6l1 11h1v-2l2-1v-1c1 1 2 1 3 2h0l2-1c1 0 2 1 4 0 2 0 5 0 7-1s4-1 5-1v2c-1 0-1 0-1 1-1 2-2 3-3 4h-1c-1 2-1 4-1 6l-1-1-1 1v2 1l1 1c2 1 4 4 6 6h1v1c-1 4 1 7-2 10l3 1c1 0 1 1 2 2h1v1c0-1 1-2 1-2 1-1 1-2 2-3l2 1h1c-1 1-1 2-1 3 1 2 2 3 2 6l-1 1c-2 2-1 5-2 8-2 0-3-2-4-3h-1c-1 0-2-1-4-1h-3v2h-1 0l-1 1h-2c-2 1-3 2-4 2-1 1-3 2-4 2-2 2-3 3-5 4l-1 1c-1 0-1 0-1-1-1 1-1 2-1 3v23 31 1h-2v18c0 3 0 6-1 8l-1 5v9c-1 1-1 3-1 4 0 3 0 5-1 8-1 1-1 2-1 3l-1 2c2 2 0 5 3 6v1h-1c-2 2-1 7 0 9 0 1 1 2 1 3-1 1-4 3-5 4-1 2-4 3-5 4v7c0 4 0 9-3 12h-1-2v-1h-3c-1 0-2 1-3 0v-22-1c-1-1-1-2-1-4-1 0-2-1-2-2l-2 1v-1c0-2-1-7 1-9h2c1 1 1 0 2 0 0-2 0-2-2-3l-1 1-2-1 2-1h-6c-1-4 0-7 0-11v-28c-1-5-2-9-3-13 0-4 0-7-1-11 0-6-1-13-1-19v-28-13-22-6l1-18v-12c0-6-1-13 0-18v-21l1 3v1l2 2 1 1h-2c0 1 0 1-1 1l2 1h0 0 1c2-1 6 1 8-1h3 1v-10-5-12h0z" class="G"></path><path d="M531 643c2 5 4 11 8 15v1c-1 0-2 1-2 1-1-1-1-2-1-3v-1c0-1-1-2-2-3v1c-1 1-1 3-1 4v1c0-1-1-3 0-4h0c0-2-2-5-3-6v2-3c0-2 0-3 1-5z" class="C"></path><path d="M534 654v-1c1 1 2 2 2 3v1c0 1 0 2 1 3v1h1c0 1-1 2 0 4 0 1 0 1 1 2v3h0v1 1c-1 3 0 7 0 10-1 5-1 9-1 13 0 6-1 11 0 17v15 51 20l-1 9c0 5 1 11 0 16l-2 22c-1 3-2 6-2 10-1-5 0-9 0-14 0-6 0-12 1-17v-17c1-3 1-7 1-11v-18-74-27c0-3-1-11 0-14h1c-1-1-1-1-1-2h-1v-7z" class="E"></path><path d="M534 654v-1c1 1 2 2 2 3v1c0 1 0 2 1 3v1h1c0 1-1 2 0 4 0 1 0 1 1 2v3h0v1 1c-1 3 0 7 0 10-1 5-1 9-1 13 0 6-1 11 0 17v15c-1-3-1-6-1-9-1-14 0-28 0-42 0-4-1-9-1-13l1-1c-1-1-2-1-3-1v-7z" class="F"></path><path d="M519 730h1c0 1 0 1 1 1l1-7 1 147c1 1 1 1 2 1s2-1 2-1c1-2 1-6 1-8h1 0l1 1c-1 2-1 5-1 7v1h3 0c2 0 2 0 4-1-2 2-1 7 0 9 0 1 1 2 1 3-1 1-4 3-5 4-1 2-4 3-5 4v7c0 4 0 9-3 12h-1-2v-1h-3c-1 0-2 1-3 0v-22-1c-1-1-1-2-1-4-1 0-2-1-2-2l-2 1v-1c0-2-1-7 1-9h2c1 1 1 0 2 0h1v-2c1-4 0-9 1-13v5h1v-6c0-11-1-21 0-32 0-1 0-1-1-1v-8-1c-1-2 0-4 0-6 0-7 1-14 1-21 1-4 0-9 0-13l1-43z" class="f"></path><path d="M523 889c0-2 1-9 0-11-1 0-1 0-1-1 0-2 0-7 1-9v2 1c1 1 1 1 2 1l2 1-1 1c1 5 0 12 1 17v7c0 4 0 9-3 12h-1-2v-1h1c1-6 1-14 1-20z" class="J"></path><path d="M527 898c-1-1-2-3-2-4l-1-8c0-4 0-9 2-12 1 5 0 12 1 17v7z" class="X"></path><path d="M525 872c1 0 2-1 2-1 1-2 1-6 1-8h1 0l1 1c-1 2-1 5-1 7v1h3 0c2 0 2 0 4-1-2 2-1 7 0 9 0 1 1 2 1 3-1 1-4 3-5 4-1 2-4 3-5 4-1-5 0-12-1-17l1-1-2-1z" class="F"></path><path d="M525 872c1 0 2-1 2-1 1-2 1-6 1-8h1 0l1 1c-1 2-1 5-1 7v1c-1 5 0 9-2 14 0-3 1-11 0-13l-2-1z" class="M"></path><defs><linearGradient id="v" x1="527.897" y1="893.962" x2="509.555" y2="872.077" xlink:href="#B"><stop offset="0" stop-color="#494642"></stop><stop offset="1" stop-color="#616160"></stop></linearGradient></defs><path fill="url(#v)" d="M518 855l2 24c1 7 1 13 1 20-1 1-1 3-1 4h1l1-4c1-4 0-8 0-12l1 2c0 6 0 14-1 20h-1-3c-1 0-2 1-3 0v-22-1c-1-1-1-2-1-4-1 0-2-1-2-2l-2 1v-1c0-2-1-7 1-9h2c1 1 1 0 2 0h1v-2c1-4 0-9 1-13v5h1v-6z"></path><path d="M520 903h1l1-4c1-4 0-8 0-12l1 2c0 6 0 14-1 20h-1-3c-2-3-1-10-1-14 1 2 2 5 2 7s0 4 1 6v-4-1z" class="Q"></path><path d="M510 880c0-2-1-7 1-9h2c1 1 1 0 2 0h1l-1 15c-1-1-1-2-1-4-1 0-2-1-2-2l-2 1v-1z" class="N"></path><defs><linearGradient id="w" x1="496.985" y1="693.96" x2="543.332" y2="664.841" xlink:href="#B"><stop offset="0" stop-color="#787371"></stop><stop offset="1" stop-color="#aeaea9"></stop></linearGradient></defs><path fill="url(#w)" d="M518 628h2c0-2 0-4 1-6l1 1h0v-1l1-1 2 5h1v3l-4-5v10 64 18 8l-1 7c-1 0-1 0-1-1h-1-1v-1-1l-2 1-1 1c-2 0-3-1-4-2v-1h-2-1v-1c1 0 1-1 2-1v-1h-7 0l-2-2v-6l1-18v-12c0-6-1-13 0-18v-21l1 3v1l2 2 1 1h-2c0 1 0 1-1 1l2 1h0 0 1c2-1 6 1 8-1h3 1v-10-5-12h0z"></path><path d="M518 628h2c0-2 0-4 1-6l1 1h0v-1l1-1 2 5h1v3l-4-5v10c-1-2-1-4-1-5h0l-1 2h0l-1-2c-1 1 0 4 0 6h0v1c-1 3 0 6-1 9h0v-5-12h0z" class="X"></path><path d="M502 686c0-6-1-13 0-18h3 0l-2 1v3c1 1 2 0 3 0h2c3 0 7 1 10-1v9c-2 0-3-1-5-1 0-1-1-1 0-3l3 2h1v-1c-4-1-8-1-12-1h-2v1h4 0c-1 1-2 2-4 1l-1 4v4h0z" class="C"></path><path d="M502 647l1 3v1l2 2 1 1h-2c0 1 0 1-1 1l2 1h0 0 1c2-1 6 1 8-1h3 1v5h-1-5-7-2v3c2 1 6 1 8 0h1c1 0 1 0 1-1h0l1 1c1 0 2 1 3 0h1v4h-3-1l2 2 2-1v2c-1 1-1 0-2 0-1-1-3-2-5-2-2 1-4 0-6 0h0-3v-21z" class="B"></path><path d="M518 655v5h-1-5l-1-4c3 0 3 0 5 2 1-1 1-2 1-3h1zm-16 27c6-1 11-1 16-1v6h-2-2l-1 1c-1-1-1-2-1-3-1-1-1-1-2-1v1l1 1-1 1-1-1 1-1c-2-1-4-1-6-1v1h3v1h-4v3c1 1 11 1 13 1 1 0 2-2 2-1v1 13h-1-2c-2-1-2-1-3-1l-6-1h0l-2 1c0-1 0 0-1-1l1-1-2-2v-12h0v-4z" class="G"></path><path d="M512 685l1-1c1 0 0 0 2-1h1v1 1h0v2h-2l-1 1c-1-1-1-2-1-3z"></path><path d="M506 701l1-1 1-1c-2-1-3 0-4-1v-1c2 0 3 0 5-1l-1-1c-1 1-2 1-3 1l-1-1 1-2h4l1 1 2-1h0l1 1h0 1 4v-4 13h-1-2c-2-1-2-1-3-1l-6-1z" class="B"></path><path d="M502 698l2 2-1 1c1 1 1 0 1 1l2-1h0l6 1c1 0 1 0 3 1h2 1c1 8 0 18 0 27v-1-1l-2 1-1 1c-2 0-3-1-4-2v-1h-2-1v-1c1 0 1-1 2-1v-1h-7 0l-2-2v-6l1-18z" class="C"></path><defs><linearGradient id="x" x1="504.689" y1="715.951" x2="502.31" y2="721.63" xlink:href="#B"><stop offset="0" stop-color="#151416"></stop><stop offset="1" stop-color="#2a2820"></stop></linearGradient></defs><path fill="url(#x)" d="M501 716h7l-1 2c1 1 2 1 3 2h-1-3-2c-1 1-1 3-1 4l-2-2v-6z"></path><path d="M510 720h3c1 1 1 1 2 1v2h2 0c-2 2-4 1-7 1h-7 0c0-1 0-3 1-4h2 3 1z" class="Z"></path><path d="M506 701l6 1c1 0 1 0 3 1h2c0 1-1 1-1 2l1 1-1 1h-4v1c-3-1-6 0-9-2 0-1 1-2 1-4l2-1h0z" class="Q"></path><path d="M538 661c1 1 2 1 2 2 1 4 1 10 1 15 0 2 1 4 0 6l1 11h1v-2l2-1v-1c1 1 2 1 3 2h0l2-1c1 0 2 1 4 0 2 0 5 0 7-1s4-1 5-1v2c-1 0-1 0-1 1-1 2-2 3-3 4h-1c-1 2-1 4-1 6l-1-1-1 1v2 1l1 1c2 1 4 4 6 6h1v1c-1 4 1 7-2 10l3 1c1 0 1 1 2 2h1v1c0-1 1-2 1-2 1-1 1-2 2-3l2 1h1c-1 1-1 2-1 3 1 2 2 3 2 6l-1 1c-2 2-1 5-2 8-2 0-3-2-4-3h-1c-1 0-2-1-4-1h-3v2h-1 0l-1 1h-2c-2 1-3 2-4 2-1 1-3 2-4 2-2 2-3 3-5 4l-1 1c-1 0-1 0-1-1-1 1-1 2-1 3v23 31 1h-2v18c0 3 0 6-1 8l-1 5v9c-1 1-1 3-1 4 0 3 0 5-1 8-1 1-1 2-1 3l-1 2c2 2 0 5 3 6v1h-1c-2 1-2 1-4 1h0-3v-1c0-2 0-5 1-7h2c1-3 0-6 1-9 0-4 1-7 2-10l2-22c1-5 0-11 0-16l1-9v-20-51-15c-1-6 0-11 0-17 0-4 0-8 1-13 0-3-1-7 0-10v-1-1h0v-3c-1-1-1-1-1-2-1-2 0-3 0-4z" class="P"></path><path d="M539 710v-13-5c2-3 0-9 2-12v4h0l1 11h1v-2l2-1c1 2 2 3 3 5 0 1 0 2-1 4 0 1 0 2 1 2l-1 1c-1 1-2 1-3 1l-1 1 1 1v1l-1 1 1 1v-1l1 1c-1 1-1 1-1 3l-1 1-1 2h0v4l-1 4v3c-1-2-1-9-1-11-1-2 1-4-1-6z" class="L"></path><path d="M547 701c0 1 0 2 1 2l-1 1c-1 1-2 1-3 1l-1 1 1 1v1l-1 1 1 1v-1l1 1c-1 1-1 1-1 3l-1 1-1 2h0v-12c1 0 1 0 2-1l1-1c2-1 0 1 1 0l1-1z" class="E"></path><path d="M538 778c1 7 1 15 1 22 0 5 1 10 0 15v4c-1 5-1 11-1 16 0 3 0 8-1 11-1 2-2 4-2 5-1 5-2 8-2 13h1c2 2 0 5 3 6v1h-1c-2 1-2 1-4 1h0-3v-1c0-2 0-5 1-7h2c1-3 0-6 1-9 0-4 1-7 2-10l2-22c1-5 0-11 0-16l1-9v-20z" class="S"></path><path d="M539 710c2 2 0 4 1 6 0 2 0 9 1 11v-3c0 2 0 4 1 5v13 10 23 31 1h-2c-1-4 0-10 0-14v-41c0-5-1-9-1-14-1-6 0-13 0-19v-9z" class="V"></path><path d="M554 692c2 0 5 0 7-1s4-1 5-1v2c-1 0-1 0-1 1-1 2-2 3-3 4h-1c-1 2-1 4-1 6l-1-1-1 1v2h-1c-1 1-1 1-2 1-3-1-6 0-8 2l-1 2h-1l-1-1v1l-1-1 1-1v-1l-1-1 1-1c1 0 2 0 3-1l1-1c-1 0-1-1-1-2 1-2 1-3 1-4-1-2-2-3-3-5v-1c1 1 2 1 3 2h0l2-1c1 0 2 1 4 0z" class="P"></path><path d="M558 703c-1 0-1 0-2-1v-5c1-1 3-1 5-1v1c-1 2-1 4-1 6l-1-1-1 1z" class="B"></path><path d="M554 692c2 0 5 0 7-1s4-1 5-1v2c-1 0-1 0-1 1-1 2-2 3-3 4h-1v-1l1-1h0c-1-1-1-1-2 0h-7l-1-1h-1c-2 3 0 5-2 8l-1 1c-1 0-1-1-1-2 1-2 1-3 1-4-1-2-2-3-3-5v-1c1 1 2 1 3 2h0l2-1c1 0 2 1 4 0z" class="M"></path><path d="M546 710l1-2c2-2 5-3 8-2 1 0 1 0 2-1h1v1l1 1c2 1 4 4 6 6h1v1c-1 4 1 7-2 10l3 1c1 0 1 1 2 2h1v1c0-1 1-2 1-2 1-1 1-2 2-3l2 1h1c-1 1-1 2-1 3 1 2 2 3 2 6l-1 1c-2 2-1 5-2 8-2 0-3-2-4-3h-1c-1 0-2-1-4-1h-3v2h-1 0l-1 1h-2c-2 1-3 2-4 2-1 1-3 2-4 2-2 2-3 3-5 4l-1 1c-1 0-1 0-1-1-1 1-1 2-1 3v-10-13c-1-1-1-3-1-5l1-4v-4h0l1-2 1-1c0-2 0-2 1-3h1z" class="T"></path><path d="M571 726c1 1 2 3 2 4v1c-1 1-1 3-2 3-1 1-2 1-3 1l2-1v-2-4c0-1 1-2 1-2z" class="L"></path><path d="M561 732h9v2l-2 1c-1-1-5 0-6-1h-7c1-1 4-2 6-2z" class="F"></path><path d="M548 743c4-3 9-4 14-5v2h-1 0l-1 1h-2c-2 1-3 2-4 2-1 1-3 2-4 2h-1c0-1-1-2-1-2z" class="C"></path><path d="M546 725h1l1 1h0v3l3 2c-1 2-1 3-3 4h-3c-1 2-1 3-1 5v2c-1-3-1-6-1-8v-1l1-2c0-1 0-1 1-2 0-1 0-2 1-4z" class="E"></path><path d="M542 728c1 1 2 1 3 1-1 1-1 1-1 2l-1 2v1c0 2 0 5 1 8v2c1 0 3-1 4-1 0 0 1 1 1 2h1c-2 2-3 3-5 4l-1 1c-1 0-1 0-1-1-1 1-1 2-1 3v-10-13-1z" class="M"></path><path d="M571 726c1-1 1-2 2-3l2 1h1c-1 1-1 2-1 3 1 2 2 3 2 6l-1 1c-2 2-1 5-2 8-2 0-3-2-4-3 0-1 1-1 2-1l1-1h-4l3-1c1-1 1-3 1-5v-1c0-1-1-3-2-4z" class="b"></path><path d="M571 726c1-1 1-2 2-3l2 1c0 2-1 2-1 4l-1 2c0-1-1-3-2-4z" class="O"></path><path d="M564 724l3 1c1 0 1 1 2 2h1v1 4h-9c-2 0-5 1-6 2h-1l-1-6 3 1h0 2c1-1 2-1 4-1 1 0 1 1 2 1 1-1 1-1 1-2l-1-2v-1z" class="B"></path><path d="M567 725c1 0 1 1 2 2h1v1 4h-9v-2l1 1c2 0 4 1 5 0v-6z" class="C"></path><path d="M553 728l3 1v1c1 0 2 0 3-1h2l1 1h-1v2c-2 0-5 1-6 2h-1l-1-6z" class="D"></path><path d="M542 716h0l1 2c-1 1-1 1 0 2v1h1c1 1 2 2 3 2l1 1v-1h1l1 1h0c3 2 7 2 10 2 1 0 2-1 4-1l1 2c0 1 0 1-1 2-1 0-1-1-2-1-2 0-3 0-4 1h-2 0l-3-1c0-1 0-1-1-2v1h-1c-1 1-1 1-2 1l-1-2h0l-1-1h-1c-1 2-1 3-1 4-1 0-2 0-3-1v1c-1-1-1-3-1-5l1-4v-4z" class="C"></path><path d="M542 728c0-2 1-2 2-4l2 1c-1 2-1 3-1 4-1 0-2 0-3-1z" class="G"></path><path d="M546 710l1-2c2-2 5-3 8-2 1 0 1 0 2-1h1v1l1 1c2 1 4 4 6 6h1v1c-1 4 1 7-2 10v1c-2 0-3 1-4 1-3 0-7 0-10-2h0l-1-1h-1v1l-1-1c-1 0-2-1-3-2h-1v-1c-1-1-1-1 0-2l-1-2 1-2 1-1c0-2 0-2 1-3h1z" class="O"></path><path d="M546 710c1 0 3-1 3-1 0 1-2 1-3 2 0 1-1 3-1 4v1l1-1v4h-2l-1 2v-1c-1-1-1-1 0-2l-1-2 1-2 1-1c0-2 0-2 1-3h1z" class="Q"></path><path d="M559 711c2 3 1 3 1 5h-1 0l-2-2-1-1c-1 1-2 3-3 4s-1 0-1 1h-1v-2h-1-1c1-3 1-3 3-4h3 1c1 1 2 0 3-1z" class="B"></path><path d="M546 715c0-1 1-2 2-3s3-3 5-3c3 0 4 1 6 2-1 1-2 2-3 1h-1-3v-1c-1 1-2 1-3 2-1 2-1 4-1 6 1 2 1 2 3 3v1c-2-1-4-2-5-4v-4z" class="S"></path><path d="M501 722l2 2h0 7v1c-1 0-1 1-2 1v1h1 2v1c1 1 2 2 4 2l1-1 2-1v1 1h1l-1 43c0 4 1 9 0 13 0 7-1 14-1 21 0 2-1 4 0 6v1 8c1 0 1 0 1 1-1 11 0 21 0 32v6h-1v-5c-1 4 0 9-1 13v2h-1c0-2 0-2-2-3l-1 1-2-1 2-1h-6c-1-4 0-7 0-11v-28c-1-5-2-9-3-13 0-4 0-7-1-11 0-6-1-13-1-19v-28-13-22z" class="B"></path><path d="M504 747l1-1c3 0 8 0 11 1v1l-1 1-4-1-7-1z" class="f"></path><path d="M504 747l7 1 4 1h-2-1-2-5v1h0c1 1 1 1 2 1 0 2-1 2-1 3l-2 2v-9z" class="V"></path><path d="M510 827s-1-3-1-4h1c0-2 0-3 1-4s1-1 2-1c0 1 0 2 1 3h0v17c-1-3-1-5-1-8v-1 3c0 3-1 5-2 7v-7h1v-3l1-1c-1-1-2-1-3-1z" class="D"></path><path d="M510 827c1 0 2 0 3 1l-1 1v3h-1v7 16c0 2 0 5-1 7l-1-1v-22c0-4-1-9 1-12z" class="S"></path><defs><linearGradient id="y" x1="509.205" y1="757.28" x2="510.428" y2="749.903" xlink:href="#B"><stop offset="0" stop-color="#5a5958"></stop><stop offset="1" stop-color="#767370"></stop></linearGradient></defs><path fill="url(#y)" d="M504 756l2-2c0-1 1-1 1-3-1 0-1 0-2-1h0v-1h5 2 1l2 1c1 2 1 6 1 8l-1-1h-8c1 0 4 0 6 1h-7v1l-1 1-1 6v-10z"></path><defs><linearGradient id="z" x1="517.841" y1="839.093" x2="512.467" y2="838.306" xlink:href="#B"><stop offset="0" stop-color="#898785"></stop><stop offset="1" stop-color="#a6a49e"></stop></linearGradient></defs><path fill="url(#z)" d="M517 813v1 8c1 0 1 0 1 1-1 11 0 21 0 32v6h-1v-5c-1 4 0 9-1 13v2h-1c0-2 0-2-2-3l-1 1-2-1 2-1h1c1-2 1-5 1-7v-22-17c1-1 1-2 1-3 1-1 1-3 2-5z"></path><path d="M517 822c1 0 1 0 1 1-1 11 0 21 0 32v6h-1v-5-34z" class="F"></path><path d="M501 722l2 2h0 7v1c-1 0-1 1-2 1v1h1 2v1c1 1 2 2 4 2v1c-1 0 0 0-1 1h1v1h0-1-1c-1 1-1 1-2 1l-1 1c1 0 2 1 3 1h3l1 1c-2 2-10 1-13 1l1 1c1 0 2-1 4 0h1c1 2 1 2 1 3-1 1-7 1-9 1 0 4 0 10-1 14v-13-22z" class="E"></path><path d="M510 735h-6-1v-1c2-1 6 0 8 0l-1 1z" class="G"></path><path d="M503 724h7v1c-1 0-1 1-2 1v1h1c-2 1-4 1-5 1 0-1 0-1-1-2v-2z" class="B"></path><path d="M511 786h4c1 1 0 3 0 5 0 4 0 10-1 15l-1 9c-1 1-2 0-3 0-1 1-2 1-3 1v-1c1-5 0-9-1-14s-2-10-2-15h7z" class="U"></path><path d="M511 786h4c1 1 0 3 0 5 0 4 0 10-1 15-1-1-2-3-3-4v-6-7l1-1c0-2-5 0-7-2h5 1z" class="V"></path><path d="M513 758h1c2 3 1 10 1 14v12h-11v-18l1-6 1-1v-1h7z" class="Y"></path><path d="M506 758l1 6 1 1c0 1-1 2-1 2-1 3 0 8-1 12l1 1h0c1 0 2 1 3 2h4c1-1 0-10 1-10v12h-11v-18l1-6 1-1v-1z" class="Z"></path><path d="M544 261c0-2 0-4-1-6 0-3 0-6 1-9l1-3v-1c0-1 0-2 1-3h1c0 2-1 3-1 4 1 1 2 1 3 2v2 1l1 8c1 1 2 2 3 2l-1 2h1l2 1v3h0c1 1 0 2 1 3v1h1l-1 2h0l1-1 1-1h3 0l4 1v4c-1 1-1 2 0 3h0c2 1 1 1 2 3h1v1c1 1 1 1 2 1v-1l1 1-1 1 1 1c1 1 1 2 1 4v4c0 1-1 3-1 4-2-1-2-1-3-2l-1 1c0 3 0 5-1 8v1 1c2 2 4 2 6 4 1-1 2-1 3-1v2h0c1 2 2 2 2 4-1 0-1 1-2 2h1 0 3v1l-2 2c0 1 2 1 3 1 1 2 2 2 2 4h4 1c1 1 1 3 2 4v-2c1 1 1 1 3 2h2 1 3c-1 1-1 1-2 1l-2 6h4c-3 7-6 12-11 17h0-1c-1 1-2 1-3 2-2 2 0 12 0 15l1 1 2 1c1 0 2 1 2 2h1c-1 1-1 1-2 0v1c-2-1-4-1-6-1h-2-2c-1 0-2 1-4 1-2-1-3-1-4 0s-2 2-2 3 1 2 0 3c0 4-2 7-1 10v2l-2-1c0 1 1 2 0 2h-1c-1-1-1-3-1-4-1 0-1 1-2 2-1 2-1 5-1 7-1 1-2 2-2 4v1 1 2c0 1 0 1 1 2 0-1 0-1 1-2v-1c0-1 0-1 1-2 0 1 0 2 1 3v1 1s0 1 1 2l1-2 1-1 1 2 1 41h0v7h3 1c-1 1-1 3-1 4v3c1 3 1 7 0 9h0c0 1 0 2-1 3h-2v9 19 11c1 0 2 0 3 1 0 1 0 2 1 2v5c-3 0-5-1-7-2h-2-9-1-1c-1 1-1 1-3 1h-1c-2 0-2-1-3-1l-1-1v-13c-1 7 0 14-1 21l-1 2v1 1l-1-1v-1l-1-1-1 1h1l-1 1-1-1v-1-1-13c0-2-1-4 0-5v-1-1-5-2-3l-3-3h-1v-1l1-1-1-1v-1l-3-1v-21c0-3 1-7 0-10l-1 1v14l-1-2c0-4-1-10 0-14h4 5c-1-2-4-1-5-1h-1-2-1-1l-1-1v1 1c-1-2-1-3-1-4-1 1-1 3-1 5-1 7 0 15 0 23v6s-1 1-2 1c0 2 0 7 1 9 0 1 0 2-1 4v8c0 2 0 4-1 6v3h0v7 4 1c-2 0-2 1-3 1h-1c-1-1-2-1-3-1s-1 0-1 1v1c-1 1-2 1-3 1s0 0-1 1l-1 1 1 1h-1l-1 2h1v3 1 1h-1 0c0-1 0-1-1-1-1 1 0 5 0 7l1 1v1h-1l-1-2c-2 1-2 3-3 3-2-1-2-2-2-4v-6c1-1 1-2 1-4 0-1 0-3-1-4v-2-14-4s1 0 1-1h1s-1 0-1-1v-5-18c0-3 2-9 1-12h-1v-9-4l-1-21-1-1v-3l-1-9c1-4 1-6 3-9 0-2 0-4 1-6 0-4 1-7 2-11v-2h-2c-1 0-2 1-3 2-1 2-1 3-1 4v1c-1-1-1-2-2-3l1-1 2-5c5-5 8-6 15-6l10 7v1c2 2 4 3 5 6 2 2 4 5 5 8h0c1 1 1 1 1 2l1 1 2 4 1 2 2 4v1 1c1 1 1 1 1 2 1-2 1-3 1-4l-1-121v-23l1-7v-3c1-4 0-11 2-14z" class="G"></path><path d="M528 456l1-1v-1h0c1-3-2-8 1-10h1v5l1 1-1 1v2c0 1-1 2-1 3h-2z" class="H"></path><path d="M551 403l1-2 5 1v1 2l-1-1h-2c-1 1-1 1-1 3l1 2c-1-1-1-1-2-1l-1 1c-1-2 0-4 0-6z" class="L"></path><path d="M549 508v11l2 1h-1c-1 1-1 1-3 1h-1c-2 0-2-1-3-1 1-2 4-3 4-5 1-1 1-5 1-6l1-1z" class="D"></path><path d="M557 391l3-2v1c-1 2-1 5-1 7-1 1-2 2-2 4v1l-5-1-1 2-1-1h-1v-1h2c1-2 1-4 2-6s3-3 4-4z" class="P"></path><path d="M557 391l3-2v1c-1 2-1 5-1 7-1 1-2 2-2 4-1-2-1-2-2-3l2-1v-2c-1-1-3-1-4 0 1-2 3-3 4-4z" class="K"></path><path d="M557 405c0 1 0 1 1 2 0-1 0-1 1-2v-1c0-1 0-1 1-2 0 1 0 2 1 3v1 1s0 1 1 2c1 3 1 6 0 9v-4h-3-7v5c-1 0 0-4 0-5l-1 1-2-2 1-1c1-1 5-2 7-2v1c1-1 2-1 3-1h0v-1h-1c-2-1-3 0-5 0l-1-2c0-2 0-2 1-3h2l1 1z" class="b"></path><path d="M544 339c1 0 3 0 4 1l1 1h0v2 1c-1 1-1 1-2 1l1 1v3h0c1 2 1 3 1 5l-1 1v1l1 2c0 1 0 2-1 3-2 2-2 3-4 4v-26z" class="F"></path><path d="M548 349c1 2 1 3 1 5l-1 1h-1l-1 1-1-1 3-6z" class="B"></path><path d="M548 298l-1-1-6-3c3 0 10 2 12 4v1h1c0 1 1 0 2 1 1 2 3 3 5 4 1 0 1 1 2 1l1 1 2-2c2 2 4 2 6 4 1-1 2-1 3-1v2h0c-3 3-3 7-5 10h-1c-1-1-1-1-3-2l1-2c1-1 2-2 2-4l-3-2c-3-3-8-5-12-7-2 0-2-1-4-2h-1v-2h-1z" class="e"></path><path d="M572 308c1-1 2-1 3-1v2h0c-3 3-3 7-5 10h-1c-1-1-1-1-3-2l1-2c1-1 2-2 2-4 0-1 0-1 1-2h0c2 1 1 2 2 3l1-1c-1-1-1-1-1-2v-1z" class="Y"></path><path d="M549 358c1-2 1-3 1-4h1c0 1 1 2 0 3h1l2 2v1h-1c-3 2-5 5-7 7v1c0 2 0 2 1 4 1 0 2 0 2-1 0 4-1 9 1 12l-1 1v1c1 0 1-1 2-1 1 1 1 1 1 3l-1 1h1v-1c1 1 0 2 0 2h0l-9 5 1-28v-1c2-1 2-2 4-4 1-1 1-2 1-3z" class="K"></path><path d="M554 286c2 0 2 1 4 2s3 3 5 5l1-1h0c0-1 0-1-1-2-2-1-4-3-5-4v-1c1 0 2 1 2 2 1 0 2 1 3 2l2 1 2 4c0 3 0 5-1 8v1 1l-2 2-1-1c-1 0-1-1-2-1-2-1-4-2-5-4-1-1-2 0-2-1h-1 3l2-3c0-1 1-2 0-3-1 1-1 0-2 1l-1 2v1l-1 1c-1-1-1-1-1-2 0-2 2-4 3-5h0c-2 0-3 0-4 1-1 2-1 2-1 4h-1c-1-2-1-3-1-5 1-2 3-3 5-5z" class="O"></path><path d="M556 291c2 1 3 1 4 3v2 2l-1 1 1 1 1 1 2-2v3h1l1-1h0l1 2v1l-2 2-1-1c-1 0-1-1-2-1-2-1-4-2-5-4-1-1-2 0-2-1h-1 3l2-3c0-1 1-2 0-3-1 1-1 0-2 1l-1 2v1l-1 1c-1-1-1-1-1-2 0-2 2-4 3-5z" class="b"></path><path d="M549 490l-1-56 1-14c0-2-1-5 0-7v71l1-1 1 2v3c0 4-1 9 1 12h0v-1c0-3 0-6 1-9h0c1 0 1 1 2 1-1 3 0 6 0 9v18h-3 0v2h-1l-2-1v-11-18z" class="Z"></path><path d="M549 490h0c0 5 0 10 1 15v2-3h1v14h1v2h-1l-2-1v-11-18zm14-145v-1c1-1 1-1 0-2l2-2c1 2 2 3 2 4-2 5-5 8-8 13-4 4-8 8-13 11v-1c2-2 4-5 7-7h1v-1l-2-2h-1c1-1 0-2 0-3h-1c0 1 0 2-1 4l-1-2v-1l1-1c0-2 0-3-1-5h0v-3l-1-1c1 0 1 0 2-1v-1-2l1 2c1 1 1 2 1 3v4h1c1-1 2-3 3-4 1 0 1 0 2-1h1l1-1h-1l-1-1h-3v-2h4 2v1c0 1 1 2 1 2 1 1 0 1 2 0v1z" class="V"></path><path d="M555 357l1-2c0-1-1-1-1-2h-1l-1 1 1 1h0c-1 0-3-1-3-2 1-2 1-2 2-3 2 0 4 1 5 2v3h-1l-1 2h-1z" class="C"></path><path d="M563 345v-1c1-1 1-1 0-2l2-2c1 2 2 3 2 4-2 5-5 8-8 13-4 4-8 8-13 11v-1c2-2 4-5 7-7h1v-1l-2-2v-2l3 2h1l1-2h1l2-2v-2c0-1 1-1 1-2l1-1c0-1 1-2 1-3z" class="X"></path><path d="M539 459l1-22h0c1 0 3 1 3 2 1 4 0 9 0 13v30l-1 18v6c-1 7 0 14-1 21l-1 2c-1-3 0-6 0-8v-15l-1-47z" class="R"></path><path d="M549 413l2 2 1-1c0 1-1 5 0 5v22 27c0 7 0 15 1 22-1 3-1 6-1 9v1h0c-2-3-1-8-1-12v-3l-1-2-1 1v-71z" class="X"></path><path d="M534 489c-1-1-1-2-1-3v-1-3l1-1-1-1 1-1h2l1-1v-3c0 1 1 1 1 2v-10-3-1c1-2 0-3 1-4l1 47v15c0 2-1 5 0 8v1 1l-1-1v-1l-1-1-1 1h1l-1 1-1-1v-1-1-13c0-2-1-4 0-5v-1-1-5-2-3l-3-3h-1v-1l1-1c0-1 0-2 1-3h0z" class="E"></path><path d="M534 489l1-2h1c0 1 1 3 1 5-1 0-2 1-2 1-1-1-1-3-1-4h0z"></path><path d="M544 339v-6-16c1-6-1-13 0-19l1-1c1 1 1 1 3 1h1v2h1l-1 2 1 1c-1 2-4 4-3 5 2-1 4-3 5-5l1 1v1 1c0 1 0 0-1 1h-3v1h1v1c-3 3-5 4-4 9 0 3 6 7 5 9 0 2 0 7 1 8s2 1 4 2l-1 2c1 0 2 0 2 1l1 1h-4v2h3l1 1h1l-1 1h-1c-1 1-1 1-2 1-1 1-2 3-3 4h-1v-4c0-1 0-2-1-3l-1-2h0l-1-1c-1-1-3-1-4-1z" class="M"></path><path d="M522 428l2-20h1c1 0 2 1 3 2 0 2 0 4-1 6v4c-1 2 0 4-1 6 0 2 0 5 1 7h0v1c-1 7 0 15 0 22h-1l-1-1v1 1c-1-2-1-3-1-4-1 1-1 3-1 5-1 7 0 15 0 23v6s-1 1-2 1c0-2 1-6 0-9v-33-8c0-2 0-5 1-7v-3z" class="P"></path><path d="M562 490c1 5 2 8 2 13h1 1v11c1 0 2 0 3 1 0 1 0 2 1 2v5c-3 0-5-1-7-2h-2-9v-2h0 3v-18c0-3-1-6 0-9h6 1v-1z" class="Q"></path><path d="M561 491h1v21c0 3 1 6 1 8h-2c-1-5-1-11-2-16 0-4-1-8 0-12l2-1z" class="b"></path><path d="M562 490c1 5 2 8 2 13h1 1v11c1 0 2 0 3 1 0 1 0 2 1 2v5c-3 0-5-1-7-2 0-2-1-5-1-8v-21-1z" class="I"></path><path d="M566 514c1 0 2 0 3 1 0 1 0 1-1 1-1 1-2 1-3 1 0-2 0-2 1-3z" class="R"></path><path d="M550 300c2 1 2 2 4 2 4 2 9 4 12 7l3 2c0 2-1 3-2 4l-1 2c2 1 2 1 3 2h1c-1 2-2 3-3 4-1 0-3-1-4-1-1 1-3 2-3 4l-2 1v1l-1 2h0c-2 0-3-1-4-1 0 1 0 1 1 3l1-1 1 1v2c-2-1-2-1-4-1v2c-1-1-1-6-1-8 1-2-5-6-5-9-1-5 1-6 4-9v-1h-1v-1h3c1-1 1 0 1-1v-1-1l-1-1c-1 2-3 4-5 5-1-1 2-3 3-5l-1-1 1-2z" class="L"></path><path d="M566 317c2 1 2 1 3 2h1c-1 2-2 3-3 4-1 0-3-1-4-1 2-2 3-3 3-5z" class="b"></path><path d="M550 300c2 1 2 2 4 2 4 2 9 4 12 7l-1 1c0 1 0 1 1 2l-1 1c-1 0-1 0-1-1l-3-3h-1c0 1 2 3 2 3l-1 1c-1-1-1-2-2-2v-1-1h-2c-1-1-3-1-4-1-1 1-2 3-3 3v1c0 1-1 1-1 2v1 1l-1 3 1 1s0 1 1 1v-1c0-1 0-1 1-2v1l2 2h4c1 1 0 2 0 3v1c1 1 1 1 1 2v1l-1 2h0c-2 0-3-1-4-1 0 1 0 1 1 3l1-1 1 1v2c-2-1-2-1-4-1v2c-1-1-1-6-1-8 1-2-5-6-5-9-1-5 1-6 4-9v-1h-1v-1h3c1-1 1 0 1-1v-1-1l-1-1c-1 2-3 4-5 5-1-1 2-3 3-5l-1-1 1-2z" class="V"></path><path d="M552 419v-5h7v1c1 0 1 0 1 1 1 1 1 2 1 3v9 8 2c-1 0-2 0-3-1h-2c0 1 1 3 1 3l-2 4c2 2 0 5 2 7l-1 1c0 1 0 1-1 2l2 2c-1 1-1 2-2 3 1 2 1 2 2 3l1 1c0 1 0 1-1 3 1 0 1 0 2 1 1 0 1 1 1 2h1v1l1 1h0c0 1 0 2 1 2 0 3 0 6-1 9v8 1h-1-6c-1 0-1-1-2-1h0c-1-7-1-15-1-22v-27-22z"></path><path d="M558 427l3 1v8c-1 0-3 0-4-1l1-1v-7z" class="Q"></path><path d="M559 415c1 0 1 0 1 1 1 1 1 2 1 3v9l-3-1c0-4 0-8 1-12z" class="L"></path><path d="M554 439c0-3-1-12 1-14l2 1v1 1c0 1 0 1-1 2l1 1v1c0 1-1 2 0 3s3 1 4 1v2c-1 0-2 0-3-1h-2c0 1 1 3 1 3l-2 4c2 2 0 5 2 7l-1 1c0 1 0 1-1 2l2 2c-1 1-1 2-2 3l-1-2c1-6 0-12 0-18z" class="a"></path><path d="M556 437v-1-4-1l1 1c0 1-1 2 0 3s3 1 4 1v2c-1 0-2 0-3-1h-2z" class="L"></path><path d="M552 441c1-1 1-2 1-3v-14h1l-1 8c1 2 0 4 1 6v1c0 6 1 12 0 18l1 2c1 2 1 2 2 3l1 1c0 1 0 1-1 3 1 0 1 0 2 1 1 0 1 1 1 2h-3v-1c0-1 0-1-1-1l-2 2c0 5-1 11 0 16l1 1c0 1-1 1-2 2v2h0c-1-7-1-15-1-22v-27z" class="M"></path><path d="M554 457l1 2c1 2 1 2 2 3l1 1c0 1 0 1-1 3 1 0 1 0 2 1 1 0 1 1 1 2h-3v-1c0-1 0-1-1-1l-2 2v-12z" class="Q"></path><path d="M554 469l2-2c1 0 1 0 1 1v1h3 1v1l1 1h0c0 1 0 2 1 2 0 3 0 6-1 9v8 1h-1-6c-1 0-1-1-2-1v-2c1-1 2-1 2-2l-1-1c-1-5 0-11 0-16z" class="P"></path><path d="M554 485c2 0 2-1 3-2l4 2v1c-2 1-4 1-6 0l-1-1z" class="S"></path><path d="M561 485c0-1 1-2 1-3v8 1h-1-6c-1 0-1-1-2-1v-2c1-1 2-1 2-2 2 1 4 1 6 0v-1z" class="G"></path><path d="M562 409l1-2 1-1 1 2 1 41h0v7h3 1c-1 1-1 3-1 4v3c1 3 1 7 0 9h0c0 1 0 2-1 3h-2v9 19h-1-1c0-5-1-8-2-13v-8c1-3 1-6 1-9-1 0-1-1-1-2h0l-1-1v-1h-1c0-1 0-2-1-2-1-1-1-1-2-1 1-2 1-2 1-3l-1-1c-1-1-1-1-2-3 1-1 1-2 2-3l-2-2c1-1 1-1 1-2l1-1c-2-2 0-5-2-7l2-4s-1-2-1-3h2c1 1 2 1 3 1v-2-8-9c0-1 0-2-1-3 0-1 0-1-1-1v-1h3v4c1-3 1-6 0-9z" class="h"></path><path d="M566 449h0v7h3 1c-1 1-1 3-1 4v3c1 3 1 7 0 9h0c0 1 0 2-1 3h-2v9-35z" class="H"></path><defs><linearGradient id="AA" x1="565.364" y1="453.562" x2="556.336" y2="456.338" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#4e4b45"></stop></linearGradient></defs><path fill="url(#AA)" d="M559 414h3v4l1 55c-1 0-1-1-1-2h0l-1-1v-1h-1c0-1 0-2-1-2-1-1-1-1-2-1 1-2 1-2 1-3l-1-1c2-1 3 0 4-1v-7-1-4c0-3-2-1-3-3h3v-3-5-2-8-9c0-1 0-2-1-3 0-1 0-1-1-1v-1z"></path><path d="M556 437h2c1 1 2 1 3 1v5 3h-3c1 2 3 0 3 3v4 1 7c-1 1-2 0-4 1-1-1-1-1-2-3 1-1 1-2 2-3l-2-2c1-1 1-1 1-2l1-1c-2-2 0-5-2-7l2-4s-1-2-1-3zm-12-176c0-2 0-4-1-6 0-3 0-6 1-9l1-3v-1c0-1 0-2 1-3h1c0 2-1 3-1 4 1 1 2 1 3 2v2 1l1 8c1 1 2 2 3 2l-1 2h1l2 1v3h0c1 1 0 2 1 3v1h1l-1 2h0l1-1 1-1h3 0l4 1v4c-1 1-1 2 0 3h0c2 1 1 1 2 3h1v1c1 1 1 1 2 1v-1l1 1-1 1 1 1c1 1 1 2 1 4v4c0 1-1 3-1 4-2-1-2-1-3-2l-1 1-2-4-2-1c-1-1-2-2-3-2 0-1-1-2-2-2v1c1 1 3 3 5 4 1 1 1 1 1 2h0l-1 1c-2-2-3-4-5-5s-2-2-4-2h0c-3 1-5 2-7 4v1h0l-2-1v-1c-1-1 0-4 0-6v-12l-1-10z" class="P"></path><path d="M565 282c1 1 1 2 1 3s-1 1-2 2h-1c0-2-1-4-1-6l1 1h1 1zm0 8c1-1 1-1 1-2h1v-1l2 2c0 1-1 2-1 3v1l-1 1-2-4z" class="M"></path><path d="M553 280h3c0 1-1 2-1 4h-3l-3 3v-2-3c2 0 2 0 4-2z" class="B"></path><path d="M554 276c1 0 4 0 6 1l1-1c1 2 0 3 0 5h-1c-1-1-1-1-2-1h-2-3-2 0c0-2 1-3 2-4h1z" class="X"></path><path d="M557 269l1-1h3 0l4 1v4c-1 1-1 2 0 3h0c-2 0-2 0-3-2h-1l-1 1-1-1-1-1h0c0-2 0-2-1-4z" class="S"></path><path d="M559 274h0c0-1 0-1 1-1 0-1 1-3 2-3l1 1v3h-1-1l-1 1-1-1z" class="B"></path><path d="M567 279h1v1c1 1 1 1 2 1v-1l1 1-1 1 1 1c1 1 1 2 1 4v4c0 1-1 3-1 4-2-1-2-1-3-2v-1c0-1 1-2 1-3l1-1v-2c-2-1-3-3-5-4h-1c0-1 0 0 1-1h1s1-1 1-2z" class="S"></path><path d="M544 261c0-2 0-4-1-6 0-3 0-6 1-9l1-3v-1c0-1 0-2 1-3h1c0 2-1 3-1 4 1 1 2 1 3 2v2 1l1 8c1 1 2 2 3 2l-1 2h1l2 1v3 4c0 1 0 2 1 3 0 2 0 3-2 4v1h-1c-1 1-2 2-2 4h0 2c-2 2-2 2-4 2v-3s-1 0-1-1c-1-2 1-3-1-4-1-1-2-2-2-3h0l-1-10z" class="C"></path><path d="M553 276v-1l-1-1c1-1 1-2 1-2 1-1 2-1 3-1 0 2 0 3-2 4v1h-1z" class="B"></path><path d="M549 247v1l1 8c1 1 2 2 3 2l-1 2h1v1c0 1-1 2-1 3l1 1v1c0 1-1 2 0 4h-1c-1-3-1-5-1-8h-1l-1 1v3c1 4 1 9 0 13 0 0-1 0-1-1-1-2 1-3-1-4-1-1-2-2-2-3v-2c1-1 1-2 1-4v-1c2-1 3-2 4-4 0-2 0-2-1-3v-10z" class="Q"></path><path d="M549 266v4h-1v-1c-1-1-2-3-1-4 0-1 1-1 2-2v3z" class="L"></path><path d="M544 261c0-2 0-4-1-6 0-3 0-6 1-9l1-3v-1c0-1 0-2 1-3h1c0 2-1 3-1 4 1 1 2 1 3 2v2 10c1 1 1 1 1 3-1 2-2 3-4 4v1c0 2 0 3-1 4v2h0l-1-10z" class="S"></path><path d="M546 243c1 1 2 1 3 2v2 10h-4v-2c1-4 0-8 1-12z" class="C"></path><path d="M575 309c1 2 2 2 2 4-1 0-1 1-2 2h1 0 3v1l-2 2c0 1 2 1 3 1 1 2 2 2 2 4h4 1c1 1 1 3 2 4v-2c1 1 1 1 3 2h2 1 3c-1 1-1 1-2 1-1-1-1-1-2-1v2c0 2-1 4-2 6l-1 1h-1c-1-1-2-1-3-1l-1 1h-1 0c-1 1-1 1-2 1v-1l-1-1h-1l-1 2v2h0c-1 0-1 1-2 1 0 1-2 1-3 1l-5 1h-1l-2 2c0-1-1-2-2-4l-2 2c1 1 1 1 0 2v1-1c-2 1-1 1-2 0 0 0-1-1-1-2v-1h-2l-1-1c0-1-1-1-2-1l1-2c-2-1-3-1-4-2v-2c2 0 2 0 4 1v-2l-1-1-1 1c-1-2-1-2-1-3 1 0 2 1 4 1h0l1-2v-1l2-1c0-2 2-3 3-4 1 0 3 1 4 1 1-1 2-2 3-4 2-3 2-7 5-10z" class="V"></path><path d="M577 318c0 1 2 1 3 1 1 2 2 2 2 4l-1 1h-2v-2c0-1 0-1-1-2v3c0 1-1 1-2 1-1-1-2-1-3-3 1-1 2-2 4-3z" class="B"></path><path d="M570 332l1 1h1c0 1 1 2 1 4l-1 1 1 1c1-1 3-1 5-2l-1 2h1l2-2v2h0c-1 0-1 1-2 1 0 1-2 1-3 1l-5 1h-1l-2 2c0-1-1-2-2-4l1-1 1-1c0-1 1-2 1-3l-1-1c1-1 2-1 3-2z" class="E"></path><path d="M570 332l1 1c0 3-1 5-2 9l-2 2c0-1-1-2-2-4l1-1 1-1c0-1 1-2 1-3l-1-1c1-1 2-1 3-2z" class="c"></path><path d="M586 323h1c1 1 1 3 2 4v-2c1 1 1 1 3 2h2 1 3c-1 1-1 1-2 1-1-1-1-1-2-1v2c0 2-1 4-2 6l-1 1h-1c-1-1-2-1-3-1l-1 1h-1 0c-1 1-1 1-2 1v-1c1-1 1-1 2-1s1 0 2-1h-1-5v-1h3v-2c1 0 2 1 3 2-1-3-3-4-4-5h-1-1l-1-1h4l-2-2h-1v-1l1-1h4z" class="b"></path><path d="M582 323h4l-1 1h0c1 1 0 2 0 3h0-1l-2-2h-1v-1l1-1z" class="O"></path><path d="M563 322c1 0 3 1 4 1-1 1-1 2-2 3l-1 1 3 3c1 0 2 1 3 2-1 1-2 1-3 2l1 1c0 1-1 2-1 3l-1 1-1 1-2 2c1 1 1 1 0 2v1-1c-2 1-1 1-2 0 0 0-1-1-1-2v-1h-2l-1-1c0-1-1-1-2-1l1-2c-2-1-3-1-4-2v-2c2 0 2 0 4 1v-2l-1-1-1 1c-1-2-1-2-1-3 1 0 2 1 4 1h0l1-2v-1l2-1c0-2 2-3 3-4z" class="Z"></path><path d="M563 322c1 0 3 1 4 1-1 1-1 2-2 3l-2-2c-1 1-2 1-3 2h0c0-2 2-3 3-4z" class="f"></path><path d="M559 333h0c1-1 3-2 4-4v1h2c1 2 1 3 0 5v1c1 1 1 2 1 3l-1 1-2 2c1 1 1 1 0 2v1-1c-2 1-1 1-2 0 0 0-1-1-1-2v-1h-2l-1-1c0-1-1-1-2-1l1-2c0-1 1-1 2-1h1c-1-1 0-1-1-1h-1c0-1 0-2 1-2h1z" class="L"></path><path d="M556 337c0-1 1-1 2-1h1c-1-1 0-1-1-1h-1c0-1 0-2 1-2h1 1c1 1 1 1 2 1v1l-1 1v1c0 1-1 3-1 4h-2l-1-1c0-1-1-1-2-1l1-2z" class="U"></path><path d="M505 415v-1c2-2 3-5 4-8v-1c1-1 1-3 1-4 0 1 1 3 1 5v1 1 1l1 3c2 6 7 8 9 13v2l1 1v3c-1 2-1 5-1 7v8c-1 1-2 1-3 2-1 3 0 7-1 9v2l-3 2c-1-1-1-1-3-1 0 1 0 1-1 2h-1l-1-2-1 2v1c-1 1-1 0-2 0 0 0-1 1-1 2v1c1 1 0 1 1 1v1h0-1c-1 1-1 2-1 4h0v3c-1 1-1 2-1 4v3c0-3 1-7-2-10v-4h0v-4l-1-21-1-1v-3l-1-9c1-4 1-6 3-9 0-2 0-4 1-6h2c1-1 1-1 2 0z" class="B"></path><path d="M516 436l1 1h2c0 2 0 2-1 4h-1l-1-1h-2l-1-2 3-2z" class="G"></path><path d="M511 446l-1 1-2 1h-1c-1-1-2 0-3 0h-1c0-2-1-4 0-6 2 0 5-1 7-1s2 2 3 3c-1 1-1 2-2 2z" class="Y"></path><path d="M509 416l1 1 1 1h1c1 2 2 3 2 5 0 4 1 11-1 13-2 1-3 1-5 0v-6c1-4 0-9 1-14z" class="g"></path><path d="M512 418c4 3 6 6 8 10h1v-1l1 1v3c-1 2-1 5-1 7v8c-1 1-2 1-3 2l-1-1c1-1 1-3 1-4v-1h-3c0-1 0-1 2-1h1c1-2 1-2 1-4h-2l-1-1c-2-2-1-10-1-13h-1c0-2-1-3-2-5z" class="E"></path><path d="M515 423h1c1 2 1 2 1 4v1h0 1 1v2c1 2 1 2 1 3v2 1c-1 1-2 1-3 1l-1-1c-2-2-1-10-1-13z" class="Y"></path><path d="M515 442h3v1c0 1 0 3-1 4l1 1c-1 3 0 7-1 9v2l-3 2c-1-1-1-1-3-1 0 1 0 1-1 2h-1l-1-2-1-1v1c-1 1-1 1-3 1 0 1 0 0-1 1v-1l1-1-1-1s0-1 1-1v-1l-1-1v-3h3l1-1h-2c1-1 1-1 2-1 1-1 2-1 2-2l1-1 1-2c1 0 1-1 2-2l2-2z" class="F"></path><path d="M515 442h3v1c0 1 0 3-1 4s-2 2-3 4h-1c-1 2-2 3-3 4h1l1-1 1 1v1h-4-5c0-1-1-2-1-3h3l1-1h-2c1-1 1-1 2-1 1-1 2-1 2-2l1-1 1-2c1 0 1-1 2-2l2-2z" class="X"></path><path d="M505 415v-1c2-2 3-5 4-8v-1c1-1 1-3 1-4 0 1 1 3 1 5v1 1 1l1 3c2 6 7 8 9 13v2 1h-1c-2-4-4-7-8-10h-1l-1-1-1-1c-1 1-6 7-7 8 1 1 2 1 2 3s-1 7 0 9c2-3-1-8 1-11l1 1v10l-2 1h-1-3 0c-1 2-1 4-1 6l-1-1v-3l-1-9c1-4 1-6 3-9 0-2 0-4 1-6h2c1-1 1-1 2 0z" class="Z"></path><path d="M501 415h2c1-1 1-1 2 0-2 2-3 4-5 6 0-2 0-4 1-6z" class="C"></path><path d="M500 437v-2c-1-3-1-8 0-11l1 1c1 2 0 5 0 8l1 1c0 2-2 1-2 3h0z" class="L"></path><path d="M592 335c1-2 2-4 2-6v-2c1 0 1 0 2 1l-2 6h4c-3 7-6 12-11 17h0-1c-1 1-2 1-3 2-2 2 0 12 0 15l1 1 2 1c1 0 2 1 2 2h1c-1 1-1 1-2 0v1c-2-1-4-1-6-1h-2-2c-1 0-2 1-4 1-2-1-3-1-4 0s-2 2-2 3 1 2 0 3c0 4-2 7-1 10v2l-2-1c0 1 1 2 0 2h-1c-1-1-1-3-1-4-1 0-1 1-2 2v-1l-3 2-2-3-3 1s1-1 0-2v1h-1l1-1c0-2 0-2-1-3-1 0-1 1-2 1v-1l1-1c-2-3-1-8-1-12 0 1-1 1-2 1-1-2-1-2-1-4 5-3 9-7 13-11 3-5 6-8 8-13l2-2h1l5-1c1 0 3 0 3-1 1 0 1-1 2-1h0v-2l1-2h1l1 1v1c1 0 1 0 2-1h0 1l1-1c1 0 2 0 3 1h1l1-1z" class="U"></path><path d="M585 336h1l1-1c1 0 2 0 3 1h1l1-1c0 2-1 4-2 5-1 0-2 1-3 1l2-1c-1-2-1-3-1-4l-1 1-2 1h-1l1-2z" class="N"></path><path d="M570 349c1 3 1 6 1 9v3c0 1 1 1 0 2v2l-1 1c-1 0-2-1-2 0-1 1-1 1-1 2h-1c1 1 2 1 3 1 0 1-1 1-2 1-1 1-1-1-3 1v5c1 1 2 0 1 2l-1-1-1-1v-1-3l1-1-1-2c0-1 0-1 1-2 0 1 1 1 2 1v-1l-1-1h-2v-2l-1-2h0l1-1v2c1 1 1 1 3 2l1-1 1-1c0 1 0 1 1 2l1-1v-15z" class="C"></path><path d="M594 334h4c-3 7-6 12-11 17h0v-1h0c1-1 4-4 4-5h-1l-2 3-1-1c0-2 0-1 2-2 0 0 1-2 1-3 1-3 3-5 4-8z" class="J"></path><path d="M563 369l1 2-1 1v3 1l1 1 1 1c-2 4-1 8-1 12 0 1 1 2 0 2h-1c-1-1-1-3-1-4-1 0-1 1-2 2v-1-1c1-1 0-2 0-3 2-3 2-7 2-11 0-1 0-3 1-5z" class="D"></path><path d="M570 349c0-2 0-3-1-5 1 0 1 1 2 1h3v1l1 2h0v4l-2 1v1c1 0 2 0 2 1-2 3-1 5-1 8v5c-2 1-2 1-3 0s-1-1-1-2l1-1v-2c1-1 0-1 0-2v-3c0-3 0-6-1-9z" class="G"></path><path d="M559 357l2 3-1 1h-1v-1-2c-2 2-2 3-3 4h2l-1 2h1l2-1h1v1l-1 1h-1l-1-1c-1 1-1 1-1 3-1 0-1 1-2 1-1 2 2 1-1 2v1h-5c0 1-1 1-2 1-1-2-1-2-1-4 5-3 9-7 13-11z" class="E"></path><path d="M569 369c3 1 5 1 8 1 2 1 4 1 6 1 1 0 2 0 3 1h1v1c-2-1-4-1-6-1h-2-2c-1 0-2 1-4 1-2-1-3-1-4 0s-2 2-2 3 1 2 0 3c0 4-2 7-1 10v2l-2-1c0-4-1-8 1-12 1-2 0-1-1-2v-5c2-2 2 0 3-1 1 0 2 0 2-1z" class="B"></path><path d="M558 373c0-1 0-2 1-3l1 2c0 1-1 1-1 2s0 1 2 2l1-2c0 4 0 8-2 11 0 1 1 2 0 3v1l-3 2-2-3-3 1s1-1 0-2v1h-1l1-1c0-2 0-2-1-3l1-1c2 0 4-3 5-4 1-2 2-4 1-6z" class="b"></path><path d="M560 385c0 1 1 2 0 3v1l-3 2-2-3 5-3z" class="M"></path><path d="M549 371h5c2 0 2 1 4 2h0c1 2 0 4-1 6-1 1-3 4-5 4l-1 1c-1 0-1 1-2 1v-1l1-1c-2-3-1-8-1-12z" class="G"></path><path d="M554 371c2 0 2 1 4 2h0c1 2 0 4-1 6-1 1-3 4-5 4 1-1 2-3 2-4 1-2 1-3 2-4 0-1-1-2-2-2v-2z" class="E"></path><path d="M518 448c1-1 2-1 3-2v33c1 3 0 7 0 9s0 7 1 9c0 1 0 2-1 4v8c0 2 0 4-1 6v3h0v7 4 1c-2 0-2 1-3 1h-1c-1-1-2-1-3-1s-1 0-1 1v1c-1 1-2 1-3 1s0 0-1 1l-1 1 1 1h-1l-1 2h1v3 1 1h-1 0c0-1 0-1-1-1-1 1 0 5 0 7l1 1v1h-1l-1-2c-2 1-2 3-3 3-2-1-2-2-2-4v-6c1-1 1-2 1-4 0-1 0-3-1-4v-2-14-4s1 0 1-1h1s-1 0-1-1v-5-18c0-3 2-9 1-12h-1v-9h0v4c3 3 2 7 2 10v-3c0-2 0-3 1-4v-3h0c0-2 0-3 1-4h1 0v-1c-1 0 0 0-1-1v-1c0-1 1-2 1-2 1 0 1 1 2 0v-1l1-2 1 2h1c1-1 1-1 1-2 2 0 2 0 3 1l3-2v-2c1-2 0-6 1-9z" class="C"></path><path d="M517 495l3 1v2 1h-1-1v1h-1c-1 0-1-1-2-1l2-2-1-1 1-1z" class="H"></path><path d="M517 527h2v-3h0v-1c0-3-1-6 1-8h0v3 7 4 1c-2 0-2 1-3 1v-4z" class="B"></path><path d="M510 514l1-1h1l1 2c2 0 2 0 4 1v11 4h-1c-1-1-2-1-3-1s-1 0-1 1v1c-1 1-2 1-3 1l3-3-1-1v-2h-4c1-2 1-3 1-5s1-2 2-3h1v-1l1-3-2-1z" class="e"></path><path d="M502 513v-4h2l1 1v-1h3c1 0 1 1 1 1v3h-1c1 1 1 1 1 2l1-1 2 1-1 3v1h-1c-1 1-2 1-2 3s0 3-1 5l-1 1h1c0 1-2 1-3 2v1h0c-3-3 0-12-1-16-1-1-1-2-1-2z" class="b"></path><path d="M501 513h1s0 1 1 2c1 4-2 13 1 16h0v-1c1-1 3-1 3-2h-1l1-1h4v2l1 1-3 3c-1 0 0 0-1 1l-1 1 1 1h-1l-1 2h1v3 1 1h-1 0c0-1 0-1-1-1-1 1 0 5 0 7l1 1v1h-1l-1-2c-2 1-2 3-3 3-2-1-2-2-2-4v-6c1-1 1-2 1-4 0-1 0-3-1-4v-2-14-4s1 0 1-1h1z" class="B"></path><path d="M501 513h1s0 1 1 2c1 4-2 13 1 16h0v-1c1-1 3-1 3-2h-1l1-1h4v2c-1 0-2 0-3 1s-2 1-1 2v1l-3 1-2-2h0-1c-2-6-1-13-1-19h1z" class="U"></path><path d="M518 448c1-1 2-1 3-2v33 4l-1 1-1 1v2h1v1c1 2 0 4 1 7l-1 1-3-1-1 1v-3l-1-1-2-2-1 2c-1-1-2-1-2-2-1 0-1 0-2-1l1-1h1v-2c0-2-1-2-2-2 0-1-1-2-1-3l-1-1c-1-1-1-1-2-1l-1-1c-1-2 0-4 0-6h0c0-2 0-3 1-4h1 0v-1c-1 0 0 0-1-1v-1c0-1 1-2 1-2 1 0 1 1 2 0v-1l1-2 1 2h1c1-1 1-1 1-2 2 0 2 0 3 1l3-2v-2c1-2 0-6 1-9z" class="E"></path><path d="M518 448c1-1 2-1 3-2v33 4l-1 1-1 1v2h1v1c1 2 0 4 1 7l-1 1-3-1v-5h1l-1-1c0-1-1-1-2-1h0v-1l1-1v-1l1-2 1-1v-3c-1-3 0-6 0-8-1-2 0-4-1-6v-6-2c1-2 0-6 1-9z" class="M"></path><path d="M517 457c2 1 2 1 2 2v5l-2 1v-6-2z" class="B"></path></svg>