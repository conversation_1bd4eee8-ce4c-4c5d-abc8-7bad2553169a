<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="122 72 790 900"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#d6d1c2}.C{fill:#e0dbcc}.D{fill:#b7b4a7}.E{fill:#b2aea1}.F{fill:#cec9b9}.G{fill:#c9c5b8}.H{fill:#d6d1bf}.I{fill:#9f9c91}.J{fill:#aaa698}.K{fill:#86847d}.L{fill:#c5c1b6}.M{fill:#282725}.N{fill:#d4d0c1}.O{fill:#373733}.P{fill:#dbd7c9}.Q{fill:#a19f94}.R{fill:#21211f}.S{fill:#1f1e1d}.T{fill:#a09e96}.U{fill:#232320}.V{fill:#bfbaad}.W{fill:#e4decc}.X{fill:#86847b}.Y{fill:#2d2d2a}.Z{fill:#4e4e48}.a{fill:#a09d90}.b{fill:#706f67}.c{fill:#969386}.d{fill:#7a786d}.e{fill:#42413e}.f{fill:#bbb9ad}.g{fill:#5e5d58}.h{fill:#8c8a7f}.i{fill:#3f3f3b}.j{fill:#585752}.k{fill:#75746e}.l{fill:#63625c}.m{fill:#54524e}.n{fill:#6a6862}.o{fill:#464540}.p{fill:#151516}.q{fill:#e7e3d5}.r{fill:#8f8e87}.s{fill:#f0ecdd}.t{fill:#7f7d73}.u{fill:#161514}.v{fill:#070707}.w{fill:#f1ebd7}</style><path d="M593 747v-2h1l1 1-2 1h0z" class="R"></path><path d="M641 373h2c1 1 1 1 0 3h-1l-1-1v-2z" class="s"></path><path d="M608 548h0l1 1c0 1-1 2-1 3h-2v-1c0-1 1-2 2-3z" class="Y"></path><path d="M637 346c1 0 1 0 2 1l-1 2c-1 0-1 0-2-1v-1l1-1z" class="s"></path><path d="M727 836h2l1 2-2 1h-1l-1-1 1-2z" class="e"></path><path d="M658 380h2c0 2 0 2-1 3h-1c-1-1-1-1-1-2l1-1z" class="C"></path><path d="M339 801h1l1 2-1 1-1 1-2-2 2-2z" class="I"></path><path d="M396 270h1l1 1c0 1 0 1-1 2h-2v-1l1-2zm252 459h2l1 1c0 1 0 1-1 2h-2v-1-2z" class="C"></path><path d="M813 905h1c1 2 0 2-1 3l1 1-1 1-2-1c0-2 1-2 2-4z" class="S"></path><path d="M310 851h1c2 1 2 1 2 2l-1 1-2 1v-1c-1-2 0-2 0-3z" class="f"></path><path d="M300 521c1 0 1 1 2 1v1l-1 2h-1l-2-1c0-2 1-2 2-3z" class="Y"></path><path d="M331 652c2 0 2 0 3 1 0 2 0 2-1 3h-1l-2-2 1-2z" class="L"></path><path d="M395 524h3c0 2 0 2-1 3h-2v-1-2z" class="s"></path><path d="M718 932h9c-2 1-5 2-8 2l-1-2z" class="M"></path><path d="M565 109c1-1 1 0 2 0l1 1-1 2h-2c-1-1-1-1-1-2l1-1z" class="s"></path><path d="M364 737h1l1 1v1l-1 1h-1c-1-1-1-1-1-2l1-1z" class="f"></path><path d="M484 134v3c-1 1-1 2-3 2h0c0-1 0-1 1-2l-1-2c1-1 2-1 3-1z" class="Y"></path><path d="M302 872c2 0 2 0 3 1v2l-1 1c-2-1-2-1-3-2v-1l1-1z" class="J"></path><path d="M786 618h2l1 1c0 2-1 2-2 3h-1c-1-1-1-1-1-2l1-2z" class="R"></path><path d="M721 388c1 0 0 0 1 1l1 2-2 2c-1 0-1 0-2-1 0-2 1-3 2-4z" class="Y"></path><path d="M721 697h1c1 0 1 0 2 2l-1 1-2 1-1-1v-2l1-1z" class="C"></path><path d="M614 581c2 0 2 0 3 1v3h-1c-1 0-1 0-2-1 0-1-1-1 0-3z" class="s"></path><path d="M689 804h1l2 2c0 1-1 1-2 2-2-1-2-1-3-2v-2l1 1h0l1-1z" class="Y"></path><path d="M717 355h2l1 1c0 2 0 2-1 3h-2l-1-1c0-1 0-1 1-3z" class="C"></path><path d="M646 755h1l-1 1 1 1 1-1h1v1l-1 1c-1 0-1 1-2 1l-1 1-1-1v-1l1-2 1-1z" class="Y"></path><path d="M643 257c1-1 1-1 3-2 1 1 2 1 3 2-1 1-2 2-3 2-2-1-2-1-3-2z" class="D"></path><path d="M379 296c2 0 2 0 3 1 0 2 0 2-2 3h-1c-1 0-1-1-1-2s0-1 1-2z" class="s"></path><path d="M816 517h1l1 1c0 1 0 2-1 3h-1l-1-1c0-2-1-2 1-3z" class="C"></path><path d="M240 864h2c0 1 1 2 2 3l-2 1h-1-1v1h-3l1-1c1-1 1-2 2-3v-1z" class="S"></path><path d="M410 92c2 0 2 0 3 1 0 1 0 1-1 3h0c-2 0-2-1-3-2 0-1 0-1 1-2z" class="s"></path><path d="M733 283h0c1 0 1 0 2 2 0 1 0 1-1 2-2 0-2 0-3-1v-2l2-1z" class="C"></path><path d="M249 463h1l2 2c-1 1-1 2-2 3h-1c-1-1-2-1-2-2l2-3z" class="S"></path><path d="M771 887c1 0 1 0 2-1l1 2c1 1 1 1 0 3h-2l-1-1c-1-1-1-2 0-3z" class="C"></path><path d="M320 804h2c0 1 0 2 1 3-1 1-2 0-3 1-1 0-2 1-2 2l-1 1h0v-1-2c1-1 1-1 2-1 0 0 0-2 1-3z" class="S"></path><path d="M768 304l1-1c1 0 1 1 2 2 0 2 0 2-1 3h-2l-1-1c0-2 0-2 1-3z" class="C"></path><path d="M221 922c2 0 3-1 5 0 1-1 2-1 3-1l1 2c-1 0-1 0-2 1h-1-4 0c-1 0-1-1-2-2z" class="V"></path><path d="M379 730c1 0 2-1 3 0h1l2-1-1 3-2 4-3-6z" class="T"></path><path d="M658 630h3c1 1 1 1 1 3 0 1-1 1-2 2h0c-2 0-2 0-3-2 0-1 0-1 1-3z" class="P"></path><path d="M286 439l2-1c2 1 2 2 3 3-1 1-1 2-2 3h-1l-3-2c0-2 0-2 1-3z" class="S"></path><path d="M649 257v4l-4 2c-1-1-2-1-2-3-1-1-1-2 0-3 1 1 1 1 3 2 1 0 2-1 3-2z" class="r"></path><path d="M306 902l3-1v-1l1-1c2 0 3 2 5 3 2 0 4-2 6-3-1 2-2 3-4 4-1 0-1 0-2 1-3-1-6-2-9-2z" class="U"></path><path d="M391 608c2 5 3 10 4 15l-2-2c-1-2-1-4-2-7 0-2-1-4 0-6z" class="T"></path><path d="M623 310l1-1c2 1 2 2 2 3 0 2 0 2-1 3h-2c-2 0-2-1-2-2 0-2 1-2 2-3z" class="D"></path><path d="M259 417c1-1 1-1 3 0l2 2c-1 2-1 2-2 3h-1c-2 0-2 0-3-1 0-3-1-3 1-4z" class="C"></path><path d="M398 626v-2l1-1c2 2 3 3 3 5l1 2v1l-3 1c-1 0 0 1-1 0v-2c0-2 0-2-1-4z" class="P"></path><path d="M820 923c3-1 6 0 9 2l3 2c-2 1-2 1-5 0-2 0-3 0-4-1l-3-3z" class="G"></path><path d="M508 935h1 5 0c-2 2-5 2-7 2l-18 1-1-1c5-2 12-1 17-1 1 0 2 0 3-1z" class="a"></path><path d="M140 929h5 7 1l2 2h-18l2-2h1z" class="T"></path><path d="M867 926c2 0 4-1 6-1l1 1c1 1 2 1 3 1l4-1c2 0 2 0 3 1h-1-2l-3 1c-1 1-3 1-5 1h0c-2 0-3 0-5-1h0l3-1v-1c-2 1-2 1-4 0zm-209-96h2c1 1 2 1 2 2 0 2 0 2-1 4h-2-1l-3-3c1-2 1-2 3-3z" class="V"></path><path d="M784 566c1 0 2 0 3 1 0 1 1 2 0 4 0 1-1 1-2 2-1 0-2-1-3-1-1-2-1-2-1-4 1-1 1-2 3-2z" class="L"></path><path d="M126 930v-3c4-2 9 0 13-1h0l1 3h-1c-5 0-9 0-13 1z" class="k"></path><path d="M823 926c1 1 2 1 4 1 0 1-1 2-2 4-4 0-9-1-14-1 1-1 3-1 4-1 0 0 1-1 1-2l1 1c1 0 2 1 3 0l-1-1c1-1 3-1 4-1z" class="J"></path><path d="M292 689c2 0 2 0 3 1 2 1 2 2 2 4l-2 2h-3l-2-4c1-1 1-2 2-3z" class="P"></path><path d="M640 754c2 0 1 2 3 3v2l-2 1v1 1h-1-1c0 1-1 1-1 0h-1l1-2h1v-1-1c-1-1-1 0-2-1h0-1l-1 1-1 1-1-1h0l-1-1 1-1h1 0 1c1-1 1 0 2 0s3-2 3-2zm-34 25s1 0 1 1v2c-1 1-1 2-3 3l-1 1h0l-2-1-1 1-1 2c-1 1-2 1-4 1l-1-1v-1c1 0 2 0 3-1v-1l1-1h1l1-1h2 2c1-1 1-1 1-3l1-1z" class="S"></path><path d="M299 275h4c1 2 1 2 1 4 0 1 0 2-2 2-1 1-2 0-3 0-1-1-2-2-2-3 1-2 1-2 2-3z" class="C"></path><path d="M636 82h3l2 4c-1 2-1 2-2 3-2 0-3 0-4-1-1 0-2-1-1-2 0-2 1-3 2-4z" class="P"></path><path d="M625 591l7 17c0 2 0 3-1 4-1-2-1-4-2-6s-2-4-3-5v-1h0c-1-4-1-6-1-9z" class="N"></path><path d="M686 615h3c1 1 1 2 2 4-1 1-1 2-2 3l-2 1s-1 0-1-1c-2 0-2-2-2-3 0-2 0-2 2-4z" class="D"></path><path d="M380 389c2 0 2 0 4 1 1 0 1 2 2 4l-3 3h-1c-1 0-2-1-3-1 0-1-1-3-1-4 1-2 1-2 2-3z" class="l"></path><path d="M288 902h3c1 1 1 2 1 3l1 1h-1c-1 0-1 0-2 1l2 2-1 1c-1 0-2-1-3-1s-2-1-3-1v-3c1-2 1-2 3-3z" class="B"></path><path d="M288 909v-3h1l1 1 2 2-1 1c-1 0-2-1-3-1z" class="P"></path><path d="M288 902h3c1 1 1 2 1 3h0c-1 0-2 1-2 0-2-1-2-1-2-3z" class="q"></path><path d="M509 72v-1c2-5 1-13 2-18 0-2-1-5 0-7l1 15c0 4 0 8 2 12h-1l-1-1h-3zm336 853c3 0 7-1 9-1s3 3 5 3c1 1 5 0 6 0v1c-4 0-7 1-10 0-2-1-5 0-6 1l-3-3-1-1z" class="V"></path><path d="M591 776h3v2l-2 2v1c1 2 1 3 1 5l-1 1h-1c-1 1-1 1-1 2 1 1 1 2 2 3h1c1 1 0 1 1 3h-2c0-1 0-1-1-2h-2v-3-3l1-1-1-1 1-2v-2h-1c1-1 1-2 1-2l1-1v-2z" class="M"></path><path d="M126 930c4-1 8-1 13-1l-2 2h-12c-4 0-7-1-10 0-3 0-8-1-11-1 3-1 6 0 9-1h4c3 0 6 0 9 1z" class="I"></path><path d="M722 268l1-1c1 0 2 0 3 1s2 2 1 4c0 2-1 2-2 3s-2 1-3 0-2-2-2-3c0-2 0-3 2-4z" class="B"></path><path d="M586 829h4v3l1 8c-1 1-1 0-1 0-1 0-1 0-1 1l-3-3h-1v-1c-1-1-1-2-1-3v-1l2-4z" class="G"></path><path d="M586 829h4v3c-2 0-2 1-4 0v-3z" class="q"></path><path d="M369 437c-1-1-1-1-1-2 0-2 2-3 3-4h2c2 0 3 1 3 3v2c-1 2-1 3-3 4h-1c-1-1-2-1-3-3h0z" class="S"></path><path d="M369 437h2c0-1 0-2 1-3 1 1 1 2 2 3l2-1c-1 2-1 3-3 4h-1c-1-1-2-1-3-3h0z" class="M"></path><path d="M867 926c2 1 2 1 4 0v1l-3 1h0c2 1 3 1 5 1h2l1 2c-5 0-14 1-19-1l1-1h-1c-3 0-6 0-10 1h-1c-2 0-3 0-4-1h7c1-1 4-2 6-1 3 1 6 0 10 0v-1l2-1z" class="r"></path><path d="M629 760c1-1 1 0 2 0 1 1 2 1 3 2l1-1 1 1c0 1 0 1-1 2v1h-1c-1 0-1 0-2-1v2l-1-1h0l-1 1c0 1 0 1-1 2s0 1-1 2c-1 0-2 0-3-1l-1-1h0l-1-1v-2-1c1-1 1-1 2-1h2v1c-1 1 0 0 0 2l3-2h0 1l-2-2v1h-1-1c1-2 0-2 2-3z" class="M"></path><path d="M590 736c2 1 2 1 3 2l-2 2c0 1 0 2 1 3v4h1 0c1 1 1 1 0 2v2c-1 1-2 2-2 3-1 1-1 2-1 3h2v6 3h0l-1-1h0v2 2 2 4c-1-1-1-5-1-7v-3-9l-1-1c0-1 0-2 1-2 1-1 1-1 1-2 1-2-1-2-2-4l1-1c0-1 0-2-1-3 0-1-1-1-1-2l1-1c1-1 1-2 1-4z" class="U"></path><path d="M873 929h0c2 0 4 0 5-1l3-1h2 1 0c1 1 3 1 4 1 3-1 6-1 9-1v1c-2 1-4 1-6 1h3c3 1 5 0 7 0 5 0 10 0 15 1-3 0-10 1-13 0h0c-4 2-8 0-11 0s-5 1-7 1h-9l-1-2h-2z" class="T"></path><path d="M193 924c3 0 6 0 8 2 2 0 5 0 7-1h0l2 1h0c1-1 0-1 1-1l1 2-1 1h-1l-1 1h1l1 1c-6-1-13 0-18 0h-2l-1-2h0 5c-1-2-1-3-2-4z" class="Q"></path><path d="M193 924c3 0 6 0 8 2 2 0 5 0 7-1v3h-1c-4-1-8 1-12 0-1-2-1-3-2-4z" class="D"></path><path d="M832 927c2-1 4 0 7-1 2 0 4-1 6-1l1 1 3 3h-7c1 1 2 1 4 1h1c4-1 7-1 10-1h1l-1 1c-5 0-10 1-15 1-6-1-11-1-17 0 1-2 2-3 2-4 3 1 3 1 5 0z" class="E"></path><path d="M842 929l-1 1-1-1h1v-1c0-1 1-1 1-2h4l3 3h-7z" class="I"></path><path d="M139 926c2-1 4-1 6 0 2 0 4 1 6 0 4-1 4 0 7 1 1 0 4-1 5-1h1 2l1 1h-2l1 1h5 0l2 2h-3c-4 1-11 1-15 1l-2-2h-1-7-5l-1-3z" class="D"></path><path d="M153 929c1-1 2-1 3-1s2 0 3 1h5 0c1 1 1 1 2 1h4c-4 1-11 1-15 1l-2-2z" class="I"></path><path d="M800 924c1 0 3 1 5 1h1c4 0 10-1 14-2l3 3c-1 0-3 0-4 1l1 1c-1 1-2 0-3 0l-1-1c0 1-1 2-1 2-1 0-3 0-4 1h-4c-1-1-1 0-1-1l-1 1c-1 1-2 0-4 0l-2-1 2-2c-1-1 0-1-1-1v-1-1z" class="V"></path><path d="M163 926c6-3 8-1 14 1l2 1 2-2c5-3 7-3 12-2 1 1 1 2 2 4h-5 0l1 2h2c-3 1-14 1-16 0h-4l-2-2h0-5l-1-1h2l-1-1h-2-1z" class="L"></path><path d="M177 930c-1-1-2-1-3-1-2-1-3-2-4-2s-1 0-2-1l1-1c1 1 1 1 2 1 2 1 4 3 7 3 1 0 2-1 3 0h3l1-1h2 0l1-1c0 1 0 1 1 2l1-1 1 2h2c-3 1-14 1-16 0z" class="h"></path><path d="M403 631h1c1-1 1-3 1-4 0-4 1-18-1-21-3 1-6 1-8 2 1-5 2-8 5-12-2 0-3 2-4 3-2 2-4 3-5 4 0-2 3-4 4-5 4-3 6-5 9-9v-1l1 43v1h0-6l3-1z" class="D"></path><path d="M412 790c1-1 2-2 4-2l1 1c1 1 2 3 2 4v2c0 2 0 3 1 5l-1 1 1 1c0 1-1 1-1 2-1 2 1 5-1 7h-6c-1 0-2 0-2-1v-1c1-2 2-3 2-4v-1-2-4-2h1c-1-2 0-5-1-6z" class="Z"></path><path d="M412 804h0c2 1 2 1 4 1h1v5h1v1h-6c-1 0-2 0-2-1v-1c1-2 2-3 2-4v-1z" class="K"></path><path d="M367 702l1-1c2 8 6 15 9 22h1 0l1-3h0 2 2l1-2c0-2 0-4 1-6 0-1-1-2 0-3l2 1c0 1 0 1 2 2l1-1c-1-1-1-2-1-2l2-1v1h1l-7 20-2 1h-1c-1-1-2 0-3 0 0 0-2-4-2-5l-10-23z" class="J"></path><path d="M391 708v1l-3 8c-1 2-2 5-3 7 0-1 0-3-1-3l-1-1 1-2c0-2 0-4 1-6 0-1-1-2 0-3l2 1c0 1 0 1 2 2l1-1c-1-1-1-2-1-2l2-1z" class="T"></path><path d="M384 718c0-2 0-4 1-6h0 2v3c-2 2-2 4-3 5h-1l1-2z" class="d"></path><path d="M392 709l1-2h1l-1 2h6v1h-6v2c0 1 0 2-1 3v1l2 2c1 0 2 0 2-1l1 1 1 1v1c1 1 1 2 1 4h1c1-1 1-1 3 0l1 1h-1v3c-2 1-5 0-7 0l-4 1c-2 0-4-1-7 1l1 1-1 2-1-1 1-3 7-20z" class="U"></path><path d="M390 723h2v1c-1 1-2 1-2 2v1h-2c0-2 1-3 2-4zm6 5h-3c1-1 2-2 2-3h3 1 2 2v3c-2 1-5 0-7 0z" class="R"></path><path d="M392 716l2 2c1 0 2 0 2-1v1l-3 6v1l-1-1v-1h-2v-4c1-1 2-1 2-3z" class="p"></path><path d="M370 614h1v3l-1 1-3 17c0 3-1 6 1 8h2c2 0 4 0 6-1h1l-1 1c4 0 8 1 11 0h1 2c1 0 1 0 2 1h1c-1-2-1-3-1-5 1-6 0-12 1-18l2 2c1 1 1 2 2 3v2h-1 0c-1 0-1 1-1 2 0 2-1 5 0 7 0 2 1 4 0 7l-1 1v1h-6-16l-1-2h-3c-2 2-1 10-1 13l-1 1-2-1c1 0 1 0 1-1h1c0-5 1-11-1-14 0-1 1-1 1-2 0-3 0-6 1-9 0-6 2-12 3-17z" class="Q"></path><path d="M596 803h1c1 1 1 1 1 2l1 1v1h1c1-1 2-1 3-2l1 1-1 1v-1l-2 1c1 2 2 4 2 6-1 1-1 1 0 2h1c1 1 1 1 0 2h-2v-3h0c-1 0-1 0-1 2-1 0-1 0-1 1-1 1-1 1-1 2h2c-1 2 0 1-1 2-1 0-2 0-2-1h-1l-1 1h-2c0 1 0 2-1 3 0 1 0 1-1 1l-2 3v-2-8l-1-2 2-1v-4c1-1 1 0 0-1v-2h0 1c1 0 2 0 3-1v-3l1-1z" class="S"></path><path d="M367 702c-1-1-1-2-1-3h7c1 1 1 1 2 1h6c0 1-1 3-1 4-1 1-1 1-1 2h0c0 3 0 5 2 7v7h-2 0l-1 3h0-1c-3-7-7-14-9-22l-1 1z" class="g"></path><path d="M375 710l2 1v2c1 0 2 1 2 1 1 2 0 5 0 6h0c0-2-1-3-2-5h0c0-2-1-4-2-5z" class="n"></path><path d="M377 711h1v-6l1-1v2c0 3 0 5 2 7v7h-2c0-1 1-4 0-6 0 0-1-1-2-1v-2z" class="k"></path><path d="M367 702c-1-1-1-2-1-3h7c1 1 1 1 2 1h6c0 1-1 3-1 4-1 1-1 1-1 2h0v-2l-1 1v6h-1l-2-1-3-6v-1h2c0-1-1-1 0-2v-1h-3c-1 0-2 0-3 1l-1 1z" class="X"></path><path d="M404 725v3h3 1c2 1 2 1 4 1l-1 1h3c0 2 0 3 2 4v-1h1c0 1 1 3 1 5-1 1-1 1-1 3l-1-1-1 1h2c1 2 1 4 0 6v1 2l1 2h-7c0-1 0-2 1-3 1-3 0-5 0-8 0-1 0-1 1-3-3 0-3 0-5-1v-1l2 1h0 2v-4l1-1h-1c-1 0-2 1-3 1-1-1-1-1-2 0h-15-6-1l1-2-1-1c3-2 5-1 7-1l4-1c2 0 5 1 7 0v-3h1z" class="l"></path><path d="M412 749h2l2-2c0-1 0 0 1-1v2 2l1 2h-7c0-1 0-2 1-3z" class="K"></path><path d="M393 712c3 0 5 0 8-1 2 0 5 1 8 0l3 1v2c0 3 1 7 0 10v4h-4-1-3v-3l-1-1c-2-1-2-1-3 0h-1c0-2 0-3-1-4v-1l-1-1-1-1c0 1-1 1-2 1l-2-2v-1c1-1 1-2 1-3z" class="R"></path><path d="M407 728c0-2 0-3 2-4h3v4h-4-1z" class="p"></path><path d="M374 603c2-6 5-12 8-17h0l1 1c-2 3-1 7-2 10v2c0 1 0 2-1 3l-1-1-2 21-2 8 1 12c-2 1-4 1-6 1h-2c-2-2-1-5-1-8l3-17 1-1v-3h-1c1-4 3-7 4-11z" class="K"></path><path d="M370 631v12h-2c-2-2-1-5-1-8 1 1 1 1 1 2l1 2h0 0c1-3 1-6 1-8z" class="t"></path><path d="M374 603c2-6 5-12 8-17h0l1 1c-2 3-1 7-2 10v2c0 1 0 2-1 3l-1-1v-9l-4 12-1-1z" class="D"></path><path d="M370 631c1-8 2-17 6-24l-1 8c-1 4-2 10 0 14v1l1 12c-2 1-4 1-6 1v-12z" class="d"></path><path d="M541 130v1c0 2-1 6 0 7h17 0-12c-2 0-3 0-4 1v1 2l-50-1-13 1c0-1 1-2 1-3v1l1-1h0c2 0 2-1 3-2v-3c0-1 0 0 1-1 0 2 0 4 1 6 1-1 1-4 1-6h1v4c1 0 5 1 7 0 0-1-1-3 0-4h0v-1h1c-1 2 0 3 0 5h4v-5l-1-1h1c1 2 1 4 1 6h1v-6h1c1 2-1 5 1 6v-5l3-1h0c0 2 0 4 1 5v1c0-2 2-3 1-5v-1h2l-1 1c0 2 0 4 1 5h0v-5c2-1 2 1 3 0l1-1 1 1v4h1c0-1 0-4 1-5h1l-1 1c0 2 0 3 1 4v-4h0v-1c2 1 1 3 2 5v-1-1l1-1s1 1 1 2h0l1-3h1c1 1 1 2 1 3v1c1-1 1-3 1-5 2 1 1 4 2 6h0c1-2 0-4 0-6h1c1 2 1 4 1 6h1v-5h1c0 1 0 4 1 5v-4h1c1 1 0 3 0 4h1c0-1 0-3 2-4 1 1 0 3 1 5h1v-7l1-1z" class="I"></path><path d="M492 141h-4v-1h54v2l-50-1z" class="s"></path><path d="M486 120c1 0 5 0 6-1h15c5 0 12-1 17 0 0 2 0 2 1 3l1 2v-2l1-1 1 1c0 2 0 4 1 6h1l-1-1v-5l1-1c1 1 1 0 2 1s2 1 3 1c1 2 0 4 1 6 0 0 4 1 5 1l-1 1h0c-5 0-11-1-15-1h-18c-8 0-16-1-24 0h0c2-1 3-1 5-1h1c0-1 0-1 1-1v-1c-1-1-1-1-1-2v-1h-1 0l-2-1v-3z" class="f"></path><path d="M521 121h2c0 2 0 3-1 5h1v1h-2v-1l-1-1h0l1-4z" class="L"></path><path d="M507 119c5 0 12-1 17 0 0 2 0 2 1 3h-1c-1 1 0 4 0 6 0-1-1 0-1-1v-1h-1c1-2 1-3 1-5h-2-2-1-1-3-7l1-1c-1 0-1-1-1-1z" class="Y"></path><defs><linearGradient id="A" x1="497.006" y1="122.819" x2="498.494" y2="117.681" xlink:href="#B"><stop offset="0" stop-color="#221d1f"></stop><stop offset="1" stop-color="#292b24"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M486 120c1 0 5 0 6-1h15s0 1 1 1l-1 1h-2v1 3h1l1-2v4h0-1v-2c-1 1-1 2-1 3l-1-1v-5h-1c-1 1 0 4-1 6l-1-1 1-5h-1-1c0 1 0 2 1 3l-1 2v1h-6c-1-1-1 0-1-1l-1 1h-2v-1c-1-1-1-1-1-2v-1h-1 0l-2-1v-3z"></path><path d="M259 920c2-1 6-1 8-2 2-2 4-3 6-4 1-1 1-1 3 0 0 2 1 2 0 4l-3 3v1 1l-1-1h-1 0-1l-1-1-1 1 1 1c1 1 2 1 2 2h1l-2 2c-1 1-2 2-3 2l-1 2-1 1 7-1h3c0-1 1-2 1-2 1-1 1-1 2-1 0 1 1 3 2 4-4 0-6 1-9 1h-1c-5 1-11 0-16-1-5 0-11 0-16-1-6-2-13-1-18-1-4 0-6 1-9 0l-1-1h-1l1-1h1l1-1-1-2c-1 0 0 0-1 1h0l-2-1c2-2 4-3 7-3s4 1 6 0c1 1 1 2 2 2h0 4 1c1-1 1-1 2-1l-1-2h1c2 1 7 2 9 2s4-3 6-3c1 0 3 1 4 2 3-2 6-2 10-2z" class="k"></path><path d="M246 926h0c2-1 2-1 3-1l1-1c1 0 2-2 3-3l1 1v1c-1 1 0 1 0 2-1 0-1 1-2 1 0 1-1 1 0 2h1 3l-1 2c-2 1-2 0-4 0 0-1 0-1-1-2v1l-1 1h-4l-1-1 1-1h0-1v-1l1-1h1z" class="H"></path><path d="M208 925c2-2 4-3 7-3s4 1 6 0c1 1 1 2 2 2h0 4c0 2 0 3-1 4h-1-2c-1 0-1-1-2-1h-3v-1l-1 1h-1l-1 1c-2 0-2 0-3-1l-1-2c-1 0 0 0-1 1h0l-2-1z" class="G"></path><path d="M229 921h1c2 1 7 2 9 2s4-3 6-3c1 0 3 1 4 2 3-2 6-2 10-2v1 1c-2 2-3 4-6 6h0-1c-1-1 0-1 0-2 1 0 1-1 2-1 0-1-1-1 0-2v-1l-1-1c-1 1-2 3-3 3l-1 1c-1 0-1 0-3 1h0v-1c-1 0-2 1-3 1l-1 1v-1h-1l-2 1v-1h-1l-1 1c-2 1-4 1-6 0-1-1-1-1-1-2-1-1-1-1-2-1 1-1 1-1 2-1l-1-2z" class="N"></path><path d="M259 920c2-1 6-1 8-2 2-2 4-3 6-4 1-1 1-1 3 0 0 2 1 2 0 4l-3 3v1 1l-1-1h-1 0-1l-1-1-1 1 1 1c1 1 2 1 2 2h1l-2 2c-1 1-2 2-3 2l-1 2-1 1c-1 0-3-1-4-2 1 0 1 0 2-1s1-1 0-2l-1-1c-1-1-2-1-3-1l-3 3h-3 0c3-2 4-4 6-6v-1-1z" class="G"></path><path d="M263 921h2c1 1 0 2 0 3h-2c0-2-1-2 0-3z" class="C"></path><path d="M383 587c3 7 5 14 8 21-1 2 0 4 0 6 1 3 1 5 2 7-1 6 0 12-1 18 0 2 0 3 1 5h-1c-1-1-1-1-2-1h-2-1c-3 1-7 0-11 0l1-1h-1l-1-12 2-8 2-21 1 1c1-1 1-2 1-3v-2c1-3 0-7 2-10z" class="B"></path><path d="M384 602c0-2-1-3-1-5l1-1 1 1c0 2 1 5 1 7l-2-2z" class="N"></path><path d="M384 602l2 2v12c-1 4-2 9-1 13l-1 1v-7c0-7 2-14 0-21zm3 40l-1-20 1-16 1-1c1 1 0 3 0 4l-1 13 1 12v9h-1v-1z" class="C"></path><path d="M391 614c1 3 1 5 2 7-1 6 0 12-1 18 0 2 0 3 1 5h-1c-1-1-1-1-2-1 0-9-1-19 1-29z" class="f"></path><path d="M381 599c1 4 2 9 1 14-1 3-1 6 0 9v5c0 5-1 10 0 14l1 1c1-4 0-11 0-14 0-2 0-4 1-5v7l1-1v1 12l1 1 1-1v1c-3 1-7 0-11 0l1-1h-1l-1-12 2-8 2-21 1 1c1-1 1-2 1-3z" class="E"></path><path d="M385 629v1 12l1 1 1-1v1c-3 1-7 0-11 0l1-1h1 6v-12l1-1z" class="N"></path><path d="M381 599c1 4 2 9 1 14-1 3-1 6 0 9v5c-3 4 1 11-1 15-2-2-1-5-1-7 0-5 0-9-1-14h0l1-19c1-1 1-2 1-3z" class="G"></path><path d="M379 601l1 1-1 19c-2 4-1 11-1 15v6h-1-1l-1-12 2-8 2-21z" class="T"></path><path d="M375 630l2-8c1 5 0 10 1 14v6h-1-1l-1-12z" class="I"></path><path d="M744 916c4 1 8 3 12 3 1 1 3 0 4 0 2 0 3 1 4 2 1-1 1-1 3 0h2l1 1h1 12 1 1 0c1 0 1 0 2-1 1 1 2 1 3 2 2-1 4-1 7-1 0 1 1 1 2 1l1 1h0v1 1c1 0 0 0 1 1l-2 2 2 1h-16c-2 0-4-1-7-1-2 0-5 1-7 2-6 2-13 1-19 1-3 0-5 1-8 1s-6-1-9-1h-1v-2h1 1 0l-1-1 1-1-1-1v-1h-1c-1 0-2-1-2-2h-1c-1-1-2 1-3 2l-1-1c2-1 3-3 5-4 1 0 3-1 4-1h1c2-2 5-1 7-1l-1-1c0-1 1-2 1-2z" class="l"></path><path d="M770 922h1v1c-1 1-1 2-1 3h0c1 0 1 1 2 1-1 1-2 1-4 2l2-1v-1h-2l-1 1c-1 0-3-1-3-2 0 0 0-1 1-2s2 0 4 0c0-1 1-1 1-2z" class="C"></path><path d="M785 922h0c1 0 1 0 2-1 1 1 2 1 3 2v3c0 1 1 1 1 2h-4l-1-1c-1 1-1 1-2 1-1-1-1-1-1-3l1-1v-1l1-1z" class="N"></path><path d="M790 923c2-1 4-1 7-1 0 1 1 1 2 1l1 1h0v1 1c1 0 0 0 1 1l-2 2h-1v-1c-1 0-2-1-4 0-1 0-2-1-3-2h-1v-3z" class="C"></path><path d="M799 923l1 1h0v1 1c1 0 0 0 1 1l-2 2h-1v-1c-1 0-2-1-4 0-1 0-2-1-3-2 1 0 2 0 3 1h1c1-1 3-2 4-4z" class="N"></path><path d="M771 922h12 1 1l-1 1h-1l-3 3v1h-5c0-1 0-1-1-2v2c-1-1-2-1-3-2v-2-1z" class="F"></path><path d="M747 929l-1-2 1-1v-1h2l1 1v-1l2 1c0-1 1-2 2-3 0 0 1 1 2 1h1 3c1 1 1 2 3 2v1 1h2v1h-5c-1 1-1 0-2 0s-2 2-4 1l-1-2v2c-2 0-1-1-2-1s-1 1-2 0h-2 0z" class="C"></path><path d="M744 916c4 1 8 3 12 3 1 1 3 0 4 0 2 0 3 1 4 2 1-1 1-1 3 0h2l1 1c0 1-1 1-1 2-2 0-3-1-4 0s-1 2-1 2l-1 1v-1c-2 0-2-1-3-2h-3-1c-1 0-2-1-2-1-1 1-2 2-2 3l-2-1v1l-1-1h-2v1l-1 1 1 2h-1c1 1 1 1 1 2h-1c-1-1 0-1-1-1 0-1-2-2-2-2l-1 1c-1 0-2-1-3-1-2 0-2 0-4-2h-1c-1 0-2-1-2-2h-1c-1-1-2 1-3 2l-1-1c2-1 3-3 5-4 1 0 3-1 4-1h1c2-2 5-1 7-1l-1-1c0-1 1-2 1-2z" class="F"></path><path d="M737 920c2-2 5-1 7-1h1 1v2h1v1h-2v1c1 1 2 0 3 0v1h0c-3 0-5 1-6 0h-2c-1 1-1 1-1 2l-1 1h1 1l-1 1c-2 0-2 0-4-2h-1c-1 0-2-1-2-2h-1c-1-1-2 1-3 2l-1-1c2-1 3-3 5-4 1 0 3-1 4-1h1z" class="C"></path><path d="M577 786v-1l1 1c0 1 1 1 2 1h1l1 1 1 1v1c2 0 4 1 6 0v3h2c1 1 1 1 1 2h2c-1 1-2 0-3 1l-1 3v1l1 2c1 0 2 1 4 1h0 1l-1 1v3c-1 1-2 1-3 1h-1 0v2c1 1 1 0 0 1v4l-2 1 1 2v8 2 1h-4l-2 4v1c-1 0-1-1-2-1s0 0-1-1l-6-3h-1c-1 0-2 0-4-1h1l-2-2h-1l-1-2c0-1 0-1 1-2v-1c-1 0-1 0-1-1 1-3 0-6-1-9h-1l-1-1-1-2c-1-2 0-2-1-3 0-1-1-2-1-2l-1-3c1-1 1-2 1-3h-1l-1-1c1-1 1-2 2-3-2-1-6 0-8 0v-1h8c1-1 1-3 1-4h1c1 1 1 2 3 2 0 0 1-1 2-1h2 1l1 1v1h1l1-2v2h1c0-1 0-2 1-3h1v-2z" class="r"></path><path d="M589 793h2c1 1 1 1 1 2h2c-1 1-2 0-3 1l-1 3-1-3-1-1 1-1v-1z" class="S"></path><path d="M561 803c3-3 10-1 14-1 0 1 0 1-1 2-1 0-1 1-2 2h0c0 1 0 1-1 2v1h2c-1 1-1 1-1 2h1c1-1 3-3 3-4 1 0 1-1 2 0-2 2-4 4-5 7h0l-1 1c-1 3-2 6-2 9l-1 2h-1l-1-2c0-1 0-1 1-2v-1c-1 0-1 0-1-1 1-3 0-6-1-9h-1l-1-1-1-2c-1-2 0-2-1-3 0-1-1-2-1-2z" class="m"></path><path d="M568 821c1-2 3-7 5-8v1l-1 1c-1 3-2 6-2 9l-1 2h-1l-1-2c0-1 0-1 1-2v-1z" class="u"></path><path d="M561 803c3-3 10-1 14-1 0 1 0 1-1 2-1 0-1 1-2 2v-2h-1v2c-1 0-1-1-2-1l-1-1h-3 0l2 1v1c-1 1-1 3-1 5h-1l-1-1-1-2c-1-2 0-2-1-3 0-1-1-2-1-2z" class="R"></path><path d="M577 786v-1l1 1c0 1 1 1 2 1h1l1 1v2c-1 1-1 1-3 1h-1l-1 6v1 1h1v-1h1c0 1 0 1-1 2l-1 1v-1h-1c-5 1-10 0-15 0h-1c1-1 1-2 1-3h-1l-1-1c1-1 1-2 2-3-2-1-6 0-8 0v-1h8c1-1 1-3 1-4h1c1 1 1 2 3 2 0 0 1-1 2-1h2 1l1 1v1h1l1-2v2h1c0-1 0-2 1-3h1v-2z" class="g"></path><path d="M568 791h1v-1h1v2h1 2v1l1 1v-1h1v2 1h-2v1l-1-1h-1c-2-1-4 0-6 0 0-2 2-2 2-5h1z" class="S"></path><path d="M563 788c1 1 1 2 3 2 0 0 1-1 2-1v2h-1c0 3-2 3-2 5 0 1-1 1-1 2l-3 2h-1c1-1 1-2 1-3h-1l-1-1c1-1 1-2 2-3-2-1-6 0-8 0v-1h8c1-1 1-3 1-4h1z" class="M"></path><path d="M561 793c1 0 1 1 3 1v4l-3 2h-1c1-1 1-2 1-3h-1l-1-1c1-1 1-2 2-3z" class="S"></path><path d="M580 808c0-1 0-2 2-3h0l1-1c-1-1-1-2-2-2l1-1v-2c1-1 1-1 2-1l-1 1v3h0c1 1 1 0 2 1v-4h0 1c1 1 2 1 3 2l1-1 1 2c1 0 2 1 4 1h0 1l-1 1v3c-1 1-2 1-3 1h-1 0v2c1 1 1 0 0 1v4l-2 1 1 2v8 2 1h-4l-2 4v1c-1 0-1-1-2-1s0 0-1-1l-6-3h-1c-1 0-2 0-4-1h1l-2-2 1-2c0-3 1-6 2-9l1-1h0c1-3 3-5 5-7l2 1z" class="Q"></path><path d="M578 807l2 1 1 3v2h0v1c-3 2-1 4-3 6h0-1-1c-3-2-1-2-1-4l-1-1-1-1c1-3 3-5 5-7z" class="I"></path><path d="M580 808c0-1 0-2 2-3h0l1-1c-1-1-1-2-2-2l1-1v-2c1-1 1-1 2-1l-1 1v3c0 1 1 1 2 2 0 0 1 0 2 1h-5v5c3 1 5 1 8 1 1-1 1 0 1-1 1 1 1 0 0 1v4l-2 1-1-1h0-1v1h-3c-1-1-1-1-1-2l-1-1c-1 3 0 7-1 10h-1l1-9v-1h0v-2l-1-3z" class="O"></path><path d="M569 826l1-2c0-3 1-6 2-9 1 2 1 4 3 5v1h1c1 1 1 1 2 1v1h-1v6h1v-4-1c1 2 0 5 1 6s2 0 2 2l-6-3h-1c-1 0-2 0-4-1h1l-2-2z" class="m"></path><path d="M584 816h3v-1h1 0l1 1 1 2v8 2 1h-4l-2 4h0v-1-1c0-2-1-8 0-10v-1-2h1l-1-1v-1z" class="V"></path><path d="M590 826h-5c0-3 1-5 1-8h4v8z" class="N"></path><path d="M583 802h0c1 1 1 0 2 1v-4h0 1c1 1 2 1 3 2l1-1 1 2c1 0 2 1 4 1h0 1l-1 1v3c-1 1-2 1-3 1h-1 0v2c0 1 0 0-1 1-3 0-5 0-8-1v-5h5c-1-1-2-1-2-1-1-1-2-1-2-2z" class="E"></path><path d="M591 802c1 0 2 1 4 1h0 1l-1 1v3c-1 1-2 1-3 1h-1 0l-3-3c1-1 1 0 2-1s1-1 1-2z" class="M"></path><path d="M366 683h2 8 3l6 1h6v2c1 1 1 2 2 3h4v1c2 1 4 3 6 3 1 0 1 0 2-1l1 1h0c1 2 1 2 1 4v1 1h8 1 1c2 0 3 0 4-1l10 1h2v1h-3l-1 2-3-1h0-1c-1 0-2 0-3 1-1-1-1 0-3-1 0 0-1 0-2-1-1 1-2 1-2 3h1l1-1 1 1 1-1 1 1c1-1 2-1 3-1v1h7v8h-1c-1-2-1-3-1-4h0c0 1 0 3-1 4-1-1-1-1-1-2h0l-1 2h-1v-1c-1 0-1 0-1 1-2 0-2 0-2-1-1 0-1 0-1 1h-1-10c-3 1-6 0-8 0-3 1-5 1-8 1v-2h6v-1h-6l1-2h-1l-1 2h-1v-1l-2 1s0 1 1 2l-1 1c-2-1-2-1-2-2l-2-1c-1 1 0 2 0 3-1 2-1 4-1 6l-1 2h-2v-7c-2-2-2-4-2-7h0c0-1 0-1 1-2 0-1 1-3 1-4h-6c-1 0-1 0-2-1h18c-1-1-2-1-4-1-3 0-8 1-11 0-2-1-4 0-6 0h-4 0l-1-1v-3h1l1-1c-2 0-2 0-3-1l1-1 1-1v-7z" class="I"></path><path d="M387 708c1-2 1-3 2-4 0 0 1 0 1 1s1 2 1 3l-2 1s0 1 1 2l-1 1c-2-1-2-1-2-2v-2z" class="d"></path><path d="M381 700c2 0 4 0 6 1-1 1-1 2-2 3 0 1 1 2 0 3l-1-1-1 1 1 2 1-1v-1c1 0 2 1 2 1v2l-2-1c-1 1 0 2 0 3-1 2-1 4-1 6l-1 2h-2v-7c-2-2-2-4-2-7h0c0-1 0-1 1-2 0-1 1-3 1-4z" class="h"></path><path d="M381 713h0c0-3 1-7 0-9v-1c1 2 1 4 2 6l-1 3c-1 2 0 4 2 6l-1 2h-2v-7z" class="T"></path><path d="M366 683h2 8 3l6 1h6v2c1 1 1 2 2 3h4v1h-1c0 2 1 1 1 3v1c-1 1-1 1 0 2l1 1 3-3c1-1 1 0 3 0 0 3-1 6-1 9v1c-1-1-2-1-3 0h-4-2c-1-2-1-2 0-4v-1h-3c-1-1-2-1-4-1-3 0-8 1-11 0-2-1-4 0-6 0h-4 0l-1-1v-3h1l1-1c-2 0-2 0-3-1l1-1 1-1v-7z" class="k"></path><path d="M394 700l1 1h4l-3 3h-2c-1-2-1-2 0-4z" class="e"></path><path d="M378 689c1 0 1-1 2-2l1 1h1l1-1c0 1 0 1 1 1v1h-1c-1 0-2 1-3 2h-5-1l2-2h2z" class="n"></path><path d="M376 683h3l6 1h6v2l-2 3c-1 0-2 0-4-1h-1c-1 0-1 0-1-1l-1 1h-1l-1-1c-1 1-1 2-2 2l-1-1-1-1v-4z" class="U"></path><path d="M376 683h3c0 2 0 3-1 4l-1-1v2l-1-1v-4z" class="R"></path><path d="M366 683h2 8v4l1 1 1 1h-2l-2 2h-1-3l-1 1c-1-1-3-1-4-1l1-1v-7z" class="M"></path><path d="M372 689c-1-1-2-2-2-3l1-1h3c1 1 1 2 1 3l-1 1h-2z" class="S"></path><path d="M366 683h2v7c1 0 1 0 2-1h2 2l1-1 1-1 1 1 1 1h-2l-2 2h-1-3l-1 1c-1-1-3-1-4-1l1-1v-7z" class="g"></path><path d="M395 630h2 0c0 4 1 7 1 11 0 0 0 1 1 1l1 1v1l-1 1c0 4 1 9-1 13h-1-2l-1 1c0 1 0 2-1 4l1 1v1c0 1 0 2 1 3v-3h1v4 6h-2l-2 2h-2s0 3-1 3v1h0c1 1 1 2 1 2l1 1h-6l-6-1h-3-8-2c-1 0 0 0 0-1v-7-15-2l1-1c0-3-1-11 1-13h3l1 2h16 6v-1l1-1c1-3 0-5 0-7-1-2 0-5 0-7z" class="h"></path><path d="M394 645l3 1v1c-1 2 0 5-1 8v2h-1v-1h-2l1-10v-1z" class="g"></path><path d="M380 669h1l1-1v9h-2v1h-1-1l1-1c-1-2-2-4-3-5v-1l1-1c2 0 2 0 3-1z" class="E"></path><path d="M394 673c-1 0-2 0-2-1 0 0 0-3-1-3-1-1-1 0-2-1l1-1h1c0-2 0-2 1-3 0 1 1 1 2 1 0 1 0 2 1 3v-3h1v4 6h-2v-2z" class="k"></path><path d="M388 646h6l-1 10h-4c0-4-1-7-1-10z" class="o"></path><path d="M383 676v-16c-2-1-2-1-4-1l1-1h7l-1 4v2c1 4 1 8 0 11h2 0c3 1 3-1 6-2v2l-2 2h-2s0 3-1 3v1h0c1 1 1 2 1 2l1 1h-6v-1c0-1 0-1 1-2h1l-1-1h-4v-2h1v-2z" class="Q"></path><path d="M383 676l1-1v-3c1 2 0 6 2 8l1-1-1-4h2 0c3 1 3-1 6-2v2l-2 2h-2s0 3-1 3v1h0c1 1 1 2 1 2l1 1h-6v-1c0-1 0-1 1-2h1l-1-1h-4v-2h1v-2z" class="r"></path><path d="M372 646h16c0 3 1 6 1 10-5 0-11-1-16 0h-1c-2 0-3 1-4 0 0-1-1-4 0-4 0-1 1-1 1-1v-4h-1 0v-1h4z" class="O"></path><path d="M367 660l1-2h1c-1 1-1 1-1 2v3 11h1v-12-2h4v1c2 0 5 1 6 1s1-1 2 0v1 1l-1 5c-1 1-1 1-3 1l-1 1v1c1 1 2 3 3 5l-1 1h1 1v-1h2v1 2h4l1 1h-1c-1 1-1 1-1 2v1l-6-1h-3-8-2c-1 0 0 0 0-1v-7-15h1z" class="K"></path><path d="M376 672c1 1 2 3 3 5l-1 1c-2 0-2 0-4-1 0-2 1-3 2-5zm6 5v1 2h4l1 1h-1c-1 1-1 1-1 2v1l-6-1h-3-8-2c-1 0 0 0 0-1 1 0 6 0 8-1-1 0-1 0-1-1 2-1 4 0 6-2h1v-1h2z" class="Q"></path><path d="M367 660l1-2h1c-1 1-1 1-1 2v3 11h1v-12-2h4v1c2 0 5 1 6 1s1-1 2 0v1 1l-1 5c-1 1-1 1-3 1l-1 1c-1 0-2 0-4 1v6 1h-1v-15c-2 3-1 11-1 14v2c-1 0-2 0-2 1-1-2-1-5-1-6v-15z" class="Z"></path><path d="M377 670h-4c-2-2-1-5-1-7 3 0 6 0 9 1l-1 5c-1 1-1 1-3 1z" class="s"></path><path d="M509 72h3l1 1h1s1 1 1 2c5 6 9 13 12 21 1 3 3 6 3 9 2 5 3 12 6 15-2 1-2 0-3 0-3-1-6-1-9-1-5-1-12 0-17 0h-15c-1 1-5 1-6 1h1c2-7 3-16 7-22 2-5 4-10 7-15 1-4 5-8 8-11z" class="P"></path><path d="M506 103l1-11v5c1 2 1 5 1 6-1 1 0 1-1 2h0-1v-2z" class="H"></path><path d="M506 117c0-2 1-4 0-6s0-5 0-8v2h1 0c1-1 0-1 1-2 0 4-1 9 1 12v1c1-1 1 0 1-1l1 1-1 1h-4z" class="F"></path><path d="M515 75c5 6 9 13 12 21 1 3 3 6 3 9v1 3l1 1h0l-1 1c0-1-1-2-1-4 0-1-1-2-1-3l-1 1c1 4 3 8 3 12l1 1-1 1v-1h-2l-1-1v-1l2 1v-3-1l-1-2-1-2-7-22c-2-4-5-8-5-12z" class="f"></path><path d="M527 116v1l1 1h2v1l1-1-1-1c0-4-2-8-3-12l1-1c0 1 1 2 1 3 0 2 1 3 1 4l1-1h0l-1-1v-3-1c2 5 3 12 6 15-2 1-2 0-3 0-3-1-6-1-9-1-5-1-12 0-17 0h-15c-1 1-5 1-6 1h1l8-2h7 1c1-1 2-1 3-1h4l1-1c1 0 4-1 5 0h0 2 1l2 1v-1h1 1 4 0z" class="B"></path><path d="M521 117c-1-1-1-2-1-3h-1c-1 0-1 0-2 1h0c-2-1-1-1-2-1l-1 1h-1c-3-5-2-13-2-19 0-5-1-9 0-13 0-1 0-1-1-2 1-1 1-1 2-1s1 0 2 1c2 1 12 30 13 35h-4-1-1v1z" class="w"></path><path d="M513 84h1v11c1 5 2 11 1 16h0-1v-7c0-6-1-13-1-20z" class="W"></path><path d="M509 72h3l1 1c1 3 1 3 0 5l-1 1h0l1-2-2-2h-1c-1 2-1 3-1 5s0 4-1 6l-1 6-1 11c0 3-1 6 0 8s0 4 0 6c-1 0-2 0-3 1h-1-7l-8 2c2-7 3-16 7-22 2-5 4-10 7-15 1-4 5-8 8-11z" class="t"></path><path d="M494 98h1c1-3 3-8 5-10l-3 8c0 1 0 3-1 4s-1 2-2 3l-1 3-2 5v1 2h-1c0 1 0 2 1 2 1-1 1-3 2-4v-1c0-1 0-2 1-2v1h0l-1 7h1v-1c0-1 0-2 1-2 1 1 0 3 0 4l-8 2c2-7 3-16 7-22z" class="g"></path><path d="M509 72h3l1 1c1 3 1 3 0 5l-1 1h0l1-2-2-2h-1c-1 2-1 3-1 5s0 4-1 6c-1 0-1 2-2 3h0-1l-1 4-1-1c-1 3-2 4-4 5l5-10-1-1 1-1v-1c1-2 1-2 1-4l-1-1-2 5-1-1c1-4 5-8 8-11z" class="X"></path><path d="M505 89l1-2c0-2 0-4 1-6 0-1 0-1 1-2l1 1h0c0 2 0 4-1 6-1 0-1 2-2 3h0-1z" class="D"></path><path d="M505 89h1 0c1-1 1-3 2-3l-1 6-1 11c0 3-1 6 0 8s0 4 0 6c-1 0-2 0-3 1h-1v-12h0 0c-1 3-1 5-1 8h-1v-7c0-2 1-3 2-5 0-4 0-6 2-9l1-4z" class="T"></path><path d="M690 894v-1l1-1-1-1c-1 0-1-1-2-1h-1v-1c1-1 3-2 5-2 4 0 6 3 8 5 1 1 1 2 2 3h0c4 0 6 3 9 5s5 3 8 5c0 1 1 1 1 2 1 0 3 2 4 2s3-1 5-1 8 2 10 4c1 0 1 1 2 2s2 1 3 2c0 0-1 1-1 2l1 1c-2 0-5-1-7 1h-1c-1 0-3 1-4 1-2 1-3 3-5 4l1 1c1-1 2-3 3-2h1c0 1 1 2 2 2h1v1l1 1-1 1 1 1h0-1-1v2h-7-9l-15-1c-4 0-7 0-10 1l-1-1h-3l-1-1c-2-4-4-7-7-10-2-1-4-3-6-4h0c-2-1-3-2-5-2 2-1 1-1 2-3 0-1 1-3 2-4 1-2 2-1 4-1 1-1 1-1 2-1l-1-1h-2l1-1-2-2v-1l-3-2c1-1 2-1 3-2-1-1-2-1-1-3 2 0 4 0 6-1 2 0 4 1 6 1h1v-1h1v1h-1c1 1 1 1 2 1z" class="K"></path><path d="M707 911h4c1 0 1-1 2-1h1l4 2c-1 1-2 1-3 1v-1c-2 0-4 2-5 3v1h-1l-1-2c0-1-1-2-1-3z" class="H"></path><path d="M720 907c1 0 3 2 4 2s3-1 5-1 8 2 10 4c1 0 1 1 2 2s2 1 3 2c0 0-1 1-1 2l1 1c-2 0-5-1-7 1l-1-2c-2 0-6-1-8 0l-1 1h-1c-1 1 0 0-2 1h-3l-1-1h0c-2-1-4-1-5-3 1 0 2-2 3-3l2 2c0-2 1-2 1-3 1 0 1 0 1-1h2l1-1h-2-1c-1-1-2-2-2-3z" class="C"></path><path d="M735 913l2-1h0c2 1 3 1 4 2s2 1 3 2c0 0-1 1-1 2l1 1c-2 0-5-1-7 1l-1-2v-1c-1-1 0-1 0-2l1 1v1l1 1h3c0-1-1-2-2-2h-1l1-1v-1l-4-1z" class="D"></path><path d="M720 907c1 0 3 2 4 2s3-1 5-1 8 2 10 4c1 0 1 1 2 2-1-1-2-1-4-2h0l-2 1c-1 0-2 0-3-1v-1h-1l-1 1h-4-2l-1 1h3 0c-2 2-3 1-5 2h-1c0-2 1-2 1-3 1 0 1 0 1-1h2l1-1h-2-1c-1-1-2-2-2-3z" class="N"></path><path d="M681 920h2v-1l-2-2 2-4h0l1-1 2 2h0l1 2c1 1 0 1 0 2v1 1l1 1v-1c1-1 2-1 2-2l1 1c-2 1-2 1-2 4l3 1h1v2h1v-1h0v-2l1 1v1 1 1h1l1-1 1 1c1-1 1-1 1-2l1 1c0 1-1 2 0 3v-2l1 1h1 0l1-1h1v2h3 1 3c0-2-1-2-2-4h0l-2-3c-1-2-2-4-4-6-2-1-4-2-7-2h1v-1h5l2 2 1 1 2 2c1 0 1 0 2-1v1 1h0 1v-2c1 1 1 1 1 2h1c1 1 1 0 2 1s1 5 2 6l-1 1h-1v-1c0-1 1-2 0-3l-2-2c-1 1-1 1-2 1 0-1 0-1 1-1l-1-1-1 1-1 1h1c1 1 2 2 2 3h1v-2h1c0 1 0 2 1 3l-1 1 1 1h1 0c1-1 1-2 2-2v-1c0-1-1-1-1-2v-2h1c0 1 0 2 1 3v2h1c-1 1 0 1-1 2h-1l-1 1h1 0 2v-1c1-1 2-1 3-2h0c0 1-1 2-2 3h1 1c1-1 1-1 2-1v-2c1 1 1 0 1 1 1 1 2 2 4 2h4l1-1c1 0 1 1 1 1l1 1h0-1-1v2h-7-9l-15-1c-4 0-7 0-10 1l-1-1h-3l-1-1c-2-4-4-7-7-10z" class="m"></path><path d="M692 931c14-3 28 0 42-1v2h-7-9l-15-1c-4 0-7 0-10 1l-1-1z" class="V"></path><path d="M676 900l3 2c1 0 1 1 2 1l1 2c1 0 2-1 2-1l3 2h1l1-1c1 0 1 1 2 1s2 1 2 1v1 2c1 2 1 2 2 2l1 3v-1c3 0 5 1 7 2 2 2 3 4 4 6l2 3h0c1 2 2 2 2 4h-3-1-3v-2h-1l-1 1h0-1l-1-1v2c-1-1 0-2 0-3l-1-1c0 1 0 1-1 2l-1-1-1 1h-1v-1-1-1l-1-1v2h0v1h-1v-2h-1l-3-1c0-3 0-3 2-4l-1-1c0 1-1 1-2 2v1l-1-1v-1-1c0-1 1-1 0-2l-1-2h0l-2-2-1 1h0l-2 4 2 2v1h-2c-2-1-4-3-6-4h0c-2-1-3-2-5-2 2-1 1-1 2-3 0-1 1-3 2-4 1-2 2-1 4-1 1-1 1-1 2-1l-1-1h-2l1-1-2-2v-1z" class="J"></path><path d="M696 914c3 0 5 1 7 2 2 2 3 4 4 6-1 0-2 0-3 1l-1 1v-2h-2v-2c-1 0-1 0-2 1 1 1 1 1 1 2h-1l-2-1h0c-1-1-1 0 0-1v-1-1c-1 0-1 1-1 1h-2l-1-1c2 0 1 0 2-1h-3v-1l2-1 2 2c1 0 2 0 2-1-1 0-2 0-3-1l1-1v-1z" class="C"></path><path d="M672 911c0-1 1-3 2-4 1-2 2-1 4-1l2 1 1 2 1-1h0c1 0 1 1 1 2 1 0 1 0 2-1v1s1 1 1 2h0-2l-1 1h0l-2 4 2 2v1h-2c-2-1-4-3-6-4h0c-2-1-3-2-5-2 2-1 1-1 2-3z" class="X"></path><path d="M672 911c0-1 1-3 2-4 1-2 2-1 4-1l2 1 1 2c1 2 1 3 1 4l-1 1c0-1-1-2-1-3h-1l-2 1v-1l-2 2-1-1c-1 0-1 0-2-1z" class="N"></path><path d="M679 911h0c-1-1-2-1-2-2-1-1-1-1-1-2l1-1 2 2 1-1 1 2c1 2 1 3 1 4l-1 1c0-1-1-2-1-3h-1z" class="C"></path><path d="M690 894v-1l1-1-1-1c-1 0-1-1-2-1h-1v-1c1-1 3-2 5-2 4 0 6 3 8 5 1 1 1 2 2 3h0c4 0 6 3 9 5s5 3 8 5c0 1 1 1 1 2s1 2 2 3h1 2l-1 1h-2c0 1 0 1-1 1 0 1-1 1-1 3l-2-2h0v-1l-4-2h-1c-1 0-1 1-2 1h-4c0 1 1 2 1 3-1 2-1 2-3 2l-1-1-2-2h-5v1h-1v1l-1-3c-1 0-1 0-2-2v-2-1s-1-1-2-1-1-1-2-1l-1 1h-1l-3-2s-1 1-2 1l-1-2c-1 0-1-1-2-1l-3-2-3-2c1-1 2-1 3-2-1-1-2-1-1-3 2 0 4 0 6-1 2 0 4 1 6 1h1v-1h1v1h-1c1 1 1 1 2 1z" class="W"></path><path d="M695 912h2v-1l-2-1 1-2h0l2 1v1c0 1 0 1 1 2-1 0-1 1-2 1v1h-1v1l-1-3z" class="F"></path><path d="M676 896h3c0 2 1 2 1 3 0 2 0 3 1 4-1 0-1-1-2-1l-3-2-3-2c1-1 2-1 3-2z" class="H"></path><path d="M698 909c1 1 2 1 3 1s1 0 2 1c1 0 1 1 1 2l1-1v-1h2c0 1 1 2 1 3-1 2-1 2-3 2l-1-1-2-2h-5c1 0 1-1 2-1-1-1-1-1-1-2v-1z" class="D"></path><path d="M675 893c2 0 4 0 6-1 2 0 4 1 6 1h1v-1h1v1h-1c1 1 1 1 2 1h0c0 2 0 4 1 6l-1 1c-1 0-1 1-2 1h-1s-1 0-2 1c0-1-1-2-1-2-1-2-2-3-3-3l-2-2h-3c-1-1-2-1-1-3z" class="w"></path><path d="M690 894v-1l1-1-1-1c-1 0-1-1-2-1h-1v-1c1-1 3-2 5-2 4 0 6 3 8 5 1 1 1 2 2 3h0c4 0 6 3 9 5s5 3 8 5c0 1 1 1 1 2s1 2 2 3h1 2l-1 1h-2c0 1 0 1-1 1l-2-2c-1 0 0 0-1-1l1-1v-1l-2 1-2-2h-4s-1-1-1-2h0c-2 0-1-1-2-1h-3c-1 0-1 0-1 1h-1c-1 0-1 0-2 1-1 0-2 1-2 1h-1c0-1-2-1-3-2v-2c1-1 1-2 1-3v-1l-1-1-1 1c-1-1-1-1-1-2 1 0 0 0 1-1l-1-1h-3 0z" class="C"></path><path d="M418 657h2l1 1c0 1 0 3-1 4l1 1c1 0 3-1 4 0h0v1h1c1 0 1 1 2 1h0c1 1 1 1 2 1 1 1 1 1 3 1 1 0 1 1 2 1 1 1 2 1 3 1v1 1h2c1 1 2 1 2 3 1 1 1 2 1 2 0 1 0 1 1 2v1l2 2h0c0 1 0 4 1 5l2-8c1-4 4-6 8-8l-1 1c-2 3-5 5-6 9l1 1v2c-1 1-1 1-2 1 0 2 1 3 0 3-3 5-1 10-3 14h0-3c-1 0-2 1-3 2v2l-1 2v-3c-2 1 0 2-2 3v-3h-1l-1 1h-1v5 1c-2-2-1-4-1-6-2 2 1 4-2 6h-1v-8h-7v-1c-1 0-2 0-3 1l-1-1-1 1-1-1-1 1h-1c0-2 1-2 2-3 1 1 2 1 2 1 2 1 2 0 3 1 1-1 2-1 3-1h1 0l3 1 1-2h3v-1h-2l-10-1c-1 1-2 1-4 1h-1-1-8v-1-1c0-2 0-2-1-4h0l-1-1c-1 1-1 1-2 1-2 0-4-2-6-3v-1h-4c-1-1-1-2-2-3v-2l-1-1s0-1-1-2h0v-1c1 0 1-3 1-3h2l2-2h2v-6-4h-1v3c-1-1-1-2-1-3v-1l-1-1c1-2 1-3 1-4l1-1h2 1 15l4-1h1z" class="p"></path><path d="M432 672c1 1 2 1 3 2v1h0c-1-1-2-2-4-2s-3 0-4 2c-1 1-2 3-3 4v-2h-1c-1 1-1 2-3 2v2h-1c-1-1-1-2-1-4l2-1v-3c2 0 2 1 3 2h1 1c3-2 4-3 7-3z" class="j"></path><path d="M424 681l5 2c1-1 2-3 3-2 2 0 2 1 2 2h0c0 1 0 2-1 3l1 1c0 1 0 1-1 1-1 1-1 2-3 3v1c1 0 1 0 1-1 1 0 1 0 2-1l1-1h2v2l-2 2v1h-2 0c-1-1-2-1-3-1 0 1-1 1-1 1v3-1c1 0 1 0 2-1 1 1 1 2 1 3v1l-10-1 1-1c1-2 1-3 1-5v-2c0-2 1-3 2-4h1v-1h-2v-1-1-1-1z" class="R"></path><path d="M423 692v-2c0-2 1-3 2-4 1 1 1 2 1 3 1 1 1 1 1 2 1 1-1 4 1 6v-1c1 0 1 0 2-1 1 1 1 2 1 3v1l-10-1 1-1c1-2 1-3 1-5z" class="e"></path><path d="M457 670l-1 1c-2 3-5 5-6 9l1 1v2c-1 1-1 1-2 1 0 2 1 3 0 3-3 5-1 10-3 14h0-3c-1 0-2 1-3 2v2l-1 2v-3c-2 1 0 2-2 3v-3h-1l-1 1h-1v5 1c-2-2-1-4-1-6-2 2 1 4-2 6h-1v-8h-7v-1c-1 0-2 0-3 1l-1-1-1 1-1-1-1 1h-1c0-2 1-2 2-3 1 1 2 1 2 1 2 1 2 0 3 1 1-1 2-1 3-1h1 0l3 1 1-2h3v-1h-2v-1c0-1 0-2-1-3-1 1-1 1-2 1v1-3s1 0 1-1c1 0 2 0 3 1h0 2l1 1v-1c1 1 1 2 1 3s0 1 1 1h2v-7c0-2 0-5 1-6v-1c0-2 0-3 1-4 1 2 0 4 1 6v1c0-2 0-5 1-7h0v6h1v-6-1l2 2h0c0 1 0 4 1 5l2-8c1-4 4-6 8-8z" class="U"></path><path d="M428 697v-3s1 0 1-1c1 0 2 0 3 1h0 2l1 1v-1c1 1 1 2 1 3s0 1 1 1l1 1v1h-2l1 1-1 1h0v-2h-3 0v-1h-2v-1c0-1 0-2-1-3-1 1-1 1-2 1v1z" class="j"></path><path d="M444 679l2 2v1c-1 2 0 4-1 6-1 1-1 3-1 4v1c-1 2 0 4 0 6 0 0-1 1-2 1h0c-1-1-1-1 0-2v-3c-1-3-1-6 0-8 0-2 0-5 1-7h0v6h1v-6-1z" class="Y"></path><path d="M418 657h2l1 1c0 1 0 3-1 4l1 1c1 0 3-1 4 0h0v1h1c1 0 1 1 2 1h0c1 1 1 1 2 1 1 1 1 1 3 1 1 0 1 1 2 1 1 1 2 1 3 1v1h-4c-1 0-1 0-2 2h0c-3 0-4 1-7 3h-1-1c-1-1-1-2-3-2v3l-2 1c0 2 0 3 1 4h1v-2c2 0 2-1 3-2h1v2 2 1 1 1 1h2v1h-1c-1 1-2 2-2 4v2c0 2 0 3-1 5l-1 1c-1 1-2 1-4 1h-1-1v-2c-1-1-1-4-1-6v-15c1-1 1-2 1-3h0l1-3v-1s0-1-1-1v-1c1-2 2-2 3-3l-1-1c0-2 1-4 1-6z" class="g"></path><path d="M415 697l1 1h0v-14h1v12h1l1-1v1l1 1c0-2 0-3 1-5h0 2c0 2 0 3-1 5l-1 1c-1 1-2 1-4 1h-1-1v-2z" class="Z"></path><path d="M418 657h2l1 1c0 1 0 3-1 4l1 1v1 1l-3 3v1c-1 0-1 1-1 2l-2 3c-1 3 0 6 0 8v15c-1-1-1-4-1-6v-15c1-1 1-2 1-3h0l1-3v-1s0-1-1-1v-1c1-2 2-2 3-3l-1-1c0-2 1-4 1-6z" class="M"></path><path d="M419 681h1v-2c2 0 2-1 3-2h1v2 2 1 1 1 1h2v1h-1c-1 1-2 2-2 4v2h-2 0c-1 2-1 3-1 5l-1-1v-1-8-1-2-1c-1-1-1-1 0-2z" class="m"></path><path d="M421 692c1-2 1-4 0-6 0-1 0-1 1-1l-1-2 1-1h2v1 1 1h2v1h-1c-1 1-2 2-2 4v2h-2z" class="g"></path><path d="M421 663c1 0 3-1 4 0h0v1h1c1 0 1 1 2 1h0c1 1 1 1 2 1 1 1 1 1 3 1 1 0 1 1 2 1 1 1 2 1 3 1v1h-4c-1 0-1 0-2 2h0c-3 0-4 1-7 3h-1c0-2 1-3 2-4v-1c-1 0-1 0-3 1l-1-1 1-1-1-2h-1c-1 1-3 4-3 6v1h-1v-3c0-1 0-2 1-2v-1l3-3v-1-1z" class="o"></path><path d="M426 671c1 0 2-1 3-1h0 2l1-1c1 0 1 1 2 1-1 0-1 0-2 2h0c-3 0-4 1-7 3h-1c0-2 1-3 2-4z" class="R"></path><path d="M417 657h1c0 2-1 4-1 6l1 1c-1 1-2 1-3 3v1c1 0 1 1 1 1v1l-1 3h0c0 1 0 2-1 3v15c0 2 0 5 1 6v2h-8v-1-1c0-2 0-2-1-4h0l-1-1c-1 1-1 1-2 1-2 0-4-2-6-3v-1h-4c-1-1-1-2-2-3v-2l-1-1s0-1-1-2h0v-1c1 0 1-3 1-3h2l2-2h2v-6-4h-1v3c-1-1-1-2-1-3v-1l-1-1c1-2 1-3 1-4l1-1h2 1 15l4-1z" class="b"></path><path d="M417 657h1c0 2-1 4-1 6l1 1c-1 1-2 1-3 3v1c1 0 1 1 1 1v1l-1 3h0-3v-1h1s0-1 1-2h-2 1l-2-2v1h-1v-1l1-1v-1-1c0-1 1-2 1-2 1-2 1-2 1-4h-11-4c-1 0-1-1-1-1h1 15l4-1z" class="i"></path><path d="M413 659h2v1 2l1 1c-1 3-2 1-2 5 0 0 0 1-1 2l-2-2v1h-1v-1l1-1v-1-1c0-1 1-2 1-2 1-2 1-2 1-4z" class="e"></path><path d="M404 668v-2h4l2 2v1h1v-1l2 2h-1-1c0 2 0 2-2 3v1 1h-2-1l1 1h1c1 1 1 1 1 2s0 1 1 2h-1v1 1 1l-1-1c-1-2-2-2-2-4h-1-1l-2-1h0c-2-1-2-1-4-1-1 0-1 0-2-1v-6h1v3l1 1h1l-1-1 1-2 2 1h1 1c0-1 0-2 1-3z" class="Z"></path><path d="M404 668v-2h4l2 2v1l-3 3-1-1c-1 1-1 1-2 1v-4z" class="k"></path><path d="M404 678h1 1c0 2 1 2 2 4l1 1v-1-1-1h1c-1-1-1-1-1-2s0-1-1-2h-1l-1-1h1 2v-1-1c2-1 2-1 2-3h1 2c-1 1-1 2-1 2h-1v1h3 0 0c0 1 0 2-1 3v15c0 2 0 5 1 6v2h-8v-1-1c0-2 0-2-1-4h0c1-1 1-2 2-4v-4-1c-1-2-2-4-4-6z" class="e"></path><path d="M394 675h2c1 1 1 1 2 1 2 0 2 0 4 1h0l2 1c2 2 3 4 4 6v1 4c-1 2-1 3-2 4h0 0l-1-1c-1 1-1 1-2 1-2 0-4-2-6-3v-1h-4c-1-1-1-2-2-3v-2l-1-1s0-1-1-2h0v-1c1 0 1-3 1-3h2l2-2z" class="M"></path><path d="M399 682h1l1-1h1c1 0 1 1 2 2v1c1 1 1 3 2 4-1 1-2 1-3 1l-1 1h-3l-1-1c2-1 3-2 4-3h-1c-1-1-2-2-2-3v-1z" class="u"></path><path d="M394 675h2c1 1 1 1 2 1 2 0 2 0 4 1h0l2 1c2 2 3 4 4 6v1 4c-1 2-1 3-2 4h0 0l-1-1 1-1c1-2 1-3 1-6h0c-1-3-2-5-4-6-2-2-4-1-7-1-1 1-2 2-2 3h1l1-1 1 1-1 1c-2 0-2 0-4-1h-1l-2-1c1 0 1-3 1-3h2l2-2z" class="K"></path><path d="M389 680l2 1h1c2 1 2 1 4 1l1-1c1 0 1 1 2 1v1c0 1 1 2 2 3h1c-1 1-2 2-4 3h-1-4c-1-1-1-2-2-3v-2l-1-1s0-1-1-2h0v-1z" class="T"></path><path d="M621 588l1-2c2 1 2 3 3 5 0 3 0 5 1 9h0v1c1 1 2 3 3 5s1 4 2 6c1-1 1-2 1-4l6 20c1 4 1 9 2 13 0 2 0 4-1 6v4c1 1 1 4 0 5s-1 0-2 1l1 1c1-1 1-1 3-1v1 1l-1-1-1 1c2 1 1 13 1 17h-1v4c-1 0-1 0-1-1-1 1-1 1-1 2l2 2h-2-8-15l-3 1h-2v-3l1-1c-1-2-1-5-1-8h-1c-1 4-1 8-1 12l-1-1c0-2 0-5-1-8v1c-1-5-3-9-7-12h3c0-3-1-3 0-6h1v-3c0 1 1 2 1 3h1l1 1v-1-7-11-5h1l-1-1h-2v-3h2c2-2 3-8 4-11 0-5 3-13 6-18l5-12 1-2z" class="G"></path><path d="M621 588l2 30v19c-1 2-1 4-1 6h-1c0-6 1-12 1-18 0-2 1-5 0-7v-10c0-2-1-4-1-6v-6l-1-6 1-2z" class="f"></path><path d="M619 604c1 1 2 2 2 3 1 7-1 14-1 21 0 4 1 10 0 15l-1 1h-1l1-40z" class="V"></path><path d="M619 600v4l-1 40h-3c2-6 1-14 1-20 0-5 1-9 1-15 0-3 1-6 2-9z" class="h"></path><path d="M611 646c1-1 2-1 3-1h0l-1-1v-4c1-1 1-2 1-3 1-3 0-10 0-13 2-5 1-10 3-15 0 6-1 10-1 15 0 6 1 14-1 20h3 1 7l2 2c2-1 4-1 6 0l-1 1h-2-1 0c-1 0-1 1-2 1v1 5h0c-2-1-1-3-1-4v-1-2h0c-1 1 0 3-1 4-2-1-1-3-2-4l-1-1h-12z" class="X"></path><path d="M621 588l1-2c2 1 2 3 3 5 0 3 0 5 1 9h0v1c-1 2-1 5 0 8h0v15c0 1 0 3 1 4 0 2 0 3-1 4-1-1 0-3-1-4l-1 1v-3c0-3 0-5-1-8l-2-30z" class="q"></path><path d="M624 629v-2-1-6h0v-2c1-3 1-6 2-9v15c0 1 0 3 1 4 0 2 0 3-1 4-1-1 0-3-1-4l-1 1z" class="H"></path><path d="M615 602h1c0-2 1-3 2-4l1 1v1c-1 3-2 6-2 9-2 5-1 10-3 15 0 3 1 10 0 13 0 1 0 2-1 3v4l1 1h0c-1 0-2 0-3 1h-4v5c0 1-1 0-1 2 1 2 0 4-1 5v-7-11-5h1l-1-1h-2v-3h2c2-2 3-8 4-11 0-5 3-13 6-18z" class="k"></path><path d="M609 620c0 1 0 3-1 4-1 7-1 14-1 21v1 5c0 1-1 0-1 2 1 2 0 4-1 5v-7-11-5h1l-1-1h-2v-3h2c2-2 3-8 4-11z" class="T"></path><defs><linearGradient id="C" x1="637.356" y1="618.847" x2="622.54" y2="615.63" xlink:href="#B"><stop offset="0" stop-color="#c8c6b9"></stop><stop offset="1" stop-color="#ede6d4"></stop></linearGradient></defs><path fill="url(#C)" d="M626 601c1 1 2 3 3 5s1 4 2 6c1-1 1-2 1-4l6 20c1 4 1 9 2 13 0 2 0 4-1 6h0v-3-2c-1 1-1 2-2 3-1 2 1 10-1 11 1-3 1-9 0-12h0l-2 2c-2-1-4-1-6 0 1-2 1-3 1-5h0v-2l1-1v-3-6h-1v1c-2-1 0-4-2-5v3c-1-1-1-3-1-4v-15h0c-1-3-1-6 0-8z"></path><path d="M632 629h0c2 2 2 5 2 8v3h-1c-1 0-2 0-2-1l1-10z" class="f"></path><path d="M636 656c2-1 0-9 1-11 1-1 1-2 2-3v2 3h0v4c1 1 1 4 0 5s-1 0-2 1l1 1c1-1 1-1 3-1v1 1l-1-1-1 1c2 1 1 13 1 17h-1v4c-1 0-1 0-1-1-1 1-1 1-1 2l2 2h-2-8-15l-3 1h-2v-3l1-1c-1-2-1-5-1-8h-1c-1 4-1 8-1 12l-1-1c0-2 0-5-1-8v1c-1-5-3-9-7-12h3c0-3-1-3 0-6h1v-3c0 1 1 2 1 3h1l1 1v-1c1-1 2-3 1-5 0-2 1-1 1-2v-5h4 12l1 1c1 1 0 3 2 4 1-1 0-3 1-4h0v2 1c0 1-1 3 1 4h0v-5-1c1 0 1-1 2-1h0 1 2l1-1 2-2h0c1 3 1 9 0 12z" class="l"></path><path d="M602 655c0 1 1 2 1 3h1l1 1v11 5 1c-1-5-3-9-7-12h3c0-3-1-3 0-6h1v-3z" class="Y"></path><path d="M601 658l2 2v5 1c-1-1-2-1-2-2 0-3-1-3 0-6z" class="p"></path><path d="M609 672h0c1-4 1-8 1-11l1-1 1 16h0l1-1c1-2 0-5 0-7 1-2 1-5 1-6l-1-2 2-1h0-2l1-2h-1c0-1 0-1 1-1 1-1 0-2 1-3v2l1 1 1 1 1 1h-1l-1 1 1 1h0v1c0 1-1 1-1 2v4l1 4v3 6h-1l-1 1v1c-2 0-3 0-4 1h3l-3 1h-2v-3l1-1c-1-2-1-5-1-8z" class="b"></path><path d="M616 667l1 4v3 6h-1l-1 1c0-5 0-10 1-14z" class="d"></path><path d="M634 646l2-2h0c1 3 1 9 0 12-4 3-10 2-15 2-1 0-1-1-3 0v3c1 3 1 6 0 9-1 1-1 2-1 4v-3l-1-4v-4c0-1 1-1 1-2v-1h0l-1-1 1-1h1l-1-1-1-1c2 0 2 0 3-1v-7l1-1c0 2-1 7 0 9l1-1 1-1c1 0 0 1 1 2v-1c2-1 1-6 1-8 1 1 0 3 2 4 1-1 0-3 1-4h0v2 1c0 1-1 3 1 4h0v-5-1c1 0 1-1 2-1h0 1 2l1-1z" class="t"></path><path d="M636 656c2-1 0-9 1-11 1-1 1-2 2-3v2 3h0v4c1 1 1 4 0 5s-1 0-2 1l1 1c1-1 1-1 3-1v1 1l-1-1-1 1c2 1 1 13 1 17h-1v4c-1 0-1 0-1-1-1 1-1 1-1 2l2 2h-2-8-15-3c1-1 2-1 4-1v-1l1-1h1v-6c0-2 0-3 1-4 1-3 1-6 0-9v-3c2-1 2 0 3 0 5 0 11 1 15-2z" class="J"></path><path d="M626 679h1l1-3 2 2h1v3c-3 0-4-1-5-2z" class="V"></path><path d="M624 674h2c1 0 1-1 2-1h0v3l-1 3h-1-1c-1-2-1-3-1-5z" class="L"></path><path d="M619 672c1 1 1 2 3 2h0c0 2 0 4 1 6v1 1h-3v-5c-1-2-1-3-1-5z" class="I"></path><path d="M617 674c0-2 0-3 1-4h1v2c0 2 0 3 1 5v5h-5v-1l1-1h1v-6z" class="h"></path><path d="M635 661c1 2 1 4 2 6 0 3 0 8-1 11v3h0c-2-2 0-4 0-6v-6h-1v3h-1 0v-3l-1-4v-1-1h-9c3-2 8 1 11-2z" class="b"></path><path d="M619 670v-2l1 1v1h1c0-1 0-3 1-4 1 2-1 5 1 6 1 0 1-1 1-1l2-1c1 0 4 0 5 1v1h0l1 1-1 1h0l-1 1v-1c-1-1-1-1-2-1h0c-1 0-1 1-2 1h-2-2 0c-2 0-2-1-3-2v-2z" class="Q"></path><path d="M624 663h9v1 1 4c-1 1-4 1-5 1h-2l-2 1v-6-2z" class="C"></path><path d="M628 665h0l1-1c1 0 1 0 2 1v1 2h-1l-2-3z" class="s"></path><path d="M631 666c1 0 1-1 2-2v1 4c-1 1-4 1-5 1v-1c-1-1 0-2 0-4l2 3h1v-2z" class="B"></path><path d="M618 670c1-3 1-6 0-9v-3c2-1 2 0 3 0l-1 1c0 2 0 2 1 4 1-1 1-1 2-1l1-1h0c3 0 8-1 11 0-3 3-8 0-11 2v2 6s0 1-1 1c-2-1 0-4-1-6-1 1-1 3-1 4h-1v-1l-1-1v2h-1z" class="T"></path><path d="M623 662l1-1h0c3 0 8-1 11 0-3 3-8 0-11 2v2c-1 2-1 3-1 5v1-1c1-2 0-6 0-8h0z" class="Z"></path><path d="M636 656c2-1 0-9 1-11 1-1 1-2 2-3v2 3h0v4c1 1 1 4 0 5s-1 0-2 1l1 1c1-1 1-1 3-1v1 1l-1-1-1 1c2 1 1 13 1 17h-1v4c-1 0-1 0-1-1-1 1-1 1-1 2l-1 1v-1h0v-3c1-3 1-8 1-11-1-2-1-4-2-6-3-1-8 0-11 0h0l-1 1c-1 0-1 0-2 1-1-2-1-2-1-4l1-1c5 0 11 1 15-2zm-89 247h1v-3c2-1 6-2 9-2 1 0 3 1 5 1v3c2 1 3 1 6 1l1-1c1-1 1-1 2-1 8 3 15 8 23 11l1 1c0 1-1 2-1 3l1 1c3 1 6 2 8 4h3l-2 3c1 2 2 0 2 3h1c0-1 0-1-1-2l1-1c1 0 1 0 2 1 2 1 5 2 8 3 1 1 2 1 3 2v-2-1l3-2c1 0 1-1 3-1 1 0 2-1 3-1l1 1 2-1c0 1 1 2 2 2 1 1 0 1 1 0h1l1-1c1 0 1-1 3-1l-1 1h0c2 0 2 0 3-1-1-1 0-1 0-3l1-1 1 1h0c1 0 1 0 2 1 0 0-1 1-1 2s0 2 1 3c1 0 2 1 3 1l2-2v-2c-1-1-2-1-3-2l-1-1h0s1 0 1-1c1-1 2-2 2-3h0c0-1 1-2 2-3s2-2 4-2h0 2l1-1c4 1 7 2 11 4 2 0 3 1 5 2h0c2 1 4 3 6 4 3 3 5 6 7 10l1 1h3l1 1h-9l1 1h9v1c-4 2-20 0-26 0-6 1-13 2-19 1-7-1-12-2-19-1s-15 0-23 0c-3 0-6 2-9 2 1-1 1-1 3-1h-4v1c-6 1-12-1-18-1-5 0-10 1-15 0-4 0-8-2-13-2-10 0-20 3-31 2-2 0-3 0-4-1 0-3 1-2 2-4v-1c0-1 2-2 2-2v-1c1-2 3-2 5-3 1 1 3 1 4 3 0 1-1 1-1 2h1l1-1v-1l2-2h0 0c-1-1-1-2-2-3-1 1 0 1 0 2h-1l-3-3h0-1l-2-1-1-2c1-2 2-3 3-4l1-1 3-3h0l-2-3 4-1 1 1c2 0 4 0 5-1h-1 0c-1-1-2-2-2-3 1 0 1-1 2-2h1c1 0 3-1 5 0h0c2 0 3 1 5 3z" class="I"></path><path d="M520 927v-1c1-2 3-2 5-3 1 1 3 1 4 3h-1c-1 1-1 2-1 2-1 0-1 1-1 1l-1 1s-1 0 0 1h-2l-1-1c0-1-1-2-2-3z" class="G"></path><path d="M561 922h0 2v1l1-1c1 0 1 0 2 1h2l2 2-2 2h0 2l-1 1-3 3v-1h0v-1c-1 1-1 0-2 1h0-1v-1-1h-1v2c-1-1-1-1-2-1v-1l-1-1h-3v1h-1v-2h0v-2l6-2z" class="w"></path><path d="M655 925c1 0 1-1 2-1v1c1 0 2 0 3-1v1c2 0 4 1 6 2v1l1 1v1l-1 2h-3-2v1h-4l-1-2-1 1h0c-1-1-1 0-2-1h0l-1-1c1 0 2 0 2-1l-1-1h-1l3-3z" class="H"></path><path d="M673 922l1 1 1-1 1 1 2-1h1l1 1c0 1 0 1-1 3h1c2 1 4 2 6 4h2l1 1h3l1 1h-9-2-1c-1-1-1-1-2-1v-1c-1 1-1 1-2 1h-1v-1c-1 0-1 0-2 1v-1l1-1h0c-1-1-1 0-3 0v-1c-1 1-1 1-1 2-2 2-3 2-5 2l1-2v-1h3l1-1-1-1c0 1-1 1-1 1-1-1-1-1-2-1 1-2 1-2 2-3l1 1-2 2h1c1-1 3-3 4-5z" class="G"></path><path d="M571 916s1 0 1-1l2 1c2 1 2 1 4 1h0v1l-1 1c1 0 1 0 2 1h-2v1h1v3c-1 0-2 2-2 2-1 0-1-1-2 0v1c1 0 1 1 1 1l-1 1h0-1c-1 1-2 1-4 1v1h-3l3-3 1-1h-2 0l2-2-2-2h-2c-1-1-1-1-2-1l1-1c1 0 1-1 2-1v-1c1-2 2-2 4-3z" class="N"></path><path d="M571 916s1 0 1-1l2 1c-1 2-1 3-3 3h-1l-2 1h-1v-1c1-2 2-2 4-3z" class="q"></path><path d="M642 920l1-1 1 1h0c1 0 1 0 2 1 0 0-1 1-1 2s0 2 1 3c1 0 2 1 3 1l2-2c1 1 1 1 2 0h2l-3 3h1l1 1c0 1-1 1-2 1l1 1c-3 1-4-1-6-2 0-1-2-1-2-1l-2 2v1l-1-1v-1h0l-1 2h-1v-1l-1 1c-2 0-2 0-4-1 0 1 0 0-1 1 0-1-1-1-1-2h-1 0v2c-1-1-1-1-2-1v1h-2c-2 1-3 1-4 1l-1-1c-2 1-7 1-9 1 0-1 1-2 1-3 0 1 0 1 1 2h5 0l-1-1v-2-1l3-2c1 0 1-1 3-1 1 0 2-1 3-1l1 1 2-1c0 1 1 2 2 2 1 1 0 1 1 0h1l1-1c1 0 1-1 3-1l-1 1h0c2 0 2 0 3-1-1-1 0-1 0-3z" class="F"></path><path d="M525 913h0c1 1 2 2 3 2h1l2 1c2 1 3 2 5 2h2 1c2 1 2 1 3 1l3-2v1c0 1 0 2 1 3 1 0 1-1 2-1-1-1-1-1-2-1l1-1c1 1 2 1 2 2 1 1 1 2 1 3l1 1 1-1c0-1 0-1-1-2 1-1 1 0 3-1 0 0 1-1 1-2l2 2h1v1h-1c0 1 0 1-1 1l-2 2c-1 0-2 1-3 2v1 1h-2v2h1v1h-2v-1h-1l-1 1c-1 0-2 1-3 1-1-2-1-2-1-3l1-2h3c-1-1 0-1-1-1h-2l-2 3c-1 0-3-2-3-2l-3-3s0-1-1-1l-2-3c-1 0-1 0-2 1s0 1 0 2h-1l-3-3h0-1l-2-1-1-2c1-2 2-3 3-4z" class="D"></path><path d="M525 913h0l-1 2 1 1h1v2c-2 0-2 0-3 1l-1-2c1-2 2-3 3-4z" class="G"></path><path d="M537 905c1 1 2 1 3 2 1 0 1 0 2-1l1 1-1 2c1 1 1 1 2 1l1 1h1c0 1 1 1 2 1 1 1 2 1 3 2l-2 1v1h-2v1c2 0 3 0 5-2l3 3c0 1-1 2-1 2-2 1-2 0-3 1 1 1 1 1 1 2l-1 1-1-1c0-1 0-2-1-3 0-1-1-1-2-2l-1 1c1 0 1 0 2 1-1 0-1 1-2 1-1-1-1-2-1-3v-1l-3 2c-1 0-1 0-3-1h-1-2c-2 0-3-1-5-2l-2-1h-1c-1 0-2-1-3-2h0l1-1 3-3h0l-2-3 4-1 1 1c2 0 4 0 5-1z" class="W"></path><path d="M533 909c1 0 2 1 2 2 2 1 3 1 5 1s3 1 4 2h1 2c-3 2-6 1-9 1-2-1-4 0-6 0l-1 1-2-1h-1c-1 0-2-1-3-2h0l1-1 3-3 4 1v-1z" class="w"></path><path d="M529 909l4 1v2c-1 1-3 2-4 3h-1c-1 0-2-1-3-2h0l1-1 3-3z" class="q"></path><path d="M526 912h4c-1 1-1 2-2 3-1 0-2-1-3-2h0l1-1z" class="s"></path><path d="M537 905c1 1 2 1 3 2 1 0 1 0 2-1l1 1-1 2c1 1 1 1 2 1l1 1h1c0 1 1 1 2 1-2 1-3 1-4 2-1-1-2-2-4-2s-3 0-5-1c0-1-1-2-2-2v1l-4-1h0l-2-3 4-1 1 1c2 0 4 0 5-1z" class="H"></path><path d="M533 909l3-1h1c-1 1-1 2-2 3 0-1-1-2-2-2z" class="W"></path><path d="M659 910c4 1 7 2 11 4 2 0 3 1 5 2h0c2 1 4 3 6 4 3 3 5 6 7 10h-2c-2-2-4-3-6-4h-1c1-2 1-2 1-3l-1-1h-1l-2 1-1-1-1 1-1-1c-1 2-3 4-4 5h-1l2-2-1-1c-1 1-1 1-2 3 1 0 1 0 2 1 0 0 1 0 1-1l1 1-1 1h-3l-1-1v-1c-2-1-4-2-6-2v-1c-1 1-2 1-3 1v-1c-1 0-1 1-2 1h-2c-1 1-1 1-2 0v-2c-1-1-2-1-3-2l-1-1h0s1 0 1-1c1-1 2-2 2-3h0c0-1 1-2 2-3s2-2 4-2h0 2l1-1z" class="q"></path><path d="M650 916h0c0-1 1-2 2-3s2-2 4-2c-1 1-2 2-2 3 1 0 2 0 3 1v1c-1 0-1 1-1 2 1 0 2 0 3 1h0l1 1 2-1 1-1v1h2v1c-1 0-1 0-2 1v1l2-1c1 0 2 0 3 1l-1 1 1 1c1-1 1-1 2-3l2 2c0-1 0-1 1-1h0c-1 2-3 4-4 5h-1l2-2-1-1c-1 1-1 1-2 3 1 0 1 0 2 1 0 0 1 0 1-1l1 1-1 1h-3l-1-1v-1c-2-1-4-2-6-2v-1c-1 1-2 1-3 1v-1c-1 0-1 1-2 1h-2c-1 1-1 1-2 0v-2c-1-1-2-1-3-2l-1-1h0s1 0 1-1c1-1 2-2 2-3z" class="J"></path><path d="M650 916h0c0-1 1-2 2-3s2-2 4-2c-1 1-2 2-2 3 1 0 2 0 3 1v1c-1 0-1 1-1 2 1 0 2 0 3 1h0l1 1 2-1v2h-1l-1 1-2-2c-1 1-1 1-1 2l-1-1-1-1v2h-2l1-1v-1h0c-2 1-2 1-3 3-1-1-2-1-3-2l-1-1h0s1 0 1-1c1-1 2-2 2-3z" class="H"></path><path d="M650 916h0c0-1 1-2 2-3s2-2 4-2c-1 1-2 2-2 3v1 1l-2 2c-1-1-1 0 0-1l-2-1z" class="W"></path><path d="M547 903h1v-3c2-1 6-2 9-2 1 0 3 1 5 1v3c2 1 3 1 6 1l1-1c1-1 1-1 2-1 8 3 15 8 23 11l1 1c0 1-1 2-1 3l1 1c3 1 6 2 8 4l-3 1 1 2-2 1-1 3h-1c0-1 1-1 1-2v-1l-2 1-2-2v4c-1-1-2-4-2-4l-2 1c1 1 1 1 1 2l1 1h-1l-1-2-2 1c-1-1-4-1-6-1h-1v1l-1 1h0c-1 0-2 0-2 1v1h1 0 1l-1 1v1h-2l-1-2 1-2h0-2s0-1-1-1v-1c1-1 1 0 2 0 0 0 1-2 2-2v-3h-1v-1h2c-1-1-1-1-2-1l1-1v-1h0c-2 0-2 0-4-1l-2-1c0 1-1 1-1 1-2 1-3 1-4 3v1c-1 0-1 1-2 1l-1 1-1 1v-1h-2 0c-1-1-2-2-3-2h-1l-2-2-3-3c-2 2-3 2-5 2v-1h2v-1l2-1c-1-1-2-1-3-2-1 0-2 0-2-1h-1l-1-1c-1 0-1 0-2-1l1-2-1-1c-1 1-1 1-2 1-1-1-2-1-3-2h-1 0c-1-1-2-2-2-3 1 0 1-1 2-2h1c1 0 3-1 5 0h0c2 0 3 1 5 3z" class="L"></path><path d="M593 919l-1 2 2 2c1 0 3 0 4-1v1l1 2-1 3h-1c0-1 1-1 1-2v-1l-2 1-2-2h0-1c0-1 0-1-1-2h-4l1-2h0c1-1 3-1 4-1z" class="P"></path><path d="M572 915l-2-1c1-1 2-1 2-2 1 0 1 0 2 1l2-2c2 1 3 1 4 3-1 0-3 0-4 1v1c2 0 5 0 6-2 1 0 1 1 2 2-1 0-1-1-2-1l-1 1c-1 0-2 1-3 1h0c-2 0-2 0-4-1l-2-1z" class="h"></path><path d="M595 917c3 1 6 2 8 4l-3 1 1 2-2 1-1-2v-1c-1 1-3 1-4 1l-2-2 1-2 2-2zm-49-12c2-1 3-1 5-1 1 1 3 2 3 3l1 1 1-1c2 1 4 3 6 3v2h-2s-1 1-2 1h-1-1-1-1c-1-1-2-2-4-3-1 0 0 0-2-1h-2c0-2 1-1 2-3l-2-1z" class="s"></path><path d="M571 901c8 3 15 8 23 11l1 1c0 1-1 2-1 3l-4-2-2-1c-1 0-2-1-3-2-2 0-2 1-3 1l-1-2c0-1-1-1-2-1v1h0-2c1-1 1-1 1-3-1-1-2-2-3-2l-6-3c1-1 1-1 2-1z" class="O"></path><path d="M590 914v-1h2c1-1 0-1 2-1l1 1c0 1-1 2-1 3l-4-2z" class="M"></path><path d="M537 900c1 0 3-1 5 0h0c2 0 3 1 5 3v1c-2 0-2 0-3 1h2l2 1c-1 2-2 1-2 3h2c2 1 1 1 2 1 2 1 3 2 4 3l-1 1h-1c-2-2-6-5-10-5l1-2-1-1c-1 1-1 1-2 1-1-1-2-1-3-2h-1 0c-1-1-2-2-2-3 1 0 1-1 2-2h1z" class="P"></path><path d="M542 909c4 0 8 3 10 5h1l1-1h1 1 1 1c2 1 3 1 5 1v1c2-1 3-1 5-1l3 2c-2 1-3 1-4 3v1c-1 0-1 1-2 1l-1 1-1 1v-1h-2 0c-1-1-2-2-3-2h-1l-2-2-3-3c-2 2-3 2-5 2v-1h2v-1l2-1c-1-1-2-1-3-2-1 0-2 0-2-1h-1l-1-1c-1 0-1 0-2-1z" class="E"></path><path d="M563 915c2-1 3-1 5-1l3 2c-2 1-3 1-4 3v1c-1 0-1 1-2 1s-1-1-2-1l1-1v1h1 1l-1-1h-1c1-1 1-1 2-1v-1h-3v-2z" class="C"></path><path d="M355 874c2 0 5 0 7 1l5 5c2 1 3 2 6 3h1 2c-1 1 0 1-1 2h0l-2 1 2 2h5 3c3 1 5 3 8 4h3c1 0 4 1 5 1 2 0 4 1 6 2 1 1 2 1 3 1l-1 2 1 1h-1l1 1h1c0 1 0 1-1 2 2 0 2-1 3-1v1h-1l-3 3h7v1c-1 1-2 1-3 1 0 1 1 1 2 2l2-1 1 1v2h1v1c1 0 2 1 3 2l1-1 1 1v-1h2c1 1 1 3 2 2 2-1 2-1 3 0h-1c0 3 0 4 1 6h1l1-1c2 0 2 1 3 1 1 1 1 2 1 3l1 2h0c0 2 0 2 1 4h0c1 1 1 0 2 1l1-1v1l-1 1c-1 0-2 0-3-1h0c0-1-1-1-1-1v2h-2v-1l-1-1v-2h0l-1 1c1 1 1 2 2 3l-1 1-1-2-1 1 1 1h2 2 1 0c5 2 10 1 15 1h2c3-1 8 0 12 1 3 1 7 1 11 1l-13-2v-1l15 1c1 0 3 0 4-1h9 0 7c3 0 9 1 11 0 2 0 3-1 5 1-2 0-3 1-5 1h-1c-1 1-2 1-3 1-5 0-12-1-17 1l1 1h-7c-6 0-11-1-17-2-2 0-4-1-7-1-5 0-10 1-15 1-4 0-7-2-10-2-4 1-7 1-11 1h-10-11c-4 0-7 1-10 1-5 0-9 0-13-1h-1 0-4-16c-3 0-7 1-11 1-3 0-6-1-9-1-6-1-11 0-17-1-2 0-4 0-5-1v-1l-6-1-29 1c-1-1-2-3-2-4-1 0-1 0-2 1 0 0-1 1-1 2h-3l-7 1 1-1 1-2c1 0 2-1 3-2l2-2h-1c0-1-1-1-2-2l-1-1 1-1 1 1h1 0 1l1 1v-1-1l3-3h1v-2c1-2 2-3 4-4h0v-1s-1-1-2-1h0v-1l2-2h2v-1c1 1 2 2 2 4 3 1 4 1 7 1h0l-1-1 1-1-2-2c1-1 1-1 2-1h1l-1-1c0-1 0-2-1-3h-3c4-2 8 2 12 1 2-1 3-2 6-1 3 0 6 1 9 2 1-1 1-1 2-1 2-1 3-2 4-4l5-6 1-2c2-2 4-3 7-4 1-2 1-3 2-5h0v-2c1-1 2-2 4-3 2 0 4 1 6 2l2 1v-3c2-2 5-3 7-3z" class="b"></path><path d="M347 933h2v1h-1c-4 0-8-1-12-2-1 0 0 1-1 0v-3c2 0 4-1 6-1-1 1-1 2-2 3h3c1 1 1 1 2 1l3 1z" class="L"></path><path d="M337 914l-2-2-1 1h0l-1-1 2-2v-1c1 1 1 1 2 1 0-2 0-2-1-4h-1l1-1 2 1h1 2v1c-1 1-1 3-2 5l1 1c-1 0-2 1-3 1h0z" class="j"></path><path d="M342 927l1 1c2 1 2 1 4 1h1 2v-1h1v1s1 1 2 1c-2 2-4 2-6 3l-3-1c-1 0-1 0-2-1h-3c1-1 1-2 2-3h0l1-1z" class="G"></path><path d="M383 922v2c1 0 1 0 1 1l2 2c-1 0-3 1-3 2l-1-1-1 1 1 1h-1l-1 1c-1 0-2 0-3-1l-1 1-1-2-1 1-1-1h0l-2 2h-7c-1-1-1 0-2 0h-1c-1-1-1-2-3-2v-1c2 0 2-1 3-1l1-1h3l3-1h1c2-1 2-1 4-1h1c1 1 2 1 3 2h1v-1l1-1h1c1 1 1 1 2 0v-1l1-1z" class="P"></path><path d="M373 929h-1c-1-1-3-1-4-1 1-1 0-1 1-1s2-1 3-1 2 1 3 2h-1l-1 1h0z" class="q"></path><path d="M340 913h1 0c-1 1-1 2-2 3v1h-1v-1l-2 1 2 1c-1 1-4 2-5 3l1 2-1 1 1 2h0l-2 2c-1 2-1 3-2 4-1 0-1 0-2-1l-1 1c-3 0-9 1-12-1h0 2c0-1 0-1 1-2l1 1h2c0-2 1-3 2-5 2-5 6-7 11-10l3-1h0c1 0 2-1 3-1z" class="G"></path><path d="M340 913h1 0c-1 1-1 2-2 3v1h-1v-1l-2 1c-1 1-2 2-4 3h0c1-2 1-3 2-5l3-1h0c1 0 2-1 3-1z" class="C"></path><path d="M323 925c2-5 6-7 11-10-1 2-1 3-2 5h0l-1 1h-1l-4 4h0v1l-3-1z" class="P"></path><path d="M334 887c1 1 3 3 5 4 1 1 2 2 4 3h1c1 0 3 0 4-1v-1l1 2-1 1c-1 0-2 1-3 2-2 1-2 2-3 4 1 0 0 0 1 1-1 0-1 0-2 1v-3c-1 2-2 3-4 4 0-3 2-3 2-6h1c1-1 2-2 2-3 0 1-1 1-2 2h-1l-2 2h-2c-1 1-1 2-2 3h1v2l-1 1c-1 1-2 3-3 3s-1 0-2 1l-2-2v-1l-1 1c-1-1-1-2-2-3h1v1h1v-1c0-1 1-1 1-2l-1-1c0-2 1-6 1-8l1-2c2-2 4-3 7-4z" class="T"></path><path d="M334 887c1 1 3 3 5 4l-1 1h-2l1 2h-1c-1 0-2 0-3 1v1h-1c0-2 1-3 2-4h0c-2 2-3 3-3 5h-1v-1c-2 1-2 2-2 3-1 1-2 1-3 2 0-2 1-6 1-8l1-2c2-2 4-3 7-4z" class="B"></path><path d="M327 891l2 4h-1l-1 4h1c-1 1-2 1-3 2 0-2 1-6 1-8l1-2z" class="J"></path><path d="M288 902c4-2 8 2 12 1 2-1 3-2 6-1 3 0 6 1 9 2 2 3 4 4 8 5v1c-1 0-2 1-3 0-1 0-1-1-2-1h-1l1 1h-1v-1l-1 1c1 1 2 1 2 2h1l-1 1h0c-1-1-1-1-2-1v1c-1 0-1 1-2 1-1-1-3-2-4-1 1 1 3 1 2 2h-3-1c1-2 1-2 1-3h-3c-1-1-2-2-3-2l-1 1c-1 1-2 1-3 2h-2l-2 1c0-1-1-2-1-3v-1c0-1-2-3-2-4h1l-1-1c0-1 0-2-1-3h-3z" class="N"></path><path d="M293 906c1 0 2 0 2 1v1l1 1v-1l2-2c1 0 1 0 2 1s2 0 3 0v-1h1 1l3 3c0 1 1 1 1 2v1h-3c-1-1-2-2-3-2l-1 1c-1 1-2 1-3 2h-2l-2 1c0-1-1-2-1-3v-1c0-1-2-3-2-4h1z" class="P"></path><path d="M363 912c2-2 3-3 5-3h4 0c2 0 4 1 5 2h1c1 1 1 1 2 1h0c2 2 4 4 5 6v1l1 1v2h-1-2l-1 1v1c-1 1-1 1-2 0h-1l-1 1v1h-1c-1-1-2-1-3-2h-1c-2 0-2 0-4 1h-1l-3 1h-3 0c0-1 2-3 2-4h-2l1-1-1-1 1-1v-2h0l-1-2c0-1 1-2 1-2v-1z" class="D"></path><path d="M363 913c0 2 2 2 2 3l1 1c0 1 1 1 1 3h-2l-2-1v-2h0l-1-2c0-1 1-2 1-2z" class="H"></path><path d="M363 912c2-2 3-3 5-3h4l-1 1c0 1-1 2-1 2-1 2-3 3-5 4 0-1-2-1-2-3v-1z" class="s"></path><path d="M372 909c2 0 4 1 5 2h1c1 1 1 1 2 1h0c2 2 4 4 5 6v1l-1 2-2-2h-2c-1-1-1-1-1-2v-1c-1 0-2 0-3-1h-1c0 1 0 1 1 2l-1 1c-2-1-1-1-2-3h-1v1l-1 1c0-2 0-2-1-3v-1l2-1v-3z" class="P"></path><path d="M352 906l1-1h1c1-1 2-2 4-2l1 1h1c1 0 1 1 2 2s1 1 3 2h0c-1 1-1 2-2 3v1 1s-1 1-1 2l1 2h0v2l-1 1 1 1-1 1h2c0 1-2 3-2 4h0l-1 1c-1 0-1 1-3 1v1c2 0 2 1 3 2h-1c-3 0-3 0-5-2h1v-1h-1c-2 1-2 1-2 2-1 0-2-1-2-1v-1h-1v1h-2-1c-2 0-2 0-4-1l-1-1h0v-2c-2 1-4 2-5 3h-2l-1 1-2-1 2-2h0l-1-2 1-1-1-2c1-1 4-2 5-3l-2-1 2-1v1h1v-1c1-1 1-2 2-3h0l6-4 2-1c1-1 2-2 3-2z" class="L"></path><path d="M350 912h0l2 2h2v2h0l1 1v-1h1l1 1c-1 0-1 1-2 1-1 1-3 0-4-1h-1c-2 1-4 1-6 1 0-2 1-2 1-3h4c1-1 1-2 1-3z" class="B"></path><path d="M362 926c-1-1-2-1-2-3h0c-2-1-3 0-5 0-2 1-3 1-5 1l1-1 3-3h4v-1h-1v-2h1 1c1 1 1 2 2 2l1 1 1 1-1 1h2c0 1-2 3-2 4z" class="P"></path><path d="M352 906l2 1c-1 1-1 2-2 2-1 1-2 2-2 3s0 2-1 3h-4s-1 0-1 1l-3 3-1-2h-1v-1c1-1 1-2 2-3h0l6-4 2-1c1-1 2-2 3-2z" class="q"></path><path d="M345 915c0 1-1 1-1 3 2 0 4 0 6-1h1l-2 1v1s-1 1-1 2v3l1 2h0l-1 1h-1c-2 0-2 0-4 1l-1-1h0v-2c-2 1-4 2-5 3h-2l-1 1-2-1 2-2h0l-1-2 1-1-1-2c1-1 4-2 5-3l-2-1 2-1v1h1 1l1 2 3-3c0-1 1-1 1-1z" class="B"></path><path d="M345 915c0 1-1 1-1 3 2 0 4 0 6-1h1l-2 1v1s-1 1-1 2v3l-2-1c0-1 0-1-1-2-1 0 0 0-1-1h-1-2-1l-2 2v-1-1l-1 1v2h-3l-1-2c1-1 4-2 5-3l-2-1 2-1v1h1 1l1 2 3-3c0-1 1-1 1-1z" class="H"></path><path d="M290 907c1-1 1-1 2-1 0 1 2 3 2 4v1c0 1 1 2 1 3-1 1-1 1-2 3 2-1 3-3 5-3-1 1-2 2-2 3l1 1-1 2h-1v-1-1c-1 0-2 1-3 2v1c1 1 1 1 3 1v-1c1 0 2-1 3-1 1-1 1-1 2-1 2-2 4-1 6-3 2 1 1 0 3 0 1 1 4 3 5 5 1 1 2 1 2 3h-1l-1-2h0-1c0 1 0 2-1 3l-1-1-2 1v-1l-1 1-1-1-1 1c-1 0-1 0-2-1l-1-1h-1l2 3h0-1l-1-1h0l2 2-1 1h-1l-2 2h3 0c1 1 3 0 4 0l2 1-29 1c-1-1-2-3-2-4-1 0-1 0-2 1 0 0-1 1-1 2h-3l-7 1 1-1 1-2c1 0 2-1 3-2l2-2h-1c0-1-1-1-2-2l-1-1 1-1 1 1h1 0 1l1 1v-1-1l3-3h1v-2c1-2 2-3 4-4h0v-1s-1-1-2-1h0v-1l2-2h2v-1c1 1 2 2 2 4 3 1 4 1 7 1h0l-1-1 1-1-2-2z" class="E"></path><path d="M272 925c0 1 1 1 2 2l1 1v-1h1c1 0 1 0 2-1h-1 0-2v-1l2-2 1 1v1l1 1c-1 1-1 1-1 2-1 0-1 0-2 1 0 0-1 1-1 2h-3l-7 1 1-1 1-2c1 0 2-1 3-2l2-2z" class="H"></path><path d="M278 925l1-1c0-1-1-1 0-2l1-1c1-1 1-2 1-3l2-2 1 1v2l1 1 2-1c-1-1-1-1-2-1l1-1h2l1 1c0 1 0 1-1 2v1h2c1 2 1 2 3 2v2h-1l-2-1-2 1h-1l-1 1h-1l-2-2v1h-2 0v2h-1c-1-1 0-1-1-1l-1-1z" class="P"></path><path d="M355 874c2 0 5 0 7 1l5 5c2 1 3 2 6 3h1 2c-1 1 0 1-1 2h0l-2 1 2 2h5 3c3 1 5 3 8 4h3l2 1-1 2 1 4-2-1-1 1h-2-1l1-2c-1 0-1 0-2 1-3 0-4 0-6 2h1c-2 2-2 4-3 6v3l1 1s0 1-1 1h-1v1c-1 0-1 0-2-1h-1c-1-1-3-2-5-2h0-4c-2 0-3 1-5 3v-1c1-1 1-2 2-3h0c-2-1-2-1-3-2s-1-2-2-2h-1l-1-1c-2 0-3 1-4 2h-1l-1 1c-1 0-2 1-3 2l-2 1-6 4h-1l-1-1c1-2 1-4 2-5v-1-1l-1-1 1-1c1-1 1-1 2-1-1-1 0-1-1-1 1-2 1-3 3-4 1-1 2-2 3-2l1-1-1-2v1c-1 1-3 1-4 1h-1c-2-1-3-2-4-3-2-1-4-3-5-4 1-2 1-3 2-5h0v-2c1-1 2-2 4-3 2 0 4 1 6 2l2 1v-3c2-2 5-3 7-3z" class="d"></path><path d="M355 897c1-1 1-1 2-1v1l1 2v1h2c-1 1-5 3-7 4s-3 1-4 3l-1-1v-5c2-2 5-3 7-4z" class="G"></path><path d="M355 890l1-1c0 1 0 1 1 2h-1v1l1 1 1-1 1 1c1 1 1 2 1 3-1 1-2 1-3 1v-1c-1 0-1 0-2 1-2 1-5 2-7 4v5l-1 2v1l-6 4h-1l-1-1c1-2 1-4 2-5v-1-1l-1-1 1-1c1-1 1-1 2-1-1-1 0-1-1-1 1-2 1-3 3-4 1-1 2-2 3-2l1-1-1-2h2v-1h2 1 1l1-1z" class="Z"></path><path d="M341 903c1-1 1-1 2-1-1-1 0-1-1-1 1-2 1-3 3-4l-1 2 1 1c0 1-1 3 0 3h1v1l-1 1-1-1c-2 1-1 0-1 1s-1 2-2 3v1c1 1 1 1 2 1v-1h1c1-1 1-1 2-1h1v1l-6 4h-1l-1-1c1-2 1-4 2-5v-1-1l-1-1 1-1z" class="l"></path><path d="M355 890l1-1c0 1 0 1 1 2h-1v1l1 1 1-1 1 1c1 1 1 2 1 3-1 1-2 1-3 1v-1c-1 0-1 0-2 1v-1h-1c-3 2-6 1-9 4l-1-1 1-2c1-1 2-2 3-2l1-1-1-2h2v-1h2 1 1l1-1z" class="D"></path><path d="M375 888h5 3c3 1 5 3 8 4h3l2 1-1 2 1 4-2-1-1 1h-2-1l1-2c-1 0-1 0-2 1-3 0-4 0-6 2l-2 1v-1h-1c-1 1-1 2-1 3-1 0-2-1-3 0-2 1-3 2-5 2v-1c-1 0-1 0-1 1h-1c0-1 0-1-1-1l-1 1h-3c-1-2-2-2-2-5l-1-1c-1-2 0-4 1-6 0-1 1-1 1-2h2c1-1 2-1 3-1l1 1c1 0 2 1 3 1 1-2 1-3 3-4z" class="B"></path><path d="M375 888h5c-2 1-4 2-5 3s0 2-2 2l-1-1c1-2 1-3 3-4zm8 0c3 1 5 3 8 4l-2 1-1-1c-2 0-3 0-4 1h0c-1 0-1 0-2-1 1-1 1-2 1-3v-1z" class="C"></path><path d="M362 893c0-1 1-1 1-2h2c1-1 2-1 3-1l-1 1v4h-1l-2 2h0c-1 1-2 1-2 3l-1-1c-1-2 0-4 1-6z" class="P"></path><path d="M366 900h1v1h2v-2h3 1 1c0-2-3-2-4-3 0 0 0-1-1-1l1-1h1c3 0 1 0 3 1 1 1 1 2 3 2 0-1 0-1 1-1s2-1 3-1v-1l1 1h5 7 1 0l1 4-2-1-1 1h-2-1l1-2c-1 0-1 0-2 1-3 0-4 0-6 2l-2 1v-1h-1c-1 1-1 2-1 3-1 0-2-1-3 0-2 1-3 2-5 2v-1c-1 0-1 0-1 1h-1c0-1 0-1-1-1l-1 1h-3c-1-2-2-2-2-5 0-2 1-2 2-3 1 1 1 2 2 3z" class="C"></path><path d="M364 897c1 1 1 2 2 3l-1 1 1 1h-1s0-1-1-1v-1h-2c0-2 1-2 2-3z" class="s"></path><path d="M355 874c2 0 5 0 7 1l5 5c2 1 3 2 6 3h1 2c-1 1 0 1-1 2h0l-2 1 2 2c-2 1-2 2-3 4-1 0-2-1-3-1l-1-1c-1 0-2 0-3 1h-2c0 1-1 1-1 2-1-1-1-1-2-1l-1 1-1-1-1 1-1-1v-1h1c-1-1-1-1-1-2l-1 1-1 1h-1-1-2v1h-2v1c-1 1-3 1-4 1h-1c-2-1-3-2-4-3-2-1-4-3-5-4 1-2 1-3 2-5h0v-2c1-1 2-2 4-3 2 0 4 1 6 2l2 1v-3c2-2 5-3 7-3z" class="B"></path><path d="M355 880h1l1-1 1 3h1l1 2-2 1v2h-1v-4h-2l-1-1-2 1c-2 0-3 0-5-1l4-1v-1h4z" class="W"></path><path d="M336 882v1c1 3 2 4 5 6 1 1 4 1 6 1 0 0 0-1 1-1s1 0 2 1v2h-2v1c-1 1-3 1-4 1h-1c-2-1-3-2-4-3-2-1-4-3-5-4 1-2 1-3 2-5z" class="e"></path><path d="M336 883h3l1 1-1 1h1 0l1-1v1l1 1v1l1-1c0-1 0-2-1-3l1-1c2-1 3 0 4 0 2 1 3 1 5 1v1h1c0 1-1 1-2 1-3 1-5 4-8 3l-2 1c-3-2-4-3-5-6z" class="H"></path><path d="M355 874c2 0 5 0 7 1-2 0-4 0-5 1l-2 4h-4v1l-4 1c-1 0-2-1-4 0l-1 1c1 1 1 2 1 3l-1 1v-1l-1-1v-1l-1 1h0-1l1-1-1-1h-3v-1h0v-2c1-1 2-2 4-3 2 0 4 1 6 2l2 1v-3c2-2 5-3 7-3z" class="C"></path><path d="M362 875l5 5c2 1 3 2 6 3h1 2c-1 1 0 1-1 2h0l-2 1 2 2c-2 1-2 2-3 4-1 0-2-1-3-1l-1-1c-1 0-2 0-3 1h-2c0 1-1 1-1 2-1-1-1-1-2-1l-1 1-1-1-1 1-1-1v-1h1c-1-1-1-1-1-2l-1 1c-1-1-1-1-1-2s0-1 1-2l2 1h1v-2l2-1-1-2h-1l-1-3-1 1h-1l2-4c1-1 3-1 5-1z" class="E"></path><path d="M359 882h6l1 3h-2l-1-1h-3l-1-2z" class="N"></path><path d="M367 880c2 1 3 2 6 3h1 2c-1 1 0 1-1 2h0l-2 1 2 2c-2 1-2 2-3 4-1 0-2-1-3-1 2-2 1-4 2-6-1-1-1 0-2 0l-1-1c-1 1-1 0-2 1h0l-1-3c1 0 1-1 2-2z" class="f"></path><path d="M362 875l5 5c-1 1-1 2-2 2h-6-1l-1-3-1 1h-1l2-4c1-1 3-1 5-1z" class="q"></path><path d="M394 892c1 0 4 1 5 1 2 0 4 1 6 2 1 1 2 1 3 1l-1 2 1 1h-1l1 1h1c0 1 0 1-1 2 2 0 2-1 3-1v1h-1l-3 3h7v1c-1 1-2 1-3 1 0 1 1 1 2 2l2-1 1 1v2h1v1c1 0 2 1 3 2l1-1 1 1v-1h2c1 1 1 3 2 2 2-1 2-1 3 0h-1c0 3 0 4 1 6h1l1-1c2 0 2 1 3 1 1 1 1 2 1 3l1 2h0c0 2 0 2 1 4h0c1 1 1 0 2 1l1-1v1l-1 1c-1 0-2 0-3-1h0c0-1-1-1-1-1v2h-2v-1l-1-1v-2h0l-1 1c1 1 1 2 2 3l-1 1-1-2-1 1 1 1h2 2 1 0c5 2 10 1 15 1h2c3-1 8 0 12 1 3 1 7 1 11 1l-13-2v-1l15 1c1 0 3 0 4-1h9 0 7c3 0 9 1 11 0 2 0 3-1 5 1-2 0-3 1-5 1h-1c-1 1-2 1-3 1-5 0-12-1-17 1l1 1h-7c-6 0-11-1-17-2-2 0-4-1-7-1-5 0-10 1-15 1-4 0-7-2-10-2-4 1-7 1-11 1h-10-11c-4 0-7 1-10 1-5 0-9 0-13-1v-1c2 0 3 1 5 1 3 0 7 0 10-1 5-1 9 0 15 0l13-1c3 0 7 1 9 0l-2-2c-1 1-1 1-2 1h-13c-1 0-4 0-5-1-1 0-1 0-1-1 1 0 1 0 2 1l1 1 1-1-1-1h0c-1-1-3-1-4-2h0l-2 2 2 1-1 1c-1-1-1-2-2-2v1 1h-2v-1c-1 1-4 1-5 1-1 1-2 0-3-1-2 1-4 1-6 1l-1-1h2l1-1v-4c-2 0-2 0-3 1l-2-2c0-1 0-1-1-1v-2h2 1v-2l-1-1v-1c-1-2-3-4-5-6h0v-1h1c1 0 1-1 1-1l-1-1v-3c1-2 1-4 3-6h-1c2-2 3-2 6-2 1-1 1-1 2-1l-1 2h1 2l1-1 2 1-1-4 1-2-2-1z" class="T"></path><path d="M395 918l1-2c1 1 1 1 2 1 0 0 1-1 2-1 1-1 2-1 3-1 1-1 1 0 2-1h2c1 1 2 1 3 3h0c1 0 1 0 2-1l2 2h0v1l2 1v-1l1-1c1 1 1 1 2 1v-1 1c-1 1-1 1-1 3l1 1v1h-1v-1h-1l-1 1-1-1-1 1c1 1 2 1 3 2h0c-1 0-2 0-2 1l-1-1c-3-2 1 1-2 0v-1l-2 1-1-1h-4l-1 1c2 1 3 0 4 1h1v1c-1 0-2-1-2-1h-1c0 1 0 0-1 1l-2-2v-1l-1 1h0v2c-1 0-1 0-2 1h1v1c1 0 1 1 2 1v1h-2v-1c-1 1-4 1-5 1-1 1-2 0-3-1-2 1-4 1-6 1l-1-1h2l1-1v-4c-2 0-2 0-3 1l-2-2c0-1 0-1-1-1l6-3 1-2h5v-1z" class="N"></path><path d="M410 917h0c1 0 1 0 2-1l2 2h0v1l2 1v-1l1-1c1 1 1 1 2 1v-1 1c-1 1-1 1-1 3l1 1v1h-1v-1h-1l-1 1-1-1-1 1c1 1 2 1 3 2h0c-1 0-2 0-2 1l-1-1c-3-2 1 1-2 0v-1l-2 1-1-1h-4l-1 1c2 1 3 0 4 1h1v1c-1 0-2-1-2-1h-1c0 1 0 0-1 1l-2-2v-1l-1 1h0v2c-1 0-1 0-2 1h1v1c1 0 1 1 2 1v1h-2v-1c-1 1-4 1-5 1-1 1-2 0-3-1-2 1-4 1-6 1l-1-1h2l1-1h1v1h3v-1l-1-1c1 0 2-1 3-1l1 2c0-1 1-1 1-2h-2c1-2 3-1 5-2-2-1-3-2-5-3v-1h0c2 0 4 2 6 2 0-1 1-1 1-2 1 0 1 0 2-1 1 1 2 1 3 1h2c0-1-1-2-1-3l1-1c0 1 0 1 1 1l-1-1 1-1z" class="E"></path><path d="M394 892c1 0 4 1 5 1 2 0 4 1 6 2 1 1 2 1 3 1l-1 2 1 1h-1l1 1h1c0 1 0 1-1 2 2 0 2-1 3-1v1h-1l-3 3h7v1c-1 1-2 1-3 1 0 1 1 1 2 2l2-1 1 1v2h1v1c1 0 2 1 3 2l1-1 1 1v-1h2c1 1 1 3 2 2 2-1 2-1 3 0h-1c0 3 0 4 1 6h-1l-1 2-3-2-1-1c-1 1-1 2-3 2v-1h-1v2l-1-1c0-2 0-2 1-3v-1 1c-1 0-1 0-2-1l-1 1v1l-2-1v-1h0l-2-2c-1 1-1 1-2 1h0c-1-2-2-2-3-3h-2c-1 1-1 0-2 1-1 0-2 0-3 1-1 0-2 1-2 1-1 0-1 0-2-1l-1 2v1h-5l-1 2-6 3v-2h2 1v-2l-1-1v-1c-1-2-3-4-5-6h0v-1h1c1 0 1-1 1-1l-1-1v-3c1-2 1-4 3-6h-1c2-2 3-2 6-2 1-1 1-1 2-1l-1 2h1 2l1-1 2 1-1-4 1-2-2-1z" class="J"></path><path d="M428 915c0 3 0 4 1 6h-1l-1 2-3-2v-1l1-1c1-2 2-3 3-4z" class="H"></path><path d="M386 899c2 0 3 0 6 1l-3 2v1 3 1h0c-1-1-2-2-2-3l-1-3v-2z" class="W"></path><path d="M386 899v2l1 3-3 3-1-1h-2c1-2 1-4 3-6l2-1z" class="N"></path><path d="M383 906v-1c1-1 2-2 3-4l1 3-3 3-1-1z" class="P"></path><path d="M392 900c2 1 4 3 6 4v1l-1 1h-3c-1 0-1 0-2-1v1h-1c1-1 1-1 0-2l-2-1v-1l3-2z" class="H"></path><path d="M407 905h7v1c-1 1-2 1-3 1 0 1 1 1 2 2l2-1 1 1v2c-1 0-2 0-2-1l-1 1c-2-1-2 0-4-1v-1c-1 0-1 0-2 1l-1-1c-1 1-2 1-3 1l-1-2c2-1 3-3 5-3z" class="W"></path><path d="M383 900c2-2 3-2 6-2 1-1 1-1 2-1l-1 2h1 2l1-1c1 3 1 3 4 4h2v1l-1 1c2 0 3 0 5-1l1 1v-1l-1-1c1-1 1-2 2-2 0 0 1 0 1-1l1 1h1c0 1 0 1-1 2 2 0 2-1 3-1v1h-1l-3 3c-2 0-3 2-5 3-1 0-2 1-3 2 0 1 1 0 0 1-1 0-2-1-2-2l-1-1v-1h0l2 1v-1-2-1c-2-1-4-3-6-4-3-1-4-1-6-1l-2 1h-1z" class="a"></path><path d="M380 912l1 1c1 0 2 0 3 1 1-2 1-1 1-3h1 1v-2l1-1v1l1 1c1 0 1 0 1-1 1 1 1 2 1 3v1l3-2c1 1 1 1 2 1-2 2-3 2-4 4l-1 2h4v1h-5l-1 2-6 3v-2h2 1v-2l-1-1v-1c-1-2-3-4-5-6z" class="l"></path><path d="M396 912h6c2-1 4 0 7 0h1c2 0 6 2 7 4l1 1 1 1v1c-1 0-1 0-2-1l-1 1v1l-2-1v-1h0l-2-2c-1 1-1 1-2 1h0c-1-2-2-2-3-3h-2c-1 1-1 0-2 1-1 0-2 0-3 1-1 0-2 1-2 1-1 0-1 0-2-1l-1 2h-4l1-2c1-2 2-2 4-4z" class="P"></path><path d="M394 892c1 0 4 1 5 1 2 0 4 1 6 2 1 1 2 1 3 1l-1 2 1 1h-1c0 1-1 1-1 1-1 0-1 1-2 2l1 1v1l-1-1c-2 1-3 1-5 1l1-1v-1h-2c-3-1-3-1-4-4l2 1-1-4 1-2-2-1z" class="F"></path><path d="M553 793c2 0 6-1 8 0-1 1-1 2-2 3l1 1h1c0 1 0 2-1 3l1 3s1 1 1 2c1 1 0 1 1 3l1 2 1 1h1c1 3 2 6 1 9 0 1 0 1 1 1v1c-1 1-1 1-1 2l1 2h1l2 2h-1c2 1 3 1 4 1h1l6 3c1 1 0 1 1 1s1 1 2 1c0 1 0 2 1 3v1h1l3 3h2c0 4 5 8 8 11l2 2 1 2 1 1c2 1 4 2 5 3l1 2h1c4-1 8 0 12 2 1 1 2 3 3 4 3 4 6 7 10 10h6c1 0 3 1 4 2 4 2 5 6 8 8h2c2 1 4 2 5 3 3 0 5 0 7 2l1 1h2l5-1c-1 2 0 2 1 3-1 1-2 1-3 2l3 2v1l2 2-1 1h2l1 1c-1 0-1 0-2 1-2 0-3-1-4 1-1 1-2 3-2 4-1 2 0 2-2 3-4-2-7-3-11-4l-1 1h-2 0c-2 0-3 1-4 2s-2 2-2 3h0c0 1-1 2-2 3 0 1-1 1-1 1h0l1 1c1 1 2 1 3 2v2l-2 2c-1 0-2-1-3-1-1-1-1-2-1-3s1-2 1-2c-1-1-1-1-2-1h0l-1-1-1 1c0 2-1 2 0 3-1 1-1 1-3 1h0l1-1c-2 0-2 1-3 1l-1 1h-1c-1 1 0 1-1 0-1 0-2-1-2-2l-2 1-1-1c-1 0-2 1-3 1-2 0-2 1-3 1l-3 2v1 2c-1-1-2-1-3-2-3-1-6-2-8-3-1-1-1-1-2-1l-1 1c1 1 1 1 1 2h-1c0-3-1-1-2-3l2-3h-3c-2-2-5-3-8-4l-1-1c0-1 1-2 1-3l-1-1c-8-3-15-8-23-11-1 0-1 0-2 1l-1 1c-3 0-4 0-6-1v-3c-2 0-4-1-5-1v-1c0-1 0-1 2-1v-1l-2-1 1-1h0l-16-10-10-8c0-1-2-2-2-3-2-1-4-3-5-6-3-3-5-8-5-11v-2c-1-3-2-9-1-12 1-1 1-2 1-3l1 1c0 1 0 2 1 3 0 2 0 4 1 6 1-1 0-1 0-1v-3l1 1v2h6v-1h-4l-1-1c0-1 0-1 1-2l-1-1-1-1h0v-4-20c0-1-1-4 0-5 0-1 1-1 1-2v-1-2c-1-2-3-4-4-6-3-2-5-3-8-3-1 0-1-1-2-1h-2c-3-1-7 0-10 1h-4l-2 2c-4 3-6 8-6 12v3h0l-1 1c0-1 0-2-1-3v-5c0-2-1-8-2-9v-4h35c-1 0-2 0-3-1h1 30c1-1 1-1 2-1 1-1 4 0 5 0z" class="X"></path><path d="M637 906l1-1v1h1l2 2v2c-2 0-4 1-6 1l-1-1 2-1c0 1 0 1 1 1v-1h-1c1-1 1-2 1-3z" class="t"></path><path d="M611 896l2 2v1 1 1l1-1 1 1-1 1h-4-1l-1-1 1-2 1-1-1-2h2z" class="C"></path><path d="M558 893l10 6c-2 0-3-1-4-1v1l-1-1-1 1c-2 0-4-1-5-1v-1c0-1 0-1 2-1v-1l-2-1 1-1z" class="M"></path><path d="M575 878l1 1v2 9c-1-2-3-2-4-3l1-1c0-1 0-2-1-3h0c0-2 2-1 3-2v-3z" class="a"></path><path d="M562 899l1-1 1 1v-1c1 0 2 1 4 1 1 0 2 1 3 2-1 0-1 0-2 1l-1 1c-3 0-4 0-6-1v-3z" class="R"></path><path d="M595 913l12 6v1 1h-1 0-3c-2-2-5-3-8-4l-1-1c0-1 1-2 1-3z" class="O"></path><path d="M591 893c2 0 2 0 3 1 0 2 1 3 0 5 2 1 3 2 4 3-4-1-8-3-12-5-1 0-3-1-4-3 2 0 3 0 5-1h3 1z" class="L"></path><path d="M587 893h3v3l-1 1c-2-2-2-2-2-4z" class="C"></path><path d="M591 893c2 0 2 0 3 1 0 2 1 3 0 5-1 0-2-1-3-1l1-1v-4h-1z" class="P"></path><path d="M550 877h4l2 1h0c2-1 7 0 9 0h2c0 1 0 4-1 5v1l1 1c1 0 1 0 2 1h2v1c-2 0-7-2-9-3-4-2-8-5-12-7z" class="i"></path><path d="M625 918h-1c0-1 1-2 1-2 1-1 1-3 1-3h1l1 1h3c-1-1-4-3-6-3h-1v-1l-3 1v-1c1 0 2-1 3-2 3-1 5 1 8 3 0 1 0 2-1 3h5v-1l2 1h0c-1 2-1 2-1 4h-2-2c0-2 0-2-1-2-2 0-2 0-3-1-2 1-2 2-4 3z" class="G"></path><path d="M632 897h1l1 1c1 1 3 1 4 1 1 1 2 1 3 3 0 2 0 2-2 4h-1v-1l-1 1c-1-1-1-2-3-2v1c-2 0-3-1-4-2l-1 2v-2h0c-1 1-1 2-2 3v-1-1-1c1-3 3-5 5-6zm-38-3l1 1 2 2c1 1 2 1 3 1h1v1l3 1 1-1h1v-2l3-1 1 2-1 1-1 2 1 1h1v1c1 0 2 0 4 1 1 1 1 1 2 1h1l-1 2c1 0 1 0 1 1l-1-1h0c-1-1-2-1-3-2v-1h-5c-1 1-2 1-3 1l-2-2h2 1c-1-1-2-1-3-1v1c-2 0-3 0-5-1-1-1-2-2-4-3 1-2 0-3 0-5z" class="F"></path><path d="M606 899v-2l3-1 1 2-1 1-1 2 1 1h-3c1-1 1-2 0-3z" class="s"></path><path d="M625 918c2-1 2-2 4-3 1 1 1 1 3 1 1 0 1 0 1 2h2 2 2l-1 2h1l2-2 1 1h0l-1 1h1c0 2-1 2 0 3-1 1-1 1-3 1h0l1-1c-2 0-2 1-3 1l-1 1h-1c-1 1 0 1-1 0-1 0-2-1-2-2-1-1-1 0 0-1v-1l-1-1-4 2h0c0-1 0-2 1-2l-1-1-1 1h-1l1-1-1-1z" class="D"></path><path d="M641 910c1 0 1 1 1 2l1 2 2 2v2h1 0 1v1 1h0l1 1c1 1 2 1 3 2v2l-2 2c-1 0-2-1-3-1-1-1-1-2-1-3s1-2 1-2c-1-1-1-1-2-1h0l-1-1-1 1h-1l1-1h0l-1-1-2 2h-1l1-2h-2c0-2 0-2 1-4h0l-2-1v1h-5c1-1 1-2 1-3 1 1 2 1 3 0 2 0 4-1 6-1z" class="P"></path><path d="M607 919l5 3 6 2c-3-3-7-4-10-7h1c1 0 2 1 3 0h2 1l-1-1-1-1c1 0 2 1 3 0h3v1h-2v1c1 1 1 1 0 2v3h1c1 0 0 0 1 1l-1 1 1 2c2 0 2 0 3-1h1l-3 2v1 2c-1-1-2-1-3-2-3-1-6-2-8-3-1-1-1-1-2-1l-1 1c1 1 1 1 1 2h-1c0-3-1-1-2-3l2-3h0 1v-1-1z" class="M"></path><path d="M638 899c1-1 2 0 3 1h1l1-1 1 1v-1h3 0 1v5c2 0 0-1 2 0h1c1 1 2 0 4 0l1 2 1-1c1 0 1 1 2 2h-1 0c1 1 2 1 3 1v1h-3l1 1-1 1h-2 0c-2 0-3 1-4 2s-2 2-2 3h0c0 1-1 2-2 3 0 1-1 1-1 1v-1-1h-1 0-1v-2l-2-2-1-2c0-1 0-2-1-2v-2l-2-2c2-2 2-2 2-4-1-2-2-2-3-3z" class="L"></path><path d="M649 911l-1-1h0c-1-1 0 0-2-1v-1-1c1-1 1-1 3 0v1 3z" class="H"></path><path d="M638 899c1-1 2 0 3 1h1l1-1 1 1v-1h3 0v4c-1 2-4 3-4 5 1 0 1 0 1 1s1 1 1 2h0c-1 0-1 0-2-1v1l2 1 1-1 1 1 1 1c1-1 0-1 1-2v-3h1c0 2 1 4-1 6l1 1h-1-1-2l-1 1-2-2-1-2c0-1 0-2-1-2v-2l-2-2c2-2 2-2 2-4-1-2-2-2-3-3z" class="X"></path><path d="M611 886c3-1 4-1 7 0l2 1c1 2 3 3 4 4 2 1 3 2 4 2l2 1h2l1-1c0-1 0-1 1 0h2 1c1 1 3 0 4 1 2 1 3 1 4 2v1c1 0 2 1 2 2h-3v1l-1-1-1 1h-1c-1-1-2-2-3-1-1 0-3 0-4-1l-1-1h-1c-1 0-2 0-3-1h1 0l1-1h-1c-1 0-3 1-3 1v2l-1 1h-1c1-1 1-1 1-2-1 0-1 1-2 1l-2 2h1 0 1c-1 2-1 2-3 3-1-1-1-2-1-3h1v-1c-1-1-1-2-2-3v2h-1c-1-1-2-3-4-4l-1-1s0-1-1-1l-1 1-1-1v-1h3 0c-2-1-2-1-4-1 0-2 2-3 2-4z" class="L"></path><path d="M611 886c3-1 4-1 7 0l2 1c1 2 3 3 4 4 2 1 3 2 4 2l-2 1c-4 3-5-3-9-3-1-1-3-1-4-2l-2-3z" class="q"></path><path d="M655 888c2 1 4 2 5 3 3 0 5 0 7 2l1 1h2l5-1c-1 2 0 2 1 3-1 1-2 1-3 2l3 2v1l2 2-1 1h2l1 1c-1 0-1 0-2 1-2 0-3-1-4 1-1 1-2 3-2 4-1 2 0 2-2 3-4-2-7-3-11-4l-1-1h3v-1c-1 0-2 0-3-1h0 1c-1-1-1-2-2-2l-1 1-1-2c-2 0-3 1-4 0h-1c-2-1 0 0-2 0v-5c1 0 1-1 2 0v2c3 0 4 0 6 1h1 1c0-2-1-4-2-5v-1h1l-2-2c-1-2-1-3-1-5l1-1z" class="h"></path><path d="M677 904h-2 0c-2 0-3 1-5 2v1l-1-1c1 0 1-1 0-1l-1-1c0 1 0 1-1 2v1h1v1l-2-1-1-1c2-1 0 0 0-1v-2c1-1 3-2 4-3 2-1 4 0 6 1h1l2 2-1 1z" class="P"></path><path d="M655 888c2 1 4 2 5 3 3 0 5 0 7 2l1 1h2l5-1c-1 2 0 2 1 3-1 1-2 1-3 2v-1h-1-2v1l-1 1c-2 1-4 3-5 4h-1-2-1v-1c-2-2-2-4-3-6l-2-2c-1-2-1-3-1-5l1-1z" class="f"></path><path d="M670 894l5-1c-1 2 0 2 1 3-1 1-2 1-3 2v-1l-3-3z" class="G"></path><path d="M663 900v-4l2-2v1 2l1 1h1l2 1c-2 1-4 3-5 4h-1c1-1 1-2 1-2l-1-1z" class="q"></path><path d="M655 888c2 1 4 2 5 3s1 2 2 3h-2 0c0 1 1 2 2 3l-1 2c1 0 1 1 2 1l1 1s0 1-1 2h-2-1v-1c-2-2-2-4-3-6l-2-2c-1-2-1-3-1-5l1-1z" class="P"></path><path d="M600 871h1l1-1c1 1 0 3 1 4h3v2c-1 0-1 1-2 1 0-1-1-2-1-2l-1 1v1h1c0 1 1 3 2 4v1l2 1v-1l1 1-1 1c0 3-3 3-4 5 2 0 3-2 4-1l2 1c0 1-1 2-1 3v1 1c1 1 2 1 3 2h-2l-3 1v2h-1l-1 1-3-1v-1h-1c-1 0-2 0-3-1l-2-2-1-1c-1-1-1-1-3-1h-1-3c-2 1-3 1-5 1-1-1-2-1-3-2l-2-1-1-1h0v-9-2l-1-1h-5l-1-1v-2h11l16-1 2-2c1 0 1-1 2-1z" class="Z"></path><path d="M600 898l-4-3c4-2 7-3 12-1 1 1 2 1 3 2h-2l-3 1v2h-1l-1 1-3-1v-1h-1z" class="N"></path><path d="M601 898c0-1-1-2 0-4 2 1 3 3 4 5l-1 1-3-1v-1z" class="s"></path><path d="M596 874c-1 1-1 1-2 1v1c-1 0-1 0-1 1-2 0-2 1-3 1h-2v-1l-1-1h-2v2c1 1 3 1 2 3h-1c-1 1-2 2-2 3-1 1-1 2-2 2l-1-1-1 1c-1 1-1 3-1 4l1 1-1 1-2-1-1-1h0v-9-2l-1-1h-5l-1-1v-2h11l16-1z" class="O"></path><path d="M577 891h0v-4-1c0-1 1-1 2-2l1 2c-1 1-1 3-1 4l1 1-1 1-2-1zm5-5v-3c1-1 1 0 1-1v-1h-2v-4h2v-1h2v2c1 1 3 1 2 3h-1c-1 1-2 2-2 3-1 1-1 2-2 2z" class="M"></path><path d="M569 875h11c0 2 0 5-1 7v1h-1l-2-2v-2l-1-1h-5l-1-1v-2z" class="Y"></path><path d="M600 871h1l1-1c1 1 0 3 1 4h3v2c-1 0-1 1-2 1 0-1-1-2-1-2l-1 1v1h1c0 1 1 3 2 4v1l2 1v-1l1 1-1 1c0 3-3 3-4 5 0 1 0 1-1 2v-2c-1 0-1 0-2 1l1 1h-1l-1-1v1 1c-1 0-1 1-1 2-1-1-1-3-1-4h-1v1h-1l-1-1 1-1h0-1l-1-1h-1-1v-1h2c0-2 0-2-1-3l1-1-1-1v-1c1-1 2-2 3-2h1 1c-2-2-2-2-3-4 1 0 1 0 2-1l2-2c1 0 1-1 2-1z" class="J"></path><path d="M600 871h1l1-1c1 1 0 3 1 4h3v2c-1 0-1 1-2 1 0-1-1-2-1-2l-1 1v1h1c0 1 1 3 2 4v1l2 1-1 1-2-2c-1 1-1 0-2 1v1c-2-1-3-2-3-4-1 0-1-1-2-1h0c-2-2-2-2-3-4 1 0 1 0 2-1l2-2c1 0 1-1 2-1z" class="L"></path><path d="M608 860l1 2h1c4-1 8 0 12 2 1 1 2 3 3 4 3 4 6 7 10 10h6c1 0 3 1 4 2 4 2 5 6 8 8h2l-1 1c0 2 0 3 1 5l2 2h-1v1c1 1 2 3 2 5h-1-1c-2-1-3-1-6-1v-2c-1-1-1 0-2 0h-1 0c0-1-1-2-2-2v-1c-1-1-2-1-4-2-1-1-3 0-4-1h-1-2c-1-1-1-1-1 0l-1 1h-2l-2-1c-1 0-2-1-4-2-1-1-3-2-4-4l1-1h1c-1-3-3-3-5-4v-1l-1 1-2-1s-1 0-2 1-1 0-2 0l-1 2-1-1-1-1v1l-2-1v-1c-1-1-2-3-2-4h-1v-1l1-1s1 1 1 2c1 0 1-1 2-1v-2c2 0 2 1 4 2-1-2-2-3-3-4-1 0-1-2-1-3-1-1 0-1-1-1s-1-1-2-1l3-3v-1h0c1-1 1-2 2-3z" class="H"></path><path d="M612 871c1-1 3-2 4-3l1 1 2 1h0-1-1v2h0c-1 1-1 1-1 2-1 0-1 0-2-1 0-1-1-1-2-2z" class="B"></path><path d="M608 860l1 2h1c4-1 8 0 12 2 1 1 2 3 3 4l-2 1h-1-1c-1 0-2-1-3-1h-2c-1 1-3 2-4 3l-1 1-1-1c-1-1-3-2-4-2-1-1 0-1-1-1s-1-1-2-1l3-3v-1h0c1-1 1-2 2-3z" class="w"></path><path d="M607 883l-2-1v-1c-1-1-2-3-2-4h-1v-1l1-1s1 1 1 2 0 1 1 2h2v-2c1 0 1 0 2 1h0l1-1c0 1 1 1 2 2l1-1c1 0 2 0 3 1v1h1v-4c1 1 1 1 1 2v1h1l1-1h2c1 0 1 0 3-1l-1 2c1 1 2 1 2 2 0 2-1 2-1 3v1c-1 1-1 2-1 3l2 2 1-1 1 1c1 0 1 0 2-1l1 1h0l-1 2h1l1 1c-1 0-1 0-2 1l-2-1c-1 0-2-1-4-2-1-1-3-2-4-4l1-1h1c-1-3-3-3-5-4v-1l-1 1-2-1s-1 0-2 1-1 0-2 0l-1 2-1-1-1-1v1z" class="B"></path><path d="M625 877h-1c-1-2-3-3-4-4h-2v1h-1v-1l1-1c4 0 5 2 8 4h0v-1c2 2 3 3 3 5h1v-1h1c2 2 3 2 6 2h2l-1-1c1 0 2 0 2-1l1-1c1 0 3 1 4 2 4 2 5 6 8 8h2l-1 1c0 2 0 3 1 5l2 2h-1v1c1 1 2 3 2 5h-1-1c-2-1-3-1-6-1v-2c-1-1-1 0-2 0h-1 0c0-1-1-2-2-2v-1c-1-1-2-1-4-2-1-1-3 0-4-1h-1-2c-1-1-1-1-1 0l-1 1h-2c1-1 1-1 2-1l-1-1h-1l1-2h0l-1-1c-1 1-1 1-2 1l-1-1-1 1-2-2c0-1 0-2 1-3v-1c0-1 1-1 1-3 0-1-1-1-2-2l1-2z" class="J"></path><path d="M641 878c1 0 3 1 4 2 4 2 5 6 8 8l-1 1c-2-1-3-4-5-5v1c-1-1-3-2-4-3l-1-1-1 1-2-1h0l-1-1c1 0 2 0 2-1l1-1z" class="N"></path><path d="M627 889c1-2 3-3 6-4h1c1 1 1 0 2 0h2 1l1-2h2v1h-1v1l1-1h2l1 1h-2l-1 1c-1 0-2 0-4 1l1 1-1 1c-1 0-2-1-2-1h-1l-1 1c3 2-1-1 2 1h1c1 0 2 0 3-1 1 2 1 2 1 4v1c-1-1-3 0-4-1h-1-2c-1-1-1-1-1 0l-1 1h-2c1-1 1-1 2-1l-1-1h-1l1-2h0l-1-1c-1 1-1 1-2 1l-1-1z" class="H"></path><path d="M640 889c1-1 1-2 2-2 3-1 5 0 7 0 3 3 5 5 7 9v1c1 1 2 3 2 5h-1-1c-2-1-3-1-6-1v-2c-1-1-1 0-2 0h-1 0c0-1-1-2-2-2v-1c-1-1-2-1-4-2v-1c0-2 0-2-1-4z" class="w"></path><path d="M649 887c3 3 5 5 7 9v1c1 1 2 3 2 5h-1-1c-2-1-3-1-6-1v-2c1 0 2 0 3 1v-1l2-2h0l-2-2h-1s-1-1-1-2c-1-1-1-2-2-3v-3z" class="q"></path><path d="M553 793c2 0 6-1 8 0-1 1-1 2-2 3l1 1h1c0 1 0 2-1 3l1 3s1 1 1 2c1 1 0 1 1 3l1 2 1 1h1c1 3 2 6 1 9 0 1 0 1 1 1v1c-1 1-1 1-1 2l1 2h1l2 2h-1c2 1 3 1 4 1h1l6 3c1 1 0 1 1 1s1 1 2 1c0 1 0 2 1 3v1h1l3 3h2c0 4 5 8 8 11l2 2 1 2 1 1c2 1 4 2 5 3-1 1-1 2-2 3h0v1l-3 3c1 0 1 1 2 1s0 0 1 1c0 1 0 3 1 3 1 1 2 2 3 4-2-1-2-2-4-2h-3c-1-1 0-3-1-4l-1 1h-1c-1 0-1 1-2 1l-2 2-16 1h-11v2l1 1h-1-2-2c-2 0-7-1-9 0h0l-2-1h-4l-10-8-2 1c-2-1-3-2-4-4h0 1l1-1-9-8h0l-1 1h0v1c-1-1-1-1-1-2s0-1-1-2l-1 1 1 1-1 1-1-1c-1 1 3 6 3 7v2c-3-3-5-8-5-11v-2c-1-3-2-9-1-12 1-1 1-2 1-3l1 1c0 1 0 2 1 3 0 2 0 4 1 6 1-1 0-1 0-1v-3l1 1v2h6v-1h-4l-1-1c0-1 0-1 1-2l-1-1-1-1h0v-4-20c0-1-1-4 0-5 0-1 1-1 1-2v-1-2c-1-2-3-4-4-6-3-2-5-3-8-3-1 0-1-1-2-1h-2c-3-1-7 0-10 1h-4l-2 2c-4 3-6 8-6 12v3h0l-1 1c0-1 0-2-1-3v-5c0-2-1-8-2-9v-4h35c-1 0-2 0-3-1h1 30c1-1 1-1 2-1 1-1 4 0 5 0z" class="Q"></path><path d="M601 854l1 2c0 1 0 2-2 3h1l-1 2-1 1v-2h-1l1-2h0c-1-1-1-3 0-4 0 1 1 0 2 0z" class="W"></path><path d="M565 830c-1-1-1-1-1-2 2-1 4 0 6 0 2 1 3 1 4 1l-1 4h0l-2-1h-1-1v-2h0l-1-1c-1 0-2 0-3 1z" class="P"></path><path d="M553 808l7 1 3-1 1 2s-1 0-2 1c1 1 1 2 1 4h0l-1 3-1-1h0v-5c-2 0-2 0-3 1h-1l1-2-1-1c-1 1-1 3-2 4v-1-1-2l-1-1h-5l4-1z" class="R"></path><path d="M560 809l3-1 1 2s-1 0-2 1h-1c0-1 0-1-1-2z" class="U"></path><path d="M555 845h14c3 0 7 0 10-1h0l1 1v1h0v2l-1 1 1 1h-1l-1-1-1-1-1 1c-1 0-1 0-2-1v1h-1-7c2-1 5 0 7-2-1-1-6 0-8 0l-3-1c-2 0-5 0-7-1z" class="B"></path><path d="M600 866c2-1 3-2 5-3h0v-2-1-1h0l-1 1h-1l-1-1 1-2c2 1 4 2 5 3-1 1-1 2-2 3h0v1l-3 3c1 0 1 1 2 1s0 0 1 1c0 1 0 3 1 3 1 1 2 2 3 4-2-1-2-2-4-2h-3c-1-1 0-3-1-4l-1 1h-1c-1-1 0-1-1-1h-1c1-2 2-2 3-3l1 2 1-1-3-2z" class="q"></path><path d="M566 849h7l2 3v1h-1-1-2c-1 0-2 0-3 1 0 0-1 1-2 1 0-1 0-2-1-3h-5v2h0l-1-1-1-1h-3c-1 0-1 1-2 2-1 0-1 0-3-1 1 0 2 0 2-1 3-2 6 0 9-2l3-1h2z" class="i"></path><path d="M568 854c0-2 0-2 1-4h1c2 1 2 2 3 3h-2c-1 0-2 0-3 1z" class="R"></path><path d="M562 811c1-1 2-1 2-1l1 1h1c1 3 2 6 1 9 0 1 0 1 1 1v1c-1 1-1 1-1 2l1 2h1l2 2h-1c-2 0-4-1-6 0 0 1 0 1 1 2s2 1 2 3l-2 1 1 1 1-1c1 2 0 4 0 6h-5v-18c1-2 1-5 1-7h0c0-2 0-3-1-4z" class="M"></path><path d="M549 809h5l1 1v2 1c-1 0-1 0-1 1s-1 2-2 4v13c0 3 1 6 0 9h1c0 1 0 2 1 3 1-1 0-1 2-1l-1 1h-1-3c-1 0-1-1-1-1h2 0v-1h-2c-1-2 0-5 0-7v-10h-1c0-2 0-2-2-4v-1c1-3 0-7 1-10h1z" class="l"></path><path d="M549 809h5l1 1v2l-4-1c-2 3-1 10-1 13h-1c0-2 0-2-2-4v-1c1-3 0-7 1-10h1z" class="U"></path><path d="M560 840c-2-1-2-1-4-1-1-3-1-6-1-9 0-5 1-10 1-15h2c2 0 2 0 3 2h0l1 1 1-3c0 2 0 5-1 7v18h-2z" class="N"></path><path d="M558 836l-1-1v-4l1-1v-4-4c0-1 0-3 1-4 0 2 0 3 1 4 1 4-1 9 0 14v1l-1-1h-1z" class="P"></path><path d="M563 815c0 2 0 5-1 7v18h-2c0-1 0-2-1-3h-1v-1h1l1 1v-1c-1-5 1-10 0-14 0-2 1-3 1-5l1 1 1-3z" class="I"></path><path d="M575 829l6 3c1 1 0 1 1 1s1 1 2 1c0 1 0 2 1 3v1h1l3 3h2c0 4 5 8 8 11l-1 1c-1 1-1 1-1 2l-1 1c1 0 1 0 1 1-1 1-1 1-2 1v-1h-2l1-1 2-2-2-2-1 1h-3l1-2c-2 1-2 0-3 1-1 0-1 0-2-1 0-1 0 0-1-1v-1c1-1 2-1 3-2 0 0 1 1 2 1v-1l-1-2c-1 1-1 1-2 1h-2v-2h-1c-1-2-3-4-4-6h1c-1-1-2-2-3-2s-2-1-2-2c-2-1-1-2-1-5z" class="s"></path><path d="M547 820c2 2 2 2 2 4h1v10c0 2-1 5 0 7h2v1h0-2s0 1 1 1c0 1 1 1 2 1v1h-18c-2 0-5 1-7-1-1 0-2 0-3 1 0-1 0-1 1-2 1 0 1-1 2-2h1v2l2-1 1 1h2c-1-2 0-4 0-6v-3-4h3v-2l1-3 1-1h0l1-1c3 0 5 0 7 2 0 1 0 0 1 1h0v-1c-1-2-1-3-1-5z" class="n"></path><path d="M550 834c0 2-1 5 0 7l-1 1c-1 0-1-1-2-1-1-1-1-4-1-5h1c0 1 0 1 1 2v-3l2-1z" class="S"></path><path d="M538 835l1-2h0c3 0 5-1 7 1h0-1l-1 1 1 1v3l-1-1h-1c0-1-1-1-1-2s-1-2-1-2h-2l-1 1z" class="j"></path><path d="M547 820c2 2 2 2 2 4h1v10l-2 1v3c-1-1-1-1-1-2h-1v-2h0c-2-2-4-1-7-1h0l-1 2h0c0 2 0 3 1 5l-1 1s0 1-1 2v-3c-1-1 0-2 0-3h-3v-3-4h3v-2l1-3 1-1h0l1-1c3 0 5 0 7 2 0 1 0 0 1 1h0v-1c-1-2-1-3-1-5z" class="Y"></path><path d="M534 830h3v7h-3v-3-4z" class="d"></path><path d="M547 820c2 2 2 2 2 4h1v10l-2 1v-1-1c-1-1 0-1-1-1-1-2-2-1-3-1v-1h0v-1h-2v-2h0l-2-1c-1-1-1-1-1-2h0l1-1c3 0 5 0 7 2 0 1 0 0 1 1h0v-1c-1-2-1-3-1-5z" class="M"></path><g class="S"><path d="M540 823c3 0 5 0 7 2h-1v2h-4l-2-1c-1-1-1-1-1-2h0l1-1z"></path><path d="M553 793c2 0 6-1 8 0-1 1-1 2-2 3l1 1h1c0 1 0 2-1 3l1 3s1 1 1 2c1 1 0 1 1 3l-3 1-7-1-4 1h-1c-1 3 0 7-1 10v1c0 2 0 3 1 5v1h0c-1-1-1 0-1-1-2-2-4-2-7-2l-1 1h0l-1 1-1 3v2h-3v4 3c0 2-1 4 0 6h-2l-1-1-2 1v-2h-1c-1 1-1 2-2 2l-1-1-1-1h0v-4-20c0-1-1-4 0-5 0-1 1-1 1-2v-1-2c-1-2-3-4-4-6-3-2-5-3-8-3-1 0-1-1-2-1h-2c-3-1-7 0-10 1h-4l-2 2c-4 3-6 8-6 12v3h0l-1 1c0-1 0-2-1-3v-5c0-2-1-8-2-9v-4h35c-1 0-2 0-3-1h1 30c1-1 1-1 2-1 1-1 4 0 5 0z"></path></g><path d="M560 797h1c0 1 0 2-1 3l1 3s1 1 1 2c1 1 0 1 1 3l-3 1-7-1v-2l-2-1v-1h0l1-1v-1c1 0 1 0 2-1-1 0-1 0-2-1v-1h1 1v-2h1c0 1 0 2 1 2h0l4-2z" class="Q"></path><path d="M553 793c2 0 6-1 8 0-1 1-1 2-2 3l1 1-4 2v-3h-2-4c-2-2-28-1-32-1h0c-1 0-2 0-3-1h1 30c1-1 1-1 2-1 1-1 4 0 5 0z" class="O"></path><path d="M535 802h-1v-2c1-2 2-1 4-1l1 1c0-1 1-2 2-2 0 0 0 1 2 0 1 0 1 0 1 1h0c0 2 0 3 1 4 1 0 1 0 2-1 1 2 0 4 0 6h1 0c2 0 3-2 3-3l2 1v2l-4 1h-1c-1 3 0 7-1 10v1c0 2 0 3 1 5v1h0c-1-1-1 0-1-1-2-2-4-2-7-2l-1 1h0l-1 1-1 3-1-15 1-5c-1-3-1-4-2-6z" class="Y"></path><path d="M551 805l2 1v2l-4 1h-1c-1 3 0 7-1 10v1c0 2 0 3 1 5v1h0c-1-1-1 0-1-1-2-2-4-2-7-2 3-2 2-6 3-9h1 1l-1 1v2l-1 1h1 1v5h1v-3c0-2 1-3 1-4-1-3-1-6-1-8h1 1 0c2 0 3-2 3-3z" class="l"></path><path d="M537 808h0c0-1 1-2 1-3v-1h1 0c1 1 1 1 2 1v3h1c0-2 0-3 1-4h1 1v3l-1 1h0c1 3 1 4 0 6h-1c-1 3 0 7-3 9l-1 1h0l-1 1-1 3-1-15 1-5z" class="o"></path><path d="M537 808h0c0-1 1-2 1-3v-1h1v10 3l1 1c1 1 0 4-1 6h0l-1 1-1 3-1-15 1-5z" class="m"></path><path d="M518 795h0l4 1c3 1 5 1 7 1v1h0c2 3 1 7 1 10v7c0 1 0 1 1 2v-5c1 0 1 0 1 1l1 1c0-3 1-5 1-7l1-5c1 2 1 3 2 6l-1 5 1 15v2h-3v4 3c0 2-1 4 0 6h-2l-1-1-2 1v-2h-1c-1 1-1 2-2 2l-1-1-1-1h0v-4-20c0-1-1-4 0-5 0-1 1-1 1-2v-1-2c-1-2-3-4-4-6-3-2-5-3-8-3-1 0-1-1-2-1h-2c-3-1-7 0-10 1h-4l-2 2c-4 3-6 8-6 12v3h0l-1 1c0-1 0-2-1-3v-5c0-2-1-8-2-9v-4h35z" class="j"></path><path d="M525 842v-5h3 0c1 0 2 0 3-1v1 1 4l-2 1v-2h-1c-1 1-1 2-2 2l-1-1z" class="p"></path><path d="M535 802c1 2 1 3 2 6l-1 5 1 15v2h-3v4h-1l-1-16v-5l1 1c0-3 1-5 1-7l1-5z" class="M"></path><path d="M535 802c1 2 1 3 2 6l-1 5c-1 1-1 2-2 4v-10l1-5z" class="k"></path><path d="M534 817c1-2 1-3 2-4l1 15v2h-3v-13z" class="K"></path><path d="M518 795h0l4 1c3 1 5 1 7 1v1h0c0 4 0 7-1 11 0 4 1 8 0 13 0-3 1-8-1-11-1 3 0 7-1 9 0-3 0-7-1-10v-1-2c-1-2-3-4-4-6-3-2-5-3-8-3-1 0-1-1-2-1h-2c-3-1-7 0-10 1h-4l-2 2c-4 3-6 8-6 12v3h0l-1 1c0-1 0-2-1-3v-5c0-2-1-8-2-9v-4h35z" class="v"></path><path d="M520 838l1 1c0 1 0 2 1 3 0 2 0 4 1 6 1-1 0-1 0-1v-3l1 1v2h6v-1h-4l-1-1c1-1 2-1 3-1 2 2 5 1 7 1h18 2c2 1 5 1 7 1l3 1c2 0 7-1 8 0-2 2-5 1-7 2h-2l-3 1c-3 2-6 0-9 2 0 1-1 1-2 1 2 1 2 1 3 1 1-1 1-2 2-2h3l1 1 1 1h0v-2h5c1 1 1 2 1 3 1 0 2-1 2-1 1-1 2-1 3-1l1 1h0l-1 1v2h-1v-2h-1l-1 2c2 0 2 0 2 1v1c1 0 1 0 3 1 1 0 1-1 2-1h5 1l2 1 2 1 2 2 2 2 1 2c2 2 3 2 6 2l1-1 2-2h1l3 2-1 1-1-2c-1 1-2 1-3 3h1c1 0 0 0 1 1-1 0-1 1-2 1l-2 2-16 1h-11v2l1 1h-1-2-2c-2 0-7-1-9 0h0l-2-1h-4l-10-8-2 1c-2-1-3-2-4-4h0 1l1-1-9-8h0l-1 1h0v1c-1-1-1-1-1-2s0-1-1-2l-1 1 1 1-1 1-1-1c-1 1 3 6 3 7v2c-3-3-5-8-5-11v-2c-1-3-2-9-1-12 1-1 1-2 1-3z" class="r"></path><path d="M528 854c-1-2-2-2-3-3v-3c3 0 5 1 7 1 0 0-1 1-2 1l-1 1c-1 1-1 1-1 3z" class="S"></path><path d="M540 869c-1 0-3-1-3-2 4 1 6 6 10 6 3 0 6 1 9 1l-2 3h-4l-10-8z" class="U"></path><path d="M525 845c1-1 2-1 3-1 2 2 5 1 7 1h18 2c2 1 5 1 7 1l3 1c2 0 7-1 8 0-2 2-5 1-7 2h-2c-3-2-10-1-14-2l-20-1h-4l-1-1z" class="V"></path><path d="M532 849h11l18 1c-3 2-6 0-9 2 0 1-1 1-2 1h-1v4h0-3-4 0c-1-2-1-3-2-4h-1c-3-1-6-1-9-2h-1l1-1c1 0 2-1 2-1z" class="U"></path><path d="M529 851h1c3 1 6 1 9 2h1c1 1 1 2 2 4h0c0 1 0 2-1 3-1 0-1 0-2-1l-1 1c2 1 3 2 5 3l8 3h1c0 1 1 2 3 2l1 2h-4-1c0-1 0-1-1-1l-1-1c-1 0-2 0-3-1h-1 0l-7-3h-1c-3-2-8-7-9-10 0-2 0-2 1-3z" class="I"></path><path d="M600 866l3 2-1 1-1-2c-1 1-2 1-3 3h1c1 0 0 0 1 1-1 0-1 1-2 1l-2 2-16 1h-11v2l1 1h-1-2-2c-2 0-7-1-9 0h0l-2-1 2-3c-3 0-6-1-9-1l2-1c1 1 1 1 2 1 2 1 26 0 29 0 2 0 3 0 5-1 3 0 7 1 10-1h0-3 0c-2 0-2 0-3-1 0-1 1-2 1-3 2 2 3 2 6 2l1-1 2-2h1z" class="V"></path><defs><linearGradient id="D" x1="555.016" y1="876.293" x2="567.507" y2="875.626" xlink:href="#B"><stop offset="0" stop-color="#151414"></stop><stop offset="1" stop-color="#2f2e2c"></stop></linearGradient></defs><path fill="url(#D)" d="M556 874l13 1v2l1 1h-1-2-2c-2 0-7-1-9 0h0l-2-1 2-3z"></path><path d="M558 852l1 1 1 1h0v-2h5c1 1 1 2 1 3v3 2l-1 3c-1-1-1-1-2-1l-1 2-1-4-1 1v4h-1l1 1h-1 0-1-6-1l-8-3c-2-1-3-2-5-3l1-1c1 1 1 1 2 1 1-1 1-2 1-3h4 3 0v-4h1c2 1 2 1 3 1 1-1 1-2 2-2h3z" class="g"></path><path d="M553 854c1-1 1-2 2-2h3v4 3c1 2 1 3 1 5h-1v-3c-1 1-1 1-1 2h-2c-1 1-1 1-1 2h-2c-1-1-2-1-3-1h-1c1-2 1-4 1-6 2 0 2 0 4 1-1 1-1 3-2 4v-3h-1v3c1 0 1 0 2 1 2-2 1-4 1-6 1-1 0-3 0-4h0z" class="T"></path><path d="M558 852l1 1 1 1h0v-2h5c1 1 1 2 1 3v3 2l-1 3c-1-1-1-1-2-1l-1 2-1-4-1 1v4h-1-5c0-1 0-1 1-2h2c0-1 0-1 1-2v3h1c0-2 0-3-1-5v-3-4z" class="D"></path><path d="M568 854c1-1 2-1 3-1l1 1h0l-1 1v2h-1v-2h-1l-1 2c2 0 2 0 2 1v1c1 0 1 0 3 1 1 0 1-1 2-1h5 1l2 1 2 1 2 2 2 2 1 2c0 1-1 2-1 3 1 1 1 1 3 1h-11c0-1-1-1-2-1 0 1 0 0-1 1v-1h-1c-4 1-11 0-15-1l-4 1s-1 0-1-1l-1 1-1-2c-2 0-3-1-3-2h6 1 0 1l-1-1h1v-4l1-1 1 4 1-2c1 0 1 0 2 1l1-3v-2-3c1 0 2-1 2-1z" class="J"></path><path d="M566 860v5c-1 1-2 1-3 1l-1-1v-1l1-2c1 0 1 0 2 1l1-3z" class="F"></path><path d="M568 854c1-1 2-1 3-1l1 1h0l-1 1v2h-1v-2h-1l-1 2c2 0 2 0 2 1v1l-1 1h2l-2 1 2 2c0 1-1 1-1 2h-1v-1c-1 1-1 0-1 1l-1-1-1-6v-3c1 0 2-1 2-1z" class="o"></path><path d="M570 859c1 0 1 0 3 1 1 0 1-1 2-1h5 1l2 1 2 1 2 2 2 2 1 2c0 1-1 2-1 3 1 1 1 1 3 1h-11c0-1-1-1-2-1 0 1 0 0-1 1v-1h-1l-6-1v-1l-1-2c2 0 4 1 6-1h0l-1-1v-1c-1-2-2-2-4-3h-2l1-1z" class="D"></path><path d="M589 865l1 2c0 1-1 2-1 3l-3-3c1-1 2-2 3-2z" class="N"></path><path d="M583 860l2 1 2 2 2 2c-1 0-2 1-3 2-1 0-1 0-2-1h-1-1v-1c-1-1-1-1-1-2v-1h0-2v-1l4-1z" class="q"></path><path d="M493 800l2-2h4c3-1 7-2 10-1h2c1 0 1 1 2 1 3 0 5 1 8 3 1 2 3 4 4 6v2 1c0 1-1 1-1 2-1 1 0 4 0 5v20 4h0l1 1 1 1c-1 1-1 1-1 2l1 1h4v1h-6v-2l-1-1v3s1 0 0 1c-1-2-1-4-1-6-1-1-1-2-1-3l-1-1c0 1 0 2-1 3-1 3 0 9 1 12v2c0 3 2 8 5 11 1 3 3 5 5 6 0 1 2 2 2 3l10 8 16 10h0l-1 1 2 1v1c-2 0-2 0-2 1v1c-3 0-7 1-9 2v3h-1c-2-2-3-3-5-3h0c-2-1-4 0-5 0h-1c-1 1-1 2-2 2 0 1 1 2 2 3h0 1c-1 1-3 1-5 1l-1-1-4 1 2 3h0l-3 3-1 1c-1 1-2 2-3 4l1 2 2 1h1 0l3 3h1c0-1-1-1 0-2 1 1 1 2 2 3h0 0l-2 2v1l-1 1h-1c0-1 1-1 1-2-1-2-3-2-4-3-2 1-4 1-5 3v1s-2 1-2 2v1c-1 2-2 1-2 4h-2 0c-2-2-3-1-5-1-2 1-8 0-11 0h-7 0-9c-1 1-3 1-4 1l-15-1v1l13 2c-4 0-8 0-11-1-4-1-9-2-12-1h-2c-5 0-10 1-15-1h0-1-2-2l-1-1 1-1 1 2 1-1c-1-1-1-2-2-3l1-1h0v2l1 1v1h2v-2s1 0 1 1h0c1 1 2 1 3 1l1-1v-1l-1 1c-1-1-1 0-2-1h0c-1-2-1-2-1-4h0l-1-2c0-1 0-2-1-3-1 0-1-1-3-1l-1 1h-1c-1-2-1-3-1-6h1c-1-1-1-1-3 0-1 1-1-1-2-2h-2v1l-1-1-1 1c-1-1-2-2-3-2v-1h-1v-2l-1-1-2 1c-1-1-2-1-2-2 1 0 2 0 3-1v-1h-7l3-3h1v-1c-1 0-1 1-3 1 1-1 1-1 1-2h-1l-1-1h1l-1-1 1-2c-1 0-2 0-3-1-2-1-4-2-6-2-1 0-4-1-5-1h-3c-3-1-5-3-8-4h-3-5l-2-2 2-1h0c1-1 0-1 1-2h-2-1c-3-1-4-2-6-3l-5-5c-2-1-5-1-7-1l4-1c-1-1 0-1 0-2v-1-1l-1-1c1-2 1-1 0-2 1-2 1-3 2-4 1 1 2 1 3 1 0 1 1 1 2 2l4-4c1 0 3 0 4 1v1c0-2-1-6 0-7h0c1-3 1-5 2-7 1-1 2-2 4-3 1 0 2 0 3 1l1-1 5-5c2-4 8-3 11-6 1-1 1-2 3-3 1 0 1 0 2 1h0 1l1-1h0v1l1 1h2v1l2-1 1 1c1 0 1 0 1-1h1v3h2v-1c-1-1-1-1-1-2l1-1v2l1 1c0 1 0 2 2 3h5c1 1 1 3 1 4l1-1v-6h1v5c1-1 0-1 1-2h0c1-1 1 0 2 0l1-2h1c2 0 3 0 5-1h1v7l-1 2c1 1 3 1 4 1l9 1c0-1 0-1-1-1-2 0-4-1-6-1 2 0 4-1 7 0h0 2c3 1 4 1 7-1h5c3 1 5 1 8 0v-1l-1-1c-1-1-1 0-1-1 0-2 0-2 2-2l1 1v2c0 1 1 1 1 2 4 1 9 0 13 1 2 0 5 0 7-1h-6c1-1 2-2 2-3h0l1-1v3h1c3-2 1-4 3-7v-1c1-2 0-6-1-8h-1 0l-2-2v-4c0-1-1-2-1-3 0-3 0-5-1-7l-1 1c0-3 1-5 2-7s2-3 3-5z" class="d"></path><path d="M449 880v1h-1l-1-1 1-1c-1-1-2-1-3-1v1h1l1 1h-3c0-2 0-2 1-4 1-1 2-1 4-1h1c1-1 1 0 2 0v3h-1l-2 2z" class="q"></path><path d="M511 908c2-1 4-1 6-1s4 0 6-1h0c0 1 0 1 1 1l2-1h1l2 3h-6-1c-1 1-2 1-2 1h-2l-2-2c-2 1-3 1-5 0z" class="T"></path><path d="M452 875c3 0 6 1 8 2 1 1 1 1 2 1v1c-1 1-1 1-1 2-1 0-2 0-2-1-2 1-3 2-5 2v-1h-1c-2 0-3 0-4-1l2-2h1v-3z" class="P"></path><path d="M499 895h14c2 0 4 0 5 1 1 0 2-1 4 0-10 2-21 2-32 1h-1l-1-1h3c3 0 5 0 8-1z" class="f"></path><path d="M483 886c3 0 6-1 9 0h7c7 0 14-1 22 0 2 0 4 0 6 1-5 1-10 0-15 0-7 0-14 1-21 0-3 0-5 0-8-1h0z" class="L"></path><path d="M483 893h26 14c2 1 4 0 7 1h0v1c-3 0-5 0-8 1-2-1-3 0-4 0-1-1-3-1-5-1h-14-7-3c-1 0-2 0-4-1h-5v-1h3z" class="B"></path><path d="M462 878h4v1c1 1 1 1 3 1l-2 1v1c1 1 1 1 2 1v1l-1-1h-2c0 1 0 2 1 3h-1-1c0-2 0-2-1-3h-1l1 2c0 2-1 2-2 3h0v-2h-1l-1 1v-1c-1 1-2 1-3 1 0-1 1-1 0-2l1-2c-1 0-1 1-2 1-1 1-2 1-3 1h0c-2 1-2 1-3 1l-1-1c1-1 2-1 4-1v-1h-5c1-1 1-1 2-1s2 0 3-1h1v1c2 0 3-1 5-2 0 1 1 1 2 1 0-1 0-1 1-2v-1z" class="f"></path><path d="M434 863h2v5c0 2-1 8 0 10l1 1h1c1-1 0-2 0-3 1 1 1 3 1 5l-1-1c-1 0-2 1-2 2v1 2c-1 0-2 0-3-1v-2h0 0c-1-1-2-3-3-4 0-1 1-3 1-5l1-5v-1l2-4z" class="l"></path><path d="M541 890h2l1-1 1 1v1h3 1v2l2 1c1 0 2 1 3 1l1 1 2 1v1c-3 0-7 1-9 2v3h-1c-2-2-3-3-5-3h0c-2-1-4 0-5 0v-1l1-1h2v-2l1-1h1l-1 1h1v-1c1 0 1-1 1-2-1 0-1 0-2-1s-4 1-6 0l1-1c2 0 3 0 5-1h0z" class="o"></path><path d="M531 883h6 2v-1c1 1 1 1 2 1h1l16 10h0l-1 1 2 1v1c-2 0-2 0-2 1l-2-1-1-1c-1 0-2-1-3-1l-2-1v-2h-1-3v-1l-1-1-1 1h-2-6v-1c2 0 5 0 7-2h-1c-3 1-6 1-9 0l3-1c-1-1-5 0-6 0l-2-1c1-1 2 0 4-1h0v-1z" class="R"></path><path d="M548 891h-1c-1-1-1 0-2-1l1-1-2-1 1-1c1 1 2 1 3 2 1 0 1 1 2 2 1 0 1 0 2 1h0c1 1 1 1 2 0 1 1 2 1 4 1h0l-1 1 2 1v1c-2 0-2 0-2 1l-2-1-1-1c-1 0-2-1-3-1l-2-1v-2h-1z" class="S"></path><path d="M475 866l2 1 3 2h0c1 1 2 2 2 3h-2v1l-1 1v1-1h-4v1l1 1h-2c-1-1-1-1-3-1h-1l-2 1c-1-1-1-1-3-2h0l7 5v1h-2v1h-1v-1c-2 0-2 0-3-1v-1h-4c-1 0-1 0-2-1h1c-3-3-8-4-12-4-2-2-3 0-5-1l1-1h4c1 1 1 0 2 0h1l1-1 2 2v-1l-1-1v-1l2 1c1 0 1-1 2-1s2 0 3 1c1-1 3-1 4-2l1-1v1c2 1 3 1 5 1v-1c1 0 1-1 2-1s1 0 2-1z" class="F"></path><path d="M456 870c1 0 1-1 2-1s2 0 3 1l1 1h-1c-2 0-3 0-5-1zm19-4l2 1 3 2-1 1c-1 0-1 1-2 1l-1 1-1-1c-1 2-1 2-1 3h-1c-1-1-1-2-1-4h0s0-1-1-1v-1c1 0 1-1 2-1s1 0 2-1z" class="s"></path><path d="M475 866l2 1-5 3s0-1-1-1v-1c1 0 1-1 2-1s1 0 2-1z" class="W"></path><path d="M485 897h1c-3-2-9-1-12-2-2 0-3-1-4-2h-2-1c0-1 1-1 2-1v-1-1h1 8l28 1c3 0 12-1 14 0-2 2-6 1-8 1h-20c-3 0-7 0-9 1h-3v1h5c2 1 3 1 4 1h3 7c-3 1-5 1-8 1h-3l1 1h1v1c3 1 7 1 10 1 4 0 9 0 13-1s8-1 12 0l-1 1c-2 0-4 0-6 1-3 0-7-1-9 1l-1 1c-2 0-4-1-5 0h-6c-1 1-3 1-3 1-1 0-2-1-2-1-4-1-9 1-13 0v-1c3-1 8 1 10-1 0-1 0-1-2-1l-2-2z" class="c"></path><path d="M525 898h2 0c-1 1-1 1-2 1v1h7v1h0-1c-2 0-3 1-3 2-1 0-2-1-2 0h-4l-1 1c2 0 3 0 4 1h-1c-1 0-1 0-2 1h-2 0-31v1h-1c-2 0-4-1-5 0 0 1 1 2 2 3h1 2 2v1c-2 1-4 0-6 1-2 0-2 0-4-1v1h1c1 1 0 1 1 1l-2 1c-1-1-1-1-1-2h-3v2 1l-2-2h-3v1h1c1 0 1 1 2 1l-1 1-2-2-1 1h-2c-1 0-2-1-2-2-2-2-3-2-5-3l-4-2h3c2 1 8 2 10 1l1-1h0c1 0 3-1 3-2h-2 0c-1-1-1-1-2-1v-1h6v-1l-1-1 1-1c-1-1-2-1-3-1v-2c2 0 5 0 7 1h0c1 1 1 1 2 1 0-1 0-1 1-2l2-1 2 2c2 0 2 0 2 1-2 2-7 0-10 1v1c4 1 9-1 13 0 0 0 1 1 2 1 0 0 2 0 3-1h6c1-1 3 0 5 0l1-1c2-2 6-1 9-1 2-1 4-1 6-1l1-1z" class="J"></path><path d="M525 898h2 0c-1 1-1 1-2 1v1h7v1h0-1c-2 0-3 1-3 2-1 0-2-1-2 0h-4l-1 1c2 0 3 0 4 1h-1c-1 0-1 0-2 1h-2 0v-1c-1 0-1-1-1-1h-5c-5 0-10 1-16 1-2 0-4 0-6-1h0c4-1 8 0 13 0h1v-1l-1-1h3l1-1c2-2 6-1 9-1 2-1 4-1 6-1l1-1z" class="K"></path><path d="M509 901c4 0 8 1 11 0h3-1-1c-2 2-3 1-5 2l-10 1v-1l-1-1h3l1-1z" class="D"></path><path d="M432 837c2 0 3 0 5-1h1v7l-1 2c1 1 3 1 4 1l9 1c0-1 0-1-1-1-2 0-4-1-6-1 2 0 4-1 7 0h0 2c3 1 4 1 7-1h5c3 1 5 1 8 0v-1l-1-1c-1-1-1 0-1-1 0-2 0-2 2-2l1 1v2c0 1 1 1 1 2 4 1 9 0 13 1 2 0 5 0 7-1 1 0 2 0 3-1l-2 5c-2 2-8 1-11 2h1c0 1-1 1-1 1h-1-5c-3 0-4-1-6 1h-1 0c-1 1-1 1-3 1v1c0 2 0 6-1 9v1c1 0 2 0 3 1l1-1h1l-1 1c2 1 3 1 4 1-1 1-1 1-2 1s-1 1-2 1v1c-2 0-3 0-5-1v-1l-1 1c-1 1-3 1-4 2-1-1-2-1-3-1s-1 1-2 1l-2-1v1l1 1v1l-2-2-1 1h-1c-1 0-1 1-2 0h-4l-1 1c2 1 3-1 5 1-2 1-5 0-7 1l-1 1v-1l2-1-1-2h1l-1-1h-1v-1-1c0-1 0-2 1-4h0l-1-1-1-1h-1c-1-1-1-1-3-2 0-1-1-2-1-3-1-1-1-1-1-2-1 0-1 1-2 0v-1l-2 1c0-1-1-1-1-2h-1c0-1 0-2 1-3-1 0-1 0-2-1v-8c1-1 0-1 1-2h0c1-1 1 0 2 0l1-2h1z" class="Q"></path><path d="M430 839l1-2h1l-1 1c2 2 1 8 1 11l-1 1c1 1 2 1 3 2-2 0-3-1-5-2 2-2 1-8 1-11z" class="d"></path><path d="M442 852c2-1 3-1 4 0h2c0 1-1 4 0 5 0 1-1 2-2 2h-2c-1-1-1-2-2-2v-1h-3l-3-3c3 0 5 3 8 3 1-1 2 0 3 0-1-2-3-3-5-4z" class="i"></path><path d="M427 841c1-1 0-1 1-2h0c1-1 1 0 2 0 0 3 1 9-1 11-1 0-1 0-2-1v-8zm15 11h0c-1-1-2-2-3-2h-1v-2-1l46 3h1c0 1-1 1-1 1h-1-5c-3 0-4-1-6 1h-1 0c-1 1-1 1-3 1v1c0 2 0 6-1 9v1c1 0 2 0 3 1l1-1h1l-1 1c2 1 3 1 4 1-1 1-1 1-2 1s-1 1-2 1v1c-2 0-3 0-5-1v-1l-1 1c-1 1-3 1-4 2-1-1-2-1-3-1s-1 1-2 1l-2-1v1l1 1v1l-2-2-1 1h-1c-2-1-3-3-4-3v-1c2 0 2 1 4 2 0-1-1-1-1-2h-1v-1c1 0 1-1 2-1h1 0-3c-1 1-1 1-2 1-2 0-2 1-3 2l-2-1v-3h1l2-1c0 1 0 1 1 1v-2h-2v-1h1v-1h-2l-4-4h3v1c1 0 1 1 2 2h2c1 0 2-1 2-2-1-1 0-4 0-5h-2c-1-1-2-1-4 0z" class="Y"></path><path d="M455 863c-2-3-1-8-1-11h1v1h1v9l-1 1z" class="o"></path><path d="M463 864v-1c0-2-1-7 1-9 1 3-1 7 0 11h1v-2h0c1-2 1-7 1-9h0c1 2 1 4 1 6 0 1-1 3 0 4h0c1 0 2 0 3 1l1-1h1l-1 1c2 1 3 1 4 1-1 1-1 1-2 1s-1 1-2 1v1c-2 0-3 0-5-1v-1l-1 1c-1 1-3 1-4 2-1-1-2-1-3-1s-1 1-2 1l-2-1v1l1 1v1l-2-2-1 1h-1c-2-1-3-3-4-3v-1c2 0 2 1 4 2 0-1-1-1-1-2h-1v-1c1 0 1-1 2-1h1 0c3 0 6 1 9 0 2 0 1 0 2-1z" class="C"></path><path d="M466 868l3-2c1 0 1 1 2 2h0v1c-2 0-3 0-5-1z" class="s"></path><path d="M448 852h1 2l1 5c0 2-1 4-1 6h1 3l1-1v-9c1-1 2-1 3 0 2 0 2 0 3 1 0 2 1 7 0 9 0 0 0 1 1 1-1 1 0 1-2 1-3 1-6 0-9 0h-3c-1 1-1 1-2 1-2 0-2 1-3 2l-2-1v-3h1l2-1c0 1 0 1 1 1v-2h-2v-1h1v-1h-2l-4-4h3v1c1 0 1 1 2 2h2c1 0 2-1 2-2-1-1 0-4 0-5z" class="Z"></path><path d="M449 852h2l1 5c0 2-1 4-1 6h1-1l-1 1h-1v-2c2-3 0-7 0-10z" class="O"></path><path d="M437 885c4-1 8 0 12 2l6 3c2 1 3 3 5 4 1 1 4 1 6 2l3 1c2 1 3 1 4 1v2c1 0 2 0 3 1l-1 1 1 1v1h-6v1c1 0 1 0 2 1h0 2c0 1-2 2-3 2h0l-1 1c-2 1-8 0-10-1h-3l4 2-1 1-2-2-1 1h0c1 1 1 1 1 2l-1 1h0v1h-2c-2 0-2-1-4 1h0v-2h0-1-2c-1 0-2 0-4-1-1 0-2 0-3 1l3 2h0c1-1 3-1 5-1-1 1-2 2-2 3 0 2 0 2-2 4h-1v1 1 1c1 1 1 1 1 2-1 2-2 2-3 3h-1c1-2 2-2 3-3v-1l-5 4-1-1v1c-1-1-2-2-2-3h0l-1-2c0-1 0-2-1-3-1 0-1-1-3-1l-1 1h-1c-1-2-1-3-1-6h1c-1-1-1-1-3 0-1 1-1-1-2-2h-2v1l-1-1-1 1c-1-1-2-2-3-2v-1h-1v-2l-1-1-2 1c-1-1-2-1-2-2 1 0 2 0 3-1v-1h-7l3-3h1v-1c-1 0-1 1-3 1 1-1 1-1 1-2h-1l-1-1h1l-1-1 1-2h4c2-1 3-2 5-2v1h1v-1l-1-1 1-1h3 1 0l-1 2 2 1 1-1-1-2 1-1h2c1-1 1-1 3 0 0 0 1-1 2-1l1 1 1-1h4l-2-1c1-1 1-2 2-3v-1z" class="s"></path><path d="M417 911h1l1 1h1 1v-2c2-2 3-3 6-3-1 1-2 3-4 5l-1 1v1l-1-1-1 1c-1-1-2-2-3-2v-1z" class="W"></path><path d="M449 887l6 3c-1 1-2 1-3 1s-1 0-2 1h0c-1 0-2 0-3-1h0c-1-1-2-1-3-1 1-3 1-1 3-2 1 0 1-1 2-1z" class="q"></path><path d="M438 912h3c0-1-1-1-1-2-1 0-1-1-1-2-1 0 0 0 0-1 1 0 1 0 2 1 2 0 3 1 5 1 1 1 2 2 2 3-1 1-2 0-4 0-1 0-2 0-3 1l3 2h0c1-1 3-1 5-1-1 1-2 2-2 3-1 0-3 0-4-1-2-1-4-1-5-4z" class="W"></path><path d="M437 885c4-1 8 0 12 2-1 0-1 1-2 1-2 1-2-1-3 2h-4-3l-2-1c1-1 1-2 2-3v-1z" class="C"></path><path d="M427 907l4-2c1 1 2 1 2 2 1 1 2 1 3 2h-3v1l2 1c-1 0-2 0-3-1l-1 1 1 1h-1 0c-1-1-1 0-2-1v1h-2l-1 2v1c-1 1-1-1-2-2h-2l1-1c2-2 3-4 4-5z" class="G"></path><path d="M435 911l2-1v1h-1v1h2c1 3 3 3 5 4 1 1 3 1 4 1 0 2 0 2-2 4h-1v1c-1 0-1 1-2 1h-1l-1 1-1-1h0c1-1 1-1 2-1h1c1-1 1-2 2-2h0v-1c-2 0-2 0-3-1l-3-1-4-2h0c-2 0-3-1-5 0-1-1-1-1-3 0v-1l1-2h2v-1c1 1 1 0 2 1h0 1l-1-1 1-1c1 1 2 1 3 1z" class="B"></path><path d="M453 905h-4 0l1-2v-1l-1 1c-1-1-2-1-3-1h-1c-1-1-1-1-2-1h-1-2-1l-1-1c2-2 3 0 6 0h2 0v-1h0c1 0 2 1 3 1l1-1h2v-1c2 1 3 0 4 0h1l1 1h4c1 0 3 1 3 1 1 0 1-1 2 0 1 0 1 0 2 1h1-2l1 1v1l-1 1c-4 0-8 1-11 0l-1-1c0-1-1-1-2-1 0-1-1-1-1-2l-1 1v1c0 1 0 3 1 3z" class="W"></path><path d="M429 915c2-1 3 0 5 0h0l4 2 3 1c1 1 1 1 3 1v1h0c-1 0-1 1-2 2h-1c-1 0-1 0-2 1h0l1 1 1-1h1c1 0 1-1 2-1v1 1c1 1 1 1 1 2-1 2-2 2-3 3h-1c1-2 2-2 3-3v-1l-5 4-1-1v1c-1-1-2-2-2-3h0l-1-2c0-1 0-2-1-3-1 0-1-1-3-1l-1 1h-1c-1-2-1-3-1-6h1z" class="F"></path><path d="M429 915c2-1 3 0 5 0h0l4 2c-1 0-1 1-2 2l1 1v1l-2-1-1 1c-1 0-1-1-3-1l-1 1h-1c-1-2-1-3-1-6h1z" class="C"></path><path d="M466 896l3 1c2 1 3 1 4 1v2c1 0 2 0 3 1l-1 1 1 1v1h-6v1c1 0 1 0 2 1h0 2c0 1-2 2-3 2h0l-1 1c-2 1-8 0-10-1h-3-1c-1 0-1 0-2-1h0l-2 1-1-1 1-1h1v-1c-1 0-1-2-1-3v-1l1-1c0 1 1 1 1 2 1 0 2 0 2 1l1 1c3 1 7 0 11 0l1-1v-1l-1-1h2 2v-1c-1-1-2-1-3-1h-1c-2-1-2-2-2-3z" class="H"></path><path d="M408 896h4c2-1 3-2 5-2v1h1v-1l-1-1 1-1h3 1 0l-1 2 2 1-1 1c2 1 3 1 5 1l2-1v3h2v1c-2 0-3 1-4 1h-1c-1 1-2 1-3 1l-5 2c-1 1-3 1-4 1h-7l3-3h1v-1c-1 0-1 1-3 1 1-1 1-1 1-2h-1l-1-1h1l-1-1 1-2z" class="W"></path><path d="M411 901c1-1 3-2 4-2s0 1 1 0c1 0 2 0 3-1v1l3 1c-1 1-1 1-1 2 1 0 3-1 5-1-1 1-2 1-3 1l-5 2c-1 1-3 1-4 1h-7l3-3h1v-1z" class="F"></path><path d="M484 912c2-1 4 0 6-1v-1h-2-2-1c-1-1-2-2-2-3 1-1 3 0 5 0h1c7 1 14 0 21 1h1c2 1 3 1 5 0l2 2h2s1 0 2-1h1 6 0l-3 3-1 1c-1 1-2 2-3 4l1 2 2 1h1 0l3 3h1c0-1-1-1 0-2 1 1 1 2 2 3h0 0l-2 2v1l-1 1h-1c0-1 1-1 1-2-1-2-3-2-4-3-2 1-4 1-5 3v1s-2 1-2 2v1c-1 2-2 1-2 4h-2 0c-2-2-3-1-5-1-2 1-8 0-11 0h-7 0-9c-1 1-3 1-4 1l-15-1v1l13 2c-4 0-8 0-11-1-4-1-9-2-12-1h-2c-5 0-10 1-15-1h0-1-2-2l-1-1 1-1 1 2 1-1c-1-1-1-2-2-3l1-1h0v2l1 1v1h2v-2s1 0 1 1h0c1 1 2 1 3 1l1-1v-1l-1 1c-1-1-1 0-2-1h0c-1-2-1-2-1-4 0 1 1 2 2 3v-1l1 1 5-4v1c-1 1-2 1-3 3h1c1-1 2-1 3-3 0-1 0-1-1-2v-1-1-1h1c2-2 2-2 2-4 0-1 1-2 2-3-2 0-4 0-5 1h0l-3-2c1-1 2-1 3-1 2 1 3 1 4 1h2 1 0v2h0c2-2 2-1 4-1h2v-1h0l1-1c0-1 0-1-1-2h0l1-1 2 2 1-1c2 1 3 1 5 3 0 1 1 2 2 2h2l1-1 2 2 1-1c-1 0-1-1-2-1h-1v-1h3l2 2v-1-2h3c0 1 0 1 1 2l2-1c-1 0 0 0-1-1h-1v-1c2 1 2 1 4 1z" class="e"></path><path d="M499 931c3-1 10-1 13 0l-2 2h-8l1-1v-1h-4z" class="V"></path><path d="M497 919c6 0 13 1 19 0l-2 2h-1c-2-1 0 0-2 0h-9l-5-1h3 0c-1 0-2 0-3-1z" class="E"></path><path d="M486 933c-5 0-11 0-17-1v-1h2 5c-1 0-2 0-3-1 4 0 8 0 12 1h12 2 4v1l-1 1h-4-7-5z" class="N"></path><g class="G"><path d="M497 931h2 4v1l-1 1h-4-7-5c4-1 7-1 11-1v-1z"></path><path d="M476 923c2 0 4 1 6 1h16c5 0 11 1 17 0v1h1v1c-8 1-16 0-24 0 2 1 6 0 8 0 3 1 8 2 12 1h1v1l-1 1c-3 1-7 0-11 1-5 1-13 1-19 0-2-1-6-1-8-2v-1h3c-2-1-2-1-3-1v-1h4v-1c-1 0-1 0-2-1z"></path></g><path d="M468 915h2l1-1 2 2 1-1c2 1 4 2 6 2 3 0 11-1 13 0v1c1 0 3 0 4 1s2 1 3 1h0-3l5 1h9c2 0 0-1 2 0h1l2-2 2-1c1 0 1 1 2 1l1 1s1 0 1 1c-1 0-1 1-2 1v1c-2-1-2-2-4-2l1 1c1 0 1 0 2 1h1c0 2-1 3-2 4h0c0-1 1-2 1-2-1-1 0-2-2-2-1 0-1 1-2 0h-2c-5 1-11 0-16 0-4 0-8 1-12 0s-9 0-12-1h0l-2-1v-1l-1-1h-1c0-1-1-1 0-1v-1l-1-2z" class="I"></path><path d="M502 921h-13l-1-1c-1-2-5-1-7 0l-2-1c1-1 1-1 2-1h1 11c1 0 3 0 4 1s2 1 3 1h0-3l5 1z" class="Q"></path><path d="M457 914v-1h0l1-1c0-1 0-1-1-2h0l1-1 2 2 1-1c2 1 3 1 5 3 0 1 1 2 2 2l1 2v1c-1 0 0 0 0 1h1l1 1v1 3c-1 0-1 0-2 1-1 0-1-1-2-1h-1l3 3c-2-1-3-1-4-3-2-2-3-3-6-5 2 2 4 4 5 6-1 0-1 1-1 1h-2 0-1l-1-2c0 2 0 4 1 6v1c-1-1-1-1-1-2 0-2-1-4-3-5h-1l-1 1v1c0 2 0 3-1 5h1 1c-1 1-2 1-3 2h-5c0-1-1-1 0-2v-1h-1l1-1h0 2 0-2v-1-1-2h0l1 1c1 1 3 1 4 3l1-1c-2-2-4-1-5-4-1-1-2-1-2-2-1 1-1 1 0 2h-1l-1-1v-1-1h1c2-2 2-2 2-4 0-1 1-2 2-3-2 0-4 0-5 1h0l-3-2c1-1 2-1 3-1 2 1 3 1 4 1h2 1 0v2h0c2-2 2-1 4-1h2z" class="H"></path><path d="M457 914v-1h0l1-1c0-1 0-1-1-2h0l1-1 2 2 1-1c2 1 3 1 5 3-2 1-4 2-4 3h-4-1-1-1l1-1 1-1z" class="W"></path><path d="M484 912c2-1 4 0 6-1v-1h-2-2-1c-1-1-2-2-2-3 1-1 3 0 5 0h1c7 1 14 0 21 1h1c2 1 3 1 5 0l2 2h2s1 0 2-1h1 6 0l-3 3-1 1c-1 1-2 2-3 4h-1v1h0v1l-1-1c-1 0-2-1-4 0h-6c-5 1-11 0-17-1-2-1-10 0-13 0-2 0-4-1-6-2-1 0-1-1-2-1h-1v-1h3l2 2v-1-2h3c0 1 0 1 1 2l2-1c-1 0 0 0-1-1h-1v-1c2 1 2 1 4 1z" class="E"></path><path d="M518 910h2s1 0 2-1h1 6 0l-3 3-1 1c-1 1-2 2-3 4h-1v1h0v1l-1-1c-1 0-2-1-4 0h-6 0v-1l-2 1c-3-2-6-1-10-2 5 0 11 1 15-1h2l1-1h2 0l1-1h-4c1-1 2-1 3-1l1-1-2-1h1z" class="K"></path><path d="M484 912c2-1 4 0 6-1v-1h-2-2-1c-1-1-2-2-2-3 1-1 3 0 5 0h1c7 1 14 0 21 1h1c2 1 3 1 5 0l2 2h-1l2 1-1 1c-1 0-2 0-3 1h4l-1 1h0-2l-1-1-4 1c-5 1-12 1-17 0-1 0-3-1-4-1-3 1-5 1-8 0-1 0 0 0-1-1h-1v-1c2 1 2 1 4 1z" class="T"></path><path d="M482 913c-1 0 0 0-1-1h-1v-1c2 1 2 1 4 1h14 0c-1 1-3 2-4 2s-3-1-4-1c-3 1-5 1-8 0z" class="d"></path><path d="M510 908h1c2 1 3 1 5 0l2 2h-1c-3 1-5 2-7 2h-3l-1-1h-7v-1l5-1h-3v-1h9 0z" class="G"></path><path d="M507 912h3c2 0 4-1 7-2l2 1-1 1c-1 0-2 0-3 1h4l-1 1h0-2l-1-1-4 1c-5 1-12 1-17 0 1 0 3-1 4-2h0 0c2-1 5-1 8-1l1 1z" class="h"></path><path d="M498 912h0c2-1 5-1 8-1l1 1c-2 1-4 1-7 1 0 0-1 0-2-1h0z" class="c"></path><path d="M402 832c1 0 1 0 2 1h0 1l1-1h0v1l1 1h2v1l2-1 1 1c1 0 1 0 1-1h1v3h2v-1c-1-1-1-1-1-2l1-1v2l1 1c0 1 0 2 2 3h5c1 1 1 3 1 4l1-1v-6h1v5 8c1 1 1 1 2 1-1 1-1 2-1 3h1c0 1 1 1 1 2l2-1v1c1 1 1 0 2 0 0 1 0 1 1 2 0 1 1 2 1 3 2 1 2 1 3 2h1l1 1c-1 1-2 2-2 4v1h0v-5h-3-2l-2 4v1l-1 5c0 2-1 4-1 5 1 1 2 3 3 4h0 0v2c1 1 2 1 3 1h1v1c-1 1-1 2-2 3l2 1h-4l-1 1-1-1c-1 0-2 1-2 1-2-1-2-1-3 0h-2l-1 1 1 2-1 1-2-1 1-2h0-1-3l-1 1 1 1v1h-1v-1c-2 0-3 1-5 2h-4c-1 0-2 0-3-1-2-1-4-2-6-2-1 0-4-1-5-1h-3c-3-1-5-3-8-4h-3-5l-2-2 2-1h0c1-1 0-1 1-2h-2-1c-3-1-4-2-6-3l-5-5c-2-1-5-1-7-1l4-1c-1-1 0-1 0-2v-1-1l-1-1c1-2 1-1 0-2 1-2 1-3 2-4 1 1 2 1 3 1 0 1 1 1 2 2l4-4c1 0 3 0 4 1v1c0-2-1-6 0-7h0c1-3 1-5 2-7 1-1 2-2 4-3 1 0 2 0 3 1l1-1 5-5c2-4 8-3 11-6 1-1 1-2 3-3z" class="h"></path><path d="M373 856c1-3 1-5 2-7 1-1 2-2 4-3 1 0 2 0 3 1v1 4l-2-1v5c-1 1-3 2-3 4l-2 1h-1c0-1 0 0 1-1 0-1 0-1 1-1l1-1h0v-1h-2 0-1l1-1h0c-1-1-1-1-2 0z" class="N"></path><path d="M380 870c0-1 1-2 2-4h0c2-2 4-2 6-3l4-1v1l-1 1h0v1c1 1 1 2 1 4v1 1l-2 1c-2 0-2 0-3 1h-3l-1-1c1-1 1-1 1-2v-1c-2 0-2 0-4 1v1-1z" class="B"></path><path d="M409 835l2-1 1 1c1 0 1 0 1-1h1v3h2v-1c-1-1-1-1-1-2l1-1v2l1 1c0 1 0 2 2 3h5c1 1 1 3 1 4-3 0-7 1-9 0h-1c-1-1-3-1-4-1h0-2v-2c1-1 0-2 0-3v-1s-1 0-1-1h1z" class="g"></path><path d="M383 853c1 0 1 0 1-1l1-1 1-2h0l1 1c-1 1-1 1-1 2l2 2h2s1-1 2-1c0 0 0 1 1 1 0-1 0-2 1-3h1 0l1-1h4 1v1c-1 0-1 0-1 1h-1l-1 1c0 1-1 2-2 3s-1 2-2 3c-1-1 0 0 0-1v-2l-2 2h-1l-2-1c-1 1-2 3-3 3h0c-1-1-2-1-3-1h-1v1l1 1 1-1v1 1 1h-1 0v-1l-1-1c-1 1-1 2-1 2h-1l1-2-2-1c1-3 3-4 4-6v-1z" class="H"></path><path d="M402 832c1 0 1 0 2 1h0 1l1-1h0v1l1 1h2v1h-1c0 1 1 1 1 1v1l-2 1c0 1 0 1 1 2l-1 1c1 2 1 3 1 5h-1v1h-1v-1h-1v-2h-1c0 2 1 3 1 4h1l-2 1 2 2c-1 0-2 0-3-1v1c-1 0-1 0-2-1h-1-4l-1 1h0-1c-1 1-1 2-1 3-1 0-1-1-1-1-1 0-2 1-2 1h-2l-2-2c0-1 0-1 1-2l-1-1h0l-1 2-1 1c0 1 0 1-1 1 1-2 1-4 0-7l5-5c2-4 8-3 11-6 1-1 1-2 3-3z" class="q"></path><path d="M383 846l5-5h0c0 2-1 3-2 4v1h1v-1c1-1 2-1 3-1l1 1c-1 1-1 2-2 3v1h4v1l2 1h-1c-1 1-1 2-1 3-1 0-1-1-1-1-1 0-2 1-2 1h-2l-2-2c0-1 0-1 1-2l-1-1h0l-1 2-1 1c0 1 0 1-1 1 1-2 1-4 0-7z" class="C"></path><path d="M407 834h2v1h-1c0 1 1 1 1 1v1l-2 1c0 1 0 1 1 2l-1 1c1 2 1 3 1 5h-1v1h-1v-1h-1v-2h-1c0 2 1 3 1 4h1l-2 1 2 2c-1 0-2 0-3-1v1c-1 0-1 0-2-1h-1-4l-2-2v-2h2l3-1c1 0 1 0 2-1h-5l1-1c1 0 1 0 2-1h1s1 0 2 1h1v-1c-1-1 0-1-1-1l1-1c0-1 0-1 1-1 2-2 3-3 3-5z" class="L"></path><path d="M396 850l-2-2v-2h2l1 1h0c2 1 3 2 5 3h-1-1-4z" class="C"></path><path d="M425 843l1-1v-6h1v5 8c1 1 1 1 2 1-1 1-1 2-1 3h1c0 1 1 1 1 2l2-1v1c1 1 1 0 2 0 0 1 0 1 1 2 0 1 1 2 1 3 2 1 2 1 3 2h1l1 1c-1 1-2 2-2 4v1h0v-5h-3-2l-2 4v1l-1 5c0 2-1 4-1 5h-1l-1-1h-4c-1 0-3 2-3 3-1-1-1-3-2-4l-1 1c-1-1-2-1-3-1 0 1 0 1-1 2l-1 1-1 1c-1 0-1 0-2-1 0-1 0-1-1-1v-1c-2 1-2 1-3 0l-2-1h0c1-1 0-1 1-1l-1-1v-1c1-1 2-2 2-3-2-1-4-3-7-4-1 1-2 1-3 2h-1l-3 2v-1c0-2 0-3-1-4v-1h0l1-1v-1l7-3h2l1 1h1v-1-1l3-1 1-1v-2c0-1 0-2 1-3 0-1 0-1 2-1v1l-2 1h1s1 0 1-1c1 0 2-1 2-1l1-1h-1-1l1-1h1v-1l-1-1v-1c3-1 8-1 12-1h1v-1z" class="X"></path><path d="M416 862h0-2v-1h8c1-1 2-1 3 0-2 0-2 0-3 2-1 0-4-1-6-1z" class="Q"></path><path d="M425 861c5 0 9 0 14 1h1l1 1c-1 1-2 2-2 4v1h0v-5h-3-2-12c1-2 1-2 3-2z" class="E"></path><path d="M407 856v-2c0-1 0-2 1-3 0-1 0-1 2-1v1l-2 1h1s1 0 1-1c1 0 2-1 2-1l2 2v1c1 0 1 0 1 1 1 1 1 3 0 5v1l-1-1v-2c-1-2-2-1-2-3v-1l-1 1c-1 0-2 1-3 1v1h-1z" class="g"></path><path d="M416 862c2 0 5 1 6 1h12l-2 4v1l-2-2c-1 2 0 2-2 4v-3c0-1-1-1-2-2l-1-1-1 1-1-1c-1 0-2 0-3 1h0c-2-1-3-1-4-2v-1z" class="U"></path><path d="M408 856c-1 1-3 4-4 4 1 1 2 1 2 2-2 1-2 2-4 1h-1l-1 1c1 1 2 1 4 2l1-1v1l-1 1c-2 0-3-2-4-2l-1 1h0c-1 1-2 1-3 2h-1l-3 2v-1c0-2 0-3-1-4v-1h0l1-1v-1l7-3h2l1 1h1v-1-1l3-1 1-1h1z" class="L"></path><path d="M425 844l1 1v3c-1 3 0 7-1 10l-1-1h-2v-2c0-2 0-3-1-3 0 0-1 1-1 2v4h0l-1-1h-1-1v-5h-1-2l-2-2 1-1h-1-1l1-1h1v-1l-1-1v-1c3-1 8-1 12-1h1z" class="O"></path><path d="M425 844l1 1v3c-2 1-2 2-3 3h-2l-1-2v-3h2c1 0 2-2 2-2h1z" class="U"></path><path d="M426 865c1 1 2 1 2 2v3c2-2 1-2 2-4l2 2-1 5c0 2-1 4-1 5h-1l-1-1h-4c-1 0-3 2-3 3-1-1-1-3-2-4l-1 1c-1-1-2-1-3-1v-1h0l-1-1 1-1h1 0c0-1-1-2 0-3h0c-2 0-4 0-6-1v-1h1c1 1 2 1 3 1s1-1 2-1 1 0 2-1c1 0 1 1 2 1 1-1 1-2 2-2l2 1 2-2z" class="o"></path><path d="M428 871l2 2h1c0 2-1 4-1 5h-1l-1-1v-6z" class="M"></path><path d="M428 870c2-2 1-2 2-4l2 2-1 5h-1l-2-2v-1z" class="O"></path><path d="M399 866c3 1 5 3 7 4 0 1-1 2-2 3v1l1 1c-1 0 0 0-1 1h0l2 1c1 1 1 1 3 0v1c1 0 1 0 1 1 1 1 1 1 2 1l1-1 1-1c1-1 1-1 1-2 1 0 2 0 3 1l1-1c1 1 1 3 2 4 0-1 2-3 3-3h4l1 1h1c1 1 2 3 3 4h0 0v2c1 1 2 1 3 1h1v1c-1 1-1 2-2 3l2 1h-4l-1 1-1-1c-1 0-2 1-2 1-2-1-2-1-3 0h-2l-1 1 1 2-1 1-2-1 1-2h0-1-3l-1 1 1 1v1h-1v-1c-2 0-3 1-5 2h-4c-1 0-2 0-3-1-2-1-4-2-6-2-1 0-4-1-5-1h-3c-3-1-5-3-8-4h-3-5l-2-2 2-1h0c1-1 0-1 1-2h-2v-3c1-1 0-1 1-2s0-1 1-2c0-2 2-4 4-6v1-1c2-1 2-1 4-1v1c0 1 0 1-1 2l1 1h3c1-1 1-1 3-1l2-1v-1l3-2h1c1-1 2-1 3-2z" class="t"></path><path d="M381 884h0 4 1v-1h1v1l1 1c0 1-1 1-1 2h-2l1-1-1-1c-1 0-2 1-3 1l-2-2h1z" class="d"></path><path d="M399 893v-1h2v-1h-2c0-2 1 0 2-1l1-1v-1-1l-1-1 1-1 1 1v3s1 0 2 1 2 1 2 3h0l-2 2c-2-1-4-2-6-2z" class="j"></path><path d="M403 886c0-1 0-2 1-3v1h1v-1c0-1 0-1 1-2l2 3 2-2h2 0c1-1 3 0 4-1v1c0 1 2 3 3 4h-1c-2-2-3-3-6-3-2 0-3 0-4 2-1 1-2 3-3 5-1-1-2-1-2-1v-3z" class="g"></path><path d="M395 874c1 1 4 1 5 2h-4l-1 1 2 1h1c1 0 1 0 2 1h0c0 2 0 2 1 5h-2c-1 0-1 0-2-1-2 1-4 2-6 1 1 1 0 1 1 2h0c-1 0-2-1-3-1v-1l-1 1-1-1v-1h-1v1h-1-4 0l5-4v-1l1-1c1-1 2-2 3-2h1c1 0 1-1 2-1l1 1h1l-1-1 1-1z" class="X"></path><path d="M393 877c1 0 1 0 3 1l-1 1h2v1c-1 1-2 1-3 2l-2-2h-3 0l4-3z" class="F"></path><path d="M399 866c3 1 5 3 7 4 0 1-1 2-2 3v1l1 1c-1 0 0 0-1 1h0l2 1c-2 1-4 1-5 1l-1 1h0c-1-1-1-1-2-1h-1l-2-1 1-1h4c-1-1-4-1-5-2l-1 1 1 1h-1l-1-1c-1-1-2-1-3-2l-1 1c-1 0-2 0-2-1 1-1 1-1 3-1l2-1v-1l3-2h1c1-1 2-1 3-2z" class="C"></path><path d="M395 868c0 1 1 2 1 3v1l1 1h-2c-1 0-1 0-1-1l-1 1 2 1h0l-1 1 1 1h-1l-1-1c-1-1-2-1-3-2l-1 1c-1 0-2 0-2-1 1-1 1-1 3-1l2-1v-1l3-2z" class="N"></path><path d="M399 866c3 1 5 3 7 4 0 1-1 2-2 3l-2-2-1 1h-2c-1-1-2-1-3-1 0-1-1-2-1-3h1c1-1 2-1 3-2z" class="q"></path><path d="M421 880c0-1 2-3 3-3h4l1 1h1c1 1 2 3 3 4h0 0v2c1 1 2 1 3 1h1v1c-1 1-1 2-2 3l2 1h-4l-1 1-1-1c-1-1-1-2-1-3h-2l-2 2h-1v-1s1 0 2-1h0-1c-2-1-4-2-5-4h-1c0-1 0-2 1-3z" class="F"></path><path d="M421 880c0-1 2-3 3-3h4l1 1c-1 1 0 2-2 3l1 1h-1c-1 1-1 0-1 1h1v1 3h-1c-2-1-4-2-5-4h-1c0-1 0-2 1-3z" class="L"></path><path d="M426 883c-1 0-1-1-2-1l1-1c0-2 0-2 2-3h0l-1 1 1 2 1 1h-1c-1 1-1 0-1 1z" class="C"></path><path d="M380 870v1-1c2-1 2-1 4-1v1c0 1 0 1-1 2l1 1h3c0 1 1 1 2 1l1-1c1 1 2 1 3 2-1 0-1 1-2 1h-1c-1 0-2 1-3 2l-1 1v1l-5 4h-1s-1 1-2 0c-1 0-2 1-3 1h0 0c1-1 0-1 1-2h-2v-3c1-1 0-1 1-2s0-1 1-2c0-2 2-4 4-6z" class="G"></path><path d="M384 873h3c0 1 1 1 2 1l-1 1c-1 0-1 0-2-1v2h0l-3 4v-1c-1 1-2 3-3 3l-1-1c-1 0 0-1-1 0h-1l-2-2 1-1 2 1 1-1c0-1-1-2-1-3v-1c2-1 4-1 6-1z" class="C"></path><path d="M405 890c1-2 2-4 3-5 1-2 2-2 4-2 3 0 4 1 6 3v4 1h2 0 4l-1 1 1 2-1 1-2-1 1-2h0-1-3l-1 1 1 1v1h-1v-1c-2 0-3 1-5 2h-4c-1 0-2 0-3-1l2-2h0c0-2-1-2-2-3z" class="J"></path><path d="M493 800l2-2h4c3-1 7-2 10-1h2c1 0 1 1 2 1 3 0 5 1 8 3 1 2 3 4 4 6v2 1c0 1-1 1-1 2-1 1 0 4 0 5v20 4h0l1 1 1 1c-1 1-1 1-1 2l1 1h4v1h-6v-2l-1-1v3s1 0 0 1c-1-2-1-4-1-6-1-1-1-2-1-3l-1-1c0 1 0 2-1 3-1 3 0 9 1 12v2c0 3 2 8 5 11 1 3 3 5 5 6 0 1 2 2 2 3l10 8h-1c-1 0-1 0-2-1v1h-2-6-9-13c-2 0-4 0-5-1h-4-17c-1 0-2 0-3-1s-2-2-3-2h-2c-1 0-1-1-2-1-2-1-3-1-5-2l2-1h1c2 0 2 0 3 1h2l-1-1v-1h4v1-1l1-1v-1h2c0-1-1-2-2-3h0l-3-2-2-1c-1 0-2 0-4-1l1-1h-1l-1 1c-1-1-2-1-3-1v-1c1-3 1-7 1-9v-1c2 0 2 0 3-1h0 1c2-2 3-1 6-1h5 1s1 0 1-1h-1c3-1 9 0 11-2l2-5c-1 1-2 1-3 1h-6c1-1 2-2 2-3h0l1-1v3h1c3-2 1-4 3-7v-1c1-2 0-6-1-8h-1 0l-2-2v-4c0-1-1-2-1-3 0-3 0-5-1-7l-1 1c0-3 1-5 2-7s2-3 3-5z" class="U"></path><path d="M504 873h17c2 0 4 1 6 0v1c-6 2-14 1-20 1h-2-1v-2z" class="N"></path><path d="M494 866h14 6 0c3 2 6 0 8 2-1 1-16 0-19 0h0c-2-1-7-1-9-2z" class="K"></path><path d="M503 873c-5-1-9 0-14-1-1 0-3 0-4-1h0 4c4 1 10 0 15 0h0 15c1 0 4 0 5 1h-7l-14 1z" class="B"></path><path d="M492 864h19 4 1 0 4l1 1v1h-7-6-14c-1 0 0 0-1-1h-1v-1z" class="E"></path><path d="M513 815l7-1h1c0 1 0 1-1 2-1 4 0 10 0 14h0c-2-3-1-9-1-12h0c-2 4-1 8-1 12v4l-1 1h-1l-1-1v-2c0-1 0-1-1-2h0c0-3 1-10-1-12h-1v-1h7l1-1v-1h-2l-1 1h-3l-1-1z" class="v"></path><path d="M507 876h10 11c-2 2-8 1-11 1-1 1-2 0-3 1h15v1c-2 1-5 0-7 0l-15 1v-1h-7c-1 1-3 0-5 0-3 0-5 1-7 0h-1c5 0 10-1 15-2h2c1-1 1-1 3-1z" class="V"></path><path d="M532 875l10 8h-1c-1 0-1 0-2-1v1h-2-6-9-13c-2 0-4 0-5-1h-4l1-1c4 0 8 0 12-1h7c3 0 8 1 11 0v1c1 1 2 1 3 0h0 1v-1l2 1v-1l-3-3c-1 0-1-1-2-2h0z" class="v"></path><path d="M520 880c3 0 8 1 11 0v1c-4 1-8 1-12 1l1-2z" class="H"></path><path d="M513 880h7l-1 2h-15-4l1-1c4 0 8 0 12-1z" class="B"></path><path d="M489 861c1 0 2-1 3-1 3-1 9 0 13 0v1h10l5 1v1 1c-2-1-3-1-4 0h-1-4-19-6v-1-1c1 0 2 0 3-1z" class="a"></path><path d="M489 861c1 1 1 0 2 1l1 1h3 6 1c4-1 8-1 12-1 0 1 1 1 1 2h-4-19-6v-1-1c1 0 2 0 3-1z" class="h"></path><path d="M482 862l1 1 3-1v1 1h6v1h1c1 1 0 1 1 1 2 1 7 1 9 2h-8c3 1 6 1 9 1 6 0 13-1 20 0l-1 1h-18l-1 1h0c-5 0-11 1-15 0h-4 0c1 1 3 1 4 1 5 1 9 0 14 1-4 1-8 0-11 0 1 2 4 0 6 1l6-1v2h1 2v1c-2 0-2 0-3 1h-2c-5 1-10 2-15 2h1c2 1 4 0 7 0 2 0 4 1 5 0h7v1h6c-4 1-8 1-12 1l-1 1h-17c-1 0-2 0-3-1s-2-2-3-2h-2c-1 0-1-1-2-1-2-1-3-1-5-2l2-1h1c2 0 2 0 3 1h2l-1-1v-1h4v1-1l1-1v-1h2c0-1-1-2-2-3h2 0 2l-1-3h0-2c-2 0-2 0-4-1 1 0 2-1 2 0 1 0 1 0 2-1h-1l2-2h0z" class="Q"></path><path d="M483 882c2-1 5-1 7-1 3-1 7 0 11 0l-1 1h-17z" class="F"></path><path d="M498 874l6-1v2h1 0-2l-1 1h-4c-5 0-9 1-14 1h-5l1-1h11l1-1c1 0 3-1 4 0h1l1-1z" class="V"></path><path d="M482 862l1 1 3-1v1 1h6v1h1c1 1 0 1 1 1 2 1 7 1 9 2h-8c-1-1-2-1-3-1h-1-3l1-1 1-1h-1l-2 1c-1 1-2 1-3 0h-1 0-2c-2 0-2 0-4-1 1 0 2-1 2 0 1 0 1 0 2-1h-1l2-2h0z" class="D"></path><path d="M498 849l2-4 1-1v-1c1 0 2-1 4-1h10 2c1 2 0 2 1 4s1 3 1 6h-2l1 1v1c-7 0-15 1-22 0v1h13c2 0 6-1 9 0 1 1 1 1 2 3v1 2-1h-3c-2 1-4 0-6 0h-6c-4 0-10-1-13 0-1 0-2 1-3 1-1 1-2 1-3 1l-3 1-1-1h0l-2 2h1c-1 1-1 1-2 1 0-1-1 0-2 0 2 1 2 1 4 1h2 0l1 3h-2 0-2 0l-3-2-2-1c-1 0-2 0-4-1l1-1h-1l-1 1c-1-1-2-1-3-1v-1c1-3 1-7 1-9v-1c2 0 2 0 3-1h0 1c2-2 3-1 6-1h5 1s1 0 1-1h-1c3-1 9 0 11-2 1 0 2 0 3 1z" class="P"></path><path d="M471 864h-1l2-1v1c1 1 4 1 5 1 2 1 2 1 4 1h2 0l1 3h-2 0-2 0l-3-2-2-1c-1 0-2 0-4-1l1-1h-1z" class="J"></path><path d="M471 852c1 1 1 1 2 1s2 1 3 1l1-1 1 1c0 1 0 0 1 1l1-1c0-1 1-1 2-1l1 1c-5 3-9 6-14 9v-1c-1-2 1-6 0-8h-1v-1c2 0 2 0 3-1z" class="Z"></path><path d="M495 848c1 0 2 0 3 1-1 0-1 1-1 2l-2 1-2 1h-1c-2 1-6 1-9 1l-1-1c-1 0-2 0-2 1l-1 1c-1-1-1 0-1-1l-1-1-1 1c-1 0-2-1-3-1s-1 0-2-1h0 1c2-2 3-1 6-1h5 1s1 0 1-1h-1c3-1 9 0 11-2z" class="O"></path><path d="M483 857l1-1c3 0 4 0 6 1h1l1 1v1c3 0 9-2 13 0h5 9 1v2-1h-3c-2 1-4 0-6 0h-6c-4 0-10-1-13 0-1 0-2 1-3 1-1 1-2 1-3 1l-3 1-1-1-1-1c-2 0-2 0-4-1v-1l2-1v1h0v1h1c1 0 2 0 3 1v1h3v-2h0c-1-2-2-1-4-2l1-1z" class="T"></path><path d="M483 857l1-1c3 0 4 0 6 1v2h0c-2-1-1 0-2 0-2 0-3-1-5-2z" class="C"></path><path d="M494 858c0-1 0 0-1-1h0c0-1 0-1 1-2v-1l1-1 1 1v1h13c2 0 6-1 9 0 1 1 1 1 2 3v1h-1-9-5c-4-2-10 0-13 0v-1h2z" class="K"></path><path d="M494 858c0-1 0 0-1-1h0c0-1 0-1 1-2v-1l1-1 1 1v1h0c3 1 8 0 9 1v1c-4 0-8 0-11 1z" class="I"></path><path d="M498 849l2-4 1-1v-1c1 0 2-1 4-1h10 2c1 2 0 2 1 4s1 3 1 6h-2l1 1h-16-5s-1-1-2-1l2-1c0-1 0-2 1-2z" class="e"></path><path d="M493 800l2-2h4c3-1 7-2 10-1h2c1 0 1 1 2 1 3 0 5 1 8 3 1 2 3 4 4 6v2 1c0 1-1 1-1 2-1 1 0 4 0 5v20 4h0l1 1 1 1c-1 1-1 1-1 2l1 1h4v1h-6v-2l-1-1v3s1 0 0 1c-1-2-1-4-1-6-1-1-1-2-1-3l-1-1v-8c0-4-1-10 0-14 1-1 1-1 1-2h-1l-7 1 1 1h3l1-1h2v1l-1 1h-7v1h1c2 2 1 9 1 12h0c1 1 1 1 1 2v2l1 1-1 1h2v2 2c-3 1-7 1-11 0l1 1c2 0 6 0 8 1h-10c-2 0-3 1-4 1v1l-1 1-2 4c-1-1-2-1-3-1l2-5c-1 1-2 1-3 1h-6c1-1 2-2 2-3h0l1-1v3h1c3-2 1-4 3-7v-1c1-2 0-6-1-8h-1 0l-2-2v-4c0-1-1-2-1-3 0-3 0-5-1-7l-1 1c0-3 1-5 2-7s2-3 3-5z" class="M"></path><path d="M501 823v-6h1 1 2v1l-1 1h1l1-2c2 0 2 1 4 0v6c-2 1-1 0-3 0h-2c-2 1-3 0-4 0z" class="m"></path><path d="M512 817v1h1c2 2 1 9 1 12h0c1 1 1 1 1 2v2l1 1-1 1h-3-3-1c-2-1-4-1-6-1-1 0-1 0-1-1l-1-4c1-2 0-5 1-7 1 0 2 1 4 0h2c2 0 1 1 3 0v11l1 1v-17l1-1z" class="g"></path><path d="M503 834v-1c0-1-1-2-1-2h-1l1-1h3v1h2v3h-4z" class="Z"></path><path d="M512 830h2 0c1 1 1 1 1 2v2l1 1-1 1h-3v-6z" class="O"></path><path d="M512 818h1c2 2 1 9 1 12h-2v-12z" class="S"></path><path d="M501 823v3l1-1 1-1v1 1c1 1 2 1 4 1-1 1-3 1-4 1v1h4l-1 1h-1-3l-1 1h1s1 1 1 2v1l-1 1c-1 0-1 0-1-1l-1-4c1-2 0-5 1-7zm12-24v-1c3 0 5 1 8 3 1 2 3 4 4 6v2 1c0 1-1 1-1 2-1 1 0 4 0 5v20 4h0l1 1 1 1c-1 1-1 1-1 2l1 1h4v1h-6v-2l-1-1v3s1 0 0 1c-1-2-1-4-1-6-1-1-1-2-1-3l-1-1v-8c0-4-1-10 0-14 1-1 1-1 1-2h-1l-7 1h0-5c-1 0-2 1-4 1h0c-1-1-2 0-3-1v-1h1 6c1 0 2 1 3 0h4v-1h-3l1-1c2 0 4 1 6 1h3v-1c-1-1-3 0-5 0-4-2-7-1-11-1h-5c4-2 12-1 16 0h4v-2c-2 1-5 1-7 0l2-1c2 0 4 1 6 1v-1c-2-4-6-6-9-9z" class="i"></path><path d="M524 837l-2-1c0-2-1-9 1-12h0l-1-3 1-1v2c2-4-1-11 1-14 1 0 0 0 1 1h0v1c0 1-1 1-1 2-1 1 0 4 0 5v20z" class="Z"></path><path d="M497 815h1c0 3 0 5 1 8v3l1 3v1l1 4c0 1 0 1 1 1 2 0 4 0 6 1h1 3 3 2v2 2c-3 1-7 1-11 0l1 1c2 0 6 0 8 1h-10c-2 0-3 1-4 1v1l-1 1-2 4c-1-1-2-1-3-1l2-5c-1 1-2 1-3 1h-6c1-1 2-2 2-3h0l1-1v3h1c3-2 1-4 3-7v-1c1-2 0-6-1-8h1c1-3 1-5 2-8v-4z" class="Y"></path><path d="M506 840h-4l-1-1 2-1c3 2 10 1 14 0v2c-3 1-7 1-11 0z" class="g"></path><path d="M497 815h1c0 3 0 5 1 8v3c0 2 1 11 0 13l-1-1v-8c0-1-1-2-1-3v-8-4z" class="i"></path><path d="M495 836v3h1c0-4 1-8 0-11h0c2 3 1 7 2 10 0 2 0 4-1 5s-2 1-3 1h-6c1-1 2-2 2-3h0l1-1v3h1c3-2 1-4 3-7z" class="Z"></path><path d="M493 800l2-2h4c3-1 7-2 10-1h2c1 0 1 1 2 1v1c3 3 7 5 9 9v1c-2 0-4-1-6-1-5 0-13-1-17 2-1 1-2 3-2 5v4c-1 3-1 5-2 8h-1-1 0l-2-2v-4c0-1-1-2-1-3 0-3 0-5-1-7l-1 1c0-3 1-5 2-7s2-3 3-5z" class="v"></path><path d="M493 800l2-2h4c3-1 7-2 10-1h2c1 0 1 1 2 1v1c-4-2-10-1-14 0-4 2-7 8-10 12l-1 1c0-3 1-5 2-7s2-3 3-5z" class="Y"></path><path d="M408 612v-27-57-162-47c0-21 0-41-5-62-5-25-17-47-34-65-28-30-65-43-105-44v-10h215l1 1c0 1-1 2-1 3h0v2c1 0 3 0 4 1l-1 6 1 7c-1 2 0 5-1 6v1l-1 3h0v4 5 1 4 5h-4 0 0c1 1 1 1 2 1l-2 2v1c1 0 3 1 3 2s0 1-1 2c-3 0-3 1-5 3h-1-6l1 1 6 2c1 2 1 2 0 3v1c0 2 1 2 2 3h0l1-1h2 1c-1 3 2 9 4 12 0 2 0 3 1 4l-1 1h0l-2 2h-2c0 1 0 1-1 2h0v2l1 1 1 2c1 1 1 2 1 4v9 2 8 9 4 5 2c0 1 0 3-1 4v2 1l-1 1v1 2l2 4c-1 1-2 3-3 4l-1 1h0v4h-2c-1 0-1 1-2 1v-1h-1v1l-3-1h-2-4-8-5-3-2l-2 2c-1 0-1-1-1-1-1 1-1 2-1 3-1 0-1-1-1-2l-1-1c-1 0-2 0-4-1v-1l-2 1c-1-1-1-2-2-3 0 2-1 5 0 6 2 4 0 9 1 13 2 2 4 3 7 3l1 1-1 6c-1 1-1 1-2 1h0l-2 1c0 1 0 2 1 3l-1 1h0l2 3 3 1-1 2v3 1 8 1 6c2 0 6 0 7-1h4v1c-2 0-1 0-2 1h0v1l-2 2-1-1c0 2-1 4 0 5-1 1-2 1-4 1l1 1h-14c0 1 0 2 1 3-1 0-2 1-3 1 1 1 2 2 2 3h0c1 1 2 2 2 4v1h-1c0 1 0 1 1 1v1l2 3v1h0c-1-1-1-1-2 0v2c0 3 3 7 5 9v1l1 2c2 0 3 2 4 3v2 7h-1-1c-1 0-2 1-3 3v2 1l1 1c1 1 1 2 2 3l-1 1h1v3 4c0 1 1 2 1 3 1 2 1 3 0 6h1v2 1 28 19l-1 12v-1c-1 0-1 1-2 0h-1l-1 1h1c0 1 1 2 2 4l-1 1v1h2c0 2 1 3 0 5v7c-1 2-1 3 0 5-1 2-1 2 0 5v1h-1c0-2-1-3-1-5v-8-2l-1-1-1-1c-2 1-1 0-2 1 0 1 0 1-1 1s-1 1-2 2h-1c0 1-1 3-1 4v3h-2-2 0l-1 1h-1c-1 1-1 2-2 2-1 3 0 6 0 8l-1 2c-1-1-2-1-4-2l1 3h1c2 1 2 1 3 3h0l-1 1c-2-1-4-3-7-4v11 5 33 7 28 3l1 1c-1 2-1 3-2 4l-1 1h-1s-1 1-2 1c1 1 2 1 3 1l2 1-7-1h0c1-1 1-1 2-1v-19z" class="s"></path><path d="M479 144l-1-1h-15v-1h10 6v2z" class="B"></path><path d="M408 612l1 10v-1c2 2 1 5 2 7l1-1v-1c0-1 0 0 1-2l1 1c-1 2-1 3-2 4l-1 1h-1s-1 1-2 1c1 1 2 1 3 1l2 1-7-1h0c1-1 1-1 2-1v-19z" class="H"></path><path d="M413 402c3 5 0 17 2 19 1 2 2 3 3 4 0 2-1 2 0 4h0l-1 5c0 2-1 3-2 4 0 1-1 3-1 4l-1-40z" class="G"></path><path d="M415 438c0-3-1-7 1-9h2l-1 5c0 2-1 3-2 4z" class="H"></path><path d="M414 321c-1-1 0-3 0-4l2 1c0-1 1-1 1-2l1 1c1 1 0 2 0 4h0 2c0-1 0-2 1-3v-3l1-1 1 1-1 2c1 1 1 1 2 3v-2c0-1 1-1 1-1v2h3v1l1 1c-2 2-2 2-4 3h-3v1l-2 1h-1l-3-1c0-1 0-1-1-1 0-1-1-1-1-2v-1z" class="C"></path><path d="M414 321v1c0 1 1 1 1 2 1 0 1 0 1 1l3 1h0l2 3s1 1 2 1c0 1-1 1-1 2 2 1 4 2 5 4l-1 1c-1-1-2-2-3-2l-1 1s-1 1-1 2l1 1h-1 0-1l-1-1c-1 0-2 0-2-1l-1-1c-1 0-1 0-2-1v-4h-1c0-3 0-6 1-10z" class="B"></path><path d="M416 325l3 1h0l-1 1-1 2-3-2v-1l2-1z" class="N"></path><path d="M419 338c-1-2-2-3-1-5l2 2v1h2s-1 1-1 2l1 1h-1 0-1l-1-1z" class="I"></path><path d="M420 335c0-2-1-3 0-4 1 0 1 0 2 1 2 1 4 2 5 4l-1 1c-1-1-2-2-3-2l-1 1h-2v-1z" class="Q"></path><path d="M362 142c5 0 10 1 16 1 11 0 24-1 36 1h-7c-1 3-2 3-5 4h-1v-1-1h-12c-3 0-6 0-10-1-2-1-6-1-9-1-3-1-5 0-8-2z" class="N"></path><path d="M428 319c-1-1-2-3-3-4 2-1 1 0 2 1h2c1 1 2 2 2 4l-1 1h2v-2c1-2 1-1 1-3 2 2 4 3 7 3l1 1-1 6c-1 1-1 1-2 1h0l-2 1c0 1 0 2 1 3l-1 1c-1-2-2-3-4-5v-1h0c-1 1-1 1-3 0h-1v1l1 1c-1 1 0 1-1 1l-2-2h-1c-1 1-1 2-1 2l-1 1c0-2-1-3-3-4l2-1v-1h3c2-1 2-1 4-3l-1-1v-1z" class="F"></path><path d="M425 327l1-1h1v-1c1-2 1-2 3-3v1l-1 3h-1v1l1 1c-1 1 0 1-1 1l-2-2h-1z" class="D"></path><path d="M432 326v-3h1 1c1 1 3 2 4 4l-2 1c0 1 0 2 1 3l-1 1c-1-2-2-3-4-5v-1h0z" class="E"></path><path d="M420 326c2 1 3 2 3 4l1-1s0-1 1-2h1l2 2c1 0 0 0 1-1l-1-1v-1h1c2 1 2 1 3 0h0v1c2 2 3 3 4 5h0l2 3 3 1-1 2s-1 0-2 1l-1 1 1 1-1 1c0-1 0-1-1-1h-1l-1-2c-1-1-2-2-4-2v3l1 1h-1-1c-1-2-2-3-2-5h0c-1-2-3-3-5-4 0-1 1-1 1-2-1 0-2-1-2-1l-2-3h0 1z" class="Q"></path><path d="M438 335l-2 2-1-1c0-2-1-3-2-4v-1l3 1 2 3z" class="E"></path><path d="M420 326c2 1 3 2 3 4l1-1s0-1 1-2h1l2 2c-1 1-1 2-2 3 1 1 3 2 4 4h0-3 0c-1-2-3-3-5-4 0-1 1-1 1-2-1 0-2-1-2-1l-2-3h0 1z" class="J"></path><path d="M413 331h1v4c1 1 1 1 2 1l1 1c0 1 1 1 2 1l1 1h1 0 1l-1-1c0-1 1-2 1-2l1-1c1 0 2 1 3 2l1-1h0c0 2 1 3 2 5h1 1l-1-1v-3c2 0 3 1 4 2l1 2h1c1 0 1 0 1 1l1-1-1-1 1-1c1-1 2-1 2-1v3 1 8 1 6c2 0 6 0 7-1h4v1c-2 0-1 0-2 1h0v1l-2 2-1-1c0 2-1 4 0 5-1 1-2 1-4 1l1 1h-14v-2c-2-1-5-4-7-5-3-1-4-5-6-8h0c-1-1-1-2-2-2l1-1c0-1 0-2-1-3l-1 3v-18z" class="t"></path><path d="M432 355h3c0 1 1 2 1 4l-2 1c-1-1-2-1-3-1 0-1 1-2 1-3v-1z" class="I"></path><path d="M428 350c2 1 3 1 4 3h0l1 1h-1v1 1l-1-1h0c-2-1-3-2-4-4l1-1z" class="f"></path><path d="M417 352c-1-1-2-5-2-6l1-1c1 2 1 2 2 2 1 1 1 1 1 2v1c0 1 1 2 1 3l2 2c-1 0-2-1-3-2l-1-1h-1z" class="c"></path><path d="M431 359h-1v-2-1h-1c-2 0-3-3-3-5v-1-2c1 0 1 0 2 1v1l-1 1c1 2 2 3 4 4h0l1 1c0 1-1 2-1 3z" class="X"></path><path d="M440 357c2 0 6 0 7-1h4v1c-2 0-1 0-2 1h0v1l-2 2-1-1c-2-1-2 0-3 1-1 0-1-1-1-1h-4c-1 2-1 2-1 4h-1v-2c0-1-1-2-2-2l2-1c1-1 3-1 4-2z" class="p"></path><path d="M413 331h1v4c1 1 1 1 2 1l1 1c0 1 1 1 2 1l1 1 1 3h-1c-1-1-2-2-3-4 1 2 2 5 2 7v1l-3-4v1l2 4c-1 0-1 0-2-2l-1 1c0 1 1 5 2 6l5 8c-3-1-4-5-6-8h0c-1-1-1-2-2-2l1-1c0-1 0-2-1-3l-1 3v-18z" class="a"></path><path d="M422 336l1-1c1 0 2 1 3 2l1-1h0c0 2 1 3 2 5h1 1l-1-1v-3c2 0 3 1 4 2l1 2h1c1 0 1 0 1 1l1-1-1-1 1-1c1-1 2-1 2-1v3 1l-2 2 1 1-1 1-1-1-1 2c0 1 0 1 1 2l1 1 1 1c-1 0-2-1-2-1-2 0-1 2-4 1v-1c0-2 0-3-1-4l-2 1v-1h-2c-1-1-1-1 0-2-1-2-1-3-3-4h0-3l-1 2c1 1 1 2 2 3h-1c-1 0-2 1-2 2l-1-1v-1c0-2-1-5-2-7 1 2 2 3 3 4h1l-1-3h1 0 1l-1-1c0-1 1-2 1-2z" class="h"></path><path d="M414 144c5-2 14-1 19 0 3 0 9 0 11 1-5 1-9 1-13 0-4 0-11 1-15-1 0 1-1 1-1 2h-1v-1c-3 0-5 0-7 2h0 0 4c2-1 4 0 6-1s7 0 9 0h1c1 0 1 1 2 1h2 1 1c1 0 0 0 1 1h3 1c1 0 2 0 3 1h1 2 3c-1 0-2 0-3-1h1 2c3 1 6 1 9 1l11-1c2 0 7-2 8-1s2 2 3 2l1 1s1 1 2 1h1l1 7-1 2c-3 0-9 0-11 1-1 0-2 1-2 2-2 0-2-1-3-2-2 0-2 0-3-1 2-2 6-1 8-3l1-1h1 1 1l-1-1c-2 1-6 0-8 0s-3 1-4 1h-2c-1 1-2 1-4 1h0-3c-2-1-6-2-8-2l-2 1-1-1h-5c-4 1-8 1-12 1-3 1-9 2-12 1v-1h-1c-1 1-1 1-2 1-3 0-5 0-8-1h0-2c-2 1-6 0-8-2h1v-1c-2 0-3 0-5-1-3-1-7-2-9-4h1l-1-1h0l-8-2h0l-1-1c3 0 7 0 9 1 4 1 7 1 10 1h12v1 1h1c3-1 4-1 5-4h7z" class="C"></path><path d="M448 151h1c3 0 7 0 10 1-3 0-7 0-9 2-2 0-5-1-7-1h0l5-2z" class="B"></path><path d="M388 152v-1h0c2-2 4-1 7-1v-2h2l2 1h4c3-1 6-1 9-1h3v1c0 1-1 1-1 2h1l-1 2h-1v-1c0 1-1 2-2 2h-1c-1 0-1 0-1 1h0c1 1 1 1 1 2-3 0-5 0-8-1h0-2c-2 1-6 0-8-2h1v-1c-2 0-3 0-5-1z" class="H"></path><path d="M402 156l1-2c2 0 2-1 2-2 3 0 5-1 8 0 0 1-1 2-2 2h-1c-1 0-1 0-1 1h0c1 1 1 1 1 2-3 0-5 0-8-1z" class="G"></path><path d="M415 151l3-1h12c1 0 1 0 1-1h-3c-3-1-6 0-9-1h-1l-1-1c2 0 7-1 9 0 2 0 3 1 5 1 4 1 8 1 13 2h3l1 1-5 2c-2 0-5 1-7 2h-1-1c-3 1-6 0-9 0v1c-3 1-9 2-12 1v-1h-1c-1 1-1 1-2 1 0-1 0-1-1-2h0c0-1 0-1 1-1h1c1 0 2-1 2-2v1h1l1-2z" class="N"></path><defs><linearGradient id="E" x1="423.046" y1="251.195" x2="394.031" y2="271.588" xlink:href="#B"><stop offset="0" stop-color="#7a796d"></stop><stop offset="1" stop-color="#999588"></stop></linearGradient></defs><path fill="url(#E)" d="M409 217c2 0 5 1 7 0v-1-1-2h2c-1 1-1 2 0 3-2 4-4 7-5 11l-2 6-1 7 1 1c-1 3 0 5 1 8l5 9v1c1 1 2 2 3 2s2 1 2 1l1 1c1 1 1 1 3 1l4 4c1 1 2 2 4 3l1 1-2 2c0 1 0 2-1 3 1 1 1 2 2 3l-1 1-1-1h-2v1h1s1 1 2 1v2l-1 1c-8 3-14 11-18 18-1-9-1-18-2-27-2-11-4-21-8-32l-4-14c-2-2-3-5-5-8l2-1-1-2 1-1 1-1 5 1 4 1 2-2h0z"></path><path d="M417 259c1 1 2 2 3 2s2 1 2 1l1 1c1 1 1 1 3 1l4 4c1 1 2 2 4 3l1 1-2 2c0 1 0 2-1 3-4-3-9-7-12-11h0c-1-1-2-3-3-4v-3z" class="c"></path><path d="M434 271h-1c-1 2-1 2-2 3l-3-1c-2-2-3-3-4-5h1l-1-1 1-1 2 2v2l1 1 1-1 1 1 1-1-1-1v-1c1 1 2 2 4 3zm-17-12c1 1 2 2 3 2s2 1 2 1l1 1v4 1c-1 0-2-2-3-2h0c-1-1-2-3-3-4v-3z" class="K"></path><defs><linearGradient id="F" x1="401.58" y1="228.148" x2="413.054" y2="215.313" xlink:href="#B"><stop offset="0" stop-color="#989588"></stop><stop offset="1" stop-color="#b7b2a2"></stop></linearGradient></defs><path fill="url(#F)" d="M409 217c2 0 5 1 7 0v-1-1-2h2c-1 1-1 2 0 3-2 4-4 7-5 11l-2 6-1 7v-4-1l-2-1h1c0-2-3-3-4-4h1c1 0 2 2 3 2-1-3-3-4-5-5-2 0-3-1-5 0 0 1 1 1 1 3h0c-2-2-3-5-5-8l2-1-1-2 1-1 1-1 5 1 4 1 2-2h0z"></path><path d="M397 218l2 1c3 0 5 1 8 2 1 0 2 1 3 1l-2 1v2c-3 0-7-1-9-3h-2v-1l-1-2 1-1z" class="c"></path><path d="M397 218l2 1v1h1c1 0 3 1 4 2-2 1-5-1-7-1l-1-2 1-1z" class="h"></path><defs><linearGradient id="G" x1="432.559" y1="360.226" x2="397.144" y2="407.672" xlink:href="#B"><stop offset="0" stop-color="#9a9989"></stop><stop offset="1" stop-color="#dcd2c1"></stop></linearGradient></defs><path fill="url(#G)" d="M413 349l1-3c1 1 1 2 1 3l-1 1c1 0 1 1 2 2h0c2 3 3 7 6 8 2 1 5 4 7 5v2c0 1 0 2 1 3-1 0-2 1-3 1 1 1 2 2 2 3h0c1 1 2 2 2 4v1h-1c0 1 0 1 1 1v1l2 3v1h0c-1-1-1-1-2 0v2c0 3 3 7 5 9v1l1 2c2 0 3 2 4 3v2 7h-1-1c-1 0-2 1-3 3v2 1l1 1c1 1 1 2 2 3l-1 1h1v3-1l-2-2h-1l1-2-2-1s-1 0-2 1l-1-1-1 1c-1-1-3-2-4-3v-1-2c-1-2-1-2-3-3l-1 1-2 1v1c0 1 0 1-1 2 1 1 1 2 1 3 1 2 0 4-1 6h0v1l-2 3c-1-2 0-2 0-4-1-1-2-2-3-4-2-2 1-14-2-19-1-3 0-9 0-12v-41z"></path><path d="M420 403c1 1 1 3 1 4l-1 1-1-1c0-2 0-3 1-4z" class="L"></path><path d="M418 422c-1-1-1-1-1-2 1-2 1-4 1-5h1l1 1c1 1 1 2 1 3 1 2 0 4-1 6h0v1c-1-2 0-2-2-4z" class="G"></path><path d="M418 422l2-2-1-1v-1h1c0 1 0 1 1 1 1 2 0 4-1 6h0v1c-1-2 0-2-2-4z" class="N"></path><path d="M416 352c2 3 3 7 6 8 2 1 5 4 7 5v2c0 1 0 2 1 3-1 0-2 1-3 1 0-2-1-3-1-4h-1v1c-2 0-2-1-3-2l-4-4c1-1 1-2 1-3-2-2-3-4-3-7z" class="X"></path><path d="M419 359c2 2 4 6 7 8h-1v1c-2 0-2-1-3-2l-4-4c1-1 1-2 1-3z" class="a"></path><path d="M424 373c-2-2-4-4-5-6-1-1-2-2-2-3l1 1c2 0-1-1 1 0l1 1h2c1 1 1 2 3 2v-1h1c0 1 1 2 1 4 1 1 2 2 2 3h0c1 1 2 2 2 4v1h-1c0 1 0 1 1 1v1h-1l-1-1c-1-3-3-5-5-7z" class="D"></path><path d="M418 365c2 0-1-1 1 0l1 1h2c1 1 1 2 3 2v-1h1c0 1 1 2 1 4 1 1 2 2 2 3h-2c-3-1-8-7-9-9z" class="Q"></path><path d="M424 373c2 2 4 4 5 7l1 1h1l2 3v1h0c-1-1-1-1-2 0v2c0 3 3 7 5 9v1l1 2c2 0 3 2 4 3v2 7h-1-1c-1 0-2 1-3 3v2 1l1 1c1 1 1 2 2 3l-1 1h1v3-1l-2-2h-1l1-2-2-1s-1 0-2 1l-1-1-1 1c-1-1-3-2-4-3v-1-2c-1-2-1-2-3-3l-1-1v1-3c-2-3 0-6-2-8v-12-2l1-1c0-2 0-3 1-5v-2-3l1-1c-1-1-1 0 0-1z" class="V"></path><path d="M421 388h1 0c1 2 3 4 2 6l-1 1c0 1 0 1 1 2l-3 3v-12z" class="N"></path><path d="M428 402l3 2c2 1 1 3 4 3 1 0 1 1 1 1 1 1 1 1 2 1l1 2c-1 0-2 1-3 3v2 1l1 1c1 1 1 2 2 3l-1 1h1v3-1l-2-2h-1l1-2-2-1s-1 0-2 1l-1-1c-1-1-1-2-1-2-2-3-3-6-2-9v-1h1l-1-1c-1-2-1-2-1-4z" class="J"></path><defs><linearGradient id="H" x1="442.633" y1="397.686" x2="420.26" y2="393.963" xlink:href="#B"><stop offset="0" stop-color="#9a978d"></stop><stop offset="1" stop-color="#b6b09d"></stop></linearGradient></defs><path fill="url(#H)" d="M424 373c2 2 4 4 5 7l1 1h1l2 3v1h0c-1-1-1-1-2 0v2c0 3 3 7 5 9v1l1 2c2 0 3 2 4 3v2 7h-1-1l-1-2c-1 0-1 0-2-1 0 0 0-1-1-1-3 0-2-2-4-3l-3-2c0-6-2-11-4-16h-1l-1-1c0-2 0-3 1-5v-2-3l1-1c-1-1-1 0 0-1z"></path><path d="M424 373c2 2 4 4 5 7l1 1h1l2 3v1h0c-1-1-1-1-2 0v2c0 3 3 7 5 9v1l1 2h-2l-3-6c-1-2-2-3-3-6 0-1-1-2-1-3l-2 1c-1-2-2-3-3-5v-2-3l1-1c-1-1-1 0 0-1z" class="D"></path><path d="M424 373c2 2 4 4 5 7l1 1h1l2 3v1h0c-1-1-1-1-2 0l-1-2h-2s-1 0-1-1l-2-2v-4c0-1-1-1-1-2-1-1-1 0 0-1z" class="Q"></path><path d="M342 164h0c-4-2-9-5-13-7-2 0-3-1-4-2-1 0-2 0-3-1h-2c0-1-1-1-2-1h0c-1-1-3-1-4-2-1 0-2 0-3-1l-8-2c-1 0-1 0-2-1h-1c-2 0-3 0-5-1l-6-1-5-1h-3 0c-1 0-1 0-2-1 1-1 1 0 2 0l1-1 1-1c1 0 3 1 5 1 1 1 4 0 5 0h12c1-1 3 0 4 0h9 2c14 1 28-1 42 0 3 2 5 1 8 2l1 1h0l8 2h0l1 1h-1c2 2 6 3 9 4 2 1 3 1 5 1v1h-1c2 2 6 3 8 2h2 0c3 1 5 1 8 1 1 0 1 0 2-1h1v1c-2 0-5 1-7 2 0 1 1 1 2 2 2 2 6 2 9 4v1h-3c2 3 5 3 8 4l-1 1c-1 0-3-1-4-1h-1-1c-2 0-3-1-4-1v-1c-1-1-1-2-2-2h-2c-1 0-2-1-2 0-3 1-7 0-9 0h-1c-2-1-3 0-4-2-1 0-2 0-3-1s-2-1-3-1c-1 2-1 3-3 3-1 1-2 1-3 1-3 2-7 2-9 1h-1v-1h3v-1h-3c-1 0-1 1-1 1-1 0-3-1-3-1h0v1h2c2 1 3 3 4 3 1 1 2 1 3 2s2 1 4 2c-2 0-4 0-5-1s-2-1-3-1l-1-1h-2l-1-1c-2-1-4-1-6-1l-1 2 1 1-1 1-2-1v1l1 2h-1-1c0-1-1-1-2-1h0l-12-9z" class="B"></path><path d="M311 145c2-1 6-1 8-1 2 1 4 1 7 1h2c5 3 11 1 16 4h0c-1 1-3 0-5 0-3 0-5 0-8-1-3 0-5-1-8-2l-12-1z" class="H"></path><path d="M342 164c3 1 4 1 6 3 1 0 3 2 4 2l1-1 2-1h1v1h1c1-1 2 0 3 0l-1 2 1 1-1 1-2-1v1l1 2h-1-1c0-1-1-1-2-1h0l-12-9z" class="N"></path><path d="M333 154c2 0 8 2 10 3l8 4h1v1h-2c-3 0-6 0-8-2-2-1-5-1-6-3h0v-1h1l-1-1c-2 0-2 0-3-1z" class="H"></path><path d="M365 165c-2 0-2 0-3-1h3l-1-2 1-1c5-3 14 0 20 1-1 2-1 3-3 3-1 1-2 1-3 1-3 2-7 2-9 1h-1v-1h3v-1h-3c-1 0-1 1-1 1-1 0-3-1-3-1h0z" class="F"></path><path d="M424 411c2 1 2 1 3 3v2 1c1 1 3 2 4 3l1-1 1 1c1-1 2-1 2-1l2 1-1 2h1l2 2v1 4c0 1 1 2 1 3 1 2 1 3 0 6h1v2 1 28 19l-1 12v-1c-1 0-1 1-2 0h-1l-1 1h1c0 1 1 2 2 4l-1 1v1h2c0 2 1 3 0 5v7c-1 2-1 3 0 5-1 2-1 2 0 5v1h-1c0-2-1-3-1-5v-8-2l-1-1-1-1c-2 1-1 0-2 1 0 1 0 1-1 1s-1 1-2 2h-1c0 1-1 3-1 4v3h-2-2 0l-1 1h-1c-1 1-1 2-2 2-1 3 0 6 0 8l-1 2c-1-1-2-1-4-2l1 3h1c2 1 2 1 3 3h0l-1 1c-2-1-4-3-7-4v-36-6-12-29c-1-3 0-9 1-12 0-1 1-3 1-4 1-1 2-2 2-4l1-5h0l2-3v-1h0c1-2 2-4 1-6 0-1 0-2-1-3 1-1 1-1 1-2v-1l2-1 1-1z" class="B"></path><path d="M429 436l2 2 2 3h-3v1c-1-1-2-2-2-4-1-1 1-1 1-2z" class="C"></path><path d="M430 442c1 2 0 2 1 3 0 1 1 3 2 4h0v1l-3-3-1 1 1 2c-1 0-1 0-1 1l-2-2c0-2 0-2-1-3l-1-2v-1c2 1 3 2 5 3v-1l-1-1 1-2z" class="W"></path><path d="M429 451c0-1 0-1 1-1l-1-2 1-1 3 3v-1c1 2 2 2 3 4 0 1 0 2 1 3h-1 0l-2-1h-1l-2-1 1 3-1 1h0c0 2 1 3 2 5-1 0-1 0-2 1l-1-1h1v-1l-2-4c-1 0-1-1-1-1-1-2-2-4-2-6l2 1 1-1z" class="H"></path><path d="M431 454l-1-1-1-1 1-1c1 0 1 0 1 1l2 2h1l2 2-2-1h-1l-2-1z" class="F"></path><path d="M424 411c2 1 2 1 3 3v2 1c1 1 3 2 4 3v1c-1 1-2 1-3 2v1l-3 2-3 3-1 1h0c1-2 1-3 2-4l-1-1h-2 0c1-2 2-4 1-6 0-1 0-2-1-3 1-1 1-1 1-2v-1l2-1 1-1z" class="W"></path><path d="M424 411c2 1 2 1 3 3v2 1c1 1 3 2 4 3v1c-1 1-2 1-3 2v1l-3 2v-1c1 0 1-1 2-2v-1c-1-2-2-2-2-4 0-1-1-1-2-1 1-2 0-3 0-5l1-1z" class="F"></path><path d="M413 454c1 1 1 3 1 5 1 3 2 9 6 10h0c-1-2-1-2-1-3l-1-2h1s1 0 1 1h0c1 3 3 5 4 8h-1 0-3v1c1 1 2 1 3 3 0 1 1 2 2 3v-1l1 1v2h-1l-1-2h-1c-1-1-2-1-3-2v-1l-3-2h-3v5l-1 3v-29z" class="C"></path><path d="M414 474l1-2 1-1 1 4h-3v-1z" class="w"></path><path d="M414 474v-1-7c1 1 2 2 2 3v2l-1 1-1 2z" class="q"></path><path d="M432 419l1 1c1-1 2-1 2-1l2 1-1 2h1l2 2v1 4c0 1 1 2 1 3 1 2 1 3 0 6h1v2 1 28l-2 1v4 2h0c-1-1-2-2-1-3 0-2 0-1-1-3v-1l-1-2-1-1-2-3c-1-2-2-3-2-5h0l1-1-1-3 2 1h1l2 1h0 1c-1-1-1-2-1-3-1-2-2-2-3-4h0c-1-1-2-3-2-4-1-1 0-1-1-3h0v-1h3l-2-3-2-2c1-1 1-2 2-3 0-1-1-2-2-3-1-2 0-4-1-6h0v-1c1-1 2-1 3-2v-1l1-1z" class="V"></path><path d="M431 433l2 2 1 1-3 2-2-2c1-1 1-2 2-3z" class="F"></path><path d="M433 441l1 1v1l-1 1c1 1 1 1 1 3 0 0-1 1-1 2-1-1-2-3-2-4-1-1 0-1-1-3h0v-1h3z" class="N"></path><path d="M437 445c1-1 1-2 2-2 1-1 2-1 2-2v28l-2 1v4 2h0c-1-1-2-2-1-3 0-2 0-1-1-3v-1l-1-2-1-1 1-2c0-1-1-2-1-4h0c1 0 2 1 3 2 3-2 0-3 0-5v-2c-1-4 1-7-1-10z" class="a"></path><path d="M438 455c1 1 2 2 2 4v1h0c0 1 0 3-1 4h-2v1l2 2-1 1-1 1-1-2-1-1 1-2c0-1-1-2-1-4h0c1 0 2 1 3 2 3-2 0-3 0-5v-2z" class="E"></path><path d="M432 419l1 1c1-1 2-1 2-1l2 1-1 2h1l2 2v1 4c0 1 1 2 1 3 1 2 1 3 0 6h1v2 1c0 1-1 1-2 2-1 0-1 1-2 2v-1c-1-1-1-4-1-5l-2-3-1-1-2-2c0-1-1-2-2-3-1-2 0-4-1-6h0v-1c1-1 2-1 3-2v-1l1-1z" class="V"></path><path d="M428 424l3-2v1 3c0 1 1 3 1 4h0-1c-1-1 0-1-1-1h0l-1 1c-1-2 0-4-1-6z" class="H"></path><path d="M432 419l1 1c1-1 2-1 2-1l2 1-1 2h1l2 2v1 4c0 1 1 2 1 3 1 2 1 3 0 6h1v2 1c0 1-1 1-2 2-1 0-1 1-2 2v-1c-1-1-1-4-1-5l-2-3-1-1 1-1c1 1 1 2 3 2 0-1 0-2 1-3v-2c-1-1-1-2-2-3l1-1c1-2-1-3-2-4s-2-1-4-2v-1l1-1z" class="D"></path><path d="M439 429c0 1 1 2 1 3 1 2 1 3 0 6h1v2 1c0 1-1 1-2 2-1 0-1 1-2 2v-1c-1-1-1-4-1-5 1-1 1-2 2-1h1v-9z" class="J"></path><path d="M427 464v-2-1l3 2 1 1c1-1 1-1 2-1l2 3 1 1 1 2v1c1 2 1 1 1 3-1 1 0 2 1 3h0v-2-4l2-1v19l-1 12v-1c-1 0-1 1-2 0h-1l-1 1h1c0 1 1 2 2 4l-1 1v1h2c0 2 1 3 0 5v7c-1 2-1 3 0 5-1 2-1 2 0 5v1h-1c0-2-1-3-1-5v-8-2l-1-1-1-1c-2 1-1 0-2 1 0 1 0 1-1 1s-1 1-2 2h-1c-1-1-1-2-2-3v-1l2-1 2-2c-1-1 0-2 1-4l-2-2v-2h0c-3-2-6-5-6-8v-1h2c0-1-1-2-1-2l-3-3 3-2h-1-1-3v-3c-1 0-1 0-2-1v-3h1c1 1 2 1 3 2h1l1 2h1v-2l-1-1v-2c0-1 0-2 1-3 1 0 1 0 2 1v-1c-1-1-2-1-2-3 0-1-1-2-1-3l1-2 1 1 1-1c0-1-1-1-1-2z" class="N"></path><path d="M427 464c2 2 3 4 3 6h-3v2l2 2-1 1v-1c-1-1-2-1-2-3 0-1-1-2-1-3l1-2 1 1 1-1c0-1-1-1-1-2z" class="W"></path><path d="M432 472h0c-1-1-2-2-2-3 1-1 1-2 1-2v-1l1-1 3 3 1-1 1 2v1c1 2 1 1 1 3-1 1 0 2 1 3h-4 0l-1-1-2-3z" class="L"></path><path d="M432 472h2c1 0 1 1 2 2h1 0l-2-3v-1h2 0c1 2 1 1 1 3-1 1 0 2 1 3h-4 0l-1-1-2-3z" class="D"></path><path d="M434 475l1 1h0c1 2 2 3 2 5l2 1v1c0 2 0 3 1 5l-1 1-2-2c-1-2-2-4-4-5h0-1c0 4 0 6 2 9l-1-1h0c-2-1-4-5-5-7l1-1v-3-1h1v1c2 0 3 2 4 3l-3-6 1-1 1 1 1-1z" class="V"></path><path d="M434 475l1 1h0c1 2 2 3 2 5-2-1-3-3-4-5l1-1z" class="L"></path><path d="M439 476v-2-4l2-1v19l-1 12v-1c-1-1-1-2-2-3v-1c0-1 0-2-1-3h0 2l-2-2v-1l-1-1 1-1 2 2 1-1c-1-2-1-3-1-5v-1l-2-1c0-2-1-3-2-5h4 0z" class="c"></path><path d="M439 476h0v6l-2-1c0-2-1-3-2-5h4zm-6 14h0l1 1c-2-3-2-5-2-9h1 0c2 1 3 3 4 5l-1 1 1 1v1l2 2h-2 0c1 1 1 2 1 3v1c1 1 1 2 2 3-1 0-1 1-2 0h-1l-1 1h1c0 1 1 2 2 4l-1 1v1h2c0 2 1 3 0 5v7c-1 2-1 3 0 5-1 2-1 2 0 5v1h-1c0-2-1-3-1-5v-8-2l-1-1-1-1c-2 1-1 0-2 1 0 1 0 1-1 1s-1 1-2 2h-1c-1-1-1-2-2-3v-1l2-1 2-2c-1-1 0-2 1-4l-2-2v-2h0c-3-2-6-5-6-8v-1h2c0-1-1-2-1-2l-3-3 3-2c1 0 1 1 2 0v-2c1 2 3 6 5 7z" class="J"></path><path d="M433 505c1 2 2 4 4 5h-2l-1 1c-1 0-1 0-2-1l-2 1 2-2c-1-1 0-2 1-4z" class="E"></path><path d="M431 501a30.44 30.44 0 0 1 8 8v1h-2c-2-1-3-3-4-5l-2-2v-2z" class="G"></path><path d="M426 485c1 0 1 1 2 0v-2c1 2 3 6 5 7l3 4v1 1l-1-1c-1-2-3-3-4-5l-1 1c0 2 0 3 1 5 1 1 2 1 2 2h0-1c-1 1-1 1-1 3-3-2-6-5-6-8v-1h2c0-1-1-2-1-2l-3-3 3-2z" class="L"></path><path d="M417 475l3 2v1h-1v3c1 1 1 1 2 1v3h3 1 1l-3 2 3 3s1 1 1 2h-2v1c0 3 3 6 6 8h0v2l2 2c-1 2-2 3-1 4l-2 2-2 1v1c1 1 1 2 2 3 0 1-1 3-1 4v3h-2-2 0l-1 1h-1c-1 1-1 2-2 2-1 3 0 6 0 8l-1 2c-1-1-2-1-4-2l1 3h1c2 1 2 1 3 3h0l-1 1c-2-1-4-3-7-4v-36-6-12l1-3v-5h3z" class="V"></path><path d="M425 510h-3 0c1-2 1-2 3-2 1 1 1 1 1 2h-1z" class="D"></path><path d="M426 510h2l1 1-1 1v1 1h-2l-2-2h0l1-2h1z" class="L"></path><path d="M424 499h2c1 0 2 1 3 3-3 1-1 2-2 4-2-1-3-3-5-5h2v-2z" class="G"></path><path d="M424 512h0l2 2h2v-1c1 1 1 2 2 3 0 1-1 3-1 4v3h-2-2 0l-1 1h-1c-1 1-1 2-2 2-1 3 0 6 0 8l-1 2c-1-1-2-1-4-2 0-1 1-2 0-4h-1c0-1 1-1 1-1v-1-5h0 2c0-2 1-2 2-3 0-1 0-1 1-2l1-1c0-1 1-2 1-4h0l1-1z" class="J"></path><path d="M424 512l2 2h2v-1c1 1 1 2 2 3h-2c-1 0-2 1-3 1-1-1-1-2-1-2v-3z" class="D"></path><path d="M417 475l3 2v1h-1v3c1 1 1 1 2 1v3h3 1 1l-3 2 3 3s1 1 1 2h-2v1c0 3 3 6 6 8h0v2l-2-1c-1-2-2-3-3-3h-2v2h-2l-3-2h-2c-1 2 1 4 2 5l1 2-5-2c-1-1-1-2-1-3h-1v-6-12l1-3v-5h3z" class="W"></path><path d="M421 485l1 2h-1l-1-1c0-1-2-4-3-4s-2 0-3-1c0-1 0-3 1-4h2l2 1v3c1 1 1 1 2 1v3z" class="w"></path><path d="M424 499c-1-1-3-2-3-4h1c1 0 1 0 2-1l-2-4 1-1c1 1 1 1 3 1 0 0 1 1 1 2h-2v1c0 3 3 6 6 8h0v2l-2-1c-1-2-2-3-3-3h-2z" class="H"></path><path d="M413 483l1-3v4c0 3-1 9 1 11l3 2 1 2h-2c-1 2 1 4 2 5l1 2-5-2c-1-1-1-2-1-3h-1v-6-12z" class="N"></path><path d="M425 156c4 0 8 0 12-1h5l1 1 2-1c2 0 6 1 8 2h3 0c2 0 3 0 4-1h2c1 0 2-1 4-1s6 1 8 0l1 1h-1-1-1l-1 1c-2 2-6 1-8 3 1 1 1 1 3 1 1 1 1 2 3 2 0-1 1-2 2-2 2-1 8-1 11-1l1-2c-1 2 0 5-1 6v1l-1 3h0v4 5 1 4 5h-4 0 0c1 1 1 1 2 1l-2 2v1c1 0 3 1 3 2s0 1-1 2c-3 0-3 1-5 3h-1-6l1 1c-6-1-12-1-18-1l-6 1-10 4c-2 1-3 2-5 3l-2-2-2 2c-2 0-5 1-7 2 1 1 2 1 3 1l-1 2-1 1-1 1h-2v2 1 1c-2 1-5 0-7 0h0l-2 2-4-1-5-1-1 1-1 1 1 2-2 1c-3-8-9-16-14-23l-8-10-7-6s-2-2-2-3l-10-7c1 0 2 0 2 1h1 1l-1-2v-1l2 1 1-1-1-1 1-2c2 0 4 0 6 1l1 1h2l1 1c1 0 2 0 3 1s3 1 5 1c-2-1-3-1-4-2s-2-1-3-2c-1 0-2-2-4-3h-2v-1h0s2 1 3 1c0 0 0-1 1-1h3v1h-3v1h1c2 1 6 1 9-1 1 0 2 0 3-1 2 0 2-1 3-3 1 0 2 0 3 1s2 1 3 1c1 2 2 1 4 2h1c2 0 6 1 9 0 0-1 1 0 2 0h2c1 0 1 1 2 2v1c1 0 2 1 4 1h1 1c1 0 3 1 4 1l1-1c-3-1-6-1-8-4h3v-1c-3-2-7-2-9-4-1-1-2-1-2-2 2-1 5-2 7-2 3 1 9 0 12-1z" class="H"></path><path d="M391 180c2-2 5-1 8-1-2 1-3 1-4 2-2 0-3 0-4-1z" class="V"></path><path d="M396 167c2 0 6 0 8 1h3v1h-3v1l-3-1c-1 1-2 1-3 1h-1c0-1 0-2-1-3z" class="F"></path><path d="M445 174l-1 1h-2c0-1 0-1-1-1-1-2-2-2-3-2s-1 0-2-1v-1h2l9 3v1h-2z" class="C"></path><path d="M397 170h1c1 0 2 0 3-1l3 1c-3 2-5 1-8 2l-7 1-1-1c3-1 6-2 9-2z" class="W"></path><path d="M387 175h1c3 0 6-1 8 1 2 1 4 1 4 3h-1c-3 0-6-1-8 1l-7-3c1-1 1-2 3-2z" class="D"></path><path d="M366 170c2 0 6 2 8 4h5c2 1 6 0 8 0v1c-2 0-2 1-3 2h-3 0c-1 1-2 1-2 2-1 0-2 1-3 1v-1c-1-3-5-4-8-6 0-1 0 0-1 0-1-1-2-1-3-1l1-1 1-1z" class="F"></path><path d="M422 165v-1h10l1-1c2 0 3 0 5 1l2 2h0-1c-3 0-4 0-6 1s-5 3-6 3c-2-1-4-1-6-1h-1l-1-1h-1l-1-1h1 2 2l1-1h-1v-1z" class="G"></path><path d="M371 183l2-2h5c3 0 4 0 6 1h2c2 0 4 0 5 1l1 2 1 1c0 1-1 1-1 1-2 0-4 0-6 1h0l-2 1c-2-1-3-2-5-4h-2l-1-2-2 1v1h-1v-1-1h-2z" class="E"></path><path d="M386 182c2 0 4 0 5 1l1 2c-1 0-2 1-3 1v-1l-3-3z" class="B"></path><path d="M371 183l2-2h5v1c0 1 0 0 1 1h-1-5-2z" class="G"></path><path d="M396 172c2 0 5 0 7 1 2 0 4 0 6 1h2c1 0 2 0 3 1v1c-1 1-2 1-4 1-2 1-4 1-7 0-1 0-2 1-3 2 0-2-2-2-4-3-2-2-5-1-8-1h-1v-1l2-1 7-1z" class="B"></path><path d="M388 172c-7 1-9 0-15-2 2-2 7-3 10-3h1c4-1 8-1 12 0 1 1 1 2 1 3-3 0-6 1-9 2z" class="V"></path><path d="M360 171h0l1-1h5l-1 1-1 1c1 0 2 0 3 1 1 0 1-1 1 0 3 2 7 3 8 6v1h-2c-2 0-4-1-6-1 1 1 1 2 2 2l1 2h0 2v1 1h1v-1c1 2 3 3 4 5l-2 2c-1-1-2-2-3-2l-7-6s-2-2-2-3l-10-7c1 0 2 0 2 1h1 1l-1-2v-1l2 1 1-1z" class="D"></path><path d="M360 172l4 1 1 1c-2 1-2 1-3 1-2-1-2-1-2-3z" class="J"></path><path d="M360 171h0l1-1h5l-1 1-1 1c1 0 2 0 3 1h-3l-4-1h-1l1-1z" class="B"></path><path d="M364 180c0-1-2-2-2-3l3 2 2-1c1 1 1 0 1 1 1 1 1 2 2 2l1 2h0 2v1 1h1v-1c1 2 3 3 4 5l-2 2c-1-1-2-2-3-2l-7-6s-2-2-2-3z" class="J"></path><path d="M364 180c0-1-2-2-2-3l3 2 2-1c1 1 1 0 1 1 1 1 1 2 2 2l1 2c0 1 2 3 2 4v1c-2-2-4-4-6-5h-1s-2-2-2-3z" class="B"></path><path d="M425 156c4 0 8 0 12-1h5l1 1 2-1c2 0 6 1 8 2v1l-3 1v1l2 1c-2 1 0 0-2 0v1c-3 1-8 2-12 2-2-1-3-1-5-1l-1 1h-10v1h-5c-3-2-7-2-9-4-1-1-2-1-2-2 2-1 5-2 7-2 3 1 9 0 12-1z" class="V"></path><path d="M438 170l1-1s1 0 2-1h-1c1-2 1-2 2-3h1c3-1 5-1 8-1 1-1 2-1 3-1h3c-2 1-3 1-4 2v1c2-1 5 0 7-1h1c1 0 3 0 4 1h1l1 1h2v1h0l-1 1h4c2 0 3 0 5-2-2-1-2-1-4-1h0 3c1-1 3-1 4-2l1 1 1-1v1l-1 3h0v4l-1-1c-1 0-2 0-3 1l-1 1c-1 0-2-1-3 0-2 2-5 3-7 4-4 0-7-2-10-2h-1c-2-1-4-1-6-1 0 1-1 1-1 0h-1v-1l-9-3z" class="F"></path><path d="M447 173l4-2 1 1c1 1 2 1 3 1 2 0 9 2 10 3l1 1c-4 0-7-2-10-2h-1c-2-1-4-1-6-1 0 1-1 1-1 0h-1v-1z" class="H"></path><path d="M467 167h2v1h0l-1 1h4c2 0 3 0 5-2-2-1-2-1-4-1h0 3c1-1 3-1 4-2l1 1 1-1v1l-1 3h0v4l-1-1h-12c-3 0-6-2-9-1h0-5v-1h5 1c1 0 1 0 2-1 2 0 3 0 5-1h0z" class="P"></path><path d="M466 177c2-1 5-2 7-4 1-1 2 0 3 0l1-1c1-1 2-1 3-1l1 1v5 1 4 5h-4 0 0c1 1 1 1 2 1l-2 2v1c1 0 3 1 3 2s0 1-1 2c-3 0-3 1-5 3h-1-6l1 1c-6-1-12-1-18-1l1-2h-1l-1-1c1 0 1-1 1-2l-1-1c1 0 2 0 3-1h1c2 0 3-2 5-2-5-3-12-2-15-6h-3-5c-1-1-2-1-3-2h-1 4 6l-1-1h0-2 1v-1c-1 1-1 1-2 1s-4 0-5-1h3c2-1 3-2 5-3h2 0c0 1 1 1 1 2 2-1 3-1 4-3-1 0-2-1-2-1h2 1c0 1 1 1 1 0 2 0 4 0 6 1h1c3 0 6 2 10 2z" class="B"></path><path d="M465 192l1-1 1-1c1-1 2-1 3-1l-1 4-4-1zm6-3c1-1 0-1 2-1h0c1-1 1-1 1-2 1 1 2 1 3 1h0c1 1 1 1 2 1l-2 2c-1 0-1-1-2-1h0c-1 0-1 0-2 1h0l-2-1z" class="D"></path><path d="M477 187l2-1c1 0 1-1 1-1v-2c-2 0-3 0-5 1l-1-1c1 0 1 0 2-1l-1-1 1-1h1c2 0 3 1 4 2v5h-4z" class="E"></path><path d="M461 186h4c2 0 4 0 7-1h2l-1 2c-1 1-1 1-2 0-1 0-2 0-3 1h-3v-1c-1 0-2 0-3-1h-1z" class="H"></path><path d="M481 178l-4 1h-3-1v1l-1-1 1-1h1c1 0 0 0 1-1 0-1 1-1 1-2 2 0 2 0 4 1l1 1v1zm-10 11l2 1h0c1-1 1-1 2-1h0c1 0 1 1 2 1v1c1 0 3 1 3 2s0 1-1 2l-1-1-9-1 1-4h1z" class="E"></path><path d="M440 183l1-1c2 0 5 1 7 2 4 1 9 1 13 1v1h1c1 1 2 1 3 1v1h-4c-1 1-2 1-3 1h0c-5-3-12-2-15-6h-3z" class="W"></path><path d="M458 189h0c1 0 2 0 3-1 0 1 0 1 1 2v1c1 1 2 1 3 1l4 1 9 1 1 1c-3 0-3 1-5 3h-1-6l1 1c-6-1-12-1-18-1l1-2h-1l-1-1c1 0 1-1 1-2l-1-1c1 0 2 0 3-1h1c2 0 3-2 5-2z" class="N"></path><path d="M467 198v-1h-1c1-1 1-2 2-2 2 0 5 0 6 1v2h-1-6zm-42-17h6 1c1 1 2 1 3 2h5 3c3 4 10 3 15 6-2 0-3 2-5 2h-1c-1 1-2 1-3 1l1 1c0 1 0 2-1 2l1 1h1l-1 2-6 1-10 4c-2 1-3 2-5 3l-2-2-2 2c-2 0-5 1-7 2 1 1 2 1 3 1l-1 2-1 1-1 1h-2v2 1 1c-2 1-5 0-7 0h0l-2 2-4-1-5-1-1 1-1 1 1 2-2 1c-3-8-9-16-14-23l-8-10c1 0 2 1 3 2l2-2c-1-2-3-3-4-5l2-1 1 2h2c2 2 3 3 5 4l2-1h0c2-1 4-1 6-1 0 0 1 0 1-1l-1-1-1-2 7-1v1c2-1 2-1 4-1 5-1 12 1 17 0 2-1 4-1 6-1z" class="E"></path><path d="M423 200c3-1 6 1 9 1-2 1-3 1-5 2-1-1-3-1-4-1v-2zm17-17h3c3 4 10 3 15 6-2 0-3 2-5 2v-1c1 0 2 0 3-1-2-1-5-1-7-2h0v1c-1-1-2-1-3-1s-1 0-2-1c-3 0-6 0-8-1 1-2 3-1 4-2z" class="V"></path><path d="M398 183c2-1 2-1 4-1 5-1 12 1 17 0 2-1 4-1 6-1-2 1-4 1-6 1l6 1c-2 1-6 0-9 0-1 1-3 0-4 1-2 1-1 1-3-1h-4-2c-2 1-2 1-3 2-1 0-1 0-3 1h0l-1-1 2-2z" class="D"></path><path d="M394 198c3-1 4 0 7 0v1h2c3 2 7 3 11 4h6l1-1c-1-2-4-3-4-4 2 0 4 2 6 2v2h0c1 1 2 1 3 2h1l-2 2c0-1-1-1-1-1h-2c-2-1-5-1-7-1l-4-1-5-1c-2 1-2-1-3-2h-2c-2-1-4-2-7-2z" class="B"></path><path d="M433 196l2-1h0c2 1 4 1 5 1 2 1 2 1 4 1v2l-10 4c-2 1-3 2-5 3l-2-2h-1c-1-1-2-1-3-2h0c1 0 3 0 4 1 2-1 3-1 5-2 1 0 1-1 1-1 1-2 1-2 2-3l1 1h1v-1c-1 0-2 0-4-1z" class="H"></path><path d="M427 204h-1c-1-1-2-1-3-2h0c1 0 3 0 4 1 2 1 5 0 7 0-2 1-3 2-5 3l-2-2z" class="P"></path><path d="M433 196c-1-1-4-1-5-3h-1-1-7c-1 1-2 1-3 1v-1c2 0 4-1 6-1 2-1 2-1 4 0h0l1-1c2 0 7-1 9 0-1 1-2 1-3 2 1 1 1 1 3 2 1-2 5 0 7 0 2-1 3-1 5-1v-2-1h4c-1 1-2 1-3 1l1 1c0 1 0 2-1 2l1 1h1l-1 2-6 1v-2c-2 0-2 0-4-1-1 0-3 0-5-1h0l-2 1z" class="L"></path><path d="M449 195l1 1h1l-1 2-6 1v-2c2 0 3-1 5-2z" class="C"></path><path d="M391 183l7-1v1l-2 2 1 1h0c2-1 2-1 3-1 2 1 3 2 5 2v1c1 1 1 1 2 1-3 2-4 2-7 3h-3v1h14l1 1c-1 2-1 2-3 2h-1c-2 0-3 1-4 1l-1 1h0-1-1c-3 0-4-1-7 0 0-1-1-2-2-2-2-1-3-1-5-1h-1c1-1 1-2 1-3s0-1-1-2c-1 0-1-1-2-1l2-1h0c2-1 4-1 6-1 0 0 1 0 1-1l-1-1-1-2z" class="J"></path><path d="M386 188c2-1 4-1 6-1v2c-3 0-3 0-6-1z" class="B"></path><path d="M391 183l7-1v1l-2 2 1 1h0c2-1 2-1 3-1 2 1 3 2 5 2v1c-4-1-9 0-13 1v-2s1 0 1-1l-1-1-1-2z" class="G"></path><path d="M374 184l2-1 1 2h2c2 2 3 3 5 4 1 0 1 1 2 1 1 1 1 1 1 2s0 2-1 3h1c2 0 3 0 5 1 1 0 2 1 2 2 3 0 5 1 7 2h2c1 1 1 3 3 2l5 1 4 1c2 0 5 0 7 1h2s1 0 1 1c-2 0-5 1-7 2 1 1 2 1 3 1l-1 2-1 1-1 1h-2v2 1 1c-2 1-5 0-7 0h0l-2 2-4-1-5-1-1 1-1 1 1 2-2 1c-3-8-9-16-14-23l-8-10c1 0 2 1 3 2l2-2c-1-2-3-3-4-5z" class="a"></path><path d="M401 200h2c1 1 1 3 3 2l5 1v1c-1 1-1 1-1 2-1 0-2 0-3 1-1-1-3-1-4-2h2c-1-2-2-1-3-2s-1-1-1-2v-1zm10 7s1 0 1 1c2 0 4 0 5 2v1h2v1l-1 1h-2v2 1 1c-2 1-5 0-7 0 2-2 2-3 4-3l1-1c-1 0-2-1-3-1h-4c1-1 3-1 5-1l-2-3 1-1z" class="J"></path><path d="M411 203l4 1c2 0 5 0 7 1h2s1 0 1 1c-2 0-5 1-7 2 1 1 2 1 3 1l-1 2-1 1v-1h-2v-1c-1-2-3-2-5-2 0-1-1-1-1-1h-4c1-1 2-1 3-1 0-1 0-1 1-2v-1z" class="D"></path><path d="M381 199l9 1h4c2 1 3 3 5 3l3 3c1 0 1 0 2 1 2 0 3 0 5 1l1 2h-1c-2 1-3 1-5 2 2 2 4 1 6 4l-3 2c-1 0-1 0-2-1l-2 1-5-1-1 1-1 1 1 2-2 1c-3-8-9-16-14-23z" class="c"></path><path d="M450 198c6 0 12 0 18 1l6 2c1 2 1 2 0 3v1c0 2 1 2 2 3h0l1-1h2 1c-1 3 2 9 4 12 0 2 0 3 1 4l-1 1h0l-2 2h-2c0 1 0 1-1 2h0v2l1 1 1 2c1 1 1 2 1 4v9 2 8 9 4 5 2c0 1 0 3-1 4v2 1l-1 1v1 2l2 4c-1 1-2 3-3 4l-1 1h0v4h-2c-1 0-1 1-2 1v-1h-1v1l-3-1h-2-4-8-5-3-2l-2 2c-1 0-1-1-1-1-1 1-1 2-1 3-1 0-1-1-1-2l-1-1c-1 0-2 0-4-1v-1l-2 1c-1-1-1-2-2-3 0-4 1-9 0-12l1-1v-2c-1 0-2-1-2-1h-1v-1h2l1 1 1-1c-1-1-1-2-2-3 1-1 1-2 1-3l2-2-1-1c-2-1-3-2-4-3l-4-4c-2 0-2 0-3-1l-1-1s-1-1-2-1-2-1-3-2v-1l-5-9c-1-3-2-5-1-8l-1-1 1-7 2-6c1-4 3-7 5-11-1-1-1-2 0-3l1-1 1-1 1-2c-1 0-2 0-3-1 2-1 5-2 7-2l2-2 2 2c2-1 3-2 5-3l10-4 6-1z" class="w"></path><path d="M419 212l1-1 1 2-3 3c-1-1-1-2 0-3l1-1z" class="B"></path><path d="M427 204l2 2c-3 2-6 4-8 7l-1-2 1-2c-1 0-2 0-3-1 2-1 5-2 7-2l2-2z" class="G"></path><path d="M476 208h0l1-1h2 1c-1 3 2 9 4 12 0 2 0 3 1 4l-1 1h0l-2 2h-2c0 1 0 1-1 2h0v2l1 1 1 2c1 1 1 2 1 4v9 2h-2-2v-1l-3 1v-2c-2 0-3 0-5 1l-1 1v-1l-1-1-2 1-1-1v-2h2 0c0-2-1-2 0-4h1c1 0 1-1 1-2l-1-1v-1h2l-2-4h0c1-1 3-2 4-4 0-1-1-1 1-2 0-1 1-2 1-3h1l1 1 1-1v-4c1-4 0-7-1-11z" class="D"></path><g class="J"><path d="M473 242v-2l1-1v-1l-1 1-1-1v-1h2v-2-1c-1-1-1-1-1-2 2-2 4-3 6-4v2c-1 1-1 2-2 2v1c0 2-1 4-2 6-1 1-1 1-2 3z"></path><path d="M467 244c2-1 2-1 3-2v-1l2-2v2l1 1c0 1 2 3 2 4-2 0-3 0-5 1l-1 1v-1l-1-1-2 1-1-1v-2h2z"></path></g><path d="M476 208h0l1-1h2 1c-1 3 2 9 4 12 0 2 0 3 1 4l-1 1h0l-2 2h-2c0-5 0-11-2-16 0-1-1-1-2-2z" class="M"></path><path d="M479 230l1 1 1 2c1 1 1 2 1 4v9 2h-2-2v-1l-3 1v-2c0-1-2-3-2-4h0c1-2 1-2 2-3 1-2 2-4 2-6v-1c1 0 1-1 2-2z" class="c"></path><path d="M473 242c1-2 1-2 2-3 0 1 0 3 1 5 1 0 1 1 2 1v2l-3 1v-2c0-1-2-3-2-4h0z" class="a"></path><path d="M481 233c1 1 1 2 1 4v9 2h-2-2v-1-2c-1 0-1-1-2-1l1-2h3l-1-1c1-1 1-2 1-3s-1-1-1-2h1c0-1 0-2 1-3z" class="h"></path><path d="M478 245c2 0 2 0 4 1v2h-2-2v-1-2z" class="c"></path><path d="M430 230h4s0-1 1-1v2c1 0 1 1 2 1 1-1 1 0 2-1l1 1v-2c1-1 2-1 2-2l1-1v1c3 1 5 2 7 3 1 1 1 1 2 1v-2l1-1c1-1 2-2 3-2l2 1c2 0 5-3 6-5h2v-1l1 1h1c1 0 2 2 3 4 0 1 0 1-1 2v-2h-1c-1 2-1 3-2 4h-2-1 0v1h0c0 1 1 1 1 1h1 1l1-1 2 4h-2v1l1 1c0 1 0 2-1 2h-1c-1 2 0 2 0 4h0-2-1v1l-2-2h-2c0-1 0-1-1-2v1h-1l-1-1c0 1-1 1-1 2v1 2h-1l-1 1c-1 1-1 0-2 1 0 1 0 1-1 2l-1-1h-3v1 1h-1 0c-1-1-1-1-2-1l-1 2c-2 0-3 0-4-1l-1 1h0v3l-1-1c-1 2-1 2-3 3h-2 0v2h0c1 2 1 3 1 4h2c1-1 0-1 1-2 0 3 1 7 0 10v1h-1l-1-1c-2-1-3-2-4-3l-4-4c-2 0-2 0-3-1l-1-1s-1-1-2-1-2-1-3-2v-1l-5-9c-1-3-2-5-1-8l-1-1 1-7 2-6 1 1h2s0 1 2 1v-2c0-2 1-3 1-4h0 2 0l2 2h0 2v2h1v-1l1-1v2h1l1-1 1 1v3z" class="H"></path><path d="M428 239l1-3v-1c1-2 1-3 2-3 0 0 1 0 1 1 0 2 1 3 2 5 1 1 1 3 2 4-2 0-2 0-3-1v-1l-2-2v-1h-1l-2 2z" class="G"></path><path d="M464 231l-1-2h0c0-3 1-4 3-5h2c1 0 1 2 2 3h-1c-1 2-1 3-2 4h-2-1z" class="V"></path><path d="M440 242l-4-4c0-1 1-2 2-3h1 1c1 0 2 0 3 1 0 2 1 2 1 3l-1 1v4c0-1-1-1-1-2h-1-1z" class="G"></path><path d="M419 223h2 0l2 2h0 2v2h1v-1l1-1v2h1l1-1 1 1v3 2c-1-1-2-1-3-1 0-1 0-1-1-2h0v-1l1 1h0v-1h-1l-2 2v1l-2-1v1l-1-1c0-1-1-3-1-4v-2l-1-1z" class="W"></path><path d="M416 237l2 2h2v1c0 1 0 0 1 1v-1-1c1-1 1-1 1-2h-2v-1h0c2-1 2-2 3-3h1v2h2v-2-2c1 1 1 0 1 1 1 2 0 3 0 5v2h1c-1 2-1 2-2 3l-1 1-1-1h-1l1 2c-1 1-1 1-2 1l-1-1c0-1 0-2-1-3h-1c-1 1-1 2-2 2v1h-1v-4h0v-2-1z" class="G"></path><path d="M423 242c-1 0-1-1-1-1l2-2c1 1 1 2 2 3l-1 1-1-1h-1z" class="E"></path><path d="M444 239c1 0 2-1 3-3h1c1 0 2-1 4 0 0 1-1 2-1 3l-1 1 1 1h1v-2l1-1h1l-1 1v1h1c0 1 1 1 1 1l1-1h1v-2-2c2 0 2 2 4 2l1-1v-2c2 1 4 1 6 1v1l1 1c0 1 0 2-1 2h-1c-1 2 0 2 0 4h0-2-1v1l-2-2h-2c0-1 0-1-1-2v1h-1l-1-1c0 1-1 1-1 2v1 2h-1l-1-1h-1l2-1c0-2 0-2-1-3h-2v1l-1 1h-1-1v2h0l-1 1-1 1h0c0-1 1-1 1-2-1-1-1-2-3-2h0l-1-4z" class="G"></path><path d="M428 239l2-2h1v1l2 2v1c1 1 1 1 3 1v2c1-1 1-1 1-2h2 1 1 1c0 1 1 1 1 2v-4l1-1 1 4h0c2 0 2 1 3 2 0 1-1 1-1 2h0l1-1 1-1h0v-2h1 1l1-1v-1h2c1 1 1 1 1 3l-2 1h1l1 1-1 1c-1 1-1 0-2 1 0 1 0 1-1 2l-1-1h-3v1 1h-1 0c-1-1-1-1-2-1l-1 2c-2 0-3 0-4-1l-1 1h0v3l-1-1c-1 2-1 2-3 3h-2 0v2l-3-3v-2h0l-4-4v-1c1 1 2 1 2 2 2 1 2 1 3 2 1-1 1-1 2-1v1c3 0 3-1 5-3h1c-1-1-1-1-1-3h0l-1-1v3l-1 1c0-2-1-3-1-4h-2c-1-1-2-1-2-3v-2h-1c0 2 0 2 1 3l-1 1h-1-3c-1 1-1 1-2 1l-1-1c1 0 1 0 2-1l-1-2h1l1 1 1-1c1-1 1-1 2-3h0z" class="D"></path><path d="M448 246l1-1h0v-2h1 1v2l1 1h0c-2 1-2 1-4 0z" class="B"></path><path d="M429 254l3 1h0c1-1 2-1 4-2l1 1c-1 2-1 2-3 3h-2 0v2l-3-3v-2z" class="Q"></path><path d="M440 242h1 1c0 1 1 1 1 2h0v2h-1c1 0 2 1 3 1v1h-2l-2-2c-2 1-2 1-3 0h-1l1-2h-2c1-1 1-1 1-2h2 1z" class="B"></path><path d="M410 240l1-7 2 1h1l1-1 1 3v1 1 2h0v4h1v-1c1 0 1-1 2-2h1c1 1 1 2 1 3l1 1 1 1c1 0 1 0 2-1h3 1l1-1c-1-1-1-1-1-3h1v2c0 2 1 2 2 3h2c0 1 1 2 1 4l1-1v-3l1 1h0c0 2 0 2 1 3h-1c-2 2-2 3-5 3v-1c-1 0-1 0-2 1-1-1-1-1-3-2 0-1-1-1-2-2v1l4 4h0v2l3 3h0c1 2 1 3 1 4h2c1-1 0-1 1-2 0 3 1 7 0 10v1h-1l-1-1c-2-1-3-2-4-3l-4-4c-2 0-2 0-3-1l-1-1s-1-1-2-1-2-1-3-2v-1l-5-9c-1-3-2-5-1-8l-1-1z" class="a"></path><path d="M414 247c2-3 0-5 2-7v4h1v-1c1 0 1-1 2-2h1c1 1 1 2 1 3h-2v1c0 1 0 2 1 3h0c-1 0-2-1-2-2-1 0-2 1-2 2v2h0c-1-1-2-2-2-3z" class="E"></path><path d="M410 240l1-7 2 1h1l1-1 1 3v1 1 2h0c-2 2 0 4-2 7 0-1-1-2-1-2v-3c-1-1-1 0-2-1l-1-1z" class="V"></path><path d="M424 260h0l-1-1c0-1 0-1-1-2-1-2-1-2-1-3v-3-1h1c1 0 2 1 3 1 1 2 2 3 3 4l1-1h0v2h0c-1 1-2 2-2 4 0 1 2 1 2 3h-1c-1-1-2-2-2-4h-1l-1 1z" class="I"></path><path d="M424 260l1-1h1c0 2 1 3 2 4h1c0-2-2-2-2-3 0-2 1-3 2-4h0l3 3h0c1 2 1 3 1 4h2c1-1 0-1 1-2 0 3 1 7 0 10-1-1-3-1-4-2-1-2-4-3-6-6 0-1 0-2-1-3h-1z" class="X"></path><path d="M455 246h1v-2-1c0-1 1-1 1-2l1 1h1v-1c1 1 1 1 1 2h2l2 2v-1h1v2l1 1 2-1 1 1v1l1-1c2-1 3-1 5-1v2l3-1v1h2 2v8 9 4 5 2c0 1 0 3-1 4v2 1l-1 1v1 2l2 4c-1 1-2 3-3 4l-1 1h0v4h-2c-1 0-1 1-2 1v-1h-1v1l-3-1h-2-4-8-5-3-2l-2 2c-1 0-1-1-1-1-1 1-1 2-1 3-1 0-1-1-1-2l-1-1c-1 0-2 0-4-1v-1l-2 1c-1-1-1-2-2-3 0-4 1-9 0-12l1-1v-2c-1 0-2-1-2-1h-1v-1h2l1 1 1-1c-1-1-1-2-2-3 1-1 1-2 1-3l2-2h1v-1c1-3 0-7 0-10-1 1 0 1-1 2h-2c0-1 0-2-1-4h0v-2h0 2c2-1 2-1 3-3l1 1v-3h0l1-1c1 1 2 1 4 1l1-2c1 0 1 0 2 1h0 1v-1-1h3l1 1c1-1 1-1 1-2 1-1 1 0 2-1l1-1z" class="K"></path><path d="M437 276v1c0 2 1 3 0 6 2 0 3 0 4 1l-1 1h-2-2c-1-2 0-1 0-2 1-2 0-5 1-7z" class="r"></path><path d="M438 287c2 0 3 0 5-1l1 1h0c-1 2-1 3-1 4l-2 3h-1v2l-2-1h0c1-2 0-5 0-8z" class="c"></path><path d="M433 284l3 3h2c0 3 1 6 0 8h0l-1 2h5c-1 1-1 1-2 1-2 0-3 1-4 1l-2 1c-1-1-1-2-2-3 0-4 1-9 0-12l1-1z" class="F"></path><path d="M444 262c1 0 2-1 3-2 1 0 2-1 3-2 1 1 3 1 5 1l-1 1v2c0 1-1 1-1 2h-1l-1 1c0 1 0 1 1 2 0 1-1 2-1 3l-2 3v3c0 1 0 2-1 2-1-1 0-2 0-3 0-2 1-5 2-7h0c-1 1-2 2-2 3v1l-1 1v1c-1-1-1-2-1-2l-1-1v1l-1 1c-1 0-2 0-2-1v-2-2c-1-2-1-3-1-5 1 1 2 1 3 1v-2z" class="X"></path><path d="M438 255v-3h0l1-1c1 1 2 1 4 1l1-2c1 0 1 0 2 1h0 1v-1-1h3c0 2 1 3 2 4v-1c1-1 2-1 3-1l1 1c-1 1-1 1-1 2h0v5c-2 0-4 0-5-1-1 1-2 2-3 2-1 1-2 2-3 2v2c-1 0-2 0-3-1v-1-4h0l-1-1-2-2z" class="Q"></path><path d="M441 258c1 1 2 0 2 2l1 2v2c-1 0-2 0-3-1v-1-4z" class="h"></path><path d="M437 254l1 1 2 2 1 1h0v4 1c0 2 0 3 1 5v2 2 5h1v6 1h-2c-1-1-2-1-4-1 1-3 0-4 0-6v-1c0-1-1-2-1-3h2v-3l-2 2v-1c1-3 0-7 0-10-1 1 0 1-1 2h-2c0-1 0-2-1-4h0v-2h0 2c2-1 2-1 3-3z" class="l"></path><path d="M437 254l1 1 2 2 1 1-1 1v5c1 2 1 3 0 4h-1v-7l-1-1v1l-2-1v1c-1 1 0 1-1 2h-2c0-1 0-2-1-4h0v-2h0 2c2-1 2-1 3-3z" class="I"></path><path d="M457 263s1 0 2 1h0l2 4 1 1c0-2 1-2 2-3l1 2c0 3 2 6 2 9 0 1-1 2-1 2l1 3v3l1 1v2c1 1 1 2 1 3l1 1c1 0 1 1 2 0v1c1 2 1 3 3 4 0 1 0 1-1 1-1 1 0 1-1 2v1l-3-1h-2-4-8-5-3-2l-2 2c-1 0-1-1-1-1-1 1-1 2-1 3-1 0-1-1-1-2l-1-1c-1 0-2 0-4-1v-1c1 0 2-1 4-1 1 0 1 0 2-1h-5l1-2 2 1v-2h1l2-3 2 1 1-1c1-2 2-4 3-7v-1l1-5c0-2 1-3 1-5 0-1 1-2 1-4l1-2c1-2 2-3 4-4z" class="E"></path><path d="M457 263s1 0 2 1h0l2 4h-4v1l-1 2c-2 3 0 7-2 11-1 0-1 0-2-1 0-2 0-6 1-8 0-2 0-2-1-4l1-2c1-2 2-3 4-4z" class="f"></path><path d="M457 268l-1-2c1-2 1-2 3-2h0l2 4h-4z" class="C"></path><path d="M450 278c1 1 1 2 1 4l-1 1v4h2v-2l1 1c0 1 0 1-1 2h-1c-1 1-1 1-2 3 1 0 1 0 2-1 1 1 3 0 5 1l1-1c1 0 1 1 2 1h0c1 1 1 2 1 4h-2-1c-3 0-10 1-12-1v1c-2 0-2 0-4-1l2-3 2 1 1-1c1-2 2-4 3-7v-1l1-5z" class="c"></path><path d="M443 291l2 1h1c1 0 2 0 4-1v1c-1 1-3 2-5 2v1c-2 0-2 0-4-1l2-3z" class="e"></path><path d="M461 268l1 1c0 2 1 4 1 6-1 3 0 5 0 8l1 3v1h-1l-1-1h-3-2c-2-3-1-11-1-15l1-2v-1h4z" class="W"></path><path d="M456 271l1-2 1 2c0 1 1 3 1 4 0 2 0 6 1 7s1 2 1 3l-2 1h-2c-2-3-1-11-1-15z" class="F"></path><path d="M462 269c0-2 1-2 2-3l1 2c0 3 2 6 2 9 0 1-1 2-1 2l1 3v3l1 1v2l-1 2h0c0 2 1 3 2 4h0l1 1h-5-5c0-2 0-3-1-4h0c-1 0-1-1-2-1v-1c1 0 2-1 3-2l1 1 2-1h1v-1l-1-3c0-3-1-5 0-8 0-2-1-4-1-6z" class="G"></path><path d="M459 291c2-1 4 0 6 1v3h-5c0-2 0-3-1-4z" class="Q"></path><path d="M465 282l2 8h0c0 2 1 3 2 4h0l1 1h-5v-3c0-1 1-2 0-3v-1-3h-1l1-3z" class="K"></path><path d="M462 269c0-2 1-2 2-3l1 2c0 3 2 6 2 9 0 1-1 2-1 2l1 3v3l1 1v2l-1 2-2-8c0-3-1-5-2-7 0-2-1-4-1-6z" class="b"></path><defs><linearGradient id="I" x1="456.714" y1="301.377" x2="456.335" y2="297.164" xlink:href="#B"><stop offset="0" stop-color="#1d1e1e"></stop><stop offset="1" stop-color="#3d3a36"></stop></linearGradient></defs><path fill="url(#I)" d="M467 290l1-2c1 1 1 2 1 3l1 1c1 0 1 1 2 0v1c1 2 1 3 3 4 0 1 0 1-1 1-1 1 0 1-1 2v1l-3-1h-2-4-8-5-3-2l-2 2c-1 0-1-1-1-1-1 1-1 2-1 3-1 0-1-1-1-2l-1-1c-1 0-2 0-4-1v-1c1 0 2-1 4-1 1 0 1 0 2-1h-5l1-2 2 1v-2h1c2 1 2 1 4 1v-1c2 2 9 1 12 1h1 2 5 5l-1-1h0c-1-1-2-2-2-4h0z"></path><path d="M455 246h1v-2-1c0-1 1-1 1-2l1 1h1v-1c1 1 1 1 1 2h2l2 2v-1h1v2l1 1 2-1 1 1v1l1-1c2-1 3-1 5-1v2l3-1v1h2 2v8 9 4 5 2c0 1 0 3-1 4v2 1l-1 1v1 2l2 4c-1 1-2 3-3 4l-1 1h0v4h-2c-1 0-1 1-2 1v-1h-1c1-1 0-1 1-2 1 0 1 0 1-1-2-1-2-2-3-4v-1c-1 1-1 0-2 0l-1-1c0-1 0-2-1-3v-2l-1-1v-3l-1-3s1-1 1-2c0-3-2-6-2-9l-1-2c-1 1-2 1-2 3l-1-1-2-4h0c-1-1-2-1-2-1l-2-1v-1c1-1 1-1 1-2 1-2 0-3-1-5h0c0-1 0-1 1-2l-1-1c-1 0-2 0-3 1v1c-1-1-2-2-2-4l1 1c1-1 1-1 1-2 1-1 1 0 2-1l1-1z" class="d"></path><path d="M477 275v-1l-1-1h1v-1c-1 0-1 0-3-1l1-1c1 0 1-1 1-2v-1s1-1 2-1h1c1 0 2-1 3-1v4 5 2c0 1 0 3-1 4h0c-1-1-2-1-3-2s-1-2-1-3z" class="b"></path><path d="M477 275h3v1h1c-1-1-1-1-1-2h-1c1-1 1-2 2-2l-1-1v-1l2-1v5 2c0 1 0 3-1 4h0c-1-1-2-1-3-2s-1-2-1-3z" class="l"></path><path d="M468 280l1-1h1l1 1c2 1 2 2 4 2v-1h-2l1-1c1 0 1 0 2 1v-1c1-1 2-1 3-1-2 0-3-2-3-3l1-1c0 1 0 2 1 3s2 1 3 2h0v2 1l-1 1v1 2c-1 0-1 1-2 1s-2 0-3-1l-1 1c-1 1-2 1-3 2l-3-10z" class="b"></path><path d="M481 280h0v2 1l-1 1v1h-2c-1-1-1-1-2-3 1-1 1-1 2-1s1 0 2 1l1-2z" class="j"></path><path d="M476 282c1 2 1 2 2 3h2v2c-1 0-1 1-2 1s-2 0-3-1c-1 0-1 0-2-1h3v-4z" class="l"></path><path d="M468 288v-2l-1-1v-3l-1-3s1-1 1-2l1 3 3 10c1-1 2-1 3-2l1-1c1 1 2 1 3 1s1-1 2-1l2 4c-1 1-2 3-3 4l-1 1h0v4h-2c-1 0-1 1-2 1v-1h-1c1-1 0-1 1-2 1 0 1 0 1-1-2-1-2-2-3-4v-1c-1 1-1 0-2 0l-1-1c0-1 0-2-1-3z" class="m"></path><path d="M455 246h1v-2-1c0-1 1-1 1-2l1 1h1v-1c1 1 1 1 1 2h2l2 2v-1h1v2l1 1 2-1 1 1v1l1-1c2-1 3-1 5-1v2l3-1v1h2 2v8l-2-1c-1 1-2 1-4 2l-1 3c0 1 1 2 2 3-1 1-2 1-2 2-2 1-5 2-8 1-1 0-1 0-2 1v1l-1-2c-1 1-2 1-2 3l-1-1-2-4h0c-1-1-2-1-2-1l-2-1v-1c1-1 1-1 1-2 1-2 0-3-1-5h0c0-1 0-1 1-2l-1-1c-1 0-2 0-3 1v1c-1-1-2-2-2-4l1 1c1-1 1-1 1-2 1-1 1 0 2-1l1-1z" class="h"></path><path d="M463 259l2 1v2l-1 1h-1l-2-2v-1l2-1z" class="f"></path><path d="M467 259l1-1v-1c1-2 1-2 2-2s2 1 2 1h1l1 1v1l-2-2c-1 1-1 2-1 2l-1 1c-1 0-1 0-1 1-1-1-1-1-2-1z" class="c"></path><path d="M480 248h2v8l-2-1v-2c0-1-1-1-1-2h0c-1-1 0-2 1-3z" class="K"></path><path d="M470 247c2-1 3-1 5-1v2 1c0 1 1 1 1 2s-1 0-2 0c0 0-1 2-2 2l-1-1h0c0-1 1-2 2-3l-3-2z" class="c"></path><path d="M460 254l1-1v-1c2 0 3-1 4-1v2l1 1c2 0 3 1 5 0v-2l1 1v1l1 2h-1s-1-1-2-1-1 0-2 2v1l-1 1h-1l-1 1-2-1-2 1-1-6z" class="a"></path><path d="M460 254l1-1v-1c2 0 3-1 4-1v2h-1v3l-2 1h-1v1h1 1v1l-2 1-1-6z" class="J"></path><path d="M458 250l1 3 1 1 1 6v1l2 2 1 3c-1 1-2 1-2 3l-1-1-2-4h0c-1-1-2-1-2-1l-2-1v-1c1-1 1-1 1-2 1-2 0-3-1-5h0c0-1 0-1 1-2l2-2z" class="c"></path><path d="M461 261l2 2 1 3c-1 1-2 1-2 3l-1-1-2-4v-1h1l1 1h1c-1-1-1-1-1-2v-1z" class="d"></path><path d="M455 246h1v-2-1c0-1 1-1 1-2l1 1h1v-1c1 1 1 1 1 2h2l2 2v-1h1v2l1 1 2-1 1 1v1l1-1 3 2c-1 1-2 2-2 3h0v2c-2 1-3 0-5 0l-1-1v-2c-1 0-2 1-4 1v1l-1 1-1-1-1-3-2 2-1-1c-1 0-2 0-3 1v1c-1-1-2-2-2-4l1 1c1-1 1-1 1-2 1-1 1 0 2-1l1-1z" class="f"></path><path d="M469 248l1-1 3 2c-1 1-2 2-2 3l-1-1c-1 0-1 1-2 1-1-1-2-2-3-2 1-1 3-2 4-2z" class="Q"></path><path d="M451 250c2 0 3 0 5-1v-3l1-1 1 1v4h0l-2 2-1-1c-1 0-2 0-3 1v1c-1-1-2-2-2-4l1 1z" class="T"></path><path d="M440 499v1 4c1 1 3 1 4 1h1 4 8 11l-1 1h-3l1 1c1 1 1 2 1 3 1-1 0-1 2-2h3c1 2 1 4 1 5v-4l3-1v2c0 3 1 8 0 11 0 1-1 5 0 6v1c1 1 2 1 2 2l1 1c0 2 0 3 2 4h4c1 1 0 1 0 3 3 0 5-1 7-1 1 1 1 1 2 0h9 1c1 0 1 0 2 1h0c1-1 2-1 2-1h15 3v1s1 0 1-1c1 0 3 1 4 1 0 1 0 0 1 1 0-1 0-1 1-2h1v-1h2v-1h5v1l2-1h1v-1c0-1 0-2 2-3v-1c0-1 0-2 1-3 2 2 1 3 1 5l1 1v-2h1 2 3v6h1 2v4l1 7-1 3c2 0 3 0 4 2v-2h-1v-6c0-5 0-9-1-14h5 0 5 0l4-1 3 1h2l-1 4v5l1 1v3c1 0 1-1 1-1v-1h1v1c1 0 1 0 2 1 0 3 1 5 2 9 0 2 1 3 2 6v2l4 39c1 6 0 13 0 19 0 3-1 7 0 10v1 1c0 1 0 0 1 1h0c1-1 1-1 2-1h1v2l-1 1v-2h-1v2l-2-1v1 1h14l-1-1h2l1 1h-1v5 11 7 1l-1-1h-1c0-1-1-2-1-3v3h-1c-1 3 0 3 0 6h-3c4 3 6 7 7 12v-1c1 3 1 6 1 8l1 1c0-4 0-8 1-12h1c0 3 0 6 1 8l-1 1v3h2l3-1h15 8 2l-2-2c0-1 0-1 1-2 0 1 0 1 1 1v-4h1c0 1 0 6 1 8h-1v7l2 1v1l-1 1c0 2 1 5-1 7h-1l-1 1c-2 5-13 32-16 34h-2v-3h-2-8c-5-1-10 0-14 0h-6v3c0 2 0 3-1 4l-1 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1c1 2 3 2 2 4 0 1 0 1-1 2-1 0-1 1-1 2l1 1v9 3c0 2 0 6 1 7h0v1 2l-1 1s0 1-1 2h1v2l-1 2 1 1-1 1v3c-2 1-4 0-6 0v-1l-1-1-1-1h-1c-1 0-2 0-2-1l-1-1v1 2h-1c-1 1-1 2-1 3h-1v-2l-1 2h-1v-1l-1-1h-1-2c-1 0-2 1-2 1-2 0-2-1-3-2h-1c0 1 0 3-1 4h-8v1c-1 0-4-1-5 0-1 0-1 0-2 1h-30-1c1 1 2 1 3 1h-35v4c1 1 2 7 2 9v5c1 1 1 2 1 3l1-1h0v-3c0-4 2-9 6-12-1 2-2 3-3 5s-2 4-2 7l1-1c1 2 1 4 1 7 0 1 1 2 1 3v4l2 2h0 1c1 2 2 6 1 8v1c-2 3 0 5-3 7h-1v-3l-1 1h0c0 1-1 2-2 3h6c-2 1-5 1-7 1-4-1-9 0-13-1 0-1-1-1-1-2v-2l-1-1c-2 0-2 0-2 2 0 1 0 0 1 1l1 1v1c-3 1-5 1-8 0h-5c-3 2-4 2-7 1h-2 0c-3-1-5 0-7 0 2 0 4 1 6 1 1 0 1 0 1 1l-9-1c-1 0-3 0-4-1l1-2v-7h-1c-2 1-3 1-5 1h-1l-1 2c-1 0-1-1-2 0h0c-1 1 0 1-1 2v-5h-1v6l-1 1c0-1 0-3-1-4h-5c-2-1-2-2-2-3l-1-1v-2l-1 1c0 1 0 1 1 2v1h-2v-3h-1c0 1 0 1-1 1l-1-1-2 1v-1h-2l-1-1v-1h0l-1 1h-1 0c-1-1-1-1-2-1 1-1 2-4 3-6 1-1 0-1 1-2l1-1c2-3 2-4 5-5 0-1 0-2 1-3l-1-4h6c2-2 0-5 1-7 0-1 1-1 1-2l-1-1 1-1c-1-2-1-3-1-5v-2c0-1-1-3-2-4l-1-1c-2 0-3 1-4 2l-1-1v-3-1l1-1c-1-1-1-2-1-3v-5c0-1 1-2 1-3v-2-16-1h-3l2-1v-1h7l-1-2v-2-1c1-2 1-4 0-6h-2l1-1 1 1c0-2 0-2 1-3 0-2-1-4-1-5h-1v1c-2-1-2-2-2-4h-3l1-1c-2 0-2 0-4-1h4v-4c1-3 0-7 0-10v-2l-3-1h10 1c0-1 0-1 1-1 0 1 0 1 2 1 0-1 0-1 1-1v1h1l1-2h0c0 1 0 1 1 2 1-1 1-3 1-4h0c0 1 0 2 1 4h1 1c3-2 0-4 2-6 0 2-1 4 1 6v-1-5h1l1-1h1v3c2-1 0-2 2-3v3l1-2v-2c1-1 2-2 3-2h3 0c2-4 0-9 3-14 1 0 0-1 0-3 1 0 1 0 2-1v-2l-1-1c1-4 4-6 6-9l1-1c-4 2-7 4-8 8l-2 8c-1-1-1-4-1-5h0l-2-2v-1c-1-1-1-1-1-2 0 0 0-1-1-2 0-2-1-2-2-3h-2v-1-1c-1 0-2 0-3-1-1 0-1-1-2-1-2 0-2 0-3-1-1 0-1 0-2-1h0c-1 0-1-1-2-1h-1v-1h0c-1-1-3 0-4 0l-1-1c1-1 1-3 1-4l-1-1h-2-1l-4 1h-15c2-4 1-9 1-13l1-1v-1l-1-1c-1 0-1-1-1-1 0-4-1-7-1-11h0-2c0-1 0-2 1-2h0 1v-2h1c1 2 1 2 1 4v2c1 1 0 0 1 0h6l7 1-2-1c-1 0-2 0-3-1 1 0 2-1 2-1h1l1-1c1-1 1-2 2-4l-1-1v-3-28-7-33-5-11c3 1 5 3 7 4l1-1h0c-1-2-1-2-3-3h-1l-1-3c2 1 3 1 4 2l1-2c0-2-1-5 0-8 1 0 1-1 2-2h1l1-1h0 2 2v-3c0-1 1-3 1-4h1c1-1 1-2 2-2s1 0 1-1c1-1 0 0 2-1l1 1 1 1v2 8c0 2 1 3 1 5h1v-1c-1-3-1-3 0-5-1-2-1-3 0-5v-7c1-2 0-3 0-5h-2v-1l1-1c-1-2-2-3-2-4h-1l1-1h1c1 1 1 0 2 0z" class="L"></path><path d="M563 705h1v2c1 1 2 1 4 1v1l-1 1c-2-1-2 0-3 0s-3-1-4-1v-1h1l2-1v-2z" class="B"></path><path d="M482 700v5c-2 0-3 1-4 0v-3l1-1h1l2-1zm102 0h6c-2 2-1 3-2 4h-1-2-1c0-1 0-1-1-2h0l1-2z" class="R"></path><path d="M544 702c1-1 2-1 3-1h4v2l-2 2-4 1v-1l-1-3z" class="v"></path><path d="M504 708h25c3 0 9 1 12 0h1c1 0 2 1 3 1l1-1c2 0 3 0 4 1v1c-1 0-2-1-3 0h-17-15-7c-2-1-3 0-5-1l1-1z" class="C"></path><path d="M574 699l2-1c0 1 0 2 1 3h1 0l1 1 1 1v-3h2 2l-1 2h0c1 1 1 1 1 2h-7c-2 0-2 1-3 0h-2v1-1l-1 1c-1 0-1-1-2 0h-1v-1-1c0-1-1-2-1-3v-1h7z" class="U"></path><path d="M568 704v-4l1 1c2 0 3 0 5-1l1 1-3 3v1-1l-1 1c-1 0-1-1-2 0h-1v-1z" class="p"></path><path d="M553 699h14v1c-1 1-1 1-1 2v3h-2-1-1c0-1-1-2-1-2-1 1 0 1-1 3l-1-1h-3v-1l-1-1v2h-3l-1-2v-2h5l-1-1s-1 0-1-1h-1z" class="R"></path><path d="M430 711h1c3-2 0-4 2-6 0 2-1 4 1 6v-1-5h1v5c1 1 2 1 3 2v-1c1 0 1 0 2 1h10 18 83 21c2 0 6 0 8 1h-16-31-94 0l1 1v1l2 2h-1c-1 1-1 2-2 3h1l2-1c1 0 2 0 3-1v1h2c-1 1-2 2-3 2l-1-1-1 1h-1c-1 0-2 0-2 1-2 1-2 1-4 1l-1 1h-3c-1-1-1-1-1-3h1c2 0 3-1 4-2h-3c-1 0-1 0-2 1h0c-1-2 0-4-1-6-2-1-2 0-4 0-1 1-2 2-2 4 0 1 0 2 2 2h1l1 1 1-1c1 1 0 2 0 3h0l-2 2h-4-1c0-1 1-1 1-1-2-2-1-2-2-3h-2l-1-4c-1-1-1-2-1-2-1-2-1-1-2-2l-2 1v-2l-3-1h10 1c0-1 0-1 1-1 0 1 0 1 2 1 0-1 0-1 1-1v1h1l1-2h0c0 1 0 1 1 2 1-1 1-3 1-4h0c0 1 0 2 1 4h1z" class="S"></path><path d="M417 717v-3h1c1 2 1 4 2 6l1-1v1l1 1v2l2-2s1 0 2-1l1 1 1-1c1 1 0 2 0 3h0l-2 2h-4-1c0-1 1-1 1-1-2-2-1-2-2-3h-2l-1-4z" class="o"></path><path d="M580 713c1-1 0-1 1-1l1-1h1c0 2 0 3 1 4v2 3l-1-1c0 1-1 2-1 3-1-1-1-3-1-5h-4v2l-2-2c0 1 0 1-1 2v-1h-7 0-2-3-8c-1-1-2-1-4-1-5 0-10 2-15 1-2-1-5-1-7-1l-25 2h0l-3-1h-4c-6 0-12 0-18-1h-10-9l-1 1-1-1h-4-1c0 1 0 2 1 2 2 2 10 4 10 7l-10-6-3-3c-1-1-1-1-2-1-1-1-1 0-2-1h0l-1 1 1 1h-1l-1 1c-1 0-2 0-3-1h1l-2-2v-1l-1-1h0 94 31 16z" class="Y"></path><path d="M459 717c4-3 12 1 17-1 2-2 6-1 10-1h17 14c2 0 3 1 5 1h4-1l8 1h0-5l-25 2h0l-3-1h-4c-6 0-12 0-18-1h-10-9z" class="p"></path><path d="M580 713c1-1 0-1 1-1l1-1h1c0 2 0 3 1 4v2 3l-1-1c0 1-1 2-1 3-1-1-1-3-1-5h-4v2l-2-2c0 1 0 1-1 2v-1h-7 0-2-3-8c-1-1-2-1-4-1-5 0-10 2-15 1-2-1-5-1-7-1h5 0l-8-1h1-4c-2 0-3-1-5-1h-14 12c1-1 3-1 4-1h11c1 0 3 1 4 0l-1-1h0 31 16z" class="U"></path><path d="M414 713c1 1 1 0 2 2 0 0 0 1 1 2l1 4h2c1 1 0 1 2 3 0 0-1 0-1 1h1 4l2-2h0c0-1 1-2 0-3l-1 1-1-1h-1c-2 0-2-1-2-2 0-2 1-3 2-4 2 0 2-1 4 0 1 2 0 4 1 6h0c1-1 1-1 2-1h3c-1 1-2 2-4 2h-1c0 2 0 2 1 3h3l1-1c2 0 2 0 4-1 0-1 1-1 2-1h1l1-1 1 1c1 0 2-1 3-2h-2v-1c-1 1-2 1-3 1l-2 1h-1c1-1 1-2 2-3 1 1 2 1 3 1l1-1h1l-1-1 1-1h0c1 1 1 0 2 1 1 0 1 0 2 1l3 3-1 1h1c2 2 4 3 5 5h0c-1 0-3-1-4-1l-4-3h-1c-1 1-1 1-2 1l-5 4v2c1 1 2 0 1 2l-2 2-2 2h0c1-1 2-1 2-2 1 1 1 2 2 3l2-1v1c0 1 0 1-2 1l-1 2c-1 1-2 3-4 4h0l-1 2-1 3c-1 1 0 3-1 4v1h-1v-1h1 0l-1-2-1-1 1-1v-1l-2-2c0 1 0 1-1 2l-1 1v-2c-1 2-1 2-1 4h-1 0-2c-1 0-1 0-2-1l-3 2v-2l-1 1c0 1 0 0-1 1l-2-1v-2-1c1-2 1-4 0-6h-2l1-1 1 1c0-2 0-2 1-3 0-2-1-4-1-5h-1v1c-2-1-2-2-2-4h-3l1-1c-2 0-2 0-4-1h4v-4c1-3 0-7 0-10l2-1z" class="e"></path><path d="M435 730c1 1 1 2 0 3v1c-2 1-2 1-2 3l-1-3-2 2c-1 0-1 0-2-1v-1l-1-1-1 2h0l-1-1c1 0 2-2 2-2h3c2 0 3-1 4-2h1z" class="R"></path><path d="M442 727v2c1 1 2 0 1 2l-2 2c-2 0-3 0-4 2v1l-2-2v-1c1-1 1-2 0-3l1-2c2 0 4-1 6-1z" class="U"></path><path d="M435 734l2 2v-1c1-2 2-2 4-2l-2 2h0c1-1 2-1 2-2 1 1 1 2 2 3l2-1v1c0 1 0 1-2 1l-1 2c-1 1-2 3-4 4h0l-1 2-1 3c-1 1 0 3-1 4v1h-1v-1h1 0l-1-2-1-1 1-1v-1l-2-2v-3l-1-3 2-1v-1c0-2 0-2 2-3z" class="O"></path><path d="M433 738l1 1h0c-1 1 0 2 0 3v1c1 0 1 0 1 1v1c-1 1-1 1-1 2l-2-2v-3l-1-3 2-1z" class="U"></path><path d="M417 733l1-1c2 0 5 0 6-1v6c-1 1-1 2-1 3h4v3h-1v-2l-2 2c0 1 1 1 0 2v2 2l-3 2v-2l-1 1c0 1 0 0-1 1l-2-1v-2-1c1-2 1-4 0-6h-2l1-1 1 1c0-2 0-2 1-3 0-2-1-4-1-5z" class="S"></path><path d="M421 742h2v2c-1 0-1 1 0 2h-1-2c1-1 1-3 1-4z" class="p"></path><path d="M418 738l1-1 1-1c1 1 1 2 2 4h0-3v1c1 0 1 0 2 1 0 1 0 3-1 4l-1 1c0 1 0 2 1 3 0 1 0 0-1 1l-2-1v-2-1c1-2 1-4 0-6h-2l1-1 1 1c0-2 0-2 1-3z" class="M"></path><path d="M414 713c1 1 1 0 2 2 0 0 0 1 1 2l1 4h2c1 1 0 1 2 3 0 0-1 0-1 1h1 4c2 0 2 1 3 1v2c-1-1-1 0-1-1l-1 1h0l-1-1v3h2v1c-1 1-2 0-3 0h-1c-1 1-4 1-6 1l-1 1h-1v1c-2-1-2-2-2-4h-3l1-1c-2 0-2 0-4-1h4v-4c1-3 0-7 0-10l2-1z" class="m"></path><path d="M421 725h1c-1 1-1 3-2 4h-1c-1-1-1-2-1-4h3z" class="U"></path><path d="M426 725c2 0 2 1 3 1v2c-1-1-1 0-1-1l-1 1h0l-1-1c-1 1-2 1-2 3-1 0-2 1-3 0v-1h-1c1-1 1-3 2-4h4z" class="M"></path><path d="M414 713c1 1 1 0 2 2 0 0 0 1 1 2l1 4h2c1 1 0 1 2 3 0 0-1 0-1 1h-3c-1 1-1 1-2 1v-2h-2c0-3 1-8 0-11z" class="i"></path><path d="M457 670c5-1 10-2 14 1 2 2 5 4 6 6 0 1-1 2-1 3 1 1 2 2 2 3l-1 2v1c0 1 0 2 1 4v9l-1 1v2l-1 4h-1c-1-1-2-1-3-1s-1 0-1 1l-1-1h-2v-1l-3 1h-4v-1h-1c-1 1-3 1-4 1h-3v2h-2l-1 1v-1h-1v1 1h-1v-3h-1l-1 1v3 1c-2 0-3 0-5-1h2v-4-1h-1l-1 3v-2h0l-1-1h0v-2c1-1 2-2 3-2h3 0c2-4 0-9 3-14 1 0 0-1 0-3 1 0 1 0 2-1v-2l-1-1c1-4 4-6 6-9l1-1z" class="k"></path><path d="M440 705v-2c1-1 2-2 3-2h3 0c0 2 1 3 0 4h-1l-1-1-1 1-1-1c-1 0-1 0-2 1h0z" class="u"></path><path d="M451 681l1-1c1-1 4-5 6-5l1 2c0 1 0 2 1 3v3h1v2c2 0 2 0 3 1h0-2v1c-2-1-1-1-2-1h-1v-1c-2 0-5-1-7 0-1 0-2 0-3-1 1 0 1 0 2-1v-2z" class="l"></path><path d="M451 681l1-1c1-1 4-5 6-5l1 2h-1v6h-1-1-1v-1c-1-1-1 0-1-1l-1 1h0l-1 2s-1 0-1-1v-2z" class="Z"></path><path d="M456 705c0-2 1-2 0-3v-2c-1 1-1 3-1 4-1 0-1 0-2 1h0l-2-1-1-3c-1 2-1 2-1 4h-1v-5h4c2-1 4 0 6 0v1l1-1h3 2s0 1 1 1l1-1h2 9v2l-1 4h-1c-1-1-2-1-3-1s-1 0-1 1l-1-1h-2v-1l-3 1h-4v-1h-1c-1 1-3 1-4 1z" class="v"></path><path d="M457 670c5-1 10-2 14 1 2 2 5 4 6 6 0 1-1 2-1 3 1 1 2 2 2 3l-1 2v1h-4l2-2h0-3c-1 1 0 1-1 1h-1c0 1-1 1-2 1v-4h-1v5h-1v-3h-1v3c-1 0-1 0-1 1h-1l1-2h0c-1-1-1-1-3-1v-2h-1v-3c-1-1-1-2-1-3l-1-2c-2 0-5 4-6 5l-1 1-1-1c1-4 4-6 6-9l1-1z" class="o"></path><path d="M460 678v-1-3h3v1c0 1 1 1 1 2h1l1-1c1 2 0 5 0 7v1h-1v3c-1 0-1 0-1 1h-1l1-2h0c-1-1-1-1-3-1v-2h-1v-3-2z" class="j"></path><path d="M460 678l2 1h1c1 1 1 2 1 3h-1-1l-1 1h-1v-3-2z" class="K"></path><path d="M457 670c5-1 10-2 14 1 2 2 5 4 6 6 0 1-1 2-1 3-1 1-1 1-2 1-1-1-1-1-1-3h0c-2-1-4-2-5-3h-1l-1-1c-1 0-1 1-2 1h-1v-1h-3v3 1 2c-1-1-1-2-1-3l-1-2c-2 0-5 4-6 5l-1 1-1-1c1-4 4-6 6-9l1-1z" class="R"></path><path d="M463 726c0-3-8-5-10-7-1 0-1-1-1-2h1 4l1 1 1-1h9 10c6 1 12 1 18 1h4l3 1h0l25-2c2 0 5 0 7 1 5 1 10-1 15-1 2 0 3 0 4 1h8 3 2 0 7v1c1-1 1-1 1-2l2 2v-2h4c0 2 0 4 1 5 1 2 0 4 0 6v1c-1-1-2-2-3-2-2-1-2 0-3-1h-1 0l3 3-1 1h1l1 1c-2 1-2 0-3 1-1-1-1-1-1-2h-1v-1c0-1-1-2-1-2h-1-4 0l2-3h0c-2 1-6 4-7 5 0 1 0 2-1 2h-6-8-5-27-4c1-1 3 0 4-1-1-1-1-1-3-1h-13c-3 0-6 1-9 1h-17-3l-1-1v-1c-2-1-4-1-6-1 0 0 0-1-1-1z" class="l"></path><path d="M554 718h8 3c0 1 0 2-2 2h0v-1h-1c-1 0-1 1-2 1s-2 0-2 1c-1-1-1-1-3-1h-1v-1h0v-1z" class="e"></path><path d="M528 717c2 0 5 0 7 1 5 1 10-1 15-1 2 0 3 0 4 1v1h0v1h1c2 0 2 0 3 1-3 1-4 0-6 1h0c-1 0-1 0-1-1-2 1-4 3-5 4 1 0 1 0 2-1 2 0 2-1 4-1v1c-4 3-6 5-11 4h-3-2v-2h-2v1l1 1c-1 0-1 0-2 1h0-2 0l2-1v-1l-3 1h-3c1-1 3-2 5-3h0v-1h-5v-1h-2c2-1 4-1 6-1l1 1c0-1 0-1 1-1 1 1 2 1 3 1l1-1c-1 0-2 0-2-1h-1-9c-3 1-6 1-8 0h-6v-1c-2 0-6 1-8 0v-1l25-2z" class="i"></path><path d="M463 726c0-3-8-5-10-7-1 0-1-1-1-2h1 4l1 1 1-1h9 10c6 1 12 1 18 1h4l3 1h0v1c2 1 6 0 8 0v1h6c2 1 5 1 8 0h9 1c0 1 1 1 2 1l-1 1c-1 0-2 0-3-1-1 0-1 0-1 1l-1-1c-2 0-4 0-6 1h2v1h5v1h0c-2 1-4 2-5 3h-6c-3 1-6 0-8 1h-13c-3 0-6 1-9 1h-17-3l-1-1v-1c-2-1-4-1-6-1 0 0 0-1-1-1z" class="g"></path><path d="M463 726c0-3-8-5-10-7-1 0-1-1-1-2h1 4c0 1 1 2 2 3l2 1c3 2 6 3 10 5h1c1-1 2 0 3 1 1-1 1-1 3-1l1-1v1 1h1l1 1c1 0 1 0 2-1h0 3l1 1v1c2 0 3 0 4 1h-17-3l-1-1v-1c-2-1-4-1-6-1 0 0 0-1-1-1z" class="O"></path><path d="M463 726c0-3-8-5-10-7-1 0-1-1-1-2h1 4c0 1 1 2 2 3l2 1v1c1 1 3 2 4 3 2 1 5 2 8 3l1-1v1h0c-1 0-2 1-3 2l-1-1v-1c-2-1-4-1-6-1 0 0 0-1-1-1z" class="o"></path><path d="M459 717h9 10c6 1 12 1 18 1h4l3 1h0v1c2 1 6 0 8 0v1c-2 0-15 1-15 1l-10 1c-2 1-6 1-8 1s-5-1-7 0h-1c-2 0-8-4-10-5h-1l-1-1 1-1z" class="M"></path><path d="M459 717h9l4 1h0l-1 1c-2 0-7-1-8 0 2 1 3 1 6 2 3 0 6-1 8 1h-8 0c-3-1-5-1-7-3h-2-1l-1-1 1-1z" class="o"></path><path d="M468 717h10c6 1 12 1 18 1-6 2-10 1-15 1-3 0-7 1-10 0l1-1h0l-4-1z" class="e"></path><path d="M460 719h2c2 2 4 2 7 3h0 8 0l3 1 1-1-2-1h5l1 1 1 1 1-1-1-1h-1l1-1h2l1 1c2 0 4 0 7 1l-10 1c-2 1-6 1-8 1s-5-1-7 0h-1c-2 0-8-4-10-5z" class="S"></path><path d="M495 724c3-3 25-1 30-1h2v1h5v1h0c-2 1-4 2-5 3h-6c-3 1-6 0-8 1h-13c-3 0-6 1-9 1-1-1-2-1-4-1v-1l-1-1h4c1-1 2-2 3-2 1-1 0 0 2-1z" class="U"></path><path d="M495 724l3 1 2-1c1 1 1 2 2 3h-5l-2-1v-2z" class="p"></path><path d="M486 727h4c1-1 2-2 3-2 1-1 0 0 2-1v2l2 1h5 8c2-1 7-1 9-1 1 0 0 1 2 0h6v1c-2 1-5-1-6 1-3 1-6 0-8 1h-13c-3 0-6 1-9 1-1-1-2-1-4-1v-1l-1-1z" class="i"></path><path d="M486 727h4c1-1 2-2 3-2 1-1 0 0 2-1v2l2 1c-2 1-3 1-5 1h-5l-1-1z" class="U"></path><path d="M606 683l1 1c0-4 0-8 1-12h1c0 3 0 6 1 8l-1 1v3h2l3-1h15 8 2l-2-2c0-1 0-1 1-2 0 1 0 1 1 1v-4h1c0 1 0 6 1 8h-1v7l2 1v1l-1 1c0 2 1 5-1 7h-1l-1 1c-2 5-13 32-16 34h-2v-3h-2-8c-5-1-10 0-14 0h-6v3c0 2 0 3-1 4h-6c-1 0-2-1-3-1l-6-5c-2 0-3-1-4-2h-4v-1h5c1 1 1 2 3 2-2-2-2-3-2-6h1s1 1 1 2v1h1c0 1 0 1 1 2 1-1 1 0 3-1l-1-1h-1l1-1-3-3h0 1c1 1 1 0 3 1 1 0 2 1 3 2v-1c0-2 1-4 0-6 0-1 1-2 1-3l1 1v-3-2c-1-1-1-2-1-4l6 1v-1h0c-2 0-5 0-7-1v-1h17 8l-1-1v-4l-1 1v3h-1v-2-5h2l-1 1h1v-1l-1-1h1l1-1v-2c-1-2-1-3-1-4-1-2 0-2 0-3 0 0 0-1-1-1 1-2 1-4 1-6z" class="I"></path><path d="M639 699c2-1 0-5 2-7l1 1-1 1c0 2 1 5-1 7h-1l-1 1c-1-1-1-1-2-1-1 2-3 5-4 8 0 3-1 6-3 9v-8h0v-1c1 0 1-1 2-2-1-1-2-4-2-6 0 0 0-1 1-1h1c2 0 6 1 8 0v-1z" class="l"></path><path d="M623 711c2-1 3 0 5-1h0 1v8c0 1 0 2-1 2-2 3-3 5-4 8l-1 1h-1-1c0-1 0-1-1-1l-1-1-3-7-2-5c2 1 3 3 4 6 0 1 1 1 1 2 0-1 1-2 1-2l1-1h1c1-2 1-5 1-7h0v-2z" class="T"></path><path d="M623 711c2-1 3 0 5-1h0 1v8c0 1 0 2-1 2l-1-1 1-1c0-1 1-4 0-6-2 0-3 0-5 1v-2z" class="K"></path><path d="M627 719l1 1c-2 3-3 5-4 8l-2-2c0-1-1-4 0-5l1-1c2-1 3-1 4-1z" class="U"></path><path d="M612 700h11l-1 1 1 1v1c1 3 0 5 0 8v2h0c0 2 0 5-1 7h-1l-1 1s-1 1-1 2c0-1-1-1-1-2-1-3-2-5-4-6l-3-8v-2l-1-1 1-1c1-1 1-2 1-3z" class="k"></path><path d="M623 713c-1-2-1-1-2-2v-6-1h1l1-1c1 3 0 5 0 8v2h0z" class="d"></path><path d="M618 704h1c0 3 1 6 1 10v5l1 1-1 1c0-1 0-2-1-3v-2l-2-2 1-2c0-2-1-5 0-8z" class="l"></path><path d="M618 712h-2c-1-1-1-1-1-2-1-1 0-2 0-3 1-1 1-2 1-3s0-2 1-3h1c-1 2-1 1 0 3-1 3 0 6 0 8z" class="X"></path><path d="M637 681c0-1 0-1 1-2 0 1 0 1 1 1v-4h1c0 1 0 6 1 8h-1v7l2 1v1l-1-1c-2 2 0 6-2 7v1c-2 1-6 0-8 0h-1c-1 0-1 1-1 1 0 2 1 5 2 6-1 1-1 2-2 2v1h0-1 0c-2 1-3 0-5 1 0-3 1-5 0-8v-1l-1-1 1-1h-11-2v-1c-1 0 0 0-1-1 0-1 0-1 2-1v-1h-2v-2h-1v-1l1-1c6 2 14 1 21 1 1-1 1-1 2-1 1 1 4 1 5 1s2-1 2-1v-1h-1l-2-1c1-2 0-4 1-7h2l-2-2z" class="m"></path><path d="M610 699h20c3 0 7-1 9 0v1c-2 1-6 0-8 0h-1c-1 0-1 1-1 1 0 2 1 5 2 6-1 1-1 2-2 2v1h0-1 0c-2 1-3 0-5 1 0-3 1-5 0-8v-1l-1-1 1-1h-11-2v-1z" class="r"></path><path d="M606 683l1 1c0-4 0-8 1-12h1c0 3 0 6 1 8l-1 1v3h2l3-1h15 8c-1 3 0 5-1 7l2 1h1v1s-1 1-2 1-4 0-5-1c-1 0-1 0-2 1-7 0-15 1-21-1l-1 1v1h1v2h2v1c-2 0-2 0-2 1 1 1 0 1 1 1v1h2c0 1 0 2-1 3l-1 1 1 1v2c0 1 0 2-1 2l1 1c-1 0-2 1-2 2h-4-15-1 0v-1h0c-2 0-5 0-7-1v-1h17 8l-1-1v-4l-1 1v3h-1v-2-5h2l-1 1h1v-1l-1-1h1l1-1v-2c-1-2-1-3-1-4-1-2 0-2 0-3 0 0 0-1-1-1 1-2 1-4 1-6z" class="j"></path><path d="M609 684h2c1 0 2 1 2 1 1 0 4-1 4 1v3h0-1-1l-1 1c-1-2 0 0-2-1h-1c-1 0-1 1-2 0v-1h1 1v-2h-2v-2z" class="U"></path><path d="M614 683h15 8c-1 3 0 5-1 7h-1c0-1-1-1-1-2l-1 1h-3c0-1 0-1-1-1 1-2 1-3 1-4h-1v5c-4 0-7-1-10 0h-2v-3c0-2-3-1-4-1 0 0-1-1-2-1l3-1z" class="Y"></path><path d="M611 707l3 8 2 5 3 7 1 1 2 6v2h-2v-3h-2-8c-5-1-10 0-14 0h-6v3c0 2 0 3-1 4h-6c-1 0-2-1-3-1l-6-5c-2 0-3-1-4-2h-4v-1h5c1 1 1 2 3 2-2-2-2-3-2-6h1s1 1 1 2v1h1c0 1 0 1 1 2 1-1 1 0 3-1l-1-1h-1l1-1-3-3h0 1c1 1 1 0 3 1 1 0 2 1 3 2v-1c0-2 1-4 0-6 0-1 1-2 1-3l1 1v-3-2c-1-1-1-2-1-4l6 1h0 1 15 4c0-1 1-2 2-2l-1-1c1 0 1-1 1-2z" class="u"></path><path d="M617 728l1-1h0v-1c-1 0-1-1-2-1v-5l3 7c0 1-1 1-2 2v-1z" class="S"></path><path d="M584 720l1 1h0v-4l1 1h1l1-1 1 1v-1l1-2h0l-1 7c1 0 1 1 1 1-1 2-1 3 0 5h4 13c2 1 5 0 8 0h2v1c1-1 2-1 2-2l1 1 2 6v2h-2v-3h-2-8c-5-1-10 0-14 0h-6v3c0 2 0 3-1 4h-6c-1 0-2-1-3-1l-6-5c-2 0-3-1-4-2h-4v-1h5c1 1 1 2 3 2-2-2-2-3-2-6h1s1 1 1 2v1h1c0 1 0 1 1 2 1-1 1 0 3-1l-1-1h-1l1-1-3-3h0 1c1 1 1 0 3 1 1 0 2 1 3 2v-1c0-2 1-4 0-6 0-1 1-2 1-3l1 1z" class="n"></path><path d="M574 734c1 0 3 1 5 1l-1-1v-1h2c1 2 1 4 0 5v1l-6-5z" class="J"></path><path d="M619 727l1 1 2 6v2h-2v-3h-2l1-1h0l-1-1c-2 1-3 1-4 1l-1-1c2 0 4 1 5-1l-1-1c1-1 2-1 2-2z" class="Y"></path><path d="M454 725c1 0 3 1 4 1h0c-1-2-3-3-5-5h-1l1-1 10 6c1 0 1 1 1 1 2 0 4 0 6 1v1l1 1h3l2 1-1 1v4h-3l-1 1h-1l-2 2v1 5c-1-1-2-2-4-2v5h-2v1 1h-3l-1 1h0l-1 1h0v2c1 1 5 1 8 1v1c0 1-1 2-2 2h-1-1-1 0c0 1 0 2-1 3l1 2c0 2 0 5-1 7h0c2 3 1 9 1 12-1 1-1 1-2 1s-1 0-2 1h5 2l1 1h0-4-2-3 0l-1 1-1-1c-6-1-12 0-17 0-8 0-17-1-25 0l1-1c-1-1-1-2-1-3v-5c0-1 1-2 1-3v-2-16-1h-3l2-1v-1h7l-1-2 2 1c1-1 1 0 1-1l1-1v2l3-2c1 1 1 1 2 1h2 0 1c0-2 0-2 1-4v2l1-1c1-1 1-1 1-2l2 2v1l-1 1 1 1 1 2h0-1v1h1v-1c1-1 0-3 1-4l1-3 1-2h0c2-1 3-3 4-4l1-2c2 0 2 0 2-1v-1l-2 1c-1-1-1-2-2-3 0 1-1 1-2 2h0l2-2 2-2c1-2 0-1-1-2v-2l5-4c1 0 1 0 2-1h1l4 3z" class="R"></path><path d="M444 763c0-1 0-1-1-2h0c0-2-1-7 1-8v1h2c1 1 1 2 2 3-1 2-1 3-1 5h0-2v1l-1 2h0v-1-1z" class="v"></path><path d="M448 757v1c0 1 0 2 1 4 1 0 2-1 3-1l-1 2c0 1 1 2 1 3 1 0 1-1 1-1h1v3c-1 0-1 0-2 1h-2v4h-4l-2 2-1-2c0-2 0-4 1-5h0c2 0 2 0 4-1l-1-1h-3 0-1v-2-2l1 1v1 1h0l1-2v-1h2 0c0-2 0-3 1-5z" class="Z"></path><path d="M447 723v4l3 3h0-1l-1 1c0 1 0 1 1 3h0c-1 1-1 0 0 1 0 1 0 1 1 1h1v1h0c-1 1-1 2-1 3-1 1-1 1-1 2-1-1-1-2-1-3h1v-2l-1-1c-1 2 0 4-1 5 0 1-1 2-2 2h0c-2 1-3 1-4 2l1-5v-1l1-2c2 0 2 0 2-1v-1l-2 1c-1-1-1-2-2-3 0 1-1 1-2 2h0l2-2 2-2c1-2 0-1-1-2v-2l5-4z" class="S"></path><path d="M456 751l1 1h0v2c1 1 5 1 8 1v1c0 1-1 2-2 2h-1-1-1 0c0 1 0 2-1 3l1 2c0 2 0 5-1 7h0c0 1-1 3-2 3l-1-2c-2 2-2 3-3 4v-2h-1l-1 1-1-1h0v-4h2c1-1 1-1 2-1v-3h-1s0 1-1 1c0-1-1-2-1-3l1-2c2 1 3 0 4 0v-6l-1-1c0-1 0-2 1-3z" class="U"></path><path d="M459 770c-1 0-1-1-2-2 2-3 0-8 2-10h1c0 1 0 2-1 3l1 2c0 2 0 5-1 7z" class="g"></path><path d="M442 739v1l-1 5c-1 5 0 13 1 19h1v2h1 0 3l1 1c-2 1-2 1-4 1h0c-1 1-1 3-1 5l1 2h-3v-5l-1-1v4l-1 1h-2v1l-1-1c-1-1-1-1-1-2v-19-1c1-1 0-3 1-4l1-3 1-2h0c2-1 3-3 4-4z" class="j"></path><path d="M442 739v1c-1 1-1 2-1 3h-1c-1 4 0 9 0 12v8c0 1-1 5 0 6v4l-1-3c-2-6 0-14-1-21l-1-1 2-2-2-1 1-2h0c2-1 3-3 4-4z" class="M"></path><path d="M440 755v8l-1 1h0c0-2-1-5 0-7 0 0 1-1 1-2z" class="R"></path><path d="M437 745l2 1-2 2 1 1c1 7-1 15 1 21l1 3-1 1h-2v1l-1-1c-1-1-1-1-1-2v-19-1c1-1 0-3 1-4l1-3z" class="u"></path><path d="M454 725c1 0 3 1 4 1h0c-1-2-3-3-5-5h-1l1-1 10 6c1 0 1 1 1 1 2 0 4 0 6 1v1l1 1h3l2 1-1 1v4h-3l-1 1h-1l-2 2v1 5c-1-1-2-2-4-2v5h-2v1 1h-3l-1 1h0l-1 1-1-1c0-2 1-2 2-3v-1-2-1-2h0l1-1c-1-2-1-4 0-5v-1h-1-1-2v2c-1-1-1 0-2-1h0l-2 1v-1h-1c-1 0-1 0-1-1-1-1-1 0 0-1h0c-1-2-1-2-1-3l1-1h1 0l-3-3v-4c1 0 1 0 2-1h1l4 3z" class="Z"></path><path d="M464 727c2 0 4 0 6 1v1c-1 1-2 1-3 1-2-1-2-1-3-3z" class="M"></path><path d="M464 743v-1-2c0-1 1-1 1-1h3v1 5c-1-1-2-2-4-2z" class="r"></path><path d="M468 739c0-2-2-6-1-8h1c1 0 2 2 2 2 1 0 1-1 3-1-1 1-1 2-1 4h0l-1 1h-1l-2 2v1-1z" class="U"></path><path d="M447 723c1 0 1 0 2-1h1l4 3c2 1 4 3 5 5l-1 1h-2l-1 1 1 1c1-1 1-1 2-1 0 1-1 1-2 2h-1l-1-1v2h-1v1l-2 1v-1h-1c-1 0-1 0-1-1-1-1-1 0 0-1h0c-1-2-1-2-1-3l1-1h1 0l-3-3v-4z" class="u"></path><path d="M432 745l2 2v1l-1 1 1 1 1 2h0-1v1h1v19c0 1 0 1 1 2l1 1v-1h2l1-1v-4l1 1v5h3l2-2h4 0l1 1 1-1h1v2c1-1 1-2 3-4l1 2c1 0 2-2 2-3 2 3 1 9 1 12-1 1-1 1-2 1s-1 0-2 1h5 2l1 1h0-4-2-3 0l-1 1-1-1c-6-1-12 0-17 0-8 0-17-1-25 0l1-1c-1-1-1-2-1-3v-5c0-1 1-2 1-3v-2-16-1h-3l2-1v-1h7l-1-2 2 1c1-1 1 0 1-1l1-1v2l3-2c1 1 1 1 2 1h2 0 1c0-2 0-2 1-4v2l1-1c1-1 1-1 1-2z" class="d"></path><path d="M432 745l2 2v1l-1 1 1 1c-2-1-2 0-4 0v-2l1-1c1-1 1-1 1-2z" class="Y"></path><path d="M429 750v3 16 4c0 1-1 1-1 2l-3-2 1-1v-3-9c-1-2 0-6 0-8l2-2h0 1z" class="S"></path><path d="M429 750v3c-3 5 1 12-1 17-1-1-1-2-1-3 0-2 0-5-1-7s0-6 0-8l2-2h0 1zm5 3v14c0 2 1 5 0 7h-4l1-22c0-1 0-1 2-2l1 2v1z" class="M"></path><path d="M420 750l1-1v2l3-2c1 1 1 1 2 1l-1 1c0 8 0 15-1 23 0-1-1 0-1-1-1-1-2-1-3-1v-1l-1-1v-17l-1-1-1-2 2 1c1-1 1 0 1-1z" class="S"></path><path d="M420 750l1-1v2l3-2c1 1 1 1 2 1l-1 1c-2 1-3 1-4 2-2 6-1 12-1 18l-1-1v-17l-1-1-1-2 2 1c1-1 1 0 1-1z" class="O"></path><path d="M411 752h7l1 1v17l1 1v1c1 0 2 0 3 1h-5l-1 6h1l1-1 2 1h1l-1-1c0-1 0-2 1-3h0v4h1c1 0 0 0 1-1l2 3h-3 0-1-1c0-1 0-1-1-1l-1 1h-1l-2 1v-3l1-1c0-2 0-4-1-5h-4v-2-16-1h-3l2-1v-1z" class="M"></path><path d="M412 755h3l1 1h1v3 12c-1 0-4 1-5 0h0v-16z" class="n"></path><path d="M416 756h1v3l-2 2h-1v-2c0-2 1-2 2-3z" class="X"></path><path d="M412 773h4c1 1 1 3 1 5l-1 1v3l2-1h1l1-1c1 0 1 0 1 1h1 1 0 3l1 1 1-1h3v1h0l2-1v1h1l1-1v1 1l2-2 1 1c2 1 6 0 9 0 1 0 2 1 3 0 3 0 6 1 8 1-1 0-1 0-2 1h5 2l1 1h0-4-2-3 0l-1 1-1-1c-6-1-12 0-17 0-8 0-17-1-25 0l1-1c-1-1-1-2-1-3v-5c0-1 1-2 1-3z" class="e"></path><path d="M416 782l2-1h1l1-1c1 0 1 0 1 1h1 1 0 3l1 1 1-1h3v1h0l2-1v1h1l1-1v1 1l2-2 1 1c2 1 6 0 9 0 1 0 2 1 3 0 3 0 6 1 8 1-1 0-1 0-2 1h-29c-3 0-8 1-11-1v-1z" class="T"></path><path d="M513 729c2 0 2 0 3 1-1 1-3 0-4 1h4l-1 1c2 0 3 0 5 2v1c1 2 1 1 2 2l3 4 1 1c3 4 3 6 4 10l-1 1v2c0 1 0 1 1 1 0 2 0 3 1 4-1 1-1 1-1 2v1h-2l-1-1c0 3-1 10 1 12h-2c0 1-1 1-2 1v1h0-2v7h-48v-3l2-2h2 1l-1-3h-2-2l-1-8-2-2v-2-2c0-1-2-1-2-1h-1l-1-1c0-2 0-2-2-3v-1c-3 0-7 0-8-1v-2h0l1-1h0l1-1h3v-1-1h2v-5c2 0 3 1 4 2v-5-1l2-2h1l1-1h3v-4l1-1-2-1h17c3 0 6-1 9-1h13z" class="g"></path><path d="M494 771c1-1 3-1 4-2 0 2-1 4 0 6h-1l-1-1h-1v1h-1v-1c1-1 1-2 0-3h0z" class="S"></path><path d="M481 765c1 1 2 2 4 1l-1 1 1 8h-5v-1c0-2 0-4 1-6v-3z" class="U"></path><path d="M497 746l2-2v2l1 1 1-1 1 1h-1c0 2 0 2 1 4v3 8 2 2l1 6v2 3h-1v-1c-1-1-2-1-3-1v-5l-1-1h0c-1 1-3 1-4 2 0-2 0-7 1-9 0 0 2-1 2-2v-2c0-2 0-7 1-8v-1l-1-3z" class="u"></path><path d="M498 769c-1-4 0-7 0-11v-5-3l1-1h1v3c-1 2-1 5-1 8l2-2v2 3l1 1v2l1 6v2 3h-1v-1c-1-1-2-1-3-1v-5l-1-1z" class="R"></path><path d="M499 760l2-2v2 3l1 1v2l-2 1c0 1 0 1-1 2v-9z" class="S"></path><path d="M518 753l1-1c1 0 2 1 3 1h1c0 1 1 1 1 1 1 3-2 6 1 8l1 1c0 3-1 8 0 11 0 1-1 1-2 1-1-1-5 0-6 0v-6c1-1 1-1 1-2-1 0-1 0-1-1 0-2-1-3 0-4v-9z" class="R"></path><path d="M505 760v-8h12v1 7c-2 0-4-1-5 0 1 1 2 1 4 1v1c0 4 0 8-1 12-1 1-1 1-2 0v-1l-1-1c-1 1-1 1-1 3h-1c-1-1-3-1-5 0-1-5 0-11 0-15z" class="v"></path><path d="M506 737h1v-1c-1 1-1 0-2 0v-1h2l1-1c4 0 8 0 12 1 1 2 1 1 2 2l3 4 1 1c3 4 3 6 4 10l-1 1v2c0 1 0 1 1 1 0 2 0 3 1 4-1 1-1 1-1 2v1h-2l-1-1c0 3-1 10 1 12h-2c-1-3 0-8 0-11l-1-1c-3-2 0-5-1-8 0 0-1 0-1-1h-1c-1 0-2-1-3-1l-1 1h-1v-1h-12v8h-1l-2 2v-8-3c-1-2-1-2-1-4h1l-1-1v-2l2-1c1-2 1-4 3-6z" class="o"></path><path d="M519 740l2 1v2h1v2h-1-1v-1l-1 1h-1c0-3 0-3 1-5z" class="p"></path><path d="M522 752v1h3v2c0 3 1 5 1 8l-1-1c-3-2 0-5-1-8 0 0-1 0-1-1h-1v-1z" class="u"></path><path d="M522 743h1 1l3 4 1-1h0c-1-2-1-2-2-3v-1c3 4 3 6 4 10l-1 1v2c0 1 0 1 1 1 0 2 0 3 1 4-1 1-1 1-1 2v1h-2l-1-1c0-3 0-7-1-11v-4c-1-2-2-3-4-4z" class="p"></path><path d="M506 737h1v-1c-1 1-1 0-2 0v-1h2l1-1c4 0 8 0 12 1 1 2 1 1 2 2l3 4 1 1v1c1 1 1 1 2 3h0l-1 1-3-4h-1-1-1v-2l-2-1v-1l-4 2c0 1 0 2-1 3h-1c-2-1-2-2-3-3h-1v1 1l-2 1-1 1v-8z" class="R"></path><path d="M503 743c1-2 1-4 3-6v8l1-1 2 2 1-1 1 1h-1l-2 2h1c1-1 2-1 2-2h1v1 1h2 3 1c1 0 1 1 2 1h1c0 1 1 1 1 2h-1l1 1v1c-1 0-2-1-3-1l-1 1h-1v-1h-12v8h-1l-2 2v-8-3c-1-2-1-2-1-4h1l-1-1v-2l2-1z" class="j"></path><g class="S"><path d="M501 746v-2l2-1v5h0c0 2 0 4-1 6v-3c-1-2-1-2-1-4h1l-1-1z"></path><path d="M513 729c2 0 2 0 3 1-1 1-3 0-4 1h4l-1 1c2 0 3 0 5 2v1c-4-1-8-1-12-1l-1 1h-2v1c1 0 1 1 2 0v1h-1c-2 2-2 4-3 6l-2 1v2l-1 1-1-1v-2l-2 2 1 3v1c-1 1-1 6-1 8v2c0 1-2 2-2 2-1-2-1-2 0-4 0-1 0-1-1-2-1 1-1 4-1 5v8c0 1-1 3 0 5l-1 1h-1-1c-4 0-1-2-2-4-1 2-1 5-1 7-1-2-1-4-1-6v-6l-2 1 1-1c-2 1-3 0-4-1v3c-1 2-1 4-1 6v1h-2-2-2l-1-8-2-2v-2-2c0-1-2-1-2-1h-1l-1-1c0-2 0-2-2-3v-1c-3 0-7 0-8-1v-2h0l1-1h0l1-1h3v-1-1h2v-5c2 0 3 1 4 2v-5-1l2-2h1l1-1h3v-4l1-1-2-1h17c3 0 6-1 9-1h13z"></path></g><path d="M483 743s0-1-1-1c0-3 0-7 1-9h1v5c-1 1-1 1-1 3h1l-1 2z" class="p"></path><path d="M489 761h-1 0v-1c-1-2 0-5 0-6h2 1c1 1 0 2 0 4l-2 2v1z" class="R"></path><path d="M476 750c1 1 1 3 1 5h-3-1v-2-4c1 0 2 0 3 1z" class="U"></path><path d="M489 760c2 1 2 1 4 1v8c0 1-1 3 0 5l-1 1h-1v-1c1-2 1-4 0-6v-2c1-2 1-2 0-4h-1l-1-1v-1z" class="M"></path><path d="M513 729c2 0 2 0 3 1-1 1-3 0-4 1h-36l-2-1h17c3 0 6-1 9-1h13z" class="b"></path><path d="M484 741h1v12c0 2 0 3 1 5v4c0 1 1 3 0 4l-2 1 1-1c-2 1-3 0-4-1h0v-4c0-1 1-1 1-2v-1c1-1 1-1 1-2h0l1 1v-1c-1-3 1-7 0-10-1-1-1-2-1-3l1-2z" class="M"></path><path d="M481 765l1-1v-1h2c0 1 0 2 1 3-2 1-3 0-4-1h0z" class="p"></path><path d="M465 755h3v1h3c1 1 3 2 3 3s1 1 1 2 0 3-1 5h2l-1 1v1h1c0-3 0 1 0-2v-1-5h1c1 2 0 5 0 7s-1 4-1 6v2h-2l-1-8-2-2v-2-2c0-1-2-1-2-1h-1l-1-1c0-2 0-2-2-3v-1z" class="O"></path><path d="M467 759c2-1 3-1 5-1 2 2 1 6 1 9l-2-2v-2-2c0-1-2-1-2-1h-1l-1-1z" class="n"></path><path d="M501 737v-2l-1 1-1-1c1-2 1-1 2-1 1-1 1-2 2-2h1 2c1 0 2 0 3 1 1-1 4-1 6-1s3 0 5 2v1c-4-1-8-1-12-1l-1 1h-2v1c1 0 1 1 2 0v1h-1c-2 2-2 4-3 6l-2 1v2l-1 1-1-1v-2l-2 2s0 1-1 2l-1-1c2-3 3-5 6-7-1 0-1-1-2-1l1-2h1z" class="M"></path><path d="M501 737l2-2 1 1-1 2-2 2c-1 0-1-1-2-1l1-2h1z" class="p"></path><path d="M472 736h3c2 4 1 10 1 14-1-1-2-1-3-1v4l-1-1c0-1-1-2-1-2v-1h-3c0-1 1-2 0-3v-1-5-1l2-2h1l1-1z" class="Y"></path><path d="M464 743c2 0 3 1 4 2v1c1 1 0 2 0 3h3v1s1 1 1 2l1 1v2h-5-3c-3 0-7 0-8-1v-2h0l1-1h0l1-1h3v-1-1h2v-5z" class="Q"></path><defs><linearGradient id="J" x1="565.326" y1="748.693" x2="578.674" y2="756.307" xlink:href="#B"><stop offset="0" stop-color="#272b28"></stop><stop offset="1" stop-color="#4d4a43"></stop></linearGradient></defs><path fill="url(#J)" d="M563 729c1-1 5-4 7-5h0l-2 3h0 4c0 3 0 4 2 6-2 0-2-1-3-2h-5v1h4c1 1 2 2 4 2l6 5c1 0 2 1 3 1h6l-1 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1c1 2 3 2 2 4 0 1 0 1-1 2-1 0-1 1-1 2l1 1v9 3c0 2 0 6 1 7h0v1 2l-1 1s0 1-1 2h1v2l-1 2 1 1-1 1v3c-2 1-4 0-6 0v-1l-1-1-1-1h-1c-1 0-2 0-2-1l-1-1v1h-2l2-2h-35v-1h-6-14v-7h2 0v-1c1 0 2 0 2-1h2c-2-2-1-9-1-12l1 1h2v-1c0-1 0-1 1-2-1-1-1-2-1-4-1 0-1 0-1-1v-2l1-1c-1-4-1-6-4-10l-1-1-3-4c-1-1-1 0-2-2v-1c-2-2-3-2-5-2l1-1h27 5 8 6c1 0 1-1 1-2z"></path><path d="M575 761c1 0 1-1 2 0v9l1 2h-2l-1-1v-10z" class="T"></path><path d="M572 742c1 0 3 2 4 4h0 1v3c-1 1 0 2 0 4s1 2 0 4v4c-1-1-1 0-2 0l-1-12c0-3-1-5-2-7z" class="K"></path><path d="M571 773h0c1-2 1-6 1-8 0-5 0-14-2-19h0c-1-2-3-3-2-5 4 6 4 9 5 17v3c0 1 0 3 1 4v8c1 0 1 0 1 2v1 1 1h-1l-1-1c0-1 1-2-1-3l-1-1z" class="g"></path><path d="M578 744c2 2 1 4 2 6 1 5 0 10 0 15v2c0 4 3 12 0 15h0v-1c0-1 0-4-1-5h0c-1-1-1-2 0-3h-1c1-1 0-1 0-2v1l-1-2v-9-4c1-2 0-2 0-4s-1-3 0-4h1v-5z" class="l"></path><path d="M565 740h2l1 1c-1 2 1 3 2 5h0c2 5 2 14 2 19 0 2 0 6-1 8h0c-1 0-2 1-3 1l-1 1v-2-1-1-14c0-3 0-6-1-9h-1c-1-2 0-6 0-8z" class="R"></path><path d="M566 748v-3h1l1 2c1 1 0 3 1 4v5 14l-1 1v3l-1 1v-2-1-1-14c0-3 0-6-1-9z" class="U"></path><path d="M563 729c1 1 2 2 2 3l1 3c0 1 0 3-1 5h0c0 2-1 6 0 8h1c1 3 1 6 1 9v14 1h-2l-1-1v-1l-1-1-1-3v-8h-2c0-1-1-2-1-3 1 0 2 0 3-1v-5l-2-1v-1h1 1v-5-1l-3-1v-5-1-1l-1 1c-1-1-1-2-2-3h6c1 0 1-1 1-2z" class="S"></path><path d="M565 748h1c1 3 1 6 1 9v14 1h-2c2-4 0-11 0-15 1-3 1-6 0-9z" class="O"></path><path d="M563 729c1 1 2 2 2 3l-1 1h-1c-1 1-1 6-1 8l-3-1v-5-1-1l-1 1c-1-1-1-2-2-3h6c1 0 1-1 1-2z" class="i"></path><path d="M548 731h8c1 1 1 2 2 3l1-1v1 1 5l3 1v1 5h-1-1v1l2 1v5c-1 1-2 1-3 1h-4-3c-1 0-2 0-3-1l1-4 1-1-1-1h-1v-9c0-1 0-3 1-4v-1l-2-3z" class="k"></path><path d="M559 735v5l3 1v1l-2-1-1 1v1c1 1 1 1 1 3-1 0-1 1-2 2l-2-1v-6h-1c1-1 2-1 3-2v-2c0-1 0-1 1-2z" class="Q"></path><path d="M560 748l2 1v5c-1 1-2 1-3 1h-4v-1h1v-1l-1-1c0 1-1 1-1 2h0-2c0-1 0-1-1-2h0l2-2c1 0 1 0 2-1h0l5-1z" class="T"></path><path d="M548 731h8c1 1 1 2 2 3l1-1v1 1c-1 1-1 1-1 2-1 0-2 0-3 1-1-1-1-2-1-4l-1 1c0 3-2 9-1 12h2v1l-3 1-1-1h-1v-9c0-1 0-3 1-4v-1l-2-3z" class="R"></path><path d="M558 734l1-1v1 1c-1 1-1 1-1 2-1 0-2 0-3 1 0-1 0-3 1-4 1 1 1 0 2 0z" class="g"></path><path d="M555 755h4c0 1 1 2 1 3h2v8l1 3 1 1v1l1 1h2v1 2l1-1c1 0 2-1 3-1l1 1c2 1 1 2 1 3v3h-1v-3l-1 4-1-1v-3-1h0l-1-1v7c3 0 7-1 10 1-1 0-2 1-2 1h-35v-1h1c1 0 1-1 2-1h2 4v-2c-1-2-1-4-1-7v-2l-1-2v-2c1-2 0-8 1-11l2-1h3z" class="K"></path><path d="M562 766l1 3 1 1v1l1 1h2v1c-2 1-3 0-5 1-1-2 0-6 0-8z" class="Y"></path><path d="M555 755h4c0 1 1 2 1 3h-3l1 1h-2v1l2 1c1-1 1-1 1-3l1 1c0 5 1 11 0 16l-1 5-1-1c-1 0-1 1-1 2-1-1-1-2-2-3h0 0v2l-1 1h-1c0-1 1-2 0-3h0c-1 1-1 2-1 3l-1 1v-2c-1-2-1-4-1-7v-2l-1-2v-2c1-2 0-8 1-11l2-1h3z" class="T"></path><path d="M555 755h4c0 1 1 2 1 3h-3-2c-1 4-1 9 0 13 0 0 0 1-1 2l1 1h-3l-1-1h2v-1h-1v-1-1l1-1v1l-1 1 1 1 1-1c0-3 1-11-1-13l-2 4v5c0 1 0 2-1 4l-1-2v-2c1-2 0-8 1-11l2-1h3z" class="Y"></path><path d="M563 729c1-1 5-4 7-5h0l-2 3h0 4c0 3 0 4 2 6-2 0-2-1-3-2h-5v1h4c1 1 2 2 4 2l6 5c1 0 2 1 3 1h6l-1 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1c1 2 3 2 2 4 0 1 0 1-1 2-1 0-1 1-1 2l1 1v9 3c0 2 0 6 1 7h0v1 2l-1 1s0 1-1 2h1v2l-1 2 1 1-1 1v3c-2 1-4 0-6 0v-1l-1-1-1-1h-1c-1 0-2 0-2-1l-1-1v1h-2l2-2s1-1 2-1l1 1 1-1-1-1c3-3 0-11 0-15v-2c0-5 1-10 0-15-1-2 0-4-2-6v5h-1v-3h-1 0c-1-2-3-4-4-4h-1l-1-3-1-2c-1-1-2-2-3-2l-1-3c0-1-1-2-2-3z" class="K"></path><path d="M584 768c1-1 1-1 1-2 2-2 3-2 5-1v3c0 2 0 6 1 7h0l-2 1c-1-1-1-1-2-1h-1c1-1 0-1 1-1v-2c-1-1-1-2-3-2v-1h1l-1-1z" class="G"></path><path d="M584 754l1 2c1 0 2 0 4-1l1 1v9c-2-1-3-1-5 1 0 1 0 1-1 2 1-2 1-3 0-4 0-1 1-1 1-2s-1-3-1-4 0-2-1-2v-1l1-1z" class="E"></path><path d="M581 783h9l-1 2 1 1-1 1v3c-2 1-4 0-6 0v-1l-1-1-1-1h-1c-1 0-2 0-2-1l-1-1v1h-2l2-2s1-1 2-1l1 1 1-1z" class="O"></path><path d="M583 789c1-1 1-2 2-3 1 0 3 0 4 1v3c-2 1-4 0-6 0v-1z" class="m"></path><path d="M563 729c1-1 5-4 7-5h0l-2 3h0 4c0 3 0 4 2 6-2 0-2-1-3-2h-5v1h4c1 1 2 2 4 2l6 5c1 0 2 1 3 1h6l-1 1c0 1 1 1 1 2 1 1 1 2 1 3l-1 1c1 2 3 2 2 4 0 1 0 1-1 2-1 0-1 1-1 2-2 1-3 1-4 1l-1-2-1 1v1c1 0 1 1 1 2-2 6 0 13-1 20 0 1 0 2 1 3l1-1v-2c1 1 1 2 1 3-1 1-2 1-3 0-2-2-1-8-1-12 0-2-1-6 0-8h0c-1-3-1-8 0-11h0c0-2 0-4-1-5l1-1v-1-2h-1c-1 0-2-1-2-1-1 0-1 0-2-1l-1-1c0 1 0 1-1 1-1-1-2-2-3-2h0c0 2 1 2 2 3 1 2 3 3 4 4v5h-1v-3h-1 0c-1-2-3-4-4-4h-1l-1-3-1-2c-1-1-2-2-3-2l-1-3c0-1-1-2-2-3z" class="O"></path><path d="M584 754v-2h2l1-2 1 1s1 1 2 1v1c-1 0-1 1-1 2-2 1-3 1-4 1l-1-2z" class="M"></path><path d="M516 731h27 5l2 3v1c-1 1-1 3-1 4v9h1l1 1-1 1-1 4c1 1 2 1 3 1l-2 1c-1 3 0 9-1 11v2l1 2v2c0 3 0 5 1 7v2h-4-2c-1 0-1 1-2 1h-1-6-14v-7h2 0v-1c1 0 2 0 2-1h2c-2-2-1-9-1-12l1 1h2v-1c0-1 0-1 1-2-1-1-1-2-1-4-1 0-1 0-1-1v-2l1-1c-1-4-1-6-4-10l-1-1-3-4c-1-1-1 0-2-2v-1c-2-2-3-2-5-2l1-1z" class="p"></path><path d="M522 737c2 0 2 1 3 2l4 5v-4l1-1c1 2 0 6 0 8h1v-9h1c1 1 0 7 1 10v2c1 3 0 9 0 12l-1 2c0-3 1-8-1-9h-2 0v-2l1-1c-1-4-1-6-4-10l-1-1-3-4z" class="Y"></path><path d="M533 762h2c-1-2-1-6-1-9v-4c3 3 1 9 2 13h1v-2l-1-1c1-7-1-14 0-21 1-2 1-3 2-4v6c-1 3-1 6 0 10-1 3 0 6 0 9v9 7l-1 1v2h-2-1l-1 1v-1c-2-3 0-8-1-11h0v-2-1l1-2z" class="M"></path><path d="M529 755h0 2c2 1 1 6 1 9v1 2h0c1 3-1 8 1 11v1l1-1h1 2c0 1 0 4-1 5h-14v-7h2 0v-1c1 0 2 0 2-1h2c-2-2-1-9-1-12l1 1h2v-1c0-1 0-1 1-2-1-1-1-2-1-4-1 0-1 0-1-1z" class="U"></path><path d="M543 731h5l2 3v1c-1 1-1 3-1 4v9h1l1 1-1 1-1 4c1 1 2 1 3 1l-2 1c-1 3 0 9-1 11v2l1 2v2c0 3 0 5 1 7v2h-4-2c-1 0-1 1-2 1h-1-6c1-1 1-4 1-5v-2l1-1v-7-9c0-3-1-6 0-9v-4h0v-1c1 4 1 7 2 10 2-4-1-10 2-14v1h0c0-2 0-3 1-5v-3-1-2z" class="v"></path><path d="M547 757c1 0 2 0 3-1-1 3 0 9-1 11v2l1 2v2l-3 1c1-3 0-5 0-8v-9z" class="U"></path><path d="M543 731h5l2 3v1c-1 1-1 3-1 4v9h1l1 1-1 1-1 4c1 1 2 1 3 1l-2 1c-1 1-2 1-3 1v-7-1l-1-1h-2c-2 0 0 4-1 6l-1-1v-11h0c0-2 0-3 1-5v-3-1-2z" class="S"></path><path d="M540 766l2-1v4h0v4h1v-2h1v2h1c1 0 1 0 1 1h1l3-1c0 3 0 5 1 7v2h-4-2c-1 0-1 1-2 1h-1-6c1-1 1-4 1-5v-2l1-1v-7l1-2h1z" class="Y"></path><path d="M538 768l1-2h1c-1 2-1 4 0 6l1 1h-2v2h-1v-7z" class="u"></path><path d="M465 756c2 1 2 1 2 3l1 1h1s2 0 2 1v2 2l2 2 1 8h2 2l1 3h-1-2l-2 2v3h48 14 6v1h35l-2 2h2v2h-1c-1 1-1 2-1 3h-1v-2l-1 2h-1v-1l-1-1h-1-2c-1 0-2 1-2 1-2 0-2-1-3-2h-1c0 1 0 3-1 4h-8v1c-1 0-4-1-5 0-1 0-1 0-2 1h-30-1c1 1 2 1 3 1h-35v4c1 1 2 7 2 9v5c1 1 1 2 1 3l1-1h0v-3c0-4 2-9 6-12-1 2-2 3-3 5s-2 4-2 7l1-1c1 2 1 4 1 7 0 1 1 2 1 3v4l2 2h0 1c1 2 2 6 1 8v1c-2 3 0 5-3 7h-1v-3l-1 1h0c0 1-1 2-2 3h6c-2 1-5 1-7 1-4-1-9 0-13-1 0-1-1-1-1-2v-2l-1-1c-2 0-2 0-2 2 0 1 0 0 1 1l1 1v1c-3 1-5 1-8 0h-5c-3 2-4 2-7 1h-2 0c-3-1-5 0-7 0 2 0 4 1 6 1 1 0 1 0 1 1l-9-1c-1 0-3 0-4-1l1-2v-7h-1c-2 1-3 1-5 1h-1l-1 2c-1 0-1-1-2 0h0c-1 1 0 1-1 2v-5h-1v6l-1 1c0-1 0-3-1-4h-5c-2-1-2-2-2-3l-1-1v-2l-1 1c0 1 0 1 1 2v1h-2v-3h-1c0 1 0 1-1 1l-1-1-2 1v-1h-2l-1-1v-1h0l-1 1h-1 0c-1-1-1-1-2-1 1-1 2-4 3-6 1-1 0-1 1-2l1-1c2-3 2-4 5-5 0-1 0-2 1-3l-1-4h6c2-2 0-5 1-7 0-1 1-1 1-2l-1-1 1-1c-1-2-1-3-1-5v-2c0-1-1-3-2-4l-1-1c-2 0-3 1-4 2l-1-1v-3-1c8-1 17 0 25 0 5 0 11-1 17 0l1 1 1-1h0 3 2 4 0l-1-1h-2-5c1-1 1-1 2-1s1 0 2-1c0-3 1-9-1-12h0c1-2 1-5 1-7l-1-2c1-1 1-2 1-3h0 1 1 1c1 0 2-1 2-2z" class="g"></path><path d="M411 786h6v3l-1-1c-2 0-3 1-4 2l-1-1v-3z" class="M"></path><path d="M453 798c2 0 4 0 6-1h0c1 1 1 1 1 2v1l1 2-2 1h-2v-3l1-1h0c-5-1-11 0-16 0-2-2-4 0-6-1h17z" class="e"></path><path d="M417 786c1 1 3 1 4 1s3 3 4 4c2 2 3 3 3 6h0-1v1c-2-2-4-4-6-5h-2c0-1-1-3-2-4v-3z" class="R"></path><path d="M455 785l1 2h-1v2c-3 1-8 0-11 0h-8-2c-1 0-1 0-2-1l-1 1c-3 0-7-1-10-2h20c3 0 7 0 9-1h4l1-1z" class="U"></path><path d="M419 793h2c2 1 4 3 6 5l-2 1c1 0 2 1 3 2 0 1 0 2-1 3v-4l-1 1v3c-1 0-1-1-1-1-1 1-1 1-1 2 1 1 2 1 3 1v3h-3-2 0c-1-1-1-2-1-3l1-1 1-1-1-2v-1l-2 1-1-1 1-1c-1-2-1-3-1-5v-2z" class="u"></path><path d="M419 793h2c2 1 4 3 6 5l-2 1-1-2-2 2c0 1 0 0-1 1l-1-1 1-1c0-1-1-1-1-2l-1-1v-2z" class="o"></path><path d="M435 813v-1h0-3l-2-2h-1-1v-2l1 1h1v-2h1v2c0 1 0 1 1 2h3 0v-1c0-1-1-2-1-3s0-2-1-3-1-2-2-3h0-1v2 1l-1-1v-2-1h7 7c2 4 2 8 2 12v2 9l1 14v5h1 4l1 1h-1l-1 2h0c-3-1-5 0-7 0 2 0 4 1 6 1 1 0 1 0 1 1l-9-1c-1 0-3 0-4-1l1-2h1c1-1 2-1 3-1h2v-1-2c1-1 1-1 0-2-1-7 2-17-2-23l-4-2c-1 0-2 0-3 1z" class="p"></path><path d="M442 814l-2-2c-1-1-1-1-1-2s0-1-1-1c0-1-1-2-1-3v-1c1 1 2 2 3 4 0 1 0 1 1 2l1 1 1 2c1-1 0-4-1-6 0-2 0-5-1-7h1l3 13v9l1 14v5h1 4l1 1h-1l-1 2h0c-3-1-5 0-7 0 2 0 4 1 6 1 1 0 1 0 1 1l-9-1c-1 0-3 0-4-1l1-2h1c1-1 2-1 3-1h2v-1-2c1-1 1-1 0-2-1-7 2-17-2-23z" class="O"></path><path d="M443 800h9 5v3l-1 1h-1v-2h0c-1 1-1 2-1 3s0 1 1 3h0l2 1h0c0 1 0 1 1 2v1 1 17c0 3 0 6-1 9 1 1 1 1 2 1-1 1-1 2-2 1h-1-1c-1 1-1 1-2 1l-1 1-1-1h-4-1v-5l-1-14v-9-2c0-4 0-8-2-12z" class="v"></path><path d="M452 800h5v3l-1 1h-1v-2h0c-1 1-1 2-1 3s0 1 1 3h0l2 1h0c0 1 0 1 1 2v1 1-1l-1-1h-1v1 2h-1l-1-1c-1 1 0 3 0 4v12 1 2 2h-1c-1 2 0 6 0 8l-1 1-1-1v-1-1-5c1-4 1-8 1-12v-5c-1-2-1-5-1-7 0-4 1-7 1-11z" class="S"></path><path d="M453 842c0-2-1-6 0-8h1v-2-2-1-12c0-1-1-3 0-4l1 1h1v-2-1h1l1 1v1 17c0 3 0 6-1 9 1 1 1 1 2 1-1 1-1 2-2 1h-1-1c-1 1-1 1-2 1z" class="U"></path><path d="M420 802l2-1v1l1 2-1 1-1 1c0 1 0 2 1 3h0c0 1-1 1 0 2v1h0c-1 1-1 2-1 3v2c-1 1-1 3-1 4h2c0-2 0-4 1-7l2-1h3l4 2h1c0-1 0-1 1-2v1l-1 1c1 1 0 1 1 3h1v-5c1-1 2-1 3-1l4 2c4 6 1 16 2 23 1 1 1 1 0 2v2 1h-2c-1 0-2 0-3 1h-1v-7h-1c-2 1-3 1-5 1h-1l-1 2c-1 0-1-1-2 0h0c-1 1 0 1-1 2v-5h-1v6l-1 1c0-1 0-3-1-4h-5c-2-1-2-2-2-3l-1-1v-2l-1 1c0 1 0 1 1 2v1h-2v-3h-1c0 1 0 1-1 1l-1-1-2 1v-1h-2l-1-1v-1h0l-1 1h-1 0c-1-1-1-1-2-1 1-1 2-4 3-6 1-1 0-1 1-2l1-1c2-3 2-4 5-5 0-1 0-2 1-3l-1-4h6c2-2 0-5 1-7 0-1 1-1 1-2z" class="M"></path><path d="M413 814l1-1c1 0 1 0 2 1 1 0 1-1 2-1v2c0 1 0 2 1 3 1 2 0 5 0 7 1 3 0 9 0 11l-1 1c-1-1 0-3-1-4v-2-8-5c-1-2-1-1-3-2 0-1 0-1-1-2z" class="g"></path><path d="M423 814l2-1h3l4 2c0 1-1 2-1 2v2h0l-2-1-1 1c0 2-1 3-2 5l-1-1c-1-1-1-1-2 0h-1v-2c0-2 0-4 1-7z" class="R"></path><path d="M422 821c0-2 0-4 1-7 2 4 1 4 0 8h1 2l-1 1c-1-1-1-1-2 0h-1v-2z" class="S"></path><path d="M413 814c1 1 1 1 1 2 2 1 2 0 3 2v5 8 2 3l-1-1v-2l-1 1c0 1 0 1 1 2v1h-2v-3h-1c0 1 0 1-1 1l-1-1-2 1v-1h-2l-1-1v-1h0l-1 1h-1 0c-1-1-1-1-2-1 1-1 2-4 3-6 1-1 0-1 1-2l1-1c2-3 2-4 5-5 0-1 0-2 1-3h0v-1z" class="d"></path><path d="M413 815h0c0 3 0 16 1 18l1-1v-3l2 2v2 3l-1-1v-2l-1 1c0 1 0 1 1 2v1h-2v-3h-1c0 1 0 1-1 1l-1-1-2 1v-1h-2l-1-1v-1l1 1c1 0 2 0 2-1 1 0 1-1 1-2s0 0 1-1h0v2h1c0-1 0-3 1-4h0l-1-2v-7c0-1 0-2 1-3z" class="b"></path><path d="M413 814c1 1 1 1 1 2 2 1 2 0 3 2v5 8l-2-2v3l-1 1c-1-2-1-15-1-18v-1z" class="I"></path><path d="M435 813c1-1 2-1 3-1l4 2c4 6 1 16 2 23 1 1 1 1 0 2v2 1h-2c-1 0-2 0-3 1h-1v-7-2c-1-2 0-3-1-5h-4l-1-1h-3 0-1c1 2 1 3 1 5h-1l-1-1-2 2c-1 1-2 1-3 1 1 1 1 1 1 2h-2l-1-1c0-2 1-4 1-6 0-1-1-1-1-2s0-1 1-2c1 0 4-1 6 0h2c1 1 1 1 2 0h1c0 1 0 1 1 2h0c2 0 3 1 4 0l-1-1-1-1h3c0-2 0-4-1-6h-2v-2-5z" class="v"></path><path d="M438 834c1 3 2 4 1 6v3h-1v-7-2z" class="S"></path><path d="M466 795h7 10v4c1 1 2 7 2 9v5c1 1 1 2 1 3l1-1h0v-3c0-4 2-9 6-12-1 2-2 3-3 5s-2 4-2 7l1-1c1 2 1 4 1 7 0 1 1 2 1 3v4l2 2h0 1c1 2 2 6 1 8v1c-2 3 0 5-3 7h-1v-3l-1 1h0c0 1-1 2-2 3h6c-2 1-5 1-7 1-4-1-9 0-13-1 0-1-1-1-1-2v-2l-1-1c-2 0-2 0-2 2 0 1 0 0 1 1l1 1v1c-3 1-5 1-8 0h-5c-3 2-4 2-7 1h-2l1-2h1l1-1c1 0 1 0 2-1h1 1c1 1 1 0 2-1-1 0-1 0-2-1 1-3 1-6 1-9v-17-1-1c-1-1-1-1-1-2h0l-2-1h0c-1-2-1-2-1-3s0-2 1-3h0v2h1l1-1h2l2-1-1-2v-1h1l1-1 2 1v-2h1l1-2z" class="O"></path><path d="M475 821c1-1 0-1 1 0v9c0-2 0-4-1-5v-4z" class="o"></path><path d="M475 825c1 1 1 3 1 5l1 4h0v5-1l-1 2-2-2v2-10 2c1-2 1-5 1-7z" class="e"></path><path d="M466 795h7c2 1 3 1 5 1 1 1 1 1 1 3v1l1 1-1 1v1c-2 3-1 9-1 12l-1 1c0-2-1-9 0-11l1-1-1-1v-1-3h-1c-1 1-2 1-2 1l-1 1-2-1v-1-1l-1-1h-2c-2 1-2 1-3 1v-1l1-2z" class="M"></path><path d="M473 795h10v4 1l-1-1-1-1c-1 1-1 2-1 4 0 1-1 6 0 7h1 0v18 6c1 2 1 4 0 6h0c-1 0-2 1-4 0v-5l1-19c0-3-1-9 1-12v-1l1-1-1-1v-1c0-2 0-2-1-3-2 0-3 0-5-1z" class="b"></path><path d="M473 795h10v4 1l-1-1-1-1c-1 1-1 2-1 4 0 1-1 6 0 7v12c0 3 0 6-1 9v-27-1l1-1-1-1v-1c0-2 0-2-1-3-2 0-3 0-5-1z" class="S"></path><path d="M480 809c-1-1 0-6 0-7 0-2 0-3 1-4l1 1 1 1v-1c1 1 2 7 2 9v5l1 26-1 1-3-1h-1 0c1-2 1-4 0-6v-6-18h0-1z" class="g"></path><path d="M480 809c-1-1 0-6 0-7 0-2 0-3 1-4l1 1 1 1v-1c1 1 2 7 2 9-1 2-1 6-1 9h0l-1-3v-9c-1 2-2 22-1 25s0 6 0 9h-1 0c1-2 1-4 0-6v-6-18h0-1z" class="e"></path><path d="M460 799h1l1-1 2 1v-2h1v1 2h3l1-1v3l-1 1 1 1 1-1c1 0 1 0 2 1h1l1-1c1-1 1 0 2 0v1 1 4h-1-1l-1 2h-2c-1 0-1-1-1-1l-1-1v2h-1c-1 0-1 0-1 1-1 1-1 2-1 3l-1 1v-1-2c-1-1-1-2-2-2l-1 1c0-1 0-1-1-1s-2 1-3 1v-1c-1-1-1-1-1-2h0l-2-1h0c-1-2-1-2-1-3s0-2 1-3h0v2h1l1-1h2l2-1-1-2v-1z" class="Q"></path><path d="M473 804l1-1c1-1 1 0 2 0v1 1 4h-1-1l-1 2h-2c-1 0-1-1-1-1l-1-1v2h-1c-1 0-1 0-1 1-1 1-1 2-1 3l-1 1v-1-2c-1-1-1-2-2-2l-1 1c0-1 0-1-1-1s-2 1-3 1v-1c-1-1-1-1-1-2h0l-2-1h0c-1-2-1-2-1-3 2 0 2-1 4-1l1 1-1 1h-1v2h1 11c1 0 3 0 4-1v-3z" class="U"></path><path d="M476 809c0 4 0 8-1 12v4c0 2 0 5-1 7v-2 10h-1l-1-1c-2 0-2 0-2 2 0 1 0 0 1 1l1 1v1c-3 1-5 1-8 0v-1l2-1h0v-27h0c0-1 0-2 1-3 0-1 0-1 1-1h1v-2l1 1s0 1 1 1h2l1-2h1 1z" class="I"></path><path d="M476 809c0 4 0 8-1 12v4c0 2 0 5-1 7v-2-17h-1v2h-1v-2h-1s0 1-1 1c-1-1-2-2-3-2 0-1 0-1 1-1h1v-2l1 1s0 1 1 1h2l1-2h1 1z" class="M"></path><path d="M466 815l2 2h1c2 3-2 11 1 15l-1 1 1 1c-1 1-1 1-1 3h1v-2h1v3h1v1c-2 0-2 0-2 2 0 1 0 0 1 1l1 1v1c-3 1-5 1-8 0v-1l2-1h0v-27z" class="D"></path><path d="M458 812c1 0 2-1 3-1s1 0 1 1l1-1c1 0 1 1 2 2v2 1l1-1h0v27h0l-2 1v1h-5c-3 2-4 2-7 1h-2l1-2h1l1-1c1 0 1 0 2-1h1 1c1 1 1 0 2-1-1 0-1 0-2-1 1-3 1-6 1-9v-17-1z" class="d"></path><path d="M464 840h1l1 2-2 1v1h-5-4l-1-1c3 0 7 0 10-1v-2z" class="I"></path><path d="M461 824c0-3-1-6 0-9 1-1 1-1 3-1 1 4 1 8 1 12l-2-1h0v-3c1-1 0-2 0-3h-1c-1 1-1 3-1 5z" class="V"></path><path d="M461 824c0-2 0-4 1-5h1c0 1 1 2 0 3v3h0l2 1v14h-1c0-1-1-1-2-2-1 1-1 1-2 1 1-1 0-1 1-2h1v-1h-1-1l1-12z" class="G"></path><path d="M487 815v-3c0-4 2-9 6-12-1 2-2 3-3 5s-2 4-2 7l1-1c1 2 1 4 1 7 0 1 1 2 1 3v4l2 2h0 1c1 2 2 6 1 8v1c-2 3 0 5-3 7h-1v-3l-1 1h0c0 1-1 2-2 3h6c-2 1-5 1-7 1-4-1-9 0-13-1 0-1-1-1-1-2v-2h1v-2l2 2 1-2v1c2 1 3 0 4 0h1l3 1 1-1-1-26c1 1 1 2 1 3l1-1h0z" class="g"></path><path d="M485 813c1 1 1 2 1 3l1-1h0v27c-4 0-8-1-12 0h-2v-2h1v-2l2 2 1-2v1c2 1 3 0 4 0h1l3 1 1-1-1-26z" class="M"></path><path d="M489 811c1 2 1 4 1 7 0 1 1 2 1 3v4l2 2h0 1c1 2 2 6 1 8v1c-2 3 0 5-3 7h-1v-3l-1 1h0c0 1-1 2-2 3-2 0-4 0-6-1h2c1-1 1 0 2 0s2-1 2-1v-4-12-14l1-1z" class="e"></path><path d="M488 826l1-1v-3h1v5h1c-1 1-1 1-1 2-2 2 1 12-1 13l-1-4v-12z" class="M"></path><path d="M489 811c1 2 1 4 1 7 0 1 1 2 1 3v4l2 2h0c0 1 1 2 0 3 0-1-1-2-2-3h0-1v-5h-1v3l-1 1v-14l1-1z" class="S"></path><path d="M465 756c2 1 2 1 2 3l1 1h1s2 0 2 1v2 2l2 2 1 8h2 2l1 3h-1-2l-2 2v3h48 14 6v1h35l-2 2h2v2h-1c-1 1-1 2-1 3h-1v-2l-1 2h-1v-1l-1-1h-1-2c-1 0-2 1-2 1-2 0-2-1-3-2h-1c0 1 0 3-1 4h-8v1c-1 0-4-1-5 0-1 0-1 0-2 1h-30-1c1 1 2 1 3 1h-35-10-7l-1 2h-1v2l-2-1-1 1h-1c0-1 0-1-1-2h0c-2 1-4 1-6 1 1-1 1-1 2-1l-1-1h-18c-2-1-4-1-6-1-1 0-1-1-1-1l-1-2h-1c3-2 2-1 5-1h9 1 3 1 1c-1-2-2-1-3-2 3 0 8 1 11 0v-2h1l-1-2h0 3 2 4 0l-1-1h-2-5c1-1 1-1 2-1s1 0 2-1c0-3 1-9-1-12h0c1-2 1-5 1-7l-1-2c1-1 1-2 1-3h0 1 1 1c1 0 2-1 2-2z" class="m"></path><path d="M563 788v-1h-1 0-1c2-2 11-1 14-1h2v2h-1c-1 1-1 2-1 3h-1v-2l-1 2h-1v-1l-1-1h-1-2c-1 0-2 1-2 1-2 0-2-1-3-2z" class="S"></path><path d="M456 792c1-1 2-1 2-2h-1l-1-1h3c1-1 2-1 4-1h3c3-1 7 0 11 0l20-1c5 0 10 1 15 0 2-1 4-1 6-1v1h-4 0c-1 1-2 1-3 1h0v1h1v1h0v1c-4 0-10-1-12 1l1 1c-4 1-8 0-12 0h-22 0c-3 0-8 0-11-1z" class="d"></path><path d="M511 789h1v1h0v1c-4 0-10-1-12 1l1 1c-4 1-8 0-12 0h-22c2-1 5-2 7-2h1c-3 0-8-1-10 1l-1-1c-2 0-3 0-4 1l-1-1 1-1h3 8l40-1z" class="f"></path><path d="M511 788h8c6 0 12-1 17 0h19c2 0 4 0 6 1l1-1c0 1 0 3-1 4h-8v1c-1 0-4-1-5 0-1 0-1 0-2 1h-30-1c1 1 2 1 3 1h-35-10-7v-1c-2 0-5 0-7 1l1 1-2 1-1-1c0-2-1-1-2-3v-2l1-1v1 1c3 1 8 1 11 1h0 22c4 0 8 1 12 0l-1-1c2-2 8-1 12-1v-1h0v-1h-1v-1z" class="L"></path><path d="M511 788h8c6 0 12-1 17 0h-5v1c6 0 13-1 19 0h2 0c1 1 3 1 5 1l1 1c-2 0-9 0-11-1-8-1-16 1-24 1-3 0-8-2-11-1h0v-1h-1v-1z" class="T"></path><path d="M466 795v-1c-2 0-5 0-7 1l1 1-2 1-1-1c0-2-1-1-2-3v-2l1-1v1 1c3 1 8 1 11 1h0 22c4 0 8 1 12 0h33 14l5-1v1c-1 0-4-1-5 0-1 0-1 0-2 1h-30-1c1 1 2 1 3 1h-35-10-7z" class="R"></path><path d="M465 756c2 1 2 1 2 3l1 1h1s2 0 2 1v2 2l2 2 1 8h2 2l1 3h-1-2l-2 2v3h48 14 6v1h-79-2-5c1-1 1-1 2-1s1 0 2-1c0-3 1-9-1-12h0c1-2 1-5 1-7l-1-2c1-1 1-2 1-3h0 1 1 1c1 0 2-1 2-2z" class="c"></path><path d="M471 765l2 2 1 8h-2s-1 0-1-1v-2-7z" class="b"></path><path d="M476 775h2l1 3h-1-2l-2 2v3h-4l2-2-1-1 1-3c1-1 1-1 0-2h2 2z" class="t"></path><path d="M465 756c2 1 2 1 2 3l1 1h1s2 0 2 1v2c-1 1-1 2-1 4v5 1c-2 1 0 2-2 2-2-3 0-13-1-14-1 0-2 0-2 1v1 1 1 1h1v1h-2s-1 0 0-1v-1l-2-1h1l-1-1h-2l-1-2c1-1 1-2 1-3h0 1 1 1c1 0 2-1 2-2z" class="X"></path><path d="M460 763h2l1 1h-1l2 1v1c-1 1 0 1 0 1v2 2s0 1 1 2l-1 1c1 3 1 6 1 9-1 0-3 0-4 1h-5c1-1 1-1 2-1s1 0 2-1c0-3 1-9-1-12h0c1-2 1-5 1-7z" class="K"></path><path d="M462 764l2 1v1c-1 1 0 1 0 1v2 2s0 1 1 2l-1 1-1 1-2-1 1-1v-2l1-1-2-2c0-2 0-2 1-4z" class="b"></path><path d="M554 617h1 3c1-1 1-1 2 0 1-1 4-1 6-1h2 3l1 1h1 0l3 1c-1 3-2 7-2 10l-1 1h6l-1 1h5 4 3v1c0 1 0 0 1 1h0c1-1 1-1 2-1h1v2l-1 1v-2h-1v2l-2-1v1 1h14l-1-1h2l1 1h-1v5 11 7 1l-1-1h-1c0-1-1-2-1-3v3h-1c-1 3 0 3 0 6h-3c4 3 6 7 7 12v-1c1 3 1 6 1 8s0 4-1 6c1 0 1 1 1 1 0 1-1 1 0 3 0 1 0 2 1 4v2l-1 1h-1l-13-1c-3 0-7-1-10 1h-2v3l-1-1-1-1h0-1c-1-1-1-2-1-3l-2 1h-7-14 1c0 1 1 1 1 1l1 1h-5-4c-1 0-2 0-3 1l-1-2v2 1 2c-1 1-2 1-3 1h-2l-1-2h0c0 1 0 1-1 2l-1-1v-3h-1v4h-1v-2c-1 1-1 0-1 1v1c-1 0-1-2-2-3 0 1 0 2-1 3h0c-2-1-4-1-5-1-1-1-1-3-1-5-1 0 0 5-1 6-2-1-2 0-4-1-1 1-1 1-2 0l-1 1-1-1-1 1h-3 0l-2-1c-1 0-1 1-3 0h-1c-1 1-2 1-3 0h-1l-2 1c-1-1-1-4-1-5h-1v1 3c-1 1-2 0-4 0l-2 1-1-1-1 1h-2l-1-1-1 1-2-1v-5l-2 1h-1l-1 1h-1v-2l1-1v-9c-1-2-1-3-1-4v-1l1-2c0-1-1-2-2-3 0-1 1-2 1-3-1-2-4-4-6-6-4-3-9-2-14-1-4 2-7 4-8 8l-2 8c-1-1-1-4-1-5h0l-2-2v-1c-1-1-1-1-1-2 0 0 0-1-1-2 0-2-1-2-2-3h-2v-1-1c-1 0-2 0-3-1-1 0-1-1-2-1-2 0-2 0-3-1-1 0-1 0-2-1h0c-1 0-1-1-2-1h-1v-1h0c-1-1-3 0-4 0l-1-1c1-1 1-3 1-4h3l-1-1h6c4 0 9-1 12 0h3 25c2 0 6-1 8 1l1-1c4-1 9 1 13 0h13c3 0 7 0 10 1v-1h1c2 1 4 1 5 1 3 0 7 0 9-1l1 1h1 1 1 0 3 3 3c2 0 4 0 6-1-2 0-3 0-4-1 0-1 0-1 1-2h0 1v-2c0-1-1-1-1-2 1 1 1 1 2 0h3v2h0l1-2h3c0-1-1-2-1-3h-1-1c1-2 1-5 1-8v-9c1-3 1-6 1-9l1-4z" class="i"></path><path d="M519 665c1 0 2 0 4 1l1-1 1 1 1-1 1 1-1 1h1c1-1 1-1 1-2-1-2-6-1-8-1 2-1 9-1 11-1l1 1-1 1v2l-1 1c-1-2 1-3 0-4-2 2-1 7-3 7-1-1-1-1-3-1 0 0-2-1-2-2-2 0-2-1-3-3z" class="m"></path><path d="M502 667c-3 2-5 4-7 7h-1c0 2-1 3-1 5 0 1 0 2-1 3v2c1 0 1-5 2-6 0-1 0-1 1-2s1-3 2-5c1-1 6-5 7-5 2-1 5-1 7-1 1 2 2 2 4 3 1 0 1 0 1 1h0c-3-1-6-1-9 0l-3 1c-4 2-7 5-9 9l-1 4c-1 1-1 3-1 3-2 2-1 6-1 8l-1-1-1-2c-1-2 0-6 0-8v-2-1c0-2-1-9 0-11 2 2 0 7 2 9v-3c2-3 3-5 5-7 2-1 3-1 5-1z" class="b"></path><path d="M524 670c2 0 2 0 3 1 2 0 1-5 3-7 1 1-1 2 0 4l1-1 1-1 1-1c0 7 1 15-1 21 0 4-1 10 0 14h-1 0-1l-2-1v-4c0-7-1-14-5-20 0-2-2-3-4-5-1 0-1 0-3-1h0c0-1 0-1-1-1-2-1-3-1-4-3 2 0 6 1 8 0 1 2 1 3 3 3 0 1 2 2 2 2z" class="Z"></path><path d="M511 665c2 0 6 1 8 0 1 2 1 3 3 3 0 1 2 2 2 2 2 2 4 5 4 8 1 1 1 1 0 2-1-1-1-2-1-3l-1-1c0-1-1-2-2-4 0-1-1-1-2-2l-1 1c0-1-1-1-2-1s-1 0-3-1h0c0-1 0-1-1-1-2-1-3-1-4-3z" class="n"></path><path d="M532 666l1-1c0 7 1 15-1 21 0 4-1 10 0 14h-1 0-1l-2-1v-4c0-7-1-14-5-20 0-2-2-3-4-5 1 0 2 0 2 1 2 2 5 4 5 7 1 2 2 4 2 6 1 4 0 11 2 15h1c-1-5 0-10 1-14v-1c0-6-1-11 0-17v-1z" class="K"></path><path d="M479 662h25c0 1 0 2-1 2-2 0-6-1-7 0 2 2 3 1 5 2l1-1 1 1-1 1h0c-2 0-3 0-5 1-2 2-3 4-5 7v3c-2-2 0-7-2-9-1 2 0 9 0 11v1 2c0 2-1 6 0 8l1 2 1 1c1 1 0 4 0 6v5l-2 1-1-1-1 1c-1-3-1-4 0-6l1-1h0l1-1-1-1c0 1-1 1-2 1v-20c0-1-1-5 0-6 0-1 0-1 1-2-2-1-4-3-6-4h-1l1-1-1-1c0-1 0-1-2-2z" class="e"></path><path d="M487 664h2c1 1 2 1 2 2h-2v2c-1-1-3-2-3-3l1-1z" class="S"></path><path d="M599 647c1 2 1 4 2 6v-6l-1-1c1-1 1-6 1-8l1-1v14 4 3h-1c-1 3 0 3 0 6h-3-2c0 1-1 1-1 2l1 1h-1c-1-1-1-1-3-1 1 1 3 2 3 3-1 0 0 0-1-1-2-1-5-1-7-1h-1-2v1h3c1-1 1 0 2 0h0c5 1 10 5 12 9v2c-1-1-1-2-1-3-1-3-7-6-9-6l-1-1h-5v1l-1 1h0c-1 2-2 3-3 4-1 0-2 0-3 1 0 0 0 1-1 2h1v5l-1 1h0c1 1 1 1 1 2l-1 1-2 3c-1-1-1-2-1-3v-1c-1-2-1-4-1-6l-1-2v-1c-2-3-5-6-8-7l-1-1c-3-1-9 0-12 1v1c0 1-1 1-1 1-1 1-2 1-2 2l-1 1c-1 0-2 1-2 2-1 1-1 1-1 2 1 0 2-1 3-2 1 4 0 7 0 10l-1 1v-3h0l-2-1h-3c0 1 0 1-1 3-1 1-1 6-1 8v3h-1v-12c0-2-1-4 0-6h0v-1h0v-1c0-2 0-3 1-4h0v2l2-2v-1l1-2c1-2 2-3 4-4h0c-1 1-1 2-2 3h1l1-1 3-3h2l1-1c1 1 2 1 3 1 3-1 6-1 9-1-1 0-6-1-7-1l1-1c2 0 5 0 7 1l1 1h2c2 0 4 1 6 0 1-1 0 0 2-1v1l-1 1h2c1-1 2-1 3-2l1-1h5c3-1 4 0 6-1v-1h1l1 1h1c-1-1-1-1-1-2h1v1h1v-3h-2l-1-1c1-2 1-1 3-3v-4l2-1c0 2 0 5 1 6v-8z" class="j"></path><path d="M577 684h0l-1 1c0-3-1-7-1-9 1 0 1-2 2-2l2-2c2-1 3-1 5-1h0c-1 2-2 3-3 4-1 0-2 0-3 1 0 0 0 1-1 2h1v5l-1 1h0z" class="U"></path><path d="M551 670v1c0 1-1 1-1 1-1 1-2 1-2 2l-1 1c-1 0-2 1-2 2-1 1-1 1-1 2 1 0 2-1 3-2 1 4 0 7 0 10l-1 1v-3h0l-2-1h-3c0-2 1-5 2-7 2-3 4-6 8-7z" class="S"></path><path d="M429 657c4 0 9-1 12 0h3 25c2 0 6-1 8 1l1-1c4-1 9 1 13 0h13c3 0 7 0 10 1v-1h1c2 1 4 1 5 1 3 0 7 0 9-1l1 1h1 1 1 0 3 3 3c2 0 4 0 6-1h0 3l1 1c0-1 1-1 2-1l1 2c0-1 0-1 1-2h0l1 1-1 1h11c2 0 7 1 8 0v-1h-14l-1-1h11c2 0 3 0 5 1v-1h4c1 0 1 0 2 1l1-1v2l1-1h1c1 0 2 0 4 1 1 0 3-1 5-1h2v3h-1v-1h-1c0 1 0 1 1 2h-1l-1-1h-1v1c-2 1-3 0-6 1h-5l-8-1h-69-25-27-8c-1 0-2 0-3 1-2-1-3-1-5-1h0l-1 1c-1 0-3-1-4-2h1c1 0 2 1 3 1v-1l-1-1h-3-1v-1c-2-1-2-1-4-1h-2l-1-1h6z" class="d"></path><path d="M556 657l1 1-1 1h11l-1 1h-11-25-80c2-2 18-1 22-1h5c0-1 2 0 3 0h1c1 0 1 0 2-1h1l1 1h4l1-1c0 1 1 1 2 1h7c4 0 12 0 15-1v-1h1c2 1 4 1 5 1 3 0 7 0 9-1l1 1h1 1 1 0 3 3 3c2 0 4 0 6-1h0 3l1 1c0-1 1-1 2-1l1 2c0-1 0-1 1-2h0z" class="E"></path><path d="M429 657c4 0 9-1 12 0h3 25c2 0 6-1 8 1l1-1c4-1 9 1 13 0h13c3 0 7 0 10 1-3 1-11 1-15 1h-7c-1 0-2 0-2-1l-1 1h-4l-1-1h-1c-1 1-1 1-2 1h-1c-1 0-3-1-3 0h-5c-4 0-20-1-22 1-2 0-2 0-4-1l-2 1h0l-1-1-1 1-1-1-1 1h0-4c-2-3-4-1-7-3z" class="K"></path><path d="M571 657c2 0 3 0 5 1v-1h4c1 0 1 0 2 1l1-1v2l1-1h1c1 0 2 0 4 1 1 0 3-1 5-1h2v3h-1v-1h-1c0 1 0 1 1 2h-1l-1-1h-1v1c-2 1-3 0-6 1h-5l-8-1h-69-25-27l1-1h69 2 9 25 13 1v1h1c1-1 1-1 2-1-2-1-6-1-9-1l1-1c2 0 7 1 8 0v-1h-14l-1-1h11z" class="I"></path><path d="M554 617h1 3c1-1 1-1 2 0 1-1 4-1 6-1h2 3l1 1h1 0l3 1c-1 3-2 7-2 10l-1 1h6l-1 1h-1-3v1c-2 2-2 2-2 5l-1 3c-1-1-2-2-2-3-1 3-3 5-3 8-1 3-1 3 0 6 1 0 0 0 1 1 2-2 3-2 5-1l-1 4h1c0 1 0 2-1 3h-11l1 1h14v1c-1 1-6 0-8 0h-11l1-1-1-1h0c-1 1-1 1-1 2l-1-2c-1 0-2 0-2 1l-1-1h-3 0c-2 0-3 0-4-1 0-1 0-1 1-2h0 1v-2c0-1-1-1-1-2 1 1 1 1 2 0h3v2h0l1-2h3c0-1-1-2-1-3h-1-1c1-2 1-5 1-8v-9c1-3 1-6 1-9l1-4z" class="K"></path><path d="M566 644c-1 3-1 3 0 6 1 0 0 0 1 1l-1-1h-3v-6h3z" class="Z"></path><path d="M560 657l-1-1v-3l1 1v1h1c0-1 0-1 1-1 0 0 1 0 2-1 1 0 1 1 2 1l1-1 3 1h0l1 1v-1h1c0 1 0 2-1 3h-11z" class="O"></path><path d="M552 639v-9c1-3 1-6 1-9l1 24c2-2 0-15 2-15 0 6 1 13-1 20v3c1 1 1 1 1 3v1h0c-1 1-1 1-1 2l-1-2c-1 0-2 0-2 1l-1-1h-3 0c-2 0-3 0-4-1 0-1 0-1 1-2h0 1v-2c0-1-1-1-1-2 1 1 1 1 2 0h3v2h0l1-2h3c0-1-1-2-1-3h-1-1c1-2 1-5 1-8z" class="l"></path><path d="M554 650c1 1 1 1 1 2-1 1-1 2-1 3v1h-1v-2c-1-1-3 0-4 0l-1 1v2h0c-2 0-3 0-4-1 0-1 0-1 1-2h0 1v-2c0-1-1-1-1-2 1 1 1 1 2 0h3v2h0l1-2h3z" class="d"></path><path d="M566 616h2 3l1 1h1 0l3 1c-1 3-2 7-2 10l-1 1h6l-1 1h-1-3v1c-2 2-2 2-2 5l-1 3c-1-1-2-2-2-3-1 3-3 5-3 8h-3v-1l1-5v-12l1-1h1c1-3 0-6 0-9z" class="l"></path><path d="M566 616h2c0 1 0 1 1 3h-1v3 2c0 1-2 2-2 2-1 3 1 7-1 10l-1 2v-12l1-1h1c1-3 0-6 0-9z" class="t"></path><path d="M563 643c0-1 1-1 1-2 1-1 1-1 2-1v-5h1v-1-1c1-3 1-5 2-8 2-2 1-5 3-8h1l-1 2 1 1 1-1v1c-1 0-2 1-2 1-1 4-1 9-3 12v3c-1 3-3 5-3 8h-3v-1z" class="j"></path><path d="M573 617h0l3 1c-1 3-2 7-2 10l-1 1h6l-1 1h-1-3v1c-2 2-2 2-2 5l-1 3c-1-1-2-2-2-3v-3c2-3 2-8 3-12 0 0 1-1 2-1v-1l-1 1-1-1 1-2z" class="R"></path><path d="M569 633c1 1 2 1 2 2l1 1-1 3c-1-1-2-2-2-3v-3z" class="M"></path><path d="M577 630h1 5 4 3v1c0 1 0 0 1 1h0c1-1 1-1 2-1h1v2l-1 1v-2h-1v2l-2-1v1 1h14l-1-1h2l1 1h-1v5 11 7 1l-1-1h-1c0-1-1-2-1-3v-4-14l-1 1c0 2 0 7-1 8l1 1v6c-1-2-1-4-2-6v8c-1-1-1-4-1-6l-2 1v4c-2 2-2 1-3 3l1 1c-2 0-4 1-5 1-2-1-3-1-4-1h-1l-1 1v-2l-1 1c-1-1-1-1-2-1h-4v1c-2-1-3-1-5-1 1-1 1-2 1-3h-1l1-4c-2-1-3-1-5 1-1-1 0-1-1-1-1-3-1-3 0-6 0-3 2-5 3-8 0 1 1 2 2 3l1-3c0-3 0-3 2-5v-1h3z" class="T"></path><path d="M577 630h1 5v2 8c-2 0-4 0-5-1 1-2 1-6 3-8h-3v-1l-1 1h-1l1-1z" class="f"></path><path d="M574 631h1c1 1 1 1 1 2h0l1 1c0 1-2 6-3 7s-1 1-2 1h-1l1 1c1 0 1 0 2 1l-1 2c-2 0-3 1-5 0l1-2c0-1 1-3 2-4v-1l1-3c0-3 0-3 2-5z" class="J"></path><path d="M569 636c0 1 1 2 2 3v1c-1 1-2 3-2 4l-1 2c2 1 3 0 5 0 1 0 1 1 2 1h5c4 0 10-1 14 0h2c1-1 1-2 2-3h1c-1 2-1 2-1 3h1v8c-1-1-1-4-1-6l-2 1c-2-1-6 0-7 0h-1-1 0c-2-1-4 0-6 0l-1-1-1-1-2 2h-1c-2 0-1 0-2-1h-1l-1 1c-2-1-3-1-5 1-1-1 0-1-1-1-1-3-1-3 0-6 0-3 2-5 3-8z" class="O"></path><path d="M603 634h2l1 1h-1v5 11 7 1l-1-1h-1c0-1-1-2-1-3v-4-14l-1 1c0 2 0 7-1 8l1 1v6c-1-2-1-4-2-6h-1c0-1 0-1 1-3h-1c-1 1-1 2-2 3h-2v-3h-1v2h-1v-2c1-1 1-2 1-4h0 1v3h1v-6c-2 0-3 0-4 1h0l-1-1v-2h14l-1-1z" class="Z"></path><path d="M602 637l1-1h1c0 1 0 1-1 2 0 2 1 4 1 6 1 2 0 5 1 7h0v7 1l-1-1h-1c0-1-1-2-1-3v-4-14z" class="o"></path><path d="M572 650l1-1h1c1 1 0 1 2 1h1l2-2 1 1 1 1c2 0 4-1 6 0h0 1 1c1 0 5-1 7 0v4c-2 2-2 1-3 3l1 1c-2 0-4 1-5 1-2-1-3-1-4-1h-1l-1 1v-2l-1 1c-1-1-1-1-2-1h-4v1c-2-1-3-1-5-1 1-1 1-2 1-3h-1l1-4z" class="n"></path><path d="M587 650h1 1c1 0 5-1 7 0v4c-2 2-2 1-3 3l1 1c-2 0-4 1-5 1-2-1-3-1-4-1h-1v-2l1-2h0 2 0v-4z" class="l"></path><path d="M585 670v-1h5l1 1c2 0 8 3 9 6 0 1 0 2 1 3v-2c-2-4-7-8-12-9h0c-1 0-1-1-2 0h-3v-1h2 1c2 0 5 0 7 1 1 1 0 1 1 1 0-1-2-2-3-3 2 0 2 0 3 1h1l-1-1c0-1 1-1 1-2h2c4 3 6 7 7 12v-1c1 3 1 6 1 8s0 4-1 6c1 0 1 1 1 1 0 1-1 1 0 3 0 1 0 2 1 4v2l-1 1h-1l-13-1c-3 0-7-1-10 1h-2v3l-1-1-1-1h0-1c-1-1-1-2-1-3v-2-1c-1-1-1-4 0-5l1-1h1l-1-2 1-1c0-1 0-1-1-2h0l1-1v-5h-1c1-1 1-2 1-2 1-1 2-1 3-1 1-1 2-2 3-4h0l1-1z" class="J"></path><path d="M605 676v-1c1 3 1 6 1 8s0 4-1 6c1 0 1 1 1 1 0 1-1 1 0 3 0 1 0 2 1 4v2l-1 1h-1l-13-1v-2c1 1 2 1 3 0 1 1 2 1 3 1l1-1c1 0 2 0 3-1s2-3 2-5h0c2-5 1-11 1-15z" class="R"></path><path d="M585 670c4 0 7 0 11 4 2 1 4 4 4 6l-1 3h-5-2c0-1 0-1-1-2h0c1-1 0-1 1-2l-1-1h-2c-2 0-2 0-3-1 0-1 0 0-1-1v1l-1 1c-1-1-1-1-2-1v1c-2 2-3 3-3 5h-1v-5h-1c1-1 1-2 1-2 1-1 2-1 3-1 1-1 2-2 3-4h0l1-1z" class="p"></path><path d="M596 674c2 1 4 4 4 6l-1 3h-5s0-2 1-2 1 0 2-1v-1-1c-1-2-1-2-1-4h0z" class="U"></path><path d="M584 678l1-1v-1c1 1 1 0 1 1 1 1 1 1 3 1h2l1 1c-1 1 0 1-1 2h0c1 1 1 1 1 2h2 5l-1 1 1 1h1v1c-1 0-1 0-2-1v1c0 1 1 1 1 2 1 0 1 1 2 2l-1 1 2 1v1h-2v2h0l2 1c-1 1-2 1-3 1l-1 1c-1 0-2 0-3-1-1 1-2 1-3 0-1-2 0-5-1-8v-1l-1-1c0 1-1 1-1 1-2-2 0-5-1-7l-1 1h0-1l1-1-1-1c0-1 0-1-1-1v2l-1-1v-2z" class="j"></path><path d="M591 688l1-1v-2c1-1 2-1 3-1v1h0v4l-1 1c-1 0-2 0-3-1v-1z" class="p"></path><path d="M595 689l1 1h0c-1 2-1 5-1 7-1 1-2 1-3 0-1-2 0-5-1-8 1 1 2 1 3 1l1-1z" class="U"></path><path d="M582 678v-1c1 0 1 0 2 1v2l1 1v-2c1 0 1 0 1 1l1 1-1 1h1 0l1-1c1 2-1 5 1 7 0 0 1 0 1-1l1 1v1c1 3 0 6 1 8v2c-3 0-7-1-10 1h-2v3l-1-1-1-1h0-1c-1-1-1-2-1-3v-2-1c-1-1-1-4 0-5l1-1h1l-1-2 1-1c0-1 0-1-1-2h0l1-1h1c0-2 1-3 3-5z" class="o"></path><path d="M578 683h1c0-2 1-3 3-5 0 3-1 6-1 9 1 2 0 2 1 4l-2 2c-1-1-1-3-1-4h0c-1 1-1 1-1 2-1-1-1-1-1-2h1l-1-2 1-1c0-1 0-1-1-2h0l1-1z" class="M"></path><path d="M424 658h2c2 0 2 0 4 1v1h1 3l1 1v1c-1 0-2-1-3-1h-1c1 1 3 2 4 2l1-1h0c2 0 3 0 5 1 1-1 2-1 3-1h8 27c2 1 2 1 2 2l1 1-1 1h1c2 1 4 3 6 4-1 1-1 1-1 2-1 1 0 5 0 6v20c1 0 2 0 2-1l1 1-1 1h0l-1 1c-1 2-1 3 0 6h-2l-1-1-1 1-2-1v-5l-2 1h-1l-1 1h-1v-2l1-1v-9c-1-2-1-3-1-4v-1l1-2c0-1-1-2-2-3 0-1 1-2 1-3-1-2-4-4-6-6-4-3-9-2-14-1-4 2-7 4-8 8l-2 8c-1-1-1-4-1-5h0l-2-2v-1c-1-1-1-1-1-2 0 0 0-1-1-2 0-2-1-2-2-3h-2v-1-1c-1 0-2 0-3-1-1 0-1-1-2-1-2 0-2 0-3-1-1 0-1 0-2-1h0c-1 0-1-1-2-1h-1v-1h0c-1-1-3 0-4 0l-1-1c1-1 1-3 1-4h3z" class="j"></path><path d="M479 668l1-1h3c0 2 1 4 2 6l-6-5z" class="i"></path><path d="M471 665c1 0 1-1 2-1 1-1 6 0 8 0l1 1-1 1h1l1 1h-3l-1 1c-3-2-4-3-8-3zm6 12c1 2 1 3 1 5l2 10c1 2 0 6 2 8l-2 1h-1l-1 1h-1v-2l1-1v-9c-1-2-1-3-1-4v-1l1-2c0-1-1-2-2-3 0-1 1-2 1-3zm-52-14c7 0 13 3 18 8 1 1 1 2 2 3 1 2 1 4 1 6v1h0l-2-2v-1c-1-1-1-1-1-2 0 0 0-1-1-2 0-2-1-2-2-3h-2v-1-1c-1 0-2 0-3-1-1 0-1-1-2-1-2 0-2 0-3-1-1 0-1 0-2-1h0c-1 0-1-1-2-1h-1v-1z" class="M"></path><path d="M452 662h27c2 1 2 1 2 2-2 0-7-1-8 0-1 0-1 1-2 1h0c-1 1-2 0-4 0-3 0-6 1-9 0h0-1l-2 1-1-1h-1l-2 2c-1 2-2 3-3 5h-1 0l1-2 3-3v-1c-1 1-2 1-3 2v-1l-1-1c-1 0-1-1-2-1l1-1h0l-1-1h-1v3h-1v-1h-2v-1h2l1-2h8z" class="Y"></path><path d="M551 670c3-1 9-2 12-1l1 1c3 1 6 4 8 7v1l1 2c0 2 0 4 1 6v1c0 1 0 2 1 3l2-3 1 2h-1l-1 1c-1 1-1 4 0 5v1 2l-2 1h-7-14 1c0 1 1 1 1 1l1 1h-5-4c-1 0-2 0-3 1l-1-2v2 1 2c-1 1-2 1-3 1l-1-8v-3c0-2 0-7 1-8 1-2 1-2 1-3h3l2 1h0v3l1-1c0-3 1-6 0-10-1 1-2 2-3 2 0-1 0-1 1-2 0-1 1-2 2-2l1-1c0-1 1-1 2-2 0 0 1 0 1-1v-1z" class="K"></path><g class="O"><path d="M552 677v-1l-1-1 1-1c1 0 4-1 6 0v3c1-1 1-2 2-3 1 1 0 2 0 3h1c0 3 1 9-1 11-2-1-3-1-3-3v-1l-2 4v-1c0-3 1-7 0-10h-1c0 3 1 7 0 10h-1c0-3 1-8-1-10z"></path><path d="M561 676l1-1h0 2c1 1 2 1 3 2v1l-1 1h2c1 0 1 1 1 2l1 1h1v-1c1 0 1-1 2-1 0 2 0 4 1 6v1c0 1 0 2 1 3l2-3 1 2h-1l-1 1c-1 1-1 4 0 5v1 2l-2 1c-2-1-2-1-2-3l-1-1-1 2c-1-1-1-2-1-3h0v-1l-2 1v-1-1h4l1-1h-4v-1h4v-1s0-1-1-2v-1h0l-2 1h0c0-2-1-3 0-4v-1h0c-1-1-1 0-2-1 0-1 0 0-1-1 0 1-1 3-1 4v1h-1c1-2 0-3 1-5v-1l-1-1c0 2 0 3-1 4v-2c-1-2-1-3-2-4z"></path></g><path d="M551 670c3-1 9-2 12-1l1 1c3 1 6 4 8 7v1l1 2c-1 0-1 1-2 1v1h-1l-1-1c0-1 0-2-1-2h-2l1-1v-1c-1-1-2-1-3-2h-2 0l-1 1v1h-1c0-1 1-2 0-3-1 1-1 2-2 3v-3c-2-1-5 0-6 0l-1 1 1 1v1c0 3 0 5-1 8v-8-1h-2c1 1 1 2 1 3-1 1-1 2-1 4v1h0v-2c1-1 0-2 0-4l-2-1c-1 1-2 2-3 2 0-1 0-1 1-2 0-1 1-2 2-2l1-1c0-1 1-1 2-2 0 0 1 0 1-1v-1z" class="v"></path><path d="M541 684h3l2 1h0l-1 1v1c-2 0-2 1-3 2v1c5 1 10 0 15 0 2 0 4 0 6 1h-2l-1 1h1 2v1 4h-1c-1-1 1-2 0-4l-2 2-1-1v-1l-1 1v1 2h-1l1-2h-1l1-1-1-1c1-1 2-1 3-1v-1h-11v1h3v1h-1-1l-1 1 1 1h1-2c-1 1-1 2-2 2-1-1 0-1-1-2v-1c-1-1-1-1-2-1 1-1 2-1 3-1v-1c-1 0-3 0-5 1h1c0 1-1 2 0 4h0v2l-1 1c3 1 8 0 11 0h1c0 1 1 1 1 1l1 1h-5-4c-1 0-2 0-3 1l-1-2v2 1 2c-1 1-2 1-3 1l-1-8v-3c0-2 0-7 1-8 1-2 1-2 1-3z" class="Y"></path><path d="M507 669c3-1 6-1 9 0 2 1 2 1 3 1 2 2 4 3 4 5 4 6 5 13 5 20v4l2 1h1l-2 2 1 1c0 1 0 2-1 3h0c-2-1-4-1-5-1-1-1-1-3-1-5-1 0 0 5-1 6-2-1-2 0-4-1-1 1-1 1-2 0l-1 1-1-1-1 1h-3 0l-2-1c-1 0-1 1-3 0h-1c-1 1-2 1-3 0h-1l-2 1c-1-1-1-4-1-5h-1v1 3c-1 1-2 0-4 0v-5c0-2 1-5 0-6 0-2-1-6 1-8 0 0 0-2 1-3l1-4c2-4 5-7 9-9l3-1z" class="j"></path><path d="M514 687h-2c0-2 0-1 1-3v-2l-1-1v-2h2 2c0 1 1 2 2 3-1 2-2 3-4 5z" class="p"></path><path d="M516 679c2 0 2 0 4-2l2 4h0v2c0 1 0 1 1 2v3c-2 0 1 1-1-1h-1-2c-1 0-1 0-1-1l-1 1h-3c2-2 3-3 4-5-1-1-2-2-2-3z" class="M"></path><path d="M518 682l1-1h0c0 2 1 2 2 3v3h-2c-1 0-1 0-1-1l-1 1h-3c2-2 3-3 4-5z" class="u"></path><path d="M507 669c3-1 6-1 9 0 2 1 2 1 3 1 2 2 4 3 4 5v1 1c2 2 2 3 2 5v1c-1-1-1-3-2-4h-1c-1-2-4-6-7-7-2-1-3-2-5-1h-3c2-1 4 0 5-1v-1h-5z" class="O"></path><path d="M500 681c0-1 1-1 2-1 0-1 0-1 1-1h1 2 3 1l-1 1c0 1 0 2 1 3 0 1 0 2-1 3h1l-1 1-1-1v1h-3c0-2 1-5-1-6-1 2-1 4-1 6-2 0-2 1-4 0v-4l1-1-1-1h1z" class="R"></path><path d="M522 679h1c1 1 1 3 2 4v-1c0-2 0-3-2-5v-1-1c4 6 5 13 5 20v4l2 1h1l-2 2 1 1c0 1 0 2-1 3h0c-2-1-4-1-5-1-1-1-1-3-1-5-1 0 0 5-1 6-2-1-2 0-4-1h1c0-2 0-2-1-3 1-1 1-2 2-3 1 0 3 1 4-1-1-4 1-9 0-13h0c0-2-1-4-2-6z" class="U"></path><path d="M524 705c0-1 0-2 1-3-1-1-1-1-1-2l1-1c1 1 1 1 2 1h2 1 1l-2 2 1 1c0 1 0 2-1 3h0c-2-1-4-1-5-1z" class="v"></path><path d="M507 669h5v1c-1 1-3 0-5 1 0 0-1 0-1 1l-2-1-3 3-1 1 1 1 3-3h1l1 1v1l-1-1h-1c-1 0-3 3-4 3-1 1-1 0-2 2v1c1 0 1 0 2 1h-1l-1-1c-1 1-1 2-1 3-1 1-1 1-1 2v1c0 1-1 2-1 3h1 1l-1 1c-1 0-1 0-1 1v2 1l1 1c-1 2-1 3-2 4 2 1 5 0 7 0h19c-1 1-1 2-2 3 1 1 1 1 1 3h-1c-1 1-1 1-2 0l-1 1-1-1-1 1h-3 0l-2-1c-1 0-1 1-3 0h-1c-1 1-2 1-3 0h-1l-2 1c-1-1-1-4-1-5h-1v1 3c-1 1-2 0-4 0v-5c0-2 1-5 0-6 0-2-1-6 1-8 0 0 0-2 1-3l1-4c2-4 5-7 9-9l3-1z" class="Y"></path><path d="M500 705c-1-1-1-2-1-4v-1c1 1 1 1 2 1l2-1v2 3h-1-1-1z" class="v"></path><path d="M440 499v1 4c1 1 3 1 4 1h1 4 8 11l-1 1h-3l1 1c1 1 1 2 1 3 1-1 0-1 2-2h3c1 2 1 4 1 5v-4l3-1v2c0 3 1 8 0 11 0 1-1 5 0 6v1c1 1 2 1 2 2l1 1c0 2 0 3 2 4h4c1 1 0 1 0 3 3 0 5-1 7-1 1 1 1 1 2 0h9 1c1 0 1 0 2 1h0c1-1 2-1 2-1h15 3v1s1 0 1-1c1 0 3 1 4 1 0 1 0 0 1 1 0-1 0-1 1-2h1v-1h2v-1h5v1l2-1h1v-1c0-1 0-2 2-3v-1c0-1 0-2 1-3 2 2 1 3 1 5l1 1v-2h1 2 3v6h1 2v4l1 7-1 3c2 0 3 0 4 2v-2h-1v-6c0-5 0-9-1-14h5 0 5 0l4-1 3 1h2l-1 4v5l1 1v3c1 0 1-1 1-1v-1h1v1c1 0 1 0 2 1 0 3 1 5 2 9 0 2 1 3 2 6v2l4 39c1 6 0 13 0 19 0 3-1 7 0 10v1h-3-4-5l1-1h-6l1-1c0-3 1-7 2-10l-3-1h0-1l-1-1h-3-2c-2 0-5 0-6 1-1-1-1-1-2 0h-3-1l-1 4c0 3 0 6-1 9v9c0 3 0 6-1 8h1 1c0 1 1 2 1 3h-3l-1 2h0v-2h-3c-1 1-1 1-2 0 0 1 1 1 1 2v2h-1 0c-1 1-1 1-1 2 1 1 2 1 4 1-2 1-4 1-6 1h-3-3-3 0-1-1-1l-1-1c-2 1-6 1-9 1-1 0-3 0-5-1h-1v1c-3-1-7-1-10-1h-13c-4 1-9-1-13 0l-1 1c-2-2-6-1-8-1h-25-3c-3-1-8 0-12 0h-6l1 1h-3l-1-1h-2-1l-4 1h-15c2-4 1-9 1-13l1-1v-1l-1-1c-1 0-1-1-1-1 0-4-1-7-1-11h0-2c0-1 0-2 1-2h0 1v-2h1c1 2 1 2 1 4v2c1 1 0 0 1 0h6l7 1-2-1c-1 0-2 0-3-1 1 0 2-1 2-1h1l1-1c1-1 1-2 2-4l-1-1v-3-28-7-33-5-11c3 1 5 3 7 4l1-1h0c-1-2-1-2-3-3h-1l-1-3c2 1 3 1 4 2l1-2c0-2-1-5 0-8 1 0 1-1 2-2h1l1-1h0 2 2v-3c0-1 1-3 1-4h1c1-1 1-2 2-2s1 0 1-1c1-1 0 0 2-1l1 1 1 1v2 8c0 2 1 3 1 5h1v-1c-1-3-1-3 0-5-1-2-1-3 0-5v-7c1-2 0-3 0-5h-2v-1l1-1c-1-2-2-3-2-4h-1l1-1h1c1 1 1 0 2 0z" class="n"></path><path d="M449 567c1-2-1-2-1-3v-4h1c0 1 0 1 1 2h1v6h-1l-1-1z" class="m"></path><path d="M440 528c0-2 0-3 2-3 1 1 1 1 1 2v1 1h0l2 1h0-3l-1 1c-1 0-2-1-3-1v-1h1 1v-1z" class="R"></path><path d="M448 523c0-1 0-1 1-2 1 0 2 1 3 1v1 3 1h1s0 1 1 1c2 0 5-1 6 0v1h-9-3c-1 1-1 0-2 0h0c1-2 2-4 2-6z" class="I"></path><path d="M448 523c0 1 1 2 1 3s2 1 2 2-1 0 0 1h-3c-1 1-1 0-2 0h0c1-2 2-4 2-6z" class="T"></path><path d="M451 562c0-1-1-6-1-6h-1c-1 0-1-1-2-1v-4l-1-1c0 1 0 2-1 3v-6c-1-2-1-6-2-8v-3-3-2h3v9 6l1-2h1c-1 2-1 3 0 4 1 2 1 3 3 5h1c0-1 0-2 1-2v1l1 1v1 1c-1 2-1 4-2 6l-1 1z" class="K"></path><path d="M437 529v-13h1v8c0 2 1 3 1 5h-1v1c1 0 2 1 3 1v1 2c1 3 0 7 1 10 0 2 0 3 1 5v6c-2-2-2-4-2-7h-1v2c-1-1-1-1-1-2l-1 1c-1 1-2 1-4 1h-1l2-2h2c-1-1-1-4-1-5l1-1v2h1v-2c-1-1-2-2-2-4l1-1v-8z" class="Z"></path><path d="M451 531h1v7 1 1c0 1 0 0 1 1v4 2c1-1 1 0 1-1h1l-1 7-1-1v-1c-1 0-1 1-1 2h-1c-2-2-2-3-3-5-1-1-1-2 0-4h-1l-1 2v-6-9h5z" class="I"></path><path d="M451 531h1v7 1 1c0 1 0 0 1 1v4h0c-1 1-1 1-1 2h-1-1l-1 1c0-1 0-3 1-4 1-3 0-7 1-10v-3z" class="Q"></path><path d="M451 547c0-3 0-6 1-8v1c0 1 0 0 1 1v4h0c-1 1-1 1-1 2h-1z" class="I"></path><path d="M446 540h0c2-3 0-6 2-8v1h0c-1 3 0 10 1 13v2l1-1h1 1c0-1 0-1 1-2h0v2c1-1 1 0 1-1h1l-1 7-1-1v-1c-1 0-1 1-1 2h-1c-2-2-2-3-3-5-1-1-1-2 0-4h-1l-1 2v-6z" class="r"></path><path d="M440 506h1c1 2 2 3 5 3l1 3c3 3 3 6 5 10-1 0-2-1-3-1-1 1-1 1-1 2 0 2-1 4-2 6h0c0-1 0-1-1-3v2h-1-1v-1c0-1 0-1-1-2-2 0-2 1-2 3-1-3-1-3 0-5-1-2-1-3 0-5v-7c1-2 0-3 0-5z" class="Z"></path><path d="M447 512c3 3 3 6 5 10-1 0-2-1-3-1-1 1-1 1-1 2 0 2-1 4-2 6h0c0-1 0-1-1-3 0-4 3-10 2-14z" class="r"></path><path d="M465 530h1c0 2 0 2 1 3v9 3h-1-9c-1 0-2 0-2 1h-1c0 1 0 0-1 1v-2-4c-1-1-1 0-1-1v-1-1-7h6c2-1 2 0 3 0 1-1 3 0 4-1z" class="f"></path><path d="M454 532l1 1c0 2 1 5 0 8h-1l-1-1c1-2 1-5 1-8z" class="B"></path><path d="M454 546v-1c0-1 0-2 2-3v1c1-1 1 0 1-1h1v1h1l2-3c0 2 0 2 1 3-2 1-2 1-4 1l-1 1c-1 0-2 0-2 1h-1z" class="I"></path><path d="M465 530h1c0 2 0 2 1 3v9 3h-1-9l1-1c2 0 2 0 4-1 1-3 0-7 1-11v-1l-1 1-1-1c1-1 3 0 4-1z" class="K"></path><path d="M440 550v-2h1c0 3 0 5 2 7 0 3 0 5 1 8l1 1c2 1 3 1 4 3l1 1h1 0l-1 2 1 1h-2c-6-1-14 0-20 0l-1-2c1 0 1 0 2-1l1-1h1c2-3 1-13 1-17h1c2 0 3 0 4-1l1-1c0 1 0 1 1 2z" class="h"></path><path d="M438 549l1-1c0 1 0 1 1 2l1 16h1l2 1-3 2h0v-1l-1-1c-1-2 0-5-2-7h-1c1-2 1-4 1-6v-2-3z" class="X"></path><path d="M440 550v-2h1c0 3 0 5 2 7 0 3 0 5 1 8l1 1c-1 2-1 2-3 2h-1l-1-16z" class="M"></path><path d="M445 564c2 1 3 1 4 3l1 1h1 0l-1 2 1 1h-2c-6-1-14 0-20 0l-1-2c1 0 1 0 2-1h1 2 5v1l1-1 1-1 1 1v1h0l3-2-2-1c2 0 2 0 3-2z" class="r"></path><path d="M445 564c2 1 3 1 4 3l1 1h1 0l-1 2-1-2c-2 0-3-1-5-1l-2-1c2 0 2 0 3-2z" class="R"></path><path d="M466 530l2 1h0 3v2 15c-1 4 0 9-1 14v11 7c0-1 0-2-1-3s-1-4-1-6h0c0-3 1-8-1-10h-3-7l-1-1c-1 0-1-2-1-3l-1-3v-1l1-7c0-1 1-1 2-1h9 1v-3-9c-1-1-1-1-1-3z" class="o"></path><path d="M469 544v1c0 1 1 1 1 2l-1 2h-1l-1-2 1-1h1v-2z" class="g"></path><path d="M466 530l2 1c2 4 1 8 1 13v2h-1l-1-1v-3-9c-1-1-1-1-1-3z" class="l"></path><path d="M457 550c0-1 0-1 1-2h1v2l1 3c1 0 2-1 3-2v-1h2 1c1 1 0 4 0 6-2 0-7 1-9-1v-5z" class="L"></path><path d="M457 545h9 1l1 1-1 1c-1 1-1 2-2 3h-2v1c-1 1-2 2-3 2l-1-3v-2h-1c-1 1-1 1-1 2-1 1-1 2-1 4 0 1-1 2-1 3l-1-3v-1l1-7c0-1 1-1 2-1z" class="m"></path><path d="M466 545h1l1 1-1 1c-1 1-1 2-2 3h-2v1c-1 1-2 2-3 2l-1-3 2-2v-2h1c1 0 2 0 3-1h1z" class="R"></path><path d="M430 516h1c1-1 1-2 2-2s1 0 1-1c1-1 0 0 2-1l1 1 1 1v2h-1v13 8l-1 1c0 2 1 3 2 4v2h-1v-2l-1 1c0 1 0 4 1 5h-2l-2 2c0 4 1 14-1 17h-1l-1 1c-1 1-1 1-2 1l1 2h0c-1 0-2 0-3 1l-1-1 1-2c-2 0-3-1-4-1v-3c1-2 3-1 4-2v-1-1l1-4c1-3 1-11 0-14l-3 1h0l-1-4h0v-3h-2l-1-1 1-2c0-2-1-5 0-8 1 0 1-1 2-2h1l1-1h0 2 2v-3c0-1 1-3 1-4z" class="i"></path><path d="M426 561l1 2c1-1 3-7 3-9l-1-1 1-1h2c1 3 0 10 0 13l-1 2-1 1c-1 1-1 1-2 1l1 2h0c-1 0-2 0-3 1l-1-1 1-2c-2 0-3-1-4-1v-3c1-2 3-1 4-2v-1-1z" class="c"></path><path d="M426 563l1 2h-1v3 1c-2 0-3-1-4-1v-3c1-2 3-1 4-2z" class="V"></path><path d="M429 531c1 1 2 1 2 2v1c1-1 1-1 2-1h1l-2-1 1-1-1-2h1l1 2c1-1 2-2 3-2v8l-1 1c0 2 1 3 2 4v2h-1v-2l-1 1c0 1 0 4 1 5h-2c-2 0-4 1-6 0 0-1 0-2 1-2 0-1 4-1 4-2h1l-1-1-2 1-2-1h-1v-3-9z" class="h"></path><path d="M429 540c1-1 2-1 3-1s2 0 3 1v1h-1c-1 0-1 0-2 1h-1l-1 1h-1v-3z" class="r"></path><path d="M430 516h1c1-1 1-2 2-2s1 0 1-1c1-1 0 0 2-1l1 1 1 1v2h-1v13c-1 0-2 1-3 2l-1-2h-1l1 2-1 1 2 1h-1c-1 0-1 0-2 1v-1c0-1-1-1-2-2h0c0-2 0-2-1-4v1c-1 1-2 1-2 2-1 0-1 1-1 2l1 1h-1-1c0-2 0-3 1-4l-1-1h-1v9c1 1 1 2 0 3h0v-3h-2l-1-1 1-2c0-2-1-5 0-8 1 0 1-1 2-2h1l1-1h0 2 2v-3c0-1 1-3 1-4z" class="I"></path><path d="M437 513l1 1v2h-1v13c-1 0-2 1-3 2l-1-2h-1l1 2-1 1 2 1h-1c-1 0-1 0-2 1v-1c0-1-1-1-2-2h0c0-2 0-2-1-4 0-1 1-1 2-2h1v2l1-1c0-1 0-2 1-3l1-1-1-2c1 0 2-1 2-1l1-1 1-5z" class="X"></path><path d="M440 499v1 4c1 1 3 1 4 1h1 4 8 11l-1 1h-3l1 1c1 1 1 2 1 3 1-1 0-1 2-2h3c1 2 1 4 1 5v2 4l1 8v3 32c-1-3 0-8-1-11l-1-3v-15-2h-3 0l-2-1h-1v-1h-5v-1c-1-1-4 0-6 0-1 0-1-1-1-1h-1v-1-3-1c-2-4-2-7-5-10l-1-3c-3 0-4-1-5-3h-1-2v-1l1-1c-1-2-2-3-2-4h-1l1-1h1c1 1 1 0 2 0z" class="R"></path><path d="M472 519c-1 1-1 2-1 4h0-1c0-2-1-5 0-7l2-1v4z" class="M"></path><path d="M473 530h-3-1v-3l1-1c1 1 1 1 3 1h0v3z" class="S"></path><g class="I"><path d="M440 499v1 4c1 1 3 1 4 1h1 4 8 11l-1 1h-3-23-1-2v-1l1-1c-1-2-2-3-2-4h-1l1-1h1c1 1 1 0 2 0z"></path><path d="M456 523l-1-1-1 1h-1c0-3-2-9-4-11h-1l1-1c1 1 1 3 3 4 0-2-2-4-3-5h0c1-1 0-1 1-1l-1-1 1-1 2 1v-1c1-1 0 0 2-1l1 1h1l1 1c1 0 1-1 2-1l4 1v2c-1-1 0-1-1 0l1 1c1 2 3 4 2 6l-2-4s-1-1-1-2c-1 0-1 0-1-1v-1h-1l-3 3c0 1 0 2-1 3v1 7z"></path></g><path d="M456 523c1-2 1-4 2-4h0l2 2c0-2-1-2-2-3 1-2 2-4 4-6 0 2 1 4 2 6 0 3 0 7 1 9l2 1c-1 1-1 1-2 1h-5v-1c-1-1-4 0-6 0-1 0-1-1-1-1 1-2 2-3 3-4z" class="Q"></path><defs><linearGradient id="K" x1="425.165" y1="585.831" x2="406.221" y2="557.945" xlink:href="#B"><stop offset="0" stop-color="#979688"></stop><stop offset="1" stop-color="#cdc5b4"></stop></linearGradient></defs><path fill="url(#K)" d="M416 534c2 1 3 1 4 2l1 1h2v3h0l1 4h0l3-1c1 3 1 11 0 14l-1 4v1 1c-1 1-3 0-4 2v3c1 0 2 1 4 1l-1 2 1 1v1c0 2 1 2 0 4v1 3 3c-1 0-1-1-2 0 0 1-1 1-1 3v1h-1l-1 1 3 3h-1c-2-1-3-2-4-3s-2-1-3-1c1 1 1 0 1 2h-1l-1-1h-1c0-1 0-2-1-3v-33-5-11c3 1 5 3 7 4l1-1h0c-1-2-1-2-3-3h-1l-1-3z"></path><path d="M417 580c2 1 2 1 3 2s1 0 2 0c0 0 2 1 2 2s-1 1-1 3c-1-2-5-5-6-7z" class="c"></path><path d="M416 550v-2c1 1 2 2 2 3 1 2 1 3 2 4l1 1c0 1 0 1-1 1-2-2-3-4-4-7z" class="B"></path><path d="M422 565c-1-2-2-2-3-4h-1c0-1-1-1-1-2l1-1c1 1 1 1 2 3h0 2v-1c2 1 3 1 4 2v1c-1 1-3 0-4 2z" class="L"></path><g class="a"><path d="M422 568c1 0 2 1 4 1l-1 2 1 1v1l-1 1h-4v1c-1 0-1-1-1-1 1-2 1-4 2-6z"></path><path d="M421 575v-1h4l1-1c0 2 1 2 0 4v1 3 3c-1 0-1-1-2 0 0-1-2-2-2-2-1 0-1 1-2 0s-1-1-3-2l1-2-1-3h1c1 0 2 1 3 1h0v-1z"></path></g><path d="M421 575v-1h4l1-1c0 2 1 2 0 4v1 3h-4s-1-2-2-3v-1l2 1s1 1 2 1l1-1-1-1h-2l-1-1v-1z" class="c"></path><path d="M416 534c2 1 3 1 4 2l1 1h2v3h0l1 4h0l1 2h0c-1 1 0 3 0 5h-1-1c0 3 1 6-1 8 0-1-1-2-1-3l-1-1c-1-1-1-2-2-4l3 1h0c0-2-1-3 0-5v-2c0-2 0-2-1-4l1-1h0c-1-2-1-2-3-3h-1l-1-3z" class="F"></path><path d="M416 534c2 1 3 1 4 2l1 1h2v3h0l1 4h0l1 2h0c-1 1 0 3 0 5h-1-1v-9c-1-1-1 0-2-1v-1h0c-1-2-1-2-3-3h-1l-1-3z" class="E"></path><path d="M413 537c3 1 5 3 7 4 1 2 1 2 1 4v2c-1 2 0 3 0 5h0l-3-1c0-1-1-2-2-3v2 3h-2-1v-5-11z" class="C"></path><path d="M413 548l1-4c1 1 0 1 1 2 0 0 1 1 1 2h0v2 3h-2-1v-5z" class="F"></path><path d="M472 513v-4l3-1v2c0 3 1 8 0 11 0 1-1 5 0 6v1c1 1 2 1 2 2l1 1c0 2 0 3 2 4h4c1 1 0 1 0 3s-1 4-1 7v16c0 3 1 6-1 8h-1v1h2v1h-2v1c1 1 3 0 4 0h1 10c-2 1-5 1-7 1-1 0-4 0-5 1v2l-2 6-5 12-2 7-2-1-2 2v-3-1c-1-6 0-12-1-18h0v-7-11c1-5 0-10 1-14l1 3c1 3 0 8 1 11v-32-3l-1-8v-4-2z" class="v"></path><path d="M471 548l1 3c1 3 0 8 1 11v38l-2 2v-3-1c-1-6 0-12-1-18h0v-7-11c1-5 0-10 1-14z" class="O"></path><path d="M512 571h1v1c-1 2-2 6-4 7 0 1-1 2-2 4h1c0 1 0 1 1 1-1 2-2 4-2 6-1 1-2 3-3 4l-1 1-2 2-2 2h0c-2 2-3 4-5 6v2h-1c-4 1-7 0-11 0l-10 1c1-1 1-2 1-3l2-4 2-7 5-12 2-6v-2c1-1 4-1 5-1 2 0 5 0 7-1h-10 0c1-1 1-1 3 0h0c2-2 19-1 21-1l1 1 1-1z" class="g"></path><path d="M484 576l1-1h0c1 1 1 1 1 2-1 1-1 5-3 6 0-1-1-1-1-1l2-6z" class="Z"></path><path d="M496 580c2-2 4-3 4-5-2 1-3 3-5 5l2-5h1v-1c2 0 4-2 6-1v2l-5 7-1-1-2 1v-2z" class="Q"></path><path d="M485 593c1 0 2-1 3-1h1v-2l3-3c1-3 2-5 4-7v2l2-1 1 1-4 7 1 1v1 1l-5 4c0 1 0 1-1 2-1 0-1 0-2-1l1-1-1-1v-1h-3c-1 1-2 1-3 1l3-2z" class="a"></path><path d="M496 582l2-1 1 1-4 7-1 1v-3c1 0 1 0 1-1 0-2 0-3 1-4z" class="E"></path><path d="M489 596l1-1c0-1-1-1-1-2 1-1 2-2 3-2 0 0 1 1 1 2-1 1-1 1-1 2l-1 1c0 1 0 1-1 2-1 0-1 0-2-1l1-1z" class="J"></path><path d="M477 594c3 1 6-2 9-3v1l-2 1h1l-3 2c1 0 2 0 3-1h3v1l1 1-1 1c1 1 1 1 2 1l1 1-1 1v1l-1 1c-1 1-2 2-3 2v3h7c-4 1-7 0-11 0l-10 1c1-1 1-2 1-3l2-4 2-7z" class="X"></path><path d="M477 594c3 1 6-2 9-3v1l-2 1h1l-3 2-2 1h-2 0v1l-1 2h0 2 0c-2 2-3 4-4 6 1 0 0 0 1 1h1 1 2l1-1 1 2-10 1c1-1 1-2 1-3l2-4 2-7z" class="n"></path><path d="M478 606v-1c1-1 2-2 2-3v-1c1-2 5-3 7-4h0 1c1 1 1 1 2 1l1 1-1 1v1l-1 1c-1 1-2 2-3 2v3h7c-4 1-7 0-11 0l-1-2-1 1h-2z" class="k"></path><path d="M488 597c1 1 1 1 2 1l1 1-1 1v1l-1 1c-1 1-2 2-3 2v-3l1-1c0-1 0-2 1-3z" class="K"></path><path d="M512 571h1v1c-1 2-2 6-4 7 0 1-1 2-2 4h1c0 1 0 1 1 1-1 2-2 4-2 6-1 1-2 3-3 4l-1 1-2 2-2 2h0c-2 2-3 4-5 6v2h-1-7v-3c1 0 2-1 3-2l1-1v-1l1-1-1-1c1-1 1-1 1-2l5-4v-1-1l-1-1 4-7 5-7h1l3-3c-4 0-8-1-12 0h-10 0c1-1 1-1 3 0h0c2-2 19-1 21-1l1 1 1-1z" class="E"></path><path d="M489 602l1 1 1-1c1-1 2-2 4-3-1 2-2 4-4 5l1 1h2v2h-1-7v-3c1 0 2-1 3-2z" class="T"></path><path d="M507 583h1c0 1 0 1 1 1-1 2-2 4-2 6-1 1-2 3-3 4l-1 1-2 2-2 2h0l1-4c2-2 4-5 5-8 1-2 2-3 2-4z" class="X"></path><path d="M508 572h2 0l-1 2v1 1c-1 2-2 4-2 5-1 2-1 2-2 3-2 3-3 6-5 9-1 0-1 1-1 1l-6 6h-1v-1h0c3-2 4-4 5-6l3-4c-1 0-2 0-3 1h-1l-1-1 4-7 5-7h1l3-3z" class="T"></path><path d="M500 586l3-2v1c0 1-1 3-3 4-1 0-2 0-3 1 0-1 0-1 1-2l2-2z" class="I"></path><path d="M499 582l5-7h1c-1 2-2 3-3 4l1 1h1l-3 4s0 1-1 2l-2 2c-1 1-1 1-1 2h-1l-1-1 4-7z" class="d"></path><path d="M451 562l1-1c1-2 1-4 2-6v-1l1 3c0 1 0 3 1 3l1 1h7 3c2 2 1 7 1 10h0c0 2 0 5 1 6s1 2 1 3h0c1 6 0 12 1 18v1 3l2-2 2 1-2 4c0 1 0 2-1 3v2l1 21h0l-6 1c-1 0-2-1-4-1h-2l-1-3c-1 1-1 1-2 1v-9-7-5l-1-3v-1c-1-2-1-3-1-5 0-4 0-8-1-12h0v-1-1-9c0-1 0-1-1-2v-2c-1-2 0-3-1-4h-2 0v-6h0z" class="b"></path><path d="M464 631c0-3-1-10 1-12 0 1 0 2 1 3h0v8l-2 1z" class="Z"></path><path d="M466 620c1 1 1 3 1 5v4s1 0 1 1h1 1 2l1 1h0l-6 1c-1 0-2-1-4-1h1l2-1v-8-2z" class="e"></path><path d="M462 579c0-1 1-1 1-1l1 1h3v3h-1v-1h-1v33l-1-9c-1 2 0 4-1 6h0v-28-3h0l-1-1z" class="K"></path><defs><linearGradient id="L" x1="472.507" y1="600.199" x2="465.941" y2="599.506" xlink:href="#B"><stop offset="0" stop-color="#393735"></stop><stop offset="1" stop-color="#51504d"></stop></linearGradient></defs><path fill="url(#L)" d="M467 579c1-1 1-6 1-8 0 2 0 5 1 6s1 2 1 3h0c1 6 0 12 1 18v1 3l2-2 2 1-2 4c0 1 0 2-1 3v2l1 21-1-1h-2-1-1c0-1-1-1-1-1v-4c0-2 0-4-1-5v-8c1-1 1-3 2-4-1-2 0-6 0-8v-13c0-2 0-4-1-5v-3z"></path><path d="M468 608c1 5 1 12 0 17h-1c0-2 0-4-1-5v-8c1-1 1-3 2-4z" class="o"></path><path d="M472 610l1 21-1-1h-2-1-1l1-4v-5l1 1v-6h1l-1-2c0-2 0-2 2-4z" class="O"></path><path d="M451 562l1-1c1-2 1-4 2-6v-1l1 3c0 1 0 3 1 3l1 1h7 3c2 2 1 7 1 10h0c0 2 0 7-1 8h-3l-1-1s-1 0-1 1l1 1h0v3 28h0v5c-1-1-1-3-1-5-1-4 0-9 0-13v-3c-1 2-1 5-1 8v16h0l-1-3c-1 1-1 4-1 7h0 0l-1-3v-7-5l-1-3v-1c-1-2-1-3-1-5 0-4 0-8-1-12h0v-1-1-9c0-1 0-1-1-2v-2c-1-2 0-3-1-4h-2 0v-6h0z" class="I"></path><path d="M460 600v16c-1 1-1 4-1 7h0 0l-1-3v-7c2-3 1-6 1-8 1-2 0-4 1-5z" class="k"></path><path d="M464 561h3c2 2 1 7 1 10h0c0 2 0 7-1 8h-3l-1-1s-1 0-1 1v-2h-1v2h-1v-3l2-1-1-1h-1l-1-5c0-2 0-3 1-5 0-1 0-1 1-2h0v2h1v-1l1-1 1 1c-1 1-1 2-1 3l1-1c1-2 1-2 0-4z" class="n"></path><path d="M465 570h1v-1h1c0 1 0 1-1 2v5s-1 0-1 1l-2 1s-1 0-1 1v-2h-1v2h-1v-3h4v-4h0l1-2z" class="k"></path><path d="M460 564c0-1 0-1 1-2h0v2h1v-1l1-1 1 1c-1 1-1 2-1 3l1-1 1 1 1-1v1c-1 1-1 1-2 1 0 1 0 1 1 1v2l-1 2h0v4h-4l2-1-1-1h-1l-1-5c0-2 0-3 1-5z" class="r"></path><path d="M451 562l1-1c1-2 1-4 2-6v-1l1 3c0 1 0 3 1 3l1 1h7c1 2 1 2 0 4l-1 1c0-1 0-2 1-3l-1-1-1 1v1h-1v-2h0c-1 1-1 1-1 2l-1-1h0c-1 1-1 4-2 6 0 1 1 3 1 5s-1 2 0 4l2 1v13 8c-1 1 0 3-1 5 0 2 1 5-1 8v-5l-1-3v-1c-1-2-1-3-1-5 0-4 0-8-1-12h0v-1-1-9c0-1 0-1-1-2v-2c-1-2 0-3-1-4h-2 0v-6h0z" class="T"></path><path d="M456 576c1 2 1 7 1 9 1 7 1 15 1 23l-1-3v-1c-1-2-1-3-1-5 0-4 0-8-1-12h0v-1-1-9h1z" class="X"></path><path d="M451 562l1-1c1-2 1-4 2-6v-1l1 3c0 1 0 3 1 3l1 1h-1v10 5h-1c0-1 0-1-1-2v-2c-1-2 0-3-1-4h-2 0v-6h0z" class="k"></path><path d="M507 537h15l1 2 1-1v1 1c1 0 2-1 2-1v1l-2 1v3 1l-1 24-1 1-1-1h-9v2l-1 1-1-1c-2 0-19-1-21 1h0c-2-1-2-1-3 0h0-1c-1 0-3 1-4 0v-1h2v-1h-2v-1h1c2-2 1-5 1-8v-16c0-3 1-5 1-7 3 0 5-1 7-1 1 1 1 1 2 0h9 1c1 0 1 0 2 1h0c1-1 2-1 2-1z" class="i"></path><path d="M498 562c0-1 1-1 2-2v1l1 1v4l-2-2-1-2z" class="Q"></path><path d="M509 553h0v-1h-4l-1-1h2l1-1h-3v-2h2l1-1c0 1 1 1 1 1 0 1 3 1 3 1v1l-1 1c0 1 1 1 1 2-1 0-1 1-2 0z" class="n"></path><path d="M511 553h1 3l-4 1v1c1 0 2 1 3 1l1 1c1 0 1 0 3-1v1l-1 1h-2v1c1 1 1 1 2 1h-3l-1-1-2 1h-5c-1 0-1 0-2-1l1-1h2 0l-2-1v-1c2 1 4 1 6 1v-1c-2-1-4-1-6-1l-1-1h2l3-1c1 1 1 0 2 0z" class="K"></path><path d="M515 553c1 1 2 0 3 1h1c1 2 1 7 1 10h-1-4-6-4l1-2h2c3 0 6 0 8 1h1l2-2-2-1c-1 0-1 0-2-1v-1h2l1-1v-1c-2 1-2 1-3 1l-1-1c-1 0-2-1-3-1v-1l4-1z" class="Q"></path><path d="M507 547c3 0 8 1 10 0v-1c-4 0-9-1-12 0 0-1 0-1 1-1 1-2 3-2 4-2 2-1 3-1 5-1 1 1 0 1 2 1h1v2h1c0 1 1 2 0 3 0 1 0 2 1 3v1l-1 1h-1c-2-1-4-1-5-1l-1 1h-1c0-1-1-1-1-2l1-1v-1s-3 0-3-1c0 0-1 0-1-1z" class="T"></path><path d="M503 569c0-2 0-4 1-5h1 4 6 4 1v2l2 2-1 1h-9-1-8z" class="V"></path><path d="M511 569c-1-1-2-2-2-3 2-1 3 0 5-1 2 0 2 0 4 1-2 2-5 0-6 3h-1z" class="P"></path><path d="M518 566h2l2 2-1 1h-9c1-3 4-1 6-3z" class="B"></path><path d="M507 537h15l1 2 1-1v1 1c1 0 2-1 2-1v1l-2 1v3 1l-1 24-1 1-1-1 1-1c0-2-1-8-1-11h-1c1-1 1-2 1-2v-3-9c-1-1-1-1-2-1-2-1-4-1-6-1-4 0-8-1-11 3-1 1-1 4-1 5v13l-1-1v-1c-1 1-2 1-2 2v-2c-1-1-1-8-1-9 0-2-1-6 0-7 1 0 2-1 3 0h0l2-2c1-1 3-2 4-3s1 0 1-1v-1z" class="I"></path><path d="M513 541l4-3c1 0 0 0 1 1 0 1 1 1 2 2l-1 1c-2-1-4-1-6-1z" class="G"></path><path d="M497 551l2-1 1 1v4h-1v-1c-1 1-1 1-1 2v1 3c-1-1-1-8-1-9z" class="T"></path><path d="M502 537h1c1 0 1 0 2 1h0c1-1 2-1 2-1v1c0 1 0 0-1 1s-3 2-4 3l-2 2h0c-1-1-2 0-3 0-1 1 0 5 0 7 0 1 0 8 1 9v2l1 2 2 2v2s1 0 2 1h8 1v2l-1 1-1-1c-2 0-19-1-21 1h0c-2-1-2-1-3 0h0-1c-1 0-3 1-4 0v-1h2v-1h-2v-1h1c2-2 1-5 1-8v-16c0-3 1-5 1-7 3 0 5-1 7-1 1 1 1 1 2 0h9z" class="e"></path><path d="M485 567c1-1 2-1 2-1 1-1 0 0 2 0l1 1c1 1 1 1 2 0v1h4l1 1c-1 0-2 1-4 0h0-9c0-1 1-2 1-2z" class="K"></path><path d="M499 564l2 2v2s1 0 2 1h8 1v2l-1 1-1-1c0-1 1-1 0-1h-6c-2-1-5-2-7-1l-1-1h-4v-1c-1 1-1 1-2 0l-1-1 5-1h3 1l1-1z" class="r"></path><path d="M485 561v-5c3-1 6-1 9 0h1c-1 1-1 1-2 1 1 1 0 1 1 2v1s1 1 2 1v1c0 1 0 2 1 3h-3l-5 1c-2 0-1-1-2 0 0 0-1 0-2 1l-1-1c0-2 0-3 1-5z" class="O"></path><path d="M496 562c0 1 0 2 1 3h-3l-5 1c-2 0-1-1-2 0 0 0-1 0-2 1 0-2 0-2 1-3l10-2z" class="b"></path><path d="M485 561h2c0-2-1-2 0-3h4v2c-1 1-3 2-4 3l-1 1c-1 1-1 1-1 3l-1-1c0-2 0-3 1-5z" class="M"></path><path d="M451 568h2c1 1 0 2 1 4v2c1 1 1 1 1 2v9 1 1h0c1 4 1 8 1 12 0 2 0 3 1 5v1l1 3v5 7 9l-3-1-1 3-1 1v5c-2 1-2 1-3 0 0 1-1 0 0 2l1 1h1l1 1c0 2 0 2-2 4h2v3h0-16-1c-3-1-7-1-10-1l1 2-1 1c0 1-1 2-1 3v2l-1-1c-2 1-2 2-3 3h2l1 1h-3l-1-1h-2-1l-4 1h-15c2-4 1-9 1-13l1-1v-1l-1-1c-1 0-1-1-1-1 0-4-1-7-1-11h0-2c0-1 0-2 1-2h0 1v-2h1c1 2 1 2 1 4v2c1 1 0 0 1 0h6l7 1-2-1c-1 0-2 0-3-1 1 0 2-1 2-1h1l1-1c1-1 1-2 2-4l-1-1v-3-28-7c1 1 1 2 1 3h1l1 1h1c0-2 0-1-1-2 1 0 2 0 3 1s2 2 4 3h1l-3-3 1-1h1v-1c0-2 1-2 1-3 1-1 1 0 2 0v-3-3-1c1-2 0-2 0-4v-1c1-1 2-1 3-1h0c6 0 14-1 20 0h2l-1-1 1-2z" class="m"></path><path d="M423 592h1 1c1 2 0 4 1 6v16l-2-1v-4-7l1-1c-1-2-2-4-1-6h1l-2-3z" class="a"></path><path d="M422 608l2 1v4l2 1v3l1 1h0c-1 2 0 5-1 7 0 1-1 1 0 2v2c0 1-1 1-1 1h-1-2-1c0-1 0-1 1-2h1 0c0-2 0-2-1-3l-1-1h1v-1c0-2 0-3-1-5h-3c1-1 2-1 3-1 1-2 1-2 1-3-1-2-1-3-1-4l1-2z" class="T"></path><path d="M422 608l2 1v4c0 1 0 2-1 4l1 4c0 1 0 2 1 3h-3v-1c0-2 0-3-1-5h-3c1-1 2-1 3-1 1-2 1-2 1-3-1-2-1-3-1-4l1-2z" class="N"></path><path d="M417 619v-2l1 1h3c1 2 1 3 1 5v1h-1l1 1c1 1 1 1 1 3h0-1c-1 1-1 1-1 2h-3-1-2-1-2v-1c1-1 1-2 2-4l-1-1v-3l1-2h3z" class="G"></path><path d="M413 621l1-2h3v2c-1 0-1-1-2-1h0v1 3l-1 1-1-1v-3z" class="F"></path><path d="M417 630v-2c-1-1-1-2-1-3 1-2 2-1 4-1l-1-2v-1l2 2h1v1h-1l1 1c1 1 1 1 1 3h0-1c-1 1-1 1-1 2h-3-1z" class="E"></path><path d="M413 586c1 1 1 2 1 3h1l1 1h1c0-2 0-1-1-2 1 0 2 0 3 1s2 2 4 3l2 3h-1c-1 2 0 4 1 6l-1 1v7l-2-1v-4c-1-4-3-6-5-9h0l-4-2v-7z" class="V"></path><path d="M413 586c1 1 1 2 1 3h1l1 1h-1-1l1 1c1 1 2 2 2 3v1l-4-2v-7z" class="E"></path><path d="M413 593l4 2h0c2 3 4 5 5 9v4l-1 2c0 1 0 2 1 4 0 1 0 1-1 3-1 0-2 0-3 1l-1-1v2h-3l-1 2v-28z" class="C"></path><path d="M447 619l5 1c1 1 0 4 0 6 1 1 0 2 0 3v2h-6c-2 0-4 0-5-1l-1 1c-1 1-2 1-3 1l-1 1v-1c-1-1 0-1-1-2h-4c0-2 0-5 1-6v-2h0l2-1 1 1h0v-2h5 0 1 0v2c1-2 2-2 4-2v1l2-1v-1z" class="c"></path><path d="M440 620h0 1 0c-1 2-1 3-1 5v4l2-1h1l1-1 1 1 1-1h1v2h-4c1 1 1 1 2 1s1 0 1 1h0c-2 0-4 0-5-1l-1 1c-1 1-2 1-3 1l-1-1v-1c1-1 2-1 3-1v-3l-2 2-2-3c1-2 1-3 2-4h1c1 0 1 0 1-1h1z" class="T"></path><path d="M441 620v2c1-2 2-2 4-2v1l2-1 1 1h0c-1 1-1 2-1 3v1 2h-1l-1 1-1-1-1 1h-1l-2 1v-4c0-2 0-3 1-5z" class="I"></path><path d="M440 625c0-1 0-1 1-2v2c2 0 3 1 3 0l1-1c1 0 0 0 1 1v2l-1 1-1-1-1 1h-1l-2 1v-4z" class="r"></path><path d="M447 619l5 1c1 1 0 4 0 6 1 1 0 2 0 3v2h-6 0c0-1 0-1-1-1s-1 0-2-1h4v-2-2-1c0-1 0-2 1-3h0l-1-1v-1z" class="X"></path><path d="M433 585l17 1h5v1h0-1c-1 0 0 0-1 1-1 0-1 1-2 1h0l-2 2v11h0c1 2 0 2 0 3v1c0 1 1 2 0 3s0 1-1 1c-3-1-6-1-9-1h-3c-1 0-1-1-1-1v-1h-2v-1c0-1 1-2 1-3h-4v-1c1 0 2 0 3-1v-3h-2 0l1-1c1 0 1 0 1-1h-2v-1h2v-1c-1 0-3 0-4-1 2 0 3 0 4-1h-2v-1h-5c0-1 0-2-1-3v-1l1-1c2-1 4 0 7 0v-1z" class="r"></path><path d="M450 586h5v1h0-1c-1 0 0 0-1 1-1 0-1 1-2 1h0c-3-1-6 0-9-1h-5l3-1c3 0 7 1 10-1z" class="e"></path><path d="M433 585l17 1c-3 2-7 1-10 1l-3 1c-1 0-3 1-4 1l-1-1v-1h3v-1h-2v-1z" class="O"></path><path d="M433 601l4-3h0v1c-1 2 0 4 0 6l1 1h-1c0 1 1 1 1 2h-3v-1h-2v-1c0-1 1-2 1-3h-4v-1c1 0 2 0 3-1z" class="a"></path><path d="M434 603h2v1 1l1 1c0 1 1 1 1 2h-3v-1h-2v-1c0-1 1-2 1-3z" class="K"></path><path d="M425 587l1-1c2-1 4 0 7 0h2v1h-3v1l1 1h-3v1h3v1h-2-5c0-1 0-2-1-3v-1z" class="M"></path><path d="M438 606c2-1 2 0 4-1l1 2v1c1-1 0-2 2-2v2h1 0v-1l1-1 1 2v-1l1-1c0 1 1 2 0 3s0 1-1 1c-3-1-6-1-9-1h-3c-1 0-1-1-1-1h3c0-1-1-1-1-2h1z" class="n"></path><path d="M451 568h2c1 1 0 2 1 4v2c1 1 1 1 1 2v9 1 1-1h-5l-17-1v1c-3 0-5-1-7 0l-1 1v5h-1l-3-3 1-1h1v-1c0-2 1-2 1-3 1-1 1 0 2 0v-3-3-1c1-2 0-2 0-4v-1c1-1 2-1 3-1h0c6 0 14-1 20 0h2l-1-1 1-2z" class="i"></path><path d="M426 577h3s0 2 1 2l-3 1h0l-1-2v-1z" class="Y"></path><path d="M426 572c1-1 2-1 3-1h3v1 1 1c-2 0-2 0-3 1l1 1h0c1-1 0-1 1 0l1 2h-1c-1 0-1 0-1 1h0c-1 0-1-2-1-2h-3c1-2 0-2 0-4v-1z" class="R"></path><path d="M426 584v1l2-1h1 3l-1-1c1-1 1-1 1-2h-2l-1-1h5v4h-1v1 1c-3 0-5-1-7 0l-1 1v5h-1l-3-3 1-1h1v-1c0-2 1-2 1-3 1-1 1 0 2 0z" class="h"></path><path d="M434 580h1c1-1 1-1 2-1 0 1 0 2 1 2v-2h1c0 1 0 3 1 4v-4h1v1c0 1 0 2 1 3 2 0 3 1 5 1 3 0 6 0 8 2h-5l-17-1v-1h1v-4z" class="E"></path><path d="M451 568h2c1 1 0 2 1 4v2c1 1 1 1 1 2v9 1 1-1c-2-2-5-2-8-2-2 0-3-1-5-1-1-1-1-2-1-3v-1h-1l1-1 1 1 1-1c1 1 0 1 1 2 1-1 1-2 1-4h-2l-1-1 2-1h0 2v1c1-1 1 0 2-1l1-3h2l-1-1 1-2z" class="l"></path><path d="M449 571h2l1 1c0 1 0 3-1 4 0 2 1 4 0 5-1-1-1-3-1-4v-2h-1c-1 0-1 0-1-1l1-3z" class="M"></path><path d="M451 589c1 0 1-1 2-1 1-1 0-1 1-1h1c1 4 1 8 1 12 0 2 0 3 1 5v1l1 3v5 7 9l-3-1-1 3-1 1-1-3c0-1 1-2 0-3 0-2 1-5 0-6l-5-1-2-1h-11c-1 0-3 0-4-1h5v-1h-5c-1-2-2-2-3-4 2-1 3-1 5-1v-1c1-1 1-1 1-2h-1l1-1h2v1s0 1 1 1h3c3 0 6 0 9 1 1 0 0 0 1-1s0-2 0-3v-1c0-1 1-1 0-3h0v-11l2-2h0z" class="I"></path><path d="M446 612c1 0 2 0 3 1 1 0 1 2 2 3h-2-2l-1-2c-1-1 0-1 0-2z" class="L"></path><path d="M451 589c1 0 1-1 2-1 1-1 0-1 1-1h1c1 4 1 8 1 12 0 2 0 5-1 7l-1-3-1 3v4h0-1 0l-1-18v-3z" class="O"></path><path d="M454 587h1c1 4 1 8 1 12 0 2 0 5-1 7l-1-3v-16z" class="g"></path><path d="M433 607h2v1l-1 1v1c3 2 5 1 8 1h3v1h1c0 1-1 1 0 2l1 2h2l-1 1h0c-4 0-9 1-13 0v-1h-5c-1-2-2-2-3-4 2-1 3-1 5-1v-1c1-1 1-1 1-2h-1l1-1z" class="k"></path><path d="M434 610c3 2 5 1 8 1h3v1h1c0 1-1 1 0 2l1 2h0-11v-1-1h-2l1-1c-1-1-1-2-1-3z" class="E"></path><path d="M434 610c3 2 5 1 8 1l-3 2v1c1 1 0 0 0 1h-1l-1-1h-1-2l1-1c-1-1-1-2-1-3z" class="f"></path><defs><linearGradient id="M" x1="449.949" y1="619.259" x2="457.051" y2="617.241" xlink:href="#B"><stop offset="0" stop-color="#2c2a2b"></stop><stop offset="1" stop-color="#42433b"></stop></linearGradient></defs><path fill="url(#M)" d="M455 606c1-2 1-5 1-7 0 2 0 3 1 5v1l1 3v5 7 9l-3-1-1 3-1 1-1-3c0-1 1-2 0-3 0-2 1-5 0-6l-5-1-2-1h7v-8h0 1 0v-4l1-3 1 3z"></path><defs><linearGradient id="N" x1="451.555" y1="621.618" x2="461.899" y2="612.921" xlink:href="#B"><stop offset="0" stop-color="#575452"></stop><stop offset="1" stop-color="#6d6f69"></stop></linearGradient></defs><path fill="url(#N)" d="M455 606c1-2 1-5 1-7 0 2 0 3 1 5v1l1 3v5 7 9l-3-1v-7-15z"></path><path d="M397 626h1c1 2 1 2 1 4v2c1 1 0 0 1 0h6l7 1-2-1c-1 0-2 0-3-1 1 0 2-1 2-1h1l1-1v1h2 1 2 1l2 1h1 0c1 1 1 0 2 0l1 1c2-1 2-1 4 0l-1 2v2c0 2 0 3 1 4v1c1-1 1-1 2-1 0-3 0-6-1-7 1-1 2-1 3-1 1 1 1 1 1 2v-1h2 1v1-1l1-1c1 0 2 0 3-1l1-1c1 1 3 1 5 1h6v-2l1 3v5c-2 1-2 1-3 0 0 1-1 0 0 2l1 1h1l1 1c0 2 0 2-2 4h2v3h0-16-1c-3-1-7-1-10-1l1 2-1 1c0 1-1 2-1 3v2l-1-1c-2 1-2 2-3 3h2l1 1h-3l-1-1h-2-1l-4 1h-15c2-4 1-9 1-13l1-1v-1l-1-1c-1 0-1-1-1-1 0-4-1-7-1-11h0-2c0-1 0-2 1-2h0 1v-2z" class="k"></path><path d="M440 640h11 1l1 1c0 2 0 2-2 4h2v3h0-16c0-1-1-1-1-1l1-1-1-2h2c1 1 0 1 2 0v1l2-1c1 1 0 2 1 3h0l1-1c0 1 0 0 1 1v-2h3v1l1 1c0-1 1-2 1-3l-1-1v-2l-2 2v-2l-1 1c-1-1-1-1-2 0h0-1c-1-1-1-1-2-1l-1-1z" class="K"></path><path d="M452 629l1 3v5c-2 1-2 1-3 0 0 1-1 0 0 2l1 1h-11l-1-1s0 1-1 1-2-1-3-1-2 1-3 1c-1-3 0-4 1-6v-1h2 1v1-1l1-1c1 0 2 0 3-1l1-1c1 1 3 1 5 1h6v-2z" class="Z"></path><path d="M433 634v-1h2 1c-1 2-2 3-2 5h2l1 1h-2c-1 0-2 1-3 1-1-3 0-4 1-6z" class="R"></path><path d="M440 631c1 2 3 4 2 6v1h-2c-1-1-2-3-4-4v-1l1-1c1 0 2 0 3-1z" class="B"></path><path d="M397 626h1c1 2 1 2 1 4v2c1 1 0 0 1 0h6l7 1h9 3v1h-3v1h1c-3 1-5 0-6 2 1 1 1 1 2 0v1c2-1 3-1 4-2v3h-1l-1-1c0 2 0 2 1 3h1v2h-1-2-1-1c0-1-1-1-1-1l-1 1h0c-2 0-2 0-4 1l-2-1c0-1 1-3 0-4h-1v4h-3-2-4l-1-1c-1 0-1-1-1-1 0-4-1-7-1-11h0-2c0-1 0-2 1-2h0 1v-2z" class="V"></path><path d="M397 626h1c1 2 1 2 1 4v2c1 1 0 0 1 0h6l7 1h9 3v1h-3v1c-2-1-6-2-9-1h0-1c-1 0-3 0-4 1-2 0-2 0-3-1l-2 1h0c-1-1-3-1-4-1-1 2 0 5 0 8-1 0-1-1-1-1 0-4-1-7-1-11h0-2c0-1 0-2 1-2h0 1v-2z" class="i"></path><path d="M408 635c1-1 3-1 4-1h1 0c3-1 7 0 9 1h1c-3 1-5 0-6 2 1 1 1 1 2 0v1c2-1 3-1 4-2v3h-1l-1-1c0 2 0 2 1 3h1v2h-1-2-1-1c0-1-1-1-1-1l-1-2 1-1v-2l-2 1v-1c-1 0-1 0-2 1l-2-1-1 1h-1l1-1-1-1c0-1-1-1-1-1z" class="T"></path><path d="M400 643h4 2 3v-4h1c1 1 0 3 0 4l2 1c2-1 2-1 4-1h0l1-1s1 0 1 1l2 2h1 2l1 2h2l1 2-1 1c0 1-1 2-1 3v2l-1-1c-2 1-2 2-3 3h2l1 1h-3l-1-1h-2-1l-4 1h-15c2-4 1-9 1-13l1-1v-1z" class="I"></path><path d="M417 642s1 0 1 1l2 2h1 2l1 2h2l1 2-1 1c0 1-1 2-1 3v2l-1-1c-2 1-2 2-3 3h2l1 1h-3l-1-1h-2-1c-1-1-1-1-1-3s0-2 1-3v-2c-1-1 0-1 0-2 1-1 0-1 1-1l1-1h-1 0-1-1c-1 0-2 0-3-1h-1c2-1 2-1 4-1h0l1-1z" class="b"></path><path d="M424 647h2l1 2-1 1c0 1-1 2-1 3v-1c-1-1-1-1-1-2h-1l1-3h0z" class="O"></path><path d="M417 642s1 0 1 1l2 2h1 2l1 2h0c-1 0-1-1-2-2l-1 1v1l1 1v4h0l-1-1c-1 1-2 2-3 2h-1l-1 1c0-2 0-2 1-3v-2c-1-1 0-1 0-2 1-1 0-1 1-1l1-1h-1 0-1-1c-1 0-2 0-3-1h-1c2-1 2-1 4-1h0l1-1z" class="K"></path><path d="M533 600v2 3l2 2 1 1 1 9v5h1v1h-4l3 2v3c-1 1-1 1 0 3v2l1 1h0 1 0c1 1 1 1 1 2l2-1c1 4 1 7 0 11v4h1 2c0 1 1 1 1 2v2h-1 0c-1 1-1 1-1 2 1 1 2 1 4 1-2 1-4 1-6 1h-3-3-3 0-1-1-1l-1-1c-2 1-6 1-9 1-1 0-3 0-5-1h-1v1c-3-1-7-1-10-1h-13c-4 1-9-1-13 0l-1 1c-2-2-6-1-8-1h-25-3c-3-1-8 0-12 0h-6-2c1-1 1-2 3-3l1 1v-2c0-1 1-2 1-3l1-1-1-2c3 0 7 0 10 1h1 16 0v-3h-2c2-2 2-2 2-4l-1-1h-1l-1-1c-1-2 0-1 0-2 1 1 1 1 3 0v-5l1-1 1-3 3 1c1 0 1 0 2-1l1 3h2c2 0 3 1 4 1l6-1h0l-1-21v-2l10-1c4 0 7 1 11 0h1 16 3c5 1 12 0 17 0v-1h1c0-2 0-2-1-4 1-1 2-1 3-2z" class="O"></path><path d="M494 649h1l1 2c-1 1-1 1-1 3h-2c-1 0-1 2-2 2-2-1-2-2-2-3h1l-1-1 1-2v-1h4z" class="X"></path><path d="M490 649h4l-4 4-1-1 1-2v-1z" class="d"></path><path d="M475 648v-3h1c2 2 0 3 2 5l2-1h3l-1 1v1l2 1c-1 1-2 1-3 2l-1 1c-1 0-1-1-1-2h-1v1h-1c-1 1-1 1-2 1l-1-1h-1l-2-1c0-2 0-2 1-4h3v-1z" class="b"></path><path d="M480 649v-4-1-1c0-2 1-2 2-2h1v8h1 2l1-1h-1v-1h0c1 0 2 0 3-1l1 2v1 1l-1 2 1 1h-1l-1 1v1l-1 1v-2h-1c0 1-1 1-2 2h0v-2-1l-1 2-2-1c1-1 2-1 3-2l-2-1v-1l1-1h-3z" class="n"></path><path d="M501 646h2l1 2h1v-2h2c1 1 1 2 2 2l1-1c0 2 0 3 1 5h1 0v1 3c-1-1-1-1-1-3-1 1-1 2-1 3-2 0-2 0-3-1l-1-2c-1 1-1 2-2 2v1-1c-1-1 0-1-1-1l-1 1h-1v-5h0v-4z" class="I"></path><path d="M501 646h2l1 2h1v-2h2l-1 2h1l1 1h-1c-1 0-2 1-3 2v4c-1-1 0-1-1-1l-1 1h-1v-5h0v-4z" class="J"></path><path d="M496 640v-1h2c1 1 3 1 3 2v5 4h0v5l-1-1v2h-2v-2h-2-1c0-2 0-2 1-3l-1-2h-1c0-1 0-2-1-4h2l1 1 1-5-1-1z" class="h"></path><path d="M494 649c0-1 0-2-1-4h2l1 1c0 1 0 3 1 4h1v1h-1l-1 3h-1c0-2 0-2 1-3l-1-2h-1z" class="k"></path><path d="M461 644v-2l1-1h3 1c1 1 1 1 2 1 3 2 2 4 2 7v5 1c-3-1-3 0-4-1v-4l-1 1v3 1c-1-1-1-1-2 0 0-2 0-5-1-6v-1c0-2 0-2-1-4z" class="i"></path><path d="M491 637v-1h2c1-1 1 0 1-1l-1-1 1-1h7v2h-4l1 1c1 0 2 0 3 1l-3 1h4c-1 1-2 1-4 1h-2v1l1 1-1 5-1-1h-2c1 2 1 3 1 4h-4v-1l-1-2c-1 1-2 1-3 1h0c0-1 0 0-1-1v-1h1v-1c1-1 1-2 1-3h0l2 2v-1-2h-1l1-1h6v-1c-1 0-2 0-4-1z" class="b"></path><path d="M491 637v-1h2c1-1 1 0 1-1l-1-1 1-1h7v2h-4l1 1c1 0 2 0 3 1l-3 1h4c-1 1-2 1-4 1h-2v1 1c0 1-1 1-1 2v1h-1v-3-1c-2 0-3 0-5-1h6v-1c-1 0-2 0-4-1z" class="K"></path><path d="M520 646c2 0 3 0 4 1h1v2l3-1v3s0 1-1 1v3h0l-4 1v-2-1h-1v2h0-2v1h-6l-1-3-1-1h0-1c-1-2-1-3-1-5 0-1 0-1 1-1 0 1 0 1 1 2h0v-2h1 3 2l2 1v-1z" class="J"></path><path d="M513 646h3 2l2 1v5h-5c-1-2-1-3-2-6z" class="f"></path><path d="M476 624h5v3h-1c0 1 0 3-1 4h-2c0 2 0 5 1 7l-3 2c3 1 8 0 10 0 1 0 2 0 2 1s0 2-1 3v1h-1v1c1 1 1 0 1 1v1h1l-1 1h-2-1v-8h-1c-1 0-2 0-2 2v1 1 4l-2 1c-2-2 0-3-2-5h-1v3h-1v-4-1-3-2-4c0-1 1-2 0-3 1-2 1-4 0-6l1-1h1z" class="e"></path><path d="M477 631l-1-2c1-1 1-1 2-1h0c0-1 0-2 1-3h1v2c0 1 0 3-1 4h-2z" class="R"></path><path d="M458 629c1 0 1 0 2-1l1 3h2c2 0 3 1 4 1h-3v1c1 1 3 0 4 0 2 0 2 2 3 3h1v-2h2v4 2 3 1c-1 0-2 1-2 1-1 1-1 3-2 4 0-3 1-5-2-7-1 0-1 0-2-1h-1-3l-1 1v2 1c-1 0-1-3-2-4h0l-4-1h0c-1 0-1 1-2 1l-1-1h-1l-1-1c-1-2 0-1 0-2 1 1 1 1 3 0v-5l1-1 1-3 3 1z" class="M"></path><path d="M458 629c1 0 1 0 2-1l1 3h-7l1-3 3 1z" class="m"></path><path d="M453 637h1c1-1 0-1 1-1v-1c2-2 6-1 8-1l1 1h0c-1 1-1 2-1 3s0 1-1 2h1c1 0 1 0 2 1h-3l-1 1v2 1c-1 0-1-3-2-4h0l-4-1h0c-1 0-1 1-2 1l-1-1h-1l-1-1c-1-2 0-1 0-2 1 1 1 1 3 0z" class="R"></path><path d="M496 624h1c1 1 3 1 4 1v5c-1 0-1 1-1 2 1 1 1 0 1 1h-7l-1 1 1 1c0 1 0 0-1 1h-2v1c2 1 3 1 4 1v1h-6l-1 1h1v2 1l-2-2h0c0-1-1-1-2-1-2 0-7 1-10 0l3-2c-1-2-1-5-1-7h2c1-1 1-3 1-4h1v-3h0 6 4 5z" class="k"></path><path d="M483 634l3 1c0 2-1 2-2 4h-1v-5z" class="O"></path><path d="M480 627h1c0 4 0 7-1 11h-2c-1-2-1-5-1-7h2c1-1 1-3 1-4z" class="u"></path><path d="M487 625c0 2-1 4-1 6h0-1l-1 1h1 1v3l-3-1c0-2-1-6 0-9h3 1z" class="U"></path><path d="M487 625h0 2v1c0 3-1 9 0 12l2-1c2 1 3 1 4 1v1h-6l-1 1h1v2 1l-2-2h0c0-1-1-1-2-1l-1-1c1-2 2-2 2-4v-3h-1-1l1-1h1 0c0-2 1-4 1-6z" class="n"></path><path d="M496 624h1c1 1 3 1 4 1v5c-1 0-1 1-1 2 1 1 1 0 1 1h-7l-1 1 1 1c0 1 0 0-1 1h-2v1l-2 1c-1-3 0-9 0-12v-1h-2v-1h4 5z" class="t"></path><path d="M496 624h1c1 1 3 1 4 1v5c-1 0-1 1-1 2-1-2-3-3-5-3h-2l1-1-1-2 1-1c-1 0-1 0-2-1h-1 5z" class="h"></path><path d="M501 625h7v1c1 1 2 1 3 1h2 1v3 5 3c3 0 4 0 6-1v1 1 1 2c0 2 1 2 0 4v1l-2-1h-2-3-1v2h0c-1-1-1-1-1-2-1 0-1 0-1 1l-1 1c-1 0-1-1-2-2h-2v2h-1l-1-2h-2v-5c0-1-2-1-3-2 2 0 3 0 4-1h-4l3-1c-1-1-2-1-3-1l-1-1h4v-2c0-1 0 0-1-1 0-1 0-2 1-2v-5z" class="F"></path><path d="M504 634l1-1 1 1c-1 1-1 1-2 1v1h4 5v1h-4l1 1h3l1 1h-2-3 0c0 1-1 1-2 1h-4l1-1v-1h-2-4l3-1c0-1 1-1 1-1l2-2z" class="E"></path><path d="M502 638h2v1l-1 1h4c1 0 2 0 2-1h0 3v1h2c0 2 0 4-1 5h-1v1 2h0c-1-1-1-1-1-2-1 0-1 0-1 1l-1 1c-1 0-1-1-2-2h-2v2h-1l-1-2h-2v-5c0-1-2-1-3-2 2 0 3 0 4-1z" class="B"></path><path d="M502 638h2v1l-1 1h4c1 0 2 0 2-1l2 1v1h-2l-1 2h-1c-1-1 0-2-1-2-1-1-1-1-2 0 0 0-1 0-1 1 1 1 0 2 0 3v1h-2v-5c0-1-2-1-3-2 2 0 3 0 4-1z" class="D"></path><path d="M501 625h7v1c1 1 2 1 3 1h2 1v3 5h-1c-2 0-3 0-5 1h-4v-1c1 0 1 0 2-1l-1-1-1 1-2 2s-1 0-1 1c-1-1-2-1-3-1l-1-1h4v-2c0-1 0 0-1-1 0-1 0-2 1-2v-5z" class="G"></path><path d="M507 630h0 1c-1 1-1 2 0 3l1 1h0c2 0 3 0 4 1-2 0-3 0-5 1h-4v-1c1 0 1 0 2-1l-1-1-1 1-1-1c0-1 0-1 1-2h3v-1z" class="f"></path><path d="M501 625h7v1h-5v1l1 2c1 1 1 1 3 1v1h-3c-1 1-1 1-1 2l1 1-2 2s-1 0-1 1c-1-1-2-1-3-1l-1-1h4v-2c0-1 0 0-1-1 0-1 0-2 1-2v-5z" class="T"></path><path d="M453 641c1 0 1-1 2-1h0l4 1h0c1 1 1 4 2 4v-1c1 2 1 2 1 4v1c1 1 1 4 1 6h-1c-2-1-3 0-4-1h0l-1 1-1-1-1 1-1-1c-2 1-2 0-4 1l-1-1v-4c-2 0 0 4-1 5l-2-1-1 1-1 2h-3c-3-1-8 0-12 0h-6-2c1-1 1-2 3-3l1 1v-2c0-1 1-2 1-3l1-1-1-2c3 0 7 0 10 1h1 16 0v-3h-2c2-2 2-2 2-4z" class="d"></path><path d="M453 641c1 0 1-1 2-1h0l4 1h0c1 1 1 4 2 4v2h-1l-1-1v1c-2-2-1-4-2-6v1h0c-1 1-1 2-1 3h-1c0 1-1 1-1 2l-1 1v-3h-2c2-2 2-2 2-4z" class="Z"></path><path d="M426 647c3 0 7 0 10 1h1 16 8v1h-7-9l-1 1v1l-1-2c-1 0-1 0-2 1h1l-1 1v-1h0l-1-1h-2-2 0-1c-1 0-2 1-2 1h-1c-2-1-4 0-6 0l1-1-1-2z" class="i"></path><path d="M426 650c2 0 4-1 6 0h1s1-1 2-1h1 0 2 2l1 1h0v1l1-1h-1c1-1 1-1 2-1l1 2v-1h1c1 1 1 3 1 4l-1 1-1 2h-3c-3-1-8 0-12 0h-6-2c1-1 1-2 3-3l1 1v-2c0-1 1-2 1-3z" class="K"></path><path d="M425 655h1l1-1 1 1 1-1 2 2c1 0 2-1 3-2l1 1c0-1 0-1 1-1v1h1v-3h1v3h1l1-1v1h3l1-1 1 1-1 2h-3c-3-1-8 0-12 0h-6-2c1-1 1-2 3-3l1 1z" class="e"></path><path d="M520 625s0-1 1-1h2c3 0 7 1 10-1h1l3 2v3c-1 1-1 1 0 3v2l1 1h0 1 0c1 1 1 1 1 2l2-1c1 4 1 7 0 11v4h1 2c0 1 1 1 1 2v2h-1 0c-1 1-1 1-1 2 1 1 2 1 4 1-2 1-4 1-6 1h-3v-2c-2-1-3 0-5-1-1 0-1 0-2-1v1s0 1-1 1c-2 0-2-1-4-1v-3c1 0 1-1 1-1v-3l-3 1v-2h-1c-1-1-2-1-4-1 1-2 0-2 0-4v-2-1-1-1c-2 1-3 1-6 1v-3-5-3-2h6 0z" class="D"></path><path d="M520 639h2 0c1-1 2-1 3-1v1 1 1h0-2c-1-1-1-1-2-1h-1v-1z" class="a"></path><path d="M534 632l1 2 2-1c0 2 0 3-1 4v2h-1 0c-1-3-1-5-1-7z" class="G"></path><path d="M526 641v-2l4-1 1 3c0 1 0 1-1 1h-3l-1-1z" class="Q"></path><path d="M520 627h1c-1 2 0 3 0 5v1h4 0-3l-1 1h1c1 1 1 0 1 2l-3 2v-1-10h0z" class="L"></path><path d="M531 626l-1-1h1 1v2l1-1v-2l1 1c0 2 1 4 0 7 0 2 0 4 1 7h-1l-2-1v1l-1-1c-1-2-1-6 0-8v-4z" class="B"></path><path d="M520 640h1c1 0 1 0 2 1h2 0 1l1 1h3l-1 1-1 3 1 1 1-1v2h0c-1-1-2-1-2 0l-3 1v-2h-1c-1-1-2-1-4-1 1-2 0-2 0-4v-2z" class="f"></path><path d="M520 640h1c1 0 1 0 2 1h2 0 1l1 1h3l-1 1c-4 0-6 0-9-1v-2z" class="T"></path><path d="M530 648h1 1v-6-1c2 0 0 5 2 7 1-2-1-6 1-8v1 3h1c0 2 0 5 1 7v1h-1c-1-1-1 0-2 0h-1v1h-1c-1-1-1-1-2-1v-2l-1 1v1h-2c1 0 1-1 1-1v-3c0-1 1-1 2 0h0z" class="E"></path><path d="M536 643v-1l2-1v3 7l2 1v2c-1 1-1 1-1 2-2-1-3 0-5-1-1 0-1 0-2-1v1s0 1-1 1c-2 0-2-1-4-1v-3h2v-1l1-1v2c1 0 1 0 2 1h1v-1h1c1 0 1-1 2 0h1v-1c-1-2-1-5-1-7v-1z" class="T"></path><path d="M520 625s0-1 1-1h2c3 0 7 1 10-1h1l3 2v3c-1 1-1 1 0 3v2l-2 1-1-2c1-3 0-5 0-7l-1-1v2l-1 1v-2h-1-1l1 1c-1 2 0 2-2 2h-1-2l-2-1c-2 0-2-1-4-2z" class="E"></path><path d="M514 627v-2h6v2h0v10c-2 1-3 1-6 1v-3-5-3z" class="q"></path><path d="M514 630c0 1 1 2 1 3 2 0 2 0 3-1v-1c1-1 1-2 1-3l1-1v10c-2 1-3 1-6 1v-3-5z" class="P"></path><path d="M539 634c1 1 1 1 1 2l2-1c1 4 1 7 0 11v4h1 2c0 1 1 1 1 2v2h-1 0c-1 1-1 1-1 2 1 1 2 1 4 1-2 1-4 1-6 1h-3v-2c0-1 0-1 1-2v-2l-2-1v-7-3l-2 1v1-3c1 0 1 0 2-1v-5h1 0z" class="X"></path><path d="M540 636l2-1c1 4 1 7 0 11v4h1c-1 1-2 1-2 0h-1v-5-9z" class="l"></path><path d="M533 600v2 3l2 2 1 1 1 9v5h1v1h-4-1c-3 2-7 1-10 1h-2c-1 0-1 1-1 1h0-6v2h-1-2c-1 0-2 0-3-1v-1h-7c-1 0-3 0-4-1h-1-5-4-6 0-5-1l-1 1c1 2 1 4 0 6 1 1 0 2 0 3h-2v2h-1c-1-1-1-3-3-3-1 0-3 1-4 0v-1h3l6-1h0l-1-21v-2l10-1c4 0 7 1 11 0h1 16 3c5 1 12 0 17 0v-1h1c0-2 0-2-1-4 1-1 2-1 3-2z" class="Y"></path><path d="M485 612h1l1 2 1-1v7h-2v-1 1l-1 1-1-1c0-2 0-5 1-8z" class="Z"></path><path d="M502 612h1c0 1 0 2-1 3v5h1c-1 1-4 0-5 0v-8h4z" class="t"></path><path d="M503 612h2l4-1 1 1v1 5l1 1c1 1 1 0 1 1h-4-1-4-1v-5c1-1 1-2 1-3z" class="K"></path><path d="M503 620c1-2 1-4 1-6h3c0 2-1 4 0 6h-4z" class="r"></path><path d="M505 612l4-1 1 1v1 5l1 1c1 1 1 0 1 1h-4-1c-1-2 0-4 0-6l1-1c-1-2-2 0-3-1z" class="b"></path><path d="M507 614l1-1v7h-1c-1-2 0-4 0-6z" class="I"></path><path d="M486 612c3-1 7-1 10-1v2h-2l2 2v5l-1 1c-1-1 0-3 0-4h-1v3h-2-1c-1-1 0-1-1-1v2c-1 0-1 0-2-1v-7l-1 1-1-2z" class="O"></path><path d="M488 613l1-1h1v7 2c-1 0-1 0-2-1v-7z" class="b"></path><path d="M521 620h2v1h5c-1 1-3 1-4 1-3 0-11-1-14 1-4 1-9 1-13 1h-1v-1c-2 0-7 1-9 0 2-2 17-1 20-2h6v-1h3 5z" class="l"></path><path d="M533 600v2 3l2 2 1 1 1 9v5h1v1h-4-1c-3 2-7 1-10 1h-2c-1 0-1 1-1 1h0-6v2h-1-2c-1 0-2 0-3-1v-1h-7c-1 0-3 0-4-1 4 0 9 0 13-1 3-2 11-1 14-1 1 0 3 0 4-1h-5v-1h3v-8h1 3v-5-1h1c0-2 0-2-1-4 1-1 2-1 3-2z" class="L"></path><path d="M533 600v2 3l-1 18h-22c3-2 11-1 14-1 1 0 3 0 4-1h-5v-1h3v-8h1 3v-5-1h1c0-2 0-2-1-4 1-1 2-1 3-2z" class="O"></path><path d="M530 612h0c0 2 0 4 1 6-2 2-2 2-4 2h-1v-8h1 3z" class="K"></path><path d="M513 607c5 1 12 0 17 0v5h-3-1v8h-3-2-5-3-1c0-1 0 0-1-1l-1-1v-5-1l-1-1-4 1h-2-1c-1-1-3-1-4-1h-2c-3 0-7 0-10 1h-1-3c0 2 1 9-1 10v1 1h0 0-5-1l-1 1c1 2 1 4 0 6 1 1 0 2 0 3h-2v2h-1c-1-1-1-3-3-3-1 0-3 1-4 0v-1h3l6-1h0l-1-21v-2l10-1c4 0 7 1 11 0h1 16 3z" class="R"></path><path d="M509 611l10 1c3 0 6-1 8 0h-1-12v1l-1 7h-1c0-1 0 0-1-1l-1-1v-5-1l-1-1z" class="Z"></path><path d="M476 624s2-1 2-2c1-3-1-7 1-10 1-1 1 0 3 0 0 2 1 9-1 10v1 1h0 0-5z" class="p"></path><path d="M514 613v-1h12v8h-3-2-5-3l1-7z" class="T"></path><path d="M519 614l1-1h1v7h-5-3l1-7c1 1 3 1 5 1z" class="G"></path><path d="M519 614l1-1h1v7h-5v-1c1 1 2 1 3 0h-2l2-1c0-1 1-2 1-3l-1-1z" class="B"></path><path d="M543 534c0-1 0-2 2-3v-1c0-1 0-2 1-3 2 2 1 3 1 5l1 1v-2h1 2 3v6h1 2v4l1 7-1 3c2 0 3 0 4 2v-2h-1v-6c0-5 0-9-1-14h5 0 5 0l4-1 3 1h2l-1 4v5l1 1v3c1 0 1-1 1-1v-1h1v1c1 0 1 0 2 1 0 3 1 5 2 9 0 2 1 3 2 6v2l4 39c1 6 0 13 0 19 0 3-1 7 0 10v1h-3-4-5l1-1h-6l1-1c0-3 1-7 2-10l-3-1h0-1l-1-1h-3-2c-2 0-5 0-6 1-1-1-1-1-2 0h-3-1l-1 4c0 3 0 6-1 9v9c0 3 0 6-1 8h1 1c0 1 1 2 1 3h-3l-1 2h0v-2h-3c-1 1-1 1-2 0h-2-1v-4c1-4 1-7 0-11l-2 1c0-1 0-1-1-2h0-1 0l-1-1v-2c-1-2-1-2 0-3v-3l-3-2h4v-1h-1v-5l-1-9-1-1-2-2v-3-2c-1 1-2 1-3 2 1 2 1 2 1 4h-1v1c-5 0-12 1-17 0h-3-16v-2c2-2 3-4 5-6h0l2-2 2-2 1-1c1-1 2-3 3-4 0-2 1-4 2-6-1 0-1 0-1-1h-1c1-2 2-3 2-4 2-1 3-5 4-7v-1h-1v-2h9l1 1 1-1 1-24v-1-3l2-1v-1s-1 1-2 1v-1-1l-1 1-1-2h3v1s1 0 1-1c1 0 3 1 4 1 0 1 0 0 1 1 0-1 0-1 1-2h1v-1h2v-1h5v1l2-1h1v-1z" class="v"></path><path d="M541 603v-1c-1-1-1-2-1-3 1 1 1 1 2 1 2 2 4 6 4 8h0-5 0c-1-2-1-4 0-5z" class="g"></path><path d="M548 533v-2h1c2 6 1 12 1 17l-1-5h0v19c0 3 0 7-1 10h0v-8-19-12z" class="Z"></path><path d="M539 572h1v1h0v1c1 9-1 18 2 26-1 0-1 0-2-1 0 1 0 2 1 3v1c-3-1-1-2-2-4v-5c-1-5-1-10-1-15v-4l-2-2c1 0 2-1 3-1z" class="n"></path><path d="M543 535v1c-1 0-1 1-1 1h-2l-1 5c0 1 1 2 1 3h0c0 2 1 3 1 4v13 1c1 1 0 4 0 5h0c-1 1-1 2-1 4h-1-1l1-2c-1-1 0-3 0-5l-1-16c0-3 1-6 0-10v-1c-2 0-3 0-5-1h0v-1h2v-1h5v1l2-1h1z" class="U"></path><path d="M548 572h0c1-3 1-7 1-10v-19h0l1 5v17c0 2 0 5 1 7h1 5c-2 2-6 0-7 2 2 1 5 0 7 0-2 1-5 1-6 1-1 1-1 1-1 2v12c0 7 0 13 1 20 0 2 0 3 1 5l-2 1h2 10-9l-1 1h-5-1v-1l1-1h1c0-1 0-2 1-3 0-4-1-8-1-12v-27z" class="j"></path><path d="M552 616l1-1v2h1l-1 4c0 3 0 6-1 9v9c0 3 0 6-1 8h1 1c0 1 1 2 1 3h-3l-1 2h0v-2h-3c-1 1-1 1-2 0h-2-1v-4h1l1-2h1v1l-1 3 2 2c1-1 1-2 1-3s1-6 1-8v-14c0-2 0-6 1-8 1 0 2-1 3-1h0z" class="o"></path><path d="M553 617h1l-1 4c0 3 0 6-1 9v9c-1-1 0-3-1-4 0-5 0-10-1-15v-3h3z" class="j"></path><path d="M552 616l1-1v2h-3v3l-1 29-1 1v-3h-1c0-1 1-6 1-8v-14c0-2 0-6 1-8 1 0 2-1 3-1h0z" class="i"></path><path d="M557 572l1 1v13l-1 1h0-5v1h2v1 2l-1 1v2 1c-1 1 0 3 0 5 0 5 0 9 1 14l-2 1h-2l2-1c-1-2-1-3-1-5-1-7-1-13-1-20v-12c0-1 0-1 1-2 1 0 4 0 6-1-2 0-5 1-7 0 1-2 5 0 7-2z" class="k"></path><path d="M557 572l1 1v13l-1 1v-3h-1l-1-1-1 1v1h2v1h-4l1-1-1-1 1-2-1-1 2-1h1 0c-1 0-1 0-2-1v-1c1-1 1-1 1-2-2 0-2 0-4 1 0-1 0-1 1-2 1 0 4 0 6-1-2 0-5 1-7 0 1-2 5 0 7-2zm-21 24c2-6 1-12 2-17 0 5 0 10 1 15v5c1 2-1 3 2 4-1 1-1 3 0 5h0v10 5 1 1 2c0 3 0 5 1 8l-2 1c0-1 0-1-1-2h0-1 0l-1-1v-2c-1-2-1-2 0-3v-3l-3-2h4v-1h-1v-5l-1-9-1-1-2-2v-3-2-1h3v-3h0z" class="K"></path><path d="M536 596c2 3 1 9 0 12l-1-1-2-2v-3-2-1h3v-3z" class="E"></path><path d="M539 599c1 2-1 3 2 4-1 1-1 3 0 5h0v10h-2v-7c0-3-1-5 0-8v-3-1z" class="t"></path><path d="M539 618h2v5 1 1 2c0 3 0 5 1 8l-2 1c0-1 0-1-1-2v-11-5z" class="n"></path><path d="M523 571l15 1h1c-1 0-2 1-3 1l2 2v4c-1 5 0 11-2 17h0v3h-3c0-4-3-9-5-12l-5-11-1-2v-2l1-1z" class="L"></path><defs><linearGradient id="O" x1="539.674" y1="579.435" x2="533.192" y2="583.344" xlink:href="#B"><stop offset="0" stop-color="#a19b96"></stop><stop offset="1" stop-color="#b0b1a6"></stop></linearGradient></defs><path fill="url(#O)" d="M532 574c2-1 2-1 4-1l2 2v4c-1 5 0 11-2 17v-1l-1-1h0c0-2 0-3 1-4-1-1-1-1-1-2v-5c0-2 1-4 0-6 0-1-1-1-2-2l-1-1z"></path><path d="M523 571l15 1h1c-1 0-2 1-3 1-2 0-2 0-4 1l1 1c1 3 0 4 1 6-2 1-1 1-3 1 0-1-1-2-1-3h-1v2c1 2 1 4-1 6l-5-11-1-2v-2l1-1z" class="G"></path><path d="M523 571l15 1h1c-1 0-2 1-3 1-2 0-2 0-4 1l-2 1-1-2h-1v1 1 1c-1 0-1 0-1-1h0l-4-1h-1v-2l1-1z" class="D"></path><path d="M533 537h0c2 1 3 1 5 1v1c1 4 0 7 0 10l1 16c0 2-1 4 0 5l-1 2-15-1-1 1v2l1 2-2 1v7c0-2 0-4-1-5 0-2 0-4-1-6v-1l-2-1c-1 0-2 0-3 1h-1v-1h-1v-2h9l1 1 1-1 1-24v-1-3l2-1v-1s-1 1-2 1v-1-1l-1 1-1-2h3v1s1 0 1-1c1 0 3 1 4 1 0 1 0 0 1 1 0-1 0-1 1-2h1z" class="d"></path><path d="M524 570c0-2 1-3 1-5v-1c1-1 1-1 3 0 2-1 5 0 7 0l1 1s1 0 2 1h0-2c1 1 1 2 2 3l-1 1h-13z" class="B"></path><path d="M528 564c2-1 5 0 7 0l1 1s1 0 2 1h0-2l-1 1c0 1 0 1-1 2v-1c-1-3-2-2-4-2l-2-2z" class="L"></path><path d="M524 545c3 0 5-1 8-1v1h-1s-1 0-2 1c0 1 0 1 1 2l1 1c-1 1-2 0-2 1 1 1 2 1 4 1h1v1c-3 0-4 0-6-1h-1c1 1 2 1 4 2h3c1 1 1 2 3 3h-1c-1 1-2 1-3 0 1 0 1 1 2-1-1 0-1-1-2-1-2 0-3 0-5-1h-2c0-2 0-3 1-4h1c-1-1-1-1-1-2l-1-1h-1c0 3 1 6 0 9v1h0c1 1 2 1 3 1h2c2 1 3 1 4 1v1c-3-1-5-1-8-1v1h3c2 0 3 1 4 1h1v1l1-1v1c-2 1-4-1-6-1-1 0-1 1-2 1s-1-1-2-1v1l1 1 3-1c2 0 3 2 6 2h0v1h0c-2 0-5-1-7 0-2-1-2-1-3 0v1c0 2-1 3-1 5l-1 1-1 1v2l1 2-2 1v7c0-2 0-4-1-5 0-2 0-4-1-6v-1l-2-1c-1 0-2 0-3 1h-1v-1h-1v-2h9l1 1 1-1 1-24z" class="O"></path><path d="M512 569h9l1 1-1 1-1 8c0-2 0-4-1-6v-1l-2-1c-1 0-2 0-3 1h-1v-1h-1v-2z" class="Z"></path><path d="M517 571l2 1v1c1 2 1 4 1 6 1 1 1 3 1 5v-7l2-1 5 11c2 3 5 8 5 12v1c-1 1-2 1-3 2 1 2 1 2 1 4h-1v1c-5 0-12 1-17 0h-3-16v-2c2-2 3-4 5-6h0l2-2 2-2 1-1c1-1 2-3 3-4 0-2 1-4 2-6-1 0-1 0-1-1h-1c1-2 2-3 2-4 2-1 3-5 4-7h1c1-1 2-1 3-1z" class="a"></path><path d="M513 585h1c2 0 2 0 4 1 0 2 0 3-1 4h-2c-1 0-1-1-2-1h0l-1 1c-1-2 0-2-1-4h1l1 1 1 1v-1l-1-1v-1z" class="G"></path><path d="M514 572c1-1 2-1 3-1 2 3 1 8 2 11l-1 2-2 1c-1-1-2-1-4-1v-1c1 0 1 0 1-1h-1c-1-2 0-3 1-5 0-2 0-3 1-5z" class="L"></path><path d="M504 594c0 1 0 1-1 3h0l-2 4v1c1-2 3-4 4-5l2-3h1l1 2-1 2c-1 2-1 1 0 3v3l-1 1h1c1 0 1 1 2 2h-16v-2c2-2 3-4 5-6h0l2-2 2-2 1-1z" class="r"></path><path d="M504 594c0 1 0 1-1 3h0l-2 4v1h0c0 1 0 2-1 3h-2-1l3-4-1-1v-1l2-2 2-2 1-1z" class="T"></path><path d="M508 604l-2-1c-1 0-1 0-2 1l1 1h-3v-1c1-1 1-2 2-3 0-1 1-3 3-4l1 1c-1 2-1 1 0 3v3z" class="I"></path><path d="M516 595h1c1 1 1 2 1 3 0 2 1 7 0 9h-1-4-3c-1-1-1-2-2-2h-1l1-1v-3c-1-2-1-1 0-3l1-2c2-1 4-1 7-1h0z" class="J"></path><path d="M509 596c2-1 4-1 7-1-1 1-2 2-2 3l-1 1-1-1c-1 2-2 1-3 3h-1 0c-1-2-1-1 0-3l1-2z" class="E"></path><path d="M516 595h1c1 1 1 2 1 3 0 2 1 7 0 9h-1v-2l-2 1c-2-2 0-2-1-3 0-1-1-1-1-1 0-1 1-2 0-3l1-1c0-1 1-2 2-3h0z" class="B"></path><path d="M516 595h0c1 1 1 2 1 3-1 1-2 1-3 1h-1l1-1c0-1 1-2 2-3z" class="N"></path><path d="M517 571l2 1v1c1 2 1 4 1 6 1 1 1 3 1 5v-7l2-1 5 11c2 3 5 8 5 12v1c-1 1-2 1-3 2 1 2 1 2 1 4h-1v1c-5 0-12 1-17 0h4 1c1-2 0-7 0-9 1-5 1-11 1-16-1-3 0-8-2-11z" class="u"></path><path d="M521 588v1c2 5 0 10 2 14v1c0 1 0 0 1 1h0l1-1h1v-1-1h1c0 1 0 0 1 1h1v3h-8v-1c-1-5 0-11 0-17z" class="S"></path><path d="M517 571l2 1v1c1 2 1 4 1 6 1 1 1 3 1 5v4c0 6-1 12 0 17v1h8 1v1c-5 0-12 1-17 0h4 1c1-2 0-7 0-9 1-5 1-11 1-16-1-3 0-8-2-11z" class="n"></path><path d="M573 530l3 1h2l-1 4v5l1 1v3c1 0 1-1 1-1v-1h1v1c1 0 1 0 2 1 0 3 1 5 2 9 0 2 1 3 2 6v2l4 39c1 6 0 13 0 19 0 3-1 7 0 10v1h-3-4-5l1-1h-6l1-1c0-3 1-7 2-10l-3-1h0-1l-1-1h-3-2c-2 0-5 0-6 1-1-1-1-1-2 0h-3-1-1v-2h9-10l2-1c-1-5-1-9-1-14 0-2-1-4 0-5v-1-2l1-1v-2-1h-2v-1h5 0l1-1v-13l-1-1h-5-1c-1-2-1-5-1-7v-17c0-5 1-11-1-17h2 3v6h1 2v4l1 7-1 3c2 0 3 0 4 2v-2h-1v-6c0-5 0-9-1-14h5 0 5 0l4-1z" class="B"></path><path d="M560 608v5h1c1-2 0-42 0-49h1c0 6-1 48 0 49h1v-36c0-3-1-10 1-12v48h1v-1h1c0 1 0 1 1 1 1 1 2 1 3 2h-8-10l2-1c-1-5-1-9-1-14 0-2-1-4 0-5v-1-2l1-1v-2-1h-2v-1h5 0l1-1c-1 1-1 2 0 2 1 3 0 9 0 12v3c3-4-1-12 2-16v14 7z" class="K"></path><path d="M558 586c-1 1-1 2 0 2 1 3 0 9 0 12v3c3-4-1-12 2-16v14 7c-1 2 0 4-1 5h-1v-2h-1v2h-1l-1-25h-1-2v-1h5 0l1-1z" class="T"></path><path d="M549 531h2 3v6h1 2v4l1 7-1 3c2 0 3 0 4 2h-1v25c0 3 1 7 0 9-3 4 1 12-2 16v-3c0-3 1-9 0-12-1 0-1-1 0-2v-13l-1-1h-5-1c-1-2-1-5-1-7v-17c0-5 1-11-1-17z" class="t"></path><path d="M555 548h3l-1 3c-1 0-2 0-3-1l1-2zm3 12c-1 0-2 0-3-1 0-2 0-5 1-7 0 1 0 1 1 1h1v7z" class="T"></path><path d="M558 553h2v25c0 3 1 7 0 9-3 4 1 12-2 16v-3c0-3 1-9 0-12-1 0-1-1 0-2v-13l-1-1h-5l1-1h4v-1h-4l1-1c1 0 2 0 3-1 0-1-1-1-2-2h2c1-1 0-1-1-2l2-1v-3-7z" class="I"></path><path d="M549 531h2 3v6h1 2v4l1 7h-3l-1 2h-1v1c-1 0-1 1-2 1 0 3-1 7 0 10 0 1-1 2-1 3v-17c0-5 1-11-1-17z" class="b"></path><path d="M557 541l1 7h-3c0-1 0-3 1-4v-1l-1-1 1-2c0 1 0 1 1 1z" class="r"></path><path d="M573 530l3 1h2l-1 4v5l1 1v3c1 0 1-1 1-1v11c-2 2-1 8-1 11l-1 8c0 2 1 3 0 5s-1 6-1 8v5c-1 1-1 2-1 4v1c-1 1-1 3-1 4 0 2 0 3-1 5v1c0 2 0 4 1 6l-1 1h1c0 1 0 0 1 1-2 1-3 1-5 1-1-1-2-1-3-2-1 0-1 0-1-1h-1v-13-27-4-12c2-4 2-8 2-12 0-2 0-4 1-5 1 2 0 8 0 10h1v-8c1-3 0-6 1-9h0l-1-1h0l4-1z" class="L"></path><path d="M573 530l3 1h1v1h-2c0 5 1 10 0 14-1 1-1 2-1 4 1 0 1 0 1 1l-1 1v3c0-1-1-2 0-3v-5h-1v6h-1c0-2 1-6 0-8h-1c0-4 0-8 1-12v-1-1c-2 1-1 4-2 5v3c-1 2 1 10 0 11l-1-1v-8c1-3 0-6 1-9h0l-1-1h0l4-1z" class="f"></path><path d="M571 545h1c1 2 0 6 0 8h1v-6h1v5c-1 1 0 2 0 3v-3l1-1c0-1 0-1-1-1 0-2 0-3 1-4 0 3 1 10 0 12 0 1 0 1-1 3v3c0 1-1 1-1 2l-1 1v-2h-1v25 4h-1c-1-5 0-11 0-17l-1 1c0-4 1-8 1-13 0-3 0-6 1-8v-12z" class="V"></path><path d="M565 572v-3h1v33c0 1-1 6 0 8 0 0 1 0 1 1 1-7-1-39 1-42v22c0 2 0 7 1 10v-23l1-1c0 6-1 12 0 17h1v-4c1 4 1 8 0 11 0 1 1 3 0 4 0 0-2 1-2 2v4l1 1c-1 1-1 1-2 1h-1c-1 0-1 0-1-1h-1v-13-27z" class="J"></path><defs><linearGradient id="P" x1="578.214" y1="587.76" x2="566.235" y2="578.305" xlink:href="#B"><stop offset="0" stop-color="#90837c"></stop><stop offset="1" stop-color="#aab09e"></stop></linearGradient></defs><path fill="url(#P)" d="M576 531h2l-1 4v5l1 1v3c1 0 1-1 1-1v11c-2 2-1 8-1 11l-1 8c0 2 1 3 0 5s-1 6-1 8v5c-1 1-1 2-1 4v1c-1 1-1 3-1 4 0 2 0 3-1 5v1c0 2 0 4 1 6l-1 1h1c0 1 0 0 1 1-2 1-3 1-5 1-1-1-2-1-3-2h1c1 0 1 0 2-1l-1-1v-4c0-1 2-2 2-2 1-1 0-3 0-4 1-3 1-7 0-11v-25h1v2l1-1c0-1 1-1 1-2v-3c1-2 1-2 1-3 1-2 0-9 0-12 1-4 0-9 0-14h2v-1h-1z"></path><path d="M574 575c1-2 1-4 1-6s1-3 1-5v16c0 1-1 5 0 6v5c-1 1-1 2-1 4v1c-1 1-1 3-1 4h-2l1-2c1-4 1-9 1-14v-9z" class="t"></path><path d="M576 531h2l-1 4v5l1 1v3c1 0 1-1 1-1v11c-2 2-1 8-1 11l-1 8c0 2 1 3 0 5s-1 6-1 8c-1-1 0-5 0-6v-16c0 2-1 3-1 5s0 4-1 6v-4-2-2-3h0v-3c1-2 1-2 1-3 1-2 0-9 0-12 1-4 0-9 0-14h2v-1h-1z" class="d"></path><path d="M576 531h2l-1 4v5c0 3 0 6-1 8-1 5 0 11 0 16 0 2-1 3-1 5s0 4-1 6v-4-2-2-3h0v-3c1-2 1-2 1-3 1-2 0-9 0-12 1-4 0-9 0-14h2v-1h-1z" class="Q"></path><defs><linearGradient id="Q" x1="576.028" y1="579.889" x2="588.541" y2="577.145" xlink:href="#B"><stop offset="0" stop-color="#b4b3ac"></stop><stop offset="1" stop-color="#e5e0d2"></stop></linearGradient></defs><path fill="url(#Q)" d="M579 543v-1h1v1c1 0 1 0 2 1 0 3 1 5 2 9 0 2 1 3 2 6v2l4 39c1 6 0 13 0 19 0 3-1 7 0 10v1h-3-4-5l1-1h-6l1-1c0-3 1-7 2-10l-3-1h0-1l-1-1h-3-2c-2 0-5 0-6 1-1-1-1-1-2 0h-3-1-1v-2h9 8c2 0 3 0 5-1-1-1-1 0-1-1h-1l1-1c-1-2-1-4-1-6v-1c1-2 1-3 1-5 0-1 0-3 1-4v-1c0-2 0-3 1-4v-5c0-2 0-6 1-8s0-3 0-5l1-8c0-3-1-9 1-11v-11z"></path><path d="M576 586c0-2 0-6 1-8s0-3 0-5l1-8c0-3-1-9 1-11h0c1 8 0 16 0 23v12c0 3-1 8 1 10l1 1c-1 0-1 1-2 1v1c0 1 1 1 0 2h3v-1h-1c1-2 1-2 1-3h1v5h-6v-1c-2 0-3 1-4 2v-1c1-2 1-3 1-5 0-1 0-3 1-4v-1c0-2 0-3 1-4v-5z" class="e"></path><path d="M574 600c0-1 0-3 1-4v-1c0-2 0-3 1-4v11l1 2c-2 0-3 1-4 2v-1c1-2 1-3 1-5z" class="b"></path><path d="M590 600c1 6 0 13 0 19 0 3-1 7 0 10v1h-3-4-5l1-1h-6l1-1c0-3 1-7 2-10l-3-1h0-1l-1-1h-3-2c-2 0-5 0-6 1-1-1-1-1-2 0h-3-1-1v-2h9 8c2 0 3 0 5-1-1-1-1 0-1-1h-1l1-1c-1-2-1-4-1-6 1-1 2-2 4-2v1h6l2-1 1 1c0 1 0 4-1 5l1 1c1 2 1 2 1 4h1c0-2-1-7 0-8 1-3 1-4 2-7z" class="T"></path><path d="M574 628c2 0 2 0 3-1 0-4 0-8 2-12 0 3 1 5 0 8 0 2 0 3 1 4l1 1h2l1 1h-5-6l1-1z" class="E"></path><path d="M573 606c1-1 2-2 4-2v1l-1 1c1 0 2 1 3 2-2 1-3 7-3 10l-3-1h0-1l-1-1h-3-2c-2 0-5 0-6 1-1-1-1-1-2 0h-3-1-1v-2h9 8c2 0 3 0 5-1-1-1-1 0-1-1h-1l1-1c-1-2-1-4-1-6z" class="Z"></path><path d="M579 615v-6h0l1-1c1-1 1 0 3 0v1c0 2 1 3 1 5 0 3 0 7 1 11v2h0-2l-2-1h0c-1 1 0 1-1 1-1-1-1-2-1-4 1-3 0-5 0-8z" class="G"></path><path d="M579 623h3c1 1 1 1 1 2v2l-2-1h0c-1 1 0 1-1 1-1-1-1-2-1-4z" class="N"></path><path d="M583 608v1c-1 3 0 6-1 9l-1-1c0-2-1-6-1-9h3z" class="P"></path><path d="M590 600c1 6 0 13 0 19 0 3-1 7 0 10v1h-3-4-5l1-1h5l-1-1h-2l-1-1c1 0 0 0 1-1h0l2 1h2 0v-2c-1-4-1-8-1-11 1-1 1-2 2-3 1 2 1 2 1 4h1c0-2-1-7 0-8 1-3 1-4 2-7z" class="a"></path><path d="M558 138h301v239h-47c-3-44-23-88-56-117-30-27-67-42-107-45-20-2-40-1-60-1v262h31c9 0 19 1 29 0 21-3 42-10 58-24 15-14 26-34 30-54 0-1 2-12 2-13h1 15v269h-23c-1-5-2-11-4-16-10-33-30-62-58-81-24-16-52-23-81-23 0 8 1 17 0 26-1 1-1 0-3 1v-2c-1-3-2-4-2-6-1-4-2-6-2-9-1-1-1-1-2-1v-1h-1v1s0 1-1 1v-3l-1-1v-5l1-4h-2l-3-1-4 1h0-5 0-5c1 5 1 9 1 14v6h1v2c-1-2-2-2-4-2l1-3-1-7v-4h-2-1v-6h-3-2-1v2l-1-1c0-2 1-3-1-5-1 1-1 2-1 3v1c-2 1-2 2-2 3v1h-1l-2 1v-1h-5v1h-2v1h-1c-1 1-1 1-1 2-1-1-1 0-1-1-1 0-3-1-4-1 0 1-1 1-1 1v-1h-3-15s-1 0-2 1h0c-1-1-1-1-2-1h-1-9c-1 1-1 1-2 0-2 0-4 1-7 1 0-2 1-2 0-3h-4c-2-1-2-2-2-4l-1-1c0-1-1-1-2-2v-1c-1-1 0-5 0-6 1-3 0-8 0-11v-2l-3 1v4c0-1 0-3-1-5h-3c-2 1-1 1-2 2 0-1 0-2-1-3l-1-1h3l1-1h-11-8-4-1c-1 0-3 0-4-1v-4l1-12v-19-28-1-2h-1c1-3 1-4 0-6 0-1-1-2-1-3v-4-3h-1l1-1c-1-1-1-2-2-3l-1-1v-1-2c1-2 2-3 3-3h1 1v-7-2c-1-1-2-3-4-3l-1-2v-1c-2-2-5-6-5-9v-2c1-1 1-1 2 0h0v-1l-2-3v-1c-1 0-1 0-1-1h1v-1c0-2-1-3-2-4h0c0-1-1-2-2-3 1 0 2-1 3-1-1-1-1-2-1-3h14l-1-1c2 0 3 0 4-1-1-1 0-3 0-5l1 1 2-2v-1h0c1-1 0-1 2-1v-1h-4c-1 1-5 1-7 1v-6-1-8-1-3l1-2-3-1-2-3h0l1-1c-1-1-1-2-1-3l2-1h0c1 0 1 0 2-1l1-6-1-1c-3 0-5-1-7-3-1-4 1-9-1-13-1-1 0-4 0-6 1 1 1 2 2 3l2-1v1c2 1 3 1 4 1l1 1c0 1 0 2 1 2 0-1 0-2 1-3 0 0 0 1 1 1l2-2h2 3 5 8 4 2l3 1v-1h1v1c1 0 1-1 2-1h2v-4h0l1-1c1-1 2-3 3-4l-2-4v-2-1l1-1v-1-2c1-1 1-3 1-4v-2-5-4-9-8-2-9c0-2 0-3-1-4l-1-2-1-1v-2h0c1-1 1-1 1-2h2l2-2h0l1-1c-1-1-1-2-1-4-2-3-5-9-4-12h-1-2l-1 1h0c-1-1-2-1-2-3v-1c1-1 1-1 0-3l-6-2-1-1h6 1c2-2 2-3 5-3 1-1 1-1 1-2s-2-2-3-2v-1l2-2c-1 0-1 0-2-1h0 0 4v-5-4-1-5-4h0l1-3v-1c1-1 0-4 1-6l-1-7 1-6c-1-1-3-1-4-1v-2h0l13-1 50 1v-2-1c1-1 2-1 4-1h12 0z" class="w"></path><path d="M819 296h1l1 1-1 2c-1 0-2-1-2-1v-1l1-1zm-69 303v-1c1 3 1 8 0 11 0-1-1-2 0-3v-7z" class="B"></path><path d="M846 143h8c1 1 0 3 0 5v-1h-1v-3h-2-1c-2 0-3 0-4-1z" class="Z"></path><path d="M720 591h1l1-1h1 1c-1 1-1 1-2 1 0 1 1 2 2 3-1 1-1 2-2 2h-1v-1l-1-1v-3z" class="H"></path><path d="M589 157c3 1 4 2 6 4l-5-1c-2-1-4-1-5-1v-1c1 0 3 0 4-1z" class="W"></path><path d="M686 506h2l1-1v1l-1 1h2l-2 2-1 1h-1c-2-1-2-1-2-2v-2h2z" class="C"></path><path d="M576 367c3-1 10-1 12 0v1l-1 1-2-1h-9v-1z" class="U"></path><path d="M749 453v-6l-1-1h-1l1-1h1l1-1v5c0 6-1 13 0 19l-1-1v-3-6-5z" class="J"></path><path d="M827 279c4-2 5-5 10-5-2 2-5 6-8 7l-4-1c1 0 1 0 2-1z" class="F"></path><path d="M716 591l1-1c1 0 0 0 1-1v1c1 1 1 1 2 1h0v3l1 1v1h1v1c1 1 2 2 2 3h-1l-1-1v1c-1 0-1 1-2 1 0-1 0-1-1-2 0-1 0-2 1-3l-1-2v-1c-1-1-1-1-2-1l-1-1z" class="B"></path><path d="M823 345l2 1v-1h1c1-1 2-1 3-2h1v1h1c1 1 0 0 0 1l-2 3c-2-1-5-1-8 0v-1l2-2h0z" class="a"></path><path d="M742 635c1-1 2-1 3-1l2 2-1 1h2c1 1 1 2 1 3h0c-1 1-1 2-2 2h-1c0-1 0-1 1-2l-1-1c-2-1-3-2-4-4zm47-348l2-1v1c-1 1-1 2-2 2l1 2h1v-1c2-2 3-3 7-3h0c-2 2-5 5-8 6v-1h-1c-1-1-1-2-2-3l2-2z" class="C"></path><path d="M792 283v-1l1-1c3 2 6-2 9-2v2c-4 1-8 2-11 5h0l-2 1c0-2 1-3 3-4z" class="K"></path><path d="M627 153c3 0 6 2 9 2h1 2c1 1 1 1 1 3v1c-1-1-1-1-2-1s-2 0-3-1c-2 0-7-2-8-4z" class="C"></path><path d="M848 326h1v-2-2l1 1h0c2 1 2 2 3 3-1 4-3 8-5 11v-1h0c0-2 1-3 1-5 0-1 0-1 1-2 0-1-1-2-2-3zM552 151l1-2c3-1 6 0 8 0 5 2 10 2 14 4h-4c-1-1-4-2-5-1-1 0-1 1-1 1-1 0-5-2-7-2h-6z" class="B"></path><path d="M640 168h5s1 1 2 1c3 1 6 1 9 3h-1v2l-1-1c-1-1-2-1-3-1l-6-1-2 2c-1-1-1-1-2-1l-2-2h-1 0 2 2l1 1 1-1h0l1-1h-1-1v1h-2l-1-2z" class="N"></path><path d="M760 257c1 0 1 0 2 1s4 4 6 5h1c2 0 4 1 6 1h0c2 2 3 3 5 3v1c-3 0-5-2-8-2h0c-1 0-1 0-2-1 0 1 1 2 1 3l-11-11z" class="c"></path><path d="M834 271c3 0 6-4 9-5-2 2-4 5-6 8-5 0-6 3-10 5 0 0-1 0-1-1l2-1 6-6z" class="D"></path><path d="M840 262v1l-6 8-6 6-2 1c0 1 1 1 1 1-1 1-1 1-2 1-4 0-9-1-13-1h0 5c4-1 7-1 11-4a30.44 30.44 0 0 0 8-8l2-2 1-2 1-1z" class="L"></path><path d="M590 160l5 1 8 4 2 1 4 3h-4l-3-1c-2-1-3-1-4-2l-2-2c-1-2-2-2-4-2l-2-2z" class="H"></path><path d="M623 171l2-1c1 1 2 1 4 2 2 0 4 1 6 2 1 0 2 0 3 1 0 0 1 0 1 1h1-3l1 1h2v1h-2v1h2v1h-5l1-2v-1c-4 1-6-2-9-4l-4-2z" class="N"></path><path d="M750 440c0 3 1 6 0 9v-5l-1 1h-1l-1 1h1l1 1v6c-1 0-2 0-3 1h0v-4-1h-2 0c-2-1-2-2-3-3l3 2v-1l-1-1 1-2c-1-1-1-1-1-2l2-1 1 1h1c1 0 2 1 3 2v-4z" class="E"></path><path d="M583 382c1 11 0 23 0 35 0 6 2 13 0 19v-20-9c-2-4-1-9-1-13 0 0-1-1-1-2 1-2 1-5 1-7l1-3z" class="B"></path><path d="M577 535h1c0 1 0 2 1 3l3-2h0 3c0 7-1 15 1 23-1-3-2-4-2-6-1-4-2-6-2-9-1-1-1-1-2-1v-1h-1v1s0 1-1 1v-3l-1-1v-5z" class="b"></path><path d="M577 535h1c0 1 0 2 1 3l3-2c0 1 1 2 1 3-1 2-2 2-3 3h-1v1s0 1-1 1v-3l-1-1v-5z" class="o"></path><path d="M710 584h1v1l-2 2c1 1 1 1 3 1h0v-1h-1v-1c0-1 1 0 2 0h1 2v1 1h-3v1h2l1 2 1 1c1 0 1 0 2 1v1l1 2c-1 1-1 2-1 3 1 1 1 1 1 2h-2c-1-3-3-5-4-7h-1c-2-2-4-5-6-8l3-2z" class="H"></path><path d="M634 162h3 1c2 0 3 2 4 3 2 2 3 2 5 3h0 1c1 0 1 1 2 1h2l1 1h2l-1-1-1-1c-1-1-2-1-4-2h1c2-1 8 2 10 4h0c0 1 0 1 1 2h-4-1c-3-2-6-2-9-3-1 0-2-1-2-1h-5 0l-1 1-1-2h0c0-1-1-1-2-2v-1h3v-1c-1 0-3 0-5-1z" class="P"></path><path d="M729 604l1 1c0-1 0-2 1-3h0l2-1c1 1 0 2 2 3 1 0 2 2 3 3h3 1l2 1v-1c1 0 1 0 2-1l1 1v1l-1 1c1 1 1 1 1 3h2 1c1 2 0 3 0 5h-1c0 1 0 1-1 2h-2c0-1 0-1 1-2v-2c1-1 1-1 0-2h0l-1-2c-1-1-3-3-4-3-1 1-1 1-1 2-1-1-1-2-2-2h-1-4-1l-2-1v-1h-1l-1-2z" class="W"></path><path d="M633 148c3-2 11 0 14 1 1 0 3 1 3 1 1 1 1 1 1 2l1 1c-1 1-1 2-2 3h-1c-1 0-1-1-2-1s-2 0-3 1h-1l-1 1c-1-1-1-1-1-2h1 1l1-1 1 1 3-1h0c-1-1-2-1-3-1h0-1l-1-1h-1l-9-4z" class="P"></path><path d="M559 366h11c2 0 4 0 6 1v1h9l2 1-2 1h-1c-2-2-6-1-9-1l-15 1-5-1v-1l3-1 1-1z" class="e"></path><path d="M576 367v1h-8l1-1h7z" class="O"></path><path d="M750 599l-1-12h-1-2l-2-1v-1c1-2 0-3 1-5h0l1-2v-1l-2 2h0c1-2 2-5 4-7 1-2 2-5 2-8l1 1c0 3-1 8-1 11v18 4 1z" class="C"></path><path d="M585 228v11h-2c-1 0-3 1-4 0h-3 0c1 3 2 7 2 10h0c-2-4-2-8-4-12 0-1-1-2-1-3v-3l2 1s1 0 2-1h2 2c1-1 2-2 4-3z" class="J"></path><g class="D"><path d="M579 239c0-1 1-2 2-3 1 1 2 0 2 2v1h0c-1 0-3 1-4 0z"></path><path d="M576 239c-1-1-2-3-2-4v-1h3c0-1 0-1 1-1h3l-1 2 1 1h-3c-1 1-1 1-2 3h0z"></path></g><path d="M741 610c0-1 0-1 1-2 1 0 3 2 4 3l1 2h0c1 1 1 1 0 2v2c-1 1-1 1-1 2h2c1-1 1-1 1-2h1c0 4-3 7-5 10h-1c-1-1-1-2-2-3 1 0 1-1 1-1 1-2-1-4-2-6v-1l-1 1-2-1-2-1v-1l1-1h2 2l1-2-1-1z" class="H"></path><defs><linearGradient id="R" x1="834.082" y1="265.332" x2="852.259" y2="251.46" xlink:href="#B"><stop offset="0" stop-color="#a9a396"></stop><stop offset="1" stop-color="#c4c0ae"></stop></linearGradient></defs><path fill="url(#R)" d="M854 215c1-4 0-10 0-14l1 19c-1 14-1 27-8 40-1 1-2 4-4 5v1c-3 1-6 5-9 5l6-8v-1c1-3 5-5 7-7 0-1 0-2-1-3v-2c2-1 2-2 4-3 1 0 1-1 1-2 1-2 2-4 2-7v-1c1-7 0-15 1-22z"></path><path d="M560 370l15-1c3 0 7-1 9 1h1c-1 2-2 3-3 4-1 2-1 4-1 5l-3 1-1-1-1-2h-3v-2l-1-1-2-1h-2-1c-3 0-7-1-10 0h-4c2-1 4-2 7-2h1c0-1 2-1 4-1h-5 0z" class="S"></path><path d="M841 289l-4-3v-1c-2 0-3-1-4-2h0 1 1c0-1 1-2 2-3l2-2v1h-1c0 2 0 1-1 2s-1 2-2 3h1l2-2h1v-1c2 0 2-2 3-3 2-1 1 1 2 0l2-2 2-2c0-2 3-4 4-6h1v-2c1-1 1-3 0-4l-1 1c-1-1 0-1-1-1v-1s1-1 1-2l2-2v1 14c0 1-1 3-2 4-3 5-9 7-11 13z" class="B"></path><path d="M580 390s0 1 1 2c1-2 0-5 1-7 0 2 0 5-1 7 0 1 1 2 1 2 0 4-1 9 1 13v9 20 2h-1 0l-1-1c-2 1-1 3-2 5v-4-1-1c1 0 1 0 2-1v-1c-1 1-1 1-2 1v-1h1l1-2c-1-2-2-1-3-2l1-3c1 1 0 1 1 2h0v-2-1h-1v-2l1-28h0c-1-2 0-4 0-6z" class="I"></path><path d="M814 335h0 0c0-1 0-1-1-2 1-2 1-2 2-3l2 1c0 1-1 2-1 3 1 0 1 1 3 1h3l-1 2h1l-1 2 3 3c0 1 0 2-1 3h0l-2 2v1c-1 0-6 2-7 2-2-1-2-3-2-4l-3-10c1-1 2-1 3-2l2 1z" class="t"></path><path d="M812 346l1 1v1l1-1c0 1 0 1 1 1l2-2h1c1-1 0-1 1 0h1c1 0 2-1 3-1l-2 2v1c-1 0-6 2-7 2-2-1-2-3-2-4z" class="c"></path><path d="M814 335h0 0c0-1 0-1-1-2 1-2 1-2 2-3l2 1c0 1-1 2-1 3 1 0 1 1 3 1h3l-1 2h1l-1 2h0c-1 0-1 1-2 1l-3-2 1-1-1-1h-1c0 1 0 2-1 2s-2 1-2 0c0-2 0 0 1-1 0-1 0-2 1-2z" class="h"></path><path d="M640 176h1 2c0 1 0 1 1 1h0l1 1c1 1 2 1 2 2l1 1h0v-1-2h1c1 1 1 0 2 0h-1-2v-1c-2 0-4-1-6-2h0c-1 0-1 0-2-1-1 0-4-1-5-2s0-1-1-1c-1-1-1-2-2-3h1c1 0 1 1 2 2l1-1h0l-1-1h1c1 1 1 1 1 2v1l1 1h0 1l1 1h1l1 1c1 1 6 3 8 3h2v1c-1 1-1 1-2 1h0v1c1 1 1 0 1 1 1 2 3 3 3 5h-2l-1 1c-2 0-9-1-10 1v1h0-1c-1-1-2-1-3-1 1 0 1-1 2-1h-1v-4c-2-1-3-1-5-2v1h0 0-1c1-2 1-2 3-2h5v-1h-2v-1h2v-1h-2l-1-1h3 0z" class="H"></path><path d="M652 186l-4-1v-1c0-2 2-2 3-3 1 2 3 3 3 5h-2z" class="D"></path><path d="M640 176h0l2 1c0 1 0 1-1 2 2 1 5-1 6 3 0 0 0 1-1 1-1 2-3 2-5 3l-3-3c-2-1-3-1-5-2v1h0 0-1c1-2 1-2 3-2h5v-1h-2v-1h2v-1h-2l-1-1h3z" class="B"></path><path d="M583 464v-4h1l1 21c3 0 11 0 13 2 1 1 2 2 2 3l2 2v5l-1 1v1l-1 1c-2-2-5-3-6-6-2-1-3-4-6-5v-1c-2 1-2 1-3 0l-2-2-1-2c-1 0-2-1-3-2v-13c1 0 2-1 3-1h1z" class="H"></path><path d="M595 485h1l1 1v1l-1 1c-1 0-2 0-3-1l2-2z" class="W"></path><path d="M582 479l1-1v3h1c2 1 3 2 4 3-2 1-2 1-3 0l-2-2-1-2v-1z" class="w"></path><path d="M582 464h1c0 5 1 10 0 14l-1 1v-9c-1 0-2-2-1-3 0-1 1-1 1-3z" class="s"></path><path d="M579 465c1 0 2-1 3-1 0 2-1 2-1 3-1 1 0 3 1 3v9 1c-1 0-2-1-3-2v-13z" class="C"></path><path d="M816 358l1-1 2 2h1c1 1 0 2 2 1l1-1v2c0 2 4 2 4 4h1 0c0-1 0-1 1-2 1 1 0 4 2 4h2l1-2c1 1 2 2 2 3v3h15 7v1c-6 1-11 0-17 0h-8-17v-2c-1-2 0-4-1-6v-5l1-1z" class="W"></path><path d="M833 372h-17v-2c-1-2 0-4-1-6v-5l1-1c0 1 1 2 2 3v1h2l-1 1v3c1 0 2 1 3 1 0-1-1-1 0-2l2 2 2-1 1 1c-1 1-1 1 0 2v1h1l1-2v1h1v2h2l1-1 1 1-1 1h0z" class="C"></path><path d="M732 633l4-2 2 2c1 0 2 0 4 1v1c1 2 2 3 4 4l1 1c-1 1-1 1-1 2h1c1 0 1-1 2-2v5 2 1 1h-11c-1 1-1 1-2 0l-5-16h1z" class="B"></path><g class="G"><path d="M744 640l-2 2h-1-2c-1 0-1 0-2-1l1-1-1-2h1 0l1 1h1 3c0 1 0 1 1 1z"></path><path d="M746 639l1 1c-1 1-1 1-1 2h1c1 0 1-1 2-2v5 2c-1-2-1-3-4-3-1-1-1-2-1-4l2-1z"></path></g><path d="M738 649h0c2-1 3-3 4-3v1l1 1c0-2 0-2 1-3h1v2 1l2-1v1h2v1h-11z" class="J"></path><path d="M732 633l4-2 2 2c1 0 2 0 4 1v1c1 2 2 3 4 4l-2 1h0c-1 0-1 0-1-1h-3-1l-1-1c1-1 2-2 2-3-1 0-2 0-3 1h0c-1-1-2-1-3-1h-1l-1-2z" class="H"></path><path d="M728 618v-2c2-1 3-2 4-3h1c1 1 1 2 2 3l1-1 2 1 2 1 1-1v1c1 2 3 4 2 6 0 0 0 1-1 1 1 1 1 2 2 3-2 2-4 2-5 3l-3 1-4 2h-1l-4-12h0l1 1h2l1-1-3-1v-2z" class="J"></path><path d="M728 618v-2c2-1 3-2 4-3h1c1 1 1 2 2 3l1-1 2 1c-1 1-2 2-2 3h-1v-2-1c-3 0-5 1-7 2z" class="V"></path><path d="M740 617l1-1v1c1 2 3 4 2 6 0 0 0 1-1 1 1 1 1 2 2 3-2 2-4 2-5 3h0l-1-1c1 0 1 0 2-1-2 0-2-1-3-1h-1v-1s1 0 2 1h1 1l1-3h0c-1 0-1 1-2 1l-1-1 1-1c-1 0-1-1-2-1h-1v-1l4-1v-3z" class="B"></path><path d="M713 594h1c1 2 3 4 4 7h2c1 0 1-1 2-1v-1l1 1h1l2 3c2 0 2 0 3 1l1 2h1v1l2 1h1 4 1c1 0 1 1 2 2l1 1-1 2h-2-2l-1 1v1l-1 1c-1-1-1-2-2-3h-1c-1 1-2 2-4 3v2 2l3 1-1 1h-2l-1-1h0c-3-4-4-9-6-12-3-5-6-10-8-15z" class="G"></path><path d="M734 608h4c-1 1-2 3-3 4h-2c1-1 1-2 1-4z" class="H"></path><path d="M724 604l2-1v3c1 1 1 2 3 3l4-1-1 2h-1l-1 1h-2l-2 2v-1c0-1 0-2 1-2l-1-1h-1c0-2-1-2-1-3h-1l1-2z" class="N"></path><path d="M720 601c1 0 1-1 2-1v-1l1 1h1l2 3c2 0 2 0 3 1l1 2h1v1l2 1-4 1c-2-1-2-2-3-3v-3l-2 1c-1 0-2-1-3-1h-1c-1-1-2-1-2-2h2z" class="H"></path><path d="M720 601c1 0 1-1 2-1v-1l1 1c1 1 1 1 0 2h-2v1h0-1c-1-1-2-1-2-2h2z" class="F"></path><path d="M578 396h2l-1 28v2h1v1 2h0c-1-1 0-1-1-2l-1-1h-1-2c-1 0-1-1-2-2v-1l-2 1-1-1h1c0-4-1-10 0-14 1-1 1-1 1-2s0-1 1-2h0l1-4-1-2 1-1h0l4-2z" class="n"></path><path d="M578 396v3c-1 0-1 1-2 2 0 2 1 4-1 5 0-3 0-6-1-8h0l4-2z" class="E"></path><path d="M578 399c0 5-1 10-1 15-1 1-1 1-2 1v-3c1-1 0-2 0-3v-3c2-1 1-3 1-5 1-1 1-2 2-2z" class="Q"></path><path d="M574 401v4c0 1-1 0 0 2h-1v5c0 4 1 8 0 12v-1l-2 1-1-1h1c0-4-1-10 0-14 1-1 1-1 1-2s0-1 1-2h0l1-4z" class="I"></path><path d="M582 374l1 3v5l-1 3c-1 2 0 5-1 7-1-1-1-2-1-2 0 2-1 4 0 6h0-2l-4 2h0l-1 1 1 2-1 4h0-3v-4-9-3s0-1 1-1l-1-4c-1 0 0 0-1 1h0l-1-1-1-1v2c-1-1-1-1-1-2h0-4v-1h2c1-2 3 0 5-1h4c1-1 3-1 5-1l3-1c0-1 0-3 1-5z" class="Z"></path><path d="M582 374l1 3v5l-1 3c-1 2 0 5-1 7-1-1-1-2-1-2l1-11c0-1 0-3 1-5z" class="E"></path><path d="M572 384l1-1h4c1 1 1 0 1 1-1 3 0 6 0 9v2c0 1-1 1-1 1-1-2 0-5-1-7-1-1-1-2-1-3h-1c-2 0-1-1-2-2z" class="J"></path><path d="M572 384c1 1 0 2 2 2h1c0 1 0 2 1 3 1 2 0 5 1 7-1 1-2 1-3 2h0l-1 1 1 2-1 4h0-3v-4-9-3s0-1 1-1l1-4z" class="D"></path><path d="M574 386h1c0 1 0 2 1 3 1 2 0 5 1 7-1 1-2 1-3 2h0l-1 1h-1v-2c1-1 2-2 2-4v-7z" class="B"></path><path d="M654 169c-1-1-2-1-3-2-2 0-3 0-4-1 1-1 1-1 2-1h2 3 1l3 1c2 1 5 2 7 3 5 3 10 5 15 5h0c1 1 2 1 3 2-2 0-3-1-5-1-1 0-3 1-5 1h0-1l2 2 2 1h-2c-1 0-3-1-4-2-1 1-2 1-4 2-1 0-3 0-5-1-1 0-3 0-4 1-1 0-1 0-2 1h-1-2l-1 1c0-1 0 0-1-1v-1h0c1 0 1 0 2-1v-1h-2c-2 0-7-2-8-3l1-1 2-2 6 1c1 0 2 0 3 1l1 1v-2h1 1 4c-1-1-1-1-1-2h0c-2-2-8-5-10-4h-1c2 1 3 1 4 2l1 1z" class="G"></path><path d="M661 178c3-2 5-2 9-1-1 1-2 1-4 2-1 0-3 0-5-1z" class="D"></path><path d="M654 169c-1-1-2-1-3-2-2 0-3 0-4-1 1-1 1-1 2-1h2 3 1l3 1c2 1 5 2 7 3 5 3 10 5 15 5h0c1 1 2 1 3 2-2 0-3-1-5-1l-18-5h0c-2-2-8-5-10-4h-1c2 1 3 1 4 2l1 1z" class="W"></path><path d="M643 173l2-2 6 1c1 0 2 0 3 1l1 1v-2h1 1l1 1c1 1 2 1 4 2l1 1h-4l-3-1c0 1 0 1-1 2h-3-2c-2 0-7-2-8-3l1-1z" class="B"></path><defs><linearGradient id="S" x1="697.355" y1="529.845" x2="699.361" y2="508.504" xlink:href="#B"><stop offset="0" stop-color="#b4b0a0"></stop><stop offset="1" stop-color="#d8d1c0"></stop></linearGradient></defs><path fill="url(#S)" d="M687 511l1 1c0-2 0-2 2-3 1-1-1-2 1-3 1-1 0-1 2-2 0 1 1 2 2 3l1-1c3 1 4 2 6 4h1v2l2 1c1 1 2 2 2 4h0c-1 1-2 1-2 2 1 0 1 0 3 1h0v1c-1 1-1 1-1 2h-1c-1 0-2 0-3 1l-1 2-3 4v1l-1-1c0-3 0-3-2-5v-4l-1-3c-1-2-2-2-3-2-1-1-1-1-2-1l-1-1-1 1h-1v-4z"></path><path d="M687 511l1 1c0-2 0-2 2-3 1-1-1-2 1-3 1-1 0-1 2-2 0 1 1 2 2 3l1-1c3 1 4 2 6 4h1v2l2 1c1 1 2 2 2 4h0c-1-1-3-1-4-2s0-1-1-1-2-1-2-1v-2h-3l-3-4h-1v2h-2l-1 1v1c1 1 1 1 1 2l2 2c1 0 2 1 4 1l1-1v1c0 2-1 3-1 4l-1 1-1-3c-1-2-2-2-3-2-1-1-1-1-2-1l-1-1-1 1h-1v-4z" class="C"></path><path d="M569 227c2 2 3 5 4 7 0 1 1 2 1 3 2 4 2 8 4 12h0c3 8 4 17 5 26 0 2 1 3 1 5h-1c0-1 0-1-1-2h-3v-1h-2c0-3 1-7-1-9h3v-2l-1 1-1-1h-7c1 0 2 0 3-1-2-3-1-10-1-13 0-4-1-7-1-10-1-1-1-4-1-5-1-1-1-3-1-5 0-1 0-2-1-3l1-2z" class="G"></path><path d="M578 249c3 8 4 17 5 26 0 2 1 3 1 5h-1c0-1 0-1-1-2h-3v-1h-2c0-3 1-7-1-9h3v-2l-1 1-1-1h0v-1l-1-1c0-3-1-6 0-8l1 2v4c0 1 1 1 2 1v1 1c1 4 0 9 2 11h1c-1-5-1-11-2-16l-2-11h0z" class="i"></path><path d="M571 242c1 0 1 0 1 1v2l1 2v-1c0-2 0-4 1-6 0 1 0 3 1 5v1 1c0 1 0 4 1 6v2 1c-1 2 0 5 0 8l1 1v1h0-7c1 0 2 0 3-1-2-3-1-10-1-13 0-4-1-7-1-10z" class="B"></path><path d="M553 373h4c3-1 7 0 10 0h1 2l2 1 1 1v2h3l1 2 1 1c-2 0-4 0-5 1h-4c-2 1-4-1-5 1h-2v1l-1 1c-2 0-3 1-4 1l-1 1h2v2h-1l-1 1-5 1v2h-1l1 1c0 1-1 1-2 2v-14c0-2 0-4 1-7h2l1-1z" class="j"></path><path d="M557 388c-2 0-4 1-5 1v-1-1c1-1 2-2 2-3v-2c1 1 1 2 0 4h2 2v2h-1z" class="X"></path><path d="M554 382c1-1 2-2 3-2 1-1 2-2 4-3 1 0 3 1 5 1v2h2l1 1c-2 1-4-1-5 1h-2v1l-1 1c-2 0-3 1-4 1l-1 1h-2c1-2 1-3 0-4z" class="K"></path><path d="M553 373h4c3-1 7 0 10 0h1 2l2 1 1 1v2h3l1 2h-3l-1-1h-5c-1-1-1-3-1-3h-1c-1 0-2 0-2-1l-1-1c-1 1-1 0-1 1h-1c-1 0-2 0-3 1h0c-3 0-4 3-7 4 0 1 0 3-1 4 0 2 1 4 1 6v1 2h-1l1 1c0 1-1 1-2 2v-14c0-2 0-4 1-7h2l1-1z" class="i"></path><path d="M606 149c2 0 3 0 5 1l1 1h3c6 2 11 6 16 10l3 1c2 1 4 1 5 1v1h-3v1h0-2 0c0 1 1 1 2 1-1 0 0 0-1 1h-1-4l-2-1-1 1-2-1-1-1c-2 0-5-1-7-1v-1l-4-1c-1-1-1-3-1-4-1-1-2-4-3-4l-1 2h-1l-1-1-1 2c-1 0-4-2-5-2-1 1-3 0-4-1l-1-1c-1 0-1 0-2-1l-1-1-1-1 1-1c1 1 2 1 3 1s2 0 3 1h4 1 1 1c0-1 0-1 1-2z" class="P"></path><path d="M606 149c2 0 3 0 5 1 0 2 0 2-1 3l-4-4h0z" class="B"></path><path d="M634 167c-1-1-1-1-2-1s-2 0-2-1h-1c-2-1-3-2-4-4l1-1 5 1 3 1c2 1 4 1 5 1v1h-3v1h0-2 0c0 1 1 1 2 1-1 0 0 0-1 1h-1z" class="H"></path><path d="M841 289c2-6 8-8 11-13 1-1 2-3 2-4v34c0 2 1 6 0 9 0 0-1 1-1 2s1 2-1 3c-1-1 0-2-2-2h0l-2-1h0l-1-1c-2-1-2 0-4 0h0l-1-1c1-1 1-2 1-4h0v-1-1l-2-1c0-1 1-1 1-2-1 0-1 0-2-1h0l-1-1c-1-2-1-3 0-4l1-1c1 1 2 2 2 3 1 1 1 2 2 2 3 2 3 3 4 5l1 1v2c1 1 2 2 2 3 0 0-1 0-1 1l1-1h1c1-1 1-5 1-6v-1c-1-1-1-2-1-2-1-1-1-2-1-3v-1c-1-1-2-3-3-5s-2-3-4-5l1-1h-2l-2-2z" class="W"></path><path d="M843 309l-1-2c1 0 1 0 2 1 0 3 0 3 1 6h1c1-1 2-1 3 0h1c-1 2-1 2-2 3h0l-1-1c-2-1-2 0-4 0h0l-1-1c1-1 1-2 1-4h0v-1-1z" class="H"></path><path d="M771 268c0-1-1-2-1-3 1 1 1 1 2 1h0c3 0 5 2 8 2 1 1 2 1 4 1l1-1c1 0 2 1 3 1 2 0 4 0 5 1h1 1v1c1 0 3 0 4 1 1 0 1 0 2 1l2 1v1c0 1 0 1 2 2h1c0 1-1 1-2 2h-2c-3 0-6 4-9 2l-1 1v1c-2 1-3 2-3 4l-2 2-16-21z" class="d"></path><path d="M789 287c-1 0-1-1-2-1 0-2 1-1 0-2-2-2-7-6-8-8l1-1h3v1c2 2 4 5 8 6l1 1c-2 1-3 2-3 4z" class="b"></path><path d="M689 566h0c0-2 0-3-1-4v-1h2c1 1 2 2 3 2 4 1 7 5 10 5-1-2-1-2-1-3l3 1c1 1 2 1 3 1v1c2 1 6 2 9 2 1 0 1 1 2 1 1 1 3 1 4 1h1 2v1c-1 1-2 1-3 2l-2-2-1 1c1 1 2 1 3 2h1l1 2-1 2c-3 0-5 1-8 2l-3 1-2 1h-1l-3 2-18-20z" class="h"></path><path d="M713 579c-1-1-2-1-3-1v-1c-3-1-5-2-7-4h1l5 3h0 3c2 1 2 1 4 0v-1l-3-2c2 1 3 1 4 2 0 1 0 1-1 2l-1 1 1 2-1 1h1v1l-3 1-2 1h-1c1-2 1-3 3-4v-1z" class="Q"></path><path d="M713 579c1 0 1-1 2 0l-2 4-2 1h-1c1-2 1-3 3-4v-1z" class="a"></path><path d="M702 565l3 1c1 1 2 1 3 1v1c2 1 6 2 9 2 1 0 1 1 2 1 1 1 3 1 4 1h1 2v1c-1 1-2 1-3 2l-2-2-1 1c1 1 2 1 3 2h1l1 2-1 2c-3 0-5 1-8 2v-1h-1l1-1-1-2 1-1c1-1 1-1 1-2-1-1-2-1-4-2-1 0-2-1-3-1-3-1-2-2-4-3 0 0-2-1-3-1-1-2-1-2-1-3z" class="J"></path><path d="M801 285c2 0 3 1 6 1v1l-1 2 1 1v2h-1c2 1 3 1 5 1h0c1 1 1 2 1 3 2 1 4 3 6 4 0 1 1 1 1 2v1c0 1 0 1 1 2h1c0-2-1-1 0-2h1 1c0 2 0 2 2 3h2 0c1 1 0 1 1 2v-3l2-2c1 0 0 0 1 1l1 1 1 1 1 1 3 1v-1l3 1 1 1h1 1v1 1c-2-1-3-2-4-2l-2-1-2 1c0 1 0 2 1 2l1 1 1 1-1 1-1-1-1 1 1 3h-2v1c1 0 1 0 1 1-1 0-1 0-2 1h-2 0l-2-1c-1 1-1 2-2 4l1 1h-1l-1-1c-1 1-1 0-2 1v-2c0-1-1-1-1-2l-1 1-1 1v-1l-4-2c-1 0-1-1-1-2-2 0-3-1-4-1l2-1v-1l-3-3c-2 1-1 2-1 3-1 0-2 0-2-1s-1-1-2-2c-1 0-1 0-2-1h1c1-1 2-1 4-2 1 0 0 0 1-1h-1l1-2c-1-1-1-2-1-2-1-1-1-1-2-1v-1l-1-1h0l-1-1v-2l1-2-1-2c-1-2-2-3-3-4 1-1 1-2 1-3l-2-1z" class="W"></path><path d="M810 305c1 0 1 1 2 1v3h2l2-1h4 1c1 1 2 2 3 4 1-1 2 0 3 0l1-2c1 1 1 2 1 3l1 1c0 1 1 2 2 2v-1c0-1 0-1 2-1v-1h-1v-1h1c1 0 1 0 2 1l-1 1 1 3h-2v1c1 0 1 0 1 1-1 0-1 0-2 1h-2 0l-2-1c-1 1-1 2-2 4l1 1h-1l-1-1c-1 1-1 0-2 1v-2c0-1-1-1-1-2l-1 1-1 1v-1l-4-2c-1 0-1-1-1-2-2 0-3-1-4-1l2-1v-1l-3-3c-2 1-1 2-1 3-1 0-2 0-2-1s-1-1-2-2c-1 0-1 0-2-1h1c1-1 2-1 4-2 1 0 0 0 1-1h-1l1-2z" class="F"></path><path d="M829 313l1 1-2 1c-1 0-1 0-2 1h0l-2-1c1-1 1-1 1-2h4 0z" class="C"></path><path d="M814 309l2-1h4c0 2-1 2-2 3h-1v1c-1 0-1 0-1-1s-1-1-2-2z" class="H"></path><path d="M820 312c1 0 1 1 2 1 0 1 0 2 1 3h0l-2 2h0v1l-2 1-1-2c1-1 1-1 2-1h1v-2l-2-2 1-1z" class="B"></path><path d="M602 488h3c-1-1 0-1-1-1-2-1-4-5-6-7 4 2 9 2 11 6 1 1 2 3 2 4-2-2-4-6-7-6v1h1c1 1 1 2 1 3l1 1 1 1h2s1 1 1 2l2 2h0v1c1 1 1 0 1 1 1 2 3 3 4 4s1 3 3 4h0c1 1 2 1 2 2l4 3h0v-1c-1-1-2-2-2-4 1-1 0-1 1-1h-1c-2-2-4-4-5-7h1l-2-2c-1-1-1-2 0-4h0c3 1 6 2 9 2h2v1c1 2 3 4 5 6-1 2-1 2-2 3h-1l1 1-1 1h-1c0 2-1 3-2 5h-1l-1 1c-1 2-3 3-5 5l-2 2v-1-3c-1-1-2-2-3-2s-1-1-2-1c-1-1 0-1-1-1l-6-3c-4-3-4-5-6-7s-4-3-5-5c-2-1-3-2-4-3h-1c-1-2-2-3-4-5-1-1-1-1-2 0v1l-1-1v-2c1 1 1 1 3 0v1c3 1 4 4 6 5 1 3 4 4 6 6l1-1v-1l1-1v-5z" class="W"></path><path d="M628 509l-2-4v-1l4 2 1-2c0 2-1 3-2 5h-1z" class="P"></path><path d="M801 285l2 1c0 1 0 2-1 3 1 1 2 2 3 4l1 2-1 2v2l1 1h0l1 1v1c1 0 1 0 2 1 0 0 0 1 1 2l-1 2h1c-1 1 0 1-1 1-2 1-3 1-4 2h-1c1 1 1 1 2 1 1 1 2 1 2 2s1 1 2 1c0-1-1-2 1-3l3 3v1l-2 1c1 0 2 1 4 1 0 1 0 2 1 2 0 1 0 2-1 2v4l3 3v1l-1 1-1 1-2-1c-1 1-1 1-2 3 1 1 1 1 1 2h0 0l-2-1c-1 1-2 1-3 2-2-6-4-11-6-17l-12-24c-1-1-2-2-2-3h1v1c3-1 6-4 8-6h1c0-1 1-1 2-2z" class="Q"></path><path d="M795 301h3l1 1h5c-1 1-1 2-2 2h-1 0c-1 1-3 1-4 1 0-2-1-2-2-4z" class="V"></path><path d="M806 300l1 1v1c1 0 1 0 2 1 0 0 0 1 1 2h-3-2l-4-1h1c1 0 1-1 2-2l2-2z" class="E"></path><path d="M809 320l-1 1h0l-2-1v1 1c1 2 2 2 3 4h0l-1 1h-1c0-2-2-3-2-5l-3-7v-1c1 0 2 1 3 2 0-1 0-3-1-4s-1-1-1-2l1-1v1c1 1 1 1 2 1 1 2 2 3 2 5l1-1 1 1h0c0 1-1 3-1 4h0z" class="J"></path><path d="M806 311c1 1 2 1 2 2s1 1 2 1c0-1-1-2 1-3l3 3v1l-2 1c1 0 2 1 4 1 0 1 0 2 1 2 0 1 0 2-1 2v4l3 3v1c-1 0-3-2-3-3-1 0-2 1-2 1l-1-1c1-1 2-2 2-4h-1l-1 1v1h-1c0-2 0-2-1-4-2 0-1 1-2 2v-2h0c0-1 1-3 1-4h0l-1-1-1 1c0-2-1-3-2-5z" class="L"></path><path d="M816 317l-1 1-1 1 1 1h-1c-2 0-2 0-3-1l1-1c-1-1-1-2-1-2h1c1 0 2 1 4 1z" class="E"></path><path d="M801 285l2 1c0 1 0 2-1 3 1 1 2 2 3 4l1 2-1 2v2l1 1h0l-2 2h-5l-1-1h-3l-1-2h0l2-1c0-1 1-2 1-3-1-1-2-2-3-2 1 1 1 2 2 3-1 0-2 1-3 2 0-1-1-2-2-3s-2-2-2-3h1v1c3-1 6-4 8-6h1c0-1 1-1 2-2z" class="G"></path><path d="M801 285l2 1c0 1 0 2-1 3l-1-1c-1 1-1 1-1 2h0-1l-1-1c0-1 1-1 1-2s1-1 2-2zm4 8l1 2-1 2v2l1 1h0l-2 2h-5c0-3 1-2 2-4l-1-1 1-1v-2l1-1c2 1 2 0 3 0zM539 162c0-5-1-11 0-15v1l2 2h6c1 0 4 2 5 1h6c2 0 6 2 7 2 0 0 0-1 1-1 1-1 4 0 5 1h4c2 1 4 1 7 2 2 1 4 2 7 2-1 1-3 1-4 1v1c1 0 3 0 5 1l2 2c2 0 3 0 4 2l2 2c1 1 2 1 4 2l-9-1c-1-1-2-1-2-1h-1c-2 0-4-1-6 0s-5 1-7 1h-1c-4 0-7-1-11-2-2-1-4-1-6-2v-1h-6c-1 1-2 1-3 1 1 0 1 0 2-1l-2-2c-1 0-2 0-3 1h-4-1l-3 1z" class="C"></path><path d="M539 162c0-5-1-11 0-15v1l2 2h6l-2 2h2l1 1h0l-1 1h-1-4v1l-1-1v2 3l1 2-3 1z" class="N"></path><path d="M539 148l2 2h6l-2 2-4 1c-1-2-1-3-2-5z" class="W"></path><path d="M585 159c1 0 3 0 5 1l2 2c2 0 3 0 4 2l2 2h-7-1c-2-1-4-1-6 0-3 1-11 1-14-1-2 0-5-1-6-2h-1-1v-1h2c3-1 7 0 10-1 1-1 2 0 3 0l1-1c2 0 5 0 7-1z" class="F"></path><path d="M577 161l1-1 1 1c1 1 1 1 2 1l-1 1c-1 0-2 0-3-1v-1z" class="H"></path><path d="M742 394c1-1 1-2 2-3 1 0 5-1 6 0v3 46 4c-1-1-2-2-3-2h-1l-1-1-2-2v-2-1-4-1l-1-1c-3 2-5 5-6 8l-1-1h-3-1l-1 1h-1c0-1-1-1-1-2-1 1-2 1-3 2h-1c2-3 3-5 5-7l5-11 5-13 2-5 1-8z" class="J"></path><path d="M745 435h2v3h-2v-3z" class="c"></path><path d="M743 432l2 3v3l-2 1v-2-1-4z" class="D"></path><path d="M745 438h2c1 2 1 2 0 4h-1l-1-1-2-2 2-1z" class="Q"></path><path d="M741 420l4 2 1-1c-1-1-1-2-2-2l1-1c0 1 1 1 2 2v1c0 1-1 3-2 4l-2 1c-1 1-2 2-2 3-2 1-2 1-3 1s-1-2-1-3l3-4c0-1 0-1 1-2v-1z" class="D"></path><path d="M738 430c1-2 2-3 4-5l1 1c-1 1-2 2-2 3-2 1-2 1-3 1z" class="B"></path><path d="M734 420l1 1c0-1 1-2 1-3h0l1 1c0-1 0-2 1-3l1 1h1l1-1h1c-1 2-1 2-1 4v1c-1 1-1 1-1 2l-3 4c0 1 0 3 1 3s1 0 3-1h1l1 1v1l-1-1c-3 2-5 5-6 8l-1-1h-3-1l-1 1h-1c0-1-1-1-1-2-1 1-2 1-3 2h-1c2-3 3-5 5-7l5-11z" class="E"></path><path d="M729 431c1 0 2-1 3-2 1 1 1 1 2 1v-1c0-2 1-2 2-3 0 2-1 3-1 5h-2c0 1-1 3-1 4l1 1c0 1-1 1-1 1h-1l-1 1h-1c0-1-1-1-1-2-1 1-2 1-3 2h-1c2-3 3-5 5-7z" class="J"></path><path d="M734 420l1 1c0-1 1-2 1-3h0l1 1c0-1 0-2 1-3l1 1h1l1-1h1c-1 2-1 2-1 4v1h-2v1c-1 1-2 2-3 4h0c-1 1-2 1-2 3v1c-1 0-1 0-2-1-1 1-2 2-3 2l5-11z" class="I"></path><path d="M742 394c1-1 1-2 2-3 1 0 5-1 6 0v3 4 5 4 11h-1-1c-1 0-1 0-1-1s-1-1-1-2l-1 1v1l-1 1-1-2h-1-1l-1 1h-1l-1-1c-1 1-1 2-1 3l-1-1h0c0 1-1 2-1 3l-1-1 5-13 2-5 1-8z" class="h"></path><path d="M743 405l1-2v-1-1c1 0 1 0 2 1h1l2 1h1v4c-1-1-1-1-2-1s-2-2-3-3c0 1-1 1-2 2z" class="t"></path><path d="M741 402c1 1 1 2 2 3l1 5c0 1 1 1 1 2h-1c-1-1-1-2-1-4l-1-1h-3l2-5z" class="K"></path><path d="M741 402l1-8c2 1 3 1 4 3v5c-1-1-1-1-2-1v1 1l-1 2h0c-1-1-1-2-2-3z" class="d"></path><path d="M742 394c1-1 1-2 2-3 1 0 5-1 6 0v3 4 5h-1l-2-1h-1v-5c-1-2-2-2-4-3z" class="g"></path><path d="M746 402l1-1 1-3 1-1 1 1v5h-1l-2-1h-1z" class="b"></path><path d="M562 383h4 0c0 1 0 1 1 2v-2l1 1 1 1h0c1-1 0-1 1-1l1 4c-1 0-1 1-1 1v3 9 4h3c-1 1-1 1-1 2s0 1-1 2c-1 4 0 10 0 14h-1l1 1 2-1v1c1 1 1 2 2 2h2l-1 1v1l1-1c0 2-1 2-1 3h-1-4-1l1-1c-1-2-1-4-2-5-2-2-6-2-8-2h-4 0l-3 1h-3v-8l1-1c-1-2-1-4-1-6l-2-1c1-4 0-8 0-12 1-1 2-1 2-2l-1-1h1v-2l5-1 1-1h1v-2h-2l1-1c1 0 2-1 4-1l1-1z" class="N"></path><path d="M570 401v4h-1-1 0-1v-1l1-1 2-2z" class="F"></path><path d="M568 384l1 1h0c1-1 0-1 1-1l1 4c-1 0-1 1-1 1v3 9l-2 2v-19z" class="L"></path><path d="M570 405h3c-1 1-1 1-1 2s0 1-1 2c-1 4 0 10 0 14h-1l-2-1c0-4-1-13 1-17h1z" class="g"></path><path d="M562 383h4l-3 3c0 2-1 6 0 9 1 1 0 2 1 4v5h-1c0-2 1-3-1-5v2c0 2-1 5-1 8v5-6c-1-3-2-7-1-10 1-5 0-9 1-14l1-1z" class="F"></path><path d="M556 386l1-1c1 0 2-1 4-1-1 5 0 9-1 14-1 3 0 7 1 10v6 8h-4l1-2-1-1v-12l-1-1v-1l2-1h-1-1c1-2 1-3 1-5v-3c-1-1-2-1-3-1v-1h-1c-1-1-1-1-2-1l-1-1h1v-2l5-1 1-1h1v-2h-2z" class="I"></path><path d="M561 408v6 8h-4l1-2c1-2 0-4 1-5v-5l2-2z" class="E"></path><path d="M557 388h1c1 4 1 8-1 11v-3c-1-1-2-1-3-1v-1h-1c-1-1-1-1-2-1l-1-1h1v-2l5-1 1-1z" class="r"></path><path d="M551 390l5-1h0c0 2 0 3-1 4 0 1-1 1-1 1h-1c-1-1-1-1-2-1l-1-1h1v-2z" class="n"></path><path d="M551 393c1 0 1 0 2 1h1v1c1 0 2 0 3 1v3c0 2 0 3-1 5h1 1l-2 1v1l1 1v12l1 1-1 2h0l-3 1h-3v-8l1-1c-1-2-1-4-1-6l-2-1c1-4 0-8 0-12 1-1 2-1 2-2z" class="l"></path><path d="M554 421c-1-3-1-7-1-10 1-1 1-2 1-4h2c0 1-1 2-1 3 1 1 0 2 0 3 0 2 0 4 1 5v3h-1c-1-1 0-1-1 0z" class="k"></path><path d="M556 407h1v12l1 1-1 2h0l-3 1v-2c1-1 0-1 1 0h1v-3c-1-1-1-3-1-5 0-1 1-2 0-3 0-1 1-2 1-3z" class="r"></path><path d="M553 394h1v1c1 0 2 0 3 1v3c0 2 0 3-1 5h1 1l-2 1v1h-1c-1-1-1-2-1-3 0-3 0-5-2-7l1-2z" class="b"></path><path d="M551 393c1 0 1 0 2 1l-1 2v1c0 1-1 2-1 2v2c1 2 2 0 1 2l1 1v1h-1l1 1 1 1c-1 0-2 1-3 1l-2-1c1-4 0-8 0-12 1-1 2-1 2-2z" class="Z"></path><path d="M836 313l1 1 1-1-1-1-1-1c-1 0-1-1-1-2l2-1 2 1c1 0 2 1 4 2h0c0 2 0 3-1 4l1 1h0c2 0 2-1 4 0l1 1h0l2 1h0c2 0 1 1 2 2 2-1 1-2 1-3s1-2 1-2c0 4 0 7-1 11-1-1-1-2-3-3h0l-1-1v2 2h-1c1 1 2 2 2 3-1 1-1 1-1 2 0 2-1 3-1 5h0v1c-3 4-7 9-12 11h-7l2-3c0-1 1 0 0-1h-1v-1h-1c-1 1-2 1-3 2h-1v1l-2-1c1-1 1-2 1-3l-3-3 1-2h-1l1-2h-3c-2 0-2-1-3-1 0-1 1-2 1-3l1-1 1-1v-1l-3-3v-4c1 0 1-1 1-2l4 2v1l1-1 1-1c0 1 1 1 1 2v2c1-1 1 0 2-1l1 1h1l-1-1c1-2 1-3 2-4l2 1h0 2c1-1 1-1 2-1 0-1 0-1-1-1v-1h2l-1-3 1-1z" class="E"></path><path d="M836 326h1l1 1-1 2h0c-2 0-2-1-2-2l1-1z" class="J"></path><path d="M830 327l2-1c2 1 1 3 4 4 0 2-1 2-2 3l-3-3c0-1 0-2-1-3z" class="Q"></path><path d="M834 334h1c1-1 1-1 1-2h1l2-1-1-2 1-2c1 1 1 1 1 2s2 2 2 2c0 1-1 1-1 2s0 1 1 2l2-1c0 2-1 3-2 4l-2-2h-3l-3-2z" class="J"></path><path d="M837 336h3l2 2v1c-1 2-2 3-2 4-1 2-3 2-4 2l-1 1s-1 1-2 1v-1c-1 0-1 0-2-1 0-1 1 0 0-1h-1v-1h1v-2c1-1 0-1 1-1s2 0 3-1l2-2v-1z" class="a"></path><path d="M831 343c1 0 2-1 3 0h2l-1 1 1 1-1 1s-1 1-2 1v-1c-1 0-1 0-2-1 0-1 1 0 0-1h-1v-1h1z" class="Q"></path><path d="M848 317l2 1h0c2 0 1 1 2 2 2-1 1-2 1-3s1-2 1-2c0 4 0 7-1 11-1-1-1-2-3-3h0l-1-1v2 2h-1c-1 1 0 1 0 2 0 0-2 1-2 2h-1v-2-2c-1 2 0 3-1 4h-1v-2c1-1 1-2 1-4l1-1 2-2c0-2 0-2-1-3l1-2 1 1h0z" class="F"></path><path d="M836 313l1 1 1-1-1-1-1-1c-1 0-1-1-1-2l2-1 2 1c1 0 2 1 4 2h0c0 2 0 3-1 4l1 1h0c2 0 2-1 4 0l-1 2c1 1 1 1 1 3l-2 2c-1-1-1-2-1-3h-3l-1 2v1c1 1 0 2 0 3h-1c0-1 0-2-1-2 0-1-1-1-1-2-1-1-3-2-4-2 1-1 1-1 2-1 0-1 0-1-1-1v-1h2l-1-3 1-1z" class="B"></path><path d="M817 319l4 2v1l1-1 1-1c0 1 1 1 1 2v2c1-1 1 0 2-1l1 1c1 1 1 1 2 1h1v2c1 1 1 2 1 3l3 3-1 1h1l3 2v1l-2 2c-1 1-2 1-3 1s0 0-1 1v2h-1-1c-1 1-2 1-3 2h-1v1l-2-1c1-1 1-2 1-3l-3-3 1-2h-1l1-2h-3c-2 0-2-1-3-1 0-1 1-2 1-3l1-1 1-1v-1l-3-3v-4c1 0 1-1 1-2z" class="c"></path><path d="M824 342c2 0 3-1 5 0v-1l-2-2v1h-1-2v-1l1-3 1 1c2-1 0-1 1-3l2 1c2 0 1 0 3 1l1-1c1 1 1 2 2 4-1 1-2 1-3 1s0 0-1 1v2h-1-1c-1 1-2 1-3 2h-1v1l-2-1c1-1 1-2 1-3z" class="X"></path><path d="M817 319l4 2v1l1-1 1-1c0 1 1 1 1 2v2c1-1 1 0 2-1l1 1c1 1 1 1 2 1h1v2c1 1 1 2 1 3l3 3-1 1h-1c0-2-1-3-3-4v-1-1h-1-3-1l1 2h-1c-1 0-1-1-2-1-2 0-2 0-4 1l1-1v-1l-3-3v-4c1 0 1-1 1-2z" class="J"></path><path d="M817 319l4 2v1c-1 0-1 1-1 2-1-1-1-1-3 0v-3h-1c1 0 1-1 1-2z" class="E"></path><defs><linearGradient id="T" x1="731.051" y1="447.922" x2="733.477" y2="502.194" xlink:href="#B"><stop offset="0" stop-color="#bfb9a9"></stop><stop offset="1" stop-color="#ede7d2"></stop></linearGradient></defs><path fill="url(#T)" d="M741 446h0c1 1 1 2 3 3h0 2v1 4h0c1-1 2-1 3-1v5 6 3l1 1v37l-9-12c-6-7-16-14-26-17 1-2 3-4 4-6 3-3 5-5 7-8 1-1 3-2 4-3 2 0 2 0 3-1l1 1h0c1 0 1 0 2 1v1 1c1 0 2-1 3-2v-1-2-2c1-2 1-4 0-6 1-1 2-2 2-3z"></path><path d="M745 466c0-2 0-3 2-5 0 1 1 2 2 3v3c0 2-1 2-3 3l-1-1v-3z" class="H"></path><path d="M743 454h-1v-1c1-1 2-2 4-3v4h0c1-1 2-1 3-1v5 6c-1-1-2-2-2-3-2 2-2 3-2 5v3h-1l-1-2-1 1c-1 1-1 2-2 3h-1c0-3 2-5 3-8 0-1 1-1 1-1-1-3 0-5 0-8z" class="F"></path><path d="M743 454v-1h1c0 3 1 5-1 9-1-3 0-5 0-8z" class="H"></path><path d="M746 454h0c1-1 2-1 3-1v5l-2-2c-1 0-1-1-1-2z" class="B"></path><path d="M577 339l2-1 2 1c1 1 1 2 1 3l-1 1h1v5 9l1 2c2 0 4-1 5 0v5c-1 1-2 1-4 1h-1v-3h-1c0 1 0 2-1 3h-2c-1-1 0-2 0-3h-1v2l-1 1h0-3-5 0v-1l-1 1h-4c-4 0-8 1-13 0h-1-1l-1 2h-1l-1 4v-5c-1-3 0-5 0-7h2l-1-1v-5-7l1 1v-8h5 7c4-1 8 0 12 0h5z" class="C"></path><path d="M568 356v-6l1-1 1 1h0c0-1 0-2 1-2v3 3h1v2h-1c0-1-1-2-1-2v-1 3h-2z" class="q"></path><path d="M561 344h11c1 0 3 0 4 1h-12c-1 3-1 8 0 11h0 0l-2-1v1h-1 0l1-1c-2-4-1-7-1-11z" class="F"></path><path d="M559 344h2c0 4-1 7 1 11l-1 1h0 1v-1l2 1h0 4 2v-3 1s1 1 1 2h1v-2h1v2l1-1h0v-3c1 0 1 0 2-1s0-5 0-6l2-1c2 3 0 8 1 12v1h3l1 2-21-1c-1 0-2-1-3-1v-1-1-2c-1-2-1-3 0-6v-2-1z" class="f"></path><path d="M576 345l2-1c2 3 0 8 1 12v1h-2-1v1h-7c1-1 5-1 6-2l-1-1h0v-3c1 0 1 0 2-1s0-5 0-6z" class="P"></path><path d="M577 339l2-1 2 1c1 1 1 2 1 3l-1 1h1v5 9h-3v-1c-1-4 1-9-1-12l-2 1c-1-1-3-1-4-1h-11-2-1-2-2-1c-2 0-3 0-4 1v2 9l-1 1v-10-8h5 7c4-1 8 0 12 0h5z" class="j"></path><path d="M553 339h7v1h-9c3 1 5 1 8 1-2 1-5 0-6 1 2 2 4 1 6 1l-1 1h-2-2-1c-2 0-3 0-4 1v2 9l-1 1v-10-8h5z" class="S"></path><path d="M554 344h2 2 1v1 2c-1 3-1 4 0 6v2 1 1c1 0 2 1 3 1l21 1c2 0 4-1 5 0v5c-1 1-2 1-4 1h-1v-3h-1c0 1 0 2-1 3h-2c-1-1 0-2 0-3h-1v2l-1 1h0-3-5 0v-1l-1 1h-4c-4 0-8 1-13 0h-1-1l-1 2h-1l-1 4v-5c-1-3 0-5 0-7h2l-1-1v-5-7l1 1v10l1-1v-9-2c1-1 2-1 4-1h1z" class="g"></path><path d="M549 347v-2c1-1 2-1 4-1-1 2-1 2-3 3 2 0 2 1 2 2-1 1-1 1-1 3l-1-1c0-2-1-3-1-4z" class="m"></path><path d="M549 347c0 1 1 2 1 4l1 1h1c-1 2 0 2-2 4l1 1v-1c1 0 1 0 1 1-1 0-2 1-4 0h0l1-1v-9z" class="Z"></path><path d="M554 344h2c0 1 1 1 0 2-1 2 0 5 0 6s0 2-1 3v1h1c1 1 1 1 2 3l-4-1c-1-1-1-1-1-2 1-4 1-8 1-11v-1z" class="K"></path><path d="M558 344h1v1 2c-1 3-1 4 0 6v2 1 1c1 0 2 1 3 1l-4 1c-1-2-1-2-2-3h-1v-1c1-1 1-2 1-3s-1-4 0-6c1-1 0-1 0-2h2z" class="I"></path><path d="M546 371v-5c-1-3 0-5 0-7h2 17v1h-6v1l-1-1h-1c0 1 0 1 1 2h0-1c-1 1-1 1-1 2h-1v-2h0c-1 1-1 1-2 1h-1-2 0c1 1 1 1 1 2h-1-1l-1 2h-1l-1 4z" class="S"></path><path d="M840 239l1-1 2 2c0 1-1 2-1 3v2h1c1-1 1-1 2-1s2 1 3 1h1v1l-3 3v1h0v2c1 1 1 2 1 3-2 2-6 4-7 7l-1 1-1 2-2 2a30.44 30.44 0 0 1-8 8c-4 3-7 3-11 4h-5 0l-6 1-4 1v-2h2c1-1 2-1 2-2h-1c-2-1-2-1-2-2v-1l-2-1c-1-1-1-1-2-1-1-1-3-1-4-1v-1c-1 0-1 0-1-1s0-1-1-2h2c-1-1-2-1-3-2-1 0 0 0-1-1 2 0 2-1 3-1l1 1 1-1 1-1v1h3v1h4c1 0 2 1 3 1h2 1v-1c1-1 2-1 3-1 1 1 2 1 4 2 1-1 2-1 2-2 1-1 2-2 3-2l-1-1h-1c-1-2 0-2 1-4l-2-2c0-1 0 0-1-1h0v-1c2 0 3 0 4-1h1 4l2-2h0l1-2 5-4c2-2 2-3 5-4z" class="a"></path><path d="M817 267c2 0 3 0 4 1v1c0 1 0 0 1 1l-2 1c0-1 0-1-1-2-1 0-1-2-2-2z" class="c"></path><path d="M818 252c2 0 3 0 4-1h1 4c0 1 0 2 1 3l-1 1-1-1c-1 2-3 3-5 3l-1 2 1 1h-1c-1-2 0-2 1-4l-2-2c0-1 0 0-1-1h0v-1z" class="E"></path><path d="M796 263l1-1v1h3v1h4c1 0 2 1 3 1h2 1c1 1 1 2 2 2l1-1 2 2h1v-3h1v1 1c1 0 1 2 2 2 1 1 1 1 1 2h0c-1 1-2 1-3 1h-1c-1 1-2 0-3 0h-2 0c1 1 2 1 4 1v1c-2 1-3 1-5 1v1 1c-1 0-2 1-3 2l-1 1-4 1v-2h2c1-1 2-1 2-2h-1c-2-1-2-1-2-2v-1l-2-1c-1-1-1-1-2-1-1-1-3-1-4-1v-1c-1 0-1 0-1-1s0-1-1-2h2c-1-1-2-1-3-2-1 0 0 0-1-1 2 0 2-1 3-1l1 1 1-1z" class="X"></path><path d="M796 263l1-1v1h3v1c-1 1-2 1-4 1l-1-1 1-1z" class="Q"></path><path d="M840 239l1-1 2 2c0 1-1 2-1 3v2h1c1-1 1-1 2-1s2 1 3 1h1v1l-3 3v1h0v2c1 1 1 2 1 3-2 2-6 4-7 7l-1 1-1 2-2 2h0c-1-1-2-1-3-2 0-1 0-1-1-2-1 1-2 2-2 3h-1-1l-3 2h-2c0-1 1-1 1-1 1-1 2-1 3-2 2-2 6-5 7-7v-1l1-2v-1l-1 2h-1s-1-1-1-2c-2 1-2 1-2 2v1c-1-1-2-2-3-2l1-1c-1-1-1-2-1-3l2-2h0l1-2 5-4c2-2 2-3 5-4z" class="J"></path><path d="M838 265c-2 0-3 0-4-1 0-1 0-2 1-3 2 0 2 0 4 2l-1 2z" class="a"></path><path d="M830 247h2l2 2v2h-1c0 1-1 1-1 2-2-2-1-2-1-3l-2-1h0l1-2z" class="E"></path><path d="M840 239l1-1 2 2c0 1-1 2-1 3v2h1c1-1 1-1 2-1s2 1 3 1h1v1l-3 3v1h-1c0 1 0 1-1 2l-2-1-3 3c-1-1-2-1-3-1l-1-3 2-1c0-1 0-1 1-2v-1h-2-1-1v3l-2-2h-2l5-4c2-2 2-3 5-4z" class="D"></path><path d="M842 245h1c1-1 1-1 2-1s2 1 3 1l-2 3h-1v-2c-1 1-1 1-2 1 0 1 0 2-1 2l-1-1c0-1 1-2 1-3h0z" class="B"></path><path d="M840 239l1-1 2 2c0 1-1 2-1 3v2h0c0 1-1 2-1 3l1 1c-2 0-2 2-4 2v-1l2-1c1-3 1-4 1-6v-1l-1-3z" class="F"></path><defs><linearGradient id="U" x1="706.859" y1="456.042" x2="718.036" y2="472.028" xlink:href="#B"><stop offset="0" stop-color="#aba799"></stop><stop offset="1" stop-color="#dfd9c6"></stop></linearGradient></defs><path fill="url(#U)" d="M736 438c1-3 3-6 6-8l1 1v1 4 1 2l2 2-2 1c0 1 0 1 1 2l-1 2 1 1v1l-3-2h0c0 1-1 2-2 3 1 2 1 4 0 6v2 2 1c-1 1-2 2-3 2v-1-1c-1-1-1-1-2-1h0l-1-1c-1 1-1 1-3 1-1 1-3 2-4 3-2 3-4 5-7 8-1 2-3 4-4 6-5-2-10-3-15-4s-11-1-16 0l-1 1c6-4 12-7 18-11 9-6 18-16 23-24h1c1-1 2-1 3-2 0 1 1 1 1 2h1l1-1h1 3l1 1z"></path><path d="M726 462c0-1 0-3 1-4 1 1 1 1 3 1-1 1-3 2-4 3zm-11 2c1 1 1 1 1 3-1 1-3 4-5 5v-1c2-2 3-4 4-7h0z" class="H"></path><path d="M736 438c1-3 3-6 6-8l1 1v1 4 1 2l2 2-2 1c0 1 0 1 1 2l-1 2 1 1v1l-3-2h0c0 1-1 2-2 3 1 2 1 4 0 6v2 2 1c-1 1-2 2-3 2v-1-1c-1-1-1-1-2-1h0l-1-1c-1 1-1 1-3 1l3-3c1-2 0-2-1-4-1-1-2-1-4-2v-1h0c-1 1-1 0-1 2h0-1v-2c-1 1-2 3-2 4v3c-1 2-2 2-3 2l-1 2c-1 1-1 2-1 3h-1 0c-2 1-1 2-2 4 0-2 0-2-1-3v-1c2-1 1-4 1-6s1-3 1-4v-2-1c2-4 6-8 8-12 1-1 2-1 3-2 0 1 1 1 1 2h1l1-1h1 3l1 1z" class="F"></path><path d="M731 443l1 1v1l-2 1c-1 0-1 0-1-1l2-2z" class="H"></path><path d="M725 438c1-1 2-1 3-2 0 1 1 1 1 2h1v1l-2 2c-1 1-3 4-4 5 0 1-1 3-2 3h0c-1 0-1 1-2 2v1c-2 0-2 1-3 1v-2-1c2-4 6-8 8-12z" class="D"></path><path d="M736 438c1-3 3-6 6-8l1 1v1 4 1 2l2 2-2 1c0 1 0 1 1 2l-1 2 1 1v1l-3-2h0c0 1-1 2-2 3 1 2 1 4 0 6v2 2 1c-1 1-2 2-3 2v-1-1c-1-1-1-1-2-1h0l-1-1c-1 1-1 1-3 1l3-3c1-2 0-2-1-4-1-1-2-1-4-2v-1l1-1 1 1h1c2 0 3-1 4-3v1 2l1-1v-1-1l-1-2 1-1c-1-1-2-2-3-2h-1-1v-1c-1 0-2 0-3 1l2-2v-1l1-1h1 3l1 1z" class="N"></path><path d="M739 449c1 2 1 4 0 6l-1-2h-1c1-1 1-2 2-4z" class="G"></path><path d="M741 446l1-1c-2-3 0-3 0-5l-1-1v-2h2v2l2 2-2 1c0 1 0 1 1 2l-1 2 1 1v1l-3-2h0zm-9-9h3l1 1-1 1c1 1 1 1 0 3 2 0 2-1 3 0v1h-2c-1-1-2-2-3-2h-1-1v-1c-1 0-2 0-3 1l2-2v-1l1-1h1z" class="B"></path><defs><linearGradient id="V" x1="849.947" y1="142.341" x2="829.064" y2="180.761" xlink:href="#B"><stop offset="0" stop-color="#464542"></stop><stop offset="1" stop-color="#bcb8a7"></stop></linearGradient></defs><path fill="url(#V)" d="M800 142h34c3 1 8 0 12 1 1 1 2 1 4 1h1 2v3h1v1 27c0 4 1 9 0 13v3c-1 1-1 2-1 3h-1v-2c-4-5-5-11-9-16h0v-1-2l-3-3v-1c-1 0-2-1-3-2l1-1-2-2v1s-2-3-3-3l-3-2v-1c-4-3-7-5-12-8l-11-5c-1-1-3-1-5-2l1-1-3-1z"></path><path d="M707 544h5l2-1c0 1 2 2 2 3 0 0 0 1 1 1 0 1 3 2 4 2v1h1c1 0 2 1 3 1l2 2h0c0 1 0 1 1 2 1-1 1-2 1-3 1-2 3-2 3-5h1c0-1 0-1 1-2 2 3 2 4 3 7l-1 1v1h0c-1 2 0 0-1 1 0 1-1 2-1 2-2 1-1 1-3 1v1l2-1v1h1l1-1 3-4h2 1c1-1 1-2 2-3v-1c1-1 2-3 3-4l1 1 1 1 1 1v-2c1 3 1 6 0 10h0c-2 9-8 12-14 18l-5 4c-2 0-4 1-6 1l1-2-1-2h-1c-1-1-2-1-3-2l1-1 2 2c1-1 2-1 3-2v-1h-2-1c-1 0-3 0-4-1-1 0-1-1-2-1-3 0-7-1-9-2v-1c-1 0-2 0-3-1l-3-1 1-1c1-1 1-3 2-4l-5-4 1-1h0 1l3 3c1 1 2 1 3 1v-1-1-2h0v-1l1-1-1-1v1c-2-1-3-2-3-3v-1c-1-1-2-1-3-2s-2-1-3-1h-1v-1c2-1 3-1 4-1l1-1 1 1h3z" class="N"></path><path d="M740 555h1c0 2 1 3 0 4h-1c-1-1-1-1-1-2h0l1-2z" class="F"></path><path d="M709 554h3v-1c0-1 0-1 1-2 3 0 4 0 6 3 0 1 1 1 1 3h-2v1c-1 0-2 0-2-1h-2-3c-1-1-2-2-2-3z" class="W"></path><path d="M723 566v-2c2-2 3-1 5-2-1-1 0-1-1-1 1-1 1-1 3-1 0 1 1 2 2 3 0-1 1-1 1-2s-1-1-1-1c2 0 2 0 3 1v1c-1 1-1 1-1 2-1 1-2 2-3 2-2 1-4 2-6 2v1h4 1s1-1 2-1c0-1 1-1 2-1v2c0 1-1 1-2 2l1 1h1v1c1 1 1 1 1 2l-5 4c-2 0-4 1-6 1l1-2-1-2h-1c-1-1-2-1-3-2l1-1 2 2c1-1 2-1 3-2v-1h-2-1l3-1v-1h-4l-1-1 1-1v-1h1v-1z" class="G"></path><path d="M724 576c1-1 2-1 4-1 1 1 2 1 2 2v2c-2 0-4 1-6 1l1-2-1-2z" class="D"></path><path d="M700 556l1-1h0 1l3 3c1 1 2 1 3 1v-1-1-2h0v-1h1c0 1 1 2 2 3h1l2 1c1 1 2 1 3 2l3 3 3 3v1h-1v1l-1 1 1 1h4v1l-3 1c-1 0-3 0-4-1-1 0-1-1-2-1-3 0-7-1-9-2v-1c-1 0-2 0-3-1l-3-1 1-1c1-1 1-3 2-4l-5-4z" class="V"></path><path d="M705 560c1 0 1 1 3 2h1c1 1 1 2 1 4h-1c-2 0-4-1-6-2 1-1 1-3 2-4z" class="E"></path><path d="M705 513c1-1 1-1 2-1l2-2c1 1 2 2 3 2h1c1 0 2 2 2 2 1 0 1-1 1-2l1-1c2 1 2 4 3 6h0-2l-1 1c2 3 4 5 6 7v1h-1 0v1c3 3 5 7 8 10h-1v2l1 2c1 2-2 1 1 3h0c2 0 3 0 4 1l2 3v4c-1-3-1-4-3-7-1 1-1 1-1 2h-1c0 3-2 3-3 5 0 1 0 2-1 3-1-1-1-1-1-2h0l-2-2c-1 0-2-1-3-1h-1v-1c-1 0-4-1-4-2-1 0-1-1-1-1 0-1-2-2-2-3l-2 1h-5-3l-1-1-1 1c-1 0-2 0-4 1h-1l-1-1h-3-1l-7-7-1-1c-1-2 0-2 0-4h1 1l1 1c0-2-1-3-1-4 2-1 4-1 5-2s1 0 1-1c0-2 0-2 1-2s2 0 3 1c2 2 2 2 2 5l1 1v-1l3-4 1-2c1-1 2-1 3-1h1c0-1 0-1 1-2v-1h0c-2-1-2-1-3-1 0-1 1-1 2-2h0c0-2-1-3-2-4z" class="W"></path><path d="M721 532l1 1s1 1 1 2l-1 1c-1 0-2-2-2-3l1-1zm-1 4v1c1 0 0 0 1 1 1 0 1 1 2 1l1 1h-1c-1 1-1 1-2 1h-2c0-2 0-4 1-5z" class="N"></path><path d="M706 523l1 1s1 0 2-1v-1h0c1 0 1 1 2 2l2 4-1 1h-3c1 1 1 1 2 1-1 1-2 0-4 1h0c-1-2-2-3-2-4v-1h-3l1-2c1-1 2-1 3-1z" class="F"></path><defs><linearGradient id="W" x1="696.849" y1="538.77" x2="712.312" y2="527.116" xlink:href="#B"><stop offset="0" stop-color="#a7a394"></stop><stop offset="1" stop-color="#cac4b4"></stop></linearGradient></defs><path fill="url(#W)" d="M691 527c1-1 1 0 1-1 0-2 0-2 1-2s2 0 3 1c2 2 2 2 2 5l1 1v-1l3-4h3v1c0 1 1 2 2 4h0c2-1 3 0 4-1h1l1 1c1 0 1 0 2 1h1c1 1 2 1 2 1l1 1s0 1 1 2c-1 1-1 3-1 5-1 0-2 0-3 1-1 0-1 1-2 1l-2 1h-5-3l-1-1-1 1c-1 0-2 0-4 1h-1l-1-1h-3-1l-7-7-1-1c-1-2 0-2 0-4h1 1l1 1c0-2-1-3-1-4 2-1 4-1 5-2z"></path><path d="M702 526h3v1c0 1 1 2 2 4h0l1 2h0l1 1 2 2c-1 0-1 1-1 2s0 1-1 2c-5 0-6-7-9-9l-1-1 3-4z" class="L"></path><path d="M686 529c2-1 4-1 5-2 0 2 0 3 1 4l1 2h-1l11 10h1 1c1 0 2 1 2 1h-3l-1-1-1 1c-1 0-2 0-4 1h-1l-1-1h-3-1l-7-7-1-1c-1-2 0-2 0-4h1 1l1 1c0-2-1-3-1-4z" class="J"></path><path d="M686 529c2-1 4-1 5-2 0 2 0 3 1 4l1 2h-1l11 10h1 1c1 0 2 1 2 1h-3l-1-1-1 1c-1 0-2 0-4 1h-1l-1-1c2 1 1 0 2-1 0 0 2 1 3 0-1-3-8-6-10-9-1-1-1-1-2-1 0 1-1 1-1 1-2 0-2-1-3-1l-1 3c-1-2 0-2 0-4h1 1l1 1c0-2-1-3-1-4z" class="D"></path><path d="M667 531l-1-1v-1c-1 0-1-1-1-1v-2c1 0 1-1 2-1l3 3c1 2 2 3 2 5l2 2c1 1 3 3 5 4l-7-8 1-2h0l3 1h0c1 2 3 3 3 5h2l2 2h0 2l7 7h1 3l1 1h1v1h1c1 0 2 0 3 1s2 1 3 2v1c0 1 1 2 3 3v-1l1 1-1 1v1h0v2 1 1c-1 0-2 0-3-1l-3-3h-1 0l-1 1 5 4c-1 1-1 3-2 4l-1 1c0 1 0 1 1 3-3 0-6-4-10-5-1 0-2-1-3-2h-2v1c1 1 1 2 1 4h0l-21-16-5-2c-1-3-4-4-6-6-1-1-2-3-2-4l-1-1-1-4 1-2s-1-1-1-2h0l1-1h3l-1-1c2 0 2 1 4 2 1 1 1 1 2 0 2 0 3 1 5 2z" class="Q"></path><path d="M683 537h2l7 7h1l2 3v1l-4-2c-3-4-6-6-8-9z" class="N"></path><path d="M670 538l1-1c2 4 5 6 7 9 0 2 1 3 3 3v1h-3l-1-1c-1-1-2-2-2-3-1-1-1-2-2-2-1-2-2-5-3-6z" class="B"></path><path d="M693 544h3l1 1h1v1h1c1 0 2 0 3 1s2 1 3 2v1c0 1 1 2 3 3v-1l1 1-1 1v1h0v2 1 1c-1 0-2 0-3-1l-3-3h-1 0l-1 1c-1-1-1 0-1-1v-1h-1c-1-2-2-3-3-5-2 0-3-2-4-3l4 2v-1l-2-3z" class="L"></path><path d="M702 552c2 0 3 0 4 1 0 1 0 1 1 1v2h-2c-1 0-1-1-1-1-1-1-2-1-2-3z" class="E"></path><path d="M657 528l-1-1c2 0 2 1 4 2 1 1 1 1 2 0 2 0 3 1 5 2 0 1 0 1 1 2v1l-1 2c1 1 2 1 3 2s2 4 3 6c1 0 1 1 2 2 0 1 1 2 2 3l1 1v2c1 1 0 1 1 2l1-3c1 0 1-1 2 0h2l1-1c1 1 2 3 4 3 2 1 3 4 6 5l2 3h-1v1c1 1 1 0 3 1h0c1 1 2 1 3 2 0 1 0 1 1 3-3 0-6-4-10-5-1 0-2-1-3-2h-2v1c1 1 1 2 1 4h0l-21-16-5-2c-1-3-4-4-6-6-1-1-2-3-2-4l-1-1-1-4 1-2s-1-1-1-2h0l1-1h3z" class="I"></path><path d="M680 551l2 1-1 2 2 2c-1 1-1 0-2 1l-2-1v-2l1-3z" class="c"></path><path d="M673 544c1 0 1 1 2 2 0 1 1 2 2 3l1 1v2c1 1 0 1 1 2v2c-1 0-2-1-3-1-1-1-1-2-2-2l-1-1v-1-1c-1-1-2-3-2-4v-2l2 1v-1z" class="D"></path><path d="M654 528h3l1 1h1l-1 1h0c2 1 4 4 6 6 1 1 1 3 3 4 0 1 1 3 1 4s0 2 1 3 1 2 1 3h-1-1l-5-2c-1-3-4-4-6-6-1-1-2-3-2-4l-1-1-1-4 1-2s-1-1-1-2h0l1-1z" class="X"></path><path d="M562 224v-1c0-1 1-2 2-3h2c2 1 3 5 3 7l-1 2c1 1 1 2 1 3 0 2 0 4 1 5 0 1 0 4 1 5 0 3 1 6 1 10 0 3-1 10 1 13-1 1-2 1-3 1h7l1 1 1-1v2h-3c2 2 1 6 1 9h2v1c-1 1-1 0-2 0-3 1-5 1-8 1h-1-2c-2 0-4 0-6-1h-1l-1 1v1c-2-1-2-1-3 0h-1v1l-3 1h-4v3c-1-2-1-4-1-6l1-6v-6c0-2 1-7 2-10v-3l3-10c0-1 2-5 2-7 2-4 4-8 6-13l1 1 1-1z" class="h"></path><path d="M560 224l1 1-1 3c-2 2-3 6-5 9-1 4-3 9-4 14l-1 4v3h0v-1c1-3 3-7 3-10h1c-1 3-1 7-1 10l-2 4h0l-1-1-1 2v-1-3-1-3l3-10c0-1 2-5 2-7 2-4 4-8 6-13z" class="n"></path><path d="M562 224c0 5 0 11-1 16h0v-1l-1 2-1-1h-1c0 2 0 1-1 2 0-3 0-3-2-5 2-3 3-7 5-9l1-3 1-1z" class="E"></path><path d="M560 228v4c-1 2-1 6-1 8h-1c0 2 0 1-1 2 0-3 0-3-2-5 2-3 3-7 5-9z" class="T"></path><path d="M549 257v1 3 1l1-2 1 1h0l2-4v8l1 1v-1l2 1h0 3 2 1 0c-1 0-2 0-4 1h-5v1h-2v7h-1l-1-1h-1c0-2 1-5-1-7 0-2 1-7 2-10z" class="j"></path><path d="M553 257v8c-1 0-1 1-2 1l-1-1c0-1 1-3 1-4h0l2-4z" class="b"></path><path d="M559 240l1 1 1-2v1 7c-1 5-1 12 0 17l1 1h1 1 5 1v1h7l1 1 1-1v2h-3-2-21v-1h5c2-1 3-1 4-1h0-1-2-3 0l-2-1v-1c-1-5 1-8 2-13 0-3 0-6 2-9v-2h1z" class="a"></path><path d="M558 242l-1 23h1v-3h1v4h-3 0l-2-1v-1c-1-5 1-8 2-13 0-3 0-6 2-9z" class="X"></path><path d="M547 267c2 2 1 5 1 7h1l1 1h1v-7h2 21 2c2 2 1 6 1 9h2v1c-1 1-1 0-2 0-3 1-5 1-8 1h-1-2c-2 0-4 0-6-1h-1l-1 1v1c-2-1-2-1-3 0h-1v1l-3 1h-4v3c-1-2-1-4-1-6l1-6v-6z" class="Y"></path><path d="M547 267c2 2 1 5 1 7h1l1 1h1v2c1-1 1 0 2-1l1 1c1-1 0-5 1-5v4 1c-2 1-2 0-4 1h-1c-1 1-1 2-3 2v1l4 1h-4v3c-1-2-1-4-1-6l1-6v-6z" class="m"></path><path d="M555 276s1 0 1-1c1-2-1-4 1-6h1v1h1l1-1v1h0v1l1-2 1 1c-1 2 0 0-1 1v4h1v-3h1c1 1 0 3 0 5h0-8v-1z" class="O"></path><path d="M574 268h2c2 2 1 6 1 9h2v1c-1 1-1 0-2 0-3 1-5 1-8 1h-1-2c-2 0-4 0-6-1h-1l-1 1v1c-2-1-2-1-3 0h-1v1l-3 1-4-1v-1c2 0 2-1 3-2h1c2-1 2 0 4-1h8c1 0 1 0 2-1v-2-2h0c1 1 1 3 1 4l1 1c1-1 0-3 1-4 0 2 0 3 1 4v-1l2 1 1-1h1 1v-7-1z" class="K"></path><path d="M562 224v-1c0-1 1-2 2-3h2c2 1 3 5 3 7l-1 2c1 1 1 2 1 3 0 2 0 4 1 5 0 1 0 4 1 5 0 3 1 6 1 10 0 3-1 10 1 13-1 1-2 1-3 1v-1h-1-5-1-1l-1-1c-1-5-1-12 0-17v-7h0c1-5 1-11 1-16z" class="C"></path><path d="M566 220c2 1 3 5 3 7l-1 2h0c-1-1 0-4-2-5v-1-3z" class="B"></path><path d="M562 224v-1c0-1 1-2 2-3v7c1 4 0 10 0 14l-1 2c-1 1-1 1-1 2-1 1 0 1-1 2v-7h0c1-5 1-11 1-16z" class="G"></path><path d="M570 237c0 1 0 4 1 5 0 3 1 6 1 10 0 3-1 10 1 13-1 1-2 1-3 1v-1h-1c-1 0-1-1-1-1 4-4 0-21 2-27z" class="P"></path><path d="M565 241v-15l1-1v1c-1 4 0 10 0 14 0 1 1 3 1 4 1 6 1 12 1 18v2s0 1 1 1h-5-1-1l-1-1c-1-5-1-12 0-17 1-1 0-1 1-2 0-1 0-1 1-2l1-2h1z" class="B"></path><path d="M565 264c0-1 1-1 1-2 1-2-1-5 1-7 1 1-1 4 0 5 0 1 1 1 1 2v2s0 1 1 1h-5l1-1z" class="H"></path><path d="M564 241h1c-1 2-1 4-1 7l1 16-1 1h-1c1-3 0-9 0-12v-10l1-2z" class="P"></path><path d="M561 247c1-1 0-1 1-2 0-1 0-1 1-2v10c0 3 1 9 0 12h-1l-1-1c-1-5-1-12 0-17zm18 231c1 1 2 2 3 2l1 2 2 2v2l1 1v-1c1-1 1-1 2 0 2 2 3 3 4 5h1c1 1 2 2 4 3 1 2 3 3 5 5s2 4 6 7l6 3c1 0 0 0 1 1 1 0 1 1 2 1s2 1 3 2v3 1c-1 1-1 2-1 3h-1c-2 0-3 1-4 1h0-5c1 2 2 6 3 7l2-1v1c0 1 1 1 2 2l-3 1-26-2h-2c-1 2 0 5 0 7h-3 0l-3 2c-1-1-1-2-1-3 1-1 1-3 1-4 1-1 2-1 2-1 1-1 1-1 1-2l1-1c-2 0-2-1-3-2l1-1h-2c-1-1 0-3 0-4v-1c-1-1-1-2 0-3v-1l1-2h0l-1-1v-4-1-3-16-6-4z" class="V"></path><path d="M606 519c2 0 5 0 7 1 1 1 1 0 1 1h-5c0-1-2-1-3-2z" class="E"></path><path d="M579 478c1 1 2 2 3 2l1 2 2 2v2l1 1v-1c1-1 1-1 2 0 2 2 3 3 4 5h1c1 1 2 2 4 3h-3 0c-1 2-3 3-2 6l1 1v1 2h-1c0-1 0-2-1-3-1-2-3-2-4-2h-1v-1c1 0 1-1 2-2-1-2-1-3-3-5 0-1 0-1-1-2-1-2-2-3-4-4h0v-2l-1-1v-4z" class="F"></path><path d="M579 478c1 1 2 2 3 2l1 2h-1c0 1 0 1-1 1 1 1 3 2 3 4v2c-1-2-2-3-4-4h0v-2l-1-1v-4zm14 24v-1l-1-1c-1-3 1-4 2-6h0 3c1 2 3 3 5 5s2 4 6 7l6 3c1 0 0 0 1 1 1 0 1 1 2 1s2 1 3 2v3 1c-1 1-1 2-1 3h-1c-1-1-1-1-1-2 1-1 2-2 2-3-2-1-3-2-4-3-4-1-4 0-7 2v2h-2 0l-1 1v-2l1-1v-5l-1-1h-1c1-1 1-1 1-2l-4-4c-2 0-6-1-8 0z" class="L"></path><path d="M614 509c1 0 0 0 1 1 1 0 1 1 2 1h0c-2 0-5 0-6-1v-1c1 1 1 1 3 0z" class="F"></path><path d="M579 482l1 1v2h0v3c0 2 0 3 1 5 0 1 0 1-1 2l2 2c-1 1-2 1-2 2l2 2h0c-1 2-1 1-1 2v1h0l2 1h1v1c2 2 3 1 5 1 2 1 3 0 5 1l1 1c0 1 0 2 1 3l1 1c-2 0-3 0-5 1 0 1-1 2-1 3-2 1-3 2-6 3-2 0-3 1-5 0h-1v-1c-1-1-1-2 0-3v-1l1-2h0l-1-1v-4-1-3-16-6z" class="I"></path><path d="M580 513l1 1v1l1 1-1 1 1 2-2 1h-1v-1c-1-1-1-2 0-3v-1l1-2h0z" class="h"></path><path d="M581 504l2 1h1v1c2 2 3 1 5 1 2 1 3 0 5 1v2c-1 1-1 2-1 3-1 0-2 0-2 1v1l-2-3c-2-3-3-2-6-2v-1h3v-1h-2c-1-1-2-3-3-4h0z" class="J"></path><path d="M594 508l1 1c0 1 0 2 1 3l1 1c-2 0-3 0-5 1 0 1-1 2-1 3-2 1-3 2-6 3-2 0-3 1-5 0l2-1h0c1 0 2-1 2-1h1v-1h-1l1-1c1-1 1-1 2-1h1c0-1 1-2 1-3l2 3v-1c0-1 1-1 2-1 0-1 0-2 1-3v-2z" class="D"></path><path d="M589 512l2 3-2 2h-2v-1h-2c1-1 1-1 2-1h1c0-1 1-2 1-3z" class="a"></path><defs><linearGradient id="X" x1="591.981" y1="532.377" x2="602.007" y2="516.566" xlink:href="#B"><stop offset="0" stop-color="#88867c"></stop><stop offset="1" stop-color="#a49f8f"></stop></linearGradient></defs><path fill="url(#X)" d="M591 517c0-1 1-2 1-3 2-1 3-1 5-1 2 1 3 2 5 3h2v1h-2l1 1c-1 1-1 2-3 2l1 1c1-1 3-1 5-2 1 1 3 1 3 2 1 2 2 6 3 7l2-1v1c0 1 1 1 2 2l-3 1-26-2h-2c-1 2 0 5 0 7h-3 0l-3 2c-1-1-1-2-1-3 1-1 1-3 1-4 1-1 2-1 2-1 1-1 1-1 1-2l1-1c-2 0-2-1-3-2l1-1h-2c-1-1 0-3 0-4h1c2 1 3 0 5 0 3-1 4-2 6-3z"></path><path d="M583 527v1c3 1 7 0 10 1h-6-2c-1 2 0 5 0 7h-3 0l-3 2c-1-1-1-2-1-3 1-1 1-3 1-4 1-1 2-1 2-1 1-1 1-1 1-2l1-1z" class="t"></path><path d="M591 517c0-1 1-2 1-3 2-1 3-1 5-1 2 1 3 2 5 3h2v1h-2l1 1c-1 1-1 2-3 2l1 1v2c-2 1-2 0-4 0-2 1-3 3-5 3v-1c1 0 1 0 2-1l4-2h0c0-1 1-2 0-3-1 0-1-1-2-1l-1-1c-1 0-2 1-4 0z" class="J"></path><path d="M541 276v1c1 1 1 1 1 2v1c1 0 1 1 1 2 1 0 2-2 3-3h0c0 2 0 4 1 6v-3h4l3-1v-1h1c1-1 1-1 3 0v-1l1-1h1c2 1 4 1 6 1h2 1c3 0 5 0 8-1 1 0 1 1 2 0h3c1 1 1 1 1 2h1v2c-1 1-1 2-2 3-1 0-1 1-1 2-2 4 1 11-1 16 1 6 0 12 0 18v8l1 1c0 2 1 4 0 6v1l-1 1h-1l-2 1h-5c-4 0-8-1-12 0h-7-5l1-8v-4-1h-2c-1-2-1-4-2-6l-2-10-1-2c-1 1-1 1-1 2v3c-1-3-1-5-1-7-1-3-2-5-3-8l-1-5-3-1c0-2-1-2-2-3 0-1-1-1-1-1 0-2 0-3-1-5 3 0 8-1 10 0h2-1l1-2v-5z" class="P"></path><path d="M568 327v-14h1v2c0 3 1 8 1 12h-1-1z" class="F"></path><path d="M575 301c0 1 1 2 1 3-1 1-1 1-2 3 0-1 0-1-1-2l-2 2h0l-1 1-1-1v-2-3c-1 0-2-1-3-2l2-2 2 2h2c1 1 2 1 3 1zm-7 28l9-1v-3 1h-1v-1c1-2 0-2 0-4-2 0-3-1-4-2h0 3 1c3-2-1-10 1-14v-1c0-1 0-2 1-3v-1c0 7-1 15 0 22 0 2 0 6 1 8h0-1c-1-1-2-1-3-1h-7 0z" class="H"></path><path d="M570 285h8v12 1c-1 1-1 2-1 4l-2-1c-1 0-2 0-3-1h-2v-6c-1-3 0-6 0-9z" class="q"></path><path d="M561 285h5c-1 3 0 5-1 7s0 3 0 4c0 2 0 3-1 5v9c1 2 1 4 1 6v11h3c-1 0-1 0-2 1l2 1h0-10c0-1 1-1 2-1h1l-1-1v-18-12c0-4 0-8 1-12z" class="N"></path><defs><linearGradient id="Y" x1="547.293" y1="284.64" x2="580.687" y2="290.716" xlink:href="#B"><stop offset="0" stop-color="#1f1e1d"></stop><stop offset="1" stop-color="#3d3a37"></stop></linearGradient></defs><path fill="url(#Y)" d="M554 281v-1h1c1-1 1-1 3 0v-1l1-1h1c2 1 4 1 6 1h2 1c3 0 5 0 8-1 1 0 1 1 2 0h3c1 1 1 1 1 2h1v2c-1 1-1 2-2 3-1 0-1 1-1 2-2 4 1 11-1 16v3c0 2-1 3-1 5-1 3 0 7-1 11-1-7 0-15 0-22v-3-12h-8-4-5-1-3-4 0c-1 1-2 2-3 2h0s0-1-1-2h-2v-3h4l3-1z"></path><path d="M554 281v-1h1c1-1 1-1 3 0v-1l1-1h1c2 1 4 1 6 1h2 1c3 0 5 0 8-1 1 0 1 1 2 0h3c1 1 1 1 1 2h1v2l-30-1z" class="N"></path><path d="M580 303c1 6 0 12 0 18v8l1 1c0 2 1 4 0 6v1l-1 1h-1l-2 1h-5c-4 0-8-1-12 0h-7-5l1-8 1-1 1-1 3 1v-1h3 1 10 7c1 0 2 0 3 1h1 0c-1-2-1-6-1-8 1-4 0-8 1-11 0-2 1-3 1-5v-3z" class="D"></path><path d="M578 337v-3l1-1c1 2 1 3 1 5h-1l-2 1c-3-2-7 0-11-1v-1c4 0 9 1 12 0z" class="C"></path><path d="M549 331l1-1 1 3c2 1 6 0 9 0 1 0 3 1 5 0 3 0 7-1 10 0l1 1c-7 0-14-1-21 0 1 1 2 1 3 1v1h-7c1 1 3 1 5 1l-3 1v1h0-5l1-8z" class="i"></path><path d="M580 303c1 6 0 12 0 18v8l1 1c0 2 1 4 0 6v1l-1 1c0-2 0-3-1-5l-1 1v3l-1-1v-1l-1-1-1-1c-3-1-7 0-10 0-2 1-4 0-5 0-3 0-7 1-9 0l-1-3 1-1 3 1v-1h3 1 10 7c1 0 2 0 3 1h1 0c-1-2-1-6-1-8 1-4 0-8 1-11 0-2 1-3 1-5v-3z" class="Y"></path><path d="M558 329h10 7v2h0c-1-1-2 0-2 0-2 0-5-1-6 0-2 1-3 1-5 1l-2 1c-3 0-7 1-9 0l-1-3 1-1 3 1v-1h3 1z" class="S"></path><path d="M558 329h10 7v2h0c-1-1-2 0-2 0-2 0-5-1-6 0h-5l-1-1h-7v-1h3 1z" class="R"></path><path d="M541 276v1c1 1 1 1 1 2v1c1 0 1 1 1 2 1 0 2-2 3-3h0c0 2 0 4 1 6h2c1 1 1 2 1 2h0c1 0 2-1 3-2h0 4 3 1c-1 4-1 8-1 12v12 18l1 1h-1c-1 0-2 0-2 1h-1-3v1l-3-1-1 1-1 1v-4-1h-2c-1-2-1-4-2-6l-2-10-1-2c-1 1-1 1-1 2v3c-1-3-1-5-1-7-1-3-2-5-3-8l-1-5-3-1c0-2-1-2-2-3 0-1-1-1-1-1 0-2 0-3-1-5 3 0 8-1 10 0h2-1l1-2v-5z" class="b"></path><path d="M557 285h3c-3 2-3 2-3 5v3c-1 1-1 3-1 5 1 2 0 4 1 6h0c-1 2 0 6-1 8v3 2l1 5c0 1-1 3 0 5l1 1-1 1h-3-1v-1h2c1-3 0-6 0-9 0-1-1-3-1-5 1-1 1 0 0-1v-1c2-2 1-7 0-10 0-1 1-2 0-3h0l1-1-1-2c1-1 1-2 2-4 0-2 0-5 1-7z" class="X"></path><path d="M560 285h1c-1 4-1 8-1 12v12 18l1 1h-1c-1 0-2 0-2 1h-1l1-1-1-1c-1-2 0-4 0-5l-1-5v-2-3c1-2 0-6 1-8h0c-1-2 0-4-1-6 0-2 0-4 1-5v-3c0-3 0-3 3-5z" class="K"></path><path d="M549 285c1 1 1 2 1 2h0c1 0 2-1 3-2h0c-1 4-1 7-1 11l1 20c-1 4 0 8-1 12l-1 1h0l-1 1-1 1v-4-1-5-36z" class="O"></path><path d="M549 327v-1l1-6c1 2 2 6 2 8l-1 1h0l-1 1-1 1v-4z" class="M"></path><defs><linearGradient id="Z" x1="551.015" y1="316.851" x2="540.778" y2="282.613" xlink:href="#B"><stop offset="0" stop-color="#878680"></stop><stop offset="1" stop-color="#b5b2aa"></stop></linearGradient></defs><path fill="url(#Z)" d="M541 276v1c1 1 1 1 1 2v1c1 0 1 1 1 2 1 0 2-2 3-3h0c0 2 0 4 1 6h2v36 5h-2c-1-2-1-4-2-6l-2-10-1-2c-1 1-1 1-1 2v3c-1-3-1-5-1-7l-3-8-1-5-3-1c0-2-1-2-2-3 0-1-1-1-1-1 0-2 0-3-1-5 3 0 8-1 10 0h2-1l1-2v-5z"></path><path d="M541 283h1c1 1 1 3 1 4s-1 1-1 2v-2h-1c-1-1-1-2-2-2h0v-2h2z" class="R"></path><path d="M541 287h1v2c-1 1-1 2-3 3 0-2 0-3-1-5h3z" class="M"></path><path d="M545 320c1-1 1-2 2-3 1 1 1 2 2 4v5h-2c-1-2-1-4-2-6z" class="K"></path><path d="M539 293h2v3c1 4 3 9 3 13l-1 1-1-2c0-1-3-13-3-15z" class="T"></path><g class="O"><path d="M538 293h1c0 2 3 14 3 15-1 1-1 1-1 2v3c-1-3-1-5-1-7-1-3-2-5-3-8l-1-5h2z"></path><path d="M529 283c3 0 8-1 10 0v2h0c1 0 1 1 2 2h-3c1 2 1 3 1 5l-1 1h-2l-3-1c0-2-1-2-2-3 0-1-1-1-1-1 0-2 0-3-1-5z"></path></g><path d="M531 289c0-1 0-2 1-3l1 2h0v-1l1-1c0 1 1 1 1 2h0l1-1h2c1 2 1 3 1 5l-1 1h-2l-3-1c0-2-1-2-2-3z" class="p"></path><path d="M633 503l2-2v-1h1v-1c1-2 6-6 8-7-1 2-3 4-3 6l1-1c1 0 2-2 3-2s3 2 4 2v-2h1l2 1c1 0 2 0 3-1v-1l2 1c4 0 8 1 12 1 1 0 2 2 3 2 1 2 2 2 2 5l-1 1h1v1h1c1 0 2 1 2 1h3 1 1 1l-1 1c0 1 2 3 2 3l3 1v4h1l1-1 1 1c1 0 1 0 2 1 1 0 2 0 3 2l1 3v4c-1-1-2-1-3-1s-1 0-1 2c0 1 0 0-1 1s-3 1-5 2c0 1 1 2 1 4l-1-1h-1-1c0 2-1 2 0 4l1 1h-2 0l-2-2h-2c0-2-2-3-3-5h0l-3-1h0l-1 2 7 8c-2-1-4-3-5-4l-2-2c0-2-1-3-2-5l-3-3c-1 0-1 1-2 1v2s0 1 1 1v1l1 1c-2-1-3-2-5-2-1 1-1 1-2 0-2-1-2-2-4-2l1 1h-3l-1 1h0c0 1 1 2 1 2l-1 2 1 4 1 1c0 1 1 3 2 4 2 2 5 3 6 6l-8-5-7-3-17-6-18-3 3-1c-1-1-2-1-2-2v-1l-2 1c-1-1-2-5-3-7h5 0c1 0 2-1 4-1h1c0-1 0-2 1-3l2-2c2-2 4-3 5-5l1-1h1c1-2 2-3 2-5h1l1-1z" class="B"></path><path d="M686 523h1l1 1v1l-1 1c-1-1-1-2-1-3z" class="G"></path><path d="M683 530v3l1-1c0 2-1 2 0 4l1 1h-2 0l-2-2v-1c0-2 1-2 2-4z" class="L"></path><path d="M672 513l2 3v1l-1 1h-3l-1-2v-1h1c1 0 1-1 2-2z" class="G"></path><path d="M629 527h-1c-1-1-1-1-1-2 1-1 2-1 4-1 1-1 2 1 3 2-2 0-3 0-5 1z" class="D"></path><path d="M634 517c1-1 0-3 1-5h0l1-1h0l3 4h-1c-1 0-1 1-1 2-1 0-2 0-2 1v4c-1-2-1-3-1-5zm39 1l5-3c2 1 2 2 3 4h0c-1 1-1 1-2 1s-1-1-2-1h0v1 1c-2-1-2-2-4-3z" class="F"></path><path d="M683 530l-1-1c-1 0-1 0-2-1v-1h2v-2h1v1l1 1h0v-2h1c1 1 1 2 1 4 0 1 1 2 1 4l-1-1h-1-1l-1 1v-3z" class="P"></path><path d="M631 504h1c0 1 0 2 1 3v4l1 1v2h-1c0 2 0 3-1 4v-6c-1 2-1 3-2 4h-1v-3c0-1-1-2-2-3l1-1h1c1-2 2-3 2-5zm43 12c1-1 1-1 1-2l-2-2 2-2 2 1c1 0-1 2-1 3h1c1 0 1-1 2-1h1l-1-1 1-1c2 0 2 0 3 1l1-2 3 1v4h1v1c-1 1-1 0 0 1l1 1c1 0 1 1 2 2h-2c-1 0-2 0-2-1h-2v-2h-1c-1 1-1 1-1 3l2 1-1 1c-1 0-1 0-2-1v-2h-1 0c-1-2-1-3-3-4l-5 3h0l1-1v-1z" class="H"></path><defs><linearGradient id="a" x1="615.136" y1="529.073" x2="623.481" y2="513.568" xlink:href="#B"><stop offset="0" stop-color="#9d998d"></stop><stop offset="1" stop-color="#c3bdac"></stop></linearGradient></defs><path fill="url(#a)" d="M627 510c1 1 2 2 2 3v3h1v4l-1-1c-1 1-2 3-3 4l-2 2 1 1v1c1 1 1 2 2 3s1 2 1 3c1 0 2 0 3 1l-18-3 3-1c-1-1-2-1-2-2v-1l-2 1c-1-1-2-5-3-7h5 0c1 0 2-1 4-1h1c0-1 0-2 1-3l2-2c2-2 4-3 5-5z"></path><path d="M627 510c1 1 2 2 2 3v3c-1-1-1-1-2-1-2 1-3 1-5 0 2-2 4-3 5-5z" class="G"></path><path d="M616 530c1-2-1-4 1-6 2 0 2-1 3 0v1l-1 1c2 0 4 0 6 1 1 1 1 2 2 3s1 2 1 3c1 0 2 0 3 1l-18-3 3-1z" class="I"></path><path d="M633 503l2-2v-1h1v-1c1-2 6-6 8-7-1 2-3 4-3 6l1-1c1 0 2-2 3-2s3 2 4 2v-2h1l2 1c1 0 2 0 3-1v-1l2 1c4 0 8 1 12 1 1 0 2 2 3 2 1 2 2 2 2 5l-1 1h1v1h1c1 0 2 1 2 1h3 1 1 1l-1 1c0 1 2 3 2 3l-1 2c-1-1-1-1-3-1l-1 1 1 1h-1c-1 0-1 1-2 1h-1c0-1 2-3 1-3l-2-1-2 2 2 2c0 1 0 1-1 2l-2-3c-1 1-1 2-2 2h-1v-1c-1-1-1-1-1-2h0-2c-2-1-2-1-3-4l-1 1 1 1v1c-1-1-2-1-3-2l-1 1c1 0 1 0 1 1h-1c0-1-1-2-1-2l-1-1c-2 0-2 1-4-1v-1c-1 1-1 1-1 2-1 0-1 0-1-1l-1 1c0 1 0 2 1 4h-1l-1-1v1c-1 0-2 1-2 1-1 0-2-1-2-1l-4 2c1 1 1 2 1 3h-1l-2-2-3-4h0l-1 1h0c-1 2 0 4-1 5v-3-2l-1-1v-4c-1-1-1-2-1-3l1-1z" class="C"></path><path d="M660 500c2 1 2 1 3 2v1c-2 0-3 2-5 3l-2-2 3-1c-1 0-1 0-2-1 1-1 2-1 3-2z" class="B"></path><path d="M663 508h1v-2h2v-1h1v-1h1v1 3l-1 1 1 1s1 0 2-1c1 2 1 3 2 4-1 1-1 2-2 2h-1v-1c-1-1-1-1-1-2h0-2c-2-1-2-1-3-4z" class="F"></path><path d="M672 498c1 2 2 2 2 5l-1 1h1v1h1c1 0 2 1 2 1h3 1 1 1l-1 1c0 1 2 3 2 3l-1 2c-1-1-1-1-3-1l-1 1 1 1h-1c-1 0-1 1-2 1h-1c0-1 2-3 1-3l-2-1-2 2 2 2c0 1 0 1-1 2l-2-3c-1-1-1-2-2-4h1 2 1c2-1 3 0 5-2h-1l-2-1h-1c-2-1-2-2-3-3 1-2 1-3 0-5z" class="W"></path><path d="M641 514c0-3-1-6-1-9h0c1 0 1 1 1 2v1h3v-1c0-2 2-3 3-3l1 1c1 1 1 1 2 1h0v-1c0-1 1-1 1-2l1 1 2-1c2 1 2 3 3 5-2 0-2 1-4-1v-1c-1 1-1 1-1 2-1 0-1 0-1-1l-1 1c0 1 0 2 1 4h-1l-1-1v1c-1 0-2 1-2 1-1 0-2-1-2-1l-4 2z" class="F"></path><path d="M638 528v-1s0-1-1-1c-1-2-1-4-1-6h2c1-1 1-1 2-1l1 2h1v-2c1-1 4 1 4-1 1-2 1-3 2-5 1 3-1-1 1 2l1 1c0 1 0 2 1 3l1-1c1 0 2 1 2 2l1 1v1h1c0-2-1-3-2-5-1-1 0-3 0-4l1-1h1v1c0 1 1 1 1 2v1l-1 1v1h3l1-1c-1-1-1-1-1-2 2 1 2 1 3 1l1-1h1c0 1 0 1 1 2 0 1 1 1 2 1l2 2h2v1c1 1 1 1 1 2 1 3 2 4 4 6h0c0-1 1 0 0-1s-1-2-1-2l-1-1v-2c1 0 1 1 2 1h2c1 1 1 2 1 4v2c2 2 1 3 2 4v1h-2c0-2-2-3-3-5h0l-3-1h0l-1 2 7 8c-2-1-4-3-5-4l-2-2c0-2-1-3-2-5l-3-3c-1 0-1 1-2 1v2s0 1 1 1v1l1 1c-2-1-3-2-5-2-1 1-1 1-2 0-2-1-2-2-4-2l1 1h-3l-1 1h0c0 1 1 2 1 2l-1 2 1 4 1 1c0 1 1 3 2 4 2 2 5 3 6 6l-8-5-7-3-17-6c-1-1-2-1-3-1 0-1 0-2-1-3l2-3c2-1 3-1 5-1l4 2z" class="E"></path><path d="M641 531l1-1h-1v-2l-1-1c1 0 1-1 2-1l2 2c1-2-1-4-1-6h1l1-1c1 0 2 0 2 1 1 1 2 2 2 4h-1c1 1 1 2 2 3 0 1 0 1 1 2 1-1 0-2 1-3h2l-1 1h0c0 1 1 2 1 2l-1 2 1 4 1 1c0 1 1 3 2 4 2 2 5 3 6 6l-8-5-7-3-17-6c-1-1-2-1-3-1 0-1 0-2-1-3l2-3c2-1 3-1 5-1l4 2c2 1 2 1 2 3h1z" class="Q"></path><path d="M640 531h1v3h-1c-1 0-2-1-3-2 1-1 1-1 3-1zm-8-3h1v1h1c1 1 1 2 1 4h-1c-2-1-2-2-2-5z" class="I"></path><path d="M648 540l-2-2h1-1c-1-1-2-4-3-6v-2-2l5 5-1 1 3 1c0 1 0 1 1 1l1-1c0 1 1 2 1 2 0 1 1 2 1 3v2s1 0 1 1l-7-3z" class="c"></path><path d="M633 148c-1 0-5-2-6-2v-1l1-1h-1c-2 0-6-1-9-1h-1c2-2 62 0 72-1 9-1 19 0 28 0h20c3 0 7 0 10 1s8 0 12 0c-2 0-3 1-4 1-2 1-4 2-6 2-3 1-5 2-7 2l-9 4-6 3c-3 1-6 4-9 3-2 1-3 1-5 2h3c0 2-1 3-2 4h3l-4 4-1 1-6 8h0c-3 5-5 11-7 17l-2-2 1-2h-1l-1 1c-1 0-2 1-3 2-1-1-1-1-1-2-1 0-2 1-3 0l1-1c1-1 1-1 2-1v-1c-3-1-3-2-4-2h-3 0c0 1 1 2 1 3l-1 1c-1-3-3-4-5-5-1-1-3-1-4-2s-1-1-2-1h-1c-1-1-2-2-4-2l-3-1c2-1 3-1 4-2 1 1 3 2 4 2h2l-2-1-2-2h1 0c2 0 4-1 5-1 2 0 3 1 5 1-1-1-2-1-3-2h0c-5 0-10-2-15-5-2-1-5-2-7-3l-3-1h-1-3l-5-3-1 1c-2-2-4-3-6-3h1 3l-1-1v-2h3-1c3-1 4 0 7 1 1 0 0 0 1 1 1-1 2-1 4-1v1c1-1 1-1 2-1h1 1c1-1 2-1 3-2h0c-1 0-2-1-3 0h-1v-1h-2v-1h2 0c2 1 3 0 4 0h0-1l-1-1h0c-1-1-1-1-2-1-2 0-4-1-6 0l-1 1-1-1c0-1 0-1-1-2 0 0-2-1-3-1-3-1-11-3-14-1z" class="B"></path><path d="M691 153c-3 0-3-2-5-3l-10-4c4-1 9-1 13-1l-3 1 1 1h1c1 0 2 0 3 1 2 1 3 1 5 2v1c-2 1-3 1-4 1l-1 1z" class="C"></path><path d="M716 148c2-1 3-1 5-2v1c2 0 8 0 10-1 1 0 2 0 3 1h1v2c-3 1-4 1-7 0 1 1 1 1 3 1l1 1s1-1 2-1h1 0l4-2h0 3l-9 4h-3v-1c-1 1-2 1-3 1h0c-2-1-3-1-4-2-2-1-4-1-7-2z" class="W"></path><path d="M713 149l2-1h1c3 1 5 1 7 2 1 1 2 1 4 2h0c1 0 2 0 3-1v1h3l-6 3c-3 1-6 4-9 3l1-1-1-1c0-2-2-3-4-4h4c-2-1-3-2-5-3z" class="H"></path><path d="M718 152c1 1 2 1 4 2l-2 2h0l-1 1-1-1c0-2-2-3-4-4h4z" class="V"></path><path d="M715 148h1c3 1 5 1 7 2 1 1 2 1 4 2h-3-1c-2 0-3 0-5-1l-3-3z" class="F"></path><path d="M689 145c8 1 17 0 24 4h0c2 1 3 2 5 3h-4c-4-2-7-3-12-3l-1 2h-2v1h1c1 0 2 0 2 1h-5-2v1c1 0 2 1 3 1l2 2c-4-1-6-3-9-4l1-1c1 0 2 0 4-1v-1c-2-1-3-1-5-2-1-1-2-1-3-1h-1l-1-1 3-1z" class="G"></path><path d="M651 152c4-2 7-2 10 0h1c1 1 3 2 4 1h1 0l-1-1c-2 0-3-1-4-2l-1-1c2-1 2-2 3-2 3 0 6 0 8 1h0c5 1 9 3 14 6-2 0-4 0-5 1h-3v-1c-3 0-6 0-8 2-2 1-4 1-6 2-4 0-7 1-12 1h0c1-1 2-1 4-1v1c1-1 1-1 2-1h1 1c1-1 2-1 3-2h0c-1 0-2-1-3 0h-1v-1h-2v-1h2 0c2 1 3 0 4 0h0-1l-1-1h0c-1-1-1-1-2-1-2 0-4-1-6 0l-1 1-1-1z" class="H"></path><path d="M700 157l-2-2c-1 0-2-1-3-1v-1h2 5c0-1-1-1-2-1h-1v-1h2l1-2c5 0 8 1 12 3 2 1 4 2 4 4l1 1-1 1c-2 1-3 1-5 2h3c0 2-1 3-2 4-2 1-3 2-5 1h-1 3l-2-2 1-2h-1c-1 0-2-1-3-1h-2c0-2-3-3-4-3z" class="D"></path><path d="M686 154l3 1c4 1 7 2 11 3 1 1 3 2 4 2h2c1 0 2 1 3 1h1l-1 2 2 2h-3 1c2 1 3 0 5-1h3l-4 4-1 1-3 1c-2 0-3 0-5 1l-4-2-5-3c-4-1-9-1-13-1-3 0-5 0-8-1l-3 3c-2 0-5 0-6 2h0c-2-1-5-2-7-3l-3-1h-1-3l-5-3-1 1c-2-2-4-3-6-3h1 3l-1-1v-2h3-1c3-1 4 0 7 1 1 0 0 0 1 1h0c5 0 8-1 12-1 2-1 4-1 6-2 2-2 5-2 8-2v1h3c1-1 3-1 5-1z" class="W"></path><path d="M714 164h3l-4 4c-3 1-7 1-10 1 1-3 2-3 5-4h1c2 1 3 0 5-1z" class="G"></path><path d="M642 159c2-1 4-1 7 0 2 1 4 1 6 2 2 0 3 1 5 1l14 2-3 3c-2 0-5 0-6 2h0c-2-1-5-2-7-3l-3-1h-1-3l-5-3-1 1c-2-2-4-3-6-3h1 3l-1-1z" class="H"></path><path d="M660 162l14 2-3 3c-1-1-2-1-4-1h-2-4l-1-2c-1 0-1 1-3 0 1-1 2-2 3-2z" class="F"></path><path d="M686 154l3 1v4c-2 0-2 0-3 1l-2 1c0 1 0 1 1 2h-1l-1-1h-1c-4 2-14-1-19-3l15-4h3c1-1 3-1 5-1z" class="V"></path><path d="M686 154l3 1v4c-2 0-2 0-3 1l-2 1c0 1 0 1 1 2h-1l-1-1h-1v-1l3-2c1-1 1-1 1-2v-1c-2 0-3 0-5-1h0c1-1 3-1 5-1z" class="F"></path><path d="M689 155c4 1 7 2 11 3 1 1 3 2 4 2h2c1 0 2 1 3 1h1l-1 2 2 2h-3c-3 1-4 1-5 4-2-2-6-3-9-4s-6-1-9-2c-1-1-1-1-1-2l2-1c1-1 1-1 3-1v-4z" class="E"></path><path d="M689 155c4 1 7 2 11 3-1 1-2 1-3 1h-3c-1 0-1 1-1 1h-1l-1-2-2 1v-4z" class="V"></path><path d="M674 164c3 1 5 1 8 1 4 0 9 0 13 1l5 3 4 2c2-1 3-1 5-1l3-1-6 8h0c-3 5-5 11-7 17l-2-2 1-2h-1l-1 1c-1 0-2 1-3 2-1-1-1-1-1-2-1 0-2 1-3 0l1-1c1-1 1-1 2-1v-1c-3-1-3-2-4-2h-3 0c0 1 1 2 1 3l-1 1c-1-3-3-4-5-5-1-1-3-1-4-2s-1-1-2-1h-1c-1-1-2-2-4-2l-3-1c2-1 3-1 4-2 1 1 3 2 4 2h2l-2-1-2-2h1 0c2 0 4-1 5-1 2 0 3 1 5 1-1-1-2-1-3-2h0c-5 0-10-2-15-5h0c1-2 4-2 6-2l3-3z" class="E"></path><path d="M700 169l4 2c2-1 3-1 5-1l3-1-6 8h0c-1-1-8-1-10-2-1 0-2 0-2-1l-1-1c2 1 4 1 6 2h0l2-1c1-2 0-4-1-5z" class="B"></path><path d="M700 169l4 2c2 0 3 1 4 1-2 2-5 3-8 3h-1l2-1c1-2 0-4-1-5z" class="D"></path><path d="M674 164c3 1 5 1 8 1l-2 1h-1l-2 2h1c-1 2-3 0-3 2 1 1 2 2 4 2h1v2h0c-5 0-10-2-15-5h0c1-2 4-2 6-2l3-3z" class="V"></path><path d="M695 166l5 3c1 1 2 3 1 5l-2 1h0c-2-1-4-1-6-2h0c-3 0-7-1-9-2-1 0-2-1-4-1h0c-1-1-1-1-1-2l1-1c1 1 1 1 2 1h3 1 1c1-1 6-1 8-2zm-17 9c2 0 3 1 5 1l1 1h1l1 1c4 1 8 3 12 4 2 0 2 1 3 2v1c0 1-2 2-3 3-2 0-2-1-3 1h1 2v1h-1l-1 1c-1 0-2 1-3 2-1-1-1-1-1-2-1 0-2 1-3 0l1-1c1-1 1-1 2-1v-1c-3-1-3-2-4-2h-3 0c0 1 1 2 1 3l-1 1c-1-3-3-4-5-5-1-1-3-1-4-2s-1-1-2-1h-1c-1-1-2-2-4-2l-3-1c2-1 3-1 4-2 1 1 3 2 4 2h2l-2-1-2-2h1 0c2 0 4-1 5-1z" class="J"></path><path d="M557 422h4c2 0 6 0 8 2 1 1 1 3 2 5l-1 1h1 4 1c0-1 1-1 1-3l-1 1v-1l1-1h1l1 1-1 3c1 1 2 0 3 2l-1 2h-1v1c1 0 1 0 2-1v1c-1 1-1 1-2 1v1 1 4 23 13 4 6 16 3 1 4l1 1h0l-1 2v1c-1 1-1 2 0 3v1c0 1-1 3 0 4h2l-1 1c1 1 1 2 3 2l-1 1c0 1 0 1-1 2 0 0-1 0-2 1 0 1 0 3-1 4h-1l1-4h-2l-3-1-4 1h0-5 0-5c1 5 1 9 1 14v6h1v2c-1-2-2-2-4-2l1-3-1-7v-4h-2-1v-6h-3-2-1v2l-1-1c0-2 1-3-1-5-1 1-1 2-1 3v1c-2 1-2 2-2 3v-3c0-1 1-3 2-5h0v-2c1-2 0-6 1-8h0l1-2 1-1 1 1 1-3c1 1 1 2 2 2v-2-1h0v-1c-1 1-1 0-1 1h-1v-3-1l-1-1 2-2h-1l-1-1v-6c2 0 1 0 2-1h-1l-1-1v-3c0-2 0-3 1-5 0 0 1-1 2-1v-1h0-2c-2-1-1-6-1-8v-29-21l4-2c2 0 3 0 4-1h4 0l-4-1h0z" class="i"></path><path d="M579 531c-1-2 0-4 0-5l1-1c1 1 1 2 3 2l-1 1c0 1 0 1-1 2 0 0-1 0-2 1z" class="d"></path><path d="M577 468v7c-1 0-2-1-3-1 0-2 0-4 1-6h2z" class="L"></path><path d="M550 506l-1-1 2-2h-1l-1-1v-6c1 1 2 1 3 1v2 1 1h-1v1h6 6v1h-4l1 1h5c-1-1 0-1-1-1 2-1 3-1 4 0h1c1 0 2 0 2 1h-3l1 1h7l1 1h-1c-1 1-4 0-5 0h-21z" class="m"></path><path d="M571 430h4l2 2v11 9h-1-1-3-1v-3-14-2h0v-2-1z" class="F"></path><path d="M571 449c1 0 1 1 2 1h0v-4l1-1-1-1 1-10v-1l1 1v8c1 0 0 0 1 1h1v9h-1-1-3-1v-3z" class="W"></path><path d="M570 481l1-1c-1-2 0-3 0-4-1-7-1-14 0-21h4 2c1 3 0 9 0 13h-2c-1 2-1 4-1 6 1 0 2 1 3 1v5s-1 0-1 1 1 0 1 2h-1-4v-1l1-1c-1 0-2 1-3 0h0z" class="B"></path><path d="M573 459h1c0 3 0 6 1 9-1 2-1 4-1 6l-1 1v-5-11z" class="N"></path><path d="M575 455h2c1 3 0 9 0 13h-2c-1-3-1-6-1-9h-1c0-1-1-2-1-3h0l2 1c1-1 1-1 1-2z" class="G"></path><path d="M552 510c1-2 2-3 3-3s3 0 4 1v-1h6 11c1 1 1 1 1 3h-1l-1-1-2 2h0c-1 5-3 10-3 15h0c1-4 3-12 6-15l1 1v2 6 6 3 1h-4 0l-4 1h0-5l-1-1c0-2 1-3 1-4 0-3-1-8 1-11l1 1c0-2-2-4-3-5 0 0 0 1-1 1v-3c-2 2-2 6-2 9v2l-1 1c0 1 0 1-1 1l-2-2 1-1h0c-1-1-1-2-1-2v-1h0c0-3 0-5 1-7h0v-1c-2 2-2 3-3 6h0v-3h0l-1 1-1-2h0z" class="Q"></path><path d="M552 510l1 2 1-1h0v3h0c1-3 1-4 3-6v1h0c-1 2-1 4-1 7h0v1s0 1 1 2h0l-1 1 2 2c1 0 1 0 1-1l1-1v-2c0-3 0-7 2-9v3c1 0 1-1 1-1 1 1 3 3 3 5l-1-1c-2 3-1 8-1 11 0 1-1 2-1 4l1 1h0-5c1 5 1 9 1 14v6h1v2c-1-2-2-2-4-2l1-3-1-7v-4h-2-1v-6h-3-2-1v2l-1-1c0-2 1-3-1-5-1 1-1 2-1 3v1c-2 1-2 2-2 3v-3c0-1 1-3 2-5h0v-2c1-2 0-6 1-8h0l1-2 1-1 1 1 1-3c1 1 1 2 2 2v-2-1z" class="m"></path><path d="M546 516h0l1-2 1-1 1 1-1 16c1 1 2 0 3 1h-2-1v2l-1-1c0-2 1-3-1-5-1 1-1 2-1 3v1c-2 1-2 2-2 3v-3c0-1 1-3 2-5h0v-2c1-2 0-6 1-8z" class="u"></path><path d="M563 516l1-3c0 1 1 1 1 2-2 3-1 8-1 11 0 1-1 2-1 4l1 1h0-5c1 5 1 9 1 14v6h1v2c-1-2-2-2-4-2l1-3-1-7v-4h-2-1v-6c2 0 4 0 5-1 1 0 1 1 2 0l-1-1c-2 0-3 0-4-1 0-1 1-2 2-3 0 1 0 1 1 2 0 1 0 0 1 1v-5c0-2 1-3 1-4 0-2 0-4 1-5 1 0 1 1 1 2z" class="I"></path><path d="M563 516l1-3c0 1 1 1 1 2-2 3-1 8-1 11 0 1-1 2-1 4l-1-1c-1-1 0-5 1-6v-7z" class="D"></path><path d="M553 476h1l1 1-1 6h7 1l2-1h2 0 3l2 1v1l1 1c1 2 5 0 5 2v9 4 2h-1l1 1v1h-6c0-1-1-1-2-1h-1c-1-1-2-1-4 0 1 0 0 0 1 1h-5l-1-1h4v-1h-6-6v-1h1v-1-1-2c-1 0-2 0-3-1 2 0 1 0 2-1h-1l-1-1v-3c0-2 0-3 1-5 0 0 1-1 2-1v-1h0v-4c0-1 0-1 1-2-1 0-2 0-2-1l2-1z" class="F"></path><path d="M562 497h2l2 2c1-1 0-1 1-2h2v2 1h-3-1-1c-2-1-2-1-2-3zm14 5c-1 0-2-1-2-2-1 1-1 1-2 1v-3-1-3c1 0 2 1 2 1l3 1v4 2h-1z" class="P"></path><path d="M569 484h2l1 1c1 2 5 0 5 2v9l-3-1c1-2 1-2 1-4v-3h-2v-1h-1c0 2 0 3-1 5h0c0-1 0-4-1-6v1l-1-1v-1-1z" class="f"></path><path d="M571 492h0c-1 3-2 1-4 3h1v1h-3l1-1-1-1h-4c0-2 1-4 0-5h0c1-3 2-1 4-3v-3h4v1 1 1l1 1v-1c1 2 1 5 1 6z" class="P"></path><path d="M553 476h1l1 1-1 6h7l-4 1c0 1 2 1 2 1v1c-1 0-1 1-2 2v3l-1 1-1 2h5v1h-3l1 1h1c0 1 0 2-1 3 0 2 0 2-1 3h-6v-1h1v-1-1-2c-1 0-2 0-3-1 2 0 1 0 2-1h-1l-1-1v-3c0-2 0-3 1-5 0 0 1-1 2-1v-1h0v-4c0-1 0-1 1-2-1 0-2 0-2-1l2-1z" class="n"></path><path d="M557 422h4c2 0 6 0 8 2h-1c1 3 1 5 1 8v23c0 6 1 12 0 17v8s0 1 1 1h0c1 1 2 0 3 0l-1 1v1 2l-1-1v-1l-2-1h-3 0-2l-2 1h-1-7l1-6-1-1h-1l-2 1c0 1 1 1 2 1-1 1-1 1-1 2v4h-2c-2-1-1-6-1-8v-29-21l4-2c2 0 3 0 4-1h4 0l-4-1h0z" class="X"></path><path d="M559 466c1 2 1 3 1 5v1h7 1l-1 2-1-1c0 1-1 1-1 2-2 0-3 0-4 1 2 1 1 3 4 4v1h-2l1 1-2 1c-1 0-2-1-3-1 0-1 1-6 0-7 0 0-1 0-1-1h1l1-1-1-1c-1-2 0-4 0-6z" class="N"></path><path d="M568 472h1v8s0 1 1 1h0c1 1 2 0 3 0l-1 1v1 2l-1-1v-1l-2-1h-3 0-2l-1-1h2v-1c-3-1-2-3-4-4 1-1 2-1 4-1 0-1 1-1 1-2l1 1 1-2z" class="C"></path><path d="M559 465c-1-2-1-3-1-4 1-1 1-1 1-2l-1-1v-1c1-2 0-4 1-6h0v-2l-1-1 1-2h0v-2c-1-1-1-1 0-2v-1h-3l-1-1h1l-1-1v-2-1c-1-2-1-2 0-4h1v1h4c0 1 1 1 1 2-1 3 0 4-2 6l2 1v4c0 1 0 1-1 2v5 2 3 5c0 1-1 1-1 2z" class="K"></path><path d="M552 484h-2c-2-1-1-6-1-8v-29-21l4-2c0 4 1 9 0 13-1 1 0 2 0 4v1c-1 2-1 3-1 4l2 1-3 1 1 1c1 1 1 3 1 5v1c-1 1 0 1 0 2-1 1 0 2 0 3v1c0 1-1 1-2 2h1c1 2 1 3 0 4h0v2l1 1c-1 1-1 1-1 2h1c-1 1-1 1-2 1v1h4-1c-1 1-2 1-4 2h3l-2 1c0 1 1 1 2 1-1 1-1 1-1 2v4z" class="m"></path><path d="M560 433l1-1h-2v-1h2v-6c0-1 0-1 1-2 2 0 4 1 6 1 1 3 1 5 1 8v23c0 6 1 12 0 17h-1-1-7v-1c0-2 0-3-1-5v-1c0-1 1-1 1-2v-5-3-2-5c1-1 1-1 1-2v-4l-2-1c2-2 1-3 2-6 0-1-1-1-1-2z" class="P"></path><path d="M747 143l35-1h14 4l3 1-1 1c2 1 4 1 5 2l11 5c5 3 8 5 12 8v1l3 2c1 0 3 3 3 3v-1l2 2-1 1c1 1 2 2 3 2v1l3 3v2 1h0c4 5 5 11 9 16v2h1c0-1 0-2 1-3v-3 13c0 4 1 10 0 14-1 7 0 15-1 22v1c0 3-1 5-2 7 0 1 0 2-1 2-2 1-2 2-4 3h0v-1l3-3v-1h-1c-1 0-2-1-3-1s-1 0-2 1h-1v-2c0-1 1-2 1-3l-2-2-1 1c-3 1-3 2-5 4l-5 4-1 2h0l-2 2h-4-1c-1 1-2 1-4 1v1h0c1 1 1 0 1 1l2 2c-1 2-2 2-1 4h1l1 1c-1 0-2 1-3 2 0 1-1 1-2 2-2-1-3-1-4-2-1 0-2 0-3 1v1h-1-2c-1 0-2-1-3-1h-4v-1h-3v-1l-1 1-1 1-1-1c-1 0-1 1-3 1 1 1 0 1 1 1 1 1 2 1 3 2h-2c1 1 1 1 1 2s0 1 1 1h-1-1c-1-1-3-1-5-1-1 0-2-1-3-1l-1 1c-2 0-3 0-4-1v-1c-2 0-3-1-5-3h0c-2 0-4-1-6-1h-1c-2-1-5-4-6-5s-1-1-2-1c-2-1-5-4-7-6l-8-7-10-6-20-11-2-1-11-4c-1 0-3-1-5-2h2c1 0 1 0 2-1 0-1-1-1 0-2 0-3-1-7 1-9v-2l-1 1h-1v2 3-1h-1l-1 1v-5-1c-1-2 0-4 1-6v-6h0c2-6 4-12 7-17h0l6-8 1-1 4-4h-3c1-1 2-2 2-4h-3c2-1 3-1 5-2 3 1 6-2 9-3l6-3 9-4c2 0 4-1 7-2 2 0 4-1 6-2 1 0 2-1 4-1-4 0-9 1-12 0z" class="w"></path><path d="M730 179h2c0 1 0 2 1 3-2 1-2 1-4 1h-1l-1-1 3-3z" class="W"></path><path d="M763 181c2-1 2 0 4 0 1 0 1 0 2 1h-1c0 2-1 3-3 3-2-1-2-2-3-4h1z" class="C"></path><path d="M763 181c2-1 2 0 4 0v2h0-2l-1-2h-1zm-45-23c3 1 6-2 9-3-4 3-7 6-10 9h-3c1-1 2-2 2-4h-3c2-1 3-1 5-2z" class="B"></path><path d="M792 225c-3 0-4 0-6-2s-5-5-5-8h1c0 1 1 3 2 4h1c0 1 3 3 3 4 2 0 4-1 4 1 1-1 1-2 2-2 0-2 0-2 1-3 0 2-1 4-1 5-1 0-1 1-2 1zm-46-38l2-1c0-1-1-2-1-3v-1l2 2h0l1 3h1v-4c1-1 2-1 3-1h1l-3 3c1 1 1 3 2 3v3l-1 1h-1v-1l-3-3c-1 0-2 0-3-1z" class="W"></path><path d="M836 236c0-1 1-2 1-3l-1-1c-1 0-2 1-3 1h0l-1-1c1-1 2-1 3-1-1-1-1-1-1-2l2-3c1 0 2 1 3 1v3c0 1 0-1 0 1 0 1-1 3-1 5-1 2-2 3-4 5l2-5zm-54-30c1 1 2 1 2 2l1 1 1 1h0v1c1 0 1 0 2-1l1 1c1 0 1 0 2 2v1l-2 2h1c2 0 4-1 6 0h-2c-2 1-4 2-6 0-1 0-1-2-2-2s-2 0-3 1c-1-2 1-3 1-5 0-1-2-3-2-4z" class="C"></path><path d="M822 220h1c2 0 5 2 6 4v1c0 2 0 3-1 4l-1 1c-2-1-3 0-5 0v-6c1-1 1-1 1-2l-1-1v-1z" class="B"></path><path d="M827 234c2-2 2-2 2-4 1-1 1-1 2-1-1-1-1-5-1-7l1-1h1 0l-2-2v-1c2 1 4 2 5 4s-1 3-1 5h0c-1 1-1 1-1 2s-1 2-1 2l-2 1h0 1l1 1h1c-1 1-1 2-1 3h-1l-3 3c-2-2-1-1 0-3 0 0-1-1-1-2z" class="C"></path><path d="M792 225h-1c-2 1-3 0-5-1-1 0-2-1-3-2l-1-2c-1-2-3-3-4-5v-2c-1-1-2-1-3-2v-1h2l1 1v-2-1h1v-1h-1v-1l1-1v-1l1 1v1h2c0 1 2 3 2 4 0 2-2 3-1 5v1c1 1 1 2 2 3h-1c-1-1-2-3-2-4h-1c0 3 3 6 5 8s3 2 6 2z" class="N"></path><path d="M773 192c0-3 1-4 3-6 1-1 1-3 2-4h1c3 1 4 3 6 5v1h-1c-2 1-3 2-4 4-2 0-3 1-5 1v1c-2-1-2-1-2-2z" class="W"></path><path d="M778 196c1-2 2-3 3-4 2 1 2 3 5 3l1-1 1 1-3 2c0 1 1 1 1 2s-1 4-1 5v1 3 1c0-2 0-3-1-4s-2-1-3-2l-1-1c-1 0-1 0-2-1 0-1 0-1-1-2h-1l1-1 1 1c1-1 0-2 0-2v-1z" class="B"></path><path d="M763 199h-2v-1c-1-2-2-2-4-3l-1 2-1-1v-2l3-1 1 1c1 0 1 0 2 1l1 1c2 0 3 0 5-1 1-1 0-1 1-1v2 1l-1-1-1 1h0l2 2v1c2-1 2-2 4-2 1 1 1 1 1 2-1 1-1 3 1 4 0 0 1 1 2 1l-1 1c-1-1-2-2-4-3-1 1 0 1 0 2l1 1v-1c1 1 1 0 2 2l1-1 1 2-1 1h-2l-3-1c-1-1-1-2-1-3h0v-1c-1 1-2 1-2 2h-1v-1h-1l-1-1c0-2 1-2 1-4-1 0-1-1-2-1z" class="W"></path><path d="M834 241h1c1-1 2-2 3-2 1-2 2-2 3-4 1-1 1-2 2-3 1 0 2 2 3 3v-2l1-1c1 0 1-1 2-2v-1l1 1-1 1 1 4v3 3h2v-1c0-1 0-1 1-2 0 3-1 5-2 7 0 1 0 2-1 2-2 1-2 2-4 3h0v-1l3-3v-1h-1c-1 0-2-1-3-1s-1 0-2 1h-1v-2c0-1 1-2 1-3l-2-2-1 1c-3 1-3 2-5 4l-1-2z" class="H"></path><path d="M850 238h0c-1 1-1 2-2 3v-1l-3 3h-1v-2c1-1 1-1 3-2l1 1c1-2 1-2 1-5h1v3z" class="L"></path><path d="M754 188c1 0 1-1 3-1v1h-1l-1 1c2 1 3 1 5 1h0v1c2-1 3-3 5-2 1 1 1 1 1 2h1v-1-1h1l5 3c0 1 0 1 2 2 1 1 2 1 3 2v1c-1-1-1-1-2-1-1-1-1-1-2-1h-1-1c-1-1-2-1-2-2-1 1-1 3-1 4v1c-1 0-2 0-3-1l1-1 1 1v-1-2c-1 0 0 0-1 1-2 1-3 1-5 1l-1-1c-1-1-1-1-2-1l-1-1-3 1v2l1 1 1-2c2 1 3 1 4 3v1h2v2h-1-2v-2c-1-1-1-2-2-3h0l-2 2h-1-1v-1h-1c-2 1-3 2-5 2-1-2-2-3-2-5l-1-3c0-1 1-3 1-4 1 1 2 1 3 1l3 3v1h1l1-1v-3z" class="H"></path><path d="M792 145c2 0 3 0 4 1h1 1 1l2 1 2 1c2 0 4 2 6 3 4 2 8 3 12 6 5 3 10 7 15 11l7 7v1h0c4 5 5 11 9 16v2h1c0-1 0-2 1-3v-3 13c0 4 1 10 0 14 0-3 0-7-1-10-1-6-3-12-6-18-12-21-32-35-55-42z" class="J"></path><path d="M706 188c1-1 2-1 3-2h1c1 1 1 1 1 2 1 1 3 1 5 1 1 1 1 1 1 2 1 0 1 1 1 1h1c0-2 0-2-1-2l1-1h1l1 1v1l1-1h1 1 0 1c1-1 1 0 2 0v1c-2 0-2 0-3 1s-1 1-2 1v3h1c2-1 0 0 1 0 1-1 2-1 3-2h1 2c-1-1-2-1-2-2v-2c2 0 4-1 5 0 1 0 3 1 4 2v1-1c-2 0-3-1-4-1v1l-1 2c1 1 2 1 3 2v2c1 1 2 3 4 4l-1 1-2-1h-1c-1-3-2-4-5-6l-2 2c-1 0-1 0-2-1l-1 1v1h-1l-3-2-1 2c-2-1-2-1-4 0s-3 3-3 5h-1c-2-3 0-5-1-8 0-2 0-1 1-2l-1-1-3 3c0 1 0 1-1 2 0-2 0-3 1-5h0c0-2-1-3-2-5z" class="C"></path><path d="M725 198c0-2 1-2 2-3 1 0 2 0 3 1l-2 2c-1 0-1 0-2-1l-1 1z" class="F"></path><path d="M747 143l35-1h14 4l3 1-1 1c2 1 4 1 5 2l11 5c5 3 8 5 12 8v1l3 2c1 0 3 3 3 3v-1l2 2-1 1c1 1 2 2 3 2v1l3 3v2l-7-7c-5-4-10-8-15-11-4-3-8-4-12-6-2-1-4-3-6-3l-2-1-2-1h-1-1-1c-1-1-2-1-4-1-4 0-9-1-13-2-6-1-14 0-20 0-4 0-9 1-12 0z" class="a"></path><path d="M742 194c2 0 2 1 3 2v-2h-1c1-1 1-2 1-3h0l1 3c0 2 1 3 2 5 2 0 3-1 5-2h1v1c0 1 1 3 2 4h1v1h-1l-1-1h0l-1 2c-2 0-1-2-3-1l-1 3h3v1 1l2 3v1c-3-2-4-5-7-6-2 0-3 1-4 2 0 2 0 3 2 4 1 1 1 2 2 3v1l-1 1-2-2h0c0-1-1-1-1-2h-2-1-2l-2-2v-1l2-1v-1s0-1 1-2l-2-2h1l-1-1 1-1c-2-1-3-3-4-4v-2c-1-1-2-1-3-2l1-2v-1c1 0 2 1 4 1v1h1v2l1 1c1-1 1-1 1-2 1-1 1 0 2 0z" class="W"></path><path d="M739 209h2c1 1 1 1 1 2-1 1 0 1-1 1v1h-2l-2-2v-1l2-1z" class="C"></path><path d="M750 206c-2-1-2-1-3-3v-1c2-1 3-2 6-2l2 2-1 2c-2 0-1-2-3-1l-1 3zm-12-3l1-1c-2-1-3-3-4-4v-2c-1-1-2-1-3-2l1-2v-1c1 0 2 1 4 1v1h1v2l1 1c1-1 1-1 1-2 1-1 1 0 2 0 2 2 3 3 3 5 0 1 0 0 1 1l-2 2c-1 1-1 3-2 4h-1l-1-2c0 1 0 2 1 3-1 0-1 1-2 1 0 0 0-1 1-2l-2-2h1l-1-1z" class="H"></path><path d="M754 198h1l1 3 1-1 1 1s0-1 1-1v2l-1 1v1l1 2h0 1c1 1 1 0 2 2l-2-1v1h2l-3 1c0 2 1 3 3 5 0 0 1 1 2 1h0-1-1c0 1 0 1 1 2l-1 2c0 2 0 2 2 3l1 1c0 1 0 2-2 4l3 3s0 1-1 1c-1-2-4-3-6-5h-1v-1c-1-1-3-2-3-4v-1l-2-1h-1v-1c-2 0-3-1-4-2v-1c-1-1-1-2-2-3-2-1-2-2-2-4 1-1 2-2 4-2 3 1 4 4 7 6v-1l-2-3v-1-1h-3l1-3c2-1 1 1 3 1l1-2h0l1 1h1v-1h-1c-1-1-2-3-2-4z" class="F"></path><path d="M757 216c2 0 2 0 3-1v-1c-1-1-1-2-1-2h-1c-2 0-1 0-2-1l2-2-1-1h-1l-1-1 1-1h0 2c1 1 1 1 1 3s1 3 3 5c0 0 1 1 2 1h0-1-1c0 1 0 1 1 2l-1 2c0 2 0 2 2 3l1 1c0 1 0 2-2 4l3 3s0 1-1 1c-1-2-4-3-6-5h-1v-1c-1-1-3-2-3-4v-1l-2-1v-2c-1-1-2-1-3-2l-1-1h2l1-1 1 1c2 1 2 2 3 2h1z" class="C"></path><path d="M753 219v-2c-1-1-2-1-3-2l-1-1h2l1-1 1 1c2 1 2 2 3 2h1l1 1c0 1 0 1 1 1 1 1 0 1 0 1v1l-1-1-1 1 1 1-1 1c1 1 3 2 3 3h-1v1h-1v-1c-1-1-3-2-3-4v-1l-2-1z" class="H"></path><path d="M706 188c1 2 2 3 2 5h0c-1 2-1 3-1 5 1-1 1-1 1-2l3-3 1 1c-1 1-1 0-1 2 1 3-1 5 1 8h1l-1 1h-1v-1c0-1-1-2-2-3v-1c-2 1-2 1-2 2s1 2 2 4h0v1 1l-1 1 1 1c0 1 0 1 1 2 0 0 0 1 1 1v-1c1 2 2 4 3 5 0 1 0 2 1 3l1 2c0 2 0 4-1 5l-2-1-11-4c-1 0-3-1-5-2h2c1 0 1 0 2-1 0-1-1-1 0-2 0-3-1-7 1-9v-2l-1 1h-1 0v-2c1-1 1-2 1-4 0-1 1-1 0-3h0l5-10z" class="F"></path><path d="M704 209c0-1-1-3-1-4 0-2 1-3 1-5 1 2 3 4 4 6l-4 3z" class="E"></path><path d="M708 206l1 1v1l-1 1 1 1c0 1 0 1 1 2 0 0 0 1 1 1v1l-1 1-1 1h-1v1c-2-3-3-5-4-8l4-3z" class="J"></path><path d="M702 208c1 4 3 9 6 12 0 1 1 2 1 3s0 0 1 1c1-2-2-6-2-7v-1h1l1-1 1-1v-1-1c1 2 2 4 3 5 0 1 0 2 1 3l1 2c0 2 0 4-1 5l-2-1-11-4c-1 0-3-1-5-2h2c1 0 1 0 2-1 0-1-1-1 0-2 0-3-1-7 1-9z" class="c"></path><path d="M713 226c-1-2-1-3-1-5l3-1 1 2c0 2 0 4-1 5l-2-1z" class="K"></path><path d="M702 208c1 4 3 9 6 12 0 1 1 2 1 3-1-1-1-1-1-2-2-2-4-3-5-5 0 2 2 4 2 6-2-1-2-1-3 0-1 0-3-1-5-2h2c1 0 1 0 2-1 0-1-1-1 0-2 0-3-1-7 1-9z" class="E"></path><path d="M762 208c1 0 2 1 2 1 2-1 1-2 3-1-1 1 0 1 0 3h2v3c1 1 1 2 2 2h1v-1l1-1 1 1h1c1-1 0-1 2-1v1h0-1c0 2 1 2 2 3 1 0 2 1 3 2l3 3v1h-1l1 4v1h-2c0 1 2 3 1 4-1-1-1-1-1-2h-1c0 2 0 2-1 3h-3-1c-1 0-3-2-4-3-2 0-3-2-4-3v-1c0-1 0-1-1-1l-1 4-3-3c2-2 2-3 2-4l-1-1c-2-1-2-1-2-3l1-2c-1-1-1-1-1-2h1 1 0c-1 0-2-1-2-1-2-2-3-3-3-5l3-1z" class="L"></path><path d="M767 213h1c1 3 3 3 5 4l-1 1-2 1h0l-1 1c0-1-1-2-1-3l-2-2h0l1-2z" class="F"></path><path d="M765 223l3-1c1 2 1 3 2 5 0 2 1 3 2 4-2 0-3-2-4-3v-1c0-1 0-1-1-1l-1 4-3-3c2-2 2-3 2-4z" class="w"></path><path d="M730 196c3 2 4 3 5 6h1l2 1 1 1h-1l2 2c-1 1-1 2-1 2v1l-2 1v1l2 2h0c-1 1-2 1-3 2-1 0-1 0-2-1v1c-1-2-2-3-3-4h-2c-1-1 0-2-1-3l-1-2h0l-2 2v3h0c1 0 1 0 2-1-1 1 0 1-1 1l-2 2v1h0c0 1-1 1 0 2v2c3 1 4 0 6 2h-1c-1-1-1-1-3-1-1 1 0 1-1 2 2 3 3 7 6 11 1 2 2 3 4 4h1l1 1c-1 1-1 1-2 1l-20-11c1-1 1-3 1-5l-1-2c-1-1-1-2-1-3-1-1-2-3-3-5v1c-1 0-1-1-1-1-1-1-1-1-1-2l-1-1 1-1v-1-1h0c-1-2-2-3-2-4s0-1 2-2v1c1 1 2 2 2 3v1h1l1-1c0-2 1-4 3-5s2-1 4 0l1-2 3 2h1v-1l1-1c1 1 1 1 2 1l2-2z" class="V"></path><path d="M719 211l2-1 1 1c-1 1-1 2 0 3-1 0-1 0-2 1l-1-1v-3z" class="E"></path><path d="M737 210c0-1 0-2-1-3 0-1 0-1 1-2 0 1 0 1 1 1v-2l2 2c-1 1-1 2-1 2v1l-2 1z" class="G"></path><path d="M714 217h0l1-1c-1-1-2-2-2-4 0-1-1-1-1-3h0l1-1c1 1 1 1 2 1 2 1 2 1 2 3l1-1h1v3l1 1 2 2c0 1 0 1 1 3h-1-1c0 1 0 0 1 1l1 1h0 1v1l1 2v2l-2 2c-1-2-1-2-1-4h1v1l1-1-1-1h-1c-1 1 0 0-1 0-1-3-2-4-5-6v3l1 1h0-1l-1-2c-1-1-1-2-1-3z" class="Q"></path><path d="M716 222h1 0l-1-1v-3c3 2 4 3 5 6 1 0 0 1 1 0h1l1 1-1 1v-1h-1c0 2 0 2 1 4l2-2v-2l-1-2v-1c2 2 3 6 5 9l1 1c1 1 2 4 4 4h1 1l1 1c-1 1-1 1-2 1l-20-11c1-1 1-3 1-5z" class="c"></path><path d="M800 228l1-1c0-1 1-2 1-3h0l1 1h0l1 1v-1l1 1-1 1c0 1 0 3 1 4l-1 1v2l1 1c0-1 1-1 1-1 0-2-1-2 1-3h3l1-1h0c-2 0-3 1-4 0l2-2h1 6l5-5h0c1 1 1 1 0 2l1 1v3l-2 2c0 1-1 1-1 1v1c1 1 2 2 3 2h2c1 0 2 0 3-1 0 1 1 2 1 2-1 2-2 1 0 3l3-3h1c0-1 0-2 1-3l2 1 1 2-2 5h0l1 2-5 4-1 2h0l-2 2h-4-1c-1 1-2 1-4 1v1h-1c-3-1-5-1-7-2l-2 2c-1-1-1-1-2-1v1c-1 0-2-2-2-2h-5c0-1 0-2-1-3h-4v1l-2-2-1-2c-1 0-3 0-4-1h-2-3v2h-2v-1-1l-1-1-3-1c0-2-2-3-2-5-2-1-4-3-5-4l-2-2-1 1-1-1c1 0 1-1 1-1l1-4c1 0 1 0 1 1v1c1 1 2 3 4 3 1 1 3 3 4 3h1 3c1-1 1-1 1-3h1c0 1 0 1 1 2 1-1-1-3-1-4h2s1 1 2 1c0 0 0 1 1 1h0 3c0-1 1-2 2-2h1c1 0 2-1 3-2 0 1 0 1 1 1 1-1 2 0 3 0z" class="B"></path><path d="M820 237h-1c-1-1-1-2-2-2v-1h0l2-1c1 1 2 2 3 2h2c-1 1-2 2-4 2z" class="C"></path><path d="M810 251l1-1 1-1 1 1 2-1h0l1-2v-2h0l1-1h0c1 1 1 2 2 3l-1 1h-1l-1 1h4c0 1-1 2-2 3v1h-1c-3-1-5-1-7-2h0zm14-16c1 0 2 0 3-1 0 1 1 2 1 2-1 2-2 1 0 3l-3 2v1h-6l2-1v-1h-2v-1l1-2c2 0 3-1 4-2zm-23 6c0-1 1-2 3-2 1 0 1-1 2-1h1 1c1-1 3-1 4-1l1 2c0 1 0 2-2 3h-3v2c1 1 1 2 1 3h-3 0c1-1 1-1 2-1-1-1-3-1-5-1 1-1 2-2 2-3l-1-1h-3zm-17-2h3l1 1 2 2v1h1v-4l2-1 4 1v1l-1 1h-2c1 1 0 1 1 1s2 1 3 1c0 1-2 2-3 2h-1c-1 0-1 1-2 2h0l-1-2c-1 0-3 0-4-1h-2v-1l1 1 1-1v-1h-3v-3z" class="H"></path><path d="M801 241h3l1 1c0 1-1 2-2 3l-2 1c0 1 0 2 1 2v1l1-1 2 2c1 1 3 1 5 1h0l-2 2c-1-1-1-1-2-1v1c-1 0-2-2-2-2h-5c0-1 0-2-1-3h-4v1l-2-2h0c1-1 1-2 2-2h1c1 0 3-1 3-2 1-1 2-1 3-2zm32-8l2 1 1 2-2 5h0l1 2-5 4-1 2h0l-2 2h-4-1c-1 1-2 1-4 1 1-1 2-2 2-3h-4l1-1h1l1-1c-1-1-1-2-2-3h0l2-2h6v-1l3-2 3-3h1c0-1 0-2 1-3z" class="F"></path><path d="M818 248c2 0 2 0 3-1s1-1 1-2c1-1 1-1 2-1l1-1c1 0 1 0 2 1 0 1 0 2-1 2l-1 1h2v-1h1v-3h1v-3c2 0 2 1 4 0v1c-1 1-2 2-2 3l-3 3v1l1 1h0l-2 2h-4-1c-1 1-2 1-4 1 1-1 2-2 2-3h-4l1-1h1z" class="B"></path><path d="M766 230l1-4c1 0 1 0 1 1v1c1 1 2 3 4 3 1 1 3 3 4 3h1 3c1-1 1-1 1-3h1c0 1 0 1 1 2 1-1-1-3-1-4h2s1 1 2 1c0 0 0 1 1 1h0 3c0-1 1-2 2-2h1c1 0 2-1 3-2 0 1 0 1 1 1 1-1 2 0 3 0h0c-2 0-4 0-5 1-1 2-1 3-1 4-1 0-2-1-2-2-1 0-1 0-2 1 0 0 0 1 1 1v2c-1 1-1 1-2 1l-1-1-1 1-1 1c-1 0-1 1-2 1v1l-2-1 2 1v3h3v1l-1 1-1-1v1h-3v2h-2v-1-1l-1-1-3-1c0-2-2-3-2-5-2-1-4-3-5-4l-2-2-1 1-1-1c1 0 1-1 1-1z" class="H"></path><path d="M769 233h2v-1l1 1 2 2v2h3 0l2 1h1c0-2 0-2 1-3 1 0 1 0 2 1 0 1 1 2 1 3l-2-1 2 1v3h3v1l-1 1-1-1v1h-3v2h-2v-1-1l-1-1-3-1c0-2-2-3-2-5-2-1-4-3-5-4z" class="F"></path><path d="M779 243l-1-2c2 0 3 1 4 2v1 2h-2v-1-1l-1-1z" class="H"></path><defs><linearGradient id="b" x1="737.914" y1="237.303" x2="748.136" y2="224.854" xlink:href="#B"><stop offset="0" stop-color="#908e81"></stop><stop offset="1" stop-color="#b4af9f"></stop></linearGradient></defs><path fill="url(#b)" d="M735 236c-2-1-3-2-4-4-3-4-4-8-6-11 1-1 0-1 1-2 2 0 2 0 3 1h1c-2-2-3-1-6-2v-2c-1-1 0-1 0-2h0v-1l2-2c1 0 0 0 1-1-1 1-1 1-2 1h0v-3l2-2h0l1 2c1 1 0 2 1 3h2c1 1 2 2 3 4v-1c1 1 1 1 2 1 1-1 2-1 3-2h0 2 1 2c0 1 1 1 1 2h0l2 2 1-1c1 1 2 2 4 2v1h1l2 1v1c0 2 2 3 3 4v1h1c2 2 5 3 6 5l1 1 1-1 2 2c1 1 3 3 5 4 0 2 2 3 2 5l3 1 1 1v1 1h2v-2h3 2c1 1 3 1 4 1l1 2 2 2v-1h4c1 1 1 2 1 3h5s1 2 2 2v-1c1 0 1 0 2 1l2-2c2 1 4 1 7 2h1 0c1 1 1 0 1 1l2 2c-1 2-2 2-1 4h1l1 1c-1 0-2 1-3 2 0 1-1 1-2 2-2-1-3-1-4-2-1 0-2 0-3 1v1h-1-2c-1 0-2-1-3-1h-4v-1h-3v-1l-1 1-1 1-1-1c-1 0-1 1-3 1 1 1 0 1 1 1 1 1 2 1 3 2h-2c1 1 1 1 1 2s0 1 1 1h-1-1c-1-1-3-1-5-1-1 0-2-1-3-1l-1 1c-2 0-3 0-4-1v-1c-2 0-3-1-5-3h0c-2 0-4-1-6-1h-1c-2-1-5-4-6-5s-1-1-2-1c-2-1-5-4-7-6l-8-7-10-6c1 0 1 0 2-1l-1-1h-1z"></path><path d="M745 244h0l-3-3-1-1h1l-1-1c0-1 0-1-1-2l1-1c1 1 1 2 2 3l1 2c1 1 2 2 4 3l2 2c1 0 1-1 2 1v1h0l4 3c1 1 2 1 4 2h1c-1-1-2-1-2-2l-2-1v-1h1c0 1 0 1 1 1l3 3v-1c-1-2-4-3-4-5l6 6h-1c0 2 0 3 1 4h1l1 1c-2 0-3-1-4 0-1-1-1-1-2-1-2-1-5-4-7-6l-8-7z" class="J"></path><path d="M753 251v-1c1 1 5 5 6 5s3-1 4-2c0 2 0 3 1 4h1l1 1c-2 0-3-1-4 0-1-1-1-1-2-1-2-1-5-4-7-6z" class="I"></path><path d="M748 233l1-1 5 5 1 1c0-1 0-2 1-2s2 0 3 1h1l7 7v1l2 2c1 1 2 2 3 2h-1l-1 1h-3l-1 1c1 1 1 1 1 2h-1c0 2 1 5 2 6 2 2 4 2 6 2l1 3c-2 0-4-1-6-1h-1c-2-1-5-4-6-5 1-1 2 0 4 0l-1-1h-1c-1-1-1-2-1-4h1l-6-6c-1-1-3-4-4-4-1-1-2-2-3-4 0 0 1-1 1-2-1-2-3-3-4-4z" class="B"></path><path d="M752 237l5 7c-1 0-1-1-2-2l-1 1c-1-1-2-2-3-4 0 0 1-1 1-2zm3 1c0-1 0-2 1-2s2 0 3 1h1l7 7-2 1-2 1c-2 0-7-6-8-8z" class="J"></path><path d="M742 213h2c0 1 1 1 1 2h0l2 2 1-1c1 1 2 2 4 2v1 2l-1 1 1 1v1c0 3 4 6 5 10l3 3h-1c-1-1-2-1-3-1s-1 1-1 2l-1-1-5-5-1 1c-1-1-1-2-2-2l-4-4c-1-1-2-2-3-2-2-1-3-1-5-2 0 0-1-1-1-2v-2l-2-2v-2h2 1v-1c1 1 1 1 2 1 1-1 2-1 3-2h0 2 1z" class="L"></path><path d="M748 216c1 1 2 2 4 2v1 2l-1 1-4-5 1-1z" class="H"></path><path d="M742 213h2c0 1 1 1 1 2h0l-1 2c0 1 0 2 1 2 1 1 1 2 2 3l4 5c1 0 1 1 2 2l2 2v1c-2-1-4-3-6-5-1-3-3-4-5-6-1-1-1-2-1-3-1-1-2-2-2-3h-1l2-2z" class="P"></path><path d="M741 213h1l-2 2h1c0 1 1 2 2 3 0 1 0 2 1 3 2 2 4 3 5 6 0 0-1-1-2-1l-2-3-2 1h0c-1-1-2-2-2-3v-1c-1 0-1 1-2 1-1-1-1-1-1-3l-2-2h-3v-1h1v-1c1 1 1 1 2 1 1-1 2-1 3-2h0 2z" class="F"></path><path d="M743 224l2-1 2 3c1 0 2 1 2 1 2 2 4 4 6 5l2 2 3 3h-1c-1-1-2-1-3-1s-1 1-1 2l-1-1-5-5-1 1c-1-1-1-2-2-2l-4-4c1 0 2 1 3 1l1 1v-1-2h0l-1-1h-1l-1-1z" class="D"></path><path d="M742 227c-1-1-2-2-3-2-2-1-3-1-5-2 0 0-1-1-1-2v-2l-2-2v-2h2v1h3l2 2c0 2 0 2 1 3 1 0 1-1 2-1v1c0 1 1 2 2 3h0l1 1h1l1 1h0v2 1l-1-1c-1 0-2-1-3-1z" class="E"></path><path d="M771 249c1 2 2 2 4 2h0 5l1 1 8 5c1 1 2 1 3 1-1 1-2 2-2 3 2 2 4 1 6 2l-1 1-1-1c-1 0-1 1-3 1 1 1 0 1 1 1 1 1 2 1 3 2h-2c1 1 1 1 1 2s0 1 1 1h-1-1c-1-1-3-1-5-1-1 0-2-1-3-1l-1 1c-2 0-3 0-4-1v-1c-2 0-3-1-5-3h0l-1-3c-2 0-4 0-6-2-1-1-2-4-2-6h1c0-1 0-1-1-2l1-1h3l1-1z" class="I"></path><path d="M768 259h4l1-1 1 1h0v2c-2 0-4 0-6-2z" class="a"></path><path d="M781 252l8 5c1 1 2 1 3 1-1 1-2 2-2 3h-2v-1-1c-1-1-3-3-6-4l-1-1v-2z" class="J"></path><path d="M775 264l2-1h2v-1h-1v-1h2c1 2 2 2 4 3l1-1c2 1 4 3 7 3 0 1 1 1 1 1 1 1 1 1 1 2s0 1 1 1h-1-1c-1-1-3-1-5-1-1 0-2-1-3-1l-1 1c-2 0-3 0-4-1v-1c-2 0-3-1-5-3z" class="K"></path><path d="M752 221v-2h1l2 1v1c0 2 2 3 3 4v1h1c2 2 5 3 6 5l1 1 1-1 2 2c1 1 3 3 5 4 0 2 2 3 2 5l3 1 1 1v1 1h2v-2h3 2c1 1 3 1 4 1l1 2 2 2v-1h4c1 1 1 2 1 3h5s1 2 2 2v-1c1 0 1 0 2 1l2-2c2 1 4 1 7 2h1 0c1 1 1 0 1 1l2 2c-1 2-2 2-1 4h1l1 1c-1 0-2 1-3 2 0 1-1 1-2 2-2-1-3-1-4-2-1 0-2 0-3 1v1h-1-2c-1 0-2-1-3-1h-4v-1h-3v-1l-1 1c-2-1-4 0-6-2 0-1 1-2 2-3-1 0-2 0-3-1l-8-5-1-1h-5 0c-2 0-3 0-4-2h1c-1 0-2-1-3-2l-2-2v-1l-7-7-3-3c-1-4-5-7-5-10v-1l-1-1 1-1z" class="D"></path><path d="M810 259c0-1-1-2-1-2l1-1h3l1-1h1c0 2 0 2-2 4h-3z" class="J"></path><path d="M798 259v-1h2 5l1-1 1 1c0 1 1 1 2 2l-2 1c0-1 0-1-1-1-3 0-5-1-8-1z" class="F"></path><path d="M817 253h1 0c1 1 1 0 1 1l2 2c-1 2-2 2-1 4h1l1 1c-1 0-2 1-3 2 0 1-1 1-2 2-2-1-3-1-4-2-1 0-2 0-3 1v1h-1-2c-1 0-2-1-3-1h-4v-1h-3v-1l-1 1c-2-1-4 0-6-2 0-1 1-2 2-3l3 1h3c3 0 5 1 8 1 1 0 1 0 1 1l2-1 1-1h3c2-2 2-2 2-4 1-1 1-2 2-2z" class="E"></path><path d="M817 253h1 0c1 1 1 0 1 1l2 2c-1 2-2 2-1 4h1l1 1c-1 0-2 1-3 2h-3c0-1-1-1-2-2v-1c-1 1-2 1-3 1h-1c-1 1-1 1-2 1l-1-1 2-1 1-1h3c2-2 2-2 2-4 1-1 1-2 2-2z" class="B"></path><path d="M752 221v-2h1l2 1v1c0 2 2 3 3 4v1h1c2 2 5 3 6 5l1 1 1-1 2 2c1 1 3 3 5 4 0 2 2 3 2 5l3 1 1 1v1 1h2s1 1 2 1l1-1c1 0 2 2 3 2 1 1 1-1 2 1 1 1 1 2 1 3 1 0 2 1 3 1h-1v1c0 2 0 2-1 2-7-1-12-6-16-11l-1 1 5 5h-5 0c-2 0-3 0-4-2h1c-1 0-2-1-3-2l-2-2v-1l-7-7-3-3c-1-4-5-7-5-10v-1l-1-1 1-1z" class="J"></path><path d="M751 222l1-1 1 3c2 3 5 3 5 8l1 1 1 2c3 4 6 7 9 10 2 1 2 2 3 4-1 0-2-1-3-2l-2-2v-1l-7-7-3-3c-1-4-5-7-5-10v-1l-1-1z" class="C"></path><defs><linearGradient id="c" x1="769.358" y1="227.648" x2="756.807" y2="232.481" xlink:href="#B"><stop offset="0" stop-color="#b2af9e"></stop><stop offset="1" stop-color="#d4cdbd"></stop></linearGradient></defs><path fill="url(#c)" d="M752 221v-2h1l2 1v1c0 2 2 3 3 4v1h1c2 2 5 3 6 5l1 1 1-1 2 2c1 1 3 3 5 4 0 2 2 3 2 5-2 0-2 0-3-1h0c-3-1-4-3-6-5-1 0-2-1-3-1s-2-1-4 0l-1-2-1-1c0-5-3-5-5-8l-1-3z"></path><path d="M759 233c1-1 1-1 3-1 2 1 4 3 5 4-1 0-2-1-3-1s-2-1-4 0l-1-2z" class="D"></path><path d="M492 141l50 1c1 2 1 2 0 3s-1 1-2 1l-1 1c-1 4 0 10 0 15l3-1h1 4c1-1 2-1 3-1l2 2c-1 1-1 1-2 1 1 0 2 0 3-1h6v1c2 1 4 1 6 2 4 1 7 2 11 2h1c2 0 5 0 7-1s4 0 6 0h1s1 0 2 1l9 1 3 1h4l-4-3h1c1 0 2 1 3 2s2 0 4 0c0 0 1 1 2 1h0c1 0 2 0 3 1h1l2 2 1-1h0 1l4 2c3 2 5 5 9 4v1l-1 2c-2 0-2 0-3 2h1 0 0v-1c2 1 3 1 5 2v4h1c-1 0-1 1-2 1 1 0 2 0 3 1h1 0v-1c1-2 8-1 10-1l1-1h2c0-2-2-3-3-5l1-1h2 1c1-1 1-1 2-1 1-1 3-1 4-1 2 1 4 1 5 1l3 1c2 0 3 1 4 2h1c1 0 1 0 2 1s3 1 4 2c2 1 4 2 5 5l1-1c0-1-1-2-1-3h0 3c1 0 1 1 4 2v1c-1 0-1 0-2 1l-1 1c1 1 2 0 3 0 0 1 0 1 1 2 1-1 2-2 3-2l1-1h1l-1 2 2 2h0v6c-1 2-2 4-1 6v1 5l1-1h1v1-3-2h1l1-1v2c-2 2-1 6-1 9-1 1 0 1 0 2-1 1-1 1-2 1h-2 0c-4-2-9-3-13-4-20-5-39-6-60-7h-40l1 1v12 6h0c-2 1-3 2-4 3h-2-2c-1 1-2 1-2 1l-2-1v3c-1-2-2-5-4-7 0-2-1-6-3-7h-2c-1 1-2 2-2 3v1l-1 1-1-1c-2 5-4 9-6 13 0 2-2 6-2 7l-3 10v3c-1 3-2 8-2 10v6l-1 6h0c-1 1-2 3-3 3 0-1 0-2-1-2v-1c0-1 0-1-1-2v-1 5l-1 2h1-2c-2-1-7 0-10 0h-2-6-1 0-12 0l-3-1h1 1l-1-1 1-1-1-1h-1v2h-1l-1-1 1-1h0c-1 0-1 1-2 1v1l-1-1v-2-1c-1 0-1-1-2-2 0 0 0-1-1-2h0l-2 2v1h-1c-1-1-1-1-2-1h0s-1 1-1 2c-1-1-1-3-1-5-1 1-2 1-2 2-1 1-1 1-1 2h0c-1 0-2 0-3 1h-2l-1-1v-2-5-4-9-8-2-9c0-2 0-3-1-4l-1-2-1-1v-2h0c1-1 1-1 1-2h2l2-2h0l1-1c-1-1-1-2-1-4-2-3-5-9-4-12h-1-2l-1 1h0c-1-1-2-1-2-3v-1c1-1 1-1 0-3l-6-2-1-1h6 1c2-2 2-3 5-3 1-1 1-1 1-2s-2-2-3-2v-1l2-2c-1 0-1 0-2-1h0 0 4v-5-4-1-5-4h0l1-3v-1c1-1 0-4 1-6l-1-7 1-6c-1-1-3-1-4-1v-2h0l13-1z" class="F"></path><path d="M549 166h2c0 1-1 2-1 3h-3v-1l2-2z" class="L"></path><path d="M559 171h-2c-1 0-1 0-2-1 4-1 10-2 14 0-4 1-7 1-10 1zm53 2c3 0 2 1 4 3 1 0 3 0 4 1 1 0 2 0 3 1l-1 1-3-1c-2 0-5-1-7-2 0-1-1-1-2-1 0 0-1 0-1-1 1 0 2 1 3 0v-1z" class="C"></path><defs><linearGradient id="d" x1="551.531" y1="183.55" x2="562.964" y2="165.737" xlink:href="#B"><stop offset="0" stop-color="#aaa597"></stop><stop offset="1" stop-color="#c8c3b2"></stop></linearGradient></defs><path fill="url(#d)" d="M569 170h2v1h1v1h1l1 1v1h0 0c0 1 0 1-1 1s-1 0-2 1c-3 1-5 0-7 1v1c-2 3-8 3-12 4 0 1-1 2-1 2h-2c-1 0-2 1-2 1h-3l-3-1v-6-6h6c3-1 5-1 8 1l1 2h2v-1h-1v-1l2-2c3 0 6 0 10-1z"></path><path d="M547 172c3-1 5-1 8 1l1 2h-1c-1 1-2 2-3 2l-1-2h-1c-2-1-2-2-3-3z" class="D"></path><path d="M541 178c1 0 3-1 4-1l1 1h-1c0 1 0 2 1 3 1 0 4 0 5 1h1c0 1-1 2-1 2h-2c-1 0-2 1-2 1h-3l-3-1v-6z" class="Q"></path><defs><linearGradient id="e" x1="566.903" y1="192.999" x2="574.103" y2="171.503" xlink:href="#B"><stop offset="0" stop-color="#a09c8d"></stop><stop offset="1" stop-color="#beb8a8"></stop></linearGradient></defs><path fill="url(#e)" d="M540 169h1v3 6 6l3 1h3s1-1 2-1h2s1-1 1-2c4-1 10-1 12-4 2 0 3 0 5-1 1-1 5-1 7-2l1-1c2-1 17 3 19 4v2l-1 1c-2 0-2 0-3 1 1 0 1 0 2 1 0 2-1 2-2 3h-5c-2 1-4-1-6 0h-4c-4 0-7 0-10 3h-1 0l-1-2h0l-1 1c0 2 0 4-1 5v1h-1 0c-1 1-3 2-3 4-2 1-3 3-5 4l-1 2h-1-1c-1-1-1-1-2-1l-1 1h-1-1c-1 0-2 0-3-1h0l-1-1c1-1 1-2 2-3-2-3-1-1-3-1l-1-2v-27z"></path><path d="M540 169h1v3 6 6l3 1h3v2c1 1 2 1 4 0l1 1c1 0 5-1 7-1h0c-2 1-6 0-6 2h3l1 1h1c-1 1-2 1-3 1h3v-1c3-1 4 1 6-2 0 2 0 4-1 5v1h-1 0c-1 1-3 2-3 4-2 1-3 3-5 4l-1 2h-1-1c-1-1-1-1-2-1l-1 1h-1-1c-1 0-2 0-3-1h0l-1-1c1-1 1-2 2-3-2-3-1-1-3-1l-1-2v-27z" class="I"></path><path d="M541 184l3 1h3v2h-5-1v-3zm10 20v-1c-1-1-2-2-3-2h0c-1 0-1-1-1-2h1 2 1 2v1l1 2-1 2h-1-1z" class="a"></path><path d="M551 199h2v1l1 2-1 2c-2-2-2-2-2-5z" class="J"></path><path d="M605 166h1c1 0 2 1 3 2s2 0 4 0c0 0 1 1 2 1h0c1 0 2 0 3 1h1l2 2 1-1h0 1l4 2c3 2 5 5 9 4v1l-1 2c-2 0-2 0-3 2h1 0 0v-1c2 1 3 1 5 2v4h1c-1 0-1 1-2 1-2 0-4 1-6 0l-2 2v2c3 0 6 1 7 2h-2c-3 0-4 1-7 2h-5l-2 2c1 1 2 2 3 2s2-1 3-2l1 1 1 1c-2 0-3 1-4 2l-1 2c0 1-1 1-2 2-1 0 0 0-1-1l-1-2h1l-2-2c-2-1-3-1-5 0l-1-1-3 3c-1-2-2-2-3-2h0c1-1 2-1 3-2v-1h0l-1-1c-1 0-2-1-3-1l-2-1-5-2c-2-2-4-2-6-2s-3 0-5 1h0c-2 0-3 0-4-1-1 0-3 1-4 0s-1 0-2-1c-3-1-6-1-8 1h-3v-2h1c3-3 6-3 10-3h4c2-1 4 1 6 0h5c1-1 2-1 2-3-1-1-1-1-2-1 1-1 1-1 3-1l1-1c3 0 5 1 8 1 4 0 8 1 12 1 3 1 5 1 8 1 2-1 5-1 7-3l-1-1h-4v-1h1 3 0 1c-1-2-3-2-4-3-5-3-11-5-16-6v1 1h-1c0-1-1-1-1-2l-4-3z" class="C"></path><path d="M601 193l-2-2 1-1c1 0 2 0 3 1l-2 2z" class="H"></path><path d="M627 187l4 1-2 2v2l-4-2 2-3z" class="L"></path><path d="M625 190c-3-1-5-1-7-3v-1h7c1 0 1 1 2 1l-2 3z" class="F"></path><path d="M566 189h1c3-3 6-3 10-3h4c2-1 4 1 6 0h5c1-1 2-1 2-3-1-1-1-1-2-1 1-1 1-1 3-1 4 0 9 2 14 4v1c-3 2-8 2-12 2h-2-7l1 1h0c1 0 2 1 3 2-2 0-3 0-5 1h0c-2 0-3 0-4-1-1 0-3 1-4 0s-1 0-2-1c-3-1-6-1-8 1h-3v-2z" class="V"></path><path d="M586 189v-2h1l1 1 1 1h0c1 0 2 1 3 2-2 0-3 0-5 1h0c0-2 0-2-1-3z" class="F"></path><path d="M580 188c2 0 4 1 6 1 1 1 1 1 1 3-2 0-3 0-4-1l-3-3z" class="L"></path><path d="M569 191v-2h3c2-1 5-1 8-1l3 3c-1 0-3 1-4 0s-1 0-2-1c-3-1-6-1-8 1z" class="P"></path><path d="M621 193c-2-1-2 0-4-2l1-1 16 4c-3 0-4 1-7 2h-5l-2 2c1 1 2 2 3 2s2-1 3-2l1 1 1 1c-2 0-3 1-4 2l-1 2c0 1-1 1-2 2-1 0 0 0-1-1l-1-2h1l-2-2c-2-1-3-1-5 0l-1-1-3 3c-1-2-2-2-3-2h0c1-1 2-1 3-2v-1h0l-1-1c-1 0-2-1-3-1l-2-1 1-1-3-1 2-2 4 1c1 0 2 0 4-1v1c2 0 3 0 4 1h6z" class="F"></path><path d="M609 199h5l-2 1-3 3c-1-2-2-2-3-2h0c1-1 2-1 3-2z" class="J"></path><path d="M611 194h0c3-1 3 1 5 2l-1 1h-2c-1 0-5 0-6-2 1 0 2-1 4-1z" class="D"></path><path d="M621 193h2v1 1c-1 0-2 0-3 1h-3c-1-1-2-2-2-3h6z" class="B"></path><defs><linearGradient id="f" x1="507.133" y1="153.377" x2="515.296" y2="132.802" xlink:href="#B"><stop offset="0" stop-color="#111715"></stop><stop offset="1" stop-color="#372e29"></stop></linearGradient></defs><path fill="url(#f)" d="M492 141l50 1c1 2 1 2 0 3s-1 1-2 1l-1 1c-1 4 0 10 0 15v3h1l-1 1-1-1v-4c0-5 1-10 0-14-1-1 0-1-1-1-1 1-1 0-1 1v2l-1 2c0-2 0-3-1-5h0l-1 1h-1 0c-1 6 0 12 0 18h-1l-1-4h-4c-1 0-2 0-2 1-1 1-1 2-1 3h-1l-1-1h-2 0c1-1 0-1 0-1v-1c-1 1-1 1-1 2l-1-1-1-1c0 1-1 1-1 3h-1-3l-5 1v-2l-1-2h0-1l-1 3h-1c-1 0-1 0-2 1h-5-4v-1c-2-1-4 0-5 0s0 0-1-1c0-4 0-9 1-13v-3-1-1c-1 0-2 0-3-1s-3-1-4-1v-2h0l13-1z"></path><path d="M522 149v-4l1 1c0 1 0 2 1 3v-4l1 1 1 4c0 3-1 8 0 11-1 0-2 0-2 1-1 1-1 2-1 3h-1l-1-1 2-3c-2-3 0-9-1-12z" class="s"></path><path d="M495 147l1-2v1 12h1c0-5-1-11 1-15 1 4 1 8 1 12v-1c1 3 1 4 0 7-1 0-2 0-3 1v3h-1-1c1-6 0-13 1-18z" class="D"></path><path d="M526 150l1-4c1 1 0 2 1 3 0-2 0-3 1-4 1 1 1 6 2 8v-7l1-1v2h0c-1 6 0 12 0 18h-1l-1-4h-4c-1-3 0-8 0-11z" class="q"></path><path d="M514 148l1-2 1-1c1 2 0 7 0 9 2-2 1-6 2-9h0c1 1 1 2 1 4h0c1-2 1-3 1-4 1 1 1 3 2 4 1 3-1 9 1 12l-2 3h-2 0c1-1 0-1 0-1v-1c-1 1-1 1-1 2l-1-1-1-1c0 1-1 1-1 3h-1c0-1 1-2 1-3l-1-1v-3c1-3 1-6 0-10z" class="N"></path><path d="M486 146c2-1 2-1 4-1h1c1 1 1 1 1 2 0 0 0-1 1-1l1-1v2h1c-1 5 0 12-1 18h1 1v-3c1-1 2-1 3-1l1 1v4h-5-4v-1c-2-1-4 0-5 0s0 0-1-1c0-4 0-9 1-13v-3-1-1z" class="O"></path><path d="M486 148h1v14l-1 1v2c-1 0 0 0-1-1 0-4 0-9 1-13v-3z" class="j"></path><path d="M499 154c0-4 1-7 2-11v8c1-2 1-5 1-8 1 3 1 7 2 11 0-3-1-7 0-9h2 1c0 1 0 1 1 2 0-1 0-1 1-2l1 1c0-1 0-1 1-1l1 3v-3h1c0 1 0 2 1 4v-1c1 4 1 7 0 10v3l1 1c0 1-1 2-1 3h-3l-5 1v-2l-1-2h0-1l-1 3h-1c-1 0-1 0-2 1v-4l-1-1c1-3 1-4 0-7z" class="L"></path><path d="M505 162c1-1 3 0 4 0h1l1 3-5 1v-2l-1-2z" class="S"></path><path d="M505 162c1-1 3 0 4 0h0l-2 2h-1l-1-2z" class="u"></path><path d="M512 148v-3h1c0 1 0 2 1 4-1 3-1 8-1 12h-1-1c1-3 1-9 1-13h0z" class="P"></path><path d="M483 145c1 1 2 1 3 1v1 1 3c-1 4-1 9-1 13 1 1 0 1 1 1s3-1 5 0v1 1h50v2h-1-2-3-1l-12-1h-5c-1 1-2 0-3 0l-1 1-1-1h-3-1l-1 6c-1 9 0 18-1 27h2l-1 1h-5-4-4l-8 1-3 1h-2l1 1-2 2h0-1-2l-1 1h0c-1-1-2-1-2-3v-1c1-1 1-1 0-3l-6-2-1-1h6 1c2-2 2-3 5-3 1-1 1-1 1-2s-2-2-3-2v-1l2-2c-1 0-1 0-2-1h0 0 4v-5-4-1-5-4h0l1-3v-1c1-1 0-4 1-6l-1-7 1-6z" class="e"></path><path d="M495 168c1 8 1 16 1 24 0 2 0 6-1 9h-1v-32l1-1z" class="K"></path><path d="M496 168c1 1 2 1 3 1-1 3 0 8 0 12v19l1 1-1 1c-1-2-2-1-3-2 1-2 0-5 0-7v-17c-1-2 0-6 0-8z" class="n"></path><path d="M499 169h1c0 4 0 9 1 13v-13h1v11c1 2 1 3 1 6 0 4-1 8 0 12l-1 3h-1v-11c-1 4-1 7-1 11l-1-1v-19c0-4-1-9 0-12z" class="E"></path><path d="M481 187v16h5l-3 1h-2l1 1-2 2h0-1-2l-1 1h0c-1-1-2-1-2-3v-1c1-1 1-1 0-3l-6-2-1-1h6 1c2-2 2-3 5-3 1-1 1-1 1-2s-2-2-3-2v-1l2-2c-1 0-1 0-2-1h0 0 4z" class="J"></path><path d="M467 198h6l2 2 1-1h0c1 1 0 1 0 2l3 2-5 1c1-1 1-1 0-3l-6-2-1-1z" class="D"></path><path d="M481 203h5l-3 1h-2l1 1-2 2h0-1-2l-1 1h0c-1-1-2-1-2-3v-1l5-1h2z" class="g"></path><path d="M502 169l6-1-1 6c-1 9 0 18-1 27h-3v-3c-1-4 0-8 0-12 0-3 0-4-1-6v-11z" class="Q"></path><path d="M486 168h7l-3 1c-2 2-1 12-1 15v15l1 1-1 1v-4h-1v2l-1-1-1-7v8h-1v-23c0-2-1-6 1-8z" class="Z"></path><path d="M493 168l1 1v32c-1 0-2 0-3-1v-3-1l-1 4-1-1v-15c0-3-1-13 1-15l3-1z" class="l"></path><path d="M483 145c1 1 2 1 3 1v1 1 3c-1 4-1 9-1 13 1 1 0 1 1 1s3-1 5 0v1 1h50v2h-1-2-3-1l-12-1h-5c-1 1-2 0-3 0l-1 1-1-1h-3-1l-6 1h-1v13c-1-4-1-9-1-13h-1c-1 0-2 0-3-1h-1l-1 1-1-1h-7-3l-1-1h2l-2-2v-1c1-1 0-4 1-6l-1-7 1-6z" class="R"></path><path d="M564 188l1-1h0l1 2h0v2h3c2-2 5-2 8-1 1 1 1 0 2 1s3 0 4 0c1 1 2 1 4 1h0c2-1 3-1 5-1s4 0 6 2l5 2 2 1c1 0 2 1 3 1l1 1h0v1c-1 1-2 1-3 2h0c1 0 2 0 3 2l3-3 1 1c2-1 3-1 5 0l2 2h-1l1 2c1 1 0 1 1 1 1-1 2-1 2-2l1-2c1-1 2-2 4-2v1h2l1 1c1 0 1 1 2 1 0 1-1 1-1 1-3 0-4 0-7 1v-1c-1 1-2 2-2 4l2-1c0-1 0-1 1-1 2 0 4 0 5-1 2 0 4 1 7 0 1 0 6 0 7 1-5 0-12-1-17 0-1 1-1 0-2 2h3-1-2l-2 1h-40l1 1v12 6h0c-2 1-3 2-4 3h-2-2c-1 1-2 1-2 1l-2-1v3c-1-2-2-5-4-7 0-2-1-6-3-7h-2c-1 1-2 2-2 3v1l-1 1-1-1 1-2-1-1 1-2v-6c0-2 0-10-1-11h-1-1v-1l1-3c0-2 2-3 3-4h0 1v-1c1-1 1-3 1-5z" class="J"></path><path d="M573 202c3 1 3 1 4 3 1 0 2 1 2 1v1h-1c-2-1-3-2-5-4v-1z" class="B"></path><path d="M608 197l1 1h0v1c-1 1-2 1-3 2h0c-2-1-4-1-5-1-2 0-3 0-5 1h-1-5v-1h4c2 0 4-1 6-1h1c2 0 2 0 3-1h2c1 0 1 0 2-1z" class="E"></path><path d="M570 202l-1 1-1-1v-5c2 0 4-1 7-1 0 0 1 0 2-1v1c-1 1 0 1-1 1s-2 1-2 1c-1 2-1 2-1 4v1l-2-1h-1z" class="D"></path><path d="M570 202h-1l-1-2 1-1h1c1 1 1 1 1 2v1h-1z" class="B"></path><path d="M609 203l3-3 1 1c-2 2-5 3-7 4 0 1-2 1-2 1-1 1 0 1-1 2h-5-12c-3 0-5-1-7-1v-1h5 15c0-1 1-1 2-1 3 0 5-1 8-2z" class="f"></path><path d="M592 191c2 0 4 0 6 2l5 2 2 1c-3 0-4-1-6-1s-6 2-8 2l-2 2v1h-2c-4 1-7 1-11 2h-1v-1l12-2v-1c1-1 2-1 4-1 1-1 2-1 3-3v-1c-3 0-4 0-7-1 2-1 3-1 5-1z" class="D"></path><path d="M601 200c1 0 3 0 5 1 1 0 2 0 3 2-3 1-5 2-8 2-1 0-2 0-2 1h-15c-1-1-1-2-2-2 1-1 4-1 6-1h6c3-1 5-2 7-3z" class="a"></path><path d="M564 188l1-1h0l1 2h0v2l1 2h2c-1 2-2 3-2 4v2c-1 2 0 3 0 4l2 1c0 1-1 1-2 2l1 2h2c1 1 1 1 2 1 0 1 1 1 2 1 1 1 2 1 2 2v1l2-1h0 0l2 1h-2c1 1 0 1 1 2l-1 1h1c2-1 3-1 5-1 1 3 0 9 1 13h0c-2 1-3 2-4 3h-2-2c-1 1-2 1-2 1l-2-1v3c-1-2-2-5-4-7 0-2-1-6-3-7h-2c-1 1-2 2-2 3v1l-1 1-1-1 1-2-1-1 1-2v-6c0-2 0-10-1-11h-1-1v-1l1-3c0-2 2-3 3-4h0 1v-1c1-1 1-3 1-5z" class="c"></path><path d="M573 224h3c1 0 1 0 2 2l-1 2s-1 0-1 1l-1-1-1-2c-1-1-1-1-1-2z" class="t"></path><path d="M576 213l2-1h0 0l2 1h-2c1 1 0 1 1 2l-1 1h1v1c-1 0-1 0-2 1v1l-1-2c-1 0-3 0-4 1h-1c-1 0-1 1-3 2h0c0-1-1-2 0-2v-1c1-1 0-1 1-3 1 1 4-1 6 0 0 0 0 1 1 1v-2z" class="a"></path><path d="M559 198c0-2 2-3 3-4h0v17c0 3 1 8-1 11l-1-1 1-2v-6c0-2 0-10-1-11h-1-1v-1l1-3z" class="q"></path><path d="M564 188l1-1h0l1 2c0 6-1 12-1 18l1 9 1 1v2l-1-2-1 1v1h-1v-11-13-1h-1v-1c1-1 1-3 1-5z" class="b"></path><path d="M661 178c2 1 4 1 5 1l3 1c2 0 3 1 4 2h1c1 0 1 0 2 1s3 1 4 2c2 1 4 2 5 5l1-1c0-1-1-2-1-3h0 3c1 0 1 1 4 2v1c-1 0-1 0-2 1l-1 1c1 1 2 0 3 0 0 1 0 1 1 2 1-1 2-2 3-2l1-1h1l-1 2 2 2h0v6c-1 2-2 4-1 6v1 5l1-1h1v1-3-2h1l1-1v2c-2 2-1 6-1 9-1 1 0 1 0 2-1 1-1 1-2 1h-2 0c-4-2-9-3-13-4-20-5-39-6-60-7l2-1h2 1-3c1-2 1-1 2-2 5-1 12 0 17 0-1-1-6-1-7-1-3 1-5 0-7 0-1 1-3 1-5 1-1 0-1 0-1 1l-2 1c0-2 1-3 2-4v1c3-1 4-1 7-1 0 0 1 0 1-1-1 0-1-1-2-1l-1-1h-2v-1l-1-1-1-1c-1 1-2 2-3 2s-2-1-3-2l2-2h5c3-1 4-2 7-2h2c-1-1-4-2-7-2v-2l2-2c2 1 4 0 6 0 1 0 2 0 3 1h1 0v-1c1-2 8-1 10-1l1-1h2c0-2-2-3-3-5l1-1h2 1c1-1 1-1 2-1 1-1 3-1 4-1z" class="c"></path><path d="M632 198h2c2-1 4 0 7 0 0-1 0-1 1-1h1l4 1h0-1-1 0c-2 1-4 0-6 1-2-2-2-1-4-1-1 0-1 0-1 1 1 0 1 1 3 1h1 0l2 2h0 0c-2 0-5-1-6 0 1 1 2 1 3 1-1 0-2 0-3 1l-1 1h-8c3-1 4-1 7-1 0 0 1 0 1-1-1 0-1-1-2-1l-1-1h-2v-1h4c-1-1-1-1-1-2h1z" class="Q"></path><path d="M685 190l1-1c0-1-1-2-1-3h0 3c1 0 1 1 4 2v1c-1 0-1 0-2 1l-1 1c1 1 2 0 3 0 0 1 0 1 1 2 1-1 2-2 3-2l1-1h1l-1 2 2 2h0v6c-1 2-2 4-1 6v1 5l1-1h1v1-3-2h1l1-1v2c-2 2-1 6-1 9-1 1 0 1 0 2-1 1-1 1-2 1h-2 0c-1-2-1-5-2-7v-1-3l-1-1 1-1v-1c1-1 0-4 0-5l-1-1v-1l2-2h-1c-1 0-2 1-3 1h-4l1-1c0-1 0-1 1-2h0v-1c-2 0-1 1-3 2h-1v-1l-1-1v-4z" class="a"></path><path d="M699 216l-1 1v2l-1-1c-1-4-1-10 0-15v10h1l1-1 1 1-1 3z" class="B"></path><path d="M697 203c0-3 1-6 2-9v6c-1 2-2 4-1 6v1 5l1-1h1v1-3-2h1l1-1v2c-2 2-1 6-1 9-1 1 0 1 0 2-1 1-1 1-2 1v-4l1-3-1-1-1 1h-1v-10z" class="L"></path><defs><linearGradient id="g" x1="670.719" y1="203.724" x2="653.479" y2="179.929" xlink:href="#B"><stop offset="0" stop-color="#8f8d7f"></stop><stop offset="1" stop-color="#aea99a"></stop></linearGradient></defs><path fill="url(#g)" d="M661 178c2 1 4 1 5 1l3 1c2 0 3 1 4 2h1c1 0 1 0 2 1s3 1 4 2c2 1 4 2 5 5v4l1 1v1h1c2-1 1-2 3-2v1h0c-1 1-1 1-1 2l-1 1c0 2-1 3-1 4-1 0-2 0-3-1l-1 1h-1c-2 1-3 2-4 2-1 1-2 2-4 2 1-2 4-2 4-4l-2-1v-1l-2-1c-1 1-2 1-3 1h-1c-2-1-3-1-4-1l-1 1 2 2h1l1 1h-1c-2 1-5 0-7 1h0-3l1-1v-1-1c-2 0-4-1-6-2l-6-1h0l-4-1h-1c-1 0-1 0-1 1-3 0-5-1-7 0h-2-1c0 1 0 1 1 2h-4l-1-1-1-1c-1 1-2 2-3 2s-2-1-3-2l2-2h5c3-1 4-2 7-2h2c-1-1-4-2-7-2v-2l2-2c2 1 4 0 6 0 1 0 2 0 3 1h1 0v-1c1-2 8-1 10-1l1-1h2c0-2-2-3-3-5l1-1h2 1c1-1 1-1 2-1 1-1 3-1 4-1z"></path><path d="M674 199l-6-3 3 1 3 1c2 0 6 0 7-1-1-2-9-5-11-6s-3-1-3-2c4 1 7 3 11 5 3 1 5 1 7 0l1 1v1h1c2-1 1-2 3-2v1h0c-1 1-1 1-1 2l-1 1c0 2-1 3-1 4-1 0-2 0-3-1l-1 1h-1c-2 1-3 2-4 2-1 1-2 2-4 2 1-2 4-2 4-4l-2-1v-1l-2-1z" class="J"></path><path d="M652 186h2l6 3 2 1 1 1h-3c-1 1-1 1-2 1s-1-1-2-1h-3c-1 0-1 0-2 1-2 0-5-1-7-1s-3-1-4 0h-1l-1 2c2 2 10 4 13 5h2v1l-6-1h0l-4-1h-1c-1 0-1 0-1 1-3 0-5-1-7 0h-2-1c0 1 0 1 1 2h-4l-1-1-1-1c-1 1-2 2-3 2s-2-1-3-2l2-2h5c3-1 4-2 7-2h2c-1-1-4-2-7-2v-2l2-2c2 1 4 0 6 0 1 0 2 0 3 1h1 0v-1c1-2 8-1 10-1l1-1z" class="D"></path><path d="M632 198l1-1h2v-1c2 0 3-1 4 0l4 1h-1c-1 0-1 0-1 1-3 0-5-1-7 0h-2z" class="J"></path><path d="M652 186h2l6 3c-2 1-4 0-6 0h0l-3-2 1-1z" class="L"></path><path d="M514 168c1 0 2 1 3 0h5l12 1h1 3 2v27l1 2c2 0 1-2 3 1-1 1-1 2-2 3l1 1h0c1 1 2 1 3 1h1 1l2 2c-1 0-1 1-3 1l1 1v1l-1-1-1 1c-2 1-2 2-4 4v1l-1-1c-1 1-1 1-2 1v1 4c-1 1-1 2-1 3l-1 1v3h-2-4c-1 0-1 0-2-1h-5 0l1 1h-2 0c1 0 2 0 4 1h-12-13-3-11c0-1 1-1 1-2h-1-1l-1-1-1 1-1-1 1-1c-1-1-1-2-1-4-2-3-5-9-4-12h0l2-2-1-1h2l3-1 8-1h4 4 5l1-1h-2c1-9 0-18 1-27l1-6h1 3l1 1 1-1z" class="B"></path><path d="M513 201h1c1-3 0-8 1-11 1 3 0 7 0 11 2 0 4 1 6 0h1 0 1 1 3 1c1 0 1 0 2-1l1 1h1 2v-2l1-1v3h1l-1-1h2c1 0 1 0 2 1 1-1 1-4 1-5l1 2c2 0 1-2 3 1-1 1-1 2-2 3l1 1h0-10l-11-1h-2c-4 0-7 1-11 0v-1h2 2z" class="h"></path><path d="M512 168l1 1 1-1v15l-1 18h-2-2l1-18c0-3 0-7-1-11v-4h3z" class="L"></path><path d="M512 168l1 1c0 5 0 12-1 17v2 1 3c-1-1-1-3-1-5v-5l1-14z" class="G"></path><path d="M509 168h3l-1 14v5 14h-2l1-18c0-3 0-7-1-11v-4z" class="a"></path><path d="M508 168h1v4c1 4 1 8 1 11l-1 18v1c4 1 7 0 11 0h2l11 1h10c1 1 2 1 3 1h1 1l2 2c-1 0-1 1-3 1l1 1v1l-1-1-1 1c-2 1-2 2-4 4v1l-1-1c-1 1-1 1-2 1v1-3l1-1c-1-1-2-1-2-1 0-1 0-1-1-1v-2h-15l-8-1-13 1-2-1h-1c1-1 0-1 1-1l1-1c-2-1-2-1-2-2h4 5l1-1h-2c1-9 0-18 1-27l1-6z" class="D"></path><path d="M508 168h1v4 8c-1-2-1-4-2-6l1-6z" class="V"></path><path d="M522 202l11 1c-1 1-1 1-2 1h-1c-1 1-1 1-2 0h0c-2 1-2 1-3 1-2-1-2-1-3-3z" class="B"></path><path d="M498 202h4c1 1 2 1 4 1l1 1-1 1 1 1c2 0 5-1 7 0l-13 1-2-1h-1c1-1 0-1 1-1l1-1c-2-1-2-1-2-2z" class="I"></path><path d="M522 207l14-1 2-2h5 1c1 1 1 1 2 1l1 1c1-1 1-1 1-2l2 2c-1 0-1 1-3 1h-10-15z" class="a"></path><path d="M537 207h10l1 1v1l-1-1-1 1c-2 1-2 2-4 4v1l-1-1c-1 1-1 1-2 1v1-3l1-1c-1-1-2-1-2-1 0-1 0-1-1-1v-2z" class="Y"></path><path d="M522 168l12 1h1 3 2v27c0 1 0 4-1 5-1-1-1-1-2-1h-2l1 1h-1v-3l-1 1v2h-2-1l-1-1c0-3 1-10-1-12-1 1 0 3 0 4-1-1 0-2-1-4h-1c0 2 0 4-1 6 0-1 0-2-1-4v6h-1v-17c-1-1-1-2-1-3-1-1-1-6-1-8z" class="N"></path><path d="M528 169l1 1c0 4-1 7-1 11 0 0-1 0-1-1v-7-2c0-1 1-1 1-2zm-4 10v-1-6l1 2v-3l1 3c1 4 0 9-1 12v4 6h-1v-17z" class="C"></path><path d="M534 169h1v17c0 4 1 9 0 12l-1 1v2h-2v-1c0-2 0-6 1-7h1v-24z" class="F"></path><path d="M535 169h3 2v27c0 1 0 4-1 5-1-1-1-1-2-1h-2l1 1h-1v-3c1-3 0-8 0-12v-17z" class="C"></path><path d="M538 169h2v27c0 1 0 4-1 5-1-1-1-1-2-1h-2l1-3 2-1v-4-23z" class="G"></path><defs><linearGradient id="h" x1="505.586" y1="214.353" x2="530.708" y2="201.208" xlink:href="#B"><stop offset="0" stop-color="#111312"></stop><stop offset="1" stop-color="#322e2d"></stop></linearGradient></defs><path fill="url(#h)" d="M486 203l8-1h4c0 1 0 1 2 2l-1 1c-1 0 0 0-1 1h1l2 1 13-1 8 1h15v2c1 0 1 0 1 1 0 0 1 0 2 1l-1 1v3 4c-1 1-1 2-1 3l-1 1v3h-2-4c-1 0-1 0-2-1h-5 0l1 1h-2 0c1 0 2 0 4 1h-12-13-3-11c0-1 1-1 1-2h-1-1l-1-1-1 1-1-1 1-1c-1-1-1-2-1-4-2-3-5-9-4-12h0l2-2-1-1h2l3-1z"></path><path d="M532 215c0-3 2-5 4-7v1c0 2-1 3-1 4l-2 1v1h-1z" class="T"></path><path d="M532 215h1v-1l2-1-3 10c0 1-1 1-2 2l1 1c-1 0-1 0-2-1l-1-1c1-3 2-6 4-9z" class="E"></path><path d="M536 209l2 1s1 0 2 1l-1 1v3 4c-1 1-1 2-1 3l-1 1v3h-2-4l-1-1c1-1 2-1 2-2l3-10c0-1 1-2 1-4z" class="Z"></path><path d="M535 226c-1-1-1-2-1-2v-1c1-2 2-3 2-5s1-5 2-7l1 1v3 4c-1 1-1 2-1 3l-1 1v3h-2z" class="K"></path><path d="M525 213c1-1 2-3 4-3l1-1c1 1 1 1 2 3-1 1-4 4-3 5v1l-2 4v1 1h1l1 1h-5l-1-1h0c-2-4 1-5 1-8l1-3z" class="k"></path><path d="M517 214c0-2 1-3 2-5 2 0 2 0 3 1h2c1 1 1 2 1 3l-1 3c0 3-3 4-1 8h0-6v-1h0c0-2-1-3-2-4l2-5z" class="O"></path><path d="M517 214c0-2 1-3 2-5 2 0 2 0 3 1l-5 13c0-2-1-3-2-4l2-5z" class="a"></path><path d="M512 210h3c1 1 1 2 2 4l-2 5c1 1 2 2 2 4h0v1h-8-1c-2-3-3-7-4-10 0-2-1-3-2-4h2 5v1h1l1-1h0 1 0z" class="Z"></path><path d="M515 219c1 1 2 2 2 4h0l-2 1c-1-2 0-3 0-5z" class="I"></path><path d="M512 210c1 0 2 0 2 1 0 2 0 3-1 5h0c1 2 1 3 0 5h-1 0v-11z" class="d"></path><path d="M502 210h2c0 1 1 2 1 2 1 3 1 5 2 7h1c0-2 0-4 1-5h0c0 2 1 5 0 8v2h-1c-2-3-3-7-4-10 0-2-1-3-2-4z" class="e"></path><path d="M486 203l8-1h4c0 1 0 1 2 2l-1 1c-1 0 0 0-1 1h1l2 1h1c0 1 1 1 2 2v1h-2c1 1 2 2 2 4 1 3 2 7 4 10h1 8 6l1 1h0l1 1h-2 0c1 0 2 0 4 1h-12-13-3-11c0-1 1-1 1-2h-1-1l-1-1-1 1-1-1 1-1c-1-1-1-2-1-4-2-3-5-9-4-12h0l2-2-1-1h2l3-1z" class="E"></path><path d="M499 209c1-1 1-1 2 0l1 1c1 1 2 2 2 4-1 1-1 2 0 3l1 2v1c-1 0-1 0-1-1-1-1-1-2-1-3l-1 1-3-7v-1z" class="a"></path><path d="M486 215c0-2-2-4-1-6h3l1 2v1l4 12c1 1 1 1 2 0l2 1h-3c-1 0-1 0-1 1 1 1 3 0 5 0 5 1 11 0 17 1h-13-3-11c0-1 1-1 1-2h1v-1c-2-3-3-6-4-9h0z" class="X"></path><path d="M486 215c0-2-2-4-1-6h3l1 2v1c0 1-1 2 0 3l1 5h0c-1-1-1-2-2-3l-1-2h-1 0z" class="k"></path><path d="M491 210h0c2 0 4 0 5-1h1 2v1l3 7h0l3 7c-3 0-6 0-8 1l-2-1c-1 1-1 1-2 0l-4-12v-1l1-1h1z" class="O"></path><path d="M491 210h0c2 0 4 0 5-1h1 2v1c-1 1-2 1-1 3l1 5h1c0 2 1 2 1 3-1 1-1 1-2 1-1-1-1-2-2-3l-1-1 2 1h1l-2-3v-2c-1-2-4-3-6-4z" class="S"></path><path d="M490 210h1c2 1 5 2 6 4v2l2 3h-1l-2-1c-1-1-2-1-3-1-2-2-3-4-3-7z" class="O"></path><path d="M490 210c0 3 1 5 3 7 1 0 2 0 3 1l1 1v4h-1l-1 1c-1 1-1 1-2 0l-4-12v-1l1-1z" class="Y"></path><path d="M486 203l8-1h4c0 1 0 1 2 2l-1 1c-1 0 0 0-1 1h1l2 1h1c0 1 1 1 2 2v1h-2l-1-1c-1-1-1-1-2 0h-2-1c-1 1-3 1-5 1h0-1l-1 1-1-2h-3c-1 2 1 4 1 6h0c1 3 2 6 4 9v1h-1-1-1l-1-1-1 1-1-1 1-1c-1-1-1-2-1-4-2-3-5-9-4-12h0l2-2-1-1h2l3-1z" class="p"></path><path d="M487 204h3l2 1c2 1 4 1 6 1h1l-12 1v-3z" class="b"></path><path d="M480 207h0l2-2-1-1h2c1 1 3 0 4 0v3h-7z" class="n"></path><path d="M486 203l8-1h4c0 1 0 1 2 2l-1 1c-1 0 0 0-1 1-2 0-4 0-6-1l-2-1h-3c-1 0-3 1-4 0l3-1z" class="Q"></path><path d="M487 225h1c0-2-3-5-4-7-1-3-1-6-2-9 1 0 2 1 2 2 0 2 1 3 2 4h0 0c1 3 2 6 4 9v1h-1-1-1z" class="O"></path><path d="M554 202c2-1 3-3 5-4l-1 3v1h1 1c1 1 1 9 1 11v6l-1 2 1 1-1 2c-2 5-4 9-6 13 0 2-2 6-2 7l-3 10v3c-1 3-2 8-2 10v6l-1 6h0c-1 1-2 3-3 3 0-1 0-2-1-2v-1c0-1 0-1-1-2v-1 5l-1 2h1-2c-2-1-7 0-10 0h-2-6-1 0-12 0l-3-1h1 1l-1-1 1-1-1-1h-1v2h-1l-1-1 1-1h0c-1 0-1 1-2 1v1l-1-1v-2-1c-1 0-1-1-2-2 0 0 0-1-1-2h0l-2 2v1h-1c-1-1-1-1-2-1h0s-1 1-1 2c-1-1-1-3-1-5-1 1-2 1-2 2-1 1-1 1-1 2h0c-1 0-2 0-3 1h-2l-1-1v-2-5-4-9-8-2-9c0-2 0-3-1-4l-1-2-1-1v-2h0c1-1 1-1 1-2h2l2-2h0l1 1 1-1 1 1h1 1c0 1-1 1-1 2h11 3 13 12c-2-1-3-1-4-1h0 2l-1-1h0 5c1 1 1 1 2 1h4 2v-3l1-1c0-1 0-2 1-3v-4-1c1 0 1 0 2-1l1 1v-1c2-2 2-3 4-4l1-1 1 1v-1l-1-1c2 0 2-1 3-1l-2-2 1-1c1 0 1 0 2 1h1 1l1-2z" class="M"></path><path d="M502 232c1 1 2 1 3 1v1h0c-1 0-2 0-2 1l-3-1h0l2-2z" class="S"></path><path d="M498 230h0l2 2h2l-2 2h0v1h-2v-5z" class="Y"></path><path d="M499 227h3l-1 1c0 1 0 2 1 3h-1l-1 1-2-2 1-3z" class="S"></path><path d="M503 235h2c1 0 1-1 2-1 0 0 0 1 1 1 0 1 0 2-1 2v5h-1v-2h0v-1h-3v-1c-1 0-2 0-3-1v-2-1l3 1z" class="R"></path><path d="M509 256c1-4 0-9 0-12s1-6 0-9h0c0-2 0-3 2-5l1 1s0-1 1-1c1 3 0 6 0 10v-6l-1 1c0 2 0 5-1 7h-1c0 5 0 10-1 14h0z" class="l"></path><path d="M509 256c1-4 1-9 1-14h1c1-2 1-5 1-7l1-1v6l1 2-1-1c-1 1-1 3-1 4v1c1 3-1 7 0 10 0 1 0 3-1 3l-2-1v-2z" class="K"></path><path d="M511 259c1 0 1-2 1-3-1-3 1-7 0-10v-1c0-1 0-3 1-4l1 1v12l-1 17h0-1v-1-7c0-1-1-2-1-4z" class="T"></path><path d="M528 229c0 1 0 2 1 3h0v2h1v4l-2-2c-1 0-2 0-2 1h0c1 6 0 14-1 19h-3v5h-3v-1c-1 0-2 0-3 1l-1-1c-2-2 0-4-1-6v-12l-1-2c0-4 1-7 0-10l2-1 1 1-1 1 1 1c1 1 1 2 2 2 2 0 3-1 5-1h0c2-1 0-2 2-3l1 1v-1h1l1-1z" class="Y"></path><path d="M515 235h5v1l-3 2v2h-1v-2c-1-1-1-2-1-3z" class="p"></path><path d="M524 239c-1 1-1 1-1 2h-1-3c-1 0-1 0-1-1h3l1-1v-4h1l1 1 1 1-1 2z" class="U"></path><path d="M524 236c1-1 1-2 3-2l1 2c-1 0-2 0-2 1h0c1 6 0 14-1 19h-3v5h-3v-1c-1 0-2 0-3 1l-1-1c0-3-1-9 1-11h2c1 1 1 1 2 1 1-1 1-1 2-1h1c0-1 0-1 1-2h0v-1-2l-1-1c1-1 1-3 1-4l1-2-1-1z" class="o"></path><path d="M525 256c1-5 2-13 1-19h0c0-1 1-1 2-1l2 2v1c-1 1-1 1-1 2h1l3 4v2l-1 7v11c0 2 1 5 0 6v3 2l2-1v1c-1 2-2 2-2 5-1-1-1 0-1-1-1 1-1 0-1 1h-1v-1l1-4h-1-5c-1 1-1 2-1 4h0l1 1-1 1-1-1v-6-2c-3-1-5 0-7-1h-1l-1-1 1-17c1 2-1 4 1 6l1 1c1-1 2-1 3-1v1h3v-5h3z" class="I"></path><path d="M530 241l3 4v2l-1 7v11c0 2 1 5 0 6v3 2l2-1v1c-1 2-2 2-2 5-1-1-1 0-1-1-1 1-1 0-1 1h-1v-1l1-4s0-1 1-1v-7c-1 2-1 4-1 6h-1v-7h2v-2-7-3c-1-2-1-3-1-4 1-1 1 0 0-1l-1-1 1-4v-4z" class="o"></path><path d="M514 254c1 2-1 4 1 6l1 1c1-1 2-1 3-1v1h3v-5h3v5h1c1-1 1-2 1-4v4l1 1c-1 4-1 8 0 12l-1 1h1s0 1 1 1h-5c-1 1-1 2-1 4h0l1 1-1 1-1-1v-6-2c-3-1-5 0-7-1h-1l-1-1 1-17z" class="i"></path><path d="M516 261c1-1 2-1 3-1v1 9c-1 0-1 0-2-1h-1c0-2 0-2 1-3h0c1-1 1-1 1-3h-1v-1l-1-1z" class="M"></path><path d="M514 254c1 2-1 4 1 6l1 1 1 1v1h1c0 2 0 2-1 3h0c-1 1-1 1-1 3l-1 1v1l1 1h-2l-1-1 1-17z" class="O"></path><path d="M524 276v-1l-1-1c1-1 1-1 1-2 2-1 1-7 1-9 1-1 2-1 3-1-1 4-1 8 0 12l-1 1h1s0 1 1 1h-5z" class="l"></path><path d="M506 242h1c0 3-1 8 0 10h0c-1 2 0 5 0 7l1 1c1-2 0-3 1-4h0v2l2 1c0 2 1 3 1 4v7 1h1 0l1 1h1c2 1 4 0 7 1v2 6l1 1 1-1-1-1h0c0-2 0-3 1-4h5 1l-1 4v1h1c0-1 0 0 1-1 0 1 0 0 1 1 0-3 1-3 2-5v-1l-2 1v-2-3c1 1 2 2 4 3v-3h1 1v1c1 1 1 2 1 3l2 1h0v5l-1 2h1-2c-2-1-7 0-10 0h-2-6-1 0-12 0l-3-1h1 1l-1-1 1-1-1-1h-1v2h-1l-1-1 1-1h0c-1 0-1 1-2 1v1l-1-1v-2-1c1-1 1-2 1-4h1v-2c1-2 0-3 1-5v-4h2 0v-10-5h0v-5z" class="Q"></path><path d="M523 280c1-2 1-2 3-3h1l1 1c0 1-1 2-1 3h-3l-1-1z" class="G"></path><path d="M538 271v1c1 1 1 2 1 3l2 1h0v5h-1v-4c-1 2-1 2-1 4l-1 1v-1-4h-1c0 1-1 1-1 2s0 2-1 2v-3h-1c1-2 1-2 3-3l-1-4h1 1z" class="e"></path><path d="M515 279c0-2 0-4 1-6h1 1v1c2 0 3 0 4 1v6c-1-1 0-2-2-3-1 0-1 0-2 1 1 1 1 1 1 2h-3-1v-2z" class="f"></path><path d="M509 258l2 1c0 2 1 3 1 4v7 1h1 0l1 1h1c2 1 4 0 7 1v2c-1-1-2-1-4-1v-1h-1-1c-1 2-1 4-1 6 0-1-1-1-1-2-1-1 0-3-1-4h-2l-1-1h0c-1-2-1-11-1-14z" class="X"></path><path d="M506 242h1c0 3-1 8 0 10h0c-1 2 0 5 0 7l1 1c1-2 0-3 1-4h0v2c0 3 0 12 1 14h0c-1 1-1 1-2 1v3h-1v-3-1l-1 1-1-1 1-1v-1c-2 1-2 1-2 3h-1v-2c1-2 0-3 1-5v-4h2 0v-10-5h0v-5z" class="U"></path><path d="M480 226h2l2-2h0l1 1 1-1 1 1h1 1c0 1-1 1-1 2h11l-1 3h0v5h2v2c1 1 2 1 3 1v1h3v1h0v2 5h0v5 10h0-2v4c-1 2 0 3-1 5v2h-1c0 2 0 3-1 4-1 0-1-1-2-2 0 0 0-1-1-2h0l-2 2v1h-1c-1-1-1-1-2-1h0s-1 1-1 2c-1-1-1-3-1-5-1 1-2 1-2 2-1 1-1 1-1 2h0c-1 0-2 0-3 1h-2l-1-1v-2-5-4-9-8-2-9c0-2 0-3-1-4l-1-2-1-1v-2h0c1-1 1-1 1-2z" class="K"></path><path d="M498 263h-1v-1l-1-1h1v-8h0l2-2v11 2l-1-1zm-4-14v-12l1-1h1v2c1 0 1-1 2-1 0 1 0 3 1 4v4h0l-1 1-1 1v-5-2h0c-2 2-1 5-2 7v2h-1z" class="b"></path><path d="M494 249h1v7c2 3 1 9 1 12h1 0c-1-1 0-3 1-5l1 1c0 2-1 5 0 7v1 3s0-1-1-2h0l-2 2v1h-1c-1-1-1-1-2-1h0l1-4v-22z" class="n"></path><path d="M494 271l2 1h0v1c-1 1 0 1 0 2v1h-1c-1-1-1-1-2-1h0l1-4z" class="o"></path><path d="M491 247h0c1-1 1-3 1-4 1 1 1 1 1 2v4h1v22l-1 4s-1 1-1 2c-1-1-1-3-1-5l-1-15 1-10z" class="O"></path><path d="M488 227h11l-1 3h0v5 2c-1 0-1 1-2 1v-2h-1l-1 1v12h-1v-4c0-1 0-1-1-2 0 1 0 3-1 4h0v-5c0-2-1-3-2-4h0c2-1 2-1 2-3l-2-2h1v-1c1 1 1 2 3 2h0c0-2 1-3 1-5h-3c-2 0-2-1-3-2h0z" class="M"></path><path d="M495 236c0-1-1-2-1-3h1v-2c1-1 1-1 3-1v5 2c-1 0-1 1-2 1v-2h-1z" class="m"></path><path d="M499 249c1-1 0-1 2-1v-2l1 1v1h1c0 2-2 4 0 7 0 1-1 3 0 5l-1 2 1 1 1-1v4c-1 2 0 3-1 5v2h-1c0 2 0 3-1 4-1 0-1-1-2-2v-3-1c-1-2 0-5 0-7v-2-11-2z" class="R"></path><path d="M480 226h2l2-2h0l1 1 1-1 1 1h1 1c0 1-1 1-1 2h0c1 1 1 2 3 2h3c0 2-1 3-1 5h0c-2 0-2-1-3-2v1h-1l2 2c0 2 0 2-2 3h0c0 1 0 1-1 1h-1v3h-1v-1-5c-1-1-2-1-3-2-1-2-2-2-3-3l-1-1v-2h0c1-1 1-1 1-2z" class="p"></path><path d="M480 226h2l2-2h0l1 1 1-1 1 1h1 1c0 1-1 1-1 2h0l-9 1c1-1 1-1 1-2z" class="b"></path><path d="M498 235h2v2c1 1 2 1 3 1v1h3v1h0v2 5h0v5 10h0-2l-1 1-1-1 1-2c-1-2 0-4 0-5-2-3 0-5 0-7h-1v-1l-1-1v2c-2 0-1 0-2 1h-1 0l1-2v-2-4c-1-1-1-3-1-4v-2z" class="i"></path><path d="M498 235h2v2c1 1 2 1 3 1v1h-1c1 1 0 1 1 2h0c-1 1-1 2-1 3v1c-1 0-2 1-3 2h0v-2-4c-1-1-1-3-1-4v-2z" class="U"></path><path d="M480 231c1 1 2 1 3 3 1 1 2 1 3 2v5 1h1v-3h1c1 0 1 0 1-1 1 1 2 2 2 4v5l-1 10 1 15c-1 1-2 1-2 2-1 1-1 1-1 2h0c-1 0-2 0-3 1h-2l-1-1v-2-5-4-9-8-2-9c0-2 0-3-1-4l-1-2z" class="K"></path><path d="M480 231c1 1 2 1 3 3 1 1 2 1 3 2l-1 1v7c-1-1-1-2-1-3h0c-1-1-2-2-2-4s0-3-1-4l-1-2z" class="M"></path><path d="M486 262c-1-1-1-7 0-8h0l1 1v8c1-1 1-2 2-4 0 1 0 1 1 2v-4l1 15c-1 1-2 1-2 2-1 1-1 1-1 2h0c-1 0-2 0-3 1h-2l-1-1v-2c1 0 2 0 3-1l1-1v-10z" class="t"></path><path d="M486 262c1 3 1 5 1 8v3h1l1 1c-1 1-1 1-1 2h0c-1 0-2 0-3 1h-2l-1-1v-2c1 0 2 0 3-1l1-1v-10z" class="U"></path><path d="M482 237c0 2 1 3 2 4h0c0 1 0 2 1 3v29c-1 1-2 1-3 1v-5-4-9-8-2-9z" class="i"></path><path d="M554 202c2-1 3-3 5-4l-1 3v1h1 1c1 1 1 9 1 11v6l-1 2 1 1-1 2c-2 5-4 9-6 13 0 2-2 6-2 7l-3 10v3c-1 3-2 8-2 10v6l-1 6h0c-1 1-2 3-3 3 0-1 0-2-1-2v-1c0-1 0-1-1-2v-1h0l-2-1c0-1 0-2-1-3v-1h-1-1v3c-2-1-3-2-4-3 1-1 0-4 0-6v-11l1-7v-2l-3-4h-1c0-1 0-1 1-2v-1-4h-1v-2h0c-1-1-1-2-1-3l-1-2h0c-2-1-3-1-4-1h0 2l-1-1h0 5c1 1 1 1 2 1h4 2v-3l1-1c0-1 0-2 1-3v-4-1c1 0 1 0 2-1l1 1v-1c2-2 2-3 4-4l1-1 1 1v-1l-1-1c2 0 2-1 3-1l-2-2 1-1c1 0 1 0 2 1h1 1l1-2z" class="J"></path><path d="M541 257c0-4-1-9 0-13v-1-1h1l1 3h2l1-1h2v1l-2 2v2c-1 0-1 0-2-1h-2v-2l-1 1c0 3 1 7 0 10z" class="D"></path><path d="M553 232l1-1-2-2h1 4c-1 1-2 2-2 3s-1 2-1 3v1c-1 0-1 1-1 2l-1-1c-2 0-2 1-3 2-1 0-2-1-3-1-1-1 0-2 0-2 0-2 1-3 3-4 1 1 1 1 2 1 2 0 1 0 2-1z" class="I"></path><path d="M541 263v1c0 1 1 2 0 3l1 1c1 1 1 2 1 3 2 1 2 2 4 2l-1 6h0c-1 1-2 3-3 3 0-1 0-2-1-2v-1c0-1 0-1-1-2v-1h0v-2-11z" class="B"></path><path d="M547 226c1 1 2 0 2 2v1c2 1 3 1 4 3-1 1 0 1-2 1-1 0-1 0-2-1-2 1-3 2-3 4 0 0-1 1 0 2v1c-1 1-1 1-2 1h-1v-1c0-1-1-1-2-2v-2c2-1 3-1 5-2v-1-2l1-4z" class="K"></path><path d="M546 249l3-2c1 0 1-1 2-3h1l-3 10v3c-1 3-2 8-2 10v6c-2 0-2-1-4-2 0-1 0-2-1-3l-1-1c1-1 0-2 0-3v-1-6c1-3 0-7 0-10l1-1v2h2c1 1 1 1 2 1z" class="q"></path><path d="M543 226l1-1v-4h1s0 1 1 2l-1 1h0c1 1 2 1 2 2l-1 4v2 1c-2 1-3 1-5 2v2c0 2 0 3-1 4v-6h-1c0 4-1 8 0 11l-1 25h-1-1v3c-2-1-3-2-4-3 1-1 0-4 0-6v-11l1-7v-2l-3-4h-1c0-1 0-1 1-2v-1-4h-1v-2h0c-1-1-1-2-1-3l-1-2h0c-2-1-3-1-4-1h0 2l-1-1h0 5c1 1 1 1 2 1h4 2 5 1z" class="O"></path><path d="M543 226l1-1v-4h1s0 1 1 2l-1 1h0c1 1 2 1 2 2l-1 4v2 1c-2 1-3 1-5 2v-1l2-2h0c1-2 1-2 1-4h-5c1 0 3 0 4-1l-1-1h1z" class="d"></path><path d="M524 225h5c1 1 1 1 2 1h4 2 5l1 1c-1 1-3 1-4 1l-3-1c-2 1-3 1-5 1v1l-1 2h1 1v1h-2-1 0c-1-1-1-2-1-3l-1-2h0c-2-1-3-1-4-1h0 2l-1-1h0z" class="L"></path><path d="M527 227h9c-2 1-3 1-5 1v1l-1 2h1 1v1h-2-1 0c-1-1-1-2-1-3l-1-2z" class="e"></path><path d="M531 229h1c1 2 1 2 1 4l1 1v-1c1-1 3-1 4-2l1-1h2 1c-1 1-2 2-2 3-2 0-2 1-3 2 0 1 0 2-1 3 1 2 0 5 1 8-2 0-3 0-4 1v-2l-3-4h-1c0-1 0-1 1-2v-1-4h-1v-2h1 2v-1h-1-1l1-2z" class="U"></path><path d="M530 239c1-1 3-2 3-3h1l1 2h1c1 2 0 5 1 8-2 0-3 0-4 1v-2l-3-4h-1c0-1 0-1 1-2z" class="D"></path><path d="M530 239c1-1 3-2 3-3h1l1 2c-1 0-1 0-2 1-1 2 0 4 0 6l-3-4h-1c0-1 0-1 1-2z" class="i"></path><path d="M533 247c1-1 2-1 4-1v25h-1v3c-2-1-3-2-4-3 1-1 0-4 0-6v-11l1-7z" class="P"></path><path d="M554 202c2-1 3-3 5-4l-1 3v1h1 1c1 1 1 9 1 11v6l-1 2c0 1-1 3-1 4l-1 1c-1 1-1 2-1 3h-4-1l2 2-1 1c-1-2-2-2-4-3v-1c0-2-1-1-2-2 0-1-1-1-2-2h0l1-1c-1-1-1-2-1-2h-1v4l-1 1h-1-5v-3l1-1c0-1 0-2 1-3v-4-1c1 0 1 0 2-1l1 1v-1c2-2 2-3 4-4l1-1 1 1v-1l-1-1c2 0 2-1 3-1l-2-2 1-1c1 0 1 0 2 1h1 1l1-2z" class="t"></path><path d="M558 226h-1c-2-1-1-2-1-3s0-1 1-2c1 1 1 2 2 4l-1 1z" class="d"></path><path d="M550 218h1 1 1v-2c1-1 2-1 3-1v1 3l1 1c-1 1-3 0-4 0s-2-1-3-2z" class="X"></path><path d="M550 218c1 1 2 2 3 2h0c0 2 0 2 1 3h0v1c-2-1-4 0-5 0-1-1-1-2-2-2l2-2h1v-2z" class="d"></path><path d="M552 212c0-2 1-2 2-3 1 0 1 1 3 2v-2c2 1 2 1 3 2l1 2h0v6h-1c-1-1-2 0-2-1l2-2c0-1 0-1-1-2l-3-1h-2-1l-1-1z" class="c"></path><path d="M546 209c0 4-2 9-3 13v4h-1-5v-3l1-1c0-1 0-2 1-3v-4-1c1 0 1 0 2-1l1 1v-1c2-2 2-3 4-4z" class="e"></path><path d="M539 219v-1c1 0 1 0 2-1 1 2 1 3 1 5h1v4h-1-5v-3l1-1c0-1 0-2 1-3z" class="o"></path><path d="M554 202c2-1 3-3 5-4l-1 3v1h1 1c1 1 1 9 1 11h0l-1-2c-1-1-1-1-3-2v2c-2-1-2-2-3-2-1 1-2 1-2 3l-3-3 3-5h1l1-2z" class="W"></path><path d="M557 204c1-1 2-1 3-2 1 2 0 2 0 4-1 1-1 1-3 1v-3z" class="H"></path><path d="M557 207l-1 1-2-1v-1c0-2 1-3 3-4v2 3z" class="B"></path><path d="M489 274c0-1 1-1 2-2 0 2 0 4 1 5 0-1 1-2 1-2h0c1 0 1 0 2 1h1v-1l2-2h0c1 1 1 2 1 2 1 1 1 2 2 2v1 2l1 1v-1c1 0 1-1 2-1h0l-1 1 1 1h1v-2h1l1 1-1 1 1 1h-1-1l3 1h0 12 0 1 6 2c1 2 1 3 1 5 0 0 1 0 1 1 1 1 2 1 2 3l3 1 1 5 3 8c0 2 0 4 1 7v-3c0-1 0-1 1-2l1 2 2 10c1 2 1 4 2 6h2v1 4l-1 8v8l-1-1v7 5l1 1h-2c0 2-1 4 0 7v5l1-4h1l1-2h1v1h6 3l-1 1-3 1v1l5 1h0 5c-2 0-4 0-4 1h-1c-3 0-5 1-7 2l-1 1h-2c-1 3-1 5-1 7v14c0 4 1 8 0 12l2 1c0 2 0 4 1 6l-1 1v8h3l3-1 4 1h0-4c-1 1-2 1-4 1l-4 2v21 29c0 2-1 7 1 8h2 0v1c-1 0-2 1-2 1-1 2-1 3-1 5v3l1 1h1c-1 1 0 1-2 1v6l1 1h1l-2 2 1 1v1 3h1c0-1 0 0 1-1v1h0v1 2c-1 0-1-1-2-2l-1 3-1-1-1 1-1 2h0c-1 2 0 6-1 8v2h0c-1 2-2 4-2 5v3 1h-1l-2 1v-1h-5v1h-2v1h-1c-1 1-1 1-1 2-1-1-1 0-1-1-1 0-3-1-4-1 0 1-1 1-1 1v-1h-3-15s-1 0-2 1h0c-1-1-1-1-2-1h-1-9c-1 1-1 1-2 0-2 0-4 1-7 1 0-2 1-2 0-3h-4c-2-1-2-2-2-4l-1-1c0-1-1-1-2-2v-1c-1-1 0-5 0-6 1-3 0-8 0-11v-2l-3 1v4c0-1 0-3-1-5h-3c-2 1-1 1-2 2 0-1 0-2-1-3l-1-1h3l1-1h-11-8-4-1c-1 0-3 0-4-1v-4l1-12v-19-28-1-2h-1c1-3 1-4 0-6 0-1-1-2-1-3v-4-3h-1l1-1c-1-1-1-2-2-3l-1-1v-1-2c1-2 2-3 3-3h1 1v-7-2c-1-1-2-3-4-3l-1-2v-1c-2-2-5-6-5-9v-2c1-1 1-1 2 0h0v-1l-2-3v-1c-1 0-1 0-1-1h1v-1c0-2-1-3-2-4h0c0-1-1-2-2-3 1 0 2-1 3-1-1-1-1-2-1-3h14l-1-1c2 0 3 0 4-1-1-1 0-3 0-5l1 1 2-2v-1h0c1-1 0-1 2-1v-1h-4c-1 1-5 1-7 1v-6-1-8-1-3l1-2-3-1-2-3h0l1-1c-1-1-1-2-1-3l2-1h0c1 0 1 0 2-1l1-6-1-1c-3 0-5-1-7-3-1-4 1-9-1-13-1-1 0-4 0-6 1 1 1 2 2 3l2-1v1c2 1 3 1 4 1l1 1c0 1 0 2 1 2 0-1 0-2 1-3 0 0 0 1 1 1l2-2h2 3 5 8 4 2l3 1v-1h1v1c1 0 1-1 2-1h2v-4h0l1-1c1-1 2-3 3-4l-2-4v-2-1l1-1v-1-2c1-1 1-3 1-4l1 1h2c1-1 2-1 3-1h0c0-1 0-1 1-2z" class="p"></path><path d="M482 291l2 2v3-1c-2-1-3 0-5 0 1-1 2-3 3-4z" class="Z"></path><path d="M476 380v2c0 5 1 9 1 13v3h3 1l1-1 2 2h-1c-2 0-5 0-7 1h-1v-1h2v-1h-2c-1-2 0-4 0-5l1-13z" class="R"></path><path d="M549 407l2 1c0 2 0 4 1 6l-1 1v8l-1 1-1-1v-16z" class="m"></path><path d="M548 367l1-2h1v1h6 3l-1 1-3 1v1l5 1h0c-3 0-9 1-12-1l2-2h-2z" class="O"></path><path d="M556 366h3l-1 1-3 1v1h-2l3-3z" class="i"></path><path d="M450 372l2 1c-1 0-2 1-2 1v1 1c1 0 1 0 2 1h-1v1c-1 0-2 0-2-1-2 0-2-1-4 0v2c0 1 1 2 1 3v3l-2-3v5 1c-2 2-2 4-2 6 0 1 0 1-1 1v-5c-1-2-1-4-1-6 1 1 1 1 1 2v1h1v-4l1-1c0-1 1-5 1-6h-2v-1l1-1 1 1 1-1h1 1c0-1 2-1 3-2z" class="O"></path><path d="M445 379c0 1 1 2 1 3v3l-2-3v-1l1-2z" class="m"></path><path d="M476 432c2 4 0 9 1 13v20c0 4 1 7 0 10l-1 1v1h-1v-30c0-5 0-10 1-15z" class="R"></path><path d="M450 372l1-2c-3-1-7 1-9-1-2 0-11 1-12 0 2-1 3 0 4-1 2 0 5-1 7 0 2 0 7 1 9 0h2 14l1 1c-2 2-4 0-5 1-2 0-3 0-4 1l1 1 3 1h3c-4 1-8 0-13 0l-2-1z" class="M"></path><path d="M463 422l2 1c1 0 2 1 2 1h1c1 0 2 1 3 2 1 4 0 7 1 11-2 1-3 0-5 0h0v-11l-4-1h-3v-1c-2 0-3 1-5 0h-2c-1 0-3 0-4 1l-1-1h-1c0 1 0 1-1 2h0c0-1 0-1-1-2h-1 0l-1-1h17l3-1z" class="Y"></path><path d="M508 283h12l-1 1h-3c-1 0-1 0-1 1h0-1v-1l-1 1-1-1-1-1v1 1h0c-1-1-1 0-2-1 0 2 0 5-1 6h-1l1-2-1-1-1 1-1-1v-1-1c-1 1-1 1-1 2-1-1-1 0-2-1v2c-1-1-1-2-1-3l-1 1v4h-1v-4c-1 1-1 3-1 4-2-1-1-2-1-4-2 1-1 2-1 4l-1-1-1-4h0 0-1-1l-1-1-1 1h-1v-1c-1 1-1 1-2 1l-1-1h1c-1-1-2-1-3-1-2 1-3 1-4 1l1-1h18 9z" class="U"></path><path d="M520 283h0 1 6 2c1 2 1 3 1 5 0 0 1 0 1 1 1 1 2 1 2 3h-1-13c2-2 2-3 2-5-1 0-1 0-1 1l-2-2h-1 0v1 1c-1-1-1-2-1-4h3l1-1z" class="M"></path><path d="M520 283h0 1 6c0 2 0 3-1 5-1 0 0-2-2-1v1c-1-1 0-2-1-3-1 1-1 2-2 2s-1 0-1 1l-2-2h-1 0v1 1c-1-1-1-2-1-4h3l1-1z" class="O"></path><path d="M460 425h3l4 1v11h-4-9l3-8h0c1-1 2-3 3-4z" class="K"></path><path d="M520 481h20 5 1c1 5 0 10 0 15v2 1h-7l-4-1c3-1 3 0 6-1l4-1c-2 0-9 1-11-1h-1-4l-1-1c3 1 5 0 7 1 2-1 4 0 5-1v-2c-1-1-1-1-1-2v-5h-10-1v-1h11 5l-1-1h-2c-1 0-1 0-1-1h-1-1c-5 0-12 0-17-1h-1z" class="g"></path><path d="M539 485h4c1 1 1 1 1 2v8c-3 0-6 1-9 0 2-1 4 0 5-1v-2c-1-1-1-1-1-2v-5z" class="Q"></path><path d="M483 325h-5c0-2 1-5 1-7l5-18c1-3 1-6 3-8h3l-6 27h0v3c0 1 0 1-1 2v1z" class="b"></path><path d="M489 274c0-1 1-1 2-2 0 2 0 4 1 5 0-1 1-2 1-2h0c1 0 1 0 2 1h1v-1l2-2h0c1 1 1 2 1 2 1 1 1 2 2 2v1 2l1 1v-1c1 0 1-1 2-1h0l-1 1 1 1h1v-2h1l1 1-1 1 1 1h-1-1l3 1h0-9-18v-1-2c1-1 1-3 1-4l1 1h2c1-1 2-1 3-1h0c0-1 0-1 1-2z" class="K"></path><path d="M489 274c0-1 1-1 2-2 0 2 0 4 1 5h0c-1 2-1 3-1 4h-1c0-1 0-2 1-3h0l-1-1-1 1-1-1v-1c0-1 0-1 1-2z" class="k"></path><path d="M482 276l1 1h2c1-1 2-1 3-1l-1 1h-2c0 1 0 1-1 2v2h1v-1c1-1 0-1 1-1h1l-1 1v2h-5v-2c1-1 1-3 1-4z" class="o"></path><path d="M496 275l2-2h0c1 1 1 2 1 2 1 1 1 2 2 2v1c-1 1-1 2-2 2v-1h-1v2h-1v-1s-1-1-1-2v-2-1z" class="T"></path><path d="M493 275h0c1 0 1 0 2 1h1v2c0 1-1 1-1 2l-1-1-1 1c1 1 1 1 2 1-2 2-4 1-7 1l-1-1 1-1c1 1 1 1 2 1h1c0-1 0-2 1-4h0c0-1 1-2 1-2z" class="X"></path><path d="M479 295c2 0 3-1 5 0v1l-5 18c0 3-2 6-3 9 0 1 0 3-1 4-1-1-1-3-1-4v-8c-1-4 0-11-1-14v-1h1v1c1 0 1-1 2-1h2v-4h0l1-1z" class="e"></path><path d="M430 370c3 1 5 2 7 4v1l2 4c1 2 1 4 1 5 0 2 0 4 1 6v5 7c-1-1-2-3-4-3l-1-2v-1c-2-2-5-6-5-9v-2c1-1 1-1 2 0h0v-1l-2-3v-1c-1 0-1 0-1-1h1v-1c0-2-1-3-2-4h0c0-1-1-2-2-3 1 0 2-1 3-1z" class="I"></path><path d="M429 374c2 0 4 3 6 4 1 1 2 1 2 2h0-1l-2 1 1 3c-1-1-3-4-4-4s-1 0-1-1h1v-1c0-2-1-3-2-4z" class="E"></path><path d="M436 397l2-1h1v-3c-2-2-3-5-5-7 1 1 2 1 3 2l2 2v-3-1c-1-1-1-2-1-2v-1c0-1 1-1 1-2l-1-1 1-1c1 2 1 4 1 5 0 2 0 4 1 6v5 7c-1-1-2-3-4-3l-1-2z" class="X"></path><path d="M529 485h10v5c0 1 0 1 1 2v2c-1 1-3 0-5 1-2-1-4 0-7-1-4-1-9 0-13 0v-9h13 1z" class="w"></path><path d="M498 481h22 1c5 1 12 1 17 1h1 1c0 1 0 1 1 1h2l1 1h-5-11v1h1-1-13v9h-2-8-9v-11c3 0 4 1 6-1h-3l-1-1z" class="H"></path><path d="M502 482h4 2v1 1l-3-1-1 1v1c1 1 1 1 0 2v1c1 2 1 4 1 6h-9v-11c3 0 4 1 6-1z" class="B"></path><path d="M478 330c1 0 3 1 4 1v10 24 23c0 3 1 6 0 9l-1 1h-1-3v-3c0-4-1-8-1-13v-2l1-49 1-1z" class="i"></path><path d="M477 395c0-2 0-5 1-7v-10-30-14l1-1c1 5 0 10 0 15 0 3 1 5 0 7 0 2 0 3 1 4h0c1 4 0 7 0 11v21c0 2-1 5 0 7h-3v-3z" class="Z"></path><path d="M480 359v-27c1 3 1 6 2 9v24 23c0 3 1 6 0 9l-1 1h-1c-1-2 0-5 0-7v-21c0-4 1-7 0-11z" class="m"></path><path d="M482 365v23c0 3 1 6 0 9l-1 1 1-17c0-4-1-10 0-14v-2z" class="j"></path><path d="M499 291l4 1c-3 2-3 9-4 13v7h-1c0 4 0 7-1 11l1 1h1v1h-2 0-7-7v-1c1-1 1-1 1-2v-3h0l6-27h1 7l1-1z" class="X"></path><path d="M498 292l1-1-2 2v6c-1 2-2 5-1 7h-1l-1 1c0 1 0 2-1 4l-1-1 2-12c0-2 0-4 1-6h3z" class="I"></path><path d="M492 310l1 1c1-2 1-3 1-4l1-1-1 17 1 1 2 1h0-7c0-2 1-4 1-6 1-3 1-6 1-9z" class="h"></path><path d="M499 291l4 1c-3 2-3 9-4 13v7h-1c0 4 0 7-1 11l1 1h1v1h-2l-2-1-1-1 1-17h1c-1-2 0-5 1-7v-6l2-2z" class="J"></path><path d="M496 306c-1 2-1 6 0 8 0-1 0-1 1-1v2c-1 1 0 1 0 2h0v-1c0-1 0-1 1-2v-2c0 4 0 7-1 11l1 1h1v1h-2l-2-1-1-1 1-17h1z" class="Q"></path><path d="M480 416h1c0 2 0 3 1 5h0c0-2 0-5 1-7l1 7 1 56-2-1-6 1v-2c1-3 0-6 0-10v-20c-1-4 1-9-1-13 0-2 0-5 1-7v-10l3 1z" class="j"></path><path d="M477 415l3 1v21c-2-3-1-9-1-12h-1-1v-10z" class="e"></path><path d="M476 432c0-2 0-5 1-7h1 1c0 3-1 9 1 12 0 5 0 30-2 33 0 2 0 5 1 6h1 1v-1l1-10h1v11l-6 1v-2c1-3 0-6 0-10v-20c-1-4 1-9-1-13z" class="Z"></path><path d="M476 432c0-2 0-5 1-7h1l-1 11c0 3 1 6 0 9-1-4 1-9-1-13z" class="O"></path><defs><linearGradient id="i" x1="551.695" y1="443.102" x2="535.131" y2="447.194" xlink:href="#B"><stop offset="0" stop-color="#3b3c32"></stop><stop offset="1" stop-color="#575451"></stop></linearGradient></defs><path fill="url(#i)" d="M544 415l2 1v61h-6l-3-1-2-15v-18c0-7-1-15 0-22h1v-5l1-1 1 1v2c0-1 0-1 1-2v-1l1 1v5-1c0-1 0-2 1-4v-1h3z"></path><path d="M541 416c1 2 0 9 0 12l1 10v23c0 4 0 9-1 14v1l-1 1-3-1h2v-2-5c1-3 1-5 1-8v-14-11c-1-4-2-11 0-15v-1c0-1 0-2 1-4z" class="n"></path><path d="M540 447l1 28v1l-1 1-3-1h2v-2-5c1-3 1-5 1-8v-14z" class="K"></path><path d="M535 421h1v-5l1-1 1 1v2c0-1 0-1 1-2v-1l1 1v5c-2 4-1 11 0 15v11 14c0 3 0 5-1 8v5 2h-2l-2-15v-18c0-7-1-15 0-22z" class="c"></path><defs><linearGradient id="j" x1="544.619" y1="464.238" x2="529.208" y2="453" xlink:href="#B"><stop offset="0" stop-color="#aba19a"></stop><stop offset="1" stop-color="#c0c1aa"></stop></linearGradient></defs><path fill="url(#j)" d="M535 443v-1c2-2 1-9 1-11h1l1 18v21c1 1 0 3 1 4v2h-2l-2-15v-18z"></path><path d="M470 300l3 1c1 3 0 10 1 14v8c0 1-1 2-1 3v3c1 2 0 4 1 6 1 4-1 8-1 12v11c-3-1-6-1-8 0v1h1s1 0 2 1v1 4 1h2 3v1h-22-8l-1-1c2 0 3 0 4-1-1-1 0-3 0-5l1 1 2-2v-1h0c1-1 0-1 2-1v-1h-4c-1 1-5 1-7 1v-6-1-8-1h9 16 0c2 0 5 0 6-1v-1c1-1 1-2 1-3-1-4-1-9-1-13 0-2 1-4 0-5-1-6 1-12-1-18z" class="S"></path><path d="M469 354v-2c-1-1-1 0-1-1-1-1-1-2-2-3s-1-1-1-2c1 0 1 0 2-2h1v1 1 1h0c1 1 2 2 2 3h1v4l-1 1-1-1z" class="M"></path><path d="M440 350c1-1 1-2 1-3 0-2 0-2 1-4 2-1 4-1 6-1-1 1-1 2-2 3v2c-1 1 0 2 0 3s0 1-1 2c1 0 1 0 2-1v1c-1 1-2 1-2 2-1 0-3 1-4 0v-3h-1v-1z" class="k"></path><path d="M448 342c5 0 10 0 14 1l-3 3h-2c-1 1-1 1-1 2-2 0-3 0-4-1-1 0-1 0-2-1h0-2c-1 0-1 0-2-1 1-1 1-2 2-3z" class="D"></path><path d="M462 343h7c-1 1 0 1-1 1h-1c-1 2-1 2-2 2 0 1 0 1 1 2s1 2 2 3c0 1 0 0 1 1v2h-9l1-2c-2-1-1-2-2-3 0-1 0-1 1-2l-1-1 3-3z" class="K"></path><path d="M445 354c0-1 1-1 2-2v-1c-1 1-1 1-2 1 1-1 1-1 1-2s-1-2 0-3v-2c1 1 1 1 2 1h2 0c1 1 1 1 2 1 1 1 2 1 4 1 0-1 0-1 1-2h2l1 1c-1 1-1 1-1 2 1 1 0 2 2 3l-1 2h-1-14z" class="f"></path><path d="M445 354c0-1 1-1 2-2v-1c-1 1-1 1-2 1 1-1 1-1 1-2s-1-2 0-3v-2c1 1 1 1 2 1h2c-1 1-2 2-2 3v1h2c1 1 1 1 1 2 2 1 2 1 3 0h1v-2h1v1 1c1 0 3 0 3 1v1h-14z" class="I"></path><path d="M453 359h3 1c1 1 1 1 2 1l1-1c1 2 0 4 0 6h1v-1c0-1 1-1 2-2l-1-1h0-1c1-1 2-2 4-1h1c0 2 0 3-1 4v1l2 1v-3-2l1-1v1 4 1h2 3v1h-22-8l-1-1c2 0 3 0 4-1-1-1 0-3 0-5l1 1 2-2h4z" class="r"></path><path d="M449 359h4c-1 0-1 1-2 1 0 1 0 1 1 1 0 2 0 3-1 4h1c1 0 0-1 2-1l1 1h-3c-1 0-1 0-2 1l-2-2h0c-1 1-1 1-2 1-1-1 0-3 0-5l1 1 2-2z" class="X"></path><path d="M475 486v-5h10 7 6l1 1h3c-2 2-3 1-6 1v11h9 8 2c4 0 9-1 13 0l1 1h4 1c2 2 9 1 11 1l-4 1c-3 1-3 0-6 1h-4c-1 1-1 0-2 0-2 1-5 0-7 0h-10-1v4 1 2h-1l-1 2h-2c-2 0-2-1-2-2l-2 2c-2-1-3-1-5-1h-14-8l-1-1-1 1v1h-2v-2-2-1l-1-1v-1c0-2 0-4 1-6h0c-1-1-1-1-1-2v-2l-1-1s0-2-1-2-1 1-3 0v-1c0-1-1-1-1-2h0l6 1 1 1h3z" class="j"></path><path d="M484 494v-2h0v-7h-3l1-1h4l1 10h-3z" class="l"></path><path d="M486 484c1-1 2-1 4 0v1c0 2 0 4-1 7v2h-2l-1-10z" class="d"></path><path d="M492 481h6l1 1h3c-2 2-3 1-6 1v11h-4-3v-2c1-3 1-5 1-7v-1l1-1h4l-7-1h-1c2 0 3-1 5-1z" class="T"></path><path d="M490 485h2c0 2 1 7 0 9h-3v-2c1-3 1-5 1-7z" class="c"></path><path d="M475 486v-5h10-1c-1 0-4 0-6 1 1 1 1 1 2 1h1v5 6h3 3 2 3 4 9 8c-4 1-9 1-13 1h-13l1 1h4l-1 1c-1 0-1 0-2 1-3 1-7 0-9 0h-5l-1-1c1-2 1-2 2-2l1-1-2-1c0-2 0-3 1-5v-1l-1-1z" class="O"></path><path d="M476 495h5 6l1 1h4l-1 1c-1 0-1 0-2 1-3 1-7 0-9 0h-5l-1-1c1-2 1-2 2-2z" class="e"></path><path d="M513 494h2c4 0 9-1 13 0l1 1h4 1c2 2 9 1 11 1l-4 1c-3 1-3 0-6 1h-4c-1 1-1 0-2 0-2 1-5 0-7 0h-10-1-8-14c1-1 1-1 2-1l1-1h-4l-1-1h13c4 0 9 0 13-1z" class="L"></path><path d="M465 484l6 1 1 1h3l1 1v1c-1 2-1 3-1 5l2 1-1 1c-1 0-1 0-2 2l1 1h5c2 0 6 1 9 0h14 8v4 1 2h-1l-1 2h-2c-2 0-2-1-2-2l-2 2c-2-1-3-1-5-1h-14-8l-1-1-1 1v1h-2v-2-2-1l-1-1v-1c0-2 0-4 1-6h0c-1-1-1-1-1-2v-2l-1-1s0-2-1-2-1 1-3 0v-1c0-1-1-1-1-2h0z" class="u"></path><path d="M477 505l-2-1c-1-2-1-2 0-3h2c0 1 0 2 1 3 1-1 1-1 3-1 0-1 0-1 1-2h3c1 0 1 1 2 1s1 1 2 1v1h3c-2 1-12 1-15 1h0zm26-7h8v4 1c-1-1-1-1-1-2h-6c-2-1-7-1-9-1l1-1h-6c-3 0-7 0-10-1 2 0 6 1 9 0h14z" class="M"></path><path d="M503 498h8v4 1c-1-1-1-1-1-2l-1-1c-3 0-3 0-6-2z" class="R"></path><path d="M504 501h6c0 1 0 1 1 2v2h-1l-1 2h-2c-2 0-2-1-2-2l-2 2c-2-1-3-1-5-1h-14-8l-1-1h2 0c3 0 13 0 15-1h-3v-1h0c1 0 2 0 3-1 2-1 4-1 6-1 2 1 4 0 6 0z" class="o"></path><path d="M489 418v-3c1 0 0 0 1-1h0v3h0l1 1c0-2 0-2 1-4 0 1 0 2 1 3h0c0-1 0-2 1-3 1 1 1 3 1 4l1-1v-2l1 1h1l1-1 1 1v21 5 26c0 2 1 6 0 7 0 1 0 1-1 1h-1c-3 1-6 0-9 0l-4 1-1-56v-5h3 1v2h1z" class="k"></path><path d="M496 417v-2l1 1h1l1-1 1 1v21h-3-1-1l1-20z" class="E"></path><path d="M496 417v-2l1 1h1v4c-1 5-1 12-1 17h-1-1l1-20z" class="I"></path><path d="M487 416h1v2h1v13c-1 4-1 9 0 13v10 22l-4 1-1-56v-5h3z" class="n"></path><path d="M487 416h1v2 1c-2 3 0 10-1 14h-1v-13c1-2 1-3 1-4z" class="b"></path><path d="M500 437v5 26c0 2 1 6 0 7 0 1 0 1-1 1h-1-1-6c0-9 1-17 0-26 0-3-1-7 0-9h-1l1-1c1-1 1-1 3-1 1 0 3 1 4 0l-2-2h1 3z" class="K"></path><path d="M500 437v5 26c0 2 1 6 0 7 0 1 0 1-1 1h-1-1c-2-6-1-14-1-20v-11-1l1-1-2-1h0c1-1 0-1 1-1v-1c-1 0-1 0-2-1h0c1 0 3 1 4 0l-2-2h1 3z" class="Q"></path><path d="M496 445l2 1v20c0 3-1 7 0 10h-1c-2-6-1-14-1-20v-11z" class="I"></path><defs><linearGradient id="k" x1="505.464" y1="447.181" x2="498.541" y2="446.32" xlink:href="#B"><stop offset="0" stop-color="#a6a692"></stop><stop offset="1" stop-color="#c3bfc1"></stop></linearGradient></defs><path fill="url(#k)" d="M500 416v-1h2l2 2v-2h1v1h1v-1h5 1v1l1 1v-2h1c1 1 1 2 1 2v1c1 2 2 12 0 15l1 43h-3 0-9-1-4c1 0 1 0 1-1 1-1 0-5 0-7v-26-5-21z"></path><path d="M500 442h1c1 11 0 23 0 34h2-4c1 0 1 0 1-1 1-1 0-5 0-7v-26z" class="K"></path><path d="M500 416v-1h2c-1 3-1 7-1 10v17h-1v-5-21z" class="k"></path><path d="M504 450c1 1 1 8 1 10h1 2v-2l1-1c0 4 0 7 1 10v8c1 1 1 1 3 1h0 0-9v-26z" class="f"></path><path d="M505 416h1v-1h5v2h-2c-1 0-1 0-2 2h0v2l1 10c-1 5-2 13 0 18 1 2 1 6 1 8l-1 1v2h-2-1c0-2 0-9-1-10v-22-11-2h1v1z" class="B"></path><path d="M504 417v-2h1v1c1 5 1 10 1 14v30h-1c0-2 0-9-1-10v-22-11z" class="G"></path><path d="M511 415h1v1l1 1v-2h1c1 1 1 2 1 2v1c1 2 2 12 0 15l1 43h-3 0c-2 0-2 0-3-1v-8c-1-3-1-6-1-10 0-2 0-6-1-8-2-5-1-13 0-18l-1-10v-2h0c1-2 1-2 2-2h2v-2z" class="H"></path><path d="M513 417v-2h1c1 1 1 2 1 2-1 4 0 8-2 11v-11h0zm-2-2h1v1c-1 2 0 6 0 9-1 3-1 6-1 9v-6h0l-1 6h0l1-17v-2z" class="C"></path><path d="M508 431l-1-10v-2h0c1-2 1-2 2-2h2l-1 17c0 5 0 11 1 16v9c0 1 0 1 1 2 1 3 1 11 1 15-2 0-2 0-3-1v-8c-1-3-1-6-1-10 0-2 0-6-1-8-2-5-1-13 0-18z" class="G"></path><path d="M508 431l-1-10v-2h0c1-2 1-2 2-2v28h-1v-14z" class="N"></path><path d="M508 398h1s1 1 2 1h7v1h-1c1 1 2 1 3 1 2 1 4 1 6 1h0 3c1-1 3 0 4-1 1 0 1-1 2-2h2 3c2 1 4 0 6 1v1l-1 1v5s1 1 1 2c1 0 2 1 1 2v1h-7-1 0c2 1 5 0 6 2l-1 1h-3v1c-1 2-1 3-1 4v1-5l-1-1v1c-1 1-1 1-1 2v-2l-1-1-1 1v5h-1v-5h-1v4h-1v-4h-2-2 0c-1-1-2 0-3 0l-2-1h0-1-1l-2 1c-1-1-2-1-3-1h-1c0 1 0 2-1 3v-1s0-1-1-2h-1v2l-1-1v-1h-1-5v1h-1v-1h-1v2l-2-2h-2v1l-1-1-1 1h-1l-1-1v2l-1 1c0-1 0-3-1-4-1 1-1 2-1 3h0c-1-1-1-2-1-3-1 2-1 2-1 4l-1-1h0v-3h0c-1 1 0 1-1 1v3h-1v-2h-1-3v5l-1-7c-1 2-1 5-1 7h0c-1-2-1-3-1-5h-1l-3-1h-1c-1 1-1 1-1 2-1-2-1-5-1-8v-3c1-2 0-3 1-5v-1h1c2-1 5-1 7-1h1 5 7 1 5 5l1-1z" class="V"></path><path d="M498 407c1-1 1-4 1-5h2c0 1 0 2 1 3l-1 3-1 1-1-1h-1v-1z" class="M"></path><path d="M501 402h3 2 2c1 1 0 4 0 6h-4-1c-1-1-1-1-1-3-1-1-1-2-1-3z" class="O"></path><path d="M520 401c2 1 4 1 6 1v5l-1 3c0-1 0-1-1-2h-1-2 0c-2-2-1-4-1-7z" class="m"></path><path d="M489 399h7 1 0l2 1v1h-1-3l3 1v5 1 1h-1c0-1 0-1-1-2l-1 2c-1-1-1-1-1-2l-1 1-1 1c-1-1-2-1-4-1h-1c-1 0-1 0-2 1h-3-1-2-2l1 1h4c1 1 1 1 2 1h16v3c-1 1 0 1-1 1l-1 1h-1l-1-1v2l-1 1c0-1 0-3-1-4-1 1-1 2-1 3h0c-1-1-1-2-1-3-1 2-1 2-1 4l-1-1h0v-3h0c-1 1 0 1-1 1v3h-1v-2h-1-3v5l-1-7c-1 2-1 5-1 7h0c-1-2-1-3-1-5h-1l-3-1h-1c-1 1-1 1-1 2-1-2-1-5-1-8v-3c1-2 0-3 1-5v-1h1c2-1 5-1 7-1h1 5z" class="R"></path><path d="M475 400h1c2-1 5-1 7-1h-1c0 1-1 1-2 1v1h3 0 0c-2 1-6 1-8 0v-1z" class="K"></path><path d="M489 399h7 1 0l2 1v1h-1-3l-1 1h-4l-7-1h0-3v-1c1 0 2 0 2-1h1 1 5z" class="I"></path><path d="M537 399h3c2 1 4 0 6 1v1l-1 1v5s1 1 1 2c1 0 2 1 1 2v1h-7-1 0c2 1 5 0 6 2l-1 1h-3v1c-1 2-1 3-1 4v1-5l-1-1v1c-1 1-1 1-1 2v-2l-1-1-1 1v5h-1v-5h-1v4h-1v-4h-2-2 0c-1-1-2 0-3 0l-2-1h0-1-1l-2 1c-1-1-2-1-3-1h-1c0 1 0 2-1 3v-1s0-1-1-2h-1v2l-1-1v-1h-1-5v1h-1v-1h-1v2l-2-2h-2v1l-1-1c1 0 0 0 1-1v-3l17 1 10-1c0-2 0-2-1-4v-5h0 3c1-1 3 0 4-1 1 0 1-1 2-2h2z" class="R"></path><path d="M532 403h1v3l-1 1-1-3 1-1zm9-1h2v4h0-2v-1c-1-1-1 1-2 1h0c0-2 1-3 2-4z" class="M"></path><path d="M543 402h2v5s1 1 1 2l-3-1v-2-4z" class="S"></path><path d="M537 399h3c2 1 4 0 6 1v1l-1 1h-2-2-10-2c1-1 3 0 4-1 1 0 1-1 2-2h2z" class="E"></path><path d="M526 402h3 2l1 1-1 1v4c3 0 6 1 9 1 1-1 2 0 3 0l-1 1h0-2v1h2l1-1h1 0l1-1v1 1h-1c-1 1-3 0-4 1h-1c-7-1-15 1-22 0l10-1c0-2 0-2-1-4v-5h0z" class="g"></path><path d="M529 402h2l1 1-1 1v4l-1 1-1-1v-6z" class="i"></path><path d="M500 411l17 1c7 1 15-1 22 0h0c2 1 5 0 6 2l-1 1h-3v1c-1 2-1 3-1 4v1-5l-1-1v1c-1 1-1 1-1 2v-2l-1-1-1 1v5h-1v-5h-1v4h-1v-4h-2-2 0c-1-1-2 0-3 0l-2-1h0-1-1l-2 1c-1-1-2-1-3-1h-1c0 1 0 2-1 3v-1s0-1-1-2h-1v2l-1-1v-1h-1-5v1h-1v-1h-1v2l-2-2h-2v1l-1-1c1 0 0 0 1-1v-3z" class="u"></path><path d="M517 415c1 0 2 0 3 1l2-1h1 1 0l2 1c1 0 2-1 3 0h0 2 2v4h1v-4h1v5c-1 7 0 15 0 22v18l2 15h-24 0 3l-1-43c2-3 1-13 0-15 1-1 1-2 1-3h1z" class="V"></path><path d="M531 428v-5l1-1c1 2 1 43 1 44h-1v-9-6c-1-7-1-15-1-23zm3-8v-4h1v5c-1 7 0 15 0 22v18c-1-2-1-5-1-7-1-9 0-17 0-25v-9z" class="N"></path><path d="M530 426l1 2c0 8 0 16 1 23v6l-1-1c-1 4 0 9-1 13v3l-1 1c-1-6 0-14 1-20v-23-4z" class="L"></path><path d="M526 416c1 0 2-1 3 0 0 2 0 4 1 6v4 4 23c-1 6-2 14-1 20v3h-5v-1-13-47l2 1z" class="P"></path><path d="M524 415l2 1-1 59h-1v-13-47z" class="f"></path><path d="M517 415c1 0 2 0 3 1l2-1h1 1 0v47 13 1h-8l-1-43c2-3 1-13 0-15 1-1 1-2 1-3h1z" class="q"></path><path d="M517 415c1 0 2 0 3 1l2-1h1 1 0v47c-1-4 1-10-2-13-1-1-1 1-3 1-2-7-1-17-1-25-1-1-1-4-1-5-1-2 0-3 0-5zm-13-123c4-1 10-1 15 0h0 13 1l3 1 1 5c1 3 2 5 3 8 0 2 0 4 1 7 0 4 1 9 3 13l-5-1h0-4-7-4-12-6-7v-1h-1l-1-1c1-4 1-7 1-11h1v-7c1-4 1-11 4-13h1z" class="P"></path><path d="M519 292h13 0v1h-3-1c-1 1-1 4-1 6l1 1c-1-1-1-2-1-3-1-2 0-2-2-3h0-1l-1 1h-1v-2l-3-1h0z" class="L"></path><path d="M533 292l3 1 1 5v8 3l-1 2v1l-2-6-2-13v-1h0 1z" class="E"></path><path d="M533 292l3 1 1 5v8c-2-4-2-8-3-13l-2-1h0 1z" class="K"></path><path d="M537 298l3 8c0 2 0 4 1 7 0 4 1 9 3 13l-5-1h0-4l3-1-2-12v-1l1-2v-3-8z" class="X"></path><path d="M537 309c1 3 2 6 2 10 1 2 1 4 1 6h-1 0-4l3-1-2-12v-1l1-2z" class="a"></path><path d="M521 304c0-1 0-1-1-1 0-1-1-1-1-2v-1h1l3 3h0c2 3 3 11 2 15-1-1-1-3-1-4h-1v6 1l-2-1h1v-5l-1-1c0 2 0 7-1 8v2h1c0-1 0-1 1-2 1 1 1 2 2 3h-12c-1-1-1-2-1-2h1c3-2 2 0 4 0l1-8c1-1 1-2 2-2h-1v-2l-1-1c1-1 0-2 0-4l2-2h2z" class="F"></path><path d="M519 304h2v1c1 2 1 2 1 4-1 1-2 2-3 2h-1l-1-1c1-1 0-2 0-4l2-2z" class="C"></path><path d="M504 292c4-1 10-1 15 0l3 1v2h1c0 3 1 5 0 8h0l-3-3h-1v1c0 1 1 1 1 2 1 0 1 0 1 1h-2l-2 2c0 2 1 3 0 4l1 1v2h1c-1 0-1 1-2 2l-1 8c-2 0-1-2-4 0h-1s0 1 1 2h-6-7v-1h-1l-1-1c1-4 1-7 1-11h1v-7c1-4 1-11 4-13h1z" class="G"></path><path d="M516 309h0c-1-2-1-4 1-5h2l-2 2c0 2 1 3 0 4l-1-1z" class="s"></path><path d="M505 292h1l-1 12c-1 1-1 2-2 3v3l-1 1c0-2 0-3 1-4 1-5 0-10 2-15zm-2 0h1c0 2-1 3-1 5v4l-1 1c0 2-1 4-1 6-1 1-1 2-1 3h0v-1c0-2 0-3-1-5 1-4 1-11 4-13z" class="N"></path><path d="M498 312h1c1 2 0 5 2 8h-1v3h1c2-2 1-5 2-8v-2c0 3 0 9 1 11h0 2v1h-7v-1h-1l-1-1c1-4 1-7 1-11z" class="D"></path><path d="M507 317c1-3 0-8 0-12 0-3-1-11 2-13v1c1 3 1 7 1 11h0l1 19s0 1 1 2h-6v-1h-2c2-3 1-9 2-12h0l1 5z" class="P"></path><path d="M510 304l1 19s0 1 1 2h-6v-1h-2c2-3 1-9 2-12h0l1 5c-1 2-1 4 0 6h2l1-19h0z" class="L"></path><path d="M510 304h1c0-3-1-10 1-12h2c1 1 0 1 2 1h0 1c0 2 1 4 0 6-1 3-3 7-2 10v1l1-1 1 1 1 1v2h1c-1 0-1 1-2 2l-1 8c-2 0-1-2-4 0h-1l-1-19z" class="C"></path><path d="M516 309l1 1 1 1v2h1c-1 0-1 1-2 2h0l-1-1c-1-1-1-2-1-4l1-1z" class="P"></path><path d="M432 297c1 1 1 2 2 3l2-1v1c2 1 3 1 4 1l1 1c0 1 0 2 1 2 0-1 0-2 1-3 0 0 0 1 1 1l2-2h2 3 5 8 4 2c2 6 0 12 1 18 1 1 0 3 0 5 0 4 0 9 1 13 0 1 0 2-1 3v1c-1 1-4 1-6 1h0-16-9v-3l1-2-3-1-2-3h0l1-1c-1-1-1-2-1-3l2-1h0c1 0 1 0 2-1l1-6-1-1c-3 0-5-1-7-3-1-4 1-9-1-13-1-1 0-4 0-6z" class="n"></path><path d="M441 320v11c-1 1-2 2-3 2v-1-4-1c1 0 1 0 2-1l1-6z" class="D"></path><path d="M436 332l1-1c-1-1-1-2-1-3l2-1h0v1 4 1c1 0 2-1 3-2v5l-3-1-2-3h0z" class="I"></path><path d="M448 300h3v2h-1c0 3 0 4 1 6v4c-1 1-1 1-2 1v1h2c1 1 1 1 2 1s2 0 3 1h-1-7-3l-1-1h2v-8l-1-1c0-1 1-2 1-2v-4h2z" class="c"></path><path d="M448 300h3v2h-1c0 1-1 2-1 4-1 1 0 3-1 5h0v-11z" class="V"></path><path d="M448 311c1-2 0-4 1-5 0-2 1-3 1-4 0 3 0 4 1 6v4c-1 1-1 1-2 1v1h2c1 1 1 1 2 1s2 0 3 1h-1-7c1-2 1-3 0-5z" class="L"></path><path d="M436 300c2 1 3 1 4 1l1 1c-1 5 0 10-1 14h-1v1 1c-2-1-3-1-5-2-1-3 0-11 0-14 0-1 1-1 2-2z" class="W"></path><path d="M456 316h1 8l2 1v1h-1v1c-1 2 0 5-1 7 0 2 0 5-1 6h-2l2 1h-7l1 1h0c-1 1 0 1-1 2h1c0 1 0 0 1 1v1c1 0 1 0 2 1h-11l-1-1c-1 0-1 1-2 0v-5-1l-1-1v-6c0-2 0-5 1-7h0 1c2-1 5-1 6-1h0l1-1h1z" class="I"></path><path d="M449 338c-1 0-1 1-2 0v-5l1 1 1-1v1h2l1 3h0c-2 0-2 1-3 1z" class="a"></path><path d="M456 316h1l1 1c0 1-1 1-1 2h0c0 1 0 2-1 3v7c1 1 2 2 2 3h-4-1c-2 0-2 0-3-1v-1l-1-2c0-3 0-7-1-10 2-1 5-1 6-1h0l1-1h1z" class="G"></path><path d="M454 331l-2-1v-4h1c0-2 1-5 1-7v12z" class="D"></path><path d="M456 316h1l1 1c0 1-1 1-1 2h0c0 1 0 2-1 3v7c1 1 2 2 2 3h-4v-1-12-2h0l1-1h1z" class="Z"></path><path d="M457 316h8l2 1v1h-1v1c-1 2 0 5-1 7 0 2 0 5-1 6h-2 0-4c0-1-1-2-2-3v-7c1-1 1-2 1-3h0c0-1 1-1 1-2l-1-1z" class="i"></path><path d="M457 319l1 1c0 1 0 0 1 1h1v3h0-2v4h1v-3h1c1 1 1 2 2 3 0 1-1 1-1 2v1h1v1h-4c0-1-1-2-2-3v-7c1-1 1-2 1-3z" class="o"></path><path d="M456 300h8 4 2c2 6 0 12 1 18 1 1 0 3 0 5 0 4 0 9 1 13 0 1 0 2-1 3v1c-1 1-4 1-6 1h0c-2-1-2-1-3-1l-1-1h3v-1h-3c0-2-1-2-1-4h-2l-1-1h7l-2-1h2c1-1 1-4 1-6 1-2 0-5 1-7v-1h1v-1l-2-1h-8-1c-1-1-2-1-3-1s-1 0-2-1h-2v-1c1 0 1 0 2-1v-4c-1-2-1-3-1-6h1v-2h5z" class="j"></path><path d="M468 300h2c2 6 0 12 1 18 1 1 0 3 0 5 0 4 0 9 1 13 0 1 0 2-1 3v1c-1 0-2-1-3-1-1-1-3-1-4-2l1-2h1l1-1h0c1 0 1 1 2 1h0v-22c0-4 0-9-1-13z" class="O"></path><path d="M456 300h8v2h1v1h-1c-1 1 0 2 0 3l-1 2-1 1h1l1 2c-1 0-1 0-2 1h1v1h2c-1 1 0 1-2 1 1 1 2 1 3 1l-1 1h-8-1c-1-1-2-1-3-1s-1 0-2-1h-2v-1c1 0 1 0 2-1v-4c-1-2-1-3-1-6h1v-2h5z" class="r"></path><path d="M456 306v-1h4c1 0 1 1 2 1h2l-1 2-1 1h1l1 2c-1 0-1 0-2 1h1-2v-1c0-1-1-1-1-2-1-1 0-1 0-2l-1-1h-2-1z" class="t"></path><path d="M451 308c1 0 3 0 4-1v-2h-1v-1h2v1 1h0c-1 2 0 4 0 6-1-1-1 0-1-1-1 1 0 3-1 3h-3-2v-1c1 0 1 0 2-1v-4z" class="G"></path><path d="M451 300h5v3 1h-2v1h1v2c-1 1-3 1-4 1-1-2-1-3-1-6h1v-2z" class="N"></path><path d="M451 300h5v3l-2-1-2 1-1-1v-2z" class="L"></path><path d="M456 300h8v2h1v1h-1c-1 1 0 2 0 3h-2c-1 0-1-1-2-1h-4v-1-1-3z" class="h"></path><path d="M445 379v-2c2-1 2 0 4 0 0 1 1 1 2 1v-1h1c-1-1-1-1-2-1v-1-1s1-1 2-1c5 0 9 1 13 0l4 1 1 1v3 1h0c2 2 2 3 1 5-1 5 0 11 0 16v21h0-1-1c-3 0-4 1-6 1l-3 1h-17-2v3 1 3h1l-2 2c0-1-1-2-1-3v-4-3h-1l1-1c-1-1-1-2-2-3l-1-1v-1-2c1-2 2-3 3-3h1 1v-7-2-7c1 0 1 0 1-1 0-2 0-4 2-6v-1-5l2 3v-3c0-1-1-2-1-3z" class="V"></path><path d="M460 391s1-1 2-1c1 1 1 1 2 1v5c-1 4-1 9-1 14 0 3 0 6 1 9l1 1h-3c-1 0-1-1-1-2v-1-25l-1-1h0z" class="T"></path><path d="M446 382h1v1l1 1v3 1c1-2 1-4 1-6l1-1v-1c1 0 2 2 3 3 1 2 2 3 3 5-2 0-2 1-4 0-1 0-1 1-1 2h1 1l2 2h-1l-1-1-1 1v21 8h-2l-3-1c-1 0-1 0-2-1v-14-7c0-2 1-3 0-4 0-1 1 0 1-1v-3c0-1 1-1 1-2v-1c0-1 0-1-1-2v-3z" class="X"></path><path d="M452 392v21 8h-2l-3-1v-1c2-6-1-22 2-26l1-1h2z" class="I"></path><path d="M449 393c2 3 1 19 1 22 0 2-1 4 0 6l-3-1v-1c2-6-1-22 2-26z" class="r"></path><path d="M445 379v-2c2-1 2 0 4 0 0 1 1 1 2 1v-1h1c-1-1-1-1-2-1v-1-1s1-1 2-1c5 0 9 1 13 0l4 1 1 1h-2v1l-1 1v1h-2-1c1 1 1 1 2 1 1 2 1 3 1 4s0 1-1 1v1l-1 1c0 2 0 7-1 10v-5c-1 0-1 0-2-1-1 0-2 1-2 1h0-2c-1-1-1-2-2-3-1-2-2-3-3-5-1-1-2-3-3-3v1l-1 1c0 2 0 4-1 6v-1-3l-1-1v-1h-1c0-1-1-2-1-3z" class="j"></path><path d="M460 391v-1c-2-1-3-3-4-5l1-1v-1c0-2 2-4 4-5l1 1h0c1 0 2 0 3 1l1-1c1 2 1 3 1 4s0 1-1 1v1l-1 1c0 2 0 7-1 10v-5c-1 0-1 0-2-1-1 0-2 1-2 1z" class="J"></path><path d="M462 379c1 0 2 0 3 1l1-1c1 2 1 3 1 4s0 1-1 1v1l-1 1c0 2 0 7-1 10v-5c0-4 0-8-2-12 0 3 0 7-1 10h0c0-4-1-8 1-10z" class="l"></path><path d="M466 379c-1 0-1 0-2-1h1 2v-1l1-1v-1h2v3 1h0c2 2 2 3 1 5-1 5 0 11 0 16v21h0-1-1c-3 0-4 1-6 1l-3 1h-17-2v3 1 3h1l-2 2c0-1-1-2-1-3v-4-3h-1l1-1c-1-1-1-2-2-3l-1-1v-1-2c1-2 2-3 3-3h1 1v-7-2-7c1 0 1 0 1-1 0-2 0-4 2-6v-1-5l2 3c1 1 1 1 1 2v1c0 1-1 1-1 2v3c0 1-1 0-1 1 1 1 0 2 0 4v7 14c1 1 1 1 2 1l3 1h2c3 0 6 0 10-1h3l-1-1c-1-3-1-6-1-9 0-5 0-10 1-14 1-3 1-8 1-10l1-1v-1c1 0 1 0 1-1s0-2-1-4z" class="j"></path><path d="M445 419c-2-1-1-12-1-14 0-3 1-5 0-8-1-1 0-2 0-3h0l-1-2c0-1 0-2 1-3 0-1 1 0 1-1l1 2v3c0 1-1 0-1 1 1 1 0 2 0 4v7 14z" class="o"></path><path d="M440 421c3 1 6 0 9 1h10 0l1 1h-17-2v3 1 3h1l-2 2c0-1-1-2-1-3v-4-3h0l1-1z" class="d"></path><path d="M441 404c1 4 1 9 1 14 0 2-1 2-2 3l-1 1h0-1l1-1c-1-1-1-2-2-3l-1-1v-1-2c1-2 2-3 3-3h1 1v-7z" class="c"></path><path d="M436 416h0c1 0 1-1 2-2h1c1 1 2 2 2 4h1c0 2-1 2-2 3l-1 1h0-1l1-1c-1-1-1-2-2-3l-1-1v-1z" class="a"></path><path d="M466 379c-1 0-1 0-2-1h1 2v-1l1-1v-1h2v3 1h0l-1 1v1c1 0 1 1 1 2 1 7 0 14 0 21 0 4 1 9 0 13v2 1h-3c0-1 0-1 1-1-1-1-1-1-2-1v2h-1l-1-1c-1-3-1-6-1-9 0-5 0-10 1-14 1-3 1-8 1-10l1-1v-1c1 0 1 0 1-1s0-2-1-4z" class="O"></path><path d="M464 396c1-3 1-8 1-10l1-1v33 2h-1l-1-1c-1-3-1-6-1-9 0-5 0-10 1-14z" class="K"></path><path d="M442 430h-1v-3-1-3h2l1 1h0 1c1 1 1 1 1 2h0c1-1 1-1 1-2h1l1 1c1-1 3-1 4-1h2c2 1 3 0 5 0v1c-1 1-2 3-3 4h0l-3 8h9c1 1 3 1 5 1s2 0 4 1c0 1-1 3-1 4 1 1 0 2 0 3 0 0 1 0 1 1l-1 1h0c1 1 1 1 1 2-1 3 0 6 0 8 0 1-1 2-1 3 0 0 1 2 1 3l-1 1c0 2 1 4 1 6s-1 5 0 7v1c-2 1-4 1-6 1v1h1c1 0 3 1 4 2h-2 0-1c2 1 3 1 3 2l-6-1h0c0 1 1 1 1 2v1c2 1 2 0 3 0s1 2 1 2l1 1v2c0 1 0 1 1 2h0c-1 2-1 4-1 6v1l1 1v1 2 2c-2-1-3 0-5-1l1-1h-11-8-4-1c-1 0-3 0-4-1v-4l1-12v-19-28-1-2h-1c1-3 1-4 0-6l2-2z" class="X"></path><path d="M455 459c2 0 6 0 8 1v1l-1 1h-1c-2 0-5 1-6 0v-3z" class="V"></path><path d="M455 464h1c1 0 5 0 7 1h-1c1 2 1 3 1 5-1 1 0 1 0 2h-2-2-2c-2 1 0 1-2 1v-7h2v-1h-1l-1-1z" class="G"></path><path d="M444 483l2-1v-4h1l1 5h2l1 1v1c-2-1-3-1-4 0h-2v3c1 1 1 3 1 5l-1 1h6 1 0c0 1-1 2-2 1h0-3-1c0 1-1 2-1 4 0 1-1 2 0 4h6 0 1 5v2h-8-4-1c-1 0-3 0-4-1v-4l1-12 1-1 1-1 1-1c1 0 1-1 1-2h-1z" class="n"></path><path d="M445 503h-3v-1-2-4h2c1-1 1-1 2-1 0 1-1 2-1 4 0 1-1 2 0 4z" class="k"></path><path d="M468 438c2 0 2 0 4 1 0 1-1 3-1 4 1 1 0 2 0 3 0 0 1 0 1 1l-1 1h0c1 1 1 1 1 2-1 3 0 6 0 8 0 1-1 2-1 3 0 0 1 2 1 3l-1 1c0 2 1 4 1 6s-1 5 0 7v1c-2 1-4 1-6 1v1h1l-1 1h1l-1 1-2-3h-1l-1-1c1-1 2 0 4 0-1-2-1-4-2-5h4c-1-2-1-2-1-4s0-2 1-4h-1l1-2c-1 0-2 0-3-1h2l1-1c-1-1-1 0-2-1h-1v-1h2v-1h-3v1l-1 1v-1c-2-1-6-1-8-1h0v-1-1-3c2-1 5 0 7 0l1 1c1 0 2 0 3 1 1 0 2 0 3-1 0 0-2 0-2-1l2-2h-2v-1c1 0 2 0 3-1-1-1-3-1-4-1l-1-1h4v-1h-2v-1h2v-1h-3v-1h1l1-1c-1 0-2 0-3-1 1 0 2 0 3-1v-1l1-1-1-1z" class="Y"></path><path d="M455 457v-3c2-1 5 0 7 0l1 1c1 0 2 0 3 1h-3c1 1 3 1 4 1 0 1-4 1-6 1-2 1-4 0-6 0v-1z" class="E"></path><path d="M460 485v-2h-1c-1 0-1 0-1-1-2-3-5-5-7-8h0c0-1-1-2-1-3 2 1 3 3 4 4 1 2 2 4 4 5 1 1 3 3 4 3s2 0 3 1h0c0 1 1 1 1 2v1c2 1 2 0 3 0s1 2 1 2l1 1v2c0 1 0 1 1 2h0c-1 2-1 4-1 6v1l1 1v1 2 2c-2-1-3 0-5-1l1-1h-11v-2h-5-1 0-6c-1-2 0-3 0-4 0-2 1-3 1-4h1 3 0c1 1 2 0 2-1h0v-3h2v-1h-1c-1-1-2-2-2-3h2 1v-1h-2v-1h6c0-1 1-1 2-1v1z" class="I"></path><path d="M446 495h1c0 1-1 1 0 3 0 0 0 1 1 2l-1 2h0c1 0 3 0 4 1h-6c-1-2 0-3 0-4 0-2 1-3 1-4z" class="K"></path><path d="M457 503c0-1-1-1-1-2l1-1v-1c-1-1 0-2 0-4 2 0 3 0 5 1v7h-1l-1-1-1 1h1l-1 1-2-1z" class="P"></path><path d="M460 485v-2h-1c-1 0-1 0-1-1-2-3-5-5-7-8h0c0-1-1-2-1-3 2 1 3 3 4 4 1 2 2 4 4 5 1 1 3 3 4 3s2 0 3 1h0c0 1 1 1 1 2v1c2 1 2 0 3 0s1 2 1 2l1 1v2c0 1 0 1 1 2h0c-1 2-1 4-1 6v1l1 1v1 2 2c-2-1-3 0-5-1l1-1h-11v-2h0l2 1 1-1h-1l1-1 1 1h1v-7-1-1h3l1-1h-1l-1-1 1-1v-2-1l-1-2c-1 1-1 1-1 2h0c-1-1-2-1-3-1v-1h2 0l-2-1z" class="Y"></path><path d="M462 495h6l1 3v1c0 2-1 3 0 5-2 0-8 1-10 0l1-1h-1l1-1 1 1h1v-7-1z" class="m"></path><path d="M442 430h-1v-3-1-3h2l1 1h0 1c1 1 1 1 1 2h0c1-1 1-1 1-2h1l1 1c1-1 3-1 4-1h2c2 1 3 0 5 0v1c-1 1-2 3-3 4h0l-3 8h9c1 1 3 1 5 1l1 1-1 1v1c-1 1-2 1-3 1 1 1 2 1 3 1l-1 1h-1v1h3v1h-2v1h2v1h-4l1 1c1 0 3 0 4 1-1 1-2 1-3 1v1h2l-2 2c0 1 2 1 2 1-1 1-2 1-3 1-1-1-2-1-3-1l-1-1c-2 0-5-1-7 0v3h-1v-3-1l-1 1c-1-1-1-1-2-1s-2-1-2-1v-3c-1 1-1 2-1 4 1 0 1 0 1 1l-1 6 1 1c1 0 1 1 1 2l-1 1v3c-1 1-1 1-3 1l-1-1c1-1 1-1 1-3h-2l-1 1h1 0c1 1 1 1 1 3h-3v2c1 0 1 0 2-1l1 1-2 2v3h1l1 1c0 1-1 1-2 1v1c0 1 0 2-1 3l2 2h1c0 1 0 2-1 2l-1 1-1 1-1 1v-19-28-1-2h-1c1-3 1-4 0-6l2-2z" class="g"></path><path d="M457 453c-1 0-2-1-2-1 0-1 0-2 1-3h3c-1 1-1 2-2 4z" class="D"></path><path d="M444 457c1 0 2 0 3 1 0 2 0 2-1 3l-3-1h0v-1c0-1 0-2 1-2z" class="b"></path><path d="M444 424h1c1 1 1 1 1 2h0c0 3-1 9 0 11h-1c0-1-1-2-1-3s1-2 1-3c0-3 0-4-1-7z" class="k"></path><path d="M449 449h4c1 2 1 3 1 4l-1 1c-1-1-1-1-2-1s-2-1-2-1v-3z" class="I"></path><path d="M444 449c1 0 2 0 3 1h-1l1 2-1 1 1 1-1 1c-1 1-1 1-3 1v-5h0l1-2z" class="k"></path><path d="M459 449h1c1 0 3 0 5 1v2l-1-1c-1 1-1 1-1 2h-6c1-2 1-3 2-4z" class="E"></path><path d="M442 430h-1v-3-1-3h2l1 1h0c1 3 1 4 1 7 0 1-1 2-1 3s1 2 1 3c-1-1-2-1-3 0v-1c1-1 1-4 0-6z" class="b"></path><path d="M449 439h3c0 1 0 1 1 2v-2h1c0 1 0 4-1 5h-1v-1 1l1 2h0v2h0c-2 0-3 0-5-1v-2c0-1 1-2 1-2 1-2 0-2 0-4z" class="G"></path><path d="M449 425c1-1 3-1 4-1h2c2 1 3 0 5 0v1c-1 1-2 3-3 4h0c-1 2-3 4-3 7v1l-2-1c0 1-1 1-1 2-1-1-1-1-2-1h-1c1-4 1-8 1-12z" class="Q"></path><path d="M522 498c2 0 5 1 7 0 1 0 1 1 2 0h4l4 1c1 0 3 0 4 1 1 0 1 1 1 1 0 1 1 2 1 3h1v3h-2c-1 0-2 0-4 1h0 6v1 3c0 1 0 2-1 4h1c-1 2 0 6-1 8v2h0c-1 2-2 4-2 5v3 1h-1l-2 1v-1h-5v1h-2v1h-1c-1 1-1 1-1 2-1-1-1 0-1-1-1 0-3-1-4-1 0 1-1 1-1 1v-1h-3-15s-1 0-2 1h0c-1-1-1-1-2-1h-1-9c-1 1-1 1-2 0-2 0-4 1-7 1 0-2 1-2 0-3h-4c-2-1-2-2-2-4l-1-1c0-1-1-1-2-2v-1c-1-1 0-5 0-6 1-3 0-8 0-11v-2l-3 1v4c0-1 0-3-1-5h-3c-2 1-1 1-2 2 0-1 0-2-1-3l-1-1h3c2 1 3 0 5 1h2v-1l1-1 1 1h8 14c2 0 3 0 5 1l2-2c0 1 0 2 2 2h2l1-2h1v-2-1-4h1 10z" class="X"></path><path d="M507 516c1 1 2 2 3 4h-5v-1-1c0-1 1-1 2-2z" class="F"></path><path d="M483 524l2-1 1 2c3 0 6-1 10-1-2 1-3 1-4 2h1v1h-1c-1 0-1 0-2 1h-1l1-1 1-1h0c-1 0-3 0-4 1 0 2 0 2 2 3v-1h1c1 0 1 0 2-1 1 0 2 1 3 1v2h3c1 0 2 0 3 1h1v2h-2c-2-1-4 0-5-1l-1-1h-3c-3 0-6 1-9-2v-1l2 2 1-1-1-2s1-1 1-2l-1-1-1-1z" class="j"></path><path d="M503 509h2l1 1 1-1h2 1c0 2-1 3 0 5h1v1c-1 0-3 0-4 1s-2 1-2 2v1 1c-1-1-1-2-1-3h0-1v4c-2 0-3 0-5-1v-3h-1v2h-1l-1-3c1-1 1-3 1-5l2-1h0c1 0 2-1 2-1 1 0 1 0 2 1v-1h1z" class="J"></path><path d="M502 510v-1h1v2l1 1-1 1-1 1v2c-1-1-2-2-2-3v-2c1 0 1 0 2-1z" class="D"></path><path d="M511 514v-5c4-1 9 0 13 0h3v3 1c0 1-1 1-1 2-1 1-1 2-1 3 0 2-1 3-2 4l-2 3s0 1-1 2-2 2-4 3h-1v-3h-1l-1 1v-1-1h-3c-1 2 0 2 0 4v1c-2 0-3-2-4-3l1-1-1-2h1 1c1 0 1 0 3 1v-1h2 1v-1h-1v-1l3-3c2 0 3-1 3-3h1c2-2 3-5 5-6h1l-1-1c-2 1-4 3-5 5l-2 2h-2 0v2c-1 1-3 2-5 2v-6-1z" class="L"></path><path d="M522 498c2 0 5 1 7 0 1 0 1 1 2 0h4l4 1c1 0 3 0 4 1 1 0 1 1 1 1 0 1 1 2 1 3h1v3h-2c-1 0-2 0-4 1h0v1h-3c-2 0-3 0-5-1l-1 1v4c-1 1 0 3 0 4h-1-3l-1 1-1 3-1 2-1-1c1-1 2-2 2-4 0-1 0-2 1-3 0-1 1-1 1-2v-1-3h-3c-4 0-9-1-13 0v5h-1c-1-2 0-3 0-5h-1-2l-1 1-1-1h-2-1v1c-1-1-1-1-2-1 0 0-1 1-2 1h0l-2 1c0-1-1-1 0-3 0 0 2-1 2-2 2 0 3 0 5 1l2-2c0 1 0 2 2 2h2l1-2h1v-2-1-4h1 10z" class="Y"></path><path d="M544 507c0-1 1-2 0-3h-1-1v-1c0-1 1-1 2-2 0 1 1 2 1 3h1v3h-2z" class="U"></path><path d="M511 498h1 10c-2 1-2 1-3 1-1 1-1 1-1 2h3c1 1 4 0 5 2h0v1h0l-1 1c0 1 0 1 1 1l-1 1c-2 0-3 0-4-1v-1h-1l-2 2h0c-2 0-2 0-4-1-2 1-3 1-5 1l1-2h1v-2-1-4z" class="Z"></path><path d="M511 498h1l2 2-1 1c1 0 1 0 2 1l1 1c-1 1-1 1-3 1 1 1 1 1 1 2-2 1-3 1-5 1l1-2h1v-2-1-4z" class="m"></path><path d="M526 506c0-1 1-2 2-2l1 1v1h1 4v1c-1 1-3 0-5 0l1 1h2 0l-1 1v4c-1 1 0 3 0 4h-1-3l-1 1-1 3-1 2-1-1c1-1 2-2 2-4 0-1 0-2 1-3 0-1 1-1 1-2v-1-3h-3c-4 0-9-1-13 0v5h-1c-1-2 0-3 0-5h-1-2l-1 1-1-1h-2-1v1c-1-1-1-1-2-1 0 0-1 1-2 1h0l-2 1c0-1-1-1 0-3 0 0 2-1 2-2 2 0 3 0 5 1l2-2c0 1 0 2 2 2h2c2 0 3 0 5-1 2 1 2 1 4 1h0l2-2h1v1c1 1 2 1 4 1l1-1z" class="i"></path><path d="M472 507h2v-1l1-1 1 1h8 14c0 1-2 2-2 2l-7 8c-1 1-2 2-2 3h2 1s1 0 2 1h0-2c-1-1-3 0-5 0v1s2 0 2 1h-3c0 1-1 1-1 2l1 1 1 1c0 1-1 2-1 2l1 2-1 1-2-2v1c3 3 6 2 9 2h3l1 1c1 1 3 0 5 1h2c0 1 1 2 1 3h-1-9c-1 1-1 1-2 0-2 0-4 1-7 1 0-2 1-2 0-3h-4c-2-1-2-2-2-4l-1-1c0-1-1-1-2-2v-1c-1-1 0-5 0-6 1-3 0-8 0-11v-2l-3 1v4c0-1 0-3-1-5h-3c-2 1-1 1-2 2 0-1 0-2-1-3l-1-1h3c2 1 3 0 5 1z" class="U"></path><path d="M500 534h2c0 1 1 2 1 3h-1-9c-1 1-1 1-2 0-2 0-4 1-7 1 0-2 1-2 0-3h0 1c3-1 7-1 10-1 0 0 0 1 1 1h3l1-1z" class="p"></path><path d="M472 507h2v-1l1-1 1 1h8 14c0 1-2 2-2 2l-7 8-2 1-1 1-1-1v-1l-1 1-1-1 1-1s2-1 2-2h0-1-1v-1-2-1c-1 1-1 1-1 2-1-1 0-1-2-2v2l-1-1h-1 0c-1 1-1 1-2 1v4c1 2 1 3 3 3-1 1-1 1-1 2s1 1 2 1l-2 2c-2 2-3 3-4 5v-1c-1-1 0-5 0-6 1-3 0-8 0-11v-2l-3 1v4c0-1 0-3-1-5h-3c-2 1-1 1-2 2 0-1 0-2-1-3l-1-1h3c2 1 3 0 5 1z" class="S"></path><path d="M484 506h14c0 1-2 2-2 2l-7 8-2 1-1 1-1-1v-1l-1 1-1-1 1-1s2-1 2-2h0-1-1v-1l1-1h1l-1-1 1-2h2 2c2-1 2-1 3-1h0l-9-1z" class="Y"></path><path d="M540 508h6v1 3c0 1 0 2-1 4h1c-1 2 0 6-1 8v2h0c-1 2-2 4-2 5v3 1h-1l-2 1v-1h-5v1h-2v1h-1c-1 1-1 1-1 2-1-1-1 0-1-1-1 0-3-1-4-1 0 1-1 1-1 1v-1h-3-15s-1 0-2 1h0c-1-1-1-1-2-1h-1 1c0-1-1-2-1-3v-2-1c-1-2-2-2-3-3 0-1-1-3-1-3l1-1c0 1 1 2 1 3 1 2 3 4 4 6h2v-2l2 1c1 0 1 0 2-1v-1c0-2-1-2 0-4h3v1 1l1-1h1v3h1c2-1 3-2 4-3s1-2 1-2l2-3 1 1 1-2 1-3 1-1h3 1c0-1-1-3 0-4v-4l1-1c2 1 3 1 5 1h3v-1z" class="j"></path><path d="M526 518l1-1h3v2h3v1h-1c-1 0-1 1-2 2v3l-1 1-3-1h2c1-2 0-2 0-3v-2l-1-1-1-1z" class="m"></path><path d="M529 526c-1 2-2 3-3 5-2 1-2 1-4 0 0 1-1 1-2 1 0-1 2-3 3-5v-1c1 0 1-1 2-2 0-1 0-1 1-2h2c0 1 1 1 0 3h-2l3 1z" class="f"></path><path d="M532 508c2 1 3 1 5 1h3 3c0 1-1 2-1 3-1 0-1 1-1 0l-1 1 1 1c-1 1-1 2-2 3h0-4c0 1 0 1-1 1 0 1 0 0-1 1h-3v-2h1c0-1-1-3 0-4v-4l1-1z" class="C"></path><path d="M502 532v-1c-1-2-2-2-3-3 0-1-1-3-1-3l1-1c0 1 1 2 1 3 1 2 3 4 4 6h2c3 2 10 1 14 2 3 0 4-1 7-1h8c1-1 3-1 5-1 0 0 1-1 2-1l1-1v3 1h-1l-2 1v-1h-5v1h-2v1h-1c-1 1-1 1-1 2-1-1-1 0-1-1-1 0-3-1-4-1 0 1-1 1-1 1v-1h-3-15s-1 0-2 1h0c-1-1-1-1-2-1h-1 1c0-1-1-2-1-3v-2z" class="R"></path><path d="M541 313v-3c0-1 0-1 1-2l1 2 2 10c1 2 1 4 2 6h2v1 4l-1 8v8l-1-1v7 5l1 1h-2c0 2-1 4 0 7v5 23c0 1-1 3-1 4s1 2 1 2c-2-1-4 0-6-1h-3-2c-1 1-1 2-2 2-1 1-3 0-4 1h-3 0c-2 0-4 0-6-1-1 0-2 0-3-1h1v-1h-7c-1 0-2-1-2-1h-1l-1 1h-5-5-1-7-5l-2-2c1-3 0-6 0-9v-23-24-10c-1 0-3-1-4-1 1 0 2-1 3-1-1-1-3 0-4 0l-1-1 1-1h9c1-1 2-1 3-1h3 3l2-1h0 0 2 7 6 12 4 7 4 0l5 1c-2-4-3-9-3-13z" class="L"></path><path d="M530 399h5c-1 1-1 2-2 2-1 1-3 0-4 1h-3l1-1h-1c1-1 2-1 3-1l1-1z" class="D"></path><path d="M533 330h1v1c0 5 1 10 0 15-1-1-1-2-1-4v-12z" class="F"></path><path d="M533 342c0 2 0 3 1 4v1c0 3-1 6 0 8v17-1-1l-1 1c0-6-1-12-1-17 0-4 1-8 1-12zm-5 56l1-1v-4c2-6 1-14 1-21 0-4-1-8 1-12 1 5 2 30 0 33h0v2 1h-1v-3l-1 4-1 1z" class="B"></path><path d="M508 368l3 2v4c-1 1-1 3-1 4 1 1 1 3 1 5v14 1l-1-1h-1v1h-1v-9-21z" class="G"></path><path d="M508 389v-7l1 1v14 1h-1v-9z" class="N"></path><path d="M526 329c2 0 3 0 5 1h1c0 3 1 11-1 14v16c-2 4-1 8-1 12 0 7 1 15-1 21v4l-1 1s-1 0-2-1v-6c1-1 0-27 0-31v-31z" class="C"></path><path d="M539 331c1 0 1 0 1-1h1c0 5 1 9 0 13 1 2 1 4 1 6v5c-1 2-1 3-1 4-1 10-1 21-1 31v8c-1 1-2 1-3 1v1h-2-5v-1h2v-2l1-2h2v-3c1-2 1-7 1-9v-23-17c1-3 0-6 1-9 1 0 1-2 1-3l1 1z" class="h"></path><path d="M537 398h0c2-7 2-14 2-21l1-16c0-6-1-12 1-18 1 2 1 4 1 6v5c-1 2-1 3-1 4-1 10-1 21-1 31v8c-1 1-2 1-3 1z" class="d"></path><path d="M537 333c1 0 1-2 1-3l1 1c0 5 0 9-1 13v14 18c0 4 0 7-1 11s1 7-3 10l-1-1h-1l1-2h2v-3c1-2 1-7 1-9v-23-17c1-3 0-6 1-9z" class="E"></path><path d="M541 330l1 2 1-2 1 1h1l1-1v14c0 2-1 4 0 6v1c0 1 1 2 1 2v5l1 1h-2c0 2-1 4 0 7v5 23c0 1-1 3-1 4s1 2 1 2c-2-1-4 0-6-1h-3v-1c1 0 2 0 3-1v-8c0-10 0-21 1-31 0-1 0-2 1-4v-5c0-2 0-4-1-6 1-4 0-8 0-13z" class="O"></path><path d="M544 346c1 4 2 8 0 11-1 1-1 3-1 4v13c0 4-1 7-1 10v12c0 1 0 1-1 2h-1v1h-3v-1c1 0 2 0 3-1v-8c1-1 0-1 1-2v-1-1-2c2-8 0-17 1-25l1-1v-1c1-2 0-3 0-5 1-1 1-3 1-5z" class="m"></path><path d="M541 330l1 2 1-2 1 1v15c0 2 0 4-1 5 0 2 1 3 0 5v1l-1 1c-1 8 1 17-1 25v2 1 1c-1 1 0 1-1 2 0-10 0-21 1-31 0-1 0-2 1-4v-5c0-2 0-4-1-6 1-4 0-8 0-13z" class="j"></path><path d="M541 313v-3c0-1 0-1 1-2l1 2 2 10c1 2 1 4 2 6h2v1 4l-1 8v8l-1-1v7s-1-1-1-2v-1c-1-2 0-4 0-6v-14l-1 1h-1l-1-1-1 2-1-2h-1c0 1 0 1-1 1l-1-1c0 1 0 3-1 3v-3h-1l-1 1h-1v-1h-1-1-1c-2-1-3-1-5-1h-2-1-3-3-9c-1 0-3-1-3 0h-2 0-1c-2 0-3 1-4 1l-1-1v1l-2-1-1 1h-1-1-1l-1-1c0 1 0 1-1 2 0-1-1-1-1-1h-1l-1 4h0c0-2-1-3-1-4h-1v8l-1-7h-1c-1 0-3-1-4-1 1 0 2-1 3-1-1-1-3 0-4 0l-1-1 1-1h9c1-1 2-1 3-1h3 3l2-1h0 0 2 7 6 12 4 7 4 0l5 1c-2-4-3-9-3-13z" class="Y"></path><path d="M549 326v1 4l-1 8v8l-1-1v-20h2z" class="b"></path><path d="M478 330c1 0 2-1 3-1-1-1-3 0-4 0l-1-1 1-1h9c1-1 2-1 3-1h3l1 1h20 8c1 0 1 1 2 2h-3-3-9c-1 0-3-1-3 0h-2 0-1c-2 0-3 1-4 1l-1-1v1l-2-1-1 1h-1-1-1l-1-1c0 1 0 1-1 2 0-1-1-1-1-1h-1l-1 4h0c0-2-1-3-1-4h-1v8l-1-7h-1c-1 0-3-1-4-1z" class="U"></path><path d="M508 329h9 3 3 1 2v31c0 4 1 30 0 31l-1 1c-1 2 1 2-1 3l-1 2h-1v-1-2-1h0l-1-1h-1c1 2 1 4 1 6h-2l-1-1v-16c0-1 0-3 1-4h1v-2c1-1 1-3 0-4l-2 1c0-1 0-1-1-1l-2-2-1 1v-1c-1-1-1-3-1-4l1-16c0-2 1-12 0-13-2 1-1 4-1 6 0 9-1 18-2 28l-3-2v-39z" class="P"></path><path d="M521 338c0-2 2-5 2-7v5l-2 2z" class="F"></path><path d="M523 329h1l-1 2c0 2-2 5-2 7h0-1v-9h0 3z" class="G"></path><path d="M517 329h3 0-1v9c-2 2-1 12-1 14v3c0 1 0 0 1 1h1 0v10 1c-1 1-1 2-2 3v-1c0-2 0-5-1-7v-9-24zm3 63c1 0 1-1 1-2h1 3v-10c-2 1-3 3-4 4l-1-1c0-2-1-4 0-5 0 1 1 1 1 1h3 1v-17c-1-1-2-1-2-1v-1h2 1c0 4 1 30 0 31l-1 1c-1 2 1 2-1 3l-1 2h-1v-1-2-1h0l-1-1h-1z" class="F"></path><path d="M505 329c0-1 2 0 3 0v39 21 9l-1 1h-5-5-1-7-5l-2-2c1-3 0-6 0-9v-23-24-10h1l1 7v-8h1c0 1 1 2 1 4h0l1-4h1s1 0 1 1c1-1 1-1 1-2l1 1h1 1 1l1-1 2 1v-1l1 1c1 0 2-1 4-1h1 0 2z" class="E"></path><path d="M495 351c1 0 2-2 2-2l1-1v1c-1 5-1 11 0 15l-1 4v-1l-1-1c0-1 0 0-1-1l-2-1v-1c0-2 1-4 1-6l-1-4c1-1 1-2 2-2z" class="X"></path><path d="M494 357h1v1 4c-1 1-1 1-2 1 0-2 1-4 1-6z" class="h"></path><path d="M502 329h1 0v14 8c-2-4 1-9-2-13-1 3 0 23 0 28-1-1-1-3-1-5 0-4 1-11-1-16 1-3 0-10 1-13l1 1h0c0-2 0-2 1-4z" class="f"></path><path d="M498 330c1 0 2-1 4-1-1 2-1 2-1 4h0l-1-1c-1 3 0 10-1 13l-1 4h0v-1l-1 1s-1 2-2 2c1-5 1-9 1-14h1v-7-1l1 1z" class="c"></path><path d="M497 330v-1l1 1v3c1 1 0 6 0 8h-1v-4-7z" class="T"></path><path d="M493 364l2 1c1 1 1 0 1 1l1 1v1l1-4c1 8 0 18 0 26 1 2 0 5 1 8h-3 0c-1-1-1 0-2-1 0-2 0-7 1-8h0v-2c-3-2 1-15-2-19v-2-2z" class="c"></path><path d="M496 398c0-5-1-10 0-14l1-1c0 2 0 5 1 6v1c1 2 0 5 1 8h-3 0z" class="J"></path><path d="M493 364l2 1c1 1 1 0 1 1l-1 21c-3-2 1-15-2-19v-2-2z" class="K"></path><path d="M505 329c0-1 2 0 3 0v39 21 9l-1 1h-5l-1-3c1-1 0-2 1-2v-2c2-3 0-18 1-22v1 6s0 1 1 2h0v-37c0-2-1-8 0-10 1-1 1-2 1-3z" class="f"></path><path d="M492 330h1 1l1-1 2 1v7h-1c0 5 0 9-1 14-1 0-1 1-2 2l1 4c0 2-1 4-1 6v1 2 2c3 4-1 17 2 19v2h0c-1 1-1 6-1 8 1 1 1 0 2 1h0 3 1l1-1v-1l1 3h-5-1-7-5l-2-2c1-3 0-6 0-9v-23-24-10h1l1 7v-8h1c0 1 1 2 1 4h0l1-4h1s1 0 1 1c1-1 1-1 1-2l1 1h1z" class="d"></path><path d="M492 351v15h0 1v2c-1 2-1 4-1 5-2 3 0 6-1 9 0-6 0-11-1-17 2-4 1-10 2-14z" class="Q"></path><path d="M489 344l1-4c0 1-1 3 0 4l1-1v2 1h0l1 4v1c-1 4 0 10-2 14-1-3 0-5 0-7 0-3-1-6-1-9v-5z" class="h"></path><path d="M491 382c1-3-1-6 1-9 0 6 0 11 1 16v9c-1 0-2 0-2-1v-15z" class="I"></path><path d="M492 373c0-1 0-3 1-5 3 4-1 17 2 19v2h0c-1 1-1 6-1 8 1 1 1 0 2 1h0-3v-9c-1-5-1-10-1-16z" class="h"></path><path d="M492 330h1 1l1-1 2 1v7h-1c0 5 0 9-1 14-1 0-1 1-2 2l1 4c0 2-1 4-1 6v1 2h-1 0v-15-1l-1-4h0v-1-2l-1 1c-1-1 0-3 0-4l-1 4c0-5 1-9 0-13 1-1 1-1 1-2l1 1h1z" class="r"></path><path d="M489 331c1-1 1-1 1-2l1 1h1l-1 16h0v-1-2l-1 1c-1-1 0-3 0-4l-1 4c0-5 1-9 0-13z" class="t"></path><path d="M492 350c0-2-1-6 1-8 1 1-1 4 1 6v-1-3l1-6 1-1c0 5 0 9-1 14-1 0-1 1-2 2l1 4c0 2-1 4-1 6v1 2h-1 0v-15-1z" class="E"></path><path d="M482 331h1l1 7v-8h1c0 1 1 2 1 4h0l1-4h1s1 0 1 1c1 4 0 8 0 13v5l-1-1c-1-2 0-9 0-12h-1l1 62h0c1 1 1 0 1 1h-5l-2-2c1-3 0-6 0-9v-23-24-10z" class="b"></path></svg>