<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="74 39 712 640"><!--oldViewBox="0 0 844 752"--><style>.B{fill:#606060}.C{fill:#bab9ba}.D{fill:#818080}.E{fill:#8b8a8a}.F{fill:#797878}.G{fill:#504f50}.H{fill:#737273}.I{fill:#d3d2d2}.J{fill:#979696}.K{fill:#aaa9a9}.L{fill:#c9c8c9}.M{fill:#363536}.N{fill:#1f1e1f}.O{fill:#5a595a}.P{fill:#2a2a2a}.Q{fill:#919090}.R{fill:#565556}.S{fill:#302f30}.T{fill:#444344}.U{fill:#b1b0b0}.V{fill:#3e3e3e}.W{fill:#484848}.X{fill:#6a6969}.Y{fill:#666}.Z{fill:#6d6d6d}.a{fill:#4c4b4b}.b{fill:#e5e4e4}.c{fill:#9f9e9e}.d{fill:#262525}.e{fill:#a3a2a2}.f{fill:#3e3d3e}.g{fill:#121212}.h{fill:#edeced}</style><path d="M551 458l1-1c1 1 1 0 2 0-1-2-1-4 0-6v-1 8c0 1-1 2-1 3h-1c0-1 0-2-1-3z" class="P"></path><path d="M323 222c1 1 2 1 2 1l-3 6v-1c0-2 0-3 1-5v-1z" class="F"></path><path d="M528 493l8-1-1 2c-1 0-2 0-3 1-1-1-3 0-4-1v-1z" class="K"></path><path d="M460 120c2-1 4-1 7-1l2 1h0c-3 1-6 0-9 2v-2z" class="H"></path><path d="M608 409c1 1 3 1 4 1-3 0-4 3-7 3-1 1-1 3-1 4v-4c1-2 2-3 4-4z" class="R"></path><path d="M739 330l6 3v2l-8-3 2-2zM269 495h0c1-1 2-1 3-1 2 1 5 2 7 2h0c-1 0-2 0-3 1h0-3c-1-1-3-1-4-2z" class="F"></path><path d="M453 94l4 1c-2 1-4 3-7 5 0-1 0-1-1-1l3-3 1-2z" class="K"></path><path d="M203 408l2-1c-1 3-1 4 0 7 0 1 0 2 1 4l-1-1c-2-1-2-4-3-6 0-1 1-2 1-3z" class="Y"></path><path d="M374 119c2 0 5 0 7 1 0 1 0 1-1 1h-1c-3-1-7 0-10 0v-1h1c1-1 3-1 4-1z" class="J"></path><path d="M273 489c2 1 2 1 3 3h2l9 3h4-3-2-2c0-1 0 0-1-1h-2-1c-1 0-2-1-3-1s-2-1-3-1c-1-1-2-1-4-2h1 1 1v-1z" class="S"></path><path d="M626 405l-2 2c-2-1-2-1-3-2 0-1 0-1 1-3 0-1 2-2 4-3l1 1-1 1h1c-2 0-2 0-3 1v1l1 1c1 1 1 0 1 1z" class="I"></path><path d="M266 500h3c3 1 6 1 9 1-1 1-1 1-1 0-2 0-4 1-6 1-3 0-6 2-8 4h0-1l1-1h0 0v-1c0-1 1-1 2-2 0 0 1 0 1-1h2v1h1c0-1 1-1 2-1h-2c-1 0-2 0-3-1z" class="P"></path><path d="M664 132c1-2 1-5 2-6 0 2 0 4-1 6 0 4 0 7 1 11l-1-1c0 1-1 2-1 2v-9h0c0-1 1-1 1-2s0-1-1-1z" class="J"></path><path d="M280 466c2-4 4-7 5-12v4c0 3-2 7-4 9-1 1-2 1-3 2l2-3z" class="P"></path><path d="M377 117c2-1 3 0 6 0h7s0 1 1 1l-1 1h0-4c-1 0-3-1-4-1-2 0-4 0-5-1z" class="L"></path><path d="M234 408c1 1 3 3 3 5s0 4-1 4l-1 1c-1-1-1-3-1-5l1-2c-1-1-2-2-2-3h1z" class="J"></path><path d="M83 395l3 3h0l5 9c-4-3-7-8-9-12h1z" class="E"></path><path d="M626 572l10 3c1 0 5 0 6 1-1 1-8 0-10 0-2-1-5-2-7-3l1-1z" class="F"></path><path d="M427 64c1 4 2 7 4 11v4l-1 1v-1h-1 0l-2-10c1-1 0-4 0-5z" class="G"></path><path d="M288 465c0-2 1-5 1-7 2 5 1 8-1 13l-3 4v-1c0-1 2-4 2-5 1-1 1-3 1-4z" class="a"></path><path d="M206 400v1c-2 2-3 4-3 7 0 1-1 2-1 3v3h0l-1 1c0-2-1-4-1-6 1-4 3-7 6-9z" class="U"></path><path d="M602 539l4 14c-1 1-1 1-1 2-2-3-3-8-4-12 0-1 0-3 1-4z" class="B"></path><path d="M101 204h1v3c0 2-1 4-2 7 0 2 0 5-1 7 0-6-1-11 2-17z" class="U"></path><path d="M609 105h1 1 0c0-1 1-2 1-3l5 12c-3-1-3-2-5-5l-1 1h0l-2-5z" class="V"></path><path d="M206 400c1-1 2-1 3-1l1 1 1 1v1 1c-2 0-4 1-6 3v1l-2 1c0-3 1-5 3-7v-1z" class="e"></path><path d="M324 138l4-5c-1 3-3 7-2 11v1h-1c0-1-1-3-2-4 0 1-1 1-2 1h-1-1l1-1v-1c2 0 3-1 4-2z" class="L"></path><path d="M516 102c-3-3-7-5-11-7l-9-5c6 1 12 3 17 7l-3-1c2 2 5 3 6 6z" class="I"></path><path d="M639 488c1-5 4-7 6-10h1c-3 5-6 8-5 14l1 3c-1 0-1 1-1 1h-1-1v-1-1-6z" class="E"></path><defs><linearGradient id="A" x1="730.804" y1="332.309" x2="736.108" y2="328.362" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#727373"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M725 330c4-1 7-1 10-1l4 1-2 2c-5-1-8-1-12 1l-1-1c1 0 1 0 2-1h0l-1-1z"></path><path d="M719 433c6 2 11 3 18 3 1 1 2 1 3 1v1c-7 0-15-1-22-3l1-1v-1z" class="B"></path><path d="M331 143c2-5 4-4 9-3 1 0 2 0 3-1v-2 3h0c0 1 0 1 1 2l-2 1c-1 0-1 1-1 1l-2 1 1-3c1 0 1 0 2-1-2 0-7 0-9 1l-1 1h-1z" class="U"></path><path d="M209 399c3 0 5 1 6 2s2 2 2 3c-1 2-1 2-2 3-1 0-2 0-3-1l-1-1v-2-1-1l-1-1-1-1z" class="J"></path><path d="M311 494c4 0 9-1 13-1l-14 4c-2 1-3 1-5 0h0v-1h1 0c1-1 4-2 5-2z" class="Z"></path><path d="M526 497c-1 0-3-1-4-1l-10-4 16 1v1c1 1 3 0 4 1-1 0-3 0-4 1h-2v1z" class="c"></path><path d="M109 197h1 0c1-1 3-1 4-2l1 1h0c-6 3-9 7-13 11v-3c2-3 4-5 7-7z" class="E"></path><path d="M74 432c-5-3-9-6-13-11l12 6v1c0 1 2 2 3 3l-2 1zm4-60h0c0 7 2 16 5 23h-1c-3-7-6-14-7-22 2 2 2 8 3 11v-2h0v-2-2l-1-1s1-1 0-2h0c0-1 0-2 1-3z" class="C"></path><path d="M277 466h3l-2 3-5 5c-2 2-5 3-8 5l1-2a57.31 57.31 0 0 0 11-11z" class="M"></path><path d="M604 417v1h0l-1-1c-1-1-1-4-1-5 1-2 3-4 4-4 3-2 6-2 8-1-1 1-2 1-3 1s-2 1-3 1c-2 1-3 2-4 4v4z" class="C"></path><path d="M187 461l1-1c1 1 1 5 1 7v2 1 1l1 2 7 8h-1 0c-2-1-9-8-9-10 0-1 1-2 1-3 0-2-1-5-1-7z" class="D"></path><path d="M756 355l2 1c-1 2 0 4-1 7v-3l-1-1c-1 2-2 5-3 7l-5 5 4-11c2-1 2-3 4-5z" class="H"></path><path d="M216 504l2 1c1 3 3 7 4 11 0 5 1 9 0 13v1l-1-1-1 1c1-5 1-11 0-16-1-3-3-6-4-10z" class="C"></path><path d="M196 481h1c1 1 2 3 3 5v4c-1 2-1 5-1 8-3 2-6 5-9 8 3-6 8-10 9-16 0-4-1-6-3-9z" class="L"></path><path d="M181 455h4 0c-5 1-12 6-16 11l-1 3-1-1c0-1 2-3 2-4l1-2c4-3 7-5 11-7z" class="D"></path><path d="M554 483l1 2c-5 6-12 7-20 9l1-2c6-1 13-4 18-9z" class="L"></path><path d="M629 385c3-4 5-8 6-12 2-4 2-8 3-12l2 2c-1 2-1 4-1 5-1 5-3 8-5 12-1 1-1 1-1 2-1 2-2 3-3 4l-1-1z" class="I"></path><path d="M716 207h1c0 1-1 2-2 2l-4 2-14 3c-1-1-3 0-4-1v-1l4-1c7 0 13-1 19-4z" class="E"></path><path d="M285 486c9 6 16 8 26 8-1 0-4 1-5 2-2 0-6 1-8 0 0 0 0-1-1-1-1-2-5-3-7-4s-4-3-5-4v-1z" class="R"></path><path d="M629 385l1 1c1-1 2-2 3-4 0-1 0-1 1-2v3c0 1-3 4-4 5l-1 1h0c-2 2-5 3-7 4-4 2-8 4-12 4 5-3 10-4 15-7 2-2 3-4 4-5z" class="L"></path><path d="M605 555c0-1 0-1 1-2 4 9 8 15 18 18l2 1-1 1c-5-1-8-3-11-5-4-4-7-8-9-13z" class="E"></path><path d="M530 334h1l5-5h2c-5 6-11 14-18 17 0-2 2-4 4-5l5-7h1z" class="F"></path><path d="M564 425c2-2 5-4 8-5h0l1-1c1 0 2 1 3 1 4 1 7 2 11 4-4-1-7-1-10-1-2-1-6-1-8 0s-3 2-4 3l-1-1z" class="e"></path><path d="M664 132c1 0 1 0 1 1s-1 1-1 2h0c-4 4-8 10-14 12h0c-1-1-1-2-2-3h2c5-1 11-9 14-12z" class="U"></path><defs><linearGradient id="C" x1="309.294" y1="333.835" x2="317.435" y2="339.402" xlink:href="#B"><stop offset="0" stop-color="#5d5d5e"></stop><stop offset="1" stop-color="#757475"></stop></linearGradient></defs><path fill="url(#C)" d="M306 323l14 20h-3c-3-3-6-7-9-11l-1-2h1c-1-2-2-4-3-5 0-1 1-1 1-2z"></path><path d="M202 411c1 2 1 5 3 6l1 1h0l6 4c1-1 1-1 1-2l1 2h0c0 2 0 2-2 2l-2 1c-4-3-7-6-9-10l1-1h0v-3z" class="D"></path><path d="M261 483l-2 7c4 2 8 3 13 4-1 0-2 0-3 1h0c-6 0-11-4-17-6 1 0 2 0 3-1h0l2-2 4-3z" class="V"></path><path d="M260 293c3 0 5 0 8 1h2c1 0 5 2 6 2h1c1 2 5 3 6 5v3c-6-6-14-9-23-11z" class="T"></path><defs><linearGradient id="D" x1="315.683" y1="256.131" x2="323.587" y2="250.118" xlink:href="#B"><stop offset="0" stop-color="#818382"></stop><stop offset="1" stop-color="#a7a5a7"></stop></linearGradient></defs><path fill="url(#D)" d="M316 251c1 0 2 0 3-1 2 0 5 1 6 1 4 5 8 8 13 12l-6-3c-5-4-8-9-15-6h-1-4l4-3z"></path><path d="M189 455h4 0c0 2 1 4 0 6v2 5-1c-1-2-2-5-3-8v5c0 1 1 3 1 4-1 0-1 0-2 1v-2c0-2 0-6-1-7l-1 1c-1-2-1-2-1-4 1 0 2 0 3-1h0l-4-1h0 4z" class="a"></path><path d="M189 455h4 0c0 2 1 4 0 6v2c-2-2-1-4-2-7h-1l-1 1v3c-1 0-1-1-1-2-1 0-1 0-2-1 1 0 2 0 3-1h0l-4-1h0 4zm185-336l1-2h2c1 1 3 1 5 1 1 0 3 1 4 1h4c0 1 0 2-1 3s-3 2-4 2-2-1-3-1h0l-2-2c1 0 1 0 1-1-2-1-5-1-7-1z" class="P"></path><path d="M386 119h4c0 1 0 2-1 3s-3 2-4 2-2-1-3-1h0l-2-2c1 0 1 0 1-1 3 0 4 2 8 1v-1l-3-1z" class="E"></path><defs><linearGradient id="E" x1="444.231" y1="92.142" x2="456.641" y2="93.283" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#E)" d="M439 89c5 2 10 3 15 2 1 0 1 0 2 1h0c3 2 6 2 9 4-3 0-6-1-8-1l-4-1h-10c0-1-1-2-2-2-1-1-2-2-3-2l1-1z"></path><path d="M660 458h1c4 4 9 8 12 14l1 3v1c1 3 2 5 2 9 0 3 0 7-1 10 0-3 0-7-1-10 0-5-1-9-3-14s-7-9-11-13z" class="L"></path><path d="M607 565c1 1 5 3 7 3 3 2 6 4 11 5 2 1 5 2 7 3-7 0-14-2-20-5v-1h0c-1 0-1 0-1-1-1 0-1-1-2-1h-1c-1 0-1-2-1-3z" class="Z"></path><path d="M276 432c-3-5-7-8-12-9-2-1-4-1-6-1-2 1-4 1-6 1 6-2 11-3 17 0 5 2 9 6 11 10v3c-1-1-2-3-2-4h-2z" class="C"></path><path d="M302 325c2 1 3 3 5 5l1 2c3 4 6 8 9 11l1 1-1 1c-6-5-11-11-16-16l1-1v-2-1z" class="J"></path><path d="M302 325c2 1 3 3 5 5l1 2c-2-1-4-2-6-4v-2-1z" class="D"></path><path d="M288 465c0 1 0 3-1 4 0 1-2 4-2 5v1c-1 2-2 4-2 6 1 2 1 3 2 5v1c-2-2-3-4-3-6l-1-1-2 1-1-1c-1-1-3-1-5-2v-1h6c4-3 7-7 9-12z" class="T"></path><path d="M279 477s0 1 1 1 1-2 2-1v4l-1-1-2 1-1-1c-1-1-3-1-5-2v-1h6z" class="M"></path><path d="M297 307h2l5 12 2 4c0 1-1 1-1 2 1 1 2 3 3 5h-1c-2-2-3-4-5-5-1-2-2-4-3-5-1-2-2-4-2-7 0 1 1 2 1 3l1 2c1 0 1 1 2 1-1-4-2-8-4-12z" class="T"></path><path d="M299 320h1l3 3c1-1 0-2 0-2v-1l1-1 2 4c0 1-1 1-1 2 1 1 2 3 3 5h-1c-2-2-3-4-5-5-1-2-2-4-3-5z" class="W"></path><path d="M270 91c9 4 18 9 28 11-1 0-1 1-2 1-1 1-20-6-22-7-2-2-3-2-5-4l1-1z" class="C"></path><path d="M624 403c1 1 1 1 2 1 1-1 3-1 4-1 2 1 2 2 3 3l1 11h0c-1 2-4 6-7 6h-1-1v-2c-1-1-1-3-1-4l3 3 1-1c1 0 2-1 3-2 1-3 2-6 1-9 0-1-1-3-2-3-2-1-2 0-4 0 0-1 0 0-1-1l-1-1z" class="L"></path><path d="M119 337c2 0 2 0 3 1 5 7 6 17 9 25 0 4 0 8-1 11v-3l-1 2v1-3h-1c1-11-1-25-9-34z" class="C"></path><path d="M764 364l1 7c-1 8-3 15-7 23-1 2-2 4-3 5 0-1 1-2 1-3 0-3 2-6 3-9 1-4 2-9 3-13v-3c1-2 2-5 2-7z" class="X"></path><path d="M317 343h3c6 7 15 11 15 22 0 2-1 4-1 6-1-7 0-11-6-17-3-3-8-6-11-9l1-1-1-1z" class="E"></path><path d="M551 458c1 1 1 2 1 3h1c0-1 1-2 1-3l1 3c0 4 1 9 1 12 1 3 1 7 0 10l-1 2-1-2s1-1 1-2v-4c0-2-1-4-2-6-2-4-2-8-2-13z" class="U"></path><defs><linearGradient id="F" x1="552.614" y1="460.993" x2="555.186" y2="467.913" xlink:href="#B"><stop offset="0" stop-color="#312e31"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#F)" d="M552 461h1c0-1 1-2 1-3l1 3c0 4 1 9 1 12-2-3-4-8-4-12z"></path><path d="M614 407c4 2 8 6 10 10h0c0 1 0 3 1 4v2 1c-1-2-2-4-4-6h-1c-2-3-4-6-8-8-1 0-3 0-4-1 1 0 2-1 3-1s2 0 3-1z" class="J"></path><path d="M712 381c3 11 5 23 10 33-1 1-2 1-2 1l-2 1c-1 0-2-1-3-2h2c1 0 2-1 2-1 0-3-1-6-2-8-1-4-2-7-3-11s-3-8-4-12l2-1z" class="D"></path><path d="M519 235h0v4 2c1-1 0 0 0-1h1v-2h1 0c0 1-1 2-1 3v1c-1 1-1 1-1 2v1c0 1 0 1-1 2 0 1 0 1-1 2h1l2-2c2 2 6 4 6 6h0c-1-1-2-2-4-3h0l-1-1c-2 1-4 1-5 3h1c2 0 3 0 5 1h1c1 0 2 0 2 1h-2c-1 0-3-1-4-1-7-1-11 8-17 9h0l2-1c8-6 13-13 14-23l1-3z" class="L"></path><path d="M556 483h1v2c-1 3-2 5-4 8h-5c-3 1-9 1-12 2-1 1-3 1-5 1h-3c1-1 3-1 4-1 1-1 2-1 3-1 8-2 15-3 20-9l1-2z" class="B"></path><path d="M714 394c1 4 2 7 3 11 1 2 2 5 2 8 0 0-1 1-2 1h-2l-1-1c0-1-1-1-2-2 0-1-1-2-2-3 0 0 0-1-1-2l-1-2-3-6h1l6 10 1-1 1 1h0l1 1c0 1 0 1 1 1h0c0-5-2-10-2-16z" class="G"></path><path d="M712 408l1-1 1 1h0l1 1c0 1 0 1 1 1h0c1 1 1 2 1 3h-1c-1-1-3-3-4-5z" class="S"></path><path d="M583 531c3 5 4 9 5 14 2 5 5 10 9 14l5 5c0 1 0 1-1 1-10-8-16-20-19-33h1v-1z" class="K"></path><defs><linearGradient id="G" x1="759.403" y1="360.639" x2="770.759" y2="351.804" xlink:href="#B"><stop offset="0" stop-color="#565856"></stop><stop offset="1" stop-color="#767678"></stop></linearGradient></defs><path fill="url(#G)" d="M766 334c1 4 1 9 1 14 0 7 0 16-2 23l-1-7c0-7-1-14 0-20 0 2-1 4 0 6l1-1-1-14c1 2 1 3 1 5v-2-1-2l1-1z"></path><path d="M626 399c3 0 6 0 8 2 1 1 2 3 3 4-1 4-1 8-3 12l-1-11c-1-1-1-2-3-3-1 0-3 0-4 1-1 0-1 0-2-1v-1c1-1 1-1 3-1h-1l1-1-1-1z" class="C"></path><path d="M199 498c0-3 0-6 1-8v5h3 0l2-1h2c2 0 2-2 4-2 2 3 5 7 6 11l1 2-2-1c-2-2-4-5-7-6-4-2-6-2-10 0z" class="e"></path><path d="M205 494h2c2 0 2-2 4-2 2 3 5 7 6 11-1-2-3-4-4-5-3-3-6-3-10-3h0l2-1z" class="D"></path><path d="M528 333h1v1l-5 7c-2 1-4 3-4 5v1c-6 3-13 8-14 15v1 8c-1-2-1-3-1-5-2-10 6-14 12-21 4-3 8-7 11-12z" class="L"></path><path d="M100 250v-1l2 2c1 5 1 9 0 14-1 1-2 2-2 3v1c2-1 2-1 3-1 1-1 3-3 4-3h-1c0 1-1 2-2 4h0l1-1s1 0 2-1l1 1c-4 3-9 5-13 8l6-16c1-4 0-7-1-10z" class="b"></path><path d="M91 243l-11-5c3 0 5 1 7 1 5 2 11 5 16 7h6l1 1c-2 1-4 1-6 1l-2 1v2l-2-2v1h-1c-2-3-5-4-8-7z" class="C"></path><path d="M91 243c4 1 7 2 10 4 1 1 2 1 3 1l-2 1v2l-2-2v1h-1c-2-3-5-4-8-7z" class="X"></path><path d="M88 333h1c0 1 0 2-1 3h1l1-1v2c-2 2-3 3-2 6-3 4-4 7-3 11 2 6 3 12 6 17-4-4-7-9-7-14v-9c1-5 2-9 3-13l1-2z" class="O"></path><defs><linearGradient id="H" x1="74.728" y1="376.512" x2="88.647" y2="387.742" xlink:href="#B"><stop offset="0" stop-color="#434344"></stop><stop offset="1" stop-color="#757575"></stop></linearGradient></defs><path fill="url(#H)" d="M78 372v-1l1 1h0c0-1 0-2 1-2v-2h0l1-1c0 11 2 21 5 31l-3-3c-3-7-5-16-5-23z"></path><path d="M649 460l1-1s0 1 1 1c-1 3-1 6-1 9 0 0 1 3 1 4l-5 5h0-1c-2 3-5 5-6 10v-5c2-4 5-7 7-10l3-3v-2l-1-3c-1-1-1-2-1-3l2-2z" class="W"></path><path d="M647 462l2-2v8l-1-3c-1-1-1-2-1-3z" class="M"></path><path d="M626 498h0c2-2 4-3 6-3 2-1 5-1 7 1h1 1s0-1 1-1c2 4 5 7 7 11-5-4-9-11-16-10-5 1-8 4-11 8-2 3-4 10-4 15h-1v-4h0c1-7 4-13 9-17z" class="Q"></path><defs><linearGradient id="I" x1="618.861" y1="425.536" x2="625.328" y2="431.153" xlink:href="#B"><stop offset="0" stop-color="#aeafaf"></stop><stop offset="1" stop-color="#d4d3d4"></stop></linearGradient></defs><path fill="url(#I)" d="M620 418h1c2 2 3 4 4 6 0 3 0 6-1 10-1 3-3 6-5 8l-2 3-7 8-1-1c4-5 9-11 12-17 2-6 2-11-1-17z"></path><path d="M580 291h1 2c0 1-1 1-2 1h-2l-1 1c-1 0-2 0-2 1h-1c-1 0-1 1-1 1l-1 1c-5 2-12 3-16 7-2 1-3 3-4 5l-1-1c1-4 3-6 6-8l7-4 5-1 7-2 3-1z" class="N"></path><path d="M741 435c9 1 17-1 24-4l9-4c-6 7-17 9-26 10l-8 1v-1l1-1h0v-1z" class="G"></path><defs><linearGradient id="J" x1="117.203" y1="343.49" x2="129.682" y2="343.808" xlink:href="#B"><stop offset="0" stop-color="#484546"></stop><stop offset="1" stop-color="#7a7a7b"></stop></linearGradient></defs><path fill="url(#J)" d="M118 332c2 1 4 2 6 4h0l1-2c0 3 1 5 2 7v1l1 1c0 2 1 5 2 7 1 4 1 9 1 13-3-8-4-18-9-25-1-1-1-1-3-1v-1l-1-1v-1-2z"></path><path d="M280 433c4 7 5 14 5 21-1 5-3 8-5 12h-3l3-5c3-7 2-18 0-25v-3z" class="X"></path><path d="M630 313c2 0 3 2 4 4l1-1 5 7c2 2 2 4 3 6 1 0 1 1 2 1-1 1-1 2-2 4h0c-1 0-1 0-1 1l-1-1v-1h0c0-1-1-2-2-2h0v3c-1 0-1-1-1-1v-5c-1-5-5-11-8-15z" class="e"></path><path d="M635 316l5 7-1 1c-2-1-4-5-5-7l1-1z" class="J"></path><path d="M612 532l1-1v1l2 2 1 1 1 2c1 2 4 4 6 5 0-1-1-2-2-2l1-2c4 4 8 6 13 6 1 1 3 1 4 1 1 1 1 1 1 2-4 1-8 0-12-1h0c-9-3-12-8-16-14z" class="Y"></path><path d="M238 458c2 1 5 3 8 5 5 2 10 3 15 3l-3 1h0 1-2l-1 1h0-3l-2-1v1c1 2 4 3 5 4 1 0 1 0 2-1l1 1c-2 1-4 2-7 3v-1c1 0 2-1 2-1l-3-3c-1-2-5-3-7-5-1-1-3-2-5-4l-2-2 1-1z" class="C"></path><defs><linearGradient id="K" x1="683.229" y1="193.897" x2="686.454" y2="199.029" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#959495"></stop></linearGradient></defs><path fill="url(#K)" d="M695 192c1 0 1 1 1 1-5 1-10 3-13 8-1 1-1 3-1 5v-1l-1-2c-1-1-2-2-4-2l-3-1h0c3-2 5-4 7-5s5-2 7-2l7-1z"></path><defs><linearGradient id="L" x1="212.983" y1="512.018" x2="220.556" y2="495.868" xlink:href="#B"><stop offset="0" stop-color="#555558"></stop><stop offset="1" stop-color="#6c6c6b"></stop></linearGradient></defs><path fill="url(#L)" d="M211 492v-1l5 3v-1c1 1 4 3 4 4l-1 1 1 2 1 3c1 0 1 1 1 1 1 4 1 9 0 12h0c-1-4-3-8-4-11l-1-2c-1-4-4-8-6-11z"></path><path d="M688 427l1 1s0 1 1 2c0 4-4 6-6 9-4 8-7 14-7 23v11c-1-3 0-6-1-9-1-2-1-7 0-10 0-2 0-4 1-6 1-3 3-6 4-9v-1c0-3 6-7 7-11z" class="O"></path><defs><linearGradient id="M" x1="725.239" y1="332.521" x2="709.253" y2="347.648" xlink:href="#B"><stop offset="0" stop-color="#404041"></stop><stop offset="1" stop-color="#656465"></stop></linearGradient></defs><path fill="url(#M)" d="M715 337c2-4 6-6 10-7l1 1h0c-1 1-1 1-2 1l1 1c-3 2-5 4-7 6-5 7-7 17-8 25-1-10 2-18 5-27z"></path><path d="M692 420h-1c1-1 4 0 5 0 4 0 5 2 7 3 2 2 4 3 5 4l1 1c1 2 4 3 6 4 1 0 3 1 4 1v1l-1 1-19-9c-2-1-3-1-5 0h-1l1-3c0-1-1-2-2-3z" class="Q"></path><path d="M213 420l1-1c2-4 6-10 11-12 3-1 6-1 9 1h-1c0 1 1 2 2 3l-1 2-3-3c-4 0-8 3-11 6l-2 3v-3c-2 2-3 4-4 6h0l-1-2z" class="L"></path><path d="M227 409c2-1 3-1 6-1 0 1 1 2 2 3l-1 2-3-3h-3l-1-1z" class="E"></path><path d="M227 409l1 1h3c-4 0-8 3-11 6l-2 3v-3c2-4 5-6 9-7z" class="G"></path><path d="M247 80c2 1 22 11 23 11l-1 1c2 2 3 2 5 4l-18-7v-1c-2 0-3-1-4-1s-2-1-2-2h0c-1-1-1-1-1-2 1 1 1 1 3 1-2-1-3-2-5-4h0z" class="c"></path><path d="M751 293v-1-1c0-2 0-2 1-3 3 8 7 16 9 24 1 3 2 7 3 11 1 3 2 7 2 11l-1 1v2 1 2c0-2 0-3-1-5-1-5-1-10-3-15-2-9-5-18-10-27z" class="J"></path><path d="M565 426c1-1 2-2 4-3s6-1 8 0c-5 2-8 4-11 8-3 6-3 16-1 21l1 3h0c-2 0-3-1-4-2v-2c-1-6 0-11 1-18 1-1 2-3 2-4l-1-1 1-2z" class="R"></path><path d="M122 209l7 3c0 2 1 12 0 12l-1 6c-3 8-9 14-17 17h-1l-1-1 6-3c7-5 11-11 12-20h0v-5c0-3-3-6-5-9z" class="I"></path><path d="M122 209l7 3c0 2 1 12 0 12v-2-6c-1 2-1 5-2 7h0 0v-5c0-3-3-6-5-9z" class="D"></path><path d="M256 89l18 7c2 1 21 8 22 7 6 2 12 4 15 9 0 1 1 3 1 4l-1 1v-2c-1-1-1-2-1-2-4-6-12-8-19-9l-12-4-13-5-3-1h-1c-1-1-2 0-4 0-1-2-2-4-2-5z" class="S"></path><path d="M128 230l1 1c-2 7-7 13-13 17-3 2-11 2-12 6 0 3 0 9-2 11h0 0c1-5 1-9 0-14v-2l2-1c2 0 4 0 6-1h1c8-3 14-9 17-17z" class="d"></path><defs><linearGradient id="N" x1="212.434" y1="423.934" x2="221.463" y2="431.037" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#7c7d7f"></stop></linearGradient></defs><path fill="url(#N)" d="M218 416v3c-2 5-2 9 0 14 0 3 2 5 4 7 4 7 9 13 16 18l-1 1c-7-6-13-12-18-19-1-2-3-5-4-7l-1-1c0-2-3-6-4-7l2-1c2 0 2 0 2-2 1-2 2-4 4-6z"></path><defs><linearGradient id="O" x1="739.497" y1="204.227" x2="735.748" y2="214.795" xlink:href="#B"><stop offset="0" stop-color="#898789"></stop><stop offset="1" stop-color="#afb1b0"></stop></linearGradient></defs><path fill="url(#O)" d="M717 191c0-1 1-1 2 0 2 0 4 1 6 2 4 2 7 6 10 9 0-1-1-2-2-2v-1c-1-2-3-3-4-5v-1c6 4 10 11 11 18 1 3 0 7-1 11-1-9-2-16-8-22h-2l-4-3-2-1c0-1-1-1-2-1l2-1-2-1-4-2z"></path><path d="M723 194c2 1 5 2 6 4v2l-4-3-2-1c0-1-1-1-2-1l2-1z" class="O"></path><path d="M306 103c6-1 12-2 18-5 7-3 13-8 20-8-9 5-21 10-27 19h-1v-1h1v-1l4-4c-4 2-7 4-12 3h0-1 0c-1 0-2 0-3-1h3v1c4 0 6 0 9-2 0-1-1 0-1 0h-1-7-3-1v1c-1 0-1-1-2-1h-2l-2-1c3 0 6 1 8 0z" class="I"></path><defs><linearGradient id="P" x1="679.981" y1="461.646" x2="671.288" y2="464.252" xlink:href="#B"><stop offset="0" stop-color="#8d8a8d"></stop><stop offset="1" stop-color="#afafae"></stop></linearGradient></defs><path fill="url(#P)" d="M681 438v1c-1 3-3 6-4 9-1 2-1 4-1 6-1 3-1 8 0 10 1 3 0 6 1 9 0 3 0 8-1 12 0-4-1-6-2-9v-1c0-2-1-7-2-10v-7c2-8 5-14 9-20z"></path><defs><linearGradient id="Q" x1="654.623" y1="457.666" x2="673.168" y2="462.056" xlink:href="#B"><stop offset="0" stop-color="#333335"></stop><stop offset="1" stop-color="#545454"></stop></linearGradient></defs><path fill="url(#Q)" d="M657 451c1 1 2 1 3 1 3 1 7 5 9 8l2 1h1 0v-3 7c1 3 2 8 2 10l-1-3c-3-6-8-10-12-14h-1c-1 0-2-1-3-1-1-1-3-1-4-1-1-1 0-1-1-1 1 0 1-1 1-1 2-1 3-2 4-3z"></path><path d="M104 254c4 0 9-1 12-4 1-1 2-1 3-2h1l-13 17c-1 0-3 2-4 3-1 0-1 0-3 1v-1c0-1 1-2 2-3h0 0c2-2 2-8 2-11z" class="L"></path><defs><linearGradient id="R" x1="227.571" y1="535.419" x2="215.885" y2="538.498" xlink:href="#B"><stop offset="0" stop-color="#4f4e52"></stop><stop offset="1" stop-color="#6d6c6a"></stop></linearGradient></defs><path fill="url(#R)" d="M223 535c3-3 5-6 9-7-2 2-3 3-4 5-6 7-11 12-20 14-3 1-9 0-12 0 3-1 5-2 8-2 2 0 4-1 5-2 5-1 9-5 11-10l1 1h2v1z"></path><path d="M220 533l1 1h2v1c-4 6-12 10-19 12l-1-1s0-1 1-1c2 0 4-1 5-2 5-1 9-5 11-10z" class="b"></path><path d="M555 445v4c1 1 1 3 1 4l2 5 3 6-1 1c0 1 1 3 0 3l-1 2c1 2 1 3 2 5l-1 1s-1 0-1-1v1c-2 3-1 6-1 9h-1v-2h-1c1-3 1-7 0-10 0-3-1-8-1-12l-1-3v-8l1-5z" class="H"></path><path d="M557 467v-1l2 2v2c1 2 1 3 2 5l-1 1s-1 0-1-1c-1-3-2-5-2-8z" class="V"></path><path d="M555 445v4c1 1 1 3 1 4-1 2 0 8 0 10-1 0-1-1-1-2l-1-3v-8l1-5z" class="e"></path><path d="M557 467c-1-3-1-6 0-9h1l3 6-1 1c0 1 1 3 0 3l-1 2v-2l-2-2v1z" class="G"></path><path d="M191 468c0-1-1-3-1-4v-5l3 8v1h0c3 4 7 7 8 12 1 2 1 6 1 9v5s1 0 1 1h0-3v-5-4c-1-2-2-4-3-5l-7-8-1-2v-1-1c1-1 1-1 2-1z" class="B"></path><path d="M189 469c1-1 1-1 2-1h0c0 2 1 3 2 4v1c-1-1-3-1-4-2v-1-1z" class="M"></path><path d="M190 473l3 1c1 1 6 6 7 8v4c-1-2-2-4-3-5l-7-8z" class="X"></path><path d="M99 316v2c-2 3-8 8-8 11l2-1-4 8 5-4 1 1h1c2-2 4-2 6-3l1-1c1 1 1 0 2 0h5c3 1 5 1 8 3v2 1l1 1c-1-1-2-1-3-2h-1c-2-2-6-2-9-2h-2c-5 0-10 3-14 7l-2 4c-1-3 0-4 2-6v-2l-1 1h-1c1-1 1-2 1-3h-1c0-1 1-4 2-5v-1c3-3 6-7 9-11z" class="T"></path><defs><linearGradient id="S" x1="102.572" y1="330.475" x2="117.588" y2="332.021" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#474747"></stop></linearGradient></defs><path fill="url(#S)" d="M105 329h5c3 1 5 1 8 3v2 1l1 1c-1-1-2-1-3-2h-1c-2-2-6-2-9-2h-2c-1-1-2-1-4-1-3 1-5 3-8 5v-1c0-1 1-1 3-2h1c2-2 4-2 6-3l1-1c1 1 1 0 2 0z"></path><path d="M177 136c4 3 6 8 11 8 9 0 18-8 24-14h0c0 3-2 3-3 5v1c2-1 4-4 6-4-4 4-8 8-13 11-5 2-10 4-16 4l-3-2-9-9c1 0 0-1 1-1s1 1 2 1z" class="h"></path><path d="M122 209c-1-1-2-2-3-4 3 1 6 3 9 4 8 3 20 4 27 1h0v-1h2c0 3-1 5-2 7l-2 3-3 2s-1 0-1-1l-1 1v-5c-1-1-1-2-2-2-1-1-3 0-4-1l-13-1-7-3z" class="c"></path><path d="M155 210v-1h2c0 3-1 5-2 7l-2 3h-1c-1-1-3-3-3-4v-1l-1-1c2-1 5-1 7-3h0z" class="N"></path><defs><linearGradient id="T" x1="643.469" y1="345.159" x2="635.041" y2="347.263" xlink:href="#B"><stop offset="0" stop-color="#a4a4a7"></stop><stop offset="1" stop-color="#d7d7d5"></stop></linearGradient></defs><path fill="url(#T)" d="M639 334v-3h0c1 0 2 1 2 2h0v1l1 1c0-1 0-1 1-1h0v2 26c-1 0-1 0-1 1-1 1-1 2-1 3-1 3 0 5-1 8v-5l-1-1c0-1 0-3 1-5l-2-2c1-9 1-18 1-27z"></path><path d="M640 363c1-6 1-12 2-17 0-3-1-6 0-8 0 0 0-1 1-2v26c-1 0-1 0-1 1-1 1-1 2-1 3-1 3 0 5-1 8v-5l-1-1c0-1 0-3 1-5z" class="Q"></path><defs><linearGradient id="U" x1="526.493" y1="225.847" x2="514.602" y2="236.162" xlink:href="#B"><stop offset="0" stop-color="#a8a6a7"></stop><stop offset="1" stop-color="#d6d4d6"></stop></linearGradient></defs><path fill="url(#U)" d="M517 223c1-1 3-1 5-1l9-1c1 1 1 2 2 2h2c-2 1-3 1-5 1-4 2-5 5-7 8l-2 6h0-1v2h-1c0 1 1 0 0 1v-2-4h0l-1 3c0-5-1-9-3-14v-1h2z"></path><path d="M515 224v-1h2 3c1 1 3 1 4 1v1c-3 1-5-1-7 0 1 3 2 6 2 9v1l-1 3c0-5-1-9-3-14z" class="K"></path><path d="M517 223c1-1 3-1 5-1l9-1c1 1 1 2 2 2h2c-2 1-3 1-5 1-4 2-5 5-7 8 0-2 0-3 1-4s1-2 1-4h-1c-1 0-3 0-4-1h-3z" class="J"></path><defs><linearGradient id="V" x1="168.236" y1="474.558" x2="156.919" y2="470.283" xlink:href="#B"><stop offset="0" stop-color="#8e8e8e"></stop><stop offset="1" stop-color="#c2c1c4"></stop></linearGradient></defs><path fill="url(#V)" d="M159 445l1-1 3 6c1 2 1 5 2 8l-1 8v3h0v2c1-1 4-6 5-7 0 1-2 3-2 4l1 1c-2 5-4 10-4 16 0 3-1 7-2 10-1-5-1-11-1-16v-13c1-3 2-7 2-10-1-4-2-8-4-11z"></path><path d="M169 464c0 1-2 3-2 4l1 1c-2 5-4 10-4 16v-4c-1-2-1-4-1-7s0-5 1-8v3h0v2c1-1 4-6 5-7z" class="Z"></path><defs><linearGradient id="W" x1="591.965" y1="283.089" x2="610.571" y2="304.089" xlink:href="#B"><stop offset="0" stop-color="#383637"></stop><stop offset="1" stop-color="#535453"></stop></linearGradient></defs><path fill="url(#W)" d="M591 290c8 0 15 1 23 3 3 2 6 3 9 5-2-1-6-1-7 0-14-5-28-6-42-3 0 0 0-1 1-1h1c0-1 1-1 2-1l1-1h2c1 0 2 0 2-1h-2c3-1 7-1 10-1z"></path><path d="M197 361c1-2 1-4 1-6l1 3h0c1 1 1 2 2 3 0 0 1 1 1 2 1 5 3 10 4 15 2 5 6 10 11 13 4 3 9 4 14 6h-1c-4 0-8-2-12-3-5-3-9-5-12-10-1-3-2-7-4-10-1-2-2-4-3-5-1 0-2 0-3-1l1-7z" class="D"></path><path d="M199 358c1 1 1 2 2 3 0 0 1 1 1 2 1 5 3 10 4 15-4-7-6-13-7-20z" class="T"></path><path d="M758 356v-1-5 3c1 0 1 0 1-1 1 2 1 8 2 9v3c0 1 0 2 1 3v7c-1 4-2 9-3 13-1 3-3 6-3 9 0 1-1 2-1 3-2 3-3 5-6 7 1-3 4-8 5-11 4-11 4-21 3-32 1-3 0-5 1-7z" class="C"></path><path d="M602 526c1 0 1-1 2-1h2 1c1 1 3 1 4 3l3 3v1-1s0-1-1-1v-1s0-1-1-1c-1-2-2-3-3-5v-1c-1-1-1-1-1-2h0l1 1h1c3 1 4 3 6 6 0-3-1-6 0-8v-1l-1-1 1-1v-1h1 0v4h1 0v7c0 4 1 8 3 11l1 1-1 2c1 0 2 1 2 2-2-1-5-3-6-5l-1-2-1-1-2-2v-1l-1 1c-2-3-4-5-7-5-2 1-3 2-4 3-1 3 1 6 1 9-1 1-1 3-1 4-2-3-2-8-2-12 1-1 3-3 3-5z" class="G"></path><path d="M441 115c-1-2-3-4-3-7-1-2-1-5 1-8s9-4 13-4l-3 3c1 0 1 0 1 1-2 2-6 4-7 7v4c1 2 1 3 3 4 3 2 6 2 10 2 2 0 4-1 6 0v1c-4 0-8 1-12 1-2-1-4-2-7-3l-2-1z" class="Q"></path><path d="M449 99c1 0 1 0 1 1-2 2-6 4-7 7v4 1h-1c0-2-1-5-1-7 1-3 6-5 8-6z" class="C"></path><path d="M223 570c1 1 4 0 6-1s4-2 6-4c11-9 17-22 21-35 3-9 6-18 13-24h1c-3 4-6 8-8 12-3 5-4 12-6 17-3 10-8 20-16 27-7 8-18 12-29 14-2 0-2 1-4-1l16-5z" class="R"></path><defs><linearGradient id="X" x1="221.955" y1="498.536" x2="227.133" y2="510.455" xlink:href="#B"><stop offset="0" stop-color="#6e6e6e"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#X)" d="M220 497l3 3h0c3 3 6 3 9 6l1 1h0 1 0 0c1 2 1 3 0 4h-1c-2 0-2 0-4 1l-1 1h0c-1 1-1 4-2 5 0 3 0 5-2 7l-1 3c0 1 0 1-1 1 1-4 0-8 0-13h0c1-3 1-8 0-12 0 0 0-1-1-1l-1-3-1-2 1-1z"></path><path d="M223 500c3 3 6 3 9 6l1 1h0 1 0v2c-3-2-5-2-8-3-1-2-3-3-3-6z" class="H"></path><path d="M220 500c4 4 5 11 5 16l-1 9-1 3c0 1 0 1-1 1 1-4 0-8 0-13h0c1-3 1-8 0-12 0 0 0-1-1-1l-1-3z" class="Z"></path><path d="M343 137v-9c0-2-1-5-2-7 2 3 4 7 6 10s5 5 8 7c0 0 2 1 2 2l-4 1v1h0c1 0 2 1 2 2-4 0-7 2-11 2h-1-1c-1 0-1 0-2 1h-2v1h-1-3c1-1 3-3 5-3l2-1s0-1 1-1l2-1c-1-1-1-1-1-2h0v-3z" class="C"></path><defs><linearGradient id="Y" x1="292.152" y1="284.267" x2="301.522" y2="284.337" xlink:href="#B"><stop offset="0" stop-color="#262626"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#Y)" d="M312 254h4l-3 2c-8 5-16 14-17 23-2 8-1 15 1 22 0 2 1 4 2 6h-2c-1-2-3-6-3-9v-6c-1-2-1-5-2-7h0c-1-1-1-2-1-3h0c1-1 0-2 0-3 1-1 2-4 2-5 2-4 5-9 8-11 3-3 7-6 10-9h1z"></path><path d="M312 254h4l-3 2c-1 0-3 1-4 1-2 1-6 6-7 6h-1c3-3 7-6 10-9h1z" class="F"></path><defs><linearGradient id="Z" x1="288.776" y1="113.028" x2="298.227" y2="102.674" xlink:href="#B"><stop offset="0" stop-color="#aeacaa"></stop><stop offset="1" stop-color="#dedee0"></stop></linearGradient></defs><path fill="url(#Z)" d="M273 106l-1-1c-1-1 0 1-1-1-1-1-3-3-3-5 2-1 8 1 11 1h0l12 4c7 1 15 3 19 9 0 0 0 1 1 2l-1 1h-1s-3-4-4-4c-3-2-6-3-9-4h-2c-3-1-7-3-10-2l-8-3-1 1v3l-1 1h0l-1-2z"></path><path d="M273 106c-1-1-2-3-3-5 2 0 5 0 7 1v1h-1l-1 1v3l-1 1h0l-1-2z" class="I"></path><defs><linearGradient id="a" x1="683.817" y1="154.358" x2="669.64" y2="159.046" xlink:href="#B"><stop offset="0" stop-color="#8f9092"></stop><stop offset="1" stop-color="#aba9a9"></stop></linearGradient></defs><path fill="url(#a)" d="M664 144s1-1 1-2l1 1c4 11 13 18 22 26 2 1 4 4 6 5h1c-2 1-3 2-5 2l1-1-1-1c1 0 0 0 1-1-2-1-4-1-6-2h-4v-1c-3-2-5-4-7-7l-9-12h2l-3-6v-1z"></path><path d="M667 151c5 7 11 15 18 20h-4v-1c-3-2-5-4-7-7l-9-12h2z" class="F"></path><defs><linearGradient id="b" x1="622.059" y1="113.912" x2="641.643" y2="149.294" xlink:href="#B"><stop offset="0" stop-color="#7b7b7d"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#b)" d="M611 110l1-1c2 3 2 4 5 5 4 11 15 24 26 28 1 1 5 1 5 2 1 1 1 2 2 3h0c-2 0-3 0-5-1-14-4-24-16-30-29 0-1-3-6-4-7z"></path><path d="M298 496c2 1 6 0 8 0h0-1v1h0c2 1 3 1 5 0-11 4-21 5-32 4-3 0-6 0-9-1h-3c-4 0-6 0-10 2v-1c2-1 3-2 5-2h0c4-1 8 0 12-1h1c1 0 1 1 2 0h4-1-2c-2 0-3 0-4-1h0 3 0c1-1 2-1 3-1 6 1 13 1 19 0z" class="B"></path><path d="M298 496c2 1 6 0 8 0h0-1v1h0c-8 1-16 1-24 1l-5-1h0c1-1 2-1 3-1 6 1 13 1 19 0z" class="D"></path><path d="M273 497h3l5 1h0c4 1 8 0 12 0-3 1-5 1-8 1h-11c-1 0-4-1-5 0v1h-3c-4 0-6 0-10 2v-1c2-1 3-2 5-2h0c4-1 8 0 12-1h1c1 0 1 1 2 0h4-1-2c-2 0-3 0-4-1h0zm-69-73l9 9 1 1h0c1 1 1 1 1 2 1 1 2 2 3 4 0 1 1 2 2 2l-3-4c-1-1-1-2-2-4h0l-1-2 1 1c1 2 3 5 4 7 5 7 11 13 18 19l2 2h0c-2 0-5-3-7-5-2 0-3-1-4-2-2-2-4-4-7-5h0c-3-1-4-5-6-6l-3-3v1h0l-7-12h1c-1-2-2-3-2-5z" class="f"></path><path d="M205 429h1l3 3c3 3 5 6 8 10l7 7 8 7c-2 0-3-1-4-2-2-2-4-4-7-5h0c-3-1-4-5-6-6l-3-3v1h0l-7-12z" class="O"></path><path d="M209 432c3 3 5 6 8 10l7 7c-2-1-4-2-6-4l-3-3c-1-2-2-3-4-4-1-2-2-4-2-6z" class="G"></path><path d="M575 499c2 0 3 0 4-1 1 0 1 1 3 1-1-1-3-1-3-1 1-1 1-1 2-1l1-1c1 0 3 1 4 0h1l1 1 1 1c1 1 1 2 1 2l-2-1-1-1c-1 1-2 1-3 2v4c1 1 1 3 1 5l-1 7c0-2-1-5-3-6l-3-3c1 2 3 5 3 7v1c-1 0-1-1-2-2s-2-2-4-3c-1-1-2-1-3-2h-1s-2-3-3-3l1-1h0l1 1h1 2l1 1h1 1l-1-1c-1 0-1-1-2-1 0 0-1 0-1-1h0 4c1 1 3 1 4 2l1 1v-1h0c-1-1-3-3-5-3-3-1-5 0-8 0-1 0-3 0-5-1 3-1 5-1 8-2 1 0 3-1 4 0z" class="P"></path><path d="M138 426c1-1 3-2 5-2h2v-1l2-2h2v1c-1 0-2 1-2 2-1 0 0 3-1 3h-1c-2-1-5-1-7 0-3 1-6 3-9 4-8 4-16 7-24 7-4 0-9 0-13-1-5-1-10-1-14-3-1-1-3-1-4-2l2-1c1 1 3 1 5 2 7 2 13 2 20 2h4l1 1c3 0 6-1 9-1v-1c2 0 4-2 6-3l13-6 4 1z" class="R"></path><path d="M134 425l4 1c-3 1-20 9-23 9v-1c2 0 4-2 6-3l13-6z" class="M"></path><path d="M637 405c2 3 2 6 2 9v1 2 1c-1 5-5 9-8 13l-6 7c-3 3-5 7-8 9v-2l2-3c2-2 4-5 5-8 1-4 1-7 1-10v-1h1 1c3 0 6-4 7-6h0c2-4 2-8 3-12z" class="B"></path><path d="M634 417l1 1c-1 1-2 3-3 5-2 1-4 3-6 5h0c0-1-1-4 0-5h1c3 0 6-4 7-6z" class="N"></path><path d="M571 508h1c1 1 2 1 3 2 2 1 3 2 4 3s1 2 2 2v-1c0-2-2-5-3-7l3 3c2 1 3 4 3 6l1-7c1 1 0 10 0 12 1 5 3 20 6 24l1-1c1 2 1 4 1 6l3 6 1 3c-4-4-7-9-9-14-1-5-2-9-5-14v1h-1c-2-5-3-11-6-16h0l-5-8z" class="Q"></path><path d="M576 516l1-1c2 2 3 4 4 6v1c1 3 1 6 2 9v1h-1c-2-5-3-11-6-16z" class="I"></path><path d="M627 137s-1-2-2-2c-3-3-5-7-8-11 0-1-2-2-2-3s0-1-1-1c0-1 1-2 1-3 6 13 16 25 30 29 2 1 3 1 5 1 6-2 10-8 14-12v9 1l3 6h-2l-2-4c-1 1-3 2-5 3s-3 2-6 2v1l1 1-1 2c-1-2-2-3-3-5l-1-1h-3-1c-3-1-4-3-6-4-4-3-7-6-11-9z" class="P"></path><path d="M638 146c3 1 6 3 10 3 6 1 9-2 14-5 1 0 1 1 2 2v-1l3 6h-2l-2-4c-1 1-3 2-5 3s-3 2-6 2v1l1 1-1 2c-1-2-2-3-3-5l-1-1h-3-1c-3-1-4-3-6-4z" class="X"></path><path d="M199 347c0-6-1-11 0-16 0-6 4-12 7-17 2-4 4-9 7-12 2-4 5-6 9-7l2 2-1 1h0v1c-2 2-4 3-6 4-10 10-16 23-16 37v17c0 2 1 4 1 6 0-1-1-2-1-2-1-1-1-2-2-3h0v-11z" class="P"></path><defs><linearGradient id="c" x1="296.382" y1="300.712" x2="285.813" y2="316.323" xlink:href="#B"><stop offset="0" stop-color="#555355"></stop><stop offset="1" stop-color="#828282"></stop></linearGradient></defs><path fill="url(#c)" d="M280 292l1-1h0c1-1 2-1 2-1l1 1 1 2c1 3 3 6 5 9l1-1 3 7 3 5c0 3 1 5 2 7 1 1 2 3 3 5v1 2l-1 1c-4-6-9-11-13-18l-5-7v-3c-1-2-5-3-6-5h-1 3c2 1 2 2 4 2l-3-6z"></path><path d="M285 293c1 3 3 6 5 9l1-1 3 7h-1c-1-1-1-2-2-2h-1c-3-3-4-9-5-13z" class="Z"></path><path d="M276 296h3c2 1 2 2 4 2 2 2 3 5 4 7s1 3 2 4v1l-1 1-5-7v-3c-1-2-5-3-6-5h-1z" class="H"></path><defs><linearGradient id="d" x1="193.436" y1="478.259" x2="207.232" y2="478.075" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#525152"></stop></linearGradient></defs><path fill="url(#d)" d="M193 463v-2l3 3c1 2 3 4 4 6 0 1 1 2 2 2h0l3 3h-1-1c3 4 8 7 11 11v1c-2 0-3-1-4-2 1 3 4 6 6 8v1l-5-3v1c-2 0-2 2-4 2h-2l-2 1c0-1-1-1-1-1v-5c0-3 0-7-1-9-1-5-5-8-8-12h0v-5z"></path><defs><linearGradient id="e" x1="204.241" y1="487.153" x2="215.482" y2="491.977" xlink:href="#B"><stop offset="0" stop-color="#5b5b5b"></stop><stop offset="1" stop-color="#797778"></stop></linearGradient></defs><path fill="url(#e)" d="M205 494v-7c-1-2-1-3-1-5h0c0 2 0 3 1 4v-2c1-1 2-1 3-1s1 1 2 2c1 3 4 6 6 8v1l-5-3v1c-2 0-2 2-4 2h-2z"></path><defs><linearGradient id="f" x1="282.644" y1="480.406" x2="282.991" y2="494.283" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#3e3e3e"></stop></linearGradient></defs><path fill="url(#f)" d="M272 489c-1-1-2-2-4-3v-1-1-2c1-2 3-4 5-5v1c2 1 4 1 5 2l1 1 2-1 1 1c0 2 1 4 3 6 1 1 3 3 5 4s6 2 7 4c1 0 1 1 1 1-6 1-13 1-19 0h0c2-1 5-1 7 0h0 8 1l1-1c0-1-1-1-2-2l-1 1h0v1l-1 1-1-1h-4l-9-3h-2c-1-2-1-2-3-3h-1z"></path><path d="M272 489c-1-1-2-2-4-3v-1-1-2c1-2 3-4 5-5v1c2 1 4 1 5 2v1c-1 0-1-1-2 0s-2 1-3 2l-1 1h-1 0v1l1 1h0c-1-1-2-1-2-2s1-2 1-3h-1c-1 0-1 1-1 2 0 3 2 4 3 6z" class="N"></path><path d="M234 507h1l1 2v4 2c1 0 1 1 1 1 0 2 1 3 2 4 0 1 0 3 1 4v2s-1 0-2 1h-1l-1 1h0l-2-1-2 1c-4 1-6 4-9 7v-1h-2l-1-1v-3l1-1 1 1v-1c1 0 1 0 1-1l1-3c2-2 2-4 2-7 1-1 1-4 2-5h0l1-1c2-1 2-1 4-1h1c1-1 1-2 0-4z" class="L"></path><path d="M230 517v-1c0-1 1-2 2-3h1v3c1-1 1-2 1-3v1h1v-1c0 1 0 2 1 3h1c0 2 1 3 2 4 0 1 0 3 1 4v2s-1 0-2 1h-1l-1 1h0l-2-1 2-1v-2h1v-2-1l1-1h0 0c0-1-1-2-1-2l-3 3c-1 1-2 3-3 4l-1 1h0l4-9h0c0-1 0 0-1-1l-1 1v-1-2c-1 1-1 2-2 3z" class="C"></path><path d="M234 507h1l1 2v4 2c1 0 1 1 1 1h-1c-1-1-1-2-1-3v1h-1v-1c0 1 0 2-1 3v-3h-1c-1 1-2 2-2 3v1l-1 5c0 1-1 1-1 2l-1 1v1c-1 1-1 1-1 2l-1 2-2 1-1 1v-2-1c1 0 1 0 1-1l1-3c2-2 2-4 2-7 1-1 1-4 2-5h0l1-1c2-1 2-1 4-1h1c1-1 1-2 0-4z" class="U"></path><path d="M716 207c2-1 3-2 5-2-4 5-10 10-9 16 0 8 3 14 8 19 4 3 9 6 14 6 1 0 3-1 5-2l8-3 11-4c-2 2-5 4-8 5-4 3-9 5-12 10v-1h-1c-1 0-2-1-3 0l-1-1c-1-1-2-1-3-1l-1-1c-1 0-2 0-3-1-4-2-7-5-11-8 0-1-1-2-2-4-4-5-5-11-4-18l1-3 1-2v-1l4-2c1 0 2-1 2-2h-1z" class="F"></path><path d="M711 212l1 1c-1 4-2 11-1 16 1 2 3 5 2 6-4-5-5-11-4-18l1-3 1-2z" class="C"></path><path d="M726 247c3 0 4 1 7 1 4 1 7-3 11-4l6-2c-4 3-9 5-12 10v-1h-1c-1 0-2-1-3 0l-1-1c-1-1-2-1-3-1l-1-1c-1 0-2 0-3-1z" class="V"></path><path d="M653 154l-1-1v-1c3 0 4-1 6-2s4-2 5-3l2 4 9 12c2 3 4 5 7 7v1l-3-1h-1v-1c-5 0-9-3-13-5-2-1-3-3-6-3 0 0 0 1-2 1v-1l-1-1c-1-2-2-2-3-3v-1l1-2z" class="E"></path><path d="M653 154c2 2 3 5 5 7 0 0 0 1-2 1v-1l-1-1c-1-2-2-2-3-3v-1l1-2z" class="G"></path><defs><linearGradient id="g" x1="260.633" y1="488.541" x2="267.411" y2="529.521" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#565656"></stop></linearGradient></defs><path fill="url(#g)" d="M250 513c-1-7 0-16 1-23l1-1c6 2 11 6 17 6 1 1 3 1 4 2h0c1 1 2 1 4 1h2 1-4c-1 1-1 0-2 0h-1c-4 1-8 0-12 1h0c-2 0-3 1-5 2v1h0v1c-4 4-3 13-3 18-1 5-1 9-2 14v-1c0-1-1-1-1-2h0c-1 1-1 1-1 2v3h0l-2 2c1-4 2-8 3-11v-15z"></path><defs><linearGradient id="h" x1="593.207" y1="290.935" x2="593.309" y2="286.547" xlink:href="#B"><stop offset="0" stop-color="#939292"></stop><stop offset="1" stop-color="#aeacad"></stop></linearGradient></defs><path fill="url(#h)" d="M581 282h7c5 1 11 3 15 6l9 3 2 1v1h0c-8-2-15-3-23-3-3 0-7 0-10 1h-1l-3 1-7 2-5 1c-1 0-1 0-1-1 2-2 4-3 6-5 3-4 6-6 11-7z"></path><path d="M580 291c-2 0-3-1-5-1-1 0-2 1-3 0h0c1-1 1-2 2-2 7-5 13-3 20-2 1 0 2 1 3 1h0c-6 0-12-2-17-1-3 1-5 2-7 4 2 0 3 0 4-1 4-1 10 0 14 1-3 0-7 0-10 1h-1z" class="Q"></path><path d="M571 289c2-1 3-3 6-4h1l1-1c5-1 11 0 15 2-7-1-13-3-20 2-1 0-1 1-2 2h0c1 1 2 0 3 0 2 0 3 1 5 1l-3 1-7 2c-1-1-1-1-1-2 0-2 1-2 2-3z" class="D"></path><path d="M570 294c-1-1-1-1-1-2 0-2 1-2 2-3v2c2 1 3 1 5 1h1l-7 2z" class="F"></path><path d="M581 282h7c5 1 11 3 15 6l-6-1c-1 0-2-1-3-1-4-2-10-3-15-2l-1 1h-1c-3 1-4 3-6 4-1 1-2 1-2 3 0 1 0 1 1 2l-5 1c-1 0-1 0-1-1 2-2 4-3 6-5 3-4 6-6 11-7z" class="Y"></path><path d="M228 291c2 1 7 0 9 0l10-1c7 0 16 1 23 4h-2c-3-1-5-1-8-1-12-1-26 0-37 5h0l1-1-2-2c-4 1-7 3-9 7-3 3-5 8-7 12-3 5-7 11-7 17-1 5 0 10 0 16-1-1-1-3-1-4h0v4h0l-1-5v-1c0-3-1-7-2-10 0-2 1-3 2-5 1-5 4-9 6-14 3-5 6-11 11-14 2-3 7-4 10-6h2c1-1 1-1 2-1z" class="a"></path><defs><linearGradient id="i" x1="554.12" y1="485.015" x2="560.896" y2="500.508" xlink:href="#B"><stop offset="0" stop-color="#b0afaf"></stop><stop offset="1" stop-color="#deddde"></stop></linearGradient></defs><path fill="url(#i)" d="M584 485l2 1c0 1 1 2 0 3v1l-1 4-2 1c-2 2-6 2-8 4-1-1-3 0-4 0-3 1-5 1-8 2-4 1-9 1-14 1-6-1-12-1-17-4l-6-1v-1h2 3 5c13 2 25 1 38-4 4 0 4-2 7-4 2-1 2-1 3-3z"></path><path d="M574 495c5-1 9-2 12-5l-1 4-2 1c-2 1-4 1-6 2-1 0-2-1-3-2z" class="B"></path><defs><linearGradient id="j" x1="558.974" y1="502.163" x2="559.891" y2="495.099" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#504c50"></stop></linearGradient></defs><path fill="url(#j)" d="M574 495c1 1 2 2 3 2 2-1 4-1 6-2-2 2-6 2-8 4-1-1-3 0-4 0-3 1-5 1-8 2-4 1-9 1-14 1-6-1-12-1-17-4 15 2 28 2 42-3z"></path><defs><linearGradient id="k" x1="585.466" y1="464.275" x2="600.869" y2="465.26" xlink:href="#B"><stop offset="0" stop-color="#aeadae"></stop><stop offset="1" stop-color="#d0cece"></stop></linearGradient></defs><path fill="url(#k)" d="M617 445v2l-18 18c-2 4-5 7-8 10-2 1-4 1-7 1h0c-3 0-6-3-9-4 0-3 0-6-2-8v-1-1l6 3h6c10-2 17-7 24-13l1 1 7-8z"></path><path d="M617 445v2l-18 18h-1c-4 4-8 10-15 9h-1l-1-1h2c2-1 5-2 7-3 3-2 5-4 8-6l12-11 7-8z" class="P"></path><path d="M573 462l6 3h6c-1 0-2 2-3 4v2c0 1 1 1 1 2h-2l1 1h1c7 1 11-5 15-9h1c-2 4-5 7-8 10-2 1-4 1-7 1h0c-3 0-6-3-9-4 0-3 0-6-2-8v-1-1z" class="Z"></path><path d="M579 465h6c-1 0-2 2-3 4v2c0 1 1 1 1 2h-2c0-1-1-1-2-1-1-1-1-5-1-7h1z" class="F"></path><defs><linearGradient id="l" x1="133.016" y1="211.291" x2="137.687" y2="222.537" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#l)" d="M129 212l13 1c1 1 3 0 4 1 1 0 1 1 2 2v5c-1 4-1 6-3 9v-1-2l1-1c1-2 1-4 0-6h-2c-2 0-4 0-5 1-3 2-4 6-5 9-3 3-5 8-7 11s-5 5-7 7h-1c-1 1-2 1-3 2-3 3-8 4-12 4 1-4 9-4 12-6 6-4 11-10 13-17l-1-1 1-6c1 0 0-10 0-12z"></path><defs><linearGradient id="m" x1="616.888" y1="303.859" x2="633.105" y2="306.392" xlink:href="#B"><stop offset="0" stop-color="#717171"></stop><stop offset="1" stop-color="#8c8b8b"></stop></linearGradient></defs><path fill="url(#m)" d="M612 291c4 1 7 2 10 4h0c1 1 2 1 3 1 5 1 16 15 19 19 3 5 5 11 8 16 0 1 0 2-1 3l-1-1c-1-1-2-2-2-4-2 0-2 0-3 1-1 0-1-1-2-1-1-2-1-4-3-6l-5-7-1 1c-1-2-2-4-4-4-1-1-3-5-4-6-3-3-7-7-10-9 1-1 5-1 7 0-3-2-6-3-9-5h0v-1l-2-1z"></path><path d="M635 316c-1-3-2-5-4-7-2-3-5-7-6-10 0-1 0-1 1 0 1 0 3 1 4 2 3 3 7 7 9 12 1 1 2 3 3 4 1 2 2 3 3 5l1 2s1 1 1 2h0c1 1 1 2 1 3h0 0c-2 0-2 0-3 1-1 0-1-1-2-1-1-2-1-4-3-6l-5-7z" class="C"></path><defs><linearGradient id="n" x1="239.301" y1="62.367" x2="208.421" y2="65.718" xlink:href="#B"><stop offset="0" stop-color="#969496"></stop><stop offset="1" stop-color="#d4d4d3"></stop></linearGradient></defs><path fill="url(#n)" d="M206 52h-1v-1c4-1 16 6 19 9 7 4 14 10 19 16l4 4h0c2 2 3 3 5 4-2 0-2 0-3-1 0 1 0 1 1 2h0c0 1 1 2 2 2l-1 1v1l4 6c-1 0-2-1-3-1-1-1-2-3-3-4l-15-16h-1 0-1l3 4h-1l-1-1c-2-2-5-5-8-5v-1c-1 0-1 0-1-1-1-1-3-3-4-3v1c-2-3-5-6-7-8-3-2-6-5-7-8z"></path><path d="M250 85c-3-2-7-6-8-9h1l4 4h0c2 2 3 3 5 4-2 0-2 0-3-1 0 1 0 1 1 2h0z" class="K"></path><path d="M206 52c8 4 15 11 21 17 1 1 3 4 5 5l3 4h-1l-1-1c-2-2-5-5-8-5v-1c-1 0-1 0-1-1-1-1-3-3-4-3v1c-2-3-5-6-7-8-3-2-6-5-7-8z" class="V"></path><defs><linearGradient id="o" x1="738.322" y1="413.272" x2="747.063" y2="436.35" xlink:href="#B"><stop offset="0" stop-color="#909090"></stop><stop offset="1" stop-color="#d5d4d5"></stop></linearGradient></defs><path fill="url(#o)" d="M710 408c1 1 2 2 2 3 1 1 2 1 2 2l1 1c1 1 2 2 3 2l2-1s1 0 2-1c5 8 12 13 21 16 8 2 18 0 25-3 4-2 7-4 11-5-2 1-4 3-5 5l-9 4c-7 3-15 5-24 4s-20-5-27-12c0-1-3-4-3-5h4l-1-1h1l1 1h1 1v-1c-1 0-1-1-2-1-2-2-5-5-6-8z"></path><defs><linearGradient id="p" x1="723.149" y1="423.319" x2="727.192" y2="426.309" xlink:href="#B"><stop offset="0" stop-color="#737272"></stop><stop offset="1" stop-color="#888786"></stop></linearGradient></defs><path fill="url(#p)" d="M720 418c4 2 6 8 11 9l4 1v1h0s1 1 2 1h2s0 1 1 1h1 0c-4 0-9-2-13-3-2-1-5-2-6-4l1-1c-1-2-3-3-3-5z"></path><defs><linearGradient id="q" x1="717.675" y1="417.544" x2="721.219" y2="423.413" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#6c6b6b"></stop></linearGradient></defs><path fill="url(#q)" d="M710 408c1 1 2 2 2 3 1 1 2 1 2 2l1 1c1 1 2 2 3 2l2 2c0 2 2 3 3 5l-1 1-3-1c-2-1-2-1-5 0 0-1-3-4-3-5h4l-1-1h1l1 1h1 1v-1c-1 0-1-1-2-1-2-2-5-5-6-8z"></path><path d="M711 418h4l4 5c-2-1-2-1-5 0 0-1-3-4-3-5z" class="D"></path><path d="M429 79h1v1l1-1c2 4 4 7 8 10l-1 1c1 0 2 1 3 2 1 0 2 1 2 2h10l-1 2c-4 0-11 1-13 4s-2 6-1 8c0 3 2 5 3 7l2 1v1s0 1-1 1c1 4 1 7 2 10s4 8 6 10l3 3c-2-1-2-2-4-2h0c-4-4-8-8-10-13-2-3-3-6-4-9-2-8-4-15-5-23v-6l-1-9z" class="N"></path><path d="M431 79c2 4 4 7 8 10l-1 1c1 0 2 1 3 2 1 0 2 1 2 2-1 0-3-1-4-2-5-2-7-7-9-12l1-1z" class="D"></path><path d="M443 94h10l-1 2c-4 0-11 1-13 4s-2 6-1 8c0 3 2 5 3 7h-2c-2-1-3-3-3-4-1-4-2-7-3-10v-4h2c1-1 1-2 2-3 1 0 3 1 5 1 1 0 1-1 1-1z" class="S"></path><path d="M298 102l8 1c-2 1-5 0-8 0l2 1h2c1 0 1 1 2 1v-1h1 3 7 1s1-1 1 0c-3 2-5 2-9 2v-1h-3c1 1 2 1 3 1h0 1 0c5 1 8-1 12-3l-4 4v1h-1v1h1c-2 4-2 7-1 11 1 3 2 5 3 8 1 2 4 4 5 6 0 1 0 2-1 3l1 1c-1 1-2 2-4 2v1l-1 1h1s-2 2-3 2c-2 0-6-1-8-1-1 0-1 0-2-1-2-1-3-2-5-2-1-1-3-1-3-3h1c4-3 9-3 12-8 1-4 1-9 0-13 0-1-1-3-1-4-3-5-9-7-15-9 1 0 1-1 2-1z" class="L"></path><defs><linearGradient id="r" x1="304.032" y1="140.426" x2="317.743" y2="141.675" xlink:href="#B"><stop offset="0" stop-color="#a6a5a4"></stop><stop offset="1" stop-color="#c7c6c6"></stop></linearGradient></defs><path fill="url(#r)" d="M299 137h1l4 1c2 1 3 2 5 2h1c2 0 4 1 6 0l1 1c1 0 2-1 3-1v1l-1 1h1s-2 2-3 2c-2 0-6-1-8-1-1 0-1 0-2-1-2-1-3-2-5-2-1-1-3-1-3-3z"></path><path d="M316 120l3 8c1 2 4 4 5 6 0 1 0 2-1 3l1 1c-1 1-2 2-4 2-1 0-2 1-3 1l-1-1h1c1 0 1-1 2-1h0 1l-2-3v-3c1 0 1-1 0-2-1-4-2-7-2-11z" class="C"></path><path d="M318 131l1-1c1 1 2 2 2 3l1 1c0 1 0 1-1 1-1 1-2 1-3 1v-3c1 0 1-1 0-2z" class="J"></path><defs><linearGradient id="s" x1="562.418" y1="458.695" x2="568.716" y2="456.093" xlink:href="#B"><stop offset="0" stop-color="#242324"></stop><stop offset="1" stop-color="#3d3d3d"></stop></linearGradient></defs><path fill="url(#s)" d="M564 425l1 1-1 2 1 1c0 1-1 3-2 4-1 7-2 12-1 18v2c1 1 2 2 4 2h0l6 6 1 1v1 1c2 2 2 5 2 8 3 1 6 4 9 4v1c1 0 2 0 3 1v1l-5 1h-3l-2-2-1 1c-1-1-2-2-3-2l-6-6c-2-2-4-5-6-7l-3-6-2-5c0-1 0-3-1-4v-4c1-7 4-13 9-20z"></path><path d="M573 474c3 0 8 5 11 3 1 0 2 0 3 1v1l-5 1h-3l-2-2c-1-2-3-3-4-4z" class="Y"></path><path d="M562 453c1 1 2 2 4 2h0l6 6 1 1v1 1c2 2 2 5 2 8-6-4-11-12-13-19z" class="T"></path><defs><linearGradient id="t" x1="574.717" y1="453.791" x2="554.453" y2="450.149" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#d2d1d2"></stop></linearGradient></defs><path fill="url(#t)" d="M564 425l1 1-1 2c-4 7-6 18-4 26s7 14 13 20c1 1 3 2 4 4l-1 1c-1-1-2-2-3-2l-6-6c-2-2-4-5-6-7l-3-6-2-5c0-1 0-3-1-4v-4c1-7 4-13 9-20z"></path><defs><linearGradient id="u" x1="138.967" y1="248.579" x2="107.118" y2="244.246" xlink:href="#B"><stop offset="0" stop-color="#c8c7c8"></stop><stop offset="1" stop-color="#fbfaf9"></stop></linearGradient></defs><path fill="url(#u)" d="M120 248c2-2 5-4 7-7s4-8 7-11c1-3 2-7 5-9 1-1 3-1 5-1h2c1 2 1 4 0 6l-1 1v2 1 1s1 0 1 1c-3 5-7 10-12 15l-12 9-5 5-7 5-2 2-1-1c-1 1-2 1-2 1l-1 1h0c1-2 2-3 2-4h1l13-17z"></path><path d="M144 220h2c1 2 1 4 0 6l-1 1v2 1 1l-1 2c-1 0-1 1-2 2l-1-1h-1c1 0 1-1 1-2 0-2 1-3 2-6v-1c1-2 1-3 1-5z" class="I"></path><path d="M145 231s1 0 1 1c-3 5-7 10-12 15l-12 9-5 5v-1c0-1 1-2 1-4 3-4 5-8 9-11h1c-1 1-2 2-2 3l3-3c3-3 6-5 8-8h0v1l3-4h1l1 1c1-1 1-2 2-2l1-2z" class="L"></path><path d="M252 87c1 0 2 1 4 1v1c0 1 1 3 2 5 2 0 3-1 4 0h1l3 1 13 5h0c-3 0-9-2-11-1 0 2 2 4 3 5 1 2 0 0 1 1l1 1 1 2h0l1-1c0 1 0 3 1 4l1 3c0 1 0 1 1 2-1 1 0 1-1 2l9 12c2 3 3 5 5 8l6 6c1 2 4 4 5 5 2 1 3 1 3 3 2 1 5 2 7 3v1c3 2 8 4 12 5h0-1c-6 0-12-2-17-4-1 0-2-1-4-1h0c-1-1-2-2-4-3 0-1 1-1 1-2 0 0-1-1-2-1-1-3-3-4-5-6l-8-8 1-1-7-7-8-12c-1-3-3-6-5-8v1 1c1 0 1 0 1 1-1-1-1-2-3-2h0l-8-14-4-6v-1l1-1z" class="R"></path><path d="M266 95l13 5h0c-3 0-9-2-11-1 0 2 2 4 3 5 1 2 0 0 1 1l1 1 1 2h0l1-1c0 1 0 3 1 4l1 3c0 1 0 1 1 2-1 1 0 1-1 2-2-2-3-4-4-6-3-5-7-10-8-16l1-1z" class="Q"></path><path d="M299 151l7 3c-1 0-2-1-3-2-2-1-3-2-4-3l6 3c2 1 5 2 7 3v1c3 2 8 4 12 5h0-1c-6 0-12-2-17-4-1 0-2-1-4-1h0c-1-1-2-2-4-3 0-1 1-1 1-2z" class="K"></path><path d="M252 87c1 0 2 1 4 1v1c0 1 1 3 2 5 2 0 3-1 4 0h1l-1 1c2 2 2 3 2 5l-1 1 24 36h0l-2-2-7-7-8-12c-1-3-3-6-5-8v1 1c1 0 1 0 1 1-1-1-1-2-3-2h0l-8-14-4-6v-1l1-1z" class="J"></path><path d="M258 94c2 0 3-1 4 0h1l-1 1c2 2 2 3 2 5l-1 1-5-7z" class="T"></path><path d="M251 88c5 4 8 9 11 14l3 6v1 1c1 0 1 0 1 1-1-1-1-2-3-2h0l-8-14-4-6v-1z" class="V"></path><path d="M561 464c2 2 4 5 6 7l6 6c1 0 2 1 3 2l1-1 2 2c1 1 3 2 4 3 0 1 1 2 1 2-1 2-1 2-3 3-3 2-3 4-7 4-13 5-25 6-38 4h-5c2 0 4 0 5-1 3-1 9-1 12-2h5c2-3 3-5 4-8h1c0-3-1-6 1-9v-1c0 1 1 1 1 1l1-1c-1-2-1-3-2-5l1-2c1 0 0-2 0-3l1-1z" class="E"></path><path d="M576 479l1-1 2 2c1 1 3 2 4 3 0 1 1 2 1 2-1 2-1 2-3 3-3 2-3 4-7 4 2-1 5-4 5-7 1-2-1-3-2-5l-1-1z" class="L"></path><path d="M571 483c1 1 1 2 1 3-1 2-2 4-4 5l-2 1c-3 1-6 1-9 1l-8 1-1-1h5l3-1c3 0 7-1 10-2 2-2 3-3 4-5 0-1 0-1 1-2z" class="U"></path><path d="M559 476c0 1 1 2 1 3v1h4c2 0 5 1 6 2 1 0 1 1 1 1-1 1-1 1-1 2-1 2-2 3-4 5-3 1-7 2-10 2l-3 1c2-3 3-5 4-8h1c0-3-1-6 1-9z" class="J"></path><path d="M563 487l-1 1-1-2v-2h1c0 1 1 2 1 3z" class="K"></path><path d="M557 485h1v2h0l1-1h1c0 3-2 4-4 6l-3 1c2-3 3-5 4-8z" class="D"></path><path d="M563 482c1-1 3-1 4 0 1 0 2 1 3 2v1c-1 2-2 3-4 5v-2h0-3v-1c0-1-1-2-1-3s0-1 1-2z" class="E"></path><path d="M563 482c1-1 3-1 4 0 1 0 2 1 3 2v1h-1c-1 0-1-1-2-2-1 0-1 0-1 1v1c-1 1-1 1-2 1v-2c0-1-1-1-1-2z" class="H"></path><path d="M561 464c2 2 4 5 6 7l6 6c1 0 2 1 3 2l1 1-3 6c0 2-2 4-5 5h-1c2-1 3-3 4-5 0-1 0-2-1-3 0 0 0-1-1-1-1-1-4-2-6-2h-4v-1c0-1-1-2-1-3v-1c0 1 1 1 1 1l1-1c-1-2-1-3-2-5l1-2c1 0 0-2 0-3l1-1z" class="Z"></path><path d="M561 475c1 2 2 1 2 3-1 1-2 1-3 1 0-1-1-2-1-3v-1c0 1 1 1 1 1l1-1z" class="O"></path><path d="M563 474c3 1 5 2 7 4h-5c-1-1-2-3-2-4z" class="R"></path><path d="M561 464c2 2 4 5 6 7l6 6c1 1 2 3 1 5-2-1-3-3-4-4-2-2-4-3-7-4l-3-6c1 0 0-2 0-3l1-1z" class="V"></path><defs><linearGradient id="v" x1="258.511" y1="274.285" x2="255.809" y2="293.343" xlink:href="#B"><stop offset="0" stop-color="#afaeae"></stop><stop offset="1" stop-color="#d0cfd0"></stop></linearGradient></defs><path fill="url(#v)" d="M274 273l3 5 3 8 3 4s-1 0-2 1h0l-1 1 3 6c-2 0-2-1-4-2h-3c-1 0-5-2-6-2-7-3-16-4-23-4l-10 1c-2 0-7 1-9 0-1 0-1 0-2 1h-2c1-1 2-2 3-2l1-1c1 0 4-1 5-2s1-2 2-3c1-2 3-2 5-2l6-1c3 0 10 1 12-1 3 0 6 0 8 1l8-8z"></path><path d="M277 281h1v3 1l1 1h1 0l3 4s-1 0-2 1h0l-1 1v-3h-1c0 1 0 1-1 2h-1v-10z" class="R"></path><path d="M274 273l3 5 3 8h0-1l-1-1v-1-3h-1c-1-2-1-4-2-6-3 2-7 8-10 9h0c-4-2-12-1-16 0-7 3-14 5-21 7-1 0-1 0-2 1h-2c1-1 2-2 3-2l1-1c1 0 4-1 5-2s1-2 2-3c1-2 3-2 5-2l6-1c3 0 10 1 12-1 3 0 6 0 8 1l8-8z" class="f"></path><path d="M193 455h2l2 4h1 0l5 3 6 6h1c0 1 2 1 2 2h3l1 1h0v-2h2l1 1c1 1 4 4 6 5l13 10-1 1c-1 0-2 0-3-1l-1 1c-2 0-2-1-4 0h0-1l-1-1c0 1-1 1-1 2l-1-2h-2l-1 1c3 4 7 7 9 11 0 1 2 5 2 5v1c1 1 1 3 1 4h-1 0l-1-1c-3-3-6-3-9-6h0l-3-3c0-1-3-3-4-4-2-2-5-5-6-8 1 1 2 2 4 2v-1c-3-4-8-7-11-11h1 1l-3-3h0c-1 0-2-1-2-2-1-2-3-4-4-6l-3-3c1-2 0-4 0-6z" class="V"></path><path d="M196 464c2 1 3 3 5 4l2 2c0 1 1 1 2 2 3 2 5 6 10 7 1 1 2 2 4 2 1 1 1 2 2 3l1 2c3 4 7 7 9 11l-1 1-3-3c-3-3-6-7-9-11-2-1-4-3-6-4l-5-4c-2-1-3-3-5-4h0c-1 0-2-1-2-2-1-2-3-4-4-6z" class="S"></path><path d="M210 485c1 1 2 2 4 2v-1c2 2 3 4 5 5 3 4 8 7 11 10 1 1 2 3 3 4v-2c1 1 1 3 1 4h-1 0l-1-1c-3-3-6-3-9-6h0l-3-3c0-1-3-3-4-4-2-2-5-5-6-8z" class="B"></path><path d="M193 455h2l2 4h1 0l5 3 6 6h1c0 1 2 1 2 2h3l1 1c-1 1-3 1-4 2 1 0 1 0 2 1l2 3 3 4c-2 0-3-1-4-2-5-1-7-5-10-7-1-1-2-1-2-2l-2-2c-2-1-3-3-5-4l-3-3c1-2 0-4 0-6z" class="N"></path><path d="M202 465h1l3 3c2 2 4 2 6 2h3l1 1c-1 1-3 1-4 2h-3c-3-1-5-5-7-8z" class="F"></path><path d="M198 459h0l5 3 6 6h1c0 1 2 1 2 2-2 0-4 0-6-2l-3-3h-1c-1 0-4-5-4-6z" class="G"></path><defs><linearGradient id="w" x1="226.925" y1="479.902" x2="225.198" y2="485.52" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5e5e5e"></stop></linearGradient></defs><path fill="url(#w)" d="M216 469h2l1 1c1 1 4 4 6 5l13 10-1 1c-1 0-2 0-3-1l-1 1c-2 0-2-1-4 0h0-1l-1-1c0 1-1 1-1 2l-1-2h-2l-1 1-1-2c-1-1-1-2-2-3l-3-4-2-3c-1-1-1-1-2-1 1-1 3-1 4-2h0v-2z"></path><path d="M214 474l1-1c2 0 3 2 4 4l1 1-2-1h-2l-2-3z" class="R"></path><path d="M216 477h2l2 1c0 2 1 3 1 5v1c-1-1-1-2-2-3l-3-4z" class="B"></path><path d="M234 485c-3-1-5-4-7-5-2-2-6-3-8-5-1-2 0-1-1-3 0-1-1-1-1-3 1 1 1 1 2 1 1 1 4 4 6 5l13 10-1 1c-1 0-2 0-3-1z" class="S"></path><path d="M482 133v-1c3-3 4-7 6-11 0 4 0 7 1 10l4-5c1 4 1 8 5 11 3 3 8 1 10 4 2 1 2 3 3 4h2l-1-10v-1l2 2 1-1-1 1h-1v1c0 1 1 1 1 2 3 3 6 4 10 5h0c-1 1-2 1-3 2-1 0-1 0-1 1 1 0 2-1 3-1l1 1c-2 1-3 2-5 2l-5 2h-1-1c-2 1-4 2-6 2-5 1-11 2-16 1s-9-2-13-4h0c-2-2-2-4-2-6v-1c-1 0-1-1-1-1 1-1 1-2 2-2l6-5v-1l-1-1h1z" class="C"></path><path d="M513 145l-1-10v-1l2 2 1-1-1 1h-1v1c0 1 1 1 1 2 3 3 6 4 10 5h0c-1 1-2 1-3 2-1 0-1 0-1 1 1 0 2-1 3-1l1 1c-2 1-3 2-5 2v-1h-7l-6-3h-1-1c-1 0-1-1-2-1h-1-2l-1-1c-2 0-3-1-4-1-2-2-4-3-5-5v-1c1 1 3 3 4 3h1c3 2 6 2 9 3 0 1 0 1 1 1s2 1 3 1 2 0 3 1h0 1 2z" class="c"></path><path d="M513 144v-1c1 0 2 1 3 1v2c-1 1-1 1-3 1h0v-3z" class="C"></path><path d="M513 145l-1-10v-1l2 2 1-1-1 1h-1v1c0 1 1 1 1 2v1c1 2 2 3 3 4 0 1 0 1-1 2v-2c-1 0-2-1-3-1v1 1z" class="U"></path><path d="M475 143c0-1 0-2 1-2 2-1 4 0 7 0l13 4c2 1 5 1 7 1 3 1 6 2 9 2h7v1l-5 2h-1-1c-2 1-4 2-6 2-5 1-11 2-16 1s-9-2-13-4h0c-2-2-2-4-2-6v-1z" class="I"></path><path d="M475 144c1 1 3 2 3 3s0 2-1 3h0c-2-2-2-4-2-6z" class="L"></path><path d="M443 116c3 1 5 2 7 3 4 0 8-1 12-1v-1c2 0 3 0 4 2h1c-3 0-5 0-7 1v2c-1 1-2 2-1 4v1c1 0 3 1 4 2 1 0 1 1 1 2 0 2-2 4-3 6l-1 1c1 1 2 3 3 2 1 0 2-1 2-1 4-3 8-9 12-9 2 0 3 2 5 3h-1l1 1v1l-6 5c-1 0-1 1-2 2 0 0 0 1 1 1v1c0 2 0 4 2 6h0c4 2 8 3 13 4-3 0-5 0-8 1 4 0 8 1 12 2 6 0 12 0 18-1 2 0 5 0 8-1h1l-2 1h0c-10 2-22 3-31 1-3-1-6-2-10-3-2 0-3-1-5-1v1c-2 0-12-6-14-7-1-1 0-3-2-4v3c-3-2-6-4-8-7 2 0 2 1 4 2l-3-3c-2-2-5-7-6-10s-1-6-2-10c1 0 1-1 1-1v-1z" class="D"></path><path d="M459 140l-1-1c-2-2-4-9-3-12 0-1 0-1 1-1 1 1 2 2 2 3 2 2 2 2 4 2h1l-1-2h1c1 0 1 1 1 2 0 2-2 4-3 6l-1 1v1h-1v1z" class="K"></path><path d="M463 129c1 0 1 1 1 2 0 2-2 4-3 6l-1 1v1h-1c0-1 0-3 1-5 0 0 1 1 2 0v-1c-2 0-3-1-4-3v-1c2 2 2 2 4 2h1l-1-2h1z" class="U"></path><path d="M443 116c3 1 5 2 7 3 4 0 8-1 12-1v-1c2 0 3 0 4 2h1c-3 0-5 0-7 1l-1 1c-1 1-2 1-4 1h-1l-8-1c2 5 3 12 7 17 1 2 3 3 4 5v3c-3-2-6-4-8-7 2 0 2 1 4 2l-3-3c-2-2-5-7-6-10s-1-6-2-10c1 0 1-1 1-1v-1z" class="S"></path><defs><linearGradient id="x" x1="470.553" y1="130.735" x2="472.188" y2="148.908" xlink:href="#B"><stop offset="0" stop-color="#9e9d9d"></stop><stop offset="1" stop-color="#c3c1c2"></stop></linearGradient></defs><path fill="url(#x)" d="M460 138c1 1 2 3 3 2 1 0 2-1 2-1 4-3 8-9 12-9 2 0 3 2 5 3h-1l1 1v1l-6 5c-1 0-1 1-2 2 0 0 0 1 1 1v1c0 2 0 4 2 6h0c4 2 8 3 13 4-3 0-5 0-8 1-1-1-2-1-4-2-1 0-3-1-4-2-5-2-9-3-13-7v-2c0-1-1-1-2-2h0v-1h1v-1z"></path><path d="M472 144h2v2h0c-1 0-2-1-2-1v-1z" class="U"></path><defs><linearGradient id="y" x1="539.016" y1="145.057" x2="526.596" y2="124.191" xlink:href="#B"><stop offset="0" stop-color="#b5b3b4"></stop><stop offset="1" stop-color="#d3d3d2"></stop></linearGradient></defs><path fill="url(#y)" d="M564 104l1 1c0 1-1 1 0 2v5h0v2l-2 3c-1 2-1 4-3 6-2 3-3 6-6 9-3 5-8 9-11 14-3 3-6 5-10 7-1 0-2 1-3 1l-9 1h-1c-3 1-6 1-8 1-6 1-12 1-18 1-4-1-8-2-12-2 3-1 5-1 8-1 5 1 11 0 16-1 2 0 4-1 6-2h1 1l5-2c2 0 3-1 5-2l-1-1c-1 0-2 1-3 1 0-1 0-1 1-1 1-1 2-1 3-2h0 5c6-1 10-4 15-8 1 0 1-1 2-2l2-3c1-2 2-5 3-7 0-1 1-2 0-3v-2s-1-1-1-2c-3 0-5 0-8 1h-5c1-2 3-4 5-5l2-2h1l1-1 3-1 5-2h2c2-1 4-2 5-2s2-1 3-1z"></path><path d="M528 152l1-1c1 0 2-1 3-2h1l1-1c1 0 2-1 3-1 1-1 2-1 3-1-2 2-4 4-6 5l-6 1z" class="U"></path><path d="M528 152l6-1c-1 0-1 1-1 2-1 0-2 1-3 1l-9 1h-1c2-2 6-2 8-3z" class="K"></path><path d="M554 107l1 1c-1 0-1 0-2 1h2 1c-1 1-2 1-3 1l-1 1-4 1c-2 1-4 2-6 2v-1l2-2h1l1-1 3-1 5-2z" class="b"></path><path d="M550 117c1 0 2 1 3 2s2 2 2 4-5 9-7 11v-1l-2 1 2-3c1-2 2-5 3-7 0-1 1-2 0-3v-2s-1-1-1-2z" class="K"></path><path d="M546 134l2-1v1c-2 2-5 4-7 6-4 2-7 4-11 5-5 2-10 6-15 6h-1l5-2c2 0 3-1 5-2l-1-1c-1 0-2 1-3 1 0-1 0-1 1-1 1-1 2-1 3-2h0 5c6-1 10-4 15-8 1 0 1-1 2-2z" class="Q"></path><path d="M565 107v5h0v2l-2 3c-1 2-1 4-3 6-2 3-3 6-6 9-3 5-8 9-11 14-3 3-6 5-10 7 0-1 0-2 1-2 2-1 4-3 6-5s4-4 6-7c1-2 3-3 5-5 1-2 3-4 4-5 2-3 4-6 5-9 2-2 2-5 3-7s1-4 2-6z" class="J"></path><defs><linearGradient id="z" x1="237.405" y1="89.557" x2="202.129" y2="129.562" xlink:href="#B"><stop offset="0" stop-color="#b7b6b7"></stop><stop offset="1" stop-color="#f4f3f3"></stop></linearGradient></defs><path fill="url(#z)" d="M224 75h1c3 3 7 6 9 9 3 4 4 8 6 12 4 10 5 18 5 29 0 5-1 9-2 14v-5c0-1-1-1 0-2v-4-8h0c0-2 0-4-1-6v-2c0-1-1-3-2-4 0-1-1-1-1-2l1-1c-1-2-1-2-2-2v5 1l1 3c-1 1-1 0-2 1v12h-1-1c-1 1-1 3-1 4 0 3 0 5-1 8l-2-1v-1-3c1-2 1-4 1-6 1-1 1-2 1-3h0v-1-3c0 1 0 1-1 1 0 1 0 2-1 3-1 0-1 1-2 1 0 1-1 2 0 3l-3 3h-2l-1-1h-2c-1 2-8 9-10 10-1 0-2 1-3 1-1 1-3 3-5 3h-1c5-3 9-7 13-11-2 0-4 3-6 4v-1c1-2 3-2 3-5h0c0-1 3-4 4-5 3-4 6-9 8-14 2-3 3-6 4-10 2-9 0-17-4-26z"></path><path d="M230 106c0-2 0-6 1-8 2 0 3 0 4 1l1 2c-1 0-1 1-1 2h0l-2 2-2 2v-1h-1z" class="E"></path><path d="M231 106v-1c1-2 2-5 4-6l1 2c-1 0-1 1-1 2h0l-2 2-2 2v-1z" class="P"></path><path d="M231 106v1l2-2 2-2h0l-7 15c-2 4-4 8-7 11-1 2-8 9-10 10-1 0-2 1-3 1-1 1-3 3-5 3h-1c5-3 9-7 13-11 7-7 11-17 15-26h1z" class="S"></path><defs><linearGradient id="AA" x1="238.533" y1="105.571" x2="233.352" y2="112.348" xlink:href="#B"><stop offset="0" stop-color="#3d3d3e"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#AA)" d="M236 101l2 8 1 3c-1 1-1 0-2 1v12h-1-1c-1 1-1 3-1 4 0 3 0 5-1 8l-2-1v-1-3c1-2 1-4 1-6 1-1 1-2 1-3h0v-1-3c0 1 0 1-1 1 0 1 0 2-1 3-1 0-1 1-2 1 0 1-1 2 0 3l-3 3h-2l-1-1h-2c3-3 5-7 7-11l7-15c0-1 0-2 1-2z"></path><path d="M276 432h2c0 1 1 3 2 4 2 7 3 18 0 25l-3 5a57.31 57.31 0 0 1-11 11l-1 2-4 4-4 3-2 2h0c-1 1-2 1-3 1l-1 1c-1 7-2 16-1 23h-1c0-3 0-7-1-9s-1-5-3-7h0c-1-2-1-4-2-5l-2-2v-3l-3-2-13-10c0-1 1-1 1-1l-6-7 2 2c1 1 2 0 3 0l2 1c2 2 6 5 9 6h1c2 0 4 1 6 1h3c2 0 4-1 6-2 3-1 5-2 7-3l-1-1c-1 1-1 1-2 1-1-1-4-2-5-4v-1l2 1h3 0l1-1h2-1 0l3-1h3c6-2 10-6 13-11 3-7 1-16-1-23z" class="b"></path><path d="M245 490h3 0c4-1 5-3 7-5 4-3 7-6 11-8l-1 2-4 4-4 3-2 2h0c-1 1-2 1-3 1l-1 1c-1 7-2 16-1 23h-1c0-3 0-7-1-9s-1-5-3-7h0c-1-2-1-4-2-5l-2-2v-3c1 1 2 2 4 3z" class="Y"></path><path d="M270 467v-1c2-1 3-3 5-5l1 1c-1 3-8 10-11 12-4 3-10 9-16 10-1 1-4 1-5 0h-4v1c1 1 2 1 4 2 0 1 1 2 1 3-2-1-3-2-4-3l-3-2-13-10c0-1 1-1 1-1l-6-7 2 2c1 1 2 0 3 0l2 1c2 2 6 5 9 6h1c2 0 4 1 6 1h3c2 0 4-1 6-2 3-1 5-2 7-3 1-1 2-1 3-2h0v1h1l2-2h1c0-1 1-1 1-1l2-2h0l1 1z" class="C"></path><path d="M259 472c1-1 2-1 3-2h0v1h1l2-2h1c0-1 1-1 1-1l2-2h0l1 1c-3 3-7 5-11 7-3 2-7 5-10 5-1-1-3-1-4-1 0-1 0-1 1-1 2 0 4-1 6-2 3-1 5-2 7-3z" class="I"></path><path d="M220 467l2 2c1 1 2 0 3 0l2 1c2 2 6 5 9 6 2 2 4 3 6 4h1v1c-1 1-1 1-2 1-4 1-11-6-15-8l-6-7z" class="G"></path><defs><linearGradient id="AB" x1="286.478" y1="118.754" x2="291.571" y2="109.932" xlink:href="#B"><stop offset="0" stop-color="#a3a1a1"></stop><stop offset="1" stop-color="#d2d1d1"></stop></linearGradient></defs><path fill="url(#AB)" d="M276 103l8 3c3-1 7 1 10 2h2c3 1 6 2 9 4 1 0 4 4 4 4h1l1-1v2l1-1c1 4 1 9 0 13-3 5-8 5-12 8h-1c0 2 2 2 3 3 2 0 3 1 5 2h-1-1-1c0 1 1 1 1 1h0-2c-1 0-1 1-1 1 3 3 6 6 10 7 8 3 16 4 24 6h-9c-4 0-9-2-13-3-1 0-2-1-3-1l-2-1-2-2-2-1h-1l-4-4c-1 0-1 0-2-1h0-1l-6-6c-2-3-3-5-5-8l-9-12c1-1 0-1 1-2-1-1-1-1-1-2l-1-3c-1-1-1-3-1-4v-3l1-1z"></path><path d="M302 117c2 2 3 3 3 5h0c-2 0-3 0-4-1h-1l1-1c0-1 0-1-1-1l-1-1h0 3v-1z" class="I"></path><path d="M297 119c1 0 2 1 3 2h1c1 1 2 1 4 1v2l-1 1c-2-1-4-1-6-2-1-1-1-2-2-4h1z" class="b"></path><path d="M276 107v-2h1l6 2h0-1-2s1 1 2 1h0c1 1 1 1 2 3h-1-1 0c-1 0-1 0-1-1h-2c0-1 0-2-1-2-1-1-1-1-2-1z" class="C"></path><path d="M292 123c1-2 2-4 3-5l2 1h-1c1 2 1 3 2 4l2 5h-1c-2-1-2 0-4 0h-1c-1-2-1-3-2-5z" class="c"></path><path d="M292 123c1-2 2-4 3-5l2 1h-1c-1 2-2 4-1 6v3h-1c-1-2-1-3-2-5z" class="I"></path><path d="M276 107c1 0 1 0 2 1 1 0 1 1 1 2l5 8c2 2 4 3 6 5v1c-1 0-2-1-3-1s-2-1-2-1c-1-1-2-1-3-2l-1-1s0-1-1-1v-1c0-1-1-2-1-2 0-1-1-2-1-3l-1-3c0-1 0-1-1-2z" class="J"></path><path d="M284 106c3-1 7 1 10 2 2 1 4 2 6 4l1 1c3 2 5 7 6 12 0 1-1 2-1 3-1 1-2 1-3 2h0c-2 0-3 0-4-2h1l-2-5c2 1 4 1 6 2l1-1v-2h0c0-2-1-3-3-5-4-6-12-8-19-10h0l1-1z" class="D"></path><path d="M305 122c0 2 0 3-1 6-1 0-2 0-3 1l-1-1-2-5c2 1 4 1 6 2l1-1v-2h0z" class="C"></path><path d="M312 116c1 4 1 9 0 13-3 5-8 5-12 8h-1c-1-1-1 0-1-1-3-3-6-5-6-9l-1-3 1-1c1 2 1 3 2 5h1c2 0 2-1 4 0 1 2 2 2 4 2h-1-3l-1 1c2 1 3 1 5 1s5 0 6-2c3-2 2-10 2-13l1-1z" class="c"></path><path d="M295 128c2 0 2-1 4 0 1 2 2 2 4 2h-1-3l-1 1c-2-1-2-2-4-3h1z" class="U"></path><path d="M294 108h2c3 1 6 2 9 4 1 0 4 4 4 4h1l1-1v2c0 3 1 11-2 13-1 2-4 2-6 2s-3 0-5-1l1-1h3 1 0c1-1 2-1 3-2 0-1 1-2 1-3-1-5-3-10-6-12l-1-1c-2-2-4-3-6-4z" class="I"></path><path d="M276 103l8 3-1 1-6-2h-1v2c1 1 1 1 1 2l1 3c0 1 1 2 1 3 0 0 1 1 1 2v1c1 0 1 1 1 1l1 1 2 3c1 2 3 4 4 6 1 0 0 0 1 1l1 1h-1c1 2 3 3 4 4 1 2 3 3 5 4l3 3c1-1 1-1 1-2 2 0 3 1 5 2h-1-1-1c0 1 1 1 1 1h0-2c-1 0-1 1-1 1 3 3 6 6 10 7 8 3 16 4 24 6h-9c-4 0-9-2-13-3-1 0-2-1-3-1l-2-1-2-2-2-1h-1l-4-4c-1 0-1 0-2-1h0-1l-6-6c-2-3-3-5-5-8l-9-12c1-1 0-1 1-2-1-1-1-1-1-2l-1-3c-1-1-1-3-1-4v-3l1-1z" class="F"></path><path d="M314 154c-2-2-4-3-7-4-3-2-7-5-9-9l-2-1 1-1h1l3 3c1-1 1-1 1-2 2 0 3 1 5 2h-1-1-1c0 1 1 1 1 1h0-2c-1 0-1 1-1 1 3 3 6 6 10 7 8 3 16 4 24 6h-9c-4 0-9-2-13-3z" class="E"></path><defs><linearGradient id="AC" x1="220.199" y1="525.195" x2="243.394" y2="551.644" xlink:href="#B"><stop offset="0" stop-color="#a4a3a4"></stop><stop offset="1" stop-color="#d9d8d8"></stop></linearGradient></defs><path fill="url(#AC)" d="M238 485l3 2v3l2 2c1 1 1 3 2 5h0c2 2 2 5 3 7s1 6 1 9h1v15c-1 3-2 7-3 11-2 7-4 15-9 20l-2 1c-4 5-8 7-13 10l-16 5c-3 1-10 2-12 1 1 0 3 0 4-1 3 0 7-1 10-2 5-1 9-3 13-5 9-6 13-21 15-31 1-3 1-6-1-9h0l1-1h1c1-1 2-1 2-1v-2c-1-1-1-3-1-4-1-1-2-2-2-4 0 0 0-1-1-1v-2-4l-1-2h-1 0 0c0-1 0-3-1-4v-1s-2-4-2-5c-2-4-6-7-9-11l1-1h2l1 2c0-1 1-1 1-2l1 1h1 0c2-1 2 0 4 0l1-1c1 1 2 1 3 1l1-1z"></path><path d="M246 509c4 5 2 10 2 15 0 2 0 4-1 5h0v-15c-1-2-1-3-1-5z" class="K"></path><path d="M240 524v-9h0l1 1 1 1c0 2 1 10-1 12-1 1-1 1-2 1h0l-1 1-2-3 1-1h1c1-1 2-1 2-1v-2z" class="J"></path><path d="M238 527h2c0 1-1 2-1 3l-1 1-2-3 1-1h1z" class="R"></path><path d="M240 496c-1-1-2-2-2-4v-1c1 1 2 2 3 2l2-1c1 1 1 3 2 5h0c2 2 2 5 3 7s1 6 1 9 0 8-1 11c0-5 2-10-2-15 0-1 0-5-1-6h-1c-1 2-1 4-1 6h0-1v-2l1-1c0-1 0-1-1-2 0-3 0-5-2-8z" class="J"></path><path d="M240 496c-1-1-2-2-2-4v-1c1 1 2 2 3 2l2-1c1 1 1 3 2 5l1 7c-2-3-3-6-5-8h-1z" class="O"></path><defs><linearGradient id="AD" x1="251.181" y1="535.514" x2="240.053" y2="538.713" xlink:href="#B"><stop offset="0" stop-color="#767578"></stop><stop offset="1" stop-color="#979593"></stop></linearGradient></defs><path fill="url(#AD)" d="M249 513h1v15c-1 3-2 7-3 11-2 7-4 15-9 20l-2 1c0-1 0-2 1-3 6-8 8-18 10-28h0c1-1 1-3 1-5 1-3 1-8 1-11z"></path><path d="M238 485l3 2v3l2 2-2 1c-1 0-2-1-3-2v1c0 2 1 3 2 4 2 3 2 5 2 8 1 1 1 1 1 2l-1 1v2h1 0l1 7h0l-1-3h-1l-1 1 1 3-1-1-1-1h0v9c-1-1-1-3-1-4-1-1-2-2-2-4 0 0 0-1-1-1v-2-4l-1-2h-1 0 0c0-1 0-3-1-4v-1s-2-4-2-5c-2-4-6-7-9-11l1-1h2l1 2c0-1 1-1 1-2l1 1h1 0c2-1 2 0 4 0l1-1c1 1 2 1 3 1l1-1z" class="M"></path><path d="M233 502c1 0 1 0 2 1l1 6-1-2h-1 0 0c0-1 0-3-1-4v-1z" class="J"></path><path d="M229 486h0c2-1 2 0 4 0 1 1 2 1 3 1 0 2 0 3 1 4v2h-1-1 0-1v-1c-1-2-3-4-4-4-1-1-1-1-1-2z" class="N"></path><path d="M238 485l3 2v3l2 2-2 1c-1 0-2-1-3-2h0v-1l-1 1c-1-1-1-2-1-4-1 0-2 0-3-1l1-1c1 1 2 1 3 1l1-1z" class="P"></path><path d="M238 485l3 2v3c-2-1-3-2-5-3-1 0-2 0-3-1l1-1c1 1 2 1 3 1l1-1z" class="G"></path><defs><linearGradient id="AE" x1="238.502" y1="499.029" x2="242.319" y2="512.711" xlink:href="#B"><stop offset="0" stop-color="#7f7e80"></stop><stop offset="1" stop-color="#a7a6a6"></stop></linearGradient></defs><path fill="url(#AE)" d="M238 501l-1-7c1 2 2 3 3 5 0 2 1 4 2 5s1 1 1 2l-1 1v2h1 0l1 7h0l-1-3h-1l-1 1c0-2-1-4-1-6 0-3-1-5-2-7z"></path><defs><linearGradient id="AF" x1="242.099" y1="510.772" x2="236.36" y2="515.245" xlink:href="#B"><stop offset="0" stop-color="#838283"></stop><stop offset="1" stop-color="#a9a8a8"></stop></linearGradient></defs><path fill="url(#AF)" d="M238 501c1 2 2 4 2 7 0 2 1 4 1 6l1 3-1-1-1-1h0v9c-1-1-1-3-1-4-1-1-2-2-2-4 0 0 0-1-1-1v-2h1 1c1-3 0-9 0-12z"></path><defs><linearGradient id="AG" x1="224.306" y1="484.537" x2="232.063" y2="500.404" xlink:href="#B"><stop offset="0" stop-color="#636263"></stop><stop offset="1" stop-color="#919191"></stop></linearGradient></defs><path fill="url(#AG)" d="M222 486l1-1h2l1 2c0-1 1-1 1-2l1 1c1 3 4 6 5 9 1 2 2 5 2 8-1-1-1-1-2-1 0 0-2-4-2-5-2-4-6-7-9-11z"></path><path d="M580 263c1 1 2 1 3 1v1l3 2h0c-2 1-5 7-6 9v1h0c1 1 2 1 2 2v1h2 0l6-1h5 8v1c4 0 7 1 10 2 3 0 5 1 7 2 1 1 3 2 4 2 4 3 9 5 13 8 2 2 4 4 5 6 3 4 5 8 8 12 0 1 1 3 2 3 2 5 5 10 6 15 2 5 2 10 3 15 1 6 3 13 2 19l-1-1c0 1-1 2-1 3-1-12-3-24-9-35-3-5-5-11-8-16-3-4-14-18-19-19-1 0-2 0-3-1h0c-3-2-6-3-10-4l-9-3c-4-3-10-5-15-6h-7c-5 1-8 3-11 7-2 2-4 3-6 5 0 1 0 1 1 1l-7 4-1-2h1l2-2c1 0 3-1 4-2s1-4 2-5c0-1 1-2 2-3 0 0 0-1 1-1 0-1 1-1 2-2h0v-1l-1-1 2-2 1 1 1-1v-1c-1-4-1-6 0-10l2 2 1-2h1c1 0 1-2 2-2v-2z" class="V"></path><path d="M595 279h8v1h0 0c1 1 3 2 5 3-4-1-6-2-9-2-2 0-4-1-6-1s-6 1-7 1-1-1-2-1l6-1h5z" class="d"></path><path d="M580 263c1 1 2 1 3 1v1l3 2h0c-2 1-5 7-6 9v1h0c1 1 2 1 2 2v1h2l-7 1h-3c0-1 2-1 3-2-3-3 0-7-1-10l1-2h1c1 0 1-2 2-2v-2z" class="a"></path><path d="M583 265l3 2h0c-2 1-5 7-6 9v1h0c1 1 2 1 2 2v1h2l-7 1 1-2c0-1-1-1-1-2s1-1 1-2c1-1 1-2 1-4v1c1-1 1-2 1-2v-1h1c0-1 2-3 2-4z" class="B"></path><path d="M603 280c4 0 7 1 10 2 3 0 5 1 7 2 1 1 3 2 4 2 4 3 9 5 13 8 2 2 4 4 5 6 3 4 5 8 8 12 0 1 1 3 2 3 2 5 5 10 6 15h0c-1 0-2-3-3-4-6-11-11-23-22-30-4-2-8-3-12-5-1-1-3-2-4-3h-3c-2-1-5-3-7-4l-3-1-5-2c3 0 5 1 9 2-2-1-4-2-5-3h0 0z" class="S"></path><path d="M603 280c4 0 7 1 10 2 3 0 5 1 7 2 1 1 3 2 4 2 4 3 9 5 13 8 2 2 4 4 5 6l-1 1-3-4-3-2h-1c-8-6-18-9-26-12-2-1-4-2-5-3h0 0z" class="N"></path><path d="M581 282c3-1 7-1 11-1 1 0 1 0 2 1 2 0 4 1 6 2h1l5 2v1l4 1 2 1c2 1 4 1 5 2 2 1 4 2 6 2 6 3 11 5 15 10 2 2 4 3 5 5 1 1 2 3 3 4 3 5 5 9 8 14 2 4 4 8 4 12l1 5c1 2 1 4 2 6 0 5 1 9 1 14 0 1-1 2-1 3-1-12-3-24-9-35-3-5-5-11-8-16-3-4-14-18-19-19-1 0-2 0-3-1h0c-3-2-6-3-10-4l-9-3c-4-3-10-5-15-6h-7z" class="G"></path><path d="M622 295c8 1 16 10 21 16 6 8 12 18 15 27l1 5c1 2 1 4 2 6 0 5 1 9 1 14 0 1-1 2-1 3-1-12-3-24-9-35-3-5-5-11-8-16-3-4-14-18-19-19-1 0-2 0-3-1h0z" class="X"></path><path d="M186 403l1-1h2c1 1 1 4 2 6l1 3 1-2c1 6 4 13 7 18h1l-1-1c0-1-1-3-1-4-1-2-2-3-3-4 0-2-1-5-1-7v-1c0-1 0-1 1-2l1 5 1 1c2 4 3 7 6 10 0 2 1 3 2 5h-1l7 12h0v-1l3 3c2 1 3 5 6 6h0c3 1 5 3 7 5 1 1 2 2 4 2 2 2 5 5 7 5h0c2 2 4 3 5 4 2 2 6 3 7 5l3 3s-1 1-2 1v1c-2 1-4 2-6 2h-3c-2 0-4-1-6-1h-1c-3-1-7-4-9-6l-2-1-6-6c-2-1-3-3-5-4l-9-12c-5-5-8-11-11-17l-7-11c0-3-1-6-1-9l1 1c0-1 0-2 1-3l-2-5z" class="O"></path><path d="M227 469l-1-1c-1 0-2-2-3-3-2-2-4-3-5-5-4-5-7-10-10-15 4 3 6 8 10 12v-1l2 1 5 6-1 1c1 2 5 5 6 6 3 1 5 3 8 4 1 0 2 1 3 1 4 1 8 0 11-1v1c-2 1-4 2-6 2h-3c-2 0-4-1-6-1h-1c-3-1-7-4-9-6v-1z" class="W"></path><path d="M218 457v-1l2 1 5 6-1 1c-3-2-5-4-6-7z" class="B"></path><path d="M227 470v-1c3 2 6 4 10 6l1-1c1 0 2 1 3 1 4 1 8 0 11-1v1c-2 1-4 2-6 2h-3c-2 0-4-1-6-1h-1c-3-1-7-4-9-6z" class="J"></path><defs><linearGradient id="AH" x1="198.59" y1="415.815" x2="192.089" y2="441.259" xlink:href="#B"><stop offset="0" stop-color="#343333"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#AH)" d="M186 410l1 1c3 7 7 13 11 19 1 3 2 6 4 9 1 2 3 5 3 8-5-5-8-11-11-17l-7-11c0-3-1-6-1-9z"></path><defs><linearGradient id="AI" x1="185.664" y1="417.45" x2="205.475" y2="419.359" xlink:href="#B"><stop offset="0" stop-color="#161516"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#AI)" d="M186 403l1-1h2c1 1 1 4 2 6l1 3 1-2c1 6 4 13 7 18l5 9c1 1 4 4 4 6l-2-2c-1-3-4-5-6-8-3-5-7-10-9-15l-4-9-2-5z"></path><path d="M201 427l-1-1c0-1-1-3-1-4-1-2-2-3-3-4 0-2-1-5-1-7v-1c0-1 0-1 1-2l1 5 1 1c2 4 3 7 6 10 0 2 1 3 2 5h-1l7 12h0c2 4 3 9 6 12 4 6 9 10 14 15h-1l1 1-2 1c-1-1-5-4-6-6l1-1-5-6c-3-3-5-6-7-9-1-2-1-4-2-6-3-5-6-11-10-15z" class="V"></path><path d="M225 463l6 5 1 1-2 1c-1-1-5-4-6-6l1-1z" class="H"></path><path d="M198 414c2 4 3 7 6 10 0 2 1 3 2 5h-1c-4-4-7-11-8-16l1 1z" class="G"></path><path d="M212 441v-1l3 3c2 1 3 5 6 6h0c3 1 5 3 7 5 1 1 2 2 4 2 2 2 5 5 7 5h0c2 2 4 3 5 4 2 2 6 3 7 5l3 3s-1 1-2 1c-3 1-7 2-11 1-1 0-2-1-3-1-3-1-5-3-8-4l2-1-1-1h1c-5-5-10-9-14-15-3-3-4-8-6-12z" class="Y"></path><path d="M232 469c1 1 3 2 4 2-1-1-2-1-2-2v-1c2-2 3-4 6-5 1 1 2 2 3 2-3 1-4 3-6 6 1 1 3 2 4 4-1 0-2-1-3-1-3-1-5-3-8-4l2-1z" class="C"></path><path d="M243 465c2 3 5 4 8 5l3 3s-1 1-2 1c-3 1-7 2-11 1-1-2-3-3-4-4 2-3 3-5 6-6z" class="L"></path><defs><linearGradient id="AJ" x1="695.337" y1="368.623" x2="714.845" y2="364.458" xlink:href="#B"><stop offset="0" stop-color="#1a191a"></stop><stop offset="1" stop-color="#454444"></stop></linearGradient></defs><path fill="url(#AJ)" d="M707 324h2c2 1 2 1 3 3 0 1 1 2 1 4 1 1 1 3 1 4v1s1 0 1 1c-3 9-6 17-5 27 0 5 0 11 2 17l-2 1c1 4 3 8 4 12 0 6 2 11 2 16h0c-1 0-1 0-1-1l-1-1h0l-1-1-1 1-6-10-11-31-2-6v-2c-1-2-1-4-2-6v-2h1l1-3v2l1 1 4-15 3-6 1-2c1-2 3-3 5-4z"></path><path d="M691 351h1l1-3v2l1 1c0 3-1 7-1 10v-2c-1-2-1-4-2-6v-2z" class="T"></path><path d="M709 324c2 1 2 1 3 3h-3c-3 2-7 10-8 13-1 2-1 5-2 6v3c0-1-1-2 0-2v-1-1-1c0-1 0-2 1-3l1-4v1c-1 1-2 3-2 4v1h-1c0-3 3-9 4-11 2-3 4-7 7-8z" class="N"></path><path d="M712 327c0 1 1 2 1 4 1 1 1 3 1 4l-1 1c-1-1-1-1-1-2s0 0-1-1h0c-2 2-4 3-5 6l-1 1h-1c-1 2-1 4-2 7-2 5-2 11-1 16h-1 0c0-2-1-5-1-8v-8-1c1-1 1-4 2-6 1-3 5-11 8-13h3z" class="S"></path><path d="M713 331c1 1 1 3 1 4l-1 1c-1-1-1-1-1-2s0 0-1-1h0c-2 2-4 3-5 6l-1 1h-1 0c1-1 1-2 1-2 1-1 1-2 1-2l1-1c0-1 1-1 1-2l2-2c1 0 1 1 3 1v-1z" class="M"></path><defs><linearGradient id="AK" x1="701.654" y1="367.337" x2="720.14" y2="361.419" xlink:href="#B"><stop offset="0" stop-color="#424141"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#AK)" d="M704 340h1l1-1c1-3 3-4 5-6h0c1 1 1 0 1 1s0 1 1 2l1-1v1s1 0 1 1c-3 9-6 17-5 27 0 5 0 11 2 17l-2 1c1 4 3 8 4 12 0 6 2 11 2 16h0l-6-16c-1-3-1-6-3-9-2-7-5-14-7-22h1c-1-5-1-11 1-16 1-3 1-5 2-7z"></path><path d="M714 336s1 0 1 1c-3 9-6 17-5 27 0 5 0 11 2 17l-2 1c-3-10-3-22-1-33 1-4 3-9 5-13z" class="e"></path><defs><linearGradient id="AL" x1="178.396" y1="368.319" x2="203.606" y2="372.492" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#AL)" d="M180 354h0c1-1 1-3 1-4h1v-2h0v-2h0c0-1 0-2 1-2v1c2-3 2-7 4-10 1-2 3-5 5-5h1c1 0 2 2 2 3h0v2c1 1 1 2 1 3 0 2 0 6 1 7v-4 1l1 5h0v-4h0c0 1 0 3 1 4v11l-1-3c0 2 0 4-1 6l-1 7c1 1 2 1 3 1l-2 21c0 8 0 16 1 24l-1-1-1-5c-1 1-1 1-1 2v1c0 2 1 5 1 7 1 1 2 2 3 4 0 1 1 3 1 4l1 1h-1c-3-5-6-12-7-18l-1 2-1-3c-1-2-1-5-2-6h-2l-1 1c-1-1-2-4-3-5l-1 1h0c-1-2-1-3-1-5-1-1-1-1-1-2v-1-4h0c1-1 0-6 0-7-1-9-1-17 0-26z"></path><path d="M197 342l1 5h0v-4h0c0 1 0 3 1 4v11l-1-3c0 2 0 4-1 6v-19z" class="H"></path><defs><linearGradient id="AM" x1="189.24" y1="387.818" x2="203.117" y2="374.897" xlink:href="#B"><stop offset="0" stop-color="#4c4b4a"></stop><stop offset="1" stop-color="#757678"></stop></linearGradient></defs><path fill="url(#AM)" d="M196 368c1 1 2 1 3 1l-2 21c-1 2-1 5-1 7h0v-3h-1 0l-1 3v-5c0-8 1-16 2-24z"></path><path d="M689 214s1 0 1-1h0c-2-1-4-1-5-2v-1c4 1 8 1 12 1l-4 1v1c1 1 3 0 4 1l14-3v1l-1 2-1 3c-1 7 0 13 4 18 1 2 2 3 2 4 4 3 7 6 11 8 1 1 2 1 3 1l1 1c1 0 2 0 3 1l1 1c1-1 2 0 3 0h1v1c0 4 1 8 2 13 2 3 3 6 4 10-2 0-3-2-5-3-3 0-5-2-7-4l-3-2c-1-2-3-3-5-4l-1 1c-2-1-3-4-5-5l-8-7h-2c-1 0-1-1-2-1-7-5-12-12-17-18-1-2-2-3-4-5-1-1-4-4-5-6h2l7 7h1l-2-2-6-7v-3l2 1h0v-1c2 0 3-1 5-2z" class="Y"></path><path d="M708 214h2l-1 3c-1 1-3 2-4 4-1 1-1 2-1 3-2 0-4-2-6-3 1 0 3 0 4-1h1v-1h1c1-2 2-3 4-5z" class="W"></path><path d="M697 234c4 4 7 8 10 12 2 2 5 4 7 6l8 8c1 0 1 0 2-1h1c2 2 3 3 4 6v1c-1-2-3-3-5-4l-1 1c-2-1-3-4-5-5l-8-7c-5-4-10-10-13-15v-2z" class="a"></path><path d="M718 246v2c1 1 2 1 2 2 1 0 2 1 3 2l2 2v1c-1 0-1 0-2 1v2c-1 0-1 1-2 1l-11-12h0v-1c0-1 0-2-1-3h1c1-1 3-1 4-1h1l1 2s1 2 2 2z" class="F"></path><defs><linearGradient id="AN" x1="694.976" y1="213.008" x2="703.297" y2="224.051" xlink:href="#B"><stop offset="0" stop-color="#1b1b1c"></stop><stop offset="1" stop-color="#373637"></stop></linearGradient></defs><path fill="url(#AN)" d="M711 211v1l-1 2h-2c-2 2-3 3-4 5h-1v1h-1c-1 1-3 1-4 1l-1-1c-1 1-2 2-2 3v6c1 2 1 4 2 5v2c-2-3-5-6-6-9-1-4-1-9 1-12 1-1 3-1 5-1l14-3z"></path><path d="M695 229l-1-1c-1-3-1-6-1-9 1-1 2-2 3-2 1-1 3-1 5-1s5-1 7-2c-2 2-3 3-4 5h-1v1h-1c-1 1-3 1-4 1l-1-1c-1 1-2 2-2 3v6z" class="M"></path><path d="M689 214s1 0 1-1h0c-2-1-4-1-5-2v-1c4 1 8 1 12 1l-4 1v1c1 1 3 0 4 1-2 0-4 0-5 1-2 3-2 8-1 12 1 3 4 6 6 9 3 5 8 11 13 15h-2c-1 0-1-1-2-1-7-5-12-12-17-18-1-2-2-3-4-5-1-1-4-4-5-6h2l7 7h1l-2-2-6-7v-3l2 1h0v-1c2 0 3-1 5-2z" class="H"></path><path d="M684 216c2 0 3-1 5-2-1 2-1 4-1 6-1-1-2-2-4-3v-1z" class="d"></path><path d="M682 216l2 1h0c2 1 3 2 4 3v1s0 1 1 2h1c0 1 0 1-1 2 0 0 0 1-1 1l-6-7v-3z" class="E"></path><path d="M715 239c4 3 7 6 11 8 1 1 2 1 3 1l1 1c1 0 2 0 3 1l1 1c1-1 2 0 3 0h1v1c0 4 1 8 2 13 2 3 3 6 4 10-2 0-3-2-5-3-3 0-5-2-7-4h0 1 1v-1-1c1-3 0-7-1-9-1-1-2-2-3-2-1-1-3-3-4-3-2-1-3-3-5-4-1 0-2-1-3-2-1 0-2-2-2-2l-1-2v-1l-1-1 1-1z" class="O"></path><path d="M715 239c4 3 7 6 11 8 1 1 2 1 3 1l1 1c1 0 2 0 3 1l1 1v3l-1 1c-3-5-7-5-12-8-1-1-3-2-5-3h0l-1-2v-1l-1-1 1-1z" class="S"></path><path d="M737 251h1v1c0 4 1 8 2 13 2 3 3 6 4 10-2 0-3-2-5-3h0c-2-5-3-11-4-17h-1c0-1 0-2 1-4h1 1z" class="Q"></path><path d="M738 263l2 2c2 3 3 6 4 10-2 0-3-2-5-3h0c1 1 1 1 2 0h0c0-2-1-3-2-4h0v-2l-1-1v-2z" class="D"></path><path d="M737 251h1v1c0 4 1 8 2 13l-2-2c-1-3-1-8-2-9h-1v1h-1c0-1 0-2 1-4h1 1z" class="H"></path><path d="M535 223c3 1 5 2 7 4 3 3 5 8 6 12 1 3 2 7 2 10v19c0 3 0 7-2 10 1 2 1 3 1 5v1c-2 5-3 11-3 16h0v1 2c0 1-1 2 0 3v1c-3 9-9 20-16 27h-1v-1h-1 0c4-5 7-11 10-17 5-10 9-22 6-34-2-12-11-21-21-28h2c0-1-1-1-2-1h-1c-2-1-3-1-5-1h-1c1-2 3-2 5-3l1 1h0c2 1 3 2 4 3h0c0-2-4-4-6-6l-2 2h-1c1-1 1-1 1-2 1-1 1-1 1-2v-1c0-1 0-1 1-2v-1c0-1 1-2 1-3h0 0l2-6c2-3 3-6 7-8 2 0 3 0 5-1z" class="C"></path><path d="M545 244c0 1 0 4 1 5v2h-1v1c-2 1-3 2-5 3h-1-1v1 1c1 0 1 0 1 1 1 0 2 1 3 2 0 1 0 2-1 3-2-2-4-5-7-6l-4-3h8c2 0 4-1 5-2 2-3 2-5 1-7l1-1z" class="c"></path><path d="M549 250c0-1-1-2 0-3 0 0 1 1 1 2v19c0 3 0 7-2 10h0c-1-9-9-16-15-21-1-1-4-2-5-4 1 0 1 1 2 1l4 3c3 1 5 4 7 6 1-1 1-2 1-3-1-1-2-2-3-2 0-1 0-1-1-1v-1-1h1 1c2-1 3-2 5-3v-1h1v-2l3 1z" class="T"></path><path d="M546 249l3 1v9c0-2 0-4-1-5v-1l-2-2h0v-2z" class="F"></path><path d="M546 251l2 2v1c1 1 1 3 1 5 0 3 0 7-1 11 0-2-2-5-1-8 1-2 0-7 0-9-1-1-1-1-1-2z" class="D"></path><defs><linearGradient id="AO" x1="550.236" y1="267.76" x2="540.361" y2="256.011" xlink:href="#B"><stop offset="0" stop-color="#828183"></stop><stop offset="1" stop-color="#a8a7a7"></stop></linearGradient></defs><path fill="url(#AO)" d="M546 251h0c0 1 0 1 1 2 0 2 1 7 0 9-1 3 1 6 1 8 1 1 1 1 0 2l-7-9c1-1 1-2 1-3-1-1-2-2-3-2 0-1 0-1-1-1v-1-1h1 1c2-1 3-2 5-3v-1h1z"></path><path d="M545 252c0 1 0 2-1 3 0 0 0 1-1 1h-5 0v-1h1 1c2-1 3-2 5-3z" class="U"></path><path d="M535 223c3 1 5 2 7 4 3 3 5 8 6 12 1 3 2 7 2 10 0-1-1-2-1-2-1 1 0 2 0 3l-3-1c-1-1-1-4-1-5l-1 1h-1v2c-2 2-3 5-6 5h0-1c-2 1-5 0-7-2-1 0-2-1-3-2-3-2-5-7-5-10l2-6c2-3 3-6 7-8 2 0 3 0 5-1z" class="Z"></path><path d="M535 223c3 1 5 2 7 4 3 3 5 8 6 12h-1c-1-1-2-3-3-5-2-4-6-6-11-6-2 0-5 1-6 3-2 2-4 7-4 10 1 1 1 3 2 4v1l3 3h1v1h0c-1 0-2-1-3-2-3-2-5-7-5-10l2-6c2-3 3-6 7-8 2 0 3 0 5-1z" class="P"></path><path d="M535 223c3 1 5 2 7 4v1c-2-1-4-2-6-2-1 0-4 0-5-1l-1-1c2 0 3 0 5-1z" class="N"></path><path d="M533 251l-1-1h0c-1 0-2 0-2-1-2-2-4-5-4-7 0-3 1-5 2-7 2-1 4-3 6-3s5 3 7 5c2 1 4 4 4 7l-1 1h-1v2c-2 2-3 5-6 5h0v-1h0-4z" class="K"></path><path d="M537 244c-1 0 0 0-1-1 1-1 2-2 4-2 1-1 1 0 3 0v1l-5 2h-1z" class="F"></path><path d="M537 244h1 1 3c1 1 1 2 1 3-2 2-3 5-6 5h0v-1h0-4c1 0 3 0 4-1v-1c1-1 2-1 2-2s-1-2-2-3z" class="D"></path><path d="M535 236c2 0 4 1 5 2-1 1-3 2-4 3l-2 2c0 2 1 3 2 5v1s-1 1-3 0c0 0-1 0-1-1-1-3 2-9 3-12z" class="C"></path><defs><linearGradient id="AP" x1="537.704" y1="139.398" x2="512.285" y2="94.588" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#AP)" d="M553 100c3-1 6-1 10-3l17-8c2 0 4-2 5-2l-1 2c-1 1-2 2-2 4-4 0-7 2-11 4l-4 1c-1 1-3 2-3 4v1 1c-1 0-2 1-3 1s-3 1-5 2h-2l-5 2-3 1-1 1h-1l-2 2c-2 1-4 3-5 5h5c3-1 5-1 8-1 0 1 1 2 1 2v2c1 1 0 2 0 3-1 2-2 5-3 7l-2 3c-1 1-1 2-2 2-5 4-9 7-15 8h-5c-4-1-7-2-10-5 0-1-1-1-1-2v-1h1l1-1c0-1 2-3 3-5 3-4 5-8 6-13 0-5-1-7-3-11l-5-4c-1-3-4-4-6-6l3 1c8 3 18 7 28 5 4-1 7-2 11-3l1 1z"></path><path d="M521 106c2 1 4 1 5 3 0 2 0 2-2 3 0 1 1 4 0 5 0-5-1-7-3-11zm30 15c1 1 0 2 0 3-1 2-2 5-3 7h0v-2c-1 0-2 1-3 2v-1c3-2 3-5 4-8h0c1-1 1-1 2-1h0z" class="I"></path><path d="M529 144c0-1 0-1 1-1v-1h3c1-1 2-1 4-2l-1-1-2 1c-1 1-2 1-3 0h1c4-1 8-4 12-4-5 4-9 7-15 8z" class="U"></path><path d="M545 121c1 4-1 8-2 11l-2 2h-2c-2 0-6 0-8-2l-1-1v-1c2 2 4 2 6 2 1 0 3 0 4-1 1 0 1-1 1-2 1-1 2-1 2-2 1-2 2-4 2-6z" class="C"></path><path d="M521 129h0c1 1 0 2 0 3-1 1 0 4 0 6 0 1 0 1 1 1s1 0 2 1v1h1v1c-2 0-3-1-5-1v-1c-1 0-2-1-3-1v-1c-1 0-2-1-2-1v-1c3-1 4-5 6-7z" class="U"></path><path d="M553 100c3-1 6-1 10-3l17-8c2 0 4-2 5-2l-1 2c-1 1-2 2-2 4-4 0-7 2-11 4l-4 1c-1 1-3 2-3 4v1 1c-1 0-2 1-3 1s-3 1-5 2h-2l-5 2-3 1-1 1h-1l-2 2c-2 1-4 3-5 5h5c3-1 5-1 8-1 0 1 1 2 1 2-2 0-3-1-5-1l-1 1c-1 1 0 1 0 2 0 2-1 4-2 6 0 1-1 1-2 2 0 1 0 2-1 2-1 1-3 1-4 1-2 0-4 0-6-2v1c-2-3-2-4-2-7-1-1-1-3-1-4 0-3-1-5 1-7 3-8 17-10 25-13z" class="L"></path><path d="M553 100c3-1 6-1 10-3l17-8c2 0 4-2 5-2l-1 2c-1 1-2 2-2 4-4 0-7 2-11 4l-4 1c-6 1-11 3-17 5-7 1-16 3-20 9-2 2-2 5-2 7l-1 1c0-3-1-5 1-7 3-8 17-10 25-13z" class="P"></path><path d="M541 129h0c-2 1-4 1-5 0-2-1-2-3-2-4v-1-1-1-2s0-1 1-1v-2h1l1-2c1-2 3-4 5-5 1 0 2-1 3-1h1c0-1 1-1 2-1h0c1-1 2-1 3-1 0-1 0 0 1 0l1-1h1c1-1 1 0 2-1h0l3-1c1 0 2 0 3-1h2v1c-1 0-2 1-3 1s-3 1-5 2h-2l-5 2-3 1-1 1h-1l-2 2c-2 1-4 3-5 5h5c3-1 5-1 8-1 0 1 1 2 1 2-2 0-3-1-5-1l-1 1c-1 1 0 1 0 2 0 2-1 4-2 6 0 1-1 1-2 2z" class="e"></path><path d="M543 124c-1 1-1 3-2 4s-3 0-4 0c0 0-1 0-1-1v-1c-1-1-1-3-1-4v-1c0-1 0-1 1-2h1l6 2v3z" class="I"></path><path d="M537 119l6 2v3h-1c-2 0-3 0-5-2v-3z" class="b"></path><defs><linearGradient id="AQ" x1="289.166" y1="247.396" x2="312.611" y2="254.575" xlink:href="#B"><stop offset="0" stop-color="#0d0d0d"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#AQ)" d="M292 229v-1h-1l4-4c1-1 3-4 4-4 1-1 3-1 4-1 2 1 4 1 5 1 4 1 9 1 12 2h3v1c-1 2-1 3-1 5v1c-2 7-1 14 3 20v2c-1 0-4-1-6-1-1 1-2 1-3 1l-4 3h-1c-3 3-7 6-10 9-3 2-6 7-8 11 0 1-1 4-2 5-1-2-2-3-2-5v-1c-1-1-1-2-1-3-1-5 0-11 1-17v1h-1 0c0-6-1-12 1-18 1-2 2-4 3-7h0z"></path><path d="M311 254h-1c-1 0-2 1-4 0s-4 1-6-1c-2-1-3-3-4-5h1c1 1 1 2 3 3s4 1 6 1l3-1h1c2 1 4-1 6-2l1 1-1 1-4 3h-1z" class="G"></path><path d="M292 229l1-1c1-1 3-2 4-4h1 1c2-1 4-2 6-2v1c-4 1-8 4-10 8-3 5-5 11-6 17v5 1h-1 0c0-6-1-12 1-18 1-2 2-4 3-7h0z" class="a"></path><path d="M292 229v-1h-1l4-4c1-1 3-4 4-4 1-1 3-1 4-1 2 1 4 1 5 1 4 1 9 1 12 2h3v1c-2 0-3 0-3 1-2 2-2 7-2 9-1-2-2-3-4-5-1-2-2-4-4-4-2-1-3-1-5-1v-1c-2 0-4 1-6 2h-1-1c-1 2-3 3-4 4l-1 1z" class="V"></path><path d="M318 233c0-2 0-7 2-9 0-1 1-1 3-1-1 2-1 3-1 5v1c-2 7-1 14 3 20v2c-1 0-4-1-6-1-1 1-2 1-3 1l1-1-1-1c-2 1-4 3-6 2h-1c2-1 4-2 6-4 3-4 3-9 3-14z" class="E"></path><path d="M316 249h0c2-3 3-6 4-9 0 3 0 6 1 8v1c-2 0-2 0-4 1l-1-1z" class="T"></path><defs><linearGradient id="AR" x1="325.275" y1="124.686" x2="359.48" y2="162.413" xlink:href="#B"><stop offset="0" stop-color="#a8a7a7"></stop><stop offset="1" stop-color="#d2d1d1"></stop></linearGradient></defs><path fill="url(#AR)" d="M391 118l4-1 8-3c1 0 1-1 2 0 0 2-1 5-2 7-4 10-11 19-19 25-2 1-4 3-6 4-1 1-3 1-4 2l-4 2v-1c-1 0-1 0-1 1h-2v-1c-2 0-3 1-5 2v-1c-3 1-6 1-8 1-4 1-8 2-12 2h-6c-8-2-16-3-24-6-4-1-7-4-10-7 0 0 0-1 1-1h2 0s-1 0-1-1h1 1 1c1 1 1 1 2 1 2 0 6 1 8 1 1 0 3-2 3-2h1c1 0 2 0 2-1 1 1 2 3 2 4h1v-1l2 2h0l3-3h1l1-1c2-1 7-1 9-1-1 1-1 1-2 1l-1 3c-2 0-4 2-5 3h3 1v-1h2c1-1 1-1 2-1h1 1c4 0 7-2 11-2 0-1-1-2-2-2h0v-1l4-1c1 1 1 1 3 2 1-1 2-1 3-2h0c-1-3-3-6-3-8 2-1 3-2 5-1 1 0 3 2 4 2 2 2 6 7 9 7 2-1 2-1 2-3v-1h0c-1-1-2-2-2-3 0-3 1-5 3-6-1 2-1 4-1 6h1c0-2 2-6 2-8 0 0-1-1-1-2 1 0 2 1 3 1s3-1 4-2 1-2 1-3h0l1-1z"></path><path d="M355 144c0-1-1-2-2-2h0v-1l4-1c1 1 1 1 3 2 1-1 2-1 3-2v1c0 1 0 2-2 3-1 0-3 1-5 0h-1zm6 1h1v1c-1 1-3 1-4 2-5 2-9 5-15 5 0 0-1 0-1-1h0c1-1 3-1 5-2v-1h2l3-1 1-1h0c1 0 3 0 4-1 1 0 3-1 4-1z" class="I"></path><path d="M312 151c-4-1-7-4-10-7 0 0 0-1 1-1h2 0s-1 0-1-1h1 1 1c1 1 1 1 2 1 5 3 13 0 18 5 1-1 1-1 2 0-1 1-2 0-2 1v1h-2 0l1 1h0c-3 0-5-1-8-2-1-1-3-1-4-2-4-1-7-2-10-3 0 0 0 1 1 1 2 2 5 4 7 6z" class="J"></path><path d="M332 143l1-1c2-1 7-1 9-1-1 1-1 1-2 1l-1 3c-2 0-4 2-5 3h-1c-1 1-3 1-5 2 0 1 2 1 3 1-2 1-3 1-5 0h0l-1-1h0 2v-1c0-1 1 0 2-1-1-1-1-1-2 0-5-5-13-2-18-5 2 0 6 1 8 1 1 0 3-2 3-2h1c1 0 2 0 2-1 1 1 2 3 2 4h1v-1l2 2h0l3-3h1z" class="K"></path><path d="M332 143l1-1c2-1 7-1 9-1-1 1-1 1-2 1l-1 1c-3 0-6 3-10 4l3-4z" class="Z"></path><path d="M391 118l4-1 8-3c1 0 1-1 2 0 0 2-1 5-2 7-4 10-11 19-19 25-2 1-4 3-6 4-1 1-3 1-4 2l-4 2v-1c-1 0-1 0-1 1h-2v-1c-2 0-3 1-5 2v-1c3-2 6-4 10-5 2 0 6-2 8-3s4-4 6-5c1-2 3-3 5-4 6-6 9-13 12-21-1 0-8 2-9 2l-1 1h-1-1-1l1-1z" class="K"></path><path d="M385 124v1c1 0 3 1 4 1 3-1 7-4 9-6 0-1 1-2 1-2h2c0 1-1 1-2 2l-1 1-3 6c-4 5-10 13-15 14h-1l-1-1c2-1 2-1 2-3v-1h0c-1-1-2-2-2-3 0-3 1-5 3-6-1 2-1 4-1 6h1c0-2 2-6 2-8 0 0-1-1-1-2 1 0 2 1 3 1z" class="b"></path><path d="M645 330c1-1 1-1 3-1 0 2 1 3 2 4l1 1c1-1 1-2 1-3 6 11 8 23 9 35 0 9-1 18-2 27-1 1-1 1 0 2 0 1 0 2-1 3l-1 4c-1 1-1 3-2 5-1 4-3 9-6 12v-1l2-5v-1 1l-1 2c-1-1 0-1 0-2l-1 1v1c-1 1-1 2-2 4-3 4-5 7-9 11-1 1-2 2-3 4h-2l-3 3 3-6h-2c3-4 7-8 8-13l3-10c2-11 0-22-2-34 1-3 0-5 1-8 0-1 0-2 1-3 0-1 0-1 1-1v-26-2c1-2 1-3 2-4z" class="I"></path><path d="M648 405c0-1 1-2 2-4 1 0 1 0 2 1v1l-4 2h0z" class="d"></path><path d="M642 408c0 3 0 6-1 8-1 1-1 3-2 5h0c1-1 2-3 4-4l-10 14h-2c3-4 7-8 8-13l3-10z" class="C"></path><path d="M656 357l1 3 1-1h0c2 11 1 25-2 35 0 2 0 4-1 5h-1c1-4 2-7 2-11 1-2 0-4 1-7 0-8 0-15-1-24z" class="Q"></path><defs><linearGradient id="AS" x1="650.456" y1="413.79" x2="637.598" y2="416.298" xlink:href="#B"><stop offset="0" stop-color="#363738"></stop><stop offset="1" stop-color="#595657"></stop></linearGradient></defs><path fill="url(#AS)" d="M648 405h0l4-2c-3 7-6 15-10 21-3 3-6 6-9 10l-3 3 3-6 10-14s1-2 1-3c1-3 2-6 4-9z"></path><defs><linearGradient id="AT" x1="651.67" y1="337.748" x2="637.486" y2="347.855" xlink:href="#B"><stop offset="0" stop-color="#131310"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#AT)" d="M645 330c1-1 1-1 3-1 0 2 1 3 2 4-1 0-2 0-4 1-1 3-2 10-1 14v14c0 4 1 8 0 12l-2-12v-26-2c1-2 1-3 2-4z"></path><path d="M650 333l1 1c5 8 7 16 7 25h0l-1 1-1-3c0-4-1-9-3-13 0-2-1-3-3-4h-3c-2 2-2 5-2 8-1-4 0-11 1-14 2-1 3-1 4-1z" class="D"></path><path d="M651 334c1-1 1-2 1-3 6 11 8 23 9 35 0 9-1 18-2 27-1 1-1 1 0 2 0 1 0 2-1 3l-1 4c-1 1-1 3-2 5-1 4-3 9-6 12v-1l2-5c2-3 3-7 4-10v-1c1-1 1-1 1-3h0l-2 2v-2h1c1-1 1-3 1-5 3-10 4-24 2-35 0-9-2-17-7-25z" class="K"></path><defs><linearGradient id="AU" x1="567.468" y1="247.422" x2="600.556" y2="266.492" xlink:href="#B"><stop offset="0" stop-color="#131212"></stop><stop offset="1" stop-color="#3d3c3d"></stop></linearGradient></defs><path fill="url(#AU)" d="M568 252c0-2 1-4 2-5l3-9c2-6 7-13 12-16 2 0 4-1 7-2l1-1c5-1 12-1 17 2 3 0 5 2 7 3 1 1 3 2 4 3 0 1 1 2 1 2 3 2 6 5 7 8-5-4-12-5-17-6-8 0-15 1-22 7-1 0-2 1-3 2-2 2-3 5-3 8 0 4 2 9 2 13h0l1 1c0 2 1 3 2 5-3-2-5-3-8-5v1c-1-1-1-1-2-1h0v1h1v2c-1 0-1 2-2 2h-1l-1 2-2-2c-1 4-1 6 0 10v1l-1 1-1-1-2 2 1 1v1h0c-1 1-2 1-2 2-1 0-1 1-1 1-1 1-2 2-2 3-1 1-1 4-2 5s-3 2-4 2l-2 2h-1l1 2c-3 2-5 4-6 8l1 1-7 10c-2 4-5 8-8 11h-2l-5 5h-1c7-7 13-18 16-27 4-6 6-13 8-20h1c1-1 1-2 2-4 1-4 2-8 3-13l3-9c0-1 0-3 1-5h0v3-1c1-1 1-1 1-2s0-1 1-1c0-1 1-2 1-4l1 1z"></path><path d="M567 276c-1-1-1-2-1-2 1-1 1-1 3-2l1 1c-1 1-2 1-3 3z" class="N"></path><path d="M557 288l1-2h1c0 3-1 7 1 9l-2 2 1-1-2-4c0-1 1-3 0-4zm13-15c1 2 2 3 3 4s0 1 1 1l-1 1-1-1-2 2c-1-1-2-3-3-4 1-2 2-2 3-3z" class="S"></path><defs><linearGradient id="AV" x1="580.687" y1="240.766" x2="597.468" y2="244.332" xlink:href="#B"><stop offset="0" stop-color="#1f1f1f"></stop><stop offset="1" stop-color="#59595a"></stop></linearGradient></defs><path fill="url(#AV)" d="M588 233v-1c1 0 1-1 2-1h0c1-1 3-2 4-2l3-1c1 0 2-1 3-1 4 0 9 2 12 4-8 0-15 1-22 7-1 0-2 1-3 2-2 2-3 5-3 8 0 4 2 9 2 13h0l1 1c0 2 1 3 2 5-3-2-5-3-8-5l-2-2c-2-4-3-9-2-14h0v-1c2-5 7-10 11-12z"></path><defs><linearGradient id="AW" x1="577.704" y1="245.035" x2="583.846" y2="247.749" xlink:href="#B"><stop offset="0" stop-color="#292929"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#AW)" d="M588 233h1c-2 1-4 2-5 4h0v1c-3 3-5 6-5 10 0 5 3 9 6 12v1h1l1 1c0 2 1 3 2 5-3-2-5-3-8-5l-2-2c-2-4-3-9-2-14h0v-1c2-5 7-10 11-12z"></path><defs><linearGradient id="AX" x1="556.91" y1="291.729" x2="548.197" y2="289.22" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#4c4b4c"></stop></linearGradient></defs><path fill="url(#AX)" d="M567 251l1 1v2l-1 1-2 12c-1 2-3 5-3 8 0 1 0 1-1 2v2 1c0 1-1 1-1 2s-1 1-1 2l-1 1 1-1-1-1c0 1-1 2-1 3v2c1 1 0 3 0 4l2 4-1 1h-1l1 2c-3 2-5 4-6 8l1 1-7 10c-2 4-5 8-8 11h-2l-5 5h-1c7-7 13-18 16-27 4-6 6-13 8-20h1c1-1 1-2 2-4 1-4 2-8 3-13l3-9c0-1 0-3 1-5h0v3-1c1-1 1-1 1-2s0-1 1-1c0-1 1-2 1-4z"></path><path d="M554 300l1-2c0-2 1-4 2-6l2 4-1 1h-1l-3 3z" class="d"></path><path d="M554 300l3-3 1 2c-3 2-5 4-6 8l1 1-7 10v-1c0-1 0-1 1-2v-1l1-1 1-1v-1l1-1c1-1 1 0 1-1v-1-1-1c1-1 1-1 1-2-1 1-2 3-3 3h0v-1h1l4-6z" class="S"></path><path d="M265 262c1 0 2-1 3-1 1 2 2 4 4 6 0 0 1 1 1 2l1 4-8 8c-2-1-5-1-8-1-2 2-9 1-12 1l-6 1c-2 0-4 0-5 2-1 1-1 2-2 3s-4 2-5 2l-1 1c-1 0-2 1-3 2-3 2-8 3-10 6-5 3-8 9-11 14-2 5-5 9-6 14-1 2-2 3-2 5 1 3 2 7 2 10v4c-1-1-1-5-1-7 0-1 0-2-1-3v-2h0c0-1-1-3-2-3h-1c-2 0-4 3-5 5-2 3-2 7-4 10v-1c-1 0-1 1-1 2h0v2h0v2h-1c0 1 0 3-1 4h0c-1 9-1 17 0 26 0 1 1 6 0 7h0v4 1c0 1 0 1 1 2 0 2 0 3 1 5h0l1-1c1 1 2 4 3 5l2 5c-1 1-1 2-1 3l-1-1c0 3 1 6 1 9-3-4-5-10-6-14h-1v-1l-2-4v-1c0-2-1-3-1-4s0 0-1-1c0-2 0-4-1-5 0-2 0-3-1-4v-1-2c-1-3 0-5-1-8 0-2-1-5-1-8l1 1v-1-1-1c0-8 2-18 4-26v-6h-1v-2l1-4v2l2-2c3-8 7-15 13-22l-1-1c0-1-1-2-1-3h0-1l1-2v-2c0-1 0-1 1-2v-2c2-2 4-5 7-6 1 0 1-1 1-1 1-1 2-2 3-4l2-2v1c1-1 1-3 3-4h0c0 2 0 2 1 3h2l2-1c0-4 3-7 5-9v-1c1 0 2-1 2-2s0-1 1-2l1 1c1 1 2 2 4 2 1-1 1-1 2-1h3c2 2 4 3 7 3 4 1 7 1 10 1l8-3h0l6-3c1-1 3-2 4-2z" class="N"></path><path d="M205 305c1-5 3-7 6-10l1 1-2 2h0c-1 2-2 3-3 4s-1 2-2 3z" class="P"></path><path d="M180 387v-1l-1-1c0-4-1-8-1-12-1-8 1-15 1-22 1 2 0 4 0 6l1-3c-1 9-1 17 0 26 0 1 1 6 0 7h0z" class="d"></path><path d="M234 279c1-1 1-1 2-1 2-1 3-2 4-1 1 0 3 0 4-1l1 1h-2 3l1 1h1 2l1 1h1l6 1c-2 2-9 1-12 1l-6 1-1-1c3-1 6-1 9-1-5-1-10 0-15-1h1z" class="P"></path><path d="M234 279c1-1 1-1 2-1 2-1 3-2 4-1 1 0 3 0 4-1l1 1h-2 3l1 1h1 2l1 1h1-18z" class="c"></path><path d="M181 405l-2-5 1 1v-3l5 13v-2-1h-1v-5l-2-4 1-1c1 1 2 4 3 5l2 5c-1 1-1 2-1 3l-1-1c0 3 1 6 1 9-3-4-5-10-6-14z" class="S"></path><path d="M182 399l1-1c1 1 2 4 3 5l2 5c-1 1-1 2-1 3l-1-1c0-1-2-5-2-7l-2-4z" class="W"></path><path d="M192 304c1-2 1-2 4-3h-1c-2 5-5 9-8 14s-6 10-8 15l-2 8v-6h-1v-2l1-4v2l2-2c3-8 7-15 13-22z" class="M"></path><path d="M176 330l1-4v2l2-2-2 6h-1v-2z" class="B"></path><path d="M210 298c1-1 2-2 3-2 3-3 8-6 11-7h1c1-1 2-1 2-1 1-1 2-1 3-1v-1l1 1c0-1 1-1 1-2v-1c2-1 4-3 7-3l1 1c-2 0-4 0-5 2-1 1-1 2-2 3s-4 2-5 2l-1 1c-1 0-2 1-3 2-3 2-8 3-10 6-5 3-8 9-11 14-2 5-5 9-6 14-1 2-2 3-2 5-1-1-1-2-3-2h0l1-1 2 2v-3c-1 0-1 0 0-1v1c1-1 1-1 1-2l1-3 1-1c0-1 1-2 1-3v-1c1-1 2-2 2-3h-1v-1l1-1c0-1 1-1 1-2h1c-1-2 1-4 2-5s1-2 2-3 2-2 3-4z" class="M"></path><path d="M265 262c1 0 2-1 3-1 1 2 2 4 4 6 0 0 1 1 1 2l1 4-8 8c-2-1-5-1-8-1l-6-1h-1l-1-1h-2-1l-1-1h-3 2l-1-1c-1 1-3 1-4 1-1-1-2 0-4 1-1 0-1 0-2 1h-1c-4 1-7 2-10 3l-12 7c-2 0-4 1-6 2l-8 8-1 2c-3 1-3 1-4 3l-1-1c0-1-1-2-1-3h0-1l1-2v-2c0-1 0-1 1-2v-2c2-2 4-5 7-6 1 0 1-1 1-1 1-1 2-2 3-4l2-2v1c1-1 1-3 3-4h0c0 2 0 2 1 3h2l2-1c0-4 3-7 5-9v-1c1 0 2-1 2-2s0-1 1-2l1 1c1 1 2 2 4 2 1-1 1-1 2-1h3c2 2 4 3 7 3 4 1 7 1 10 1l8-3h0l6-3c1-1 3-2 4-2z" class="C"></path><path d="M237 269c4 1 7 1 10 1l-3 1c-1 0-3 1-5 2-8 3-16 4-23 8h-4c0-1 1-1 1-2 1-1 1-2 2-3l1 1c1 0 6-3 8-4 3-1 7-2 10-3 1 0 2 0 3-1z" class="E"></path><path d="M215 276l1 1c1 0 6-3 8-4-1 2-3 2-5 3s-4 4-5 4l-1-1c1-1 1-2 2-3z" class="D"></path><path d="M217 268c1 0 2-1 2-2s0-1 1-2l1 1c1 1 2 2 4 2 1-1 1-1 2-1h3c2 2 4 3 7 3-1 1-2 1-3 1-3 1-7 2-10 3-2 1-7 4-8 4l-1-1c-1 1-1 2-2 3 0 1-1 1-1 2h0v-1-1c-1 1-2 2-3 2-1 1-1 2-2 3-1 2-3 3-5 4 1-1 3-3 3-4 1-1 2-3 4-4h1v-1l2-1c0-4 3-7 5-9v-1z" class="F"></path><path d="M212 278c3-3 5-9 8-12l-1 5-1 1c-1 2-2 3-3 4s-1 2-2 3c0 1-1 1-1 2h0v-1-1c-1 1-2 2-3 2-1 1-1 2-2 3-1 2-3 3-5 4 1-1 3-3 3-4 1-1 2-3 4-4h1v-1l2-1z" class="Q"></path><path d="M204 280c1-1 1-3 3-4h0c0 2 0 2 1 3h2v1h-1c-2 1-3 3-4 4 0 1-2 3-3 4 2-1 4-2 5-4 1-1 1-2 2-3 1 0 2-1 3-2v1 1h0 4l-4 2c-2 1-2 2-3 3l1 1c1 0 2 0 3-1 3-2 5-4 9-4h1l-12 7c-2 0-4 1-6 2l-8 8-1 2c-3 1-3 1-4 3l-1-1c0-1-1-2-1-3h0-1l1-2v-2c0-1 0-1 1-2v-2c2-2 4-5 7-6 1 0 1-1 1-1 1-1 2-2 3-4l2-2v1z" class="D"></path><path d="M190 300h1c1 0 1 1 2 1s1-1 2-2h2l-1 2c-3 1-3 1-4 3l-1-1c0-1-1-2-1-3z" class="H"></path><path d="M204 279v1c-5 6-8 13-14 18v-2c0-1 0-1 1-2v-2c2-2 4-5 7-6 1 0 1-1 1-1 1-1 2-2 3-4l2-2z" class="S"></path><path d="M202 288c2-1 4-2 5-4 1-1 1-2 2-3 1 0 2-1 3-2v1 1h0 4l-4 2c-2 1-2 2-3 3l1 1c1 0 2 0 3-1 3-2 5-4 9-4h1l-12 7c-2 0-4 1-6 2h-1c-2 0-5 3-6 4s-2 2-3 4c2-5 4-8 7-11z" class="c"></path><defs><linearGradient id="AY" x1="253.973" y1="266.583" x2="260.967" y2="281.625" xlink:href="#B"><stop offset="0" stop-color="#a9a8a8"></stop><stop offset="1" stop-color="#c8c7c7"></stop></linearGradient></defs><path fill="url(#AY)" d="M265 262c1 0 2-1 3-1 1 2 2 4 4 6 0 0 1 1 1 2l1 4-8 8c-2-1-5-1-8-1l-6-1h-1l-1-1h-2-1l-1-1h-3 2l-1-1h0c3-1 6-1 8-3h0-1c-1 0-1 1-2 1h-1c-1 1-3 2-4 2h-1-1 0c3-3 9-3 11-7h1c1 0 1-1 2-1l-1-1h0l6-3c1-1 3-2 4-2z"></path><path d="M265 262c1 0 2-1 3-1 1 2 2 4 4 6 0 0 1 1 1 2l-1-1c-1 1-2 1-3 2h0v-4c-1-1-1-2-3-3-1 1-2 3-3 4-1 2-3 3-4 4h0c1-2 2-3 3-4s1-2 2-3c1 0 1-1 1-1v-1z" class="e"></path><path d="M635 284c4 5 9 10 13 15 1 0 1 1 2 1-1-3-1-7-1-10 0-1-1-1-1-2 3 0 2 2 4 2v4l1 3c1 5 3 10 5 15h0 1 0l3 3c2 5 3 11 4 17l3 15c0 4 1 8 1 12v3 4c0 2 1 5 0 6 0 2 0 3-1 4v1s1 0 0 1v2c-1 2-1 4 0 5v1l2-2v-1l1 1 1-1s1 0 2-1c0 0 1-1 1-2s0-3 1-4h0c0 2-1 3-1 5h1 1l1 1c-1 1-5 2-6 3-1 2-1 6-2 8-2 4-4 9-7 13-11 23-25 43-42 61-3 3-7 6-10 8-1 0-2 1-3 2l-12 9-4 3c-2 1-4-1-7 0 1-1 0-2 0-3l-2-1s-1-1-1-2c-1-1-3-2-4-3h3l5-1v-1c-1-1-2-1-3-1v-1h0c3 0 5 0 7-1 3-3 6-6 8-10l18-18c3-2 5-6 8-9l6-7h2l-3 6 3-3h2c1-2 2-3 3-4 4-4 6-7 9-11 1-2 1-3 2-4v-1l1-1c0 1-1 1 0 2l1-2v-1 1l-2 5v1c3-3 5-8 6-12 1-2 1-4 2-5l1-4c1-1 1-2 1-3-1-1-1-1 0-2 1-9 2-18 2-27 0-1 1-2 1-3l1 1c1-6-1-13-2-19-1-5-1-10-3-15-1-5-4-10-6-15-1 0-2-2-2-3-3-4-5-8-8-12-1-2-3-4-5-6h1c0-1-1-2-1-2-1-2-3-4-3-7h1v-1z" class="L"></path><path d="M634 443c-1 1-1 2-2 3-1 3-3 5-4 7l-6 6v-1l-1 1h0c1-3 4-6 6-9l14-20c3-4 6-8 8-12v1c0 1-1 2-2 4l-9 13c-1 2-3 5-4 7z" class="U"></path><path d="M615 457c1-1 2-3 4-4s3-5 5-7c0 2-1 3-2 4h0v1h0c0 3-4 6-6 9-1 1-1 3-3 4a30.44 30.44 0 0 1-8 8c-4 2-8 4-12 5 2-3 4-5 7-8 6-2 10-8 15-12z" class="b"></path><path d="M617 447c3-2 5-6 8-9v3l-1 1s-1 1-1 2-1 2-1 3c-1 2-3 4-4 6-1 1-2 2-3 4-5 4-9 10-15 12-3 1-4 4-7 5h0c-1 0-1 1-2 1 3-3 6-6 8-10l18-18z" class="C"></path><path d="M590 478c6 0 12 0 18-4h0c1 0 2-1 2-1l1-1h1l2-2c1-1 2-2 3-2h0l-10 8v1c1 0 1-1 2 0l-12 9-4 3c-2 1-4-1-7 0 1-1 0-2 0-3l-2-1s-1-1-1-2c-1-1-3-2-4-3h3l5-1v-1h3z" class="U"></path><path d="M587 478h3l3 1h0l-4 1c-1 0-2 1-3 2s-2 1-3 1c-1-1-3-2-4-3h3l5-1v-1z" class="F"></path><path d="M586 486c2-1 3-2 5-3 2-2 3-2 6-2h0 1 2c-2 1-5 2-7 4h-1c-1 1-1 1-1 2l2 2h0c-2 1-4-1-7 0 1-1 0-2 0-3z" class="C"></path><path d="M607 477c1 0 1-1 2 0l-12 9-4 3h0l-2-2c0-1 0-1 1-2h1c2-2 5-3 7-4 3-1 5-3 7-4z" class="I"></path><path d="M593 489h0l-2-2c0-1 0-1 1-2l1 1h4l-4 3z" class="L"></path><defs><linearGradient id="AZ" x1="674.493" y1="304.299" x2="604.22" y2="431.609" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#d7d5d6"></stop></linearGradient></defs><path fill="url(#AZ)" d="M635 284c4 5 9 10 13 15 1 0 1 1 2 1-1-3-1-7-1-10 0-1-1-1-1-2 3 0 2 2 4 2v4l1 3c1 5 3 10 5 15h0 1 0l3 3c2 5 3 11 4 17l3 15c0 4 1 8 1 12v3 4c0 2 1 5 0 6 0 2 0 3-1 4v1s1 0 0 1v2c-1 2-1 4 0 5v1l2-2v-1l1 1c-1 3-3 5-4 7-1 4-2 8-5 11-1 2-2 4-3 5-2 4-4 9-7 14-6 12-15 24-24 34h-1l3-3 4-6c0 1-1 2-2 2 0-1 0-2 1-2v-3c1-2 3-5 4-7l9-13c1-2 2-3 2-4 3-3 5-8 6-12 1-2 1-4 2-5l1-4c1-1 1-2 1-3-1-1-1-1 0-2 1-9 2-18 2-27 0-1 1-2 1-3l1 1c1-6-1-13-2-19-1-5-1-10-3-15-1-5-4-10-6-15-1 0-2-2-2-3-3-4-5-8-8-12-1-2-3-4-5-6h1c0-1-1-2-1-2-1-2-3-4-3-7h1v-1z"></path><path d="M662 363l1 1c0 4 0 8-1 12l-3 19c-1-1-1-1 0-2 1-9 2-18 2-27 0-1 1-2 1-3z" class="B"></path><defs><linearGradient id="Aa" x1="663.989" y1="351.268" x2="672.069" y2="358.702" xlink:href="#B"><stop offset="0" stop-color="#323135"></stop><stop offset="1" stop-color="#5b5a58"></stop></linearGradient></defs><path fill="url(#Aa)" d="M666 349c1-1 2-2 3-2 0 4 1 8 1 12v3l-1 5v-3c-1 1-1 1-1 2 0 3-1 5-2 7v-8c0-2 1-3 2-4 0-4-1-9-2-12z"></path><path d="M658 312h1 0l3 3c2 5 3 11 4 17l3 15c-1 0-2 1-3 2-1-8-2-16-4-23-1-5-2-10-4-14z" class="S"></path><path d="M635 285l9 12c3 4 7 8 9 13 0 1-2 1-3 2 1 1 2 2 2 3h0c-1 0-2-2-2-3-3-4-5-8-8-12-1-2-3-4-5-6h1c0-1-1-2-1-2-1-2-3-4-3-7h1z" class="Q"></path><defs><linearGradient id="Ab" x1="667.476" y1="387.354" x2="659.928" y2="387.721" xlink:href="#B"><stop offset="0" stop-color="#3f403e"></stop><stop offset="1" stop-color="#555155"></stop></linearGradient></defs><path fill="url(#Ab)" d="M670 362v4c0 2 1 5 0 6 0 2 0 3-1 4v1s1 0 0 1v2c-1 2-1 4 0 5v1l2-2v-1l1 1c-1 3-3 5-4 7-1 4-2 8-5 11-1 2-2 4-3 5-2 4-4 9-7 14-6 12-15 24-24 34h-1l3-3 4-6c3-3 5-6 7-9 6-9 12-19 16-29 4-11 6-23 8-35 1-2 2-4 2-7 0-1 0-1 1-2v3l1-5z"></path><path d="M670 362v4c0 2 1 5 0 6 0 2 0 3-1 4v1s1 0 0 1v2c-1 2-1 4 0 5v1l2-2v-1l1 1c-1 3-3 5-4 7-1 4-2 8-5 11-1 2-2 4-3 5 1-5 4-10 5-16 1-3 1-6 2-9l2-15 1-5z" class="U"></path><defs><linearGradient id="Ac" x1="169.657" y1="315.869" x2="229.2" y2="323.911" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#3e3d3e"></stop></linearGradient></defs><path fill="url(#Ac)" d="M228 236h6c2 1 4 2 5 2 3 1 7 2 9 4l-6-2-1 1 7 4c-4 0-8 0-11 1-1 0-3 0-5 1h-2 0l-2 1h0c-1 1-2 1-2 2h-2-1s1 1 1 2v1h0l-1 1c-1 2-2 2-2 3-2 2-3 4-4 7h0c1 1 0 3 0 4v1c-2 2-5 5-5 9l-2 1h-2c-1-1-1-1-1-3h0c-2 1-2 3-3 4v-1l-2 2c-1 2-2 3-3 4 0 0 0 1-1 1-3 1-5 4-7 6v2c-1 1-1 1-1 2v2l-1 2h1 0c0 1 1 2 1 3l1 1c-6 7-10 14-13 22l-2 2v-2l-1 4v2h1v6c-2 8-4 18-4 26v1 1 1l-1-1c0 3 1 6 1 8 1 3 0 5 1 8v2c0 1 0-1 0 1 1 1 1 2 1 4 1 1 1 3 1 5 1 1 1 0 1 1s1 2 1 4v1l2 4v1l-1-1-1-2-1-2-1-1c0-1-1-2-1-3 0-2-1-3-2-5l-1-5c-1 0-1-1-1-1 0-4-1-7-1-11-1-3 0-8-1-12l-1-14c-1-7-1-17 0-24 1-1 1-2 1-3h-2v-2c1-1 1-2 1-3l-1-1v2c-1-2 1-5 0-7 0-4 2-8 3-11 4-13 9-25 14-36 1-1 3-4 3-5l5-9h1c1 0 1-1 2 0h1c1-1 3-2 4-3l1-1h1l1-1c1 0 2-1 3-2h1c1-1 1-1 2-1 1-1 2-2 3-2s1 0 2-1c1 0 3-1 5-1 1 0 1-1 2-1h0 7 0z"></path><path d="M176 330v2h1v6c-2 8-4 18-4 26l-1-1c-1-11 2-22 4-33z" class="G"></path><path d="M184 304c0-5 2-9 3-13 1-2 1-2 3-3 2 0 4-1 5-2s1-1 2-1h1l-1 1h1c-3 1-5 4-7 6v2c-1 1-1 1-1 2l-1-1v2c0 1-1 2-1 3l-4 7-1-1 1-2z" class="W"></path><path d="M184 304c2-2 3-7 3-10 1-2 1-3 2-5l1 1-1 5v2c0 1-1 2-1 3l-4 7-1-1 1-2z" class="D"></path><path d="M188 300c0-1 1-2 1-3v-2l1 1v2l-1 2h1 0c0 1 1 2 1 3l1 1c-6 7-10 14-13 22l-2 2v-2c1-7 3-14 6-20l1 1 4-7z" class="X"></path><path d="M188 300c0 1 1 3 1 4-1 2-3 3-4 5s-2 4-4 6c1-2 2-5 3-8l4-7z" class="W"></path><path d="M192 249h1c1 0 1-1 2 0h1c-1 1-2 2-2 4l-1 1h0c-1 1-1 1-1 2-1 1-2 3-3 5-6 13-12 27-16 41-1 3-2 7-3 10v2c-1 1-1 2-1 3v4h-2v-2c1-1 1-2 1-3l-1-1v2c-1-2 1-5 0-7 0-4 2-8 3-11 4-13 9-25 14-36 1-1 3-4 3-5l5-9z" class="P"></path><path d="M207 270c1-2 1-3 2-4v-3c1 0 1-1 1-1 1-3 3-5 4-7 2-2 4-4 7-6l-1-1c0-1 0-1 1-2l1 1h3c0-2 6-4 7-4 2-1 6 0 7-2 0 0-1 0-1-1h0 4l-1 1 7 4c-4 0-8 0-11 1-1 0-3 0-5 1h-2 0l-2 1h0c-1 1-2 1-2 2h-2-1s1 1 1 2v1h0l-1 1c-1 2-2 2-2 3-2 2-3 4-4 7h0c1 1 0 3 0 4v1c-2 2-5 5-5 9l-2 1h-2c-1-1-1-1-1-3h0c-2 1-2 3-3 4v-1-1c1-1 1-1 1-2-1-1-2-2-3-2h0s0-1 1-1c1-1 3-3 4-3z" class="W"></path><defs><linearGradient id="Ad" x1="208.455" y1="267.841" x2="213.285" y2="271.578" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#Ad)" d="M224 252v1h0l-1 1c-1 2-2 2-2 3-2 2-3 4-4 7h0c1 1 0 3 0 4v1c-2 2-5 5-5 9l-2 1h-2c-1-1-1-1-1-3h0c-2 1-2 3-3 4v-1-1c1-1 1-1 1-2-1-1-2-2-3-2h0s0-1 1-1c1-1 3-3 4-3h1c2-1 6-4 7-6 1-1 2-1 2-3l1-1c1-1 0-1 1-2 0-1 2-3 3-5 1-1 1-1 2-1z"></path><defs><linearGradient id="Ae" x1="735.842" y1="265.652" x2="697.099" y2="287.126" xlink:href="#B"><stop offset="0" stop-color="#515050"></stop><stop offset="1" stop-color="#737373"></stop></linearGradient></defs><path fill="url(#Ae)" d="M668 213h0l1-1 1 1h3c1 0 2 2 2 3l1 1 1 1 3 3h0c1 2 4 5 5 6 2 2 3 3 4 5 5 6 10 13 17 18 1 0 1 1 2 1h2l8 7c2 1 3 4 5 5l1-1c2 1 4 2 5 4l3 2c2 2 4 4 7 4 2 1 3 3 5 3l1 3s2 1 2 2c1 2 4 5 5 8-1 1-1 1-1 3v1 1c5 9 8 18 10 27 2 5 2 10 3 15l1 14-1 1c-1-2 0-4 0-6-1 6 0 13 0 20 0 2-1 5-2 7v3-7c-1-1-1-2-1-3v-3c-1-1-1-7-2-9 0 1 0 1-1 1v-3 5 1l-2-1c-2 2-2 4-4 5l3-9c-2-6-5-12-10-16v-2l-6-3-4-1h0c-2-2-4-2-7-2v-1c2 0 4-1 6 0 1 1 3 2 5 3s5 2 8 3l5 3c-3-6-7-11-13-14l1-2-1-1v-2h0c-3-5-6-9-11-12 0-1 1-1 2-1v-1-1c1 0 2-1 3-2l-1-1c-2-1-6 0-9 0h-7v-1c4 0 9-1 13-1v-2l1-1c0-1-1-3-2-5-4-6-9-10-15-16-1 0-7-5-9-6l-27-19-3-3h-1l1-1c-2-1-4-2-5-4-1-1-3-2-5-3l-8-5 1-1c1-1 1-2 2-3l4-4c2-1 5-5 6-7 1-1 1-2 0-3h-1z"></path><path d="M747 280c1 2 4 5 5 8-1 1-1 1-1 3v1 1c-1-2-2-5-3-8-1-2-2-3-1-5z" class="E"></path><path d="M751 309c0 1 1 2 2 2 3 4 5 11 5 16l-7-18z" class="H"></path><path d="M731 269h0c5 3 8 8 14 9-1 1-2 2-2 3l-1-1h1c-1-1-2-1-2-1h-1v1l-9-11z" class="f"></path><path d="M723 263l1-1c2 1 4 2 5 4l3 2c2 2 4 4 7 4 2 1 3 3 5 3l1 3h0c-6-1-9-6-14-9h0c-2-1-4-4-7-5l-1-1z" class="N"></path><path d="M761 343c0-1 1-1 1-2v-2c0-3-1-5-1-8l-3-11c0-2-1-4-1-7 5 10 6 21 7 31-1 6 0 13 0 20 0 2-1 5-2 7v3-7-12c0-4-1-8-1-12zm-48-71h1c3 2 5 3 7 5 2 0 3 1 5 2s3 4 5 6c7 6 14 13 18 22h0-1v5 1c-2-3-4-6-6-8h-1-1l1-2-3-1h0l1-1c-2-1-2 0-4 0l-2 1h-3v-1c1 0 2-1 3-2l-1-1c-2-1-6 0-9 0h-7v-1c4 0 9-1 13-1v-2l1-1c0-1-1-3-2-5-4-6-9-10-15-16z" class="D"></path><path d="M735 301c2-1 4-1 5-1 1 1 3 3 3 4l-1 1-1-2-3-1h0l1-1c-2-1-2 0-4 0z" class="B"></path><path d="M735 294c2 0 4 1 5 3 1 1 1 1 1 2h-1 0c-2 0-5 1-7 0 2 0 4 0 5-2h0l-3-3h0z" class="J"></path><path d="M728 288h1c1 0 1 1 2 1 1 2 3 3 4 5h0l3 3h0c-1 2-3 2-5 2l-1-1c-2-1-6 0-9 0h-7v-1c4 0 9-1 13-1v-2l1-1c0-1-1-3-2-5z" class="K"></path><path d="M730 293l1 1c1 1 1 2 1 2-1 1-2 1-3 0h0v-2l1-1zm8 4h-1-2-2v-1s0-1 1-2h1l3 3z" class="C"></path><defs><linearGradient id="Af" x1="762.332" y1="315.876" x2="744.874" y2="345.133" xlink:href="#B"><stop offset="0" stop-color="#828282"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#Af)" d="M741 305h1c2 2 4 5 6 8v-1-5h1 0l2 2 7 18 3 16c0 4 1 8 1 12v12c-1-1-1-2-1-3v-3c-1-1-1-7-2-9 0 1 0 1-1 1v-3 5 1l-2-1c1-5 0-12-1-17-1-3-3-6-4-9h0c-1-1-1-2-1-3h1c0-1-1-3-1-4-1-4-3-8-6-11-1-2-2-3-3-5v-1z"></path><defs><linearGradient id="Ag" x1="745.944" y1="322.405" x2="754.966" y2="323.641" xlink:href="#B"><stop offset="0" stop-color="#9c9b9a"></stop><stop offset="1" stop-color="#bbb9bb"></stop></linearGradient></defs><path fill="url(#Ag)" d="M741 305h1c2 2 4 5 6 8 2 2 2 4 3 7 2 5 4 12 4 18-1-3-3-6-4-9h0c-1-1-1-2-1-3h1c0-1-1-3-1-4-1-4-3-8-6-11-1-2-2-3-3-5v-1z"></path><defs><linearGradient id="Ah" x1="745.732" y1="334.258" x2="752.411" y2="331.792" xlink:href="#B"><stop offset="0" stop-color="#a8a7a8"></stop><stop offset="1" stop-color="#cbcacb"></stop></linearGradient></defs><path fill="url(#Ah)" d="M735 301c2 0 2-1 4 0l-1 1h0l3 1-1 2h1v1c1 2 2 3 3 5 3 3 5 7 6 11 0 1 1 3 1 4h-1c0 1 0 2 1 3h0c1 3 3 6 4 9 1 5 2 12 1 17-2 2-2 4-4 5l3-9c-2-6-5-12-10-16v-2l-6-3-4-1h0c-2-2-4-2-7-2v-1c2 0 4-1 6 0 1 1 3 2 5 3s5 2 8 3l5 3c-3-6-7-11-13-14l1-2-1-1v-2h0c-3-5-6-9-11-12 0-1 1-1 2-1v-1h3l2-1z"></path><path d="M735 301c2 0 2-1 4 0l-1 1h0l3 1-1 2h1v1c1 2 2 3 3 5 3 3 5 7 6 11 0 1 1 3 1 4h-1c-2-3-5-6-7-9-3-4-5-8-8-11-2-1-3-1-5-2v-1-1h3l2-1z" class="H"></path><path d="M733 302c1 1 2 1 3 2h-6v-1-1h3z" class="X"></path><path d="M735 301c2 0 2-1 4 0l-1 1h0l-2 2c-1-1-2-1-3-2l2-1z" class="V"></path><defs><linearGradient id="Ai" x1="707.435" y1="246.594" x2="693.617" y2="262.217" xlink:href="#B"><stop offset="0" stop-color="#7a7a7a"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#Ai)" d="M668 213h0l1-1 1 1h3c1 0 2 2 2 3l1 1 1 1 3 3h0c1 2 4 5 5 6 2 2 3 3 4 5 5 6 10 13 17 18 1 0 1 1 2 1h2l8 7c2 1 3 4 5 5l1 1 3 3c0 2 1 3 2 4h0c1 2 3 4 5 6 3 3 5 6 7 10 1 1 2 2 2 3-3-2-7-4-9-7-2-2-4-3-6-5-13-10-27-19-41-28-6-4-11-8-18-11-1-1-3-2-5-3l-8-5 1-1c1-1 1-2 2-3l4-4c2-1 5-5 6-7 1-1 1-2 0-3h-1z"></path><path d="M670 213h3c1 0 2 2 2 3l1 1 1 1 3 3h-2c-1 0-2-1-2-1-1 0-1 0-1 1l-1-1c-1-1-1-1-1-2l-1-1v-2h0-1l-1-1v-1z" class="F"></path><path d="M734 283c2 1 3 2 5 3 0-1-1-1-1-2-2-3-5-5-7-8-2-5-7-7-10-11v-1l3 3 2 1 3 3h0 0c1 2 3 4 5 6 3 3 5 6 7 10 1 1 2 2 2 3-3-2-7-4-9-7z" class="H"></path><defs><linearGradient id="Aj" x1="417.79" y1="629.126" x2="417.048" y2="651.183" xlink:href="#B"><stop offset="0" stop-color="#242323"></stop><stop offset="1" stop-color="#444"></stop></linearGradient></defs><path fill="url(#Aj)" d="M450 619c2 0 4 2 6 3 3 1 6 2 9 4 10 3 20 5 30 6h12 0 1 4l10 3v1l47 5 1 1v4c-4 1-8 1-12 1h-17-57-135-83c-1-1-1-3-1-4 2 1 9 0 11 0 25 0 49-1 74-4 1-2 2-2 4-3 3-1 7-2 10-3l6-3h4l1 1c2 2 6 2 10 2l13 1h6 21l9-1h12c3 0 5-3 8-4 1 1 1 1 1 2h0c1-1 1-1 1-2 0 0 1 0 2-1-1 0-3-2-3-2-2-1-4-4-5-7z"></path><path d="M350 639c1-2 2-2 4-3 3-1 7-2 10-3l6-3h4l1 1c2 2 6 2 10 2v2c-3-1-5-1-7-1-2-1-4-2-5-2-3 1-7 2-10 3-4 2-9 3-13 4z" class="L"></path><defs><linearGradient id="Ak" x1="418.299" y1="625.68" x2="422.286" y2="640.418" xlink:href="#B"><stop offset="0" stop-color="#ceccca"></stop><stop offset="1" stop-color="#f0f0f4"></stop></linearGradient></defs><path fill="url(#Ak)" d="M454 629c1 1 1 1 1 2-2 2-3 3-5 3-5 1-11 1-16 2-15 0-30 0-44-1h-5v-2l13 1h6 21l9-1h12c3 0 5-3 8-4z"></path><path d="M450 619c2 0 4 2 6 3 3 1 6 2 9 4 10 3 20 5 30 6h12 0 1 4l10 3v1l47 5 1 1-1 1h-8-15c-19 0-39 0-58-4-11-1-22-4-30-11-1 0-3-2-3-2-2-1-4-4-5-7z" class="h"></path><path d="M450 619c2 0 4 2 6 3 3 1 6 2 9 4 10 3 20 5 30 6h12 0 1 4l10 3v1h0c1 1 7 3 9 3 1 0 2 1 4 1-5 1-11 0-16-1-3-1-7-3-10-3-12-2-23-2-34-5l-11-3c-3 0-7-3-9-2-2-1-4-4-5-7z" class="c"></path><defs><linearGradient id="Al" x1="151.158" y1="332.46" x2="104.222" y2="265.64" xlink:href="#B"><stop offset="0" stop-color="#c2c2c3"></stop><stop offset="1" stop-color="#efeeee"></stop></linearGradient></defs><path fill="url(#Al)" d="M156 218l3-6c3-4 8-7 13-7 6-1 13 0 19 4 2 1 3 3 3 6 0 1-1 3-2 5h0c-1 1-2 3-3 4h-1c0 1-1 2-1 3l1 1c2 0 3-1 4-2 0 0 1-1 2-1v1l-3 3c-2 1-4 1-6 3l-5 2c-2 2-24 15-25 16-4 3-9 6-13 9-9 6-17 12-25 18l-12 10c0 1 0 2-1 3-1 2-4 4-5 7-3 3-5 6-7 10v1h1l4-5c1-2 3-4 4-6l1 1c1-1 2-1 4-2 4 1 7 3 11 4-2 2-4 2-6 3-5 3-7 6-10 10-1 1-2 3-2 3-3 4-6 8-9 11v1c-1 1-2 4-2 5l-1 2c-1 4-2 8-3 13v9h-1l-1 8s0 2-1 2l-1 1h0v2c-1 0-1 1-1 2h0l-1-1v1h0c-1 1-1 2-1 3h0c1 1 0 2 0 2l1 1v2 2h0v2c-1-3-1-9-3-11h0c-1-8-2-16-2-24 0-5 0-11 1-16 1-15 7-31 14-44 1-3 3-7 5-10 1-2 3-2 5-3l7-4c5-6 12-10 18-15l-1-1 12-9c5-5 9-10 12-15 0-1-1-1-1-1v-1c2-3 2-5 3-9l1-1c0 1 1 1 1 1l3-2 2-3 1 2z"></path><path d="M93 279l1 1c-1 2-2 5-2 7 0 0-1 2-2 2v-1-1-1h0c-1 1-1 2-2 3 1-3 3-7 5-10z" class="L"></path><defs><linearGradient id="Am" x1="97.974" y1="276.28" x2="94.219" y2="284.406" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#Am)" d="M93 279c1-2 3-2 5-3l7-4c-3 5-8 10-13 15 0-2 1-5 2-7l-1-1z"></path><path d="M84 321c4-13 11-25 21-34 0 1 0 2-1 3-1 2-4 4-5 7-3 3-5 6-7 10s-4 8-5 12h-1v-1c0-1 0-1 1-2h-1c0 2-1 4-2 5z" class="a"></path><path d="M170 215h0c2-1 3-2 5-3 1-1 3-1 4-2 1 0 3 0 5 1 0 1 1 2 1 3h1 1c1 1 4 2 4 3h1v2 1h0c-1 1-2 3-3 4h-1c0 1-1 2-1 3l1 1c2 0 3-1 4-2 0 0 1-1 2-1v1l-3 3c-2 1-4 1-6 3l-5 2c1-3 3-3 5-6 0 0 0-1 1-1v-1c1-1 0-1 0-1h-2-1c-1 0-3-1-4-2h-1c-1-1-2-1-3-2l-1-1s-1 0-2-1v-1c-1-1-2-2-2-3z" class="I"></path><path d="M192 217v2l-1 1c-1 1-2 2-2 3-2 0-2 0-3-1h0l3-3c1-1 1-1 3-2z" class="b"></path><path d="M156 218l3-6c3-4 8-7 13-7 6-1 13 0 19 4 2 1 3 3 3 6 0 1-1 3-2 5v-1-2h-1c0-1-3-2-4-3h-1-1c0-1-1-2-1-3-2-1-4-1-5-1-1 1-3 1-4 2-2 1-3 2-5 3h0l-2-1h0v2c-1 1-1 2-3 2l-1-1c-4 1-7 5-10 8-1 2-2 3-3 4-3 3-5 7-8 10-6 7-13 12-20 18l-1-1 12-9c5-5 9-10 12-15 0-1-1-1-1-1v-1c2-3 2-5 3-9l1-1c0 1 1 1 1 1l3-2 2-3 1 2z" class="N"></path><path d="M155 216l1 2c-2 2-4 4-6 7-1 2-2 5-4 7 0-1-1-1-1-1v-1c2-3 2-5 3-9l1-1c0 1 1 1 1 1l3-2 2-3z" class="b"></path><path d="M106 296c4 1 7 3 11 4-2 2-4 2-6 3-5 3-7 6-10 10-1 1-2 3-2 3-3 4-6 8-9 11v1c-1 1-2 4-2 5l-1 2c-1 4-2 8-3 13v9h-1l-1 8s0 2-1 2l-1 1h0v2c-1 0-1 1-1 2h0l-1-1v1h0c0-3-1-7-1-10 0-11 1-22 4-33 0-2 2-6 3-8 1-1 2-3 2-5h1c-1 1-1 1-1 2v1h1c1-4 3-8 5-12v1h1l4-5c1-2 3-4 4-6l1 1c1-1 2-1 4-2z" class="f"></path><path d="M84 321c1-1 2-3 2-5h1c-1 1-1 1-1 2v1h1l-3 7-2 2-1 4v1 1-5c0-2 2-6 3-8z" class="T"></path><path d="M92 307v1h1c-1 4-3 8-4 11l-4 16-3 9v9c-1 4-1 9-2 13-2-10-1-22 2-32 0-3 1-5 2-8l3-7c1-4 3-8 5-12zm14-11c4 1 7 3 11 4-2 2-4 2-6 3-5 3-7 6-10 10-1 1-2 3-2 3-3 4-6 8-9 11v1c-1 1-2 4-2 5l-1 2v-1c1-7 3-16 7-23 1-3 3-5 5-8 0-2 2-3 3-5 1-1 2-1 4-2z" class="N"></path><path d="M105 299c1 0 2 1 3 1v1l-2 2v1c-1 0-2 0-2-1 1-1 1-1 1-4z" class="T"></path><path d="M167 310c1 2-1 5 0 7v-2l1 1c0 1 0 2-1 3v2h2c0 1 0 2-1 3-1 7-1 17 0 24l1 14c1 4 0 9 1 12 0 4 1 7 1 11 0 0 0 1 1 1l1 5c1 2 2 3 2 5 0 1 1 2 1 3l1 1 1 2 1 2 1 1h1c1 4 3 10 6 14l7 11c3 6 6 12 11 17l9 12c2 1 3 3 5 4l6 6c-1 0-2 1-3 0l-2-2 6 7s-1 0-1 1c-2-1-5-4-6-5l-1-1h-2v2h0l-1-1h-3c0-1-2-1-2-2h-1l-6-6-5-3h0-1l-2-4h-2 0-4-4-4c-4 2-7 4-11 7l-1 2c-1 1-4 6-5 7v-2h0v-3l1-8c-1-3-1-6-2-8l-3-6-1 1h0c-2-4-4-7-6-10l-4-4c0-1 1-6 1-7s-1-2-1-2v-1h-2l-2 2v1h-2c-2 0-4 1-5 2l-4-1-13 6h-3l-1-1c3-2 6-4 9-7 4-4 7-9 10-14l10-12c2-4 4-7 7-10l2-2 3-2 5-1h1c0-2 0-3-1-5-1-3-1-3-4-4 0-3-1-5-1-7l3-12 6-44z" class="N"></path><path d="M170 402c0-1 0 0 1-1 2 4 4 9 7 12l7 11c0 1 1 3 2 4-1 0-1 1-1 1-4-4-6-9-8-13l-8-14z" class="B"></path><path d="M163 382h1l-1 1h-1v1 1l4 5 2 6c1 1 3 5 3 5-1 1-1 0-1 1-2-2-3-6-4-8-2-3-4-5-6-7-1 0-1-1-2-2h-3l3-2 5-1z" class="V"></path><path d="M134 425c2-1 5-1 7-2 3-2 5-4 7-6 1-1 1-1 2-1 1-1 2-1 2-2l-1-1 8-2h0l-5 2c-2 3-7 6-9 8h0c1 0 2-1 3-1h3 2l2-1c2 1 4 1 6 1h1l-1 1v1c-2 1-4 3-6 3h-1c1-2 4-3 6-4-3 0-6 0-8 2-1 0-1 2-1 3 0-1-1-1-1-2s-1-2-1-2v-1h-2l-2 2v1h-2c-2 0-4 1-5 2l-4-1z" class="S"></path><path d="M161 417v-1h2l4-1c7 2 12 7 15 14-2-2-4-5-7-6l-2-1h0c-4-2-8-2-12 0v-1l1-1h-1c-2 0-4 0-6-1h0 4l2-2z" class="M"></path><path d="M161 417c3 0 5 0 8 1-3 0-4 1-7 2h0-1c-2 0-4 0-6-1h0 4l2-2z" class="a"></path><path d="M162 420h0c3-1 4-2 7-2 1 1 3 2 4 3v1c-4-2-8-2-12 0v-1l1-1z" class="B"></path><defs><linearGradient id="An" x1="156.684" y1="430.584" x2="154.092" y2="437.898" xlink:href="#B"><stop offset="0" stop-color="#5c5c5c"></stop><stop offset="1" stop-color="#757374"></stop></linearGradient></defs><path fill="url(#An)" d="M150 424c0 1 1 1 1 2 1 2 3 3 4 5 4 5 7 10 9 17-1 1 0 3 0 4s0 0 1 1v5c-1-3-1-6-2-8l-3-6-1 1h0c-2-4-4-7-6-10l-4-4c0-1 1-6 1-7z"></path><path d="M186 429s0-1 1-1c5 9 11 17 17 24 2 2 5 7 8 7l6 6c0 1 1 2 2 2l6 7s-1 0-1 1c-2-1-5-4-6-5l-1-1c-13-11-24-26-32-40z" class="F"></path><path d="M165 428h-2c-1 0-2 0-3 1l-1-1c2-2 5-3 8-3 7 0 12 6 17 10l4 4c3 3 5 6 7 9 4 7 9 12 15 17 2 2 4 3 6 4v2h0l-1-1h-3c0-1-2-1-2-2h-1l-6-6-5-3h0-1l-2-4h1 1c-1-3-3-6-5-8-2-3-5-5-7-8h1l-3-3-13-8h-5z" class="T"></path><path d="M196 455h1c1 1 1 1 1 3h0c-1 0-1 0-1 1l-2-4h1z" class="f"></path><path d="M165 428h5l13 8 3 3h-1c2 3 5 5 7 8 2 2 4 5 5 8h-1-1-2 0-4-4-4c-4 2-7 4-11 7l-1 2c-1 1-4 6-5 7v-2h0v-3l1-8v-5c-1-1-1 0-1-1s-1-3 0-4h0c1 1 1 3 2 3v-1c0-3-1-6-3-8-1-1-1-2-2-3-1-3-2-4-3-6-1-1-1-1-1-2l1 1c2 1 3 3 4 5h0c-1-2-1-3-1-4 1-2 1-3 3-4l1-1h0z" class="T"></path><path d="M180 440c3 1 6 3 8 5 1 0 2 2 4 2 2 2 4 5 5 8h-1l-6-6c-3-3-6-6-10-9z" class="S"></path><path d="M171 438c4-2 6-2 10-1 1 1 3 2 4 2 2 3 5 5 7 8-2 0-3-2-4-2-2-2-5-4-8-5-2-1-5-1-8-1l-1-1z" class="d"></path><path d="M164 448c1 2 2 4 2 6v7h1c1-2 1-6 1-8 1-3-1-9 2-11 1-1 1-1 2-1 3 0 6-1 9 1l5 5 7 7v1h-4l1-1c0-3-5-7-8-9l-1-1h-1c-2-2-3-2-5-1-1 0-2 1-3 2-1 2 0 5 0 7 0 0 1 1 0 2v1l-4 6c-2 2-3 5-4 8h0v-3l1-8v-5c-1-1-1 0-1-1s-1-3 0-4h0z" class="O"></path><defs><linearGradient id="Ao" x1="161.39" y1="434.052" x2="183.086" y2="435.932" xlink:href="#B"><stop offset="0" stop-color="#494849"></stop><stop offset="1" stop-color="#6a696a"></stop></linearGradient></defs><path fill="url(#Ao)" d="M165 428h5l13 8 3 3h-1c-1 0-3-1-4-2-4-1-6-1-10 1-2 1-3 3-5 5h-1l1-2-1 1c-1-2-2-4-3-5h0c-1-2-1-3-1-4 1-2 1-3 3-4l1-1h0z"></path><path d="M164 469c1-3 2-6 4-8l4-6v-1c1-1 0-2 0-2 0-2-1-5 0-7 1-1 2-2 3-2 2-1 3-1 5 1h1l1 1c3 2 8 6 8 9l-1 1h-4-4c-4 2-7 4-11 7l-1 2c-1 1-4 6-5 7v-2z" class="E"></path><path d="M170 462v-2c1-1 2-2 2-3 1-2 1-3 3-5 2-1 7-3 9-2 2 0 2 1 3 3 0 1-5 2-6 2-4 2-7 4-11 7z" class="C"></path><defs><linearGradient id="Ap" x1="167.491" y1="399.14" x2="209.627" y2="391.447" xlink:href="#B"><stop offset="0" stop-color="#1c1c1c"></stop><stop offset="1" stop-color="#3f3e3f"></stop></linearGradient></defs><path fill="url(#Ap)" d="M167 310c1 2-1 5 0 7v-2l1 1c0 1 0 2-1 3v2h2c0 1 0 2-1 3-1 7-1 17 0 24l1 14c1 4 0 9 1 12 0 4 1 7 1 11 0 0 0 1 1 1l1 5c1 2 2 3 2 5 0 1 1 2 1 3l1 1 1 2 1 2 1 1h1c1 4 3 10 6 14l7 11c3 6 6 12 11 17l9 12c2 1 3 3 5 4l6 6c-1 0-2 1-3 0l-2-2c-1 0-2-1-2-2l-6-6c-3 0-6-5-8-7-6-7-12-15-17-24-1-1-2-3-2-4l-7-11c-3-3-5-8-7-12 0 0-2-4-3-5l-2-6-4-5v-1-1h1l1-1c0-2 0-3-1-5-1-3-1-3-4-4 0-3-1-5-1-7l3-12 6-44z"></path><path d="M167 363v-11c0-1-1-4 0-5h0v-1l1 2 1 14-1 1v-4c-1 1 0 2-1 4z" class="S"></path><path d="M162 369c1 0 1 1 1 2l1 1c1 2 0 3 0 5 1 2 0 3 1 5 0 1 0 3 1 4l1 3-1 1-4-5v-1-1h1l1-1c0-2 0-3-1-5 0-3-1-6-1-8z" class="d"></path><path d="M158 366l3-12 1 1c-1 5-1 9 0 14 0 2 1 5 1 8-1-3-1-3-4-4 0-3-1-5-1-7z" class="B"></path><path d="M166 386h0v-1-1c0-1 0-1-1-1l1-1h0c2 7 5 14 8 20 2 3 4 7 4 11-3-3-5-8-7-12 0 0-2-4-3-5l-2-6 1-1-1-3z" class="M"></path><path d="M167 389c1 2 2 4 2 6l-1 1-2-6 1-1z" class="P"></path><path d="M167 363c1-2 0-3 1-4v4l1-1c1 4 0 9 1 12 0 4 1 7 1 11 0 0 0 1 1 1l1 5c1 2 2 3 2 5 0 1 1 2 1 3l1 1 1 2 1 2c0 1 0 1-1 1 0 0 0-1-1-1l-2-6-3-6c0-2-1-4-2-5-1-3-1-5-1-8-1-5-2-11-2-16z" class="d"></path><path d="M185 424c1 1 2 1 2 2 2 2 3 4 4 6s3 4 4 6c2 2 3 4 4 6l5 5c1 2 3 4 4 6 1 1 3 2 4 4h0c-3 0-6-5-8-7-6-7-12-15-17-24-1-1-2-3-2-4z" class="T"></path><path d="M553 179l6 2 6-1-1 1c2 2 5 2 7 3l15 7c4 2 8 4 12 7l12 8c1 1 3 2 4 2l10 6v-1-1c-1 0-3-1-3-2-2-2-7-5-7-8 2 1 3 2 5 3l1 1 7 4c2 2 5 5 7 5 2 1 5 2 6 4l1 1c1 0 3 1 4 2 0 0 2 3 3 3l1 2-2 2c0 2 0 2 1 3l2-1h0l2-2 1 1c0 2 1 2-1 3l-3 1c-2 0-4 2-6 4v1c-2-1-5-4-6-6-1 0-2-1-3-1h-1l-3-3c-2-2-3-4-5-5 0 1 1 2 2 3-1 0-3-1-4-1l-6-3v1c-2-1-4-3-7-3-5-3-12-3-17-2l-1 1c-3 1-5 2-7 2-5 3-10 10-12 16l-3 9c-1 1-2 3-2 5l-1-1c0 2-1 3-1 4-1 0-1 0-1 1s0 1-1 2v1-3h0c-1 2-1 4-1 5l-3 9c-1 5-2 9-3 13-1 2-1 3-2 4h-1c-2 7-4 14-8 20v-1c-1-1 0-2 0-3v-2-1h0c0-5 1-11 3-16v-1c0-2 0-3-1-5 2-3 2-7 2-10v-19c0-3-1-7-2-10-1-4-3-9-6-12-2-2-4-3-7-4h-2c-1 0-1-1-2-2l5-1h9v1h2c1 0 1 0 2-1 1 0 1-1 1-2v-3h-4c1 0 3 0 4-1l-1-1h1l1 1h1l-1-2-1-1c0-1-2-7-2-9-1-2-4-4-5-6h1c1 1 2 2 3 2s2 0 2 1l1-1c0-2-1-4-2-5 0-3-2-4-3-6 0-1 0-1 1-1 3 0 4 1 6 2 0-2-1-3-2-4l1-4s1 0 2-1z" class="N"></path><path d="M571 224c3-4 6-7 10-11 1 0 2-1 4-2-5 5-10 9-13 14l-1 1v-2z" class="P"></path><path d="M610 217c1 1 3 1 5 3h2c1 1 2 2 2 3 1 0 1 0 2 1 0 0 1 1 2 1s2 0 3 1h0l1 1c-1 0-3-1-4-1l-6-3c0-1-2-1-3-2 0 0-1 0-1-1h-1l-2-2v-1h0z" class="S"></path><defs><linearGradient id="Aq" x1="587.523" y1="216.139" x2="590.965" y2="242.36" xlink:href="#B"><stop offset="0" stop-color="#2f2e2e"></stop><stop offset="1" stop-color="#4a4a4b"></stop></linearGradient></defs><path fill="url(#Aq)" d="M567 251c1-1 2-4 2-6 1-4 1-7 3-10 1-3 2-6 4-9 1-2 3-5 5-6l-2 2h1l1-1c2-1 3-2 4-3 6-3 13-5 20-3l5 2h0v1l2 2h1c0 1 1 1 1 1 1 1 3 1 3 2v1c-2-1-4-3-7-3-5-3-12-3-17-2l-1 1c-3 1-5 2-7 2-5 3-10 10-12 16l-3 9c-1 1-2 3-2 5l-1-1z"></path><defs><linearGradient id="Ar" x1="581.348" y1="234.457" x2="543.981" y2="270.162" xlink:href="#B"><stop offset="0" stop-color="#191b1b"></stop><stop offset="1" stop-color="#4e4b4c"></stop></linearGradient></defs><path fill="url(#Ar)" d="M564 222v1c1 1 1 2 1 3 1-1 1-2 1-3 1 1 1 1 1 2h0c1 0 1-1 1-1v-1c0-1 0-2 1-3 1-2 3-4 4-5l1 1c-2 2-3 3-4 5v2h0l1 1h0v2c0 2-2 3-3 5 0 1-1 2-1 3-1 4-1 7-2 11l-1 11c-1 2-1 4-1 5l-3 9c-1 5-2 9-3 13-1 2-1 3-2 4h-1l7-46 1-2c1-3 2-6 2-9v-8z"></path><defs><linearGradient id="As" x1="578.511" y1="187.881" x2="571.152" y2="210.343" xlink:href="#B"><stop offset="0" stop-color="#2d2d2d"></stop><stop offset="1" stop-color="#676667"></stop></linearGradient></defs><path fill="url(#As)" d="M553 179l6 2 6-1-1 1c2 2 5 2 7 3l15 7c4 2 8 4 12 7l-1 1v1l13 8-5-1c-6-4-12-6-19-9-3-1-7-3-11-4-2 0-4 0-6 1-4 1-5 4-7 7s-2 6-2 10l-2-9-4-9-2-6c0-2-1-3-2-4l1-4s1 0 2-1z"></path><path d="M559 181l6-1-1 1c2 2 5 2 7 3l15 7c4 2 8 4 12 7l-1 1v1c-3-2-5-4-8-5l-19-10-11-4z" class="B"></path><path d="M597 200v-1l1-1 12 8c1 1 3 2 4 2l10 6v-1-1c-1 0-3-1-3-2-2-2-7-5-7-8 2 1 3 2 5 3l1 1 7 4c2 2 5 5 7 5 2 1 5 2 6 4l1 1c1 0 3 1 4 2 0 0 2 3 3 3l1 2-2 2c0 2 0 2 1 3l2-1h0l2-2 1 1c0 2 1 2-1 3l-3 1c-2 0-4 2-6 4v1c-2-1-5-4-6-6-1 0-2-1-3-1h-1l-3-3c-2-2-3-4-5-5 0-1-1-1-2-2-2-1-4-3-6-5-1 0-3-1-4-2-2-1-4-2-7-3l-3-1s1 0 1-1h1l6 2c-1-2-4-3-6-5l5 1-13-8z" class="H"></path><path d="M640 219l1 1h0c-2 1-3 3-4 4s-2 1-3 1h-1c-1-2-1-3-1-4 1 1 1 2 3 2 1 0 2-1 3-2 0-1 1-1 2-2h0z" class="D"></path><path d="M617 217h1l6 4c2 1 4 2 6 4 0 1 0 1 1 1l3 3c1 2 2 3 3 4-1 0-2-1-3-1h-1l-3-3c-2-2-3-4-5-5 0-1-1-1-2-2-2-1-4-3-6-5z" class="J"></path><path d="M637 228c3-1 5-1 8 0l1 1 1 2c-2 1-3 2-5 3 0 1 0 1-1 2-1-1-1-1-2-3-1-1-2-3-4-4l1-1h1z" class="V"></path><path d="M637 228c3-1 5-1 8 0v1c-1 1-2 1-3 1-2 0-4 0-5-1v-1z" class="P"></path><path d="M645 222s2 3 3 3l1 2-2 2v2l-1-2-1-1c-3-1-5-1-8 0h-1l-1 1v-2c1-1 2-2 4-3h1c1-2 2-2 5-2z" class="B"></path><path d="M636 228c2-2 6-3 9-4l1 5-1-1c-3-1-5-1-8 0h-1z" class="W"></path><path d="M614 202c2 1 3 2 5 3l1 1 7 4c2 2 5 5 7 5 2 1 5 2 6 4h0c-1 1-2 1-2 2-1 1-2 2-3 2-2 0-2-1-3-2v-2c-1-2-6-6-8-7-1 0-3-1-3-2-2-2-7-5-7-8z" class="E"></path><defs><linearGradient id="At" x1="613.603" y1="218.16" x2="625.368" y2="211.936" xlink:href="#B"><stop offset="0" stop-color="#313132"></stop><stop offset="1" stop-color="#555353"></stop></linearGradient></defs><path fill="url(#At)" d="M605 207l5 1c2 1 6 3 8 5 4 2 9 6 14 8v2h-1c-3 0-5-1-7-2l-6-4h-1c-1 0-3-1-4-2-2-1-4-2-7-3l-3-1s1 0 1-1h1l6 2c-1-2-4-3-6-5z"></path><defs><linearGradient id="Au" x1="546.531" y1="257.238" x2="558.673" y2="259.131" xlink:href="#B"><stop offset="0" stop-color="#b9b8b9"></stop><stop offset="1" stop-color="#dbdada"></stop></linearGradient></defs><path fill="url(#Au)" d="M546 186c3 0 4 1 6 2l2 6 4 9 2 9c0-4 0-7 2-10 1 6 2 14 2 20v8c0 3-1 6-2 9l-1 2-7 46c-2 7-4 14-8 20v-1c-1-1 0-2 0-3v-2-1h0c0-5 1-11 3-16v-1c0-2 0-3-1-5 2-3 2-7 2-10v-19c0-3-1-7-2-10-1-4-3-9-6-12-2-2-4-3-7-4h-2c-1 0-1-1-2-2l5-1h9v1h2c1 0 1 0 2-1 1 0 1-1 1-2v-3h-4c1 0 3 0 4-1l-1-1h1l1 1h1l-1-2-1-1c0-1-2-7-2-9-1-2-4-4-5-6h1c1 1 2 2 3 2s2 0 2 1l1-1c0-2-1-4-2-5 0-3-2-4-3-6 0-1 0-1 1-1z"></path><path d="M558 218c1 1 1 1 2 1 1 4 1 7 1 10h0c-1-4-2-7-3-11z" class="C"></path><path d="M548 202h1c1 3 3 5 4 7v1c1 2 1 5 2 8h-1c0-2-1-4-2-6h-1l-1-1c0-1-2-7-2-9z" class="Z"></path><path d="M551 212h1c1 2 2 4 2 6h1c1 4 2 11 1 15-2-6-2-13-4-19l-1-2z" class="J"></path><path d="M554 256h0c0-1 1-2 1-3 0 1 0 3 1 4-1 11-4 21-6 31-1 4-3 8-4 12h0c0-5 1-11 3-16 4-8 5-19 5-28z" class="W"></path><path d="M562 202c1 6 2 14 2 20v8c0 3-1 6-2 9l-1 2v-12c0-3 0-6-1-10v-7c0-4 0-7 2-10z" class="M"></path><defs><linearGradient id="Av" x1="548.881" y1="246.913" x2="560.518" y2="236.427" xlink:href="#B"><stop offset="0" stop-color="#4d4e4f"></stop><stop offset="1" stop-color="#696766"></stop></linearGradient></defs><path fill="url(#Av)" d="M549 213h1l1 1h1c2 6 2 13 4 19 1 5 1 11 0 16v8c-1-1-1-3-1-4 0 1-1 2-1 3h0c0-5 1-11-1-16 0-2-1-5-2-7l1-1c0-1-1-3-1-5h1l-2-9v-3h-4c1 0 3 0 4-1l-1-1z"></path><path d="M549 213h1l1 1v3c1 0 1 1 1 2l1 5c0 2 1 6 0 8h-1c0-1-1-3-1-5h1l-2-9v-3h-4c1 0 3 0 4-1l-1-1z" class="F"></path><defs><linearGradient id="Aw" x1="553.652" y1="194.091" x2="552.574" y2="209.948" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#a09f9f"></stop></linearGradient></defs><path fill="url(#Aw)" d="M546 186c3 0 4 1 6 2l2 6 4 9 2 9v7c-1 0-1 0-2-1v-4h-1c-2-2-3-3-3-5-1-2-3-7-4-7h-1-1c-1-2-4-4-5-6h1c1 1 2 2 3 2s2 0 2 1l1-1c0-2-1-4-2-5 0-3-2-4-3-6 0-1 0-1 1-1z"></path><defs><linearGradient id="Ax" x1="558.931" y1="203.995" x2="555.824" y2="213.417" xlink:href="#B"><stop offset="0" stop-color="#929292"></stop><stop offset="1" stop-color="#afadae"></stop></linearGradient></defs><path fill="url(#Ax)" d="M554 209c1 1 1 2 2 3h1l-1-5c0-1 0-4 1-4h1l2 9v7c-1 0-1 0-2-1v-4h-1c-2-2-3-3-3-5z"></path><defs><linearGradient id="Ay" x1="549.917" y1="187.169" x2="547.849" y2="194.549" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#6e6e6e"></stop></linearGradient></defs><path fill="url(#Ay)" d="M546 186c3 0 4 1 6 2l2 6h-2c-1 2-1 3-1 5h-1-1l1-1c0-2-1-4-2-5 0-3-2-4-3-6 0-1 0-1 1-1z"></path><path d="M550 218l2 9h-1c0 2 1 4 1 5l-1 1c1 2 2 5 2 7 2 5 1 11 1 16 0 9-1 20-5 28v-1c0-2 0-3-1-5 2-3 2-7 2-10v-19c0-3-1-7-2-10-1-4-3-9-6-12-2-2-4-3-7-4h-2c-1 0-1-1-2-2l5-1h9v1h2c1 0 1 0 2-1 1 0 1-1 1-2z" class="L"></path><path d="M546 224c1 0 3 0 4 1 0 1 0 2 1 2 0 2 1 4 1 5l-1 1c-2-3-3-6-5-9z" class="E"></path><path d="M550 218l2 9h-1c-1 0-1-1-1-2-1-1-3-1-4-1-1-1-3-3-4-3-1-1-5-1-6-1h9v1h2c1 0 1 0 2-1 1 0 1-1 1-2z" class="B"></path><defs><linearGradient id="Az" x1="229.082" y1="207.927" x2="242.364" y2="240.096" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#323232"></stop></linearGradient></defs><path fill="url(#Az)" d="M245 200c4-3 9-6 14-8l1 1c2 0 4 0 5-1h3v1h3 1l1 1c3 2 5 3 6 7s1 12 0 16v1h1c0-2 0-4 1-7l1 1v3h0c-1 5-2 9-2 15-1 1-1 4 0 5v3c-1 1-1 1-1 2l2 19 1 10c2 11 5 22 9 32l-1 1c-2-3-4-6-5-9l-1-2-1-1-3-4-3-8-3-5-1-4c0-1-1-2-1-2-2-2-3-4-4-6-1 0-2 1-3 1s-3 1-4 2l-6 3h0l-8 3c-3 0-6 0-10-1-3 0-5-1-7-3h-3c-1 0-1 0-2 1-2 0-3-1-4-2l-1-1c-1 1-1 1-1 2s-1 2-2 2c0-1 1-3 0-4h0c1-3 2-5 4-7 0-1 1-1 2-3l1-1h0v-1c0-1-1-2-1-2h1 2c0-1 1-1 2-2h0l2-1h0 2c2-1 4-1 5-1 3-1 7-1 11-1l-7-4 1-1 6 2c-2-2-6-3-9-4-1 0-3-1-5-2h-6 0-7 0c-1 0-1 1-2 1-2 0-4 1-5 1-1 1-1 1-2 1s-2 1-3 2c-1 0-1 0-2 1h-1c-1 1-2 2-3 2l-1 1h-1l-1 1c-1 1-3 2-4 3h-1c-1-1-1 0-2 0h-1-2l1-3c0-1 1-1 1-2l2-2s1-1 1-2 1-1 1-2c-1-2-2-3-4-3l-1-1h0 1 1-1l-7-2c2-2 4-2 6-3l3-3h1c2-2 4-3 6-4l3-1c2-1 5-2 7-4l4-1c0-1 3-1 3-2l8-3 2-2c1 0 2-1 3-1l9-5 5-3z"></path><path d="M245 200h4c-3 2-6 3-9 3l5-3z" class="d"></path><path d="M253 215c-1 0-3-2-4-2h4c2 0 3 0 5 1-1 0-1 0-2 1h-2-1z" class="N"></path><path d="M185 232c2-2 4-2 6-3l-1 1c0 1 1 1 1 2h1 0l-2 1h0 1l1 1-7-2zm29-11l3-3 1 1h0l-9 9v-2l5-5z" class="S"></path><path d="M218 214l8-3c-1 3-10 6-13 7-3 0-6 2-7 4-3 3-7 3-11 4 2-2 4-3 6-4l3-1c2-1 5-2 7-4l4-1c0-1 3-1 3-2z" class="N"></path><path d="M277 201c1 4 0 9 0 12v4l-1 2h-1l-1-1c-1 1-1 2-2 3h0l-4-3h-1c-1-2-3-3-4-5 2 0 3 3 5 3h1v1c1 0 1-1 1-2 1-2 2-2 3-4v-1c1 0 1 1 2 2h0v-2l1 1v3h0v-2h1v-11z" class="T"></path><defs><linearGradient id="BA" x1="199.44" y1="230.219" x2="203.744" y2="241.116" xlink:href="#B"><stop offset="0" stop-color="#282727"></stop><stop offset="1" stop-color="#454646"></stop></linearGradient></defs><path fill="url(#BA)" d="M194 244c2-4 4-6 6-9 2-2 4-5 6-7 1-1 1-2 3-3 0 0 0-1 1-1 1-2 2-2 4-3l-5 5v2c-1 2-3 4-4 6l-5 6-5 6-1-2z"></path><path d="M245 200c4-3 9-6 14-8l1 1c2 0 4 0 5-1h3v1h3 1l1 1-1 1c-7-2-16 2-23 5h-4z" class="S"></path><defs><linearGradient id="BB" x1="209.365" y1="234.968" x2="214.108" y2="242.399" xlink:href="#B"><stop offset="0" stop-color="#201f20"></stop><stop offset="1" stop-color="#3a393a"></stop></linearGradient></defs><path fill="url(#BB)" d="M210 235s0 1 1 1c6-4 17-4 25-4-2-1-5-1-7-2h1c1 1 2 0 4 0h0c1 1 2 1 3 1h0l2 1v1c-2 0-3 0-4 1h-6c-2 1-3 1-5 1 2 0 3 0 4 1h0-7 0c-1 0-1 1-2 1-2 0-4 1-5 1-1 1-1 1-2 1s-2 1-3 2c-1 0-1 0-2 1h-1c-1 1-2 2-3 2l-1 1h-1l-1 1c-1 1-3 2-4 3h-1c-1-1-1 0-2 0h-1-2l1-3 3-2 1 2 5-6 1 1s2-2 2-3c2-1 3-2 5-4 1-1 3-3 5-4-2 3-5 4-6 7h0 1l2-2z"></path><path d="M194 244l1 2-2 3h-1-2l1-3 3-2z" class="R"></path><defs><linearGradient id="BC" x1="238.241" y1="216.141" x2="242.041" y2="227.352" xlink:href="#B"><stop offset="0" stop-color="#262627"></stop><stop offset="1" stop-color="#515051"></stop></linearGradient></defs><path fill="url(#BC)" d="M253 215h1 2c1-1 1-1 2-1 2 1 4 3 6 4l2 3c2 2 3 6 3 8 1 1 3 6 3 7-1 0-1 0-1-1-1-2-1-3-3-4h0v-1c-1-2-3-6-5-7l-1 1c1 2 4 5 4 7-1 0-2-1-3-2v1c-1 0-1-1-1-1-2-3-4-5-7-6l-1-1c-1-1-3-1-5-2-8-2-16-1-24 3l-2 1c-1 0-2 1-2 1v-1h0c5-4 10-6 15-7 1-1 3-1 5-1 3 0 6 0 8 1s4 2 7 3v-2c0-1-2-2-3-3z"></path><path d="M262 224c-2-2-3-4-4-5 0-1 0-2-1-2l6 3c1 2 3 4 4 7 0 1 2 2 2 2 1 1 3 6 3 7-1 0-1 0-1-1-1-2-1-3-3-4h0v-1c-1-2-3-6-5-7l-1 1z" class="F"></path><defs><linearGradient id="BD" x1="224.315" y1="220.446" x2="263.574" y2="257.741" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#4e4e4e"></stop></linearGradient></defs><path fill="url(#BD)" d="M223 224l2-1c8-4 16-5 24-3 2 1 4 1 5 2l1 1c3 1 5 3 7 6 0 0 0 1 1 1 1 3 3 6 4 9v1h0v4c0 3 0 7-1 11v1c-1 3-3 5-5 8l-6 3h0l-8 3c-3 0-6 0-10-1-3 0-5-1-7-3v-1h1c2 2 3 3 6 3h0 2v-2-1h3c3 0 6-1 8-2 4-1 6-4 8-7 1-2 2-3 2-5 1-3 0-6-1-9h0c0-2-1-3-2-5v-1h0 3l-2-1v-1l-1-1c-2-2-4-4-7-5l2 2h-1l2 3c-1-1-1-1-2-1-1-1-2-2-3-2h0c-1-1-1-1-2-1h-4 0 0c-2 1-4 1-5 2h0c-1 0-2 0-3-1h0c-2 0-3 1-4 0h-1c2 1 5 1 7 2-8 0-19 0-25 4-1 0-1-1-1-1 3-3 8-6 11-10 0 0 1-1 2-1z"></path><path d="M234 230c2-1 3-1 4-1h1 3c-2 1-4 1-5 2h0c-1 0-2 0-3-1z" class="W"></path><path d="M223 224l2-1c8-4 16-5 24-3v1l-2-1c-2 0-3 0-4-1-2 0-5 1-8 1h0c-1 1-3 1-5 2-2 0-4 2-7 2z" class="g"></path><path d="M250 228c-2 0-3 0-4-1s-2-1-3-1h-6-2c5-1 9-1 14 0 2 0 4 0 5 1 2 1 4 5 5 6 0 1 0 1-1 1l-1-1c-2-2-4-4-7-5z" class="M"></path><defs><linearGradient id="BE" x1="252.874" y1="236.183" x2="253.418" y2="266.873" xlink:href="#B"><stop offset="0" stop-color="#9b9a9b"></stop><stop offset="1" stop-color="#d9d8d9"></stop></linearGradient></defs><path fill="url(#BE)" d="M259 242h0c0-2-1-3-2-5v-1h0 3c3 6 6 12 4 18-1 5-4 9-8 11l-2 1 1 1h0l-8 3c-3 0-6 0-10-1-3 0-5-1-7-3v-1h1c2 2 3 3 6 3h0 2v-2-1h3c3 0 6-1 8-2 4-1 6-4 8-7 1-2 2-3 2-5 1-3 0-6-1-9z"></path><path d="M230 265h1c2 2 3 3 6 3h0 2 1c5 0 9 0 14-2l1 1h0l-8 3c-3 0-6 0-10-1-3 0-5-1-7-3v-1z" class="G"></path><path d="M273 194c3 2 5 3 6 7s1 12 0 16v1h1c0-2 0-4 1-7l1 1v3h0c-1 5-2 9-2 15-1 1-1 4 0 5v3c-1 1-1 1-1 2l2 19 1 10c2 11 5 22 9 32l-1 1c-2-3-4-6-5-9l-1-2-1-1-3-4-3-8-3-5-1-4c0-1-1-2-1-2-2-2-3-4-4-6-1 0-2 1-3 1s-3 1-4 2c2-3 4-5 5-8v-1c1-4 1-8 1-11v-4h0v-1c-1-3-3-6-4-9v-1c1 1 2 2 3 2 0-2-3-5-4-7l1-1c2 1 4 5 5 7v1h0c2 1 2 2 3 4 0 1 0 1 1 1 0-1-2-6-3-7 0-2-1-6-3-8l-2-3c4 2 7 8 9 12 0-5-2-8-6-12h1l4 3h0c1-1 1-2 2-3l1 1h1l1-2v-4c0-3 1-8 0-12-1-2-2-4-5-6l1-1z" class="J"></path><path d="M262 224l1-1c2 1 4 5 5 7v1h0l-1 1h0c1 1 1 1 1 2l3 8h-1c-1-4-2-8-4-11 0-2-3-5-4-7zm16 50c2 5 6 11 6 17l-1-1-3-4-3-8 1-1-1-2 1-1z" class="E"></path><path d="M281 211l1 1v3h0c-1 5-2 9-2 15-1 1-1 4 0 5v3c-1 1-1 1-1 2-1-7 0-15 0-22h1c0-2 0-4 1-7z" class="V"></path><path d="M263 230v-1c1 1 2 2 3 2 2 3 3 7 4 11l4 16s1 3 1 4l-1 1c-2-8-4-16-7-24-1-3-3-6-4-9z" class="e"></path><defs><linearGradient id="BF" x1="272.541" y1="224.06" x2="276.355" y2="229.401" xlink:href="#B"><stop offset="0" stop-color="#7d7d7d"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#BF)" d="M267 218h1l4 3h0c1-1 1-2 2-3l1 1h1l1-2c0 4-1 7-2 10 0 3 1 4 1 7v5l-1-1v-1c0-2-1-5-2-7 0-5-2-8-6-12z"></path><defs><linearGradient id="BG" x1="271.641" y1="229.765" x2="273.949" y2="242.383" xlink:href="#B"><stop offset="0" stop-color="#515151"></stop><stop offset="1" stop-color="#727271"></stop></linearGradient></defs><path fill="url(#BG)" d="M264 218c4 2 7 8 9 12 1 2 2 5 2 7v4c1 4 2 9 2 12l-1 1c-1-3-1-6-1-9l-3-9c0-1-2-6-3-7 0-2-1-6-3-8l-2-3z"></path><defs><linearGradient id="BH" x1="264.759" y1="243.854" x2="275.33" y2="262.085" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#6d6d6d"></stop></linearGradient></defs><path fill="url(#BH)" d="M266 256v-1c1-4 1-8 1-11v-4h0v-1c3 8 5 16 7 24 1 4 3 7 4 11l-1 1 1 2-1 1-3-5-1-4c0-1-1-2-1-2-2-2-3-4-4-6-1 0-2 1-3 1s-3 1-4 2c2-3 4-5 5-8z"></path><path d="M266 256h1c2 1 2 2 2 4 1 1 2 3 3 4 3 3 4 8 5 11l1 2-1 1-3-5-1-4c0-1-1-2-1-2-2-2-3-4-4-6-1 0-2 1-3 1s-3 1-4 2c2-3 4-5 5-8z" class="Q"></path><path d="M250 228c3 1 5 3 7 5l1 1v1l2 1h-3 0v1c1 2 2 3 2 5h0c1 3 2 6 1 9 0 2-1 3-2 5-2 3-4 6-8 7-2 1-5 2-8 2h-3v1 2h-2 0c-3 0-4-1-6-3h-1v1h-3c-1 0-1 0-2 1-2 0-3-1-4-2l-1-1c-1 1-1 1-1 2s-1 2-2 2c0-1 1-3 0-4h0c1-3 2-5 4-7 0-1 1-1 2-3l1-1h0v-1c0-1-1-2-1-2h1 2c0-1 1-1 2-2h0l2-1h0 2c2-1 4-1 5-1 3-1 7-1 11-1l-7-4 1-1 6 2c-2-2-6-3-9-4-1 0-3-1-5-2h-6c-1-1-2-1-4-1 2 0 3 0 5-1h6c1-1 2-1 4-1v-1l-2-1c1-1 3-1 5-2h0 0 4c1 0 1 0 2 1h0c1 0 2 1 3 2 1 0 1 0 2 1l-2-3h1l-2-2z" class="Y"></path><path d="M230 265c-1 0-2-1-3-2 0-1-1-1-2-1 0-1 2-2 3-3v2c1 1 2 2 3 4h-1z" class="R"></path><path d="M243 247c1 0 2-1 3-1h3s1-1 2-1c1 1 1 3 1 4v1h-7c-2 0-4 0-5 1-2 0-3 0-4 1-2 0-3 0-4-1 3-2 7-3 11-4z" class="U"></path><path d="M243 247c1 0 2-1 3-1h3s1-1 2-1c1 1 1 3 1 4v1h-7c2-1 4-1 6-1h0l-1-1c-2-2-5-1-7-1z" class="D"></path><defs><linearGradient id="BI" x1="239.707" y1="249.118" x2="248.643" y2="254.825" xlink:href="#B"><stop offset="0" stop-color="#211e20"></stop><stop offset="1" stop-color="#464645"></stop></linearGradient></defs><path fill="url(#BI)" d="M245 250h7l1 1h0c-5 1-11 1-14 4-1 2-1 4 0 6 0 1 0 1 1 2h0 1l-1 1h1 0-4l-1 1c0 1 1 2 1 3h0c-3 0-4-1-6-3-1-2-2-3-3-4v-2-1-1c-1-1-1-2-1-3l5-3c1 1 2 1 4 1 1-1 2-1 4-1 1-1 3-1 5-1z"></path><path d="M236 252c1-1 2-1 4-1-3 2-6 4-6 7l-1 1h0l-1 1-1 3c-1-1-1-2-1-3 1-3 2-5 4-7l2-1z" class="B"></path><path d="M230 260c0 1 0 2 1 3l1-3 1-1c1 2 2 4 4 5h0l-1 1c0 1 1 2 1 3h0c-3 0-4-1-6-3-1-2-2-3-3-4l2 1h0v-2z" class="H"></path><path d="M232 251c1 1 2 1 4 1l-2 1c-2 2-3 4-4 7v2h0l-2-1v-2-1-1c-1-1-1-2-1-3l5-3z" class="K"></path><path d="M228 258c2-1 2-3 3-4s2-1 3-1c-2 2-3 4-4 7v2h0l-2-1v-2-1z" class="F"></path><path d="M256 250h1c0 1-1 1 0 2 0-1 1-3 0-5v-1-1c1 1 1 1 1 2 1 1 1 1 1 2 1 0 1 2 1 2 0 2-1 3-2 5-2 3-4 6-8 7-2 1-5 2-8 2h-3v1 2h-2c0-1-1-2-1-3l1-1h4 0-1l1-1h-1 0c-1-1-1-1-1-2-1-2-1-4 0-6 3-3 9-3 14-4h0c1 0 2-1 3-1z" class="D"></path><path d="M250 263l2-2h0l1-1c0-1 0-1 1-2 0 0 1 0 1-1 1-1 2-1 3-1-2 3-4 6-8 7z" class="E"></path><path d="M250 228c3 1 5 3 7 5l1 1v1l2 1h-3 0v1c1 2 2 3 2 5h0c1 3 2 6 1 9 0 0 0-2-1-2 0-1 0-1-1-2 0-1 0-1-1-2v1 1c1 2 0 4 0 5-1-1 0-1 0-2h-1v-2c-1 0-1-1-1-1-3-6-9-10-15-11v-1h3c-1 0-7-1-8 0h2c-1 1-2 1-2 1h4v1 1c-1 0-3-1-5-2h-6c-1-1-2-1-4-1 2 0 3 0 5-1h6c1-1 2-1 4-1v-1l-2-1c1-1 3-1 5-2h0 0 4c1 0 1 0 2 1h0c1 0 2 1 3 2 1 0 1 0 2 1l-2-3h1l-2-2z" class="H"></path><path d="M257 233l1 1v1h-2l-1-1v-1h2z" class="D"></path><path d="M256 235h2l2 1h-3 0v1c1 2 2 3 2 5h0l-6-6h0l1-1 2 1v1-2z" class="E"></path><path d="M242 229h0 0 4c1 0 1 0 2 1h0c1 0 2 1 3 2-4-1-7 0-10 0 2 2 5 3 7 5h0l-5-2c-1 0-7-1-8 0h2c-1 1-2 1-2 1h4v1 1c-1 0-3-1-5-2h-6c-1-1-2-1-4-1 2 0 3 0 5-1h6c1-1 2-1 4-1v-1l-2-1c1-1 3-1 5-2z" class="G"></path><path d="M617 223l6 3c1 0 3 1 4 1-1-1-2-2-2-3 2 1 3 3 5 5l3 3h1c1 0 2 1 3 1 1 2 4 5 6 6l4 7c6 8 10 16 14 25s8 19 11 28l1 7 1 1h1l1 1c0 3 1 6 1 9l4 30v4h0v-2h-1l2 24c1 2 1 5 1 7 0 1 0 1 1 2h-3-2l-1-1h-1-1c0-2 1-3 1-5h0c-1 1-1 3-1 4s-1 2-1 2c-1 1-2 1-2 1l-1 1-1-1v1l-2 2v-1c-1-1-1-3 0-5v-2c1-1 0-1 0-1v-1c1-1 1-2 1-4 1-1 0-4 0-6v-4-3c0-4-1-8-1-12l-3-15c-1-6-2-12-4-17l-3-3h0-1 0c-2-5-4-10-5-15l-1-3v-4c-2 0-1-2-4-2 0 1 1 1 1 2 0 3 0 7 1 10-1 0-1-1-2-1-4-5-9-10-13-15v1h-1c0 3 2 5 3 7 0 0 1 1 1 2h-1c-4-3-9-5-13-8-1 0-3-1-4-2-2-1-4-2-7-2-3-1-6-2-10-2v-1h-8-5l-6 1h0-2v-1c0-1-1-1-2-2h0v-1c1-2 4-8 6-9h0l-3-2v-1c-1 0-2 0-3-1h-1v-1h0c1 0 1 0 2 1v-1c3 2 5 3 8 5-1-2-2-3-2-5l-1-1h0c0-4-2-9-2-13 0-3 1-6 3-8 1-1 2-2 3-2 7-6 14-7 22-7 5 1 12 2 17 6-1-3-4-6-7-8 0 0-1-1-1-2-1-1-3-2-4-3v-1z" class="C"></path><path d="M674 331c1 5 1 9 1 14h0l-1-1v6h0l-1-9v-4h1v-6z" class="E"></path><path d="M673 324l1 7v6h-1l-1-14 1 1z" class="F"></path><path d="M679 368h0c1 3 0 7 2 9h1v-4c1 2 1 5 1 7 0 1 0 1 1 2h-3v-1-1h-2c-1-1-1-2-1-3 1-1 1-3 1-4-1-1 0-4 0-5z" class="a"></path><path d="M673 383c-1-3 0-4 0-7 0-1 1-3 1-4s0-1 1-2c0 1-1 3-1 4 1-1 2-1 3-1v-3 6c-1 1-1 3-1 4s-1 2-1 2c-1 1-2 1-2 1z" class="I"></path><path d="M633 232h1c1 0 2 1 3 1 1 2 4 5 6 6l4 7c-1 0-1-1-2-2l-1-1c-2 0-3-3-5-4 1 2 2 3 3 4v1l-3-3c-1-2-4-7-6-8v-1z" class="K"></path><path d="M673 306l1 1h1l1 1c0 3 1 6 1 9v4h0v2c0 2 0 3 1 4 0 2-1 3 0 5h0v9c1 0 0 1 0 1v3l-5-39z" class="T"></path><path d="M677 317l4 30v4h0v-2h-1l2 24v4h-1c-2-2-1-6-2-9h0l-1-23v-3s1-1 0-1v-9h0c-1-2 0-3 0-5-1-1-1-2-1-4v-2h0v-4z" class="V"></path><path d="M617 223l6 3c1 0 3 1 4 1-1-1-2-2-2-3 2 1 3 3 5 5l3 3v1c2 1 5 6 6 8l3 3c4 6 8 11 11 17s5 11 7 17c5 10 8 21 11 32l2 14-1-1c-2-12-6-25-10-37v1h0c0 2 3 8 3 10-2-2-3-5-4-8v-1l-1 1c-3-6-5-13-8-20-2-4-5-9-8-13-1-2-2-4-3-5l-2-3-6-6 1-1c-1-1-1-1-2-1h0c-2-1-3-2-3-3-1-3-4-6-7-8 0 0-1-1-1-2-1-1-3-2-4-3v-1z" class="O"></path><path d="M634 241c2 1 3 2 5 4 1 2 3 5 5 7 3 3 5 4 6 8-1 0-3-2-4-3l-2-3c-1-2-3-4-5-6h0l-6-6 1-1z" class="Q"></path><path d="M639 248h0c2 2 4 4 5 6l2 3c1 1 3 3 4 3 5 6 8 14 10 21 1 2 2 4 2 5v1h0c0 2 3 8 3 10-2-2-3-5-4-8v-1l-1 1c-3-6-5-13-8-20-2-4-5-9-8-13-1-2-2-4-3-5l-2-3z" class="K"></path><path d="M617 223l6 3c1 0 3 1 4 1-1-1-2-2-2-3 2 1 3 3 5 5l3 3v1c2 1 5 6 6 8-1 0-3-1-4-2 1 2 3 4 4 6-2-2-3-3-5-4-1-1-1-1-2-1h0c-2-1-3-2-3-3-1-3-4-6-7-8 0 0-1-1-1-2-1-1-3-2-4-3v-1z" class="T"></path><path d="M625 224c2 1 3 3 5 5l3 3v1h0c-2-1-4-3-6-5-2 0-4-1-4-2 1 0 3 1 4 1-1-1-2-2-2-3z" class="M"></path><path d="M622 229c5 0 9 7 13 10 1 2 3 4 4 6-2-2-3-3-5-4-1-1-1-1-2-1h0c-2-1-3-2-3-3-1-3-4-6-7-8z" class="D"></path><path d="M591 250h0c-1 1-2 1-3 0v-1c1-2 3-3 5-4s5-1 8-1h1c2-1 6 0 8 0 3 1 7 0 11 0 6 0 14 3 19 7h1c1 1 2 3 3 5 3 4 6 9 8 13 3 7 5 14 8 20 8 19 12 39 11 59 0 4 0 8-1 11 0-4-1-8-1-12l-3-15c-1-6-2-12-4-17l-3-3h0-1 0c-2-5-4-10-5-15l-1-3 2-2v-4c-1-1-1-2-1-3v-3 1l1 2v-1l-1-3-2-6c-2-4-3-9-5-12-1-2-3-3-4-5 0-1-2-2-2-3v-1c-1-1-7-6-9-6h0c-1-1-1-1-2-1h-1c-1-1-2 0-3 0v-1h-4v1c-3-2-3 0-4 1s-4 0-5 0h-2c-1-1-2-1-3-1h-3c-1-1-6-1-7 0h-2c-1 0-3 1-5 1v2h1z" class="H"></path><path d="M653 297c0-1 0-2 1-3 1 2 1 5 2 8 2 4 4 8 6 13l-3-3h0-1 0c-2-5-4-10-5-15zm-41-66c5 1 12 2 17 6 0 1 1 2 3 3h0c1 0 1 0 2 1l-1 1 6 6 2 3h-1c-5-4-13-7-19-7-4 0-8 1-11 0-2 0-6-1-8 0h-1c-3 0-6 0-8 1s-4 2-5 4v1c1 1 2 1 3 0h0 3 5c4 1 7 2 8 6 1 1 1 2 1 3l-1 2v1c-1 0-2 1-3 2-4 1-8 1-11 0-2 0-4-1-6-2l-1-1h0c0-4-2-9-2-13 0-3 1-6 3-8 1-1 2-2 3-2 7-6 14-7 22-7z" class="M"></path><path d="M601 238c7-2 13-2 20-1 4 1 8 2 11 5 0 1 1 1 1 2-1-1-3-1-5-2-2 0-5-1-7-2h-1-2 0c-1-1-2-1-3-1-3-1-7 0-10 0h-3c-1 0-1-1-1-1z" class="d"></path><defs><linearGradient id="BJ" x1="627.526" y1="243.249" x2="595.91" y2="230.143" xlink:href="#B"><stop offset="0" stop-color="#848284"></stop><stop offset="1" stop-color="#a7a6a6"></stop></linearGradient></defs><path fill="url(#BJ)" d="M612 231c5 1 12 2 17 6 0 1 1 2 3 3h0c1 0 1 0 2 1l-1 1h-1c-3-3-7-4-11-5-7-1-13-1-20 1-3 1-6 2-8 3l-3-2-2 1h-1c1-1 2-2 3-2 7-6 14-7 22-7z"></path><path d="M590 238h1c1 0 3 0 4-1 4-1 8-2 13-3 4 0 8 1 12 1l-1 1c1 0 1 0 2 1-7-1-13-1-20 1-3 1-6 2-8 3l-3-2-2 1h-1c1-1 2-2 3-2z" class="D"></path><defs><linearGradient id="BK" x1="584.454" y1="255.337" x2="606.005" y2="254.572" xlink:href="#B"><stop offset="0" stop-color="#575656"></stop><stop offset="1" stop-color="#737373"></stop></linearGradient></defs><path fill="url(#BK)" d="M587 240h1l2-1 3 2c-2 1-4 3-6 2-1 1-1 2-2 3h0v2 1h1c0 2 2 3 3 3 6 1 12-2 16 4 0 1 1 2 2 3h1l-1 2v1c-1 0-2 1-3 2-4 1-8 1-11 0-2 0-4-1-6-2l-1-1h0c0-4-2-9-2-13 0-3 1-6 3-8z"></path><path d="M591 250h-1v-2c2 0 4-1 5-1h2c1-1 6-1 7 0h3c1 0 2 0 3 1h2c1 0 4 1 5 0s1-3 4-1v-1h4v1c1 0 2-1 3 0h1c1 0 1 0 2 1h0c2 0 8 5 9 6v1c0 1 2 2 2 3 1 2 3 3 4 5 2 3 3 8 5 12l2 6 1 3v1l-1-2v-1 3c0 1 0 2 1 3v4l-2 2v-4c-2 0-1-2-4-2 0 1 1 1 1 2 0 3 0 7 1 10-1 0-1-1-2-1-4-5-9-10-13-15v1h-1c0 3 2 5 3 7 0 0 1 1 1 2h-1c-4-3-9-5-13-8-1 0-3-1-4-2-2-1-4-2-7-2-3-1-6-2-10-2v-1h-8-5l-6 1h0-2v-1c0-1-1-1-2-2h0v-1c1-2 4-8 6-9h0l-3-2v-1c-1 0-2 0-3-1h-1v-1h0c1 0 1 0 2 1v-1c3 2 5 3 8 5-1-2-2-3-2-5 2 1 4 2 6 2 3 1 7 1 11 0 1-1 2-2 3-2v-1l1-2c0-1 0-2-1-3-1-4-4-5-8-6h-5-3z" class="E"></path><path d="M594 250v-2c3 0 6 0 9 1-1 0-2 0-4 1h-5z" class="e"></path><path d="M631 280v-4l1-1c2 2 1 6 3 8v1 1h-1c0 1 0 1-1 1s-2-1-3-2v-1l1-3z" class="K"></path><path d="M631 280c1 1 1 1 1 3 0 1 0 1-2 1v-1l1-3z" class="C"></path><path d="M635 283h2c1 1 2 3 4 4h0c1 3 3 5 5 7 1 1 2 3 2 5-4-5-9-10-13-15v-1zm-49-16l3 1h0c-2 4-7 4-7 9h1l1 1c2 1 4 1 6 1l-6 1h0-2v-1c0-1-1-1-2-2h0v-1c1-2 4-8 6-9z" class="F"></path><path d="M603 249c3 1 5 2 7 4h0v-1c1-1 2 0 3-1l1 1c-1 1-2 3-2 4h0l-1 2c0-2 0-2-1-4h-1v2c1 1 0 2 0 3-1 1-1 2-2 2l1-2c0-1 0-2-1-3-1-4-4-5-8-6 2-1 3-1 4-1z" class="U"></path><path d="M612 256l1 1v-1c1 0 1 0 2 1 0 0 2 2 2 3s-1 3-2 4c0 2-1 4-3 4 0 1-1 1-1 1h-1c-2 2-4 1-6 1h-1c1 0 2-1 3-2 2-1 4-3 5-5h-1 0c0-2 1-3 1-5l1-2z" class="F"></path><path d="M595 279c-4-1-7-1-10-2l-1-1c1-2 2-2 4-3h11c0 1 0 1 1 1 2 1 4 3 5 5h1-3-8z" class="K"></path><path d="M624 286h2 0c-2-2-4-3-7-4l-13-5c-1-1-2-1-3-2h1l2 1c1 0 3 1 4 2h2l4 2c1 0 3 1 5 2h1l3 1h1c-1 0-2-1-3-1-3-1-7-4-11-5-3-1-5-2-8-3-1 0-2-1-3-1h-1c5 1 11 3 15 5 2 0 13 6 14 6l1-1v1c1 1 2 2 3 2s1 0 1-1c0 3 2 5 3 7 0 0 1 1 1 2h-1c-4-3-9-5-13-8z" class="e"></path><path d="M607 261c1 0 1-1 2-2 0-1 1-2 0-3v-2h1c1 2 1 2 1 4s-1 3-1 5h0 1c-1 2-3 4-5 5-1 1-2 2-3 2-5 0-9-1-14-2l-3-1h0l-3-2v-1c-1 0-2 0-3-1h-1v-1h0c1 0 1 0 2 1v-1c3 2 5 3 8 5-1-2-2-3-2-5 2 1 4 2 6 2 3 1 7 1 11 0 1-1 2-2 3-2v-1z" class="L"></path><path d="M581 262l8 5h0c4 1 9 2 13 1 3-1 6-2 8-5h1c-1 2-3 4-5 5-1 1-2 2-3 2-5 0-9-1-14-2l-3-1h0l-3-2v-1c-1 0-2 0-3-1h-1v-1h0c1 0 1 0 2 1v-1z" class="M"></path><defs><linearGradient id="BL" x1="616.787" y1="447.77" x2="676.7" y2="502.804" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#484748"></stop></linearGradient></defs><path fill="url(#BL)" d="M679 382h2 3l1 1c0 1 2 3 2 4 2 3 5 7 7 10l5 6c0 1 1 2 2 2 2 3 3 6 5 9l2 2h1c0 1 1 2 2 2 0 1 3 4 3 5 7 7 18 11 27 12v1h0l-1 1c-1 0-2 0-3-1-7 0-12-1-18-3-1 0-3-1-4-1-2-1-5-2-6-4l-1-1c-1-1-3-2-5-4-2-1-3-3-7-3-1 0-4-1-5 0h1c-2 3-2 7-2 10-1-1-1-2-1-2l-1-1c-1 4-7 8-7 11-4 6-7 12-9 20v3h0-1l-2-1c-2-3-6-7-9-8-1 0-2 0-3-1-1 1-2 2-4 3 0 0 0 1-1 1 1 0 0 0 1 1h-1c-1 1-1 3-1 4-1 0-1-1-1-1l-1 1-2 2c0 1 0 2 1 3l1 3v2l-3 3c-2 3-5 6-7 10v5 6 1 1c-2-2-5-2-7-1-2 0-4 1-6 3h0c-5 4-8 10-9 17h-1v1l-1 1 1 1v1c-1 2 0 5 0 8-2-3-3-5-6-6h-1l-1-1h0c0 1 0 1 1 2v1c1 2 2 3 3 5 1 0 1 1 1 1v1c1 0 1 1 1 1v1-1l-3-3c-1-2-3-2-4-3h-1-2c-1 0-1 1-2 1 0 2-2 4-3 5 0 4 0 9 2 12 1 4 2 9 4 12 2 5 5 9 9 13-2 0-6-2-7-3 0 1 0 3 1 3h1c1 0 1 1 2 1 0 1 0 1 1 1h0v1c-3-1-8-3-11-6 1 0 1 0 1-1l-5-5-1-3-3-6c0-2 0-4-1-6l-1 1c-3-4-5-19-6-24 0-2 1-11 0-12 0-2 0-4-1-5v-4c1-1 2-1 3-2l1 1 2 1s0-1-1-2l-1-1-1-1h-1c-1 1-3 0-4 0l-1 1c-1 0-1 0-2 1 0 0 2 0 3 1-2 0-2-1-3-1-1 1-2 1-4 1 2-2 6-2 8-4l2-1 1-4v-1c3-1 5 1 7 0l4-3 12-9c1-1 2-2 3-2 3-2 7-5 10-8 17-18 31-38 42-61 3-4 5-9 7-13 1-2 1-6 2-8 1-1 5-2 6-3z"></path><path d="M599 531v-1l1-1c-1-2 1-3 1-5v-3h2c0 1 1 1 1 2h1l-1 1-2-2v1h0v3c0 2-2 4-3 5z" class="S"></path><path d="M674 420c2-1 5 0 6 1 2 0 2 1 4 2l3 3-7-3c-1-1-2-1-3-1l-3-2z" class="N"></path><path d="M677 422c-1 0-1 0-2-1-5-2-10 1-14 2 3-2 6-4 9-4 2 0 3 0 4 1l3 2z" class="P"></path><path d="M613 491h1 1 0c-3 3-4 7-6 10l-2-1v-1l1-1c1-1 1-2 2-3 1-2 2-3 3-4z" class="N"></path><path d="M710 424c2 1 3 3 5 4 1 0 1 2 2 2l4 1 1 1h1c0 1 1 2 2 2l1 1h1 2c1 0 0 0 1 1h5 2 0c-7 0-12-1-18-3-1 0-3-1-4-1 0-2-1-2-2-3l-1-3c-1 0-2-1-2-2zm-84 74v-1c1-1 2-2 3-2 2-1 3-1 5-1h0l1-1c1 1 2 1 3 1 0 0-2 0-2-1l-1-1c-1 0-5 2-7 2 1-1 3-1 4-2h2 1v-2c0-2 3-6 4-7v5 6 1 1c-2-2-5-2-7-1-2 0-4 1-6 3h0z" class="V"></path><path d="M605 523c-1-1-1-2-2-3 0-1 0-2-1-3v-2-1-2c-1-2 0-6 0-8 1-1 1-3 2-5 1-4 4-9 7-12h1 0v1l-4 5c-1 3-3 6-4 8v3c-1 2-1 7 0 10 1 1 1 2 1 3s2 4 2 5l-1-1c0-1 0-1-1-2 0-1-1-2-1-3h0c0 1 0 2 1 3v1c0 1 1 2 2 4h0l-2-1z" class="S"></path><path d="M591 534c2 8 5 16 8 23 1 4 5 6 8 8 0 1 0 3 1 3h1c1 0 1 1 2 1 0 1 0 1 1 1h0v1c-3-1-8-3-11-6 1 0 1 0 1-1l-5-5-1-3-3-6c0-2 0-4-1-6h0v-1c-1-3-1-5-1-9z" class="F"></path><path d="M688 427c0-1 1-3 1-5 0-1 0-1-1-2l1-1-1-1v-2l1-1-1-1h3v1c1 0 2 0 3 1h3c1 1 2 1 3 1h0l1 1v-1h1v-1l1 1v-1l7 8c0 1 1 2 2 2l1 3c1 1 2 1 2 3-2-1-5-2-6-4l-1-1c-1-1-3-2-5-4-2-1-3-3-7-3-1 0-4-1-5 0h1c-2 3-2 7-2 10-1-1-1-2-1-2l-1-1z" class="T"></path><defs><linearGradient id="BM" x1="589.95" y1="521.584" x2="585.274" y2="522.587" xlink:href="#B"><stop offset="0" stop-color="#898789"></stop><stop offset="1" stop-color="#b3b3b3"></stop></linearGradient></defs><path fill="url(#BM)" d="M585 509c0-2 0-4-1-5v-4c1-1 2-1 3-2l1 1 2 1v8c0 9-1 18 1 26 0 4 0 6 1 9v1h0l-1 1c-3-4-5-19-6-24 0-2 1-11 0-12z"></path><path d="M647 462v-4c1 0 1-1 1-1h0 2l1-2c0-1 0-1-1-2l-3-1h-1v-1c-4 5-5 11-9 16-3 3-7 7-11 7h-2l-2 2h0v-1-1c1-1 2-3 4-4 4-2 10-8 12-12 1-2 3-3 5-5 4-6 7-13 13-17 1 0 5 1 6 1 2 0 3-1 4 0 2 0 4 4 5 5v1c-1-2-2-3-3-3l-1-1s-1-1-2-1h0c-1-1-4-1-5 0-5 1-7 2-10 6-1 2-3 5-3 7h1c1 1 2 1 4 1v-1h2 3c-1 1-2 2-4 3 0 0 0 1-1 1 1 0 0 0 1 1h-1c-1 1-1 3-1 4-1 0-1-1-1-1l-1 1-2 2z" class="d"></path><path d="M633 467h1 0l-2 2c-1 1-3 2-4 3h-1v-1c2-1 4-3 6-4z" class="N"></path><path d="M653 230l3 1 8 5c2 1 4 2 5 3 1 2 3 3 5 4l-1 1h1l3 3 27 19c2 1 8 6 9 6 6 6 11 10 15 16 1 2 2 4 2 5l-1 1v2c-4 0-9 1-13 1v1h7c3 0 7-1 9 0l1 1c-1 1-2 2-3 2v1 1c-1 0-2 0-2 1 5 3 8 7 11 12h0v2l1 1-1 2c6 3 10 8 13 14l-5-3c-3-1-6-2-8-3s-4-2-5-3c-2-1-4 0-6 0v1c3 0 5 0 7 2h0c-3 0-6 0-10 1s-8 3-10 7c0-1-1-1-1-1v-1c0-1 0-3-1-4 0-2-1-3-1-4-1-2-1-2-3-3h-2c-2 1-4 2-5 4l-1 2-3 6-4 15-1-1v-2l-1 3h-1v2c1 2 1 4 2 6v2l2 6 11 31h-1l3 6 1 2c1 1 1 2 1 2 1 3 4 6 6 8 1 0 1 1 2 1v1h-1-1l-1-1h-1l1 1h-4c-1 0-2-1-2-2h-1l-2-2c-2-3-3-6-5-9-1 0-2-1-2-2l-5-6c-2-3-5-7-7-10 0-1-2-3-2-4l-1-1c-1-1-1-1-1-2 0-2 0-5-1-7l-2-24h1v2h0v-4l-4-30c0-3-1-6-1-9l-1-1h-1l-1-1-1-7c-3-9-7-19-11-28s-8-17-14-25l-4-7v-1c2-2 4-4 6-4l3-1c2-1 1-1 1-3z" class="d"></path><path d="M672 299v1c1 1 1 1 1 2 1 0 1 1 1 2l2 4-1-1h-1l-1-1-1-7z" class="M"></path><path d="M687 376c1 5 3 9 6 13l-1 1-5-5c1-2-1-6-2-8l2-1z" class="E"></path><path d="M691 353c1 2 1 4 2 6v2l2 6h-1v3c-2-5-3-11-4-16h0l1-1z" class="O"></path><path d="M694 370v-3h1l11 31h-1c-5-8-9-18-11-28z" class="Y"></path><path d="M653 230l3 1 8 5c2 1 4 2 5 3 1 2 3 3 5 4l-1 1h1l3 3c-7-4-14-10-21-13h-1-6l3-1c2-1 1-1 1-3z" class="O"></path><path d="M686 324c-2-3-3-8-3-11 0-2-1-4-2-6h0c2 1 3 2 4 4h0v-1h1v2c0 1 1 2 1 3s0 2-1 2v1h1c0 1 0 3 1 4h1c1 1 0 8 0 10l-1 8v-2c-2-5-1-10-2-14z" class="T"></path><path d="M686 324l2-1c1 2 0 9 0 11h0c-1 1 0 3 0 4-2-5-1-10-2-14z" class="M"></path><defs><linearGradient id="BN" x1="684.075" y1="351.34" x2="682.068" y2="372.64" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#828182"></stop></linearGradient></defs><path fill="url(#BN)" d="M681 347l6 29-2 1c1 2 3 6 2 8l-2-2-1-1c-1-1-1-1-1-2 0-2 0-5-1-7l-2-24h1v2h0v-4z"></path><path d="M683 380c1-2 1-5 1-8 0 2 1 4 1 5 1 2 3 6 2 8l-2-2-1-1c-1-1-1-1-1-2z" class="e"></path><path d="M679 272c0-1 0-2 1-3v-1c1-1 2-2 4-3h0-1c-1 0-2-1-3-1-1-1-3-2-4-3h0s1-1 2 0c1 0 1 1 2 1s1 1 2 1l-1-1h0c-1 0-1-1-2-1l-1-1c-1 0-1-1-2-2-1 0-1-1-2-1v-1c4 2 8 5 11 8 6 3 13 6 18 11 1 1 2 1 4 1 1 1 2 3 2 5 0 1 0 2 1 3v3c1 2 0 4 0 7-1-1-1-3-1-5 0-3 0-5-1-8v-1c-1-1-1-2-2-2 0 1 0 1-1 1-3-2-6-6-9-8h-1c-1-1-2-1-4-1-2-2-5-4-7-4-1 0-2 1-3 2s-1 1-1 2l-1 2z" class="V"></path><path d="M685 383l2 2 5 5 1-1c2 2 3 3 4 5 3 4 6 8 9 11 0 1 1 1 1 1h0c3 2 6 8 8 11h-1l1 1h-4c-1 0-2-1-2-2h-1l-2-2c-2-3-3-6-5-9-1 0-2-1-2-2l-5-6c-2-3-5-7-7-10 0-1-2-3-2-4z" class="G"></path><path d="M709 416l-3-6s1 0 1 1c2 1 3 4 6 5l1 1 1 1h-4c-1 0-2-1-2-2z" class="X"></path><path d="M685 383l2 2 5 5v1c1 1 3 2 4 4s3 3 4 5 2 4 4 5c0 1 1 2 2 3-2-1-2-2-4-3v1c-1 0-1-1-1-1-1 0-2-1-2-2l-5-6c-2-3-5-7-7-10 0-1-2-3-2-4z" class="T"></path><path d="M710 294v-1c1-1 1-1 1-2-1-1-1-5-1-7h0c0-1 0-1 1-2v2 3h2c1 0 2-1 3-1-1-2-2-2-3-3l-1-1c2 0 3 1 4 2l7 4c2 1 4 3 5 6h1v2c-4 0-9 1-13 1v1h-1c-1 0-3 0-4-1h1l-1-2h-2v-6c0 2 0 4 1 5z" class="O"></path><defs><linearGradient id="BO" x1="722.133" y1="291.569" x2="717.96" y2="295.918" xlink:href="#B"><stop offset="0" stop-color="#5f5e5e"></stop><stop offset="1" stop-color="#757474"></stop></linearGradient></defs><path fill="url(#BO)" d="M716 284l7 4c2 1 4 3 5 6h1v2c-4 0-9 1-13 1v1h-1c-1 0-3 0-4-1h1l2-1v-1h1v1c1 1 1 1 2 0v-1l-1-1v-3c3-2 5 1 7 2 1 1 2 1 3 1h0l-3-3c0-1-2-2-3-3l-4-4z"></path><path d="M679 272l1-2c0-1 0-1 1-2s2-2 3-2c2 0 5 2 7 4 2 0 3 0 4 1h1c3 2 6 6 9 8 1 0 1 0 1-1 1 0 1 1 2 2v1c-1 2 0 6 0 9h0c-1 2-3 4-5 6l-2 2c0 1-1 2-2 3-1 3-3 5-4 8h0c-1 1-1 2-1 2h-1c0-3-1-5-2-7 0-2 0-3-1-4l-1 1v-2h-1v-1c-2-7-7-12-9-19l-1-1v-4l1-1v-1z" class="H"></path><path d="M682 269l1-1c1 0 3 1 5 1l-3 1c-1 0-1 0-2 1 0-1-1-1-1-2z" class="a"></path><path d="M702 281c0 2 1 4 1 6 0 3-1 7 0 9l-2 2c0-4 1-7 1-11 0-2-1-4 0-6z" class="D"></path><defs><linearGradient id="BP" x1="707.059" y1="280.023" x2="700.429" y2="292.13" xlink:href="#B"><stop offset="0" stop-color="#4d4e4d"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#BP)" d="M691 270c2 0 3 0 4 1h1c3 2 6 6 9 8 1 0 1 0 1-1 1 0 1 1 2 2v1c-1 2 0 6 0 9h0c-1 2-3 4-5 6-1-2 0-6 0-9 0-2-1-4-1-6h0c-1-2-2-5-3-6h-2c-2 0-5-4-6-5z"></path><path d="M682 269c0 1 1 1 1 2 0 2 0 4 1 6h0c2 5 6 9 8 14 1 1 2 5 3 5 1 3 1 4 1 7l1 1c0-1 1-2 1-3h1c-1 3-3 5-4 8h0c-1 1-1 2-1 2h-1c0-3-1-5-2-7 0-2 0-3-1-4l-1 1v-2h-1v-1c-2-7-7-12-9-19v-2-3c1 0 0-1 1-2h0l1 2c0-1 0-3 1-5z" class="F"></path><path d="M685 283c3 4 6 9 8 14-2-2-3-4-5-6 0-2-2-4-2-5-1-1-1-2-1-3z" class="H"></path><path d="M682 269c0 1 1 1 1 2 0 2 0 4 1 6h0c2 5 6 9 8 14 1 1 2 5 3 5 1 3 1 4 1 7 0 1 0 1-1 1-1-2-1-5-2-7-2-5-5-10-8-14-2-3-3-5-4-9 0-1 0-3 1-5z" class="V"></path><path d="M688 269c2 1 6 3 7 5v1c0 1 1 2 2 3l3 9c-1 1-2 1-2 1 0 1 1 3 0 4 0 1 0 1-1 2 0 1 0 2-1 3h0c0-1 0 0-1-1h0c-1 0-2-4-3-5-2-5-6-9-8-14h0c-1-2-1-4-1-6 1-1 1-1 2-1l3-1z" class="R"></path><defs><linearGradient id="BQ" x1="698.701" y1="314.049" x2="715.675" y2="333.18" xlink:href="#B"><stop offset="0" stop-color="#151515"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#BQ)" d="M708 281c1 3 1 5 1 8v6h2l1 2h-1c1 1 3 1 4 1h1 7c3 0 7-1 9 0l1 1c-1 1-2 2-3 2v1 1c-1 0-2 0-2 1 5 3 8 7 11 12h0v2l1 1-1 2c6 3 10 8 13 14l-5-3c-3-1-6-2-8-3s-4-2-5-3c-2-1-4 0-6 0v1c3 0 5 0 7 2h0c-3 0-6 0-10 1s-8 3-10 7c0-1-1-1-1-1v-1c0-1 0-3-1-4 0-2-1-3-1-4-1-2-1-2-3-3h-2c-2 1-4 2-5 4l-1 2-3 6-4 15-1-1v-2l-1 3h-1v2l-1 1h0l-2-14 1-8c0-2 1-9 0-10h-1c-1-1-1-3-1-4v-3c1-1 1-1 1-2v-2c0-1 1-3 1-4v-1-3c-1-1 0-2 0-4v2l1-1c1 1 1 2 1 4 1 2 2 4 2 7h1s0-1 1-2h0c1-3 3-5 4-8 1-1 2-2 2-3l2-2c2-2 4-4 5-6h0c0-3-1-7 0-9z"></path><path d="M730 318c3-1 5-1 9 0l1 1-1 2c-1-1-2-2-3-2-2-1-4-1-6-1z" class="Z"></path><path d="M730 318l-5-2c5-1 9-1 14 0v2c-4-1-6-1-9 0z" class="K"></path><path d="M698 328v3 1l-1 1c0 2 0 2 1 3l-4 15-1-1v-2c0-7 1-14 5-20z" class="G"></path><path d="M689 332c0-4 1-7 3-11 0-2 2-4 4-5v1c-6 11-6 22-5 34v2l-1 1h0l-2-14 1-8z" class="Z"></path><path d="M708 281c1 3 1 5 1 8v6h2l1 2h-1c1 1 3 1 4 1h1 7c3 0 7-1 9 0l1 1c-1 1-2 2-3 2v1 1c-1 0-2 0-2 1-2-1-3-2-5-2-1 0-2-1-3-2-2 1-3 1-5 1-3 1-8 5-10 7l-2 2c-1-1-1 0-1-1l-2 1h-1c1-2 2-3 3-4l1-1 1-3c0-1 1-2 1-3v-1c0-1 0-2 1-3s2-2 3-4l-1-1h0c0-3-1-7 0-9z" class="F"></path><path d="M723 298c3 0 7-1 9 0l1 1c-1 1-2 2-3 2v1 1c-1 0-2 0-2 1-2-1-3-2-5-2-1 0-2-1-3-2-2 0-4 1-5 0h-1-3c1 0 2-1 3-1 3-1 6 0 9-1z" class="T"></path><path d="M723 302c2-1 5-1 7-1v1 1c-1 0-2 0-2 1-2-1-3-2-5-2z" class="F"></path><path d="M703 305c1-1 3-4 4-5 0-1 0-1 1-2 1 1 1 1 2 1l1 1h3 1c1 1 3 0 5 0-2 1-3 1-5 1-3 1-8 5-10 7l-2 2c-1-1-1 0-1-1l-2 1h-1c1-2 2-3 3-4l1-1z" class="M"></path><path d="M708 290l1 1c-1 2-2 3-3 4s-1 2-1 3v1c0 1-1 2-1 3l-1 3-1 1c-1 1-2 2-3 4h1l2-1c0 1 0 0 1 1l-7 7v-1c-2 1-4 3-4 5-2 4-3 7-3 11 0-2 1-9 0-10h-1c-1-1-1-3-1-4v-3c1-1 1-1 1-2v-2c0-1 1-3 1-4v-1-3c-1-1 0-2 0-4v2l1-1c1 1 1 2 1 4 1 2 2 4 2 7h1s0-1 1-2h0c1-3 3-5 4-8 1-1 2-2 2-3l2-2c2-2 4-4 5-6z" class="O"></path><path d="M689 301l1-1c1 1 1 2 1 4 1 5 2 8-1 13 0 0 0 1-1 1h0v1h-1c0-3 1-5 1-7s1-4 1-5l-1-6z" class="Q"></path><path d="M690 307c1 1 2 4 1 6 0 1-1 1-1 2-1-1-1-2-1-3 0-2 1-4 1-5z" class="J"></path><path d="M708 290l1 1c-1 2-2 3-3 4s-1 2-1 3v1c0 1-1 2-1 3l-1 3-1 1c-2 1-4 3-6 4l-3 6c-1 1-3 2-4 2 1 0 1-1 1-1 3-5 2-8 1-13 1 2 2 4 2 7h1s0-1 1-2h0c1-3 3-5 4-8 1-1 2-2 2-3l2-2c2-2 4-4 5-6z" class="D"></path><path d="M708 290l1 1c-1 2-2 3-3 4-2 2-3 4-5 6 0 2-1 3-2 4-2 1-2 3-4 4 1-3 3-5 4-8 1-1 2-2 2-3l2-2c2-2 4-4 5-6z" class="C"></path><defs><linearGradient id="BR" x1="714.131" y1="315.708" x2="717.902" y2="326.228" xlink:href="#B"><stop offset="0" stop-color="#3e3d3e"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#BR)" d="M698 328c2-5 6-13 11-15l1-1v1l-1 1s1 1 2 1c1-1 1-1 2-1v1h0c-1 0-2 0-2 1 1 1 1 1 2 1 0 1 1 1 2 1h1v-1l1 1 1-1h2c1 0 3 1 4 2 2 1 3 2 4 3 4 2 8 4 12 5 1 1 4 2 5 2 1 1 1 2 2 3-3-1-6-2-8-3s-4-2-5-3c-2-1-4 0-6 0v1c3 0 5 0 7 2h0c-3 0-6 0-10 1s-8 3-10 7c0-1-1-1-1-1v-1c0-1 0-3-1-4 0-2-1-3-1-4-1-2-1-2-3-3h-2c-2 1-4 2-5 4l-1 2-3 6c-1-1-1-1-1-3l1-1v-1-3z"></path><path d="M701 330h-1 0v-1-1c0-1 0-1 1-1 0-1 1-1 1-2h0l1-1c0-1 3-4 4-5s2-1 3-1l1 1c-2 2-3 3-4 5-2 1-4 2-5 4l-1 2z" class="R"></path><path d="M728 327c3 0 5 0 7 2h0c-3 0-6 0-10 1s-8 3-10 7c0-1-1-1-1-1v-1l1-2c3-5 8-5 13-6z" class="c"></path><defs><linearGradient id="BS" x1="646.561" y1="187.68" x2="645.801" y2="163.803" xlink:href="#B"><stop offset="0" stop-color="#bdbbbd"></stop><stop offset="1" stop-color="#dedddd"></stop></linearGradient></defs><path fill="url(#BS)" d="M582 170h4l1-1c1-1 3-3 4-5 1-1 2-4 2-5-1-1-1-2 0-3 2-2 1-5 4-7l3 2 1-1 3 4 2 2v-1h2l2 2h1c1 0 2 1 2 1l2 2 6 3c3 2 7 3 11 4l1-1c2 2 7 1 10 1 1 0 2-1 3 0l2-1h2c1-1 2-2 2-3 0-3-4-4-5-7v-1c1-2 1-3 2-4 1 2 2 3 3 5v1c1 1 2 1 3 3l1 1v1c2 0 2-1 2-1 3 0 4 2 6 3 4 2 8 5 13 5v1h1l3 1h4c2 1 4 1 6 2-1 1 0 1-1 1l1 1-1 1c2 0 3-1 5-2 9 4 19 7 27 13 2 2 5 4 7 6v1c1 2 3 3 4 5v1c1 0 2 1 2 2-3-3-6-7-10-9-2-1-4-2-6-2-1-1-2-1-2 0l4 2 2 1-2 1c1 0 2 0 2 1l2 1c-4-1-7-2-10-3-5-1-10-1-15-1h-4s0-1-1-1l-7 1c-2 0-5 1-7 2s-4 3-7 5h0l3 1c2 0 3 1 4 2l1 2v1c0 3 0 7 2 10v1h0l-2-1v3l6 7 2 2h-1l-7-7h-2 0l-3-3-1-1-1-1c0-1-1-3-2-3h-3l-1-1-1 1h0 1c1 1 1 2 0 3-1 2-4 6-6 7l-4 4c-1 1-1 2-2 3l-1 1-3-1-1-1-2 2h0l-2 1c-1-1-1-1-1-3l2-2-1-2c-1 0-3-3-3-3-1-1-3-2-4-2l-1-1c-1-2-4-3-6-4-2 0-5-3-7-5l-7-4-1-1c-2-1-3-2-5-3 0 3 5 6 7 8 0 1 2 2 3 2v1 1l-10-6c-1 0-3-1-4-2l-12-8c-4-3-8-5-12-7l-15-7c-2-1-5-1-7-3l1-1h3c-1-1-3-1-5-2h-1v-1h-2c-1 0-1 0-1-1h-1c0-1-1-2-2-3 2 0 3 1 5 0 2 0 4-1 5-2l1-1c0-1 1-2 1-4 1 0 1-1 1-2 1 0 1-1 2-1 2 3 4 5 7 7-1-1-3-2-3-4 2 2 2 2 4 3l3 1z"></path><path d="M649 151c1 2 2 3 3 5v1c1 1 2 1 3 3l1 1v1c2 0 2-1 2-1 3 0 4 2 6 3 4 2 8 5 13 5v1h1l3 1h4c2 1 4 1 6 2-1 1 0 1-1 1h-2-4-15c-5-1-9-2-14-2h-7c-1 1-2 1-3 2h-6c-5 1-10 1-14 1h-24c-2-1-5-1-7-1 4 0 8-2 13-2 8-1 16-1 24 0h6c2-1 4 0 6 0 2-1 4-2 5-3 1 0 1 0 2-1l-1-1c0 1-1 1-2 1l2-1h-1-2l2-1h2c1-1 2-2 2-3 0-3-4-4-5-7v-1c1-2 1-3 2-4z" class="C"></path><path d="M678 170l3 1h4c2 1 4 1 6 2-1 1 0 1-1 1h-2l-11-2c0-1 0-1 1-2z" class="B"></path><path d="M649 151c1 2 2 3 3 5v1l1 4c-1 0-1-1-2-2s-2-2-3-2l-1-1v-1c1-2 1-3 2-4z" class="I"></path><path d="M652 157c1 1 2 1 3 3l1 1c-1 2 0 3 0 5 1 0 6 3 7 2l4 2c-3 0-9 0-12-2-2-1-1-4-2-6v-1l-1-4z" class="K"></path><defs><linearGradient id="BT" x1="657.805" y1="165.909" x2="670.804" y2="164.304" xlink:href="#B"><stop offset="0" stop-color="#5e5d5e"></stop><stop offset="1" stop-color="#7d7d7c"></stop></linearGradient></defs><path fill="url(#BT)" d="M658 161c3 0 4 2 6 3 4 2 8 5 13 5v1h1c-1 1-1 1-1 2l-10-2-4-2c-1 1-6-2-7-2 0-2-1-3 0-5v1c2 0 2-1 2-1z"></path><path d="M663 168h2c2 0 4 1 7 1 2 0 3 1 5 1h1c-1 1-1 1-1 2l-10-2-4-2z" class="X"></path><defs><linearGradient id="BU" x1="590.83" y1="159.649" x2="645.151" y2="170.424" xlink:href="#B"><stop offset="0" stop-color="#585858"></stop><stop offset="1" stop-color="#cbc9c9"></stop></linearGradient></defs><path fill="url(#BU)" d="M582 170h4l1-1c1-1 3-3 4-5 1-1 2-4 2-5-1-1-1-2 0-3 2-2 1-5 4-7l3 2 1-1 3 4 2 2v-1h2l2 2h1c1 0 2 1 2 1l2 2 6 3c3 2 7 3 11 4l1-1c2 2 7 1 10 1 1 0 2-1 3 0h2 1l-2 1c1 0 2 0 2-1l1 1c-1 1-1 1-2 1-1 1-3 2-5 3-2 0-4-1-6 0h-6c-8-1-16-1-24 0-5 0-9 2-13 2l-9-2-3-2z"></path><path d="M582 170h4l1-1c1-1 3-3 4-5 1-1 2-4 2-5-1-1-1-2 0-3 2-2 1-5 4-7l3 2 1-1 3 4h-1c-1-1-2-1-4-1h0v-1-1c-2 0-2 1-3 2s-2 3-2 4h0c1-1 1 0 1-1l1 1h0l-1 1 1 1h-2v2c0 3-1 4-3 5v1c0 1-1 2-2 3-2 1-3 1-4 2l-3-2z" class="W"></path><path d="M633 166c2 2 7 1 10 1 1 0 2-1 3 0h2 1l-2 1c1 0 2 0 2-1l1 1c-1 1-1 1-2 1-1 1-3 2-5 3-2 0-4-1-6 0l-4-1h0c1 0 2-1 3 0h2 4 0c1 0 2 0 3-1h0l1-1c-1 0-2 1-3 1h-8c-1-1-2-1-3-1h0c0-1-2-1-3-2h3 0l1-1z" class="U"></path><path d="M556 173c2 0 3 1 5 0 2 0 4-1 5-2l1-1c0-1 1-2 1-4 1 0 1-1 1-2 1 0 1-1 2-1 2 3 4 5 7 7 5 3 11 4 17 6l13 2 35 2c17 0 35-1 52 2 4 1 8 1 11 2h5c3 1 9 4 11 3 2 2 5 4 7 6v1c1 2 3 3 4 5v1c1 0 2 1 2 2-3-3-6-7-10-9-2-1-4-2-6-2-1-1-2-1-2 0l-9-3c-15-4-33-6-49-5-6 0-12 2-18 2-11 0-25-1-36 4h-1c2 4 4 7 7 10l3 3c0 3 5 6 7 8 0 1 2 2 3 2v1 1l-10-6c-1 0-3-1-4-2l-12-8c-4-3-8-5-12-7l-15-7c-2-1-5-1-7-3l1-1h3c-1-1-3-1-5-2h-1v-1h-2c-1 0-1 0-1-1h-1c0-1-1-2-2-3z" class="M"></path><path d="M706 184h5c3 1 9 4 11 3 2 2 5 4 7 6v1l-23-10z" class="Q"></path><path d="M614 208c1 0 1 1 2 1 0-1 0-1-1-1-2-1-3-3-4-4l-3-3c0-1-1-3-2-4h-1l-3-4h-1c-1 0-1 1-2 2h0l-1-2c2-1 2-2 4-4h1v2c1 1 1 2 2 3 1 3 4 5 6 7h1c-1-1-1-1-1-2l3 3c0 3 5 6 7 8 0 1 2 2 3 2v1 1l-10-6z" class="V"></path><defs><linearGradient id="BV" x1="584.588" y1="168.08" x2="580.498" y2="179.017" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#a1a1a1"></stop></linearGradient></defs><path fill="url(#BV)" d="M556 173c2 0 3 1 5 0 2 0 4-1 5-2l1-1c0-1 1-2 1-4 1 0 1-1 1-2 1 0 1-1 2-1 2 3 4 5 7 7 5 3 11 4 17 6l13 2c-5 1-11 1-16 0l-22 1-2 1c-1-1-3-1-5-2h-1v-1h-2c-1 0-1 0-1-1h-1c0-1-1-2-2-3z"></path><defs><linearGradient id="BW" x1="585.932" y1="172.436" x2="572.508" y2="183.168" xlink:href="#B"><stop offset="0" stop-color="#8a8a89"></stop><stop offset="1" stop-color="#b7b5b9"></stop></linearGradient></defs><path fill="url(#BW)" d="M570 179h-1 0c3-1 4-2 7-3h8 2c1 1 2 0 3 1h2l1 1-22 1z"></path><defs><linearGradient id="BX" x1="659.681" y1="182.963" x2="661.45" y2="208.941" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#BX)" d="M611 199c-3-3-5-6-7-10h1c11-5 25-4 36-4 6 0 12-2 18-2 16-1 34 1 49 5l9 3 4 2 2 1-2 1c1 0 2 0 2 1l2 1c-4-1-7-2-10-3-5-1-10-1-15-1h-4s0-1-1-1l-7 1c-2 0-5 1-7 2s-4 3-7 5h0l3 1c2 0 3 1 4 2l1 2v1c0 3 0 7 2 10v1h0l-2-1v3l6 7 2 2h-1l-7-7h-2 0l-3-3-1-1-1-1c0-1-1-3-2-3h-3l-1-1-1 1h0 1c1 1 1 2 0 3-1 2-4 6-6 7l-4 4c-1 1-1 2-2 3l-1 1-3-1-1-1-2 2h0l-2 1c-1-1-1-1-1-3l2-2-1-2c-1 0-3-3-3-3-1-1-3-2-4-2l-1-1c-1-2-4-3-6-4-2 0-5-3-7-5l-7-4-1-1c-2-1-3-2-5-3l-3-3z"></path><path d="M695 192c3-1 7-1 9-1l2 1c0 1-4 0-6 1h-4s0-1-1-1z" class="O"></path><path d="M704 191c4 0 7 1 10 1 2 0 6 2 7 1l2 1-2 1c1 0 2 0 2 1l2 1c-4-1-7-2-10-3-5-1-10-1-15-1 2-1 6 0 6-1l-2-1z" class="G"></path><path d="M623 188h0c5 1 10 1 15 0 0 1-1 1-2 2h0-1c-2 1-10 2-12 1v-3z" class="P"></path><path d="M623 188v3c2 1 10 0 12-1h1c-1 1-1 3-2 4l-1-1c0 1-2 1-2 1h-1c-2 0-6 1-7 0h-1 0c1-2 0-4 1-6z" class="N"></path><path d="M622 194h0 1c1 1 5 0 7 0h1s2 0 2-1l1 1v3c-1 1-1 2-1 4h-2c-2 1-4 1-6 0h0l-1-1-1 1h0c1 1 1 2 2 3l2 2-2 1c1 1 2 2 2 3l-7-4-1-1c0-2-2-4-2-5 1-1 1-2 2-3h2c0-1 1-2 1-3z" class="M"></path><path d="M620 206h0c0-2-1-4 0-5 2 1 4 4 5 6 1 1 2 2 2 3l-7-4z" class="W"></path><path d="M625 204c-2-1-3-3-3-4-1-1-1-2 0-3 4-1 8 0 12 0-1 1-1 2-1 4h-2c-2 1-4 1-6 0h0l-1-1-1 1h0c1 1 1 2 2 3z" class="B"></path><defs><linearGradient id="BY" x1="635.366" y1="203.355" x2="631.572" y2="216.544" xlink:href="#B"><stop offset="0" stop-color="#6f6f70"></stop><stop offset="1" stop-color="#a7a5a5"></stop></linearGradient></defs><path fill="url(#BY)" d="M625 204c-1-1-1-2-2-3h0l1-1 1 1h0c2 1 4 1 6 0h2c2 5 6 9 10 12v1l1 2v1 1 2s-1 0-2-1h-2c-1-2-4-3-6-4-2 0-5-3-7-5 0-1-1-2-2-3l2-1-2-2z"></path><path d="M627 206l7 9c-2 0-5-3-7-5 0-1-1-2-2-3l2-1z" class="f"></path><path d="M647 208c-1-1-4-2-5-3v-1h4v-1h-2 0l-1-1v-1h-1c3-3 5-4 10-4 1 0 2 0 3-1 1 0 1-1 2-2 3 1 6 0 9 0s6 0 10-1c2 0 6-1 7-1 2 1 3 1 5 1-2 0-5 1-7 2s-4 3-7 5h-2v-2c-6 0-11 0-17 1-2 0-5 0-7 2-1 1-1 1-1 3 1 1 2 1 3 2l-3 2z" class="R"></path><defs><linearGradient id="BZ" x1="661.651" y1="211.783" x2="661.596" y2="219.211" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#6a6969"></stop></linearGradient></defs><path fill="url(#BZ)" d="M650 206c-1-1-2-1-3-2 0-2 0-2 1-3 2-2 5-2 7-2 6-1 11-1 17-1v2h2 0l3 1c2 0 3 1 4 2l1 2v1c0 3 0 7 2 10v1h0l-2-1v3l6 7 2 2h-1l-7-7h-2 0l-3-3-1-1-1-1c0-1-1-3-2-3h-3l-1-1-1 1h0 1c1 1 1 2 0 3-1 2-4 6-6 7l-4 4c-1 1-1 2-2 3l-1 1-3-1-1-1-2 2h0l-2 1c-1-1-1-1-1-3l2-2-1-2c-1 0-3-3-3-3-1-1-3-2-4-2l-1-1h2c1 1 2 1 2 1v-2-1-1l-1-2 1-1h0v-1c1-2 1-3 3-4l3-2z"></path><path d="M645 220h1l1-1 1-1c1 0 2-1 3 0h1 2c2-2 1-3 3-5h1v4c0 1 3 3 4 4l-3 1c-1-1-1-2-2-4 0-1 0-1-1-1l-1 1v5c-1-2-2-3-4-4 0-1-1-1-1-1l-3 3h0-1l-1-1z" class="H"></path><path d="M654 210h3c1-1 4 0 6 0h0 3c-1 1-3 1-5 1v1h-1c0-1 0-2-1-2h-2 0c-1 1-1 1-2 1v1l-1 1c-1 1-1 2-2 2-2 2-5 2-7 3h0l-1-1v-1c4-2 7-3 10-6z" class="M"></path><path d="M645 220l1 1h1 0l3-3s1 0 1 1c2 1 3 2 4 4v-5l1-1c1 0 1 0 1 1 1 2 1 3 2 4-1 1-2 1-4 2-1-2-1-3-3-3l-1-1c-1 2-2 3-3 5-1 0-3-3-3-3-1-1-3-2-4-2l-1-1h2c1 1 2 1 2 1v-2-1l1 1v2z" class="E"></path><path d="M668 213h1c1 1 1 2 0 3-1 2-4 6-6 7l-4 4c-1 1-1 2-2 3l-1 1-3-1-1-1-2 2h0l-2 1c-1-1-1-1-1-3l2-2-1-2c1-2 2-3 3-5l1 1c2 0 2 1 3 3 2-1 3-1 4-2l3-1c3-2 5-4 6-8z" class="U"></path><path d="M650 231h-1c0-1 0-2 1-3 0 0 0-1 1-1 2 0 2 1 3 1v1h-2l-2 2z" class="L"></path><path d="M657 230v-4h-1l1-1c2-1 4-2 6-2l-4 4c-1 1-1 2-2 3z" class="c"></path><path d="M660 204c4 1 8 1 11 3 5 3 7 7 11 12l6 7 2 2h-1l-7-7-2-3c-2-1-3-3-5-5-3-3-8-4-12-5-2-1-4-1-5-1-2 1-3 2-4 3-3 3-6 4-10 6l-1-2 1-1h0v-1c1-2 1-3 3-4l3-2c4-2 7-2 10-2z" class="N"></path><path d="M660 204c4 1 8 1 11 3-5 2-10-2-15-1-1 0-3 2-4 3-2 1-4 2-5 3 0 1-2 1-2 1h-1 0v-1c1-2 1-3 3-4l3-2c4-2 7-2 10-2z" class="g"></path><path d="M650 206c-1-1-2-1-3-2 0-2 0-2 1-3 2-2 5-2 7-2 6-1 11-1 17-1v2h2 0l3 1c2 0 3 1 4 2l1 2v1c0 3 0 7 2 10v1h0l-2-1v3c-4-5-6-9-11-12-3-2-7-2-11-3-3 0-6 0-10 2z" class="H"></path><path d="M664 202h-8c-2 1-4 1-5 1h-1c1-1 2-2 3-2 4-2 9-1 14-1 3 1 4 1 7 0l3 1c2 0 3 1 4 2l1 2c-2-1-2-1-4-1l-6-1c-3-1-6-2-8-1z" class="e"></path><path d="M664 202c2-1 5 0 8 1l6 1c2 0 2 0 4 1v1c0 3 0 7 2 10v1h0l-2-1v3c-4-5-6-9-11-12-3-2-7-2-11-3l7-1-3-1z" class="K"></path><path d="M667 203c7 3 12 6 15 13v3c-4-5-6-9-11-12-3-2-7-2-11-3l7-1z" class="B"></path><path d="M185 232l7 2h1-1-1 0l1 1c2 0 3 1 4 3 0 1-1 1-1 2s-1 2-1 2l-2 2c0 1-1 1-1 2l-1 3h2l-5 9c0 1-2 4-3 5-5 11-10 23-14 36-1 3-3 7-3 11l-6 44-3 12c0 2 1 4 1 7 3 1 3 1 4 4 1 2 1 3 1 5h-1l-5 1-3 2-2 2c-3 3-5 6-7 10l-10 12c-3 5-6 10-10 14-3 3-6 5-9 7l1 1h3c-2 1-4 3-6 3v1c-3 0-6 1-9 1l-1-1h-4c-7 0-13 0-20-2-2-1-4-1-5-2s-3-2-3-3v-1c4 2 8 3 13 3 7 1 17-1 23-6 3-3 6-6 8-10 3-5 4-11 6-17 2-8 5-16 5-26h1v3-1l1-2v3c1-3 1-7 1-11s0-9-1-13c-1-2-2-5-2-7l-1-1v-1c-1-2-2-4-2-7l-1 2h0c-2-2-4-3-6-4-3-2-5-2-8-3h-5c-1 0-1 1-2 0l-1 1c-2 1-4 1-6 3h-1l-1-1-5 4 4-8-2 1c0-3 6-8 8-11v-2s1-2 2-3c3-4 5-7 10-10 2-1 4-1 6-3-4-1-7-3-11-4-2 1-3 1-4 2l-1-1c-1 2-3 4-4 6l-4 5h-1v-1c2-4 4-7 7-10 1-3 4-5 5-7 1-1 1-2 1-3l12-10c8-6 16-12 25-18 4-3 9-6 13-9 1-1 23-14 25-16l5-2z" class="b"></path><path d="M153 387c0-2 1-4 2-7l1 1 1 1h0l1 1-3 2-2 2z" class="K"></path><path d="M139 392l3-5c0 2-1 2 0 4-1 2-3 5-4 8-1-1-1-1-1-3l2-4z" class="D"></path><path d="M190 249h2l-5 9c0 1-2 4-3 5v-1c0-1 1-1 1-2h0c0-1 0 0 1-1v-1c0-1 0-1 1-1 0-1 0-1 1-2 0 0 0-1 1-1h-2c-2 3-4 8-7 10 2-5 7-11 10-15z" class="J"></path><path d="M132 405c2-2 3-6 5-9 0 2 0 2 1 3-2 4-10 17-14 20v-1c3-4 5-8 8-13z" class="K"></path><path d="M129 383v2h0v5c-3 8-6 15-8 23v-1c1-10 5-20 8-29z" class="T"></path><path d="M141 379v4h0 0v1l-2 4v4l-2 4c-2 3-3 7-5 9v-1l-1 1c0 1 0 1-1 2h0 0c0-1 1-3 2-4l-1-1c1-3 4-6 5-9l5-14z" class="L"></path><path d="M155 380c2-4 2-10 3-14 0 2 1 4 1 7 3 1 3 1 4 4 1 2 1 3 1 5h-1l-5 1-1-1h0l-1-1-1-1z" class="J"></path><path d="M157 382l5-6c0 2 0 4 1 6l-5 1-1-1h0z" class="U"></path><path d="M101 435c4-2 8-3 12-4 4-3 8-7 13-8-3 3-6 5-9 7l1 1h3c-2 1-4 3-6 3v1c-3 0-6 1-9 1l-1-1h-4z" class="F"></path><path d="M105 435c5-1 8-2 12-5l1 1h3c-2 1-4 3-6 3v1c-3 0-6 1-9 1l-1-1z" class="S"></path><path d="M155 342c1-12 2-24 8-34 1 4-1 8-2 12l-5 31h-1v-1c-1-2 0-5 0-8h0z" class="B"></path><defs><linearGradient id="Ba" x1="154.161" y1="319.707" x2="159.673" y2="320.652" xlink:href="#B"><stop offset="0" stop-color="#1e1e1d"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#Ba)" d="M159 308h0c6-7 7-17 10-25l1 4c-3 7-4 14-7 21-6 10-7 22-8 34-1-2 0-4 0-5h-1c-2-4-1-9 0-14l1-5c0-1-1-2-1-3h0c0-1-1-1-1-2 1-2 3-4 5-5h1z"></path><path d="M153 313c1-2 3-4 5-5h1c-1 3-3 6-4 10 0-1-1-2-1-3h0c0-1-1-1-1-2z" class="C"></path><defs><linearGradient id="Bb" x1="137.732" y1="349.168" x2="158.575" y2="376.46" xlink:href="#B"><stop offset="0" stop-color="#373835"></stop><stop offset="1" stop-color="#5c5a5c"></stop></linearGradient></defs><path fill="url(#Bb)" d="M137 298c2 0 3 2 5 3l1 2h0-1c0 1 1 1 1 2 3 3 4 8 6 12l3 6h0 2c-1 5-2 10 0 14h1c0 1-1 3 0 5h0c0 3-1 6 0 8v1h1c-2 10-4 20-8 29-1 4-3 7-6 11-1-2 0-2 0-4l-3 5v-4l2-4v-1h0 0v-4c0-2 2-6 2-8l2-10c1-2 1-5 1-7 1-3 2-5 1-8l1-4c0-3 1-6 0-8 0-5-1-9-3-13s-4-7-7-11v-2c-1-1-1-2-2-3v-2-1-4h1z"></path><path d="M152 323h0v2c1 3 1 6 1 9 0 7-1 13-2 19 0 5 0 10-1 14s-3 7-4 10c0 1-1 2-1 3 0-5 2-9 3-13 1-6 2-13 2-19 1-5 1-10 1-15 0-3 1-5 1-7v-3z" class="X"></path><path d="M148 342v-1c1-1 1-3 1-5v-5c-1-1-1-1 0-1v3c1 2 0 5 1 7 0 1 0 4-1 5 0 1 0 1 1 2v1c0 6-1 13-2 19-1 4-3 8-3 13-1 2-2 5-3 7l-3 5v-4l2-4v-1h0 0v-4c0-2 2-6 2-8l2-10c1-2 1-5 1-7 1-3 2-5 1-8l1-4z" class="I"></path><defs><linearGradient id="Bc" x1="131.763" y1="302.792" x2="152.506" y2="324.456" xlink:href="#B"><stop offset="0" stop-color="#686767"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#Bc)" d="M137 298c2 0 3 2 5 3l1 2h0-1c0 1 1 1 1 2 3 3 4 8 6 12l3 6v3c0 2-1 4-1 7 0 5 0 10-1 15v-1c-1-1-1-1-1-2 1-1 1-4 1-5-1-2 0-5-1-7v-3c-1 0-1 0 0 1v5c0 2 0 4-1 5v1c0-3 1-6 0-8 0-5-1-9-3-13s-4-7-7-11v-2c-1-1-1-2-2-3v-2-1-4h1z"></path><defs><linearGradient id="Bd" x1="124.765" y1="366.146" x2="139.876" y2="369.421" xlink:href="#B"><stop offset="0" stop-color="#8b898a"></stop><stop offset="1" stop-color="#a6a5a5"></stop></linearGradient></defs><path fill="url(#Bd)" d="M134 326c2 3 4 5 5 8 2 2 2 5 4 7v4c1-1 1-1 1-2l-1-1h2c0 2 1 3 2 4h0c1 3 0 5-1 8 0 2 0 5-1 7l-2 10c0 2-2 6-2 8l-5 14c-1 3-4 6-5 9-2 4-4 9-7 12h-3l-1-1 1-1v1c2-8 5-15 8-23v-5h0v-2-3l1-6c1-3 1-7 1-11s0-9-1-13c-1-2-2-5-2-7l-1-1v-1c-1-2-2-4-2-7 0-2 1-4 2-7l1 1c1-1 2-1 3-1s2-1 3-1z"></path><defs><linearGradient id="Be" x1="144.065" y1="367.474" x2="136.117" y2="367.286" xlink:href="#B"><stop offset="0" stop-color="#737273"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#Be)" d="M135 333c1 2 2 3 3 5s2 4 4 5c0-1 0-1 1-2v4c1-1 1-1 1-2l-1-1h2c0 2 1 3 2 4h0c1 3 0 5-1 8 0 2 0 5-1 7l-2 10c0 2-2 6-2 8l-5 14-1-1c0-4 0-7 1-11l6-17c1-4 1-12-1-15-1-2-1-3-2-5l-3-3c0-1 0-1-1-2s0-4 0-6z"></path><path d="M143 345c1-1 1-1 1-2l-1-1h2c0 2 1 3 2 4h0c1 3 0 5-1 8 0 2 0 5-1 7 0-5-1-10-2-16z" class="U"></path><path d="M134 326c2 3 4 5 5 8 2 2 2 5 4 7-1 1-1 1-1 2-2-1-3-3-4-5s-2-3-3-5c-1-1-1-2-3-2-1 0-2 0-2 1-3 4 0 12 1 16 1 3 2 6 2 9 0 7 0 13-1 20 0 4-1 9-3 13v-5h0v-2-3l1-6c1-3 1-7 1-11s0-9-1-13c-1-2-2-5-2-7l-1-1v-1c-1-2-2-4-2-7 0-2 1-4 2-7l1 1c1-1 2-1 3-1s2-1 3-1z" class="G"></path><path d="M134 326c2 3 4 5 5 8-4-1-4-9-10-6-2 3-3 5-2 8 0 2 1 4 0 6v-1c-1-2-2-4-2-7 0-2 1-4 2-7l1 1c1-1 2-1 3-1s2-1 3-1z" class="P"></path><defs><linearGradient id="Bf" x1="114.548" y1="303.321" x2="124.818" y2="320.05" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#Bf)" d="M117 300h3v1c5-1 7 1 11 4 2 1 4 2 6 4h0l1 1c3 4 5 7 7 11s3 8 3 13c1 2 0 5 0 8l-1 4h0c-1-1-2-2-2-4h-2l1 1c0 1 0 1-1 2v-4c-2-2-2-5-4-7-1-3-3-5-5-8-1 0-2 1-3 1s-2 0-3 1l-1-1c-1 3-2 5-2 7l-1 2h0c-2-2-4-3-6-4-3-2-5-2-8-3h-5c-1 0-1 1-2 0l-1 1c-2 1-4 1-6 3h-1l-1-1-5 4 4-8-2 1c0-3 6-8 8-11v-2s1-2 2-3c3-4 5-7 10-10 2-1 4-1 6-3z"></path><path d="M104 315c1 0 2 1 3 1h7l-1 1-3 1c-2 0-4 1-6 2l-1-1v-2l1-1v-1h0z" class="M"></path><path d="M139 316c-1-2-4-4-4-6h1l2 2h0c0-1-1-2-1-3l1 1c3 4 5 7 7 11v2l-6-7z" class="P"></path><path d="M139 316l6 7v-2c2 4 3 8 3 13v4c-1-1-1-2-1-2-1-3-1-5-2-7-1-5-4-9-6-13z" class="M"></path><path d="M117 300h3v1c-4 1-9 4-13 7-2 2-4 4-5 6 0 1 1 1 1 1h1 0v1l-1 1v2l1 1c-4 2-7 5-11 8l-2 1c0-3 6-8 8-11v-2s1-2 2-3c3-4 5-7 10-10 2-1 4-1 6-3z" class="P"></path><path d="M102 314c0 1 1 1 1 1h1 0c-2 2-3 3-5 4 0-2 2-3 3-5z" class="N"></path><path d="M113 317h1c-1 2-5 4-5 5-1 0 0 1 0 1-1 1-1 1-2 1 0 2 0 2-1 3h0-2c-2 2-7 2-10 5l-5 4 4-8c4-3 7-6 11-8 2-1 4-2 6-2l3-1z" class="g"></path><defs><linearGradient id="Bg" x1="118.169" y1="312.238" x2="126.684" y2="322.399" xlink:href="#B"><stop offset="0" stop-color="#2a292a"></stop><stop offset="1" stop-color="#656465"></stop></linearGradient></defs><path fill="url(#Bg)" d="M109 328l3-3c1-3 3-6 5-8l-1-2 4-6c2 0 4-1 6-1 2 1 4 0 6 1v1l-2-1-1 1c1 1 2 1 3 2 4 3 5 6 6 12 3 6 5 12 7 18h-2l1 1c0 1 0 1-1 2v-4c-2-2-2-5-4-7-1-3-3-5-5-8-1 0-2 1-3 1s-2 0-3 1l-1-1c-1 3-2 5-2 7l-1 2h0c-2-2-4-3-6-4-3-2-5-2-8-3h-5v-1h0c1-1 3 0 4 0z"></path><path d="M109 328c1 0 2-1 3-1l1-1h1l1 1c-1 0-1 1-2 1h-3v1h0-5v-1h0c1-1 3 0 4 0z" class="W"></path><defs><linearGradient id="Bh" x1="115.076" y1="320.064" x2="133.106" y2="330.069" xlink:href="#B"><stop offset="0" stop-color="#515050"></stop><stop offset="1" stop-color="#868686"></stop></linearGradient></defs><path fill="url(#Bh)" d="M113 328c2 0 3 1 5 1h-1l1 1h1l-1-1h1 0c1 0 2-1 3-2l3-3c0-1 1-3 1-3 1-2 2-3 3-4 2 0 2 0 4 1 1 1 1 4 2 5v2l1-1c1 1 1 0 2 0 3 6 5 12 7 18h-2l1 1c0 1 0 1-1 2v-4c-2-2-2-5-4-7-1-3-3-5-5-8-1 0-2 1-3 1s-2 0-3 1l-1-1c-1 3-2 5-2 7l-1 2h0c-2-2-4-3-6-4-3-2-5-2-8-3h0v-1h3z"></path><path d="M127 327l3-3c2 0 3 1 4 2-1 0-2 1-3 1s-2 0-3 1l-1-1z" class="N"></path><defs><linearGradient id="Bi" x1="144.906" y1="256.471" x2="164.612" y2="283.097" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#49494a"></stop></linearGradient></defs><path fill="url(#Bi)" d="M185 232l7 2h1-1-1 0l1 1c2 0 3 1 4 3 0 1-1 1-1 2s-1 2-1 2l-2 2c0 1-1 1-1 2l-1 3c-3 4-8 10-10 15l-10 23-1-4c-3 8-4 18-10 25h0-1c-2 1-4 3-5 5 0 1 1 1 1 2h0c0 1 1 2 1 3l-1 5h-2 0l-3-6c-2-4-3-9-6-12 0-1-1-1-1-2h1 0l-1-2c-2-1-3-3-5-3h-1v4 1 2c1 1 1 2 2 3v2l-1-1h0c-2-2-4-3-6-4-4-3-6-5-11-4v-1h-3c-4-1-7-3-11-4-2 1-3 1-4 2l-1-1c-1 2-3 4-4 6l-4 5h-1v-1c2-4 4-7 7-10 1-3 4-5 5-7 1-1 1-2 1-3l12-10c8-6 16-12 25-18 4-3 9-6 13-9 1-1 23-14 25-16l5-2z"></path><path d="M129 294l1 2-2 2h-2l-1-1c1-1 3-2 4-3z" class="X"></path><path d="M129 294l1-9v1c0 2 1 4 2 6h0v1 4h-1v1c-1-1-1-2-1-2l-1-2z" class="O"></path><path d="M106 296c0-1 2-2 2-3l2 1c4 2 9 4 14 3h1l1 1h2c-1 0-1 0-2 1-1 0-2 0-3 1h-3-3c-4-1-7-3-11-4z" class="B"></path><path d="M114 283c1-1 1-3 3-3h0l1 1v-1c0 2-2 3-2 4-2 2-6 7-6 10l-2-1c0 1-2 2-2 3-2 1-3 1-4 2l-1-1c2-2 4-5 6-7 3-2 6-4 7-7z" class="V"></path><path d="M162 279h-1l-1-1c1-1 1-4 3-6 1-2 4-5 6-7s4-3 6-5h0l-7 11-6 8z" class="G"></path><defs><linearGradient id="Bj" x1="124.319" y1="302.664" x2="135.739" y2="301.499" xlink:href="#B"><stop offset="0" stop-color="#3b3b3b"></stop><stop offset="1" stop-color="#646363"></stop></linearGradient></defs><path fill="url(#Bj)" d="M130 296s0 1 1 2v-1h1v-4-1c2 2 3 4 5 5v1h-1v4 1 2c1 1 1 2 2 3v2l-1-1h0c-2-2-4-3-6-4-4-3-6-5-11-4v-1h3c1-1 2-1 3-1 1-1 1-1 2-1l2-2z"></path><path d="M155 250c0 1 0 1-1 2-15 11-31 20-44 32l-3 3v1c0 1-2 2-2 3 1-1 9-8 9-8-1 3-4 5-7 7-2 2-4 5-6 7-1 2-3 4-4 6l-4 5h-1v-1c2-4 4-7 7-10 1-3 4-5 5-7 1-1 1-2 1-3l12-10c8-6 16-12 25-18 4-3 9-6 13-9z" class="g"></path><path d="M138 296h-1c-1-5 1-11 3-15 2-5 9-12 14-13 2-1 3 0 4 0 0 1 1 2 1 3v11l2-2v1l1-2 6-8c1 3 1 7 2 9l-1 3c-3 8-4 18-10 25h0-1c-2 1-4 3-5 5 0 1 1 1 1 2h0c0 1 1 2 1 3l-1 5h-2 0l-3-6c-2-4-3-9-6-12 0-1-1-1-1-2h1 0l-1-2c-2-1-3-3-5-3v-1l1-1z" class="W"></path><path d="M143 291c0 1 1 1 1 2-1 3-1 5-2 8-2-1-3-3-5-3v-1l1-1v-1h1l1 2h1l2-6z" class="Y"></path><defs><linearGradient id="Bk" x1="143.789" y1="277.873" x2="150.825" y2="285.292" xlink:href="#B"><stop offset="0" stop-color="#262526"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#Bk)" d="M139 295h0c0-7 1-13 6-18 1-2 8-9 11-9l1 1c0 3-1 4-3 6h0c-2 1-3 4-5 6s-5 7-6 10h0l-2 6h-1l-1-2z"></path><path d="M155 280c2-3 2-6 3-9h1v11c-1 2-2 3-3 4-5 8-8 14-6 24v1l1 3c1 0 1 0 2-1 0 1 1 1 1 2h0c0 1 1 2 1 3l-1 5h-2 0l-3-6c-2-4-3-9-6-12 0-1-1-1-1-2h1 0l1-1c1-4 1-7 3-11l1-1v-1c1-2 5-9 7-9z" class="c"></path><path d="M155 280c2-3 2-6 3-9h1v11c-1 2-2 3-3 4v-4-1c1-1 1-1 1-2h1v-3-2c0 2-1 5-2 6h-1 0z" class="E"></path><path d="M143 303l1-1c1-4 1-7 3-11l1-1v-1c1-2 5-9 7-9h0c-3 5-7 11-8 17-1 3-1 7-1 10l-3-4h0z" class="O"></path><path d="M168 271c1 3 1 7 2 9l-1 3c-3 8-4 18-10 25h0-1c-2 1-4 3-5 5-1 1-1 1-2 1l-1-3v-1c-2-10 1-16 6-24 1-1 2-2 3-4l2-2v1l1-2 6-8z" class="H"></path><path d="M159 282l2-2v1h0c-2 7-8 12-9 19-1 4-1 7-1 11h-1v-1c-2-10 1-16 6-24 1-1 2-2 3-4z" class="M"></path><path d="M220 68v-1c1 0 3 2 4 3 0 1 0 1 1 1v1c3 0 6 3 8 5l1 1h1l-3-4h1 0 1l15 16c1 1 2 3 3 4 1 0 2 1 3 1l8 14h0c2 0 2 1 3 2 0-1 0-1-1-1v-1-1c2 2 4 5 5 8l8 12 7 7-1 1-1-1-1 1c2 3 4 7 3 11v1h-1l4 10c3 6 5 13 12 16h1l1 1 1 1h0c-3 2-6 1-8 1h-3l-2 2h-1c-10 3-20 8-30 13-5 2-10 5-14 8l-5 3-9 5c-1 0-2 1-3 1l-2 2-8 3c0 1-3 1-3 2l-4 1c-2 2-5 3-7 4l-3 1c-2 1-4 2-6 4h-1v-1c-1 0-2 1-2 1-1 1-2 2-4 2l-1-1c0-1 1-2 1-3h1c1-1 2-3 3-4h0c1-2 2-4 2-5 0-3-1-5-3-6-6-4-13-5-19-4-5 0-10 3-13 7l-3 6-1-2c1-2 2-4 2-7v-4c-1-4-4-7-7-9-9-5-22-4-32-1l-3 1h0l-1-1c-1 1-3 1-4 2h0-1c-3 2-5 4-7 7h-1c4-7 9-11 15-16 5-3 10-7 15-9s10-3 14-5c2-2 5-4 7-6 5-4 10-8 14-14 6-8 8-16 6-26 0 1 1 2 1 3 1 2 3 3 4 5-1 0-1-1-2-1s0 1-1 1l9 9 3 2c6 0 11-2 16-4h1c2 0 4-2 5-3 1 0 2-1 3-1 2-1 9-8 10-10h2l1 1h2l3-3c-1-1 0-2 0-3 1 0 1-1 2-1 1-1 1-2 1-3 1 0 1 0 1-1v3 1h0c0 1 0 2-1 3 0 2 0 4-1 6v3 1l2 1c1-3 1-5 1-8 0-1 0-3 1-4h1 1v-12c1-1 1 0 2-1l-1-3v-1-5c1 0 1 0 2 2l-1 1c0 1 1 1 1 2 1 1 2 3 2 4v2c1 2 1 4 1 6h0v8 4c-1 1 0 1 0 2v5c1-5 2-9 2-14 0-11-1-19-5-29-2-4-3-8-6-12-2-3-6-6-9-9h-1 0c-1-2-2-5-4-7z" class="I"></path><path d="M265 108c2 2 4 5 5 8h-1l1 3-7-10c2 0 2 1 3 2 0-1 0-1-1-1v-1-1z" class="W"></path><path d="M174 136l9 9-2 1c-2-1-3-3-5-5-2 7-5 11-9 16a30.44 30.44 0 0 1-8 8c2-3 4-5 6-7 6-7 9-13 9-22z" class="P"></path><path d="M239 112c1 11 2 23-4 33v-1c-1-2 1-5 1-7 0-4 0-8 1-12v-12c1-1 1 0 2-1z" class="a"></path><defs><linearGradient id="Bl" x1="269.484" y1="115.576" x2="286.802" y2="146.372" xlink:href="#B"><stop offset="0" stop-color="#434242"></stop><stop offset="1" stop-color="#787879"></stop></linearGradient></defs><path fill="url(#Bl)" d="M270 119l-1-3h1l8 12 7 7-1 1-1-1-1 1c2 3 4 7 3 11v1h-1l-3-7c-1-3-2-6-3-8-3-5-6-9-8-14z"></path><path d="M233 137c1-3 1-5 1-8 0-1 0-3 1-4h1 1c-1 4-1 8-1 12 0 2-2 5-1 7v1c-3 7-11 15-18 18h0c1-2 1-4 2-6 6-5 11-13 14-20z" class="Q"></path><path d="M256 180c-2-1-6-1-8-1h0 10 3 1 4 6s0 1 1 1c2 0 4-1 6 0h1 2 0 1 2c1-1 0-1 1-1s2-1 3-1h1c1-1 0-1 0-2v-1-4c-1-3 0-6-1-8 0-1-1-2-1-3s0-1-1-2v-1-1c-1-1-2-3-2-4v-1l-1-1c0-1-1-3-1-4-1-2-2-3-2-5l3 7 4 10c3 6 5 13 12 16h1l1 1 1 1h0c-3 2-6 1-8 1h-3l-2 2h-1c-10 3-20 8-30 13-5 2-10 5-14 8l-5 3-9 5c-1 0-2 1-3 1-1 1-2 0-3 1h0c1-1 2-1 3-3h0l2-2c6-3 14-7 18-12 1-1 0-2 0-3 1-1 1-1 2-1 6-1 11-1 17-3 4-2 8-4 12-5l-11-1h-2-10z" class="h"></path><defs><linearGradient id="Bm" x1="240.169" y1="197.685" x2="229.314" y2="192.71" xlink:href="#B"><stop offset="0" stop-color="#7f7d7f"></stop><stop offset="1" stop-color="#a5a4a3"></stop></linearGradient></defs><path fill="url(#Bm)" d="M248 190h0c0 1 1 2 0 3-4 5-12 9-18 12l-2 2h0c-1 2-2 2-3 3h0c1-1 2 0 3-1l-2 2-8 3c0 1-3 1-3 2h-2c0-2-1-2-1-3 2-3 4-5 6-7 2-1 3-3 5-4l2-1c0-1 0-2 1-3l-1-1v-6c6 0 12-1 19-1 1 0 2 1 4 0z"></path><path d="M248 190h0c0 1 1 2 0 3-4 5-12 9-18 12h-1v-1c1-2 10-9 12-10 1-1 4-2 4-3l-1-1c1 0 2 1 4 0z" class="W"></path><path d="M226 198v-2l1-1c1 1 1 2 1 3-1 2-1 4-2 5l-1 1-3 6h0c2 0 4-2 6-3h0c-1 2-2 2-3 3h0c1-1 2 0 3-1l-2 2-8 3c0 1-3 1-3 2h-2c0-2-1-2-1-3 2-3 4-5 6-7 2-1 3-3 5-4l2-1c0-1 0-2 1-3z" class="C"></path><path d="M217 209l8-7v2l-3 6c-1 0-3 2-4 2-1-1-1-1-1-3z" class="h"></path><path d="M228 207h0c-1 2-2 2-3 3h0c1-1 2 0 3-1l-2 2-8 3h-2l-1-1v-1l2-3c0 2 0 2 1 3 1 0 3-2 4-2h0c2 0 4-2 6-3z" class="I"></path><defs><linearGradient id="Bn" x1="220.206" y1="146.217" x2="225.192" y2="166.535" xlink:href="#B"><stop offset="0" stop-color="#979596"></stop><stop offset="1" stop-color="#cccbcb"></stop></linearGradient></defs><path fill="url(#Bn)" d="M247 133v-12h2c2 1 5 3 6 5v1-2c3 6 4 13 4 20 1 3 2 9 5 11l-2 5-1 1h-4c-1 0-1-1-2-1v-2c-2 0-4 1-5 1-3 0-6 1-8 2l-15 4c-3 1-5 2-7 3h-1c-2 0-3 1-5 1s-3 1-5 1c-4 0-7 2-12 1l1 1h1 3c2 1 4 0 5 0s1 1 2 1h2v1c-3 0-5-1-7-1h-5s-1 0-2-1h-4c-7 0-14 1-21 2-5 0-9 1-14 1h2 1c1-1 3 0 4-1h3c1 0 1 0 2-1h2c1-1 3 0 4-1 1 0 3 1 5 0h4 8v-1h0c-1-1 0-1-1-2 0 0 0-1-1-1v-1h-6 0 8c6 2 12 0 18 0 1-1 1-1 2-1 4-1 8-3 11-5 5-3 10-8 14-13v2h0 0l2-1 1-2s1 0 1-1v-1h1v-2c1-1 1-1 1-2l1-1v-2c1-1 0 0 0-1l1-1v-2s0-1 1-2z"></path><path d="M224 162v1c3-1 5-3 7-4l1-1h1c1-1 2-2 4-3 1 0 2-2 4-3 1-1 3-2 5-3-1 1-3 3-4 5h0c0 1 0 1-1 2-2 1-3 2-5 3-2 2-5 3-8 4l-1-1-4 3h0 0c1 0 2-1 3-1l3-1h0c1 0 2-1 3-1l3-1 2-2 2-1h1 0c-1 1-3 3-5 4h-2c-1 1-3 2-4 2l-3 1c-1 0-1 0-2 1-1 0-6 2-8 1-2 0-3 0-5 1 1-1 1-1 2-1 4-1 8-3 11-5z" class="L"></path><path d="M253 144l1-2h1l1 1h1c1 1 2 1 2 2 1 3 2 9 5 11l-2 5h-1c-2-1-4-4-5-6h0v-3h0v-1c0-1 0-2-1-3 0-2 0-3-2-4z" class="Q"></path><defs><linearGradient id="Bo" x1="255.013" y1="124.613" x2="245.604" y2="139.572" xlink:href="#B"><stop offset="0" stop-color="#666566"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#Bo)" d="M247 133v-12h2c2 1 5 3 6 5v1-2c3 6 4 13 4 20 0-1-1-1-2-2h-1l-1-1h-1l-1 2v1h-1c0-1 0-1 1-2h0v-1c-1-1-1-2-3-2-1-1-2-1-3 0h0c-1 0-1-1-1-2 1-1 1-3 1-5h0z"></path><path d="M220 68v-1c1 0 3 2 4 3 0 1 0 1 1 1v1c3 0 6 3 8 5l1 1h1l-3-4h1 0 1l15 16c1 1 2 3 3 4 1 0 2 1 3 1l8 14c-1-1-2-1-3-2h0 0v2c0 1 2 5 3 6l1 1c1 0 2 1 2 2l1 1 2 4v1c2 4 3 7 4 11 1 2 1 4 2 6v1l-1-2-5 1h0c0 2 0 2-1 3h0l-2-8c-1-1 0-2-1-3v-1-1c0-1 0-1-1-2v-1l-1-3v2 1c1 1 1 2 1 3v1c1 1 0 1 1 2v4c1 1 1 3 1 5s-1 6-1 9v1l-1 3c-3-2-4-8-5-11 0-7-1-14-4-20v2-1c-1-2-4-4-6-5h-2v12c-1 1-1 2-1 2v2l-1 1c0 1 1 0 0 1v2l-1 1c0 1 0 1-1 2v2h-1v1c0 1-1 1-1 1l-1 2-2 1h0 0v-2l2-3s1-1 1-2c1-1 2-3 2-5 1-5 2-9 2-14 0-11-1-19-5-29-2-4-3-8-6-12-2-3-6-6-9-9h-1 0c-1-2-2-5-4-7z" class="C"></path><path d="M260 123c0-1-1-2 0-3v1h1c1 2 1 5 2 7h-1l-2-5z" class="f"></path><path d="M252 118v-2l5 5c1 0 2 1 3 2l2 5h1c1 3 2 8 2 11s0 8-1 12l1 2-1 3c-3-2-4-8-5-11 0-7-1-14-4-20l-2-2v-1-1h0c-1-1-1-2-1-3z" class="R"></path><path d="M252 118v-2l5 5c1 0 2 1 3 2l2 5h1c1 3 2 8 2 11s0 8-1 12h0c0-3-1-5-1-7-1-6-2-14-6-20-1-2-3-4-5-6z" class="a"></path><path d="M257 121c1 0 2 1 3 2l2 5h1c1 3 2 8 2 11l-1 3v-1 1c-1-8-3-15-7-21z" class="T"></path><defs><linearGradient id="Bp" x1="253.201" y1="104.816" x2="234.952" y2="114.836" xlink:href="#B"><stop offset="0" stop-color="#343434"></stop><stop offset="1" stop-color="#7a7a7a"></stop></linearGradient></defs><path fill="url(#Bp)" d="M220 68v-1c1 0 3 2 4 3 0 1 0 1 1 1v1c3 0 6 3 8 5l1 1h1c10 10 17 22 23 34l3 9h-1v-1c-1 1 0 2 0 3-1-1-2-2-3-2l-5-5v2c0 1 0 2 1 3h0v1 1l2 2v2-1c-1-2-4-4-6-5h-2v12c-1 1-1 2-1 2v2l-1 1c0 1 1 0 0 1v2l-1 1c0 1 0 1-1 2v2h-1v1c0 1-1 1-1 1l-1 2-2 1h0 0v-2l2-3s1-1 1-2c1-1 2-3 2-5 1-5 2-9 2-14 0-11-1-19-5-29-2-4-3-8-6-12-2-3-6-6-9-9h-1 0c-1-2-2-5-4-7z"></path><path d="M221 129h2l1 1h2l3-3c-1-1 0-2 0-3 1 0 1-1 2-1 1-1 1-2 1-3 1 0 1 0 1-1v3 1h0c0 1 0 2-1 3 0 2 0 4-1 6v3 1l2 1c-3 7-8 15-14 20-1 2-1 4-2 6h0c0 1-1 1-2 1-2 1-4 2-7 2h0c-5 2-10 1-16 0-2 0-3-1-4-2l-1-1c0-1 1-2 1-3l-2 1v-1h-1v-2h0 0c-2 2-5 7-7 7-1 1 0 0-1 0 1-2 3-3 4-4v-1c1 0 1-1 2-2v-1h0c1-2 2-2 3-3 0-1 1-1 0-2-2-1-4-1-7-2h0c-3 1-4 2-6 4-2 1-3 3-5 3h-1c4-5 7-9 9-16 2 2 3 4 5 5l2-1 3 2c6 0 11-2 16-4h1c2 0 4-2 5-3 1 0 2-1 3-1 2-1 9-8 10-10z" class="L"></path><path d="M186 158c1-1 2-1 3-1h1c-1 1-1 2-2 3l-2 1v-1-2z" class="h"></path><path d="M186 158c0-3 2-4 3-6h1s1 1 1 2-1 2-1 3h-1c-1 0-2 0-3 1z" class="b"></path><path d="M199 161v1c-2 2-4 3-7 4-2 0-3-1-4-2 1-1 5-1 6-1 2 0 3-1 5-2z" class="G"></path><path d="M202 143h1c2 0 4-2 5-3 1 0 2-1 3-1-2 3-5 4-8 6s-8 5-12 5h-5c-2-1-4-3-5-4l2-1 3 2c6 0 11-2 16-4z" class="T"></path><path d="M199 161h0l1-1h0c2-1 3-1 4-2s1-1 2-1h1c-2 2-4 3-5 4s-1 2 0 3h2l-1 1h4c1-1 2-1 2-2 2-1 5-3 7-4l1-2v1s0 1-1 2l-1 4c-2 1-4 2-7 2h0c-5 2-10 1-16 0 3-1 5-2 7-4v-1z" class="U"></path><path d="M208 166h-1c2-1 3-2 5-3 2 0 3-2 4-3h0l-1 4c-2 1-4 2-7 2z" class="H"></path><path d="M256 180h10 2l11 1c-4 1-8 3-12 5-6 2-11 2-17 3-1 0-1 0-2 1h0c-2 1-3 0-4 0-7 0-13 1-19 1v6l1 1c-1 1-1 2-1 3l-2 1c-2 1-3 3-5 4-2 2-4 4-6 7 0 1 1 1 1 3h2l-4 1c-2 2-5 3-7 4l-3 1c-2 1-4 2-6 4h-1v-1c-1 0-2 1-2 1-1 1-2 2-4 2l-1-1c0-1 1-2 1-3h1c1-1 2-3 3-4h0c1-2 2-4 2-5 0-3-1-5-3-6-6-4-13-5-19-4-5 0-10 3-13 7l-3 6-1-2c1-2 2-4 2-7v-4c-1-4-4-7-7-9-9-5-22-4-32-1l-3 1h0l-1-1c-1 1-3 1-4 2h0-1c3-3 6-5 10-7 28-13 60-10 90-10h47z" class="N"></path><path d="M266 180h2l11 1c-4 1-8 3-12 5-6 2-11 2-17 3-1 0-1 0-2 1h0c-5-4-13-4-19-4-2 0-5 0-8-1h-19c-5 0-10 0-15-2 13-1 73 4 80-1 1-1 2-1 4-1-3 0-3 0-5-1z" class="B"></path><path d="M268 180l11 1c-4 1-8 3-12 5-6 2-11 2-17 3-1 0-1 0-2 1h0c-5-4-13-4-19-4 3-1 7-1 11-1h16 3c1-1 1-1 2-1h1 2l3-1h1 1c2-1 2-1 3-2h0l-4-1z" class="H"></path><path d="M109 197c3-3 6-5 10-7v1c2 1 7 0 9 0h8c5 0 11 0 17 1 3 1 6 1 9 1 6 1 11 0 16 0h9c5 0 11 0 15 1 1 1 2 1 3 2h0v-3c1-1 1-1 2-1h1c1 0 4 0 5 1h3c1 1 1 1 1 2 1 1 1 1 1 2l1-1c0-1 0-2 2-2 1-1 1-1 2 0 0 1 1 1 1 2l1 1 1 1c-1 1-1 2-1 3l-2 1c-2 1-3 3-5 4-2 2-4 4-6 7 0 1 1 1 1 3h2l-4 1c-2 2-5 3-7 4l-3 1c-2 1-4 2-6 4h-1v-1c-1 0-2 1-2 1-1 1-2 2-4 2l-1-1c0-1 1-2 1-3h1c1-1 2-3 3-4h0c1-2 2-4 2-5 0-3-1-5-3-6-6-4-13-5-19-4-5 0-10 3-13 7l-3 6-1-2c1-2 2-4 2-7v-4c-1-4-4-7-7-9-9-5-22-4-32-1l-3 1h0l-1-1c-1 1-3 1-4 2h0-1z" class="R"></path><path d="M178 199c5-2 12 0 18 0 2 1 5 1 7 2 0 0 1 0 1 1l1 1c-1 2-3 4-5 6l-1-1 1-1c-1-1-1-2-2-2-2-1-3-2-5-3-3-1-7-3-11-3h-4z" class="K"></path><defs><linearGradient id="Bq" x1="181.778" y1="217.707" x2="171.695" y2="193.602" xlink:href="#B"><stop offset="0" stop-color="#bdbbbc"></stop><stop offset="1" stop-color="#e6e5e6"></stop></linearGradient></defs><path fill="url(#Bq)" d="M150 196c0-1 1-1 1-1l8 3c2 0 4 0 6 1 4 1 9 0 13 0h4c4 0 8 2 11 3 2 1 3 2 5 3 1 0 1 1 2 2l-1 1 1 1c-2 1-5 4-6 6 0-3-1-5-3-6-6-4-13-5-19-4-5 0-10 3-13 7l-3 6-1-2c1-2 2-4 2-7v-4c-1-4-4-7-7-9z"></path><path d="M205 203v-1c1-3 1-5 1-8l11 3c1 2 1 4 0 6s-4 4-5 6v1 1c-1 1-1 2-1 4 0 0 1 1 2 1h2l-4 1c-2 2-5 3-7 4l-3 1c-2 1-4 2-6 4h-1v-1c-1 0-2 1-2 1-1 1-2 2-4 2l-1-1c0-1 1-2 1-3h1c1-1 2-3 3-4h0c1-2 2-4 2-5 1-2 4-5 6-6 2-2 4-4 5-6z" class="b"></path><path d="M212 209v1 1c-1 1-1 2-1 4 0 0 1 1 2 1h2l-4 1c-2 2-5 3-7 4l-3 1c-2 1-4 2-6 4h-1v-1c1-1 2-3 4-4 4-4 9-8 14-12z" class="G"></path><path d="M201 222c2-2 4-6 7-6 1 1 1 1 2 1h1c-2 2-5 3-7 4l-3 1z" class="M"></path><defs><linearGradient id="Br" x1="440.128" y1="223.457" x2="554.136" y2="610.999" xlink:href="#B"><stop offset="0" stop-color="#393939"></stop><stop offset="1" stop-color="#5c5b5c"></stop></linearGradient></defs><path fill="url(#Br)" d="M435 551v-16-40-142c0-27-1-55 1-82 2-14 5-30 10-43 1-2 3-5 5-7 2-3 8-3 11-4 6 0 11-1 17-1l37-1h30 4v3c0 1 0 2-1 2-1 1-1 1-2 1h-2v-1h-9l-5 1-9 1c-2 0-4 0-5 1h-2v1l-1-1c-14 1-30 6-39 17-9 10-13 25-15 38-2 18-1 37-1 55v78 80c0 16-1 32 1 48 1 13 3 27 7 40 1 4 2 8 4 12 3 8 8 15 13 21 3 3 6 7 10 10 2 2 5 3 7 5l11 5h-4-1 0-12c-10-1-20-3-30-6-3-2-6-3-9-4-2-1-4-3-6-3-1-1-2-2-3-4-1-1-2-5-3-7v2 3h-1l-3-7c-4-8-6-19-6-28l3 1-2-28z"></path><path d="M449 448l1-1v1c0 2-1 2-3 3-1 1-1 1-2 0-1 0-1-1-2-1 1 0 0-1 1-1l2 1c1-1 2-2 3-2z" class="N"></path><path d="M452 377c-1-2-2-3-2-5-1-1-1-1-1-2-1-1 0-1 0-2v-1l2 1 1-4v13z" class="M"></path><path d="M454 422c1 4 0 9 0 13-1-1-1-2-2-3l-1-7h1c1 0 1 1 1 1v1h1v-5z" class="S"></path><path d="M452 425l-4-11v-1c1 1 2 3 3 4 1 2 2 3 3 4v1 5h-1v-1s0-1-1-1z" class="N"></path><path d="M441 602c2 2 1 4 3 6v2 3h-1l-3-7 1-1c1-1 0-2 0-3z" class="D"></path><path d="M449 458c1 0 0 0 1 1h0v1 3l-1 1h-1 2v1c-1 1-2 1-3 1-1-1-1 0-2-2 1 0 1-1 1-2 2-1 2-2 3-4z" class="g"></path><path d="M450 459v1 3l-1 1h-1c0-2 1-4 2-5z" class="R"></path><path d="M453 476l1 1-1 34c-1-3 0-12-2-14h-1v-1-1c1-1 1-1 2-3l1-10-1-1-2 1v-2h0l1-1c1-1 1-1 2-3z" class="N"></path><path d="M453 455v7 2l1 1v12l-1-1c-1 2-1 2-2 3l-1 1h0c-1-1-1-2-1-2 1-4 1-8 1-12v-1-1h-2 1l1-1c1-1 2-6 3-8z" class="S"></path><path d="M450 480v-1c0-2 1-4 2-5l1 2c-1 2-1 2-2 3l-1 1z" class="G"></path><defs><linearGradient id="Bs" x1="433.684" y1="601.783" x2="441.533" y2="581.087" xlink:href="#B"><stop offset="0" stop-color="#898a8b"></stop><stop offset="1" stop-color="#bab8b8"></stop></linearGradient></defs><path fill="url(#Bs)" d="M434 578l3 1 3 19c0 1 1 3 1 4s1 2 0 3l-1 1c-4-8-6-19-6-28z"></path><path d="M442 438c0-2 1-4 2-5s2-1 3-1c2 1 3 2 4 3 1 2 1 3 0 6v1h0c-2 2-5 4-8 4h0c2-1 3-2 4-3h-1c-1 0-2 0-4-1-1-1 0-3 0-4z" class="N"></path><path d="M443 436h2c-1 1-1 2-1 3l2 1c1 0 1 0 2-1l1-1h1v1l-3 3h-4c-1-2 0-2-1-4l1-2z" class="G"></path><path d="M449 435c1 1 1 2 1 3h0-1l-1 1c-1 1-1 1-2 1l-2-1c0-1 0-2 1-3l1-1h3z" class="N"></path><path d="M442 438c0-2 1-4 2-5s2-1 3-1c2 1 3 2 4 3 1 2 1 3 0 6l-1-2v-1h0c0-1 0-2-1-3h-3l-1 1h-2l-1 2h0z" class="g"></path><path d="M443 436l3-3c2 1 2 1 3 2h-3l-1 1h-2z" class="W"></path><path d="M451 435v-1c0-4-1-9-3-13 0-1-1-1-2-1v4h-1c-1-1-1-2-1-3 0-2 1-2 2-3 1 0 2 0 3 1 1 2 1 4 2 6l1 7c1 1 1 2 2 3v30l-1-1v-2-7c-1 2-2 7-3 8v-3-1h0c-1-1 0-1-1-1 1-5 2-10 2-15v-1h0v-1c1-3 1-4 0-6z" class="d"></path><path d="M450 459c1-3 1-5 2-8h1v1 3c-1 2-2 7-3 8v-3-1h0z" class="M"></path><path d="M452 364l1-16h0l1 73c-1-1-2-2-3-4 1-1 0-3 0-4l-1-9-3-2c-1-1-1-1 0-2 1 0 1-1 3-1v-1c0-3 1-9 0-12-1-1-1-2 0-4h1 1v-5-13z" class="P"></path><path d="M444 608v-2h0c1 3 4 4 6 5l4 4c1 0 1 1 2 1v-1h1l2 2 1 1c2 1 3 2 5 3 10 6 21 7 33 6-4-4-9-7-12-12-1-1-3-2-3-3h1c3 3 6 7 10 10 2 2 5 3 7 5l11 5h-4-1 0-12c-10-1-20-3-30-6-3-2-6-3-9-4-2-1-4-3-6-3-1-1-2-2-3-4-1-1-2-5-3-7z" class="D"></path><path d="M447 615c1 0 2 0 3 1 3 2 7 5 11 7 7 3 16 5 23 6l12 1h5l1-1h-1v-1l-2-2h0 0c1 1 1 1 2 1l11 5h-4-1 0-12c-10-1-20-3-30-6-3-2-6-3-9-4-2-1-4-3-6-3-1-1-2-2-3-4z" class="Q"></path><defs><linearGradient id="Bt" x1="389.24" y1="154.409" x2="427.509" y2="279.9" xlink:href="#B"><stop offset="0" stop-color="#100f10"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#Bt)" d="M313 179c6-1 13-2 19-2 7-1 15-2 23-1l31 1 78-2c2 2 7 0 9 1h4c5 0 10 1 15 1h14c2 0 4 1 6 1h1 3 6c1 1 2 1 3 1h-1c3 1 6 1 9 1 1 1 3 2 4 3 3 1 5 2 8 4 1 2 3 3 3 6 1 1 2 3 2 5l-1 1c0-1-1-1-2-1s-2-1-3-2h-1c1 2 4 4 5 6 0 2 2 8 2 9l1 1 1 2h-1l-1-1h-1-6-5-7l-56 1c-4 0-10 1-14 1-4 1-9 1-11 3-2 1-3 2-3 3l-2 2-1 1c-1 3-3 8-3 11-1-1-1-2-2-3h0l-2-1c0-1 1-1 1-2h0c0-2 1-3 1-5-2-1-3-1-4 0h-1v-2c-1 0-2 0-2 1l1 1h-2-1l-2-1h-1c-1-1-3-1-4 0h-4-8-4-3l-14 1-2 2 1 4-1 1c-2 1-3 5-5 7-3 5-8 11-12 16-1 0-2 1-3 1h-1l-1 1-3-7h0l-2-3s-1-3-2-3l-3-4c-3-4-6-7-10-9l-1-1c-5-3-14-6-20-6 0 0-1 0-2-1h-3c-3-1-8-1-12-2-1 0-3 0-5-1-1 0-3 0-4 1-1 0-3 3-4 4l-4 4h1v1h0c-1 3-2 5-3 7-2 6-1 12-1 18h0 1v-1c-1 6-2 12-1 17 0 1 0 2 1 3v1c0 2 1 3 2 5 0 1 1 2 0 3h0c0 1 0 2 1 3h0c1 2 1 5 2 7v6c0 3 2 7 3 9 2 4 3 8 4 12-1 0-1-1-2-1l-1-2c0-1-1-2-1-3l-3-5-3-7c-4-10-7-21-9-32l-1-10-2-19c0-1 0-1 1-2v-3c-1-1-1-4 0-5 0-6 1-10 2-15h0v-3l-1-1c-1 3-1 5-1 7h-1v-1c1-4 1-12 0-16s-3-5-6-7l-1-1h-1-3v-1h-3c-1 1-3 1-5 1l-1-1c10-5 20-10 30-13h1l2-2h3c2 0 5 1 8-1 2 0 4 1 6 1v1h3l1 1z"></path><path d="M437 189c2-2 5-4 8-5-2 1-4 3-6 5h-2zm-49 4c-1-1-3-1-4-2l-6-3c2 0 4 0 6 1l3 1h-2v1c2 1 3 1 3 2z" class="N"></path><path d="M392 193h2c0-1 0-3-1-4l-2-3c2 1 3 2 4 3 1 2 1 3 2 4l-1 2h-1-1c0-1-1-1-2-1v-1z" class="d"></path><path d="M388 193c0-1-1-1-3-2v-1h2c1 1 3 2 5 3v1h-2l-1 1c-1 0-1 1-2 1h-1c0-1-1-2-2-3l4 1h0v-1z" class="P"></path><path d="M447 188c2-1 4-1 6-1 0 1-5 3-7 3l-3 3h2l1-1h1 2c-2 1-4 2-6 2h-1c0-1 1-1 1-2v-1c1-1 3-2 4-3z" class="d"></path><path d="M281 259v-3-5 4l1-1v-1l1 8v5 1 2h-1 0l-1-10z" class="P"></path><path d="M485 194c-5-1-11-2-15-5 5 1 11 3 16 3 1 1 2 1 3 1l3 1h0c-2 0-5-1-7-1v1z" class="S"></path><path d="M290 213c3-2 9-1 12-1h3c1 0 2-1 3-2 2-1 4-3 7-4h0 0c-1 1-1 2-2 2-1 1-2 1-2 2 0 0 1-1 2-1 0 1-3 1-3 2 1 0 2 0 3 1v1h-23z" class="Z"></path><path d="M408 194v-1-2h1c1 2 2 2 4 3 1 1 2 1 3 1h-1 0c1 1 1 1 1 2v1l-6-1v-1c-1 0-2-1-2-2z" class="f"></path><path d="M340 195l3 1h0c-1 0-2 1-3 1v1l1 1h-1v1h-3c-2-1-3 0-5 0h-3l11-5z" class="G"></path><path d="M289 220c0 2 1 5 1 7-1 1-2 1-4 2-2 3-1 6-1 9l-1 1c0-2 1-6 0-8 1-4 3-7 5-11z" class="N"></path><path d="M280 230c0 3 0 9 1 12l1 11v1l-1 1v-4 5 3l-2-19c0-1 0-1 1-2v-3c-1-1-1-4 0-5z" class="M"></path><path d="M492 194h3v-2l12 3c-2 1-4 0-5 0s-1 0-1 1c-1-1-2-1-3-1h-8c1 0 1 0 2 1h0-4v-1c-1 0-2-1-3-1v-1c2 0 5 1 7 1z" class="f"></path><path d="M422 193c1-2 3-4 4-6h0c0 1-1 4-2 5l-1 1v1l-2 2c1 0 1 0 2-1h1l4-4s0-2 1-2c0 2 0 3-1 4l1 1h1 0c-2 0-2 1-4 2h-3c-1 1-2 1-3 0h0c1-1 1-2 2-3h0z" class="P"></path><path d="M437 179c1 0 6 0 7 1h-7 0 7l-29 1-11 1h-4c-1-1-1-1-2-1l39-2z" class="N"></path><path d="M536 206l1-1c2 0 3 1 5 1 2 2 4 4 5 7h3-1-6v-1c-2-2-4-3-6-5l-1-1z" class="B"></path><path d="M397 193c1 0 2 0 3 1l1-1s1 0 1 1c1 1 2 1 2 2l1-1h1 0c1 0 1 1 2 1v1c-1 0-2 0-3 1 0-1 0-1-1-1-1-1-1-1-2-1h-1c-1-1-1-1-2-1l-2-1v2c-1 1-3 1-4 1h0l-1 1h-2l-2-2 1-1 1-1h2c1 0 2 0 2 1h1 1l1-2z" class="M"></path><defs><linearGradient id="Bu" x1="379.954" y1="189.526" x2="382.941" y2="199.303" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#444344"></stop></linearGradient></defs><path fill="url(#Bu)" d="M370 191c6-1 9-1 14 2 1 1 2 2 2 3h1c1 0 1-1 2-1l-1 1h0c0 1 0 3-1 3 0 1-1 1-2 1 0-1-1-2-2-3-1-2-3-4-5-5-1 0-2 0-3-1h0-5z"></path><path d="M422 193h0c-1 1-1 2-2 3h0c1 1 2 1 3 0h3c2-1 2-2 4-2 1 1 2 1 3 2 1 0 1-1 2-1l2 2-1 1h-1c0-1-1-2-2-2s-1 1-2 0c-1 0-2 1-3 1 0 1 1 1 1 2s0 1-1 1c-2 0-3 0-5-1-2 0-2 0-4-1h-3v-1c0-1 0-1-1-2h0 1c2-1 4-2 6-2z" class="G"></path><path d="M423 196h3c-1 1-2 3-3 3h-1c0-1 1-2 1-3z" class="V"></path><path d="M449 192l2-1c5-1 10 0 15 0-2 0-7 1-9 0h-3c-1 0-1 0-2 1h3v1h2c1 0 2 0 3-1h6c-4 2-9 0-13 3v2h4l-1 1-1 1c-1 0-3 0-4 1l-1 1-1-1s1-1 1-2v-2c1-1 2 0 2-2h-7c-2 2-3 4-5 6h0l-2-1h0c2-1 3-3 5-5 2 0 4-1 6-2z" class="f"></path><path d="M536 206c-3-2-7-4-10-6-3-1-7-3-10-6 3 1 6 2 8 3h1c1 0 1 0 2 1l1-1h-1c2 0 3 1 4 2 4 2 9 4 11 7-2 0-3-1-5-1l-1 1z" class="G"></path><path d="M294 198c1-2 3-3 4-4 3-2 5-4 8-5 4-2 10-4 13-7l3-4c1 0 2 1 3 1h1 0c2 0 3 0 5 1h1c-2 0-4 1-6 1-4 2-8 4-11 6-7 3-14 6-19 11h-2z" class="N"></path><defs><linearGradient id="Bv" x1="437.813" y1="188.726" x2="437.534" y2="197.357" xlink:href="#B"><stop offset="0" stop-color="#1f1f1f"></stop><stop offset="1" stop-color="#393839"></stop></linearGradient></defs><path fill="url(#Bv)" d="M441 189c1-1 2-1 3-2 2 0 2 0 3 1-1 1-3 2-4 3v1c0 1-1 1-1 2h1c-2 2-3 4-5 5h0l-1 2-1-1v-2l1-1-2-2c-1 0-1 1-2 1-1-1-2-1-3-2h0c1-1 1-1 3-1l4-4h2 2z"></path><path d="M441 189c1-1 2-1 3-2 2 0 2 0 3 1-1 1-3 2-4 3s-2 1-3 2v-1c0-1 1-1 2-2l-1-1z" class="N"></path><path d="M294 198h2c-4 5-8 10-10 15l-3 15c-1 2-2 4-2 7-1 2 0 5 0 7-1-3-1-9-1-12 0-6 1-10 2-15 1-2 1-3 2-4l3-6h0 1l-1 3c1-1 2-4 3-6 2-1 3-3 4-4z" class="d"></path><path d="M498 195c1 0 2 0 3 1 0-1 0-1 1-1s3 1 5 0c4 2 9 4 14 6 6 2 11 4 15 8v1l-1-1-1 1 1 1 1-1v1c1 0 2 0 3 1h2c-1-2-3-3-4-5 2 2 4 3 6 5v1h-5c-11-7-22-12-34-16 0 0-2 0-2-1l-4-1z" class="T"></path><path d="M329 200h3c2 0 3-1 5 0-1 1-2 1-3 2l1 2-1 1h0c-2 0-3 0-4 1-4 0-7 1-10 3-3 1-5 2-7 3-1-1-2-1-3-1 0-1 3-1 3-2-1 0-2 1-2 1 0-1 1-1 2-2 1 0 1-1 2-2h0l14-6z" class="H"></path><defs><linearGradient id="Bw" x1="517.86" y1="196.37" x2="520.243" y2="212.801" xlink:href="#B"><stop offset="0" stop-color="#7f7f80"></stop><stop offset="1" stop-color="#9c9c9c"></stop></linearGradient></defs><path fill="url(#Bw)" d="M504 197c12 4 23 9 34 16h-7v-1h-3l-4-2c-4-2-9-5-15-5h0v-1l1-1c-1 0-1-1-2-1l-2-1c0-1 0-1-1-2s0-1-1-2z"></path><path d="M303 176c2 0 4 1 6 1v1h3l1 1c-3 0-6 0-9 1-6 2-10 8-13 13-2 3-3 6-5 9 0 1-2 3-2 5-1 2-1 3-2 5l-1-1 3-6v-3c0-3 1-6 3-9l1-3 1-1 1-2 2-2c0-1 2-3 3-4 1 0 1-1 2-1h0c0-1 0-1 1-2-3 0-5 0-8 1l2-2h3c2 0 5 1 8-1z" class="P"></path><defs><linearGradient id="Bx" x1="362.443" y1="191.343" x2="362.839" y2="199.095" xlink:href="#B"><stop offset="0" stop-color="#2c2c2c"></stop><stop offset="1" stop-color="#515151"></stop></linearGradient></defs><path fill="url(#Bx)" d="M340 195c4-2 9-2 14-3 1-1 2-1 3-1h0 13 5 0c1 1 2 1 3 1 2 1 4 3 5 5 1 1 2 2 2 3l1 1c-2 1-2 1-3 2h-1v-1h-1v2l1 1c-1 0-2 0-2 1h0c-1-1-1-2-1-3-1 0-1 1-2 1-1 1-2 1-3 1v-1s1 0 1-1c1 0 1 0 2-1l-2-4h2c-1 0-1-1-2-1l1-1c-4-3-10-3-15-3h-7c-4 1-8 2-11 4v-1h0l-3-1z"></path><path d="M376 196c2 1 4 2 4 4v1c-2 0-3-2-3-3-1 0-1-1-2-1l1-1z" class="M"></path><path d="M513 178h3 6c1 1 2 1 3 1h-1c3 1 6 1 9 1 1 1 3 2 4 3 3 1 5 2 8 4 1 2 3 3 3 6 1 1 2 3 2 5l-1 1c0-1-1-1-2-1s-2-1-3-2h-1-1c-6-5-13-8-20-12l-6-3h0 3v-1c-2-1-4-2-6-2z" class="T"></path><path d="M537 183c3 1 5 2 8 4 1 2 3 3 3 6v1c0 1 1 1 1 2l-3-2c-1-1-3 0-4-1s-1-4-3-4v2h-1l1-1c0-2-1-5-2-7z" class="P"></path><path d="M524 179c3 1 6 1 9 1 1 1 3 2 4 3 1 2 2 5 2 7l-1 1-16-9h0c0-1 1-2 2-3z" class="g"></path><path d="M406 195c-1-1-2-3-3-4l1-1c1 2 2 3 3 4h1c0 1 1 2 2 2v1l6 1h3-1v1h1 1c-1 2-1 2-1 3h-1v1 2 3l-1 1-2-2-1-2c-1 1-1 1 0 2v1c0 1 0 1-1 2-1-1-1-1-1-2h-1c0 1-1 2-1 3v2c0-2 0-2-1-3h-1l-1-1-1-2h-2c-1-1-2-1-4-1h-4c0-1-1-1-1-2-1-1-1-3-2-4-1 0-1 0-1 1l-2 1v-3-1h2l1-1h0c1 0 3 0 4-1v-2l2 1c1 0 1 0 2 1h1c1 0 1 0 2 1 1 0 1 0 1 1 1-1 2-1 3-1v-1c-1 0-1-1-2-1h0z" class="B"></path><path d="M405 200l2 1 1 1-1 1c-1 1-1 1-2 1l-1-1c0-1 0-2 1-3z" class="J"></path><path d="M398 198l1-1c1 1 2 1 2 2s-1 1-2 2h-1c-1 0-1-1-2-1 1-2 1-2 2-2z" class="I"></path><path d="M411 198c1 0 3 1 4 0h2v2l-1 1h-1-2l-2-2v-1z" class="G"></path><path d="M414 205l1-1c1 0 1 0 2 1v-1c0-1 0-1-1-1v-1-1l2 1v1 2 3l-1 1-2-2-1-2z" class="J"></path><path d="M390 198h2l1-1h0v3l2-1v1c0 2 1 3 2 4 2 0 1-1 3 0v1 1h-4c0-1-1-1-1-2-1-1-1-3-2-4-1 0-1 0-1 1l-2 1v-3-1z" class="G"></path><path d="M406 195c-1-1-2-3-3-4l1-1c1 2 2 3 3 4h1c0 1 1 2 2 2v1h-1c1 1 1 1 2 1v1l-1 1 1 2c-1 2-1 2-2 3h-1l-1-2 1-1-1-1-2-1h0v-1h-1-1c0-1-1-2-2-3h1c1 0 1 0 2 1 1 0 1 0 1 1 1-1 2-1 3-1v-1c-1 0-1-1-2-1h0z" class="W"></path><path d="M388 196l2 2v1 3l2-1c0-1 0-1 1-1 1 1 1 3 2 4 0 1 1 1 1 2h4c2 0 3 0 4 1h2l1 2c-1-1-1-1-2-1s-1-1-1-1h-1-2 0c-1 0-2 0-3 1h0c0 1-1 1-1 2 0 2 0 4 1 5 1 0 3 1 4 1v-1l2 3h1c1 1 2 3 4 3 0 1 1 2 2 2h-4-3l-14 1-2 2-1-1-1-3h0v-1-3-2c-1-1-1-4-2-5v-2l2-1c0-1-1-2-1-3s0-2 1-4l-1-1c1 0 2 0 2-1 1 0 1-2 1-3h0z" class="c"></path><path d="M404 218h1c1 1 2 3 4 3 0 1 1 2 2 2h-4-3l2-2h0-4-3-1c-1-1-1-1-2-1v-1l1-1h6 1z" class="B"></path><path d="M391 213c1 1 1 2 2 3 1 0 2-1 3-1 0-1 0-3 1-4v-1c0 2 0 4 1 5h-1l-1 1c0 1 1 1 0 2h0c-1 0-2 0-3 1v-1h-3c0 1 1 1 1 2v2l-2-1c0-1 1-1 1-2 0 0-1 0-1-1-1 0-1-2-1-3v-4 3h2l1-1z" class="F"></path><defs><linearGradient id="By" x1="389.697" y1="220.389" x2="401.444" y2="226.423" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#737373"></stop></linearGradient></defs><path fill="url(#By)" d="M402 221h4 0l-2 2-14 1-2 2-1-1 2-3v-1l2 1h5c1 0 1 1 2 0h5 1v-1h-2z"></path><path d="M386 210c1-1 1-1 1-2 0 1 0 2 1 3h0v4c0 1 0 3 1 3 0 1 1 1 1 1 0 1-1 1-1 2v1l-2 3-1-3h0v-1-3-2c-1-1-1-4-2-5v-2h2v1z" class="R"></path><path d="M386 222l1-2 2 2-2 3-1-3h0z" class="B"></path><path d="M386 210c1-1 1-1 1-2 0 1 0 2 1 3h0v4l-2-4v-1z" class="H"></path><path d="M388 196l2 2v1 3l2-1c0-1 0-1 1-1 1 1 1 3 2 4 0 1 1 1 1 2h4c2 0 3 0 4 1h2l1 2c-1-1-1-1-2-1s-1-1-1-1h-1-2 0c-1 0-2 0-3 1h0c0 1-1 1-1 2v1c-1 1-1 3-1 4-1 0-2 1-3 1-1-1-1-2-2-3l-1 1h-2v-3h0c-1-1-1-2-1-3 0 1 0 1-1 2v-1h-2l2-1c0-1-1-2-1-3s0-2 1-4l-1-1c1 0 2 0 2-1 1 0 1-2 1-3h0z" class="X"></path><path d="M391 211c1 0 1 0 2-1 0 2 0 2-2 3l-1 1v-3h1z" class="D"></path><path d="M395 204c0 1 1 1 1 2 0 2 0 1 1 2-1 1-2 2-3 2l-1-2c1-2 1-3 2-4z" class="B"></path><path d="M388 196l2 2v1 3 2h1c1 1 1 1 1 2h-1v1 4h-1v3h-2v-3h0c-1-1-1-2-1-3 0 1 0 1-1 2v-1h-2l2-1c0-1-1-2-1-3s0-2 1-4l-1-1c1 0 2 0 2-1 1 0 1-2 1-3h0z" class="Q"></path><path d="M388 211v-1c0-1 1-3 2-4h1c0 2 0 2-1 4v1 3h-2v-3h0z" class="e"></path><path d="M388 196l2 2v1h-1c-1 2-1 4-1 6-1 1-1 2-1 3s0 1-1 2v-1h-2l2-1c0-1-1-2-1-3s0-2 1-4l-1-1c1 0 2 0 2-1 1 0 1-2 1-3h0z" class="B"></path><defs><linearGradient id="Bz" x1="280.029" y1="183.315" x2="291.019" y2="206.208" xlink:href="#B"><stop offset="0" stop-color="#222122"></stop><stop offset="1" stop-color="#555455"></stop></linearGradient></defs><path fill="url(#Bz)" d="M290 179c3-1 5-1 8-1-1 1-1 1-1 2h0c-1 0-1 1-2 1-1 1-3 3-3 4l-2 2-1 2-1 1-1 3c-2 3-3 6-3 9v3l-3 6c-1 3-1 5-1 7h-1v-1c1-4 1-12 0-16s-3-5-6-7l-1-1h-1-3v-1h-3c-1 1-3 1-5 1l-1-1c10-5 20-10 30-13h1z"></path><path d="M259 192c10-5 20-10 30-13 1 1 1 1 1 2h0l-1 2h0v1l-3 3h-2c-2 1-5 1-7 1s-3 0-5 1h-1c-3 0-5 1-7 2l-1 1-3 1-1-1zm84 5c3-2 7-3 11-4h7c5 0 11 0 15 3l-1 1c1 0 1 1 2 1h-2l2 4c-1 1-1 1-2 1 0 1-1 1-1 1v1c-2 1-4 3-6 4-2 0-4 2-7 2h-1l-4-4c0-2-2-2-2-4l1-1v-1l-1-1-1-1-3-1v2c1 0 2 1 2 1l-1 2c-1-1-2-1-3-1h-2l-1-1h-1-4v-1-1h1l-1-1v-1c1 0 2-1 3-1v1z" class="N"></path><path d="M342 197c3 1 11 0 14-1-2 1-3 2-3 3l-3-1h0c-3-1-6 0-9 0v-1h1z" class="V"></path><path d="M340 199h1l-1-1v-1c1 0 2-1 3-1v1h-1-1v1c3 0 6-1 9 0h0v2c-1-1-3 0-4-1h-6z" class="W"></path><path d="M340 199h6c1 1 3 0 4 1 1 0 2 1 2 1l-1 2c-1-1-2-1-3-1h-2l-1-1h-1-4v-1-1z" class="S"></path><path d="M365 196c3 0 7 0 10 1 1 0 1 1 2 1h-2-1l-1 2-1 1-2-2h-1c-1 0-1-1-2-2-1 0-2-1-2-1z" class="V"></path><path d="M356 196c2-1 6-1 9 0 0 0 1 1 2 1 1 1 1 2 2 2 0 1 0 2-2 2h0c-2-1-3-3-4-3l-2-1h-4c-2 1-2 2-3 3l-1-1c0-1 1-2 3-3z" class="a"></path><path d="M367 197c1 1 1 2 2 2 0 1 0 2-2 2h0c0-1 0-1-1-2v-1l1-1z" class="R"></path><path d="M369 199h1l2 2 1-1 1-2h1l2 4c-1 1-1 1-2 1 0 1-1 1-1 1v1c-2 1-4 3-6 4-2 0-4 2-7 2 1-1 1-2 2-3l2-4-1-1h0l1-1c0-2-1-2-2-4 1 0 2 2 4 3h0c2 0 2-1 2-2z" class="X"></path><path d="M363 198c1 0 2 2 4 3h0c0 1 1 1 1 1 0 1-1 1-1 1 1 1 1 0 1 2l-5 3 2-4-1-1h0l1-1c0-2-1-2-2-4z" class="F"></path><path d="M354 200c1-1 1-2 3-3h4l2 1c1 2 2 2 2 4l-1 1h0l1 1-2 4c-1 1-1 2-2 3h-1l-4-4c0-2-2-2-2-4l1-1v-1l-1-1z" class="X"></path><path d="M355 201c2 0 8 2 10 1l-1 1h0-3c0 1 0 1-1 2h-2v-1c-2 0-2-1-3-2v-1z" class="a"></path><path d="M364 203l1 1-2 4c-1 1-1 2-2 3h-1l-4-4h3c2 0 4-2 5-4z" class="Q"></path><path d="M354 200c1-1 1-2 3-3h4l2 1c1 2 2 2 2 4-2 1-8-1-10-1l-1-1z" class="P"></path><path d="M350 198l3 1 1 1 1 1v1l-1 1c0 2 2 2 2 4l4 4h1c3 0 5-2 7-2 0 1 0 1-1 2l-1 1h-2c-1 1-3 1-4 1h-1-25-17-4v-1c2-1 4-2 7-3 3-2 6-3 10-3 1-1 2-1 4-1h0l1-1-1-2c1-1 2-1 3-2h3v1h4 1l1 1h2c1 0 2 0 3 1l1-2s-1-1-2-1v-2z" class="R"></path><path d="M343 202l1-1h1l1 1 1 1c0 1-1 1 0 2 0 1 0 2 1 3v3h-4l-1-1h1c-1-2-1-3-1-5l-1-1c1 0 1 0 3-1l-2-1z" class="B"></path><path d="M345 203l1 1h-2v1c1 2 1 3 0 5-1-2-1-3-1-5l-1-1c1 0 1 0 3-1z" class="a"></path><path d="M337 200h3v1h4 1-1l-1 1 2 1c-2 1-2 1-3 1-2 1-4 1-6 1l-1-1h0l-1-2c1-1 2-1 3-2z" class="P"></path><path d="M340 201h4 1-1l-1 1h-3l-1-1h1z" class="M"></path><path d="M337 200h3v1h-1c-1 2-2 2-4 3h0l-1-2c1-1 2-1 3-2z" class="N"></path><path d="M350 198l3 1 1 1 1 1v1l-1 1c0 2 2 2 2 4l4 4c-4 0-8 1-11 0h-1v-3c-1-1-1-2-1-3-1-1 0-1 0-2l-1-1h2c1 0 2 0 3 1l1-2s-1-1-2-1v-2z" class="B"></path><path d="M352 201l2 1h0c0 2 0 3-1 4 0 1 0 1-1 1-2 1-3 2-3 4h-1v-3c-1-1-1-2-1-3-1-1 0-1 0-2l-1-1h2c1 0 2 0 3 1l1-2z" class="R"></path><path d="M347 203c1 1 2 2 2 3 1 0 2-2 3-3v4c-2 1-3 2-3 4h-1v-3c-1-1-1-2-1-3-1-1 0-1 0-2z" class="Z"></path><path d="M335 204h0l1 1v2c1 1 1 2 1 3 1 0 1 0 1 1h-1l1 1c-3 2-17 0-21 1h-4v-1c2-1 4-2 7-3 3-2 6-3 10-3 1-1 2-1 4-1h0l1-1z" class="N"></path><path d="M335 204h0l1 1v2c1 1 1 2 1 3 1 0 1 0 1 1h-1c-2-2-4-4-7-5 1-1 2-1 4-1h0l1-1z" class="B"></path><defs><linearGradient id="CA" x1="491.145" y1="216.91" x2="488.488" y2="201.88" xlink:href="#B"><stop offset="0" stop-color="#494848"></stop><stop offset="1" stop-color="#757475"></stop></linearGradient></defs><path fill="url(#CA)" d="M488 196h4 0c-1-1-1-1-2-1h8l4 1c0 1 2 1 2 1 1 1 0 1 1 2s1 1 1 2l2 1c1 0 1 1 2 1l-1 1v1h0c6 0 11 3 15 5l4 2h3v1l-56 1c-4 0-10 1-14 1h-2c-2-1-3 0-4-1v-1h-1-1v-3c1 0 1-1 2-1h-3s-1-1-2-1v-2l-1-1c0-1 1-1 2-2 0-1 0-1 1-2v-1h-1c1-1 3-1 4-1l1-1 1-1c2 0 4 0 6 1l2-2h1c1-1 3-1 5-1 5 0 11 1 17 1z"></path><path d="M524 210l4 2c-1 0-1 0-2 1-2 0-3-1-4-1l2-2z" class="M"></path><path d="M488 207h1c1 0 2 0 3 1v2h-2c-1-1-1-1-2-3z" class="Q"></path><path d="M477 206l2-2h0v2h0v4h-3v-1l1-3zm19-2l1-1c1 0 1 0 1 1s1 2 0 3l-1 1c-1 0-1 0-2-1-1 0 0-1 0-2l1-1z" class="D"></path><path d="M495 205l1-1h1l1 1c-1 1-2 2-3 2s0-1 0-2z" class="E"></path><path d="M508 202c1 0 1 1 2 1l-1 1v1h0c-2 1-3 3-5 4-2 2-3 2-6 2h0l3-1h0l1-1v-1c1 0 2-1 3-1 0-1 1-1 2-2 0-1 1-2 1-3z" class="X"></path><path d="M474 202h2c2 0 2 1 4 2h1l-2 2v-2h0l-2 2-1 3-2 2c-1 0-1 0-1-1 1 0 1-1 2-2-2-1 0-2-1-3v-3z" class="Y"></path><path d="M475 208v-3l1-1 1 2-1 3-2 2c-1 0-1 0-1-1 1 0 1-1 2-2z" class="B"></path><path d="M509 205c6 0 11 3 15 5l-2 2-7-2c-3-1-5-2-9-1-1 1-1 0-2 0 2-1 3-3 5-4z" class="P"></path><path d="M485 203c0-2 1-2 2-3l1 2c3 0 5 0 8-1 1 1 1 1 2 1h3 0l2 2c2 0 3 1 4 1-1 1-2 1-2 2-2-1-3-1-4-1l-2-2h-1c0-1 0-1-1-1l-1 1-1 1c-1 0-2 1-3 1s-3-2-4-3h0-3 0 0z" class="W"></path><path d="M485 199c2-1 5-1 7-1 3 1 8 3 11 1l3 2 2 1c0 1-1 2-1 3-1 0-2-1-4-1l-2-2h0-3c-1 0-1 0-2-1-3 1-5 1-8 1l-1-2-2-1z" class="S"></path><path d="M490 200c2-1 3 0 4 0v1h-4v-1h0z" class="P"></path><path d="M468 206c1 0 1 0 2-1 1-2 2-2 4-3v3c1 1-1 2 1 3-1 1-1 2-2 2l-2 2c-1 0-3-1-5 0 0-1-1-1-2-1-1-1-3-4-4-5l1-1v1c1 1 1 1 2 1h0c1 1 5 1 6 1l1-1c-1 0-1 0-2-1z" class="F"></path><path d="M451 200c1-1 3-1 4-1-1 1-1 2 0 3l1 2 1 1c2 0 2-2 3-1l1 1-1 1c1 1 3 4 4 5 1 0 2 0 2 1h0 0c-2 0-3 0-4-1h-2l1 1 1 1h2l-1 1h0c-1-1-1-1-2-1l-2 2c-2-1-3 0-4-1v-1h-1-1v-3c1 0 1-1 2-1h-3s-1-1-2-1v-2l-1-1c0-1 1-1 2-2 0-1 0-1 1-2v-1h-1z" class="B"></path><path d="M452 201c2 1 3 3 4 4v1h-2l-1-1-2-2c0-1 0-1 1-2z" class="W"></path><path d="M449 205c0-1 1-1 2-2l2 2 1 1 2 1-1 2h-3s-1-1-2-1v-2l-1-1z" class="X"></path><path d="M449 205c0-1 1-1 2-2l2 2v1h-3l-1-1z" class="C"></path><path d="M456 207c0 2 1 3 2 4s2 2 3 2l-2 2c-2-1-3 0-4-1v-1h-1-1v-3c1 0 1-1 2-1l1-2z" class="J"></path><path d="M456 207c0 2 1 3 2 4-2 0-3 1-4 2h-1v-3c1 0 1-1 2-1l1-2z" class="U"></path><path d="M488 196h4 0c-1-1-1-1-2-1h8l4 1c0 1 2 1 2 1 1 1 0 1 1 2s1 1 1 2l-3-2c-3 2-8 0-11-1-2 0-5 0-7 1l2 1c-1 1-2 1-2 3h0 0 3 0v2l-1 1c-1 1-2 1-4 2-1-1-1-2-2-3v-2 1h-1c-2-1-2-2-4-2h-2c-2 1-3 1-4 3-1 1-1 1-2 1 1 1 1 1 2 1l-1 1c-1 0-5 0-6-1h0c-1 0-1 0-2-1v-1l-1-1c-1-1-1 1-3 1l-1-1-1-2c-1-1-1-2 0-3l1-1 1-1c2 0 4 0 6 1l2-2h1c1-1 3-1 5-1 5 0 11 1 17 1z" class="R"></path><path d="M485 203h0l1 1c-1 1-1 1-1 2h-1v-2l1-1z" class="O"></path><path d="M468 202c1-1 2-2 4-2-1 2-1 2-2 3-1 0-1 0-2-1z" class="Y"></path><path d="M485 203h-2v-1c0-1 1-2 2-3l2 1c-1 1-2 1-2 3z" class="f"></path><path d="M502 196c0 1 2 1 2 1 1 1 0 1 1 2s1 1 1 2l-3-2c-1 0-2-1-3-1h-1v-1l3-1z" class="Y"></path><path d="M468 196c2 0 3 1 5 2v1 1h-1c-2 0-3 1-4 2h-2l-2-1v-2c1-1 2-2 4-3z" class="M"></path><path d="M471 195c5 0 11 1 17 1-1 1-2 0-2 1-1 0-3 3-4 3-4 2-3-2-6-3h-2c-1-1-2-1-3-2z" class="V"></path><path d="M457 197c2 0 4 0 6 1l2-2h1 1 1c-2 1-3 2-4 3v2l2 1 1 1v3h1c1 1 1 1 2 1l-1 1c-1 0-5 0-6-1h0c-1 0-1 0-2-1v-1l-1-1c-1-1-1 1-3 1l-1-1-1-2c-1-1-1-2 0-3l1-1 1-1z" class="D"></path><path d="M457 197c2 0 4 0 6 1l2-2h1 1 1c-2 1-3 2-4 3v2l2 1 1 1v3c-2-1-3-3-4-4v-1h-1c0 1 0 1-1 1l-1-1-1-1c-2-1-2-1-3-2l1-1z" class="B"></path><path d="M440 200c2-2 3-4 5-6h7c0 2-1 1-2 2v2c0 1-1 2-1 2l1 1 1-1h1v1c-1 1-1 1-1 2-1 1-2 1-2 2l1 1v2c1 0 2 1 2 1h3c-1 0-1 1-2 1v3h1 1v1c1 1 2 0 4 1h2c-4 1-9 1-11 3-2 1-3 2-3 3l-2 2-1 1c-1 3-3 8-3 11-1-1-1-2-2-3h0l-2-1c0-1 1-1 1-2h0c0-2 1-3 1-5-2-1-3-1-4 0h-1v-2c-1 0-2 0-2 1l1 1h-2-1l-2-1h-1c-1-1-3-1-4 0h-4-8c-1 0-2-1-2-2-2 0-3-2-4-3h-1l-2-3v1c-1 0-3-1-4-1-1-1-1-3-1-5 0-1 1-1 1-2h0c1-1 2-1 3-1h0 2 1s0 1 1 1 1 0 2 1l1 1h1c1 1 1 1 1 3v-2c0-1 1-2 1-3h1c0 1 0 1 1 2 1-1 1-1 1-2v-1c-1-1-1-1 0-2l1 2 2 2 1-1v-3-2-1h1c0-1 0-1 1-3h-1-1v-1h1c2 1 2 1 4 1 2 1 3 1 5 1 1 0 1 0 1-1s-1-1-1-2c1 0 2-1 3-1 1 1 1 0 2 0s2 1 2 2h1v2l1 1 1-2 2 1h0z" class="Z"></path><path d="M408 217h3v1l-2 2-1-3z" class="Q"></path><path d="M414 212h1c1 1 1 2 1 3h-2v-3z" class="B"></path><path d="M425 215h-2v-1l1-2c1 1 1 2 1 3h0z" class="F"></path><path d="M436 218c0 1 0 2-1 3-1-1-2-1-3-1l-1 1h-1v-1h0l2-2h4z" class="H"></path><path d="M421 209v-2s-1 0-1-1l1-1 4 1c-1 0-1 1-2 1 0 1-1 1-1 2s0 2-1 3l-1 1-1-1c0-1 1-2 2-3z" class="O"></path><path d="M430 220c-1-1-2-1-3-1h-2 0c2-2 5-2 7-2h2c0-1 1-2 2-2h0l1 1-1 2h-4l-2 2h0z" class="J"></path><path d="M416 218h1l2-1 1 1c1 1 3 0 4 0v1l-1 1-2 1c-1 0-1 0-1-1h-1c-1 1-3 1-5 0v-1l2-1z" class="K"></path><path d="M439 224v-1h-2l-2-2c1-1 5 0 6 0h1l1 1-3 2 1 1h1v1l-4 3h0c0-2 1-3 1-5z" class="B"></path><path d="M441 205h0c1 3 1 6 1 9v3l-1 1h-1c-1-2-1-5-1-7s1-3 1-5l1-1z" class="L"></path><path d="M452 210h1v3c-1 0-1 1-2 2-2-1-3-1-5-1-1-1-1-3-1-4h1 3 3z" class="J"></path><path d="M446 210h3 3v1l-1 1h-1c-1 0-1 1-2 1h0-2v-3z" class="I"></path><path d="M443 222c1 0 1 1 2 1l-1 1c-1 3-3 8-3 11-1-1-1-2-2-3h0l-2-1c0-1 1-1 1-2l4-3v-1h-1l-1-1 3-2z" class="F"></path><path d="M443 222c1 0 1 1 2 1l-1 1h0c-1 0-2 0-3 1l-1-1 3-2z" class="E"></path><path d="M438 199l2 1h0v5h1l-1 1c0 2-1 3-1 5h-3v-3c-1-1 0-3-1-4h-1l-2-1c2 0 3-1 4-2h1 0l1-2z" class="Y"></path><path d="M438 199l2 1h0v5c-1 0-2 0-3-1s0-2 0-3h0l1-2z" class="G"></path><path d="M398 208c2 0 3-1 4 0h1c1 1 1 1 2 3 0 0 1 1 1 2h1l2-2v1c0 1 0 1-1 2v2h2l1-1v3-1h-3-1-1c0 1 1 1 2 2 0 1 0 1 1 2-2 0-3-2-4-3h-1l-2-3v1c-1 0-3-1-4-1-1-1-1-3-1-5 0-1 1-1 1-2h0z" class="E"></path><path d="M398 208c2 0 2 0 3 1 0 1 2 2 2 3h-2l-1 1c1 1 2 1 2 2v1c-1 0-3-1-4-1-1-1-1-3-1-5 0-1 1-1 1-2z" class="I"></path><path d="M398 208c2 0 2 0 3 1-2 2-2 2-3 4v2c-1-1-1-3-1-5 0-1 1-1 1-2z" class="e"></path><path d="M434 204h1c1 1 0 3 1 4 0 2 0 3-2 5-1 1-2 1-3 1v1c-2 0-4 1-6 0h0c0-1 0-2-1-3 1-2 2-3 3-5l1-1 1-1 1 1 2-1h1l1-1z" class="K"></path><path d="M426 213l2-2c1 0 1 1 2 1l-1 2c-2-1-2-1-3-1z" class="I"></path><path d="M425 215c0-1 0-1 1-2 1 0 1 0 3 1l1-2 1 3c-2 0-4 1-6 0h0z" class="U"></path><path d="M434 204h1c1 1 0 3 1 4 0 2 0 3-2 5-1 1-2 1-3 1 1-1 2-2 2-3-1-1-3-2-4-3 0-1 0-1-1-2l1-1 1 1 2-1h1l1-1z" class="D"></path><path d="M419 198c2 1 2 1 4 1 2 1 3 1 5 1 1 0 1 0 1-1s-1-1-1-2c1 0 2-1 3-1 1 1 1 0 2 0s2 1 2 2h1v2l1 1h0-1c-1 1-2 2-4 2l2 1-1 1h-1l-2 1-1-1-1 1-1 1c-2-1-2 0-4 0 1 0 1-1 2-1l-4-1-1 1c0 1 1 1 1 1v2h-1c-1-1-1-1-2-1v-3-2-1h1c0-1 0-1 1-3h-1-1v-1h1z" class="F"></path><path d="M425 206c0-1 1-1 1-1v-1h4l2 1-2 1-1-1-1 1-1 1c-2-1-2 0-4 0 1 0 1-1 2-1z" class="X"></path><path d="M435 198h1v2l1 1h0-1c-1 1-2 2-4 2-1 0-2 0-3-1v-1c1 0 2 0 2 1h1c1-2 2-3 3-4h0z" class="R"></path><path d="M440 200c2-2 3-4 5-6h7c0 2-1 1-2 2v2c0 1-1 2-1 2l1 1 1-1h1v1c-1 1-1 1-1 2-1 1-2 1-2 2l1 1v2c1 0 2 1 2 1h3c-1 0-1 1-2 1h-1-3-3-1c0-2 1-2 0-4v2c-1 1-1 1-2 1h0c-1-1-1-1 0-2l-1-1-1-1h0-1v-5z" class="B"></path><path d="M442 202v-2l2-2 2 1c1-1 1-2 3-3v1l-1 1c0 1 0 2-1 2h0v2l1 1h-2 0-1v-1-1h-1l-1 2-1-1z" class="D"></path><path d="M442 202l1 1 1-2h1v1 1h1c-1 2 0 2 0 4 2 1 2 1 4 1 1 0 2 1 2 1h3c-1 0-1 1-2 1h-1-3-3-1c0-2 1-2 0-4-1-1-1-2-2-3l-1-1z" class="H"></path><path d="M451 200h1v1c-1 1-1 1-1 2-1 1-2 1-2 2l1 1v2c-2 0-2 0-4-1 0-2-1-2 0-4h0 2l-1-1c1 0 2 0 3-1l1-1z" class="O"></path><path d="M451 200h1v1c-1 1-1 1-1 2-1 1-2 1-2 2h-1v-2h-2 2l-1-1c1 0 2 0 3-1l1-1z" class="H"></path><defs><linearGradient id="CB" x1="336.9" y1="209.379" x2="330.83" y2="240.352" xlink:href="#B"><stop offset="0" stop-color="#353435"></stop><stop offset="1" stop-color="#4f4e4f"></stop></linearGradient></defs><path fill="url(#CB)" d="M382 205l-1-1v-2h1v1h1c1-1 1-1 3-2-1 2-1 3-1 4s1 2 1 3l-2 1v2c1 1 1 4 2 5v2 3 1h0l1 3 1 1 1 4-1 1c-2 1-3 5-5 7-3 5-8 11-12 16-1 0-2 1-3 1h-1l-1 1-3-7h0l-2-3s-1-3-2-3l-3-4c-3-4-6-7-10-9l-1-1c-5-3-14-6-20-6 0 0-1 0-2-1h-3c-3-1-8-1-12-2-1 0-3 0-5-1l-2-1c-3 0-8 1-11 0v-4-1h23 4 17 25 1c1 0 3 0 4-1h2l1-1c1-1 1-1 1-2 2-1 4-3 6-4 1 0 2 0 3-1 1 0 1-1 2-1 0 1 0 2 1 3h0c0-1 1-1 2-1z"></path><path d="M359 243l2-1c2 1 3 3 5 5l-2-1c-2-1-2-1-3 0 0 0-1-3-2-3z" class="O"></path><path d="M379 217l4 1h1c0 3-1 6-3 9-3 6-7 14-13 19h-1c0-2 3-3 4-5 0 0 0-1 1-2v-1h1 0c0-2 1-2 1-4h0l2-2 1 1h0v-1-1l-1 1h0v-2h2l-1-1v-1h1v-2h1v-2c1-1 1-1 1-2v-1s0-1 1-2v-1l-2-1z" class="Y"></path><path d="M301 218c6 0 13 1 19 2 3 0 5 0 7 1 4 0 8 2 12 4 1 0 3 1 4 2h1-1c4 3 8 5 11 8 1 1 2 2 2 3v1c-3-4-6-7-10-9l-1-1c-5-3-14-6-20-6 0 0-1 0-2-1h-3c-3-1-8-1-12-2-1 0-3 0-5-1l-2-1z" class="S"></path><path d="M290 213h23 4 17 25c6 0 14 0 20 1 1 1 3 1 4 2v2l-4-1c-2-1-4-1-5-1-13-1-26-1-38-1h-31-10c-2 0-4 0-5-1v-1z" class="h"></path><path d="M382 205l-1-1v-2h1v1h1c1-1 1-1 3-2-1 2-1 3-1 4s1 2 1 3l-2 1v2c1 1 1 4 2 5v2 3 1h0l1 3 1 1 1 4-1 1c-2 1-3 5-5 7-3 5-8 11-12 16-1 0-2 1-3 1h-1l-1 1-3-7h0l-2-3c1-1 1-1 3 0l2 1h0c1 0 1 0 1-1h1c6-5 10-13 13-19 2-3 3-6 3-9h-1v-2c-1-1-3-1-4-2-6-1-14-1-20-1h1c1 0 3 0 4-1h2l1-1c1-1 1-1 1-2 2-1 4-3 6-4 1 0 2 0 3-1 1 0 1-1 2-1 0 1 0 2 1 3h0c0-1 1-1 2-1z" class="X"></path><path d="M374 205c1 0 2 0 3-1 1 0 1-1 2-1 0 1 0 2 1 3-2 0-4 2-6 2-1 0-4 1-5 2v1l-3 1 1-1c1-1 1-1 1-2 2-1 4-3 6-4z" class="B"></path><path d="M382 205l-1-1v-2h1v1h1c1-1 1-1 3-2-1 2-1 3-1 4s1 2 1 3l-2 1v2 2h-3v-1c-1-1-1-1-3-1l-1 1-7-1c1-1 3-1 5-2 2 0 4-1 6-2 1 0 1 0 1-1v-1z" class="D"></path><path d="M363 249c1 0 2 0 3 1h2c3-1 5-5 7-8 3-4 5-8 7-12 2-2 3-6 4-8l1 3 1 1 1 4-1 1c-2 1-3 5-5 7-3 5-8 11-12 16-1 0-2 1-3 1h-1l-1 1-3-7z" class="Q"></path><path d="M383 95c-3 0-5 1-8 1 3-2 6-2 9-3 2-1 5-1 8-1 5-1 9-3 12-7 4-5 9-20 8-26v-3c1-6 4-11 5-16 1-1 1-3 3-4 4 9 7 18 7 28 0 1 1 4 0 5l2 10h0l1 9v6c1 8 3 15 5 23 1 3 2 6 4 9 2 5 6 9 10 13h0c2 3 5 5 8 7v-3c2 1 1 3 2 4 2 1 12 7 14 7v-1c2 0 3 1 5 1 4 1 7 2 10 3 9 2 21 1 31-1h0l2-1 9-1c1 0 2-1 3-1 4-2 7-4 10-7 3-5 8-9 11-14 3-3 4-6 6-9 2-2 2-4 3-6l2-3v-2h0v-5c-1-1 0-1 0-2l-1-1v-1-1c0-2 2-3 3-4l4-1c4-2 7-4 11-4 0-2 1-3 2-4l1-2c-1 0-3 2-5 2l-17 8c-4 2-7 2-10 3l-1-1c10-4 20-9 30-14 3-2 8-3 11-6 3-2 6-5 9-8 6-5 12-10 18-14 4-3 8-5 13-6h1l-1 1c-1 4-7 7-9 10-7 7-11 17-12 26-1 3-1 6-1 8s1 4 1 6c0 1-1 2-1 3h0-1-1l2 5h0c1 1 4 6 4 7s-1 2-1 3c1 0 1 0 1 1s2 2 2 3c3 4 5 8 8 11 1 0 2 2 2 2 4 3 7 6 11 9 2 1 3 3 6 4h1 3l1 1c-1 1-1 2-2 4v1c1 3 5 4 5 7 0 1-1 2-2 3h-2l-2 1c-1-1-2 0-3 0-3 0-8 1-10-1l-1 1c-4-1-8-2-11-4l-6-3-2-2s-1-1-2-1h-1l-2-2h-2v1l-2-2-3-4-1 1-3-2c-3 2-2 5-4 7-1 1-1 2 0 3 0 1-1 4-2 5-1 2-3 4-4 5l-1 1h-4l-3-1c-2-1-2-1-4-3 0 2 2 3 3 4-3-2-5-4-7-7-1 0-1 1-2 1 0 1 0 2-1 2 0 2-1 3-1 4l-1 1c-1 1-3 2-5 2-2 1-3 0-5 0 1 1 2 2 2 3h1c0 1 0 1 1 1h2v1h1c2 1 4 1 5 2h-3l-6 1-6-2c-1 1-2 1-2 1l-1 4c1 1 2 2 2 4-2-1-3-2-6-2-1 0-1 0-1 1-3-2-5-3-8-4-1-1-3-2-4-3-3 0-6 0-9-1h1c-1 0-2 0-3-1h-6-3-1c-2 0-4-1-6-1h-14c-5 0-10-1-15-1h-4c-2-1-7 1-9-1l-78 2-31-1c-8-1-16 0-23 1-6 0-13 1-19 2l-1-1h-3v-1c-2 0-4-1-6-1h0l-1-1-1-1h-1c-7-3-9-10-12-16l-4-10h1v-1c1-4-1-8-3-11l1-1 1 1 8 8c2 2 4 3 5 6 1 0 2 1 2 1 0 1-1 1-1 2 2 1 3 2 4 3h0c2 0 3 1 4 1 5 2 11 4 17 4h1 0c-4-1-9-3-12-5v-1c-2-1-5-2-7-3 0-2-1-2-3-3-1-1-4-3-5-5h1 0c1 1 1 1 2 1l4 4h1l2 1 2 2 2 1c1 0 2 1 3 1 4 1 9 3 13 3h9 6c4 0 8-1 12-2 2 0 5 0 8-1v1c2-1 3-2 5-2v1h2c0-1 0-1 1-1v1l4-2c1-1 3-1 4-2 2-1 4-3 6-4 8-6 15-15 19-25 1-2 2-5 2-7-1-1-1 0-2 0-3 1-5 2-8 3l-4 1c-1 0-1-1-1-1 2-1 3-2 5-3 1-1 1-3 1-5-1-4-5-7-8-10-2-1-4-2-5-4z" class="b"></path><path d="M407 163c1-1 3-1 4-1 3 0 5 1 8 2 4 2 8 3 12 5l-6-1-2-1-3-1h-2c-1 0-2 0-3-1s-2-1-4-1c-1 0-3 0-4-1z" class="O"></path><path d="M414 103c0-2 1-5 1-7 0-6 0-11 1-17h1v19l-1 10c0 5 0 11-1 16v-12c0-3 1-7-1-9z" class="Q"></path><path d="M405 157c1-1 2-1 3-1s1 1 2 1v1l-1 1c-2 0-5 1-7 2-8 3-16 4-23 5-3 0-5 0-8 1h-8-3c2-1 7-1 9-1h4c1-1 3 0 4 0 2-1 3 0 4-1h2 1c1 0 2 0 3-1h1 2c1-1 1-1 2-1h3c1-1 3-1 5-2 1-1 4-3 5-4z" class="I"></path><path d="M399 157c0-1-1-1-1-2l2-2h1l1-1c1 0 4-1 5-1v3c1 0 3-1 4-2 1 0 1 1 2 2-1 1-2 3-3 4v-1c-1 0-1-1-2-1s-2 0-3 1v-1h-1-1-1l3-3-1 1c-2 1-3 2-5 3z" class="C"></path><path d="M521 166h2c5 0 11-2 16-6 1-1 3-2 5-3h0c-3 5-9 10-15 11-3 0-5 0-8-2z" class="M"></path><path d="M521 166s-1 0-1-1v-1c1 0 4 0 5-1 6-1 10-3 15-6l3-3 1-1h0c1 1 1 2 1 3l-1 1h0c-2 1-4 2-5 3-5 4-11 6-16 6h-2z" class="D"></path><path d="M374 152c-2 3-7 5-9 6-3 1-5 2-7 3h0v1c1-1 3-1 4-2l3-1 4-2h1c1-1 1-1 2-1 1-1 2-1 2-1 3-3 7-4 10-6 2-1 3-2 5-3h0l2-1 1-1c1 0 1 0 2-1 0 1 0 2-1 3 0 1-4 2-5 3l-10 5c-8 4-15 7-24 9l-1-1c3-1 6-2 8-3l9-5 4-2z" class="I"></path><defs><linearGradient id="CC" x1="565.015" y1="131.057" x2="548.773" y2="137.834" xlink:href="#B"><stop offset="0" stop-color="#747774"></stop><stop offset="1" stop-color="#9e999d"></stop></linearGradient></defs><path fill="url(#CC)" d="M547 145c3-3 6-7 8-10l9-11 5-8 12-18c1-1 2-4 4-5-1 3-4 6-5 9l-8 11c-3 5-5 10-8 14-2 3-4 6-7 9-2 4-5 8-9 10v1l-1-2z"></path><path d="M383 95c1 0 3 0 4-1 2 0 5 0 8-1h2 0 2l1-1c2 0 4-2 5-3 0 0 3-3 4-3 0-1 0 0 0 0 0 1-1 3-1 4l-1 1h0c-2 0-2 1-3 2-1 0-1 0-1 1s0 1-1 2h0c0 1-1 2-1 3h-2-6c-1-1-3-1-4-1l-1 1c-2-1-4-2-5-4z" class="L"></path><path d="M545 177c5-2 6-7 9-12h0c1 2 2 6 2 8 1 1 2 2 2 3h1c0 1 0 1 1 1h2v1h1c2 1 4 1 5 2h-3l-6 1-6-2c-3 0-5-1-8-2z" class="C"></path><path d="M557 177h0c1 0 2 0 2 1 1 0 2 0 2 1 1 0 1 0 1 1h-1c-2 0-3-1-4-2h0 1v-1h-1z" class="b"></path><path d="M425 168l-52 2c1-1 8-1 10-1l24-6c1 1 3 1 4 1 2 0 3 0 4 1s2 1 3 1h2l3 1 2 1z" class="X"></path><defs><linearGradient id="CD" x1="361.482" y1="156.71" x2="362.199" y2="164.47" xlink:href="#B"><stop offset="0" stop-color="#201d1e"></stop><stop offset="1" stop-color="#3a3a3b"></stop></linearGradient></defs><path fill="url(#CD)" d="M378 154h0c1 0 2 1 3 0 0 0 1 0 1-1h2l1-1v1c-2 1-2 1-3 2l-9 5c-2 1-5 1-7 3l-1 1-2 1c-11 1-21 3-32 1h0 11v-1-1l11-2 1 1c9-2 16-5 24-9z"></path><path d="M342 164l11-2 1 1c-4 2-8 3-12 3v-1-1z" class="L"></path><path d="M394 143c0-1 0-1 1-2 1-3 4-6 6-9 2-4 4-9 5-14 1 1-1 6-1 7h0c1 0 1-1 1-2v-1c1-1 1-2 1-2h1v1c-1 5-4 9-6 14 2-2 3-4 5-7h0c0-1 1-1 1-2v-1-1l1-1c0-2 1-3 1-5 1 0 1 0 1-1v-2c1 0 0 0 0-1l1-1v-1c0-2 0-1 1-2l-1-2c1-1 1-1 1-3v-2c1-1 1-6 1-7v-2c1-1 1-2 1-3l-1-3c1-1 1-4 1-6h0v-6c1-1 1-2 1-4v2h1v-1l1 2c-1 1-1 3-1 4h-1c-1 6-1 11-1 17 0 2-1 5-1 7 2 2 1 6 1 9-1 1-1 3-1 4l-1 6-2 3c-1 3-1 5-3 7v-1l1-2c-1 0-1 1-2 1-2 3-4 5-6 8-2 2-4 3-7 5z" class="C"></path><path d="M410 122c1 1 1 2 1 3-1 3-1 5-3 7v-1l1-2c-1 0-1 1-2 1l3-8z" class="f"></path><path d="M413 114l1 2-1 6-2 3c0-1 0-2-1-3l3-8z" class="T"></path><path d="M414 103c2 2 1 6 1 9-1 1-1 3-1 4l-1-2v-4l1-7z" class="B"></path><path d="M427 163c-1 0-2-1-3-2-2-1-4-3-5-5-1-3-1-8-1-12v-15-18c1-1 0-4 1-6v-2l1-1v-3c-1-2-1-5-1-6s1-2 0-3v-5c-1-2 0-4 0-6-1-2 0-3-1-5v-5-1l1-2c0 3-1 6 0 9v-6l2 6c0 1 1 2 0 3l-1 14 1 35 1 18c0 4 0 7 1 11 1 2 2 4 5 6l-1 1z" class="I"></path><path d="M419 75v-6l2 6c0 1 1 2 0 3l-1 14c-1-5-1-12-1-17z" class="G"></path><defs><linearGradient id="CE" x1="285.854" y1="157.677" x2="297.529" y2="157.279" xlink:href="#B"><stop offset="0" stop-color="#8d8c8d"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#CE)" d="M285 148v-1c1-4-1-8-3-11l1-1 1 1 8 8c2 2 4 3 5 6 1 0 2 1 2 1 0 1-1 1-1 2 0 4 3 7 5 9l1 1h-1l-3-3h0l-2-2v-1c0-1 0-1-1-1v-1-4h0v2 4c0 1 1 1 1 2s1 1 2 2c0 1 0 1-1 2-1-1-1-2-2-2v3l1 6s0 2 1 2c1 1 2 2 3 2 0-1-1-1-1-3 2 1 1 3 3 3h0c0 1-1 1-1 2l-1-1-1-1h-1c-7-3-9-10-12-16l-4-10h1z"></path><defs><linearGradient id="CF" x1="282.405" y1="137.583" x2="288.523" y2="140.304" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#8b8989"></stop></linearGradient></defs><path fill="url(#CF)" d="M285 148v-1c1-4-1-8-3-11l1-1 1 1 8 8c2 2 4 3 5 6l-1-1c-1-1-2-1-2-2h-1c0-1-2-3-3-3h-1-1 0v1h1v1c0 1-1 2-1 3l-1 1-1-1c-1 0-1 0-1-1z"></path><defs><linearGradient id="CG" x1="417.663" y1="128.845" x2="407.201" y2="152.888" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#adacac"></stop></linearGradient></defs><path fill="url(#CG)" d="M414 116c0-1 0-3 1-4v12c0 10 1 20-2 30-1-1-1-2-2-2-1 1-3 2-4 2v-3c-1 0-4 1-5 1l-1 1h-1l-2 2c0 1 1 1 1 2h0c-5 2-9 5-15 5 0 0-1 0-2 1h-1 0-1c-2-1-4 0-6 0-2 1-5 2-7 2h-4l2-1c3 0 7-1 10-2 12-2 21-10 31-16l1-1h-1c-1 0-3 1-5 1 1-2 3-2 4-3 4-3 7-8 8-13v-8l1-6z"></path><path d="M406 146l1-1h-1c-1 0-3 1-5 1 1-2 3-2 4-3 4-3 7-8 8-13v4c0 3-1 5-3 7-1 0-2 1-2 1l-3 2c3 0 5-2 8-2-2 1-5 3-7 4z" class="F"></path><path d="M420 36c4 9 7 18 7 28 0 1 1 4 0 5l2 10h0l1 9v6c-1 0-1 1-2 2h0c0-4-2-8-1-11l-1-3h-1v4c0-1 0-2-1-3s0 1 0-1c-1-1-1-2-2-3l-1-1c1-1 0-2 0-3l-2-6v6c-1-3 0-6 0-9l-1-11 1-1c2-4 1-8 1-12v-6z" class="O"></path><path d="M420 42c2 5 3 9 5 14-2 0-4-1-7-1l1-1c2-4 1-8 1-12z" class="L"></path><path d="M427 85h0c1-4 0-8 0-12-1-2-1-4 0-5v1l2 10h0l1 9v6c-1 0-1 1-2 2h0c0-4-2-8-1-11zm-9-30c3 0 5 1 7 1 1 2 0 4 0 6-1 2-1 3-1 5 0 5 2 10 2 15h-1v4c0-1 0-2-1-3s0 1 0-1c-1-1-1-2-2-3l-1-1c1-1 0-2 0-3l-2-6v6c-1-3 0-6 0-9l-1-11z" class="K"></path><defs><linearGradient id="CH" x1="442.76" y1="146.819" x2="446.218" y2="166.259" xlink:href="#B"><stop offset="0" stop-color="#b5b3b5"></stop><stop offset="1" stop-color="#dfe0df"></stop></linearGradient></defs><path fill="url(#CH)" d="M430 149c0-1 0-2 1-2 1-2 1-5 1-7 3 3 7 8 11 10 5 4 10 8 16 11l4 2h-3v1h1c1 0 1 1 2 1h0 2c1 1 3 1 4 1-3 0-5 0-7 1h-2c0-1 0-1 1-1h2l-1-1h-2-2c-1-1-1 0-2-1-2 0-4 1-6 0-1-1-2 0-3-1-2 0-3-1-5-1-1 0-1 0-1-1-1 0-3 0-4-1h-2c1 1 1 2 2 3 0 1-1 2-1 2l1 1v1h-2c-1 0-2-1-3-1v-1-8c1 0 1-1 2-1h0l1-1c-2-1-3-4-4-6h-1z"></path><path d="M430 149c0-1 0-2 1-2 1-2 1-5 1-7 3 3 7 8 11 10 5 4 10 8 16 11-6 0-7-2-12-4-3-2-5-3-7-6 0 1 0 2 1 2h-1c-1-1-3-2-4-3l-1-1c-1-1-2-1-4-1v1h-1z" class="K"></path><path d="M584 89l1 1 1-2c0 1-1 1-1 2h2 0c-1 1-1 2-2 3-2 1-3 4-4 5l-12 18-5 8-9 11c-2 3-5 7-8 10 0 1-2 2-2 2-1 0-1 0-2 1h-1c0-1 1-1 1-2 3-5 8-9 11-14 3-3 4-6 6-9 2-2 2-4 3-6l2-3v-2h0v-5c-1-1 0-1 0-2l-1-1v-1-1c0-2 2-3 3-4l4-1c4-2 7-4 11-4 0-2 1-3 2-4z" class="I"></path><path d="M571 97c4-2 7-4 11-4l-8 10c-2 3-3 6-6 8 0 1-1 3-3 3v-2h0v-5c-1-1 0-1 0-2l-1-1v-1-1c0-2 2-3 3-4l4-1z" class="U"></path><path d="M567 98l4-1c1 1 1 1 1 3s-1 4-2 5l-4 7c0-3 0-5-1-7l-1-1v-1-1c0-2 2-3 3-4z" class="I"></path><path d="M413 122v8c-1 5-4 10-8 13-1 1-3 1-4 3 2 0 4-1 5-1h1l-1 1c-10 6-19 14-31 16-3 1-7 2-10 2l1-1c2-2 5-2 7-3l9-5c1-1 1-1 3-2v-1l-1 1h-2c0 1-1 1-1 1-1 1-2 0-3 0h0l10-5c1-1 5-2 5-3 1-1 1-2 1-3 3-2 5-3 7-5 2-3 4-5 6-8 1 0 1-1 2-1l-1 2v1c2-2 2-4 3-7l2-3z" class="R"></path><path d="M400 144c-1 2-3 4-6 6l-1 1v1l-17 9c-1 1 0 1-1 1-3 1-7 2-10 2l1-1c2-2 5-2 7-3l9-5c7-1 13-7 18-11z" class="a"></path><path d="M407 130c1 0 1-1 2-1l-1 2v1c-1 3-5 10-8 12-5 4-11 10-18 11 1-1 1-1 3-2v-1l-1 1h-2c0 1-1 1-1 1-1 1-2 0-3 0h0l10-5c1-1 5-2 5-3 1-1 1-2 1-3 3-2 5-3 7-5 2-3 4-5 6-8z" class="N"></path><path d="M547 145l1 2c-9 7-20 14-31 17-3 1-6 1-8 1-5 0-10 0-14-1-2 0-4 0-6-1 0 0-1 0-1-1h-1-1-1 1 2v-1c-5-1-10-4-15-7v-1c2 0 3 1 5 1 4 1 7 2 10 3 9 2 21 1 31-1h0l2-1 9-1c1 0 2-1 3-1 4-2 7-4 10-7 0 1-1 1-1 2h1c1-1 1-1 2-1 0 0 2-1 2-2z" class="b"></path><path d="M521 155l9-1c-2 2-3 2-5 3l-4 1c-1 1-3 1-4 1-1 1-2 1-3 1l-1-1c1-1 3-1 4-1s1 0 2-1v-1h0l2-1z" class="C"></path><path d="M547 145l1 2c-9 7-20 14-31 17-3 1-6 1-8 1-5 0-10 0-14-1-2 0-4 0-6-1 0 0-1 0-1-1h-1-1-1 1 2v-1c5 1 10 2 16 2 15 0 30-5 41-16 0 0 2-1 2-2z" class="a"></path><defs><linearGradient id="CI" x1="561.434" y1="145.038" x2="574.594" y2="149.31" xlink:href="#B"><stop offset="0" stop-color="#3c3b3c"></stop><stop offset="1" stop-color="#626262"></stop></linearGradient></defs><path fill="url(#CI)" d="M573 118c1 3-3 14-2 18-2 9-3 17 0 27-1 0-1 1-2 1 0 1 0 2-1 2 0 2-1 3-1 4l-1 1c-1 1-3 2-5 2-2 1-3 0-5 0 0-2-1-6-2-8l6-15 4-10 5-14c1-3 1-6 4-8z"></path><path d="M297 144h1 0c1 1 1 1 2 1l4 4h1l2 1 2 2 2 1c1 0 2 1 3 1 4 1 9 3 13 3h9 6c4 0 8-1 12-2 2 0 5 0 8-1v1c2-1 3-2 5-2v1h2c0-1 0-1 1-1v1l-9 5c-2 1-5 2-8 3l-11 2v1 1h-11 0-1c-2-1-4-1-6-2h-1c0 1-1 1-1 2-1 0-2 1-3 1-6 2-10-1-15-4l-1-1c-2-2-5-5-5-9 2 1 3 2 4 3h0c2 0 3 1 4 1 5 2 11 4 17 4h1 0c-4-1-9-3-12-5v-1c-2-1-5-2-7-3 0-2-1-2-3-3-1-1-4-3-5-5z" class="Q"></path><path d="M324 164h18v1 1h-11 0-1c-2-1-4-1-6-2z" class="C"></path><path d="M298 153c2 1 3 2 4 3h0c4 4 9 6 14 8 1 0 4 0 6 1v1c-1 0-2 1-3 1-6 2-10-1-15-4l-1-1c-2-2-5-5-5-9z" class="S"></path><path d="M297 144h1 0c1 1 1 1 2 1l4 4h1l2 1 2 2 2 1c1 0 2 1 3 1 4 1 9 3 13 3h9 6c4 0 8-1 12-2 2 0 5 0 8-1v1c2-1 3-2 5-2v1 1c-2 0-3 1-4 2-2 1-4 1-6 2h0c-5 2-10 2-14 3s-8 0-11 0-5-1-8-1c-4-1-9-3-12-5v-1c-2-1-5-2-7-3 0-2-1-2-3-3-1-1-4-3-5-5z" class="I"></path><path d="M362 154v1c-9 2-17 4-26 4h-8c2-1 4-1 5-1l-6-1h9 6c4 0 8-1 12-2 2 0 5 0 8-1z" class="H"></path><path d="M297 144h1 0c1 1 1 1 2 1l4 4h1l2 1 2 2 2 1c1 0 2 1 3 1 4 1 9 3 13 3l6 1c-1 0-3 0-5 1-5 0-11-2-16-4-2-1-5-2-7-3 0-2-1-2-3-3-1-1-4-3-5-5z" class="X"></path><path d="M420 92l1-14 1 1c1 1 1 2 2 3 0 2-1 0 0 1s1 2 1 3v-4h1l1 3c-1 3 1 7 1 11 0 1 0 2-1 3v1 10c2 5 2 10 4 15l3 6 1 5c2 1 3 3 5 4h1 0c1 1 2 3 3 4v1 2c-2-2-3-2-4-2l3 5c-4-2-8-7-11-10 0 2 0 5-1 7-1 0-1 1-1 2h1c1 2 2 5 4 6l-1 1h0c-1 0-1 1-2 1v8 1c-2-1-3-2-5-3l1-1c-3-2-4-4-5-6-1-4-1-7-1-11l-1-18-1-35z" class="P"></path><path d="M422 79c1 1 1 2 2 3 0 2-1 0 0 1s1 2 1 3v-4h1l1 3c-1 3 1 7 1 11 0 1 0 2-1 3v1 10l-1-4-1-4c-2-7-2-15-3-23z" class="c"></path><path d="M425 86v-4h1l1 3c-1 3 1 7 1 11 0 1 0 2-1 3-1-4-2-9-2-13z" class="b"></path><path d="M425 102l1 4 1 4c2 5 2 10 4 15l3 6 1 5c2 1 3 3 5 4h1 0c1 1 2 3 3 4v1 2c-2-2-3-2-4-2l3 5c-4-2-8-7-11-10 0 2 0 5-1 7-1 0-1 1-1 2h1c1 2 2 5 4 6l-1 1h0c-1 0-1 1-2 1v8 1c-2-1-3-2-5-3l1-1h0l1-11v-3c-1-1-2-2-2-3-1-1 0-4 0-6 0-3-1-7-1-10v-7l-1-20z" class="D"></path><path d="M436 140c-2-3-6-8-6-11 2 3 3 4 5 7 2 1 3 3 5 4h1 0c1 1 2 3 3 4v1 2c-2-2-3-2-4-2l-4-5z" class="W"></path><path d="M430 149c0-1-1-2-1-2 1-3 1-10 1-13 2 2 3 4 5 6h1l4 5 3 5c-4-2-8-7-11-10 0 2 0 5-1 7-1 0-1 1-1 2z" class="J"></path><path d="M426 106l1 4c2 5 2 10 4 15l3 6 1 5c-2-3-3-4-5-7l-1-1c0-1 0-2-1-2v-1-1c-1-1 0-1 0-2-1-1-1-1-1-3v-2c-1-2-1-9-1-11z" class="E"></path><defs><linearGradient id="CJ" x1="430.159" y1="173.209" x2="430.324" y2="187.184" xlink:href="#B"><stop offset="0" stop-color="#2c2b2c"></stop><stop offset="1" stop-color="#595859"></stop></linearGradient></defs><path fill="url(#CJ)" d="M304 174c1 1 2 2 3 2 4 0 7-1 11-1 6-1 12-1 18-1l149-1c14 0 28 1 42 3l9 1c3 0 6 1 9 0 3 1 5 2 8 2-1 1-2 1-2 1l-1 4c1 1 2 2 2 4-2-1-3-2-6-2-1 0-1 0-1 1-3-2-5-3-8-4-1-1-3-2-4-3-3 0-6 0-9-1h1c-1 0-2 0-3-1h-6-3-1c-2 0-4-1-6-1h-14c-5 0-10-1-15-1h-4c-2-1-7 1-9-1l-78 2-31-1c-8-1-16 0-23 1-6 0-13 1-19 2l-1-1h-3v-1c-2 0-4-1-6-1h0c0-1 1-1 1-2z"></path><path d="M546 186l1-1h0c-1-1-2-1-3-1-1-1-1-1-2-1l1-1c2 1 4 1 6 2h1c1 1 2 2 2 4-2-1-3-2-6-2z" class="a"></path><defs><linearGradient id="CK" x1="459.888" y1="126.232" x2="443.518" y2="141.877" xlink:href="#B"><stop offset="0" stop-color="#c6c5c5"></stop><stop offset="1" stop-color="#e9e9e9"></stop></linearGradient></defs><path fill="url(#CK)" d="M428 96h0c1-1 1-2 2-2 1 8 3 15 5 23 1 3 2 6 4 9 2 5 6 9 10 13h0c2 3 5 5 8 7v-3c2 1 1 3 2 4 2 1 12 7 14 7 5 3 10 6 15 7v1h-2-1 1 1 1c0 1 1 1 1 1 2 1 4 1 6 1 4 1 9 1 14 1h-7-3l-1 1h-2c-3 1-5 0-8 0l-9-1c-6 0-10-1-16-2l-4-2c-6-3-11-7-16-11l-3-5c1 0 2 0 4 2v-2-1c-1-1-2-3-3-4h0-1c-2-1-3-3-5-4l-1-5-3-6c-2-5-2-10-4-15v-10-1c1-1 1-2 1-3z"></path><path d="M434 131c1-1-1-4-1-6h1l7 15h0-1c-2-1-3-3-5-4l-1-5z" class="J"></path><path d="M427 100c3 6 3 12 5 19 0 2 1 4 2 6h-1c0 2 2 5 1 6l-3-6c-2-5-2-10-4-15v-10z" class="C"></path><path d="M457 143c2 1 1 3 2 4 2 1 12 7 14 7 5 3 10 6 15 7v1h-2-1 1 1 1c0 1 1 1 1 1-7 0-13-4-18-7s-10-6-14-10v-3z" class="V"></path><defs><linearGradient id="CL" x1="458.797" y1="157.26" x2="462.854" y2="151.828" xlink:href="#B"><stop offset="0" stop-color="#303030"></stop><stop offset="1" stop-color="#4c4949"></stop></linearGradient></defs><path fill="url(#CL)" d="M444 144l2-2c1 1 1 3 1 4l2 2c2 1 3 1 5 2s4 3 6 4c10 6 20 10 31 11-1 1-2 1-3 1l-9-1c-6 0-10-1-16-2l-4-2c-6-3-11-7-16-11l-3-5c1 0 2 0 4 2v-2-1z"></path><defs><linearGradient id="CM" x1="442.91" y1="154.757" x2="466.615" y2="155.108" xlink:href="#B"><stop offset="0" stop-color="#706f6f"></stop><stop offset="1" stop-color="#8c8b8c"></stop></linearGradient></defs><path fill="url(#CM)" d="M440 145c1 0 2 0 4 2 7 6 17 12 27 16 3 1 6 1 8 2-6 0-10-1-16-2l-4-2c-6-3-11-7-16-11l-3-5z"></path><defs><linearGradient id="CN" x1="583.712" y1="104.557" x2="612.099" y2="114.552" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#CN)" d="M578 120c9-23 24-44 43-60 4-2 7-5 12-8-1 4-7 7-9 10-7 7-11 17-12 26-1 3-1 6-1 8s1 4 1 6c0 1-1 2-1 3h0-1-1l2 5h0c1 1 4 6 4 7s-1 2-1 3c1 0 1 0 1 1s2 2 2 3c3 4 5 8 8 11 1 0 2 2 2 2 4 3 7 6 11 9 2 1 3 3 6 4h1 3l1 1c-1 1-1 2-2 4v1c1 3 5 4 5 7 0 1-1 2-2 3h-2l-2 1c-1-1-2 0-3 0-3 0-8 1-10-1l-1 1c-4-1-8-2-11-4l-6-3-2-2s-1-1-2-1h-1l-2-2h-2v1l-2-2-3-4-1 1-3-2c-3 2-2 5-4 7-1 1-1 2 0 3 0 1-1 4-2 5-1 2-3 4-4 5l-1 1h-4l-3-1c-2-1-2-1-4-3 0 2 2 3 3 4-3-2-5-4-7-7-3-10-2-18 0-27-1-4 3-15 2-18l4-4c0 2 0 5 1 6z"></path><path d="M610 103v-2c1-1 0-6 0-8 0-1 1-2 1-3 0 2-1 4 0 6h0c0 2 1 4 1 6 0 1-1 2-1 3h0-1-1s0-1-1-1l1-1 1 1h1l-1-1z" class="M"></path><path d="M573 118l4-4c0 2 0 5 1 6h0c-2 7-5 12-6 19 0-2 0-2-1-3-1-4 3-15 2-18z" class="Z"></path><path d="M571 136c1 1 1 1 1 3-1 7-2 19 3 25 1 2 3 3 4 5-2-1-2-1-4-3 0 2 2 3 3 4-3-2-5-4-7-7-3-10-2-18 0-27z" class="K"></path><defs><linearGradient id="CO" x1="604.495" y1="128.121" x2="597.111" y2="148.178" xlink:href="#B"><stop offset="0" stop-color="#444546"></stop><stop offset="1" stop-color="#5d5c5c"></stop></linearGradient></defs><path fill="url(#CO)" d="M598 145h0c-5-9-4-21-3-31 0 1 1 1 0 2v5 7h0l1-3h1c0-1 1-2 1-3 0 6 0 12 3 18 1 2 2 5 3 7l4 6 4 3 1 2s-1-1-2-1h-1l-2-2h-2v1l-2-2-3-4-3-5z"></path><path d="M598 145l1-1c2 4 4 8 9 9h0l4 3 1 2s-1-1-2-1h-1l-2-2h-2v1l-2-2-3-4-3-5z" class="H"></path><defs><linearGradient id="CP" x1="600.354" y1="127.009" x2="613.896" y2="127.232" xlink:href="#B"><stop offset="0" stop-color="#101010"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#CP)" d="M600 114c2-4 1-8 3-12l1-2v1c2 0 5 1 6 2h0 0l1 1h-1l-1-1-1 1c1 0 1 1 1 1l2 5h0c1 1 4 6 4 7s-1 2-1 3c1 0 1 0 1 1s2 2 2 3c3 4 5 8 8 11 1 0 2 2 2 2h0l-10-9-1-1h-1v-1-1-1c-1-1-2-2-3-2l-1-1c-2 1-2 2-3 3-1 9 2 18 6 25 2 2 4 3 5 5l1 1c0 2 0 4 1 6v2h0l-6-3-2-2-1-2-4-3-4-6c-1-2-2-5-3-7-3-6-3-12-3-18 0-1 0-1 1-2v-1c0-1 0-1 1-1v-2-5 3z"></path><path d="M604 147h1l1 2c1 0 2 1 2 2h0l1 1v-1l-1-2-1-1v-1c-2-2-2-2-2-4l1-1 2 4c1 3 3 7 4 10h0l-4-3-4-6z" class="N"></path><path d="M598 122c0-1 0-1 1-2v-1c0-1 0-1 1-1v-2-5 3c0 9 0 17 1 26-3-6-3-12-3-18z" class="V"></path><path d="M608 146l2 2c4 4 6 10 11 13v2h0l-6-3-2-2-1-2h0c-1-3-3-7-4-10z" class="M"></path><path d="M608 146l2 2c1 4 5 8 5 12l-2-2-1-2h0c-1-3-3-7-4-10z" class="P"></path><path d="M603 115h1v1h1v5c-1 1 0 3 0 4v12h-1c-2-5-2-12-2-17l1-5v1-1z" class="N"></path><defs><linearGradient id="CQ" x1="608.878" y1="143.431" x2="646.693" y2="143.841" xlink:href="#B"><stop offset="0" stop-color="#616060"></stop><stop offset="1" stop-color="#9d9c9c"></stop></linearGradient></defs><path fill="url(#CQ)" d="M620 155l-1-1c-1-2-3-3-5-5-4-7-7-16-6-25 1-1 1-2 3-3l1 1c1 0 2 1 3 2v1 1 1h1l1 1 10 9h0c4 3 7 6 11 9 2 1 3 3 6 4h1 3l1 1c-1 1-1 2-2 4v1c1 3 5 4 5 7 0 1-1 2-2 3h-2l-2 1c-1-1-2 0-3 0-3 0-8 1-10-1l-1 1c-4-1-8-2-11-4h0v-2c-1-2-1-4-1-6z"></path><path d="M620 155l1-2h0c1 1 0 4 1 5 0 2 1 4 1 5h-2v-2c-1-2-1-4-1-6z" class="D"></path><path d="M622 158c2 1 3 2 4 4 2 1 5 3 7 3v1h0l-1 1c-4-1-8-2-11-4h0 2c0-1-1-3-1-5z" class="T"></path><path d="M633 165h4c1 0-1 0 1 0 0 0 0 1 1 1h2 1 2-4v-1c-2-1-4-2-6-4v-2c-1 0-2-1-3-2s-3-2-4-4l7 4c2 1 4 2 5 3 3 2 9 3 11 6h-2l-2 1c-1-1-2 0-3 0-3 0-8 1-10-1h0v-1z" class="X"></path><path d="M639 160c3 2 9 3 11 6h-2-3c0-1 0 0-1-1-2-1-3-2-5-4v-1z" class="a"></path><path d="M538 213h5 6l1 1c-1 1-3 1-4 1h-30l-37 1c-6 0-11 1-17 1-3 1-9 1-11 4-2 2-4 5-5 7-5 13-8 29-10 43-2 27-1 55-1 82v142 40 16l2 28-3-1c0 9 2 20 6 28l3 7h1v-3-2c1 2 2 6 3 7 1 2 2 3 3 4 1 3 3 6 5 7 0 0 2 2 3 2-1 1-2 1-2 1 0 1 0 1-1 2h0c0-1 0-1-1-2-3 1-5 4-8 4h-12l-9 1h-21-6l-13-1c-4 0-8 0-10-2l-1-1h-4l-6 3c-3 1-7 2-10 3-2 1-3 1-4 3-25 3-49 4-74 4-2 0-9 1-11 0v-2c2-1 9 0 11-1l22-2c23-4 42-15 56-34 8-11 11-25 14-38 1-7 3-14 3-22 2-14 1-29 1-44v-76-79-36c0-13 0-25-2-38l-4-15 1-1h1c1 0 2-1 3-1 4-5 9-11 12-16 2-2 3-6 5-7l1-1-1-4 2-2 14-1h3 4 8 4c1-1 3-1 4 0h1l2 1h1 2l-1-1c0-1 1-1 2-1v2h1c1-1 2-1 4 0 0 2-1 3-1 5h0c0 1-1 1-1 2l2 1h0c1 1 1 2 2 3 0-3 2-8 3-11l1-1 2-2c0-1 1-2 3-3 2-2 7-2 11-3 4 0 10-1 14-1l56-1h7z" class="b"></path><path d="M384 421h1l1 1v3l-1-1c-1 0-2-1-2-2l1-1z" class="S"></path><path d="M377 366c0-7 0-14 1-21l1 20-2 1z" class="N"></path><path d="M383 417c2 0 2 0 4 1v1h-1c-1-1-1-1-3-1-2 3-3 8-4 11 0 2-1 5-1 7l-1 1h0l-1-5h1c1-1 1-2 1-2l-1-1v-2c3-3 3-7 6-10z" class="W"></path><path d="M383 405c1 1 1 2 1 2-1 3-2 6-2 9 0 0 0 1 1 1-3 3-3 7-6 10 1-1 1-3 1-5 1-2 2-3 2-5v-5c1-3 2-5 3-7z" class="I"></path><path d="M379 365v4c1-1 2-2 2-4 1 1 1 1 2 1v1c0 2-1 3-1 4l-2 7v-1-1c0-2 3-7 2-9-3 3-3 10-4 13v1c0 1 0 1-1 1 0-5 1-10 0-15v-1l2-1z" class="S"></path><path d="M376 453c1 1 1 2 1 3h0c1 5 1 10 2 14 1 2 1 4 2 5h1l-1 1-3-3v-1h0v2h-2v-7-14z" class="d"></path><path d="M378 436c2-2 3-4 5-6 2 2 4 3 5 5h0c1 2 1 3 1 5-1 1-2 0-3 0v-1c0-1 0-1 1-2h-1c-1 0-2 0-2 1v1h-1v-1-2h3v-1-1c-2 0-3 0-4 1l-2 2v1 1h-1l-2-2 1-1z" class="f"></path><path d="M397 384h0c0 2 0 10 1 12v8 3 1c1 1 1 6 0 7h0v1l1 4h-1v3c0 1-1 3-1 4 0 3 1 6 0 9v5c0 5 1 10 0 15v-72z" class="H"></path><path d="M378 474v-2h0v1l3 3 1-1 1 1c1 1 1 1 1 2-1 2-1 2-3 3-1 0-2-2-3-2v6l1 1v3l-1-3c0 2 0 6 1 8h1c1 0 1-1 1-1l1 1c0 1-1 2-1 3-2 0-2-1-3-1 1 6 0 14-1 20l-1-42h2z" class="g"></path><path d="M378 474v-2h0v1l3 3c1 1 2 1 2 2l-1 1h-1c-1-1-3-3-3-5z" class="I"></path><path d="M377 367c1 5 0 10 0 15 1 0 1 0 1-1h2c2 0 2 1 2 2v1c-1 2-3 1-4 4v11c1-2 1-3 1-5h0l1-2c0-1 0-1-1-2h0c1-1 1-2 1-3l1-1c1 1 0 5 0 6l-1 1v2 4c-1 0-1 0-2 1v3l1 1h0c-1 3-2 6-2 10l1 1c0 1-1 1-2 2l1-50z" class="M"></path><defs><linearGradient id="CR" x1="376.645" y1="412.984" x2="379.164" y2="425.135" xlink:href="#B"><stop offset="0" stop-color="#222125"></stop><stop offset="1" stop-color="#40403e"></stop></linearGradient></defs><path fill="url(#CR)" d="M379 404c0-2 0-3 1-4s2-1 4-1c1 0 2 0 3 1v1c0 2-2 3-4 4-1 2-2 4-3 7v5c0 2-1 3-2 5 0 2 0 4-1 5v2l1 1s0 1-1 2h-1 0v-15c1-1 2-1 2-2l-1-1c0-4 1-7 2-10h0z"></path><path d="M379 404c1-1 2-3 3-4h3l1 1c-1 0-1 1-2 1-3 3-5 8-6 13l-1-1c0-4 1-7 2-10z" class="I"></path><path d="M376 432h0l1 5h0l2 2h1v-1c1 1 3 4 5 4h4v1 3h-3v1c0 1 1 1 1 2v1c-1 1-1 1-2 1l-1-1-1 1c-1 0-2-1-3-1 0 3 2 6 4 9 1 1 3 2 3 4 0 1-1 1-1 2-1 0-1 1-2 1-2 0-2-1-3-2-2-2-2-5-3-7v-2 1h-1 0c0-1 0-2-1-3v-6-15z" class="R"></path><path d="M380 454h0c2 2 3 4 4 6s1 2 1 3c-1 1-1 1-2 1v-1c-3-2-3-5-3-9z" class="b"></path><path d="M384 450c0-2-4-3-4-5-1-1-1-2-1-2l1-1c1 1 3 1 4 2 2 0 4 0 5-1v3h-3v1c0 1 1 1 1 2v1c-1 1-1 1-2 1l-1-1z" class="I"></path><path d="M366 256l1-1h1c1 0 2-1 3-1 4-5 9-11 12-16 2-2 3-6 5-7l1-1c2 5 5 12 5 18l-1-2c-1-1-1-2-1-3h0c0-1-1-2-1-3l-1-3h0c-1 1-1 3-2 4-1 2-3 4-4 6-1 3-4 5-5 7-2 2-4 5-7 6h-1-2c-1 4 2 8 1 11l-4-15z" class="C"></path><path d="M397 456c1-5 0-10 0-15v-5c1-3 0-6 0-9 0-1 1-3 1-4v-3c1 1 1 3 1 5 0 1 1 1 1 3v1c1 0 2 0 3 1l-1 2 2 4h0 0v1h0-3v2 1h-1c-1 6 1 13-1 18v31 9 16c0 2 0 5-1 6v1 1c0 1 0 2-1 3 0 2 0 4 1 5v4c0 1 0 1 1 2 0 6-1 11-1 17h-1v3h0V456z" class="X"></path><path d="M399 425c0 1 1 1 1 3v1c1 0 2 0 3 1l-1 2 2 4h0 0v1h0-3v2 1h-1c-1 6 1 13-1 18v-16-9c-1-1-1-3-1-4 1-2 1-3 1-4z" class="W"></path><path d="M400 429c1 0 2 0 3 1l-1 2 2 4h-1-2c-2-2-1-5-1-7zm-1 29c2-5 0-12 1-18h1l2 1c0 1 0 2-1 3v3 4 7 4c0 3-1 5-1 8v1c1 0 2-1 3 0h2c0 2-3 3-4 5v5 21 9c-1 1-1 2 0 4 0 1 0 1 1 2 0 2 0 2 1 3h2l2 2-1 1c-2 0-4-1-5 0l-1 1h2c4 1 5 2 8 5 2 2 2 4 3 7l1 4c0 2-1 3-1 5l-1 3c0 1 0 2-1 4h-1l1 1h-2l-1 1h-1c-1 0-3 2-5 3v1h0l1 1v1c-1 1-2 2-2 3h1l-1 1c0 1-1 1-1 2h1c0 1 0 2-1 2 0 1-1 4-1 5v1l1 1h0v3c0 1 0 3 1 4 1 0 1 0 2 1 1 2 0 4 0 6 1 2 3 2 3 4h0c-2 0-3-1-5-2l-1-1c0-1 0-1-1-2s-1-1-1-3v-12l-2 2c0 2 0 3-1 5l1-24h0v-3h1c0-6 1-11 1-17-1-1-1-1-1-2v-4c-1-1-1-3-1-5 1-1 1-2 1-3v-1-1c1-1 1-4 1-6v-16-9-31z" class="M"></path><path d="M411 537c1 3 1 5-1 8-1 1-1 2-2 3h0c-2 1-3 2-4 2v-1h-3 0l1-12c0 2 0 5 1 8v3h0 1s1 0 1-1c1-1 1-1 1-2l2-2v-3 1c1 1 1 2 1 2-1 2-3 4-4 5v1l1-1h1c1-1 2-3 2-4l1-3c1 0 1 0 1-1l-1-1c1-1 1-1 1-2z" class="T"></path><path d="M402 537h0c-1-2 0-5 0-6h1v1c2 1 3 3 4 6l1 1v1 3l-2 2c0 1 0 1-1 2 0 1-1 1-1 1h-1 0v-3c-1-3-1-6-1-8z" class="G"></path><path d="M402 537h0c-1-2 0-5 0-6h1v1 2 3c-1 3 0 7 1 10l-1 1v-3c-1-3-1-6-1-8z" class="W"></path><path d="M403 532c2 1 3 3 4 6l1 1v1 3l-2 2c-2-2-2-8-3-11v-2z" class="J"></path><path d="M397 556h0v-3h1c0-6 1-11 1-17-1-1-1-1-1-2v-4c-1-1-1-3-1-5 1-1 1-2 1-3v-1-1c1-1 1-4 1-6v-16-9c1 12 0 24 0 35v24c0 5 1 10 0 15 0 4-1 8-2 12 0 2 0 3-1 5l1-24z" class="R"></path><path d="M401 524h2c4 1 5 2 8 5 2 2 2 4 3 7l1 4c0 2-1 3-1 5l-1 3c0 1 0 2-1 4h-1l1 1h-2l-1 1h-1c-1 0-3 2-5 3v-1c0-2 0-4 1-6 1 0 2-1 4-2h0c1-1 1-2 2-3 2-3 2-5 1-8-1-2-2-5-4-7l-1-1c-1-1-1-2-2-2l-1-1h-1l-1-1v-1z" class="F"></path><path d="M404 550c1 0 2-1 4-2 0 1-1 2-1 2l-3 3h1 2l1 1c-1 0-3 2-5 3v-1c0-2 0-4 1-6z" class="G"></path><path d="M403 524c4 1 5 2 8 5 2 2 2 4 3 7l1 4c0 2-1 3-1 5l-1 3c-1 1-3 3-5 3h0l4-4c1-3 1-8 1-11v-1l-1-1c-1-3-5-7-8-8h0l-1-2z" class="c"></path><path d="M396 248l2 8v3l1 8v10c1 1 0 2 1 3 0 0 1 0 1 1h1l1 2c0 2 1 4 0 5l-1 1c1 1 2 3 3 4v1c0 1 0 1 1 2 1 2 0 3 1 5l-1 1h0c0 1 0 1 1 1 0 1 0 2 1 3l-2 2c-1 2-2 5-3 7v1l1 3h0c-1 1-1 2-1 3-1 2-1 4-2 6v2h1l4-4h2v3c-2 2-5 4-7 6v1l2 2c1 1 0 2 0 3l1 2c1 0 2-2 3-2 2-1 2-1 2-3l4-1c1 1 3 1 5 1l1 1c3 1 5 2 8 5l1 2c1 1 1 2 1 3l-1 1-1-1h0c-1-1-2-3-3-4 0 0-1-1-1-2-2-1-7-1-9 0h-2l-2 2h-1c-2 2-2 3-3 6 0 3-1 7-1 10 0 1 1 1 1 2v1c0 2-1 4 1 6l2 1 3 3h2l1 1-1 1v1c-3 0-6 0-9-1 0-1 0-1-1-1 0 1 1 1 2 2v3h-2l-3 3 1 2 1 1c-1 1-1 1-1 2l-1 2h1l1 1h-1v1l1 1c1 1 1 2 1 3h-2c0-1-1-2-2-2l-1 1v14c0 3 1 7 0 11l-1-4v-1h0c1-1 1-6 0-7v-1-3-8c-1-2-1-10-1-12h0v-98c-1-3 0-6-1-9v-16-13z" class="X"></path><path d="M399 336v-5c-1-8-1-15 0-23 0-3 0-5 1-7v12 14c0 3 0 6-1 9z" class="O"></path><path d="M396 248l2 8v3c-1 1-1 3-1 5v1 1c1 0 0 1 0 2v5 13c-1-3 0-6-1-9v-16-13z" class="F"></path><defs><linearGradient id="CS" x1="403.186" y1="374.51" x2="400.11" y2="393.092" xlink:href="#B"><stop offset="0" stop-color="#343333"></stop><stop offset="1" stop-color="#4f4f4e"></stop></linearGradient></defs><path fill="url(#CS)" d="M401 373l3 2c0 1 1 1 2 2v3h-2l-3 3 1 2 1 1c-1 1-1 1-1 2l-1 2h1l1 1h-1v1l1 1c1 1 1 2 1 3h-2c0-1-1-2-2-2l-1 1 1-22h1z"></path><path d="M400 280s1 0 1 1h1l1 2c0 2 1 4 0 5l-1 1c1 1 2 3 3 4v1c0 1 0 1 1 2 1 2 0 3 1 5l-1 1-1 1h-1-2c-1 1-1 1-1 2-1 1-1 7-1 8v-12-21z" class="S"></path><path d="M402 289c1 1 2 3 3 4v1c0 1 0 1 1 2-1 1-1 1-1 2-1 0-1 1-2 1-1-2-1-3-1-5h0v-1c-1-1 0-3 0-4z" class="a"></path><defs><linearGradient id="CT" x1="396.55" y1="336.677" x2="428.657" y2="340.278" xlink:href="#B"><stop offset="0" stop-color="#302f30"></stop><stop offset="1" stop-color="#636262"></stop></linearGradient></defs><path fill="url(#CT)" d="M401 305c0-1 0-1 1-2h2 1l1-1h0c0 1 0 1 1 1 0 1 0 2 1 3l-2 2c-1 2-2 5-3 7v1l1 3h0c-1 1-1 2-1 3-1 2-1 4-2 6v2h1l4-4h2v3c-2 2-5 4-7 6v1l2 2c1 1 0 2 0 3l1 2c1 0 2-2 3-2 2-1 2-1 2-3l4-1c1 1 3 1 5 1l1 1c3 1 5 2 8 5l1 2c1 1 1 2 1 3l-1 1-1-1h0c-1-1-2-3-3-4 0 0-1-1-1-2-2-1-7-1-9 0h-2l-2 2h-1c-2 2-2 3-3 6 0 3-1 7-1 10 0 1 1 1 1 2v1c0 2-1 4 1 6l2 1 3 3h2l1 1-1 1v1c-3 0-6 0-9-1 0-1 0-1-1-1l-3-2h-1c-1-2-1-4 0-6l-1-31c1-3 1-6 1-9v-14c0-1 0-7 1-8z"></path><path d="M401 328c0-5 0-8 2-12l1 3h0c-1 1-1 2-1 3-1 2-1 4-2 6z" class="f"></path><path d="M413 337c1 1 3 1 5 1l1 1c-1 0-2-1-4 0h-1c-2 1-5 2-6 3s-1 1-2 1l-3 3-1-1 2-2c1 0 2-2 3-2 2-1 2-1 2-3l4-1z" class="D"></path><path d="M401 305c0-1 0-1 1-2h2 1l1-1h0c0 1 0 1 1 1 0 1 0 2 1 3l-2 2c-1 2-2 5-3 7v-6c1-1 1-2 0-2 0-1-1-2-2-2z" class="M"></path><path d="M405 369c-1-2-2-4-2-6-1-4-1-13 2-16 1-2 3-2 4-2-2 2-2 3-3 6 0 3-1 7-1 10 0 1 1 1 1 2v1c0 2-1 4 1 6l-2-1z" class="R"></path><path d="M405 369l2 1 2 1 3 3h2l1 1-1 1v1c-3 0-6 0-9-1 0-1 0-1-1-1l-3-2v-3-1l3 3c1-1 1-2 1-3z" class="K"></path><path d="M405 369l2 1 2 1 3 3h2l1 1-1 1c-2 0-3 0-5-1h0c-2-1-3-2-5-3 1-1 1-2 1-3z" class="a"></path><path d="M399 395l1-1c1 0 2 1 2 2h2 3l1 1-1 3h1c2-1 3-1 5-1v12 3 1c0 2 0 2 2 4h0 0l2 2-1 1v1c1 0 1 1 2 1-1 3-2 6-2 9v1c0 1 1 3 1 4v1c0 2 1 4 0 6h0v1c1 0 1 0 2 1l-1 3c-1 1-1 2-1 2 0 1 1 2 1 2v1c0 2 0 6 1 8 0 1 0 2 1 3 0 1-1 1 0 2l2 2v2c-2-1-4-2-6-2-3 1-4 1-6 2s-4 2-5 4c-2 3-1 21 0 25h0c0 2 0 3-1 5h0c0 1 0 1-1 2 0 1 1 3 2 4h1c1 1 1 2 2 2v3l1 2c1 1 1 2 2 3h0l1 2v1c1 0 1 1 1 1v1l1 1c0 2 1 4 1 6l-1 2c-1-3-1-5-3-7-3-3-4-4-8-5h-2l1-1c1-1 3 0 5 0l1-1-2-2h-2c-1-1-1-1-1-3-1-1-1-1-1-2-1-2-1-3 0-4v-9-21-5c1-2 4-3 4-5h-2c-1-1-2 0-3 0v-1c0-3 1-5 1-8v-4-7-4-3c1-1 1-2 1-3l-2-1v-1-2h3 0v-1h0 0l-2-4 1-2c-1-1-2-1-3-1v-1c0-2-1-2-1-3 0-2 0-4-1-5h1c1-4 0-8 0-11v-14z" class="B"></path><path d="M415 419l2 2-1 1c-1 0-1 0-2-1v-1l1-1h0z" class="R"></path><path d="M405 456l2-1v-1h0c1 2 0 3 0 5-2-1-2-1-2-3z" class="D"></path><path d="M405 456c0 2 0 2 2 3l-2 3-1 1v-1-1c0-2 0-3 1-5z" class="H"></path><path d="M402 511c1 1 3 2 3 3v1h-2-1c-1-2-1-3 0-4z" class="T"></path><path d="M418 455l-1 2c0-2 1-6 0-7s0-3 0-5h0 0 0v1c1 0 1 0 2 1l-1 3c-1 1-1 2-1 2 0 1 1 2 1 2v1z" class="X"></path><path d="M416 470h2 0l-1-2h1 2l2 2v2c-2-1-4-2-6-2z" class="Z"></path><path d="M409 462v4c-1 1-2 3-4 3l-1 1v-1c1-2 4-4 5-7z" class="e"></path><path d="M402 462h1v2h0l1-1 1-1 1 1c-1 2-3 5-5 7 0-3 1-5 1-8z" class="W"></path><path d="M412 463c0 2 0 3-1 5-1 1-3 1-4 2h-1l-1-1c2 0 3-2 4-3l3-3z" class="Z"></path><path d="M406 444c2 1 2 2 3 4 0 2 1 5 0 7l-1 1c0-3-1-6-2-8 1-2 1-2 0-4zm2 78c2 2 3 4 4 6l-1 1c-3-3-4-4-8-5h-2l1-1c1-1 3 0 5 0l1-1z" class="G"></path><path d="M403 441l2 1 1 2c1 2 1 2 0 4l-3-3c0 4 2 10 0 14v3h-1v-4-7-4-3c1-1 1-2 1-3z" class="V"></path><path d="M409 437c0 2 1 3 3 4v2s0 1 1 1l1 1c0 4 1 9 0 13l-2 5-3 3v-4l2-3c1-3 0-8-2-11-1-2-1-3-3-4l-1-2c0-1 0-2-1-3v-1l2 1c1 0 2-1 3-2z" class="C"></path><path d="M409 437c0 2 1 3 3 4v2s0 1 1 1l1 1c0 4 1 9 0 13-1-8-2-13-8-19 1 0 2-1 3-2z" class="X"></path><path d="M399 395l1-1c1 0 2 1 2 2h2 3l1 1-1 3h1c2-1 3-1 5-1v12 3 1c0 2 0 2 2 4h0l-1 1c-1 0-1 0-1 1 1 2 0 7 0 9s-1 3-1 5c0 1 0 2-1 3 0 1 1 2 1 3-2-1-3-2-3-4-1 1-2 2-3 2l-2-1v1c1 1 1 2 1 3l-2-1-2-1v-1-2h3 0v-1h0 0l-2-4 1-2c-1-1-2-1-3-1v-1c0-2-1-2-1-3 0-2 0-4-1-5h1c1-4 0-8 0-11v-14z" class="V"></path><path d="M402 396h2c1 1 2 1 2 1v1h-1c-2 1-2 1-4 0h0l1-2z" class="M"></path><path d="M403 418h1c1 0 2-1 3-2l3 3h1v1 2c-2-2-2-3-4-3h-2l-3 3h0-1c0-2 1-2 2-4z" class="e"></path><path d="M410 414l2 2v1c-2-1-2-2-4-2v1c0 1 1 1 2 1l1 1-1 1-3-3c-1 1-2 2-3 2h-1 0l-2 1v-1l3-3c1-1 2-1 3-1h3 0z" class="H"></path><path d="M406 411v1l-6 2c1-2 3-4 4-5l-1-1-1 1-1-1c0-1 0-1 1-2 0 0 1 0 2 1 1-1 2-1 4-1l-2 1c1 1 1 2 2 2h1v1h0l-2-1c-1 1-1 1-1 2h0z" class="G"></path><path d="M408 400c2-1 3-1 5-1v12 3 1c0 2 0 2 2 4h0l-1 1c-1 0-1 0-1 1 1 2 0 7 0 9s-1 3-1 5v1c-2-1-1-3-2-5 1-1 1-3 2-4 0-1-1-2-1-3h0c0-1-1-1 0-2v-2-1h-1l1-1-1-1c-1 0-2 0-2-1v-1c2 0 2 1 4 2v-1l-2-2v-1c-1-1-3-2-4-2h0c0-1 0-1 1-2l2 1h0v-1h-1c-1 0-1-1-2-2l2-1c0-1 1-2 0-3h-2c1-1 1-1 1-2h-1l1-1h1z" class="E"></path><path d="M411 424v-1h1 1v2 5c0 2-1 3-1 5v1c-2-1-1-3-2-5 1-1 1-3 2-4 0-1-1-2-1-3z" class="c"></path><path d="M408 400c2-1 3-1 5-1v12 3h0c-1-1-1-3-1-4-1-2-1-4-2-5v-3c-1-1-1-1-1-2h-1z" class="I"></path><path d="M407 419c2 0 2 1 4 3-1 1 0 1 0 2h0c0 1 1 2 1 3-1 1-1 3-2 4 1 2 0 4 2 5v-1c0 1 0 2-1 3 0 1 1 2 1 3-2-1-3-2-3-4-1 1-2 2-3 2l-2-1v1c1 1 1 2 1 3l-2-1-2-1v-1-2h3 0v-1h0 0l-2-4 1-2v-3c0-2 1-2 3-3h0c0-2 1-3 0-4l-1-1h2z" class="B"></path><path d="M405 434v-2c1 1 2 1 3 3h-1v2h-1c-1 0-1 0-2-1h0l1-2z" class="Z"></path><path d="M407 419c2 0 2 1 4 3-1 1 0 1 0 2h0c0 1 1 2 1 3-1 1-1 3-2 4l-1-1c-1-1-2-2-2-3 1-1 1 0 2 1 1-1 1-1 1-2l-1-1-1-1v-1h0c0-2 0-3-1-4z" class="D"></path><path d="M406 424v3 2c-1 0-1 1-1 2h-2c1 1 1 2 2 3l-1 2h0l-2-4 1-2v-3c0-2 1-2 3-3z" class="a"></path><path d="M409 430l1 1c1 2 0 4 2 5v-1c0 1 0 2-1 3 0 1 1 2 1 3-2-1-3-2-3-4l-1-1c0-2 0-4 1-6z" class="F"></path><path d="M410 345l2-2h2c2-1 7-1 9 0 0 1 1 2 1 2 1 1 2 3 3 4h0l1 1 1-1c0-1 0-2-1-3l1-2c0 2 0 3 1 5v6 13l1 2c1-1 0-7 1-9v38c0 7 1 14 0 20l-1 1h0v-2l-2-1-1 1v1c-1-1-2-2-4-3-2 1-3 2-4 4l-1 1h0-2l-2-2h0 0c-2-2-2-2-2-4v-1-3-12c-2 0-3 0-5 1h-1l1-3-1-1h-3c0-1 0-2-1-3l-1-1v-1h1l-1-1h-1l1-2c0-1 0-1 1-2l-1-1-1-2 3-3h2v-3c-1-1-2-1-2-2 1 0 1 0 1 1 3 1 6 1 9 1v-1l1-1-1-1h-2l-3-3-2-1c-2-2-1-4-1-6v-1c0-1-1-1-1-2 0-3 1-7 1-10 1-3 1-4 3-6h1z" class="Y"></path><path d="M429 388h3l-1 3h-2v-3z" class="T"></path><path d="M428 380h1v-1h1v5h-1l-2-2-1-2h2z" class="Q"></path><path d="M430 384l1-1c1 1 0 4 1 5h-3l1-1c-1 0-1-1-1-1-1-1 0-1 0-2h1z" class="a"></path><path d="M431 370c-1 1 0 2-1 3l-4-2 1-4 1-1 2 2 1 2z" class="L"></path><path d="M422 379v-2h1 0c1-1 2-1 3-1l2 2v2h-2c0-1-2 0-2-1h-2z" class="O"></path><path d="M407 363c-1-1 0-3 1-4 1-2 5-4 7-4 3 0 6 1 7 3 2 2 3 3 3 6-1 0-2-4-4-5 0-1-2-2-3-2-4 0-7 1-10 4l-1 2z" class="C"></path><path d="M418 357c1 0 3 1 3 2 2 1 3 5 4 5 0 3 0 6-2 9-2 2-6 4-9 4h0v-1l1-1-1-1h-2c2 0 3 0 4-1l2-2v-1c2 1 3 0 4 1 1-2 2-5 1-7 0 0-1-1-2-1v-3l-2-1h0l-1-1v-1z" class="J"></path><path d="M418 370c2 1 3 0 4 1-2 2-5 4-7 4l-1-1h-2c2 0 3 0 4-1l2-2v-1z" class="H"></path><defs><linearGradient id="CU" x1="432.559" y1="404.429" x2="423.967" y2="402.583" xlink:href="#B"><stop offset="0" stop-color="#4d4c4d"></stop><stop offset="1" stop-color="#636363"></stop></linearGradient></defs><path fill="url(#CU)" d="M426 405c1-1 2-2 3-2 0-1 0-2-1-3l-2-2c-1-1-1-1-1-2l1-1c0-1 1-1 2-1 0 0 1 1 2 1v-1l1 1v19 1l-1-1v-3l-2-2-3-3h-1l2-1z"></path><path d="M410 345l2-2h2c2-1 7-1 9 0 0 1 1 2 1 2 1 1 2 3 3 4 0 3 0 6-1 8h0l-1-1h0c-1-2-5-5-8-5-1 0-3-1-5 0l-1 2h0v-2h0c-2 0-2 0-3 1l-2-1c1-3 1-4 3-6h1z" class="K"></path><path d="M421 348l-2-1v-2c-2 0-2 2-4 0v-1c1-1 3-1 4 0 2 0 4 2 5 4v2h-1c0-1-1-1-2-2z" class="I"></path><path d="M424 345c1 1 2 3 3 4 0 3 0 6-1 8h0l-1-1c0-1 0-3-1-4-2-1-5-2-5-4h2c1 1 2 1 2 2 1 1 1 2 2 2h0c1-1 0-2 0-3s0-2-1-4z" class="Q"></path><path d="M409 345h1c0 1-1 1 0 2l2 2h1v-3h3c1 1 2 1 3 2 0 2 3 3 5 4 1 1 1 3 1 4h0c-1-2-5-5-8-5-1 0-3-1-5 0l-1 2h0v-2h0c-2 0-2 0-3 1l-2-1c1-3 1-4 3-6zm9 12v1l1 1h0l2 1v3c1 0 2 1 2 1 1 2 0 5-1 7-1-1-2 0-4-1v1l-2 2c-1 1-2 1-4 1l-3-3-2-3v-3h1l-1-2 1-2c3-3 6-4 10-4z" class="D"></path><path d="M416 373l-1-1c0-1 0-1 1-2 1 0 1 0 2 1l-2 2z" class="J"></path><path d="M414 361v1c-1 1-1 2-3 2v-1c1-1 1-2 3-2z" class="c"></path><path d="M414 365c0-2 1-2 2-3s1-1 2 0v1c0 1-1 2-2 2h-2z" class="G"></path><path d="M410 368c1 0 3-1 4-1 0 1 1 1 1 1v2c-1 1-2 0-4 0-1 0-1 0-1-1h0v-1z" class="Z"></path><path d="M408 361c2 1 2 1 2 2 0 2-1 2 0 4v1 1h0c-2-1-2-2-3-4h1l-1-2 1-2z" class="B"></path><path d="M418 357v1l1 1h0l2 2h-1-1c-2-1-3-1-5 0-2 0-2 1-3 2h-1c0-1 0-1-2-2 3-3 6-4 10-4z" class="X"></path><path d="M419 359l2 1v3c1 0 2 1 2 1 1 2 0 5-1 7-1-1-2 0-4-1 1 0 1 0 1-1h0c-1 0-1 0-2 1h-1v-1l1-1 1-1c-1-2-2-2-4-2h0 2c1 0 2-1 2-2h1c0 1 1 1 1 0 1-1 0-1 0-2h1l-2-2z" class="F"></path><path d="M418 363h1c0 1 1 2 0 4v1c-1 0-1-1-1-1-1-2-2-2-4-2h0 2c1 0 2-1 2-2z" class="O"></path><path d="M409 396c3-1 4-1 7-1h7c1-1 2-1 2-3v-1h1c1 1 1 2 1 2l1 1c-1 0-2 0-2 1l-1 1c0 1 0 1 1 2l2 2c1 1 1 2 1 3-1 0-2 1-3 2l-2 1h1l3 3 2 2v3l1 1v-1c0 2 0 3 1 5l-1 1h0v-2l-2-1-1 1v1c-1-1-2-2-4-3-2 1-3 2-4 4l-1 1h0-2l-2-2h0 0c-2-2-2-2-2-4v-1-3-12c-2 0-3 0-5 1h-1l1-3-1-1h0 2z" class="H"></path><path d="M407 396h0 2 5c1 1 1 1 3 1h2 2v1 1h-2-1c-1 1-2 3-3 3-1-1-1-1-1-3h-1c-2 0-3 0-5 1h-1l1-3-1-1z" class="O"></path><path d="M422 400l4-1v1l-1 1c-2 1-5 5-7 7l-1 1-1 1v4h-1c-1 0-1 0-2 1v-1-3-12h1c0 2 0 2 1 3 1 0 2-2 3-3h1l3 1z" class="c"></path><path d="M413 411c1-2 1-3 2-4h1v2 1 4h-1c-1 0-1 0-2 1v-1-3z" class="D"></path><path d="M422 400l4-1v1l-1 1c-2 1-5 5-7 7l-1-1c0-1 0-2 1-3 1-2 2-3 4-4z" class="I"></path><defs><linearGradient id="CV" x1="428.42" y1="416.768" x2="419.825" y2="415.28" xlink:href="#B"><stop offset="0" stop-color="#5e5d5e"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#CV)" d="M425 401c0 2 0 2 1 4l-2 1h1l3 3 2 2v3l1 1v-1c0 2 0 3 1 5l-1 1h0v-2l-2-1-1 1v1c-1-1-2-2-4-3-2 1-3 2-4 4l-1 1h0-2l-2-2h0 0c-2-2-2-2-2-4 1-1 1-1 2-1h1v-4l1-1 1-1c2-2 5-6 7-7z"></path><path d="M415 414h1 3l-1 2c-1 1-1 2 0 3h0c0 1 1 1 1 2h0-2l-2-2h0 0c-2-2-2-2-2-4 1-1 1-1 2-1z" class="B"></path><path d="M413 415c1-1 1-1 2-1 0 1 0 2 1 4l-1 1h0 0c-2-2-2-2-2-4z" class="Y"></path><path d="M419 414c1-1 2-1 3-3h-1 0c0-1 1-1 2-2h1c1 0 2 1 3 1l1 1c1 1 1 2 2 3l1 1v1c-2 0-2-2-3-2-1-1-5-1-6-1-2 1-2 3-3 4l-1-1 1-2z" class="E"></path><path d="M425 401c0 2 0 2 1 4l-2 1h1l3 3 2 2v3c-1-1-1-2-2-3l-1-1c-1 0-2-1-3-1h-1c-1 1-2 1-2 2h0 1c-1 2-2 2-3 3h-3v-4l1-1 1-1c2-2 5-6 7-7z" class="Z"></path><path d="M425 401c0 2 0 2 1 4l-2 1h0-2-1l2 2c-1 1-4 1-5 1h-1l1-1c2-2 5-6 7-7z" class="D"></path><path d="M406 377h1c2 1 2 1 3 2l2-1 1 1c1 1 0 1 0 2l1 1c2 0 3-2 4-3h4 0 2c0 1 2 0 2 1l1 2 2 2c0 1-1 1 0 2 0 0 0 1 1 1l-1 1v3l-1 1h1l1 1v1 1c-1 0-2-1-2-1l-1-1s0-1-1-2h-1v1c0 2-1 2-2 3h-7c-3 0-4 0-7 1h-2 0-3c0-1 0-2-1-3l-1-1v-1h1l-1-1h-1l1-2c0-1 0-1 1-2l-1-1-1-2 3-3h2v-3z" class="F"></path><path d="M420 392c0-1 0-1 1-1h0l1-2h1c1 2 1 2 1 3-1 1-1 1-3 1l-1-1z" class="U"></path><path d="M424 379c0 1 2 0 2 1l1 2v5c-1 0-1 1-1 2 0-1 0-2-1-3v-1-2c1 0 1-1 1-2 0 0-2-1-2-2z" class="Y"></path><path d="M422 385c1 1 2 0 3 0v1 2h-1l-1 1h-1c0-1-1-1-1-1l-1-1c0-1 0-1 1-2h1z" class="K"></path><path d="M422 379h2c0 1 2 2 2 2 0 1 0 2-1 2v2c-1 0-2 1-3 0h0l2-2c-1-1-2-1-3-2l1-2h0z" class="D"></path><path d="M407 394l-2-2v-1c2-1 6-1 8-1 1 1 2 1 3 1l2 1 4 3-15-1z" class="h"></path><path d="M403 386c1-1 3-1 4-1h0l1 1 1-1 3 3c2 1 3 1 4 3-1 0-2 0-3-1-2 0-6 0-8 1v1l2 2-4-1-1-1v-1h1l-1-1h-1l1-2c0-1 0-1 1-2z" class="C"></path><path d="M406 377h1c2 1 2 1 3 2l2-1 1 1c1 1 0 1 0 2l1 1c2 0 3-2 4-3h4l-1 2c1 1 2 1 3 2l-2 2h0-1c-1 1-1 1-1 2l1 1s1 0 1 1l-1 2h0c-1 0-1 0-1 1l-1-1-1 1-2-1c-1-2-2-2-4-3l-3-3-1 1-1-1h0c-1 0-3 0-4 1l-1-1-1-2 3-3h2v-3z" class="Y"></path><path d="M406 377h1c2 1 2 1 3 2 0 2 3 5 4 6s1 1 1 2h-1-1v-2c-2-2-4-1-6-2 0-1 0-2-1-2 0 0-1 0-1 1s0 1-1 2l-2-1v2l-1-2 3-3h2v-3z" class="G"></path><path d="M406 377h1l1 2v1h-2v-3z" class="V"></path><path d="M410 379l2-1 1 1c1 1 0 1 0 2l1 1c2 0 3-2 4-3h4l-1 2c1 1 2 1 3 2l-2 2h0-1c-1 1-1 1-1 2l1 1s1 0 1 1l-1 2h0c-1 0-1 0-1 1l-1-1-3-4v-4c-1 1-1 2-2 2-1-1-4-4-4-6z" class="Q"></path><path d="M421 385v-1c-1-1-1-1-2-1h-2l-1-1 2-1h3c1 1 2 1 3 2l-2 2h0-1z" class="J"></path><defs><linearGradient id="CW" x1="369.159" y1="619.159" x2="412.928" y2="594.646" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#343434"></stop></linearGradient></defs><path fill="url(#CW)" d="M412 553c2-1 4-1 6 0l1 1 1 1h1c4 2 7 4 9 7v-3c-1-2-4-2-4-4v-1l1 1c1 0 2 0 2 1h2s1 0 1 1l2 21c0 9 2 20 6 28l3 7h1v-3-2c1 2 2 6 3 7 1 2 2 3 3 4 1 3 3 6 5 7 0 0 2 2 3 2-1 1-2 1-2 1 0 1 0 1-1 2h0c0-1 0-1-1-2-3 1-5 4-8 4h-12l-9 1h-21-6l-13-1c-4 0-8 0-10-2l-1-1h-4c1-1 2-2 3-2 16-12 20-30 23-48 1-2 1-3 1-5l2-2v12c0 2 0 2 1 3s1 1 1 2l1 1c2 1 3 2 5 2h0c0-2-2-2-3-4 0-2 1-4 0-6-1-1-1-1-2-1-1-1-1-3-1-4v-3h0l-1-1v-1c0-1 1-4 1-5 1 0 1-1 1-2h-1c0-1 1-1 1-2l1-1h-1c0-1 1-2 2-3v-1l-1-1h0v-1c2-1 4-3 5-3h1l1-1h2z"></path><path d="M398 589c-1-2-1-4-1-6h0l1 2h1c0 2 0 2 1 3s1 1 1 2l1 1h-1-1l-1-2h-1zm1 2v1c3 3 5 5 8 7-2 0-4 0-6-2s-2-3-2-6z" class="T"></path><path d="M397 622h1c1 1 3 1 4 2v1c-1 2-3 3-4 3l-5 1c1-1 1-2 2-2 1-2 2-3 2-5z" class="L"></path><path d="M402 632c1-1 1-2 1-4 0-1 1-2 1-3 1-2 1-5 0-6v-1-1l-1-1c0-2-2-4-4-5h0c-2-1-3-1-4-2h-2c1-1 1-1 2-1 2 1 5 0 8 0 1 0 2 1 3 2h0c0 1 1 2 1 3 1 1 0 1 0 2 1 3 1 8 0 11-2 3-3 4-5 6z" class="T"></path><path d="M406 610h1c4 4 5 7 5 12 0 3-1 7-4 9-1 1-1 2-3 2 0 0-1 0-1 1h-6c2-1 3-1 4-2 2-2 3-3 5-6 1-3 1-8 0-11 0-1 1-1 0-2 0-1-1-2-1-3h0z" class="E"></path><path d="M411 610l3 3c2 0 3 1 5 3v2h0v3l1 3c2 0 3 0 4-1 1 2 2 3 2 5 0 1-1 2-2 3l2 2c-1 0-1 1-1 1h-21c0-1 1-1 1-1 2 0 2-1 3-2 3-2 4-6 4-9 0-5-1-8-5-12h4z" class="P"></path><path d="M414 613c2 0 3 1 5 3v2h0v3c-2-3-3-6-5-8z" class="U"></path><path d="M424 623c1 2 2 3 2 5 0 1-1 2-2 3-1-2-2-5-4-7 2 0 3 0 4-1z" class="e"></path><path d="M415 620c1 3 4 6 4 9-1 2-1 2-2 2h-1c-1-1-2-1-3-2 0-3 1-6 2-9z" class="W"></path><path d="M397 604c-1 0-1 0-1-1l1-1c1 0 4-2 5-2 3 0 8 2 11 3h0 2c1 0 1 1 2 1l1-2h2l1 1h2v3h3l2 1 1-1c1 1 1 1 2 0v-2c1 1 1 1 1 2s0 2 1 3l1 1 2 1v1h1 2c1 0 1 0 2 1v1l-1 1-1 1v1h2v2h0v-2l2-2v-2h1v-3-2c1 2 2 6 3 7 1 2 2 3 3 4 1 3 3 6 5 7 0 0 2 2 3 2-1 1-2 1-2 1 0 1 0 1-1 2h0c0-1 0-1-1-2-3 1-5 4-8 4h-12l-9 1s0-1 1-1l-2-2c1-1 2-2 2-3 0-2-1-3-2-5-1 1-2 1-4 1l-1-3v-3h0v-2c-2-2-3-3-5-3l-3-3c-2-2-5-4-8-5-2 0-4-1-6-1z" class="a"></path><path d="M430 621h1c1 0 1 0 2 1h0v2h-2l-1-3z" class="B"></path><path d="M430 615c0 1 1 2 1 3h2c1 1 0 2 0 4h0c-1-1-1-1-2-1h-1 0v-6z" class="R"></path><path d="M429 609l-2 1v1c0 1 1 2 1 2h1c0 2 0 3-1 5h0l-2-2c-1 0-2-2-2-3-1-2-2-4-3-5-2-1-3-2-5-4l4 2 2 3c1-2 1-2 1-3h3l2 1v1l1 1z" class="X"></path><path d="M423 606h3l2 1v1h-4 0-1c1 1 1 1 1 2l-2-1c1-2 1-2 1-3z" class="O"></path><path d="M431 604c1 1 1 1 1 2s0 2 1 3l1 1 2 1v1h1 2c1 0 1 0 2 1v1l-1 1-1 1v1h2v2h0c-2 1-2 2-2 3v1h-1c0 1 0 1 1 2v2l-3-1-1-2h-1v2 1c0-1 0-2-1-3v-2c0-2 1-3 0-4h-2c0-1-1-2-1-3 0-2 1-4 1-5l-1-1h-1l-1-1v-1l1-1c1 1 1 1 2 0v-2z" class="H"></path><path d="M429 606c1 1 1 1 2 0v4l-1-1h-1l-1-1v-1l1-1z" class="B"></path><path d="M436 626v-2h-1l-1-1h0c1-1 1-2 2-2s1 1 2 1h1v1h-1c0 1 0 1 1 2v2l-3-1z" class="R"></path><path d="M436 611v1h1 2c1 0 1 0 2 1v1l-1 1-1 1v1h2v2h0-1c-1 0-1 0-2-1 0-1 0-3 1-4l1-1h-1c-1 0-3 1-3 2-1 1-1 1-2 1v-1c-1 0-1-1-2-1v-2l1-1c0 1 1 1 2 1l1-1z" class="B"></path><path d="M397 604v-1l3-1c4-1 11 2 14 4 7 4 7 11 10 17-1 1-2 1-4 1l-1-3v-3h0v-2c-2-2-3-3-5-3l-3-3c-2-2-5-4-8-5-2 0-4-1-6-1z" class="C"></path><path d="M403 605v-1h1 1l1 1c2 0 3 1 4 2 2 1 3 3 5 4l1-1c1 1 2 3 3 5h0-1 0l1 1c-2-2-3-3-5-3l-3-3c-2-2-5-4-8-5z" class="I"></path><path d="M444 608c1 2 2 6 3 7 1 2 2 3 3 4 1 3 3 6 5 7 0 0 2 2 3 2-1 1-2 1-2 1 0 1 0 1-1 2h0c0-1 0-1-1-2-3 1-5 4-8 4h-12l-9 1s0-1 1-1l-2-2c1-1 2-2 2-3v-1c0-1 1-2 0-3s-1-3-1-4h1c0 1 1 3 2 4 2 2 3 2 3 6v1c2-1 2-1 3-2h0l1-1c-1 0-1 0-1-1v-1-2h1l1 2 3 1v-2c-1-1-1-1-1-2h1v-1c0-1 0-2 2-3h0 0v-2l2-2v-2h1v-3-2z" class="M"></path><path d="M441 619v1 4c-1 0-1-1-2-1v-1c0-1 0-2 2-3h0z" class="a"></path><path d="M434 627v-1-2h1l1 2 3 1 2 2h0c-2 0-4-1-6-1-1 0-1 0-1-1z" class="K"></path><path d="M426 628v-1c1 2 2 4 4 5v1h4l-9 1s0-1 1-1l-2-2c1-1 2-2 2-3z" class="J"></path><path d="M444 608c1 2 2 6 3 7 1 2 2 3 3 4 1 3 3 6 5 7 0 0 2 2 3 2-1 1-2 1-2 1 0 1 0 1-1 2h0c0-1 0-1-1-2-3 1-5 4-8 4l-3-1h1c-1-1-1-2 0-4 0-1 0-2-1-3h1l2 3 1-1c-1-1-1-2-2-2v-1c-1-1-1-2-2-2v1h-1c0-1-1-2-1-3v-1h0v-2l2-2v-2h1v-3-2z" class="d"></path><path d="M444 608c1 2 2 6 3 7 1 2 2 3 3 4 1 3 3 6 5 7 0 0 2 2 3 2-1 1-2 1-2 1 0 1 0 1-1 2h0c0-1 0-1-1-2l-1-1c-2-1-3-3-4-4l-6-9v-2h1v-3-2z" class="B"></path><path d="M412 553c2-1 4-1 6 0l1 1 1 1h1c4 2 7 4 9 7v-3c-1-2-4-2-4-4v-1l1 1c1 0 2 0 2 1h2s1 0 1 1l2 21c0 9 2 20 6 28l3 7v2l-2 2v2h0v-2h-2v-1l1-1 1-1v-1c-1-1-1-1-2-1h-2-1v-1l-2-1-1-1c-1-1-1-2-1-3s0-1-1-2v2c-1 1-1 1-2 0l-1 1-2-1h-3v-3h-2l-1-1h-2c-1-1-2-1-3-2-2 1-6-1-8-1-3-2-5-4-8-7v-1l-1-2h1l1 2h1 1c2 1 3 2 5 2h0c0-2-2-2-3-4 0-2 1-4 0-6-1-1-1-1-2-1-1-1-1-3-1-4v-3h0l-1-1v-1c0-1 1-4 1-5 1 0 1-1 1-2h-1c0-1 1-1 1-2l1-1h-1c0-1 1-2 2-3v-1l-1-1h0v-1c2-1 4-3 5-3h1l1-1h2z" class="W"></path><path d="M430 592l1 1v1c-1 1-1 1-2 1v-2l1-1z" class="f"></path><path d="M409 586h1c0 1 1 1 1 0 1 1 0 2 2 2l-1 1c0 1 0 1-1 2h0l-2-2v-3z" class="O"></path><path d="M426 587l2-1c2 2 0 4 2 6l-1 1c-1 1-1 2-1 3v1c0 1 0 2 1 3l-1-1c-1 0-2-1-2-2v-1c-1 0-1 0-1-1h2v1l1-1c-1-3 0-2 1-5l-1-1-3-2h1z" class="Y"></path><path d="M410 585c2-2 3-3 5-3h4l2 1c2 1 3 3 5 4h-1l-1 1s0 1 1 1h-1-2c-1 1-2 2-2 3-1 1-2 0-3 0-1 1-2 2-4 2v-1l1-1c0-1-1-3-1-4-2 0-1-1-2-2 0 1-1 1-1 0h-1l1-1z" class="F"></path><path d="M419 582l2 1v1 2l-1-1c-1-1-1-1-1-3z" class="J"></path><path d="M415 587h3c1 1 1 2 1 3l-2 2c-1 1-2 2-4 2v-1l1-1h2v-2s0-1-1-1l-1-1 1-1z" class="K"></path><path d="M410 585h1c1 0 2-1 3-1h2v2l-1 1-1 1 1 1c1 0 1 1 1 1v2h-2c0-1-1-3-1-4-2 0-1-1-2-2 0 1-1 1-1 0h-1l1-1z" class="Y"></path><path d="M430 562v-3c-1-2-4-2-4-4v-1l1 1c1 0 2 0 2 1h2s1 0 1 1l2 21c0 9 2 20 6 28l3 7v2l-2 2v2h0v-2h-2v-1l1-1 1-1v-1c-1-1-1-1-2-1h-2-1v-1l-2-1-1-1c-1-1-1-2-1-3s0-1-1-2h0v-3-2c1-1 0-2 0-4 1-1 1-1 3-2v-1c-1-1 0-2-1-3h-3c1-2 1-4 1-6v-8c0-2-1-3-1-5v-2h0c-1-1-1-2-2-3v-1h0l1 1 2-1c0-1 0-1-1-2h0z" class="M"></path><path d="M434 593v4c0 1 1 3 1 5 0 1 0 1 1 2l1 3c1 1 1 2 0 4v1h-1v-1l-2-1-1-1c-1-1-1-2-1-3s0-1-1-2h0v-3-2c1-1 0-2 0-4 1-1 1-1 3-2z" class="G"></path><path d="M431 599h1l1 1c0 1 0 1 1 2v1c-1 1 0 1-1 1l-1 1h1c0 1 1 2 1 4v1l-1-1c-1-1-1-2-1-3s0-1-1-2h0v-3-2z" class="B"></path><path d="M425 587l3 2 1 1c-1 3-2 2-1 5l-1 1v-1h-2c0 1 0 1 1 1v1c0 1 1 2 2 2l1 1v2l2-1v3h0v2c-1 1-1 1-2 0l-1 1-2-1h-3v-3h-2l-1-1h-2c-1-1-2-1-3-2-2 1-6-1-8-1-3-2-5-4-8-7v-1l-1-2h1l1 2h1 1c2 1 3 2 5 2 0 1 0 1 1 1l1-1h1 0l1-2h0c1-1 1-1 1-2l1-1c0 1 1 3 1 4l-1 1v1c2 0 3-1 4-2 1 0 2 1 3 0 0-1 1-2 2-3h2 1c-1 0-1-1-1-1l1-1z" class="G"></path><path d="M424 589h1v1l-1 2c-1 1-2 3-3 5 0 0-1 0-1 1-1-1-1-1-3-1v-1l-2 1h-1c1 0 2-1 2-1 1-1 2 0 3-1 1 0 3-2 3-4 0 0 0-1 1-1h0l1-1z" class="Z"></path><path d="M399 592h2c2 2 6 5 10 6 1 0 1 0 2-1l1 1v-1h1l2-1v1c2 0 2 0 3 1l-3 1v1h5v1h2 0v1l-1 1h-2l-1-1h-2c-1-1-2-1-3-2-2 1-6-1-8-1-3-2-5-4-8-7z" class="B"></path><path d="M415 600h2c2 1 3 1 5 1h2 0v1l-1 1h-2l-1-1h-2c-1-1-2-1-3-2z" class="G"></path><path d="M425 587l3 2 1 1c-1 3-2 2-1 5l-1 1v-1h-2c0 1 0 1 1 1v1c0 1 1 2 2 2l1 1v2l2-1v3h0v2c-1 1-1 1-2 0l-1 1-2-1h-3v-3l1-1v-1h0-2v-1h-5v-1l3-1c0-1 1-1 1-1 1-2 2-4 3-5l1-2v-1c-1 0-1-1-1-1l1-1z" class="C"></path><path d="M425 587l3 2-1 2c-1 0-1 0-2-1v-1c-1 0-1-1-1-1l1-1z" class="I"></path><path d="M420 598c0-1 1-1 1-1 1 0 1 0 2-1v1l-1 2h1c1-1 2-1 3-2 0 1 1 2 2 2-2 1-4 1-6 1h-5v-1l3-1z" class="D"></path><path d="M428 599l1 1v2l2-1v3h0v2c-1 1-1 1-2 0l-1 1-2-1h-3v-3l1-1v-1h0-2v-1c2 0 4 0 6-1z" class="Z"></path><path d="M426 606l-1-1h-1v-1c1 0 2-1 4-1v1l-1 1 2 1-1 1-2-1z" class="J"></path><path d="M431 601v3h0v2c-1 1-1 1-2 0l-2-1 1-1 1-2 2-1z" class="G"></path><defs><linearGradient id="CX" x1="422.852" y1="566.466" x2="410.96" y2="558.311" xlink:href="#B"><stop offset="0" stop-color="#555455"></stop><stop offset="1" stop-color="#716f70"></stop></linearGradient></defs><path fill="url(#CX)" d="M412 553c2-1 4-1 6 0l1 1 1 1h1c4 2 7 4 9 7h0c1 1 1 1 1 2l-2 1-1-1h0v1c1 1 1 3 1 4h0c-1 2-1 5-1 7 1 1 1 1 1 2-1 1-1 5-2 6v-1l-1-1c-2-2-4-2-6-3-3-1-7 0-10 2-1 2-3 3-4 5v1l-1-1c0-1 0-2 1-3v-1l-2-1h0l-3-3v-3h0l-1-1v-1c0-1 1-4 1-5 1 0 1-1 1-2h-1c0-1 1-1 1-2l1-1h-1c0-1 1-2 2-3v-1l-1-1h0v-1c2-1 4-3 5-3h1l1-1h2z"></path><path d="M420 555h1c4 2 7 4 9 7h0v1c-2-1-4-3-7-4-3 0-6 0-10 1l-4 1-1-1c3-1 6-2 9-2h3 2c-1-1-2-2-3-2l1-1z" class="I"></path><path d="M413 564l1-1c3-1 6 0 9 1l1 1c2 2 3 3 3 5 0 1 0 1-1 1l-2-3-1 1-2-1h-1l-1-1h-2-2v-3h1-2-1z" class="K"></path><path d="M416 564c1 0 3 0 4 1-1 1-2 1-3 2h-2v-3h1z" class="B"></path><path d="M420 565c2 1 3 2 4 3l-1 1-2-1h-1l-1-1h-2c1-1 2-1 3-2z" class="F"></path><path d="M423 559c3 1 5 3 7 4v-1c1 1 1 1 1 2l-2 1-1-1h0v1c1 1 1 3 1 4h0c-1 2-1 5-1 7 1 1 1 1 1 2-1 1-1 5-2 6v-1-6c0-1 0-2-1-3v-1h1v-1-2c0-2-1-3-3-5h1c0-3 0-2-1-3s-1-2-1-3z" class="O"></path><path d="M414 564h2-1v3h2 2l1 1h-2 0l1 1v1h-2c0-1 0-1-1-1-2 0-4 4-5 5v1h-2l-2-2v-1-1-1c0-1-1-3 0-4 0-1 1-1 2-2v1l4-1h1z" class="H"></path><path d="M414 564h2-1v3h2 2l1 1h-2 0l1 1v1h-2c0-1 0-1-1-1-1-1-2-1-3-1s-1 1-2 1v1c-1 0-1 0-2-1v-1c-1-1-2-1-2-2s1-1 2-2v1l4-1h1z" class="E"></path><path d="M413 564h1v2c-1 1-3 0-4 0h0l-1-1 4-1z" class="O"></path><path d="M412 553c2-1 4-1 6 0l1 1 1 1-1 1c1 0 2 1 3 2h-2-3c-3 0-6 1-9 2-1 1-2 1-3 2v1h2 0c0 1-1 2-1 4 0 1-1 2-1 3l1 1v2l-1-1c-1 0-2 0-3 1 0 0-1 1-2 1v-1c0-1 1-4 1-5 1 0 1-1 1-2h-1c0-1 1-1 1-2l1-1h-1c0-1 1-2 2-3v-1l-1-1h0v-1c2-1 4-3 5-3h1l1-1h2z" class="Y"></path><path d="M402 566l1-1 2 2c-1 1-2 2-1 3l1 2c-1 0-2 0-3 1 0 0-1 1-2 1v-1c0-1 1-4 1-5 1 0 1-1 1-2z" class="d"></path><path d="M412 553c2-1 4-1 6 0l1 1 1 1-1 1c1 0 2 1 3 2h-2-3c-2 0-2 0-4-1-3 0-6 1-9 3v-1l-1-1h0v-1c2-1 4-3 5-3h1l1-1h2z" class="E"></path><path d="M409 555l6-1c2 1 4 2 5 4h-3c-2 0-2 0-4-1v-1l-4-1z" class="B"></path><path d="M403 557h1 1c1-1 2-1 3-1l1-1 4 1v1c-3 0-6 1-9 3v-1l-1-1h0v-1z" class="G"></path><path d="M417 570h2v-1l-1-1h0 2 1l2 1 1-1 2 3c1 0 1 0 1-1v2 1h-1v1c1 1 1 2 1 3v6l-1-1c-2-2-4-2-6-3-3-1-7 0-10 2-1 2-3 3-4 5v1l-1-1c0-1 0-2 1-3v-1l-2-1h0l-3-3v-3h0l-1-1c1 0 2-1 2-1 1-1 2-1 3-1l1 1c0 2 1 3 2 4 1 0 2-1 2-2h1v-1c1-1 3-5 5-5 1 0 1 0 1 1z" class="J"></path><path d="M421 575h0c1 1 1 1 2 1h0v1l-1 1c-1 0-1-1-2-1v-1l1-1z" class="K"></path><path d="M424 568l2 3c1 0 1 0 1-1v2h0l-1-1-1 1c-1-1-3-2-4-4l2 1 1-1z" class="D"></path><path d="M414 577c1-1 3-3 5-4 0 1 1 2 1 3-2 1-4 1-6 1z" class="E"></path><path d="M425 572l1-1 1 1h0v1h-1v1c1 1 1 2 1 3v6l-1-1c0-2-1-3-1-5h0l1-1h0c0-1-1-1-1-2l-1-1 1-1z" class="F"></path><defs><linearGradient id="CY" x1="401.486" y1="576.362" x2="410.576" y2="579.172" xlink:href="#B"><stop offset="0" stop-color="#3e3d3d"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#CY)" d="M416 569c1 0 1 0 1 1l-1 2v1l2-1h0l1 1c-2 1-4 3-5 4s-5 2-5 3h0c1 0 1 0 1 1-1 2-3 3-4 5v1l-1-1c0-1 0-2 1-3v-1l-2-1h0l-3-3v-3h0l-1-1c1 0 2-1 2-1 1-1 2-1 3-1l1 1c0 2 1 3 2 4 1 0 2-1 2-2h1v-1c1-1 3-5 5-5z"></path><path d="M401 575h1c1 1 3 2 3 4 0 1-1 1-1 2l-3-3v-3z" class="E"></path><path d="M416 569c1 0 1 0 1 1l-1 2-1 1v1l-2 2-1 1-1-1v-2c1-1 3-5 5-5z" class="R"></path><path d="M538 213h5 6l1 1c-1 1-3 1-4 1h-30l-37 1c-6 0-11 1-17 1-3 1-9 1-11 4-2 2-4 5-5 7-5 13-8 29-10 43-2 27-1 55-1 82v142 40 16l2 28-3-1-2-21c0-1-1-1-1-1h-2c0-1-1-1-2-1l-1-1v1c0 2 3 2 4 4v3c-2-3-5-5-9-7h-1l-1-1-1-1c-2-1-4-1-6 0l-1-1h1c1-2 1-3 1-4l1-3c0-2 1-3 1-5l-1-4 1-2c0-2-1-4-1-6l-1-1v-1s0-1-1-1v-1l-1-2h0c-1-1-1-2-2-3l-1-2v-3c-1 0-1-1-2-2h-1c-1-1-2-3-2-4 1-1 1-1 1-2h0c1-2 1-3 1-5h0c-1-4-2-22 0-25 1-2 3-3 5-4s3-1 6-2c2 0 4 1 6 2v-2l-2-2c-1-1 0-1 0-2-1-1-1-2-1-3-1-2-1-6-1-8v-1s-1-1-1-2c0 0 0-1 1-2l1-3c-1-1-1-1-2-1v-1h0c1-2 0-4 0-6v-1c0-1-1-3-1-4v-1c0-3 1-6 2-9-1 0-1-1-2-1v-1l1-1h2 0l1-1c1-2 2-3 4-4 2 1 3 2 4 3v-1l1-1 2 1v2h0l1-1c1-6 0-13 0-20v-38c-1 2 0 8-1 9l-1-2v-13-6c-1-2-1-3-1-5l-1 2-1-2c-3-3-5-4-8-5l-1-1c-2 0-4 0-5-1l-4 1c0 2 0 2-2 3-1 0-2 2-3 2l-1-2c0-1 1-2 0-3l-2-2v-1c2-2 5-4 7-6v-3h-2l-4 4h-1v-2c1-2 1-4 2-6 0-1 0-2 1-3h0l-1-3v-1c1-2 2-5 3-7l2-2c-1-1-1-2-1-3-1 0-1 0-1-1h0l1-1c-1-2 0-3-1-5-1-1-1-1-1-2v-1c-1-1-2-3-3-4l1-1c1-1 0-3 0-5l-1-2h-1c0-1-1-1-1-1-1-1 0-2-1-3v-10l-1-8v-3l-2-8v13l-2-13c0-6-3-13-5-18l-1-4 2-2 14-1h3 4 8 4c1-1 3-1 4 0h1l2 1h1 2l-1-1c0-1 1-1 2-1v2h1c1-1 2-1 4 0 0 2-1 3-1 5h0c0 1-1 1-1 2l2 1h0c1 1 1 2 2 3 0-3 2-8 3-11l1-1 2-2c0-1 1-2 3-3 2-2 7-2 11-3 4 0 10-1 14-1l56-1h7z" class="b"></path><path d="M430 436l1 1v1c0-2 0-7 1-9v11c-2 0-2 1-3 2 0-1-1-2-2-2 2-1 3-2 3-4z" class="X"></path><path d="M429 288c2-1 2-1 3-3v10c-1 0-2-1-2-2v-2c-1-2-1-2-2-2l1-1z" class="Y"></path><path d="M432 548v1c1 1 1 3 1 4l1 7v7c0 2 0 5 1 7v2l1 1h0c0-1 0-3-1-4v-2c1-2 0-3 0-4 0-2 1-5 0-6 0-3-1-5 0-7v-3l2 28-3-1-2-21v-9z" class="C"></path><path d="M428 419v-1l1-1 2 1v2h0l1 9c-1 2-1 7-1 9v-1l-1-1h-3l-1-2h0v-2c0-1 2-1 2-2-1-1-1-1-2-1l3-2c1-2 1-3 1-5l-2-3z" class="G"></path><path d="M419 421l1-1c1-2 2-3 4-4 2 1 3 2 4 3l2 3c0 2 0 3-1 5l-3 2c1 0 1 0 2 1 0 1-2 1-2 2v2h0c-1 1-2 2-3 2h-1c-1 2-2 2-3 3h-2v-1c0-1-1-3-1-4v-1c0-3 1-6 2-9-1 0-1-1-2-1v-1l1-1h2 0z" class="Z"></path><path d="M425 423v-1c1 0 1-1 2-2l2 2v3c-1 1-3-1-4-1v-1z" class="R"></path><path d="M422 426v1c0 2 0 5-2 7l-1 1-1 1c0-2 0-4 1-5v-1-1c0-1-1-1 0-1h1v1h1v-1c0-1 0-1 1-2zm-3-5l1-1c1-2 2-3 4-4 2 1 3 2 4 3l2 3h-1l-2-2c-1 1-1 2-2 2v1 1h-2c0-1-1-2-2-3-1 1-1 3-2 4-1-2 0-2 0-4h0z" class="L"></path><path d="M422 419c1 0 2-1 3 0 1 0 2 1 2 1-1 1-1 2-2 2v1c-1-1-1-2-2-3l-1-1z" class="H"></path><path d="M422 419l1 1c1 1 1 2 2 3v1h-2c0-1-1-2-2-3l1-2z" class="B"></path><path d="M422 426v-1c2 1 2 3 3 4 0 1-1 2-2 2 1 1 1 2 1 2 1 0 1 0 2-1v2h0c-1 1-2 2-3 2h-1c-1 2-2 2-3 3h-2v-1c0-1 1-2 1-2l1-1 1-1c2-2 2-5 2-7v-1z" class="D"></path><path d="M422 436c0-2 0-3 1-5 1 1 1 2 1 2 1 0 1 0 2-1v2h0c-1 1-2 2-3 2h-1zm7-190l1 1v5c1 1 1 2 1 3v1-3h1v-2c1 0 2 0 3 1h1l-3 22-1 8-1-1v-3l-2-1c0-1 0-1-1-2v2c-1 1-1 2-2 3v-1c0-1 1-2 1-4 0-1 0-2 1-3h0v-1c0-3 1-4 1-6l-2-3h1 0 1 0l1-1c-1-1-2-1-2-1v-2c-2-1-3-2-5-2 0 0-1-1-2-1h0l1-1 3-3c-1-1-1-2-1-3l2-1c1 0 2-1 3-1h0z" class="B"></path><path d="M430 274c-1-2-1-2-1-3l1-2 1 1v1c0 2 0 2-1 3z" class="E"></path><path d="M431 271l1-1c1 1 1 2 1 4l-1 8-1-1v-3l-2-1 1-1-1-1h-1c1-1 1-1 2-1 1-1 1-1 1-3z" class="O"></path><path d="M429 246l1 1v5c1 1 1 2 1 3v1 1c1 0 2-1 3 0 0 1 0 1-1 1l-1 1c-1 1-1 3-1 4v4h-1v-1c0-1 0-2-1-3v-1l1-1c-1-1-2-1-2-1v-2c-2-1-3-2-5-2 0 0-1-1-2-1h0l1-1 3-3c-1-1-1-2-1-3l2-1c1 0 2-1 3-1h0z" class="C"></path><path d="M426 247c1 0 2-1 3-1-1 2-2 3-4 5-1-1-1-2-1-3l2-1z" class="Q"></path><path d="M422 254c2 0 3 0 5 1 1 1 2 2 3 4h0 1c-1-2-1-3-2-4s-1-2-2-3v-1h1 0c0 2 1 3 3 4v1 1c1 0 2-1 3 0 0 1 0 1-1 1l-1 1c-1 1-1 3-1 4v4h-1v-1c0-1 0-2-1-3v-1l1-1c-1-1-2-1-2-1v-2c-2-1-3-2-5-2 0 0-1-1-2-1h0l1-1z" class="E"></path><path d="M423 223c1-1 3-1 4 0h1l2 1h1 2l-1-1c0-1 1-1 2-1v2h1c1-1 2-1 4 0 0 2-1 3-1 5h0c0 1-1 1-1 2l2 1h0c1 1 1 2 2 3l-5 17h-1c-1-1-2-1-3-1v2h-1v3-1c0-1 0-2-1-3v-5l-1-1h0c-1 0-2 1-3 1h-2l1-1c2-2 3-3 4-5v-1l-1-1 1-1v-1c-1-1-1-2-2-3s-1-2-2-4h1v-1c-1 0-2-1-2-1l-1-1c1 0 2-1 2-1-1 0-4-1-6-2v-1h4z" class="F"></path><path d="M429 238h0c1-2 0-5 2-7 0 1 0 2 1 4v1c0 1-1 3-1 3l-2 2v-1l-1-1 1-1z" class="D"></path><path d="M434 224h1s1 1 2 1v1c-1 1-1 2-2 2h-1c-2-1-2-1-2-2l1-1c0-1 0-1 1-1z" class="I"></path><path d="M431 239v5l-2 2h0c-1 0-2 1-3 1h-2l1-1c2-2 3-3 4-5l2-2z" class="Y"></path><path d="M419 223h4 0c1 0 2 1 3 1 2 1 3 2 4 4h-1c-1 0-2-1-4-2-1 0-4-1-6-2v-1z" class="U"></path><path d="M432 244l1-1c1 0 2 0 3 1h1l-1 1c1 2 0 4-1 7-1-1-2-1-3-1 0-3-1-5 0-7z" class="J"></path><path d="M432 244l1-1c1 0 2 0 3 1h1l-1 1s-1 1-2 1l-1-1-1-1z" class="L"></path><path d="M438 229h0c0 1-1 1-1 2l2 1h0c1 1 1 2 2 3l-5 17h-1c1-3 2-5 1-7l1-1v-2h-2c0-1 1-4 1-5-1 1-2 2-3 4v-1c0-2 0-2 2-4h1l1-1c0-1 0-2-1-3v-1l2-2z" class="Z"></path><path d="M423 290l4-7c1 0 1-1 1-1v-2c1 0 1 1 2 2l1-1 1 1v3c-1 2-1 2-3 3l-1 1c1 0 1 0 2 2v2c0 1 1 2 2 2v39 17 10c-1 2 0 8-1 9l-1-2v-13-6c-1-2-1-3-1-5l-1 2-1-2c-3-3-5-4-8-5l-1-1c-2 0-4 0-5-1l-4 1c0 2 0 2-2 3-1 0-2 2-3 2l-1-2c0-1 1-2 0-3l-2-2v-1c2-2 5-4 7-6l3-2c1-1 1-2 1-3v-1h0c-1 0-1 1-2 0h0l1-2h1l2-3c0-1 1-2 1-2v-1-1h-1 0l-1-1 1-6c1-1 1-3 2-4 1 0 1 1 2 1 1-1 2-3 2-5l1-1 1-4h0l1 1 1-1c-1-1-1-2-3-2l2-2z" class="G"></path><path d="M419 333c1 0 2-1 2-2 2 0 3-1 5 0l1 1h0-1c-1 0-2 1-3 1h-2c-1 1-1 0-2 0z" class="b"></path><path d="M426 331v-1-1h1c1 1 1 3 1 4l1 1h0l-1 1c0 1 1 3 1 4v5c-1-1-1-3-1-5-1-1-2-3-3-3-1-1-2 0-3-1l1-2c1 0 2-1 3-1h1 0l-1-1z" class="H"></path><defs><linearGradient id="CZ" x1="405.583" y1="337.898" x2="418.833" y2="324.068" xlink:href="#B"><stop offset="0" stop-color="#5e5d5e"></stop><stop offset="1" stop-color="#787776"></stop></linearGradient></defs><path fill="url(#CZ)" d="M414 328c2 0 3 0 4-1s3-2 5-3c-1 1-2 3-3 4 0 1 0 1-1 1s-3 2-5 3l-6 3c-1 1-2 3-4 2v-1h-2c5-2 9-5 12-8z"></path><path d="M413 337c2-1 4-3 6-4 1 0 1 1 2 0h2l-1 2c1 1 2 0 3 1 1 0 2 2 3 3 0 2 0 4 1 5l-1 2-1-2c-3-3-5-4-8-5l-1-1c-2 0-4 0-5-1z" class="J"></path><path d="M418 338h0c1-1 2-1 3-2 1 0 1 0 1 1 1 2 6 1 5 6v1c-3-3-5-4-8-5l-1-1z" class="C"></path><path d="M427 311v6c-1 3-2 6-4 7h0c-2 1-4 2-5 3s-2 1-4 1c-3 3-7 6-12 8l-1-1c2-2 5-4 7-6l3-2c1-1 1-2 1-3v-1h0c-1 0-1 1-2 0h0l1-2c0 1 0 1 1 1 1 1 1 1 2 0v-1h1c0-1 1-1 2-1 2 0 2 0 3-1l3 1c2-2 3-4 3-7l1-2z" class="I"></path><path d="M411 321c0 1 0 1 1 1 1 1 1 1 2 0v-1h1c0-1 1-1 2-1l1 3h-1c-1 0-1 0-1-1-1 1-1 2-1 2 0 1-3 2-4 3 1-1 1-2 1-3v-1h0c-1 0-1 1-2 0h0l1-2z" class="Q"></path><path d="M427 311v6c-1 3-2 6-4 7h0c-2 1-4 2-5 3s-2 1-4 1c3-2 6-5 9-8 2-2 3-4 3-7l1-2z" class="E"></path><path d="M423 290l4-7c1 0 1-1 1-1v-2c1 0 1 1 2 2l1-1 1 1v3c-1 2-1 2-3 3l-1 1c1 0 1 0 2 2v2c0 1-1 3-1 4-1 1-1 3-1 4v9c0 2 0 5-1 7v-6h0v-4h-1 0l-2-7c0-2 0-3-1-5l1-1c-1-1-1-2-3-2l2-2z" class="B"></path><path d="M424 294c1 0 1-1 2-1v1 4h0l-2 2c0-2 0-3-1-5l1-1z" class="D"></path><path d="M431 281l1 1v3c-1 2-1 2-3 3l-1-2c0-1 1-3 2-4l1-1z" class="G"></path><path d="M424 300l2-2c1 4 2 9 1 13v-4h-1 0l-2-7z" class="J"></path><path d="M428 289c1 0 1 0 2 2v2c0 1-1 3-1 4h-1c0-2-2-4-2-6 0 0 1-2 2-2z" class="L"></path><path d="M422 294h0l1 1c1 2 1 3 1 5l2 7h0 1v4h0l-1 2c0 3-1 5-3 7l-3-1c-1 1-1 1-3 1-1 0-2 0-2 1h-1v1c-1 1-1 1-2 0-1 0-1 0-1-1h1l2-3c0-1 1-2 1-2v-1-1h-1 0l-1-1 1-6c1-1 1-3 2-4 1 0 1 1 2 1 1-1 2-3 2-5l1-1 1-4z" class="U"></path><path d="M420 319c2-2 4-4 5-6 1-1 0-1 1-1v1c0 3-1 5-3 7l-3-1z" class="L"></path><path d="M423 300c1 4 0 11-2 14-2 4-6 5-9 7l2-3c0-1 1-2 1-2h2 0 1c2-3 3-6 4-9 0-2 1-4 1-7z" class="F"></path><path d="M418 304l-1 2c0 1-1 2 0 3h0c3-2 4-7 5-11l1 1v1c0 3-1 5-1 7-1 3-2 6-4 9h-1 0-2v-1-1h-1 0l-1-1 1-6c1-1 1-3 2-4 1 0 1 1 2 1z" class="B"></path><path d="M414 314c0-1 1-2 2-2 0 1 1 1 0 2l-1 1v-1h-1 0z" class="G"></path><path d="M414 265c1-2 3-3 5-4 1 1 2 1 3 1h1l2 1c1 0 2 1 2 2h2c0 2-1 3-1 6v1h0c-1 1-1 2-1 3 0 2-1 3-1 4v1c1-1 1-2 2-3v-2c1 1 1 1 1 2l2 1v3l-1 1c-1-1-1-2-2-2v2s0 1-1 1l-4 7-2 2c2 0 2 1 3 2l-1 1-1-1h0l-1 4-1 1c0 2-1 4-2 5-1 0-1-1-2-1-1 1-1 3-2 4l-1 6 1 1h0 1v1 1s-1 1-1 2l-2 3h-1l-1 2h0c1 1 1 0 2 0h0v1c0 1 0 2-1 3l-3 2v-3h-2l-4 4h-1v-2c1-2 1-4 2-6 0-1 0-2 1-3h0l-1-3v-1c1-2 2-5 3-7l2-2c-1-1-1-2-1-3-1 0-1 0-1-1h0l1-1c-1-2 0-3-1-5-1-1-1-1-1-2v-1c-1-1-2-3-3-4l1-1c1-1 0-3 0-5l-1-2h-1c0-1-1-1-1-1-1-1 0-2-1-3v-10s1 0 2 1c1 0 3 3 4 4v2c1 1 2 2 4 3v-2c1 1 1 0 1 1v-5-1h1l3-5z" class="F"></path><path d="M412 288l-1-2c0-2-1-4 0-6h1c1 3 1 5 1 8v1c-1 0-1 0-1-1z" class="R"></path><path d="M402 281l1-1v1h3c1 1 1 2 2 3-1 1-2 2-2 3l-1-1c-1-1-1-2-2-3l-1-2z" class="L"></path><path d="M408 284c1 2 2 4 3 7 0 1 0 4-1 5v-1l-2-2c0-2-1-4-2-6 0-1 1-2 2-3z" class="I"></path><path d="M414 272c1-1 1 0 2 0h2l1 1h-1c-1 1-1 2-2 3v1h0c1 0 1-1 2-1h0v1c-1 1 0 2 0 3l1 2-2 1c-1-2-2-3-4-4h-1c0-2 0-4 1-6l1-1z" class="c"></path><path d="M414 272c1-1 1 0 2 0h2l1 1h-1c-1 1-1 2-2 3v1l1 1-1 1c-1-1-1-2-2-3 0 0 1-1 1-2l-1-1v-1z" class="E"></path><path d="M399 267s1 0 2 1c1 0 3 3 4 4v2l-3-2v1c2 1 7 6 7 8v1c-1 0-2-1-3-1-1-1-2-1-3-2h-1c-1 1-1 1-1 2 0-1-1-1-1-1-1-1 0-2-1-3v-10z" class="W"></path><path d="M418 284c0 2 1 3 2 3h0c0 1 1 2 1 2h0l2 1-2 2c2 0 2 1 3 2l-1 1-1-1h0l-1 4-1 1c0 2-1 4-2 5-1 0-1-1-2-1-1 1-1 3-2 4h0c-1 1-2 5-4 6h0c1-3 2-6 2-8v-1c1-3 0-6 0-9 0-1 1-4 0-5v-2c0 1 0 1 1 1v-1h0c1 1 2 2 2 3 1 2 0 6 0 9 1-3 1-6 1-8l-1-1v-5c1 0 2-1 3-2z" class="Z"></path><path d="M416 292c0 1 1 3 1 5h1v-1c1 1 2 2 3 2l-1 1c0-1-1-1-2-1h-1c0 1 1 2 0 3 0 1-1 1-1 1h-1v-2c1-3 1-6 1-8z" class="E"></path><path d="M418 284c0 2 1 3 2 3h0c0 1 1 2 1 2h0l2 1-2 2c2 0 2 1 3 2l-1 1-1-1h0l-1 4c-1 0-2-1-3-2v1h-1c0-2-1-4-1-5l-1-1v-5c1 0 2-1 3-2z" class="Q"></path><path d="M418 296c1-1 1-2 2-2h2l-1 4c-1 0-2-1-3-2z" class="H"></path><path d="M418 284c0 2 1 3 2 3h0c0 1 1 2 1 2h0l2 1-2 2v-1l-1-1v-1c-2 0-3 1-5 2v-5c1 0 2-1 3-2z" class="E"></path><defs><linearGradient id="Ca" x1="411.7" y1="308.667" x2="406.152" y2="305.178" xlink:href="#B"><stop offset="0" stop-color="#969596"></stop><stop offset="1" stop-color="#b5b4b5"></stop></linearGradient></defs><path fill="url(#Ca)" d="M403 283c1 1 1 2 2 3l1 1c1 2 2 4 2 6l2 2v1c1-1 1-4 1-5l1 4c0 3 1 6 0 9v1c0 2-1 5-2 8h0c2-1 3-5 4-6h0l-1 6 1 1h0 1v1 1s-1 1-1 2l-2 3h-1l-1 2h0c1 1 1 0 2 0h0v1c0 1 0 2-1 3l-3 2v-3h-2l-4 4h-1v-2c1-2 1-4 2-6 0-1 0-2 1-3h0l-1-3v-1c1-2 2-5 3-7l2-2c-1-1-1-2-1-3-1 0-1 0-1-1h0l1-1c-1-2 0-3-1-5-1-1-1-1-1-2v-1c-1-1-2-3-3-4l1-1c1-1 0-3 0-5z"></path><path d="M404 322h1c1-1 2-3 3-5 0 2-2 5-3 7l1 1v1l-4 4h-1v-2c1-2 1-4 2-6h1z" class="B"></path><path d="M406 308l1 1 1-2v2h-1 1c0 1 0 1 1 2l-5 11h-1c0-1 0-2 1-3h0l-1-3v-1c1-2 2-5 3-7zm7 5l1 1h0 1v1 1s-1 1-1 2l-2 3h-1l-1 2h0c1 1 1 0 2 0h0v1c0 1 0 2-1 3l-3 2v-3h-2v-1c3-3 5-8 7-12z" class="O"></path><path d="M414 314h1v1 1s-1 1-1 2l-1-1c0-1 0-2 1-3z" class="W"></path><path d="M403 283c1 1 1 2 2 3l1 1c1 2 2 4 2 6 2 6 2 11 1 18-1-1-1-1-1-2h-1 1v-2l-1 2-1-1 2-2c-1-1-1-2-1-3-1 0-1 0-1-1h0l1-1c-1-2 0-3-1-5-1-1-1-1-1-2v-1c-1-1-2-3-3-4l1-1c1-1 0-3 0-5z" class="B"></path><path d="M403 283c1 1 1 2 2 3l1 7h-1c-1-1-2-3-3-4l1-1c1-1 0-3 0-5z" class="V"></path><path d="M414 265c1-2 3-3 5-4 1 1 2 1 3 1h1l2 1c1 0 2 1 2 2h2c0 2-1 3-1 6v1h0c-1 1-1 2-1 3 0 2-1 3-1 4v1c1-1 1-2 2-3v-2c1 1 1 1 1 2l2 1v3l-1 1c-1-1-1-2-2-2v2s0 1-1 1l-4 7-2-1h0s-1-1-1-2h0c-1 0-2-1-2-3l-1-1 2-1-1-2c0-1-1-2 0-3v-1h0c-1 0-1 1-2 1h0v-1c1-1 1-2 2-3h1l-1-1h-2c-1 0-1-1-2 0l-1 1c-1-2-2-2-3-2v-1h1l3-5z" class="D"></path><path d="M425 271s1-1 2-1v1c-1 2-1 3-2 5l-1-1h-2v-1h1c0-1 1-1 1-2l1-1z" class="C"></path><path d="M422 278c1 0 1-1 2-2l-1 4c0 3-1 5-3 7h0c-1 0-2-1-2-3l-1-1 2-1-1-2c0-1-1-2 0-3 1 0 3 0 4 1z" class="L"></path><path d="M419 282l-1-2c0-1-1-2 0-3 1 0 3 0 4 1 0 1 0 2-1 3l-1 2-1-1z" class="E"></path><path d="M419 273c0-1 1-2 2-3v-4h2 0v1c0 1 0 2 1 3v1h1l-1 1c0 1-1 1-1 2h-1v1h2l1 1h-1c-1 1-1 2-2 2-1-1-3-1-4-1v-1h0c-1 0-1 1-2 1h0v-1c1-1 1-2 2-3h1z" class="c"></path><path d="M418 273c1 1 1 1 1 2h1v-2c1 0 1 1 2 1v1h2l1 1h-1c-1 1-1 2-2 2-1-1-3-1-4-1v-1h0c-1 0-1 1-2 1h0v-1c1-1 1-2 2-3z" class="b"></path><path d="M414 265c1-2 3-3 5-4 1 1 2 1 3 1h1l2 1c1 0 2 1 2 2h2c0 2-1 3-1 6h-1v-1c-1 0-2 1-2 1h-1v-1c-1-1-1-2-1-3v-1h0-2v4c-1 1-2 2-2 3l-1-1h-2c-1 0-1-1-2 0l-1 1c-1-2-2-2-3-2v-1h1l3-5z" class="K"></path><path d="M415 267c1-2 2-4 4-5v5h0-2c-1 1-1 0-2 0z" class="H"></path><path d="M419 267l3-2 1 1h-2v4c-1 1-2 2-2 3l-1-1c0-1-1-2-3-2 0-1-1-1-1-1 0-1 0-1 1-2 1 0 1 1 2 0h2 0z" class="D"></path><path d="M415 270c0-1-1-1-1-1 0-1 0-1 1-2 1 0 1 1 2 0h2v2h-3l-1 1z" class="E"></path><path d="M419 262h4l2 1c1 0 2 1 2 2h2c0 2-1 3-1 6h-1v-1c-1 0-2 1-2 1h-1v-1c-1-1-1-2-1-3v-1h0l-1-1-3 2v-5z" class="B"></path><path d="M425 263c1 0 2 1 2 2l1 2-2-1c-1 0-1 0-2-1l1-2z" class="F"></path><path d="M429 265c0 2-1 3-1 6h-1v-1c-1 0-2 1-2 1h-1v-1c-1-1-1-2-1-3 1 0 1 1 2 1 0 0 1 0 2 1h0l1-1v-1l-1-2h2z" class="Q"></path><path d="M411 223h8v1c2 1 5 2 6 2 0 0-1 1-2 1l1 1s1 1 2 1v1h-1c1 2 1 3 2 4s1 2 2 3v1l-1 1 1 1v1c-1 2-2 3-4 5l-1 1h2l-2 1c0 1 0 2 1 3l-3 3-1 1h0c1 0 2 1 2 1 2 0 3 1 5 2v2s1 0 2 1l-1 1h0-1 0-1l2 3h-2c0-1-1-2-2-2l-2-1h-1c-1 0-2 0-3-1-2 1-4 2-5 4l-3 5h-1v1 5c0-1 0 0-1-1v2c-2-1-3-2-4-3v-2c-1-1-3-4-4-4-1-1-2-1-2-1l-1-8v-3l-2-8v13l-2-13c0-6-3-13-5-18l-1-4 2-2 14-1h3 4z" class="H"></path><path d="M419 261c1-1 3-2 5-1 1 0 2 1 3 2l2 3h-2c0-1-1-2-2-2l-2-1h-1c-1 0-2 0-3-1z" class="b"></path><path d="M419 224c2 1 5 2 6 2 0 0-1 1-2 1h-1-1l-2 2h-1l-1-1c-1 1-1 1-2 1v-2c1-1 2 0 4-1v-2h0z" class="E"></path><path d="M415 227h6l-2 2h-1l-1-1c-1 1-1 1-2 1v-2z" class="K"></path><path d="M414 254c1 0 2-1 3-1 1 2 1 3 1 5-1 1-1 3-2 4h-2-1l1-2c1-2 1-3 0-6z" class="R"></path><path d="M402 248h5c2 1 4 2 5 3l-1 1h0c-1-1-2-1-2-1-3-1-6 0-8 1h-1 0c-1-2 1-3 2-4z" class="L"></path><path d="M412 251c1 1 2 2 2 3 1 3 1 4 0 6l-1 2h-1v-1c0-1 1-2 1-3v-1c-2-1-3-1-4-2 1-1 1-2 2-3h0l1-1z" class="Q"></path><path d="M411 252h0c2 1 2 3 2 5-2-1-3-1-4-2 1-1 1-2 2-3z" class="Z"></path><path d="M408 258h5c0 1-1 2-1 3l-2-1c-1 1-1 1-2 1 2 1 3 1 4 3v2c-1 0-1 0-2-1s-2-1-3-1c-1-1-2-2-3-2l1-1v-1c1-1 2-2 3-2z" class="B"></path><path d="M411 223h8v1h0-1c-2 1-4 1-7 1h-1-2l2 2c1 0 1 0 2 1v1c-2-1-4-1-4-2-1 0-1-1-1-2-1-1-5 0-7 0-2 1-4 0-6 1l-3-1-1-1 14-1h3 4z" class="J"></path><path d="M409 238h0 1l-1-3h1l1 1c1 2 2 3 3 4l1 1 2 1c2-1 2-1 4-1l-1 2h-9-1c-2 1-4 0-6 0 1-1 2-2 2-4 1 0 1-1 1-1h2z" class="K"></path><path d="M410 243c-2 1-4 0-6 0 1-1 2-2 2-4 1 0 1-1 1-1h2c-1 1-1 1-1 2v1h1 1l1 1h2 1c-2 1-2 1-3 1h-1z" class="C"></path><path d="M395 230c1 0 3 0 4-1 2 0 3-1 5-1v1s1 1 1 2-1 2-1 4h0c0 1 0 2 1 3-1 1-1 2-2 3l-1-1-1 1-1-1c-1-2-1-3-1-6 1-1 1-2 1-3h-2c-1-1-2-1-3-1z" class="Y"></path><path d="M402 231h1v1c0 1 0 3-1 4v-2c-1-1-1-2 0-3z" class="G"></path><path d="M416 231c1 1 1 2 2 2v1s0 1-1 1c1 1 1 1 1 2l2-1v2h1c1 0 1 1 2 1l-1 1-1 1h0c-2 0-2 0-4 1l-2-1-1-1c-1-1-2-2-3-4h1v-1c0-2 1-3 1-4l2 1 1-1z" class="E"></path><path d="M414 234c1 0 1 1 1 1 0 1 1 3 1 4h1-3c-1-1-1-2-1-4 1 0 1-1 1-1z" class="e"></path><path d="M416 231c1 1 1 2 2 2v1s0 1-1 1c1 1 1 1 1 2l2-1v2c-1 0-2 1-3 1h-1c0-1-1-3-1-4 0 0 0-1-1-1l1-2 1-1z" class="L"></path><path d="M401 252c2-1 5-2 8-1 0 0 1 0 2 1-1 1-1 2-2 3 1 1 2 1 4 2v1h-5c-1 0-2 1-3 2v1l-1 1h-1-1c0-1 0-4 1-6v-2l-1-1c-1 0-2-1-3-1h1 1z" class="G"></path><path d="M403 253h5l1 1v1h-5l-1-2z" class="Y"></path><path d="M403 262c0-1 0-3 1-4l1-1c1-1 1-1 2-1h1v2c-1 0-2 1-3 2v1l-1 1h-1z" class="O"></path><path d="M401 252c2-1 5-2 8-1 0 0 1 0 2 1-1 1-1 2-2 3h0v-1l-1-1h-5l1-1h-3zm20-25h1 1l1 1s1 1 2 1v1h-1c1 2 1 3 2 4s1 2 2 3v1l-1 1c-2 1-3 1-5 0-1 0-1-1-2-1h-1v-2l-2 1c0-1 0-1-1-2 1 0 1-1 1-1v-1c-1 0-1-1-2-2 2-1 2-1 2-2h1l2-2z" class="B"></path><path d="M425 234h1v3 1h-2c0-1 1-3 1-4z" class="G"></path><path d="M421 227h1 1l1 1s1 1 2 1v1h-1c-1-1-2-1-3-1-2 1-3 1-3 3l-1 1c-1 0-1-1-2-2 2-1 2-1 2-2h1l2-2z" class="Q"></path><path d="M418 233l2 1c0-1 1-1 1-2 1 0 1 0 2 1 0 2-1 3-2 5h-1v-2l-2 1c0-1 0-1-1-2 1 0 1-1 1-1v-1z" class="K"></path><path d="M421 241h0l1-1 1-1c2 1 3 1 5 0l1 1v1c-1 2-2 3-4 5l-1 1h2l-2 1c0 1 0 2 1 3l-3 3-1 1h0c-1 0-2 0-3-1v-1l-3-3c-1-2-2-2-2-4l-2-1v-2h9l1-2z" class="D"></path><path d="M416 246c3 0 6 0 8-1l1 1-1 1s-1 1-2 1c0 0-1-1-2-1l-1 1c0-1-1-1-1-1l-1 1-1 1v-3z" class="C"></path><path d="M421 241h0l1-1 1-1c2 1 3 1 5 0l1 1v1h0c-1 0-2 1-3 2h-6l1-2z" class="c"></path><path d="M413 246h3v3l1-1 1-1s1 0 1 1 0 2 1 3c-1 1-1 2-2 2l-3-3c-1-2-2-2-2-4z" class="E"></path><path d="M424 247h2l-2 1c0 1 0 2 1 3l-3 3-1 1h0c-1 0-2 0-3-1v-1c1 0 1-1 2-2-1-1-1-2-1-3l1-1c1 0 2 1 2 1 1 0 2-1 2-1z" class="D"></path><path d="M419 248l1-1c1 0 2 1 2 1-1 1-2 2-2 3-1-1-1-2-1-3z" class="L"></path><path d="M402 253l1 1v2c-1 2-1 5-1 6h1 1c1 0 2 1 3 2 1 0 2 0 3 1s1 1 2 1l1-1h0 1l-3 5h-1v1 5c0-1 0 0-1-1v2c-2-1-3-2-4-3v-2c-1-1-3-4-4-4-1-1-2-1-2-1l-1-8v-3l1-3h3z" class="M"></path><path d="M404 264c3 2 4 7 5 11h0v2l-1-2c-1-3-1-4-2-7h-2c-1-1 0-3 0-4z" class="O"></path><path d="M403 262h1c1 0 2 1 3 2 1 0 2 0 3 1s1 1 2 1l1-1h0 1l-3 5h-1v1 5c0-1 0 0-1-1h0c-1-4-2-9-5-11l-2-2h1z" class="C"></path><path d="M407 264c1 0 2 0 3 1s1 1 2 1l1-1h0 1l-3 5h-1l-3-6z" class="F"></path><path d="M388 226l2-2 1 1 3 1-1 1-1 1 1 1 2 1c1 0 2 0 3 1h2c0 1 0 2-1 3 0 3 0 4 1 6l1 1c1 1 0 1 0 3l2 2-1 2c-1 1-3 2-2 4h0-1c1 0 2 1 3 1h-3l-1 3-2-8v13l-2-13c0-6-3-13-5-18l-1-4z" class="O"></path><path d="M393 231h0c4 3 4 6 5 11 0 1 1 3 1 4 0 2-1 4 0 6 1 0 2 1 3 1h-3l-1 3-2-8c0-1 0-4-1-6 0-3-1-7-2-11z" class="T"></path><defs><linearGradient id="Cb" x1="391.451" y1="225.216" x2="393.306" y2="254.795" xlink:href="#B"><stop offset="0" stop-color="#626161"></stop><stop offset="1" stop-color="#7b7b7c"></stop></linearGradient></defs><path fill="url(#Cb)" d="M388 226l2-2 1 1 3 1-1 1-1 1 1 1-1 1v1h1c1 4 2 8 2 11 1 2 1 5 1 6v13l-2-13c0-6-3-13-5-18l-1-4z"></path><path d="M423 436c1 0 2-1 3-2l1 2h3c0 2-1 3-3 4 1 0 2 1 2 2 1-1 1-2 3-2v108 9c0-1-1-1-1-1h-2c0-1-1-1-2-1l-1-1v1c0 2 3 2 4 4v3c-2-3-5-5-9-7h-1l-1-1-1-1c-2-1-4-1-6 0l-1-1h1c1-2 1-3 1-4l1-3c0-2 1-3 1-5l-1-4 1-2c0-2-1-4-1-6l-1-1v-1s0-1-1-1v-1l-1-2h0c-1-1-1-2-2-3l-1-2v-3c-1 0-1-1-2-2h-1c-1-1-2-3-2-4 1-1 1-1 1-2h0c1-2 1-3 1-5h0c-1-4-2-22 0-25 1-2 3-3 5-4s3-1 6-2c2 0 4 1 6 2v-2l-2-2c-1-1 0-1 0-2-1-1-1-2-1-3-1-2-1-6-1-8v-1s-1-1-1-2c0 0 0-1 1-2l1-3c-1-1-1-1-2-1v-1h0c1-2 0-4 0-6h2c1-1 2-1 3-3h1z" class="V"></path><path d="M428 521c1 2 0 7 0 9s1 3 1 5c-1-3-2-6-4-8h1c2-2 2-4 2-6z" class="H"></path><path d="M425 520c0-2 1-3 2-5s1-4 2-6c1 2 1 4 1 6-1 1-1 2-2 3l-1 1v1l-1 1-1-1z" class="Z"></path><path d="M428 476c3 4 1 6 1 10v1l-1-1c-1-2-2-4-2-7 0-2 1-2 2-3z" class="C"></path><path d="M425 450c1-3 3-6 6-8-1 2-2 3-3 5-3 4-1 10 0 14h0c-1-2-2-3-3-5v-6z" class="K"></path><path d="M425 450v6c1 2 2 3 3 5h0c1 1 1 2 1 3-1 0-1-1-2-2l-1 1h0c1 1 1 2 1 4l3 3v1h-1l-4-3h0l-1-3v-1h1c0-1-1-1-1-2-1-1 0-8 0-10 1-1 1-1 0-2h1z" class="J"></path><path d="M425 520l1 1 1-1 1 1c0 2 0 4-2 6h-1l-1-1c-1 2-1 3-1 5v5c0-2 0-4-1-5v-2l-3-2h0v-5l1-1c2 2 1 2 2 4h1c0-1 0-2 1-3 0-1 0-1 1-2z" class="Q"></path><path d="M420 521c2 2 1 2 2 4h0c0 1 0 1 1 2l-1 2-3-2h0v-5l1-1z" class="B"></path><path d="M421 453c1-2 2-4 2-6 1-1 1-2 2-3l1 1c-1 1-2 3-2 5h0c1 1 1 1 0 2 0 2-1 9 0 10 0 1 1 1 1 2h-1v1l-2-2c-2-3-1-7-1-10z" class="E"></path><path d="M418 454l1-2h1l1 1c0 3-1 7 1 10l2 2 1 3h0l4 3c-1 0-2 1-3 1 0 1-1 1-1 2l-3-2v-2l-2-2c-1-1 0-1 0-2-1-1-1-2-1-3-1-2-1-6-1-8v-1z" class="O"></path><path d="M422 463l2 2 1 3h0s-1 0-1 1l2 2-1 1-1-1c-1-3-2-5-2-8z" class="H"></path><path d="M423 436c1 0 2-1 3-2l1 2h3c0 2-1 3-3 4 1 0 2 1 2 2 0 0-2 3-3 3l-1-1c-1 1-1 2-2 3 0 2-1 4-2 6l-1-1h-1l-1 2s-1-1-1-2c0 0 0-1 1-2l1-3c-1-1-1-1-2-1v-1h0c1-2 0-4 0-6h2c1-1 2-1 3-3h1z" class="b"></path><path d="M423 436c1 0 2-1 3-2l1 2c-2 1-3 2-4 4v-4z" class="D"></path><g class="H"><path d="M417 439h2c1-1 2-1 3-3h1v4c-2 2-3 4-4 7h0c-1-1-1-1-2-1v-1h0c1-2 0-4 0-6z"></path><path d="M418 450v1c1-1 1-1 1-2 2-4 5-6 8-9 1 0 2 1 2 2 0 0-2 3-3 3l-1-1c-1 1-1 2-2 3 0 2-1 4-2 6l-1-1h-1l-1 2s-1-1-1-2c0 0 0-1 1-2z"></path></g><path d="M423 536v-5c0-2 0-3 1-5l1 1-1 1v3c1 3 2 7 4 10 1 2 2 4 1 6-1-3-3-7-5-10-1 1 0 3 0 5h0c3 2 1 7 6 7 0 2 0 4-1 5l-2 1-1-1v1c0 2 3 2 4 4v3c-2-3-5-5-9-7h1c0-1 1-1 1-1h1l-2-2-1-2c-1-1-1-3-1-5 1-1 1-2 2-3v-1l1-5z" class="O"></path><path d="M422 541c0 1 1 1 1 2v1c1 1 1 3 2 4l4 4v1h-2c-1 0-1 1-2 1-1-1-1-1-1 0l-2-2-1-2c-1-1-1-3-1-5 1-1 1-2 2-3v-1z" class="Y"></path><path d="M423 543v1c1 1 1 3 2 4l4 4h-4c-1-2-2-3-2-5-1-1-1-3 0-4z" class="C"></path><path d="M422 487c1 0 2 0 3 1l1 1c1 0 1 1 2 1v1h0l1 2c0 5 1 11 0 15v1c-1 2-1 4-2 6s-2 3-2 5c-1 1-1 1-1 2-1 1-1 2-1 3h-1c-1-2 0-2-2-4v-1c0-1 0-1-1-2 0-1-1-1-1-1 0-1-1-1-1-2l-1-1c0-1 0-1 1-2l1 1 1-1c1 0 2-1 3-2 3-2 4-6 4-9s-2-7-5-8c0-1-1-1-2-1l-1-1h-4c1-2 1 0 2-1 0 0 0-1 1-1l1 1v-1c2-1 2-2 4-2z" class="E"></path><path d="M428 490v1h0c0 2-1 3-1 4h0l-2-2c0-1 2-2 3-3z" class="C"></path><path d="M422 512h1v2c0 1-1 1-3 2h-1v-1l3-3z" class="Y"></path><path d="M422 487c1 0 2 0 3 1l1 1c-1 1-1 2-2 2s-3 0-4-1c0 0-1 0-2-1 2-1 2-2 4-2z" class="L"></path><path d="M418 489c2-1 2-2 4-2l1 1v1c-1 1-1 1-3 1 0 0-1 0-2-1zm5 29c2-5 5-11 6-16v-1 7 1c-1 2-1 4-2 6s-2 3-2 5c-1 1-1 1-1 2-1 1-1 2-1 3h-1c-1-2 0-2-2-4v-1c0-1 0-1-1-2 2-1 2-1 4 0z" class="I"></path><path d="M419 518c2-1 2-1 4 0-1 2-1 4-1 7-1-2 0-2-2-4v-1c0-1 0-1-1-2z" class="F"></path><path d="M414 510l1-1h2c1 0 1 1 2 2v1l-1 1-1-1c-1 1-1 1-1 2l1 1c0 1 1 1 1 2 0 0 1 0 1 1 1 1 1 1 1 2v1l-1 1v5h0l3 2v2c1 1 1 3 1 5l-1 5v1c-1 1-1 2-2 3 0 2 0 4 1 5l1 2 2 2h-1s-1 0-1 1h-1-1l-1-1-1-1c-2-1-4-1-6 0l-1-1h1c1-2 1-3 1-4l1-3c0-2 1-3 1-5l-1-4 1-2c0-2-1-4-1-6l-1-1v-1s0-1-1-1v-1l-1-2h0c-1-1-1-2-2-3l-1-2v-3h1c0-1-1-2-1-4h1l1 1c2 0 3 0 4-1z" class="b"></path><path d="M419 528c1 4 1 9 0 13v3h-1v-8h-1l-1-6c2 2 2 4 3 6v-8z" class="L"></path><path d="M419 527l3 2v2c1 1 1 3 1 5l-1 5v1c-1 1-1 2-2 3 0 2 0 4 1 5l1 2c-1 0-2 0-2-1-1 0-2-1-2-2-1 0 0-2 0-2l-1-11h1v8h1v-3c1-4 1-9 0-13v-1z" class="K"></path><g class="F"><path d="M419 527l3 2v2l-2 11h0l-1-1c1-4 1-9 0-13v-1z"></path><path d="M414 510l1-1h2c1 0 1 1 2 2v1l-1 1-1-1c-1 1-1 1-1 2l1 1c0 1 1 1 1 2 0 0 1 0 1 1 1 1 1 1 1 2v1l-1 1v5c-1-4-3-6-5-10-1-2-2-4-4-6 2 0 3 0 4-1z"></path></g><path d="M414 510l1-1h2c1 0 1 1 2 2v1l-1 1-1-1c-1 1-1 1-1 2l1 1c0 1 1 1 1 2 0 0 1 0 1 1 1 1 1 1 1 2v1l-1 1c-1-3-3-4-4-7 0-2 0-4-1-5z" class="O"></path><path d="M409 514c3 5 5 10 7 16l1 6 1 11s-1 2 0 2c0 1 1 2 2 2 0 1 1 1 2 1l2 2h-1s-1 0-1 1h-1-1l-1-1-1-1c-2-1-4-1-6 0l-1-1h1c1-2 1-3 1-4l1-3c0-2 1-3 1-5l-1-4 1-2c0-2-1-4-1-6l-1-1v-1s0-1-1-1v-1l-1-2h0c-1-1-1-2-2-3l-1-2v-3h1z" class="H"></path><path d="M415 540c1 0 2 0 2 1 0 2 0 6-1 8v-4h-2c0-2 1-3 1-5z" class="B"></path><path d="M414 545h2v4l-3 3h-1c1-2 1-3 1-4l1-3z" class="O"></path><path d="M416 470c2 0 4 1 6 2l3 2c1 0 2 2 3 2-1 1-2 1-2 3 0 3 1 5 2 7l1 1v-1l1 1c0 2-1 4-1 6l-1-2h0v-1c-1 0-1-1-2-1l-1-1c-1-1-2-1-3-1-2 0-2 1-4 2v1l-1-1c-1 0-1 1-1 1-1 1-1-1-2 1 1 1 2 1 2 2v1h1-1-2c0-1 0-1-1-2-1 1-2 1-3 1h0c-1-1-1-1-2-1v-1h-1c-1-2 0-3 0-4-1-2-1-4-1-5v-3h-1v-3c1-2 3-3 5-4s3-1 6-2z" class="J"></path><path d="M412 476h7c2 2 4 4 5 7l-1 1h-1c-1 0-2 1-3 1l-1 1v-2l1-1c1 0 1 0 2-1l-1-1h0l-4-4c-2-1-4 1-6 2l2-3z" class="E"></path><path d="M406 482c1-1 2-1 2-2 2-2 2-3 4-4l-2 3c-1 1-1 2-2 4h1c1 2 1 3 3 4 1 0 1 1 1 1 0 1 0 2-1 2h-1c-1 1-1 2-1 3h0c-1-1-1-1-2-1v-1h-1c-1-2 0-3 0-4-1-2-1-4-1-5z" class="X"></path><path d="M406 482c1-1 2-1 2-2 2-2 2-3 4-4l-2 3c-1 1-1 2-2 4v2h1c1 1 1 2 2 3h-1c-1 0-2-1-3-1-1-2-1-4-1-5z" class="B"></path><path d="M410 472c1 1 1 1 2 1h2c3-1 6 2 9 4 1 1 1 3 1 4l1 1v1h-1 0c-1-3-3-5-5-7h-7c-2 1-2 2-4 4 0 1-1 1-2 2v-3h-1v-3c1-2 3-3 5-4z" class="a"></path><path d="M410 472c1 1 1 1 2 1-3 2-4 3-6 6h-1v-3c1-2 3-3 5-4z" class="C"></path><path d="M416 470c2 0 4 1 6 2l3 2c1 0 2 2 3 2-1 1-2 1-2 3 0 3 1 5 2 7l1 1v-1l1 1c0 2-1 4-1 6l-1-2h0v-1c-1 0-1-1-2-1l-1-1c-1-1-2-1-3-1-2 0-2 1-4 2v1l-1-1c0-1 1-2 1-3l1-1c1 0 2-1 3-1h1l1-1h0 1v-1l-1-1c0-1 0-3-1-4-3-2-6-5-9-4h-2c-1 0-1 0-2-1 2-1 3-1 6-2z" class="B"></path><path d="M416 470c2 0 4 1 6 2l3 2c1 0 2 2 3 2-1 1-2 1-2 3l-1-4c-4-3-7-3-11-2h-2c-1 0-1 0-2-1 2-1 3-1 6-2z" class="U"></path><defs><linearGradient id="Cc" x1="400.222" y1="491.433" x2="415.234" y2="503.577" xlink:href="#B"><stop offset="0" stop-color="#afacae"></stop><stop offset="1" stop-color="#d1d0d1"></stop></linearGradient></defs><path fill="url(#Cc)" d="M408 514c-1 0-1-1-2-2h-1c-1-1-2-3-2-4 1-1 1-1 1-2h0c1-2 1-3 1-5h0c-1-4-2-22 0-25v3h1v3c0 1 0 3 1 5 0 1-1 2 0 4h1v1c1 0 1 0 2 1h0c1 0 2 0 3-1 1 1 1 1 1 2h2 1-1v-1c0-1-1-1-2-2h4l1 1c1 0 2 0 2 1 3 1 5 5 5 8s-1 7-4 9c-1 1-2 2-3 2v-1c-1-1-1-2-2-2h-2l-1 1c-1 1-2 1-4 1l-1-1h-1c0 2 1 3 1 4h-1z"></path><path d="M408 502v-1c1-2 3-3 5-4l1 2c0 1-1 1-1 2-1 1-1 0-1 1-2-1-2-1-4 0z" class="G"></path><path d="M408 492c1 0 1 0 2 1h0c1 0 2 0 3-1 1 1 1 1 1 2h-1c-3 1-4 3-6 4 0-2-1-4 1-6zm5 5c2 0 3 0 5 1-1 0-1 1-1 2s0 1 1 2c0 0 1 1 1 2-1 1-2 1-4 1l-1-1h-1v-2h-1c0-1 0 0 1-1 0-1 1-1 1-2l-1-2z" class="B"></path><path d="M413 502c1 0 2-1 4-1v1l-3 2h-1v-2z" class="D"></path><path d="M414 491h4l1 1c1 0 2 0 2 1 3 1 5 5 5 8s-1 7-4 9c-1 1-2 2-3 2v-1c-1-1-1-2-2-2h-2l-1 1c-1 1-2 1-4 1-1-2-1-4-2-6v-3c2-1 2-1 4 0h1v2h1l1 1c2 0 3 0 4-1 0-1-1-2-1-2-1-1-1-1-1-2s0-2 1-2h0 2c1 0 1-1 1-1-2-1-3-2-4-3h-1v-1c0-1-1-1-2-2z" class="X"></path><path d="M408 502c2-1 2-1 4 0h1v2 6h-1-1v-5h-2-1v-3z" class="W"></path><path d="M421 497c1 2 2 4 1 7 0 1-2 3-3 4h-2l-1-1c1-1 2-1 3-2v-1c0-1-1-2-1-2-1-1-1-1-1-2s0-2 1-2h0 2c1 0 1-1 1-1z" class="C"></path><path d="M418 498h0c2 2 2 2 2 4 0 1 0 2-1 3v-1c0-1-1-2-1-2-1-1-1-1-1-2s0-2 1-2z" class="F"></path></svg>