<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="120 78 808 872"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#141212}.C{fill:#363435}.D{fill:#272626}.E{fill:#1d1c1c}.F{fill:#484648}.G{fill:#302f30}.H{fill:#2d2b2c}.I{fill:#3c393a}.J{fill:#191819}.K{fill:#3f3e3f}.L{fill:#545354}.M{fill:#6c6b6d}.N{fill:#0b0b0b}.O{fill:#645f61}.P{fill:#222121}.Q{fill:#bebdbd}.R{fill:#4f4d4f}.S{fill:#656467}.T{fill:#5b5b5d}.U{fill:#46403f}.V{fill:#838183}.W{fill:#929091}.X{fill:#747172}.Y{fill:#787778}.Z{fill:#afaeaf}.a{fill:#898889}.b{fill:#7d7b7d}.c{fill:#5c5556}.d{fill:#c0bfbf}.e{fill:#b6b6b6}.f{fill:#c2c1c1}.g{fill:#979496}.h{fill:#f3f2f3}.i{fill:#e6e6e6}.j{fill:#a09fa0}.k{fill:#dededd}.l{fill:#271c19}.m{fill:#4d4545}.n{fill:#9c9a9a}.o{fill:#d6d6d5}.p{fill:#3d2e29}.q{fill:#010101}.r{fill:#5d423a}.s{fill:#361a13}.t{fill:#4d2a1d}.u{fill:#3f221a}.v{fill:#3d1a10}.w{fill:#572918}.x{fill:#936452}.y{fill:#9e644a}.z{fill:#8b5b48}.AA{fill:#ed8a2b}.AB{fill:#69250c}.AC{fill:#c95d16}.AD{fill:#a1491f}.AE{fill:#734d40}.AF{fill:#b27a5a}.AG{fill:#eb9b46}.AH{fill:#c28760}</style><path d="M162 211l2 2 1 4c-2-1-3-3-4-5l1-1z" class="D"></path><path d="M164 213l2 1 1 4-1 1v2c0-2-1-3-1-4l-1-4z" class="G"></path><path d="M282 474c-2-1-2-3-3-4 0-1 0-2 1-3l1 1h2v1l-2 2c0 1 0 2 1 3z" class="N"></path><path fill="#e07622" d="M911 263l6-1c0 1 1 1 1 2-1 1-4 1-5 1-1-1-1-2-2-2z"></path><path d="M168 242v-1c0-3-1-6-1-9 2 4 4 8 4 13l-3-3z" class="B"></path><path d="M229 472l7 6v1l-3-1s-3 0-3-1c-1-2-1-3-1-5z" class="Q"></path><path d="M283 588c1 1 2 1 2 3h1v-1 1c1 0 1 1 1 2l1 2v6l-5-13z" class="u"></path><path d="M653 198l1-1 1-1h0c-1-1 0-1 0-1 0-1 1-1 1 0h1c2 0 2 1 3 2h0-1-2c-1 1-2 2-2 3l-2-1h1l-1-1zm218 56l-2-2v-1h-1v-4c1-2 1-3 2-4l3 1c-2 2-3 3-3 6v3l1 1h0z" class="N"></path><path d="M114 176c5 0 8 4 12 6v1 1c-4-2-8-5-12-8z" class="l"></path><path d="M376 741h2l7 9-2 2-7-11z" class="v"></path><path d="M162 471l3 3v-1c1-1 1-2 2-3h1c-1 1-2 3-2 5-1 2 0 5-1 6-2-2-3-4-3-7v-3z" class="C"></path><path d="M334 677l1 1c2 3 5 6 6 9v4l-7-10v-4z" class="u"></path><path d="M283 469h2c1 1 2 2 2 4l-1 2h-3l-1-1c-1-1-1-2-1-3l2-2z" class="h"></path><path d="M650 217c2 0 9 0 10 1 1 0 1 1 2 1 0 1 5 0 6 0 0-1 1-1 2-1l-1 2h-8-9c-1-1-1-1-2-3z" class="D"></path><path d="M652 207c-1-1-3 0-4 0h-1c-1-1-2-2-3-2l1-2 2 1h3c1 1 1 1 2 1h1l6-1v2h-3 0-1c-1 0-2 0-3 1z" class="J"></path><path d="M412 788l4 6 5 8h-2l-7-10v-2c-1-1-1-1 0-2z" class="u"></path><path d="M905 265l6-2c1 0 1 1 2 2-8 2-12 4-17 9h0c1-4 6-7 9-9z" class="AA"></path><path d="M652 202l-3 1c-1 0-1 0-2-1 1-1 1-1 2-1h1l1-1h-3-1c-1 0-2 0-3-1 1-2 3-1 5-1h4l1 1h-1l2 1 2 1-1 1c-2 0-1 0-2-1h-2v1z" class="E"></path><path d="M421 802c2 2 4 5 6 8l1 1 2 2-3 1c-1 0-2-2-2-3l-6-9h2z" class="v"></path><path d="M880 336c1 0 2 0 3 1l1 1c-3 4-4 8-6 12l-1-1v-1c0-5 1-7 3-12z" class="AC"></path><path d="M437 827c1 0 1 1 2 0 3 3 5 7 8 11 0 1 0 2 1 4v1l-11-16z" class="v"></path><path d="M662 188v4h3l1 1v1h-2-2l-1 1h0c-2 0-1 0-2-1l-2-1h0v-1h-1l1-2h-2l-1-1c3 0 6 1 8-1zm-5 13c2 1 2 1 3 3 1 1 2 1 3 2l1-1 1 1h0c-2 1-2 1-4 1h-2-1-2-4c1-1 2-1 3-1h1 0 3v-2l-6 1c0-2 0-2-1-3v-1h2c1 1 0 1 2 1l1-1z" class="N"></path><path d="M447 838l3 4c1 0 1 1 2 2 1 2 3 6 4 7 0 2 1 3 0 5l-8-13v-1c-1-2-1-3-1-4z" class="s"></path><path d="M427 814l3-1c1 2 2 3 3 4l1 1c0 1 1 2 1 3h0l1 2c0 1 2 3 3 4-1 1-1 0-2 0l-10-13z" class="u"></path><path d="M283 475h3 0 2 1v1c-1 2-1 2-3 3-1 2-3 1-5 1 0-1-1-1-1-2-1-1-1-2 0-3h3z" class="w"></path><path d="M141 327c2-1 4-2 7-1-7 5-14 8-19 16l-1-1c3-3 7-7 9-10l-1-1c2-2 2-3 5-3z" class="J"></path><path d="M233 487l-3-3c0-1 0-2-1-2-1-1-1-2-2-4 0-3-2-7 0-10l2 4c0 2 0 3 1 5 0 1 3 1 3 1l-1 1c-1 2-1 3 0 5 1 1 2 2 3 4h0l-2-1z" class="f"></path><path d="M868 190l2 2h0-1c-1-1-2-1-3-1-1 1-2 1-2 2v1c-2 1-2 2-3 3-1 2-3 3-3 5v2l1 1-1 1 1 1c0 2-1 4-1 6-1-2-1-5-2-6l-1-1v-3c0-1 2-3 3-4 0-2 0-2 2-3 2-2 4-5 8-6h0z" class="B"></path><path fill="#b14e0b" d="M665 705l2-2 9-12 1 2-13 19c-1-1-1 0-1-1 1-2 1-3 1-4l1-2z"></path><path d="M114 176c-2-1-5-2-7-3s-3-1-4-2c2-1 5-1 7-1l-2 1h1l11 5c0 1 0 1 1 2 1 0 1 1 2 1l1 1 2 1v1c-4-2-7-6-12-6z" class="B"></path><path d="M174 350c1 3 1 5 1 8h0c-1 2-1 4-1 5-1 2 0 4 0 6-1 3-2 5-2 7l-1 1c1 1 1 0 3 2h-3c-1 2-3 3-4 5v-1l1-1-1-1c2 0 2 0 3-1-1-1-2-1-2 0h-1-1c4-6 7-13 7-19 1-4 1-8 1-11z" class="G"></path><path d="M168 242l3 3c1 4 2 11-1 15v-1c-1-1-2-3-3-3l-3-1h0l-1-1h2l3-3c1 0 1-1 1-1v-2h0c0-2 0-3-1-4h-1l1-2z" class="P"></path><path d="M169 250h1v1c-1 1 0 1 0 2-2 2-3 2-6 2l-1-1h2l3-3c1 0 1-1 1-1z" class="G"></path><path d="M893 314l4 13c1 5 3 9 4 14l-3-6v-1c-2-6-6-9-10-13-2-1-3-2-4-4 1 0 8 5 10 5 0-1 0-2-1-3v-4-1z" class="J"></path><path d="M287 458c1 0 2 0 3 1s1 1 1 2v1c-1 1-1 0-1 1v2l-1 1v3l1 1v1h-1l-3-3h-1v1h-2v-1h-2l-1-1c1-1 3-1 4-2h0l1-1h1l1-6z" class="G"></path><path d="M286 464c0 1 0 2 1 3h-2l-1-2h0l1-1h1z" class="D"></path><path d="M167 244h1c1 1 1 2 1 4h0v2s0 1-1 1l-3 3h-2l1 1h0l-10 2c0-1 0-1-1-1 8-3 10-5 14-12z" class="I"></path><path d="M665 211l1-1v2h1c1 0 1 0 2 1v1c-1 1-2 1-3 1l-1 1h-10c-3 0-7 0-9-1h-1-1c-1-1-3 0-5-1h5 9l1 1c1 0 3-1 4-1h2v-3h1 4z" class="B"></path><path d="M660 211h1 4v1h0l1 1 1 1c-2 0-3-1-4 0l-1 1c-1 0-1 0-2-1v-3z" class="J"></path><path d="M787 647c1 0 2-1 3 0l1 1-10 15-4-1 10-15z" class="AG"></path><defs><linearGradient id="A" x1="498.287" y1="120.418" x2="491.815" y2="134.362" xlink:href="#B"><stop offset="0" stop-color="#97939a"></stop><stop offset="1" stop-color="#c1c3bf"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M495 113v1c0 2-1 3-1 5 0 1 1 4 2 5l3 10 1 4h-1v2c-1 0-1-1-2-1-1-7-3-14-6-20l-1-1c2-1 4-3 5-5z"></path><path d="M421 166h37c-6 1-14 0-20 0 3 1 7 1 10 1h-3 0l-30 2c-2 0-5 0-7-1 2 0 5 1 6 0h1c1 0 2 0 2-1h0-5 0c3 0 6-1 9-1z" class="Q"></path><path d="M159 295l-3 8-1 2c1 0 1 0 2-1 2-1 5 0 7 0s5 0 7 1c-8 0-13 0-19 6l-2 1h0l-2 2c0 1 0 1-1 1 0-2 2-3 3-4-1 0-1 0-2-1l11-15z" class="I"></path><path d="M915 171c1 0 2 0 4 1-1 1-3 1-5 2-7 3-14 8-21 12v-2c6-7 14-10 22-13z" class="AC"></path><path d="M845 246c-1-1-2-3-2-4 0-3 0-6 2-9 3-3 5-8 9-10h1c0 5-4 12-6 16h0c-1 0-1 0-1-1 0-3 3-7 4-10l-3 3c-1 1-2 2-2 3s0 1-1 2h0v1c-2 3-1 6-1 9z" class="B"></path><path d="M164 283v4 2c-1 3-2 6-1 9h1l1 3h0l1 2h-3c-2 0-2 1-4-1-2 1-2 1-3 2v-1l3-8c0-1 1-3 2-4 0-1 1-3 2-5 0 0 1-2 1-3z" class="T"></path><path d="M161 291c-1 3-1 5-2 8h0c1 1 1 1 1 2h0c1 2 1 2 3 2-2 0-2 1-4-1-2 1-2 1-3 2v-1l3-8c0-1 1-3 2-4z" class="R"></path><path d="M164 283v4 2c-1 3-2 6-1 9h1l1 3-1 1c-1 0-2-1-3-2-1-2 0-5 1-7 0-2 1-4 1-6v-1s1-2 1-3z" class="M"></path><path d="M777 662l4 1c-7 9-16 21-27 24l-1-1 2-1h0c2 0 4-2 5-3 7-6 12-12 17-20z" class="AA"></path><path d="M139 249c4-2 7-3 11-4 1 1 1 4 1 5l-1 2v2l-1 2h1 3c1 0 1 0 1 1-4 1-8 2-13 2 1-1 2-2 3-2h0c1-2 1-3 2-5 0-1 0-1-1-2s-3-1-6-1z" class="F"></path><path d="M139 249c3 0 5 0 6 1s1 1 1 2c-1 2-1 3-2 5h0c-1 0-2 1-3 2h-18c3-1 10 0 13-1l2-1c1 0 3 0 4-2-1-1-1-1-2-1l-1-1c0 1-1 1-1 2-1-1-1-1-1-2-1 0-2 0-3 1-1-1-3-1-4-2l9-3z" class="c"></path><path d="M357 714l2-2 1 2 18 27h-2l-19-27z" class="s"></path><path d="M172 384c1 1 2 1 3 3 1 1 1 2 2 2v-1c2 2 2 6 2 9v2l1 2v2h1c0 5 1 11 0 17h-1c0 2 1 1 0 2-1 3-1 7-2 10v-2c0-2 0-3 1-4v-2h0v-1-1-4-1c0-1 0-1 1-2-1-1-1-3-1-4v-1c-1-1 0-2 0-2h1v5-1-1-1-1l1-1c-1-1-1-1-2-1h0c0-2 0-3-1-4v-2-1c0-1-1-2-1-2-1-2 0-7-1-8l-2-2c0-1-1-2-2-2h0c0 1 0 1 1 3-2 0-3 2-4 3l-1-1v-1h1l1-1c1 0 1-1 2-1-1-1-1-1-1-2l1-2z" class="N"></path><path d="M260 424l2 1c1 8 3 16 6 23 1 4 3 8 5 12l3 6h-1l-4-8c-1-2-2-4-3-5-2-1-1 0-2-1-3-3-6-6-10-8l2-1c2 0 3 2 5 3v-8c0-3-1-7-2-10v-1l-1-3z" class="W"></path><path d="M123 259l-9-1 16-6c1 1 3 1 4 2 1-1 2-1 3-1 0 1 0 1 1 2 0-1 1-1 1-2l1 1c1 0 1 0 2 1-1 2-3 2-4 2l-2 1c-3 1-10 0-13 1z" class="O"></path><path d="M798 626h0c0 2 0 3-1 4l-1 4 3-5c1 0 2 0 3 1l-7 11-3 5-1 2-1-1c-1-1-2 0-3 0l10-20 1-1z" class="AA"></path><path d="M792 646l-1-1c0-3 0-3 1-5 2 0 2 0 3 1l-3 5z" class="AG"></path><path d="M458 166h17c3 0 6-1 8 0-1 2-3 5-5 5-4 1-7 1-11 1 2-1 3-2 5-2s4 0 5-1c-3-2-8-1-12-1-7 0-13 0-20-1h0 0 3c-3 0-7 0-10-1 6 0 14 1 20 0z" class="k"></path><defs><linearGradient id="C" x1="186.314" y1="340.696" x2="201.186" y2="332.304" xlink:href="#B"><stop offset="0" stop-color="#0e1111"></stop><stop offset="1" stop-color="#302d2f"></stop></linearGradient></defs><path fill="url(#C)" d="M204 311c0 1-1 2-2 3 0 2-1 3-1 5s-1 2-2 3c-1 3-1 5-2 8-3 9-4 18-6 27-2-10 0-22 4-32v1c2-4 5-8 6-12l3-3z"></path><path d="M341 687c1 1 7 10 8 10 1 2 2 3 3 4h0v3c1 1 3 2 4 3 0 1 1 1 2 1 0 0 0 1 2 1v-1c1 0 1 0 2-1h1 1l1-1-1-1c2-1 2-1 3-1l1 1-2 1c-1 1 0 1 0 2v1 1h-1c-1 0-1 0-2 1s-3 1-4 1l-2 2-16-23v-4z" class="t"></path><path d="M747 485h1c1-4 1-6 4-9l-4 64h0-1c0-5 1-10 2-15 0-4-1-8 0-12h1v-18-6h-1l1-1-1-1-2-2z" class="p"></path><path d="M302 631h3c1 5 4 11 8 13l13 21c2 4 5 8 8 12h0v4c-4-5-7-11-11-16l-21-34z" class="v"></path><path d="M853 364l1-1c-1-1-1-1-1-2h0c1-2 1-3 1-5h1v2 1 1 1c0 1 0 1 1 1v1 2 3h0l-1-1-1 1c0 1 1 2 1 3h1 0v1c0 1 1 1 1 2h1c1 2 0 2 1 4 0 1 1 2 2 3h0l2 2c0 1 1 3 1 3 1 1 1 2 2 3 0 1 1 2 2 3 0 0 0 1 1 2l1 3-5-5c-4-5-10-10-12-16h0l2 2 1 1 1 1s0-1-1-1c0-1-1-1-1-2h0l-1-2-1-2v-2l-1-1c0-2 0-4 1-6z" class="J"></path><path d="M853 376h0l2 2 1 1 1 1s0-1-1-1c0-1-1-1-1-2h0l-1-2-1-2v-2l-1-1c0-2 0-4 1-6 1 6 2 13 6 18l1 1 3 3c1 1 0-1 1 1l2 2c0 1 1 2 2 3 0 0 0 1 1 2l1 3-5-5c-4-5-10-10-12-16z" class="N"></path><path d="M385 750l23 32c1 1 3 5 4 6-1 1-1 1 0 2v2l-29-40 2-2z" class="s"></path><path d="M193 514c1 0 2 1 2 2s-1 3 0 4c0 1 0 2 1 3 0 1-1 2 0 3 0 1 0 3 1 4v1 3 1c0 1 0 3 1 4v3c0 1 0 1 1 2v2-6c-1-2-1-4-1-6h1c1 2 1 4 2 6h-1l1 1c0 2-1 6 0 8 0 1 0 2 1 3v4c0 1 0 2 1 3v3c0 1 0 3 1 4 0 1 0 4-1 5 0 1 0 1 1 2v3c1 1 2 5 2 6h-1l-3-12-8-51c-1-2-1-3-1-5z" class="B"></path><path d="M749 459h2v4l1 13c-3 3-3 5-4 9h-1c-1-3-1-6 0-9h0l-1-1v-2c-1-2-2-3-2-5h1c0-4 3-6 4-9z" class="l"></path><path d="M749 459h2v4c-1 2-2 3-3 5l-1 3c-1 1-1 2-1 2-1-2-2-3-2-5h1c0-4 3-6 4-9z" class="B"></path><path d="M842 473l2 2-8 53-2-2s0-1 1-2v-4l1-1h-1l-1-6h0c0-2 1-3 3-4l-1-1c-1-2 1-10 2-12 3-2 1-4 2-7v-2h0c1-1 1-7 1-8l1-6z" class="x"></path><path fill="#e07622" d="M865 404c1 0 2-1 3-1v1h3c1 1 2 2 2 3v2c1 1 1 2 1 2-2 0-4 0-7-1l-2 1-4 2c-4 4-7 7-9 11v-1c-1-3 2-8 4-11 2-1 3-2 4-5 2 0 3-2 5-3z"></path><path d="M861 413s-1 0-1-1c1-2 2-3 3-4h1l1 3-4 2z" class="AA"></path><path d="M871 404c1 1 2 2 2 3v2c1 1 1 2 1 2-2 0-4 0-7-1 1 0 2 0 3-1 0-1 0-1-1-2 1-2 1-2 2-3z" class="AC"></path><path d="M177 349v-3c1-1 0-3 1-5h0c0-1-1-2 0-3v-1-1l-1-1 1-1v-3-1c1 2 0 4 1 5h0v1 1 1c-1 0-1 1-1 2l1-1h1s1 0 1 2-1 5 0 7l1 1c0 1-1 5-1 7 0 1 1 2 1 3v6c1 3 1 5 2 8l1 1c0 2 0 3-1 5h-1c-1 0-1-1-1-1 0-2 0-2-1-4h0l-1-2-1-4v-3-3c-1-1 0-2 0-4 0-1-1-1-1-2 0-2 1-5-1-7z" class="E"></path><path d="M769 667h2l6-9c7-9 14-20 18-30v-4c0-3 3-8 4-11l1-1c-1 5-4 10-3 15l-10 20-10 15c-5 8-10 14-17 20-1 1-3 3-5 3h0c5-5 10-11 14-18z" class="Q"></path><defs><linearGradient id="D" x1="185.865" y1="371.033" x2="172.811" y2="369.544" xlink:href="#B"><stop offset="0" stop-color="#131210"></stop><stop offset="1" stop-color="#38383b"></stop></linearGradient></defs><path fill="url(#D)" d="M174 369c0-2-1-4 0-6 0-1 0-3 1-5h0c1 1 1 2 1 3l1 2v2l1 4c1 1 1 2 2 3l1 2h0c1 2 1 2 1 4 0 0 0 1 1 1h1l1 1c0 2 0 3 1 5h0-1c-3-3-8-4-11-6-2-2-2-1-3-2l1-1c0-2 1-4 2-7z"></path><path d="M174 369l2 2-2 3h0l1 1h0c-1 1-2 2-3 1h0c0-2 1-4 2-7z" class="K"></path><path d="M294 563c1 0 3 2 4 2l2 3c0 1 1 3 1 5l-1 2c0 2-1 3-2 5h-1c-2 0-3 1-4 1h0c-2 0-3 0-5-1h0l-2-2h1c1 0 1 0 2 1s1 1 3 1l-1-1c-1 0-1-1-2-1h0v-2c-1-1-1-1 0-2v-1c1 0 2-1 2-2v-3l1-2c1-1 1 0 1-1h1c1 1 1 1 2 1l1 1v-1c-1-1-3-2-3-2-2 0-3 0-4-1h4z" class="E"></path><path d="M292 566c2 0 3 1 5 2v3l-2 1c-2 0-3 0-4-1v-3l1-2z" class="h"></path><path d="M289 576c0 1 2 2 2 2 1 1 4 0 5 0 1-1 2-1 2-2s1-1 2-2v1c0 2-1 3-2 5h-1c-2 0-3 1-4 1h0c-2 0-3 0-5-1h0l-2-2h1c1 0 1 0 2 1s1 1 3 1l-1-1c-1 0-1-1-2-1h0v-2z" class="B"></path><path d="M165 295v3c1-1 1-1 2-1l1-1h3 2l2 2c0 1 0 1 1 2h0v2h1c1 0 1 1 2 1h0l2 2c2 1 3 2 4 4 2 1 3 3 6 4l4 1h1c0 1-1 2-1 2l1 2c-1 1-2 1-3 2l-1-1-3-3c-2-3-6-8-10-10-3-1-4-1-8-1-2-1-5-1-7-1s-5-1-7 0c-1 1-1 1-2 1l1-2v1c1-1 1-1 3-2 2 2 2 1 4 1h3l-1-2h0l-1-3 1-3z" class="F"></path><path d="M165 301h0c2-1 3-1 4-2 1 0 2 1 3 1s2 0 4 2h-2c-2 0-5 0-8 1l-1-2z" class="C"></path><path d="M623 191c2 0 3-1 5-1 1 1 2 1 3 1l1 1c1 0 3 0 4 1 1 2 4 3 5 5 1 0 1 0 2 1l-1 1-1-1h0l-2-1h-3v-1h-5v-1h-2c-2 0-4 0-6-1h0l1 1v1l-1 1c0 1 0 1 1 2h1l4 4h0v1c-2-1-2-1-3-3h-1l-1-1v1 2c-2 0-3-1-4-2s-1-1-2-1c-1-1-2-2-2-4 0-1-2-2-2-3l2-2h1 0c2-1 4-1 6-1z" class="J"></path><path d="M617 192c2-1 4-1 6-1v2h3l1-1 1 1-1 1-2 1c-2-2-5-2-8-3z" class="W"></path><path d="M623 191c2 0 3-1 5-1 1 1 2 1 3 1l1 1-2 1h-2l-1-1-1 1h-3v-2z" class="n"></path><path d="M632 192c1 0 3 0 4 1 1 2 4 3 5 5-2-1-3-2-5-2-4 0-7-1-11-1l2-1 1-1h2l2-1z" class="F"></path><path d="M616 192h1 0c0 1 1 2 1 3l-1 1c1 0 2 1 3 1 1 1 3 2 4 4v1 2c-2 0-3-1-4-2s-1-1-2-1c-1-1-2-2-2-4 0-1-2-2-2-3l2-2z" class="N"></path><path d="M912 170h3v1c-8 3-16 6-22 13v2c-3 4-8 6-11 8l-8 6-1-1h0l-1-1h1 1c2 0 2-1 4-2 1-1 2-2 3-2l1-1c2-2 5-3 7-5 0 0 1 0 2-1l-1-1c0-1 1-1 1-2-1-1 0-1-1 0h-3c-1 0-2 1-3 1s-1 0-1 1h-1-1-1-3v1h-2l-1 1h-2l-1 1h-1c-1 1-1 1-2 1h0l-1-1 6-2c3-2 7-3 10-4l9-3-1-1c1 0 1 0 1-1h0l2-1h0-2l2-3c6-1 12-3 18-4z" class="l"></path><path d="M912 170l1 1-21 9-1-1c1 0 1 0 1-1h0l2-1h0-2l2-3c6-1 12-3 18-4z" class="d"></path><path d="M148 310c1 1 1 1 2 1-1 1-3 2-3 4v1c-1 1 0 0 0 1-1 1-2 2-3 4l-2 2-3 3 1 1h1c-3 0-3 1-5 3l-2 2c-8 5-14 14-16 23v6c0 2-1 5-1 8-1-1-1-1-1-2-1-13 7-30 15-40 5-6 12-11 17-17z" class="K"></path><path d="M854 196l2-2c0-2 1-2 2-4 2 0 6-2 9-1h0l1 1c-4 1-6 4-8 6-2 1-2 1-2 3-1 1-3 3-3 4v3l1 1c1 1 1 4 2 6-2 4-3 6-3 10h-1v-1c0-1-1-3 0-4 1-3 2-5 1-8-1 0-2 0-3 2 0 1 0 1-1 2h-2c-1 0-1-1-3-2l-1-2-1-2c1-2 2-3 3-5l1 1h1v-1c1-1 1-1 2-1 1-1 2-3 2-5 0-1 1-1 1-1z" class="D"></path><path d="M846 210l1-2c1-1 1-1 2-1 1 2 0 3 0 4l-1 1-1-1-1-1z" class="E"></path><path d="M849 214l1-1v-2c0-1 2-5 4-5v2c1 0 1 1 1 2-1 0-2 0-3 2 0 1 0 1-1 2h-2zm5-18l2-2c0-2 1-2 2-4 2 0 6-2 9-1-3 1-6 2-9 4v3c-1 0-1 1-2 2-1 0-2 2-3 3-3 2-3 4-7 4v5l1 1-1 1-1-2-1-2c1-2 2-3 3-5l1 1h1v-1c1-1 1-1 2-1 1-1 2-3 2-5 0-1 1-1 1-1z" class="K"></path><path d="M132 285c-2-4-2-8-5-12-3-5-12-10-18-11h-1 14l22-1c5 0 10-1 15-1 1 0 3 1 4 1l1 2-1 1h-1v-2h-1c-1 1-2 1-3 1-1-1-1-1-1-2h0-5v1h0 1c2 0 2 0 3 1h-2-2-1l-2 1c-1 0-2-1-3 0l-1 1c-2-1-3-1-5 0-1 0-2 0-3-1-2 0-8 1-9 2 0 6 5 12 4 19z" class="D"></path><defs><linearGradient id="E" x1="435.502" y1="154.59" x2="453.498" y2="182.41" xlink:href="#B"><stop offset="0" stop-color="#d5cfcd"></stop><stop offset="1" stop-color="#fafeff"></stop></linearGradient></defs><path fill="url(#E)" d="M415 169l30-2h0c7 1 13 1 20 1 4 0 9-1 12 1-1 1-3 1-5 1s-3 1-5 2l-41-1c-3-1-6 0-9 0v-2h0-2z"></path><path d="M207 569h0c1 3 2 6 2 9l5 18c9 33 22 64 47 88-3 0-4-2-6-4-4-4-8-7-11-11v-1l2 2c1 2 2 3 3 4v-1l-2-2c0-1 0 0-1-1l-2-3-1-2-1-1c0-1-1-2-1-3h-1c-1-1-1-2-1-3l-1-1-1-1c-1 0-1 0-2-1l1-1c-1-1-1-1-1-2l-1-1h0l-4-9-1-1c-1-2 0 0 0-1h-1v-2c-1-1-1-2-2-2-1-1-2-1-2-3v-1c-1 0-1-1-2-2 0-2-1-3-1-4-1-1 0-1-1-2v-1l-1-1c0-1 0-1-1-3h0v-1l-1-1v-1s-1-1-1-2v-2h-1c0-2 0-3-1-4v-2l-1-2h0v-1l-1-1c0-1 0-1 1-2h0l1 2v1c0 1 1 3 1 4l1 1v1 1h1l-2-5v-1h0c0-1 0-2-1-3v-2h-1v-1c-1-1 0-2 0-3-1-3-1 0-1-2l-1-2v-2l-1-1 1-1c-1-1-1-1-1-2v-1c-1-1-1-1-1-2v-1-1l-1-1 1-1c-1-1-1-2-2-3l1-2-1-1v-2c-1-1 0 1-1-1h1z" class="p"></path><path d="M110 170c4 1 7 1 10 2s7 3 10 3l10 2 9 1 3 1c1 0 2 0 2 1h1v1l1 4-3-1-1 1h-6c-1 0-3-1-4-1l-4-2c-6-1-12-4-18-6l-11-5h-1l2-1z" class="o"></path><path d="M130 175l10 2c0 1-1 1-1 2h2v1c-1 0-1 0-2 1l4 1-1 2-4-2v-1c-3-2-6-2-8-6z" class="f"></path><path d="M140 177l9 1 3 1c1 0 2 0 2 1h1v1l1 4-3-1-1 1h-6c-1 0-3-1-4-1l1-2-4-1c1-1 1-1 2-1v-1h-2c0-1 1-1 1-2z" class="X"></path><path d="M143 182l1-1c1 0 3 1 4 1 3-1 5-1 7-1l1 4-3-1-1 1h-6c-1 0-3-1-4-1l1-2h0z" class="T"></path><path d="M143 182c3 1 6 2 10 2l-1 1h-6c-1 0-3-1-4-1l1-2h0z" class="Q"></path><path d="M605 192c2 0 4 0 6-1 2 1 4 1 5 1l-2 2c0 1 2 2 2 3 0 2 1 3 2 4 1 0 1 0 2 1s2 2 4 2c1 0 1 0 2 1 1 0 1 0 1 1h1l1 1c-2 0-5-2-6-2s-1 1-1 2l-3 1c-1 1-2 1-3 1-1-1-2-1-3-1l-1-1c-1 0-2 1-3 1-1-1-1-1-1-2l-1-2v-3-1h-2v-2c-1 1-1 0-1 1h-1v-2h-1c0-1 0-2 1-2 0-1 1-2 2-3h0z" class="B"></path><path d="M612 207l1-1v-1l3 1c1 0 2 0 4 1h2l-3 1c-1 1-2 1-3 1-1-1-2-1-3-1l-1-1z" class="G"></path><path d="M607 201c1 0 1 0 2-1h3l1 1 1 1h1l3 3c-1 1-1 1-2 1l-3-1v1l-1 1c-1 0-2 1-3 1-1-1-1-1-1-2l-1-2v-3-1 1z" class="q"></path><defs><linearGradient id="F" x1="613.944" y1="196.241" x2="604.523" y2="194.055" xlink:href="#B"><stop offset="0" stop-color="#131313"></stop><stop offset="1" stop-color="#2f2f2f"></stop></linearGradient></defs><path fill="url(#F)" d="M605 192c2 0 4 0 6-1 2 1 4 1 5 1l-2 2c0 1 2 2 2 3h-2v1h-1v1 2l-1-1h-3c-1 1-1 1-2 1v-1h-2v-2c-1 1-1 0-1 1h-1v-2h-1c0-1 0-2 1-2 0-1 1-2 2-3h0z"></path><path d="M605 198v-2l3-1 1 1c-1 2-1 3-2 5v-1h-2v-2z" class="d"></path><path d="M622 208c1 0 3 0 4 1h3 1c0-1 0 0 0 0l3 1v-1-1c-1-1-1-2-1-3-1 1-1 1-2 1h0c2-1 2-2 4-3l1-1 1 1v-1c-1 0-2 0-3-1 0-1-1-1-2-2 0-1-1-1-1-2v-1l2 2 1 1 1-1c1 0 1 1 2 1h1c2 0 3 2 4 3v2h1c0 1 1 1 1 2 1 1 1 2 3 3 3 1 7-1 10 1 1 0 2 0 3 1h1v3h-2c-1 0-3 1-4 1l-1-1h-9-5-3 1v-3h-5l-2-1h-2l-3-1-3-1z" class="E"></path><path d="M646 211l1-1 1 1h11 1v3h-2c-1 0-3 1-4 1l-1-1-1-2h-3c-2 0-2 0-3-1z" class="G"></path><path d="M637 211h4 5c1 1 1 1 3 1h3l1 2h-9-5-3 1v-3z" class="R"></path><path d="M637 211h4c1 0 3 1 4 2l-1 1h-5-3 1v-3z" class="O"></path><path d="M865 392l5 5 3 4c4 4 9 8 13 13 0 3 1 5 1 8l-1 1c-1-2-1-4-3-6-1-1-2-2-4-3-1-1-3-1-4-2l-1-1s0-1-1-2v-2c0-1-1-2-2-3h-3v-1c-1 0-2 1-3 1-2 1-3 3-5 3-1 0-2 0-3-1l-3 3c0-1 0-1 1-2v-1l3-6c1-1 1-2 1-3l1-1 3-3 2-1z" class="AD"></path><path d="M875 412c0-2 0-3-1-4 1-1 2-1 3-1 3 2 5 4 6 7v3c-1-1-2-2-4-3-1-1-3-1-4-2z" class="B"></path><path d="M865 392l5 5 3 4h-2c0-1-2-2-3-3-1 0-2 1-2 1-1 0-2-1-3 0l-2 1v1c1 1 1 2 1 3h3c-2 1-3 3-5 3-1 0-2 0-3-1l-3 3c0-1 0-1 1-2v-1l3-6c1-1 1-2 1-3l1-1 3-3 2-1z" class="AE"></path><path d="M860 396l3-3c2 2 2 2 2 4h-2c-1 0-2-1-3-1z" class="r"></path><path d="M861 400v1c1 1 1 2 1 3h3c-2 1-3 3-5 3-1 0-2 0-3-1l1-2 2-2 1-2z" class="y"></path><path d="M865 392l5 5 3 4h-2c0-1-2-2-3-3s-2-1-3-1c0-2 0-2-2-4l2-1z" class="w"></path><path d="M851 258c-1-3-3-9-2-13 0-2 0-4 2-5l1 2c2 2 3 4 5 7 2 4 8 6 12 6 1 1 2 1 3 0l-1-1h0l-1-1v-3c0-3 1-4 3-6l2 2 2 1c3 0 5 1 7 2v1h-1c-1 0-1 0-2 1-2 2-2 2-2 5h0l3 3h-4c-8 0-14-3-21-5-2 1-2 1-3 2l-2 2h-1z" class="E"></path><path d="M874 255l-2-1v-5c0-1 2-3 3-3l2 1s-1 1-1 2l-2 1v3 2z" class="H"></path><path d="M877 247c3 0 5 1 7 2v1h-1c-1 0-1 0-2 1-2 2-2 2-2 5h0l3 3h-4v-1c-1-1-2-2-3-2s-1 0-1-1v-2-3l2-1c0-1 1-2 1-2z" class="K"></path><path d="M497 139c1 0 1 1 2 1v1l2-1v1c1 0 1 0 2 1v-1l3-1v2 1l-2 2c0 2 0 3 1 4l1 1h-1-1s-1 0-1 1 0 2-1 3l-3 3c-1 0-2 1-3 1l-2 3-6 9-3 3v1c-2 0-3 0-4-1 1-3 2-5 3-7h0c2-4 5-6 8-9 0-3 0-6 1-8v-1c1-3 4-6 4-9z" class="o"></path><path d="M499 149c-1-1-1 0-2-1h0l-1-1 3-3 1 2 1 1c0 1-1 2-2 2h0zm-15 17h0c1 0 2 0 3 1v3c-1 0-2 1-3 2l1 1v1c-2 0-3 0-4-1 1-3 2-5 3-7z" class="i"></path><path d="M497 139c1 0 1 1 2 1v1l2-1v1c1 0 1 0 2 1-1 1-3 2-4 2l-3 3 1 1h0c1 1 1 0 2 1h0l1 1c-1 2-2 2-3 3s-2 2-4 3l-1 1c0-3 0-6 1-8v-1c1-3 4-6 4-9z" class="L"></path><path d="M499 149h0l1 1c-1 2-2 2-3 3l-2-1c-1-1-1-2 0-3h4z" class="E"></path><path d="M191 357c2-9 3-18 6-27 0 1 1 3 0 5h0c0 1 0 1-1 2-1 2 0 4-1 6v3l-1 2c0 2 0 4-1 6 0 4-1 8-1 11v9 6c1 1 2 1 3 1h2c-1 2-1 3 0 6 1 2 1 3 2 5l-1 3h1c1-1 1-1 1-2l1 1 1 1h-1c-1 1-1 2-1 2v2c0 2 0 5 1 8l-1 1 2 2c-1 1-1 2-1 3l-1-1h0c0 1-1 3-1 4v1c-2 2-1 5-3 7v7c-1-3-1-7-1-10-1-7 0-15-1-22-1-3-1-6-2-9 0-2-1-4-1-6-1-3 0-7 0-10v-17z" class="L"></path><path d="M199 409l1 3c0 1-1 3-1 4v1c-2 2-1 5-3 7v-3l1-8c0-2 0-3 2-4z" class="Y"></path><path d="M196 421c1-2 1-3 1-5v-1l1-1 1 1v2c-2 2-1 5-3 7v-3z" class="a"></path><path d="M197 407c-1-2 0-4 0-6s1-3 3-4v2c0 2 0 5 1 8l-1 1 2 2c-1 1-1 2-1 3l-1-1h0l-1-3c-2 1-2 2-2 4-1-2-1-4 0-6z" class="S"></path><path d="M197 407l1-2h1v3 1c-2 1-2 2-2 4-1-2-1-4 0-6z" class="M"></path><path d="M535 165c2 0 4 0 7 1h-6l3 5c3 1 7 1 11 1h18l55-1h10v-1l-2-2c-2 0-4 0-6-2h-11c6-1 12-1 18 0 4 0 8-1 12 0l2 2 3 3c-5 1-9 1-14 1h-13c-3 0-7 0-10 1h-51c1 1 1 1 3 1 1 0 4 0 5 1h8v1h-4-31l-1 1c1 1 1 2 3 3h1l-1 1v1h3v1c-1 1-1 1-3 1 0 1 0 1 1 2l-2 1c0-3-1-6-2-8 0-1 0-1-1-2-1-3-2-5-4-8l-1-4z" class="H"></path><path d="M625 166c3 0 8-1 11 0 1 1 1 2 4 2 1 1 1 2 1 3-3 1-5 0-8 0v-1l-2-2c-2 0-4 0-6-2z" class="S"></path><path d="M456 851c2 2 3 4 4 6 1 1 2 3 4 4s3 2 5 4h2l3 3-1 2 4 4 1 3c0 1 2 4 3 4v5c2 1 3 3 4 4l8 13v1c1 3 3 6 6 9 1 1 1 2 2 3 2 2 3 4 4 6 1 1 4 6 5 7l1 1c0 1-1 2-2 2l-6-9-15-22-32-45c1-2 0-3 0-5z" class="u"></path><path d="M464 861c2 1 3 2 5 4h2l3 3-1 2 4 4 1 3c0 1 2 4 3 4v5l-3-3c-1-2-2-4-3-5h-1c-2-7-8-11-10-17z" class="p"></path><path d="M469 865h2l3 3-1 2-1-1c-2-1-2-3-3-4z" class="m"></path><path d="M472 869l1 1 4 4 1 3c-2-1-3-2-4-3v-1c-1-2-2-2-2-4z" class="K"></path><path d="M849 214h2c1-1 1-1 1-2 1-2 2-2 3-2 1 3 0 5-1 8-1 1 0 3 0 4v1c-4 2-6 7-9 10-2 3-2 6-2 9 0 1 1 3 2 4l3 9 1 5-2-2-2-4-1 1h-1l-3-9-1-1v-2-1h1c1 0 1 0 1-1h-2c-1-1-1-1-1-3 0-1-1-2-1-3l2-1-1-1c0-1-1-2-1-3v-1h0c-2-2-3-5-3-7s0-2 1-3c1 0 2 0 2-1h1c1 0 3-1 4-2 2-1 2-1 4 0 1 0 1 0 2 1h2c-1-2-1-1-2-2l-2-2c-1-1-1-1-1-3l1 2c2 1 2 2 3 2z" class="R"></path><path d="M842 233c0 2 0 1-1 3v2h-2v3c-1-1-1-1-1-3 0-1-1-2-1-3l2-1 1 1 2-2z" class="c"></path><path d="M839 245v-2-1h1l2 3s1 1 1 2 1 3 1 4l1 1c1 1 1 2 3 3l1 5-2-2-2-4-1 1h-1l-3-9-1-1z" class="T"></path><path d="M842 216c2-1 2-1 4 0 1 0 1 0 2 1h2l3 3c0 1 0 1-1 1l-1 1c-4 2-8 7-9 11h0l-2 2-1-1-1-1c0-1-1-2-1-3v-1h0c-2-2-3-5-3-7s0-2 1-3c1 0 2 0 2-1h1c1 0 3-1 4-2z" class="j"></path><path d="M837 229h0c0-3 0-5-1-7 3-1 6-4 9-4h2l1 2c-2 2-4 1-5 3-2 2-2 5-4 7-1 0-1-1-2-1z" class="Q"></path><path d="M747 540h1c-3 21-8 40-15 59-9 23-21 43-35 63l-21 31-1-2 8-11 28-43c2-3 4-6 5-9 0-2 1-3 1-5h-1l-1 1-1-1-1-1v-1c1 0 2-1 3-2l-1-1-3 2c-1 1-4-1-5-1 1-1 2-2 3-2h1c1-1 1-1 2-1h1c0-2 1-2 2-3h2l2 2c1 0 2 0 3-1 6-10 10-23 14-34l2-7c-1-1-2-2-3-4 1-1 1-2 2-3l1 2h0l1-1 1-1 5-26z" class="AC"></path><path d="M739 566l1 2h0l1-1 1-1c-1 2-2 5-2 7-1-1-2-2-3-4 1-1 1-2 2-3z" class="t"></path><path d="M708 619c1-1 2-2 3-2h1c1-1 1-1 2-1h1c0-2 1-2 2-3h2l2 2c1 0 2 0 3-1-2 5-4 10-7 14 0-2 1-3 1-5h-1l-1 1-1-1-1-1v-1c1 0 2-1 3-2l-1-1-3 2c-1 1-4-1-5-1z" class="U"></path><path d="M717 619h0l2-1c1 0 1 0 1 1v1c-1 1-1 2-1 3h-1-1l-1 1-1-1-1-1v-1c1 0 2-1 3-2z" class="r"></path><path d="M509 90h1c4 10 10 19 16 27 1 1 2 1 2 2-1 3-3 6-3 9-2 2-3 2-3 4l-1 1-1-1h-1l-1-3c-1-1-2-2-4-3-1 0-2 1-3 2s-1 2-1 3l-1 1v-4-10-19-9z" class="i"></path><path d="M526 117c1 1 2 1 2 2-1 3-3 6-3 9-2 2-3 2-3 4l-1 1-1-1c2-5 3-11 6-15z" class="X"></path><path d="M664 707c0 1 0 2-1 4 0 1 0 0 1 1-6 9-13 18-19 27l-29 41-44 62-25 36-23 33c-4 6-9 12-13 19l-1-1 16-24 6-9c2-4 5-7 8-10l21-32 73-103c8-12 17-24 25-36v-1c2-2 3-5 5-7z" class="AD"></path><path d="M150 312c0 1 0 3-1 4l-2 2c0 1-1 1-2 2l-1 2h0v1l1-1c1-2 3-3 4-5 1-1 2-1 2-2 3-1 5-5 9-6h1l-1 1h-1 1l1 1 1-1 1 2v1c0 1 0 1-1 2l1 1v1c0 1 0 1 1 1l1-1h1c1 1 1 1 2 3h1c1 0 2 1 3 3h-1c0 2 1 2 1 4 1 0 1 1 1 2 1 1 1 2 1 4 1 1 1 4 2 6 0 1-1 5 0 6 0 1 0 1 1 2h-1c1 1 1 1 1 2h0c2 2 1 5 1 7 0 1 1 1 1 2 0 2-1 3 0 4v3 3l1 4c-1-1-1-2-2-3l-1-4v-2l-1-2c0-1 0-2-1-3 0-3 0-5-1-8-2-8-3-16-8-22l-5-4c-3-1-7 0-9 1-1 0-2 0-3 1h-1 0c-3-1-5 0-7 1h-1l-1-1 3-3 2-2c1-2 2-3 3-4 0-1-1 0 0-1v-1c1 0 1 0 1-1l2-2z" class="H"></path><path d="M166 328v-1c0-1-1-2-3-3l1-1c2 0 3 1 4 3 1 1 2 3 3 4v1c1 2 2 5 2 8l3 13 2 8 1 5v3l1 4c-1-1-1-2-2-3l-1-4v-2l-1-2c0-1 0-2-1-3 0-3 0-5-1-8-2-8-3-16-8-22z" class="L"></path><path d="M146 322c2-2 3-4 6-4 3-3 4-4 8-5 1 2 1 3 1 5h0c1 1 1 1 1 2 2 1 2 1 4 1 0-1 0-1 1-1 1 1 2 3 1 5h1c1 2 2 3 2 5-1-1-2-3-3-4-1-2-2-3-4-3l-1 1c2 1 3 2 3 3v1l-5-4c-3-1-7 0-9 1-1 0-2 0-3 1h-1 0c-3-1-5 0-7 1h-1l6-5z" class="I"></path><path d="M146 322h2c1-1 2-3 4-3h1c1 0 1-1 3-1l1 1-4 4c-2 0-3 0-4 1l-1 2h0c-3-1-5 0-7 1h-1l6-5z" class="F"></path><path d="M309 623c1 1 2 1 3 1l2 4v1l13 22c2 1 3 3 4 5l2 4h0v5l1-1v2c-1 1-1 1-1 2v1 2h0l-1 1h0c1 1 2 2 2 4v1c-3-4-6-8-8-12l-13-21c-4-2-7-8-8-13-2-2-3-3-3-5h1c1 0 2 1 2 1l1 1h0c0-1-1-2-1-2l-1-1 1 1 1-1h1l2-2z" class="m"></path><path d="M309 623c1 1 2 1 3 1l-2 2v1h-2v1-1c-1-1-1-1-1-2l2-2z" class="I"></path><path d="M312 624l2 4v1l-1 1c2 2 2 4 2 7-1-1-3-2-3-3s0-2-1-3c0-2 0-3-1-5l2-2z" class="L"></path><path d="M302 626h1l5 8c2 3 4 7 5 10-4-2-7-8-8-13-2-2-3-3-3-5z" class="C"></path><path d="M318 643c1 1 2 2 3 4s3 3 4 6l2-2c2 1 3 3 4 5l2 4v2h-2c-2-2-3-3-4-5-1 0-1-1-2-2s-3-3-4-5c0-2-2-5-3-7z" class="L"></path><path d="M314 629l13 22-2 2c-1-3-3-4-4-6s-2-3-3-4l-3-6c0-3 0-5-2-7l1-1z" class="S"></path><path d="M495 113l14-23v9 19 10 4h-1v4c0 1-1 2-2 4l-3 1v1c-1-1-1-1-2-1v-1l-2 1v-1-2h1l-1-4-3-10c-1-1-2-4-2-5 0-2 1-3 1-5v-1z" class="b"></path><defs><linearGradient id="G" x1="503.173" y1="130.086" x2="502.472" y2="123.313" xlink:href="#B"><stop offset="0" stop-color="#474749"></stop><stop offset="1" stop-color="#656467"></stop></linearGradient></defs><path fill="url(#G)" d="M496 124c0-1 0-1 1-2v-2h1c0 2 0 3-1 5h1c1 0 1 0 2-1h2l2-2c1-1 3 0 4-1l1 7v4h-1v4c0 1-1 2-2 4l-3 1v1c-1-1-1-1-2-1v-1l-2 1v-1-2h1l-1-4-3-10z"></path><path d="M499 134c1-1 1-2 2-3h2l1 1s1-1 2-1 1 0 1 1h1v4c0 1-1 2-2 4l-3 1v1c-1-1-1-1-2-1v-1l-2 1v-1-2h1l-1-4z" class="C"></path><path d="M507 132h1v4l-2 1-1-1-2-2c2-1 3-2 4-2z" class="L"></path><path d="M506 137l2-1c0 1-1 2-2 4l-3 1-1-1c0-1 0-2 1-3h3z" class="M"></path><path d="M282 411c3 1 5 4 7 7v-1h1c0 1 0 2 1 3v-1c0-1 0-2 1-4h0v3c1 4 2 8 1 11v1c-2 4-2 7-3 11 0 2-1 3-2 4v3c0 1-1 2-1 3l-1 1h1v2h0v4l-1 6h-1l-1 1c-2-2-2-4-4-5v-1-1c-1-1-2-1-2-3v-3c-1-2-1-5-1-7v-1-1-2h0c0-1 1-2 1-4v-1-3l1-1v-1h1 1v3h-1c0 1 1 1 1 2-1 1-1 1-1 2v1h0c0 1-1 2 0 4 1 0 1 0 1 1v-1c0-3 0-5 1-7 0-2 1-3 0-4 0-5 2-10 1-15 0-1-1-1 0-2 0-1-1-3-1-4z" class="N"></path><path d="M285 464v-4c1-2 1-3 1-4h0v-2h-1v-5c1-1 2-3 3-4v3c0 1-1 2-1 3l-1 1h1v2h0v4l-1 6h-1z" class="E"></path><path d="M289 417h1c0 1 0 2 1 3v-1c0-1 0-2 1-4h0v3c1 4 2 8 1 11-2-2-2-3-3-6 0-2-1-3-1-5v-1zm-12 28c1-1 1-2 1-3h1v1c0 1 0 0 1 1l2 2h0c0 2 0 8-1 9h0c-1 1-1 0-1 1v2c-1-1-2-1-2-3v-3c-1-2-1-5-1-7z" class="P"></path><path d="M509 132l1-1c0-1 0-2 1-3s2-2 3-2c2 1 3 2 4 3l1 3h1l1 1 1-1c0-2 1-2 3-4-1 4-3 10-3 14 2 2 5 4 5 7s-2 4 0 7c1 2 7 7 8 9l1 4c-2 1-3 0-5 0h0c-2-4-7-9-10-12-2 1-4 3-4 5v1l-1 1 1 1h-1c0 1 0 1-1 2-1 0-1-1-2-1 1-1 1-1 1-2s0 0 1-1v-1l-1-1v1l-2-1v-2c1-2 0-3 0-4h1 1l-2-2v-2l2-1c-1 0-1 0-2-1v-6c1-1 1-1 1-2v-1l-2-2h0l-3-2v-4h1z" class="M"></path><path d="M518 150c-1-1-2-2-2-3s0-2-1-2l1-1c2 0 3 1 5 3l2 2v1h-1l-1-1c-1 0-2 0-2 1h-1z" class="i"></path><path d="M521 133l1-1c0-2 1-2 3-4-1 4-3 10-3 14l-1 1 1 1 1 1h-1c-2-2-3-2-4-3l-1-1h1 2v-1-4c0-1 0-2 1-3z" class="K"></path><path d="M509 132l1-1c0-1 0-2 1-3s2-2 3-2c2 1 3 2 4 3l1 3h1l1 1c-1 1-1 2-1 3-1-1-1-2-3-2h-5 0c0 1 1 2 1 2 0 1-1 1-2 2h0l-3-2v-4h1z" class="Z"></path><path d="M509 132l1-1c0-1 0-2 1-3s2-2 3-2c2 1 3 2 4 3l-2 1-2-1-1 1c-1 1-2 2-3 2 0 3 0 3 1 6l-3-2v-4h1z" class="f"></path><path d="M521 154h1 1c0 2 1 2 2 3s3 2 4 4v-1c-1-1-2-2-2-4 1 2 7 7 8 9l1 4c-2 1-3 0-5 0h0c-2-4-7-9-10-12h0v-3z" class="i"></path><path d="M518 150h1c0-1 1-1 2-1l1 1c0 2 0 2-1 3v1 3h0c-2 1-4 3-4 5v1l-1 1 1 1h-1c0 1 0 1-1 2-1 0-1-1-2-1 1-1 1-1 1-2s0 0 1-1v-1l-1-1v1l-2-1v-2c1-2 0-3 0-4h1 1l-2-2v-2l2-1 1-1 3 1z" class="J"></path><path d="M521 157c-2-1-4-2-5-3 0-2-1-2 0-3 2 0 3 1 5 2v1 3z" class="k"></path><defs><linearGradient id="H" x1="319.083" y1="147.587" x2="338.763" y2="128.949" xlink:href="#B"><stop offset="0" stop-color="#c4c4c4"></stop><stop offset="1" stop-color="#f7f7f6"></stop></linearGradient></defs><path fill="url(#H)" d="M323 127c1-5 3-11 5-16 0 12 1 27 10 35 2 1 3 2 5 3 2 0 4 0 6-1-1 2-2 3-3 4-2 0-4 3-6 3-4-2-8 1-12-1-1-1-2-1-3-2h-1v-1l-1-1v-1h-3c-1-1-1-2-2-3v1h-1c-5 6-9 11-15 16l-1-1-1 1-1 1c-2 0-4 1-6 2 1-1 2-1 2-2 6-3 10-7 14-11 2-1 4-6 5-7s2-3 2-3c3-3 4-7 5-11 1-1 2-3 2-5z"></path><path d="M343 149c2 0 4 0 6-1-1 2-2 3-3 4-2 0-4 3-6 3-4-2-8 1-12-1-1-1-2-1-3-2h-1v-1c2 0 4 1 6 1h7v-1c2 0 4 0 5-1 1 0 1 0 1-1z" class="d"></path><path d="M343 149c2 0 4 0 6-1-1 2-2 3-3 4l-2-1h-2l-1 1c-3 2-7 1-11 0h7v-1c2 0 4 0 5-1 1 0 1 0 1-1z" class="Q"></path><path d="M321 132c1 2 0 4-1 6h0 1l1-2h0c-1 3-3 7-5 10v1c-5 6-9 11-15 16l-1-1-1 1-1 1c-2 0-4 1-6 2 1-1 2-1 2-2 6-3 10-7 14-11 2-1 4-6 5-7s2-3 2-3c3-3 4-7 5-11z" class="k"></path><defs><linearGradient id="I" x1="625.541" y1="176.256" x2="539.757" y2="161.96" xlink:href="#B"><stop offset="0" stop-color="#c6c5c6"></stop><stop offset="1" stop-color="#ebe9e9"></stop></linearGradient></defs><path fill="url(#I)" d="M595 166h19 11c2 2 4 2 6 2l2 2v1h-10l-55 1h-18c-4 0-8 0-11-1l-3-5h6 36c6 0 11-1 17 0z"></path><path d="M595 166h19 11c2 2 4 2 6 2l2 2v1h-10 0 4c-1-1-2-2-3-2-4 0-8-1-12-2h6c-2-1-4 0-6 0-5-2-11 0-17-1h0z" class="j"></path><path d="M206 562v-7-1h1v1l2 2c1 6 1 13 4 19 2 19 9 39 17 57h1c1 0 3 0 5 1 0 1 0 1 1 2v1 1c0 1 1 1 1 2v2h1c1 2 0 4 3 6 4 11 9 22 16 31 3 3 6 6 9 8l-6-3c-25-24-38-55-47-88l-5-18c0-3-1-6-2-9h0l-1-7z" class="o"></path><path d="M149 178c13 0 25-2 38-5 1 1 1 0 1 1l-2 1v1c-1 1-1 2-1 3s0 1-1 2v3 1h1v1h1l8-1h-1l1 1 1-1h1c1-1 2-1 3-2h1v1l-2 1-1 3h0 1 2l-2 2-1 1c-1 1-1 3-1 5h0v1l-1 2-2-5-2-3h-9c-3 0-6 1-9 1-2 0-2 0-3 2l1 2-1 1v2l-4-3v-1-1c0-1 0-1-1-2 0-1-2-1-3-2h-2l-1-1c-1 0-2 0-3-1s-3 0-4 0l-2-1c-1 0-3-1-4-2h6l1-1 3 1-1-4v-1h-1c0-1-1-1-2-1l-3-1z" class="S"></path><path d="M162 186c1-1 1-1 3-2v1c1 0 1 1 2 1v-2-1-1l3-3c1 1 1 2 3 3 1-2 2-3 4-3 1 1 1 1 1 2-1 2-2 3-4 4l-1-1h-1l-1 1c0 1 1 2 1 3h1 0c-3 1-5 0-8-1-1 0-2-1-3-1z" class="M"></path><path d="M194 185h-1l1 1 1-1h1c1-1 2-1 3-2h1v1l-2 1-1 3h0 1 2l-2 2-1 1c-1 1-1 3-1 5h0v1l-1 2-2-5-2-3h-9c-3 0-6 1-9 1-2 0-2 0-3 2l1 2-1 1v2l-4-3v-1-1c0-1 0-1-1-2 0-1-2-1-3-2h-2l-1-1c-1 0-2 0-3-1s-3 0-4 0l-2-1c-1 0-3-1-4-2h6l1-1 3 1 6 1c1 0 2 1 3 1 3 1 5 2 8 1h0l1-1s1 0 2-1l1-1 2-1h1l4 3s1 0 2-1l8-1z" class="W"></path><path d="M194 193l-2-2v-1c1-1 2-1 3-2h2 0 1 2l-2 2c-3 0-2 1-4 2v1z" class="j"></path><path d="M194 193v-1c2-1 1-2 4-2l-1 1c-1 1-1 3-1 5h0v1l-1 2-2-5 1-1z" class="g"></path><path d="M194 185h-1l1 1 1-1h1c1-1 2-1 3-2h1v1l-2 1c-7 2-17 4-25 3h0l1-1s1 0 2-1l1-1 2-1h1l4 3s1 0 2-1l8-1z" class="T"></path><path d="M153 184l3 1 6 1c1 0 2 1 3 1v1l1 1c2 2 3 2 5 2h1l14-2c2 0 4 1 5 2h-9c-3 0-6 1-9 1-2 0-2 0-3 2l1 2-1 1v2l-4-3v-1-1c0-1 0-1-1-2 0-1-2-1-3-2h-2l-1-1c-1 0-2 0-3-1s-3 0-4 0l-2-1c-1 0-3-1-4-2h6l1-1z" class="M"></path><path d="M162 186c1 0 2 1 3 1v1l1 1-1 1-3-1h-1c-2-1-3-1-5-2h5l1-1z" class="V"></path><path d="M153 184l3 1 6 1-1 1h-5-6c-1 0-3-1-4-2h6l1-1z" class="W"></path><defs><linearGradient id="J" x1="161.852" y1="421.935" x2="192.83" y2="453.876" xlink:href="#B"><stop offset="0" stop-color="#1e1e1d"></stop><stop offset="1" stop-color="#65646a"></stop></linearGradient></defs><path fill="url(#J)" d="M185 399c-1-3 0-6 0-9 2 2 4 5 4 8 0 1 0 3 1 5v4c1 1 0 2 1 3 0 11-1 22-2 33 0 2 0 5-1 7 0 1-1 2-2 3l-1 1-1 1-1 2h1c2 0 3 0 4 1 1 2 1 4 1 6h-1c-2-2-3-2-5-3h-2c-6 1-9 4-13 9h-1c-1 1-1 2-2 3v1l-3-3h0c3-7 8-11 12-17 4-7 6-14 7-22 3-11 4-21 4-33z"></path><path d="M186 428c0-1 0-3 1-4 0-4 1-9 1-13l1 17-3 6v-6z" class="P"></path><path d="M179 455v-1c1-3 1-6 3-9 0 2-1 3 1 4v2h1c0-1 1-1 2-1v2c-1 0-1 1-1 2l-1 1c0-1 1-2 0-3l-5 3z" class="F"></path><path d="M182 445c1-2 1-4 1-6 1-4 2-8 3-11v6c0 5-1 11-3 15-2-1-1-2-1-4z" class="C"></path><path d="M179 455l5-3c1 1 0 2 0 3l-1 2h1c2 0 3 0 4 1 1 2 1 4 1 6h-1c-2-2-3-2-5-3h-2 5c0-1-1-1-1-2h-1v-1c-2 0-2 0-3-1v-2l-2 1h0v-1z" class="R"></path><path d="M185 399c-1-3 0-6 0-9 2 2 4 5 4 8 0 1 0 3 1 5v4 10 5c-1 1-1 1-1 2v4l-1-17v-7c0-1 0-1-1-2v-1l1-1-1-1h-2z" class="B"></path><path d="M188 404c3 6 1 12 2 18-1 1-1 1-1 2v4l-1-17v-7z" class="E"></path><defs><linearGradient id="K" x1="184.242" y1="422.3" x2="188.269" y2="448.775" xlink:href="#B"><stop offset="0" stop-color="#121214"></stop><stop offset="1" stop-color="#313132"></stop></linearGradient></defs><path fill="url(#K)" d="M190 407c1 1 0 2 1 3 0 11-1 22-2 33 0 2 0 5-1 7 0 1-1 2-2 3l-1 1c0-1 0-2 1-2v-2c-1 0-2 0-2 1h-1v-2c2-4 3-10 3-15l3-6v-4c0-1 0-1 1-2v-5-10z"></path><defs><linearGradient id="L" x1="146.484" y1="185.192" x2="138.808" y2="197.988" xlink:href="#B"><stop offset="0" stop-color="#020201"></stop><stop offset="1" stop-color="#292627"></stop></linearGradient></defs><path fill="url(#L)" d="M120 176c6 2 12 5 18 6l4 2c1 0 3 1 4 1 1 1 3 2 4 2l2 1c1 0 3-1 4 0s2 1 3 1l1 1h2c1 1 3 1 3 2 1 1 1 1 1 2v1 1l4 3c2 3 4 5 6 8l4 6-1 1h-4c-1 0-2 1-3 2 0-1 0 0-1-1 0-1-1-2-1-2 0-1-2 0-2 0v-1l-1-2h-1v4l-2-1-2-2-1 1c-3-3-5-5-7-8-8-8-18-14-28-20v-1-1-1l-2-1-1-1c-1 0-1-1-2-1-1-1-1-1-1-2z"></path><path d="M152 188c1 0 3-1 4 0s2 1 3 1l1 1h2c1 1 3 1 3 2 1 1 1 1 1 2v1 1l4 3c2 3 4 5 6 8l4 6-1 1h-4c-1 0-2 1-3 2 0-1 0 0-1-1 0-1-1-2-1-2 0-1-2 0-2 0v-1l-1-2h-1v4l-2-1-2-2v-1-1h1c0-2 0-2-1-3h1l1 1v2c1-1 1-2 2-3v-3h0c-2-5-7-9-10-13h0l-4-2z" class="D"></path><path d="M162 210l1-1 2 2 1-1v4l-2-1-2-2v-1z" class="C"></path><path d="M152 188c1 0 3-1 4 0s2 1 3 1l1 1h2c1 1 3 1 3 2 1 1 1 1 1 2v1 1l4 3c2 3 4 5 6 8l4 6-1 1h-4l1-1c0-1-1-2-1-2 0-4-2-6-5-8-1-1-2-1-3-2-2-3-5-9-8-10l-3-1h0l-4-2z" class="O"></path><defs><linearGradient id="M" x1="816.26" y1="605.683" x2="806.855" y2="599.393" xlink:href="#B"><stop offset="0" stop-color="#f89f30"></stop><stop offset="1" stop-color="#c7723e"></stop></linearGradient></defs><path fill="url(#M)" d="M819 580l3-9c0-2 2-4 2-6s1-4 2-6c0-1 0-3 1-4s0-1 1-2c0-1 1-1 1-1v-2c1-2 2-3 3-4-3 15-7 29-13 43-5 14-10 27-17 41-1-1-2-1-3-1l-3 5 1-4c1-1 1-2 1-4h0l-1 1c-1-5 2-10 3-15 0-1 0-3 1-4l4-13c3-3 3-9 4-13l3-14c2 2 2 2 2 5l-1 2v1c0 1 0 2-1 3v4l4-15v3h0c0 2 0 3-1 5l1 1h0 1v-1c1 2 1 3 2 4z"></path><path d="M812 583l4-15v3h0c0 2 0 3-1 5l1 1h0 1v-1c1 2 1 3 2 4l-6 16c-1 1-2 2-2 3-1 0-2-1-2-2l2-8 1-6z" class="AD"></path><path d="M809 597c0 1 1 2 2 2 0-1 1-2 2-3l-2 5v1c0 3-2 5-3 7l-9 20-3 5 1-4c1-1 1-2 1-4h0l8-23 3-6z" class="AC"></path><path d="M812 568c2 2 2 2 2 5l-1 2v1c0 1 0 2-1 3v4l-1 6-2 8-3 6-8 23-1 1c-1-5 2-10 3-15 0-1 0-3 1-4l4-13c3-3 3-9 4-13l3-14z" class="AG"></path><path fill="#dba871" d="M806 603h0-1v-1c1-2 1-3 2-5 0-1 0-2 1-2v-2h0c1-2 1-4 2-6v2h1l-2 8-3 6z"></path><path d="M338 659l5-1 3 2 2 1v-1l1 1c1 1 2 2 2 4h1v1c-1 2-1 4-1 6-1 1-1 2-3 2v2h1l-1 2v2c2 3 3 5 6 7l1 1 2 1c3 4 5 8 9 11l1 2v1h-1c-1 0-2 1-3 1v1h1l1 1-1 1h-1-1c-1 1-1 1-2 1v1c-2 0-2-1-2-1-1 0-2 0-2-1-1-1-3-2-4-3v-3h0c-1-1-2-2-3-4-1 0-7-9-8-10-1-3-4-6-6-9l-1-1h0v-1c0-2-1-3-2-4h0l1-1h0v-2-1c0-1 0-1 1-2v-2l-1 1v-5c1 1 1 2 2 3h0c-1-1 0-1 0-2 1 0 1-1 2-1l1-1z" class="L"></path><path d="M346 683c4 1 4 3 7 5 1 1 1 2 2 4l-4-5c-2 0-2-1-3-1l-2-3z" class="F"></path><path d="M346 683h0l2 3 3 5v2l1 1v1c-1-1-1-2-3-2l-1-2v-3c-1-1-2-3-3-4v-1h1z" class="c"></path><path d="M340 679h2 2s0 1 1 1h2 1c2 3 3 5 6 7l-1 1c-3-2-3-4-7-5h0c-1-1-2-2-2-3-1 0-2 1-2 0-1 0-2-1-2-1z" class="K"></path><path d="M335 678h1s1 0 1 1c1 1 1 2 2 2 1 1 2 3 3 4-1-3-1-4-3-5v-1l1-1v1s1 1 2 1c0 1 1 0 2 0 0 1 1 2 2 3h-1v1c1 1 2 3 3 4v3l1 2v1l1 1c1 0 1 1 1 2h0-2c-1 0-7-9-8-10-1-3-4-6-6-9z" class="m"></path><path d="M337 660l1-1c-2 3-3 4-3 7 1 3 2 5 3 7l2 1c2 1 4 2 6 1l2 3v2h-1-2c-1 0-1-1-1-1h-2-2v-1l-1 1v1c2 1 2 2 3 5-1-1-2-3-3-4-1 0-1-1-2-2 0-1-1-1-1-1h-1l-1-1h0v-1c0-2-1-3-2-4h0l1-1h0v-2-1c0-1 0-1 1-2v-2l-1 1v-5c1 1 1 2 2 3h0c-1-1 0-1 0-2 1 0 1-1 2-1z" class="C"></path><path d="M335 666c1 3 2 5 3 7l2 1c2 1 4 2 6 1l2 3v2h-1c0-1-1-1-1-2h-1-2l-1 1c-1-1-1-2-2-3h-2c-1 0-1-2-2-2v-2c-1-2-1-4-1-6z" class="G"></path><path d="M354 687l1 1 2 1c3 4 5 8 9 11l1 2v1h-1c-1 0-2 1-3 1v1h1l1 1-1 1h-1-1c-1 1-1 1-2 1v1c-2 0-2-1-2-1-1 0-2 0-2-1-1-1-3-2-4-3v-3c1 2 2 4 4 4 1-1 2-1 3-1l1 1 1-1-1-1-1-2c1-1-2-6-3-8l-1-1c-1-2-1-3-2-4l1-1z" class="C"></path><path d="M356 693l1 1 1 1v-1c2 2 3 5 5 7l-3 2-1-2c1-1-2-6-3-8z" class="K"></path><path d="M338 659l5-1 3 2 2 1v-1l1 1c1 1 2 2 2 4h1v1c-1 2-1 4-1 6-1 1-1 2-3 2v2h1l-1 2-2-3c-2 1-4 0-6-1l-2-1c-1-2-2-4-3-7 0-3 1-4 3-7z" class="N"></path><path d="M342 673v-1c-2 0-2-1-3-3l1-1c1 1 2 1 3 2l2 1c-1 0-2 0-3 2z" class="AB"></path><path d="M345 671v-1h4c-1 1-2 2-2 3h-5c1-2 2-2 3-2z" class="w"></path><path d="M348 661v-1l1 1c1 1 2 2 2 4h1v1c-1 2-1 4-1 6-1 1-1 2-3 2l-1-1c0-1 1-2 2-3h-4v1l-2-1c1 0 0 0 1-1l-1-2v-1c-1-1-1-1-2-3 1 0 1-1 1-1 2-1 3-1 5-1h1z" class="P"></path><path d="M349 670v-2c1-1 1-1 3-2-1 2-1 4-1 6-1 1-1 2-3 2l-1-1c0-1 1-2 2-3z" class="B"></path><path d="M343 666c-1-1-1-1-2-3 1 0 1-1 1-1 2-1 3-1 5-1 1 2 2 3 1 4 0 2-1 2-2 3-1 0-2-1-3-2h0z" class="h"></path><path d="M544 180c-2-1-2-2-3-3l1-1h31 9 3l1 1c1 1 3 1 4 1 4-1 9 0 12-1h2l2-1h0l1 1c1 1 1 3 1 5h0c-1 3-1 3-3 5-2 1 1-1-1 1-1 0-1 0-1 1-1 0-1 0-2-1h1c-1-1-1-2-2-2h-1c-6 1-12 0-18 0-4-1-8-2-12 1-1 1-3 4-4 5 1 2 1 4 2 5l-1 2-2-4c0-1 0-2-1-4v-3c-7 0-14 2-21 2h0l1-2h-1v-1h1l2-1c-1-1-1-1-1-2 2 0 2 0 3-1v-1h-3v-1l1-1h-1z" class="Q"></path><path d="M568 177l6 1v1c0 1-1 2-2 2l-1 1-1 1-1-1c-1 1-2 1-3 1h-1l1-1h-3l-1-1c2 0 5 1 7 0l1-1v-1h-4l2-2z" class="f"></path><path d="M565 183h1c1 0 2 0 3-1l1 1-3 3c0 1 0 1-1 2v1c-1 0-1 1-1 2h0v1c1 2 1 4 2 5l-1 2-2-4c0-1 0-2-1-4v-3h1 0l-1-2h-2l-2 1 2-2h5c-1-1-1-1-1-2z" class="k"></path><path d="M544 180h8 0 1c2 1 5 1 8 1l-1 1h-5-2l1 1h7v1h-5c-4 1-8 1-11 2-1-1-1-1-1-2 2 0 2 0 3-1v-1h-3v-1l1-1h-1z" class="S"></path><path d="M545 186c3-1 7-1 11-2l5 1-2 2 2-1h2l1 2h0-1c-7 0-14 2-21 2h0l1-2h-1v-1h1l2-1z" class="o"></path><path d="M544 180c-2-1-2-2-3-3l1-1h31 9 3l1 1c1 1 3 1 4 1h-3v1h4v1h-8c-2 0-3-1-5 0h-1c-2 1-3 2-6 2l1-1c1 0 2-1 2-2v-1l-6-1-2 2c-1 1-3 1-4 1-3-1-6-1-9-1l-1 1h0-8z" class="W"></path><path d="M552 180c-2 0-2-1-3-2l1-1h6 12l-2 2c-1 1-3 1-4 1-3-1-6-1-9-1l-1 1h0z" class="Z"></path><path d="M606 176l1 1c1 1 1 3 1 5h0c-1 3-1 3-3 5-2 1 1-1-1 1-1 0-1 0-1 1-1 0-1 0-2-1h1c-1-1-1-2-2-2h-1c-6 1-12 0-18 0-4-1-8-2-12 1-1 1-3 4-4 5v-1h0c0-1 0-2 1-2v-1c1-1 1-1 1-2l3-3 1-1c3 0 4-1 6-2h1c2-1 3 0 5 0h8v-1h-4v-1h3c4-1 9 0 12-1h2l2-1h0z" class="Y"></path><path d="M600 180h0 2l1 1c0 1-1 2-2 3h-1l-1-1h-2l-1-1h4v-2z" class="V"></path><path d="M606 176l1 1c-1 2-2 7-5 8l-2 1h-1c-2-1-3-1-4-1l5-1h1c1-1 2-2 2-3l-1-1v-1l2-2 2-1h0z" class="Z"></path><path d="M590 178c4-1 9 0 12-1h2l-2 2v1h-2 0c-3 1-6 0-9-1h-4v-1h3z" class="j"></path><path d="M607 177c1 1 1 3 1 5h0c-1 3-1 3-3 5-2 1 1-1-1 1-1 0-1 0-1 1-1 0-1 0-2-1h1c-1-1-1-2-2-2l2-1c3-1 4-6 5-8z" class="C"></path><path d="M571 182c3 0 4-1 6-2h1c2-1 3 0 5 0v1c2 1 6 0 6 2h-5 0v1c2 0 4 0 5 1 2 0 3 0 5-1l1 1c1 0 2 0 4 1-6 1-12 0-18 0-4-1-8-2-12 1-1 1-3 4-4 5v-1h0c0-1 0-2 1-2v-1c1-1 1-1 1-2l3-3 1-1z" class="Q"></path><path d="M571 182c3 0 4-1 6-2h1c2-1 3 0 5 0v1c2 1 6 0 6 2h-5 0v1c-4 0-8-1-12 0-1 1-3 2-5 2l3-3 1-1z" class="V"></path><path d="M714 622l1 1 1 1 1-1h1c0 2-1 3-1 5-1 3-3 6-5 9l-28 43c-1-1-1-1-2-1 1-2 1-2 1-4l2-1c1-2 2-4 2-7 0-1 1-2 0-3l-3-3h-1c0-1-1-1-2-2 1-1 1-2 2-3 2-1 2-3 3-4v-1l3-4 2-2c2-5 5-9 7-13l2-3c1-2 1-4 3-5h5 2 2 1l1-2z" class="m"></path><path d="M716 624l1-1h1c0 2-1 3-1 5-1 3-3 6-5 9l-1-1-1-1c0-1 1-1 1-2 2-3 4-5 5-9z" class="L"></path><path d="M692 648l3-3v2l2-1c0 2-2 3-3 6 0 1-2 2-3 3v1h-1l-1-3c2-2 3-3 3-5z" class="O"></path><path d="M689 647c1 1 0 1 1 1l-2 3v1c1-2 3-3 4-4 0 2-1 3-3 5l1 3c-1 2-2 3-2 4l-1 1v3l-3-3h-1c0-1-1-1-2-2 1-1 1-2 2-3 2-1 2-3 3-4v-1l3-4z" class="F"></path><path d="M689 653l1 3c-1 2-2 3-2 4l-1 1v3l-3-3c1-3 3-5 5-8z" class="X"></path><path d="M714 622l1 1-7 9c-1 1-1 1-2 1l-2 3c-1 1-2 2-2 4-1-1-1 0-2-1l1 1c-1 1 0 1-1 2h0c-1 1-3 3-3 4l-2 1v-2l-3 3c-1 1-3 2-4 4v-1l2-3c-1 0 0 0-1-1l2-2c2-5 5-9 7-13l2-3c1-2 1-4 3-5h5 2 2 1l1-2z" class="C"></path><path d="M706 626l1-1c1 1 1 0 2 1v1c0 2-2 4-3 6l-2 3c0-2 0-3 1-5 1-1 2-2 2-4l-1-1z" class="c"></path><path d="M706 626l1 1c0 2-1 3-2 4-1 2-1 3-1 5-1 1-2 2-2 4-1-1-1 0-2-1l1 1c-1 1 0 1-1 2h0c-1 1-3 3-3 4l-2 1v-2l11-19z" class="M"></path><defs><linearGradient id="N" x1="274.464" y1="130.104" x2="291.122" y2="86.208" xlink:href="#B"><stop offset="0" stop-color="#c9c9c8"></stop><stop offset="1" stop-color="#f7f8f7"></stop></linearGradient></defs><path fill="url(#N)" d="M252 92c-1-3-1-7 0-10 1-6 2-9 5-14-1 4-1 9-1 13 1 10 6 19 12 27 5 6 11 11 17 16 6 4 12 9 20 9 3 1 6 0 9-1h1v1l-3 5c-1 1-3 2-4 2h-1l-2 2c-2-1-2-2-3-4 0-1 0-1-1-2-3-1-5 1-7 1s-4 0-5-1c0-1 0-1-1-2-2-1-4-2-5-4h-1c-2 0-3-1-4-2-4-2-8-5-11-8-2-2-4-5-7-5-2 0-2 1-4 3l-4-26z"></path><path d="M565 192c1-1 3-4 4-5 4-3 8-2 12-1 6 0 12 1 18 0h1c1 0 1 1 2 2h-1c1 1 1 1 2 1h0-2c-1 1-1 2-2 1-1 0-2-1-2-1l-1 1h-6l-1-1v1 1c3 1 8 0 11 0l5 1h0c-1 1-2 2-2 3-1 0-1 1-1 2h1v2h1c0-1 0 0 1-1v2h-1v1h-2v3c1 1 1 2 1 3-1 1-1 1-2 1v-1h-1-2c-4-2-12-1-17 0-2 0-4 1-6 1-2-1-4-1-6-1v-2h-1 0c-2-1-4 0-5 0l-1-2c-1-1 0-2 0-3l3-1c2 0 3 2 4 3v-1-1c-1-1-1-1-1-2h0l-1-1c-1-1-1-3-2-5z" class="P"></path><path d="M572 191h2v1c0 1-2 2-2 3l-2-1-1-1-1 1-1-1v-1c2-1 3-1 5-1z" class="B"></path><path d="M581 186c6 0 12 1 18 0h1c1 0 1 1 2 2h-1c1 1 1 1 2 1h0-2c-1 1-1 2-2 1-1 0-2-1-2-1l-1 1h-6l-1-1v1 1c3 1 8 0 11 0-2 1-5 1-7 1l-1 1c-3 1-9 1-12 0-1 0-1 0-2-1 0 0 0-1-1-1h-1c0-1 0-1 1-2v-1c1-1 1-1 2-1l1-1h1z" class="F"></path><path d="M572 199h0 1l3 1h0 2 1c1-1 2-1 2-1 2-1 2-2 2-3h-3c-1 0-2 0-3-1l1-1h13c1 1 1 1 3 1h0 3c1 1 1 1 2 1s1 0 2 1h1 1v2h1c0-1 0 0 1-1v2h-1v1h-2v3c-2-1-3-2-5-3h-1-3c-2-1-3-1-5-1h-1c-1 1-2 1-4 1h-1l-6 3-1 1-1-1c1-1 0-1 0-2h0c-1-1-1-2-2-3z" class="H"></path><path d="M568 198v-1l1 1 1 1c1 0 1 1 2 0 1 1 1 2 2 3h0c0 1 1 1 0 2l1 1 1-1 6-3h1c2 0 3 0 4-1h1c2 0 3 0 5 1h3 1c2 1 3 2 5 3 1 1 1 2 1 3-1 1-1 1-2 1v-1h-1-2c-4-2-12-1-17 0-2 0-4 1-6 1-2-1-4-1-6-1v-2h-1 0c-2-1-4 0-5 0l-1-2c-1-1 0-2 0-3l3-1c2 0 3 2 4 3v-1-1c-1-1-1-1-1-2h0z" class="q"></path><path d="M521 157c3 3 8 8 10 12h0c2 0 3 1 5 0 2 3 3 5 4 8 1 1 1 1 1 2 1 2 2 5 2 8h-1l-1-1c-1 1-2 1-3 2h-1c-1-1-1-1-2-1h0l-1 1h1v3h-1l-1-1c-1-2-1-1-2-2-1 0-1 0-2 1-2 1-3 0-5 0l2 1h1v1c0 1-2 1-2 3-1 2-2 2-2 4 1 1 2 2 3 2v1s-1 0-2 1l-2-1h-1c0 1 0 1-1 2l-1 1v1c0-3-1-5 0-7v-1l-2-1c0-2-1-4-1-6-1-1 0-1 0-2h0v-2-1h-1c0-1 0-1 1-2h0 0-2v-2h0c1-1 1-1 1-3l-2-1h-1l-1-2h0-1c0-1-1-1-2-1v2-5c0-1 0-5 1-6h2 0l-1-1c0-1 1-2 2-3l2 1v-1l1 1v1c-1 1-1 0-1 1s0 1-1 2c1 0 1 1 2 1 1-1 1-1 1-2h1l-1-1 1-1v-1c0-2 2-4 4-5z" class="D"></path><path d="M508 171c0-1 0-5 1-6h2v10h0-1c0-1-1-1-2-1v2-5z" class="d"></path><path d="M531 169h0l5 8h-3l-1-1v2l-1-1c-1-1-2-2-2-3h-1v-3c1 2 2 3 3 4 0-2 0-3-2-4v-1c1 0 1 0 1-1h1z" class="G"></path><path d="M536 169c2 3 3 5 4 8 1 1 1 1 1 2 1 2 2 5 2 8h-1l-1-1-5-9-5-8c2 0 3 1 5 0z" class="Q"></path><path d="M521 157c3 3 8 8 10 12h-1c0 1 0 1-1 1v1c2 1 2 2 2 4-1-1-2-2-3-4 0-1 0-1-1-2s-1-2-2-3h-1 0v2l1 1v1c-2 0-2 0-3 1v2h-1l-4-4-2-2c1-1 1-1 1-2h1l-1-1 1-1v-1c0-2 2-4 4-5z" class="E"></path><defs><linearGradient id="O" x1="208.953" y1="493.332" x2="197.3" y2="494.697" xlink:href="#B"><stop offset="0" stop-color="#898889"></stop><stop offset="1" stop-color="#abaaab"></stop></linearGradient></defs><path fill="url(#O)" d="M199 417v10c0 1 1 2 1 3l1 8v-4h1c0 2 0 3 1 5l-1 36h1v17l2 18h0c-1 11 1 22 3 33 1 3 1 7 2 10v2c0 1 0 1 1 2v2c0 2 3 6 1 8v-1c0-1 0-2-1-3 0 1 0 2 1 4 0 2 1 6 1 9-3-6-3-13-4-19l-2-2v-1h-1v1 7c-2-5-2-12-3-17l-6-58c-1-10-2-21-2-32-1-7 0-16 1-24v-7c2-2 1-5 3-7z"></path><path d="M202 475h1v17l2 18-2-2-1-2c0-1 0-1 1-2-1-3 0-5 0-7-1-2-1-3-1-4v-3-6l-1-1c0-2 0-3 1-4l-1-2 1-2z" class="W"></path><defs><linearGradient id="P" x1="196.088" y1="427.993" x2="200.912" y2="456.507" xlink:href="#B"><stop offset="0" stop-color="#716f72"></stop><stop offset="1" stop-color="#909090"></stop></linearGradient></defs><path fill="url(#P)" d="M199 427c0 1 1 2 1 3l1 8c-3 3-2 18-2 23l-2-6c1-3 0-7 0-9 0-7 0-13 2-19z"></path><path d="M199 417v10c-2 6-2 12-2 19 0 2 1 6 0 9-2-3-1-12-1-15l1-1c0-1 0-2-1-3 0 3 1 6 0 9s0 6-1 10h0c-1-7 0-16 1-24v-7c2-2 1-5 3-7z" class="W"></path><path d="M228 149v1l1 1 2 2c0 2 1 3 3 4h1c1 0 2 0 2 2h1v1 2l2 1h2c1 1 1 2 1 4v2l-2 2h-2l-31 10-8 3v-1h-1c-1 1-2 1-3 2h-1l-1 1-1-1h1l-8 1h-1v-1h-1v-1-3c1-1 1-1 1-2s0-2 1-3v-1l2-1c0-1 0 0-1-1 3 0 8-2 12-3l3-1c3-1 5-3 8-5l2-2 2-1 7-6 2-1 4-4 1-1z" class="S"></path><path d="M223 154l4-4 1 3c-1 2-1 2-2 3h-2l-1-2z" class="F"></path><path d="M227 163h2 0l2-1v1l-1 2v1c-1 1-1 1-2 1l-1-1c0-1 0-1 1-2l-1-1z" class="X"></path><path d="M228 153c0 2 1 4 0 6v2h-1c0-1-3-4-3-5h2c1-1 1-1 2-3z" class="T"></path><path d="M228 150l1 1 2 2c0 2 1 3 3 4h1c1 0 2 0 2 2h1v1 2l2 1h2c1 1 1 2 1 4v2l-2 2h-2l1-2c0-1-1-2-3-3h-2 0-1l1-1-2-2c-1-1 1-1 0-2 0-2-2-5-3-6h-1c-1-2-1-3-1-5z" class="Y"></path><path d="M212 162c0 2-1 4-1 5-1 4 0 6-3 10v4l-8 3v-1h-1c-1 1-2 1-3 2h-1l-1 1-1-1h1c1-1 3-2 4-2 0-1 1-3 1-4 0-3 1-7 0-9l3-1c3-1 5-3 8-5l2-2z" class="c"></path><path d="M210 164c-1 4-3 7-6 9v3l-3 2h0c0-3 1-6 1-9 3-1 5-3 8-5z" class="F"></path><path d="M187 173c3 0 8-2 12-3 1 2 0 6 0 9 0 1-1 3-1 4-1 0-3 1-4 2l-8 1h-1v-1h-1v-1-3c1-1 1-1 1-2s0-2 1-3v-1l2-1c0-1 0 0-1-1z" class="b"></path><path d="M187 173c3 0 8-2 12-3 1 2 0 6 0 9 0 1-1 3-1 4-1 0-3 1-4 2l-8 1h-1v-1h-1v-1l2 1 2-1h0 2 2c3-2 5-4 6-7 0-2 0-3-2-4-1-1-2-1-4-1l-3 1v1c-1 1-2 1-3 2v-1l2-1c0-1 0 0-1-1z" class="M"></path><path d="M221 155l2 4 4 4 1 1c-1 1-1 1-1 2l1 1c1 0 1 0 2-1h1l1 1c-1 1-1 1-1 2s0 1-1 1l-1-1c-3 3-5 2-8 3l-1 1c-2 1-5 2-7 1h0l-1-1 1-1v-2c-1-1 0-2 0-3 1-2 1-3 1-6h0l7-6z" class="b"></path><path d="M541 186l1 1v1h1l-1 2h0c7 0 14-2 21-2v3c1 2 1 3 1 4l2 4 1-2 1 1h0c0 1 0 1 1 2v1 1c-1-1-2-3-4-3l-3 1c0 1-1 2 0 3l1 2c1 0 3-1 5 0 1 1 0 2 0 3h-4-3c0 1 0 2 1 2v1h-1 0l-1-1h-3c0 1 0 1-1 2l1 1-1 1c-2 1-5 0-8 0h-6-1l-1 1h-1l-1 1s0 1 1 1l-1 1-2-1v-3h1l1-1c-3-2-5-4-8-5-2-1-3 0-5-1v-1-2l2 1v-3h-3c1-1 2-1 2-1v-1c-1 0-2-1-3-2 0-2 1-2 2-4 0-2 2-2 2-3v-1h-1l-2-1c2 0 3 1 5 0 1-1 1-1 2-1 1 1 1 0 2 2l1 1h1v-3h-1l1-1h0c1 0 1 0 2 1h1c1-1 2-1 3-2z" class="V"></path><path d="M539 203l2 2c0 1-1 1-1 2l-2-2h-1c0 2 1 2 1 4-2-1-5-3-7-4h0 3 1c1-1 3-1 4-2z" class="B"></path><path d="M542 203l1 1v2l1 1c1-1 1-1 1-2h1l1 2-3 1 1 1h2v2l1 1v2h-6l1-1-1-1v-2h-1l2-2-1-1v-4z" class="F"></path><path d="M537 200c2-2 4-1 7-1h4 0l-1 1-2 2v2l1 1h-1c0 1 0 1-1 2l-1-1v-2l-1-1c-2-1-4-1-5-2h-1c1 2 2 2 3 2-1 1-3 1-4 2h-1-3 0c-1-1-1 0 0-1l3-3c1-1 2-1 3-1z" class="E"></path><path d="M544 199h4 0l-1 1-2 2v2l1 1h-1l-1-2c-1-1 0-3 0-4h0z" class="D"></path><path d="M548 199c4 1 9 2 12 1h1 1c0 1-1 2 0 3l1 2c1 0 3-1 5 0 1 1 0 2 0 3h-4-3c0 1 0 2 1 2v1h-1 0l-1-1h-3c0 1 0 1-1 2l1 1-1 1c-2 1-5 0-8 0v-2l-1-1v-2h-2l-1-1 3-1-1-2-1-1v-2l2-2 1-1z" class="q"></path><path d="M547 207h0 1l1-2h6v3h-6c-1 1-1 1-2 1h-2l-1-1 3-1z" class="C"></path><path d="M548 199c4 1 9 2 12 1h1 1c0 1-1 2 0 3l1 2h-3v-2c-1-1-1-1-2-1s-2 0-3 1c0-1-1-1-1-1-1-1-2-1-3-1-1 1-1 1-2 1l-2-2 1-1z" class="J"></path><path d="M563 205c1 0 3-1 5 0 1 1 0 2 0 3h-4-3-6v-3c2 1 4 0 5 0h3z" class="D"></path><path d="M555 208h6c0 1 0 2 1 2v1h-1 0l-1-1h-3c0 1 0 1-1 2l1 1-1 1c-2 1-5 0-8 0v-2l-1-1v-2c1 0 1 0 2-1h6z" class="g"></path><path d="M555 208h6c0 1 0 2 1 2v1h-1 0l-1-1h-3l-7 1c-1-1-2-2-3-2h0c1 0 1 0 2-1h6z" class="R"></path><path d="M541 186l1 1v1h1l-1 2h0c7 0 14-2 21-2v3c1 2 1 3 1 4l2 4 1-2 1 1h0c0 1 0 1 1 2v1 1c-1-1-2-3-4-3l-3 1h-1-1c-3 1-8 0-12-1h0-4c-3 0-5-1-7 1-1 0-2 0-3 1-1-2-2-2-3-2l-2 1h0c0-1 0-2-1-4h-2v-1l2-2c0-1 0-1 1-2 0 1 0 1 1 2 0-1 0-1 1-1v1h1l1-1v-2l1 1h1v-3h-1l1-1h0c1 0 1 0 2 1h1c1-1 2-1 3-2z" class="I"></path><path d="M552 193h-1c-1 0-1 0-2-1l4-2c2 0 4 1 6 0h1c1 1 1 0 1 1v1h2v1c-3 1-5 0-7 0h-1v-1h-1v1h-2z" class="N"></path><path d="M563 191c1 2 1 3 1 4l2 4 1-2 1 1h0c0 1 0 1 1 2v1 1c-1-1-2-3-4-3l-3 1h-1v-1l-3-3h-1l-2-1c-1 0-1 0-1-1h-1v1l-1-2h2v-1h1v1h1c2 0 4 1 7 0v-1-1z" class="U"></path><path d="M558 196c2 0 4 0 6 1l1 2-3 1h-1v-1l-3-3z" class="Q"></path><path d="M537 200l-1-1c-1 0-2 0-3-1s1-1 1-3c-1 1-1 1-2 0 1-1 2-1 3-2h0 2s1 1 2 1h0l-1 3h0l2-1 1 1 1-1h2 0 3c1-1 0-1 1-1h1 2l1 1 1-1v-1h1c0 1 0 1 1 1l2 1h1l3 3v1h-1c-3 1-8 0-12-1h0-4c-3 0-5-1-7 1z" class="k"></path><path d="M557 196h1l3 3v1h-1c-2 0-2 0-3-1-2-1-3-2-6-1v-1c2-1 4-1 6-1z" class="i"></path><path d="M746 475l1 1h0c-1 3-1 6 0 9l2 2 1 1-1 1h1v6 18h-1c-1 4 0 8 0 12-1 5-2 10-2 15l-5 26-1 1-1 1h0l-1-2c-1 1-1 2-2 3s-1 1-2 1l-1-3-2-1c0-2 0-3-1-4v-1h0v-4h0c1-2 1-6 2-8l2-14c1-2 1-3 2-5 0-4 1-7 1-10v-3c1-2 1-2 1-4v-1l1-7c2-5 0-11 1-16v3h1c0-2-1-4 0-5v-1l1-3c0-1 2-6 3-8z" class="K"></path><path d="M735 561l1 3h1c1 0 2 1 2 1v1c-1 1-1 2-2 3s-1 1-2 1l-1-3v-2l1-4z" class="R"></path><path d="M746 475l1 1h0c-1 3-1 6 0 9l2 2 1 1-1 1h1v6 18h-1v-6c0-2-1-2-3-3-1-3 0-6 0-10 0-2 1-4 0-5l-2-1s0-1 1-1l1-1v-1c0-1-1-1-1-2v-2h0c1-1 1-2 1-3-1 2-2 4-2 5h-1c0-1 2-6 3-8z" class="C"></path><path d="M741 489v3h1c0-2-1-4 0-5v-1c1 6 0 13 0 19-1 9-1 19-3 28 1 4 0 9-1 13h-1v-2l1-7c0-3-1-5-1-7 0-4 1-7 1-10v-3c1-2 1-2 1-4v-1l1-7c2-5 0-11 1-16z" class="E"></path><path d="M737 530c0-4 1-7 1-10v-3c1-2 1-2 1-4v-1c1 5 0 10 0 15 0 2-1 4 0 6 1 4 0 9-1 13h-1v-2l1-7c0-3-1-5-1-7z" class="H"></path><path d="M732 566c0-2 0-3-1-4v-1h0v-4h0c1-2 1-6 2-8l2-14c1-2 1-3 2-5 0 2 1 4 1 7l-1 7v2h1l-2 11c0 1-1 4-1 4l-1 4v2l-2-1z" class="G"></path><path d="M734 565v-2l-1-1v-3l1-1c0-1-1-3-1-4 1-3 1-6 2-9l1-2 1 1v2h1l-2 11c0 1-1 4-1 4l-1 4z" class="p"></path><path d="M577 175h13c2 0 3 0 5-1 1 0 1 0 2 1l1-1c1 0 3 0 4 1 1 0 1 0 2-1 1 0 1 0 3 1 1 0 6 0 7-1h13 0 1 2c4 0 7 1 11 1 2 0 8 0 9 2l1 1h1c-1 0-1 1-2 0v-1l-2-1c0 1 1 1 1 2l1 1c0 1-1 2 0 2h0c2 0 2 0 4 1h0l1 1c1 1 2 2 3 2l1 1-1 1c1 1 1 1 2 1l1-1c1-1 0-1 1-1v2c-2 2-5 1-8 1l1 1h2l-1 2h1v1h-13-1-7c-1-1-3-1-4-1l-1-1c-1 0-2 0-3-1-2 0-3 1-5 1s-4 0-6 1h0-1c-1 0-3 0-5-1-2 1-4 1-6 1l-5-1c-3 0-8 1-11 0v-1-1l1 1h6l1-1s1 1 2 1c1 1 1 0 2-1h2 0c0-1 0-1 1-1 2-2-1 0 1-1 2-2 2-2 3-5h0c0-2 0-4-1-5l-1-1h0l-2 1h-2c-3 1-8 0-12 1-1 0-3 0-4-1l-1-1h-3-9 4v-1z" class="P"></path><path d="M656 192h-5l-1-1h-2l1-1h2c1-1 2-1 3-1l1 1h2l-1 2z" class="E"></path><path d="M649 178l1 1c0 1-1 2 0 2h0c2 0 2 0 4 1h0l1 1c-1 0-1 0-2 1h-1-2c-1 1-2 1-3 1h-1c-1 0-2 1-2 2h-1-1-1c1-2 2-2 3-3 2 0 3 0 5-1l-1-1c1-1 1-2 1-4h0z" class="B"></path><path d="M577 176h22c1 1 5 0 7 0l-2 1h-2c-3 1-8 0-12 1-1 0-3 0-4-1l-1-1h-3-9 4z" class="V"></path><path d="M628 190c4-1 7-3 10-5 1-1 3-3 6-4-3 3-7 6-9 9 0 1 1 1 2 2h7v1h-1-7c-1-1-3-1-4-1l-1-1c-1 0-2 0-3-1z" class="Z"></path><path d="M641 178c1 0 1-1 2 0 0 1 1 1 2 2 0 1-1 1-1 1-3 1-5 3-6 4-3 2-6 4-10 5-2 0-3 1-5 1s-4 0-6 1h0-1c-1 0-3 0-5-1 2 0 4 0 5-1 5 0 11 0 15-2 1-1 3-2 4-3 0-1 0-1 1-2l1 1c1-1 2-1 3-2h-2v-1l3-2h1l-1-1z" class="R"></path><path d="M599 176h21c6-1 12-1 18 1h1c1 0 0 0 2 1l1 1h-1l-3 2v1h2c-1 1-2 1-3 2l-1-1c-1 1-1 1-1 2-1 1-3 2-4 3 0-1-1-1-1-2l-1 1-3-1 1-1v-1-1h3l1-1v-1l-1 1-1-1h-1v1h-1v-2l-1-1h-2c-1-1-2 0-4 0l-2 1 1 2c-2 0-3 1-5 1h-1c-2 0-3-1-5-1h0c0-2 0-4-1-5l-1-1h0c-2 0-6 1-7 0z" class="M"></path><path d="M620 179l1-2c3 0 2-1 5 0l1-1h1v1h1l-1 2h2l1 1v-2l3-1 2 2h0v1c-1 0-1 1-1 1l-2 1v1h1c-1 1-3 1-5 1l-2 1h0v-1-1h3l1-1v-1l-1 1-1-1h-1v1h-1v-2l-1-1h-2c-1-1-2 0-4 0z" class="b"></path><path d="M606 176c3 0 5 0 8 1 1 0 4-1 4 0v2 1l1 2c-2 0-3 1-5 1h-1c-2 0-3-1-5-1h0c0-2 0-4-1-5l-1-1z" class="a"></path><path d="M620 179c2 0 3-1 4 0h2l1 1v2h1v-1h1l1 1 1-1v1l-1 1h-3v1 1l-1 1 3 1 1-1c0 1 1 1 1 2-4 2-10 2-15 2-1 1-3 1-5 1-2 1-4 1-6 1l-5-1c-3 0-8 1-11 0v-1-1l1 1h6l1-1s1 1 2 1c1 1 1 0 2-1h2 0c0-1 0-1 1-1 2-2-1 0 1-1 2-2 2-2 3-5 2 0 3 1 5 1h1c2 0 3-1 5-1l-1-2 2-1z" class="X"></path><path d="M615 186c1 0 2 0 3 1-2 1-3 2-6 3l1-1c1-1 2-2 2-3z" class="I"></path><path d="M620 179c2 0 3-1 4 0h2c-1 1-2 1-3 1s-1 1-1 1c-1 1-2 1-3 1l-1-2 2-1z" class="g"></path><path d="M615 185h1c1-1 2-1 3-1v-1h1 0 2l1 1c-2 2-3 2-5 3-1-1-2-1-3-1l-2-1h2z" class="U"></path><path d="M611 186c1-1 1-1 2-1l2 1c0 1-1 2-2 3l-1 1h-4c0-1 2-3 3-4z" class="E"></path><path d="M616 190l2-1h0c1-1 1-1 2-1h2 0 3v-1h-1v-1h2l3 1 1-1c0 1 1 1 1 2-4 2-10 2-15 2z" class="b"></path><path d="M603 189c0-1 0-1 1-1 2-2-1 0 1-1 2-2 2-2 3-5 2 0 3 1 5 1l2 2h-2c-1 0-1 0-2 1-3 0-3 2-6 2v1h-2 0z" class="R"></path><path d="M822 180h1c1-2 0-6 0-9 1-2 2-3 4-3 1 0 3 1 4 1l1 1c1 0 4 1 5 1 10 3 20 7 31 7 7 0 13-1 20-2 2 0 4-1 6-2l-2 3h2 0l-2 1h0c0 1 0 1-1 1l1 1-9 3c-3 1-7 2-10 4l-6 2h0c-3-1-7 1-9 1-1 2-2 2-2 4l-2 2s-1 0-1 1c0 2-1 4-2 5-1 0-1 0-2 1v1h-1l-1-1c-1 2-2 3-3 5v-2l-2 2c0-2 0-2-1-3v-1l4-4 2-2 2-3 1-1v-1l-2-1h-1l-3-3h-1c-1 0-3-1-4-1h-2l-1-1h-2l-6-2v1c-1 1-1 1-1 2v1h-1-1v-1h0v-1c-2-1-2-2-3-4v-2-1z" class="O"></path><path d="M835 177c1 0 2 1 3 2s0 2-1 3-1 2-3 3c1-2 1-4 1-7v-1z" class="c"></path><path d="M863 185h-1c-1 0-4 1-5 0-1 0-1 0-1-1 2 0 3-2 5-3h1c1 0 2 0 4 1-2 1-2 2-4 2h0l1 1z" class="R"></path><path d="M866 182h3 1 3 0 2c1 0 1 0 1 1-4 1-8 2-12 2h-1l-1-1h0c2 0 2-1 4-2z" class="I"></path><path d="M876 183h1c1-1 2-1 3-1s2 1 3 1c-3 1-7 2-10 4h-8l-2-1 1-1c4 0 8-1 12-2z" class="X"></path><path d="M894 174l-2 3h2 0l-2 1h0c0 1 0 1-1 1l1 1-9 3c-1 0-2-1-3-1l2-2h-2 0c2-1 4-1 6-2 0-1 1-1 2-2 2 0 4-1 6-2z" class="f"></path><path d="M840 187c-1-2-1-2-1-4 1-2 1-3 2-4l1-1v-1c0-1 0-1 1-2h1l1 2h1 2c1 1 2 1 2 2l-1 2h0v3l1 2v1c-1-1-1 0-1-1h-1c-1 1-1 1-2 1h-6z" class="M"></path><path d="M842 182h0c2 0 2 1 3 2l-1 1-1 1-1-1v-3z" class="Y"></path><path d="M831 185c0-2 0-2 1-4l-1-2c0-2 1 0 2-1h2c0 3 0 5-1 7h1c1 1 3 2 5 2h6 5 5c1 1 2 1 3 0 1 0 1 1 2 1 2 0 3-1 4-1h0 8l-6 2h0c-3-1-7 1-9 1-1 2-2 2-2 4l-2 2c1-3 1-4 2-6h1c-4-1-9-1-13-1h-1c-1 0-3-1-4-1h-2l-1-1h-2c-1-1-2-1-3-2z" class="R"></path><path d="M844 189c4 0 9 0 13 1h-1c-1 2-1 3-2 6 0 0-1 0-1 1 0 2-1 4-2 5-1 0-1 0-2 1v1h-1l-1-1c-1 2-2 3-3 5v-2l-2 2c0-2 0-2-1-3v-1l4-4 2-2 2-3 1-1v-1l-2-1h-1l-3-3z" class="S"></path><path d="M850 194l1 1c0 1-1 2-1 3-1 1-1 2-1 3h-1-1c-1 1-3 4-3 5l-2 2c0-2 0-2-1-3v-1l4-4 2-2 2-3 1-1z" class="d"></path><path d="M822 180h1c1-2 0-6 0-9 1-2 2-3 4-3 1 0 3 1 4 1l1 1c1 0 4 1 5 1-1 1-2 3-3 5l1 1v1h-2c-1 1-2-1-2 1l1 2c-1 2-1 2-1 4 1 1 2 1 3 2l-6-2v1c-1 1-1 1-1 2v1h-1-1v-1h0v-1c-2-1-2-2-3-4v-2-1z" class="C"></path><path d="M822 183l6 2v1c-1 1-1 1-1 2v1h-1-1v-1h0v-1c-2-1-2-2-3-4z" class="M"></path><path d="M822 180h1c1-2 0-6 0-9 1-2 2-3 4-3 1 0 3 1 4 1h0c-2 1-3 1-4 1l-2 1v1 2c-1 2 0 6-2 7h-1v-1z" class="H"></path><path d="M832 170c1 0 4 1 5 1-1 1-2 3-3 5l1 1v1h-2c-1 1-2-1-2 1l1 2c-1 2-1 2-1 4-1 0-2-1-3-1-1-1 0-1-1-1h-1v-1-2l1-1v-1c0-2 2-6 3-6 0 0 1 0 1-1l1-1z" class="F"></path><defs><linearGradient id="Q" x1="853.576" y1="371.475" x2="887.928" y2="376.72" xlink:href="#B"><stop offset="0" stop-color="#313334"></stop><stop offset="1" stop-color="#4d3c3b"></stop></linearGradient></defs><path fill="url(#Q)" d="M863 381l-1-4-2-5v-3h-1c0-1-1-3 0-3v-2-1h-1v-3-1c-1-1 0-5 0-6v-3l-1-1v-5h-1c0-2 0-4 1-5v-2-1h1l1-1c2-1 4-2 5-1 0 1 0 1 1 2v2c0 1 0 0 1 1v1h-1c-2-2-1-3-2-5h1l-1-1c-2 1-4 2-5 3l1 1 3-2v1c-1 1-3 1-3 3h-1 0c-1 1-1 1 0 2h1l1 1-1-1c0-2 0-2 1-3 1 1 1 1 2 1l2 2h0c0 1 0 1 1 2h0c1 2 5 4 7 4 1 0 0 0 1 1h1v-1h0c0-2 0-2 1-3l-1-1h1c2-2 2-3 2-5 1-2 2-2 3-3-2 5-3 7-3 12v1l1 1c-3 19-1 36 4 53l3 8c1 1 1 2 1 3-4-5-9-9-13-13l-3-4-1-3c-1-1-1-2-1-2-1-1-2-2-2-3-1-1-1-2-2-3 0 0-1-2-1-3l-2-2h2z"></path><path d="M861 381h2c1 2 3 4 4 6s1 4 3 6 3 4 5 6l5 5c1 1 1 1 2 3 0 1 0 2 1 2 0-2-1-3-1-6l3 8c1 1 1 2 1 3-4-5-9-9-13-13l-3-4-1-3c-1-1-1-2-1-2-1-1-2-2-2-3-1-1-1-2-2-3 0 0-1-2-1-3l-2-2z" class="l"></path><path d="M414 773c1 1 2 2 2 3 1 2 2 3 3 5v1c0 3 1 7 3 9 2 1 3 2 5 3s3 1 5 1c2 1 3 3 4 5 3 5 7 10 10 15 1 2 2 3 3 5l-1 4c2 5 2 7 6 10 1 1 2 1 3 2s1 2 2 3l5 8c1 0 1 0 2-1 1 1 2 2 4 3 2 4 4 7 7 11-2 0-2-1-4-2 0 1 0 1 1 1l-1 1 1 1-1 1v1c1 1 2 3 3 5h-2l-3-3h-2c-2-2-3-3-5-4s-3-3-4-4c-1-2-2-4-4-6-1-1-3-5-4-7-1-1-1-2-2-2l-3-4c-3-4-5-8-8-11-1-1-3-3-3-4l-1-2h0c0-1-1-2-1-3l-1-1c-1-1-2-2-3-4l-2-2-1-1c-2-3-4-6-6-8l-5-8h1c1-1-2-4-2-6l1 1-1-1 1-1 1 1v-3h-1v-3c-1-2-1-6-2-9z" class="C"></path><path d="M447 833h1l1 1-1 1-2-1 1-1z" class="G"></path><path d="M432 807h1c3 1 4 2 5 3-1 1-1 1-2 1-2-1-3-3-4-4z" class="U"></path><path d="M428 800h1c2 2 3 3 3 6-1 0-2-1-3-3v-1l-1-2z" class="K"></path><path d="M416 776c1 2 2 3 3 5v1c0 3 1 7 3 9 2 1 3 2 5 3l2 1c2 1 4 5 5 6h0l-1 1-1-2c0-1-2-3-3-3h-1l-3-2h-1l-1-1h-1l-1-1-2-2c-1-2-2-8-2-9l1-1c-1-2-1-3-2-5zm31 44c1 2 1 3 1 4 2 5 2 7 6 10 1 1 2 1 3 2s1 2 2 3l-2 1h-1c-1-2-3-2-4-4l-4-4v-1h0v-2c-1-1-1-2-1-4h-1c1-1 1-3 1-5z" class="H"></path><path d="M452 836c1 0 1 0 2 1h1 1l1-1c1 1 1 2 2 3l-2 1h-1c-1-2-3-2-4-4z" class="G"></path><path d="M427 794c2 1 3 1 5 1 2 1 3 3 4 5 3 5 7 10 10 15 1 2 2 3 3 5l-1 4c0-1 0-2-1-4-1-1-2-4-3-6-3-4-7-8-10-13h0c-1-1-3-5-5-6l-2-1z" class="J"></path><path d="M466 859c-2-2-3-3-4-5s-3-4-4-7-4-3-4-6l-2-2v-1l4 2h1l2-1 5 8c1 0 1 0 2-1 1 1 2 2 4 3 2 4 4 7 7 11-2 0-2-1-4-2 0 1 0 1 1 1l-1 1 1 1-1 1v1c1 1 2 3 3 5h-2l-3-3-5-6z" class="K"></path><path d="M466 859c1-1 1-1 3-1 1 1 1 2 2 4l2 1c1 1 2 3 3 5h-2l-3-3-5-6z" class="R"></path><path d="M466 846c1 1 2 2 4 3 2 4 4 7 7 11-2 0-2-1-4-2 0 1 0 1 1 1l-1 1c-3-3-7-9-9-13 1 0 1 0 2-1z" class="H"></path><defs><linearGradient id="R" x1="279.305" y1="520.992" x2="298.043" y2="522.457" xlink:href="#B"><stop offset="0" stop-color="#212123"></stop><stop offset="1" stop-color="#424141"></stop></linearGradient></defs><path fill="url(#R)" d="M290 563c-1 1-1 1-2 1 0-6-2-13-3-19l-3-32c0-7-1-14-1-21 0-3 0-6 1-8 3-1 6-1 8-4v-1c1-1 2-3 2-4v-2c1 2 0 4 0 6v16c1-2 0-5 1-8v1c0 1 1 2 1 3v1c0 2 0 6 1 8-1 1-1 1-1 2 1 1 1 3 1 4 0 6 1 12 1 18v6c1 5 2 10 2 15v6c0 2 1 8 1 10l-1 1v3c-1 0-3-2-4-2h-4z"></path><path d="M297 555c0 2 0 3 1 5v2 3c-1 0-3-2-4-2 1-1 1-2 2-3-1-1-1-2-1-3l2-2z" class="U"></path><path d="M292 495c1-2 0-5 1-8v1c0 1 1 2 1 3v1c0 2 0 6 1 8-1 1-1 1-1 2 1 1 1 3 1 4l-1-1v-2h-1 0v8c-1-5-1-11-1-16z" class="Q"></path><path d="M293 511v-8h0 1v2l1 1c0 6 1 12 1 18v6h0c-1-1-1-2-1-3v6c-1-1 0-4-1-6v4c-1-7-1-14-1-20z" class="d"></path><path d="M294 531v-4c1 2 0 5 1 6v-6c0 1 0 2 1 3h0c1 5 2 10 2 15v6c0 2 1 8 1 10l-1 1v-2c-1-2-1-3-1-5l-3-24z" class="f"></path><path d="M208 300v5l-2 5c1 1 2 1 3 1v1 5h2l-1 5v1 1l-1 4v3l-3 15h0 1l1 1c0 3-1 9 0 12v2h-2c1 6 2 11 5 17 1 3 2 5 4 8l1 1 3 3c-2 0-4-1-7-2-2 0-4-1-5-1-2 1-2 1-3 2v2h1v2c2 0 3 1 5 1l-3 3-1-1-1 2-3-3-1-1-1-1c0 1 0 1-1 2h-1l1-3c-1-2-1-3-2-5-1-3-1-4 0-6h-2c-1 0-2 0-3-1v-6-9c0-3 1-7 1-11 1-2 1-4 1-6l1-2v-3c1-2 0-4 1-6 1-1 1-1 1-2h0c1-2 0-4 0-5 1-3 1-5 2-8 1-1 2-1 2-3s1-3 1-5c1-1 2-2 2-3l4-11z" class="O"></path><path d="M195 381l-2-2c0-2 0-3 1-4h1l1 2c0 1 1 3 1 4h-2z" class="b"></path><path d="M206 346h1l1 1c0 3-1 9 0 12v2h-2c-1-5 0-10 0-15z" class="j"></path><path d="M199 332c0 2-1 6 0 8l1-1c1-4 1-7 4-10v-1l1 2v1h-1c0 1-1 2 0 4h-1c-1 2-1 3-1 4s-1 2-1 3l-1 1v1c-1-1-1 0-1-1v-1-1l-2-1 1-1v-2l-1-1 2-4z" class="M"></path><path d="M210 383c0-1-1-2-2-3-2-4-3-6-3-10h1c0 1 0 2 1 3s1 4 3 5h1c1 3 2 5 4 8l1 1h-2 0c0-2-2-2-4-3v-1z" class="T"></path><path d="M195 375v-5c-1-4-1-10 0-14 1-2 2-5 4-6v1c0 1 1 2 0 3v1h-1c-2 5-1 12-2 17v5l-1-2z" class="F"></path><path d="M200 380v-1-7c1-3-1-6 2-9v1c1 4 0 7 1 11 0 0 0 1 1 2v1h1c1 3 2 4 4 5h1v1c-2 0-4-2-7-2h0c-1-1-2-1-3-1l-1 1c0-1 0-1 1-2z" class="Y"></path><defs><linearGradient id="S" x1="191.277" y1="376.677" x2="205.223" y2="368.823" xlink:href="#B"><stop offset="0" stop-color="#0b0c0d"></stop><stop offset="1" stop-color="#373637"></stop></linearGradient></defs><path fill="url(#S)" d="M196 377v-5c1-5 0-12 2-17h1l1 25c-1 1-1 1-1 2l1 1c0 2 1 6-1 9h0c-1-2-1-3-2-5-1-3-1-4 0-6 0-1-1-3-1-4z"></path><path d="M199 382l1-1c1 0 2 0 3 1h0c3 0 5 2 7 2 2 1 4 1 4 3h0 2l3 3c-2 0-4-1-7-2-2 0-4-1-5-1-2 1-2 1-3 2v2h1v2c2 0 3 1 5 1l-3 3-1-1-1 2-3-3-1-1-1-1c0 1 0 1-1 2h-1l1-3h0c2-3 1-7 1-9l-1-1z" class="F"></path><path d="M206 396c-1-1-1-3-2-4s-1-1-1-2l1-1v2h1v2c2 0 3 1 5 1l-3 3-1-1z" class="C"></path><path d="M199 382l1-1c1 0 2 0 3 1h0c3 0 5 2 7 2 2 1 4 1 4 3h0c-5-2-10-2-14-4l-1-1z" class="d"></path><path d="M206 310c1 1 2 1 3 1v1 5h2l-1 5v1 1l-1 4v3l-3 15c0-1-1-3 0-4v-3c0-1 0-1 1-2v-3h-2v-3-1l-1-2v1c-3 3-3 6-4 10l-1 1c-1-2 0-6 0-8s1-3 1-5c2-6 4-11 6-17z" class="b"></path><path d="M209 312v5h2l-1 5v1 1l-1 4-2 2h-1c1-2 1-2 1-3 0-2 1-2 1-3v-1l-1-2h1l1-1h0c0-1-1-1-1-2h-1c2-3 1-1 1-4l1-2z" class="L"></path><path d="M204 328c1-1 2-1 2-3h0c0-2 1-2 2-2v1c0 1-1 1-1 3 0 1 0 1-1 3h1l2-2v3l-3 15c0-1-1-3 0-4v-3c0-1 0-1 1-2v-3h-2v-3-1l-1-2z" class="T"></path><defs><linearGradient id="T" x1="707.784" y1="160.008" x2="723.105" y2="122.105" xlink:href="#B"><stop offset="0" stop-color="#cdcccc"></stop><stop offset="1" stop-color="#f6f6f6"></stop></linearGradient></defs><path fill="url(#T)" d="M659 138c10 2 18 9 28 11 2 1 5 0 7-1 6-4 10-13 12-21h0c1-5 1-10 1-15 2 2 4 10 4 13 2 13 9 29 20 37 3 2 6 3 9 4-2 2-3 1-5 1h0c3 2 5 4 9 5v3h-1c-5-1-9-3-15-4l-1 1h-2v1h0c-3-1-6-1-8-2-1 0-2 0-3-1 0 0-1-1-1-2-2 0-3-1-5-1h0l-14-10h0l-2-1-20-11c-4-3-10-3-13-7z"></path><defs><linearGradient id="U" x1="719.866" y1="169.143" x2="718.235" y2="148.247" xlink:href="#B"><stop offset="0" stop-color="#918f90"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#U)" d="M692 156v-1h2c2 2 5 2 7 1s3-2 4-3l2-3c2 1 5-1 7-1l1-1v-2h0v1c5 8 10 17 19 20h1 0c3 2 5 4 9 5v3h-1c-5-1-9-3-15-4l-1 1h-2v1h0c-3-1-6-1-8-2-1 0-2 0-3-1 0 0-1-1-1-2-2 0-3-1-5-1h0l-14-10h0l-2-1z"></path><path d="M713 168l15 3-1 1h-2v1h0c-3-1-6-1-8-2-1 0-2 0-3-1 0 0-1-1-1-2z" class="d"></path><path d="M694 157h2l3 2c3 0 8-1 12 1 1 1 4 2 6 2 1 2 1 2 2 3-1 1-4 0-5 1-2 0-4-1-6 1h0l-14-10z" class="n"></path><defs><linearGradient id="V" x1="612.392" y1="241.282" x2="578.991" y2="190.276" xlink:href="#B"><stop offset="0" stop-color="#0b0b0a"></stop><stop offset="1" stop-color="#2f2e30"></stop></linearGradient></defs><path fill="url(#V)" d="M605 200h2v1 3l1 2c0 1 0 1 1 2 1 0 2-1 3-1l1 1c1 0 2 0 3 1 1 0 2 0 3-1 1 0 2 1 3 0l3 1 3 1h2l2 1h5v3h-1 3c2 1 4 0 5 1h1 1c2 1 6 1 9 1-2 0-4 0-5 1h-1 1c1 2 1 2 2 3l-20-1v1c-5 1-11 0-16 0h-44-17-10-2c-2 1-2 1-4 3l-1-1 2-7 1-1h1 6c3 0 6 1 8 0l1-1-1-1c1-1 1-1 1-2h3l1 1h0 1v-1c-1 0-1-1-1-2h3 4c0-1 1-2 0-3h0 1v2c2 0 4 0 6 1 2 0 4-1 6-1 5-1 13-2 17 0h2 1v1c1 0 1 0 2-1 0-1 0-2-1-3v-3h2v-1h1z"></path><path d="M557 210h3l1 1h0 1v-1l2 2 1-1c2 0 7-1 9 0 0 0 0 1 1 1 0 0 0-1 1-1h1 14 6l1 2c3 0 3-1 5 1h-47l1-1-1-1c1-1 1-1 1-2z" class="j"></path><path d="M619 208c1 0 2 1 3 0l3 1 3 1h2l2 1h5v3h-1-12c-3 1-7 0-10 0h-11c-2-2-2-1-5-1l-1-2c2-1 3 0 5 1h2s0-1 1-1l1 1c1 0 1 0 2-1v-1h1v-1h7c1 0 2 0 3-1z" class="b"></path><path d="M632 211h5v3h-1-12c4-1 8 0 11-1-2-1-5-1-8-2h5z" class="M"></path><path d="M619 208c1 0 2 1 3 0l3 1 3 1h2l2 1h-5-12-3-4v-1h1v-1h7c1 0 2 0 3-1z" class="C"></path><path d="M597 211c2-1 3 0 5 1h2s0-1 1-1l1 1c1 0 1 0 2-1h4 3v1l-1 1h1l2-1h0l1 2h-4 0-11c-2-2-2-1-5-1l-1-2z" class="V"></path><path d="M605 200h2v1 3l1 2c0 1 0 1 1 2 1 0 2-1 3-1l1 1c1 0 2 0 3 1h-7v1h-1v1c-1 1-1 1-2 1l-1-1c-1 0-1 1-1 1h-2c-2-1-3-2-5-1h-6-14-1c-1 0-1 1-1 1-1 0-1-1-1-1-2-1-7 0-9 0l-1 1-2-2c-1 0-1-1-1-2h3 4c0-1 1-2 0-3h0 1v2c2 0 4 0 6 1 2 0 4-1 6-1 5-1 13-2 17 0h2 1v1c1 0 1 0 2-1 0-1 0-2-1-3v-3h2v-1h1z" class="J"></path><path d="M561 208h3 4c2 0 4 0 6 1h1v1c3-2 8-1 11-1 4 0 8-1 12 0 2 1 4 0 7 1l1-1v1h2v1c-1 1-1 1-2 1l-1-1c-1 0-1 1-1 1h-2c-2-1-3-2-5-1h-6-14-1c-1 0-1 1-1 1-1 0-1-1-1-1-2-1-7 0-9 0l-1 1-2-2c-1 0-1-1-1-2z" class="I"></path><path d="M610 217c1-1 34 0 39 0h1c1 2 1 2 2 3l-20-1v1c-5 1-11 0-16 0h-44-17-10v-1h-3v-1c3-2 59-1 68-1z" class="d"></path><path d="M618 220h-7c-5 0-9 0-13-1 6-1 13 0 20 0v1z" class="Z"></path><path d="M610 217c1-1 34 0 39 0h1c1 2 1 2 2 3l-20-1-14 1v-1c4-1 8 0 12-1l-20-1z" class="K"></path><path d="M793 137l2 2 6 6 5 3v1l1 2 12 10c2 3 4 5 8 7-2 0-3 1-4 3 0 3 1 7 0 9h-1v1 2l-5-2-11-5-15-5c-1 0-3 0-4-1h-2l-2-2c0-1 0-2-1-4 0-1-1-2-1-2-1-3-3-6-3-10 0-1 1-3 1-5 1-1 2-2 3-4 0-1 0-1 1-2l2-2s1-1 2-1h4 1l1-1z" class="M"></path><path d="M794 141c1 1 1 1 2 3-1 0-1 2-1 3h1-3v1 1c1 0 1 1 1 2l-1 1h-1c0-2 0-3-1-4v-2c-1 0-1-1-1-1 2-1 2-2 3-4h1z" class="b"></path><path d="M801 145l5 3v1l1 4c-1 2-2 4-4 5-1 0-2 0-3-1v-2l-1-1c1-2 1-4 2-6-1-1-1-2 0-3z" class="a"></path><path d="M801 145l5 3c-1 2-1 3-2 5h-2c-1-2-1-3-1-5-1-1-1-2 0-3z" class="e"></path><path d="M808 154v1c1 1 1 3 1 4l1 1c1-1 2-1 4-1 1 1 2 2 2 4v3h0c-1 0-1 0-2 1l2 1c-1 2-1 3-2 4h-1v-1h-1l-3 1c0-1-1-1-1-2-1-1-1-2 0-4v-1c-1 0-1-1-2-1l-1-1v-1l2-1v-1h-1l-1-1h3l-1-1c1-2 1-3 1-4z" class="S"></path><path d="M812 171v-1c-1 0-2-1-2-1l-1-1h1c1 0 2-1 2-1 1 1 1 1 1 3v1h-1z" class="T"></path><path d="M807 151l12 10c2 3 4 5 8 7-2 0-3 1-4 3 0 3 1 7 0 9h-1v1 2l-5-2s1-1 1-2c0-2-1-4-2-5s-2-1-3-2h1c1-1 1-2 2-4l-2-1c1-1 1-1 2-1h0v-3c0-2-1-3-2-4-2 0-3 0-4 1l-1-1c0-1 0-3-1-4v-1l-1-3z" class="c"></path><path d="M819 161c2 3 4 5 8 7-2 0-3 1-4 3 0 3 1 7 0 9h-1l-3-6v-3c0-1-1-1-1-1v-3l1-6z" class="E"></path><defs><linearGradient id="W" x1="779.111" y1="155.03" x2="789.875" y2="154.559" xlink:href="#B"><stop offset="0" stop-color="#3a3a3d"></stop><stop offset="1" stop-color="#68676a"></stop></linearGradient></defs><path fill="url(#W)" d="M793 137l2 2-3 1-1 1h1 2-1c-1 2-1 3-3 4 0 0 0 1 1 1l-1 3h0c-1 1-1 2-1 3v1l2 1v1h-1-1v1l1 1c0 1-1 2-1 3 0 2 0 3-1 4 1 2 1 2 2 3h0c-1 0-1 0-1 1 0 0-1 1 0 2l3 1h-1c-1 0-3 0-4-1h-2l-2-2c0-1 0-2-1-4 0-1-1-2-1-2-1-3-3-6-3-10 0-1 1-3 1-5 1-1 2-2 3-4 0-1 0-1 1-2l2-2s1-1 2-1h4 1l1-1z"></path><path d="M783 159v-1-1h1v1l1 1v2h-1l-1-2z" class="R"></path><path d="M782 164v-4l1-1 1 2v1l1 2c0 1 1 3 1 4l-1 2-2-2c0-1 0-2-1-4z" class="F"></path><path d="M793 137l2 2-3 1-1 1c-2 1-3 1-4 3v3c-1 1-1 2-1 3v1h0c-1 1-2 0-2 0 0-3-1-4 1-6v-4-1l2-2h4 1l1-1z" class="c"></path><path d="M884 249l17 6 10 4c2 0 4 2 6 3l-6 1-6 2v-1h1v-1h-1c-3 1-6-1-8-1-8-1-16 0-24-1-4 0-8-2-12-2l-1-1c-1 0-2 0-3 1 0 2-1 3-1 5s0 4 1 6v2c0 1 1 3 1 4l1 1c2 1 3 8 4 10 3 7 7 14 11 21 2 3 7 7 10 9 1 2 2 3 4 4 4 4 8 7 10 13v1l3 6c2 6 4 11 6 18 0 3 0 6-1 9-1-2-1-7-1-10-1-5-2-10-6-15v-1c-3-4-6-7-9-10-2 0-3-1-4-2l-3-4c0-1-2-3-3-4-4-4-8-11-14-14l-2-1c-1-1-1-1-2-1h0c-2-1-3-1-4-1h-1c-1 0 0 0-1-1-2 0-8 0-10 1-1 0-3 1-4 1 0-1 1-2 1-2l1-1h-1l1-1 1 1h2l-1-1h-1c1-1 2-1 4-1v1c2-1 3-1 4-1-1 0-2 0-4-1v-1h-2c0-1 0-1 1-2l2 1h0 1 1l4 2c-1-2-1-5 0-7h0c0-5-2-10-4-15 1 0-2-6-2-7-3-5-4-10-6-16h0l1-1 2 4 2 2c1 0 2-1 2-1v-1h1l2-2c1-1 1-1 3-2 7 2 13 5 21 5h4l-3-3h0c0-3 0-3 2-5 1-1 1-1 2-1h1v-1z" class="N"></path><defs><linearGradient id="X" x1="903.924" y1="254.514" x2="894.547" y2="260.098" xlink:href="#B"><stop offset="0" stop-color="#47484a"></stop><stop offset="1" stop-color="#5f5d62"></stop></linearGradient></defs><path fill="url(#X)" d="M893 255c1 0 3 0 4 1h2l2-1 10 4h-18v-1-1-2z"></path><path d="M884 249l17 6-2 1h-2c-1-1-3-1-4-1v2 1 1h-6-5l-3-3h0c0-3 0-3 2-5 1-1 1-1 2-1h1v-1z" class="R"></path><path d="M882 255h0-1c0-2 0-3 1-4 2 0 4 0 5 1v1c-1 1-3 2-5 2h0z" class="c"></path><path d="M882 255h0c2 0 4-1 5-2l1 2h1l1-1c1 1 2 1 3 1v2 1 1h-6 0l-3-1-2-3z" class="O"></path><path d="M844 255l1-1 2 4 2 2 9 24c5 9 9 18 16 26l18 18c2 2 4 5 6 7l3 6c2 6 4 11 6 18 0 3 0 6-1 9-1-2-1-7-1-10-1-5-2-10-6-15v-1c-3-4-6-7-9-10-2 0-3-1-4-2l-3-4c0-1-2-3-3-4-4-4-8-11-14-14l-2-1c-1-1-1-1-2-1h0c-2-1-3-1-4-1h-1c-1 0 0 0-1-1-2 0-8 0-10 1-1 0-3 1-4 1 0-1 1-2 1-2l1-1h-1l1-1 1 1h2l-1-1h-1c1-1 2-1 4-1v1c2-1 3-1 4-1-1 0-2 0-4-1v-1h-2c0-1 0-1 1-2l2 1h0 1 1l4 2c-1-2-1-5 0-7h0c0-5-2-10-4-15 1 0-2-6-2-7-3-5-4-10-6-16h0z" class="F"></path><path d="M883 326c3 2 5 3 7 6-2 0-3-1-4-2l-3-4z" class="K"></path><path d="M861 294c1 2 2 3 3 5 0 2 2 3 2 5h-2c-3-1-5-2-8-1h-1l1-1v-2h0c0 1 0 1 1 1 2 0 2 0 4-2v-5z" class="L"></path><path d="M844 255l1-1 2 4 2 2 9 24c0 3 0 4 1 6h0l2 4v5c-2 2-2 2-4 2-1 0-1 0-1-1v-7h0c0-5-2-10-4-15 1 0-2-6-2-7-3-5-4-10-6-16h0z" class="W"></path><path d="M859 290l2 4v5c-2 2-2 2-4 2-1 0-1 0-1-1v-7c1 1 1 3 1 4l2 1v-1-2-5z" class="X"></path><defs><linearGradient id="Y" x1="249.839" y1="94.052" x2="253.294" y2="138.134" xlink:href="#B"><stop offset="0" stop-color="#1f201f"></stop><stop offset="1" stop-color="#4c4c50"></stop></linearGradient></defs><path fill="url(#Y)" d="M242 137c1-2 3-3 4-5 2-4 2-9 2-13 0-7 0-14 1-21 0-3 0-4 3-6l4 26c1 1 1 6 2 7l1 2c2 8 5 17 3 26 0 2 0 3 1 4s1 0 2 0l1 2h-1c-3 1-4 5-6 8-2 1-2 2-3 4-2-2-5-1-7-1l-8 1 2-2v-2c0-2 0-3-1-4h-2l-2-1v-2-1h-1c0-2-1-2-2-2h-1c-2-1-3-2-3-4l-2-2-1-1v-1-1c2-2 4-3 6-4l2-2 1-1 1-1 2-2 2-1z"></path><path d="M248 161h0v-1c1 0 1 0 2-1h0c1-1 1-1 1-2l1-1h0 1v1c0 2-1 3-2 5-1 1-1 2-2 3v1c0 1 1 2 2 3l-2 1c-1-1-1-1-2-1v-3h-1c1-2 2-3 2-4v-1z" class="b"></path><path d="M259 139c1 2 1 8 1 10 0 3-1 7 0 10h0c-2 3-4 6-6 8l-2-1c0-2 3-5 4-7s2-4 2-6c1-5 0-8 1-13v-1z" class="G"></path><path d="M262 153c0 2 0 3 1 4s1 0 2 0l1 2h-1c-3 1-4 5-6 8-2 1-2 2-3 4-2-2-5-1-7-1h0l2-1 3-2c2-2 4-5 6-8 1-2 1-4 2-6z" class="d"></path><path d="M259 139l-2-7c0-3 0-3 2-5 2 8 5 17 3 26-1 2-1 4-2 6h0c-1-3 0-7 0-10 0-2 0-8-1-10z" class="E"></path><path d="M242 137c1 0 4 1 5 0h1 2l2-1c1 0 1 0 2 1 1 2 2 3 2 5 0 1 0 2 1 3 0 1-1 1-1 2s2 4-1 5v-3c0-2-1-2-3-3v-1h-1 0c1-1 1-2 2-4h-2c-4-1-7-2-11-3l2-1z" class="O"></path><defs><linearGradient id="Z" x1="250.748" y1="140.249" x2="245.75" y2="145.251" xlink:href="#B"><stop offset="0" stop-color="#67656b"></stop><stop offset="1" stop-color="#7c7b7d"></stop></linearGradient></defs><path fill="url(#Z)" d="M240 138c4 1 7 2 11 3h2c-1 2-1 3-2 4h0 1v1c-1 1 0 1-1 2v-1h-1-1l-4 1h-1l1 1h-1v-1l-1-1c0-2 0-2-1-3-1-2-3-3-5-3l1-1 2-2z"></path><path d="M238 140c3 1 5 2 7 4v4h-1l1 1h-1v-1l-1-1c0-2 0-2-1-3-1-2-3-3-5-3l1-1z" class="a"></path><defs><linearGradient id="a" x1="254.485" y1="149.518" x2="243.218" y2="153.118" xlink:href="#B"><stop offset="0" stop-color="#6f6e72"></stop><stop offset="1" stop-color="#929092"></stop></linearGradient></defs><path fill="url(#a)" d="M252 146c2 1 3 1 3 3v3c-1 2 0 2-2 4h-1 0l-1 1c0 1 0 1-1 2h0c-1 1-1 1-2 1v1h0-1c0-1 0-1 1-2 1 0 1 0 2-1v-1c-1 0-2-1-3-1-2 0-2-1-3-2h0l-1-1v1-1-1c-1 0-1-1-1-1 0-1 1-2 2-3v1h1l-1-1h1l4-1h1 1v1c1-1 0-1 1-2z"></path><path d="M237 141c2 0 4 1 5 3 1 1 1 1 1 3l1 1c-1 1-2 2-2 3 0 0 0 1 1 1v1 1-1l1 1h0c1 1 1 2 3 2 1 0 2 1 3 1v1c-1 1-1 1-2 1-1 1-1 1-1 2h1v1c0 1-1 2-2 4h1v3c1 0 1 0 2 1h0l-8 1 2-2v-2c0-2 0-3-1-4h-2l-2-1v-2-1h-1c0-2-1-2-2-2h-1c-2-1-3-2-3-4l-2-2-1-1v-1-1c2-2 4-3 6-4l2-2 1-1z" class="W"></path><path d="M231 153c1 1 1 1 3 2h1c1 1 1 1 2 1l-2 1h-1c-2-1-3-2-3-4z" class="a"></path><path d="M243 153h-2-1c-1-1-1-2-1-3s0-1-1-2l1-1v-2c1-1 1-1 2-1l2 3 1 1c-1 1-2 2-2 3 0 0 0 1 1 1v1z" class="n"></path><path d="M236 142c1 3 0 4-1 6s-1 3-3 5l-1-1s-1-1-2-1l-1-1v-1-1c2-2 4-3 6-4l2-2z" class="Q"></path><path d="M234 144l1 1v1h0l-3 3c-2 1-2 0-4-1 2-2 4-3 6-4z" class="e"></path><path d="M508 136l3 2h0l2 2v1c0 1 0 1-1 2v6c1 1 1 1 2 1l-2 1v2l2 2h-1-1c0 1 1 2 0 4v2c-1 1-2 2-2 3l1 1h0-2c-1 1-1 5-1 6v5l-1 6c-1 4-1 8-2 11 0 0 0-1-1-2v-4l-1-1c-1 1-1 2-2 3v1 1h-1c-1 1-1 1-1 2-1 1-1 2-1 3l-1 1h-1-3 0v1h0c0 1 0 1-1 2-2 2 0 3-1 6l-2 2v-1-3h0l-3-2c1-1 1-2 2-2h2c1-1 1-1 1-2l-2-1c-1 1-1 1-3 1-1 0-3 1-3 1-1 0-1-1-2-1 0 0 0 1-1 1h-2-2-7l-13 1h0c0-2 1-3 2-4l1-1v-2l1 1v1c3 1 9 1 12 0h0v-1c1-1 2-1 3-2l-2 1h0l-1-1 1-1h0c0-1 0-1-1-1 1-1 1-1 2 0 2 0 3 1 5 0h2c2 0 2 0 3 1 1 0 1-1 2-1-1-1-2-1-3-2h-3c-1 0-2 0-3-1 1-2 3-4 4-6l-3-1 1-2 2-5c1 1 2 1 4 1v-1l3-3 6-9 2-3c1 0 2-1 3-1l3-3c1-1 1-2 1-3s1-1 1-1h1 1l-1-1c-1-1-1-2-1-4l2-2v-1-2c1-2 2-3 2-4z" class="R"></path><path d="M485 180c1-1 1 0 2-1l-1-1c1-1 1-2 2-2v-1c1-1 0-1 1-1h1c0-2 1-2 1-3 1 0 2 0 3 1s2 2 4 2h1l1 1h-1v3s1 1 2 1c-1 2-2 3-3 4-1 0-2 0-4-1l2-1-1-1-1-1c-2-1-2-1-4-1v1c-1-1-1-1-2-1v1l1 2h-1c-1-1-1-1-3-1z" class="H"></path><path d="M494 192v1h-1l-1-1c-1 0-1 0-2-1-1 0-1 0-2-1s-1 0-1-1h1l1-1h1l1-1-3-3h1 1l2 2h1c1-1 1-1 1-2l-2-3c-1 1-1 1-2 1 0-1 1-1 1-2h1c0 1 1 1 1 2h1c2 1 3 1 4 1l-1 2 2 1 2 3v1 1h-1c-1 1-1 1-1 2l-1-1h-4z" class="J"></path><path d="M497 185l2 1 2 3v1 1h-1c-1 1-1 1-1 2l-1-1h-4l-1-1 1-1c1-2 2-3 3-5z" class="T"></path><path d="M499 186l2 3v1 1h-1c-1 1-1 1-1 2l-1-1v-4c1-1 1-1 1-2h0z" class="F"></path><path d="M472 195h4l1 1h0l1-1h1 0c-1 1 0 1 0 2l1 1h1c-1-2-1-2-1-3 2 1 1 2 2 2l2-1 1 1 1-1v-1h-1-1c0-1 0-1-1-1l1-1h0l3 2-1 3c-1 0-3 1-3 1-1 0-1-1-2-1 0 0 0 1-1 1h-2-2-7l-13 1h0c0-2 1-3 2-4l1-1v-2l1 1v1c3 1 9 1 12 0h0z" class="i"></path><path d="M506 172l2-1v5l-1 6c-1 4-1 8-2 11 0 0 0-1-1-2v-4l-1-1c-1 1-1 2-2 3l-2-3-2-1 1-2c1-1 2-2 3-4-1 0-2-1-2-1v-3h1c0-1 1-1 2-2h0c1 1 1 1 1 2l1 1v-2c1 0 1-1 2-2z" class="C"></path><path d="M502 173c0 3 0 4-1 6-1 0-2-1-2-1v-3h1c0-1 1-1 2-2z" class="E"></path><path d="M506 172l2-1v5l-1 6c-1 4-1 8-2 11 0 0 0-1-1-2v-4l-1-1c0-1 1-2 1-3 0-2 1-2 1-3 0-2 0-2 1-3v-2-2-1z" class="Y"></path><path d="M494 161c1 1 2 1 3 2l-3 3c1 1 1 1 3 1h1 2v3h-1v2h-5c-1-1-2-1-3-1 0 1-1 1-1 3h-1c-1 0 0 0-1 1v1c-1 0-1 1-2 2l1 1c-1 1-1 0-2 1v1h1l1 1h-2c-1 1-2 2-3 4h1v2h0-3c-1 0-2 0-3-1 1-2 3-4 4-6l-3-1 1-2 2-5c1 1 2 1 4 1v-1l3-3 6-9z" class="D"></path><path d="M479 178l2-5c1 1 2 1 4 1l-3 5-1 2-3-1 1-2z" class="k"></path><path d="M479 178l3 1-1 2-3-1 1-2z" class="Q"></path><path d="M508 136l3 2h0l2 2v1c0 1 0 1-1 2v6c1 1 1 1 2 1l-2 1v2l2 2h-1-1c0 1 1 2 0 4v2c-1 1-2 2-2 3l1 1h0-2c-1 1-1 5-1 6l-2 1c-1 1-1 2-2 2v2l-1-1c0-1 0-1-1-2h0c-1 1-2 1-2 2l-1-1h-1c-2 0-3-1-4-2h5v-2h1v-3h-2-1c-2 0-2 0-3-1l3-3c-1-1-2-1-3-2l2-3c1 0 2-1 3-1l3-3c1-1 1-2 1-3s1-1 1-1h1 1l-1-1c-1-1-1-2-1-4l2-2v-1-2c1-2 2-3 2-4z" class="L"></path><path d="M496 158c2 1 3 1 5 1-2 1-3 2-4 4-1-1-2-1-3-2l2-3z" class="H"></path><path d="M502 154c0 1 0 2 1 3v2h-1 0-1c-2 0-3 0-5-1 1 0 2-1 3-1l3-3z" class="P"></path><path d="M507 162l3 2 1 1h0-2c-1 1-1 5-1 6l-2 1c-1 1-1 2-2 2v2l-1-1c0-1 0-1-1-2 0-1 0 0-1-1h0v-1h2c1-2 3-4 4-5l-1-2 1-2z" class="G"></path><path d="M508 148l1 2c-1 0-1 0-1 2h1c0 1 0 1 1 2v-1h1 1l2 2h-1-1c0 1 1 2 0 4v2c-1 1-2 2-2 3l-3-2h1v-2c-1-4-1-8 0-12z" class="o"></path><path d="M508 136l3 2h0l2 2v1c0 1 0 1-1 2v6c1 1 1 1 2 1l-2 1v2h-1-1v1c-1-1-1-1-1-2h-1c0-2 0-2 1-2l-1-2c0-2-1-3-1-5h-1v-1-2c1-2 2-3 2-4z" class="i"></path><path d="M508 136l3 2h0l2 2c-2 2-3 3-6 3h-1v-1-2c1-2 2-3 2-4z" class="h"></path><path d="M727 476l1 2h0c0-1 0-2 1-3l1-1s1 1 1 2h0l-1-2-1 1c0 3 2 6 5 9 2 0 4 1 6 0 1 1 1 3 1 5-1 5 1 11-1 16l-1 7v1c0 2 0 2-1 4v3c0 3-1 6-1 10-1 2-1 3-2 5l-2 14c-1 2-1 6-2 8h0v4h0v1c1 1 1 2 1 4l-3-3c-1-1-2 0-4 0h-1c1-1 1-3 1-4 1-3 1-4 0-7v3l-2 2v-3c0-2-1-1 0-2h1l-1-1-1-1-1-1h0l-1-5-1 3-1 1v-6l1-9v-1l1-1c1-2 0-4 0-6 1-2 1-8 1-10 1-2 0-4 0-5 1-4 1-7 1-10l2-2v-1h-1v-2c1-2 1-5 1-8v-7l1-1 1 1 1-1v-3h0z" class="C"></path><path d="M734 484c2 0 4 1 6 0 1 1 1 3 1 5-1 5 1 11-1 16-1-5 0-11-1-15l-1-1c0-1 0-1-1-2s-1-1-2-1l-1-2z" class="P"></path><path d="M726 480l1-1v1c1 1 3 4 3 6-1 1-1 0 0 1v6c-1 2 1 4-1 7h1c1 5-1 11-1 16v8c0 2-1 6 0 8-1 0-1 0-1 1-1 1 0 4 0 5 0 2-1 2-1 3v4 3l1-1v-1h0c0 2-1 3-2 5l-1 1v3l-2 2v-3c0-2-1-1 0-2h1l-1-1-1-1-1-1h0l-1-5-1 3-1 1v-6l1-9v-1l1-1c1-2 0-4 0-6 1-2 1-8 1-10 1-2 0-4 0-5 1-4 1-7 1-10l2-2v-1h-1v-2c1-2 1-5 1-8v-7l1-1 1 1z" class="F"></path><path d="M726 531v20l-1 1v3l-2 2v-3c0-2-1-1 0-2h1l-1-1c1-3 1-6 1-8l2-12z" class="c"></path><path d="M726 480l1-1v1c1 8 1 16 1 24-1-2 0-2-2-4v4h-1c2-8 1-17 1-24z" class="a"></path><path d="M724 487v-7l1-1 1 1c0 7 1 16-1 24l-1 1c0 5 1 9-1 14-1-7 1-15 1-22h-1v-2c1-2 1-5 1-8z" class="G"></path><path d="M726 504v-4c2 2 1 2 2 4l-1 15c0 4-1 8-1 12l-2 12h0c-1-1-1-2-1-4 2-11 2-23 3-35z" class="g"></path><path d="M723 519c2-5 1-9 1-14l1-1h1c-1 12-1 24-3 35 0 2 0 3 1 4h0c0 2 0 5-1 8l-1-1-1-1h0l-1-5c1-8 3-16 3-25z" class="m"></path><path d="M721 549l2-10c0 2 0 3 1 4h0c0 2 0 5-1 8l-1-1-1-1z" class="a"></path><path d="M724 497c0 7-2 15-1 22 0 9-2 17-3 25l-1 3-1 1v-6l1-9v-1l1-1c1-2 0-4 0-6 1-2 1-8 1-10 1-2 0-4 0-5 1-4 1-7 1-10l2-2v-1z" class="P"></path><defs><linearGradient id="b" x1="151.818" y1="296.856" x2="119.647" y2="279.905" xlink:href="#B"><stop offset="0" stop-color="#272728"></stop><stop offset="1" stop-color="#4c4b4f"></stop></linearGradient></defs><path fill="url(#b)" d="M132 285c1-7-4-13-4-19 1-1 7-2 9-2 1 1 2 1 3 1 2-1 3-1 5 0l1-1c1-1 2 0 3 0l2-1h1 2 2c-1-1-1-1-3-1h-1 0v-1h5 0c0 1 0 1 1 2 1 0 2 0 3-1h1v2h1l1-1c1 1 0 2 0 4h0v2c-3 15-10 28-18 41h0c-1 1-3 2-3 3l-4 4-1 1-3 2-1 1h-1c-2 1-3 4-5 5l-3 3 4-12c3-11 4-21 3-32z"></path><path d="M129 317c0 3-1 5-2 7v1c1-1 1 0 1-1l1-1 1-1h1c2-2 8-5 9-8l1-1c1-1 1-2 2-3s1-2 2-2c0-1 0-2 1-3h0v2h1c0-1 0-1 1-2 1-3 2-5 3-8 0-1 0-1 1-2v-1h0-1c2-2 3-4 3-6 2-2 3-5 3-7 1-2 2-3 2-5v-1c0-2 0-5 1-7v-1h1v-1-1l2 1c0 1 0 2-1 3h0-1-1v2l2 2h0c0-2 1-3 2-4-3 15-10 28-18 41h0c-1 1-3 2-3 3l-4 4-1 1-3 2-1 1h-1c-2 1-3 4-5 5l-3 3 4-12z" class="J"></path><path d="M191 191l2 3 2 5v1l2 3c2 0 3 0 4 1l1 1h1c3 3 6 5 10 7l-1 1v1c2 0 2 0 4 1l-1 2c-6 1-11 5-16 9h0c1 1 1 2 1 3l-1-1c-2 1-3 2-4 3-1 3-2 4-3 5-3 2-4 3-6 5l-3 4h-1c-1 0-2 0-3 1 0 2-1 2-2 4l1-5c0-4 0-10-2-14-1-2-4-6-6-6-2-1-3-2-4-4h0v-2l1-1-1-4v-4h1l1 2v1s2-1 2 0c0 0 1 1 1 2 1 1 1 0 1 1 1-1 2-2 3-2h4l1-1-4-6c-2-3-4-5-6-8v-2l1-1-1-2c1-2 1-2 3-2 3 0 6-1 9-1h9z" class="X"></path><path d="M196 221l1 1h-2c-1 1-1 1-1 2 0 0 1 0 1-1h1 1l-2 2c-1-1-2-1-3-1v2h-1l-1-1c0-1 1-1 2-2 1 0 2-1 3-1l1-1zm-11 13c1 1 1 2 1 3l1 1c2-2 4-5 7-6l1-1c-1 3-2 4-3 5-3 2-4 3-6 5-1-1-1-1-2-1 1-2 1-3 1-5v-1z" class="S"></path><path d="M179 235l2 2c1 0 2-2 3-2 0-1 1-1 1-1v1c0 2 0 3-1 5 1 0 1 0 2 1l-3 4h-1c-1 0-2 0-3 1 0 2-1 2-2 4l1-5v-1l1-2v-4-3z" class="T"></path><path d="M179 238l1 1 2-1 1 1 1 1h-1l-3 6h-1c0 2-1 2-2 4l1-5v-1l1-2v-4z" class="R"></path><path d="M166 210h1l1 2v1s2-1 2 0c0 0 1 1 1 2 1 1 1 0 1 1h0l-5 4c2 2 2 3 4 5 5 2 7 5 8 10v3 4l-1 2v1c0-4 0-10-2-14-1-2-4-6-6-6-2-1-3-2-4-4h0v-2l1-1-1-4v-4z" class="K"></path><path d="M170 199v-2l1-1 3 3c4 6 9 15 15 19 1 1 2 2 3 2 4 1 10-1 13-2h1c-3 2-7 3-10 3h0l-1 1c-3 0-4 0-6 1 0 2 1 4-1 6-2 1-4 4-6 6h-2v-2c-1-3-2-5-3-7-1 0-2-1-2-2h-2l-2-2c-1 0-1 0-2-1 1-1 1-2 3-3h1c1-1 3-1 4-1l1 2h1c1-1 2-1 2-3 0-1 0-2-1-3l-4-6c-2-3-4-5-6-8z" class="g"></path><path d="M188 229l-3-8h1 10 0l-1 1c-3 0-4 0-6 1 0 2 1 4-1 6z" class="b"></path><path d="M178 221l1 2c2-1 3-2 3-3h1l1 1c0 1-1 1-1 3l3 4v1h0c-2 1-3 3-4 4h-2c-1-3-2-5-3-7l2 1 1 3v1h1c2-1 3-1 4-3l-1-1c-1-1-1-2-2-3 0-1 0-1-1-1l-1 1h-1l-1-3z" class="Q"></path><path d="M177 226c-1 0-2-1-2-2h-2l-2-2c-1 0-1 0-2-1 1-1 1-2 3-3h1c1-1 3-1 4-1l1 2v2l1 3h1l1-1c1 0 1 0 1 1 1 1 1 2 2 3l1 1c-1 2-2 2-4 3h-1v-1l-1-3-2-1z" class="Z"></path><path d="M179 227c0-1 0-1 2-2 1 1 1 1 2 3v1l-2 1h-1l-1-3z" class="d"></path><path d="M191 191l2 3 2 5v1l2 3c2 0 3 0 4 1l1 1h1c3 3 6 5 10 7l-1 1v1l-3 2c-2 1-3 1-4 2-3 1-9 3-13 2-1 0-2-1-3-2-6-4-11-13-15-19l-3-3-1-2c1-2 1-2 3-2 3 0 6-1 9-1h9z" class="R"></path><path d="M182 199h1l2 2h1c1 0 2 1 2 1l1 1c2 2 2 2 3 4 1 1 2 3 4 4l1 1-2 1 1 1h1 0c-2 1-3 1-5 1-2-1-2-2-3-3l-1-1c-1 0-2 0-3-1 0-1 0-2-1-2 0-1-1-1-1-1v-1c-1-1-2-3-2-4 0 0 1 0 1-1v-2z" class="T"></path><path d="M191 191l2 3 2 5v1l2 3c2 0 3 0 4 1l1 1h1c3 3 6 5 10 7l-1 1v1l-3 2c-10-2-15-13-23-17l-1-1-2-1c-1-1-1 1-2 1s-2-1-3 0l-1 1h-3l-3-3-1-2c1-2 1-2 3-2 3 0 6-1 9-1h9z" class="D"></path><path d="M197 203c2 0 3 0 4 1l1 1h1c3 3 6 5 10 7l-1 1c-6-2-12-5-15-10z" class="Z"></path><path d="M191 191l2 3 2 5v1l1 3v1c0-1-1-1-1-2-3-1-4-3-7-4-1-1-3-2-4-3h-2c0-1-1-1-1-1-2 0-3 0-4 1s-1 1-2 1c-2-1-2-1-3-2l1-2c3 0 6-1 9-1h9z" class="N"></path><path d="M295 491c1 2 1 4 1 6 0 5 0 10 1 14v4c0 1 1 1 1 2 1 2-1 6 1 7v1c-1 1 0 1 0 2 0 2-1 5 0 6 0 1 0 3 1 4h0c0 3 1 6 1 9 0 1 0 3 1 4 0 1-1 3 0 5v1l1 1c0 1-1 3 0 4 0 1 0 2 1 3v1 2 1c1 1 1 1 1 3h0l1 1v-2 2l1 1c0-2-1-3-1-4v-3h-1v-2c0-2 0-2-1-2v-2c-1-1-1-2-1-3v-1-1h-1c0-2 1-7 0-8v-1-2-3l5 28c0 3 2 5 2 7l6 20c1 3 3 5 3 8 2 6 6 12 9 18 2 3 3 6 5 9l1-1c1 1 1 2 2 4l1 1c1 1 2 4 4 4h0c4 5 6 10 9 15-1 0-1-1-1-1h-1l3 5c-1 1-1 2-1 3l-1-1v1l-2-1-3-2-5 1-1 1c-1 0-1 1-2 1 0 1-1 1 0 2h0c-1-1-1-2-2-3h0l-2-4c-1-2-2-4-4-5l-13-22v-1c0-1-1-2-1-4h1l1 1v-1h2c2 1 1 1 2 0h2l1 1c0-1 0-2-1-3v-1l1-2v-1h0c0-2-1-4-3-6h0v-2c0-1-1-2-2-3h-1l-2-2h-3c-1 0-2 1-3 2s-1 1-2 1c0-1-1-2-1-3-1-4-3-8-5-12-1-3-2-5-3-8v-5h1c1-2 2-3 2-5l1-2c0-2-1-4-1-5l-2-3v-3l1-1c0-2-1-8-1-10v-6c0-5-1-10-2-15v-6c0-6-1-12-1-18 0-1 0-3-1-4 0-1 0-1 1-2-1-2-1-6-1-8l1-1z" class="J"></path><path d="M332 631l1-1c1 1 1 2 2 4l1 1c1 1 2 4 4 4h0c4 5 6 10 9 15-1 0-1-1-1-1h-1c-1 0-2-2-2-3-5-5-10-13-13-19z" class="T"></path><defs><linearGradient id="c" x1="336.257" y1="630.901" x2="326.906" y2="644.365" xlink:href="#B"><stop offset="0" stop-color="#504f52"></stop><stop offset="1" stop-color="#81807f"></stop></linearGradient></defs><path fill="url(#c)" d="M322 619l24 41-3-2-5 1-1 1c-1 0-1 1-2 1 0 1-1 1 0 2h0c-1-1-1-2-2-3h0l-2-4c-1-2-2-4-4-5l-13-22v-1c0-1-1-2-1-4h1l1 1v-1h2c2 1 1 1 2 0h2l1 1c0-1 0-2-1-3v-1l1-2z"></path><path d="M325 631l8 15c0-1-1-1-1-2h-1c-1-1-1-1-2-1-1-1-2-3-3-4s-1-3-1-4v-4z" class="U"></path><path d="M329 643c1 0 1 0 2 1h1c0 1 1 1 1 2 4 3 7 8 10 12l-5 1-1 1c-1-1-1-3-2-4-2-3-4-5-5-8l1-1c-1-1-2-3-2-4z" class="K"></path><path d="M317 624c2 1 1 1 2 0h2l1 1 3 6v4c0 1 0 3 1 4s2 3 3 4c0 1 1 3 2 4l-1 1c1 3 3 5 5 8 1 1 1 3 2 4-1 0-1 1-2 1 0 1-1 1 0 2h0c-1-1-1-2-2-3h0l-2-4c-1-2-2-4-4-5l-13-22v-1c0-1-1-2-1-4h1l1 1v-1h2z" class="H"></path><path d="M322 636l-1-4h1l3 3c0 1 0 3 1 4s2 3 3 4c0 1 1 3 2 4l-1 1-8-12z" class="R"></path><path d="M317 624c2 1 1 1 2 0h2l1 1 3 6v4l-3-3h-1l1 4c-1-1-1-3-2-4 0-3-2-5-3-8z" class="K"></path><path d="M295 491c1 2 1 4 1 6 0 5 0 10 1 14-1 4-1 7 0 10l4 36c0 2 1 3 1 5 3 12 6 23 10 35 2 3 3 7 4 10l-2-2h-3c-1 0-2 1-3 2s-1 1-2 1c0-1-1-2-1-3-1-4-3-8-5-12-1-3-2-5-3-8v-5h1c1-2 2-3 2-5l1-2c0-2-1-4-1-5l-2-3v-3l1-1c0-2-1-8-1-10v-6c0-5-1-10-2-15v-6c0-6-1-12-1-18 0-1 0-3-1-4 0-1 0-1 1-2-1-2-1-6-1-8l1-1z" class="L"></path><path d="M306 594l2 1v1s1 1 1 2h0c-1 1-1 0-1 1l-2-2v-3z" class="K"></path><path d="M301 573h0l2 1v1c1 3 3 6 4 9v3c0 1 1 1 1 2l-1 2 1 1-1 1-1 1h0v3l2 2h0c1 1 2 1 3 3h0v3c-1 0-2 1-3 2s-1 1-2 1c0-1-1-2-1-3-1-4-3-8-5-12-1-3-2-5-3-8v-5h1c1-2 2-3 2-5l1-2z" class="C"></path><path d="M301 578h1v1c0 2-1 3-2 4h-1l-1-1 3-4z" class="H"></path><path d="M301 573l2 1v1c1 3 3 6 4 9v3c0 1 1 1 1 2l-1 2 1 1-1 1c0-1 0 0-1-1h-1c1-1 1-2 2-3h-1c-3-3-3-8-3-11h0l-2-2v-3z" class="F"></path><path d="M300 593v-1-3-3h1c0 1 1 2 1 3s1 3 1 4h2v3c1 2 2 3 2 4l1-1c1 1 2 1 3 3h0v3c-1 0-2 1-3 2s-1 1-2 1c0-1-1-2-1-3-1-4-3-8-5-12z" class="K"></path><path d="M305 596c1 2 2 3 2 4v2h0l-2-1c0-2-1-3 0-5z" class="F"></path><defs><linearGradient id="d" x1="236.927" y1="395.453" x2="224.914" y2="443.737" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#4e4e53"></stop></linearGradient></defs><path fill="url(#d)" d="M204 389c1-1 1-1 3-2 1 0 3 1 5 1 3 1 5 2 7 2 8 6 16 15 25 19h1c6 5 12 8 15 15l1 3v1c1 3 2 7 2 10v8c-2-1-3-3-5-3l-2 1c4 2 7 5 10 8 1 1 0 0 2 1 1 1 2 3 3 5-1 0-2-1-2-1l-11-10c-5-3-9-5-14-4-2 1-3 3-3 5-2-1-4-3-6-2-4 1-7 5-9 9 0-4 0-4 2-7l2-2h0c1-1 2-2 3-2s1 0 2-1c1-2 1-4 0-6h-3c-2-2-1-2-1-4-1-2-3-3-3-5h0c-1-1-1-2-1-3v-1c0-1-1-2-1-4 0-1 0 0-1-1v-6c-1-1-3-1-3-3l1-1-2-6 3 3h1l-1-1-3-2-2-2-2-2-3-3-1-1-3-1c-2 0-3-1-5-1v-2h-1v-2z"></path><path d="M205 391l3 1h3 0c3 3 6 4 8 7h1l4 4v2l-3-2-2-2-2-2-3-3-1-1-3-1c-2 0-3-1-5-1v-2z" class="H"></path><path d="M261 427v1c0 3 0 4-1 7 0 1 1 3 1 5-1 1-1 1-3 1 0 0 0-1-1-1h0c-2 0-3-1-3-2l-1 1c1 1 0 1 0 2 0 0 2 3 3 3l-4-2c-1-1-1-1-1-3l1-1c-1-1-1-1-1-2h1c1-1 2 2 4 2v-1c1 1 2 2 3 2v-3c0-1-1-2 0-2v-1c0-3 1-4 2-6z" class="c"></path><path d="M261 428c1 3 2 7 2 10v8c-2-1-3-3-5-3l-2 1h0c-1 0-3-3-3-3 0-1 1-1 0-2l1-1c0 1 1 2 3 2h0c1 0 1 1 1 1 2 0 2 0 3-1 0-2-1-4-1-5 1-3 1-4 1-7z" class="T"></path><path d="M834 463c-1 0-1-1-1-1v-1 1h1v1l1-1c2-1 5-1 7 0h1l-1 1 2 2c1 2 1 2 1 4l-1 6-2-2-1 6c0 1 0 7-1 8h0v2c-1 3 1 5-2 7-1 2-3 10-2 12l1 1c-2 1-3 2-3 4h0l1 6h1l-1 1v4c-1 1-1 2-1 2l2 2-3 13c0 1 0 4-1 5s-2 2-3 4v2s-1 0-1 1c-1 1 0 1-1 2s-1 3-1 4c-1 2-2 4-2 6s-2 4-2 6l-3 9c-1-1-1-2-2-4v1h-1 0l-1-1c1-2 1-3 1-5h0v-3c1-2 1-5 1-7 1-2 1-5 1-7l3-15c0-7 2-15 3-21-1-1-1-2-1-4 0-1-1-1-1-2-1-2 0-5 0-7l3-24c0-1 0-2 1-3 1 2 1 3 1 5v2c2-7 1-14 2-21 1 0 1 1 2 1v1l3-3z" class="O"></path><path d="M841 479l-1-2c-1 0-2 0-2 1l-1-1c1 0 1-1 2-1l1-1v-1l-1 1h-1c1-2 1-2 3-2v-1l-1-1 1-1 1 1v2h0l-1 6z" class="X"></path><path d="M817 576l7-18c1-3 0-6 2-8 0-1 1-3 1-4h1l-1-1c2-4 1-7 2-10 0-2 2-4 3-5v-1l-1-1c0-1 1-2 1-2h1l-1-2v-1c1-1 1-2 2-3v-6-1l1 6h1l-1 1v4c-1 1-1 2-1 2l2 2-3 13c0 1 0 4-1 5s-2 2-3 4v2s-1 0-1 1c-1 1 0 1-1 2s-1 3-1 4c-1 2-2 4-2 6s-2 4-2 6l-3 9c-1-1-1-2-2-4z" class="z"></path><path d="M822 546l8-53 1-14c1-4 2-8 3-11l3-3h4c1 0 3 3 4 4l-1 6-2-2h0c1-1 1-2 1-3-1-2-2-3-4-4-3 2-4 6-4 9v1c0 1-1 2-1 4 0 1-1 3-1 5 0 1 1 3 0 4 0 5-1 10-2 15 0 2-1 7 0 9l-1 1c-1 3 0 8-2 11v3c0 2-2 5-2 7l-2 12c-1 1-1 3-2 5 0 3 0 7-1 11-1 5-3 9-4 14h-1 0l-1-1c1-2 1-3 1-5h0v-3c1-2 1-5 1-7 1-2 1-5 1-7l3-15v2c0 2 1 3 1 5z" class="r"></path><path d="M817 561c1-2 1-5 1-7l3-15v2c0 2 1 3 1 5s0 5-1 7c0 4-2 7-3 11-1 2-1 4-2 7v-3c1-2 1-5 1-7z" class="t"></path><path d="M834 463c-1 0-1-1-1-1v-1 1h1v1l1-1c2-1 5-1 7 0h1l-1 1 2 2c1 2 1 2 1 4-1-1-3-4-4-4h-4l-3 3c-1 3-2 7-3 11l-1 14-8 53c0-2-1-3-1-5v-2c0-7 2-15 3-21-1-1-1-2-1-4 0-1-1-1-1-2-1-2 0-5 0-7l3-24c0-1 0-2 1-3 1 2 1 3 1 5v2c2-7 1-14 2-21 1 0 1 1 2 1v1l3-3z" class="p"></path><path d="M834 463h1c0 1-1 2-1 3-2 2-3 5-4 8-1 6-1 13-2 19l-2 16h-1v-3l2-21c2-7 1-14 2-21 1 0 1 1 2 1v1l3-3z" class="E"></path><path d="M825 481c0-1 0-2 1-3 1 2 1 3 1 5v2l-2 21-1 12c-1-1-1-2-1-4 0-1-1-1-1-2-1-2 0-5 0-7l3-24z" class="AA"></path><defs><linearGradient id="e" x1="203.016" y1="538.626" x2="185.595" y2="540.925" xlink:href="#B"><stop offset="0" stop-color="#2d2321"></stop><stop offset="1" stop-color="#343639"></stop></linearGradient></defs><path fill="url(#e)" d="M176 471v-5c3-1 4-2 7-1 2 0 3 2 4 4 3 6 3 15 4 23l2 22c0 2 0 3 1 5l8 51 3 12h1v1c1 4 2 9 4 13v1l2 4v2 1l1 2v1c0 1 1 2 1 3v1l1 1c0 1 0 2 1 4l2 3h-1l-1-2-2-2h0 0c-2-1-3-3-4-5 0 2 1 4 2 5v1c1 3 3 6 4 9 1 2 3 5 4 7 0 1 1 2 2 4l1 2c2 3 4 6 6 10l3 5 3 3c1 1 0 1 1 2 1 0 1 1 2 2 0 1 0 0 1 1l5 7v1c-2-1-3-4-5-5l-9-13c-13-21-22-43-30-66l-7-22c-7-23-10-46-14-69l-3-23z"></path><path d="M192 526c2 3 2 7 2 10v2l1 2c1 4 1 9 1 13l-1-2v-3h0v-1-1-1l-1-1v-1c0-2 0-4-2-6 1-2 0-4 0-5-1-3-1-3 0-6z" class="D"></path><path d="M176 471v-5c3-1 4-2 7-1 2 0 3 2 4 4 3 6 3 15 4 23l2 22c0 2 0 3 1 5l8 51s-1-1-1-2-1-2-2-4v-1c0-1-1-1-1-2h1c0 1 1 1 1 2v1-2-1l-1-2-1 1h0c-1-3-2-5-2-7 0-4 0-9-1-13l-1-2v-2c0-3 0-7-2-10l-1-3h1v-2l-1-1c0-5-1-9-1-14 0-4-2-7-2-11v-10-5c-1-1-1-1-1-2 0-3-1-6-2-9-2-2-3-2-5-2-1 1-3 1-3 2l-1 2z" class="E"></path><defs><linearGradient id="f" x1="857.014" y1="290.643" x2="906.662" y2="288.368" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#4b4b4e"></stop></linearGradient></defs><path fill="url(#f)" d="M884 317c-3-2-8-6-10-9-4-7-8-14-11-21-1-2-2-9-4-10l-1-1c0-1-1-3-1-4v-2c-1-2-1-4-1-6s1-3 1-5c1-1 2-1 3-1l1 1c4 0 8 2 12 2 8 1 16 0 24 1 2 0 5 2 8 1h1v1h-1v1c-3 2-8 5-9 9h0c-6 8-6 22-5 32l2 8v1 4c1 1 1 2 1 3-2 0-9-5-10-5z"></path><path d="M892 265h2c0 1 0 1-1 2-1-1-1-1-1-2z" class="F"></path><defs><linearGradient id="g" x1="268.713" y1="546.934" x2="304.787" y2="547.575" xlink:href="#B"><stop offset="0" stop-color="#1f2021"></stop><stop offset="1" stop-color="#463a39"></stop></linearGradient></defs><path fill="url(#g)" d="M283 588c-1-2-2-6-2-8-7-22-10-47-11-70 0-11-1-21 0-32 1-1 0-1 1-2h-1v-2h1v2h1c1 2 2 5 2 7 2 1 2 1 4 1 1 5 0 12 0 17l3 41 3 20c0 1 1 3 0 5 0 1-1 3-1 5l3 6 2 2h0c2 1 3 1 5 1h0c1 0 2-1 4-1v5c1 3 2 5 3 8 2 4 4 8 5 12 0 1 1 2 1 3 1 0 1 0 2-1s2-2 3-2h3l2 2h1c1 1 2 2 2 3v2h0c2 2 3 4 3 6h0v1l-1 2v1c1 1 1 2 1 3l-1-1h-2c-1 1 0 1-2 0h-2v1l-1-1h-1c0 2 1 3 1 4l-2-4c-1 0-2 0-3-1l-2 2h-1l-1 1-1-1 1 1s1 1 1 2h0l-1-1s-1-1-2-1h-1c0 2 1 3 3 5h-3l-12-25c-1-2-2-4-2-5v-6l-1-2c0-1 0-2-1-2v-1 1h-1c0-2-1-2-2-3z"></path><path d="M304 625l-1-2-1-1-1-1c-1-1-1-2-1-3l-1-2h1l1 1v2c0 1 1 2 2 2s1 0 1 1c1 1 1 1 1 2l1 1-1 1-1-1z" class="p"></path><path d="M288 580c2 1 3 1 5 1 0 1 0 1 1 2l-1 1v2l-1-1-2 1c0-2-1-3-1-5l-1-1zm14 32v1c0 2 1 4 2 6l5 4-2 2h-1l-1-1c0-1 0-1-1-2 0-1 0-1-1-1l1-1-2-3s-1-1-1-2h0l-1-1h1c0-2 0-2 1-2z" class="G"></path><path d="M272 476c1 2 2 5 2 7 2 1 2 1 4 1 1 5 0 12 0 17-1 0 0-8-1-10-1-3-4-5-6-8h0c1-1 1-1 2-1v-1c-1 0-1 0-1-1h0v-4z" class="J"></path><path d="M287 593c1 0 1 1 2 1l-1-1 1-1h0l1-2c1 1 1 1 2 3l-1 1h-1c-1 3 0 6 1 8v1l1-2h1c1 2 0 2 1 3 0 1 0 1 1 2v3c1 2 3 4 3 7h-1c-1-4-4-9-7-13v3c-1-2-2-4-2-5v-6l-1-2z" class="U"></path><path d="M288 595c1 2 3 6 2 8v3c-1-2-2-4-2-5v-6z" class="s"></path><path d="M290 603c3 4 6 9 7 13 1 1 1 1 1 2 1 1 2 3 2 5h1c0 1 1 2 1 3 0 2 1 3 3 5h-3l-12-25v-3z" class="p"></path><path d="M293 581h0c1 0 2-1 4-1v5l3 8c2 4 4 8 5 12 0 1 1 2 1 3 1 0 1 0 2-1l2 1v1l-1 2h0l-1-1 1-1h0c-2 0-2 0-3 1l1 1c-1 0-1-1-2-1l-3 3v-1h0c-1-1-1-1-1-2-2-9-9-17-7-26v-1c-1-1-1-1-1-2z" class="P"></path><path d="M294 584l6 15c1 3 3 6 3 8v3l-1 2c-1-1-1-1-1-2-2-9-9-17-7-26z" class="Y"></path><path d="M308 607c1-1 2-2 3-2h3l2 2h1c1 1 2 2 2 3v2h0c2 2 3 4 3 6h0v1l-1 2v1c1 1 1 2 1 3l-1-1h-2c-1 1 0 1-2 0h-2v1l-1-1h-1c0 2 1 3 1 4l-2-4c-1 0-2 0-3-1l-5-4c-1-2-2-4-2-6l3-3c1 0 1 1 2 1l-1-1c1-1 1-1 3-1h0l-1 1 1 1h0l1-2v-1l-2-1z" class="B"></path><path d="M319 617v-2c1 0 2 2 3 3v1l-1 2v1c1 1 1 2 1 3l-1-1h-2c-1 1 0 1-2 0h-2c0-1 2-2 3-3l1-4h0z" class="m"></path><path d="M319 617h0l1 4c1 1 1 2 1 3l-3-3 1-4z" class="I"></path><path d="M318 621l3 3h0-2c-1 1 0 1-2 0h-2c0-1 2-2 3-3z" class="C"></path><path d="M317 607c1 1 2 2 2 3v2h0c2 2 3 4 3 6h0c-1-1-2-3-3-3v2h0-1 0l-1-1h0c0 2-1 3-3 4-1 1-1 1-1 0-1 1-2 1-3 1l-1-1 1-1h2c1 0 1 0 1-1h-1-1-2l-1-1h0c2-1 2-1 3-2h2c2 0 2 0 3-1s1-2 2-3h0l-1-4z" class="E"></path><path d="M308 607c1-1 2-2 3-2h3l2 2h1l1 4h0c-1 1-1 2-2 3s-1 1-3 1h-2c-1-1-2-3-2-4h0l1-2v-1l-2-1z" class="i"></path><path d="M309 611h0c1 0 2 0 3 1 0 1 1 2 1 3h-2c-1-1-2-3-2-4z" class="Q"></path><path d="M308 607c1-1 2-2 3-2h3l2 2h1l1 4h0c-1 1-1 2-2 3v-2c0-2-1-2-2-4-2 0-2 0-4 1v-1l-2-1z" class="H"></path><path d="M712 559c0 1 0 2 1 3 4-5 3-14 5-20v6l1-1 1-3 1 5h0l1 1 1 1 1 1h-1c-1 1 0 0 0 2v3l2-2v-3c1 3 1 4 0 7 0 1 0 3-1 4h1c2 0 3-1 4 0l3 3 2 1 1 3c1 0 1 0 2-1 1 2 2 3 3 4l-2 7c-4 11-8 24-14 34-1 1-2 1-3 1l-2-2h-2c-1 1-2 1-2 3h-1c-1 0-1 0-2 1h-1c-1 0-2 1-3 2 1 0 4 2 5 1l3-2 1 1c-1 1-2 2-3 2v1l-1 2h-1-2-2-5v-2c-1 0-1 0-2-1h0c-1 0-1 0-1 1l-1-1c0-2 0-3-1-5l-1 2v-1-2l-1 1h0l1-3 1-1v-1c1-1 2-4 3-6v-1-2l1-1 1-1c0-2 0-3 1-4l-1-1c0-1 1-2 1-3 1-3 1-4 2-6 0-1 1-1 1-2v-3c1 0 2-1 2-2v-4-4l3-12z" class="m"></path><path d="M721 601v1l-1 1 1 1c1-2 1-3 2-5v-1l1-1v1l-1 2 1 1c-2 2-3 4-3 7h-1v2c0 1 0 2-1 3h-2c-1 1-2 1-2 3h-1c-1 0-1 0-2 1h-1c-1 0-2 1-3 2h-2l-1-2 1-1 1 1 1-1h2v-3c1-1 1-2 0-4l2-1h-1l1-1h0c1 0 1 0 2 1l2 3c2-1 2-4 2-7 1-1 2-3 3-5v2z" class="K"></path><path d="M712 607c1 0 1 0 2 1l2 3v1l-4 3c0-1 1-4 0-6v-1h-1l1-1h0z" class="B"></path><path d="M735 570c1 0 1 0 2-1 1 2 2 3 3 4l-2 7c-1-1-1-2-1-2-1 0-2 1-2 2l-1 1v1l-1-2v1c-1 1-1 2-2 3s-1 2-1 2l-1-1 1-1h0l1-1-1-2c-1 1-1 2-1 3h-1v-2-1h0c-2-1-3-1-4-1h0l-4-5 1-3h1c0 1 0 1 1 2 1 2 2 4 5 4 1 0 2 0 3-1l-1-2-1 1c-1 0-1 0-2-1l1-1 2-1h0c1-1 2-2 4-2h0l1-1z" class="I"></path><path d="M720 575l1-3h1c0 1 0 1 1 2 1 2 2 4 5 4 1 0 2 0 3-1h0c1 0 3-1 3-2v-1l1-1h1c0 2-1 3-1 4-2 1-3 1-3 4-1-1-1 0-2-1-1 0-1 0-2 1-2-1-3-1-4-1h0l-4-5z" class="N"></path><path d="M725 563c2 0 3-1 4 0l3 3 2 1 1 3-1 1h0c-2 0-3 1-4 2h0l-2 1-1 1c1 1 1 1 2 1l1-1 1 2c-1 1-2 1-3 1-3 0-4-2-5-4-1-1-1-1-1-2h-1l-1 3v-6l1-4c1-1 2-1 3-2h1z" class="E"></path><path d="M730 572h-1c-1 1-1 1-2 1-2-1-4-2-4-4v-1c2-2 2-2 4-2h0c1 1 1 2 2 3l1 3z" class="Q"></path><path d="M729 569l-1 1v1h-1l-1-1c-1-1-1-2-1-3l2-1c1 1 1 2 2 3z" class="k"></path><path d="M725 563c2 0 3-1 4 0l3 3 2 1 1 3-1 1h0c-2 0-3 1-4 2v-1l-1-3c-1-1-1-2-2-3h0 0l-2-1h0-1l1-2z" class="N"></path><path d="M718 563v4h1l2-2-1 4v6l4 5h0c1 0 2 0 4 1h0l-5 13c0 2-1 3-1 4-1 1-1 2-1 3v-2c-1 2-2 4-3 5 0 3 0 6-2 7l-2-3c-1-1-1-1-2-1l-2-1-1-1h-1-1v-4l2-5-1-3c1-5 4-10 5-15 2-5 4-10 5-15z" class="H"></path><path d="M718 563v4h1l2-2-1 4-2 3h0c1 1 0 3 0 4s1 3 0 3v4l1 1-2 2v-3l-1-1 1-1v-1c-2 0-2-1-4-2h0c2-5 4-10 5-15z" class="K"></path><path d="M725 583v-1l1-1v2 1l-2 6-3 9c-1 2-2 4-3 5 0 3 0 6-2 7l-2-3c1-6 4-11 6-16l1-5c1-2 2-3 4-4z" class="R"></path><path d="M720 592l1-5c1-2 2-3 4-4-1 1-1 2-1 3l-1 3v1c-1 1-2 1-3 2z" class="I"></path><path d="M713 578h0c2 1 2 2 4 2v1l-1 1 1 1v3s1 0 0 1v3h0c0 1-1 3-1 4-2 1-2 2-2 3 0 2-1 4-2 5l-2 1v3l-1-1h-1-1v-4l2-5-1-3c1-5 4-10 5-15z" class="m"></path><path d="M709 596v-1c1-1 1-2 2-3l1-1c1-1 1-4 1-6l1-2 1 1h0v3h0c-1 1 0 2 0 3h-2l1 2h0c-1 1-1 1-1 2l-2 2c-1 2-1 3-2 5v1 1c-1 1-1 1 0 2h-1-1v-4l2-5z" class="L"></path><path d="M708 605v-5c0-2 1-3 3-4-1 2-1 3-2 5v1 1c-1 1-1 1 0 2h-1z" class="R"></path><path d="M712 559c0 1 0 2 1 3 4-5 3-14 5-20v6l1-1 1-3 1 5h0l1 1 1 1 1 1h-1c-1 1 0 0 0 2v3l2-2v-3c1 3 1 4 0 7 0 1 0 3-1 4s-2 1-3 2l-2 2h-1v-4c-1 5-3 10-5 15-1 5-4 10-5 15l1 3-2 5v4h1 1l1 1 2 1h0l-1 1h1l-2 1c1 2 1 3 0 4v3h-2l-1 1-1-1-1 1 1 2h2c1 0 4 2 5 1l3-2 1 1c-1 1-2 2-3 2v1l-1 2h-1-2-2-5v-2c-1 0-1 0-2-1h0c-1 0-1 0-1 1l-1-1c0-2 0-3-1-5l-1 2v-1-2l-1 1h0l1-3 1-1v-1c1-1 2-4 3-6v-1-2l1-1 1-1c0-2 0-3 1-4l-1-1c0-1 1-2 1-3 1-3 1-4 2-6 0-1 1-1 1-2v-3c1 0 2-1 2-2v-4-4l3-12z" class="J"></path><path d="M705 602c0-3 2-6 3-9l1 3-2 5-2 1z" class="C"></path><path d="M707 601v4h1v1c-2 0-2 0-3 1s-1 1-2 1v-2c1-1 1-2 2-3v-1l2-1z" class="F"></path><path d="M698 616c1-1 1-2 2-3 1 1 1 4 1 5v1c1 0 1 0 2 1h0v2h0c-1 0-1 0-2-1h0c-1 0-1 0-1 1l-1-1c0-2 0-3-1-5zm22-72l1 5c0 3-1 6-2 9v2c-1-5-1-8 0-13l1-3z" class="C"></path><path d="M708 616c-2-1-3-3-4-4l1-2c1-1 2-1 2-1 2-1 2 0 3 0 1 2 1 3 0 4v3h-2z" class="h"></path><path d="M721 549h0l1 1 1 1 1 1h-1c-1 1 0 0 0 2v3l2-2v-3c1 3 1 4 0 7 0 1 0 3-1 4s-2 1-3 2l-2 2h-1v-4c0-1 0-2 1-3v-2c1-3 2-6 2-9z" class="L"></path><path d="M721 549h0l1 1 1 1 1 1h-1c-1 1 0 0 0 2v3l-1-1-1 2 1 1v1c-1 0-2-1-3-2 1-3 2-6 2-9z" class="X"></path><path d="M712 559c0 1 0 2 1 3 4-5 3-14 5-20v6c-1 3-1 7-2 10 0 2-1 4-2 6-1 7-3 14-6 20 0 2-1 4-1 6l-3 6-1-1c0-1 1-2 1-3 1-3 1-4 2-6 0-1 1-1 1-2v-3c1 0 2-1 2-2v-4-4l3-12z" class="L"></path><path d="M256 118c2-2 2-3 4-3 3 0 5 3 7 5 3 3 7 6 11 8 1 1 2 2 4 2h1c1 2 3 3 5 4 1 1 1 1 1 2 1 1 3 1 5 1s4-2 7-1c1 1 1 1 1 2 1 2 1 3 3 4l2-2h1c1 0 3-1 4-2l3-5v-1h-1l1-1c3-1 5-3 7-5l1 1h0c0 2-1 4-2 5-1 4-2 8-5 11 0 0-1 2-2 3s-3 6-5 7c-4 4-8 8-14 11 0 1-1 1-2 2 2-1 4-2 6-2l1-1 1-1 1 1-2 1 1 1c-2 1-5 2-6 4h1c-2 1-4 1-6 1-3 1-6 2-9 1h-1c-2-1-4-1-5-3h-1-1 0v3c1 1 2 1 2 3l1 2c-3-1-5-1-8-2h-1l-8-2-3-1c1-2 1-3 3-4 2-3 3-7 6-8h1l-1-2c-1 0-1 1-2 0s-1-2-1-4c2-9-1-18-3-26l-1-2c-1-1-1-6-2-7z" class="f"></path><path d="M303 150h0c0-1 0-2-1-2l1-1h2c1-1 2-1 3-2h0c2-1 0 0 1 0 7-2 7-7 10-12l1-1h0l1-2c0-1 1-2 1-3h1 0c0 2-1 4-2 5-1 4-2 8-5 11l-1-1c-3 3-7 6-11 6v1c0 1 0 0-1 1z" class="e"></path><path d="M265 140l2 2c0 1 0 2 1 3l1-1 1 1 1-1h1c2 2 6 6 7 8 0 2 2 3 4 4h1v1c1 1 2 1 3 2h1c1 1 4 0 6 0l1-1h0c-2 1-4 1-6 0h-1c1-1 3-1 4-1l1 1 1-1h2v-1h0l4-3h0l2-2 1-1h0c1-1 1 0 1-1v-1c4 0 8-3 11-6l1 1s-1 2-2 3-3 6-5 7c-4 4-8 8-14 11l-3 1c-1 1-2 1-3 1-4 0-7-2-9-4-5-4-9-9-11-14-2-2-3-5-4-8z" class="j"></path><path d="M303 150c1-1 1 0 1-1v-1c4 0 8-3 11-6l1 1s-1 2-2 3l-1-1c-4 3-8 7-13 8l2-2 1-1h0z" class="Z"></path><path d="M269 148h1c1 1 4 3 5 5 3 3 5 6 9 7 1 1 2 1 3 2 2 0 6-1 8-1 2-1 3-2 4-4 2-2 7-4 9-5l1 1c-4 4-8 8-14 11l-3 1c-1 1-2 1-3 1-4 0-7-2-9-4-5-4-9-9-11-14z" class="V"></path><path d="M280 162l1-1c3 2 6 3 9 3l2 1c-1 1-2 1-3 1-4 0-7-2-9-4z" class="X"></path><path d="M258 125l1-1v2l4 15v4c1 2 1 4 1 6 1 2 1 3 1 4v1c1 1 2 4 3 5 1-1 0-2 0-3v-1-1-1l1-1c2 3 2 5 5 7 1 0 2 1 2 2 1 1 1 2 2 3h0 3 2s1 0 1 1l2-1c-1 0-2 0-3-1 0 0-1 0-1-1h-1l-2-2h0c-3-1-5-4-6-6l-3-4v-1h-1v-1c-1-2-3-5-4-7h0l-1-6c-1-2-2-5-2-7 1 3 2 7 3 10s2 6 4 8c2 5 6 10 11 14 2 2 5 4 9 4 1 0 2 0 3-1l3-1c0 1-1 1-2 2 2-1 4-2 6-2l1-1 1-1 1 1-2 1 1 1c-2 1-5 2-6 4h1c-2 1-4 1-6 1-3 1-6 2-9 1h-1c-2-1-4-1-5-3h-1-1 0v3c1 1 2 1 2 3l1 2c-3-1-5-1-8-2h-1l-8-2-3-1c1-2 1-3 3-4 2-3 3-7 6-8h1l-1-2c-1 0-1 1-2 0s-1-2-1-4c2-9-1-18-3-26l-1-2z" class="o"></path><path d="M273 168c-1-1-2-5-3-7v-1l1 1h0l1 1h0c1 3 4 6 7 7 5 2 17-3 21-5l1 1c-2 1-5 2-6 4h1c-2 1-4 1-6 1-3 1-6 2-9 1h-1c-2-1-4-1-5-3h-1-1z" class="Z"></path><path d="M266 159c3 4 4 11 9 15l1 2c-3-1-5-1-8-2-2-2-1-3-1-6-1-1-1-1-2-1v-1c-1-3-1-4 0-7h1z" class="D"></path><path d="M259 167c2-3 3-7 6-8-1 3-1 4 0 7v1c1 0 1 0 2 1 0 3-1 4 1 6h-1l-8-2-3-1c1-2 1-3 3-4z" class="j"></path><path d="M256 171c1-2 1-3 3-4v5l-3-1z" class="Z"></path><path d="M249 170c2 0 5-1 7 1l3 1 8 2-2 2c-2-1-4-1-6-1 4 2 9 3 11 7 0 1 0 2-1 3l-2-1-2 2-2 1v1h1l1 1c-1 0-1 0-2 1v1l-2 2c-7 4-13 8-20 11-3 1-5 3-8 4-1 0-2 1-3 1l-3 1c-1 1-2 2-4 2-3 1-5 2-7 3-2-1-2-1-4-1v-1l1-1c-4-2-7-4-10-7h-1l-1-1c-1-1-2-1-4-1l-2-3v-1l1-2v-1h0c0-2 0-4 1-5l1-1 2-2h-2-1 0l1-3 2-1 8-3 31-10h2l8-1z" class="T"></path><path d="M239 187c-1-1-1-2-2-3h-1l1-1c1 1 0 1 1 1h1c0 1 1 1 2 2l2-2h1l-2 2h2c-2 1-4 1-5 1z" class="S"></path><path d="M195 199l1-2v-1l1 3c1 2 3 3 4 5-1-1-2-1-4-1l-2-3v-1z" class="j"></path><path d="M230 203c-1 0-1 0-2-1h-1v-3h-1c1-2 2-2 3-3s2-1 3-2l-1 3-1-1c0 1-1 1-1 2l-1 4h3l-1 1z" class="S"></path><path d="M196 196c3 0 4-2 6-3s6-1 8-3c0-1 1-1 2-1-1 1-2 3-3 4h1-1l-6 3c-1 1-3 2-4 2l-2 1-1-3h0z" class="F"></path><path d="M202 205c2 0 3 0 4 1l3 2v-3h0l1 2c1 1 1 2 2 3v1h1c0-1-1-2 0-3h0 0c0 1 1 1 1 2v2h2c2-1 2-3 5-4 1 1 1 2 2 4-3 1-5 2-7 3-2-1-2-1-4-1v-1l1-1c-4-2-7-4-10-7h-1z" class="O"></path><path d="M244 186h1c1-1 0-2 2-2l1 1h0c2 0 2 0 3 1l1 1c-3 2-6 2-9 3-2 1-4 0-6 2h-2 0l-2 1c0 1-2 0-3 0s-1 1-3 0c0-1 0-1 1-2 3 0 4-3 7-3 1 0 2-1 4-1 1 0 3 0 5-1z" class="M"></path><path d="M244 186h0l1-1v-1c0-1 1-2 2-2h1c1 1 2 0 3-1l1 1c3-1 5-2 8-1 2 1 4 2 5 4v1l-2 1-2 2h-2v-1c-3 0-5 2-8 2-1 0-2 1-3 1h-2l-8 2-1-1c2-2 4-1 6-2 3-1 6-1 9-3l-1-1c-1-1-1-1-3-1h0l-1-1c-2 0-1 1-2 2h-1z" class="S"></path><path d="M259 188l1-1c-1-1-3 0-4 0l-1-1c2-2 4-3 7-3l3 2v1l-2 1-2 2h-2v-1z" class="Y"></path><defs><linearGradient id="h" x1="254.159" y1="202.76" x2="234.605" y2="188.581" xlink:href="#B"><stop offset="0" stop-color="#5b595e"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#h)" d="M261 189l2-2v1h1l1 1c-1 0-1 0-2 1v1l-2 2c-7 4-13 8-20 11-3 1-5 3-8 4-1 0-2 1-3 1l-3 1 1-2h1v-1-1l1-2v-1l1-1h-3l1-4c0-1 1-1 1-2l1 1 1-3 1 1c2-1 3-2 5-2l8-2h2c1 0 2-1 3-1 3 0 5-2 8-2v1h2z"></path><path d="M229 198v1 2-2c1 0 1-1 2-1l1 1 1-1h2 1v1h-1c-1 2-2 3-3 5l-1 1c-1 1-1 2 0 3 1 0 1 0 2-1v1c-1 0-2 1-3 1l-3 1 1-2h1v-1-1l1-2v-1l1-1h-3l1-4z" class="M"></path><defs><linearGradient id="i" x1="255.702" y1="193.1" x2="250.298" y2="188.9" xlink:href="#B"><stop offset="0" stop-color="#747277"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#i)" d="M246 191h2c1 0 2-1 3-1 3 0 5-2 8-2v1h2c-2 2-4 3-7 4h-1-2c-2 1-3 2-4 2l-1-2h-1c1 0 1 0 2-1l-1-1h0z"></path><path d="M249 170c2 0 5-1 7 1l3 1 8 2-2 2c-2-1-4-1-6-1 4 2 9 3 11 7 0 1 0 2-1 3l-2-1c-2-1-4-3-6-4-5-1-8-2-13-1-1 1-3 0-4 0-2 0-5 1-8 1-4 1-8 3-12 5l-7 2v1c-2 0-3 0-5 1-1 0-2 0-2 1-2 2-6 2-8 3s-3 3-6 3c0-2 0-4 1-5l1-1 2-2h-2-1 0l1-3 2-1 8-3 31-10h2l8-1z" class="I"></path><path d="M230 177h-1c-1 1-2 1-3 2 1 0 2 0 3-1h4c1-1 2-1 4-1h0 1l1-1c4 0 11-1 14 1-11 0-22 2-33 5-2 0-4 1-5 0l15-5z" class="J"></path><path d="M200 188c5-3 10-5 15-6 1 1 3 0 5 0-3 2-7 3-10 5l-3 1-1 1-5 2c-1 0-2 0-2 1l-2-1 1-1 2-2z" class="P"></path><path d="M230 177c9-3 20-5 29-2 4 2 9 3 11 7 0 1 0 2-1 3l-2-1c-2-1-4-3-6-4-3-2-5-2-8-3-3-2-10-1-14-1l-1 1h-1 0c-2 0-3 0-4 1h-4c-1 1-2 1-3 1 1-1 2-1 3-2h1z" class="N"></path><defs><linearGradient id="j" x1="223.897" y1="171.874" x2="231.605" y2="186.201" xlink:href="#B"><stop offset="0" stop-color="#afaeaf"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#j)" d="M249 170c2 0 5-1 7 1l3 1 8 2-2 2c-2-1-4-1-6-1-9-3-20-1-29 2l-15 5c-5 1-10 3-15 6h-2-1 0l1-3 2-1 8-3 31-10h2l8-1z"></path><path d="M366 700h0c1 0 2 1 3 1l2 1h0v-1l1-1 1 2h0c1 2 3 3 4 4l2 3c1 1 1 1 1 2 1 1 4 5 6 5v1h0c2 0 4 1 5 2h1l17 23c4 5 8 11 12 16 2 4 5 8 8 12l3 3h0-2c-1 0-1-1-2-1h-1c1 1 3 4 3 5l-3-1c-1 0-2 0-3 1l-3 3-2 2v-1c-1-2-2-3-3-5 0-1-1-2-2-3 1 3 1 7 2 9v3h1v3l-1-1-1 1 1 1-1-1c0 2 3 5 2 6h-1l-4-6c-1-1-3-5-4-6l-23-32-7-9-18-27-1-2c1 0 3 0 4-1s1-1 2-1h1v-1-1c0-1-1-1 0-2l2-1-1-1c-1 0-1 0-3 1h-1v-1c1 0 2-1 3-1h1v-1l-1-2z" class="U"></path><path d="M359 712c1 0 3 0 4-1s1-1 2-1v1c1 1 0 2 0 4h-1c-1 0-2-1-3-2l-1 1-1-2z" class="P"></path><path d="M366 709c4 2 6 5 8 9l-1 1c1 1 2 2 2 3-2-1-5-5-6-7 0 0 0-1-1-1-1-2-1-3-2-4h0v-1z" class="K"></path><path d="M396 756c1 0 2 1 3 1 2 1 4 1 5 3s3 4 4 6c-2-1-3-2-4-3-2-1-3-2-5-3-2 0-2-1-3-2h-1c0-1 1-1 1-2z" class="C"></path><path d="M366 709v-1c0-1-1-1 0-2l2-1c3 4 5 8 7 11v3l-1-1c-2-4-4-7-8-9z" class="B"></path><path d="M375 716c2 2 3 4 5 6 1 1 1 3 2 4l3 3c1 2 3 5 4 7s2 5 4 6c-1 2 0 4 0 6 1 2 1 4 2 5s2 1 3 2h0c1 1 1 1 2 1 2 0 2 1 3 2l1 2c-1-2-3-2-5-3-1 0-2-1-3-1h0l-1-1c-1 0-2 0-4-1l1-1c0-1-1-2-1-3 1-1 1-1 0-2 0-2 0-4-1-6-3-6-7-12-11-17-1-2-3-4-4-6v-3z" class="H"></path><path d="M366 700h0c1 0 2 1 3 1l2 1h0v-1l1-1 1 2h0c1 2 3 3 4 4l2 3c1 1 1 1 1 2 1 1 4 5 6 5v1h0c2 2 3 5 4 7 2 1 4 3 5 5 2 3 4 7 7 10h-1l-2-1c-2 0-3 1-4 2l-2 2c-2-1-3-4-4-6s-3-5-4-7l-3-3c-1-1-1-3-2-4-2-2-3-4-5-6-2-3-4-7-7-11l-1-1c-1 0-1 0-3 1h-1v-1c1 0 2-1 3-1h1v-1l-1-2z" class="R"></path><path d="M366 700h0c1 0 2 1 3 1l2 1h0v-1l1-1 1 2h0c1 2 1 3 2 4h0l-1 2c1 1 1 2 1 2 1 2 1 3 2 5h0c-1-1-2-1-2-2-1-2-1-2-2-3l-1-2c0-1-1-2-1-3h-3c1-1 1-1 1-2-1-1-1-1-2-1l-1-2z" class="c"></path><path d="M395 740c-1-4-4-7-6-10l-11-17 1-1 18 23c0 1 1 2 2 3-2 0-3 1-4 2z" class="C"></path><path d="M373 702c1 2 3 3 4 4l2 3c1 1 1 1 1 2 1 1 4 5 6 5v1h0c2 2 3 5 4 7 2 1 4 3 5 5 2 3 4 7 7 10h-1l-2-1c-1-1-2-2-2-3l-18-23-4-6h0c-1-1-1-2-2-4z" class="O"></path><path d="M386 717c2 0 4 1 5 2h1l17 23c4 5 8 11 12 16 2 4 5 8 8 12l3 3h0-2c-1 0-1-1-2-1h-1c1 1 3 4 3 5l-3-1c-1 0-2 0-3 1l-3 3-2 2v-1c-1-2-2-3-3-5 0-1-1-2-2-3-2-2-4-5-6-7-1-2-3-4-4-6l-1-2c-1-1-1-2-3-2-1 0-1 0-2-1h0c-1-1-2-1-3-2s-1-3-2-5c0-2-1-4 0-6l2-2c1-1 2-2 4-2l2 1h1c-3-3-5-7-7-10-1-2-3-4-5-5-1-2-2-5-4-7z" class="D"></path><path d="M402 739c1-1 2-1 3 0 0 1 1 1 1 2h0c1 1 1 1 1 2s1 2 1 2v1h0l-2 2c0-1 0-1-1-1v-3c0-2-2-2-3-3l-1-2h1z" class="G"></path><path d="M408 746h0v-1c2 3 3 5 4 7 3 2 4 5 6 9l-1-1h-1c-1-1-1-1-2-1l-2-2-2-4-2-2h0-1l-1-3 2-2z" class="F"></path><path d="M408 746c1 2 1 3 0 5h0-1l-1-3 2-2z" class="s"></path><path d="M399 738l2 1 1 2c1 1 3 1 3 3v3c1 0 1 0 1 1l1 3h1c0 2 0 2-1 3-1 0-1 1-1 2h-2-1c-2-1-4-1-5-1-1-1-2-1-3-2s-1-3-2-5c0-2-1-4 0-6l2-2c1-1 2-2 4-2z" class="N"></path><path d="M402 741c1 1 3 1 3 3v3h-1c-2 0-2 0-4-1-1-1-1-2-2-3 1-1 2-2 4-2z" class="h"></path><path d="M400 746c2 1 2 1 4 1h1c1 0 1 0 1 1l1 3c-2 2-3 2-6 2-1 0-3 0-4-1-1-2-1-2-1-4 2 1 3 2 5 2 1 1 3 0 4-1h-3c-2-1-2-1-2-3z" class="AB"></path><path d="M408 751h0l2 2 2 4 2 2c1 0 1 0 2 1h1l1 1 6 7c1 1 2 3 3 4s3 4 3 5l-3-1c-1 0-2 0-3 1l-3 3-2 2v-1c-1-2-2-3-3-5 0-1-1-2-2-3-2-2-4-5-6-7-1-2-3-4-4-6l-1-2c-1-1-1-2-3-2-1 0-1 0-2-1h0c1 0 3 0 5 1h1 2c0-1 0-2 1-2 1-1 1-1 1-3z" class="D"></path><path d="M415 767c4 2 6 6 9 10l-3 3c-1-1-1-2-2-3l1-1c-2-3-4-6-5-9z" class="m"></path><path d="M407 754c0 1 1 2 2 3 2 1 5 7 6 10s3 6 5 9l-1 1-13-21c0-1 0-2 1-2zm7 5c1 0 1 0 2 1h1l1 1 6 7c1 1 2 3 3 4s3 4 3 5l-3-1c-3-3-6-6-8-9-1-2-2-3-3-4 0-2-1-3-2-4z" class="L"></path><path d="M398 755c1 0 3 0 5 1h1 2l13 21c1 1 1 2 2 3l-2 2v-1c-1-2-2-3-3-5 0-1-1-2-2-3-2-2-4-5-6-7-1-2-3-4-4-6l-1-2c-1-1-1-2-3-2-1 0-1 0-2-1h0z" class="P"></path><defs><linearGradient id="k" x1="666.474" y1="129.698" x2="685.816" y2="190.21" xlink:href="#B"><stop offset="0" stop-color="#bbbab8"></stop><stop offset="1" stop-color="#f4f4f5"></stop></linearGradient></defs><path fill="url(#k)" d="M641 140c-9-3-17-5-26-5 12-4 31-1 44 3 3 4 9 4 13 7l20 11 2 1h0l14 10h0c2 0 3 1 5 1 0 1 1 2 1 2 1 1 2 1 3 1 2 1 5 1 8 2h0l13 11c2 0 7 4 8 6v1h1c0 1 1 1 1 2l3 5-1 2-3 1c-2 0-3-2-5-3h0c-2 0-3-1-4-2s-3-2-4-3v5c0 3-1 7 0 9v1c-1-1-1-1-1-2 1-2 1-4 1-6-1-6-5-9-10-13h2l-6-7c-1-1-2-1-3-2l-1 1c-2 0-3 0-4-1l-5-4v1c1 2 4 3 5 5l-1 1v1-1c-2-3-2-4-5-4 2 2 5 4 5 8v1c0 2-5 5-7 6l-16 11c-1 1-3 3-5 3-1 1-2 1-3 2l-4 2c3-4 6-7 7-12 2-9-1-16-5-24l-5-5c-2-2-4-3-6-4-2 0-7-1-9-2l-5 1c2-1 6-3 8-5v-3c0-3-2-4-4-6-5-4-10-7-16-10z"></path><path d="M683 164v-1l1 1c1 0 1 0 2 1l1 1 1-1 1 1h2c2 0 3 2 5 3 1 0 1 0 1 1v1c1 0 2 1 4 2h-1c-2 0-6-3-7-4-4-2-7-3-10-5z" class="h"></path><path d="M648 139c7 2 13 5 19 8 4 2 8 5 12 7h0l-1 1c3 2 6 3 8 5h-3c-2 0-4 0-5-1-2 0-3 0-4-1 1-1 1 0 2-1l-1-1 1-1-1-1v-1c0-1-1-1-2-2-1 0-2-1-3-2h-1l-1-1c-1-1-5-3-7-3 1 1 2 1 3 2l-1 1c-1-1-2-1-3-2-1 0-1 0-2-1h-1c-2-1-3-2-5-2-1-1-2-1-4-1l-2-2c1-1 1 0 2-1z" class="f"></path><path d="M658 163c4 0 9-2 13-1 4 0 8 1 12 2 3 2 6 3 10 5 1 1 5 4 7 4l6 4c2 2 5 4 5 8v1s0-1-1-2l-1-1c-1-2-5-4-7-5-5-4-10-7-16-10-4-1-9-2-13-3h-6c-2 0-7-1-9-2z" class="H"></path><path d="M667 165h6c4 1 9 2 13 3 6 3 11 6 16 10 2 1 6 3 7 5l1 1c1 1 1 2 1 2 0 2-5 5-7 6l-1-1 1-2c2 0 3 0 4-2-1-2-4-4-6-5-1-1-3-3-4-3-1-1-2 0-4 0 0-2-2-3-3-4-2-3-3-3-6-4v-1h-2-1c-1-1-1-1-2-1h0-2l-1-1c-2 0-3 0-4 1-2-2-4-3-6-4z" class="g"></path><defs><linearGradient id="l" x1="689.921" y1="173.03" x2="679.5" y2="207.65" xlink:href="#B"><stop offset="0" stop-color="#aba9a9"></stop><stop offset="1" stop-color="#d8d7d7"></stop></linearGradient></defs><path fill="url(#l)" d="M673 169c1-1 2-1 4-1l1 1h2 0c1 0 1 0 2 1h1 2v1c3 1 4 1 6 4 1 1 3 2 3 4 2 0 3-1 4 0 1 0 3 2 4 3 2 1 5 3 6 5-1 2-2 2-4 2l-1 2 1 1-16 11c-1 1-3 3-5 3-1 1-2 1-3 2l-4 2c3-4 6-7 7-12 2-9-1-16-5-24l-5-5z"></path><path d="M693 182l1 1 2-1h1 2v1c1 0 2 1 3 1v2l-1-1-2 1c-2 0-2-3-3-3s-2 1-4 1l1-2z" class="Z"></path><path d="M702 184v-2c2 1 5 3 6 5-1 2-2 2-4 2l-1 2 1 1-16 11 4-4c1-1 4-3 5-5h0l2-2v-1-2-1h2c0-1 1-1 1-2v-2z" class="j"></path><path d="M673 169c1-1 2-1 4-1l1 1h2 0c1 0 1 0 2 1h1 2v1c3 1 4 1 6 4 1 1 3 2 3 4 2 0 3-1 4 0 1 0 3 2 4 3v2c-1 0-2-1-3-1v-1h-2-1l-2 1-1-1v-1l-1-1s-1 1-2 1c0-1 0-1-1-2v-1l-2-2-1-1v-1c1 0 2 0 2-1l-1-1c-3 0-6-1-9-2h-1-1c1 1 1 2 2 3v1l-5-5z" class="n"></path><path d="M689 178h2l2 2v1l-1-1s-1 1-2 1c0-1 0-1-1-2v-1z" class="Q"></path><path d="M641 140c-9-3-17-5-26-5 12-4 31-1 44 3 3 4 9 4 13 7l20 11 2 1h0l14 10h0c2 0 3 1 5 1 0 1 1 2 1 2 1 1 2 1 3 1 2 1 5 1 8 2h0l13 11c2 0 7 4 8 6v1h1c0 1 1 1 1 2l3 5-1 2-3 1c-2 0-3-2-5-3h0c-2 0-3-1-4-2s-3-2-4-3v5c0 3-1 7 0 9v1c-1-1-1-1-1-2 1-2 1-4 1-6-1-6-5-9-10-13h2l-6-7c-1-1-2-1-3-2l-1 1c-2 0-3 0-4-1l-5-4c-5-6-14-10-21-14-2-2-5-3-8-5l1-1h0c-4-2-8-5-12-7-6-3-12-6-19-8-3-1-5-1-7-1l-1 1h1v1z" class="i"></path><path d="M686 160c-2-2-5-3-8-5l1-1c5 3 9 6 14 9 8 5 16 9 24 15l-1 1c-2 0-3 0-4-1l-5-4c-5-6-14-10-21-14z" class="O"></path><path d="M720 180v1c5 3 9 7 14 10 1 2 4 3 6 4 1 1 3 2 5 2l1 1h0c0-2 0-1-1-2-1-2-2-5-3-7s-2-4-4-5c2 0 7 4 8 6v1h1c0 1 1 1 1 2l3 5-1 2-3 1c-2 0-3-2-5-3h0c-2 0-3-1-4-2s-3-2-4-3v5c0 3-1 7 0 9v1c-1-1-1-1-1-2 1-2 1-4 1-6-1-6-5-9-10-13h2l-6-7z" class="e"></path><defs><linearGradient id="m" x1="182.15" y1="500.277" x2="263.487" y2="612.763" xlink:href="#B"><stop offset="0" stop-color="#6c6c6d"></stop><stop offset="1" stop-color="#adabac"></stop></linearGradient></defs><path fill="url(#m)" d="M206 492v-2-4l2-1c0-1 0-1-1-2h1v-2l1 1v-1c1-1 2-1 3-1s1 1 1 2c1 1 1 1 2 1l2 2 1-1v2c1 3 2 3 3 6 0 2 1 3 1 5h0c1 1 2 2 2 3 1 1 0 4 1 5 1 0 3 1 3 3l1 3v2c-1 3-1 7 1 11 1 1 5 5 5 6h-1c-4 3-5 7-6 11-3 12-2 26 0 38 2 24 6 47 14 69-3-2-2-4-3-6h-1v-2c0-1-1-1-1-2v-1-1c-1-1-1-1-1-2-2-1-4-1-5-1h-1c-8-18-15-38-17-57 0-3-1-7-1-9-1-2-1-3-1-4 1 1 1 2 1 3v1c2-2-1-6-1-8v-2c-1-1-1-1-1-2v-2c-1-3-1-7-2-10-2-11-4-22-3-33h0c1 1 1 2 1 3l2 1 1-1v-1l-2-2 1-1c0-2-1-2-2-3 0-2 1-3 0-5h1l1 1h1v-2-2c-1-1-2-1-3-3l1-2c0-1 0-1-1-1z"></path><path d="M206 515h1l1 1v1l1 2h-2c-1-1 0-3-1-4zm1 5h1c1 1 1 1 1 3-1 1-1 0-1 1v2l-1-1c-1-2-1-3 0-5z" class="X"></path><path d="M850 370l3 6c2 6 8 11 12 16l-2 1-3 3-1 1c0 1 0 2-1 3l-3 6v1c-1 1-1 1-1 2l3-3c1 1 2 1 3 1-1 3-2 4-4 5-2 3-5 8-4 11v1l-4 19v3 9c4 5 12 13 11 21 0 2-1 4-3 5v-5l-1-1c-1-4-6-10-10-12-1-1-1-1-2-1h-1c-2-1-5-1-7 0l-1 1v-1h-1v-1 1s0 1 1 1l-3 3v-1c-1 0-1-1-2-1-1 7 0 14-2 21v-2c0-2 0-3-1-5-1 1-1 2-1 3l1-12c-1-10 1-19 0-29l-3-33-1-3 1-4h0v-11l1-1h1l2 2h0v1s1 1 1 2v4l1-1 2-2v-1c2-3 3-6 3-9 2-1 2-2 2-4h1 1v2c2 0 1 0 2-1h2l4-1h0l1-1h1c1 0 1 0 3-1 0-1 0-2-1-3v-5z" class="m"></path><path d="M847 441c-1-4-2-7-2-12v-3h-1c1-1 2 0 4-1 0-1 0-1 1-1v-3l-1-1c1 0 2-1 3-3 0 2 0 3-1 4v2c-1 6-3 12-3 18z" class="r"></path><path d="M857 406c1 1 2 1 3 1-1 3-2 4-4 5-2 3-5 8-4 11v1l-4 19c-1-1-1-1-1-2 0-6 2-12 3-18v-2c1-1 1-2 1-4l2-7 1-1 3-3z" class="AD"></path><defs><linearGradient id="n" x1="852.876" y1="380.936" x2="853.236" y2="400.033" xlink:href="#B"><stop offset="0" stop-color="#292b2c"></stop><stop offset="1" stop-color="#4d4140"></stop></linearGradient></defs><path fill="url(#n)" d="M850 370l3 6c2 6 8 11 12 16l-2 1-3 3-1 1c0 1 0 2-1 3l-3 6v1c-1 1-1 1-1 2l-1 1h0v-4c1-1 1-2 1-3h1v-1-1c-2-1-1-2-2-4h0c1-2 2-4 3-5-1-1-1-2-1-2v-1c-1 0-2-1-2-1-2 0-3 0-5 1l-1-1-1 1c-1 2-2 5-3 7 0 1 0 2-1 3h0c0-1-1-2 0-3v-1l1-6c0-1 1-2 2-3h1l1-3c1 0 1-1 2-2-1-1-2-1-3-1h0l1-1h1c1 0 1 0 3-1 0-1 0-2-1-3v-5z"></path><path d="M846 389v-2c1 0 2 1 3 0h0c1-1 2-1 3-2l2 1v2h1v1c-1 0-2-1-2-1-2 0-3 0-5 1l-1-1-1 1z" class="I"></path><path d="M850 370l3 6c2 6 8 11 12 16l-2 1-3 3-1 1v-4l2-2c-1-1-1-2-2-3-3-2-6-7-9-8l-3-1h1c1 0 1 0 3-1 0-1 0-2-1-3v-5z" class="U"></path><path d="M846 380c1 0 2 0 3 1-1 1-1 2-2 2l-1 3h-1c-1 1-2 2-2 3l-1 6v1c-1 1 0 2 0 3 1 2 0 4 0 6l-1 12c0 3 0 7 1 10v7c1 4 2 9 4 13 0 1 1 2 1 3h0c0-2 1-3 1-4v9c-8-9-10-25-12-36l-1-12v-4c-1 1-2 4-2 6l-1-6c1-4 1-7 2-11 1 0 1-1 2-1 1-4 3-7 6-10l4-1z" class="N"></path><path d="M832 403c1-4 1-7 2-11 1 0 1-1 2-1l-1 16v-4c-1 1-2 4-2 6l-1-6z" class="r"></path><path d="M842 434c-1-3-1-6-2-8-2-10-2-19-1-29v-4c2-5 3-7 8-10l-1 3h-1c-1 1-2 2-2 3l-1 6v1c-1 1 0 2 0 3 1 2 0 4 0 6l-1 12c0 3 0 7 1 10v7z" class="G"></path><path d="M833 409c0-2 1-5 2-6v4l1 12c2 11 4 27 12 36 4 5 12 13 11 21 0 2-1 4-3 5v-5l-1-1c-1-4-6-10-10-12-1-1-1-1-2-1h-1c-2-1-5-1-7 0l-1 1v-1h-1v-1l1-1c1-1 2-2 4-3h1 0l-1-1-1-1c-1-1-2-2-4-3v-1c1-1 1-2 1-4v-3l1-1c0-1-1-2-1-3 0-3 0-5-1-8-1-8-1-15 0-23z" class="X"></path><path d="M839 448c1 1 1 1 0 2v1l-1-1c-1 0-1 0-1-1l-2 1 1-1h2l1-1z" class="M"></path><path d="M838 446l1 2-1 1h-2c0-1 0-2 1-2h0l1-1z" class="O"></path><path d="M842 454h2c4 2 9 9 10 14l-1-1c-2-2-4-3-6-5 0-1-1-1-1-2l-1 1c-2-1-2-2-3-3l1-1s0-1-1-1v-1-1z" class="W"></path><path d="M833 409c0-2 1-5 2-6v4l1 12v-1c-1-2-1-5-1-8-1 2-1 5-1 7 0 5 0 10 1 14 0 3 1 6 1 9l1 2c0 1 1 3 1 4l-1 1h0c-1 0-1 1-1 2l-1 1 2 5c-1-1-2-2-4-3v-1c1-1 1-2 1-4v-3l1-1c0-1-1-2-1-3 0-3 0-5-1-8-1-8-1-15 0-23z" class="c"></path><defs><linearGradient id="o" x1="822.849" y1="424.651" x2="838.064" y2="427.939" xlink:href="#B"><stop offset="0" stop-color="#0e0604"></stop><stop offset="1" stop-color="#1d201f"></stop></linearGradient></defs><path fill="url(#o)" d="M836 380h1 1v2c2 0 1 0 2-1h2c-3 3-5 6-6 10-1 0-1 1-2 1-1 4-1 7-2 11l1 6c-1 8-1 15 0 23 1 3 1 5 1 8 0 1 1 2 1 3l-1 1v3c0 2 0 3-1 4v1c2 1 3 2 4 3l1 1 1 1h0-1c-2 1-3 2-4 3l-1 1v1s0 1 1 1l-3 3v-1c-1 0-1-1-2-1-1 7 0 14-2 21v-2c0-2 0-3-1-5-1 1-1 2-1 3l1-12c-1-10 1-19 0-29l-3-33-1-3 1-4h0v-11l1-1h1l2 2h0v1s1 1 1 2v4l1-1 2-2v-1c2-3 3-6 3-9 2-1 2-2 2-4z"></path><path d="M832 452h1c2 1 3 2 4 3l1 1 1 1h0-1c-2 1-3 2-4 3l-1 1v1s0 1 1 1l-3 3v-1c0-2-1-5 1-6v-1c1 0 2-1 2-2s-1-1-1-2c0 0 0-1-1-2z" class="H"></path><path d="M836 380h1 1v2c2 0 1 0 2-1h2c-3 3-5 6-6 10-1 0-1 1-2 1-1 4-1 7-2 11-2 8-1 16-2 24-2-8 0-16 1-24 0-3 1-6 0-9v-1c2-3 3-6 3-9 2-1 2-2 2-4z" class="U"></path><path d="M832 403l1 6c-1 8-1 15 0 23 1 3 1 5 1 8 0 1 1 2 1 3l-1 1v3c0 2 0 3-1 4v1h-1v-10l-2-15c1-8 0-16 2-24z" class="F"></path><path d="M824 388h1l2 2h0v1s1 1 1 2v4l-1 9v17c1 7 2 13 2 19 1 5 1 11 0 17v5c-1 7 0 14-2 21v-2c0-2 0-3-1-5-1 1-1 2-1 3l1-12c-1-10 1-19 0-29l-3-33-1-3 1-4h0v-11l1-1z" class="t"></path><path d="M827 454c0-4-1-12 1-15 0 1 1 2 1 3 1 5 1 11 0 17v-3c-1-1-1 0-1-1v-1h-1z" class="AD"></path><path d="M827 454h1v1c0 1 0 0 1 1v3 5c-1 7 0 14-2 21v-2c0-2 0-3-1-5-1 1-1 2-1 3l1-12 1-15z" class="AC"></path><path d="M824 388h1l2 2h0v1s1 1 1 2v4l-1 9-1 3-1 1c-2-2-2-8-2-10h0v-11l1-1z" class="z"></path><path d="M824 388h1l2 2c-1 3-1 6-2 9h-1l-1-10 1-1z" class="AF"></path><defs><linearGradient id="p" x1="242.54" y1="432.464" x2="189.768" y2="473.853" xlink:href="#B"><stop offset="0" stop-color="#454549"></stop><stop offset="1" stop-color="#848384"></stop></linearGradient></defs><path fill="url(#p)" d="M210 394l3 1 1 1 3 3 2 2 2 2 3 2 1 1h-1l-3-3 2 6-1 1c0 2 2 2 3 3v6c1 1 1 0 1 1 0 2 1 3 1 4v1c0 1 0 2 1 3h0c0 2 2 3 3 5 0 2-1 2 1 4h3c1 2 1 4 0 6-1 1-1 1-2 1s-2 1-3 2h0l-2 2c-2 3-2 3-2 7l-1 6c0 3 1 5 2 7-2 3 0 7 0 10 1 2 1 3 2 4 1 0 1 1 1 2l3 3 2 1h-3c-2 2-2 5-1 7 0 4 2 7 4 10 1 1 1 2 2 2l1 1c-2 1-1 1-2 1-2-1-3 0-4 1-1 0-2 2-3 3v-2l-1-3c0-2-2-3-3-3-1-1 0-4-1-5 0-1-1-2-2-3h0c0-2-1-3-1-5-1-3-2-3-3-6v-2l-1 1-2-2c-1 0-1 0-2-1 0-1 0-2-1-2s-2 0-3 1v1l-1-1v2h-1c1 1 1 1 1 2l-2 1v4 2c1 0 1 0 1 1l-1 2c1 2 2 2 3 3v2 2h-1l-1-1h-1c1 2 0 3 0 5 1 1 2 1 2 3l-1 1 2 2v1l-1 1-2-1c0-1 0-2-1-3h0 0l-2-18v-17h-1l1-36c-1-2-1-3-1-5h-1v4l-1-8c0-1-1-2-1-3v-10-1c0-1 1-3 1-4h0l1 1c0-1 0-2 1-3l-2-2 1-1c-1-3-1-6-1-8v-2s0-1 1-2h1l3 3 1-2 1 1 3-3z"></path><path d="M210 394l3 1 1 1h-1c-1-1-2-1-2-1l-2 1v5h-1c-1 0-2 0-3-1v-1-1l1-2 1 1 3-3z" class="K"></path><path d="M229 511c0-3 1-5 3-7 1 1 2 3 5 3l1 1c-2 1-1 1-2 1-2-1-3 0-4 1-1 0-2 2-3 3v-2z" class="F"></path><path d="M224 455v-1c0-1 1-1 1-2v-1l1-1-1-1c-1 0-1 0-2-1h0l-1 2-1-2c0-1 1-2 1-3 1 1 1 1 2 1h1c1 0 3 1 3 2-2 3-2 3-2 7l-1 6c-1-2-1-4-1-6z" class="O"></path><path d="M203 475l-1-10h1c1 5-2 12 1 17v3c-1 1 0 1 0 2v5h1 1c1 0 1 0 1 1l-1 2c1 2 2 2 3 3v2 2h-1l-1-1h-1c1 2 0 3 0 5 1 1 2 1 2 3l-1 1 2 2v1l-1 1-2-1c0-1 0-2-1-3h0 0l-2-18v-17z" class="X"></path><path d="M233 487h-3v1h0c-1 0-2-1-3-1-1-2-2-3-4-4v-1-1h2c0-1-2-3-2-4h0l-1-3c-1-1 0-2 0-4h-1-1l1-1v-1-1-2l1-2c1 0 1 0 2-1v-1c0-2-1-4 0-6 0 2 0 4 1 6 0 3 1 5 2 7-2 3 0 7 0 10 1 2 1 3 2 4 1 0 1 1 1 2l3 3z" class="W"></path><path d="M202 395l3 3v1l-1 1c0 1 1 2 1 4v1l-2 24v5 5c-1-2-1-3-1-5h-1v4l-1-8c0-1-1-2-1-3v-10-1c0-1 1-3 1-4h0l1 1c0-1 0-2 1-3l-2-2 1-1c-1-3-1-6-1-8v-2s0-1 1-2h1z" class="a"></path><path d="M202 410v1c-1 5 0 10-2 14v5c0-1-1-2-1-3v-10-1c0-1 1-3 1-4h0l1 1c0-1 0-2 1-3z" class="X"></path><path d="M202 395l3 3v1l-1 1c0 1 1 2 1 4v1l-2-2h-1l-1 3c1 1 1 1 1 2v3-1l-2-2 1-1c-1-3-1-6-1-8v-2s0-1 1-2h1z" class="V"></path><path d="M202 395l3 3v1l-1 1-2-2h0l-2 1v-2s0-1 1-2h1z" class="X"></path><path d="M508 176v-2c1 0 2 0 2 1h1 0l1 2h1l2 1c0 2 0 2-1 3h0v2h2 0 0c-1 1-1 1-1 2h1v1 2h0c0 1-1 1 0 2 0 2 1 4 1 6l2 1v1c-1 2 0 4 0 7v-1l1-1c1-1 1-1 1-2h1l2 1h3v3l-2-1v2 1c2 1 3 0 5 1 3 1 5 3 8 5l-1 1h-1v3l2 1 1-1c-1 0-1-1-1-1l1-1h1l-2 7-7 13c-3 5-6 9-10 13-1 1-2 3-4 4h1c-1 1-1 2-2 3l-1 1h0l-1-1h-1v1c-1 2-1 3-3 3v1 5 3c-1 2-1 5-1 7-1 3 0 5-2 8v-13c0-5-2-9-4-13l-5-7-16-27h0l-5-11c-1-2-1-4-1-6h0l-1-2h-1l-1-1 1-1c1 0 2 0 3-1v-1l-1-1c-2 1-4 1-7 0h7 2 2c1 0 1-1 1-1 1 0 1 1 2 1 0 0 2-1 3-1 2 0 2 0 3-1l2 1c0 1 0 1-1 2h-2c-1 0-1 1-2 2l3 2h0v3 1l2-2c1-3-1-4 1-6 1-1 1-1 1-2h0v-1h0 3 1l1-1c0-1 0-2 1-3 0-1 0-1 1-2h1v-1-1c1-1 1-2 2-3l1 1v4c1 1 1 2 1 2 1-3 1-7 2-11l1-6z" class="G"></path><path d="M517 236c-1 0-1 0-2 1-1-1-1 0-1-1-1 0-1-1-2-1h-1v-1c1-1 0-1 1-1v-1h2c1 1 3 2 3 4z" class="B"></path><path d="M499 193c0-1 0-1 1-2l1 3-2 2 1 1 1 2c1 1 1 2 1 3-1-1-3-2-3-4v-1h-1l-1 1h-1l-2-1h-1 3 1l1-1c0-1 0-2 1-3z" class="U"></path><path d="M485 210h1v2c1 2 1 2 0 4 2 1 2 1 5 1v1h-1v1l-2 2-2-3c0-1-1-2-2-3v-1c0-2 0-3 1-4z" class="B"></path><path d="M514 250l1-2v-1c1-1 2-2 3-4h0 2l-2 3h0c2 2 2 2 3 2-1 1-2 3-4 4v-1-1l-2 1h0l-1-1z" class="k"></path><path d="M498 220c1 1 2 1 2 2s0 2 1 3v1s-1 1-1 2c-2 0-2 1-3 2v-1c-1-1-1-1-1-2v-1h0-2l-1-1h2l1-1c1-1 2-1 3-1-1-1-1-1-1-2v-1z" class="N"></path><path d="M510 259c1-2 1-4 2-7v-3h1v2l1-1 1 1h0l2-1v1 1h1c-1 1-1 2-2 3l-1 1h0l-1-1h-1v1c-1 2-1 3-3 3z" class="i"></path><path d="M476 206c1 0 2 0 3 1v1 1l2-1h0v1h1c0-1 0-1 1-1l1 1c-1 2-3 1-3 3v1c2 2 3 4 4 7-1 1-1 2-3 3l-5-11c-1-2-1-4-1-6z" class="f"></path><path d="M498 219v-1l1-1c1 0 2 0 3 1s1 3 2 5h0v5c1 0 1-1 2-1v2h-1-1c0 1-1 2-1 3-1-1-2-1-3-2-1 1-1 1-1 3l-1 1-1 1h0v2c-1-1-2-2-2-3h-1c1-1 2-1 3-1h0c0-1-1-1-2-1 1-1 1-1 2-1v-1c1-1 1-2 3-2 0-1 1-2 1-2v-1c-1-1-1-2-1-3s-1-1-2-2v-1z" class="B"></path><defs><linearGradient id="q" x1="479.797" y1="226.815" x2="505.21" y2="245.674" xlink:href="#B"><stop offset="0" stop-color="#c8cac9"></stop><stop offset="1" stop-color="#eeeaec"></stop></linearGradient></defs><path fill="url(#q)" d="M482 223c2-1 2-2 3-3 3 6 6 13 9 19 2 3 3 6 6 8 1-1 3-1 4-2h0 0c-1 2-1 3-1 4v1 2c-1 0-1 0-1-1-2-1-3-1-4-1l-16-27h0z"></path><path d="M498 219l-2 1 1 1c-1 0-1 1-1 1l-2-1 1-1-1-1 2-2c0-1 1-2 1-2 0-1-1-2-1-3l-2-2 1-1c-1-2 0-4 0-6l1-1h2v1c1 1 1 2 2 3 1 2 1 4 2 6s2 6 3 7l1 3v5c-1 0-1 1-2 1v-5h0c-1-2-1-4-2-5s-2-1-3-1l-1 1v1z" class="N"></path><path d="M503 249h2c1 1 3 3 3 5 1 1 1 3 2 5v1h0v5 3c-1 2-1 5-1 7-1 3 0 5-2 8v-13c0-5-2-9-4-13l-5-7c1 0 2 0 4 1 0 1 0 1 1 1v-2-1z" class="i"></path><path d="M503 249h2c-1 2-1 3 0 5l-2 2v1l-5-7c1 0 2 0 4 1 0 1 0 1 1 1v-2-1z" class="k"></path><path d="M503 186l1 1v4c1 1 1 2 1 2v3c1 3 2 9 2 12v4h1v-2h1v2c1 1 1 1 3 1-1 2-1 3-1 5v4l-2 2c1 1 1 2 2 3-1 1-1 2-2 3l-1 1v-7l-2-2-1-3s1 0 1-1c0-3 0-5-2-7h0v-2c1-1 1-1 1-2 0 0-1-2-2-2 0-1-1-3-1-3 0-1 0-2-1-3l-1-2-1-1 2-2-1-3h1v-1-1c1-1 1-2 2-3z" class="L"></path><path d="M500 197h1c1-2 0-4 2-5 1 1 0 3 1 5v1 1c0 1 0 2 1 3-1 1-1 2-2 3 0-1-1-3-1-3 0-1 0-2-1-3l-1-2z" class="R"></path><path d="M509 212c1 1 1 1 3 1-1 2-1 3-1 5v4l-2 2c1 1 1 2 2 3-1 1-1 2-2 3l-1 1v-7c0-2-1-7 0-9 1-1 0-2 1-3z" class="o"></path><path d="M519 205v-1l1-1c1-1 1-1 1-2h1l2 1h3v3l-2-1v2h-1-1v1c1 1 1 2 1 3s-1 2-2 3c1 2 2 3 3 4v1c-2-1-3-1-4-2l-1 1c1 0 2 1 2 2h-1c0 1-1 1-1 2l1 1c-1 0-1 0-2-1v1l2 2c1 1 3 0 4 2l-1 1v1l-1-1-1-1-2 1v-1l-1 1v2l-1 1 1 1c1 1 1 2 1 3l3 3c-1 1-1 1-1 2l-1-1c0-2-1-2-2-2-2 1-1 4-1 6-2 0-2 1-3 2 0 1-1 1-1 1h-1c-2-3 1 1-1-2h-1l-2 2v2l-1 1v-1l-2-1h0l-1-1c2 0 3 0 4-1v-1h-4c2-1 3-2 3-4h1 1c0 2 0 2 1 2 2-1 3-2 5-3 1 0 1-1 1-2 0-2-2-3-3-4 1 1 3 1 4 1v1l1-1v-1l-3-3c-2 0-1 0-2-1-1 1-2 2-4 3l-1-1c1-1 1-2 2-3l8-22z" class="q"></path><path d="M508 176v-2c1 0 2 0 2 1h1 0l1 2h1l2 1c0 2 0 2-1 3h0v2h2 0 0c-1 1-1 1-1 2h1v1 2h0c0 1-1 1 0 2 0 2 1 4 1 6l2 1v1c-1 2 0 4 0 7l-8 22c-1-1-1-2-2-3l2-2v-4c0-2 0-3 1-5-2 0-2 0-3-1v-2h-1v2h-1v-4c0-3-1-9-2-12v-3c1-3 1-7 2-11l1-6z" class="Y"></path><path d="M512 187h0c1 2 1 2 2 3v1c0 1 0 2 1 4h-1v3 2l2 2c-1 4-3 8-3 11v2l-2 3c0-2 0-3 1-5v-2c-1-4 0-8 0-12v-12z" class="e"></path><path d="M508 176v-2c1 0 2 0 2 1h1l1 12v12c0 4-1 8 0 12v2c-2 0-2 0-3-1v-2h-1v2h-1v-4c0-3-1-9-2-12v-3c1-3 1-7 2-11l1-6z" class="i"></path><path d="M507 182v15 1l1 1c1 0 1 1 2 2v4 3l-1 1-2-1c0-3-1-9-2-12v-3c1-3 1-7 2-11z" class="o"></path><path d="M522 239c0-1 0-1 1-2l-3-3c0-1 0-2-1-3l-1-1 1-1v-2l1-1v1l2-1 1 1 1 1v-1l1-1c-1-2-3-1-4-2l-2-2v-1c1 1 1 1 2 1l-1-1c0-1 1-1 1-2h1c0-1-1-2-2-2l1-1c1 1 2 1 4 2v-1c-1-1-2-2-3-4 1-1 2-2 2-3s0-2-1-3v-1h1 1v1c2 1 3 0 5 1 3 1 5 3 8 5l-1 1h-1v3l2 1 1-1c-1 0-1-1-1-1l1-1h1l-2 7-7 13c-3 5-6 9-10 13-1 0-1 0-3-2h0l2-3 2-2v-2z" class="E"></path><path d="M528 233l3 2c-3 5-6 9-10 13-1 0-1 0-3-2 5-3 8-8 10-13z" class="Q"></path><path d="M536 217l2 1 1-1c-1 0-1-1-1-1l1-1h1l-2 7-7 13-3-2c3-5 6-11 8-16z" class="d"></path><path d="M349 148l17-7c11-4 21-7 33-8h14c3 1 6 2 9 2-5 0-10 0-15 1-13 2-26 8-35 16 0 3 0 4 1 6 2 3 5 4 8 6-2 1-5 0-8 0h-4l-6 4-5 4c-5 6-7 13-6 21 1 7 3 11 6 17l1 1 2 4c-2 0-2 0-4-1l-1 1c-1 0-2-1-3-1l-10-9c-1-1-3-2-4-3l-4-2c-5-4-10-8-15-10v-1c1 1 2 1 3 0 0-1-1-2-2-2l1-2c-1-1-1-2-1-4 1-2 2-4 3-5 0-1-1-1-2-2h0c-2 0-3 0-4 1l-2-1c1 0 2-1 3-2-2 1-5 1-7 2h0-6-7l-1 1c-1 0-2-1-3-1-1-1-1-2-1-3-3 2-5 3-9 3-5-1-8-2-12-6h0 1 1c1 2 3 2 5 3h1c3 1 6 0 9-1 2 0 4 0 6-1h-1c1-2 4-3 6-4l-1-1 2-1c6-5 10-10 15-16h1v-1c1 1 1 2 2 3h3v1l1 1v1h1c1 1 2 1 3 2 4 2 8-1 12 1 2 0 4-3 6-3 1-1 2-2 3-4z" class="i"></path><path d="M317 147h1v-1c1 1 1 2 2 3h3v1l1 1v1h1c1 1 2 1 3 2 4 2 8-1 12 1l-4 2c2 1 4 0 6-1v1l-4 1-4 1c-8 4-18 12-27 12-4 1-9-1-13 0-3 2-5 3-9 3-5-1-8-2-12-6h0 1 1c1 2 3 2 5 3h1c3 1 6 0 9-1 2 0 4 0 6-1h-1c1-2 4-3 6-4l-1-1 2-1c6-5 10-10 15-16z" class="e"></path><path d="M301 165c2 0 4 0 7-1l2 1c-1 1-1 2-2 2-2 1-3 1-5 1-1 0-3 0-4 1h-3-1c1-2 4-3 6-4z" class="f"></path><path d="M299 169c2 1 4 1 7 1l1-1c5-1 9-4 13-7 3-1 5-4 8-4 2-1 5-1 6 0h-1l1 1c-8 4-18 12-27 12-4 1-9-1-13 0-3 2-5 3-9 3-5-1-8-2-12-6h0 1 1c1 2 3 2 5 3h1c3 1 6 0 9-1 2 0 4 0 6-1h3z" class="W"></path><path d="M358 157c2-2 11-4 14-5 0 3 0 4 1 6 2 3 5 4 8 6-2 1-5 0-8 0h-4l-6 4-5 4-1-3-2 1c-1 1 0 1-1 3-2 1-2 1-3 1l-2-1h-1v2h-2c-2 1-3 1-4 0h-2l-1 1h0v-1l1-1v-1h-6c-4 2-8 5-11 7l-1 2-1-1c1-2 2-4 3-5 0-1-1-1-2-2h0c-2 0-3 0-4 1l-2-1c1 0 2-1 3-2 2 0 4-2 6-3 3-2 7-4 10-5l17-5 6-1v-1z" class="Z"></path><path d="M358 157c2-2 11-4 14-5 0 3 0 4 1 6-2 0-3-1-4-1h0c-2 1-8 0-11 0z" class="e"></path><path d="M322 174c6-6 16-10 24-12 1 0 2 0 3-1 1 0 1 0 2 1v1c-3 1-7 2-10 4-6 2-12 5-17 9 0-1-1-1-2-2z" class="i"></path><path d="M351 163c5-1 9-1 14-1 3 0 5 1 8 2h-4l-6 4-5 4-1-3-2 1c-1 1 0 1-1 3-2 1-2 1-3 1l-2-1h-1v2h-2c-2 1-3 1-4 0h-2l-1 1h0v-1l1-1v-1h-6c-4 2-8 5-11 7l-1 2-1-1c1-2 2-4 3-5 5-4 11-7 17-9 3-2 7-3 10-4z" class="V"></path><path d="M365 162c3 0 5 1 8 2h-4c-5 0-10 1-15 1 3-2 7-1 11-3z" class="C"></path><g class="W"><path d="M340 174h3c1-1 0-1 1-3 1 0 2 0 3-1h3l-1 1 2 1v1h-2-1v2h-2c-2 1-3 1-4 0h-2l-1 1h0v-1l1-1z"></path><path d="M350 170v-1c3-1 7-2 10-2 1 0 2 1 3 1l-5 4-1-3-2 1c-1 1 0 1-1 3-2 1-2 1-3 1l-2-1h2v-1l-2-1 1-1z"></path></g><path d="M350 170l1-1c1 1 2 3 3 4-2 1-2 1-3 1l-2-1h2v-1l-2-1 1-1z" class="j"></path><path d="M351 163c5-1 9-1 14-1-4 2-8 1-11 3l-10 3c-2 0-3 1-4 1h-2l3-2c3-2 7-3 10-4z" class="D"></path><path d="M341 167l-3 2h2c1 0 2-1 4-1-4 2-7 3-10 5-4 2-8 5-11 7l-1 2-1-1c1-2 2-4 3-5 5-4 11-7 17-9z" class="H"></path><defs><linearGradient id="r" x1="337.293" y1="175.196" x2="366.321" y2="200.164" xlink:href="#B"><stop offset="0" stop-color="#a09e9e"></stop><stop offset="1" stop-color="#d2d2d1"></stop></linearGradient></defs><path fill="url(#r)" d="M354 173c1-2 0-2 1-3l2-1 1 3c-5 6-7 13-6 21 1 7 3 11 6 17l1 1 2 4c-2 0-2 0-4-1l-1 1c-1 0-2-1-3-1l-10-9c-1-1-3-2-4-3l-4-2c-5-4-10-8-15-10v-1c1 1 2 1 3 0 0-1-1-2-2-2l1-2c-1-1-1-2-1-4l1 1 1-2c3-2 7-5 11-7h6v1l-1 1v1h0l1-1h2c1 1 2 1 4 0h2v-2h1l2 1c1 0 1 0 3-1z"></path><path d="M341 185l3-2v3c0 1 0 1 1 2h-1l-1-1-2-2z" class="Z"></path><path d="M328 191c-1-2-1-3-1-5 1-1 1-2 3-3-1 3-3 2-1 5l2 3-1 1h1v1c-1 0-2-1-3-2z" class="g"></path><path d="M353 210h1c1 0 1 1 2 2 1-1 2-1 2-2l1 1 2 4c-2 0-2 0-4-1l-4-4z" class="k"></path><path d="M339 176l1-1h2c1 1 2 1 4 0v2c1 0 1 0 1 2-1 1-3 0-4 1h-2-2v-4z" class="n"></path><defs><linearGradient id="s" x1="323.039" y1="200.246" x2="354.509" y2="201.733" xlink:href="#B"><stop offset="0" stop-color="#cdd0cb"></stop><stop offset="1" stop-color="#faf6f9"></stop></linearGradient></defs><path fill="url(#s)" d="M321 187l1-2c2 3 4 4 6 6 1 1 2 2 3 2l22 17 4 4-1 1c-1 0-2-1-3-1l-10-9c-1-1-3-2-4-3l-4-2c-5-4-10-8-15-10v-1c1 1 2 1 3 0 0-1-1-2-2-2z"></path><defs><linearGradient id="t" x1="334.741" y1="174.426" x2="338.026" y2="185.993" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#a29e9e"></stop></linearGradient></defs><path fill="url(#t)" d="M334 173h6v1l-1 1v1h0v4h2 2l2 2-1 1-3 2c-1 1-2 1-3 2-1 0-2-2-3-3v-1h0c-1 0-2 0-3 1-1-1-1-1-2-1-2 1-2 2-3 3 0 2 0 3 1 5-2-2-4-3-6-6-1-1-1-2-1-4l1 1 1-2c3-2 7-5 11-7z"></path><path d="M323 180h5v2h0 2v1c-2 1-2 2-3 3 0 2 0 3 1 5-2-2-4-3-6-6-1-1-1-2-1-4l1 1 1-2z" class="W"></path><defs><linearGradient id="u" x1="826.825" y1="339.876" x2="873.441" y2="350.171" xlink:href="#B"><stop offset="0" stop-color="#1f1e1e"></stop><stop offset="1" stop-color="#454446"></stop></linearGradient></defs><path fill="url(#u)" d="M842 306c1 0 3-1 4-1 2-1 8-1 10-1 1 1 0 1 1 1h1c1 0 2 0 4 1h0c1 0 1 0 2 1l2 1c6 3 10 10 14 14 1 1 3 3 3 4l3 4c-7-1-14-5-22-6-4 1-7 2-9 6-2 2-3 5-4 7-3 11-5 22-1 33v5c1 1 1 2 1 3-2 1-2 1-3 1h-1l-1 1h0l-4 1h-2c-1 1 0 1-2 1v-2h-1-1c0 2 0 3-2 4 0 3-1 6-3 9v1l-2 2-1 1v-4c0-1-1-2-1-2v-1h0l-2-2h-1l-1 1v11h0l-1 4v-5c-1-1-1-2-2-3v-1-1l-1-1h-1v1c0 1-1 2-2 3l-1 6v-7l-1-1c1-2 1-5 3-7v-1l-1-2h-4-1c0-1 1-1 1-2 1-3 4-7 5-10v-1c0-2 1-5 1-8-1-2 0-4 0-6v-4-1l1-1-1-1-1-2v-8l-1-2v-1-3l-1-1 1-1c1-1 1-2 2-4v1l2 2v-1h0l1 3v-3c-1-1 0-3 0-4-1-1-1-1-1-2-1-2 0-4 0-5l-1-5c1 1 1 1 1 3l2 1c1 1 1 3 2 4l1 2c1 2 1 3 1 5 0-3 0-5-1-8h2c2-1 4-3 5-6h0c1-1 2-2 3-2 2-3 4-5 7-8z"></path><path d="M870 318c4 0 5 2 7 4h-1c-2 0-4-2-6-4z" class="K"></path><path d="M858 305c1 0 2 0 4 1h0c1 0 1 0 2 1l2 1c6 3 10 10 14 14l-2 1-1-1c-2-2-3-4-7-4-1-1-3-3-5-3 0-1-1-1-1-1 1 0 2 0 4 1 1 1 3 1 5 1v1c1 2 4 3 5 5l1-1c-1-1-3-1-3-2-2-3-5-6-7-8s-8-5-11-5h-1l1-1z" class="G"></path><path d="M864 324l-2-4 1-1 1 1h0c0 1 0 1 1 1 0 0 1 1 2 1 5 1 10 4 15 5l-4-4 2-1c1 1 3 3 3 4l3 4c-7-1-14-5-22-6z" class="I"></path><path d="M864 314h-3l-1-2c-1 0-1 0-2-1h1l-2-2-2 1v-2c-1 0-3 0-4 1l-1-1h-2c0-1 0-1 1-1 1-1 8-1 10 0 0 0 1 0 1 1l1 1h1c2 0 3 2 5 3h0c1 0 2 1 3 2 1 0 2 1 3 2-2 0-4 0-5-1-2-1-3-1-4-1z" class="H"></path><path d="M829 360l1 1c0 2 1 7 0 9v1 4h-1v2l1 1c0 1 1 2 1 4h2v-3h0v-4-1 1c-1 0-1 0-1 1-1-1-1-1-1-2l1-1c0-1 1-2 1-3v-3l1-1c1-2 0-2 1-4 0 1 0 3 1 4v1c-1 4-2 10-1 14 0 1 0 2-1 3 0 3-1 6-3 9v1l-2 2-1 1v-4c0-1-1-2-1-2v-1h0l-2-2c1-2 1-2 2-3s1-2 1-3c1-1 1-1 1-3v-19z" class="l"></path><path d="M825 388c1-2 1-2 2-3l1 1h1c2 2 2 2 2 5v1 1 1l-2 2-1 1v-4c0-1-1-2-1-2v-1h0l-2-2z" class="E"></path><path d="M825 388c1-2 1-2 2-3l1 1-1 4h0l-2-2z" class="x"></path><path d="M832 316h0c1-1 2-2 3-2l-5 10c-1 1-1 1-1 2 0 2 1 4 1 7h0v14c0 1 0 1-1 3v1c-1-3-2-6-2-9 0-1 0 0-1-1 0-2-1-4-1-6-1-3-1-6-3-9l-2-6-1-5c1 1 1 1 1 3l2 1c1 1 1 3 2 4l1 2c1 2 1 3 1 5 0-3 0-5-1-8h2c2-1 4-3 5-6z" class="D"></path><path d="M827 334c0-3 0-5 2-8 0 2 1 4 1 7h0l-3 1z" class="t"></path><path d="M827 334l3-1v14c-1-2-1-3-2-4v-3c0-1-1-1 0-2 0-2 0-3-1-4h0z" class="w"></path><path d="M836 366v-4c1-1 1-2 2-3-1-2-1-4-2-6l-1-1c1 0 1-1 1-1 0-2 0-3-1-5 0-3 0-8 1-11v1-2c1-1 1-1 1-2l3-3v1c0 1-1 2-2 3v1 1 1-2h1c0 1 1 1 0 2-2 4-2 6-1 10h0v-3h1c1 1 0 3 1 5l1 1v1h-1v-1 3h0c0 2-1 5 0 7v7c-1 3-3 6-3 10 0 1 0 2-1 3v1h0c0 2 0 3-2 4 1-1 1-2 1-3-1-4 0-10 1-14v-1z" class="C"></path><path d="M840 352h1v-2l1-1c0-3 1-6 0-9h0v-2l1-1-1-1c0-1 1-1 1-2l1 1 1-1c0-2 0-1 2-2 0 1 1 2 2 2h1v-1c1-2 0-2 0-4 1 1 1 2 1 2v2 1c-1 1 0 1 0 3-3 11-5 22-1 33v5c1 1 1 2 1 3-2 1-2 1-3 1h-1l-1 1h0l-4 1h-2c-1 1 0 1-2 1v-2h-1-1 0v-1c1-1 1-2 1-3 0-4 2-7 3-10v-7c-1-2 0-5 0-7z" class="L"></path><path d="M846 380c-1-1-2-1-2-2 1-1 1-2 2-2 1 1 2 2 2 3h-1l-1 1zm1-41h1v5c-1 1-1 3-1 5v1c-1 1-1 2-1 3v4 1h-1v2 2l-1 1v1 2l-1 1v2h-1c0 3-2 5-3 7h-1l2-6 1-1c1-3 1-8 1-11 1-2 1-3 1-5s0-1 1-1c1-1 1-4 1-5v-2c1-1 1-2 1-3s1-2 1-3z" class="O"></path><path d="M820 320l2 6c2 3 2 6 3 9 0 2 1 4 1 6 1 1 1 0 1 1 0 3 1 6 2 9v9 19c0 2 0 2-1 3 0 1 0 2-1 3s-1 1-2 3h-1l-1 1v11h0l-1 4v-5c-1-1-1-2-2-3v-1-1l-1-1h-1v1c0 1-1 2-2 3l-1 6v-7l-1-1c1-2 1-5 3-7v-1l-1-2h-4-1c0-1 1-1 1-2 1-3 4-7 5-10v-1c0-2 1-5 1-8-1-2 0-4 0-6v-4-1l1-1-1-1-1-2v-8l-1-2v-1-3l-1-1 1-1c1-1 1-2 2-4v1l2 2v-1h0l1 3v-3c-1-1 0-3 0-4-1-1-1-1-1-2-1-2 0-4 0-5z" class="W"></path><path d="M826 341c1 1 1 0 1 1 0 3 1 6 2 9v9 19h-1v-9l-2-29z" class="t"></path><path d="M823 369c-1-4-1-10-1-13 1-1 1-2 1-3-1-1-1-2-1-3v-3c1-1 1-1 2-1 1 4 1 8 1 12v6 2c0-1-1-3-2-4v7z" class="Y"></path><path d="M823 362c0-2 0-2 1-3 1 1 1 3 1 5v2c0-1-1-3-2-4z" class="M"></path><path d="M823 362c1 1 2 3 2 4h0c1 2 1 4 1 6h1l1-2v9h1c0 2 0 2-1 3 0 1 0 2-1 3s-1 1-2 3h-1l-1 1v11h0l-1 4v-5c-1-1-1-2-2-3v-1-1l-1-1h-1v1c0 1-1 2-2 3l-1 6v-7l-1-1c1-2 1-5 3-7v-1l-1-2h0c2 0 3-1 5-2 1 0 1-1 1-2 2-3 0-8 1-12v-7z" class="O"></path><path d="M816 397c0-2 1-2 0-3 1-2 1-3 2-4h1l1 1v3l-1-1h-1v1c0 1-1 2-2 3z" class="Y"></path><path d="M820 391l2 1c0 2 0 6 1 8h0l-1 4v-5c-1-1-1-2-2-3v-1-1-3z" class="M"></path><path d="M825 366c1 2 1 4 1 6h1v6c-1 1-1 2-2 2v-14z" class="X"></path><path d="M827 372l1-2v9h1c0 2 0 2-1 3 0 1 0 2-1 3s-1 1-2 3h-1c0-3 0-5 1-8 1 0 1-1 2-2v-6z" class="y"></path><defs><linearGradient id="v" x1="828.473" y1="351.73" x2="808.76" y2="359.788" xlink:href="#B"><stop offset="0" stop-color="#929092"></stop><stop offset="1" stop-color="#b5b5b4"></stop></linearGradient></defs><path fill="url(#v)" d="M817 341l-1-2v-1-3l-1-1 1-1c1-1 1-2 2-4v1l2 2v-1h0l1 3h0v3c-1 1-1 2-1 4l-1 1c1 1 2 0 3 1l-2 2 1 2-1 2h0c1 2 1 4 0 6 1 1 1 1 1 2-1 1 0 1 0 2 1 4-1 10 0 14 0 3 1 6 1 8 0 1 0 2-1 2-2 1-3 2-5 2h0-4-1c0-1 1-1 1-2 1-3 4-7 5-10v-1c0-2 1-5 1-8-1-2 0-4 0-6v-4-1l1-1-1-1-1-2v-8z"></path><path d="M816 385l-2-2c1-1 1-1 1-2 1-1 1-1 2-1 0 0 1 0 2 1 1 0 1 1 2 2-2 1-3 2-5 2z" class="Q"></path><defs><linearGradient id="w" x1="194.576" y1="280.051" x2="168.427" y2="275.321" xlink:href="#B"><stop offset="0" stop-color="#363639"></stop><stop offset="1" stop-color="#5a595d"></stop></linearGradient></defs><path fill="url(#w)" d="M195 231c1-1 2-2 4-3l1 1 1 3 5 8c4 5 7 13 12 16l1 1 1 1c0 1 1 1 1 2 1 1 1 1 1 3h1c0 3-1 8-2 10l-1 3-1 4c-1 3-3 6-4 9-2 4-4 8-6 13l-1 3v-5l-4 11-3 3c-1 4-4 8-6 12v-1h0c-3-3-4-5-6-9l3 3 1 1c1-1 2-1 3-2l-1-2s1-1 1-2h-1l-4-1c-3-1-4-3-6-4-1-2-2-3-4-4l-2-2h0c-1 0-1-1-2-1h-1v-2h0c-1-1-1-1-1-2l-2-2h-2-3l-1 1c-1 0-1 0-2 1v-3l-1 3h-1c-1-3 0-6 1-9v-2-4c0-1 1-3 2-4l4-12c1-6 4-12 7-17 1-2 2-2 2-4 1-1 2-1 3-1h1l3-4c2-2 3-3 6-5 1-1 2-2 3-5z"></path><path d="M164 289c1-2 1-4 2-7h0 1c-1 3-3 10-2 13l-1 3h-1c-1-3 0-6 1-9z" class="V"></path><path d="M185 309c1-1 2-1 3-1h2v-3-1l1-1 1-1v-6h0c1 3 1 7-1 10-1 2 0 4 0 6v1c-3-1-4-3-6-4z" class="G"></path><path d="M192 296l1-1v-2l-1-2c-1-1-1-2-1-3 1-1 1-1 0-2v-1h0l3 2v3c0 5 0 9-1 14 0 3 0 5-1 7v2h2c1 0 1 1 1 1l-4-1v-1c0-2-1-4 0-6 2-3 2-7 1-10z" class="D"></path><path d="M170 267c1-1 2-3 3-4l1-1v1h0v1c-2 2-2 5-3 7l-4 11h-1 0c-1 3-1 5-2 7v-2-4c0-1 1-3 2-4l4-12z" class="Y"></path><path d="M192 236c-1 4-6 5-6 9l1 1 1-1c0 3 2 6 2 8l1 4c-1 3 0 10 1 13 1 2 0 2 0 4l1 2h-1c1 3 1 4 1 7l1 2-3-2-3 3h0v-1c1-1 2-2 2-3s-2-4-2-5h-2c-1-2 1-2 1-4s0-2-1-3v-2-5c0-1 1-2 2-2 0-1 1-6 1-7 0-2-2-2-2-4v-1h-1c-1 1-2 2-2 3v1c-1 0-5 4-6 6 0 1 1 0 0 2-1-1 0-2-1-3-1 1-2 5-2 7l-1-1v-1h0v-1l-1 1c-1 1-2 3-3 4 1-6 4-12 7-17 1-2 2-2 2-4 1-1 2-1 3-1h1l3-4c2-2 3-3 6-5z" class="C"></path><g class="D"><path d="M191 283v-3-5 2l1-1c1 3 1 4 1 7l1 2-3-2z"></path><path d="M190 253l1 4c-1 3 0 10 1 13 1 2 0 2 0 4l1 2-2-1c-1-1 0-4 0-5-2-3-1-7-1-10v-7z"></path></g><path d="M179 246c1-1 2-1 3-1l-5 12-3 6h0v-1l-1 1c-1 1-2 3-3 4 1-6 4-12 7-17 1-2 2-2 2-4z" class="O"></path><path d="M188 245v-1c0-1 1-2 2-2h1c2 1 1 3 1 5 1 2 1 4 2 7l4 19v1c0 3 1 7 0 11 0 5 0 11-1 16v1c0 3 0 6-2 8l-3 1c1-2 1-4 1-7 1-5 1-9 1-14v-3-2l-1-2c0-3 0-4-1-7h1l-1-2c0-2 1-2 0-4-1-3-2-10-1-13l-1-4c0-2-2-5-2-8z" class="N"></path><path d="M191 257c3 10 4 20 4 31 1 7 1 14-1 21v1c1-1 2-3 2-4v-1l1-4v1c0 3 0 6-2 8l-3 1c1-2 1-4 1-7 1-5 1-9 1-14v-3-2l-1-2c0-3 0-4-1-7h1l-1-2c0-2 1-2 0-4-1-3-2-10-1-13z" class="E"></path><path d="M195 231c1-1 2-2 4-3l1 1 1 3 5 8c4 5 7 13 12 16l1 1 1 1c0 1 1 1 1 2 1 1 1 1 1 3h1c0 3-1 8-2 10l-1 3-1 4c-1 3-3 6-4 9-2 4-4 8-6 13l-1 3v-5l-4 11-3 3c-1 4-4 8-6 12v-1h0c-3-3-4-5-6-9l3 3 1 1c1-1 2-1 3-2l-1-2s1-1 1-2h-1s0-1-1-1h-2v-2l3-1c2-2 2-5 2-8v-1c1-5 1-11 1-16 1-4 0-8 0-11v-1l-4-19c-1-3-1-5-2-7 0-2 1-4-1-5h-1c-1 0-2 1-2 2v1l-1 1-1-1c0-4 5-5 6-9 1-1 2-2 3-5z" class="N"></path><path d="M196 314l1 1v1c1 1 1 1 1 2l-2 4h0c1-1 1-1 1-2l1-1 1-1c0-1 1-2 2-4-1 4-4 8-6 12v-1h0c-3-3-4-5-6-9l3 3 1 1c1-1 2-1 3-2l-1-2s1-1 1-2z" class="E"></path><path d="M196 314l1 1c0 3-1 4-2 7h-1l-1-2c1-1 2-1 3-2l-1-2s1-1 1-2z" class="I"></path><path d="M212 271v-6h1c0-1 0-1 1-1s1 2 2 4l2 11 1 1c-1 3-3 6-4 9-2 4-4 8-6 13l-1 3v-5c2-5 4-11 4-16 1-4 0-9 0-13z" class="V"></path><path d="M215 271v1c2 5 2 10 0 15h-1v-14c0-1 0-1 1-2z" class="g"></path><path d="M200 246c1 2 2 3 3 4 0 1 1 2 2 3v3c0 1 0 1 1 1 1 3 1 5 1 8 1 7 2 16 0 22l-6 15-1-1-1-1-2 2v-1c1-5 1-11 1-16l3 9 3-9v-1l1-2c1-1 1-4 0-5v-2l1-1v-4h0l-1-1c1-2 1-4 0-5v-2c0-1 0-3-1-5h-1l1-1c-1-1-1-2-2-4 0-1 0-1-1-2-1-2-1-2-1-4z" class="K"></path><path d="M195 231c1-1 2-2 4-3l1 1 1 3 5 8c0 4 3 7 3 11l-3-5c-1-2-3-5-6-5-2 0-3 0-4 1l1 1c-2 0-2 0-3 1-2 3 0 6 0 10-1-3-1-5-2-7 0-2 1-4-1-5h-1c-1 0-2 1-2 2v1l-1 1-1-1c0-4 5-5 6-9 1-1 2-2 3-5z" class="G"></path><path d="M206 240c4 5 7 13 12 16l1 1 1 1c0 1 1 1 1 2 1 1 1 1 1 3h1c0 3-1 8-2 10l-1 3-1 4-1-1-2-11c-1-2-1-4-2-4s-1 0-1 1h-1v6c-1-4 0-9-1-13 0-3-1-5-2-7 0-4-3-7-3-11z" class="T"></path><path d="M220 276c-2-3-2-6-3-9 0-2-1-4-1-6v-2c-1 0-1-1-1-2l1-1c1 1 1 1 1 2 1 0 1 1 1 1l1 1c3 4 2 9 2 13l-1 3z" class="J"></path><path d="M194 254c0-4-2-7 0-10 1-1 1-1 3-1l3 3c0 2 0 2 1 4 1 1 1 1 1 2 1 2 1 3 2 4l-1 1h1c1 2 1 4 1 5v2c1 1 1 3 0 5l1 1h0v4l-1 1v2c1 1 1 4 0 5l-1 2v1l-3 9-3-9c1-4 0-8 0-11v-1l-4-19z" class="L"></path><path d="M200 250c1 2 2 3 2 5l1 1-1 1c1 0 1 0 1 1 0 2 0 3 1 4v5 3c1 2 0 8-1 9l-1 2c-1 2 1 2-1 4-1-1-1-1-1-2 0-4-1-6-2-9v-1-6-5c0 2 0 6 1 9 1 1 1 2 1 4v2c1 0 1 0 2-1v-5-1-4h-1c1-4 1-8 0-11-1-2-1-3-1-5z" class="M"></path><path d="M198 262c-2-3-1-7-2-10l-1-1c-1-2 0-4 0-6h2v1l2 4h1c0 2 0 3 1 5 1 3 1 7 0 11h1v4 1 5c-1 1-1 1-2 1v-2c0-2 0-3-1-4-1-3-1-7-1-9z" class="b"></path><path d="M199 250h1c0 2 0 3 1 5 1 3 1 7 0 11l-1-5v-2c-1-2-1-4-1-6 1-2 0-2 0-3z" class="X"></path><defs><linearGradient id="x" x1="718.261" y1="131.234" x2="789.829" y2="114.393" xlink:href="#B"><stop offset="0" stop-color="#b5b5b5"></stop><stop offset="1" stop-color="#e8e8e7"></stop></linearGradient></defs><path fill="url(#x)" d="M722 133c2 0 5 1 8 0 4 0 9-3 13-5 9-6 18-13 25-22 6-7 11-16 11-26 0-5-1-9-1-13 6 10 6 20 5 31l1 1v-3h0c0 1 0 1 1 2 1 2 1 6 1 8v17c0 3 0 6 1 8s3 3 4 5l2 1-1 1h-1-4c-1 0-2 1-2 1l-2 2c-1 1-1 1-1 2-1 2-2 3-3 4 0 2-1 4-1 5 0 4 2 7 3 10 0 0 1 1 1 2 1 2 1 3 1 4h-1c-5-3-9-9-10-15 0 1-1 2-1 3-1 1-1 1-2 1l-1 1-2 6c-2 2-3 5-5 7-1 0-3 1-4 1 0-1 1-2 2-3h-1c-1 1-3 3-5 4-1 1-2 1-3 1-2 0-4 0-6 1v-3c-4-1-6-3-9-5h0c2 0 3 1 5-1-3-1-6-2-9-4-11-8-18-24-20-37h1 0l6 6 4 2z"></path><path d="M760 155h0c-1 2-1 3-3 4-2 2-5 4-7 6 1 0 2 0 3-1h0l4-2h0v1 1h3c1 1 1 1 1 2 1 0 1 1 1 0 1-1 2-2 4-2-2 2-3 5-5 7-1 0-3 1-4 1 0-1 1-2 2-3h-1c-1 1-3 3-5 4-1 1-2 1-3 1-2 0-4 0-6 1v-3c-4-1-6-3-9-5h0c2 0 3 1 5-1 8 0 13-4 18-9l2-2z" class="Q"></path><path d="M744 169h1c2-1 4-1 6-2 1-1 2-1 3-1s2-1 3-1h1v1l-3 3h-1c-3-2-6 0-10 0z" class="d"></path><path d="M735 167c4 2 6 2 9 2 4 0 7-2 10 0-2 1-4 2-7 2l1 1c2 1 3 1 5 1-1 1-2 1-3 1-2 0-4 0-6 1v-3c-4-1-6-3-9-5z" class="Z"></path><path d="M783 98l1 1v-3h0c0 1 0 1 1 2 1 2 1 6 1 8v17c0 3 0 6 1 8s3 3 4 5l2 1-1 1h-1-4c-1 0-2 1-2 1l-2 2c-1 1-1 1-1 2-1 2-2 3-3 4 0 2-1 4-1 5 0 4 2 7 3 10 0 0 1 1 1 2 1 2 1 3 1 4h-1c-5-3-9-9-10-15-2-7 1-16 3-24l5-18c1-4 2-9 3-13z" class="H"></path><path d="M779 147c0-4 2-6 4-10 1 0 2-1 3-2 1 0 4 1 5 1l2 1-1 1h-1-4c-1 0-2 1-2 1l-2 2c-1 1-1 1-1 2-1 2-2 3-3 4z" class="I"></path><path d="M711 125h1 0l6 6 4 2c-2 0-2 0-3 2 0 2 1 3 2 5 1 1 3 1 3 1 0 1-1 1 1 2 0 1 1 2 1 3 1 1 3 3 5 4h1c2 0 2 1 3 2h0c1-1 2-1 3-1l1 1c1-1 2-4 4-4 0 1 0 1 1 1 2 0 2-1 4-2h2c0-1 1-2 2-2l2 2c1-1 2-3 3-2l1 1 1-1c0-1 0-2-1-3 2-1 3 0 5-2 1 1 2 2 4 2h0c0-2 1-2 1-3l1-2c0 2-1 5-2 6 0 1-3 6-4 7l-3 5-2 2c-5 5-10 9-18 9-3-1-6-2-9-4-11-8-18-24-20-37z" class="Z"></path><path d="M712 125l6 6-2 2c0 4 5 10 7 13 1 2 2 3 4 5 2 3 7 4 9 7 0 1 2 2 3 2 1 1 4 0 5 0l2-1 1-1c1 0 1-1 2-1l1-1h1 1v-1c2 0 3-1 4-2h1l3-4h1v3c1-1 1-2 2-2l-3 5-2 2v-1c-3 0-5 2-7 3-2 2-5 3-7 3-2 1-5 1-8 0-10-7-20-21-23-33 0-1 0-1-1-2v-2z" class="W"></path><path d="M711 125h1 0v2c1 1 1 1 1 2 3 12 13 26 23 33 3 1 6 1 8 0 2 0 5-1 7-3 2-1 4-3 7-3v1c-5 5-10 9-18 9-3-1-6-2-9-4-11-8-18-24-20-37z" class="b"></path><defs><linearGradient id="y" x1="355.662" y1="173.049" x2="468.269" y2="204.77" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#y)" d="M373 164c3 0 6 1 8 0 3 0 5 1 8 1 7 1 15-1 22 1h10c-3 0-6 1-9 1h0 5 0c0 1-1 1-2 1h-1c-1 1-4 0-6 0 2 1 5 1 7 1h2 0v2c3 0 6-1 9 0l41 1c4 0 7 0 11-1 2 0 4-3 5-5h1c-1 2-2 4-3 7l-2 5-1 2 3 1c-1 2-3 4-4 6 1 1 2 1 3 1h3c1 1 2 1 3 2-1 0-1 1-2 1-1-1-1-1-3-1h-2c-2 1-3 0-5 0-1-1-1-1-2 0 1 0 1 0 1 1h0l-1 1 1 1h0l2-1c-1 1-2 1-3 2v1h0c-3 1-9 1-12 0v-1l-1-1v2l-1 1c-1 1-2 2-2 4h0l13-1c3 1 5 1 7 0l1 1v1c-1 1-2 1-3 1l-1 1 1 1h1l1 2h0c0 2 0 4 1 6l5 11h0-1c-5 1-11 1-16 1-4 0-8 0-11-1h-38c-2-1-5 0-7-1-8 0-16-1-24-1h-8c-1 0-1-1-1-1-3-1-7-1-10 0h-1c-1-1-2-3-3-5h-1l-2-4-1-1c-3-6-5-10-6-17-1-8 1-15 6-21l5-4 6-4h4z"></path><path d="M409 176h7-4-2v1l-1 2-1-1h-1c-2 1-3 0-5-1 2-1 5-1 7-1z" class="X"></path><path d="M374 180l1-1c4-3 8-5 13-4-3 1-5 3-7 5-1 0-2 1-2 2-1 0-1 1-2 1v1c-1-1-1-1-1-2-1-1-2 0-3-1h-1l2-1z" class="J"></path><path d="M416 176h35-4l-1 1c-1 0-2 1-3 1s-2 0-3 1c-1-1-2-1-4-1h-2-5c-1-1-2 0-2-1-2 0-1-1-3 0-2 0-3 1-5 1h-3v1h-1l-3-3h4z" class="a"></path><path d="M369 187c-1 0-2 0-3-1l-1-1c1-1 1-2 2-3 0 1 1 2 2 3 2 0 4 1 5 0v-1c-1-1-1-2-2-2v-3l2 1-2 1h1c1 1 2 0 3 1 0 1 0 1 1 2l1 1c1 1 2 1 3 1v1c1 0 2 1 3 1v2l-1-1c-1 0-1 0-2 1h1c0 1 1 1 1 2-1 1 0 1-2 1l-2-2c0-1-1-2-1-2-3-2-6-2-9-2z" class="N"></path><path d="M378 194c-1-1-6-1-8-1-1 1-3 1-5 0h0l8-2-10-4v-1l8 3c1 1 2 1 3 1v-1c-1-1-3-1-5-2 3 0 6 0 9 2 0 0 1 1 1 2l2 2c2 0 1 0 2-1 0-1-1-1-1-2h-1c1-1 1-1 2-1l1 1 1 1c0 1 1 1 2 2v1c-1 1-1 1-2 1l-1-1-1 1-3 1-1 1h-1l2-2v-1h-2z" class="I"></path><path d="M378 194h2v1l-2 2h1l1-1h4c0 1 1 1 1 2l-1 1h-3c-1 1-1 2-2 3l-2 1v1c-2 0-4 1-5 2h-1-3v1c-1-1-1-1-2-1s-1 0-2-1h0 1c2 0 3 1 5 0v-1c0-2-1-3-3-4-1 0-1 0-2-1 1-1 4 0 5 0l3-3h0 2v1h1c1-1 2-1 2-3z" class="N"></path><defs><linearGradient id="z" x1="400.793" y1="167.24" x2="415.481" y2="173.067" xlink:href="#B"><stop offset="0" stop-color="#9d9a9e"></stop><stop offset="1" stop-color="#c6c7c6"></stop></linearGradient></defs><path fill="url(#z)" d="M411 166h10c-3 0-6 1-9 1h0 5 0c0 1-1 1-2 1h-1c-1 1-4 0-6 0 2 1 5 1 7 1h2 0v2c3 0 6-1 9 0h-37 0 1c1-1 2-1 3-1h1v-1h-4v-1l7-1-2-1h16z"></path><path d="M394 175c2 1 3 1 4 0 3 0 7 0 11 1-2 0-5 0-7 1h-9c-1 2-1 2-2 2h-1c-1 1-2 1-2 2h4v2l1 1h-4c-1 1-1 1-1 3 1 0 3 0 4-1v1 2h1c2 1 4 1 6 2v2 1l-2-1-1 1h-3l-2-2v-1l-1 1h-1l-1 1v-2h-2 0-1c1-1 1-1 1-2l-2-1c-1 0-2-1-3-1v-1c-1 0-2 0-3-1l-1-1v-1c1 0 1-1 2-1 0-1 1-2 2-2 2-2 4-4 7-5h6z" class="F"></path><path d="M388 175h6l-3 1c-2 0-3 0-5 2l-1 1s-2 1-3 1c0 1 1 1 1 2-1 0-1 0-2 1 0 1 1 0 0 2-1-1-1-1-3 0l-1-1v-1c1 0 1-1 2-1 0-1 1-2 2-2 2-2 4-4 7-5z" class="C"></path><path d="M394 175c2 1 3 1 4 0 3 0 7 0 11 1-2 0-5 0-7 1h-9c-1 2-1 2-2 2h-1c-1 1-2 1-2 2h4v2c-1 0-3 0-4-1h-1c-1 1-2 1-3 1v-1c1 0 1 0 2-1v-1c1-2 3-3 5-4l3-1z" class="O"></path><path d="M410 177v-1h2l3 3h1v-1h3c2 0 3-1 5-1 2-1 1 0 3 0 0 1 1 0 2 1h5 2c2 0 3 0 4 1v1h-1v1h1 0l-1 1h1v1c-1 0-2 0-3 1h1c0 1 2 2 3 3l2 1h-6l2 2c-1 0-2 0-3-1-3-1-5-1-8-1-4-1-8-1-11-2l-3-1-1-2c-1 0-2-5-3-6z" class="V"></path><path d="M413 183h1c1 0 2 2 3 2 2 1 6 0 9 1h1 4c1 1 2 1 3 1h0c-1-1-2-1-4-2h1 3c2 0 3 2 4 2h3l2 1h-6l2 2c-1 0-2 0-3-1-3-1-5-1-8-1-4-1-8-1-11-2l-3-1-1-2z" class="e"></path><path d="M416 179v-1h3c2 0 3-1 5-1 2-1 1 0 3 0 0 1 1 0 2 1h5 2c2 0 3 0 4 1v1h-1v1h1 0l-1 1c-2 0-6-1-7 0l-1 1c-2 0-2-1-3 1-4 0-9 0-12-1v-1c1 1 1 1 2 1h1c2 0 7 1 9 0v-1h-2c-1-1-3 0-4 0l-1-1-1 1-1-1-1 1c-1-1-2-1-3-1h0l-1-1h3l-1-1z" class="Q"></path><path d="M429 178h5 2c2 0 3 0 4 1v1h-1c-2-1-5 0-8 0l-2-2z" class="e"></path><path d="M391 179c1 0 1 0 2-2h9c2 1 3 2 5 1h1l1 1 1-2c1 1 2 6 3 6l1 2 3 1c0 2-1 2 0 4-2 1-3 1-5 0h-1v1c-1 0-2 1-3 0h-2l-2 2h-1-3l-1 1v-1-2c-2-1-4-1-6-2h-1v-2-1c-1 1-3 1-4 1 0-2 0-2 1-3h4l-1-1v-2h-4c0-1 1-1 2-2h1z" class="M"></path><path d="M392 187h3 2l-1 2h6c1 0 1 0 1 1l1-1h3c1 0 2 1 4 1h0v1c-1 0-2 1-3 0h-2l-2 2h-1-3l-1 1v-1-2c-2-1-4-1-6-2h-1v-2z" class="U"></path><path d="M410 177c1 1 2 6 3 6l1 2 3 1c0 2-1 2 0 4-2 1-3 1-5 0h-1 0c-2 0-3-1-4-1h-3l1-1c1-1 3-3 5-4h0c-1-2-2-3-1-5l1-2z" class="C"></path><path d="M410 177c1 1 2 6 3 6l1 2v1 1h-1c-2-1-2-2-3-3-1-2-2-3-1-5l1-2z" class="D"></path><path d="M391 179c1 0 1 0 2-2h9c2 1 3 2 5 1h1l1 1c-1 2 0 3 1 5h0-7c-1 0-1 1-2 1h-1-2c-1 0-1 0-2-1 0-1 0-1-1-1-1-1-2-1-3-2h-4c0-1 1-1 2-2h1z" class="b"></path><path d="M392 181h-4c0-1 1-1 2-2h1v1c1 1 0 1 1 0 3 0 4 1 6 2h1c1 1 1 2 1 3h-2c-1 0-1 0-2-1 0-1 0-1-1-1-1-1-2-1-3-2z" class="Y"></path><path d="M451 176c7 0 15-1 22 1 1 0 3-1 5 0l-3 3h1l-1 1h-1-2 0-3v1h3c-1 2-3 1-2 3h2 0c2-1 2 0 4 0 1-2 1-3 2-5l3 1c-1 2-3 4-4 6 1 1 2 1 3 1h3c1 1 2 1 3 2-1 0-1 1-2 1-1-1-1-1-3-1h-2c-2 1-3 0-5 0-1-1-1-1-2 0l-1-1h-6c-1-1-2-1-3-2h-3l-1-2h-2l-1-1c-1 0-2-1-4-1h-3c-2 1-6 0-8 0v-1h-1l1-1h0-1v-1h1v-1c1-1 2-1 3-1s2-1 3-1l1-1h4z" class="W"></path><path d="M478 180l3 1c-1 2-3 4-4 6 1 1 2 1 3 1h3c1 1 2 1 3 2-1 0-1 1-2 1-1-1-1-1-3-1h-2c-2 1-3 0-5 0-1-1-1-1-2 0l-1-1h-6c-1-1-2-1-3-2h-3l-1-2c2 0 4-1 6 1l4-1 2 2h0c1-1 1-1 2-1h0c2 0 3 0 4-1 1-2 1-3 2-5z" class="k"></path><path d="M478 180l3 1c-1 2-3 4-4 6 1 1 2 1 3 1-1 0-1 0-2 1h-1 0c0-1-1-1 0-2v-1c-2 1-3 0-5 0 2 0 3 0 4-1 1-2 1-3 2-5z" class="o"></path><path d="M443 178c3 0 5 1 8-1h0c3 1 6 1 8 1h3 7v1c-2 1-7 1-10 1v1l1 1h-4c-2-1-5 0-7 0l-1 1c-2 1-6 0-8 0v-1h-1l1-1h0-1v-1h1v-1c1-1 2-1 3-1z" class="Z"></path><path d="M440 181c2 0 6 1 9 0 0-1 1-1 1-1 1 0 2 0 3-1h1s1 1 2 1 2-1 3 0v1l1 1h-4c-2-1-5 0-7 0l-1 1c-2 1-6 0-8 0v-1h-1l1-1z" class="j"></path><path d="M384 188l2 1c0 1 0 1-1 2h1 0 2v2l1-1h1l1-1v1l2 2h3l1-1 2 1 1-1h3 1l2-2h2c1 1 2 0 3 0l2 1c-1 1-1 2-2 2s-1 1-2 1h0l-3 3h1 1l2 1 2 1v1c0 2 0 3 1 5l2 2c-2 1-1 1-2 1h-14-7-1-1c-2 0-4 1-6 2-1 0-2 0-3 1l-1-1c-1 0-2 1-2 1l-1-1h-1c-2 0-4 0-6 1h-7v1l1 1c-1 0-1 0-2 1h-1l-2-4h8c2-1 4 0 6-2l1-1h1c1-2 2-3 2-4v-1l2-1c1-1 1-2 2-3h3l1-1c0-1-1-1-1-2h-4l3-1 1-1 1 1c1 0 1 0 2-1v-1c-1-1-2-1-2-2l-1-1v-2z" class="D"></path><path d="M398 195c1 0 2 1 3 1l-5 4v-1l1-1h-3c2 0 3-1 4-2v-1z" class="I"></path><path d="M379 202c1-1 1-2 2-3h3l1-1v1l1-1h0c-1 1-1 2-2 2h-2-1v2h0-1s-1 1-1 2v1l3 3v1h-1l-1 1h-2l-1 1h-1c-2 0-4 0-6 1h-7v1l1 1c-1 0-1 0-2 1h-1l-2-4h8c2-1 4 0 6-2l1-1h1c1-2 2-3 2-4v-1l2-1z" class="E"></path><path d="M377 204v-1l2-1c0 1 0 2-1 3s-1 1-1 2l-2 1c1-2 2-3 2-4z" class="B"></path><path d="M399 194l1-1h3 1l2-2h2c1 1 2 0 3 0l2 1c-1 1-1 2-2 2s-1 1-2 1h0l-3 3h1 1l2 1 2 1v1c0 2 0 3 1 5l2 2c-2 1-1 1-2 1h-14-7c-1-1-1-2-2-3l6-6h0l5-4c-1 0-2-1-3-1 0 0 0-1-1-2l2 1z" class="E"></path><path d="M397 204v1l-1 2h-3c1-2 3-3 4-3z" class="H"></path><path d="M397 204v-1c1-2 1-1 3-2v2 1h0l-3 1v-1z" class="D"></path><path d="M399 194l1-1h3 1l2-2h2c1 1 2 0 3 0l2 1c-1 1-1 2-2 2s-1 1-2 1h0l-3 3h1c-1 2-5 4-7 6v-1-2s0-1 1-1v-1c3-2 5-3 7-6-3 1-5 2-7 3-1 0-2-1-3-1 0 0 0-1-1-2l2 1z" class="C"></path><path d="M407 198h1l2 1 2 1v1c0 2 0 3 1 5l2 2c-2 1-1 1-2 1-2-2-5-2-6-3-2 0-1-1-3-2v1h-1l-1 1-2-2h0c2-2 6-4 7-6z" class="N"></path><path d="M408 198l2 1 2 1v1c0 2 0 3 1 5l-3-3c-1-2-1-3-2-5z" class="J"></path><path d="M440 183c2 0 6 1 8 0h3c2 0 3 1 4 1l1 1h2l1 2h3c1 1 2 1 3 2h6l1 1c1 0 1 0 1 1h0l-1 1 1 1h0l2-1c-1 1-2 1-3 2v1h0c-3 1-9 1-12 0v-1l-1-1v2l-1 1c-1 1-2 2-2 4h0 0l-1 1-1-1v-1l-1 1c-1-1-1-2-1-3-3 3-5 5-9 7l-5-3c-3-1-7-1-10-1h-1l-4 1s-1 0-1 1c-2 0-4 1-6 2 0 1-1 2-1 3l-1-1v-2c-1-1-1-2-1-4l1-1h-1v-1l2-1v-1h0l-3 1-1-1-1 3-2-1h-1-1l3-3h0c1 0 1-1 2-1s1-1 2-2l-2-1v-1h1c2 1 3 1 5 0-1-2 0-2 0-4 3 1 7 1 11 2 3 0 5 0 8 1 1 1 2 1 3 1l-2-2h6l-2-1c-1-1-3-2-3-3h-1c1-1 2-1 3-1z" class="F"></path><path d="M413 192l1 1h3c0 1 0 0-1 1h-2v1h-1c-1 0-1 0-2 1l-1 3-2-1h-1-1l3-3h0c1 0 1-1 2-1s1-1 2-2z" class="I"></path><path d="M445 193h0v-1c1 0 1 1 2 1l1 1c1-1 2-2 3-2h1v3c-2 2-4 4-7 6h-1l-1-1h2c2 0 2-1 3-3-1-2-2-1-4-2l1-2z" class="J"></path><path d="M443 188c3 0 3 2 4 3h4l1 1h-1c-1 0-2 1-3 2l-1-1c-1 0-1-1-2-1v1h0-6c0-1 1-1 2-1 0-1 0-1-1-2h-1l-2-2h6z" class="I"></path><path d="M420 196c2 0 6 0 8-1h4c3 1 6 1 10 2 1 0 2-1 4 0v1c-1 1-2 1-3 2l1 1c-2 1-3 1-4 1l-1-1-3-2c-6-2-14 1-19 4l-1 1c0 1-1 2-1 3l-1-1v-2c-1-1-1-2-1-4l1-1s1-1 2-1l1-1c1 0 2-1 3-1z" class="D"></path><path d="M414 199s1-1 2-1l1-1c1 0 2-1 3-1-2 1-3 2-4 3l-1 1h0c1 1 1 2 2 3l-1 1c0 1-1 2-1 3l-1-1v-2c-1-1-1-2-1-4l1-1z" class="P"></path><path d="M440 183c2 0 6 1 8 0h3c2 0 3 1 4 1l1 1h2l1 2h3c1 1 2 1 3 2h6l1 1c1 0 1 0 1 1h0l-1 1 1 1h0l2-1c-1 1-2 1-3 2v1h0c-3 1-9 1-12 0v-1l-1-1v2l-1 1c-1 1-2 2-2 4h0 0l-1 1-1-1v-1l-1 1c-1-1-1-2-1-3-3 3-5 5-9 7l-5-3c-3-1-7-1-10-1h-1l-4 1s-1 0-1 1c-2 0-4 1-6 2l1-1c5-3 13-6 19-4l3 2 1 1c1 0 2 0 4-1h1c3-2 5-4 7-6v-3l-1-1h-4c-1-1-1-3-4-3l-2-1c-1-1-3-2-3-3h-1c1-1 2-1 3-1z" class="o"></path><path d="M438 184c4 0 8 0 11 3 1 1 2 3 2 4h-4c-1-1-1-3-4-3l-2-1c-1-1-3-2-3-3z" class="J"></path><path d="M453 194c1-1 1-2 1-3-1-2-3-4-4-6 2 1 4 2 7 3h0c2 1 5 1 7 1h1 6c-1 1-1 1-2 1-1 1-2 1-3 2v-2c-1-1-1 0-2 0l-1 1c-3 0-5-1-8-1v1 2s-1 1-2 1z" class="C"></path><path d="M453 194c1 0 2-1 2-1v-2-1c3 0 5 1 8 1l1-1c1 0 1-1 2 0v2c1-1 2-1 3-2 1 0 1 0 2-1l1 1c1 0 1 0 1 1h0l-1 1 1 1h0l2-1c-1 1-2 1-3 2v1h0c-3 1-9 1-12 0v-1l-1-1v2l-1 1c-1 1-2 2-2 4h0 0l-1 1-1-1v-1l-1 1c-1-1-1-2-1-3l1-3z" class="B"></path><path d="M471 189l1 1c1 0 1 0 1 1h0l-1 1 1 1h0l2-1c-1 1-2 1-3 2 0-1-1-1-1-1-1 1-1 1-2 1-2 0-3 0-4-1v-1h1c1-1 2-1 3-2 1 0 1 0 2-1z" class="q"></path><defs><linearGradient id="AA" x1="412.023" y1="234.397" x2="428.663" y2="197.186" xlink:href="#B"><stop offset="0" stop-color="#0a0604"></stop><stop offset="1" stop-color="#23272b"></stop></linearGradient></defs><path fill="url(#AA)" d="M411 196l1 1 3-1h0v1l-2 1v1h1l-1 1c0 2 0 3 1 4v2l1 1c0-1 1-2 1-3 2-1 4-2 6-2 0-1 1-1 1-1l4-1h1c3 0 7 0 10 1l5 3c4-2 6-4 9-7 0 1 0 2 1 3l1-1v1l1 1 1-1h0l13-1c3 1 5 1 7 0l1 1v1c-1 1-2 1-3 1l-1 1 1 1h1l1 2h0c0 2 0 4 1 6l5 11h0-1c-5 1-11 1-16 1-4 0-8 0-11-1h-38c-2-1-5 0-7-1-8 0-16-1-24-1h-8c-1 0-1-1-1-1-3-1-7-1-10 0h-1c-1-1-2-3-3-5 1-1 1-1 2-1l-1-1v-1h7c2-1 4-1 6-1h1l1 1s1-1 2-1l1 1c1-1 2-1 3-1 2-1 4-2 6-2h1 1 7 14c1 0 0 0 2-1l-2-2c-1-2-1-3-1-5v-1l-2-1 1-3z"></path><path d="M469 217v-2h-1c3-1 5-1 8 0 1 0 2 0 2 1v2h-2l-7-1z" class="q"></path><path d="M364 214c5 1 10 0 15 1 0 0-1 1-2 1-2 1-6-1-7 1 3 1 6-1 9 1h0c2 1 3 1 5 1l-4 1h-4c-3-1-7-1-10 0h-1c-1-1-2-3-3-5 1-1 1-1 2-1z" class="N"></path><path d="M379 218c5-1 11 0 16-1h74l7 1c-1 1-2 1-3 2h-1-65-8-19l4-1c-2 0-3 0-5-1h0z" class="g"></path><path d="M415 208v2c-1 0-2 1-3 1l3 1h0c1-1 1-1 2-1l1 1v-1c2 0 3 0 5-1h0c1 1 3 1 4 1v1h1v1h-1v1h1c-5 1-11 0-16 0h-24c-2 1-6 1-9 1-5-1-10 0-15-1l-1-1v-1h7c2-1 4-1 6-1h1l1 1s1-1 2-1l1 1c1-1 2-1 3-1 2-1 4-2 6-2h1 1 7 14c1 0 0 0 2-1z" class="Y"></path><path d="M384 211h9c-1 0-2 1-4 1-1 0-2 0-3 1-1 0-1 0-2-1-1 1-2 1-3 2h-7c1-1 2-2 4-2 0 0 1-1 2-1l1 1c1-1 2-1 3-1z" class="S"></path><path d="M377 211l1 1c-2 0-3 1-4 2h7 7c-2 1-6 1-9 1-5-1-10 0-15-1l-1-1v-1h7c2-1 4-1 6-1h1z" class="C"></path><path d="M415 208v2c-1 0-2 1-3 1-2 0-6 0-8 1h0l-1-1h-3-7-9c2-1 4-2 6-2h1 1 7 14c1 0 0 0 2-1z" class="I"></path><path d="M476 214l1-2 5 11h0-1c-5 1-11 1-16 1-4 0-8 0-11-1h-38c-2-1-5 0-7-1-8 0-16-1-24-1h-8c-1 0-1-1-1-1h4 19 8 65 1c1-1 2-1 3-2h2v-2c0-1-1-1-2-1v-1z" class="d"></path><path d="M476 214l1-2 5 11h0-1c-5 1-11 1-16 1-4 0-8 0-11-1 4 0 22 1 25-1-1-1-1-2-2-3-1 0-3 1-4 1 1-1 2-1 3-2h2v-2c0-1-1-1-2-1v-1z" class="J"></path><path d="M407 220h65v1h-12-20-32l-1-1z" class="h"></path><path d="M411 196l1 1 3-1h0v1l-2 1v1h1l-1 1c0 2 0 3 1 4v2l1 1c0-1 1-2 1-3 2-1 4-2 6-2 0-1 1-1 1-1l4-1h1c3 0 7 0 10 1l5 3c4-2 6-4 9-7 0 1 0 2 1 3l1-1v1l1 1 1-1h0l13-1c3 1 5 1 7 0l1 1v1c-1 1-2 1-3 1l-1 1 1 1h1l1 2h0c0 2 0 4 1 6l-1 2c-3 0-9-1-13 0h0-11-9-15-1v-1h1v-1h-1v-1c-1 0-3 0-4-1h0c-2 1-3 1-5 1v1l-1-1c-1 0-1 0-2 1h0l-3-1c1 0 2-1 3-1v-2l-2-2c-1-2-1-3-1-5v-1l-2-1 1-3z" class="U"></path><path d="M428 213c2-1 3-1 4-2h1l1 1 3-1c1 1 1 0 2 1l1-1v1c1 0 2 1 4 0h1c1 0 2 1 3 0h2l1-1h9c2 0 6-1 9 0 2 1 4 2 7 2v1c-3 0-9-1-13 0h0-11-9-15-1v-1h1z" class="e"></path><path d="M418 209c3-1 7-1 10-1 2-1 4-2 6-1 3 1 7 1 10 1 1-1 1-2 3-3l2-1s1-1 2-1c1 1 2 1 3 1h10c1 1 1 1 2 1l6 1 2-2h1l1 2v2l-2 1-1 1-3-2c-4-1-5-2-9-2h-1c-1 0-2 1-3 1-2 1-4 1-5 1-4 2-9 0-13 1-2 1-3 0-5 0s-4 1-7 0h-9z" class="E"></path><path d="M451 205c1 1 4 0 5 0l-1 1-3 1-1-1v-1z" class="B"></path><path d="M452 197c0 1 0 2 1 3l1-1v1l1 1 1-1h0l13-1c3 1 5 1 7 0l1 1v1c-1 1-2 1-3 1l-1 1 1 1-2 2-6-1c-1 0-1 0-2-1h-10c-1 0-2 0-3-1-1 0-2 1-2 1l-2 1c-2 1-2 2-3 3-3 0-7 0-10-1-2-1-4 0-6 1-3 0-7 0-10 1-1 0-2-1-3-1v-1c0-1 1-2 1-3 2-1 4-2 6-2 0-1 1-1 1-1l4-1h1c3 0 7 0 10 1l5 3c4-2 6-4 9-7z" class="N"></path><defs><linearGradient id="AB" x1="813.859" y1="552.896" x2="773.951" y2="551.855" xlink:href="#B"><stop offset="0" stop-color="#737173"></stop><stop offset="1" stop-color="#878688"></stop></linearGradient></defs><path fill="url(#AB)" d="M806 379l1 1-1 2v3l1 1v1l5-2h4l1 2v1c-2 2-2 5-3 7l1 1v7l1-6c1-1 2-2 2-3v-1h1l1 1v1 1c1 1 1 2 2 3v5l1 3 3 33c1 10-1 19 0 29l-1 12-3 24c0 2-1 5 0 7 0 1 1 1 1 2 0 2 0 3 1 4-1 6-3 14-3 21l-3 15c0 2 0 5-1 7 0 2 0 5-1 7l-4 15v-4c1-1 1-2 1-3v-1l1-2c0-3 0-3-2-5l-3 14c-1 4-1 10-4 13l-4 13c-1 1-1 3-1 4l-1 1c-1 3-4 8-4 11v4c-4 10-11 21-18 30l-6 9h-2c5-10 9-19 12-29 5-20 8-40 11-59 1-12 2-24 0-36-1-4-2-9-6-11 0-1-1-1-1-2 1-1 3-3 4-5 1-3 1-7 0-9s-2-4-4-5c-2 0-5 0-6 1-1 0-2 1-3 1l3-3c2-3 5-7 4-11h-2c3-3 6-5 8-7l3-9c3-13 1-31-6-42-3-3-5-5-9-5-4 1-7 3-9 6-2 2-2 4-4 5-2 2-5 4-7 6-2 1-4 3-6 5v1h-2c2-4 7-8 10-11h0l-1-1h-1l-2 2-1 1h-1l6-25-1 1v-7c3-4 7-6 11-9 5-3 10-6 14-10l7-6c6-5 12-9 16-15z"></path><path d="M789 492l1 1c-2 2-6 5-6 8v4c0 1 1 1 2 1-1 2-4 3-6 3-1 1 0 1-1 1 2-3 5-7 4-11h-2c3-3 6-5 8-7z" class="n"></path><path d="M816 397c1-1 2-2 2-3v-1h1l1 1v1 1c1 1 1 2 2 3v5l1 3 3 33c1 10-1 19 0 29l-1 12-3 24c0 2-1 5 0 7 0 1 1 1 1 2 0 2 0 3 1 4-1 6-3 14-3 21l-3 15c0 2 0 5-1 7 0 2 0 5-1 7l-4 15v-4c1-1 1-2 1-3v-1l1-2c0-3 0-3-2-5l-3 14c-1 4-1 10-4 13 0-1 0-2 1-3 0-2 1-3 1-5v-1c1-1 1-2 1-3l1-9c2-8 2-17 3-25 1-5 2-10 2-15l2-35 1-11v-6-4h1v-2c-1-5 0-11 0-16 1-5 0-10 1-16l-1-12-2-4-1-25 1-6z" class="g"></path><path d="M812 568l5-27c1 5 1 10 0 14v2 4h0c0 2 0 5-1 7l-4 15v-4c1-1 1-2 1-3v-1l1-2c0-3 0-3-2-5z" class="AA"></path><path d="M822 512c0 1 1 1 1 2 0 2 0 3 1 4-1 6-3 14-3 21l-3 15c0 2 0 5-1 7h0v-4-2c1-4 1-9 0-14 0-3 1-7 2-10 1-6 1-13 3-19z" class="AG"></path><path d="M819 444c1 6 0 14 0 20 0 2 1 5 0 8s-1 5-1 8v13c0 4-1 9-1 13v19c-1 2-1 4-2 6v6l-1 7c-1 2-1 3-1 5h-1c1-5 2-10 2-15l2-35 1-11v-6-4h1v-2c-1-5 0-11 0-16 1-5 0-10 1-16z" class="a"></path><path d="M816 397c1-1 2-2 2-3v-1h1l1 1v1 1c1 1 1 2 2 3v5l1 3 3 33-2 1c0 2 0 6 1 9v1l-2 2c-1-1-1-5-1-7l-3-23c-1 3-2 6-1 9l-2-4-1-25 1-6z" class="V"></path><path d="M824 441l-6-34v-2c0-2-1-5 0-7 1-1 1-2 1-3v-1 2l1-1v1c1 1 1 2 2 3v5l1 3 3 33-2 1z" class="j"></path><path d="M819 396l1-1v1c1 1 1 2 2 3v5l1 3c-2-1-3-3-3-5s-1-4-1-6z" class="W"></path><defs><linearGradient id="AC" x1="786.701" y1="399.177" x2="803.343" y2="478.065" xlink:href="#B"><stop offset="0" stop-color="#3f3f41"></stop><stop offset="1" stop-color="#757375"></stop></linearGradient></defs><path fill="url(#AC)" d="M806 379l1 1-1 2v3l1 1v1l5-2h4l1 2v1c-2 2-2 5-3 7l1 1v7l1 25 2 4 1 12c-1 6 0 11-1 16 0 5-1 11 0 16v2h-1 0c-1-2 1-4 0-6 0 1 0 2-1 3v1h-1v-2c-1 0-1-1-2-2 0-2 1-3 0-4-2-1-2-1-3-2-1-2-2-6-4-7v-1c-1-2-1-4-2-5l1-2-1-1c0-1 0-2 1-2v-1l-2-2h-1-2c-3-4-6-4-10-6h-2c-1 0-2 1-2 2-3-3-5-5-9-5-4 1-7 3-9 6-2 2-2 4-4 5-2 2-5 4-7 6-2 1-4 3-6 5v1h-2c2-4 7-8 10-11h0l-1-1h-1l-2 2-1 1h-1l6-25-1 1v-7c3-4 7-6 11-9 5-3 10-6 14-10l7-6c6-5 12-9 16-15z"></path><path d="M796 404h1v2h-1v-2z" class="K"></path><path d="M761 441c2-1 2-1 4-1l1 1-3 4c-1 1-3 3-4 3h0l-1-1h-1c2-1 3-4 4-6z" class="g"></path><path d="M767 432h1 0c1 1 1 2 2 3 0 2-1 4-3 6h-1l-1-1c-2 0-2 0-4 1v-1c1-1 2-2 2-3h0l1-2c1-2 1-2 3-3z" class="X"></path><path d="M763 437l4-1c1 2 0 3 0 5h-1l-1-1c-2 0-2 0-4 1v-1c1-1 2-2 2-3h0z" class="V"></path><path d="M816 428l2 4 1 12c-1 6 0 11-1 16h-1c1-5 1-10 0-14-2-6-1-13-1-18z" class="M"></path><defs><linearGradient id="AD" x1="779.181" y1="403.307" x2="788.959" y2="420.015" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#5d5b5c"></stop></linearGradient></defs><path fill="url(#AD)" d="M806 379l1 1-1 2v3l1 1v1l5-2h4l1 2v1c-2 2-2 5-3 7l1 1c-2 1-2 2-3 1h-1c0-2-2-3-3-4h-4c-1 1-2 2-3 4s-2 3-4 4c-1 2-2 3-3 5 0 0-1 0-1 1-1 1-3 2-5 3l-3 3c-2 2-4 2-6 3l-2 2-1 1h-2v1c-1 2-3 3-4 6l-1 1v1c0 1-1 2-1 2-1 1-1 1-1 2-2 1-2 1-3 3l-1 2h0c0 1-1 2-2 3v1c-1 2-2 5-4 6l-2 2-1 1h-1l6-25-1 1v-7c3-4 7-6 11-9 5-3 10-6 14-10l7-6c6-5 12-9 16-15z"></path><path d="M757 442c1-2 2-4 4-6l2 1c0 1-1 2-2 3s-2 1-4 2z" class="S"></path><path d="M757 442c2-1 3-1 4-2v1c-1 2-2 5-4 6l-2 2c0-3 1-5 2-7z" class="Y"></path><path d="M812 385h4l1 2v1c-2 2-2 5-3 7-1-2-1-4 0-6 0-1 0-1-1-2l-2 1c-2 0-2 0-4-1h0 0l5-2z" class="F"></path><path d="M777 418v-1c1-2 4-4 6-5 3-2 5-4 8-6 3-4 5-7 10-9-1 2-2 3-4 4-1 2-2 3-3 5 0 0-1 0-1 1-1 1-3 2-5 3l-3 3c-2 2-4 2-6 3l-2 2z" class="K"></path><path d="M806 379l1 1-1 2v3l1 1v1h0c-2 1-3 1-4 2-1 0-2 1-3 1-5 4-9 7-13 11l-19 13c-4 3-8 6-9 11l-1 1v-7c3-4 7-6 11-9 5-3 10-6 14-10l7-6c6-5 12-9 16-15z" class="k"></path><path d="M806 382v3l1 1v1h0c-2 1-3 1-4 2-1 0-2 1-3 1l6-8z" class="M"></path><path d="M772 153c1 6 5 12 10 15h1l2 2h2c1 1 3 1 4 1l15 5 11 5 5 2c1 2 1 3 3 4v1h0v1h1 1v-1c0-1 0-1 1-2v-1l6 2h2l1 1h2c1 0 3 1 4 1h1l3 3h1l2 1v1l-1 1-2 3-2 2-4 4v1c1 1 1 1 1 3l2-2v2l1 2c0 2 0 2 1 3l2 2c1 1 1 0 2 2h-2c-1-1-1-1-2-1-2-1-2-1-4 0-1 1-3 2-4 2h-1c0 1-1 1-2 1-1 1-1 1-1 3s1 5 3 7h0v1c0 1 1 2 1 3l1 1-2 1c0 1 1 2 1 3 0 2 0 2 1 3h2c0 1 0 1-1 1h-1v1 2l1 1 3 9h1 0c2 6 3 11 6 16 0 1 3 7 2 7 2 5 4 10 4 15h0c-1 2-1 5 0 7l-4-2h-1-1 0l-2-1c-1 1-1 1-1 2h2v1c2 1 3 1 4 1-1 0-2 0-4 1v-1c-2 0-3 0-4 1h1l1 1h-2l-1-1-1 1h1l-1 1s-1 1-1 2c-3 3-5 5-7 8-1 0-2 1-3 2h0c-1 3-3 5-5 6h-2c1 3 1 5 1 8 0-2 0-3-1-5l-1-2c-1-1-1-3-2-4l-2-1c0-2 0-2-1-3l1 5c0 1-1 3 0 5 0 1 0 1 1 2 0 1-1 3 0 4v3l-1-3h0v1l-2-2v-1c-1 2-1 3-2 4l-1 1 1 1v3 1l1 2-2-3-2-6h-2c-2-5-3-9-5-13-3-6-7-11-11-15-2-2-4-5-6-6-5-2-9-4-14-5h-2c-2 0-2 1-4 2l-4-1-9 1 6-3c6-3 12-7 17-12l3-4v-1-3l1-1v-2l-2 4v-3l1-2v-4c-3-1-3-2-4-5v-2c-4-12-14-21-20-31-1-2-1-3 0-5l-8-9c-1 1-3 1-4 1h-2v-2-1-2h1l-2-2h-1c0-3-1-5 0-8h0c2 1 3 3 5 3l3-1 1-2-3-5c0-1-1-1-1-2h-1v-1c-1-2-6-6-8-6l-13-11v-1h2l1-1c6 1 10 3 15 4h1c2-1 4-1 6-1 1 0 2 0 3-1 2-1 4-3 5-4h1c-1 1-2 2-2 3 1 0 3-1 4-1 2-2 3-5 5-7l2-6 1-1c1 0 1 0 2-1 0-1 1-2 1-3z" class="S"></path><path d="M813 223l-1-2h1c3 1 5 3 6 6 1 2 1 3-1 6v1l-1-3v-2-2c-1-2-3-3-4-4z" class="X"></path><path d="M825 229l-1-1h-1v-2h1l-1-1c-1-1-2 0-2-1-1-1-2-1-2-2h2c0 1 1 1 2 1h1 1c3 4 5 9 8 13l1 2c-1-1-3-1-4-1 0-1 0-2-1-3h-1v-2c0-1-2-3-3-3z" class="T"></path><path d="M803 184l1-1 2 1v2 1c1 1 2 3 2 4-1 0-1 1-2 2v3c-1 0-2 0-4-1 0 0-1-1-1-2l-1 1-2-1v1h0c1 1 1 0 2 1h-2-2v1l-1-1c-1 0-2-1-3-1h0-3l3-2c1 1 3 0 4-1l1 1-1 1 1 1c0-1 0-1 1-1 1-1 1-1 2-1h0c1 0 2 2 3 2v-1l2-2v-1c1 1 1 1 2 1l-1-1v-1l-1-1v-1-1c0-1-1-2-2-2z" class="M"></path><path d="M768 188l5 2c2 1 3 1 5 2h0l4 1v1c-1 1-3 2-4 2l-1-1c-1 0-2 1-4 1l-1-1-1 1c-1-1-2-1-3-1h-3c1-1 2-1 3-1h2c1 0 1 0 3-1-2-3-4-2-6-3l-1-1 2-1z" class="c"></path><defs><linearGradient id="AE" x1="832.95" y1="241.468" x2="833.05" y2="225.529" xlink:href="#B"><stop offset="0" stop-color="#585858"></stop><stop offset="1" stop-color="#747177"></stop></linearGradient></defs><path fill="url(#AE)" d="M825 223l2 2s1-1 2-1l1 2c1 1 1 1 1 2h0c2 0 2 0 3 1 1 0 2 0 3 1 0 1 1 2 1 3l1 1-2 1c0 1 1 2 1 3 0 2 0 2 1 3h2c0 1 0 1-1 1h-1v1 2l-1-1c0-1 0-1-1-2-1-2-2-3-3-4l-1-2c-3-4-5-9-8-13z"></path><path d="M831 228c2 0 2 0 3 1 1 0 2 0 3 1 0 1 1 2 1 3l-3-3-2 1c-1-1-1-2-2-3z" class="Y"></path><path d="M839 216h3c-1 1-3 2-4 2h-1c0 1-1 1-2 1-1 1-1 1-1 3s1 5 3 7h0v1c-1-1-2-1-3-1-1-1-1-1-3-1h0c0-1 0-1-1-2l-1-2c-1 0-2 1-2 1l-2-2h-1l-2-1c-1-1-1-1-2-1-1-1-1-1-2-1l-2-2 4 1h3c1 0 3 1 4 1 1-1 2-1 2-1l1 1h2 0c2-1 2-2 3-3 2 0 2 0 4-1z" class="V"></path><path d="M825 229c1 0 3 2 3 3v2h1c1 1 1 2 1 3 1 0 3 0 4 1s2 2 3 4h-2l-2-1s-1 0-1 1h-2l-1 1h0c-1 1 0 1-1 2 0 1 0 2-1 4v1l-1-1 1-1v-5h1l1-1c0-1 0-1-1-2h-2l-1-1-1 1h-1l-1-1c0-1 1-2 1-2v-1h-2c-2 1-5 6-6 7-1 2-1 3-2 4v-4l2-2c3-3 6-7 6-12h0l1 1c1-1 2-1 3-1z" class="C"></path><path d="M830 237c1 0 3 0 4 1s2 2 3 4h-2l-2-1c-1-1-2-2-3-2s-3-1-3-2 1 0 3 0z" class="F"></path><defs><linearGradient id="AF" x1="807.658" y1="253.45" x2="828.471" y2="251.531" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#2c2c2e"></stop></linearGradient></defs><path fill="url(#AF)" d="M813 247c1-1 1-2 2-4 1-1 4-6 6-7h2v1s-1 1-1 2l1 1h1l1-1 1 1h2c1 1 1 1 1 2l-1 1h-1c-3 0-5 0-7 2h0c-2 3-4 5-5 8l-3 5c-1 3-1 6-1 9 0 1 0 3-1 4v7c-1-2 0-7-1-9l-1-3h0l1-8c1-4 2-7 4-11z"></path><defs><linearGradient id="AG" x1="773.925" y1="177.179" x2="804.382" y2="194.274" xlink:href="#B"><stop offset="0" stop-color="#69676b"></stop><stop offset="1" stop-color="#7f7d7f"></stop></linearGradient></defs><path fill="url(#AG)" d="M772 180h0c1 0 1 0 2 1l2-1c1 1 2 2 4 1h7c1 1 1 1 3 2h4v1l2-1c1 0 3 0 5 1h0 2c1 0 2 1 2 2v1 1l1 1v1l1 1c-1 0-1 0-2-1v1l-2 2v1c-1 0-2-2-3-2h0c-1 0-1 0-2 1-1 0-1 0-1 1l-1-1 1-1-1-1c-1 1-3 2-4 1l-3 2c-1 0-2-1-3-1h-2c-1 0-1-1-2-1h-4c-2-1-3-1-5-2l-5-2-2-1h0 4l1-1h-1c-1-1-1-2-2-3l4-3z"></path><path d="M802 186h1v2c-1 1-1 2-3 3l-1-1-1-1c0-1 1-1 1-2h2c1 0 1 0 1-1z" class="a"></path><path d="M768 183l4-3c-1 2-1 3-2 4h4v1l-2 2-1-1h-1c-1-1-1-2-2-3z" class="O"></path><path d="M771 186l1 1c1 0 2 1 2 1 1 0 1-1 2 0h2 3c1 1 3 1 5 1l1 1 1 2-2 1h0-2c-1 0-1-1-2-1h-4c-2-1-3-1-5-2l-5-2-2-1h0 4l1-1z" class="Y"></path><path d="M786 193c-1-1-1-1-2-1 1-2 1-2 3-2l1 2-2 1z" class="V"></path><path d="M770 176h15c5 1 12 4 18 2h0 2 1v-2l11 5 5 2c1 2 1 3 3 4v1h0-1l-1 1h-2c0 1 1 2 1 2v1l-1-1c-2-1-3-1-4-2l-2-2c-2 0-3-1-4-1v-1l-4-2h-2l-1-1h-3c-1 0-2-1-3-1h-3c-3-1-6 1-9-1-3 0-7-1-10 0l-2 1c-1-1-1-1-2-1h0l-4 3c1 1 1 2 2 3h1l-1 1h-4 0l2 1-2 1 1 1c2 1 4 0 6 3-2 1-2 1-3 1h-2c-1 0-2 0-3 1v1h0v1c-2-1-3-3-5-4h0l-4-3-3-3c2-1 2-2 3-4 0-1 1-1 2-2v-1c4-2 8-3 12-4z" class="L"></path><path d="M811 184c3 0 7 2 9 1l4 3-1 1h-2c-3-2-7-3-10-5z" class="C"></path><path d="M758 183c1 0 1-1 2 0h0c2 0 5-1 7-2h0 1l-1 1 1 1c1 1 1 2 2 3h1l-1 1h-4 0l2 1-2 1c-1-1-2-1-3-1l-1-1c-2-1-4-1-5-2l1-2z" class="M"></path><path d="M758 183c1 0 1-1 2 0h0c2 0 5-1 7-2h0 1l-1 1c-1 1-3 2-4 3-1 0-2 0-3-1l-1 1h1c1 0 1 1 2 2-2-1-4-1-5-2l1-2z" class="O"></path><path d="M766 179l-1 2-6 1-1 1-1 2c1 1 3 1 5 2l1 1c1 0 2 0 3 1l1 1c2 1 4 0 6 3-2 1-2 1-3 1h-2c-1 0-2 0-3 1v1h0v1c-2-1-3-3-5-4h0l-4-3-3-3c2-1 2-2 3-4 0-1 1-1 2-2h3c2-1 3-1 5-2z" class="F"></path><path d="M753 187c2-1 2-2 3-4 0 1 0 2 1 4 1 0 2 1 3 1 3 1 4 2 6 4-1 1-2 1-3 1h-3 0l-4-3-3-3z" class="C"></path><path d="M770 176h15c5 1 12 4 18 2h0 2 1v-2l11 5 5 2c1 2 1 3 3 4v1h0-1l-4-3c-2 1-6-1-9-1 0 0-2-1-3-1-2-1-5-2-7-3l-19-2c-3-1-8 0-10-1-2 0-1 0-2 1-1 0-1 0-2 1h-2c-2 1-3 1-5 2h-3v-1c4-2 8-3 12-4z" class="G"></path><defs><linearGradient id="AH" x1="816.045" y1="187.34" x2="811.418" y2="175.567" xlink:href="#B"><stop offset="0" stop-color="#7a7a7c"></stop><stop offset="1" stop-color="#aba8a9"></stop></linearGradient></defs><path fill="url(#AH)" d="M806 176l11 5 5 2c1 2 1 3 3 4v1h0-1l-4-3c-5-3-11-5-17-7h2 1v-2z"></path><defs><linearGradient id="AI" x1="780.749" y1="179.885" x2="783.068" y2="170.306" xlink:href="#B"><stop offset="0" stop-color="#979798"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#AI)" d="M772 153c1 6 5 12 10 15h1l2 2h2c1 1 3 1 4 1l15 5v2h-1-2 0c-6 2-13-1-18-2h-15c-4 1-8 2-12 4v1c-1 1-2 1-2 2-1 2-1 3-3 4-1-1-2-2-2-4 1-3 3-4 5-6l1-1h-1 0c-1 0-2 1-3 0v-1c-3 0-6 1-10 0h1c2-1 4-1 6-1 1 0 2 0 3-1 2-1 4-3 5-4h1c-1 1-2 2-2 3 1 0 3-1 4-1 2-2 3-5 5-7l2-6 1-1c1 0 1 0 2-1 0-1 1-2 1-3z"></path><path d="M772 173c10-1 21 2 31 5-6 2-13-1-18-2h-15c-4 1-8 2-12 4v1c-1 1-2 1-2 2-1 2-1 3-3 4-1-1-2-2-2-4 1-3 3-4 5-6l1-1 15-3z" class="N"></path><path d="M772 173c10-1 21 2 31 5-6 2-13-1-18-2h-15 1l2-1h4c1-1 1-1 3-1 1 1 3 1 5 1 1 1 2 0 3 0h-4c-4-1-8-1-11-1l-1-1z" class="J"></path><path d="M772 153c1 6 5 12 10 15h1l2 2h2-15c-1 0-3 1-4 1-2 0-6 1-7 0 2-2 3-5 5-7l2-6 1-1c1 0 1 0 2-1 0-1 1-2 1-3z" class="W"></path><path d="M769 158c2 1 3 2 4 5 1 2 0 2 2 4l-3 3c-1 0-3 1-4 1v-1h0c3-4 2-8 1-12z" class="V"></path><path d="M768 158h1c1 4 2 8-1 12h0v1c-2 0-6 1-7 0 2-2 3-5 5-7l2-6z" class="E"></path><defs><linearGradient id="AJ" x1="766.04" y1="204.428" x2="768.734" y2="199.336" xlink:href="#B"><stop offset="0" stop-color="#959496"></stop><stop offset="1" stop-color="#b6b6b6"></stop></linearGradient></defs><path fill="url(#AJ)" d="M728 171c6 1 10 3 15 4 4 1 7 0 10 0v1c1 1 2 0 3 0h0 1l-1 1c-2 2-4 3-5 6 0 2 1 3 2 4l3 3 4 3h0c2 1 3 3 5 4h0l11 6c5 2 11 5 16 8l-1 1h-1-2c1 1 2 2 3 2-1 0-2 0-3 1-1 0-5-2-6-3-2-1-6-3-8-3h-1l-2-1c-3-1-5-2-8-2-1 0-2-1-2-1-1 1-1 2-1 4v2c2 3 3 6 5 9v2c-3-3-6-9-9-11v-1c-1-1-2-3-2-4-1-1-2-2-2-4-1 0-2-1-2-2l1-2-3-5c0-1-1-1-1-2h-1v-1c-1-2-6-6-8-6l-13-11v-1h2l1-1z"></path><path d="M725 173v-1h2c2 1 9 2 10 4l-1 1c2 2 5 3 7 5h0c5 4 7 10 14 11h3 0c2 1 3 3 5 4h0c-4-1-8-1-11-3-1-1-3-3-5-3-2-1-3-3-4-4-4-5-7-7-12-10-2-2-5-4-8-4z" class="n"></path><path d="M728 171c6 1 10 3 15 4 4 1 7 0 10 0v1c-2 1-3 1-4 2-2 1-3 2-5 2l-1 1v1c-2-2-5-3-7-5l1-1c-1-2-8-3-10-4l1-1z" class="Q"></path><path d="M737 176c3 1 6 3 9 2h3c-2 1-3 2-5 2l-1 1v1c-2-2-5-3-7-5l1-1z" class="W"></path><path d="M753 176c1 1 2 0 3 0h0 1l-1 1c-2 2-4 3-5 6 0 2 1 3 2 4l3 3 4 3h-3c-7-1-9-7-14-11h0v-1l1-1c2 0 3-1 5-2 1-1 2-1 4-2z" class="Y"></path><path d="M753 176c1 1 2 0 3 0h0 1l-1 1h-2c-1 1-2 1-2 1l-1 1c-2 1-3 1-5 3 0 2 1 3 3 5s5 3 7 3l4 3h-3c-7-1-9-7-14-11h0v-1l1-1c2 0 3-1 5-2 1-1 2-1 4-2z" class="V"></path><defs><linearGradient id="AK" x1="761.116" y1="195.589" x2="781.424" y2="218.813" xlink:href="#B"><stop offset="0" stop-color="#222220"></stop><stop offset="1" stop-color="#515054"></stop></linearGradient></defs><path fill="url(#AK)" d="M753 200c1 0 1-1 2-1 3 0 27 10 32 12 0 1 1 1 1 1 1 1 2 2 3 2-1 0-2 0-3 1-1 0-5-2-6-3-2-1-6-3-8-3h-1l-2-1c-3-1-5-2-8-2-1 0-2-1-2-1-1 1-1 2-1 4v2c2 3 3 6 5 9v2c-3-3-6-9-9-11v-1c-1-1-2-3-2-4-1-1-2-2-2-4 0-1 0-1 1-2z"></path><path d="M752 202c0-1 0-1 1-2h2l1 3c1 2 1 4 1 6l-1 1c-1-1-2-3-2-4-1-1-2-2-2-4z" class="J"></path><path d="M828 185l6 2h2l1 1h2c1 0 3 1 4 1h1l3 3h1l2 1v1l-1 1-2 3-2 2-4 4v1c1 1 1 1 1 3l2-2v2l1 2c0 2 0 2 1 3l2 2c1 1 1 0 2 2h-2c-1-1-1-1-2-1-2-1-2-1-4 0h-3c-2 1-2 1-4 1-1 1-1 2-3 3h0-2l-1-1s-1 0-2 1c-1 0-3-1-4-1h-3l-4-1-3-2-4-2c4-2 8-4 11-8 0 0 1-1 2-1 0-1 0-2 1-3 0-1 1-2 1-2 1-2 1-3 2-4h-1v-1c-2 0-3-1-3-3v-1s-1-1-1-2h2l1-1h1v1h1 1v-1c0-1 0-1 1-2v-1z" class="c"></path><path d="M823 214c2 0 3 0 4 1-1 1-2 1-2 1l-2-1v-1h0z" class="S"></path><path d="M833 200c1-1 1-1 2-1l2-1 3 2v2 1c-1 1-2 2-3 2-1-1 0-1 0-2v-2l1-1c-1 0-1-1-1-1l-2 1h-2z" class="R"></path><path d="M827 215l1 1v-1h3c1-2 4-4 5-6v-1c1-2 3-3 5-4v1c-4 5-7 10-12 14 0 0-1 0-2 1 0-2-1-2-1-3-1 0-2 0-3-1v-1l2 1s1 0 2-1zm6-14h1c0 2 0 2-1 4h2c0 2 0 2-1 3s-2 2-4 3l-2 2c-1 0-2 0-2-1 0-2 1-3 2-5 1-1 3-2 4-2v-2l1-2h0z" class="O"></path><path d="M841 205c1 1 1 1 1 3l-2 2-5 7c-1 1-1 2-3 3h0-2l-1-1c5-4 8-9 12-14z" class="Q"></path><path d="M844 206v2l1 2c0 2 0 2 1 3l2 2c1 1 1 0 2 2h-2c-1-1-1-1-2-1-2-1-2-1-4 0h-3c-2 1-2 1-4 1l5-7 2-2 2-2z" class="Y"></path><path d="M840 210l2 2c0 2-2 3-3 4-2 1-2 1-4 1l5-7z" class="L"></path><path d="M820 219c-1-2-2-2-3-3 1-1 1-2 2-2 2-1 3-3 5-4 1-2 2-3 2-5h0c3-2 4-4 5-7 2 1 4-1 7-1s5 1 9 1l-2 2c-2 0-3 1-5 3v-1-2l-3-2-2 1c-1 0-1 0-2 1v1h0l-2 1v1c-1 2-3 2-4 5l-1 1c-1 1-1 2-2 4l-1 1h0v1 1c1 1 2 1 3 1 0 1 1 1 1 3-1 0-3-1-4-1h-3z" class="F"></path><path d="M823 219v-1c-1 0-2-1-3-2v-2h3v1 1c1 1 2 1 3 1 0 1 1 1 1 3-1 0-3-1-4-1z" class="c"></path><path d="M828 185l6 2h2l1 1h2c1 0 3 1 4 1h1l3 3h1l2 1v1l-1 1-2 3c-4 0-6-1-9-1s-5 2-7 1c-1 3-2 5-5 7h0c0 2-1 3-2 5-2 1-3 3-5 4-1 0-1 1-2 2 1 1 2 1 3 3l-4-1-3-2-4-2c4-2 8-4 11-8 0 0 1-1 2-1 0-1 0-2 1-3 0-1 1-2 1-2 1-2 1-3 2-4h-1v-1c-2 0-3-1-3-3v-1s-1-1-1-2h2l1-1h1v1h1 1v-1c0-1 0-1 1-2v-1z" class="E"></path><path d="M813 216c4-2 6-4 9-7 1-2 3-3 3-4l1-2c2-5 6-8 12-7 2 0 5 1 7 0 2 0 3-1 4-1l-2 3c-4 0-6-1-9-1s-5 2-7 1c-1 3-2 5-5 7h0c0 2-1 3-2 5-2 1-3 3-5 4-1 0-1 1-2 2 1 1 2 1 3 3l-4-1-3-2z" class="G"></path><path d="M828 185l6 2h2l1 1h2c1 0 3 1 4 1h1l3 3h1 0c-3 1-6 0-8-1-3 0-6-1-9 0-1 2-2 4-4 6-1 1-1 2-2 3 0 2-2 4-3 5 0-1 0-2 1-3 0-1 1-2 1-2 1-2 1-3 2-4h-1v-1c-2 0-3-1-3-3v-1s-1-1-1-2h2l1-1h1v1h1 1v-1c0-1 0-1 1-2v-1z" class="K"></path><path d="M839 188c1 0 3 1 4 1h1l3 3c-1-1 0-1-1-1h-3c-1-1-2-1-3-2l-1-1z" class="O"></path><path d="M828 185l6 2h2l1 1h2l1 1h-3c-1-1-3-1-4-1-2 0-3 0-4 1l-2 1-1-1h1v-1c0-1 0-1 1-2v-1z" class="c"></path><path d="M824 188h1v1h1l1 1 2-1c-1 3-1 5-3 7h-1v-1c-2 0-3-1-3-3v-1s-1-1-1-2h2l1-1z" class="R"></path><path d="M824 188h1v1c0 2 0 2-2 3l-1-1s-1-1-1-2h2l1-1z" class="K"></path><defs><linearGradient id="AL" x1="804.698" y1="247.252" x2="783.703" y2="220.851" xlink:href="#B"><stop offset="0" stop-color="#706e71"></stop><stop offset="1" stop-color="#888688"></stop></linearGradient></defs><path fill="url(#AL)" d="M765 222v-2c-2-3-3-6-5-9v-2c0-2 0-3 1-4 0 0 1 1 2 1 3 0 5 1 8 2l2 1h1c2 0 6 2 8 3 1 1 5 3 6 3 1-1 2-1 3-1 2 0 3 1 5 2s5 2 7 3l10 4c1 1 3 2 4 4v2 2c-2 5-5 9-8 12-4 5-8 10-13 13l-1 1h-2l-2-1c-6-4-11-13-15-19-1-1-2-3-3-4l-8-11z"></path><path d="M790 222l2-2h1c1 2 2 3 3 4l-1-1-1 2v-2c-2-1-3-1-4-1z" class="Y"></path><path d="M788 225c-1-1-1-2-1-3l1-1 2 1c1 0 2 0 4 1-2 1-3 1-4 1h-1l-1 1z" class="V"></path><path d="M794 249c-1 0-1 0-1-1v-1h-1l1-1 2 1c2-2 3-2 5-2l1 1h-2c-1 1-1 2-2 3-1 0-1-1-2-1l-1 1z" class="M"></path><path d="M805 228l2 2h0 0l4 1 1 1c-1 2-3 4-5 6h-1v-2c0-1 1-1 1-3l-2-2 1-1-1-1-1 1c-1 0-2-1-3-1h1 1l2-1z" class="X"></path><path d="M771 221v-1c-1 0-2-1-2-1v-1h5c2 1 6 1 7 3 0 2-1 4-1 6h-1-1l1 1v1l-2-1-1-1c-2-2-4-3-5-6z" class="W"></path><path d="M773 221h1l1 1-1 1h-1v-1-1z" class="n"></path><path d="M778 227v-1c0-1 0-2 1-2h1c0 1-1 2-1 3h-1z" class="g"></path><path d="M793 219c3 1 5 3 8 5s8 3 11 5c1 1 1 1 2 3-1 2-2 3-4 4-1 1-1 3-2 4s-2 1-2 2l-1-1h0v2l-1 1c0-2-1-3 0-5l2 1v-2h1c2-2 4-4 5-6l-1-1-4-1h0 0l-2-2h0c-2 0-4-1-5-2v-1c-1-1-2-1-3-1h-1c-1-1-2-2-3-4v-1z" class="M"></path><defs><linearGradient id="AM" x1="786.323" y1="213.845" x2="778.42" y2="221.855" xlink:href="#B"><stop offset="0" stop-color="#716e72"></stop><stop offset="1" stop-color="#858485"></stop></linearGradient></defs><path fill="url(#AM)" d="M782 212c1 1 5 3 6 3 2 1 3 2 5 3v1 1h-1l-2 2-2-1-1 1c0 1 0 2 1 3v1l-1 1-2-1-1 1h0l-1-1-2-5c-1-2-5-2-7-3v-3h1l3 2c1 0 2-1 3 0h4v-1l-6-3 3-1z"></path><path d="M771 208l2 1h1c2 0 6 2 8 3l-3 1 6 3v1h-4c-1-1-2 0-3 0l-3-2h-1v3h-5v1s1 1 2 1v1h-2c-1-2-2-3-3-4s-2-2-2-4c-1-1-1-2-1-3s1-1 2-2c3 1 4 0 6 0z" class="a"></path><defs><linearGradient id="AN" x1="768.402" y1="212.522" x2="769.098" y2="217.478" xlink:href="#B"><stop offset="0" stop-color="#6d6c6e"></stop><stop offset="1" stop-color="#828183"></stop></linearGradient></defs><path fill="url(#AN)" d="M764 213v-1l1 1c1 1 2 1 3 1l2 2v-1l1-1 3 1v3h-5v1s1 1 2 1v1h-2c-1-2-2-3-3-4s-2-2-2-4z"></path><path d="M771 208l2 1h1c2 0 6 2 8 3l-3 1h-1c-1-1-2-2-4-2h-3c-1 0-1-1-2-1-2 0-2 1-4 3l-1-1v1c-1-1-1-2-1-3s1-1 2-2c3 1 4 0 6 0z" class="M"></path><path d="M791 214c2 0 3 1 5 2s5 2 7 3l10 4c1 1 3 2 4 4v2 2c-2 5-5 9-8 12-2 0-2 1-3-1 0-1 1-1 2-2s1-3 2-4c2-1 3-2 4-4-1-2-1-2-2-3-3-2-8-3-11-5s-5-4-8-5v-1c-2-1-3-2-5-3 1-1 2-1 3-1z" class="L"></path><path d="M811 226l-4-1c-2-1-12-7-12-9h1c2 1 5 2 7 3v1c2 3 6 4 8 6z" class="K"></path><path d="M803 219l10 4c1 1 3 2 4 4v2c-1 1-4-2-6-3-2-2-6-3-8-6v-1z" class="H"></path><path d="M765 222v-2c-2-3-3-6-5-9v-2c0-2 0-3 1-4 0 0 1 1 2 1 3 0 5 1 8 2-2 0-3 1-6 0-1 1-2 1-2 2s0 2 1 3c0 2 1 3 2 4s2 2 3 4 3 3 4 5c2 2 3 4 5 6h2c2 2 3 5 4 7v1c2 4 4 6 7 8l3 3 1-1-1-1 1-1c1 0 1 1 2 1 1-1 1-2 2-3h2v1c0 1 0 1 1 2-1 3-4 4-6 7l-1 1h-2l-2-1c-6-4-11-13-15-19-1-1-2-3-3-4l-8-11z" class="O"></path><path d="M791 252c2 1 2 1 3 2v2l-1 1-2-1c1-1 0-2 0-4z" class="R"></path><path d="M766 217c1 1 2 2 3 4s3 3 4 5c2 2 3 4 5 6h2c2 2 3 5 4 7v1l-10-10-5-7c-2-2-2-4-3-6z" class="b"></path><path d="M776 237l1-1c6 5 9 11 14 16 0 2 1 3 0 4-6-4-11-13-15-19z" class="I"></path><path d="M742 198h0c2 1 3 3 5 3l3-1c0 1 1 2 2 2 0 2 1 3 2 4 0 1 1 3 2 4v1c3 2 6 8 9 11l8 11c1 1 2 3 3 4 4 6 9 15 15 19l2 1h2l1-1c5-3 9-8 13-13 3-3 6-7 8-12l1 3-3 7-2 2v4c-2 4-3 7-4 11l-1 8h0l1 3c-1 6-2 12-1 18 2 10 7 19 11 28l1 5c0 1-1 3 0 5 0 1 0 1 1 2 0 1-1 3 0 4v3l-1-3h0v1l-2-2v-1c-1 2-1 3-2 4l-1 1 1 1v3 1l1 2-2-3-2-6h-2c-2-5-3-9-5-13-3-6-7-11-11-15-2-2-4-5-6-6-5-2-9-4-14-5h-2c-2 0-2 1-4 2l-4-1-9 1 6-3c6-3 12-7 17-12l3-4v-1-3l1-1v-2l-2 4v-3l1-2v-4c-3-1-3-2-4-5v-2c-4-12-14-21-20-31-1-2-1-3 0-5l-8-9c-1 1-3 1-4 1h-2v-2-1-2h1l-2-2h-1c0-3-1-5 0-8z" class="f"></path><path d="M800 275c-2-2-2-5-3-7 0-1-1-2-1-3-1-4 2-4 4-7l6-6c-1 2-2 4-3 5-1 2-2 4-4 6v3l2 8-1 1z" class="e"></path><path d="M813 243v4c-2 4-3 7-4 11l-1-2c-2 1-1 0-2 2 0 1-1 3-2 4l-1-1c-1 0-1 1-2 1-1 1 0 1-1 2 0 1 0 1-1 2v-3c2-2 3-4 4-6 1-1 2-3 3-5 3-2 5-7 7-9z" class="O"></path><path d="M804 262c1-1 2-3 2-4 1-2 0-1 2-2l1 2-1 8h-1c0-1-1-2-1-2h-1c-1 2-1 5-2 8-1 2 0 5-1 6-1-1-1-2-2-3l1-1-2-8c1-1 1-1 1-2 1-1 0-1 1-2 1 0 1-1 2-1l1 1z" class="V"></path><path d="M799 266c1-1 1-1 1-2 1-1 0-1 1-2 1 0 1-1 2-1l1 1c-2 4-2 7-2 11l-1 1-2-8z" class="L"></path><path d="M802 278c1-1 0-4 1-6 1-3 1-6 2-8h1s1 1 1 2h1 0l1 3c-1 6-2 12-1 18 2 10 7 19 11 28l1 5c0 1-1 3 0 5 0 1 0 1 1 2 0 1-1 3 0 4v3l-1-3h0v1l-2-2v-1c-1 2-1 3-2 4l-1 1 1 1v3 1l1 2-2-3-2-6-3-10v-1h2 2c-1-1-1-2-1-4l-5-15c2 1 2 3 3 4 0 1 1 2 2 2l-1-1v-2c-1-1-1-2-1-3-1-5-3-10-5-15-2-3-4-5-4-9z" class="n"></path><path d="M802 278c1-1 0-4 1-6 1-3 1-6 2-8h1s1 1 1 2h1 0v3l-1 1c-2 3-1 12-1 17-2-3-4-5-4-9z" class="Z"></path><path d="M742 198h0c2 1 3 3 5 3l3-1c0 1 1 2 2 2 0 2 1 3 2 4 0 1 1 3 2 4v1h-1c-2-1-3-4-7-3 1 0 1 1 2 2 3 2 4 5 7 8 2 3 5 5 7 8 3 3 5 6 7 10 2 2 4 5 6 7 1 2 2 4 3 7 2 2 3 5 5 7 2 3 5 5 6 9l1 1c2 3 4 7 6 11 1 2 2 3 2 5-1-1-3-3-4-5 0-1 0 0-1-1v-1h-2-4c-1-1-2-1-4-1l-3 3c-1 1-2 2-3 2l3-4v-1-3l1-1v-2l-2 4v-3l1-2v-4c-3-1-3-2-4-5v-2c-4-12-14-21-20-31-1-2-1-3 0-5l-8-9c-1 1-3 1-4 1h-2v-2-1-2h1l-2-2h-1c0-3-1-5 0-8z" class="Q"></path><path d="M744 208c2 2 5 3 6 4-1 1-3 1-4 1h-2v-2-1-2z" class="C"></path><path d="M742 198h0c2 1 3 3 5 3l3-1c0 1 1 2 2 2 0 2 1 3 2 4h-1c-2 0-2-1-3-2h-3c-1 0-3-2-4-1v3h-1c0-3-1-5 0-8z" class="a"></path><path d="M781 257l1-1c3 4 2 8 2 13 0 2-1 4-2 6v-3l1-1v-2l-2 4v-3l1-2v-4c-3-1-3-2-4-5v-2l2 1 1-1h0z" class="d"></path><path d="M778 257l2 1 1-1h0l1 7c-3-1-3-2-4-5v-2z" class="U"></path><defs><linearGradient id="AO" x1="791.749" y1="273.219" x2="783.948" y2="264.294" xlink:href="#B"><stop offset="0" stop-color="#787679"></stop><stop offset="1" stop-color="#8f8e8f"></stop></linearGradient></defs><path fill="url(#AO)" d="M784 269l1-1v-6l1-1c1 1 2 2 3 4 0 1 0 1 1 2 2 3 3 6 5 9h-2-4c-1-1-2-1-4-1l-3 3c-1 1-2 2-3 2l3-4v-1c1-2 2-4 2-6z"></path><path d="M782 276c1-1 2-1 2-2 1-1 2-1 3-1h0c2 1 3 1 5 1l1 2h-4c-1-1-2-1-4-1l-3 3c-1 1-2 2-3 2l3-4z" class="M"></path><path d="M758 221c8 10 21 23 23 36l-1 1-2-1c-4-12-14-21-20-31-1-2-1-3 0-5z" class="D"></path><defs><linearGradient id="AP" x1="815.005" y1="312.235" x2="779.186" y2="291.031" xlink:href="#B"><stop offset="0" stop-color="#868486"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#AP)" d="M782 278l3-3c2 0 3 0 4 1h4 2v1c1 1 1 0 1 1 1 2 3 4 4 5l3 6c2 4 4 8 5 13h0l5 15c0 2 0 3 1 4h-2-2v1l3 10h-2c-2-5-3-9-5-13-3-6-7-11-11-15-2-2-4-5-6-6-5-2-9-4-14-5h-2c-2 0-2 1-4 2l-4-1-9 1 6-3c6-3 12-7 17-12 1 0 2-1 3-2z"></path><path d="M808 302h0l5 15-1 1c-3-3-4-10-5-14l1-2z" class="T"></path><path d="M782 278l3-3c2 0 3 0 4 1h4 2v1c1 1 1 0 1 1 1 2 3 4 4 5l3 6c2 4 4 8 5 13l-1 2c-1-3-2-6-4-8-4-3-7-7-11-11h-1-3c0 1-1 2-2 2-2 1-3 1-4 3v1l-1-1-2 2c-1 1-7 0-9 0-2 1-3 1-5 2l-9 1 6-3c6-3 12-7 17-12 1 0 2-1 3-2z" class="R"></path><path d="M785 277h2c1 0 1 0 1 1l-1 1c-2 1-3 2-4 3 0-1 0-1 1-2s1-2 1-3z" class="I"></path><path d="M789 276h4 2v1c1 1 1 0 1 1 1 2 3 4 4 5l3 6c-3-2-3-5-6-7-1 0-1-1-2-2h0c-3-1-4-2-6-4z" class="S"></path><path d="M785 277c0 1 0 2-1 3s-1 1-1 2l-9 6c-1 1-2 1-3 2-3 1-7 3-9 2 6-3 12-7 17-12 1 0 2-1 3-2 1 0 2 0 3-1z" class="C"></path><path d="M771 290h1c2 0 3-1 5-1 1-1 11-9 11-8 1 0 3 2 5 2 4 1 8 9 10 12v1c-4-3-7-7-11-11h-1-3c0 1-1 2-2 2-2 1-3 1-4 3v1l-1-1-2 2c-1 1-7 0-9 0-2 1-3 1-5 2l-9 1 6-3c2 1 6-1 9-2z" class="W"></path><defs><linearGradient id="AQ" x1="822.397" y1="281.075" x2="857.544" y2="288.573" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#57565a"></stop></linearGradient></defs><path fill="url(#AQ)" d="M833 241l2 1h2c1 1 1 1 1 2l1 1 1 1 3 9h1 0c2 6 3 11 6 16 0 1 3 7 2 7 2 5 4 10 4 15h0c-1 2-1 5 0 7l-4-2h-1-1 0l-2-1c-1 1-1 1-1 2h2v1c2 1 3 1 4 1-1 0-2 0-4 1v-1c-2 0-3 0-4 1h1l1 1h-2l-1-1-1 1h1l-1 1s-1 1-1 2c-3 3-5 5-7 8-1 0-2 1-3 2h0c-1 3-3 5-5 6h-2c1 3 1 5 1 8 0-2 0-3-1-5l-1-2c-1-1-1-3-2-4l-2-1c0-2 0-2-1-3-4-9-9-18-11-28-1-6 0-12 1-18 1 2 0 7 1 9v-7c1-1 1-3 1-4 0-3 0-6 1-9l3-5c1-3 3-5 5-8h0c2-2 4-2 7-2v5l-1 1 1 1v-1c1-2 1-3 1-4 1-1 0-1 1-2h0l1-1h2c0-1 1-1 1-1z"></path><path d="M831 250v-1h1v-2h1c0 1 0 2 1 4l-1 1c-1 1-1 2-1 3v-2-3h-1z" class="D"></path><path d="M840 246l3 9c0 1 0 1-1 2l1 1h-2l-1 1v2-1l-2 1 1 3c0 2 0 2 1 4v2h0c0 1-1 2-1 3 1 0 1 0 1 1v3c1 1 1 1 2 3 0 1 0 1 1 2h1v1h-1c0-1-1-1-2-2l-1-1h1c-1-2-2-3-3-4 1-1 0-3 0-5v-1l-1 1c-1-2-1-5-1-7 1-1 1-1 1-2v-1c-1 0-1-1-1-1 0-1 1-2 1-2 0-1-1-3-1-4 1 0 1 0 2-1h1c0 1 1 1 2 1h0l-1-2c-1-2 0-4 0-6z" class="F"></path><path d="M843 303h1l-1 1s-1 1-1 2c-3 3-5 5-7 8-1 0-2 1-3 2h0c-1 3-3 5-5 6h-2c0-3-2-6-3-9h2l2 1h1c1 0 1 0 2 1 2-1 7-8 9-10 2-1 3-2 5-2z" class="L"></path><path d="M822 313h2l2 1c-1 1-1 2-1 4l2 1c2-1 3-2 5-3-1 3-3 5-5 6h-2c0-3-2-6-3-9z" class="U"></path><path d="M833 241l2 1h2c1 1 1 1 1 2l1 1 1 1c0 2-1 4 0 6l1 2h0c-1 0-2 0-2-1h-1c-1 1-1 1-2 1 0 1 1 3 1 4 0 0-1 1-1 2 0 0 0 1 1 1v1c0 1 0 1-1 2 0 2 0 5 1 7-1 0-1-1-2-1 0-2-1-4 0-6v-1h-1c0-1-1-1-1-2-1-1-1-3 0-4v-5l1-1c-1-2-1-3-1-4l1-1h-1c0-2-1-3-1-4s1-1 1-1z" class="K"></path><path d="M833 241l2 1v1c1 0 1 1 2 1v2h0l2 2h0c-1 1-1 1-2 1h-2-1l1 2h0c-1 2 0 4-1 6s0 4 0 6c0-1-1-1-1-2-1-1-1-3 0-4v-5l1-1c-1-2-1-3-1-4l1-1h-1c0-2-1-3-1-4s1-1 1-1z" class="H"></path><path d="M843 255h1 0c2 6 3 11 6 16 0 1 3 7 2 7h-1v-1c0-1-1-2-1-3h-1l-1 2h1c2 3 2 7 3 11v2h-1c-1-1-3-3-4-3-1-1-1-1-1-2l-1-1v-2-1h-1l-1-1c0-2-1-3-2-5h0v-2c0-1 0-2-1-2h0v-2c-1-2-1-2-1-4l-1-3 2-1v1-2l1-1h2l-1-1c1-1 1-1 1-2z" class="T"></path><path d="M843 255h1 0l-1 4h-1c-2 2 0 2-3 3h1c1 2 1 3 1 5 0 1 0 3 1 4 0 1 1 2 1 3 0 2 1 3 1 5l1 1h-1l-1-1c0-2-1-3-2-5h0v-2c0-1 0-2-1-2h0v-2c-1-2-1-2-1-4l-1-3 2-1v1-2l1-1h2l-1-1c1-1 1-1 1-2z" class="L"></path><path d="M830 242h2c0 1 1 2 1 4h1l-1 1h-1v2h-1v1c0 1 0 2-1 4 0 1 0 1-1 3 0 1 0 2-2 3v1c1 1 0 1 0 2-1 6-2 12-2 18-1 6-2 11 0 17v3c0 3 2 8 4 11v2h-2-1l-2-1v-1h0c2-2-1-8-1-10-1-3-1-5-1-7l-1-7c0-13 2-25 6-38v-1c1-2 1-3 1-4 1-1 0-1 1-2h0l1-1z" class="B"></path><path d="M830 242h2c0 1 1 2 1 4h1l-1 1h-1v2h-1v1c0 1 0 2-1 4v-5h0l1-2c0-1-1-1-2-2l-2 4c1-2 1-3 1-4 1-1 0-1 1-2h0l1-1z" class="E"></path><path d="M830 242h2c0 1 1 2 1 4-1-1-2-1-3-1l1-2-1-1z" class="D"></path><path d="M827 243v5l-1 1 1 1c-4 13-6 25-6 38l1 7c0 2 0 4 1 7 0 2 3 8 1 10h0v1h-2c1 3 3 6 3 9 1 3 1 5 1 8 0-2 0-3-1-5l-1-2c-1-1-1-3-2-4l-2-1c0-2 0-2-1-3-4-9-9-18-11-28-1-6 0-12 1-18 1 2 0 7 1 9v-7c1-1 1-3 1-4 0-3 0-6 1-9l3-5c1-3 3-5 5-8h0c2-2 4-2 7-2z" class="I"></path><path d="M822 295c-1 1-2 2-3 2-2-1-3-4-4-6-1-5-1-10-2-14 0-7-1-14 2-19-1 3-1 8-1 11s0 5 1 7v2c0 5 0 12 3 16l1 1 2-2h0c-1-2 0-3 0-5l1 7z" class="F"></path><path d="M811 267c0-3 0-6 1-9v5 7c0 5 0 11 1 16l1 1v1h0c1 4 2 7 4 10 1 3 2 6 3 8h2c-1-1-1-2-2-2l2-2c0 2 3 8 1 10h0v1h-2-1v-3c-9-13-10-28-10-43z" class="H"></path><path d="M809 269c1 2 0 7 1 9v-7c1-1 1-3 1-4 0 15 1 30 10 43v3h1c1 3 3 6 3 9 1 3 1 5 1 8 0-2 0-3-1-5l-1-2c-1-1-1-3-2-4l-2-1c0-2 0-2-1-3-4-9-9-18-11-28-1-6 0-12 1-18z" class="N"></path><path d="M827 243v5l-1 1 1 1c-4 13-6 25-6 38 0 2-1 3 0 5h0l-2 2-1-1c-3-4-3-11-3-16v-2c-1-2-1-4-1-7s0-8 1-11c2-2 2-6 4-9h0c0-2 1-2 1-4h0c2-2 4-2 7-2z" class="V"></path><path d="M819 249l3-3c1-1 1-1 3-2l1 1c0 1-1 4-1 5-1 0-1 0-1-1-2 0-3 1-4 2 0 1 0 1-1 2l-2 5h0c-1 2-2 5-2 6v3l-1 2c0-3 0-8 1-11 2-2 2-6 4-9z" class="b"></path><path d="M824 249c0 1 0 1 1 1-1 3-1 4-1 7l-1 1c-1 2-1 4-1 5-1 4 0 7-2 10v3c-1 2-1 4-2 4h-1c-1-5-1-10 0-14v-2-1c0-1 0-3 1-4l3-8c1 0 2-1 2-2h1z" class="W"></path><path d="M827 243v5l-1 1 1 1c-4 13-6 25-6 38 0 2-1 3 0 5h0l-2 2-1-1c-3-4-3-11-3-16 1 2 1 4 2 7 0 1 1 3 2 4 1-4 1-8 1-13v-3c2-3 1-6 2-10 0-1 0-3 1-5l1-1c0-3 0-4 1-7 0-1 1-4 1-5l-1-1c-2 1-2 1-3 2l-3 3h0c0-2 1-2 1-4h0c2-2 4-2 7-2z" class="R"></path><path d="M273 168c4 4 7 5 12 6 4 0 6-1 9-3 0 1 0 2 1 3 1 0 2 1 3 1l1-1h7 6 0c2-1 5-1 7-2-1 1-2 2-3 2l2 1c1-1 2-1 4-1h0c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2c1 0 2 1 2 2-1 1-2 1-3 0v1c5 2 10 6 15 10l4 2c1 1 3 2 4 3l10 9c1 0 2 1 3 1l1-1c2 1 2 1 4 1h1c1 2 2 4 3 5h1c3-1 7-1 10 0 0 0 0 1 1 1h8c8 0 16 1 24 1 2 1 5 0 7 1h38c3 1 7 1 11 1 5 0 11 0 16-1h1l16 27 5 7c2 4 4 8 4 13v13c2-3 1-5 2-8 0-2 0-5 1-7v-3-5-1c2 0 2-1 3-3v-1h1l1 1h0l1-1c1-1 1-2 2-3h-1c2-1 3-3 4-4 4-4 7-8 10-13l7-13 1 1c2-2 2-2 4-3h2 10 17 44c5 0 11 1 16 0v-1l20 1h9 8l1-2c1-1 5-7 6-8l4-2c1-1 2-1 3-2 2 0 4-2 5-3l16-11c2-1 7-4 7-6v-1c0-4-3-6-5-8 3 0 3 1 5 4v1-1l1-1c-1-2-4-3-5-5v-1l5 4c1 1 2 1 4 1l1-1c1 1 2 1 3 2l6 7h-2c5 4 9 7 10 13 0 2 0 4-1 6 0 1 0 1 1 2v-1c-1-2 0-6 0-9v-5c1 1 3 2 4 3s2 2 4 2c-1 3 0 5 0 8h1l2 2h-1v2 1 2h2c1 0 3 0 4-1l8 9c-1 2-1 3 0 5 6 10 16 19 20 31v2c1 3 1 4 4 5v4l-1 2v3l2-4v2l-1 1v3 1l-3 4c-5 5-11 9-17 12l-6 3 9-1 4 1c2-1 2-2 4-2h2c5 1 9 3 14 5 2 1 4 4 6 6 4 4 8 9 11 15 2 4 3 8 5 13h2l2 6 2 3v8l1 2 1 1-1 1v1 4c0 2-1 4 0 6 0 3-1 6-1 8v1c-1 3-4 7-5 10 0 1-1 1-1 2h1l-5 2v-1l-1-1v-3l1-2-1-1c-4 6-10 10-16 15l-7 6c-4 4-9 7-14 10-4 3-8 5-11 9v7l1-1-6 25h1l1-1 2-2h1l1 1h0c-3 3-8 7-10 11-1 3-4 5-4 9h-1c0 2 1 3 2 5v2c-1 2-3 7-3 8l-1 3v1c-1 1 0 3 0 5h-1v-3c0-2 0-4-1-5-2 1-4 0-6 0-3-3-5-6-5-9l1-1 1 2h0c0-1-1-2-1-2l-1 1c-1 1-1 2-1 3h0l-1-2h0v3l-1 1-1-1-1 1v7c0 3 0 6-1 8v2h1v1l-2 2c0 3 0 6-1 10 0 1 1 3 0 5 0 2 0 8-1 10 0 2 1 4 0 6l-1 1v1l-1 9c-2 6-1 15-5 20-1-1-1-2-1-3l-3 12v4 4c0 1-1 2-2 2v3c0 1-1 1-1 2-1 2-1 3-2 6 0 1-1 2-1 3l1 1c-1 1-1 2-1 4l-1 1-1 1v2 1c-1 2-2 5-3 6v1l-1 1-1 3h0l1-1v2 1l1-2c1 2 1 3 1 5l1 1c0-1 0-1 1-1h0c1 1 1 1 2 1v2c-2 1-2 3-3 5l-2 3c-2 4-5 8-7 13l-2 2-3 4v1c-1 1-1 3-3 4-1 1-1 2-2 3 1 1 2 1 2 2h1l3 3c1 1 0 2 0 3 0 3-1 5-2 7l-2 1c0 2 0 2-1 4 1 0 1 0 2 1l-8 11-9 12-2 2-1 2c-2 2-3 5-5 7v1c-8 12-17 24-25 36l-73 103-21 32c-3 3-6 6-8 10l-6 9-16 24c-1-1-4-6-5-7-1-2-2-4-4-6-1-1-1-2-2-3-3-3-5-6-6-9v-1l-8-13c-1-1-2-3-4-4v-5c-1 0-3-3-3-4l-1-3-4-4 1-2h2c-1-2-2-4-3-5v-1l1-1-1-1 1-1c-1 0-1 0-1-1 2 1 2 2 4 2-3-4-5-7-7-11-2-1-3-2-4-3-1 1-1 1-2 1l-5-8c-1-1-1-2-2-3s-2-1-3-2c-4-3-4-5-6-10l1-4c-1-2-2-3-3-5-3-5-7-10-10-15-1-2-2-4-4-5-2 0-3 0-5-1s-3-2-5-3c-2-2-3-6-3-9l2-2 3-3c1-1 2-1 3-1l3 1c0-1-2-4-3-5h1c1 0 1 1 2 1h2 0l-3-3c-3-4-6-8-8-12-4-5-8-11-12-16l-17-23h-1c-1-1-3-2-5-2h0v-1c-2 0-5-4-6-5 0-1 0-1-1-2l-2-3c-1-1-3-2-4-4h0l-1-2-1 1v1h0l-2-1c-1 0-2-1-3-1h0c-4-3-6-7-9-11l-2-1-1-1c-3-2-4-4-6-7v-2l1-2h-1v-2c2 0 2-1 3-2 0-2 0-4 1-6v-1h-1c0-2-1-3-2-4 0-1 0-2 1-3l-3-5h1s0 1 1 1c-3-5-5-10-9-15h0c-2 0-3-3-4-4l-1-1c-1-2-1-3-2-4l-1 1c-2-3-3-6-5-9-3-6-7-12-9-18 0-3-2-5-3-8l-6-20c0-2-2-4-2-7l-5-28v3 2 1c1 1 0 6 0 8h1v1 1c0 1 0 2 1 3v2c1 0 1 0 1 2v2h1v3c0 1 1 2 1 4l-1-1v-2 2l-1-1h0c0-2 0-2-1-3v-1-2-1c-1-1-1-2-1-3-1-1 0-3 0-4l-1-1v-1c-1-2 0-4 0-5-1-1-1-3-1-4 0-3-1-6-1-9h0c-1-1-1-3-1-4-1-1 0-4 0-6 0-1-1-1 0-2v-1c-2-1 0-5-1-7 0-1-1-1-1-2v-4c-1-4-1-9-1-14 0-2 0-4-1-6l-1 1v-1c0-1-1-2-1-3v-1c-1 3 0 6-1 8v-16c0-2 1-4 0-6l1-1-2-1c0-2-1-4-1-6v-2c0-1 0 0 1-1v-1c0-1 0-1-1-2s-2-1-3-1v-4h0v-2h-1l1-1c0-1 1-2 1-3v-3c1-1 2-2 2-4 1-4 1-7 3-11v-1c1-3 0-7-1-11v-3h0c-1 2-1 3-1 4v1c-1-1-1-2-1-3h-1v1c-2-3-4-6-7-7l-3-1c-4-1-8-1-12 1-2 1-5 4-5 7-1 2-1 5 0 7l-2-1c-3-7-9-10-15-15h-1c-9-4-17-13-25-19l-3-3-1-1c-2-3-3-5-4-8-3-6-4-11-5-17h2v-2c-1-3 0-9 0-12l-1-1h-1 0l3-15v-3l1-4v-1-1l1-5h-2v-5-1c-1 0-2 0-3-1l2-5 1-3c2-5 4-9 6-13 1-3 3-6 4-9l1-4 1-3c1-2 2-7 2-10h-1c0-2 0-2-1-3 0-1-1-1-1-2l-1-1-1-1c-5-3-8-11-12-16l-5-8-1-3c0-1 0-2-1-3h0c5-4 10-8 16-9l1-2c2-1 4-2 7-3 2 0 3-1 4-2l3-1c1 0 2-1 3-1 3-1 5-3 8-4 7-3 13-7 20-11l2-2v-1c1-1 1-1 2-1l-1-1h-1v-1l2-1 2-2 2 1c1-1 1-2 1-3-2-4-7-5-11-7 2 0 4 0 6 1l2-2h1c3 1 5 1 8 2l-1-2c0-2-1-2-2-3v-3z" class="q"></path><path d="M540 803h2c1 1 1 1 1 3h-1c-1-1-1-1-2-1h-1l1-2z" class="B"></path><path d="M618 585h2c0 1 0 1-1 2-1 0-2 1-3 1 0-2 0-2 2-3z" class="l"></path><path d="M491 824h2c-1 1-2 1-2 2l-2 1v-1-2h2z" class="J"></path><path d="M573 643h2c-1 1-2 2-2 3l-2 1h0c0-2 1-3 2-4z" class="B"></path><path d="M651 468c1 1 2 2 2 4-1 1-1 1-3 2h0v1l-1-1c1-1 1-1 2-1l1-1v-1l-1 1-1-1c0-1 1-2 1-3z" class="G"></path><path d="M493 830c-1 0-2 1-2 1-1 0-2-1-2-2l1-1 2 1c1-2 1-2 3-3-1 1-2 2-2 4z" class="N"></path><path d="M383 256c1-1 1-1 2 0h0l-2 2h-2v-1h-1c1-1 1-1 3-1z" class="G"></path><path d="M750 392h1l1 2c0 2 0 3-1 4l-1-1v-5z" class="E"></path><path d="M472 807l1-1 3 4 1 1v1h-1c-2-2-3-3-4-5z" class="N"></path><path d="M339 575l1-1 2 2v1 1c-2 0-2-1-3-2v-1z" class="i"></path><path d="M436 579l1 1h1c-1 1-2 1-3 1h0v1c-2 0-3 0-4-1l1-2c2 1 2 1 4 0z" class="N"></path><path d="M564 696l2 2-1 3c-1 0-2 0-3-1 1-1 2-3 2-4z" class="l"></path><path d="M526 809c1-1 2-3 4-4l2 2h-1c-1 1-1 1-1 2h-3-1z" class="J"></path><path d="M410 560c2 2 2 4 4 5l-1 2h-2v-1l-1-1c1-1 0-3 0-4v-1z" class="B"></path><path d="M369 581l1 1v2h-1c0 2 0 3-1 5l-2-1c0-1 0-1 1-2l1-1c1-1 1-2 1-4z" class="H"></path><path d="M495 808v-3c-1-2-1-4 0-6h1c0 2 0 4 1 5v1c0 1-1 2-1 2l-1 1z" class="E"></path><path d="M635 641c1-1 2-1 3-2 1 0 2 0 2-1h1c1 0 2 0 3 1-3 1-5 2-9 2z" class="B"></path><path d="M518 839c2-1 3-1 5-1-1 2-1 4-3 5 0-2-1-2-2-4z" class="v"></path><path d="M565 635c2-1 2 0 3 0v2 1h0c-2 1-2 0-3 0-1-1 0-2 0-3z" class="o"></path><path d="M429 712h1v1l1-1h1 0v1l-1 2c-1 1-2 1-3 2l-1-1 2-4z" class="J"></path><path d="M371 559l2-1 2 2h1v1h-2v2 1c-1-1-2-3-3-5z" class="M"></path><path d="M445 654c1-2 0-2 1-3l1 1c1 1 0 1 2 1 1 1 3 2 4 3l1 1h-1v-1c-1-1-2-1-3-1h-2-1l-2-1z" class="N"></path><path d="M613 670l1 3c0 2 0 3 2 4h1l-2 2c0-1-3-1-3-2s1-3 1-4-1-1-1-1l1-2zM314 392l2 1c0 2 0 2-1 4 0 1 1 2 1 3h-1v1 1l-1 2v-12z" class="B"></path><path d="M283 281h3c1 2 1 3 1 4h-2c-2 0-2-1-2-2v-2z" class="h"></path><path d="M443 603c2 1 3 2 4 4v1h0v2c-1 0-1 1-3 0h1c0-2 0-2-1-3l-1-4z" class="E"></path><path d="M591 530l1-1h3v1h1l-9 5c1-2 3-3 4-5z" class="w"></path><path d="M338 605h1c1 1 2 2 2 3 0 2-1 2-2 3-1-1-2-2-2-4l1-2z" class="D"></path><path d="M735 281h0c1 0 2 0 2 1 0 2-1 3-2 4-1-1-2-1-2-2l-1-1c1-1 1-1 3-2z" class="h"></path><path d="M597 524l-2-1h1c1-1 1-1 2 0h3c1 2 1 2 2 3h1l1 2c-1 0-1-1-2-1l-1-1c-1-1-2-1-3 0 0-1-1-1-2-2z" class="H"></path><path d="M670 269c1-2 1-3 3-4 1 0 1 0 2 2-1 1-1 2-1 3l-4-1z" class="J"></path><path d="M432 737h0c2 1 2 2 2 3 0 2 0 3-1 4h-1l-1-2c-1-2 0-3 1-5z" class="P"></path><path d="M495 808l1-1s1-1 1-2l1 1h2c0 2-2 4-3 5l-1 1h-1v-4z" class="B"></path><path d="M562 700c1 1 2 1 3 1-1 3-2 5-3 7l-2-2h0l2-6z" class="s"></path><path d="M596 563c3 1 5 0 8 0h0c1 1 1 2 2 3h2c0 1 0 2-1 3h-1c-4-7-6-3-10-6zm-41 118c3-1 7 2 10 2l1 1v1h-1c-3 0-8-2-10-4z" class="B"></path><path d="M272 244h5c1 0 3 1 4 2v1c-2-1-6-2-9-1-2 1-2 2-2 3 0-1-1-3 1-3 0-1 0-1 1-2z" class="F"></path><path d="M638 366v2c-1 2-1 3-3 3-1 1-2 1-2 1h-2l-2 2-1-1 10-7z" class="k"></path><path d="M321 537l2-1v1 2c1 1 1 1 2 1 1 1 1 1 1 2-1 2-1 3-2 5 0-1-2-4-2-6 0-1 0-3-1-4z" class="H"></path><path d="M436 708h1c0-1 0-1 1-1v1c-1 1-3 3-5 4h-1 0-1l-1 1v-1h-1l3-4 2 1h0c1 0 1-1 2-1z" class="B"></path><path d="M351 488l3 6 1 1c0 1 1 3 2 4l-2 2h0c-2-4-3-8-4-13z" class="t"></path><path d="M373 526l3-2v1c-1 1-2 1-3 2l1 2-4 1c-1 0-1 0-2-1 1-2 3-3 5-3z" class="L"></path><path d="M371 535c1-1 3-1 5-1 0 1 1 1 1 2s0 3-1 4l-5-5z" class="k"></path><path d="M410 657l1-2h1s0 1 1 2l1 1 1 1v1h0v2c0 1 0 1-1 2l-1-1-1 1v-2-1-3l-2-1z" class="B"></path><path d="M444 788h3l1 1v-1l4 6h0c-1 0-1 0-1-1h-1-3l-3-5z" class="X"></path><path d="M602 509l-2-1c-3-1-6-2-8-5l1-1 6 2-1 1 1 1s1 1 1 2c1 0 2 0 2 1z" class="B"></path><path d="M727 295v1c1 1 0 3 0 5 1 3 1 8-1 12h0v-3l1-15z" class="G"></path><path d="M561 656c1-1 3-2 3-2h2c1 0 2-1 3-1h0c2 0 3 0 4-1l1 1c-2 0-2 1-3 1v1l-1-1v1c-2 1-3 1-4 1l-3 1c-1 0-1-1-2-1z" class="J"></path><path d="M493 253l1-1c2 3 4 7 5 10 0 0-1 0-2-1h0l-4-8z" class="G"></path><path d="M410 657l2 1v3 1 2l1-1 1 1c-1 2-1 2-1 5h-1-1l-1-12z" class="E"></path><path d="M539 223c2-2 2-2 4-3v1c3 0 7 0 9 2-4 0-9 1-13 0z" class="g"></path><path d="M670 269l4 1c0 1 0 0-1 1v1c1 0 0 0 1 1-1 1-3 2-4 3h0-1v-3c1-1 1-3 1-4z" class="D"></path><path d="M334 541h3c1 1 3 1 3 3l-1 1-1 1c-1 0-1 0-2-1s-2-3-2-4z" class="I"></path><path d="M511 837h5l2 2c1 2 2 2 2 4 0 1-1 1-1 1-1 0-4-5-6-6-1 0-3-1-4-1h2z" class="AB"></path><path d="M734 314c2 1 2 1 3 2s1 1 0 2l-1 1v1h-1c-1-1-2-1-3-1h0c0-2 0-2 1-3v-1l1-1z" class="h"></path><path d="M318 503h1 1v2h0l1 1-1 1 1 2v1l1-2c0-1 0-2 1-3h0 0v1c0 2-1 3-2 5 0-1-1-1-1-2-1-1-3-3-4-5 1-1 1-1 2-1z" class="E"></path><path d="M611 585h1c0-1 0-2 1-2 1-1 1-1 1-2l1-1c1-1 2-1 4-1l-1 3c1 1 2 1 2 1v1h-1c-2-1-2-2-4-1l-3 3-1-1z" class="B"></path><path d="M432 708l2-10c1 3 2 6 2 10-1 0-1 1-2 1h0l-2-1z" class="m"></path><path d="M666 257l-1-3h0c2 0 5 2 6 4l-3 6c0-4 0-5-2-7z" class="Q"></path><path d="M750 392c-1-3-1-6 0-8 1-1 1-1 2-1v11l-1-2h-1zM420 577l12 2-1 2c-2-1-4-2-6-2-1 0 1 0-1 0h-1l-1 1-1 1c-2-1-2-1-3-2 1-1 1-1 2-1v-1zm102 233c0-1-2-5-2-6 1-2 2-5 4-6v1c0 2-1 3-1 5-1 2-1 3 0 5h1l-2 1z" class="B"></path><path d="M695 543c-1-3-2-5-5-8l7 2c1 1 1 2 1 3l-3 3z" class="H"></path><path d="M411 239v1c-3 2-6 5-9 8v-1c0-2 1-3 2-5 2-2 4-2 7-3z" class="g"></path><path d="M361 544l2 1c3 3 4 6 6 10l-1 1c-3-4-5-8-7-12zm357-253l2 1c0 6 1 13 0 20-1-5 0-10-2-14v-7z" class="F"></path><path d="M398 646c-3 0-6 1-8 1 0-2 0-2 1-4h0v-1c1 0 1 0 1 1 1 0 4 0 5 1 0 1 0 2 1 2z" class="s"></path><path d="M640 606c2-1 3-1 4 0 2 0 5-1 7 1v3h-1c-1-1-2-1-2-2h1v-1h-2c-1 1-1 0-2 1v1s1 1 1 2c-1 1-2 1-3 1v-1h2v-1c-1-1-1-2-1-3h-2c-1 0-1 1-2 1v-2z" class="B"></path><path d="M474 748h0 1c-1 2-2 3-2 5v1l-1 1c0 1 1 2 2 3l-1 1-3 1h-1-1c0-3 2-3 3-5 1-3 2-5 3-7z" class="D"></path><path d="M359 535l3 6c0 2 0 2 1 4l-2-1-6-9c1 0 2 0 3 1h1v-1z" class="U"></path><path d="M631 372h2l1 1c1 0 2 0 3-1h2l-10 7c-1-1-1-2-1-3l4-3-1-1z" class="f"></path><path d="M725 255c2-1 3-1 3-3 1-1 2-1 3-1h1c1 3 3 3 4 5-4 0-7-1-11-1z" class="y"></path><path d="M382 522h0c-1 1 0 1-1 2l1 1c-2 1-5 3-8 4l-1-2c1-1 2-1 3-2v-1l6-2z" class="m"></path><path d="M611 572c0 2 1 5 0 8-2 1-3 2-4 3v-2c0-2 0-3 1-5 0-1 2-3 3-4z" class="AB"></path><path d="M638 545l2 3c0 1 0 2-1 4h-2-1l-1-1-2-1 5-5z" class="l"></path><path d="M472 761c7 1 13 2 18 7v2c-3-5-13-7-18-8v-1z" class="B"></path><path d="M621 272l14 7c-5-1-10-2-15-4l1-3z" class="H"></path><path d="M496 771l1-1 2 2c-1 1-1 1-1 2 1 2 2 5 4 6-1 2-2 3-3 4-2-3-2-9-3-13z" class="z"></path><path d="M392 657l2 1h-1v3 1l-1 1 2 2c1 0 0 0 1 1v2c-2-1-4-1-5-2 0-2-1-2-2-3h1 2v-1-2c0-1 0-2 1-3z" class="P"></path><path d="M627 253c2-3 4-5 7-5l-1 1h1l1 1h-2 0l-2 2c-2 1-3 2-3 4h-2-3-2-1c-1 1-2 0-2 0h0l1-1h2 4l2-2z" class="D"></path><path d="M643 622c1 0 2 1 2 3l4 4c0-1 1-1 1-2h1l1-2v3c-1 2-3 3-5 4h0-1c0-2 0-3-1-4 0-1-1-2-1-3-1-1-1-2-1-3z" class="N"></path><path d="M560 695h1v2c-1 2-1 3-2 5 0 1-1 2-1 3v1l-1 1c-1-1-1-4-1-5s1-2 2-2c0-1-1-2-1-2 1-2 1-2 3-3z" class="J"></path><path d="M294 438v-6l2 2c1 2 0 4 0 6v11c-1-2-1-4-2-7v-6z" class="D"></path><path d="M642 245c5 3 9 8 12 12 0 1 1 2 1 3h0-1c-1-1-1-2-1-2 0-2-2-3-3-5h-1-1-2l-1-1c-1 0-2-1-3-2l2-1 3 2h0 1l-2-2h-1v-1h-1-1l-2-2 1-1z" class="G"></path><path d="M736 397c0 2 1 3 2 4l1-1h-1l1-1 1 1c0 2-1 2-2 4h0c-1-1-2-2-3-4v1c-1 1-1 1-1 3 0 1-1 3-2 4 1-3-1-9 1-11l1 1h1l1-1z" class="B"></path><path d="M691 572h1c1 3 1 5 0 8v5l1 1h-1c-2-2-2-5-2-8-1-2-1-4 1-6z" class="N"></path><path d="M459 793c2 2 3 3 4 6h1c1 1 3 5 3 6 1 1 1 3 1 4-4-4-6-9-10-14 1-1 1-1 1-2z" class="E"></path><path d="M628 642l1 1h0c0 2 0 3 1 4 0 1 0 1 1 2 0 2 1 3 0 5-1-2-1-2-1-4-2-1-4-2-6-2-2-1-4 0-6-1 2-2 5-1 8-1 2-2 2-2 2-4z" class="AB"></path><path d="M298 450c0-3 1-5 1-8v-10c-1-4-1-8 0-12 1-1 1-2 1-3v20c0 3 0 7-1 10l-1 3zm303 232c-1-1-1-1-1-2v-1-3c-1-1-1-2-1-3 3-1 3-2 5-3v1l1 1-2 4v2c-1 2-2 3-2 4z" class="B"></path><path d="M372 259c1 1 0 1 1 2 0 1 1 2 1 2 0-1 1-1 2-2v1c1-1 2-2 4-2h0c1 1 1 1 1 3v2c-1 1-1 1-2 1-2-1-2-1-3-2h-1l-4-2c0-1 0-2 1-3z" class="G"></path><path d="M606 663l-1-1c-1-1-1-2-2-3h0 2c1-1 1-1 1-2-1-2-1-2-2-2l-1-1h0l1-1c1-2 2-2 4-2 1 1 0 1-1 3 0 0 1 1 1 2-1 1-1 1-1 2s0 1 1 2l1 1c-1 1-2 2-3 2z" class="l"></path><path d="M431 657c-1-1-3-2-4-3l1-2h0c2-1 6 3 8 4 1 2 2 2 2 4l-1 2-2-2c-1-2-2-3-4-3z" class="f"></path><path d="M414 565c2-1 3-1 4 0 2 0 4 0 6-1l1-1v1l-3 2h0l2 1h-2c-2 0-2 1-3 0s-2-1-3-1h0l1 1s0 1 1 2l-2 1v-1h-2l-1-1c-1 0-1 0-2 1l-1-3h0v-1l1 1v1h2l1-2z" class="E"></path><path d="M672 553l2-2s1 0 1-1c0 4 0 8 3 11h0l-3 2c-1-3-3-6-3-10z" class="S"></path><path d="M321 537c1 1 1 3 1 4 0 2 2 5 2 6l1 2c-1 0-3-1-4-2s-1-1-2-1h-1 0l-1-1c2-3 2-6 4-8zm108 60l4 1 8 4v2l-1 1v1l-3-2c-1 0-1 0-2-1l-2-1c-1 0-1 0-1-1h1c-2-1-2-2-3-3l-1-1z" class="C"></path><path d="M429 597l4 1c1 2 3 3 4 6h0c-1 0-1 0-2-1l-2-1c-1 0-1 0-1-1h1c-2-1-2-2-3-3l-1-1z" class="F"></path><path d="M497 261h0c1 1 2 1 2 1 2 3 6 11 5 14l-1 1-6-16z" class="H"></path><path d="M638 228h13 11v1h-17-24c5-1 11-1 17-1zm-286 42c0-3 0-5-1-7v-1c0-1-1-2-2-3v-1l2-2v1h0v1h2s0 1 1 1h0v1c0 1 1 1 2 1l2-1h1l-1 2c-3 0-4 2-5 4-1 1-1 2-1 4z" class="B"></path><path d="M326 458l1-1-6-6h0c4 3 11 6 12 11v1c-2-1-3-3-5-4l-5 2c-2 1-4 2-5 4 1-4 5-5 8-7z" class="Q"></path><path d="M337 560c1-1 2 0 3 1 2-2 3-5 3-8 1 2 2 3 3 4 0 2-2 3-3 6h1l-1 1c-1 0-1 0-2-1l-5-2 1-1z" class="L"></path><path d="M411 588c6 3 12 5 17 8-4 1-7 0-10-2l-5-2v-1c-1-1-2-2-2-3z" class="f"></path><path d="M346 526l2 1h1c2 2 4 3 7 5l1 2c0-1 1-2 2-2v3 1h-1c-1-1-2-1-3-1-4-4-8-7-14-8 1-1 3 0 5-1z" class="L"></path><path d="M491 782h2v4l-1 1c-1 2-3 5-6 6l-1 1c-1 1-2 2-3 2h-1c-1 1-2 1-4 1-1 0-1-1-2-1 1-1 4 0 6-1h1l1-1c4-1 5-4 6-7l2-5zm189-441v3c1 2 1 4 1 6 1 2 1 4 1 6-2 0-2 1-3 2v-7l-1-1c0-1 0-2-1-3l3-6z" class="B"></path><path d="M680 344c1 2 1 4 1 6l-2-2c1-1 1-2 1-4zm-38 273v-3c-1-1-2-3-1-5h0l2 1v1h-1v-1 2 1h2 1 4v-1c0 1 0 1 2 1h0c1 0 2 1 3 1l-1 1c0 1 2 1 1 2-1 3 0 5-2 8l-1 2h-1 0l1-1c-1-1-1-2-1-2 2-2 1-4 3-6 0-2-1-2-2-4h-8v3h-1z" class="J"></path><path d="M395 670c2 1 3 1 4 3 1 0 1 1 2 1l1 1c1-1 1 0 2-1h0c2 0 3-1 4-2l-2 6c-1 0-1 1-2 1h-2c-3-3-5-6-7-9zm25-441h0-15l-26 1c-2 0-6 0-8-1 2-1 27 0 31 0 4-1 8-1 13-1 1 1 3 1 5 1z" class="B"></path><path d="M736 248l3-2c1 1 1 0 1 1s1 1 1 2c-3 3-3 4-4 8l-1-1c-1-2-3-2-4-5l1-1 3-2z" class="AF"></path><path d="M627 253c1-2 1-3 2-4 0-1 1-2 1-3 1-1 1 0 2-1 1 0 1-1 3-2l1 1h1l-1-2v-1c2 2 4 3 6 4l-1 1-1-1c-1 0-1-1-2-2v1l3 3-1 1-1-1h0c-2 0-3-2-5-3v1l3 3h0l-2-1-1 1c-3 0-5 2-7 5z" class="B"></path><path d="M363 540c-1-3-1-5-1-8-1-4-3-10-2-14 0 1 0 0 1 1 1 4 1 13 4 16h2c1 1 0 2 0 3h-1c-2 1-2 1-2 3l-1-1z" class="AB"></path><path d="M374 564v-1-2h2l6 7c0 2 1 4 2 6h-1l-9-10z" class="Y"></path><path d="M603 691c3 2 6 5 7 9l1 2c-2 0-4-1-5-2l-3-3h-1l1-1h-1c1-1 1-1 1-2h-1c0-2 0-2 1-3z" class="u"></path><path d="M575 604c1-1 3-2 4-2-1 2-1 3-3 4h1l1 1h0 0c-2 1-2 1-3 2h0c0 1-7 2-8 2l4-4v-2c1-1 2-1 4-1z" class="F"></path><path d="M575 604c1-1 3-2 4-2-1 2-1 3-3 4h1c-1 1-1 1-2 1h-2c0-1 1-2 1-3h1z" class="L"></path><path d="M382 568l12 9c-1 1-1 1-1 3-2 0-4-2-6-4-1 0-2-1-3-2-1-2-2-4-2-6z" class="V"></path><path d="M661 220h8l-1 2h-1c-1 0-2 1-4 1h-9-5v-1l-2-1c4 0 11 1 14-1z" class="T"></path><path d="M564 696c0-1 0-2 1-3 0-1 1-2 1-3 0-2 2-4 2-6h0l2-2 1-3 2 2c-1 1-2 2-2 3 0 2-1 4-2 7-1 2-2 4-3 7l-2-2z" class="B"></path><path d="M570 662c1 1 1 1 3 2l1-1 1 1v3c-1 1-1 0-1 1v-1l-1-1c-2 1-5 3-7 2l-4-2c1 0 1-1 1-2l1 1c1 0 5-2 6-3z" class="E"></path><path d="M648 553h1c2-1 3-3 3-5h1c0-1 1-1 2-2s2-1 3-2c-1 2-9 15-10 16-1-1 0-1-1-1h-3l4-6z" class="Y"></path><path d="M596 517c-2-1-3-2-5-3 1-1 1-1 1-2l1-1c1 0 2 1 3 1h1c1 0 2 1 4 2 1 1 2 2 4 3 1 0 2 0 2 1h1 1v1h1 2l1 1h0l2 1c-6-1-11-5-17-5-1 0-1 0-2 1z" class="J"></path><path d="M411 543l2-1v1c0 1 1 2 1 3v1c1 4 3 7 6 9l2 2-1 1c-2-2-4-3-5-5-1 1 0 1 0 2-3-1-4-4-5-7h0v-6z" class="Q"></path><path d="M388 253h0c-1-1-3-3-4-3s-2 0-2 1l-1-1 2-2c1 0 2 0 3 1l2 2h1c1 0 3 2 4 3s2 2 4 2l6-3h1c2 0 4 0 5-1v1s-1 1-2 1 0 0-1 1c-2-1-2-1-4-1v1c-1 0-1 0-2 1h1l1 1h-6 0-1c-3 0-4-1-5-3h-2v-1z" class="P"></path><path d="M569 241l2-1c4 3 8 5 11 9h-1c-5-1-10-4-14-7h0l2-1z" class="B"></path><path d="M547 727c-1 3-4 7-4 10-2 9 2 14 7 20v2l1 1h0-1-2c-1 0-1-1-2-1h-1-1l1-1h3c0-3-3-6-4-8l-4-10c2-4 4-10 7-13h0z" class="l"></path><path d="M629 483c-2 1-4 0-5 0-2-1-5-3-7-5v-5c1-2 2-3 4-4 1-1 1-1 2 0l-1 1c-1 0-3 1-3 3-1 1-1 1-1 3 1 1 4 3 5 3 1 1 2 1 3 1l1 1h3l-1 1v1z" class="T"></path><path d="M352 270c0-2 0-3 1-4 1-2 2-4 5-4-2 6-4 14-7 20 0-4 1-8 1-12z" class="Y"></path><path d="M614 673c2-1 0-2 1-3 0-1 1-2 2-2h3 1l1 1c-1 2-2 3-1 5-1 1-2 1-4 2v1h-1c-2-1-2-2-2-4z" class="P"></path><path d="M621 668l1 1c-1 2-2 3-1 5-1 1-2 1-4 2v-2c1 0 1 0 2-1l-1-1c1 0 1-1 1-1 0-1 1-2 1-3h1z" class="B"></path><path d="M717 291v-25h0l1 11v2h1c2 3 1 10 1 13l-2-1c0-3 0-5-1-8v8z" class="C"></path><path d="M688 227c3-2 3-4 7-5l1 1c0 1 0 1-1 2-1 0-1 1-1 2-5 3-7 6-9 10 0-4 1-7 3-10z" class="Q"></path><path d="M392 657c0-2 1-2 0-4-1 0-1 1-2 1v-1c1-1 2-2 4-2v1l2 1 1-1h0c2 1 4 0 7 2h1l1 2c1 0 1 1 2 2v1h0v1 1h-1v3-2-1c-1-1 0-2 0-4-3 0-4-3-6-3v1c-2 0-3 1-4 1l-2-1h-1v1 2l-2-1z" class="B"></path><path d="M717 291v-8c1 3 1 5 1 8v7 25c-1-2-1-3-1-4h0c-1-3-1-7-1-9l1-19z" class="V"></path><path d="M679 358c1-1 1-2 3-2 1 3 2 7 2 10-2 1-3 1-4 2-2-4-2-6-1-10z" class="D"></path><path d="M408 539l1-1c2 2 0 3 2 5v6-1c-1-1-1-2-1-4v-1c-2 1-2 2-2 4-1 4 1 9 2 13v1-1c0 2-1 2-2 2s-1-1-2-2l1-1v-3h0v-2l-1-1s1-2 1-3l-1-1c1-1 0-3 0-4h0l1-1c0-2 0-3 1-5zm208-108l7 1c1 0 1 0 2 1 0 2 0 2 1 4-2 1-2 1-5 1-4-1-4-1-7-4h1c1 0 1 0 2-1 0 0-1-1-2-1l1-1z" class="P"></path><path d="M415 228l41 1h17c3 0 6-1 9 0h0-62c-2 0-4 0-5-1zm189 340h-1c-1-1-1-1-2-1l-3-1h1l1-1h-1-2-1 0-1c-1 0-1-1-1-1v-2-3l1-2 1-1v-1c1-1 1-2 3-3l-1-1h1l1-1c0-1 1-2 1-3s-1-2-2-3h0v-1l1 1c0 1 1 1 2 2v2c-1 0-1 0-1 1v1c-1 1-1 0-1 1 0 2-2 3-3 5v1c-1 0-1 1-1 2h0l-1 1c0 1 0 2 1 3 4 3 6-1 10 6h0l-2-1z" class="E"></path><path d="M678 336v-2c0-1 0-1 1-2v-1c1-2 1-8 0-10h0l-3-6c-1-1-1-2-1-3v-1l2 3 1 1c1 2 2 3 4 5 0-1 0 0-1-1h0l1-1v1c1 0 1 2 1 3-1 3-2 7-2 11h-1l-2 3z" class="J"></path><path d="M678 336l2-3h1l-1 8-3 6h0c-1 1-2 2-3 4-1-1 0-2-1-3h-3c-1 1-3 0-4 1-1 0-2-1-3-1-1-1-2-1-3-1l-1 1c-1 0-1 0-2-1 1 0 2-1 3-1 1 1 1 0 2 0s2 1 3 1c1 1 2 1 3 1 1-1 1 0 2 0h1c1-1 1-1 2-1h3l-1-1h0c1-2 2-3 2-5h0c1-2 1-3 1-4v-1z" class="E"></path><path d="M560 706l2 2c-1 3-3 6-5 9-3 3-7 6-10 10h0l1-3c4-6 9-11 12-18z" class="L"></path><path d="M695 543l3-3v7h2v2c-3 4-7 7-12 9 1-3 4-3 5-6 2-2 3-5 2-9z" class="C"></path><path d="M579 696v1c-1 1-1 1-1 2h-1c-1-3-1-5 0-8 4-4 7-5 13-7-1 2-3 3-4 4-3 2-6 5-7 8z" class="f"></path><defs><linearGradient id="AR" x1="638.831" y1="526.069" x2="648.161" y2="524.868" xlink:href="#B"><stop offset="0" stop-color="#3c332f"></stop><stop offset="1" stop-color="#574f51"></stop></linearGradient></defs><path fill="url(#AR)" d="M636 521c2 0 6 3 8 3 2 1 6 3 8 5h-2c-3 1-12-4-16-6l2 1h1 0l-1-2v-1h0z"></path><path d="M294 444c1 3 1 5 2 7v13c0 3 0 6-1 9v3 7c-1-3 0-7-1-10-1-10 0-19 0-29z" class="C"></path><path d="M638 269v1c1 1 1 2 0 3-1 0-1-1-2-2v-1c0 1-1 1-1 2-2 0-2 0-4-1h1 1l1-1c0-1 0-1 1-1h1v-2l-2 2h-1 0-2l-1-1c-1 0-2 1-3 1l-1-2-2 1c-1-1-1-1-1-2-1 0-1-1-1-2-1 1-1 0-2 0s-1-1-2-2h1c2 1 1 1 3 1l1 1 1-1c0 1 2 1 2 2l2 2 1-1c1 0 2 1 2 1 1 1 2 0 3 1l1-1h1c1 1 1 2 1 3l1-1z" class="H"></path><defs><linearGradient id="AS" x1="442.736" y1="607.502" x2="435.211" y2="605.722" xlink:href="#B"><stop offset="0" stop-color="#2b2a2c"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#AS)" d="M441 602l2 1 1 4c1 1 1 1 1 3h-1l-13-4 4-3c1 1 1 1 2 1l3 2v-1l1-1v-2z"></path><path d="M441 602l2 1 1 4v1h-1c0-1-1-2-2-2l-1-1 1-1v-2z" class="D"></path><path d="M597 524c1 1 2 1 2 2 1-1 2-1 3 0l1 1c1 0 1 1 2 1l3 2v2 2h0c-2-2-2-3-5-3h0-1l-3-2c-2-2-3-1-5-1-1 0-2-1-3-1v-1h1 2c1 1 1 1 2 1l1-3z" class="P"></path><path d="M599 526c1-1 2-1 3 0l1 1c1 0 1 1 2 1l3 2v2l-9-6z" class="G"></path><path d="M357 612l1 1v1l1 1v1 4 1c-1 1-1 3-1 5 0 1 1 3 1 4h0 1c0-1 0 0 1-1v1h1l1-1 2 2c0 1 0 2 1 3 0 1 0 0-1 1l-2-2-1-1h-3c0 2-1 2-2 3-1 0-1 0-2-1 0-1 1 0 2-1s1-1 1-2c0 0 0-1-1-1v-1-4l1-1c-1-1-1-2-1-3 1-1 0-5 0-6v-3z" class="N"></path><path d="M370 624c-1 1-1 1-2 1h-1v-2h-1c0-2 0-3 1-4v-4c0 1-1 1-1 2v2l-1-2-1-3c1 0 1-1 2-1h1-1l1-1 1 1h1c1 1 1 2 1 3 1 1 1 2 1 3 0 2 2 3 0 4h-1v1z" class="D"></path><path d="M352 612h1c1 1 1 2 1 3 1 3 1 4 0 6 0 3-1 4-3 6-2-1-1-1-3-2 1-2 2-3 2-4v-1l1-1c0-3 1-5 1-7zm398-251h2c1 4 0 8 0 13v5c0 1-1 1-1 1l1 1v1h-1l-1-1c-1-3-1-6-1-9 0-4 0-8 1-11z" class="P"></path><path d="M362 541l1-1 1 1c2 4 4 8 7 12 2 2 4 5 5 7h-1l-2-2-2 1-3-3 1-1c-2-4-3-7-6-10-1-2-1-2-1-4z" class="K"></path><path d="M608 542c1 1 1 2 1 3 0 2 0 4-1 6-1 5-6 6-11 9h0l2-3c4-5 7-10 9-15z" class="o"></path><path d="M572 654c2 0 3 0 5 1h1 0l1-1c1 0 1 1 2 1l2-2c1-1 0-2 2-3l1 1c1 0 1 1 2 1h1c-1 0-1 1-2 1h-2v1h0c-2 1-2 2-3 4 0 1 0 1 1 2h-2v1h-1l-1 2c-1 0-1 0-1-1h0l-1-1c0-3 0-4-2-6h-2l-1-1z" class="D"></path><path d="M702 263c2-1 5-2 7-3 1-2 3-2 4-3l2 1c-9 5-15 11-20 19 0 1 0 1-1 2 1-2 1-7 2-9l1 1c1-3 3-5 5-8z" class="AA"></path><path d="M381 663c3 0 3 0 4 2h0v2h3c2 3 4 3 7 5v1l-1 1-2-2h-3c0 1 0 1 1 2v1h-1c-1-1-2-1-2-3-1 0 0 0 0-1h1 0l-3-3v10c0 1 2 2 3 3v1h0l-1-1c-1 0-3-2-3-3-1-3 0-7 0-10l-1-1c0-2 0-3-2-4z" class="C"></path><path d="M716 422l1-3c2 5-1 11 1 16v20c0 3 1 6 0 9v-5c0-2 0-3-2-5v-8-24z" class="e"></path><path d="M566 621h5c6 1 11 5 14 10 1 1 1 1 0 3-1 0-3-3-4-4-5-4-10-6-15-9z" class="X"></path><path d="M337 226l-1-1h0v-1c-1 0-1 0-2-1h3c1 1 1 1 2 1l2-1c2 2 3 3 5 4 1 3 2 7 2 10-3-5-6-8-11-11z" class="f"></path><path d="M672 553v-1c-1-4-1-8 2-12 1-1 3-3 5-3h3l-3 3c-4 3-4 6-4 10 0 1-1 1-1 1l-2 2z" class="b"></path><path d="M467 805c3 3 5 7 7 10l11 15c1 3 4 5 5 9h-1c-2-2-3-5-5-7-6-7-12-15-16-23 0-1 0-3-1-4z" class="B"></path><path d="M381 626s-1-1-1-2c-1-2-1-8 0-10h1c0-2 1-2 2-3 1 0 2 0 3 1l1 1v1c-1 0-1 1-1 2v1c-2 1-3 2-3 4v1c-1 1 0 2 0 3v2l-1-2-1 1z" class="D"></path><path d="M416 647h2 0 1c1 0 2 0 2 2l1 1c1 0 1 0 2 1v1l-2 2h-1-2c0 1-1 2-2 3 0 1-1 2-2 3v-1s1-2 1-3-3-4-3-5v-1c1 0 3 0 4-1h1l-2-2z" class="l"></path><path d="M454 702l3 2v1c4 9 12 17 16 26 2 3 3 6 3 9v-1c-3-11-11-17-17-26-2-3-3-7-5-11z" class="C"></path><path d="M631 372l1 1-4 3c0 1 0 2 1 3-4 2-8 5-12 7-1 0-1 0-1-1 2-5 8-9 12-12l1 1 2-2z" class="j"></path><path d="M454 702c-3-5-4-12-6-17-1-1 0 0-1-2 0-1 0-1-1-1v-1c-1-2-4-4-4-6l1-1 2 2v1h1l3 6c1 1 2 3 2 5v1c1 1 1 1 1 2s1 2 1 4c1 1 1 2 1 3 2 2 2 4 3 6l-3-2z" class="B"></path><path d="M591 530v-1c-1 0-2-1-3-1h-1v-1c0-1 0-2 1-3 1 0 2 0 3 1v1 1c1 0 2 1 3 1 2 0 3-1 5 1l3 2h1 0c1 1 4 3 4 5v3c-4-4-8-6-11-9h-1v-1h-3l-1 1z" class="l"></path><path d="M636 628h0c1-4 1-7-1-10-1-2-2-3-4-4l-1-1h3c0-2 0-2 1-3h1c1 0 3 2 4 3 0 4 1 8 0 12l-1 1c0 1 0 2-1 3l-1-1z" class="m"></path><path d="M660 533s1 1 2 0c1 0 1 0 2-1v1h0c-1 2-1 2-1 4-1 1-4 6-5 7s-2 1-3 2-2 1-2 2h-1c0 2-1 4-3 5h-1l3-6c1-2 2-3 4-4 0-1 1-2 2-2 0-1 0-3 1-3 0-2 1-4 2-5z" class="r"></path><path d="M378 367h5c3 3 6 5 9 8h-3l-1 1s1 1 1 2l-3-2c-3-3-7-4-10-7 1 0 1-1 2-2z" class="R"></path><path d="M301 289c2 7 0 15 1 22l1 20c-1 5 0 10-1 15h-1v-21-36z" class="Q"></path><path d="M590 720c3-1 6-2 9-4 2-1 4-3 7-4 1 0 3-2 4-4-2 5-4 8-9 11s-11 4-16 5v-1c1-1 3-1 5-3z" class="k"></path><path d="M668 570v1l2-3 1 1h-1c0 2-1 3-1 4v2l-1 1c0 4 1 7 3 11h0l-1-1c-1-1-1-1-1-2l-1-3-1 1c-1 1-1 2-1 4 0 1 1 2 1 3 0-1-1-1-1-2l-1 1c1 1 1 1 1 2v1h0c1 1 1 2 2 2l-1 1c-2 0-2-1-3-2h0c0-1 1-2 0-3h0v-1c0-1 1-1 1-3-1-1-2-2-1-4h0c-1-2 0-2 0-3v1l1 1 1-3v-1l1-1c-1 0-1 0-2-1l2-1-1-1c0-1 0-2 1-2h1z" class="B"></path><path d="M487 843l19 28-1 1c-1 1-1 1-1 2h0l-1-2c-1-2-3-4-5-5-1-3-3-4-3-7v1h2 0 0c-3-6-8-11-11-17l1-1z" class="r"></path><path d="M611 585l1 1c-4 4-9 8-15 10-1 1-6 3-8 2l1-2c7-4 15-6 21-11z" class="h"></path><path d="M343 553c1-4 1-7 0-11-1-2-2-3-3-4-1 0-1-1-1-1 1-1 3 0 4 1 2 1 4 3 4 5 1 4 0 10-1 14-1-1-2-2-3-4z" class="T"></path><path d="M566 737h3c-1 4-2 6-1 10 1 2 1 4 3 6 0 0 2 0 3 1h1l-1 1c-1 0-1 0-2 1h0-1v-1l-1 1c1 2 2 5 3 7h0 0c-4-4-7-11-8-17 0-2-1-6 1-9z" class="G"></path><path d="M655 558h0c1 10 2 21 5 30 0 3 1 4 2 6h-1c-4-2-5-10-6-14-1-7-1-15 0-22z" class="D"></path><path d="M610 660h1c0-2 0-3-1-4 1-1 2-1 4-1 0-1 0 0 1-1 1 0 4-1 5 0h1l2 1v1c2 1 3 1 4 2 0 1 1 1 0 2-1 0-2 1-3 1-1-2-2-4-3-5l-7 5c-2 0-3 0-4-1z" class="J"></path><path d="M318 546h0 1c1 3 4 7 6 9 5 3 10 5 15 8 1 1 2 1 2 2-5-2-11-3-16-6s-7-7-8-13z" class="Q"></path><path d="M589 719c-1-4-3-5-5-8-3-3-3-6-2-10l3-3h0c0 3 0 6 1 8v1c1 3 4 8 7 10h0l-4 2zm-150 17l1-1c2 0 4 0 6 2 1 1 1 3 1 5-1 2-2 3-4 5v-3l-1-1h0c-2-1-3-1-5 0l1-5 1-2z" class="P"></path><path d="M439 736h3 1c1 2 1 3 2 5 0 0 0 1-1 2 0-2 0-3-1-4s-3-1-5-1l1-2z" class="D"></path><path d="M438 738c2 0 4 0 5 1s1 2 1 4l-1 1-1-1h0c-2-1-3-1-5 0l1-5z" class="C"></path><path d="M602 596l11-3 1 1v-1c4-1 14 2 18 2 2 1 4 1 6 1v1c1 0 4 0 5 1-2 0-6 1-8 0s-4-1-6-2c-5-1-10-2-16-2l-9 5-2 1c-2-1-2-1-4 0v-1l4-2v-1z" class="S"></path><path d="M337 521c7 0 14 2 19 7 1 1 2 3 3 4-1 0-2 1-2 2l-1-2c-3-2-5-3-7-5h-1l-2-1-7-2 1-2c-1 0-2-1-3-1z" class="e"></path><path d="M340 522c4 1 5 2 8 5l-2-1-7-2 1-2z" class="S"></path><path d="M505 828h6 1 0c3 0 4 1 7 1 2 1 4 1 6 3-1 2-1 4-2 6-2 0-3 0-5 1l-2-2c0-2 1-2 2-3l-1-2v-1c-1-1-2 0-3-1-4-1-6-1-9-2z" class="E"></path><path d="M397 644h5l1-1c1-1 1 0 3-1h0c1 1 1 1 1 2l1-1h1l1 1-1 1h1 2l2 1 2 1 2 2h-1c-1 1-3 1-4 1-2-1-4-2-7-3l-8-1c-1 0-1-1-1-2z" class="u"></path><path d="M680 526h0l-1-1c2-1 3-1 4-2 1 0 3-1 4 0h2 1l1 1c4 2 10 9 11 13v2h-1c-1-2-3-4-4-7-1-1-3-3-5-4-3-2-9-2-12-2z" class="U"></path><path d="M712 250h0l3-3 1 1v1c0 1-2 3-1 4l1 1v2h1 2c-2 0-3 1-4 2l-2-1c-1 1-3 1-4 3-2 1-5 2-7 3l10-13z" class="AH"></path><path d="M735 352c-1-6-1-12-2-19 0-2-2-6-1-9 1 0 2 1 3 3 4 5 3 28 2 36v-21c-1 1-1 4-1 5 0 2-1 3-1 4v1z" class="D"></path><path d="M603 697l3 3c1 1 3 2 5 2 0 2 0 4-1 6h0c-1 2-3 4-4 4-3 1-5 3-7 4-3 2-6 3-9 4h-1c4-2 7-3 10-5 2-1 3-2 4-4l1-3c2-4 0-7-1-11z" class="s"></path><path d="M436 656c2-3 2-1 4-2l2-2c1 1 1 2 2 4 0 1 0 2-1 2 0 2-2 2-1 5-2 1-2 1-2 3-1 1-1 1-3 0 0-1 0-2-1-3s-3-1-4-4c0-1 0-2-1-2 2 0 3 1 4 3l2 2 1-2c0-2-1-2-2-4z" class="B"></path><path d="M603 678c0 1 1 1 1 2v2 1c1 1 1 2 2 3 1 2 8 3 10 4 1 2 0 2 0 4-1 1-1 1-1 2s-1 1-1 2h-1v-1c0-3-4-5-6-7l-2-2c-2-1-3-1-3-3 0-1-1-2-1-3s1-2 2-4z" class="t"></path><path d="M724 372v-38-11c0-1 0-3 1-4 1 3 1 8 1 11-1 5 0 9 0 14 1 6 0 12 0 19v14l-1-5h0-1z" class="G"></path><path d="M606 663c1 0 2-1 3-2l2 4c0 1 0 2 1 2v1c0 1 1 1 1 2l-1 2c-1 1-3 4-4 5-2 0-2 0-3 2-1 1-1 2-1 3v1-1-2c0-1-1-1-1-2v-2l2-4c1-1 2-3 2-4 1-2 0-3-1-5z" class="v"></path><path d="M608 677c0-2-1-3 1-5 1-1 0-3 2-5l1 1c0 1 1 1 1 2l-1 2c-1 1-3 4-4 5z" class="s"></path><path d="M366 644c1 0 2 0 3 1l1 1c1 2 0 4 0 6 1 1 1 3 2 5 2 2 4 6 6 9 1 1 2 2 2 3h0v1l-2-2-2-3v-1c-1 1-1 2 0 3h0l-1 1-1-1c0-1 0-2-1-2v-1c-1-1-2-3-3-4l-1-4-1-3c-1-1-1-3-1-4v-1l-1-1v-3z" class="B"></path><path d="M361 359h-3-3c-1 0-2 1-3 1-1-1-2-1-3-1-1-1-1-2-1-3l2-2c1 0 2 1 3 1s2-1 4-1c1 1 2 1 3 1h1c2 0 2 0 3 1l-1 1c1-1 1 0 2-1h1c1 0 2 0 4 1l2-1 1 1c0 1 0 2-1 3l-4-2-2-2c-1 1-2 1-2 2-2 0-2 0-3 1z" class="N"></path><path d="M319 546c1 0 1 0 2 1s3 2 4 2c1 4 5 7 9 9l1 1 2 1-1 1 5 2c1 1 1 1 2 1l1-1c3 1 6 4 7 7l1 1s0 1 1 2h-1 0c-3-3-6-5-9-7l-1-1c0-1-1-1-2-2-5-3-10-5-15-8-2-2-5-6-6-9z" class="U"></path><path d="M381 597c1 0 1 0 3 1h0 1c1 1 2 1 3 1l4 3c0 1 0 2 1 2 2 1 2 1 4 1h1 2 2 1 0c-2 2-4 2-6 2h-1-3v3h0 0c-1 0-1-1-1-2-1 0-1-1-2-1h0-1c-2-3-4-4-6-6-1-2-3-2-4-2-1-1-1-1-2-1 1 0 3-1 4-1z" class="E"></path><path d="M301 289v-1c-1-5 0-11 2-15l1 23v10c-1 2 0 3-2 5-1-7 1-15-1-22z" class="J"></path><path d="M498 867c2 1 4 3 5 5l1 2h0c0-1 0-1 1-2l1-1c1 2 2 4 3 5l5-7c0 1 1 2 1 3l-1 1c-1 1-1 2-1 4l-1 1c-1 1 0 1 0 2-1 1-3 2-4 3-2-2-5-7-7-9-1-2-2-5-3-7z" class="m"></path><path d="M320 458h6c-3 2-7 3-8 7h-1v2c-1 1-1 4-1 6 0 1 0 2 1 3v1h0c1 2 2 2 3 4 1 0 1 1 2 1h1c2 1 3 1 4 1 3-1 5-2 9-1-2 1-6 1-7 3 1 0 1 1 2 2 1 0 1 0 2 1l-1 1s-1-1-2-1-2-1-3-2c-2-1-3-2-5-3l-1-1c-2-1-4-2-4-4h-1c-1-3-2-8-1-11l1-1c0-2-1-3 1-5 1-1 1-2 3-3z" class="D"></path><path d="M317 461c1-1 1-2 3-3 0 2-1 2-2 3h-1z" class="H"></path><path d="M653 642v-1-1c1 1 1 2 2 3l-1 1c0 1-1 1-1 3-1 4-3 8-5 12-2 5-6 8-9 12-2 3-3 6-5 9-1 0-1 1-3 2h0-1c1-1 3-2 3-3 1-1 1-5 1-6v-10c1 0 1 0 2-1v12h0l9-11c2-3 3-6 4-8l2-6 1-2c0-2 0-3 1-5z" class="K"></path><path d="M692 455h1c0 1-1 2-2 3h1 0 2c1 0 2-1 3-1 2 1 4 1 6 3 1 2 2 4 1 6 0 2 0 3 1 4 0 3-1 6-2 8h-1l1-2c0-1 1-3 1-4-1-2-1-4-2-5v-1c-1-2-4-4-6-5v-1h-4-1c-2 1-3 3-5 4 0 0 0-1-1-1 1-3 4-4 5-6l2-2z" class="D"></path><path d="M408 623l2 2 1-1 3-3h1c-3 6-7 10-12 13-4 2-9 2-13 0-3-1-7-3-8-6-1-1-1-1-1-2l1-1 1 2c1 3 4 5 7 6l3 1c2 0 4 0 6-1 4-1 7-6 9-10z" class="O"></path><path d="M620 579c3-1 4-2 6-3 1 2 1 2 1 5l1-1 1 1c1 0 2-1 3-2v1h0c-5 5-11 9-17 12h-1c1-1 2-2 3-2l2-2 1-1h-1c1-1 1-1 1-2h-2l1-1h1v-1s-1 0-2-1l1-3h1z" class="H"></path><path d="M620 583l1 1h3v1c-1 1-3 2-4 2h-1c1-1 1-1 1-2h-2l1-1h1v-1z" class="E"></path><path d="M620 579c3-1 4-2 6-3 1 2 1 2 1 5h-2c-1 0-1-1-2-1s-2-1-3-1z" class="d"></path><path d="M666 257c2 2 2 3 2 7v1c-1 4 0 5 0 8 0 2-1 5 0 6v1h0c-2-1-4-6-4-9 0 0-1-1-1-2 0-2-1-4-2-6v-1-1h-1l1-1h4l1-3z" class="G"></path><path d="M300 417v-21c0-5-1-10 0-14l1 8v83c-2-3-2-5-2-7l-1-16 1-3c1-3 1-7 1-10v-20z" class="P"></path><path d="M365 220h1c3-1 7-1 10 0 0 0 0 1 1 1h8c8 0 16 1 24 1 2 1 5 0 7 1h-26-12c-4 0-7 0-10-1h-1l-1-1-1-1z" class="a"></path><path d="M668 478c0 2 1 4 0 5l1 1v-1c1-3 3-7 5-9l1-2 1 1v1h0c-1 6-4 10-7 15l-3 6c-1-2-1-3-1-5h0c1-2 1-1 0-2l-1-1h0l2-2c-1-1-1 0-2-1v-1c1-2 3-3 4-5z" class="D"></path><path d="M527 850l2-1c0 3-3 5-3 8 0 2-4 6-5 7-3 4-4 10-8 13 0-2 0-3 1-4l1-1c0-1-1-2-1-3 2-4 5-8 8-12 1-2 3-5 5-7z" class="F"></path><path d="M361 359c1-1 1-1 3-1 0-1 1-1 2-2l2 2 4 2 6 3v4c-1 1-1 2-2 2l-9-6c-1 0-5-3-6-4z" class="k"></path><path d="M361 359c1-1 1-1 3-1 0-1 1-1 2-2l2 2-1 1 1 1v1l2 2h-3c-1 0-5-3-6-4z" class="D"></path><path d="M532 807l1 1c0-1 0-1-1-2 0-1 0-1-1-2v-1h0v-2c1-1 1 0 3 0l2 2c1 2 0 4-1 6-1 3-1 4-4 6-2 0-3 0-5-1l-4-4 2-1h2 1 3c0-1 0-1 1-2h1z" class="P"></path><path d="M586 688c3 2 6 0 9 1 2 0 5 1 8 2-1 1-1 1-1 3h1c0 1 0 1-1 2h1l-1 1c-2-2-4-3-7-4l-2-1c-3 0-7 1-10 2h0c-1 1-2 1-3 2h-1c1-3 4-6 7-8z" class="s"></path><path d="M606 618l1 3c1 1 1 2 2 3l2-1c2 5 6 9 12 11 3 0 5 0 8-2 2-1 4-2 5-4l1 1c-2 3-5 5-8 6-4 1-9 1-13-1-6-3-9-9-12-15l2-1z" class="S"></path><path d="M293 438h1v6c0 10-1 19 0 29 1 3 0 7 1 10v8l-1 1v-1c0-1-1-2-1-3v-1c-1 3 0 6-1 8v-16c0-2 1-4 0-6l1-1v-34z" class="a"></path><path d="M643 654v4c0 1 0 1-1 2l-2-1c-1 0-1 0-1 1-1 0-1 1-2 2h1l1-1 2-1 1 2s-1 1-2 1c-1 1-1 1-2 0l-1 1v2 1 1 3l1-1 1-1c1-2 0-1 1-2l2-2h0l1-1c1-2 1-2 2-3h0c1-1 1-2 2-3l-1-1c0 1-1 3-2 3h0c0-1 0-2 1-3s1-1 3-1c0-1 0-2 1-2 0-2 0-3-1-5 2-2 3-4 4-6l1-1c-1 2-1 3-1 5l-1 2-2 6c-1 2-2 5-4 8l-9 11h0v-12c0-1 1-2 2-3v-1c0-1 1-2 1-3 1-1 1-2 2-2 1-1 0-1 1 0h0l1 1h0z" class="B"></path><path d="M639 655c1-1 1-2 2-2 1-1 0-1 1 0h0l1 1-5 5v-1c0-1 1-2 1-3z" class="U"></path><path d="M353 214c1 0 2 1 3 1l1-1c2 1 2 1 4 1h1c1 2 2 4 3 5l1 1c3 7 6 13 8 20-5-9-11-19-20-25l-1-1v-1z" class="h"></path><path d="M437 743c2-1 3-1 5 0h0l1 1v3c-1 1-1 2-1 4l2 3h1l1 1h1l1 1-2 2h-4l-1-1h-2l-1-1c-2-3-2-7-1-10v-3z" class="P"></path><path d="M438 751h4l2 3c-1 0 0 0-1 1h0c-1-1-2-1-3-2-1 0-1-1-2-2z" class="H"></path><path d="M437 743c2-1 3-1 5 0h0l1 1v3c-1 1-1 2-1 4h-4l-1-1v-4-3z" class="I"></path><path d="M603 711c-1 2-2 3-4 4-3 2-6 3-10 5h1c-2 2-4 2-5 3v1c-6 3-14 6-16 13h-3c5-11 13-14 23-18l4-2c4-1 7-3 10-6z" class="d"></path><path d="M416 625v-2-1-1c1-1 1-3 1-4v-2l1-1c0-2 0-3 1-4 8 2 15 5 22 10l2 3c-5 2-8 5-12 9l-2-1c1-3 6-6 8-8l2-1 1-1c-1-1-2-2-3-2-5-3-12-6-18-6-1 3-1 9-3 12z" class="J"></path><path d="M452 735c1 1 1 1 1 3h1c0 3 0 7-1 9 0 1 0 2-1 3v1c0 1-1 2-1 3 0 2-1 3-2 4s-1 3-2 4c0 1-1 3-1 5l-7-10h2l1 1h4l2-2-1-1 2-2c0-1 0-2 1-3h0l1-2v-1c0-2 1-7 0-9l-1-2h0l2-1z" class="B"></path><path d="M483 843l3 1c3 6 8 11 11 17h0 0-2v-1c0 3 2 4 3 7 1 2 2 5 3 7-1-2-2-3-3-4-1 0-3-4-4-5s-1-2-2-3v-1c0-1-1-1-1-2-1-1-1-2-1-3-2-3-3-4-5-6 1-1 1 0 3 0 0-2-3-5-5-7z" class="U"></path><path d="M608 530l1 1c0 1 0 2 1 2 1 2 1 4 2 6h0l-1 1 1 1h3c1 1 0 0 0 1v4l1 1v1 1l1 2c-1 2-2 5-3 7-1-1-1-1 0-2l-1-1c0-3 0-5-1-8v-1l-1-1h-2c0-1 0-2-1-3l-1-3v-3c0-2-3-4-4-5 3 0 3 1 5 3h0v-2-2z" class="H"></path><path d="M571 654h1l1 1h2c2 2 2 3 2 6l1 1h0c0 1 0 1 1 1l1 1h0l-1 1 1 1c-1 1-2 1-2 3h0c-1 1-2 3-4 3h-1c0-2 1-2 1-4 0-1 0 0 1-1v-3l-1-1-1 1c-2-1-2-1-3-2s0-3 0-4v-2l-2 1-2-1c1 0 2 0 4-1v-1l1 1v-1z" class="C"></path><path d="M571 654h1l1 1h2c2 2 2 3 2 6l-2-2h-1v2h-1c-2-1-2-1-3-3v-2l-2 1-2-1c1 0 2 0 4-1v-1l1 1v-1z" class="H"></path><path d="M607 572l5-2-1 2c-1 1-3 3-3 4-1 2-1 3-1 5v2c-1 0-2 1-3 1h-3v-1-1c-1-1-2 0-3 0l-2-2c1 0 1-1 1-2l2-2 1-1c2 0 3-1 4-1 1-1 2-1 3-2z" class="B"></path><defs><linearGradient id="AT" x1="641.512" y1="557.47" x2="631.51" y2="579.587" xlink:href="#B"><stop offset="0" stop-color="#85807d"></stop><stop offset="1" stop-color="#bbb9bb"></stop></linearGradient></defs><path fill="url(#AT)" d="M644 559h3c1 0 0 0 1 1-6 7-12 14-20 20l-1 1c0-3 0-3-1-5 7-5 12-10 18-17z"></path><path d="M578 519l1 2h1v-2l1-3h1 0l-9 39h-2v-1l7-35z" class="B"></path><path d="M596 517c1-1 1-1 2-1 6 0 11 4 17 5h1c1 1 1 1 2 1h0l1 1h1 0c1 1 1 1 2 1 1 1 0 1 1 1 2 1 3 1 5 2-1 0-1 1-1 1v1h0l-1 1c-10-3-20-9-30-13z" class="G"></path><path d="M776 329c2 1 3 2 4 4-1 4-7 5-9 9l-1 1c-1 1-2 1-2 2l-5 5c-5 4-10 7-16 9 11-8 24-17 29-30z" class="d"></path><path d="M597 576l-3 1c-1 1-3 1-5 2-1 0-1 0-2 1h-1-2c-2 1-3 1-5 1l-1-1c2-3 5-1 8-3 1 0 2-1 3-1h2 1c1-1 2-1 3-1l-1-2h0c-1-1-1-2-1-3h0l-1-1 2-1h1c0 1 1 1 2 2h5 1v1 1c2 0 2 0 3-1l1 1c-1 1-2 1-3 2-1 0-2 1-4 1l-1 1h-2z" class="H"></path><path d="M603 571v1c2 0 2 0 3-1l1 1c-1 1-2 1-3 2-1 0-2 1-4 1l-1 1h-2-2c1-1 1-1 2-1h2c1-1 1-1 2-1v-1h-2c-1 1-2 1-3 1v-1-1c2-1 5-1 7-1z" class="C"></path><path d="M643 535l4-1v1l-9 10-5 5c-3 3-6 6-8 10l-3 2v-1h1c2-2 3-6 5-8 3-5 6-8 8-13h0c2-1 4-3 6-4l1-1z" class="k"></path><path d="M349 299h1v3h-1l-1 5c-2 4-5 10-8 14-2 4 0 8-2 11 0-3 0-6-1-8v-3c1-2 0-4 2-6 5-3 4-8 6-13 1-1 3-2 4-3z" class="P"></path><path d="M611 643c0-1 1-1 2-1 2 0 2 0 4 1l1 1c1-1 2-1 3-1 2 0 4-1 6-1h1c0 2 0 2-2 4-3 0-6-1-8 1h-1c-1-1-1 0-2-1v1c-1 1-5 0-6 0-2 1-5 3-7 4h-1l3-3c-1 1-1 1-2 1h-2 0c0-1 1-2 2-2h0 1l1-1h1l1-1c1 0 1 0 1-1h2c0-1 1-1 2-1z" class="w"></path><path d="M376 617h0l-1-1c0-1 0-1 1-2v1c0 1 1 1 1 2v3l-3 3c0 1 0 1 1 1-1 1-2 1-3 1h0c-1 1-1 1-2 1 0 2 0 2-1 4h-1c0 2 2 2 2 4l-2 1-1-1c0-1 0-1 1-1-3-5-2-12-5-16-1-2-1-3 0-4s1-2 1-3v-3c2-1 3-1 5-1 0-1 0-1 1-1s1 1 2 1h1v2c-1 0-1-1-1-1-1 0-2 1-2 1-1 1 0 1-1 2-1 0-2 0-3 1s-1 1-1 2h-1c-1 1-1 2 0 3 0 1-1 1 0 2s2 3 2 4c0 2 0 3 1 5h0c0 1 0 1 1 2h0l1-1v-3c1 0 1 1 2 0h1l-1-1h-1v-1h1c2-1 0-2 0-4l1 1h0v-1c1-1 1 0 1-1l1-1h2z" class="B"></path><path d="M372 619c1-1 1 0 1-1l1-1h2v1c0 1 0 1-1 2l-2 1c0-1-1-1-1-2zm271-2v-3h8c1 2 2 2 2 4-2 2-1 4-3 6 0 0 0 1 1 2l-1 1h0c0 1-1 1-1 2l-4-4c0-2-1-3-2-3l-1-2c1-1 0-2 0-3h1z" class="D"></path><path d="M643 617h0c1 0 1 0 2-1v5l-3-1c1-1 0-2 0-3h1z" class="P"></path><path d="M642 620l3 1c0 1 0 1 1 2h2v2l2 2h0c0 1-1 1-1 2l-4-4c0-2-1-3-2-3l-1-2z" class="E"></path><path d="M483 811v-7h0c1-1 1-1 2-1h-2-2l1-1h1c2 0 4-2 6-1-1 1-3 1-3 2l3 1-1 1c2 1 3 2 4 4l3 3h0 1c-1 1-2 2-3 2-2 1-3 2-5 1-3-1-4-2-5-4z" class="J"></path><path d="M483 811v-2h1 3 0c2 1 3 2 4 3s2 1 2 2c-2 1-3 2-5 1-3-1-4-2-5-4z" class="H"></path><path d="M438 238c2 0 5 0 5 1-1 0-1 1-3 2-5 3-8 8-14 8-5 0-5-3-8-5h0l-3-3-2-2c5 0 4 3 7 5 2 1 3 1 4 1 6-1 9-6 14-7z" class="V"></path><path d="M735 352v-1c0-1 1-2 1-4 0-1 0-4 1-5v21l-3 20c0 1 1 0 0 1v4 3h-1l-1 1c0-4 0-7 1-10v-6c0-5 1-11 1-17l1-7z" class="C"></path><path d="M718 298c2 4 1 9 2 14l1 37c0 5 0 10-1 16-1-5 0-11 0-15l-1-2h0c-1-3 0-7-1-10v-15h0v-25z" class="S"></path><path d="M718 323c1 2 1 4 2 6v7 14l-1-2h0c-1-3 0-7-1-10v-15z" class="X"></path><defs><linearGradient id="AU" x1="307.394" y1="361.602" x2="295.106" y2="366.398" xlink:href="#B"><stop offset="0" stop-color="#a1a0a1"></stop><stop offset="1" stop-color="#bfc0c2"></stop></linearGradient></defs><path fill="url(#AU)" d="M301 325v21h1c1-5 0-10 1-15v33c0 1-1 1-1 2v2 5 13h0c-1 1 0 3-1 4l-1-8 1-8v-49z"></path><path d="M301 374c0 4 0 8 1 12-1 1 0 3-1 4l-1-8 1-8z" class="f"></path><path d="M333 521h4c1 0 2 1 3 1l-1 2 7 2c-2 1-4 0-5 1h-3c-2-1-4-1-6 0h-1c-4 1-6 4-9 5-2 1-3 5-5 6v-1c0-4 3-5 5-8 1-1 2-3 3-4 3-2 5-3 8-4z" class="C"></path><path d="M333 521h4c1 0 2 1 3 1l-1 2h-4v-2h-2v-1z" class="F"></path><path d="M524 754l2-3-3 16c-1 4-2 7-3 11l-1 9v5c0 1-1 2-1 3v3c0-2 0-5-1-7 0-11 2-21 4-31 2-2 2-4 3-6z" class="v"></path><path d="M648 360l6-4c-1 1-3 4-3 6l2 1c-3 2-7 4-10 6-1 1-3 2-4 3h-2c-1 1-2 1-3 1l-1-1s1 0 2-1c2 0 2-1 3-3v-2l3-3h0c2-1 4-2 7-3z" class="Q"></path><path d="M638 366l3-3c1 1 2 1 3 3-2 1-3 2-5 2h-1v-2z" class="J"></path><path d="M648 360h1v4c-2 1-3 2-5 3-1-1-1-1 0-1-1-2-2-2-3-3h0c2-1 4-2 7-3z" class="h"></path><path d="M688 558c5-2 9-5 12-9-1 4-2 7-6 10-6 4-13 4-19 8h-1c-2 1-4 4-5 6 0-1 1-2 1-4h1l-1-1-2 3v-1l1-1c2-3 4-4 6-6l3-2 10-3z" class="Q"></path><path d="M604 619l-1-5c0-3-1-7 1-10 2-1 5-3 7-4-2 8-3 16 0 23l-2 1c-1-1-1-2-2-3l-1-3-2 1z" class="a"></path><path d="M606 618c-1-2-1-5-1-7h1c1 2 2 7 2 10h-1l-1-3z" class="g"></path><path d="M716 454c2 2 2 3 2 5v5l-1 50-2 2 1-62z" class="f"></path><path d="M619 255h0c-1-1-1 0-2-1-1 0-2-1-3-1h-1c1 0 1 0 1 1v1-1h-1l-1 1h0c-1 0-2 1-3 2l-1-1c1-1 2-1 3-2l1-1h0l-1-1h-1c0 1-1 1-1 1-1 0-2 1-2 1h0v1l1 1h-1v-1l-1 2v-1c-1 0-1 0-2 1v1h0v1c1 1 2 1 3 2 1 0 0 0 1 1 1 0 2 0 3 1 1 0 1 0 2 1 1 0 2 1 3 1l1 1h1 0c2 1 5 2 7 3 1 1 1 1 2 1l4 2c1 1 2 1 3 2h2l1 1 1-1v-1c1 0 1 1 2 2h3l1 1c0 1 0 1 1 2h0c-2 0-4-1-6-2h-1c-3 0-5-2-8-3l-10-5-25-12v-1c1-1 3-1 4-1h1c2-1 3 0 5-1 2-2 6-2 9-1 1 0 1 1 2 1 1 1 3 1 4 1 1 1 0 1 1 1h-2zM317 477h1l2 1c1 1 3 1 5 0h1 0c3-1 4-5 7-6 0 0 1 0 2 1s1 1 2 3v2l1 1c1 0 1 0 3-1h1 0c-2 2-4 3-6 4h0c-4-1-6 0-9 1-1 0-2 0-4-1h-1c-1 0-1-1-2-1-1-2-2-2-3-4z" class="B"></path><path d="M495 826c1-1 5-4 7-4s6 2 7 3v1l-4 1-6 3c-1 1-1 1-2 1v1c-1 0 0 0-1 1 2-1 4-3 7-3 0-1 0-1 1-1l1-1c3 1 5 1 9 2 1 1 2 0 3 1v1h-1c-2-2-4 0-6-2-2-1-4 0-6 0-3 1-5 2-7 4-1 1-1 2-1 3h1v1h-1c0-1-1-2 0-3 0-2 0 1 0-1l-1-1c-2-1-2-2-2-3 0-2 1-3 2-4z" class="D"></path><path d="M410 619c0-6 0-11-1-16 2 0 4 1 6 1 1 1 1 2 1 3 1 5 0 10-1 14h-1l-3 3-1 1-2-2c1-1 1-3 2-4z" class="n"></path><defs><linearGradient id="AV" x1="555.48" y1="660.827" x2="569.538" y2="660.724" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#363638"></stop></linearGradient></defs><path fill="url(#AV)" d="M561 656c1 0 1 1 2 1l3-1 2 1 2-1v2c0 1-1 3 0 4-1 1-5 3-6 3l-1-1c0 1 0 2-1 2-2 0-2 0-4-1l-2-1c0-1 0-2-1-3v-1h1 0c0-1-1-2 0-3h1c1-2 2-1 4-1z"></path><path d="M458 795l-44-63-2-2 1-1 46 64c0 1 0 1-1 2z" class="D"></path><defs><linearGradient id="AW" x1="618.908" y1="267.285" x2="592.084" y2="267.715" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#8f8e90"></stop></linearGradient></defs><path fill="url(#AW)" d="M580 256l41 16-1 3-30-12c-3-2-7-3-11-5h2l1 1c2 0 3 1 5 2h2c1 1 1 1 2 1h1c-2-2-4-2-6-3h-1c-2-1-3-1-4-3h-1z"></path><path d="M445 654l2 1h1c2 1 3 2 4 4 0 1 0 2 1 3v2c-1 1-1 1-2 1l-1 1c-2 0-3-1-4-2l-2 1c0 2 0 3 1 5l1 1c-2 1-2 1-3 1-2-3-2-4-2-7l1-2h0c-1-3 1-3 1-5 1 0 1-1 1-2l1-2z" class="P"></path><path d="M734 384c1 1 1 2 1 3 0 2 1 4 1 6h1c2-3 0-7 2-9 1-2 1-6 3-8 0-1 1-1 1-2 1 0 1-1 1-1v-1c0-1 0-2 1-3v-2l1-1v1c0 2 0 4-1 5 0 3-1 6-2 8l-1 3v3c-2 4-3 10-2 14l-1-1-1 1h1l-1 1c-1-1-2-2-2-4l-1-1v-1l-1 1h-1s-1-1-1-2v-2l1-1h1v-3-4z" class="G"></path><path d="M575 260c4 0 7 2 10 4 2 1 4 1 6 2 6 2 12 3 18 7 3 2 9 4 13 6h0c-7 0-12-5-18-7-3-1-6-3-8-2v1h1c3 1 6 2 9 4-3 1-6 0-9 1h-5c1 0 2-1 3-1 1-1 2 0 3-1l-1-1c-1 0-2-1-3-1-2-1-1 0-2-1h-1c-1-1-2-3-4-4 0 0-1-1-2-1l-1-1c-3-1-6-3-8-5h-1z" class="H"></path><path d="M632 219l20 1h9c-3 2-10 1-14 1l2 1v1h-71c7-1 16 0 24-1 2 0 5 1 8 0 4-1 8-1 12-1h19l1-1h-10v-1z" class="a"></path><path d="M383 601c-5-2-10-2-15-2-6 0-10 1-14 5l-1 1c-2-3-3-4-6-6v-1c1 1 4 2 5 2 2-1 5-3 7-3 5-2 14-3 20-1 0 0 1 1 2 1-1 0-3 1-4 1 1 0 1 0 2 1 1 0 3 0 4 2z" class="e"></path><path d="M576 238c2 1 4 1 6 2 4 1 7 4 12 5h5 1l4-4v-1c-2 0-2 0-4 1v-1h0c2-1 6-1 8-1l1 1h1c3 1 6 5 8 8v2h0l-12-10c-1 0-2 2-3 3-3 3-6 5-10 6-6 0-13-7-17-11z" class="X"></path><path d="M688 227l4-9 1-1c1 0 1 0 2-1 2 0 5 0 7 1 1 0 1 0 1 1l8 10h-1c-2-2-5-4-8-4l-8 3c0-1 0-2 1-2 1-1 1-1 1-2l-1-1c-4 1-4 3-7 5z" class="d"></path><path d="M426 632l3-1c-2 2-4 2-4 5h-2l-1 1h-1l-2 1c2 2 4 4 5 6l2 2h0l-1 1c-1-1-2-2-2-3l-4-1v3 1h-1 0-2l-2-1-2-1h-2-1l1-1-1-1h-1 0v-1h1v-2c1-1 1-1 2-1s2 0 3-1l2-2h3c1 0 1-1 2-1 1-1 3-2 5-3z" class="J"></path><defs><linearGradient id="AX" x1="283.377" y1="442.061" x2="300.105" y2="459.955" xlink:href="#B"><stop offset="0" stop-color="#222123"></stop><stop offset="1" stop-color="#444341"></stop></linearGradient></defs><path fill="url(#AX)" d="M293 430v8 34l-2-1c0-2-1-4-1-6v-2c0-1 0 0 1-1v-1c0-1 0-1-1-2s-2-1-3-1v-4h0v-2h-1l1-1c0-1 1-2 1-3v-3c1-1 2-2 2-4 1-4 1-7 3-11z"></path><path d="M290 441c0 1 1 2 1 3s-2 3-3 4v-3c1-1 2-2 2-4z" class="P"></path><path d="M287 451h2l1-1v1c0 1 0 2 1 2l-1 1v2l-1 1v-1c0-1-1-2-1-2h-1 0v-2h-1l1-1z" class="H"></path><path d="M521 864c1 1 1 1 1 2s-1 1-1 2c-1 2-3 4-3 6h0c0 2-1 3-1 4l1 2-6 9-1-1c-3 0-2-2-4-3l-1 1c0-1-1-1-1-2l-3-6c-1-3-3-5-4-8 1 1 2 2 3 4 2 2 5 7 7 9 1-1 3-2 4-3 0-1-1-1 0-2l1-1c4-3 5-9 8-13z" class="p"></path><path d="M511 888c0-1-1-2-1-3 3-3 5-8 8-11 0 2-1 3-1 4l1 2-6 9-1-1z" class="l"></path><path d="M678 521c3-1 8 0 11 1l1 1h-1-2c-1-1-3 0-4 0-1 1-2 1-4 2l1 1h0c-1 0-3 1-4 1-6 2-9 5-13 10 0-2 0-2 1-4h0v-1c-1 1-1 1-2 1-1 1-2 0-2 0l1-2c4-6 10-9 17-10z" class="e"></path><path d="M678 521c3-1 8 0 11 1l1 1h-1-2c-1-1-3 0-4 0-1 1-2 1-4 2l1 1h0c-1 0-3 1-4 1-3-1-4 1-7 1 1-1 5-2 6-3 1 0 1 0 1-1h-3-1c1-1 3-1 4-1s2-1 3-1l-1-1z" class="O"></path><path d="M404 592c2 0 3 1 5 1l1-1 6 3h1l1-1c3 2 6 3 10 2l1 1 1 1c1 1 1 2 3 3h-1c0 1 0 1 1 1l2 1-4 3-27-14z" class="M"></path><path d="M418 594c3 2 6 3 10 2l1 1 1 1c1 1 1 2 3 3h-1c0 1 0 1 1 1-5-1-9-4-13-5-1-1-2-1-3-2l1-1z" class="O"></path><path d="M601 617l1 4 2 6c1 1 1 2 1 3v1c-1 1-2 2-4 2-2 1-5 0-8 0l1 1c0 1 0 2-1 3h-2l-1 1h-2c0-1-2-1-3-2l3-3v-1l1-1-4-4c1 1 2 1 4 1l1 1c2 0 3 0 4 1 1-1 2-1 3-1 0-1 0-1 1-2h0l2 1c2-3 1-8 1-11z" class="G"></path><path d="M602 621l2 6c1 1 1 2 1 3h-3l-1-1c2-3 2-4 1-7v-1z" class="D"></path><path d="M590 629c2 0 3 0 4 1 1-1 2-1 3-1 0-1 0-1 1-2h0l2 1c-1 1-3 3-4 3h-1l-1 1v-1h-2c0-1-1-1-2-2z" class="F"></path><path d="M585 627c1 1 2 1 4 1l1 1c1 1 2 1 2 2l-1 1c0 1 0 1 1 2h-1 0c-1 0-2-1-3-1v-1l1-1-4-4z" class="E"></path><path d="M299 466c0 2 0 4 2 7v26l1 32v10c-1-2-2-3-2-5v-1c-1-2-1-3-1-5v-10h0v-2c1-4 0-8 0-12 0-5 1-13 0-18 0-1-1-1 0-2 0-4 0-7-1-10-1-4 0-7 1-10z" class="J"></path><path d="M594 649v-2l2-1 1 1c0 1-1 1 0 2l-3 3c-1 1-1 2-2 3h0-1v1c-3 2-6 4-8 7v1c0 2-1 4-2 7h0c-2 5-5 6-8 10l-2-2 6-6s1 0 1-1v-3h0c0-2 1-2 2-3l-1-1 1-1h0l-1-1 1-2h1v-1h2c-1-1-1-1-1-2 1-2 1-3 3-4h0v-1h2c1 0 1-1 2-1 2-1 3-2 5-3z" class="AB"></path><path d="M594 649c-2 4-8 8-11 11-1-1-1-1-1-2 1-2 1-3 3-4h0v-1h2c1 0 1-1 2-1 2-1 3-2 5-3z" class="d"></path><path d="M731 352v12 1l1 1c0 1-1 2-1 3h0v3 4 1c0 3-1 6 0 10l-1 1v13c0-2 1-4 1-5 1-2 0-5 0-7l1-1c0-1-1-3 0-4v-7h0v-2h-1l1-1c0-1-1-3 0-4v-3-1h0c1-2 1-2 1-3 0-2 1-3 1-4 0 6-1 12-1 17v6c-1 3-1 6-1 10v2c0 1 1 2 1 2h1l1-1v1l1 1-1 1h-1l-1-1c-2 2 0 8-1 11 0 1-1 3 0 4l1 1c-1 1-1 2-2 4h1c0 2-1 3-1 5-1 1-1 2-1 3v3h-1-1c2-3 0-6 1-9h1c-1-2-1-6-1-8v-22c1-2 1-7 1-8 1-2 0-8 0-10 1-6 0-13 1-19z" class="J"></path><path d="M545 244c-3-2-7-3-10-5 1-1 2 0 3 0 4 1 41 15 42 17h1c1 2 2 2 4 3h1c2 1 4 1 6 3h-1c-1 0-1 0-2-1h-2c-2-1-3-2-5-2l-1-1h-2c-1 0-6-2-8-3-9-4-17-8-26-11z" class="e"></path><path d="M777 323c2 0 2 0 4 2 1 2 2 2 2 4v1c3 2 3 4 3 7v1 3h-1-1v3h-2l-2-1-1 1h0v-2c-1-1-3-1-5-1-1 0-3 1-4 2l1-1c2-4 8-5 9-9-1-2-2-3-4-4l1-4v-2z" class="g"></path><path d="M777 325c3 2 5 4 5 8h0c1 1 1 2 2 3-1 1-1 1-2 1s-1 1-1 1h-2c-3 1-5 2-8 4 2-4 8-5 9-9-1-2-2-3-4-4l1-4z" class="Q"></path><path d="M383 258h1c1-1 1-1 2-1h1v-1c-1-2-1 0-1-2h0 0-2-1 0-1-2l-1 1-1-1 1-1h1v-1l1 1h2 0c1-1 2 0 3 0h0 2v1h2c1 2 2 3 5 3h1v1 2 2l-1 1v1 1c-2-1-2-1-3-1-1 1-1 0-1 1v2s1 1 2 1 3 0 4-1c0-1 1-1 1-2 1 0 2 0 3-1 1 0 2 0 3-1h0 2c-1 1-3 1-5 3-3 2-10 5-14 6-2 0-3 1-5 1v-1c2 0 3 0 5-1 1-1 2-1 3-1s2 0 3-1h-1l-3-3v-1-2l-1 1-1 1c-1 0-1-1-2-2h1c-1-1-2-1-2-2l-2-1 1-2z" class="G"></path><path d="M443 623c1-1 5-2 7-1-8 3-12 8-18 14l1 1c1 1 1 0 2 2l-3 3h-1c-2 1-2 1-3 0s-1-1-1-2h-2v2c-2-1-2-4-3-5l1-1h2c0-3 2-3 4-5h0l2 1c4-4 7-7 12-9z" class="c"></path><path d="M429 631l2 1c-1 0-1 0-2 1-1 2-3 2-4 4 0 1 1 2 3 2l1 1c1 0 1 0 1 1l2 1h-1c-2 1-2 1-3 0s-1-1-1-2h-2v2c-2-1-2-4-3-5l1-1h2c0-3 2-3 4-5h0z" class="P"></path><path d="M419 690c5-2 10 0 15 1-1-1-3-3-3-5l-1-1h1c2 1 7 3 8 5v1h1c1 2 1 3 1 4v1c1 2 1 5 0 7h0-1l-1-6c-1-3-6-4-9-5-6 1-9 2-14 7v-2h-1v-3c1-1 3-2 5-3l-1-1z" class="s"></path><path d="M420 691h2v2c-3 0-4 1-5 3l-1 1h-1v-3c1-1 3-2 5-3z" class="B"></path><path d="M628 527c1 0 1 0 2-1-1 0-1 0-2-1h-2v-1h0l17 7c2 1 4 2 5 3l-1 1v-1l-4 1-1 1c-1-1-2-1-3 0h-1c-1 0-1 0-2 1-2 1-4 2-6 2-1-1-2-1-2-2h-1-1c-1 0-2-1-2-2h1l1 1v-1c-1-1-2-2-2-3h0c1 0 2 1 3 2h1 1v1 1h0 1v-1c-1-1-1-1-2-1v-1c1 0 1 0 3 1v1c1-1 1-1 2-1l2-1c-1 0-1 0-2-1l-7-2 1-1h0v-1s0-1 1-1z" class="C"></path><path d="M643 535v-1l-9-2v-1c1 0 3 0 4 1 2-1 3-1 5-1 2 1 4 2 5 3l-1 1v-1l-4 1z" class="L"></path><defs><linearGradient id="AY" x1="614.79" y1="519.301" x2="622.05" y2="510.277" xlink:href="#B"><stop offset="0" stop-color="#130804"></stop><stop offset="1" stop-color="#282625"></stop></linearGradient></defs><path fill="url(#AY)" d="M599 504a369.08 369.08 0 0 1 29 13c2 1 6 2 8 4h0v1l1 2h0-1l-2-1-32-14c0-1-1-1-2-1 0-1-1-2-1-2l-1-1 1-1z"></path><path d="M504 830c2 0 4-1 6 0 2 2 4 0 6 2h1l1 2c-1 1-2 1-2 3h-5-2c-2 1-4 1-5 2l-4 4-1-1c0-1 0-2 1-4h0l-1-1h-2-1c0-1 0-2 1-3 2-2 4-3 7-4z" class="G"></path><path d="M504 830l1 2c-1 2-4 3-6 4-1 0-1 0-2-2h0c2-2 4-3 7-4z" class="F"></path><path d="M504 839c-1 0-2 0-3-1h0l2-3c1-1 4-2 6-1 2 0 4 0 5 2-1 0-2 0-2 1h-1-2c-2 1-4 1-5 2z" class="a"></path><path d="M579 624l1-1h2l1-1c1 0 1 0 2-1 1 0 2 0 4-1l1 1 1-1c1 0 3-2 4-2 2 0 3 0 4 1 0 1 0 1-1 1v1 1c1 0 1 0 1 1s0 2-1 4h0c-1 1-1 1-1 2-1 0-2 0-3 1-1-1-2-1-4-1l-1-1c-2 0-3 0-4-1l4 4-1 1-9-8z" class="R"></path><path d="M591 624l2-2h2v2c0 1 0 1 1 2l-1 2-1-1h0l-3-3z" class="S"></path><path d="M585 627c0-1-2-2-2-3s1-1 2-2l1 2h1c1-1 3-1 4 0l3 3h0l1 1h-2-4c-2 0-3 0-4-1z" class="T"></path><path d="M638 596c7-1 12-2 19 0 4 1 7 3 10 5l1-1c1-1 2-1 4-1h0c-2 1-4 2-6 4-1 1 0 1-1 1l-1-1h0-1c-8-6-18-4-28-4-4 5-6 9-9 14l-1-1 1-1 3-6c-1 0-2 1-2 1 0-3 3-5 5-6l2-2h1c2 1 6 0 8 0-1-1-4-1-5-1v-1zM434 258c6-3 13-6 19-8l16-7c4-1 8-3 13-3-4 2-7 4-11 5l-26 12c-3 1-6 1-8 3l-4 1h0-2l-1 1v-1c1-1 0-1 1-1s2 0 3-1v-1zm-115-57l1 1c1 0 1-1 2-1h3c4 1 9 3 14 3 1 1 2 1 2 1h2l10 9v1c-4-2-8-4-13-5-7-1-12-1-17 3v-2c1-1 3-2 5-3 1 0 1 0 2-1l-1-1c-1-1-2-1-3-1l-2-1c-2 0-4-1-5-2v-1z" class="Z"></path><path d="M302 386h0v15 18 46 12 6 22l-1-1v-5h0v-26-83c1-1 0-3 1-4z" class="d"></path><defs><linearGradient id="AZ" x1="397.037" y1="264.34" x2="424.741" y2="272.744" xlink:href="#B"><stop offset="0" stop-color="#312f32"></stop><stop offset="1" stop-color="#7c7d7d"></stop></linearGradient></defs><path fill="url(#AZ)" d="M378 281h5v-1h1v-1c5-1 8-4 13-6l37-15v1c-1 1-2 1-3 1s0 0-1 1v1l1-1h2 0c-4 3-10 4-15 6l-25 11c-2 0-5 2-8 2 1 1 0 1 1 2l-8-1z"></path><path d="M587 597h1l2-1-1 2c2 1 7-1 8-2l1 1c1 0 3-1 4-1v1l-4 2v1c2-1 2-1 4 0l-20 8c-2 1-4 1-7 1h0c1-1 1-1 3-2h0 0l-1-1h-1c2-1 2-2 3-4 3-1 6-3 8-5z" class="j"></path><path d="M587 597h1l2-1-1 2c2 1 7-1 8-2l1 1-17 7c1-2 2-3 3-4h1c2-1 2-1 2-3z" class="M"></path><path d="M587 597c0 2 0 2-2 3h-1c-1 1-2 2-3 4l-2 1h1 0 2c3 0 4-2 7-3 2-1 4-1 6-1-1 1-4 2-6 2-1 0-2 1-3 1 0 1-1 1-2 2h-2v2c-2 1-4 1-7 1h0c1-1 1-1 3-2h0 0l-1-1h-1c2-1 2-2 3-4 3-1 6-3 8-5z" class="O"></path><defs><linearGradient id="Aa" x1="415.238" y1="512.253" x2="414.115" y2="523.18" xlink:href="#B"><stop offset="0" stop-color="#131213"></stop><stop offset="1" stop-color="#3a3b3d"></stop></linearGradient></defs><path fill="url(#Aa)" d="M423 511v1c0 2 0 2 1 3-2 2-4 4-6 5-2 0-4 2-6 2l-6 3-1-1h-1c-1 1-1 1-2 1-2 0-4 1-6 1 2 0 3-1 4-2-2 0-5 1-7 1h-3c4-2 10-4 14-6 5-3 10-4 16-6l3-2z"></path><path d="M404 519c1 0 2 1 3 1 0-1 1-1 2-1v1c-3 2-6 2-9 4-2 0-5 1-7 1h-3c4-2 10-4 14-6z" class="G"></path><path d="M610 660c1 1 2 1 4 1l7-5c1 1 2 3 3 5 1 0 2-1 3-1l1 3h0c-2 2-3 3-5 4-1 0-2 1-2 1h-1-3c-1 0-2 1-2 2-1 1 1 2-1 3l-1-3c0-1-1-1-1-2v-1c-1 0-1-1-1-2l-2-4-1-1c-1-1-1-1-1-2s0-1 1-2l2 4z" class="D"></path><path d="M604 627c3 3 6 7 9 10v1c-1 1-3 2-4 3v1l2 1c-1 0-2 0-2 1h-2c0 1 0 1-1 1l-1 1h-1l-1-1c-1-1 0-1-1-2h-1l-1-1c-1 0-1-1-2-1l1-3c-2-1-2-1-3-2l-2-2-1-1c3 0 6 1 8 0 2 0 3-1 4-2v-1c0-1 0-2-1-3z" class="J"></path><path d="M390 525h3c2 0 5-1 7-1-1 1-2 2-4 2 2 0 4-1 6-1 1 0 1 0 2-1h1l1 1-15 6v2h-1v2l-1 1-1-1c-2 0-3-1-6 0h0l-1-1h-1v-1c-1 0-2 0-3 1h-1c-2 0-4 0-5 1-1-1-1 0-1-1 3-3 6-3 10-5l10-4z" class="U"></path><path d="M396 526c2 0 4-1 6-1 1 0 1 0 2-1h1l1 1-15 6v2h-1v2l-1 1-1-1c-2 0-3-1-6 0h0l-1-1h-1v-1l4-1c1 0 2-1 3-1h1l1-1c1 0 1 0 2-1 2-1 3-2 5-3z" class="R"></path><path d="M391 531v2h-1v2l-1 1-1-1c-2 0-3-1-6 0h0l-1-1 10-3z" class="G"></path><path d="M578 623c-2-1-3-2-3-2v-1c2-1 4-2 7-3l18-6 1 6c0 3 1 8-1 11l-2-1c1-2 1-3 1-4s0-1-1-1v-1-1c1 0 1 0 1-1-1-1-2-1-4-1-1 0-3 2-4 2l-1 1-1-1c-2 1-3 1-4 1-1 1-1 1-2 1l-1 1h-2l-1 1-1-1z" class="I"></path><path d="M578 623l1-1c2 0 5-3 8-3 2-1 4-1 6-2 1 0 1-1 2-1l1 1h0c2 0 3 1 4 2v1l-2 1v-1c1 0 1 0 1-1-1-1-2-1-4-1-1 0-3 2-4 2l-1 1-1-1c-2 1-3 1-4 1-1 1-1 1-2 1l-1 1h-2l-1 1-1-1z" class="U"></path><path d="M665 490c0 2 0 3 1 5h0v1l-3 7c-1 0-2 2-2 2v6l-1 4v2l-1 4v3c0 1 0 0-1 1 0 2 1 6 0 7-1-1-1-1-1-2 0-4 0-8 1-12 1-1 0-3 1-4v-3h-1c-1 1-1 2-2 4-1 4-2 7-2 11l-1 1-1 1v1c-2-2-6-4-8-5 1 0 2 0 3 1 2 0 2 1 4 1l1-1v-1c1-1 1-2 2-3v-1c0-1 0-2 1-3v-1-2h1v-2c1 0 1-1 1-1-1-1-1-1-2-1h-2c-1-1-1 0-2 0v-2l1-1 2-2v-2l4-2c2-2 5-3 6-6v-2h-1l2-3z" class="B"></path><path d="M665 490c0 2 0 3 1 5h0c-3 4-6 8-8 13v1c-2 1-4 0-6 0 2-2 4-3 5-5 0-1 1-2 1-3 2-2 5-3 6-6v-2h-1l2-3z" class="W"></path><path d="M576 748c-1-1-2-3-3-3-1-2-2-1-2-4 0-1 1-3 3-4 2-2 4-3 6-3l1 16c0 3 1 5 0 8l-1 1-4-2-1-1h-3 0c1-1 1-1 2-1l1-1h-1v-1c2-2 2-2 2-5z" class="D"></path><path d="M576 748c0-1 0-2 1-4v-1l1-1c2 2 1 8 1 10l-1 1-1-1c-1 1-1 2-2 2h-1v-1c2-2 2-2 2-5z" class="I"></path><path d="M330 225c-1 0-3 1-5 0-1 0-1 1-2 1h-1l-1-2c2-3 3-3 5-5 1-1 2-2 4-3 0-1 1-1 2-2v1h2c2-1 4 0 6 2 1 0 1 1 2 2 1 2 3 5 4 8-2-1-3-2-5-4l-2 1c-1 0-1 0-2-1h-3c1 1 1 1 2 1v1h0l1 1c-2 0-4-1-7-1z" class="b"></path><path d="M330 225v-1c1-1 1-2 2-2 1-1 1-2 2-3s1-1 3-1c2 2 3 3 4 5l-2 1c-1 0-1 0-2-1h-3c1 1 1 1 2 1v1h0l1 1c-2 0-4-1-7-1z" class="Z"></path><path d="M552 623c1-1 2-1 2-2v-1c1-1 1-1 1-2v-2l1-1v-3c1 1 1 2 0 3v1h1l-16 70c-1-2 0-4-1-6 0 0 0-1 1-2v-1c0-1 0-2 1-3v-1-1s1-1 1-2l1-4v-2l2-10 1-1v-2c0-1 0-1 1-2v-2-3c1-1 1-2 1-3 1-1 1-1 1-2l1-4v-2l1-1c-1 0-1 0-1-1h-1l2-8z" class="t"></path><path d="M717 319h0c0 1 0 2 1 4h0v15c0 4 1 8 0 11v8 24 54c-2-5 1-11-1-16l-1 3v-11-18l1-74z" class="Z"></path><path d="M525 251v-2l1 1 1-1c1-1 1-3 3-4 1-1 2-1 3-1h1c3 0 8 2 11 0 9 3 17 7 26 11 2 1 7 3 8 3 4 2 8 3 11 5 0 1 0 2 1 3-2-1-4-1-6-2-3-2-6-4-10-4-1 0-3-1-3-2-12-6-23-11-37-12v-1l-1 1h-2c-3 3-2 6-5 9-1-2-1-3-2-4z" class="B"></path><path d="M635 641c4 0 6-1 9-2l2 2-1 1h0c-2 1-4 3-6 4-1 1-2 2-2 3v1c1-1 2-1 3-2l1 1-1 2c-1 1-1 1-2 0h0-1c0 1 0 1-1 2v1h0 0c-1-1-1 0-1-1v-1h0l-1 1v2c-1 1-1 1-1 2v1c-1 0-2 1-2 2h0 1c0-1 1-1 2-2l4-4 3-3h0c1 1 2 0 3 0v1l-1 2h0l-1-1h0c-1-1 0-1-1 0-1 0-1 1-2 2 0 1-1 2-1 3v1c-1 1-2 2-2 3-1 1-1 1-2 1l-1-1c-1 0-2 0-2 1-1 3-3 5-5 7-2 1-4 3-5 4-1-2 0-3 1-5l-1-1s1-1 2-1c2-1 3-2 5-4h0c1-1 2-2 2-3s1-1 1-2c1-1 2-2 2-4 1-1 1-3 2-4v-2h-1l-1-1h1c0-1 0-1-1-1v-3h2v-2z" class="P"></path><path d="M639 655c0 1-1 2-1 3v1c-1 1-2 2-2 3-1 1-1 1-2 1l-1-1c-1-1-1-1-1-2l7-5z" class="L"></path><path d="M628 663c1 2 1 2 0 3s-1 1-2 1c-1 1-2 1-2 2s0 1 2 1c-2 1-4 3-5 4-1-2 0-3 1-5l-1-1s1-1 2-1c2-1 3-2 5-4z" class="J"></path><path d="M715 516l2-2v14c-2 8-2 18-4 26-1-2-1-3-1-5v3h-1l-6 24c-1 3-2 8-4 11l1-4v-1c0-2 1-4 2-6l3-14c1-4 1-7 2-11 1-2 1-4 1-6 1-6 2-10 2-15l1-1v6h0v-2-2c1-1 0-3 0-4s1-2 2-2v-9z" class="E"></path><path d="M715 516l2-2v14c-2 8-2 18-4 26-1-2-1-3-1-5v3h-1c0-2 1-7 2-10 0-5 1-11 2-17v-9z" class="Z"></path><path d="M383 574h1c1 1 2 2 3 2 2 2 4 4 6 4 0-2 0-2 1-3 2 1 3 1 5 2l-2 1v1l3 1c1 1 1 1 2 1h1 2c1 1 2 1 3 3 1 0 3 1 3 2s1 2 2 3v1l5 2-1 1h-1l-6-3-1 1c-2 0-3-1-5-1-1 0-3-1-3-2-5-3-11-6-15-11-1-1-3-4-3-5z" class="L"></path><path d="M383 574h1c1 1 2 2 3 2v1c1 1 1 2 2 3h0c-1 0-2-1-3-1-1-1-3-4-3-5z" class="C"></path><path d="M397 581l3 1c1 1 1 1 2 1h1 2c1 1 2 1 3 3v1h1c-1 1-2 1-3 1h0c-2 0-4-2-6-3-1-1-2-2-3-4zm177 191h1c-1 2-2 3-3 4l-2 4-21 29v1-2c1-2 3-4 4-6 0-2 0-4 1-5v-2l-1-1c0-5 5-7 7-11l2-3c1-1 1-2 1-3 1-1 2-3 3-3h3 2 1l2-2z" class="N"></path><path d="M381 663h0c-1-3-2-5-3-7 0-1 0-2-1-3s-2-1-2-3h0c-1 0-1-1-2-1v-5c0-1 0-3-1-5l1-1 2 1h4l3 1c2 1 3 1 4 3 0 2-1 2-2 3v1c1-1 1-1 2 0 1 0 1 0 1 1s-1 2-1 4l-2-1c-1-1-1-2-1-3l-1-1-1 1-1-1h-2c0 2 0 2 1 4h0c0 1 1 1 1 2l4 4c1 0 1 0 1 2 1 1 2 1 3 2l1-1c0 1 1 1 1 2h1v1h-2-1l-1-1v2h-1c0 1 1 2 2 3h-3v-2h0c-1-2-1-2-4-2z" class="B"></path><path d="M404 546l1-1c0-1 0-1 1-2v-2l2-2c-1 2-1 3-1 5l-1 1h0c-1 2-1 3-1 6l1 1c0 1 0 1-1 2 0 1 0 2 1 3l-1 2 1 1v1c0 1 1 2 2 3h0v2 1s0 1 1 1v1s1 1 2 1 1 1 2 2l4 2v-1h0l-2-1v-1l1-1h1c1 1 3 0 5 1 0 1 0 1 1 1l-1 2h-1c1 0 1 0 2 1l3 1c3 1 6 2 10 3-2 1-2 1-4 0l-12-2-22-13-1-1c-2 0-3 1-3 1l-2-1 1-3v-1l3 2h1c2 0 1-1 2-2h2l1 1h0l1-2-2-3c1-1 1-1 2-1l-1-1c0-1 0-1-1-2h0c1-1 1-2 1-3l1-2h1z" class="G"></path><path d="M403 546h1v4c0 1 0 1 1 3h-1l-2 3h1 1v3l1 1c0 1 2 4 2 5-1-1-2-1-3-2v2c-2-1-3-2-5-3l-1 1h1l-1 1-1-1c-2 0-3 1-3 1l-2-1 1-3v-1l3 2h1c2 0 1-1 2-2h2l1 1h0l1-2-2-3c1-1 1-1 2-1l-1-1c0-1 0-1-1-2h0c1-1 1-2 1-3l1-2z" class="I"></path><path d="M545 220h10 17 44c5 0 11 1 16 0h10l-1 1h-19c-4 0-8 0-12 1-3 1-6 0-8 0-8 1-17 0-24 1h-26c-2-2-6-2-9-2v-1h2z" class="o"></path><defs><linearGradient id="Ab" x1="609.931" y1="558.399" x2="620.337" y2="560.88" xlink:href="#B"><stop offset="0" stop-color="#1f1e1e"></stop><stop offset="1" stop-color="#404042"></stop></linearGradient></defs><path fill="url(#Ab)" d="M612 546v1c1 3 1 5 1 8l1 1c-1 1-1 1 0 2 1-2 2-5 3-7v1l-1 2h2c1-1 0-2 2-3h1v1c0 1 1 2 2 3 0 1 0 2-1 4l1 2h-1v1l3-2s1 0 1 1c1 1 1 3 1 4l-1 1-3-3h0c-1 0-2 1-3 1l-6 4c0 1-1 1-2 2l-5 2-1-1h0c-1-1-1-2-2-3l2 1h0 1c1-1 1-2 1-3 1-3 2-3 2-6 1-1 1-3 1-4 1-4 1-6 1-10z"></path><path d="M606 569v1c2-1 2-1 3-2s2-2 2-4h0 1 0l1 2v1h1v1h0c0 1-1 1-2 2l-5 2-1-1h0c-1-1-1-2-2-3l2 1z" class="D"></path><path d="M447 793h3 1c0 1 0 1 1 1h0l15 21c4 4 6 8 9 12 4 6 8 11 11 16l-1 1-3-1c1-1-14-18-15-22l-4-5-11-16c-2-2-4-4-6-7z" class="R"></path><path d="M447 793h3 1c0 1 0 1 1 1h0l15 21v1l-1-1c-2-4-5-9-9-12v1c2 4 7 7 8 11l1 1v1c1 2 2 2 2 4l-4-5-11-16c-2-2-4-4-6-7z" class="S"></path><defs><linearGradient id="Ac" x1="257.476" y1="279.547" x2="282.686" y2="286.58" xlink:href="#B"><stop offset="0" stop-color="#afaead"></stop><stop offset="1" stop-color="#e8e8e8"></stop></linearGradient></defs><path fill="url(#Ac)" d="M260 270c1-1 1-1 3-2 1 5 2 9 4 13 4 6 10 12 15 18-1 1-1 1-2 1s-5-4-6-4c-5-3-11-5-16-8h0l-1-1c1-2 2-3 3-4v1l1-1-1-2c0-1 0-2 1-4l1 1c0-3-1-4-2-7h-1l1-1z"></path><path d="M401 655v-1c2 0 3 3 6 3 0 2-1 3 0 4v1 2c0 2 1 4 1 6v2c-1 1-2 2-4 2h0c-1 1-1 0-2 1l-1-1c-1 0-1-1-2-1-1-2-2-2-4-3v-2-2c-1-1 0-1-1-1l-2-2 1-1v-1-3h1v-2-1h1l2 1c1 0 2-1 4-1z" class="E"></path><path d="M403 662h2v5c0 1 0 2 1 3 0 2-1 2-2 4h0c-1 1-1 0-2 1l-1-1v-2c1 0 1-1 1-2h0v-1-2h0v-1-2l1-1v-1z" class="G"></path><path d="M401 655h1c0 1 1 1 2 2l2 1h0l-3 3-1 1h-2v-3h-2-1v1l-1-1-3-1h1v-2-1h1l2 1c1 0 2-1 4-1z" class="D"></path><path d="M396 659h0l2-2c1 0 3 2 4 1 1 0 0-1 1 0v3l-1 1h-2v-3h-2-1v1l-1-1z" class="C"></path><path d="M393 658l3 1 1 1v-1h1 2v3h2 1v1l-1 1v2 1h0v2 1h0c0 1 0 2-1 2v2c-1 0-1-1-2-1-1-2-2-2-4-3v-2-2c-1-1 0-1-1-1l-2-2 1-1v-1-3z" class="I"></path><path d="M393 661l1 1v1h3v2 1h-2c-1-1 0-1-1-1l-2-2 1-1v-1z" class="C"></path><path d="M397 666h0v2c1 0 1 1 2 2h1c0 1 1 2 1 2v2c-1 0-1-1-2-1-1-2-2-2-4-3v-2-2h2z" class="H"></path><path d="M291 367l1-3v-16-10c0-1 1-3 1-5 0-3-1-10 1-12v111 6h-1v-8-1c1-3 0-7-1-11l1-12c-1-6-1-13-1-19 0-2 1-6 0-8s0-7 0-9v-1h1l-2-2z" class="O"></path><path d="M480 245h6c1 1 3 2 4 3s1 3 2 4l1-1 1 1-1 1 4 8 6 16 1 4c1-1 0-2 1-3 0-1 1-2 1-2l1-6v13 9-2c0-1 0-1-1-2v-1l-2-2v5l-2-2c-1-3-2-6-4-10l-1-1-5-12c-1-2-2-4-2-6-1-3-1-6-2-9l-3-3v-1c-1 0-1 0-1 1-4 1-7 1-10 1h-1c2-1 5-2 7-3z" class="D"></path><path d="M493 253l4 8 6 16 1 4c1 1 1 2 1 3h-1l-7-17c-2-4-5-9-4-14z" class="Q"></path><path d="M419 690l1 1c-2 1-4 2-5 3v3h1v2c-2 3-2 5-2 9 4 7 10 8 16 11 1 1 3 1 4 2h2c1 1 2 1 3 1 1 1 1 1 3 2h0c1 0 1 1 2 1l1 1c2 1 4 3 5 5l1 1 1 3-2 1c-3-6-8-9-14-11l-2-1h-2l-2-1-3-1-3-1c-3-1-6-2-9-4l-3-3c0-1-1-1-1-2-2-4-2-10-1-14 2-5 5-6 9-8z" class="K"></path><path d="M415 694v3h1v2c-2 3-2 5-2 9v-3c-1 0-1 2-2 3l-1-1c0-3 0-6 1-9v-1l3-3z" class="l"></path><path d="M402 605c1-1 1-1 2 0 1 2 1 4 2 6v1c-1 1 0 1 0 2v5l1 2h1v-1c1 0 1-1 2-1-1 1-1 3-2 4-2 4-5 9-9 10-2 1-4 1-6 1 0-1 2-1 3-1 1-2 2-3 2-5s1-3 1-5c-1 0-1 0-2-1l1-1c0-2 0-3-1-5-1 0-1-1-2-2v-1h-4-2v-1h1v-5h0c1 0 1 1 2 1 0 1 0 2 1 2h0 0v-3h3 1c2 0 4 0 6-2h0-1z" class="B"></path><path d="M400 621h2l2 1 1 2v2c-1 1-2 2-4 2h-1l1-1c-1-3-1-4-1-6z" class="P"></path><path d="M400 621h2l2 1-2 2c-1-1-1-2-2-3z" class="D"></path><path d="M408 737v-1c0-1-1-2-2-3l1-1 9 13 1-1 31 44v1l-1-1h-3l-4-6c-1-2-2-3-2-4l-3-3-3-2-3-3c-3-4-6-8-8-12-4-5-8-11-12-16l3 1c-1-3-3-4-4-6z" class="Y"></path><path d="M430 767s1 0 2-1c1 1 0 1 1 2l2 2 1 2c1 1 3 4 4 6l1 1-1 1-2-2h0l-3-3c-1-3-4-6-5-8z" class="X"></path><path d="M409 742l3 1c6 8 11 16 18 24 1 2 4 5 5 8l-3-2-3-3c-3-4-6-8-8-12-4-5-8-11-12-16z" class="F"></path><path d="M290 237c1 2 2 2 3 3v1c1 1 2 1 4 2 1 1 1 2 3 3h1v-1h1l1 1 3 3c4 4 8 6 12 9h1c0 1 1 1 2 0 2 3 4 6 5 8h-2c-4-5-10-8-16-10l-8-2c-5-1-8 0-12 2l-1 1c-1-3-2-5-3-7l-1-1c0-2 0-2-1-4 1 0 1-1 2-1l1 1h1c1 1 2 1 3 2h1l-1-1 1-1-1-1h2v-2c0-1 0-2-1-3v-2z" class="S"></path><path d="M295 248h1l1 2-1 2-1-1v-3z" class="M"></path><path d="M282 245c1 0 1-1 2-1l1 1h1c1 1 2 1 3 2h-1c-1 0-2-1-2-1h-1v1c1 1 2 2 2 4 4 2 7 0 10 2h3v1c-5-1-8 0-12 2l-1 1c-1-3-2-5-3-7l-1-1c0-2 0-2-1-4z" class="T"></path><defs><linearGradient id="Ad" x1="314.152" y1="265.594" x2="302.848" y2="240.906" xlink:href="#B"><stop offset="0" stop-color="#5b5c5e"></stop><stop offset="1" stop-color="#7b787b"></stop></linearGradient></defs><path fill="url(#Ad)" d="M308 256c-1-2-2-3-3-3s-2 0-3-1h-1-3c0-1 0-2 1-3h0v-1c-1 0-2 0-3-1h0l-2 1h0l1-2-1-2c2 0 2 1 4 2h0c3 0 6 2 8 3 4 4 8 6 12 9h1c0 1 1 1 2 0 2 3 4 6 5 8h-2c-4-5-10-8-16-10z"></path><path d="M718 338c1 3 0 7 1 10h0l1 2c0 4-1 10 0 15v8 38c-1 7 0 15 0 23v44 11c0 2 0 3-1 5h0l1 2-1 1c0 4 1 8 0 12s-1 9-1 13c0 2 0 4-1 6v-14l1-50c1-3 0-6 0-9v-20-54-24-8c1-3 0-7 0-11z" class="a"></path><defs><linearGradient id="Ae" x1="643.448" y1="465.651" x2="611.552" y2="452.84" xlink:href="#B"><stop offset="0" stop-color="#b5b4b5"></stop><stop offset="1" stop-color="#e4e3e3"></stop></linearGradient></defs><path fill="url(#Ae)" d="M626 480c5-1 8-2 11-6s4-10 3-15c-1-4-3-8-7-11-4-2-9-3-13-2-5 1-7 4-10 8v-7l1-3c6-3 11-5 18-3 6 2 10 5 13 10s3 12 1 18c-1 5-5 11-9 13-2 0-4 1-5 1v-1l1-1h-3l-1-1z"></path><path d="M550 631h1c0 1 0 1 1 1l-1 1v2l-1 4c0 1 0 1-1 2 0 1 0 2-1 3v3 2c-1 1-1 1-1 2v2l-1 1-2 10v2l-1 4c0 1-1 2-1 2v1 1c-1 1-1 2-1 3v1c-1 1-1 2-1 2 1 2 0 4 1 6l-10 45-3 14c-1 2-2 4-2 6l-2 3c-1 2-1 4-3 6 1-6 3-13 4-19l7-32 18-78z" class="w"></path><path d="M540 680c1 2 0 4 1 6l-10 45-3 14c-1 2-2 4-2 6l-2 3c0-4 1-9 3-12 1-4 1-7 2-11 1-3 2-7 2-10 0-1 0-2 1-2v-2-1c1-2 1-4 2-6 0-2 0-4 1-6v-1l1-2v-2-1-1l2-7 1-5 1-1v-4z" class="u"></path><path d="M587 479c0 4-1 6-1 9-1 6-3 11-4 16s-2 10-4 15l-7 35v1h2l-6 22-3 11-7 28h-1v-1c1-1 1-2 0-3v3l-1 1v2c0 1 0 1-1 2v1c0 1-1 1-2 2l30-127h0l5-17z" class="w"></path><path d="M564 588c-1-2-1-3 0-5l3-12v6l-3 11z" class="s"></path><path d="M567 571c0-5 2-12 4-17v1h2l-6 22v-6z" class="l"></path><path d="M416 625c2-3 2-9 3-12 6 0 13 3 18 6 1 0 2 1 3 2l-1 1-2 1c-2 2-7 5-8 8h0l-3 1c-2 1-4 2-5 3-1 0-1 1-2 1h-3c-2 0-2 0-4-2 0-3 2-6 4-9z" class="H"></path><path d="M425 632c-2 0-2 0-3-1 0-3-3-2-4-4l1-1v-6c0-2 0-3 1-5h2c1 1 2 1 4 1 4 1 9 4 13 6l-2 1c-1 0-2-1-3-1v-1c-1-1-1-1-2-1-2 0-6-2-7-2l-1 1c-2 0-1-1-3 1v1c0 1 0 3-1 4l1 1h-1c0 1 0 1 1 2h1c1 1 0 1 1 1 1 1 1 2 2 3z" class="I"></path><path d="M425 632c-1-1-1-2-2-3-1 0 0 0-1-1h-1c-1-1-1-1-1-2h1l-1-1c1-1 1-3 1-4v-1c2-2 1-1 3-1l1-1c1 0 5 2 7 2 1 0 1 0 2 1v1c1 0 2 1 3 1-2 2-7 5-8 8h0l-3 1h-1z" class="F"></path><path d="M527 255c3-3 2-6 5-9h2l1-1v1h0c-1 1-1 1-2 1-1 1-1 2-3 2 0 2 0 2 1 4-1 1-2 1-3 2 0 2 1 3 2 6-1 3-3 7-4 11l-5 8c0 1-1 2-1 3 0 0-1 1-1 2-1 1-2 2-2 4l-2 3c0-1-1-1-2-2v2c0-6 1-12 0-18v-6c1-7 4-13 9-18v1c0 2-2 4-2 6l-1 1h-1c-1 1-2 1-2 3 0 4-1 7-1 11 2-2 2-6 5-6v-2c-1 0-1 0-2-1h0c0-1 1-2 2-2l1-1c1 0 1-3 2-4 1-2 1-2 2-3v-2c1 1 1 2 2 4z" class="E"></path><path d="M525 251c1 1 1 2 2 4-1 11-7 21-12 31 0-12 10-22 10-33v-2z" class="f"></path><defs><linearGradient id="Af" x1="516.858" y1="273.05" x2="525.71" y2="274.546" xlink:href="#B"><stop offset="0" stop-color="#141214"></stop><stop offset="1" stop-color="#2e302e"></stop></linearGradient></defs><path fill="url(#Af)" d="M527 255c3-3 2-6 5-9h2l1-1v1h0c-1 1-1 1-2 1-1 1-1 2-3 2 0 2 0 2 1 4-1 1-2 1-3 2 0 2 1 3 2 6-1 3-3 7-4 11l-5 8c0 1-1 2-1 3 0 0-1 1-1 2-1 1-2 2-2 4l-2 3c0-1-1-1-2-2l2-4c5-10 11-20 12-31z"></path><defs><linearGradient id="Ag" x1="411.028" y1="515.944" x2="400.673" y2="543.955" xlink:href="#B"><stop offset="0" stop-color="#1a1b1b"></stop><stop offset="1" stop-color="#3b3a3b"></stop></linearGradient></defs><path fill="url(#Ag)" d="M418 520l3-1c1 0 2 0 3 1-1 0-3 2-5 3l-2 2c-1 2 0 3 0 4h2c-1 0-1 1-1 2l1 1v2l-6 6v2l-2 1c-2-2 0-3-2-5l-1 1-2 2v2c-1 1-1 1-1 2l-1 1h-1l-1-2h1v-1c-1-1-3-2-3-3l1-1-2-2-1-2v-1h0l-1 2h-1l-2-1v-1c-1 0-2 1-3 0v-1-2l15-6 6-3c2 0 4-2 6-2z"></path><path d="M415 529v-1c0-3 2-4 4-5l-2 2c-1 2 0 3 0 4-1 1-1 2-2 2l-1-2h1z" class="J"></path><path d="M412 535l1-1 1-1c-1-1-1-2-1-3l1-1 1 2 1 1-1 1v2h-1s-1 0-2 1v-1z" class="D"></path><path d="M412 535l-1 2-1-1 1-3v-2l2-2-1-1 1-1h1l1 2h-1l-1 1c0 1 0 2 1 3l-1 1-1 1z" class="G"></path><path d="M680 368c1-1 2-1 4-2 1 9 3 17 3 26 2 24-3 50-10 73 0 1-1 1-1 2h-2 0-1c0-4 2-7 2-10 6-24 10-47 8-71l-3-18z" class="Z"></path><path d="M302 386v-13-5-2c0-1 1-1 1-2l1 39v-1c1-1 1-2 1-4l1-1v10c1 2 0 4 0 5v11 5 15c0 6-1 13 0 19v5c0 5 0 10-1 15v3c0 1 0 2-1 4l1 16h-1-1v-3-12c0-2 0-5-1-7v-6-12-46-18-15z" class="j"></path><path d="M304 403v-1c1-1 1-2 1-4l1-1v10c1 2 0 4 0 5v11 5c-1 3-1 6-1 10v3h-1v-38z" class="B"></path><path d="M304 441h1v-3c0-4 0-7 1-10v15c0 6-1 13 0 19v5c0 5 0 10-1 15v3c0 1 0 2-1 4v-17-31z" class="E"></path><path d="M634 248l1-1 2 1h0l-3-3v-1c2 1 3 3 5 3l1 1c1 1 1 2 2 3l2 1v1l1 1 2 2 1-1 1 1h0v1l1 1v1 1h1c0 1-1 1 0 2-1 2 0 2-1 4 0 1 0 0-1 2v1h1c-1 1 0 1-1 1l-2 2h-1-1 1l2-2h-1c-2 0-2-2-4-2h-1v1h1c0 1 1 2 1 3h0c-1 1-1 1-2 0v-1h-1l1 1-1 1c-1 0-1-1-2-2v-3h-1v1l-1 1c0-1 0-2-1-3h-1l-1 1c-1-1-2 0-3-1 0 0-1-1-2-1l-1 1-2-2c0-1-2-1-2-2l-1 1-1-1c-2 0-1 0-3-1l1-1h-1v-1l-1 1v-1-1h0l1-1h1 1v1h3c1-1 1-2 2-3h2c0-2 1-3 3-4l2-2h0 2l-1-1h-1l1-1z" class="G"></path><defs><linearGradient id="Ah" x1="394.433" y1="539.206" x2="388.826" y2="546.612" xlink:href="#B"><stop offset="0" stop-color="#46474a"></stop><stop offset="1" stop-color="#646366"></stop></linearGradient></defs><path fill="url(#Ah)" d="M391 533v1c1 1 2 0 3 0v1l2 1h1l1-2h0v1l1 2 2 2-1 1c0 1 2 2 3 3v1h-1l1 2-1 2c0 1 0 2-1 3h0c1 1 1 1 1 2l1 1c-1 0-1 0-2 1l2 3-1 2h0l-1-1h-2c-1 1 0 2-2 2h-1l-1-2-2-1-1-2-9-8-7-8c1-1 1-3 1-4s-1-1-1-2h1c1-1 2-1 3-1v1h1l1 1h0c3-1 4 0 6 0l1 1 1-1v-2h1z"></path><path d="M380 534h1l1 1h0c3 0 5 1 7 1 1 0 2 1 2 1v2c-2-1-2 0-4 0-1-1-2-2-3-2-1-1-2-2-4-2h-1 0l1-1z" class="F"></path><path d="M391 533v1c1 1 2 0 3 0v1l2 1-1 2-3-1h-1s-1-1-2-1c-2 0-4-1-7-1 3-1 4 0 6 0l1 1 1-1v-2h1z" class="I"></path><path d="M394 535l2 1-1 2-3-1c1-1 2-1 2-2z" class="G"></path><path d="M392 556c0-2 0-4-1-6v-1-1-1h1l1 1 1 1v1c1 2 1 4 1 7l-2 1-1-2z" class="T"></path><path d="M377 536c3 0 4 1 6 4 1 1 1 2 2 4 0 1 1 2 2 3l-1 1h-3l-7-8c1-1 1-3 1-4z" class="Q"></path><path d="M398 547h1c0 2 0 4 1 5s1 2 1 3l2 3-1 2h0l-1-1h-2c-1 1 0 2-2 2h-1l-1-2-2-1 2-1c0-3 0-5-1-7v-1l-1-1c1 0 1 0 2 1v1l3-3z" class="L"></path><path d="M395 559h0c1-1 1-2 1-4h1 0c1 1 2 2 3 2l1 1v1h-2c-1 1 0 2-2 2h-1l-1-2z" class="F"></path><path d="M396 536h1l1-2h0v1l1 2 2 2-1 1c0 1 2 2 3 3v1h-1l1 2-1 2c0 1 0 2-1 3h0c1 1 1 1 1 2l1 1c-1 0-1 0-2 1 0-1 0-2-1-3s-1-3-1-5h-1c-1-1-4-4-3-6v-3l1-2z" class="K"></path><g class="F"><path d="M402 548l-2-1c1-2 1-2 2-3l1 2-1 2zm-3-11h0v3l-1 1-2-2c1-1 1-1 1-2v-1l1-1 1 2z"></path><path d="M395 541l1 1 1-1c1 0 2 1 3 2l-1 1v3h-1c-1-1-4-4-3-6z"></path></g><defs><linearGradient id="Ai" x1="598.591" y1="528.319" x2="627.456" y2="547.762" xlink:href="#B"><stop offset="0" stop-color="#181817"></stop><stop offset="1" stop-color="#454546"></stop></linearGradient></defs><path fill="url(#Ai)" d="M601 523c2 1 5 1 7 1 2 1 3 2 5 2l1 1c1 1 2 1 4 1v1l1 1c1 0 2 1 3 1h0c1 0 1 0 2 1 0 1 1 2 2 3v1l-1-1h-1c0 1 1 2 2 2h1 1c0 1 1 1 2 2 2 0 4-1 6-2 1-1 1-1 2-1h1c1-1 2-1 3 0-2 1-4 3-6 4h0c-2 5-5 8-8 13-2 2-3 6-5 8l-1-2c1-2 1-3 1-4-1-1-2-2-2-3v-1h-1c-2 1-1 2-2 3h-2l1-2v-1l-1-2v-1-1l-1-1v-4c0-1 1 0 0-1h-3l-1-1 1-1h0c-1-2-1-4-2-6-1 0-1-1-1-2l-1-1-3-2-1-2h-1c-1-1-1-1-2-3z"></path><path d="M628 540l1 1v1l-2 1c-1 0-1 0-2-1h0c1-1 2-1 3-2z" class="F"></path><path d="M618 535h2l1 1h0-1v2c0 2 0 4 2 5h-1c-2-1-3-2-3-3l-1-2 2-1-1-2z" class="I"></path><path d="M726 330v-1-9h1v3 2l1 1c0-2-1-5 0-6 1 1 1 3 1 5 1 3 1 6 2 9 0 3-1 6 0 8v10h0c-1 6 0 13-1 19 0 2 1 8 0 10 0 1 0 6-1 8v22c0 2 0 6 1 8h-1c-1 3 1 6-1 9h-2c0-3 1-11 0-13h-1c-1-4-1-10 0-15l-1-28h1 0l1 5v-14c0-7 1-13 0-19 0-5-1-9 0-14z" class="P"></path><path d="M724 372h1 0l1 5v14c-1-3-1-7 0-11-2 2 0 17-1 20l-1-28z" class="C"></path><path d="M711 552h1v-3c0 2 0 3 1 5l-1 5-3 12c-5 13-9 26-15 38l-9 17c-2 4-6 8-8 13 0 2-2 3-3 5s-2 4-4 7c-1 1-2 2-3 4-2 6-7 10-10 16v1l-7 7c-4 5-7 10-10 16l-3 3c-1 2-2 4-3 5-2 3-5 6-7 8 0 1-1 2-1 3-1 2-6 7-8 10l-4 5c-3 4-5 10-9 13-3 3-4 6-6 9-1 2-3 3-4 5-2 2-3 3-5 6-1 2-2 3-3 5s-3 4-5 6c-3 4-6 9-9 13l-12 17-9 13-3 4c-3 4-20 27-20 29l-2 1 54-78 63-88c19-27 40-54 53-85 1-4 3-8 4-12 2-3 3-8 4-11l6-24z" class="d"></path><path d="M535 246c14 1 25 6 37 12 0 1 2 2 3 2h1c2 2 5 4 8 5l1 1c1 0 2 1 2 1 2 1 3 3 4 4h1c1 1 0 0 2 1 1 0 2 1 3 1l1 1c-1 1-2 0-3 1-1 0-2 1-3 1 0 1-1 1-1 1-1-1-1-2-1-3s0-1-1-1h0l-1-1c0 1-1 1-2 2l-1-1v-1c0-1-1-1-2-2h0-1c-1 0-1 0-1-1h2l-1-1c-2 0-3 0-4 1h-1l1-1h1v-2h-2c-2-1-3-1-4-2h-1-1-2-1l1-1-5-3-2 1v-1c-2 0-3 0-4-1v-1l-2 1h-1c-1-1-2-1-4-2h-1c-1 0-2 0-3-1l-1 1-1-1h-3l-1 1c0 1-1 1-1 1-1 1-2 2-2 3-1 1-2 1-2 3v1l-2 5-4 4h0v2h-1v2l-1 3h-2l-2 1v2 1 3h-1v-1h-2c0-1 0-1 1-1-1-1-1-1-1-2s0-1 1-2l-1-2 5-8c1-4 3-8 4-11-1-3-2-4-2-6 1-1 2-1 3-2-1-2-1-2-1-4 2 0 2-1 3-2 1 0 1 0 2-1h0z" class="I"></path><path d="M530 261l2-5c0-1 1-2 1-3s-1-1 0-2l1-2 2-1h0c1 0 2 1 3 1s3 1 5 1 4 1 6 2c3 0 5 1 7 2l14 6v2h1 1c1 1 1 1 2 1l1 1c1 0 1 0 2 1h1c2 1 4 3 5 3h1l4 3-1 1c0 1-1 1-2 2l-1-1v-1c0-1-1-1-2-2h0-1c-1 0-1 0-1-1h2l-1-1c-2 0-3 0-4 1h-1l1-1h1v-2h-2c-2-1-3-1-4-2h-1-1-2-1l1-1-5-3h0c-1-1-1 0-2-1-2-2-5-4-8-4-2 0-5-3-6-3s-1 1-2 0c-2-1-4-1-6-1h-1c-1-1-2 0-4 0-1 2-1 4-1 5-1 2-2 3-3 5v3c-1 0 0 1 0 1-1 1-1 0-2 1 0 2-2 4-3 6 1-4 3-8 4-11z" class="T"></path><path d="M526 272c1-2 3-4 3-6 1-1 1 0 2-1 0 0-1-1 0-1v-3c1-2 2-3 3-5 0-1 0-3 1-5 2 0 3-1 4 0h1c2 0 4 0 6 1 1 1 1 0 2 0s4 3 6 3c3 0 6 2 8 4 1 1 1 0 2 1h0l-2 1v-1c-2 0-3 0-4-1v-1l-2 1h-1c-1-1-2-1-4-2h-1c-1 0-2 0-3-1l-1 1-1-1h-3l-1 1c0 1-1 1-1 1-1 1-2 2-2 3-1 1-2 1-2 3v1l-2 5-4 4h0v2h-1v2l-1 3h-2l-2 1v2 1 3h-1v-1h-2c0-1 0-1 1-1-1-1-1-1-1-2s0-1 1-2l-1-2 5-8z" class="V"></path><path d="M533 263v3l3-1-2 5-4 4h0v2h-1v2l-1 3h-2l-1-1c1-1 1-2 1-2v-1c2-2 2-4 3-6 0-1 1-1 1-2s0-2 1-3l2-3z" class="n"></path><path d="M533 266l3-1-2 5-4 4 1-4c0-2 1-3 2-4z" class="Z"></path><path d="M533 263c1-1 1-1 1-2 0-2 2-2 3-3v-4c1-1 2-1 4-1h2 0c2 1 2 1 4 1h0c1 0 2 1 3 2h3l3 3h-1c-1-1-2-1-4-2h-1c-1 0-2 0-3-1l-1 1-1-1h-3l-1 1c0 1-1 1-1 1-1 1-2 2-2 3-1 1-2 1-2 3v1l-3 1v-3z" class="Q"></path><path d="M651 468h0v-1-1-4h0c0-1 0-1-1-2l1-1c1-2 2-3 2-4-1-1-2-1-3-1-1 1 0 1 0 3-1 0-1 0-1 1h-1l1-1v-4c3-1 2 0 5 0 0 1 0 1 1 1v1h0-1 0v1c3 0 3 1 4 2v-1l2-2 1 2h2c-1 1-1 1-1 2 1 0 2 0 2 1h1c1 0 1 0 2-1 2-1 3-1 4-2s3 0 4 0c0 3-2 6-2 10h1 0 2v2c-1 0-1-1-2-1-3 1-4 8-6 10-1 2-3 3-4 5v1c1 1 1 0 2 1l-2 2h0l-8 5-1 2v-1-1l-1-1h1 0c0-1-1-1-1 0h-1l-2-2h-3c-1-1-1-1-2-1v-1h1v-1-1c0-1 0-2 1-3v-2-1c0-1 1-2 2-2l1-1s0-1-1-2h0c2-1 2-1 3-2 0-2-1-3-2-4z" class="C"></path><path d="M720 373c1 1 1 2 1 4 2 2 1 42 1 49 1 2 1 7 1 10 0 2-1 4 0 6 0-1 0-6 1-7 0-1 0-2 1-4v-16h1c1 2 0 10 0 13h2 1v2h-1c-1 1-1 0 0 1l-1 1-1 23c1 1 1 3 1 5v1c0 1 1 2 1 3v3h1v3c-2 3-2 3-2 6v3l-1 1-1-1-1 1v7c0 3 0 6-1 8v2h1v1l-2 2c0 3 0 6-1 10 0 1 1 3 0 5 0 2 0 8-1 10 0 2 1 4 0 6l-1 1v1l-1 9c-2 6-1 15-5 20-1-1-1-2-1-3l1-5c2-8 2-18 4-26 1-2 1-4 1-6 0-4 0-9 1-13s0-8 0-12l1-1-1-2h0c1-2 1-3 1-5v-11-44c0-8-1-16 0-23v-38z" class="H"></path><path d="M722 478c1 3 0 7 1 10l1-1c0 3 0 6-1 8-1-2-1-4-1-6v-11z" class="E"></path><path d="M722 426c1 2 1 7 1 10 0 2-1 4 0 6 0-1 0-6 1-7 0-1 0-2 1-4 0 2 0 4-1 6v9 16 1 3c0 2 0 3-1 4 0 1 0 2-1 3v1-14-11-14c0-1-1-2-1-3 0-2 0-4 1-6z" class="B"></path><path d="M725 415h1c1 2 0 10 0 13h2 1v2h-1c-1 1-1 0 0 1l-1 1-1 23v5l-1 2h-1v-16-9c1-2 1-4 1-6v-16z" class="D"></path><path d="M726 455c1 1 1 3 1 5v1c0 1 1 2 1 3v3h1v3c-2 3-2 3-2 6v3l-1 1-1-1-1 1v7l-1 1c-1-3 0-7-1-10v-4-1c1-1 1-2 1-3 1-1 1-2 1-4v-3-1h1l1-2v-5z" class="J"></path><path d="M726 455c1 1 1 3 1 5v1c0 1 1 2 1 3v3h1v3c-2 3-2 3-2 6v3l-1 1-1-1c1-5 1-11 0-17l1-2v-5z" class="U"></path><path d="M726 455c1 1 1 3 1 5v1c0 1 1 2 1 3v3h1v3h-3v-10-5z" class="F"></path><path d="M720 411c1 4 0 7 0 11v17 50l-1 27c0 1 1 3 0 5v12l-1 9c-2 6-1 15-5 20-1-1-1-2-1-3l1-5c2-8 2-18 4-26 1-2 1-4 1-6 0-4 0-9 1-13s0-8 0-12l1-1-1-2h0c1-2 1-3 1-5v-11-44c0-8-1-16 0-23z" class="M"></path><path d="M255 252l-1-1 1-1c3-3 4-3 7-3h1 1l4-2 1 1v2s1 1 1 2 0 2-1 2l-1 1c0 1 0 1-1 1l-2 4-1 2v4h-1l1 2-1 2c-2 1-2 1-3 2l-1 1h1c1 3 2 4 2 7l-1-1c-1 2-1 3-1 4l1 2-1 1v-1c-1 1-2 2-3 4l1 1h0c-4-1-8-2-10-5l-3-2-1-2c-1-1-2-3-3-4-1-2-1-4-1-5s1-1 2-2v-2c0-2 1-4 2-6 0 0 1-1 1-2 1-2 1-3 4-4 0-1 0-2 1-3l1 3 2 1c1-1 1-1 1-2l1-1z" class="T"></path><path d="M245 262l1-3c1 0 2-1 3-2h1 1l1 1h2 0l-1 2h-3l-1-1c-2 0-2 1-4 3z" class="O"></path><path d="M265 258c-1-1-1-2-1-3 0-2 0-2 1-4h2c1 1 1 1 2 1l-1 1c0 1 0 1-1 1l-2 4z" class="F"></path><path d="M255 252l-1-1 1-1c3-3 4-3 7-3h1 1l4-2 1 1v2s1 1 1 2 0 2-1 2-1 0-2-1h2s0-1 1-1c-1-1-2-1-2-2l-1 1h-5l-1-1h-1c-1 1-1 2-3 2v2h-2z" class="L"></path><path d="M245 262c2-2 2-3 4-3l1 1h3c0 1-1 2-2 3h0v1l-1-1-2 1h-1c-1 2 0 4-1 7 0 1 0 1 1 1h2v1l-1 1h-1l-1-1-2 1 1 2v1c-1 0-1 1-1 2-1-1-2-3-3-4l1-1c2-2 0-4 1-6 0-2 1-3 2-4v-2z" class="M"></path><path d="M254 258c3-1 5-2 8-1 1 1 2 2 2 3v4h-1l1 2-1 2c-2 1-2 1-3 2 0-1 0-1-1-1-1-1 0-1 0-2l-1-3c-1 1-1 1-1 2h0l-1-2v1h-2c-1 0-1 1-1 2l1 1v1c-1 1-2 2-3 2h-1c-1 0-1 1-1 2v-1h-2c-1 0-1 0-1-1 1-3 0-5 1-7h1l2-1 1 1v-1h0c1-1 2-2 2-3l1-2z" class="b"></path><path d="M256 265c-1-1-2-2-3-2 1-2 2-3 3-3 2-1 4-1 6 0 1 1 1 2 2 4h-1v-1l-2-2c-1 1-2 1-2 2-1 0-1 1-1 1-1 1-1 1-1 2h0l-1-2v1z" class="a"></path><path d="M256 264c-1-1-1-1-1-2h3 0c1-2 1-2 2-2 2 1 2 2 3 3l-2-2c-1 1-2 1-2 2-1 0-1 1-1 1-1 1-1 1-1 2h0l-1-2z" class="W"></path><path d="M258 264s0-1 1-1c0-1 1-1 2-2l2 2v1l1 2-1 2c-2 1-2 1-3 2 0-1 0-1-1-1-1-1 0-1 0-2l-1-3z" class="n"></path><path d="M259 267c1-1 2-1 4-1h1l-1 2c-2 1-2 1-3 2 0-1 0-1-1-1-1-1 0-1 0-2z" class="Q"></path><defs><linearGradient id="Aj" x1="248.678" y1="273.482" x2="260.853" y2="278.531" xlink:href="#B"><stop offset="0" stop-color="#828081"></stop><stop offset="1" stop-color="#afadac"></stop></linearGradient></defs><path fill="url(#Aj)" d="M256 264l1 2h0c0-1 0-1 1-2l1 3c0 1-1 1 0 2 1 0 1 0 1 1l-1 1h1c1 3 2 4 2 7l-1-1c-1 2-1 3-1 4l1 2-1 1v-1c-1 1-2 2-3 4l1 1h0c-4-1-8-2-10-5l-3-2-1-2c0-1 0-2 1-2v-1l-1-2 2-1 1 1h1l1-1c0-1 0-2 1-2h1c1 0 2-1 3-2v-1l-1-1c0-1 0-2 1-2h2v-1z"></path><path d="M254 271h1l1 1c-1 1-1 3-2 4h-1c-1-2 0-3 1-5z" class="a"></path><path d="M249 273l1 1c0 2-1 5-2 8v1l-3-2-1-2c0-1 0-2 1-2v-1l-1-2 2-1 1 1h1l1-1z" class="b"></path><path d="M536 264c0-2 1-2 2-3 0-1 1-2 2-3 0 0 1 0 1-1l1-1h3l1 1 1-1c1 1 2 1 3 1h1c2 1 3 1 4 2h1 1c0 1 4 2 4 3 1 2 0 2 2 3-1 2-1 1-1 2-1 2-1 2-3 2h2c0 1-1 2-2 2v1 1l-2 3c-1 2-2 3-1 6l-5 2c-1 1-4 2-4 3h-3 0c-2 0-2 0-3-1v-3l-2 1h0l-2 2-3 3-1-1 1-1-1-1c-1 1-2 1-3 2 0-1 0-1-1-1l-1 1c-1-1-1-1-2-1v-1c-1 0-1 0-2-1v-1-2l2-1h2l1-3v-2h1v-2h0l4-4 2-5v-1z" class="f"></path><path d="M541 259c1-1 2-1 3-1l1 1c-1 2-1 2-1 3h1l1 2h-1-1c-1 0-1 0-2-1 0-2 1-2 2-4h-3zm-11 15l4-4c0 1 0 1 1 2v1c1 0 1 1 1 1 2 0 2 0 3-1l1 1c-1 1-2 2-3 2-1 1-2 1-3 1l-1 1h-2l-2 2v-2-2h1v-2h0z" class="e"></path><g class="Z"><path d="M536 264c0-2 1-2 2-3 0-1 1-2 2-3 0 0 1 0 1-1l1-1h3l1 1 1-1c1 1 2 1 3 1h1c2 1 3 1 4 2h1 1c0 1 4 2 4 3 1 2 0 2 2 3-1 2-1 1-1 2-1 2-1 2-3 2l-1 1c1 0 1 0 1 1-1 1-2 1-3 1v-1h-1l1-2h0c-1-3-1-1-2-2-1 0-1-1-1-1-1 0-1 1-3 0 2-1 2-3 4-4h0v-1l-1 1c-2-1-2 0-4-1v-1-2h0c-2-1-3-1-4-1v1l2 1c0-1 0-1 1-1v1h-2-1l-1-1c-1 0-2 0-3 1l-1 2-1-1c0 1-2 3-3 4z"></path><path d="M537 276c0 1 0 1 1 1v1l1 1v1c1-1 1-1 3-1 0-1 1-1 2-2v1l1 1h2 1c1 0 2-1 3-2 0-1 1-2 3-2h1l1-2c1 0 2 0 3-1v1l-2 3c-1 2-2 3-1 6l-5 2c-1 1-4 2-4 3h-3 0c-2 0-2 0-3-1v-3l-2 1h0l-2 2-3 3-1-1 1-1-1-1c-1 1-2 1-3 2 0-1 0-1-1-1l-1 1c-1-1-1-1-2-1v-1c-1 0-1 0-2-1v-1-2l2-1h2l1-3v2l2-2h2l1-1c1 0 2 0 3-1z"></path></g><path d="M533 280l2-2 2 2-1 1c-1 0-1 0-1 1v1h1v1h1l1-1 1 1h0l-2 2-3 3-1-1 1-1-1-1c-1 1-2 1-3 2 1-2 2-3 4-4v-2c-1-1-1-1-1-2zm11 7c1-4 7-4 9-8l1-3h3c-1 2-2 3-1 6l-5 2c-1 1-4 2-4 3h-3z" class="j"></path><path d="M529 283l1-1c1-1 2-1 3-2 0 1 0 1 1 2v2c-2 1-3 2-4 4 0-1 0-1-1-1l-1 1c-1-1-1-1-2-1v-1c-1 0-1 0-2-1v-1-2l2-1h2v2h1z" class="n"></path><path d="M526 281h2v2h1l-1 1h-3-1v-2l2-1z" class="Q"></path><path d="M526 286h2 1l3-3h1l1 1c-2 1-3 2-4 4 0-1 0-1-1-1l-1 1c-1-1-1-1-2-1v-1z" class="a"></path><defs><linearGradient id="Ak" x1="789.343" y1="379.903" x2="748.804" y2="414.407" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#d3d2d1"></stop></linearGradient></defs><path fill="url(#Ak)" d="M783 371v1 2c2 0 2 0 2 1v1c0 1-1 1-1 2 2 1 3 2 6 1v3l-1 2v3h-1c0 1-1 1-2 2v1c0 1-1 2-1 3-1 1-1 3-1 5l-1 1v1c-4 4-9 7-14 10-4 3-8 5-11 9v-1l-1-3c0-2-3-4-5-5-5-2-10 0-15 2-2 1-4 2-5 5h-1c1-2 1-3 2-4 4-5 9-7 15-10 10-6 22-12 30-22 2-3 4-6 5-10z"></path><path d="M785 381h1l1 1c-1 0-1 0-2-1z" class="n"></path><defs><linearGradient id="Al" x1="251.195" y1="235.783" x2="289.165" y2="245.323" xlink:href="#B"><stop offset="0" stop-color="#38383a"></stop><stop offset="1" stop-color="#59585b"></stop></linearGradient></defs><path fill="url(#Al)" d="M272 214h0c2-2 4-2 6-1 1 2 2 3 3 5 0 2 1 2 2 3-1 2 0 2-1 2-2 1-2 2-3 3 0 1 0 1 1 1 1 1 1 1 1 2v2-1l1-1h1v1h1l1 1v2h2c1 0 2 1 3 1l1 1h-1-1l1 1v1h0v2c1 1 1 2 1 3v2h-2l1 1-1 1 1 1h-1c-1-1-2-1-3-2h-1l-1-1c-1 0-1 1-2 1 1 2 1 2 1 4l-2-2v-1c-1-1-3-2-4-2h-5c-1 1-1 1-1 2-2 0-1 2-1 3v1c0-1-1-2-1-2v-2l-1-1-4 2h-1-1c-3 0-4 0-7 3l-1 1 1 1-1 1c0 1 0 1-1 2l-2-1-1-3h1l-1-2h-1-1l2-2c1-2 1-4 3-6l1-1c1-2 4-5 5-7 0-2 0-3 1-4s1 0 1-1c2-2 3-4 5-6l2-3c1-2 2-3 4-4v-1z"></path><path d="M287 239h2v1l-1 2h-1c-1-1-1-1-1-2l1-1z" class="O"></path><path d="M266 222l2-3v2l2 2 2-1v1h2l2 1-1 1c-1-1-2 0-4-1v1c-1 2-3 4-5 4l-2-1c0-2 1-4 2-6z" class="T"></path><path d="M272 244c-2-1-3-2-5-3l1-2c2-1 3-2 5-2h1c-1 0-2 1-3 1l1 1c1-1 1-1 3-1l1 1h1 1c1 0 1 1 2 1v5l1-1h0c1-1 2-1 2 0 0 0-1 0-1 1 1 2 1 2 1 4l-2-2v-1c-1-1-3-2-4-2h-5z" class="c"></path><path d="M272 239c1-1 1-1 3-1l1 1h0l-2 2s0 1-1 1c-1-1-1-2-1-3z" class="T"></path><path d="M272 214h0c2-2 4-2 6-1 1 2 2 3 3 5 0 2 1 2 2 3-1 2 0 2-1 2-2 1-2 2-3 3l-2-2h-1l-2-1h-2v-1l-2 1-2-2v-2c1-2 2-3 4-4v-1z" class="M"></path><path d="M272 214h0c2-2 4-2 6-1 1 2 2 3 3 5 0 2 1 2 2 3-1 2 0 2-1 2-2 1-2 2-3 3l-2-2h0c1-1 3-2 3-3s0-2-1-2v-2c-1-1-1-2-3-2l-1-1h-3v1-1z" class="c"></path><path d="M276 215c1 1 2 2 2 3-1 2-1 3-3 4l-1-1h-3l-1-1 1-2c1-2 3-3 5-3z" class="Y"></path><path d="M274 217h2v1l-1 2c-1 0-1-1-2-1l1-2z" class="V"></path><defs><linearGradient id="Am" x1="403.586" y1="262.37" x2="429.85" y2="285.588" xlink:href="#B"><stop offset="0" stop-color="#050607"></stop><stop offset="1" stop-color="#373637"></stop></linearGradient></defs><path fill="url(#Am)" d="M471 245v1c-1 1-2 1-3 2 1 0 1 0 2-1h3c1 0 2-1 4-1 1 0 2-1 3-1-2 1-5 2-7 3h1c3 0 6 0 10-1 0-1 0-1 1-1v1l3 3c1 3 1 6 2 9 0 2 1 4 2 6-1-1-4-5-5-7 0-4-2-6-5-8v-1c-2 1-5 1-7 2-1 1-2 0-4 1-2 0-3 2-5 2-5 1-9 3-13 5-2 1-3 3-5 4-1 1-2 0-3 1h-2v2c-3 0-4 1-6 2h0c1 2 1 3 2 5l1 3c-2 0-4-1-7 0 2 1 5 1 7 2-1 1-2 0-4 0h-3l-1 1c-1 0-3-1-3-1l-1 1h-1-14c-1 2-2 2-5 2-2 1-5 0-7 0l-15 1c-1-1 0-1-1-2 3 0 6-2 8-2l25-11c5-2 11-3 15-6l4-1c2-2 5-2 8-3l26-12z"></path><path d="M424 275c0 1-3 0-4 0v-1c3-2 4-2 7-2v1l-3 1v1z" class="C"></path><path d="M427 273c1 0 3-1 3-1 2-2 2-2 4-2l-1 2h0c-1 0-3 1-3 2-1 0-1 1-1 2l-5-1v-1l3-1z" class="F"></path><defs><linearGradient id="An" x1="430.117" y1="274.37" x2="437.847" y2="271.931" xlink:href="#B"><stop offset="0" stop-color="#4a4a4d"></stop><stop offset="1" stop-color="#666567"></stop></linearGradient></defs><path fill="url(#An)" d="M434 270c1-1 1 0 2-1l1-1c1 2 1 3 2 5l1 3c-2 0-4-1-7 0h-4c0-1 0-2 1-2 0-1 2-2 3-2h0l1-2z"></path><path d="M471 245v1c-1 1-2 1-3 2 1 0 1 0 2-1h3c1 0 2-1 4-1 1 0 2-1 3-1-2 1-5 2-7 3-3 2-7 3-10 4l-14 6-4 1c-3 2-6 2-8 4-1 0-2 0-3 1l-11 4h0l1-1h1l1-1c1 0 2 0 3-1 1 0 1 0 2-1 1 0 1 0 2-1h2l1-1 1-1v-1h0c2-2 5-2 8-3l26-12z" class="E"></path><path d="M473 248h1c3 0 6 0 10-1 0-1 0-1 1-1v1l3 3c1 3 1 6 2 9 0 2 1 4 2 6-1-1-4-5-5-7 0-4-2-6-5-8v-1c-2 1-5 1-7 2-1 1-2 0-4 1-2 0-3 2-5 2-5 1-9 3-13 5-2 1-3 3-5 4-1 1-2 0-3 1h-2v2c-3 0-4 1-6 2h0l-1 1c-1 1-1 0-2 1-2 0-2 0-4 2 0 0-2 1-3 1v-1h0l-1-1c1 0 2 0 2-1h1c1-1 2-1 3-1h0v-1l1-2h1v2h0l2-2c1 0 0 1 1-1h-2c5-3 10-4 14-7l14-6c3-1 7-2 10-4z" class="K"></path><path d="M709 571v4 4c0 1-1 2-2 2v3c0 1-1 1-1 2-1 2-1 3-2 6 0 1-1 2-1 3l1 1c-1 1-1 2-1 4l-1 1-1 1v2 1c-1 2-2 5-3 6v1l-1 1-1 3h0l1-1v2 1l1-2c1 2 1 3 1 5l1 1c0-1 0-1 1-1h0c1 1 1 1 2 1v2c-2 1-2 3-3 5l-2 3c-2 4-5 8-7 13l-2 2-3 4v1c-1 1-1 3-3 4-1 1-1 2-2 3l-3-2c-3 0-4 1-6 2h0l2-3v-3l-3 3c0 1-1 1-1 2l-1 2-3 1-1 2c-2 2-3 4-5 6 0 1-1 2-2 2h-1c3-6 8-10 10-16 1-2 2-3 3-4 2-3 3-5 4-7s3-3 3-5c2-5 6-9 8-13l9-17c6-12 10-25 15-38z" class="M"></path><path d="M686 631l1 1-2 2c0 1-1 2-1 2 0 2-1 3-2 5 1-1 1 0 2-1 0-1 0-1 1-1h0l-2 5c-2 3-5 6-6 9l-3 3v-3l-3 3c0 1-1 1-1 2l-1 2-3 1 5-7c2-2 4-4 4-6 1-2 2-3 3-4l1-1v-1c0-2 2-3 3-4s2-3 3-4c0-2 0-3 1-3z" class="G"></path><path d="M686 631c3-4 5-8 7-12l1-1v-2c1-3 4-6 4-8 1-2 1-3 2-4h0c0-1 0-1 1-2h0v2 1c-1 2-2 5-3 6v1l-1 1-1 3h0l1-1v2 1l1-2c1 2 1 3 1 5-1 1-1 2-2 3 0 1-1 3-2 3h0c-1 0-1-1-2-1l-8 13h0c-1 0-1 0-1 1-1 1-1 0-2 1 1-2 2-3 2-5 0 0 1-1 1-2l2-2-1-1z" class="P"></path><g class="L"><path d="M698 616c1 2 1 3 1 5-1 1-1 2-2 3 0 1-1 3-2 3h0c-1 0-1-1-2-1l4-8 1-2z"></path><path d="M695 627c1 0 2-2 2-3 1-1 1-2 2-3l1 1c0-1 0-1 1-1h0c1 1 1 1 2 1v2c-2 1-2 3-3 5l-2 3c-2 4-5 8-7 13l-2 2-3 4v1c-1 1-1 3-3 4-1 1-1 2-2 3l-3-2c-3 0-4 1-6 2h0l2-3 3-3c1-3 4-6 6-9l2-5 8-13c1 0 1 1 2 1h0z"></path></g><path d="M678 657c0-2 2-3 3-5 0-2 2-2 3-3v1h1c0 1-1 2-1 3-1 1-1 2-1 3-1 1-1 2-2 3l-3-2z" class="O"></path><path d="M695 627c1 0 2-2 2-3 1-1 1-2 2-3l1 1c0-1 0-1 1-1h0c1 1 1 1 2 1v2c-2 1-2 3-3 5l-2-1-1 1v1c-1 0-2 2-2 3-1 1-3 2-4 4s-2 4-5 5c2-2 4-4 5-6 0-2 1-3 2-5l2-4z" class="U"></path><path d="M693 626c1 0 1 1 2 1h0l-2 4c-1 2-2 3-2 5-1 2-3 4-5 6l-2 2c-1 3-4 8-7 9 1-3 4-6 6-9l2-5 8-13z" class="O"></path><defs><linearGradient id="Ao" x1="772.212" y1="275.563" x2="750.247" y2="277.824" xlink:href="#B"><stop offset="0" stop-color="#969494"></stop><stop offset="1" stop-color="#dfdfdf"></stop></linearGradient></defs><path fill="url(#Ao)" d="M757 255l3-2h0v-3l2-1v-1l1 1h3v-1l4 2 3 3c2 2 4 4 5 6 1 3 1 4 4 5v4l-1 2v3l2-4v2l-1 1v3 1l-3 4c-5 5-11 9-17 12l-6 3c-9 3-15 6-22 12l-6 6 26-34c2-4 3-7 4-10v-2-5l-1-3v-4z"></path><path d="M758 267s0-1 1-1c1-1 2-1 3-1 0 1 0 2 1 3 0 1 0 1 1 2l-1 1v-1c-1-1-2-1-3-1h-2v-2z" class="Z"></path><path d="M758 262c2-2 3-2 5-3h1 5 1c1 1 1 2 2 2 1 1 2 1 3 2h0c-1 2-1 3-2 5l2 1c0 1 1 1 1 2v1c1 1 1 2 2 3l2-1c-2 3-3 5-6 7-1 1-3 3-5 4l-1-1c0-1 0-2 1-3v-1l1 1c1-1 1-1 1-2l-1-2 3-3c-1-1-3-2-3-3l2-2c-2 0-2 1-3 2-3-3-4-3-4-7v-1c-1 1-2 1-3 2-1 0-2 0-3 1-1 0-1 1-1 1v-5z" class="W"></path><path d="M774 281h0c-1-4 2-5 2-9 1 1 1 2 2 3l2-1c-2 3-3 5-6 7z" class="a"></path><path d="M757 255l3-2h0v-3l2-1v-1l1 1h3v-1l4 2 3 3c2 2 4 4 5 6 1 3 1 4 4 5v4l-1 2v3l-1 1-2 1c-1-1-1-2-2-3v-1c0-1-1-1-1-2l-2-1c1-2 1-3 2-5h0c-1-1-2-1-3-2-1 0-1-1-2-2h-1-5-1c-2 1-3 1-5 3l-1-3v-4z" class="S"></path><path d="M773 253c2 2 4 4 5 6 1 3 1 4 4 5v4h0c-1 0-2 0-3-1 1-1 0-5-1-6l-3-4c0-1 0-1-2-2v-1-1z" class="T"></path><path d="M757 259c2-1 3-3 5-4 1 0 2 1 3 1h2v-1h2v1l1 1c2 0 3 0 4 1s1 1 1 2c2 3 4 6 3 9l1 1h2v3l-1 1-2 1c-1-1-1-2-2-3v-1c0-1-1-1-1-2l-2-1c1-2 1-3 2-5h0c-1-1-2-1-3-2-1 0-1-1-2-2h-1-5-1c-2 1-3 1-5 3l-1-3z" class="b"></path><path d="M775 269l-2-1c1-2 1-3 2-5l2 4c-1 1-1 1-2 1v1z" class="a"></path><defs><linearGradient id="Ap" x1="757.813" y1="269.482" x2="759.187" y2="304.018" xlink:href="#B"><stop offset="0" stop-color="#d3d2d1"></stop><stop offset="1" stop-color="#f8f8f8"></stop></linearGradient></defs><path fill="url(#Ap)" d="M781 273l2-4v2l-1 1v3 1l-3 4c-5 5-11 9-17 12l-6 3c-9 3-15 6-22 12l-6 6 26-34c2 1 2 1 3 4-1 4-7 7-9 10l1-1c7-2 14-2 20-7 2-1 4-3 5-4 3-2 4-4 6-7l1-1z"></path><path d="M427 772h1c1 0 1 1 2 1h2 0l3 2 3 3c0 1 1 2 2 4l4 6 3 5c2 3 4 5 6 7l11 16 4 5c1 4 16 21 15 22 2 2 5 5 5 7-2 0-2-1-3 0h-2c-1-3-3-5-4-7 0 2 0 2 1 4 0 1 1 2 1 3h-1c-5-8-12-15-16-24v3c0 2 0 2-1 4l-3 1h-6c-4-3-4-5-6-10l1-4c-1-2-2-3-3-5-3-5-7-10-10-15-1-2-2-4-4-5-2 0-3 0-5-1s-3-2-5-3c-2-2-3-6-3-9l2-2 3-3c1-1 2-1 3-1l3 1c0-1-2-4-3-5z" class="B"></path><path d="M430 773h2 0l3 2 3 3c0 1 1 2 2 4-2-1-3-2-5-3-1-3-3-4-5-6z" class="U"></path><path d="M458 817c2 0 2 1 3 3 1 0 2 1 2 2 0 2 0 2 1 4v3l-1-1c0-1 0-1-1-2-1 0-1 0-2-1 1-1 1-2 0-2-1-2-1-2-3-3l-1-1v-2h2z" class="E"></path><path d="M455 817c-5-4-7-10-11-16-2-2-6-7-7-10v-2l-1-1v-2h1c2 4 5 8 8 12l13 19h-2-1z" class="U"></path><path d="M435 779c2 1 3 2 5 3l4 6 3 5c2 3 4 5 6 7l11 16h-1c-1-1-2-2-3-2l-4-4v-1l-1-1c-4-8-10-15-15-22-1-1-2-3-3-4s-2-2-2-3z" class="C"></path><path d="M431 780c0-1 0-2 1-3 1 1 1 2 2 3v1l1 1c0 2 1 3 2 4h0-1v2l1 1v2c1 3 5 8 7 10 4 6 6 12 11 16h-3c-5-6-9-12-13-18-1-2-3-4-3-6l-1-1h-1c0-1 1-1 1-2v-6c-1-1-1-1-2-1l-1 1-1-4z" class="P"></path><path d="M434 792h1l1 1c0 2 2 4 3 6 4 6 8 12 13 18l-3 3c-1-2-2-3-3-5-3-5-7-10-10-15-1-2-2-4-4-5l2-3h0z" class="U"></path><path d="M460 814c1 0 2 1 3 2h1l4 5c1 4 16 21 15 22 2 2 5 5 5 7-2 0-2-1-3 0h-2c-1-3-3-5-4-7l-19-29z" class="K"></path><path d="M452 817h3 1v2l1 1c2 1 2 1 3 3 1 0 1 1 0 2 1 1 1 1 2 1 1 1 1 1 1 2l1 1c0 2 0 2-1 4l-3 1h-6c-4-3-4-5-6-10l1-4 3-3z" class="N"></path><path d="M456 819l1 1c-2 1-3 2-4 4l-2 1v-1c0-1 0-2 1-2 1-2 2-2 4-3z" class="B"></path><path d="M453 824l3 1v2h-1l-2 1-2-2v-1l2-1z" class="P"></path><path d="M457 820c2 1 2 1 3 3 1 0 1 1 0 2-2 1-2 1-4 0l-3-1c1-2 2-3 4-4z" class="h"></path><path d="M456 825c2 1 2 1 4 0 1 1 1 1 2 1 1 1 1 1 1 2v1c-1 1-2 2-3 2h0c-2 1-2 1-4 0h0-1c-2-1-2-2-2-3l2-1h1v-2z" class="G"></path><path d="M424 777c1-1 2-1 3-1 0 1 1 2 0 3 2 1 3 1 4 1l1 4 1-1c1 0 1 0 2 1v6c0 1-1 1-1 2h0l-2 3c-2 0-3 0-5-1s-3-2-5-3c-2-2-3-6-3-9l2-2 3-3z" class="B"></path><path d="M427 779c2 1 3 1 4 1l1 4-2 2-4-1c-1-1-1-2-1-2 0-2 1-3 2-4z" class="h"></path><path d="M431 791c-1 1-3 1-4 0-2 0-3-3-4-4l1-1h1c1 1 2 2 4 2 1 0 3-1 4-2v1c1 1-1 3-2 4z" class="AD"></path><path d="M424 777c1-1 2-1 3-1 0 1 1 2 0 3s-2 2-2 4c0 0 0 1 1 2 0 0 0 1-1 1h-1l-1 1c1 1 2 4 4 4 1 1 3 1 4 0l2-1 1 1v1l-2 3c-2 0-3 0-5-1s-3-2-5-3c-2-2-3-6-3-9l2-2 3-3z" class="q"></path><path d="M556 259l2-1v1c1 1 2 1 4 1v1l2-1 5 3-1 1h1 2 1 1c1 1 2 1 4 2h2v2h-1l-1 1h1c1-1 2-1 4-1l1 1h-2c0 1 0 1 1 1h1 0c1 1 2 1 2 2v1l1 1c1-1 2-1 2-2l1 1h0c1 0 1 0 1 1s0 2 1 3c0 0 1 0 1-1h5c3-1 6 0 9-1l5 4c0 1 1 2 1 2 3 1 8 0 11 0h2l53 1-17 6-1-1c-2 0-4 1-6 1h-8-45c-9 0-19 1-28 0-3-1-5-1-8-2-2-1-5-1-7-2l-1-1-1 1h-4-1l5-2c-1-3 0-4 1-6l2-3v-1-1c1 0 2-1 2-2h-2c2 0 2 0 3-2 0-1 0 0 1-2-2-1-1-1-2-3 0-1-4-2-4-3h-1z" class="k"></path><path d="M592 276h5v1c-1 0-1 0-2 1s-1 2-3 2h-3v1l-27 1c3-2 6-3 9-3s6-1 9-1 8 1 11-1c0 0 1 0 1-1z" class="E"></path><path d="M589 280c1-1 1-1 1-2h5c-1 1-1 2-3 2h-3z" class="I"></path><path d="M606 275l5 4c0 1 1 2 1 2 3 1 8 0 11 0h2c-4 1-9 0-14 0h-22v-1h3c2 0 2-1 3-2s1-1 2-1v-1c3-1 6 0 9-1z" class="J"></path><path d="M597 277c2 0 4-1 6 0v1h-5v1c2 0 3-1 5 0v2h8-22v-1h3c2 0 2-1 3-2s1-1 2-1z" class="G"></path><path d="M578 269c1-1 2-1 4-1l1 1h-2c0 1 0 1 1 1h1 0c1 1 2 1 2 2v1l1 1c1-1 2-1 2-2l1 1h0c1 0 1 0 1 1s0 2 1 3c-3 2-8 1-11 1s-6 1-9 1c-2-2-9 1-13 2v-1c1 0 1-1 2-1 1-1 3-1 4-1 0-1 1-2 1-2 1-1 3-1 4-1v-1l1 1 1-1v-1c1 0 2-1 2-1l-1-1 1-1h1 1l2-1h1z" class="R"></path><path d="M577 276v-1l1-2h1l1 1h3c1 1 1 1 2 1v1h-5-3z" class="S"></path><path d="M578 269c1-1 2-1 4-1l1 1h-2c0 1 0 1 1 1h1 0c1 1 2 1 2 2v1l1 1h-3-3l-1-1h-1l-1 2v1l-13 2c0-1 1-2 1-2 1-1 3-1 4-1v-1l1 1 1-1v-1c1 0 2-1 2-1l-1-1 1-1h1 1l2-1h1z" class="X"></path><path d="M582 270h1 0c1 1 2 1 2 2v1l1 1h-3v-1h-3v-1l2-2h0z" class="S"></path><path d="M577 269h1c0 1-1 1-1 2s2 1 0 2-3 1-4 1h-1-1v-1c1 0 2-1 2-1l-1-1 1-1h1 1l2-1z" class="V"></path><path d="M556 259l2-1v1c1 1 2 1 4 1v1l2-1 5 3-1 1h1 2 1 1c1 1 2 1 4 2h2v2h-1l-1 1-2 1h-1-1l-1 1 1 1s-1 1-2 1v1l-1 1-1-1v1c-1 0-3 0-4 1 0 0-1 1-1 2-1 0-3 0-4 1-1 0-1 1-2 1v1l-2 1c-1-3 0-4 1-6l2-3v-1-1c1 0 2-1 2-2h-2c2 0 2 0 3-2 0-1 0 0 1-2-2-1-1-1-2-3 0-1-4-2-4-3h-1z" class="g"></path><path d="M572 271h-3l2-2c-1 0-2 0-3-1h0l2-1v1c1 0 2 0 3-1h0c1 1 1 1 2 0 0 1-1 2-1 3h-1l-1 1z" class="W"></path><path d="M573 264c1 1 2 1 4 2h2v2h-1l-1 1-2 1h-1c0-1 1-2 1-3-1 1-1 1-2 0h0c-1 1-2 1-3 1v-1c0-1 1-1 2-2h1v-1z" class="Y"></path><path d="M556 259l2-1v1c1 1 2 1 4 1v1c1 1 2 1 3 2 0 2 0 2 1 4v2l-1 1-1-1-2 2 1 2h-1-3v-1-1c1 0 2-1 2-2h-2c2 0 2 0 3-2 0-1 0 0 1-2-2-1-1-1-2-3 0-1-4-2-4-3h-1z" class="j"></path><path d="M563 273c1 0 2 0 3-1h1l1-1v2h3v1l-1 1-1-1v1c-1 0-3 0-4 1 0 0-1 1-1 2-1 0-3 0-4 1-1 0-1 1-2 1v1l-2 1c-1-3 0-4 1-6l2-3h3 1z" class="W"></path><path d="M559 273h3c-1 1-2 2-2 4-1 0-2 1-3 1l1 2v1l-2 1c-1-3 0-4 1-6l2-3z" class="g"></path><path d="M518 252l5-3h0l-1 1c-5 5-8 11-9 18v6c1 6 0 12 0 18v-2c1 1 2 1 2 2l2-3c0-2 1-3 2-4 0-1 1-2 1-2 0-1 1-2 1-3l1 2c-1 1-1 1-1 2s0 1 1 2c-1 0-1 0-1 1h2v1h1v-3c1 1 1 1 2 1v1c1 0 1 0 2 1l1-1c1 0 1 0 1 1 1-1 2-1 3-2l1 1-1 1 1 1v1c1 1 1 2 0 4h0c0 3-1 6-3 8l-1 1v-1l1-1c0-1 0-1-1-2-1 2-1 3-3 4l-4 8 1 1h0v3c-1 0-1 1-1 2 1 1 1 2 1 4l-1-1c0 1 0 1 1 2l1 1c0 1 0 1-1 1-3 2-4 7-7 8v-2h0-2l1 1c-2 1-3 1-5 2-2 0-3 0-5-1h0l-4-3v-1l-2-2h0l-2-2h0-1c0-1 0-1-1-2l-1 1h-1v-1h-1-1v-2c-1-2-1-2-2-3l3-1c2-1 2-2 3-3l2-2-2-2h0c0-2 0-4 1-6l1 2 1-1h2l1-1c1-2 2-2 2-5v-2-6-5l2 2v1c1 1 1 1 1 2v2-9c2-3 1-5 2-8 0-2 0-5 1-7v-3-5-1c2 0 2-1 3-3v-1h1l1 1h0l1-1c1-1 1-2 2-3z" class="h"></path><path d="M508 297v1c1 3 0 5 1 7 0 3 1 5 0 7 0 2 0 3 1 4 0-1 0-2 1-3h1c0-2 0-5 1-7v5 8h-1c1 2 1 1 2 3-1 0-2 0-2 1-1 0-1-1-1-2-1-1-2-1-3-2 0-1 0-1-1-3v-6-5l1-8z" class="i"></path><path d="M507 313v-3 6c1 2 1 2 1 3 1 1 2 1 3 2 0 1 0 2 1 2 0-1 1-1 2-1-1-2-1-1-2-3h1l3 1 2 2h0v3c0 1 0 2-1 3l-3-1h-1l-6-2c-1-1-2-1-3-2 0-1 0-2 1-3 1-2 2-4 2-7z" class="k"></path><path d="M508 319c1 1 2 1 3 2 0 1 0 2 1 2v1l-1 1-2-1c0-1 0-2-1-2l-1-1h0l1-2z" class="h"></path><path d="M516 320l2 2h0v3c0 1 0 2-1 3l-3-1v-4c1-1 1-2 2-3z" class="C"></path><path d="M516 320l2 2h0v3l-4-2c1-1 1-2 2-3z" class="J"></path><path d="M498 324c1-1 1-1 3-1l1 1 2-1c1 1 2 1 3 2l6 2h1l3 1c-1 0-2 0-2 2h0l1 1c-2 1-3 1-5 2-2 0-3 0-5-1h0l-4-3v-1l-2-2h0l-2-2z" class="E"></path><path d="M514 327l3 1c-1 0-2 0-2 2h0l1 1c-2 1-3 1-5 2l1-2 1 1-1-2 1-3h1z" class="H"></path><path d="M498 324c1-1 1-1 3-1l1 1 2-1c1 1 2 1 3 2 0 1 0 2-1 3l1 1-1 1-2-2h-2l-2-2h0l-2-2z" class="N"></path><path d="M513 274c1 6 0 12 0 18v-2c1 1 2 1 2 2l1 3v1c0 1 0 2-1 4l1 1c1 0 2 1 2 2v2c2 2 5 4 5 6l1 1h0v3c-1 0-1 1-1 2 1 1 1 2 1 4l-1-1c0 1 0 1 1 2l1 1c0 1 0 1-1 1-3 2-4 7-7 8v-2h0-2 0c0-2 1-2 2-2 1-1 1-2 1-3v-3h0l-2-2-3-1v-8-5-7c0-9-1-17 0-25z" class="H"></path><path d="M517 311h1c1 0 1 0 2-1h1l1 1c0 1-2 1-2 4-1 0-1 1-2 1l-1-5z" class="B"></path><path d="M518 322c2 0 2 0 4 1-1 1-3 3-3 4v1c-1 1-2 1-2 2h-2 0c0-2 1-2 2-2 1-1 1-2 1-3v-3z" class="N"></path><path d="M513 311h1 2l1-1v1l1 5c-1 0-2 1-3 1v1c2 1 2 1 3 3v1l-2-2-3-1v-8zm-9-21v-5l2 2v1c1 1 1 1 1 2v2l1 5-1 8v5 3c0 3-1 5-2 7-1 1-1 2-1 3l-2 1-1-1c-2 0-2 0-3 1h0-1c0-1 0-1-1-2l-1 1h-1v-1h-1-1v-2c-1-2-1-2-2-3l3-1c2-1 2-2 3-3l2-2-2-2h0c0-2 0-4 1-6l1 2 1-1h2l1-1c1-2 2-2 2-5v-2-6z" class="U"></path><path d="M497 303l1 2 1-1h2c-1 2-3 4-3 7l-2-2h0c0-2 0-4 1-6z" class="i"></path><defs><linearGradient id="Aq" x1="500.451" y1="292.043" x2="508.812" y2="298.15" xlink:href="#B"><stop offset="0" stop-color="#171916"></stop><stop offset="1" stop-color="#343234"></stop></linearGradient></defs><path fill="url(#Aq)" d="M504 290v-5l2 2v1c1 1 1 1 1 2v2l1 5-1 8v1l-2 2c-1 1 0 0-1 0-1-2 0-8 0-12v-6z"></path><path d="M501 310l1-1c1 0 2 1 2 2v1c1 0 2 1 3 1 0 3-1 5-2 7-1 1-1 2-1 3l-2 1-1-1c-2 0-2 0-3 1h0-1c0-1 0-1-1-2l-1 1h-1v-1h-1-1v-2c-1-2-1-2-2-3l3-1h1 2l2 1h0v-2l1 2 1-1c0-2 0-3 1-6z" class="I"></path><path d="M501 310l1-1c1 0 2 1 2 2v1c-1 1 0 3-1 4 0 1 0 0-1 1l-1-1c0-2 1-3 0-5v-1z" class="B"></path><path d="M494 316h2l2 1h0v-2l1 2c0 1-1 2-2 3h-3c-1-1-1-1-1-2l1-2z" class="k"></path><path d="M521 280l1 2c-1 1-1 1-1 2s0 1 1 2c-1 0-1 0-1 1h2v1h1v-3c1 1 1 1 2 1v1c1 0 1 0 2 1l1-1c1 0 1 0 1 1 1-1 2-1 3-2l1 1-1 1 1 1v1c1 1 1 2 0 4h0c0 3-1 6-3 8l-1 1v-1l1-1c0-1 0-1-1-2-1 2-1 3-3 4l-4 8c0-2-3-4-5-6v-2c0-1-1-2-2-2l-1-1c1-2 1-3 1-4v-1l-1-3 2-3c0-2 1-3 2-4 0-1 1-2 1-2 0-1 1-2 1-3z" class="L"></path><path d="M524 292l1 1-1 3h-1l-2-2c1-1 2-1 3-2z" class="F"></path><path d="M515 292l2-3c0 1 0 2 1 3h2v-1l1 1v1c-1 1-2 1-2 3h1c0 1-1 1-2 2v-1-2-1l-2 1-1-3z" class="R"></path><path d="M516 295l2-1v1 2 1c1-1 2-1 2-2h1v4c-2 2-2 3-3 5v-2c0-1-1-2-2-2l-1-1c1-2 1-3 1-4v-1z" class="F"></path><path d="M521 280l1 2c-1 1-1 1-1 2s0 1 1 2c-1 0-1 0-1 1h2v1h1v-3c1 1 1 1 2 1v1c1 0 1 0 2 1l1-1c1 0 1 0 1 1 1-1 2-1 3-2l1 1-1 1 1 1v1l-2 3h-1v-2l-1 1-1-1-2 1-1-1c-2 0-1 0-2-1l-1 1c-2 1-2-1-4-1v-1l1-1c0-1-1-2-1-3s1-2 1-2c0-1 1-2 1-3z" class="S"></path><path d="M524 285c1 1 1 1 2 1v1c1 0 1 0 2 1l1-1c1 0 1 0 1 1 1-1 2-1 3-2l1 1-1 1 1 1v1l-2 3h-1v-2h1v-1h0c-2-1-5-1-7-1h-1c-2 0-2 0-3-2h2v1h1v-3z" class="b"></path><path d="M534 290c1 1 1 2 0 4h0c0 3-1 6-3 8l-1 1v-1l1-1c0-1 0-1-1-2-1 2-1 3-3 4l-4 8c0-2-3-4-5-6 1-2 1-3 3-5l10-7h1l2-3z" class="h"></path><path d="M534 290c1 1 1 2 0 4h0c0 3-1 6-3 8l-1 1v-1l1-1c0-1 0-1-1-2-1 2-1 3-3 4l5-10 2-3zm-70 539v-3c4 9 11 16 16 24h1c0-1-1-2-1-3-1-2-1-2-1-4 1 2 3 4 4 7h2c2 2 3 3 5 6 0 1 0 2 1 3 0 1 1 1 1 2v1c1 1 1 2 2 3s3 5 4 5c1 3 3 5 4 8l3 6c0 1 1 1 1 2l1-1c2 1 1 3 4 3l1 1-2 4c0 1 0 1-1 2 0 1-1 3-1 4h0v2c1 0 3 1 5 1l-1 4h2c0 2-1 2-3 3-1 1-1 1-2 0l-1 3c0-2-1-3-2-4-2 1-1 1-3 1v-1l-2-2h-1-1c-2-2-3-3-6-3v1-1l-8-13c-1-1-2-3-4-4v-5c-1 0-3-3-3-4l-1-3-4-4 1-2h2c-1-2-2-4-3-5v-1l1-1-1-1 1-1c-1 0-1 0-1-1 2 1 2 2 4 2-3-4-5-7-7-11-2-1-3-2-4-3-1 1-1 1-2 1l-5-8c-1-1-1-2-2-3s-2-1-3-2h6l3-1c1-2 1-2 1-4z" class="G"></path><path d="M454 834h6l6 12c-1 1-1 1-2 1l-5-8c-1-1-1-2-2-3s-2-1-3-2zm25 9c1 2 3 4 4 7h2c2 2 3 3 5 6 0 1 0 2 1 3 0 1 1 1 1 2v1c1 1 1 2 2 3s3 5 4 5c1 3 3 5 4 8l3 6c0 1 1 1 1 2l1-1c2 1 1 3 4 3l1 1-2 4h-1-1c-1-1-2-3-4-5l-24-38h1c0-1-1-2-1-3-1-2-1-2-1-4z" class="E"></path><path d="M506 886l1-1c2 1 1 3 4 3l1 1-2 4h-1c-1-2-2-5-3-7z" class="D"></path><path d="M483 850h2c2 2 3 3 5 6 0 1 0 2 1 3 0 1 1 1 1 2v1c-4-3-6-8-9-12z" class="p"></path><path d="M466 839c-1-1-1-2-2-2-1-1-1-1-1-2l1-1 1 1h1c1 4 4 4 5 8v1c2 1 3 4 4 6 2 4 5 7 7 12 2 2 4 7 7 9 1 1 2 3 3 4l10 17c2 2 3 5 6 7h0v2l-1 1-1-1c-1 0-1-1-1-2-4-7-8-13-13-20-2-2-3-5-6-8v1c-3-4-6-10-9-12-3-4-5-7-7-11l-5-9 1-1z" class="P"></path><path d="M470 849l-5-9 1-1 20 32v1c-3-4-6-10-9-12-3-4-5-7-7-11z" class="F"></path><path d="M473 860l1-1c-1 0-1 0-1-1 2 1 2 2 4 2 3 2 6 8 9 12v-1c3 3 4 6 6 8 5 7 9 13 13 20 0 1 0 2 1 2l1 1 1-1c1 0 3 1 5 1l-1 4h2c0 2-1 2-3 3-1 1-1 1-2 0l-1 3c0-2-1-3-2-4-2 1-1 1-3 1v-1l-2-2h-1-1c-2-2-3-3-6-3v1-1l-8-13c-1-1-2-3-4-4v-5c-1 0-3-3-3-4l-1-3-4-4 1-2h2c-1-2-2-4-3-5v-1l1-1-1-1z" class="C"></path><path d="M476 868l6 9h-1c-1-1-3-2-4-3l-4-4 1-2h2z" class="L"></path><path d="M477 874c1 1 3 2 4 3h1l3 6c1 4 4 6 5 10l1 1v1c-1-1-3-3-4-5 0-3-4-7-6-9-1 0-3-3-3-4l-1-3z" class="R"></path><path d="M481 881c2 2 6 6 6 9 1 2 3 4 4 5l1 1 3 3s-1 1 0 1l1 1 1-1 2 2c0 1 0 1 1 2l1 2h-1-1c-2-2-3-3-6-3v1-1l-8-13c-1-1-2-3-4-4v-5z" class="I"></path><path d="M497 900c0-1-1-2-2-3 0-2 0-3-2-4v-1c1-1 0-1 0-3 1 2 4 5 4 7v1c1 1 3 2 3 4v1c1 0 1 0 2 1l1-1c0-1 0-2 1-2l1-1c0 1 0 2 1 2l1 1 1-1c1 0 3 1 5 1l-1 4h2c0 2-1 2-3 3-1 1-1 1-2 0l-1 3c0-2-1-3-2-4-2 1-1 1-3 1v-1l-2-2-1-2c-1-1-1-1-1-2l-2-2z" class="F"></path><path d="M508 901c1 0 3 1 5 1l-1 4v1c-1 1-1 1-2 1s-1 0-2-1-3-3-3-4l2-1 1-1z" class="i"></path><path d="M668 372h2l1 1c1 0 3 0 4 1h-1v4 1c0 1 0 1 1 2v2l1 1c-1 1-1 4-1 5 1 1 1 1 1 2-1 1-1 3-1 4v3c-1 1-1 3-1 5v3l-1 1c0 1 0 2-1 4h0c0 1 0 2-1 3v2l-2 2c0 2 0 1-1 2l-2 2h0l-1 1h-1c0 1-1 1-1 3h-1v-1c-1 0-1 1-1 0h-2c-1 1-4 1-6 1v-1h-2l-1-1c-2-1-3-1-4-2l-7-4c-1 0-1 0-2-1-1 0-1 0-1-1h0-1c-2-2-3-4-3-5l1-1v-3h1v-2c1-1 1-1 1-2 1-1 1-2 2-3l3-4 1-1 1-2 3-3c1 0 1 0 1-1 1-1 2-2 4-3h1 1 2l1 1h1v-3c1 0 1-1 2-1l1-1v-1h1 1v-1h-1c1-1 2-3 4-3h2 0 1v-2l-1-1 2-2z" class="G"></path><path d="M441 558l3-4c-1-1-1-2-1-2 1 0 1-1 2 0h1c1 0 1-1 2 0h3c2-1 4-1 5-2 0-1 0-1 1-2l1 1h1l2 8 9 38 1 4c0 2 0 3 1 5l9 37h-1l-1-2-1-4h0-2c-2-1-4-1-6-1-4-1-6 0-9 1l-2-9-15-60-3-8z" class="AF"></path><path d="M441 558l3-4c-1-1-1-2-1-2 1 0 1-1 2 0h1c1 0 1-1 2 0h3c2-1 4-1 5-2 0-1 0-1 1-2l1 1h1l2 8h-1c-1-1-1-2-1-3l-1-2c0 1-1 2-1 2-1 1-1 1-2 0-2 0-7 1-9 2h-1l-1-1c-2 4 0 5-1 9l1 2-3-8z" class="x"></path><path d="M459 626c3 0 4 0 6-2l2 2h4c2 0 3 1 4 2h1l2 7h0-2c-2-1-4-1-6-1-4-1-6 0-9 1l-2-9z" class="y"></path><path d="M734 198v-5c1 1 3 2 4 3s2 2 4 2c-1 3 0 5 0 8h1l2 2h-1v2 1 2h2c1 0 3 0 4-1l8 9c-1 2-1 3 0 5 6 10 16 19 20 31v2c-1-2-3-4-5-6l-3-3-4-2v1h-3l-1-1v1l-2 1v3h0l-3 2-2-2c-2 0-2-1-3-2 0-2-1-3-2-5h-1-3l-3 2-2 1c0-1-1-1-1-2s0 0-1-1l-3 2-3 2-1 1h-1c-1 0-2 0-3 1 0 2-1 2-3 3-1 0-6 1-6 1h-2-1v-2l-1-1c-1-1 1-3 1-4v-1l-1-1-3 3h0l1-2v-3c2-2 4-3 5-5 2-3 4-6 6-8 3-3 5-7 7-11 1-4 2-8 3-13v-1c-1-2 0-6 0-9z" class="m"></path><path d="M741 220c2-1 4-1 6-1l-1 1s-1 0-1 1h-1c-1-1-2-1-3 0v-1z" class="R"></path><path d="M749 246l3-3 2 2h0c-1 1-1 2-1 2l1 2c0 1-1 1-2 2 0-2-1-3-2-5h-1z" class="U"></path><path d="M744 210v1 2h2c2 1 2 0 4 2 0 2 0 2-1 4h-2c-2 0-4 0-6 1v-2c1-1 2-2 2-4 1 0 1-3 1-4z" class="c"></path><path d="M742 237h1l1-1h1c1 2 0 3 1 5h1c1-1 0-1 1-1v1 2s0 1-1 2l-1 1-3 2c0-1 0-2-1-3-2-1-5-1-6-1v-1h1l1-1-1-1 1-1v1h2 1 1l1-1c0-1 0-1-1-2v-1z" class="z"></path><path d="M724 232h2c1-2 3-5 5-6 2-3 5-5 7-7s3-4 5-6v1c0 2-1 3-2 4v2 1c-3 3-5 5-8 7v2h3c-2 1-2 1-3 2 1 0 2 1 3 1h0 3l1 2c1 0 1 0 2 1v1 1c1 1 1 1 1 2l-1 1h-1-1-2v-1l-1 1-1-1c-2-1-2-2-4-3l1 2-1 1c-2-2-2-3-3-4h-1v1h0l-1-2c-2 1-2 1-4 3-1 1-1 2-3 2-2 2-4 5-7 8v-3c2-2 4-3 5-5 2-3 4-6 6-8z" class="y"></path><path d="M724 232h2c1-2 3-5 5-6 2-3 5-5 7-7s3-4 5-6v1c0 2-1 3-2 4l-21 22c-2 2-4 5-7 8v-3c2-2 4-3 5-5 2-3 4-6 6-8z" class="d"></path><path d="M734 198v-5c1 1 3 2 4 3s2 2 4 2c-1 3 0 5 0 8h1l2 2h-1v2c0 1 0 4-1 4v-1c-2 2-3 4-5 6s-5 4-7 7c-2 1-4 4-5 6h-2c3-3 5-7 7-11 1-4 2-8 3-13v-1c-1-2 0-6 0-9z" class="b"></path><path d="M739 207l2 1c0 1 0 1-1 2v3h-1c0 1-1 2-1 3h-1v-1-3l1-5h1zm-5-9h1l1 4c0 4 0 9-1 13 0-2 0-6-1-8s0-6 0-9z" class="X"></path><path d="M734 207c1 2 1 6 1 8-1 2-1 5-3 6h-1c1-4 2-8 3-13v-1z" class="M"></path><path d="M734 198v-5c1 1 3 2 4 3s2 2 4 2c-1 3 0 5 0 8l-1 2-2-1h-1l-1 5c-1-2 0-7-1-10l-1-4h-1z" class="g"></path><path d="M738 197c2 3 1 6 1 10h-1c0-4-2-7 0-10z" class="V"></path><path d="M738 196c1 1 2 2 4 2-1 3 0 5 0 8l-1 2-2-1c0-4 1-7-1-10v-1z" class="c"></path><defs><linearGradient id="Ar" x1="731.23" y1="237.446" x2="720.319" y2="255.773" xlink:href="#B"><stop offset="0" stop-color="#a1654b"></stop><stop offset="1" stop-color="#b57d5c"></stop></linearGradient></defs><path fill="url(#Ar)" d="M720 240c2 0 2-1 3-2 2-2 2-2 4-3l1 2h0v-1h1c1 1 1 2 3 4l1-1-1-2c2 1 2 2 4 3l1 1 1 1-1 1h-1v1c1 0 4 0 6 1 1 1 1 2 1 3l-2 1c0-1-1-1-1-2s0 0-1-1l-3 2-3 2-1 1h-1c-1 0-2 0-3 1 0 2-1 2-3 3-1 0-6 1-6 1h-2-1v-2l-1-1c-1-1 1-3 1-4v-1l-1-1-3 3h0l1-2c3-3 5-6 7-8z"></path><path d="M733 246c1 1 2 1 3 2l-3 2v-4z" class="y"></path><path d="M736 240l1 1 1 1-1 1h-1v1c1 0 4 0 6 1 1 1 1 2 1 3l-2 1c0-1-1-1-1-2s0 0-1-1l-3 2c-1-1-2-1-3-2v-2h1 0 1l-1-1h-2l-1-1h3v-1c1 1 1 1 2 1v-2z" class="x"></path><path d="M707 174l5 4c1 1 2 1 4 1l1-1c1 1 2 1 3 2l6 7h-2c5 4 9 7 10 13 0 2 0 4-1 6 0 1 0 1 1 2-1 5-2 9-3 13-2 4-4 8-7 11-2 2-4 5-6 8-1 2-3 3-5 5v3l-1 2-10 13c-2 3-4 5-5 8l-1-1c2-6 4-12 8-18 4-7 10-15 11-23v-3c0-3 0-6-2-9-1-3-4-6-8-7-7-3-16 1-22 5-1 1-2 2-4 3-3 1-5 5-8 6l-11 16c2-6 5-12 8-18l1-2 1-2c1-1 5-7 6-8l4-2c1-1 2-1 3-2 2 0 4-2 5-3l16-11c2-1 7-4 7-6v-1c0-4-3-6-5-8 3 0 3 1 5 4v1-1l1-1c-1-2-4-3-5-5v-1z" class="i"></path><path d="M717 178c1 1 2 1 3 2l6 7h-2c-1-1-3-2-5-3-1 0-2 0-3-1 0-1-1-1-1-2-2-1-2-1-3-3 1 1 2 1 4 1l1-1z" class="Q"></path><path d="M713 189c1-1 3-1 5-1 1 0 1 1 2 1 5 2 8 6 10 10 1 3 0 7 0 10-1-1-1-4-1-6-2-4-5-7-8-9-1 0-3-2-4-2s-2 1-3 1l-12 6c-3 2-6 3-9 4l-1 1 21-15z" class="G"></path><path d="M713 189c1-1 3-1 5-1 1 0 1 1 2 1-2 1-3 1-5 1s-1 0-2-1z" class="H"></path><path d="M692 204l1-1c3-1 6-2 9-4l12-6c1 0 2-1 3-1s3 2 4 2c3 2 6 5 8 9 0 2 0 5 1 6 0 2 0 4-1 6-2 5-4 11-8 16h0v-1l-1-1c-1-1-1-3-2-4v-4l-1-1v2c0 2 0 1-1 2 0 1 0 4-1 5h0v-3c0-3 0-6-2-9-1-3-4-6-8-7-7-3-16 1-22 5-1 1-2 2-4 3-3 1-5 5-8 6h0c0-3 7-8 9-9 3-2 5-4 7-5h1-1v-1c-2 1-3 2-5 2 1-2 8-7 10-7z" class="f"></path><path d="M737 412c5-2 10-4 15-2 2 1 5 3 5 5l1 3v1 7l1-1-6 25h1l1-1 2-2h1l1 1h0c-3 3-8 7-10 11-1 3-4 5-4 9h-1c0 2 1 3 2 5v2c-1 2-3 7-3 8l-1 3v1c-1 1 0 3 0 5h-1v-3c0-2 0-4-1-5-2 1-4 0-6 0-3-3-5-6-5-9l1-1 1 2h0c0-1-1-2-1-2l-1 1c-1 1-1 2-1 3h0l-1-2h0c0-3 0-3 2-6v-3h-1v-3c0-1-1-2-1-3v-1c0-2 0-4-1-5l1-23 1-1c-1-1-1 0 0-1h1v-2h1v-3c0-1 0-2 1-3 0-2 1-3 1-5 1-3 3-4 5-5z" class="q"></path><path d="M734 469h1c2 0 2 1 3 2 0 1 1 2 0 3 0 1-1 1-1 1-1 0-2 0-2-1-2 0-2-1-3-2 0-2 1-2 2-3z" class="i"></path><path d="M732 417c1-3 3-4 5-5h1c0 1 0 1 1 1-1 2-3 3-4 5l-2 4h-2l-1 3c0-1 0-2 1-3 0-2 1-3 1-5z" class="B"></path><path d="M730 428c1 0 2 1 3 1 1-1 0-2 2 0v4 1c0 1-1 3-2 4-2-2-2-6-4-8v-2h1z" class="m"></path><path d="M758 426l1-1-6 25h1l1-1 2-2h1l1 1h0c-3 3-8 7-10 11-1 3-4 5-4 9h-1l-1-2c7-7 9-16 11-25 2-5 3-10 4-15z" class="n"></path><path d="M737 453h0 1 0 0c1 0 1 0 1-1 1 0 2-1 4-2 0 1 0 3-1 4v4c0 1-3 3-3 5 1-1 2-1 2-1h2c-2 1-5 1-7 2-1 0-1 1-2 2v-3c0-1-1-2-1-2 0-1 1-1 1-2v-1c2 0 2 0 3-1v-4z" class="P"></path><path d="M737 444l-1-1h1c1-1 1-3 2-4v-4c1 1 1 1 1 2 0 2 1 4 2 6v2l1 4v1c-2 1-3 2-4 2 0 1 0 1-1 1h0 0-1 0 0c0-2-1-3-2-5v-1c1-1 1-2 2-3z" class="l"></path><path d="M737 444s1 1 1 2c0 2-1 2 1 3h1 3v1c-2 1-3 2-4 2 0 1 0 1-1 1h0 0-1 0 0c0-2-1-3-2-5v-1c1-1 1-2 2-3z" class="J"></path><path d="M728 431v6l1 1c1 2 1 5 2 7h1c1 0 2 1 3 2v1c1 2 2 3 2 5h0v4c-1 1-1 1-3 1v1c0 1-1 1-1 2 0 0 1 1 1 2v3h-1c-1 2-1 2-3 3v1c0 1-1 2-2 3 0 1 0 2-1 3h0c0-3 0-3 2-6v-3h-1v-3c0-1-1-2-1-3v-1c0-2 0-4-1-5l1-23 1-1z" class="G"></path><path d="M728 437l1 1c1 2 1 5 2 7h1c1 0 2 1 3 2v1l-2 1h-1c-1 3-1 7-1 11v1 2c-1-1-1-2-2-4v-3c1-6 0-13-1-19z" class="D"></path><path d="M728 431v6c1 6 2 13 1 19v3 8h0-1v-3c0-1-1-2-1-3v-1c0-2 0-4-1-5l1-23 1-1z" class="L"></path><path d="M727 461v-1c1-2 1-3 1-5v-2l1-1v4 3 8h0-1v-3c0-1-1-2-1-3z" class="O"></path><path d="M443 266v-2h2c1-1 2 0 3-1 2-1 3-3 5-4 4-2 8-4 13-5 2 0 3-2 5-2 2-1 3 0 4-1 2-1 5-1 7-2v1c3 2 5 4 5 8 1 2 4 6 5 7l5 12 1 1c2 4 3 7 4 10l2 2v6 2c0 3-1 3-2 5l-1 1h-2l-1 1-1-2c-1 2-1 4-1 6h0-2l-3-6-1 1v1l-1-1h0v1l-1 1h-2c-1 0-1-1-1-2 1 0 1 0 2-1l-1-1v-2l-1-1h-1c1-1 1-1 1-3l-3 1v-2h-1c0-1-2-2-2-2v-2c0-1-1-2-2-3l-1-1c2 0 4-1 5-1 0-2 0-3-1-4l-6 1-1-1c-4 0-8 0-12-1h-3c-5 0-9 1-14 0l-1-1-1 1-2-1c-1 0-3 0-4-1v-1c2 0 3 1 4 0-2-1-5-1-7-2 3-1 5 0 7 0l-1-3c-1-2-1-3-2-5h0c2-1 3-2 6-2z" class="V"></path><path d="M471 255c2 0 2-1 4-1l1 1 1-1c2 1 4 2 4 4h0l1 2c-2-1-3-2-5-3v1l2 1-1 1h0l-4-4c0-1-2 0-3-1z" class="g"></path><path d="M490 290l2 1 1-1-1-1v-2c1 1 1 0 2 1v-1c1-1 1 0 2-1 0 2 0 3-1 4l1 1c1 0 1 0 2 1v1h2l1 2c-1 0-2-1-4-2 0-1-1-1-2-1v-1l-3 1h-1l-1-2z" class="M"></path><path d="M478 260h0l1-1-2-1v-1c2 1 3 2 5 3 0 1 1 3 2 3 1 1 1 0 2 2h0-2l-1-1h-2l-1-1h1c-1-1-3-1-4-2l1-1zm13 17c2 0 3 0 3 1l-1 2 2 2c0 1 1 2 0 3h-3l-1-2-1 1h-1-1l-1-2c0-2 0-2 2-3 1 1 1 0 2 0 1 1 1 1 2 1 0-2-1-2-2-3z" class="j"></path><path d="M487 282l2-1c0 1 1 1 2 2l-1 1h-1-1l-1-2z" class="g"></path><path d="M484 265l2 1h1v1 1l1 1-1 1h1l1-1v1l-1 2c1 1 2 1 3 2v1 2c-1 0-1 0-2 1-1 0-2 0-2 1l-1-1c2-2 1-3 1-5-1-1-1-2-2-3s-2-2-3-2l2-3z" class="Q"></path><path d="M478 271v-1l1-1h2c1 0 2 2 3 3v-2h1c1 1 1 2 2 3 0 2 1 3-1 5l1 1c0-1 1-1 2-1 1-1 1-1 2-1h0c1 1 2 1 2 3-1 0-1 0-2-1-1 0-1 1-2 0-2 1-2 1-2 3l-1-1v-1-1l-1 1h-1l1-1c0-1-1-1-1-1v-2-1l-1-1h-2v1l2 1v2h-1c-2-1-3-2-5-4-1 0 0 0-1-1h1l1-2z" class="e"></path><path d="M473 282c1-1 1-1 1-2-1 0-2-1-4 0h0l-1-1c-1-1-1-2-3-2-1-1-3-2-4-4l-3-3c-1-1-1-1-2-1l-1-1h0v-1h3c1 2 1 2 3 2 0 1 0 1 1 2l1 1 1-1 1 1v1c0 1 0 2 1 3h2 0l3 3v-1l-1-1 1-1c1 1 2 1 3 3v1h2c2-2 2-2 5-2h1v-2l-2-1v-1h2l1 1v1 2s1 0 1 1l-1 1h1l1-1v1 1l1 1 1 2v1c-1-1-2-1-2-2h-2v-1 1l1 1h-1c-1-1-2-1-2-2h0-2l-6 1-1-1z" class="Q"></path><path d="M456 262l1-1c1 0 2 0 3-1h0 2 0v-1c1-1 1-1 2-1l2-1c0 1-1 2-2 2h-1l-1 2h0c-1 0-2 1-3 1l-1 1c2 1 2 1 3 1-1 1-1 2-1 3h-1-3v1h0l1 1c1 0 1 0 2 1l3 3c1 2 3 3 4 4 2 0 2 1 3 2l1 1h0c2-1 3 0 4 0 0 1 0 1-1 2-4 0-8 0-12-1h5 2c-1-1-2-2-4-3-1 0-1-1-1-1-2-1-3-2-5-3l-1 1-2-1v-3h0l-1-3c-1 0-2 1-2 2l-2-1c-1-2 0-3 0-5 1-1 1 0 2 0 1-1 1-2 2-2h2z" class="g"></path><path d="M455 271l2-1c1 1 2 2 2 3l-1 1-1 1-2-1v-3h0zm-5-7c1-1 1 0 2 0 1-1 1-2 2-2h2l-1 2h0l-2 1h1c-1 1-2 1-2 1l-1 1c1 1 1 1 2 1h1c-1 0-2 1-2 2l-2-1c-1-2 0-3 0-5z" class="a"></path><path d="M443 266v-2h2c1-1 2 0 3-1 2-1 3-3 5-4 4-2 8-4 13-5 2 0 3-2 5-2 2-1 3 0 4-1 2-1 5-1 7-2v1c3 2 5 4 5 8 1 2 4 6 5 7l5 12 1 1c2 4 3 7 4 10l2 2v6 2c-1-1-2-1-3-3l-1-2h-2v-1c-1-1-1-1-2-1l1-3h2v1c-1 0-1 0-2 1h1 1l1 1 1-1c-1 0-1-1-1-2h0c-1-1 0-1-1-1-2 0-1 0-2-1 1 0 1 0 2-1-1-1-2-1-3-2 1-1 1-1 1-2l-1-1 1-1v-1h-2v-1c0-1-1-1-1-2-1-1-1-1-1-2-1-2-2-2-3-4 0-2-1-2-1-4v-1c-2-1-3-3-2-5h0l-3-2 1-1c-1-1-1-1-2-1-1-1-1-1-1-2h-4c-1 0 0-1-1-1s-2 1-3 1h-1c-2 0-2 1-4 1h0l-1 1c-1 0-2 0-2 1-1 0-2 1-3 1v-1c-3 3-6 4-9 6-1 0-1 1-2 2-1 0-1-1-2 0-2 1-2 1-4 0v1h-2l-1 1z" class="S"></path><path d="M502 288l2 2v6 2c-1-1-2-1-3-3l-1-2h1 1v-4-1z" class="L"></path><path d="M481 286c2 1 3 3 5 3v-1l2 2 2-1v1h0l1 2h1l3-1v1c1 0 2 0 2 1 2 1 3 2 4 2 1 2 2 2 3 3 0 3-1 3-2 5l-1 1h-2l-1 1-1-2c-1 2-1 4-1 6h0-2l-3-6-1 1v1l-1-1h0v1l-1 1h-2c-1 0-1-1-1-2 1 0 1 0 2-1l-1-1v-2l-1-1h-1c1-1 1-1 1-3l-3 1v-2h-1c0-1-2-2-2-2v-2c0-1-1-2-2-3l-1-1c2 0 4-1 5-1z" class="T"></path><path d="M492 292l3-1v1l1 1c1 2 3 3 4 5h-1c-1-1-2-1-2-2h-1 0l-1-1c-1 0-2 0-3-1v-2z" class="R"></path><path d="M488 294l1-1 11 9 2 1-1 1h-2l-1 1-1-2-2-3h0c-1 0-3-1-5-1l-2-5z" class="i"></path><path d="M495 300h1l2 2h2l2 1-1 1h-2l-1 1-1-2-2-3z" class="h"></path><path d="M489 304c-2-2-2-5-3-7-1-1-2-2-2-3 0-2 1-2 2-3h0l2 3 2 5c2 0 4 1 5 1h0l2 3c-1 2-1 4-1 6h0-2l-3-6-1 1v1l-1-1h0z" class="D"></path><path d="M490 299c2 0 4 1 5 1h0l2 3c-1 2-1 4-1 6-2-3-4-7-6-10z" class="k"></path><path d="M450 264c0 2-1 3 0 5l2 1c0-1 1-2 2-2l1 3h0v3l2 1 1-1c2 1 3 2 5 3 0 0 0 1 1 1 2 1 3 2 4 3h-2-5-3c-5 0-9 1-14 0l-1-1-1 1-2-1c-1 0-3 0-4-1v-1c2 0 3 1 4 0-2-1-5-1-7-2 3-1 5 0 7 0l-1-3c-1-2-1-3-2-5h0c2-1 3-2 6-2l1-1h2v-1c2 1 2 1 4 0z" class="S"></path><path d="M449 273l1-1 3 3-1 1h-2l-1 1-2-1c-2 0-3-1-4-2h1 2v-1l3 1v-1z" class="M"></path><path d="M449 273v-1h0c1-1 1-1 3-1v1l3-1v3l2 1 1-1c2 1 3 2 5 3 0 0 0 1 1 1 2 1 3 2 4 3h-2l-2-1-2-2c-2-2-4-2-6-2s-2 0-3-1l-3-3-1 1z" class="Y"></path><path d="M433 276c3-1 5 0 7 0 3 0 6 1 8 3 1-1 2-1 3 0 1 0 1 0 1 1 2 0 5 0 6 1-5 0-9 1-14 0l-1-1-1 1-2-1c-1 0-3 0-4-1v-1c2 0 3 1 4 0-2-1-5-1-7-2z" class="K"></path><defs><linearGradient id="As" x1="439.914" y1="267.507" x2="452.177" y2="273.3" xlink:href="#B"><stop offset="0" stop-color="#6b6a6c"></stop><stop offset="1" stop-color="#858385"></stop></linearGradient></defs><path fill="url(#As)" d="M450 264c0 2-1 3 0 5l2 1c0-1 1-2 2-2l1 3h0l-3 1v-1c-2 0-2 0-3 1h0v1 1l-3-1v1h-2-1c-1 0-3-1-4-1-1-2-1-3-2-5h0c2-1 3-2 6-2l1-1h2v-1c2 1 2 1 4 0z"></path><path d="M471 255c1 1 3 0 3 1l4 4-1 1c1 1 3 1 4 2h-1l1 1h2l1 1h0l-2 3c1 0 2 1 3 2h-1v2c-1-1-2-3-3-3h-2l-1 1v1l-1 2h-1c1 1 0 1 1 1 2 2 3 3 5 4-3 0-3 0-5 2h-2v-1c-1-2-2-2-3-3l-1 1 1 1v1l-3-3h0-2c-1-1-1-2-1-3v-1l-1-1-1 1-1-1c-1-1-1-1-1-2-2 0-2 0-3-2h1c0-1 0-2 1-3-1 0-1 0-3-1l1-1c1 0 2-1 3-1h0l1-2h1c1 0 2-1 2-2l2 1v-1c1-1 1-1 3-1v-1z" class="Z"></path><path d="M470 263l2-1 1 1v1l-1 1v-1 1c0 1 1 2 0 4l1 2-2 1h0v-2l-3-3 2-1-1-1-1 1-1-1-1 1-1-1c1 0 2-1 3-1s1-1 2-1z" class="d"></path><path d="M478 271c-1 0-1 0-2-1l-1 1v1c-2-1 0-1-1-3v-1c0-2 0 0 1-2l1 1 1-1c-1 0-2-1-2-2v-1c0-1-2-1-2-3l2-1 2 2c1 1 3 1 4 2h-1l1 1h2l1 1h0l-2 3c1 0 2 1 3 2h-1v2c-1-1-2-3-3-3h-2l-1 1v1z" class="f"></path><path d="M482 268c-1-1-1-2-2-3h-1v-1l1-1 1 1h2l1 1h0l-2 3z" class="Z"></path><path d="M471 255c1 1 3 0 3 1v1c-1 1-2 1-3 1s-1 1-1 1h-1l-1 1 2 3c-1 0-1 1-2 1s-2 1-3 1l1 1 1-1 1 1 1-1 1 1-2 1h-1-1v1c-1 1-1 2-3 2-1-2 0-1 0-3l-1-1-1 1h-1c0-1 0-2 1-3-1 0-1 0-3-1l1-1c1 0 2-1 3-1h0l1-2h1c1 0 2-1 2-2l2 1v-1c1-1 1-1 3-1v-1z" class="Q"></path><path d="M302 483c1 2 1 5 1 7v12 3h1 1v1c0 6 0 13 1 19 1 13 2 25 5 37 5 22 14 44 25 64l21 33 42 60 18 25-1 1-9-13-1 1c1 1 2 2 2 3v1c1 2 3 3 4 6l-3-1-17-23h-1c-1-1-3-2-5-2h0v-1c-2 0-5-4-6-5 0-1 0-1-1-2l-2-3c-1-1-3-2-4-4h0l-1-2-1 1v1h0l-2-1c-1 0-2-1-3-1h0c-4-3-6-7-9-11l-2-1-1-1c-3-2-4-4-6-7v-2l1-2h-1v-2c2 0 2-1 3-2 0-2 0-4 1-6v-1h-1c0-2-1-3-2-4 0-1 0-2 1-3l-3-5h1s0 1 1 1c-3-5-5-10-9-15h0c-2 0-3-3-4-4l-1-1c-1-2-1-3-2-4l-1 1c-2-3-3-6-5-9-3-6-7-12-9-18 0-3-2-5-3-8l-6-20c0-2-2-4-2-7l-5-28h0v-10l-1-32h0v5l1 1v-22z" class="W"></path><path d="M347 653h1s0 1 1 1l3 3c2 1 2 2 3 4h-3l-2-3-3-5z" class="S"></path><path d="M318 596v-1l-1-3v-2h0l2 5c1 1 1 2 2 3l1 2 1 3v1c1 1 1 3 1 5-2-2-3-4-3-6-2-2-3-4-3-7z" class="a"></path><path d="M350 658l2 3h3l2 5c0 1 1 3 1 4v1l-2-3c0 1 0 3-1 4l-2-5-1-2h-1c0-2-1-3-2-4 0-1 0-2 1-3z" class="B"></path><path d="M352 661h3l2 5c0 1 1 3 1 4v1l-2-3c-1-2-3-4-4-7z" class="F"></path><path d="M357 666c4 3 6 8 9 12l13 19c1 2 3 3 4 6 1 2 3 4 5 6 2 3 3 5 4 7l16 21c1 2 3 3 4 6l-3-1-17-23-34-48v-1c0-1-1-3-1-4z" class="L"></path><path d="M356 668l2 3 34 48h-1c-1-1-3-2-5-2h0c-2-5-6-11-9-14l-3-4v-2c-1-1-2-2-3-4-2-1-3-3-4-5l-12-16c1-1 1-3 1-4z" class="E"></path><path d="M302 531l3 16c1 6 1 11 3 17 1 1 1 2 1 3l-1 1 2 4v2c0 3 1 5 2 8 2 4 3 9 6 14 0 3 1 5 3 7 0 2 1 4 3 6 2 4 4 9 7 13 3 6 7 11 9 17h0c-2 0-3-3-4-4l-1-1c-1-2-1-3-2-4l-1 1c-2-3-3-6-5-9-3-6-7-12-9-18 0-3-2-5-3-8l-6-20c0-2-2-4-2-7l-5-28h0v-10z" class="L"></path><path d="M352 665l1 2 2 5 12 16c1 2 2 4 4 5 1 2 2 3 3 4v2l3 4c3 3 7 9 9 14v-1c-2 0-5-4-6-5 0-1 0-1-1-2l-2-3c-1-1-3-2-4-4h0l-1-2-1 1v1h0l-2-1c-1 0-2-1-3-1h0c-4-3-6-7-9-11l-2-1-1-1c-3-2-4-4-6-7v-2l1-2h-1v-2c2 0 2-1 3-2 0-2 0-4 1-6v-1z" class="m"></path><path d="M352 665l1 2-1 2c1 2 2 3 2 6-1-1-1-2-2-2l-1-1c0-2 0-4 1-6v-1z" class="K"></path><path d="M366 695h2c1 1 2 3 3 4v1 1l-1-1c-2-2-3-2-4-5z" class="C"></path><path d="M348 676l1-1c2 1 3 3 4 4v2 1c1 1 3 3 4 5v1 1l-2-1-1-1c-3-2-4-4-6-7v-2l1-2h-1z" class="D"></path><path d="M348 676l1-1c2 1 3 3 4 4v2 1c1 1 3 3 4 5v1 1l-2-1c-2-4-3-8-6-12h-1z" class="K"></path><path d="M348 674c2 0 2-1 3-2l1 1 16 22h-2c-5-5-9-10-13-16-1-1-2-3-4-4l-1 1v-2z" class="H"></path><path d="M670 658c0-1 1-1 1-2l3-3v3l-2 3h0c2-1 3-2 6-2l3 2c1 1 2 1 2 2h1l3 3c1 1 0 2 0 3 0 3-1 5-2 7l-2 1c0 2 0 2-1 4 1 0 1 0 2 1l-8 11-9 12-2 2-1 2c-2 2-3 5-5 7h-1l-2-2c-2 0-3-1-5-1v-1l-1 1c-1 2-3 5-5 6h-2l-2 4c-2 3-8 13-10 14-1 0-2 1-2 2h-2c-1 2-1 3-1 4l-2-1-1-1-1-1h-2v-1l-3 2 1 1h0l-2-2 2-1c-3-1-3 0-5 1l-1 1c-1 3-4 4-5 8 1 0 1 0 1 1h1l-2 2c-5 8-10 17-16 23l-1 1-1 1 1 2c-1 1-2 0-3 1-1 0-2 2-2 3l-2 2-1 2-2 3c-4 6-9 12-13 19h0c0 1-1 2-1 3-1 2-3 4-5 5l-1 1v2c-1 0-2-1-3-2v1l-4 1 1-3-2 1 9-13 12-17c3-4 6-9 9-13 2-2 4-4 5-6s2-3 3-5c2-3 3-4 5-6 1-2 3-3 4-5 2-3 3-6 6-9 4-3 6-9 9-13l4-5c2-3 7-8 8-10 0-1 1-2 1-3 2-2 5-5 7-8 1-1 2-3 3-5l3-3c3-6 6-11 10-16l7-7v-1h1c1 0 2-1 2-2 2-2 3-4 5-6l1-2 3-1 1-2z" class="c"></path><path d="M605 747v-1c2-3 4-6 7-7-1 3-4 4-5 8 1 0 1 0 1 1h1l-2 2c-5 8-10 17-16 23 0-1 0-3 1-4h0-1c-1 2-4 4-6 6h0 0l6-7c2-4 5-7 7-11 1-1 1-2 2-3 2-2 3-5 5-7z" class="H"></path><path d="M605 747v-1c2-3 4-6 7-7-1 3-4 4-5 8 1 0 1 0 1 1h1l-2 2c-1 1-2 2-2 3-1 0-1 0-2 1 0-3 2-4 2-7h0zm-20 28h0c2-2 5-4 6-6h1 0c-1 1-1 3-1 4l-1 1-1 1 1 2c-1 1-2 0-3 1-1 0-2 2-2 3l-2 2-1 2-2 3c-4 6-9 12-13 19h-1l1-2c-1 0-1-1-1-2 2-3 6-7 6-12v1c2-2 3-2 3-4 1-2 3-4 4-6s2-3 4-4l2-3z" class="l"></path><path d="M585 775h0c2-2 5-4 6-6h1 0c-1 1-1 3-1 4l-1 1-1 1 1 2c-1 1-2 0-3 1-1 0-2 2-2 3l-2 2-1 2-2 3h-1c0-1 1-2 1-3h0l-2 2h0l3-6c1-1 2-2 2-3l2-3z" class="B"></path><path d="M561 803c3-1 6-4 8-7 0-1 0-1 1-2h0c1-1 1-2 2-3 0 5-4 9-6 12 0 1 0 2 1 2l-1 2h1 0c0 1-1 2-1 3-1 2-3 4-5 5l-1 1v2c-1 0-2-1-3-2v1l-4 1 1-3-2 1 9-13z" class="U"></path><path d="M565 803h1c0 1 0 2 1 2l-1 2h1 0c0 1-1 2-1 3-1 2-3 4-5 5l-1 1v2c-1 0-2-1-3-2 1-2 4-4 5-7 1-2 3-3 3-6z" class="D"></path><path d="M561 803c3-1 6-4 8-7 0-1 0-1 1-2h0c1-1 1-2 2-3 0 5-4 9-6 12h-1c-2 2-4 4-7 5 0 1-1 1-1 2s0 1-1 2c0 1 0 2-2 3l-2 1 9-13z" class="R"></path><path d="M613 738c1-3 4-5 6-7l6-9c1-2 2-3 3-4 1-4 5-7 7-10 2-4 5-6 7-9 1-2 3-4 3-5h0-1c1-2 3-4 5-6l1 1h2l-4 6c-1 1-2 2-2 4l-1 1h1c0 1 1 1 2 1h0l1 2c0 1 1 1 2 1v1l2-1 1 1h1 0l1 1c-1 0-1 1-2 1-2 0-2 0-3 1l-2 2v1h1c-1 2-3 5-5 6h-2l-2 4c-2 3-8 13-10 14-1 0-2 1-2 2h-2c-1 2-1 3-1 4l-2-1-1-1-1-1h-2v-1l-3 2 1 1h0l-2-2 2-1c-3-1-3 0-5 1z" class="G"></path><path d="M623 731h0l21-30 5 2c-1 2-3 3-5 5s-4 4-6 7l-5 6c-1 2-1 3-2 4-1 3-3 5-5 7l-3 3v1c1 1 1 2 0 3l-1-1h-2v-1l-3 2 1 1h0l-2-2 2-1c0-1 3-4 3-4 0-1-1-1 0-2h2z" class="X"></path><path d="M629 724c1-1 1-2 2-3 2-3 4-5 6-7v1c-2 2-3 4-5 5l1 1c-1 2-1 3-2 4l-2-1z" class="O"></path><path d="M629 724l2 1c-1 3-3 5-5 7l-3 3v1c1 1 1 2 0 3l-1-1h-2v-1l-3 2 1 1h0l-2-2 2-1c0-1 3-4 3-4 0-1-1-1 0-2h2c-1 1-1 2-1 3l2-2c2-3 4-4 5-8z" class="c"></path><path d="M649 703c0 1 1 1 2 1v1l2-1 1 1h1 0l1 1c-1 0-1 1-2 1-2 0-2 0-3 1l-2 2v1h1c-1 2-3 5-5 6h-2l-2 4c-2 3-8 13-10 14-1 0-2 1-2 2h-2c-1 2-1 3-1 4l-2-1-1-1c1-1 1-2 0-3v-1l3-3c2-2 4-4 5-7 1-1 1-2 2-4l5-6c2-3 4-5 6-7s4-3 5-5z" class="U"></path><path d="M653 704l1 1h1 0l1 1c-1 0-1 1-2 1-2 0-2 0-3 1l-2 2v1h1c-1 2-3 5-5 6h-2l-2 4c-2 3-8 13-10 14-1 0-2 1-2 2h-2c1-3 4-5 5-8 1-2 3-3 4-5 0-1 0-2 1-2 1-3 4-4 5-7 2-3 6-8 9-9v-1l2-1z" class="O"></path><g class="I"><path d="M653 704l1 1h1 0l1 1c-1 0-1 1-2 1-2 0-2 0-3 1l-2 2v1h1c-1 2-3 5-5 6h-2c2-4 5-7 8-11v-1l2-1z"></path><path d="M670 658c0-1 1-1 1-2l3-3v3l-2 3h0c2-1 3-2 6-2l3 2c1 1 2 1 2 2h1l3 3c1 1 0 2 0 3 0 3-1 5-2 7l-2 1c0 2 0 2-1 4 1 0 1 0 2 1l-8 11-9 12-2 2-1 2c-2 2-3 5-5 7h-1l-2-2c-2 0-3-1-5-1v-1l-1 1h-1v-1l2-2c1-1 1-1 3-1 1 0 1-1 2-1l-1-1h0-1l-1-1-2 1v-1c-1 0-2 0-2-1l-1-2h0c-1 0-2 0-2-1h-1l1-1c0-2 1-3 2-4l4-6h-2l-1-1 7-10c1-3 4-6 6-8 0-2 2-3 3-5v-2l1-2 3-1 1-2z"></path></g><path d="M661 692l-1-1c2-4 3-7 6-10l1 2c-2 2-3 4-4 7-1 0-2 1-2 2z" class="F"></path><path d="M667 683l2-3v-1l2-1c0 1 0 1 1 2h1l-1 1c-1 2-2 3-3 5-2 1-4 3-6 4 1-3 2-5 4-7z" class="m"></path><path d="M657 685h3c-1 2-3 4-4 6-1 4-5 5-5 10h-3 0c-1 0-2 0-2-1h-1l1-1c0-2 1-3 2-4l4-6h0c2-1 4-3 5-4z" class="c"></path><path d="M655 691h1c-1 4-5 5-5 10h-3 0c1-1 1 0 1-1v-2l6-7z" class="r"></path><path d="M657 685h3c-1 2-3 4-4 6h-1l-1-1-1 1c-1 2-3 4-5 4l4-6h0c2-1 4-3 5-4z" class="R"></path><path d="M669 666c0 2 0 3-1 5l-1 1-4 8c-1 1-2 3-3 5h-3c-1 1-3 3-5 4h0-2l-1-1 7-10c1-3 4-6 6-8v1l4-2 3-3z" class="H"></path><path d="M652 689c1-3 3-6 5-8 1 2 1 2 0 4-1 1-3 3-5 4z" class="m"></path><path d="M669 666c0 2 0 3-1 5l-1 1-4 8c-1 1-2 3-3 5h-3c1-2 1-2 0-4 4-3 7-8 9-12l3-3z" class="L"></path><path d="M670 658c0-1 1-1 1-2l3-3v3l-2 3h0c2-1 3-2 6-2l3 2c1 1 2 1 2 2h1l3 3c1 1 0 2 0 3 0 3-1 5-2 7l-2 1v1c-3 1-5 1-8 2l1-2-1-1h-1c-2-1-2-2-2-3h-1c0 1-1 2-2 3h0c0-2 1-2 0-4h0-1c1-2 1-3 1-5l-3 3-4 2v-1c0-2 2-3 3-5v-2l1-2 3-1 1-2z" class="P"></path><path d="M674 662c1-1 2-1 3 0 1 0 2 1 2 2 0 2 0 2-2 3-1 0-1 1-2 0s-2-2-2-3 0-1 1-2z" class="h"></path><path d="M670 658c1 1 1 1 1 2v4 3h1v1c0 1 0 1 1 2 0 0 0 1-1 1h-1-1c0-1-1-1-1-2v-2-1l-3 3-4 2v-1c0-2 2-3 3-5v-2l1-2 3-1 1-2z" class="N"></path><path d="M669 660c-1 2-2 3-3 5 1 1 1 1 3 1l-3 3-4 2v-1c0-2 2-3 3-5v-2l1-2 3-1z" class="l"></path><path d="M683 661h1l3 3c1 1 0 2 0 3 0 3-1 5-2 7l-1-2c-1 0-2 0-3 1h-1-2l-3-3c2 0 4-1 5-1 1-1 3-4 4-5h0v-2l-1-1z" class="p"></path><path d="M669 666v1 2c0 1 1 1 1 2h1 1c1 0 1-1 1-1h2l3 3h2 1c1-1 2-1 3-1l1 2-2 1v1c-3 1-5 1-8 2l1-2-1-1h-1c-2-1-2-2-2-3h-1c0 1-1 2-2 3h0c0-2 1-2 0-4h0-1c1-2 1-3 1-5z" class="E"></path><path d="M678 673h2 1c1-1 2-1 3-1l1 2-2 1v1c-3 1-5 1-8 2l1-2c1 0 3 0 5-1v-1c-2 0-2 0-3-1z" class="D"></path><path d="M683 675c0 2 0 2-1 4 1 0 1 0 2 1l-8 11-9 12-2 2-1 2c-2 2-3 5-5 7h-1l-2-2c-2 0-3-1-5-1v-1l-1 1h-1v-1l2-2c1-1 1-1 3-1 1 0 1-1 2-1l-1-1h0-1l-1-1-2 1v-1c-1 0-2 0-2-1l-1-2h3 1c1-1 1-1 2-1 0-1 2-3 2-3l5-5c0-1 1-2 2-2 2-1 4-3 6-4 1-2 2-3 3-5l1-1 1-1 1-1c3-1 5-1 8-2v-1z" class="L"></path><path d="M656 702h0c2 1 3 1 4 3-1 1-1 1-1 2l-2-1c-1 1-1 1-2 3l-1-2c1 0 1-1 2-1l-1-1h0-1l-1-1c2 0 2 0 3-2z" class="H"></path><path d="M683 675c0 2 0 2-1 4-2 1-4 3-5 5 0 1-1 2-1 2-1-1-1 0 0-1s1-1 1-2-1-1-2-2l-1-2 1-1c3-1 5-1 8-2v-1z" class="U"></path><path d="M675 678c3-1 5-1 8-2l-1 2c-2 1-5 2-7 3l-1-2 1-1z" class="C"></path><path d="M655 709c1 1 2 2 4 2 0 0 1-1 1-2s0-1 1-2l1 1 3-3-1 2c-2 2-3 5-5 7h-1l-2-2c-2 0-3-1-5-1v-1l-1 1h-1v-1l2-2c1-1 1-1 3-1l1 2z" class="D"></path><path d="M663 690h0v2c-2 3-7 7-7 10h0c-1 2-1 2-3 2l-2 1v-1c-1 0-2 0-2-1l-1-2h3 1c1-1 1-1 2-1 0-1 2-3 2-3l5-5c0-1 1-2 2-2z" class="m"></path><path d="M651 701h1l4 1c-1 2-1 2-3 2l-2 1v-1c-1 0-2 0-2-1l-1-2h3z" class="l"></path><path d="M481 641l-9-37c-1-2-1-3-1-5l43 168c1 5 1 22 3 24 1 2 1 5 1 7l-1 2c-2-2-4-5-5-7l-9-11-1-2c-2-1-3-4-4-6 0-1 0-1 1-2l-2-2-1 1s-1-6-1-7l-7-31-21-77-3-11-2-8-1-2c3-1 5-2 9-1 2 0 4 0 6 1h2 0l1 4 1 2h1z" class="r"></path><path d="M502 780c-2-1-3-4-4-6 0-1 0-1 1-2 1 2 5 7 5 9l-1 1-1-2z" class="x"></path><path d="M464 645l4 1h2c3-1 8-1 11 0l1 1h1v2c0 2 0 1 1 2v3c-1-1-2-1-4-1h-6c-3 1-5 2-7 3l-3-11z" class="AE"></path><path d="M461 635c3-1 5-2 9-1 2 0 4 0 6 1h2 0l1 4 1 2h1c1 2 1 2 1 3s0 2 1 3h-1l-1-1c-3-1-8-1-11 0h-2l-4-1-2-8-1-2z" class="z"></path><path d="M461 635c3-1 5-2 9-1 2 0 4 0 6 1h2 0l1 4c-1-1-2-2-3-2-3-1-7 2-10 1l-4-1-1-2z" class="x"></path><defs><linearGradient id="At" x1="804.063" y1="337.862" x2="781.918" y2="346.825" xlink:href="#B"><stop offset="0" stop-color="#676568"></stop><stop offset="1" stop-color="#807f80"></stop></linearGradient></defs><path fill="url(#At)" d="M769 295c2-1 2-2 4-2h2c5 1 9 3 14 5 2 1 4 4 6 6 4 4 8 9 11 15 2 4 3 8 5 13h2l2 6 2 3v8l1 2 1 1-1 1v1 4c0 2-1 4 0 6 0 3-1 6-1 8v1c-1 3-4 7-5 10 0 1-1 1-1 2h1l-5 2v-1l-1-1v-3l1-2-1-1c-4 6-10 10-16 15l-7 6v-1l1-1c0-2 0-4 1-5 0-1 1-2 1-3v-1c1-1 2-1 2-2h1v-3l1-2v-3c-3 1-4 0-6-1 0-1 1-1 1-2v-1c0-1 0-1-2-1v-2-1c1-4 1-8 0-12 0-1-1-3-1-4-2-3-5-5-8-6-1 0-4-1-5-1-2 0-4 2-6 2l5-5c0-1 1-1 2-2s3-2 4-2c2 0 4 0 5 1v2h0l1-1 2 1h2v-3h1 1v-3-1c0-3 0-5-3-7v-1c0-2-1-2-2-4-2-2-2-2-4-2v-1-1l-1-2c-2-5-5-9-10-12-7-4-11-6-18-4-5 0-8 2-12 5l-2-1c7-6 13-9 22-12l9-1 4 1z"></path><path d="M794 370h0c2 0 2-1 3-2h1l1 2h-1l1 1c-2 0-4 0-5-1z" class="W"></path><path d="M786 338l1-1h0c1-1 1-1 2-1h-1c0 2 0 3 1 5l-1 1h-1-1l-1-1h1v-3z" class="V"></path><path d="M796 357l1-1 2 2c1 1-1 1-1 2 0 0-1 1-1 2h-1l-1-3c0-1 0-2 1-2z" class="M"></path><path d="M779 318l2-2 1 1c0 2 0 3-1 4-2 0-3 0-4 1v-1c1-1 1-2 2-3z" class="b"></path><path d="M777 322c1-1 2-1 4-1 0 1 0 2 1 2l-1 1 2 2c1 1 0 1 1 2 1 2 3 2 3 4v1 3l-1 1c0-3 0-5-3-7v-1c0-2-1-2-2-4-2-2-2-2-4-2v-1z" class="V"></path><defs><linearGradient id="Au" x1="811.711" y1="367.336" x2="788.044" y2="368.858" xlink:href="#B"><stop offset="0" stop-color="#535253"></stop><stop offset="1" stop-color="#848284"></stop></linearGradient></defs><path fill="url(#Au)" d="M803 334c0 2 1 3 2 5l1 1c0 1 0 1 1 2h0c1 2 1 4 2 6l1-3 1 1v-1c1 6 0 12 0 18-2 5-3 10-5 16-4 6-10 10-16 15h0v-1c0-1-1-2-1-2 0-2 1-2 2-3 1-2 3-4 4-5v-2l1-2h3v-2c0-1 1-2 2-3v-1l-2-2h0v-2l2-2v-1h0v-2l1 1 1 2 1-1v-1-1h0 1v1h1v1 1h1c0-2 0-2 1-3v-1c0-2 0-2-1-3l-1 1h-1v-2h-1v-1l-1-3v-1c0-2 0-2 2-3l-1-1c0-1 0-2 1-4v-2h0c0-2-1-3-1-5l-1-1c-1-1 0-3 0-4z"></path><path d="M803 355l2-2c1 1 1 1 1 2l1 3c0 1-1 2-1 3h-1v-2h-1v-1l-1-3z" class="M"></path><path d="M803 367l1-1v-1-1h0 1v1h1v1 1c0 1 0 2-1 3h0v3c-1 1-1 2-2 3-1 2-2 4-4 6h-1c-1 1-2 2-3 2-1 2-2 3-4 4 1-2 3-4 4-5v-2l1-2h3v-2c0-1 1-2 2-3v-1l-2-2h0v-2l2-2v-1h0v-2l1 1 1 2z" class="V"></path><path d="M802 372c1 1 1 2 1 4-1 2-2 4-4 6h-1v-1l2-1v-4c1-2 2-2 2-4z" class="b"></path><path d="M803 367l1-1v-1-1h0 1v1h1v1 1c0 1 0 2-1 3h0v3c-1 1-1 2-2 3 0-2 0-3-1-4 0 0-1 0-1-1 1 0 2 0 2-1l-1-1 1-2z" class="X"></path><defs><linearGradient id="Av" x1="792.714" y1="344.268" x2="778.386" y2="380.276" xlink:href="#B"><stop offset="0" stop-color="#7e7d80"></stop><stop offset="1" stop-color="#9e9b9c"></stop></linearGradient></defs><path fill="url(#Av)" d="M789 341h0v1 1c1 1 1 1 1 2h1v1c-1 0-2 0-3 1h0c0 1-1 1-1 2v1h0-2l-1 1v1c1 2 1 3 2 5v2c1 1 1 1 1 2v2c1 1 0 1 2 1v1c1 1 1 2 2 2 0 1 0 2 1 3v1l1-1h1c1 1 3 1 5 1h0 0l2 2v1c-1 1-2 2-2 3v2h-3l-1 2v2c-1 1-3 3-4 5-1 1-2 1-2 3 0 0 1 1 1 2v1h0l-7 6v-1l1-1c0-2 0-4 1-5 0-1 1-2 1-3v-1c1-1 2-1 2-2h1v-3l1-2v-3c-3 1-4 0-6-1 0-1 1-1 1-2v-1c0-1 0-1-2-1v-2-1c1-4 1-8 0-12 0-1-1-3-1-4-2-3-5-5-8-6-1 0-4-1-5-1-2 0-4 2-6 2l5-5c0-1 1-1 2-2s3-2 4-2c2 0 4 0 5 1v2h0l1-1 2 1h2v-3h1l1 1h1 1l1-1z"></path><path d="M768 345c2-1 4-1 7-2v1 2c1 2 2 2 4 3 3 3 5 6 4 10 0-1-1-3-1-4-2-3-5-5-8-6-1 0-4-1-5-1-2 0-4 2-6 2l5-5z" class="O"></path><path d="M799 371h0 0l2 2v1c-1 1-2 2-2 3v2h-3l-1 2v2c-1 1-3 3-4 5-1 1-2 1-2 3 0 0 1 1 1 2v1h0l-7 6v-1l1-1c0-2 0-4 1-5 0-1 1-2 1-3v-1c1-1 2-1 2-2h1c1-1 2-4 3-5v2c1 0 2-1 2-1v-3l1-1c1-1 1-2 2-2l2-2v-4z" class="W"></path><defs><linearGradient id="Aw" x1="752.862" y1="332.186" x2="800.115" y2="312.981" xlink:href="#B"><stop offset="0" stop-color="#121110"></stop><stop offset="1" stop-color="#424243"></stop></linearGradient></defs><path fill="url(#Aw)" d="M769 295c2-1 2-2 4-2h2c5 1 9 3 14 5 2 1 4 4 6 6 4 4 8 9 11 15 2 4 3 8 5 13h2l2 6 2 3v8l1 2 1 1-1 1v1 4c0 2-1 4 0 6 0 3-1 6-1 8v1c-1 3-4 7-5 10 0 1-1 1-1 2h1l-5 2v-1l-1-1v-3l1-2-1-1c2-6 3-11 5-16 0-6 1-12 0-18v1l-1-1-1 3c-1-2-1-4-2-6h0c-1-1-1-1-1-2l-1-1c-1-2-2-3-2-5l-2-2c-1 0-1-1-2-1 0-1-1-2-2-3h0l-2-1-1-1h0c-2-3-4-7-7-8v-1c-2 0-3-1-5-2h-1 0l-1 1-1 2c-1 1-1 2-2 3l-1-2c-2-5-5-9-10-12-7-4-11-6-18-4-5 0-8 2-12 5l-2-1c7-6 13-9 22-12l9-1 4 1z"></path><path d="M766 307c0-2-1-2-2-3l1-2c2-2 2-2 5-2 0 1 1 1 1 1-1 0-2 0-3 1h1v1l-1 1v2c4 4 8 7 8 13-2-5-5-9-10-12z" class="U"></path><path d="M769 303c2 1 2 0 3 1h4v-1h2c1 2 3 4 5 5h1v-1l1 1-1 1h1l6 6 3 3c2 1 2 2 3 4 1 1 3 2 4 4s3 3 4 4c2 5 2 10 5 15l-1 3c-1-2-1-4-2-6h0c-1-1-1-1-1-2l-1-1c-1-2-2-3-2-5l-2-2c-1 0-1-1-2-1 0-1-1-2-2-3h0l-2-1-1-1h0c-2-3-4-7-7-8v-1c-2 0-3-1-5-2h-1 0l-1 1-1 2c-1 1-1 2-2 3l-1-2c0-6-4-9-8-13v-2l1-1z" class="F"></path><path d="M786 315c3 1 5 5 8 7 1 0 3 2 3 3l1 1v-1c2 2 2 4 3 6 1 1 3 2 4 4 0 1 1 2 1 3v1l1 1v2c-1-1-1-1-1-2l-1-1c-1-2-2-3-2-5l-2-2c-1 0-1-1-2-1 0-1-1-2-2-3h0l-2-1-1-1h0c-2-3-4-7-7-8l-1-3z" class="L"></path><defs><linearGradient id="Ax" x1="775.022" y1="304.956" x2="774.092" y2="316.218" xlink:href="#B"><stop offset="0" stop-color="#4f4e51"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#Ax)" d="M768 306l1-1h3c1 1 3 1 4 1l1 1c1 0 3 1 4 3 1 1 0-1 0 1 1 1 4 3 5 4h0l1 3v-1c-2 0-3-1-5-2h-1 0l-1 1-1 2c-1 1-1 2-2 3l-1-2c0-6-4-9-8-13z"></path><path d="M769 295c2-1 2-2 4-2h2c5 1 9 3 14 5 2 1 4 4 6 6 4 4 8 9 11 15 2 4 3 8 5 13h2l2 6 2 3v8l1 2 1 1-1 1v1 4c0 2-1 4 0 6 0 3-1 6-1 8v1c-1 3-4 7-5 10 0 1-1 1-1 2h1l-5 2v-1l-1-1v-3l1-2-1-1c2-6 3-11 5-16 0-6 1-12 0-18 0-8-4-20-9-27-8-13-18-20-33-23z" class="e"></path><path d="M811 363v-1c2-2 1-5 1-8 1 2 0 4 0 5v3h0v1c0 1 0 0 1 1-1 5-2 12-6 16l-1-1c2-6 3-11 5-16z" class="f"></path><defs><linearGradient id="Ay" x1="804.977" y1="360.348" x2="815.762" y2="360.114" xlink:href="#B"><stop offset="0" stop-color="#5b5a5d"></stop><stop offset="1" stop-color="#807e81"></stop></linearGradient></defs><path fill="url(#Ay)" d="M813 332l2 6 2 3v8l1 2 1 1-1 1v1 4c0 2-1 4 0 6 0 3-1 6-1 8v1c-1 3-4 7-5 10 0 1-1 1-1 2h1l-5 2v-1l-1-1v-3l1-2c4-4 5-11 6-16 1-7 1-17 0-25l-2-7h2z"></path><path d="M815 338l2 3v8l1 2 1 1-1 1v1 4c0 2-1 4 0 6 0 3-1 6-1 8v1c-1 3-4 7-5 10 0 1-1 1-1 2h1l-5 2v-1c1-2 3-3 4-4 3-7 4-14 5-21 0-3 0-6 1-9h0l-1-2c1-3-1-9-1-12z" class="W"></path><defs><linearGradient id="Az" x1="551.817" y1="802.336" x2="592.049" y2="825.961" xlink:href="#B"><stop offset="0" stop-color="#2e2321"></stop><stop offset="1" stop-color="#534f50"></stop></linearGradient></defs><path fill="url(#Az)" d="M650 711l1-1v1c2 0 3 1 5 1l2 2h1v1c-8 12-17 24-25 36l-73 103-21 32c-3 3-6 6-8 10l-6 9-16 24c-1-1-4-6-5-7-1-2-2-4-4-6-1-1-1-2-2-3-3-3-5-6-6-9v-1c3 0 4 1 6 3h1 1l2 2v1c2 0 1 0 3-1 1 1 2 2 2 4l1-3c1 1 1 1 2 0 2-1 3-1 3-3h-2l1-4c-2 0-4-1-5-1v-2h0c0-1 1-3 1-4 1-1 1-1 1-2l2-4 6-9-1-2c0-1 1-2 1-4h0c0-2 2-4 3-6 0-1 1-1 1-2s0-1-1-2c1-1 5-5 5-7 0-3 3-5 3-8 0-2 17-25 20-29l3-4 2-1-1 3 4-1v-1c1 1 2 2 3 2v-2l1-1c2-1 4-3 5-5 0-1 1-2 1-3h0c4-7 9-13 13-19l2-3 1-2 2-2c0-1 1-3 2-3 1-1 2 0 3-1l-1-2 1-1 1-1c6-6 11-15 16-23l2-2h-1c0-1 0-1-1-1 1-4 4-5 5-8l1-1c2-1 2-2 5-1l-2 1 2 2h0l-1-1 3-2v1h2l1 1 1 1 2 1c0-1 0-2 1-4h2c0-1 1-2 2-2 2-1 8-11 10-14l2-4h2c2-1 4-4 5-6z"></path><path d="M574 813v4h-1 0l-1-1 2-3z" class="I"></path><path d="M600 781c1 0 1-1 2-2 0 1 0 2 1 3 0 1 0 1-1 1 0 1 0 0-1 1 0-1 0-2-1-3z" class="K"></path><path d="M553 837c1 0 2-2 3-3h1v2c-2 2-3 4-4 6v-1l-1-3 1-1z" class="D"></path><path d="M650 711l1-1v1c2 0 3 1 5 1l2 2h1v1h-1c-1-1-2-1-3-2h-2l-1-1-4 4c-1 3-3 5-5 7v-1-2c-1 1-1 1-2 1l2-4h2c2-1 4-4 5-6z" class="C"></path><path d="M598 780l2 1h0c1 1 1 2 1 3-1 3-2 6-4 8 0 1-1 1-2 1-1-1-1-2-2-2h2c1-1 2-2 2-4l1-1c-1-1 0-2 0-3l-2-1h1l1-1v-1z" class="l"></path><path d="M570 830c1-1 1-2 2-4 0 4-1 6-3 9-1 1-3 3-4 3h0v-1c-2 1-3 3-5 4-1-1-1-1-2-1v1l-1-1h0s0-1-1 0l-1 2v-1l1-1c0-1 1-2 1-3v-1-2l1-1 2 2h3c2 0 3 0 5-1 1-1 2-2 2-4z" class="H"></path><path d="M531 873c2-2 3-4 5-6l9-12c0-1 1-2 2-4 0 3-2 5-3 7l-7 10c-1 1-2 3-3 4 0 2 1 2-1 3h-1c0 1-1 1-1 2s0 2-1 3l-6 9v-1-1c0-1 1-2 1-3s1-2 1-3l5-8z" class="m"></path><path d="M595 793c1 0 2 0 2-1l2 2c-1 2-3 3-5 4l-1-1v-1l-1 1c-1 1-2 1-3 1l-3 5h0c-3 1-3 4-6 6v-1l-2-1 3-4c1-1 1-2 2-3s1-2 2-3l1-2 1-1 1 1c2-1 4-1 6-2h1z" class="C"></path><path d="M587 794l1 1c2-1 4-1 6-2-1 1-1 2-3 3-1 0-1-1-2 0s-1 1-1 2h-1-1l-1-1 1-2 1-1z" class="D"></path><path d="M585 797l1 1h1 1c-1 2-2 3-3 4s-1 1-1 2c-1 0-2 1-3 0v-1c1-1 1-2 2-3s1-2 2-3z" class="G"></path><path d="M531 872h0c1-4 4-7 7-11 2-3 3-6 6-8 1-1 0-2 0-3 0 0 3-3 4-3 0-2 1-3 2-5h1c0-2 1-3 1-4l1 3v1c-2 3-5 6-6 9-1 2-2 3-2 4l-9 12c-2 2-3 4-5 6v-1z" class="p"></path><path d="M627 749c1 2 3 2 3 3-2 3-3 5-7 7-2 1-4 2-6 2-1 1-1 2-2 3v1c-2 1-3 2-4 3-1 0-1-1-2-1 0 0-1 1-1 2l-1-1 2-2c0-1 1-1 2-2v-2c1-1 2-2 2-3h1c0-1 1-2 2-3l1-1c3 1 4 1 6 0h1l2-2c0-1 0-2 1-3v-1z" class="I"></path><path d="M613 759l1 2c-1 1-1 2-3 3v-2c1-1 2-2 2-3z" class="U"></path><path d="M623 755h1c-1 2-2 2-3 3l-2 1v1h-3c-1 0-2-1-2-1 0-1 1-2 2-3l1-1c3 1 4 1 6 0z" class="D"></path><path d="M641 721c1 0 1 0 2-1v2 1c-2 3-5 6-6 9-2 4-7 9-7 13 0 1-1 2-2 3v-3c0-1-1-2-1-3l-1-1c0-1 0-2 1-4h2c0-1 1-2 2-2 2-1 8-11 10-14z" class="I"></path><path d="M627 737h2c0-1 1-2 2-2-1 2-3 6-4 7l-1-1c0-1 0-2 1-4z" class="b"></path><path d="M531 872v1l-5 8c0 1-1 2-1 3s-1 2-1 3v1 1c-3 4-5 8-7 12-1 2-3 3-3 5h-2l1-4c-2 0-4-1-5-1v-2c0 1 1 1 2 1 1-2 2-3 4-5l7-11c1-1 4-2 5-4 1-3 3-5 5-8z" class="G"></path><path d="M525 884c0 1-1 2-1 3v1 1c-3 4-5 8-7 12-1 2-3 3-3 5h-2l1-4v-1c3-3 4-7 7-10 1-1 2-2 2-4l3-3z" class="r"></path><path d="M583 783l2-2c0-1 1-3 2-3 1-1 2 0 3-1l-1-2 1-1c1 1 2 1 3 1l1 1 1 1c1 1 1 2 3 2v1 1l-1 1h-1l2 1c0 1-1 2 0 3l-1 1c0 2-1 3-2 4h-2c1 0 1 1 2 2h-1c-2 1-4 1-6 2l-1-1v-1c-1-1-2-1-2-3h0c-1-1-1-2-1-3s0-3-1-4z" class="C"></path><path d="M584 787l1-3h1c0 3 2 5 4 6 0 0 1 0 2 1h1c1 0 1 1 2 2h-1c-2 1-4 1-6 2l-1-1v-1c-1-1-2-1-2-3h0c-1-1-1-2-1-3z" class="N"></path><path d="M583 783l2-2c0-1 1-3 2-3 1-1 2 0 3-1l-1-2 1-1c1 1 2 1 3 1l1 1 1 1c1 1 1 2 3 2v1 1l-1 1h-1c0 1-1 1-1 1l-2-1c0 1-1 1-1 2l-2 1-3-3c0 1 0 2-1 2h-1l-1 3c0-1 0-3-1-4z" class="P"></path><path d="M595 783c-1-1-1-1-1-2 1-1 1-2 1-3v-1c1 1 1 2 3 2v1 1l-1 1h-1c0 1-1 1-1 1z" class="E"></path><path d="M587 782c1-2 1-3 3-4 2 0 2 0 3 2v2c0 1-1 1-1 2l-2 1-3-3z" class="h"></path><path d="M616 738l2 2h0l-1-1 3-2v1h2l1 1 1 1 2 1 1 1c0 1 1 2 1 3v3l-1 1v1c-1 1-1 2-1 3l-2 2h-1c-2 1-3 1-6 0-2-1-3-1-4-2l1-2c1-1 0-2 1-3h-1l-1-1v-3-1l1-1c0-1 0-2 1-3h1v-1z" class="l"></path><path d="M615 748c2 0 2 0 2 1v2c0 1 1 1 2 1v1h4v2c-2 1-3 1-6 0-2-1-3-1-4-2l1-2c1-1 0-2 1-3z" class="E"></path><path d="M616 738l2 2h0l-1-1 3-2v1h2l1 1 1 1c0 1 0 2 1 3l-1 1h-3c0 2-1 2-2 4l-2 1c0-1 0-1-2-1h-1l-1-1v-3-1l1-1c0-1 0-2 1-3h1v-1z" class="J"></path><path d="M619 748c-2-1-3-1-4-3v-2c2-1 2-2 4-2 1 1 2 1 2 3s-1 2-2 4z" class="i"></path><path d="M582 785l1-2c1 1 1 3 1 4s0 2 1 3h0c0 2 1 2 2 3v1l-1 1-1 2c-1 1-1 2-2 3s-1 2-2 3l-3 4-4 6-2 3c-1 1-1 3-2 4-2-2-4-3-7-4l-2-1c2-1 4-3 5-5 0-1 1-2 1-3h0c4-7 9-13 13-19l2-3z" class="m"></path><path d="M568 811c0 1 1 2 2 3l-3 2-2-2c1-1 2-2 2-3h1z" class="p"></path><path d="M576 800c1 0 1 1 2 1 0 2-1 3-2 4-1 3-5 6-6 9-1-1-2-2-2-3 3-4 5-8 8-11z" class="I"></path><path d="M582 785l1-2c1 1 1 3 1 4s0 2 1 3h0c0 2 1 2 2 3v1l-1 1-1 2c-1 1-1 2-2 3h-1c-1 1-1 1-1 2l-2 1c0-1 0-1-1-2-1 0-1-1-2-1 2-4 5-7 7-11l-1-1s1 0 1-1 0-1-1-2z" class="p"></path><path d="M578 801c1-1 2-2 2-3l1-1c1-1 1-1 3-2h0 2l-1 2c-1 1-1 2-2 3h-1c-1 1-1 1-1 2l-2 1c0-1 0-1-1-2z" class="U"></path><path d="M613 738c2-1 2-2 5-1l-2 1v1h-1c-1 1-1 2-1 3l-1 1v1 3l1 1h1c-1 1 0 2-1 3l-1 2c1 1 2 1 4 2l-1 1c-1 1-2 2-2 3h-1c0 1-1 2-2 3v2c-1 1-2 1-2 2l-2 2c-1 2-2 3-3 5l-1 1-1 2c-1 2-1 3-1 4l-1 1-2-1v-1c-2 0-2-1-3-2l-1-1-1-1c-1 0-2 0-3-1l1-1c6-6 11-15 16-23l2-2h-1c0-1 0-1-1-1 1-4 4-5 5-8l1-1z" class="I"></path><path d="M603 759c1 1 1 0 2 1-1 1-2 3-2 4h-2c0 2-1 3-2 4s-1 1-1 2c-1 2-2 3-3 4l-1 2-1-1v-1h0c3-3 4-7 6-10 1-2 3-3 4-5z" class="L"></path><path d="M609 748v1c0 4-4 7-6 10-1 2-3 3-4 5-2 3-3 7-6 10h0v1c-1 0-2 0-3-1l1-1c6-6 11-15 16-23l2-2z" class="M"></path><path d="M613 738c2-1 2-2 5-1l-2 1v1h-1c-1 1-1 2-1 3l-1 1v1 3l1 1h1c-1 1 0 2-1 3l-1 2c-2-2-1-2-2-4-1-1-1-1-1-2l1-1v-1h-1c0 2-1 2-2 3 0-1 0-1-1-1 1-4 4-5 5-8l1-1z" class="B"></path><path d="M598 779v-1c1-2 0-3 1-4 2-4 6-7 8-10 1-2 2-3 3-5v-1c1-1 1-2 3-3v1h3c-1 1-2 2-2 3h-1c0 1-1 2-2 3v2c-1 1-2 1-2 2l-2 2c-1 2-2 3-3 5l-1 1-1 2c-1 2-1 3-1 4l-1 1-2-1v-1z" class="r"></path><path d="M547 840c1-2 3-5 6-6v3l-1 1c0 1-1 2-1 4h-1c-1 2-2 3-2 5-1 0-4 3-4 3 0 1 1 2 0 3-3 2-4 5-6 8-3 4-6 7-7 11h0c-2 3-4 5-5 8-1 2-4 3-5 4l-7 11c-2 2-3 3-4 5-1 0-2 0-2-1h0c0-1 1-3 1-4 1-1 1-1 1-2l2-4 6-9-1-2c0-1 1-2 1-4h0c0-2 2-4 3-6l1-1 5-5c2-2 3-6 5-8 0 2 0 2-1 3l1 1 3-3 7-10 3-5h2z" class="U"></path><path d="M545 840h2l-2 3-1 2h-1-1l3-5z" class="I"></path><path d="M532 858l3-3c-1 5-3 8-6 12-1 1-1 2-2 3l-1-1v1l-3 3c2-4 8-10 9-15z" class="C"></path><path d="M527 862c2-2 3-6 5-8 0 2 0 2-1 3l1 1c-1 5-7 11-9 15-2 2-4 4-5 7l-1-2c0-1 1-2 1-4h0c0-2 2-4 3-6l1-1 5-5z" class="P"></path><path d="M521 868l1-1 5-5c-2 5-5 9-9 12 0-2 2-4 3-6z" class="H"></path><path d="M552 816l2-1-1 3 4-1v-1c1 1 2 2 3 2v-2l1-1 2 1c3 1 5 2 7 4h0c2 2 2 3 2 6h0c-1 2-1 3-2 4 0 2-1 3-2 4-2 1-3 1-5 1h-3l-2-2-1 1h-1c-1 1-2 3-3 3v-3c-3 1-5 4-6 6h-2l-3 5-7 10-3 3-1-1c1-1 1-1 1-3-2 2-3 6-5 8l-5 5-1 1c0-1 1-1 1-2s0-1-1-2c1-1 5-5 5-7 0-3 3-5 3-8 0-2 17-25 20-29l3-4z" class="B"></path><path d="M560 818v-2l1-1 2 1c-1 1-1 2-2 2h-1z" class="C"></path><path d="M561 820h2c1 1 2 2 2 3 0 2 0 2-2 3h-1c-2-1-3-2-3-3 1-2 1-2 2-3z" class="h"></path><path d="M553 826c0-1 1-1 2-2l1-1v1h1c0 1 1 1 1 2h1c1 1 2 1 2 2h0 1l1 1c-1 1-1 1-3 1-1 0-1-1-2-2v-1h-1l-1 1 1 4c-1-2-2-3-3-4h-2c1-1 1-1 1-2z" class="E"></path><path d="M553 826l2-1c1 1 1 2 1 3l1 4c-1-2-2-3-3-4h-2c1-1 1-1 1-2z" class="D"></path><path d="M552 816l2-1-1 3 4-1-8 10v-2-2-3l3-4z" class="p"></path><path d="M552 816l2-1-1 3c-1 2-2 3-4 5v-3l3-4z" class="F"></path><path d="M570 820c2 2 2 3 2 6h0c-1 2-1 3-2 4-1 0-2 1-2 1l-1 1c-1-1-3-2-4-3l-1-1h1 2l3-3v-1c1-1 2-2 2-4z" class="v"></path><path d="M552 828h2c1 1 2 2 3 4 0 1 0 1-1 2s-2 3-3 3v-3c-3 1-5 4-6 6h-2c2-4 5-8 7-12z" class="p"></path><path d="M556 828l1-1h1v1c1 1 1 2 2 2 2 0 2 0 3-1 1 1 3 2 4 3l1-1s1-1 2-1c0 2-1 3-2 4-2 1-3 1-5 1h-3l-2-2-1 1h-1c1-1 1-1 1-2l-1-4z" class="N"></path><defs><linearGradient id="BA" x1="538.623" y1="847.137" x2="531.657" y2="841.046" xlink:href="#B"><stop offset="0" stop-color="#352c2b"></stop><stop offset="1" stop-color="#504647"></stop></linearGradient></defs><path fill="url(#BA)" d="M529 849c0-2 17-25 20-29v3 2 2l-1 1c-1 4-4 8-6 11l-3 5c-3 3-5 7-7 10-2 2-3 6-5 8l-5 5-1 1c0-1 1-1 1-2s0-1-1-2c1-1 5-5 5-7 0-3 3-5 3-8z"></path><path d="M542 839l-2 2-1-1c1-2 1-3 3-5s4-5 6-7c-1 4-4 8-6 11z" class="I"></path><path d="M273 168c4 4 7 5 12 6 4 0 6-1 9-3 0 1 0 2 1 3 1 0 2 1 3 1l1-1h7 6 0c2-1 5-1 7-2-1 1-2 2-3 2l2 1c1-1 2-1 4-1h0c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2c1 0 2 1 2 2-1 1-2 1-3 0v1c5 2 10 6 15 10l4 2c1 1 3 2 4 3h-2s-1 0-2-1c-5 0-10-2-14-3h-3c-1 0-1 1-2 1l-1-1v1c1 1 3 2 5 2l2 1c1 0 2 0 3 1l1 1c-1 1-1 1-2 1-2 1-4 2-5 3v2c-4 5-4 8-4 14 2 9 5 17 8 25s4 17 6 25c-2-3-4-7-7-11-1-2-3-5-5-8-1 1-2 1-2 0h-1c-4-3-8-5-12-9l-3-3-1-1h-1v1h-1c-2-1-2-2-3-3-2-1-3-1-4-2v-1c-1-1-2-1-3-3h0v-1l-1-1h1 1l-1-1c-1 0-2-1-3-1h-2v-2l-1-1h-1v-1h-1l-1 1v1-2c0-1 0-1-1-2-1 0-1 0-1-1 1-1 1-2 3-3 1 0 0 0 1-2-1-1-2-1-2-3-1-2-2-3-3-5-2-1-4-1-6 1h0v1c-2 1-3 2-4 4l-2 3c-2 2-3 4-5 6 0 1 0 0-1 1s-1 2-1 4c-1 2-4 5-5 7l-1 1c-2 2-2 4-3 6l-2 2h1 1l1 2h-1c-1 1-1 2-1 3-3 1-3 2-4 4 0 1-1 2-1 2-1 2-2 4-2 6v2c-1 1-2 1-2 2s0 3 1 5c1 1 2 3 3 4l1 2-1 1c-1 0-2-2-3-3l-3-2 1 1-5-1-1 1 2 2c-4-1-4-1-8 1l-1-1c-2 1-4 3-5 5l-5 7v-2-1h-1c1-3 3-6 4-9l1-4 1-3c1-2 2-7 2-10h-1c0-2 0-2-1-3 0-1-1-1-1-2l-1-1-1-1c-5-3-8-11-12-16l-5-8-1-3c0-1 0-2-1-3h0c5-4 10-8 16-9l1-2c2-1 4-2 7-3 2 0 3-1 4-2l3-1c1 0 2-1 3-1 3-1 5-3 8-4 7-3 13-7 20-11l2-2v-1c1-1 1-1 2-1l-1-1h-1v-1l2-1 2-2 2 1c1-1 1-2 1-3-2-4-7-5-11-7 2 0 4 0 6 1l2-2h1c3 1 5 1 8 2l-1-2c0-2-1-2-2-3v-3z" class="o"></path><path d="M312 174v1c-3 2-7 2-10 4l-2-1-1-1 1-1c2 0 4 0 6-1v-1h6z" class="e"></path><path d="M273 168c4 4 7 5 12 6 4 0 6-1 9-3 0 1 0 2 1 3 1 0 2 1 3 1l1-1h7v1c-2 1-4 1-6 1l-1 1 1 1 2 1-2 1h0c-1 0-1 0-2-1h-3c1 0 1 0 2-1l-1-2c-1-1-1 0-1-1-2 0-3-1-4 0-3 0-7 0-9 2-2 0-4 0-6-1l-1-2c0-2-1-2-2-3v-3zm-13 30l1-1 12-3h0v1h2c0 1 1 1 1 2-1 2-5 5-6 6-2 3-2 6-4 9-3 2-5 6-7 9v-3c3-4 7-8 8-12 1-2 1-5 0-6-4-1-10 2-15 4 1-2 1-2 3-3l3-1c1-1 2-1 2-2z" class="f"></path><path d="M268 174c3 1 5 1 8 2 2 1 4 1 6 1h-1c0 1-1 1-1 2l-3 3c-3 3-6 7-10 8v1c-1 0-2 1-3 1-1 1-2 1-3 1l2-2v-1c1-1 1-1 2-1l-1-1h-1v-1l2-1 2-2 2 1c1-1 1-2 1-3-2-4-7-5-11-7 2 0 4 0 6 1l2-2h1z" class="Q"></path><path d="M269 177c4 0 7 0 10 1-2 1-3 3-5 5l-4 4c1-3 3-5 4-8-1-1-3-1-5-2z" class="e"></path><path d="M259 175c2 0 4 0 6 1s3 1 4 1c2 1 4 1 5 2-1 3-3 5-4 8l-3 3v1c-1 0-2 1-3 1-1 1-2 1-3 1l2-2v-1c1-1 1-1 2-1l-1-1h-1v-1l2-1 2-2 2 1c1-1 1-2 1-3-2-4-7-5-11-7z" class="j"></path><path d="M267 184l2 1c-2 2-3 4-6 6v-1c1-1 1-1 2-1l-1-1h-1v-1l2-1 2-2z" class="K"></path><defs><linearGradient id="BB" x1="233.444" y1="201.444" x2="253.843" y2="206.366" xlink:href="#B"><stop offset="0" stop-color="#a2a3a1"></stop><stop offset="1" stop-color="#c4c2c5"></stop></linearGradient></defs><path fill="url(#BB)" d="M261 193c1 0 2 0 3-1 1 0 2-1 3-1l-2 2-1 1c-2 2-5 3-8 5h1 1c1-1 1-1 2-1 0 1-1 1-2 2l-3 1c-2 1-2 1-3 3l-6 2v1c1 1 1 1 2 1l-1 1h0l-1 1h-3-1c-1 0-3 1-5 0-1 1-3 1-4 2-2 0-3 0-4-1l1-2c1 0 2-1 3-1 3-1 5-3 8-4 7-3 13-7 20-11z"></path><path d="M237 210l9-4v1c1 1 1 1 2 1l-1 1h0l-1 1h-3-1c-1 0-3 1-5 0z" class="D"></path><defs><linearGradient id="BC" x1="255.716" y1="201.233" x2="257.513" y2="220.31" xlink:href="#B"><stop offset="0" stop-color="#171717"></stop><stop offset="1" stop-color="#454547"></stop></linearGradient></defs><path fill="url(#BC)" d="M252 204c5-2 11-5 15-4 1 1 1 4 0 6-1 4-5 8-8 12l-5 7h-2c1-2 1-3 2-4l5-7c2-3 3-4 3-7h-2c-1-1-2 0-4 0-1 1-2 1-4 1s-3 1-5 1h0l1-1c-1 0-1 0-2-1v-1l6-2z"></path><path d="M282 212l3 2 5 7c6 7 14 13 19 21 2 2 4 5 5 7 1 1 1 2 2 2 1 2 3 4 4 6l1 1c-1 1-2 1-2 0h-1c-4-3-8-5-12-9l-3-3-1-1h-1v1h-1c-2-1-2-2-3-3-2-1-3-1-4-2v-1c-1-1-2-1-3-3h0v-1l-1-1h1 1l-1-1c-1 0-2-1-3-1h-2v-2l-1-1h-1v-1h-1l-1 1v1-2c0-1 0-1-1-2-1 0-1 0-1-1 1-1 1-2 3-3 1 0 0 0 1-2-1-1-2-1-2-3-1-2-2-3-3-5l1 1c1-2 2-2 3-2z" class="K"></path><path d="M278 213l1 1 3 3c2 2 4 6 3 10 2 2 3 2 5 3l9 6c3 2 5 5 7 8 2 1 5 5 7 5h1c1 1 1 2 2 2l-1 1c-4 0-10-10-14-12l-13-10c-2-1-3-2-4-3h-1 1l-1-3v-1h1l-1-2c-1-1-2-1-2-3-1-2-2-3-3-5z" class="C"></path><path d="M282 217h0c3 2 5 4 6 7 3 5 8 7 12 11l8 7h1c2 2 4 5 5 7h-1c-2 0-5-4-7-5-2-3-4-6-7-8l-9-6c-2-1-3-1-5-3 1-4-1-8-3-10z" class="D"></path><path d="M290 237h0v-1l-1-1h1 1c7 4 13 9 19 15 2 2 4 4 6 5 1 1 2 1 3 1v1h1l1 1c-1 1-2 1-2 0h-1c-4-3-8-5-12-9l-3-3-1-1h-1v1h-1c-2-1-2-2-3-3-2-1-3-1-4-2v-1c-1-1-2-1-3-3z" class="T"></path><path d="M282 212l3 2 5 7c6 7 14 13 19 21h-1l-8-7c-4-4-9-6-12-11-1-3-3-5-6-7h0l-3-3c1-2 2-2 3-2z" class="J"></path><path d="M283 195c2-2 3-3 6-3 0 2-1 4-1 5-1 2-1 4-1 6-1 4 0 6 1 10 2 3 2 4 2 8l-5-7-3-2c-1 0-2 0-3 2l-1-1c-2-1-4-1-6 1h0v1c-2 1-3 2-4 4l-2 3c-2 2-3 4-5 6 0 1 0 0-1 1s-1 2-1 4c-1 2-4 5-5 7l-1 1c-2 2-2 4-3 6l-2 2h1 1l1 2h-1c-1 1-1 2-1 3-3 1-3 2-4 4 0 1-1 2-1 2-1 2-2 4-2 6v2c-1 1-2 1-2 2s0 3 1 5c1 1 2 3 3 4l1 2-1 1c-1 0-2-2-3-3l-3-2 1 1-5-1-1 1 2 2c-4-1-4-1-8 1l-1-1c-2 1-4 3-5 5l-5 7v-2-1h-1c1-3 3-6 4-9l1-4 1-3c1-2 2-7 2-10h-1c0-2 0-2-1-3 0-1-1-1-1-2l-1-1c1 0 2 0 3 1h1v-2c1 1 3 2 4 2 2 0 3-1 4-2 6-6 12-13 16-20 1-1 1-2 2-3l5-8 5-7v3c2-3 4-7 7-9v1l-2 2h1 0 1c2-4 6-6 9-10 1-2 2-4 4-6v-1c1-1 3-3 4-3z" class="Z"></path><path d="M269 213c2-1 4-3 6-3 3-1 5 0 7 2-1 0-2 0-3 2l-1-1c-2-1-4-1-6 1h0l-1 1-2-2z" class="D"></path><path d="M266 212v1l-2 2h1 0 1c-4 5-7 10-12 14 2-3 3-6 5-8 2-3 4-7 7-9zm19 2c0-1-1-2-1-2l-2-4 1-1c1 1 1 2 2 3h1c0 1 1 2 2 2v1c2 3 2 4 2 8l-5-7z" class="d"></path><path d="M279 199v-1c1-1 3-3 4-3h3v1c0 1-1 1-2 2-2 2-3 5-5 7h-1c0-2 0-3 1-4v-2z" class="j"></path><path d="M238 253c0 2 0 5-1 7l-1-1c-1 1-2 2-2 3-2 1-1 0-2 1s-2 2-2 4l-4 7c-1 1-1 2-1 3l-3 3 3-6 3-9c1-2 3-3 4-4 1-3 4-5 6-8z" class="W"></path><path d="M225 277c0-1 0-2 1-3l4-7c0-2 1-3 2-4s0 0 2-1c0-1 1-2 2-3l1 1c-1 3 0 5 0 8l1 6c-1-1-1-1-1-2-1-2-2-2-3-3l-1 1-1 2h0c-3 0-4 1-6 3l-1 1v1z" class="M"></path><path d="M233 270c0-2-1-3 0-5h1l3 3 1 6c-1-1-1-1-1-2-1-2-2-2-3-3l-1 1z" class="O"></path><path d="M233 270l1-1c1 1 2 1 3 3 0 1 0 1 1 2l3 5-3-2 1 1-5-1-1 1 2 2c-4-1-4-1-8 1l-1-1c-2 1-4 3-5 5 0-2 0-3 1-5l3-3v-1l1-1c2-2 3-3 6-3h0l1-2z" class="T"></path><path d="M226 280c2-2 6-4 9-5l3 2 1 1-5-1-1 1 2 2c-4-1-4-1-8 1l-1-1z" class="G"></path><path d="M238 253h0l4-5c1-1 2-2 2-3 0-2 0-2 1-3l1 1s0-1 1-2c1-2 2-3 4-4-5 8-10 16-12 25l3 4v2c-1 1-2 1-2 2s0 3 1 5c1 1 2 3 3 4l1 2-1 1c-1 0-2-2-3-3l-3-5-1-6c0-3-1-5 0-8 1-2 1-5 1-7z" class="Q"></path><path d="M240 270c-1-3-1-6-1-8l3 4v2c-1 1-2 1-2 2z" class="F"></path><defs><linearGradient id="BD" x1="250.127" y1="239.003" x2="253.738" y2="241.199" xlink:href="#B"><stop offset="0" stop-color="#1a1b1b"></stop><stop offset="1" stop-color="#383738"></stop></linearGradient></defs><path fill="url(#BD)" d="M251 237l5-7 13-17 2 2 1-1v1c-2 1-3 2-4 4l-2 3c-2 2-3 4-5 6 0 1 0 0-1 1s-1 2-1 4c-1 2-4 5-5 7l-1 1c-2 2-2 4-3 6l-2 2h1 1l1 2h-1c-1 1-1 2-1 3-3 1-3 2-4 4 0 1-1 2-1 2-1 2-2 4-2 6l-3-4c2-9 7-17 12-25z"></path><path d="M250 249l1 2h-1c-1 1-1 2-1 3-3 1-3 2-4 4 0 1-1 2-1 2-1-1-1-1-1-2 1-2 2-6 4-7 0-1 2-1 3-2z" class="I"></path><defs><linearGradient id="BE" x1="294.833" y1="214.569" x2="323.305" y2="224.448" xlink:href="#B"><stop offset="0" stop-color="#9f9d9c"></stop><stop offset="1" stop-color="#c6c5c4"></stop></linearGradient></defs><path fill="url(#BE)" d="M322 174c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2c1 0 2 1 2 2-1 1-2 1-3 0v1c5 2 10 6 15 10l4 2c1 1 3 2 4 3h-2s-1 0-2-1c-5 0-10-2-14-3h-3c-1 0-1 1-2 1l-1-1v1c1 1 3 2 5 2l2 1c1 0 2 0 3 1l1 1c-1 1-1 1-2 1-2 1-4 2-5 3v2c-4 5-4 8-4 14h0c-1-2-1-4-1-5-1-2-1-3-1-4l1-1h0-2 0c-1 3-4 3-4 6h2l1 1-2 2c1 2 2 2 2 3l-1 2-1-1-1 1h1c1 2 2 4 2 6l1 3c0 3 1 4 1 6v1l2 3h0l-10-12c-2-3-6-7-7-10-2-2-4-4-5-7-3-4-6-10-6-14v-7c0-2 0-3 1-5 2-2 4-5 7-6h3 1l-3-2v-3c2-2 3-3 6-3 1-2 3-2 5-3s4-3 5-4l2 1c1-1 2-1 4-1h0z"></path><path d="M300 200c1 0 1 0 2 1v1 1h-2-1l-1-2 2-1zm6 14c1 0 1 0 2 1-1 1-1 2-2 3h0l-2-3 2-1zm1-14h1l1-1 1 1c0 1 0 1 1 2 1-1 2 0 2-1 1-1 1 0 1-1v-1h0c1 1 1 1 1 2-1 0-1 1-2 1-1 1-2 0-3 0l-1-1h0c-1 1-2 2-3 2h-3l2-2h2v-1z" class="j"></path><path d="M291 207c2-4 2-8 5-11 10-9 22-3 32 2 2 1 4 2 7 2h0l4 2c1 1 3 2 4 3h-2s-1 0-2-1c-5 0-10-2-14-3h-3c-1 0-1 1-2 1l-1-1v-1c-1-1-2-1-3-1h0c-2-1-3-1-6 0 0 0-2-1-2-2l-2 1 1 2v1h-2l-2 2-1 1v-1-1-1c-1-1-1-1-2-1l-2 1v5 1c1 2-2 3 1 5-1 1-1 1-1 2 1 1 2 2 2 3v2c1 2 1 6 1 8 1 1 1 0 1 1-2-2-4-4-5-7-3-4-6-10-6-14z" class="W"></path><path d="M304 196c1 0 3-2 4-1 1 0 2 0 3-1l26 8c-5 0-10-2-14-3h-2c-3-2-6-2-9-2-2 0-2 0-4-1-1 0-1 0-2 1h0l-2-1z" class="a"></path><path d="M291 207c2-4 2-8 5-11 10-9 22-3 32 2 2 1 4 2 7 2h0l4 2h-2l-26-8c-1 1-2 1-3 1-1-1-3 1-4 1h-1c-2 0-5 2-6 4-1 1-1 2-1 3s0 1-1 2l1 1c0 1 0 1-1 2-1 4 4 10 2 13-3-4-6-10-6-14z" class="Y"></path><path d="M322 174c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2c1 0 2 1 2 2-1 1-2 1-3 0v1c5 2 10 6 15 10h0c-3 0-5-1-7-2-10-5-22-11-32-2-3 3-3 7-5 11v-7c0-2 0-3 1-5 2-2 4-5 7-6h3 1l-3-2v-3c2-2 3-3 6-3 1-2 3-2 5-3s4-3 5-4l2 1c1-1 2-1 4-1h0z" class="H"></path><path d="M291 200c0-2 0-3 1-5 2-2 4-5 7-6h3 1l-3-2v-3c2-2 3-3 6-3-2 1-3 2-4 4v1c4 1 7-1 10 0 2 1 5 0 7 0h1l1 1c1 0 2 1 2 2-1 1-2 1-3 0v1c-5-1-9-2-14-2l-1 1c-4 0-9 3-12 7-1 1-1 3-2 4z" class="e"></path><path d="M322 174c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2-1-1h-1c-2 0-5 1-7 0-3-1-6 1-10 0v-1c1-2 2-3 4-4 1-2 3-2 5-3s4-3 5-4l2 1c1-1 2-1 4-1h0z" class="W"></path><path d="M318 175c1-1 2-1 4-1-1 1-2 3-3 4l-1 1c-2 1-3 1-4 2l-1-1c2-1 3-4 5-5z" class="S"></path><path d="M322 174c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2-1-1h-1c-2 0-5 1-7 0-3-1-6 1-10 0v-1c1-2 2-3 4-4 1-2 3-2 5-3h0c-2 4-7 5-10 8 4 0 8-1 11-1h1v-2l4 1c1-2 1-3 2-5v-1c1-1 2-3 3-4h0z" class="n"></path><path d="M322 174c1 1 2 1 2 2-1 1-2 3-3 5 0 2 0 3 1 4l-1 2-1-1c-1-2-1-4-1-7v-1c1-1 2-3 3-4h0z" class="d"></path><path d="M247 209c2 0 3-1 5-1s3 0 4-1c2 0 3-1 4 0h2c0 3-1 4-3 7l-5 7c-1 1-1 2-2 4h2l-5 8c-1 1-1 2-2 3-4 7-10 14-16 20-1 1-2 2-4 2-1 0-3-1-4-2v2h-1c-1-1-2-1-3-1l-1-1c-5-3-8-11-12-16l-5-8-1-3c0-1 0-2-1-3h0c5-4 10-8 16-9l1-2c2-1 4-2 7-3 2 0 3-1 4-2l3-1-1 2c1 1 2 1 4 1 1-1 3-1 4-2 2 1 4 0 5 0h1 3l1-1z" class="T"></path><path d="M237 235c-1-1-2-2-3-2v-2-1l2-2c1 0 1 1 2 1-1 2-1 4-1 6zm2-7l-1-1c0-1 1-2 1-4 0-1-2-2-2-2h-3l1-1 1 1 1-1 1 1c1 0 1-1 1-1-1-2 0-2 0-4h2l1 1h3l-3 3c-1-1-1-1-1-3l-2 2v1 2l1 1-1 1v3 1z" class="M"></path><path d="M254 210l1 1v2l-2 2 1 1c-3-1-5 0-8 0h0v-1l1-1h1 1l-1-1 1-1c2 0 1 0 3-1v1l2-2z" class="S"></path><path d="M231 251s1-1 1-2 0-2 1-3c2-3 3-5 5-7l2-2v-1c1 0 1-1 1-2h0 2c-1 2-2 4-4 5 0 1-1 1-1 3-1 1-2 3-4 5-1 2-2 3-3 4z" class="c"></path><path d="M252 225h2l-5 8c-1 1-1 2-2 3v-1l2-4c-2 0-2 1-3 3l-2 2c-3 1-3 4-6 6 0-2 1-2 1-3 2-1 3-3 4-5 3-2 6-6 9-9z" class="R"></path><defs><linearGradient id="BF" x1="242.389" y1="244.379" x2="237.94" y2="239.018" xlink:href="#B"><stop offset="0" stop-color="#292b2b"></stop><stop offset="1" stop-color="#454446"></stop></linearGradient></defs><path fill="url(#BF)" d="M238 242c3-2 3-5 6-6l2-2c1-2 1-3 3-3l-2 4v1c-4 7-10 14-16 20v-5c1-1 2-2 3-4 2-2 3-4 4-5z"></path><path d="M247 209c2 0 3-1 5-1s3 0 4-1c2 0 3-1 4 0h2c0 3-1 4-3 7v-2c-2 0-3 0-4-1h0l-1-1h-1c-1 1-1 1-2 1h-3c-2 2-5 1-7 3-2 0-2 0-3 1h-2c-1 1-2 1-3 2s-1 2-2 3c-2 0-3 1-4 2l1-2 3-3 4-3c2 0 4-2 5-2 2-1 4-1 6-2l1-1z" class="R"></path><path d="M201 232c0-2 0-3 1-4l1 2c1 3 3 6 5 9 5 6 9 12 15 17v2h-1c-1-1-2-1-3-1l-1-1c-5-3-8-11-12-16l-5-8z" class="e"></path><path d="M246 216c3 0 5-1 8 0h2c-1 2-2 1-2 3-1 1-2 3-3 4v1c-3 4-6 6-10 9h-2l-1 2h-1c0-2 0-4 1-6 1 1 1 2 1 3h1l1 1v-1l-1-1c0-2 0-2-1-3h0v-1-3l1-1-1-1v-2-1l2-2c0 2 0 2 1 3l3-3-1-1h1 1z" class="Y"></path><path d="M242 223v-1c1 1 2 1 3 2v-1l1 1h2c-1 1-3 2-4 3l-2 1c0 1 1 1 1 2-2 0-2-1-2-2 0-2 0-3 1-5z" class="V"></path><path d="M227 210l3-1-1 2c1 1 2 1 4 1 1-1 3-1 4-2 2 1 4 0 5 0h1 3c-2 1-4 1-6 2-1 0-3 2-5 2l-4 3-3 3-1 2c0 1-1 3-1 4h-1l-1-1 1-1-2 1h0v2l-3-1s-1 0-1 1h0-2c-1 0-3 1-4 1 0 0-1 1-2 1s-2 0-3 1h-4-1l-1-2c-1 1-1 2-1 4l-1-3c0-1 0-2-1-3h0c5-4 10-8 16-9l1-2c2-1 4-2 7-3 2 0 3-1 4-2z" class="H"></path><path d="M213 221c4-1 8-4 12-5v1c-3 4-5 5-10 7-2 1-4 1-5 2-2 1-4 2-6 2v-1c2-2 6-4 9-6z" class="E"></path><path d="M227 210l3-1-1 2c1 1 2 1 4 1-2 1-8 3-8 4-4 1-8 4-12 5 1-2 3-4 6-5v-1h-1l-3 2 1-2c2-1 4-2 7-3 2 0 3-1 4-2z" class="Y"></path><path d="M215 217l3-2h1v1c-3 1-5 3-6 5-3 2-7 4-9 6-1 1-1 1-1 3l-1-2c-1 1-1 2-1 4l-1-3c0-1 0-2-1-3h0c5-4 10-8 16-9z" class="V"></path><path d="M210 226c3 0 6-2 9-2 1 0 3-1 4-1 2-2 4-6 6-8 1 0 5-1 6-1l-4 3-3 3-1 2c0 1-1 3-1 4h-1l-1-1 1-1-2 1h0v2l-3-1s-1 0-1 1h0-2c-1 0-3 1-4 1 0 0-1 1-2 1s-2 0-3 1h-4-1c0-2 0-2 1-3v1c2 0 4-1 6-2z" class="I"></path><defs><linearGradient id="BG" x1="651.76" y1="573.388" x2="501.925" y2="313.336" xlink:href="#B"><stop offset="0" stop-color="#cacac8"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#BG)" d="M551 284h1 4l1-1 1 1c2 1 5 1 7 2 3 1 5 1 8 2 9 1 19 0 28 0h45 8c2 0 4-1 6-1l1 1c-5 3-11 6-15 9-14 12-21 27-26 44l-14 57-10 43-7 27c-1 3-2 8-2 11l-5 17h0c0 1-1 2-1 3l-1 1v2c-1 1-1 2-1 3v2 2c-1 1-1 1-1 2v1l-1 2v2c-1 1-1 3-1 4l-1 1v4c-1 0-1 0-1 1h0c-1-3-3-4-5-6h0c-1 0-1 0-2-1-1 0-3-1-4 0h-1c-1 1-2 1-4 0-1-1-1-3 0-5v-1c1-1 1-3 1-4l-2 4h0c0 2-1 2-1 3v1l-1 3v1l-1 6v1c-1 1-1 2-1 3l-1 2v4c-1 1-1 1-1 2s-1 3-1 4 0 2-1 3v3c-1 1-1 2-1 3l-1 1v4 1c-1 1-1 2-1 3-1 2 0 0-1 1v4 1c-1 1-1 1-1 2s-1 3-1 4 0 2-1 3v3c-1 1-1 2-1 3-1 2 0 0-1 1v4 1c-1 1-1 1-1 2s-1 1-1 2v3 1c-1 1-1 2-1 3l-1 2c0 2 0 4-1 5 0 1 0 2-1 3v3c-1 1-1 2-1 3s-1 2-1 3 0 2-1 3v3c-1 1-1 2-1 3s-1 2-1 3 0 2-1 3v1l-1 2v2 2h-1c0 2-1 3-1 5v1 1h-1c0-3 1-6 2-9l10-40 14-58 7-33h0c0-4 1-9 2-13l6-23 17-79c5-21 11-42 13-63 0-1-1-2-1-2-2-1-3-1-4 0l-1-4c0-1 0-4-1-5-1-7-6-12-11-18h-1c-5-5-16-8-22-10l-4-1-1-1h-1l-1-1c0-1 3-2 4-3z"></path><path d="M594 313c1 5 1 11 1 16 0-1-1-2-1-2-2-1-3-1-4 0l-1-4c1-1 1-2 2-3 0-1 1-1 1-2l2-1v-4z" class="O"></path><path d="M576 300c2-2 3-4 5-6 5 3 11 10 13 15v4 4l-2 1c0 1-1 1-1 2-1 1-1 2-2 3 0-1 0-4-1-5-1-7-6-12-11-18h-1z" class="P"></path><defs><linearGradient id="BH" x1="293.991" y1="356.608" x2="209.817" y2="336.776" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2d2d2e"></stop></linearGradient></defs><path fill="url(#BH)" d="M238 277l3 2c1 1 2 3 3 3l1-1 3 2c2 3 6 4 10 5 5 3 11 5 16 8 1 0 5 4 6 4s1 0 2-1c3 4 8 9 10 14l-3-3v1c1 3 5 4 4 8h-1v2 1c0 1 0 2-1 3-1 2-1 4-1 6v2 4l1 1v5 2 8 7c0 2 0 4-1 6v-1l-1 1v1c0 1 0 2 2 3h0v-3l2 2h-1v1c0 2-1 7 0 9s0 6 0 8c0 6 0 13 1 19l-1 12v-3h0c-1 2-1 3-1 4v1c-1-1-1-2-1-3h-1v1c-2-3-4-6-7-7l-3-1c-4-1-8-1-12 1-2 1-5 4-5 7-1 2-1 5 0 7l-2-1c-3-7-9-10-15-15h-1c-9-4-17-13-25-19l-3-3-1-1c-2-3-3-5-4-8-3-6-4-11-5-17h2v-2c-1-3 0-9 0-12l-1-1h-1 0l3-15v-3l1-4v-1-1l1-5h-2v-5-1c-1 0-2 0-3-1l2-5 1-3c2-5 4-9 6-13h1v1 2l5-7c1-2 3-4 5-5l1 1c4-2 4-2 8-1l-2-2 1-1 5 1-1-1z"></path><path d="M232 319h1l1 1h-2v-1z" class="F"></path><path d="M239 322c0-1 0-1-2-2v-1h1 1c1 0 1 0 2-1l1-1 1 1 1-2 1 2c-1 1-1 2-1 4h0c-1 0-2-1-3-1l-2 1z" class="K"></path><path d="M285 315l1-1c2 1 2 1 3 3v3l-3-1-1-1v2c-2-1-2-1-2-2 0-2 0-2 2-3z" class="h"></path><path d="M212 323c1-1 1-3 3-3-4 9-6 17-7 27l-1-1h-1 0l3-15c1-3 2-5 3-8z" class="g"></path><path d="M285 380l-9-28c0-4-1-8-1-12s-1-8 0-12c1-2 2-3 4-4h0c-1 1-1 2-2 3-2 5-1 12 0 16 0 4 0 7 1 11v1c3 7 7 17 7 25h0z" class="S"></path><path d="M268 360h2c1 2 1 15 1 18 0 6 1 15-1 20v-1c1-2 0-7 0-9v-9h-2v2h1c-1 1-1 3-1 5l1 1v1h-1c1 0 1 0 1-1l-1-1-1-9v-12c0-1 0-3 1-5z" class="J"></path><path d="M268 360h2c1 2 1 15 1 18h-3c0-2 0-3 1-4v-4c0-2-1-3-1-4-1-2 0-4 0-6z" class="P"></path><path d="M240 352c1-2 3-3 4-6-2-1-2-3-3-5l1-1h3l4 1 12 11c-4-1-7-6-11-5 1 1 1 0 0 1-4 1-7 1-10 4z" class="G"></path><path d="M249 341c-2-2-7-5-8-7 1-3 1-4 4-6 3 10 12 19 19 25 2 1 6 3 7 5l-10-6-12-11z" class="e"></path><path d="M277 380l1 6 2 5c1 5 0 10 3 15h0c2-2 2-6 2-9 1-2 1-2 1-4v-2-1c0-1 0-2 1-3v3 2c0 1 0 2 1 3v7 2c0 2 0 4 1 6v4l-1 1c-6-7-13-9-20-14h0c3 0 9 3 11 5 1 0 2 1 2 1l1 1v-1l-2-3 1-1c-2-2-1-5-1-7v-1c-1-1-1-3-1-4h0v-2l-1-1v-1l-1-1c1-1 1-1 0-1 0-2-1-3 0-4v-1z" class="N"></path><defs><linearGradient id="BI" x1="231.536" y1="314.058" x2="227.215" y2="297.486" xlink:href="#B"><stop offset="0" stop-color="#7c7a7d"></stop><stop offset="1" stop-color="#9c9b9b"></stop></linearGradient></defs><path fill="url(#BI)" d="M217 312c5-7 12-12 18-16 3-1 7-4 10-3 2 0 3 1 4 2-15 3-25 11-33 24l-1 1c-2 0-2 2-3 3 0-4 3-8 5-11z"></path><path d="M244 282l1-1 3 2c2 3 6 4 10 5 5 3 11 5 16 8 1 0 5 4 6 4s1 0 2-1c3 4 8 9 10 14l-3-3-4-4c-7-5-13-8-21-11l-6-4c-5-2-10-5-14-9z" class="i"></path><path d="M277 380c0-1 0-2-1-2v-3l-1-6c-1-1-1-3 0-4h0c0 1 1 2 1 3 0 0 1 1 2 1 1 2 2 7 2 9 1 3 1 5 1 7 1 2 1 5 2 7 1-2 1-4 2-6 1 2 0 8 0 11s0 7-2 9h0c-3-5-2-10-3-15l-2-5-1-6z" class="R"></path><path d="M276 368s1 1 2 1c1 2 2 7 2 9 1 3 1 5 1 7 1 2 1 5 2 7l-1 3h0l-3-14-1-3-1-3v-2c-1-1-1-2-1-2v-3z" class="J"></path><path d="M278 355l1-1h-1v-1c0-4-1-7 1-11 1 3 0 8 1 11v3c0 1 1 2 1 3 1 1 1 2 2 3 0 1 1 3 0 4v1c0 2 1 4 1 7 1 1 2 3 2 5v1c0-2 1-7 0-8v-7c-1 0-1 0-2-1h1c0-1 0-3 1-4 1-3-1-9 1-12 0-2-1-6 0-7 0-2 0-5 1-6v-1h0c1 1 1 2 1 3l-1 1v20c0 4 1 11 0 14-2 2-1 15-1 18l1 1v4c-1-1-1-2-1-3v-2-3c-1 1-1 2-1 3v1 2c0 2 0 2-1 4 0-3 1-9 0-11h0c-1-2-1-4 0-6h0c0-8-4-18-7-25z" class="B"></path><defs><linearGradient id="BJ" x1="282.349" y1="380.754" x2="298.116" y2="389.312" xlink:href="#B"><stop offset="0" stop-color="#131318"></stop><stop offset="1" stop-color="#31312f"></stop></linearGradient></defs><path fill="url(#BJ)" d="M288 358c1 2 2 6 1 9 0 1 0 2 2 3h0v-3l2 2h-1v1c0 2-1 7 0 9s0 6 0 8c0 6 0 13 1 19l-1 12v-3h0c-1 2-1 3-1 4v1c-1-1-1-2-1-3h-1c0-1-1-2-1-2l1-1v-4c-1-2-1-4-1-6v-2-7-4l-1-1c0-3-1-16 1-18 1-3 0-10 0-14z"></path><path d="M288 402h1 0v-1c1 0 2 1 2 2 1 1 0 1 0 1 0 3 0 6 1 9v2h0c-1 2-1 3-1 4v1c-1-1-1-2-1-3h-1c0-1-1-2-1-2l1-1v-4c-1-2-1-4-1-6v-2z" class="J"></path><path d="M239 322l2-1c1 0 2 1 3 1v1h-3l-3 3h0c-1 1-1 2-1 3l-4 2h0c-2 1-2 0-3 0-1 1-2 4-2 6-1 1-2 2-3 2l-1 2h0l-1-1c-1 1-2 1-2 2v2 1h0v2l-1 1c0 1 0 2-1 3l-1-1c0 1 1 2 1 4l-1-1-1-1h-2v7h1l-1 1h-1v-1c-1 1-1 2-1 3v1c0 3 0 8 2 11 1 3 5 8 4 12-7-7-9-16-11-25v-2c1 3 1 6 2 9h0c0 1 1 2 1 3l3 6c0-1 0-2-1-3v-2c-1-2-2-3-2-4v-2-2-1h-1c0-3 0-4 2-6 0-1 1-3 2-4 0-1-1-3-1-4l1-1c0-1 0-3 1-4 0-1 1-2 1-2 0-2 1-3 1-4s1-1 2-2l-1-1h1c1 1 1 1 1 3 1-1 1-2 2-3h1 0c0-1-1-2-1-2l1-2c-1-2-1-2 0-4h1l2 2v-1c1-1 1-1 0-2 1-1 1-1 1-2 1 0 1 0 1-1h2v1 1c-1 0-1 1-1 2h2c2-2 1-2 2-4h1v1h1v-1l2 1 2-2z" class="F"></path><path d="M238 277l3 2c1 1 2 3 3 3 4 4 9 7 14 9l6 4c-5-1-10 0-15 0-1-1-2-2-4-2-3-1-7 2-10 3-6 4-13 9-18 16-2 3-5 7-5 11-1 3-2 5-3 8v-3l1-4v-1-1l1-5h-2v-5-1c-1 0-2 0-3-1l2-5 1-3c2-5 4-9 6-13h1v1 2l5-7c1-2 3-4 5-5l1 1c4-2 4-2 8-1l-2-2 1-1 5 1-1-1z" class="Y"></path><path d="M217 301h1l2 2-2 1c0 1 0 1-1 2 0-1 0-1-1-1 0-2 1-3 1-4z" class="V"></path><path d="M231 289h2v2 1 3l-1-1c-1 0-2 0-3-1v-1h3c-1-1-1-2-1-3z" class="M"></path><path d="M225 284c1-1 2-1 3-2 1 0 2 0 3-1 2 1 3 2 5 3l2 2c3 2 7 4 10 4 2 1 3 1 4 2-3 0-6 0-9-1h-2-3-2l-1-1 2-1c-1-1-2-2-4-3h-1-2v-1h-2l-2 2c-2 1-4 2-5 5-1 1-2 1-3 2v-1h1c1-4 4-6 6-9z" class="T"></path><path d="M238 277l3 2c1 1 2 3 3 3 4 4 9 7 14 9l-1 1h-5c-1-1-2-1-4-2-3 0-7-2-10-4l-2-2c-2-1-3-2-5-3-1 1-2 1-3 1-1 1-2 1-3 2 1-2 2-2 2-3 4-2 4-2 8-1l-2-2 1-1 5 1-1-1z" class="K"></path><path d="M235 280l-2-2 1-1 5 1c1 2 3 4 5 6 2 1 3 2 4 4v1c-4-1-6-3-9-5l-4-4z" class="D"></path><defs><linearGradient id="BK" x1="214.724" y1="303.857" x2="209.776" y2="290.643" xlink:href="#B"><stop offset="0" stop-color="#929394"></stop><stop offset="1" stop-color="#b4b0b3"></stop></linearGradient></defs><path fill="url(#BK)" d="M221 285c1-2 3-4 5-5l1 1c0 1-1 1-2 3-2 3-5 5-6 9h-1v1 1c0 2-1 3-2 5 0 1-1 2-1 4 1 1 1 1 1 2 1 1 0 1 1 2v4c-2 3-5 7-5 11-1 3-2 5-3 8v-3l1-4v-1-1l1-5h-2v-5-1c-1 0-2 0-3-1l2-5 1-3c2-5 4-9 6-13h1v1 2l5-7z"></path><path d="M209 302c0 2-1 4 0 6v3c-1 0-2 0-3-1l2-5 1-3z" class="a"></path><path d="M213 299c1 1 1 1 0 2 0 2-1 3-1 5-1 4-1 7-1 11h-2v-5-1-3l4-9z" class="F"></path><defs><linearGradient id="BL" x1="218.413" y1="282.885" x2="220.575" y2="296.538" xlink:href="#B"><stop offset="0" stop-color="#343135"></stop><stop offset="1" stop-color="#4b4e4d"></stop></linearGradient></defs><path fill="url(#BL)" d="M221 285c1-2 3-4 5-5l1 1c0 1-1 1-2 3-2 3-5 5-6 9h-1v1 1c-2 2-4 5-4 8v1l-2 2c0-2 1-3 1-5 1-1 1-1 0-2 1-3 2-5 3-7l5-7z"></path><path d="M214 304v-1c0-3 2-6 4-8 0 2-1 3-2 5 0 1-1 2-1 4 1 1 1 1 1 2 1 1 0 1 1 2v4c-2 3-5 7-5 11-1 3-2 5-3 8v-3l1-4v-1-1l1-5c0-4 0-7 1-11l2-2z" class="S"></path><path d="M214 304l-1 7c0 1 0 2-1 3v3 1l-1 1c0 1 0 2-1 3l1-5c0-4 0-7 1-11l2-2z" class="O"></path><defs><linearGradient id="BM" x1="219.718" y1="343.362" x2="240.142" y2="361.845" xlink:href="#B"><stop offset="0" stop-color="#4c4c4e"></stop><stop offset="1" stop-color="#7d7c7d"></stop></linearGradient></defs><path fill="url(#BM)" d="M215 360l1-1h-1v-7h2l1 1 1 1c0-2-1-3-1-4l1 1c1-1 1-2 1-3l1-1v-2h0v-1-2c0-1 1-1 2-2l1 1h0l1-2c1 0 2-1 3-2 0-2 1-5 2-6 1 0 1 1 3 0h0l4-2c0-1 0-2 1-3h0l3-3h3l1 5c-3 2-3 3-4 6 1 2 6 5 8 7l-4-1h-3l-1 1c1 2 1 4 3 5-1 3-3 4-4 6-4 4-5 9-5 14l1 3 1 5v3h-1v1c-1 1-1 4-1 5v1c-2-1-3-1-4-2h0c1 1 1 1 2 3-1 0-1 1-1 1h0l-2-1-2-1h-2l-2-3c0-1 0-1-1-2h0c-1-1-1-2-1-3 0-2-1-2-1-3h-1l-1-1c0 1-1 2 0 3h-1v-3c-1-1-2-2-2-4h0 0l-1-2h-1l1-6z"></path><defs><linearGradient id="BN" x1="223.92" y1="364.982" x2="222.121" y2="373.082" xlink:href="#B"><stop offset="0" stop-color="#6a6969"></stop><stop offset="1" stop-color="#807e7f"></stop></linearGradient></defs><path fill="url(#BN)" d="M219 372c-1-2-2-3-2-5v-1-4h1 0c0 1-1 1 0 3h0v1l3 2v-2l1-1v1c1-1 1 0 1-1l1-1h1v-1l1 1c0 1-1 0-1 2 1 1 2 1 2 2 0 2 1 3 1 4-1 1-1 1-2 1h-1c-1-1-1-1-2-1s-2 1-2 1h-1l-1-1z"></path><path d="M228 372c1 1 1 1 2 1h1v-2c0-2 1-3 3-4h0l1-1h0l1 3 1 5v3h-1v1c-1 1-1 4-1 5v1c-2-1-3-1-4-2h0c1 1 1 1 2 3-1 0-1 1-1 1h0l-2-1-2-1h-2l-2-3c0-1 0-1-1-2h0c-1-1-1-2-1-3 0-2-1-2-1-3 0 0 1-1 2-1s1 0 2 1h1c1 0 1 0 2-1z" class="n"></path><path d="M227 379c1 1 2 1 3 3l-1 1s1 1 1 2l-2-1-1-1 1-1-1-3z" class="W"></path><path d="M228 372c1 1 1 1 2 1h1v-2c0-2 1-3 3-4h0l1-1h0l1 3h-1c-1 0-2 0-2 1-1 1-1 2-1 3l-1 1h0l1 1-1 3c0 1 0 1 1 1v1c-2-1-4-3-6-4l-1 1 2 2 1 3-1 1 1 1h-2l-2-3c0-1 0-1-1-2h0c-1-1-1-2-1-3 0-2-1-2-1-3 0 0 1-1 2-1s1 0 2 1h1c1 0 1 0 2-1z" class="a"></path><defs><linearGradient id="BO" x1="239.784" y1="375.208" x2="258.263" y2="422.183" xlink:href="#B"><stop offset="0" stop-color="#a4a2a2"></stop><stop offset="1" stop-color="#d5d4d3"></stop></linearGradient></defs><path fill="url(#BO)" d="M219 386c1-4-3-9-4-12-2-3-2-8-2-11v-1c0-1 0-2 1-3v1h1l-1 6h1l1 2h0 0c0 2 1 3 2 4v3h1c-1-1 0-2 0-3l1 1h1c0 1 1 1 1 3 0 1 0 2 1 3h0c1 1 1 1 1 2l2 3h2l2 1 2 1h0s0-1 1-1c-1-2-1-2-2-3h0c1 1 2 1 4 2v-1c0-1 0-4 1-5v-1h1v-3c5 11 12 16 22 22 2 1 5 4 8 5h1 0c7 5 14 7 20 14 0 0 1 1 1 2v1c-2-3-4-6-7-7l-3-1c-4-1-8-1-12 1-2 1-5 4-5 7-1 2-1 5 0 7l-2-1c-3-7-9-10-15-15h-1c-9-4-17-13-25-19l-3-3-1-1c-2-3-3-5-4-8-3-6-4-11-5-17h2c2 9 4 18 11 25z"></path><path d="M206 361h2c2 9 4 18 11 25 3 3 7 6 11 9 3 3 7 7 10 8l1 1 3 3h0l-14-10c-1-1-2-3-4-3l-3-3-1-1h-1l6 5 1 1 1 1c1 1 2 1 3 2s1 2 2 2h1c0 1 1 1 2 2l1 1c1 1 1 1 2 1l1 1 1 1 2 1h0v1c-9-4-17-13-25-19l-3-3-1-1c-2-3-3-5-4-8-3-6-4-11-5-17z" class="o"></path><defs><linearGradient id="BP" x1="220.291" y1="387.002" x2="233.709" y2="389.498" xlink:href="#B"><stop offset="0" stop-color="#747375"></stop><stop offset="1" stop-color="#a09e9e"></stop></linearGradient></defs><path fill="url(#BP)" d="M219 386c1-4-3-9-4-12-2-3-2-8-2-11v-1c0-1 0-2 1-3v1h1l-1 6h1l1 2h0 0c0 2 1 3 2 4v3h1c-1-1 0-2 0-3l1 1h1c0 1 1 1 1 3 0 1 0 2 1 3h0c1 1 1 1 1 2l2 3h2l2 1 2 1h0c2 2 2 3 2 5l-1 3v3c1 1 4 3 5 4 1 0 2 1 2 2-3-1-7-5-10-8-4-3-8-6-11-9z"></path><path d="M219 372l1 1h1c0 1 1 1 1 3 0 1 0 2 1 3l-1 1c-1-1-1-2-2-3h0l-1-2c-1-1 0-2 0-3z" class="X"></path><defs><linearGradient id="BQ" x1="408.551" y1="426.905" x2="340.548" y2="448.341" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#2c2d2e"></stop></linearGradient></defs><path fill="url(#BQ)" d="M335 368c-1-4 1-10 2-14h0l1 4v-2c0-1 1-2 0-4v1c0-3 1-5 1-7h0v-2c1 2 1 4 3 5 1 0 2 0 4-1v1h2c1 2 1 2 1 4 0 1 0 1-1 2v1l-2 2c0 2 0 2-1 3v1c2-1 3-1 4-2h1 0c3 1 5-1 8 0h1c2 0 4 2 6 3l2 1h0l4 3 1 1c1 1 2 1 4 2l3 3c1 0 2 1 4 2l2 1h1l3 2c0-1-1-2-1-2l1-1h3c1 1 1 2 2 2l1 4 3 8 3 15 3 10c0 1 1 3 1 5l3 8 2 9 2 10 19 70 3 12-1 1-3 3c-2 0-5-1-7 0l-2 1-2 1v-2l-1-1c0-1 0-2 1-2h-2c0-1-1-2 0-4l2-2c2-1 4-3 5-3-1-1-2-1-3-1l-3 1c2-1 4-3 6-5-1-1-1-1-1-3v-1l-3 2h-5 0l2-1c1-1 3-2 4-3v-1l-2 1c0 1-1 1-2 2 0 0-1 0-1 1l-17 7c-1 0-1 0-2 1h-2c-3 2-8 4-12 4l-1 1-1-1c1-1 0-1 1-2h0l-6 2-3 2c-2-1-3-1-5 0h0l-1-1-1-2-1-2c-1-2-1-4-2-5-1-2-1-2-1-4 1 0 2-1 2-2v-1l-2 1c-2-2-2-3-2-5-1-1-1-2-2-3l-1-3c-1-1-2-3-2-4l-1-1-3-6h0c-1-2-3-6-3-9 1 1 1 2 3 2 1-3-4-10-5-13-1-1-2-1-4-3-9-31-15-65-7-97z"></path><path d="M412 451c0 2 1 2 0 3-1 0-2 1-3 0l-1-1c1 0 3-1 4-2z" class="B"></path><path d="M361 460h1c1 0 1 1 2 1 1 1 1 3 1 4l-5-4 1-1z" class="I"></path><path d="M366 445h1v1h2 1 0l-3 1c-1 1-3 1-4 2-2 0-2 0-4-1 3-1 5-2 7-3z" class="B"></path><path d="M360 505h3l3 3h0l-2 1-2 1c-2-2-2-3-2-5z" class="b"></path><path d="M382 522c5-3 11-5 16-7-1 1-3 2-5 3-3 2-7 3-10 6l-1 1-1-1c1-1 0-1 1-2h0z" class="U"></path><path d="M392 375c1 1 1 2 2 2l1 4c-2 0-4-2-6-3 0-1-1-2-1-2l1-1h3z" class="G"></path><path d="M423 511c1-1 1-1 2 0h0l1 1c0 2 0 3 1 4v1c-1 1-1 2-2 3h-1c-1-1-2-1-3-1l-3 1c2-1 4-3 6-5-1-1-1-1-1-3v-1z" class="N"></path><path d="M404 443c4 1 6 5 8 8-1 1-3 2-4 2-1-1-2-3-3-4-1-2-1-4-1-6z" class="E"></path><path d="M394 481l1-1h1c3-1 4-3 5-5 0-1 0-3-2-4 0-1-1-1-2-1l1-1c1 0 1 0 2 1s2 3 3 4c0 2-1 4-2 6-2 1-5 3-7 3h-4c1 0 0 0 1-1h0 2c1 0 1-1 1-1z" class="L"></path><path d="M355 492h1c1 1 2 1 2 3 1 2 2 3 3 4l1 1c0 1 0 2 1 3v2h0-3c-1-1-1-2-2-3l-1-3c-1-1-2-3-2-4l1-1-1-2z" class="H"></path><path d="M354 486h2 0 1c1 1 3 2 5 2h0l1 2c1 0 2 0 3-1l-1-1c0-2 1-2 2-3v-2c-1-1-1 1-2 1h-2c-1-1-1-1 0-2s2-2 3-2v-1h1v2l1 1c1 0 2 2 2 3v1h-1l-1 1h-1v1 2c-2 0-2 0-4 2-1-1-2-1-3-2l-2-2c-2 0-3-1-4-2z" class="C"></path><path d="M395 426h0c-5 3-11 5-15 8 2 0 5-2 7-2-2 2-5 3-8 4-6 3-12 5-18 7h0c1-1 3-2 4-3 4-2 7-3 11-5l6-3 1-1s1-1 2-1h1c3-2 6-3 9-4z" class="N"></path><path d="M353 469h1c1 1 2 2 3 4h0c2 0 3 1 4 1l1 2c0-1 1 0 1-1s1-1 1-2v-1h1 0c1 3 2 4 3 7h-1c0-1 0-2-1-4h-1 0c-1 1-1 2-2 3-1 0-1 1-1 2h-2-3c-1-2-1-3-2-5s-3-3-2-6z" class="I"></path><path d="M421 528c3-2 5-2 8-2h1l2 1 1 2-3 3c-2 0-5-1-7 0l-2 1-2 1v-2l-1-1c0-1 0-2 1-2l2-1z" class="l"></path><path d="M419 529l2-1c1 0 2 0 3 1v1h-5v-1z" class="B"></path><path d="M355 492h-1c0-2-2-5-1-6h1c1 1 2 2 4 2l2 2c1 1 2 1 3 2 2 1 3 1 3 3v3c-2 1-3 1-5 1-1-1-2-2-3-4 0-2-1-2-2-3h-1z" class="N"></path><path d="M336 421c1-2 1-3 0-4h1v4c0 1 0 1 1 3v3 3c1 1 0 2 1 3v1 2l1 1v1 2 2c1 1 1 1 1 2v1l1 1v2l1 1 3-3h1c2-1 3-2 4-2l4-2c1-1 2-1 3-1l1-1h1l2-1s1-1 2-1 1-1 3-1h1l4-3c2-1 5-2 7-3 1-1 1 0 2 0l1-1h2l1-1h0c3 0 5-2 8-3h1 1c-3 1-6 2-9 4h-1c-1 0-2 1-2 1l-1 1-6 3c-4 2-7 3-11 5-1 1-3 2-4 3h0c-3 2-8 3-12 5-1 0-2 0-3 1-2 0-3 0-4 1l-3-9-3-20z" class="B"></path><defs><linearGradient id="BR" x1="375.831" y1="460.479" x2="400.711" y2="459.435" xlink:href="#B"><stop offset="0" stop-color="#a19fa0"></stop><stop offset="1" stop-color="silver"></stop></linearGradient></defs><path fill="url(#BR)" d="M390 483c-5-2-9-4-12-9-3-6-4-14-2-21h0v-1c2-4 5-8 9-9 5-3 11-3 17-1l2 1c0 2 0 4 1 6-4-3-8-4-13-3-4 1-9 4-11 8-3 4-3 10-1 15 1 4 3 7 7 9 2 2 4 2 7 3 0 0 0 1-1 1h-2 0c-1 1 0 1-1 1z"></path><path d="M335 368h1l1 1c1 0 1 0 2-1v1c-4 14-4 30-3 44v8c1 6 2 13 3 20l3 9 5 18h-1c-1-1-2-1-4-3-9-31-15-65-7-97z" class="W"></path><defs><linearGradient id="BS" x1="454.684" y1="767.946" x2="514.479" y2="507.076" xlink:href="#B"><stop offset="0" stop-color="#827c7c"></stop><stop offset="1" stop-color="#cfcfce"></stop></linearGradient></defs><path fill="url(#BS)" d="M412 446h1c2-1 4 0 6 1 3-1 8-2 11 0h1v-3c-1-1 0-2 0-3l2 7v1c2 3 3 8 4 11l6 24v1c1 2 1 4 2 6l4 18h1c1-1 3-1 5-1 2-1 9-1 12 0h0c3-1 10 1 12-1v-2h0l-1-2v-1c-1-1-1-2-1-3v-2c-1-1-1-1-1-2v-1l-1-2-1-3v-2c0-1-1-2-1-3v-2c0-1-1-2-1-2v-2c0-1-1-2-1-2v-2c0-1-1-2-1-2v-1c0-1-1-2-1-3v-1l52 193c5-12 7-24 10-36l16-68 8-33c1-3 2-6 2-10h0l2-4c0 1 0 3-1 4v1c-1 2-1 4 0 5 2 1 3 1 4 0h1c1-1 3 0 4 0 1 1 1 1 2 1h0c2 2 4 3 5 6h0c0-1 0-1 1-1v-4l1-1c0-1 0-3 1-4v-2l1-2v-1c0-1 0-1 1-2v-2-2c0-1 0-2 1-3v-2l1-1c0-1 1-2 1-3l-30 127-2 8-18 78-7 32c-1 6-3 13-4 19-2 10-4 20-4 31-2-2-2-19-3-24l-43-168-1-4-9-38-2-8h-1l-1-1c-1 1-1 1-1 2-1 1-3 1-5 2h-3c-1-1-1 0-2 0h-1c-1-1-1 0-2 0 0 0 0 1 1 2l-3 4-4-19-1-3c-2-1-4-3-6-4l3-3 1-1-3-12-19-70z"></path><path d="M412 446h1c2-1 4 0 6 1 3-1 8-2 11 0h1v-3c-1-1 0-2 0-3l2 7 17 66 5 19c1 4 2 7 2 11 1 1 1 3 2 5h-1l-1-1c-1 1-1 1-1 2-1 1-3 1-5 2h-3c-1-1-1 0-2 0h-1c-1-1-1 0-2 0 0 0 0 1 1 2l-3 4-4-19-1-3c-2-1-4-3-6-4l3-3 1-1-3-12-19-70z" class="r"></path><path d="M431 516c0-1 0-2 1-4h2 1 1c4 0 6 3 9 0v-1l2-1 1 1v2l1 1 2 11c0 1 1 2 1 3l1 3v2c1 1 1 2 1 3l1 1c0 2 1 3 1 5l1 2c1 1 1 3 2 5h-1l-1-1c-1 1-1 1-1 2-1 1-3 1-5 2h-3c-1-1-1 0-2 0h-1c-1-1-1 0-2 0 0 0 0 1 1 2l-3 4-4-19-1-3c-2-1-4-3-6-4l3-3 1-1-3-12z" class="AE"></path><path d="M434 528l2 8c-2-1-4-3-6-4l3-3 1-1z" class="B"></path><path d="M453 533c1 1 1 2 1 3l1 1c0 2 1 3 1 5l1 2c1 1 1 3 2 5h-1l-1-1c-1 1-1 1-1 2-1 1-3 1-5 2h-3c-1-1-1 0-2 0h-1c-1-1-1 0-2 0 0 0 0 1 1 2l-3 4-4-19c1-1 1-1 1-3v-1h1c1 1 1 2 2 3 1 0 2-2 3-2 0 1 1 1 2 2l2-3c1 0 1 1 2 2 2-1 2-2 3-4z" class="z"></path><defs><linearGradient id="BT" x1="404.727" y1="477.597" x2="525.676" y2="434.68" xlink:href="#B"><stop offset="0" stop-color="#d7d7d6"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#BT)" d="M408 281c3 0 4 0 5-2h14 1l1-1s2 1 3 1l1-1h3v1c1 1 3 1 4 1l2 1 1-1 1 1c5 1 9 0 14 0h3c4 1 8 1 12 1l1 1 6-1c1 1 1 2 1 4-1 0-3 1-5 1l1 1c1 1 2 2 2 3v2s2 1 2 2h1v2l3-1c0 2 0 2-1 3h1l1 1v2l1 1c-1 1-1 1-2 1 0 1 0 2 1 2h2l1-1v-1h0l1 1v-1l1-1 3 6h2l2 2-2 2c-1 1-1 2-3 3l-3 1c1 1 1 1 2 3v2h1 1v1h1l1-1c1 1 1 1 1 2h1 0l2 2h0l2 2v1l4 3h0c2 1 3 1 5 1 2-1 3-1 5-2l-1-1h2 0v2c3-1 4-6 7-8 1 0 1 0 1-1l-1-1c-1-1-1-1-1-2l1 1c0-2 0-3-1-4 0-1 0-2 1-2v-3h0l-1-1 4-8c2-1 2-2 3-4 1 1 1 1 1 2l-1 1v1l1-1c2-2 3-5 3-8h0c1-2 1-3 0-4v-1l3-3 2-2h0l2-1v3c1 1 1 1 3 1h0 3l1 1h1l1 1 4 1c6 2 17 5 22 10h1c5 6 10 11 11 18 1 1 1 4 1 5l1 4c1-1 2-1 4 0 0 0 1 1 1 2-2 21-8 42-13 63l-17 79-6 23c-1 4-2 9-2 13h0l-7 33-14 58-10 40c-1 3-2 6-2 9h1v-1-1c0-2 1-3 1-5h1v-2-2l1-2v-1c1-1 1-2 1-3s1-2 1-3 0-2 1-3v-3c1-1 1-2 1-3s1-2 1-3 0-2 1-3v-3c1-1 1-2 1-3 1-1 1-3 1-5l1-2c0-1 0-2 1-3v-1-3c0-1 1-1 1-2s0-1 1-2v-1-4c1-1 0 1 1-1 0-1 0-2 1-3v-3c1-1 1-2 1-3s1-3 1-4 0-1 1-2v-1-4c1-1 0 1 1-1 0-1 0-2 1-3v-1-4l1-1c0-1 0-2 1-3v-3c1-1 1-2 1-3s1-3 1-4 0-1 1-2v-4l1-2c0-1 0-2 1-3v-1l1-6v-1l1-3v-1c0-1 1-1 1-3 0 4-1 7-2 10l-8 33-16 68c-3 12-5 24-10 36l-52-193v1c0 1 1 2 1 3v1s1 1 1 2v2s1 1 1 2v2s1 1 1 2v2c0 1 1 2 1 3v2l1 3 1 2v1c0 1 0 1 1 2v2c0 1 0 2 1 3v1l1 2h0v2c-2 2-9 0-12 1h0c-3-1-10-1-12 0-2 0-4 0-5 1h-1l-4-18c-1-2-1-4-2-6v-1l-6-24c-1-3-2-8-4-11v-1l-2-7c0 1-1 2 0 3v3h-1c-3-2-8-1-11 0-2-1-4-2-6-1h-1l-2-10-2-9-3-8c0-2-1-4-1-5l-3-10-3-15-3-8-1-4-4-15-2-9-1-3-1-5-1-2-1-3-1-4-4-10c-2-3-4-7-6-11l-9-11h0l3 2c1-1 1-1 1-2v-3h0v-2c0-2-1-2-2-3h-2c0-1-1-2-2-2s0 1-1 0v-1c-9-6-21-7-31-11l48-1 8 1 15-1c2 0 5 1 7 0z"></path><path d="M522 613c0 2 1 4 1 6l-1 6s-1-1-1-2c-1-3-1-7 1-10z" class="k"></path><path d="M438 302c3-3 7-6 11-8l-1 1c0 1-1 1-2 2 0 2 1 3 1 4h0-2c-3 0-3 0-5 1h-2z" class="X"></path><path d="M408 281c3 0 4 0 5-2h14 1l1-1s2 1 3 1l1-1h3v1c1 1 3 1 4 1l2 1h0-34z" class="G"></path><path d="M525 621c1-3 2-9 5-11h1l-1 2 2-1-9 33-1-19 1-6h0l1-1v3h1 0z" class="M"></path><path d="M525 621c1-3 2-9 5-11h1l-1 2c0 3-4 22-5 24-2-2 0-12 0-14v-1z" class="n"></path><path d="M361 293c4 1 7 4 10 6 12 9 22 19 28 32l1 1c-1 0-2-1-3-2h0l-7-11c-4-5-8-10-14-14l-3 10-9-11h0l3 2c1-1 1-1 1-2v-3h0v-2c0-2-1-2-2-3h-2c0-1-1-2-2-2s0 1-1 0v-1z" class="H"></path><path d="M438 302h2c2-1 2-1 5-1h2l3 3-3 5c-1 3-2 5-2 8-3 0-8 0-12-1v-1c0-5 2-9 5-13z" class="x"></path><path d="M445 301h2l3 3-3 5c-1-1-1-2-1-2 0-3 0-4-1-6z" class="O"></path><defs><linearGradient id="BU" x1="382.789" y1="324.609" x2="383.255" y2="310.558" xlink:href="#B"><stop offset="0" stop-color="#686769"></stop><stop offset="1" stop-color="#999898"></stop></linearGradient></defs><path fill="url(#BU)" d="M373 315l3-10c6 4 10 9 14 14 0 2 0 3-2 3-3 2-7 1-9 4-2-3-4-7-6-11z"></path><path d="M480 282c1 1 1 2 1 4-1 0-3 1-5 1l-2 2c-2 1-4 1-6 3-6 2-13 7-18 12l-3-3h0c0-1-1-2-1-4 1-1 2-1 2-2l1-1c1-1 4-2 5-3 6-4 13-6 20-8l6-1z" class="M"></path><path d="M418 392l13 49c0 1-1 2 0 3v3h-1c-3-2-8-1-11 0-2-1-4-2-6-1h-1l-2-10h1c4-1 9-1 14-1h0c0-1 0-1 1-2l2 2h0l-1-5-1-5-7-27c-1-2-2-5-1-6z" class="AE"></path><path d="M405 419c3 0 6 1 8 1h6l1 2h1 1l1-3 2 6h1l1 5 1 5h0l-2-2c-1 1-1 1-1 2h0c-5 0-10 0-14 1h-1l-2-9-3-8z" class="z"></path><path d="M405 419c3 0 6 1 8 1h6l1 2h1 1l1-3 2 6h1l1 5-1 1v-1c-1-1-1-1-1-2l-1 1c-1 0-1-1-1-2-1 0-2-1-2-1-1 1-1 1-2 1v1l-6-2c-2 1-3 1-5 1l-3-8z" class="x"></path><path d="M390 319l7 11h0c1 1 2 2 3 2l1 2v1l2 3h-1l-1-1v1 1 2c-4 3-10 4-15 4l-1-2-1-3-1-4-4-10c2-3 6-2 9-4 2 0 2-1 2-3z" class="z"></path><path d="M401 334v1c0 1 0 1-1 2v1l-8 3c-3 0-5 0-7 2l-1-3c4-3 11-4 15-5l2-1z" class="AE"></path><path d="M397 330c1 1 2 2 3 2l1 2-2 1c-4 1-11 2-15 5l-1-4c3-3 8-2 12-4 1 0 2-2 2-2z" class="r"></path><defs><linearGradient id="BV" x1="390.015" y1="333.063" x2="386.269" y2="322.315" xlink:href="#B"><stop offset="0" stop-color="#493e3e"></stop><stop offset="1" stop-color="#656667"></stop></linearGradient></defs><path fill="url(#BV)" d="M390 319l7 11h0s-1 2-2 2c-4 2-9 1-12 4l-4-10c2-3 6-2 9-4 2 0 2-1 2-3z"></path><path d="M476 287l1 1c1 1 2 2 2 3v2s2 1 2 2h1v2l3-1c0 2 0 2-1 3h1l1 1v2l1 1c-1 1-1 1-2 1 0 1 0 2 1 2h2l1-1v-1h0l1 1v-1l1-1 3 6h2l2 2-2 2c-1 1-1 2-3 3l-3 1-18 7c-4 1-9 4-13 5l-9 3h-1l-2-5-2-10c0-3 1-5 2-8l3-5c5-5 12-10 18-12 2-2 4-2 6-3l2-2z" class="J"></path><path d="M450 332v-2c4-2 7-4 11-5l-3 3h0 1v1l-9 3z" class="n"></path><path d="M489 305l2 2-1 1s-1 0-2 1v1l-2-1v1c-2 2-3 2-5 3-1 0-3 0-4 1l-2-1 1-1h2l1-1v1l1-1 2-2c0 1 1 1 1 1l1-1-1-1 1-1h1l1-1h2l1-1z" class="F"></path><path d="M477 314c-3 2-11 5-15 6-1 0-2 0-3 1h-3c0-1-1-1-2-2-1 0 0 0-1-1l1-1v-3h1c1-2 2-3 3-4l3-1c0 3 0 3 1 5 2 2 3 1 5 1h0 2c0-1 0 0 1-1h0c2 0 4-1 5-1l2 1z" class="C"></path><path d="M462 314c2 2 3 1 5 1-2 2-4 2-7 3-1 0-2-1-2-2v-1l2 1 1-1 1-1z" class="I"></path><defs><linearGradient id="BW" x1="475.692" y1="324.603" x2="474.308" y2="317.397" xlink:href="#B"><stop offset="0" stop-color="#b3b2b5"></stop><stop offset="1" stop-color="#e7e7e5"></stop></linearGradient></defs><path fill="url(#BW)" d="M461 325c7-3 28-13 35-12-1 1-1 2-3 3l-3 1-18 7c-4 1-9 4-13 5v-1h-1 0l3-3z"></path><path d="M468 292h1c1 0 2 0 3-1h1l1 1-1 1v2h-1c-1 1-2 1-2 2-1 2-3 3-4 4-2 1-2 2-3 3s-2 1-3 2-3 2-4 3l-6 6c-1 4 1 10-3 12l-2-10c0-3 1-5 2-8l3-5c5-5 12-10 18-12z" class="q"></path><path d="M476 287l1 1c1 1 2 2 2 3v2s2 1 2 2h1v2l3-1c0 2 0 2-1 3h1l1 1v2l1 1c-1 1-1 1-2 1 0 1 0 2 1 2l-1 1h-1l-1 1 1 1-1 1s-1 0-1-1l-2 2-1 1v-1l-1 1h-2l-1 1c-1 0-3 1-5 1h0c-1 1-1 0-1 1h-2 0c-2 0-3 1-5-1-1-2-1-2-1-5h0c2-1 2-1 2-3 1 0 2-1 3-1 0-1 0-2 1-2v1h1v-2c1-1 2-1 3-2 2-2 3-2 3-4l1-1h1v-1c-1-2-2-3-2-5h0l2-2z" class="L"></path><path d="M476 299c-2 1-2 2-4 3v1l-1 1-2 2c-1 0-2 1-2 3-1 0 0 0-1 1v1l-1 1c1 1 2 1 3 1l1 1 1-1c1 1 1 0 0 1h0c-1 1-1 0-1 1h-2 0c-2 0-3 1-5-1-1-2-1-2-1-5h0l2 1 1-1c1-2 1-2 2-3h1v-1c1 0 2 0 2-1 1-1 1-1 1-2h1l1-1c1-1 2-2 4-2z" class="F"></path><path d="M476 287l1 1v1 2c1 2 0 3 1 5h-1c-1 1-1 2-1 3-2 0-3 1-4 2l-1 1h-1c0 1 0 1-1 2 0 1-1 1-2 1v1h-1c-1 1-1 1-2 3l-1 1-2-1c2-1 2-1 2-3 1 0 2-1 3-1 0-1 0-2 1-2v1h1v-2c1-1 2-1 3-2 2-2 3-2 3-4l1-1h1v-1c-1-2-2-3-2-5h0l2-2z" class="I"></path><path d="M480 311h0l-1-2-2 2h0-5c-1-1-3-2-3-3l3-3h0 2c1-4 3-5 5-8v-2h2v1s-1 1-1 2 0 1-2 3c-1 0-1 1-2 2h-1 1l-1 2c0 1 2 3 3 4l1-1v-1l1-1v1c1 0 1 0 2 1h0l1-1-2-1 1-1 2 2-1 1 1 1-1 1s-1 0-1-1l-2 2z" class="S"></path><path d="M481 295h1v2l3-1c0 2 0 2-1 3h1l1 1v2l1 1c-1 1-1 1-2 1 0 1 0 2 1 2l-1 1h-1l-2-2-1 1 2 1-1 1h0c-1-1-1-1-2-1v-1l-1 1v1l-1 1c-1-1-3-3-3-4l1-2h-1 1c1-1 1-2 2-2 2-2 2-2 2-3s1-2 1-2v-1h0z" class="M"></path><path fill="#dba871" d="M386 345c5 0 11-1 15-4v-2-1-1l1 1h1c3 9 7 20 8 30 2 4 3 10 5 15 0 3 1 6 2 9-1 1 0 4 1 6l7 27h-1l-2-6-1 3h-1-1l-1-2h-6c-2 0-5-1-8-1 0-2-1-4-1-5l-3-10-3-15-3-8-1-4-4-15-2-9-1-3-1-5z"></path><path d="M388 353c1 0 2-1 3-1 4 0 8-3 12-4l1 1v-3c-1-1 0-1 0-2 0 1 1 2 1 4l-1 1 1 1c1 3 2 8 2 11-1-3-1-6-3-8-1 0-1 1-2 1-1 1-1 1-3 1-1 0-2 1-4 2-1 0-1-1-2 0l-3 5-2-9z" class="AH"></path><path d="M386 345c5 0 11-1 15-4v-2-1-1l1 1h1c3 9 7 20 8 30-1-3-2-6-2-8l-4-12c0-2-1-3-1-4 0 1-1 1 0 2v3l-1-1c-4 1-8 4-12 4-1 0-2 1-3 1l-1-3-1-5z" class="y"></path><path d="M387 350v-1c2 0 3-1 4-1 4 0 7-2 11-3h0c1-1 0-2 0-3v-1-1c1 1 1 2 2 4 0 1-1 1 0 2v3l-1-1c-4 1-8 4-12 4-1 0-2 1-3 1l-1-3z" class="AF"></path><path d="M398 389c1 0 2 0 4 1l1-2 1 1h2c1 1 1 1 3 1l1-1c2 1 4 3 5 6h0c-1-4-2-7-2-10h1 0l5 18 2 10 2 6-1 3h-1-1l-1-2h-6c-2 0-5-1-8-1 0-2-1-4-1-5l-3-10-3-15z" class="AH"></path><path d="M419 403l2 10 2 6-1 3h-1-1l-1-2h-6c-2 0-5-1-8-1 0-2-1-4-1-5l-3-10c2 0 3 0 4 1v1c1-1 1-1 1-2h1 1c2 0 5 1 7 1l4 4v-2c-1-1-1-2 0-4h0z" class="AF"></path><path d="M404 414c4-2 10 3 14 0 1 1 2 2 3 4v-1h0v-4l2 6-1 3h-1-1l-1-2h-6c-2 0-5-1-8-1 0-2-1-4-1-5z" class="y"></path><path d="M490 317c1 1 1 1 2 3v2h1 1v1h1l1-1c1 1 1 1 1 2h1 0l2 2h0l2 2v1l4 3h0l-1 1c0 1 1 2 2 3l-1 1-1-1-1 1c0 3-2 3-1 6 1 0 3 1 3 2 1 1 1 4 1 6s0 5 1 8v12 4c0 3 1 7-1 9v1c0 3 0 6-2 9-1 0-1 0-2 1h0c0 2-1 4-3 5l-1 1c0-1-1-2-2-2s-2-1-4-1c-7 2-14 3-21 6-2 1-2 1-3 3l-2-7-8-30c-3-7-5-16-6-23l-4-15h1l9-3c4-1 9-4 13-5l18-7z" class="P"></path><path d="M489 371c1 2 4 4 4 6h0-2l-1 1-1-1 1-1-1-2v-3z" class="G"></path><path d="M508 375c0 3 1 7-1 9h0l-1 1c-2 3-4 5-7 6 0-1 1-1 2-2l1-1h-2l-4-1 3-2c4-3 8-5 9-10zm-24-18l4 4c1 1 3 2 4 4l1 1c4 4 7 7 10 12 0 1 0 1-1 1l-20-19c1-1 1-2 2-3z" class="k"></path><defs><linearGradient id="BX" x1="471.814" y1="392.756" x2="501.703" y2="391.043" xlink:href="#B"><stop offset="0" stop-color="#b2b1b2"></stop><stop offset="1" stop-color="#e6e7e6"></stop></linearGradient></defs><path fill="url(#BX)" d="M471 396l25-9 4 1h2l-1 1c-1 1-2 1-2 2-8 4-17 5-26 7 0-1-1-2-2-2z"></path><path d="M493 377l1 1c1 1 1 1 3 1 1 1 1 2 0 3 0 1-2 2-4 2h-1l-7 3c-2 0-4 2-7 2-2 0-5 0-7-1v-1c-1-1-1-2-2-3l3 2c1 0 3 0 4-1l1-1c1-1 1-1 3-1 1-1 3-1 4-2 1 0 2-1 2-2l1 1c1 0 4-2 6-3h0z" class="D"></path><path d="M484 383h3 0c2-2 7-2 9-2h1c-2 2-3 2-5 3l-7 3c-1 0-1 0-2-1h0-3l-1-1 3-1h2v-1z" class="F"></path><path d="M476 385l1-1c1-1 1-1 3-1 1-1 3-1 4-2-1 1-1 2-2 2h2 0 0v1h-2l-3 1 1 1h3 0c1 1 1 1 2 1-2 0-4 2-7 2-2 0-5 0-7-1v-1c-1-1-1-2-2-3l3 2c1 0 3 0 4-1z" class="C"></path><defs><linearGradient id="BY" x1="454.443" y1="354.114" x2="463.639" y2="350.95" xlink:href="#B"><stop offset="0" stop-color="#141415"></stop><stop offset="1" stop-color="#303030"></stop></linearGradient></defs><path fill="url(#BY)" d="M453 347v-3l1 1h1l1-1c1 1 1 1 2 0h2v2l-2-1v1 1c2 1 2 2 2 3 1 1 1 1 2 1s2 1 3 0h0 3l1-1v1h3l1 1c-1 0-2 1-4 1l3 3c-3 1-6 1-9 4l-2 1-1-1-1 2h0l1 1 1-1c0 1 0 1-1 2s-1 1-1 2v1 3c-3-7-5-16-6-23z"></path><path d="M469 384h0c-1-2-3-5-3-7l1-1c-1 0-1-1-1-2s0-2-1-2v-3-2c-1-1-1-1 0-2 1-2 4-4 7-4h4c2 0 4 2 5 4l8 6v3l-2-1-2-2c-1-1-2-1-2-2-1-1-1 0-2-1h-1v-1c-3-2-4-3-8-2l2 2c1-1 0-1 2 0h0 1l-1 1h0c0 1-1 2-2 3h-1c0 3 2 1-1 4l-1 1c-1 1-1 1-1 2l2 2-1 1-1-1v1l3 3c1 0 2 1 3 1-1 1-3 1-4 1l-3-2z" class="I"></path><path d="M472 375c0-1-1-1-1-1v-1-1l-2 2v-1-3c0-3 0-3 1-5l1 1 1-1 2 2c1-1 0-1 2 0h0 1l-1 1h0c0 1-1 2-2 3h-1c0 3 2 1-1 4z" class="F"></path><path d="M472 365c4-1 5 0 8 2v1h1c1 1 1 0 2 1 0 1 1 1 2 2l2 2 2 1 1 2-1 1 1 1 1-1h2c-2 1-5 3-6 3l-1-1c0 1-1 2-2 2-1 1-3 1-4 2-2 0-2 0-3 1l-1 1c-1 0-2-1-3-1l-3-3v-1l1 1 1-1-2-2c0-1 0-1 1-2l1-1c3-3 1-1 1-4h1c1-1 2-2 2-3h0l1-1h-1 0c-2-1-1-1-2 0l-2-2z" class="L"></path><path d="M474 377v-2l1-1-1-1c1-1 2-1 3-2 0 1 0 1 1 1l2 2c1 0 1 1 2 0h0c1-2 2-1 4-1l1 1c-1 1-1 1-2 1l-1 1c-1-1-2-1-3-1l-2 1h0l-1 1h-1-1-2z" class="T"></path><path d="M478 372l2 2-2 2c-1-1-2-1-2-2l2-2z" class="R"></path><path d="M487 373l2 1 1 2-1 1 1 1 1-1h2c-2 1-5 3-6 3l-1-1c0 1-1 2-2 2-1 1-3 1-4 2-2 0-2 0-3 1l-1 1c-1 0-2-1-3-1l-3-3v-1l1 1 1-1-2-2c0-1 0-1 1-2l2 2 1-1h2 1 1l1-1h0c1 1 2 2 4 2v1c2-2 3-2 5-3l-1-3z" class="F"></path><path d="M499 391c3-1 5-3 7-6l1-1h0v1c0 3 0 6-2 9-1 0-1 0-2 1h0c0 2-1 4-3 5l-1 1c0-1-1-2-2-2s-2-1-4-1c-7 2-14 3-21 6-2 1-2 1-3 3l-2-7h0v-2c1-1 3-2 4-2s2 1 2 2c9-2 18-3 26-7z" class="q"></path><path d="M467 400v-2c1-1 3-2 4-2s2 1 2 2c-2 1-4 2-6 2z" class="W"></path><path d="M503 395c0 2-1 4-3 5l-1 1c0-1-1-2-2-2s-2-1-4-1c4-1 7-1 10-3z" class="I"></path><path d="M500 342h0c0 2 0 3 1 5l-2 4 1 1 3-9c1 0 3 1 3 2 1 1 1 4 1 6s0 5 1 8v12c-1 1-1 2-1 4 0 1 0 1-1 2v1s-1 1-1 2v-7l-1 1c0 2 0 4-1 5s0 1-1 0c1 0 1 0 1-1-3-5-6-8-10-12l-1-1c-1-2-3-3-4-4l2-3 2-3c1-2 2-3 3-5s2-2 1-4h1c1 2 0 0 1 1v-2c1-1 1-2 2-3z" class="q"></path><path d="M500 359c1-1 1-1 3-1 1 2 1 2 1 4h0c-2-2-2-2-4-3z" class="B"></path><path d="M500 342h0c0 2 0 3 1 5l-2 4 1 1c-2 4-5 9-8 12v1c-1-2-3-3-4-4l2-3 2-3c1-2 2-3 3-5s2-2 1-4h1c1 2 0 0 1 1v-2c1-1 1-2 2-3z" class="E"></path><path d="M499 351l1 1c-2 4-5 9-8 12v-2c-1-4 5-9 7-11z" class="e"></path><path d="M500 359c2 1 2 1 4 3h0v5l1 1s-1 1-1 2c0 0 0 2 1 3l-1 1c0 2 0 4-1 5s0 1-1 0c1 0 1 0 1-1-3-5-6-8-10-12 2 0 2 0 3-1 1-2 3-4 4-6z" class="H"></path><path d="M500 359c2 1 2 1 4 3-1 1-2 1-2 1 0 1 1 1 1 2l-4 2c-1-1-2-1-3-2 1-2 3-4 4-6z" class="C"></path><path d="M479 339v-1c1-1 2-1 3-2h1 1l1 2c2 3 7 7 11 8h0c1 2 0 2-1 4s-2 3-3 5l-2 3-2 3-4-4c-1 1-1 2-2 3-3-2-6-4-10-4l-3-3c2 0 3-1 4-1l-1-1h-3v-1l-1 1h-3c0-1-1-2-1-2 1-1 1-2 1-3h0c0-1 0-2 1-3 1 0 1-1 2-1v-1l2-1c0 1 0 0 1 1v-3l2 1c1 0 1 1 2 1h0c2-1 2-1 4-1h0z" class="S"></path><path d="M480 351l1-1h-1v-4c2 1 0 2 3 2 0 1 0 1-1 2h1v2h-2 0l-1-1zm-1-12v-1c1-1 2-1 3-2h1 1l1 2c2 3 7 7 11 8h0c1 2 0 2-1 4s-2 3-3 5l-2 3c-1 0-2-2-3-2h0c-2-1-2-2-3-3l2-2h1v1c1-1 1-1 3-1l-1 2v2h1l1-1-1-1 1-1c1 0 1-1 2-1h1l-1-1c-1-1 0 0-2-1h0v-2c-1-2-4-6-5-6-1 1-1 0-2 1-1 0-2 0-2 1l-1 2h-1c1-1 1-2 2-3l-1-1c0-1 0-1-1-2h-1z" class="T"></path><path d="M468 341l2-1c0 1 0 0 1 1v-3l2 1c1 0 1 1 2 1h0c2-1 2-1 4-1l-1 2 1 1h-1l-1-1-1 1-1 1h0v3l1 1v1c-1 0-1 1-2 2h3c1 0 1-1 2-1 0 1 0 1 1 2h0l1 1h0 2l1 1c1 1 1 2 3 3h0c1 0 2 2 3 2l-2 3-4-4c-1 1-1 2-2 3-3-2-6-4-10-4l-3-3c2 0 3-1 4-1l-1-1h-3v-1l-1 1h-3c0-1-1-2-1-2 1-1 1-2 1-3h0c0-1 0-2 1-3 1 0 1-1 2-1v-1z" class="R"></path><path d="M473 352c4 1 8 2 11 5-1 1-1 2-2 3-3-2-6-4-10-4l-3-3c2 0 3-1 4-1z" class="f"></path><path d="M468 341l2-1c0 1 0 0 1 1v-3l2 1c1 0 1 1 2 1h0c2-1 2-1 4-1l-1 2 1 1h-1l-1-1-1 1-1 1h0v3l1 1v1c-1 0-1 1-2 2v-1l-1 1c-1-1-3-1-4-3l-1-1c-1 0 0 0-1-1h1c1 0 1 0 2 1l1-1c-1-1 0-1-1-1-1-1-1-2-2-3z" class="c"></path><path d="M473 339c1 0 1 1 2 1h0c2-1 2-1 4-1l-1 2 1 1h-1l-1-1-1 1-1 1h0v3h-1c-1-1-2-1-2-2v-1h2v-1c-1 0-1 0-2-1 0 0 1 0 1-1v-1z" class="O"></path><path d="M490 317c1 1 1 1 2 3v2h1 1v1h1l1-1c1 1 1 1 1 2h1 0l2 2h0l2 2v1l4 3h0l-1 1c0 1 1 2 2 3l-1 1-1-1-1 1c0 3-2 3-1 6l-3 9-1-1 2-4c-1-2-1-3-1-5h0c-1 1-1 2-2 3v2c-1-1 0 1-1-1h-1 0c-4-1-9-5-11-8l-1-2h-1-1c-1 1-2 1-3 2v1h0c-2 0-2 0-4 1h0c-1 0-1-1-2-1l-2-1v3c-1-1-1 0-1-1l-2 1v1c-1 0-1 1-2 1-1 1-1 2-1 3h0c0 1 0 2-1 3 0 0 1 1 1 2h0c-1 1-2 0-3 0s-1 0-2-1c0-1 0-2-2-3v-1-1l2 1v-2h-2c-1 1-1 1-2 0l-1 1h-1l-1-1v3l-4-15h1l9-3c4-1 9-4 13-5l18-7z" class="N"></path><path d="M455 335l3-1v3l-1 1c-1 0-1 0-2-1v-2z" class="b"></path><path d="M455 335v2c1 1 1 1 2 1h0c-2 0-3 1-5 1v-1c0-1 2-2 3-3z" class="M"></path><path d="M456 344c3-3 6-5 10-6h0c-1 1-2 2-3 2-2 1-2 3-3 4h-2c-1 1-1 1-2 0z" class="P"></path><path d="M492 320v2h1 1v1c-1 2-3 4-5 5h-2v1h-1c-2 0-3 0-4-1l4-1c2-1 3-2 3-4 2 0 2 0 3-1v-2zm-8 12l4 2c2 1 5 1 6 4v1h-1 0c-2 0-2 0-3-1h-2l-1 1 1 1c1 0 1 1 2 2h1l1 2c2 1 3-1 4 2h0c-4-1-9-5-11-8v-2l1-1 1-1c-2-1-2-1-3-2z" class="E"></path><path d="M474 327c5-2 10-4 15-4 0 2-1 3-3 4l-4 1-6 2c0-1-1-2-2-3z" class="Z"></path><defs><linearGradient id="BZ" x1="458.076" y1="332.024" x2="475.899" y2="332.476" xlink:href="#B"><stop offset="0" stop-color="#858385"></stop><stop offset="1" stop-color="#b7b8b7"></stop></linearGradient></defs><path fill="url(#BZ)" d="M458 334l16-7c1 1 2 2 2 3l-18 7v-3z"></path><path d="M466 338l1-1c2-1 4-2 6-2 4-2 7-4 11-3 1 1 1 1 3 2l-1 1-1 1v2l-1-2h-1-1c-1 1-2 1-3 2v1h0c-2 0-2 0-4 1h0c-1 0-1-1-2-1l-2-1v3c-1-1-1 0-1-1l-2 1v1c-1 0-1 1-2 1-1 1-1 2-1 3h0c0 1 0 2-1 3 0 0 1 1 1 2h0c-1 1-2 0-3 0s-1 0-2-1c0-1 0-2-2-3v-1-1l2 1v-2c1-1 1-3 3-4 1 0 2-1 3-2h0z" class="G"></path><path d="M475 337c1-2 2-2 3-2h0c1 1 1 0 2 1l2-2 2 2h-1-1c-1 1-2 1-3 2v1h0c-2 0-2 0-4 1h0c-1 0-1-1-2-1v-1c1 0 2-1 2-1z" class="L"></path><path d="M462 351l-1-1v-1-2c0-1 0-2 1-3 2-2 5-5 8-6l1-1c1-1 2-1 2-2l2 2s-1 1-2 1v1l-2-1v3c-1-1-1 0-1-1l-2 1v1c-1 0-1 1-2 1-1 1-1 2-1 3h0c0 1 0 2-1 3 0 0 1 1 1 2h0c-1 1-2 0-3 0z" class="K"></path><path d="M495 323l1-1c1 1 1 1 1 2h1 0l2 2h0l2 2v1l4 3h0l-1 1c0 1 1 2 2 3l-1 1-1-1-1 1c0 3-2 3-1 6l-3 9-1-1 2-4c-1-2-1-3-1-5h0v-1c-1-2-1-3-2-5-2-4-7-6-11-7v-1h2c2-1 4-3 5-5h1z" class="O"></path><path d="M500 326l2 2v1h0c0 2 1 4 0 6h-1c-1-1-2-2-2-4v-1l1-1v-3z" class="H"></path><path d="M495 323l1-1c1 1 1 1 1 2h1 0l2 2h0v3l-1 1v1c-1-1-2-1-4-1v1l-6-3h0c2-1 4-3 5-5h1z" class="R"></path><path d="M495 323l1-1c1 1 1 1 1 2h1 0l2 2h0v3l-1 1c0-1-1-1-2-2l-2-5z" class="E"></path><path d="M539 284l2-1v3c1 1 1 1 3 1h0 3l1 1h1l1 1 4 1c6 2 17 5 22 10h1c5 6 10 11 11 18 1 1 1 4 1 5l1 4c1-1 2-1 4 0 0 0 1 1 1 2-2 21-8 42-13 63l-17 79-6 23c-1 4-2 9-2 13l-16 68-3 13c-1 1-1 4-2 5l-4 18-2 1 1-2h-1c-3 2-4 8-5 11h0-1v-3l-1 1h0c0-2-1-4-1-6l-8-33c-2-4-2-8-3-11-2-6-4-13-5-19l-17-66v-1l-2-7v-1c-2-3-2-6-3-9l-6-23-2-10-6-19-1-5v-2c1-2 1-2 3-3 7-3 14-4 21-6 2 0 3 1 4 1s2 1 2 2l1-1c2-1 3-3 3-5h0c1-1 1-1 2-1 2-3 2-6 2-9v-1c2-2 1-6 1-9v-4-12c-1-3-1-6-1-8s0-5-1-6c0-1-2-2-3-2-1-3 1-3 1-6l1-1 1 1 1-1c-1-1-2-2-2-3l1-1c2 1 3 1 5 1 2-1 3-1 5-2l-1-1h2 0v2c3-1 4-6 7-8 1 0 1 0 1-1l-1-1c-1-1-1-1-1-2l1 1c0-2 0-3-1-4 0-1 0-2 1-2v-3h0l-1-1 4-8c2-1 2-2 3-4 1 1 1 1 1 2l-1 1v1l1-1c2-2 3-5 3-8h0c1-2 1-3 0-4v-1l3-3 2-2h0z" class="N"></path><path d="M562 334l6 2c-1 1-1 2-2 2l-2-1c0-1-1-2-2-3z" class="a"></path><path d="M558 324l6 3v2l-5-2h1 2c-1 0-1-1-1-1-1 0-2 0-3-1v-1z" class="Z"></path><path d="M518 431h3v3c-1 1-1 2-3 3 1-1 1-2 1-3l-1-2h-1l1-1z" class="D"></path><path d="M529 554l3 1c0 1 0 1-1 2l-1 1-3-2h0l2-2z" class="E"></path><path d="M525 560h0c2 0 3 1 5 3 0 1-1 2-1 2-1 1-2 1-2 1-1-1 0-1 0-3h0-1c0-2-1-2-1-3z" class="B"></path><path d="M555 355c1 1 2 1 3 3 0 1 0 1-1 2-2-2-4-3-7-4 2-1 3 0 5-1z" class="S"></path><path d="M521 493c-1-2-1-3-1-5v-1-3l1 1c1 0 2 0 2 1v3c-1 0-2 1-2 1v3z" class="B"></path><path d="M569 376l2-1c2 0 2 1 4 0-1 1-1 2-2 3l-1 1h-1v1c-1 0-1-1-1-2 0 0-1-1-1-2z" class="E"></path><path d="M543 483c2 1 5 2 7 4v1h-1-1c-1 0-3 0-4-1 0-1-1-3-1-4z" class="B"></path><path d="M538 481c2 1 3 2 5 2 0 1 1 3 1 4l-6-2c1-1 1-2 0-4z" class="C"></path><path d="M534 479l4 2c1 2 1 3 0 4l-4-1c-1-1-1-3-2-4l2-1z" class="F"></path><path d="M555 433c4 1 6 5 8 8l1 1c-2-1-3-2-5-4-2-1-5-2-7-3 1-1 2 0 3-2z" class="G"></path><path d="M563 304l-2-1 1-1c2 2 4 3 6 5l3 3-1 1v1h1l1-1v1 1h-3c0-1 1-1 0-2-2-2-5-3-6-6v-1z" class="B"></path><path d="M568 336c3 1 6 2 8 5l-1 1c-3-1-6-2-9-4 1 0 1-1 2-2z" class="X"></path><path d="M521 458l1-1v-2l1-1c2 1 5 0 7 2-2 2-4 3-6 3-1 0-2-1-3-1z" class="n"></path><path d="M564 327c4 1 8 3 11 6-4 0-8-2-11-4v-2z" class="j"></path><path d="M550 404c1 1 5 3 6 3l2 5-9-4 1-4z" class="O"></path><path d="M518 432l1 2c0 1 0 2-1 3 0 2-2 5-3 8-1-3-1-9 0-13h0c1 1 1 1 2 1h0l1-1z" class="I"></path><path d="M551 453v1c0 1 0 3-1 4s-2 1-2 2l-1 1v2c-1 1-2 3-4 3-1-1-2 0-3-1h1 0c2 0 3 0 4-1v-1c0-1 1-2 1-3h1c0-2 0-2 1-3h0c-1 0-2 1-3 2v1l-1-2h0 1v-1-2h3 2l1-2z" class="G"></path><path d="M532 339v-1c1 0 0-1 0-2s0-1 1-2c1 0 2-1 4-2 2 0 4 0 6 1v1l-2-1-3 2c-1 0-2 1-3 2h0c1 2 0 2 0 3h-3v-1z" class="D"></path><path d="M528 506c0 1 1 2 2 4l1 1 2-2v1c1 3 0 3-2 6 0 1 0 1-1 2-1-1-1-1-2-1v-1c-1-1-2-2-3-2v-1l2 1c1 1 2 0 3 0v-1l-2-3c-1-1-1-3 0-4z" class="B"></path><path d="M518 338l2-3h0c2-2 2-3 5-4 1 0 1 0 2 1-3 2-5 5-6 9v1l-1 3-2-3v-4z" class="Q"></path><path d="M556 407c5 1 8 4 12 7v1c-2 1-7-1-10-3l-2-5z" class="B"></path><path d="M550 289l4 1c0 2 0 6 1 8 2 2 5 3 6 6h2v1h-2l-1-1h-1v-1c0-1-2-1-3-1l-1-1c-1-1-1-1 0-2l-1-1c-1-1-2-1-3-2h-1c1-3 1-5 0-7z" class="E"></path><path d="M575 358c2 0 2 0 4 1v1h1v-2 1c-1 2-1 5-1 8v1c0 1 0 1-1 3-1 1-1 4-1 6v1c-1 0-1 1-1 1v-2-1l1-1v-4h-1-1l1-1-1-1v-3-3-1c2 0 2-1 3-1v-2l-3-1z" class="B"></path><path d="M575 363h1 2c0 2 0 3-1 5v2 1h-1-1l1-1-1-1v-3-3z" class="P"></path><path d="M517 330h0v2c3-1 4-6 7-8 1 0 1 0 1-1l-1-1c-1-1-1-1-1-2l1 1c1 0 1 0 2 1v1c1 1 2 3 4 4-1 0-1 0-2 1h6v-1c2 0 3 1 5 1l-7 1c-1 0-4 0-5-1s-1-1-2-1c-2 1-3 3-5 4-1 1-2 1-3 2l-1-1v-1l-1-1h2z" class="R"></path><path d="M527 556c-4-2-8-4-12-7 0-1-1-2 0-4 5 2 10 6 14 9l-2 2z" class="H"></path><path d="M532 488c1-1 3 0 4 0 1 1 2 2 3 2h2v1c0 1 1 1 1 2 1 1 1 2 1 3h1l1 1c-2 2-2 2-2 4v1 2l1 1v1c-1 1-1 1-2 1h-1c1-1 1-1 2-1v-1c-1-2-1-4-1-6h1v-2c-1 0-1 0-2 1v-2c0-1-1-1-1-2s0 0-1-1c-1 0-2 1-3 1v-1c-1-1-2-1-3-2h-1v-1-2z" class="E"></path><path d="M516 332l1 1c1-1 2-1 3-2 2-1 3-3 5-4 1 0 1 0 2 1s4 1 5 1c-1 1-4 3-5 3-1-1-1-1-2-1-3 1-3 2-5 4h0l-2 3h-1-1c-1 0-2-1-3-1 1-2 2-3 3-5z" class="V"></path><path d="M531 518h1 1 0 1c-1 2-1 2-3 3v1h1c0 1-1 2-2 3s0 2-1 4c-1-2 0-2 0-2l-1-1-2 2c0 1-1 2-2 2-2-1-1-3-2-4v-1h1v-1l1-1v-1h1l1-1c1 0 2-1 2-1l1-1c0 1 1 1 1 2 1-1 1-2 1-3z" class="G"></path><path d="M524 470l-7-3c-1-1-2-1-3-3v-2h1v1c8 5 19 7 26 12-1 0-3 0-4-1-2 0-4 1-6 0l-7-4z" class="C"></path><path d="M524 470h5c3 2 6 3 8 4-2 0-4 1-6 0l-7-4z" class="E"></path><path d="M569 313h3c0 1 0 1 1 2 0 1 1 3 1 4 0 0 0 1 1 1v1c-1 1-1 2-2 2s-1 0-1 1h-3c-1-1 0-1-2-1l-1-1 1-1c-1-1-2-1-3-2-1 0-1 0-2 1v-1h2l3-3h1l1-2v-1z" class="H"></path><path d="M567 316h1 2c1 1 1 2 1 3h1v1h-1l-1-1-3 1-1-1 1-3z" class="I"></path><path d="M528 434l2 2c-4 2-9 5-10 10-1 2 0 3 0 5l3 3h0l-1 1v2l-1 1c-1-1-3-3-3-5-2-3-2-6-1-8 3-6 6-8 11-11z" class="T"></path><path d="M560 319l-1 1 1 1h4c1-1 1 0 2 0l-1 1h-1 1l1 1s1 0 2 1h1-1c-1 0-2 0-3-1-1 0-2-1-4-1h-1c-1 0-1-1-3-1-1 0 0 0-1-1h0-2c-1-1-1-1-2-1-1-1-1 0-2-1l-1-1h-1v-1h-3c-1-1 0-1-2-1l-1-1c-1 0-3 0-4-1h0c-2 0-3 0-4-1s-2-2-4-2h-1v-3l1 2h1c1 0 1 1 3 1h1l1 1c2 1 3 1 5 2 1 0 2 1 3 1s1 0 2 1h1 3 0c1-1 4 0 5 0l1 1 2 1h1l-2 1c1 1 2 1 3 1z" class="G"></path><path d="M547 315h3 0c1-1 4 0 5 0l1 1 2 1c-1 0-2 1-4 0h0l-2 1-1-1h-1c-1-1-2-1-3-2z" class="I"></path><path d="M516 476l-1-1v-2l1-1 18 7-2 1c1 1 1 3 2 4l-2-1c-5-2-11-4-16-7z" class="L"></path><path d="M516 476l1-1 3 1c2 0 8 2 10 4 1 0 2 2 2 3-5-2-11-4-16-7z" class="S"></path><defs><linearGradient id="Ba" x1="538.437" y1="410.013" x2="531.748" y2="394.905" xlink:href="#B"><stop offset="0" stop-color="#787979"></stop><stop offset="1" stop-color="#969395"></stop></linearGradient></defs><path fill="url(#Ba)" d="M521 398c10 2 19 3 29 6l-1 4c-4-2-7-2-11-4-3 0-5-1-8-1h-2-1l1-1c-1-1-2-1-3-2h-3c-1-1-1-1-1-2z"></path><path d="M528 444h4v-1c1 0 1 0 2-1l2 2h0l-2 2c-1 0-1 1-2 1h-1l-2 3c0 3 2 4 4 6 1 1 2 1 4 1v3l-1-1v-1h-2l-1-1-1 2v-1-1c-1-1-1-1-2-1h0c-2-2-5-1-7-2h0c1-2 1-4 1-5 1-2 2-4 4-5z" class="D"></path><path d="M512 411c2 1 2 1 2 2 1 1 1 2 1 2 2 3 3 7 4 10 1 2 2 4 2 6h-3l-1 1h1l-1 1h0c-1 0-1 0-2-1h0c-1-2-1-5-1-8v-5l-2 2v-10z" class="F"></path><path d="M519 425c1 2 2 4 2 6h-3l1-1-1-1 1-4z" class="H"></path><path d="M517 400c0-1 1-2 1-3 1 0 3 0 3 1s0 1 1 2h3c1 1 2 1 3 2l-1 1h1 2c1 0 1 0 2 1l1 1h-1v1h-2s-1 0-2 1h-2c-2 1-3 1-5 1v1h0l-4-9z" class="B"></path><path d="M518 397c1 0 3 0 3 1s0 1 1 2h3c1 1 2 1 3 2l-1 1h1c-2 1-5 1-7 0-1-1-3-4-3-6z" class="n"></path><path d="M523 486c2 1 2 1 4 1h1 1c1 0 1 1 2 1h1v2h-1c-1 0-1 0-2-1-1 0-2 0-3 1v2c-1 0-1 1-1 2h2l-1 2v1 1h1l1-2c1 1 1 2 1 3h0c0 1 1 2 1 3h1c0 2 0 2-1 4h0c1 1 1 1 1 2h2c-1 1-2 1-3 2-1-2-2-3-2-4l-2-4c-2-3-4-6-5-9v-3s1-1 2-1v-3z" class="D"></path><path d="M526 502h2l1 2s-1 1 0 2h1 0c1 1 1 1 1 2h2c-1 1-2 1-3 2-1-2-2-3-2-4l-2-4z" class="E"></path><defs><linearGradient id="Bb" x1="550.122" y1="338.063" x2="547.535" y2="321.912" xlink:href="#B"><stop offset="0" stop-color="#999b9a"></stop><stop offset="1" stop-color="#d4d1d3"></stop></linearGradient></defs><path fill="url(#Bb)" d="M534 327l-3-3v-1c3-1 12 3 16 4l15 7c1 1 2 2 2 3-8-3-16-7-25-9-2 0-3-1-5-1z"></path><path d="M528 444l6-3c1-1 1-1 2-1v-1c1-1 3-2 5-2 1-1 3-1 4 0 1 0 1 1 3 1l2 2c1 0 2 1 4 1v1c1 0 2 0 2 1v1h1c1 1 1 2 1 3 0 2-2 4-3 6 0 2-1 3-2 4 0-1 0-1 1-2v-1h0c-1-2-1-2-3-3l-1-1c1-1 1-2 1-4-1-1-1-2-1-3v-1s-1-1-2-1v-1l-2-1c-1 0-1-1-2-2l-1 1c-1 1-1 2-3 2h-2c0 1-1 1-1 2s0 1-1 2h0 0l-2-2c-1 1-1 1-2 1v1h-4z" class="J"></path><defs><linearGradient id="Bc" x1="542.921" y1="323.429" x2="544.211" y2="316.899" xlink:href="#B"><stop offset="0" stop-color="#c4c4c3"></stop><stop offset="1" stop-color="#eee"></stop></linearGradient></defs><path fill="url(#Bc)" d="M524 312c12 3 23 7 34 12v1c1 1 2 1 3 1 0 0 0 1 1 1h-2-1l-15-5c-7-2-14-4-20-7v-3z"></path><path d="M509 546v-2c1 0 2 1 3 1l1 1 1 19c1 4 0 9 1 13l-1 2c-2-4-2-8-3-11-2-6-4-13-5-19 2 2 2 4 2 7h1v-2-3h-1c0-1 1-2 1-2v-3-1z" class="C"></path><path d="M509 546v-2c1 0 2 1 3 1l1 1 1 19c-2-2-2-3-2-4l-3-13v-1-1z" class="R"></path><path d="M551 296c1 1 2 1 3 2l1 1c-1 1-1 1 0 2l1 1c1 0 3 0 3 1v1h1l1 1h2c1 3 4 4 6 6 1 1 0 1 0 2v1l-1 2h-1l-3 3h-2-2c-1 0-2 0-3-1l2-1c0 1 0 0 1 1v-1c1-1 1-2 1-3v-3l-2-1 1-1c-1 0-1 0-2-1l-1-1c-1 0-2-1-3-2-1 0-1-1-1-2l1-1c-1 0-1 0-2-1l1-1c-1-1-1-1-2-1v-3z" class="C"></path><path d="M561 311h0 2c1 2 0 3 2 4v1c1 0 2-1 3-1l1-1-1 2h-1l-3 3h-2-2c-1 0-2 0-3-1l2-1c0 1 0 0 1 1v-1c1-1 1-2 1-3v-3z" class="K"></path><path d="M561 314h2v2l-1 1 2 2h-2-2c-1 0-2 0-3-1l2-1c0 1 0 0 1 1v-1c1-1 1-2 1-3z" class="C"></path><path d="M556 302c1 0 3 0 3 1v1h1l1 1h2c1 3 4 4 6 6 1 1 0 1 0 2v1l-1 1-1-1v-2h0c-1-1-1-1-2-1l-1-2c-1 0-1-1-2-2 0-1 0 0-1-1l-2 1v-2c-1-1-1 0-3-1 1-1 0-1 0-2z" class="D"></path><path d="M563 366v-1-1c-1-1-1-2-2-3l1-2c1 1 0 1 2 1v1 1c2-1 3-1 4 0h1 1 1l2-3 2 1-1 2h0l1 1v3 3l1 1-1 1c0 1 0 1 1 2 0 1 0 1-1 2-2 1-2 0-4 0l-2 1h0c-1-2-2-4-4-5h0v-2h0c-1-1-2-2-2-3z" class="H"></path><path d="M568 362h1l1 1h2v1c-1 0-1 1-2 2v-1c-1-1-1-2-2-3z" class="C"></path><path d="M564 362c2-1 3-1 4 0s1 2 2 3v1l-1 1c-1-1-1-1-2-1l-3-3v-1z" class="K"></path><path d="M565 369c0-2 1-1 0-3h1c1 1 1 1 1 3 0 1 1 2 2 3l1 1h-1v3c-1-2-2-4-4-5h0v-2h0z" class="E"></path><path d="M569 372l1-1c1-1 1-1 2-1h-1l1 1h3c0 1 0 1 1 2 0 1 0 1-1 2-2 1-2 0-4 0l-2 1h0v-3h1l-1-1z" class="D"></path><defs><linearGradient id="Bd" x1="536.651" y1="365.666" x2="530.723" y2="360.823" xlink:href="#B"><stop offset="0" stop-color="#9f9ea0"></stop><stop offset="1" stop-color="#cecdcc"></stop></linearGradient></defs><path fill="url(#Bd)" d="M548 352c1 1 1 1 2 1 2 0 3 1 5 2-2 1-3 0-5 1-5 0-8 0-12 4-7 5-13 11-19 18l-1 1h-2c1-4 6-9 9-12l2-3c7-6 12-11 21-12z"></path><path d="M530 506c1-2 1-2 1-4h-1c0-1-1-2-1-3h0c0-1 0-2-1-3l-1 2h-1v-1-1l1-2h-2c0-1 0-2 1-2v-2c1-1 2-1 3-1 1 1 1 1 2 1h1v1h1c1 1 2 1 3 2v1c1 0 2-1 3-1 1 1 1 0 1 1s1 1 1 2v2 3l-2 2v2 2h0c-1-1-1-1-1-2-1 1-2 1-3 1l-1 1-1 1h-2c0-1 0-1-1-2h0z" class="G"></path><path d="M516 331v1c-1 2-2 3-3 5 1 0 2 1 3 1h1 1v4l2 3-1 2s0-1-1-1l-1-1h-2-1l-1 1h-1c-3 2-3 3-4 7v6c-1-3-1-6-1-8s0-5-1-6c0-1-2-2-3-2-1-3 1-3 1-6l1-1 1 1 1-1c-1-1-2-2-2-3l1-1c2 1 3 1 5 1 2-1 3-1 5-2z" class="Q"></path><path d="M508 344c-1-1 0-1-1-2l3-3 3-2c1 0 2 1 3 1h1 1v4l2 3-1 2s0-1-1-1l-1-1h-2-1-2-3l-1-1z" class="V"></path><path d="M510 342c2 1 4 1 6 2l1-1v2h-2-1-2-3l-1-1c0-1 1-1 2-2z" class="O"></path><path d="M508 344c-1-1 0-1-1-2l3-3 3-2c1 0 2 1 3 1h1 1v4h0c-2 0-2-1-3-2-3 0-3 2-5 2-1 1-2 1-2 2z" class="g"></path><defs><linearGradient id="Be" x1="522.218" y1="353.324" x2="534.755" y2="349.918" xlink:href="#B"><stop offset="0" stop-color="#0d0d0c"></stop><stop offset="1" stop-color="#2f3031"></stop></linearGradient></defs><path fill="url(#Be)" d="M521 342l1 3h1s1-1 2-1c1-1 1 0 2-1h-1l1-1h1 1l1-1v-1l2-1v1 1 3 1c-1 0-1 0-2 1v1c1-1 2-1 3-1l1 1c0 1-1 2-1 2l1 1c0 1-1 2-1 3-1 0 0 0-1-1h-1l-1 1v-1c-1 0-2 1-2 2v2l1 1h2c1-1 3-3 6-3 3-1 5-3 8-3 2 0 2 0 3 1-9 1-14 6-21 12v-1l1-1c0-4-6-11-9-15l1-2 1-3z"></path><path d="M528 354l-2-3c-2-1-2-2-3-4l1-1h3v-1h1 1c0-1 1-1 1-2 1 0 1 0 1 1h1v1c-1 0-1 0-2 1v1c1-1 2-1 3-1l1 1c0 1-1 2-1 2l1 1c0 1-1 2-1 3-1 0 0 0-1-1h-1l-1 1v-1c-1 0-2 1-2 2z" class="O"></path><path d="M530 347c1-1 2-1 3-1l1 1c0 1-1 2-1 2l1 1c0 1-1 2-1 3-1 0 0 0-1-1h-1l-1 1v-1c-1-1-1-1-2-1h-1l1-1c1-1-1 0 1-1v1h1l-1-2 1-1z" class="M"></path><path d="M513 377l1 3c6 6 13 8 21 11l16 6 7 3h-6c-4-2-8-3-12-4-9-1-20-3-26-11h-1 0-1l1-8z" class="o"></path><path d="M555 338l7 3c6 1 13 4 16 9v1c0 1 0 1 1 2h-1c0 1 0 2 1 3-1 0-1 1-1 1l-2 1h-1l3 1v2c-1 0-1 1-3 1v1l-1-1h0l1-2-2-1-2 3h-1-1-1c-1-1-2-1-4 0v-1l1-1c2 0 2-1 3-2h-2v-1c1 0 1-1 2-1 0-2-1-1-2-2l1-2v-1c1-1 0-2 0-3l-2 1v-1-1c-1-1-1-1-2-1l-1-1v-2c-1-2-3-3-5-4-1 0-1 0-2-1z" class="E"></path><path d="M575 351v2h1c1 1 1 2 1 3s-1 1-1 2h-1l3 1v2c-1 0-1 1-3 1v1l-1-1h0l1-2-2-1h0v-2-5c1 0 1-1 2-1z" class="D"></path><path d="M555 338l7 3c1 1 1 2 2 2 2 1 3 1 4 3h1c2 1 4 3 6 5-1 0-1 1-2 1 0-1 0-1-1-1v-1c-2-1-2 0-3 0l-1-1h2v-1h-3l-2 1v-1-1c-1-1-1-1-2-1l-1-1v-2c-1-2-3-3-5-4-1 0-1 0-2-1z" class="H"></path><path d="M567 348h3v1h-2l1 1c1 0 1-1 3 0v1c1 0 1 0 1 1v5 2h0l-2 3h-1-1-1c-1-1-2-1-4 0v-1l1-1c2 0 2-1 3-2h-2v-1c1 0 1-1 2-1 0-2-1-1-2-2l1-2v-1c1-1 0-2 0-3z" class="K"></path><path d="M568 358h1c0 2 0 3-1 4-1-1-2-1-4 0v-1l1-1c2 0 2-1 3-2z" class="C"></path><path d="M555 343h1v1h1 2 2l1 1 1 1c1 0 1 0 2 1v1 1l2-1c0 1 1 2 0 3v1l-1 2c1 1 2 0 2 2-1 0-1 1-2 1v1h2c-1 1-1 2-3 2l-1 1v-1c-2 0-1 0-2-1l-1 2c1 1 1 2 2 3v1 1l-6-6c1-1 1-1 1-2-1-2-2-2-3-3-2-1-3-2-5-2-1 0-1 0-2-1s-1-1-3-1c-3 0-5 2-8 3-3 0-5 2-6 3h-2l-1-1v-2c0-1 1-2 2-2v1l1-1h1c1 1 0 1 1 1s1 0 2-1c2 0 3 0 4-2 1-1 2-1 3-1h0l1-1 1-1c1 0 2-1 3-1 1 1 1 0 1 1s-1 1-1 2l1 1 1-1v1l1 1h1c0-1 1-2 1-2l2-2h-1c0-1 1-3 2-4z" class="L"></path><path d="M530 352v1l1-1h1c1 1 0 1 1 1-1 1-3 2-5 3v-2c0-1 1-2 2-2z" class="S"></path><path d="M558 351l-2-1h1l1 1c0-1 0-1 1-1v2c1 0 2-1 3-2l2-2h1v1l2-1c0 1 1 2 0 3v1l-1 2c1 1 2 0 2 2-1 0-1 1-2 1v1h2c-1 1-1 2-3 2l-1 1v-1c-2 0-1 0-2-1l-1 2c1 1 1 2 2 3v1 1l-6-6c1-1 1-1 1-2-1-2-2-2-3-3-2-1-3-2-5-2l1-1c1 0 2 0 2 1h1c2 0 2 0 4-2z" class="F"></path><path d="M561 353c1 1 2 1 3 1h1v1h-2l-1 1 2 1h1v1l-2 1 1 1c-2 0-1 0-2-1s-1-1-2-1c1-1 1-2 1-3h1l-1-2z" class="U"></path><path d="M558 351v1c0 1 1 2 1 3l-1 1h1c1-1 1-2 1-3h1l1 2h-1c0 1 0 2-1 3 1 0 1 0 2 1l-1 2c1 1 1 2 2 3v1 1l-6-6c1-1 1-1 1-2-1-2-2-2-3-3-2-1-3-2-5-2l1-1c1 0 2 0 2 1h1c2 0 2 0 4-2z" class="I"></path><path d="M536 444h0c1-1 1-1 1-2s1-1 1-2h2c2 0 2-1 3-2l1-1c1 1 1 2 2 2l2 1v1c1 0 2 1 2 1v1c0 1 0 2 1 3 0 2 0 3-1 4 0 1-1 2-2 2l-4 3v1h0l-1 1v-1c-1 1-1 1 0 2 0 1-1 1-1 2l1 2c-1 0-1 0-2 1h-2l1 1v1h-2v-2c0-1 1-2 2-3-1 0-2 0-2-1h0v-1c-1-1-1-1-1-2-1 1 0 0 0 1-2 0-3 0-4-1-2-2-4-3-4-6l2-3h1c1 0 1-1 2-1l2-2z" class="G"></path><path d="M532 447c1 0 1-1 2-1 1 1 3 2 4 3v2c-1 1-1 2-1 3l-1 1h-1v-3h-1c-1-1-2-3-2-5z" class="I"></path><path d="M538 335l3-2 2 1v-1c2 0 3 0 4 1 2 1 3 1 5 2 1 0 2 1 3 2s1 1 2 1c2 1 4 2 5 4v2l-1-1h-2-2-1v-1h-1c-1 1-2 3-2 4h1l-2 2s-1 1-1 2h-1l-1-1v-1l-1 1-1-1c0-1 1-1 1-2s0 0-1-1c-1 0-2 1-3 1l-1 1-1 1h0c-1 0-2 0-3 1-1 2-2 2-4 2-1 1-1 1-2 1 0-1 1-2 1-3l-1-1s1-1 1-2l-1-1c-1 0-2 0-3 1v-1c1-1 1-1 2-1v-1-3-1h3c0-1 1-1 0-3h0c1-1 2-2 3-2z" class="S"></path><path d="M535 340v1c1 0 1 0 1-1 1-1 0-1 0-2l2-1h0c2 1 2 1 4 1v-1l1-1 1 3c2 0 3 0 4 1h1v1l1 1v1l1-1h4v1c-1 1-2 3-2 4h1l-2 2s-1 1-1 2h-1l-1-1v-1l-1 1-1-1c0-1 1-1 1-2s0 0-1-1c1 0 1-1 2-2v-1l-2 1c-1 1-2 1-4 0v-1l2-2h-1l-2 2h-3v-1h2 1v-1l-1-1-1 1-1-1-2 1-1 1h0c-2-1-2-1-4-1v-1h3z" class="T"></path><path d="M538 335l3-2 2 1v-1c2 0 3 0 4 1 2 1 3 1 5 2 1 0 2 1 3 2s1 1 2 1c2 1 4 2 5 4v2l-1-1h-2-2-1v-1h-1v-1h-4l-1 1v-1l-1-1v-1h-1c-1-1-2-1-4-1l-1-3-1 1v1c-2 0-2 0-4-1h0l-2 1c0 1 1 1 0 2 0 1 0 1-1 1v-1c0-1 1-1 0-3h0c1-1 2-2 3-2z" class="F"></path><path d="M538 335l3-2 2 1v-1c2 0 3 0 4 1l2 2v1c1 0 2 1 3 2 2 0 2 0 4 2-2-1-4-1-5-2h-1 0l-1-1c-1 0-1 0-2-1v-1l-3 1c0-1 0-1-1-2v1c-2-1-3-1-5-1z" class="I"></path><path d="M547 334c2 1 3 1 5 2 1 0 2 1 3 2s1 1 2 1c2 1 4 2 5 4v2l-1-1c-2-1-3-2-5-2h-1l1-1c-2-2-2-2-4-2-1-1-2-2-3-2v-1l-2-2z" class="G"></path><defs><linearGradient id="Bf" x1="528.196" y1="390.095" x2="508.066" y2="390.476" xlink:href="#B"><stop offset="0" stop-color="#cbcbcb"></stop><stop offset="1" stop-color="#f1f1f1"></stop></linearGradient></defs><path fill="url(#Bf)" d="M508 359v-6c1-4 1-5 4-7h1c-1 10-2 22 0 31l-1 8h1c0 5 1 10 4 15l4 9h0l3 8 6 14 1 4-1 1-2-2-17-35v-2c-1-1-1-2-2-3 0-2 0-3-1-5l-1-1v-3-1c2-2 1-6 1-9v-4-12z"></path><path d="M514 345h1 2l1 1c1 0 1 1 1 1 3 4 9 11 9 15l-1 1v1l-2 3c-3 3-8 8-9 12 0-1 0-2-1-3 0 2 0 3-1 4l-1-3c-2-9-1-21 0-31l1-1z" class="q"></path><path d="M518 346c1 0 1 1 1 1 3 4 9 11 9 15l-1 1c-4-4-7-11-9-17z" class="d"></path><path d="M515 376v-1c0-2 0-18 1-19h1c2 1 5 6 6 8 0 1 1 2 1 3h1c-3 3-8 8-9 12 0-1 0-2-1-3z" class="C"></path><path d="M532 370c1-2 4-4 5-6 1-1 1-2 2-2h0c3-2 6-3 9-3s5 2 7 3 3 2 4 4l2 1 3 6 1 1c2 3 4 4 5 7 0 1 0 1 1 1-1 1 0 2-1 3l1 1h-1l-2 2-1 1 1 1h-1c-2 2-2 2-3 4l-1-1-3 3v-1c0-1 0-1 1-2h-2-1c-1 1-3 0-4 1-2 1-2 1-3 0h-1c-1-1 0-1-1-1-1-1-2-1-2-2 1 1 4 2 6 2s4 0 6-1h0c1 0 3 1 4 0s3-3 3-4v-1h1l1-1h-1c1-1 1-2 1-3-1-1-2-2-2-4h0c0-1-1-2-2-3s0-2-1-2c0-1-1-1-2-2h0c-1 0-1 0-2-1h0c-1-1-1-1-1-2-1-1-2-1-3-1-1-1-2-1-2-1h-2v-1h-2 0v-2l-3 1h-2l-1 1c1 2 1 2 1 4l-3 2v5c0 1 0 1-1 2h1l-1 1h-3l-1 1-2-1c-1 0-2 1-3 1-1-1-3-3-4-3l-1-1v-1l5-5 1-1z" class="P"></path><path d="M540 362c2-1 4-2 6-2l8 2c2 1 3 3 4 5 1 1 3 3 3 5-1 0-1 0-2-1h0c-1-1-1-1-1-2-1-1-2-1-3-1-1-1-2-1-2-1h0c0-1 1-1 1-2h0c-1 0-2 0-2-1l-3-2c-2-1-6 0-9 0z" class="G"></path><path d="M540 362c3 0 7-1 9 0l3 2c0 1 1 1 2 1h0c0 1-1 1-1 2h0-2v-1h-2 0v-2l-3 1h-2l-1 1c-1 2-2 3-4 3h-1l-1 1v3c-1 1 0 1-2 2h-2-1-1v-4l1-1c3-1 6-6 8-8z" class="F"></path><path d="M540 362c3 0 7-1 9 0-2 0-4 1-6 1-2 1-3 1-4 3-1 1-2 2-3 2 0 1-1 2-2 2-1 2-1 2-1 3v1 1h-1-1v-4l1-1c3-1 6-6 8-8z" class="K"></path><path d="M543 366c1 2 1 2 1 4l-3 2v5c0 1 0 1-1 2h1l-1 1h-3l-1 1-2-1c-1 0-2 1-3 1-1-1-3-3-4-3l-1-1v-1l5-5v4h1 1 2c2-1 1-1 2-2v-3l1-1h1c2 0 3-1 4-3z" class="T"></path><path d="M533 375h2c2-1 1-1 2-2v-3l1 2h2v1c-1 1-3 1-3 3-1 1-1 1-1 2h-1l-1-1v-1c-1 0-1 0-2-1h1z" class="L"></path><path d="M526 376l5-5v4h1c1 1 1 1 2 1v1l1 1h1c0-1 0-1 1-2v2h1v-2h1 0v3h0c-1 1-1 0-2 1l-1 1-2-1c-1 0-2 1-3 1-1-1-3-3-4-3l-1-1v-1z" class="F"></path><path d="M526 376l5-5v4c-1 0-2 0-3 1h1c1 1 2 2 2 3l1 1 2-1v1c-1 0-2 1-3 1-1-1-3-3-4-3l-1-1v-1z" class="C"></path><defs><linearGradient id="Bg" x1="500.45" y1="531.6" x2="520.21" y2="516.78" xlink:href="#B"><stop offset="0" stop-color="#535358"></stop><stop offset="1" stop-color="#888582"></stop></linearGradient></defs><path fill="url(#Bg)" d="M507 428l1 3c1-2 2-3 4-4v-5h0v34c0 5 1 10 0 15l1 75-1-1c-1 0-2-1-3-1v2l-1-77v-7l-1-34z"></path><path d="M511 481c0-3 0-5 1-8v27c-1-1-1-4-1-5 0-5-1-9 0-14z" class="e"></path><path d="M507 428l1 3c1-2 2-3 4-4v-5h0v34c0 5 1 10 0 15v2c-1 3-1 5-1 8l-1-7h0v9 1c-1 1-1 3 0 4v2 6 1 3l1 3h-2v-10c-1-1-1-2-1-3 1-1 1 0 1-1-1-2-1-2-1-4 1-2 1-9 1-11-1-2-1-3-1-5v-7l-1-34z" class="Z"></path><path d="M539 284l2-1v3c1 1 1 1 3 1h0 3l1 1h1l1 1c1 2 1 4 0 7h1v3c1 0 1 0 2 1l-1 1c1 1 1 1 2 1l-1 1c0 1 0 2 1 2 1 1 2 2 3 2l1 1c1 1 1 1 2 1l-1 1 2 1v3c0 1 0 2-1 3v1c-1-1-1 0-1-1h-1l-2-1-1-1c-1 0-4-1-5 0h0-3-1c-1-1-1-1-2-1s-2-1-3-1c-2-1-3-1-5-2l-1-1h-1c-2 0-2-1-3-1h-1l-1-2c0-1 1-2 1-4l1-1c2-2 3-5 3-8h0c1-2 1-3 0-4v-1l3-3 2-2h0z" class="S"></path><path d="M542 297l1-1c-1 0 0 0-1-1 1 0 2-1 3-1l-1-1c1-2 2-4 3-5 0 1 1 3 0 4s-1 2-1 4l-1 1c1 1 1 4 3 4v1l-1-1c0 1-1 2-1 2h-1l1-2h-1l-3-2v-2z" class="M"></path><path d="M549 288l1 1c1 2 1 4 0 7h1v3c1 0 1 0 2 1l-1 1c1 1 1 1 2 1l-1 1h-3l1-1-2-1v-2-1c-1-1-1-1-1-2h-1c0-1 0-2 1-3 1-2 1-3 1-5z" class="K"></path><path d="M550 303h3c0 1 0 2 1 2 1 1 2 2 3 2l1 1c1 1 1 1 2 1l-1 1h0v2l-2 2-1-1c-1 1-1 1-2 1v-1h-1l-1 1h-1v-1-1h0c1-1 2-1 3-2 1 0 1 0 1-1h-1-1v-1-2c-1-1-2-1-2-2l-1-1z" class="L"></path><path d="M536 301l1 1h2 1l1-1v-1h-1l2-3h0v2l3 2h1l-1 2h1s1-1 1-2l1 1c0 2-1 2-1 3v1c-1 0-1 0-2-1-1 1-1 0-1 1l-2-1c-1 1-1 0-1 1l-1 1h-2v1l-2-2-1 1v-1-1-3l1-1z" class="X"></path><path d="M536 301l1 1h2 1l1-1v-1h-1l2-3h0v2l3 2-1 1c-1 0-2 0-3-1-1 1 0 2-1 4l-1-1h-2 0-1v1h-1v-3l1-1z" class="b"></path><path d="M539 284l2-1v3c1 1 1 1 3 1h0 3l1 1h-1c-1 1-2 3-3 5l1 1c-1 0-2 1-3 1 1 1 0 1 1 1l-1 1h0l-2 3h1v1l-1 1h-1-2l-1-1c1-1 1-1 0-2l1-2v-1c1 0 2-1 2-2v-2s1-1 2-1h0c1-1-1-1-2-2v-2c0-1 1-1 1-2l-1-1h0z" class="g"></path><path d="M539 284l1 1c0 1-1 1-1 2v2c1 1 3 1 2 2h0c-1 0-2 1-2 1v2c0 1-1 2-2 2v1l-1 2c1 1 1 1 0 2l-1 1v3h0l-1 1h-1c1-1 0-1 1-2l-1-1-2-1c2-2 3-5 3-8h0c1-2 1-3 0-4v-1l3-3 2-2z" class="V"></path><path d="M537 286c1 1 1 2 0 4 0 1 0 1-1 2l-2 2h0c1-2 1-3 0-4v-1l3-3z" class="O"></path><path d="M534 294l2-2h1c0 1 0 1 1 2l-1 1v1 1l-1 2c1 1 1 1 0 2l-1 1v3h0l-1 1h-1c1-1 0-1 1-2l-1-1-2-1c2-2 3-5 3-8z" class="X"></path><path d="M530 303l1-1 2 1 1 1c-1 1 0 1-1 2h1l1-1h0v1 1l1-1 2 2v-1h2v1l1-1 1 1h3l2-1v1l-1 1h2c-1 1-1 1-2 1 1 1 2 1 3 2v-2l2 2h0v1 1h1l1-1h1v1c1 0 1 0 2-1l1 1 2-2v-2h0l2 1v3c0 1 0 2-1 3v1c-1-1-1 0-1-1h-1l-2-1-1-1c-1 0-4-1-5 0h0-3-1c-1-1-1-1-2-1s-2-1-3-1c-2-1-3-1-5-2l-1-1h-1c-2 0-2-1-3-1h-1l-1-2c0-1 1-2 1-4z" class="K"></path><path d="M557 314l2-2v-2h0l2 1v3h-1l-1 1v1l-2-2zm-17-4l2-1v1c1 0 1 0 2 1 1-1 1-1 2-1 1 1 2 1 3 2v-2l2 2h0l-1 1c-2 0-3 0-4-1-1 1-3 0-4 0l-2-1v-1z" class="R"></path><path d="M533 303l1 1c-1 1 0 1-1 2h1l1-1h0v1 1l1-1 2 2v-1h2v1l1-1 1 1h3l2-1v1l-1 1h2c-1 1-1 1-2 1s-1 0-2 1c-1-1-1-1-2-1v-1l-2 1h-1l-1-1h0-1c-1 0-2-1-3-1s-1-1-1-1c-1-1-2 0-3 0 1-1 1-2 2-3v-1h1z" class="T"></path><path d="M524 417l2-2c1-1 4-2 5-3 5-1 7-3 12-2 4 0 10 4 14 6h0c2 1 2 2 3 3v1l-1-1c0-1-1-1-1-2-1 1 0 2 1 3v3h1c-1 1 0 1-1 1v2c1 0 1 1 1 2h-2l1 2h-1c-1 1-2 0-3 1h-2l2 1v1c-1 2-2 1-3 2-2-1-4-1-6-2-5 0-9 0-13 1l-2 1-1-4-6-14z" class="D"></path><path d="M530 431c1 0 3-1 5-1l1 1c-2 1-3 1-3 3l-2 1-1-4z" class="E"></path><path d="M548 431c2 0 4 1 7 2-1 2-2 1-3 2-2-1-4-1-6-2l2-1v-1z" class="O"></path><path d="M536 431h0 1 1c3-1 6-1 10 0v1l-2 1c-5 0-9 0-13 1 0-2 1-2 3-3z" class="Y"></path><path d="M546 413c2 0 3 1 5 2l2 2h1 0c1 0 2 1 2 2h1c0 1 1 2 1 3h-1 0v1 1c-1 2-2 4-4 5h-6c-2 0-3 0-5-1-1 0-1 0-2 1h-2-3c-1 1-1 0-2 1-3-3-4-4-5-8v-2-1l-1-1h1c2-1 3-3 5-4v1c-1 1-1 1-1 2h-1l-2 2c0 1 2 7 3 8s2 0 3 0v-1h1c1 1 2 1 3 1 0-1 0-2 1-2v-1l1-1c2 0 2 0 3 1v-1c1 0 2-1 3-1l-1 2h1l3-1v-1h-1c-1-1 0-2 0-3v-1h0v-2l-1-1-2 1-1-1c0-1 1-1 1-2h0z" class="G"></path><path d="M549 416c2 1 3 1 4 2l-1 1c1 0 1 1 2 2v1 1h0v1h-1-1c0 1 1 1 1 1 0 1-2 2-2 2-1 1-2 0-3 0s0 1-2 0l-1-1c-1 1-2 1-3 0v-1h1c-1-1-1-1-3-1l1-1c2 0 2 0 3 1v-1c1 0 2-1 3-1l-1 2h1l3-1v-1h-1c-1-1 0-2 0-3v-1h0v-2z" class="K"></path><path d="M533 414c0-1 0-1 1-2h3c1-1 3 0 5 0h0v1h0c1 0 1 0 2-1l2 1h0c0 1-1 1-1 2l1 1 2-1 1 1v2h0v1c0 1-1 2 0 3h1v1l-3 1h-1l1-2c-1 0-2 1-3 1v1c-1-1-1-1-3-1l-1 1v1c-1 0-1 1-1 2-1 0-2 0-3-1h-1v1c-1 0-2 1-3 0s-3-7-3-8l2-2h1c0-1 0-1 1-2v-1z" class="F"></path><path d="M533 414c0-1 0-1 1-2h3c1-1 3 0 5 0h0v1h0c1 0 1 0 2-1l2 1h0c0 1-1 1-1 2l1 1 2-1c-1 1-1 2-2 3-1-1-1-1-3-1v-2c-1 0-1 1-3 1 0 2 0 2-1 2h-2c-1 1-1 1-2 1v1l-2-1h0l-1 1h-1c1 1 3 2 3 4l-1 1 1 1h1v1c-1 0-2 1-3 0s-3-7-3-8l2-2h1c0-1 0-1 1-2v-1z" class="K"></path><path d="M543 366l1-1h2l3-1v2h0 2v1h2s1 0 2 1c1 0 2 0 3 1 0 1 0 1 1 2h0c1 1 1 1 2 1h0c1 1 2 1 2 2 1 0 0 1 1 2s2 2 2 3h0c0 2 1 3 2 4 0 1 0 2-1 3h1l-1 1h-1v1c0 1-2 3-3 4s-3 0-4 0h0c-2 1-4 1-6 1s-5-1-6-2h-4c-1-1-1-1-2-1h-1c-1 0-1 0-2-1-2 0-3-1-5-1l-2-2h-1c-2 0-3-1-4-1 0 0-1 1 0 1l15 6h1c1 1 3 1 5 2s3 1 4 3l-16-6c-8-3-15-5-21-11 1-1 1-2 1-4 1 1 1 2 1 3h2l1-1 2 2c1-1 2-2 4-2h0v-1l1-1v1l1 1c1 0 3 2 4 3 1 0 2-1 3-1l2 1 1-1h3l1-1h-1c1-1 1-1 1-2v-5l3-2c0-2 0-2-1-4z" class="G"></path><path d="M521 380c1-1 2-2 4-2h0v-1l1-1v1l1 1c1 0 3 2 4 3h0c-4 0-7 0-10-1z" class="E"></path><path d="M537 380h3 2v2c1 1 2 1 3 1s2-1 3-1l-1 2h0 1c1 0 2-1 3-1h1c1-1 1-1 2-1 1 1 1 1 1 2l2-1h0 2v2l-1 1v1h2 0c-1 1-2 1-2 1l-1 1h-2v-2-1c-2 0-3 0-4 2h-4c-1-1-4-1-5-1l-1-1 1-1c-1-1-2-1-2-1-1 0-2-1-3-2v-1h-1l1-1z" class="K"></path><defs><linearGradient id="Bh" x1="563.269" y1="378.586" x2="548.696" y2="376.541" xlink:href="#B"><stop offset="0" stop-color="#363638"></stop><stop offset="1" stop-color="#56555a"></stop></linearGradient></defs><path fill="url(#Bh)" d="M555 368c1 0 2 0 3 1 0 1 0 1 1 2h0c1 1 1 1 2 1h0c1 2 1 3 1 4 2 2 2 2 2 5-2 1-3 3-5 4v-2h-2 0l-2 1c0-1 0-1-1-2-1 0-1 0-2 1h-1c-1 0-2 1-3 1h-1 0l1-2 1-1v-1h1v-1h-1l-1-1c1 0 2-2 3-2l1-1c1 0 3-1 4-2s2-1 2-2c-1-1-3-1-4-2l1-1z"></path><path d="M556 373h2l1 2c0 1-1 1-2 2h0c-1 1-2 1-3 1v2l-1-1h-2v-1h-1v1h-1l-1-1c1 0 2-2 3-2l1-1c1 0 3-1 4-2z" class="R"></path><path d="M543 366l1-1h2l3-1v2h0 2v1h2s1 0 2 1l-1 1c1 1 3 1 4 2 0 1-1 1-2 2s-3 2-4 2l-1 1c-1 0-2 2-3 2l1 1h1v1h-1v1l-1 1c-1 0-2 1-3 1s-2 0-3-1v-2h-2l1-1h-1c1-1 1-1 1-2v-5l3-2c0-2 0-2-1-4z" class="L"></path><path d="M548 378c-1-1 0-1 0-3l2-1 1 2c-1 0-2 2-3 2z" class="T"></path><path d="M546 370h1l-1 1 1 2-2 1v1s1 0 1 1l-1 1c-1 0-1-1-2-1 1-1 1-1 1-3l-1-1v-1h2l1-1z" class="O"></path><path d="M508 462v7l1 77v1 3s-1 1-1 2h1v3 2h-1c0-3 0-5-2-7l-17-66v-1l-2-7v-1c2-3 6-5 9-6l10-5c0-1 1-2 2-2z" class="N"></path><path d="M489 483l1-3c3-2 6-3 9-4 2-1 4-3 6-3v1c0 3-1 4-4 5-2 2-9 5-12 5h0v-1z" class="V"></path><path d="M506 464c0 2 0 3-1 5-3 3-10 5-13 6-1 1-3 1-4 1h-1v-1c2-3 6-5 9-6l10-5z" class="j"></path><defs><linearGradient id="Bi" x1="583.256" y1="477.744" x2="537.642" y2="459.515" xlink:href="#B"><stop offset="0" stop-color="#4c4f4e"></stop><stop offset="1" stop-color="#827e82"></stop></linearGradient></defs><path fill="url(#Bi)" d="M590 327c1-1 2-1 4 0 0 0 1 1 1 2-2 21-8 42-13 63l-17 79-6 23c-1 4-2 9-2 13l-16 68-3 13c-1 1-1 4-2 5l-4 18-2 1 1-2h-1c-3 2-4 8-5 11h0-1v-3c1-3 2-5 3-8l3-12c-1-1 0-4 1-5l4-18 21-94 14-61 13-56c2-12 5-25 7-37z"></path><path d="M536 593l-4 18-2 1 1-2h-1c-3 2-4 8-5 11h0-1v-3c1-3 2-5 3-8l3-12c0 3-1 8-2 11l1-1v-1l1-1 1 1h1v-1l1-1c0-2 1-4 1-5s0-2 1-3v-1-1l1-2z" class="Y"></path><defs><linearGradient id="Bj" x1="505.044" y1="463.14" x2="487.589" y2="436.807" xlink:href="#B"><stop offset="0" stop-color="#050504"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#Bj)" d="M505 394c2-3 2-6 2-9v3l1 1c1 2 1 3 1 5 1 1 1 2 2 3v2l1 12v10 1h0v5c-2 1-3 2-4 4l-1-3 1 34c-1 0-2 1-2 2l-10 5c-3 1-7 3-9 6-2-3-2-6-3-9l-6-23-2-10-6-19-1-5v-2c1-2 1-2 3-3 7-3 14-4 21-6 2 0 3 1 4 1s2 1 2 2l1-1c2-1 3-3 3-5h0c1-1 1-1 2-1z"></path><path d="M500 452c0 2-1 3-1 4h1l4-4c-2 3-3 5-7 7-1 1-3 1-4 0-2-1-1-2-2-3l1-1 3 1c1 0 3-2 5-3v-1z" class="Q"></path><path d="M500 424c0 2 0 2 2 4h3l-1 1c-1 1 0 1-1 1l-1 2h2v2c-1 2-1 4-1 6h-1v-1c-1-1-2-1-3-1l1 1-1 1-3-2 2-1c0-3 0-6 1-9l1-4z" class="P"></path><path d="M500 424c0 2 0 2 2 4h3l-1 1c-1 1 0 1-1 1l-1 2c-1 1 0 4-1 6l-1-1c0-1 0-3 1-5h0v-1h0l-2-3 1-4z" class="H"></path><path d="M476 433h5 1l6 2 1 1c2 1 4 1 6 2s4 4 5 7c0 1 0 1 1 2v4h-1v1 1h-4v-2l-1 1c-1 1-2 1-3 0h-1c-1 1-2 1-3 1-1-2-3-3-4-5-1-1-2-2-2-3-2-1-2-1-2-2h-2l-2-10z" class="q"></path><path d="M480 443c0-1 1-1 2-1s2 1 3 0h0c0-1 1-1 1-2l1 1h1c3 3 6 6 8 10l-1 1c-1 1-2 1-3 0h-1c-1 1-2 1-3 1-1-2-3-3-4-5-1-1-2-2-2-3-2-1-2-1-2-2z" class="G"></path><path d="M505 394c2-3 2-6 2-9v3l1 1c1 2 1 3 1 5 1 1 1 2 2 3v2l1 12v10 1h0v5c-2 1-3 2-4 4l-1-3v-5c-1 1-1 1-1 2h0v3s-1 1-1 0h-3c-2-2-2-2-2-4l-1 4c-1 3-1 6-1 9l-2 1 3 2 2 4-1 1c-1-3-3-6-5-7s-4-1-6-2l-1-1-6-2c1 0 1 0 1-1h1 2l-1-1h1l1-1c1-1 2-3 2-4l2-3c0-2 1-4 2-5v-1l2 1 4-9c-1-2-1-2-2-3 1-1 1-1 1-3 0-1-2-1-2-2h0l2 1 1-1 1-1c2-1 3-3 3-5h0c1-1 1-1 2-1z" class="o"></path><path d="M503 395h0c1-1 1-1 2-1l-5 12s-1 2-1 3c-1-2-1-2-2-3 1-1 1-1 1-3 0-1-2-1-2-2h0l2 1 1-1 1-1c2-1 3-3 3-5z" class="G"></path><path d="M493 417l2 1c-2 6-5 12-7 17l-6-2c1 0 1 0 1-1h1 2l-1-1h1l1-1c1-1 2-3 2-4l2-3c0-2 1-4 2-5v-1z" class="E"></path><path d="M496 438l-5-2 17-38h0l-1 7v6c-1 2-3 4-4 5l-3 8-1 4c-1 3-1 6-1 9l-2 1z" class="N"></path><path d="M508 398c1-1 2-1 3-1v2l1 12v10 1h0v5c-2 1-3 2-4 4l-1-3v-5c-1 1-1 1-1 2h0v3s-1 1-1 0h-3c-2-2-2-2-2-4l3-8c1-1 3-3 4-5v-6l1-7z" class="i"></path><path d="M503 416c1-1 3-3 4-5v-6l1 16-2-2c-1-1-1-2-3-3z" class="G"></path><path d="M503 416c2 1 2 2 3 3l2 2-1 2c-1 1-1 1-1 2h0v3s-1 1-1 0h-3c-2-2-2-2-2-4l3-8z" class="U"></path><path d="M493 398c2 0 3 1 4 1s2 1 2 2l-1 1-2-1h0c0 1 2 1 2 2 0 2 0 2-1 3 1 1 1 1 2 3l-4 9-2-1v1c-1 1-2 3-2 5l-2 3c0 1-1 3-2 4l-1 1h-1l1 1h-2-1c0 1 0 1-1 1h-1-5l-6-19-1-5v-2c1-2 1-2 3-3 7-3 14-4 21-6z" class="q"></path><path d="M477 413c2-2 5-3 8-4 1 2 3 3 5 4 0 1 2 1 2 2 1 0 1 2 1 2v1l-1-1c-2-1-2-2-4-2v-1c-1-1-3-1-4-2-1 1-2 1-2 1h-1-1l-1 1c-1 0-2-1-2-1z" class="G"></path><path d="M493 398c2 0 3 1 4 1s2 1 2 2l-1 1-2-1h0c0 1 2 1 2 2 0 2 0 2-1 3-3 1-6-1-9-1h-1c0-1 1-1 2-2-2 0-4 1-5 1l-10 3c-1 0-3 1-5 1v1-2c1-2 1-2 3-3 7-3 14-4 21-6z" class="b"></path><path d="M472 404l1 1c2 0 3-1 4-1h2c-1 0-2 1-3 1s-1 1-2 2c-1 0-3 1-5 1v1-2c1-2 1-2 3-3z" class="S"></path><defs><linearGradient id="Bk" x1="473.272" y1="426.29" x2="483.715" y2="420.018" xlink:href="#B"><stop offset="0" stop-color="#141314"></stop><stop offset="1" stop-color="#333334"></stop></linearGradient></defs><path fill="url(#Bk)" d="M481 413h1s1 0 2-1c1 1 3 1 4 2v1c2 0 2 1 4 2l1 1c-1 1-2 3-2 5l-2 3c0 1-1 3-2 4l-1 1h-1l1 1h-2-1c0 1 0 1-1 1h-1-5l-6-19h1c2-1 4-1 6-1 0 0 1 1 2 1l1-1h1z"></path><path d="M484 424h1l4 2c0 1-1 3-2 4h-2c0-1-1-3-2-4h0l1-2z" class="C"></path><path d="M481 413h1s1 0 2-1c1 1 3 1 4 2v1c2 0 2 1 4 2l1 1c-1 1-2 3-2 5l-2 3-4-2h-1l-1-1c-1 0-1-1-3-1h0c-1-1-1-2-2-3 1-2 2-3 3-4v-1h0v-1z" class="K"></path><path d="M485 424v-1h2v-1c2 0 2 0 4 1l-2 3-4-2z" class="F"></path></svg>