<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="117 118 799 820"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#afaeaf}.C{fill:#9b9a9a}.D{fill:#d2d2d2}.E{fill:#4a4949}.F{fill:#f2f1f1}.G{fill:#838283}.H{fill:#d5d4d4}.I{fill:#c6c5c6}.J{fill:#cfcfce}.K{fill:#626161}.L{fill:#262626}.M{fill:#3d3d3d}.N{fill:#8f8e8e}.O{fill:#696868}.P{fill:#787878}.Q{fill:#0b0b0b}.R{fill:#e4e3e3}.S{fill:#727172}.T{fill:#e8e7e7}.U{fill:#545454}.V{fill:#a4a3a3}.W{fill:#373737}.X{fill:#2a2929}.Y{fill:#181818}</style><path d="M704 792l4-1c0 2-1 3-1 5h-2c0-2 0-3-1-4z" class="G"></path><path d="M245 329l-3-2-1-1v-3h0l6 5-2 1z" class="F"></path><path d="M756 446c3 0 5 1 7 1l4 4c-1 0-1 0-2-1l-6-2c0-1-2-1-3-2z" class="M"></path><path d="M505 216l3 13c-2-2-3-4-3-5l-2-3c1-1 1-2 1-4l1-1z" class="B"></path><path d="M504 226c0-1 0-1 1-2 0 1 1 3 3 5 1 2 0 5 0 8l-4-11z" class="W"></path><path d="M694 621c3 3 5 6 6 11-4-3-6-8-9-11h3z" class="H"></path><path d="M266 534c-1-2-2-3-2-4l1-1c2-1 5 1 6 2 1 0 1 1 1 1 0 1-1 1-2 2h-4z" class="I"></path><path d="M331 797l7 6v1l-1 1c-3-1-7-4-10-6 1-2 2-2 4-2z" class="C"></path><path d="M712 832h2 10c-4 2-6 4-11 4-2 0-3-1-5-2h2 6c-1-1-2-1-3-1v-1h-1z" class="D"></path><path d="M702 829l2-1h1c3 1 5 2 7 4h1v1c1 0 2 0 3 1h-6-2c-2-1-4-3-6-5z" class="H"></path><path d="M341 585v-8c-1-5-3-10-7-13v-1h0c2 1 4 1 5 3 2 2 3 4 3 7s1 9-1 11v1z" class="N"></path><path d="M724 494c7-2 15-3 22-1 1 1 2 1 3 2-5-1-11-2-16 0v-1c-1 0-3 0-4 1h0c-2 0-4 0-5-1z" class="B"></path><path d="M708 791c2 1 3 1 4 3s1 5 1 7h-1c-2-1-2-1-3-3 0-1-1-2-2-2 0-2 1-3 1-5z" class="O"></path><path d="M696 516v1c-1 3-6 11-5 14-1 1-1 1-1 2l-1-1v-1h-2l1-1v-2c2-5 5-8 8-12z" class="B"></path><path d="M713 647s1 1 3 1l8 10c1 1 3 2 4 4l-1 1c0-1-1-2-2-3-1 1 0 3 0 4l-12-17z" class="E"></path><path d="M683 572v1h0c1-3 2-7 5-8 1-1 3-2 5-3-4 4-7 8-8 14l-1 1v11c0-1-1-3-1-4h0c-1-4-1-8 0-12z" class="G"></path><path d="M342 623h1c3-3 11-8 16-8 1 0 1 0 1 1-3 2-7 4-10 6h0c-2 0-3 1-4 1-1 1-2 1-3 1l-1-1z" class="D"></path><path d="M749 442h1c2-1 6-2 8-1 2 2 4 4 5 6-2 0-4-1-7-1h0-1c-2-1-4-1-7-2h-1l2-2z" class="E"></path><path d="M507 163c-1-4-1-11 1-15 0-1 2-2 4-3 1 0 2 1 3 2 4 3 4 7 4 12-1 3 0 6 0 9 0 2-1 5 0 7 0 2 2 3 2 5v1l-1-1c0-1-1-2-1-3-1-1-1-1-1-2-1-1 0-2-1-3 0-1 0-1 1-2v-4-7-6-1l-1-1c-1-2-2-3-3-4-1 0-1 0-2 1l-2-1c-1 0-2 1-2 3h0c-1 2 0 3 0 4-1 1-1 2-1 3v6z" class="Q"></path><path d="M376 813l27 4v1c-4-1-9-1-13-1h-16l4-1c2 1 5 0 8 0v-1h-2c-3 0-5 0-7-2h-1z" class="D"></path><path d="M337 612h2 0v1l-1 1h1c1-1 2-1 2-2h2c-3 3-7 6-10 10-2 3-4 7-7 9 1-5 5-11 8-15 1-1 1-1 1-2l2-2z" class="R"></path><path d="M676 607l3 1 3 3h1l-1-2v-1c2 1 3 2 4 3 0 1 1 2 1 2 1-1 0-1 1-3l3 4c1 2 2 4 3 7h-3l-15-14z" class="J"></path><path d="M269 441c2 0 5 1 7 1l3 1-1 1c-2 1-4 2-6 2-2 1-3 1-5 2h0c-3 1-5 4-9 4l11-11z" class="U"></path><path d="M276 442l3 1-1 1c-2 1-4 2-6 2-2 1-3 1-5 2l1-2 3-3h3c1 0 1 0 2-1z" class="E"></path><path d="M758 441c3 1 5 2 7 4 5 3 8 8 14 9l1 1c-1 1-2 1-3 1-2 1-5-1-7-2l-3-3-4-4c-1-2-3-4-5-6z" class="F"></path><path d="M248 661c0-7 2-13 5-19v3 3h0c0 7 0 13 2 19h0l1 4c-2-2-3-4-3-7l-1-1c0-1-2-2-4-2z" class="P"></path><path d="M288 704v14l6-4v7c0 1-1 2 0 3-1 2-1 3-3 4h-3l-1-1c1-2 1-5 1-7 0-5-1-11 0-16z" class="V"></path><path d="M316 830h0c2-1 2-2 4-2 1-1 2-1 4-1l-1 1h1c-3 3-5 5-9 7l-2 1h-2c-4 0-7-2-9-5h0c3 1 7 1 9 1l1-1 4-1z" class="H"></path><path d="M302 831h0c3 1 7 1 9 1l1-1 4-1v1c-2 2-5 2-8 2l1 1c2 0 4 0 6 1l-2 1h-2c-4 0-7-2-9-5z" class="B"></path><path d="M269 441l-11 11v1c-3 2-6 4-9 4-2-1-2-1-3-2 7-1 11-7 17-11 2-2 4-2 6-3z" class="F"></path><defs><linearGradient id="A" x1="704.664" y1="794.092" x2="688.939" y2="800.656" xlink:href="#B"><stop offset="0" stop-color="#878788"></stop><stop offset="1" stop-color="#b5b4b4"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M698 794c2 0 4-2 6-2 1 1 1 2 1 4-7 2-13 8-20 11v-2c5-3 9-8 13-11z"></path><defs><linearGradient id="C" x1="313.544" y1="795.088" x2="324.976" y2="798.341" xlink:href="#B"><stop offset="0" stop-color="#686768"></stop><stop offset="1" stop-color="#878687"></stop></linearGradient></defs><path fill="url(#C)" d="M327 799c-2-2-5-3-8-4l-3 3-2 4h-1l-1-2c0-2 0-5 3-7 2-2 6-2 8-1s3 2 5 3l3 2c-2 0-3 0-4 2z"></path><path d="M293 495v-1c-5-2-13 0-17 0 12-4 24-2 35 4l7 4c-4-1-8-3-12-4s-9-2-13-3z" class="V"></path><path d="M334 616c0-2 1-4 1-6 1-6 3-11 2-17 0-4-1-7-4-10h0v-1h2c1 0 2 1 3 2s1 3 2 4v8c0 2-1 3-1 5 1 2 0 2 0 4l-1 3h0l-3 6c0 1 0 1-1 2z" class="B"></path><path d="M364 825c3-1 6-3 10-4h1c6-1 12-2 18-2v2l-9 1c-5 1-9 3-14 5-3 0-4 0-6-2z" class="S"></path><path d="M294 724c0 4 0 7-3 10-1 1-2 2-3 2h-1l-6-3c-1-1-2-2-2-3 2-4 4-2 7-3h1l1 1h3c2-1 2-2 3-4z" class="P"></path><path d="M635 819c8 0 15 1 22 4 1 1 3 1 4 2v1 2c-1 0-1 1-2 2-7-6-16-7-25-9h-2l3-2z" class="K"></path><path d="M685 592h0c0-4 1-7 3-10 2-1 2-1 4-1l1 1-2 2c-4 5-3 11-2 17 0 4 2 9 2 13h0l-3-4h0c-1-1-1-2-2-3l1-1h0c0-1-1-2-1-4h0c-1-1-1-2-1-4v-1-3c-1-1-1-1 0-2z" class="V"></path><path d="M733 686c0-2 1-2 0-4 0-1 0-3 1-4h1c2 5 4 7 10 9 7 2 14 1 21-3v1 1h-1c-1 0-1 0-1 1h-1-1c-1 1 0 1-1 1h-2l-1 1h-2c-1 1-5 0-6 0h-1-1-1-1c-2 2-3 1-5 1-1 0-2 1-3 1h-1-1c0 1 1 2 1 3s0 1 1 2l-1 3-4-13z" class="Q"></path><path d="M729 736h1c1-1 1-3 1-4l1-1v1c1 2 2 3 4 4h0c1 1 2 2 4 3 3 2 7 3 11 5-2 0-4 1-5 1h-1c-4 1-9 0-12-2l-5-5 1-2z" class="E"></path><defs><linearGradient id="D" x1="345.126" y1="812.483" x2="344.356" y2="821.22" xlink:href="#B"><stop offset="0" stop-color="#929192"></stop><stop offset="1" stop-color="#b1b0b0"></stop></linearGradient></defs><path fill="url(#D)" d="M340 813c7-1 15-1 22 2l9 2c1 0 2 0 2 1-3 0-6-1-10-1-10-2-21-3-30 3-3 2-5 4-8 7v-1c1-2 3-4 4-5h1v-1l-2-2c4-2 8-4 12-5z"></path><path d="M253 318v3h0c-2-1-3-3-4-4-2-1-4-2-5-3-3-3-6-6-8-9-1-2-5-7-5-8 1 0 1 1 3 1l5 5 2-1 1-1c-1-1-1-2-2-3v-1c1 1 2 2 2 3 4 6 7 12 11 18z" class="E"></path><path d="M240 297c1 1 2 2 2 3v1c2 3 4 7 5 10a30.44 30.44 0 0 1-8-8l2-1 1-1c-1-1-1-2-2-3v-1z" class="S"></path><defs><linearGradient id="E" x1="680.18" y1="812.507" x2="679.88" y2="820.408" xlink:href="#B"><stop offset="0" stop-color="#7f7e7f"></stop><stop offset="1" stop-color="#b3b3b3"></stop></linearGradient></defs><path fill="url(#E)" d="M662 814h1c2 0 5-1 7-1 5-1 10-1 15 0 4 1 8 2 11 5h0l1 1h0v1 2 1c1 0 1 1 1 2-2-3-5-5-8-6-10-5-22-3-32-2-4 0-8 1-12 0l6-1c0-1 2-1 3-1l7-1z"></path><path d="M661 825c5 3 8 5 10 10 2 6 1 11-2 17-2 3-4 6-6 8 1-2 2-5 3-8v-1c2-6 0-12-3-17-1-2-3-3-4-4 1-1 1-2 2-2v-2-1zm-297 0c2 2 3 2 6 2-4 3-7 6-9 11-3 8-2 15 2 22-2-1-3-3-4-5-4-6-6-12-4-19 1-6 4-8 9-11z" class="J"></path><defs><linearGradient id="F" x1="314.867" y1="816.069" x2="311.491" y2="832.346" xlink:href="#B"><stop offset="0" stop-color="#bbbaba"></stop><stop offset="1" stop-color="#e1e1e1"></stop></linearGradient></defs><path fill="url(#F)" d="M302 831l-2-1c-2-2-3-6-3-8 0-3 2-6 4-8 2 2-1 8 1 11 1 2 3 3 5 3 8 1 16-6 21-10l2 2v1h-1c-1 1-3 3-4 5v1l-1 1h-1l1-1c-2 0-3 0-4 1-2 0-2 1-4 2h0l-4 1-1 1c-2 0-6 0-9-1h0z"></path><defs><linearGradient id="G" x1="708.324" y1="813.862" x2="718.175" y2="832.774" xlink:href="#B"><stop offset="0" stop-color="#bfbebe"></stop><stop offset="1" stop-color="#e6e6e6"></stop></linearGradient></defs><path fill="url(#G)" d="M696 818h1c6 4 14 12 22 10 2-1 4-2 5-4 1-3 1-8 0-11 3 3 5 6 4 10 0 4-2 6-4 9h-10-2c-2-2-4-3-7-4h-1l-2 1c-1-2-3-3-4-4 0-1 0-2-1-2v-1-2-1h0l-1-1z"></path><path d="M298 680c-1 3-2 7-3 11 0 5-1 10-1 16v7l-6 4v-14c0-7 3-16 7-22 1-1 1-2 3-2zm462-264c2-2 4-4 5-6 1-1 2-5 3-6 4 4 5 9 8 14l-14 9h-1l-1 1-1-1c1-2 1-1 3-2 0-2 1-3 2-4l-1-1h-1c-1 1-1 2-2 3-1 0-2 0-3 1s-1 1-2 1c1-2 4-4 4-7l1-1v-1zm-34 261l1-1c0 2 1 3 2 4 0 2 0 3 1 4l2-1 1 3 4 13c1 5 1 11 0 17l-6-2v-11h0c0-9-2-18-5-26z" class="R"></path><defs><linearGradient id="H" x1="735.083" y1="733.846" x2="737.944" y2="726.126" xlink:href="#B"><stop offset="0" stop-color="#595859"></stop><stop offset="1" stop-color="#777676"></stop></linearGradient></defs><path fill="url(#H)" d="M731 703h0v11l6 2c0 3-1 6 0 9 3 3 7 1 9 5-1 2-4 3-6 4l-4 2c-2-1-3-2-4-4v-1l-1 1c0 1 0 3-1 4h-1c0-6 0-14 1-20 1-4 0-9 1-13z"></path><defs><linearGradient id="I" x1="264.262" y1="348.147" x2="241.308" y2="338.857" xlink:href="#B"><stop offset="0" stop-color="#cfcecf"></stop><stop offset="1" stop-color="#f7f6f7"></stop></linearGradient></defs><path fill="url(#I)" d="M245 329l2-1c2 3 2 5 1 9 0 1-4 6-4 7 3 1 5 1 7 1 3 1 5 1 8 2 2 1 4 3 5 6 2 2 3 5 4 8-3-4-7-10-13-11h-5c-1 1-1 2-2 3l-3-3c0 2 3 4 4 6-3-2-8-4-10-8-1-2 0-4 1-6 3-5 5-7 5-13z"></path><path d="M626 851v-1c0-2-1-5-2-7l2 1v1l1-1 2 2 2 1v1h0v1c1 1 1 2 2 2h0c2 7-1 16-5 21-5 8-12 10-20 12 5-2 9-5 13-9 5-7 6-15 5-23v-1z" class="D"></path><path d="M395 844c2 1 5 1 7 2-2 5-3 11-2 16 2 11 9 16 18 22-2 0-5-1-7-2-6-2-12-7-15-13-4-8-4-16-1-25z" class="R"></path><path d="M785 323l1 1c-1 3-5 4-5 7-1 4 3 7 5 10 1 2 2 4 1 7-1 4-6 6-9 7h-1c0-2 0-3-1-4s-3-2-4-2c-4 0-6 3-9 5l-4 5c1-3 3-7 6-10 4-5 11-4 18-4l-4-5c-2-2-2-4-2-7 1-5 4-7 8-10z" class="T"></path><defs><linearGradient id="J" x1="610.696" y1="826.21" x2="607.968" y2="836.567" xlink:href="#B"><stop offset="0" stop-color="#727273"></stop><stop offset="1" stop-color="#aba9a9"></stop></linearGradient></defs><path fill="url(#J)" d="M609 833h0c-6-7-13-9-21-11 2-1 4 0 6 0 7 1 13 3 20 5 9 5 16 12 18 22l1 2c-1 0-1-1-2-2v-1h0v-1l-2-1-2-2-1 1v-1l-2-1c1 2 2 5 2 7v1c-1-2-2-6-4-7v-1c-2-4-7-9-11-11-1 0-1 0-2 1z"></path><defs><linearGradient id="K" x1="223.49" y1="302.992" x2="216.009" y2="280.5" xlink:href="#B"><stop offset="0" stop-color="#b2b0b0"></stop><stop offset="1" stop-color="#e0dfe0"></stop></linearGradient></defs><path fill="url(#K)" d="M234 298c-2-2-4-5-6-7-6-4-14-6-20-4-6 1-9 3-12 7v1h-1c1-3 3-5 5-7 6-7 16-7 24-8l-1 1c1 1 0 1 1 1h3 0c-1 0-1 0-2 1 2 0 3 1 4 2v1h-2 0c1 1 0 1 2 1h0c4 3 8 6 11 9v1 1c1 1 1 2 2 3l-1 1-2 1-5-5z"></path><defs><linearGradient id="L" x1="277.772" y1="746.571" x2="293.044" y2="726.162" xlink:href="#B"><stop offset="0" stop-color="#383838"></stop><stop offset="1" stop-color="#575656"></stop></linearGradient></defs><path fill="url(#L)" d="M298 680l1-2c1-3 2-4 4-6l-2 4c-1 4-3 7-4 11-3 11-2 25-1 37 1 4 1 9 1 13-4 5-9 7-15 9h0-6 0c-1-1-2-1-3-1-2-2-2-3-2-5l3-3c4-1 11 1 13-1h1c1 0 2-1 3-2 3-3 3-6 3-10-1-1 0-2 0-3v-7-7c0-6 1-11 1-16 1-4 2-8 3-11z"></path><path d="M696 516c6-11 16-19 28-22 1 1 3 1 5 1h0c1-1 3-1 4-1v1c12 2 27 6 34 17 1 2 2 3 2 4l-4-4c-1-2-2-4-4-5-4-3-8-6-13-7-1 0-2 1-3 2h1v1l-8-2c-3-1-5-1-8 0h-3l-2 1h-2l-2 1c-1 0-4 1-4 1-2 0-4 2-6 2-4 2-9 3-11 6-2 2-3 3-4 5v-1z" class="V"></path><path d="M248 661c2 0 4 1 4 2l1 1c0 3 1 5 3 7 0 2 3 6 5 7h1 0c1 1 1 1 2 1l1 1c1 0 2 1 3 1h2c2 1 4 1 6 1l1-1h2 0c2-1 3-1 4-2l1-1c1 0 2-1 3-2l1-1c1-1 1-1 2-1h1c-1 3-2 6-4 9s-8 5-12 5c-7 0-13-1-18-6-6-6-8-13-9-21zm519 13c2-1 3-4 4-5l1-3c2-1 3-4 5-6 0 9-3 16-9 22 0 1-2 2-2 2-7 4-14 5-21 3-6-2-8-4-10-9l-2-6h0c2 1 3 2 5 3 1 1 1 2 3 3 0 0 1 1 2 1l1 1c1 1 3 1 5 1 2 1 5 1 7 1l1-1h2c2-1 0 0 1-1 1 0 2-1 3-2s1 0 2-1c1 0 1-1 2-2v-1zM541 207s9-6 11-6c1-1 2 0 3 0l-4 3c-10 5-17 15-22 24-2 3-4 7-6 10h0c-3 7-5 15-7 22l-1-1c0-1 0-2-1-3 1-8 2-18 6-25 5-9 12-18 21-24z" class="F"></path><path d="M844 250h0c3 9 9 17 18 22 2 1 4 2 7 3h1c9 2 19 0 27-5l6-4c0 1-1 2-2 3h0l-1 1h-1v1h-1-1v1c-1 0-1 1-1 1-1 1-1 2-2 3-3 2-5 3-8 4l-2 1c-1 1-1 1-2 1-1 1-3 0-4 0-1 1-3 1-4 1-1-1-3-1-4-1h-1l-4-2c-1 0-2 0-3-1-1 0-2 0-3-1-1 0-2-1-3-2-3-1-5-5-7-7-1-2-2-3-2-5l-1-1c-1-2-1-1-1-2-1-2 0-4 0-5-1-1-1-2-1-3v-3z" class="Q"></path><defs><linearGradient id="M" x1="804.153" y1="299.156" x2="805.719" y2="281.043" xlink:href="#B"><stop offset="0" stop-color="#b2b1b1"></stop><stop offset="1" stop-color="#dbdbdb"></stop></linearGradient></defs><path fill="url(#M)" d="M789 281c4-1 8-1 11 0 3 0 7-1 10 0 6 0 12 2 16 7 3 3 4 6 5 9-4-5-6-9-13-11-3 0-6 0-9 1h-1c-9 2-14 9-21 15l-7 5v-2c1-4 2-7 5-10 0-1 1-1 1-1 1-1 1 0 1-1 1-1 3-2 3-3-1-1-1-1-1-2l1-1c0-2-1-3-2-5l1-1z"></path><path d="M785 295h0l-4 8c3-1 4-3 6-5l6-6c0 3-8 7-6 10l-7 5v-2c1-4 2-7 5-10z" class="C"></path><path d="M789 281c4-1 8-1 11 0 3 0 7-1 10 0h-7v1c-1 1-2 1-4 1-3 1-7 4-9 7-1-1-1-1-1-2l1-1c0-2-1-3-2-5l1-1z" class="I"></path><path d="M754 384c0-2 1-5 1-7 2-8 6-13 12-17-4 8-8 16-9 26l1-1c4-2 8-2 13-1 2 0 5 1 8 0 8 0 13-5 16-12 0 7-2 12-8 16-2 2-6 3-9 4-3 2-15 3-17 7h-1c-2-1-1-4-3-6v-1c0-1-1-2-2-3 0-1 0-1-1-2h0l-1 3v-6z" class="D"></path><path d="M381 584l7 16 4 12 6 15c-3-2-6-4-9-5-5-2-10-2-16-3 2-1 4-1 5 0h1v-1c1-1 0-4 0-6-1-1-3 0-5-1 0-1 1-2 1-3h1v-1l1-1 2-2v-1c-2 0-2-1-4 0h0c-1-2-3-2-4-3h-5-1-1v1l-1-1c1-1 3-1 4-1 3-1 6 1 8 3l1-1c0-1 0-2-1-3-1-2-5-4-7-5l6-2c2-1 4-3 5-5 1-1 2-1 2-2z" class="Q"></path><path d="M388 600l4 12c-3 0-6-1-9-1-2 0-6 1-8-1 2-4 7-7 11-9l2-1z" class="G"></path><path d="M388 600l4 12c-3 0-6-1-9-1h9l-8-3c1-2 1-2 2-3 1-2 1-3 0-4l2-1z" class="E"></path><path d="M404 843h0 1v-1c2-4 5-7 8-9 1-1 2 0 4 0-1 3-1 5-1 7v1c0 8 3 19 9 25 4 4 8 5 13 6-9 0-14-1-21-7-3-3-6-7-7-11 0 7 1 13 7 19 2 2 5 4 8 5-8-2-14-5-18-12-4-6-5-15-3-22v-1z" class="T"></path><path d="M609 833c1-1 1-1 2-1 4 2 9 7 11 11v1c2 6 2 13-1 19-4 9-10 13-19 16 2-2 5-4 7-6 6-6 8-12 8-21-2 5-4 9-8 12-6 6-12 8-21 8 1-1 3-1 5-1 4-2 8-5 11-9 3-5 5-12 6-18v-6h0c0-2-1-4-1-5z" class="R"></path><defs><linearGradient id="N" x1="375.6" y1="624.724" x2="292.666" y2="661.64" xlink:href="#B"><stop offset="0" stop-color="#9e9e9e"></stop><stop offset="1" stop-color="#dedcdc"></stop></linearGradient></defs><path fill="url(#N)" d="M362 619h9c1 1 2 1 3 1v1h-2 0c3 1 5 2 7 4l1 1c-19-3-37 1-52 12-9 6-20 18-23 28v1 2 1l-1 1c-1 0-1 0-1 1-2 2-3 3-4 6l-1 2c-2 0-2 1-3 2 1-7 2-13 6-19 2-4 5-7 7-10 2-2 3-5 5-7 9-9 19-15 29-23l1 1c1 0 2 0 3-1 1 0 2-1 4-1h0c3 0 8-2 12-3z"></path><path d="M362 619h9c1 1 2 1 3 1v1h-2 0c-4 0-7 0-10-2z" class="G"></path><defs><linearGradient id="O" x1="272.199" y1="320.745" x2="233.786" y2="310.446" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#O)" d="M224 280h10 1c3 1 5 4 8 7 2 1 4 4 6 4 2 1 5 0 6 2 0 1 0 2 1 3v2c0 1 0 1 1 3v3c0 1 0 1 1 3 0 1-1 2 0 4 0 1 0 1 1 3v1h-1c0 1 0 1 1 2 0 1 0 2 1 2v3l2 5c2 4 4 8 5 13 1 1 1 3 1 5 1 4 5 9 4 13-5-14-11-28-19-40-4-6-7-12-11-18 0-1-1-2-2-3v-1c-3-3-7-6-11-9h0c-2 0-1 0-2-1h0 2v-1c-1-1-2-2-4-2 1-1 1-1 2-1h0-3c-1 0 0 0-1-1l1-1z"></path><path d="M224 280h10 1v1c1 2 4 2 4 5l1 1-1 1 1 2c0 1 0 1-1 1h-1c-1-2-2-3-4-4h0 0v1l2 2 4 6h0c-3-3-7-6-11-9h0c-2 0-1 0-2-1h0 2v-1c-1-1-2-2-4-2 1-1 1-1 2-1h0-3c-1 0 0 0-1-1l1-1z" class="J"></path><path d="M243 287c2 1 4 4 6 4 2 1 5 0 6 2 0 1 0 2 1 3v2c0 1 0 1 1 3v3c0 1 0 1 1 3 0 1-1 2 0 4 0 1 0 1 1 3v1h-1l-2-2c-1-4-2-7-3-10-1 0-1-1-2-2l-1-2c-1-1-1-2-2-3v-1c-2-3-5-4-5-8z" class="V"></path><defs><linearGradient id="P" x1="666.541" y1="657.677" x2="713.74" y2="637.998" xlink:href="#B"><stop offset="0" stop-color="#989798"></stop><stop offset="1" stop-color="#c9c8c7"></stop></linearGradient></defs><path fill="url(#P)" d="M665 619c3 1 7 3 11 3-2-1-9-5-10-6 4-1 9 1 12 3 4 3 7 6 11 8 2 2 6 4 9 6 5 4 10 9 15 14l12 17c0-1-1-3 0-4 1 1 2 2 2 3 3 3 4 6 6 9h0l2 6h-1c-1 1-1 3-1 4 1 2 0 2 0 4l-1-3-2 1c-1-1-1-2-1-4-1-1-2-2-2-4l-1 1c-1-1-4-6-4-7-2-4-3-7-5-11-3-4-6-8-9-11-14-15-31-22-52-23-1-1-2-1-3-1l1-2h4l1-1c2 0 4 0 6-2z"></path><defs><linearGradient id="Q" x1="732.438" y1="676.303" x2="726.456" y2="663.02" xlink:href="#B"><stop offset="0" stop-color="#1f1f20"></stop><stop offset="1" stop-color="#3f3f3f"></stop></linearGradient></defs><path fill="url(#Q)" d="M725 664c0-1-1-3 0-4 1 1 2 2 2 3 3 3 4 6 6 9h0l2 6h-1c-1 1-1 3-1 4 1 2 0 2 0 4l-1-3c-1-2-4-8-4-10l1 2c1-4-2-8-4-11z"></path><defs><linearGradient id="R" x1="490.956" y1="216.305" x2="470.023" y2="157.739" xlink:href="#B"><stop offset="0" stop-color="#9c9a9b"></stop><stop offset="1" stop-color="#f5f4f4"></stop></linearGradient></defs><path fill="url(#R)" d="M462 176l-1-2h0 1c1 0 2 0 4-1h0l-1-1c-4-3-6-8-7-13 2 2 5 4 7 5l12 3c9 4 18 15 20 25l1 1c2 6 6 13 6 19v4c-2-2-4-6-5-9l-3-6c-3-6-7-12-12-16-6-4-12-3-18-3l-4-6z"></path><path d="M498 193h-2v1c-3-6-7-13-12-18l1-1c6 2 9 12 12 17h0l1 1z" class="C"></path><defs><linearGradient id="S" x1="286.445" y1="406.64" x2="236.352" y2="371.151" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#ebebeb"></stop></linearGradient></defs><path fill="url(#S)" d="M235 386c-4-5-5-9-6-15 3 7 6 11 14 13 4 1 8 1 13 0 2 0 5-1 7 0 2 0 4 1 6 2-2-6-3-12-5-17-2-4-4-6-6-9 8 4 11 11 13 20l1 4c1 6 0 12 0 18-1 12 4 22 10 32l-1-1h-1l-1-1-2-2-1-1-1-1c-2-3-5-6-7-9v-1c-1-1-2-3-3-5-1-5 0-10-1-15h0c-4-2-9-3-13-4-6-2-11-4-16-8z"></path><path d="M235 386c3-1 4-1 7 0 1 0 1 0 2 1 4 1 8 0 12 1h2l1 1h0c1 0 1 0 2 1h0c2 1 2 2 3 4v1h-1l1 1v2c-4-2-9-3-13-4-6-2-11-4-16-8z" class="H"></path><path d="M253 648c1-6 3-10 6-16-1 9-1 17 5 24 2 3 5 5 7 7 7 3 11 4 18 2 3-1 5-2 8-3h2c-1 2-3 3-4 5s-3 5-4 7h-1c-1 0-1 0-2 1l-1 1c-1 1-2 2-3 2l-1 1c-1 1-2 1-4 2h0-2l-1 1c-2 0-4 0-6-1h-2c-1 0-2-1-3-1l-1-1c-1 0-1 0-2-1h0-1c-2-1-5-5-5-7l-1-4h0c-2-6-2-12-2-19z" class="B"></path><path d="M263 673c3 0 5 1 7 1 8 1 14-2 20-7-2 1-3 1-5 2-7 1-12-1-18-5-2-1-4-3-6-4l1-1c2 2 4 4 7 5 1 0 1-1 2-1 7 3 11 4 18 2 3-1 5-2 8-3-5 6-14 13-22 13-4 1-8 0-12-2z" class="S"></path><path d="M297 662h2c-1 2-3 3-4 5s-3 5-4 7h-1c-1 0-1 0-2 1l-1 1c-1 1-2 2-3 2l-1 1c-1 1-2 1-4 2h0-2l-1 1c-2 0-4 0-6-1h-2c-1 0-2-1-3-1l-1-1c-1 0-1 0-2-1h0-1c-2-1-5-5-5-7l-1-4h0 1c1 2 5 6 7 6 4 2 8 3 12 2 8 0 17-7 22-13z" class="D"></path><defs><linearGradient id="T" x1="749.334" y1="331.913" x2="786.06" y2="334.543" xlink:href="#B"><stop offset="0" stop-color="#949393"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#T)" d="M781 288c2-2 4-4 7-6 1 2 2 3 2 5l-1 1c0 1 0 1 1 2 0 1-2 2-3 3 0 1 0 0-1 1 0 0-1 0-1 1-3 3-4 6-5 10v2c-3 4-7 8-9 12-4 8-8 15-11 23-3 5-5 10-6 16-2 5-2 10-3 15-1 4-1 8-1 13v4h0l-1-5-1 3v-5c-1-2-2-4-2-6 0-3 0-6-1-10v-2c0-1 0-2 1-3l2 2 1-3c1-8 4-16 6-23 3-7 4-14 7-20h1l1-1c0-1 1-1 2-2l3-8h-1v-5c1-1 1-2 1-3v-6c2-2 5-1 7-2s4-2 5-3z"></path><path d="M769 307l1-1c1-2 1-2 2-3-3 6-5 12-7 18l-6 14-1-1c1-7 5-13 8-19l3-8z" class="S"></path><path d="M762 318h1l1-1c0-1 1-1 2-2-3 6-7 12-8 19l1 1c-1 3-3 7-4 11-2 4-3 12-6 15 1-8 4-16 6-23 3-7 4-14 7-20z" class="G"></path><path d="M769 293c2-2 5-1 7-2s4-2 5-3l1 1h2l1 1c-3 4-8 7-11 11l-2 2c-1 1-1 1-2 3l-1 1h-1v-5c1-1 1-2 1-3v-6z" class="V"></path><path d="M731 665h4c1 1 2 0 2 1 1 0 1 1 2 1 6 2 13 1 18-2v-1c-2 1-4 2-7 3-2 0-7 1-9-1h1c8-1 14-5 19-11s6-13 6-21h0l1 1c2 4 5 8 6 12 2 4 4 10 3 13-2 2-3 5-5 6l-1 3c-1 1-2 4-4 5v1c-1 1-1 2-2 2-1 1-1 0-2 1s-2 2-3 2c-1 1 1 0-1 1h-2l-1 1c-2 0-5 0-7-1-2 0-4 0-5-1l-1-1c-1 0-2-1-2-1-2-1-2-2-3-3-2-1-3-2-5-3h0 0c-2-3-3-6-6-9l1-1 3 3z" class="B"></path><path d="M768 635c2 4 5 8 6 12-2 3 0 8-2 12 0-5 0-11-1-16-1-3-3-4-3-8z" class="O"></path><path d="M774 647c2 4 4 10 3 13-2 2-3 5-5 6l-1 3c-1 1-2 4-4 5 1-3 3-5 3-8 1 0 2-6 2-7 2-4 0-9 2-12z" class="P"></path><path d="M728 662l3 3c5 5 10 9 17 10 4 1 9 0 13-2h1c2-1 6-7 8-7 0 3-2 5-3 8v1c-1 1-1 2-2 2-1 1-1 0-2 1s-2 2-3 2c-1 1 1 0-1 1h-2l-1 1c-2 0-5 0-7-1-2 0-4 0-5-1l-1-1c-1 0-2-1-2-1-2-1-2-2-3-3-2-1-3-2-5-3h0 0c-2-3-3-6-6-9l1-1z" class="R"></path><defs><linearGradient id="U" x1="542.722" y1="207.081" x2="548.717" y2="158.911" xlink:href="#B"><stop offset="0" stop-color="#b6b5b6"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#U)" d="M530 188c3-8 9-15 16-19 4-2 9-3 13-5 3-2 6-3 8-5-1 5-3 9-6 13l-1 1c2 1 3 0 5 1l-1 3h0c0 1 0 2-1 2v1l-1 1c1 0 1 1 2 1v1 1c2-1 3-1 4-2l2 1c-5 1-9 3-13 5-8 2-15 6-21 11-1 1-3 2-4 4l-7 7c0 1 0 2-1 3-1 2-3 4-4 7l-2 1 2-9c3-8 6-17 10-24z"></path><path d="M530 188c1 0 1-1 1-1 2-3 5-6 8-9v1l-5 5c-3 5-4 10-6 15-1 2-2 4-2 6 0 1 0 1 1 1l-2 4c0 1 0 2-1 3-1 2-3 4-4 7l-2 1 2-9c3-8 6-17 10-24z" class="C"></path><path d="M564 177h0c0 1 0 2-1 2v1l-1 1c1 0 1 1 2 1v1 1c2-1 3-1 4-2l2 1c-5 1-9 3-13 5-8 2-15 6-21 11-1 1-3 2-4 4l-7 7 2-4c3-7 8-15 15-20 5-5 11-4 17-4l5-5zm84 397c0 1 0 2-1 4l1 1c3 5 11 9 15 12l13 11v1l3 4v1h0l-3-1c-3-2-7-4-10-6v1c-1 0-2-1-3-1h-1c-1-1-1-1-2-1h-1 0l-1-1c-1 0-2 0-3 1h0c-2 0-2 0-3 1s-2 1-3 1l-2 2v1c2 0 3 2 3 3l1 1v1h0v2l-1 1c0 1 0 1 1 2v2l-1 1h6c-1 0-3 0-5 1h0c-2 2-3 2-6 3h0c-5 2-10 5-14 7-2 0-3 1-5 0l2-4 6-15 4-10 6-17 4-9z" class="Q"></path><path d="M659 598c-3 0-5 1-7 2-1 0-1 1-2 1 0-2 1-3 2-5 1 1 3 2 4 2h3z" class="C"></path><path d="M629 625c5-4 15-5 22-6-2 2-3 2-6 3h0c-3-1-8 0-10 1l-5 2h-1z" class="O"></path><defs><linearGradient id="V" x1="629.431" y1="623.503" x2="637.106" y2="627.741" xlink:href="#B"><stop offset="0" stop-color="#3a383a"></stop><stop offset="1" stop-color="#535454"></stop></linearGradient></defs><path fill="url(#V)" d="M629 625h1l5-2c2-1 7-2 10-1-5 2-10 5-14 7-2 0-3 1-5 0l2-4h1z"></path><defs><linearGradient id="W" x1="640.021" y1="606.021" x2="648.861" y2="608.886" xlink:href="#B"><stop offset="0" stop-color="#6e6d6d"></stop><stop offset="1" stop-color="#8e8d8e"></stop></linearGradient></defs><path fill="url(#W)" d="M638 600l1 1c3 0 9 5 10 7 1 1 1 2 1 3-1 1-4 0-5 0-4 0-7 0-10 1l-1-1v-1l4-10z"></path><defs><linearGradient id="X" x1="663.037" y1="583.544" x2="662.966" y2="607.456" xlink:href="#B"><stop offset="0" stop-color="#878888"></stop><stop offset="1" stop-color="#d3d1d2"></stop></linearGradient></defs><path fill="url(#X)" d="M648 574c0 1 0 2-1 4l1 1c3 5 11 9 15 12l13 11v1l3 4v1h0l-3-1c-3-2-7-4-10-6-3-1-5-2-7-3h-3c-1 0-3-1-4-2 2-1 3-2 5-2v-1c-4-2-8-3-11-7h0l-2-3 4-9z"></path><path d="M648 574c0 1 0 2-1 4l1 1c3 5 11 9 15 12l13 11v1c-5-4-9-8-14-12-5-3-10-5-14-9h0c-1 1-2 2-2 4l-2-3 4-9z" class="L"></path><defs><linearGradient id="Y" x1="316.552" y1="437.233" x2="254.075" y2="443.341" xlink:href="#B"><stop offset="0" stop-color="#a8a6a7"></stop><stop offset="1" stop-color="#eeeeed"></stop></linearGradient></defs><path fill="url(#Y)" d="M279 442c-5-5-10-11-16-16-4-3-8-6-13-8 3-5 5-11 8-15 3 7 5 11 10 16 2 3 5 6 7 9l1 1 1 1 2 2 1 1h1l1 1h1c0 4 7 11 10 13 1 1 2 1 3 1 7 9 16 17 25 24-1 1-1 3-3 4-7-3-14-7-21-11l-16-11c-3-3-6-4-9-5l-1-1 1-1v-1c2 0 4-1 6-2l1-1v-1z"></path><path d="M279 442l11 9 6 6c-5-2-10-9-15-10-2-1-7 0-9 0v-1c2 0 4-1 6-2l1-1v-1z" class="L"></path><defs><linearGradient id="Z" x1="465.379" y1="186.686" x2="459.246" y2="203.996" xlink:href="#B"><stop offset="0" stop-color="#9c9b9b"></stop><stop offset="1" stop-color="#bebcbd"></stop></linearGradient></defs><path fill="url(#Z)" d="M453 182v-1c1 0 1 1 2 1s2 0 3 1h1l2-1c1 0 1 0 2-1v-1l-1-1v-1c-1 0-1-1-1-2h1l4 6c6 0 12-1 18 3 5 4 9 10 12 16l3 6c1 3 3 7 5 9v-4c1 2 1 3 1 4l-1 1c0 2 0 3-1 4l2 3c-1 1-1 1-1 2-6-9-14-17-23-23-1-2-5-3-7-4-1 0-5-3-6-3-5-3-11-7-17-9h-1c-1-1-3-1-4-1-3-1-7-2-11-2-7-2-14-3-22-2-3 1-6 2-9 2h-1 0l1-1h1c1 0 1-1 2-1l9-1v-2c-1-1-3-1-4-1h11c7 0 14 1 21 2 3 1 6 2 9 2z"></path><path d="M423 178c7 0 14 1 21 2 3 1 6 2 9 2l1 1h-2c-2 1-6-1-8-1l-12-1c-2-1-5-1-7-1-1 0-1-1-2-2z" class="N"></path><path d="M474 199c2 0 4 1 6 2 6 3 12 7 17 12l6 8 2 3c-1 1-1 1-1 2-6-9-14-17-23-23-1-2-5-3-7-4z" class="M"></path><path d="M453 182v-1c1 0 1 1 2 1s2 0 3 1h1l2-1c1 0 1 0 2-1v-1l-1-1v-1c-1 0-1-1-1-2h1l4 6c6 0 12-1 18 3 5 4 9 10 12 16l3 6h-1c-5-7-12-13-21-17-3-1-7-2-11-4-4-1-8-3-12-3l-1-1z" class="Q"></path><defs><linearGradient id="a" x1="623.11" y1="646.45" x2="716.967" y2="643.84" xlink:href="#B"><stop offset="0" stop-color="#030302"></stop><stop offset="1" stop-color="#353435"></stop></linearGradient></defs><path fill="url(#a)" d="M655 619h10c-2 2-4 2-6 2l-1 1h-4l-1 2c1 0 2 0 3 1 21 1 38 8 52 23 3 3 6 7 9 11 2 4 3 7 5 11 0 1 3 6 4 7 3 8 5 17 5 26-1 4 0 9-1 13v-1c0-16-1-32-10-46-3-5-8-10-12-14 0-2-6-7-7-9-6-5-12-10-21-10l-6-2 1 1c-2 1-3 1-5 1h1 3c-1 1-5 0-7 0h0-1v-1c-1 1-2 1-3 2l-1-1h0 2v-1h-1-1c-1 0-1 1-2 1h0c-1-1-1-1-2-1s-1 0-1-1c-2 1-4 2-6 2-3 1-6 3-8 6-2 0-2 0-3-1l-1 1-1-1-1 1h-2l-2 1-3 3v1h0l-1 2h-1c0-2-1-4-1-5l-1-1 2-2-4 3c-1-2-1-2-1-4 1-2 3-3 4-5l-1-1c1-1 2-1 3-2 0-1 1-2 2-3 4-2 9-5 14-7h0c3-1 4-1 6-3h0 4z"></path><defs><linearGradient id="b" x1="641.123" y1="621.17" x2="650.229" y2="627.058" xlink:href="#B"><stop offset="0" stop-color="#6a686a"></stop><stop offset="1" stop-color="#8f8f8f"></stop></linearGradient></defs><path fill="url(#b)" d="M655 619h10c-2 2-4 2-6 2l-1 1h-4l-1 2c1 0 2 0 3 1-4 0-8 0-11 1l-1 1c-7 1-12 4-17 8l-1-1c1-1 2-1 3-2 0-1 1-2 2-3 4-2 9-5 14-7h0c3-1 4-1 6-3h0 4z"></path><path d="M655 619h10c-2 2-4 2-6 2h-5l1-1h-2l2-1z" class="N"></path><defs><linearGradient id="c" x1="630.272" y1="651.375" x2="669.235" y2="626.689" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#a3a2a3"></stop></linearGradient></defs><path fill="url(#c)" d="M628 641c3-3 7-5 10-7 13-6 24-5 36 0l1 1c-2 1-3 1-5 1h1 3c-1 1-5 0-7 0h0-1v-1c-1 1-2 1-3 2l-1-1h0 2v-1h-1-1c-1 0-1 1-2 1h0c-1-1-1-1-2-1s-1 0-1-1c-2 1-4 2-6 2-3 1-6 3-8 6-2 0-2 0-3-1l-1 1-1-1-1 1h-2l-2 1-3 3v1h0l-1 2h-1c0-2-1-4-1-5l-1-1 2-2z"></path><path d="M301 676l3 1v1l-3 15c-2 15 0 33 4 48 5 22 17 46 36 58 4 2 8 5 12 6 1 1 3 2 4 2 1 1 2 2 3 2l16 4h1c2 2 4 2 7 2h2v1c-3 0-6 1-8 0l-4 1-13-3 1 1c-7-3-15-3-22-2v-1h0 1c2-1 3 0 5 0l1-2-3-1h-1l-2-2h-2v-1l-2-1 1-1v-1l-7-6-3-2c-16-15-26-36-31-58 0-4 0-9-1-13-1-12-2-26 1-37 1-4 3-7 4-11z" class="F"></path><path d="M339 806l22 8 1 1c-7-3-15-3-22-2v-1h0 1c2-1 3 0 5 0l1-2-3-1h-1l-2-2h-2v-1z" class="Q"></path><defs><linearGradient id="d" x1="388.319" y1="625.595" x2="323.944" y2="680.645" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#4c4c4d"></stop></linearGradient></defs><path fill="url(#d)" d="M373 619c6 1 11 1 16 3 3 1 6 3 9 5 2 5 5 11 6 16l3 8v5c1 1 0 3 0 4 0-4-1-8-3-12-5-8-18-15-27-17-8-2-16-1-23 2-2 0-5 1-7 2-1 2-2 3-1 5-8 2-13 5-19 9-3 2-7 6-9 9l-2 2c0 1 0 1-1 2l-1 2h0c-2 3-3 6-5 9 0 1 0 1-1 2 0 1-1 2-1 3l-2 4h0c0-2 0-3 1-4v-2l1-2c1-1 1-2 1-2l-1-2-3 8v-1l-3-1 2-4c0-1 0-1 1-1l1-1v-1-2-1c3-10 14-22 23-28 15-11 33-15 52-12l-1-1c-2-2-4-3-7-4h0 2v-1c-1 0-2 0-3-1h2z"></path><path d="M305 666l1 1c3-3 5-7 8-9-2 3-4 7-6 11l-1 1-3 8v-1l-3-1 2-4c0-1 0-1 1-1l1-1v-1-2-1z" class="H"></path><defs><linearGradient id="e" x1="330.565" y1="642.334" x2="335.192" y2="645.975" xlink:href="#B"><stop offset="0" stop-color="#4d4d4d"></stop><stop offset="1" stop-color="#646465"></stop></linearGradient></defs><path fill="url(#e)" d="M316 660v-2l1-1c1-2 5-4 6-6v-1l1-1c1-2 3-5 5-6l18-8c-1 2-2 3-1 5-8 2-13 5-19 9-3 2-7 6-9 9l-2 2z"></path><defs><linearGradient id="f" x1="395.305" y1="624.93" x2="385.45" y2="630.99" xlink:href="#B"><stop offset="0" stop-color="#383738"></stop><stop offset="1" stop-color="#636363"></stop></linearGradient></defs><path fill="url(#f)" d="M373 619c6 1 11 1 16 3 3 1 6 3 9 5 2 5 5 11 6 16l-2-2c-3-6-9-10-15-12-2-1-4-2-7-2v-1l-1-1c-2-2-4-3-7-4h0 2v-1c-1 0-2 0-3-1h2z"></path><defs><linearGradient id="g" x1="891.067" y1="234.744" x2="851.034" y2="261.318" xlink:href="#B"><stop offset="0" stop-color="#c3c2c2"></stop><stop offset="1" stop-color="#ecebeb"></stop></linearGradient></defs><path fill="url(#g)" d="M847 230c4-7 8-11 16-14-4 6-6 12-7 19-2 6-1 11 2 16 3 6 10 11 17 12 2 1 6 1 9 1h0c7-1 15-2 20-4 2-1 5-2 7-3-1 2-8 9-8 9l-6 4c-8 5-18 7-27 5h-1c-3-1-5-2-7-3-9-5-15-13-18-22-1-7 0-13 3-20z"></path><path d="M708 655c4 4 9 9 12 14 9 14 10 30 10 46v1c-1 6-1 14-1 20l-1 2c-5 20-13 42-30 56-4 3-8 8-13 11v2l-23 7-7 1c-1 0-3 0-3 1l-6 1h-21v-1c5 0 10-1 15-2l12-2c10-2 19-5 28-10 20-13 34-33 39-55 3-9 4-19 4-28l1-1v-1c1-3 1-7 1-10-1-15-3-30-11-43-1-3-6-7-6-9z" class="F"></path><path d="M640 814l12-2-1 1c-2 1-5 1-7 1-1 0-2 1-2 1 0 1 1 1 2 2v-1h3 1 1 3l-6 1h-21v-1c5 0 10-1 15-2z" class="B"></path><path d="M507 163c-1 2 0 5-1 7h-1v-1c0-2 0-4-1-6 0-3-1-6 0-9v-2c-2 5-1 9 0 15v4c1 1 1 2 0 3h1c1-1 1-4 3-5-1 4-3 7-5 11-1 3-1 6-2 9 0-11-3-21-4-32-1-5 0-10-1-14-1-2-2-4-3-5 1-1 2-1 4-1s3 1 5 2c0-7-2-12-4-18 4 3 9 9 11 14l1 1c-1-10 0-22 3-31 2 10 3 21 3 31 3-6 7-10 12-15 0 2-2 4-2 6-2 4-3 8-3 13h1v-1c3-2 5-2 8-2l1 1h-1c0 2-1 3-2 4-1 5 0 10-1 14 0 6-2 12-3 18l-3 12c-1-2-1-4-1-6h-1c0-2-2-3-2-5-1-2 0-5 0-7 0-3-1-6 0-9 0-5 0-9-4-12-1-1-2-2-3-2-2 1-4 2-4 3-2 4-2 11-1 15z" class="T"></path><path d="M519 159h1v-8h0c1 1 2 1 2 3h0c1 4 1 8 0 12v4c0 2-1 5 0 7v3h-1c0-2-2-3-2-5-1-2 0-5 0-7 0-3-1-6 0-9z" class="J"></path><defs><linearGradient id="h" x1="716.702" y1="425.505" x2="754.108" y2="439.631" xlink:href="#B"><stop offset="0" stop-color="#989798"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#h)" d="M745 367c1 4 1 7 1 10 0 2 1 4 2 6v5l1-3 1 5h0c1 8 1 16 0 24-1 7-4 14-8 20 6-6 9-12 11-20l1-1c2-10-2-19 0-28v-1 6l1-3h0c1 1 1 1 1 2 1 1 2 2 2 3v1c2 2 1 5 3 6h1c0 6 0 11-2 17v1l-1 1c0 3-3 5-4 7 1 0 1 0 2-1s2-1 3-1c1-1 1-2 2-3h1l1 1c-1 1-2 2-2 4-2 1-2 0-3 2l1 1 1-1h1l-15 14 2 1-2 2h1c3 1 5 1 7 2h1 0c1 1 3 1 3 2h-3l-6 3c-7 4-14 10-21 14-7 5-15 8-23 11-3 2-7 4-11 5l-1-1c16-12 30-22 40-40 11-17 14-34 9-54 0-3-1-14 1-15 0 0 0 2 1 3h0c0-2 0-3-1-5l1-2z"></path><path d="M749 447c-7 0-11 4-16 8h-1l15-14 2 1-2 2h1c3 1 5 1 7 2h1 0c1 1 3 1 3 2h-3l-6 3c-1-1-1-2-2-3l1-1z" class="L"></path><path d="M749 447c3 0 5 1 7 1l-6 3c-1-1-1-2-2-3l1-1z" class="H"></path><defs><linearGradient id="i" x1="755.825" y1="502.513" x2="676.164" y2="593.176" xlink:href="#B"><stop offset="0" stop-color="#b3b2b2"></stop><stop offset="1" stop-color="#dddcdd"></stop></linearGradient></defs><path fill="url(#i)" d="M738 501l8 2v-1h-1c1-1 2-2 3-2 5 1 9 4 13 7 2 1 3 3 4 5l4 4c3 5 4 11 2 16-1 3-3 6-6 8s-8 1-12 0c-1 0-2-1-2-2-1 0-2-1-3-2-2-5 2-6 3-10v-1h0c0-1 0-1 1-2s1-1 1-2v-3c-1 0-2-1-3-1l-1-1h0-1c-2-1-3-1-4-2h0c-1-1-2 0-3 0-1-1-2-1-3-1h-7c-1 0-2 1-3 1h-2c-1 0-1 0-1 1h-1c-1 0-2 0-3 1l-1 1v-2c-13 6-26 17-32 31-3 8-5 17-5 26-1 4-1 8 0 12h0c0 1 1 3 1 4s1 3 1 4c-1 1-1 1 0 2v3 1c0 2 0 3 1 4h0c0 2 1 3 1 4h0c-10-17-12-34-7-53 2-8 5-15 7-23v1h2v1l1 1c0-1 0-1 1-2-1-3 4-11 5-14 1-2 2-3 4-5 2-3 7-4 11-6 2 0 4-2 6-2 0 0 3-1 4-1l2-1h2l2-1h3c3-1 5-1 8 0z"></path><path d="M751 525l2-1h0c0 2-1 7 0 9 2-2 4-4 7-5 0 1 1 1 1 2v3l-3 1c0 1-1 1-2 1h-1v1h1c-2 0-2 0-3-1l-1-2h0c0-2 0-3 1-4l-1-3c-2 3-3 5-3 9 0 1 1 2 2 3h0c-1 0-2-1-3-2-2-5 2-6 3-10v-1z" class="D"></path><path d="M720 515c10-3 19-4 29 0 5 2 11 7 13 12 1 1 1 2 0 4 0 1-1 1-1 2v-3c0-1-1-1-1-2-3 1-5 3-7 5-1-2 0-7 0-9h0l-2 1h0c0-1 0-1 1-2s1-1 1-2v-3c-1 0-2-1-3-1l-1-1h0-1c-2-1-3-1-4-2h0c-1-1-2 0-3 0-1-1-2-1-3-1h-7c-1 0-2 1-3 1h-2c-1 0-1 0-1 1h-1c-1 0-2 0-3 1l-1 1v-2z" class="Q"></path><path d="M717 504s3-1 4-1l2-1h2l2-1h3c3-1 5-1 8 0l-1 1h3c1 1 0 1 1 1s2 0 3 1h1l1 1h1l2 2v1l3 2c-2 1-3-1-4 0h1l-1 1-4-2h-2c-2 0-1 0-2-1h-9l-1 1h-3c-1 0-1 0-2 1h-2l-3 1-8 4c-1-1-1 0-1-1-5 2-10 6-14 10-1 1-2 2-3 2l-3 5c-1-3 4-11 5-14 1-2 2-3 4-5 2-3 7-4 11-6 2 0 4-2 6-2z" class="B"></path><path d="M713 506l2 1c1-1 2-2 4-2l2-1h2 0c-1 1-1 1-3 1-2 1-5 3-7 4v1c3 0 7-3 9-2h0l-5 2v1h0 3l-8 4c-1-1-1 0-1-1-5 2-10 6-14 10-1 1-2 2-3 2l-3 5c-1-3 4-11 5-14 1-2 2-3 4-5 2-3 7-4 11-6 2 0 4-2 6-2-1 1-3 1-4 2z" class="V"></path><path d="M696 517c1-2 2-3 4-5 2-3 7-4 11-6 2 0 4-2 6-2-1 1-3 1-4 2-7 4-16 13-19 20l-3 5c-1-3 4-11 5-14z" class="L"></path><defs><linearGradient id="j" x1="286.729" y1="500.5" x2="322.018" y2="608.382" xlink:href="#B"><stop offset="0" stop-color="#b5b4b4"></stop><stop offset="1" stop-color="#e8e7e8"></stop></linearGradient></defs><path fill="url(#j)" d="M254 524c0-4 3-8 5-12 8-10 22-15 34-17 4 1 9 2 13 3s8 3 12 4c4 3 7 6 10 10h0l2 4c3 5 7 10 9 16 7 16 12 38 8 56-2 7-5 13-9 20h0l1-3c0-2 1-2 0-4 0-2 1-3 1-5v-8c0-1 1-2 1-3v-1c2-2 1-8 1-11l1 3c1-8-1-16-3-24-4-15-17-28-31-35-9-5-19-6-28-3-7 2-14 6-17 12-1 2-1 4 0 6 0 1 1 1 2 2h4 2c3-4 1-8 1-12 2 3 5 7 6 11 0 2-1 3-2 4-2 3-6 4-9 4s-6 0-8-2h-1l-1-2c-3-3-4-8-4-13z"></path><path d="M283 507c2 0 3-1 4 0h-1l-1 1h-3l-1 1c-4 1-8 3-12 5-1 1-1 2-2 2v-1c2-2 6-6 9-6 3-1 5-2 7-2z" class="I"></path><path d="M342 573l1 3c0 9-1 17-4 25 0-2 1-3 1-5v-8c0-1 1-2 1-3v-1c2-2 1-8 1-11z" class="M"></path><path d="M291 502h10 1l6 3c2 0 4 1 5 2s2 1 3 2l1 1c2 1 4 3 5 4h-1c-2-1-3-3-5-4l-6-3c0-1-1-1-2-1v-1c-6-2-12-1-18-2l1-1z" class="C"></path><path d="M311 504c2 0 5 2 7 3 3 2 7 3 9 6 2 1 2 2 3 4 1 3 3 6 3 10v-1-1c-1-1-1-2-1-3-2-4-5-5-8-8-4-4-8-7-13-10z" class="L"></path><path d="M254 524c0-4 3-8 5-12 8-10 22-15 34-17 4 1 9 2 13 3s8 3 12 4c4 3 7 6 10 10h0l2 4v1c-1-2-1-3-3-4-2-3-6-4-9-6-2-1-5-3-7-3h0c-1 0-2 0-2-1h-1l-6-2h-2l-2-1c-2 0-7 0-8 1h-2c-2 1-7 4-9 3-1 0-2 0-3 1h-1l-2 1h-1c-2 0-2 1-4 1l-1 1c0 1-1 1-2 2h0c-1 1-2 1-3 2-3 1-4 5-5 7-1 1-1 2-1 3-1 1-1 2-2 2z" class="B"></path><path d="M414 818v-1l-3-1 1-1c9 3 17 4 26 5l4 1h3l5 1 7 2 4 1h3l7 2h3l37 91c8-15 14-30 20-45l18-47 1 1c2 0 4-1 5-1l3-1 15-3 10-1 23-4 1 1-1 1h1c3 0 7-1 11-2 2 0 4-1 7-1v1l-5 1h0l15 1-3 2h2l-1 1h0-2c-1 0-3-1-4-1h-5 0-2c-2-1-3 0-5 0h-2c-1 0-3 3-3 4h0c2 0 4 1 5 2h-1c-7-2-13-4-20-5-2 0-4-1-6 0 8 2 15 4 21 11h0c0 1 1 3 1 5h-1 0v-2c-1-1-1-1-1-2s-1-1-1-2v-1l-2-1h0c-3-2-5 0-7-2-2 0-3 1-4 0-2 0-5 0-6-1h-5-3c-1 1-3 0-5 1h-3c-1 0-1 0-2 1h-2-2l-1 1h-1c-1 1-2 1-2 2-2 0-3 1-4 2l-1 1-1 1h-1l-5 5v1l-1 2h-1l-1 3-2 2-3 6v1c0 1 0 1-1 2 0 1 0 3-1 4v1h0c-1 1-1 2-1 2l-3 9c0 1-1 2-1 3l-3 10s-1 1-1 2h0l-1 3c-1 1-1 2-2 3v1 2l-2 6c-1 1-1 2-2 3 0 2-1 3-1 4l-2 5c-1 1-1 1-1 2v1l-1 1-2 2h-1c0 1-1 1-2 2l-1-1-1 1c-4 0-5-1-8-3v-1c-1-1-1-2-2-3v-2h-1l-1-3v-1c-1-1-2-3-2-4s-1-3-1-4l-4-10c0-1 0-1-1-1l1-1c-1-1-1-2-1-4-1-1-2-2-2-4 0-1-1-2-1-4l-4-9v-2l-1-2v-1h0c-1-1-1-2-1-3s-1-2-1-3-1-1-1-2v-1l-1-2h0l-1-1h0v-2c-1-1-1-1-1-2 0 0-1-1-1-2l-1-1-2-4-1-1-1-2h-1c0-1-1-2-2-3l-1-1-1-1c-1 0-2-1-2-1l-1-1c-1 0-1-1-2-1l-2-1c-1-1-2-1-2-1h-1c-1 0-2-1-2-1h-2c-1 0-1 0-1-1h-5 0c-2 0-5-1-6 0h-2c-2 0-5 0-6 1-1 0-5 0-6-1l-1 1h-1l-1 1c-1 1-3 3-5 4l-1 2h-1v1 1 1c-1 0 0 1-1 1v1c0-2 0-4 1-7-2 0-3-1-4 0-3 2-6 5-8 9v1h-1 0c-1 1-1 2-2 3-2-1-5-1-7-2 4-8 9-12 16-17h-1c1-1 2-1 3-2 1 0 2 0 2-1l-2-1v-1h-1c-1 0-1 0-2-1-1 0-2-1-3 0h-2-1-5-3 0c-1 0-2 1-4 1h0-1-2c-2 1-3 1-5 0l9-1v-2l12-1h-2v-1l11 1z" class="Q"></path><path d="M403 817l11 1 8 1-29 2v-2l12-1h-2v-1zm203 2h1c3 0 7-1 11-2 2 0 4-1 7-1v1l-5 1h0l15 1-3 2c-1 0-4 0-6-1h-22c0-1 1-1 2-1z" class="G"></path><defs><linearGradient id="k" x1="414.411" y1="825.734" x2="416.884" y2="836.556" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#a2a1a2"></stop></linearGradient></defs><path fill="url(#k)" d="M411 827c8-3 18-6 27-5-8 2-16 4-21 11-2 0-3-1-4 0-3 2-6 5-8 9v1h-1 0c-1 1-1 2-2 3-2-1-5-1-7-2 4-8 9-12 16-17z"></path><defs><linearGradient id="l" x1="656.977" y1="512.12" x2="708.523" y2="554.38" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#l)" d="M688 473c0-1 0-2 1-3 1 0 1 1 2 1l3 9 1 1-1 1c0 10 1 20-2 30-1 5-4 12-4 18l-1 1v-1c-2 8-5 15-7 23-5 19-3 36 7 53l-1 1c1 1 1 2 2 3h0c-1 2 0 2-1 3 0 0-1-1-1-2-1-1-2-2-4-3v1l1 2h-1l-3-3h0v-1l-3-4v-1l-13-11c-4-3-12-7-15-12l-1-1c1-2 1-3 1-4s1-3 1-4l4-10c0-1 2-3 2-5h0l3-7c3-12 9-23 13-34l17-41z"></path><defs><linearGradient id="m" x1="655.262" y1="554.237" x2="663.462" y2="569.074" xlink:href="#B"><stop offset="0" stop-color="#807f7f"></stop><stop offset="1" stop-color="#b0b0b0"></stop></linearGradient></defs><path fill="url(#m)" d="M658 548l10 26c-1 1-1 1-2 0s-1-2-1-3c-1-1-2-1-2-2-1-1-2-3-2-4h-1c1 3 3 7 3 10 0 0-1-1-1-2-1-2-3-3-4-5s-1-3-1-5c-1-2 0-5-2-8h0l3-7z"></path><defs><linearGradient id="n" x1="680.432" y1="488.727" x2="698.875" y2="498.174" xlink:href="#B"><stop offset="0" stop-color="#8c8d8d"></stop><stop offset="1" stop-color="#b9b7b8"></stop></linearGradient></defs><path fill="url(#n)" d="M688 473c0-1 0-2 1-3 1 0 1 1 2 1l3 9 1 1-1 1c0 10 1 20-2 30-1 5-4 12-4 18l-1 1v-1c2-13 5-28 3-42 0-5-2-10-2-15z"></path><path d="M655 555c2 3 1 6 2 8 0 2 0 3 1 5s3 3 4 5c0 1 1 2 1 2 0-3-2-7-3-10h1c0 1 1 3 2 4 0 1 1 1 2 2 0 1 0 2 1 3s1 1 2 0l18 33c1 1 1 2 2 3h0c-1 2 0 2-1 3 0 0-1-1-1-2-1-1-2-2-4-3v1l1 2h-1l-3-3h0v-1l-3-4v-1l-13-11c-4-3-12-7-15-12l-1-1c1-2 1-3 1-4s1-3 1-4l4-10c0-1 2-3 2-5z" class="I"></path><path d="M677 600c3 3 6 4 7 8l2 3c-1-1-2-2-4-3v1l1 2h-1l-3-3h0v-1l-3-4v-1l1-2z" class="D"></path><path d="M660 586v-3c1 0 2 0 3 1 0 1 0 1 2 1 4 5 7 11 12 15h0l-1 2-13-11c0-3-1-3-3-5z" class="B"></path><path d="M655 555c2 3 1 6 2 8 0 2 0 3 1 5s3 3 4 5c0 1 1 2 1 2l1 1 1 2 1 3v1l-2-2c-1 1-1 0-2 1l3 4c-2 0-2 0-2-1-1-1-2-1-3-1v3c2 2 3 2 3 5-4-3-12-7-15-12l-1-1c1-2 1-3 1-4s1-3 1-4l4-10c0-1 2-3 2-5z" class="O"></path><path d="M648 579l1-1c0 1 0 1 1 1 2 3 4 5 7 6h0c0-1-1-1-2-2-2-2-4-5-6-9 3 2 5 7 8 7 1 1 2 3 3 5 2 2 3 2 3 5-4-3-12-7-15-12z" class="C"></path><defs><linearGradient id="o" x1="650.813" y1="569.133" x2="659.726" y2="581.694" xlink:href="#B"><stop offset="0" stop-color="#787778"></stop><stop offset="1" stop-color="#a3a2a2"></stop></linearGradient></defs><path fill="url(#o)" d="M657 581c-3-3-5-6-7-10 0-1 0-2 1-3 3 1 3 5 6 6 2 2 3 5 5 7l3 4c-2 0-2 0-2-1-1-1-2-1-3-1v3c-1-2-2-4-3-5z"></path><defs><linearGradient id="p" x1="654.968" y1="561.862" x2="661.361" y2="580.075" xlink:href="#B"><stop offset="0" stop-color="#848384"></stop><stop offset="1" stop-color="#bbbab9"></stop></linearGradient></defs><path fill="url(#p)" d="M655 555c2 3 1 6 2 8 0 2 0 3 1 5s3 3 4 5c0 1 1 2 1 2l1 1 1 2 1 3v1l-2-2c-1 1-1 0-2 1-2-2-3-5-5-7-1-3-3-6-3-9 0-2 0-3-1-5 0-1 2-3 2-5z"></path><defs><linearGradient id="q" x1="367.9" y1="515.821" x2="316.6" y2="555.179" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#q)" d="M328 512h1l3 4 1 3c2 2 2 5 4 7v1h1v1-2l-1-3v-1-2c-1-1-1 0-1-1v-2c-1-1-1 0-1-1s0-2-1-2v-3c-1-2-1 0-1-2v-1-1c0-1 0-3-1-3v-1c0-2-1-5 0-7v-5-3-2-3c-2 0-2 0-3-1l-1-1h-1c-2-1 1 0-1-1h-1l-1-1h-1c-1 0-1 0-2-1-1 0-1-1-2-1h-1l-1-1h0-1c-1 0-1 0-2-1l-2-1h0c-1-1-1 0-2-1-1 0-1-1-1-1h-1-1c-1-1-2-1-3-2-1 0-1-1-2-1h0-1l-1-1-1-1c-1 0-1 0-2-1h0c-2 0-2-1-2-1h-1l-1-2c-1 0-1 0-2-1h0l-1-1c-2 0-2-1-2-1h-1c0-1-1-2-1-2h-1l-1-2c-3-1 0 1-2-1h0-1l-1-1h1l16 11c7 4 14 8 21 11 2-1 2-3 3-4l6 4c2 1 4 4 6 4l1-1c0-3 1-7 3-9l1 2 30 77 10 25 3 10c0 1-1 1-2 2-1 2-3 4-5 5l-6 2c2 1 6 3 7 5 1 1 1 2 1 3l-1 1c-2-2-5-4-8-3-1 0-3 0-4 1-5 1-10 4-14 7-1 1-5 5-6 5h-2c0 1-1 1-2 2h-1l1-1v-1h0-2l-2 2 3-6c4-7 7-13 9-20 4-18-1-40-8-56-2-6-6-11-9-16l-2-4z"></path><path d="M327 476c2 1 4 4 6 4v2l-9-4 3-2z" class="G"></path><path d="M321 472l6 4-3 2-6-2c2-1 2-3 3-4z" class="C"></path><path d="M345 599c0 1-1 3-1 4h4c-1 1-2 2-3 4-1 1-3 2-3 4l-1 1h0c0 1-1 1-2 2h-1l1-1v-1h0-2l8-13z" class="H"></path><defs><linearGradient id="r" x1="340.248" y1="492.384" x2="331.477" y2="494.172" xlink:href="#B"><stop offset="0" stop-color="#878788"></stop><stop offset="1" stop-color="#a9a8a9"></stop></linearGradient></defs><path fill="url(#r)" d="M337 470l1 2c0 5-1 9-1 14-1 5-1 10-1 15 0 4 1 9 1 14 1 3 2 6 2 9-2-5-4-10-5-16-1-5-2-11-1-17 0-3-1-6 0-8v-1-2l1-1c0-3 1-7 3-9z"></path><defs><linearGradient id="s" x1="373.44" y1="568.648" x2="367.142" y2="570.38" xlink:href="#B"><stop offset="0" stop-color="#696869"></stop><stop offset="1" stop-color="#868686"></stop></linearGradient></defs><path fill="url(#s)" d="M368 549l10 25 3 10c0 1-1 1-2 2-1 2-3 4-5 5l-6 2c2 1 6 3 7 5 1 1 1 2 1 3l-1 1c-2-2-5-4-8-3-1 0-3 0-4 1-5 1-10 4-14 7-1 1-5 5-6 5h-2 0l1-1c0-2 2-3 3-4 1-2 2-3 3-4h-4c0-1 1-3 1-4 6-14 13-27 18-41l3-9h1 1z"></path><path d="M363 558l3-9h1l1 4c1 3 1 5 0 8l-1-3c0-1 0-2-2-3 0 1 0 1-1 2l-1 1z" class="S"></path><path d="M372 576l2-2 1 2v-3c1 1 1 2 2 3v3l-2 2c-1-1-1-2-1-3-3 0-4 4-7 4l5-6z" class="O"></path><defs><linearGradient id="t" x1="369.053" y1="579.27" x2="356.671" y2="585.149" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#bdbbbb"></stop></linearGradient></defs><path fill="url(#t)" d="M358 583h0c1-1 2-3 3-3l1-1c2-4 4-7 7-10 0 2-2 4-2 6h0 3c-1 1-1 2-2 3s-1 2-2 3c1-1 4-5 6-5l-5 6c-4 2-6 7-10 9l-1 1v-1l2-3h-1c0-2 1-3 1-5z"></path><path d="M377 579v-1c0-1 0-3 1-4l3 10c0 1-1 1-2 2-1 2-3 4-5 5v-5h-1c-3 1-6 2-9 4-4 3-8 8-12 11h-1 0l3-3v-1c1-1 3-3 4-5l-6 5c1-2 3-3 3-4l1-1 1-1c4-2 6-7 10-9 3 0 4-4 7-4 0 1 0 2 1 3l2-2z" class="L"></path><path d="M373 586c2-2 3-3 5-4 1 1 1 2 1 4-1 2-3 4-5 5v-5h-1z" class="S"></path><defs><linearGradient id="u" x1="371.478" y1="578.251" x2="359.801" y2="594.186" xlink:href="#B"><stop offset="0" stop-color="#8d8c8b"></stop><stop offset="1" stop-color="#b5b4b6"></stop></linearGradient></defs><path fill="url(#u)" d="M367 582c3 0 4-4 7-4 0 1 0 2 1 3l-21 17v-1c1-1 3-3 4-5l-6 5c1-2 3-3 3-4l1-1 1-1c4-2 6-7 10-9z"></path><defs><linearGradient id="v" x1="375.116" y1="592.537" x2="344.118" y2="604.385" xlink:href="#B"><stop offset="0" stop-color="#999899"></stop><stop offset="1" stop-color="#cbcaca"></stop></linearGradient></defs><path fill="url(#v)" d="M373 586h1v5l-6 2c2 1 6 3 7 5 1 1 1 2 1 3l-1 1c-2-2-5-4-8-3-1 0-3 0-4 1-5 1-10 4-14 7-1 1-5 5-6 5h-2 0l1-1c0-2 2-3 3-4 1-2 2-3 3-4l3-2h1c4-3 8-8 12-11 3-2 6-3 9-4z"></path><defs><linearGradient id="w" x1="372.97" y1="562.697" x2="337.872" y2="596.688" xlink:href="#B"><stop offset="0" stop-color="#8c8b8c"></stop><stop offset="1" stop-color="#d9d8d7"></stop></linearGradient></defs><path fill="url(#w)" d="M363 558l1-1c1-1 1-1 1-2 2 1 2 2 2 3l1 3v1 4c-1-1-1-1-2-1l1 1v2c-1 3-3 6-5 9h-1-1v1l-1 1c-1 1-1 2-2 4-1 1-2 2-2 3l1 1v-1l2-3c0 2-1 3-1 5h1l-2 3v1l-1 1c0 1-2 2-3 4l6-5c-1 2-3 4-4 5v1l-3 3h0l-3 2h-4c0-1 1-3 1-4 6-14 13-27 18-41z"></path><path d="M367 568h-1-1v-5-3-1c0-1 0-1 1-1h1c0 2 0 4-1 6v1l1 1v2z" class="N"></path><path d="M129 182l-3-4h279 7c1 0 3 0 4 1v2l-9 1c-1 0-1 1-2 1h-1l-1 1h0s-1 1-2 1c-5 0-11 2-16 3-3 1-7 1-10 1H150c-5 0-11 1-15-2-2-1-4-3-6-5z" class="F"></path><path d="M405 178h7c1 0 3 0 4 1v2l-9 1c-1 0-1 1-2 1h-1l-1 1h0s-1 1-2 1c-5 0-11 2-16 3-3 1-7 1-10 1l2-2c1 0 2 0 3-1s4-1 6-2c3-1 7-2 10-2 3-1 7-1 10-2h1l1 1h1 2l2-1v-1c-3 0-5 0-8-1z" class="I"></path><path d="M622 178h277v1l-1 2h1l-1 1h0c-1 2-5 6-6 6l-1 1c0 1 1 2 2 4s1 4 2 6c0 0 1 1 1 2s1 3 1 4c1 1 1 2 2 3h1v1c0 1 1 1 1 2h-1c-2-3-5-6-8-9-3-4-10-9-15-10-6-1-13 0-19 0h-49-152c-3 0-7 1-10 0-2 0-4-1-6-2s-5-2-7-3c-5-1-10-2-15-4l-8-1h0v-1h5c1 1 2 1 3 0h0l8-1c-1-1-2-2-5-2z" class="F"></path><defs><linearGradient id="x" x1="772.908" y1="217.668" x2="768.092" y2="175.832" xlink:href="#B"><stop offset="0" stop-color="#010303"></stop><stop offset="1" stop-color="#292626"></stop></linearGradient></defs><path fill="url(#x)" d="M898 181h1l-1 1h0c-1 2-5 6-6 6l-1 1c0 1 1 2 2 4s1 4 2 6c0 0 1 1 1 2s1 3 1 4c1 1 1 2 2 3h1v1c0 1 1 1 1 2h-1c-2-3-5-6-8-9-3-4-10-9-15-10-6-1-13 0-19 0h-49-152c-3 0-7 1-10 0-2 0-4-1-6-2s-5-2-7-3c-5-1-10-2-15-4l-8-1h0v-1h5c1 1 2 1 3 0h0l8-1c3 1 6 1 9 2 4 2 9 4 13 5l2 2h232c7 0 10-3 15-8z"></path><path d="M627 180c3 1 6 1 9 2 4 2 9 4 13 5l2 2h-5c-7 0-14-3-20-5-3 0-6-1-9-2h-6v-1h5c1 1 2 1 3 0h0l8-1z" class="H"></path><path d="M626 184c6 0 13 1 18 3l2 2c-7 0-14-3-20-5z" class="I"></path><defs><linearGradient id="y" x1="738.874" y1="315.52" x2="796.576" y2="360.76" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232323"></stop></linearGradient></defs><path fill="url(#y)" d="M831 216c9-3 24-6 33-1 6 2 10 6 13 12v1c-6-1-10-1-15 3-2 3-3 5-4 8-1 1-1 2-1 3v4c-1-2-1-3-1-4v-7c1-7 3-13 7-19-8 3-12 7-16 14-3 7-4 13-3 20h0l-1-1v-2h0v-3-3l1-1h-1c0-2 1-3 1-4h0c0-1 0-1 1-2-1-1-1-1-1-2-1 0-1 0-2-1 0-1-1-1-2-2h0l-1-1h-1-5-1c-1 0-1 0-2 1h-2-1c-1 1-2 1-3 1 0 1 0 1-1 1-1 1-3 2-4 3l-8 5-2 1c-1 1-2 3-4 3-1 1-7 7-8 9v1l-3 3-1 1-6 9c0 1 0 2-1 3v1c-1 1-1 1-1 2s0 1-1 2v2c-1 2 0 6 0 9 1-2 3-4 5-5v1l-1 1c-3 2-5 4-7 6-1 1-3 2-5 3s-5 0-7 2v6c0 1 0 2-1 3v5h1l-3 8c-1 1-2 1-2 2l-1 1h-1c-3 6-4 13-7 20-2 7-5 15-6 23l-1 3-2-2c-1 1-1 2-1 3v2l-1 2c1 2 1 3 1 5h0c-1-1-1-3-1-3-2 1-1 12-1 15 5 20 2 37-9 54-10 18-24 28-40 40l-3-9c-1 0-1-1-2-1v-1c2-7 5-14 8-21l16-39c15-38 29-76 47-112 14-28 40-71 71-81z"></path><path d="M741 363h0c0-2 0-2 1-3 2 0 3 1 4 2-1 1-1 2-1 3v2l-1 2c-1-2-1-4-3-6z" class="C"></path><path d="M725 426c-1-3-1-6-2-9 3 4 5 9 6 15l-2-2s-1 0-2-1v-3z" class="I"></path><path d="M740 376l-5-10c0-1 0-1 1-2s3-1 4-1l-1 2c0 1 1 2 1 4 1 2 0 5 0 7z" class="P"></path><path d="M741 363c2 2 2 4 3 6s1 3 1 5h0c-1-1-1-3-1-3-2 1-1 12-1 15l-3-10c0-2 1-5 0-7 0-2-1-3-1-4l1-2h1z" class="N"></path><defs><linearGradient id="z" x1="693.279" y1="468.843" x2="709.142" y2="455.945" xlink:href="#B"><stop offset="0" stop-color="#585759"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#z)" d="M706 456c2 1 4 1 7 1l-12 8c-3 2-6 5-10 6h0c-1 0-1-1-2-1v-1l1 1c3-1 13-11 16-14z"></path><path d="M762 318v-5l-4-1h-1c3-7 8-12 12-19v6c0 1 0 2-1 3v5h1l-3 8c-1 1-2 1-2 2l-1 1h-1z" class="N"></path><defs><linearGradient id="AA" x1="704.727" y1="450.18" x2="730.512" y2="436.738" xlink:href="#B"><stop offset="0" stop-color="#7d7b7d"></stop><stop offset="1" stop-color="#c6c5c5"></stop></linearGradient></defs><path fill="url(#AA)" d="M725 426v3c1 1 2 1 2 1l2 2c0 6-1 11-5 16-3 3-7 7-11 9-3 0-5 0-7-1 4-4 10-7 14-13l1-2c0-2-3-4-2-6h1c1 1 1 2 2 3h0c2-4 3-7 3-12z"></path><path d="M831 216c9-3 24-6 33-1 6 2 10 6 13 12v1c-6-1-10-1-15 3-2 3-3 5-4 8-1 1-1 2-1 3v4c-1-2-1-3-1-4v-7c1-7 3-13 7-19-8 3-12 7-16 14l-1-2c1 0 1-1 2-1v-1l1-1c2-3 4-5 6-7h1c1-1 2-1 4-2 1 0 1-1 2-1-1-1-1-1-2-1h-2c-4-1-9-1-13 0h0c-3 0-6 1-8 1-2 1-4 1-5 1h-1zm-657 6h0l-3-3c-1-1-2-2-3-2l-4-2c1 0 2-1 4-1h0c3-1 8-1 11 0h4 1l1 1h4 1c29 7 50 38 65 63s26 53 37 81l27 65 12 31c2 5 4 11 6 15-2 2-3 6-3 9l-1 1c-2 0-4-3-6-4l-6-4c-9-7-18-15-25-24-1 0-2 0-3-1-3-2-10-9-10-13-1-3-3-6-4-10-2-6-3-12-3-18-1-10 1-19-1-29l-1-8-2-11c1-4-3-9-4-13 0-2 0-4-1-5-1-5-3-9-5-13l-2-5v-3c-1 0-1-1-1-2-1-1-1-1-1-2h1v-1c-1-2-1-2-1-3-1-2 0-3 0-4-1-2-1-2-1-3v-3c-1-2-1-2-1-3v-2c-1-1-1-2-1-3-1-2-4-1-6-2-2 0-4-3-6-4-3-3-5-6-8-7h-1-2 0l3-1 1 1h1l1 1h0l1 1v-2l1-1v-5l-1-1c0-1 0-2-1-3v-1l-1-2v-1l-2-2v-2c-1-1-2-2-3-4l-2-2v-1l-1-1c-1-1-3-2-3-4-2-1-5-5-7-7-2-1-3-2-4-3l-2-2h-1l-3-2c-3-2-6-4-9-5h-1c-2-1-1-1-2-1h-3c-2-1-5-1-7-1-1 1-2 1-3 1h-1-2v3h0v2l1 1h0c1 2 0 2 0 3 1 2 1 8 1 9-1 1-1 2-1 3v1 1l-1 1h0v1c-1 1 0 0-1 2h0c-1 1-1 1-1 2h-1v1 1 1h-1v3c-1 2-2 3-3 5s-4 5-7 7h0c-1 1-2 1-2 1l-1 1h-2v1c-1 0-2 0-3 1h-2c-1 1-1 1-2 1h-2c-2 1-6 1-7 1l-1-1h-3-2s-1-1-2-1-3-1-5-2h0l-2-1h-1c-2-1-5-4-6-6h0c-1-1-2-2-2-3h0c-1-2-1-2-1-3-1-1-1-1-1-3l-4-6c7 3 13 5 21 6h0c8 2 18 0 24-5 5-3 8-8 10-15 0-9-4-19-8-27 5 1 7 3 11 6z" class="Q"></path><path d="M284 361l1 1h0l1 1c1 1 4 1 6 1l-5 9c-1-2-1-5-1-7 0-1-1-2-2-3 0 1-1 2-2 3 0-1 1-2 1-3 1-1 1-1 1-2z" class="S"></path><defs><linearGradient id="AB" x1="321.176" y1="465.923" x2="322.515" y2="456.19" xlink:href="#B"><stop offset="0" stop-color="#575656"></stop><stop offset="1" stop-color="#727274"></stop></linearGradient></defs><path fill="url(#AB)" d="M318 455c4 3 7 6 11 10 2 2 4 4 6 7-9-5-17-11-25-17 2 0 4 0 7 1l1-1z"></path><defs><linearGradient id="AC" x1="316.383" y1="438.943" x2="297.512" y2="439.08" xlink:href="#B"><stop offset="0" stop-color="#7e7e7f"></stop><stop offset="1" stop-color="#bcbaba"></stop></linearGradient></defs><path fill="url(#AC)" d="M310 455c-6-4-11-8-13-15s2-17 6-23c0 2-1 4-1 6-1 3-1 6-1 9 1 2 1 5 3 7v-1c1-1 2-2 3-2 1 1-1 4-2 5 3 6 8 10 13 14l-1 1c-3-1-5-1-7-1z"></path><defs><linearGradient id="AD" x1="274.209" y1="330.618" x2="259.576" y2="331.32" xlink:href="#B"><stop offset="0" stop-color="#6d6d6e"></stop><stop offset="1" stop-color="#969596"></stop></linearGradient></defs><path fill="url(#AD)" d="M255 293c6 5 10 12 12 19-1 2-3 0-4 2 2 11 7 22 10 32 1 4 3 8 3 12-2 3 1 7-2 11l-2-11c1-4-3-9-4-13 0-2 0-4-1-5-1-5-3-9-5-13l-2-5v-3c-1 0-1-1-1-2-1-1-1-1-1-2h1v-1c-1-2-1-2-1-3-1-2 0-3 0-4-1-2-1-2-1-3v-3c-1-2-1-2-1-3v-2c-1-1-1-2-1-3z"></path><defs><linearGradient id="AE" x1="293.102" y1="406.39" x2="275.819" y2="411.591" xlink:href="#B"><stop offset="0" stop-color="#908f8f"></stop><stop offset="1" stop-color="#b9b8b8"></stop></linearGradient></defs><path fill="url(#AE)" d="M276 358l1 5h1c1-1 1-2 3-2 1-1 2 0 3 0 0 1 0 1-1 2 0 1-1 2-1 3 1-1 2-2 2-3 1 1 2 2 2 3 0 2 0 5 1 7-8 21-9 39 0 60l9 15c-1 0-2 0-3-1-3-2-10-9-10-13-1-3-3-6-4-10-2-6-3-12-3-18-1-10 1-19-1-29l-1-8c3-4 0-8 2-11z"></path><path d="M276 358l1 5h1c1-1 1-2 3-2 1-1 2 0 3 0 0 1 0 1-1 2 0 1-1 2-1 3-1 1-1 1-1 2v2l-1 1v2c0 1 0 0 1 1l-2 8v-1h-1c-1-2-1-2-1-4h-2l-1-8c3-4 0-8 2-11z" class="C"></path><path d="M276 358l1 5h1c0 2-1 3-1 5v9h-2l-1-8c3-4 0-8 2-11z" class="P"></path><defs><linearGradient id="AF" x1="140.124" y1="236.435" x2="170.528" y2="265.599" xlink:href="#B"><stop offset="0" stop-color="#c5c4c4"></stop><stop offset="1" stop-color="#e8e7e7"></stop></linearGradient></defs><path fill="url(#AF)" d="M171 243c0-9-4-19-8-27 5 1 7 3 11 6 6 8 9 16 8 26-2 9-7 16-14 21-7 6-16 8-25 6-9-1-17-6-23-12l-4-6c7 3 13 5 21 6h0c8 2 18 0 24-5 5-3 8-8 10-15z"></path><path d="M347 635c2-1 5-2 7-2 7-3 15-4 23-2 9 2 22 9 27 17 2 4 3 8 3 12 0-1 1-3 0-4v-5l61 160 1 1 3 8 2 7h-3l-7-2h-3l-4-1-7-2-5-1h-3l-4-1c-9-1-17-2-26-5l-1 1 3 1v1l-11-1-27-4-16-4c-1 0-2-1-3-2-1 0-3-1-4-2-4-1-8-4-12-6-19-12-31-36-36-58-4-15-6-33-4-48l3-15 3-8 1 2s0 1-1 2l-1 2v2c-1 1-1 2-1 4h0l2-4c0-1 1-2 1-3 1-1 1-1 1-2 2-3 3-6 5-9h0l1-2c1-1 1-1 1-2l2-2c2-3 6-7 9-9 6-4 11-7 19-9-1-2 0-3 1-5z" class="X"></path><path d="M432 809l7 2-2 2c-2 0-4 0-6-1v-1h1 1l-1-1v-1z" class="B"></path><path d="M323 692v-4c1-4 4-12 8-15v1c1 4-2 12-3 16l-1 4c0-1 0-3 1-4v-3-1c0-2 1-2 0-3h0c-1-1-1 0-1-1-2 1-3 3-3 5v5 1l-1-1z" class="F"></path><path d="M447 803c2 1 5 2 7 4l1 2h1c1 0 2 0 3 1l-2 2-7-2-7-2h3l1 1v-1c0-1-1-2-1-3l-1-1h0s1 0 1-1h1z" class="C"></path><path d="M446 805c1 0 2 1 4 2v3l-7-2h3l1 1v-1c0-1-1-2-1-3z" class="B"></path><defs><linearGradient id="AG" x1="466.595" y1="814.278" x2="456.771" y2="818.562" xlink:href="#B"><stop offset="0" stop-color="#383737"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#AG)" d="M469 812l3 8-14-2c1 0 2 1 3 0-1-2-5-2-7-2-1-1-2-2-3-2 6 0 12 1 17 2h1l-1-2c0-1 0-1 1-2z"></path><defs><linearGradient id="AH" x1="450.171" y1="813.075" x2="439.329" y2="814.925" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#9a9a9a"></stop></linearGradient></defs><path fill="url(#AH)" d="M439 811l12 3c1 0 2 1 3 2 2 0 6 0 7 2-1 1-2 0-3 0-7-1-14-2-21-5l2-2z"></path><path d="M369 735l-13-9c-2-2-3-4-3-7v-1c2 1 3 2 4 3 2 1 3 3 5 4h0l3 3c1 2 4 4 4 7z" class="C"></path><defs><linearGradient id="AI" x1="378.242" y1="775.849" x2="384.23" y2="785.479" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#848383"></stop></linearGradient></defs><path fill="url(#AI)" d="M375 774c2-1 4 1 6 3l8 8c-2 1-3 2-5 2-1 0 0 0-1 1l-4-5c0-4-2-6-4-9z"></path><path d="M407 798c8 5 16 8 25 11v1l1 1h-1-1v1c-5-1-10-3-15-5-2-1-5-2-8-2l-2-2v-1c-1-1-1-1-2-1l-1-1h-1l2-2c2 1 1 1 2 1l1-1z" class="D"></path><defs><linearGradient id="AJ" x1="384.608" y1="789.805" x2="404.654" y2="795.823" xlink:href="#B"><stop offset="0" stop-color="#9d9c9c"></stop><stop offset="1" stop-color="#d4d4d4"></stop></linearGradient></defs><path fill="url(#AJ)" d="M383 788c1-1 0-1 1-1 2 0 3-1 5-2 6 5 12 10 18 13l-1 1c-1 0 0 0-2-1l-2 2h1l1 1c1 0 1 0 2 1v1c-2 0-3-1-5-2-1-1-2-1-3-1l-1-1c-2-1-3-2-5-2l-9-9z"></path><path d="M345 748v-1c1 1 2 2 2 3l2 2c1 1 2 2 3 4h2 2l1-1c-1-1-1-2-2-2v-2h0c6 8 12 15 20 23 2 3 4 5 4 9-3-2-6-5-10-7-8-8-18-18-24-28z" class="F"></path><path d="M408 805c3 0 6 1 8 2 5 2 10 4 15 5 2 1 4 1 6 1 7 3 14 4 21 5l14 2 2 7h-3l-7-2h-3l-4-1-7-2-5-1h-3l-4-1v-1c0-1-1-1-1-2v-1c-1 0-1-1-2-2 0 0-4-1-5-2-7-2-15-4-22-7z" class="L"></path><path d="M457 820l5 1-1 4-4-1v-4z" class="O"></path><path d="M445 817l5 2v3l-5-1v-4z" class="B"></path><path d="M450 819l7 1v4l-7-2v-3z" class="G"></path><path d="M462 821l2 1c4 0 5 1 8 4 0 1 0 0-1 1l-7-2h-3l1-4z" class="E"></path><path d="M462 821l2 1c0 1 1 1 1 3h-1-3l1-4z" class="U"></path><path d="M435 814l7 2 3 1v4h-3l-4-1v-1c0-1-1-1-1-2v-1c-1 0-1-1-2-2z" class="J"></path><path d="M442 816l3 1v4h-3c1-2 0-3 0-5z" class="D"></path><path d="M366 668s-1 1-1 2l10-7c-2 4-7 7-11 9l-1-1-5 4c-5 3-8 6-10 11-1 1-1 3-2 4h-1c1-1 1-2 1-3 1-1 0-1 1-1l1-2h0l1-2c-1 0-2-1-3 0h0c-3-1-6-3-8-5-1-1-1-2-1-3 2-2 3-4 5-5 4-1 9-1 13 1 2 0 2 1 3 3h0c1 0 1-1 2-1 3-2 4-4 6-4z" class="M"></path><path d="M392 797c2 0 3 1 5 2l1 1c1 0 2 0 3 1 2 1 3 2 5 2l2 2c7 3 15 5 22 7 1 1 5 2 5 2 1 1 1 2 2 2v1c0 1 1 1 1 2v1c-9-1-17-2-26-5-3-1-6-2-9-4-4-4-8-10-11-14z" class="F"></path><path d="M305 741h0c2 2 2 5 3 7 0 3 1 6 3 8 2 1 4 1 6 2 3 1 5 3 6 6 2 1 3 2 4 4l17 24c-1 1-1 2-2 3s-1 1-2 1c0 1 0 2 1 2 1 1 1 0 2 1h2 1c-1-2-1-3 0-4v-1c0-2 2-4 3-5h1l1-1 1 1c1 0 1 1 1 1l-2 7c3 6 11 10 18 11 1 1 2 1 3 1l1 1c1 0 2 1 2 1v1c-1 0-2 0-3-1-4 0-8-2-12-2-1 0-2-1-3-2-1 0-3-1-4-2-4-1-8-4-12-6-19-12-31-36-36-58z" class="M"></path><path d="M346 800v-2c0-2 1-6 3-7 1-1 2-1 3-1-1 3-2 5-1 9h0-4l-1 1z" class="B"></path><path d="M346 800l1-1h4c4 5 10 8 16 10 2 0 4 1 5 2-4 0-8-2-12-2-1 0-2-1-3-2 0-1-9-3-11-7z" class="J"></path><path d="M314 695c0-3 1-5 2-8 1-2 3-6 6-7h1s-1 2-1 3c0 3 0 5 1 8v1h0l1 1v-1-5c0-2 1-4 3-5 0 1 0 0 1 1h0c1 1 0 1 0 3v1 3c-1 1-1 3-1 4l1-4c0 6 1 8 5 13 0-3 1-8 3-10 1 0 2 0 3 1 2 2 3 6 3 10l-1 1s1 0 1 1c1 1 2 6 2 8 1 7 2 15 5 23 1 5 4 9 6 14h0v2c1 0 1 1 2 2l-1 1h-2-2c-1-2-2-3-3-4l-2-2c0-1-1-2-2-3v1c-2-1-4-5-6-7-3-3-6-7-9-10-6-6-14-8-19-14 0-4-1-11 1-13h1l1 3c0 2 1 3 2 4l-1-3c-2-4-2-9-1-13z" class="I"></path><path d="M314 695c0-3 1-5 2-8 1-2 3-6 6-7h1s-1 2-1 3c0 3 0 5 1 8v1 2-1l-1-2c-1-1-1-2-1-4h0c-1-1-1-2-2-3-1 1-2 3-3 4-1 4-2 7-2 11h0v-4z" class="T"></path><defs><linearGradient id="AK" x1="441.661" y1="712.497" x2="384.29" y2="745.641" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#3b3b3b"></stop></linearGradient></defs><path fill="url(#AK)" d="M347 635c2-1 5-2 7-2 7-3 15-4 23-2 9 2 22 9 27 17 2 4 3 8 3 12 0-1 1-3 0-4v-5l61 160 1 1c-1 1-1 1-1 2l-11-2 2-2c-1-1-2-1-3-1h-1l-1-2c-2-2-5-3-7-4h-1c0 1-1 1-1 1h0l1 1c0 1 1 2 1 3v1l-1-1h-3c-5 0-11-3-15-5l-15-7c-2 0-4-2-7-3-1 0-2 0-3-1-3-1-8-5-9-9h0l-1-4c0-1-1-3-1-4-2-5-3-9-3-14 0-4-2-6-4-9-5-7-10-12-16-17 0-3-3-5-4-7l-3-3c-4-6-8-12-11-19-3-5-5-9-5-16 1-1 1-3 2-4 2-5 5-8 10-11l5-4 1 1c4-2 9-5 11-9l-10 7c0-1 1-2 1-2 1-1 1-1 2-1 1-1 5-5 6-7 0-2 0-4-1-6-1-5-4-9-8-12-6-3-13-3-19-2-1-2 0-3 1-5z"></path><path d="M455 805h3c2 1 3 2 4 2v1h-2l-3-1-2-2z" class="X"></path><path d="M454 807h3l3 1c2 1 6 3 8 3l1 1c-1 1-1 1-1 2l-11-2 2-2c-1-1-2-1-3-1h-1l-1-2z" class="E"></path><defs><linearGradient id="AL" x1="373.51" y1="663.412" x2="367.417" y2="674.549" xlink:href="#B"><stop offset="0" stop-color="#999898"></stop><stop offset="1" stop-color="#bebdbe"></stop></linearGradient></defs><path fill="url(#AL)" d="M375 663h0l2-3c1-1 1-1 3-2-3 10-11 17-18 23 0-2 1-4 3-5h1 0c-2 0-5 0-6 1-1 0-3 3-4 3v-1l2-2v-2l5-4 1 1c4-2 9-5 11-9z"></path><path d="M403 669c-2 3-5 6-9 6-2 1-5 1-7-1s-3-4-4-7c0-3 2-4 4-6 3 2 5 3 8 4 2 0 3 0 4-1l1-1-1 1c0 3-2 4-4 5-4 0-5 0-8-2h0c2 2 4 4 7 4 1 0 3 0 4-1h1 0l1 1c1-1 2-1 3-2z" class="B"></path><path d="M358 675v2l-2 2v1c1 0 3-3 4-3 1-1 4-1 6-1h0-1c-2 1-3 3-3 5-5 5-6 9-6 17 1 3 3 5 5 7-1 1-2 1-3 1v1l-3-2v1c1 1 2 2 3 2v2c-2-2-4-3-7-4-3-5-5-9-5-16 1-1 1-3 2-4 2-5 5-8 10-11z" class="R"></path><defs><linearGradient id="AM" x1="406.504" y1="651.259" x2="375.542" y2="649.571" xlink:href="#B"><stop offset="0" stop-color="#484748"></stop><stop offset="1" stop-color="#7f7e7f"></stop></linearGradient></defs><path fill="url(#AM)" d="M377 631c9 2 22 9 27 17 2 4 3 8 3 12s-1 7-4 9h0c-1 1-2 1-3 2l-1-1h0-1c-1 1-3 1-4 1-3 0-5-2-7-4h0c3 2 4 2 8 2 2-1 4-2 4-5l1-1v-3c0-2-1-2-2-4-1 0-3-1-4-2-5-3-8-8-12-12 1 0 2 0 2 1l4 4c0-2-1-2-2-4 1 0 0 0 1-1h-2-1c1-1 2-1 3-2-2-1-5-3-7-4-2-2-3-2-3-5z"></path><path d="M387 640c2 0 3 2 5 3h0c1 1 2 1 2 2 2 0 4 1 4 2-1 1-1 0-2 1l3 5v1l-1-1c0-1 0-1-1-1v1c0 1 1 2 1 3-1 0-3-1-4-2-5-3-8-8-12-12 1 0 2 0 2 1l4 4c0-2-1-2-2-4 1 0 0 0 1-1h-2-1c1-1 2-1 3-2z" class="K"></path><path d="M387 640c2 0 3 2 5 3h0c1 1 2 1 2 2v2h0-1c-1-2-4-4-5-4 0 1 1 4 2 6s4 3 4 5c-5-3-8-8-12-12 1 0 2 0 2 1l4 4c0-2-1-2-2-4 1 0 0 0 1-1h-2-1c1-1 2-1 3-2z" class="S"></path><defs><linearGradient id="AN" x1="380.239" y1="632.878" x2="356.549" y2="652.041" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#aaa9aa"></stop></linearGradient></defs><path fill="url(#AN)" d="M347 635c2-1 5-2 7-2 7-3 15-4 23-2 0 3 1 3 3 5 2 1 5 3 7 4-1 1-2 1-3 2h1 2c-1 1 0 1-1 1 1 2 2 2 2 4l-4-4c0-1-1-1-2-1-3-4-6-6-11-7 6 4 9 9 10 16 0 2 0 5-1 7-2 1-2 1-3 2l-2 3h0l-10 7c0-1 1-2 1-2 1-1 1-1 2-1 1-1 5-5 6-7 0-2 0-4-1-6-1-5-4-9-8-12-6-3-13-3-19-2-1-2 0-3 1-5z"></path><path d="M356 698l1-3v3c0 1 1 2 2 3 1 0 1 0 1-1 2-2 5-4 7-6h1c3-2 9-4 13-4v1h4c6 2 14 5 17 10h1c4 5 5 11 7 16l16 47c3 9 5 19 9 28l6 6h1c3 2 6 3 9 5 1 0 2 1 4 2l2 2h-3c-2-2-5-3-7-4h-1c0 1-1 1-1 1h0l1 1c0 1 1 2 1 3v1l-1-1h-3c-5 0-11-3-15-5l-15-7c-2 0-4-2-7-3-1 0-2 0-3-1-3-1-8-5-9-9h0l-1-4c0-1-1-3-1-4-2-5-3-9-3-14 0-4-2-6-4-9-5-7-10-12-16-17 0-3-3-5-4-7l-3-3c-4-6-8-12-11-19 3 1 5 2 7 4v-2c-1 0-2-1-3-2v-1l3 2v-1c1 0 2 0 3-1-2-2-4-4-5-7z" class="W"></path><path d="M404 772c4 4 6 8 10 11 5 5 11 9 17 13v1c1 0 1 1 2 1h-1c-1-1-2-1-3-1h-1 0l-1-1c-1 0-2-1-2-1l-8-5v-1l-1 1h0v-1h1v-1c-2-1-3-2-5-3v-1-1c0-2-3-3-4-5-1-1-1-2-3-3h0c0-1 0-1-1-2v-1z" class="D"></path><defs><linearGradient id="AO" x1="432.607" y1="795.252" x2="427.155" y2="802.045" xlink:href="#B"><stop offset="0" stop-color="#c2c0c0"></stop><stop offset="1" stop-color="#e9e9e9"></stop></linearGradient></defs><path fill="url(#AO)" d="M414 788c1 0 1 0 2 1v1h0l1-1v1l8 5s1 1 2 1l1 1h0 1c1 0 2 0 3 1h1c-1 0-1-1-2-1v-1l12 6 4 1h-1c0 1-1 1-1 1h0l1 1c0 1 1 2 1 3v1l-1-1h-3c-5 0-11-3-15-5-1-2-2-4-4-5s-4-1-5-3c-1-1-3-2-4-3s-1-2-2-2v-1l1-1z"></path><path d="M431 796l12 6h-1-1-1v1l-8-4c-2 0-3-1-4-2h1c1 0 2 0 3 1h1c-1 0-1-1-2-1v-1z" class="I"></path><defs><linearGradient id="AP" x1="404.795" y1="726.389" x2="378.192" y2="775.655" xlink:href="#B"><stop offset="0" stop-color="#a3a2a3"></stop><stop offset="1" stop-color="#e9e8e8"></stop></linearGradient></defs><path fill="url(#AP)" d="M361 705c3 2 5 3 8 4 3 2 5 7 8 10 2-2 3-7 3-9 4 15 6 31 13 45 3 6 7 12 11 17v1c1 1 1 1 1 2h0c2 1 2 2 3 3 1 2 4 3 4 5v1 1c2 1 3 2 5 3v1h-1c-1-1-1-1-2-1l-1 1v1c1 0 1 1 2 2s3 2 4 3c1 2 3 2 5 3s3 3 4 5l-15-7c-2 0-4-2-7-3-1 0-2 0-3-1-3-1-8-5-9-9h0l-1-4c0-1-1-3-1-4-2-5-3-9-3-14 0-4-2-6-4-9-5-7-10-12-16-17 0-3-3-5-4-7l-3-3c-4-6-8-12-11-19 3 1 5 2 7 4v-2c-1 0-2-1-3-2v-1l3 2v-1c1 0 2 0 3-1z"></path><path d="M413 789c-2 0-2-2-3-3h1c0 1 1 1 2 1l1 1-1 1z" class="D"></path><path d="M389 761l8 17c-1 2 0 2 0 3-2 0-3-1-4-2 0-1-1-3-1-4-2-5-3-9-3-14z" class="E"></path><path d="M361 705c3 2 5 3 8 4h0-3-1l1 3c0 1-1 1 0 2l-1 1h-1-1 0l-1-1h-1l1 1v1l-3-3 1-1c-1 0-1-1-2-2v-2c-1 0-2-1-3-2v-1l3 2v-1c1 0 2 0 3-1z" class="B"></path><path d="M361 705c3 2 5 3 8 4h0-3-1l1 3c-2 0-3-2-5-3-1-1-2-1-3-2v-1c1 0 2 0 3-1z" class="I"></path><path d="M393 779c1 1 2 2 4 2 0-1-1-1 0-3 4 8 10 13 16 18-2 0-4-2-7-3-1 0-2 0-3-1-3-1-8-5-9-9h0l-1-4z" class="M"></path><path d="M674 634l6 2c9 0 15 5 21 10 1 2 7 7 7 9s5 6 6 9c8 13 10 28 11 43 0 3 0 7-1 10v1l-1 1c0 9-1 19-4 28-5 22-19 42-39 55-9 5-18 8-28 10l-12 2c-5 1-10 2-15 2-3 0-5 1-7 1-4 1-8 2-11 2h-1l1-1-1-1-23 4-10 1-15 3-3 1c-1 0-3 1-5 1l-1-1 2-6 5-13 62-159 1 2c2-2 3-4 5-6l4-3-2 2 1 1c0 1 1 3 1 5h1l1-2h0v-1l3-3 2-1h2l1-1 1 1 1-1c1 1 1 1 3 1 2-3 5-5 8-6 2 0 4-1 6-2 0 1 0 1 1 1s1 0 2 1h0c1 0 1-1 2-1h1 1v1h-2 0l1 1c1-1 2-1 3-2v1h1 0c2 0 6 1 7 0h-3-1c2 0 3 0 5-1l-1-1z" class="X"></path><path d="M567 808l1 1c0 1 1 1 1 2h0l-5 2v-3l3-2z" class="C"></path><path d="M568 809l5-2v1 1h1 1 2 2l-10 2h0c0-1-1-1-1-2z" class="B"></path><path d="M564 813c-2 0-7 2-9 2v-2c2-2 6-2 9-3v3z" class="G"></path><defs><linearGradient id="AQ" x1="563.349" y1="816.015" x2="588.674" y2="812.134" xlink:href="#B"><stop offset="0" stop-color="#6f6e6f"></stop><stop offset="1" stop-color="#b6b4b5"></stop></linearGradient></defs><path fill="url(#AQ)" d="M553 820v-2h5c4-2 8-3 13-4l27-6h-1c-2 2-6 2-8 4h1l1-1h2c1 0 1 0 2-1h3c-4 2-8 3-12 4-11 3-22 5-33 6z"></path><path d="M661 726v-1l12-9c-1 6-2 9-8 12s-10 7-15 12v-2c1-3 5-7 7-9h0l1-1c1-1 2-2 3-2z" class="C"></path><path d="M636 762v2h1c-1 7-1 13-6 19h0c-5 1-9 9-15 9h0c4-2 7-5 9-8 0-4 3-8 5-11 1-3 2-4 4-6 1-2 1-3 2-5h0z" class="M"></path><path d="M630 773c1-3 2-4 4-6-1 6-5 13-9 17 0-4 3-8 5-11z" class="D"></path><path d="M621 797h1c2-1 3-2 5-3l4 2-4 2-4 2v2h0c-3 0-9 3-12 4s-6 2-10 3c-1 1-2 2-3 1h-3c-1 1-1 1-2 1h-2l-1 1h-1c2-2 6-2 8-4h1c1-1 6-3 8-4 5-2 10-5 15-7z" class="H"></path><path d="M659 667c2 0 5 3 7 5h2l4-1c1 0 0 0 2-1h2 2c1 0 2 0 3-1 1 1 2 0 3 1 1 0 4 2 4 2v3c-1 2-2 2-4 2-1 1-2 1-4 2h0c-2 1-2 1-3 2l1 3c1 2 2 3 1 5-3-8-6-12-13-16l-1-1-6-5z" class="W"></path><path d="M673 747v1c0 2-3 6-4 7v1h1c1 1 1 1 2 1s1 0 2-1v-1l3-3 1-1 1-1c0-1 1-1 1-1-8 12-19 24-31 32h0c-1-2 0-4 0-6 3-1 5-4 8-6 6-7 11-14 16-22z" class="F"></path><path d="M642 779c1-2 1-2 3-3 2-6 9-12 13-17 1-2 2-4 4-5l1-1c2-2 5-2 7-3 1-1 1-3 2-3 0-2 1-3 2-4l-1 4c-5 8-10 15-16 22-3 2-5 5-8 6s-5 2-7 4z" class="M"></path><path d="M598 810c1 1 2 0 3-1 4-1 7-2 10-3s9-4 12-4l-42 15-9 2 1 3-15 3-3 1c-1 0-3 1-5 1l-1-1 2-6h2 0c11-1 22-3 33-6 4-1 8-2 12-4z" class="L"></path><path d="M556 822h2v3l-3 1c-1 0-3 1-5 1l1-3c1-1 3-1 5-2z" class="U"></path><path d="M556 822h2v3l-3 1 1-4z" class="K"></path><defs><linearGradient id="AR" x1="559.434" y1="823.174" x2="570.486" y2="820.929" xlink:href="#B"><stop offset="0" stop-color="#727172"></stop><stop offset="1" stop-color="#9a9999"></stop></linearGradient></defs><path fill="url(#AR)" d="M558 822l14-3 1 3-15 3v-3z"></path><defs><linearGradient id="AS" x1="639.888" y1="775.415" x2="635.284" y2="796.067" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#AS)" d="M642 779c2-2 4-3 7-4 0 2-1 4 0 6h0c-4 4-7 9-11 12-1 1-2 3-4 4-1 0-3 1-5 2h-1c-1 1-3 2-5 3v-2l4-2 4-2-4-2c-2 1-3 2-5 3h-1c8-6 14-11 21-18z"></path><path d="M669 679c2 3 4 5 5 9v-1c-2-5-5-9-9-13l1-1c7 4 10 8 13 16 1 6 0 10-3 15-3 1-6 2-8 4-2 1-3 3-5 4l2-6c2-2 4-4 5-7 1-5-1-11-3-15h0c2-2 2-2 2-5z" class="R"></path><path d="M680 636c9 0 15 5 21 10 1 2 7 7 7 9s5 6 6 9c8 13 10 28 11 43 0 3 0 7-1 10v1l-1 1v-4c0-5 0-10-1-15v-5c-1-1-1-3-1-4l-1-5c-1-1-1-2-1-3-1-2-1-4-2-6v-1l-4-8-3-5-2-3-1-2c-5-8-14-12-22-16l-1-2c0-2-2-3-4-4z" class="E"></path><path d="M623 802c2-1 4-2 5-3h1c2-1 4-2 5-2-3 4-6 10-10 13-5 4-12 6-18 7l-23 4-10 1-1-3 9-2 42-15h0z" class="T"></path><path d="M572 819l9-2h0c-1 1-2 2-4 2v1h3 0l3 1-10 1-1-3z" class="B"></path><path d="M674 634l6 2c2 1 4 2 4 4l1 2c-6-3-15-4-22-1-4 1-7 4-9 8-1 3-3 8-1 11 0 2 4 6 6 7l6 5 1 1-1 1c4 4 7 8 9 13v1c-1-4-3-6-5-9-2-2-6-7-8-7-1-1-1 0-2-1-1 0-3-2-4-3s-2-1-4-1c-2-2-5-8-5-11-2-7 1-14 5-20 2 0 4-1 6-2 0 1 0 1 1 1s1 0 2 1h0c1 0 1-1 2-1h1 1v1h-2 0l1 1c1-1 2-1 3-2v1h1 0c2 0 6 1 7 0h-3-1c2 0 3 0 5-1l-1-1z" class="C"></path><defs><linearGradient id="AT" x1="601.918" y1="782.095" x2="610.125" y2="794.575" xlink:href="#B"><stop offset="0" stop-color="#c7c6c6"></stop><stop offset="1" stop-color="#eae9e9"></stop></linearGradient></defs><path fill="url(#AT)" d="M592 799c2-2 4-2 7-4 1-1 2-1 3-2l4-3c1 0 2-1 2-1l2-1 2-2c1 0 1-1 2-1 1-1 2-3 4-3l4-4c1-2 0-1 1-2l2-2h0c1-2 2-3 3-4 1-2 2-3 3-5l3-4 2 1c-1 2-1 3-2 5-2 2-3 3-4 6-2 3-5 7-5 11-2 3-5 6-9 8h0c-12 7-24 14-37 17h-2-2-1-1v-1-1c4-2 9-4 13-7h0c2 1 4-1 6-1z"></path><path d="M586 800c2 1 4-1 6-1-1 2-8 5-10 6l-2-1-7 4v-1c4-2 9-4 13-7h0z" class="I"></path><path d="M616 792l2-3c0-2 1-2 2-3l1-1c2-4 5-5 6-9 1-2 2-2 3-3-2 3-5 7-5 11-2 3-5 6-9 8z" class="J"></path><path d="M640 814c1-1 2-1 3-2 2 0 2-1 4-1 1-1 1-1 2-1s3 0 4-1h0c1 0 2 0 3-1h1 1v-1l1-1c1-1 3-2 5-2 1 1 0 1 1 1 1-2 3-2 4-3h1c1-1 2-1 2-2 1-1 2-3 2-4v-2c1-2-1-3-1-5l1-1v1h2l1 1c1-3 3-4 4-6 1-1 1-2 3-3 0-1 1-1 1-2l1-1c2-3 5-5 7-8 2-2 3-4 5-6 1-2 2-3 3-4 2-2 3-4 6-5l1 1c2 3-2 6-1 9v2l-1 2c0 1 1 2 2 2 0-1 1-1 2-2v-2c1-1 2-2 2-3h0c2-5 3-9 5-13l1-4h1c-5 22-19 42-39 55-9 5-18 8-28 10l-12 2z" class="M"></path><path d="M674 790h2l3 3c0 2 1 5-1 8-2 5-15 8-21 9l-3 1h-1c7-4 17-5 21-12 2-4 1-6 0-9z" class="D"></path><defs><linearGradient id="AU" x1="616.983" y1="729.617" x2="640.758" y2="767.428" xlink:href="#B"><stop offset="0" stop-color="#989697"></stop><stop offset="1" stop-color="#f0efef"></stop></linearGradient></defs><path fill="url(#AU)" d="M668 708c2-2 5-3 8-4-1 2-2 4-3 5-4 7-8 11-12 17-1 0-2 1-3 2l-1 1h0c-2 2-6 6-7 9v2c-5 6-13 14-14 22h0l-2-1-3 4c-1 2-2 3-3 5-1 1-2 2-3 4h0l-2 2c-1 1 0 0-1 2l-4 4c-2 0-3 2-4 3-1 0-1 1-2 1l-2 2-2 1s-1 1-2 1l-4 3c-1 1-2 1-3 2-3 2-5 2-7 4-2 0-4 2-6 1l6-4h0c22-12 39-34 46-58 3-10 4-20 7-29 1 3 2 7 4 10v1c1-2 3-4 4-6 1-1 2-5 3-6 1 0 4-1 5-1s2-1 4-1l-2 6c2-1 3-3 5-4z"></path><path d="M668 708c2-2 5-3 8-4-1 2-2 4-3 5-3 2-7 4-9 6-1 0-2 1-3 1 1-2 5-5 7-7v-1z" class="I"></path><path d="M665 706l-2 6c-1 2-3 4-4 6-2 3-5 5-6 9h-1 0l-1-1 1-1h-1l-2 2v1 1 1c-1 1-1 2-2 3-1-1-1-1-1-2l1-2v-1l3-4h-1v-4h0c1-2 3-4 4-6 1-1 2-5 3-6 1 0 4-1 5-1s2-1 4-1z" class="B"></path><path d="M674 743h1c3-8 4-15 5-23l2-13c1-3 1-7 2-10 1-2 2-3 4-4h2c2 4 2 7 1 11 3-3 7-7 6-11 0-4-5-17-3-20 2 1 3 2 4 4 2 4 4 9 4 14 2-3-1-10 2-11 3 1 6 7 7 10 1 6 1 14 0 19h0c0-2 1-3 1-5l1-1c2 2 1 8 1 10 0 3-2 5-4 7-5 3-10 6-14 10-6 6-10 13-16 19 0 0-1 0-1 1l-1 1-1 1-3 3v1c-1 1-1 1-2 1s-1 0-2-1h-1v-1c1-1 4-5 4-7v-1l1-4z" class="I"></path><defs><linearGradient id="AV" x1="583.192" y1="711.304" x2="641.598" y2="745.171" xlink:href="#B"><stop offset="0" stop-color="#0a0a0a"></stop><stop offset="1" stop-color="#464646"></stop></linearGradient></defs><path fill="url(#AV)" d="M643 642c2-3 5-5 8-6-4 6-7 13-5 20 0 3 3 9 5 11 2 0 3 0 4 1s3 3 4 3c1 1 1 0 2 1 2 0 6 5 8 7 0 3 0 3-2 5h0c2 4 4 10 3 15-1 3-3 5-5 7-2 0-3 1-4 1s-4 1-5 1c-1 1-2 5-3 6-1 2-3 4-4 6v-1c-2-3-3-7-4-10-3 9-4 19-7 29-7 24-24 46-46 58h0l-6 4h0c-4 3-9 5-13 7l-5 2-1-1v-1h-2l-4 2h-2-1l1-1c-1 0-2 0-3-1l62-159 1 2c2-2 3-4 5-6l4-3-2 2 1 1c0 1 1 3 1 5h1l1-2h0v-1l3-3 2-1h2l1-1 1 1 1-1c1 1 1 1 3 1z"></path><path d="M642 704c2-1 2-1 4-1v2c-1 1-2 0-3 0l-1-1z" class="M"></path><defs><linearGradient id="AW" x1="653.166" y1="673.084" x2="669.385" y2="679.122" xlink:href="#B"><stop offset="0" stop-color="#b8b9b7"></stop><stop offset="1" stop-color="#e4e3e4"></stop></linearGradient></defs><path fill="url(#AW)" d="M651 667c2 0 3 0 4 1s3 3 4 3c1 1 1 0 2 1 2 0 6 5 8 7 0 3 0 3-2 5h0c-4-7-11-10-16-17z"></path><path d="M641 665c1 2 1 3 0 5-1 3-4 5-6 5-3 1-6 0-8-2l-4-3c1 0 3 1 5 1 3 0 7-2 9-3 1 0 3-2 4-3z" class="B"></path><path d="M559 808l4-2 6-2c3-1 7-3 10-5l6-3 1-1h0c2-1 4-3 6-4 0-1 1-1 2-2l2-1c0-1 1-1 2-2h1c1-1 2-3 3-3l1-1 1-1h0l1 1-6 5-1 1c-2 2-4 3-6 5l-1 1c0 1-1 2-2 3l2-1h1l-6 4h0c-4 3-9 5-13 7l-5 2-1-1v-1h-2l-4 2h-2-1l1-1z" class="E"></path><path d="M591 794c0 1-1 2-2 3l2-1h1l-6 4h0l-2-1c1-1 1-1 2-1v-1c2-1 4-2 5-3z" class="M"></path><path d="M624 644l4-3-2 2 1 1c0 1 1 3 1 5h1l1-2h0v-1l3-3 2-1h2l1-1 1 1 1-1c1 1 1 1 3 1-2 3-4 6-7 8-3 3-7 5-10 7-1 3-1 5 1 7 0 1 1 1 2 2 3-1 5-3 8-6 2 2 3 3 4 5-1 1-3 3-4 3-2 1-6 3-9 3-2 0-4-1-5-1h-1c-1-1-2-2-3-4-3-5-1-11 0-16 2-2 3-4 5-6z" class="U"></path><path d="M629 666c3-1 5-3 8-6 2 2 3 3 4 5-1 1-3 3-4 3h0c-3 1-6 3-8 2-1 0-2-1-3-2l-1-1v-1c1 1 2 0 4 0z" class="C"></path><path d="M626 668l1-1c2 0 7 0 9-2 1-1 1-1 3-2v1 1l-2 3c-3 1-6 3-8 2-1 0-2-1-3-2z" class="B"></path><path d="M630 647v-1l3-3 2-1h2l1-1 1 1 1-1c1 1 1 1 3 1-2 3-4 6-7 8-3 3-7 5-10 7 0-2 1-4 2-5l1-1c2-3 5-4 8-7h0l-7 3z" class="S"></path><path d="M133 259v1h-1l-1-1c-2-1-4-3-5-4 0-1 0-1-1-2v-1c-1-1-2-1-3-2l1-2h-1c-1-1-1-1-1-2v-2h0c0-1-1-2-1-2l1-1c-1-1-1-3-1-4v-2-2-2h0v-2-2-1c0-1 0-2 1-3v-3h0v-1l1-2 1-1v-2-1c1 0 1-1 1-1v-1c1-2 1 0 1-2s1-2 1-3c1-1 1-3 2-3v-1c1-2 2-3 3-4v-1l4-4c0-2 1-2 2-4-1 0-2 0-3-1h0c-3-2-4-4-6-6h1c2 2 4 4 6 5 4 3 10 2 15 2h225c3 0 7 0 10-1 5-1 11-3 16-3h-1c-7 2-14 5-22 7-5 1-10 0-15 0l1 1c2 1 7 0 9 0v1h-1l-2 2h-2l-1 1c-1 2-3 3-5 4h0c-6 5-12 10-18 13-4 2-9 4-12 7-5 5-9 12-13 18-3 5-7 10-7 16 1 3 2 6 5 8l1-3c0 1 0 2 1 2 1 2 1 5 1 7s1 4 2 6l1 2c0 1 0 2 1 2l1 1c0-2-1-3-1-5h0c1 2 2 5 3 8v1c2 4 4 7 8 10h0-1c-4-4-7-7-10-12v2 3 1c0 1-1 1-1 2-1 1-1 9 0 10v1 2c0 1 2 2 1 3v1l1 1c0 2 0 0 1 2v1l3 6 2 3-1 1c0 1-1 1-1 2l-1 1h-1-1l-5-6c-1-1 0 1-1-1 0-1 0-1-1-1v-1c-1-1-1-2-2-3s-2-3-3-4h0l-3-3c1 2 2 3 3 4l11 16h0l3 4v4l-2-2c-4-2-7-6-10-9-1-2-2-3-3-5l-7-9c-1-2-3-3-4-5 0-1-1-2-1-3 1 6 4 13 7 19l23 55c2 5 4 11 6 16l31 77 1 1 143 375 144-370 25-64 38-99c1-1 2-6 3-8v-1l1 1v-2h-1c-1 0-3 1-4 2 1-2 3-5 5-7 3-5 7-10 10-15 2-4 3-9 4-13 0-2 1-5 2-7 3-3 8-3 10-7h-1l-2-2v-1c0-2-1-2-3-3-8-5-14 1-21 3-1 0-7 2-8 1l-1-2c-1-1-1-2-2-3-1 0-3-4-3-5l-6-9c0-1-1-2-2-3a57.31 57.31 0 0 0-11-11l-8-4c-4-2-8-5-11-7l-2-1-2-2v-1c-1 0-2 0-2-1l-9-6h0l-1-1-1-1c2-1 2-1 4-1v-1h152 49c6 0 13-1 19 0 5 1 12 6 15 10 3 3 6 6 8 9 5 11 7 24 3 35-2 6-6 11-11 14-1 0-3 1-4 1h-1l-3 1h5c1-1 1 0 2 0h-1l-1 1v-1c-1 0-2 0-2 1h-2l-1 1c-3 0-7 0-9-1-7-1-14-6-17-12-3-5-4-10-2-16v7c0 1 0 2 1 4v-4c0-1 0-2 1-3 1-3 2-5 4-8 5-4 9-4 15-3v-1c-3-6-7-10-13-12-9-5-24-2-33 1-31 10-57 53-71 81-18 36-32 74-47 112l-16 39c-3 7-6 14-8 21v1c-1 1-1 2-1 3l-17 41c-4 11-10 22-13 34l-3 7h0c0 2-2 4-2 5l-4 10c0 1-1 3-1 4l-4 9-6 17-4 10-6 15-2 4c2 1 3 0 5 0-1 1-2 2-2 3-1 1-2 1-3 2l1 1c-1 2-3 3-4 5 0 2 0 2 1 4-2 2-3 4-5 6l-1-2-62 159-5 13-2 6-18 47c-6 15-12 30-20 45l-37-91-2-7-3-8-1-1-61-160-3-8c-1-5-4-11-6-16l-6-15-4-12-7-16-3-10-10-25-30-77-1-2c-2-4-4-10-6-15l-12-31-27-65c-11-28-22-56-37-81s-36-56-65-63h-1-4l-1-1h-1-4c-3-1-8-1-11 0h0c-2 0-3 1-4 1l4 2c1 0 2 1 3 2l3 3h0c-4-3-6-5-11-6 4 8 8 18 8 27-2 7-5 12-10 15-6 5-16 7-24 5h0 3v-1c-1 0-2 0-4 1v-1h1 3c0-1-1-1-2-1l-5-2z" class="R"></path><path d="M777 218l1 1-1 3h0c-1 1-2 2-3 4v-1-1-2l3-4z" class="T"></path><path d="M227 212l-7-6c4 1 7 2 10 4-1 1-2 1-3 2z" class="H"></path><path d="M272 247h0c-1 1-1 1-1 2v1l-1-1c-1-5 1-11 3-15 0 4 0 9-1 13z" class="D"></path><path d="M728 205c1 0 2 0 4 1h3 15 0c-6 1-12 0-19 0-4 0-10 1-15 0 4-2 8-1 12-1z" class="H"></path><path d="M263 226l-1-1c1 0 3-1 4-1v-1c3-2 7-6 7-9 0-1 0-1 1-1 0 1 0 2-1 3v1l-1 1-2 2v1l2-2 1-1 2-2c0-1 0-1 1-2v-1c1 0 2-1 2-1 1-1 6 0 8 0 1 1 8 0 10 0v1h-7l-11-1c-3 5-8 13-14 14h-1zm41-10h1v1c-2 2-4 4-7 5l-8-2c-1-1-2 0-3-1v-1c2-1 3-1 4-1 4 0 9 1 13-1z" class="F"></path><defs><linearGradient id="AX" x1="278.588" y1="237.467" x2="270.196" y2="244.786" xlink:href="#B"><stop offset="0" stop-color="#9c9a9a"></stop><stop offset="1" stop-color="#bcbbbb"></stop></linearGradient></defs><path fill="url(#AX)" d="M277 230c0 1 0 3 1 4-3 5-3 8-3 12l1 3-1 1v-1h-2l-1-2c1-4 1-9 1-13l4-4z"></path><defs><linearGradient id="AY" x1="264.825" y1="244.147" x2="257.904" y2="234.813" xlink:href="#B"><stop offset="0" stop-color="#989595"></stop><stop offset="1" stop-color="#b6b6b6"></stop></linearGradient></defs><path fill="url(#AY)" d="M258 233h2 1l2-2h1l1-1h0c-1 1-3 2-4 3l5 19h-1c-4-5-6-13-7-19z"></path><path d="M257 231l1 2c1 6 3 14 7 19h1c2 5 3 10 5 15-1 0-1 0-2-1l-1-2c-6-10-10-21-11-33z" class="D"></path><path d="M220 215l14 12c3 2 4 4 6 6-1 1-1 0-2 0l-1-1c-2-1-3-2-5-2-3-2-5-5-7-7l-7-6v-1c1 0 1 0 2-1h0z" class="E"></path><path d="M225 223l2-1 6 6 1-1c3 2 4 4 6 6-1 1-1 0-2 0l-1-1c-2-1-3-2-5-2-3-2-5-5-7-7zm52 7c3-2 6-4 10-3 4 0 8 3 10 7 0 0 1 1 1 2h0c-4-2-9-6-14-5-2 0-4 1-6 3-1-1-1-3-1-4z" class="N"></path><path d="M294 205h-46c-9 0-17 1-26 0-2 0-4 0-5-1h97c-6 1-13 1-20 1h0z" class="D"></path><defs><linearGradient id="AZ" x1="244.425" y1="228.801" x2="231.221" y2="211.09" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#d5d4d4"></stop></linearGradient></defs><path fill="url(#AZ)" d="M230 210c8 5 17 13 19 23v1c-6-7-12-15-20-20l-2-2c1-1 2-1 3-2z"></path><path d="M765 234l-1-1c-5-2-15-9-16-14v-1c-1-1-2-2-2-4h1s0 1 1 2c2 4 9 9 14 11h4 1l2-2c0-2 1-3 3-4-1 2-2 4-4 6v1l-2 4c-1 1-1 1-1 2z" class="J"></path><defs><linearGradient id="Aa" x1="782.928" y1="228.526" x2="794.853" y2="208.345" xlink:href="#B"><stop offset="0" stop-color="#abaaaa"></stop><stop offset="1" stop-color="#dcdbdb"></stop></linearGradient></defs><path fill="url(#Aa)" d="M801 209h1l-24 23h-1c0-2 0-4 1-5 3-6 12-15 19-17l4-1z"></path><path d="M750 251l1-1c1-4 1-9-1-13-1-3-5-5-8-6-4-1-7 1-11 3-1 1-2 2-4 3 1-4 3-6 6-8 3-1 7-3 10-2s7 5 9 8c1 2 1 3 1 4v4c0 3-1 6-2 8h-1z" class="G"></path><path d="M263 226h1c6-1 11-9 14-14l11 1h-1c-3 1-8-1-10 1h-1v1c-1 1-1 1-1 2l-2 2c-2 3-4 5-7 7l-1 2h-1c-1 0-1 0-1 1h-1l-1-1h0l-2 1-1-1h-1c-1 0-1-1-2-2 0 1 0 3 1 4v1c1 12 5 23 11 33-1 0-2 0-2-1l-2-3-1-1c-1-2-1-3-2-5v-1l-5-15c-1-2-1-1-1-2v-2c0-3 1-5 0-7h-1c0-3-1-4-2-6-2-2-4-4-6-7h0c1 0 4 3 5 4 2 2 3 4 5 5h1c0 1 2 2 3 3s1 1 2 1l1-1z" class="J"></path><path d="M232 230c2 0 3 1 5 2l1 1c1 0 1 1 2 0 3 3 5 6 7 9 3 2 5 5 7 8 4 6 9 13 12 19-2 0-5-5-6-7h-1c-1-1-2-3-4-5 0-1-1-2-2-3-1 0-3-3-4-4l-13-15-4-5z" class="V"></path><path d="M232 230c2 0 3 1 5 2l1 1c1 0 1 1 2 0 3 3 5 6 7 9-2 0-2-1-4-3-2-1-4-4-7-4l-4-5z" class="C"></path><path d="M772 221c3-4 6-7 10-10l-5 7-3 4h-1v5h0l-4 9c-1 1-1 3-2 5s-2 5-4 7c0 2-1 3-1 4s-1 2-2 3c0 1 0 1-1 1 0 2-1 2-2 3l1-2c2-8 3-15 7-23 0-1 0-1 1-2l2-4v-1c2-2 3-4 4-6z" class="B"></path><path d="M769 236v-2c0-5 2-9 4-12v5h0l-4 9z" class="H"></path><defs><linearGradient id="Ab" x1="320.651" y1="283.969" x2="308.099" y2="255.94" xlink:href="#B"><stop offset="0" stop-color="#292828"></stop><stop offset="1" stop-color="#4e4e4f"></stop></linearGradient></defs><path fill="url(#Ab)" d="M303 258h1l2 3c2 3 2 9 6 10 2 0 2 0 3-1 1-2 2-5 2-7l1-3c0 1 0 2 1 2 1 2 1 5 1 7s1 4 2 6l1 2c0 1 0 2 1 2l1 1c0-2-1-3-1-5h0c1 2 2 5 3 8v1c-4-5-7-8-13-11l2 17c-2-3-4-7-6-11-3-7-5-13-7-21z"></path><path d="M749 253l1-2h1c-2 4-4 6-7 7-1 1-3 2-4 3-2 3-2 7-4 10l-4 12c4-4 8-8 10-12 1-2 2-6 4-8 0-1 1-1 2-1 2 0 3 0 5 1v1c-1 2-2 4-4 5-1 2-3 3-4 6-1 0-2 1-3 2-2 2-6 5-7 9-1 2-3 4-4 6-3 4-5 9-7 13 0-1 0-1 1-2h0c0-1 0-2 1-3 0-1 1-1 1-3 0-1 1-3 2-4h1l-1-1c1-2 2-4 4-6 0-2 1-3 1-4-1 1-2 3-3 4-1 3-3 7-5 8-1 1-2 1-3 1 3-5 7-10 10-15 2-4 3-9 4-13 0-2 1-5 2-7 3-3 8-3 10-7z" class="B"></path><defs><linearGradient id="Ac" x1="364.442" y1="430.612" x2="332.388" y2="412.793" xlink:href="#B"><stop offset="0" stop-color="#6f6d6b"></stop><stop offset="1" stop-color="#959699"></stop></linearGradient></defs><path fill="url(#Ac)" d="M337 390l31 77h0-1c0-1-1-2-1-3s-1-2-1-3l-4-9c0-3-2-6-3-8-1 0-1 1-1 2 2 5 4 11 7 16 1 2 2 6 3 9 1 1 3 4 2 6l-33-82 1 1 2 4h1v-3c-2-1-1-1-1-2l-1-2-2-2 1-1z"></path><defs><linearGradient id="Ad" x1="655.586" y1="448.476" x2="679.907" y2="445.463" xlink:href="#B"><stop offset="0" stop-color="#6b686b"></stop><stop offset="1" stop-color="#8d8f8c"></stop></linearGradient></defs><path fill="url(#Ad)" d="M681 409h1c-2 4-5 10-4 15 0-1 1-2 1-2 1-5 4-9 5-14l1-2h0l1-2v-1c0-1 1-1 1-2v-1l1-1s0-1 1-2h0c-2 6-4 12-7 17l-13 34-8 21c-2 5-3 10-6 14 0-1 0-2 1-3h0l1-2v-2l1-1v-1-3c1-1 1-1 0-1 0 1-1 1-1 2l-1 1 25-64z"></path><defs><linearGradient id="Ae" x1="861.437" y1="196.572" x2="844.236" y2="233.214" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#212221"></stop></linearGradient></defs><path fill="url(#Ae)" d="M807 214c4-3 8-6 12-8 13-7 30-10 44-7 10 1 19 6 25 14 5 7 6 14 6 22l-1-1c0-2-1-4-2-6l-1 4-2-1c1-6 0-11-3-16-4-6-11-11-18-12-14-4-32-1-45 5-4 2-7 4-11 6h-1v1c-1 0-1-1-2 0v1h-1v-2z"></path><path d="M285 279c-3-3-6-8-8-12 0-1-1-2-2-3l-1-1c1-1 1-1 3-1 1 0 2 1 2 2 2 3 3 6 5 9 3 5 8 8 11 12 6 6 11 14 16 20 1 2 2 3 3 4l11 16h0l3 4v4l-2-2c-4-2-7-6-10-9-1-2-2-3-3-5l-7-9-2-6-3-4c-4 0-6-5-8-7h-2l-1 1-5-13z" class="C"></path><path d="M285 279c3 3 7 6 9 10 2 3 6 6 7 9-4 0-6-5-8-7h-2l-1 1-5-13z" class="I"></path><path d="M285 279c3 3 7 6 9 10h0l-7-5c2 3 3 5 4 7l-1 1-5-13z" class="B"></path><path d="M304 302c5 4 7 10 11 15 1 1 3 3 4 5 2 3 5 6 9 7v4l-2-2c-4-2-7-6-10-9-1-2-2-3-3-5l-7-9-2-6z" class="J"></path><defs><linearGradient id="Af" x1="699.746" y1="350.805" x2="710.715" y2="354.359" xlink:href="#B"><stop offset="0" stop-color="#9d9c9b"></stop><stop offset="1" stop-color="#c4c3c3"></stop></linearGradient></defs><path fill="url(#Af)" d="M723 295c1 0 2 0 3-1 2-1 4-5 5-8 1-1 2-3 3-4 0 1-1 2-1 4-2 2-3 4-4 6l1 1h-1c-1 1-2 3-2 4 0 2-1 2-1 3-1 1-1 2-1 3h0c-1 1-1 1-1 2-1 4-2 8-4 12l-9 23-14 37c-3 7-5 14-8 20h0c-1 1-1 2-1 2l-1 1v1c0 1-1 1-1 2v1l-1 2h0l-1 2c-1 5-4 9-5 14 0 0-1 1-1 2-1-5 2-11 4-15h-1l38-99c1-1 2-6 3-8v-1l1 1v-2h-1c-1 0-3 1-4 2 1-2 3-5 5-7z"></path><path d="M358 200c-20-1-156 2-162-2-1-1-1-1-1-2 3 3 13 1 18 1h154c-1 2-3 3-5 4v-1h-4z" class="X"></path><path d="M655 196c7 2 15 1 22 1h28 83 42c-4 4-35 3-42 3h-77l-26-1c-5 0-10 0-14 1-2 0-8-1-9 0l2 2-9-6z" class="L"></path><defs><linearGradient id="Ag" x1="313.27" y1="347.09" x2="320.81" y2="343.428" xlink:href="#B"><stop offset="0" stop-color="#adacac"></stop><stop offset="1" stop-color="#d3d2d2"></stop></linearGradient></defs><path fill="url(#Ag)" d="M291 291h2c2 2 4 7 8 7l3 4 2 6c-1-2-3-3-4-5 0-1-1-2-1-3 1 6 4 13 7 19l23 55c2 5 4 11 6 16l-1 1 2 2 1 2c0 1-1 1 1 2v3h-1l-2-4-1-1-3-6-5-13-20-47c-6-13-13-25-18-37l1-1z"></path><path d="M664 202l-2-2c1-1 7 0 9 0h-5c0 1 1 1 2 1 3 1 5 2 9 2l26 1h53 37 15c1 0 1-1 2 0-4 1-7 1-11 1h-13-24-25-1c-3 0-6-1-8 0-4 0-8-1-12 1l-42-2c4 2 9 4 12 7 1 1 2 2 4 2 1 1 2 3 3 4 3 2 7 5 8 9l1 1c2 1 2 3 3 4l2 2c0 1 1 2 2 4s2 4 4 6v1c1 0 1 1 2 1v2h2c1 0 3 0 5-1h2 1c2-1 5-3 8-4h2c1 0 3-1 5 0 1 0 1 0 2 1 2 1 3 1 5 2v1c1 0 2 1 2 2l1 2c0 1-1 2-2 3l-2-2v-1c0-2-1-2-3-3-8-5-14 1-21 3-1 0-7 2-8 1l-1-2c-1-1-1-2-2-3-1 0-3-4-3-5l-6-9c0-1-1-2-2-3a57.31 57.31 0 0 0-11-11l-8-4c-4-2-8-5-11-7l-2-1-2-2v-1c-1 0-2 0-2-1z" class="I"></path><defs><linearGradient id="Ah" x1="287.784" y1="343.542" x2="303.178" y2="334.693" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#b0aeaf"></stop></linearGradient></defs><path fill="url(#Ah)" d="M253 254c1 1 2 2 2 3 2 2 3 4 4 5h1c1 2 4 7 6 7 5 6 8 13 12 20 22 40 37 84 54 126l17 43c3 8 7 16 9 25-2-2-2-5-3-7l-1-2c0-1-1-2-1-3l-3-8-4-10c-1-1-1-2-1-3l-2-5h0l-1-1c-1-2-1-2-1-4h0l-2-5c-2-5-5-11-7-16l-1-4-1-1h-3l-29-73c-5-13-10-25-16-37-9-18-18-34-29-50z"></path><defs><linearGradient id="Ai" x1="748.027" y1="319.743" x2="735.91" y2="313.805" xlink:href="#B"><stop offset="0" stop-color="#727171"></stop><stop offset="1" stop-color="#a7a6a6"></stop></linearGradient></defs><path fill="url(#Ai)" d="M793 226c1 1 2 0 4 1l-3 2c-6 7-12 13-18 21-28 37-44 81-61 124l-12 29c-2 4-3 9-5 13l-1-1h0l1-1c0-2 1-3 1-4v-2h-1l-4 10c-1 3-1 5-3 6l24-59c16-41 33-84 60-119 4-6 9-12 14-17l4-3z"></path><path d="M793 226c1 1 2 0 4 1l-3 2h-5l4-3z" class="O"></path><path d="M209 192h119 28 7l1 1c2 1 7 0 9 0v1h-1l-2 2h-2l-1 1H213c-3-1-9-1-12-1h-4v1l-1-1c-1-1-1-2-1-3h7c1 1 9 0 12 0h1c-1 0-3 0-5-1h-1z" class="D"></path><path d="M133 259v1h-1l-1-1c-2-1-4-3-5-4 0-1 0-1-1-2v-1c-1-1-2-1-3-2l1-2h-1c-1-1-1-1-1-2v-2h0c0-1-1-2-1-2l1-1c-1-1-1-3-1-4v-2-2-2h0v-2-2-1c0-1 0-2 1-3v-3h0v-1l1-2 1-1v-2-1c1 0 1-1 1-1v-1c1-2 1 0 1-2s1-2 1-3c1-1 1-3 2-3v-1c1-2 2-3 3-4v-1l4-4c0-2 1-2 2-4-1 0-2 0-3-1h0c-3-2-4-4-6-6h1c2 2 4 4 6 5 4 3 10 2 15 2h225c3 0 7 0 10-1 5-1 11-3 16-3h-1c-7 2-14 5-22 7-5 1-10 0-15 0h-7-28-119-39-15c-2 0-5 0-8 1-5 1-10 5-14 9-9 8-12 18-13 30 0 8 2 17 8 23 1 2 3 3 5 4z" class="Q"></path><path d="M362 200v1h0c-6 5-12 10-18 13-4 2-9 4-12 7-5 5-9 12-13 18-3 5-7 10-7 16 1 3 2 6 5 8 0 2-1 5-2 7-1 1-1 1-3 1-4-1-4-7-6-10l-2-3h-1c-1-1-1-4-2-5s-2-2-3-2l-8-5c-2 2-3 4-5 5-3 0-5 0-7-2h-2l1 3c0 2 3 4 5 5v1c-4-2-8-5-9-9h2v1l1-1-1-3 2-1c5-3 10-6 16-5 2 1 3 1 4 2h0l2 2h2c1 1 2 1 4 2 3-4 7-10 10-15v-1c2-2 3-5 5-6l1-1c0-1 1-2 2-2 2-2 3-3 5-4 0-1 1-1 1-2 1-1 3-2 5-2 3-2 7-3 9-5 1-1 1-2 2-2 2 0 4-1 5-2h-1-2-1v1h-1v1h-1c-2-1-4-1-6-1-4 0-9 1-13 0h-1-1c-3-1-7 0-9 0h-19-1 0c7 0 14 0 20-1 13 0 25 1 38-2 2 0 4-1 6-2h4z" class="I"></path><path d="M362 200v1h0c-6 5-12 10-18 13-4 2-9 4-12 7-5 5-9 12-13 18-3 5-7 10-7 16 1 3 2 6 5 8 0 2-1 5-2 7-1 1-1 1-3 1-4-1-4-7-6-10l-2-3h-1c-1-1-1-4-2-5s-2-2-3-2l-8-5c-2 2-3 4-5 5-3 0-5 0-7-2h0 4c2-1 6-3 8-3 1 0 2 1 3 1 5 1 9 5 14 5 1 0 3-1 3-2l3-3 1-1c0-2-1 0 0-2 1-1 1-1 1-2l1-1c5-8 10-18 18-23 7-4 15-8 22-13 2-1 4-3 6-5z" class="U"></path><defs><linearGradient id="Aj" x1="427.737" y1="644.262" x2="408.948" y2="650.675" xlink:href="#B"><stop offset="0" stop-color="#2c2a29"></stop><stop offset="1" stop-color="#7f8082"></stop></linearGradient></defs><path fill="url(#Aj)" d="M327 414h3l1 1 1 4c2 5 5 11 7 16l2 5h0c0 2 0 2 1 4l1 1h0l2 5c0 1 0 2 1 3l4 10 3 8c0 1 1 2 1 3l1 2c1 2 1 5 3 7l151 397-1 3-181-469z"></path><defs><linearGradient id="Ak" x1="586.281" y1="635.765" x2="628.032" y2="648.179" xlink:href="#B"><stop offset="0" stop-color="#2e2f30"></stop><stop offset="1" stop-color="#747372"></stop></linearGradient></defs><path fill="url(#Ak)" d="M691 424c2-1 2-3 3-6l4-10h1v2c0 1-1 2-1 4l-1 1h0l1 1-181 462c-1-2-1-2-3-2l177-452z"></path><defs><linearGradient id="Al" x1="372.29" y1="785.855" x2="538.307" y2="415.606" xlink:href="#B"><stop offset="0" stop-color="#111110"></stop><stop offset="1" stop-color="#5f5f60"></stop></linearGradient></defs><path fill="url(#Al)" d="M369 477c1-2-1-5-2-6l-3-9c-3-5-5-11-7-16 0-1 0-2 1-2 1 2 3 5 3 8l4 9c0 1 1 2 1 3s1 2 1 3h1 0l1 1 143 375 144-370 1-1c0-1 1-1 1-2 1 0 1 0 0 1v3 1l-1 1v2l-1 2h0c-1 1-1 2-1 3 1 1-34 90-39 101l-67 174-24 61-12 33-144-375z"></path><path d="M132 237v-1c-1-8 2-19 7-25 6-8 16-12 26-13 20-2 39 4 55 17h0c-1 1-1 1-2 1v1l7 6c2 2 4 5 7 7l4 5 13 15c1 1 3 4 4 4 11 16 20 32 29 50 6 12 11 24 16 37l29 73 181 469 1-3c1 1 1 3 2 4 1-2 2-6 3-8 2 0 2 0 3 2l181-462c2-4 3-9 5-13l12-29c17-43 33-87 61-124 6-8 12-14 18-21l3-2c-2-1-3 0-4-1l4-4 3-2 7-6v2h1v-1c1-1 1 0 2 0v-1h1c4-2 7-4 11-6 13-6 31-9 45-5 7 1 14 6 18 12 3 5 4 10 3 16l2 1 1-4c1 2 2 4 2 6l1 1c-1 7-2 13-6 18-2 3-4 5-7 8h6l-3 1h5c1-1 1 0 2 0h-1l-1 1v-1c-1 0-2 0-2 1h-2l-1 1c-3 0-7 0-9-1-7-1-14-6-17-12-3-5-4-10-2-16v7c0 1 0 2 1 4v-4c0-1 0-2 1-3 1-3 2-5 4-8 5-4 9-4 15-3v-1c-3-6-7-10-13-12-9-5-24-2-33 1-31 10-57 53-71 81-18 36-32 74-47 112l-16 39c-3 7-6 14-8 21v1c-1 1-1 2-1 3l-17 41c-4 11-10 22-13 34l-3 7h0c0 2-2 4-2 5l-4 10c0 1-1 3-1 4l-4 9-6 17-4 10-6 15-2 4c2 1 3 0 5 0-1 1-2 2-2 3-1 1-2 1-3 2l1 1c-1 2-3 3-4 5 0 2 0 2 1 4-2 2-3 4-5 6l-1-2-62 159-5 13-2 6-18 47c-6 15-12 30-20 45l-37-91-2-7-3-8-1-1-61-160-3-8c-1-5-4-11-6-16l-6-15-4-12-7-16-3-10-10-25-30-77-1-2c-2-4-4-10-6-15l-12-31-27-65c-11-28-22-56-37-81s-36-56-65-63h-1-4l-1-1h-1-4c-3-1-8-1-11 0h0c-2 0-3 1-4 1l4 2c1 0 2 1 3 2l3 3h0c-4-3-6-5-11-6 4 8 8 18 8 27-2 7-5 12-10 15-6 5-16 7-24 5h0 3v-1c-1 0-2 0-4 1v-1h1 3c0-1-1-1-2-1h8l-6-5c-5-5-8-12-8-19z" class="F"></path><path d="M622 639l1 1c0 2 0 2 1 4-2 2-3 4-5 6l-1-2c1-3 3-6 4-9z" class="Y"></path><defs><linearGradient id="Am" x1="804.924" y1="215.122" x2="803.076" y2="220.378" xlink:href="#B"><stop offset="0" stop-color="#2f2d2e"></stop><stop offset="1" stop-color="#444644"></stop></linearGradient></defs><path fill="url(#Am)" d="M807 214v2h1v-1c1-1 1 0 2 0v-1h1c-2 3-5 5-8 7-1 0-2-1-3-1l7-6z"></path><path d="M797 222l3-2c1 0 2 1 3 1l-2 2-4 4c-2-1-3 0-4-1l4-4z" class="K"></path><path d="M797 222l3-2c1 0 2 1 3 1l-2 2c-1 0-3 0-4-1zM622 639l4-10c2 1 3 0 5 0-1 1-2 2-2 3-1 1-2 1-3 2l1 1c-1 2-3 3-4 5l-1-1z" class="E"></path><path d="M514 876c2 0 2 0 3 2l-6 14-3-9 1-3c1 1 1 3 2 4 1-2 2-6 3-8z" class="X"></path><path d="M137 232c3 6 6 11 12 13 2 1 4 1 7 1 1-1 3-2 3-4 1-2 0-5-1-7-2-3-4-5-7-6h0c2 0 5 1 6 3 3 3 4 6 4 10 0 2-1 4-3 6-2 1-4 1-6 1-6-2-10-5-13-9-2-2-2-4-3-6l1-2z" class="W"></path><path d="M132 237h0c2 7 3 12 9 16 4 3 9 4 14 3s8-4 11-8c1-2 2-5 2-7 0 1 0 3 1 4h1l-1-1 2-1c-2 7-5 12-10 15-6 5-16 7-24 5h0 3v-1c-1 0-2 0-4 1v-1h1 3c0-1-1-1-2-1h8l-6-5c-5-5-8-12-8-19z" class="E"></path><path d="M877 227h0v-3-1c-1-1-2-2-3-4h-1c-1-2-2-3-3-4l1-1c1 2 3 3 4 5l1 1c1 1 1 3 2 5h0l1 3c1 2 1 5 1 7 1 2 1 4 2 5l1 1c-3 3-7 5-11 5-2 0-3 0-5-2-1-1-1-4 0-5 0-4 3-7 6-10v-1h4v-1z" class="D"></path><defs><linearGradient id="An" x1="155.199" y1="218.655" x2="168.539" y2="245.232" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#An)" d="M168 241c0-2-1-3-1-4-2-4-5-7-9-9-3-1-7-2-10 0-1 1-1 2-1 3v1c-1-3 0-6 1-8 2-6 7-10 12-13 12-5 24-3 35 1-1 0-10-3-12-3-6-1-12-1-17 1h-1c-2 0-3 1-5 2-7 4-10 8-12 16 2-4 5-8 9-11 10-6 23-5 33-2h-1-4l-1-1h-1-4c-3-1-8-1-11 0h0c-2 0-3 1-4 1l4 2c1 0 2 1 3 2l3 3h0c-4-3-6-5-11-6 4 8 8 18 8 27l-2 1 1 1h-1c-1-1-1-3-1-4z"></path><defs><linearGradient id="Ao" x1="170.763" y1="197.457" x2="175.905" y2="223.282" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#383837"></stop></linearGradient></defs><path fill="url(#Ao)" d="M132 237v-1c-1-8 2-19 7-25 6-8 16-12 26-13 20-2 39 4 55 17h0c-1 1-1 1-2 1v1c-17-12-36-19-56-15-8 1-16 5-20 12-4 5-5 12-5 18l-1 2-1-6c-2 3-3 5-3 9h0z"></path><defs><linearGradient id="Ap" x1="869.002" y1="227.656" x2="883.503" y2="252.413" xlink:href="#B"><stop offset="0" stop-color="#d6d5d5"></stop><stop offset="1" stop-color="#fdfdfd"></stop></linearGradient></defs><path fill="url(#Ap)" d="M858 239c1-3 2-5 4-8 5-4 9-4 15-3h-4v1c-3 3-6 6-6 10-1 1-1 4 0 5 2 2 3 2 5 2 4 0 8-2 11-5s4-6 5-10l2 1 1-4c1 2 2 4 2 6l1 1c-1 7-2 13-6 18-2 3-4 5-7 8h6l-3 1h5c1-1 1 0 2 0h-1l-1 1v-1c-1 0-2 0-2 1h-2l-1 1c-3 0-7 0-9-1-7-1-14-6-17-12-3-5-4-10-2-16v7c0 1 0 2 1 4v-4c0-1 0-2 1-3z"></path><path d="M890 232c-1 3-2 5-3 7-3 5-7 9-12 10-3 0-6 0-8-2-2-1-2-3-3-5 0-3 1-6 3-9 1-2 3-3 6-4-3 3-6 6-6 10-1 1-1 4 0 5 2 2 3 2 5 2 4 0 8-2 11-5s4-6 5-10l2 1zm3 2l1 1c-1 7-2 13-6 18-2 3-4 5-7 8h6l-3 1h5c1-1 1 0 2 0h-1l-1 1v-1c-1 0-2 0-2 1h-2l-1 1c-3 0-7 0-9-1-7-1-14-6-17-12-3-5-4-10-2-16v7c0 1 0 2 1 4v-4c0-1 0-2 1-3 0 4 0 8 3 11 3 4 8 6 12 7 5 0 9-2 13-5 6-5 7-11 7-18z" class="M"></path><path d="M609 178h9 4c3 0 4 1 5 2l-8 1h0c-1 1-2 1-3 0h-5v1h0l8 1c5 2 10 3 15 4 2 1 5 2 7 3s4 2 6 2c3 1 7 0 10 0v1c-2 0-2 0-4 1l1 1 1 1h0l9 6c0 1 1 1 2 1v1l2 2 2 1c3 2 7 5 11 7l8 4a57.31 57.31 0 0 1 11 11c1 1 2 2 2 3l6 9c0 1 2 5 3 5 1 1 1 2 2 3l1 2c1 1 7-1 8-1 7-2 13-8 21-3 2 1 3 1 3 3v1l2 2h1c-2 4-7 4-10 7-1 2-2 5-2 7-1 4-2 9-4 13-3 5-7 10-10 15-2 2-4 5-5 7 1-1 3-2 4-2h1v2l-1-1v1c-1 2-2 7-3 8l-38 99-25 64-144 370-143-375-1-1-31-77c-2-5-4-11-6-16l-23-55c-3-6-6-13-7-19 0 1 1 2 1 3 1 2 3 3 4 5l7 9c1 2 2 3 3 5 3 3 6 7 10 9l2 2v-4l-3-4h0l-11-16c-1-1-2-2-3-4l3 3h0c1 1 2 3 3 4s1 2 2 3v1c1 0 1 0 1 1 1 2 0 0 1 1l5 6h1 1l1-1c0-1 1-1 1-2l1-1-2-3-3-6v-1c-1-2-1 0-1-2l-1-1v-1c1-1-1-2-1-3v-2-1c-1-1-1-9 0-10 0-1 1-1 1-2v-1-3-2c3 5 6 8 10 12h1 0c-4-3-6-6-8-10v-1c-1-3-2-6-3-8h0c0 2 1 3 1 5l-1-1c-1 0-1-1-1-2l-1-2c-1-2-2-4-2-6s0-5-1-7c-1 0-1-1-1-2l-1 3c-3-2-4-5-5-8 0-6 4-11 7-16 4-6 8-13 13-18 3-3 8-5 12-7 6-3 12-8 18-13h0c2-1 4-2 5-4l1-1h2l2-2h1v-1c-2 0-7 1-9 0l-1-1c5 0 10 1 15 0 8-2 15-5 22-7h1c1 0 2-1 2-1h1c3 0 6-1 9-2 8-1 15 0 22 2 4 0 8 1 11 2 1 0 3 0 4 1h1c6 2 12 6 17 9 1 0 5 3 6 3 2 1 6 2 7 4 9 6 17 14 23 23l4 11c1 5 3 10 3 16v16l-1 14 1-3 1-22c1 1 1 3 1 4l1 1v-7c1 1 1 2 1 3l1 1c2-7 4-15 7-22h0c2-3 4-7 6-10 5-9 12-19 22-24l4-3c-1 0-2-1-3 0-2 0-11 6-11 6-2-1-8 3-8 3-6 7-12 12-16 20l1-9 2-1c1-3 3-5 4-7 1-1 1-2 1-3l7-7c1-2 3-3 4-4 6-5 13-9 21-11 4-2 8-4 13-5l2-1 9-2c9-1 18-2 28-2z" class="T"></path><path d="M371 281l1-1 1 4-2 1v-4z" class="P"></path><path d="M608 434l-1 8c-1 4-1 8-1 13v-14-2c0-2 1-3 1-5h1zm-246-71l2 2c-1 3-1 6-2 8l-1-1c2-2 1-7 1-9z" class="D"></path><path d="M370 279c1-2 0-4 0-5l1-1 1 2v5l-1 1-1-2z" class="G"></path><path d="M390 338c0 2-1 3 1 4-1 1-1 2-2 3h-2c-1 0-2 0-4-1 1 0 2 0 3-1 2-1 3-2 4-5z" class="D"></path><path d="M381 448h1c3-1 5-1 8-4-1 3-4 5-6 6s-4 1-6 0c2 0 3 0 5-1l-2-1z" class="N"></path><path d="M659 326v-1c3-1 6-1 9-2l-2 2c-1 2 0 2-2 3l-5-2z" class="B"></path><path d="M367 437c-2-4-4-9-5-13 1 0 1-1 3 0l3 10h-1 0v2 1z" class="O"></path><path d="M547 627c1-1 2-2 3-2 0 6-1 12-3 18h0-1l1-1v-4l1-1v-4c1-2 1-5-1-6z" class="H"></path><path d="M399 514c1 0 3 0 3 1s0 2 1 3l1 1v1l-1 1h-1v4l-1-4c-2-2-4-4-6-7h0c1 0 1 0 2 1v1l1-1c0 1 1 1 2 2l-1-3z" class="G"></path><path d="M484 672l1-3c1 1 1 3 1 4l1 1c1 2 2 5 3 7h-1v-1h-2c-2-2-3-3-3-5v-3zm142-222c-1-3 4-12 5-16v1c1 4-1 11-3 15h-1v-1l-1 1z" class="H"></path><path d="M626 450l1-1v1h1c1 3 5 6 7 8l2 1c-2 1-3 1-4 3v-1c0-3-5-7-7-11z" class="D"></path><path d="M651 451c-2 1-4 1-6 1-4-1-7-5-10-8 4 3 6 4 11 4h0c2 1 3 2 4 3h1z" class="N"></path><path d="M391 502c1 1 2 2 2 3l6 9 1 3c-1-1-2-1-2-2l-1 1v-1c-1-1-1-1-2-1-3-3-3-8-4-12z" class="P"></path><path d="M547 643v5l1 1v1h1c0-6 4-12 8-17-1 5-3 8-5 12s-3 7-4 10h0v-2l-1-3c-1-2-1-4-1-7h1z" class="J"></path><path d="M488 640v2l1 1c1 2 0 4 1 5 0 1-1 3-1 5-4 4-6 12-5 18v1 3c-2-7-1-14 1-21 1-2 2-4 2-7l-1-1h0c1-2 1-4 2-6z" class="V"></path><path d="M444 555l-5 1c-7 2-15-1-22-5l14 1h-1-4c3 2 7 2 10 2s5 0 8 1z" class="U"></path><path d="M443 576c2 3 3 6 6 9 0-1 0-2 1-3v1c0 2 1 2 3 3l-1 1 1 1 3 3v2l-4-2-4-2c-3-3-5-9-5-13z" class="B"></path><path d="M450 582v1c0 2 1 2 3 3l-1 1 1 1 3 3v2l-4-2c0-1 0-3-1-4 0-1-1-2-2-2 0-1 0-2 1-3z" class="C"></path><path d="M369 349h6c7 5 10 9 12 17v4c-3-7-5-11-11-16 0-1-4-3-5-4l-2-1z" class="E"></path><path d="M622 338c5-8 8-16 12-24 1-1 1-1 1-2 1-2 2-4 3-5h0l-15 36-1-1v-4z" class="M"></path><path d="M484 625c2 2 3 5 4 8v7c-1 2-1 4-2 6h0l-4 9c1-6 3-15 2-22h0 1c0-1 0-1-1-2v-6z" class="O"></path><path d="M470 629h1c3 4 3 9 5 14 1 4 3 10 4 15v2h0c-2-9-9-17-11-27 0-2 0-2 1-4z" class="B"></path><path d="M632 495l1-1c2 6 1 12-2 18-1 3-5 7-8 9h-1l-1 1-1-2 1-1c1-2 2-2 1-4l3-2c2-1 5-4 7-7 1-3 1-8 0-11z" class="P"></path><path d="M342 324c-4-6-8-12-8-19h0c3 3 9 7 11 11-2 0-2 0-3 2 0 2 1 4 2 5l-2 1z" class="L"></path><path d="M655 371h0l2-1c1-1 2-1 3-1-2 2-3 4-3 6v1c-1 2-1 5 0 8v4l-1-3v-11c-3 4-7 9-8 14v1c0 3 0 4 1 7 0 1 2 3 1 4l-3-6c-2 4-4 9-6 12h-1c7-11 7-24 15-35z" class="C"></path><path d="M348 329c6 5 12 10 18 14l9 6h-6c-3-3-7-5-11-8a57.31 57.31 0 0 1-11-11l1-1z" class="K"></path><path d="M653 348c5-2 9-4 14-7-5 6-13 9-19 14-5 5-8 10-10 16h0v-3c3-9 7-15 15-20z" class="E"></path><path d="M622 342l1 1c-2 7-2 13-4 20-1 4-3 8-4 12-2 4-3 9-5 13h-1l6-19c1-6 4-10 5-16 1-1 0-5 0-7 0-1 1-3 2-4z" class="P"></path><path d="M370 279v-1-7c0-13 2-23 5-34 1-3 1-5 3-7h0c-2 8-4 17-5 25-1 7-1 14-1 20l-1-2-1 1c0 1 1 3 0 5z" class="B"></path><path d="M562 594h1v1c-2 10-8 22-9 32l-1-1-3-3c1-2 2-5 3-7 3-7 5-15 9-22z" class="V"></path><path d="M533 635c0 2 0 3 1 4 1 2 1 2 1 3-1 9 11 23 4 30 1-3 0-6 0-8s0-3-1-5h-1v1c-2 2-2 6-2 9h0c-1-1 0-2-1-4v-2l-1-28z" class="P"></path><path d="M534 663h0v-9-1c1 0 2 1 2 3 0 1 0 1 1 2v1 1c-2 2-2 6-2 9h0c-1-1 0-2-1-4v-2z" class="O"></path><path d="M595 551h11c-4 3-9 4-14 5h-2c-2 1-4 2-7 1h-3c0-1-1-2-1-4h0 1c1 0 2 1 3 1-1-1-1-2-1-2 1-1 1-1 2-1h5c1 1 5 1 6 0z" class="M"></path><path d="M579 553h0c4 2 7 3 11 3-2 1-4 2-7 1h-3c0-1-1-2-1-4zm58-94h1c1 0 2 0 3-1h2v1c-2 1-5 3-6 5v1c-1 1-1 1-1 3l-3 6-1 3-1 4c-1 1-1 2-2 3v4l-2-2h0c1-1 1-3 1-5l3-14c1-2 1-4 2-6v1c1-2 2-2 4-3z" class="H"></path><defs><linearGradient id="Aq" x1="436.16" y1="557.497" x2="447.34" y2="537.003" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#716f70"></stop></linearGradient></defs><path fill="url(#Aq)" d="M453 544l1-1c1 0 1 0 2-1l1 1-1 1h0l-6 7c-1 0-3 1-4 2l-2 2c-3-1-5-1-8-1s-7 0-10-2h4 1v-1c1 1 2 1 2 0h7l2-2c1 0 2-1 3-2 1 0 1-1 2-1l1 1h1l4-3h0z"></path><path d="M368 434c3 6 6 11 12 14h1l2 1c-2 1-3 1-5 1l-4-2 7 23c1 3 4 8 3 11l-11-30c-2-5-5-10-6-15v-1-2h0 1z" class="G"></path><path d="M537 659h1c1 2 1 3 1 5s1 5 0 8c-2 2 0 4-1 6 0 1-2 1-2 3h0c-4 3-9 9-14 8h-3c6-5 13-10 17-17 2-3 2-8 1-12v-1z" class="F"></path><path d="M646 448c7-4 10-7 13-15 1-2 2-5 3-7v-1l2 1c-2 5-4 11-6 16-2 4-4 8-5 12l-6 17c-1 2-2 6-3 8 0-3 1-6 2-10l8-20c-1 0-2 1-3 2h-1c-1-1-2-2-4-3z" class="P"></path><path d="M456 591c2 0 4 1 5 0 1 0 1 0 2 1v1l1 1 5 16c1 3 2 7 4 9 1 2 4 4 5 6l6 6h0c1 1 1 1 1 2h-1 0c-1-1-2-2-2-3-3-3-8-7-11-8-1 0-2 0-3 1v-1c1-5-1-10-3-14-1-5-2-10-4-14-2 0-3 0-5-1v-2z" class="G"></path><defs><linearGradient id="Ar" x1="433.214" y1="489.582" x2="421.07" y2="500.17" xlink:href="#B"><stop offset="0" stop-color="#565151"></stop><stop offset="1" stop-color="#6c6f71"></stop></linearGradient></defs><path fill="url(#Ar)" d="M420 470h1c0 3 1 6 2 9l5 13 9 25c1 4 3 8 4 11v1h-1l-1-5h-1c0-1 0-2-1-3h0l-2-2-16-44h-1v-1-1h2v-3z"></path><defs><linearGradient id="As" x1="372.228" y1="319.549" x2="388.206" y2="307.004" xlink:href="#B"><stop offset="0" stop-color="#525457"></stop><stop offset="1" stop-color="#6d6967"></stop></linearGradient></defs><path fill="url(#As)" d="M371 285l2-1c2 12 6 24 10 35l7 19c-1 3-2 4-4 5-1 1-2 1-3 1h-1c2-1 4-2 5-4v-2c-2-6-4-12-6-17-4-12-8-24-10-36z"></path><defs><linearGradient id="At" x1="643.392" y1="311.916" x2="648.608" y2="314.084" xlink:href="#B"><stop offset="0" stop-color="#5b595b"></stop><stop offset="1" stop-color="#737372"></stop></linearGradient></defs><path fill="url(#At)" d="M644 345l-3-3c-1-1-4-3-4-4 0-2 1-4 2-5l6-19c6-16 9-32 9-50h1c1 20-3 39-9 58-2 6-4 12-7 17 2 2 4 3 6 5l-1 1z"></path><path d="M595 446c2-1 2-4 2-5 0-2 0-3 1-4 2-1 1-6 2-8 0-2 0-2 1-3-1 9-2 19 1 28 1 4 3 8 4 12v1c0 1 1 1 1 2l1 1-3 3h-1l-1-2c-1-1 0-1-1 0-5-8-6-16-7-25z" class="E"></path><path d="M627 486l2 2c2 2 3 4 4 6l-1 1c1 3 1 8 0 11-2 3-5 6-7 7l-3 2-3 1-1-1c0-2 2-5 3-6l1 1c1-1 2-3 2-4 3-4 2-10 0-14v-1c0-1-1-3 0-4l2 1 1-2z" class="B"></path><path d="M627 486l2 2c2 2 3 4 4 6l-1 1c-1-2-3-3-4-4s-2-1-2-2h-1l-1 3v-1c0-1-1-3 0-4l2 1 1-2z" class="C"></path><defs><linearGradient id="Au" x1="441.081" y1="571.556" x2="459.664" y2="558.504" xlink:href="#B"><stop offset="0" stop-color="#878686"></stop><stop offset="1" stop-color="#a9a8a8"></stop></linearGradient></defs><path fill="url(#Au)" d="M446 553c1-1 3-2 4-2v1l-2 2c-1 2-1 4-1 6h0l1-3 1 1c2 0 4-2 6-3 1-1 1-1 3-1l1-1h0l-5 5v1 1 1h-2c-1 1-1 2-1 3l-1 3v-2c-2 3-3 7-2 11l1 2 2 2c1 1 2 3 3 4l-4-1v-1c-1 1-1 2-1 3-3-3-4-6-6-9-2-7-1-14 2-20 1 0 1-1 1-2v-1z"></path><path d="M449 578l2 2c1 1 2 3 3 4l-4-1v-1c-1-1-2-1-2-3h1v-1z" class="G"></path><path d="M332 338l24 64c1 0 2-1 3-1h0l-2 2c1 4 3 8 4 12 1 2 0 5 0 8l-21-59-7-18c-1-2-2-5-2-7l1-1z" class="K"></path><path d="M687 338l8-7 1 1c-2 9-6 19-9 29l-13 37-10 28-2-1c1-3 2-6 3-10l28-80-4 4-2-1z" class="O"></path><path d="M639 378h0l-28 86-1 1c0-1-1-1-1-2v-3c2-9 7-18 10-27 6-18 12-38 20-55z" class="S"></path><path d="M621 522l1-1c0 4 1 8 0 12h-1c-1 5-6 12-11 14 0 1-1 1-2 1h-1c-4 2-8 1-12 3-1 1-5 1-6 0h-5c-1-1-2-2-2-3h0c8 2 16 0 23-4 7-5 14-14 16-22z" class="U"></path><defs><linearGradient id="Av" x1="531.657" y1="679.905" x2="514.188" y2="672.246" xlink:href="#B"><stop offset="0" stop-color="#5c5b5c"></stop><stop offset="1" stop-color="#969594"></stop></linearGradient></defs><path fill="url(#Av)" d="M534 665c1 2 0 3 1 4h0c0-3 0-7 2-9 1 4 1 9-1 12-4 7-11 12-17 17-2 1-5 2-8 2l-1-1c-1-1-1 0-1-1-1-1-1 0-1-1h0c2-2 6-7 6-9 5-1 10 0 15-3 3-2 4-7 5-11z"></path><defs><linearGradient id="Aw" x1="340.915" y1="360.107" x2="366.715" y2="352.856" xlink:href="#B"><stop offset="0" stop-color="#5c5c5d"></stop><stop offset="1" stop-color="#858383"></stop></linearGradient></defs><path fill="url(#Aw)" d="M332 332l19 15c1 1 3 4 5 4 9 9 18 22 22 35l3 12c2 4 4 7 6 10-1-1-3-3-5-4-3-6-3-12-5-18-1-3-3-7-6-10 0 4 0 10-1 14h0l-1 1v-1l1-5c1-8-2-14-6-20l-2-2c-7-12-20-17-29-27-1-1-1-1-2-1h0v-1c0-1 1-1 1-2h0z"></path><path d="M403 521c3 10 9 19 19 24 6 3 12 5 19 4h0 1l-2 2h-7c0 1-1 1-2 0h0-2c-9-1-15-2-22-9-4-5-6-10-5-17v-4h1z" class="K"></path><defs><linearGradient id="Ax" x1="643.395" y1="377.198" x2="603.605" y2="385.802" xlink:href="#B"><stop offset="0" stop-color="#6c6f6c"></stop><stop offset="1" stop-color="#837f82"></stop></linearGradient></defs><path fill="url(#Ax)" d="M607 434c6-27 14-53 23-78 2-4 4-12 7-14h0c1 1 1 2 0 3l-12 35-11 34c-2 7-5 13-6 20h-1z"></path><path d="M453 544l1-2c1 0 1-1 1-1 0-2-1-4-1-5l-4-11-13-38c-2-5-5-11-5-17 3 6 5 14 8 21l17 47 9-8h0c2-1 3-1 4-2s1-3 1-5c1-1 2-2 2-3 1-2 2-4 2-6l2 1c-1 5-2 9-5 13a120.94 120.94 0 0 1-16 16h0l1-1-1-1c-1 1-1 1-2 1l-1 1z" class="O"></path><defs><linearGradient id="Ay" x1="606.349" y1="471.267" x2="624.034" y2="497.811" xlink:href="#B"><stop offset="0" stop-color="#504f4f"></stop><stop offset="1" stop-color="#848484"></stop></linearGradient></defs><path fill="url(#Ay)" d="M606 466l5 6c5 5 10 10 16 14h0l-1 2-2-1c-1 1 0 3 0 4v1c2 4 3 10 0 14 0 1-1 3-2 4l-1-1 1-2c1 0 1-1 1-2h-1-1v-2l1-5c-1-6-5-11-10-15-2-2-5-4-6-7l-1-1h-1l-1-1c0-1 0-2-1-3 1-1 0-1 1 0l1 2h1l3-3-1-1c0-1-1-1-1-2v-1z"></path><path d="M622 498c1 2 2 4 2 6l-1 1h-1-1v-2l1-5z" class="C"></path><defs><linearGradient id="Az" x1="614.038" y1="480.789" x2="625.302" y2="484.673" xlink:href="#B"><stop offset="0" stop-color="#777776"></stop><stop offset="1" stop-color="#979696"></stop></linearGradient></defs><path fill="url(#Az)" d="M611 472c5 5 10 10 16 14h0l-1 2-2-1c-1 1 0 3 0 4-3-4-7-8-10-12-2-2-3-4-3-7z"></path><defs><linearGradient id="BA" x1="567.265" y1="593.358" x2="590.371" y2="555.197" xlink:href="#B"><stop offset="0" stop-color="#c2c1c1"></stop><stop offset="1" stop-color="#eae9e9"></stop></linearGradient></defs><path fill="url(#BA)" d="M590 556h2l2 1 1 1 1 1c-4 2-6 4-8 9l-7 19c-5 3-9 7-15 8l2-2c1-2 2-3 3-5l1-1h-1c3-3 5-4 6-7l1-3 1-3h0 1c0 1 0 0 1 1v1l1-1c2-6 1-11-1-16l-1-2h3c3 1 5 0 7-1z"></path><path d="M578 577l1-3h0 1c0 1 0 0 1 1v1c-1 3-5 9-9 11h-1c3-3 5-4 6-7l1-3z" class="N"></path><defs><linearGradient id="BB" x1="536.657" y1="622.759" x2="545.776" y2="624.039" xlink:href="#B"><stop offset="0" stop-color="#555454"></stop><stop offset="1" stop-color="#717071"></stop></linearGradient></defs><path fill="url(#BB)" d="M537 619l13-29c1 1 1 1 2 1h0c-4 14-11 27-13 41 3-3 7-7 11-9l3 3 1 1c1 2 2 4 2 5-1-2-4-7-6-7-1 0-2 1-3 2-3 1-7 4-8 7-2 5 0 10 2 15s3 10 3 15l-1 1c0 4-1 8-3 11h-1c3-6 5-13 3-19l-7-15c0-1 0-1-1-3-1-1-1-2-1-4 0-6 1-10 4-16z"></path><path d="M537 619c1 4-1 7-2 11 0 3 0 6-1 9-1-1-1-2-1-4 0-6 1-10 4-16z" class="S"></path><path d="M358 343l8 5c2 1 3 2 5 2 1 1 5 3 5 4s0 1 1 2c5 6 6 16 12 21h0-2c0 3 2 5 2 7l6 15 18 49c1 4 4 9 4 13 0 1-1 2-1 3h-1l-19-53-7-21c-1-4-2-8-4-12-1-3-3-6-4-9-2-3-3-7-5-10-4-6-13-12-19-16h1z" class="K"></path><path d="M641 288c0 7 0 13-3 19h0c-1 1-2 3-3 5 0 1 0 1-1 2-4 8-7 16-12 24l-1-1c-1-1-2-1-2-1l-2-2v2l-1-1c0-1 0-1-1-2l7-13 18-30s1-1 1-2z" class="E"></path><path d="M552 591h2c1 1 2 2 4 2 0 0 3-1 3 0 1 0 1 1 1 1-4 7-6 15-9 22-1 2-2 5-3 7-4 2-8 6-11 9 2-14 9-27 13-41h0z" class="T"></path><path d="M492 642l1 1c0 2 0 4-1 7-1 5-3 11-2 16 2 4 3 8 6 12 1 1 1 2 2 2 6 1 11 0 16-1 0 2-4 7-6 9h0c0 1 0 0 1 1 0 1 0 0 1 1l1 1-1 1c-1 0-1 1-3 1-5 0-9-4-13-7-1-2-2-4-3-5h-1c-1-2-2-5-3-7l-1-1c0-1 0-3-1-4l-1 3v-1c-1-6 1-14 5-18 0-2 1-4 1-5 1-2 1-4 2-6z" class="F"></path><path d="M489 653c-2 7-3 13-2 21l-1-1c0-1 0-3-1-4l-1 3v-1c-1-6 1-14 5-18z" class="I"></path><path d="M490 666c2 4 3 8 6 12 1 1 1 2 2 2 1 2 1 3 0 5-1-1-1-2-2-2-5-5-6-10-6-17z" class="P"></path><path d="M498 680c6 1 11 0 16-1 0 2-4 7-6 9h0c0 1 0 0 1 1 0 1 0 0 1 1-4-1-8-3-12-5 1-2 1-3 0-5z" class="N"></path><defs><linearGradient id="BC" x1="453.964" y1="560.977" x2="472.222" y2="534.906" xlink:href="#B"><stop offset="0" stop-color="#9f9e9e"></stop><stop offset="1" stop-color="#d8d8d7"></stop></linearGradient></defs><path fill="url(#BC)" d="M472 528v9c0 3 0 4-1 7v1l1 1v1c-2 1-2 2-2 3l-3 8v1h-3c-3 0-5 0-7 1-1 1-2 1-3 1v-1-1-1l5-5h0l-1 1c-2 0-2 0-3 1-2 1-4 3-6 3l-1-1-1 3h0c0-2 0-4 1-6l2-2v-1l6-7a120.94 120.94 0 0 0 16-16z"></path><path d="M474 590h1c1 7 3 15 7 20 1 4 3 8 4 12l1 5c1 2 1 4 1 6-1-3-2-6-4-8v6h0l-6-6c-1-2-4-4-5-6-2-2-3-6-4-9l-5-16-1-1v-1c3 0 5 0 8-1 1 0 2-1 3-1z" class="F"></path><path d="M474 590h1l-1 1-3 2c-2 1-4 1-6 1h-1l-1-1v-1c3 0 5 0 8-1 1 0 2-1 3-1z" class="H"></path><path d="M474 591l1-1c1 7 3 15 7 20 1 4 3 8 4 12l1 5c1 2 1 4 1 6-1-3-2-6-4-8l-10-34z" class="S"></path><defs><linearGradient id="BD" x1="671.233" y1="317.628" x2="693.854" y2="324.591" xlink:href="#B"><stop offset="0" stop-color="#aba9aa"></stop><stop offset="1" stop-color="#d4d3d3"></stop></linearGradient></defs><path fill="url(#BD)" d="M693 306c0-1 1-2 1-3h1 0 4 1l2-2h0l1-1v2 1h0c-1 1-1 2-2 3 0 1 0 1-1 2h0v1l-9 15-3 3c-2 3-6 6-10 9-2 2-5 4-8 6h-2c-1-1 0-1-1-1-5 3-9 5-14 7 1-2 2-2 3-3l3-2c-1 0-2 1-3 1-5 2-7 3-12 1l1-1c2 0 3 1 5 1l1-1c7-1 12-5 18-9h1l4-4c3-3 9-10 9-15l-3 1c1-1 2-2 4-3l9-8z"></path><path d="M680 317c1-1 2-2 4-3l9-8c-1 3-3 5-4 8-3 6-7 14-13 18h-1c-2 1-3 3-5 3l4-4c3-3 9-10 9-15l-3 1z" class="W"></path><defs><linearGradient id="BE" x1="434.52" y1="413.502" x2="376.822" y2="399.207" xlink:href="#B"><stop offset="0" stop-color="#585657"></stop><stop offset="1" stop-color="#717172"></stop></linearGradient></defs><path fill="url(#BE)" d="M389 345c1-1 1-2 2-3 3 6 5 13 7 19l19 55c3 8 6 16 7 24 1 7 1 15 0 22-1 2-2 6-3 8h-1l-1 1-3 3h-1l3-3c1-2 1-3 1-4v-1c5-11 0-22-2-33l-4-20c-7-23-15-46-24-68z"></path><path d="M384 286l16 29c4 6 9 13 11 20l-1 1h-1l-2 2c1 2 0 5-1 7l1 2c0 4 1 8 3 11l-1 2-10-27c-4-9-10-19-13-29h0c-3-6-2-12-2-18z" class="U"></path><path d="M407 338c1 2 0 5-1 7-1-2-3-4-2-6l3-1z" class="R"></path><path d="M392 313l1-1c3 3 7 8 7 13l-1 1c-3-3-5-9-7-13z" class="K"></path><path d="M386 304c3 1 4 6 6 9 2 4 4 10 7 13l2 1s1 0 1-1c1 2 0 3-1 4l-2 3c-4-9-10-19-13-29z" class="W"></path><defs><linearGradient id="BF" x1="407.384" y1="486.014" x2="399.087" y2="512.787" xlink:href="#B"><stop offset="0" stop-color="#8a8989"></stop><stop offset="1" stop-color="#bdbcbc"></stop></linearGradient></defs><path fill="url(#BF)" d="M419 467c0 1 0 2-1 4l-3 3h1l3-3 1-1v3h-2v1 1c-1 2-4 4-5 7l-4 5c-1 2-3 4-4 7v1 4 3c3 4 8 9 9 14h1c-5 1-9 0-13-1 0-1-2-1-3-1l-6-9c0-1-1-2-2-3v-1c0-12 11-16 18-23 4-4 7-8 10-11z"></path><path d="M402 503l3-4v3c0 3-1 6 0 10h0c-1 0-2-1-2-2v-2c-1-2-1-3-1-5z" class="C"></path><path d="M415 474h1l3-3 1-1v3h-2v1 1c-1 2-4 4-5 7l-4 5c-1 2-3 4-4 7v1 4l-3 4c0 2 0 3 1 5v2c-2-2-4-4-4-7 0-6 1-14 5-18 2-2 4-4 6-5 2-2 4-4 5-6z" class="K"></path><path d="M405 495v4l-3 4 1-5 2-3z" class="N"></path><path d="M403 498c0-4 1-6 2-9v2 1l1-1v-1c2-2 1-2 3-3-1 2-3 4-4 7v1l-2 3z" class="G"></path><path d="M718 302c1-1 3-2 4-2h1v2l-1-1v1c-1 2-2 7-3 8-3 0-6 4-8 7l-8 9c-2 2-4 4-7 5v1l-1-1-8 7c-3 3-7 6-11 9-1 1-3 4-6 5v-1l-1-1c-3-1-7 1-10 2l-5 3-7 7c-1 1-1 2-2 3v-1c1-2 4-5 4-7l-1-2c6-5 14-8 19-14 1 0 0 0 1 1h2c3-2 6-4 8-6 4-3 8-6 10-9l3-3c-1 1-1 2-2 3l1 1c1-1 2-2 4-3h1c0-1 1-2 2-3 3-3 5-8 9-11 1-1 2-3 3-4 0 2-1 3-2 5 0 1-3 5-2 6v1c1-1 1-2 2-3l1-2c3-3 6-9 9-12h1z" class="B"></path><path d="M695 325c0-1 1-2 2-3 3-3 5-8 9-11 0 2-1 5-3 6-1 1-2 2-4 3-1 2-3 4-4 5z" class="D"></path><path d="M722 302c-1 2-2 7-3 8-3 0-6 4-8 7l-8 9c-2 2-4 4-7 5l11-12c0-1 0-1 1-1 6-4 8-12 14-16z" class="H"></path><path d="M454 561c1 0 2 0 3-1 2-1 4-1 7-1h3v-1l3-8c0-1 0-2 2-3 0 2 0 6-1 7 0 1 0 2 1 2 1 3 0 8 1 11v4h2v1 1 1c-1 1-2 5-4 6-2 2-5 5-9 5-3 0-5 0-8-1-1-1-2-3-3-4l-2-2-1-2c-1-4 0-8 2-11v2l1-3c0-1 0-2 1-3h2z" class="C"></path><path d="M465 579c1-3 2-5 2-7 1-5 0-15 3-18h1c0 1 0 2 1 2 1 3 0 8 1 11v4h2v1 1l-10 6z" class="R"></path><path d="M449 578l-1-2c-1-4 0-8 2-11v2c1 4 2 8 5 10 2 2 5 3 8 2h2l10-6v1c-1 1-2 5-4 6-2 2-5 5-9 5-3 0-5 0-8-1-1-1-2-3-3-4l-2-2z" class="K"></path><path d="M451 580c2 1 3 2 6 3h0c4 0 7-1 10-1 2-1 3-2 4-2-2 2-5 5-9 5-3 0-5 0-8-1-1-1-2-3-3-4z" class="P"></path><path d="M410 358l2 5 15 40 13 40 18 51 8 22c1 2 2 6 4 8 0 0 1 0 1-1 0 2 0 4-1 5s-2 1-4 2h0l1-2c-1-5-4-11-6-16l-18-50-14-37-8-30-12-35 1-2z" class="K"></path><path d="M311 305l3 3h0c1 1 2 3 3 4s1 2 2 3v1c1 0 1 0 1 1 1 2 0 0 1 1l5 6h1 1l1-1c0-1 1-1 1-2l1-1-2-3-3-6v-1c-1-2-1 0-1-2l-1-1v-1c1-1-1-2-1-3v-2-1c-1-1-1-9 0-10 0-1 1-1 1-2 0 4-1 10 1 14v2c0 2 2 3 3 5h0v1l1 1 2 5c1 2 3 5 4 7l2 2v-1c-1-1-1-2-2-2l1-1c1 0 0 0 1 1v-2c0-2-1-3-1-4l-1-1v-1-1-1h-1v-2-1c-1-2-1-3-2-4s0 0 0-1v-1l-1-1 1-1c1 1 1 3 2 4 0 7 4 13 8 19l2-1 4 6-1 1a57.31 57.31 0 0 0 11 11c4 3 8 5 11 8l2 1c-2 0-3-1-5-2l-8-5h-1c6 4 15 10 19 16-3-1-6-3-9-5l-5-2-1-1-1-1c-2 0-3 0-4 1-2 0-4-3-5-4l-19-15h0c0 1-1 1-1 2v1h0l1 3-1 1-3-6v-4l-3-4h0l-11-16c-1-1-2-2-3-4z" class="D"></path><path d="M344 323l4 6-1 1-5-6 2-1z" class="U"></path><path d="M325 325l7 7h0c0 1-1 1-1 2v1h0l1 3-1 1-3-6v-4l-3-4z" class="X"></path><path d="M357 343h-1-1c-6-3-12-8-16-13v-1c3 3 6 5 9 8l1 1c1 0 2 0 3-1l6 6h-1z" class="V"></path><path d="M616 200c5 0 9 3 14 6 10 7 20 16 29 26 12 16 18 38 16 58-1 3-1 6-2 8-2 7-7 13-10 19-1 1-1 3-2 4l-3 3c0 1-1 1-1 2l-1-1v-4h1v-3l3-5 2-3 1-4c0-1 1-2 1-3l1 1c0-1 0-1 1-2h0v-2-1-1l1-1h-1l1-2h0v-1h0v-2c1-1 1-2 1-3h-1v-1-2c1-6 1-12 1-18l-1-1v-2-3c-1-1-1 0-1-1 0-4-2-7-3-10 0-1 0-2-1-3-1-3-2-5-4-8-1-2-2-5-4-7-1-1-1-2-2-3l-2-2c-1-2-4-4-6-6 0-1-1-2-2-3-1 0-2-1-3-2-2-1 0 1-1-1-1-1-2-1-2-2-3 0-5-3-6-4-3-3-7-6-10-8-1-1-2-1-4-2z" class="D"></path><path d="M689 339l4-4-28 80-1-1c0-1-1-1-2-2 0-1 0-2-1-3l-1-2-2-2c-1 0-2-1-2-2l-1-1c0-1-1-1-1-2-1-1 0-1-1-2-1 0 0 0-1-1h2 0v-2l-1-1h1 0v1l1 1v1s1-1 2 0c1 0 1 1 3 0-1-1-1-1-1-2-1-1-1-2-1-3l-1-1v-3-4c-1-3-1-6 0-8v-1c0-2 1-4 3-6-1 0-2 0-3 1l-2 1h0c2-5 5-9 9-13 2-2 3-5 6-7v1c3-1 5-4 6-5 4-3 8-6 11-9l2 1z" class="R"></path><path d="M670 351v1c3-1 5-4 6-5 4-3 8-6 11-9l2 1-10 8c-3 3-7 6-10 9-4 4-7 9-9 13-1 0-2 0-3 1l-2 1h0c2-5 5-9 9-13 2-2 3-5 6-7z" class="G"></path><path d="M581 548v-1l14-41 7-22c1-2 2-6 4-8 1 3 4 5 6 7 5 4 9 9 10 15l-1 5v2h1 1c0 1 0 2-1 2l-1 2c-1 1-3 4-3 6l1 1 3-1c1 2 0 2-1 4l-1 1 1 2c-2 8-9 17-16 22-7 4-15 6-23 4h0-1z" class="T"></path><path d="M615 510l6-7v2h1 1c0 1 0 2-1 2-2 1-5 3-7 3z" class="B"></path><path d="M615 510c2 0 5-2 7-3l-1 2c-1 1-3 4-3 6l1 1c-4 1-7 1-10 0 1-2 4-4 6-6z" class="J"></path><path d="M620 520c-1 1-2 2-4 2s-5-1-7-1c-1-1-2-3-3-4l-5-5 8 4c3 1 6 1 10 0l3-1c1 2 0 2-1 4l-1 1z" class="L"></path><path d="M418 475h1l16 44 2 2h0c1 1 1 2 1 3h1l1 5h1v-1l6 18c-1 0-1 1-2 1-1 1-2 2-3 2h-1 0c-7 1-13-1-19-4-10-5-16-14-19-24l1-1v-1l-1-1c-1-1-1-2-1-3 4 1 8 2 13 1h-1c-1-5-6-10-9-14v-3-4-1c1-3 3-5 4-7 1-1 4-5 4-5 1-3 4-5 5-7z" class="F"></path><path d="M404 519h2c4 2 8 1 12 0-2 2-4 3-8 3-2 0-4-1-6-2v-1z" class="O"></path><path d="M415 516c2 0 5-1 7-1-2 1-2 2-4 4-4 1-8 2-12 0h-2l-1-1c-1-1-1-2-1-3 4 1 8 2 13 1z" class="M"></path><path d="M435 519l2 2h0c1 1 1 2 1 3h1l1 5h1v-1l6 18c-1 0-1 1-2 1-2-4-4-8-5-13l-5-15z" class="G"></path><path d="M413 482h0v3c-2 4 2 13 3 17 1 3 4 10 6 12h1 0l-1 1c-2 0-5 1-7 1h-1c-1-5-6-10-9-14v-3-4-1c1-3 3-5 4-7 1-1 4-5 4-5z" class="T"></path><path d="M418 359c2 6 5 11 7 17 1 3 2 6 4 8 3 9 7 17 10 26l26 69 7 19c1 4 3 7 3 11 1 1 1 3 0 5 0 2-1 4-2 6 0 1-1 2-2 3 0 1-1 1-1 1-2-2-3-6-4-8l-8-22-18-51-13-40-15-40c0-1 0-2-1-3v-1c2 1 4 0 7 0zm177 87c1 9 2 17 7 25 1 1 1 2 1 3l1 1h1l1 1c-2 2-3 6-4 8l-7 22-14 41v1c-1-1-2-1-3-2-2-1-4-3-7-5-1 0-2-1-3-3l-5-5c4-15 10-29 15-43l9-25c2-6 5-14 8-19z" class="F"></path><path d="M570 534l2 3-1 4c-1 0-2-1-3-3 1-1 2-3 2-4z" class="S"></path><path d="M570 534l12-32c1 3-2 9-3 12l-7 23-2-3z" class="G"></path><path d="M578 546l25-72 1 1h1l1 1c-2 2-3 6-4 8l-7 22-14 41v1c-1-1-2-1-3-2z" class="P"></path><path d="M421 344c1 5 4 9 6 14 2 4 4 9 7 13 2 3 5 7 6 10 2 0 4 0 5 1h-4c-1 2 1 2 0 3 0 3 2 7 4 9v1c1 4 4 8 6 12 1 1 2 2 3 4 0 1 2 4 2 5l9 16c4 9 8 17 11 26 1 1 2 4 2 5h-1c0-1 0 0-1-1v-3l-1-1v-2c-1-1 0 1-1-1 0-1 0-2-1-3l-2 2v2c-1 0-2 1-2 1 0 1 1 3 2 4l2 7c1 1 1 2 1 3h0c0 4 1 7 2 10 2 11 3 23 1 34l-2-1c1-2 1-4 0-5 0-4-2-7-3-11l-7-19-26-69c-3-9-7-17-10-26-2-2-3-5-4-8-2-6-5-11-7-17l-1-2 1-3c0-1 0-3 1-3l2-7z" class="X"></path><defs><linearGradient id="BG" x1="458.051" y1="437.838" x2="474.006" y2="450.419" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#d2d1d1"></stop></linearGradient></defs><path fill="url(#BG)" d="M465 432c4 9 8 17 11 26 1 1 2 4 2 5h-1c0-1 0 0-1-1v-3l-1-1v-2c-1-1 0 1-1-1 0-1 0-2-1-3l-2 2v2c-1 0-2 1-2 1 0 1 1 3 2 4l2 7c1 1 1 2 1 3l-16-34 7-5z"></path><defs><linearGradient id="BH" x1="419.668" y1="388.203" x2="456.587" y2="386.229" xlink:href="#B"><stop offset="0" stop-color="#474648"></stop><stop offset="1" stop-color="#656564"></stop></linearGradient></defs><path fill="url(#BH)" d="M421 344c1 5 4 9 6 14 2 4 4 9 7 13 2 3 5 7 6 10 2 0 4 0 5 1h-4c-1 2 1 2 0 3 0 3 2 7 4 9v1c1 4 4 8 6 12 1 1 2 2 3 4 0 1 2 4 2 5l9 16-7 5-7-14c-1 0-3-3-3-4-3-5-7-10-9-15s-4-11-6-16c-1-1-1-3-2-4-2-2-4-5-4-7 0-1 0-1-1-2v2l2 3c0 1 1 2 1 3v1c-2-2-3-5-4-8-2-6-5-11-7-17l-1-2 1-3c0-1 0-3 1-3l2-7z"></path><defs><linearGradient id="BI" x1="455.587" y1="426.695" x2="462.865" y2="433.21" xlink:href="#B"><stop offset="0" stop-color="#797878"></stop><stop offset="1" stop-color="#969595"></stop></linearGradient></defs><path fill="url(#BI)" d="M451 423h0c1 1 2 1 2 3 0 0 1 1 1 2h1l4-5-1-2v-1c-1-1-1-2-2-3v-1l9 16-7 5-7-14z"></path><path d="M620 353c-1 6-4 10-5 16l-6 19h1c-3 9-5 19-7 29 0 1-1 8-2 9s-1 1-1 3c-1 2 0 7-2 8-1 1-1 2-1 4 0 1 0 4-2 5-3 5-6 13-8 19l-9 25c-5 14-11 28-15 43-1-2-2-3-2-5l-5-7c-3-5-2-6-1-11h0l35-94c2-7 5-12 8-18l14-34c0-3 1-4 0-6 1 0 2 1 2 1h2v1 2h0l1-1 1-5c1-1 1-2 2-3z" class="F"></path><path d="M609 388h1c-3 9-5 19-7 29 0 1-1 8-2 9s-1 1-1 3c-1 2 0 7-2 8-1 1-1 2-1 4 0 1 0 4-2 5-3 5-6 13-8 19l-9 25c-5 14-11 28-15 43-1-2-2-3-2-5 2-6 4-12 7-18l8-24 19-52c2-6 3-12 5-17l9-29z" class="O"></path><defs><linearGradient id="BJ" x1="709.709" y1="318.493" x2="689.55" y2="266.726" xlink:href="#B"><stop offset="0" stop-color="#cccbcb"></stop><stop offset="1" stop-color="#f1f0f0"></stop></linearGradient></defs><path fill="url(#BJ)" d="M711 246c1 1 1 2 2 3l1 2c1 1 7-1 8-1 7-2 13-8 21-3 2 1 3 1 3 3v1l2 2h1c-2 4-7 4-10 7-1 2-2 5-2 7-1 4-2 9-4 13-3 5-7 10-10 15-2 2-4 5-5 7h-1c-3 3-6 9-9 12l-1 2c-1 1-1 2-2 3v-1c-1-1 2-5 2-6 1-2 2-3 2-5-1 1-2 3-3 4-4 3-6 8-9 11-1 1-2 2-2 3h-1c-2 1-3 2-4 3l-1-1c1-1 1-2 2-3l9-15v-1h0c1-1 1-1 1-2 1-1 1-2 2-3h0v-1-2l-1 1h0l-2 2h-1-4 0-1c0 1-1 2-1 3l-9 8c-2 1-3 2-4 3l-1 1c-2 1-4 1-6 2-1 0-2 1-3 2h0l-2 1c-3 1-6 1-9 2v1h-2c0-1 1-1 1-2 3 0 15-14 17-16h1 2c1-1 2-2 3-4 5-4 10-9 15-14l13-17h1l1-1 1 2c1 0 1-1 1-1-2-2-4-5-5-8s-2-6-2-10v-1c2 1 3 3 5 5v-1-1l1-2c2-3 0-6-1-9z"></path><path d="M686 304h2c-1 3-8 5-10 8-2 1-3 3-4 4s-1 1-2 0h0c2-4 6-6 8-8 1-1 2-3 4-3l2-1z" class="J"></path><path d="M713 273l1-1c3-3 3-8 6-11l1-1h0c1-1 0-1 1 0l1 1c0 2-1 5-1 7-2 6-5 11-8 16-1 2-2 4-3 5v1c-1-1-1-1-2-3 0-4 2-9 1-14l1-1 1 2c1 0 1-1 1-1z" class="E"></path><path d="M711 246c1 1 1 2 2 3l1 2c1 1 7-1 8-1 7-2 13-8 21-3 2 1 3 1 3 3v1c-3-1-7-4-10-3l-1 1c-1 3-1 6-2 9-2 8-5 14-9 22-1 2-2 5-4 6 2-4 4-8 6-11 4-9 7-17 8-26-2 1-4 1-6 3l-3 2c-1 2-2 3-2 5v1 1l-1-1c-1-1 0-1-1 0h0l-1 1c-3 3-3 8-6 11l-1 1c-2-2-4-5-5-8s-2-6-2-10v-1c2 1 3 3 5 5v-1-1l1-2c2-3 0-6-1-9z" class="C"></path><path d="M706 255l1 1c1 2 1 2 1 4v1c1 1 2 3 3 5h1c3-2 5-7 7-10 1-1 4-2 6-2-1 2-2 3-2 5v1 1l-1-1c-1-1 0-1-1 0h0l-1 1c-3 3-3 8-6 11l-1 1c-2-2-4-5-5-8s-2-6-2-10z" class="K"></path><defs><linearGradient id="BK" x1="709.519" y1="279.917" x2="745.227" y2="282.165" xlink:href="#B"><stop offset="0" stop-color="#cac7c7"></stop><stop offset="1" stop-color="#f7f8f8"></stop></linearGradient></defs><path fill="url(#BK)" d="M720 286c2-1 3-4 4-6 4-8 7-14 9-22 1-3 1-6 2-9l1-1c3-1 7 2 10 3l2 2h1c-2 4-7 4-10 7-1 2-2 5-2 7-1 4-2 9-4 13-3 5-7 10-10 15-2 2-4 5-5 7h-1c-3 3-6 9-9 12l-1 2c-1 1-1 2-2 3v-1c-1-1 2-5 2-6 1-2 2-3 2-5s1-3 2-4c1-2 2-5 3-7 2-3 4-7 6-10z"></path><defs><linearGradient id="BL" x1="556.772" y1="517.379" x2="600.9" y2="348.643" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#4a4a4a"></stop></linearGradient></defs><path fill="url(#BL)" d="M607 348l1-2c0 1 1 4 2 4 0 2 1 6 2 8s0 3 0 6l-14 34c-3 6-6 11-8 18l-35 94h0c-1 1-1 1-1 3h-1v1 2h-1-1c-2-12-1-25 2-38h-1v-2h1v-2-1c1-1 1-1 1-2v-1l1-1v-2c0-1 0-1 1-2v-2c-1 0-2-1-4-2l-2 9c-1 2-1 4-2 5l-1 1c2-15 10-30 16-42l2-3 2-4c2-4 13-22 17-23 1-7 2-14 4-21 1-1 1-2 1-3h0c1-3 6-7 9-9 0-2 1-4 2-5l5-11 2-7z"></path><path d="M607 348c1 1 2 2 1 3l-2 4h-1l2-7z" class="U"></path><path d="M598 371c0 3-2 7-3 10-2 6-5 12-7 18-1 1-2 3-4 5h0c1-7 2-14 4-21 1-1 1-2 1-3h0c1-3 6-7 9-9z" class="F"></path><defs><linearGradient id="BM" x1="566.032" y1="434.015" x2="554.712" y2="462.385" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#d1d0d0"></stop></linearGradient></defs><path fill="url(#BM)" d="M563 434l7 1c-6 13-12 25-16 39l-1 4h-1v-2h1v-2-1c1-1 1-1 1-2v-1l1-1v-2c0-1 0-1 1-2v-2c-1 0-2-1-4-2l-2 9c-1 2-1 4-2 5l-1 1c2-15 10-30 16-42z"></path><defs><linearGradient id="BN" x1="584.111" y1="405.802" x2="570.781" y2="431.929" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#7c7c7c"></stop></linearGradient></defs><path fill="url(#BN)" d="M588 399c0 3-2 4-4 7 2 1 3 1 4 3h1l1-1c0-1 0-1 1-2v1c-5 9-13 16-19 25l-2 3-7-1 2-3 2-4c2-4 13-22 17-23h0c2-2 3-4 4-5z"></path><path d="M565 431c2-1 4-1 6 0l1 1-2 3-7-1 2-3z" class="G"></path><path d="M596 559l1-1 1 1-84 226-14-38-53-145c-5-15-14-28-18-43l1-1c1 1 2 3 2 5 3 5 5 12 8 17 1 3 4 4 5 7 0 1 1 2 2 3 6 10 9 21 13 32l4 8 5 14c1 3 3 5 4 8s1 6 2 9c2 5 5 10 6 16l5 14 16 42 8 22c1 4 3 9 5 14l27-76 12-31 14-40c5-11 10-23 13-35l7-19c2-5 4-7 8-9z" class="F"></path><path d="M475 574c2-3 4-7 6-10 1 1 2 3 2 5v1l10 27 10 25 5 15c2 4 3 8 6 12 6-7 8-18 11-26 7-15 13-30 18-46v-1c0-1 0-1-1-2v-2c1-1 3-2 4-2 2 1 3 3 4 4s2 3 2 3c1 1 2 3 3 3l1 1c2 1 5 2 8 3 2 0 4 0 7-1s5-4 6-7l1 1-1 3c-1 3-3 4-6 7h1l-1 1c-1 2-2 3-3 5l-2 2h-3v-1h-1s0-1-1-1c0-1-3 0-3 0-2 0-3-1-4-2h-2c-1 0-1 0-2-1l-13 29c-3 6-4 10-4 16l1 28v2c-1 4-2 9-5 11-5 3-10 2-15 3s-10 2-16 1c-1 0-1-1-2-2-3-4-4-8-6-12-1-5 1-11 2-16 1-3 1-5 1-7l-1-1c-1 2-1 4-2 6-1-1 0-3-1-5l-1-1v-2-7c0-2 0-4-1-6l-1-5c-1-4-3-8-4-12-4-5-6-13-7-20h-1c-1 0-2 1-3 1-3 1-5 1-8 1-1-1-1-1-2-1-1 1-3 0-5 0l-3-3-1-1 1-1c-2-1-3-1-3-3l4 1c3 1 5 1 8 1 4 0 7-3 9-5 2-1 3-5 4-6z" class="T"></path><path d="M479 575c1-2 2-5 4-6v1c-1 2-1 7-1 10-1-2-2-3-3-5z" class="V"></path><path d="M473 587h1c1 1 1 1 1 2v-1c0-1 0 0 1-1v-1l1-1v1h0c-2 4 2 8 1 12 1 4 4 8 4 12-4-5-6-13-7-20h-1v-2l-1-1z" class="D"></path><path d="M479 575c1 2 2 3 3 5-1 4-1 7 0 11-2-2-3-6-4-9v-1c0-2 0-4 1-6z" class="K"></path><path d="M577 576l1 1-1 3c-1 3-3 4-6 7-5 2-9 2-15 1h0c0-1 1-1 1-1 2 1 8 1 10 0v-2c-3 0-6 0-8-1l-3-2v-1c2 1 5 2 8 3 2 0 4 0 7-1s5-4 6-7z" class="D"></path><path d="M453 586c7 3 12 3 20 1l1 1v2c-1 0-2 1-3 1-3 1-5 1-8 1-1-1-1-1-2-1-1 1-3 0-5 0l-3-3-1-1 1-1z" class="U"></path><path d="M453 588c3 0 5 1 7 3 1 0 2 0 4-1 1 0 4 0 6 1h1c-3 1-5 1-8 1-1-1-1-1-2-1-1 1-3 0-5 0l-3-3z" class="K"></path><path d="M526 641v-2c2 3 1 11 1 14l-1-1c-3 5-7 12-11 16l5-12c1-1 1-4 2-5l4-10z" class="J"></path><path d="M526 641c1 2 1 3 0 5 0 1-1 3-2 5l-1-1-1 1h0l4-10z" class="I"></path><path d="M571 587h1l-1 1c-1 2-2 3-3 5l-2 2h-3v-1h-1s0-1-1-1c0-1-3 0-3 0-2 0-3-1-4-2h-2c-1 0-1 0-2-1 0-1 1-2 2-3l4 1h0c6 1 10 1 15-1z" class="W"></path><path d="M562 591h2c3 0 5-2 7-3-1 2-2 3-3 5-3 0-4 0-6-2z" class="B"></path><path d="M552 591l1-2s1 0 1 1c3 1 6 1 8 1 2 2 3 2 6 2l-2 2h-3v-1h-1s0-1-1-1c0-1-3 0-3 0-2 0-3-1-4-2h-2z" class="D"></path><path d="M486 622c1-3 0-9-1-12 4 6 7 13 8 20 0 4-1 8-1 12-1 2-1 4-2 6-1-1 0-3-1-5l-1-1v-2-7c0-2 0-4-1-6l-1-5z" class="C"></path><path d="M493 630c0 3 1 6 2 9v9c2 5 0 12 1 18v12c-3-4-4-8-6-12-1-5 1-11 2-16 1-3 1-5 1-7l-1-1c0-4 1-8 1-12z" class="H"></path><path d="M403 184h1c3 0 6-1 9-2 8-1 15 0 22 2 4 0 8 1 11 2 1 0 3 0 4 1h1c6 2 12 6 17 9 1 0 5 3 6 3 2 1 6 2 7 4l-1 1h1l4 3 4 3c4 3 6 7 9 10h0l1 1c-1 1-1 2-2 2h-1l-1-1c-2-3-5-6-7-9l-3-3c-2-1-3-3-5-4-1-1-2-1-3-2v1l-1-1c-1 1-1 1-2 1-4-3-9-3-13-5l-9-2v-1l-2-1c-2 0-5 0-7-1h-5c-2-1-5 0-7 0-6 1-13 1-19 2-2 1-5 2-8 3v1c-1 2-8 7-10 9h-1l3-3c-1 0-1 0-2 1h-1l-1 1h-1l-1 1-1 1c-1 0-2 1-2 1l-1 1h-1l-1 1c-3 1-5 4-8 6-4 5-10 10-14 15-5 7-8 15-11 23l-2 10c-2 5-1 11-1 17 0 3-1 7 0 10 1 2 1 6 2 7 2 2 4 2 5 5l3 2h-1s0 1 0 0c-2-1-4-2-5-3l-2-1c-1-1-2-2-4-3v-1l-7-4-4-3h0c-4-3-6-6-8-10v-1c-1-3-2-6-3-8h0c0 2 1 3 1 5l-1-1c-1 0-1-1-1-2l-1-2c-1-2-2-4-2-6s0-5-1-7c-1 0-1-1-1-2l-1 3c-3-2-4-5-5-8 0-6 4-11 7-16 4-6 8-13 13-18 3-3 8-5 12-7 6-3 12-8 18-13h0c2-1 4-2 5-4l1-1h2l2-2h1v-1c-2 0-7 1-9 0l-1-1c5 0 10 1 15 0 8-2 15-5 22-7h1c1 0 2-1 2-1z" class="J"></path><path d="M445 194l9 1-4 1c-2 0-5 0-7-1l2-1z" class="X"></path><path d="M404 201v-1c2-1 4-3 7-3v-1c8-2 15-3 23-3 3 0 7 0 11 1l-2 1h-5c-2-1-5 0-7 0-6 1-13 1-19 2-2 1-5 2-8 3v1z" class="S"></path><path d="M454 195c8 2 15 4 22 9-1 1-1 1-2 1-4-3-9-3-13-5l-9-2v-1l-2-1 4-1z" class="L"></path><path d="M323 262c2 3 2 5 3 8s2 6 2 10h-1l1 2c0-2 0-4 1-5v-6c1-2 1-4 1-6v-2c1-4 1-7 2-11l2-6v-2c1 1 1 1 1 2-1 1-1 3-2 4l-2 14c-1 3-1 11 0 14 1 1 1 3 1 4 1 2 1 3 2 5-2-1-2-1-3-2l-4-2c-1-3-2-6-3-8-1-5-1-8-1-13z" class="B"></path><path d="M318 260c1-6 2-10 8-14-1 5-3 11-3 16s0 8 1 13h0c0 2 1 3 1 5l-1-1c-1 0-1-1-1-2l-1-2c-1-2-2-4-2-6s0-5-1-7c-1 0-1-1-1-2z" class="L"></path><path d="M338 267c1-15 5-28 13-41 2-4 5-8 9-11 3-3 6-4 8-7-1 2-3 8-5 9v-1c-2 0-3 2-4 3-15 17-21 41-20 63 1 4 1 11 3 15 1 1 2 3 4 4l-7-4-4-3h0c-4-3-6-6-8-10v-1l4 2c1 1 1 1 3 2v1h3v-2h-1l1-1v-1c2-4 0-12 1-17z" class="E"></path><path d="M334 287v1h3v-2h-1l1-1v-1c2-4 0-12 1-17v18c1 3 2 7 2 11h-1v1l-4-3h0c-4-3-6-6-8-10v-1l4 2c1 1 1 1 3 2z" class="C"></path><path d="M474 471c0-1 0-2-1-3l-2-7c-1-1-2-3-2-4 0 0 1-1 2-1v-2l2-2c1 1 1 2 1 3 1 2 0 0 1 1v2l1 1v3c1 1 1 0 1 1h1c4 14 6 29 5 43 0 6-2 11-2 16 0 2 2 6 3 9l8 20 23 63 22-58 7-20 3-9c1-3-1-9-1-12-2-11-2-21 0-31 0-3 1-6 1-8l1-1c1-1 1-3 2-5l2-9c2 1 3 2 4 2v2c-1 1-1 1-1 2v2l-1 1v1c0 1 0 1-1 2v1 2h-1v2h1c-3 13-4 26-2 38h1 1v-2-1h1c0-2 0-2 1-3-1 5-2 6 1 11l5 7c0 2 1 3 2 5l5 5c1 2 2 3 3 3 3 2 5 4 7 5 1 1 2 1 3 2h1c0 1 1 2 2 3-1 0-1 0-2 1 0 0 0 1 1 2-1 0-2-1-3-1h-1 0c0 2 1 3 1 4l1 2c2 5 3 10 1 16l-1 1v-1c-1-1-1 0-1-1h-1 0l-1 3-1-1c-1 3-3 6-6 7s-5 1-7 1c-3-1-6-2-8-3l-1-1c-1 0-2-2-3-3 0 0-1-2-2-3s-2-3-4-4c-1 0-3 1-4 2v2c1 1 1 1 1 2v1c-5 16-11 31-18 46-3 8-5 19-11 26-3-4-4-8-6-12l-5-15-10-25-10-27v-1c0-2-1-4-2-5-2 3-4 7-6 10v-1-1-1h-2v-4c-1-3 0-8-1-11-1 0-1-1-1-2 1-1 1-5 1-7v-1l-1-1v-1c1-3 1-4 1-7v-9c3-4 4-8 5-13 2-11 1-23-1-34-1-3-2-6-2-10h0z" class="F"></path><path d="M474 544c1 1 1 4 2 5l5 15c-2 3-4 7-6 10v-1-1-1h-2v-4c-1-3 0-8-1-11-1 0-1-1-1-2 1-1 1-5 1-7v-1l-1-1v-1l1 1c1 1 0 3 0 5h1c2 2 3 4 4 7 1-2-1-4-2-6v-1h0v-2c-1-1-1-1-1-2v-1l-1-1h1z" class="J"></path><path d="M474 544v-4h1c5 13 35 98 40 100 3-5 5-11 7-17l14-35c2-4 4-8 7-12v1c-5 16-11 31-18 46-3 8-5 19-11 26-3-4-4-8-6-12l-5-15-10-25-10-27v-1c0-2-1-4-2-5l-5-15c-1-1-1-4-2-5z" class="E"></path><defs><linearGradient id="BO" x1="572.573" y1="570.796" x2="547.598" y2="548.171" xlink:href="#B"><stop offset="0" stop-color="#969595"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#BO)" d="M551 516h1 1v-2-1h1c0-2 0-2 1-3-1 5-2 6 1 11l5 7c0 2 1 3 2 5l5 5c1 2 2 3 3 3 3 2 5 4 7 5 1 1 2 1 3 2h1c0 1 1 2 2 3-1 0-1 0-2 1 0 0 0 1 1 2-1 0-2-1-3-1h-1 0c0 2 1 3 1 4l1 2c2 5 3 10 1 16l-1 1v-1c-1-1-1 0-1-1h-1 0l-1 3-1-1c-1 3-3 6-6 7s-5 1-7 1c-3-1-6-2-8-3l-1-1c-1 0-2-2-3-3 0 0-1-2-2-3s-2-3-4-4c0-2-1-4 0-6 0-2 1-5 2-7l5-13c0-5 1-10-1-15v-2-1c-1-1 0-3 0-4s-1-4-1-6z"></path><path d="M561 577h1c1 1 1 1 2 1 1 1 2 0 3 0 1-3 3-5 4-8 1-1 1-3 3-3h1v-3h1c-1 4-2 9-5 13-2 1-5 2-7 2-1 0-2-1-3-2z" class="C"></path><path d="M576 563c1 5 2 9 1 13-1 3-3 6-6 7s-5 1-7 1c-2-2-3-2-4-4l-2-3h1 1 1c1 1 2 2 3 2 2 0 5-1 7-2 3-4 4-9 5-13v-1z" class="M"></path><defs><linearGradient id="BP" x1="558.203" y1="563.916" x2="546.286" y2="567.229" xlink:href="#B"><stop offset="0" stop-color="#464646"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#BP)" d="M553 544h1c0 2 1 3 1 5 4 7 4 16 3 23v2h0v-1l2 4h-1-1l2 3c1 2 2 2 4 4-3-1-6-2-8-3l-1-1c-1 0-2-2-3-3 0 0-1-2-2-3s-2-3-4-4c0-2-1-4 0-6 0-2 1-5 2-7l5-13z"></path><path d="M552 577l1-1 3 3c-1 1 0 1-1 1s-2-2-3-3z" class="K"></path><path d="M555 549c4 7 4 16 3 23v2h0-1c-3-1-6-3-8-5 1-7 4-13 6-20z" class="R"></path><defs><linearGradient id="BQ" x1="584.442" y1="553.546" x2="549.751" y2="548.064" xlink:href="#B"><stop offset="0" stop-color="#8e8d8d"></stop><stop offset="1" stop-color="#dededd"></stop></linearGradient></defs><path fill="url(#BQ)" d="M551 516h1 1v-2-1h1c0-2 0-2 1-3-1 5-2 6 1 11l5 7c0 2 1 3 2 5l5 5c1 2 2 3 3 3 3 2 5 4 7 5 1 1 2 1 3 2h1c0 1 1 2 2 3-1 0-1 0-2 1 0 0 0 1 1 2-1 0-2-1-3-1h-1 0c0 2 1 3 1 4l1 2c2 5 3 10 1 16l-1 1v-1c-1-1-1 0-1-1h-1 0l-1 3-1-1c1-4 0-8-1-13-1-2-6-4-8-7l1-1v1l1-1h-1c-1-2-1-2-2-3l-1-1c-1-1-2-2-2-3h0l-1-1c0-1-1-2-1-3h-1v-1l-1 1c0-1-1-1-1-2l-2-2v1l-1-1h0c-2 2-1 3-2 4h-1c0-5 1-10-1-15v-2-1c-1-1 0-3 0-4s-1-4-1-6z"></path><path d="M581 559h-2c-2-1-3-3-3-5 0-1 0-2-1-3 1 0 2 1 3 1l1 1c0 2 1 3 1 4l1 2z" class="N"></path><defs><linearGradient id="BR" x1="568.709" y1="535.094" x2="565.11" y2="538.495" xlink:href="#B"><stop offset="0" stop-color="#474647"></stop><stop offset="1" stop-color="#5e5d5e"></stop></linearGradient></defs><path fill="url(#BR)" d="M551 516h1 1v-2-1h1c0-2 0-2 1-3-1 5-2 6 1 11l5 7c0 2 1 3 2 5l5 5c1 2 2 3 3 3 3 2 5 4 7 5 1 1 2 1 3 2h1c0 1 1 2 2 3-1 0-1 0-2 1 0 0 0 1 1 2-1 0-2-1-3-1h-1 0l-1-1c-8-10-21-17-26-30 0-1-1-4-1-6z"></path><defs><linearGradient id="BS" x1="570.521" y1="282.414" x2="674.488" y2="198.581" xlink:href="#B"><stop offset="0" stop-color="#939192"></stop><stop offset="1" stop-color="#e5e5e5"></stop></linearGradient></defs><path fill="url(#BS)" d="M609 178h9 4c3 0 4 1 5 2l-8 1h0c-1 1-2 1-3 0h-5v1h0l8 1c5 2 10 3 15 4 2 1 5 2 7 3s4 2 6 2c3 1 7 0 10 0v1c-2 0-2 0-4 1l1 1 1 1h0l9 6c0 1 1 1 2 1v1l2 2 2 1c3 2 7 5 11 7l8 4a57.31 57.31 0 0 1 11 11c1 1 2 2 2 3l6 9c0 1 2 5 3 5 1 3 3 6 1 9l-1 2v1 1c-2-2-3-4-5-5v1c0 4 1 7 2 10s3 6 5 8c0 0 0 1-1 1l-1-2-1 1h-1l-13 17c-5 5-10 10-15 14-1 2-2 3-3 4h-2-1c-2 2-14 16-17 16l3-3c1-1 1-3 2-4 3-6 8-12 10-19 1-2 1-5 2-8 2-20-4-42-16-58-9-10-19-19-29-26-5-3-9-6-14-6-2-1-4-2-6-2-5-2-10-2-15-3-2 0-5 0-7 1h-3c-1-1-3-1-4-2-5 1-11 1-16 3-2 1-4 2-7 3 0 0-2 1-3 1s-2-1-3 0c-2 0-11 6-11 6-2-1-8 3-8 3-6 7-12 12-16 20l1-9 2-1c1-3 3-5 4-7 1-1 1-2 1-3l7-7c1-2 3-3 4-4 6-5 13-9 21-11 4-2 8-4 13-5l2-1 9-2c9-1 18-2 28-2z"></path><path d="M525 210l7-7 1 1c-3 2-5 4-7 7s-4 7-5 10h1 0c0-1 1-1 1-2 1-1 1-2 2-2 2-2 6-6 7-8l1 1c-6 7-12 12-16 20l1-9 2-1c1-3 3-5 4-7 1-1 1-2 1-3z" class="B"></path><path d="M702 256l-2-9 1-1c3 2 4 5 5 8v1c0 4 1 7 2 10h0l-1-1c-1-1-1-2-2-3s-1-1-1-3c-1 0-2-1-2-2z" class="L"></path><path d="M570 183l2-1 9-2c9-1 18-2 28-2l-4 1h-2c-3 2-6 2-9 2l-19 5v-1h1c1 0 2-1 3-1h2s1-1 2-1l5-1c-3 0-6 1-9 1h-2 0c1-1 4-1 5-2-5 1-11 3-16 4-3 1-6 3-9 3 4-2 8-4 13-5z" class="N"></path><defs><linearGradient id="BT" x1="707.908" y1="259.84" x2="698.847" y2="280.785" xlink:href="#B"><stop offset="0" stop-color="#3e3e3e"></stop><stop offset="1" stop-color="#5e5d5d"></stop></linearGradient></defs><path fill="url(#BT)" d="M702 256c0 1 1 2 2 2 0 2 0 2 1 3s1 2 2 3l1 1h0c1 3 3 6 5 8 0 0 0 1-1 1l-1-2-1 1h-1l-13 17h-1l3-4c0-1 1-2 1-4l1-3 1-3c2-6 1-13 1-20z"></path><path d="M681 304c2-5 4-10 5-15v-1-5c1-8 1-18-1-25s-3-14-6-20c-2-6-6-12-10-17-2-3-5-6-7-8l-1 1c-2-3-3-5-4-7 4 4 9 7 13 12 2 1 3 3 4 5 5 8 9 16 11 25l3 15v9l1 2-1 6v8h2v-2l1 1 2-1c0-1 1-2 2-3 1 0 2-1 3-2 0-1 1-2 2-3l-1 3c0 2-1 3-1 4l-3 4h1c-5 5-10 10-15 14z" class="P"></path><path d="M688 281v8h2v-2l1 1 2-1c0-1 1-2 2-3 1 0 2-1 3-2 0-1 1-2 2-3l-1 3c-4 4-6 9-11 13l-1-1v-3-4c1-1 1-4 1-6z" class="N"></path><path d="M581 194c4-1 8-1 11-1 14 0 25 2 37 9 7 5 13 10 19 16 14 12 25 25 30 43 2 11 3 23 1 35-1 4-1 7-4 11v1c-2 2-14 16-17 16l3-3c1-1 1-3 2-4 3-6 8-12 10-19 1-2 1-5 2-8 2-20-4-42-16-58-9-10-19-19-29-26-5-3-9-6-14-6-2-1-4-2-6-2-5-2-10-2-15-3-2 0-5 0-7 1h-3c-1-1-3-1-4-2z" class="O"></path><defs><linearGradient id="BU" x1="413.696" y1="240.877" x2="445.11" y2="318.129" xlink:href="#B"><stop offset="0" stop-color="#191919"></stop><stop offset="1" stop-color="#434343"></stop></linearGradient></defs><path fill="url(#BU)" d="M438 195h5c2 1 5 1 7 1l2 1v1l9 2c4 2 9 2 13 5 1 0 1 0 2-1l1 1v-1c1 1 2 1 3 2 2 1 3 3 5 4l3 3c2 3 5 6 7 9l1 1h1c1 0 1-1 2-2l-1-1h0c-3-3-5-7-9-10l-4-3-4-3h-1l1-1c9 6 17 14 23 23l4 11c1 5 3 10 3 16v16l-1 14c-1 13-4 26-6 39-2 0-4 0-6 1v1-2c1-3 3-9 3-12-1 1-2 5-3 7h0c-1-1-1-2 0-3l-1-1c0-1 0 0-1-1-1 1-1 1-1 2l-3 3c-1-2 2-5 3-8v-3c1-3 3-6 4-10-2 2-3 5-4 7-3 4-7 8-11 12-3 2-5 4-8 5-5 3-10 4-16 6-1 1-3 2-5 2 0-1 0-2 1-3 2-3 8-4 10-6-4 1-9 3-13 5s-8 6-12 9-7 6-10 10l-1-6-1-5c-4 3-6 8-8 12l-2 7c-1 0-1 2-1 3l-1 3 1 2c-3 0-5 1-7 0v1c1 1 1 2 1 3l-2-5c-2-3-3-7-3-11l-1-2c1-2 2-5 1-7l2-2h1l1-1c-2-7-7-14-11-20l-16-29h1c-1-5-1-7 1-12h-1l-1-1c0-2-1-6 0-8 1-8 2-16 5-23 5-16 19-33 34-41h0c5-3 10-4 15-6z"></path><path d="M386 274c3-7 6-14 11-21 3 3 6 4 9 5h0-3c-5 3-9 8-13 11-2 2-5 7-5 10 0 2 1 5 0 7-1-5-1-7 1-12z" class="Q"></path><path d="M384 265h1 0c2-6 4-10 8-15v-2-4c1-4 3-8 7-10 3-2 7-3 10-2 5 1 7 4 9 7 2 5 2 8 0 12l-1 1c3 2 5 3 7 5-3-1-5-2-8-3h0c-2 1-5 2-7 3-1 0-3 0-4 1-3-1-6-2-9-5-5 7-8 14-11 21h-1l-1-1c0-2-1-6 0-8z" class="D"></path><path d="M405 235h5c3 1 5 3 6 5s1 6 0 8c-1 3-4 4-7 6-2 0-4 0-6-1-2 0-4-2-5-4s-1-5 0-7c1-4 4-5 7-7z" class="L"></path><path d="M409 244h3c1 2 1 2 0 3l-1 1h-1c-1-1-2-1-2-3l1-1z" class="T"></path><defs><linearGradient id="BV" x1="442.044" y1="355.596" x2="444.251" y2="304.18" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#BV)" d="M474 304l1-1 2 3c1 0 5-1 6 0-1 1-2 2-4 3-4 2-7 3-11 4l-4 1c-1 1-2 1-3 1l-3 1h-2c-1 0-2 1-2 1h-2c0 1-1 1-2 1h-1l-1 1c-1 0-1 0-2 1h-2c-2 1-4 3-5 4h-1c-1 1-2 2-3 2l-2 2c-1 1 0 0-2 1-1 1-1 2-2 3-4 3-6 8-8 12l-2 7c-1 0-1 2-1 3l-1 3 1 2c-3 0-5 1-7 0v1c1 1 1 2 1 3l-2-5c-2-3-3-7-3-11l-1-2c1-2 2-5 1-7l2-2h1l1-1c4-7 9-15 16-18 2-1 4-2 7-2h0c-3-2-5-4-7-6 4 0 9-1 14 0 7 0 18 3 25 0 3-1 6-3 8-5z"></path><path d="M427 317h1c1 0 3 0 4-1h0c1 1 2 1 2 1h0c-1 1-2 1-2 2h-1 0 1v1c-3 2-6 4-9 8 0 1-2 2-3 3v1c-2 0-5 5-7 7v1c-1 1-1 1-1 2l-1 1c-1 2-1 5 0 7s2 3 3 4h2l1 1v-1h1l-1 3 1 2c-3 0-5 1-7 0v1c1 1 1 2 1 3l-2-5c-2-3-3-7-3-11l-1-2c1-2 2-5 1-7l2-2h1l1-1c4-7 9-15 16-18z" class="C"></path><path d="M407 347h1c3 2 2 11 8 10l1-1v1l1 2c-3 0-5 1-7 0v1c1 1 1 2 1 3l-2-5c-2-3-3-7-3-11z" class="H"></path><path d="M384 265c1-8 2-16 5-23 5-16 19-33 34-41-1 4-5 5-7 9-2 2-4 3-5 5l-2 2c3 0 5-1 8-1h6c15 2 26 7 35 18 7 9 9 17 10 28l-1 6c-1-3 1-9-1-12v-1c-2-3-7-8-10-10s-5-3-8-4l-9-3h-4c-3-1-7-1-10-1h-1c-1 1-3 1-5 2-2-3-4-6-9-7-3-1-7 0-10 2-4 2-6 6-7 10v4 2c-4 5-6 9-8 15h0-1z" class="Q"></path><defs><linearGradient id="BW" x1="453.59" y1="342.509" x2="464.14" y2="263.29" xlink:href="#B"><stop offset="0" stop-color="#959495"></stop><stop offset="1" stop-color="#d6d6d5"></stop></linearGradient></defs><path fill="url(#BW)" d="M492 261c0-1 0-2 2-3 1 1 2 3 3 4l1 4h1 0c0 2 1 3 1 5v2c1 4 0 7 2 11h1v1l-2 7c-1 1-1 2-2 3v1c-2 2-3 5-4 7-3 4-7 8-11 12-3 2-5 4-8 5-5 3-10 4-16 6-1 1-3 2-5 2 0-1 0-2 1-3 2-3 8-4 10-6-4 1-9 3-13 5s-8 6-12 9-7 6-10 10l-1-6-1-5c1-1 1-2 2-3 2-1 1 0 2-1l2-2c1 0 2-1 3-2h1c1-1 3-3 5-4h2c1-1 1-1 2-1l1-1h1c1 0 2 0 2-1h2s1-1 2-1h2l3-1c1 0 2 0 3-1l4-1c4-1 7-2 11-4 2-1 3-2 4-3-1-1-5 0-6 0l-2-3-1 1c-6 2-13 2-20 2-8 0-16 0-24-1-5 0-9-1-13-4l-2-1c6 1 12 2 17 1 10-2 18-4 25-11 5-5 11-13 10-21v-1l1-6v1c1 2 1 5 0 7v2l4-1 6-2-1-1v-1-1-3h1c0 5 1 8 4 12 0-4 0-8 1-12v5h4c1 0 3-1 4-1l1-1h0c-1-1-1-3 0-4v-1z"></path><path d="M430 337h1v3h1c1-1 1-2 2-3h0v-1c1-1 1-3 1-4v1h0l1 1c0-1 1-2 2-3 0 1-1 1-1 2 2 0 2 0 3-1h1v1c-4 3-7 6-10 10l-1-6z" class="C"></path><path d="M492 261c0-1 0-2 2-3 1 1 2 3 3 4l1 4c0 4 0 8-1 12 0 6-3 7-7 11 1-2 3-4 4-6 1-3 1-10 0-13s-2-6-2-9z" class="L"></path><defs><linearGradient id="BX" x1="498.624" y1="227.132" x2="485.881" y2="239.862" xlink:href="#B"><stop offset="0" stop-color="#3c3b3b"></stop><stop offset="1" stop-color="#636263"></stop></linearGradient></defs><path fill="url(#BX)" d="M438 195h5c2 1 5 1 7 1l2 1v1l9 2c4 2 9 2 13 5 1 0 1 0 2-1l1 1v-1c1 1 2 1 3 2 2 1 3 3 5 4l3 3c2 3 5 6 7 9l1 1h1c1 0 1-1 2-2l-1-1h0c-3-3-5-7-9-10l-4-3-4-3h-1l1-1c9 6 17 14 23 23l4 11c1 5 3 10 3 16v16l-1 14c-1 13-4 26-6 39-2 0-4 0-6 1v1-2c1-3 3-9 3-12-1 1-2 5-3 7h0c-1-1-1-2 0-3l-1-1c0-1 0 0-1-1-1 1-1 1-1 2l-3 3c-1-2 2-5 3-8v-3c1-3 3-6 4-10v-1c1-1 1-2 2-3l2-7v-1h-1c-2-4-1-7-2-11v-2c0-2-1-3-1-5h0-1l-1-4c-1-1-2-3-3-4-2 1-2 2-2 3v1c-1 1-1 3 0 4h0l-1 1c-1 0-3 1-4 1h-4v-5c-1 4-1 8-1 12-3-4-4-7-4-12h-1v3 1 1l1 1-6 2-4 1v-2c1-2 1-5 0-7v-1c-1-11-3-19-10-28-9-11-20-16-35-18h-6c-3 0-5 1-8 1l2-2c1-2 3-3 5-5 2-4 6-5 7-9h0c5-3 10-4 15-6z"></path><path d="M506 272c0 6 0 12-2 18l-1-5v-1l1-2v-2c0-1 1-3 1-4v-2c0-1 0-1 1-2z" class="T"></path><path d="M477 205v-1c1 1 2 1 3 2 2 1 3 3 5 4l3 3c2 3 5 6 7 9l1 1h1c1 0 1-1 2-2l-1-1h0c-3-3-5-7-9-10l-4-3-4-3h-1l1-1c9 6 17 14 23 23l4 11c1 5 3 10 3 16-1 2-1 4-1 6v5 5c0-4 1-9-1-12-4-22-15-38-32-52z" class="F"></path><path d="M438 195h5c2 1 5 1 7 1l2 1v1l9 2c-4 0-7-1-11 0-1 0-3 1-5 1-9 2-16 4-24 9-3 1-7 5-10 5 1-2 3-3 5-5 2-4 6-5 7-9h0c5-3 10-4 15-6z" class="W"></path><path d="M438 195h5c2 1 5 1 7 1l2 1v1c-15-2-24 3-36 12 2-4 6-5 7-9h0c5-3 10-4 15-6z" class="Y"></path><path d="M417 216h0v-1l7-4c9-5 22-9 33-8 6 0 15 3 20 7 2 1 4 4 5 6l2 1h-3v1c-2-2-4-3-6-4h-1c-3-1-6 0-9 0h-2c-3 0-5-2-7-4 0-1-1-1-2-2l-1 1h0v-1h-4c-2 1-4 0-6 1h-1c-5 0-9 2-13 3-2 1-4 1-6 3v1h0-6z" class="J"></path><path d="M456 210c1-1 1-1 3-1h0c1-1 0 0 1 0h3c2 1 5 1 7 1l6 3 3 2c1 0 2 1 3 1l2 1h-3v1c-2-2-4-3-6-4h-1c-3-1-6 0-9 0h-2c-3 0-5-2-7-4z" class="H"></path><g class="D"><path d="M511 253v16l-1 14c-1 13-4 26-6 39-2 0-4 0-6 1v1-2c1-3 3-9 3-12-1 1-2 5-3 7h0c-1-1-1-2 0-3l-1-1c0-1 0 0-1-1-1 1-1 1-1 2l-3 3c-1-2 2-5 3-8v-3c1-3 3-6 4-10v-1c1-1 1-2 2-3l2-7 1 5v3c2-7 4-14 5-21v-15c2 3 1 8 1 12v-5-5c0-2 0-4 1-6z"></path><path d="M492 226c5 10 10 18 12 30 1 5 1 10 2 15v1c-1 1-1 1-1 2v2c0 1-1 3-1 4v2l-1 2h-1c-2-4-1-7-2-11v-2c0-2-1-3-1-5h0-1l-1-4c-1-1-2-3-3-4-2 1-2 2-2 3v1c-1 1-1 3 0 4h0l-1 1c-1 0-3 1-4 1h-4v-5c3-11 13-20 10-33-1-1-1-2-1-3v-1z"></path></g><path d="M423 216h0v-1c2-2 4-2 6-3 4-1 8-3 13-3h1c2-1 4 0 6-1h4v1h0l1-1c1 1 2 1 2 2 2 2 4 4 7 4h2c3 0 6-1 9 0h1c2 1 4 2 6 4v-1h3l3 4h2c1 1 3 3 3 5h0v1c0 1 0 2 1 3 3 13-7 22-10 33-1 4-1 8-1 12-3-4-4-7-4-12h-1v3 1 1l1 1-6 2-4 1v-2c1-2 1-5 0-7v-1c-1-11-3-19-10-28-9-11-20-16-35-18z" class="R"></path><path d="M453 209l1-1c1 1 2 1 2 2 2 2 4 4 7 4h2c3 0 6-1 9 0h1c2 1 4 2 6 4-5-2-9-2-13-1-3 0-7 1-10 1-2-1-3-2-4-4-1-1-1-3-1-5z" class="U"></path><path d="M453 209l1-1c1 1 2 1 2 2 2 2 4 4 7 4h2c-1 1-1 2-2 2-3 0-5 0-7-1-1-1-1-1-2-1-1-1-1-3-1-5zm34 12h2c1 1 3 3 3 5h0v1c0 1 0 2 1 3 3 13-7 22-10 33-1 4-1 8-1 12-3-4-4-7-4-12 1-10 10-17 12-27 2-6 0-10-3-15z" class="E"></path><defs><linearGradient id="BY" x1="625.408" y1="212.502" x2="577.822" y2="316.142" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#494949"></stop></linearGradient></defs><path fill="url(#BY)" d="M565 197c5-2 11-2 16-3 1 1 3 1 4 2h3c5 1 10 3 14 5 15 7 28 24 34 40 3 6 4 13 5 20 0 1 1 4 0 5l-1-3h-1v-1c-2-3-3-6-5-9h-1l-2-2-1 2 2 4 1 1h0c1 1 1 1 1 2h0l1 2v1c2 4 5 10 6 15v10c0 1-1 2-1 2l-18 30-7 13c1 1 1 1 1 2l1 1v-2l2 2s1 0 2 1l1 1v4c-1 1-2 3-2 4 0 2 1 6 0 7s-1 2-2 3l-1 5-1 1h0v-2-1h-2s-1-1-2-1c-1-2-2-6-2-8-1 0-2-3-2-4l-1 2-2 7-5 11c-1 1-2 3-2 5-3 2-8 6-9 9h0-1l-3 1c-8 2-17 0-24-5-5-2-10-8-12-13h0c0-2 1-4 2-6-1-1-3-1-5-3-4-2-4-6-5-10 0-2-1-3-1-5v-2l-1-1-1-2v-1l-4-5c-1-2-2-4-4-5v-1 4h0c0 1 0 2-1 3l1 1v1c-1 0-1-1-1-2-1-1-1-2-1-2-1-2-2-4-2-6h0l-1-1c-1 1-3 2-3 4v3 1c-4-16-11-32-11-48l1-22c1 1 1 3 1 4l1 1v-7c1 1 1 2 1 3l1 1c2-7 4-15 7-22h0c2-3 4-7 6-10 5-9 12-19 22-24l4-3c1 0 3-1 3-1 3-1 5-2 7-3z"></path><path d="M635 263l-6-9h-1c-4 3-8 4-12 3-3 0-5-2-7-3h-2l-6 3c2-2 4-3 6-5-1-4-3-8-1-13 2-3 4-6 8-7s7-1 11 1c3 2 6 5 7 9 1 3 0 5 0 8 3 3 7 8 8 13h-1v-1c-2-3-3-6-5-9h-1l-2-2-1 2 2 4 1 1h0c1 1 1 1 1 2h0l1 2v1z" class="D"></path><path d="M616 235c2 0 4 0 6 1s4 3 6 6c1 2 1 4 0 7s-4 4-6 5c-2 0-4 0-6-1-2 0-5-2-6-4s-1-6 0-8c1-3 3-5 6-6z" class="Y"></path><defs><linearGradient id="BZ" x1="576.179" y1="352.272" x2="567.183" y2="281.051" xlink:href="#B"><stop offset="0" stop-color="#8b8a8a"></stop><stop offset="1" stop-color="#c5c4c4"></stop></linearGradient></defs><path fill="url(#BZ)" d="M529 285l4 4 1 1h0 2c1 1 4 1 6 1 1-1 2 0 3 0 3 0 6 1 9 1h6c3 1 7 0 10-1 9 7 19 10 32 10 3 0 7-1 10 0-10 5-19 5-30 5-7 0-14 0-22-1-1 0-6-1-6 0 4 5 10 5 16 5 9 0 19-3 28-2v1c-1 2-2 4-4 6 12 3 15 8 21 18 1 1 1 1 1 2l1 1v-2l2 2s1 0 2 1l1 1v4c-1 1-2 3-2 4 0 2 1 6 0 7s-1 2-2 3l-1 5-1 1h0v-2-1h-2s-1-1-2-1c-1-2-2-6-2-8-1 0-2-3-2-4-2-4-5-8-8-12-1-1-2-3-3-4-2-1-4-3-5-4l-1-1s-1-1-2-1l-1-1c-1-1-2-1-3-2h-1c-1-1-2-1-3-2l-4-1h-2l-1-1h-2l-3-1h-2-7c1 1 1 2 2 2l1 1h0c-1 0-1 0-2 1l-3-2-3-3-4-2-3-3-4-2-4-4-2-1h0l-2-1-3-4-2-3-1-1v1h0l-1 1c0-1 0-2-1-2v-2c-1-1-1-1-1-3 0-1 1-2 2-4z"></path><path d="M594 315l-4-1h0c1 0 2 0 2-1 3-1 3-4 6-4-1 2-2 4-4 6z" class="C"></path><path d="M610 350h0c2 1 2 5 3 6 1-4-1-9-2-13-2-5-3-9-5-14h1 0c2 3 3 5 4 8h1l2 10c0 2 0 4 1 6 1 1 1 2 2 4h0c-1 1-1 2 0 4l-1 1h0v-2-1h-2s-1-1-2-1c-1-2-2-6-2-8z" class="B"></path><path d="M612 337c0-2 0-2-1-3l1-1 1 2h2l1 2v-1-1l1 1v-2l2 2s1 0 2 1l1 1v4c-1 1-2 3-2 4 0 2 1 6 0 7s-1 2-2 3l-1 5c-1-2-1-3 0-4h0c-1-2-1-3-2-4-1-2-1-4-1-6l-2-10z" class="C"></path><path d="M620 346c0 1-1 1-1 1l-1-5c0-2 1-4 3-5l1 1v4c-1 1-2 3-2 4z" class="T"></path><defs><linearGradient id="Ba" x1="539.99" y1="321.213" x2="554.844" y2="241.107" xlink:href="#B"><stop offset="0" stop-color="#a9a8a8"></stop><stop offset="1" stop-color="#e9e9e8"></stop></linearGradient></defs><path fill="url(#Ba)" d="M531 234c2-3 3-6 5-9l1 1c-2 4-3 9-1 14 1 2 3 5 4 8 3 3 5 7 7 11l1-2c1 0 2 0 3-1 2 0 3-1 5-1 1 2 1 3 2 4 0 14 2 22 12 32-3 1-7 2-10 1h-6c-3 0-6-1-9-1-1 0-2-1-3 0-2 0-5 0-6-1h-2 0l-1-1-4-4c-1 2-2 3-2 4 0 2 0 2 1 3v2c1 0 1 1 1 2l1-1h0v-1l1 1 2 3 3 4 2 1h0l2 1 4 4 4 2 3 3 4 2 3 3 3 2c1 0 2 1 3 1 2 2 5 3 7 6l1 2-12-4c-4-2-8-3-12-6-1-1-5-5-7-5l2 2c-2 2-2 1-4 2-6-5-8-10-12-15-1-1-1-1-1-2l-1-1v-2c-1-4-2-7-3-11-2-10-3-24 0-34v-1c2-5 4-9 6-14 1-1 2-3 3-4z"></path><path d="M527 280c0-1 1-2 1-2l-1-1c1-1 1-2 1-2h1c0 3 0 6 1 9 1 2 2 3 3 5l-4-4c-1-1-1-3-2-5z" class="L"></path><path d="M527 280c-2-5-1-13 1-18 1-2 1-3 3-3 1 0 2 0 2 2 2 4-3 10-4 14h0-1s0 1-1 2l1 1s-1 1-1 2z" class="M"></path><path d="M531 234c2-3 3-6 5-9l1 1c-2 4-3 9-1 14 1 2 3 5 4 8 3 3 5 7 7 11 1 7-1 12-4 18-1-5 0-9-2-14-3-10-10-18-10-29z" class="E"></path><path d="M565 197c5-2 11-2 16-3 1 1 3 1 4 2h3c5 1 10 3 14 5h0c-2 0-3-1-5-1 0-1-1-1-2-1 1 1 3 2 4 2l2 2 1 2 15 13c-2 0-5-1-6-1-14-2-25 1-35 9-8 7-14 15-17 25 0 3-1 5-1 8-1-1-1-2-2-4-2 0-3 1-5 1-1 1-2 1-3 1l-1 2c-2-4-4-8-7-11-1-3-3-6-4-8-2-5-1-10 1-14l-1-1c-2 3-3 6-5 9-1 1-2 3-3 4-2 5-4 9-6 14v1c-3 10-2 24 0 34 1 4 2 7 3 11-2-3-3-6-4-9-1-4-2-8-2-13v-11-1c-1 0-1-3-1-4 1-2 2-6 3-8 0-2-1-4 0-5 1-3 2-5 2-9h0c2-3 4-7 6-10 5-9 12-19 22-24l4-3c1 0 3-1 3-1 3-1 5-2 7-3z" class="R"></path><path d="M550 214c2-1 5 0 8 0 2 0 4 1 6 0 3 0 4-4 8-5 0 1 1 1 0 3 0 1-1 3-3 4-2 2-5 2-7 1-7-1-11-2-17 2-3 2-6 4-8 7l-1-1c2-3 6-7 9-9 1-1 3-1 4-2h1z" class="M"></path><path d="M565 197c5-2 11-2 16-3 1 1 3 1 4 2h3c5 1 10 3 14 5h0c-2 0-3-1-5-1 0-1-1-1-2-1 1 1 3 2 4 2l2 2 1 2 15 13c-2 0-5-1-6-1v-1h1c-5-4-12-7-18-10-11-4-25-4-36 1-3 1-7 4-8 7h-1c-1 1-3 1-4 2-3 2-7 6-9 9s-3 6-5 9c-1 1-2 3-3 4-2 5-4 9-6 14v1c-3 10-2 24 0 34 1 4 2 7 3 11-2-3-3-6-4-9-1-4-2-8-2-13v-11-1c-1 0-1-3-1-4 1-2 2-6 3-8 0-2-1-4 0-5 1-3 2-5 2-9h0c2-3 4-7 6-10 5-9 12-19 22-24l4-3c1 0 3-1 3-1 3-1 5-2 7-3z" class="O"></path><path d="M555 201c1 0 3-1 3-1 3-1 5-2 7-3 2 1 3 1 4 1 3 0 10-1 12 0 1 0 1 0 2 1 2 0 2 0 4 1-6 0-11-1-17-1-3 1-7 2-10 3-2 1-6 3-9 2l4-3z" class="X"></path><path d="M565 197c5-2 11-2 16-3 1 1 3 1 4 2h3c5 1 10 3 14 5h0c-2 0-3-1-5-1 0-1-1-1-2-1 1 1 3 2 4 2l2 2 1 2-3-1-12-4c-2-1-2-1-4-1-1-1-1-1-2-1-2-1-9 0-12 0-1 0-2 0-4-1z" class="L"></path><path d="M585 196h3c5 1 10 3 14 5h0c-2 0-3-1-5-1 0-1-1-1-2-1 1 1 3 2 4 2l2 2 1 2-3-1c-2-2-4-4-7-5s-5-2-8-2h-9 0c1-1 2-1 4-1h6z" class="Y"></path><defs><linearGradient id="Bb" x1="568.921" y1="313.453" x2="578.724" y2="381.191" xlink:href="#B"><stop offset="0" stop-color="#1f1e1e"></stop><stop offset="1" stop-color="#383838"></stop></linearGradient></defs><path fill="url(#Bb)" d="M523 238c0 4-1 6-2 9-1 1 0 3 0 5-1 2-2 6-3 8 0 1 0 4 1 4v1 11c0 5 1 9 2 13 1 3 2 6 4 9v2l1 1c0 1 0 1 1 2 4 5 6 10 12 15 2-1 2 0 4-2l-2-2c2 0 6 4 7 5 4 3 8 4 12 6l12 4-1-2c-2-3-5-4-7-6-1 0-2-1-3-1 1-1 1-1 2-1h0l-1-1c-1 0-1-1-2-2h7 2l3 1h2l1 1h2l4 1c1 1 2 1 3 2h1c1 1 2 1 3 2l1 1c1 0 2 1 2 1l1 1c1 1 3 3 5 4 1 1 2 3 3 4 3 4 6 8 8 12l-1 2-2 7-5 11c-1 1-2 3-2 5-3 2-8 6-9 9h0-1l-3 1c-8 2-17 0-24-5-5-2-10-8-12-13h0c0-2 1-4 2-6-1-1-3-1-5-3-4-2-4-6-5-10 0-2-1-3-1-5v-2l-1-1-1-2v-1l-4-5c-1-2-2-4-4-5v-1 4h0c0 1 0 2-1 3l1 1v1c-1 0-1-1-1-2-1-1-1-2-1-2-1-2-2-4-2-6h0l-1-1c-1 1-3 2-3 4v3 1c-4-16-11-32-11-48l1-22c1 1 1 3 1 4l1 1v-7c1 1 1 2 1 3l1 1c2-7 4-15 7-22z"></path><path d="M549 327v-1-1c1 1 2 1 3 1l1 1 8 4c-1 1-1 0-2 0v1l-10-5z" class="W"></path><path d="M552 341l-1-2h0c3 1 5 2 8 1 6 1 14 2 20 0h1v1c-6 2-15 5-21 3h-2c-1 0-3-2-5-3z" class="C"></path><path d="M527 303c4 5 6 10 12 15 2-1 2 0 4-2l-2-2c2 0 6 4 7 5 4 3 8 4 12 6-2-1-4-1-5-1l-2-1-1 1h1v3l-1-1c-1 0-2 0-3-1v1 1h0c-1 0-1-1-2-2-1-2-3-2-5-4l-5-3c2 2 5 4 6 6l-1 1-1-1-2-2h0c-2-4-4-7-8-9l-4-10z" class="L"></path><path d="M543 316c2 1 3 3 5 4v1c-1 1-1 1-2 1-3-1-5-2-7-4 2-1 2 0 4-2zm18 4c1-1 1-1 2-1h0l-1-1c-1 0-1-1-2-2h7 2l3 1h2l1 1h2l4 1c1 1 2 1 3 2h1c1 1 2 1 3 2l1 1c1 0 2 1 2 1l1 1c1 1 3 3 5 4 0 4-1 8-2 12-2-3-4-5-7-8l-6-3c-6-4-11-8-18-10-1 0-2-1-3-1z" class="C"></path><path d="M535 327c1 2 2 3 3 4h0c2 2 4 5 7 6l1 1c2 1 4 3 6 3 2 1 4 3 5 3 4 2 10 4 13 7 4 2 6 8 8 12 0 3 0 6-1 8-2 3-5 4-8 4-3 1-5 1-7-1-1-1-1-1-1-2v1h-2c-3-1-7-6-8-9l-1-2-1 1h0c0-2 1-4 2-6-1-1-3-1-5-3-4-2-4-6-5-10 0-2-1-3-1-5v-2l-1-1-1-2v-1l-4-5 1-1z" class="D"></path><path d="M551 357c-1-1-3-1-5-3-4-2-4-6-5-10 0-2-1-3-1-5l1 1c7 8 20 12 24 23-1 3-1 6-4 9v1h-2c-3-1-7-6-8-9l-1-2-1 1h0c0-2 1-4 2-6z" class="X"></path><path d="M551 357c4 2 5 3 6 7l4 9h-2c-3-1-7-6-8-9l-1-2-1 1h0c0-2 1-4 2-6z" class="K"></path><defs><linearGradient id="Bc" x1="528.398" y1="296.062" x2="507.738" y2="292.627" xlink:href="#B"><stop offset="0" stop-color="#c7c7c6"></stop><stop offset="1" stop-color="#f7f6f7"></stop></linearGradient></defs><path fill="url(#Bc)" d="M523 238c0 4-1 6-2 9-1 1 0 3 0 5-1 2-2 6-3 8 0 1 0 4 1 4v1 11c0 5 1 9 2 13 1 3 2 6 4 9v2l1 1c0 1 0 1 1 2l4 10c4 2 6 5 8 9h0l-1 1 4 4v1l5 4c4 3 7 6 12 8-3 1-5 0-8-1h0l1 2c-2 0-4-2-6-3l-1-1c-3-1-5-4-7-6h0c-1-1-2-2-3-4l-1 1c-1-2-2-4-4-5v-1 4h0c0 1 0 2-1 3l1 1v1c-1 0-1-1-1-2-1-1-1-2-1-2-1-2-2-4-2-6h0l-1-1c-1 1-3 2-3 4v3 1c-4-16-11-32-11-48l1-22c1 1 1 3 1 4l1 1v-7c1 1 1 2 1 3l1 1c2-7 4-15 7-22z"></path><path d="M523 238c0 4-1 6-2 9-1 1 0 3 0 5-1 2-2 6-3 8 0 1 0 4 1 4-5 12 0 25 3 37 2 8 3 18 8 25 0 1 0 2-1 3-8-22-17-45-13-69 2-7 4-15 7-22z" class="K"></path><path d="M519 264v1 11c0 5 1 9 2 13 1 3 2 6 4 9v2l1 1c0 1 0 1 1 2l4 10c4 2 6 5 8 9h0l-1 1 4 4v1l-4-4h-1c-3-3-5-5-7-8-2-4-4-8-6-13 0-1-1-1-2-2-3-12-8-25-3-37z" class="H"></path><path d="M531 313c4 2 6 5 8 9h0l-1 1 4 4v1l-4-4c-3-4-5-7-7-11z" class="E"></path><path d="M522 301c1 1 2 1 2 2 2 5 4 9 6 13 2 3 4 5 7 8h1l4 4 5 4c4 3 7 6 12 8-3 1-5 0-8-1h0l1 2c-2 0-4-2-6-3l-1-1c-3-1-5-4-7-6h0c-1-1-2-2-3-4l-1 1c-1-2-2-4-4-5v-1 4h0c-5-7-6-17-8-25z" class="C"></path><path d="M522 301c1 1 2 1 2 2 0 6 4 12 7 17 2 2 3 5 4 7l-1 1c-1-2-2-4-4-5v-1 4h0c-5-7-6-17-8-25z" class="J"></path><path d="M538 331c1 0 2 1 3 2 2 2 4 3 7 5h1 0c-1-1-2-2-3-2-3-3-5-5-7-9 2 2 6 5 8 5 4 3 7 6 12 8-3 1-5 0-8-1h0l1 2c-2 0-4-2-6-3l-1-1c-3-1-5-4-7-6z" class="G"></path><path d="M511 280c0 16 7 32 11 48v-1-3c0-2 2-3 3-4l1 1h0c0 2 1 4 2 6 0 0 0 1 1 2 0 1 0 2 1 2v-1l-1-1c1-1 1-2 1-3h0v-4 1c2 1 3 3 4 5l4 5v1l1 2 1 1v2c0 2 1 3 1 5 1 4 1 8 5 10 2 2 4 2 5 3-1 2-2 4-2 6h0c2 5 7 11 12 13 7 5 16 7 24 5l3-1h1c0 1 0 2-1 3-2 7-3 14-4 21-4 1-15 19-17 23l-2 4-2 3c-6 12-14 27-16 42 0 2-1 5-1 8-2 10-2 20 0 31 0 3 2 9 1 12l-3 9-7 20-22 58-23-63-8-20c-1-3-3-7-3-9 0-5 2-10 2-16 1-14-1-29-5-43 0-1-1-4-2-5-3-9-7-17-11-26l-9-16c0-1-2-4-2-5-1-2-2-3-3-4-2-4-5-8-6-12v-1c-2-2-4-6-4-9 1-1-1-1 0-3h4c-1-1-3-1-5-1-1-3-4-7-6-10-3-4-5-9-7-13-2-5-5-9-6-14 2-4 4-9 8-12l1 5 1 6c3-4 6-7 10-10s8-7 12-9 9-4 13-5c-2 2-8 3-10 6-1 1-1 2-1 3 2 0 4-1 5-2 6-2 11-3 16-6 3-1 5-3 8-5 4-4 8-8 11-12 1-2 2-5 4-7-1 4-3 7-4 10v3c-1 3-4 6-3 8l3-3c0-1 0-1 1-2 1 1 1 0 1 1l1 1c-1 1-1 2 0 3h0c1-2 2-6 3-7 0 3-2 9-3 12v2-1c2-1 4-1 6-1 2-13 5-26 6-39l1-3z" class="W"></path><path d="M445 395c2 0 3 1 4 2 3 3 5 9 10 8 2 0 3-2 5-3h0c1 1 1 2 1 4-1 2-3 3-6 4-2 0-3 1-5 1-1-2-2-3-3-4-2-4-5-8-6-12zm138-3h1l-1 1c-1 2-3 3-4 5-1 3-1 6-3 8-1 2-3 3-6 4-4 0-6-2-9-4l-1-2v-1c0-1 1-2 3-2 1 1 1 2 3 3 6 1 8-5 11-9l6-3z" class="H"></path><path d="M489 417c2 0 2 2 3 4l2 4 1 1c2 0 3 0 4-1 1 0 1 1 2 2h2v-1-1h1 0c2 10 2 22-2 31-1-6-1-13-3-20-3-7-7-13-10-19z" class="I"></path><path d="M511 280c0 16 7 32 11 48 4 19 6 37 5 57-1-3 0-11 0-15v-2c0-1-1-2-1-3v-5c1-1 0-1 0-1v-2-2h0c-1-2 0-3-1-5v-2c0-2-1-1 0-3-1-1-1-1-1-2 0-2-1-4-2-5l-1-2h0c0-1-1-2-1-2 0-1 0-2-1-3v-1c-2-2-4-3-6-5-2 0-5-1-7 1-1 1-3 2-3 4v4l-1 5v2c-1 1-1 3-1 5v2c0 2-1 5-1 8-1-1-1-3-1-4 1-10 3-20 5-30 2-13 5-26 6-39l1-3z" class="Q"></path><defs><linearGradient id="Bd" x1="483.422" y1="371.389" x2="510.933" y2="375.684" xlink:href="#B"><stop offset="0" stop-color="#969594"></stop><stop offset="1" stop-color="#bababa"></stop></linearGradient></defs><path fill="url(#Bd)" d="M498 317c1-2 2-6 3-7 0 3-2 9-3 12v2-1c2-1 4-1 6-1-2 10-4 20-5 30-1 7-1 14-1 21 0 12 0 23 2 35 1 5 3 11 4 17h-1v1 1h-2c-1-1-1-2-2-2-1 1-2 1-4 1l-1-1-2-4c-1-2-1-4-3-4-2-1-9-15-10-17l1-1c1 1 1 3 2 5v-4c0-1-1-2-1-3h0v-10l-1-1v-4l1-4c0-1 1-2 1-2v-2l3-6v-2l3-6c0-1 0-2 1-3v-3c1-1 1-2 1-3 1-1 0-3 0-4 1-1 1-2 1-3v-1c0-1 0-2 1-3v-2-1c1-1 1-3 1-4l2-5 3-11z"></path><defs><linearGradient id="Be" x1="543.535" y1="383.866" x2="515.363" y2="386.548" xlink:href="#B"><stop offset="0" stop-color="#8c8b8b"></stop><stop offset="1" stop-color="#bebdbd"></stop></linearGradient></defs><path fill="url(#Be)" d="M522 328v-1-3c0-2 2-3 3-4l1 1h0c0 2 1 4 2 6 0 0 0 1 1 2 0 1 0 2 1 2v-1l-1-1c1-1 1-2 1-3h0v-4 1c2 1 3 3 4 5l4 5v1l1 2 1 1v2c0 2 1 3 1 5 1 4 1 8 5 10 2 2 4 2 5 3-1 2-2 4-2 6h0c2 5 7 11 12 13 7 5 16 7 24 5l3-1h1c0 1 0 2-1 3-1 0-1 0-2 1-2 2-8 2-12 1-8-1-16-5-22-10-2-2-4-4-5-6 3 10 4 21 0 30v1l-10 16c-5 9-11 17-12 27-1 5-1 10 0 15-3-5-5-11-5-16 0-7 1-13 2-20 3-11 6-24 5-36v-1c1-20-1-38-5-57z"></path><defs><linearGradient id="Bf" x1="531.608" y1="366.011" x2="570.862" y2="348.351" xlink:href="#B"><stop offset="0" stop-color="#898989"></stop><stop offset="1" stop-color="#cdcbcc"></stop></linearGradient></defs><path fill="url(#Bf)" d="M530 326h0v-4 1c2 1 3 3 4 5l4 5v1l1 2 1 1v2c0 2 1 3 1 5 1 4 1 8 5 10 2 2 4 2 5 3-1 2-2 4-2 6h0c2 5 7 11 12 13 7 5 16 7 24 5l3-1h1c0 1 0 2-1 3-1 0-1 0-2 1-2 2-8 2-12 1-8-1-16-5-22-10-2-2-4-4-5-6 3 10 4 21 0 30v1h-1v-2-2l-1-1v-1l1-1v-7c-1-2-1-3-1-4v-1c0-1 0-2-1-3v-2-1s-1-1-1-2l-1-4c-2-4-4-8-5-12l-1-1v1l-1-1h1v-1h-1 0l1-1-1-1h0-1l1-1h0v-1h-1 0l1-1v-1c0-1-1-3-1-4-1-2 0-5-1-6 0-1 0-2-1-3v-3h-1v-3h-1l-1-1c1-1 1-2 1-3z"></path><defs><linearGradient id="Bg" x1="456.399" y1="308.172" x2="457.762" y2="380.391" xlink:href="#B"><stop offset="0" stop-color="#1e1e1e"></stop><stop offset="1" stop-color="#403f40"></stop></linearGradient></defs><path fill="url(#Bg)" d="M484 315c4-4 8-8 11-12 1-2 2-5 4-7-1 4-3 7-4 10v3c-1 3-4 6-3 8l3-3c0-1 0-1 1-2 1 1 1 0 1 1l1 1c-1 1-1 2 0 3h0l-3 11-2 5c0 1 0 3-1 4v1 2c-1 1-1 2-1 3v1c0 1 0 2-1 3 0 1 1 3 0 4 0 1 0 2-1 3v3c-1 1-1 2-1 3l-3 6v2l-3 6v2s-1 1-1 2l-1 4v4l1 1v10h0c0 1 1 2 1 3v4c-1-2-1-4-2-5l-1 1c-4-9-3-20-1-29l-6 6c-8 7-20 9-30 8h-1c1-1-1-1 0-3h4c-1-1-3-1-5-1-1-3-4-7-6-10-3-4-5-9-7-13-2-5-5-9-6-14 2-4 4-9 8-12l1 5 1 6c3-4 6-7 10-10s8-7 12-9 9-4 13-5c-2 2-8 3-10 6-1 1-1 2-1 3 2 0 4-1 5-2 6-2 11-3 16-6 3-1 5-3 8-5z"></path><path d="M455 343c-5 0-7-1-11-3 4 0 8 1 12 1-1 1-1 1-1 2z" class="N"></path><path d="M479 326l3-2c0 1 0 2-1 3v1h0c-6 5-13 7-20 8h0c6-2 13-6 18-10z" class="U"></path><path d="M492 317l3-3-1 1-3 6c1 0 1 0 2-1-1 3-4 6-5 9-3 0-6 5-9 7-1 1-3 1-4 1l1-2h1c3-2 11-10 11-13 1-1 3-4 4-5z" class="G"></path><path d="M484 315c4-4 8-8 11-12 1-2 2-5 4-7-1 4-3 7-4 10v3c-1 3-4 6-3 8-1 1-3 4-4 5l-7 6v-1c1-1 1-2 1-3l-3 2-1-1c-2 1-3 3-5 4 2-3 5-6 8-9 2-1 3-2 4-4 1 0 1-1 2-2l1 1 1-1v-1l2-2c0-2 1-2 1-3-1 2-5 6-7 7h-1z" class="E"></path><path d="M495 306v3c-1 3-4 6-3 8-1 1-3 4-4 5l-7 6v-1c1-1 1-2 1-3l1-1c4-6 8-11 12-17zm-20 31c1 0 3 0 4-1 3-2 6-7 9-7-1 2-3 3-4 5s-3 3-4 4c0 2-1 2-3 3l-1 1c-2 2-5 3-7 4h-1c-5 0-9-1-13-3 0-1 0-1 1-2 6 0 14-1 19-4z" class="B"></path><path d="M493 320c0-1 0-1 1-2v1 1 1c-1 1-4 5-4 6h2v1l-1 1v-1l-2 2c0 2-1 3-1 4-1 1-3 6-3 7-3 2-5 5-8 7-5 4-17 10-17 16 1 3 5 8 5 10l-2 1c-3 1-5 1-9 0-2 0-4-2-5-5-2-3 0-8 1-11 2-4 4-7 8-9 3-2 6-3 10-4h1c2-1 5-2 7-4l1-1c2-1 3-1 3-3 1-1 3-2 4-4s3-3 4-5c1-3 4-6 5-9z" class="D"></path><path d="M458 350l1 1c2-1 4-3 6-3l1 1-4 2-1 1c-1 1-1 1-2 1-1 1-1 2-2 2-1 1-2 2-3 2l-1-1-3 3c2-4 4-7 8-9z" class="B"></path><defs><linearGradient id="Bh" x1="499.906" y1="367.113" x2="458.982" y2="346.974" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#c9c8c8"></stop></linearGradient></defs><path fill="url(#Bh)" d="M495 314c0-1 0-1 1-2 1 1 1 0 1 1l1 1c-1 1-1 2 0 3h0l-3 11-2 5c0 1 0 3-1 4v1 2c-1 1-1 2-1 3v1c0 1 0 2-1 3 0 1 1 3 0 4 0 1 0 2-1 3v3c-1 1-1 2-1 3l-3 6v2l-3 6v2s-1 1-1 2l-1 4v4l1 1v10h0c0 1 1 2 1 3v4c-1-2-1-4-2-5l-1 1c-4-9-3-20-1-29l-6 6c-8 7-20 9-30 8h-1c1-1-1-1 0-3h4c7 0 14-2 19-5l-1-2 2-1c0-2-4-7-5-10 0-6 12-12 17-16 3-2 5-5 8-7 0-1 2-6 3-7 0-1 1-2 1-4l2-2v1l1-1v-1h-2c0-1 3-5 4-6v-1-1-1c-1 1-1 1-1 2-1 1-1 1-2 1l3-6 1-1z"></path><path d="M495 314c0-1 0-1 1-2 1 1 1 0 1 1l1 1c-1 1-1 2 0 3h0l-3 11h-1l1-2v-3h1v-1-1c0-1 0-1-1-1l-1 1v-1-1-1c-1 1-1 1-1 2-1 1-1 1-2 1l3-6 1-1z" class="J"></path><defs><linearGradient id="Bi" x1="482.29" y1="345.665" x2="461.82" y2="369.981" xlink:href="#B"><stop offset="0" stop-color="#181818"></stop><stop offset="1" stop-color="#323233"></stop></linearGradient></defs><path fill="url(#Bi)" d="M463 375l2-1c0-2-4-7-5-10 0-6 12-12 17-16 3-2 5-5 8-7-1 4-5 11-8 15-2 2-5 3-6 5l5-1h1c-1 2-1 3-2 5h0c-3 4-7 9-11 12l-1-2z"></path><path d="M471 361l5-1h1c-1 2-1 3-2 5h0c-2 1-6 7-7 8 0-4 0-8 3-12z" class="K"></path></svg>