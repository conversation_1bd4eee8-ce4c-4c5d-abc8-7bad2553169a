<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="102 54 850 904"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#858485}.C{fill:#5d5c5d}.D{fill:#ebeae8}.E{fill:#4f4f4f}.F{fill:#343434}.G{fill:#a7a6a6}.H{fill:#666}.I{fill:#242323}.J{fill:#0f0f0e}.K{fill:#484749}.L{fill:#d7d6d5}</style><path d="M481 759l2-2 1 10v4c-1 1 0 8-1 8 0-7-1-13-2-20z" class="H"></path><path d="M322 120h25c-2 0-8 0-9 1 0 1-1 2-1 3-1-1-3-1-4-1h-7v-2l-4-1z" class="C"></path><path d="M464 723c5 4 10 10 13 16l-1 4c-2-3-3-7-5-10s-7-7-7-10z" class="B"></path><defs><linearGradient id="A" x1="482.23" y1="749.139" x2="476.162" y2="748.878" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#7f7e80"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M477 739l7 17-1 1-2 2c-1-6-3-11-5-16l1-4z"></path><path d="M456 733c3 0 3-1 5 1 2 1 3 4 4 6 4 8 5 16 5 25l-2-7c-3-5-6-8-12-11-2-1-5-2-7-4v-2c1-4 4-7 7-8zM183 121c3-2 9-1 12-1h26c5 0 10 1 15 0 2-1 6 0 8 0 1-1 1 0 2 0h76l4 1v2h7c1 0 3 0 4 1 0-1 1-2 1-3 1-1 7-1 9-1h21 8 68l-5 50c-6-4-9-7-17-5-12 2-23 9-30 19-5 6-8 13-10 20l19 42c0 1 1 2 1 3 2 4 6 10 6 14 2 3 4 7 5 10l5 11c0 2 2 4 2 5l124 281 14 31c2 4 4 8 7 12v-1l1 1c0 1 1 2 1 3l1 1v1h1c1 1 0 1 1 2 2 1 3 4 4 6l3 3c1 2 3 3 5 5 1 1 2 1 3 2l1 1 1 1h0 1c2 1 3 2 5 2l1 1h1 1c0-2 0-2 1-3h0c1-2 2-4 2-6l4-10 23-64 41-111c15-41 29-80 37-123 3-18 5-37 5-56v-18c0-10-1-18 1-27l-5 8c0-3-1-6-1-9 0-2 1-3 1-5-1 2-2 5-4 7-11-19-20-39-43-48-11-4-24-4-35 0l-5-42-2-14h31 40c9 0 18-1 27 1h3 1 0c4 1 10-1 14 1h41 10c2 0 4 0 5-1h0c8 0 17 1 25-1h11 24 89c-29 16-52 39-72 65-10 12-19 25-26 38 0-4 0-7-1-11 0 5-1 10-3 15-3 8-8 15-12 23l-16 35-25 59-13 37c-2-5-2-10-2-16v-4c-1 5-2 9-2 14 1 4 2 7 3 11l-5-9c1 6 4 12 1 18h-1c-1-1-2-4-3-6 1 7 0 13-2 20-4 14-10 27-15 41l-28 75-44 119-34 90-11 30-4-7c1 4 2 8 1 12 0 5-2 9-4 14l-7 18-21 57c-6 16-14 33-16 49-1 6-1 12 3 17 3 3 6 6 10 8h-83l-66-152 7 1c-3-3-7-5-10-8-2-4-4-8-6-13l-7-16-31-70h0c13 10 25 19 35 32 4 5 7 10 10 16 1 3 7 8 7 11l-12-9 19 34c-4-4-8-7-13-10 3 4 6 9 8 13l8 16c2 3 3 8 6 11 3 1 5 5 6 7 1 1 1 1 1 2l-1 1c-3-2-5-4-7-6h0c1 1 2 3 3 4v1c1 1 3 2 4 2h1c3 1 5 5 7 8 0-2 0-4-1-6v-1c1-1 0-2 0-3s-1-2-1-3 0-2-1-3c0-2-1-3-1-4v-1c-2-5-6-8-10-11 3-3 5-5 9-6 1 0 2 0 4 1 4 3 5 10 6 15 0 2 1 5 0 8v3h1c0-1 0-1 1-2v-2-2c0-2 1-2 0-4h0c1-1 1-2 2-3v-4c1 0 0-7 1-8v-4l-1-10 1-1-7-17c-3-6-8-12-13-16-8-8-17-14-25-20l-29-19c-6-5-13-10-18-17-12-13-20-32-23-49-1 0-1 0-2-2-1-1-2-2-2-4-1-7 0-15 2-22l-3-2-3 17c-2-1-9-20-10-23l-30-70c8 9 15 18 25 25 3 2 7 4 11 5 14 6 28 9 43 10 10 1 19 1 28 3-3-2-7-4-10-5 14-3 26-8 37-18v-2c1-1 2-2 3-4l2-1s0-1 1-1c0-2-1 0 0-2h1l-2-1c-1 2-5 7-8 9h-1c-5 4-10 8-16 11-14 6-28 6-42 5-9-1-19-3-28-6-17-7-33-20-43-34-11-15-18-32-25-49l-23-53-37-85-27-61c-3-7-4-11-1-18l-6 6c-1-3-2-5-3-7 3-1 4-3 6-5-2 1-4 1-6 1s-2-1-3-2-1-1-1-2c3 1 5 1 8 0v-1c-4 0-8-2-10-5-4-4-7-9-9-14l-11-16c-15-22-32-42-53-59-7-6-15-11-23-16h59 15 8c1 1 1 1 2 1h1z"></path><path d="M365 139c1 1 2 2 3 2l-2 1-2-1c-1 0-2 1-3 0h-1l5-2z" class="E"></path><path d="M357 143l3-2h1c1 1 2 0 3 0l2 1-3 2c-2-1-3-1-4-1h-2z" class="K"></path><path d="M373 427c-2-1-3-2-4-4v-1l5 1c0 1 0 3-1 4z" class="C"></path><path d="M320 405h11c-3 1-6 2-10 3 0-1-1-2-1-3z" class="K"></path><path d="M460 698h12c3 1 5 1 8 2h-5c-4 0-11 1-15-2z" class="E"></path><path d="M717 485c2-4 4-8 7-10l-5 11h0c-1-1-1 0-1-1h-1z" class="F"></path><path d="M365 612l2-6 2 12c-1 0-1 0-2-2-1-1-2-2-2-4z" class="D"></path><path d="M781 192l1-13c1 6 3 16 2 22-1-3 0-7-1-10h-1l-1 1z" class="I"></path><path d="M467 505c2-2 3-7 5-7-1 3-1 7-3 11l-4 1 2-5z" class="B"></path><path d="M485 545l2 4c-5 2-10 3-16 2 5-1 9-2 13-5l1-1z" class="H"></path><path d="M342 547c-2-4-5-12-4-15 2 2 2 4 3 7 1 2 2 5 4 8h-3z" class="B"></path><path d="M565 885c2 3 3 4 3 7h0v5l1 2c-1 2-2 3-4 5 2-6 1-13 0-19z" class="F"></path><path d="M368 146l5 3c1 0 1 0 2 1l-6 1c-3 0-4-2-6-3l5-2zm116 610c2 6 3 13 3 19-1 1-1 0-1 2h0c-1 0-2-9-2-10l-1-10 1-1z" class="E"></path><path d="M366 442c1-2 2-5 3-7l1 1c1 3 0 7-1 10-1 1-2 3-3 5 0-3 1-7 0-9zm180 223c1 3-1 7-1 10s0 5 1 7l1 3c-3-4-5-7-5-12 1-1 1-2 1-2h1c0-2 1-4 2-6z" class="H"></path><path d="M466 440c0-3 1-6 2-8h0l1 2v10c1 2 1 4 1 6l-1 1v1h-1c0-2 1-7 0-10h-1l-1-2z" class="J"></path><path d="M277 188c0-4 2-8 3-12h0c1-1 1-2 2-3l-3 19h0-1v-2c1-1 1-1 1-2v-3h0c-1 2-2 2-2 4v1-2z" class="C"></path><path d="M498 715l-2-4c-2-2-4-3-6-6 7 3 11 7 14 14v1c-2-1-4-3-5-6l-1 1z" class="E"></path><path d="M471 488c0 1 1 3 1 5l1-1h0c0 1 0 5-1 6-2 0-3 5-5 7v-1c0-1 1-4 1-5l3-11z" class="F"></path><path d="M480 827h1c0-2 0-5 1-6 1-3-1 1 0-2l1-1-1 27h-1c-2-5-1-13-1-18z" class="E"></path><path d="M250 320c3 2 5 7 7 10v1l1-1 5 15-13-25z" class="C"></path><path d="M748 378c4 0 9 0 12 1 1 1 2 1 2 3-1 1-1 2-2 2h-5c0-2 0-2-1-4v-1l-6-1z" class="I"></path><path d="M403 297c0-9 3-19 6-27 0 3-1 7-1 10l-2 13c-1 0-1-1-2 0h0c0 2-1 3-1 4z" class="G"></path><path d="M259 225c1 1 0 3 0 5 0 4 1 8 0 13 0 1 0 2 1 4 0-1 0-2 1-3v-1c0-1 1-1 1-2l-3 14c0 1 0 1-1 1l-1-1 2-30z" class="L"></path><path d="M547 909c2 0 5 0 7-1h4v1c-2 2-8 4-11 4h0-3c1-2 1-3 3-4z" class="F"></path><path d="M372 472l1 2c-5 5-10 10-12 17l-1-1c0-1 1-3 1-4 2-2 3-5 4-8l7-6z" class="B"></path><path d="M372 472c4-4 10-9 16-9 1 0 1 0 2 1h0c-7 2-11 5-17 10l-1-2z" class="H"></path><path d="M618 134l-2-14h31c-2 1-7 0-9 1h-3-5-10-2c0 1-1 2 0 4h0 0l3 5-1 1-1-1h-1v4z" class="J"></path><path d="M470 626c1 4 1 7-1 11l-2 2c-1-1-4-1-5-1 1-1 3-3 4-5 1-1 1-2 1-3h1c1-1 2-2 2-4z" class="E"></path><path d="M385 430c6 5 11 9 20 11h0c-5 1-10 0-15-3-2-2-4-5-5-7v-1z" class="C"></path><path d="M365 552h0c-2-1-4-1-6-2-7-2-11-8-15-13 3 2 5 5 7 7 4 2 9 4 13 6 2 0 5 1 7 2l-4 1-2-1z" class="B"></path><path d="M471 488l2-22h0c1 2 1 5 1 7 0 7 0 13-1 19h0 0l-1 1c0-2-1-4-1-5z" class="I"></path><path d="M330 491h1l2 2c0-2-1-9 0-10 0 6 0 12 1 18 0 2 1 5 2 7-3-5-9-12-10-17h0c2 1 3 3 5 5 0-2 0-3-1-4v-1z" class="H"></path><path d="M368 497c2-1 4-3 6-5h4c-5 4-9 7-11 13v13-1c-3-6-2-13 1-20z" class="E"></path><path d="M785 226l7-13v-1c-1 9-3 19-7 26v-3l1-1c0-1-1-1-1-2 0-3 2-3 0-6z" class="F"></path><path d="M274 363c6 8 13 15 21 21l-6-2c-7-3-12-12-15-19z" class="D"></path><path d="M632 717h3 0v1c-3 5-5 10-6 16-1 2-1 5-2 7h-2c0-3 1-6 2-9 1-5 3-10 5-15z" class="C"></path><defs><linearGradient id="C" x1="340.428" y1="147.17" x2="358.572" y2="149.83" xlink:href="#B"><stop offset="0" stop-color="#141514"></stop><stop offset="1" stop-color="#403f41"></stop></linearGradient></defs><path fill="url(#C)" d="M363 144c-3 1-5 1-8 2-3 2-7 4-10 6s-6 3-8 5h0-1-1c1-2 3-3 5-4 5-4 11-8 17-10h2c1 0 2 0 4 1z"></path><path d="M468 452h1c0 2-1 3-1 4 0 3-1 6-1 9-2 7-3 13-6 20-1 2-2 4-4 6 0-1 0-2 1-3h0v-1c1-1 1-1 1-2l1-1v-1c1-1 1 0 1-2h0l1-1v-1h0l1-2c-2-1-5 9-7 10 0 1-1 1-1 1 2-5 5-9 7-15 3-5 4-11 6-17v-4zm-126 95h3c3 3 6 6 11 6 3 0 6 0 9-1l2 1c-4 2-11 3-16 2-4-1-7-4-9-8z" class="G"></path><path d="M507 860c0 9 0 18 2 27 2 5 4 9 5 14-1 0-1 0-1-1l-7-19c-1-3-2-6-2-8 2-4 2-9 3-13z" class="J"></path><defs><linearGradient id="D" x1="714.568" y1="483.525" x2="710.981" y2="494.326" xlink:href="#B"><stop offset="0" stop-color="#878787"></stop><stop offset="1" stop-color="#a8a6a7"></stop></linearGradient></defs><path fill="url(#D)" d="M706 497c6-9 12-19 21-26-1 1-2 2-3 4-3 2-5 6-7 10-3 5-5 11-8 16v-3l1-1v-1l1-1h0l-1-1-4 6c-1 1-1 1-2 1l2-4z"></path><path d="M465 510l4-1c-3 8-7 17-14 23v-2c1-1 2-2 3-4l2-1s0-1 1-1c0-2-1 0 0-2h1l-2-1c-1 2-5 7-8 9h-1c6-6 11-12 14-20z" class="G"></path><path d="M479 817l1-1h0v2c1 2 0 4 0 6v2 1c0 5-1 13 1 18h1c1 1 1 6 1 7 1 1 1 1 0 2h0c-1-3-2-6-4-9-1-2-2-4-2-7h1c1-7 1-14 1-21z" class="F"></path><defs><linearGradient id="E" x1="751.868" y1="380.649" x2="740.563" y2="382.405" xlink:href="#B"><stop offset="0" stop-color="#424243"></stop><stop offset="1" stop-color="#686868"></stop></linearGradient></defs><path fill="url(#E)" d="M748 378l6 1v1c1 2 1 2 1 4h-8c-1-1-6-1-7-1-1-1-2-2-3-2 3-2 8-2 11-3z"></path><path d="M352 235l3-5c1 6 1 12 0 18-1 2-2 5-1 6l-6 3h-1l2-1v-1l1-2c1-2 3-7 2-9v-2-2-5z" class="I"></path><defs><linearGradient id="F" x1="496.571" y1="763.544" x2="494.297" y2="750.713" xlink:href="#B"><stop offset="0" stop-color="#222323"></stop><stop offset="1" stop-color="#504f50"></stop></linearGradient></defs><path fill="url(#F)" d="M498 737v1c0 4-2 9-2 12 1 0 1 0 1-1l1-2 2-2 3-6s0-1 1-2l1 1c-3 5-5 10-7 15s-2 10-4 15l-1-9 1-3 4-19z"></path><defs><linearGradient id="G" x1="718.309" y1="165.335" x2="700.826" y2="160.205" xlink:href="#B"><stop offset="0" stop-color="#353635"></stop><stop offset="1" stop-color="#6e6d6f"></stop></linearGradient></defs><path fill="url(#G)" d="M699 149c8 7 14 16 21 24-2 0-4-1-6-1l-1-1v1l-1 1h-1c1-1 1-1 1-2-1-6-6-12-10-16h0l1-1-1-1c-2-2-2-2-3-4z"></path><path d="M583 833l-1 1c0 1-1 1-2 2-1 0-1 0-1 1s0 1-1 2c0 2 1 3 0 4 0 1 0 1-1 3v2c1 1 3 0 4 1h-2-10c1 0 3-1 4-1-1-1 0-3 0-4 0-2 2-5 1-7h-2 0 2l1-1c-2-1-6-1-8-1h1c3 0 8 1 11-2h4z" class="F"></path><path d="M323 329c6 7 12 14 21 18 2 1 5 1 7 2-10 0-18-4-26-11-2-2-4-4-5-7 2 1 2 2 3 4 2 1 2 1 4 1-2-2-4-4-4-7z" class="B"></path><path d="M714 127c2 1 5 2 7 3-3 0-4 1-6 3s-4 3-6 4l-9-4c1 0 2-1 3-1 3-1 5-2 7-3l4-2z" class="G"></path><path d="M334 467h1c2-8 5-15 9-22l1 1c-7 11-11 23-12 37-1 1 0 8 0 10l-2-2h-1v-1c-1-6 1-17 4-23z" class="B"></path><path d="M458 422c0-6 0-12-1-18-1-5-2-9-3-13l-1-7c2 3 4 6 5 10l-1 1c0-2-1-3-2-5 1 3 1 6 2 9 1 5 4 9 4 14l1 10c-1 0-3 0-4 1v-2z" class="F"></path><path d="M458 422v-7h1c0 1 0 1 1 1v-3h1l1 10c-1 0-3 0-4 1v-2z" class="C"></path><path d="M557 611c5 16 0 33-6 47l-2 1c0-3 2-5 2-7 3-8 4-16 5-23 1-6 1-12 1-18z" class="E"></path><defs><linearGradient id="H" x1="286.222" y1="157.179" x2="274.304" y2="170.344" xlink:href="#B"><stop offset="0" stop-color="#464747"></stop><stop offset="1" stop-color="#666566"></stop></linearGradient></defs><path fill="url(#H)" d="M275 184v-1l3-13c1-8 2-16 2-24 2 9 3 18 2 27-1 1-1 2-2 3h0c-1 4-3 8-3 12h-1c0-2 0-3-1-4z"></path><path d="M567 821c8 0 13 1 20 4-1 0-1 0-3-1h-2c0 1 0 2-1 3v2c0 1 1 1 2 2h1l-1 2c-1-3-6-4-9-5l-1-1c-3-1-6-1-9-1l1-5h0 2z" class="F"></path><path d="M565 821h0 2 1c2 1 4 1 7 1 0 1 0 4-1 5h-1c-3-1-6-1-9-1l1-5z" class="E"></path><path d="M461 413c0-5-3-9-4-14-1-3-1-6-2-9 1 2 2 3 2 5 4 8 7 20 6 29 0 3 0 6-1 9l-3 10c0-3 2-7 2-11v-1 1h-5l2-8c1-1 3-1 4-1l-1-10z" class="J"></path><path d="M458 424c1-1 3-1 4-1l-1 9v-1 1h-5l2-8z" class="B"></path><path d="M311 424h2 0 0c-3 1-6 3-8 6-1 2-1 4 0 6 0 1 1 1 2 2v-4c1-4 6-7 9-8l-4 4c-1 1-2 2-2 4 0 3 1 5 3 8h0c-3 0-6 0-8-2-2-1-4-3-4-5s0-5 1-7c3-3 6-4 9-4z" class="G"></path><path d="M393 495l1 2c1 0 2 0 3 2 2 1 1 4 1 6-1 3-5 5-8 6s-8-2-11-2v-1l1 1c4-1 7-3 10-5 3-3 3-5 3-9z" class="C"></path><defs><linearGradient id="I" x1="352.101" y1="220.432" x2="338.167" y2="246.408" xlink:href="#B"><stop offset="0" stop-color="#343535"></stop><stop offset="1" stop-color="#69686a"></stop></linearGradient></defs><path fill="url(#I)" d="M342 225c2-4 7-8 11-11-3 5-5 10-6 16-1 3-1 6-1 10-1 0-1 1-1 2-1 1 0 5-1 5h0c0 1-1 1-1 2 0-2 0-4-1-6-1-1 0-3-1-4 0-5 1-9 1-14z"></path><path d="M363 419c2 0 3-1 5-1l-23 28-1-1c-4 7-7 14-9 22h-1c0-5 3-11 5-15 6-12 14-24 24-33z" class="E"></path><path d="M352 235v5 2 2c1 2-1 7-2 9l-1 2v1l-2 1h1v1l-4 2c-1-1-1 0-1-2 0-1 0-2 1-3v-8c1 0 0-4 1-5 0-1 0-2 1-2 0 1 0 3 1 4h1c0-3 2-6 4-9z" class="H"></path><path d="M352 235v5c-1 1-1 3-1 5s-1 3-2 5 0 2-2 3c1-3 1-6 1-9s2-6 4-9z" class="G"></path><path d="M344 247c1 0 0-4 1-5 0-1 0-2 1-2 0 1 0 3 1 4h1c0 3 0 6-1 9-2 1-1 2-3 2v-8z" class="E"></path><defs><linearGradient id="J" x1="425.34" y1="706.227" x2="414.16" y2="727.273" xlink:href="#B"><stop offset="0" stop-color="#5d5e5f"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#J)" d="M410 691c2 1 2 3 3 5 3 4 6 8 7 13 2 8 1 16 5 23 1 2 3 4 5 6-2-1-5-2-7-5-6-6-5-16-7-24-1-7-3-12-6-18z"></path><defs><linearGradient id="K" x1="378.456" y1="412.98" x2="372.044" y2="396.02" xlink:href="#B"><stop offset="0" stop-color="#505155"></stop><stop offset="1" stop-color="#676562"></stop></linearGradient></defs><path fill="url(#K)" d="M389 379v1c4-8 6-18 7-26v6c-4 21-13 42-28 58h0c-2 0-3 1-5 1 8-9 15-18 21-29 2-3 3-7 5-10v-1z"></path><defs><linearGradient id="L" x1="565.916" y1="889.888" x2="575.933" y2="885.216" xlink:href="#B"><stop offset="0" stop-color="#323232"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#L)" d="M563 876l10 2c1-1 1-1 1-2h1v2l-2 9c0 3 0 6-1 8-2 1-2 3-3 4l-1-2v-5h0c0-3-1-4-3-7l-2-9z"></path><path d="M441 594c4 1 8 0 12 0 1 1 1 1 1 3 1 3 3 6 5 8 0 1 1 1 1 2-1-1-2-1-3-2-3 0-6 2-9 3 0 0 0-1-1-1-2-3-6-9-6-13z" class="I"></path><defs><linearGradient id="M" x1="480.586" y1="742.755" x2="490.203" y2="731.492" xlink:href="#B"><stop offset="0" stop-color="#444445"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#M)" d="M493 759c-3-11-5-21-11-31-3-5-7-9-11-14 4 2 9 7 12 11 6 7 12 16 11 25v6l-1 3z"></path><path d="M454 581c-1 4-2 8-1 13-4 0-8 1-12 0-1-4-1-9 0-12h1c2-1 4 0 6-1h2 4z" class="G"></path><defs><linearGradient id="N" x1="642.277" y1="150.585" x2="637.488" y2="158.902" xlink:href="#B"><stop offset="0" stop-color="#464647"></stop><stop offset="1" stop-color="#767575"></stop></linearGradient></defs><path fill="url(#N)" d="M620 131l1-1c6 11 15 27 27 30 4 1 6 1 10 1v-1c2 0 5-1 7-1-7 4-13 6-20 4-6-1-10-6-13-11-5-6-8-14-12-21z"></path><path d="M563 876c-1-4-1-8 2-12 1-1 2-2 4-2h0 0c-1 2-1 3-1 6h1 0c4-3 6-3 11-3-3 2-5 3-6 6v1c3-1 5-3 8-3-3 2-5 5-7 9v-2h-1c0 1 0 1-1 2l-10-2z" class="D"></path><path d="M543 671c1-6 3-12 5-18 3-8 3-18 2-27 0-4-2-9-3-14 2 3 4 7 5 11 0 1 0 4 2 6v-4c0-2 1-3 1-5v1c0 1 1 2 1 4h-1 1v4c-1 7-2 15-5 23 0 2-2 4-2 7-1 2-2 5-3 6-1 2-2 4-2 6h-1z" class="L"></path><defs><linearGradient id="O" x1="460.512" y1="507.416" x2="445.977" y2="508.978" xlink:href="#B"><stop offset="0" stop-color="#555656"></stop><stop offset="1" stop-color="#6d6b6c"></stop></linearGradient></defs><path fill="url(#O)" d="M456 496c1-2 3-4 4-6s1-4 2-6c2-3 3-6 4-8 0-2 1-4 2-5-1 5-3 11-5 16 0 2 0 5-1 7-2 9-7 16-12 23-2 3-4 5-7 7h-2c2-2 4-4 5-6 4-5 5-10 8-15 1-2 2-3 2-5 1-1 0-1 0-2z"></path><path d="M477 838c-5-13-12-25-19-37 3 3 6 6 9 10 2 2 4 6 6 8h1c0 1 1 1 1 2h1 1c0-1 1-1 1-2v-1l1-1c0 7 0 14-1 21h-1z" class="G"></path><defs><linearGradient id="P" x1="703.891" y1="497.649" x2="714.288" y2="511.591" xlink:href="#B"><stop offset="0" stop-color="#39393b"></stop><stop offset="1" stop-color="#545453"></stop></linearGradient></defs><path fill="url(#P)" d="M717 485h1c0 1 0 0 1 1h0l-17 37-3 4-3 8c0 1-1 1-1 2-3-6 11-29 14-36 3-5 5-11 8-16z"></path><path d="M565 613v-1l1 1c0 1 1 2 1 3l1 1v1h1c1 1 0 1 1 2 2 1 3 4 4 6l3 3c1 2 3 3 5 5 1 1 2 1 3 2l1 1 1 1h0 1c2 1 3 2 5 2l1 1h1 1c0-2 0-2 1-3h0v2l9-4h17 9c-9 4-18 6-28 6-4 1-8 0-11-1-12-4-23-17-28-28z" class="D"></path><defs><linearGradient id="Q" x1="487.398" y1="796.214" x2="477.602" y2="793.786" xlink:href="#B"><stop offset="0" stop-color="#3c3e3c"></stop><stop offset="1" stop-color="#625f63"></stop></linearGradient></defs><path fill="url(#Q)" d="M484 767c0 1 1 10 2 10h0c0-2 0-1 1-2 0 11-2 22-3 34 0 3 0 6-1 9l-1 1c-1 3 1-1 0 2-1 1-1 4-1 6h-1v-1-2c0-2 1-4 0-6v-2h0l-1 1 4-34v-4c1 0 0-7 1-8v-4z"></path><path d="M364 588c2-9 2-20 10-25 3-2 7-4 11-3 1 0 3 0 5 1-7 2-12 5-16 11-3 5-5 12-7 18l-3-2z" class="B"></path><path d="M825 150c2 2 2 4 3 6 1 7 2 14 1 20 0 7-1 14-2 21-3 11-7 21-11 32-2 5-4 13-8 18 1-4 2-7 3-10l4-11c6-15 11-30 12-45 1-11 0-21-2-31z" class="E"></path><path d="M420 565h1c-2 1-5 2-7 4-10 4-22 8-26 20-2 4-2 8 0 12v3l-2-1c-1-2-3-4-4-6-1-3-1-7 0-10 7-14 25-17 38-22z" class="B"></path><path d="M721 273v-1c4-14 14-29 23-42 5-6 11-14 18-17l-2 1c-10 9-18 23-23 35-2 4-3 9-4 13l-2 2h0v2l-1 3v-1h-2c-1-1-2-1-2-2h-2c-1 3-1 5-3 7z" class="D"></path><defs><linearGradient id="R" x1="386.27" y1="459.641" x2="351.581" y2="477.605" xlink:href="#B"><stop offset="0" stop-color="#4f4f4f"></stop><stop offset="1" stop-color="#706f70"></stop></linearGradient></defs><path fill="url(#R)" d="M373 450h1 0c2-2 6-2 9-3-7 10-14 20-18 31-1 3-2 6-4 8 0 1-1 3-1 4-1 1-2 2-2 4h0-1c0-4 1-8 2-12 2-6 5-12 8-18 2-5 5-9 6-14z"></path><defs><linearGradient id="S" x1="678.858" y1="121.778" x2="682.646" y2="129.21" xlink:href="#B"><stop offset="0" stop-color="#3f3e40"></stop><stop offset="1" stop-color="#626261"></stop></linearGradient></defs><path fill="url(#S)" d="M691 130c-1 0-4-1-5-1l-18-6 2-1c1 1 2 0 4 0 6 1 12 1 18 1 6 1 11 2 16 3l6 1-4 2c-2 1-4 2-7 3-1 0-2 1-3 1-1 1-7-2-9-3z"></path><path d="M708 126l6 1-4 2c-2 1-4 2-7 3-1 0-2 1-3 1-1 1-7-2-9-3 3-2 7-2 10-2 3-1 5-1 7-2z" class="B"></path><path d="M277 188v2-1c0-2 1-2 2-4h0v3c0 1 0 1-1 2v2h1l-19 73s0-1-1-2v-1c-1-1 0-4 0-6v-1l3-14c4-9 6-20 8-30 2-8 5-15 6-23h1z" class="H"></path><defs><linearGradient id="T" x1="545.307" y1="909.216" x2="522.804" y2="892.727" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#6d6c6c"></stop></linearGradient></defs><path fill="url(#T)" d="M523 900c2-5 2-12 5-16h2c0 8 1 14 6 20 3 3 7 4 11 5-2 1-2 2-3 4h3c-3 0-5 1-7 0-8-1-13-7-17-13z"></path><defs><linearGradient id="U" x1="399.917" y1="409.683" x2="423.583" y2="420.817" xlink:href="#B"><stop offset="0" stop-color="#646364"></stop><stop offset="1" stop-color="#7e7e7e"></stop></linearGradient></defs><path fill="url(#U)" d="M404 396l-2-24c4 14 11 27 13 42v13c0 3 0 6 1 9 0 2 2 4 3 5-3-1-5-3-7-6-3-5-2-14-2-20l-2-18c-1-3-1-6-2-8v1l-1-1c-1 2-1 4-1 7z"></path><path d="M749 147c4 1 10 10 12 15 3 6 5 14 3 21-2 6-6 10-11 13-2-1-5-3-7-4 2-1 3-1 4-2 5-3 8-8 9-14 2-11-4-21-10-29z" class="C"></path><path d="M483 783l-4 34-1 1v1c0 1-1 1-1 2h-1-1c0-1-1-1-1-2l-21-33 14 11-7-9c3 1 5 5 6 7 1 1 1 1 1 2l-1 1c-3-2-5-4-7-6h0c1 1 2 3 3 4v1c1 1 3 2 4 2h1c3 1 5 5 7 8 0-2 0-4-1-6v-1c1-1 0-2 0-3s-1-2-1-3 0-2-1-3c0-2-1-3-1-4v-1c4 8 5 17 5 26 3-9 3-18 4-27 0 2 1 5 0 8v3h1c0-1 0-1 1-2v-2-2c0-2 1-2 0-4h0c1-1 1-2 2-3z" class="L"></path><path d="M470 786c-2-5-6-8-10-11 3-3 5-5 9-6 1 0 2 0 4 1 4 3 5 10 6 15-1 9-1 18-4 27 0-9-1-18-5-26z"></path><path d="M543 628c2 14 0 31-8 42-10 14-28 26-46 28h0v-1c3-1 7-1 10-4h0c8-4 16-8 23-14 12-11 19-25 20-41v-4c1-2 1-4 1-6z" class="E"></path><defs><linearGradient id="V" x1="748.55" y1="246.511" x2="737.725" y2="243.936" xlink:href="#B"><stop offset="0" stop-color="#4f4f50"></stop><stop offset="1" stop-color="#686768"></stop></linearGradient></defs><path fill="url(#V)" d="M733 262c1-4 2-9 4-13 5-12 13-26 23-35 0 4-3 5-4 8-2 3-5 7-6 10 0 3 0 5-1 7h1c-2 3-6 8-7 11l-9 19h0l-4 11c0-6 1-12 3-18z"></path><path d="M734 269c0-2 1-4 1-6 2-7 4-13 8-20 1-2 2-4 4-6 1-2 1-3 3-5 0 3 0 5-1 7h1c-2 3-6 8-7 11l-9 19z" class="F"></path><defs><linearGradient id="W" x1="331.653" y1="163.052" x2="364.512" y2="166.257" xlink:href="#B"><stop offset="0" stop-color="#0c0c0b"></stop><stop offset="1" stop-color="#424243"></stop></linearGradient></defs><path fill="url(#W)" d="M329 184c0-2 0-4 1-5 0-2 2-5 4-8 7-11 16-18 29-23 2 1 3 3 6 3-6 2-13 5-18 10-4 4-8 9-11 13v1l1 1h0l-12 8z"></path><defs><linearGradient id="X" x1="765.942" y1="212.326" x2="780.338" y2="222.587" xlink:href="#B"><stop offset="0" stop-color="#343334"></stop><stop offset="1" stop-color="#616161"></stop></linearGradient></defs><path fill="url(#X)" d="M781 192l1-1h1c1 3 0 7 1 10-1 11-5 20-10 30-2 5-5 10-8 14-2 2-3 4-4 5v1h-1c-1 1-2 1-4 1 0-2 1-2 1-3l2-2c2-6 6-11 9-16 3-7 5-14 8-22 1-1 1-2 1-4 0 0 2-12 3-13z"></path><path d="M760 247h0l2-1c1-1 3-3 4-3 0 1-1 1-1 2h0 1c-2 2-3 4-4 5v1h-1c-1 1-2 1-4 1 0-2 1-2 1-3l2-2z" class="H"></path><defs><linearGradient id="Y" x1="378.661" y1="237.129" x2="361.548" y2="254.726" xlink:href="#B"><stop offset="0" stop-color="#1e1d1d"></stop><stop offset="1" stop-color="#454545"></stop></linearGradient></defs><path fill="url(#Y)" d="M362 252c3-1 6-1 9-4 0-3-4-9-6-12 3-1 6-3 9-2 2 0 6 2 7 4s0 7 0 10c-2 6-8 11-13 15v-1c-1-2-1-3-2-4v-1-5h-4z"></path><path d="M404 396c0-3 0-5 1-7l1 1c1 11 1 21-7 30-3 5-8 8-14 9v1 1l-2-2c-3 0-6-1-10-2 1-1 1-3 1-4 6 0 11-1 15-4 9-6 13-14 15-23z" class="B"></path><path d="M778 205c0 2 0 3-1 4-3 8-5 15-8 22-3 5-7 10-9 16l-2 2v-1-1h0 0-2l-4 2c-2 1-4 1-6 3-1 1-2 1-3 1v-3c1-3 5-8 7-11 2-2 3-6 5-8l-2 9c9-10 20-21 25-35z" class="D"></path><path d="M466 662v1c5 0 9-1 13-2-5 4-12 8-19 10-5 1-10 1-15 1s-10 1-15 1c-5-1-11-3-15-6 4 0 8 1 13 0 2 0 5-1 8-2h3c2-1 5 0 7 0 7 1 13-1 20-3z" class="C"></path><defs><linearGradient id="Z" x1="742.387" y1="428.267" x2="701.828" y2="486.963" xlink:href="#B"><stop offset="0" stop-color="#353535"></stop><stop offset="1" stop-color="#4e4d4e"></stop></linearGradient></defs><path fill="url(#Z)" d="M732 434c4-4 8-6 13-8-6 8-12 17-17 26l-14 22-9 20h-1c0-3 1-4 0-7 3-7 6-15 10-22l9-14c3-4 8-11 9-17z"></path><defs><linearGradient id="a" x1="740.714" y1="416.226" x2="720.773" y2="413.479" xlink:href="#B"><stop offset="0" stop-color="#2e2f2f"></stop><stop offset="1" stop-color="#535253"></stop></linearGradient></defs><path fill="url(#a)" d="M740 383c1 0 6 0 7 1h-2c0 6 0 13-2 19-2 7-6 13-10 19-2 4-4 9-7 13s-8 9-10 14h0l-1 1h-1c1-3 2-6 4-9 5-12 11-23 17-34 5-8 5-15 5-24z"></path><defs><linearGradient id="b" x1="368.656" y1="189.506" x2="329.911" y2="179.109" xlink:href="#B"><stop offset="0" stop-color="#454648"></stop><stop offset="1" stop-color="#6c6b6a"></stop></linearGradient></defs><path fill="url(#b)" d="M358 167c7-3 16-4 24-4-5 2-10 3-15 7-6 4-39 35-40 35h-2v1c-1-2-1-3-1-4l27-27c2-2 5-4 7-6v-2z"></path><defs><linearGradient id="c" x1="361.651" y1="277.694" x2="347.509" y2="272.586" xlink:href="#B"><stop offset="0" stop-color="#3c3c3d"></stop><stop offset="1" stop-color="#626162"></stop></linearGradient></defs><path fill="url(#c)" d="M354 254l8-2h4v5 1c1 1 1 2 2 4v1c-11 8-23 18-25 33v6 4c0 1-1 2-1 3l-1-3v-6c0-16 8-26 19-38-4 1-8 2-13 2v-1l1-1c1-2 0-2 0-4v-1l6-3z"></path><defs><linearGradient id="d" x1="450.463" y1="634.565" x2="453.925" y2="615.711" xlink:href="#B"><stop offset="0" stop-color="#2e2e2e"></stop><stop offset="1" stop-color="#616162"></stop></linearGradient></defs><path fill="url(#d)" d="M459 639l1-2c-2-10-13-11-20-17-3-2-4-5-4-8l1 1c1 1 2 3 4 3 1 1 4 0 5 0 4 0 9-1 13 0 5 2 9 5 11 10 0 2-1 3-2 4h-1c0 1 0 2-1 3-1 2-3 4-4 5-2 1-2 1-3 1z"></path><path d="M466 633h-2c-1 0-4-4-5-5-4-3-9-5-13-8 4-1 8-1 12 0 4 2 7 6 9 10 0 1 0 2-1 3z"></path><defs><linearGradient id="e" x1="259.602" y1="296.067" x2="226.185" y2="292.176" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#8d8b8c"></stop></linearGradient></defs><path fill="url(#e)" d="M250 320l-14-27c-3-5-6-11-7-16-1-4-1-8 0-11 1-2 2-4 3-7 1 2 2 4 2 5 1 3 1 6 1 9s1 6 2 9c6 17 15 31 21 48l-1 1v-1c-2-3-4-8-7-10z"></path><defs><linearGradient id="f" x1="430.175" y1="321.365" x2="421.021" y2="323.763" xlink:href="#B"><stop offset="0" stop-color="#191818"></stop><stop offset="1" stop-color="#4e4e4f"></stop></linearGradient></defs><path fill="url(#f)" d="M413 273l5 11c0 2 2 4 2 5l-1 1c-1 6 2 13 3 19 3 17 6 35 16 51 2 2 4 5 6 8 1 0 1 1 2 2l2 1v1c-4-2-7-4-9-7-5-6-10-14-13-21-5-15-8-31-10-47-2-7-4-16-3-24z"></path><defs><linearGradient id="g" x1="565.372" y1="878.987" x2="541.852" y2="860.609" xlink:href="#B"><stop offset="0" stop-color="#393839"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#g)" d="M534 888l3-3v-5c0-8 2-17 7-23 8-9 17-12 29-13 0 1-1 3 0 4-1 0-3 1-4 1-9 2-18 7-23 15s-7 18-5 27c1 6 6 11 11 14l2 1c-8-2-14-5-17-12l-3-6z"></path><path d="M485 545c6-7 9-17 9-27-1-2-1-4-2-6l-1-2c0-1 1-2 1-3 1-2 2-6 4-8h0c0 8 2 16 2 23 4-8 8-19 4-29 0-2-1-3-2-4 0-1-2-3-2-4 2 2 4 4 6 7v1c3 4 3 9 3 14-1 10-6 22-11 31-1 3-3 6-6 9 0 1-1 1-3 2l-2-4z" class="F"></path><defs><linearGradient id="h" x1="837.248" y1="153.184" x2="858.692" y2="156.078" xlink:href="#B"><stop offset="0" stop-color="#646464"></stop><stop offset="1" stop-color="#8a8989"></stop></linearGradient></defs><path fill="url(#h)" d="M834 126c5 1 9 2 14 5 6 5 10 13 11 21 2 12-4 22-11 31 2-10 3-22-1-32-1-3-3-6-5-9s-4-5-6-7v-1l-2-2h1c3 1 6 4 8 6 9 9 11 19 10 31 3-5 3-11 2-16-1-14-12-20-21-27z"></path><path d="M673 148c-4-2-9-2-13-3-13-1-22-6-32-15h1l1 1h2c2-2 6-3 9-5h2l-1-1c-2 0-2 0-4-1 5-2 14 0 19 2l4 1h-5c-3 0-5 1-7 3-2 1-4 1-5 1h-4l-1 1c3 1 9 2 12 3 8 3 16 6 24 10v1c1 1 2 2 3 2 3 2 6 6 8 9h0c-1-2-2-3-3-3-3-2-6-6-10-6z" class="F"></path><defs><linearGradient id="i" x1="321.073" y1="399.09" x2="289.263" y2="412.617" xlink:href="#B"><stop offset="0" stop-color="#4d4d4e"></stop><stop offset="1" stop-color="#959394"></stop></linearGradient></defs><path fill="url(#i)" d="M321 408c-8 1-13 0-20-3-1 1-2 3-3 5 0 1 0 1 1 2 9 6 22-1 30-5 2-1 3-3 6-3-8 7-18 13-29 15h-1-2c-6 1-11-2-15-6 2-7 4-13 8-18 5 2 9 5 14 7 3 2 6 3 10 3 0 1 1 2 1 3z"></path><defs><linearGradient id="j" x1="671.825" y1="157.718" x2="691.283" y2="140.686" xlink:href="#B"><stop offset="0" stop-color="#403f40"></stop><stop offset="1" stop-color="gray"></stop></linearGradient></defs><path fill="url(#j)" d="M661 127c15 3 27 12 38 22h0c1 2 1 2 3 4l1 1-1 1h0c4 4 9 10 10 16 0 1 0 1-1 2h-1c-1-4-3-7-6-11-14-19-37-28-60-31 1 0 3 0 5-1 2-2 4-3 7-3h5z"></path><defs><linearGradient id="k" x1="691.96" y1="186.362" x2="715.378" y2="180.571" xlink:href="#B"><stop offset="0" stop-color="#202020"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#k)" d="M675 145c8 5 17 15 20 25 1 6 1 12 7 15 1 1 3 2 5 1s3-3 3-5c1-2 1-4 0-7v-1h0 1 1l1-1v-1l1 1c1 2 1 4 2 6 0 9-2 17-8 24-5-3-11-5-14-10-5-6-2-12-2-19 1-4-1-8-4-12-4-5-9-9-15-13 4 0 7 4 10 6 1 0 2 1 3 3h0c-2-3-5-7-8-9-1 0-2-1-3-2v-1z"></path><path d="M401 246c0 1 1 2 1 3-16 27-2 62-22 88l-1-1-1 1h-2-1c2-2 4-5 4-7 0-1 0-2 1-2l2-7c1-3 2-5 2-8 4-20 1-41 11-59 2-3 4-5 6-8z" class="L"></path><defs><linearGradient id="l" x1="763.204" y1="320.127" x2="727.796" y2="313.373" xlink:href="#B"><stop offset="0" stop-color="#232424"></stop><stop offset="1" stop-color="#4b494a"></stop></linearGradient></defs><path fill="url(#l)" d="M753 293c5-5 14-9 21-10-2 2-5 4-8 7-7 10-12 21-19 31-3 3-16 18-16 22h1c-4 4-8 6-13 8 6-11 13-21 20-31h0c4-5 7-10 11-15l8-13h0c-2 1-3 1-4 1h-1z"></path><defs><linearGradient id="m" x1="742.613" y1="371.061" x2="708.582" y2="358.645" xlink:href="#B"><stop offset="0" stop-color="#1d1d1f"></stop><stop offset="1" stop-color="#555"></stop></linearGradient></defs><path fill="url(#m)" d="M734 342c1-2 3-3 4-4 2-1 4-2 5-2 5-2 8-3 12-4l3 1h0c-7 5-11 10-17 16l-25 22h0c3-1 6-1 9 0 0 1 0 0-1 0-10 2-20 13-25 22-3 4-5 9-7 14 0-3 0-7 1-10h0c2-2 4-7 5-9 4-6 7-12 11-17 10-12 24-20 33-33-3 2-5 3-8 4h0z"></path><path d="M785 226c2 3 0 3 0 6 0 1 1 1 1 2l-1 1v3c-5 12-12 23-20 33-7 9-16 18-24 27-10 10-20 21-25 35 0 2-2 5-2 6 0 2-2 7-3 8 1-13 8-27 16-37 3-5 7-9 11-13l19-23c5-7 10-15 15-23 2-3 5-8 5-12l8-13z" class="E"></path><defs><linearGradient id="n" x1="366.317" y1="137.652" x2="422.23" y2="150.78" xlink:href="#B"><stop offset="0" stop-color="#545455"></stop><stop offset="1" stop-color="#8b8a8a"></stop></linearGradient></defs><path fill="url(#n)" d="M365 139c13-6 28-10 42-4 7 2 13 7 18 12-5 7-10 11-18 14-8-7-14-14-26-13-2 1-5 1-6 2-1-1-1-1-2-1l-5-3c10-3 20-4 30 0 3 1 5 4 9 5 1-1 2-1 2-3 1-1 1-2 1-3-1-3-4-5-6-6-11-5-26-1-36 2-1 0-2-1-3-2z"></path><path d="M675 145c-8-4-16-7-24-10-3-1-9-2-12-3l1-1h4c23 3 46 12 60 31 3 4 5 7 6 11h0v1c1 3 1 5 0 7 0 2-1 4-3 5s-4 0-5-1c-6-3-6-9-7-15-3-10-12-20-20-25z"></path><defs><linearGradient id="o" x1="317.184" y1="263.509" x2="294.427" y2="251.855" xlink:href="#B"><stop offset="0" stop-color="#4b4b4c"></stop><stop offset="1" stop-color="#6a6a6a"></stop></linearGradient></defs><path fill="url(#o)" d="M338 202c4-4 12-7 17-7-3 3-6 5-8 8l-17 18c-10 11-21 21-30 33-13 17-22 37-23 59v14c0 3 1 7 1 10-1-3-2-5-2-8-6-31 6-65 27-88l23-24c4-5 9-9 12-14v-1z"></path><defs><linearGradient id="p" x1="435.499" y1="360.843" x2="422.031" y2="366.248" xlink:href="#B"><stop offset="0" stop-color="#343435"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#p)" d="M433 376c-2-4-5-8-8-11-6-9-11-17-15-27-4-11-5-23-5-36 0-2 0-5 1-7v8c0 3 2 7 2 10 3 13 9 25 16 37 8 12 17 23 23 36l6 18c1 4 1 9 2 12 1 2 1 6 1 8v1c0 3-1 6-1 8l-1 1c-1-1-1-4-1-6l-1-11-3-12c-1-7-10-24-16-29z"></path><path d="M452 417h1v2h0c0-1 0-2 1-2 0 2 0 6 1 8v-1l1 1c0 3-1 6-1 8l-1 1c-1-1-1-4-1-6l-1-11z" class="C"></path><path d="M504 720c1 3 1 5 0 7l-1 4c4-6 7-13 12-18 3-3 6-5 9-8 6-4 12-7 18-11 3-2 6-4 8-8 2-3 3-8 4-11l3-21c5 4 9 9 9 16s-4 16-10 21c-7 8-16 11-26 15 7 0 13 0 19 5v1h-1-2c-7-3-14 0-20 3-10 6-15 13-21 23l-1-1c-1 1-1 2-1 2l-3 6-2 2-1 2c0 1 0 1-1 1 0-3 2-8 2-12v-1c-1-1 0-3 1-5 1-6 1-11-1-17l1-1c1 3 3 5 5 6z" class="B"></path><defs><linearGradient id="q" x1="711.721" y1="162.015" x2="757.092" y2="158.066" xlink:href="#B"><stop offset="0" stop-color="#c2c2c1"></stop><stop offset="1" stop-color="#ecece9"></stop></linearGradient></defs><path fill="url(#q)" d="M721 130c10 3 20 9 28 17 6 8 12 18 10 29-1 6-4 11-9 14-1 1-2 1-4 2-4-2-7-4-9-8 3-2 5-4 7-7 2-4 2-9 1-13-3-10-12-19-22-24 3 3 5 6 6 10 0 4-1 6-4 9v-5c-2-8-10-13-16-17 2-1 4-2 6-4s3-3 6-3z"></path><defs><linearGradient id="r" x1="307.084" y1="340.259" x2="345.46" y2="306.426" xlink:href="#B"><stop offset="0" stop-color="#868585"></stop><stop offset="1" stop-color="#9e9d9d"></stop></linearGradient></defs><path fill="url(#r)" d="M380 337c-3 7-12 14-20 17-11 5-24 6-35 1-10-4-15-10-21-17-1-2-3-4-4-6l2 1h3c0-3-3-7-4-10l-7-24c5 6 8 12 11 19 2-11-5-23-8-33-1-3-1-7-1-10 2 8 5 15 10 22 3 4 3 9 4 14 3 6 8 12 13 18 0 3 2 5 4 7-2 0-2 0-4-1-1-2-1-3-3-4l-1-1c-2 1-3 3-3 5-1 1-1 3 0 5 3 5 15 9 21 11 10 2 21 1 30-4 3-2 8-6 10-9l1-1 1-1 1 1z"></path><defs><linearGradient id="s" x1="488.075" y1="608.266" x2="517.956" y2="556.827" xlink:href="#B"><stop offset="0" stop-color="#b5b3b3"></stop><stop offset="1" stop-color="#eae9e8"></stop></linearGradient></defs><path fill="url(#s)" d="M441 582c2-7 5-12 10-17 11-10 27-13 42-12 15 0 32 3 44 16 1-1 1-1 2 0s2 2 4 2l1-1 14 31h-1l1 1v1h-1c-1 1 0 5 0 8h0c0 6 0 12-1 18v-4h-1 1c0-2-1-3-1-4v-1c0 2-1 3-1 5v-8c-1-15-9-34-21-44-9-9-24-13-37-12h0 0l15-2h-1c-3-1-8-1-12-2-10 0-21 0-30 6-7 4-12 10-14 18h-4-2c-2 1-4 0-6 1h-1z"></path><path d="M537 569c1-1 1-1 2 0s2 2 4 2l1-1 14 31h-1l1 1v1h-1c-1 1 0 5 0 8-1-3-1-6-2-8-3-9-6-17-11-25-1-2-2-5-4-6l-3-3h0z" class="I"></path><defs><linearGradient id="t" x1="488.69" y1="646.599" x2="438.69" y2="627.302" xlink:href="#B"><stop offset="0" stop-color="#7a797a"></stop><stop offset="1" stop-color="#929191"></stop></linearGradient></defs><path fill="url(#t)" d="M434 646c11 5 23 10 36 5 5-1 11-4 13-9s2-12 0-18c-1-2-3-4-4-6-2-1-4-3-4-5 1-1 1-2 2-3 3 0 6 1 8 4 6 4 8 10 8 17 0 10-4 20-10 27l-4 3c-4 1-8 2-13 2v-1c-7 2-13 4-20 3-2 0-5-1-7 0-12-3-21-8-28-19 5 4 9 7 15 10l1-1h1c3-2 7-4 10-6-1 0-2-1-3-2l-1-1z"></path><path d="M439 665c-12-3-21-8-28-19 5 4 9 7 15 10 16 4 35 5 51 0-4 2-7 5-11 6-7 2-13 4-20 3-2 0-5-1-7 0z"></path><path d="M455 488s1 0 1-1c2-1 5-11 7-10l-1 2h0v1l-1 1h0c0 2 0 1-1 2v1l-1 1c0 1 0 1-1 2v1h0c-1 1-1 2-1 3h0l-6 10v1c2-1 4-4 5-6 0 1 1 1 0 2 0 2-1 3-2 5-3 5-4 10-8 15-1 2-3 4-5 6v1c-8 7-20 10-30 9-7 0-14-2-20-6-3-2-5-4-7-7 5 3 9 6 14 6 15-1 23-9 32-19l-5-5c6-4 11-7 16-12 1 3 1 7 4 9 4-2 7-8 10-12z" class="D"></path><path d="M437 511h1c1 1 2 3 2 5 0 1 0 2-1 3-5 6-15 8-22 9-1 0-1 0-2-1 3-1 6-2 9-4 5-3 9-8 13-12z"></path><defs><linearGradient id="u" x1="558.37" y1="826.429" x2="539.438" y2="798.041" xlink:href="#B"><stop offset="0" stop-color="#5a5a5b"></stop><stop offset="1" stop-color="#878686"></stop></linearGradient></defs><path fill="url(#u)" d="M504 852c1-18 14-36 27-48 16-14 39-22 60-21 6 1 12 2 16 7l2 3c-12-7-26-6-39-3-7 2-15 4-21 8 14-4 27-8 41-1 4 2 7 6 10 9-5-2-9-5-14-6h-1c-15-4-32 2-45 10-15 9-28 23-32 41v1l-1 8c-1 4-1 9-3 13-1-6-1-11-1-17 0-1 0-3 1-4z"></path><path d="M504 852v-1c1-1 1-2 1-3v-1c2-3 2-7 4-10 1-4 3-8 6-12-3 7-7 15-9 23 0 3 1 5 0 7h1v-1s0-1 1-2l-1 8c-1 4-1 9-3 13-1-6-1-11-1-17 0-1 0-3 1-4z" class="I"></path><defs><linearGradient id="v" x1="500.213" y1="654.508" x2="490.832" y2="566.146" xlink:href="#B"><stop offset="0" stop-color="#666565"></stop><stop offset="1" stop-color="#8d8c8c"></stop></linearGradient></defs><path fill="url(#v)" d="M490 566c4-1 9-1 13 0h0c6 1 11 5 16 7 4 2 8 2 12 3-4 0-8 0-13-1s-9-4-14-5c-9-3-18 0-26 4 6 3 13 3 18 6-4 0-10 0-13 2-2 1-3 2-4 5 1 1 1 0 2 1 11 1 19 5 26 14 8 9 11 18 10 29-2 15-12 26-23 36v-1c3-3 6-7 9-11 7-9 9-21 7-32s-11-18-21-21c-5-1-12-1-16-4s-7-8-7-13c-1-4 0-9 2-12 5-5 16-6 22-7z"></path><path d="M366 442c1 2 0 6 0 9v1h1l3-3c1 0 2 0 3 1-1 5-4 9-6 14-3 6-6 12-8 18-1 4-2 8-2 12h1 0c0-2 1-3 2-4l1 1c-1 3-4 8-2 12h0c2-7 4-14 11-17 3-3 9-3 13-2s7 4 9 7l1 4h0c0 4 0 6-3 9-3 2-6 4-10 5l-1-1 3-3c2-3 5-7 4-11l-1-1h-1c-2-1-4-1-6-1h-4c-2 2-4 4-6 5-3 7-4 14-1 20v1c3 9 9 14 17 20h-1l-6-3c-10-5-17-13-23-22-6-11-17-37-14-49 2 1 2 7 3 9 1 5 2 9 4 13 1-17 10-30 19-44z" class="D"></path><path d="M368 497c1-3 3-6 6-8 2-1 4-1 6-1 3 1 5 3 6 6l-1-1h-1c-2-1-4-1-6-1h-4c-2 2-4 4-6 5z" class="H"></path><path d="M368 120h8c-1 0-3 0-4 1-1 0-8 6-10 7l-70 70h-1l1-1v-1c-2 2-4 5-6 7 1-3 3-6 5-9 5-8 9-16 12-26 3-13 3-26-4-38 4 0 7 1 10 4 4 6 4 13 3 20 2-3 4-7 5-11l4-12c2-3 3-6 5-8h7c1 0 3 0 4 1 0-1 1-2 1-3 1-1 7-1 9-1h21z" class="D"></path><defs><linearGradient id="w" x1="331.951" y1="121.201" x2="332.988" y2="186.004" xlink:href="#B"><stop offset="0" stop-color="#333033"></stop><stop offset="1" stop-color="#5f6260"></stop></linearGradient></defs><path fill="url(#w)" d="M368 120h8c-1 0-3 0-4 1-1 0-8 6-10 7l-70 70h-1l1-1v-1c1-2 3-4 5-6l31-36c7-8 14-16 22-24-7 2-11 3-16 9l3-15h0c0-1 1-2 1-3 1-1 7-1 9-1h21z"></path><path d="M368 120h8c-1 0-3 0-4 1-1 0-8 6-10 7l1-1c1-1 0-1 1-1-3-2-4-4-7-5h-1l11-1h1z" class="I"></path><path d="M565 821l-1 5c3 0 6 0 9 1l1 1c3 1 8 2 9 5h-4c-3 3-8 2-11 2h-1c2 0 6 0 8 1l-1 1h-2 0 2c1 2-1 5-1 7-12 1-21 4-29 13-5 6-7 15-7 23v5l-3 3v-2c-1-5-1-9-1-14l-3 12h-2c-3 4-3 11-5 16-5-6-7-12-8-20-2-15 1-27 10-38 10-13 24-19 40-21z" class="D"></path><path d="M567 835c2 0 6 0 8 1l-1 1h-2 0l-17 3c-2 1-5 3-8 3 6-4 10-6 17-8h3z" class="J"></path><path d="M560 832c-5 0-10 2-15 5 5-6 11-9 19-11 3 0 6 0 9 1l1 1c3 1 8 2 9 5h-4c-3 3-8 2-11 2h-1-3l1-1 1 1-1-1 1-1h-1-1c-1-1-3-1-4-1z"></path><path d="M560 832c7-1 12 0 19 1-3 3-8 2-11 2h-1-3l1-1 1 1-1-1 1-1h-1-1c-1-1-3-1-4-1z" class="G"></path><path d="M534 845h0c2 0 4 1 5 3 2 1 3 2 4 5-1 3-6 5-9 7-5 4-8 9-8 16l-1 7h0c-2-4-2-9-2-13 1-8 4-19 11-25z"></path><path d="M260 265c-3 13-5 27-3 40 1 15 5 29 10 43v1c-5-13-10-26-13-40-2-11-2-22-3-34-2-19-3-39-19-53-10-9-22-12-36-11 7-4 15-6 23-4 10 2 19 10 24 18 2 3 3 5 4 8-4-26-20-46-47-51-6-1-13 0-20-2-5-1-8-4-12-8 3-4 6-6 10-7h1c7-2 16 0 23 3 32 10 48 38 52 70l2 12c0 2 0 5 1 7v-2l1 1c1 0 1 0 1-1v1c0 2-1 5 0 6v1c1 1 1 2 1 2z" class="B"></path><defs><linearGradient id="x" x1="434.68" y1="368.598" x2="409.844" y2="378.439" xlink:href="#B"><stop offset="0" stop-color="#a7a6a6"></stop><stop offset="1" stop-color="#dcdbda"></stop></linearGradient></defs><path fill="url(#x)" d="M403 297c0-1 1-2 1-4h0c1-1 1 0 2 0v2c-1 2-1 5-1 7 0 13 1 25 5 36 4 10 9 18 15 27 3 3 6 7 8 11 6 5 15 22 16 29l3 12 1 11c0 2 0 5 1 6v2h1c0-1 0-2 1-4h5v-1 1c0 4-2 8-2 11v2 1c-1 1-2 2-2 3s-1 2-2 3l-26-37c-13-20-26-43-28-67-1-8-1-16 0-25 0-8 0-18 2-26z"></path><path d="M433 376c6 5 15 22 16 29l-1 1-1-2c-3-2-5-5-8-8l-2-2v-1l-1-1c-2-2-2-5-1-7l1-5-3-3v-1z" class="G"></path><path d="M415 365c8 7 12 18 16 27 5 10 10 19 14 29-3-3-6-7-9-11-10-14-18-28-21-45z"></path><path d="M326 491c0-2-1-4-1-5l-3-8c-3-15-3-31 2-46 2-9 7-20 15-25 4-3 8-5 12-7 14-7 32-21 39-36 3-6 4-14 6-21 0 12-3 25-7 36v1c-2 3-3 7-5 10-6 11-13 20-21 29-10 9-18 21-24 33-2 4-5 10-5 15-3 6-5 17-4 23v1 1c1 1 1 2 1 4-2-2-3-4-5-5h0z" class="D"></path><path d="M338 428h1l3 2c1 1 1 3 0 4-1 3-4 7-6 10-3 6-5 12-7 19v3h-1v-2c-1-12 2-27 10-36zm42-45c-2 6-7 11-10 16-4 6-8 13-13 19-4 5-12 4-18 4h-1v-1c7-6 14-11 20-16 8-7 15-14 22-22z"></path><path d="M632 717c-2 5-4 10-5 15-1 3-2 6-2 9-7 1-14 2-21 4-17 4-33 11-49 19-6 4-14 9-20 14 6-2 11-6 17-9 14-5 35-8 49-2l5 3 4 2h-1 0c-3-1-5-1-7-1-8-1-16-1-24 0-22 5-40 16-56 31-6 5-11 11-15 18h0-2c-3 6-6 11-9 18-5 12-6 29-2 42s13 27 25 33c9 5 20 5 29 2 7-2 13-5 17-11 2-2 3-3 4-5 1-1 1-3 3-4-3 8-8 16-15 20-9 6-22 8-32 5h-1c-8-2-14-6-19-11-10-9-14-25-17-38-1-5-2-10-2-16 0-1 0-3 1-5h0l2-9c0 2 0 4 1 6 10-45 43-76 81-99l19-10c4-2 9-4 12-7l1-1 3 9c5-2 11-3 15-6 0-1 1-1 1-2 0-2-2-4-3-6h0c3-3 9-6 13-8z" class="L"></path><path d="M487 850l2-9c0 2 0 4 1 6 0 5 0 10-1 15-1-4-2-8-2-12z" class="I"></path><path d="M549 778c14-7 32-11 47-10 2 1 5 1 7 1 1 1 2 1 3 1h0l4 2h-1 0c-3-1-5-1-7-1-8-1-16-1-24 0h-2c-3 0-6 1-8 1h-2l-16 6h-1z" class="K"></path><path d="M549 778h1l16-6h2c2 0 5-1 8-1h2c-22 5-40 16-56 31-6 5-11 11-15 18h0-2c2-4 4-8 7-11 9-14 22-25 37-31z" class="C"></path><path d="M342 225c0 5-1 9-1 14 1 1 0 3 1 4 1 2 1 4 1 6 0-1 1-1 1-2h0v8c-1 1-1 2-1 3 0 2 0 1 1 2l4-2c0 2 1 2 0 4l-1 1v1c5 0 9-1 13-2-11 12-19 22-19 38v6l1 3c0-1 1-2 1-3 1 11 6 19 14 26l4 3c-7-9-12-18-11-30 0-8 4-16 10-22 7-6 14-7 23-8-3 1-7 3-9 4-7 4-12 13-15 21s-3 20 1 28c2 4 6 8 10 9h4c2-1 3-5 5-7 0 2-2 5-4 7h1 2l-1 1c-6 2-12 3-19 3-10-1-19-8-25-15-13-15-19-38-18-58h1l2 2v-1c2-11 8-21 12-32l3 13c2-9 4-17 9-25z" class="D"></path><path d="M342 243c1 2 1 4 1 6 0-1 1-1 1-2h0v8c-1 1-1 2-1 3-1-1 0-3-1-5v-10z" class="C"></path><path d="M334 261h1l5 5s2 1 2 2l-1 1c-1 5-3 10-5 16-1 6-2 13-1 20 1 6 3 11 6 17-2-1-3-3-4-4-8-14-12-33-8-50 1-3 2-5 5-7z"></path><path d="M460 698c-18-1-37-10-51-21 12 5 24 9 37 9 24 0 47-10 64-26 10-10 18-24 19-38 0-13-6-24-14-33-2-2-5-3-7-5l2 1c13 6 24 15 29 29 0 1 1 1 1 1 1 1 2 6 2 7l1 6c0 2 0 4-1 6v4c-1 16-8 30-20 41-7 6-15 10-23 14h0c-3 3-7 3-10 4v1c-3 1-6 2-9 2-3-1-5-1-8-2h-12z" class="D"></path><path d="M539 614c0 1 1 1 1 1 1 1 2 6 2 7l1 6c0 2 0 4-1 6v4c0-8-1-16-3-24z" class="C"></path><path d="M472 698c10 0 18-2 27-5-3 3-7 3-10 4v1c-3 1-6 2-9 2-3-1-5-1-8-2z" class="K"></path><path d="M523 651c3 3 5 7 6 11v1h-4l-8-1c3-4 5-7 6-11zm10-19l1 1c1 4 2 9 3 14h-7-4c3-5 5-9 7-15z" class="J"></path><path d="M510 665h1c0 1 1 1 1 2 2 4-3 9-4 13 0 2 0 2-1 2l-2-1-6-6c4-3 8-6 11-10zm-24 18c2 3 5 4 7 6-4 2-10 2-14 3-9 0-18 1-27 0-3 0-6 0-9-2 16 1 29 0 43-7z"></path><path d="M606 636h0c5-4 7-11 9-17 7-16 14-33 17-51l3-17c0-3 1-7 1-10 0 8 1 16 3 24 1 6 5 14 4 20v1 1c13-17 18-38 16-59 0-7-2-15-1-23 1 10 6 18 9 27 6 19 9 42 2 61-3 8-6 14-11 21-6 8-17 17-26 22h-9-17z" class="B"></path><path d="M636 580h1c1 2 1 2 1 4 1 11-10 22-17 30l-1 1h0l8-15c2-6 4-15 8-20zm28-9c1 2 0 7 0 10-1 5-2 10-5 15-7 10-21 15-30 22h-1c1-2 4-5 6-7l12-13c7-9 12-18 18-27z"></path><defs><linearGradient id="y" x1="748.096" y1="324.529" x2="704.401" y2="295.518" xlink:href="#B"><stop offset="0" stop-color="#9a9999"></stop><stop offset="1" stop-color="#eae9e6"></stop></linearGradient></defs><path fill="url(#y)" d="M777 239c0 4-3 9-5 12-5 8-10 16-15 23l-19 23c-4 4-8 8-11 13-8 10-15 24-16 37 1-1 3-6 3-8 5-7 9-14 15-21 3-4 8-9 12-13l12-12h1c1 0 2 0 4-1h0l-8 13c-4 5-7 10-11 15h0c-7 10-14 20-20 31 5-2 9-4 13-8l2-1h0c3-1 5-2 8-4-9 13-23 21-33 33-4 5-7 11-11 17-1 2-3 7-5 9l11-50c3-18 6-36 10-53 2-7 4-15 7-21 2-2 2-4 3-7h2c0 1 1 1 2 2h2v1l1-3v-2h0l2-2c-2 6-3 12-3 18l4-11h0l9-19v3c1 0 2 0 3-1 2-2 4-2 6-3l4-2h2 0 0v1 1c0 1-1 1-1 3 2 0 3 0 4-1h1c6-3 11-8 15-12z"></path><path d="M753 293h1c1 0 2 0 4-1h0l-8 13c-4 5-7 10-11 15h0v-1c0-1 0-1 1-2l1-1 8-12 2-3 1-1-3-1c-2 0-3 3-5 4l-8 9c-3 2-4 5-7 6h0c3-4 8-9 12-13l12-12z" class="B"></path><path d="M750 259c2 4 5 7 5 11 0 3-2 6-4 8-7 10-16 18-25 26-1 1-2 2-3 2 0-1 1-2 2-3l9-12c7-10 12-21 16-32z"></path><defs><linearGradient id="z" x1="838.187" y1="248.703" x2="736.226" y2="113.888" xlink:href="#B"><stop offset="0" stop-color="#cbcaca"></stop><stop offset="1" stop-color="#fffffe"></stop></linearGradient></defs><path fill="url(#z)" d="M717 121h1 0c4 1 10-1 14 1h41 10c2 0 4 0 5-1h25c7 8 10 19 12 29s3 20 2 31c-1 15-6 30-12 45l-4 11c-1 3-2 6-3 10-4 13-10 27-15 40-2 4-3 9-6 13 0-4 3-9 4-13 6-19 14-38 16-57 3-21-3-43-15-60-12-19-30-34-51-42-8-3-16-5-24-7z"></path><path d="M812 171c2 0 3 0 4 1 1 0 2 1 3 3 2 4 1 9 1 13 0 12-3 26-8 38l-1 1h-1c3-13 3-26 1-39-1-2-5-13-4-14v-1c2-1 3-1 5-2zm-23-47h2c1 0 2 1 3 2 11 9 18 28 20 42l-3-6c-4-8-11-16-19-20-3-1-7-3-10-5v-3c1-4 4-7 7-10z"></path><path d="M183 121c3-2 9-1 12-1h26c5 0 10 1 15 0 2-1 6 0 8 0 1-1 1 0 2 0v1c8 9 15 19 19 31 2 3 3 7 4 10 1 4 2 8 3 13l1 10c0 2-1 4 0 5l2-6c1 1 1 2 1 4-1 8-4 15-6 23-2 10-4 21-8 30 0 1-1 1-1 2v1c-1 1-1 2-1 3-1-2-1-3-1-4 1-5 0-9 0-13 0-2 1-4 0-5-2-28-21-53-44-66-4-2-7-4-10-5-2-1-6-2-7-3v-1l1-1s1-1 1-2c1-1 0-1 0-2-2-4-8-6-11-7-5-1-12 0-16 2-6 4-8 10-10 16-2-1-4-3-5-6 1-6 5-12 10-17 9-6 18-5 28-4-3-2-6-3-9-5-2-1-4-1-5-3h1z" class="D"></path><defs><linearGradient id="AA" x1="268.871" y1="164.386" x2="262.63" y2="165.376" xlink:href="#B"><stop offset="0" stop-color="#747475"></stop><stop offset="1" stop-color="#aeadac"></stop></linearGradient></defs><path fill="url(#AA)" d="M260 147c0-2 0-2-1-4h2 0c0 1 0 2 1 2l3 8c0 1 0 2 1 2 1 2 1 6 3 7 1 4 2 8 3 13l1 10c0 2-1 4 0 5 0 2-1 4-2 5-1-5-1-10-2-15-1-12-5-22-9-33z"></path><defs><linearGradient id="AB" x1="253.562" y1="141.482" x2="254.938" y2="127.018" xlink:href="#B"><stop offset="0" stop-color="#a6a3a7"></stop><stop offset="1" stop-color="#c7c7c3"></stop></linearGradient></defs><path fill="url(#AB)" d="M183 121c3-2 9-1 12-1h26c5 0 10 1 15 0 2-1 6 0 8 0 1-1 1 0 2 0v1c8 9 15 19 19 31 2 3 3 7 4 10-2-1-2-5-3-7-1 0-1-1-1-2-1-2-2-5-3-8-1 0-1-1-1-2h0-2c1 2 1 2 1 4v-1c-3-7-7-13-12-19-1-1-3-4-4-5h-2-8-37c-3 0-8 0-11-1h-3z"></path><path d="M257 174h3c2 1 3 2 4 4 2 4 2 9 3 13 1 11 0 24-3 33-1 2-1 3-2 3v-3c1-11 1-22-4-32-3-5-6-9-10-13 3-3 6-4 9-5zm-39-43h0c3-1 4 0 6 1 10 7 18 18 23 29 1 3 3 6 4 10h-1l-2-2c-6-10-15-18-26-22-2-1-7-3-9-4v-3c0-4 2-7 5-9z"></path><defs><linearGradient id="AC" x1="484.165" y1="548.479" x2="395.943" y2="524.741" xlink:href="#B"><stop offset="0" stop-color="#bebdbd"></stop><stop offset="1" stop-color="#efefeb"></stop></linearGradient></defs><path fill="url(#AC)" d="M458 394c5 10 9 21 13 31 1 6 3 12 4 18l3 7c-1-6-3-14-2-19 0-2 2-4 3-5v1c-1 4 0 8 0 12 2 9 4 18 7 28 1 6 2 12 2 19 1 25-9 47-28 63-6 6-13 11-20 17-2 2-5 4-7 6 0 1-1 2-2 3-1 3-3 6-5 9l-8 15 1 1 8-3c-2 10-4 20 2 29 3 6 8 9 13 12l5 1c4 1 8 0 12 0 1 0 1 0 3-1 1 0 4 0 5 1-7 5-15 7-24 6h-2c-6-1-11-3-16-6 3 2 5 5 9 7l1 1c1 1 2 2 3 2-3 2-7 4-10 6h-1l-1 1c-6-3-10-6-15-10 7 11 16 16 28 19h-3c-3 1-6 2-8 2-5 1-9 0-13 0-8-5-15-13-19-22l-1-2c-9-24 2-53 21-69 6-5 13-8 19-13 17-10 32-22 40-41s7-39 2-59c-2-9-4-18-8-27l-1-2h0c-1 2-2 5-2 8-3 16-7 26-20 35-7 6-17 9-26 7-4 0-7-3-9-6l-2-3c2-3 4-8 8-9 2 0 5 0 6 2 2 2 3 4 3 6 13-2 22-10 29-20 1-1 2-2 2-3s1-2 2-3v-1-2l3-10c1-3 1-6 1-9 1-9-2-21-6-29l1-1z"></path><path d="M442 638l5 1c-2 1-3 3-5 2-1 0-2 0-2-1 1 0 1-1 2-2z" class="L"></path><defs><linearGradient id="AD" x1="420.608" y1="583.33" x2="419.914" y2="599.171" xlink:href="#B"><stop offset="0" stop-color="#686867"></stop><stop offset="1" stop-color="#807e80"></stop></linearGradient></defs><path fill="url(#AD)" d="M419 600h-3-1c3-9 9-20 16-25-1 3-3 6-5 9l-8 15 1 1z"></path><path d="M458 394c5 10 9 21 13 31 1 6 3 12 4 18-1-1-1-2-2-4h0c0-2 0-4-1-6v-1l-1-1c0-2-1-4-1-6l-1-2-1-1c0-2-1-4-2-6v-1-1l-2 1c1 4 1 9 0 13v2c0 2-1 2-2 3 1-3 1-6 1-9 1-9-2-21-6-29l1-1z" class="B"></path><defs><linearGradient id="AE" x1="419.342" y1="665.931" x2="417.72" y2="654.986" xlink:href="#B"><stop offset="0" stop-color="#747273"></stop><stop offset="1" stop-color="#a5a4a3"></stop></linearGradient></defs><path fill="url(#AE)" d="M396 645l1 1c1 1 2 3 3 4 0 2 2 3 3 5l2 2c1 0 2 0 3-1h7 2c-1-2-3-4-5-6-1-1-1-1-1-3 1-1 1-1 2-1v1c1 1 2 1 2 1l1 1 2 1v1l6 3c1 0 2 1 3 1l-1 1c-6-3-10-6-15-10 7 11 16 16 28 19h-3c-3 1-6 2-8 2-5 1-9 0-13 0-8-5-15-13-19-22z"></path><path d="M405 607c6 10 11 20 18 29l-5 1-18 2 5-32z"></path><path d="M358 167v2c-2 2-5 4-7 6l-27 27c0 1 0 2 1 4v-1h2l-3 2 1 1 13-6v1c-3 5-8 9-12 14l-23 24c-21 23-33 57-27 88 0 3 1 5 2 8 2 13 10 27 20 36 11 11 26 18 42 18 8-1 20-4 26-10-8 4-16 6-25 4-10-1-19-7-25-15 7 6 16 11 26 11 10 1 18-1 26-8h0l-6 1c-3-1-5-3-8-5 5 1 10 1 15-2 15-8 23-39 25-55 1-10 1-22 4-33 1-5 5-13 10-16-1 3-3 5-4 7-2 4-4 8-4 12-2 8-2 17-2 25-1 12-3 24-5 35-3 13-6 26-16 35-9 10-23 17-37 17-18 0-34-7-47-20-20-19-29-48-30-75 0-30 7-61 26-86 6-9 14-16 22-25l-1 13c2-2 4-5 6-7 4-4 9-7 13-10l12-8c5-4 11-7 17-9z" class="D"></path><path d="M324 202c0 1 0 2 1 4v-1h2l-3 2 1 1c-5 2-8 4-12 7 3-5 7-9 11-13z" class="C"></path><path d="M302 215c5 3 9 7 10 12-1 4-7 7-11 9-2 1-5 2-7 4-4 3-7 7-10 11l-5 8c4-17 13-30 23-44z"></path><path d="M486 855c-1-4-1-8-1-12 0-9 0-18 1-26 2-12 5-23 9-33 7-17 19-31 32-43l23-18c11-9 22-18 31-29 4-6 10-14 12-22 1-4 0-6-2-10l8-6c0 1 0 1 1 2 6 8 6 16 5 26 3-4 6-9 8-13l15-21c5-7 11-13 18-19-1 2-2 4-2 7v1c1 0 3 2 4 3-1 23-13 41-30 56-2 2-11 8-11 11l17-9 1-1c1-1 3-2 5-3 4-3 9-6 14-7l-10 7c-12 10-25 21-39 29-6 4-13 7-20 10l-24 13-1 1c-3 2-6 4-10 6-9 7-19 15-26 24-9 11-16 24-20 38-2 5-3 11-4 17-1 2-1 5-1 7l-2 9h0c-1 2-1 4-1 5z" class="D"></path><path d="M487 839v1l1-1v-1c1-1 1-2 2-4-1 2-1 5-1 7l-2 9h0c0-4-1-8 0-11z" class="K"></path><path d="M583 697h1c2 1 4 3 6 6v3c-1 3-4 5-6 7-11 10-24 15-37 21l-8 3c4-3 9-5 13-8 12-9 23-19 31-32z"></path><defs><linearGradient id="AF" x1="521.143" y1="795.162" x2="505.303" y2="782.5" xlink:href="#B"><stop offset="0" stop-color="#484849"></stop><stop offset="1" stop-color="#605f5f"></stop></linearGradient></defs><path fill="url(#AF)" d="M487 839c0-13 3-27 8-39 8-19 21-31 37-43 7-5 13-9 21-13l1 1-3 2v1l-1 1c-3 2-6 4-10 6-9 7-19 15-26 24-9 11-16 24-20 38-2 5-3 11-4 17-1 2-1 3-2 4v1l-1 1v-1z"></path><path d="M607 709c-6 4-14 6-21 9l22-17c19-17 34-37 36-62 1 0 3 2 4 3-1 23-13 41-30 56-2 2-11 8-11 11z" class="C"></path><defs><linearGradient id="AG" x1="576.487" y1="721.581" x2="581.119" y2="731.138" xlink:href="#B"><stop offset="0" stop-color="#444"></stop><stop offset="1" stop-color="#5f5f5f"></stop></linearGradient></defs><path fill="url(#AG)" d="M624 700l1-1c1-1 3-2 5-3 4-3 9-6 14-7l-10 7c-12 10-25 21-39 29-6 4-13 7-20 10l-24 13v-1l3-2-1-1c1 0 1 0 1-1-3 1-7 3-10 4-11 6-23 14-33 22 1-3 5-6 7-8 14-16 34-24 53-31 9-4 18-6 27-11 4-3 25-15 26-19z"></path><defs><linearGradient id="AH" x1="725.289" y1="493.056" x2="671.465" y2="486.884" xlink:href="#B"><stop offset="0" stop-color="#aeadad"></stop><stop offset="1" stop-color="#e1e0de"></stop></linearGradient></defs><path fill="url(#AH)" d="M737 381c1 0 2 1 3 2 0 9 0 16-5 24-6 11-12 22-17 34-2 3-3 6-4 9h1l1-1h0c5-5 10-11 16-15-1 6-6 13-9 17l-9 14c-4 7-7 15-10 22 1 3 0 4 0 7h1c0 2 0 3 1 3l-2 4c1 0 1 0 2-1l4-6 1 1h0l-1 1v1l-1 1v3c-3 7-17 30-14 36 0-1 1-1 1-2l3-8 3-4c1 1 2 1 3 2l5-7-19 45-8 21c-2 6-5 11-8 16-2 4-4 7-7 10 2-3 4-7 6-11 7-17 7-35 5-53l-5-28c-4-24-3-49 5-73 6-18 17-32 30-45 5-5 11-11 17-14 3-2 8-4 11-5z"></path><path d="M702 523c1 1 2 1 3 2l-3 5-3-3 3-4z" class="J"></path><path d="M700 503l2 2-6 9c0-3 1-6 2-9l2-2z" class="K"></path><path d="M699 527l3 3-8 15v-5c0-1 0-2 1-3 0-1 1-1 1-2l3-8z" class="F"></path><path d="M698 505c1-6 3-12 6-18 1 3 0 4 0 7h1c0 2 0 3 1 3l-2 4-2 4-2-2-2 2z" class="H"></path><path d="M704 494h1c0 2 0 3 1 3l-2 4-2 4-2-2 4-9z" class="F"></path><path d="M737 381c1 0 2 1 3 2 0 9 0 16-5 24 0-1 0-2 1-3h0l-1-2c-2-4-1-9-2-13l-1-1c0-1-1-2-1-3-2 1-3 1-4 1h-1c3-2 8-4 11-5z" class="G"></path><path d="M717 402l2-1c4 2 6 6 8 10-4 2-8 4-12 7-5 4-9 9-13 15l-4 7v-4c2-12 9-27 19-34zm-19 48c2-1 3-1 5-1 3 1 5 4 6 6-9 9-15 20-19 33l-2 8c-3-11-2-26 1-36 2-5 4-8 9-10zm-21 43l5 5c11 18 7 46 3 65 0 1 0 2-1 2v-20c-2-17-8-35-7-52z"></path></svg>