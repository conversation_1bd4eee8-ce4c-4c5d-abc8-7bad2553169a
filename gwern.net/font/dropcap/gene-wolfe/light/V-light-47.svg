<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="113 120 794 796"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#595a5a}.C{fill:#272727}.D{fill:#6b6b6b}.E{fill:#353536}.F{fill:#3f3f3f}.G{fill:#4f5050}.H{fill:#8c0d0f}.I{fill:#282828}.J{fill:#444445}.K{fill:#870d0b}.L{fill:#626262}.M{fill:#d0c9c8}.N{fill:#cdcccd}.O{fill:#d21715}.P{fill:#d41411}.Q{fill:#570a0a}.R{fill:#7e7e7f}.S{fill:#3a3b3b}.T{fill:#c31414}.U{fill:#b0100f}.V{fill:#8b8b8c}.W{fill:#b4b3b4}.X{fill:#de221e}.Y{fill:#dfdedf}.Z{fill:#848485}.a{fill:#f5f2f0}.b{fill:#5f0808}.c{fill:#fff}.d{fill:#494949}.e{fill:#a0a1a1}.f{fill:#ac0e0e}.g{fill:#bd1010}.h{fill:#222223}.i{fill:#757475}.j{fill:#680a0b}.k{fill:#810d0e}.l{fill:#400808}.m{fill:#989899}.n{fill:#0c0c0c}.o{fill:#9d0d10}.p{fill:#171717}.q{fill:#330908}.r{fill:#939394}.s{fill:#797979}.t{fill:#e94139}.u{fill:#e72e2b}</style><path d="M757 603h2l1 1v2l-2 1c-1 0-1 0-2-1v-1l1-2z" class="t"></path><path d="M472 241h1v1c-1 1-2 3-2 4-1 0-3 1-4 2h-1v1-1c1-3 3-6 6-7z" class="X"></path><path d="M705 523c1 1 3 2 4 4v7l-1 3-2-10-1-4z" class="a"></path><path d="M464 421v-1c2-4 2-9 3-13 1 4 0 10 1 14h1-5z" class="T"></path><path d="M693 376c2 2 4 3 6 5-2 1-2 1-3 3l-5-3c1-2 1-3 2-5z" class="O"></path><path d="M330 726l2-1-3 16c-2-5-1-10 1-15z" class="j"></path><path d="M322 572c1 1 1 1 2 1s2-1 3-2v5l-1 5h-3l-1-9z" class="K"></path><path d="M482 342h4c1 0 2 1 3 3l-2 2h-6c-1-2 0-2 0-4l1-1z" class="u"></path><path d="M433 363l5-2c0 1 1 3 1 4-1 2-2 4-2 6h-1l-3-8z" class="U"></path><path d="M336 458c-3-2-5-5-8-7s-6-3-9-4-6-3-9-4h5c9 2 16 7 21 15h0z" class="X"></path><path d="M444 830l7 7c-1 0-3-2-4-2-1-1-2 0-3 0-3 2-4 4-5 7l1 8h-1v-4c-1-7 0-10 5-16z" class="P"></path><path d="M462 805l1-1c1 0 3 0 5 1 2 0 5 1 8 2l-1 1h-1c-2 2-1 3-3 5-2-1-2-3-3-4-2-2-3-2-5-3l-1-1z" class="T"></path><path d="M763 520l5-3 6 3v1c-1 2-2 3-4 4h-4l-1-1 2-2h0l1-1-1-1h-1c-1 1-2 0-3 0z" class="P"></path><path d="M714 583c1 5 0 10 1 16v1 2c1 2 1 4 0 6h1v3h0c0 2 0 3-1 4v1c-1-2-1-3 0-4v-3c-2 2-1 5-1 7 0 1-1 2-2 2l2-35z" class="a"></path><path d="M438 361v-1c3-2 4-5 6-8h-1c-2-3-3-5-4-8v-1c2 3 3 6 6 7 1 1 3 0 4 0l-3 3c-3 4-4 8-7 12 0-1-1-3-1-4zm-102 97c1 2 3 4 3 6 2 5 1 11 4 15l-2 1c1 1 1 1 1 2l1 1v1l-3-1c-1-2-2-4-2-7-1-6 1-11-2-18h0z" class="P"></path><path d="M328 778c-2-7-3-16-2-23 0 1 0 1 1 2 0-2 1-3 1-4l2 33v-1c0-1 0-1-1-2-1-2-1-3-1-5z" class="b"></path><path d="M496 418h14 24c-1 0-2 0-2 1-4 2-9 1-13 1-2 0-4 0-6 1-1 1-2 1-3 0h2c1-1 1-1 2-1h1l1-2h-1c-6 1-12 1-18 1l-1-1z" class="O"></path><path d="M573 810c1-1 2-1 3-2v-1h2l-1 1c-4 5-5 11-7 17v1c-3 10-2 20-4 30-2-7 0-15 0-21 1-1 1-2 2-4l5-21z" class="P"></path><path d="M436 798c1 0 1 0 2 1l1-1c1-1 2-1 3-1s2 0 4 1c0 1 1 1 1 3l-3-1-3 3c-1 7 3 16 4 23h-1c-2-9-4-19-8-27v-1z" class="U"></path><path d="M695 694c-1 3-2 5-2 8 0 1-1 3-1 5-2 4-5 8-7 13-2 3-4 8-7 10l-2 2 5-9c2-1 12-25 14-29z" class="a"></path><path d="M351 722l4 3c-1 3-1 6-1 9-1 9-2 18-1 27 1 5 2 10 2 15-3-8-3-17-3-26l-1-28z" class="P"></path><path d="M599 386c1 0 3 1 4 1 2 1 4 1 6 2l-15 4-2-2c-1-1-1-1-2-1-1-1-3 0-5 0l-4 4 2-6c2 1 2 1 5 1 5-1 7 0 11-3z" class="U"></path><path d="M300 756h1c-1 2-3 5-2 7 0 5-2 9 0 13 0 2 1 2 2 3 1 0 1-1 2-1 2-1 3-3 3-5l1-2v2c0 3-2 7-4 9-1 1-2 1-4 0s-4-4-4-6c-2-6 2-15 5-20z" class="P"></path><path d="M118 181h7 13 54c7 0 14 1 21 0h9v1h-6c2 2 5 4 8 7v1l-9-3v-1c-1-1-2-1-3-2-2-1-5-1-7-2h0l-5-1h-7-34-41z" class="e"></path><path d="M582 802l2 2-6 3h-2v1c-1 1-2 1-3 2-1-1-1-2-1-3-4 0-8 1-11 2 2 5 3 10 4 16-1-4-2-7-4-10s-4-5-7-6h-1v-1l4-1h0l25-5z" class="f"></path><path d="M736 495c6 4 9 9 12 15 3 4 7 8 11 10h4c1 0 2 1 3 0h1l1 1-1 1h0l-2 2 1 1c1 2 2 3 3 5 1 3 1 6 0 9 0-3 0-6-2-9-1-3-5-9-9-10-1 0-3-1-4-2s-4-4-5-4-1 1-3 1c1-1 1-3 1-4-1-5-9-12-14-15l3-1zm-460-12c3 0 6 1 9 3 1 1 2 3 2 5 0 1-1 3-2 4s-6-2-8-2l1 1c1 0 2 1 3 2l-1 1-1 1-1 1c-1-2-2-5-3-7-1-1-1-2-1-3l-1-1c1-2 0-2 0-4l3-1z" class="O"></path><path d="M599 386c5-5 9-12 14-16 5-6 14-7 18-15 2-4 4-9 5-14 1-3 0-7 2-10 1 2-1 7-1 9-1 6-7 16-4 21h-1c-2 1-5 2-7 4-3 1-6 2-9 5-5 4-10 11-13 17-1 0-3-1-4-1zM330 520c-1 2-6 4-8 6-6 3-10 3-17 1 3 0 6 1 9 0 2-1 5-2 7-4 9-6 15-16 19-25 1-2 2-4 3-5l1 1v2 1h-1v1 1 3l-2 4c-1 2-3 5-5 7s-6 5-6 7h0z" class="P"></path><path d="M583 337c1 8 2 15 1 22 0 3 1 6 0 9 0 7-1 15 4 21-3 0-3 0-5-1h-1 0c-2-3-3-7-2-10v-5l1-8c0-2 0-4 1-6v-6c0-3-1-8 0-11 1-2 1-3 1-5z" class="H"></path><path d="M583 337c1 8 2 15 1 22 0 3 1 6 0 9-1-4 0-8 0-11l-1 6c0 2 0 7-1 9v-13-6c0-3-1-8 0-11 1-2 1-3 1-5z" class="f"></path><path d="M534 418c5 0 12 0 16 1-2 2-5 1-6 4v2c-1 1-2 1-3 1 0 0-1 0-1-1h-16c-2-1-3-1-5-1l1-1h-1l-2-2h-4c2-1 4-1 6-1 4 0 9 1 13-1 0-1 1-1 2-1z" class="g"></path><path d="M517 421c4-1 9 1 13 2h10l1 1-1 1h-16c-2-1-3-1-5-1l1-1h-1l-2-2zm-234 92c2 3 4 9 8 10 2 0 4-1 5-2 2-2 2-3 2-5h0c1 2 0 4 0 6l2 3h0c-1-1-2-1-3-1-3 3-4 8-5 13l-1 4c0 4 1 9 1 13-3-6-4-13-4-20-1-3-1-7-1-10-1-4-3-7-4-11z" class="P"></path><path d="M363 470c1 3 1 7 3 10v5l-3 3v-1c-1-1 0-1 0-3h-2l-1 1c-2 0-4 0-6 1h0-5c-2-1-4-1-5-2h-1v-1l-1-1c0-1 0-1-1-2l2-1c2 2 3 3 6 4l4-2c4-3 7-6 10-11z" class="U"></path><path d="M343 479c2 2 3 3 6 4l4-2 3-1c0 2 0 2-1 4h-2v1h0c1 1 1 0 1 1h0-5c-2-1-4-1-5-2h-1v-1l-1-1c0-1 0-1-1-2l2-1zm356-98v-1c6-2 10-6 16-7-3 1-5 3-8 5-6 5-11 13-13 21-2 7-3 17 1 24 2 2 5 5 5 8 0 1-1 2-1 3v-4c0-1-2-3-2-3-5-5-7-10-8-16 0-5 1-8 2-13l-1-1v-1l2-1 3-6c1-2 2-3 1-5 1-2 1-2 3-3z" class="X"></path><defs><linearGradient id="A" x1="601.741" y1="187.153" x2="599.098" y2="179.906" xlink:href="#B"><stop offset="0" stop-color="#b0afb5"></stop><stop offset="1" stop-color="#d2d2d1"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M566 194h0-2-1c3-2 6-3 9-5 17-8 35-8 53-8h5c1 1 0 2 0 3l-3-1-1 2c-17-3-35-1-51 5-3 1-6 2-9 4z"></path><path d="M625 181h5c1 1 0 2 0 3l-3-1c-2 0-4 0-5-1l3-1z" class="D"></path><path d="M496 418l1 1c6 0 12 0 18-1h1l-1 2h-1c-1 0-1 0-2 1h-2-3l-1-1h-2c-1 0-1 0-2 1h-3-1c-1 1-2 0-3 1h-1c-1 0-1-1-2-1s-1 0-2 1h-1-1-2c-1 0-1 0-2 1h-2-2v1h-2l-1 1c-2 1-2 1-4 0-1 1-2 1-3 1l-3 1h-3v1h-2c-2 1-2 1-4 0 0-1-1-2-1-3h0c3-1 5-2 7-4h5l8-1c2-1 17-1 19-2z" class="T"></path><path d="M469 421l8-1-1 1c-1 1-5 0-6 1-1 0-3 1-4 0l-1 1v2c2-1 2-1 4-1h4c1-1 2 0 3-1l-1-1h1l1 1c0 1 0 1-1 1h-3v1c-1 1-2 1-3 1l-3 1h-3v1h-2c-2 1-2 1-4 0 0-1-1-2-1-3h0c3-1 5-2 7-4h5z" class="o"></path><path d="M684 493c6 1 11-1 16-5l2-5h0 1c0 2 2 3 3 4 5 1 11 0 16 1l1 1c5 1 9 3 13 6l-3 1c-6-3-12-4-19-4-3-1-4-1-7-1-2 0-7 2-9 3h-2l-1 1-1 1c-3 1-4 0-7 2h-2c-2 0-2 1-4 0v-1c2 0 2 0 4 1l1-1-2-4z" class="P"></path><path d="M698 494c2-1 7-3 9-3 3 0 4 0 7 1l-4 1c-4 2-9 4-11 9s3 15 5 20l-1 5h-2c-1-4-2-8-3-13-2 1-3 2-4 2s-2-1-3-2c0-2 1-4 2-6v-4l-1-1-3 2 3-6c2-1 3-3 5-4 1-1 1 0 1-1z" class="l"></path><path d="M693 508l1-1c2 1 3 3 3 5l1 2c-2 1-3 2-4 2s-2-1-3-2c0-2 1-4 2-6z" class="b"></path><path d="M698 494c2-1 7-3 9-3 3 0 4 0 7 1l-4 1c-1-1-2 0-4 1-2 0-3 1-5 3h-2c-2 1-5 4-7 5v1l-3 2 3-6c2-1 3-3 5-4 1-1 1 0 1-1z" class="f"></path><path d="M477 425l1-1h2v-1h2l1 1h2c0 1-1 1-2 2-1 0-2 1-2 1h0c1 1 3 0 3 1h0-1c0 2 0 1 1 2h0 1l-2 1 1 1-5 1-3 2h-1c-4 1-9 4-13 4h0l-3-7-1-4c2 1 2 1 4 0h2v-1h3l3-1c1 0 2 0 3-1 2 1 2 1 4 0z" class="g"></path><path d="M477 425h3v1l-3 1c-3 1-16 3-17 5h-1l-1-4c2 1 2 1 4 0h2v-1h3l3-1c1 0 2 0 3-1 2 1 2 1 4 0z" class="K"></path><path d="M460 432c5 0 9-2 13-2h3l-5 5c-2 1-6 2-9 3v1h0l-3-7h1z" class="k"></path><path d="M482 423h2c1-1 1-1 2-1h2 1 1c1-1 1-1 2-1s1 1 2 1h1c1-1 2 0 3-1h1 3c1-1 1-1 2-1h2l1 1h3c1 1 2 1 3 0h4l2 2h1l-1 1c2 0 3 0 5 1-10 1-20 1-30 4l-10 3-1-1 2-1h-1 0c-1-1-1 0-1-2h1 0c0-1-2 0-3-1h0s1-1 2-1c1-1 2-1 2-2h-2l-1-1z" class="X"></path><path d="M581 394l4-4c2 0 4-1 5 0 1 0 1 0 2 1l2 2c-8 4-13 9-16 17l-2 1c-2 4-3 9-4 14h-1l-1-1c1-2 2-5 2-8-1 1-1 1-2 1l-3-2c2-2 2-5 4-7v-1h-1l1-2h0c1-3 3-4 5-6l5-5z" class="b"></path><path d="M581 394l4-4c2 0 4-1 5 0 1 0 1 0 2 1-8 2-15 6-19 13l-2 3h-1l1-2h0c1-3 3-4 5-6l5-5z" class="O"></path><path d="M575 407l2-2h1c0 2-1 4-2 6-2 4-3 9-4 14h-1l-1-1c1-2 2-5 2-8-1 1-1 1-2 1l-3-2c2-2 2-5 4-7l1-1h1v-1h1l1 1z" class="H"></path><path d="M571 408l1-1h1v-1h1l1 1-3 9c-1 1-1 1-2 1l-3-2c2-2 2-5 4-7z" class="Q"></path><path d="M679 720v1l1-1c0-1 0-2 2-3v3c-1 1-1 1-1 2v1l-5 9c-9 14-18 26-31 36-3 3-6 6-10 8v-1c-2 2-4 2-5 4l1-3c2-1 3-3 4-5h0l16-13c6-5 11-12 16-19l7-11 5-8z" class="K"></path><path d="M418 328c6-10 9-21 14-31 1-4 4-8 6-11 1-3 3-7 5-10 4-3 8-5 11-9 0 2-2 3-3 4-2 3-5 5-7 8-8 11-13 25-16 39-1 3-3 7-2 11 0 1 1 1 2 2 4 2 10 1 13 5-4-1-7-4-12-1-1 1-2 2-3 4 0 3 1 7 1 10-3-4-4-11-6-15-1-3-1-4-3-6z" class="U"></path><defs><linearGradient id="C" x1="655.038" y1="461.057" x2="676.344" y2="463.842" xlink:href="#B"><stop offset="0" stop-color="#8c0c0d"></stop><stop offset="1" stop-color="#bf1617"></stop></linearGradient></defs><path fill="url(#C)" d="M653 471l12-31c0 5-1 11 1 15 1 1 2 2 4 1 5 0 12-3 16-7 2-1 4-4 6-5-5 7-14 12-21 17-2 1-5 2-7 4s-2 4-2 7l2 3c-1 1-1 1-1 2s1 2 1 3v2c0 1 0 1 1 2h-1l-2-1-1-1-2-4c-2-3-4-5-6-7z"></path><path d="M550 419h0l2 2-2 3-6 8-3 3-1-1c-3-2-7-3-11-3-11-1-22 0-33 3-3 1-7 3-10 4v-2c1 0 2 0 3-1-1 0-1 0-2-1-1 1-1 0-1 1h-1l-2 1-1-2h0c1-1 3-1 5-2 1 0 0 0 1 1 1-1 2-2 4-2h1l1-2c10-3 20-3 30-4h16c0 1 1 1 1 1 1 0 2 0 3-1v-2c1-3 4-2 6-4z" class="Q"></path><path d="M421 182l23 1c11 1 21 3 31 7l-1 1h0c-2 0-3 0-4-1-15-4-33-2-48 3l-1-2-19 7-1-1 1-1c1 0 1-1 2-2 2 0 1 1 2 0s1-2 3-3l2-1 2-2v-1l2-2h1l2-1c1-1 2-2 3-2z" class="h"></path><path d="M421 182l23 1c-3 2-10 1-14 1h-6l-1-1c-3 1-6 4-10 4l2-2h1l2-1c1-1 2-2 3-2z" class="C"></path><path d="M421 191c16-5 30-5 47-3l2 2c-15-4-33-2-48 3l-1-2z" class="Y"></path><path d="M632 772l-2 2h1c1-1 3-2 4-3h0c-1 2-2 4-4 5l-1 3c1-2 3-2 5-4v1c-2 3-6 5-9 7-9 5-18 10-28 15-2 1-6 3-9 4-1 1-3 2-5 2l-2-2-25 5h0l-4 1v1c-4 0-8 0-12 1 0-2 0-3 1-5h7 7c20-3 39-10 56-20 7-4 14-8 20-13z" class="O"></path><path d="M582 802h1c3-1 4-1 6 0-1 1-3 2-5 2l-2-2zm-33 3h7 3 4s-1 0-2 1h0-1-2-1l-1 1h1 0l-4 1v1c-4 0-8 0-12 1 0-2 0-3 1-5h7z" class="g"></path><path d="M329 715c2-3 4-6 6-7h1l2-1h2c4-1 7 0 11 3 2 2 3 3 4 6v1l-1 1-1-1c-1-2-2-3-3-4l-1-1c-2-1-4-1-7-1 1 0 2 1 3 2-4 1-6 2-9 5-1 2-3 5-4 7l-2 1c-2 5-3 10-1 15l-1 12c0 1-1 2-1 4-1-1-1-1-1-2-1 7 0 16 2 23 0 2 0 3-2 5l-3-24c0-8-1-17 1-26l3-14c1-1 2-3 2-4z" class="l"></path><path d="M330 726c0-2 0-4 1-6s3-5 4-7c2-1 4-2 7-2 1 0 2 1 3 2-4 1-6 2-9 5-1 2-3 5-4 7l-2 1z" class="Q"></path><path d="M222 181h137 37c6 0 14-1 20 0-7 1-15 0-22 1h-17c-2 0-5-1-7 1-3 2-6 6-9 7h-1l7-6c1 0 2-1 3-2h-22c-2 0-6-1-8 0-1 0-3 3-5 4v-1c-5 3-10 6-16 8-1 0-1-1-2-2 0-2-2-2-4-3l1-2-4-1h0l1-1-1-1c-1 1-2 0-3 0l-5 1h5c-1 1 0 1-1 1h-2l-1 1h1l1 1c-4-1-9-1-12-2s-5-1-8-1h-2-5c-3 0-8 2-11 0h-1c-4 2-10 0-15 1l1 1-1 1c-1-1-2-1-3-3 0 0-1 0-1 1l-1-1v-2h3-27v-1z" class="L"></path><path d="M310 183c-1-1-2-1-3-1h32l-4 3c-5 3-10 6-16 8-1 0-1-1-2-2 0-2-2-2-4-3l1-2-4-1h0l1-1-1-1z" class="h"></path><path d="M416 181l5 1c-1 0-2 1-3 2l-2 1h-1l-2 2v1l-2 2-2 1c-2 1-2 2-3 3s0 0-2 0c-1 1-1 2-2 2l-1 1 1 1-22 13-1-1v1h-4c0-3 1-3 2-4 1-3 5-3 6-5-1-1-1-1-2-1h0c0-1 0-1 1-2l-1-1c-1 0-2 0-3 1-1-1-1-2-2-3h0c3-1 12-9 15-11l3-3c7-1 15 0 22-1z" class="I"></path><path d="M574 426h1c4 0 7 2 11 2 11 3 22 7 33 13 2 1 4 2 6 4h2 0c0 2 0 4 1 5-2 2-3 5-6 7 1 0 1 1 1 1v1l-1 3c-1 1 0 1-1 1-1-3-2-6-4-8l-3-3h-1c-3-2-7-5-10-7-3-1-5-3-9-4s-9-3-14-5h-3-3l-1-2v-1c0-2 0-6 1-7z" class="U"></path><path d="M574 426h1c4 0 7 2 11 2l-3 4h1v1h1s1 1 1 2v1c-3 0-6-1-9-1h0v1h-3l-1-2v-1c0-2 0-6 1-7z" class="K"></path><path d="M574 426h1l-1 2 1 1c1 1 1 1 2 3v2c-2 1-3 0-4 0v-1c0-2 0-6 1-7z" class="H"></path><path d="M614 452l-3-3c-1-1-2-1-2-2l1-1 1 1v-1c1 1 2 1 2 1h0c-1-1-3-2-3-3 1 0 8 4 9 3l1-1 4 2v-1-1l1-1h2 0c0 2 0 4 1 5-2 2-3 5-6 7 1 0 1 1 1 1v1l-1 3c-1 1 0 1-1 1-1-3-2-6-4-8l-3-3z" class="K"></path><path d="M627 445c0 2 0 4 1 5-2 2-3 5-6 7 1 0 1 1 1 1v1l-1 3c-1 1 0 1-1 1-1-3-2-6-4-8 0-1-1-2-1-4l1 1c1 0 2 0 3 1s1 1 2 1c2-3 4-5 5-9z" class="p"></path><path d="M310 183l1 1-1 1h0l4 1-1 2c2 1 4 1 4 3 1 1 1 2 2 2-11 5-24 7-36 7-3 0-7 1-9 0-1-1-2-1-2-2v-1l-2-1h-7l-7-2v-1-1l-1-1h0v-1c-1-2-2-1-4-3h0l1-1-1-1c5-1 11 1 15-1h1c3 2 8 0 11 0h5 2c3 0 5 0 8 1s8 1 12 2l-1-1h-1l1-1h2c1 0 0 0 1-1h-5l5-1c1 0 2 1 3 0z" class="i"></path><path d="M277 189h2l1 2c1-1 2-1 3-1s1 1 2 1c2 0 1 0 2 1l-2 1c-2-1-5 1-8 0v-1l1-1-1-2zm5 9c-1-1-4-1-5-1l3-1h0-1l-1-1c2 0 4-1 6 0h7c-3 3-5 1-8 3h-1z" class="Z"></path><path d="M256 192h2v-1l1 1c3 1 6 1 9 3l1-1c0 1 1 1 1 2h-7l-7-2v-1-1z" class="L"></path><path d="M291 195c1-1 1-1 3-1h0 2l6 1-2 2c-5 1-12 2-17 1 3-2 5 0 8-3z" class="R"></path><path d="M305 187l-1-1h-1l1-1h2 0c1 1 1 1 2 1 2 2 2 4 4 4 1 0 1 1 2 1l-1 2 1 1c-1 1-2 1-4 1h0c-1-1-1-1-2-1h-3-3c1 0 2-1 3 0h1c1-1 1-1 3-2h2l2-1h-1-2-3 1c-1-1-2-1-3-2v-2h0z" class="B"></path><path d="M310 183l1 1-1 1h0l4 1-1 2c2 1 4 1 4 3 1 1 1 2 2 2-11 5-24 7-36 7-3 0-7 1-9 0-1-1-2-1-2-2v-1c0-1 0-2 2-3 0-1 0-1 1-1-1 2-2 3-3 5h10 1c5 1 12 0 17-1l10-2c2 0 3 0 4-1l-1-1 1-2c-1 0-1-1-2-1-2 0-2-2-4-4-1 0-1 0-2-1h0c1 0 0 0 1-1h-5l5-1c1 0 2 1 3 0z" class="F"></path><defs><linearGradient id="D" x1="346.559" y1="823.376" x2="332.365" y2="828.497" xlink:href="#B"><stop offset="0" stop-color="#b10d09"></stop><stop offset="1" stop-color="#e41412"></stop></linearGradient></defs><path fill="url(#D)" d="M320 746c1 2 1 5 2 7v3 1 2l1 1v-1l3 24c2-2 2-3 2-5 0 2 0 3 1 5 1 1 1 1 1 2v1c5 25 11 49 20 74l8 21 20 40c-2-2-4-5-6-7l-9-16c-20-33-34-71-41-109-2-12-3-24-4-36v-3l2-4z"></path><path d="M347 862c0-2-1-4 0-6 1 2 1 2 3 4l8 21h-1c-2-1-2-3-3-5-3-5-6-9-7-14z" class="U"></path><defs><linearGradient id="E" x1="327.408" y1="820.356" x2="346.586" y2="820.132" xlink:href="#B"><stop offset="0" stop-color="#570000"></stop><stop offset="1" stop-color="#8c1b19"></stop></linearGradient></defs><path fill="url(#E)" d="M328 778c0 2 0 3 1 5 1 1 1 1 1 2v1c5 25 11 49 20 74-2-2-2-2-3-4-1 2 0 4 0 6-2-6-5-11-6-17-3-9-5-18-7-27-3-11-6-23-8-35 2-2 2-3 2-5z"></path><path d="M340 483l3 1h1c1 1 3 1 5 2h5 0c2-1 4-1 6-1l1-1h2c0 2-1 2 0 3v1l-4 7c-3 4-6 9-8 13l-3 6h-1c-1 1-1 2-1 4-1 1-2 3-3 5-1 0-2 2-2 4-2 5-4 10-6 14 0 1-1 2-1 3l-1-1c-1 2-1 3-2 4l-1 5-1-1c-1 2-2 6-2 8 2 3 2 7 1 11-1 1-1 0-1 1-1 1-2 2-3 2s-1 0-2-1h-2c-2 2-7 4-10 4-4-1-8-3-11-6-2-2-5-6-5-9 3 5 6 9 11 11 3 1 6 0 8-1 7-4 9-14 11-21l7-29-1-1h0c0-2 4-5 6-7s4-5 5-7l2-4v-3-1-1h1v-1-2l-1-1v-1c0-3-1-6-3-9z" class="O"></path><path d="M343 502c2-3 3-6 6-8l-3 6c-1 2-2 6-4 7l-1-1 2-4z" class="U"></path><path d="M340 483l3 1h1c1 2 2 4 2 7-1 1-1 2-2 3l-1-1v-1c0-3-1-6-3-9z" class="H"></path><path d="M341 506l1 1-2 4c-2 5-5 8-6 12l-3-2-1-1h0c0-2 4-5 6-7s4-5 5-7z" class="j"></path><path d="M329 551l2-6c2-7 3-17 8-22h1c0 1-1 2-1 3l-2 4v1s0 1-1 2c0 2-2 6-1 8h0c0 1-1 2-1 3l-1-1c-1 2-1 3-2 4l-1 5-1-1z" class="U"></path><path d="M327 559c2 3 2 7 1 11-1 1-1 0-1 1-1 1-2 2-3 2s-1 0-2-1h-2l-1-1v-1c4-3 6-7 8-11z" class="l"></path><path d="M331 521l3 2c-1 7-4 15-6 22h0c-1 2-1 2-2 3v1c-1 0-1 0-2 1l7-29z" class="U"></path><path d="M704 522l1 1 1 4 2 10 3 16 3 30-2 35v1l-2 17c-1 10-3 20-6 30l-6 19c-1 3-3 7-3 9-2 4-12 28-14 29v-1c0-1 0-1 1-2v-3c-2 1-2 2-2 3l-1 1v-1c13-25 20-51 25-79l2-15 2-21-1-14c-2-8 0-17-6-23v-1c-2-1-4-2-6-4h0c-1 0-2-1-3-2 1-1 2-3 3-4v-1l2-4c0-1-1-1-1-1 4-8 5-16 7-24l1-5z" class="q"></path><path d="M708 578c2 5 3 11 2 16-1 2-1 3-2 5v4 2l-1-14c1-2 1-5 0-6 0-3 1-4 1-7z" class="l"></path><path d="M701 551l2 1h1v2h1c1 3 2 5 1 8v1 3h0c2 4 0 8 2 12 0 3-1 4-1 7 1 1 1 4 0 6-2-8 0-17-6-23v-1c-2-1-4-2-6-4h0 1c0-1 1-1 1-2 1-3 3-5 4-10z" class="j"></path><path d="M704 558h1v4c1 1 0 3 0 4l-1 1c-1 0-1-1-2-1 1-3 2-5 2-8h0z" class="K"></path><path d="M704 522l1 1 1 4 2 10 3 16c-1-1-1-1-1-2l-1-1v-2h0v-2-2c0-1-1-1-1-2l1-1c-1-1-1-1-1-2-1-2-1-3-1-4h-1c-3 5-2 11-5 16h-1 1c-1 5-3 7-4 10 0 1-1 1-1 2h-1c-1 0-2-1-3-2 1-1 2-3 3-4v-1l2-4c0-1-1-1-1-1 4-8 5-16 7-24l1-5z" class="Q"></path><path d="M704 522l1 1 1 4c-2 2-2 7-2 10-1 6-4 16-9 20v-1l2-4c0-1-1-1-1-1 4-8 5-16 7-24l1-5z" class="O"></path><path d="M691 381l5 3c1 2 0 3-1 5l-3 6-2 1v1l1 1c-1 5-2 8-2 13 1 6 3 11 8 16 0 0 2 2 2 3v4c-1 1-2 2-2 4h-1c0 1 0 1-1 2l-1 2c-1 1-1 2-2 2h0c-2 1-4 4-6 5-4 4-11 7-16 7-2 1-3 0-4-1-2-4-1-10-1-15 2-7 5-13 8-20 5-13 11-27 18-39z" class="a"></path><path d="M329 666l3-5c1-1 0-2 0-3h1v3l1 2 2 4 7 20c4 7 6 14 11 21 0 0-1 1-2 1l-1 1c-4-3-7-4-11-3h-2l-2 1h-1c-2 1-4 4-6 7 0 1-1 3-2 4l-3 14c-2 9-1 18-1 26v1l-1-1v-2-1-3c-1-2-1-5-2-7l-2 4v3c-1-2-1-4-1-6-2-2-3-2-4-2-6 3-8 6-12 11h-1c2-3 4-6 6-8 3-3 7-6 9-9 4-8 3-20 3-28 1-10 1-19 2-29h0c1 1 1 2 1 4h0c1-1 1-2 2-2l1-1c0-1 1-2 1-3 0 0 2-9 3-10 0-1 0-3 1-4h0z" class="T"></path><path d="M313 745c1-2 4-5 6-6 1 1 0 5 1 7l-2 4v3c-1-2-1-4-1-6-2-2-3-2-4-2zm15-75c1 3 0 7 0 10 0 2-1 4-1 6v1c-1 1-1 0-1 1s0 2-1 2h0 0l-1 2 1-12s2-9 3-10z" class="H"></path><path d="M327 687l1 4c-2 6-2 14-3 21 0 1 0 1-1 2l-2-3c2-6 1-13 2-19l1-2h0 0c1 0 1-1 1-2s0 0 1-1z" class="K"></path><path d="M329 690h1c0 2 0 4-1 6v2l-1 2v2c1 2-1 5 1 7l-1 2 1 1v3c0 1-1 3-2 4 0 1-1 1-1 2-1 1-2 4-2 5l-2 10c-1-3 0-7 0-11h0v-14l2 3c1-1 1-1 1-2 1-7 1-15 3-21l1-1z" class="U"></path><path d="M322 711l2 3c1-1 1-1 1-2v5h0c0 1 0 1-1 2-1 2 0 4-1 6h-1v-14zm7-45l3-5c1-1 0-2 0-3h1v3l1 2 2 4-1 1c1 2 1 6 1 8l-2 1v2c-1 1-2 3-2 4s1 2 0 3l-1 3-1 1h-1l-1 1-1-4v-1c0-2 1-4 1-6 0-3 1-7 0-10 0-1 0-3 1-4h0z" class="o"></path><path d="M329 690c1-4 2-8 4-12l1 1c-1 1-2 3-2 4s1 2 0 3l-1 3-1 1h-1z" class="f"></path><path d="M329 666l3-5c1-1 0-2 0-3h1v3l1 2 2 4-1 1-1 1h0v4h-1v-1-2c-1 0-1 0-2 1-1 3-1 6-3 9 0-3 1-7 0-10 0-1 0-3 1-4h0z" class="K"></path><path d="M336 667l7 20c4 7 6 14 11 21 0 0-1 1-2 1l-1 1c-4-3-7-4-11-3h-2l-2 1h-1c-2 1-4 4-6 7v-3l-1-1 1-2c-2-2 0-5-1-7v-2l1-2v-2c1-2 1-4 1-6l1-1 1-3c1-1 0-2 0-3s1-3 2-4v-2l2-1c0-2 0-6-1-8l1-1z" class="H"></path><path d="M338 697c0 2 1 4 0 6h0c-2-1-2-1-3-3h0c2-1 2-2 3-3z" class="Q"></path><path d="M346 704c-2-1-3-1-5-1-1 0-1-1-2-1 1-2 1-4 2-5h4 1l3 6 1 1-1 1c-1-1-2-1-3-1z" class="b"></path><path d="M346 704c-1-1-1-2-2-3h0v-1c2 0 3 2 5 3l1 1-1 1c-1-1-2-1-3-1z" class="Q"></path><path d="M336 667l7 20-1 1 1 1c0 1 1 2 1 3v1c1 2 1 2 1 3l-1-1c-1-1-1-2-2-2h-1v-3l-2-2v1 3h-1c0 1 0 1-1 2l1 3c-1 1-1 2-3 3h0-2c-1-4 2-10 2-14h1v-3-7c0-2 0-6-1-8l1-1z" class="b"></path><path d="M336 693l1-1-1-1c0-1 1-2 2-3l1 1v3h-1c0 1 0 1-1 2l-1-1z" class="Q"></path><path d="M336 693l1 1 1 3c-1 1-1 2-3 3l-1-1c0-1 0-1 1-2 0-2 1-3 1-4z" class="l"></path><path d="M334 679v-2l2-1v7 3h-1c0 4-3 10-2 14 1 2 2 4 4 5v-1c3-1 8 1 11 2 1 1 3 2 4 3l-1 1c-4-3-7-4-11-3h-2l-2 1h-1c-2 1-4 4-6 7v-3l-1-1 1-2c-2-2 0-5-1-7v-2l1-2v-2c1-2 1-4 1-6l1-1 1-3c1-1 0-2 0-3s1-3 2-4z" class="X"></path><path d="M334 679v-2l2-1v7 3h-1c-2 2-2 4-2 7 0 1-1 2-1 4l-3 12c-2-2 0-5-1-7v-2l1-2v-2c1-2 1-4 1-6l1-1 1-3c1-1 0-2 0-3s1-3 2-4z" class="H"></path><path d="M337 652l-1-2v-5l1-1c1 3 1 9 3 11h1l1-1 2 11c0 2 2 6 2 7 2 7 4 14 7 21l7 12c1 2 2 5 3 7 6 10 13 20 20 30 12 15 27 29 42 40 5 3 9 6 14 9 11 6 24 10 37 13l6 1 7 1 2 4-17-2h1l1-1c-3-1-6-2-8-2-2-1-4-1-5-1l-1 1c-2 0-4-1-6-1-3-1-7-2-9-3 0-2-1-2-1-3-2-1-3-1-4-1s-2 0-3 1l-1 1c-1-1-1-1-2-1-4-4-7-7-12-10-4-3-8-6-12-8l-1-1c-6-2-13 0-18 0 3-1 7-1 10-3 1 0 1 0 1-1-1-3-3-5-5-7l-9-10-13-13-4-4-9-10c-2-3-5-6-8-7l-1 1-4-3-1-5c-1-1-3-2-5-4-1-1-2-2-3-2 3 0 5 0 7 1l1 1c1 1 2 2 3 4l1 1 1-1v-1c-1-3-2-4-4-6l1-1c1 0 2-1 2-1-5-7-7-14-11-21l-7-20-2-4h2v2c2 1 2 1 3 2h0c1-2 1-1 0-2 0-3-1-5-2-8-1-1 0-2-1-3v-1l1-1z" class="O"></path><path d="M354 708l6 9 2 2c3 3 5 8 8 11 2 3 5 6 7 9 1 1 2 2 2 4 1 2 5 4 4 6l-4-4-1-1-1 1-4-4-9-10c-2-3-5-6-8-7l-1 1-4-3-1-5c-1-1-3-2-5-4-1-1-2-2-3-2 3 0 5 0 7 1l1 1c1 1 2 2 3 4l1 1 1-1v-1c-1-3-2-4-4-6l1-1c1 0 2-1 2-1z" class="K"></path><path d="M342 711c3 0 5 0 7 1l1 1c1 1 2 2 3 4l1 1v4h3v1h-2c-1-1-2-1-2-2v-2c-1-1-1-1-3-2-1-1-3-2-5-4-1-1-2-2-3-2z" class="b"></path><path d="M354 708l6 9 2 2h-1-1c-1 0-1 0-1 1v3h-1v-1l-1-2v-1l-2-2v-1c-1-3-2-4-4-6l1-1c1 0 2-1 2-1z" class="k"></path><path d="M337 652l-1-2v-5l1-1c1 3 1 9 3 11h1l1-1 2 11c0 2 2 6 2 7 2 7 4 14 7 21l7 12c1 2 2 5 3 7h0c0 2 0 3 1 4l2 3 2 2 1 2 1 1h0c0 1 0 1 1 2 0 1 1 1 1 2l2 2c1 1 1 3 2 4 1 0 1 1 1 2l1 1h0l-1-1-4-5-9-12c-1-1-1-3-3-4l-1 2-6-9c-5-7-7-14-11-21l-7-20-2-4h2v2c2 1 2 1 3 2h0c1-2 1-1 0-2 0-3-1-5-2-8-1-1 0-2-1-3v-1l1-1z" class="f"></path><path d="M337 652l-1-2v-5l1-1c1 3 1 9 3 11h1l1-1 2 11c0 2 2 6 2 7s-1 0-1 0c0-1-1-2-1-4 0-1 0-2-1-3 0-1 0-2-1-3v-2-2c-2-1-3-2-4-4h0l-1-1v-1z" class="H"></path><path d="M336 663v2c2 1 2 1 3 2 0 1 1 2 1 4l10 23c1 2 2 4 3 7 1 1 0 1 1 3s2 4 3 5l2 3c1 1 1 2 1 2l1 1-1 2-6-9c-5-7-7-14-11-21l-7-20-2-4h2z" class="T"></path><path d="M336 665c2 1 2 1 3 2 0 1 1 2 1 4l-3-2-1-4z" class="g"></path><defs><linearGradient id="F" x1="351.05" y1="188.635" x2="363.769" y2="204.143" xlink:href="#B"><stop offset="0" stop-color="#1a1a1a"></stop><stop offset="1" stop-color="#39393a"></stop></linearGradient></defs><path fill="url(#F)" d="M361 190c3-1 6-5 9-7 2-2 5-1 7-1h17l-3 3c-3 2-12 10-15 11h0c1 1 1 2 2 3 1-1 2-1 3-1l1 1c-1 1-1 1-1 2h0c1 0 1 0 2 1-1 2-5 2-6 5-1 1-2 1-2 4h4v-1l1 1c-2 2-4 3-6 4-9 5-19 7-29 9-3 1-7 1-11 2-1 0-4 1-5 0-2-1-4-2-5-3-5 0-9 1-13 2v-1-1l-4-1c1-1 2-1 4-1l1 1c1 0 2 1 3 1 1-1 1-1 2-1h1l4-3v-1h-2c-3-1-5-1-8-1v-1c1 0 1 0 2-1l2-1c0-1-1-1-1-2h-1c-3 1-7 1-10 2v-2c1-1 3-1 4-1l13-3 13-3 6-3c7-3 15-7 20-12h1z"></path><path d="M344 207l-3 3v1c3 1 7-2 9-3 1 0 2-1 3-1l6-3 2 1c-8 4-16 8-24 11-1-2-1-3 0-5 2-1 4-3 7-4z" class="L"></path><path d="M360 190h1c-11 9-25 16-39 21l-7 1h-1c-3 1-7 1-10 2v-2c1-1 3-1 4-1l13-3 13-3 6-3c7-3 15-7 20-12z" class="M"></path><path d="M325 213c8-1 16-6 23-10 4-2 7-4 10-6-4 5-9 7-14 10-3 1-5 3-7 4-1 2-1 3 0 5-2 1-3 1-5 2-4 1-10 4-14 4l4-3v-1h-2c-3-1-5-1-8-1v-1c1 0 1 0 2-1l2-1c0-1-1-1-1-2l7-1c1 0 1 1 2 2h1z" class="Z"></path><path d="M332 218c0-3 3-5 5-7-1 2-1 3 0 5-2 1-3 1-5 2z" class="i"></path><path d="M330 214h0c1 1 1 1 1 2-2 2-4 0-6 1l-1 1-1-1h-2v-1c1-1 2 0 4 0s4-1 5-2z" class="e"></path><path d="M315 212l7-1c1 0 1 1 2 2h1c-2 1-4 1-5 2h-2-4l2-1c0-1-1-1-1-2z" class="L"></path><defs><linearGradient id="G" x1="356.136" y1="202.596" x2="356.794" y2="217.34" xlink:href="#B"><stop offset="0" stop-color="#4a4746"></stop><stop offset="1" stop-color="#616364"></stop></linearGradient></defs><path fill="url(#G)" d="M361 205c5-3 10-7 15-9h0c1 1 1 2 2 3 1-1 2-1 3-1l1 1c-1 1-1 1-1 2h0c1 0 1 0 2 1-1 2-5 2-6 5-1 1-2 1-2 4h4v-1l1 1c-2 2-4 3-6 4-9 5-19 7-29 9-3 1-7 1-11 2-1 0-4 1-5 0-2-1-4-2-5-3-5 0-9 1-13 2v-1-1l-4-1c1-1 2-1 4-1l1 1c1 0 2 1 3 1 1-1 1-1 2-1h1c4 0 10-3 14-4 2-1 3-1 5-2 8-3 16-7 24-11z"></path><path d="M371 200l5-4c1 1 1 2 2 3l-1 1c-2 1-3 2-5 3l-1-1v-2z" class="E"></path><path d="M360 212c2-1 3-2 5-1l1 1h-1l-3 3c-1 1-2 1-2 2-2 0-2 0-3 1-3 2-6 2-9 3-3 0-7 0-9 1v1h3c1 2 1 1 3 1-3 1-7 1-11 2-1 0-4 1-5 0-2-1-4-2-5-3l1-1 5 3v-1c1 0 2-1 4-1 1 0 2-1 4-2 0-1 1-1 2-2l-1 1v1c2-2 4-2 7-3 2-1 4-2 7-3 2-2 4-2 7-3z" class="i"></path><path d="M360 212c2-1 3-2 5-1l1 1h-1l-3 3c-1 1-2 1-2 2-2 0-2 0-3 1-3 2-6 2-9 3 2-1 4-2 5-3v-1c2 0 4-1 5-2l2-1c1 0 1 0 1-1l-1-1z" class="D"></path><path d="M361 205c5-3 10-7 15-9h0l-5 4c-5 4-11 8-17 11-5 2-9 4-13 6v1h-1c-1 0-2 1-3 2-2 1-3 1-5 1v1h0 1l1 1c-2 0-3 1-4 1v1l-5-3-1 1c-5 0-9 1-13 2v-1-1l-4-1c1-1 2-1 4-1l1 1c1 0 2 1 3 1 1-1 1-1 2-1h1c4 0 10-3 14-4 2-1 3-1 5-2 8-3 16-7 24-11z" class="N"></path><path d="M341 217v1h-1c-1 0-2 1-3 2-2 1-3 1-5 1v1h0 1l1 1c-2 0-3 1-4 1v1l-5-3 16-5z" class="G"></path><defs><linearGradient id="H" x1="365.913" y1="203.886" x2="352.778" y2="228.329" xlink:href="#B"><stop offset="0" stop-color="#30302f"></stop><stop offset="1" stop-color="#68696b"></stop></linearGradient></defs><path fill="url(#H)" d="M378 199c1-1 2-1 3-1l1 1c-1 1-1 1-1 2h0c1 0 1 0 2 1-1 2-5 2-6 5-1 1-2 1-2 4h4v-1l1 1c-2 2-4 3-6 4-9 5-19 7-29 9-2 0-2 1-3-1h-3v-1c2-1 6-1 9-1 3-1 6-1 9-3 1-1 1-1 3-1 0-1 1-1 2-2l3-3h1l-1-1v-2-1c1-2 2-2 4-3 1 0 3 0 4-1h1l3-3v-1l1-1z"></path><path d="M378 199c1-1 2-1 3-1l1 1c-1 1-1 1-1 2h0c1 0 1 0 2 1-1 2-5 2-6 5-2 1-4 2-6 4-1 0-1 1-2 1l-7 5c-1 1-1 1-2 1h-1l1-1c0-1 1-1 2-2l3-3h1l-1-1v-2-1c1-2 2-2 4-3 1 0 3 0 4-1h1l3-3v-1l1-1z" class="F"></path><path d="M365 209c2-2 4-2 7-3h1c0 2-3 4-4 6l-7 5c-1 1-1 1-2 1h-1l1-1c0-1 1-1 2-2l3-3h1l-1-1v-2z" class="G"></path><defs><linearGradient id="I" x1="321.243" y1="623.643" x2="337.559" y2="624.75" xlink:href="#B"><stop offset="0" stop-color="#b70305"></stop><stop offset="1" stop-color="#e5201f"></stop></linearGradient></defs><path fill="url(#I)" d="M343 523c1-2 2-4 3-5 0-2 0-3 1-4h1c-5 12-9 24-11 37-1 3-1 7-2 10 0 3 0 9 1 12v8l1 4v4l2 1c0 2 2 4 2 6 1 2 2 5 3 7 2 0 2 1 3 3l1 2c-2-1-3-2-4-4h-1 0v1c1 1 2 2 2 4h1c1 2 2 4 4 6l1 1-1 1-3-3h-2l-1-1c-1 0-1 1-2 2h0l-1 1c-1 2-3 3-4 6h-1c0 1-1 3-1 5-1 2-1 4-2 6v4 1h1c1 0 2 1 3 2 0 1 1 3 0 4l-1 1v5l1 2-1 1v1c1 1 0 2 1 3 1 3 2 5 2 8 1 1 1 0 0 2h0c-1-1-1-1-3-2v-2h-2l-1-2v-3h-1c0 1 1 2 0 3l-3 5h0c-1 1-1 3-1 4-1 1-3 10-3 10 0 1-1 2-1 3l-1 1c-1 0-1 1-2 2h0c0-2 0-3-1-4l3-101h3l1-5v-5c0-1 0 0 1-1 1-4 1-8-1-11 0-2 1-6 2-8l1 1 1-5c1-1 1-2 2-4l1 1c0-1 1-2 1-3 2-4 4-9 6-14 0-2 1-4 2-4z"></path><path d="M333 616c1 2 2 3 2 5 0 1 0 2-1 2-1-2-1-5-1-7z" class="X"></path><path d="M334 608v-2h-1v4h-1c-1-5 0-12 0-18l3 6c0 4-1 7-1 10z" class="P"></path><path d="M329 666h-2c1-3 1-6 1-9l2-10h0c0-2 0-5 1-7v5c0 4 1 10-1 14v2 1c-1 1-1 2-1 4h0z" class="T"></path><path d="M337 589l2 1c0 2 2 4 2 6 1 2 2 5 3 7 2 0 2 1 3 3l1 2c-2-1-3-2-4-4h-1 0v1c1 1 2 2 2 4h1c1 2 2 4 4 6l1 1-1 1-3-3c-2-2-2-3-3-4-1-2-2-3-2-4s-1-4-1-5c-1-1 0-1-1-2l-1-3c0-1 0-2-1-4 0-1 0-2-1-3z" class="O"></path><path d="M329 551l1 1c0 3 0 6 1 9h0l1-1c0 1 0 2 1 2l1-2c0 2 0 4-1 6h0c-1-1-1 0-1-1h0 0c0 1 0 3-1 4-1 3 0 8 0 11l-1-1-2-3h-1 0v-5c0-1 0 0 1-1 1-4 1-8-1-11 0-2 1-6 2-8z" class="f"></path><path d="M335 541c2-4 4-9 6-14 0-2 1-4 2-4-1 5-4 10-5 15-1 3-2 6-2 9 0 1 0 0-1 1v2c0 3 0-1 0 2v3c-1 1 0 3-1 5l-1 2c-1 0-1-1-1-2l-1 1h0c-1-3-1-6-1-9l1-5c1-1 1-2 2-4l1 1c0-1 1-2 1-3z" class="H"></path><path d="M330 552l1-5c1-1 1-2 2-4l1 1c-2 5-3 11-2 16l-1 1h0c-1-3-1-6-1-9z" class="T"></path><path d="M335 598c1 2 2 4 3 7h0c1 1 1 2 1 3 1 2 0 4 0 6 1 1 2 1 2 2-1 2-3 3-4 6h-1c0 1-1 3-1 5-1 2-1 4-2 6v4 1h1c1 0 2 1 3 2 0 1 1 3 0 4l-1 1v5l1 2-1 1v1c1 1 0 2 1 3 1 3 2 5 2 8 1 1 1 0 0 2h0c-1-1-1-1-3-2v-2h-2l-1-2v-3h-1c0 1 1 2 0 3l-3 5c0-2 0-3 1-4v-1-2c2-4 1-10 1-14h0l1-11 1-4v-3c1-1 1-2 1-4 1 0 1-1 1-2 0-2-1-3-2-5l1-2v-6c0-3 1-6 1-10z" class="t"></path><path d="M334 614v-1c1-1 1-1 1-2l1-1c0 3 0 7-1 10v1c0-2-1-3-2-5l1-2z" class="u"></path><path d="M333 637v1h1c1 0 2 1 3 2 0 1 1 3 0 4l-1 1v5l1 2-1 1v1c1 1 0 2 1 3 1 3 2 5 2 8 1 1 1 0 0 2h0c-1-1-1-1-3-2v-2l-1-3-1-3v-1l-1-1v-3-3-3-9z" class="K"></path><path d="M333 649l2 1c0 3 1 5 1 8 1 2 2 4 3 7 1 1 1 0 0 2h0c-1-1-1-1-3-2v-2l-1-3-1-3v-1l-1-1v-3-3z" class="U"></path><path d="M552 421l2-2v2 1c0 1 0 1-1 2 0 1 0 2 1 3v3h0v-1c1-1 0-2 1-3l1 1c0 1 1 3 2 4h1c0 1 1 1 1 2h0l-2 2c0 2 0 3-1 4l-3 3h0c-1 1-2 1-3 2h0c-1 1-1 1-2 1-1 1-2 2-4 2l-2 2c-6 3-10 5-16 6-2 1-5 1-7 2-11 1-20 1-30-1-6-1-12-2-18-5l-2-2v-3c0-4 4-7 6-9v-2l3-2 5-1 10-3-1 2h-1c-2 0-3 1-4 2-1-1 0-1-1-1-2 1-4 1-5 2h0l1 2 2-1h1c0-1 0 0 1-1 1 1 1 1 2 1-1 1-2 1-3 1v2c3-1 7-3 10-4 11-3 22-4 33-3 4 0 8 1 11 3l1 1 3-3 6-8 2-3z" class="u"></path><path d="M552 421l2-2v2 1c0 1 0 1-1 2 0 1 0 2 1 3v3h0v-1c1-1 0-2 1-3l1 1c0 1 1 3 2 4h1c0 1 1 1 1 2h0l-2 2c0 2 0 3-1 4l-3 3h0v-1c-3-1-5 3-7 3-2-3 3-4 4-6v-3l1-2v-7c0-1-1-1-2-2l2-3z" class="X"></path><path d="M554 430v-1c1-1 0-2 1-3l1 1c0 1 1 3 2 4h1c0 1 1 1 1 2h0l-2 2c-1 1-2 3-4 3-1-3 0-5 0-8z" class="T"></path><path d="M494 429l-1 2h-1c-2 0-3 1-4 2-1-1 0-1-1-1-2 1-4 1-5 2h0l1 2 2-1h1c0-1 0 0 1-1 1 1 1 1 2 1-1 1-2 1-3 1v2l-1 1c-1 1-2 2-3 4 0 2 1 3 2 4 2 0 4 1 6 2 6 1 13 2 20 1 12-2 22-7 30-16l1 1 3-3c-1 7-11 12-16 15s-10 4-14 7c-2 1-7 0-9 0v1h15c-4 1-7 2-11 2-5 0-13 0-18-1-7-2-12-3-18-6l-1 1-2-2v-3c0-4 4-7 6-9v-2l3-2 5-1 10-3z" class="g"></path><path d="M494 429l-1 2h-1c-2 0-3 1-4 2-1-1 0-1-1-1-2 1-4 1-5 2h0l1 2 2-1h1c0-1 0 0 1-1 1 1 1 1 2 1-1 1-2 1-3 1v2l-1 1c-1 1-2 2-3 4 0 2 1 3 2 4h-1-4c-1-1-1-2-3-2v1c-1 1-1 0-2 1l-1 1h0l-1-2c3-6 7-8 11-12-1 1-3 1-4 2-1 0-1 1-2 1h-1v-2l3-2 5-1 10-3z" class="H"></path><path d="M486 438c3-1 7-3 10-4 11-3 22-4 33-3 4 0 8 1 11 3-8 9-18 14-30 16-7 1-14 0-20-1-2-1-4-2-6-2-1-1-2-2-2-4 1-2 2-3 3-4l1-1z" class="a"></path><path d="M222 182h27-3v2l1 1c0-1 1-1 1-1 1 2 2 2 3 3h0c2 2 3 1 4 3v1h0l1 1v1 1l7 2h7l2 1v1c0 1 1 1 2 2 2 1 6 0 9 0 12 0 25-2 36-7 6-2 11-5 16-8v1c2-1 4-4 5-4 2-1 6 0 8 0h22c-1 1-2 2-3 2l-7 6c-5 5-13 9-20 12l-6 3-13 3-13 3c-1 0-3 0-4 1-6 0-10 0-15-2-2 1-4 0-6 0l-1 1c-5 0-9-1-13-3-5-2-13-2-17-6-1 0-1 0-2-1l-2 1-5-3-19-10c-3-3-6-5-8-7h6z" class="V"></path><path d="M263 196h7l2 1v1h-3c-3 0-4 0-6-2z" class="Z"></path><path d="M306 200l8-3v3l-3 2h-3c-1-1-1 0-1-1-1 0-1-1-1-1z" class="F"></path><defs><linearGradient id="J" x1="289.836" y1="209.044" x2="272.664" y2="196.456" xlink:href="#B"><stop offset="0" stop-color="#504e50"></stop><stop offset="1" stop-color="#656564"></stop></linearGradient></defs><path fill="url(#J)" d="M272 201l4 1c6 1 13 0 19-1v2c-2 0-2 0-3 1s-2 1-3 1-2 1-4 1h0v1l1 1c-3 0-7 0-9-1-1 0-2-2-2-2h-5-1v-1h-1c1 0 2 0 3-1h-2l-1-1c1 0 3 0 4-1z"></path><path d="M295 201c2-1 8-1 11-1 0 0 0 1 1 1 0 1 0 0 1 1h3 3 2l-2 1-3 1h0-1l-2 2h-1c-2-1-3 0-5 0-4-1-8 0-11 1h-6v-1h0c2 0 3-1 4-1s2 0 3-1 1-1 3-1v-2z" class="J"></path><path d="M308 202h3 3 2l-2 1-3 1c-2 0-4 0-5-1l1-1h1z" class="E"></path><path d="M295 201c2-1 8-1 11-1 0 0 0 1 1 1 0 1 0 0 1 1h-1c-2 0-4 1-7 1-1 0-2 0-4 1h-4c1-1 1-1 3-1v-2z" class="B"></path><path d="M311 204h6v2l1 1c1 0 2 0 3 1l-13 3c-1 0-3 0-4 1-6 0-10 0-15-2-2 1-4 0-6 0-2-1-4-1-6-1-1-1-2-2-3-2-2 0-2-1-4-2h5s1 2 2 2c2 1 6 1 9 1l-1-1h6c3-1 7-2 11-1 2 0 3-1 5 0h1l2-2h1z" class="R"></path><path d="M311 204h6v2l1 1c-2 0-3 1-5 1-1 0-3 0-4 1v-1h1c1-1 2-1 3-2-2 0-4 2-6 2h-3-5c-2 1-3 1-4 0h-9l-1-1h6c3-1 7-2 11-1 2 0 3-1 5 0h1l2-2h1z" class="D"></path><defs><linearGradient id="K" x1="236.54" y1="196.213" x2="274.96" y2="203.287" xlink:href="#B"><stop offset="0" stop-color="#59595a"></stop><stop offset="1" stop-color="#8e8d8e"></stop></linearGradient></defs><path fill="url(#K)" d="M233 189l11 3c3 1 7 3 10 4 2 0 4 2 6 2 2 1 5 1 7 2 0 1 0 1 1 1h4c-1 1-3 1-4 1l1 1h2c-1 1-2 1-3 1h1v1h1c2 1 2 2 4 2 1 0 2 1 3 2 2 0 4 0 6 1l-1 1c-5 0-9-1-13-3-5-2-13-2-17-6-1 0-1 0-2-1l-2 1-5-3h1c0-1 0-2-1-2 0-1-1-1-2-2-3-2-6-3-8-6z"></path><path d="M269 205c-1 0 0 0-1-1h-1c-4-1-6-2-9-3l1-1h1 2 1 4c0 1 0 1 1 1h4c-1 1-3 1-4 1l1 1h2c-1 1-2 1-3 1h1v1z" class="D"></path><defs><linearGradient id="L" x1="218.186" y1="181.451" x2="255.628" y2="194.101" xlink:href="#B"><stop offset="0" stop-color="#0c0c0c"></stop><stop offset="1" stop-color="#676868"></stop></linearGradient></defs><path fill="url(#L)" d="M222 182h27-3v2l1 1c0-1 1-1 1-1 1 2 2 2 3 3h0c2 2 3 1 4 3v1h0l1 1v1 1c-3 0-6-2-9-3l-15-4h0l1 2c2 3 5 4 8 6 1 1 2 1 2 2 1 0 1 1 1 2h-1l-19-10c-3-3-6-5-8-7h6z"></path><path d="M335 186c2-1 4-4 5-4 2-1 6 0 8 0h22c-1 1-2 2-3 2l-7 6c-5 5-13 9-20 12l-6 3-13 3c-1-1-2-1-3-1l-1-1v-2h-6 0l3-1 2-1h-2-3l3-2v-3l4-1 10-4 6-5 1-1z" class="h"></path><path d="M328 192l6-5 1 3c-1 1-2 1-3 1-2 1-2 1-4 1zm-10 4v1c-1 1-1 2-2 3 2 1 3 1 5 1-2 1-4 1-5 1h-2-3l3-2v-3l4-1z" class="C"></path><path d="M329 200c1-1 1-1 2-1 0 2 0 2 1 3h0c3-1 5-1 8 0l-6 3-2-1c-2 0-3 0-4-1 1 0 2-1 2-2v-1l-2 1 1-1z" class="G"></path><path d="M321 201l8-1-1 1 2-1v1c0 1-1 2-2 2-1 1-1 1-1 0h-1l-1 1h-5c-2-1-4-1-6-1l2-1c1 0 3 0 5-1z" class="J"></path><path d="M314 203c2 0 4 0 6 1h5l1-1h1c0 1 0 1 1 0 1 1 2 1 4 1l2 1-13 3c-1-1-2-1-3-1l-1-1v-2h-6 0l3-1z" class="L"></path><defs><linearGradient id="M" x1="290.766" y1="216.979" x2="281.091" y2="229.476" xlink:href="#B"><stop offset="0" stop-color="#080807"></stop><stop offset="1" stop-color="#383839"></stop></linearGradient></defs><path fill="url(#M)" d="M224 189l19 10 5 3 2-1c1 1 1 1 2 1 4 4 12 4 17 6 4 2 8 3 13 3l1-1c2 0 4 1 6 0 5 2 9 2 15 2v2c3-1 7-1 10-2h1c0 1 1 1 1 2l-2 1c-1 1-1 1-2 1v1c3 0 5 0 8 1h2v1l-4 3h-1c-1 0-1 0-2 1-1 0-2-1-3-1l-1-1c-2 0-3 0-4 1l4 1v1 1c4-1 8-2 13-2 1 1 3 2 5 3l-4 1c2 0 5 0 7 1-5 1-10 1-13 4-1 5 0 9 1 14 1 2 1 4 2 6 0 2 0 2 2 3v-1c0-1 1-1 1-2v-1l1-1 1 1c0 1 0 1 1 1l1 1c-1 1-1 1-3 1v1l-2 1-1 1h-4 0-1-1-1c-1-1-1-1-2 0-1-1-1-2-1-3h1l-1-2h0c-1 0-1 0-2-1l-1-1h0v-2c-1 0-1 0-2 2h1c1 2 0 2 0 4l1 1v1h-3c-2-2-5-4-6-5l-2-2h-1c-1-1-1-1-2-1l-3-3c-2-1-2-1-3-1l-3-2-1-1-2-2h-1c-1-1-1-2-1-3l-1-1c0-2 0-3-2-3 0 2 0 4-2 6h0c-1 1-1 1-2 1-1-1-2-2-2-3v-1l-3-3s-1 0-2-1l-12-9h1l-1-1 1-1c0 1 0 1 1 1l1-1v-1c-1-1-2-2-4-3l-4-2c0-1 1-1 1-1-1-2-3-3-5-4-1-2-4-3-6-6 0 0-3-1-3-2h5c-6-4-13-7-19-11v-1z"></path><path d="M311 230c3 0 5 2 8 2-1 5 0 9 1 14 1 2 1 4 2 6l-2-1c0-2-1-4-2-6-1-6-2-11-7-15z" class="N"></path><path d="M299 221l8 1 4 1v1 1c4-1 8-2 13-2 1 1 3 2 5 3l-4 1c2 0 5 0 7 1-5 1-10 1-13 4-3 0-5-2-8-2l-2-1c-1 0-8-3-10-4l3-1c-1 0-2 0-3-1v-1-1z" class="e"></path><path d="M299 221l8 1 4 1v1c-3 1-6 1-9 0-1 0-2 0-3-1v-1-1z" class="c"></path><path d="M324 223c1 1 3 2 5 3l-4 1v-1h-1c-2 0-3 0-4-1h-1-1c-1 1-1 0-2 0h-2c-1 0-2 0-4 1h0l-1-1h2c4-1 8-2 13-2z" class="V"></path><path d="M324 226h1v1c2 0 5 0 7 1-5 1-10 1-13 4-3 0-5-2-8-2l-2-1c3-1 7 0 10-2 2 0 3-1 5-1z" class="c"></path><path d="M224 189l19 10 5 3c6 3 11 5 16 8 10 4 19 8 29 10l6 1v1 1c1 1 2 1 3 1l-3 1-35-14h0l-4-1c-2-1-4-3-6-3 1 3 3 3 4 5-3-2-6-4-10-6-2 0-4-2-7-3 0 0-3-1-3-2h5c-6-4-13-7-19-11v-1z" class="Y"></path><path d="M243 201l21 10-4-1c-2-1-4-3-6-3 1 3 3 3 4 5-3-2-6-4-10-6-2 0-4-2-7-3 0 0-3-1-3-2h5z" class="J"></path><path d="M248 202l2-1c1 1 1 1 2 1 4 4 12 4 17 6 4 2 8 3 13 3l1-1c2 0 4 1 6 0 5 2 9 2 15 2v2c3-1 7-1 10-2h1c0 1 1 1 1 2l-2 1c-1 1-1 1-2 1v1c3 0 5 0 8 1h2v1l-4 3h-1c-1 0-1 0-2 1-1 0-2-1-3-1l-1-1c-2 0-3 0-4 1l-8-1-6-1c-10-2-19-6-29-10-5-3-10-5-16-8z" class="L"></path><path d="M304 214c3-1 7-1 10-2 0 0-1 1-1 2-1 0-4 1-5 2h-4c-1-1-1-1-2-1s-2 0-4-1h6z" class="Z"></path><path d="M283 210c2 0 4 1 6 0 5 2 9 2 15 2v2h-6c-5-1-11-1-16-3l1-1z" class="N"></path><path d="M314 212h1c0 1 1 1 1 2l-2 1c-1 1-1 1-2 1v1c3 0 5 0 8 1h2v1l-4 3h-1c-1 0-1 0-2 1-1 0-2-1-3-1l-1-1c-2 0-3 0-4 1l-8-1-6-1h2c2-1 3-1 5 0h5c2-2 3-3 3-4 1-1 4-2 5-2 0-1 1-2 1-2z" class="i"></path><path d="M305 220l6-1c1 1 2 1 3 1 1 1 0 2 3 2-1 0-1 0-2 1-1 0-2-1-3-1l-1-1c-2 0-3 0-4 1l-8-1-6-1h2c2-1 3-1 5 0h5z" class="m"></path><path d="M241 203c3 1 5 3 7 3 4 2 7 4 10 6l11 6 6 3c2 0 3 2 4 3 4 0 9 3 11 6 1 0 1 0 1 1h-1c-1-1-2-1-4-2 0 0-1 0-1-1h-2l-1 1c0 1 1 1 2 2l1-1v3c-1 1-1 2-1 4h0v1c-1 0-1 0-1 1-1-1-1-2-1-3l-1-1c0-2 0-3-2-3 0 2 0 4-2 6h0c-1 1-1 1-2 1-1-1-2-2-2-3v-1l-3-3s-1 0-2-1l-12-9h1l-1-1 1-1c0 1 0 1 1 1l1-1v-1c-1-1-2-2-4-3l-4-2c0-1 1-1 1-1-1-2-3-3-5-4-1-2-4-3-6-6z" class="B"></path><path d="M251 214c0-1 1-1 1-1l12 5-2 1v1c-1 0-1-1-1-2-1 1-1 1-2 1-1-1-2-2-4-3l-4-2z" class="i"></path><path d="M259 219c1 0 1 0 2-1 0 1 0 2 1 2v-1l2-1 3 3c0 1 1 1 2 2 2 1 4 3 5 4s1 0 2 1 3 2 3 4 0 4-2 6h0c-1 1-1 1-2 1-1-1-2-2-2-3v-1l-3-3s-1 0-2-1l-12-9h1l-1-1 1-1c0 1 0 1 1 1l1-1v-1z" class="G"></path><path d="M259 219c1 0 1 0 2-1 0 1 0 2 1 2v-1l2-1 3 3c0 1 1 1 2 2 2 1 4 3 5 4s1 0 2 1 3 2 3 4 0 4-2 6v-3-1c0-3-5-4-6-5s-2-1-2-2v-1l-1-1-1 1c-1-1-2-2-4-3-1 0-2-2-4-3v-1z" class="D"></path><defs><linearGradient id="N" x1="292.988" y1="248.36" x2="318.437" y2="241.596" xlink:href="#B"><stop offset="0" stop-color="#737474"></stop><stop offset="1" stop-color="#9b9a9a"></stop></linearGradient></defs><path fill="url(#N)" d="M283 239c0-1 0-1 1-1v-1h0c0-2 0-3 1-4v-3l-1 1c-1-1-2-1-2-2l1-1h2c0 1 1 1 1 1 2 1 3 1 4 2h1l6 3c2 1 3 2 5 3 2 2 4 3 6 4 3 1 6 4 9 6l1-1v-1c1 2 2 4 2 6l2 1c0 2 0 2 2 3v-1c0-1 1-1 1-2v-1l1-1 1 1c0 1 0 1 1 1l1 1c-1 1-1 1-3 1v1l-2 1-1 1h-4 0-1-1-1c-1-1-1-1-2 0-1-1-1-2-1-3h1l-1-2h0c-1 0-1 0-2-1l-1-1h0v-2c-1 0-1 0-2 2h1c1 2 0 2 0 4l1 1v1h-3c-2-2-5-4-6-5l-2-2h-1c-1-1-1-1-2-1l-3-3c-2-1-2-1-3-1l-3-2-1-1-2-2h-1z"></path><path d="M290 240c3 2 7 5 10 8 0 1 1 2 1 3l-2-2h-1c-1-1-1-1-2-1l-3-3c-2-1-2-1-3-1l-3-2-1-1h2 0c1 0 1 0 2-1z" class="V"></path><path d="M283 239c0-1 0-1 1-1v-1h0c0-2 0-3 1-4v-3l-1 1c-1-1-2-1-2-2l1-1h2c0 1 1 1 1 1 2 1 3 1 4 2h1l6 3c2 1 3 2 5 3 2 2 4 3 6 4 3 1 6 4 9 6l1-1v-1c1 2 2 4 2 6l-1 1-9-7c-1-1-2-2-3-2-2-1-2-2-4-3-1-1-2-1-2-2h-1c-1-1-1-1-2-1-3-1-5-2-8-3v-1l-1 1c0 1 1 2 2 3v1c-1 1 0 1-1 2h0c-1 1-1 1-2 1h0-2l-2-2h-1z" class="D"></path><path d="M284 239h2c1-1 1-1 0-2 1-1 1-2 2-2l3 3c-1 1 0 1-1 2h0c-1 1-1 1-2 1h0-2l-2-2z" class="R"></path><path d="M550 419c16-28 21-63 19-95-3-34-13-67-35-93-7-8-14-15-22-21 7 3 14 9 19 15 13 12 24 26 32 43s14 35 18 54c1 5 2 11 2 15 0 2 0 3-1 5-1 3 0 8 0 11v6c-1 2-1 4-1 6l-1 8v5c-1 3 0 7 2 10h0 1l-2 6-5 5c-2 2-4 3-5 6h0l-1 2h1v1c-2 2-2 5-4 7l-4 11-2 5c0 1 0 1-1 2 0-1-1-1-1-2h-1c-1-1-2-3-2-4l-1-1c-1 1 0 2-1 3v1h0v-3c-1-1-1-2-1-3 1-1 1-1 1-2v-1-2l-2 2-2-2z" class="P"></path><defs><linearGradient id="O" x1="578.293" y1="311.857" x2="561.161" y2="317.812" xlink:href="#B"><stop offset="0" stop-color="#ab0603"></stop><stop offset="1" stop-color="#e62327"></stop></linearGradient></defs><path fill="url(#O)" d="M574 367h-2v-1c1-2 0-3 1-4 0-2 0-4 1-6v-6-11c0-1-1-4 0-5l1-1c0-1 0-4-1-5v-5c0-4-1-7-1-10 0-1 0-2-1-3v-3l-4-18-7-19 2-2c8 17 14 35 18 54-2 3-4 5-3 9v1c0 10 0 19-1 29h-1c-1-2-1-3-1-5-1 2 0 4-1 6v3 2z"></path><path d="M581 322c1 5 2 11 2 15 0 2 0 3-1 5-1 3 0 8 0 11v6c-1 2-1 4-1 6l-1 8v5c-1 3 0 7 2 10h0 1l-2 6-5 5c-2 2-4 3-5 6h0l-1 2h1v1c-2 2-2 5-4 7l-4 11-2 5c0 1 0 1-1 2 0-1-1-1-1-2h-1c-1-1-2-3-2-4 0-2 1-9 2-10v-2l1-1v-3c1-1 1 0 1-1s0-2 1-2l1-1v-2l4-12 1-2c1-3 1-5 3-7h0l2-7v-3l1-2c0-2 0-3 1-5v-2-3c1-2 0-4 1-6 0 2 0 3 1 5h1c1-10 1-19 1-29v-1c-1-4 1-6 3-9z" class="g"></path><path d="M574 365c1 0 2 0 2 1 1 1 1 5 0 6l-2 2-1-1-1 1 1-2c0-2 0-3 1-5v-2z" class="H"></path><path d="M579 362l2 3-1 8h-1v1c0 1 0 0-1 2v1 1 2l-1 1c0 1 1 2 0 3v4c-1 1-1 3-2 3-1-1 2-11 2-13 1-5 2-11 2-16z" class="k"></path><path d="M577 388v-4c1-1 0-2 0-3l1-1v-2-1-1c1-2 1-1 1-2v-1h1v5c-1 3 0 7 2 10h0 1l-2 6-5 5-1-1c2-4 3-8 4-12l-1-1c-1 1-1 2-1 3z" class="K"></path><path d="M581 322c1 5 2 11 2 15 0 2 0 3-1 5-1 3 0 8 0 11v6c-1 2-1 4-1 6l-2-3 1-11c0-5 1-10-1-15l-1-4v-1c-1-4 1-6 3-9z" class="H"></path><path d="M580 351v-1c1-2 1-5 1-7h0c1 3 0 7 1 10v6c-1 2-1 4-1 6l-2-3 1-11z" class="j"></path><path d="M572 374l1-1 1 1 2-2c-1 5-1 10-2 15-1 4-2 7-3 11-1 2-3 5-3 7-1 1-1 1-2 1l-2-2 2-6 3-9h0c0-2 0-3 1-5h0l2-7v-3z" class="K"></path><path d="M556 427c0-2 1-9 2-10v-2l1-1v-3c1-1 1 0 1-1s0-2 1-2l1-1v-2l4-12 1-2c1-3 1-5 3-7-1 2-1 3-1 5h0l-3 9-2 6 2 2c1 0 1 0 2-1-1 3-2 5-3 8 0 1 1 3 0 4s-1 1-1 2c0 2-1 4-2 6l1 1-2 5c0 1 0 1-1 2 0-1-1-1-1-2h-1c-1-1-2-3-2-4z" class="X"></path><path d="M564 404l2 2c1 0 1 0 2-1l-3 8c0 1 1 3 0 4s-1 1-1 2c0 2-1 4-2 6l1 1-2 5c0 1 0 1-1 2 0-1-1-1-1-2h0c2-5 1-10 2-14l1-6 1-3v-2l1-2z" class="f"></path><defs><linearGradient id="P" x1="348.04" y1="220.119" x2="344.811" y2="264.43" xlink:href="#B"><stop offset="0" stop-color="#030303"></stop><stop offset="1" stop-color="#3b3b3c"></stop></linearGradient></defs><path fill="url(#P)" d="M402 198l19-7 1 2c-15 4-30 11-42 23l-1 3h0c-2 7-3 13-2 21 0 2 1 5 1 7-1 2-1 6-1 8 0 1 1 1 1 2v1c-2-1-3-1-5 0s-4 4-5 6c-2 2-2 5-3 8h-1v4 1c-1-1-1-1-1-2-1 0-2 1-2 2h-1c-1 2-2 2-2 5l-2-2h-2c1 1 2 3 4 4l1 1 1 2h0-1c-1-1-2-1-2-1-2-1-3-2-3-2-2 0-2 0-4-1h-1c-2-1-2-1-4-3-2-1-3-1-5-3-1 0-2-1-3-1v-1h-2c1-1 1-1 2-1-1-1-3-4-5-5-1 0-1 0-1-1h-1-1l-1-1c-2-1-2-1-3-3s-3-3-4-5c0-1-1-1-2-2h0 4l1-1 2-1v-1c2 0 2 0 3-1l-1-1c-1 0-1 0-1-1l-1-1-1 1v1c0 1-1 1-1 2v1c-2-1-2-1-2-3-1-2-1-4-2-6-1-5-2-9-1-14 3-3 8-3 13-4-2-1-5-1-7-1l4-1c1 1 4 0 5 0 4-1 8-1 11-2 10-2 20-4 29-9 2-1 4-2 6-4l22-13z"></path><path d="M364 251l1-3v1c0 1 1 1 2 3v2l-2 2-2-1c1-2 1-2 1-4zm-41-9l3-3 1 1 2-1v4c2 1 2 1 3 3h-2l-1-1c-2-1-2-1-3-2 0-1-2-1-3-1z" class="I"></path><path d="M357 263c0-1 1-1 1-2 1-1 3-1 4-2h0 2l-1 3h-1c-2 1-2 1-3 2v2h1c1 0 1-1 2-1h1c-1 2-2 4-4 6h-1l-1-1v-1-1-5z" class="C"></path><path d="M332 246h2c1-1 1 0 1-1 0-3 2-5 4-7-1 3-2 6-4 9 0 2 0 2 1 3h1c1-1 0-1 1-1v2 1c-1 1-2 2-3 2s-1 1-2 0v-3h-1c-1 1-1 1-1 2h-2l-1-1c-1 0-1 0-1-1 2 0 3-2 4-3l-1-2h2z" class="E"></path><path d="M357 248c1-3 2-5 4-7l1 1h0c0 3 0 4 1 6v1c0 1 1 2 1 3v-1c0 2 0 2-1 4l2 1 1 2-1 2-1-1h-2 0c-1 1-3 1-4 2 0 1-1 1-1 2l-1-1 1-1h1c0-1 0-1 1-2l3-1v-2h0v-3c0-1 1-1 1-3-2-1-3 3-5 3 1-2 1-2 1-4l-2-1z" class="F"></path><path d="M363 265c2-2 3-3 5-4-2 3-6 8-5 12v2c-1 0-2 1-2 2l-2 1-2-1v-1-1c-1-2-2-3-4-5 1-1 2-1 4-1v1l1 1h1c2-2 3-4 4-6z" class="B"></path><path d="M353 270c1-1 2-1 4-1v1l1 1h1v3l-1 1 1 1h-2v-1c-1-2-2-3-4-5z" class="F"></path><path d="M357 248l2 1c0 2 0 2-1 4 2 0 3-4 5-3 0 2-1 2-1 3v3h0v2l-3 1c-1 1-1 1-1 2h-1 0c-1 0-2 1-3 1h0c0-1 1-1 1-2v-7h0l-1 2h0c-1-1-1-1 0-2s2-3 3-5z" class="G"></path><path d="M323 242c1 0 3 0 3 1 1 1 1 1 3 2l1 1 1 2c-1 1-2 3-4 3l-1-1-1 1v1c0 1-1 1-1 2v1c-2-1-2-1-2-3-1-2-1-4-2-6h1c2-1 2-2 2-4z" class="J"></path><path d="M350 255h0c0-3 0-4 3-6l1 4c-1 1-1 1 0 2h0l1-2h0v7c0 1-1 1-1 2h0c1 0 2-1 3-1h0l-1 1 1 1v5 1c-2 0-3 0-4 1v-2 1l-1-1c0-1 0-2-1-3-1-3-1-6-1-9v-1z" class="R"></path><path d="M356 262l1 1v5 1c-2 0-3 0-4 1v-2l3-6z" class="J"></path><defs><linearGradient id="Q" x1="379.721" y1="240.378" x2="371.573" y2="238.698" xlink:href="#B"><stop offset="0" stop-color="#a5a5a6"></stop><stop offset="1" stop-color="#c7c6c8"></stop></linearGradient></defs><path fill="url(#Q)" d="M373 228c1-2 1-4 2-5 0-2 2-3 4-4-2 7-3 13-2 21 0 2 1 5 1 7-1 2-1 6-1 8 0 1 1 1 1 2v1c-2-1-3-1-5 0s-4 4-5 6c-2 2-2 5-3 8h-1v4 1c-1-1-1-1-1-2v-2c-1-4 3-9 5-12 3-3 5-7 6-10s1-6 1-8l-2-15z"></path><path d="M347 253v3l3-1v1c0 3 0 6 1 9 1 1 1 2 1 3l1 1v-1 2c2 2 3 3 4 5v1 1l2 1 2-1h-1c-1 2-2 2-2 5l-2-2h-2l-1-1c-1 0-2-1-3-2l-3-4c-1-1-1-1-1-3l-3-3v-2h1v-3l-1-1v-4l3-1h0l-1-1 2-2z" class="D"></path><path d="M347 253v3 1l1 1h1l-1 1h1v3 3c-1-1-2-2-3-4h0v-1h-1l-1 2-1-1v-4l3-1h0l-1-1 2-2z" class="i"></path><path d="M343 257l3-1 1 2-1 1h0v2-1h-1l-1 2-1-1v-4z" class="V"></path><path d="M349 266v1c1 0 1 0 2 1 0 1 0 2-1 3 1 2 3 4 3 5l3 3c1-1 0-1 1-2l2 1 2-1h-1c-1 2-2 2-2 5l-2-2h-2l-1-1-3-6c-1-1-2-1-2-2h1 0v-3-2z" class="i"></path><path d="M344 262l1-2h1v1h0c1 2 2 3 3 4v1 2 3h0-1c0 1 1 1 2 2l3 6c-1 0-2-1-3-2l-3-4c-1-1-1-1-1-3l-3-3v-2h1v-3z" class="r"></path><path d="M346 261c1 2 2 3 3 4v1 2c-1-1-1 0-2-1h0l-1-1 1-1c-1-1-1-2-1-4z" class="Z"></path><path d="M339 247c0-2 0-2 1-3v-1c0-1 1-1 1-2s1-2 2-3c0 1 1 2 2 3v-1c2 2 2 2 2 4l1-1c0-1 0-2 1-3 1 0 2 0 3 1s0 2 0 4c1 1 1 2 1 4-3 2-3 3-3 6h0l-3 1v-3l-2 2 1 1h0l-3 1v4h-1l-1-3-2-1h-1l-1 2c0-2 0-5 1-7v-1-2l1-2z" class="E"></path><path d="M347 249h1v-4h1v1l1 1c0-1 0-2 1-3l1 1c1 1 1 2 1 4-3 2-3 3-3 6h0l-3 1v-3-4z" class="G"></path><path d="M339 247l3-3 2 2v2l1 1c0-1 1-2 1-2 1 0 1 1 1 2v4l-2 2 1 1h0l-3 1v4h-1l-1-3-2-1h-1l-1 2c0-2 0-5 1-7v-1-2l1-2z" class="L"></path><path d="M343 256c-1-1-1-3 0-5h1l1 4 1 1h0l-3 1v-1z" class="s"></path><path d="M338 251c1 2 1 2 1 4 1 1 1 0 1 2 1 0 2 0 3-1v1 4h-1l-1-3-2-1h-1l-1 2c0-2 0-5 1-7v-1z" class="D"></path><path d="M402 198l19-7 1 2c-15 4-30 11-42 23l-1 3h0c-2 1-4 2-4 4-1 1-1 3-2 5v-1c-1-3-2-4-4-7-12 3-24 7-37 8-2-1-5-1-7-1l4-1c1 1 4 0 5 0 4-1 8-1 11-2 10-2 20-4 29-9 2-1 4-2 6-4l22-13z" class="N"></path><path d="M369 220c1 0 2 0 3-1 3 0 5-2 8-3l-1 3h0c-2 1-4 2-4 4-1 1-1 3-2 5v-1c-1-3-2-4-4-7z" class="W"></path><path d="M329 253h2c0-1 0-1 1-2h1v3c1 1 1 0 2 0s2-1 3-2c-1 2-1 5-1 7l1-2h1l2 1 1 3h1l1 1v3h-1v2l3 3c0 2 0 2 1 3l3 4c1 1 2 2 3 2l1 1c1 1 2 3 4 4l1 1 1 2h0-1c-1-1-2-1-2-1-2-1-3-2-3-2-2 0-2 0-4-1h-1c-2-1-2-1-4-3-2-1-3-1-5-3-1 0-2-1-3-1v-1h-2c1-1 1-1 2-1-1-1-3-4-5-5-1 0-1 0-1-1h-1-1l-1-1c-2-1-2-1-3-3s-3-3-4-5c0-1-1-1-2-2h0 4l1-1 2-1v-1c2 0 2 0 3-1z" class="m"></path><path d="M323 257l1-1c2 2 5 3 5 5l-1 1c-1 0-2 0-3-1 0-1 0-1-1-1-1-1-1-2-1-3z" class="e"></path><path d="M340 277c-1 0-2-1-3-1v-1h-2c1-1 1-1 2-1h5l3 3c1 1 2 1 2 3h1c2 1 4 2 6 4-2 0-2 0-4-1h-1c-2-1-2-1-4-3-2-1-3-1-5-3z" class="Z"></path><path d="M329 253h2c0-1 0-1 1-2h1v3c1 1 1 0 2 0s2-1 3-2c-1 2-1 5-1 7l1-2h1l2 1 1 3h1l1 1v3h-1v2l3 3c0 2 0 2 1 3l3 4-1 1h0c-3-2-4-3-6-6-2 0-3-1-4-3v-1h-1c-1-1-2-2-2-3l-1-1c-3-1-5-4-7-6 0 0 1 0 1-1h0c-1-1-1-1-3-1v-1-1c2 0 2 0 3-1z" class="W"></path><path d="M338 252c-1 2-1 5-1 7 0 1 1 3 0 4l-2-2c-2-2-2-4-2-7 1 1 1 0 2 0s2-1 3-2z" class="F"></path><path d="M337 259l1-2h1l2 1 1 3h1l1 1v3h-1v-1c-1 0-1 0-2-1h0-2v1l-2-1c1-1 0-3 0-4z" class="R"></path><defs><linearGradient id="R" x1="407.339" y1="198.617" x2="519.183" y2="426.673" xlink:href="#B"><stop offset="0" stop-color="#020302"></stop><stop offset="1" stop-color="#262525"></stop></linearGradient></defs><path fill="url(#R)" d="M422 193c15-5 33-7 48-3 1 1 2 1 4 1h0l1-1 8 4c-5-1-10-2-14-2-18 0-35 6-49 16-12 9-20 22-22 38-1 6-1 14 0 20 3 17 9 33 15 49 1 4 3 9 5 13 2 2 2 3 3 6 2 4 3 11 6 15 1 5 3 10 6 14 1 3 2 5 3 8h1v1c1 3 2 8 5 10 2-1 4-1 5-3l1-1v2c1 0 3 0 4 1l-2 2v3l-1 4c0 1 0 3-1 4 0 2 0 5 1 6l-1 3c3 7 5 15 9 22h0c0 1 1 2 1 3l1 4 3 7c0 2 1 4 2 6l1 2c0 1 1 3 1 4s1 2 2 3v1l2 2h0c0 2 1 3 2 4h0c0 1 1 4 1 4 1 1 4 3 3 4-1 0-2-1-3-1 0 3 2 7 3 10l4 10-2-1c0-4-9-6-12-8s-5-4-7-6h0c0-1-1-2-1-2-1-1-1-2-2-2v-1c0-1 0-1-1-2v-1h-1c-1 0-2-1-3-2l-3-3-1-2c-1-2-1-3-3-4h0c-2-1-4-4-5-6v-1c-1 0-1 0-1 1h0c-1 0-2-2-2-3-1 1-2 1-2 3l-1-1c-4-9-6-19-7-28v-1c0-1 0-2-1-3v-7h1c1-1 2-1 3-2h4c1-1 1-1 3 0l2-1h2c2 3 4 7 6 10 0-2-4-9-5-11v-4-1c0-3-2-5-3-9-2-2-3-4-4-6-1-1-2-2-2-4v-1h0l-2-5c-1-2-3-6-3-8-1-2 0 1-1-2h0c-1-1-1-3-2-3v-1-2l-1-2h0l-1-2c0-1-1-2-2-3-2 0-29-62-33-70-3-7-4-14-8-21-1 0-1-1-1-2v-1c0-1-1-1-1-2 0-2 0-6 1-8 0-2-1-5-1-7-1-8 0-14 2-21h0l1-3c12-12 27-19 42-23z"></path><path d="M445 395l-3-9 4-3c0 1 1 3 0 4l-1 1v5 2z" class="M"></path><path d="M426 364c1 0 2 1 2 2l1 2c1 2 3 5 4 8 0 2 1 3 2 4 2 4 3 7 3 10-2-2-3-4-4-6-1-1-2-2-2-4v-1h0l-2-5c-1-2-3-6-3-8-1-2 0 1-1-2h0z" class="I"></path><path d="M446 383c1-1 2-1 3-1l1 1v3l-1 4c0 1 0 3-1 4 0 2 0 5 1 6l-1 3-3-8v-2-5l1-1c1-1 0-3 0-4z" class="Y"></path><path d="M378 247c2 8 4 14 7 22l12 31c2 5 23 50 23 51-2 0-29-62-33-70-3-7-4-14-8-21-1 0-1-1-1-2v-1c0-1-1-1-1-2 0-2 0-6 1-8z" class="e"></path><defs><linearGradient id="S" x1="455.921" y1="421.276" x2="434.609" y2="444.741" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#303031"></stop></linearGradient></defs><path fill="url(#S)" d="M426 419v-1c0-1 0-2-1-3v-7h1c1-1 2-1 3-2h4c1-1 1-1 3 0l2-1h2c2 3 4 7 6 10 2 4 4 8 7 12h0v-2c1 1 4 7 4 9 1 1 1 0 1 1 0 3 2 7 3 9 1 1 0 0 1 2v1h0c1 2 2 5 4 6 0-1-1-1-1-2v-1c-1-1-1-1-1-2h-1l1-1c-1-1-1-2-2-3v-1h0c-1-2-1-4-2-6-1-1-1-2-1-3l-1-1v-1c-1-1-1-2-1-2v-1-2c-1-1 0-1 0-2 0 1 1 2 1 3l1 4 3 7c0 2 1 4 2 6l1 2c0 1 1 3 1 4s1 2 2 3v1l2 2h0c0 2 1 3 2 4h0c0 1 1 4 1 4 1 1 4 3 3 4-1 0-2-1-3-1 0 3 2 7 3 10l4 10-2-1c0-4-9-6-12-8s-5-4-7-6h0c0-1-1-2-1-2-1-1-1-2-2-2v-1c0-1 0-1-1-2v-1h-1c-1 0-2-1-3-2l-3-3-1-2c-1-2-1-3-3-4h0c-2-1-4-4-5-6v-1c-1 0-1 0-1 1h0c-1 0-2-2-2-3-1 1-2 1-2 3l-1-1c-4-9-6-19-7-28z"></path><path d="M440 443h0c-1-2-2-3-2-5v-1h-1c-1-4-2-8-3-11-1-2-1-3-1-5h1c1 1 2 1 3 2s2 3 3 4c0 0 1 0 2 1 1 2 3 5 4 8 2 2 3 5 4 7 2 4 5 6 6 9h-1c-1 0-1 0-2-1l-1-1h-1c-1-1-4-4-5-6-1-1-2-2-3-4h-1s0 1-1 2v1l-1-1v1z" class="J"></path><path d="M440 427s1 0 2 1c1 2 3 5 4 8 2 2 3 5 4 7 2 4 5 6 6 9h-1c-1-2-4-3-5-4v-1c-1-1-1-1-1-2l-1-1c-1-1-1-2-2-3s-2-2-2-4c-1-1-2-2-2-4 0 0-1-1-1-2v-1c-1-1-1-2-1-3z" class="S"></path><defs><linearGradient id="T" x1="470.813" y1="459.514" x2="448.237" y2="465.608" xlink:href="#B"><stop offset="0" stop-color="#140f0f"></stop><stop offset="1" stop-color="#404345"></stop></linearGradient></defs><path fill="url(#T)" d="M462 447c1 2 2 5 4 6 0-1-1-1-1-2v-1c-1-1-1-1-1-2h-1l1-1c-1-1-1-2-2-3v-1h0c-1-2-1-4-2-6-1-1-1-2-1-3l-1-1v-1c-1-1-1-2-1-2v-1-2c-1-1 0-1 0-2 0 1 1 2 1 3l1 4 3 7c0 2 1 4 2 6l1 2c0 1 1 3 1 4s1 2 2 3v1l2 2h0c0 2 1 3 2 4h0c0 1 1 4 1 4 1 1 4 3 3 4-1 0-2-1-3-1 0 3 2 7 3 10l4 10-2-1c0-4-9-6-12-8s-5-4-7-6h0c0-1-1-2-1-2-1-1-1-2-2-2v-1c0-1 0-1-1-2v-1h-1c-1 0-2-1-3-2l-3-3-1-2c-1-2-1-3-3-4-1-1-1-3-2-5l-2-6v-1l1 1v-1c1-1 1-2 1-2h1c1 2 2 3 3 4 1 2 4 5 5 6h1l1 1c1 1 1 1 2 1h1c1 1 1 1 2 1l2 2 2 2v-1h0l1-1 1 2c1 1 2 1 3 1-1-3-4-4-4-6-1-1-1-2-1-3h0v-2z"></path><path d="M440 443v-1l1 1v-1l9 15c1 2 3 4 4 7v1c-1 0-2-1-3-2l-3-3-1-2c-1-2-1-3-3-4-1-1-1-3-2-5l-2-6z" class="d"></path><path d="M442 449c2 2 3 3 4 6h0c3 2 4 5 5 8l-3-3-1-2c-1-2-1-3-3-4-1-1-1-3-2-5z" class="F"></path><defs><linearGradient id="U" x1="691.45" y1="302.5" x2="551.412" y2="250.605" xlink:href="#B"><stop offset="0" stop-color="#080808"></stop><stop offset="1" stop-color="#343435"></stop></linearGradient></defs><path fill="url(#U)" d="M630 181h21v1h-8c1 1 4 1 5 2 2 0 3 0 4 1h1c1 0 3 1 4 2h1c1 1 1 1 2 1 1 1 2 1 3 2h0v1c1 0 2 1 3 1 0 0 1 1 2 1l1 1h1l1 1c2 2 5 3 7 4 2 2 7 5 8 8-1 2-1 3-2 4l2 4v1 1c1 1 1 2 2 3 2 3 4 5 6 8 5 10 6 21 5 32 1 1 1 2 1 4v4s1-1 2-1 0 0 1-1h0v-1c-1-1-1-1-1-2h0l-1-1 1-1v-4h0c0 2 0 4 1 6 1 1 1 1 2 1 1-1 2-1 3-2l2 1-6 7v-2c-2 1-2 1-3 3-1 1-2 5-2 7l1 2-7 24-3 8-1-1c-2 4-5 8-5 13 0 2-1 5-2 7 0 1-1 1-1 2s0 1-1 2l-3 7c-1 1-1 2-2 4h0l-2 3-1 2c-1 3-3 7-5 10l-5 7-1 1-2 3c-1 1-3 2-4 4l-2 2-6 6h-1l9-37 8-40c2-17 4-34 0-50-5-21-19-39-37-51-18-11-39-17-60-12 3-2 6-3 9-4 16-6 34-8 51-5l1-2 3 1c0-1 1-2 0-3z"></path><path d="M699 260c1 1 1 2 1 4v4s1-1 2-1 0 0 1-1h0v-1c-1-1-1-1-1-2h0l-1-1 1-1v-4h0c0 2 0 4 1 6 1 1 1 1 2 1 1-1 2-1 3-2l2 1-6 7v-2c-2 1-2 1-3 3-1 1-2 5-2 7l1 2-7 24-3 8-1-1c5-12 7-25 9-38l1-13z" class="N"></path><path d="M627 183l3 1c9 2 18 4 26 9 10 4 18 11 27 17 1-2 2-2 3-3-1 2-1 3-2 4l2 4v1 1c1 1 1 2 2 3-5-4-9-10-14-14-13-11-31-19-48-21l1-2z" class="M"></path><defs><linearGradient id="V" x1="662.618" y1="212.367" x2="646.888" y2="174.84" xlink:href="#B"><stop offset="0" stop-color="#3a393b"></stop><stop offset="1" stop-color="#676767"></stop></linearGradient></defs><path fill="url(#V)" d="M630 181h21v1h-8c1 1 4 1 5 2 2 0 3 0 4 1h1c1 0 3 1 4 2h1c1 1 1 1 2 1 1 1 2 1 3 2h0v1c1 0 2 1 3 1 0 0 1 1 2 1l1 1h1l1 1c2 2 5 3 7 4 2 2 7 5 8 8-1 1-2 1-3 3-9-6-17-13-27-17-8-5-17-7-26-9 0-1 1-2 0-3z"></path><path d="M680 203c1 1 2 1 3 2v2h-2v-1c-1-1-1-1-1-3z" class="C"></path><path d="M626 195h2l1 1c2 0 0-1 2 0l1 1c2 0 4 2 5 2s2-1 4-1c1 0 2 0 4 1l1 1c3 0 5 2 8 4 1 1 2 3 4 4s4 2 6 5c1 2 1 2 1 4h1 0l1 2s-1 0-1 1c0 0 1 2 1 3l-2 1 1 2 2 4 3 2h0c2 1 3 5 4 7-3-3-5-7-8-10l-1-1c-1-2-4-8-6-9l-1 1c1 1 2 2 2 3l-1 1-1 1 5 5 1-2 1 1-1 2-1-1h-1l-2-1c-5-1-11-8-15-12-1-1-2-1-3-2-2-2-4-5-6-7-3 0-5 0-7-2v-1l-2-2v-6h1c-1-1-2-1-3-2z" class="F"></path><path d="M279 232c2 0 2 1 2 3l1 1c0 1 0 2 1 3h1l2 2 1 1 3 2c1 0 1 0 3 1l3 3c1 0 1 0 2 1h1l2 2c1 1 4 3 6 5h3v-1l-1-1c0-2 1-2 0-4h-1c1-2 1-2 2-2v2h0l1 1c1 1 1 1 2 1h0l1 2h-1c0 1 0 2 1 3 1-1 1-1 2 0h1 1 1c1 1 2 1 2 2 1 2 3 3 4 5s1 2 3 3l1 1h1 1c0 1 0 1 1 1 2 1 4 4 5 5-1 0-1 0-2 1h2v1c1 0 2 1 3 1 2 2 3 2 5 3 2 2 2 2 4 3h1c2 1 2 1 4 1 0 0 1 1 3 2 0 0 1 0 2 1h1 0l-1-2-1-1c-2-1-3-3-4-4h2l2 2c0-3 1-3 2-5h1c0-1 1-2 2-2 0 1 0 1 1 2v-1-4h1c1-3 1-6 3-8 1-2 3-5 5-6s3-1 5 0c0 1 0 2 1 2 4 7 5 14 8 21 4 8 31 70 33 70 1 1 2 2 2 3l1 2h0l1 2v2 1c1 0 1 2 2 3h0c1 3 0 0 1 2 0 2 2 6 3 8l2 5h0v1c0 2 1 3 2 4 1 2 2 4 4 6 1 4 3 6 3 9v1 4c1 2 5 9 5 11-2-3-4-7-6-10h-2l-2 1c-2-1-2-1-3 0h-4c-1 1-2 1-3 2h-1v7c1 1 1 2 1 3v1c-1-2-1-6-2-8h-1v1 2c-1 1 0 2 0 3-2-1-3-8-4-10s-2-1-3-2c0-1-1-2-1-3-2-1-4-4-5-6 0-1 1-2 0-3h-2-1v1c-1-1-3-3-4-5s-3-3-4-6l-1-3h-1v3h0c-1-1-2-1-3-2 0 0-1-1-1-2-2 0-2-2-4-2v2 2 1c-2-1-3-1-4-2-1-2-1-5-2-8l-1-3c0-2 0-4-1-6-1 0-1 0-2-1-2-1-3-3-5-5-1-1-1-1-3-1l-3-3-3 1c2 1 4 2 5 4-1 1-1 2-1 3-1-2-2-3-4-4-1 0-2 1-4 1l-22-30c-1-1-3-3-4-5l-14-20-4-5-9-11-13-16c1-1 1-2 0-4l-1 1c-1-2-1-4-2-6h0v-1c-3-3-5-6-7-9l-1-2-4-5-1-2v-1h1 0v-3c-1 0-2-1-3-1h0c2-2 2-4 2-6z" class="p"></path><path d="M408 343c4 3 7 9 8 14-2-2-4-5-5-7l-3-7zm-8-19c2 4 4 8 5 11s3 5 2 8c-1-2-3-4-4-6l-3-9v-4z" class="I"></path><path d="M362 305c2 2 3 2 4 5l2-1v2c1 1 2 2 2 3v2l1 1-1 1v-1c-1-1-2-2-3-2h-1s-1-2-2-2c-1-3 0-4-2-6v-2z" class="C"></path><path d="M383 322c-2-1-4-3-5-4-1-3-2-2-3-4-1-1-2-3-3-4 1-3 1-4 2-6 0 4 1 7 4 10l1 2c1 1 1 0 1 1l1 1c0 1 1 2 2 2v2z" class="I"></path><path d="M370 273v-2c0-2 0-3 1-4 2 2 3 3 4 6h1l2-1c1 2 3 7 3 10-1 1-1 0-2 0s-2 1-2 2l-1-1v1l-1-1h0c-1-1-1-1-2-1-1-1-2-1-3-2h0c-1-1-1-2-2-3 2-1 2-2 2-4z" class="h"></path><path d="M370 273v-2c0-2 0-3 1-4 2 2 3 3 4 6h0l3 6v1c-1-1-2-1-2-3h-2l-1-3h-1v-2l-2 1z" class="C"></path><path d="M370 273l2-1v2h1l1 3 1 2 1 3s-1 0-1 1h0c-1-1-1-1-2-1-1-1-2-1-3-2h0c-1-1-1-2-2-3 2-1 2-2 2-4z" class="E"></path><path d="M375 279l-1 1c-1-1-1-2-2-2l-1-1c0-1 1-2 2-3l1 3 1 2z" class="S"></path><path d="M380 294c-1-1-1-1-1-2 1 0 1-1 2-1h1c0-1 1-1 2-2h0c2 2 3 3 3 5l8 16c2 4 2 8 4 12 0 1 0 2 1 2v4c-2-1-3-4-4-6 0-1-2-3-3-4s-2-3-2-5c0-1-1-1-1-2 0-2 0-3-1-4 0-1-1-1-1-2l-1-4c-1-2-1-4-2-5s-1-2-1-3h-1v3h-1v-3h-1l-1 1z" class="E"></path><path d="M380 317h2l4 3c2 2 4 5 7 7h0c1 1 3 2 4 4h1l1 2c0-1-1-2-1-3-2-2-3-5-4-7v-1c-1-1-1-3-1-4 1 1 3 3 3 4 1 2 2 5 4 6l3 9c1 2 3 4 4 6h1l3 7h0c0 2 0 3 1 4v1l-1-1c0-1-1-1-2-2h0c-1-1-1-1-1-2l-2-2-2-3-6-9-2-1-3-3c-4-2-7-7-10-10v-2c-1 0-2-1-2-2l-1-1z" class="F"></path><path d="M383 322l2-1 5 5c0 1 1 2 2 2 2 2 3 3 4 5 1 1 2 2 2 3l-2-1-3-3c-4-2-7-7-10-10z" class="I"></path><path d="M368 264h0c0 2 0 2-1 4-1 4 0 5 1 9 1 1 1 2 2 3h0c1 1 2 1 3 2 1 0 1 0 2 1h0l1 1c1 1 2 3 3 4l-2 2h-2l-4 1c-1 1-4 2-6 2l-3 1-1-2-2-2-2-4s1 0 2 1h1 0l-1-2-1-1c-2-1-3-3-4-4h2l2 2c0-3 1-3 2-5h1c0-1 1-2 2-2 0 1 0 1 1 2v-1-4h1c1-3 1-6 3-8z" class="D"></path><path d="M365 293h0l1-2c1-1 2-2 3-2v-1-1c-1 0-1 0-2-1-2-1-3-2-5-3l1-1c1 0 2 1 3 2h1 0l-2-2 1-1 1 1 2 2h1v3h2l-1 4c-1 1-4 2-6 2z" class="B"></path><path d="M354 280h2l2 2 3 3 1 1-1 1c1 1 2 1 3 1h0c0 1 0 1 1 2h-1v2h-3l-2-2-2-4s1 0 2 1h1 0l-1-2-1-1c-2-1-3-3-4-4z" class="r"></path><path d="M368 264h0c0 2 0 2-1 4-1 4 0 5 1 9 1 1 1 2 2 3h0c1 1 2 1 3 2 1 0 1 0 2 1h0l1 1c1 1 2 3 3 4l-2 2h-2l-4 1 1-4h-2v-3h1v-1c-2-1-2-1-3-3v-1l-1 1-1-1v-3c-1-2-1-3-1-4 1-3 1-6 3-8z" class="F"></path><path d="M375 283h0l1 1c1 1 2 3 3 4l-2 2c-1-1-1-1-2-1s-1-1-2-1v-2h-1c0-2 2-2 3-3z" class="I"></path><path d="M380 294l1-1h1v3h1v-3h1c0 1 0 2 1 3s1 3 2 5l1 4c0 1 1 1 1 2 1 1 1 2 1 4 0 1 1 1 1 2 0 2 1 4 2 5 0 1 0 3 1 4v1c1 2 2 5 4 7 0 1 1 2 1 3l-1-2h-1c-1-2-3-3-4-4h0c-3-2-5-5-7-7l-4-3h-2c0-1 0 0-1-1l-1-2c-3-3-4-6-4-10l1-1c1-2 3-4 4-6 0-1 0-2 1-3z" class="L"></path><path d="M385 296c1 1 1 3 2 5l1 4c0 1 1 1 1 2 1 1 1 2 1 4-1-1-1-2-2-3-1-2-3-4-3-6v-1c-1-2-1-3 0-5zm-10 7v2l2 1h0c0 3 0 4 2 6s2 2 3 5h-2c0-1 0 0-1-1l-1-2c-3-3-4-6-4-10l1-1z" class="G"></path><path d="M377 306c0-2 0-3 1-4s1-1 1-2 1-2 2-2v1c1 2 1 5 2 8 1 1 2 2 2 3 1 1 0 1 1 2 0 1 0 2 1 3 0 1 1 2 1 2v1c1 2 2 3 2 5-2-3-6-6-7-9-1-2-5-7-6-8h0z" class="V"></path><path d="M411 350c1 2 3 5 5 7 1 2 2 5 4 6l4 4c3 3 4 9 8 13 0 2 1 3 2 4 1 2 2 4 4 6 1 4 3 6 3 9v1 4c1 2 5 9 5 11-2-3-4-7-6-10h-2l-2 1c-2-1-2-1-3 0h-4c-1 1-2 1-3 2h-1v7c1 1 1 2 1 3v1c-1-2-1-6-2-8h-1v-12c-1-3-1-4-2-7h3v-1c1 0 1-1 1-2 0-3-1-5-2-8-1-6-5-13-8-18-1-2-2-3-3-5v-1l-1-1c-1-2-1-3-2-4 1 1 2 1 2 2l1 1v-1c-1-1-1-2-1-4h0z" class="N"></path><path d="M421 392h3l1 2c-1 1 0 2 0 2-1 1-1 1-1 2l-1 1h0l-2-7z" class="a"></path><path d="M411 350c1 2 3 5 5 7 1 2 2 5 4 6 0 2 1 4 2 5 1 2 2 4 2 6 1 1 1 2 2 3 1 2 1 4 2 6l5 7c1 3 2 4 2 6-6-6-10-15-14-23-1-4-3-8-6-10-1-2-2-3-3-5v-1l-1-1c-1-2-1-3-2-4 1 1 2 1 2 2l1 1v-1c-1-1-1-2-1-4h0z" class="J"></path><path d="M424 411v-8-1c1-2 1-4 3-5 1-1 1-1 3-1 4 1 7 6 10 9h-2l-2 1c-2-1-2-1-3 0h-4c-1 1-2 1-3 2h-1v7c1 1 1 2 1 3v1c-1-2-1-6-2-8z" class="n"></path><path d="M420 363l4 4c3 3 4 9 8 13 0 2 1 3 2 4 1 2 2 4 4 6 1 4 3 6 3 9v1 4l-6-8c0-2-1-3-2-6l-5-7c-1-2-1-4-2-6-1-1-1-2-2-3 0-2-1-4-2-6-1-1-2-3-2-5z" class="E"></path><defs><linearGradient id="W" x1="401.539" y1="369.273" x2="413.813" y2="357.771" xlink:href="#B"><stop offset="0" stop-color="#1a1a1b"></stop><stop offset="1" stop-color="#383839"></stop></linearGradient></defs><path fill="url(#W)" d="M371 291l4-1c-2 1-5 4-8 4l-1 4c-1 1-1 1-1 2h-1v-1h-1c0 2 0 5-1 6v2c2 2 1 3 2 6 1 0 2 2 2 2h1c1 2 1 3 2 5 1 0 2 0 3 1 1 2 3 4 5 5 1 0 2 1 4 2 0 1 1 1 2 1 4 3 7 6 10 9 3 2 6 4 7 7l2 1h0c0 2 1 2 2 3v-4l2 3 2 2c0 1 0 1 1 2h0c1 1 1 2 2 4l1 1v1c1 2 2 3 3 5 3 5 7 12 8 18 1 3 2 5 2 8 0 1 0 2-1 2v1h-3c1 3 1 4 2 7v12 1 2c-1 1 0 2 0 3-2-1-3-8-4-10s-2-1-3-2c0-1-1-2-1-3-2-1-4-4-5-6 0-1 1-2 0-3h-2-1v1c-1-1-3-3-4-5s-3-3-4-6l-1-3h-1v3h0c-1-1-2-1-3-2 0 0-1-1-1-2-2 0-2-2-4-2v2h-1v-2c0-3 0-5-1-7 0-3-2-5-1-8-1-2-1-2-1-4h0l-2-3h-2l1-1h0l-2-4h-2c-2-1-1-1-2-2-1-3-4-4-5-6l-1-2c-1-1-2-1-3-2 1-1 1-2 2-4h0v-1l-1-2v-1h-1c-1-1 0-2 0-3-1-1-1-2-1-4l6 8 1-1c-2-3-5-6-6-9-4-6-6-12-7-18h-1c-2 0-2-1-3-2 1-1 1-2 3-3 1-1 1-2 2-4h1l3-1c2 0 5-1 6-2z"></path><path d="M406 375c2 1 3 3 6 4h0 2 1c2 0 4 3 5 5h0l1 2 1-1v1h2v-1c-1-1-1-3-1-4 1 3 2 5 2 8 0 1 0 2-1 2-2-3-6-5-9-7-4-3-7-6-9-9z" class="E"></path><path d="M373 330c5 5 10 11 16 15v1c1 1 3 1 4 2s1 1 1 3h-1l-2-2h-1c-3 0-7-4-9-6l21 29h0l-30-41 1-1z" class="W"></path><path d="M371 291l4-1c-2 1-5 4-8 4l-1 4c-1 1-1 1-1 2h-1v-1h-1c0 2 0 5-1 6v2c2 2 1 3 2 6 1 0 2 2 2 2h1c1 2 1 3 2 5 1 0 2 0 3 1 1 2 3 4 5 5 1 0 2 1 4 2 0 1 1 1 2 1 4 3 7 6 10 9v2l3 3v1c0 1 1 2 2 2 1 1 1 2 2 4l2 3v2h1v1l2-1v1h2c1 2 2 3 3 5v1c2 2 2 5 4 8h0v1c-2-2-5-3-6-5v-1h-2c-1-1-1-1-2-1v-1c-2-3-6-6-7-9 0-2-1-4-2-6h-2c-1-1-3-1-4-2v-1c-6-4-11-10-16-15-2-3-5-6-6-9-4-6-6-12-7-18h-1c-2 0-2-1-3-2 1-1 1-2 3-3 1-1 1-2 2-4h1l3-1c2 0 5-1 6-2z" class="S"></path><path d="M402 355h1v1l2 1h0v1l1 1c1 1 2 2 2 3l-1 1-1-1c-2-2-2-2-2-3-1-1-2-2-2-4z" class="J"></path><path d="M371 291l4-1c-2 1-5 4-8 4-2 1-6 3-7 5-1 1-1 2 0 4h-1c-2 0-2-1-3-2 1-1 1-2 3-3 1-1 1-2 2-4h1l3-1c2 0 5-1 6-2z" class="W"></path><path d="M367 321c1 1 2 1 3 3 2 2 5 4 7 7 1 1 1 1 2 1l1-1c4 3 8 7 12 11l6 6c1 2 2 3 3 5v1c-3-2-3-5-6-6h-2c-1-1-3-1-4-2v-1c-6-4-11-10-16-15-2-3-5-6-6-9z" class="G"></path><path d="M366 323l6 8 30 41h0l4 3c2 3 5 6 9 9 3 2 7 4 9 7v1h-3c1 3 1 4 2 7v12 1 2c-1 1 0 2 0 3-2-1-3-8-4-10s-2-1-3-2c0-1-1-2-1-3-2-1-4-4-5-6 0-1 1-2 0-3h-2-1v1c-1-1-3-3-4-5s-3-3-4-6l-1-3h-1v3h0c-1-1-2-1-3-2 0 0-1-1-1-2-2 0-2-2-4-2v2h-1v-2c0-3 0-5-1-7 0-3-2-5-1-8-1-2-1-2-1-4h0l-2-3h-2l1-1h0l-2-4h-2c-2-1-1-1-2-2-1-3-4-4-5-6l-1-2c-1-1-2-1-3-2 1-1 1-2 2-4h0v-1l-1-2v-1h-1c-1-1 0-2 0-3-1-1-1-2-1-4z" class="p"></path><path d="M369 334c1 2 2 3 2 4 1 2 2 3 3 4 2 2 3 4 5 7l1 1h-2c-2-1-1-1-2-2-1-3-4-4-5-6l-1-2c-1-1-2-1-3-2 1-1 1-2 2-4z" class="F"></path><path d="M402 372l4 3c2 3 5 6 9 9 3 2 7 4 9 7v1h-3l-19-20h0z" class="Y"></path><path d="M386 362h0 1c0 2 1 3 2 4v1l2 3 1 1c1 0 1 2 2 3h0v1l1-1c3 2 5 5 6 8 2 3 4 6 6 10v1 1c-1-1-3-3-4-5s-3-3-4-6l-1-3h-1v3h0c-1-1-2-1-3-2 0 0-1-1-1-2-2 0-2-2-4-2v2h-1v-2c0-3 0-5-1-7 0-3-2-5-1-8z" class="S"></path><path d="M386 362h0 1c0 2 1 3 2 4v1l2 3 1 1c1 0 1 2 2 3h0l-2-2v1 2h-1c-1-2-1-4-3-5v1c1 2 1 4 0 6 0-3 0-5-1-7 0-3-2-5-1-8z" class="I"></path><defs><linearGradient id="X" x1="308.2" y1="304.392" x2="326.903" y2="266.906" xlink:href="#B"><stop offset="0" stop-color="#303131"></stop><stop offset="1" stop-color="#545353"></stop></linearGradient></defs><path fill="url(#X)" d="M279 232c2 0 2 1 2 3l1 1c0 1 0 2 1 3h1l2 2 1 1 3 2c1 0 1 0 3 1l3 3c1 0 1 0 2 1h1l2 2c1 1 4 3 6 5h3v-1l-1-1c0-2 1-2 0-4h-1c1-2 1-2 2-2v2h0l1 1c1 1 1 1 2 1h0l1 2h-1c0 1 0 2 1 3 1-1 1-1 2 0h1 1 1c1 1 2 1 2 2 1 2 3 3 4 5s1 2 3 3l1 1h1 1c0 1 0 1 1 1 2 1 4 4 5 5-1 0-1 0-2 1h2v1c1 0 2 1 3 1 2 2 3 2 5 3 2 2 2 2 4 3h1c2 1 2 1 4 1 0 0 1 1 3 2l2 4 2 2 1 2h-1c-1 2-1 3-2 4-2 1-2 2-3 3 1 1 1 2 3 2h1c1 6 3 12 7 18 1 3 4 6 6 9l-1 1-6-8c0 2 0 3 1 4 0 1-1 2 0 3h1v1l1 2v1h0c-1 2-1 3-2 4 1 1 2 1 3 2l1 2c1 2 4 3 5 6 1 1 0 1 2 2h2l2 4h0l-1 1h2l2 3h0c0 2 0 2 1 4-1 3 1 5 1 8 1 2 1 4 1 7v2h1v2 1c-2-1-3-1-4-2-1-2-1-5-2-8l-1-3c0-2 0-4-1-6-1 0-1 0-2-1-2-1-3-3-5-5-1-1-1-1-3-1l-3-3-3 1c2 1 4 2 5 4-1 1-1 2-1 3-1-2-2-3-4-4-1 0-2 1-4 1l-22-30c-1-1-3-3-4-5l-14-20-4-5-9-11-13-16c1-1 1-2 0-4l-1 1c-1-2-1-4-2-6h0v-1c-3-3-5-6-7-9l-1-2-4-5-1-2v-1h1 0v-3c-1 0-2-1-3-1h0c2-2 2-4 2-6z"></path><path d="M280 245c1 0 2 0 2 1 1 1 2 1 4 2l1 1-2 3-1-2-4-5z" class="B"></path><path d="M307 268l2-1c1 2 2 2 3 4s3 4 5 6v2l-10-11z" class="G"></path><path d="M350 326l-2-2h1c1 1 2 2 3 2l2-2h1v1c0 2 1 2 2 3v2l-3-1v1h-1l-1-1c0-1-2-2-2-3z" class="E"></path><path d="M353 330v-2-3h2c0 2 1 2 2 3v2l-3-1v1h-1z" class="F"></path><path d="M279 232c2 0 2 1 2 3l1 1c0 1 0 2 1 3h1l2 2 1 1 2 3 1 1c0 1 2 2 3 3h-1c0 1 1 2 2 2 2 1 2 2 4 3 1 1 2 1 2 2h-1l-9-7c-2-2-4-4-7-5h-2c-1-1-1-1-2-1v-1h1 0v-3c-1 0-2-1-3-1h0c2-2 2-4 2-6z" class="C"></path><path d="M292 261v-1c0-1 0-1-1-2h-1l1-1h0l1 1 1-1c1 1 2 2 2 3 1 1 2 1 3 2v-1-1-1c2 1 3 2 3 4 1 1 2 1 2 3 1 0 2 2 3 2h1l10 11c0 1 0 1 1 2 2 1 2 2 3 5 1 1 2 2 2 3 1 1 1 2 1 2v2l-1-1h0l1 3h-1c0-1-1-2-2-2v1h0l-1 1 1 1 2 2c1 3 5 7 5 10-2-2-3-4-5-6-1 0-2 0-2 1l-4-5-9-11-13-16c1-1 1-2 0-4l-1 1c-1-2-1-4-2-6h0v-1z" class="J"></path><path d="M292 258l1-1c1 1 2 2 2 3 1 1 2 1 3 2v-1-1-1c2 1 3 2 3 4 1 1 2 1 2 3 1 0 2 2 3 2 0 1 1 2 2 3 0 1 1 1 1 2s0 1 1 2c3 3 5 6 8 10v2h0v1c-3-2-5-4-7-6-1-2-2-4-3-5-2-1-4-3-4-5-1-1-1-1-1-2s-1-1-1-2c-1-1-1-2-1-3-3-2-7-4-9-7z" class="G"></path><defs><linearGradient id="Y" x1="300.468" y1="285.678" x2="317.646" y2="280.87" xlink:href="#B"><stop offset="0" stop-color="#1c1d1c"></stop><stop offset="1" stop-color="#3e3e3f"></stop></linearGradient></defs><path fill="url(#Y)" d="M295 267h0c0-1-1-2-1-3h1c3 2 4 6 6 9l4 4c1 1 1 1 1 2 1 2 4 5 6 7s6 9 9 10l2 2c1 3 5 7 5 10-2-2-3-4-5-6-1 0-2 0-2 1l-4-5-9-11-13-16c1-1 1-2 0-4z"></path><path d="M323 298c2 0 2 1 3 3l1 1c2 4 5 6 8 9 2 2 3 4 5 7 2 1 7 5 7 8 2-1 2 0 3 0 0 1 2 2 2 3l1 1h1v-1l3 1 5 5h2l3 3c1 1 2 1 3 2l1 2c1 2 4 3 5 6 1 1 0 1 2 2h2l2 4h0l-1 1h2l2 3h0c0 2 0 2 1 4-1 3 1 5 1 8 1 2 1 4 1 7v2h1v2 1c-2-1-3-1-4-2-1-2-1-5-2-8l-1-3c0-2 0-4-1-6-1 0-1 0-2-1-2-1-3-3-5-5-1-1-1-1-3-1l-3-3-3 1c2 1 4 2 5 4-1 1-1 2-1 3-1-2-2-3-4-4-1 0-2 1-4 1l-22-30c-1-1-3-3-4-5l-14-20c0-1 1-1 2-1 2 2 3 4 5 6 0-3-4-7-5-10z" class="h"></path><path d="M347 326c2-1 2 0 3 0 0 1 2 2 2 3v2h-1c-1-2-3-4-4-5z" class="I"></path><path d="M379 362c2 0 2 1 4 1 1 3 1 5 2 8l-3-2c0-2 0-4-1-6-1 0-1 0-2-1z" class="S"></path><path d="M382 369l3 2c1 2 2 5 2 8h1 1v2 1c-2-1-3-1-4-2-1-2-1-5-2-8l-1-3z" class="F"></path><path d="M347 331c2 2 4 4 6 5 2 2 3 2 5 4 2 3 5 5 7 9 1 1 2 2 3 2h1l2 2 2 2c1 0 1 2 1 2-1-1-1-1-3-1l-3-3c-2-1-3-2-4-3-6-6-13-12-17-19z" class="C"></path><path d="M354 330v-1l3 1 5 5h2l3 3c1 1 2 1 3 2l1 2c1 2 4 3 5 6 1 1 0 1 2 2h2l2 4h0l-1 1-1 1-4-5c-1 0-1 0-2-1l-20-20z" class="B"></path><path d="M321 303c0-1 1-1 2-1 2 2 3 4 5 6s4 4 5 6l1 1c3 4 5 7 8 10 1 1 4 4 4 5l1 1c4 7 11 13 17 19 1 1 2 2 4 3l-3 1c2 1 4 2 5 4-1 1-1 2-1 3-1-2-2-3-4-4-1 0-2 1-4 1l-22-30c-1-1-3-3-4-5l-14-20z" class="F"></path><path d="M359 349c2 0 5 3 6 5h0c2 1 4 2 5 4-1 1-1 2-1 3-1-2-2-3-4-4l-6-8z" class="D"></path><path d="M346 330l1 1c4 7 11 13 17 19 1 1 2 2 4 3l-3 1h0c-1-2-4-5-6-5h0c-4-4-6-8-9-12-1-1-4-3-4-5-1-1 0-1 0-2zm-25-27c0-1 1-1 2-1 2 2 3 4 5 6s4 4 5 6l1 1c1 2 1 2 1 4l1 1h0c0 1 1 1 1 2 2 2 4 4 5 6l1 1c1 2 2 3 2 4v1c-2-2-3-5-5-6h-1c-1-1-3-3-4-5l-14-20z" class="B"></path><defs><linearGradient id="Z" x1="340.415" y1="283.131" x2="324.892" y2="294.419" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#414142"></stop></linearGradient></defs><path fill="url(#Z)" d="M287 242l3 2c1 0 1 0 3 1l3 3c1 0 1 0 2 1h1l2 2c1 1 4 3 6 5h3v-1l-1-1c0-2 1-2 0-4h-1c1-2 1-2 2-2v2h0l1 1c1 1 1 1 2 1h0l1 2h-1c0 1 0 2 1 3 1-1 1-1 2 0h1 1 1c1 1 2 1 2 2 1 2 3 3 4 5s1 2 3 3l1 1h1 1c0 1 0 1 1 1 2 1 4 4 5 5-1 0-1 0-2 1h2v1c1 0 2 1 3 1 2 2 3 2 5 3 2 2 2 2 4 3h1c2 1 2 1 4 1 0 0 1 1 3 2l2 4 2 2 1 2h-1c-1 2-1 3-2 4-2 1-2 2-3 3 1 1 1 2 3 2h1c1 6 3 12 7 18 1 3 4 6 6 9l-1 1-6-8c0 2 0 3 1 4 0 1-1 2 0 3h1v1l1 2v1h0c-1 2-1 3-2 4l-3-3h-2l-5-5v-2c-1-1-2-1-2-3v-1c0-1-1-1-2-2 0-1 0-1 1-2-1-1-2-2-2-4 0-1 0-1-1-2v3h-1c-1-3-2-6-4-9-3-2-7-7-8-10h0c-1-1-3-1-4-2v-1c-3-3-5-5-6-8-1-1-4-3-4-4v-1c0-1-1-2-1-2-1-1-3-2-4-4-2-4-6-7-9-10l-3-3c-1-1-2-1-3-1-1-1-2-3-3-4-1 0-2-1-2-2h1c0-1-1-1-2-2-2-1-2-2-4-3-1 0-2-1-2-2h1c-1-1-3-2-3-3l-1-1-2-3z"></path><path d="M351 308l3 3v2c1 1 1 2 1 4l1 1h1 0c0 1-1 2-2 2h0c-1-2 0-3-1-5l-3-6v-1z" class="C"></path><path d="M355 325l3 1c2 1 3 2 4 4l2 5h-2l-5-5v-2c-1-1-2-1-2-3z" class="S"></path><path d="M318 267c3 0 5 2 7 4 5 4 10 9 16 13h0c1 1 2 1 3 2l2 1 3 4c-1 0-2 0-3 1l-28-25z" class="M"></path><path d="M343 301l1-1v1c1 2 3 3 4 4l3 3v1l3 6c1 2 0 3 1 5h0c0 1 1 2 2 3 0 2 0 2 1 3l-3-1v-1c0-1-1-1-2-2 0-1 0-1 1-2-1-1-2-2-2-4 0-1 0-1-1-2v3h-1c-1-3-2-6-4-9l-1-1c0-2-3-4-3-6h1z" class="I"></path><path d="M343 301l1-1v1c1 2 3 3 4 4 0 0-1 1-1 2h0c-2-2-3-4-4-6z" class="h"></path><path d="M351 297c2 1 4 1 4 2l1 2h0c1 1 1 2 3 2h1c1 6 3 12 7 18 1 3 4 6 6 9l-1 1-6-8-2-3-4 4c0-3 1-5 1-7 0-7-5-15-10-20z" class="M"></path><path d="M290 244c1 0 1 0 3 1l3 3c1 0 1 0 2 1h1l2 2c1 1 4 3 6 5l4 3c1 0 1 0 1-1l12 10c2 1 3 2 4 4 2 2 4 3 6 5l2 2c3 2 6 4 8 7-1-1-2-1-3-2h0c-6-4-11-9-16-13-2-2-4-4-7-4-2-1-5-5-7-6l-21-17z" class="e"></path><path d="M307 256h3v-1l-1-1c0-2 1-2 0-4h-1c1-2 1-2 2-2v2h0l1 1c1 1 1 1 2 1h0l1 2h-1c0 1 0 2 1 3 1-1 1-1 2 0h1 1 1c1 1 2 1 2 2 1 2 3 3 4 5s1 2 3 3l1 1h1 1c0 1 0 1 1 1 2 1 4 4 5 5-1 0-1 0-2 1h2v1c1 0 2 1 3 1-1 1-3 1-4 2l-2-2c-2-2-4-3-6-5-1-2-2-3-4-4l-12-10c0 1 0 1-1 1l-4-3z" class="V"></path><path d="M340 277c2 2 3 2 5 3 2 2 2 2 4 3h1c2 1 2 1 4 1 0 0 1 1 3 2l2 4 2 2 1 2h-1c-1 2-1 3-2 4-2 1-2 2-3 3h0l-1-2c0-1-2-1-4-2l-5-5c1-1 2-1 3-1l-3-4-2-1c-2-3-5-5-8-7 1-1 3-1 4-2z" class="F"></path><path d="M346 287v-2c-1-1-1-1-1-2h2s1 1 2 1l1 1 1-1 2 2c-1 0-2 1-3 0s-2-1-3-1c2 2 4 4 5 7l1 1-4-2-3-4z" class="d"></path><path d="M346 292c1-1 2-1 3-1l4 2c2 2 2 3 5 2l3-1c-1 2-1 3-2 4-2 1-2 2-3 3h0l-1-2c0-1-2-1-4-2l-5-5z" class="N"></path><path d="M340 277c2 2 3 2 5 3 2 2 2 2 4 3h1c2 1 2 1 4 1 0 0 1 1 3 2l2 4h-1c0-1-1-1-1-2-2 0-3-1-4-2l-2-2-1 1-1-1c-1 0-2-1-2-1h-2c0 1 0 1 1 2v2l-2-1c-2-3-5-5-8-7 1-1 3-1 4-2z" class="D"></path><path d="M350 600h0l3 9c4 8 10 15 15 22 13 16 28 33 47 43l3 1c3 1 4 4 7 6s6 4 9 5c2 1 4 3 6 3l1-1c-1-1-1-1-1-2l3 1 3-3 2 3c2 4 4 9 4 14h0c2 3 5 8 5 12l1 1c2-5 3-11 5-16h0l-1 5-3 9c0 1-1 3-1 4s1 2 1 3l4 9c1 5 3 10 5 15h1v-1 2c-1 3 2 9 3 12l12 33c2 5 5 11 5 17l-7-1-6-1c-13-3-26-7-37-13-5-3-9-6-14-9-15-11-30-25-42-40-7-10-14-20-20-30-1-2-2-5-3-7l-7-12c-3-7-5-14-7-21 0-1-2-5-2-7l-2-11-1 1h-1c-2-2-2-8-3-11 1-1 0-3 0-4-1-1-2-2-3-2h-1v-1-4c1-2 1-4 2-6 0-2 1-4 1-5h1c1-3 3-4 4-6l1-1h0c1-1 1-2 2-2l1 1h2l3 3 1-1-1-1c-2-2-3-4-4-6h-1c0-2-1-3-2-4v-1h0 1c1 2 2 3 4 4l1 1h1c-1-2-2-4-2-6v-1l1-2 1 1v-1z" class="c"></path><path d="M434 697c2 1 2 1 4 1l1 1c2 0 3 0 4 1h1 2 1l1-1h3c0 1 0 2 1 2h0v1c-3 0-5 0-6 2-1 3 1 7 1 10-2-4-3-10-6-12-2-2-5-2-7-3v-2zm-78-62c3-1 3-1 5 1 1 1 2 2 3 4 1 5 4 10 6 15 1 4 5 9 4 14-5-11-9-24-17-34h-1z" class="K"></path><path d="M463 698h0l-1 5-3 9c0 1-1 3-1 4s1 2 1 3l4 9c1 5 3 10 5 15h1v-1 2c-1 3 2 9 3 12l12 33c2 5 5 11 5 17l-7-1 2-1h1 3v-1l-1-1c0-1-1-3-1-4-1-1-1-1-1-2v-1l-2-5-1-5c-1-1-2-2-2-4l-2-6c-1-1-1-2-1-3l-1-1-1-6c-1-1-1-1-1-3l-2-4-2-6c-1-3-2-7-4-10l-3-6c0-2-2-4-3-6h0c0 3 1 5 2 7l2 7c0 1 1 2 1 3 1 1 0 1 0 2-1-2-2-3-2-5 0-1 0 0-1-1v-2l-1-2v-1c0-1 0-1-1-2v-1c-1-2-2-5-3-7 0-2-1-3-2-5v-1-3h0c-2-2-3-3-4-5l1-1c0-1-1-2-1-2-2-2-2-3-2-5v-2h-2v6l1 1c0 2 1 3 1 4 2 3 3 6 2 10 0-3-2-5-3-8-1-1-1-2-1-3 0-3-2-7-1-10 1-2 3-2 6-2v-1c2 3 5 8 5 12l1 1c2-5 3-11 5-16z" class="M"></path><path d="M452 701c2 3 5 8 5 12-1 0-2-1-3-1-1-3-1-6-2-9v-1-1z" class="W"></path><path d="M411 681v-1h-1l-1-1c2 0 4 1 6 2l2 1c1 1 2 1 3 1l4 2h2l3 1h2c-2-2-4-3-5-5h-1c3 2 6 4 9 5 2 1 4 3 6 3l1-1c-1-1-1-1-1-2l3 1 3-3 2 3c2 4 4 9 4 14-1 0-1-1-1-2h-3l-1 1h-1-2-1c-1-1-2-1-4-1l-1-1c-2 0-2 0-4-1-3-1-7-4-10-6-5-3-9-6-13-10z" class="j"></path><path d="M425 681c3 2 6 4 9 5 2 1 4 3 6 3l1-1c-1-1-1-1-1-2l3 1 3-3 2 3v4c0 1-1 2 0 3v1h-1c-7-1-17-5-23-10h2l3 1h2c-2-2-4-3-5-5h-1z" class="f"></path><defs><linearGradient id="a" x1="393.455" y1="640.807" x2="373.195" y2="657.828" xlink:href="#B"><stop offset="0" stop-color="#b00406"></stop><stop offset="1" stop-color="#ef2622"></stop></linearGradient></defs><path fill="url(#a)" d="M350 600h0l3 9c4 8 10 15 15 22 13 16 28 33 47 43l3 1c3 1 4 4 7 6h1c1 2 3 3 5 5h-2l-3-1h-2l-4-2c-1 0-2 0-3-1l-2-1c-2-1-4-2-6-2l1 1h1v1c-2 0-4-2-5-3-4-2-7-4-10-5-4-1-7 0-11-1-3-1-7-5-10-8 2 1 4 3 6 4s5 1 8 1l2-2c-2-5-7-9-11-13l-14-13-2-1c-1-2-2-3-3-4-2-2-2-2-5-1l-2-2c-1-1-3-1-4-2h-1c-1-1-1-1-2-1-3 1-4 3-5 5-2 5-1 14 0 19l-1 1h-1c-2-2-2-8-3-11 1-1 0-3 0-4-1-1-2-2-3-2h-1v-1-4c1-2 1-4 2-6 0-2 1-4 1-5h1c1-3 3-4 4-6l1-1h0c1-1 1-2 2-2l1 1h2l3 3 1-1-1-1c-2-2-3-4-4-6h-1c0-2-1-3-2-4v-1h0 1c1 2 2 3 4 4l1 1h1c-1-2-2-4-2-6v-1l1-2 1 1v-1z"></path><path d="M369 639c3 3 10 9 12 13l-1 2-14-13c1-1 2-1 3-2z" class="H"></path><path d="M396 673c0-1 0-2 2-3 3 1 5 3 8 4 1 1 14 9 14 9-1 0-2 0-3-1l-2-1c-2-1-4-2-6-2l1 1h1v1c-2 0-4-2-5-3-4-2-7-4-10-5z" class="f"></path><path d="M349 617c5 4 9 9 13 14 2 3 4 5 7 8-1 1-2 1-3 2l-2-1c-1-2-2-3-3-4-2-2-2-2-5-1l-2-2h1 1l1-1 1 1h2l1 1c-1-1-2-2-3-2-2-4-7-7-11-9l2-2-1-1v-1l1-2z" class="k"></path><path d="M342 615h1c1 1 2 1 4 1l2 1-1 2v1l1 1-2 2c4 2 9 5 11 9 1 0 2 1 3 2l-1-1h-2l-1-1-1 1h-1-1c-1-1-3-1-4-2h-1c-1-1-1-1-2-1-3 1-4 3-5 5-2 5-1 14 0 19l-1 1h-1c-2-2-2-8-3-11 1-1 0-3 0-4-1-1-2-2-3-2h-1v-1-4c1-2 1-4 2-6 0-2 1-4 1-5h1c1-3 3-4 4-6l1-1z" class="b"></path><path d="M342 615h1c1 1 2 1 4 1l2 1-1 2v1l1 1-2 2-1-1c-1 0-2 1-2 1h-1c-1 0-1 0-1 1-2 2-4 2-6 5l-1 2-2 2c1-2 1-4 2-6 0-2 1-4 1-5h1c1-3 3-4 4-6l1-1z" class="H"></path><path d="M336 629c1-5 4-6 8-9l1 1v1h1c-1 0-2 1-2 1h-1c-1 0-1 0-1 1-2 2-4 2-6 5z" class="j"></path><path d="M342 615h1c1 1 2 1 4 1l2 1-1 2-1-1-3 1h-1l-1 1c-1 0-3 2-4 3s-2 3-3 4c0-2 1-4 1-5h1c1-3 3-4 4-6l1-1z" class="K"></path><path d="M578 651c1 1 1 1 1 3h0c-1 1-2 2-2 3v1c0 1-1 2-1 3v3h0c-1 1 0 1-1 2v3l-1 1c0 1 1 2 1 2 1 2-1 8-2 10l-1-1h0l-2 9v1 1l-1-4v5l1 1 1-2v5c1 1 0 1 0 2v1l1-4 2 2-2 4c-1 1-2 3-2 5 1 1 1 1 2 0v1h-1c-2 3-3 7-4 10 3 1 6 4 10 4l3-1h0c-1 1-3 1-4 2 0 1-1 2-2 3l-1 1c0 2-2 3-3 4-2 1-4 2-5 4-1 3 0 5 1 8l-1-1-3-6-6 20-8 28c-2 6-3 12-6 17v4c-1 2-1 3-1 5-7 24-17 47-22 71-2 6-2 13-3 20l-2-11c-4-28-13-54-23-80l-2-4c0-6-3-12-5-17l-12-33c-1-3-4-9-3-12v-2 1h-1c-2-5-4-10-5-15l-4-9c0-1-1-2-1-3s1-3 1-4l3-9 1-5c1-1 1-2 1-3l5-18c1-7 2-17 6-22 7 7 9 17 11 27v-2-1l2-1h1l2 1 2 1c0 1 0 2 1 2v-1l5 5 2 2c4 2 7 5 11 7l9 6v1l2 3c1 1 2 2 4 2l3 3 1 1c1 1 1 1 2 1 1-1 2-2 4-2l1-2h3c2 0 3-2 5-3v2c-1 1-1 1-1 2h0 1c1-1 2-4 2-5h-2l-3-1 4-2c4-2 7-5 10-8 3-4 6-10 8-14 4-10 9-18 12-28l1 1v-1z" class="n"></path><path d="M501 734h1c1 2 1 5 1 7 1 6 1 14-1 21l-2 11-1 4c-1 1-1 1-1 2h0v-3c3-9 6-22 3-31v-1l-4 3c2-5 4-8 4-13z" class="Y"></path><path d="M520 775l1 6 6-14c1-1 2-3 2-4 0-2-1-3-1-5 5 8 6 18 6 27h0c0-4-1-8-2-12 0-2-1-5-2-6-12 12-12 33-12 49-1 6 0 11 0 17 0 18 1 37-2 55h-1l3-119 1 7h0l1-1z" class="W"></path><path d="M495 705c2 1 3 4 4 6 4 6 7 11 11 16 1 1 3 4 4 5 2 1 5 1 7 0 2 0 4-2 6-3v2c-1 2-1 3-2 4 2-1 3-3 5-6v2c-2 5-5 9-6 14-2 3-2 7-3 11-1 6-2 12-1 19l-1 1h0l-1-7c0-6 1-13 0-20-1-10-8-18-13-27l-10-17z" class="c"></path><path d="M527 731c-1 2-1 3-2 4l-3 3h-2c-1 0-3-2-4-3h3c3-1 5-3 8-4z" class="E"></path><defs><linearGradient id="b" x1="512.422" y1="793.113" x2="535.271" y2="808.349" xlink:href="#B"><stop offset="0" stop-color="#030303"></stop><stop offset="1" stop-color="#343535"></stop></linearGradient></defs><path fill="url(#b)" d="M518 816c0-16 0-37 12-49 1 1 2 4 2 6-1 1-2 3-2 4s-1 4 0 5l-4 24c-1 3-1 6-1 9v2l-1 3c-1 2-1 4-1 5 0-2 0-3-1-4v3l-1-1c0-1-1-1-1-2v-1c-1 2 0 5 0 8v6l-1 1v1l-1 1v-6-15z"></path><path d="M536 804c0-1 0 0 1-1v1 3c1 2 1 7 1 9h0v-1-1c1-1 1-2 2-3v-1-4l1-1c0-1 0-2 1-4h0v4c-1 2-1 3-1 5-7 24-17 47-22 71-2 6-2 13-3 20l-2-11h1v-1-1h1c3-18 2-37 2-55 0-6-1-11 0-17v15 6l1-1v-1l1-1v-6c0-3-1-6 0-8v1c0 1 1 1 1 2l1 1v-3c1 1 1 2 1 4v4l-1 22v4c2-2 3-9 4-12l7-18c1-3 2-5 3-8 1-4 1-8 0-13z" class="E"></path><path d="M520 828c0-3-1-6 0-8v1c0 1 1 1 1 2l1 1v-3c1 1 1 2 1 4v4l-1 22c-2 2 0 6-1 9l-1-1h0v1c-1-3-1-7-1-10l1-16v-6z" class="C"></path><path d="M520 828c0-3-1-6 0-8v1c0 1 1 1 1 2l1 1-1 13h0c-1-1 0-3 0-4-1-1-1 0-1-1v-4z" class="S"></path><path d="M520 828v4c0 1 0 0 1 1 0 1-1 3 0 4l-1 22h0v1c-1-3-1-7-1-10l1-16v-6z" class="J"></path><path d="M532 773c1 4 2 8 2 12h0c1 1 1 2 1 3 1 2 0 5 1 8v8c1 5 1 9 0 13-1 3-2 5-3 8l-7 18c-1 3-2 10-4 12v-4l1-22v-4c0-1 0-3 1-5l1-3v-2c0-3 0-6 1-9l4-24c-1-1 0-4 0-5s1-3 2-4z" class="d"></path><path d="M523 825c0-1 0-3 1-5l1-3c0 4 1 9 0 13l-1-1h0-1v-4z" class="F"></path><path d="M532 773c1 4 2 8 2 12v8 6-3h-1c-1-1 0-2 0-3-1-1-1-2-1-3h0v-1c0-1 0-2-1-3v-3l-1 1c0 1 0 1 1 2 1 5-1 10-2 14-1 3-1 7-2 10v2c0 1-1 2-1 3h-1c0-3 0-6 1-9l4-24c-1-1 0-4 0-5s1-3 2-4z" class="E"></path><path d="M532 773c1 4 2 8 2 12v8h-1c0-4 0-8-2-11h-1c-1-1 0-4 0-5s1-3 2-4z" class="C"></path><path d="M488 678h1l2 1c1 12 6 21 12 31l1 2c1 2 3 4 5 5h1l4 4c2 1 4 1 5 2 2 0 3 0 4-1s0-1 0-2h4l3-3h-1v-1c3 0 6-4 8-6l1-2h3c2 0 3-2 5-3v2c-1 1-1 1-1 2h0v1l1 1-16 20v-2c-2 3-3 5-5 6 1-1 1-2 2-4v-2c-2 1-4 3-6 3-2 1-5 1-7 0-1-1-3-4-4-5-4-5-7-10-11-16-1-2-2-5-4-6h0l-1-1-3-6v-1l-2-5v-1l-1-2h0l-1-2v-1c-1 0-1-1-1-1v-1-2-2-1l2-1z" class="I"></path><path d="M545 710l1 1-16 20v-2c-2 3-3 5-5 6 1-1 1-2 2-4v-2h0 1c6-5 12-13 17-19z" class="Y"></path><path d="M491 679l2 1c0 1 0 2 1 2v-1l5 5 2 2c4 2 7 5 11 7l9 6v1l2 3c1 1 2 2 4 2l3 3 1 1c1 1 1 1 2 1 1-1 2-2 4-2-2 2-5 6-8 6v1h1l-3 3h-4c0 1 1 1 0 2s-2 1-4 1c-1-1-3-1-5-2l-4-4h-1c-2-1-4-3-5-5l-1-2c-6-10-11-19-12-31z" class="B"></path><path d="M505 699l1-1v1l3 3v1c0 3 2 3 3 5 0 1-1 1-1 2v1h-1v-1l-2-4c0-1-1-1-3-2h0l1-1c-1-2-1-3-1-4z" class="G"></path><path d="M512 702c2 1 3 2 4 4l1 1v1l1 2c-1 0-1 0-2-1h-1c-1-1-1 0-3-1-1-2-3-2-3-5v-1h3z" class="L"></path><path d="M512 702c2 1 3 2 4 4l1 1h-3v-1c-1-1-3-2-5-3v-1h3z" class="R"></path><path d="M499 686l2 2c2 4 3 11 3 16h-1v-3c0-1-1-2-2-3v-2h-1v-2c0-1 0-1-1-2v-3h-1c0-2 1-2 1-3z" class="G"></path><path d="M501 688c4 2 7 5 11 7l-1 2-2-1c-2-1-3-2-4-3v1l-1 1c0 1 0 3 1 4 0 1 0 2 1 4l-1 1h0v2c0-1 0-1-1-2 0-5-1-12-3-16z" class="R"></path><path d="M505 694v-1c1 1 2 2 4 3l2 2c3 1 4 5 6 7l-1 1c-1-2-2-3-4-4h-3l-3-3v-1l-1 1c-1-1-1-3-1-4l1-1z" class="r"></path><path d="M505 699c-1-1-1-3-1-4l1-1c1 3 5 6 7 8h-3l-3-3v-1l-1 1zm7-4l9 6v1l2 3c0 1 1 2 1 3h-2-1 0-1-3v-1l-1-1 1-1c-2-2-3-6-6-7l-2-2 2 1 1-2z" class="D"></path><path d="M509 696l2 1 3 2c3 1 5 4 6 6v1 2h-3v-1l-1-1 1-1c-2-2-3-6-6-7l-2-2z" class="Z"></path><path d="M503 710l2-2 2 2c0 1 0 1 1 2v1c3 2 5 4 8 6h1c-2-2-4-3-6-5l4 1c1 1 2 1 3 1v-1-3c1 2 2 4 3 5v1c-1 0-2-1-3-1h-1l1 1h0c1 1 1 1 1 2 1 1 1 0 1 1h-3-3l-4-4h-1c-2-1-4-3-5-5l-1-2z" class="d"></path><path d="M523 705c1 1 2 2 4 2l3 3 1 1c1 1 1 1 2 1 1-1 2-2 4-2-2 2-5 6-8 6v1h1l-3 3h-4c0 1 1 1 0 2s-2 1-4 1c-1-1-3-1-5-2h3 3c0-1 0 0-1-1 0-1 0-1-1-2h0l-1-1h1c1 0 2 1 3 1v-1c-1-1-2-3-3-5h0l1-1-1-1-1-2h3 1 0 1 2c0-1-1-2-1-3z" class="J"></path><path d="M531 711c1 1 1 1 2 1 1-1 2-2 4-2-2 2-5 6-8 6v1h1l-3 3 1-2h-3c-1 0-2-1-2-2v-1h3c2 0 3-2 5-4z" class="E"></path><defs><linearGradient id="c" x1="525.447" y1="710.635" x2="519.424" y2="707.084" xlink:href="#B"><stop offset="0" stop-color="#5a595b"></stop><stop offset="1" stop-color="#6f7071"></stop></linearGradient></defs><path fill="url(#c)" d="M523 705c1 1 2 2 4 2l3 3c-1 0-1 1-3 0v3h-3-3c-1 0-2-1-2-2l-1-1-1-2h3 1 0 1 2c0-1-1-2-1-3z"></path><defs><linearGradient id="d" x1="532.644" y1="726.816" x2="571.567" y2="745.67" xlink:href="#B"><stop offset="0" stop-color="#0d0e0d"></stop><stop offset="1" stop-color="#424242"></stop></linearGradient></defs><path fill="url(#d)" d="M578 651c1 1 1 1 1 3h0c-1 1-2 2-2 3v1c0 1-1 2-1 3v3h0c-1 1 0 1-1 2v3l-1 1c0 1 1 2 1 2 1 2-1 8-2 10l-1-1h0l-2 9v1 1l-1-4v5l1 1 1-2v5c1 1 0 1 0 2v1l1-4 2 2-2 4c-1 1-2 3-2 5 1 1 1 1 2 0v1h-1c-2 3-3 7-4 10 3 1 6 4 10 4l3-1h0c-1 1-3 1-4 2 0 1-1 2-2 3l-1 1c0 2-2 3-3 4-2 1-4 2-5 4-1 3 0 5 1 8l-1-1-3-6-6 20-8 28c-2 6-3 12-6 17h0c-1 2-1 3-1 4l-1 1v4 1c-1 1-1 2-2 3v1 1h0c0-2 0-7-1-9v-3-1c-1 1-1 0-1 1v-8c-1-3 0-6-1-8 0-1 0-2-1-3 0-9-1-19-6-27 0 2 1 3 1 5 0 1-1 3-2 4l-6 14-1-6c-1-7 0-13 1-19 1-4 1-8 3-11 1-5 4-9 6-14l16-20-1-1v-1h1c1-1 2-4 2-5h-2l-3-1 4-2c4-2 7-5 10-8 3-4 6-10 8-14 4-10 9-18 12-28l1 1v-1z"></path><path d="M539 777c-1-1-1-1-1-2h-1c0-4-1-7-1-11h0l1 3c0 1 0 2 1 3h1c0-2 0-2 1-3l-1 10zm5-50c0-2 1-6 2-8 1 1 2 2 2 4-2 1-1 2-1 4h-1l-1-2v2h-1z" class="C"></path><path d="M564 731c0-4 1-8 3-12l6 3c-1 0-2 1-3 1h-2c-1 0-1 0-1 1v1c-1 1-2 3-2 4l-1 2z" class="M"></path><path d="M554 749l1-2v-1-2c1 0 1-1 1-1 1-3 1-4 1-7 0-2 1-6 3-7h1-1c0 1-1 2-1 3h0c1 2 1 4 0 6v1c0 1 0 1-1 1-2 3-2 9-4 12l-1-1 1-2zm7-42l1-1 3-7-3 14c0 1 1 2 1 3v2 1 1l-1-1c-1 2 0 5-1 8v-12h0v-3l-2 2h0l1-3c1-1 0-3 1-4z" class="F"></path><path fill="#efcdcb" d="M573 722l3 1c0 1-1 2-2 3l-1 1c0 2-2 3-3 4-2 0-3 1-4 1s-2 1-3 0l1-1 1-2c0-1 1-3 2-4v-1c0-1 0-1 1-1h2c1 0 2-1 3-1z"></path><path d="M577 651l1 1c-2 6-6 14-9 20-3 7-7 14-8 22h0c-7 5-11 10-15 17l-1-1v-1h1c1-1 2-4 2-5h-2l-3-1 4-2c4-2 7-5 10-8 3-4 6-10 8-14 4-10 9-18 12-28z" class="N"></path><path d="M549 723c0-2 0-3 1-4v-1c2-4 5-9 8-13l-2 8v2l2-3 3-6v1c-1 1 0 3-1 4l-1 3h0l2-2v3h0v12l-1 2c-2 1-3 5-3 7 0 3 0 4-1 7 0 0 0 1-1 1v2 1l-1 2-1-2c0 1-1 2-1 3l-2 2c-1 2-1 3-1 5s-2 5-3 7l-1 1-1-1h1l1-1 1-3c-1-1-1-1-2-1 0-1 1-2 1-3v-1c-1 1-2 3-3 5v1c1 1 1 3 1 5v1c1 2 0 5 0 6-1-1-1-2 0-3v-1c0-1-1-2-1-3v-1l-1-1c0 1-1 2 0 4 0 1 1 2 1 4v7l-1 1c-2 2-2 4-2 6-1-1-1-2 0-3v-2c0-1-1-1-1-1v5c-1-2-1-6 0-8l1-10v-6l3-11c1-1 1-1 1-2h0-1v2h-1c-2-4-2-10-1-15 1-1 1-2 1-4l2-4h1c-1 3-2 5-3 8l1 1v-2c0-1 0-1 1-2h1v-1l1-1-1-2c1 0 1 0 2 1l-3 11c0 1 0 2 1 4v-3h1v-2c0-1 0-2 1-3v-2h0l-1-1 1-1c0-1 0-2 1-4 0 0 1-1 1-2h0v-3z" class="G"></path><path d="M549 723c0-2 0-3 1-4v-1c2-4 5-9 8-13l-2 8v2c0 3-2 5-2 8-2 5-3 11-5 15-1-2 1-4 1-6l3-11c0-1 1-3 1-4-2 1-3 2-3 4l-2 2z" class="I"></path><defs><linearGradient id="e" x1="410.122" y1="593.711" x2="356.786" y2="615.833" xlink:href="#B"><stop offset="0" stop-color="#e6dcda"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#e)" d="M380 409c4 4 6 9 8 13h1c1 2 2 4 4 6 1 2 8 20 10 21 0 1 3 6 3 8l-1 1c0 3 1 7 0 10v5l1 1c-2 7-4 16-4 23h-1c-1 3-1 6-1 10-1 7-2 15-4 22 0 2-1 5-1 7-2 2-3 5-3 7s0 3-1 4v2l3 4 3 10 29 67 12 31 2 7 6 16-3 3-3-1c0 1 0 1 1 2l-1 1c-2 0-4-2-6-3-3-1-6-3-9-5s-4-5-7-6l-3-1c-19-10-34-27-47-43-5-7-11-14-15-22l-3-9h0v1l-1-1-1 2v1c0 2 1 4 2 6h-1l-1-1-1-2c-1-2-1-3-3-3-1-2-2-5-3-7 0-2-2-4-2-6l-2-1v-4l-1-4v-8c-1-3-1-9-1-12 1-3 1-7 2-10 2-13 6-25 11-37l3-6c2-4 5-9 8-13l4-7 3-3v-5c-2-3-2-7-3-10l-3-7c-1-1-2-2-2-4h1c2-5 3-10 4-15l2-1 6-22 3-9c1-1 2-2 3-2 0 0 1 1 1 2 0-2 1-2 2-3z"></path><path d="M349 589h1c3 1 5 0 7-1 2-2 3-3 4-5-1 4-4 7-7 10 2 1 4 3 5 5-2-1-4-3-7-3l-1 2-1 3v1l-1-1v-1c-1-2-2-3-2-5l-1-3c0-2 0-3-1-4h1v-2l1-1c1 2 1 3 2 4v1z" class="T"></path><path d="M349 588v1c0 2 1 2 1 3v1c-1 2 0 3 1 4l-1 3v1l-1-1v-1c-1-2-2-3-2-5h1c2-2 0-2 0-4 0-1 1-1 1-2z" class="U"></path><path d="M346 585l1-1c1 2 1 3 2 4 0 1-1 1-1 2 0 2 2 2 0 4h-1l-1-3c0-2 0-3-1-4h1v-2z" class="o"></path><path d="M387 548h0c-2 1-3 2-3 4h0c-2-1-3-4-4-6-2-3-4-6-7-8-3-1-6-3-9-2h-1l1-1h2c1-1 2 0 3 0 3 2 8 5 12 4l5-1c1 1 1 2 1 3-1 2 0 2 0 4v3z" class="X"></path><path d="M418 675c1-4-5-15-7-19-1-4-3-8-3-13 6 17 15 34 31 43h1c0 1 0 1 1 2l-1 1c-2 0-4-2-6-3-3-1-6-3-9-5s-4-5-7-6z" class="O"></path><path d="M372 497l2 7 10 25c1 2 2 5 4 8l-2 1-5 1c-4 1-9-2-12-4 1-1 1-1 1-2l1-1v-1-8h1c-1-2-1-4-1-6-1-3-1-5-2-8-1-2-2-2-4-3 2-3 5-6 7-9z" class="M"></path><path d="M372 497l2 7-4 3c1 4 1 8 2 12v4c-1-2-1-4-1-6-1-3-1-5-2-8-1-2-2-2-4-3 2-3 5-6 7-9z" class="T"></path><defs><linearGradient id="f" x1="335.284" y1="514.082" x2="376.216" y2="569.918" xlink:href="#B"><stop offset="0" stop-color="#250402"></stop><stop offset="1" stop-color="#930e0f"></stop></linearGradient></defs><path fill="url(#f)" d="M366 480l1 3c2 1 3 3 5 4v1c-1 2 0 3 0 5v4c-2 3-5 6-7 9-13 17-19 38-20 60 0 6 0 12 2 18l-1 1v2h-1c1 1 1 2 1 4l1 3c0 2 1 3 2 5v1l-1 2v1c0 2 1 4 2 6h-1l-1-1-1-2c-1-2-1-3-3-3-1-2-2-5-3-7 0-2-2-4-2-6l-2-1v-4l-1-4v-8c-1-3-1-9-1-12 1-3 1-7 2-10 2-13 6-25 11-37l3-6c2-4 5-9 8-13l4-7 3-3v-5z"></path><path d="M340 580c-1-4 0-8 0-12v2 1c0 1 0 3 1 4v3c1 1 1 1 1 2v-2c1 1 1 1 1 2s2 3 3 5v2h-1c-1 0-2 0-3 1l-2-8z" class="U"></path><path d="M342 578v-4c0-1 0-1 1-3h0 1 0v1c1-2 0-1 0-3h0l1-2-3-1v-2l3 2h0c0 6 0 12 2 18l-1 1c-1-2-3-4-3-5s0-1-1-2z" class="H"></path><path d="M366 480l1 3c0 2 1 4 1 6v2c-2 1-3 2-4 4 0 1 0 2-1 2-1 2-3 3-4 4-1 2-1 2-2 3-2 1-4 3-6 4 2-4 5-9 8-13l4-7 3-3v-5z" class="Q"></path><path d="M366 480l1 3c0 2 1 4 1 6v2c-1 0-2 1-3 1-2 0-3 0-4 2 0 1-1 1-2 1l4-7 3-3v-5z" class="b"></path><path d="M340 580l2 8c1-1 2-1 3-1 1 1 1 2 1 4l1 3c0 2 1 3 2 5v1l-1 2v1c0 2 1 4 2 6h-1l-1-1-1-2c-1-2-1-3-3-3-1-2-2-5-3-7 0-2-2-4-2-6l-1-5h1c0 1 1 2 1 4 0 1 0 2 1 3 0 1 1 2 1 3h1l-1-1v-2-1c0-1 0-3-1-4 0-1-1-1-1-2v-1c-1-2 0-2 0-4z" class="k"></path><path d="M342 588c1-1 2-1 3-1 1 1 1 2 1 4h-1v6l-3-9z" class="g"></path><path d="M345 597v-6h1l1 3c0 2 1 3 2 5v1l-1 2-3-5z" class="O"></path><defs><linearGradient id="g" x1="414.441" y1="451.86" x2="355.751" y2="500.979" xlink:href="#B"><stop offset="0" stop-color="#040404"></stop><stop offset="1" stop-color="#272727"></stop></linearGradient></defs><path fill="url(#g)" d="M380 409c4 4 6 9 8 13h1c1 2 2 4 4 6 1 2 8 20 10 21 0 1 3 6 3 8l-1 1c0 3 1 7 0 10v5l1 1c-2 7-4 16-4 23h-1c-1 3-1 6-1 10-1 7-2 15-4 22 0 2-1 5-1 7-2 2-3 5-3 7s0 3-1 4v2l-4-1v-3c0-2-1-2 0-4 0-1 0-2-1-3l2-1c-2-3-3-6-4-8l-10-25-2-7v-4c0-2-1-3 0-5v-1c-2-1-3-3-5-4l-1-3c-2-3-2-7-3-10l-3-7c-1-1-2-2-2-4h1c2-5 3-10 4-15l2-1 6-22 3-9c1-1 2-2 3-2 0 0 1 1 1 2 0-2 1-2 2-3z"></path><path d="M386 469l1 1c-1 10-4 19-5 29v-3c1-2 2-5 1-7 0-2 0-3 1-4v-2h0c1-2 1-2 1-4v-1c0-2 0-3 1-4v-5zm6 18h0c0 8-1 16-3 23h0v-2l1-1c-1-1-1 0-1-1 0-2 0-1-1-2v-1c1-1 1-1 1-2v-2l1-1v-3h1v-1c0-1 1-2 1-3-1-2-1-3 0-4z" class="C"></path><path d="M388 537l3 10v2l-4-1v-3c0-2-1-2 0-4 0-1 0-2-1-3l2-1z" class="T"></path><path d="M389 422c1 2 2 4 4 6 1 2 8 20 10 21 0 1 3 6 3 8l-1 1c0 3 1 7 0 10v5l1 1c-2 7-4 16-4 23h-1l2-22c0-8 0-16-2-24-3-10-8-20-13-29h1z" class="N"></path><defs><linearGradient id="h" x1="356.67" y1="434.482" x2="389.83" y2="461.518" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#2e2e2e"></stop></linearGradient></defs><path fill="url(#h)" d="M374 412c1-1 2-2 3-2 0 0 1 1 1 2 1 1 1 1 1 3h1v2 2c1 1 1 9 0 11v8l-1 1v5c-1 3 0 6-1 9-1 6-1 13-2 19 0 1 0 2-1 3-1 6-1 13-3 18 0-2-1-3 0-5v-1c-2-1-3-3-5-4l-1-3c-2-3-2-7-3-10l-3-7c-1-1-2-2-2-4h1c2-5 3-10 4-15l2-1 6-22 3-9z"></path><path d="M365 443l-5 20c-1-1-2-2-2-4h1c2-5 3-10 4-15l2-1z" class="V"></path><path d="M118 181h41 34 7l5 1h0c2 1 5 1 7 2 1 1 2 1 3 2v1l9 3c6 4 13 7 19 11h-5c0 1 3 2 3 2 2 3 5 4 6 6 2 1 4 2 5 4 0 0-1 0-1 1l4 2c2 1 3 2 4 3v1l-1 1c-1 0-1 0-1-1l-1 1 1 1h-1l12 9c1 1 2 1 2 1l3 3v1c0 1 1 2 2 3 1 0 1 0 2-1 1 0 2 1 3 1v3h0-1v1l1 2 4 5 1 2c2 3 4 6 7 9v1h0c1 2 1 4 2 6l1-1c1 2 1 3 0 4l13 16 9 11 4 5 14 20v4l-1 1-1-1c1 1 0 1 2 2 4 3 7 8 10 12 5 7 9 13 12 20 2 3 4 8 6 10h1l11 21c1 0 2 0 2 1l1 1c1 2 1 3 2 4l1 2 1 2-1 2 1 1c0 1 1 2 1 2v1c1 1 1 1 1 3h0c1 4 4 7 5 11h-1c-2-4-4-9-8-13-1 1-2 1-2 3 0-1-1-2-1-2-1 0-2 1-3 2l-3 9-6 22-2 1c-1 5-2 10-4 15h-1l-23-49c-11-21-22-42-34-62-10-15-24-33-43-36-12-3-27 1-38 8-4 2-7 5-11 8 9-10 18-21 22-33 5-13 5-26-1-38-4-10-11-18-19-26-18-18-43-31-67-39l-23-7-12-3 9-1z" class="n"></path><path d="M310 307h1c1 2 1 2 1 3h-1l-1-1v-2z" class="p"></path><path d="M313 323h1c1 2 1 2 1 4l-2 2h0-1-1v-1c1-2 1-2 1-3l-1-1h2v-1z" class="h"></path><path d="M267 269l3-10 1 4v3l-1 1c-1 1-1 0-1 1l-2 1zm43 44h1l-1 1-5 11-1-1c0-1 1-2 1-4l1-2 1-1c1-1 2-3 3-4z" class="C"></path><path d="M335 329c-5 1-10 7-14 11l6-9c1-2 1-5 2-7 2 0 3 2 4 3h0c1 1 0 1 2 2z" class="V"></path><path d="M302 313h2 1c0 2 0 6-1 8 0 1-1 2-1 2-1 2-1 3-2 4l-1-1h0v-7l1-5 1-1z" class="C"></path><path d="M290 267l5 4 13 16c-1 1-1 2-2 2l-3-3c-2-6-9-13-14-18l1-1z" class="M"></path><path d="M267 269l2-1c0-1 0 0 1-1l1-1c-1 6-4 10-8 14l-10 10h0c1-3 4-4 5-7l1-1 1-1-2-1c1-2 2-3 3-4s1-2 1-4l1 1h1c2-1 2-2 3-4z" class="h"></path><path d="M303 286l3 3c1 0 1-1 2-2l9 11-1 1c-2-2-5-4-7-6h-2c-5 2-9 11-12 16h0c1-3 2-6 3-8 2-2 4-5 5-8v-7z" class="N"></path><path d="M317 298l4 5 14 20v4l-1 1-1-1h0c-1-1-2-3-4-3h0c1-3 0-5-2-8-3-6-7-12-11-17l1-1z" class="Y"></path><path d="M261 259h1v4c-2 3-3 6-5 9-7 10-14 20-23 29h0c5-7 11-14 16-22 4-6 7-13 11-20z" class="c"></path><path d="M276 302c-1 0-2-1-3-2v-3c2 0 2 1 4 1h1c1 1 2 2 2 3 1 1 1 1 1 2 1 1 2 2 2 3 1 2 0 3 1 5-1 0-3 0-3 1-2 0-1 1-3 0s-3-2-6-2l1-1-1-2v-3l2-1h0 2v-1z" class="h"></path><path d="M276 302c-1 0-2-1-3-2v-3c2 0 2 1 4 1h1c1 1 2 2 2 3v1c1 1 1 1 1 2h-1 0-1l1 3s1 1 1 2h-1 0l-1-2-2 1s0-1-1-2l-2-2h1 2c0-1 0-1-1-2z" class="C"></path><path d="M372 393l2 7c2 2 3 8 6 9-1 1-2 1-2 3 0-1-1-2-1-2-1 0-2 1-3 2l-3 9-6 22-2 1c5-17 9-33 9-51z" class="W"></path><path d="M374 400c2 2 3 8 6 9-1 1-2 1-2 3 0-1-1-2-1-2-1 0-2 1-3 2v-5c1-3 0-5 0-7z" class="Y"></path><path d="M264 237c1 1 4 5 4 6v1c0 1 1 1 1 2s1 2 1 3l-1 1 4 5c-3 0-4 1-6 2l-5 6v-4h-1v-2c2-6 2-10 1-15h2c-1-2-1-3 0-5z" class="a"></path><path d="M269 250l4 5c-3 0-4 1-6 2l2-4-1-1v-2h1z" class="M"></path><path d="M262 242h2l1 4c2 4 0 8-2 12l-1 1h-1v-2c2-6 2-10 1-15z" class="W"></path><path d="M312 330l1-1 2 1v-1h-1l1-1h1v2c-2 2-2 4-1 7v6c0 2 0 3-2 5-1-1-2-1-2-2v-2l-1-1v-1l-1-1v3 6-1l-1 1-2-4c0-1 0-2-1-3-1-2-2-3-2-4-1-1-1-2-1-3l-1-1v-1-1c1-2 4-6 7-7v1l2-1h0l1 3h-1l-1-1c-2 0-3 0-4 1s-2 3-3 3c0 1 1 2 1 4l1-1v-1h0c0 1 1 1 1 2-1 0-1 1-1 1-1 1 0 3 1 4h1l-1-1v-2c1-1 0-1 0-2h1 1c1 2 1 2 0 3h0v4c0 2 1 3 1 5 1-1 1-1 0-2v-5-1c1-1 1-2 1-3v-1c-1-2-1-3 0-5h0c1 1 1 1 0 2 0 2 1 5 1 7h0 0v-1-1c0-2 0-4 1-6 0-2 0-2 1-2z" class="C"></path><path d="M310 340v-1-1c0-2 0-4 1-6 0-2 0-2 1-2l2 1v5h-2c0 3 1 6 0 8-1 0-1-3-2-4z" class="I"></path><path d="M364 371l11 21c1 0 2 0 2 1l1 1c1 2 1 3 2 4l1 2 1 2-1 2 1 1c0 1 1 2 1 2v1c1 1 1 1 1 3h0c1 4 4 7 5 11h-1c-2-4-4-9-8-13-3-1-4-7-6-9l-2-7c-2-8-6-14-9-22h1z" class="c"></path><path d="M375 392c1 0 2 0 2 1l1 1c1 2 1 3 2 4l1 2 1 2-1 2 1 1c0 1 1 2 1 2v1c1 1 1 1 1 3h0l-9-19z" class="B"></path><path d="M169 184v-1h1l21 6c5 3 11 4 16 7 7 3 14 6 20 10 4 2 7 5 10 7 4 4 8 8 13 10 4 4 9 8 13 12l1 2c-1 2-1 3 0 5h-2l-3-6c-4-5-9-9-14-14-15-13-34-24-54-32-7-3-15-5-22-6z" class="N"></path><defs><linearGradient id="i" x1="171.605" y1="177.394" x2="194.37" y2="190.267" xlink:href="#B"><stop offset="0" stop-color="#2c2a2b"></stop><stop offset="1" stop-color="#434344"></stop></linearGradient></defs><path fill="url(#i)" d="M159 181h34v2l5 4c2 1 7 4 9 4-1 1-1 2-1 2 0 1 1 2 1 3-5-3-11-4-16-7l-21-6h-1v1c-1 0-2 0-3-1h-1c-3 0-4 0-6-2z"></path><path d="M191 189v-1l-2-1c-1 0-1-1-2-1 3-1 6 2 9 2 1 0 2-1 2-1 2 1 7 4 9 4-1 1-1 2-1 2 0 1 1 2 1 3-5-3-11-4-16-7z" class="B"></path><path d="M268 231c1 1 2 1 2 1l3 3v1c0 1 1 2 2 3 1 0 1 0 2-1 1 0 2 1 3 1v3h0-1v1l1 2 4 5 1 2c2 3 4 6 7 9v1h0c1 2 1 4 2 6l1-1c1 2 1 3 0 4l-5-4-1 1c-5-5-9-10-16-13l-4-5 1-1c0-1-1-2-1-3s-1-1-1-2v-1c0-1-3-5-4-6l-1-2h3v-2h0l2-2z" class="E"></path><path d="M268 243l3 3c4 9 13 14 19 21l-1 1c-5-5-9-10-16-13l-4-5 1-1c0-1-1-2-1-3s-1-1-1-2v-1z" class="Y"></path><path d="M277 238c1 0 2 1 3 1v3h0-1v1l1 2 4 5h-1-2c0 2 2 3 3 5l-2-1c0-1-1-2-1-3-2-2-3-3-5-4 0-1-1-1-2-2l-1 1-2-1v-2c1-1 1-2 2-3 0-1 1-1 2-1s1 0 2-1z" class="p"></path><path d="M268 231c1 1 2 1 2 1l3 3v1c0 1 1 2 2 3-1 0-2 0-2 1-1 1-1 2-2 3v2 1l-3-3c0-1-3-5-4-6l-1-2h3v-2h0l2-2z" class="D"></path><path d="M268 231c1 1 2 1 2 1l-1 5v-1c-1-1-1-2-3-3h0l2-2z" class="E"></path><path d="M270 232l3 3v1c0 1 1 2 2 3-1 0-2 0-2 1l-1-1c-1-1-2-2-3-2l1-5z" class="C"></path><defs><linearGradient id="j" x1="242.081" y1="205.84" x2="229.412" y2="218.137" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#3e3e41"></stop></linearGradient></defs><path fill="url(#j)" d="M200 181l5 1h0c2 1 5 1 7 2 1 1 2 1 3 2v1l9 3c6 4 13 7 19 11h-5c0 1 3 2 3 2 2 3 5 4 6 6 2 1 4 2 5 4 0 0-1 0-1 1l4 2c2 1 3 2 4 3v1l-1 1c-1 0-1 0-1-1l-1 1 1 1h-1l12 9-2 2h0v2h-3c-4-4-9-8-13-12-5-2-9-6-13-10-3-2-6-5-10-7-6-4-13-7-20-10 0-1-1-2-1-3 0 0 0-1 1-2-2 0-7-3-9-4l-5-4v-2h7z"></path><path d="M237 213c5 0 10 5 13 8v1 1c-5-2-9-6-13-10z" class="d"></path><path d="M248 216v-1-1c1 0 2 1 3 1v-1l4 2c2 1 3 2 4 3v1l-1 1c-1 0-1 0-1-1l-1 1 1 1h-1l-8-6z" class="B"></path><path d="M235 207l1-1h1c1 1 3 2 4 3s2 2 3 2h1l-1-1c1-1 2-1 3-1 2 1 4 2 5 4 0 0-1 0-1 1v1c-1 0-2-1-3-1v1 1c-4-4-9-6-13-9z" class="D"></path><path d="M222 198h2c1 0 1 1 2 1s1-1 2-2c2 0 8 3 10 4 0 1 3 2 3 2 2 3 5 4 6 6-1 0-2 0-3 1l1 1h-1c-1 0-2-1-3-2s-3-2-4-3h-1l-1 1c-1-1-1 0-2-1l-11-8z" class="L"></path><defs><linearGradient id="k" x1="216.929" y1="181.25" x2="217.57" y2="201.703" xlink:href="#B"><stop offset="0" stop-color="#171615"></stop><stop offset="1" stop-color="#454748"></stop></linearGradient></defs><path fill="url(#k)" d="M200 181l5 1h0c2 1 5 1 7 2 1 1 2 1 3 2v1l9 3c6 4 13 7 19 11h-5c-2-1-8-4-10-4-1 1-1 2-2 2s-1-1-2-1h-2l-1 1c-2-1-3-2-5-3l-2-2h1l-1-1h-3l-4-2c-2 0-7-3-9-4l-5-4v-2h7z"></path><path d="M212 184c1 1 2 1 3 2v1l-6-1 3-2z" class="r"></path><path d="M200 181l5 1h0c2 1 5 1 7 2l-3 2c-3-1-6-2-9-5z" class="V"></path><path d="M696 551s1 0 1 1l-2 4v1c-1 1-2 3-3 4 1 1 2 2 3 2h0c2 2 4 3 6 4v1c6 6 4 15 6 23l1 14-2 21-2 15c-5 28-12 54-25 79l-5 8-7 11c-5 7-10 14-16 19l-16 13c-1 1-3 2-4 3h-1l2-2c-6 5-13 9-20 13-17 10-36 17-56 20h-7-7v-4c3-5 4-11 6-17l8-28 6-20 3 6 1 1c-1-3-2-5-1-8 1-2 3-3 5-4 1-1 3-2 3-4l1-1c1-1 2-2 2-3 1-1 3-1 4-2h0l-3 1c-4 0-7-3-10-4 1-3 2-7 4-10h1v-1c-1 1-1 1-2 0 0-2 1-4 2-5 2 0 3 0 5-1 4 0 9-2 13-4l5-1c3-2 6-3 9-5 8-4 15-9 22-14 11-11 20-22 29-35 1-1 2-3 3-5 0-3 4-8 5-11l1-2c0-1 1-2 2-3 0-1 0-2 1-2 0-3 2-5 2-7l2-5 2-4 8-23c0-2 0-2 1-3 0-3 1-6 1-8 0-3-2-7-3-9 0-1 0-1 1 0h1v-2c1 2 1 4 2 6l2 1v-1c-1-2-1-6 0-9l1-1v1h1c2 0 6-3 8-4z" class="c"></path><path d="M668 684h1c1 0 2 0 3 1v2c-1 4-5 7-8 9h-1l2-1 1-2c2-2 3-3 3-6l-1-1c-1-1-2 0-4 0-1 1 0 1-1 0 1-1 3-2 5-2z" class="X"></path><path d="M675 719c0 2-1 4-2 6v1h0c0 2 0 1 1 2l-7 11-2 1-1-1h1c1-1 0-1 1-2h0v-2c-1 2-1 3-3 3l12-19z" class="P"></path><path d="M580 721c1-1 2-1 3-2 0 0 1-1 2-1l3-2 3-3 6-3 1-1c1-1 2-1 4-2-1 2-3 5-2 7-1 1-1 1-1 2-1-1-1 0-1-1v-1l1-3h-1c-4 2-8 6-12 8-3 2-6 3-9 5-1 0-1 1-1 2v1c0 2 0 3-1 4 0 1 0 0-1 1l-2-1-6 4v5c1 3 2 5 1 7l-1-1c0-1 0-1 1-2l-1-1c-1-3-2-5-1-8 1-2 3-3 5-4 1-1 3-2 3-4l1-1c1-1 2-2 2-3 1-1 3-1 4-2z" class="Y"></path><path d="M542 801c3-5 4-11 6-17l8-28 6-20 3 6 1 1 1 1c-1 1-1 1-1 2l-1-1c-1-1 0-1-1-2-1 1-2 3-3 4v1c-2 3-3 5-4 8l-14 49v-1c2 0 5 0 6 1h-7v-4z" class="M"></path><path d="M663 738c2 0 2-1 3-3v2h0c-1 1 0 1-1 2h-1l1 1 2-1c-5 7-10 14-16 19l-16 13c-1 1-3 2-4 3h-1l2-2c1-2 5-4 6-6 6-5 12-11 17-17 3-4 5-7 8-10v-1z" class="X"></path><path d="M699 621c3 1 3 0 6-1v2h1v4l-2 15c-5 28-12 54-25 79l-5 8c-1-1-1 0-1-2h0v-1c1-2 2-4 2-6 13-27 23-57 24-88v-10z" class="O"></path><path d="M705 622h1v4l-2 15v-4c1-4 0-8 0-13l1-2z" class="t"></path><path d="M699 621c3 1 3 0 6-1v2l-1 2v5c-1 1-3 4-4 5l-1-3v-10z" class="o"></path><defs><linearGradient id="l" x1="575.393" y1="701.499" x2="595.811" y2="713.985" xlink:href="#B"><stop offset="0" stop-color="#ddc9c7"></stop><stop offset="1" stop-color="#f1efee"></stop></linearGradient></defs><path fill="url(#l)" d="M607 692c2 0 3 1 4 2-4 5-9 13-11 20-1-2 1-5 2-7-2 1-3 1-4 2l-1 1-6 3-3 3-3 2c-1 0-2 1-2 1-1 1-2 1-3 2h0l-3 1c-4 0-7-3-10-4 1-3 2-7 4-10h1v-1c-1 1-1 1-2 0 0-2 1-4 2-5 2 0 3 0 5-1 4 0 9-2 13-4l5-1 4-1 1 1c1 0 5-3 7-4h0z"></path><defs><linearGradient id="m" x1="576.609" y1="693.084" x2="601.699" y2="704.826" xlink:href="#B"><stop offset="0" stop-color="#2e0302"></stop><stop offset="1" stop-color="#4f0e0e"></stop></linearGradient></defs><path fill="url(#m)" d="M607 692c2 0 3 1 4 2-4 5-9 13-11 20-1-2 1-5 2-7 2-3 3-6 3-10l-6 2c-9 4-17 8-27 9v-1c-1 1-1 1-2 0 0-2 1-4 2-5 2 0 3 0 5-1 4 0 9-2 13-4l5-1 4-1 1 1c1 0 5-3 7-4h0z"></path><path d="M572 707c5-2 8-3 13-4 3-1 5-3 8-3 2-1 4-1 6-1-9 4-17 8-27 9v-1z" class="b"></path><defs><linearGradient id="n" x1="637.408" y1="613.552" x2="667.746" y2="647.621" xlink:href="#B"><stop offset="0" stop-color="#1d0201"></stop><stop offset="1" stop-color="#4e0908"></stop></linearGradient></defs><path fill="url(#n)" d="M696 551s1 0 1 1l-2 4v1c-1 1-2 3-3 4 1 1 2 2 3 2h0c2 2 4 3 6 4v1c6 6 4 15 6 23l1 14-2 21v-4h-1v-2c-3 1-3 2-6 1l-1-4c-1-2-2-4-4-5-3-1-5 0-7 1-3 2-5 4-7 6-4 4-9 10-10 16-1 3 0 5 1 8l-4-2c-6 5-10 11-14 16-5 6-12 13-16 20l3 11c-2-3-3-7-4-10-5 3-8 5-12 8 3 4 7 8 9 12h-1c-3-4-6-8-11-8-3 0-7 2-10 4-1-1-2-2-4-2h0c-2 1-6 4-7 4l-1-1-4 1c3-2 6-3 9-5 8-4 15-9 22-14 11-11 20-22 29-35 1-1 2-3 3-5 0-3 4-8 5-11l1-2c0-1 1-2 2-3 0-1 0-2 1-2 0-3 2-5 2-7l2-5 2-4 8-23c0-2 0-2 1-3 0-3 1-6 1-8 0-3-2-7-3-9 0-1 0-1 1 0h1v-2c1 2 1 4 2 6l2 1v-1c-1-2-1-6 0-9l1-1v1h1c2 0 6-3 8-4z"></path><path d="M626 677c-1 2-1 2-3 3l-14 10h0l-3 1 1 1h0c-2 1-6 4-7 4l-1-1-4 1c3-2 6-3 9-5 8-4 15-9 22-14z" class="Q"></path><path d="M698 582h1v-3l1 1s0 1-1 2c2 2 3 3 3 6v1l-1-3c-1 2-3 3-3 5h-1c-1 1 0 1-1 1v1h0-2c0-1-1-2-1-3-1 2-2 3-4 5 1-4 4-7 6-10l3-3z" class="q"></path><path d="M698 582l1 1c-2 2-3 5-5 7l1 1c0 1-1 1-1 2 0-1-1-2-1-3-1 2-2 3-4 5 1-4 4-7 6-10l3-3z" class="l"></path><path d="M698 617c0-1 1-2 2-3v-2c0-1 2-3 3-4h2l1-1-1 13c-3 1-3 2-6 1l-1-4z" class="Q"></path><path d="M690 587h1l1 1 1-1c1-1 1-2 2-2-2 3-5 6-6 10 0 2-2 4-3 6h1c-1 2-4 6-6 7v-4h-1c0-1 0-1 1-2v-1c0-1 1-1 2-2 0-1 1-2 1-2 0-1 1-2 1-2 1-1 4-7 5-8z" class="q"></path><path d="M685 595v-2c-4 2-4 8-6 10 1-4 3-8 5-12l3-6c2-3 4-10 7-11v3h2c-1 1-1 1-1 2-1 1-2 1-2 2v1l-3 5c-1 1-4 7-5 8z" class="l"></path><path d="M695 563c2 2 4 3 6 4v1c6 6 4 15 6 23l1 14-2 21v-4h-1v-2l1-13c0-11 1-27-5-37-2-3-5-4-8-5 1 0 1-1 2-2z" class="u"></path><path d="M696 551s1 0 1 1l-2 4v1c-1 1-2 3-3 4 1 1 2 2 3 2h0c-1 1-1 2-2 2-3 0-4 0-6 2-2 1-2 4-3 6-3 8-6 17-9 26s-7 18-11 27c-2 4-3 8-6 11 0-3 4-8 5-11l1-2c0-1 1-2 2-3 0-1 0-2 1-2 0-3 2-5 2-7l2-5 2-4 8-23c0-2 0-2 1-3 0-3 1-6 1-8 0-3-2-7-3-9 0-1 0-1 1 0h1v-2c1 2 1 4 2 6l2 1v-1c-1-2-1-6 0-9l1-1v1h1c2 0 6-3 8-4z" class="t"></path><path d="M696 551s1 0 1 1l-2 4h0c-3 3-4 6-7 8h-1l1-1c-1-2-1-6-1-8h1c2 0 6-3 8-4z" class="T"></path><path d="M576 411l2-1c-2 5-4 11-4 16-1 1-1 5-1 7v1l1 2h3 3c5 2 10 4 14 5s6 3 9 4c3 2 7 5 10 7h1l3 3c2 2 3 5 4 8v1l2 1 2-1-3 5c0 2-1 3-1 4l1 2-11 15h-1l-6 21-12 34c-1 5-3 11-6 15-1 3-2 7-4 10-1 5-2 10-4 15-4 12-8 24-13 36l-3 5h1c1 0 2 1 3 0 2-2 4-3 6-4v1l-3 3v1l-4 4c-3 5-6 10-10 15l-4 10c-2-5-5-10-8-15l-17-40c-3-7-6-13-8-20 0-1-1-2-2-4l-16-37c-1-2-2-4-2-7l-18-45-4-10c-1-3-3-7-3-10 1 0 2 1 3 1 1-1-2-3-3-4 0 0-1-3-1-4h0c-1-1-2-2-2-4h0l-2-2v-1c-1-1-2-2-2-3s-1-3-1-4l-1-2c-1-2-2-4-2-6h0c4 0 9-3 13-4h1v2c-2 2-6 5-6 9v3l2 2c6 3 12 4 18 5 10 2 19 2 30 1 2-1 5-1 7-2 6-1 10-3 16-6l2-2c2 0 3-1 4-2 1 0 1 0 2-1h0c1-1 2-1 3-2h0l3-3c1-1 1-2 1-4l2-2h0c1-1 1-1 1-2l2-5 4-11 3 2c1 0 1 0 2-1 0 3-1 6-2 8l1 1h1c1-5 2-10 4-14z" class="a"></path><path d="M548 448h2v2c1 1 1 1 2 1l-7 3 3-6z" class="k"></path><path d="M562 627v1c0 2-2 5-2 7l3-4h2c-3 5-6 10-10 15 2-6 4-13 7-19z" class="R"></path><path d="M550 448c3-1 6-2 8 0 1 0 1 1 1 1 1 1 0 1 1 1 2 1 4 3 5 4v2c-1-2-3-4-5-4l-1-1h-7c-1 0-1 0-2-1v-2z" class="j"></path><path d="M562 626h1c1 0 2 1 3 0 2-2 4-3 6-4v1l-3 3v1l-4 4h-2l-3 4c0-2 2-5 2-7v-1-1z" class="W"></path><path d="M498 533h0c3-6 3-15 2-21v-5h1c1 6 2 15 1 21 0 4-1 8-2 12-1-2-2-4-2-7z" class="P"></path><path d="M495 479c1 2 5 8 5 11 1 4 0 10 0 14-1-2-1-5-2-7-2-3-4-6-4-9s1-6 1-9z" class="O"></path><path d="M543 449c2 0 3 0 5-1l-3 6c-2 1-5 3-8 5 0 1 1 1 1 3h0c-2 0-2-1-3-1-3 0-5 1-7 2 3 6 5 12 7 18 0 2 1 5 1 7-1-1-1-3-2-4-1-8-5-17-11-21h-10l3-1c1-2 3-2 4-4v-1c2-1 5-1 7-2 6-1 10-3 16-6z" class="H"></path><path d="M476 435v2c-2 2-6 5-6 9v3l2 2c6 3 12 4 18 5 10 2 19 2 30 1v1c-1 2-3 2-4 4l-3 1-28-1-9-1c-1 0-2-1-3 0h-1 0c-1-1-2-2-2-4h0l-2-2v-1c-1-1-2-2-2-3s-1-3-1-4l-1-2c-1-2-2-4-2-6h0c4 0 9-3 13-4h1z" class="Q"></path><path d="M466 441l2 1c0 2 0 3-1 4 0 1 0 1 1 3-1-1-2-1-3-2l-1-2c0-1 1-2 2-4z" class="K"></path><path d="M462 439c4 0 9-3 13-4v1c-2 3-3 3-6 4-1 0-2 1-3 1-1 2-2 3-2 4-1-2-2-4-2-6h0z" class="l"></path><path d="M466 451h1c4 2 9 3 13 6-1 1 0 1-1 1-1 1-2 1-3 1h-3c-1 1-1 1-1 2-1-1-2-2-2-4h0l-2-2v-1c-1-1-2-2-2-3z" class="q"></path><path d="M558 435l2-2c0 2 0 4 1 7v1c1 4 3 7 5 10l1 2c1 1 2 2 2 3 5 11 10 22 12 34l2 8v1c0 2 0 3 1 5 1 5 2 12 2 18v38c-1 3-2 7-4 10l1-27c0-30-4-60-18-87v-2c-1-1-3-3-5-4-1 0 0 0-1-1 0 0 0-1-1-1-2-2-5-1-8 0h-2c-2 1-3 1-5 1l2-2c2 0 3-1 4-2 1 0 1 0 2-1h0c1-1 2-1 3-2h0l3-3c1-1 1-2 1-4z" class="O"></path><path fill="#e7a7a5" d="M561 441c1 4 3 7 5 10l1 2h0c-3-4-6-6-11-7h0v-1c1 0 2-1 3-1 1-1 1-2 2-3z"></path><path d="M576 411l2-1c-2 5-4 11-4 16-1 1-1 5-1 7v1l1 2h3 3v8c-2 0-5-1-7 0h0v5c1 8 4 15 6 23 3 10 5 21 5 32-1-2-1-3-1-5v-1l-2-8c-2-12-7-23-12-34 0-1-1-2-2-3l-1-2c-2-3-4-6-5-10v-1c-1-3-1-5-1-7h0c1-1 1-1 1-2l2-5 4-11 3 2c1 0 1 0 2-1 0 3-1 6-2 8l1 1h1c1-5 2-10 4-14z" class="f"></path><path d="M569 445h4v-1 5 2h0l-2 2-1-1v-1c0-3 0-4-1-6z" class="j"></path><path d="M567 427l1-2h2v-1l1 1c-1 3-2 7-2 10l-2 2-1-1-1-1v-1l2-7z" class="k"></path><path d="M565 434v1l1 1 1 1 2-2c0 2 0 2-1 4h-1c-2 3 0 6 0 10 0 0 0 2-1 2-2-3-4-6-5-10h2c0 1 1 2 1 3h1v-1l-1-2v-6l1-1z" class="K"></path><path d="M561 440v-4c2-3 1-6 2-9 1-1 2-2 2-3l1 1c0 1 0 0 1 1v1l-2 7-1 1v6l1 2v1h-1c0-1-1-2-1-3h-2v-1z" class="Q"></path><path d="M563 426l4-11 3 2c1 0 1 0 2-1 0 3-1 6-2 8v1h-2l-1 2v-1c-1-1-1 0-1-1l-1-1c0 1-1 2-2 3-1 3 0 6-2 9v4c-1-3-1-5-1-7h0c1-1 1-1 1-2l2-5z" class="j"></path><path d="M576 411l2-1c-2 5-4 11-4 16-1 1-1 5-1 7v1l1 2h3 3v8c-2 0-5-1-7 0h0v1h-4c1-7 1-13 3-20 1-5 2-10 4-14z" class="q"></path><defs><linearGradient id="o" x1="605.389" y1="505.601" x2="560.994" y2="494.07" xlink:href="#B"><stop offset="0" stop-color="#e4dcd9"></stop><stop offset="1" stop-color="#fdffff"></stop></linearGradient></defs><path fill="url(#o)" d="M580 436c5 2 10 4 14 5s6 3 9 4c3 2 7 5 10 7h1l3 3c2 2 3 5 4 8v1l2 1 2-1-3 5c0 2-1 3-1 4l1 2-11 15h-1l-6 21-12 34c-1 5-3 11-6 15v-38c0-6-1-13-2-18 0-11-2-22-5-32-2-8-5-15-6-23v-5h0c2-1 5 0 7 0v-8z"></path><path d="M580 436c5 2 10 4 14 5s6 3 9 4c3 2 7 5 10 7h1l3 3c2 2 3 5 4 8v1l2 1 2-1-3 5c0 2-1 3-1 4l1 2-11 15h-1c1-1 1-3 2-5 1-6 4-13 2-20h0c-1-3-2-5-4-7-8-8-19-12-30-14v-8z" class="H"></path><path d="M621 464l2 1 2-1-3 5c0 2-1 3-1 4l1 2-11 15h-1c1-1 1-3 2-5h0c2-2 3-5 3-6 2-5 6-10 6-15z" class="Z"></path><path d="M580 436c5 2 10 4 14 5 4 4 10 6 15 9 2 2 5 5 6 8 1 1 1 2 2 3h-1c-2-1-3-3-6-3-8-8-19-12-30-14v-8z" class="Q"></path><defs><linearGradient id="p" x1="577.989" y1="650.263" x2="457.227" y2="618.627" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#303031"></stop></linearGradient></defs><path fill="url(#p)" d="M335 323c1 2 3 4 4 5l22 30c2 0 3-1 4-1 2 1 3 2 4 4 0-1 0-2 1-3-1-2-3-3-5-4l3-1 3 3c2 0 2 0 3 1 2 2 3 4 5 5 1 1 1 1 2 1 1 2 1 4 1 6l1 3c1 3 1 6 2 8 1 1 2 1 4 2v-1-2-2c2 0 2 2 4 2 0 1 1 2 1 2 1 1 2 1 3 2h0v-3h1l1 3c1 3 3 4 4 6s3 4 4 5v-1h1 2c1 1 0 2 0 3 1 2 3 5 5 6 0 1 1 2 1 3 1 1 2 0 3 2s2 9 4 10c0-1-1-2 0-3v-2-1h1c1 2 1 6 2 8 1 9 3 19 7 28l1 1c0-2 1-2 2-3 0 1 1 3 2 3h0c0-1 0-1 1-1v1c1 2 3 5 5 6h0c2 1 2 2 3 4l1 2 3 3c1 1 2 2 3 2h1v1c1 1 1 1 1 2v1c1 0 1 1 2 2 0 0 1 1 1 2h0c2 2 4 4 7 6s12 4 12 8l2 1 18 45c0 3 1 5 2 7l16 37c1 2 2 3 2 4 2 7 5 13 8 20l17 40c3 5 6 10 8 15l4-10c4-5 7-10 10-15l4-4v-1l3-3v-1c2-2 5-4 7-5l5-4 5-6 1 1c0 1 0 2-1 3 0 2 0 3-1 4v2s-1 1-1 2l1 2c-2 10-7 19-10 30v1l-1-1c-3 10-8 18-12 28-2 4-5 10-8 14-3 3-6 6-10 8l-4 2 3 1h2c0 1-1 4-2 5h-1 0c0-1 0-1 1-2v-2c-2 1-3 3-5 3h-3l-1 2c-2 0-3 1-4 2-1 0-1 0-2-1l-1-1-3-3c-2 0-3-1-4-2l-2-3v-1l-9-6c-4-2-7-5-11-7l-2-2-5-5v1c-1 0-1-1-1-2l-2-1-2-1h-1l-2 1v1 2c-2-10-4-20-11-27-4 5-5 15-6 22l-5 18c0 1 0 2-1 3h0c-2 5-3 11-5 16l-1-1c0-4-3-9-5-12h0c0-5-2-10-4-14l-2-3-6-16-2-7-12-31-29-67-3-10-3-4v-2c1-1 1-2 1-4s1-5 3-7c0-2 1-5 1-7 2-7 3-15 4-22 0-4 0-7 1-10h1c0-7 2-16 4-23l-1-1v-5c1-3 0-7 0-10l1-1c0-2-3-7-3-8-2-1-9-19-10-21-2-2-3-4-4-6-1-4-4-7-5-11h0c0-2 0-2-1-3v-1s-1-1-1-2l-1-1 1-2-1-2-1-2c-1-1-1-2-2-4l-1-1c0-1-1-1-2-1l-11-21h-1c-2-2-4-7-6-10-3-7-7-13-12-20-3-4-6-9-10-12-2-1-1-1-2-2l1 1 1-1v-4z"></path><path d="M538 656c2 0 7 2 10 3h1 0-3l-5-1c-2 0-4 0-5-1h1 2l-1-1z" class="p"></path><path d="M524 678c2 0 4 6 5 8l1 1h-1-1c-2-2-3-5-5-8l1-1z" class="C"></path><path d="M579 617l3 2c-5 2-9 5-13 8v-1l3-3v-1c2-2 5-4 7-5z" class="M"></path><path d="M489 627c2 0 4 4 5 6 2 2 4 3 6 5l3-4c1 2 1 5 1 7l-1 1c1 1 1 3 2 4l-1 1-15-20z" class="a"></path><path d="M515 639h1l1 1c2 2 4 4 6 5 1 1 3 2 4 3h0c2 0 3 1 5 2 1 0 2 1 3 1l3 1c-3 1-6-2-8-2l1 1 1 1h0c-1 0-2-1-3-1 0-1 0-1-1-1v1c-4 0-5-4-9-4v-1c-1-2-5-5-6-6v-1c2 2 3 3 5 4v-1l-3-3z" class="E"></path><path d="M495 643c4 3 4 7 6 11 1 2 2 2 2 4l1 1c1 1 3 3 3 5 0 1-1 2-2 3s-2 1-3 2l-1-1h-1l-1 1v-3-1l1 1c3 0 3 1 4-1s-2-4-2-6c0-1-1-2-2-3-1-2-2-5-3-7v-1c-1-1-2-3-2-5z" class="C"></path><path d="M514 658c5 7 9 11 16 15l-9-2h0 0v-1c-1 0-3-1-4-1h-1c-1-1-2-1-3-2l2-1c0-3-1-6-1-8z" class="m"></path><path d="M589 607l1 1c0 1 0 2-1 3 0 2 0 3-1 4v2s-1 1-1 2l1 2c-2 10-7 19-10 30v1l-1-1 9-27c1-2 1-3-1-5h-3l-3-2 5-4 5-6z" class="a"></path><path d="M584 613l-1 3c1 1 1 0 2 1v2h-3l-3-2 5-4zm-77 29l7 16c0 2 1 5 1 8l-2 1c1 1 2 1 3 2h1c1 0 3 1 4 1v1h0c-5 0-9 0-13 1-3 0-5 1-8 1 2-1 4-2 7-3 2-2 3-3 4-6 1-6-4-12-7-17l1-1c0 1 0 1 1 2 0 0 0 1 1 1h1 0v-3c-1-1-1-2-1-3v-1z" class="N"></path><path d="M508 680c2 0 3 1 5 2 0 0 1 0 1 1 2 1 4 3 6 4 2 0 3 0 5 1 0 1 1 1 2 1h0c7 4 14 6 21 8l-8 1c-8 1-11-2-19-5-1 0-2-1-3-1l-2-2c-2-2-5-3-6-6v-1c-1-1-2-1-2-3z" class="I"></path><path d="M457 590h2l2 6c0 2 1 7 2 8 6 22 13 45 23 65 0 2 0 3 1 4v3c0 1 0 1 1 2l-2 1v1 2c-2-10-4-20-11-27l-1-4c-1-1-1-2-2-3-1-20-9-40-15-58z" class="Y"></path><path d="M476 632c1 2 3 4 4 6l3 6c2 2 3 6 5 7s2 2 5 3l3 6s-1 0-1 1c1 2 2 2 3 3v2h1v3h0l-2 2c-3-1-4-4-6-6-7-5-9-15-12-22v-1l-2-4c0-1 0-2-1-3v-3z" class="E"></path><defs><linearGradient id="q" x1="493.151" y1="660.745" x2="490.811" y2="652.622" xlink:href="#B"><stop offset="0" stop-color="#3c3c3e"></stop><stop offset="1" stop-color="#535453"></stop></linearGradient></defs><path fill="url(#q)" d="M488 651c2 1 2 2 5 3l3 6s-1 0-1 1l-1 1c1 0 1 1 1 2h-1c-1-1-1-1-2-3s-2-4-3-7c-1-1-1-2-1-3z"></path><path d="M486 669c4 5 8 10 12 14 5 5 12 9 18 13 5 3 11 6 17 7h10l3 1h2c0 1-1 4-2 5h-1 0c0-1 0-1 1-2v-2c-2 1-3 3-5 3h-3l-1 2c-2 0-3 1-4 2-1 0-1 0-2-1l-1-1-3-3c-2 0-3-1-4-2l-2-3v-1l-9-6c-4-2-7-5-11-7l-2-2-5-5v1c-1 0-1-1-1-2l-2-1-2-1h-1c-1-1-1-1-1-2v-3c-1-1-1-2-1-4z" class="M"></path><defs><linearGradient id="r" x1="536.134" y1="709.441" x2="526.622" y2="701.546" xlink:href="#B"><stop offset="0" stop-color="#3b3a3b"></stop><stop offset="1" stop-color="#535352"></stop></linearGradient></defs><path fill="url(#r)" d="M523 705l-2-3v-1l3 1h0l8 3h1c4 0 9 0 13-1h2c0 1-1 4-2 5h-1 0c0-1 0-1 1-2v-2c-2 1-3 3-5 3h-3l-1 2c-2 0-3 1-4 2-1 0-1 0-2-1l-1-1-3-3c-2 0-3-1-4-2z"></path><path d="M523 705l-2-3v-1l3 1h0c0 2 2 4 3 5-2 0-3-1-4-2z" class="B"></path><path d="M486 642l-9-16 1-1c0 1 3 6 4 6h2l1 1v-4l3 5 3 4 4 6c0 2 1 4 2 5v1c1 2 2 5 3 7 1 1 2 2 2 3 0 2 3 4 2 6s-1 1-4 1l-1-1v1h-1v-2c-1-1-2-1-3-3 0-1 1-1 1-1l-3-6c-3-1-3-2-5-3s-3-5-5-7l1-1c0-1-1-1-1-2v-2l1 1c0 1 1 1 1 2h1z" class="D"></path><path d="M485 628l3 5 1 5v1c-3-3-3-4-4-7v-4z" class="B"></path><path d="M488 633l3 4c0 2 0 3 1 5l1 3-2-2v-1c-1-1-2-2-2-3v-1l-1-5z" class="d"></path><path d="M491 637l4 6c0 2 1 4 2 5v1h0c-1-1-2-2-4-3v-1l-1-3c-1-2-1-3-1-5z" class="S"></path><path d="M483 644l1-1c0-1-1-1-1-2v-2l1 1c0 1 1 1 1 2h1c0 1 1 2 1 3 0 2 2 4 3 5 2 1 2 2 3 4-3-1-3-2-5-3s-3-5-5-7z" class="B"></path><path d="M493 646c2 1 3 2 4 3h0c1 2 2 5 3 7 1 1 2 2 2 3 0 2 3 4 2 6s-1 1-4 1l-1-1v1h-1v-2c-1-1-2-1-3-3 0-1 1-1 1-1l2-1 1 2 1 1v-1c0-1 0-2-1-3-1-2-1-3-2-4l-3-7-1-1z" class="F"></path><defs><linearGradient id="s" x1="454.636" y1="602.616" x2="493.864" y2="623.884" xlink:href="#B"><stop offset="0" stop-color="#232323"></stop><stop offset="1" stop-color="#5f6061"></stop></linearGradient></defs><path fill="url(#s)" d="M464 586c0-1 0-2 1-4v-2c2 8 4 14 7 20l6 9c0 1 1 2 2 3v1c-1 2-1 2 0 4v1c1 4 4 7 5 10v4l-1-1h-2c-1 0-4-5-4-6l-1 1 9 16h-1c0-1-1-1-1-2l-1-1v2c0 1 1 1 1 2l-1 1-3-6c-1-2-3-4-4-6l-3-6c-2-8-5-16-7-23v-1h-1v-3l-1-1-1 1c-1 1 0 3 1 4v1h-1c-1-1-2-6-2-8l3-10z"></path><path d="M489 581c0-3-2-5-2-7l-1-3v-4c0 2 2 3 3 4v3l1 1c1 2 1 3 2 4h0l8 13 2 3c0 1 1 2 2 2v1c0 2 2 3 3 5 2 3 4 5 6 8 4 5 9 9 13 14l1 1c1 2 4 4 6 6-2 0-3-1-5-2h-1c-1-1-2-1-3-2l1 1 1 1-1 2-4-4c-1-1-1 0-2-1l-1-1-1-1c-2-1-4-3-5-5-2-1-6-5-7-7l-1-1-1 1c-1-1-1-2-1-3l-2-2v-1l-1-1c-1-1-1-2-2-4-1-1-2-3-3-4s-1-2-1-2v-1c1-2-1-2-1-4l-1-1c0-2 0-4-1-5l-1-1h1c0-1-1-2-1-3z" class="C"></path><defs><linearGradient id="t" x1="454.04" y1="534.864" x2="479.46" y2="541.636" xlink:href="#B"><stop offset="0" stop-color="#3d4042"></stop><stop offset="1" stop-color="#6e6c6b"></stop></linearGradient></defs><path fill="url(#t)" d="M455 516c3 2 5 5 7 9 3 4 7 9 10 14 0 1 1 1 2 2 2 2 4 6 7 9 2 2 4 5 5 8 2 2 3 3 4 7v1c0 1-1 3 0 4 1 3 1 6 2 9h0c-1-1-1-2-2-4l-1-1v-3c-1-1-3-2-3-4v4l1 3c0 2 2 4 2 7 0 1 1 2 1 3h-1l1 1c1 1 1 3 1 5l1 1c0 2 2 2 1 4l-18-34v1c-1-1-1-3-2-4l1-1h1c0-1 0-2-1-2 0-1-1-2-1-3h0c-1-2 0-1-1-2 0-2-2-4-3-5-1-2-1-3-2-5-1-1-2-2-2-3-2-3-2-6-4-9l-6-12z"></path><defs><linearGradient id="u" x1="491.578" y1="577.366" x2="477.037" y2="569.423" xlink:href="#B"><stop offset="0" stop-color="#3e3e3d"></stop><stop offset="1" stop-color="#59595d"></stop></linearGradient></defs><path fill="url(#u)" d="M479 564v-1l-1-2h1 1c1 2 1 3 3 4 1 1 1 2 2 4 0 4 2 8 4 12 0 1 1 2 1 3h-1l1 1c1 1 1 3 1 5-1-2-3-5-3-7-1-2-3-4-3-6l-6-13z"></path><path d="M472 539c0 1 1 1 2 2 2 2 4 6 7 9 2 2 4 5 5 8 2 2 3 3 4 7v1c0 1-1 3 0 4 1 3 1 6 2 9h0c-1-1-1-2-2-4l-1-1v-3c-1-1-3-2-3-4v4l1 3c0 2 2 4 2 7-2-4-4-8-4-12-1-2-1-3-2-4-2-1-2-2-3-4h-1-1l1 2v1l-1-1c0-2-1-2 0-4-1-3-2-6-2-8-1-3-2-5-3-7 0-1-1-4-1-5z" class="S"></path><path d="M473 544c6 6 8 13 10 21-2-1-2-2-3-4h-1-1l1 2v1l-1-1c0-2-1-2 0-4-1-3-2-6-2-8-1-3-2-5-3-7z" class="B"></path><path d="M418 450v-2h0l-1-3-1-1c0-1 0-1 1-3l-1-1 1-1h1l2 3c-1 1 0 2 0 4 2 2 5 4 6 7 1 2 3 4 4 5 1 2 3 4 4 7 2 2 4 5 5 7-3-1-4-3-7-4v1c0 1 1 2 1 3v1c1 3 3 5 4 8 2 2 4 5 5 7 0 1 0 1 1 2 0 1 1 2 1 3h-1c-2-2-3-6-5-8h-1l12 24h0v-3h0l6 10 6 12c2 3 2 6 4 9 0 1 1 2 2 3 1 2 1 3 2 5 1 1 3 3 3 5 1 1 0 0 1 2h0c0 1 1 2 1 3 1 0 1 1 1 2h-1l-1 1c1 1 1 3 2 4l19 41c4 8 7 15 10 22 2 6 2 12 3 17v1c0 1 0 2 1 3v3h0-1c-1 0-1-1-1-1-1-1-1-1-1-2-1-1-1-3-2-4l1-1c0-2 0-5-1-7 1-5 0-8-2-12-4-11-10-22-15-32l-26-56-4-8-15-31-24-44 1-1z" class="m"></path><path d="M449 509h0v-3h0l6 10 6 12c2 3 2 6 4 9 0 1 1 2 2 3 1 2 1 3 2 5 1 1 3 3 3 5 1 1 0 0 1 2h0c0 1 1 2 1 3 1 0 1 1 1 2h-1l-1 1-24-49z" class="D"></path><defs><linearGradient id="v" x1="431.398" y1="458.875" x2="414.206" y2="444.086" xlink:href="#B"><stop offset="0" stop-color="#727172"></stop><stop offset="1" stop-color="#929393"></stop></linearGradient></defs><path fill="url(#v)" d="M418 450v-2h0l-1-3-1-1c0-1 0-1 1-3l-1-1 1-1h1l2 3c-1 1 0 2 0 4 2 2 5 4 6 7 1 2 3 4 4 5 1 2 3 4 4 7 2 2 4 5 5 7-3-1-4-3-7-4v1c0 1 1 2 1 3v1c1 3 3 5 4 8 2 2 4 5 5 7 0 1 0 1 1 2 0 1 1 2 1 3h-1c-2-2-3-6-5-8h-1c-2-3-3-7-5-11l-9-16c-2-3-3-5-5-8z"></path><path d="M432 469l-1-1c-1-2-3-3-3-6 0 0 0-1-1-2 0 0-1-1-1-2 3 2 4 5 7 6l1 1c2 2 4 5 5 7-3-1-4-3-7-4v1z" class="B"></path><path d="M432 469v-1c3 1 4 3 7 4l8 12c1 2 2 3 3 5l-4-3c0 1 0 0 1 1l-1 1 4 5c1 2 1 3 3 5 1 1 1 2 2 3 2 1 3 4 4 6l-1 1v-1h-1l9 14 18 23c3 4 7 7 11 11 2 3 4 5 7 8l2 1v1 2c1 2 1 1 1 3-1-1-2-2-4-3s-4-3-7-2c-2 1-3 3-4 5-1-1 0-3 0-4v-1c-1-4-2-5-4-7-1-3-3-6-5-8-3-3-5-7-7-9-1-1-2-1-2-2-3-5-7-10-10-14-2-4-4-7-7-9l-6-10h0v3h0l-12-24h1c2 2 3 6 5 8h1c0-1-1-2-1-3-1-1-1-1-1-2-1-2-3-5-5-7-1-3-3-5-4-8v-1c0-1-1-2-1-3z" class="C"></path><path d="M432 469v-1c3 1 4 3 7 4l8 12c1 2 2 3 3 5l-4-3c0 1 0 0 1 1l-1 1 4 5c1 2 1 3 3 5 1 1 1 2 2 3 2 1 3 4 4 6l-1 1v-1h-1l9 14 18 23c3 4 7 7 11 11 2 3 4 5 7 8l2 1v1 2c1 2 1 1 1 3-1-1-2-2-4-3s-4-3-7-2c-2 1-3 3-4 5-1-1 0-3 0-4 1-2 3-3 4-5h1c-3-6-8-10-11-15l-12-15-15-21c-5-7-9-15-15-22-1-2-3-5-5-7-1-3-3-5-4-8v-1c0-1-1-2-1-3z" class="M"></path><path d="M501 567v-2h-2v-1l-1-1 1-1 1 1 1 1 1-1 2 1v1 2c1 2 1 1 1 3-1-1-2-2-4-3z" class="W"></path><path d="M432 469v-1c3 1 4 3 7 4l8 12c1 2 2 3 3 5l-4-3c0 1 0 0 1 1l-1 1 4 5c1 2 1 3 3 5 1 1 1 2 2 3 2 1 3 4 4 6l-1 1v-1h-1c-3-4-6-8-9-13-5-7-11-14-15-22 0-1-1-2-1-3z" class="J"></path><defs><linearGradient id="w" x1="482.333" y1="583.058" x2="470.431" y2="588.829" xlink:href="#B"><stop offset="0" stop-color="#1d1d1d"></stop><stop offset="1" stop-color="#4f5050"></stop></linearGradient></defs><path fill="url(#w)" d="M449 540h0c1-4-3-5-3-9 0-1-1-2-1-3-1-2-1-1-1-2-1-2-2-5-3-7l-1-2c0-1 0-1-1-2v-2h0c1 1 2 3 2 4l3 6 3 6h2c1 2 2 4 4 4 1 3 3 6 5 7h1c1 2 2 4 4 5l1-1-2-3-1-2h0c-1-2-1-3-1-5l26 56c5 10 11 21 15 32 2 4 3 7 2 12l-3 4c-2-2-4-3-6-5-1-2-3-6-5-6-1-1-2-2-2-3l-7-11v-1c-1-1-2-2-2-3l-6-9c-3-6-5-12-7-20v2c-1 2-1 3-1 4h-1v-3-1-1c1-1 1-2 1-2h1v-4l-1-1v-1h0v-2l-1-1c0-2 0-2-1-3 0-1-1-2-1-4-1-5-5-9-6-14l-2-3c-2-2-2-3-4-4v-2z"></path><path d="M447 529h2c1 2 2 4 4 4 1 3 3 6 5 7 1 1 1 1 1 2s0 0 1 1l4 9s1 1 1 2h-1l-2-2c-1-3-3-5-5-7-1-2-2-3-3-5-1 0-1-1-2-2 0-1 1 0 0-1v2h0c-2-2-4-7-5-10z" class="I"></path><path d="M455 549v-3h0c2 2 2 4 3 6 0 1 1 2 2 3s1 3 2 4c0 2 2 3 3 4 1 3 3 7 4 10l5 11c1 2 3 3 3 5h-1-1l-3-5v2c-1-2-2-4-2-5-1-3-3-6-5-8v2l-1-1v-1h0v-2l-1-1c0-2 0-2-1-3 0-1-1-2-1-4-1-5-5-9-6-14z" class="J"></path><path d="M465 575v-2c2 2 4 5 5 8 0 1 1 3 2 5l-1 1c1 1 1 1 1 2 0 2 1 3 2 5 1 0 1 1 2 2v1h0c1 1 1 1 1 2l1 1 1 2v2l-1-1v1c0 1 1 3 1 4l1 2h0c1 1 1 2 1 3l2 2v1c-1-1-2-4-3-4-1-1-2-2-2-3l-6-9c-3-6-5-12-7-20v2c-1 2-1 3-1 4h-1v-3-1-1c1-1 1-2 1-2h1v-4z" class="G"></path><path d="M474 594h-1l2 2c0 1 1 2 1 3h-1 0v-1l-1-1c-1-2-2-3-3-4-1-2-2-5-1-7h0l1 1c1 1 1 1 1 2 0 2 1 3 2 5z" class="B"></path><defs><linearGradient id="x" x1="496.275" y1="513.784" x2="473.404" y2="529.41" xlink:href="#B"><stop offset="0" stop-color="#0e0e0e"></stop><stop offset="1" stop-color="#323233"></stop></linearGradient></defs><path fill="url(#x)" d="M434 465c-1-3-3-5-4-7-1-1-3-3-4-5-1-3-4-5-6-7 0-2-1-3 0-4l10 13c0-2-1-4-2-6v-3-1l4 7c1-2 1-3 1-5l1 1c0-2 1-2 2-3 0 1 1 3 2 3h0c0-1 0-1 1-1v1c1 2 3 5 5 6h0c2 1 2 2 3 4l1 2 3 3c1 1 2 2 3 2h1v1c1 1 1 1 1 2v1c1 0 1 1 2 2 0 0 1 1 1 2h0c2 2 4 4 7 6s12 4 12 8l2 1 18 45c0 3 1 5 2 7l16 37c1 2 2 3 2 4-3-2-5-4-8-7l-5-4c0-2 0-1-1-3v-2-1l-2-1c-3-3-5-5-7-8-4-4-8-7-11-11l-18-23-9-14h1v1l1-1c-1-2-2-5-4-6-1-1-1-2-2-3-2-2-2-3-3-5l-4-5 1-1c-1-1-1 0-1-1l4 3c-1-2-2-3-3-5l-8-12c-1-2-3-5-5-7z"></path><path d="M458 482h2c1 2 2 3 2 4-1 0-1 0-2-1 0 2 1 3 1 5l-2-2c-2-1-2-3-2-5 1 0 1 1 2 1l-1-2z" class="I"></path><path d="M459 507h0c3 5 8 8 7 14l-9-14h1v1l1-1z" class="S"></path><path d="M459 473c2 2 4 4 7 6s12 4 12 8c-2 0-3-2-5-3-2 0-4-1-5-2h-1c1 1 1 1 2 1l1 1h0-2c2 2 4 5 6 8h0c-2-2-4-3-6-6-1-1-3-2-4-4v-1h-1l-3-3 1-2c-1-1-1-1-2-3z" class="J"></path><path d="M455 479c1 0 2 1 3 1h1v1l-1 1 1 2c-1 0-1-1-2-1 0 2 0 4 2 5l2 2c2 4 5 6 8 10l3 9h0l-3-5c-2-3-4-7-7-9-3-4-6-7-8-12 1 0 1 0 2 1v-2-1l-1-2z" class="C"></path><path d="M504 564h0c-4-4-8-8-11-12 4 2 7 6 10 9 2 2 5 4 7 6 0 1 1 2 1 3 1 2 3 4 4 6 0 1 0 1 1 1h0c1 2 2 3 2 4-3-2-5-4-8-7l-5-4c0-2 0-1-1-3v-2-1z" class="m"></path><path d="M504 565c1 1 3 2 4 3h0c0 2 1 4 2 5v1l-5-4c0-2 0-1-1-3v-2z" class="e"></path><path d="M434 465c-1-3-3-5-4-7-1-1-3-3-4-5-1-3-4-5-6-7 0-2-1-3 0-4l10 13c0-2-1-4-2-6v-3-1l4 7c1-2 1-3 1-5l1 1c0-2 1-2 2-3 0 1 1 3 2 3h0c0-1 0-1 1-1v1c1 2 3 5 5 6h0c2 1 2 2 3 4l1 2 3 3c1 1 2 2 3 2h1v1c1 1 1 1 1 2v1c1 0 1 1 2 2 0 0 1 1 1 2h0c1 2 1 2 2 3l-1 2 3 3h1v1c1 2 3 3 4 4-1 0-1 0-2-1l-2-2-1 1c0-1-1-1-1-2l-3-1v-1h-1c-1 0-2-1-3-1l1 2v1 2c-1-1-1-1-2-1 2 5 5 8 8 12 3 2 5 6 7 9l-2 3-3-3v-1c-2-1-3-2-4-4-1-1-1-2-2-2l-1-1c0-1-1-3-2-3 1 2 2 3 2 5 0 1 0 1 1 1 0 1 1 2 1 3l1 1-2 1-1-2c-1-2-2-3-3-4h-1c-2-2-2-3-3-5l-4-5 1-1c-1-1-1 0-1-1l4 3c-1-2-2-3-3-5l-8-12c-1-2-3-5-5-7z" class="F"></path><path d="M451 476l1 1c1 1 2 2 3 2l1 2v1 2c-1-1-1-1-2-1s-1-1-2-1h0 0c-2-2-2-3-2-6h1z" class="J"></path><path d="M452 482c0-1 0-2 1-4 0 1 1 2 2 2l1 1v1 2c-1-1-1-1-2-1s-1-1-2-1z" class="E"></path><path d="M436 464v-4h0 2l1 2c1 2 7 9 8 9 2 1 2 2 3 3l1 2h-1c-1-1-3-2-4-4s-4-4-6-6c1 2 1 4 3 7l1 4v1c-3-4-6-10-8-14z" class="B"></path><path d="M434 465c-1-3-3-5-4-7-1-1-3-3-4-5-1-3-4-5-6-7 0-2-1-3 0-4l10 13c2 3 4 7 6 9 2 4 5 10 8 14l6 9 3 5h-1l-2-3c-1-2-2-3-3-5l-8-12c-1-2-3-5-5-7z" class="e"></path><path d="M434 448c0-2 1-2 2-3 0 1 1 3 2 3h0c0-1 0-1 1-1v1c1 2 3 5 5 6h0c2 1 2 2 3 4l1 2 3 3c1 1 2 2 3 2h1v1c1 1 1 1 1 2v1c1 0 1 1 2 2 0 0 1 1 1 2h0c1 2 1 2 2 3l-1 2 3 3h1v1c1 2 3 3 4 4-1 0-1 0-2-1l-2-2-1 1c0-1-1-1-1-2l-3-1v-1h-1c-1 0-2-1-3-1s-2-1-3-2l-1-1-1-2c-1-1-1-2-3-3-1 0-7-7-8-9l-1-2h-2 0v4c-2-2-4-6-6-9 0-2-1-4-2-6v-3-1l4 7c1-2 1-3 1-5l1 1z" class="E"></path><path d="M437 455l3 3c2 0 2 1 4 3h0c3 3 4 7 7 11h0-1v2c-1-1-1-2-3-3-1 0-7-7-8-9v-2c-1-2-1-3-2-5z" class="d"></path><g class="D"><path d="M437 455l3 3c2 4 4 8 7 13-1 0-7-7-8-9v-2c-1-2-1-3-2-5z"></path><path d="M428 445l4 7c1-2 1-3 1-5l1 1c1 2 2 4 3 7 1 2 1 3 2 5v2l-1-2h-2 0v4c-2-2-4-6-6-9 0-2-1-4-2-6v-3-1z"></path></g><path d="M433 447l1 1c1 2 2 4 3 7 1 2 1 3 2 5v2l-1-2c-1-2-3-3-4-5v-1c-1-1-1-2-2-2 1-2 1-3 1-5z" class="R"></path><defs><linearGradient id="y" x1="419.308" y1="448.299" x2="402.847" y2="455.86" xlink:href="#B"><stop offset="0" stop-color="#060606"></stop><stop offset="1" stop-color="#2b2b2c"></stop></linearGradient></defs><path fill="url(#y)" d="M335 323c1 2 3 4 4 5l22 30c2 0 3-1 4-1 2 1 3 2 4 4 0-1 0-2 1-3-1-2-3-3-5-4l3-1 3 3c2 0 2 0 3 1 2 2 3 4 5 5 1 1 1 1 2 1 1 2 1 4 1 6l1 3c1 3 1 6 2 8 1 1 2 1 4 2v-1-2-2c2 0 2 2 4 2 0 1 1 2 1 2 1 1 2 1 3 2h0v-3h1l1 3c1 3 3 4 4 6s3 4 4 5v-1h1 2c1 1 0 2 0 3 1 2 3 5 5 6 0 1 1 2 1 3 1 1 2 0 3 2s2 9 4 10c0-1-1-2 0-3v-2-1h1c1 2 1 6 2 8 1 9 3 19 7 28 0 2 0 3-1 5l-4-7v1 3c1 2 2 4 2 6l-10-13-2-3h-1l-1 1 1 1c-1 2-1 2-1 3l1 1 1 3h0v2l-1 1 24 44 15 31 4 8c0 2 0 3 1 5h0l1 2 2 3-1 1c-2-1-3-3-4-5h-1c-2-1-4-4-5-7-2 0-3-2-4-4h-2l-3-6-3-6c0-1-1-3-2-4 0-2-2-4-3-6l-1-2c0-1-1-2-1-3l-1-1c0-1 0-1-1-2h0-1l1-1h0-1c-1-1-2-3-3-5l-2-3-1-3-3-4c-1-2-2-4-3-5v-1c-2-2-5-7-8-8-1-1-1 0-2 0-2 1-3 3-3 5l-1-1v-5c1-3 0-7 0-10l1-1c0-2-3-7-3-8-2-1-9-19-10-21-2-2-3-4-4-6-1-4-4-7-5-11h0c0-2 0-2-1-3v-1s-1-1-1-2l-1-1 1-2-1-2-1-2c-1-1-1-2-2-4l-1-1c0-1-1-1-2-1l-11-21h-1c-2-2-4-7-6-10-3-7-7-13-12-20-3-4-6-9-10-12-2-1-1-1-2-2l1 1 1-1v-4z"></path><path d="M444 523c1-1 1-1 1 0l2 2c1 1 1 3 2 4h-2l-3-6z" class="p"></path><path d="M411 460c-1-2-2-4-2-5v-1-1h0l1 2c1 1 2 3 4 4l6 11c-4-3-6-6-9-10z" class="G"></path><path d="M406 474l-1-1v-5c1-3 0-7 0-10l1-1c1 3 3 6 5 8 2 4 6 8 8 13v-1c-2-2-5-7-8-8-1-1-1 0-2 0-2 1-3 3-3 5z" class="c"></path><path d="M335 323c1 2 3 4 4 5l22 30c2 0 3-1 4-1 2 1 3 2 4 4l1 2c2 2 3 4 5 7h0c0 1 1 2 2 3v2l-2 2 2 4 5 7c1 2 2 3 2 5l-12-18c-5-7-9-13-15-18 2 5 4 9 7 14h-1c-2-2-4-7-6-10-3-7-7-13-12-20-3-4-6-9-10-12-2-1-1-1-2-2l1 1 1-1v-4z" class="M"></path><path d="M365 357c2 1 3 2 4 4l1 2c-2 2-2 2-2 4l-7-9c2 0 3-1 4-1z" class="G"></path><path d="M370 363c2 2 3 4 5 7h0c0 1 1 2 2 3v2l-2 2-7-10c0-2 0-2 2-4z" class="B"></path><path d="M381 400c1 1 2 1 2 1 1 1 2 3 2 4l4 7c4 6 9 12 12 19l1 2 3 6 4 7v1c0 1 2 3 2 5 1 2 2 4 3 7-2-1-3-3-4-4l-1-2h0v1 1c0 1 1 3 2 5-1-1-1-1-1-2h0-2s-1-1-1-2v-2c-1 0-1-1-1-1l-2-4h-1c-2-1-9-19-10-21-2-2-3-4-4-6-1-4-4-7-5-11h0c0-2 0-2-1-3v-1s-1-1-1-2l-1-1 1-2-1-2z" class="J"></path><path d="M384 411h0c0-2 0-2-1-3v-1s-1-1-1-2l-1-1 1-2c1 1 1 3 2 4 2 7 8 11 8 19l1 3c-2-2-3-4-4-6-1-4-4-7-5-11z" class="L"></path><path d="M368 353l3 3c2 0 2 0 3 1 2 2 3 4 5 5 1 1 1 1 2 1 1 2 1 4 1 6l1 3c1 3 1 6 2 8 1 1 2 1 4 2v-1-2-2c2 0 2 2 4 2 0 1 1 2 1 2 1 1 2 1 3 2h0v-3h1l1 3c1 3 3 4 4 6s3 4 4 5v-1h1 2c1 1 0 2 0 3 1 2 3 5 5 6 0 1 1 2 1 3 1 1 2 0 3 2s2 9 4 10c0-1-1-2 0-3v-2-1h1c1 2 1 6 2 8 1 9 3 19 7 28 0 2 0 3-1 5l-4-7v1 3c1 2 2 4 2 6l-10-13-2-3h-1l-1 1 1 1c-1 2-1 2-1 3l1 1 1 3h0v2l-1 1c-2-3-3-6-4-8l-6-10-14-25c-3-5-6-9-9-15 0-2-1-3-2-5l-5-7-2-4 2-2v-2c-1-1-2-2-2-3h0c-2-3-3-5-5-7l-1-2c0-1 0-2 1-3-1-2-3-3-5-4l3-1z" class="V"></path><path d="M401 408c2 2 3 5 4 7h-1 0c-2-1-2-4-3-6v-1z" class="r"></path><path d="M418 439c-1-2-2-3-3-5l-1-1c-1-2-1-3-2-5 0-1-1-2-1-3h1 2 1c0 1 0 0 1 1l1 4c-1 0-1 0-2 1 0 1 0 3 1 3 0 1 1 1 1 2 0 0 0 1 1 1v2h0z" class="s"></path><defs><linearGradient id="z" x1="404.695" y1="402.684" x2="398.268" y2="406.457" xlink:href="#B"><stop offset="0" stop-color="#777778"></stop><stop offset="1" stop-color="#919091"></stop></linearGradient></defs><path fill="url(#z)" d="M401 408l-1-1-1 1c-2-1-2-5-3-6-1-2 0-3-1-5h0v-2c0 2 1 2 2 3 2 1 3 2 4 4l1-1h3c0 2 1 4 2 6 0 2 1 3 1 5-1 1-1 2-3 3-1-2-2-5-4-7z"></path><path d="M418 439v-2c-1 0-1-1-1-1 0-1-1-1-1-2-1 0-1-2-1-3 1-1 1-1 2-1l4 6c2 2 4 5 6 7 1 0 1 1 1 2v1 3c1 2 2 4 2 6l-10-13-2-3h0 0z" class="Z"></path><path d="M418 439v-2c-1 0-1-1-1-1 0-1-1-1-1-2-1 0-1-2-1-3 1-1 1-1 2-1l4 6 2 5 2 5-3-3-4-4z" class="B"></path><path d="M413 413c1 0 2 1 3 2s0 0 1 0c1 1 2 2 2 3s2 2 2 2v2l1 1c2 1 4 8 5 11h0v1l-1-1-1-1-2 1c0 2 1 2 1 3-1-1-3-3-4-5l-4-9c2-2 1-2 2-5-1 0-1-1-1-1h-1v1l-2-1c0-2-1-3-1-4h0z" class="s"></path><path d="M413 413c1 0 2 1 3 2s0 0 1 0c1 1 2 2 2 3s2 2 2 2v2l1 1c2 1 4 8 5 11h0v1l-1-1-3-3c-1-2-1-4-2-6-1-1-2-2-2-4 0-1-1-2-1-3-1 0-1-1-1-1h-1v1l-2-1c0-2-1-3-1-4h0z" class="L"></path><path d="M407 393h1 2c1 1 0 2 0 3 1 2 3 5 5 6 0 1 1 2 1 3 1 1 2 0 3 2s2 9 4 10c0-1-1-2 0-3v-2c1 9 3 17 5 25v2-1l-1-3v-1h0c-1-3-3-10-5-11l-1-1v-2-1c0-1-1-1-1-2h0l-3-3h-1l-1-1c1-1 1-3 0-4h0l-1 1c-2-1-2-3-3-5l-4-10v-1-1z" class="E"></path><path d="M368 353l3 3c2 0 2 0 3 1 2 2 3 4 5 5 1 1 1 1 2 1 1 2 1 4 1 6l1 3-1 2 1 2 1 3v3c2 4 4 8 5 12h-1l-3-6h-3l-5-7-2-4 2-2v-2c-1-1-2-2-2-3h0c-2-3-3-5-5-7l-1-2c0-1 0-2 1-3-1-2-3-3-5-4l3-1z" class="R"></path><path d="M375 370h1c1 1 3 2 3 3 1 3 4 7 3 10h0-1c-1-2-2-2-4-2l-2-4 2-2v-2c-1-1-2-2-2-3h0z" class="D"></path><path d="M368 353l3 3c2 0 2 0 3 1 2 2 3 4 5 5 1 1 1 1 2 1l-2 1v-1c-2 0-3-2-4-1 1 2 2 4 4 6v2h-1l-2-1v1h-1c-2-3-3-5-5-7l-1-2c0-1 0-2 1-3-1-2-3-3-5-4l3-1z" class="L"></path><path d="M369 361c0-1 0-2 1-3 2 3 7 8 6 11v1h-1c-2-3-3-5-5-7l-1-2z" class="i"></path><path d="M389 379v-2c2 0 2 2 4 2 0 1 1 2 1 2 1 1 2 1 3 2h0v-3h1l1 3c1 3 3 4 4 6s3 4 4 5v1l4 10c1 2 1 4 3 5l1-1h0c1 1 1 3 0 4l1 1h1l3 3h0c0 1 1 1 1 2v1s-2-1-2-2-1-2-2-3c-1 0 0 1-1 0s-2-2-3-2h0c0 1 1 2 1 4l2 1v-1h1s0 1 1 1c-1 3 0 3-2 5-2-2-4-6-4-9-1-1-1-2-2-3l-4-9c-1-3-1-5-3-7v1l2 5h-3l-1 1c-1-2-2-3-4-4l-1-3c-1-1-1-2-1-2-1-1-1-2-1-3v-1-2h-1v3h-1v-4c-1 1-1 1-2 1l-1-1v2 1l-2-3-2-6c1 1 2 1 4 2v-1-2z" class="D"></path><path d="M394 389c2 0 3 1 4 3v1l3 6c-2-2-3-5-5-6-1-1-1-2-2-3v-1z" class="i"></path><path d="M394 390c1 1 1 2 2 3 2 1 3 4 5 6 1 0 1 1 1 2l-1 1c-1-2-2-3-4-4l-1-3c-1-1-1-2-1-2-1-1-1-2-1-3z" class="R"></path><path d="M389 379v-2c2 0 2 2 4 2 0 1 1 2 1 2 1 1 2 1 3 2h0v-3h1l1 3c1 3 3 4 4 6s3 4 4 5v1l4 10c1 2 1 4 3 5l1-1h0c1 1 1 3 0 4l1 1h1l3 3h0c0 1 1 1 1 2v1s-2-1-2-2-1-2-2-3c-1 0 0 1-1 0s-2-2-3-2l-1-1c0-1-1-2-1-3-1-4-3-7-4-10-1-2-2-4-3-5-1-2-2-3-3-4 0-2 0-3-2-4 0 2 1 4 2 6v1l-2-2c0-1 0-1-1-2-1 0-1-1-2-1v-1c-1-1-2-2-2-3v-1h-2v-2l-1 1c-1 1-1 3-3 4h-1l-2-6c1 1 2 1 4 2v-1-2z" class="B"></path><defs><linearGradient id="AA" x1="391.672" y1="541.783" x2="446.648" y2="553.609" xlink:href="#B"><stop offset="0" stop-color="#050505"></stop><stop offset="1" stop-color="#2c2c2d"></stop></linearGradient></defs><path fill="url(#AA)" d="M406 474c0-2 1-4 3-5 1 0 1-1 2 0 3 1 6 6 8 8v1c1 1 2 3 3 5l3 4 1 3 2 3c1 2 2 4 3 5h1 0l-1 1h1 0c1 1 1 1 1 2l1 1c0 1 1 2 1 3l1 2c1 2 3 4 3 6h0v2c1 1 1 1 1 2l1 2c1 2 2 5 3 7 0 1 0 0 1 2 0 1 1 2 1 3 0 4 4 5 3 9h0v2c2 1 2 2 4 4l2 3c1 5 5 9 6 14 0 2 1 3 1 4 1 1 1 1 1 3l1 1v2h0v1l1 1v4h-1s0 1-1 2v1 1 3h1l-3 10-2-6h-2c6 18 14 38 15 58 1 1 1 2 2 3l1 4c-4 5-5 15-6 22l-5 18c0 1 0 2-1 3h0c-2 5-3 11-5 16l-1-1c0-4-3-9-5-12h0c0-5-2-10-4-14l-2-3-6-16-2-7-12-31-29-67-3-10-3-4v-2c1-1 1-2 1-4s1-5 3-7c0-2 1-5 1-7 2-7 3-15 4-22 0-4 0-7 1-10h1c0-7 2-16 4-23z"></path><path d="M395 536c0-2 1-5 1-7 2-7 3-15 4-22 0-4 0-7 1-10h1l-4 52c0 5-1 10-1 14l-3-10-3-4v-2c1-1 1-2 1-4s1-5 3-7z" class="D"></path><path d="M395 536c-1 3-1 6-1 9 0 2 1 7 0 8l-3-4v-2c1-1 1-2 1-4s1-5 3-7z" class="C"></path><path d="M433 532v-1l3 4v-1c0-2-2-5-4-6-1-3-2-6-4-8l-12-28h1l7 15v1c1 2 2 3 2 4l1 1 2 5h0l-4-10 1-1c1 1 6 9 6 11v1c1 3 4 6 5 9 0 1 1 1 1 2l1-1h2 0l-1-1c0-3-3-6-3-9l-4-7h0c5 7 7 16 12 23v2h0 1 1v2 1l-1-1c-1 3-1 3 0 6 1 1 1 2 2 3 0-1-1-2-1-2l1-1h1l-3-3c1-2 1-1 3-2v2c2 1 2 2 4 4l2 3c1 5 5 9 6 14 0 2 1 3 1 4 1 1 1 1 1 3l1 1v2h0v1l1 1v4h-1s0 1-1 2v1 1 3h1l-3 10-2-6h-2c0-1-1-4-2-5-1-3-3-6-5-10l-1-5c0-2-1-4-3-5-1-1-1-2-2-3-1-9-5-17-8-25-1 0-3-4-3-5z" class="d"></path><path d="M436 537c3 2 12 25 14 29l1 4h-2c0-2-1-4-3-5-1-1-1-2-2-3-1-9-5-17-8-25z" class="M"></path><path d="M450 566v-1c0-1-1-2-1-4-2-4-4-8-5-12h1c0 1 1 1 1 2 2 4 4 8 5 12h1c2 4 4 11 7 14l1 1h1v-2c1-2 1-3 2-3h1v1c-1 2-2 3-2 5h-2c-1 0-3-1-4-1-1-2-2-3-3-5v1c-1-1-2-3-2-4l-1-4z" class="D"></path><path d="M449 542c2 1 2 2 4 4l2 3c1 5 5 9 6 14 0 2 1 3 1 4 1 1 1 1 1 3l1 1v2h0-1c-1 0-1 1-2 3v2h-1l-1-1h2c-1-2-1-3-1-4v-5c-1-1-2-1-2-3h0 0v3c-1-1 0-1-1-1 0-2 0-3-1-4s-1-3-1-5c-1-3-3-5-4-9 0-2-1-4-2-6h-1l1-1z" class="G"></path><path d="M460 568c-1-1-1-2-1-3s-1-3-2-4l1-1v1l3 2c0 2 1 3 1 4 1 1 1 1 1 3l1 1v2h0-1c-1 0-1 1-2 3v2h-1l-1-1h2c-1-2-1-3-1-4v-5z" class="L"></path><path d="M451 570c0 1 1 3 2 4v-1c1 2 2 3 3 5 1 0 3 1 4 1h2c0-2 1-3 2-5l1 1v4h-1s0 1-1 2v1 1 3h1l-3 10-2-6h-2c0-1-1-4-2-5-1-3-3-6-5-10l-1-5h2z" class="N"></path><path d="M453 574v-1c1 2 2 3 3 5 1 3 2 4 5 6v1h-1c-1 1-1 2-1 4h0l-6-15z" class="F"></path><path d="M464 574l1 1v4h-1s0 1-1 2v1 1 3h1l-3 10-2-6v-1h0c0-2 0-3 1-4h1v-1c-3-2-4-3-5-6 1 0 3 1 4 1h2c0-2 1-3 2-5z" class="B"></path><path d="M464 574l1 1v4h-1s0 1-1 2v1 1 3l-1 1v1-3c0-3-1-4 0-6 0-2 1-3 2-5z" class="I"></path><g class="n"><path d="M426 630c1-1 1-3 2-4v-3c0-1 0-1 1-2v-3c1-1 1-2 1-3 1-4 1-9 2-14 0-1-1-3 0-5 1-4 1-9 1-14v-2-18c0-4 1-9 0-13s0-8-1-12c-1-1-1-2-1-3l1 1h1l-1-3h1c0 1 2 5 3 5 3 8 7 16 8 25l-2 32c-1 4-1 9-1 14l-1 40c-1 5-1 9-2 13l-12-31z"></path><path d="M444 562c1 1 1 2 2 3 2 1 3 3 3 5l1 5c2 4 4 7 5 10 1 1 2 4 2 5 6 18 14 38 15 58 1 1 1 2 2 3l1 4c-4 5-5 15-6 22l-5 18c0 1 0 2-1 3h0c-2 5-3 11-5 16l-1-1c0-4-3-9-5-12h0c0-5-2-10-4-14l-2-3-6-16-2-7c1-4 1-8 2-13l1-40c0-5 0-10 1-14l2-32z"></path></g><path d="M472 648c1 1 1 2 2 3l1 4c-4 5-5 15-6 22l-5 18c0 1 0 2-1 3h0c1-5 2-11 4-16 2-11 4-23 5-34z" class="N"></path><path d="M444 562c1 1 1 2 2 3 2 1 3 3 3 5l1 5h-1-3c-3 6-3 12-3 18 0 4-1 8-1 12v-11l2-32z" class="W"></path><path d="M446 575c0-1-1-5 1-6v1c0 1 0 1 1 2 0 1 1 2 1 3h-3z" class="N"></path><path d="M438 661c1-4 1-8 2-13l1-40c0-5 0-10 1-14v11c1 6 1 13 1 19-1 14 0 29-3 44l-2-7z" class="F"></path><path d="M651 181h167 43c9 0 19-1 27 0l10 1c-3 1-8 2-12 2l-15 4c-24 7-49 17-67 34-6 6-12 12-14 19-2 5-2 10-1 15h1c3 11 13 17 21 23-19-5-37-7-54 3-24 12-38 40-50 63-5 10-11 20-14 31-1 2-1 3-2 5-7 12-13 26-18 39-3 7-6 13-8 20l-12 31c2 2 4 4 6 7l2 4 1 1 2 1h1c-1-1-1-1-1-2v-2c0-1-1-2-1-3s0-1 1-2l9 12c1 0 3 2 3 2 2 2 5 3 8 4l2 4-1 1c-2-1-2-1-4-1v1c2 1 2 0 4 0h2c3-2 4-1 7-2l1-1 1-1h2c0 1 0 0-1 1-2 1-3 3-5 4l-3 6 3-2 1 1v4c-1 2-2 4-2 6 1 1 2 2 3 2s2-1 4-2c1 5 2 9 3 13h2c-2 8-3 16-7 24-2 1-6 4-8 4h-1v-1l-1 1c-1 3-1 7 0 9v1l-2-1c-1-2-1-4-2-6v2h-1c-1-1-1-1-1 0 1 2 3 6 3 9 0 2-1 5-1 8-1 1-1 1-1 3l-8 23-2 4-2 5c0 2-2 4-2 7-1 0-1 1-1 2-1 1-2 2-2 3l-1 2c-1 3-5 8-5 11-1 2-2 4-3 5-9 13-18 24-29 35-7 5-14 10-22 14-3 2-6 3-9 5l-5 1c-4 2-9 4-13 4-2 1-3 1-5 1l2-4-2-2-1 4v-1c0-1 1-1 0-2v-5l-1 2-1-1v-5l1 4v-1-1l2-9h0l1 1c1-2 3-8 2-10 0 0-1-1-1-2l1-1v-3c1-1 0-1 1-2h0v-3c0-1 1-2 1-3v-1c0-1 1-2 2-3h0c0-2 0-2-1-3 3-11 8-20 10-30l-1-2c0-1 1-2 1-2v-2c1-1 1-2 1-4 1-1 1-2 1-3l-1-1-5 6-5 4c-2 1-5 3-7 5-2 1-4 2-6 4-1 1-2 0-3 0h-1l3-5c5-12 9-24 13-36 2-5 3-10 4-15 2-3 3-7 4-10 3-4 5-10 6-15l12-34 6-21h1l11-15-1-2c0-1 1-2 1-4l3-5-2 1-2-1v-1c1 0 0 0 1-1l1-3v-1s0-1-1-1c3-2 4-5 6-7-1-1-1-3-1-5h0c3-7 4-14 7-21 1-5 3-10 4-15l8-25h1l6-6 2-2c1-2 3-3 4-4l2-3 1-1 5-7c2-3 4-7 5-10l1-2 2-3h0c1-2 1-3 2-4l3-7c1-1 1-1 1-2s1-1 1-2c1-2 2-5 2-7 0-5 3-9 5-13l1 1 3-8 7-24-1-2c0-2 1-6 2-7 1-2 1-2 3-3v2l6-7-2-1c-1 1-2 1-3 2-1 0-1 0-2-1-1-2-1-4-1-6h0v4l-1 1 1 1h0c0 1 0 1 1 2v1h0c-1 1 0 1-1 1s-2 1-2 1v-4c0-2 0-3-1-4 1-11 0-22-5-32-2-3-4-5-6-8-1-1-1-2-2-3v-1-1l-2-4c1-1 1-2 2-4-1-3-6-6-8-8-2-1-5-2-7-4l-1-1h-1l-1-1c-1 0-2-1-2-1-1 0-2-1-3-1v-1h0c-1-1-2-1-3-2-1 0-1 0-2-1h-1c-1-1-3-2-4-2h-1c-1-1-2-1-4-1-1-1-4-1-5-2h8v-1z" class="n"></path><path d="M574 670c1 1 3 1 4 2-1 4-3 7-4 11 0 1 0 4-1 4v-5c1-2 3-8 2-10 0 0-1-1-1-2z" class="C"></path><path d="M647 487c2-5 4-10 5-16 1 3 1 4 0 6v1c1 2 1 3 1 5-1 1-1 3-3 4v2h-1l-2-2z" class="q"></path><path d="M574 670l1-1v-3c1-1 0-1 1-2h0v-3c0-1 1-2 1-3v-1c0-1 1-2 2-3 0 6 1 13-1 18-1-1-3-1-4-2z" class="S"></path><path d="M760 262c11-1 24 2 33 9-11-4-21-8-33-8h3v-1h-3z" class="M"></path><path d="M652 471h1c2 2 4 4 6 7-1 2-1 2-3 4 0 0 1 1 2 1v1h-2c-1 0-2-1-3-1 0-2 0-3-1-5v-1c1-2 1-3 0-6z" class="b"></path><path d="M839 185h3l-44 16v-1h0c2-1 3-2 4-4 1 0 7-2 8-3 5-2 11-4 16-5 4-1 9-2 13-3z" class="W"></path><defs><linearGradient id="AB" x1="672.96" y1="401.395" x2="669.767" y2="400.743" xlink:href="#B"><stop offset="0" stop-color="#222324"></stop><stop offset="1" stop-color="#3c3c3c"></stop></linearGradient></defs><path fill="url(#AB)" d="M676 383c1 1 1 2 1 3s0 2-1 3c-2 6-4 13-5 19 0 3 0 7-1 10v2c-1 0-2 1-2 1v1c0 1 0 1-1 2v-3c1-1 1-3 1-4v-11c1-7 5-14 7-21l1-2z"></path><path d="M674 384l1 1c-2 7-6 14-7 21v11c0 1 0 3-1 4 0-3 0-7-1-10-3 2-6 9-8 13v-4c1-2 1-3 2-5 0-1 1-2 1-3l4-8 9-20z" class="c"></path><path d="M656 420h0c1-2 2-3 4-5-1 2-1 3-2 5v4l-20 59-17 48c-2 7-5 13-7 20-1 7 0 15-1 22l-1-12c-5 8-7 17-11 25l-9 22c-1 4-2 9-4 13l-1-2c0-1 1-2 1-2v-2c1-1 1-2 1-4 1-1 1-2 1-3l-1-1c2-3 4-6 5-9 3-5 5-10 7-15l10-29 17-48 12-34c3-8 7-17 8-26l-1 1c-1-1-1-1-1-2v-3l-3 4-1-3c-1 1-2 3-3 4-1-1-1-1-1-2l3-4 15-21z" class="Y"></path><path d="M647 436c2-3 3-4 6-4l-7 10-3 4-1-3c2-2 4-4 5-7z" class="d"></path><path d="M656 420h0c1-2 2-3 4-5-1 2-1 3-2 5l-5 12c-3 0-4 1-6 4v-1c1-3 3-5 5-8l1-1c1-1 2-2 2-3 1-1 1-2 1-3z" class="E"></path><path d="M656 420c0 1 0 2-1 3 0 1-1 2-2 3l-1 1c-2 3-4 5-5 8v1c-1 3-3 5-5 7-1 1-2 3-3 4-1-1-1-1-1-2l3-4 15-21z" class="S"></path><path d="M798 200v1c-15 7-30 16-41 28-4 4-8 8-10 13 0 1 0 2-1 3 3 2 8 0 10 2-3 0-7 0-10 1v1c-4 2-8 7-9 12v4c1 1 2 1 3 2 7-1 13-5 20-5h3v1h-3c-10 2-23 5-30 14s-13 19-18 29c-12 21-21 42-30 64l-4 9c-1 1-2 3-2 4l-1 2-1-1 9-21 41-82c5-7 8-15 12-23l7-14 2-6c-2 0-3 1-5 2v-1l-4 2v-2h1l-1-2-1-1c2-1 3-3 5-4l2-2c-1-1-1 0-1-1h-1c1-1 3-2 4-3 2-3 7-5 9-9 2-1 5-2 7-4l-1 3c2-1 3-2 5-3 3-1 4-1 6-1s3-1 5 0c3-1 5-5 8-5 2-1 3-2 5-3l10-4z" class="N"></path><path d="M755 222c2-1 5-2 7-2-3 3-7 6-10 10l-2-1c1-2 2-2 1-4 1-1 3-2 4-3z" class="s"></path><path d="M770 212c2 0 3-1 5 0-1 1-3 2-4 2-3 2-6 5-9 6-2 0-5 1-7 2l3-2 2-2c3-2 7-5 10-6z" class="V"></path><path d="M751 225c1 2 0 2-1 4l2 1c-1 1-6 8-7 8-2 0-3 1-5 2v-1l-4 2v-2h1l-1-2-1-1c2-1 3-3 5-4v1c2-2 5-5 7-5 2-1 3-2 4-3z" class="D"></path><path d="M750 229l2 1c-1 1-6 8-7 8-2 0-3 1-5 2v-1h0l1-2c1-2 3-3 5-5l4-3z" class="V"></path><path d="M759 216c2-1 3-2 5-3 3-1 4-1 6-1-3 1-7 4-10 6l-2 2-3 2c-1 1-3 2-4 3s-2 2-4 3c-2 0-5 3-7 5v-1l2-2c-1-1-1 0-1-1h-1c1-1 3-2 4-3 2-3 7-5 9-9 2-1 5-2 7-4l-1 3z" class="r"></path><path d="M759 216c2-1 3-2 5-3 3-1 4-1 6-1-3 1-7 4-10 6l-2 2-6 2c1-2 6-5 7-6z" class="W"></path><defs><linearGradient id="AC" x1="766.325" y1="189.798" x2="800.692" y2="225.982" xlink:href="#B"><stop offset="0" stop-color="#090909"></stop><stop offset="1" stop-color="#4e4d4f"></stop></linearGradient></defs><path fill="url(#AC)" d="M828 181l13 1h0 7-1l-2 1h-2l-2 1h-2 0v1h0c-4 1-9 2-13 3-5 1-11 3-16 5-1 1-7 3-8 3-1 2-2 3-4 4h0l-10 4c-2 1-3 2-5 3-3 0-5 4-8 5-2-1-3 0-5 0s-3 0-6 1c-2 1-3 2-5 3l1-3c-2 2-5 3-7 4-2 4-7 6-9 9-1 1-3 2-4 3h1c0 1 0 0 1 1l-2 2c-2 1-3 3-5 4l1 1 1 2h-1v2l-2 2c-1-2-2-3-2-5-1 1-1 1-2 1 0-1 0-1 1-2l1-2h0c-2 1-3 2-4 4l-3 3c-1 1 0 0-1 0l-1-1c0-2 0-3-1-5 1-3 2-6 4-8 2-3 5-5 6-7h1c5-3 10-6 15-8 18-8 36-14 55-20 4-2 8-3 12-5 1-1 3-1 4-2h2 0v-1l2-1c1 0 3 0 4-1 1 0 1-1 1-2z"></path><path d="M760 213c1 1 8-3 10-4 10-5 21-9 32-13-1 2-2 3-4 4h0l-10 4c-2 1-3 2-5 3-3 0-5 4-8 5-2-1-3 0-5 0s-3 0-6 1c-2 1-3 2-5 3l1-3z" class="e"></path><path d="M725 242v-2c3-7 9-11 14-15 2-1 3-3 5-4 3-1 5-3 9-4-2 4-7 6-9 9-1 1-3 2-4 3h1c0 1 0 0 1 1l-2 2c-2 1-3 3-5 4l1 1 1 2h-1v2l-2 2c-1-2-2-3-2-5-1 1-1 1-2 1 0-1 0-1 1-2l1-2h0c-2 1-3 2-4 4l-3 3z" class="J"></path><path d="M732 238l1-1 6-6c0-2 0-2 1-2h1c0 1 0 0 1 1l-2 2c-2 1-3 3-5 4l1 1 1 2h-1v2l-2 2c-1-2-2-3-2-5z" class="L"></path><path d="M725 242l3-3c1-2 2-3 4-4h0l-1 2c-1 1-1 1-1 2 1 0 1 0 2-1 0 2 1 3 2 5l2-2 4-2v1c2-1 3-2 5-2l-2 6-7 14c-4 8-7 16-12 23l-41 82-1-1 2-3h-1-1v-4c2-6 4-13 5-19 0-1 1-5 1-6 1-6 0-12 2-18l3-8 7-24-1-2c0-2 1-6 2-7 1-2 1-2 3-3v2l6-7-2-1c2-3 5-5 8-8 1-2 4-5 6-7h1c0-2-1-4-1-6v-5c1 2 1 3 1 5l1 1c1 0 0 1 1 0z" class="G"></path><path d="M696 306h1v2c-1 1-2 3-3 5h0-1l-2 1c1-1 1-1 1-2 1-3 1-4 4-6z" class="d"></path><path d="M682 355c2-6 4-13 5-19 1 1 2 1 2 2-1 3-1 6-3 10 0 2-1 4-2 6v5h-1-1v-4z" class="B"></path><path d="M718 267l2-2c2-5 9-8 13-13 2-2 4-5 6-7 1-1 2-1 4-1l-7 14c-4 8-7 16-12 23h-1v-1c1 0 1 0 2-1 0-1 0-2 1-2l1-1v-2l1-1-2 2-1-1 2-2v-4l-1 2s0 1-1 2c0 1 0 2-1 2h0c1-2 1-3 1-4v-1c1-1 2-1 2-3h0c1-1 1-1 1-2l-1 1c0 1-1 1-1 2h-1c1-2 1-3 2-5h1v-1c-4 0-3 2-5 3l-5 3z" class="S"></path><defs><linearGradient id="AD" x1="712.386" y1="263.598" x2="722.691" y2="273.964" xlink:href="#B"><stop offset="0" stop-color="#101111"></stop><stop offset="1" stop-color="#3d3e3f"></stop></linearGradient></defs><path fill="url(#AD)" d="M725 242l3-3c1-2 2-3 4-4h0l-1 2c-1 1-1 1-1 2 1 0 1 0 2-1 0 2 1 3 2 5l2-2 4-2v1c2-1 3-2 5-2l-2 6c-2 0-3 0-4 1-2 2-4 5-6 7-4 5-11 8-13 13l-2 2c-2 3-5 5-7 8v1c-2 2-2 5-3 8 0 1-1 3-2 4s-1 1-1 2-1 2-2 2l-1 1c-1 0-2 1-2 2h0v3c-1 1-1 1-1 2l-4 4h-1-1l7-24-1-2c0-2 1-6 2-7 1-2 1-2 3-3v2l6-7-2-1c2-3 5-5 8-8 1-2 4-5 6-7h1c0-2-1-4-1-6v-5c1 2 1 3 1 5l1 1c1 0 0 1 1 0z"></path><path d="M736 241l4-2v1c-8 5-14 10-21 16-4 4-9 8-12 13-3 3-5 7-7 11l-1-2c0-2 1-6 2-7 1-2 1-2 3-3v2l6-7-2-1c2-3 5-5 8-8v1h2l16-12 2-2z" class="M"></path><path d="M708 262c2-3 5-5 8-8v1h2c-2 3-6 5-8 8l-2-1z" class="B"></path><path d="M725 242l3-3c1-2 2-3 4-4h0l-1 2c-1 1-1 1-1 2 1 0 1 0 2-1 0 2 1 3 2 5l-16 12h-2v-1c1-2 4-5 6-7h1c0-2-1-4-1-6v-5c1 2 1 3 1 5l1 1c1 0 0 1 1 0z" class="G"></path><path d="M689 311l1 1c-2 6-1 12-2 18 0 1-1 5-1 6-1 6-3 13-5 19v4h1 1l-2 3 1 1-9 21-9 20-4 8c0 1-1 2-1 3-2 2-3 3-4 5h0l-15 21c-3 1-6 3-8 6v-1l-1-1c-2 1-3 3-4 5-1-1-1-3-1-5h0c3-7 4-14 7-21 1-5 3-10 4-15l8-25h1l6-6 2-2c1-2 3-3 4-4l2-3 1-1 5-7c2-3 4-7 5-10l1-2 2-3h0c1-2 1-3 2-4l3-7c1-1 1-1 1-2s1-1 1-2c1-2 2-5 2-7 0-5 3-9 5-13z" class="n"></path><path d="M659 388v1c-4 7-11 12-16 17h-1v-1c4-6 11-11 17-17z" class="C"></path><path d="M634 424l1 1 3-3s1-1 2-1l2-2 1 1-1 1h0c0 1 0 1-1 2l-1 1c-1 2-3 4-4 5 3 0 4-1 5-2h1c-3 3-6 6-9 8-3 3-2 8-6 10 3-7 4-14 7-21z" class="I"></path><defs><linearGradient id="AE" x1="680.891" y1="373.376" x2="675.257" y2="366.01" xlink:href="#B"><stop offset="0" stop-color="#343c3f"></stop><stop offset="1" stop-color="#585758"></stop></linearGradient></defs><path fill="url(#AE)" d="M670 384h0c6-9 8-19 12-29v4h1 1l-2 3 1 1-9 21-9 20c-1 0-1-1-1-1l4-9v1h0c1-1 1-1 1-2l3-5v-1l1-2c1-2 2-4 2-5l1-1-1-1c-1 1-2 3-2 4-1 2-2 3-3 4 0 1-1 1-1 1l-1-1 2-2z"></path><path d="M689 311l1 1c-2 6-1 12-2 18 0 1-1 5-1 6-1 6-3 13-5 19-4 10-6 20-12 29h0c4-10 8-19 11-29s6-20 6-31c-2 2-3 5-4 7h-1c1-2 2-5 2-7 0-5 3-9 5-13z" class="c"></path><path d="M641 441l-3 4c0 1 0 1 1 2 1-1 2-3 3-4l1 3 3-4v3c0 1 0 1 1 2l1-1c-1 9-5 18-8 26l-12 34-17 48-10 29c-2 5-4 10-7 15-1 3-3 6-5 9l-5 6-5 4c-2 1-5 3-7 5-2 1-4 2-6 4-1 1-2 0-3 0h-1l3-5c5-12 9-24 13-36 2-5 3-10 4-15 2-3 3-7 4-10 3-4 5-10 6-15l12-34 6-21h1l11-15-1-2c0-1 1-2 1-4l3-5-2 1-2-1v-1c1 0 0 0 1-1l1-3v-1s0-1-1-1c3-2 4-5 6-7 1-2 2-4 4-5l1 1v1c2-3 5-5 8-6z" class="n"></path><path d="M643 446l3-4v3c0 1 0 1 1 2l-7 6v-1-3-1c1 0 2-1 3-2z" class="N"></path><path d="M627 462l13-13v3 1l-18 22-1-2c0-1 1-2 1-4l3-5 2-2z" class="m"></path><path d="M565 621l1 1c3-1 5-3 7-5 4-4 9-7 13-12 3-2 5-5 7-7h1c-1 3-3 6-5 9l-5 6-5 4c-2 1-5 3-7 5-2 1-4 2-6 4-1 1-2 0-3 0h-1l3-5z" class="G"></path><path d="M641 441l-3 4c0 1 0 1 1 2 1-1 2-3 3-4l1 3c-1 1-2 2-3 2v1l-13 13-2 2-2 1-2-1v-1c1 0 0 0 1-1l1-3v-1s0-1-1-1c3-2 4-5 6-7 1-2 2-4 4-5l1 1v1c2-3 5-5 8-6z" class="S"></path><path d="M639 447c1-1 2-3 3-4l1 3c-1 1-2 2-3 2h-1l-3 3h-1l4-4z" class="J"></path><path d="M623 459c1 0 1 1 2 1h1v1l1 1-2 2-2 1-2-1v-1c1 0 0 0 1-1l1-3z" class="i"></path><path d="M628 450c1-2 2-4 4-5l1 1v1c0 1 0 1-1 2 0 1-1 2-1 3-2 2-3 3-6 4 0 1-1 2-2 2 0 0 0-1-1-1 3-2 4-5 6-7z" class="h"></path><path d="M641 441l-3 4c0 1 0 1 1 2l-4 4c-1 1-2 2-3 4l-3 3v-1l3-4c-1-1 0-1-1-1 0-1 1-2 1-3 1-1 1-1 1-2 2-3 5-5 8-6z" class="I"></path><defs><linearGradient id="AF" x1="736.784" y1="177.501" x2="735.663" y2="195.253" xlink:href="#B"><stop offset="0" stop-color="#747373"></stop><stop offset="1" stop-color="#88898a"></stop></linearGradient></defs><path fill="url(#AF)" d="M651 181h167 43c9 0 19-1 27 0-3 1-7 1-11 0l-18 1c-5 1-12 3-17 3h-3 0v-1h0 2l2-1h2l2-1h1-7 0l-13-1c0 1 0 2-1 2-1 1-3 1-4 1l-2 1v1h0-2c-1 1-3 1-4 2-4 2-8 3-12 5-19 6-37 12-55 20-5 2-10 5-15 8h-1c-1 2-4 4-6 7-2 2-3 5-4 8v5c0 2 1 4 1 6h-1c-2 2-5 5-6 7-3 3-6 5-8 8-1 1-2 1-3 2-1 0-1 0-2-1-1-2-1-4-1-6h0v4l-1 1 1 1h0c0 1 0 1 1 2v1h0c-1 1 0 1-1 1s-2 1-2 1v-4c0-2 0-3-1-4 1-11 0-22-5-32-2-3-4-5-6-8-1-1-1-2-2-3v-1-1l-2-4c1-1 1-2 2-4-1-3-6-6-8-8-2-1-5-2-7-4l-1-1h-1l-1-1c-1 0-2-1-2-1-1 0-2-1-3-1v-1h0c-1-1-2-1-3-2-1 0-1 0-2-1h-1c-1-1-3-2-4-2h-1c-1-1-2-1-4-1-1-1-4-1-5-2h8v-1z"></path><path d="M691 191c2 0 4 0 6 1-1 1-2 2-4 2v-2l-2-1z" class="R"></path><path d="M681 191h0 0c1-1 3-2 3-1 0 0 1 1 2 1h5l2 1v2l-4-1h-2c-2 0-4 0-6-2z" class="B"></path><path d="M841 182h18c-5 1-12 3-17 3h-3 0v-1h0 2l2-1h2l2-1h1-7 0z" class="J"></path><path d="M704 191c-1 0-1 0-3-1h-3v-1l1-1 3-3c-1 0-1 0-1-1h6 1 2 0 4s1-1 2-1c0 0 0 2 1 2l5 1-2 1c-3 1-3 2-6 2h0-4l-4 2h-2z" class="m"></path><path d="M710 189c-2 0-4 1-5 2l-1-1c-1 0-1-1-1-2 1 0 1 0 2-1 1 0 2 0 3-1l1-1 1 2c2-1 6-1 8 0-2 0-2 1-4 2h0-4z" class="e"></path><path d="M811 182l17-1c0 1 0 2-1 2-1 1-3 1-4 1l-2 1c-2 1-5 2-8 3-2 0-5 1-7 2h-4c-3 1-6 1-9 2v-1l-19 6v-1l-8 2h-3l-1 1h-3c1-1 2-1 2-1 5-1 8-3 12-5 4-1 12-4 15-7h0c1 0 2 0 2-1h1l1 1c0-1 1-2 2-2v1h1l1-1v1 1h3l1-1c0-1 1-1 1-1h2l4-1 1-1h0-13c5-1 11 0 16 0z" class="B"></path><path d="M807 186c1 1 1 1 2 0 1 0 3 1 4 2-2 0-5 1-7 2h-4c-3 1-6 1-9 2v-1l7-2c2-1 5-2 7-3z" class="S"></path><path d="M811 182l17-1c0 1 0 2-1 2-1 1-3 1-4 1l-2 1c-2 1-5 2-8 3-1-1-3-2-4-2-1 1-1 1-2 0l-1-1-8 3-3 2h-3c-2 1-5 2-6 2 1-2 3-2 5-3 5-1 9-3 13-4l6-3h1z" class="E"></path><path d="M807 186l2-1h1l1-1h5c1 1 5 0 7 0h0l-2 1c-2 1-5 2-8 3-1-1-3-2-4-2-1 1-1 1-2 0z" class="I"></path><path d="M714 189c3 0 3-1 6-2l2-1c1 1 3 1 3 2l-2 2c-2 1-2 2-3 2v1h0l-5 5c-1 3-3 5-4 7 0 1 0 1-1 2h-2-1-1l-1 1-1 1h-3c-1 0-1 0-2-1 2-1 3-1 5-2v-1l1 1 1-1-1-1c-2-1-4-2-6-2-1-1-3-2-4-3h0-2-1-1l-1-3 2 2c1-1 1-1 1 0h2v-2h0c-2-1-3 0-5-1l-1-2 4 1c2 0 3-1 4-2l3 1h0l2 2c0-2 1-3 2-4h2l4-2h4 0z" class="s"></path><path d="M710 189h4c0 1 0 1-1 2h-7l4-2zm-13 3l3 1h0l2 2v1l-2 1c1 1 2 1 3 1h1c2 0 4 0 6-1 0 2-1 2-2 4l-1 1c-1-1-1-2-2-2s-1 0-2 1l-1-1c-1-1-1-1-2-1l-4-1h-1v-2h0c-2-1-3 0-5-1l-1-2 4 1c2 0 3-1 4-2z" class="V"></path><path d="M700 197c-1-1-2-1-3-2 1-1 2-1 3-2l2 2v1l-2 1z" class="Z"></path><path d="M704 191h2l1 1h2v1h1c2 0 3 0 6-2v1l1 1c-1 1-2 2-4 2l-3 2c-2 1-4 1-6 1h-1c-1 0-2 0-3-1l2-1v-1c0-2 1-3 2-4z" class="D"></path><path d="M704 191h2l1 1h2v1h-3v1 1c-1 1-3 0-3 2l1 1h-1c-1 0-2 0-3-1l2-1v-1c0-2 1-3 2-4z" class="R"></path><path d="M651 182h14c2 0 5-1 7 0h-7c1 2 2 3 3 4v1-1-1c1 0 2 0 3 1h1s1 0 2 1h-1c-1-1-2-1-4-1 1 1 2 2 3 2l4 2c2 1 3 2 5 2v-1c2 2 4 2 6 2h2l1 2c2 1 3 0 5 1h0v2h-2c0-1 0-1-1 0l-2-2 1 3h1 1 2 0c1 1 3 2 4 3 2 0 4 1 6 2l1 1-1 1-1-1v1c-2 1-3 1-5 2h-2l-2-2h0c-1 1 0 1 0 2v3c-1-1-3-1-4-2s-3-2-4-3-1-2-2-4c-1 0-2-1-2-2-3-2-7-3-9-5-2 0-2 0-4-1h-1l-1-1c-1 0-2-1-2-1-1 0-2-1-3-1v-1h0c-1-1-2-1-3-2-1 0-1 0-2-1h-1c-1-1-3-2-4-2h-1c-1-1-2-1-4-1-1-1-4-1-5-2h8z" class="G"></path><path d="M686 201h0c-1-1-2-1-3-3h3l2 1-2 2h0z" class="F"></path><path d="M688 199l1 1 2 1c-1 2-2 1-4 1h0l-1-1 2-2z" class="E"></path><path d="M691 201l1 1c1 1 1 2 1 3-1 0-2 0-3-1h-3v-2h0c2 0 3 1 4-1z" class="J"></path><path d="M668 187v-1-1c1 0 2 0 3 1h1s1 0 2 1h-1c-1-1-2-1-4-1 1 1 2 2 3 2l4 2c2 1 3 2 5 2v-1c2 2 4 2 6 2h2l1 2c2 1 3 0 5 1h0v2h-2c0-1 0-1-1 0l-2-2 1 3h1 1 2 0c1 1 3 2 4 3-3 0-3 0-5-1-1-1-4-1-5-1l-1-1-2-1h-1v-1l1-1-4-1c-1 0-2 0-3-1-2-2-5-2-7-4-2-1-3-2-4-3z" class="L"></path><path d="M681 191c2 2 4 2 6 2h2l1 2c2 1 3 0 5 1h0v2h-2c0-1 0-1-1 0l-2-2c0-1-1-1-1-1l-1 1c-2-1-3-2-4-2-2 0-2-1-3-2v-1z" class="R"></path><path d="M732 191v-2c1-1 1-2 2-2s2-1 3-2 2 0 3 0l1-1h3c1-1 5-1 7 0h3 1c1 1 10 1 12 0h2 1 1c2 0 3 0 5 1 1 1 4 1 6 1h0l-1 1c-1 1-2 2-4 2-1 1-2 2-3 2l-3 1-4 1-12 4c-2 1-4 1-6 2h-4v1h-1c0 1 0 1-1 2v-1h-2l-1 1h-3l1-1h-3-6c-1-1-1-1-1-3l1-1v-1-1-1c1-2 1-2 3-3z" class="m"></path><path d="M732 191v-2c1-1 1-2 2-2s2-1 3-2 2 0 3 0l1-1h3c1-1 5-1 7 0h3 1c1 1 10 1 12 0h2 1 1c2 0 3 0 5 1h-6c-2 1-5 0-7 0h-3c-2 0-3 1-5 1h-1c-1 0-2 0-4-1-1-1-4 0-6 0-2 1-4 1-6 1l-1 1c-1 1-2 1-4 2h0v3c-1 1-2 2-4 3v-1c1-2 1-2 3-3z" class="V"></path><path d="M821 185v1h0-2c-1 1-3 1-4 2-4 2-8 3-12 5-19 6-37 12-55 20-5 2-10 5-15 8h-1c-1 2-4 4-6 7-2 2-3 5-4 8v5c0 2 1 4 1 6h-1c-2 2-5 5-6 7-3 3-6 5-8 8-1 1-2 1-3 2-1 0-1 0-2-1-1-2-1-4-1-6h0v4l-1 1 1 1h0c0 1 0 1 1 2v1h0c-1 1 0 1-1 1s-2 1-2 1v-4c0-2 0-3-1-4 1-11 0-22-5-32-2-3-4-5-6-8-1-1-1-2-2-3v-1-1l-2-4c1-1 1-2 2-4-1-3-6-6-8-8-2-1-5-2-7-4l-1-1c2 1 2 1 4 1 2 2 6 3 9 5 0 1 1 2 2 2 1 2 1 3 2 4s3 2 4 3 3 1 4 2v-3c0-1-1-1 0-2h0l2 2h2c1 1 1 1 2 1h3l1-1 1-1h1 1 2c1-1 1-1 1-2 1-2 3-4 4-7l5-5c1 0 1 0 2-1l1-1 3-3c1-1 2-1 3-2l2-1v1h0v3l1 2c-2 1-2 1-3 3v1 1 1l-1 1c0 2 0 2 1 3h6 3l-1 1h3l1-1h2v1c1-1 1-1 1-2h1v-1h4c2-1 4-1 6-2l12-4 4-1 3-1-1 2c-4 2-7 4-12 5 0 0-1 0-2 1h3l1-1h3l8-2v1l19-6v1c3-1 6-1 9-2h4c2-1 5-2 7-2 3-1 6-2 8-3z" class="c"></path><path d="M793 192c3-1 6-1 9-2h4l-18 6c-3 1-7 3-10 3 3-4 10-5 15-7z" class="G"></path><path d="M750 207c5-1 9-5 15-4l-37 15-1-1h-3 0c0-1 1-3 2-3h5c2-1 5-2 6-4-1 0-2 0-2-1h-1-1v-1-1c3 0 6-1 9-2v1h-1c2 1 3 1 4 1 2-1 4-1 5 0z" class="L"></path><path d="M733 207c3 0 6-1 9-2v1h-1c2 1 3 1 4 1 2-1 4-1 5 0-2 0-5 1-8 1l-1-1c-1 1-3 2-4 2-2 0-1-1-3 0h-1v-1-1z" class="d"></path><defs><linearGradient id="AG" x1="688.715" y1="218.233" x2="704.823" y2="209.876" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#4c4b4b"></stop></linearGradient></defs><path fill="url(#AG)" d="M686 207h1c0 1 1 1 2 2 4 4 14 6 20 7l15 1h0 3l1 1c-8 3-14 6-22 7h-2c-1 0-2 1-3 0h0c-6-1-11-6-15-10l-2-4c1-1 1-2 2-4z"></path><path d="M702 222c-2-1-5-2-7-4-2-1-4-2-5-3v-1c5 3 11 3 16 6 1 0 2 0 2-1h1 4 0c0 1-1 1-2 1 0 1-1 1-2 2h0-4-3z" class="B"></path><defs><linearGradient id="AH" x1="707.54" y1="226.931" x2="722.75" y2="213.75" xlink:href="#B"><stop offset="0" stop-color="#414142"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#AH)" d="M709 216l15 1h0 3l1 1c-8 3-14 6-22 7h-1 0c-2-1-2-2-3-3h3 4 0c1-1 2-1 2-2 1 0 2 0 2-1h0-4l-1-1c1 0 1-1 1-2z"></path><path d="M729 196v1l-1 1c0 2 0 2 1 3h6 3l-1 1h3l1-1h2v1c1-1 1-1 1-2h1v-1h4c2-1 4-1 6-2l12-4 4-1 3-1-1 2c-4 2-7 4-12 5 0 0-1 0-2 1h3l1-1h3l8-2v1l19-6v1c-5 2-12 3-15 7-4 1-9 4-13 4-6-1-10 3-15 4-1-1-3-1-5 0-1 0-2 0-4-1h1v-1c-3 1-6 2-9 2h0-5 0c1-1 2-1 3-1 3 0 6-1 8-2h1 2s1-1 1-2c-1 0-4 1-5 1-3 1-10 2-13 1h0-1v-2-1c1-3 3-3 5-5z" class="Z"></path><path d="M725 204h1c2-1 3-1 5-1v-1c3-1 5 0 7 1-3 1-10 2-13 1h0z" class="R"></path><path d="M793 191v1c-5 2-12 3-15 7-4 1-9 4-13 4-6-1-10 3-15 4-1-1-3-1-5 0-1 0-2 0-4-1h1v-1l32-8 19-6z" class="C"></path><path d="M726 188c1-1 2-1 3-2l2-1v1h0v3l1 2c-2 1-2 1-3 3v1 1c-2 2-4 2-5 5v1 2h1 0c3 1 10 0 13-1 1 0 4-1 5-1 0 1-1 2-1 2h-2-1c-2 1-5 2-8 2-1 0-2 0-3 1h0 5 0v1 1h1 1c0 1 1 1 2 1-1 2-4 3-6 4h-5c-1 0-2 2-2 3l-15-1c-6-1-16-3-20-7-1-1-2-1-2-2h-1c-1-3-6-6-8-8-2-1-5-2-7-4l-1-1c2 1 2 1 4 1 2 2 6 3 9 5 0 1 1 2 2 2 1 2 1 3 2 4s3 2 4 3 3 1 4 2v-3c0-1-1-1 0-2h0l2 2h2c1 1 1 1 2 1h3l1-1 1-1h1 1 2c1-1 1-1 1-2 1-2 3-4 4-7l5-5c1 0 1 0 2-1l1-1 3-3z" class="D"></path><path d="M724 194h1c-1 1-2 3-3 4 0 1 0 1-1 2l-2-1v-3l5-2z" class="L"></path><path d="M726 188c1-1 2-1 3-2l2-1v1h0v3l1 2c-2 1-2 1-3 3v1 1c-2 2-4 2-5 5v1 2c-1-1-2-1-3-2h0 2c0-4 3-6 4-9v-1h2v-2c-1-1-1-1-3-2z" class="s"></path><path d="M715 198l2 4c1 1 2 2 3 2l1 1c1 1 2 2 4 2h1v1l-5-1v1c1 0 2 0 2 1h-1c-1-1-2-1-2 0-2-1-5-3-7-2-1 0-2 0-2-2 1-2 3-4 4-7z" class="E"></path><path d="M670 194c2 1 2 1 4 1 2 2 6 3 9 5 0 1 1 2 2 2 1 2 1 3 2 4s3 2 4 3 3 1 4 2h2c1 1 2 1 3 1l3 1h5l1 1h2 5l2 1c2 0 3 0 5-2h-1c-1 1-2 1-2 0h-1l2-1c1-1 1-1 2-1 1-1 4 0 5 0 0-1 0-1 1-1 0-1 1-1 2-1v1l-1 1h1c1-1 2-1 2-2h1 1c0 1 1 1 2 1-1 2-4 3-6 4h-5c-1 0-2 2-2 3l-15-1c-6-1-16-3-20-7-1-1-2-1-2-2h-1c-1-3-6-6-8-8-2-1-5-2-7-4l-1-1z" class="Z"></path><path d="M730 221h3-1c-1 2-4 4-6 7-2 2-3 5-4 8v5c0 2 1 4 1 6h-1c-2 2-5 5-6 7-3 3-6 5-8 8-1 1-2 1-3 2-1 0-1 0-2-1-1-2-1-4-1-6 1-8 1-16 5-22 5-9 14-11 23-14z" class="n"></path><path d="M730 221h3-1c-1 2-4 4-6 7-2 2-3 5-4 8v5c0 2 1 4 1 6h-1v-1c-1-4-2-6-5-8 2-4 6-9 9-12 2-1 3-3 4-4v-1z" class="W"></path><path d="M664 475l9 12c1 0 3 2 3 2 2 2 5 3 8 4l2 4-1 1c-2-1-2-1-4-1v1c2 1 2 0 4 0h2c3-2 4-1 7-2l1-1 1-1h2c0 1 0 0-1 1-2 1-3 3-5 4l-3 6 3-2 1 1v4c-1 2-2 4-2 6 1 1 2 2 3 2s2-1 4-2c1 5 2 9 3 13h2c-2 8-3 16-7 24-2 1-6 4-8 4h-1v-1l-1 1c-1 3-1 7 0 9v1l-2-1c-1-2-1-4-2-6v2h-1c-1-1-1-1-1 0 1 2 3 6 3 9 0 2-1 5-1 8-1 1-1 1-1 3l-8 23-2 4-2 5c0 2-2 4-2 7-1 0-1 1-1 2-1 1-2 2-2 3l-1 2c-1 3-5 8-5 11-1 2-2 4-3 5-9 13-18 24-29 35-7 5-14 10-22 14-3 2-6 3-9 5l-5 1c-4 2-9 4-13 4-2 1-3 1-5 1l2-4 3-6v-1c3-10 6-19 9-28l14-42 28-81 19-53 2 2h1v-2c2-1 2-3 3-4 1 0 2 1 3 1h2v-1c-1 0-2-1-2-1 2-2 2-2 3-4l2 4 1 1 2 1h1c-1-1-1-1-1-2v-2c0-1-1-2-1-3s0-1 1-2z" class="a"></path><path fill="#e7a7a5" d="M651 496h1c0 2 0 2 1 4l3 3v1h-2l-1-2h0l-3 1c-1-1-2-1-3-2v-1l4-4z"></path><path d="M659 478l2 4 1 1 2 1c0 2 0 2 1 4 1 1 2 3 3 4h0v1l-5-5-1 1-1 1v2c1 2 1 3 1 6 0 5 6 11 3 17l-4-10c-3-6-7-11-12-16h1v-2c2-1 2-3 3-4 1 0 2 1 3 1h2v-1c-1 0-2-1-2-1 2-2 2-2 3-4z" class="k"></path><path d="M662 483l2 1c0 2 0 2 1 4 1 1 2 3 3 4h0v1l-5-5-1 1-1 1v2c-1 0-1 0-1-1s0-2-1-2v-1c1-2 3-3 3-5z" class="o"></path><path d="M653 483c1 0 2 1 3 1l2 2v1 2 1c-1 0-1 0-1-1-1 0-1 1-1 2s1 1 1 2c2 3 5 8 5 11l-1 1c-3-6-7-11-12-16h1v-2c2-1 2-3 3-4z" class="Q"></path><path d="M661 492v-2l1-1-1 1c1 1 1 1 2 3l1 1v1c0 2 2 4 3 5 1 3 2 5 3 7 1 3 2 5 1 8h1c2 2 2 5 5 8 0 2 0 3 2 5h1l3 5c1 1 1 3 1 4l1 5-3 6h-1l-1-5-1-1c0-1 0-3-1-4h-2c0-1-1-2-1-3v-2l1-2h0 1c0-2-2-3-2-4s-1-3-1-4h-1v1h-1 0v3h-1c0 1-1 2-1 2 0 1 1 3 1 4l1 7v1c0 2 0 4 1 7 0 2-1 3 0 5v1c-1 4 0 8-1 13v6c-1 5-2 13-4 18h-1l4-21c0-3 0-6 1-8v-4c-1 0-3-3-4-4h1c1 1 1 1 3 1v-6c-1-12-3-23-7-34 3-6-3-12-3-17 0-3 0-4-1-6z" class="g"></path><path d="M678 536l-1-3v-1l1-1c3 1 4 4 5 6h0v1c-1 1-1 1-2 1h0c0-1-1-2-2-3v-1l-1 1z" class="H"></path><path d="M678 536l1-1v1c1 1 2 2 2 3h0c1 0 1 0 2-1v-1h1l1 5-3 6h-1l-1-5c1 0 1-1 1-1l-3-6z" class="o"></path><path d="M664 475l9 12c1 0 3 2 3 2h-1l-2-1c-1 1-1 1-1 2v1l1 1c1 2 2 3 3 5 0 3 0 6 2 9 0 1 1 3 0 4 0 0 0-1-1-1 0-1 0-1-1-2l4 12 2 5v4l1 1v2 2l-3-5h-1c-2-2-2-3-2-5-3-3-3-6-5-8h-1c1-3 0-5-1-8-1-2-2-4-3-7-1-1-3-3-3-5v-1l-1-1c-1-2-1-2-2-3l1-1 1-1 5 5v-1h0c-1-1-2-3-3-4-1-2-1-2-1-4h1c-1-1-1-1-1-2v-2c0-1-1-2-1-3s0-1 1-2z" class="O"></path><path d="M680 528c0-1-1-1-1-2l-2-2 2-2 2 1 1 1v4l1 1v2 2l-3-5z" class="P"></path><path d="M681 523c-2-4-5-8-6-12l-3-10v-1c1 1 1 2 1 3 1 1 2 3 2 4h1l4 12 2 5-1-1z" class="H"></path><path d="M664 475l9 12c1 0 3 2 3 2h-1l-2-1c-1 1-1 1-1 2v1l1 1c1 2 2 3 3 5 0 3 0 6 2 9 0 1 1 3 0 4 0 0 0-1-1-1 0-1 0-1-1-2h-1c0-1-1-3-2-4 0-1 0-2-1-3h1l-1-3h-1l-2-4v-1h-1c-1-1-2-3-3-4-1-2-1-2-1-4h1c-1-1-1-1-1-2v-2c0-1-1-2-1-3s0-1 1-2z" class="g"></path><path d="M673 492l-1-1v-1c0-1 0-1 1-2l2 1h1c2 2 5 3 8 4l2 4-1 1c-2-1-2-1-4-1v1c2 1 2 0 4 0h2c3-2 4-1 7-2l1-1 1-1h2c0 1 0 0-1 1-2 1-3 3-5 4l-3 6c-4 12-4 24-3 36 0 4 0 8 1 12v1l-1 1c-1 3-1 7 0 9v1l-2-1c-1-2-1-4-2-6v2h-1c-1-1-1-1-1 0l-1-1c1-2 1-3 1-6-1-2 0-4-1-6v-5l1 1 1 5h1l3-6-1-5c0-1 0-3-1-4v-2-2l-1-1v-4l-2-5-4-12c1 1 1 1 1 2 1 0 1 1 1 1 1-1 0-3 0-4-2-3-2-6-2-9-1-2-2-3-3-5z" class="X"></path><path d="M685 542c0 5-1 8-1 13l-1 2c-1-2-2-7-2-9h1l3-6z" class="g"></path><path d="M673 492l-1-1v-1c0-1 0-1 1-2l2 1h1c2 2 5 3 8 4l2 4-1 1c-2-1-2-1-4-1v1c2 1 2 0 4 0h2c3-2 4-1 7-2l1-1 1-1h2c0 1 0 0-1 1-2 1-3 3-5 4-1 0-1 0-2 1l-1 1-1 2c-1 1-1 2-1 3-1 1-1 1-1 2s-1 1-1 2c-1 1-1 3-1 4v2l-1 1c-1-1-1-2-1-3 0 0 0-1 1-1-1-2-3-4-3-6v-1c0-3-3-7-4-9s-2-3-3-5z" class="t"></path><path d="M673 492c3 0 5 4 8 5v1h-2c0 2 1 4 1 5l1 3c1 2 2 4 2 7h0c-1-2-3-4-3-6v-1c0-3-3-7-4-9s-2-3-3-5z" class="u"></path><path d="M673 492l-1-1v-1c0-1 0-1 1-2l2 1h1c2 2 5 3 8 4l2 4-1 1c-2-1-2-1-4-1-3-1-5-5-8-5z" class="T"></path><path d="M692 503l1 1v4c-1 2-2 4-2 6 1 1 2 2 3 2s2-1 4-2c1 5 2 9 3 13h2c-2 8-3 16-7 24-2 1-6 4-8 4h-1v-1-1c-1-4-1-8-1-12-1-12-1-24 3-36l3-2z" class="q"></path><path d="M691 535c1 2 2 5 3 7 1-1 2-2 2-3 1-3 3-5 4-7 0-2 0-3 1-4v-1h2c-2 8-3 16-7 24-2 1-6 4-8 4h-1v-1-1c3 0 3-1 6-1l1-1s0-1 1-2l-2-2c0-1-1-2 0-3 0-2-1-4-1-6h0c-1-3-1 0-1-2v-1z" class="Q"></path><path d="M691 514c1 1 2 2 3 2s2-1 4-2c1 5 2 9 3 13v1c-1 1-1 2-1 4-1 2-3 4-4 7 0 1-1 2-2 3-1-2-2-5-3-7-1-5-1-11 0-16v-5z" class="K"></path><path d="M694 522h2v2c1 2 1 5 0 7 0 1-1 2-1 2v1l-2-2c-1-1-1-1 0-2v-1c1-1 1-2 0-4h0l1-3z" class="H"></path><path d="M667 591h1c2-5 3-13 4-18v-6c1-5 0-9 1-13v-1c-1-2 0-3 0-5-1-3-1-5-1-7v-1l-1-7c0-1-1-3-1-4 0 0 1-1 1-2h1v-3h0 1v-1h1c0 1 1 3 1 4s2 2 2 4h-1 0l-1 2v2c0 1 1 2 1 3h2c1 1 1 3 1 4v5c1 2 0 4 1 6 0 3 0 4-1 6l1 1c1 2 3 6 3 9 0 2-1 5-1 8-1 1-1 1-1 3l-8 23-2 4-2 5c0 2-2 4-2 7-1 0-1 1-1 2-1 1-2 2-2 3l-1 2c-1 3-5 8-5 11-1 2-2 4-3 5-9 13-18 24-29 35-7 5-14 10-22 14-3 2-6 3-9 5l-5 1c-4 2-9 4-13 4-2 1-3 1-5 1l2-4 3-6v-1l6-1c6-1 11-3 16-6 29-13 50-44 61-73 3-6 4-13 7-19v-1z" class="P"></path><path d="M679 566l2 3h2c0 2-1 5-1 8-1-1 0-3-1-3 0-1-1-1-1-1 0-2-1-5-1-7z" class="T"></path><path d="M679 559l1 1c1 2 3 6 3 9h-2l-2-3h0c-1-1-2-2-2-4 0-1 1-2 2-3z" class="k"></path><path d="M590 692l1-1s1 0 2-1h1 1l2-1c1 0 3 0 4-1l1-1c0 1 0 1 1 2h2l2-2v1l-3 2v1c-3 2-6 3-9 5l-5 1v-1c1 0 2-1 3-1 1-1 2-1 3-2l-1-1c-1-1-3-1-5 0z" class="o"></path><path d="M577 691l6-1c0 1 1 2 2 2h5c2-1 4-1 5 0l1 1c-1 1-2 1-3 2-1 0-2 1-3 1v1c-4 2-9 4-13 4-2 1-3 1-5 1l2-4 3-6v-1z" class="K"></path><path d="M574 698l3-6c2 2 2 3 5 3 0 1 0 1-1 2h3c-2 1-5 2-7 3h-1l1 1c-2 1-3 1-5 1l2-4z" class="b"></path></svg>