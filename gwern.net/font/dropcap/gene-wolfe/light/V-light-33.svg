<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="30 36 970 976"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#211f1d}.C{fill:#030303}.D{fill:#181816}.E{fill:#0c0c0c}.F{fill:#e1e0db}.G{fill:#f4f3f2}.H{fill:#32312e}.I{fill:#bdb7a7}.J{fill:#11100f}.K{fill:#a9a28f}.L{fill:#a8a08f}.M{fill:#cbc6b9}.N{fill:#c7c5bd}.O{fill:#eae9e6}.P{fill:#464441}.Q{fill:#fcfbfb}.R{fill:#938d7f}.S{fill:#b6b2ac}.T{fill:#d4d1cb}.U{fill:#a19e98}.V{fill:#fff}.W{fill:#7c7973}.X{fill:#b2a999}.Y{fill:#d7d5cd}.Z{fill:#c2c1bf}.a{fill:#6d685a}.b{fill:#64635f}.c{fill:#5d594d}</style><path d="M947 639l2-1-1 5h0c-1-1-1-2-1-4zM303 781l2 1-3 2-1-2 1-1h1z" class="c"></path><path d="M836 504h7l-4 1v2h-3v-3z" class="B"></path><path d="M933 429c1 0 1 1 1 1 1 6 1 10-1 15h0c0-4 1-8 1-13l-1-3z" class="G"></path><path d="M949 628v10l-2 1v-2-2l1-1 1-6zM299 938h0 5l1 2h-8 1l1-1v-1z" class="P"></path><path d="M888 665c2 0 5-1 6 1h1l-2 2-3-1-2-2z" class="J"></path><path d="M294 936l5 2v1l-1 1h-1c-2 0-3-1-4-1l1-3z" class="H"></path><path d="M300 777l1-1c1 2 0 4 1 5h0l-1 1-3 1 2-6z" class="L"></path><path d="M927 669l2-2c2-1 2-1 3 0 0 1 0 1-1 2s-2 1-3 2c0-1 0-2-1-2z" class="a"></path><path d="M914 342c1 0 2 0 3 1s1 5 1 7c-1 1-1 1-2 1v-1c1-3 0-6-2-8z" class="M"></path><path d="M378 925h0c1 1 0 1 1 2 0-1 0-2 1-3v2c0 3 0 10-2 13v-7-7z" class="C"></path><path d="M834 329c2-1 5-1 6 0l1 1c-2 1-4 1-5 1l-1-1c-1 0-2 0-3-1h0 2z" class="P"></path><path d="M951 612l2 1 1 8c-1 1-1 1-2 1l-1-10z" class="c"></path><path d="M926 513h2c1 1 2 2 2 3l-1 2h-1c-2 0-2 0-3-1v-2l1-2z" class="H"></path><path d="M403 931h2 0c0 4 0 8-2 12v-3l1-1h-2c1-3 1-5 1-8z" class="B"></path><path d="M120 105h-1c-2-2-7-5-7-7 1 0 0 0 1 1s2 1 4 2h0c2 1 4 1 7 1-2 1-3 2-4 3z" class="M"></path><path d="M842 395v-1c2-1 5-2 7-3l1 1h3c-1 1-3 1-4 2-1 0-1 0-2 1h-5z" class="S"></path><path d="M298 783l3-1 1 2c-3 2-5 4-8 5 1-2 2-4 4-6z" class="a"></path><path d="M835 389c4-3 7-6 10-9v1l-2 3-3 3v2c-2 1-4 2-5 3v-3z" class="F"></path><path d="M372 959h5c0 3-1 5-2 8-1 0-1-1-1-2l-1 1c0-2 1-4 1-6l-2-1z" class="P"></path><path d="M120 437c0-1 1-1 2-1h0c-1 5 0 8 0 12l-2 1c-2-3 0-8 0-12h0z" class="R"></path><path d="M303 781l7-7c1 1 2 1 2 1-2 3-4 5-7 7l-2-1z" class="b"></path><path d="M555 965l3 1c1 1 1 1 1 3-2 1-6 0-8 0l2-3 2-1z" class="P"></path><path d="M871 322l2-2 1 2-3 8c-1 0-2-1-3-1l3-7z" class="W"></path><path d="M729 657h1c3 1 6 2 8 5v1s-1 0-1 1c-1-2-4-2-5-3-2-1-2-3-3-4z" class="M"></path><path d="M737 804c1 0 1-1 2-2 2 3 4 7 5 10-3-2-5-4-7-8z" class="K"></path><path d="M909 656c1 0 1 0 2 1s1 1 1 3l-2 2-2-1c-1-1-1-1-1-2 0-2 0-2 2-3z" class="B"></path><path d="M912 92h2c1 6 2 11 1 17 0-2 0-3-1-4v-7h-1l-1 1-1-1 2-1c0-1-1-4-1-5z" class="I"></path><path d="M373 966l1-1c0 1 0 2 1 2-2 4-4 5-8 7 0-1-1-1-1-1l2-1-1-1c3-1 4-3 6-5z" class="B"></path><path d="M112 411c3-1 6-3 9-4 0 1 0 2-1 3s-3 1-4 3c-1-1-2-1-4-1v-1z" class="D"></path><path d="M876 311c3 0 5 0 8 1 1 1 1 2 1 4-3-1-5-2-8-1 0-2 0-2-1-4z" class="U"></path><path d="M198 561l8-2-1 2 1 1v-1 1h3c-4 0-8 1-11 2v-3z" class="H"></path><path d="M384 945c1 0 2-1 2-2 1-1 1-2 2-2h0l1 1c-1 3-3 4-2 7 0 2 2 3 3 3l-1 1c-2-1-3-3-4-5l-1-3z" class="P"></path><defs><linearGradient id="A" x1="872.744" y1="318.492" x2="876.53" y2="312.856" xlink:href="#B"><stop offset="0" stop-color="#88877e"></stop><stop offset="1" stop-color="#a29b89"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M871 322c1-4 2-9 5-11 1 2 1 2 1 4l-3 7-1-2-2 2z"></path><path d="M284 540c0 1 1 3 2 4-2 2-1 3-1 5-2-1-3-2-4-2 0-3 1-5 2-8l1 1z" class="F"></path><path d="M932 667c3 2 5 6 5 9v3l-2-2c-1-3-2-5-4-8 1-1 1-1 1-2z" class="R"></path><path d="M252 880c1 0 1 0 2 1 0 2 1 5 1 8l1 1-1 1h-2c-1-3-1-6-1-9v-2z" class="H"></path><path d="M893 458c2 0 4 0 6 1v2c-1 1-3 2-4 2-2-1-2-1-3-2 0-1 1-2 1-3z" class="C"></path><path d="M786 566v-2c2-2 5-3 8-4 1 2 2 2 3 3-3 0-6 1-9 3h-2z" class="D"></path><path d="M936 199c2 1 4 1 6 1v1c-3 1-7 1-9 5v1h-1v-3l2-4c1 0 2 0 2-1z" class="P"></path><path d="M752 790c1 1 2 2 1 4-1 5-4 10-7 13h-1c4-5 6-9 6-15v-2h1z" class="X"></path><path d="M954 640c1-1 0-7 0-8l2-1 1 9c-2 2-2 6-2 9l-1-9z" class="L"></path><path d="M729 657v-1c2-6 6-8 12-11-5 4-8 8-11 12h-1z" class="I"></path><path d="M932 204v3h1v2c-2 1-7 2-10 2h0c0-1 1-1 2-2h1v-1l-3-1h0c3 0 6 1 8 0l1-3z" class="L"></path><path d="M755 880h1 1v10h-1l-2 1v-1c1-3-1-8 1-10z" class="B"></path><path d="M139 589l1-1c0-1 1-1 2-1-2 4-3 9-3 14l-1 1c0-1-1-3-1-4 0-3 1-6 2-9z" class="R"></path><path d="M127 399l1-11 3 8v2 1c0 1-1 2-1 3l-1-1h1v-5c-1 0-2 2-3 3z" class="Y"></path><path d="M265 627l2 1-3 3c-1 2-1 5-3 6-2 0-2-1-3-2 0-2 3-4 5-6 1 0 1-1 2-1v-1z" class="K"></path><path d="M111 202l3 1v1c-4 1-8 3-12 4-2 0-4 0-5 1l-1 1-4-1c8-1 13-2 19-7z" class="H"></path><path d="M354 976h2l11-5 1 1-2 1s1 0 1 1c-3 1-6 2-9 4-2 0-3-1-4-1v-1z" class="E"></path><path d="M234 896c0-2 1-4 2-5 3-3 5-4 9-4h1c1 1 2 1 3 3h-1 0c-4-1-8-1-10 1-2 1-3 3-4 5zm62-787c3 4 6 8 10 12-1 0-2 0-3-1v1 2c-3-4-5-8-7-12v-2z" class="B"></path><path d="M293 676v-1c0-1-1-1-1-2 1 1 2 1 3 1 3 2 7 6 8 10 0 0-1 0-2-1h-1l-7-7z" class="K"></path><path d="M372 959c-2 0-3 0-5 2v-1l3-3c4 0 12 0 15 2-3 1-6 1-8 0h-5z" class="B"></path><path d="M918 390c2 0 2 0 3-1 0 3-2 6-3 9h0c-1 0-1 1-2 2h-1v-4l1-3h1c1-1 1-1 2-3h-1z" class="H"></path><path d="M923 617c0-5 1-9 0-14v-8c2 8 3 16 2 24l-2-2z" class="P"></path><path d="M895 100l1 1c4 1 9 1 13-1l1-1v1c-4 2-8 5-13 3-2 0-4-1-6-2h2c1 0 1 0 2-1z" class="X"></path><path d="M920 335c1-1 2-1 3-1 3 1 5 2 7 4l-1 1-3-1c-2-1-5 0-8 0h0-1c0-2 1-2 3-3z" class="C"></path><path d="M380 949l1 1v-2l3-3 1 3-1 1c-1 1-2 5-1 7v1h0l-1-1c-1-1-3-5-3-6l1-1z" class="T"></path><path d="M953 562c5 5 8 13 11 19l-1 1-1-1h0l-5-9c0-2-1-5-3-7 0 0 0-1-1-2h0v-1z" class="L"></path><path d="M604 926l2 2c0 5 0 9 2 14l-2 1c-2-6-2-11-2-17z" class="P"></path><path d="M898 434c2 1 4 2 5 3v1 6c0 3 0 4-3 6v-1-1l1-1c1-5-1-9-3-13z" class="L"></path><path d="M124 124c6 0 12-2 18-3l-3 3h0c-1 0-2 1-4 1l-6 1c-2-2-3-1-5-2z" class="X"></path><path d="M296 926l3 1c-2 3-4 6-5 9l-1 3-3-1 6-12z" class="B"></path><path d="M105 390h0c4 0 6-1 8-3 0-1 0-1 1-2 0 2-1 3-2 5s-3 5-4 8c0-2-1-4-2-6-1-1-1-1-1-2z" class="b"></path><path d="M952 622c1 0 1 0 2-1l2 10-2 1c0 1 1 7 0 8l-2-18z" class="a"></path><path d="M124 102c1-1 3-2 4-3l2 2h-1v3s0 1-1 1c-1 2-5 1-7 0h-1c1-1 2-2 4-3z" class="I"></path><path d="M577 915l1-2h1l2 2 2 10-1 1h0c-1-1-1-1-2-1v-1l-3-9z" class="D"></path><path d="M471 181h3c1 1 2 2 2 4 0 1-1 1-2 2s-2 1-3 0c-1 0-2-1-2-2 0-2 1-3 2-4z" class="C"></path><path d="M864 463l-1-1c1-2 1-3 2-4 5-3 11-2 17-1l-3 1c-2 1-4-1-7 1-3 0-6 0-8 2v2z" class="B"></path><path d="M108 291l-3-3c0-3 0-6 1-9h1l1 2c0 3 1 6 1 9 1 1 3 1 4 1-1 0-4 1-5 0z" class="L"></path><path d="M843 504c6 0 10 1 16 3h-20v-2l4-1z" class="U"></path><path d="M903 340l3-1c2-1 7 0 9 1 2 0 3 2 4 4v5l-1 1c0-2 0-6-1-7s-2-1-3-1-1 0-2-1c-4-1-7 0-11 1l2-2z" class="N"></path><path d="M948 643c1 7-2 16-4 22-1 1-1 2-2 3 1-4 2-8 3-13 1-6 1-12 2-18v2c0 2 0 3 1 4h0z" class="a"></path><path d="M127 399c1-1 2-3 3-3v5h-1l1 1-1 4h1 0l-6 6c1-4 3-8 3-12v-1z" class="O"></path><path d="M705 779l3 1h1 0c1-1 0-1 1-1v1c2 4 5 6 8 9-5-2-10-5-14-9 0-1 1-1 1-1z" class="H"></path><path d="M940 196s0 1 1 1c1-1 1-1 2-1l1 1c1 1 3 1 4 3v1c1 1 1 2 1 2-2-1-5-2-7-2v-1c-2 0-4 0-6-1l4-3z" class="R"></path><path d="M735 906l-3-2v-1c1 0 1-1 2-1 3-2 8-4 12-3-1 2-7 4-9 5l-2 2z" class="J"></path><path d="M452 964l3-1 2 2v1h-1c2 1 2 1 3 3l-1 1c-2 0-6 1-7-1 0-1 0-2 1-4v-1z" class="B"></path><path d="M452 964l3-1 2 2v1h-1c-1 1-1 1-2 1l-2-2v-1z" class="T"></path><path d="M899 459c5 0 10-1 15-1h0l-2 2h3 0-1c-2 1-3 2-5 2-3 0-7-1-10-1v-2z" class="U"></path><path d="M912 92l-1-1c-2-4-5-8-10-9l-5-1c3-1 5-1 8-1 3 1 6 6 8 8l2 4h-2z" class="M"></path><path d="M606 943l2-1c2 3 4 6 7 9 0 1 0 2 1 3l1 1h-1c-5-3-8-8-10-12z" class="D"></path><path d="M291 922c3-1 7-3 11-3 1 1 1 2 2 3-1 1-1 1-2 1-2 1-2 3-3 4l-3-1 2-3-1-1c-2 0-4 1-7 1 1 0 1-1 1-1z" class="C"></path><path d="M513 248c3-5 5-10 8-15 1-2 1-5 3-6v1c-4 8-7 16-9 25l-2-5z" class="I"></path><path d="M210 79c3 2 6 4 8 7v1c-2 0-5 0-7-1v-1c0-2-2-2-3-3-1 0-2 0-2-1h0v-1c2 1 3 0 4 1 1 0 2 1 3 1-2 0-2-1-3-2v-1z" class="O"></path><path d="M170 456l2 2 1-1v1c1 1 1 2 1 4h2c-1 1-1 1-2 1-2 4-4 6-8 8l2-4c3-3 3-7 2-11z" class="U"></path><path d="M370 78l3-3c8-7 16-5 26-4l-4 3-1-1c0-1 0-1-1-1-5-1-13-1-17 2-2 1-4 3-5 5l-1-1z" class="T"></path><path d="M953 597c1 2 3 4 4 6v7l1 1-3-1v1l-1 1-1-4c1-3 0-7 0-11z" class="F"></path><path d="M96 335c2-1 4-1 6 0 1 0 2 1 2 3h0-7l-8 3c3-3 4-5 7-6z" class="D"></path><path d="M641 122c0 1 0 1 1 2l2-1v1c-5 5-9 7-16 10 4-4 9-7 13-11v-1z" class="P"></path><path d="M89 669l3-1v1 1c-3 3-4 9-5 13l-2-3c0-2 0-3-1-4h1 0c1-2 2-5 4-7z" class="I"></path><path d="M257 918l-4-4c-2-2-3-4-3-7 2-2 6 0 9 0h1 0c-1 1-2 1-3 1s-1 0-1 1l-1 1-5-3c2 4 6 7 10 10l-3 1z" class="W"></path><path d="M847 436v-2c2-2 6-4 8-5 3-1 7-1 9 0l-17 7z" class="G"></path><path d="M441 928v1c-4 4-6 7-8 12 0 0 0-1-1-2l-1-1-1-2c0-2 8-7 11-8z" class="H"></path><path d="M430 936h3l1 1c-1 1-1 2-2 2l-1-1-1-2z" class="P"></path><path d="M804 445l-4 1c2-4 3-10 7-13v1 1c-1 1 0-1-1 1l-1 1c0 1-1 2-1 3 0 0 0 1 1 1 0-1 0-1 1-2 0-1 0 0 1-1 0-2 1-4 2-4h1c0 2-4 8-6 11z" class="T"></path><path d="M455 140c3-6 11-10 17-12h1v1c-2 3-7 4-11 6-2 2-4 4-5 6 0-1 0-2 1-3h0c-1 0-2 1-3 2z" class="I"></path><path d="M546 180h1c2 0 2 1 3 2s1 2 0 3c0 2-2 2-3 3-2 0-2-1-3-1-1-2-1-2-1-4 1-2 2-2 3-3z" class="C"></path><path d="M682 710c0 1 0 0 1 1v1c2 0 2 1 4 1l1 1 1 1h0v1c1-2 0-2 0-4h-1v-3-1-3-1-1c1-1 0-1 1-2v-1 10c1 3 1 6 2 9l-1 2c-1-3-4-5-6-7l-6-5 1-1v1c1 0 2 1 3 1z" class="Y"></path><path d="M889 713c8-3 14-7 20-12h0c-1 1-1 2-2 3h1c-5 5-11 8-17 10 0 0-1 0-2-1z" class="L"></path><path d="M756 509h0c4-3 9-6 15-8-2 3-4 3-6 5l-9 6-5 2 5-5z" class="Z"></path><path d="M730 98l1 5c-2 4-3 8-6 12-1-2-1-4-1-6l6-11zm159 436h3c2 0 4 2 5 4 0 1 0 2-1 3s-1 0-2 0c-3-1-5-3-6-5l1-2z" class="R"></path><path d="M907 384l1-2c2 5 5 6 10 8h1c-1 2-1 2-2 3h-1l-2 1c-3-3-6-6-7-10zm-800-15h-1c1 4 8 7 10 9 0 2 0 2-1 3-3-3-9-4-10-9 0-2 0-3 1-5l1 2z" class="B"></path><path d="M697 769c1 2 3 5 5 6 1 2 2 3 3 4 0 0-1 0-1 1-3-2-6-5-8-7 0 1 0 3-1 4 0 2-1 4-1 6h-1v-8h1v-2c1-1 2-2 3-4z" class="P"></path><path d="M239 639c1 1 1 1 2 1-1-4 1-7 3-10l1 1c-1 2-2 3-3 6h0c0 3 0 5 2 7l5 7c-4-2-8-5-11-9 1-1 1-2 1-3z" class="I"></path><path d="M764 886c5-1 6 5 10 6h0c2 1 3 4 3 6h-1v-1 3h-1c-1-3-2-6-4-8-4-4-7-3-11-3v-1c1-1 2-2 4-2z" class="H"></path><path d="M335 681c-2-1-3-4-4-5-4-5-9-10-14-13v-1h0c8 5 14 12 21 18-1-2-3-4-3-5 1 0 2 2 3 3v2 1 1h0c-1-1-1-1-3-1z" class="D"></path><path d="M277 646c1 1 2 2 4 3 2 2 6 4 9 6 2-1 4-3 5-5l1-1v2l-3 3-3 2h0 0c-3-1-6-1-8-3-3-1-5-4-8-5h-1 4 0 1l-1-2z" class="M"></path><path d="M882 457l1-2v2c2 2 7 1 10 1 0 1-1 2-1 3-2-1-7 0-9 0l-3 3v-1l1-1v-1c-3-1-6-2-9-2 3-2 5 0 7-1l3-1z" class="J"></path><path d="M328 657c-2-2-2-5-3-7-3-6-8-11-12-16l1-1 5 7c2 4 6 8 9 12v-4c0 1 1 2 1 2 1 1 0 1 1 2v2l1 3h0-2v1l-1-1z" class="X"></path><path d="M124 341h-4c-3-1-7 0-11 1-3 2-5 5-6 9 0 1 0 1-1 2-1-2-1-4 0-7 2-3 4-4 7-6h0c3-1 8-1 11 0h3l1 1z" class="L"></path><path d="M263 583l3-1v-1h-2c-2 0-4 0-5-1v-1c2 0 4 1 5 1h2l2 1c1 0 3 0 4 1 2 0 4 1 6 0 3 1 4 4 6 6-1 1-1 1-2 1-1-2-2-2-4-3 0 0-1-1-2-1h0c-1-1-1-1-2-1h-1c-1-1-2-1-3-1-2-1-5 0-7 0z" class="F"></path><path d="M242 862h1l1 1v3c0 1 0 1-1 2s-2 1-4 1v-1c-1 0-1 0-2 1h0l-1 1v2h1c0 1 1 2 1 2 0 1 1 1 1 2 1 2 3 3 5 4l3 1v1 1h0c-1-1-1-2-2-2-8-4-10-11-14-17l1-1 1 1c1 1 3 2 5 3s3 1 5 0c1-2 0-3-1-5z" class="Y"></path><path d="M528 204c2-1 3-3 5-3l1-1 3-1c-4 4-8 7-11 11-3 5-5 11-9 15 0-1 1-3 2-4 3-7 5-12 9-17z" class="B"></path><path d="M111 117c-1-1-1-2-1-4 1 2 2 3 3 4h0c2 2 4 4 6 5 1 0 4 2 5 2 2 1 3 0 5 2-2 1-3 1-5 1-6-1-10-5-13-10z" class="L"></path><path d="M659 135c1 0 1-1 2-1h1l2-1v-1c2-1 4-3 6-4 1-1 3-2 5-2l-7 8c-1 2-1 2-3 2-2 1-4 0-5 0-1-1-1 0-1-1z" class="G"></path><path d="M402 939h2l-1 1v3c-1 3-5 9-9 11-1 1-3 0-5-1h0l1-1h1c5-2 9-9 11-13z" class="D"></path><path d="M103 424h1c1 4 3 7 7 9 2 2 7 2 9 1 3-1 5-3 7-4-2 2-3 4-5 6-1 0-2 0-2 1h-2c-4 0-8-1-10-3-3-3-4-6-5-10z" class="B"></path><path d="M790 630c-1 8-4 14-9 19l-7 5c2-3 9-9 9-13v-3-1h0c1 1 1 2 1 3l1 1 3-10c1 3-1 6-2 9l2-4v-1c0-1 1-1 0-2v-1l2-2z" class="I"></path><path d="M743 521s1-1 2-1c1-1 3-2 4-3s1-1 2 0h0s-1 1-1 2c-1 0-1 1-2 1-1 1-1 0-1 1h1 1 0 1c1-1 2-1 3-1l-21 11-1-1c1-1 2-2 3-2l3-3h0c2 0 4-2 5-3h1v-1z" class="Z"></path><path d="M86 418c2-2 4-3 6-5h0c5-3 14-2 20-2v1h-1c-8 1-13 1-20 6h-5z" class="M"></path><path d="M845 517c3-2 20 2 24 3-2 0-4 0-5 1h-2l6 3v1c-4-2-9-4-14-6-3-1-6-1-9-2h0zm-49-104c4 0 8-5 11-8s6-6 10-8c-4 5-10 8-13 13l-2 2h1l1-1 2 1-1 1h-3c-4 2-7 5-10 9l1-3 3-6z" class="U"></path><path d="M172 394l13 4h-10c-1-1-1-1-2-1-1-1-2-1-3-1h-3l-1-1h-8c-1 1-1 1-2 1h-2l-11 3h-1l1-1 4-1 5-1 1-1h3l1-1c3 0 8 0 11 1 2 0 3 0 4-1z" class="F"></path><path d="M911 642c2-1 3-1 4 0-5 7-13 13-21 16-1 1-1 1-2 1 8-5 14-10 19-17z" class="C"></path><path d="M106 367c1-2 1-3 3-3 1-1 5-2 7-1h2c1 0 2 1 3 1h2c1 0 1 1 2 2-3 0-14 0-17 2l-1 1-1-2z" class="G"></path><path d="M711 927c-1-2-2-5-4-5l-1-1c0-1 0-2 1-2l1-1c2 0 4 1 5 1 3 1 7 2 10 3l-1 1-10-2c-1 2 2 5 2 7v2c-1-1-1-2-2-3h-1z" class="C"></path><path d="M300 683h1c1 1 2 1 2 1 1 1 2 2 2 3-1 2 0 5 1 8-3 3-6 7-9 11l-3 3-1-1c4-4 8-8 9-13s0-8-2-12z" class="a"></path><path d="M380 949c2-4 3-10 7-13 1-1 2-1 3 0 1 0 2 1 2 2-1 2-2 3-3 4l-1-1h0c-1 0-1 1-2 2 0 1-1 2-2 2l-3 3v2l-1-1z" class="H"></path><path d="M884 312l-2-3c3 1 6 7 7 9s2 5 3 7h3c-1 1-2 1-3 2l-4 1h0-3c0-1 2-1 4-2-1-4-2-7-4-10 0-2 0-3-1-4z" class="B"></path><path d="M264 570c5 3 10 7 16 8 1 1 1 1 1 3l2 1-1 1-3-2h-1c-2-1-3-2-5-3l-5-2c-1-1-1-1-2-1l-1-1h-1c-1 0-1 0-2-1 1-1 1 0 2 0s1-1 2 0h1l1 1c1 0 2 1 3 1v1-1c-3-2-6-3-7-5z" class="O"></path><path d="M569 928l6 4c1 1 3 2 4 4-1 0-2 1-2 1v4c0 3 0 5-1 8h0l-2-1 1-5v-5c-2-3-4-5-6-7v-3z" class="B"></path><path d="M138 314c1-1 2-1 4-1h2c1-1 2-1 3 0 2 2 4 4 5 7l1 1v6 2 1c-3-1-4-9-5-12-2-3-3-3-6-4h-4z" class="U"></path><path d="M829 562c6 2 10 5 15 8 1 2 4 4 3 6-6-5-12-8-19-11v-1h0c0-1 1-1 1-2z" class="K"></path><path d="M147 429c4 0 9 0 12 2l4 2-12-1c-4 0-10 1-13 3l-1 1v-2c2-3 7-4 10-5z" class="H"></path><path d="M918 425c1 3 0 5-1 7s-4 4-7 5c-2 1-5 1-7 0-1-1-3-2-5-3-2-2-3-4-5-6l1-1c2 2 4 6 7 7 4 2 8 1 11-1 3-1 4-4 5-7h1v-1z" class="P"></path><path d="M267 789c2-1 2-1 4 0 2 2 2 3 3 6 0 7-3 11-7 16l-1 1c-1-2 5-11 5-15 1-3-2-6-4-8z" class="K"></path><path d="M917 368v-1c1 2 2 3 1 5-1 4-7 7-10 10l-1 2-1-3h0c0-2 3-4 5-6s4-4 6-7z" class="J"></path><path d="M326 925v3c-4 6-13 11-20 12h-1l-1-2h-5c4 0 7-1 10-2 6-1 14-6 17-11z" class="b"></path><path d="M234 896h0c0 2 0 3-1 5h-1l-1-1c-1-2 0-5 2-7 0-1 1-2 1-2 1-3 3-5 5-7 4 0 4 0 6 2v1c-4 0-6 1-9 4-1 1-2 3-2 5z" class="T"></path><defs><linearGradient id="C" x1="864.499" y1="699.913" x2="856.203" y2="695.69" xlink:href="#B"><stop offset="0" stop-color="#b6ada1"></stop><stop offset="1" stop-color="#c6c4af"></stop></linearGradient></defs><path fill="url(#C)" d="M853 703c-1-1-1-1-1-2 7-4 15-7 22-10l1 1c-4 2-8 5-12 7-1 1-5 2-6 3-1 0-1 1-1 1l-2-1-1 1z"></path><path d="M689 700c1-1 1-2 2-3h1c2 0 3 1 5 3h-3c-1 1-1 2-1 3-1 5 0 11-2 16-1-3-1-6-2-9v-10z" class="D"></path><path d="M888 328l4-1-2 2 1 1c0 1-1 2 0 3 0 1 0 1 1 2 0 1-1 2-1 3l-1 1h0v2h1l1-1h1c-3 2-5 4-7 6-1-1-1-2 0-3 1-2 0-4 1-6 0-1 0-3 1-4v-5z" class="O"></path><path d="M147 605c1-2 1-3 2-5 3-3 9-5 13-5 2 0 3 1 4 3-1 0-2 0-2 1-2-1-1-1-3-1-4 1-8 2-11 4l1 5h-1 0c-2 0-1-1-2-3l-1 1z" class="D"></path><path d="M485 200h0c2 1 4 3 6 5 8 6 10 14 13 23h0c-3-1-5-9-7-12-1-2-3-5-4-7-3-4-5-6-8-9z" class="B"></path><path d="M312 118h0c2 3 4 6 4 9-1 1-2 2-3 2-4 0-8-4-10-6h0v-2-1c1 1 2 1 3 1l1 1c2 1 4 2 7 3l-2-7z" class="J"></path><path d="M98 429v1 2c-1 4 0 9 3 12 3 4 7 6 12 7h3-1c-3 1-6 2-8 1-4-1-8-4-10-8s-1-10 1-15z" class="B"></path><path d="M798 587h3c4-1 5-4 6-7 0 3 0 7-2 10-2 1-4 1-6 1-6-1-13-2-20-3 0 0-1 0-2-1h-2v-1l20 3h5l-2-2h0z" class="Y"></path><path d="M153 321l8-32h1c-1 5-1 10-3 15-1 7-6 15-5 22l-1 3v-2-6z" class="P"></path><path d="M231 864c4 6 6 13 14 17 1 0 1 1 2 2-1 1-1 2-1 4h-1v-1c-2-2-2-2-6-2l3-2c-1-1-3-2-4-3-4-4-7-9-8-14l1-1z" class="a"></path><path d="M293 676c-2-2-4-3-6-4l-6-3c-2 0-3-1-3-3s1-5 2-6c2-1 3-1 5-2-2 2-4 2-5 5v3c2 1 3 1 5 2 4 0 4-4 7-6v1c-1 2-3 4-5 6 3 1 6 3 8 5-1 0-2 0-3-1 0 1 1 1 1 2v1z" class="L"></path><path d="M67 609c1 0 1 0 1-1h4l-1 1 1 1c-1 3 0 8-1 11l-3 22-1-1c0-3 1-7 1-10 1-2 1-5 0-7l1-9c1-2 1-4 0-5h-2-1l1-2z" class="a"></path><path d="M843 384c8 0 14-10 21-10h1 1c1-1 2-1 4-2v1s-1 1-2 1l-6 3c-1 1-2 1-3 2h0c-2 0-5 2-6 3l-13 7v-2l3-3z" class="O"></path><defs><linearGradient id="D" x1="331.832" y1="938.142" x2="329.752" y2="930.395" xlink:href="#B"><stop offset="0" stop-color="#1c1a18"></stop><stop offset="1" stop-color="#353430"></stop></linearGradient></defs><path fill="url(#D)" d="M326 923c1 4 2 8 5 12l4-8 1-1c1 2-2 4-1 5v-1c1-1 0-1 1-2l1 1-8 15-2 1c1-1 3-5 3-7 0 0-2-2-2-3-1-2-2-4-2-6v-1-3-2z"></path><path d="M106 401c-2-3-4-6-5-10-2-4-3-9-3-14 1 4 3 9 6 13h1c0 1 0 1 1 2 1 2 2 4 2 6 0 1 0 3-1 5l-1-2z" class="H"></path><path d="M142 121c2-1 3-1 5-1l1 2c-1 3-5 5-7 7-3 2-5 5-8 7l-2-1c4-3 8-7 10-11h-2 0l3-3z" class="B"></path><path d="M864 429c8-1 15 1 21 5-13-4-27-3-39 3l-6 4v-1l7-4 17-7z" class="P"></path><path d="M647 137c0-2 1-4 1-6v-1c1-2 2-7 4-9 1-2 3-2 4-4 1-1 1-2 2-2-1 3-2 6-4 8-2 3-2 6-2 10h1l-1 2 1 2h-6z" class="M"></path><path d="M138 314h4c-3 3-6 8-7 13l2 1c-5 1-12 0-17-1 3-1 6-1 9-2h3s2-4 2-5c1-2 3-4 4-6z" class="P"></path><path d="M362 136c0-1 0-3 1-4 2-1 3-1 4-1h1c-1-6-3-11-5-16 0-2-1-4-1-5 2 1 2 6 3 8 3 1 6 2 8 3 1 1 2 1 2 2v1c-2-1-6-3-8-3 0 3 1 9 3 12-1 1-1 2-2 3h-1-2-3z" class="T"></path><path d="M72 202c1-1 3-2 4-2 4-2 7-3 11-2 1 1 4 3 4 5 1 2 1 4 0 6h-1 0v-1c0-1 0-2-1-4-3-2-6-3-10-3-2 1-4 1-6 3l-1-2z" class="D"></path><path d="M314 698c2-1 3-1 5-1 2 2 2 3 3 5 0 6-2 13-4 18 0-2-1-4 0-6 0-4 1-8 0-11-1-2-2-3-4-5z" class="B"></path><path d="M256 909h0c5 1 8 0 12-3 1-1 2-2 4-2-3-2-8-4-11-3l-1 1h-1c0-2 0-3 2-4h0c1 0 1 0 2 1 3 0 5 1 8 1 2 1 4 1 7 2v1c-2 2-6 3-9 4-4 2-7 4-12 3h-2l1-1z" class="D"></path><path d="M252 821c-1-1-1-3-1-4 5 6 11 10 19 10 6 0 11-1 16-4l-1 1c-3 3-11 5-15 5-7 0-13-3-18-8z" class="H"></path><path d="M99 259c0-2 1-4 0-6-2-2-5-3-7-4l14-7c-1 2-3 5-4 7-1 4-1 8-2 11l-1-1z" class="D"></path><path d="M111 117c-2-2-3-4-4-7-1-7 0-16 4-22 2-3 5-7 8-8 2 0 5 0 6 1-2 0-5 1-7 2-4 3-7 7-9 12v1 1c-2 8 0 14 4 20h0c-1-1-2-2-3-4 0 2 0 3 1 4z" class="K"></path><path d="M331 696c1 1 2 2 3 2 1 1 1 2 3 2l4 4h0c-4 2-7 4-10 6l-8 8c0-2 0-3 2-4l3-3 5-3v-1c-1-2-1-2-1-3-1-1 0-2 0-3-1-2-1-3-1-4v-1z" class="T"></path><path d="M367 939l10-19 1 5h0v7l-1 1c0 2-1 4-2 7 0-4 1-8 1-11-2 3-4 6-6 8-2 3-3 8-4 12-1-2-1-3 0-4 0-1 0-3 1-3v-3z" class="Y"></path><path d="M735 522c3-1 5-3 8-6 4-3 8-5 13-7l-5 5h0c-3 3-6 4-9 7h1 0v1h-1c-1 1-3 3-5 3h0c-1 1-3 1-4 2 0-2 1-1 1-3 1 0 1-1 1-2z" class="S"></path><path d="M131 213c1 1 7 3 8 5l-2 1c-2 1-4 6-5 8v-2l1-1h0v-1h-2-1-1 0-1l-1 1h1 0 2v1h-5c3-4 4-7 6-12z" class="M"></path><defs><linearGradient id="E" x1="248.674" y1="594.101" x2="245.326" y2="600.899" xlink:href="#B"><stop offset="0" stop-color="#535046"></stop><stop offset="1" stop-color="#767263"></stop></linearGradient></defs><path fill="url(#E)" d="M238 606c1-1 1-1 1-2 6-8 14-13 24-15l-1 1c-1 0-1 1-2 1 0 1-2 1-3 2v1h0c-5 2-9 5-13 9-1 0-2 1-3 1-1 1-1 2-3 2z"></path><path d="M253 803h3c2 0 5 3 6 4v2c-2 0-4 0-7-1l-6 3-2-1v-2c1-3 3-4 6-5z" class="J"></path><path d="M827 99c2-1 6-1 8-1 7 0 14 1 20 1 3 1 6 1 8 2h0-1l-16-1c-12 0-24 0-36 3 5-3 10-4 17-4z" class="C"></path><path d="M285 658h2c1 1 1 2 2 2s2 2 3 2c-3 2-3 6-7 6-2-1-3-1-5-2v-3c1-3 3-3 5-5z" class="G"></path><path d="M260 917c5 3 10 5 15 6s11-1 16-1c0 0 0 1-1 1l-7 2c-9 1-18-1-26-7l3-1z" class="L"></path><path d="M427 892c1 1 2 3 2 5-2 8-6 15-11 22l-3-1c7-7 9-15 11-25l1-1z" class="D"></path><defs><linearGradient id="F" x1="914.54" y1="452.677" x2="916.553" y2="442.342" xlink:href="#B"><stop offset="0" stop-color="#171515"></stop><stop offset="1" stop-color="#33322c"></stop></linearGradient></defs><path fill="url(#F)" d="M920 424c3 1 4 3 5 6 1 4 1 11-1 15-3 4-6 6-10 7s-7 1-10-1c7-1 13-2 17-7 2-2 3-4 3-6v-1c1-4 0-7-1-10-1-2-2-2-4-2l1-1z"></path><defs><linearGradient id="G" x1="187.8" y1="540.614" x2="186.134" y2="554.438" xlink:href="#B"><stop offset="0" stop-color="#1b1914"></stop><stop offset="1" stop-color="#4c4d48"></stop></linearGradient></defs><path fill="url(#G)" d="M193 537l1 1-2 3h0c1 0 2-1 2-2l1 1-7 7c-4 6-7 13-10 20h0c0-2 1-5 1-8 2-5 5-11 8-16 1 0 6-5 6-6z"></path><path d="M235 839c2 8 1 17 7 23h0c1 2 2 3 1 5-2 1-3 1-5 0s-4-2-5-3l-1-1h4c1 1 3 2 4 2-1-3-3-6-4-9-1-5-1-11-2-16l1-1z" class="C"></path><path d="M868 82l-1-1-1-1c-2 0-4-3-5-5-2-1-9 0-11 0-1 0-3-1-4 0-2 0-4 0-5 1h-3l-2 1h-5l-3 1s-1 0-2 1h-1l-3 1-1-1c1 0 2-1 3-1h1l5-1 1-1 9-1c1-1 2-1 3-1h4c4-1 8-1 12-1 1 0 3 0 4 1s3 3 3 4c1 1 2 1 3 2s2 1 3 3c-1 0-3-1-4-1z" class="Y"></path><defs><linearGradient id="H" x1="917.432" y1="640.877" x2="919.611" y2="618.175" xlink:href="#B"><stop offset="0" stop-color="#040502"></stop><stop offset="1" stop-color="#363535"></stop></linearGradient></defs><path fill="url(#H)" d="M923 617l2 2c-2 8-4 16-9 23h-1c-1-1-2-1-4 0 6-8 10-16 12-25z"></path><path d="M639 96c1 2 1 7 1 9 1-4 2-7 1-11v-2c2 8 2 15-3 21-3 6-10 9-15 12v-1c2-2 4-3 6-4 5-4 9-10 10-16v-8z" class="W"></path><path d="M504 261c-1-18-9-37-22-51l1-1 2 2c10 12 14 23 19 37v1c0 2 0 4 1 6h-1v5c1 1 1 2 1 3l-1 1v-3zM70 203l1-1h1l1 2c-4 6-7 12-6 20 0 2 1 4 3 6l3 3v2 1l-1-1c-3-2-6-6-7-10-1-7 1-15 6-22h-1z" class="K"></path><path d="M841 556c2 4 4 9 5 13 0 3 3 6 5 8-2 3-3 4-5 6-4 3-8 5-13 6 5-3 11-7 14-12v-1c1-2-2-4-3-6l-4-11c1-1 1-2 1-3z" class="I"></path><path d="M868 525v-1l-6-3h2c1-1 3-1 5-1l15 6c-1 0-3 0-4-1l-2-1v1h0l1 1h0 0v1l-1-1h-1 0-1l-1-1h-2l1 1c1 0 3 1 4 2v1s2 0 2 1c3 1 4 2 6 3v1l-18-9z" class="N"></path><path d="M881 136l-8-8c-1-1-2-3-2-4 0-2 1-3 2-3 5-1 9 2 14 4h0l1 2c-4-1-8-3-12-3l-1 1c0 4 6 8 8 11h-2z" class="D"></path><defs><linearGradient id="I" x1="942.23" y1="616.098" x2="954.178" y2="625.632" xlink:href="#B"><stop offset="0" stop-color="#14130e"></stop><stop offset="1" stop-color="#37332b"></stop></linearGradient></defs><path fill="url(#I)" d="M947 635c0-3 0-6-1-9s-2-7-2-11c0-2 1-4 2-5 3-2 5-2 7-2l1 4-1 1-2-1v-1c-1 1-2 1-3 2-2 3 1 11 1 15l-1 6-1 1z"></path><path d="M150 82c3-3 6-3 9-5l4-4c10-1 18 0 28 3 4 1 7 1 11 3h-1-1l-19-4c-3-1-7-1-11-1-1 0-3 0-4 1l-1 2c-2 2-7 1-10 4h0c-2 0-3 1-5 1z" class="M"></path><path d="M247 821c-3-5-4-9-4-15 1 2 2 4 4 5h1l3 6c0 1 0 3 1 4 0 2 2 6 4 8h1v1c-4-1-7-5-10-9z" class="S"></path><path d="M166 552c4-4 9-7 14-9 2-2 5-2 8-4 2-1 3-2 5-2h0c0 1-5 6-6 6-2 0-3 2-5 2-5 3-10 6-15 10v-1l5-5c-1 0-2 1-3 1-1 1-1 2-3 2h0z" class="W"></path><path d="M95 317c-1 1 0 1-1 1-3 4-6 9-8 13v1c-3 7-1 13-1 20 0 3-1 6-2 9-1-4-1-9-1-13 0-11 4-24 12-32l1 1z" class="M"></path><defs><linearGradient id="J" x1="888.59" y1="674.673" x2="892.612" y2="679.573" xlink:href="#B"><stop offset="0" stop-color="#1e1b1a"></stop><stop offset="1" stop-color="#343430"></stop></linearGradient></defs><path fill="url(#J)" d="M895 666c1 2 2 3 2 5 1 4-4 10-7 12-4 5-9 7-15 9l-1-1c1 0 2-1 2-1 7-3 11-8 16-13 1-1 2-4 3-5 0-2-1-3-2-4l2-2z"></path><path d="M914 224c5 3 10 4 17 2 4 0 8-3 10-7l3-6c1 3 0 6-2 9s-5 6-9 7c-3 0-7 1-10 0-3 0-5-1-8-2l-1-3z" class="P"></path><path d="M504 249c2 4 2 9 2 13v19c0 6-1 12 0 18h-1c0-1-1-1-1-2v-36 3l1-1c0-1 0-2-1-3v-5h1c-1-2-1-4-1-6z" class="I"></path><path d="M326 923l2-1c0-2 0-1 1-2l3-3 2 2c-1 1-1 2 0 3 0 1 1 3 1 5l-4 8c-3-4-4-8-5-12z" class="G"></path><defs><linearGradient id="K" x1="86.309" y1="432.251" x2="80.326" y2="422.665" xlink:href="#B"><stop offset="0" stop-color="#b0a685"></stop><stop offset="1" stop-color="#c3bbad"></stop></linearGradient></defs><path fill="url(#K)" d="M86 418h5c-6 6-10 12-11 21 0 7 2 12 5 19h0c-4-4-6-11-6-16-1-9 1-17 7-24z"></path><path d="M367 939v3c-1 0-1 2-1 3-1 1-1 2 0 4 0 1 0 1-1 2s-1 5-3 6v-1-1h-1c0 1 0 2-1 3 0 2-2 4-3 5 0-2 0-3 1-5l-2-2 2-6 2-3c1 0 1 1 2 1l5-9z" class="F"></path><path d="M358 950l2-3c1 0 1 1 2 1l-4 10-2-2 2-6z" class="c"></path><path d="M125 366h2c-3-3-7-5-11-6 2-1 3 0 5 1 5 2 12 6 17 9 5 4 11 8 15 12-4-1-6-4-10-6-5-3-10-7-16-8s-14-1-19 0c3-2 14-2 17-2z" class="D"></path><path d="M948 200c6 5 10 11 11 19 0 5-1 10-4 13-1 1-2 3-3 3l-1-2 1-1c3-4 5-8 4-12 0-7-2-12-7-17 0 0 0-1-1-2v-1zM779 605h0c0-2-3-3-4-5l3 1h1c5 3 9 12 10 18 1 4 1 8 1 11l-2 2v1c1 1 0 1 0 2v1l-2 4c1-3 3-6 2-9 0-4 0-8-1-12-2-5-5-9-8-14z" class="K"></path><path d="M144 224c1 0 1 1 2 2 2 5 0 11-2 17-2 5-5 11-10 14l-1-1c8-10 11-20 11-32z" class="J"></path><path d="M695 671c1-1 1-1 2-1 1-1 1-1 2-1h1c1 0 1-1 2-1s1 0 1 1-2 3-3 4h-1c-1 1-2 3-4 3l-1 1-3 3s-1 1-2 1c-1-1-1-1-2 0-2 0-1 0-2-1v-3l1-1c1 0 3-2 5-2l1-1 1-1c1 0 1 0 2-1z" class="G"></path><defs><linearGradient id="L" x1="904.766" y1="123.267" x2="900.062" y2="112.712" xlink:href="#B"><stop offset="0" stop-color="#9b9685"></stop><stop offset="1" stop-color="#bdb09b"></stop></linearGradient></defs><path fill="url(#L)" d="M887 125c6 0 11 0 16-3 7-4 9-10 11-17 1 1 1 2 1 4-2 6-5 12-11 16-4 2-11 3-16 2h0l-1-2z"></path><path d="M92 501v1c0 1-1 1-2 2v2c-9 6-20 14-22 26 0 1 0 1 1 1-1 1-1 2-2 3-1-3 0-6 0-9 3-11 16-20 25-26z" class="I"></path><path d="M127 400c0 4-2 8-3 12-4 6-13 11-20 12h0-1l-1 1-1-1c4-3 14-5 16-9l-1-2c1-2 3-2 4-3s1-2 1-3c2-3 4-4 6-7z" class="C"></path><path d="M714 813c1 0 3 0 3 2l1 1c0 3 3 5 6 6 7 4 15 6 23 4 4-2 8-4 12-8l1 1c-3 2-5 5-8 7-2 1-4 2-7 3-9 2-20-2-26-9l-3-6-2-1z" class="B"></path><path d="M738 663c0 2 0 2-1 4-5 3-11 3-16 6-1 1-3 3-5 4-1 1-3 4-4 5-1-1-1-2-2-2v-1c6-5 10-8 17-11-1 0-5-2-6-3-1 0-1 1-2 0v-1h1v-1c4-1 6 4 9 4s6-1 8-3c0-1 1-1 1-1z" class="K"></path><defs><linearGradient id="M" x1="130.254" y1="221.938" x2="124.959" y2="245.609" xlink:href="#B"><stop offset="0" stop-color="#0c0b0b"></stop><stop offset="1" stop-color="#23231c"></stop></linearGradient></defs><path fill="url(#M)" d="M137 219c0 6-3 11-6 16-1 2-2 5-4 6-3 4-8 6-12 8 6-7 13-14 17-22 1-2 3-7 5-8z"></path><defs><linearGradient id="N" x1="247.296" y1="618.972" x2="230.941" y2="625.849" xlink:href="#B"><stop offset="0" stop-color="#8e8b7b"></stop><stop offset="1" stop-color="#c2b7a4"></stop></linearGradient></defs><path fill="url(#N)" d="M238 606c2 0 2-1 3-2 1 0 2-1 3-1-6 7-10 15-9 25 0 1 0 2 1 4h0c1 3 2 5 3 7 0 1 0 2-1 3-2-4-5-7-5-12-1-8 0-18 5-24z"></path><path d="M206 559c8 0 16 1 23 0 3 1 6 3 8 6 0 1-1 1-2 2-1 0 0 0-1-1-1-2-5-3-7-3l-18-1h-3v-1 1l-1-1 1-2z" class="D"></path><path d="M955 573v-1h2l5 9v2l-1-1-1-1h0v4 1c0 3 1 10 0 12 0 1-1 2-2 3l-1 2c-1-2-3-4-4-6v-1c0 1 1 1 1 2h4c2-6-1-14-4-20h1v-5z" class="a"></path><path d="M955 573v-1h2l5 9v2l-1-1-1-1h0v4 1c-2-4-3-9-5-13z" class="F"></path><path d="M909 701c3-3 5-6 7-10 4-7 7-15 11-22 1 0 1 1 1 2-5 11-9 27-20 33h-1c1-1 1-2 2-3h0z" class="K"></path><path d="M159 241c2 1 3 4 3 6v1l1 4c0 8 0 17-2 24-1 4-3 7-4 10-5 8-8 16-15 23v-1c6-9 12-18 16-28 2-4 2-8 2-12 1-7 2-16 1-22-1-2-2-4-2-5z" class="W"></path><path d="M329 663c3 1 6 6 7 8 2 2 3 4 4 6 2 5 5 10 7 15h-1v-1l-2-2v-1h-2l1 2c1 2 0 3 2 5h0c-4-5-9-10-14-13l-14-12v-1l25 21-7-9c2 0 2 0 3 1h0v-1-1l2 1-1-2c-1-3-2-4-3-6l-7-10z" class="C"></path><path d="M882 212h1c3 4 3 10 6 14 3 8 11 15 16 21-2-1-5-3-7-3h-1c-2-2-3-4-4-6-6-7-9-16-11-26z" class="D"></path><path d="M455 140c1-1 2-2 3-2h0c-1 1-1 2-1 3 0 8 2 16 8 22 2 2 4 4 6 5 6 5 14 11 20 17 1 2 3 4 5 6h-1c-3-2-5-5-7-7-3-3-7-6-10-8l-7-7c-6-4-12-10-15-16-2-4-2-9-1-13z" class="K"></path><path d="M883 208c2-1 4-2 6-2 1 7 2 13 6 18-1 0-2 0-2-1-1-1-1-1-1-2l-1 1h-3c0 2 1 2 2 3l-1 1c-3-4-3-10-6-14h-1 0v-2c0-1 1-1 1-2z" class="N"></path><path d="M872 459c3 0 6 1 9 2v1l-1 1v1l-3 4c0-1-2-1-3-1-4 0-6 0-9-3l-1-1v-2c2-2 5-2 8-2z" class="G"></path><path d="M865 464h3c3 1 6 1 9 2l3-3v1l-3 4c0-1-2-1-3-1-4 0-6 0-9-3z" class="S"></path><path d="M312 118c-2-1-2-2-3-4-3-4-5-8-6-13h0c2 5 5 11 10 14l32 10c-3 0-5 1-8 1-4-1-8-3-12-5-4-1-9-3-13-5v1 1h0z" class="M"></path><path d="M759 818c1-1 1-3 2-4-1-2-2-3-3-4-4-3-7-1-12 0l1-1h0c1-1 1-2 2-3 2-1 5-4 7-3 4 0 7 2 10 5l-3 5-3 6-1-1z" class="E"></path><path d="M518 206h0l1 1c1-1 1-2 1-3h1c0 1 0 3-1 4v1c-2 1-1 2-1 3v1l1-3 2-4 4-7c0-1 1-2 2-2h0l-1 2v1l-1 2h0c0 1 0 2-1 3v2c1-1 1-2 2-3h1c-4 5-6 10-9 17-1 1-2 3-2 4l-1 2h0l-1-1h-1l2-5c1-1 0-3 1-5 0-3 1-6 1-10z" class="O"></path><defs><linearGradient id="O" x1="302.95" y1="593.979" x2="293.999" y2="590.502" xlink:href="#B"><stop offset="0" stop-color="#b1aeaa"></stop><stop offset="1" stop-color="#d5d4cf"></stop></linearGradient></defs><path fill="url(#O)" d="M285 584c1 0 2 1 3 2l1 2c2 1 3 2 5 3l1-3 1-1c0-2-1-2-1-4-1 0-1-1-1-1-1-2 0 0-1-1l-1-2c-1-1-2-2-2-3-1 0 0 0-1-1s0-1 0-2l8 11c1 2 4 5 5 8 0 3 0 5 2 8-1 1-1 1-1 2-2-1-3-3-5-4-1-2-3-5-5-7-3-2-6-4-8-7z"></path><path d="M823 330c2 0 7-2 9-1h0c1 1 2 1 3 1l1 1c-6 1-12 3-17 6-4 3-8 8-12 12 0-4 2-7 5-10l5-6h1c2-1 3-1 5-3h0z" class="J"></path><path d="M69 533c3-3 8-5 13-4 1 0 1 1 2 1 1 3-2 7-4 9-1 3-3 5-4 8 0 1 0 2-1 3l-1 1v4h0c-1-1 0-3-1-4 0-8 5-13 9-19-6 1-10 2-15 5v-1c1-1 1-2 2-3z" class="D"></path><path d="M164 599c0-1 1-1 2-1 3 4 6 8 8 14 3 6 4 12 3 18l-1-1h0-1v-1c0-4-1-7-2-10s-3-6-5-9-3-7-4-10z" class="C"></path><path d="M173 618c1-1 1 0 1-1 0-2-3-6-4-7v-1-2l2 2v1l1 2h0 1c3 6 4 12 3 18l-1-1h0-1v-1c0-4-1-7-2-10z" class="B"></path><path d="M278 581h1l3 2 1-1 1 2h1c2 3 5 5 8 7 2 2 4 5 5 7-1 0-2 0-3-1l-1-1v1h-1c-3-1-8-3-12-3-2 0-4 0-6-1h0c2-1 3-1 5-1h5c0-1-1-1-2-2h0l-1-1c1 0 1 0 2-1-2-2-3-5-6-6v-1z" class="X"></path><path d="M284 584h1c2 3 5 5 8 7 2 2 4 5 5 7-1 0-2 0-3-1l-1-1v1h-1l-1-1c-2-1-4-3-6-5h0l6 4c-2-2-8-8-8-11z" class="J"></path><path d="M434 907c0 1 0 2 1 3l6 16v1c-2-6-5-9-9-13-2 6-2 13-8 16l-2 2h-1c-3 0-9 0-11-2-1 0-1 0-1-1v-1h0l2 1c4 1 9 1 13-1 3-2 3-9 4-13 1-2 2-2 3-3 1 0 2 0 3 1l1 1c0-2-1-5-1-7z" class="E"></path><path d="M853 703l1-1 2 1h0c4 7 3 12 11 15 6 2 13-1 19-3 0-1 3-2 3-2 1 1 2 1 2 1-6 4-19 9-26 7-8-3-9-11-12-18h0z" class="I"></path><path d="M512 249l1-1 2 5c0 8 3 17 1 25 0 7 1 15-1 22l-1-1v-22c0-4 0-8-1-12 0-2-1-5-1-7-1-1-1-3-1-4v-1c0-1 1-2 1-4z" class="M"></path><path d="M514 277h1c1 4 0 9 0 13 1-3 1-9 0-12h1 0c0 7 1 15-1 22l-1-1v-22z" class="N"></path><path d="M842 306c1 0 1 0 2 1v1 3c-2 5-5 14-10 17h-2-1c-1-1-1-1-1-2 1-4 3-7 5-10h2l4-7 1-3z" class="T"></path><path d="M844 308v3c-2 5-5 14-10 17h-2c5-7 9-13 12-20z" class="H"></path><path d="M135 445c1 0 2 2 2 3 1 3 3 5 4 8l1 1h2c4-1 7-1 11 1 1 1 2 2 2 3-4-2-10-2-15 0h0c-1 0-2-1-2-1h-1-2c-2 0-8 0-9 1l1 2h-1c-1 1-2 1-3 1-2-1-3-2-4-3l2-2c1-1 2-1 3-1 5 1 9 0 14 0l-5-13h0z" class="E"></path><path d="M747 586h1 0c10 5 23 7 31 15h-1l-3-1c1 2 4 3 4 5h0c-3-4-7-5-12-7l-13-5c-3-2-6-3-7-5v-2z" class="D"></path><path d="M244 630c9-8 19-8 29-5 2 1 5 2 7 2h1 1 0 0l1 3 5 4h0-1c-5-3-11-5-16-6h-4l-2-1c-7-1-13-1-19 3l-1 1-1-1z" class="B"></path><path d="M597 921h1c0-4 0-7 1-10 1-2 1-3 3-4 3 0 4 1 6 4h0l-1-1c-1 0-2-1-2 0l-1 1-1 1h-1v2c0 3-1 6-1 9 0 2 1 4 0 7-4 1-11 3-15 1-3-1-4-4-6-6 1 0 1 0 2 1h0l1-1c2 2 3 4 6 4 2 1 9 1 11-1 1-1 0-1 1-2-1-2-4-3-6-4 1-1 2-1 2-1z" class="E"></path><path d="M917 368l-2-2c-3 1-7 2-10 3 1-1 3-1 3-3l-1 1c-2 0-3 1-5 1h-10-1c-2 1-5 2-6 3-2 1-4 1-5 2l-5 3-2 2c-2 1-3 1-4 2 0 1-1 1-1 2l-1-1c0-1 3-3 4-4l10-6 2-1 2-1c3-2 7-4 12-6v1s-1 1-2 1h1c6 0 12 0 18-2l3 4v1z" class="Z"></path><path d="M724 109c0 2 0 4 1 6-4 4-10 13-15 15-2 0-2 0-4-2v-5h2c2 0 3-2 5-3 4-3 8-5 11-10v-1z" class="B"></path><defs><linearGradient id="P" x1="958.7" y1="583.686" x2="970.487" y2="613.74" xlink:href="#B"><stop offset="0" stop-color="#9c927e"></stop><stop offset="1" stop-color="#beb199"></stop></linearGradient></defs><path fill="url(#P)" d="M964 581c5 13 7 27 1 40l-1 2c-1 0-2-2-3-3-2-3-4-7-6-9v-1l3 1c2 3 4 6 6 10 3-12 4-27-2-38v-2h0l1 1 1-1z"></path><path d="M177 390c-8-4-16-10-23-16h0l4 2c5 3 11 6 16 9l5 4c5 3 10 6 15 8l13 8h0l-8-4-1 1c-1 0-2-1-3-2l-6-3c-5-1-9-2-13-4 1-1 1-1 1-3z" class="a"></path><path d="M177 390c2 1 11 6 12 7-5-1-9-2-13-4 1-1 1-1 1-3z" class="T"></path><defs><linearGradient id="Q" x1="195.337" y1="567.249" x2="174.379" y2="580.316" xlink:href="#B"><stop offset="0" stop-color="#908b7b"></stop><stop offset="1" stop-color="#b6ad97"></stop></linearGradient></defs><path fill="url(#Q)" d="M198 564a54.5 54.5 0 0 0-24 12c2 5 9 9 13 12-8-2-12-6-17-12 7-8 18-13 28-15v3z"></path><path d="M774 862h3c0 1 1 1 1 2-1 6-6 13-11 17h-1-1c0 1 1 1 2 2l2 2h0-2c-1-1-2-1-2-1-2 0-3-1-4-1v-1-1l2-1s1-1 2-1v-1c3-1 5-5 7-7v-1c0-1-1-2-2-3l-1 1c-1 0-3 0-4-1h-1v-1c1-1 0-1 0-2l1-1v3c1 1 1 1 2 1 1-1 1-3 2-4 2 0 3 0 5-1z" class="Y"></path><path d="M774 862l1 1c-2 2-4 4-8 4 1-1 1-3 2-4 2 0 3 0 5-1z" class="H"></path><path d="M97 209c4 2 15 2 17 7 1 2 1 4 0 5-1 4-5 6-8 7l-3 1h-8-1l-3-1c-5-2-9-5-11-9 0-2-1-4 0-6l1 2c2 3 4 7 7 9 4 3 11 4 16 3 3-1 6-3 7-5s1-4 1-5c-2-4-13-6-16-7l1-1z" class="B"></path><defs><linearGradient id="R" x1="572.979" y1="169.75" x2="564.83" y2="161.246" xlink:href="#B"><stop offset="0" stop-color="#0f0f0e"></stop><stop offset="1" stop-color="#2f2d26"></stop></linearGradient></defs><path fill="url(#R)" d="M575 149c2 4 1 8 0 12-1 2-1 4-2 7l-1 2v2 1c-1 1-3 2-4 3-2 2-5 2-7 2-2-1-3-2-4-4 0-2 0-3 1-5h1c-1 1-1 2-1 3l1 1c2 1 4 2 6 1 2-2 3-4 3-6l7-19z"></path><path d="M427 169c4 1 7 5 11 7l16 10c4 2 22 14 23 18-13-9-26-17-39-26-4-2-9-4-12-8l1-1z" class="M"></path><defs><linearGradient id="S" x1="712.638" y1="929.309" x2="696.783" y2="940.673" xlink:href="#B"><stop offset="0" stop-color="#151412"></stop><stop offset="1" stop-color="#615f5e"></stop></linearGradient></defs><path fill="url(#S)" d="M681 933c0-3 2-7 2-10 2 6 6 8 11 11 7 3 13 4 22 1l-5-8h1c1 1 1 2 2 3v-2c2 3 3 6 5 9-9 3-16 4-25 0-3-2-7-4-9-8l-3 6-1-2z"></path><path d="M289 573c-6-7-12-13-18-20l-7-7c-2-2-5-3-8-4 2 0 5 2 7 3 14 8 26 21 34 36 2 4 3 7 6 11h0-1c-1-3-4-6-5-8l-8-11z" class="B"></path><path d="M737 804l-1-1c-1-3-2-7 0-11 1-2 1-3 3-4 1 0 2 1 4 0 4-1 9-1 13 0v1c6 1 9 5 12 9 4 6 5 15 5 22h0c0-2 0-3-1-4 0-3-1-5-2-7-2-6-5-14-11-17-2-1-4-3-7-2h-1c-2-2-7-1-9-1-2 2-4 4-4 7 0 2 0 4 1 6-1 1-1 2-2 2z" class="I"></path><path d="M675 126c0-1 1-2 2-3 3-1 8-1 11-2 4-1 8-3 12-5s9-4 13-8c3-2 4-4 6-7 1 1-1 4-2 5-6 8-13 11-22 15-3 2-6 4-10 4h-8c-2 3-4 9-8 10l-1-1 7-8z" class="M"></path><path d="M261 81l-1-1c-2-2-3-5-5-6l-7-5 1-1c4 3 10 7 13 11V68c9 2 15 7 20 14 5 6 7 13 11 20 0 1 3 5 3 7v2c-2-3-4-6-5-9-3-7-7-17-12-23-4-4-10-7-15-9 1 2 2 5 2 7-1 2-1 2-2 3-1 0-2 0-3 1z" class="L"></path><path d="M957 640c2 14 2 31-6 42-4 5-7 7-13 9 2-2 5-4 7-6 6-5 8-13 10-20v-11-5c0-3 0-7 2-9z" class="K"></path><path d="M682 689h1 1l6-4v1c0 1 0 1-1 2l-1 2h-1c-1 1-2 1-3 2h-1v1c0 1 0 0-1 1v1 2s-1 1-1 2h0c0 2-1 8 0 9v1l1 1c-1 0-2-1-3-1v-1l-1 1-9-6 5-4c1 0 3-3 5-4 1-1 2-2 3-4h-2v-1l2-1z" class="T"></path><defs><linearGradient id="T" x1="155.591" y1="573.888" x2="149.197" y2="566.825" xlink:href="#B"><stop offset="0" stop-color="#807b6d"></stop><stop offset="1" stop-color="#9e998e"></stop></linearGradient></defs><path fill="url(#T)" d="M166 552h0c2 0 2-1 3-2 1 0 2-1 3-1l-5 5v1c-11 10-19 18-25 32-1 0-2 0-2 1l-1 1c4-15 14-29 27-37z"></path><path d="M263 583c2 0 5-1 7 0 1 0 2 0 3 1h1c1 0 1 0 2 1h0c1 0 2 1 2 1 2 1 3 1 4 3l1 1h0c1 1 2 1 2 2h-5c-2 0-3 0-5 1h0-1c-2-1-5-1-7-1h-1c-3 1-6 2-9 2h0v-1c1-1 3-1 3-2 1 0 1-1 2-1l1-1c1-1 2-1 3-1 3-1 7-1 10-1 0-1-1-1-2-2h-1c-1 0-2 0-3-1h-1c-3 0-7 1-9 0h0c1-1 2-1 3-1z" class="I"></path><path d="M263 589c1-1 2-1 3-1 3-1 7-1 10-1h1l1 1c-3 1-5 2-8 2-4 1-9 2-13 4v-1c1-1 3-1 3-2 1 0 1-1 2-1l1-1z" class="B"></path><defs><linearGradient id="U" x1="425.99" y1="900.065" x2="426.367" y2="917.383" xlink:href="#B"><stop offset="0" stop-color="#a49d91"></stop><stop offset="1" stop-color="#bcbaae"></stop></linearGradient></defs><path fill="url(#U)" d="M429 897l2 1c-1 1-1 0-1 1s1 3 2 4 1 2 2 3l-1 1h1c0 2 1 5 1 7l-1-1c-1-1-2-1-3-1-1 1-2 1-3 3h-1c-3 2-6 5-9 6h-1v-1l1-1c5-7 9-14 11-22z"></path><path d="M107 403c0 1-1 3-3 4-2 2-5 2-7 2-4 0-7-3-10-5-2-3-6-10-6-14l3 6c-1-3-2-5-1-8 1 3 2 6 4 9 0-4 0-7 1-10 1 0 0 6 0 7 1 3 2 5 3 7 2 2 4 4 7 4s5-1 7-3l1-1 1 2z" class="C"></path><path d="M538 176c9-9 24-16 24-30 0-4-1-7-3-10-4-3-9-4-12-8h0l-1-1c7 3 15 6 18 13 2 4 1 8 0 12-5 10-15 18-24 24h-2z" class="K"></path><path d="M406 910l-1-1-3 3h0c0-2 1-3 3-4 1 0 2 0 3 1 2 2 3 9 3 12l4-3 3 1-1 1c-3 2-7 3-8 6v2 1c-2 1-3 1-4 2h0-2c0-5 0-10 1-16 1-2 1-3 2-5z" class="J"></path><path d="M406 910c1 0 1 0 2 1 0 2 0 3-1 4h-3c1-2 1-3 2-5z" class="C"></path><path d="M405 931c0-6 1-11 3-16v10l1 1v2 1c-2 1-3 1-4 2h0z" class="F"></path><path d="M824 86l-13 4c-2 1-5 2-7 2-2-1-2-1-3-2v-4c2-2 4-3 5-5 5-3 11-5 16-8l8-4c1-1 3-2 4-3h4c-10 8-27 11-34 22 7 0 12-2 18-3l2 1z" class="D"></path><path d="M847 640h2c3 12 5 22 17 28 2 1 5 2 8 3 2-1 3-1 5-3h-3c-1 0-1 0-1-1h0c5 1 8-1 13-2l2 2c-6 1-9 5-14 6-6 1-10-2-15-5-9-6-12-17-14-28z" class="E"></path><path d="M693 704l1-2 3 6c7 8 13 20 25 21 2 0 6-1 8-3h0c-1-2-2-2-3-3 1 0 2 0 3-1l4 4c-2 2-5 4-8 5-4 1-8 1-11-1-8-4-17-14-20-22l-2-4z" class="L"></path><defs><linearGradient id="V" x1="929.909" y1="430.445" x2="938.993" y2="422.899" xlink:href="#B"><stop offset="0" stop-color="#a99f85"></stop><stop offset="1" stop-color="#c5bcab"></stop></linearGradient></defs><path fill="url(#V)" d="M912 410c9 0 18 1 24 8 6 6 8 14 8 21-1 7-2 14-7 19h0-1c3-5 4-8 5-13 1-7 0-16-5-22h0c-4-5-9-9-15-10l-1-1h-10l3-1-1-1z"></path><path d="M581 915h1l1 1 9 6v-1h0 0c0-1-1-1-1-2h0c1 1 3 2 4 3 2 1 5 2 6 4-1 1 0 1-1 2-2 2-9 2-11 1-3 0-4-2-6-4l-2-10z" class="O"></path><path d="M297 749c1-5 4-10 6-14 2-2 7-8 11-9-1 2-2 3-4 5v1c-7 10-12 19-10 32 0 3 1 5 2 8s0 6 0 9c-1-1 0-3-1-5l-1 1c-1-5-3-10-3-15-1-4 0-9 0-13z" class="K"></path><path d="M297 749c1 2 1 4 1 6s0 5-1 7c-1-4 0-9 0-13z" class="X"></path><path d="M399 125h1c1-2 1-5 2-7 5 1 9 2 14 4 1 1 2 1 4 2 1 1 2 2 4 3l9 9h0 0c-2-1-4-4-7-5l-6-4-9-4-7-3v16h-7c0-1-1-1-1-2v-1l-3-2h4c1-2 2-3 1-5l1-1z" class="F"></path><path d="M766 82c7-4 13-9 20-13 9-5 19-7 29-8 5-1 9-1 14 0 1 1 3 1 4 2-18 0-34 1-50 10-5 3-10 7-15 9-3 1-5 1-7-1 1 0 3 0 5 1z" class="C"></path><path d="M417 920v1h1c3-1 6-4 9-6h1c-1 4-1 11-4 13-4 2-9 2-13 1l-2-1h0v-2c1-3 5-4 8-6z" class="O"></path><path d="M264 80c-1 1-2 2-3 2h-4c-4-1-8-5-11-7-5-3-9-5-14-7-10-3-20-6-31-6-2 0-4 1-7-1h1c10-5 34 2 43 8 8 4 14 10 23 12 1-1 2-1 3-1z" class="C"></path><defs><linearGradient id="W" x1="928.34" y1="407.566" x2="926.373" y2="399.476" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#252421"></stop></linearGradient></defs><path fill="url(#W)" d="M918 398h0c0 3 0 5 3 7 1 1 3 1 4 1 3 0 5-2 6-4 3-3 4-8 3-13-1-8-6-18-12-25h1c5 6 11 16 12 24 1 3 1 5 1 8 1-1 2-2 3-4 0-2 1-7 2-8 1 6-3 16-7 21-2 3-6 4-10 5-2 0-3 0-5-2s-3-4-4-8h1c1-1 1-2 2-2z"></path><path d="M817 397l1-1 1-1c5-3 11-5 16-6v3c-4 1-8 4-10 7l-4 2-15 11-2-1-1 1h-1l2-2c3-5 9-8 13-13z" class="G"></path><path d="M804 410c1 0 3-2 5-3 3-3 8-6 12-6l-15 11-2-1-1 1h-1l2-2z" class="S"></path><path d="M730 98l1-2c2-3 3-6 5-9 4-5 10-13 16-16 3-1 6-2 9-2v9c3-3 5-4 9-6 6-3 12-7 18-10l1 1-6 3c-5 3-11 6-15 9-2 2-4 3-5 4 1 1 1 2 2 2l1 1c-2-1-4-1-5-1l-2-1c-2 2-4 5-7 6h0l6-6v-1c-1-3 1-6 1-9-3 1-5 2-7 3l-1 1c-2 0-3 2-5 3-7 8-11 17-15 26l-1-5z" class="K"></path><path d="M840 389l13-7c1-1 4-3 6-3-2 2-5 3-7 4l-1 3c-1 1-1 1-1 2l1 1 8-1c6-1 12-1 18-1-4 1-7 1-11 1l-17 3c-2 1-5 2-7 3v1c-3 1-6 1-8 2-3 0-6 2-9 2h0c2-3 6-6 10-7 1-1 3-2 5-3z" class="W"></path><path d="M846 391c-4 2-10 3-14 3l3-1c1-1 2-1 3-1l1-1 2-1c2 1 3-1 5 1z" class="X"></path><path d="M852 383l-1 3c-1 1-1 1-1 2l1 1-5 2c-2-2-3 0-5-1l11-7z" class="T"></path><path d="M737 904c3 2 8 5 11 5 2 0 3 0 4-1h0v-1c-2-1-2-4-4-6-1-3-3-5-5-8 5 3 8 8 10 13 1 1 5 0 6 1-3 7-8 13-16 16-7 2-14 2-21 0l1-1c5 1 9 1 13 0 8-2 17-8 22-15l-7 3c-5 2-11-2-16-4l2-2z" class="L"></path><path d="M692 722l6 3c7 4 12 13 14 21 2 6 2 13 0 20-1 5-2 9-2 13h0c-1 0 0 0-1 1h0-1c0-2-1-4 0-6 1-8 4-14 3-23-3-12-10-21-20-28l1-1z" class="K"></path><path d="M863 226l1-1 1 1h0c-3 11-2 23-2 33l2 42c0 4 1 8 2 12 0 2 1 4 1 6-2-3-4-6-4-9-2-8-2-17-2-25v-32c0-8-1-16 1-24v-2-1z" class="B"></path><path d="M65 645c0-7 1-13 3-20 1 2 1 5 0 7 0 3-1 7-1 10l1 1c-1 14-1 29 9 41 2 3 6 4 9 7-7-2-11-4-15-11-4-6-5-13-6-20-1-5-1-10 0-15z" class="K"></path><path d="M65 660c-1-5-1-10 0-15 1 4 1 9 1 13l-1 2z" class="X"></path><defs><linearGradient id="X" x1="753.47" y1="624.023" x2="753.51" y2="629.371" xlink:href="#B"><stop offset="0" stop-color="#121111"></stop><stop offset="1" stop-color="#3d3c35"></stop></linearGradient></defs><path fill="url(#X)" d="M737 624l-4 6c3-1 5-3 7-4 7-3 19-4 26-2 6 1 12 5 16 10 0 1 1 2 1 3h0c-1-1-2-3-4-4-5-6-12-6-20-6 2 3 4 6 5 9-1 1-1 1-2 1-2 1-5-1-6-2h2s1-1 2-1h1 0l-2 1h-1v1h5v-1c-1-1-1-2-2-3s-2-3-3-5h-11-1c-5 2-11 4-16 7v-1l1-1c0-2 0-4 1-6v4h0l1-1c0-1 2-3 2-3 1-1 1-2 2-2z"></path><path d="M434 138c1 0 2 2 3 3 3 3 5 7 7 11 2 7 3 16 9 21 2 1 4 1 7 1 1-1 2-1 2-3 0-1 0-1-1-2l-1-2v-1l2 2c1 1 1 2 1 4 0 1-1 2-2 3-2 2-5 3-7 2-13-2-11-19-14-28-1-4-3-7-6-11h0z" class="B"></path><path d="M911 494c-1-1-3-2-3-3h3 1c3 1 7 3 10 5 11 6 25 16 31 27 3 4 4 10 3 15h0l-1-1h1c-1-1-1-2-1-2l-1-1v-1c0-1 0-3-1-4-3-13-20-23-31-29-3-2-7-4-10-6h-1z" class="K"></path><path d="M233 420c-8-18-26-36-41-49h0c14 10 26 21 35 36l15 29c-1-1-2-1-3-1-3-5-10-8-13-14h1c-3-3-7-6-10-9 3 1 5 3 7 5 3 3 8 7 11 9l-2-6zm684 6c-2-2-6-3-9-5-5-2-7-4-10-9 0-1-1-3-2-4-1-5-3-10-3-16 3 0 2-3 3-4v1c-1 4 0 10 2 13 4 4 8 7 13 8h1l1 1-3 1c-1 1-3 3-4 5 4 4 10 5 14 7l-1 1h-1v1h-1z" class="D"></path><defs><linearGradient id="Y" x1="334.021" y1="689.56" x2="311.941" y2="681.04" xlink:href="#B"><stop offset="0" stop-color="#dad8d4"></stop><stop offset="1" stop-color="#fefdfe"></stop></linearGradient></defs><path fill="url(#Y)" d="M312 678l-1-2v-1l1 1v-1c1 0 2-1 2 0h1l1 1 2 2 2 1c1 0 2 1 2 1 1 1 2 1 2 2 2 3 6 4 8 7v1c0 1 1 3 2 4-1 1-1 1-1 2h-2v1l-3-3h-1c0-1-1-1-2-1-1-1 0-1-1-1l-1-1c-1 0-1 0-2-1v-1l-1 1h-1c-1 1 0 3 0 5h0c-1-2-1-4-1-6l-6-11z"></path><path d="M312 678c3 3 5 7 9 9v1c-1 0-2 1-3 1l-6-11z" class="E"></path><defs><linearGradient id="Z" x1="383.427" y1="923.377" x2="361.462" y2="924.549" xlink:href="#B"><stop offset="0" stop-color="#010101"></stop><stop offset="1" stop-color="#2d2d25"></stop></linearGradient></defs><path fill="url(#Z)" d="M382 901v1 3 1h1c1-1 1-2 1-3l1 1c-3 7-4 14-5 22v-2c-1 1-1 2-1 3-1-1 0-1-1-2h0 0l-1-5-10 19-5 9c-1 0-1-1-2-1l3-7 19-39z"></path><path d="M154 693h1c3 1 6 3 8 5 3 1 6 2 9 4h0c-3 3-4 7-6 10-2 5-7 8-12 9-4 0-7 0-10-1h-1c-7-2-13-5-19-9-2 0-5-2-6-3 0-1 0-1-1-2h1 2c1 1 4 3 5 3 7 4 20 11 28 9 10-2 11-8 15-15-4-1-7-3-10-6-2-1-3-2-5-4h1z" class="I"></path><path d="M835 544c-1 0-4-4-5-5 4 1 8 5 12 5 5 2 9 6 14 9 11 9 21 19 26 32 1 3 2 6 2 9v1 1c0 1 0 2-1 2-3-13-9-24-19-34-7-7-14-12-23-17-1-1-4-3-6-3z" class="R"></path><path d="M66 567c1 0 2-1 3-1 1-1 2-1 3 0l-5 8h0l-1-1c-5 4-10 20-11 26s0 15 4 20c1-2 2-4 3-7h1c1-3 1-5 2-7l1-3h1l-1 4c0 1 1 2 1 3l-1 2h1l-9 11c-3-5-5-10-5-16 0-11 3-22 8-32 2-2 3-5 5-7z" class="K"></path><path d="M66 606c0 1 1 2 1 3l-1 2-4 4 4-9z" class="O"></path><path d="M822 85c16-4 30-5 46-3 1 0 3 1 4 1 7 3 12 6 17 11 2 2 4 5 6 6-1 1-1 1-2 1h-2c-5-4-9-9-14-12-15-9-37-7-53-3l-2-1z" class="C"></path><path d="M960 586v-1-4h0l1 1 1 1c6 11 5 26 2 38-2-4-4-7-6-10l-1-1v-7l1-2c1-1 2-2 2-3 1-2 0-9 0-12z" class="Q"></path><path d="M411 851c1 1 2 2 2 4-4 5-7 10-11 16l-12 22c-1 2-4 10-5 11l-1-1c0 1 0 2-1 3h-1v-1-3-1c0-2 2-5 3-7 4-8 8-15 13-23l13-20z" class="E"></path><path d="M710 680c1 0 1 1 2 2-3 3-5 6-4 11 1 7 5 13 10 16 4 3 7 4 11 4 1 1 1 1 2 1-1 3-4 4-6 5l5 3c-1 1-2 1-3 1l-6-4h-1c0-2-2-3-4-4-3-3-9-10-9-13s-1-4 1-5c-1-2-1-3-2-5-1 1-1 2-2 3 0-1 0-2-1-3 2-5 5-9 7-12z" class="P"></path><defs><linearGradient id="a" x1="714.502" y1="699.045" x2="717.251" y2="717.771" xlink:href="#B"><stop offset="0" stop-color="#9f9b8e"></stop><stop offset="1" stop-color="#cec8b9"></stop></linearGradient></defs><path fill="url(#a)" d="M708 697c3 8 9 14 17 17 1 1 2 1 3 1h0c-2 2-4 2-6 4h-1-1c0-2-2-3-4-4-3-3-9-10-9-13s-1-4 1-5z"></path><path d="M642 871c3 2 6 6 9 6 4 4 8 7 13 10 2 1 4 3 6 3l4 2c3 2 6 3 9 5 9 6 19 10 29 14h0c-17-5-36-11-51-22l-11-8c-2-1-4-4-7-5h0l-3-3 1-1 1 1v-2z" class="B"></path><path d="M66 602l-1 3c-1 2-1 4-2 7h-1c-1 3-2 5-3 7-4-5-5-14-4-20s6-22 11-26l1 1-3 6c-2 4-4 13-3 18 0 1 1 1 1 2h1c1 1 1 1 2 1l1-1v2z" class="V"></path><path d="M416 861c1-1 1-1 2-1v-2h0l9 20c1 4 3 9 5 13l-1 1v-1 6 2l1 1v1 2c-1-1-2-3-2-4s0 0 1-1l-2-1c0-2-1-4-2-5l-1 1-1-1v-1c-1-1-1-1-1-2v-3l-1-1c0-1 1-1 1-2h-1v-2-1c-1-1-2-5-2-5l-1 1v1c-1 1-1 1-1 2v1c0 1-1 2-1 3-1 1 0 1 0 2l-2 2c0 1 0 2-1 3v-1l3-15c1-4 1-6 1-9l-10 25h-1l8-22v-1h1c0-3 0-4-1-6z" class="R"></path><path d="M416 887l5-17 5 17c1 2 1 3 1 5l-1 1-1-1v-1c-1-1-1-1-1-2v-3l-1-1c0-1 1-1 1-2h-1v-2-1c-1-1-2-5-2-5l-1 1v1c-1 1-1 1-1 2v1c0 1-1 2-1 3-1 1 0 1 0 2l-2 2z" class="F"></path><path d="M293 708l1 1 3-3c3-4 6-8 9-11 2 4 2 5 1 9v1c-2 1-2 2-3 3s-2 2-2 3c-1 2-3 3-5 3-1 1-1 2-2 2v1c-1 1-3 2-4 2-2 1-3 1-4 2l-2 1c-2 1-3 1-4 2l-2-1 6-3-6-6c5-2 9-3 14-6z" class="K"></path><path d="M291 719c-1-1-1 0-2-1h-2c-1-1-2-1-3-1v-2c10-1 13-8 20-14-1 3-3 5-2 8h0c-2 2-3 4-5 5-1 1-1 2-2 2v1c-1 1-3 2-4 2z" class="M"></path><path d="M136 284l1 1c-4 4-8 7-14 9-2 0-5 1-8 1-4 1-10 0-12 4-1 1-1 1 0 2 2 2 7 4 8 7-2 1-5 2-7 2-3 2-7 5-9 7l-1-1c3-2 5-5 8-7-2-2-4-3-5-5s-1-3 0-4c2-5 7-7 11-9 1 1 4 0 5 0 4 0 7-1 11-2 5 0 8-2 12-5zm491 577c1-3-2-4-3-8 3 3 7 8 10 10l8 8v2l-1-1-1 1 3 3h0c7 9 14 15 23 21h0c-5-2-9-5-13-8l-9-9c1 3 2 5 4 6l2 1-1 2 1 2c0 4 3 9 7 12h1l1 2c1 2 1 3 2 4v2l-34-50z" class="D"></path><path d="M264 570l-15-13c-11-8-26-15-40-16 2-1 7-1 10 0 11 1 25 7 34 15 4 3 7 6 11 9 5 3 11 6 15 11 0 1 1 1 1 2-6-1-11-5-16-8z" class="E"></path><path d="M639 96h0c-1-1-1-1-1-2-2-9-9-16-17-21l-3-2c6 0 12-1 17-1h1c5 1 10 5 13 9s4 8 5 13h0c1 10-2 19-5 28l1 1h-1c-2 1-3 2-5 3v-1l-2 1c-1-1-1-1-1-2 1-3 4-6 6-8 2-3 3-6 4-9 2-8 1-18-3-25-2-4-6-7-11-8h0c-5-1-10 0-15 0l5 3c7 3 12 10 14 17v2c1 4 0 7-1 11 0-2 0-7-1-9z" class="I"></path><path d="M814 444h4l1-1c2-4 4-7 8-10h0c-3 3-5 6-6 10v1l29 2c2-3 5-5 7-7 4 2 8 4 13 6-7 1-12 1-17 5-2 3-3 5-4 9l-1 2c-1-3 0-4 1-7-6 0-12-1-18-1-9 0-17 2-26 4v-1c3-1 7-2 10-2l21-3h11c1 0 2-1 2-1l1-1c-2-1-4-2-5-2-4 0-8-1-13-1h-17 0l3-1h-4v-1zM377 864c-1 3-4 5-5 8-1 2-4 4-4 5-10 8-20 14-30 20-10 5-21 9-32 12-4 1-10 4-14 3h1c12-3 25-9 37-15 14-8 28-15 40-26l7-7z" class="D"></path><path d="M371 117c3 1 3 5 6 4 4 5 10 7 16 10l3 2v1c0 1 1 1 1 2h-20-7v-3c-2-3-3-9-3-12 2 0 6 2 8 3v-1c0-1-1-1-2-2l-2-4z" class="Q"></path><path d="M371 117c3 1 3 5 6 4 4 5 10 7 16 10l3 2v1c-8-2-15-5-21-10v-1c0-1-1-1-2-2l-2-4z" class="L"></path><path d="M174 385h1 1l1 1 1 1s1 1 2 1l-1-1v-1c1 1 2 2 4 3 1 0 1 0 2 1h1l-3-3c-2-2-4-4-5-6l1-1c3 4 6 7 10 10l5 3c0 1 1 1 2 2h1c1 1 3 2 4 3s2 1 3 2l4 2h2c0 1 0 1 1 1h1c1 1 2 1 3 1 4 2 6 7 9 10 1 0 2 2 3 2s2 0 2 1l2 3h2l2 6c-3-2-8-6-11-9-2-2-4-4-7-5 3 3 7 6 10 9h-1l-13-10c-5-4-10-7-15-9l1-1 8 4h0l-13-8c-5-2-10-5-15-8l-5-4z" class="O"></path><path d="M179 389c2 0 2 0 4 1 1 1 3 1 5 2h1c2 1 4 3 5 5-5-2-10-5-15-8z" class="F"></path><path d="M331 682c5 3 10 8 14 13h0c-2-2-1-3-2-5l-1-2h2v1l2 2v1h1c2 4 4 9 5 14h0c-6 2-13 3-19 5l-2-1c3-2 6-4 10-6h0l-4-4c-2 0-2-1-3-2-1 0-2-1-3-2h2c0-1 0-1 1-2-1-1-2-3-2-4v-1c-2-3-6-4-8-7l4 3h2 1v-3z" class="J"></path><path d="M332 689c4 4 10 8 13 13-2 1-4 0-5 0l-2-1c-1-1 0-1-1-1-2 0-2-1-3-2-1 0-2-1-3-2h2c0-1 0-1 1-2-1-1-2-3-2-4v-1zm-1-7c5 3 10 8 14 13 0 2 2 4 3 5-8-4-13-10-20-15h2 1v-3z" class="S"></path><path d="M123 340c-4-3-6-7-8-10 2 2 4 4 7 6s8 3 11 6c5 4 8 10 13 14 3 4 8 7 12 10 2 1 4 3 6 5h-1c-4 1-13-3-17-5-3-3-7-6-10-10-4-5-6-12-12-15l-1-1z" class="E"></path><path d="M850 238c10-18 30-36 51-42-2 1-2 2-4 2-1 1-2 2-4 2l1 1c-3 1-5 2-7 4-2 1-3 1-4 3h0c0 1-1 1-1 2-2 2-5 3-6 5 0 5 0 11 1 15v1l2 6h0l2 5c1 3 3 6 5 9 0 1 1 2 2 3h0c-3-1-5-3-6-6-1-1-1-2-2-4l-3-6c-1-3-1-6-2-9-1-4 0-9 0-13-4 3-8 6-10 10h0l-1-1-1 1v1c-4 4-8 10-10 15-1 1-4 4-4 6l-3 4c0-2 0-3 1-5 1-1 3-5 4-7l-1-2z" class="L"></path><path d="M851 240c3-2 6-6 8-9 1-2 2-4 4-5v1c-4 4-8 10-10 15-1 1-4 4-4 6l-3 4c0-2 0-3 1-5 1-1 3-5 4-7z" class="R"></path><path d="M225 104c15 0 29 2 43 8l12 6c-8-3-16-6-25-7-18-4-36-4-54 0-9 3-18 6-26 11a79.93 79.93 0 0 0-13 13h-1c5-6 11-12 18-17 13-9 30-12 46-14z" class="C"></path><path d="M538 176h2c-7 6-15 14-20 22l1 3-1 1h-1 0l-1 1v1 1c-1 1 0 1-1 2l1-1c0 4-1 7-1 10-1 2 0 4-1 5l-2 5h0c-1 1-3 1-5 1-1 0-2-1-3-2-3-5-1-10 0-15-1-7-6-14-11-19h1l14 19c7-15 16-24 28-34z" class="E"></path><path d="M516 221c0-2 0-5-1-8-1-5 2-10 5-15l1 3-1 1h-1 0l-1 1v1 1c-1 1 0 1-1 2l1-1c0 4-1 7-1 10-1 2 0 4-1 5z" class="N"></path><defs><linearGradient id="b" x1="178.649" y1="75.174" x2="174.71" y2="104.035" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#24231f"></stop></linearGradient></defs><path fill="url(#b)" d="M210 79l-23-14h1c2 0 7 3 8 4 7 4 15 8 22 12 2 2 4 4 5 6 0 2 0 2-2 4-5 1-13-3-19-5-24-6-56-9-71 15l-2 3v-3h1l-2-2c6-8 13-14 22-17 2 0 3-1 5-1 10-1 21-1 32 0 8 1 16 3 24 5 2 1 5 1 7 1v-1c-2-3-5-5-8-7z"></path><path d="M753 520l24-8c19-5 39-8 59-8v3c-3-1-7 0-10-1-3 2-6 2-9 2-2 0-3-1-4 0-2 0-4 0-5 1-2 0-3-1-4 1v1h1c1 0 1-1 2-1 2 1 3 0 5 0h4c-10 2-20 2-29 4-18 2-35 9-51 18l-1-1-2 2c-1 1-2 1-3 1 0-2 1-2 2-3l21-11z" class="P"></path><path d="M735 531c2-1 4-3 7-4 4-2 7-4 11-5 17-7 35-12 53-14 7-1 13-2 20-2-3 2-6 2-9 2-2 0-3-1-4 0-2 0-4 0-5 1-2 0-3-1-4 1v1h1c1 0 1-1 2-1 2 1 3 0 5 0h4c-10 2-20 2-29 4-18 2-35 9-51 18l-1-1z" class="Z"></path><path d="M954 578c-4-7-8-13-13-20-2-3-8-9-12-10l-2 1h0c1-2 1-2 3-3 7-1 12 3 17 7-1-8-5-12-9-19-1-1-2-2-1-4 1 0 2-1 3-1 5 0 11 2 14 5l1 1s0 1 1 2h-1c-5-3-9-4-14-5 3 5 7 11 7 17 1 2 0 4 1 6 0 2 3 5 4 7v1h0c1 1 1 2 1 2 2 2 3 5 3 7h-2v1 5h-1z" class="B"></path><path d="M955 573c-2-1-3-3-4-5-2-4-4-7-6-11 3 1 5 3 8 6h0c1 1 1 2 1 2 2 2 3 5 3 7h-2v1z" class="R"></path><defs><linearGradient id="c" x1="377.082" y1="884.166" x2="354.462" y2="889.039" xlink:href="#B"><stop offset="0" stop-color="#454343"></stop><stop offset="1" stop-color="#5f615a"></stop></linearGradient></defs><path fill="url(#c)" d="M372 872v1c0 1 0 2-1 3 1 0 2-1 3-1 1-1 1-1 3-1-5 9-9 18-17 25h-1c-3 3-5 6-8 8-1 2-3 5-5 5-3 3-9 10-10 14l-1 1c0-2-1-4-1-5-1-1-1-2 0-3l-2-2h1c3-3 8-6 11-8 6-5 13-11 18-17-2 0-8 6-9 7-9 7-17 12-26 18l-1-1c16-10 33-20 43-37-4 3-7 7-11 10-5 5-11 9-17 13l-1-1c9-6 16-11 23-18 2-2 4-4 5-6 0-1 3-3 4-5z"></path><path d="M333 917c4-1 8-5 12-7l3-3c-1 2-2 4-2 5-3 3-9 10-10 14l-1 1c0-2-1-4-1-5-1-1-1-2 0-3l-2-2h1z" class="O"></path><path d="M810 367c-12 16-21 33-27 51v2h1l19-28c6-8 15-14 23-20 5-3 11-7 17-10v1c-6 4-12 7-17 11-12 8-22 19-30 30l1 1 3-3 1-1 1 1h-1l-6 9c-1 1-2 3-3 4l1 1c3-4 5-9 9-12 0 3-4 7-6 9l-3 6-1 3c3-4 6-7 10-9h3l-12 12-1-1-2 2h0l-1-1-2 3c0 1-2 2-3 3 0-1 0-1 1-2s2-3 3-5h-1v1c-1 2-2 3-4 4h0-1l1-1-1-1 2-5c-2 2-3 4-4 6 0-1-1-2 0-3h-2v-1c2-1 3-4 3-6 1-4 3-8 5-11 6-15 14-29 24-41v1z" class="W"></path><path d="M796 404l1 1c-1 4-5 8-8 11 1-2 2-6 4-8l3-4z" class="N"></path><path d="M316 618c1 0 3 1 4 2v2l3 5c1 3 0 0 1 2h0c0 2 1 3 2 5l1-1 16 39c0 2 1 3 1 4s0 1 1 3h-1 0v2h0c-1-2-2-2-4-4-1-2-2-4-4-6-1-2-4-7-7-8l-11-14 1-1c2 1 3 4 5 6l3 4 1-1 1 1v-1h2 0l-1-3v-2c-1-1 0-1-1-2 0 0-1-1-1-2v4c-3-4-7-8-9-12l1-2h2l-2-7v-2c-1-3-2-8-4-10v-1z" class="I"></path><path d="M336 671v-2l1-1h1c1 1 1 2 2 4 0 1 1 2 2 3h1l-1-2 1-1c0 2 1 3 1 4s0 1 1 3h-1 0v2h0c-1-2-2-2-4-4-1-2-2-4-4-6z" class="L"></path><path d="M322 638l1 1h0v-2c0-2 0-3-1-4l1-1c2 5 5 11 5 16v4c-3-4-7-8-9-12l1-2h2z" class="O"></path><path d="M893 340h0c6-2 10-6 14-10 3-4 5-8 8-11-2 8-6 16-12 21l-2 2a79.93 79.93 0 0 0-13 13c-8 10-18 13-30 14l-1-1c0-1 0-1 2-2 3-2 6-3 9-4 8-3 13-9 18-16 2-2 4-4 7-6z" class="J"></path><path d="M862 135c-5-5-10-10-16-14-8-4-16-7-25-9-11-2-23-4-34-3-12 0-24 2-35 5-6 2-12 5-19 6 20-10 42-17 64-16 9 1 18 3 26 6 11 3 22 7 31 15 4 3 7 6 10 10h-2z" class="E"></path><path d="M504 248l3 1h5c0 2-1 3-1 4v1c0 1 0 3 1 4 0 2 1 5 1 7 1 4 1 8 1 12v22l1 1h1c-2 1-3 2-5 3-3 0-5-2-7-3v-3c0 1 1 1 1 2h1c-1-6 0-12 0-18v-19c0-4 0-9-2-13v-1z" class="G"></path><path d="M507 249h5c0 2-1 3-1 4-2 0-3 0-4-1s0-2 0-3z" class="I"></path><path d="M774 839l1 1c-1 8-2 15-6 23-1 1-1 3-2 4-1 0-1 0-2-1v-3l-1 1c-2 2-5 0-8 1h-1c-1 0-1 1-2 2h0v1c-2 1-3 3-4 5l-1 1-3 3c-2 1-3 2-4 3 1-2 3-4 4-6l-1-1c4-5 7-10 10-15 1-2 3-4 2-7 3-3 6-5 8-8h1l6-3h0c1-1 2-1 3-1z" class="M"></path><path d="M774 839l1 1c-1 8-2 15-6 23-1 1-1 3-2 4-1 0-1 0-2-1v-3h1c6-5 7-17 8-24z" class="E"></path><path d="M754 858c1 0 1 1 2 2l3-6c1-1 1-3 2-4h1c-3 6-9 19-14 21l-3 3-1-1c4-5 7-10 10-15z" class="L"></path><path d="M764 843h1l6-3c-1 1-2 2-3 2-1 1-3 2-3 3-1 1-3 5-3 5h-1c-1 1-1 3-2 4l-3 6c-1-1-1-2-2-2 1-2 3-4 2-7 3-3 6-5 8-8z" class="U"></path><path d="M650 887c1 2 2 3 4 4s4 4 6 6l3 2c-1 1-1 1-2 1l-1-1-3-3-2-1v-1l-1 1h1c2 1 3 4 5 6h0c2 2 4 4 6 5l2 2c3 3 7 5 10 8l3 3 1-1 1 1c1 0 1 0 1 1h1c0 1 0 0 1 1h0l2 1c0 1 1 1 1 2h0-1 0c-2-1-3-2-4-1h-1c0 3-2 7-2 10l-1 2h-2-2c-1-1-4-6-4-8-2-1-3-4-4-6l-7-10v-2c-1-1-1-2-2-4l-1-2h-1c-4-3-7-8-7-12l-1-2 1-2z" class="G"></path><path d="M673 926l5 9h-2c-1-1-4-6-4-8h0l1 2c1 1 1 0 1 1v-1c0-1-1-2-1-3h0z" class="B"></path><path d="M650 891c3 3 5 6 8 9 4 5 9 8 14 12h-1c-2-1-5-3-7-4-2-2-4-5-6-5h-1c-4-3-7-8-7-12z" class="P"></path><path d="M659 905c5 4 7 9 11 14 1 2 3 5 3 7h0c0 1 1 2 1 3v1c0-1 0 0-1-1l-1-2h0c-2-1-3-4-4-6l-7-10v-2c-1-1-1-2-2-4z" class="H"></path><path d="M843 492c11-1 21 0 32 3 17 5 40 16 49 32 1 1 2 2 1 3-8-11-19-17-31-23-11-4-22-8-33-10-13-2-26-2-38-1-5 0-10 0-15 1l-11 2c-1 0-3 1-5 1 17-4 33-7 51-8z" class="E"></path><defs><linearGradient id="d" x1="700.171" y1="710.961" x2="729.901" y2="718.28" xlink:href="#B"><stop offset="0" stop-color="#b7b0a1"></stop><stop offset="1" stop-color="#dddbd3"></stop></linearGradient></defs><path fill="url(#d)" d="M703 692c1 1 1 2 1 3 1-1 1-2 2-3 1 2 1 3 2 5-2 1-1 2-1 5s6 10 9 13c2 1 4 2 4 4h1l6 4c1 1 2 1 3 3h0c-2 2-6 3-8 3-12-1-18-13-25-21l-3-6-1 2v-1c0-1 0-2 1-3h3c1 0 1 1 2 0 1-3 3-6 4-8z"></path><path d="M703 692c1 1 1 2 1 3l-1 2c-1 3-1 5-2 8 0 0-1 0-1-1-1-1-1-2-1-4 1-3 3-6 4-8z" class="H"></path><path d="M697 700c1 0 1 1 2 0 0 2 0 3 1 4v2l-3 2-3-6-1 2v-1c0-1 0-2 1-3h3z" class="O"></path><path d="M704 695c1-1 1-2 2-3 1 2 1 3 2 5-2 1-1 2-1 5s6 10 9 13c2 1 4 2 4 4-6-4-14-12-16-19v-2l-1-1 1-2z" class="R"></path><path d="M235 839c3 0 4 1 6 3 2 0 3 1 4 1h1c1 2 2 4 4 5l3 2h0l-1 1v1c1 1 1 2 1 3 1 4 5 7 6 11 3 4 6 7 10 11v1c2 2 4 3 6 5l1 1 1 1c6 5 17 9 24 10l10 1c3 1 6 0 8 1-1 1-3 1-5 1h-1l-1 1h-5 5v-1h-1c-2-1-2-1-3-1-1-1-4 0-5 0-1-1-1-1-2-1h-2c-1-1-1-1-2-1h-1c-2-1-3-1-5-2-1 0-1 0-2-1h-1c-2-1-3-1-4-2-1 0-1-1-2-1s-2-1-3-2c-2 0-2 0-3-1-1 0-1-1-2-1-1-1-1-1-2-1l-2-1-4-3c-2 0-3-1-4-3s-3-3-4-5-3-4-5-6v-1h-3l-1-1h-2c-1-1-1-1-3-1l-1-1h-1 0c-6-6-5-15-7-23z" class="T"></path><path d="M241 842c2 0 3 1 4 1h1c1 2 2 4 4 5l3 2h0l-1 1v1c1 1 1 2 1 3 1 4 5 7 6 11 3 4 6 7 10 11v1c-4-2-8-6-11-10s-7-9-9-14c-1-2-3-4-3-6-1-1 0-2 0-2-2-2-3-3-5-4h0z" class="K"></path><path d="M250 848l3 2h0l-1 1v1c1 1 1 2 1 3 1 4 5 7 6 11-3-2-5-5-7-8l-2-4v-1c-1-1-1-2-1-4l1-1z" class="L"></path><path d="M934 623h0c1-2 1-4 2-6h0v5h1l-3 15c-6 22-18 40-35 55-5 4-10 7-14 10-4 2-8 5-12 5 0 1 0 1-1 2h-2-1c-1-1-1-1-2-1h0l-1-1c1-2 1-1 3-2 0 0 1-1 2-1v-1c1-3-1-5 2-7h1c3 0 7-1 10-2h1c5-2 10-5 15-8 13-9 23-26 28-41 2-5 4-10 4-15 1-2 1-5 2-7h0z" class="R"></path><path d="M874 696h0c11 1 23-7 32-13-4 4-7 8-11 11-5 4-10 6-15 9-2 2-5 3-7 4 0 1 0 1-1 2h-2-1c-1-1-1-1-2-1h0l-1-1c1-2 1-1 3-2 0 0 1-1 2-1v-1c1-3-1-5 2-7h1z" class="G"></path><path d="M441 929h1c0 1 0 2 1 3h0v1l2 3 1 2c1 2 1 4 3 6 1 1 1 3 2 4l1 1h1l1 1c1 2 2 5 3 7 0 2 0 4 1 5v1l-1-1-1-1c0 1 0 1-1 2l-3 1c-1-1-4-2-6-2-4-1-7-3-10-6-5-5-5-11-5-18l1 1c1 1 1 2 1 2 2-5 4-8 8-12h0z" class="J"></path><path d="M447 952h2c1 2-1 4 0 6l-1 1c-1 0-1 0-2-1-1-2 0-3 1-6zm5-3h1l1 1c1 2 2 5 3 7 0 2 0 4 1 5v1l-1-1-1-1c0 1 0 1-1 2 1-5-2-9-3-14z" class="U"></path><path d="M447 952c0-1 1-3 2-5l2 1 1 1c-1 1-1 1-1 2 1 2 2 4 1 6l1 1v2h-3c0-1 0-1-1-2h0c-1-2 1-4 0-6h-2z" class="W"></path><path d="M446 938c1 2 1 4 3 6l-6 14c-3-1-5-2-6-4 1-6 6-12 9-16zm-5-9h1c0 1 0 2 1 3h0v1l2 3c-1 3-4 7-6 10l-3 7c-2-4-2-7-3-12 2-5 4-8 8-12h0z" class="G"></path><path d="M441 929h1c0 1 0 2 1 3h0v1l2 3c-1 3-4 7-6 10 0-4 2-6 2-8v-1c-2 1-3 3-4 4h-1v-1l4-4h0c1-2 1-3 1-4v-1l1-1-1-1z" class="F"></path><defs><linearGradient id="e" x1="305.704" y1="707.026" x2="286.105" y2="728.752" xlink:href="#B"><stop offset="0" stop-color="#b7aea0"></stop><stop offset="1" stop-color="#d2d1c7"></stop></linearGradient></defs><path fill="url(#e)" d="M305 687c2 4 5 8 7 13h0l2-2c2 2 3 3 4 5h-1v5l-2 3c-2 3-3 5-5 7-3 3-6 7-9 9-3 1-4 2-7 3h0l-1 1h-1c-1 0-5 1-6 0l-1-1h-1v-1h-1c-2-1-4-3-5-5h0l1-1 2 1c1-1 2-1 4-2l2-1c1-1 2-1 4-2 1 0 3-1 4-2v-1c1 0 1-1 2-2 2 0 4-1 5-3 0-1 1-2 2-3s1-2 3-3v-1c1-4 1-5-1-9-1-3-2-6-1-8z"></path><path d="M312 700h0l2-2c2 2 3 3 4 5h-1-1c-2 2-3 5-5 7 0 0-1 0-1-1h-1v-1-2l3-6z" class="O"></path><path d="M305 687c2 4 5 8 7 13l-3 6c-1 1-2 3-4 3l2-4v-1c1-4 1-5-1-9-1-3-2-6-1-8z" class="D"></path><path d="M694 662c-1 1-1 2-2 3-2 1-4 3-4 4v1c1-1 3-2 4-3s2-1 3-1h1c-2 2-3 2-5 4v1c-1 0-1 0-2 1 1 0 2 0 3-1h0c1-1 2 0 3 0-1 1-1 1-2 1l-1 1-1 1c-2 0-4 2-5 2l-1 1v3c1 1 0 1 2 1 1-1 1-1 2 0-1 0-2 2-3 2h0l-2 2h1l3-1c-1 2-4 4-6 5l-2 1v1h2c-1 2-2 3-3 4-2 1-4 4-5 4l-5 4 9 6 6 5-1 2c-2-2-4-4-7-5-4-3-11-4-16-5h-2c3-8 6-15 11-22l12-13 8-7 1 1c1 0 3-2 4-3z" class="C"></path><path d="M674 699c-2 0-4 2-6 3-1 1-2 0-3 0v-1l7-7c1 1 1 2 3 2 1 0 1 0 2-1h2c-2 1-4 4-5 4z" class="L"></path><path d="M679 687h0c3-2 4-3 7-4l-2 2h1l3-1c-1 2-4 4-6 5l-2 1v1h2c-1 2-2 3-3 4h-2c-1 1-1 1-2 1-2 0-2-1-3-2 2-2 5-4 7-7z" class="N"></path><defs><linearGradient id="f" x1="670.076" y1="691.475" x2="684.607" y2="679.752" xlink:href="#B"><stop offset="0" stop-color="#c1bab3"></stop><stop offset="1" stop-color="#ebeaeb"></stop></linearGradient></defs><path fill="url(#f)" d="M679 687c-6 4-11 9-16 14 4-9 14-19 22-24v3c1 1 0 1 2 1 1-1 1-1 2 0-1 0-2 2-3 2h0c-3 1-4 2-7 4h0z"></path><path d="M694 662c-1 1-1 2-2 3-2 1-4 3-4 4v1c1-1 3-2 4-3s2-1 3-1h1c-2 2-3 2-5 4v1c-1 0-1 0-2 1 1 0 2 0 3-1h0c1-1 2 0 3 0-1 1-1 1-2 1l-1 1-1 1c-2 0-4 2-5 2s-2 1-3 2c-3 1-5 3-8 5-1 2-3 4-4 4 0-1 1-2 2-3h-1c1-3 3-4 5-6a79.93 79.93 0 0 1 13-13c1 0 3-2 4-3z" class="O"></path><defs><linearGradient id="g" x1="114.686" y1="204.522" x2="108.564" y2="231.515" xlink:href="#B"><stop offset="0" stop-color="#ada99c"></stop><stop offset="1" stop-color="#cac6bc"></stop></linearGradient></defs><path fill="url(#g)" d="M114 203c1-1 2-1 2 0h1l2 1h1l2 1v1s1 0 2 1 3 1 4 3v1c2 1 2 1 3 2-2 5-3 8-6 12l-2 3c-4 5-7 11-14 12h-1c-2-1-4-1-6-2s-4-1-6-1c2-2 6-1 8-3 1-1 1-1 2-1s1 0 2-1c-1-1-1-1-1-3-1 0-1 1-2 2v1-1l-1 1v-2l-1-1 3-1c3-1 7-3 8-7 1-1 1-3 0-5-2-5-13-5-17-7 1-1 3-1 5-1 4-1 8-3 12-4v-1z"></path><path d="M128 211c2 1 2 1 3 2-2 5-3 8-6 12l-2 3-1-2-3 3h-1c2-3 5-6 6-10 2-2 2-5 4-8z" class="B"></path><defs><linearGradient id="h" x1="117.438" y1="230.925" x2="109.33" y2="238.838" xlink:href="#B"><stop offset="0" stop-color="#988f7d"></stop><stop offset="1" stop-color="#b9b1a0"></stop></linearGradient></defs><path fill="url(#h)" d="M118 229h1l3-3 1 2c-4 5-7 11-14 12h-1c0-2 0-3 1-4 1-2 7-7 9-7z"></path><path d="M839 340l6-12c1-2 1-5 1-7l2 13c1 3 2 4 5 6h4c2-1 3-3 4-5v-6c-1-7-3-14-7-20 0-2-2-3-3-5h1c6 3 7 9 10 15l6 10c1 0 2 1 3 1-2 3-5 5-8 7-3 3-5 6-9 7-2 0-4 0-5-2-2-1-2-2-3-3l-1-1c-3 9-7 17-16 21-1 1-2 1-3 1h-1c-2-1-4-1-6 0-3 2-6 4-9 7v-1c3-3 7-7 12-8h0l1-3c1-5 3-11 6-15l1 1h1s0-1 1-1c0-1 0-1 1-2l2 1 3 1h1z" class="E"></path><path d="M830 341h1s0-1 1-1c0-1 0-1 1-2l2 1 3 1h1c-1 2-2 4-3 5-3-1-5-1-6-4z" class="F"></path><path d="M853 242c0 14 2 28 0 42l-3 15c-2 4-4 9-4 14l-2-2v-3-1c-1-1-1-1-2-1l-1 3-4 7h-2c-2 3-4 6-5 10 0 1 0 1 1 2h1 2v1h-2c-2-1-7 1-9 1h0c-2 2-3 2-5 3h-1c2-2 3-6 5-9 2-4 4-7 6-11h0c9-13 16-28 18-44 0-1 1-3 1-3l2-13c0-2 1-4 0-5 0-2 3-5 4-6z" class="I"></path><path d="M847 269v-1c1-2 1-4 1-6 1-1 1-3 1-4v-2c1-1 1-3 1-4 1-2 0-4 1-5h1c0 4 0 10-1 14v2c0 2 0 4-1 5 0 5 0 10-1 15l-4 16-2 3v2c-1 1-1 2-1 2l-1 3-4 7h-2l2-4c1-1 3-7 4-9-2 2-3 4-5 4 2-4 6-10 6-15 0-4 2-8 3-13 1-3 1-6 2-10z" class="Y"></path><defs><linearGradient id="i" x1="825.185" y1="314.444" x2="831.762" y2="316.71" xlink:href="#B"><stop offset="0" stop-color="#302c2f"></stop><stop offset="1" stop-color="#44463c"></stop></linearGradient></defs><path fill="url(#i)" d="M846 269h1c-1 4-1 7-2 10-1 5-3 9-3 13 0 5-4 11-6 15 2 0 3-2 5-4-1 2-3 8-4 9l-2 4c-2 3-4 6-5 10 0 1 0 1 1 2h1 2v1h-2c-2-1-7 1-9 1h0c-2 2-3 2-5 3h-1c2-2 3-6 5-9 2-4 4-7 6-11h0c9-13 16-28 18-44z"></path><path d="M836 307c2 0 3-2 5-4-1 2-3 8-4 9l-2 4c-2 3-4 6-5 10 0 1 0 1 1 2h1 2v1h-2c-2-1-7 1-9 1h0-1c1-5 6-11 9-15 1-2 3-5 5-8z" class="M"></path><path d="M823 330l1-2c0-1 1-1 1-2h1c1-3 4-5 5-7 2-2 4-7 6-7l-2 4c-2 3-4 6-5 10 0 1 0 1 1 2h1 2v1h-2c-2-1-7 1-9 1z" class="X"></path><path d="M816 510c18 0 38 0 56 6 14 4 26 13 36 23 19 18 30 40 30 67l-1 16h-1v-5h0c-1 2-1 4-2 6h0c1-15 0-29-4-43-8-24-24-43-46-54l-15-6c-4-1-21-5-24-3-5-1-9 0-13 0h-20c-4 0-7-2-10-1-5 0-9 1-13 1l-2-1h-1-1c-1 0-1 0-2 1h0-2c-1 0-2 0-3 1-1 0 0-1-1 0h-2c-2 1-3 1-5 1h-1l9-3 8-1h1v-1c9-2 19-2 29-4z" class="E"></path><defs><linearGradient id="j" x1="891.981" y1="201.224" x2="928.345" y2="231.479" xlink:href="#B"><stop offset="0" stop-color="#a29b8c"></stop><stop offset="1" stop-color="#cbc6ba"></stop></linearGradient></defs><path fill="url(#j)" d="M901 196l6-1c1 0 2 1 2 1 3 1 5-1 7 0v1l-2 1v1c1 1 0 3 1 4s4 2 6 3c0 0 2 0 2 1h0l3 1v1h-1c-1 1-2 1-2 2h0c-3 1-9 2-11 5-1 1-1 3 0 4 0 1 1 3 2 4l1 3c3 1 5 2 8 2v1c3 3 5 4 9 6-6 1-15 2-20 1h-1l1 1v1c-2-1-3-2-4-3l-13-12c-4-5-5-11-6-18-2 0-4 1-6 2h0c1-2 2-2 4-3 2-2 4-3 7-4l-1-1c2 0 3-1 4-2 2 0 2-1 4-2z"></path><path d="M899 204c1 1 2 2 2 3 1 3 0 6 0 8v-1c-1-2-1-2-1-3v-2c-1-2-1-4-1-5z" class="K"></path><path d="M923 211h0c-3 1-9 2-11 5-1 1-1 3 0 4 0 1 1 3 2 4l1 3c-2-1-4-3-5-5s-2-4-1-6c2-4 9-4 13-5h1z" class="B"></path><path d="M889 206v-1l1 3c1 3 2 6 5 9l1 2c2 4 4 8 7 11 2 2 5 4 5 6l-13-12c-4-5-5-11-6-18z" class="D"></path><path d="M894 201c1 0 2 0 4-1h2v2c-1-1-1-1-3-1l1 1h1v2c0 1 0 3 1 5v2c0 1 0 1 1 3v1c-1 1 0 1-2 1l-3 3-1-2c-3-3-4-6-5-9l-1-3v1c-2 0-4 1-6 2h0c1-2 2-2 4-3 2-2 4-3 7-4z" class="U"></path><path d="M895 217c1-1 2-2 4-1h0l-3 3-1-2z" class="R"></path><path d="M371 79c1-2 3-4 5-5 4-3 12-3 17-2 1 0 1 0 1 1l1 1c-8 8-14 14-14 27 0 6 3 13 8 17 3 3 7 5 10 7l-1 1c1 2 0 3-1 5h-4c-6-3-12-5-16-10-3 1-3-3-6-4-1-1-3-7-4-9-3-11-2-20 3-30l1 1z" class="V"></path><path d="M370 78l1 1c-2 2-3 4-4 6v1c-3 8-1 16 3 23l7 12c-3 1-3-3-6-4-1-1-3-7-4-9-3-11-2-20 3-30z" class="I"></path><defs><linearGradient id="k" x1="378.926" y1="107.31" x2="404.281" y2="92.669" xlink:href="#B"><stop offset="0" stop-color="#b1a997"></stop><stop offset="1" stop-color="#d9d4c3"></stop></linearGradient></defs><path fill="url(#k)" d="M394 73l1 1c-8 8-14 14-14 27 0 6 3 13 8 17 3 3 7 5 10 7l-1 1c-2-1-5-2-8-4-6-3-10-9-11-16l-1-1c-1-5 0-10 2-15 2-6 8-14 14-17z"></path><path d="M73 233h1c2 1 4 2 7 2 2-1 9-3 10-1-1 3-6 4-9 7-2 1-4 4-6 6 8 2 13 3 18 10l3 6h1l1-4 1 1v2c2 0 5-2 7-2l16-4v1c-1 6 2 11 6 16h1v-1c2-4 9-8 12-11 2-2 4-6 7-7 1 0 1 0 2 1 2 1 2 4 1 5-2 10-8 19-15 25l-1-1c-4 3-7 5-12 5-4 1-7 2-11 2-1 0-3 0-4-1 0-3-1-6-1-9l2 1c1 1 2 1 3 1 4 0 8-2 11-4 2-1 3 0 5-2h0-2c-4-4-6-10-7-16h0c-6 1-14 1-19 4-2 1-3 3-5 4-2-5-5-9-9-13-5-4-10-5-17-4h0c2-4 6-9 9-13v-1c-2 0-4-1-6-2h0v-1-2z" class="E"></path><path d="M140 272h1c2-3 4-12 7-12 1 1 1 3 0 5-2 8-6 13-12 19-4 3-7 5-12 5l3-2 1-1c2-1 7-5 8-8 1 0 2-1 2-2v-1c1-1 2-1 2-3z" class="N"></path><path d="M129 277c4-8 12-14 18-20-1 3-3 7-4 10-1 1-3 3-3 5h0c0 2-1 2-2 3v1c0 1-1 2-2 2-1 3-6 7-8 8l-1 1-3 2c-4 1-7 2-11 2-1 0-3 0-4-1 0-3-1-6-1-9l2 1c1 1 2 1 3 1 4 0 8-2 11-4 2-1 3 0 5-2h0z" class="I"></path><path d="M180 303v3l2-1c1 1 1 3 2 5 2 6 2 14 6 19-1 1 0 1-1 1l1 1c0 2 1 3 1 5l2 5c1 1 2 3 2 5 2 4 2 8 3 12h0c9 3 14 11 18 18 1 1 2 3 2 4l-10-12c-3-4-5-7-10-9l-2 1c-1 0-2 1-3 1-3 0-4-1-6-2-5-5-8-11-9-18v-1h-1c-1 2-2 3-4 3-11 4-12-9-20-13v-1l1-3c5-5 7-13 12-19 1-1 2-2 4-2-1 3-3 6-5 9-3 6-4 14-4 20 0 2 1 3 3 5 1 1 3 2 5 1 6-1 6-24 7-30 1-2 2-5 4-7h0z" class="J"></path><path d="M183 339l1-2c-1-2-1-5 0-7h0l1 1h1c1-1 2-1 3-1l1 1c0 2 1 3 1 5l2 5c-1 3-3 4-6 7-1-3-3-6-4-9z" class="F"></path><path d="M180 306l2-1c1 1 1 3 2 5 2 6 2 14 6 19-1 1 0 1-1 1s-2 0-3 1h-1l-1-1h0c-1 2-1 5 0 7l-1 2-4-6c-3-9-3-19 1-27z" class="V"></path><path d="M191 136c5-3 9-6 14-8 17-7 38-8 55-4 12 2 22 7 33 12h-1c-3 1-8 1-11 1h-25l-63-1h-2z" class="E"></path><path d="M768 444c6-5 14-6 23-8-4 4-7 10-6 16v1c2 0 6-2 8-2 7-3 15-4 22-5h17c5 0 9 1 13 1 1 0 3 1 5 2l-1 1s-1 1-2 1h-11l-21 3c-3 0-7 1-10 2-15 3-29 8-42 16-3 2-6 3-9 6v-1c1-5 3-10 8-14h0l7-5-5 2h-1c-1 1-2 1-3 1 1-5 5-12 8-17z" class="Q"></path><path d="M727 136c12-8 28-12 42-14 21-2 48 0 65 14-1 0-2 1-3 1h-19-40-28c-6 0-11 0-17-1z" class="E"></path><defs><linearGradient id="l" x1="842.348" y1="515.46" x2="850.91" y2="561.733" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#3e3d3b"></stop></linearGradient></defs><path fill="url(#l)" d="M804 526v-1h0c0-2 2-2 3-3l1 1v1l2 2h1l4 1c6 1 12 0 19 1 16 2 32 8 46 17 4 3 8 6 12 10-1 0-2 0-2 1l-8-6c-3-2-6-4-10-6l-3 1s-1-1-2-1h0v1 1c-1-1-2-2-3-2l-7-4c-2-1-4-2-5-2-2 0-2 0-3-1l-13-3c-4-1-9-3-13-2 19 6 37 12 51 28 2 2 5 5 6 8 0 1 1 1 1 2 1 3 3 6 5 10 1 4 2 8 3 13s1 12-1 17v1c0 2-1 3-2 5 0 0-1 1-1 2l-1 1c-1 1-2 2-3 4-1-2 0-3 0-5l1-4 2-3h-1c1-5 1-8 0-13 1 0 1-1 1-2v-1-1c0-3-1-6-2-9-5-13-15-23-26-32-5-3-9-7-14-9-4 0-8-4-12-5 1 1 4 5 5 5 3 4 4 8 6 12 0 1 0 2-1 3-2-6-5-12-10-16-6-5-13-9-20-13-2-2-4-3-6-4z"></path><path d="M849 537h0c-4-2-9-3-13-4-3-1-5-2-8-2-1 0 0 0-1-1 16 3 31 7 45 14l-3 1s-1-1-2-1h0v1 1c-1-1-2-2-3-2l-7-4c-2-1-4-2-5-2-2 0-2 0-3-1z" class="O"></path><defs><linearGradient id="m" x1="853.279" y1="579.373" x2="871.086" y2="558.757" xlink:href="#B"><stop offset="0" stop-color="#d8d7d4"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#m)" d="M842 544l-19-10c5 1 9 1 13 3 18 6 33 16 43 32 7 11 12 27 9 40 0 2-1 4-2 5v2s-1 1-1 2l-1 1c-1 1-2 2-3 4-1-2 0-3 0-5l1-4 2-3h-1c1-5 1-8 0-13 1 0 1-1 1-2v-1-1c0-3-1-6-2-9-5-13-15-23-26-32-5-3-9-7-14-9z"></path><path d="M883 598c1 0 1-1 1-2v-1-1l1 3c1 5 1 12 0 17h1v2s-1 1-1 2l-1 1c-1 1-2 2-3 4-1-2 0-3 0-5l1-4 2-3h-1c1-5 1-8 0-13z" class="M"></path><path d="M882 614c1 2 1 3 2 4h1l-1 1c-1 1-2 2-3 4-1-2 0-3 0-5l1-4z" class="Z"></path><path d="M883 598c1 0 1-1 1-2v-1-1l1 3c0 5 0 9-1 14h-1c1-5 1-8 0-13z" class="W"></path><path d="M951 233l1 2h0c-3 2-6 3-9 3 4 4 7 9 10 14h0c-8 0-15 2-20 7-3 3-5 7-7 10h0c-6-6-15-7-22-8-1 5-3 11-7 15v1h-1l3 3c2 1 3 1 4 2 1 0 2 0 3 1h1c1 0 3 0 4-1h1c1 0 2-1 3-2l1-1c2 2 2 2 2 5s-2 5-4 7c4 2 8 3 11 7 1 1 2 3 1 4 0 3-3 5-5 6 2 2 10 9 10 11-3-3-6-7-10-9-3-1-9-2-10-4 3-2 8-2 11-5v-2c-2-5-12-5-16-5-3-1-5-1-7-2-13-4-23-16-28-27-1-3-2-7-1-11 1-1 2-2 4-3 2 0 2 1 3 2 3 3 5 6 7 9a57.31 57.31 0 0 0 11 11c3-4 5-8 5-14-1-2-3-3-5-5 10 4 20 5 30 10-2-5-3-10-3-15h0l-6-7c5 2 10 3 15 6-2 1-6 2-8 4l2 10 5-5c4-6 10-8 17-9h1l-6-6c-3-2-8-4-9-8 1-2 7 1 9 1 3 0 6-1 8-2h1z" class="E"></path><path d="M883 264l7 7c-1 0-2 0-4-1-1 0-2-1-3-1v-1c-1-1-1-2-2-3l2-1z" class="S"></path><path d="M876 263c-1-2-1-3 0-4h1c2 1 4 3 6 5h0l-2 1c1 1 1 2 2 3v1l2 2-1 1c-1-2-2-5-4-6l-3-3-1-1v1z" class="L"></path><path d="M876 263v-1l1 1 3 3c2 1 3 4 4 6l1-1-2-2c1 0 2 1 3 1 2 1 3 1 4 1 1 2 3 4 4 5-1 1-1 2-2 4 1 2 5 5 7 6h-1-2-2l1 1-6-3c-7-5-12-13-13-21z" class="I"></path><path d="M894 276c2 2 3 4 5 4 2 1 3 1 4 2 1 0 2 0 3 1h1c1 0 3 0 4-1h1c1 0 2-1 3-2l1-1c2 2 2 2 2 5s-2 5-4 7c-7 0-14-1-19-4l-1-1h2 2 1c-2-1-6-4-7-6 1-2 1-3 2-4z" class="T"></path><path d="M899 280c2 1 3 1 4 2 1 0 2 0 3 1h1c1 0 3 0 4-1h1c1 0 2-1 3-2l1-1c2 2 2 2 2 5s-2 5-4 7c-7 0-14-1-19-4l-1-1h2 2 1 0c3 2 10 4 14 3 1-2 1-4 1-6h-2s-1 1-2 1h0-3c-2-1-3 0-4-1l-1-1c-1 0-1 0-2-1l-1-1z" class="N"></path><defs><linearGradient id="n" x1="712.188" y1="859.454" x2="717.582" y2="892.275" xlink:href="#B"><stop offset="0" stop-color="#b8b2a6"></stop><stop offset="1" stop-color="#e2e1d9"></stop></linearGradient></defs><path fill="url(#n)" d="M756 851c1 3-1 5-2 7-3 5-6 10-10 15l1 1c-1 2-3 4-4 6-12 12-29 18-45 19-3 0-6-1-9-1-1-1-2-1-3-1h-1c-3-2-6-3-9-5l-4-2h0v-1c2 0 4 0 6-1 1 0 3 0 4-1 1 0 1 0 2-1h1 2 1c1-1 1-1 2-1h2l1-1c3 1 8-2 11-3h1v-1l3-1h0c3-1 6-3 9-5 4-1 6-3 9-5 2-1 4-4 7-6 1-1 3-2 5-4l9-2 11-6z"></path><path d="M684 897c-1-1-1 0-1-1h1 11c13 0 27-5 37-12 5-3 9-7 12-11l1 1c-1 2-3 4-4 6-12 12-29 18-45 19-3 0-6-1-9-1-1-1-2-1-3-1z" class="U"></path><path d="M736 859l9-2c1 1 0 2-1 3l-3 4-2 2-2 2 1 1c-2 2-3 3-5 4l-1 1c0 1-1 1-2 2l-3 2-6 3-6 3h-1c-4 2-9 4-13 5s-8 2-12 2h-10c-2 0-3-1-5 0v1l-4-2h0v-1c2 0 4 0 6-1 1 0 3 0 4-1 1 0 1 0 2-1h1 2 1c1-1 1-1 2-1h2l1-1c3 1 8-2 11-3h1v-1l3-1h0c3-1 6-3 9-5 4-1 6-3 9-5 2-1 4-4 7-6 1-1 3-2 5-4z" class="N"></path><path d="M888 611v-1c2-5 2-12 1-17s-2-9-3-13c-2-4-4-7-5-10 0-1-1-1-1-2-1-3-4-6-6-8-14-16-32-22-51-28 4-1 9 1 13 2l13 3c1 1 1 1 3 1 1 0 3 1 5 2l7 4c1 0 2 1 3 2v-1-1h0c1 0 2 1 2 1l3-1c4 2 7 4 10 6l8 6c9 8 14 17 17 30l-1 1c0-1-1-1-1-1v-1h0l-1 1v6 2h0c-1 1-2 1-3 1l-1 10-1 7c-2 6-4 11-8 16-2 2-4 3-5 5v1c-2 0 0 0-1 1l-2 1c-1 1-2 1-3 1l-1-1-3 1h-1l2-1c-1 0-2-1-3-1 5-4 9-7 12-12l-1-1c-1-1-1-2-1-3h0l1-1c0-1 1-2 1-2 1-2 2-3 2-5z" class="G"></path><path d="M872 544c4 2 7 4 10 6-2 1-4-1-6-2-3-1-5-2-7-3l3-1z" class="F"></path><path d="M891 613c2-7 3-14 2-21 0-3 0-5-1-8-1-2-2-5-2-7 2 6 4 11 5 18 0 5 0 11-2 16-3 9-8 20-16 25-1 0-2-1-3-1 5-4 9-7 12-12l-1-1c-1-1-1-2-1-3h0l1-1c0-1 1-2 1-2 1-2 2-3 2-5l1 2h2z" class="C"></path><path d="M888 611l1 2h2c-1 3-3 7-5 10l-1-1c-1-1-1-2-1-3h0l1-1c0-1 1-2 1-2 1-2 2-3 2-5z" class="N"></path><path d="M899 595c0-3-1-8-1-11 1 0 2 1 2 2h1l1-1c-2-7-6-13-10-19v-1c5 6 9 14 11 21h1v6 2h0c-1 1-2 1-3 1l-1 10-1 7c-2 6-4 11-8 16-2 2-4 3-5 5v1c-2 0 0 0-1 1l-2 1c-1 1-2 1-3 1l-1-1-3 1h-1l2-1c8-5 13-16 16-25 2-5 2-11 2-16 1 2 1 2 3 3 0-1 0-2 1-3z" class="C"></path><path d="M903 586h1v6 2h0c-1 1-2 1-3 1l-2-1c1-2 1-3 2-4 0 1 0 1 1 2l1-1v-5z" class="O"></path><path d="M895 595c1 2 1 2 3 3 0-1 0-2 1-3 0 7-2 15-4 21-1-2-1-3-2-5 2-5 2-11 2-16z" class="N"></path><path d="M877 636c8-5 13-16 16-25 1 2 1 3 2 5-3 8-9 15-16 20l-3 1h-1l2-1z" class="S"></path><path d="M303 592c6 8 9 16 13 26v1c-2-1-3-3-5-5-4-5-10-10-17-12-1 0-3 1-5 1-7 1-16-2-21 6 0 2 2 4 3 5 4 4 11 7 11 13h0 0 0-1-1c-2 0-5-1-7-2-10-3-20-3-29 5-2 3-4 6-3 10-1 0-1 0-2-1-1-2-2-4-3-7h0c-1-2-1-3-1-4-1-10 3-18 9-25 4-4 8-7 13-9 3 0 6-1 9-2h1c2 0 5 0 7 1h1c2 1 4 1 6 1 4 0 9 2 12 3h1v-1l1 1c1 1 2 1 3 1 2 1 3 3 5 4 0-1 0-1 1-2-2-3-2-5-2-8h1 0z" class="Q"></path><path d="M284 599h3c1 1 1 1 2 1 2 1 3 1 5 1v1c-1 0-3 1-5 1-7 1-16-2-21 6 0 2 2 4 3 5 4 4 11 7 11 13h0 0 0-1-1c-1-4-6-6-9-8-2-1-4-3-5-5-2-2-3-4-4-7l-7 3c3-2 6-3 9-5 4-1 7-3 11-4 3-1 6-1 9-2z" class="D"></path><path d="M303 592c6 8 9 16 13 26v1c-2-1-3-3-5-5-4-5-10-10-17-12v-1c-2 0-3 0-5-1-1 0-1 0-2-1h-3c-3-1-7-1-10-1-4 1-7 2-10 2 7-4 16-3 24-2-9-5-18-5-28-2h0l6-2 8-1h1c2 1 4 1 6 1 4 0 9 2 12 3h1v-1l1 1c1 1 2 1 3 1 2 1 3 3 5 4 0-1 0-1 1-2-2-3-2-5-2-8h1 0z" class="C"></path><path d="M302 592h1c2 4 4 9 6 14-2-1-4-3-6-4 0-1 0-1 1-2-2-3-2-5-2-8z" class="U"></path><path d="M699 612c3-2 7-5 10-7 1-3 3-6 4-8 2-5 6-11 9-14l-3 6c-4 4-6 10-8 14l7-3 1-1c1-2 1-3 3-4 1-2 3-4 5-5 2-2 5-3 6-5h1v1c-2 1-4 2-5 3 3 0 5 0 8 1h9v1l2-1-1-1v-1c1 2 4 3 7 5l13 5c5 2 9 3 12 7 3 5 6 9 8 14 1 4 1 8 1 12l-3 10-1-1c0-1 0-2-1-3 0-1-1-2-1-3-4-5-10-9-16-10-7-2-19-1-26 2-2 1-4 3-7 4l4-6c-1 0-1 1-2 2 0 0-2 2-2 3l-1 1h0v-4c0-7 7-7 10-13 1-2 2-4 1-6-3-3-9-4-13-4-15-1-25 8-36 17h0c0-3 3-6 5-8z" class="Q"></path><path d="M699 612c3-2 7-5 10-7 1-3 3-6 4-8 2-5 6-11 9-14l-3 6c-4 4-6 10-8 14l7-3 1-1c1-2 1-3 3-4 1-2 3-4 5-5 2-2 5-3 6-5h1v1c-2 1-4 2-5 3 3 0 5 0 8 1h9v1l2-1-1-1v-1c1 2 4 3 7 5l13 5c-1 0-2 0-3-1h-1c-1 0-2 0-3-1l-1 1h-3-1l-2 1h2 1l3 1h1c0 1 1 1 2 1l-1 1 2 1c3 1 5 2 7 4-3-2-7-4-11-4-2 0-3 0-5-1-2 0-4 0-5-1h-3c-2-1-5 0-7 0l8 3h1c1 0 2 1 3 1 1 1 2 1 3 1v1h-3c2 1 2 0 3 0 0 0 0 1 1 1 2 0 1 0 3 1l6 2h-3c-4-1-10-2-14-1v1c-2 6-5 10-10 14-1 0-1 1-2 2 0 0-2 2-2 3l-1 1h0v-4c0-7 7-7 10-13 1-2 2-4 1-6-3-3-9-4-13-4-15-1-25 8-36 17h0c0-3 3-6 5-8z" class="D"></path><path d="M747 588c1 2 4 3 7 5l-1 1c1 0 1 0 1 1h0c-1 0-1 0-2-1-2-1-6-1-8-2h0v-1h2 0l2-1-1-1v-1z" class="F"></path><path d="M737 597h0l-7 1c-1 0-1 1-3 1 3-4 7-5 12-6l-2 3v1z" class="S"></path><path d="M722 595c2 1 5-2 8-3 1 0 3 1 4 1-2 1-5 1-7 3-3 1-6 3-9 4l1-1c1-2 1-3 3-4z" class="U"></path><path d="M739 593c2 0 6 0 9 1v2c1 0 2 1 3 2l-7-1h-7v-1l2-3z" class="N"></path><path d="M748 594v2c1 0 2 1 3 2l-7-1h1v-2-1h3z" class="F"></path><path d="M754 593l13 5c-1 0-2 0-3-1h-1c-1 0-2 0-3-1l-1 1h-3-1l-2 1h2 1l3 1h1c0 1 1 1 2 1l-1 1-10-3c-1-1-2-2-3-2v-2h4 0c1 1 1 1 2 1h0c0-1 0-1-1-1l1-1z" class="O"></path><path d="M758 608c-3 0-10-1-12-3h0c-3-4-13-5-17-6 10 0 20-1 30 3-2 0-3 0-5-1-2 0-4 0-5-1h-3c-2-1-5 0-7 0l8 3h1c1 0 2 1 3 1 1 1 2 1 3 1v1h-3c2 1 2 0 3 0 0 0 0 1 1 1 2 0 1 0 3 1z" class="F"></path><path d="M733 585h1v1c-2 1-4 2-5 3 3 0 5 0 8 1h9v1h0-2v1h0c-4 0-7 0-10 1-1 0-3-1-4-1-3 1-6 4-8 3 1-2 3-4 5-5 2-2 5-3 6-5z" class="N"></path><path d="M847 395c12-1 22-1 33 3 5 2 10 4 13 8-10-6-24-10-35-10h-15c5 2 9 3 14 5 12 6 24 17 30 30 0 1 1 2 1 3-10-15-24-28-43-32-4 0-11-1-15 1h0l-1 1h-2v1h0 2s1 1 2 1c2 0 3 1 4 3 4 1 9 2 13 4s7 6 13 7c-1 1-2 1-3 1-1 1 0 2 0 3-1-1-1-1-1-2-2-1-5 0-7 0l-9 6c-1-5-1-9-1-14-12 1-24 7-31 17-1 1-2 1-2 2-4 3-5 9-7 13l4-1c2 0 5 0 6-1s3-6 5-8c2-3 5-6 8-9 0 1-3 4-4 5-3 4-5 8-6 12h1v1h4l-3 1h0c-7 1-15 2-22 5-2 0-6 2-8 2v-1c-1-6 2-12 6-16-9 2-17 3-23 8h0c0-3 8-21 10-23v3h0v1h2c-1 1 0 2 0 3 1-2 2-4 4-6l-2 5 1 1-1 1h1 0c2-1 3-2 4-4v-1h1c-1 2-2 4-3 5s-1 1-1 2c1-1 3-2 3-3l2-3 1 1h0l2-2 1 1 12-12 1-1 15-11 4-2h0c3 0 6-2 9-2 2-1 5-1 8-2h5z" class="E"></path><path d="M817 408c2 1 5 1 8 1-7 1-12 3-18 7-1 1-1 1-2 1l7-6 5-3z" class="S"></path><path d="M820 406c3-1 7-3 10-3h0l-1 1h-2v1h0 2s1 1 2 1c2 0 3 1 4 3h-7-3c-3 0-6 0-8-1 1-1 2-1 3-2z" class="N"></path><path d="M817 408c1-1 2-1 3-2 3 1 4 1 6 1 1 0 2 1 2 2h-3c-3 0-6 0-8-1z" class="Z"></path><defs><linearGradient id="o" x1="311.832" y1="853.998" x2="302.889" y2="892.627" xlink:href="#B"><stop offset="0" stop-color="#a8a395"></stop><stop offset="1" stop-color="#dfddd2"></stop></linearGradient></defs><path fill="url(#o)" d="M253 850c7 4 13 7 21 10 4 1 8 1 12 2l9 6c7 4 16 8 24 8l2-1c1 0 1 1 3 0h-3v-1c9 1 16 1 24-1l2 1c6-1 12-4 17-6h1c1 0 2 0 3-1l2 2-1 1 1 1c-12 11-26 18-40 26l-1-1c-3 1-6 1-10 1-2-1-5 0-8-1l-10-1c-7-1-18-5-24-10l-1-1-1-1c-2-2-4-3-6-5v-1c-4-4-7-7-10-11-1-4-5-7-6-11 0-1 0-2-1-3v-1l1-1z"></path><path d="M321 874c9 1 16 1 24-1l2 1c-9 2-19 4-28 2l2-1c1 0 1 1 3 0h-3v-1z" class="C"></path><path d="M301 893c9 2 23 4 32 1h0l-4 2c-3 1-6 1-10 1-2-1-5 0-8-1l-10-1v-2z" class="R"></path><path d="M269 877c3 1 5 3 7 5 8 5 17 8 25 11v2c-7-1-18-5-24-10l-1-1-1-1c-2-2-4-3-6-5v-1z" class="X"></path><path d="M660 706c5 1 12 2 16 5 3 1 5 3 7 5l1-2c2 2 5 4 6 7 1 0 1 1 2 1l-1 1c10 7 17 16 20 28 1 9-2 15-3 23-1 2 0 4 0 6l-3-1c-1-1-2-2-3-4-2-1-4-4-5-6-1 2-2 3-3 4v2h-1c-1-5-1-9-4-14-2-2-4-8-7-9-1 1-3 1-4 1-3 0-5 1-8 1 1 4 2 6 2 10l-3-9c-2-7-6-17-12-20h-3c-1-1-2-2-3-2-1-1-1-1-2 0-1-1-1-1 0-2 0-2 1-4 3-5h-2c1-3 2-6 3-8 1 0 1-1 1-1l1-1h-1c1-3 2-6 3-8 1-1 2-1 3-2z" class="V"></path><path d="M684 714c2 2 5 4 6 7 1 0 1 1 2 1l-1 1h-1c0 1 1 4 0 5h0l-1-3-2-1h0c0-4-1-5-4-8l1-2z" class="E"></path><path d="M687 724l2 1 1 3h0c1 2 1 3 2 5 2 1 3 3 4 5 0 0 0 1-1 1-1 1-1 1-2 0 0-4-3-6-5-9-1-2-1-4-1-6z" class="B"></path><path d="M675 727l2 1c8 6 14 13 19 21h0l-4-4c-5-7-13-13-20-18h3z" class="L"></path><path d="M653 718c1 0 1-1 1-1l1-1c7 3 14 6 20 11h-3c-6-4-12-6-19-9z" class="R"></path><path d="M649 733c-1-1-1-1 0-2 0-2 1-4 3-5 5 1 10 3 14 6-5-1-9-4-15-4 6 3 12 5 18 8 14 8 22 19 28 33-1 2-2 3-3 4v2h-1c-1-5-1-9-4-14-2-2-4-8-7-9-1 1-3 1-4 1-3 0-5 1-8 1 1 4 2 6 2 10l-3-9c-2-7-6-17-12-20h-3c-1-1-2-2-3-2-1-1-1-1-2 0z" class="D"></path><path d="M657 735c2 0 3 0 5 1 6 3 13 7 18 12 4 4 7 8 9 13-2-2-4-8-7-9-1 1-3 1-4 1-3 0-5 1-8 1 1 4 2 6 2 10l-3-9c-2-7-6-17-12-20z" class="I"></path><path d="M834 136c1 1 2 0 3 0h13l-4-4c-4-3-7-5-10-7h-1l-1-1c-2 0-3-1-4-2-1 0-2 0-2-1h0c1-1 1 0 2 0 9 3 16 9 22 15 3 0 8 1 10-1h2c3 2 12 2 15 1h2 2 1c7 1 14 0 21 0h37c6 0 12 0 17 1 3 0 5 1 7 3 1 2 1 4 1 6-1 5-4 10-6 15l-13 26c-1 3-4 7-4 10l-1-1c-1 0-1 0-2 1-1 0-1-1-1-1h0l15-32c2-6 7-14 8-20-1-2-2-3-4-3-3-1-8 0-11 0h-19-81-171-92c-2 0-4-1-5 0s-2 2-2 3c0 4 19 49 21 51 1 2 13 1 16 1 6 1 14 2 19 4h-11c-7-1-14 0-21 0l-1-1c-1 0-2 0-3-1h0c-2 0-2-1-3-2-1-2-2-7-5-8h1c0-2-1-4-2-6-3 0-9 3-12 5l-1-1 12-5-10-24v3l-1 3c-1 3-2 7-4 9l-1 1v-1-2l1-2c1-3 1-5 2-7 1-4 2-8 0-12 0-2-1-5 0-7 1-6 7-4 11-7h1v1c8 1 16 0 24 0l36 1h6l-1-2 1-2c2 1 4 1 6 2 0 1 0 0 1 1 1 0 3 1 5 0 2 0 2 0 3-2l1 1v1c9 1 21 0 31 0 8 0 18 1 27 0 6 1 11 1 17 1h28 40 19c1 0 2-1 3-1z" class="L"></path><path d="M352 706c2 3 5 8 5 11l1 1h0c1 3 2 6 2 8l2 3c0 1 1 2 1 3h-1l-3 1-3 3h-2c-1-1-1-1-2 0v-1l1-1-1-1h0c-2 0-2 1-3 2h1c-5 6-7 11-9 18-1 1-2 3-2 4l-2 6v-2-1-1h1v-1-2h-4v-1h-2-2-4s-1 1-1 2l-3 3v1c-4 6-6 12-6 20l-2-2c0-1 0-3-1-5l-1 1s-1 0-2-1l-7 7h-1 0c0-3 1-6 0-9s-2-5-2-8c-2-13 3-22 10-32v-1c2-2 3-3 4-5 3-2 6-5 9-8l8-8 2 1c6-2 13-3 19-5z" class="V"></path><path d="M360 726l2 3c0 1 1 2 1 3h-1l-3 1-1-1-1-2h-2c-4-1-7 0-12 1 5-3 12-4 17-5z" class="J"></path><path d="M362 729c0 1 1 2 1 3h-1c-1-1-1-1-2 0h0l-2-2v-1h4z" class="C"></path><defs><linearGradient id="p" x1="317.216" y1="720.978" x2="321.284" y2="735.522" xlink:href="#B"><stop offset="0" stop-color="#212119"></stop><stop offset="1" stop-color="#444245"></stop></linearGradient></defs><path fill="url(#p)" d="M331 710l2 1c-3 2-9 6-10 11v6c-1 3-5 9-7 11h0-1c0-3 4-7 5-10v-7c-3 3-7 6-10 10v-1c2-2 3-3 4-5 3-2 6-5 9-8l8-8z"></path><path d="M358 718c-18 7-34 18-45 33v-1l1-2c2-3 5-6 8-10 10-10 21-18 35-21l1 1h0z" class="W"></path><path d="M355 730h2l1 2 1 1-3 3h-2c-1-1-1-1-2 0v-1l1-1-1-1h0c-2 0-2 1-3 2h1c-5 6-7 11-9 18-1 1-2 3-2 4l-2 6v-2-1-1h1v-1-2h-4v-1h-2-2-4s-1 1-1 2l-3 3v1c-4 6-6 12-6 20l-2-2c0-1 0-3-1-5l-1 1s-1 0-2-1c3-3 4-7 6-11 3-7 8-14 14-19 2-2 6-5 9-7 4-2 8-3 12-5 1 0 3-1 4-2z" class="B"></path><path d="M349 735h1c-5 6-7 11-9 18-1 1-2 3-2 4l-2 6v-2-1-1h1v-1-2h-4v-1h-2-2-4s-1 1-1 2l-3 3v1c5-13 17-19 27-26z" class="I"></path><path d="M890 556c0-1 1-1 2-1 12 11 22 29 23 46 0 12-4 24-13 33-8 8-17 14-28 14h-8l-1-1c-5 0-12-2-15-6l-1-1h0-2c-1-6-4-15-2-21h0c1-2 1-4 2-5 2-7 8-15 13-20 4 1 11 3 13 7 3 4 1 10 0 14-2 5-4 9-8 13h1c2-3 5-5 8-9 1-1 3-6 3-6 1 1 0 2 0 3h0 2l1 1 3-6h1l-2 3-1 4c0 2-1 3 0 5 1-2 2-3 3-4h0c0 1 0 2 1 3l1 1c-3 5-7 8-12 12 1 0 2 1 3 1l-2 1h1l3-1 1 1c1 0 2 0 3-1l2-1c1-1-1-1 1-1v-1c1-2 3-3 5-5 4-5 6-10 8-16l1-7 1-10c1 0 2 0 3-1h0v-2-6l1-1h0v1s1 0 1 1l1-1c-3-13-8-22-17-30z" class="J"></path><path d="M900 605h1v-1l1-1v2l1 1c-1 3-1 5-2 8-1-1-1-2-2-2l1-7z" class="Z"></path><path d="M901 595c1 0 2 0 3-1 0 4 0 8-1 12l-1-1v-2l-1 1v1h-1l1-10z" class="T"></path><path d="M881 618c0 2-1 3 0 5-2 4-8 10-12 11h-1l1-1c5-2 9-10 12-15zm-7 17h-2 0c6-5 9-9 12-16 0 1 0 2 1 3l1 1c-3 5-7 8-12 12zm3-19h0 2l1 1c-1 2-2 4-4 5-1 2-5 5-6 7l-5 4c0-1 1-3 2-3 3-4 6-6 8-10l2-4zm22-4c1 0 1 1 2 2-3 7-8 16-15 19 1-2 3-3 5-5 4-5 6-10 8-16z" class="S"></path><path d="M883 611h1l-2 3-1 4c-3 5-7 13-12 15v-1c1-2 1-2 1-3 1-2 5-5 6-7 2-1 3-3 4-5l3-6z" class="D"></path><path d="M847 614v-1l2-2h0l1 1h-1c0 1 0 2-1 2 0 2-1 3-1 4v1c-1 2-1 3-1 5l1 1c1 6 2 10 7 14l1 1h0-1c-1-1-2-1-3-2s-1-1-2-1v3h0-2c-1-6-4-15-2-21h0c1-2 1-4 2-5z" class="H"></path><path d="M904 586l1-1h0v1s1 0 1 1l1-1c3 13 0 26-8 38-2 2-6 7-9 8 4-4 7-7 10-12 5-11 6-20 5-31l-1 3v-6z" class="T"></path><path d="M847 625l1-6c1-2 2-7 4-9 2-1 4-2 5-4s1-6 3-7c4 0 8 1 11 3 1 2 0 7-1 10-1 2-2 5-4 7s-4 3-5 4c-4 3-4 9-1 13 1 3 3 4 4 6-3 0-6-1-9-2l-1-1c-5-4-6-8-7-14z" class="V"></path><path d="M85 458c9 9 19 13 31 14 8 0 14-2 21-5l-9 7c6-2 11-5 17-7 4-1 8 1 12-3-1 2-2 3-3 3-3 1-6 2-9 2 5 3 11 5 16 4 2 0 4-1 5-2 4-2 6-4 8-8 1 0 1 0 2-1 7 0 13-1 20 0 17 1 33 9 47 17 7 5 17 10 22 17 4 4 6 10 8 14 1 4 3 7 4 11v1c-2-2-4-4-7-5l-27-16c-19-9-42-17-63-18-20-2-42 2-61 8h-1l1-1-2-1c0-1 1-1 1-2-1-3-4-5-6-6-6-5-12-8-18-13-3-3-6-6-9-10h0z" class="E"></path><path d="M196 469c1-1 2-1 4-1 1 1 3 2 4 3s1 3 0 4c0 3-1 3-3 4-1 1-2 1-3 1-2 0-3-1-4-3-1-1-2-3-1-5 0-1 1-2 3-3z" class="V"></path><path d="M172 394c-6-3-11-4-17-6-4 0-7-1-11-1l-1-1c3 0 7 1 10 1 8 1 16 3 23 6 4 2 8 3 13 4l6 3c1 1 2 2 3 2 5 2 10 5 15 9l13 10c3 6 10 9 13 14 1 0 2 0 3 1s2 3 2 5h-1c2 1 2 3 3 5l6 15-1 1c1 1 2 2 3 4 1 3 5 7 4 11 2 2 3 4 3 6l1 3v1h-2c-3-1-9-6-11-7-5-3-10-6-14-8l-1 1 8 4c1 1 1 1 1 2-14-8-30-16-47-17-7-1-13 0-20 0h-2c0-2 0-3-1-4v-1l-1 1-2-2c0-2-1-3-1-4-4-5-10-7-16-8h-1c4-2 7-3 10-5 2-1 7 6 9 7h5c8-1 15-1 23-1-3-5-6-8-10-12v-1c5 4 8 8 11 13l6 1c-3-6-5-11-8-15v-1-1c4 5 7 11 10 16l6 2-5-11c2 2 3 4 4 6 1 1 2 4 3 5h1c-1-5-4-10-7-14-8-11-17-17-31-19l1 13c-1 0-3-1-4-2-4-2-7-4-11-5-1 1-2 1-2 2l-1 1h0v-2h0-1-1l1-1c2-1 5-3 7-4 6-4 12-6 19-7-1 0 0 0 0-1h5 0c0-1 1-2 1-3-1 0-2 0-2-1h-1-1l-1-1c-5-2-15-1-20 1-15 4-27 15-34 28v1h0l1-3c4-12 15-24 27-29 4-1 8-3 13-3h10l-13-4z" class="E"></path><path d="M188 403c8 1 14 5 21 9 2 2 4 3 6 5-7-4-13-7-21-8-2 0-6 1-7 0-1 0 0 0 0-1h5 0c0-1 1-2 1-3-1 0-2 0-2-1h-1-1l-1-1z" class="Z"></path><path d="M251 462c-3-2-6-5-9-8s-4-7-7-10c-2-2-5-4-8-5 5-2 12 0 16 2 2 1 2 3 3 5l6 15-1 1z" class="V"></path><path d="M194 453c-3 0-6-1-10-1-3-1-8 0-11-1 0-1-1-2-1-3 2-1 5-1 7-1 13-2 27 0 40 3l18 6c6 3 11 7 17 10 1 3 5 7 4 11-19-13-41-20-64-24z" class="Q"></path><path d="M194 453c23 4 45 11 64 24 2 2 3 4 3 6l1 3v1h-2c-3-1-9-6-11-7-5-3-10-6-14-8l-1 1 8 4c1 1 1 1 1 2-14-8-30-16-47-17-7-1-13 0-20 0h-2c0-2 0-3-1-4v-1-2c5-3 16-1 22-1l-1-1z" class="V"></path><path d="M194 453c23 4 45 11 64 24 2 2 3 4 3 6-4-4-10-8-15-11-16-9-33-15-51-18l-1-1z" class="B"></path><defs><linearGradient id="q" x1="727.434" y1="770.237" x2="698.299" y2="842.824" xlink:href="#B"><stop offset="0" stop-color="#d5d3ca"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#q)" d="M672 764c0-4-1-6-2-10 3 0 5-1 8-1 1 0 3 0 4-1 3 1 5 7 7 9 3 5 3 9 4 14v8h1c0-2 1-4 1-6 1 11 4 24 12 31v1c2 1 3 2 5 3l2 1 2 1 3 6c6 7 17 11 26 9 3-1 5-2 7-3 3-2 5-5 8-7l3-6 3-5 1 1 1-2 1 2h1c1 2 2 4 2 7 1 1 1 2 1 4-1 5-2 13-6 17 0-1-1-1-2-2l-3 3c-8 9-19 12-31 12-14 1-30-4-43-11-5-2-9-5-13-8 2-2 4-4 7-5-3-10-6-19-7-29-1-11-1-22-3-33z"></path><path d="M772 816c1 1 1 2 1 4-1 5-2 13-6 17 0-1-1-1-2-2 4-7 6-12 7-19z" class="H"></path><path d="M763 813c2 1 3 1 4 2 0 1-2 2-3 4-3 4-6 8-10 11l-1 1-2 1c-1 1-2 1-2 1l-2 1c0-1 0-1-1-1 1-2 3-2 5-3v-1l1 1 3-3h0c0-2 1-2 2-3h-1l-3 2h-1c3-2 5-5 8-7l3-6z" class="T"></path><path d="M682 794h1c0 1 0 2 1 4 4 15 12 27 26 35 10 5 24 9 35 6 3-1 5-2 7-3h1v1c-3 1-6 3-9 3-13 3-27 1-38-7-10-6-18-17-22-29-1-3-1-7-2-10z" class="a"></path><path d="M695 777c1 11 4 24 12 31v1c2 1 3 2 5 3h-1-2v1c2 2 8 8 8 10 0 1 0 2 1 2h0c2 2 4 4 5 6h0c1 0 2 1 2 1 1 0 2 1 3 1h2l-1 1c-1 1-3 0-4-1l-6-3h-1l-3-2-7-7c-1-2-2-3-3-4-6-11-11-22-12-34h1c0-2 1-4 1-6z" class="K"></path><path d="M707 809c2 1 3 2 5 3h-1-2v1c2 2 8 8 8 10 0 1 0 2 1 2h0c2 2 4 4 5 6h0c-1 0-2-1-3-1l-2-2c-1 0-1-1-2-1l-1-1v-1l1-1-3-3c-2-2-3-4-4-7-1-1-2-3-3-4l1-1h0z" class="I"></path><path d="M723 831h0c-1-2-3-4-5-6h0c-1 0-1-1-1-2 0-2-6-8-8-10v-1h2 1l2 1 2 1 3 6c6 7 17 11 26 9 3-1 5-2 7-3h1l3-2h1c-1 1-2 1-2 3h0l-3 3-1-1v1c-2 1-4 1-5 3 1 0 1 0 1 1-2 0-3 1-5 1l-2 1c-3 0-5 0-8-1-1 0-2 0-3-1l1-1h-2c-1 0-2-1-3-1 0 0-1-1-2-1z" class="M"></path><path d="M378 759c4 7 6 15 9 22l15 37 13 29c1 4 3 7 3 11h0v2c-1 0-1 0-2 1h0c-2 1-3 4-4 6l-6 10c-1 2-1 4-2 5l-5 10h0c-1-3 10-21 12-25s4-7 2-12h0c0-2-1-3-2-4l-13 20c-5 8-9 15-13 23-1 2-3 5-3 7l-19 39-3 7-2 3-2 6 2 2c-1 2-1 3-1 5l-5 12 2 1v1c-6-3-18-11-21-17-2-4 0-11 1-16l-7 3v-2l2-1 8-15-1-1c-1 1 0 1-1 2v1c-1-1 2-3 1-5 1-4 7-11 10-14 2 0 4-3 5-5 3-2 5-5 8-8h1c8-7 12-16 17-25-2 0-2 0-3 1-1 0-2 1-3 1 1-1 1-2 1-3v-1c1-3 4-5 5-8 0-2 4-5 6-7l-1-1 13-14c3-4 5-9 6-14 0-2-1-4-2-6l-4-10c-1-1-3-2-4-3-4-3-8-3-13-2h-2c-1 1-2 1-3 2-1-1-1-1-1-2s2-3 2-5c3-7 6-11 11-16 0-1-1-1-2-2h1l-1-2c0-3-2-6-3-10v-2c-1 0-1-1-1-2-1 0-1 0-1-1v-3l1-1h0c-1-1 0-2-1-4z" class="Q"></path><path d="M357 947c2-3 3-5 6-7l-3 7-2 3c-1 0-1 0-2-1 0-1 1-1 1-2z" class="F"></path><path d="M351 974c1-1 2-4 2-6v-3l3-9 2 2c-1 2-1 3-1 5l-5 12-1-1z" class="W"></path><path d="M356 949c-1 1-1 1-2 1 0 1-1 1-2 1-2 1-6 3-8 2 1-4 6-8 9-11l6-6v1h-1c-2 4-7 8-10 12 3-1 6 0 9-2h0c0 1-1 1-1 2z" class="N"></path><path d="M346 912c2 0 4-3 5-5 3-2 5-5 8-8h1c-3 4-7 8-11 12-1 2-12 18-12 18l-1-1c-1 1 0 1-1 2v1c-1-1 2-3 1-5 1-4 7-11 10-14z" class="P"></path><path d="M329 944h1c2 0 4-1 6-2 3-1 6-2 9-2-5 4-9 7-10 14 0 2 0 4 2 6l14 14 1 1 2 1v1c-6-3-18-11-21-17-2-4 0-11 1-16l-7 3v-2l2-1z" class="J"></path><path d="M378 759c4 7 6 15 9 22l15 37 13 29c1 4 3 7 3 11h0v2c-1 0-1 0-2 1h0c-2 1-3 4-4 6l-6 10c-1 2-1 4-2 5l-5 10h0c-1-3 10-21 12-25s4-7 2-12h0c0-2-1-3-2-4 1-1-2-5-2-7l-7 10c-9 12-17 25-24 39-2 4-4 7-6 11v2c-1 0-1 1-1 2s-1 2-2 3h0c0-3 2-6 3-9s3-6 4-9c4-10 10-19 16-27l1-2c1-2 3-4 4-5l11-16-3-9c-2 4-4 8-7 11h0l-5 7c-1 0-1 2-2 2l-14 20c-2 0-2 0-3 1-1 0-2 1-3 1 1-1 1-2 1-3v-1c1-3 4-5 5-8 0-2 4-5 6-7l-1-1 13-14c3-4 5-9 6-14 0-2-1-4-2-6l-4-10c-1-1-3-2-4-3-4-3-8-3-13-2h-2c-1 1-2 1-3 2-1-1-1-1-1-2s2-3 2-5c3-7 6-11 11-16 0-1-1-1-2-2h1l-1-2c0-3-2-6-3-10v-2c-1 0-1-1-1-2-1 0-1 0-1-1v-3l1-1h0c-1-1 0-2-1-4z" class="L"></path><path d="M394 807c2 2 5 8 6 12 0 1 0 2-1 3l-4-10c0-2-1-3-1-5z" class="K"></path><path d="M401 828l1 3c1 3-2 7-3 9l-3 5-1-3c3-4 5-9 6-14z" class="W"></path><path d="M395 842l1 3c-3 2-5 5-7 8 3 0 6-8 9-8l-5 7c-1 0-1 2-2 2h-1c-1 1-2 3-3 4v-1c-1-2 4-5 2-7-2 2-4 5-6 7l-1-1 13-14z" class="b"></path><path d="M383 857c2-2 4-5 6-7 2 2-3 5-2 7v1c1-1 2-3 3-4h1l-14 20c-2 0-2 0-3 1-1 0-2 1-3 1 1-1 1-2 1-3v-1c1-3 4-5 5-8 0-2 4-5 6-7z" class="H"></path><path d="M385 786c3 5 5 12 7 18 1 0 1 3 2 3 0 2 1 3 1 5-1-1-3-2-4-3-4-3-8-3-13-2h-2c-1 1-2 1-3 2-1-1-1-1-1-2s2-3 2-5c3-7 6-11 11-16z" class="Q"></path><path d="M805 457c9-2 17-4 26-4 6 0 12 1 18 1-1 3-2 4-1 7l1-2c1 3 1 7 4 10 2 3 7 5 11 4 3 0 11-2 14-4-4-1-11-1-14-5v-1l1 1c3 3 5 3 9 3 1 0 3 0 3 1 4 0 8 1 12 1 6 1 13 3 19 2 12-1 21-4 28-13h1c-2 3-5 7-8 10-4 3-8 5-12 7l-9 9c-1 1-2 3-3 4l7 3h-1-3c0 1 2 2 3 3-7-3-15-6-23-8-39-8-79-3-115 14l-2 1c-6 2-11 5-15 8h0c-5 2-9 4-13 7-3 3-5 5-8 6 0-1 1-3 2-5 1-4 3-8 5-12 1-6 7-9 9-14v-1l-2-2v-1c0-3 3-7 5-9 3-3 6-4 9-6 13-8 27-13 42-16v1z" class="E"></path><path d="M881 471h3c-3 2-5 5-8 7l-2 1v-1c1-3 3-5 7-7z" class="U"></path><path d="M897 474h1 3c1 1 2 1 3 1l-1 1c-2 1-3 3-5 4h-1c-1-1 0-2 0-4h-1c-1 1-1 1-2 1h-1c1-2 3-2 4-3z" class="D"></path><path d="M820 469s1-1 2-1c2 0 4 1 5 3 1 1 1 2 1 4-1 2-2 3-4 4-1 0-2 1-3 1s-3-1-4-3c-1-1-1-3-1-4 1-2 2-3 4-4z" class="V"></path><defs><linearGradient id="r" x1="758.719" y1="498.122" x2="770.824" y2="468.981" xlink:href="#B"><stop offset="0" stop-color="#9b9590"></stop><stop offset="1" stop-color="#aeb0aa"></stop></linearGradient></defs><path fill="url(#r)" d="M751 490c9-8 20-14 32-19 4-1 7-3 12-3l-15 7c-14 8-28 18-38 30 1-6 7-9 9-14v-1z"></path><path d="M805 457c9-2 17-4 26-4 6 0 12 1 18 1-1 3-2 4-1 7l2 6c-3-1-4-3-7-5h-6c-8-1-16-1-25 1-6 1-12 3-17 5-5 0-8 2-12 3-12 5-23 11-32 19l-2-2v-1c0-3 3-7 5-9 3-3 6-4 9-6 13-8 27-13 42-16v1z" class="Q"></path><path d="M754 478c3-3 6-4 9-6 13-8 27-13 42-16v1c-16 4-31 11-45 21-3 2-8 6-11 9 0-3 3-7 5-9z" class="C"></path><path d="M133 136c4 1 9 0 13 0h1c1-2 3-3 4-4 4-4 7-7 11-10l7-5v1l-19 16-2 2h23 15c2 0 4 1 5 0h2l63 1h25c3 0 8 0 11-1h1 59l-8-10h0c2 1 4 4 6 6 1 1 3 4 4 4 1 1 6 0 8 0h3 2 1c1-1 1-2 2-3v3h7 20 7 15c3 0 7 0 10 1 2 0 3 1 4 2 2 1 2 3 1 5-1 8-5 18-9 26-1 3-3 6-3 9 6 1 12 3 18 6h-1l-17-5c-2 0-3 5-4 6v1l-2 7-1 1c-2 1-3 3-5 5h1-7-17-4l1-1c3-1 7-2 10-2l12-1h4c0-1 1-2 1-3 2-4 4-9 6-14 3-6 15-31 13-36 0-1-1-2-2-2-3-1-7-1-10-1h-22l-85 1H109 63c-4 0-11-1-14 0l-3 3v5c3 7 7 14 10 21 5 9 8 18 13 27 7-2 14-3 21-3 15-1 28 4 40 12 16 11 28 25 38 43 9 16 15 33 22 50l16 37 20 50 39 97 33 80 29 70-1 1c-1-2-2-3-2-5h0c-1-2 0 1-1-2l-3-5v-2c-1-1-3-2-4-2-4-10-7-18-13-26-3-4-4-7-6-11l1-1c2-6-10-29-12-36-1-1-2-3-2-4v-2c-1-2-2-3-3-4s-3-3-4-3v-1c-1 0-2-1-2-1-3-1-6-3-8-5-1 0-2-1-2-2l-3-1c-1-1 0-1-1-2l1-1 14 8v-1c-1-1-4-2-6-4 3 1 5 2 7 3-2-2-5-4-6-6l-1-1c3 1 5 3 7 5v-1c-1-4-3-7-4-11-2-4-4-10-8-14-5-7-15-12-22-17 0-1 0-1-1-2l-8-4 1-1c4 2 9 5 14 8 2 1 8 6 11 7h2v-1l-1-3c0-2-1-4-3-6 1-4-3-8-4-11-1-2-2-3-3-4l1-1-6-15c-1-2-1-4-3-5h1c0-2-1-4-2-5l-15-29c1-1 1-1 1-2 1-5-8-19-10-25h0c0-1-1-3-2-4-4-7-9-15-18-18h0c-1-4-1-8-3-12 0-2-1-4-2-5l-2-5c0-2-1-3-1-5l-1-1c1 0 0 0 1-1-4-5-4-13-6-19-1-2-1-4-2-5l-2 1v-3c1-2 3-3 4-4l1-1-9-19v7l-3 9c-1-3 0-5 1-7 1-5 2-8 1-12l-8-20c-1-3-2-7-5-8v-1c0-2-1-5-3-6l-1-1h-2l1-1c-2-5-7-10-10-14l-1 1c-1-1-1-2-2-2l-1-1c-1-2-2-4-4-5-1-2-7-4-8-5s-1-1-3-2v-1c-1-2-3-2-4-3s-2-1-2-1v-1l-2-1h-1l-2-1h-1c0-1-1-1-2 0l-3-1-5-3c-5-1-9-1-14-1-2 0-4-1-5 0-4-1-7 0-11 2-1 0-3 1-4 2h-1l-1 1c-1-1-2-1-3-2-5-8-9-18-13-26s-9-17-11-26c-1-1-1-4-1-6 1-4 4-5 7-6 5-1 12-1 17-1h41c7 0 14 1 21 0 1 0 2 0 3-1h0l2 1z" class="X"></path><path d="M270 517c3 1 5 3 7 5 2 4 4 7 5 11-2-1-4-3-6-5-2-1-5-3-7-4-1 0-1 0-2-1-1 0-1-1-2-1l-3-1c-1-1 0-1-1-2l1-1 14 8v-1c-1-1-4-2-6-4 3 1 5 2 7 3-2-2-5-4-6-6l-1-1zm-85-219c2 2 2 4 3 7 4 7 7 15 10 23l-1 1-1-2c-2 0-3 0-4 1s-1 1-1 2l1 1h-2l-1-1c1 0 0 0 1-1-4-5-4-13-6-19-1-2-1-4-2-5l-2 1v-3c1-2 3-3 4-4l1-1z" class="M"></path><path d="M192 328c-1-1-1-1-1-2l-3-10c-1-2-2-5-1-7 2 7 4 13 9 18-2 0-3 0-4 1z" class="H"></path><path d="M184 299c1 2 1 2 1 3v1l1 2v1c0 1 1 2 1 3-1 2 0 5 1 7l3 10c0 1 0 1 1 2-1 1-1 1-1 2l1 1h-2l-1-1c1 0 0 0 1-1-4-5-4-13-6-19-1-2-1-4-2-5l-2 1v-3c1-2 3-3 4-4z" class="S"></path><path d="M196 327l1 2c1 2 2 3 2 5 3 4 5 9 7 14l9 22c0 2 1 4 1 6-4-7-9-15-18-18h0c-1-4-1-8-3-12 0-2-1-4-2-5l-2-5c0-2-1-3-1-5h2l-1-1c0-1 0-1 1-2s2-1 4-1z" class="V"></path><path d="M196 327l1 2c1 2 2 3 2 5l-7-3-1-1c0-1 0-1 1-2s2-1 4-1z" class="D"></path><path d="M736 532c16-9 33-16 51-18v1h-1l-8 1-9 3h1c2 0 3 0 5-1h2c1-1 0 0 1 0 1-1 2-1 3-1h2 0c1-1 1-1 2-1h1 1l2 1h-4l-1 1h-1 0l9-1c7-1 11 1 16 5h1c0 2 1 3 2 4h-1l-2-2v-1l-1-1c-1 1-3 1-3 3h0v1c2 1 4 2 6 4 7 4 14 8 20 13 5 4 8 10 10 16l4 11c-5-3-9-6-15-8 0 1-1 1-1 2h0v1c-7-2-14-3-22-3l-9 1c-1-1-2-1-3-3-3 1-6 2-8 4v2h2c0 3 1 4 3 6v2c-1 1-1 2-1 3 2 4 5 7 8 10h0l2 2h-5l-20-3v1l-9-2c-5 0-14-1-18 1h0-1v2 1l1 1-2 1v-1h-9c-3-1-5-1-8-1 1-1 3-2 5-3v-1h-1c-1 2-4 3-6 5-2 1-4 3-5 5-2 1-2 2-3 4l-1 1-7 3c2-4 4-10 8-14l3-6c-3 3-7 9-9 14-1 2-3 5-4 8-3 2-7 5-10 7v-2c1-1 1-2 2-3v-2l1-1c1-3 4-7 4-11 0-1 1-3 2-4 1-4 4-7 5-11h0c-1-6 7-22 10-27l3-10c2-3 5-5 7-6l3-3z" class="Q"></path><path d="M733 575h0c-1 2-2 3-4 4l1 1c1 0 1-1 2-1h0l1-1c1 0 2 0 3-1l1 1c-1 0-1 0-2 1-3 0-6 3-9 5 0-1 0-3-1-4l8-5z" class="F"></path><path d="M812 548h2c2 1 2 1 2 3-1 2-1 2-2 3-1 0-2 0-3-1s-1-1-2-3c1-1 1-1 3-2zm-18 12c4-1 9-1 13-1l-1 3-9 1c-1-1-2-1-3-3z" class="C"></path><path d="M747 586c0-1 0-2 2-3 6-3 20 1 26 3v1l-9-2c-5 0-14-1-18 1h0-1z" class="B"></path><path d="M788 566c0 3 1 4 3 6v2c-1 1-1 2-1 3 2 4 5 7 8 10h0c-1 0-1 0-2-1-4-2-9-6-10-10l2-2c0-3-1-5-2-8h2z" class="M"></path><defs><linearGradient id="s" x1="807.533" y1="557.724" x2="822.984" y2="564.738" xlink:href="#B"><stop offset="0" stop-color="#050105"></stop><stop offset="1" stop-color="#3c3d35"></stop></linearGradient></defs><path fill="url(#s)" d="M807 559c4 0 8 0 13 1 2 0 7 2 9 2 0 1-1 1-1 2h0v1c-7-2-14-3-22-3l1-3z"></path><path d="M713 578c6-11 13-20 24-27 2-1 5-3 7-3h1c-10 7-19 14-26 24-1 1-3 3-3 5-4 5-7 11-10 16 0-1 1-3 2-4 1-4 4-7 5-11z" class="B"></path><path d="M726 584c3-2 6-5 9-5-4 2-8 6-12 10 0 2-3 4-4 6 4-2 7-6 11-9 1 0 2-1 3-1-1 2-4 3-6 5-2 1-4 3-5 5-2 1-2 2-3 4l-1 1-7 3c2-4 4-10 8-14l3-6 3-3c1 1 1 3 1 4z" class="R"></path><path d="M725 580c1 1 1 3 1 4-2 2-4 3-6 5h-1l3-6 3-3z" class="T"></path><defs><linearGradient id="t" x1="708.388" y1="608.338" x2="711.853" y2="583.979" xlink:href="#B"><stop offset="0" stop-color="#bbb5a6"></stop><stop offset="1" stop-color="#e9e8e4"></stop></linearGradient></defs><path fill="url(#t)" d="M716 577v1c-1 2-2 4-4 7h4 2l1-1h1s1-1 1-2l1-1c1-1 1-1 1-2h1c0 2-1 2-2 4-3 3-7 9-9 14-1 2-3 5-4 8-3 2-7 5-10 7v-2c1-1 1-2 2-3v-2l1-1c1-3 4-7 4-11 3-5 6-11 10-16z"></path><path d="M724 579l1-1c0-2 1-3 2-4 4-4 9-7 13-9 5-4 9-7 14-9 18-10 40-18 61-12-3 1-6 1-9 1-4 0-9 0-13 1-22 3-42 15-60 28v1l-8 5-3 3c1-2 2-2 2-4z" class="E"></path><path d="M736 532c16-9 33-16 51-18v1h-1l-8 1-9 3h1c2 0 3 0 5-1h2c1-1 0 0 1 0 1-1 2-1 3-1h2 0c1-1 1-1 2-1h1 1l2 1h-4l-1 1h-1 0l9-1c7-1 11 1 16 5h1c0 2 1 3 2 4h-1l-2-2v-1l-1-1c-1 1-3 1-3 3h0v1l-1-1v-2c-4-1-8 0-13 0-6 1-13 2-20 3-5 2-9 3-14 5 3 0 6-1 9-1 9 0 22 0 29 6-13-1-27-3-40 1-2 1-6 3-9 1v-2c-2 0-4 1-5 2-4 2-7 5-10 7-2 2-5 5-7 6l3-10c2-3 5-5 7-6l3-3z" class="C"></path><path d="M733 535h3 1 1 0l-2 1c-4 2-5 5-10 5 2-3 5-5 7-6z" class="H"></path><path d="M736 532c16-9 33-16 51-18v1h-1l-8 1-9 3h1c2 0 3 0 5-1h2c1-1 0 0 1 0 1-1 2-1 3-1h2 0c1-1 1-1 2-1h1 1l2 1h-4l-1 1h-1 0c-2 1-3 1-4 1l-1 1h-1-1l-2 1h-2c-2 1-4 1-5 2-2 0-3 1-4 1l-3 1-12 6c-2 1-4 2-6 2-1 1-3 2-4 2h0-1-1-3l3-3z" class="D"></path><path d="M786 387l2-2v1l4-7 2-2-11 29h0v2c0 3-3 7-3 9l1 1c0 2-1 5-3 6h0v-3c-2 2-10 20-10 23h0c-3 5-7 12-8 17 1 0 2 0 3-1h1l5-2-7 5h0c-5 4-7 9-8 14v1c-2 2-5 6-5 9v1l2 2v1c-2 5-8 8-9 14-2 4-4 8-5 12-1 2-2 4-2 5s0 2-1 2c0 2-1 1-1 3 1-1 3-1 4-2l-3 3c-1 0-2 1-3 2l1 1c-1 1-2 1-2 3 1 0 2 0 3-1l2-2 1 1-3 3c-2 1-5 3-7 6l-3 10c-3 5-11 21-10 27h0c-1 4-4 7-5 11-1 1-2 3-2 4 0 4-3 8-4 11l-1 1v2c-1 1-1 2-2 3v2c-2 2-5 5-5 8h0c11-9 21-18 36-17 4 0 10 1 13 4 1 2 0 4-1 6-3 6-10 6-10 13-1 2-1 4-1 6l-1 1v1l-17 12-19 16c-1 1-3 3-4 3l-1-1-8 7-12 13c-5 7-8 14-11 22h2c-1 1-2 1-3 2-1 2-2 5-3 8h1l-1 1s0 1-1 1c-1 2-2 5-3 8h2c-2 1-3 3-3 5-1 1-1 1 0 2l-1 2c-1 1-1 1-1 2v2l2 3c1 1 1 2 2 3v1 1l-3-5-2-2h-1c-1 2-4 4-4 7h1c-2 4-4 8-5 12-1 1-2 3-2 4v1c0 1-1 2-1 3l-3 6 3 1c4 3 7 7 10 11h0c1 2 2 3 2 5l1 1c0 1 1 2 1 3v3h1c0 2 0 6-1 8v1l-1 1c-1 1-1 1-1 2l-2 1c-1-1-1-1-2-1-1 1-2 1-3 1s-1 0-2 1h0v-2-1-1c0-8-6-15-12-20l-6 16c-2 2-3 6-3 8-3 6-7 13-7 19-3 2-5 8-6 11 10 16 24 30 31 48v1c1 1 1 2 1 4h0l-5-10c-1-1-1-1-1-2l-2-3-25-36c-1 2-3 6-3 8h0l-1 4c-1 1-1 2-1 3 1 2 0 1 0 2 1 3 3 6 4 9 2 3 4 7 5 11l3 8 1 1v1l1 4 1 1 2 7h-1c-3-8-6-17-10-26-2-5-6-11-7-16-1 1-2 3-1 5v1c1 4 1 8 3 12v2l1 8c1 1 1 1 1 2l-1 1c0-2-1-4-1-6 0-1 0-2-1-2l-3-14v-1c0-1 0-2-1-3-1 5 0 11 1 17v3l1 5-1 1c-1-4-1-8-2-11l-1-8c-4 7-7 14-8 22v-2c-1 2-3 5-3 8l5 9 1 3c2 2 4 4 5 7h0c0 1 1 1 1 2h0 0v1l-9-6-1-1h-1l-2-2h-1l-1 2-1 1c-2 2-7 7-7 11v1 3c2 2 4 4 6 7v5l-1 5 2 1c-3 7-7 10-13 13l-8 2v1l-2 1-2 3c-2 5-9 24-12 25h-69c-4-7-7-16-10-24h-1-1l1-1c-1-2-1-2-3-3h1v-1l-2-2c1-1 1-1 1-2l1 1 1 1v-1c-1-1-1-3-1-5v-1l1 1 1 1 14 33h65l248-604z" class="K"></path><path d="M749 488l2 2v1c-2 1-4 3-6 4h-1c2-3 3-5 5-7z" class="I"></path><path d="M575 938l-1 3c-2-2-4-4-5-6 0-2-1-3 0-4 2 2 4 4 6 7z" class="O"></path><path d="M580 900l5 9c-4-1-4-1-8 0l-3 3 1-2c0-2 2-5 3-7 1-1 1-2 2-3z" class="L"></path><path d="M569 947c-1 0-4-7-5-9l2-4h1l2 3-1 1h0l1 1v1c1 2 1 4 0 6v1z" class="F"></path><path d="M569 947v-1c1-2 1-4 0-6v-1l-1-1h0l1-1 6 6-1 5c0 2-1 3-2 4l-1-1c0-2-1-3-2-4z" class="G"></path><path d="M574 912l3-3c4-1 4-1 8 0l1 3c2 2 4 4 5 7h0c0 1 1 1 1 2h0 0v1l-9-6-1-1h-1l-2-2h-1l-1 2-1 1v-1l-1 1-1-1v-3z" class="I"></path><path d="M561 947l2-7 7 11h1l1 1c1-1 2-2 2-4l2 1c-3 7-7 10-13 13l-8 2h0c0-3 2-9 4-11v1c3 1 2 3 4 4h1c-1-2-2-5-3-7l-1-1 1-2v-1z" class="C"></path><path d="M561 947l2-7 7 11c1 1 1 2 1 3-2 2-3 3-5 4l-5-11zm65-162l5-11c3 2 6 5 9 8v1c3 3 4 5 5 9l2 2v3h0c1 2 0 6 0 8v2c-1 1-1 1-1 2l-2 1c-1-1-1-1-2-1-1 1-2 1-3 1s-1 0-2 1h0v-2-1-1c0-8-6-15-12-20l1-2z" class="G"></path><path d="M626 785c6 3 10 9 12 15v7l-1 1v-1c0-8-6-15-12-20l1-2z" class="D"></path><path d="M694 620c11-9 21-18 36-17 4 0 10 1 13 4 1 2 0 4-1 6-3 6-10 6-10 13-1 2-1 4-1 6l-1 1v1l-17 12-19 16c-1 1-3 3-4 3l-1-1-8 7-12 13c0-1 0-1 1-2v-1c2-6 6-12 10-16 4-5 7-10 11-15 7-9 15-16 23-23v-1c-7 6-13 11-19 17l-8 9 4-7v-1l-15 22c2-9 6-17 9-25 3-6 4-12 7-17l1-3 1-1z" class="V"></path><path d="M699 621h1c4 1 10 0 14 2l-1 1c-3 1-5 1-8 1s-4-2-6-4z" class="U"></path><path d="M711 645c2-2 4-3 6-5 4-2 9-5 13-7v1l-17 12c0-1 0 0 1-1 0-1 0 0 1-1-1 0-2 1-3 1h-1z" class="L"></path><path d="M669 684c0-1 0-1 1-2v-1c2-6 6-12 10-16h0c0 3-2 5-3 6h-1c-1 2-2 3-2 6h1c0-1 4-4 4-5l2-1-12 13z" class="T"></path><path d="M711 645h1c1 0 2-1 3-1-1 1-1 0-1 1-1 1-1 0-1 1l-19 16c-1 1-3 3-4 3l-1-1h0l14-13c2-2 5-4 8-6z" class="K"></path><path d="M635 894c0-2 0-3-1-4v-1c-7-18-21-32-31-48 1-3 3-9 6-11 1 3 1 5 2 7 4 9 10 17 16 24l34 50 7 10c1 2 2 5 4 6 0 2 3 7 4 8h2 2l1-2 1 2-2 2 1 4c0 1 2 4 2 5l-7-1c1 4 2 9-1 14-2 5-9 8-13 11h0c-4 3-9 7-13 8h0l1-1c-1-2-8-4-10-5-5-3-7-8-8-13l-7 1v-2h0v-3l-2-3c-2 1-3 2-6 3l-1-1c-1-1-1-2-1-3-3-3-5-6-7-9-2-5-2-9-2-14l-2-2c0-4 0-8-2-12v-2h1l1-1 1-1c0-1 1 0 2 0l1 1h0c-2-3-3-4-6-4-2 1-2 2-3 4-1 3-1 6-1 10h-1s-1 0-2 1c-1-1-3-2-4-3-1-3-3-5-5-7l-1-3-5-9c0-3 2-6 3-8v2c1-8 4-15 8-22l1 8c1 3 1 7 2 11l1-1-1-5v-3c-1-6-2-12-1-17 1 1 1 2 1 3v1l3 14c1 0 1 1 1 2 0 2 1 4 1 6l1-1c0-1 0-1-1-2l-1-8v-2c-2-4-2-8-3-12v-1c-1-2 0-4 1-5 1 5 5 11 7 16 4 9 7 18 10 26h1l-2-7-1-1-1-4v-1l-1-1-3-8c-1-4-3-8-5-11-1-3-3-6-4-9 0-1 1 0 0-2 0-1 0-2 1-3l1-4h0c0-2 2-6 3-8l25 36 2 3c0 1 0 1 1 2l5 10h0z" class="Q"></path><path d="M681 933l1 2-2 2 1 4-1 1-4-7h2 2l1-2z" class="E"></path><path d="M586 912h1l3 4v-1c0-1-1-1-1-2l-1-1v-2c3 5 5 8 9 11 0 0-1 0-2 1-1-1-3-2-4-3-1-3-3-5-5-7zm18-1c2 6 2 11 2 17l-2-2c0-4 0-8-2-12v-2h1l1-1z" class="B"></path><path d="M641 935c3 6 6 14 9 20h-3c-2-6-6-14-6-20z" class="F"></path><path d="M580 900c0-3 2-6 3-8v2l5 16v2l1 1c0 1 1 1 1 2v1l-3-4h-1l-1-3-5-9z" class="J"></path><path d="M635 894c1 0 1 1 1 2 1 1 1 2 1 3l3 9s0 1 1 1v1l1 2c0 1 1 1 1 2v1c1 2 1 4 2 5 0 1 0 0-1 1-1-1-1-2-2-3 0-2-1-3-1-4v-1c-1-1-1-1-3-1-1-3-5-9-4-13 1-2 1-2 1-5h0 0z" class="F"></path><path d="M625 958c5-1 12-3 18 0 1 1 1 2 2 2-4 0-7-1-11-1 0 1 0 0 1 1 1 2 2 6 3 8 4 4 11 5 15 8l7-7 2 1h0c-4 3-9 7-13 8h0l1-1c-1-2-8-4-10-5-5-3-7-8-8-13l-7 1v-2z" class="B"></path><path d="M615 951l2 2c2 0 3-1 4-3 1-1 1-3 1-4-1-4-5-5-5-9 2-1 2-1 3-2 2 0 3 1 5 2 2 2 3 8 3 11s-2 8-3 10v-3l-2-3c-2 1-3 2-6 3l-1-1c-1-1-1-2-1-3z" class="E"></path><path d="M623 952c0-2 2-4 2-6 0-1-2-5-3-7 2 3 6 7 5 10l-2 6-2-3z" class="O"></path><path d="M660 969c4-3 10-8 12-12 1-2 2-4 2-6-1-7-7-6-8-9l1-1c5 0 9 1 13 3v-2l1-1c0 1 2 4 2 5l-7-1c1 4 2 9-1 14-2 5-9 8-13 11l-2-1z" class="J"></path><path d="M599 851c8 9 15 20 21 30 6 12 11 26 16 39 4 8 22 46 21 51v1c-1-1-1-1-1-2l-6-15c-3-6-6-14-9-20l-2-3-7-13c-1 6-1 12-3 19 0-10 0-20-2-29-1-6-4-11-6-16l-12-22c-4-5-6-12-11-16l1-4z" class="C"></path><path d="M649 733c1-1 1-1 2 0 1 0 2 1 3 2h3c6 3 10 13 12 20l3 9c2 11 2 22 3 33 1 10 4 19 7 29-3 1-5 3-7 5 4 3 8 6 13 8 13 7 29 12 43 11 12 0 23-3 31-12l3-3c1 1 2 1 2 2l-3 6c-2 3-5 5-8 8l-11 6-9 2c-2 2-4 3-5 4-3 2-5 5-7 6-3 2-5 4-9 5-3 2-6 4-9 5h0l-3 1v1h-1c-3 1-8 4-11 3l-1 1h-2c-1 0-1 0-2 1h-1-2-1c-1 1-1 1-2 1-1 1-3 1-4 1-2 1-4 1-6 1v1h0c-2 0-4-2-6-3-5-3-9-6-13-10-3 0-6-4-9-6l-8-8c-3-2-7-7-10-10 1 4 4 5 3 8-6-7-12-15-16-24-1-2-1-4-2-7 0-6 4-13 7-19 0-2 1-6 3-8l6-16c6 5 12 12 12 20v1 1 2h0c1-1 1-1 2-1s2 0 3-1c1 0 1 0 2 1l2-1c0-1 0-1 1-2l1-1v-1c1-2 1-6 1-8h-1v-3c0-1-1-2-1-3l-1-1c0-2-1-3-2-5h0c-3-4-6-8-10-11l-3-1 3-6c0-1 1-2 1-3v-1c0-1 1-3 2-4 1-4 3-8 5-12h-1c0-3 3-5 4-7h1l2 2 3 5v-1-1c-1-1-1-2-2-3l-2-3v-2c0-1 0-1 1-2l1-2z" class="E"></path><path d="M649 733c1-1 1-1 2 0 1 0 2 1 3 2l5 7h-2c-2-2-3-3-4-5-1-1-1-2-2-3l-2-1z" class="U"></path><path d="M621 835c3 2 5 4 8 5h-1-2c0 1 0 1 1 2v4h0c-2-1-4-3-6-4l1-1h1c-1-1-1-1-1-2-1-1-1-2-1-4z" class="X"></path><path d="M648 862c-7-2-13-6-19-10 6 2 11 3 18 3h-1-5c2 2 5 4 7 7z" class="K"></path><path d="M621 842c-6-6-7-12-7-19 0 0 0 1 1 1 1 5 3 8 6 11 0 2 0 3 1 4 0 1 0 1 1 2h-1l-1 1z" class="R"></path><path d="M611 837l2 1c1 1 2 3 3 4 2 4 5 7 8 10 2 2 3 4 5 5l6 6h-1c-3-2-7-7-10-10 1 4 4 5 3 8-6-7-12-15-16-24z" class="B"></path><path d="M716 853h1v1c-3 8-13 15-21 19-12 4-26 3-37-2-2-1-4-2-5-3l19 3c13 1 25-1 35-9 3-3 5-6 8-9h0z" class="U"></path><path d="M695 876c8-2 16-4 23-9 2-2 5-4 8-6 3-1 7-1 10-2-2 2-4 3-5 4-3 2-5 5-7 6-3 2-5 4-9 5-3 2-6 4-9 5h0l-3 1c-2 0-3 1-5 0h1c1-1 2-1 3-2h-2c-1 1-2 1-3 1h0c-1 0-1 0-2-1h-2v1-1l2-2z" class="S"></path><defs><linearGradient id="u" x1="644.609" y1="848.623" x2="670.49" y2="854.678" xlink:href="#B"><stop offset="0" stop-color="#c8c4bc"></stop><stop offset="1" stop-color="#edede7"></stop></linearGradient></defs><path fill="url(#u)" d="M647 855c7-1 16-3 21-10l3-4c1 4 2 10 0 15-2 3-6 6-10 7s-9 0-13-1c-2-3-5-5-7-7h5 1z"></path><defs><linearGradient id="v" x1="665.067" y1="868.795" x2="663.799" y2="880.621" xlink:href="#B"><stop offset="0" stop-color="#9e9a95"></stop><stop offset="1" stop-color="#c4c0b1"></stop></linearGradient></defs><path fill="url(#v)" d="M635 863c16 11 39 16 59 13h1 0l-2 2v1-1h2c1 1 1 1 2 1h0c1 0 2 0 3-1h2c-1 1-2 1-3 2h-1c2 1 3 0 5 0v1h-1c-3 1-8 4-11 3l-1 1h-2c-1 0-1 0-2 1h-1-2-1c-1 1-1 1-2 1-1 1-3 1-4 1-2 1-4 1-6 1v1h0c-2 0-4-2-6-3-5-3-9-6-13-10-3 0-6-4-9-6l-8-8h1z"></path><defs><linearGradient id="w" x1="667.632" y1="870.564" x2="685.67" y2="890.774" xlink:href="#B"><stop offset="0" stop-color="#b1aea3"></stop><stop offset="1" stop-color="#d1ccbf"></stop></linearGradient></defs><path fill="url(#w)" d="M651 877h0c2 1 3 1 4 2 2 1 5 1 8 1l1 1h2 2l1 1h2c2 0 6 0 8 1l7-1 3-1h2l2-1h3l1-1h0c1 0 2 0 3-1h2c-1 1-2 1-3 2h-1c2 1 3 0 5 0v1h-1c-3 1-8 4-11 3l-1 1h-2c-1 0-1 0-2 1h-1-2-1c-1 1-1 1-2 1-1 1-3 1-4 1-2 1-4 1-6 1v1h0c-2 0-4-2-6-3-5-3-9-6-13-10z"></path><defs><linearGradient id="x" x1="653.455" y1="794.934" x2="617.29" y2="795.57" xlink:href="#B"><stop offset="0" stop-color="#d1cfc4"></stop><stop offset="1" stop-color="#fdfcfc"></stop></linearGradient></defs><path fill="url(#x)" d="M635 763l8 7c-1-2-4-5-5-6 8 5 13 14 15 24 3 10 2 21-4 30-3 4-6 7-11 8-3 1-7 0-10-2-2-1-3-4-3-6-1-3 1-5 2-7l4-4c-2-1-3-1-5-1-4 0-7 2-10 5 0-2 1-6 3-8l6-16c6 5 12 12 12 20v1 1 2h0c1-1 1-1 2-1s2 0 3-1c1 0 1 0 2 1l2-1c0-1 0-1 1-2l1-1v-1c1-2 1-6 1-8h-1v-3c0-1-1-2-1-3l-1-1c0-2-1-3-2-5h0c-3-4-6-8-10-11l-3-1 3-6c0-1 1-2 1-3v-1z"></path><defs><linearGradient id="y" x1="632.397" y1="793.2" x2="670.779" y2="798.503" xlink:href="#B"><stop offset="0" stop-color="#afaa9a"></stop><stop offset="1" stop-color="#f3f3f2"></stop></linearGradient></defs><path fill="url(#y)" d="M649 733l2 1c1 1 1 2 2 3 1 2 2 3 4 5h2v1c8 10 10 25 11 38 0 6 1 11 0 17h0c1 15-1 30-11 42-6 5-12 9-20 9-4 0-9-1-12-3h0v-4c-1-1-1-1-1-2h2 1l3 1h7 0-3c-7-1-11-4-16-10l8 6h1 0c-3-3-5-6-8-8 4 2 8 4 13 4 6 0 11-3 14-7 9-8 10-20 10-31 0-13-3-25-8-36-2-4-5-9-8-12h-1c0-3 3-5 4-7h1l2 2 3 5v-1-1c-1-1-1-2-2-3l-2-3v-2c0-1 0-1 1-2l1-2z"></path><path d="M649 733l2 1c1 1 1 2 2 3 1 2 2 3 4 5h2v1 3l-1-1c-1 1-1 1-1 2l1 2 2 3c0 1 1 2 1 2l-1 1c-1-1-2-3-3-4v-1-1c-1-1-2-2-2-3h-2c0-1-1-1-1-2h-1v1c-1-1-1-2-2-3l-2-3v-2c0-1 0-1 1-2l1-2z" class="S"></path><path d="M649 733l2 1c1 1 1 2 2 3l-2-1c-1 1-1 1-1 3 0 0 1 1 1 2 0 0 1 1 1 2h0l-4-4h0-1v-2c0-1 0-1 1-2l1-2z" class="X"></path><path d="M629 837c1 0 2-1 4 0 2 0 4 1 6 0h1c3-1 5-3 8-4v-1l1-1c1-1 1-1 2-1 1-1 2-3 4-4v1c1 1 2 1 2 1h1l-2 2c-1 1 0 1-1 2s-3 3-5 4l-1 1-1 1-1 1-1-1h0c-1 1-1 1-2 1h-1c-1 1-1 1-2 1l-2 1h-3c-7-1-11-4-16-10l8 6h1z" class="Y"></path><defs><linearGradient id="z" x1="668.888" y1="760.587" x2="658.554" y2="767.016" xlink:href="#B"><stop offset="0" stop-color="#abacae"></stop><stop offset="1" stop-color="#d4cfc3"></stop></linearGradient></defs><path fill="url(#z)" d="M659 743c8 10 10 25 11 38 0 6 1 11 0 17h0 0v-2c-1-2-1-3-1-5l1-1c0-2 0-4-1-6 0 3 1 9-1 12v-1c-1-3-1-6-1-9-1-2 0-3 0-4-1-2-1-3-1-4v-3c-1-1-1-2-1-3v-1-3c-1-1-1-2-1-3 0 0-1-1-1-2l-1-5-2-3 1-1s-1-1-1-2l-2-3-1-2c0-1 0-1 1-2l1 1v-3z"></path><path d="M340 677c2 2 3 2 4 4h0v-2h0 1c-1-2-1-2-1-3 9 17 15 36 22 54l8 18c2 4 3 8 4 11 1 2 0 3 1 4h0l-1 1v3c0 1 0 1 1 1 0 1 0 2 1 2v2c1 4 3 7 3 10l1 2h-1c1 1 2 1 2 2-5 5-8 9-11 16 0 2-2 4-2 5s0 1 1 2c1-1 2-1 3-2h2c5-1 9-1 13 2 1 1 3 2 4 3l4 10c1 2 2 4 2 6-1 5-3 10-6 14l-13 14 1 1c-2 2-6 5-6 7l-7 7-1-1 1-1-2-2c-1 1-2 1-3 1h-1c-5 2-11 5-17 6l-2-1c-8 2-15 2-24 1v1h3c-2 1-2 0-3 0l-2 1c-8 0-17-4-24-8l-9-6c-4-1-8-1-12-2-8-3-14-6-21-10h0l-3-2c-2-1-3-3-4-5-2-2-3-4-5-7-3-6-6-14-5-22h1c0-4 1-7 2-11 1-1 1-2 2-3h1c-1 1-1 2-1 3h0c0 2 0 3-1 4h0c1 4 1 6 3 10v1l3 3c0 1 1 2 2 3 0-1-1-2-1-3 3 4 6 8 10 9v-1h-1c-2-2-4-6-4-8 5 5 11 8 18 8 4 0 12-2 15-5l1-1h0c2-1 4-3 5-5v-2c1-1 1-1 1-2h1c1-1 3-2 5-2 3-2 5-5 8-8 2-3 4-7 5-10 2-5 3-9 3-15l2 2c0-8 2-14 6-20v-1l3-3c0-1 1-2 1-2h4 2 2v1h4v2 1h-1v1 1 2l2-6c0-1 1-3 2-4 2-7 4-12 9-18h-1c1-1 1-2 3-2h0l1 1-1 1v1c1-1 1-1 2 0h2l3-3 3-1h1c0-1-1-2-1-3l-2-3c0-2-1-5-2-8h0l-1-1c0-3-3-8-5-11h0c-1-5-3-10-5-14-2-5-5-10-7-15z" class="E"></path><path d="M372 832c6 1 10 1 15-2-2 3-4 6-8 7h-1c-1 1-2 1-3 1h0 0c-2 0-2 0-4-1h3c1-1 2-1 3-1l1 1v-1-2h-2c-1 0-2 0-3-1l-1-1z" class="S"></path><defs><linearGradient id="AA" x1="375.508" y1="861.159" x2="369.373" y2="868.392" xlink:href="#B"><stop offset="0" stop-color="#898885"></stop><stop offset="1" stop-color="#aba9a1"></stop></linearGradient></defs><path fill="url(#AA)" d="M382 856l1 1c-2 2-6 5-6 7l-7 7-1-1 1-1-2-2c-1 1-2 1-3 1h-1c6-3 13-7 18-12z"></path><path d="M381 840c6-3 12-7 14-14v-2c1-1 1-2 2-3-1 8-3 16-10 21v1c-2-2-4-2-6-3z" class="X"></path><path d="M361 855c6 0 12 0 18-3-7 7-18 12-28 12h-1c1-1 1-1 3-1 0-1 1-1 1-1 4-1 8-2 11-4 1-1 1-1 1-3h-5 0z" class="W"></path><path d="M349 735c1-1 1-2 3-2h0l1 1-1 1v1c1-1 1-1 2 0h2c-4 5-8 11-11 17l-3 8v-2-1-1h1v-2l1-1v-2l2-4h1c-1-1-2-1-3-2-2 4-3 8-4 11h-1c0-1 1-3 2-4 2-7 4-12 9-18h-1z" class="H"></path><path d="M321 874h0-2l-3-1c-12-4-19-11-24-21 2 2 4 5 6 7 3 2 5 5 8 6 16 11 36 5 53 2-5 2-9 4-14 6-8 2-15 2-24 1z" class="U"></path><path d="M350 864l-2-1c-4-1-7-3-9-6-3-4-2-11-1-16l3 5c5 5 13 8 20 9h0 5c0 2 0 2-1 3-3 2-7 3-11 4 0 0-1 0-1 1-2 0-2 0-3 1z" class="O"></path><path d="M363 772c1-1 2-3 3-4 2-3 5-7 8-8 0 1 1 1 1 2-3 1-4 3-6 5l-4 4v1 1l-1 1c-1 1-1 1 0 2 3-3 5-7 9-10l1-1c1 0 1-1 2-1 1 1 2 2 2 3s0 1 1 1c0 1 0 2 1 2v2c1 4 3 7 3 10l1 2h-1c1 1 2 1 2 2-5 5-8 9-11 16 0 2-2 4-2 5s0 1 1 2c1-1 2-1 3-2h2c3 1 5 3 6 6 1 2 1 5 0 8-1 2-3 4-5 4-4 2-8 0-12-2-5-3-8-9-10-14-4-14-1-26 6-37z" class="Y"></path><path d="M368 812h-1-2-1c-1 0-2 0-2-1-1-2-2-3-1-5 0-2-1-5 0-7 1-1 0-3 0-4l1-3h1v-3l2-2c0-1 1-2 2-3l1-1c1-3 4-5 6-7h1l-2 2-3 3c0 1-1 2-2 3h0c-1 1-2 2-2 4l-2 3v1c-1 0-1 1-1 2h0v2c0 1 0 1-1 2v5l1 1v2 1c1 2 4 4 5 5z" class="M"></path><path d="M380 772c1 4 3 7 3 10l1 2h-1c1 1 2 1 2 2-5 5-8 9-11 16 0 2-2 4-2 5s0 1 1 2c0 1 1 2 1 3-2-1-2-2-3-3l-1 3h-2c-1-1-4-3-5-5v-1-2l-1-1v-5c1-1 1-1 1-2v-2h0c0-1 0-2 1-2v-1l2-3c0-2 1-3 2-4h0c1-1 2-2 2-3l3-3 2-2s1-1 2-1l3-3z" class="G"></path><path d="M380 772c1 4 3 7 3 10-2-1-4-2-6-5h0l-4 1 2-2s1-1 2-1l3-3z" class="F"></path><path d="M383 784c1 1 2 1 2 2-5 5-8 9-11 16 0 2-2 4-2 5s0 1 1 2c0 1 1 2 1 3-2-1-2-2-3-3v-6c1-8 6-14 12-19z" class="B"></path><defs><linearGradient id="AB" x1="377.171" y1="790.081" x2="336.798" y2="798.287" xlink:href="#B"><stop offset="0" stop-color="#b3afa2"></stop><stop offset="1" stop-color="#f6f6f5"></stop></linearGradient></defs><path fill="url(#AB)" d="M340 677c2 2 3 2 4 4h0v-2h0 1c-1-2-1-2-1-3 9 17 15 36 22 54l8 18c2 4 3 8 4 11 1 2 0 3 1 4h0l-1 1v3c0-1-1-2-2-3-1 0-1 1-2 1l-1 1c-4 3-6 7-9 10-1-1-1-1 0-2l1-1v-1-1l4-4c2-2 3-4 6-5 0-1-1-1-1-2-3 1-6 5-8 8-1 1-2 3-3 4 2-6 6-10 10-14 0-4-3-9-5-13-12 16-19 40-16 60 2 9 6 18 13 24 2 1 5 2 7 3l1 1c1 1 2 1 3 1h2v2 1l-1-1c-1 0-2 0-3 1h-3c2 1 2 1 4 1h0 1c5-1 9-2 13-4-5 4-9 6-16 6h-5l3 1c3 0 7 0 10-1 2 1 4 1 6 3l-3 2c-5 3-12 4-18 3-8-2-16-8-20-15-6-11-8-22-8-34 0-13 1-25 4-38l3-8c3-6 7-12 11-17l3-3 3-1h1c0-1-1-2-1-3l-2-3c0-2-1-5-2-8h0l-1-1c0-3-3-8-5-11h0c-1-5-3-10-5-14-2-5-5-10-7-15z"></path><path d="M358 747c-1 2-2 5-4 7h0v-2-1c-1-1-1-2 0-3l1-2c1-2 1-2 2-3l1 1v3z" class="Z"></path><path d="M368 840c-1 0-2 0-3-1h-1c-1 0-2-1-2-1l-1-1c-1 0-2-1-3-2h-1c-2-1-4-5-5-6v-1c1 0 1 1 2 2v-1l-3-7c3 3 6 7 10 9l1 1c2 1 4 2 7 3 0 1 1 1 2 2 2 1 2 1 4 1h0 1c5-1 9-2 13-4-5 4-9 6-16 6h-5z" class="N"></path><path d="M340 677c2 2 3 2 4 4h0v-2h0 1c-1-2-1-2-1-3 9 17 15 36 22 54l8 18c2 4 3 8 4 11 1 2 0 3 1 4h0l-1 1v3c0-1-1-2-2-3-1 0-1 1-2 1l-1 1c-4 3-6 7-9 10-1-1-1-1 0-2l1-1v-1-1l4-4c2-2 3-4 6-5 0-1-1-1-1-2-3 1-6 5-8 8-1 1-2 3-3 4 2-6 6-10 10-14 0-4-3-9-5-13l1-1c0-1-2-5-3-7h-1l-2 2-3 5-2 3v-3c1-2 2-3 2-5l-1-1c-1 0-2 1-3 2 0 1-1 2-2 2l-1 1h0c-1 1-1 2-1 3l-4 8c0 1 0 2-1 2v1c0 2-1 3-1 5 0 0 0 1-1 1l-1 4h0v-2c0-1 0-2 1-3 0-1 0-3 1-4 1-2 1-4 2-6l-1-1h1v-1h-1c0 1-1 2-2 3 3-6 7-12 11-17l3-3 3-1h1c0-1-1-2-1-3l-2-3c0-2-1-5-2-8h0l-1-1c0-3-3-8-5-11h0c-1-5-3-10-5-14-2-5-5-10-7-15z" class="X"></path><defs><linearGradient id="AC" x1="282.199" y1="766.501" x2="303.935" y2="845.833" xlink:href="#B"><stop offset="0" stop-color="#d5d3c9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AC)" d="M322 761v-1l3-3c0-1 1-2 1-2h4 2 2v1h4v2 1h-1v1 1 2c0 5-2 11-2 16l-2 25c-1 7-2 14-4 21l6 5c-12 13-34 20-50 20-13 1-27-1-37-10-5-5-9-11-10-19h0c-1-2-1-4-1-7h0c1-4 2-8 4-11h0c0 2 0 3-1 4h0c1 4 1 6 3 10v1l3 3c0 1 1 2 2 3 0-1-1-2-1-3 3 4 6 8 10 9v-1h-1c-2-2-4-6-4-8 5 5 11 8 18 8 4 0 12-2 15-5l1-1h0c2-1 4-3 5-5v-2c1-1 1-1 1-2h1c1-1 3-2 5-2 3-2 5-5 8-8 2-3 4-7 5-10 2-5 3-9 3-15l2 2c0-8 2-14 6-20z"></path><path d="M238 821h0c-1-2-1-4-1-7h0c1-4 2-8 4-11h0c0 2 0 3-1 4h0c1 4 1 6 3 10v1l3 3c-3 0-3 0-4-2-1-1-1-2-2-3 0 0-1-2-2-2v7h0z" class="G"></path><path d="M314 779l2 2c-1 9-3 17-6 26-2 3-3 7-6 10 0 1-2 2-2 2l-1-1c2-1 4-3 4-5h1c0-2 1-2 2-3l3-9c1-3 1-4 0-7 2-5 3-9 3-15z" class="R"></path><path d="M306 804c2-3 4-7 5-10 1 3 1 4 0 7l-3 9c-1 1-2 1-2 3h-1c0 2-2 4-4 5l1 1s2-1 2-2h1 1c0 2-4 5-5 7-2 1-3 3-5 4l-4 2-1 1-6 3-7 1c-3 1-8 1-12 0l2-1h0c2 1 7 1 8 0h1 2l5-2c1 0 2 0 2-1l6-2v-1-1c1-1 2-3 3-4 2-2 4-3 5-4 1-2 1-2 2-3s1-2 2-3h0c-2 1-2 2-3 3l-1 1c-1 1-3 3-4 3l5-5 1-3h0-1v-1c-1 0-1 0-2 1h-1c3-2 5-5 8-8z" class="X"></path><path d="M298 812c3-2 5-5 8-8 0 2 0 5-2 7-1 1-2 2-3 4l1-3h0-1v-1c-1 0-1 0-2 1h-1z" class="I"></path><defs><linearGradient id="AD" x1="307.474" y1="835.703" x2="311.188" y2="801.815" xlink:href="#B"><stop offset="0" stop-color="#d9d7cf"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AD)" d="M322 802l1 1c0 2-1 4-2 7 0 0 0 1-1 1v1s0 1-1 1v1c1 1 2 0 3 1l-1 1c0 2-1 2-1 3l-2 3h-1c-1 3-5 7-7 9-1 0-1 0-2 1-1 0-1 1-2 1l-1 1c-1 0-2 1-3 1-5 2-9 4-14 5l-1 1h-3l-1 1h-10c-1-1-2-1-3-1s-2 0-3-1h1 1l6 1c1 0 7 0 8-1h3c0-1 1-1 2-1l3-1c2-3 5-3 7-6a57.31 57.31 0 0 0 11-11c2-2 3-4 5-6h1v-1c1-1 1-1 1-2h1l2-4 3-6z"></path><path d="M299 812c1-1 1-1 2-1v1h1 0l-1 3-5 5c1 0 3-2 4-3l1-1c1-1 1-2 3-3h0c-1 1-1 2-2 3s-1 1-2 3c-1 1-3 2-5 4-1 1-2 3-3 4v1 1l-6 2c0 1-1 1-2 1l-5 2h-2-1c-1 1-6 1-8 0h0l-2 1c-1 0-1 0-2-1h0c-2-1-3-1-4-2h0c-1-1-2-1-3-2v-1h-1c-2-2-4-6-4-8 5 5 11 8 18 8 4 0 12-2 15-5l1-1h0c2-1 4-3 5-5v-2c1-1 1-1 1-2h1c1-1 3-2 5-2h1z" class="N"></path><path d="M292 814h1c1-1 3-2 5-2h1c-1 4-4 5-7 7h0v-3-2z" class="Y"></path><path d="M119 491c19-6 41-10 61-8 21 1 44 9 63 18l27 16 1 1c1 2 4 4 6 6-2-1-4-2-7-3 2 2 5 3 6 4v1l-14-8-1 1c1 1 0 1 1 2l3 1c0 1 1 2 2 2 2 2 5 4 8 5 0 0 1 1 2 1v1c1 0 3 2 4 3s2 2 3 4v2l-1-1c-1 3-2 5-2 8 0-1 0-1-1-2-6-4-14-8-21-9-12-3-24-1-36 1 11-7 21-7 33-7h0c-6-3-13-3-19-4-8-1-16-4-23-1l-2 2c-4 5-11 9-17 13l-1-1c0 1-1 2-2 2h0l2-3-1-1h0c-2 0-3 1-5 2-3 2-6 2-8 4-5 2-10 5-14 9-13 8-23 22-27 37-1 3-2 6-2 9 0 1 1 3 1 4l1-1c0 2 0 3 1 4 1 2 1 5 2 8v-3l1 2v-2-1c1 2 2 4 2 6 2 3 4 7 6 10-2-7-3-13-4-20l1-1c1 2 0 3 2 3h0 1l-1-5c3-2 7-3 11-4 2 0 1 0 3 1 1 3 2 7 4 10s4 6 5 9 2 6 2 10v1h1 0l1 1c-3 14-3 30-16 38-5 4-11 6-17 4-3 0-11-4-13-2h-1c0 5 6 12 9 15 4 4 10 6 15 8h-1c2 2 3 3 5 4 3 3 6 5 10 6-4 7-5 13-15 15-8 2-21-5-28-9-1 0-4-2-5-3h-2-1c1 1 1 1 1 2-8-5-13-14-17-23l-5-11c-1-2-2-4-4-5v-1l-3 1c-2 2-3 5-4 7h0-1l-6-15c-4-10-6-25-4-36v-3l1-6c0-3-1-4-3-6l-1-1 1-1h-4c0 1 0 1-1 1 0-1-1-2-1-3l1-4h-1v-2l-1 1c-1 0-1 0-2-1h-1c0-1-1-1-1-2-1-5 1-14 3-18l3-6h0l5-8c-1-1-2-1-3 0-1 0-2 1-3 1 1-3 4-6 6-8 1-1 2-2 2-4v-4l1-1c1-1 1-2 1-3 1-3 3-5 4-8 2-2 5-6 4-9-1 0-1-1-2-1-5-1-10 1-13 4-1 0-1 0-1-1 2-12 13-20 22-26v-2c1-1 2-1 2-2v-1c7-5 17-10 25-12l2 1-1 1h1z" class="V"></path><path d="M89 669c1-1 2-3 4-3 3 1 4 4 5 7l-2 1c-1-2-2-4-4-5v-1l-3 1z" class="H"></path><path d="M96 512h3c1 1 1 1 1 3 0 1 0 1-1 2-2 1-2 1-4 0-1-2 0-2 0-3l1-2z" class="B"></path><path d="M98 673l6 13c-1 0-1 0-2-1h0-1l-5-11 2-1z" class="L"></path><path d="M114 660c1-1 2-1 3 0s1 1 1 3c0 1 0 1-1 2h-2c-2 0-2-1-3-2 0-2 1-2 2-3z" class="C"></path><path d="M72 608c2 1 4 2 5 4v1 1c1 3 0 7-1 10l-1-1-1-1 1-6c0-3-1-4-3-6l-1-1 1-1z" class="B"></path><path d="M85 554c0 1 1 1 1 2l-3 4h1l-2 2c-3 2-6 5-9 7l6-9 6-6z" class="T"></path><path d="M130 695c6 4 15 11 22 11-1 1-1 1-3 1-1 0-2 0-4-1-6-2-11-6-16-10l1-1z" class="K"></path><path d="M66 602v-2l-1 1c-1 0-1 0-2-1h-1c0-1-1-1-1-2-1-5 1-14 3-18h1 1v1 1c-2 5-3 10-4 15l5 1 1-2v1c0 2-1 3-1 5h-1z" class="a"></path><defs><linearGradient id="AE" x1="74.152" y1="566.44" x2="64.538" y2="578.215" xlink:href="#B"><stop offset="0" stop-color="#333"></stop><stop offset="1" stop-color="#676459"></stop></linearGradient></defs><path fill="url(#AE)" d="M72 566l5-7 2 1-6 9-7 13v-1-1h-1-1l3-6h0l5-8z"></path><path d="M85 554l3-3c2-1 3-2 5-2 1 1 2 1 2 3-1 2-4 4-6 5v1l-3 1c-1 1-1 1-2 1h-1l3-4c0-1-1-1-1-2z" class="G"></path><path d="M101 685h1 0c1 1 1 1 2 1 4 8 8 14 16 20h-2-1c1 1 1 1 1 2-8-5-13-14-17-23z" class="K"></path><path d="M128 670v2h1v-1c1 0 0 0 1-1 0 5 6 12 9 15 4 4 10 6 15 8h-1-4c-8-3-16-9-20-16-1-2-2-5-1-7z" class="B"></path><defs><linearGradient id="AF" x1="103.699" y1="500.262" x2="98.034" y2="494.956" xlink:href="#B"><stop offset="0" stop-color="#92887d"></stop><stop offset="1" stop-color="#b9b69d"></stop></linearGradient></defs><path fill="url(#AF)" d="M92 501c7-5 17-10 25-12l2 1-1 1h1c-11 4-20 9-29 15v-2c1-1 2-1 2-2v-1z"></path><path d="M106 640c0-1 0-2-1-3h0v-1l3 2c7 9 18 21 30 23 2 1 3 1 5 1-11 0-23-7-31-14-2-2-6-5-6-8z" class="C"></path><defs><linearGradient id="AG" x1="109.071" y1="628.794" x2="93.399" y2="618.514" xlink:href="#B"><stop offset="0" stop-color="#120906"></stop><stop offset="1" stop-color="#3d4041"></stop></linearGradient></defs><path fill="url(#AG)" d="M106 640l-2-2c-8-14-8-28-4-43h0c0 3-1 7-1 10 0 12 3 23 9 33l-3-2v1h0c1 1 1 2 1 3z"></path><defs><linearGradient id="AH" x1="166.349" y1="520.352" x2="158.651" y2="517.648" xlink:href="#B"><stop offset="0" stop-color="#9d9a99"></stop><stop offset="1" stop-color="#bab9b9"></stop></linearGradient></defs><path fill="url(#AH)" d="M147 522c3-2 7-3 11-3 4-1 17-4 21-2-12 2-22 6-33 11v-1c1 0 1-1 2-1l1-1c0-1 1-2 2-3h-4z"></path><path d="M86 622v-1-8c2 2 0 7 3 9 1 4 1 9 3 13 1 4 3 8 4 12-1 2 0 3 0 5l-1 2c-4-9-9-22-9-32z" class="L"></path><defs><linearGradient id="AI" x1="80.679" y1="644.216" x2="72.321" y2="651.284" xlink:href="#B"><stop offset="0" stop-color="#787361"></stop><stop offset="1" stop-color="#a29a88"></stop></linearGradient></defs><path fill="url(#AI)" d="M74 622l1 1 1 1v6c0 11 2 23 5 34 1 4 3 8 4 12h-1l-6-15c-4-10-6-25-4-36v-3z"></path><path d="M74 622l1 1 1 1v6s-1 1-1 2l-1-1v-6-3z" class="H"></path><path d="M75 550c0 1 0 2 1 3 3-3 7-4 11-5 3-1 4-1 6 1-2 0-3 1-5 2l-3 3-6 6-2-1c-1 1-5 7-5 7-1-1-2-1-3 0-1 0-2 1-3 1 1-3 4-6 6-8 1-1 2-2 2-4v-4l1-1z" class="C"></path><path d="M72 559h1c3 0 5-2 8-4l-4 4c-1 1-5 7-5 7-1-1-2-1-3 0-1 0-2 1-3 1 1-3 4-6 6-8z" class="R"></path><path d="M211 509c-10-2-21-3-32-3h-12-1c5-1 9-1 14-1 4 0 8-1 12 0 18 1 35 6 52 11l7 4-2 1c-5-1-10-4-15-6-7-2-15-5-23-6z" class="P"></path><path d="M95 654l1-2c0-2-1-3 0-5 8 20 20 38 41 47 2 1 5 1 8 2h2c-7 2-16-4-22-8l-6-4c2 4 6 8 10 10l1 1h0l-1 1c-10-8-19-18-26-28-3-5-5-10-8-14z" class="a"></path><path d="M260 517c-12-6-26-12-39-16-25-7-53-7-78 1-18 5-37 14-48 29 6-16 24-26 39-32 36-14 77-5 111 10 7 3 13 6 20 9 1 1 4 3 5 3 2 2 5 3 6 4v1l-14-8h-1l-1-1z" class="J"></path><path d="M211 509c8 1 16 4 23 6 5 2 10 5 15 6l2-1-7-4h2 0c4 0 8 3 12 3-1-1-2-2-4-2 2-1 4 1 6 0l1 1h1l-1 1c1 1 0 1 1 2l3 1c0 1 1 2 2 2 2 2 5 4 8 5 0 0 1 1 2 1v1c1 0 3 2 4 3s2 2 3 4v2l-1-1c-1 3-2 5-2 8 0-1 0-1-1-2-6-4-14-8-21-9-12-3-24-1-36 1 11-7 21-7 33-7h0c-6-3-13-3-19-4-8-1-16-4-23-1l-2 2c-4 5-11 9-17 13l-1-1c0 1-1 2-2 2h0l2-3-1-1h0c-2 0-3 1-5 2-3 2-6 2-8 4-5 2-10 5-14 9-13 8-23 22-27 37-1 3-2 6-2 9 0 1 1 3 1 4l1-1c0 2 0 3 1 4 1 2 1 5 2 8v-3l1 2v-2-1c1 2 2 4 2 6 2 3 4 7 6 10-2-7-3-13-4-20l1-1c1 2 0 3 2 3h0 1l-1-5c3-2 7-3 11-4 2 0 1 0 3 1 1 3 2 7 4 10s4 6 5 9 2 6 2 10v1h1 0l1 1c-3 14-3 30-16 38-5 4-11 6-17 4-3 0-11-4-13-2h-1c-1 1 0 1-1 1v1h-1v-2-1c0-1 1-2 2-2 4-2 10 2 14 3 5 1 9 0 14-3 10-6 12-15 15-26-3 3-5 4-8 5-10 4-22 4-31-1-11-5-19-14-23-26-6-14-3-30 3-43 12-27 43-45 71-48h11c3 0 7 1 10-1 3-1 1-2 2-5 4-2 7-3 11-4-4 0-9-1-14-1h-9-17c-4-2-17 1-21 2-4 0-8 1-11 3-4 1-8 3-12 6-15 7-26 19-35 34-10 18-14 40-11 60-3-2-1-7-3-9v8 1l-1-2c-1-8-1-17 0-25 3-26 17-49 38-65 9-6 18-12 28-15 20-6 42-5 63-2v-1l1 1h3c1 1 1 0 2 0 1 1 1 1 2 1s1 0 2 1l-1-2-4-1h0l-2-1h0-4l-2-2z" class="E"></path><path d="M142 613v-3l1 2v-2l2 7-2 1-1-1v-1-3z" class="G"></path><path d="M145 617c2 3 3 5 4 7 1 1 1 1 1 2 1 2 3 3 4 4l1 1h-1c-6-2-10-8-11-13l2-1z" class="Z"></path><path d="M142 617l1 1c1 5 5 11 11 13h1c-1 1-2 2-3 2v1 1h0c-1 0-2-1-3-2h0v-1l-2-1c-3-4-5-10-5-14z" class="B"></path><path d="M138 602l1-1c0 2 0 3 1 4 1 2 1 5 2 8v3 1c0 4 2 10 5 14l2 1v1c-7-5-8-12-10-20 0-4 0-7-1-11z" class="F"></path><path d="M211 509c8 1 16 4 23 6 5 2 10 5 15 6 5 2 15 5 18 9-9-4-18-8-28-11l-25-6h0v-1l1 1h3c1 1 1 0 2 0 1 1 1 1 2 1s1 0 2 1l-1-2-4-1h0l-2-1h0-4l-2-2z" class="N"></path><path d="M244 516h2 0c4 0 8 3 12 3-1-1-2-2-4-2 2-1 4 1 6 0l1 1h1l-1 1c1 1 0 1 1 2l3 1c0 1 1 2 2 2 2 2 5 4 8 5 0 0 1 1 2 1v1c1 0 3 2 4 3s2 2 3 4v2l-1-1v-1h-2c-2 1-6-2-7-3s-2-1-2-1l-2-1-1-1c-1 0-1-1-2-2-3-4-13-7-18-9l2-1-7-4z" class="B"></path><path d="M244 516h2 0c4 0 8 3 12 3-1-1-2-2-4-2 2-1 4 1 6 0l1 1h1l-1 1c1 1 0 1 1 2l3 1c0 1 1 2 2 2 2 2 5 4 8 5 0 0 1 1 2 1v1c1 0 3 2 4 3l-30-14-7-4z" class="Z"></path><path d="M158 540c2 0 3-2 5-2 10-3 20-7 30-8 3-1 6 0 9 0-1 1-2 2-4 2-3 1-7 2-11 4h0c-1 1-1 1-3 1-1 1-3 2-5 3h-2l-1 1c-1 0-2 1-3 1-2 1-5 3-7 3h0v-1h0c-2 0-2 1-3 1h-1c1-1 2-1 3-3h-1-1l1-1v-1c-2 0-3 1-5 0h0-1z" class="F"></path><defs><linearGradient id="AJ" x1="135.3" y1="588.924" x2="116.916" y2="572.962" xlink:href="#B"><stop offset="0" stop-color="#cacbc9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AJ)" d="M158 540h1l-2 2h-1c-3 2-6 4-9 7h-1c-1 1-2 1-3 1l-3 3c-2 1-2 2-3 3 0 1-1 2-2 3-6 3-14 18-17 25-3 10-3 20 2 30s12 18 22 23l3 3h0l-1-1c-13-6-24-18-29-31-4-11-2-24 3-34 6-12 15-20 27-27 4-3 8-6 13-7z"></path><path d="M151 607l-1-5c3-2 7-3 11-4 2 0 1 0 3 1 1 3 2 7 4 10s4 6 5 9 2 6 2 10c-1 2-1 5-3 8-3 5-10 6-15 7 1-2 3-3 4-5 4-8-2-14-6-20-2-4-3-7-4-11z" class="V"></path><path d="M187 536l12-3c-2 1-4 3-6 4-2 0-3 1-5 2-3 2-6 2-8 4-5 2-10 5-14 9-13 8-23 22-27 37-1 3-2 6-2 9 0 1 1 3 1 4 1 4 1 7 1 11l-1 1 1 6c2 4 3 9 6 12 2 2 4 3 6 5l-1 1h0-3l-2-1h-3c-10-5-17-13-22-23s-5-20-2-30c3-7 11-22 17-25 1-1 2-2 2-3 1-1 1-2 3-3l3-3c1 0 2 0 3-1h1c3-3 6-5 9-7h1l2-2h0c2 1 3 0 5 0v1l-1 1h1 1c-1 2-2 2-3 3h1c1 0 1-1 3-1h0v1h0c2 0 5-2 7-3 1 0 2-1 3-1l1-1h2c2-1 4-2 5-3 2 0 2 0 3-1z" class="Q"></path><path d="M159 540h0c2 1 3 0 5 0v1l-1 1h1c-1 1-2 1-3 1l-3 3v-1h-1l-1-1 1-1-1-1h1l2-2z" class="G"></path><path d="M126 585c1-4 3-7 4-10 3-4 5-8 9-12v1c-6 8-11 18-11 28v8l-3-4v-1c-1-1-2-2-2-4 0-1 0-2-1-3 0-1 1-1 1-2 1-1 1-1 1-2v-1h1c0 1 0 1 1 1v1z" class="B"></path><path d="M122 588c0-1 1-1 1-2 1-1 1-1 1-2v-1h1c0 1 0 1 1 1v1l-1 10c-1-1-2-2-2-4 0-1 0-2-1-3z" class="F"></path><path d="M128 592v10h1v-9c1-1 0-2 1-3v-1c0-1 0-1 1-1v2c0 1 0 2-1 4v4h1l-1 8c0 6 1 11 3 17h-1c-1-1-2-1-2-2-1-1-1-1-1-2l-1 1 1 1-1 1c-3-3-6-7-7-12v-1c1 2 2 2 3 3h1l-1-2h1v-4-10l3 4v-8z" class="Z"></path><path d="M125 596l3 4v6c0 2 1 5 0 7-1 0-2 0-3-1v-2-4-10z" class="C"></path><path d="M142 637c-10-5-17-13-22-23s-5-20-2-30c3-7 11-22 17-25l-10 13c-3 6-5 11-6 18h0c1-1 1 0 1-1 2 2 0-1 1 1v1l1-3c1 1 1 2 1 3 0 2 1 3 2 4v1 10 4h-1l1 2h-1c-1-1-2-1-3-3v1c1 5 4 9 7 12l1-1-1-1 1-1c0 1 0 1 1 2 0 1 1 1 2 2h1c2 4 4 7 8 10 2 2 4 3 6 5l-2-1h-3z" class="B"></path><path d="M119 590h0c1-1 1 0 1-1 2 2 0-1 1 1v1 3 2 4c-1 0-1 0-2 1 0 1 0 1-1 2-1-5-1-9 1-13z" class="F"></path><path d="M121 600c1 1 2 3 2 5v1c0 1 0 2 1 3v1l1 2h-1c-1-1-2-1-3-3v1c-2-2-2-5-3-7 1-1 1-1 1-2 1-1 1-1 2-1zm7 22l1-1-1-1 1-1c0 1 0 1 1 2 0 1 1 1 2 2h1c2 4 4 7 8 10 2 2 4 3 6 5l-2-1c-6-3-13-9-17-15z" class="S"></path><path d="M122 588c1 1 1 2 1 3 0 2 1 3 2 4v1 10 4h-1v-1c-1-1-1-2-1-3v-1c0-2-1-4-2-5v-4-2-3l1-3z" class="N"></path><defs><linearGradient id="AK" x1="164.472" y1="581.345" x2="143.481" y2="566.309" xlink:href="#B"><stop offset="0" stop-color="#d5d4d2"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AK)" d="M187 536l12-3c-2 1-4 3-6 4-2 0-3 1-5 2-3 2-6 2-8 4-5 2-10 5-14 9-13 8-23 22-27 37-1 3-2 6-2 9 0 1 1 3 1 4 1 4 1 7 1 11l-1 1 1 6c2 4 3 9 6 12 2 2 4 3 6 5l-1 1h0-3c-2-2-4-3-6-5-4-3-6-6-8-10-2-6-3-11-3-17l1-8c2-9 8-30 16-37v-1c1-1 4-5 6-6 1 0 2-1 3-2 2-1 0 1 1-1 1-1 2-2 3-2 2-1 4-3 6-4 2 0 5-2 7-3 1 0 2-1 3-1l1-1h2c2-1 4-2 5-3 2 0 2 0 3-1z"></path><path d="M137 598c0 1 1 3 1 4 1 4 1 7 1 11l-1 1v-1c-1-5-2-9-1-15z" class="a"></path><path d="M131 598c2-9 8-30 16-37v1c-2 4-5 8-7 12-5 10-10 23-8 34 0 4 2 8 3 12 2 0 2 0 3 1l1-1c2 4 3 9 6 12 2 2 4 3 6 5l-1 1h0-3c-2-2-4-3-6-5-4-3-6-6-8-10-2-6-3-11-3-17l1-8z" class="B"></path><path d="M139 620c2 4 3 9 6 12 2 2 4 3 6 5l-1 1h0c-7-6-11-9-15-18 2 0 2 0 3 1l1-1z" class="S"></path><path d="M327 633l-29-70-33-80-39-97-20-50-16-37c-7-17-13-34-22-50-10-18-22-32-38-43-12-8-25-13-40-12-7 0-14 1-21 3-5-9-8-18-13-27-3-7-7-14-10-21v-5l3-3c3-1 10 0 14 0h46 202l85-1h22c3 0 7 0 10 1 1 0 2 1 2 2 2 5-10 30-13 36-2 5-4 10-6 14 0 1-1 2-1 3h-4l-12 1c-3 0-7 1-10 2-4 1-8 2-11 3-7 3-14 8-19 13-4 4-7 8-10 13 4-14 13-26 26-33 6-3 12-5 18-6 2-1 14-2 14-3 0 0 1-1 1-2l3-7 9-22H61l15 31c6-1 12-2 18-2 16 1 32 7 45 16 15 11 27 25 36 42 16 27 27 58 39 87l64 158 97 236 97 240 8 17v1h3 8 39l103-253 109-264 61-147c11-26 20-54 34-78 12-21 30-39 52-48 14-6 29-8 44-6l14-30H686h-67-17-9l14 31c17 2 37 6 49 20 4 4 7 9 9 14-8-10-19-17-31-20-5-2-13-3-19-4-3 0-15 1-16-1-2-2-21-47-21-51 0-1 1-2 2-3s3 0 5 0h92 171 81 19c3 0 8-1 11 0 2 0 3 1 4 3-1 6-6 14-8 20l-15 32h0l-4 3c0 1-1 1-2 1l-2 4-1 3c-2 1-5 0-8 0 0-1-2-1-2-1-2-1-5-2-6-3s0-3-1-4v-1l2-1v-1c-2-1-4 1-7 0 0 0-1-1-2-1l-6 1c-21 6-41 24-51 42l1 2c-1 2-3 6-4 7-1 2-1 3-1 5l3-4c1 1 0 3 0 5l-2 13s-1 2-1 3c-2 16-9 31-18 44h0c-2 4-4 7-6 11-2 3-3 7-5 9l-5 6c-3 3-5 6-5 10l-6 14c-2 5-4 10-7 14l-2 2-4 7v-1l-2 2-248 604h-65l-14-33-1-1-1-1v1c-1-2-2-5-3-7l-1-1h-1l-1-1c-1-1-1-3-2-4-2-2-2-4-3-6l-1-2-2-3v-1h0c-1-1-1-2-1-3h-1 0v-1-1-1l-6-16c-1-1-1-2-1-3h-1l1-1c-1-1-1-2-2-3v-2-1l-1-1v-2-6 1l1-1c-2-4-4-9-5-13l-9-20c0-4-2-7-3-11l-13-29-15-37c-3-7-5-15-9-22-1-3-2-7-4-11l-8-18c-7-18-13-37-22-54 0-1-1-2-1-4l-16-39z" class="V"></path><path d="M454 950h2l3 8-1-1-1-1v1c-1-2-2-5-3-7z" class="X"></path><path d="M841 254c2-6 5-11 9-16l1 2c-1 2-3 6-4 7h-2c-1 2-2 5-4 7z" class="S"></path><path d="M907 195c6-1 12-1 18-1 5 0 10 1 15 2h0l-4 3c0 1-1 1-2 1l-1-1c-6-1-12 0-17-2v-1c-2-1-4 1-7 0 0 0-1-1-2-1z" class="X"></path><path d="M916 197c5 2 11 1 17 2l1 1-2 4-1 3c-2 1-5 0-8 0 0-1-2-1-2-1-2-1-5-2-6-3s0-3-1-4v-1l2-1z" class="V"></path><path d="M432 903v-2-1l-1-1v-2-6 1l1-1c5 11 9 22 13 33 4 9 8 18 11 26h-2l-1-1h-1l-1-1c-1-1-1-3-2-4-2-2-2-4-3-6l-1-2-2-3v-1h0c-1-1-1-2-1-3h-1 0v-1-1-1l-6-16c-1-1-1-2-1-3h-1l1-1c-1-1-1-2-2-3zm409-649c2-2 3-5 4-7h2c-1 2-1 3-1 5l3-4c1 1 0 3 0 5l-2 13s-1 2-1 3c-2 16-9 31-18 44h0c-2 4-4 7-6 11-2 3-3 7-5 9l-5 6c-3 3-5 6-5 10l-6 14c-2 5-4 10-7 14l-2 2-4 7v-1l-2 2c3-8 6-15 9-22l21-50 10-26c5-12 9-24 15-35z" class="L"></path><path d="M825 306v1c-1 2-1 4-2 6v1c0-1 1-2 2-3 0 2-1 3-2 5l-2 4 1 1c1 0 1-1 2-2 0-1-1 0 1-1v-1c1-1 2-2 3-4-2 4-4 7-6 11-2 3-3 7-5 9l-5 6-1-1c1-4 3-7 5-11 3-7 5-14 9-21z" class="M"></path><path d="M849 248c1 1 0 3 0 5l-2 13s-1 2-1 3c-2 16-9 31-18 44h0c-1 2-2 3-3 4v1c-2 1-1 0-1 1-1 1-1 2-2 2l-1-1 2-4c1-2 2-3 2-5-1 1-2 2-2 3v-1c1-2 1-4 2-6v-1c5-11 8-23 12-34 3-7 6-14 9-20l3-4z" class="G"></path><path d="M634 200c12 3 23 10 31 20-2-5-5-10-9-14-12-14-32-18-49-20l-14-31h9 17 67 261l-14 30c-15-2-30 0-44 6-22 9-40 27-52 48-14 24-23 52-34 78l-61 147-109 264-103 253h-39-8-3v-1l-8-17-97-240-97-236-64-158c-12-29-23-60-39-87-9-17-21-31-36-42-13-9-29-15-45-16-6 0-12 1-18 2l-15-31h354l-9 22-3 7c0 1-1 2-1 2 0 1-12 2-14 3-6 1-12 3-18 6-13 7-22 19-26 33 3-5 6-9 10-13 5-5 12-10 19-13 3-1 7-2 11-3l-1 1h4 17 7-1c2-2 3-4 5-5l1-1 2-7c25 4 50 16 65 37 8 11 14 25 16 39 1 7 1 13 1 19v10 4l4 4c2 1 4 3 7 3 2-1 3-2 5-3 1-1 4-3 4-5 1-3 0-7 0-10 0-6 0-12 1-19 2-14 7-29 15-41 10-15 23-24 38-31 5-2 11-4 16-6 3 1 4 6 5 8 1 1 1 2 3 2h0c1 1 2 1 3 1l1 1c7 0 14-1 21 0h11z" class="J"></path><path d="M792 308c2-1 3-2 5-2h1c-1 1-1 2-1 4l-1-2c-2-1-3 0-4 0z" class="N"></path><path d="M506 669l6 1h2c-1 1-3 2-4 2-2 0-3-1-4-3z" class="R"></path><path d="M293 427l2-1c0 1 1 1 1 2 1 1 1 1 1 2h0c-2 0-3 0-4-2v-1z" class="U"></path><path d="M594 431c2-1 3-1 5 0h1l1 2v1l-7-3z" class="O"></path><path d="M716 385l3-3 1 1c0 1 0 3-1 4l-3-2z" class="b"></path><path d="M415 724h2l1 1c0 2 0 2-1 4l-1-1c-1-2-1-2-1-4z" class="G"></path><path d="M405 422l4 4v2h-2 0c-1-2-2-4-2-6zm370-74h4c-2 3-5 5-8 7 0-1 0-1 1-2l2-2c0-1 0-2 1-3z" class="W"></path><path d="M719 387v1c-1 1-2 2-4 3l-1-1c0-2 1-3 2-5l3 2z" class="a"></path><path d="M338 522h1c1 1 2 2 2 3s-1 1-1 2h-1c-2 0-2-1-3-2 1-2 0-1 2-3z" class="Q"></path><path d="M470 866h1c2 2 3 5 4 8-2-2-5-4-6-6 0-1 0-1 1-2z" class="b"></path><path d="M787 333c-1 2-1 3-2 4h-3-1-1v-1l1-1 3-2h3z" class="U"></path><path d="M325 520h1c1 0 2 0 3 1 0 2 0 2-1 4h-1c-1 0-2-1-2-1-1-2-1-2 0-4z" class="Q"></path><path d="M519 415c3 2 4 3 7 4v1c-2 1-3 1-5 0l-2-1v-1c1-1 0-2 0-3z" class="S"></path><path d="M676 533h3c1 1 1 1 1 3-1 1-1 2-2 3h-2c-1-1-1-1-2-3 1-1 1-2 2-3z" class="U"></path><path d="M436 342h2c1 2 2 3 2 6h0c1 1 1 2 1 4v1c-3-3-4-7-5-11z" class="N"></path><path d="M214 301h1c3 0 5 3 7 5-2 1-4 0-6-1s-2-1-2-3v-1z" class="D"></path><path d="M637 585v1h1v1c1-1 2-1 3-1l-4 5c-3 1-3 2-5 4l-1-1 6-9z" class="W"></path><path d="M574 792l2-1c1 1 1 1 2 3-1 2-1 2-2 3h-2c-1 0-1 0-2-2 0-2 0-2 2-3z" class="Q"></path><path d="M326 498c2 0 3 0 4 1v2c0 2-1 2-2 3-1 0-1 0-2-1l-2-2c1-2 1-2 2-3z" class="V"></path><path d="M782 337c-5 0-11 1-16 1 1-1 4-3 5-3 3-1 7 0 10 0l-1 1v1h1 1z" class="W"></path><path d="M351 548h2c0 1 2 2 2 4 0 1-1 2-2 2-1 1-2 0-3 0-1-1-2-2-2-3 1-2 1-2 3-3zm257 102v4h0l2-2c-1 3-2 5-3 7-1 1-1 2-2 3s-1 2-2 4h-1l-1 1 7-17z" class="G"></path><path d="M781 330h7l-1 3h-3-1c-4 1-8 1-12 1 3-1 7-2 10-3v-1z" class="W"></path><path d="M495 945c1 0 1 0 1-1h0c0-2 0-2 1-3 0 1 1 1 1 2s0 1 1 1c1 3 2 8 1 11v1c-2-3-3-8-5-11z" class="P"></path><path d="M495 655c2 2 2 4 3 6v3l-1 1c1 2 2 3 3 5 0-2 1-4 1-6 0 2 1 5 0 7l-2-1c-2-2-4-9-5-12-1-1 0-2 1-3z" class="N"></path><path d="M466 887h3c1 2 1 2 1 4s-1 2-2 3c-2 0-2 0-3-1s-1-2-1-3c0-2 1-2 2-3z" class="Q"></path><path d="M444 400c0-1-1-2-2-2 4 0 8 3 12 4l2 1c2 1 5 2 8 2l-1 2h0c-2 0-4-1-7-1-2-1-2-2-4-3-2 0-4-2-6-2-1 0-1-1-2-1z" class="B"></path><path d="M517 659c1 1 3 1 4 1 1 1 2 1 3 1-1 4-2 7-5 9h-1c0-4 0-7-1-11z" class="T"></path><path d="M540 411c5-1 10-3 16-4l-2 1h0c-1 0-2 1-3 1h-1c-1 0-1 0-1 1 1 0 1 1 2 0h1 2c-4 1-9 3-13 4l-7-1 1-1 3-1h2z" class="D"></path><path d="M252 352h1c4 4 13 18 14 24-2-3-4-7-7-10-1-3-4-6-5-9-1-2-2-3-3-5z" class="B"></path><path d="M409 426l2 1 2 1-1 2h1l3 6h-5c-2-2-3-5-4-7v-1h0 2v-2z" class="U"></path><path d="M409 426l2 1c0 1 0 3-1 5h0l-1-1c0-1-1-1-2-2v-1h0 2v-2z" class="R"></path><path d="M553 846h1c1 1 1 1 2 3-2 3-5 5-8 7h-2 0c0-2 0-3 1-4 1-3 3-5 6-6z" class="H"></path><path d="M461 564v3h1l1 2h0c0-1 1-2 1-3h1c0-1 1-2 2-3h0c-1 4-4 9-2 14 0 0 1 1 1 2h0l1 1v1c-1 0-1 0-2-1v3l-5-15c-1-2 0-2 1-4z" class="T"></path><path d="M440 745l7 18-2 2-9-20h4z" class="S"></path><path d="M589 705c1 0 1-1 2 0l1 1c-2 6-7 12-10 16l-1 1c1-6 4-13 8-18z" class="H"></path><path d="M599 431c6-2 8-4 12-7l-4 12c-1 0-4-1-5-2h-1v-1l-1-2h-1z" class="Z"></path><path d="M631 594l1 1c2-2 2-3 5-4-6 9-12 18-17 28 0-3 8-21 11-24v-1z" class="P"></path><path d="M780 341c-5 3-13 2-18 2 2-1 4-3 6-3 4-1 11-3 15-1l1 1c-2 0-3 0-4 1z" class="R"></path><path d="M487 631c5 0 10 2 14 6 3 3 5 8 7 12-1 3-1 6-2 9 0-2 0-2-1-4v-3c0-5-4-9-8-13h1l1 1c1 0 1 0 2 1l2 2c0-2-3-5-5-6-1-1 0 1-1-1h0-1l-1-1h-1l-1-1c-1 0-1 0-2-1h0-1c-1 0-2 0-2-1h-1 0z" class="B"></path><path d="M563 775h0c-3 8-7 16-11 24 1-7 3-13 6-19 1-3 2-6 4-8l1 3z" class="P"></path><path d="M536 853l-10 20c0-8 3-18 8-24l2 4z" class="H"></path><defs><linearGradient id="AL" x1="734.181" y1="378.084" x2="740.649" y2="364.106" xlink:href="#B"><stop offset="0" stop-color="#2a2a2a"></stop><stop offset="1" stop-color="#45433f"></stop></linearGradient></defs><path fill="url(#AL)" d="M747 358l-16 28c3-9 5-18 10-25h0 1c2-2 3-2 5-3z"></path><path d="M774 351l-2 2c-1 1-1 1-1 2-2 2-9 2-12 3-4 1-9 4-13 6 0-2 1-3 2-4 1-2 3-3 4-4 4-2 10-1 15-2h-1s3 0 3-1c2 0 3-1 5-2z" class="P"></path><path d="M781 330h-6l12-9c2-1 3-2 5-3-2 4-4 8-4 12h-7z" class="U"></path><defs><linearGradient id="AM" x1="496.629" y1="933.83" x2="491.461" y2="936.371" xlink:href="#B"><stop offset="0" stop-color="#86857b"></stop><stop offset="1" stop-color="#a39b8d"></stop></linearGradient></defs><path fill="url(#AM)" d="M493 928h0c1-1 1-2 0-3 0-1 1-1 1-2l5 21c-1 0-1 0-1-1s-1-1-1-2c-1 1-1 1-1 3h0c0 1 0 1-1 1l-3-6 1-11z"></path><path d="M230 308c1 0 1-1 2 0 4 0 7 3 9 7 3 5 5 10 7 15h0c-5-7-9-13-15-18-1-2-2-2-3-4z" class="H"></path><path d="M490 595l8-9c2-2 7-5 11-5v3c-4 0-6 3-9 6l-1 2h0c-1 0-2 1-3 1h0c-1 1-2 2-3 2h-3z" class="a"></path><path d="M784 340c-1 3-2 5-4 8h-1v-2h-1c-5 2-13 1-19 1 0-1 1-1 1-1 4-3 11-2 16-2 0 0 2 0 3-1 0 0 1-1 1-2 1-1 2-1 4-1z" class="W"></path><path d="M481 580c3 0 6 1 9 1 3 1 5 0 8 1-4 3-7 6-10 10-3-4-4-8-6-12h-1z" class="N"></path><path d="M758 349c5-1 11 0 17-1-1 1-1 2-1 3-2 1-3 2-5 2 0 1-3 1-3 1h-13c1-1 3-1 4-1v-1h-2l3-3z" class="R"></path><path d="M757 353v-1h-2l3-3c1 2 5 1 7 2 2 0 5 0 8 1l-14 1h-2z" class="P"></path><path d="M467 580c0-4 0-9 3-13h0v6l2-2c-2 5-3 9-1 14l1 1c-1 2-1 5-1 8l-2-1-4-10v-3c1 1 1 1 2 1v-1z" class="N"></path><path d="M595 196c1 1 1 2 3 2h0c1 1 2 1 3 1l1 1c-1 1-3 1-5 1-3 0-6 1-9 2-5 1-10 2-14 4-3 1-5 2-8 3 7-5 15-9 24-11 2-1 4-1 5-3z" class="W"></path><path d="M528 402c3 1 5 2 8 2 2 1 4 1 7 1 1 0 2 0 4 1h1v1c1 0 2 0 4-1 3-1 10-4 13-3-3 1-7 2-9 4-6 1-11 3-16 4l-1-1c-2-1-3-1-5-2l1-1h1c0-1 0-2-1-2h-2l-1-1c-2 0-2-1-4-2z" class="Z"></path><path d="M491 925l1-1c0-2-1-4-2-6-2-5-5-9-7-14v-3h2c1 0 1 0 1 1 1 1 2 3 3 4 2 5 5 13 5 17 0 1-1 1-1 2 1 1 1 2 0 3h0l-2-3z" class="P"></path><path d="M492 939c-3-3-6-6-7-9-1-2-1-4 0-6 0-1 1-2 3-2s2 1 3 3l2 3-1 11z" class="Q"></path><path d="M447 763c3 7 12 25 11 32h-1c-8-8-7-20-12-30l2-2z" class="G"></path><path d="M287 485h1c4 0 7 3 9 6 2 2 5 6 5 9h-2l-1-1-3-3h0c0 1 1 2 1 4h-1c-2-3-5-6-7-9-1-2-2-4-2-6z" class="I"></path><defs><linearGradient id="AN" x1="780.38" y1="296.046" x2="792.923" y2="288.854" xlink:href="#B"><stop offset="0" stop-color="#4d4c49"></stop><stop offset="1" stop-color="#706e67"></stop></linearGradient></defs><path fill="url(#AN)" d="M794 284c0 2-4 4-5 6 0 1 1 1 1 2 1 1 1 2 0 3 0 2-3 5-5 5-1 1-2 1-2 0-2 0-2-1-2-2 0-2 2-4 3-6 3-5 6-10 10-13h0c0 2-1 2-2 3 0 1-1 2-2 3h2l1-1h1z"></path><path d="M472 586c1 1 2 2 5 3 1 0 3-1 4-1l2-1h0c0 2 1 3 2 5l-1 1c-2 1-4 3-6 4-3 0-5 1-6 2l-3-6 2 1c0-3 0-6 1-8z" class="Y"></path><path d="M481 588l2-1h0c0 2 1 3 2 5l-1 1c-2 1-4 3-6 4-3 0-5 1-6 2l-3-6 2 1c1 1 1 1 3 1 1 1 4 0 5-1 2-1 3-3 3-5l-1-1z" class="S"></path><path d="M407 267c8-7 18-11 29-11 6 0 12 2 16 6 0 0 1 1 1 2v1-1c-6-5-14-6-21-5-9 0-16 4-23 9l-2-1z" class="G"></path><path d="M407 267l2 1c-15 13-23 32-32 50-2 4-4 10-7 14v-1c1-5 4-10 6-15 8-17 17-36 31-49z" class="Q"></path><path d="M581 392l4 1h2c-7 4-15 8-22 10-3-1-10 2-13 3-2 1-3 1-4 1v-1c3-1 8-2 10-3h-3l-1 1c-2 0-5 1-6 1s-2-1-3-1c2 0 3 0 4-1l12-3c7-2 13-4 20-8z" class="R"></path><path d="M485 257c-2-4-4-8-7-11 2 1 3 2 5 4 0 1 0 1 1 1 2 1 3 4 4 6 3 6 5 13 5 20 0 5 0 11-3 15-1 0-1 1-3 0 2-2 2-6 3-8l-1-4c1-3 1-8 0-11-1-2-1-3-1-5 0-3-2-5-3-7z" class="b"></path><path d="M488 264c1 1 1 2 2 4v1 2c1 4 2 9 0 13l-1-4c1-3 1-8 0-11-1-2-1-3-1-5z" class="W"></path><path d="M680 199h102 1 11c-1 1-1 1-2 1h-37 4 27c2 0 6-1 8 0l-1 1h0c-1-1-3-1-4-1-3 0-7 0-10 1h-98 3c2-2 8-1 10-1h29 14 0-51c-2-1-3-1-5-1h-1z" class="C"></path><path d="M508 649v2l1-1c1 0 1 2 2 1 1 4 6 15 4 18l-1 1h-2l-6-1c-2-4-1-7 0-11 1-3 1-6 2-9z" class="L"></path><defs><linearGradient id="AO" x1="429.891" y1="199.221" x2="427.198" y2="205.355" xlink:href="#B"><stop offset="0" stop-color="#5f5f5f"></stop><stop offset="1" stop-color="#7a766e"></stop></linearGradient></defs><path fill="url(#AO)" d="M404 200h7-1c2-2 3-4 5-5l1-1v1c2 1 4 1 6 2 4 1 7 2 11 3 7 2 13 6 20 10-11-4-22-7-33-9-6 0-11 0-16-1z"></path><path d="M250 391h1c6 0 13 5 18 9l-8-4-1 1 6 3c4 2 7 5 9 8l-7-3h-1c3 1 6 3 9 5 2 1 3 4 4 5-3-3-6-5-10-7-6-4-14-6-18-11-2-2-3-3-2-6z" class="G"></path><defs><linearGradient id="AP" x1="799.022" y1="287.392" x2="806.425" y2="261.014" xlink:href="#B"><stop offset="0" stop-color="#797672"></stop><stop offset="1" stop-color="#aaa79a"></stop></linearGradient></defs><path fill="url(#AP)" d="M794 279c4-6 13-15 19-17 2 1 2 1 3 2 0 2 0 3-2 5-3 3-6 5-9 7l-11 8h-1l-1 1h-2c1-1 2-2 2-3 1-1 2-1 2-3h0z"></path><path d="M476 548c7-2 15-2 23-1l-5 2h-1c-1 0-2 1-3 1h-2l-6 3-1-1 1-1h-1l-5 3c-3 2-5 6-7 8-1 1-1 1-2 1h0c-1 1-2 2-2 3h-1c0 1-1 2-1 3h0l-1-2h-1v-3c2-3 5-7 8-9 2-2 6-4 7-7zm24 127l6 3c0 1 2 2 3 2 3 0 7-3 10-4l-5 12c-1 2-2 5-3 7-1 1-1 1-3 1-1-2-2-5-3-7l-5-14z" class="F"></path><path d="M523 931c1 2-1 5-1 7l-5 13v-1c-3-10-1-21 1-31 1-2 1-6 2-8l2-1 1 10c0 4-1 8 0 11z" class="H"></path><defs><linearGradient id="AQ" x1="650.887" y1="585.954" x2="649.589" y2="556.503" xlink:href="#B"><stop offset="0" stop-color="#96928f"></stop><stop offset="1" stop-color="#bfbcad"></stop></linearGradient></defs><path fill="url(#AQ)" d="M637 585c3-6 18-28 24-30h2c1 1 2 2 2 3-1 1-1 2-2 3a57.31 57.31 0 0 1-11 11c-4 4-8 9-11 14-1 0-2 0-3 1v-1h-1v-1z"></path><path d="M390 382l43 14h0c-2 1-10-1-12-1-9-1-18-2-27-2-2-3-4-7-4-11z" class="S"></path><path d="M467 563c1 0 1 0 2-1 2-2 4-6 7-8l5-3h1l-1 1 1 1 6-3h2c1 0 2-1 3-1l-6 6c-3 1-4 3-6 5-1 1-3 3-4 3-2 2-4 5-5 8l-2 2v-6h0c-3 4-3 9-3 13l-1-1h0c0-1-1-2-1-2-2-5 1-10 2-14z" class="O"></path><path d="M500 590h4v2c-1 1-2 1-3 2-3 2-5 4-7 6-4 4-14 8-19 7l-3-8c1-1 3-2 6-2 2-1 4-3 6-4l1-1c1 1 2 3 4 4l1-1h3c1 0 2-1 3-2h0c1 0 2-1 3-1h0l1-2z" class="M"></path><path d="M484 593l1-1c1 1 2 3 4 4l1-1h3c1 0 2-1 3-2h0c1 0 2-1 3-1-3 4-7 7-11 9h-1l2-2c-1-1-2-1-2-2v-1c0-1-2-2-3-3z" class="I"></path><path d="M581 256c3-1 7-1 11 0 13 1 23 10 31 20 9 12 15 26 21 39 2 3 5 9 4 12h0c-3-4-4-10-6-14l-14-26c-7-12-18-25-32-28-10-2-21-1-30 5h-1c0-1 1-2 1-2 3-4 10-6 15-6zm-64 403c-2-5-3-9-1-14 3-5 8-8 13-10l5-1-10 27c-1 0-2 0-3-1-1 0-3 0-4-1z" class="G"></path><path d="M622 380c2-1 5-2 7-3l-4 10c-1 2-1 4-3 5l-41 6c12-8 27-11 40-17 1 0 1 0 1-1z" class="X"></path><path d="M495 655l-2-1c-2-2-3-6-4-8l-5-13c5 1 9 3 13 5 4 4 8 8 8 13v3l-4 10c0 2-1 4-1 6-1-2-2-3-3-5l1-1v-3c-1-2-1-4-3-6z" class="O"></path><defs><linearGradient id="AR" x1="490.917" y1="596.204" x2="495.223" y2="608.059" xlink:href="#B"><stop offset="0" stop-color="#d3d1c8"></stop><stop offset="1" stop-color="#fefefc"></stop></linearGradient></defs><path fill="url(#AR)" d="M500 590c3-3 5-6 9-6v10c-1 4-4 7-6 9-6 6-12 8-19 10-2 0-4 1-6 1h-1l-2-7c5 1 15-3 19-7 2-2 4-4 7-6 1-1 2-1 3-2v-2h-4z"></path><path d="M322 448h-1c-2-4-4-9-5-14 0-1-1-2 0-3h1c4 3 6 10 7 14l8 19c3 5 7 12 8 18-5-7-10-13-16-18-4-4-8-7-13-10h0c-4-3-7-5-8-9l1-1-1-2h2c8 2 17 10 22 17-2-4-4-7-5-11z" class="G"></path><path d="M304 444c1 0 2 0 3 1 2 0 3 1 5 2 1 0 2 1 3 2h1l5 5-1 1-1-1s-1-1-2-1v-1h-1c-2 0-2-1-3-2-2-1-3-1-5-2l-1 1c1 1 1 2 3 2 1 1 1 2 1 3h0c-4-3-7-5-8-9l1-1z" class="F"></path><path d="M322 448h-1c-2-4-4-9-5-14 0-1-1-2 0-3h1c4 3 6 10 7 14v1c0 2 1 3 2 4v2c1 2 2 5 3 8 1 1 2 3 2 5-2-3-4-6-5-9 0-2-2-5-3-7 0 0 0-1-1-1h0z" class="R"></path><path d="M534 849c4-13 9-27 17-38 2-2 3-5 6-6 1-1 3-1 4-1 2 1 2 2 3 4 0 2 0 4-2 6-3 5-8 9-11 14-6 7-11 16-15 25l-2-4z" class="Q"></path><path d="M562 772l7-20c1-3 2-7 4-10 0 0 1-1 2-1 5-3 6-11 13-10 1 1 2 2 2 4 0 7-7 13-10 18-6 7-13 14-17 22l-1-3zm45-113c3-2 6-7 10-6 2 0 2 1 3 2 1 8-12 19-17 26-3 4-4 9-7 12-2 2-3 3-4 3-2-1-1-1-2-2 2-9 7-19 11-27l1-1h1c1-2 1-3 2-4s1-2 2-3z" class="V"></path><defs><linearGradient id="AS" x1="774.768" y1="340.902" x2="765.126" y2="321.891" xlink:href="#B"><stop offset="0" stop-color="#a09b92"></stop><stop offset="1" stop-color="#d1cec1"></stop></linearGradient></defs><path fill="url(#AS)" d="M792 308c1 0 2-1 4 0l1 2c-1 2-2 4-4 5-2 2-5 3-7 5-3 2-6 3-9 6-9 7-17 15-24 24-2 2-5 5-6 8-2 1-3 1-5 3h-1c11-21 29-44 51-53z"></path><path d="M617 371h0l2-1 2-2 3-2s1-1 2-1c1-1 3-2 5-3 0-1 1-1 1-1l1-1 3-1-5 13-2 5c-2 1-5 2-7 3-5 2-12 4-18 7l-6 2c-4 2-7 3-11 4h-2l-4-1 1-1 5-2c2-2 4-2 5-3l9-5 4-3 7-4h1l1-1c1-1 2-2 3-2z" class="N"></path><path d="M590 390l1-1c1-1 1-1 2-1s2-1 3-1c3-1 5-1 8 0l-6 2c-3 0-5 0-8 1z" class="I"></path><path d="M582 391l5-2v1l-3 1h2 1l1-1c1-1 1-1 2 0 3-1 5-1 8-1-4 2-7 3-11 4h-2l-4-1 1-1z" class="S"></path><path d="M617 371h0l2-1 2-2 3-2s1-1 2-1c1-1 3-2 5-3 0-1 1-1 1-1l1-1 3-1-5 13-9 3c-3 1-5 2-7 3s-4 2-6 2c-2 1-3 1-4 1v-2h2l2-2 6-3c1-1 1-1 2-3h0z" class="G"></path><defs><linearGradient id="AT" x1="415.58" y1="393.346" x2="413.299" y2="380.473" xlink:href="#B"><stop offset="0" stop-color="#b2b1a5"></stop><stop offset="1" stop-color="#d4d0c3"></stop></linearGradient></defs><path fill="url(#AT)" d="M379 355c1 1 2 1 2 2 1 2 4 4 5 5 2 1 2 2 4 3l2 2c1 0 2 1 3 2 1 0 1 1 2 2 1 0 1 1 2 1s1 1 2 2l2 1c1 1 2 1 3 2 6 3 12 8 18 10l1 1c2 4 6 4 8 7h-1l1 1-43-14c0-1 0-1-1-2h0c-2-3-4-7-5-10l-5-15z"></path><path d="M391 376l-1-2h0 1c3 2 10 3 14 4h1c1 1 2 1 3 2l-1 1c-3-1-6 0-9-1s-5-3-8-4h0z" class="O"></path><path d="M389 380c15 3 29 11 43 15l1 1-43-14c0-1 0-1-1-2z" class="C"></path><path d="M379 355c1 1 2 1 2 2 1 2 4 4 5 5 2 1 2 2 4 3l2 2c1 0 2 1 3 2 1 0 1 1 2 2 1 0 1 1 2 1s1 1 2 2l2 1c1 1 2 1 3 2v1h-1c-4-1-11-2-14-4h-1 0l1 2c0 1 0 0 1 1h0l1 1h-2c-1-1-2-1-3-1l-1-2v-1c-1-2-1-2-2-3l-1-1-5-15z" class="G"></path><path d="M487 631c-2-1-3-1-4-2-2-3-2-7-4-10 10 3 21 6 27 15 2 3 3 5 4 8 3-9 9-15 19-19 3-2 6-3 10-3l-4 10c-8 2-14 3-19 10-2 3-3 7-5 11-1 1-1-1-2-1l-1 1v-2c-2-4-4-9-7-12-4-4-9-6-14-6z" class="Q"></path><defs><linearGradient id="AU" x1="215.222" y1="277.83" x2="220.752" y2="267.062" xlink:href="#B"><stop offset="0" stop-color="#b5b3ac"></stop><stop offset="1" stop-color="#dad9d3"></stop></linearGradient></defs><path fill="url(#AU)" d="M203 263h9c3 1 6 2 9 4 8 3 15 7 21 14l-2 1c2 0 2 1 3 2v1l-2-2-1 1c2 1 4 1 6 4h-1c-1-1-5-3-6-2 2 1 5 2 7 4 3 2 4 5 6 7l-8-6-3-2-1 1c7 3 11 7 15 13-4-4-9-8-14-11-11-7-25-10-33-20-2-2-3-6-5-9z"></path><path d="M522 910c1-8 4-14 7-21 2-3 3-6 5-9 1-1 3-2 4-2h1c0 2-2 6-3 8l-7 15c4-4 7-12 9-18 1-2 2-4 4-6l3-1h3c1 2 2 2 2 4-1 11-10 21-15 29-5 7-9 15-12 22-1-3 0-7 0-11l-1-10z" class="Q"></path><path d="M527 907h1 0c0 2 0 3-1 5-1 0-1 0-2-1v-1c0-1 1-2 2-3z" class="J"></path><path d="M560 475c2-2 1-3 1-5 0-1 1-2 2-3 1 1 2 1 3 1 4 1 10 1 13 5v2c0 1-1 3-2 4-1 3 0 6 1 9s2 5 2 8c2 1 3 1 4 1 0 2-1 3-2 4-2 2-6 2-9 3-2-4-3-7-7-8-2-1-4-1-6-1s-4 0-5 1c-2 0-4 1-6 1-1 1-3 0-4-1v-3c1-5 6-10 10-13 1-1 4-2 5-5z" class="Q"></path><path d="M545 493c1-1 4-2 5-3h2v1c3-1 5-1 8-1s5 1 8 2v1l-2-1c-1 0-2 0-3-1-2 0-5 0-6 1h-1c-2 0-4 1-5 2 1 0 1 0 2-1 1 0 2 0 3 1s2 0 3 0c0 1 1 1 1 1-2 0-4 0-5 1-2 0-4 1-6 1-1 1-3 0-4-1v-3z" class="N"></path><path d="M560 475c2-2 1-3 1-5 0-1 1-2 2-3 1 1 2 1 3 1l-1 2c0 3 1 5-1 8h-1c-1 2-4 4-4 5h1l1 1-2 1v1h1 1-1c0 1-1 1-2 2v1l2 1c-3 0-5 0-8 1v-1h-2c-1 1-4 2-5 3 1-5 6-10 10-13 1-1 4-2 5-5z" class="F"></path><path d="M608 650c4-7 8-14 13-20 10-13 22-25 36-33 3-2 6-1 9-2 1-1 3-2 4-3 0 1-1 2-1 3-1 3 0 5 0 8s-1 5-3 8c-7 12-27 13-38 21a79.93 79.93 0 0 0-13 13c-1 2-3 5-5 7l-2 2h0v-4z" class="V"></path><defs><linearGradient id="AV" x1="528.327" y1="593.744" x2="520.938" y2="605.52" xlink:href="#B"><stop offset="0" stop-color="#d6d4d1"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AV)" d="M509 577l2-2v1c2 2 6 4 9 5h10c3 0 6-1 9-2l-3 6c1 2 1 3 3 3 2 1 4 1 5 1h-2s-1 1-2 1l3 3h1l5-1-3 8-3 10-2 5c-10-3-19-6-25-13-6-6-6-10-6-18v-1c0-2 1-5-1-6z"></path><path d="M513 588l4 4c2 1 8 5 9 6 0 2-1 1 1 2l1 1h0c1 1 1 2 2 2l-15-9h0c-1-1-2-1-2-2-1-1-1-3 0-4z" class="F"></path><path d="M510 583l2-1a30.44 30.44 0 0 0 8 8c3 3 7 7 12 8 3 1 6 0 9 1 2 1 3 0 5 1l-3 10-13-7c-1 0-1-1-2-2h0l-1-1c-2-1-1 0-1-2-1-1-7-5-9-6l-4-4c0-2 0-3-1-4h-2v-1h0z" class="Y"></path><path d="M509 577l2-2v1c2 2 6 4 9 5h10c3 0 6-1 9-2l-3 6c1 2 1 3 3 3 2 1 4 1 5 1h-2s-1 1-2 1l3 3h1l5-1-3 8c-2-1-3 0-5-1-3-1-6 0-9-1-5-1-9-5-12-8a30.44 30.44 0 0 1-8-8l-2 1h0c0-2 1-5-1-6z" class="M"></path><path d="M524 584h0l7 8c1-2 3-4 4-6 1 2 1 2 1 4-1 2-3 4-5 6l-6-8c-1-1-1-2-1-4z" class="E"></path><path d="M509 577l2-2v1c2 2 6 4 9 5 1 1 4 2 4 3 0 2 0 3 1 4l-3-3c-4 0-9-3-11-6h0c-1 1-1 2-1 4h0c0-2 1-5-1-6z" class="H"></path><path d="M520 581h10c3 0 6-1 9-2l-3 6-1 1c-1 2-3 4-4 6l-7-8h0c0-1-3-2-4-3z" class="Y"></path><path d="M439 778h2c3 2 5 5 7 7 4 6 8 11 11 17 0 1 1 1 1 2 1-1 0-1 1-1 1 1 2 3 3 5 1 4 3 9 4 13l11 30c3 8 7 17 9 25 1 2 1 5 1 7-2 1-2 1-4 1-3-3-5-11-6-15-3-9-9-19-15-26-3-3-7-7-9-10-1-2-1-4-1-5s1-1 2-1c5 0 8 8 11 12 0-4-2-8-3-12-6-11-15-21-22-31-3-4-7-10-6-14 1-2 2-3 3-4z" class="Q"></path><path d="M470 841h1c1 2 1 2 0 4h-1l-2-2 2-2z" class="B"></path><path d="M560 475c-3 2-6 2-9 4s-6 5-9 8c-2-3-3-6-4-9-1-5 0-11 3-16 4-5 10-9 17-11h1c7-1 15 0 21 4 6 2 11 8 13 14 1 5-2 11-4 16-1 3-2 6-4 8h-1s-1-1-1-2c-1-1-1-3-2-4v-5c1-3 2-5 3-8-1-1-1-2-2-3-2-3-6-5-9-5-3-1-7-1-10 1-1 1-2 2-2 3 0 2 1 3-1 5z" class="G"></path><path d="M436 745c-4-8-8-15-12-21-3-5-7-9-11-13-1-2-4-4-4-7-1-1-1-2 0-3h2c2 1 3 3 5 5 3 5 6 10 10 14-3-8-7-17-11-24-3-4-6-7-9-10-3-4-7-7-7-12 1-2 1-2 3-2 4 1 9 10 11 13-3-12-8-21-17-30l-7-7c-1-2-1-3 0-5h1c2 1 3 4 5 6l6 7c-2-6-4-12-8-17s-11-8-13-14c-1-1-1-3 0-5h3c4 0 7 8 8 10-2-10-7-19-15-26-3-3-6-5-9-8-1-2-2-3-1-5 0-2 1-2 2-3 1 0 2 0 3 1 5 2 9 10 12 15s8 10 10 16c5 10 8 20 12 30l35 95h-4z" class="Q"></path><path d="M680 199c-6-1-12-1-16-5-1-2-2-2-2-4 2-2 2-2 4-2 6-1 12-1 18-1h39l93 1h4c1 1 2 1 2 3-2 3-6 5-9 6-7 1-14 1-21 2-3 0-7-1-10 0H680zm-454-12h82 28c3 0 8 0 11 1 1 1 1 1 2 3-1 2-1 2-2 3-5 5-15 4-21 5h-15-67-26c-6 0-13 0-19-2-3 0-8-2-10-5-1-1 0-1 0-3 1-1 2-1 4-1 3-1 7-1 10-1h23z" class="V"></path><path d="M538 448l1-1c1 0 1 0 2-1h2v-1h2l1-1 3-1h2l6-1c5-1 14-2 19-1l5 1 6 3c1 0 2 0 3 1h2l1-1h0l2 2c2 0 2 1 3 2l1 1 2 2-4 12c-7-10-15-16-27-18-10-1-19 1-27 7l-3 2c-5 6-9 12-8 20 0 4 1 8 3 11 6 10 12 19 24 22 7 2 15 1 21 0-1 4-3 9-4 12-18-1-32-6-44-18 2 5 9 9 14 12 7 3 14 6 22 7 2 0 6 0 7 1l-1 3c-3 2-8-1-10 0h-3l-2-1h-1-1c-1 0-2-1-2-1l-4-1c-1 0-3-1-4-2l-4-2-5-2-3-3c-2-1-3-2-5-3l-2-2-1-2-2-2-1-1c0-1-1-1-1-2h0c-1-1-1-1-1-2 0-3-2-6-2-9-1-4-1-7-1-11 2-2 2-7 3-9 1-4 3-7 5-10 1-2 3-3 4-4 2-2 3-3 5-4s2-2 2-4z" class="G"></path><path d="M535 486c-3-2-3-5-4-8l-1 3v1l-1-1v-2l-1-2c0-5 2-12 5-16 2-3 4-5 7-6-5 6-9 12-8 20 0 4 1 8 3 11z" class="N"></path><path d="M546 514c-2 0-3 1-4 0-6-1-8-5-11-9-1-2-3-5-5-7h1v-1l-1-2h0v-4l3 6c1 1 1 2 2 3l1 1v1c2 5 9 9 14 12z" class="O"></path><path d="M474 493v2c0 1-1 1-2 2-2 0-4-1-6-1-4-1-9-1-13 0-3 1-5 4-7 7h0c-3 0-7-1-9-2s-3-2-4-3l2-2s1-1 2-1c2-3 4-7 5-11 1-1 1-2 1-3 0-2-3-5-3-7 1-2 3-3 5-4 3-1 8-2 11-1h1 0v-1c-2-2-4-2-7-2-4-1-10 0-13 4-4 4 1 8 1 13s-3 8-6 11c-3-5-5-11-6-17-2-4-1-7 1-11 4-7 10-11 18-14 8-2 18-1 26 3 5 3 9 8 11 14 2 7-1 12-4 19-3-3-6-7-9-9-4-3-8-4-12-5l1 1c1 1 4 2 6 3 4 3 10 10 10 15z" class="Q"></path><path d="M474 493c-1 0-2-1-3-2-1 0-1 0-2-1h0l-2-1c-1 0-2-1-3-2-5-2-5-5-8-9l-1-2c0-1 0-2 1-3l1 1 1 1c1 1 4 2 6 3 4 3 10 10 10 15z" class="G"></path><path d="M494 395c9-5 11-12 15-22 0-1 0-3 1-3l3 9v4h0c-1 1 0 2 0 3l1 1c0 3 2 5 4 7l1 1c1 2 2 3 4 4 1 1 1 1 3 2h0c1 1 1 1 2 1h0c2 1 2 2 4 2l1 1h2c1 0 1 1 1 2h-1l-1 1c2 1 3 1 5 2l1 1h-2l-3 1-1 1 7 1-8 3c-1 0-3 1-5 1-3 1-6-2-9-4v1c0 1 1 2 0 3v1c-1 3-2 6-4 9-2 1-3 1-5 1s-5-2-6-4c-1-1-1-3-1-5h-1-7l-30-13h-2l1-2c-3 0-6-1-8-2l-2-1c-4-1-8-4-12-4 1 0 2 1 2 2l-5-2c-2 0-4-2-6-2h0l-1-1h1c-2-3-6-3-8-7l-1-1h2c1 1 1 1 2 1 1 1 1 1 3 1-2-2-4-2-6-4l1-1c3 2 7 3 10 4h4v-1l5 2c5 2 10 5 16 6h5l9 1c3 0 8-1 10 1l9-2z" class="T"></path><path d="M515 414l2-1-1-1h1s2 1 2 2v1c0 1 1 2 0 3l-4-4z" class="N"></path><path d="M526 411h-1 0c-2-2-4-3-5-4-2-2-5-6-5-10 3 6 5 10 11 12h1l3 1c-1 1 0 1-1 2l-3-1z" class="H"></path><path d="M526 411l3 1c2 0 4 1 5 1l7 1-8 3c-1-1-2-1-3-1h-2l-1-1h-2l-1-1c-1 0-1 0-2-1h0 3c0 1 0 1 1 1h4v-1c-2 0-3 0-4-2z" class="S"></path><path d="M506 416c0-2 1-3 2-5h0v1c0 1 0 2 1 3v-6h1c1 2-1 6 2 8v-1-3c1 1 1 1 1 2-1 3 0 5-1 7-1 0-2 1-3 1v-1-1l-1 1-2-1v-2-1l1-1h0l-1-1z" class="N"></path><path d="M475 409h1c2 1 6 1 9 1 9 0 15-6 20-12-2 6-7 11-13 13-4 1-7 1-10 1h-1c-6-1-11-6-16-5h-2l1-2c4 1 7 2 11 4z" class="H"></path><path d="M513 415c1 3 1 4 1 7l1-1v-4-2-1l4 4v1c-1 3-2 6-4 9-2 1-3 1-5 1s-5-2-6-4c-1-1-1-3-1-5h-1c1-2 2-3 3-4v1c0 1-1 1-1 2h1c1-1 1-2 1-3l1 1h0l-1 1v1 2l2 1 1-1v1 1c1 0 2-1 3-1 1-2 0-4 1-7z" class="W"></path><defs><linearGradient id="AW" x1="503.123" y1="400.787" x2="499.499" y2="393.943" xlink:href="#B"><stop offset="0" stop-color="#c2c0c0"></stop><stop offset="1" stop-color="#dad9cd"></stop></linearGradient></defs><path fill="url(#AW)" d="M491 404c1-1 3-2 5-3s5-5 6-7h1c1-2 2-4 2-6 0-1 1-1 2-2v-2-1h1 1c0 1-1 2-1 3v3 1h0v1l-1 2v1c0 2-1 3-2 4-5 6-11 12-20 12-3 0-7 0-9-1h-1 3c1 1 2 0 3 0h1c1 0 1 0 2-1-1-1-5 1-7-1 1-1 5 0 6-1h4c1-1 3-1 4-2z"></path><path d="M494 395c9-5 11-12 15-22 0-1 0-3 1-3l3 9v4h0c-1 1 0 2 0 3l1 1c0 3 2 5 4 7l1 1c1 2 2 3 4 4 1 1 1 1 3 2h0c1 1 1 1 2 1h0c2 1 2 2 4 2l1 1h2c1 0 1 1 1 2h-1l-1 1c2 1 3 1 5 2l1 1h-2l-3 1-1 1c-1 0-3-1-5-1 1-1 0-1 1-2l-3-1h-1c-6-2-8-6-11-12 0-1 0-2-1-2 0-1 0-2-1-3h0v-1c-1-2-1-3-2-5v-1l-1-1c0-1 0-1-1-1h-1-1v1 2c-1 1-2 1-2 2 0 2-1 4-2 6h-1c-1 2-4 6-6 7s-4 2-5 3c-1-1-3 0-4 1h-2-1c-3 1-7 1-9-1 4 0 10-2 15-4 2 0 3-1 5-2l2-1c3-1 5-4 5-7-1 0-5 4-7 5h-1z" class="F"></path><path d="M527 409l1-1h-1c-3-1-7-5-9-8l1-1 4 4h0v-1-1c2 2 2 2 5 2 2 2 5 3 7 4l-1 1c2 1 3 1 5 2l1 1h-2l-3 1-1 1c-1 0-3-1-5-1 1-1 0-1 1-2l-3-1z" class="N"></path><path d="M529 412c1-1 0-1 1-2 3 1 6 1 8 1l-3 1-1 1c-1 0-3-1-5-1z" class="B"></path><path d="M424 387h2c1 1 1 1 2 1 1 1 1 1 3 1-2-2-4-2-6-4l1-1c3 2 7 3 10 4h4v-1l5 2c5 2 10 5 16 6h5l9 1c3 0 8-1 10 1l9-2h1c2-1 6-5 7-5 0 3-2 6-5 7l-2 1c-2 1-3 2-5 2-5 2-11 4-15 4 2 2 6 2 9 1h1 2c1-1 3-2 4-1-1 1-3 1-4 2h-4c-1 1-5 0-6 1 2 2 6 0 7 1-1 1-1 1-2 1h-1c-1 0-2 1-3 0h-3c-4-2-7-3-11-4-3 0-6-1-8-2l-2-1c-4-1-8-4-12-4 1 0 2 1 2 2l-5-2c-2 0-4-2-6-2h0l-1-1h1c-2-3-6-3-8-7l-1-1z" class="F"></path><path d="M444 396c2-1 3-2 5-1l6 3v1h-2l-9-3z" class="M"></path><path d="M445 389c5 2 10 5 16 6l2 1c0 1-1 1-2 1-5 0-9-2-13-4-2-1-6-1-7-3 1-1 1 0 2 0s1-1 2-1z" class="S"></path><path d="M425 388l19 8 9 3c1 1 2 1 4 2 1 0 2 0 2 1h1 2 2c1 0 1 0 2 1l2 1h0-2c-1-1-3-1-5-1h-1c-1 0 0 0-1-1h-2l-1 1-2-1c-4-1-8-4-12-4 1 0 2 1 2 2l-5-2c-2 0-4-2-6-2h0l-1-1h1c-2-3-6-3-8-7z" class="K"></path><path d="M455 398c4 2 8 2 11 3 6 1 11 1 17 0h1l5-1h1c-5 2-11 4-15 4 2 2 6 2 9 1h1 2c1-1 3-2 4-1-1 1-3 1-4 2h-4c-1 1-5 0-6 1 2 2 6 0 7 1-1 1-1 1-2 1h-1c-1 0-2 1-3 0h-3c-4-2-7-3-11-4-3 0-6-1-8-2l1-1h2c1 1 0 1 1 1h1c2 0 4 0 5 1h2 0l-2-1c-1-1-1-1-2-1h-2-2-1c0-1-1-1-2-1-2-1-3-1-4-2h2v-1z" class="I"></path><path d="M494 395h1c2-1 6-5 7-5 0 3-2 6-5 7l-2 1c-2 1-3 2-5 2h-1c2-1 3-1 4-2-2-1-7 1-9 2-4 1-12 1-16 0h0c-3-1-5-1-7-3 1 0 2 0 2-1l-2-1h5l9 1c3 0 8-1 10 1l9-2z" class="Z"></path><path d="M461 395h5l9 1c3 0 8-1 10 1-5 2-16 2-22-1l-2-1z" class="H"></path><path d="M645 172h195c3 0 4 1 6 3 1 2 1 2 0 4-2 2-6 4-8 5-11 2-22 1-32 1h-44-65-37c-5 0-10-1-14-2-3-1-6-3-7-5-1-1-1-2 0-3 1-2 3-2 6-3zm-474 0c10-1 21 0 31 0h60 69 35c2 0 3 0 5 1 1 0 2 1 2 3 1 1 0 1 0 2-2 3-5 4-8 5-7 2-13 2-20 2h-23-63-55c-8 0-17 1-25 0-5-1-11-2-14-6-1-1-1-2 0-3 2-3 3-3 6-4z" class="V"></path><path d="M476 548c-8 1-14 7-20 12 0-3-1-6-2-8l1-2h0c-1 0-1 1-2 0 0-1-1-2-1-4 2-2 5-3 8-4 4-1 7-3 12-4 8-2 17-2 26-1 5 0 9 1 14 1 3 0 7 0 10-1 7-1 15-1 22 0 8 1 15 4 22 8l-3 11c-6-4-10-7-18-8 1 1 1 2 2 3l2 1c4 2 8 6 11 9 0 3-2 5-2 8-3 7-6 15-9 23l-5 1h-1l-3-3c1 0 2-1 2-1h2c-1 0-3 0-5-1-2 0-2-1-3-3l3-6c-3 1-6 2-9 2h-10c-3-1-7-3-9-5v-1l-2 2-11 5c-3-1-5 0-8-1-3 0-6-1-9-1-1-1-2-3-3-5-2-4-2-8-1-12 1 0 3-2 4-3 2-2 3-4 6-5l6-6h1l5-2c-8-1-16-1-23 1z" class="Q"></path><path d="M540 559h-1c-2-1-3-3-5-4h-1c-1-1-1-1-2-1h-2-2l-2-1v-3c-1-1-2-2-2-3s0-1 1-2c7 0 15 1 21 3 1 1 1 2 2 3l2 1c4 2 8 6 11 9 0 3-2 5-2 8-3 7-6 15-9 23l-5 1h-1l-3-3c1 0 2-1 2-1h2c-1 0-3 0-5-1-2 0-2-1-3-3l3-6c2-2 4-4 5-7 1-5-2-9-4-13z" class="O"></path><path d="M540 559c4 4 8 10 8 16l1 1v1c0 3 0 7-2 10-1 1-2 1-3 2-1 0-3 0-5-1-2 0-2-1-3-3l3-6c2-2 4-4 5-7 1-5-2-9-4-13zm-21-15h0v1l-1 1c-1 2-2 7-1 9 4 6 12 1 16 3 2 1 3 3 4 5 0 3 0 5-2 8-4 5-15 3-20 1-1-1-4-3-5-3v1c-5 2-9 4-15 5-2 0-5 0-7-2-3-1-4-3-5-5 0-2 0-5 1-7 0-1 2-3 3-4 1 0 2 1 3 1 3 0 6 0 9-1 1 0 2-1 3-3 1-3 1-6 0-9v-1l1 1c3 2 2 9 2 13 2 0 8 0 9-1v-3c1-4 2-7 5-10z" class="E"></path><path d="M399 407l-1-1 1-1c27-2 57 5 81 18 6 3 13 7 19 12 3 3 7 7 11 10 5-3 8-7 12-11 4-2 7-5 11-7 20-13 46-19 69-22 5 0 11-1 16 0h0c0 1 0 2-1 3 0 1-1 4-1 4-2 4-3 8-5 12-4 3-6 5-12 7-2-1-3-1-5 0-17-6-35-6-50 2h-1 1c15-6 35-5 49 2l12 7c-1 2-2 8-4 9l-2-1-1-1c-1-1-1-2-3-2l-2-2h0l-1 1h-2c-1-1-2-1-3-1l-6-3-5-1c-5-1-14 0-19 1l-6 1h-2l-3 1-1 1h-2v1h-2c-1 1-1 1-2 1l-1 1c0 2 0 3-2 4s-3 2-5 4c-1 1-3 2-4 4-2 3-4 6-5 10-1 2-1 7-3 9 0 4 0 7 1 11 0 3 2 6 2 9 0 1 0 1 1 2h0c0 1 1 1 1 2l1 1 2 2 1 2 2 2c2 1 3 2 5 3l3 3 5 2 4 2c1 1 3 2 4 2l4 1s1 1 2 1h1 1l2 1h3c3 1 6 0 9 2 0 1-1 2-1 4-3-1-6-1-9-2-9-2-17-5-25-9h-1c11 6 22 10 35 11l-2 5c-13-2-25-5-36-12s-22-19-24-32h0c-2 7-5 13-10 19-13 16-32 23-52 25-1-1-1-2-2-4h1c6-1 14-3 20-5 0-1-2 0-2 0-5 2-11 3-16 3-1 0-2 1-4 0v-2c2-2 7-1 9-2h2-1c-3 0-7 1-10 1-2-1-2-3-3-5 12-1 21-3 31-8v-1c-11 5-20 7-31 8l-6-14c8 2 17 3 24 1 12-3 19-11 24-21 3-7 5-12 3-20-2-5-4-8-8-12-2-2-4-4-7-5-8-4-17-5-25-3-11 2-19 8-25 16 0 1-1 1-1 2l-2-3c1-2-1-5-2-7h0c2-3 4-5 6-7l1-1c-3 2-5 4-8 7 0-4-2-7-3-10 15-13 38-18 57-13 3 1 6 2 8 3 2 0 4 1 5 1l-2-1c-18-9-38-8-56-2l-9 4-3-6h-1l1-2-2-1-2-1-4-4-4-11h0l-2-4z" class="Q"></path><path d="M528 454c2-1 4-3 6-4l1-1c1 0 2-1 3-1 0 2 0 3-2 4s-3 2-5 4l-3-2z" class="T"></path><path d="M520 466h0c-1 4-2 8-1 11v1 1c0 4 0 7 1 11 0 3 2 6 2 9-1-2-2-5-3-7l-1-6c-1-5-1-11 0-16 1-1 1-3 2-4z" class="M"></path><path d="M413 428c3 2 6 3 10 3l2 1-9 4-3-6h-1l1-2z" class="Z"></path><path d="M580 413v1c-2 3-7 8-11 9h-1l2-3c3-4 5-6 10-7z" class="K"></path><path d="M417 455h0c2-3 4-5 6-7v2c0 1-4 3-4 5 1-1 3-1 4-2s1-1 2-1l1 1c-1 2-3 3-4 5s0 2-1 4c0 1 0 1 1 1 0 1-1 1-1 2l-2-3c1-2-1-5-2-7z" class="O"></path><path d="M417 455l1 1c1 0 2 0 4-1 0 1-1 2-1 3l-1 2h0l-1 2c1-2-1-5-2-7z" class="F"></path><path d="M440 413c2-1 4 1 5 1l1 1c3 2 7 5 8 8h-2c-6 0-8-6-12-10h0z" class="T"></path><path d="M487 491v4c1 2-1 5-1 7 2-2 4-5 5-7 1 1 1 1 0 2-3 4-5 8-9 12-1 2-2 2-4 2l-3 2-1-1 3-1v-1c6-4 8-12 10-19z" class="M"></path><path d="M520 466c0-2 1-3 2-5l1-1c1-2 3-4 5-6l3 2c-1 1-3 2-4 4-2 3-4 6-5 10-1 2-1 7-3 9v-1-1c-1-3 0-7 1-11h0z" class="Y"></path><path d="M418 409c3 0 5 1 7 2s4 3 6 4c4 3 7 6 10 10h-5c-2 0-3-2-5-3-4-4-9-8-13-13zm183 0h1s0 1-1 1c-2 3-14 14-16 14-1 1-4 0-5 0h-1c7-6 13-14 22-15z" class="I"></path><defs><linearGradient id="AX" x1="411.279" y1="409.236" x2="411.707" y2="424.265" xlink:href="#B"><stop offset="0" stop-color="#a89d8c"></stop><stop offset="1" stop-color="#bfbba4"></stop></linearGradient></defs><path fill="url(#AX)" d="M399 407c7 3 11 6 17 10 4 4 8 7 12 10l-1 1h-2c-1 0-3-2-4-2-7-5-13-10-20-15h0 0l-2-4z"></path><defs><linearGradient id="AY" x1="610.315" y1="408.134" x2="596.603" y2="429.492" xlink:href="#B"><stop offset="0" stop-color="#938e81"></stop><stop offset="1" stop-color="#b9b3a5"></stop></linearGradient></defs><path fill="url(#AY)" d="M616 412c-1 0-17 14-21 17-1 0-2 0-3-1v-1c7-7 17-15 25-19 0 1-1 4-1 4z"></path><path d="M479 455c2 0 4 1 5 3h0c0-1 0-2-1-3v-1h0l4 5c4 2 7 8 9 13 1 3 0 8 0 11v4h0c-1 1-1 2-1 2v1 1c0 1-1 2-1 3v1l1-1c-1 2-1 3-2 4-4 6-8 12-13 17l-1-1h0c2-2 4-3 5-5 1-1 2-2 2-3l1-1c2-3 4-5 4-8 1-1 1-1 0-2-1 2-3 5-5 7 0-2 2-5 1-7v-4c0-2 0-3-1-5h1v-1c-1 0-2 1-3 2 3-7 5-12 3-20-2-5-4-8-8-12z" class="Y"></path><path d="M479 455c2 0 4 1 5 3h0c0-1 0-2-1-3v-1h0l4 5c1 5 3 8 3 14h0c-1-1-1-2 0-4l-1-1v-1c0-1-1-1-1-2-1 1-1 1-1 2-2-5-4-8-8-12z" class="N"></path><path d="M537 246c13-17 32-29 54-32h2c25-3 51 5 71 20 2 1 4 3 6 6v11l-1 8c-5-6-14-15-22-18 8 6 17 13 22 22l-4 16c-1 1-1 1-1 2l-1 1c1 2 0 4-1 6l-3 9-5 15c-7-23-19-45-41-56-15-8-32-10-47-5-9 3-15 8-19 16-2 4-2 9 0 13 1 3 3 5 6 6 2 1 5 1 8 2-2 1-4 3-6 4-2 4-4 9-6 13h1c9-2 18 0 27 3-4-2-10-5-11-10-1-3 0-7 2-10 5 2 9 4 15 2l2-2v-1c-2 0-4 1-6 0-3-2-5-6-6-9 2-6 5-10 10-13 2-1 5-2 8-1 2 0 4 3 5 4 2 3 4 5 7 7 4 5 10 9 15 13 3 2 7 4 9 6s3 5 4 7c3 12 1 27-5 38-3 7-10 12-15 17-1-1-2-2-2-3 0-2 1-4 2-6-1 0-3 5-3 6-1 2-1 5-1 7 1 1 1 1 2 1 3 1 5 0 8-2 6-6 11-14 18-19 3-1 6-3 9-4-2 7-5 16-8 23l-3 1-1 1s-1 0-1 1c-2 1-4 2-5 3-1 0-2 1-2 1l-3 2-2 2-2 1h0c-1 0-2 1-3 2l-1 1h-1l-7 4-4 3-9 5c-1 1-3 1-5 3l-5 2-1 1c-7 4-13 6-20 8l-12 3c-1 1-2 1-4 1 1 0 2 1 3 1s4-1 6-1l1-1h3c-2 1-7 2-10 3h-1c-2-1-3-1-4-1-3 0-5 0-7-1-3 0-5-1-8-2h0c-1 0-1 0-2-1h0c-2-1-2-1-3-2-2-1-3-2-4-4l-1-1c-2-2-4-4-4-7l-1-1c0-1-1-2 0-3h0v-4l-3-9c-1 0-1 2-1 3-4 10-6 17-15 22l-9 2c-2-2-7-1-10-1l-9-1h-5c-6-1-11-4-16-6l-5-2v1h-4c-3-1-7-2-10-4l-1 1c2 2 4 2 6 4-2 0-2 0-3-1-1 0-1 0-2-1h-2c-6-2-12-7-18-10-1-1-2-1-3-2l-2-1c-1-1-1-2-2-2s-1-1-2-1c-1-1-1-2-2-2-1-1-2-2-3-2l-2-2c-2-1-2-2-4-3-1-1-4-3-5-5 0-1-1-1-2-2h-1c-2-4-3-9-5-13 0-1-1-3-1-5h3c9-1 14 8 19 14 3 4 7 7 10 10 2 2 4 4 7 3 2-1 2-2 3-3 1-5-2-9-4-13h0l1 4c1 2 2 5 1 7l-1 1c-1 0-2-1-3-2-10-6-17-21-19-31-2-11-2-23 4-32 3-5 7-4 11-7 6-5 12-10 17-16 3-3 6-8 11-8 3-1 7 1 9 3 4 3 6 8 6 12 0 3 0 7-2 8-1 1-2 1-4 1s-2-2-3-4c-1 1-1 1 0 2v1c1 2 2 3 5 4 3 0 10-3 11-3 0 0 1 0 1 1 1 3 1 6-1 9s-5 5-7 7c-6 5-10 10-12 18v1c-2 5-2 11-1 16 2 6 4 12 8 16l-1-3v-1c0-2 0-3-1-4h0c0-3-1-4-2-6h-2c-1-5-1-10-1-15 1-6 4-13 8-17 3-3 8-5 12-6 3-1 6-1 9 0l8 1c-2-4-4-8-6-11-1-2-6-4-6-6 4 0 8-1 11-5 2-3 3-8 2-12-1-6-6-12-12-16-11-7-25-8-38-5-21 5-35 20-45 38-6 9-9 20-14 30 0-2-1-6-2-7l-3-8c-1-2-3-5-3-7s3-7 4-9l-1-1-4 9-1-4c0-1-1-3-1-4 0-4 3-9 5-12 11-20 29-34 51-41h1-1c-3 0-6 1-8 2-14 3-27 11-37 22-3 5-6 10-10 14-1 0-2-1-3-1v-2c1-3 3-5 4-7 7-10 17-18 27-24-15 4-28 17-36 30 0-2-1-5-1-7v-1c-2-2-2-12-3-15 6-7 14-13 21-18 24-13 51-18 78-10 18 5 34 18 43 34-1 0-1 0-1-1-2-2-3-3-5-4 3 3 5 7 7 11 1 2 3 4 3 7 0 2 0 3 1 5 1 3 1 8 0 11l1 4c-1 2-1 6-3 8l-8-3c-2 0-3-1-5 0 6 7 17 7 24 13 5 4 8 10 12 16 6-15 16-19 30-25 2-1 4-2 6-4h-1-3l-5 2-3 1c-2 0-3 1-4 0-4-7-4-20-2-27v-1c2-7 5-12 9-18z" class="V"></path><path d="M425 238c2-1 3-2 5-2 2 1 2 1 3 2-1 1-3 1-4 1s-2 0-4-1z" class="U"></path><path d="M543 313c4-2 8-3 12-3-5 2-12 5-15 9h-1c1-1 0-1 1-1 1-2 2-3 3-5z" class="L"></path><path d="M587 283h1c1 1 2 1 3 2s0 1 0 2l-1 2c-2 0-3 1-4 0 0-2 0-4 1-6z" class="P"></path><path d="M505 330c-3-3-6-4-10-5h-1c2-1 3-1 5-1 4 0 7 3 10 6-1-1-3-2-4-3h-1c1 1 1 2 1 3z" class="B"></path><path d="M621 244l-18-7c6 0 15 3 21 6-1 0-2 0-3-1h-2-1 1l2 2z" class="C"></path><path d="M408 358l1-1c-1-2-1-4-2-5-1-2-1-3-2-4 2 2 3 4 5 5l1-1c1 2 2 5 1 7l-1 1c-1 0-2-1-3-2z" class="F"></path><path d="M485 370c1 0 2 0 3 1s1 2 1 3c-1 1-2 2-3 2h-3c-1-2-1-2-1-4 1-1 1-2 3-2z" class="C"></path><path d="M481 275c1 2-1 5 1 5l1 2h0c0 2-1 3-2 5l-2 2c-2 0-3-1-5 0v-1c1-1 2-2 3-2l2-2c1-1 1-3 2-5v-4z" class="M"></path><path d="M433 283c1 0 2 0 3 1h2c-1 1-1 1 0 2l-1 1-2 3h-3c-1-2-2-2-2-4 1-2 1-2 3-3z" class="J"></path><path d="M433 283c1 0 2 0 3 1h2c-1 1-1 1 0 2l-1 1h-1l-1 1-1-1-1-3v-1z" class="G"></path><path d="M534 370c1 0 2-1 3 0s2 2 2 4c-1 1-1 1-2 3-2 0-3 0-4-1s-1-2-1-3c0-2 0-2 2-3z" class="C"></path><path d="M461 309c5 1 14 2 18 6h0v4l-1-1c-3-2-7-3-9-5-1-1-1 0-2 0h0c-4-2-7-3-11-3l5-1z" class="K"></path><path d="M479 315c1 2 6 4 6 6 1 2 2 5 2 7 0 5 0 10-2 15l-1-1c2-3 2-6 2-10 0-2-1-4-2-6l-5-7v-4z" class="E"></path><path d="M408 327c-1-2-1-4-1-6-1-12 2-24 11-34h0c-9 12-11 24-9 38l-1 2z" class="L"></path><path d="M541 341c1 1 2 3 4 3l2-1c1 2 2 3 3 4 2 0 4-4 6-5h0c-3 4-6 8-11 8-3 0-6-2-8-4l-1-1h1l2 1c1-1 0-1 1-1 1-1 0-3 1-4z" class="C"></path><path d="M541 341c1 1 2 3 4 3-1 1-2 2-4 3-1 0-1-1-2-1 1-1 0-1 1-1 1-1 0-3 1-4z" class="K"></path><path d="M510 332c4-4 9-7 14-9l1 1c-2 4-7 4-10 8-1 1-2 3-2 5-2 6 0 14-1 20-1-6 1-12 0-18 0-2-1-5-2-7z" class="M"></path><path d="M409 325v1c2 13 12 26 20 37-8-6-13-13-16-21-2-5-3-10-5-15l1-2z" class="J"></path><path d="M449 243h-1-3c-5-1-12-2-17-1h-1l-14 3-1 1-1-1s1-1 2-1c2-1 4-1 6-3h1 0c2 0 4-1 5-2v-1c2 1 3 1 4 1v1h10c1 0 2 0 3 1h1l3 1h1 2v1z" class="F"></path><path d="M531 331c1-4 2-6 3-10 0-2 0-5 1-7 0-6 3-11 6-16h0c-3 7-5 13-6 21 2-3 5-5 8-6-1 2-2 3-3 5-1 0 0 0-1 1-1 3-3 5-4 7-1 1-2 2-2 3l-2 2z" class="R"></path><path d="M535 326c0 3 0 5 1 8h0c1 3 3 5 5 7-1 1 0 3-1 4-1 0 0 0-1 1l-2-1h-1c-2-1-3-4-3-5-2-2-2-7-2-9l2-2c0-1 1-2 2-3z" class="X"></path><path d="M533 340v-1-1c0-2-2-5-1-7l1 1h0c1 5 5 8 7 12l-3 1h-1c-2-1-3-4-3-5z" class="I"></path><path d="M456 328c2 3 5 5 8 6 2 3 4 6 5 9 1 1 1 2 3 3 1 2 2 3 5 3l1 1c-3 0-5 0-8-2-2-1-5-4-6-6-4-5-7-8-8-14z" class="Y"></path><path d="M359 303l5-14c2-6 6-11 10-16 2-2 4-4 6-7 2-2 5-5 7-6l2-2h1l-5 5c-6 5-10 12-14 19-3 6-10 13-10 21 0 1 0 3 1 4v4l-3-8z" class="U"></path><path d="M441 365h-1c-8-6-14-15-16-25h0c-2-6-1-12-1-18 1 1 1 2 1 4 0 3 1 6 2 9 2 11 8 21 16 28-1 1-1 1-1 2z" class="B"></path><path d="M484 326c1 2 2 4 2 6 0 4 0 7-2 10l1 1c-2 4-3 5-7 7l-1-1c-3 0-4-1-5-3v-1c0-1 0-1 1-1 0-1 1-2 1-3 2 1 5 1 7 0s2-3 2-4l1-11z" class="I"></path><path d="M484 342l1 1c-2 4-3 5-7 7l-1-1c-3 0-4-1-5-3v-1c2-1 5 1 7 1h1c2-1 3-2 4-4z" class="J"></path><defs><linearGradient id="AZ" x1="480.449" y1="340.601" x2="477.31" y2="321.357" xlink:href="#B"><stop offset="0" stop-color="#ccc6b9"></stop><stop offset="1" stop-color="#e9e7e0"></stop></linearGradient></defs><path fill="url(#AZ)" d="M478 318l1 1 5 7-1 11c0 1 0 3-2 4s-5 1-7 0c4-7 6-15 4-23z"></path><path d="M436 384l-6-5v-1c13 10 29 14 45 16h4l-4 2-9-1h-5c-6-1-11-4-16-6l-5-2v1h-4v-4z" class="C"></path><path d="M436 384l4 3v1h-4v-4z" class="O"></path><path d="M449 242c2 1 4 1 6 2h0c3 1 6 3 8 4 5 3 9 6 13 10 2 2 3 3 4 5 1 1 1 1 2 1 2 3 2 11 2 14l-1 4h0l-1-2c-2 0 0-3-1-5h0l-1-1c0-3-1-5-2-8-5-8-12-16-20-20l-9-3v-1z" class="I"></path><path d="M476 258c2 2 3 3 4 5 1 1 1 1 2 1 2 3 2 11 2 14l-1 4h0l-1-2c1-5-1-10-3-15-1-2-3-4-3-7z" class="T"></path><path d="M634 251c12 8 22 18 29 31 1 2 0 4-1 6l-1-4c-1 0-1-1-2-2l-1-1-4-6v-1l-2-2c0-1-1-1-1-2-1-1-2-2-2-3-1-1-3-2-3-3l-5-5c-2-1-1 0-2-1 0-1-1-1-2-2l-5-3 1-1 1-1z" class="O"></path><path d="M597 282h1c7 6 12 13 14 21l1 1c3 14-2 24-6 37l-1 2-3 6c-1 3-3 5-4 7l-1 1c-2 3-5 5-7 8l-2 1v-1c1-1 4-3 5-5l10-17c1-2 2-4 3-7 1-2 2-3 2-5l2-4v-1c1-3 1-7 1-11 0-13-6-24-15-33z" class="K"></path><path d="M536 265c0 2-2 5-2 7v2 1-1-1l1-1v-1l1-1c0-1 0-2 1-2l1-1 3-5c3-4 9-9 13-12l1-1h1 0 0l-2 2-5 5c0 4-5 7-7 11-2 2-3 4-5 6-1 4-1 10 1 14 1 1 2 1 3 2h1l-5 2-4-2c-1-8-1-18 3-24z" class="T"></path><path d="M549 256c0 4-5 7-7 11-2 2-3 4-5 6 1-4 3-7 5-10s5-6 7-7z" class="F"></path><path d="M539 319h1v1l2 9v2c1 2 1 4 2 6 1 1 1 3 3 3v3l2 2c1-1 2-3 4-3l3-3h0l1-1c1 0 2-1 3-1 0-1 0-1 1-2l1-1v1c0 2-4 7-6 7h0c-2 1-4 5-6 5-1-1-2-2-3-4l-2 1c-2 0-3-2-4-3-2-2-4-4-5-7h0c-1-3-1-5-1-8 1-2 3-4 4-7z" class="O"></path><defs><linearGradient id="Aa" x1="639.747" y1="262.061" x2="645.184" y2="256.31" xlink:href="#B"><stop offset="0" stop-color="#030200"></stop><stop offset="1" stop-color="#2e2d2b"></stop></linearGradient></defs><path fill="url(#Aa)" d="M621 244l-2-2h-1 1 2c1 1 2 1 3 1 4 1 8 3 11 5 13 9 22 18 30 31-1 1-1 1-1 2l-1 1c-7-13-17-23-29-31l-13-7z"></path><path d="M482 264c-1-2-2-5-3-7v-1l4 7c2 1 2 1 3 3v1h1l-1-2c0-1-1-1-1-2v-6c1 2 3 4 3 7 0 2 0 3 1 5 1 3 1 8 0 11l1 4c-1 2-1 6-3 8l-8-3 2-2c1-2 2-3 2-5l1-4c0-3 0-11-2-14z" class="M"></path><path d="M482 264c-1-2-2-5-3-7v-1l4 7 1 3v2 1c1 6 2 11 0 17v3h1 1l2-4v1c0-2 0-2 1-3v-3l1 4c-1 2-1 6-3 8l-8-3 2-2c1-2 2-3 2-5l1-4c0-3 0-11-2-14z" class="X"></path><defs><linearGradient id="Ab" x1="443.24" y1="345.86" x2="430.575" y2="349.574" xlink:href="#B"><stop offset="0" stop-color="#a5a29c"></stop><stop offset="1" stop-color="#d8d6cf"></stop></linearGradient></defs><path fill="url(#Ab)" d="M426 335h1v-5c1-8 1-16 6-23 0 1-1 3-2 5s-1 4-2 6c-1 6-1 15 1 20h0c1 3 3 5 4 7v1c0-2 0-2-1-3 0-1 1-2 1-3 2 6 4 12 8 16 0 1 0 1 1 2v-1c2 2 3 3 4 5l2 2c1 1 2 2 4 2l2 1c1 0 1 1 2 1-6 0-10-2-15-5-8-7-14-17-16-28z"></path><path d="M467 313c1 0 1-1 2 0 2 2 6 3 9 5 2 8 0 16-4 23 0 1-1 2-1 3-1 0-1 0-1 1v1c-2-1-2-2-3-3-1-3-3-6-5-9 2-1 3-2 4-4 3-3 3-8 2-12v-1c0-2-1-3-3-4z" class="Q"></path><defs><linearGradient id="Ac" x1="527.116" y1="269.97" x2="535.77" y2="269.58" xlink:href="#B"><stop offset="0" stop-color="#949490"></stop><stop offset="1" stop-color="#c0bdb2"></stop></linearGradient></defs><path fill="url(#Ac)" d="M537 246l-1 3c1 0 2-1 2-2 1 0 1-1 3-1v1c-2 2-4 5-5 7v1c-1 3-4 5-4 9l1-1 1-2c0 1 1 1 1 1l2-3c0-1 1-2 1-3 1 0 2 0 2-1l1 1-2 2h0c-1 2-2 5-3 7-4 6-4 16-3 24l4 2-3 1c-2 0-3 1-4 0-4-7-4-20-2-27v-1c2-7 5-12 9-18z"></path><path d="M539 258l-3 3c-3 6-4 13-5 20 0 2 1 5 1 8-3-6-3-11-3-17 0-3 0-5 1-7 2-4 3-7 6-11v1c-1 3-4 5-4 9l1-1 1-2c0 1 1 1 1 1l2-3c0-1 1-2 1-3 1 0 2 0 2-1l1 1-2 2h0z" class="Y"></path><path d="M451 349h1c0-6 0-12 2-19h0c0 6-4 16 0 21 3 4 14 9 14 15-1 1-2 3-4 3-5 3-10 2-15 0-3-1-6-2-8-4 0-1 0-1 1-2 5 3 9 5 15 5-1 0-1-1-2-1l-2-1c-2 0-3-1-4-2l-2-2c-1-2-2-3-4-5v1c-1-1-1-1-1-2l-1-3v-1c0-2 0-3-1-4 2 2 4 3 5 5 1 1 2 1 2 2l1-1v-1c1-1 1-1 1-3l2-1z" class="J"></path><path d="M448 354c5 2 8 5 12 8-1 0-2 0-3-1 0-1-2-2-4-3v1h-2v-1c-2 0-2-2-4-3l1-1z" class="L"></path><defs><linearGradient id="Ad" x1="458.45" y1="363.438" x2="450.522" y2="363.691" xlink:href="#B"><stop offset="0" stop-color="#898175"></stop><stop offset="1" stop-color="#999788"></stop></linearGradient></defs><path fill="url(#Ad)" d="M440 348c2 2 4 3 5 5 1 1 2 1 2 2 2 1 2 3 4 3v1h2v-1c2 1 4 2 4 3 1 1 2 1 3 1l3 3c-1 1-1 2-2 2-1 1-3 1-4 1s-1-1-2-1l-2-1c-2 0-3-1-4-2l-2-2c-1-2-2-3-4-5v1c-1-1-1-1-1-2l-1-3v-1c0-2 0-3-1-4z"></path><path d="M449 364c1 0 1 0 2 1 1 0 3 0 4 1h5 0c-1-1-2-3-3-4v-1c1 1 2 1 3 1l3 3c-1 1-1 2-2 2-1 1-3 1-4 1s-1-1-2-1l-2-1c-2 0-3-1-4-2z" class="R"></path><path d="M440 348c2 2 4 3 5 5v1c1 2 5 4 5 6h-1-1v1l-1 1c-1-2-2-3-4-5v1c-1-1-1-1-1-2l-1-3v-1c0-2 0-3-1-4z" class="S"></path><path d="M456 310c4 0 7 1 11 3h0c2 1 3 2 3 4v1c1 4 1 9-2 12-1 2-2 3-4 4-3-1-6-3-8-6h0c-2-4-5-4-9-5-1-1-2-1-2-2 0-2 1-4 1-5 3-4 6-5 10-6z" class="E"></path><defs><linearGradient id="Ae" x1="462.181" y1="339.958" x2="441.04" y2="312.682" xlink:href="#B"><stop offset="0" stop-color="#d4d1cb"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Ae)" d="M455 304c3-1 6-1 9 0-2 0-2 0-3 2h-3c-1 1-1 1-2 1v1l5 1-5 1c-4 1-7 2-10 6 0 1-1 3-1 5 0 1 1 1 2 2 0 0-1 1-1 2l-1 1v1l-2 6v1c-1 3 0 6 1 9 2 2 4 5 7 6l-2 1c0 2 0 2-1 3v1l-1 1c0-1-1-1-2-2-1-2-3-3-5-5h0c0-3-1-4-2-6h-2c-1-5-1-10-1-15 1-6 4-13 8-17 3-3 8-5 12-6z"></path><path d="M443 310c3-3 8-5 12-6l-1 2c-1 1-1 1-2 0-1 1-1 2-3 2-2 1-3 2-6 2z" class="V"></path><path d="M448 353c-2-4-5-8-6-13s0-10 3-13l-2 6v1c-1 3 0 6 1 9 2 2 4 5 7 6l-2 1c0 2 0 2-1 3z" class="B"></path><path d="M560 310c5-1 9 1 12 4 2 1 3 3 4 6 0 0 0 1-1 2 0 2-7 2-8 3-1 0-3 4-3 5l-3 3c-1 1-2 2-3 2s-3 0-4-1c-3-2-6-7-6-10-1-3-1-6 1-9s7-5 11-5z" class="E"></path><path d="M640 237c-6-3-11-8-19-8l-9-3c-8-2-17-2-25-2-4 1-7 1-10 1-2 1-10 4-12 3 20-9 43-10 63-3l18 8c4 3 11 7 14 11 2 0 3 2 4 3 2 1 4 3 6 4l-1 8c-5-6-14-15-22-18h0s-1 0-2-1l-2-2c-2 0-2 0-3-1z" class="M"></path><path d="M640 237v-1c-2-2-8-3-9-6 1 0 2 0 3 1l15 6c3 2 8 6 11 7 2 0 3 2 4 3 2 1 4 3 6 4l-1 8c-5-6-14-15-22-18h0s-1 0-2-1l-2-2c-2 0-2 0-3-1z" class="G"></path><path d="M584 341c1-6 2-12 1-18-1-2-4-8-3-9 3 6 4 13 5 20 1 2 1 3 0 5l-1 7c1 0 2 1 3 2 1 0 1-1 1-1l1-2c0-1 0-2 1-3h0l1-4v-1c0-1 1-2 1-3 1-1 0-1 0-2h1l1-2 1-9c0 3 1 6 1 9 0 7-3 16-6 22-5 9-14 15-23 18-5 1-11 1-15-2-1-1-2-2-3-4 0-1 1-2 2-3 5-5 13-7 14-15 0-5-1-11-1-16 2 5 4 12 2 18 0 1-1 1-1 3 5-3 7-6 9-11 1-3 1-7 0-11h0c3 5 2 10 1 15v1c0 1 0 1-1 3 1 1 2 1 3 2 0-1 0-2 1-3h0c1-3 2-4 4-6z" class="B"></path><path d="M565 366l-2 1c-2 0-5 0-6-1l1-2c2 1 4 1 6 1l1 1z" class="R"></path><defs><linearGradient id="Af" x1="576.203" y1="366.814" x2="582.408" y2="345.596" xlink:href="#B"><stop offset="0" stop-color="#9c9c9d"></stop><stop offset="1" stop-color="#d3d1ca"></stop></linearGradient></defs><path fill="url(#Af)" d="M587 334c1 2 1 3 0 5l-1 7c1 0 2 1 3 2 1 0 1-1 1-1l1-2c0-1 0-2 1-3h0l1-4v-1c0-1 1-2 1-3 1-1 0-1 0-2h1c-2 11-6 23-17 31-4 3-9 4-15 5l6-3c8-5 15-14 17-23l1-8z"></path><defs><linearGradient id="Ag" x1="564.832" y1="366.417" x2="573.549" y2="350.795" xlink:href="#B"><stop offset="0" stop-color="#948b7a"></stop><stop offset="1" stop-color="#c3bbb0"></stop></linearGradient></defs><path fill="url(#Ag)" d="M579 350c0-1 0-2 1-3h0c1-3 2-4 4-6-2 10-7 18-16 24l-3 1-1-1c-2 0-4 0-6-1 2-2 9-10 12-10v1h0l1-2c3-3 4-6 6-9v1c0 1 0 1-1 3 1 1 2 1 3 2z"></path><path d="M577 344v1c0 1 0 1-1 3 1 1 2 1 3 2-1 2-2 3-3 4s-2 1-3 1l-1-1-1-1c3-3 4-6 6-9z" class="M"></path><path d="M505 330c0-1 0-2-1-3h1c1 1 3 2 4 3l1 2c1 2 2 5 2 7 1 6-1 12 0 18 1 10 3 22 10 29 6 6 16 9 24 8 16 0 32-7 44-16 4-3 7-7 10-10 0 2-3 4-4 5-4 5-9 10-15 14l-1 2v1l1 1h1l-1 1c-7 4-13 6-20 8l-12 3c-1 1-2 1-4 1 1 0 2 1 3 1s4-1 6-1l1-1h3c-2 1-7 2-10 3h-1c-2-1-3-1-4-1-3 0-5 0-7-1-3 0-5-1-8-2h0c-1 0-1 0-2-1h0c-2-1-2-1-3-2-2-1-3-2-4-4l-1-1c-2-2-4-4-4-7l-1-1c0-1-1-2 0-3h0v-4l-3-9c-1 0-1 2-1 3-4 10-6 17-15 22l-9 2c-2-2-7-1-10-1l4-2h-4c5 0 10 0 15-2a25.03 25.03 0 0 0 12-13c5-12 8-37 3-49z" class="E"></path><path d="M551 400h0-4-4c-1 0-4 0-5-1 11 1 22-3 32-6-2 4-3 4-8 5-3 1-7 2-11 2z" class="U"></path><path d="M581 387l-1 2v1l1 1h1l-1 1c-7 4-13 6-20 8l-12 3c-1 1-2 1-4 1h-1c-2-1-4 0-6-1 1-1 1-1 2-1h1c2 0 4 0 6-1 1 0 3 0 4-1 4 0 8-1 11-2 5-1 6-1 8-5l11-6z" class="N"></path><path d="M513 379c4 10 9 17 19 20l9 3h-1c-1 0-1 0-2 1 2 1 4 0 6 1h1c1 0 2 1 3 1s4-1 6-1l1-1h3c-2 1-7 2-10 3h-1c-2-1-3-1-4-1-3 0-5 0-7-1-3 0-5-1-8-2h0c-1 0-1 0-2-1h0c-2-1-2-1-3-2-2-1-3-2-4-4l-1-1c-2-2-4-4-4-7l-1-1c0-1-1-2 0-3h0v-4z" class="Y"></path><path d="M681 201h98c5 0 13-1 17 2v2c-3 4-9 4-13 6-2 0-3 1-5 2-8 6-14 17-19 25l-12 28-17 43-44 112-125 334-26 69-13 30c-4 12-7 24-9 37l-3 18c-1 5 0 11-2 16 0 1-1 1-2 2-1-1-2 0-3-2-2-2-3-17-3-21l-3-15c-5-22-13-42-21-63l-96-259-86-219-27-68-11-27c-3-8-6-15-10-22-3-6-7-12-13-17-2-1-4-3-6-4-4-1-13-1-14-7 1-1 2-2 4-2 5-1 12 0 18 0h65 20c4 0 8 0 11 1 1 1 1 1 1 3s-1 4-3 6c-4 3-16 2-22 2-14 0-27 0-41 1 14 1 29 1 43 1 3 0 12-1 14 2 2 1 2 2 2 4-1 4-2 7-2 11-2 15 1 30 5 45 6 18 13 35 20 52l18 48 68 176 53 138 12 31c2 6 4 13 7 18l2 2c2 0 3 0 4-2 3-2 4-6 5-10l4-12 15-41 95-250 34-90 19-51c5-16 9-34 7-51 0-3-1-7-2-11 0-1-2-3-2-4s1-1 1-2c1-1 2-2 5-3h15c14 0 28 0 42-1l-43-1c-6 0-13 0-19-1-3 0-4-1-6-3-1-1-2-3-2-5v-1c0-1 1-2 1-2z" class="V"></path><path d="M585 625h2c0 2 0 2-1 3l-1 1h-1c0-2 0-2 1-4z" class="B"></path><path d="M472 743c0-1-1-2-1-3s0-1 1-2h2v1c-1 1-1 2-2 4z" class="J"></path><path d="M308 316h2c1 1 1 2 1 4v1h-1c0-1-1-1-1-1-1-1-1-2-1-4z" class="B"></path><path d="M553 710h1l1 1c0 2 0 2-1 4h-1l-1-1c0-2 0-2 1-4zm97-258h1 1c0 2 0 3-1 4h-2v-1c0-1 0-2 1-3zm-9 22h1l1 1c0 2 0 3-2 4h-1-1c1-2 1-3 2-5zm-297-71h2c1 2 1 3 1 5-1 0-1 0-2-1-1-2-1-2-1-4z" class="C"></path><path d="M467 725h1c1 0 1 1 2 1 1 2 1 2 0 4h-1c-1 0-1-1-2-1v-4z" class="J"></path><path d="M617 537h2c1 0 1 0 1 2 0 1-1 2-2 3-1 0-1 0-2-1 0-2 1-2 1-4z" class="B"></path><path d="M438 647l1 1c1 1 2 1 2 2s0 2-1 2h-1c-1-1-2-2-2-4l1-1z" class="J"></path><path d="M597 591h1c1 1 1 1 1 2s0 2-1 3c-2 0-2 0-3-1 0-2 1-3 2-4zm100-262h0c1 0 1 1 2 2 0 1-1 2-2 3l-1 1-1-1h0c0-2 0-3 2-5z" class="B"></path><path d="M406 564h1c1 1 2 1 2 3 1 1 0 1 0 3h-1c-1-1-2-1-2-2-1-2 0-2 0-4zm4 10h1l1 1c1 2 1 3 1 5h-2c0-1-1-1-1-2-1-1 0-2 0-4z" class="C"></path><path d="M593 602h2 0c0 2-1 4-2 6h-1l-1-1c1-2 1-4 2-5z" class="E"></path><path d="M701 317c1 0 1 1 2 1v2c0 1-1 2-2 3-1 0-1 0-2-1v-1c1-2 1-3 2-4z" class="H"></path><path d="M420 598h1c1 1 1 2 1 3 1 1 1 1 0 3h-2c-1-1-1-2-1-4 0-1 0-1 1-2z" class="J"></path><path d="M580 635h1c1 0 1 1 2 2 0 1-1 2-2 4h-2v-1c-1-2 0-3 1-5z" class="C"></path><path d="M613 549h1l2 2-2 4c-2-1-2-1-3-2v-1c0-1 1-2 2-3z" class="D"></path><path d="M570 662h2l1 1c0 2-1 3-2 5h-1-1c0-3 0-4 1-6zM373 475h0c1 1 2 2 2 3 1 2 1 3 0 5 0-1-1-1-1-1-1-2-2-3-2-4 0-2 0-2 1-3z" class="C"></path><path d="M320 345c1 0 1-1 2 0 1 0 2 2 2 3 1 2 0 2 0 3h-1c-1 0-1 0-2-1-1-2-1-3-1-5z" class="J"></path><path d="M393 530h1c1 1 2 1 3 3 0 1 0 2-1 3h-1c-1 0-1-1-2-1 0-2-1-4 0-5z" class="C"></path><path d="M361 447h1c2 0 2 1 2 2 1 2 1 3 1 5h-1c-1-1-2-1-2-2-1-2-1-3-1-5z" class="E"></path><path d="M348 413h1c1 1 2 2 2 4 1 2 1 3 0 4-2-1-3-2-3-4-1-2-1-2 0-4z" class="J"></path><path d="M609 561h2v1c0 2-1 4-3 6h-1c0-1-1-1-1-2 0-2 1-4 3-5zM326 357h1c1 1 2 3 2 4 1 2 0 2 0 4h-1l-1-1c-1-2-2-5-1-7z" class="D"></path><path d="M655 437h2v1c0 3-1 5-2 7-1 0-1 0-2-1 0-3 1-5 2-7zm23-58h2v2c0 2-1 3-2 4l-1 1c-1-1-1-2-2-4 1-2 1-2 3-3z" class="C"></path><path d="M543 738c1 0 1 0 2 1v2c0 2-1 3-2 4-1-1-2-1-3-2v-2l3-3zm117-312h2v2c0 2-1 3-2 5h-2c0-1-1-2-1-3 0-2 1-3 3-4z" class="D"></path><path d="M355 430h1l1 1c1 3 2 5 2 8l-2-1c-1-1-1-2-2-3-1-2-1-4 0-5z" class="B"></path><path d="M378 490h1l1 2c2 1 2 3 2 5-1 1-1 1-2 1l-1-1c-2-2-2-5-1-7z" class="C"></path><path d="M383 504h1c1 1 2 1 2 2 1 2 1 4 1 6h-1c-2-1-2-2-3-3 0-2-1-3 0-5z" class="B"></path><path d="M315 330h1c2 3 3 6 3 9h-2c-1-1-1-1-1-2-1-2-2-5-1-7z" class="J"></path><path d="M414 584c1 0 1 1 2 1 1 3 3 7 2 9h-1c-1-2-2-3-3-5 0-2-1-3 0-5z" class="C"></path><path d="M339 391c1 0 2 1 2 1 1 1 2 2 2 4 0 1 0 1-1 2h-2c-1-1-2-3-2-4 0-2 1-2 1-3z" class="D"></path><path d="M388 517h1c1 0 2 1 3 3s0 3 0 5c-2 0-3-1-3-3-1-2-1-3-1-5z" class="C"></path><path d="M549 722l1 1c1 0 1 0 1 1 0 2-2 6-3 8h-2v-3c0-2 1-5 3-7z" class="D"></path><path d="M577 646h1 0c1 3-2 8-3 11h-1v-1c0-3 1-7 3-10z" class="C"></path><path d="M589 611c2-1 2-1 3 0 0 3-2 6-3 8l-1 1-1-1c0-3 1-6 2-8z" class="J"></path><path d="M686 357h2v2c1 2 0 3-2 5h-1l-2-2c0-2 1-3 3-5z" class="E"></path><path d="M332 374c1 0 1 0 2 1 1 2 2 5 2 8h-1c-1-1-2-3-3-4-1-2-1-3 0-5z" class="D"></path><path d="M665 412h1l1 1c0 3-2 6-3 8l-1 1-1-1c0-3 1-6 3-9z" class="B"></path><path d="M670 398h1l1 1c1 3-1 5-3 7 0 1 0 0-1 1 0-1 0-2-1-4 0-2 1-3 3-5z" class="D"></path><path d="M629 504h3c0 1 0 1 1 2 0 2-1 4-3 5h-1l-2-2c0-2 1-3 2-5z" class="B"></path><path d="M433 634h1s0 1 1 1c1 2 3 8 2 10l-3-3c-1-2-2-5-1-8z" class="C"></path><path d="M366 461h1c2 1 3 3 4 6 0 1 0 1-1 2h-2c-1-1-1-2-2-4s0-2 0-4z" class="J"></path><path d="M603 577h1c1 3-1 7-3 10h-1l-1-1c0-2 2-7 4-9z" class="C"></path><path d="M691 342h2l1 1c-1 3-2 7-4 9h-2v-1c0-3 1-6 3-9z" class="B"></path><path d="M647 459h1l1 1c-1 3-3 7-4 11l-1 1v-2c-1-3 1-9 3-11z" class="C"></path><path d="M533 765c1-3 1-8 4-11h1l1 1c0 4-3 8-5 12l-1-2z" class="D"></path><path d="M625 520h1v2c0 1-2 4-3 6h-1c1 1 0 1 1 2h0c0 2-1 2-2 4l-2-1v-1c1-4 4-9 6-12z" class="H"></path><path d="M636 487h2c1 3-3 10-4 13h-1-1c-1-3 3-10 4-13z" class="B"></path><path d="M397 541h2l2 2c0 2 1 3 0 5h0c2 3 4 7 4 10h-1c-3-1-3-5-4-8-1-1-2-3-2-4-1-2-1-3-1-5z" class="C"></path><path d="M423 608h1c1 0 1 1 1 1 1 1 6 17 6 18h-1c-3-3-4-9-5-12-1 0-2-1-3-2 0-2 0-3 1-5z" class="E"></path><path d="M443 660c1 0 1 0 1 1 2 1 4 9 5 11 1 4 4 8 4 12 1 1 0 1 0 2-1 0-1-1-2-1-1-3-1-7-3-10-2-5-5-8-6-14l1-1z" class="C"></path><path d="M456 696c2 0 2 0 3 1 2 2 1 5 2 7 1 5 4 10 4 15v1c-3-5-4-13-7-17-1-1-2-1-3-3 0-2 0-2 1-4z" class="D"></path><path d="M565 677c1 0 1 0 2 1 0 2-2 7-3 9-2 6-4 12-6 17l-1 1h-1c0-5 4-12 5-17 1-3 2-8 4-11zm-91 62h1c1 2 1 5 1 7 1 4 3 7 4 11l29 75 24-67 1 2-25 72-24-66-7-18c-1-4-3-8-5-11l-1-1c1-2 1-3 2-4z" class="C"></path></svg>