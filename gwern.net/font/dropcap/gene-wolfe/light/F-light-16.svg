<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="142 60 799 884"><!--oldViewBox="0 0 1024 1024"--><style>.B{fill:#2d2f31}.C{fill:#030303}.D{fill:#3c3e41}.E{fill:#d9d8d4}.F{fill:#cbc9c3}.G{fill:#26282a}.H{fill:#424648}.I{fill:#1c1d1f}.J{fill:#333638}.K{fill:#212224}.L{fill:#e5e4e0}.M{fill:#17181a}.N{fill:#d1d0c9}.O{fill:#a9a7a0}.P{fill:#0d0d0e}.Q{fill:#3e4043}.R{fill:#505356}.S{fill:#d6d4ce}.T{fill:#eeedeb}.U{fill:#b2b1ab}.V{fill:#8f8d89}.W{fill:#4b4d50}.X{fill:#c3c2bb}.Y{fill:#484b4f}.Z{fill:#131414}.a{fill:#807e7b}.b{fill:#6e6c6a}.c{fill:#b9b6b0}.d{fill:#fefefd}.e{fill:#5f6061}.f{fill:#bebcb5}.g{fill:#9f9c96}</style><path d="M662 520l1 1v10h0c-1-1-1-9-1-11z" class="F"></path><path d="M636 678c2 0 6 0 8 1v2c-1-2-3-1-4-1h-1l-3-2z" class="e"></path><path d="M198 127c2-1 5 0 7 0v1c-2 1-4 1-6 1l-1-2z" class="B"></path><path d="M326 443c2 2 2 2 3 5l-7-3 2-1h2v-1z" class="G"></path><path d="M363 667v4 1c-1 1-2 1-2 2h-1c-1 0-1 0-1-1 1-3 2-4 4-6z" class="R"></path><path d="M189 142c3-1 7 0 9 1v1c-1 0-3 1-4 0-2 0-3-1-5-2z" class="C"></path><path d="M662 486h1c0 7 1 14 0 20-1-2-1-4-1-6v-14z" class="N"></path><path d="M167 408l1 1v1c1 1 2 2 4 2h0 1l1 2c-2 0-4 0-6-2-1 0-3-2-3-3h2v-1z" class="O"></path><path d="M198 483l1-1c2-1 6-1 8-1l-2 3v1c-3-2-4-2-7-2z" class="E"></path><path d="M354 686h1l1 3 1 1h1l1 4c0 1 0 0 1 1l-2 3h0v-1c-2-3-3-7-4-11z" class="D"></path><path d="M387 496c2 0 2 0 4 1 1 1 1 1 1 3 0 1-2 4-3 5 0-2 0-3 1-5 0-1-1-1-1-2s-1-1-2-2z" class="W"></path><path d="M385 575c-1-1-1-2-1-4 1 0 3 1 5 1l1 3-1 1h0c-2-1-2-1-4-1z" class="B"></path><path d="M829 117l4-1-4 5-6-3 6-1z" class="K"></path><path d="M168 174l9-6c-1 1-2 2-2 3-1 0-1 1-1 1 0 2-1 3-2 4l-1-1h-2c-1 0-1 0-1-1z" class="S"></path><path d="M469 253h2c0 1 1 1 1 2 1 2 1 3 0 5h-2c-1-1-1-2-2-3 0-2 1-3 1-4zm-14-13c2 0 4 0 6 1 1 0 1 1 1 2h-1c-1 1-3 1-4 0-2 0-2-1-3-2l1-1z" class="O"></path><path d="M532 123l3-1h1c0 1 1 1 2 1 2 1 5 2 7 5v1c-1 0-3-1-4-2v-1h-2c-1-1-3-2-5-2-1 0-1-1-2-1z" class="e"></path><path d="M577 884h4c-1 2-1 4-3 5-2 0-4-1-5-3 1 0 2-1 4-2z" class="H"></path><path d="M760 510v4l1 9c0 2-1 3 0 4l-1 1v2 2h1v3l-1 1v-26z" class="a"></path><path d="M537 147l2-2h1 7v1l-2 3-8-2z" class="D"></path><path d="M168 409l7 2 6 3h-7l-1-2h-1 0c-2 0-3-1-4-2v-1z" class="V"></path><path d="M167 408c1-2 1-3 2-4 2 1 3 3 4 4s2 2 4 3h-2l-7-2-1-1z" class="D"></path><path d="M192 320h6c2 2 2 4 2 6h-1v1 1c-1-1-1-4-3-5-1-1-1-1-3-1v-1l-1-1z" class="E"></path><path d="M643 587c1 1 1 1 1 2l1 2h1c0 4 2 7 3 11-3-1-2-3-4-5v-1l-2-3v-6z" class="b"></path><path d="M236 104c1 0 1 0 2 1l-1 1h0c-1-1-2-1-3 0h-2c-1 0-2 0-4 1h-2-2l-2-2c2-1 5 0 7 0 1 0 1-1 1-1h2 1 3z" class="E"></path><path d="M474 195l13 2c-1 1-2 1-3 2h0c0 1 0 1-1 2h-2c-1-1-1-3-2-3-2-1-3-1-5-3z" class="Q"></path><path d="M354 686c-1-4-1-9-1-12h1l1 1c0 2 0 4 1 6v3l1 3 1 3h-1l-1-1-1-3h-1z" class="e"></path><path d="M634 519c2-1 6-1 8 0 1 0 1 0 1 1v2 1c-1 2 0 5-1 7h0v-10c-2-2-5-1-7-1 0 3 1 9-1 11l-1-1c0-3 0-8 1-10z" class="L"></path><path d="M576 879c3 0 5 0 7 1l-2 4h-4-1c-1-1-1-1 0-2v-3z" class="R"></path><path d="M480 145l1-1c2 1 2 1 4 1 3-1 4-1 6 1l-10 3-1-4z" class="H"></path><path d="M188 429c1-1 2-1 3-1h2v1c2 1 3 0 4 2v2c1 0 2 0 3 1-2 1-6 3-8 3h0c0-2 1-3 3-3h0l1-1c-3-2-4-3-8-4z" class="T"></path><path d="M235 416l2-1 2-2h-1v1h-3c-2 1-7 0-9 0h-1 2c2 0 3 0 5-1h1 1l1-1c1 0 2 0 3-1s2 0 3 0l2 1v1c-2 0-4 1-5 2l-3 1z" class="L"></path><path d="M455 159c4-3 7-3 11-3l1 1v2c-2 0-2 1-4 2v-1l2-2c-2 0-4 0-6 1s-3 2-5 2c1-1 1-1 1-2z" class="H"></path><path d="M870 410l8-6c1 1 2 4 2 5l-7 2c-2 0-2 0-3-1zM464 191c5 0 6 3 10 4 2 2 3 2 5 3 1 0 1 2 2 3-3 0-6-2-9-4 0-1-1-2-3-3 0 0-1 0-1-1l-4-2z" class="B"></path><path d="M439 879l1-1h3l1 1c0 3 2 4 2 6-1 1-2 1-3 1l-4-7z" class="e"></path><path d="M645 575c2-2 6-3 9-3-1 0-2 1-3 2h0c0 1-1 2-2 3l-7 4c0-2 2-4 3-6h0z" class="Y"></path><path d="M489 95v-1c1-1 4 0 6 1 4 1 8 2 11 5-1 0-2 1-3 1-1-1-2-2-3-2-1-1-3-1-4-2-1 0-2 0-3-1h-2v1h-1c0-1-1-1-1-2z" class="R"></path><path d="M472 136c1 1 2 2 3 4h-7c-1 0-2-1-3-1h-7l5-1v-1s6-1 9-1z" class="H"></path><path d="M511 552v-24c0-2 0-7 1-8 1 2 0 7 0 10l1 28c-1-2-1-4-2-6z" class="S"></path><path d="M620 673c5 3 10 5 16 5l3 2h1c1 0 3-1 4 1l-1 1c-1-1-8-1-10-2h-2c-5-2-9-3-11-7z" class="R"></path><path d="M534 111c7-3 16-3 23 1l1 1h-3c-5-1-10-3-15-1h-4l-2-1z" class="D"></path><path d="M636 619c9 2 17 5 25 9-2 1-7-1-10-1l-1-1c-1-1-2-1-3-2-2 0-2 0-3-1 1 1 2 0 3 0-2 0-3-1-5-1-1-1-2-1-3-1s-2 0-3-2z" class="I"></path><path d="M615 191c2-1 4-1 6-3 3-1 4-6 8-6 2 0 4 0 6 2-3 0-4 1-6 2-2 0-3 1-5 2s-3 2-6 3c-1 1-1 1-3 1v-1z" class="e"></path><path d="M398 448c7-3 15-3 21 0 2 1 5 2 6 3v1 1c-2 0-4-2-5-3-6-2-12-2-17-1-2 0-3 0-5-1z" class="b"></path><path d="M371 632c8-6 18-9 27-12 0 2-1 3-3 3-4 2-8 3-11 5l-1 1c-3 1-9 3-12 3z" class="G"></path><path d="M206 113c2 0 3 1 5 1h1c-1 1-1 0-1 1 1 1 1 1 2 1s1 0 1 1c1 0 1-1 2 0h1 1 3s1 0 2 1h-3c-3 1-5 3-7 2h-1c-2-1-3-2-4-3h0-2c-2-1-4 0-6 0h-1-2c2-2 7 0 9-2v-2z" class="L"></path><path d="M213 120l-5-4 12 2c-3 1-5 3-7 2z" class="B"></path><path d="M681 318c1 2 1 3 1 5 0 1 0 2 1 3-3 7-6 14-11 20h0l-1-1c1 0 2-1 2-2l-2 1h0c1-1 2-2 3-4h0c1-2 3-5 4-7 1-4 3-8 3-12v-3z" class="Y"></path><path d="M213 427v-1c-1-1-1-6 0-7v-3h1v10c1-2 3-3 4-5l-2-3v-4l-2-3c-1-1-1-2-1-3h0l2 3c1 1 1 2 2 3 1 0 0-2 0-3v1c1 2 0 0 1 1l1 2h0 3c2 2 6 2 9 3h0c-3 0-8 1-11-1l-1-1v1h0v4h0c-1 2-3 4-5 6h-1z" class="T"></path><path d="M394 565h4c4-1 6-2 10-2l1 2c-1 2-3 3-5 4-3 1-8 0-10-1l-3-2 3-1z" class="C"></path><path d="M651 574h4c-1 6-8 11-9 17h-1l-1-2c0-1 0-1-1-2 1-3 5-7 7-9l-1-1c1-1 2-2 2-3z" class="Q"></path><path d="M424 887c1-4 9-8 12-11 1-1 1-1 2-1l1 2c1 0 1 0 2-1l2 2h-3l-1 1c-4 3-11 8-15 8z" class="J"></path><path d="M219 147c2-3 5-7 8-8-4 4-8 9-8 15 0 3 1 6 3 9 1 0 3-1 4-1l1 1-8 2h0c-3-6-3-12 0-18z" class="C"></path><path d="M196 131h3 0c1-1 2 0 2 1h0-2c-2 1-3 1-5 1l-1-1h-2c-4-2-6-4-8-7-1-1-1-1-1-2h-1c1-1 1-2 2-3l2 3 5 5c2 1 4 1 6 3z" class="E"></path><path d="M185 123l5 5c2 1 4 1 6 3h-4v-1c-1 0-2 0-2-1-3-1-5-3-5-6z" class="L"></path><path d="M206 113c-1-2-3-3-5-5-3-4-4-7-6-11l1-1h0 0c1 1 1 2 2 3s3 3 4 5c2 3 9 8 11 9v1h-1-1c-2 0-3-1-5-1z" class="N"></path><path d="M822 541l5-7h1 0c1 2 1 2 2 3 2 0 4 0 6-1 1-2 2-3 2-5s0-3-1-5h1c1 3 2 6 1 9 0 2-2 4-4 5s-4 1-6 0c-2 0-2-1-4-2 0 1-1 2-2 3h-1z" class="Z"></path><path d="M840 301c2 0 7 0 8 1h1c-1 1-1 1-2 1-3 2-6 4-8 7-2-1-2 0-3 0h-1c-1-2 1-4 0-6 1-1 3-2 5-3z" class="O"></path><path d="M840 305h0c-1 1-2 1-3 2h-1c0-2 0-2 1-3 1 0 2 1 3 1z" class="U"></path><path d="M840 301c2 0 7 0 8 1h1c-1 1-1 1-2 1-3 0-5 0-7 2-1 0-2-1-3-1l3-3zm-328 99h0l1 3-1 103h0l-1-3c1-9 1-18 1-26v-53c0-8-1-16 0-24z" class="F"></path><path d="M759 249h1l1 2c1 14 0 29 0 44v15 8l-1-2-2-2 1-1 1 1h0v-43c0-6 1-13 0-19 0-1 0-2-1-3z" class="N"></path><path d="M209 524l-1 6c-1 2 0 4 2 5 2 2 4 2 7 2 1-1 2-2 2-3h1c1 2 2 2 1 4 0 1-1 2-3 2-2 1-5 1-8 0-2-1-3-3-4-5-1-4 1-7 3-11z" class="P"></path><path d="M829 117l5-3c0-1 2-3 3-4s1 0 1-1h1v-2h-1 0v-1c-1 0-2 0-3-1 1 0 1 0 2-1 1 0 2 0 4-1h0c2-1 4-4 6-6 0-1 0-1 1-2l1 1c-1 3-3 7-5 10-3 4-7 7-11 10l-4 1z" class="N"></path><path d="M749 520c1 0 0 0 1 1v45 23c0 4 1 8-1 11h0c-1-8 0-16 0-24v-56z" class="S"></path><path d="M611 148c4 8 6 19 5 28l-1-1c-1-2-1-3-1-5-2-7-3-12-4-19h0l1-3z" class="R"></path><path d="M831 500c3 2 4 5 6 8 0 2 0 7-1 9v2c0 1 0 1-1 2l-2 3h0c0-1 0-2 1-3h0v-2c1-1 0-1 1-2v-1h0-1l-1 1c-1 1-1 2-1 3h0c-1 1 0 1-1 1v1l-1 1-1 1c-1 2-2 3-3 4 2-4 4-7 5-11 3-6 2-11 0-17z" class="E"></path><defs><linearGradient id="A" x1="817.198" y1="156.078" x2="825.552" y2="147.188" xlink:href="#B"><stop offset="0" stop-color="#0d0d0e"></stop><stop offset="1" stop-color="#312f2e"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M822 163c2-5 3-10 1-15-3-5-9-9-13-13 6 3 13 7 16 13 0 1 0 2 1 3v1c1 5-1 9-3 13h0v-1h0l-1-1h-1z"></path><path d="M387 758c-4-2-10-1-12-4-2-2-2-4-2-7l1 1c1 2 2 3 3 4 5 4 15 0 20-1l-2 2v1s0 1-1 1v1l-5 1-2 1z" class="W"></path><path d="M389 757l-1-1v-1l7-2v1s0 1-1 1v1l-5 1z" class="B"></path><path d="M300 505c1-1 1-2 3-2h1 1c1 0 3-1 4 0 1 0 1 1 2 0 1 0 2 0 3 1 0 0 1 0 1-1h0-1c-2-1-3 0-4-1h0-14c2-1 5-1 7-1 5 0 10 0 15 2 5 4 9 10 10 16l-2 2v-1-2c-2-5-6-10-11-12-5-3-10-3-15-1z" class="L"></path><path d="M203 308c-2-2-7-5-8-6 1-1 1-1 2-1 4-1 8 0 11 3h1 1c0 1 1 3 1 4l-1-1-1 1v2c-1 1-2 2-3 2l-3-4z" class="f"></path><path d="M203 308c1-1 2-1 3 0h2v-2h1l1 1-1 1v2c-1 1-2 2-3 2l-3-4z" class="O"></path><path d="M238 415c-2 3-6 4-8 5-1 0-2 1-2 1-3 2-6 1-8 5l-1 1c-1 0-2 1-3 1h-2v1l-1 1h0c0-2 1-2 1-3 2-2 4-4 5-6h0v-4h0v-1l1 1c3 2 8 1 11 1h0c2-1 3-1 4-2l3-1z" class="g"></path><path d="M607 464c0 3 0 7 2 10h1v-1c1-1 2-2 2-4 2 1 2 2 4 3v-1l1-1h0c1 0 1 2 2 2h5 1c1 1 1 2 1 3h-22c-3 1-7 1-10 0v-1 1c5-1 9 0 14-1 0-1-1-2-2-3 0-2 0-4 1-6v-1z" class="I"></path><path d="M892 413h1c0-2 0-4-1-5 0-1-1-2-1-3l1-1c1 2 2 4 3 5 2 2 2 4 3 6v1c1 4 0 10-3 14h0l-1-4c0-1 0-2-2-3 0-1 1-2 2-3 1-2 0-4 0-6-1-1-1-1-2-1z" class="E"></path><path d="M247 150c3-1 6-3 8-5 1-1 2-2 4-2h0c2 4 6 8 9 10-1 1-1 0-2 1h-1 0c-2-1-3-2-5-2-5-2-8-2-12 0l-1-2z" class="C"></path><path d="M755 119h2c-12 10-20 18-26 32v1h0l2-10c4-10 13-18 22-23z" class="B"></path><path d="M291 455l2-1h0-5l-1-1c1 0 2 0 3-1 2 0 8 0 10 1 1 0 3-1 5 0 1 0 1 0 3 1h2c1 0 0 0 2 1 1 0 2 0 3 1 1 0 1 0 2 1 2 0 4 1 6 2h0 0c-1 0-1 0-1 1-2 0-2 0-2-1h-1l2 2h1v1c-4-2-8-4-12-5l-6-1c-4-1-8-1-13-1zm2 32c3 0 7-1 10 0v1h1c-1 0-2 1-3 1h-2v-1c-2 0-3 0-5 1h-1c-3 0-6 3-7 4-3 1-4 2-6 4 0 2 1 3 2 4h1c0 1 0 1 1 2h1 2l1-1c0-2 2-3 3-4 1-2 0-3 2-4h1l1-1h1 1c1-1 3-1 5-1 0 1 0 1 1 2h0 0c-4 1-10 3-13 6v1c-1 1 0 1-1 1-1 1-1 2-2 2-3 0-5-1-7-4-1-1-1-2 0-4 2-3 7-6 11-8 1 0 1 0 2-1h0z" class="L"></path><defs><linearGradient id="C" x1="214.551" y1="304.452" x2="203.434" y2="291.143" xlink:href="#B"><stop offset="0" stop-color="#8a8983"></stop><stop offset="1" stop-color="#b6b1ab"></stop></linearGradient></defs><path fill="url(#C)" d="M208 287c2 2 4 5 5 8v2 10l-1 2-1-1c0-1-1-3-1-4-2-5-3-11-6-16v-2l3 2 1-1z"></path><path d="M511 679c1-10 0-21 0-32l1-49-1-12c0-2 0-4 1-5h0v-3l1 121c-2-4-1-8-2-13v-7z" class="N"></path><path d="M614 758c2 4 6 8 8 12s4 9 5 14v1l-1-1v-1c-5-4-6-9-9-14-2-2-4-5-4-7-1-2 0-3 1-4z" class="H"></path><path d="M383 629l6-2v3h4l-1 1h-3 0 3l1 2c-4 0-9-1-12 2h-1c-1 0-3 0-5 1l-4-1c-2 0-2 0-3-1 1-1 2-2 3-2 3 0 9-2 12-3z" class="e"></path><path d="M389 631h3l1 2c-4 0-9-1-12 2h-1c-1 0-3 0-5 1l-4-1v-1s1 0 2-1h1c5-1 10-1 15-2z" class="D"></path><defs><linearGradient id="D" x1="556.866" y1="861.313" x2="575.103" y2="884.016" xlink:href="#B"><stop offset="0" stop-color="#101114"></stop><stop offset="1" stop-color="#525455"></stop></linearGradient></defs><path fill="url(#D)" d="M556 859l-1-1 1-1h1c5 5 8 11 12 17 1 3 2 6 5 8l2 2h1c-2 1-3 2-4 2-7-8-10-18-17-27z"></path><path d="M266 137c2-1 8-1 11 0 10 3 19 11 24 19 1 3 3 6 3 9h0l-1-1c-1-3-5-10-7-11 0 2 2 3 3 5 2 3 3 6 4 9 1 2 1 3 1 4 0-1-1-1-1-2 0-2-2-4-2-7-1-1-2-3-3-4l-2-2v-1c-1-1-1-2-2-3-1 0-1 0-2-1-2-2-6-7-9-8h-1c-1-1-1-1-2-1l-2-1h0s2-1 2 0h1c1 1 3 1 4 2s1 2 2 2c1 1 2 1 3 2h0 1l-3-4h-1c-2-1-3-2-4-3h-1c-1-1-1-1-2-1l-1-1h-1-1-2 0c-2-1-6-1-7-1l-2 1v-1zm245 415c1 2 1 4 2 6 0 2 0 17-1 20v3h0c-1 1-1 3-1 5l1 12-1 49c0 11 1 22 0 32-1-10 0-20 0-30v-57l-1-29c0-3 0-7 1-11z" class="T"></path><defs><linearGradient id="E" x1="831.908" y1="322.144" x2="840.503" y2="318.268" xlink:href="#B"><stop offset="0" stop-color="#72716c"></stop><stop offset="1" stop-color="#9d9a93"></stop></linearGradient></defs><path fill="url(#E)" d="M835 304c1 2-1 4 0 6h1c1 0 1-1 3 0-3 5-3 10-2 16v1h0c-1 1-1 2-1 3-1 0-1-1-2-1l-1-3c-1-4-2-8-3-13h1l1 2 1-2v-1c-1-2-1-3-1-4 1-1 1-2 1-3 1 0 1 0 2-1z"></path><path d="M832 308l2 1c0 1 0 2-1 3-1-2-1-3-1-4z" class="V"></path><path d="M830 313h1l1 2 1-2c0 2 2 8 1 11 0 0-1 1-1 2-1-4-2-8-3-13z" class="b"></path><path d="M569 238h2l5 6 2 1c1 1 3 0 4 0h10 38c10 0 20 0 30 2h5c8 2 16 4 24 7l11 5c1 0 3 1 4 2v1c-9-5-18-9-28-12-10-2-20-3-30-4h-44-17c-2 0-5 1-7 1-1-1-7-7-9-8v-1z" class="F"></path><defs><linearGradient id="F" x1="836.007" y1="314.397" x2="832.164" y2="288.879" xlink:href="#B"><stop offset="0" stop-color="#656562"></stop><stop offset="1" stop-color="#aeaaa2"></stop></linearGradient></defs><path fill="url(#F)" d="M840 286c0 3-2 5-3 8-2 3-3 7-4 11 0 1 0 2-1 3 0 1 0 2 1 4v1l-1 2-1-2h-1c-1-2-2-6-2-9h1l1-1v-9c1 1 1 0 1 2v1h1v1c1-1 1-1 1-2 1-2 1-4 2-6h1-1c-1-1-1-1-1-2l1-1c0 1 0 1 1 2 1-1 2-2 4-3h0z"></path><path d="M892 423c2 1 2 2 2 3l1 4c-2 3-4 6-7 7h-1c-3 1-7 1-10 0h-3c1-1 2-2 3-2h2 0c1-1 2-1 3-1l1-1c4-2 7-6 9-10z" class="N"></path><path d="M412 870c-3 2-7 3-10 3h-1c-3 1-6 1-8 0h-1c-1 0-1-1-2-1-1-2-1-2-1-4v-1c-3 0-7 0-10-1 1-1 1-2 2-3 2 1 5 3 8 3l2-2v1l-1 2h2 1 1c1 1 3 1 4 2 2 1 5 0 7 0s4 1 7 1z" class="L"></path><path d="M602 858c4 0 9 0 13 1h0l2 2 2 2 1 1-3 1c0 2 1 3 2 4v1h-1-3c-1 0-2-1-3-2-3-3-7-6-10-10z" class="c"></path><path d="M612 868c2-1 3 0 4-1 0-2-2-3-2-5l3-1 2 2 1 1-3 1c0 2 1 3 2 4v1h-1-3c-1 0-2-1-3-2z" class="F"></path><defs><linearGradient id="G" x1="370.49" y1="458.997" x2="368.01" y2="436.503" xlink:href="#B"><stop offset="0" stop-color="#313134"></stop><stop offset="1" stop-color="#504f4c"></stop></linearGradient></defs><path fill="url(#G)" d="M364 443l-2-8c4 3 8 6 10 10s3 8 4 12v3l2 6h-1c-1-1-1-4-2-5-1-3-3-6-4-8l-2-2-1-1c0-1 0-2-1-3h0c-1-2-2-3-3-4z"></path><path d="M611 751c1 2 2 5 3 7-1 1-2 2-1 4 0 2 2 5 4 7 3 5 4 10 9 14v1l-4 1v2l-3-2c-3-2-3-7-4-10l1-4h0c-1-1-1-2-2-3l-1-1c-1-1-1-2-3-2 0-4 0-11 1-14z" class="I"></path><path d="M615 775l1-4c1 3 3 6 3 9 0 2 0 2 1 4 1 1 1 1 2 1v2l-3-2c-3-2-3-7-4-10z" class="Y"></path><path d="M645 596v1c2 2 1 4 4 5 0 4 0 6-2 10-3 4-7 6-12 7h0-5c2-1 3-1 6-1-1-1-1-1-2-1v-1c2-1 3-1 4-3 3-2 5-6 6-10 0-2 0-4 1-7z" class="R"></path><path d="M209 374l1-1h1c-1 4-1 8-2 12 0 1-1 3 0 4 1-2 1-6 2-8h0v4 2l-1 1c0 1 0 5 1 7v3c1 1 1 1 1 2s0 1 1 2v2h1l1 2h-1c-1-1-1-2-2-3l-1-2-1-3c-1-1 0 0-1-2h0v-2h0c0 2 0 4-1 6h0c1 1 1 1 1 2v2c0 1 0 2 1 3v1 1 3h-1v-2c0-2 0-3-1-3v-2c0-2 0-2-1-2v-2c0-2 0-2-1-2v-3l-1-1v-1c1-2 1-7 0-9v1 2c0 1 0 1-1 2v2 1c0 1 0 1-1 2h-1 0c-1-1 0-2-1-3l-1 1h0c1-2 1-4 2-5l1-1v3h0l1-2v-2c0-1 1-1 1-2s0-1 1-2v1 4 2l1-1v-1c1-1 1-1 1-2v-1-3c1-1 1-2 1-3v-4z" class="L"></path><path d="M500 821h2c0 1 2 2 2 3v3 8l-112 1v-2h111v-5c0-3 0-6-3-8zM155 375c-1-3-1-6 0-8v-1c1-2 2-3 4-4 3-2 10-4 14-3l-1 1c-1 2-3 4-6 6 0 0-1 0-1 1-4 1-7 3-9 6l-1 1v1z" class="N"></path><path d="M620 864l2 1v1l2 2c1 0 8-1 9-2s0-1 2-1c2 1 5 0 8 0l-1 1v4l-2 2c-3 0-4 0-7-2s-5-1-9-1c0 2 0 2 1 4v1h-4v-1c-2-1-4-1-6-3h3 1v-1c-1-1-2-2-2-4l3-1z" class="L"></path><path d="M620 864l2 1v1h-1-2-1 0c2 2 3 4 3 6v1c-2-1-4-1-6-3h3 1v-1c-1-1-2-2-2-4l3-1z" class="E"></path><path d="M364 136c0 1-1 2-1 3h0c3-2 6-3 9-3 2 2 1 6 1 8-1 1-2 1-3 2l-1 1h-1l-2 2c-1 0-1 0-2 1h-1c-1 1-1 1-2 1h0-2l-1 1c-1 0-2 1-3 1v-1h1l1-1c1 0 1-1 2-1 2 0 2 0 4-1l2-1c1-1 2-1 3-2 1 0 0 0 1-1s1 0 2-1h1v-1c0-2 1-5 0-6-1 0-2-1-3 0h-1c-1 1-2 1-3 2s1 0-1 1l-1 1-1 1v1h1 1c1 1 1 1 2 1l-2 2-2 1h-1-1 0-1c-1 1-2 2-3 2h-1l1-1c-2 2-4 3-6 5 0 1-1 1-2 1l-1 2h-1-1c-1 1-2 2-3 2l-1 1h-2c-1-1-2-2-2-4 0-1 0 0-1-1v-3-3c1 1 1 0 1 1 0 3 0 5 1 8 1 0 1 1 2 1 4-1 9-7 11-10h2 1c1-1 2-3 3-4 2-2 3-4 5-6 1-1 1-2 2-2z" class="S"></path><path d="M436 150c0-2 1-4 2-6 3-5 8-9 14-11s14-1 20 3c-3 0-9 1-9 1-3 0-5 0-7 1h-1c-4 1-7 2-10 5 0-1 0-2 1-2-1-1-3-1-4-1-1 2-3 3-4 5s-1 3-2 5z" class="R"></path><defs><linearGradient id="H" x1="418.287" y1="755.685" x2="422.228" y2="778.399" xlink:href="#B"><stop offset="0" stop-color="#0d0f0e"></stop><stop offset="1" stop-color="#3f3f42"></stop></linearGradient></defs><path fill="url(#H)" d="M410 783c2-4 3-7 5-10 2-4 5-7 7-11 3-4 5-7 6-11 1 4 0 7 0 11-1 3-2 5-5 7v1h0l-2 3-2 2c-1 2-5 7-8 8h-1z"></path><path d="M419 775c0-1 0-1 1-2l-2-1 4-3 1 1h0l-2 3-2 2z" class="J"></path><path d="M771 560c4 0 8-1 12-2v1l-4 1c0 2 0 2 1 3l-5 4h0l-1 1h2 0l1 1h-1v1h0v1c-1 1-1 2-2 4v1l3-1c1-1-1 0 1-1h1c1 0 2-1 3-1 1-1 1-1 2-1v1c-4 3-11 4-17 5h-1c0-1 1-9 2-10 2-4 4-2 8-4h0c-1 0-2 0-2-1l-3-3z" class="F"></path><path d="M155 374c3 4 7 7 12 7 2 1 5 1 8 1h1c-1 1-2 2-2 3-1 2-1 2 0 4 1 1 2 1 4 1-2 1-5 1-7 1s-4-1-6-2c-6-2-9-9-10-14v-1zm37 30c0 6-1 10 1 16v1l-2 2c-3 1-5 3-7 3 1-2 2-5 1-7 0-2-2-4-4-5l-6-3h2 3c4 0 9-4 12-7z" class="C"></path><path d="M779 104c3-1 10 0 13 0 1 1 4 0 6 0 1 1 2 1 4 1h3c1 1 1 1 2 1h3c1 1 1 1 3 1h2c1 1 1 1 3 1 1 1 2 0 3 0 2 2 4 0 6 1 0 1-1 1-1 1-1 0-1 0-2 1-1-1-2 0-3 0v1l-4 1h0c-4 0-9-2-12-3 1 0 3 1 5 1h1l1 1h5c2-1 0 0 2 0h1v-2h-2-2c-1-1-2-1-3-1h-2c-2-1-2-1-3-1-1-1-2 0-3 0l-1-1h-3l2 1h3l1 1h0c-2 0-3 1-4 0-2-1-5 0-7-1v-1h-1-2c-2-1-3-1-4-1-5-1-10 0-15 0h-1-2l-4 1c-1 0-2 1-2 1h-1-1l-1 1c-1 0-1 0-2-1 2-1 3-1 5-1 1-1 0-1 2-1h2c1-1 1-1 3-1h2c1-1 4-1 5-1z" class="T"></path><path d="M606 157c0-6-1-12-3-19 3 3 6 6 8 10l-1 3h0c1 7 2 12 4 19-1 1-2 2-2 4 0-2 0-5-1-6-1 1-1 3-2 4h0v-3h-1 0c0 1 0 1-1 2l-1-1v-2-1c1-3 1-8 0-10z" class="Q"></path><path d="M610 151c1 7 2 12 4 19-1 1-2 2-2 4 0-2 0-5-1-6-1 1-1 3-2 4h0v-3h0c1-4 1-7 0-10 0-2-1-4 0-6v2c0-2 1-3 1-4z" class="J"></path><path d="M189 386c2-1 3-2 5-4 3-3 5-6 7-10s4-8 6-11l1 1c-2 2-3 4-4 6l-3 6v1 1 2l-1 1c-1 1-3 3-4 5l-3 4c-1 2-3 5-4 5-2 1-4 5-6 4h0 0c2-2 2-2 3-4h0c1-1 1-2 2-2h-2-1l-1 2h-2c-1 0-1 0-2 1h-2v1h-3-1c-1 1-5 1-7 1l-1-1h-2 0c1-1 5-1 6-1 2 0 3 1 5 0h0 2v-1c1-1 2-1 3-1v-1c-1 0 0 0-1 1-2 0-6 0-8-1 2 0 5 0 7-1h2s1-1 2-1 1 0 2-1h1l2-1c1-1 1-1 2-1z" class="E"></path><defs><linearGradient id="I" x1="642.281" y1="721.145" x2="640.463" y2="733.61" xlink:href="#B"><stop offset="0" stop-color="#3b3b3c"></stop><stop offset="1" stop-color="#5e6466"></stop></linearGradient></defs><path fill="url(#I)" d="M629 719c1 0 1 0 2-2 1 3 7 7 10 9 5 2 11 4 15 7 0 2-1 1-2 2v-1c-1 1-1 1-2 1h-1-1 0v-1h-3l-1-1h-3c-2 0-3-1-5-2h-1c-2-1-4-3-6-5h-1l-1-1c0-1-1-2-1-3h1c0-1-1-2 0-3z"></path><defs><linearGradient id="J" x1="417.858" y1="477.285" x2="417.008" y2="448.585" xlink:href="#B"><stop offset="0" stop-color="#292a2d"></stop><stop offset="1" stop-color="#4f5255"></stop></linearGradient></defs><path fill="url(#J)" d="M403 449c5-1 11-1 17 1 1 1 3 3 5 3v-1-1c5 5 8 11 8 18 0 4-2 7-5 10 0-4 1-7 1-10 0-5-3-10-6-13l-1-1c0-1-2-2-3-2-2 0-4-1-6-1h0l-7-2c-1 0-2-1-3-1z"></path><path d="M400 540c-7-3-11-6-16-13 3-1 5-1 8-2 5-1 10 0 14 3h-1c-2 1-3 0-5 0h0l1 1h0v1 6 3h0c-1 1-1 0-1 1z" class="H"></path><path d="M401 536l-2-2c0-2-1-3 0-5h2v1 6z" class="G"></path><path d="M665 292v-1l1-1 6 3 1-1 1 1c0 1-1 1 0 1 6 4 10 11 11 18 0 5-1 9-2 14-1-1-1-2-1-3 0-2 0-3-1-5 0-8-3-13-9-18-1-4-4-6-7-8z" class="D"></path><defs><linearGradient id="K" x1="310.41" y1="448.782" x2="303.779" y2="432.977" xlink:href="#B"><stop offset="0" stop-color="#2f302c"></stop><stop offset="1" stop-color="#6b696a"></stop></linearGradient></defs><path fill="url(#K)" d="M267 444l18-5v-1c7-1 14-2 20-1 5 2 9 1 13 3l1-1c3 1 5 3 7 4v1h-2l-2 1c-17-6-34-7-51-1-5 1-9 4-13 5 2-2 6-4 9-5z"></path><defs><linearGradient id="L" x1="640.465" y1="743.299" x2="651.227" y2="759.246" xlink:href="#B"><stop offset="0" stop-color="#2f2e2d"></stop><stop offset="1" stop-color="#646668"></stop></linearGradient></defs><path fill="url(#L)" d="M624 739c2 2 4 5 7 6 5 3 10 5 16 7 3 0 6 2 10 1 2 0 4 0 6-2 0-1 1-2 1-3h1c0 2 0 3-1 5-5 4-14 1-17 5-2-1-4-2-7-2l-9-4-2-1h0c-2-2-4-4-5-6v-1l2 2c0 1 1 1 1 2h2c2 1 2 1 4 1v-1c-1-1-3-2-4-3l-3-3c-1-1-2-1-2-3z"></path><path d="M629 751c1-1 1-1 2-1 3 1 5 2 7 3l1 1 1 2-9-4-2-1z" class="H"></path><path d="M246 489c1-3 2-5 4-8l-2 9c-1 4-1 8 0 12 1 3 2 6 4 9-1 0-2-1-2-1-2-2-1-5-3-7v-3-1-2l-2 1-1 1v1 8l1 1v2h0l1 1 1 1c0 2 3 4 4 5h-1l1 1h1 1c1 1 1 1 2 1l1 1c4 0 9-1 12 1h0c-5 0-10 0-14-1h-1c-4-1-9-5-11-8v-1l-1-2c-2-5-1-14 2-20h0 1c1 0 1-1 2-1z" class="N"></path><path d="M243 490h0 1c1 0 1-1 2-1-2 7-4 14-2 20v3c-1-2-2-4-2-7h-1c0 2 0 4 1 6v1l-1-2c-2-5-1-14 2-20z" class="O"></path><path d="M767 125c1-1 2-1 4-1 0-1 1-1 2-1h0c1 0 1 0 2-1 1 0 1 0 2 1v1c-1 1-4 1-6 2s1 0-1 0c-1 0-1 1-1 1-2 1-4 1-6 2 0 1-1 1-1 1l-5 4-2 1c-3 2-6 5-8 7 1 0 2-1 4-2 1-1 3-2 5-3 1 0 1 0 2-1 2 0 5-1 6-3l4-1h3c1-1 1-1 2-1h4 0l3 1h5 0c3 1 6 2 8 3h0l-1 1 2 1-1 1c-3-2-5-4-8-4l-1-1h0c-1 1-2 1-3 0h-5c-1 0-2 0-2 1h-1-3c-2 1-3 1-5 2h5v-1h2c2 1 5 0 7 1h2c1 0 4 0 5 1-1 1-3 1-5 1l-1-1h-2c-4-2-12-1-16 1h-1c-1 0-2 1-3 1-1 1-1 1-2 1h-2c1-1 2-1 3-2l3-1h-1c-6 1-12 6-16 10v-2l1-1 1-2c3-5 9-9 15-12 1-1 7-3 7-5z" class="L"></path><path d="M195 278c-1 1-5 1-7 1 3-5 6-8 11-10 2 1 4 1 7 3l1 1 1 1h0l1-1h1v3c-1 2-1 4-1 7 0 1 1 2 2 3l-1 1-3-3c-1 0-1 0-2-1-3-2-6-4-10-5z" class="S"></path><path d="M195 278h-1v-1h3v1c2 0 4 1 6 2l1 1c1 0 1 1 2 1l1 1v-1c0-1 0-2-1-3h0l1-2c0-2-1-3-2-4l1-1 1 1 1 1h0l1-1h1v3c-1 2-1 4-1 7 0 1 1 2 2 3l-1 1-3-3c-1 0-1 0-2-1-3-2-6-4-10-5z" class="U"></path><path d="M413 752c0 1-1 3-1 4l3-2c-1 3-2 4-3 6l-2 2c-3 3-5 5-10 5h-4v-1-1l2-1v-1h-2l-1-1c-2 0-4-2-6-3l2-1 7-1 7-2h2c2 0 3 0 5-1l1-2z" class="B"></path><path d="M413 752c0 1-1 3-1 4h-1-1-1l-3 2-1 1-1-1c1-1 1 0 0-1-2 1-3 1-5 1l-1-1 7-2h2c2 0 3 0 5-1l1-2z" class="G"></path><path d="M389 759l2-1c2 2 2 2 4 2 4 1 6 1 10-1h0l1-1v1c-1 2-4 4-6 5-1 1-2 1-4 1l2-1v-1h-2l-1-1c-2 0-4-2-6-3z" class="H"></path><path d="M412 756l3-2c-1 3-2 4-3 6l-2 2c-3 3-5 5-10 5h-4v-1-1c2 0 3 0 4-1 2-1 5-3 6-5v-1l3-2h1 1 1z" class="D"></path><path d="M347 93l-1 2v1c-3 3-5 8-7 12h0l-2 7c0 2 0 3-1 4h0 0l-1-1c0-1 0-1 1-2h0l-1-1c1-2 0-3 1-4 0-2 1-2 1-4h0v-2h1v-1c0-1 1-2 1-3 1-1 1-2 2-4h1c1-2 3-4 4-6-1 1-2 2-3 2l-3 3c0 1 0 1-1 2 0 0-1 1-1 2-1 2-3 4-4 6s-1 3-2 5v-1-1c1-3 1-4 3-6l2-3c-1-1-1-1-2-1l1-1c0-1 0-1 1-2l1-1c1-2 4-6 6-7 1 0 2-1 3-1h-2-1-1-2c-1 1-2 1-2 1l-1 1c-1 0-2 1-3 2-1 2-1 3-2 5 0 1-1 1-1 2v1l-1 2v2c-2 3 1 8-1 11 0-6 0-10 1-16 1-3 3-7 5-9 4-3 9-3 14-3 1 0 2 0 4 1-1 1-2 2-3 2-1 1-2 3-4 4z" class="S"></path><path d="M350 86c1 0 2 0 4 1-1 1-2 2-3 2-1 1-2 3-4 4h0l3-5-3 2c-3 1-5 3-7 4h-1c2-2 5-5 7-6 2 0 2-1 3-2h1z" class="X"></path><path d="M841 270c2-1 3-1 5 1 2 1 7 4 8 7h0c-1 0-3 1-3 0-4 1-7 2-10 3-1 1-1 2-1 4v1h0c-2 1-3 2-4 3-1-1-1-1-1-2l-1 1c0 1 0 1 1 2h1-1c-1 2-1 4-2 6-1-2-1-2-1-4v-3c0-1 0-3 1-4v1c1 0 2-1 2-2v-1c1-2 1-2 1-4v-1c1-1 1 0 1-1v-2l1-1v-1-1-1c1-1 2-1 3-1z" class="S"></path><path d="M841 270c1 2-2 8-3 10l-1 1c1-1 1 0 2-1h2c-2 1-4 2-6 4v-1c1-2 1-2 1-4v-1c1-1 1 0 1-1v-2l1-1v-1-1-1c1-1 2-1 3-1z" class="X"></path><path d="M841 280c3-1 8-3 11-2h0-1c-4 1-7 2-10 3-1 1-1 2-1 4v1h0c-2 1-3 2-4 3-1-1-1-1-1-2l-1 1c0 1 0 1 1 2h1-1c-1 2-1 4-2 6-1-2-1-2-1-4v-3c0-1 0-3 1-4v1c1 0 2-1 2-2 2-2 4-3 6-4z" class="O"></path><path d="M835 287c1-3 3-4 6-6-1 1-1 2-1 4v1h0c-2 1-3 2-4 3-1-1-1-1-1-2z" class="T"></path><path d="M837 257l4-1v1c-3 4-7 10-6 15 1 0 2 0 3-1v1 1 1l-1 1v2c0 1 0 0-1 1v1c0 2 0 2-1 4v1c0 1-1 2-2 2v-1c-1 1-1 3-1 4v3c0 2 0 2 1 4 0 1 0 1-1 2v-1h-1v-1c0-2 0-1-1-2-1-3-2-8 0-11 0-3 1-4 0-7-1-1 1-6 1-8 2-4 3-8 6-11z" class="U"></path><path d="M830 283h1l3-6c1 2-1 6-1 8-1 1-1 3-1 4v3c0 2 0 2 1 4 0 1 0 1-1 2v-1h-1v-1c0-2 0-1-1-2-1-3-2-8 0-11z" class="g"></path><path d="M760 558c3 0 7 0 10 1l1 1 3 3c0 1 1 1 2 1h0c-4 2-6 0-8 4-1 1-2 9-2 10h1l-7 1v-16-5z" class="V"></path><path d="M760 558c3 0 7 0 10 1l-2 1h-1l1 1s0 1 1 1l2 2h1c-1 1-5 1-7 1l-1-1h-1-1l-2-1v-5z" class="b"></path><path d="M504 249v-1c1 7 1 14 1 21v31 113 67 18c0 2 0 6-1 8v-5-10-44-198z" class="F"></path><defs><linearGradient id="M" x1="418.953" y1="425.096" x2="399.311" y2="439.072" xlink:href="#B"><stop offset="0" stop-color="#0a090b"></stop><stop offset="1" stop-color="#383b3c"></stop></linearGradient></defs><path fill="url(#M)" d="M390 430c1-1 3-2 4-3 6-3 13-3 19-2 1 2 4 2 5 3 3 1 5 3 7 5l1 1v1l-1 1c-1-1-3-2-4-2h-3c-2-2-9-2-12-3h-5c-1-1-1 0 0-1-2 0-4 0-6 1-2 0-3 0-5-1z"></path><path d="M401 430c4 0 7-1 11 0 3 1 6 3 9 4h-3c-2-2-9-2-12-3h-5c-1-1-1 0 0-1z" class="H"></path><path d="M465 284h1c4 7 5 15 6 23 1 3 1 7 1 11v31 68 66 16c0 2 0 5-1 8v-2-57-64-47c0-11 1-23-1-34-2-7-4-13-6-19z" class="N"></path><defs><linearGradient id="N" x1="159.973" y1="152.716" x2="176.07" y2="160.516" xlink:href="#B"><stop offset="0" stop-color="#aaa9a3"></stop><stop offset="1" stop-color="#d2d0cb"></stop></linearGradient></defs><path fill="url(#N)" d="M177 167c-2 0-4 0-6-1-1-2-2-5-4-7-1-3-4-5-5-8v-3l1-1c3 1 6 3 9 4 0 1 1 1 2 1 2 1 4 1 7 1v-1h2c1-1 1 0 2-1h1v-1c1-1 1-1 2 0v1c-1 1-3 3-5 3v1h-2l-2 1-1 1-1 3h2 1 1c-1 2-4 2-5 4-1 0-1 0 0 1s1 1 1 2z"></path><path d="M211 365c1-3 3-6 5-8l-2 10c-1 1-2 6-3 6h-1l-1 1v4c0 1 0 2-1 3v3 1c0 1 0 1-1 2v1l-1 1v-2-4-1c-1 1-1 1-1 2s-1 1-1 2v2l-1 2h0v-3l-1 1c-1 1-1 3-2 5h0l-2 5v-2l-1 1v2c-1 2-1 3-1 6h0 0c0-1-1-2-1-3l1-1-2-1v-2h0c1-1 1-4 2-5 0-2 1-3 2-4v-2h0v-1l1-1 1-1c1-1 1-2 2-2v-2c1-1 1-2 2-3 0-2 1-3 1-4h1v-1l1-4c1-1 1-2 2-3l1-3c0 1 1 1 0 3h1z" class="E"></path><path d="M211 365c1-3 3-6 5-8l-2 10c-1 1-2 6-3 6h-1l-1 1h0v-3c1-1 1-2 1-3l1-3z" class="S"></path><path d="M192 404c0-3-1-6 1-9h1c0 1 0 1-1 2v1 1 2c0 1 0 1 1 2 0 1-1 1-1 2v1 1c0 2 0 5-1 6h0c1 3 1 5 3 8 2 0 2 1 4 3 0-2-1-5 0-6 0-1 0-2 1-3v-1c1 2 0 3 0 5v7s1 1 1 2h1v2l1 1c1 0 1 1 1 1 2 2 0-1 1 1 0 1 1 1 2 2v-3l-1-2v-2h-1v-3c0-1 0-2-1-2 0-2 1-3 0-4v-10h1c1 2 0 7 0 9 1 0 1-1 1-1v1c0 1 0 0 1 2v1c0 1 0 1 1 2v2c0 1 0 2 1 3 0 1-1 2 0 4v1l2-2c0-1 0-1 1-2v-1l-1-1v-1-1-4-4h1v1 1c1 2-1 6 1 8h0 1c0 1-1 1-1 3l-4 5c-1-2 0-3-1-5 0-1-1-3-1-4v-1l-1-2v3 1c0 1 0 1 1 2v2 1c1 1 0 2 0 4h-1 0c-1-1-2-1-3-1l-3 1-7 1c0 1-1 1-2 1l1-1c2 0 6-2 8-3-1-1-2-1-3-1v-2c-1-2-2-1-4-2v-1l1-2v-1l1-1c1-2-1 1 1-2h-1c-1 1-2 1-4 1h0l2-2v-1c-2-6-1-10-1-16z" class="E"></path><path d="M200 427l6 9h0c-1-1-2-1-3-1l-3 1-7 1c0 1-1 1-2 1l1-1c2 0 6-2 8-3 1 0 2-1 2-1l-1-2c-1-1-1-2-1-3v-1z" class="c"></path><path d="M194 426l2 2c1-1 1-1 2-1v-2h0l2 2v1c0 1 0 2 1 3l1 2s-1 1-2 1c-1-1-2-1-3-1v-2c-1-2-2-1-4-2v-1l1-2z" class="S"></path><path d="M621 753v-2h2 1c1 1 2 1 3 1l2 1h1c1 1 2 1 2 2 2 2 5 1 6 2h1 1s1 0 1 1h3c-1-1-3-1-4-2-3 0-7-2-9-4l9 4c3 0 5 1 7 2v1c-2 2-2 2-4 3 0 1 1 1 2 2l-3-1c0 2 0 3-1 4-1 0-2 1-4 1-4-1-9-6-12-10-1-2-3-4-4-5z" class="B"></path><path d="M642 763h-1-2c-1-1-2-1-3-1h-1c-2-1-6-5-7-7 1 1 3 1 4 1 1 1 2 1 3 1 4 1 7 2 11 2h1c-2 2-2 2-4 3 0 1 1 1 2 2l-3-1z" class="D"></path><path d="M243 490c2-6 5-12 9-17 7-10 20-15 32-17h0v1c-2 0-3 1-5 2-11 4-20 8-26 18-3 5-5 16-4 22l2 4c1 1 2 3 3 4s3 2 3 3c-4-2-5-2-7-6 0 1 0 1 1 3h0c1 3 2 5 4 7h0l-3-3c-2-3-3-6-4-9-1-4-1-8 0-12l2-9c-2 3-3 5-4 8-1 0-1 1-2 1h-1 0z" class="g"></path><defs><linearGradient id="O" x1="421.713" y1="680.911" x2="400.086" y2="669.944" xlink:href="#B"><stop offset="0" stop-color="#3f4448"></stop><stop offset="1" stop-color="#5a5a59"></stop></linearGradient></defs><path fill="url(#O)" d="M427 668h1c0 4-3 6-6 8-4 4-7 7-13 9l-1 1h-1c-1 0-2 0-3-1v1 1l-2 1h-4l1-1c-2-3-3-6-5-10l8 1c10 1 18-4 25-10z"></path><defs><linearGradient id="P" x1="733.388" y1="178.277" x2="722.933" y2="165.269" xlink:href="#B"><stop offset="0" stop-color="#bcbab4"></stop><stop offset="1" stop-color="#dcdbd5"></stop></linearGradient></defs><path fill="url(#P)" d="M721 145v4 11 2c1 1 1 1 1 2-1 0-1 0 0 1v1l1 5h1c0-2 1-2 2-3h-1c0-2 0-6 1-7h0c0-1 0-2 1-2h1l-1-1v-2c1 1 2 1 2 2v5c1-1 1 0 1-1l1-1c1-1 1-2 2-3h0v2c-1 1-2 3-3 4l-1 1c0 1 1 2 1 3l2-1v-1h0c0-2 1-2 1-3h0v-1l1-1 1-2c0-2 1-2 1-3s0-2 1-2v-1c0-1 1-1 1-2l2-4c1-2 3-4 4-5l2-1v-1l4-5c1 0 2-1 3-2s1 0 2-1c2-2 5-4 8-5l2-1c1-1 1-1 2-1 0 2-6 4-7 5-6 3-12 7-15 12l-1 2-1 1c-2 2-6 9-6 12h-1c-1 4-3 8-4 12 0 4 0 9-1 13-1 0-3 0-4-1s-2-3-3-5c-1-3-2-5-2-8l-1-3c-1-5-1-10-1-15 0-1 0-3 1-5z"></path><path d="M228 479c1-1 1 0 3 0-2 3-4 7-7 10 0-1 0-2 1-3h1c0-1 0-2 1-2 0-1 1-2 2-4l-2 1c-1 0-2 1-3 2-1 0-2 0-2 1-1 0-1 1-2 1l-2 1h3 0c1-1 2-1 3-1-1 1-3 3-5 4-1 1-2 1-3 2h0-1l-1 1h-1-1l-1 1h-1c-2 0-5 0-8-1h-1l-1 1c-4 0-6 1-9 1-1 1-2 1-3 0v-1c1-2 5-4 6-6 1-1 3-2 4-4h0c3 0 4 0 7 2v1c3 2 5 1 9 1l2-1c4-2 9-4 12-7z" class="L"></path><path d="M228 479c1-1 1 0 3 0-2 3-4 7-7 10 0-1 0-2 1-3h1c0-1 0-2 1-2 0-1 1-2 2-4l-2 1c-1 0-2 1-3 2-1 0-2 0-2 1-1 0-1 1-2 1l-2 1h3c-3 4-8 6-13 6h-3l1-1c-1-1-2-1-3-1-1-1-3-1-4-3h0l1-1c1 0 3 0 4 1l1-1c3 2 5 1 9 1l2-1c4-2 9-4 12-7z" class="F"></path><path d="M205 486c3 2 5 1 9 1h0l-1 1h1c-1 1-3 1-5 1l-5-2 1-1z" class="E"></path><defs><linearGradient id="Q" x1="376.493" y1="464.577" x2="388.917" y2="447.546" xlink:href="#B"><stop offset="0" stop-color="#404245"></stop><stop offset="1" stop-color="#6b6d6e"></stop></linearGradient></defs><path fill="url(#Q)" d="M376 457c1-7 2-11 6-16 5 6 5 10 4 17v4 1c0 1 0 1 1 1l-1 3c0 2-2 3-2 5-1-2-1-3-3-4-1-1-2-1-3-2h0l-2-6v-3z"></path><path d="M511 686c1 5 0 9 2 13h0c1 7 0 15 0 23v52l-1 19c0 4 1 9 0 12v2l-1-1c1-9 0-19 0-28v-8c-1-14-1-27-1-41v-20c0-4 0-9 1-13v-10z" class="L"></path><path d="M511 686c1 5 0 9 2 13h0l-1 10c0-4 1-8-1-13h0v-10z" class="E"></path><path d="M511 778l1-6v10c0 7-1 16 0 23v2l-1-1c1-9 0-19 0-28z" class="F"></path><defs><linearGradient id="R" x1="597.851" y1="189.978" x2="593.806" y2="161.14" xlink:href="#B"><stop offset="0" stop-color="#1f2026"></stop><stop offset="1" stop-color="#525756"></stop></linearGradient></defs><path fill="url(#R)" d="M606 157c1 2 1 7 0 10v1 2l1 1c1 1 0 2 0 3-1 0-1 0-2 1l-2-2c-1 1-1 1-1 2-3 6-9 12-14 17h-1l-1-2c0-2 1-3 2-5l-1-1c-1-1 0-1-1-2h0 0 1c1-1 3-4 4-6 6-6 12-10 15-19z"></path><path d="M246 469h0c-6 7-11 15-13 24-2 10-1 23 3 32l1 1c2 6 7 9 12 13v2c2 0 3 1 5 2l3 1c2 0 3 1 4 1 3 1 6 0 9 1 2 0 9 0 10 1l-1 1h-1c-5-2-11 0-16-2h-1-3l-4-2h-1c-7-3-14-8-18-14v-1c-4-6-6-12-6-19 0-9 2-20 6-28 3-5 7-9 11-13z" class="c"></path><path d="M504 520l1 1v4 15 213 42 8s-1 2 0 3v1c2-1 3-1 5-1h-1c-2 1-4 1-5 1-1-2 0-5 0-7v-11-36-233z" class="F"></path><path d="M504 249h-9l-1 72v120 45c0 6 1 14 0 20h-1V248c3 0 9-1 11 0v1z" class="S"></path><defs><linearGradient id="S" x1="634.316" y1="856.953" x2="641.684" y2="866.547" xlink:href="#B"><stop offset="0" stop-color="#a8a6a0"></stop><stop offset="1" stop-color="#dad7d1"></stop></linearGradient></defs><path fill="url(#S)" d="M641 858h8c0 2 1 4 0 6l-1 1h-5c-3 0-6 1-8 0-2 0-1 0-2 1s-8 2-9 2l-2-2v-1l-2-1-1-1-2-2-2-2c2-1 4-1 6-1h11 8l1 1v-1z"></path><path d="M641 858h8c0 2 1 4 0 6l-1 1c0-1 0-2-1-2-2-1-4-4-6-5z" class="L"></path><path d="M619 863c0-1-1-2-1-3 1 0 3 2 5 2s3 0 5-2c2 0 2 0 4 1-2 2-3 2-6 3h-4v1l-2-1-1-1z" class="U"></path><path d="M622 865v-1h4c3-1 4-1 6-3 1 1 2 2 3 4-2 0-1 0-2 1s-8 2-9 2l-2-2v-1z" class="X"></path><path d="M426 733h1c1 0 1 0 1-1l2-2v-1c1 1 1 1 2 1v3c-1 1-1 3-1 4l-1 5-1 3c-3 7-10 17-17 20l-1 1v-1-3h-1l2-2c1-2 2-3 3-6l-3 2c0-1 1-3 1-4h0c2-1 2-1 4-1l4-5 5-13z" class="J"></path><path d="M421 746c1 1 0 3-1 5l-1 3v1c-2 2-4 5-7 6l-1 1h-1l2-2c1-2 2-3 3-6l-3 2c0-1 1-3 1-4h0c2-1 2-1 4-1l4-5z" class="Y"></path><path d="M413 752h0c2-1 2-1 4-1 0 1-1 2-2 3l-3 2c0-1 1-3 1-4z" class="I"></path><path d="M201 365c2-2 4-4 7-6v1l-1 1c-2 3-4 7-6 11s-4 7-7 10c-2 2-3 3-5 4-1 0-1 0-2 1l-2 1h-1c-1 1-1 1-2 1s-2 1-2 1h-2c-2 0-3 0-4-1-1-2-1-2 0-4 0-1 1-2 2-3s3-1 4-2c2 0 4-1 5-2s1-1 2-1c2-1 3-3 5-4v1c1 0 2-1 3-2 2-2 5-4 6-7z" class="E"></path><path d="M201 365c2-2 4-4 7-6v1l-1 1c-2 3-4 7-6 11s-4 7-7 10c-2 2-3 3-5 4l4-4c4-4 7-10 10-16-1 2-3 4-4 5-2 0-3 2-4 3-3 2-11 8-14 7 1 0 3-1 4-1 2-2 5-4 7-6 1 0 2-1 3-2 2-2 5-4 6-7z" class="O"></path><path d="M174 385h1c7 0 10-2 15-6 1-1 2-1 3-2l1-1c1-1 1-1 4-1l-2 2-1 1c-1 1-1 2-2 3v1l-4 4c-1 0-1 0-2 1l-2 1h-1c-1 1-1 1-2 1s-2 1-2 1h-2c-2 0-3 0-4-1-1-2-1-2 0-4z" class="T"></path><defs><linearGradient id="T" x1="395.27" y1="781.754" x2="418.616" y2="790.389" xlink:href="#B"><stop offset="0" stop-color="#100f12"></stop><stop offset="1" stop-color="#2b2e2f"></stop></linearGradient></defs><path fill="url(#T)" d="M419 775l2-2h1c1 1 1 2 1 3l2 1v1l-1-1-2 1-1 1h1v1c-2 3 0 6-2 8 0 1-1 2-1 3-4 5-13 8-20 8-3 1-7 1-9-1v-1-1c3-1 5-3 7-5l1 1v1h0c3 1 10-1 13-2 1-1 2-1 4-1 0-2 1-4 1-5h-2-4v-2h1c3-1 7-6 8-8z"></path><path d="M398 793h0c3 1 10-1 13-2 1-1 2-1 4-1-4 4-12 5-17 6h-3l3-3z" class="U"></path><path d="M236 118c2-1 4-1 7-2h0c3-1 5-1 7-1s4 0 6-1v1h8c2 1 3 0 5 1 3 1 6 1 9 3h1l6 3c1 0 1 1 2 1h2v1l1 1h2c-1-2-3-2-4-4l-2-2 1-1c9 5 15 11 20 20l2 2v1l1 4c0 1 1 2 1 3 0 2 0-1 0 1 0 1 0 2 1 2v1c-1 1-1 1-1 3 1 1 0 1 0 2 0-1-1-1-1-2v-1c0-1-1-2-1-3v-2h0l-1-2-1-2c-1-1-1-2-2-3v-1l-1 1v2l1 1v2c-1-1-1-2-2-3 0-1 0-2-1-3v-1c-1-2-2-4-4-5v-1l-1-1-1-1-1-1c-1 0 0 1-1 0-2-2-4-3-6-5l-5-3-3-1c-1 0-1-1-2-1h0l-1-1c-2 0-3 0-4-1-2-1-2-1-3-1l-1-1h-2-3c-6-1-16-2-22 0l-3 1-2 1-1-1z" class="N"></path><path d="M288 121l-2-2 1-1c9 5 15 11 20 20l3 9c-6-10-12-20-22-26z" class="K"></path><path d="M892 413c1 0 1 0 2 1 0 2 1 4 0 6-1 1-2 2-2 3-2 4-5 8-9 10l-1 1c-1 0-2 0-3 1h0-2c-1 0-2 1-3 2h-1c-1 0-3-2-4-3-1 0-1-1-1-1-3-2-11-5-15-4-2 1-3 1-5 3h1c-1 1-1 1-3 1l1-1c2-2 2-4 3-7h0c1-2 2-2 4-3 2 1 5 3 8 4 2 1 3 2 5 3 4 1 8 1 12-1 6-3 11-9 13-15z" class="L"></path><path d="M523 244h1c-1 2-2 2-2 4-1 8 0 17 0 26v57 175h0c-1-2-1-5-1-7v-76l1-177c0-1 1-1 1-2z" class="F"></path><defs><linearGradient id="U" x1="444.45" y1="650.916" x2="407.049" y2="666.793" xlink:href="#B"><stop offset="0" stop-color="#3d4145"></stop><stop offset="1" stop-color="#66686a"></stop></linearGradient></defs><path fill="url(#U)" d="M442 647h2c-1 1-3 5-3 5h1v1l1 1c-1 2-4 5-6 6h0l-3 2h-1 0c-8 5-19 6-28 5 1-3 3-6 5-8v1c4 1 12-2 16-3 2-1 5-2 7-3l1-1c1 0 2-1 3-2l5-4z"></path><path d="M442 653l1 1c-1 2-4 5-6 6h0l-3 2h-1c3-4 6-7 9-9z" class="e"></path><defs><linearGradient id="V" x1="423.73" y1="740.888" x2="397.844" y2="735.726" xlink:href="#B"><stop offset="0" stop-color="#000101"></stop><stop offset="1" stop-color="#353637"></stop></linearGradient></defs><path fill="url(#V)" d="M427 721v3 1h0 1l-2 8-5 13-4 5c-2 0-2 0-4 1h0l-1 2c-2 1-3 1-5 1h-2l-7 2-7 1-2 1-2-1 2-1 5-1v-1c1 0 1-1 1-1v-1l2-2 5-2c10-4 17-10 21-20h1c1-2 1-4 2-6l1-2z"></path><path d="M410 751c0-1 1-1 2-2h1v1c-1 1-1 2-2 3l1 1c-2 1-3 1-5 1h-2c2-1 4-2 5-4z" class="M"></path><path d="M410 751c-1 2-3 3-5 4l-7 2-7 1-2 1-2-1 2-1 5-1c6-1 11-3 16-5z" class="D"></path><defs><linearGradient id="W" x1="503.886" y1="105.33" x2="492.249" y2="109.68" xlink:href="#B"><stop offset="0" stop-color="#383b3f"></stop><stop offset="1" stop-color="#55585a"></stop></linearGradient></defs><path fill="url(#W)" d="M491 97v-1h2c1 1 2 1 3 1 1 1 3 1 4 2 1 0 2 1 3 2 1 0 2-1 3-1h0c1 2 2 3 3 5h-2c0 2 2 4 3 6h0v2c0 2 1 5 3 7v8l-1 1c-2-4-4-8-8-11h0c0-1 0-1 1-1v-1c-5-3-10-6-14-9-2-1-2-9-2-12 0 1 1 1 1 2h1z"></path><path d="M502 106l1-2c3 3 5 6 7 9 0 2 1 5 3 7v8l-1 1c-2-4-4-8-8-11h0c0-1 0-1 1-1 1 1 2 3 3 4l1-1-1-1h1c0-5-4-10-7-13z" class="B"></path><path d="M491 97v-1h2c1 1 2 1 3 1 1 1 3 1 4 2 1 0 2 1 3 2 1 0 2-1 3-1h0c1 2 2 3 3 5h-2c0 2 2 4 3 6h0v2c-2-3-4-6-7-9l-1 2-6-5c-2-1-4-2-5-4z" class="Q"></path><path d="M496 101v-1c3 0 6 3 7 4l-1 2-6-5z" class="J"></path><path d="M561 519h1c-1 0-1 0-1 1v7 19 39 116 37c0 8 0 17 1 25 1 12 4 24 10 34l-1 1c-3-4-4-10-6-14 0-2-1-4-2-6 0-2 0-2-1-4v4l-1 33h-1v-8l1-30-1-27V523c0-2 0-3 1-4z" class="N"></path><path d="M482 520h1v298c-8-1-16-2-24-1h-1 0l2-1c2 1 5 0 7 0h13c1 0 1 1 2 0v-24c0-4 1-9 0-12-1 2-1 4-2 6-1 4-2 7-4 11l-1-1c2-3 3-7 4-11 1-3 2-6 3-10v-15-24-36-117-63z" class="F"></path><path d="M827 166h0c4-2 6-6 8-10 0 7 0 14 6 20 2 2 5 3 8 4 1 0 1-1 2-2l1-5s1 1 2 1c3 3 10 5 14 4 1 0 1-1 2-1l1-1c-1 0-1 0-1-1h1v-2l10 5-1 1c-3 1-6 1-9 2-5 1-8 4-12 7 0-2 0-5-2-7l-1-1c-3 1-8 6-10 8 0-2 0-5-1-7-2-1-4-1-6-1l-4 1-2 2c1-3 1-8 2-12-3-2-5-3-8-4v-1z" class="C"></path><path d="M384 859c4-1 8-1 11 0 9 0 18 0 27-1-1 1-1 2-2 3l-1 2c-2 3-4 5-7 7-3 0-5-1-7-1s-5 1-7 0c-1-1-3-1-4-2h-1-1-2l1-2v-1l-2 2c-3 0-6-2-8-3l3-3v-1z" class="S"></path><path d="M760 426v1 14c2 1 4 1 6 1h1c-1 2-1 6-1 8v1l1 1s1 0 1 1l-1 2 3 1c1 0 2 0 3 1h0-3v1l1 1c2 1 5 2 7 3 1 1 3 2 3 2l6 3h0-1c-1 0-2-1-3-2-3-1-7-3-10-4h-1c-2 0-3-1-5 0-1 0-2 1-3 2l1 1h3l-1 1 2 1c-1 1-1 3-2 4h0 0l-1 7 2 1c-1 0-3 1-3 0-2 0-3-2-4-1 0 1-1 4 0 4l1 1v1 1l1 1-3-2v7h1c0 3 1 11-1 13l1 1h-1c-1-4-1-10-1-14l1-27v-24c0-4-1-9 0-13z" class="g"></path><path d="M770 456c1 0 2 0 3 1h0-3v1l1 1h-3l-2-1c-1 0-1 0-2-1 0-1 0-1 1-1h5z" class="O"></path><path d="M766 477l-3-3c-1-2 1-4 1-5 1-1 1-2 1-3 0 0 0-1 1-1h1l2 1c-1 1-1 3-2 4h0 0l-1 7z" class="c"></path><path d="M766 442h1c-1 2-1 6-1 8v1l1 1s1 0 1 1l-1 2h-3c0-1-1-2-1-3-1-3-1-7 0-9 0-1 2-1 3-1z" class="O"></path><path d="M511 503v-21-46l-1-103 1-34v-24c0-4 1-9 0-13v-11-4c-1-2-1-1-1-2l1-1c0 1 1 2 1 3v12l1 59v57c0 9 1 18 0 28h0l-1-3h0c-1 8 0 16 0 24v53c0 8 0 17-1 26z" class="T"></path><path d="M512 400v-40-1 28c0 4 1 9 0 13h0z" class="E"></path><defs><linearGradient id="X" x1="881.767" y1="149.657" x2="866.936" y2="160.99" xlink:href="#B"><stop offset="0" stop-color="#b3b1a8"></stop><stop offset="1" stop-color="#d5d4ce"></stop></linearGradient></defs><path fill="url(#X)" d="M811 120c2 0 2 0 4 1h0 1c1 1 2 1 3 2 4 3 8 6 11 9l5 7 4 4c0 1 0 1 1 1l9 10 1 1c0 1 5 6 6 7h1 1c1 2 5 3 7 3 1 0 2-1 2-2v-1h-1-1c-1-1-2-1-3-2-1 0-2 0-3-1-1 0-2 0-3-1s-2-1-3-1l1-1c1 0 1 0 2 1 2 1 3 1 5 1h1c1 1 1 0 2 1 1 0 1 0 2 1v-1c-2-1-2-2-4-2h0c1-1 1-1 3-1-1-1-2-1-2-1h-3l-2-1h0l-2-2 1-1h2 1l2 2h2 2 1 2 1c3-1 9-6 11-6v3 2c-1 2-3 3-4 5-2 3-3 6-5 8s-4 2-6 2h-1-1-1c-1 0-1-1-2-1h-1c-1-1-3-2-4-3h-1c-3-2-5-5-8-7-3-3-6-7-9-10-4-5-8-11-13-15h0c-5-4-9-8-14-11z"></path><path d="M822 541h1c-13 22-36 41-60 47l-3 1v-10l7-1c6-1 13-2 17-5 3-1 6-3 9-5 7-4 14-10 20-17 3-3 6-6 9-10z" class="C"></path><path d="M392 160c1 1 2 1 3 2 1 0 2 1 3 3h1c0 1 1 2 1 2v1l-1 1c-1-2-3-3-5-5-1 0-2-1-3-2h0-1c-1 0-1 0-1-1l-3-1h-2-2c-2 0-3-1-4-1h-3c-1-1-2 0-3 0l-1-1h-1l-2-2v-1c1 0 1-1 2-1h1 0l-1-1h0s1-1 2-1h0c1-1 1 0 2-1h1c1-1 1-1 2-1h2l2-1c1 0 2 0 3 1 2-2 4-1 7-1h1 2l1 1c1 0 0-1 1 0s2 1 3 1v-1c3 2 7 6 7 10v4c1 2 1 4 1 6h0c-1-1-1-1-1-2l-1-1 1-1-1-1h0c-1-1-3-2-4-3-2 0-3-1-5-2h0c-1 0-2 0-2-1h-1-1l-1-1h-1-2c0 1 1 1 1 1 1 0 2 1 3 1z" class="E"></path><path d="M392 160v1c-1 1-1 1-2 0-2-1-5-2-8-2-2-1-5 0-8-1s-3 0-5-2c1-1 1-1 3-1h0c1-1 2 0 3-1 5-1 10 0 15 0h1c1 0 0 0 2 1h0l1 1h-2l2 2h1 2l4 2c2 1 4 2 5 4s1 4 1 6h0c-1-1-1-1-1-2l-1-1 1-1-1-1h0c-1-1-3-2-4-3-2 0-3-1-5-2h0c-1 0-2 0-2-1h-1-1l-1-1h-1-2c0 1 1 1 1 1 1 0 2 1 3 1z" class="F"></path><path d="M539 520v1c1 7 1 16 1 24v54 219c-3 1-7 1-10 3s-2 10-2 14l30-1h56l19 1c2 0 6 0 8-1 0 0 1-1 2-1v1h-1c0 1-1 1-2 2h-10-22-81v-10c0-2 1-4 2-5 2-3 7-3 10-4v-82-120l-1-63c0-10 0-22 1-32z" class="E"></path><path d="M375 230c4-1 9-1 13-1h23 72v5 12 59 10 31 107 34 19h-1v-89-67c0-17 1-35-2-52-1-2-1-3-2-5-1-3-2-7-3-10-1-1-2-4-2-5h0c4 8 7 16 9 25 1-7 0-16 0-24v-49h-43-18-37c-4 1-7 0-11 1h-8c-4 1-9 1-13 2-8 2-15 5-22 8l-1-1c6-3 12-5 19-7l8-1c2-1 4-1 6-1 4-1 8-1 13-1z" class="F"></path><path d="M743 145v2c4-4 10-9 16-10h1l-3 1c-1 1-2 1-3 2h2c1 0 1 0 2-1 1 0 2-1 3-1h1c4-2 12-3 16-1-3 0-6 0-9 1h-1c-7 2-20 10-23 17-1 1-1 1-1 2h0l-1 1-1 2s1 1 1 2l-2 2v2c0 1 0-1-1 1h0v1c-1 1-1 0-1 1h1c1 1 2 2 2 3l2 1c0-1 1-2 1-3 2-3 4-7 7-8-3 4-6 8-8 13-1 2-2 5-3 8v1h-7c-1-1-5-1-7-1-1-2-2-4-3-7 1 2 2 4 3 5s3 1 4 1c1-4 1-9 1-13 1-4 3-8 4-12h1c0-3 4-10 6-12z" class="N"></path><path d="M737 157h0c-2 7-6 12-4 19h1c2-3 3-8 6-11-2 5-5 12-5 18h6v1h-7c-1-1-5-1-7-1-1-2-2-4-3-7 1 2 2 4 3 5s3 1 4 1c1-4 1-9 1-13 1-4 3-8 4-12h1z" class="V"></path><path d="M149 431c-2-3-3-6-3-9-1-6 2-11 5-15l1-1 1-1c0-1 1-1 1-1 1 1 0 2-1 3v1 1c-1 2 1 8 2 10 3 4 8 9 13 10 3 1 8 1 12 0 1-1 2-2 4-3 2 0 4-2 7-3h0c2 0 3 0 4-1h1c-2 3 0 0-1 2l-1 1v1l-1 2h-2c-1 0-2 0-3 1s-3 1-5 2c-3 1-8 5-11 5-5 2-13 2-18-1-2 0-4-2-5-4z" class="L"></path><path d="M150 430h0c0-1-1-1-1-2 0-3 0-3 2-6h0l1 2v1 2h-1c-1 2 0 2-1 3z" class="S"></path><path d="M150 430c1-1 0-1 1-3h1v-2-1c1 1 2 2 2 3 5 5 10 8 18 9-5 2-13 2-18-1-2 0-4-2-5-4l1-1z" class="b"></path><path d="M150 430c1-1 0-1 1-3h1v-2-1c1 1 2 2 2 3h0c1 2 3 4 4 6l-4-3-1 1c2 2 4 3 6 4 1 0 1 0 2 1-3 0-4-1-7-1-2 0-4-2-5-4l1-1z" class="F"></path><path d="M563 249c2 0 3 0 4 1h-2c-2 1-2 3-2 5-1 5-1 11-2 17 3-2 6-5 9-8v140 55 22c0 2 0 4-1 6h0V266c-2 2-4 4-7 6h0l-1 2v139 51c0 11-1 22 0 32 0 4 1 7 3 11h1l-1 1c-2-2-2-4-3-7-1-2-1-4-1-6v-26-50-101-51l3-18h0z" class="N"></path><path d="M540 112c5-2 10 0 15 1h3c4 2 7 5 8 9 0 2 0 3-1 4-3 2-9 1-12 1-5-1-8-4-12-5-2-1-4 0-6 0l-3 1h-3c-2 1-3 1-4 2-2 2-4 3-5 4l-1 1s0 1-1 1c0 1 0 1-1 2s-1 3-3 4h0c1-3 2-5 1-7v-1h1l1-2 1-1c2-2 6-7 8-7l7-3 1-1v-1h0l2-1v-1h4z" class="W"></path><path d="M536 112h4l-1 1-6 3 1-1v-1h0l2-1v-1z" class="J"></path><path d="M554 117c3 1 4 2 6 3l1 1h0c-2 1-6-1-9-2v-1h1l1-1z" class="e"></path><path d="M540 112c5-2 10 0 15 1-1 1-1 1-3 0-4-1-8 0-13 0l1-1z" class="Q"></path><path d="M526 119l1 1c2-1 3-2 5-3 3-1 8-2 11-2 4 0 9 0 11 2l-1 1c-8 0-18-1-24 5-2 1-3 1-4 2-2 2-4 3-5 4l-1 1s0 1-1 1c0 1 0 1-1 2s-1 3-3 4h0c1-3 2-5 1-7v-1h1l1-2 1-1c2-2 6-7 8-7z" class="B"></path><path d="M181 471c7-2 10-11 14-16-1 2-1 4-1 6-1 2-1 6 1 8s5 2 8 2c2-1 2-8 4-11 1-2 3-4 6-4 3-1 7-1 10 1 2 1 4 3 6 4 4 2 11 0 15-1h-1c-3 3-6 4-10 3-5 0-13-6-18-2-1 1-2 2-2 3s1 3 1 3c1 1 4 2 5 2-2 1-4 2-6 4v2c2 3 6 3 9 4h6c-3 3-8 5-12 7l-2 1c-4 0-6 1-9-1v-1-1l2-3h1c1-1 1-3 0-4-2-2-5-4-8-5h0c-4-1-7 0-10 0-3 1-6 1-9-1z" class="C"></path><path d="M529 248h11v258h-1V249h-9v207 34c0 5 1 11 0 16h-1V248z" class="N"></path><path d="M752 162c5-4 12-10 14-16 3 3 6 5 10 6 3-4 5-7 10-10v1c5 6 27 18 35 20h1 1l1 1h0v1c-4-1-8-2-13-3-3 0-7 0-10-1l3-4c-6-4-14-9-21-6-1 0-5 3-6 3-2 0-3-1-5-2-3 0-8 4-9 6-1 3 1 6 2 8-2 0-4-1-5-1-3 3-6 6-5 11 0 1 0 3 1 4-2 2-6 4-9 4h-5-1v-1c1-3 2-6 3-8 2-5 5-9 8-13z" class="C"></path><path d="M345 160c1-1 2-2 4-2v-1c2-1 2-1 3 0l1-1c1 0 2 0 3-1l2-1h2c1 1 1 1 1 3v1h1v2h0 1c1 1 1 1 2 1h1c1 0 2 0 3 1 2 0 5-1 6 0h1c1 1 2 1 3 2l1-1 1 1h1 2v1l2 1v2c0 2 1 2 1 4-1 1-1 3 0 4s3 1 4 2h1 4c1 0 1-1 2 0h-1c-1 0-2 1-3 1h-3l-2-1c-2-1-3-1-3-3v-1l-2 2c-2 1-4 0-5 1-1-1-1-3-2-4l1-1c-1-2-3-3-4-3-12-4-25 1-36 6l-8 5c-1 1-1 0-1 1h-2c-2 1-3 1-5 0l7-7c1-1 2-1 3-3 1 0 1 0 2-1 1 0 2-1 3-2l4-3c3-2 7-3 10-6 1 0 1 0 2-1h0-5c-1 1-1 1-2 1l-1 1z" class="E"></path><path d="M817 484h0l-1-2-1-2c-1 0 0-1 0-2 0 1 1 1 1 1 7 2 13 10 21 8 1-1 2-2 2-4 1 0 1-1 1-1 1-1 2-1 3-1l3 1 7 7c0 1 2 2 2 3 1 0 1 1 1 1l-2 2h-1c2 4 9 5 11 9h0-13l-1 1c-2 1-5 1-7 1-3-1-6-6-9-7-2-2-3-3-6-3l-9-10-2-2z" class="T"></path><path d="M817 484h0l-1-2-1-2c-1 0 0-1 0-2 0 1 1 1 1 1 7 2 13 10 21 8 1-1 2-2 2-4 1 0 1-1 1-1 1-1 2-1 3-1l3 1h0c-2 1-3 1-5 1-1 1-1 1-1 2s1 1 2 1c-1 1-3 2-5 2s-4 0-7-1h-2c1 1 1 1 3 1l1 1c1 0 2-1 4 0h4c1-1 2-2 4-2h0l1-1 1 1c0 1 0 1-1 2-4 3-6 3-10 2h-3c-4 0-9-4-13-5l-2-2z" class="E"></path><path d="M832 491v-1h1l1 1 1-1c3 1 6-1 9-2l1 1c-4 3-6 3-10 2h-3z" class="X"></path><path d="M521 664V563v-29-14h1v286c2 0 5 1 7 1V520h1v287c-3 0-6 1-9-1v-10-18-56-17-9-32z" class="N"></path><path d="M521 664v-1c1-5 1-11 1-15h0c0 5 1 12 0 17-1 2 0 4 0 6v17c-1 3 0 6-1 8v-32z" class="S"></path><defs><linearGradient id="Y" x1="622.36" y1="700.86" x2="638.836" y2="745.484" xlink:href="#B"><stop offset="0" stop-color="#2f3133"></stop><stop offset="1" stop-color="#5d6164"></stop></linearGradient></defs><path fill="url(#Y)" d="M612 709h0c-1-6-1-12-1-18l2 3 3 5h1l1 1s1 2 1 3l3 5c3 3 6 6 9 7l1 2h-1c-1 2-1 2-2 2-1 1 0 2 0 3h-1c0 1 1 2 1 3l1 1h1c2 2 4 4 6 5h1c2 1 3 2 5 2h3l1 1h3v1h0 1 1c1 0 1 0 2-1v1c-4 2-8 2-11 3v8c-6 1-14-6-17-10-2-2-5-6-7-7h-1c-3-6-5-13-6-20z"></path><path d="M612 709h0c-1-6-1-12-1-18l2 3 3 5h1l1 1s1 2 1 3l3 5c3 3 6 6 9 7l1 2h-1c-1 2-1 2-2 2-1 1 0 2 0 3h-1c0 1 1 2 1 3h-1c-1-2-2-3-2-4l-8-11c-1-2-2-5-4-6l-1 1h0v3 1h-1z" class="I"></path><path d="M615 703c-1-2-2-4-2-6h0l2 2h1 1l1 1s1 2 1 3h-1s-3-2-3-3v3z" class="G"></path><path d="M622 713c2 0 2 1 3 2l4 4c-1 1 0 2 0 3h-1c0 1 1 2 1 3h-1c-1-2-2-3-2-4 0-3-3-6-4-8z" class="H"></path><path d="M615 703v-3c0 1 3 3 3 3 2 4 4 8 7 11v1c-1-1-1-2-3-2-2-1-6-8-7-10z" class="B"></path><path d="M618 703h1l3 5c3 3 6 6 9 7l1 2h-1c-1 2-1 2-2 2l-4-4v-1c-3-3-5-7-7-11z" class="Z"></path><defs><linearGradient id="Z" x1="756.433" y1="395.121" x2="773.041" y2="391.446" xlink:href="#B"><stop offset="0" stop-color="#9c9892"></stop><stop offset="1" stop-color="#d5d5ce"></stop></linearGradient></defs><path fill="url(#Z)" d="M748 368h1c3 0 9 1 11 0 0-1 1-2 1-3l2-2 2 2c3 1 6 3 7 5h1l2 2c1 1 1 3 1 5v2c0 1 1 1 1 1h1l-1 2-2 2c-1 1-1 3-3 4l2 1c-1 1-1 2-2 4l-1 3c-1 0-1 1-1 2 1 2 1 5 2 7h1l2-1v-1h3l-1 1c-1 2-3 3-5 4 1 0 1-1 2-1s0 1 2 0h1 0c-2 2-5 3-7 4-4 2-7 3-10 4v-39h-15l-1-1 4-7z"></path><path d="M771 407h0l-1 1c-2-1-3 0-4-1v-1-6l1-1 1 1c0 3 0 5 1 7h2z" class="f"></path><path d="M768 400c0-1 0-3 1-4 0-1 1-2 1-3h2l-1 3c-1 0-1 1-1 2 1 2 1 5 2 7h1l2-1c-1 1-1 1-2 1s-2 1-2 2h-2c-1-2-1-4-1-7z" class="N"></path><path d="M765 365c3 1 6 3 7 5v2l-3-2c-2 0-2 0-2 2 0 4 0 7 1 11l-1 1 1 1v2l-1 1h0c-1-2-1-4-1-6l-1-14c-1-1 0-1-1-2l1-1z" class="F"></path><path d="M768 387v-2l-1-1 1-1c-1-4-1-7-1-11 0-2 0-2 2-2l3 2c0 5 0 8-2 12-1 1-2 2-2 3z" class="E"></path><path d="M772 370h1l2 2c1 1 1 3 1 5v2c0 1 1 1 1 1h1l-1 2-2 2c-1 1-1 3-3 4l-1 1-1 1-1-1 1-1v-4c2-4 2-7 2-12v-2z" class="f"></path><path d="M771 389c0-1 3-10 3-11 0 2 0 4 1 6-1 1-1 3-3 4l-1 1z" class="B"></path><path d="M773 370l2 2c1 1 1 3 1 5v2c0 1 1 1 1 1h1l-1 2-2 2c-1-2-1-4-1-6v-4l-1-4z" class="K"></path><path d="M774 374c1 2 2 4 1 7l2 1-2 2c-1-2-1-4-1-6v-4z" class="G"></path><defs><linearGradient id="a" x1="746.44" y1="370.84" x2="756.086" y2="373.71" xlink:href="#B"><stop offset="0" stop-color="#545351"></stop><stop offset="1" stop-color="#7d7b77"></stop></linearGradient></defs><path fill="url(#a)" d="M748 368h1c3 0 9 1 11 0 0-1 1-2 1-3l2-2 2 2-1 1c-2 1-2 3-3 6-1 1-1 2-1 3h-12c-1 0-2 1-4 0l4-7z"></path><defs><linearGradient id="b" x1="407.038" y1="707.415" x2="402.727" y2="745.59" xlink:href="#B"><stop offset="0" stop-color="#3a3e40"></stop><stop offset="1" stop-color="#5e6164"></stop></linearGradient></defs><path fill="url(#b)" d="M414 713c2-4 6-6 8-10 0-1 1-2 2-3v2 1c0 1-1 2-2 3 0 1 0 1 1 3-1 1-2 3-2 4 1-1 3-3 5-4l1 2v-1 1 2 1l1 2-1 5-1 2c-1 2-1 4-2 6h-1c-2 2-4 5-7 7-6 5-11 9-19 11v-9l-10-2c-1 0-3-1-3-2 3-4 11-6 16-8 3-2 6-5 9-7 1-2 2-3 4-5l1-1z"></path><path d="M414 713l1 1-1 1v2l-1 2c-1 2-3 3-5 4h0l5-8v-1l1-1z" class="J"></path><path d="M427 711v-1 1 2 1l1 2-1 5-1 2h-1l-1 3h-1c0-2 1-4 2-6 1-3 1-6 2-9z" class="G"></path><path d="M414 713c2-4 6-6 8-10 0-1 1-2 2-3v2 1c0 1-1 2-2 3 0 1 0 1 1 3-1 1-2 3-2 4-5 6-8 13-15 16 2-2 5-4 7-7l2-2-2-1 1-2v-2l1-1-1-1z" class="B"></path><path d="M414 717l4-4c-1 2-2 4-3 7l-2-1 1-2z" class="Q"></path><path d="M506 100h0v-2c-1-6 1-10 4-14l3-4c3 4 9 11 8 16l-1 4c6-3 12-5 18-7l-1 14-7 3 1 2 3-1 2 1v1l-2 1h0v1l-1 1-7 3c-2 0-6 5-8 7l-1 1-1 2h-1c1-2 3-5 4-7l-1-1c0-2 1-2 1-4 1-2 2-4 4-5-3 0-3 0-4 2v1c-1 1-2 3-4 4 0-1 1-2 2-3h0c-1-1-1-1-2 0h0v3l-1 2v1h0c-1 2-1 4-1 6h0v-8c-2-2-3-5-3-7v-2h0c-1-2-3-4-3-6h2c-1-2-2-3-3-5z" class="W"></path><path d="M518 107c0-2 1-3 1-4 3-1 4-2 6-3 4-2 7-3 12-4l-9 6-1 1c-2-1-3 1-5 1-1 0-3 2-4 3z" class="J"></path><path d="M530 102l2-1c0 1 1 2 0 3-2 4-7 6-10 9l1 1c-1 2-2 2-2 4l1 1-3 3-1-1c0-2 1-2 1-4 1-2 2-4 4-5 2-2 5-5 7-8h1v-1-1h-1z" class="H"></path><path d="M514 102l1-6h1c1 1 0 3 0 4-1 3 0 6-1 9s-2 9-1 12v1h0c-1 2-1 4-1 6h0v-8l1-18z" class="M"></path><path d="M518 107c1-1 3-3 4-3 2 0 3-2 5-1l-5 4c-3 2-4 5-5 8v1c-1-1-1-1-2 0h0v3l-1 2c-1-3 0-9 1-12v4c2 0 2-5 3-6z" class="B"></path><path d="M530 102h1v1 1h-1c-2 3-5 6-7 8-3 0-3 0-4 2v1c-1 1-2 3-4 4 0-1 1-2 2-3h0v-1c1-3 2-6 5-8 2 0 6-4 8-5zm-21 3l2 5-1-14c0-3-1-6 0-8 1 2 2 5 3 8 0 2 0 4 1 6h0l-1 18c-2-2-3-5-3-7v-2h0c-1-2-3-4-3-6h2z" class="D"></path><defs><linearGradient id="c" x1="524.884" y1="124.97" x2="528.263" y2="111.442" xlink:href="#B"><stop offset="0" stop-color="#161a1b"></stop><stop offset="1" stop-color="#39393d"></stop></linearGradient></defs><path fill="url(#c)" d="M523 114c2-1 4-3 7-4l1 2 3-1 2 1v1l-2 1h0v1l-1 1-7 3c-2 0-6 5-8 7l-1 1-1 2h-1c1-2 3-5 4-7l3-3-1-1c0-2 1-2 2-4z"></path><path d="M523 114c2-1 4-3 7-4l1 2c-1 1-2 1-3 2-2 1-4 3-6 5l-1-1c0-2 1-2 2-4z" class="e"></path><defs><linearGradient id="d" x1="613.748" y1="640.08" x2="629.233" y2="666.887" xlink:href="#B"><stop offset="0" stop-color="#3e4245"></stop><stop offset="1" stop-color="#616365"></stop></linearGradient></defs><path fill="url(#d)" d="M587 623l3 3 1 2c1 6 4 12 9 16l3 1h4v1c7 2 16 0 24 0l4-1v1h1c2 2 4 2 5 4 0 1-1 2-3 3l-9 5 6 8c-10 1-18 0-27-4h-2-1l-1-2c-1 0-2-1-3-2v-3c-2-1-3-2-4-4s-3-4-4-6l-1-1c-1 0-1-1-2-1 1 2 1 3 1 5-4-7-4-17-4-25z"></path><defs><linearGradient id="e" x1="601.541" y1="647.016" x2="600.26" y2="655.264" xlink:href="#B"><stop offset="0" stop-color="#26272a"></stop><stop offset="1" stop-color="#3e4245"></stop></linearGradient></defs><path fill="url(#e)" d="M593 645c-1-2-2-3-3-5h1c1 1 2 2 3 4 2 2 2 4 5 5l2 1c3 2 6 5 10 6 2 1 4 2 7 2 2 1 6 1 8 3h0-1c-2 0-5 0-8-1s-6-2-10-3c-1 0-3-1-4-1-1-1-1-1-2-1-2-1-3-2-4-4s-3-4-4-6z"></path><defs><linearGradient id="f" x1="587.72" y1="636.845" x2="602.766" y2="637.216" xlink:href="#B"><stop offset="0" stop-color="#1c1d1f"></stop><stop offset="1" stop-color="#3d3e40"></stop></linearGradient></defs><path fill="url(#f)" d="M587 623l3 3 1 2c1 6 4 12 9 16l3 1v2c1 1 2 2 3 2v1c-2 0-4-2-5 0l-2-1c-3-1-3-3-5-5-1-2-2-3-3-4h-1c1 2 2 3 3 5l-1-1c-1 0-1-1-2-1 1 2 1 3 1 5-4-7-4-17-4-25z"></path><path d="M670 238h2c2 1 9 0 12 0 2 1 8 1 11 1 1-1 6-1 8-1 2 1 9 1 12 1 1 1 2 0 3 0l1 1h2c1 1 3 1 4 2l1 1c1 0 1 1 2 1l1 1c1 1 2 1 3 2 0 1 1 2 2 3v1c1 1 1 2 2 3v1 1l1 1v3c1 1 1 3 1 5 1 1 0 4 0 6l1 1c0 1 0 3-1 4l-3-3-4-5-3-2-2-2h-1l-3-2c-1-1-3-2-4-3-3-2-8-5-12-7l-3-2-4-1-2-1-5-2-6-2h-1c-2-1-1-1-2-1h-2c-1-1-1-1-2-1h-3c-1-1-1-1-2-1h-3c-1-1-1-1-2-1-1-1-2 0-3 0-2-1-2-1-3-1 1-1 6-1 7-1z" class="L"></path><path d="M338 129l-1-1h0v-2l1-1v-2-1h0c1-1 1-2 1-2v-1c1-2 3-4 5-6l3-3c0 3 0 7-1 11 0 3-1 8 0 11l-1 1v2c0 1 0 4-1 5v2c1-2 2-5 2-7h0c0-2 0-3 1-5 0-1 2-3 3-4 3-5 14-11 20-12l1 1-1 2v3l-1 2v2l-1 2v3c-1 2-2 5-4 7-1 0-1 1-2 2-2 2-3 4-5 6-1 1-2 3-3 4h-1-2c-2 3-7 9-11 10-1 0-1-1-2-1-1-3-1-5-1-8 0-1 0 0-1-1v-3-1h-1v-4c1-1 1-2 1-3-1-3-1-7-1-10l1-1c1 2 0 8 0 10l2-7z" class="T"></path><path d="M342 116h1c1 1 1 1 1 2s-1 1-2 2l-1 1h-1c0-3 1-3 2-5z" class="U"></path><path d="M350 139c0-1 2-2 3-3 2-2 4-3 6-3-1 3-3 5-5 7-2 1-3 2-4 2v-3z" class="F"></path><path d="M350 139v3c1 0 2-1 4-2-1 3-2 4-4 5s-4 4-5 5h-1c0-2 0-4 1-5h1c1-1 3-4 4-6z" class="O"></path><path d="M343 148l3-8h1l-2 5c-1 1-1 3-1 5h1c-1 1-1 2-2 3l-4 3v-7l-1-1c-1-2 0-3 0-5l1 1v7c2 0 2 0 3-1s1-1 1-2z" class="a"></path><path d="M341 134c1-3 1-6 2-9 0-1 0-4 1-5l-2 19c0 2-1 6 0 8l1 1c0 1 0 1-1 2s-1 1-3 1v-7l-1-1c0-1 1-3 2-4h0v2h1v-7z" class="V"></path><path d="M338 129v-1l1-2v-2c1 1 0 0 0 1v5c-1 1-1 0-1 1-1 3 0 5 1 7h0c0-2 1-3 2-4v7h-1v-2h0c-1 1-2 3-2 4 0 2-1 3 0 5l-1 1c0-1 0 0-1-1v-3-1h-1v-4c1-1 1-2 1-3-1-3-1-7-1-10l1-1c1 2 0 8 0 10l2-7z" class="F"></path><defs><linearGradient id="g" x1="345.552" y1="154.799" x2="344.429" y2="149.106" xlink:href="#B"><stop offset="0" stop-color="#565555"></stop><stop offset="1" stop-color="#6d6a66"></stop></linearGradient></defs><path fill="url(#g)" d="M359 133h0c-1 5-5 10-8 15-2 3-7 9-11 10-1 0-1-1-2-1-1-3-1-5-1-8l1-1 1 1v7l4-3c1-1 1-2 2-3s3-4 5-5 3-2 4-5c2-2 4-4 5-7z"></path><path d="M347 130c0-1 2-3 3-4 3-5 14-11 20-12l1 1-1 2-5 4-9 6-6 6v1l-3 3h0v-7z" class="S"></path><path d="M407 542c2 0 3 1 5 2 1 0 2 1 3 1h-1-2c0 1 1 1 2 2 1 0 2 1 4 2l5 2 3 2c4 2 6 3 9 6v2l2 2-1 1c4 4 6 7 9 11-2 1-5-4-6-4l-1 1 2 1-1 1c-1-1-2-1-3-2-3-2-6-4-10-6-5-2-12-3-17-1l-1-2c-4 0-6 1-10 2h-4l-3 1c-3 0-5 2-8 3-1-1-1-1-2-1 0-4 4-8 6-11h0l-2-1c0-3 3-5 4-6 6-5 11-6 18-8z" class="M"></path><path d="M421 557c2 1 3 2 5 3 1 0 1 0 2 1l3 2c3 1 4 2 7 5h-1-1c-3-3-6-4-10-6h-2l1-1c-3-1-5-2-7-3h0c1 0 2 0 3-1z" class="B"></path><path d="M417 553h3c2 0 4 1 6 0 4 2 6 3 9 6v2l2 2-1 1c-4-4-11-7-17-9h-4-1 0c3 0 5 1 7 2-1 1-2 1-3 1l1-1c-2-1-4-1-6-1h-1c-3-1-6-1-8-1h0c2-1 5-1 7-1v-1h5 1z" class="D"></path><path d="M404 555c2 0 5 0 8 1h1c2 0 4 0 6 1l-1 1h0c2 1 4 2 7 3l-1 1c-6-1-10 0-16 1-4 0-6 1-10 2h-4l4-4c1 1 1 1 2 1h1c0-1 1-2 1-3h1l-1-1c-1 0-2 1-3 1h-1c1-1 1-1 3-2 0 0 1-1 2-1l1-1z" class="Y"></path><path d="M391 556c2 1 4-1 6-2 4-1 10-2 14-1v1c-2 0-5 0-7 1h0l-1 1c-1 0-2 1-2 1-2 1-2 1-3 2h1c1 0 2-1 3-1l1 1h-1c0 1-1 2-1 3h-1c-1 0-1 0-2-1l-4 4-3 1c-3 0-5 2-8 3-1-1-1-1-2-1 0-4 4-8 6-11l4-1z" class="H"></path><path d="M407 542c2 0 3 1 5 2 1 0 2 1 3 1h-1-2c0 1 1 1 2 2 1 0 2 1 4 2l5 2 3 2c-2 1-4 0-6 0h-3-1-5c-4-1-10 0-14 1-2 1-4 3-6 2l-4 1h0l-2-1c0-3 3-5 4-6 6-5 11-6 18-8z" class="Q"></path><path d="M404 546h7l-1-1-1-1c1 0 1 0 3 1 0 1 1 1 2 2 1 0 2 1 4 2h-3l-4-1c-2-1-4-1-5-1h0 4 0c-1 0-3 0-5-1h-1z" class="J"></path><path d="M407 542c2 0 3 1 5 2 1 0 2 1 3 1h-1-2c-2-1-2-1-3-1l1 1 1 1h-7c-2 0-4 0-6 1h-1 0c-1 1-1 1-2 1l-1 1h-1l-1 1h-1-2c6-5 11-6 18-8z" class="H"></path><path d="M387 557c2-1 4-2 7-4 5-4 10-5 17-5l4 1h3l5 2 3 2c-2 1-4 0-6 0h-3-1-5c-4-1-10 0-14 1-2 1-4 3-6 2l-4 1h0z" class="R"></path><path d="M418 549l5 2 3 2c-2 1-4 0-6 0-1-2-3-3-5-4h3z" class="Y"></path><path d="M391 556c2-1 4-2 6-2 7-3 13-3 20-1h-1-5c-4-1-10 0-14 1-2 1-4 3-6 2z" class="B"></path><path d="M211 381c0-2 0-4 1-6s1-5 3-6c0 2 1 3 2 5 0-2 0-3 1-4 0 3 0 4 1 7l1 1c1 1 3 2 4 4h0l4 3h0c5 3 11 4 16 7 4 2 7 5 10 8 4 3 10 5 15 7 1 0 2 1 3 1l8 3c1 0 2 1 3 1v1s0 1 1 1v1c-3-1-5-1-7-1 4 2 12 4 15 7-3 0-5-1-7-1-3-1-6-2-9-4-5-2-9-5-15-5-2 0-4-1-7-1-2 0-4-1-7 1-1 1-3 1-4 2v-1l1-1c-1 0-1 0-2-1h0-1-3l-4-1-1-1c-2 0-4-1-6-1h0l-1-1s-1 0-2-1h0c-2-1-4-3-5-6h0c-1-1-1-2-2-4h0v-2-1c-1-1-1-1-1-2h0c-2-1-1-1-2-1v2 1 4l-1-1v-2c-1-2-1-4-1-5v-10 1s0 1-1 2h0 0z" class="F"></path><path d="M260 406c3 0 6 1 9 1 1 0 2 1 3 1l8 3c1 0 2 1 3 1v1s0 1 1 1v1c-3-1-5-1-7-1l-15-7-2-1z" class="U"></path><path d="M217 374c0-2 0-3 1-4 0 3 0 4 1 7l1 1c1 1 3 2 4 4h0l4 3h0l-1 1h1c-1 1-1 1-1 2s0 2 1 3h0c1 1 1 1 1 3h1v1c1 2 1 4 3 5h0v1c1 1 4 3 6 3l-3 1-1 3h1c-2 0-2 0-4-1-3-1-7-2-10-5-6-6-6-20-5-28z" class="C"></path><path d="M222 391l3 1 1 3c-1 0-1 1-1 2-1-1-2-4-3-6z" class="O"></path><path d="M225 397c0-1 0-2 1-2 2 1 4 4 7 5v1c1 1 4 3 6 3l-3 1-1 3h1c-2 0-2 0-4-1h1c-3-2-5-4-7-7l-1-3z" class="V"></path><path d="M226 400v-1c1 0 1 0 1-1 2 2 6 5 7 8 1 1 1 1 1 2h1c-2 0-2 0-4-1h1c-3-2-5-4-7-7z" class="a"></path><path d="M220 378c1 1 3 2 4 4h0l4 3h0l-1 1h1c-1 1-1 1-1 2s0 2 1 3h0c1 1 1 1 1 3h1v1c1 2 1 4 3 5h0c-3-1-5-4-7-5l-1-3-3-1-2-13z" class="U"></path><path d="M225 392l-1-8c0 1 1 2 2 3 0 2 0 2 1 3l1 1c1 1 1 1 1 3h1v1c1 2 1 4 3 5h0c-3-1-5-4-7-5l-1-3z" class="S"></path><path d="M228 386h-1l1-1c5 3 11 4 16 7 4 2 7 5 10 8 4 3 10 5 15 7-3 0-6-1-9-1l2 1c-3 1-7 0-10 0-1 0-3-1-4 1h1l-1 1h-2c-4 0-7 0-10-1h-1l1-3 3-1c-2 0-5-2-6-3v-1h0c-2-1-2-3-3-5v-1h-1c0-2 0-2-1-3h0c-1-1-1-2-1-3s0-1 1-2z" class="C"></path><path d="M228 386c6 4 11 6 13 13v2 1h-3c-1-1-2-1-3-2l-5-5v-1h-1c0-2 0-2-1-3h0c-1-1-1-2-1-3s0-1 1-2z" class="E"></path><defs><linearGradient id="h" x1="244.13" y1="409.873" x2="255.631" y2="401.306" xlink:href="#B"><stop offset="0" stop-color="#a5a19f"></stop><stop offset="1" stop-color="#cccdc4"></stop></linearGradient></defs><path fill="url(#h)" d="M230 395l5 5c1 1 2 1 3 2h0c7 2 15 2 22 4l2 1c-3 1-7 0-10 0-1 0-3-1-4 1h1l-1 1h-2c-4 0-7 0-10-1h-1l1-3 3-1c-2 0-5-2-6-3v-1h0c-2-1-2-3-3-5z"></path><path d="M246 409c-2-1-4-1-6-1v-1h1c2-1 10-1 11 0-1 0-3-1-4 1h1l-1 1h-2z" class="X"></path><path d="M266 137c-2 0-4 0-5 1v-1c5-1 9-2 15-1h1 1 0c2 0 2 0 3 1h1 1 1v1h1 0c1 1 1 0 2 1 2 0 4 2 6 3h2c1 0 0 0 1-1-3-3-7-5-10-7l-3-2-5-3-2-1h-2c0-1-1-1-1-1l-3-1-2-1h-3c-1-1-2-1-3-1h-1c-2 0-4 0-5-1-2 0-4 0-6 1h-1c-4 0-9 2-12 4l-1-1 6-2c3-2 7-2 11-3h0 8c1 1 3 0 4 1l1-1 1 1h1 0l1 1h2l2 1 3 1 3 1 2 1 1 1h1 0c1 1 1 1 2 1 0 0 1 1 2 1 1 2 5 5 7 6h0c1 1 2 1 3 2 4 4 8 9 10 14l2 4v1l1 1v1h0v1l1 1v1 1 1l1 1v1 2 2 2 2 2h1c1-1 0-2 1-3 0-1-1-2 0-3 0-2 1-5 0-8v-1h0c0-2 0-6-1-7v-4-1-3c-1-1-1-4-1-4v-1c-1-1-1-1-1-2l-1-1c-1-2 0-3-1-5-3-7-7-13-13-17-4-3-7-4-10-5l-6-2-3-1-4-1-9-2c-2 0-14 0-15 1-2 0-3-1-4 0-2 0-2 0-3 1h-3c-2 0-2 0-2 1h-2c-2 0-1 0-2 1h-4c-2 1-5 1-7 1l-1-1c2-1 5 0 7-1h3l2-1h2c1-1 2-1 3-1h2c1-1 2-1 3-1h3c5-2 11-2 17-1 2 1 3 0 6 1h1c1 0 2 1 4 1h0c14 3 26 9 33 21 3 4 5 8 7 13v1 1l1 1h0v2c1 1 0 1 1 2l-1 1h0c1 1 1 1 1 2v1 1c0 1 0 4 1 6v5 4 1 1h1 1 0c1 0 1 0 1-1s0-1 1-2v-3-4c1-4 1-8 0-11v-2-4h0c-1-1-1-2-1-2 0-2 0-2-1-2v-1-2l-1-2c0-1-1-1-1-1-1-3-2-4-2-6-1-3-3-6-4-8l-1-1-3-4v-1c2 1 3 4 5 4 2 3 5 6 6 10l2 5v1c1 1 1 1 1 3l1 1v2c0 1 1 2 1 2v1 3h0c2 4 0 10 1 15 0 1 0 2-1 3v2c1 0 1-1 2-1s4 1 4-2c0-2 1-9-1-10v-1c0-1 1-3 0-3v-2-2-1l-1-1c0-1 1-1 0-2v-2c-1-2-2-4-2-7 0-1-1-1-1-3l-1-2-1-3-1-2v-1c1 1 1 2 2 3 0 1 1 1 2 2v1l2 4c1 1 0 0 0 1l1 2c0 1 0 2 1 3v1 2c1 0 1 1 1 2 1 1 0 0 1 2h0v2l1-3h0c1 2 1 3 1 5l-1 1h0l-1 1v11 4l-1 4 1 1 3-4c2-1 3-2 6-3 1-1 4-3 5-2l1-1c1 0 1 0 2-1h5 0c-1 1-1 1-2 1-3 3-7 4-10 6l-4 3c-1 1-2 2-3 2-1 1-1 1-2 1-1 2-2 2-3 3l-7 7c-4 2-9 4-12 7v-2l-2-2-1-1h-1c-1 1-1 1-2 1v-2l1 1c0-1 0-1 1-2h0v-3-1-1c-1-1-2-3-2-5 0-1 0-2-1-4-1-3-2-6-4-9-1-2-3-3-3-5 2 1 6 8 7 11l1 1h0c0-3-2-6-3-9-5-8-14-16-24-19-3-1-9-1-11 0z" class="N"></path><path d="M331 171c0-1 1-2 2-3 2-2 7-7 11-7h2 1l1-1c1 0 2 0 3-1-3 3-7 4-10 6l-4 3c-1 1-2 2-3 2-1 1-1 1-2 1h-1z" class="X"></path><path d="M307 183l2 1h1c1 0 2 0 3-1l1-1 2-2v-1h2l3-8v-1c1-7 2-18 1-25-1-4-2-7-3-10l1-1c1 3 2 6 2 9 1 4 1 9 1 12-1 4 0 8-1 11l-1 8c0 1-1 2-1 3h1c2-2 4-5 7-7h0c1-5 1-10 1-15 0-2 1-4 0-6v-5c-1-1-1-2-1-4v-2h0l1 4c1 2 1 5 1 8 1 6 1 14-1 20 0 1 0 1 1 2l1-1h1c-1 2-2 2-3 3l-7 7c-4 2-9 4-12 7v-2l-2-2-1-1z" class="c"></path><path d="M386 462h0c3-6 6-11 12-14 2 1 3 1 5 1 1 0 2 1 3 1l7 2h0c2 0 4 1 6 1 1 0 3 1 3 2l1 1c3 3 6 8 6 13 0 3-1 6-1 10-2 1-3 2-5 3-11 1-23 2-32 8-3 2-5 5-7 7l-1 1c-1 0-2 1-2 2l-1 1 1-3-1 1c-1 1-1 1-1 2l-1 1v1h-1v-1c1-2 1-3 1-5v-1l5-19 1-5c0-2 2-3 2-5l1-3c-1 0-1 0-1-1v-1z" class="R"></path><path d="M413 452c2 0 4 1 6 1 1 0 3 1 3 2l1 1v2 1 1h0c0 1 1 2 1 2 2 5 2 9 0 13 0 2 0 2-1 2h-1c-2 0-6 1-9 1-1 0-2 0-3-1l1-1c3 0 4 0 7-2h0 1 2 1 1l-1 1 1 1s0-1 1-2v-1c0-2 1-5 0-7l-1-3v-2c-2-4-6-7-10-9z" class="Q"></path><path d="M423 456c3 3 6 8 6 13 0 3-1 6-1 10-2 1-3 2-5 3l-1-2-1-1c-1-1-6-1-7-1h-1c3 0 7-1 9-1h1c1 0 1 0 1-2 2-4 2-8 0-13 0 0-1-1-1-2h0v-1-1-2z" class="G"></path><path d="M381 498c1-6 4-13 8-18s8-5 13-5c4-1 8-1 10-4 2-2 3-5 3-7-1-3-2-5-4-7-1 0-1 0-1-1h1c2 1 5 4 6 6s1 5 0 7l-1 1c-1 2-3 4-5 6l-1 1c1 1 2 1 3 1h1c1 0 6 0 7 1l1 1 1 2c-11 1-23 2-32 8-3 2-5 5-7 7l-1 1c-1 0-2 1-2 2l-1 1 1-3z" class="C"></path><path d="M206 436h1c0-2 1-3 0-4v-1-2c-1-1-1-1-1-2v-1-3l1 2v1c0 1 1 3 1 4 1 2 0 3 1 5l4-5h0l1-1v-1h2c1 0 2-1 3-1-1 1-1 3-1 4-2 1-4 2-4 3v1c2 0 4 0 6 1v-2h4c5 2 8 8 12 11h3c2 2 4 5 5 8 4-7 10-11 16-14h2l-1 1h0 0c-1 1-2 1-2 2h-1l-4 4c-2 3-4 4-4 8l-3 3-3 3c-4 1-11 3-15 1-2-1-4-3-6-4-3-2-7-2-10-1-3 0-5 2-6 4-2 3-2 10-4 11-3 0-6 0-8-2s-2-6-1-8c0-2 0-4 1-6-4 5-7 14-14 16-3-2-8 0-11-2 1-7 4-14 7-20 2-4 5-6 8-8 1-1 4-3 6-3 1 0 2 0 2-1l7-1 3-1c1 0 2 0 3 1h0z" class="T"></path><path d="M223 442l-2-2h0c7 4 13 8 20 12l-5-7h3c2 2 4 5 5 8h0v3c-1 0-2 1-3 1-2 0-4-1-5-2v-1c1 1 2 1 3 1l3 2-5-5c-4-4-10-7-14-10z" class="f"></path><path d="M214 435c2 0 4 0 6 1v-2h4c5 2 8 8 12 11l5 7c-7-4-13-8-20-12-1-2-3-3-6-4h0l-1-1z" class="L"></path><path d="M206 436h1c0-2 1-3 0-4v-1-2c-1-1-1-1-1-2v-1-3l1 2v1c0 1 1 3 1 4 1 2 0 3 1 5l4-5h0l1-1v-1h2c1 0 2-1 3-1-1 1-1 3-1 4-2 1-4 2-4 3v1l1 1h0c3 1 5 2 6 4h0l2 2c-4 1-9-2-13-3-5 0-9 1-14 3l-1-1c2-1 4-2 6-2l1-1h1l1-1 1 1v-1l-5-1 3-1c1 0 2 0 3 1h0z" class="F"></path><path d="M213 430h0l1-1v-1h2v2h0c-1 2-4 4-6 5h-1l4-5z" class="U"></path><path d="M656 145v-2c0-2 0-4 2-6h6c0 2 1 4 3 6h0c1 1 2 2 3 2 1 1 2 3 3 4 3 3 7 4 11 6l1 1s1 0 2 1h-2c10 5 23 10 30 20v1l-1 1-4-1 4 2 1 1c2 1 9 2 12 2 2 0 6 0 7 1 0 1 0 3-1 4 0-1 0-2-1-3-2-1-5-1-8-1-5-1-10-1-15-1-4 0-5 2-8 4v-1h1c0-1 1-2 1-2h1l2-2h2v-1c-1 0-2-1-3-1l-2 1v1c-3 1-3 1-5 3h0v-1l1-1 1-1v-1h-2v1l-1-1c-3-1-7 0-11 0-1-1-3-1-5-1-4-1-7-3-11-5-2-2-5-3-8-3s-6 1-8 2c-4 2-5 5-11 5h-1c-4 0-14-1-16-4h-1c-3-4-4-9-4-13 1-4 3-6 5-8 1-2 3-4 5-5h0l1-1c8-4 14-4 23-2l1-1z" class="L"></path><path d="M642 148c2 0 2 0 4 1h-1c-2 2-5 1-8 2h0-3v-1c3-2 5-2 8-2z" class="N"></path><path d="M656 145v-2c0-2 0-4 2-6h6c0 2 1 4 3 6h0c1 1 2 2 3 2 1 1 2 3 3 4 3 3 7 4 11 6l1 1s1 0 2 1h-2c-2 1-4-2-7-2-4 0-10-5-14-7h-2c-2-1-4-3-6-3z" class="E"></path><path d="M664 148l-2-1c1-1 1 0 2 0l1-1-5-4 1-1c3 3 6 6 10 8h2c3 3 7 4 11 6l1 1s1 0 2 1h-2c-2 1-4-2-7-2-4 0-10-5-14-7z" class="X"></path><path d="M630 153h0c1 0 2 0 3-1 1 0 4 0 6 1h4l1-1h10c1 0 1 0 2 1h1c1 0 0 0 2 1h0c1 0 1 1 2 1l1 1v1h-1c-1 0-3 0-4-1h-13c-1 0-3 0-4 1h-2-1l-7 3c-1 0-2 1-3 1s-2 1-2 1h0l-3 2h0l-1-2c1-4 3-6 5-8l4-1z" class="F"></path><path d="M626 154l4-1s1 1 2 1h6c-3 1-7 3-9 5h-1c-1 0-2 0-3 1v2h0l-3 2h0l-1-2c1-4 3-6 5-8z" class="E"></path><defs><linearGradient id="i" x1="704.77" y1="171.055" x2="665.759" y2="168.928" xlink:href="#B"><stop offset="0" stop-color="#706e6b"></stop><stop offset="1" stop-color="#949392"></stop></linearGradient></defs><path fill="url(#i)" d="M660 166h-5l-2 1h-1c2-1 3-2 4-2s2-1 4-1c18-4 35 5 50 14l4 2 1 1c2 1 9 2 12 2 2 0 6 0 7 1 0 1 0 3-1 4 0-1 0-2-1-3-2-1-5-1-8-1-5-1-10-1-15-1l2-1h0c-6-3-11-7-17-9-10-5-23-8-34-7z"></path><path d="M660 166c11-1 24 2 34 7 6 2 11 6 17 9h0l-2 1c-4 0-5 2-8 4v-1h1c0-1 1-2 1-2h1l2-2h2v-1c-1 0-2-1-3-1l-2 1v1c-3 1-3 1-5 3h0v-1l1-1 1-1v-1h-2v1l-1-1c-3-1-7 0-11 0-1-1-3-1-5-1-4-1-7-3-11-5-2-2-5-3-8-3s-6 1-8 2c-4 2-5 5-11 5v-6c1 0 2 2 4 3 2-2 2-3 3-5 2-3 6-4 10-5z" class="C"></path><defs><linearGradient id="j" x1="365.309" y1="814.76" x2="380.194" y2="763.32" xlink:href="#B"><stop offset="0" stop-color="#0a0b0b"></stop><stop offset="1" stop-color="#575c5f"></stop></linearGradient></defs><path fill="url(#j)" d="M359 761c7-3 17-3 23 0 3 2 6 3 8 6 0 1-1 1-1 2 4 2 8 6 9 10 1 3 2 10 0 13l-1-1c-2 2-4 4-7 5v1 1c2 2 6 2 9 1 7 0 16-3 20-8l1 1c-2 4-6 8-11 10l-11 3v1h-3l1 1c3 0 8-1 11-2h1v1c-9 3-20 6-29 3s-14-10-18-18c-1-1-1-3-2-4v3c1 2 2 4 3 7s3 5 5 8h0c1 2 3 3 5 5 1 1 3 2 4 3 4 0 7 0 11 1h-14c-5 0-11 0-17-1-4-4-8-9-10-15-3-7-4-16-1-23s8-12 14-14z"></path><path d="M357 786l1-4v-2h1v5h1c1 1 0 1 0 2s1 3 1 4c-1-1-1-3-2-4v3 1c-1-2-1-3-1-4l-1-1z" class="G"></path><path d="M357 786l1 1c0 1 0 2 1 4v-1c1 2 2 4 3 7s3 5 5 8h0c0 2 1 3 3 4 0 1 1 2 2 3-2-1-5-2-7-4l-2-2h-1c-2-3-5-7-6-10 1 0 1 1 2 2v-3c-1-1 0-3 0-4-1-2-1-3-1-5z" class="K"></path><path d="M351 782c-1-2-1-2 0-4l1-1v2 3h0l1-1h0c1 1 1 2 1 3h-1c-1 1-1 3-1 4h1c1 3 1 6 3 8 1 3 4 7 6 10l-1 1c0 1 1 1 0 1-2 0-2-1-3-2s-2-3-2-4c-2-2-3-4-4-6 0-1-1-2-1-3-1-1 0-3 0-5h-2l2-6z" class="P"></path><path d="M351 782c-1-2-1-2 0-4l1-1v2 3h0l1-1h0c1 1 1 2 1 3h-1c-1 1-1 3-1 4 1 6 4 10 5 15-2-2-4-6-5-9-2-4-1-7-1-12h0z" class="B"></path><path d="M359 761c7-3 17-3 23 0 3 2 6 3 8 6 0 1-1 1-1 2h-4l1 1h1l-1 1c-4-1-6 0-9 0h-1c1-2 2-2 3-3 0-1 1-1 2-1h0c1 0 2-1 3 0 1 0 2 0 3-1l-1-1h-3-1 0c-1 0-2-1-3 0h-3c-1 0-2-1-2-1l-1-2h-3s0-1-1-1h-2 0l-2-1c-2 1-3 1-5 1h-1z" class="R"></path><path d="M377 771c3 0 5-1 9 0l2 2v1l-1-1c-3-1-6-1-8 0-4 1-7 3-8 6s-1 7 0 9c1 4 4 6 7 7 4 2 8 2 12 1v1 1c2 2 6 2 9 1 7 0 16-3 20-8l1 1c-2 4-6 8-11 10l-11 3c-4 0-8 0-12-1-7-2-14-6-18-12-2-4-2-8-1-12 2-5 6-7 10-9z" class="C"></path><path d="M389 769c4 2 8 6 9 10 1 3 2 10 0 13l-1-1c-2 2-4 4-7 5-4 1-8 1-12-1-3-1-6-3-7-7-1-2-1-6 0-9s4-5 8-6c2-1 5-1 8 0l1 1v-1l-2-2 1-1h-1l-1-1h4z" class="W"></path><path d="M267 517c3 1 8 2 11 0 1-1 2-3 2-4 1-2 1-4-1-5-1-1-3-2-4-2l2-8c0-2 1-4 3-6 2-5 5-9 10-13 7-4 17-6 26-4 8 2 15 8 19 15 3 5 4 9 5 14 3 14-2 30-10 41-8 12-21 20-35 22h-5v-1c6-1 12-3 18-6 13-6 21-17 26-30 3-10 3-20-2-29s-13-15-23-18c-6-2-15-4-21-1v1l4 4h1 0c-1 1-1 1-2 1-4 2-9 5-11 8-1 2-1 3 0 4 2 3 4 4 7 4 1 0 1-1 2-2 0 4 1 7 4 10l3 1c1-3 2-6 4-8h0c5-2 10-2 15 1 5 2 9 7 11 12v2 1c1 7 0 13-2 19v-4c-1-2-1-4-2-5-2-1-3-1-4-1-3 4-4 10-6 14-4 7-12 12-19 14-16 5-30 2-45-6-7-4-13-9-19-15-6-7-12-14-14-22-3-12 4-17 9-26h0c3-3 5-7 7-10h0c-1 5-4 9-5 14-5 10-6 24-1 35 6 11 19 21 31 24 8 2 16 2 23-2 2 2 9 0 10 1 1 2 2 2 4 3 4 0 7-2 9-5 4-4 5-11 6-16 0-2 2-3 3-4 3-3 7-7 5-12-4-3-12 1-17 1-4 0-8-5-12-4-1 0-3 2-3 4-1 1-1 3 0 4 1 3 2 4 5 6 1 0 3 0 4-1 3-1 17-9 19-8-9 6-20 10-31 14-5 1-11 3-16 2l-7-2c7 0 16 1 20-5 1-1 1-2 1-3-1-2-2-3-4-4-3-1-6-2-8-4z" class="C"></path><path d="M442 652l4-3h0v1h2l-1 2v1c-6 13-14 24-22 35v1l-1 1-1 1s0 1-1 1c0 1 0 1-1 2v1h1v-1l2-2 3-3 1-1h0c1 0 1 1 1 2h-1v2c-1 1-2 3-2 4l-2 4c-1 1-2 2-2 3-2 4-6 6-8 10l-1 1c-2 2-3 3-4 5l-2-3c-5 0-9 2-13 2-2 1-5 1-6-1-2-1-4-1-6-1-3 0-6-1-9-2-4-1-8-4-12-6-3-2-7-6-7-9l1-1h3 0l2-3c-1-1-1 0-1-1l-1-4-1-3-1-3v-3c-1-2-1-4-1-6l-1-1h-1c0-2 1-3 1-4h2c2 0 2 1 3 3 0 1 0 1 1 1h1c0-1 1-1 2-2v-1-4l6-3c5-1 9-1 13 1l1 1c2 3 6 5 8 8s2 8 3 11c2 1 3 2 4 3h4l2-1v-1-1c1 1 2 1 3 1h1l1-1c6-2 9-5 13-9 3-2 6-4 6-8h-1l7-6 3-2h0c2-1 5-4 6-6l-1-1v-1z" class="W"></path><path d="M402 688v1c-3 2-4 1-7 1-3-3-9-7-11-11l1-1 7 7h2c2 1 3 2 4 3h4z" class="D"></path><path d="M384 689h2c1 1 1 1 3 2 1 0 4 1 6 1h4c1 1 2 1 4 1h1v1c-3 1-7 2-10 1h-1c-4-1-7-3-9-6z" class="H"></path><path d="M437 660c0 1 0 2-1 4 0 1-1 2-2 3-3 5-8 9-12 13-4 3-10 5-14 6l1-1c6-2 9-5 13-9 3-2 6-4 6-8h-1l7-6 3-2z" class="B"></path><path d="M363 702s0-1 1-1l3 3v1c3 1 6 3 8 4l4 1c3 1 8 1 11 0l9-2c6-1 16-7 20-12 1-2 3-6 5-7l1-1v1l-1 1-1 1s0 1-1 1c0 1 0 1-1 2v1h1c-1 2-1 3-3 5-3 2-7 6-11 6l-6 3h-5c-2 1-4 1-5 2-3 1-5 1-8 1h-2-2v-1h-2l-1-1h-2-1c-2-1-4-2-6-4-2 0-4-2-5-4z" class="D"></path><path d="M382 665l1 1c-3 2-5 4-6 8-2 5 0 11 3 15 3 5 7 8 12 10 8 1 15-1 22-4-5 4-11 7-17 8-4 1-8 1-12 0-4-3-9-6-11-10-1-2-2-3-2-5l-1-1c-1-4-1-8 0-12h0c1-3 2-5 4-7s4-2 7-3z" class="C"></path><defs><linearGradient id="k" x1="389.469" y1="689.765" x2="361.519" y2="672.702" xlink:href="#B"><stop offset="0" stop-color="#141514"></stop><stop offset="1" stop-color="#494d51"></stop></linearGradient></defs><path fill="url(#k)" d="M363 667l6-3c5-1 9-1 13 1-3 1-5 1-7 3s-3 4-4 7h0c-1 4-1 8 0 12l1 1c0 2 1 3 2 5 2 4 7 7 11 10 4 1 8 1 12 0-2 2-4 3-7 3s-6 0-9-1c-6-1-11-5-14-10-2-4-6-15-5-20 0 0 1-1 1-2v-1-1-4z"></path><path d="M390 706v-1c-3 0-9 0-12-2h0c-3-1-5-3-7-5h0c1 0 2 1 3 1 1 1 3 2 4 2 2 1 3 2 6 2h1c4 1 8 1 12 0-2 2-4 3-7 3z" class="G"></path><defs><linearGradient id="l" x1="401.268" y1="677.901" x2="377.381" y2="716.731" xlink:href="#B"><stop offset="0" stop-color="#3d3f40"></stop><stop offset="1" stop-color="#535759"></stop></linearGradient></defs><path fill="url(#l)" d="M422 694l2-2 3-3 1-1h0c1 0 1 1 1 2h-1v2c-1 1-2 3-2 4l-2 4c-1 1-2 2-2 3-2 4-6 6-8 10l-1 1c-2 2-3 3-4 5l-2-3c-5 0-9 2-13 2-2 1-5 1-6-1-2-1-4-1-6-1-3 0-6-1-9-2-4-1-8-4-12-6-3-2-7-6-7-9l1-1h3 0l2-3v1 3l3 3c1 2 3 4 5 4 2 2 4 3 6 4h1 2l1 1h2v1h2 2c3 0 5 0 8-1 1-1 3-1 5-2h5l6-3c4 0 8-4 11-6 2-2 2-3 3-5v-1z"></path><path d="M422 694l2-2 3-3 1-1h0c1 0 1 1 1 2h-1v2c-1 1-2 3-2 4l-2 4c-1 1-2 2-2 3-2 4-6 6-8 10l-1 1c-2 2-3 3-4 5l-2-3-2-1c2-1 4-2 6-4 3-2 7-5 9-8 4-4 5-8 7-13l-4 4h-1z" class="I"></path><path d="M591 648c0-2 0-3-1-5 1 0 1 1 2 1l1 1c1 2 3 4 4 6s2 3 4 4v3c1 1 2 2 3 2l1 2h1l14 11c2 4 6 5 11 7h2c2 1 9 1 10 2l-3 5v1l2-1c1 0 3-1 4-2 1-4 1-10 4-13 2-1 5-4 7-4v-1-1l2-1c4-2 8-2 12-1 4 2 7 4 10 8l1-1c2-1 2-2 3-2 3 3 2 10 2 13-1 6-3 10-5 16h3l1 1c-4 8-13 13-21 15-3 1-6 2-9 2h-4l-1 1v1c-5 1-12 0-17-1h0-2l-1-2c-3-1-6-4-9-7l-3-5c0-1-1-3-1-3l-1-1c-1 0-1-1-1-2l-1-1v-2l-3-12c-9-11-16-21-21-34z" class="H"></path><path d="M649 708h3c1-1 2-1 3-1 3 0 7-1 10-2 1 0 1 0 2 1l-3 2c-5 1-10 0-15 0z" class="D"></path><path d="M646 685v-1c3-1 5-4 7-6l1 1c-1 3-3 5-6 7-1 1-4 2-6 2v-1c1 0 3-1 4-2z" class="K"></path><path d="M673 689v2c0 1-1 1-1 2 1-1 2-1 3-2-2 4-8 12-13 13-3 1-5 1-7 0l4-2h2c5-4 8-8 12-13z" class="J"></path><defs><linearGradient id="m" x1="665.151" y1="679.854" x2="679.672" y2="672.289" xlink:href="#B"><stop offset="0" stop-color="#292b2e"></stop><stop offset="1" stop-color="#5e6062"></stop></linearGradient></defs><path fill="url(#m)" d="M659 665c4-2 8-2 12-1 4 2 7 4 10 8 0 4-2 8-1 11 0-1 1-3 2-4 0 2 0 4-1 7v2c-1 0-1 0-1-1h-2v-3 1c-1 1-1 2-2 4 0 0 0 1-1 2s-2 1-3 2c0-1 1-1 1-2v-2h0c0-2 1-4 1-6 0-4 0-7-3-10l-2-3c-1-1-3-4-4-5h-6z"></path><path d="M659 665h6c1 1 3 4 4 5l2 3c3 3 3 6 3 10 0 2-1 4-1 6h0c-4 5-7 9-12 13h-2l-4 2c-10 1-19-2-27-8l6 2c5 2 14 1 19-2s8-8 10-14c1-4 1-8-1-12-2-2-3-3-5-3v-1l2-1z" class="C"></path><path d="M666 693l2-4c1-4 1-10 0-15-1-3-3-5-5-8l7 7h1c3 3 3 6 3 10 0 2-1 4-1 6h0c-4 5-7 9-12 13h-2c3-2 5-6 7-9z" class="J"></path><path d="M671 673c3 3 3 6 3 10 0 2-1 4-1 6h0c-4 5-7 9-12 13h-2c3-2 5-6 7-9l1 1c2-2 4-4 5-6 2-6 1-10-2-15h1z" class="K"></path><path d="M682 671c2-1 2-2 3-2 3 3 2 10 2 13-1 6-3 10-5 16h3l1 1c-4 8-13 13-21 15-3 1-6 2-9 2h-4l-1 1v1c-5 1-12 0-17-1h0-2l-1-2c-3-1-6-4-9-7l-3-5c0-1-1-3-1-3l-1-1c-1 0-1-1-1-2l-1-1v-2l-3-12c1 2 3 4 4 6l9 12c2 2 2 4 5 5v-2c2 0 2 1 4 1v-1c5 3 9 4 15 5 5 0 10 1 15 0h0c0 1-1 1-2 2h-1-2c-1 0 0 0-1 1h1c0 1 4 0 5 0l1-1c2 0 3-1 5-2s6-5 7-7l1-3c1-1 1-2 1-3 2-3 3-7 4-10l1-7c1-2 1-3 0-5 0-2-1-2-2-2z" class="R"></path><path d="M682 698h3v1l-1 1h0l-2 2h-1l-1 1v-1c0-1 1-3 2-4z" class="Y"></path><defs><linearGradient id="n" x1="611.392" y1="696.278" x2="636.516" y2="701.193" xlink:href="#B"><stop offset="0" stop-color="#232528"></stop><stop offset="1" stop-color="#4c4e4f"></stop></linearGradient></defs><path fill="url(#n)" d="M612 682c1 2 3 4 4 6l9 12c2 2 2 4 5 5h1v2c-1 0-2-1-3-2l-1 1c3 2 7 4 10 6l1 1h-2v1h-2l1 2-1 1h-2l-1-2c-3-1-6-4-9-7l-3-5c0-1-1-3-1-3l-1-1c-1 0-1-1-1-2l-1-1v-2l-3-12z"></path><path d="M622 708l1-2c1 1 3 2 4 3l7 5 1 2-1 1h-2l-1-2c-3-1-6-4-9-7z" class="D"></path><path d="M291 455c5 0 9 0 13 1l6 1c4 1 8 3 12 5 1 1 3 2 5 3l1 1 1 1c2 1 3 3 4 4 2 0 5 3 5 5 1 1 3 2 3 4l1 1c1 0 1 1 2 2h0c1 1 1 2 1 3 1 1 2 1 2 3 1 1 2 3 2 5 0 1 1 2 1 2v1 3l1 1c0 1-1 3 0 5 0 1 1 3 0 5 0 1 0 5-1 6v3c0 1 0 1-1 2v2c0 1 0 1-1 2v1c0 1-1 3-2 4 0 1 0 2-1 3l-2 4c0 1-1 3-2 4l-1 1c0 1-1 2-2 3l-1 1-1 1c-1 2-2 2-3 3 0 2-1 2-3 3 0 1-1 1-2 2l-1 1c-1 0-1 1-2 1l-2 2-5 2-1 1c-1 0-2 1-3 1l-4 2h-2-1l-2 1h-2l-3 1h-2l-1 1h-5c-1 0-2-2-2-2h5c14-2 27-10 35-22 8-11 13-27 10-41-1-5-2-9-5-14-4-7-11-13-19-15-9-2-19 0-26 4-5 4-8 8-10 13-2 2-3 4-3 6l-2 8c1 0 3 1 4 2 2 1 2 3 1 5 0 1-1 3-2 4-3 2-8 1-11 0-1 0-2-1-2-1-2-1 0 1-2-1s-4-3-6-5c0-1-2-2-3-3s-2-3-3-4l-2-4c-1-6 1-17 4-22 6-10 15-14 26-18 2-1 3-2 5-2v-1h0 0c2-1 5-1 7-1z" class="T"></path><path d="M304 467h6 1c1 1 2 1 2 1-2 1-4 1-7 1-4 0-10 0-14 2l-1 1c-1 0-2 0-2 1h-1c-1 0-2 1-3 1-1 2-3 3-5 4l-3 3c-1 1-1 2-2 2h-1l1-2c2-2 4-5 7-6 1-2 4-3 6-4 5-2 10-4 16-4z" class="L"></path><path d="M291 455c5 0 9 0 13 1l6 1c4 1 8 3 12 5 1 1 3 2 5 3-3 0-6-3-9-4-1 0-3 1-5 0-7-3-17-3-25-3l-9 1c2-1 3-2 5-2v-1h0 0c2-1 5-1 7-1z" class="O"></path><path d="M288 458c6-1 15-2 20 0 1 0 3 0 4 1l6 2c-1 0-3 1-5 0-7-3-17-3-25-3z" class="F"></path><path d="M279 464h1c-1 1-3 2-4 3-5 4-10 10-11 17-1 6 1 10 5 14-4 1-7 1-10 1l-1-1c-3-1-5-3-6-6-1-4 1-9 3-13 5-8 14-13 23-15z" class="C"></path><path d="M788 424l13 2c0-1 1-1 1-2 11 2 21 5 31 9l1 1 1 1v1h1 2v1c1-1 2-1 3-1h1c1-1 2-3 4-3 1 2 4 2 6 3s5 2 7 4c8 6 14 17 15 27l1 2c-3 1-8 0-10 2h-1 0c-3 1-5 1-7 1-4 0-11-1-14 0-1 1-3 2-4 2l-3 3v2c1 2 1 2 3 2l1 1s0 1-1 1c0 2-1 3-2 4-8 2-14-6-21-8 0 0-1 0-1-1 2 0 4 1 7 0 3 0 8 0 10-3v-1c-1-2-4-4-6-5 2-1 4-1 6-2 0-2 0-3-1-5-6-7-14 3-22 1h0c-3 0-5-1-7-3-9-8-17-13-28-16l-7-2h-1c-2 0-4 0-6-1v-14l8-1 5-1h2l5-1h8z" class="T"></path><path d="M838 437c1-1 2-1 3-1h1c1-1 2-3 4-3 1 2 4 2 6 3-1 0-1 1-3 0-2 0-4 0-5 2h-1 2s1 1 2 1h1l2 2h0l-3-1c-4-1-8-1-13-1 0-1 1-1 1-2l2 1 1-1z" class="c"></path><path d="M834 434l1 1v1h1 2v1l-1 1-2-1c0 1-1 1-1 2-8 2-13 6-20 10 0-1 1-2 2-3h1c0-4 5-8 7-11 3 1 7 0 10-1z" class="X"></path><path d="M802 453c2-2 4-5 5-7 2-2 2-1 4-1 0 0 1-2 2-2 3-3 6-9 11-10h1l-1 2c-2 3-7 7-7 11h-1c-1 1-2 2-2 3-1 1-3 2-5 3l-4 4 5-1 5-2 1 1c-3 2-9 3-13 2-1-1-1-2-1-3z" class="E"></path><path d="M802 424c11 2 21 5 31 9l1 1c-3 1-7 2-10 1l1-2h-1c-5 1-8 7-11 10-1 0-2 2-2 2-2 0-2-1-4 1-1 2-3 5-5 7-4-7-8-11-15-14 2-1 4 0 6 0 6 0 13 1 18 0 2 0 2 0 2-1v-3c-2-5-7-7-12-9 0-1 1-1 1-2zm0 35c5 2 9 4 14 2 2-1 4-3 7-4 3-2 8-2 11-1s4 3 5 5c1 3 1 7 3 10 2 0 6 0 7-1 2-2 3-4 3-6 0-3-1-6-2-9 4 4 6 9 10 13 1 1 3 2 4 3h0c-3 1-5 1-7 1-4 0-11-1-14 0-1 1-3 2-4 2l-3 3v2c1 2 1 2 3 2l1 1s0 1-1 1c0 2-1 3-2 4-8 2-14-6-21-8 0 0-1 0-1-1 2 0 4 1 7 0 3 0 8 0 10-3v-1c-1-2-4-4-6-5 2-1 4-1 6-2 0-2 0-3-1-5-6-7-14 3-22 1h0c-3 0-5-1-7-3v-1z" class="C"></path><defs><linearGradient id="o" x1="781.188" y1="421.204" x2="804.84" y2="444.324" xlink:href="#B"><stop offset="0" stop-color="#c0beb9"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#o)" d="M788 424l13 2c5 2 10 4 12 9v3c0 1 0 1-2 1-5 1-12 0-18 0-2 0-4-1-6 0h-2v1h1c1 1 2 3 3 5v1 1c3 3 7 4 9 9l4 3v1c-9-8-17-13-28-16l-7-2h-1c-2 0-4 0-6-1v-14l8-1 5-1h2l5-1h8z"></path><path d="M783 432h10 8v1h-12-2c-2-1-3-1-4-1z" class="c"></path><path d="M776 433h1 4l3 1h-1-2c-2 1-3 1-5 1v1h1c3 2 9 7 11 10v1h-1s-1-1-2-1c-2-2-5-3-6-6-2-1-2-2-4-3 0-1 0-1-1-2h1v-1h-2c1 0 2 0 3-1z" class="f"></path><path d="M775 425l5-1-3 2c0 2-1 4 1 5 1 0 1 0 2 1h3c1 0 2 0 4 1h-6-4v-1h-1-4c0-2 0-4 1-7h2z" class="U"></path><path d="M775 425c0 2 0 4 1 7h-4c0-2 0-4 1-7h2z" class="O"></path><path d="M768 426l5-1c-1 3-1 5-1 7h4 1v1h-1c-1 1-2 1-3 1h2v1h-1c1 1 1 1 1 2 2 1 2 2 4 3 1 3 4 4 6 6 1 0 2 1 2 1h1l1-1v1c3 3 7 4 9 9l4 3v1c-9-8-17-13-28-16l-7-2h-1c-2 0-4 0-6-1v-14l8-1z" class="b"></path><path d="M768 426l5-1c-1 3-1 5-1 7-2 0-5 0-7-1h-1c1-1 2-1 3-1 0-2 0-3 1-4z" class="V"></path><path d="M798 456c-6-4-11-9-16-12-4-2-10-3-13-6-1-2-1-3-1-5h8c-1 1-2 1-3 1h2v1h-1c1 1 1 1 1 2 2 1 2 2 4 3 1 3 4 4 6 6 1 0 2 1 2 1h1l1-1v1c3 3 7 4 9 9z" class="U"></path><path d="M609 571c4-1 8-2 13-2h0c-4 4-10 8-11 13 4 2 13 0 17-1l3-1c0-1 12-5 14-5h0c-1 2-3 4-3 6l7-4 1 1c-2 2-6 6-7 9v6l2 3c-1 3-1 5-1 7-1 4-3 8-6 10-1 2-2 2-4 3v1c1 0 1 0 2 1-3 0-4 0-6 1h5 1c1 2 2 2 3 2s2 0 3 1c2 0 3 1 5 1-1 0-2 1-3 0 1 1 1 1 3 1 1 1 2 1 3 2l1 1c3 0 8 2 10 1l7 4h1c1 1 2 1 3 2-3 1-6 2-10 3-1 0-1 1-3 1-2 2-8 2-10 3l1 2v1c-4 1-10 3-15 2v-1l-4 1c-8 0-17 2-24 0v-1h-4l-3-1c-5-4-8-10-9-16 0-2-1-4 0-5 1-3 0-8-1-11-1-11 2-21 7-31 1 0 4-4 4-6 2 0 5-1 6-3l2-1z" class="C"></path><path d="M608 594c2 0 6-2 8-2-1 1-2 1-3 2l-1 1c-2 1-4 2-5 2h-1-1l3-3z" class="J"></path><path d="M627 584h1c1 0 1 0 1 1l-8 2c-3 1-5 2-8 2 0 0-1 0-1-1h-1c3-1 7-2 10-3h4c1-1 1-1 2-1z" class="I"></path><path d="M645 575h0c-1 2-3 4-3 6-3 3-7 7-11 10-2-1-8 1-11 1-3 1-6 2-8 3l1-1c1-1 2-1 3-2 3-2 6-2 9-3s6-3 8-4c3-2 4-3 6-5 1-1 3-2 4-4l-12 5v-1c0-1 12-5 14-5z" class="D"></path><path d="M591 623c2 3 4 5 6 8 3 3 10 9 10 14h0-4l-3-1c-5-4-8-10-9-16 0-2-1-4 0-5z" class="d"></path><path d="M630 619c-2 1-5 0-7 0-6 0-12-1-18-3h0c2 0 4 0 6-1h-3l-2-1h-1-1-1v-1c-2 0-4-2-5-4h0v-3c-1-3-1-6 2-9 2-2 5-3 8-3l-3 3c-1 0-2 1-3 2h1l-1 1c0 1-1 1-2 2-1 2-1 4-1 6s2 3 3 4c3 2 8 1 11 1 1 0 3 0 4 1h-1c5 2 11 2 16 3 1-2 3-5 4-7 2-1 1-3 1-5 1 3 1 5 0 7l1 1c-1 2-2 2-4 3v1c1 0 1 0 2 1-3 0-4 0-6 1z" class="K"></path><defs><linearGradient id="p" x1="625.01" y1="644.667" x2="638.436" y2="631.909" xlink:href="#B"><stop offset="0" stop-color="#2e3236"></stop><stop offset="1" stop-color="#4e4f4e"></stop></linearGradient></defs><path fill="url(#p)" d="M618 633c1 0 1 0 2 1h2 1c1 0 0 0 1 1h3 3c2 1 4 1 5 1h2c1 0 0 0 2 1h0 2c0 1 1 1 1 1l1 1h1c1 0 3 1 5 2l1 2v1c-4 1-10 3-15 2v-1l-4 1v-1c-2-1-5-1-6-2-4-1-7-2-10-4h0c1 0 2 1 3 1l1 1h3c-1-5-5-4-8-7v-1c1 0 2 0 2 1 1 0 1 0 2-1z"></path><path d="M635 631l-22-3v-1c3 0 6 1 10 0l-12-3c-6-2-13-6-15-11v-1c6 10 17 10 27 11l13 2 14 1 1 1c3 0 8 2 10 1l7 4-16-1c-2 1-4 0-6 0h-9-2z" class="H"></path><path d="M623 623l13 2h1c-1 1-2 1-3 1-4 0-8 0-11-3zm28 4c3 0 8 2 10 1l7 4-16-1c3-1 8 1 11-1-2-1-3-1-5-1l-6-1-1-1h0z" class="R"></path><defs><linearGradient id="q" x1="631.077" y1="618.537" x2="645.423" y2="645.463" xlink:href="#B"><stop offset="0" stop-color="#373231"></stop><stop offset="1" stop-color="#4f575e"></stop></linearGradient></defs><path fill="url(#q)" d="M618 633c-4-1-7-2-11-4h3c6 1 12 1 18 2h7 2 9c2 0 4 1 6 0l16 1h1c1 1 2 1 3 2-3 1-6 2-10 3-1 0-1 1-3 1-2 2-8 2-10 3-2-1-4-2-5-2h-1l-1-1s-1 0-1-1h-2 0c-2-1-1-1-2-1h-2c-1 0-3 0-5-1h-3-3c-1-1 0-1-1-1h-1-2c-1-1-1-1-2-1z"></path><path d="M637 631h9c2 0 4 1 6 0l16 1h1l-1 1c-3 1-6 0-9 0h-18v-1c-2 0-3 0-4-1z" class="D"></path><path d="M649 577l1 1c-2 2-6 6-7 9v6l2 3c-1 3-1 5-1 7-1 4-3 8-6 10l-1-1c1-2 1-4 0-7 0 2 1 4-1 5-1 2-3 5-4 7-5-1-11-1-16-3h1c-1-1-3-1-4-1-3 0-8 1-11-1-1-1-3-2-3-4s0-4 1-6c1-1 2-1 2-2l1-1h-1c1-1 2-2 3-2h1 1c1 0 3-1 5-2s5-2 8-3c3 0 9-2 11-1 4-3 8-7 11-10l7-4z" class="C"></path><path d="M643 593h0c-1 3-1 8-1 11h0c0-3 0-7-1-9l-1 7v1c0-5 0-12 3-16v6z" class="Z"></path><defs><linearGradient id="r" x1="611.991" y1="590.496" x2="619.567" y2="599.069" xlink:href="#B"><stop offset="0" stop-color="#393b3f"></stop><stop offset="1" stop-color="#52565a"></stop></linearGradient></defs><path fill="url(#r)" d="M612 595c2-1 5-2 8-3 3 0 9-2 11-1-5 4-12 10-20 9-3 0-5-1-8-1h-1c1-1 2-2 3-2h1 1c1 0 3-1 5-2z"></path><path d="M633 593l1-1c0 1 0 1 1 2 1 0 1 1 2 2 0 2-1 5 0 7v2c0 2 1 4-1 5-1 2-3 5-4 7-5-1-11-1-16-3h1c2 0 4 0 5-2 3-3 4-7 4-11v-1c2-3 4-5 7-7z" class="d"></path><path d="M633 593l1-1c0 1 0 1 1 2 1 0 1 1 2 2 0 2-1 5 0 7v2c0 2 1 4-1 5-1-3-1-7-2-10 0-2 0-5-1-7z" class="G"></path><path d="M783 233c9 1 17 3 25 2 5 0 9-1 13-2 11-2 21-5 32-6 3 0 7 0 10 1h0c2 1 3 3 5 5h-1c-1 2-1 5-2 7-4 1-7-4-10-4-4 0-9 2-12 4l1 3h-1c0 3-1 5-2 7-1 1-4 5-4 7-3 3-4 7-6 11 0 2-2 7-1 8 1 3 0 4 0 7-2 3-1 8 0 11v9l-1 1h-1c0 3 1 7 2 9 1 5 2 9 3 13l1 3c1 0 1 1 2 1 0-1 0-2 1-3l4 14c1 1 1 1 2 1-1-7-1-15 2-22h4c3 1 5 0 8-1-3 6-6 11-7 18 0 3 0 7 1 11 2 4 3 8 7 10s7 2 11 1c2-1 7-1 9 0h-8c3 2 5 4 7 7h-9c-3 1-7-1-9-1l-4-2c-15-11-26-29-33-47-1-2-2-4-2-6l-1 1-3-2-1-4c-3-3-5-6-7-9-3-4-5-9-7-13h-1l-1 1c0-2-1-2-2-3h0c0 1 1 3 1 4l-9-17-5-16c-1-1-2-6-3-8l-6-16c3 3 5 4 8 5z" class="L"></path><path d="M837 327l4 14h0c-3-4-5-8-7-12 1 0 1 1 2 1 0-1 0-2 1-3zm-7-51c1 3 0 4 0 7-2 3-1 8 0 11v9l-1 1h-1c-2-9 0-19 2-28z" class="a"></path><defs><linearGradient id="s" x1="803.77" y1="246.575" x2="812.491" y2="255.675" xlink:href="#B"><stop offset="0" stop-color="#100f12"></stop><stop offset="1" stop-color="#2f2e2a"></stop></linearGradient></defs><path fill="url(#s)" d="M806 241h2v1l2 1c0 6 1 14-1 20h-1c-2-6 0-13-2-20v-2z"></path><path d="M849 320c3 1 5 0 8-1-3 6-6 11-7 18h0v6c-1-1-1-2 0-3v-4c0-1 0-2 1-3v-4h1c0-1 0-2 1-3v-2l1-1c0-1 0-1 1-1-1-1-2-1-3-1l-2 1c1 1 1 2 1 4 0 1-1 3-1 4 0 2-1 3-1 4-1 0-1 1-2 2 1 1 1 2 1 4h-1c-1-1 0-3-1-4v1c0 1-1 2-2 3 0-7 7-14 5-20z" class="F"></path><defs><linearGradient id="t" x1="839.782" y1="334.396" x2="849.409" y2="326.847" xlink:href="#B"><stop offset="0" stop-color="#a19e97"></stop><stop offset="1" stop-color="#bfbdb4"></stop></linearGradient></defs><path fill="url(#t)" d="M841 341c1 1 1 1 2 1-1-7-1-15 2-22h4c2 6-5 13-5 20 0 3 0 6 2 8h-1l-4-7h0z"></path><path d="M820 310c-1-3-2-6-2-9l-1-2c-1-5-1-10-2-14 0-4 0-9 1-12 0-2-1-4 0-6 0 8-1 15 0 23 1 1 1 3 1 4v1 3c1 1 1 1 1 2 2 17 17 45 31 56 6 4 11 8 19 10-3 1-7-1-9-1l-4-2c-15-11-26-29-33-47-1-2-2-4-2-6z" class="b"></path><path d="M799 264c1-1 1-1 1-2-1-1-1-2-1-3v-1-7h0l1 2h0v-4h1c1 1 0 4 0 5v7c1 5 3 10 5 15s5 11 5 17c-2-3-3-6-4-9-1-1-2-2-3-4-1-4-3-8-4-12h0c0-1-1-2-1-4z" class="N"></path><path d="M840 244c2-1 2-1 3-1 0 3-1 5-2 7-1 1-4 5-4 7-3 3-4 7-6 11v-3-1c2-1 2-3 3-5 1-1 1-3 2-4l4-4h0v-1-1h1l-1-2h-1l-4 4c1 0 1 1 1 2-5 3-5 11-8 16-1 1 0 1-1 2 0 0 0 1-1 2h0c-2-3 0-8 1-11v-2c2-4 4-9 7-13 1-1 1-2 2-3l1 1c-1 1-2 1-2 3h1l4-4z" class="F"></path><path d="M790 239v-2c2 0 3-1 5 0 1 2 2 5 2 7v-1c1-2 1-3 3-4 1-1 2-1 3-1l3 3v2h0c-1-1-2-3-3-4-1 0-2 0-3 1-2 2-1 7-2 9l-1-2v1c-1-2 0-4-1-5-1 0-1-2-1-3-1 0-1-1-2-1v5 3c1 5 3 10 3 15h0c-1-1-1-1-1-2s0-2-1-3c-2-2-2-2-2-5h-1v-1-1c0 2 0 3 1 5 1 5 1 9 4 13l2 1v-1-2l-1-2v-3c-1-1-1-2-1-3h2c-1-1-1-2-1-2v-3h1c0 4 0 8 1 11 0 2 1 3 1 4h0c1 4 3 8 4 12 1 2 2 3 3 4 1 3 2 6 4 9l4 12c-3-3-5-6-7-9-3-4-5-9-7-13-4-10-8-19-11-29v-4-11z" class="X"></path><path d="M783 233c9 1 17 3 25 2 5 0 9-1 13-2 11-2 21-5 32-6 3 0 7 0 10 1h0c2 1 3 3 5 5h-1c-1 2-1 5-2 7-4 1-7-4-10-4-4 0-9 2-12 4l1 3h-1c-1 0-1 0-3 1l-1-1 1-1-1-1c0-2 1-5 1-7-5 0-8 1-11 4-1 1-2 2-2 4-1-1-1-2-1-2 0-2-1-3-2-4-1 0-3 0-5 1s-3 5-3 7c0-2-1-4-2-6-1-1-2-1-3-1-1 2-1 4-1 6l-2-1v-1h-2l-3-3c-1 0-2 0-3 1-2 1-2 2-3 4v1c0-2-1-5-2-7-2-1-3 0-5 0v2 11 4c3 10 7 19 11 29h-1l-1 1c0-2-1-2-2-3h0c0 1 1 3 1 4l-9-17-5-16c-1-1-2-6-3-8l-6-16c3 3 5 4 8 5z" class="C"></path><path d="M840 242l3-2 1 3h-1c-1 0-1 0-3 1l-1-1 1-1z" class="T"></path><path d="M797 281c-2-3-4-7-6-10-4-8-7-18-9-28-1-2-2-6-2-8 1 1 2 2 3 2v1h2l1-1c1 0 2 0 3 1l1 1h0v11 4c3 10 7 19 11 29h-1l-1 1c0-2-1-2-2-3h0z" class="g"></path><path d="M783 238h2l1-1c1 0 2 0 3 1l1 1h0v11 4 3c1 2 1 3 1 4 1 3 1 5 2 7h0c-5-8-6-18-9-26-1-2-1-3-1-4z" class="U"></path><path d="M770 222l1-1 4 7h0l6 16c1 2 2 7 3 8l5 16 9 17c0-1-1-3-1-4h0c1 1 2 1 2 3l1-1h1c2 4 4 9 7 13 2 3 4 6 7 9l1 4 3 2 1-1c0 2 1 4 2 6 7 18 18 36 33 47l4 2c2 0 6 2 9 1h9c-2-3-4-5-7-7h8c4 1 9 1 12 5 2 2 3 5 2 8 0 1 0 2-1 3-1 6-5 11-10 14-3 2-8 3-12 2v1h-1l-2-1h-1-1l-1-1h-1c-1 0-1-1-1-1l-1-1-1-1h0c-1-1-2-1-2-1h-1l3 3c1 1 2 1 3 2 1 0 1 0 2 1h1 1c1 1 1 1 2 1h2l1 1v1h3 1c-1 1-3 1-4 1l-1-1h-2 0c-1-1-2-1-2-1l-2-2h-1-1-1l-1-1h-1c-5-2-16-19-18-25-1-2-2-3-3-5 0-1-3-4-4-5h0l-1-1-1 1-4-2c-7-10-14-19-25-25-4 1-7-2-10-3-2 0-2 0-3 1h-1-1c-3-2-7-4-10-6 0 0-1 0-2-1h-3 0l-1 1v-13-21-1-52l-2-12z" class="T"></path><path d="M816 309l3 2 1-1c0 2 1 4 2 6l-1 2v1 1l-4-6-1-5z" class="E"></path><path d="M790 308c2 1 5 2 5 4 0 1 0 2-1 2l1 1v1l1 1c-2 0-3-1-5-2v-1l-3-2c0-1 0-1 1-2l2 1-1-2v-1z" class="S"></path><path d="M808 296c2 3 4 6 7 9l1 4 1 5c-2-3-7-8-8-11 0-1 0-2-1-3s0-3 0-4z" class="c"></path><path d="M797 281h0c1 1 2 1 2 3l1-1h1c2 4 4 9 7 13 0 1-1 3 0 4s1 2 1 3c-2-2-3-5-5-8s-5-6-6-10c0-1-1-3-1-4z" class="V"></path><path d="M855 363l4 2c1 1 2 2 4 2 2 2 4 3 7 3 1 0 2 0 3 1 1 0 2-1 3-1h1 1c1-1 1 0 2 1v1h2l1 1h-1c-4-1-6 1-9 0-4 0-7-1-11-3-3-2-6-4-7-7z" class="N"></path><path d="M779 258c1-1 1-2 1-3v-1l4-2 5 16h0c-2-2-3-4-4-7 0-1 0-1-1-2v-1h-1l-1 1 3 10c-1-1-1 0-1-1l-1-1v-1h-1c0 2 1 4 2 6l1 3h-1c-1-1-1-1-1-2l-1-1c0-2 0-3-1-5 0-3-1-6-2-9z" class="X"></path><path d="M860 391h-1c-5-2-16-19-18-25-1-2-2-3-3-5l11 9c1 1 4 3 5 4 1 3 6 6 9 7 1 1 3 1 5 1 2 1 2 1 3 2-5 0-16-5-19-9h-1c-1-1-2-1-3-2l-1 1 6 9 7 8z" class="U"></path><path d="M847 374c-2-2-2-4-3-6l7 7c-1-1-2-1-3-2l-1 1z" class="F"></path><path d="M794 301c1 0 1 1 2 1l2 1 27 31-1 1-14-13c-3-3-7-7-11-10-1 0-4-3-5-4h1c5 2 9 8 14 11 0-1-4-4-5-6-2-4-7-7-10-11v-1z" class="V"></path><defs><linearGradient id="u" x1="788.6" y1="292.887" x2="787.9" y2="306.613" xlink:href="#B"><stop offset="0" stop-color="#a29d9b"></stop><stop offset="1" stop-color="#b9bcb2"></stop></linearGradient></defs><path fill="url(#u)" d="M775 287c2 0 6 3 8 5 3 1 6 4 8 6 1 1 2 3 3 3v1c3 4 8 7 10 11 1 2 5 5 5 6-5-3-9-9-14-11l-20-21z"></path><path d="M887 378c2-1 3-2 4-3v-1 1c-1 6-5 11-10 14-3 2-8 3-12 2-1 0-1 0-2-1 2 0 4 0 5-2s1-4-1-6h0c6-1 11 0 16-3v-1z" class="C"></path><defs><linearGradient id="v" x1="772.149" y1="295.04" x2="787.057" y2="300.729" xlink:href="#B"><stop offset="0" stop-color="#a09f9c"></stop><stop offset="1" stop-color="#c6c4c3"></stop></linearGradient></defs><path fill="url(#v)" d="M772 287l1-1 2 1 20 21h-1c1 1 4 4 5 4h-1l-4-3-3-2c-1-1-2-2-4-3h-1v1l4 3v1l-10-8c-2-1-3-3-5-4l-2-4v14l-1 1h0v-21h0z"></path><path d="M772 287l1-1 2 1 20 21h-1l-16-14c-1-1-4-3-5-5-1 0-1-1-1-2z" class="C"></path><defs><linearGradient id="w" x1="772.498" y1="302.748" x2="783.85" y2="303.833" xlink:href="#B"><stop offset="0" stop-color="#747373"></stop><stop offset="1" stop-color="#95938d"></stop></linearGradient></defs><path fill="url(#w)" d="M773 307v-14l2 4c2 1 3 3 5 4l10 8 1 2-2-1c-1 1-1 1-1 2l3 2v1c-2 0-15-8-18-8z"></path><path d="M878 359c4 1 9 1 12 5 2 2 3 5 2 8 0 1 0 2-1 3v-1 1c-1 1-2 2-4 3l3-3h0c0-1-1-2-2-3h0l-9-6c0 2 0 2-2 4h-1c-1 0-2 1-3 1-1-1-2-1-3-1-3 0-5-1-7-3-2 0-3-1-4-2 2 0 6 2 9 1h9c-2-3-4-5-7-7h8z" class="F"></path><path d="M859 365c2 0 6 2 9 1h9 1v2c-6 1-10 0-15-1-2 0-3-1-4-2z" class="V"></path><defs><linearGradient id="x" x1="771.516" y1="312.761" x2="784.841" y2="316.519" xlink:href="#B"><stop offset="0" stop-color="#9e9c99"></stop><stop offset="1" stop-color="#c9c8c1"></stop></linearGradient></defs><path fill="url(#x)" d="M773 307c3 0 16 8 18 8 2 1 3 2 5 2 1 0 1 0 2 1h-2c-3 0-5-2-7-3l-3-1c-1 2-2 4-1 6h2c1 1 1 1 2 1s2 1 3 1 2 1 3 1v1c3 1 6 3 8 5-4 1-7-2-10-3-2 0-2 0-3 1h-1-1c-3-2-7-4-10-6 0 0-1 0-2-1h-3 0l-1 1v-13h0l1-1z"></path><path d="M778 321c3-1 5 0 8 0 1 1 2 0 4 1h0c2 1 3 1 5 2 3 1 6 3 8 5-4 1-7-2-10-3-2 0-2 0-3 1h-1-1c-3-2-7-4-10-6z" class="V"></path><defs><linearGradient id="y" x1="770.095" y1="259.172" x2="786.802" y2="261.736" xlink:href="#B"><stop offset="0" stop-color="#9b9992"></stop><stop offset="1" stop-color="#c6c6bf"></stop></linearGradient></defs><path fill="url(#y)" d="M770 222l1-1 4 7h0l6 16c1 2 2 7 3 8l-4 2v1c0 1 0 2-1 3 1 3 2 6 2 9 1 2 1 3 1 5l1 1c0 1 0 1 1 2h1c0 4 2 8 4 12 1 1 2 3 3 5 2 3 4 3 4 7l2 4-2-1c-1 0-1-1-2-1s-2-2-3-3c-2-2-5-5-8-6-2-2-6-5-8-5l-2-1-1 1h0v-1-52l-2-12z"></path><path d="M772 274l1-7h0c3 7 8 14 12 20-2-1-4-4-6-6-2-1-4-3-5-4 0-1 0-1-1-2l-1-1z" class="D"></path><defs><linearGradient id="z" x1="770.931" y1="226.899" x2="782.003" y2="256.566" xlink:href="#B"><stop offset="0" stop-color="#8a8883"></stop><stop offset="1" stop-color="#c4c4bf"></stop></linearGradient></defs><path fill="url(#z)" d="M770 222l1-1 4 7h0l6 16c1 2 2 7 3 8l-4 2v1c0 1 0 2-1 3v-1c0-1-1-2-1-3l-1-3v-2-2h0c-1-2-1-2-1-3v-2c-1-2-1-2-1-3-1-2 0-3-1-3l-1-2-1-1v1l-2-12z"></path><path d="M772 274l1 1c1 1 1 1 1 2 1 1 3 3 5 4 2 2 4 5 6 6 3 2 5 5 7 8l4 4 2 4-2-1c-1 0-1-1-2-1s-2-2-3-3c-2-2-5-5-8-6-2-2-6-5-8-5l-2-1-1 1h0v-1c0-2 0-4 1-5-1-2-1-5-1-7z" class="b"></path><path d="M773 281h0 0c1 2 2 3 3 4 3 1 6 3 8 5 2 1 4 4 7 5h1l4 4 2 4-2-1c-1 0-1-1-2-1s-2-2-3-3c-2-2-5-5-8-6-2-2-6-5-8-5l-2-1-1 1h0v-1c0-2 0-4 1-5z" class="a"></path><path d="M273 226v1c-3 2-6 5-7 8l-3 9c-1 2-2 5-2 8 0 1-1 2-1 3l-1 2c-1 0-1 0-1 1h0c-2 5-5 10-6 15l-2 1h-1c-2 1-3 5-4 7l-8 13c-2 4-4 7-6 11-1 1-1 2-1 3h0c-1 3-4 6-4 8-1 2-1 3-2 5h-2 0v1l-1 1c0 2-1 4-2 5l-6 9h0l-1 1c-1 0-1 1-1 2-4 5-9 11-14 15-6 4-11 9-18 10l-1 1h-12c3-2 5-4 6-6l1 1c5 0 10-2 14-4 2-1 5-4 6-6v-1c1-5 1-13 0-18l-3-9c-1-1-2-3-2-4 1 1 3 1 4 1l1 1v1c2 0 2 0 3 1 2 1 2 4 3 5v-1-1h1l1 5 1 7c1-1 2-3 3-4 2-6 3-13 2-20l-1-2c1 0 2-1 3-2v-2l1-1 1 1 1 1 1-2v-10-2c-1-3-3-6-5-8l-1-3 3 3 1-1c-1-1-2-2-2-3 0-3 0-5 1-7v-3h-1l-1 1h0l-1-1c-1-5-2-10-4-15-1-1-1-1-1-2l1-2-3-9c-1-2-1-3 0-6l-4-2c-7-2-11 1-17 4-1-2-2-5-2-7h0c0-2 1-4 3-5 8-5 30 2 39 4 11 2 22 3 33 2 5-1 10-2 14-5l7-4z" class="L"></path><path d="M239 267c1-7 2-13 2-20h1v10c0 3-1 8-2 10h-1z" class="E"></path><path d="M193 322c2 0 2 0 3 1 2 1 2 4 3 5 0 3 1 9-1 11 0-4 0-7-2-10l-3-7z" class="f"></path><path d="M199 328v-1-1h1l1 5 1 7c1-1 2-3 3-4 1 3-2 5-2 8v1 1c-2 2-4 5-6 7h-1l2-2c0-2 1-5 0-6v-4c2-2 1-8 1-11z" class="N"></path><path d="M201 331l1 7c1-1 2-3 3-4 1 3-2 5-2 8v1 1h0-2v-2c1-3 0-7 0-11z" class="E"></path><path d="M239 267h1c0 3-1 6-2 9v1c1 4-1 9-2 13h0c1-1 2-2 2-3 1-1 1-1 1-2l1-1v-1l1-2v-1l1-3c1-1 1-2 2-3-1 4-2 7-4 11-1 3-3 5-3 9-2 4-4 7-6 11 0-1-1-2-1-2 1-4 3-9 4-13l5-23z" class="F"></path><path d="M258 237h2 2 2c0 3-1 6-2 9-1 2-2 4-1 6 0 1-1 2-1 3l-1 2c-1 0-1 0-1 1h0c-2 5-5 10-6 15l-2 1h-1c-2 1-3 5-4 7l-8 13c0-4 2-6 3-9 2-4 3-7 4-11 2-5 4-12 5-18-1-2 0-7 1-9 0-3 0-6-1-8h-1l1-1c1-1 2-1 3-1 2 1 3 2 3 4l1-2 2-2z" class="O"></path><path d="M256 239l2-2c1 2 1 3 0 5-1 6-1 11-3 16 0 1 0 1-1 2 0-3 1-5 1-8 1-5 1-9 1-13z" class="S"></path><path d="M258 237h2 2 2c0 3-1 6-2 9-1 2-2 4-1 6 0 1-1 2-1 3-2 0-2 0-3 1v2l-1 1-1-1h0c2-5 2-10 3-16 1-2 1-3 0-5z" class="U"></path><path d="M248 239l1-1c1-1 2-1 3-1 2 1 3 2 3 4 0 8-1 17-4 25-1 2-1 5-3 6v1l-1 2c-1-5 2-10 2-16h0v-3c-1-2 0-7 1-9 0-3 0-6-1-8h-1z" class="X"></path><path d="M273 226v1c-3 2-6 5-7 8l-3 9c-1 2-2 5-2 8-1-2 0-4 1-6 1-3 2-6 2-9h-2-2-2l-2 2-1 2c0-2-1-3-3-4-1 0-2 0-3 1l-1 1-2 5h0c-1-2-1-4-2-6-1-1-2 0-4 0-1 0-2 1-2 3-2 3-1 6-2 9 0 5 0 10-2 15v-6-18c-1-2-1-3-2-4h-2c-1 2-2 4-2 5l-1 1c0-2-1-5-2-6s-2-1-3-1c-2 0-2-1-3 0-1 2-1 4-2 6v1l-3-6c-3-3-6-3-10-4-1 2-1 7-1 9l-3-3-4-2c-7-2-11 1-17 4-1-2-2-5-2-7h0c0-2 1-4 3-5 8-5 30 2 39 4 11 2 22 3 33 2 5-1 10-2 14-5l7-4z" class="C"></path><defs><linearGradient id="AA" x1="185.253" y1="270.509" x2="231.848" y2="293.795" xlink:href="#B"><stop offset="0" stop-color="#d6d3d1"></stop><stop offset="1" stop-color="#f8f8f5"></stop></linearGradient></defs><path fill="url(#AA)" d="M200 239l3 3c0-2 0-7 1-9 4 1 7 1 10 4l3 6v-1c1-2 1-4 2-6 1-1 1 0 3 0 1 0 2 0 3 1s2 4 2 6c-2 7 0 17 0 25l1 1c0 1-1 3 0 4 0 2 0 5 1 6 0 2 0 3-1 4 0 2 1 4 0 6 0 3-1 5-1 8v2h-1v1 1c-1 1-1 1-1 2v1c0 1 0 1-1 3v2c0 1 0 0-1 2v1-2c-2 7-5 13-8 19-2 3-3 6-5 8l-1-1h-1v-3l-5 10v-1c0-3 3-5 2-8 2-6 3-13 2-20l-1-2c1 0 2-1 3-2v-2l1-1 1 1 1 1 1-2v-10-2c-1-3-3-6-5-8l-1-3 3 3 1-1c-1-1-2-2-2-3 0-3 0-5 1-7v-3h-1l-1 1h0l-1-1c-1-5-2-10-4-15-1-1-1-1-1-2l1-2-3-9c-1-2-1-3 0-6z"></path><path d="M202 256c3 0 5 1 8 3 0-1-1-2-2-3h0c-1 0-2-1-3-1v-1l1 1h2c1 0 1-1 2-1l2 6v1c1 1 1 2 1 3h-1l1 1v3l1 7c-1 2-1 4-1 6l-2-2-1-3v-3h-1l-1 1h0l-1-1c-1-5-2-10-4-15-1-1-1-1-1-2z" class="X"></path><defs><linearGradient id="AB" x1="216.363" y1="307.266" x2="204.458" y2="303.34" xlink:href="#B"><stop offset="0" stop-color="#706f6b"></stop><stop offset="1" stop-color="#a7a59c"></stop></linearGradient></defs><path fill="url(#AB)" d="M214 275l-1-7v-3l-1-1h1c2 4 2 10 3 15 2 20 0 37-8 54l-5 10v-1c0-3 3-5 2-8 2-6 3-13 2-20l-1-2c1 0 2-1 3-2v-2l1-1 1 1 1 1 1-2v-10-2c-1-3-3-6-5-8l-1-3 3 3 1-1c-1-1-2-2-2-3 0-3 0-5 1-7l1 3 2 2c0-2 0-4 1-6z"></path><path d="M213 297l1-4h0c1 2 1 6 1 9h0l-1-2v4c-1 1-1 2-1 4h0v-1-10z" class="a"></path><path d="M210 307l1 1 1 1-1 7-4-2-1-2c1 0 2-1 3-2v-2l1-1z" class="g"></path><path d="M214 275l1 12-3-5h-1v4c-1-1-2-2-2-3 0-3 0-5 1-7l1 3 2 2c0-2 0-4 1-6z" class="O"></path><defs><linearGradient id="AC" x1="449.512" y1="615.72" x2="390.164" y2="601.695" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#232325"></stop></linearGradient></defs><path fill="url(#AC)" d="M435 559c2 1 3 2 4 2 8 9 13 17 14 29h0c1 8 1 16 1 24-1 6-1 13-3 19l1 1 1-1c1 1 0 2 0 3h0c-1 1-4 4-4 5-1 2-3 3-5 4 0 0-2 1-2 2l-5 4c-1 1-2 2-3 2l-1 1c-2 1-5 2-7 3-4 1-12 4-16 3v-1-1h-1c0-1-5-3-6-3-2-1-3-3-4-4 1-1 3-2 4-3v-1c-4 0-8-2-12-3-1-2 0-1 0-3l-11-3c-1 0-2-1-3-2h-2c2-1 4-1 5-1h1c3-3 8-2 12-2l-1-2h-3 0 3l1-1h-4v-3l-6 2 1-1c3-2 7-3 11-5 2 0 3-1 3-3l6-1c-1-1 0-1-1-1-4-1-7-3-9-7-4-6-2-10 0-16l1-2c0-1-1-3-2-3-2-5-6-9-8-14v-1c2 0 2 0 4 1h0l1-1-1-3 8 4c9 4 17 7 27 8l1-1c-5-2-7-8-9-13 5 0 10 1 14 3h1 0c2-1 2-1 3-1h1 1c1 1 2 1 3 2l1-1-2-1 1-1c1 0 4 5 6 4-3-4-5-7-9-11l1-1-2-2v-2z"></path><path d="M426 578h0l-1-1v-1c2 1 3 0 6 1 0 1 0 2 1 3l-1-1c-1 0-3-2-5-2v1zm-26 4c0-1 1-1 2-1l1 1h1 2 1c-1 1-2 0-3 1l1 1h-3l-1-1-1-1z" class="I"></path><path d="M429 629c2-1 5-1 7-2-3 6-8 12-15 14 1 0 2-1 3-2v-2c0-1 0-1 1-2l2-1-1-1-3 1v-1c2-1 5-1 7-3l-1-1z" class="M"></path><path d="M425 583c-5-2-7-8-9-13 5 0 10 1 14 3h1 0c2-1 2-1 3-1-1 1-1 2-2 4 1 2 4 3 6 5-3-1-5-3-7-4-3-1-4 0-6-1v1l1 1h0l2 3-2 2-1-1v1z" class="K"></path><path d="M410 586c2 0 3 0 5 1h6c4 1 9 2 13 3 3 0 6 2 8 5l1 2 1 1v2h-1l-1-1s0-1-1-1h-1c-1-1-2-3-4-3-1-1-2-1-3-1l-5-1-7-1c-4-2-7-4-11-6z" class="I"></path><path d="M389 627c13-2 25-2 37-5 3 0 6-1 9-2 2-1 4-3 6-3-5 4-9 6-14 7h-2l1 1h0c-2 1-5 2-7 3-3 2-7 3-11 4-5 1-11 0-16-1h-3 0 3l1-1h-4v-3z" class="W"></path><path d="M440 598h1c1 0 1 1 1 1l1 1h1v-2c2 4 3 9 1 13v1h-1c0 2-1 3-3 5h0c-2 0-4 2-6 3-3 1-6 2-9 2-12 3-24 3-37 5l-6 2 1-1c3-2 7-3 11-5 2 0 3-1 3-3l6-1c1 0 5-1 7-1l1-3h8c4 1 12 1 16-1v-1c1-1 2-1 3-2h0 1c1-1 2-3 2-4 1-4 1-6-2-9z" class="M"></path><path d="M425 619h2v1c-6 2-12 2-19 3l-2-1c6-2 13-2 19-3z" class="P"></path><path d="M440 598h1c1 0 1 1 1 1l1 1h1v-2c2 4 3 9 1 13v1h-1c0 2-1 3-3 5 0-1-1-1-1-1l1-1c1-1 1-2 2-3 1-2 1-2 1-4v-1l1-1c-1-1-1 0-1-1v-2h0v1l-1 1c0 4-2 6-5 9h-1c0-1 1-1 1-2l1-1h0 1c1-1 2-3 2-4 1-4 1-6-2-9z" class="Z"></path><defs><linearGradient id="AD" x1="410.498" y1="627.154" x2="396.376" y2="647.111" xlink:href="#B"><stop offset="0" stop-color="#202224"></stop><stop offset="1" stop-color="#4a4c4f"></stop></linearGradient></defs><path fill="url(#AD)" d="M429 629l1 1c-2 2-5 2-7 3v1l3-1 1 1-2 1c-1 1-1 1-1 2v2c-1 1-2 2-3 2-4 3-8 3-12 5-2 0-4 0-6 1-4 0-8-2-12-3-1-2 0-1 0-3l-11-3c-1 0-2-1-3-2h-2c2-1 4-1 5-1h1c3-3 8-2 12-2h4c5 1 9 0 13-1 7-1 13-1 19-3z"></path><defs><linearGradient id="AE" x1="422.197" y1="622.353" x2="392.303" y2="643.647" xlink:href="#B"><stop offset="0" stop-color="#2f3132"></stop><stop offset="1" stop-color="#565a5e"></stop></linearGradient></defs><path fill="url(#AE)" d="M429 629l1 1c-2 2-5 2-7 3v1l3-1 1 1-2 1c-6 1-12 4-19 5-2 1-6 1-8 1 2-1 3-2 5-2-1-1 0-1-1 0h-7l1-1h4c-3-1-7-1-10-1s-6-1-9-1c0 1 0 1 1 1-1 1-1 1-2 1s-2-1-3-2h-2c2-1 4-1 5-1h1c3-3 8-2 12-2h4c5 1 9 0 13-1 7-1 13-1 19-3z"></path><path d="M393 633h4c-6 3-12 1-18 2l-2 1h-2c2-1 4-1 5-1h1c3-3 8-2 12-2z" class="Y"></path><defs><linearGradient id="AF" x1="453.693" y1="608.39" x2="397.407" y2="612.901" xlink:href="#B"><stop offset="0" stop-color="#2e3134"></stop><stop offset="1" stop-color="#55575a"></stop></linearGradient></defs><path fill="url(#AF)" d="M435 559c2 1 3 2 4 2 8 9 13 17 14 29h0c1 8 1 16 1 24-1 6-1 13-3 19l1 1 1-1c1 1 0 2 0 3h0c-1 1-4 4-4 5-1 2-3 3-5 4 0 0-2 1-2 2l-5 4c-1 1-2 2-3 2l-1 1c-2 1-5 2-7 3-4 1-12 4-16 3v-1-1h-1c0-1-5-3-6-3-2-1-3-3-4-4 1-1 3-2 4-3v-1c2-1 4-1 6-1l7 2c6 0 12-1 17-3 3-9 12-15 16-23 0-1 1-4 1-5 1-5 0-10 0-15-1-6-3-13-6-19l-3-6c-2-2-4-3-6-5h1c1 1 2 1 3 2l1-1-2-1 1-1c1 0 4 5 6 4-3-4-5-7-9-11l1-1-2-2v-2z"></path><path d="M451 633l1 1 1-1c1 1 0 2 0 3h0c-1 1-4 4-4 5-1 2-3 3-5 4 0 0-2 1-2 2l-5 4h0c0-2 5-5 6-6 4-2 7-7 8-12z" class="P"></path><path d="M439 574l1-1-2-1 1-1c1 0 4 5 6 4 3 3 4 10 5 14-3-4-4-10-9-13l-2-2z" class="K"></path><path d="M403 647c2-1 4-1 6-1l7 2h5l-3 1v1h2c-4 1-7 1-11 1l-1-1c1-1 2-1 3-2-1 0-3 0-4 1l-1-1h-3v-1z" class="e"></path><path d="M449 622c2 3-1 9-2 12-2 4-5 9-10 10-1 1-2 1-4 1 3-9 12-15 16-23z" class="d"></path><path d="M389 572l8 4s-1 1-1 2c1 1 2 3 4 4l1 1 1 1h3c2 1 3 2 5 2 4 2 7 4 11 6l7 1 5 1c1 0 2 0 3 1 2 0 3 2 4 3 3 3 3 5 2 9 0 1-1 3-2 4h-1 0c-1 1-2 1-3 2v1c-4 2-12 2-16 1h-8l-1 3c-2 0-6 1-7 1-1-1 0-1-1-1-4-1-7-3-9-7-4-6-2-10 0-16l1-2c0-1-1-3-2-3-2-5-6-9-8-14v-1c2 0 2 0 4 1h0l1-1-1-3z" class="C"></path><path d="M389 572l8 4s-1 1-1 2c1 1 2 3 4 4l1 1v1c-4-3-7-6-11-9l-1-3z" class="J"></path><path d="M412 599c3 2 3 5 6 7l1 1h0c1 3 7 5 10 6h6 1v1c-4 2-12 2-16 1-4-4-6-11-8-16z" class="B"></path><path d="M418 606l1 1h0c1 3 7 5 10 6h6c-1 1-2 0-4 1h-6c-2-1-5-3-6-5 0-1 0 0-1-1v-2z" class="K"></path><defs><linearGradient id="AG" x1="400.484" y1="595.153" x2="404.334" y2="612.928" xlink:href="#B"><stop offset="0" stop-color="#1a1c1d"></stop><stop offset="1" stop-color="#3b3d3f"></stop></linearGradient></defs><path fill="url(#AG)" d="M399 610c-1-4 1-8 3-12 0-1 0-3 1-4 1 0 2-1 3 0-1 2-1 7-1 10h0v12c-2-2-4-3-6-6h0z"></path><path d="M406 594c3 1 4 3 6 5 2 5 4 12 8 16h-8-7v-11c0-3 0-8 1-10z" class="d"></path><defs><linearGradient id="AH" x1="414.843" y1="607.875" x2="379.093" y2="587.8" xlink:href="#B"><stop offset="0" stop-color="#3e3e41"></stop><stop offset="1" stop-color="#515556"></stop></linearGradient></defs><path fill="url(#AH)" d="M385 575c2 0 2 0 4 1h0c3 3 8 10 9 14 0 5-4 9-4 13 1 2 4 5 5 7h0c2 3 4 4 6 6v-12h0v11h7l-1 3c-2 0-6 1-7 1-1-1 0-1-1-1-4-1-7-3-9-7-4-6-2-10 0-16l1-2c0-1-1-3-2-3-2-5-6-9-8-14v-1z"></path><defs><linearGradient id="AI" x1="425.658" y1="593.621" x2="422.173" y2="599.482" xlink:href="#B"><stop offset="0" stop-color="#45494d"></stop><stop offset="1" stop-color="#606163"></stop></linearGradient></defs><path fill="url(#AI)" d="M401 583l1 1h3c2 1 3 2 5 2 4 2 7 4 11 6l7 1 5 1c1 0 2 0 3 1 2 0 3 2 4 3 3 3 3 5 2 9 0 1-1 3-2 4h-1c1-1 2-2 2-4s-1-4-2-6h-3-3-1c-3 0-7 0-10-1-8-2-16-11-21-16v-1z"></path><path d="M433 601c0-1 0-2-1-3h0 2c1 0 2 1 2 3h-3zm-21-9h9l7 1h1c1 1 2 1 3 2v1l-1-1h-2 0c-3 0-9 0-11-1s-4-1-6-2z" class="H"></path><path d="M402 584h3c2 1 3 2 5 2 4 2 7 4 11 6h-9c-4-2-7-5-10-8z" class="D"></path><path d="M428 593l5 1c1 0 2 0 3 1 2 0 3 2 4 3 3 3 3 5 2 9 0 1-1 3-2 4h-1c1-1 2-2 2-4s-1-4-2-6h-3c0-2-1-3-2-3-1-2-4-2-5-3h2l1 1v-1c-1-1-2-1-3-2h-1z" class="K"></path><path d="M428 593l5 1c2 2 5 4 6 7h0-3c0-2-1-3-2-3-1-2-4-2-5-3h2l1 1v-1c-1-1-2-1-3-2h-1z" class="D"></path><path d="M679 114l-1-1 2-1c4 1 6 5 10 8 1 0 1 2 2 3s1 2 1 3h1c0-1 0-1-1-1 0-2 1-2 0-3 0-1 0-2-1-3 1-1 1-1 1-2-3-7-5-15-10-21-1-2-3-4-5-6h-1c-1-1-2-1-3-2h-1c0-2 2-3 3-3 3-2 7-2 10-1h1 1c6 2 12 6 18 9 1-2 1-5 2-8 1 7 1 15 2 22 3-9 6-17 12-25 1 4 2 10 2 14 2-1 4-6 6-8 3-4 7-7 11-9v11c5-5 11-9 18-11h1v2c2 0 5-1 7-2 2 0 4-1 7-1 8-1 17-1 24 2 2 1 5 2 6 4 1 1 1 1 0 2-3 4-6 6-11 8v-1c1 0 1 0 2-1 2 0 3-1 5-3h1v-1c-3-1-7 0-10 0h-2 0-1c-2 0-2 0-4 1h-1-1-1c-1 0-1 0-1 1h-1-2-2c0 1-1 1-1 1-1 1-3 1-3 1l-6 2-2 1-5 2-1 1h-1l-2 2-3 1-1 1h-1c-1 1-2 2-4 3-1 1-3 2-4 4-3 2-5 4-7 7 0 1-1 1-2 2h-1c-5 8-10 17-10 27-1 2-1 4-1 5 0 5 0 10 1 15l1 3c0 3 1 5 2 8s2 5 3 7c-3 0-10-1-12-2l-1-1-4-2 4 1 1-1v-1c-7-10-20-15-30-20h2c-1-1-2-1-2-1l-1-1c-4-2-8-3-11-6-1-1-2-3-3-4-1 0-2-1-3-2h0c-2-2-3-4-3-6h0c0-3-3-5-5-8-2-4-3-10-4-14l1-1c1 0 2 1 3 1 0 1 1 1 2 1l1 1c2-1 5 1 7 2 3 2 7 4 10 5l1-1c-1-1-1-1-1-3v-2-4z" class="L"></path><path d="M709 133v1 1 10l-1 2v1-1c0-3-1-7 0-10 1-2 1-2 1-4z" class="T"></path><path d="M679 114h0l1 1 1 1c3 2 4 6 5 9 1 1 0 3 0 4-1-3-4-10-6-11h-1v-4z" class="S"></path><path d="M680 118c2 1 5 8 6 11 2 5 5 13 9 17 1 1 2 0 2 2h-1l1 5-2-3-9-19c-2-5-5-8-6-13z" class="a"></path><path d="M699 147c1-1 0-3 0-5 0-3-1-10 1-13 0 0 0 1 1 2v-5l1 1v13l-1-2v5 1c1 1 1 3 1 4 1 2 1 5 1 7h-1c0-1 0-1-1-2 0-2-1-4-2-6z" class="U"></path><path d="M688 152c-3-1-7-4-10-6v-1c3 2 7 5 10 6-1-2-5-4-7-5-2-2-4-3-6-5l1-1c4 4 10 8 15 10-2-2-4-5-6-8-2-2-7-6-8-8h0a120.94 120.94 0 0 1 16 16h2l2 3c1 1 2 2 2 4-2-1-8-5-11-5z" class="V"></path><defs><linearGradient id="AJ" x1="700.471" y1="156.691" x2="698.311" y2="150.707" xlink:href="#B"><stop offset="0" stop-color="#555"></stop><stop offset="1" stop-color="#6e6d6b"></stop></linearGradient></defs><path fill="url(#AJ)" d="M697 148h1c-1-2-2-5-2-7-1-3-2-6-2-9-1-2-3-6-3-7v-1c3 3 4 9 5 13s2 7 3 10c1 2 2 4 2 6 1 1 1 1 1 2h1c0-2 0-5-1-7 0-1 0-3-1-4v-1-5l1 2c1 4 3 12 1 16 0 1-1 2-2 2-3 0-10-4-13-6 3 0 9 4 11 5 0-2-1-3-2-4l-1-5h1z"></path><path d="M714 170l-1-3c-1-3-1-7-1-11 0-14 3-25 10-38h1c-4 6-7 13-8 19-2 8-3 16-2 24 0 3 1 9 4 11 1 0 0 0 1 1l4 5c1 0 1 1 2 2h1c-1-1-1-2-2-4 0 0 0-1-1-2v-2c0-1 0-1-1-2 0-1 0-2-1-3v-3c-1-6-1-15 0-21 2-7 4-14 7-21 1-1 3-4 4-4-5 8-10 17-10 27-1 2-1 4-1 5 0 5 0 10 1 15l1 3c0 3 1 5 2 8s2 5 3 7c-3 0-10-1-12-2l-1-1-4-2 4 1 1-1v-1c-7-10-20-15-30-20h2l9 4c3 0 5 0 7 1s3 2 4 2c3 2 4 4 6 6h1z" class="U"></path><path d="M696 161c3 0 5 0 7 1s3 2 4 2c3 2 4 4 6 6h1 0c1 3 3 4 4 6 0 1 1 1 0 2l-1-1c-5-7-14-12-21-16z" class="F"></path><path d="M767 442l7 2c11 3 19 8 28 16 2 2 4 3 7 3h0c8 2 16-8 22-1 1 2 1 3 1 5-2 1-4 1-6 2 2 1 5 3 6 5v1c-2 3-7 3-10 3-3 1-5 0-7 0 0 1-1 2 0 2l1 2 1 2h0l2 2 9 10s2 4 3 4c2 6 3 11 0 17-1 4-3 7-5 11-13 14-25 24-43 30-4 1-8 2-12 2l-1-1c-3-1-7-1-10-1v-6-16l1-1v-3h-1v-2-2l1-1c-1-1 0-2 0-4l-1-9v-4-6h1l-1-1c2-2 1-10 1-13h-1v-7l3 2-1-1v-1-1l-1-1c-1 0 0-3 0-4 1-1 2 1 4 1 0 1 2 0 3 0l-2-1 1-7h0 0c1-1 1-3 2-4l-2-1 1-1h-3l-1-1c1-1 2-2 3-2 2-1 3 0 5 0h1c3 1 7 3 10 4 1 1 2 2 3 2h1 0l-6-3s-2-1-3-2c-2-1-5-2-7-3l-1-1v-1h3 0c-1-1-2-1-3-1l-3-1 1-2c0-1-1-1-1-1l-1-1v-1c0-2 0-6 1-8z" class="T"></path><path d="M772 552c0-1 1-2 2-2l1-1v-1c2 0 5 0 7-1 2 0 4 0 6-1 5-1 9-4 13-5-2 3-5 4-8 5-1 0-1 0-2 1-2 2-8 2-11 3-2 1-4 1-6 1l1 1h0 3l2 1c-3 0-5 0-8-1z" class="S"></path><path d="M793 508l3-6c1-5 3-12 1-18 0-2-2-5-3-7v-1l2 3c4 6 5 16 3 23-1 4-3 7-6 11h0c2-4 3-7 4-11l1-1v-2c-1 3-2 7-5 9z" class="a"></path><path d="M781 535h0c-2 1-3 1-4 1l-1 1c2 2 4 1 7 1 2 1 4 0 7 0l1-1c4-1 8-4 10-8l2-2 1 1c-2 3-4 5-7 7s-5 3-8 4h-1c-4 1-9 0-13 2h-3v1 1l-1 2c1 1 1 0 2 0 0 0 1 0 2 1h0c-2 1-4 0-5 0-1-1-1-3-1-4 0-2 1-3 2-4s1-1 1-2 1-1 1-1h8zm-14-93l7 2c-1 0-2 0-2 2 0 3-1 3 0 6l4 2c0 1 1 2 2 3s4 1 5 2c5 2 9 5 13 7l-1 1c-4-3-8-6-12-7-1 0-2 0-3-1l1 1c-1 0-1 1-2 0-2-1-3-1-6-2h-2c2 1 0 0 1 0 2 1 3 2 4 2 2 1 4 2 5 3v1s-2-1-3-2c-2-1-5-2-7-3l-1-1v-1h3 0c-1-1-2-1-3-1l-3-1 1-2c0-1-1-1-1-1l-1-1v-1c0-2 0-6 1-8z" class="F"></path><defs><linearGradient id="AK" x1="759.45" y1="543.241" x2="765.839" y2="542.248" xlink:href="#B"><stop offset="0" stop-color="#817f7d"></stop><stop offset="1" stop-color="#9a9992"></stop></linearGradient></defs><path fill="url(#AK)" d="M760 530c4 2 9 3 13 5h0s-1 0-1 1 0 1-1 2-2 2-2 4c0 1 0 3 1 4 1 0 3 1 5 0h7-3l-1 1c-2 1-4 1-6 1-1 1-2 1-3 3l-2-2c-2 2-3 3-7 3h0v-16l1-1v-3h-1v-2z"></path><path d="M767 549l1-1h0c1-1 3-1 4 0-1 1-2 1-3 3l-2-2z" class="c"></path><defs><linearGradient id="AL" x1="760.533" y1="534.23" x2="766.966" y2="536.509" xlink:href="#B"><stop offset="0" stop-color="#878683"></stop><stop offset="1" stop-color="#a6a49c"></stop></linearGradient></defs><path fill="url(#AL)" d="M760 530c4 2 9 3 13 5h0s-1 0-1 1 0 1-1 2-2 2-2 4c0 1 0 3 1 4-1 0-3 0-3-1-2-1-1-1-3-3h-1v-4c-1-1-2-2-2-3v-3h-1v-2z"></path><path d="M763 538l1 1v3h-1v-4z" class="V"></path><path d="M782 546c6-1 12-2 17-6 3-3 10-9 11-13 0-2 1-5 2-8 2-6 2-15 1-22h0c-2-9-5-16-10-23v-1c1 0 2 1 2 2l2 3c9 12 11 26 9 40-2 8-8 18-15 23-4 1-8 4-13 5-2 1-4 1-6 1-2 1-5 1-7 1v1l-1 1c-1 0-2 1-2 2l-3-1c1-2 2-2 3-3 2 0 4 0 6-1l1-1h3z" class="X"></path><path d="M761 490h-1v-7l3 2c3 4 5 8 7 12v4l-1 3c1 1 1 1 3 1v1c-3 0-4 0-6 2-1 1-2 2-2 4 0 1 1 3 3 4 3 2 7 3 10 2l2-1h2l-9 3c-2 1-3 2-4 4v3c1 3 3 4 6 5s7 1 11 1v1l-4 1h-8 0c-4-2-9-3-13-5v-2l1-1c-1-1 0-2 0-4l-1-9v-4-6h1l-1-1c2-2 1-10 1-13z" class="C"></path><path d="M760 514l8 5c-2 3-4 6-7 8-1-1 0-2 0-4l-1-9z" class="O"></path><path d="M761 490c2 1 5 3 5 5 1 2 1 3 0 5s-3 3-5 4l-1-1c2-2 1-10 1-13z" class="U"></path><path d="M817 484l2 2 9 10s2 4 3 4c2 6 3 11 0 17-1 4-3 7-5 11-13 14-25 24-43 30-4 1-8 2-12 2l-1-1c-3-1-7-1-10-1v-6h0c4 0 5-1 7-3l2 2 3 1c3 1 5 1 8 1 6 0 12-1 17-4 11-5 23-15 27-26 5-14-2-27-7-39z" class="C"></path><defs><linearGradient id="AM" x1="757.974" y1="480.87" x2="794.841" y2="502.18" xlink:href="#B"><stop offset="0" stop-color="#b9b7b2"></stop><stop offset="1" stop-color="#eeedea"></stop></linearGradient></defs><path fill="url(#AM)" d="M768 464h-3l-1-1c1-1 2-2 3-2 2-1 3 0 5 0h1c3 1 7 3 10 4 1 1 2 2 3 2h1c1 1 2 1 3 2l1 1c1 1 3 3 4 5l3 3-2 1-2-3v1c1 2 3 5 3 7 2 6 0 13-1 18l-3 6c-2 1-3 4-5 4-2 1-3 3-4 4h-1c-1 0 0 0-1 1h-1-2l-2 1c-3 1-7 0-10-2-2-1-3-3-3-4 0-2 1-3 2-4 2-2 3-2 6-2v-1c-2 0-2 0-3-1l1-3v-4c-2-4-4-8-7-12l-1-1v-1-1l-1-1c-1 0 0-3 0-4 1-1 2 1 4 1 0 1 2 0 3 0l-2-1 1-7h0 0c1-1 1-3 2-4l-2-1 1-1z"></path><path d="M770 497l4 2-4 2v-4z" class="E"></path><path d="M768 464c8 2 17 6 22 14 3 4 4 10 3 14-1 3-2 5-5 6s-6 2-9 1c-1-1-1 0-1-1v-1c-1-2-1-6-2-8-2-4-5-8-8-11l-2-1 1-7h0 0c1-1 1-3 2-4l-2-1 1-1z" class="C"></path><path d="M782 486c0 4 0 7-3 10l-1 1c-1-2-1-6-2-8h5v-2l1-1z" class="T"></path><defs><linearGradient id="AN" x1="770.315" y1="466.353" x2="776.758" y2="489.406" xlink:href="#B"><stop offset="0" stop-color="#bebdbb"></stop><stop offset="1" stop-color="#f1f1ee"></stop></linearGradient></defs><path fill="url(#AN)" d="M769 466c6 4 11 10 13 18v2l-1 1v2h-5c-2-4-5-8-8-11l-2-1 1-7h0 0c1-1 1-3 2-4z"></path><defs><linearGradient id="AO" x1="414.323" y1="213.529" x2="424.565" y2="137.162" xlink:href="#B"><stop offset="0" stop-color="#0d0d0e"></stop><stop offset="1" stop-color="#3d4143"></stop></linearGradient></defs><path fill="url(#AO)" d="M463 137v1l-5 1h7c1 0 2 1 3 1-2 1-4 1-6 2-1 1-2 3-3 4l-1 4-3 8v1h0c0 1 0 1-1 2 2 0 3-1 5-2s4-1 6-1l-2 2v1c2-1 2-2 4-2 0 10-12 15-9 26v1c1 2 1 2 3 3l3 2 4 2c0 1 1 1 1 1 2 1 3 2 3 3 3 2 6 4 9 4h2c1-1 1-1 1-2h0c1-1 2-1 3-2h2c1-1 2-1 3-3 1-1 1-3 0-5v-1l-2 1c0 1-1 1-2 1-3 0-5-2-6-4h-1s-1 0-1 1c-2-1-3-2-5-4h-1c-1-1-2-1-2-3 1-3 2-5 3-8s2-9 5-11c2-1 4-1 6-1h1v-1c2-4 6-6 7-10 2 0 3 1 5 1l12 6c0 3-1 7 0 10v14c-1 3-1 6-2 9-2 5-5 9-5 14l1 1h-7-98-29c-8 1-16 1-24 2l-6 1 1-2c1-5-1-10-3-14 2 1 2 2 3 4 0 0 1 0 2-1l-1-1c0-1-1-1-1-3h0v-1c-3-5-8-7-13-8 0-1 0 0 1-1l8-5c11-5 24-10 36-6 1 0 3 1 4 3l-1 1c1 1 1 3 2 4 1-1 3 0 5-1l2-2v1c0 2 1 2 3 3l2 1h3c-1 1-2 2-4 2 1 0 2 1 2 1l1 1c0 1 3 2 4 2h2l1-1 1 1c3 4 8 5 12 7 7 2 12 5 19 7-6-4-11-8-16-13-4-5-6-9-6-15 1-13 3-23 12-32 0 3-1 6-1 9-1 7-1 12 4 18l7 7c2 1 7 7 8 7-3-10-7-20-4-30 1-2 1-3 2-5s3-3 4-5c1 0 3 0 4 1-1 0-1 1-1 2 3-3 6-4 10-5h1c2-1 4-1 7-1z"></path><path d="M397 190h0c0 3-1 6 1 8h-1v3c-1-1-1-2-2-3l1-1-1-1v-4l1-1c0-1 0-1 1-1zm-4-2l1 2h0c-1 1-3 3-3 5 1 1 1 1 1 2h-1v2h-1c0-2-1-2-1-4h1v-1-2-1l3-3h0z" class="M"></path><path d="M345 181l1-1h3l1-1h1v1h1l1-1h1l1 2h-2-1l1 1 2 1v1c-1 0-1 0-2-1-2 0-4-1-5 0-1-1-2-1-3-2z" class="I"></path><path d="M384 188c2-2 3-3 5-4h2c0 2-2 1 0 3l2 1-3 3c1-1 0-1 1-2v-1l-1-1c-2 1-4 2-5 3l-1 1-1-1 1-2z" class="G"></path><path d="M345 181c1 1 2 1 3 2 2 1 4 2 6 4l1 1v1c-1 0-1-1-2 0h-1v-1c-1-1-1-1-2-1v2c-1 1-1 0-2 0l-1 1c-1-1-1-2-1-3l1-3h0l-1-1h0v-1l-1-1z" class="I"></path><path d="M345 181c1 1 2 1 3 2 2 1 4 2 6 4-3 0-5-2-7-3h0l-1-1h0v-1l-1-1z" class="P"></path><path d="M360 194c3 0 3 2 5 2h0 3l1-2v1 2c1 0 2 1 3 1l1 1c0 1 1 1 1 2h-1c-1-1-2-2-4-2h0c-2 0-4 1-5 1 0 0-2-1-3-1h0c-1-1-1-2-1-3l1-1-1-1z" class="I"></path><path d="M427 171c-3-1-4-4-6-6l1-1v-1c-1 0-1-1-1-2l-1-1v-2h0v-10h1c-1 7-1 12 4 18l7 7-2-1c-1 0-1-1-3-1z" class="R"></path><path d="M379 189l1 1h1l2-2h1l-1 2v1c-1 1-2 5-2 7h-1c-2 0-2 0-3-1 0-1 0-1-1-2 0-1 0-1 1-2v-1c0-1 1-2 2-3z" class="B"></path><path d="M351 194h2c1-1 3-3 5-4v1l-3 4h0l1 1c1-2 2-3 4-4 0-1 2-2 2-3h0c1 0 1-1 2 0l-2 2c0 1-1 2-2 2-1 1-2 3-3 4l3 2v1c-1-1-3-2-4-2h0-2 0c1 1 1 2 3 2l3 2v1c-2 0-7-3-7-5-1 0-1-1-1-1 0-2 0-2-1-3z" class="I"></path><path d="M342 190c4 5 4 9 4 15 1 1 1 0 1 1l-6 1 1-2c1-5-1-10-3-14 2 1 2 2 3 4 0 0 1 0 2-1l-1-1c0-1-1-1-1-3h0z" class="c"></path><path d="M348 183c1-1 3 0 5 0 1 1 1 1 2 1v-1c1 0 3 1 4 2l1 1-1 1-1-1c-1 0-1 0-2-1h-1c-1-1-2-1-3-1h0v1l3 1c1 1 1 2 2 3h1c1 0 1 0 2-1 1 0 1-1 2-1s2 1 3 2l1 1-1 1h-3l2-2c-1-1-1 0-2 0h0c0 1-2 2-2 3-2 1-3 2-4 4l-1-1h0l3-4v-1c-2 1-4 3-5 4h-2c0-2 2-4 4-5v-1l-1-1c-2-2-4-3-6-4z" class="G"></path><path d="M423 173h2l1 1c0 1-1 1-1 2 1 4 15 17 19 20l6 4c-3 0-5-2-7-3h0v1 1c-1 0-2 1-3 0s-2-2-2-3c2 1 2 2 4 2l-1-1c-1-1-2-1-2-2-4-6-11-10-14-16l-1-1c-1-2-1-3-1-5z" class="P"></path><path d="M397 190c1 1 1 1 3 2v1h1l2-3h1v1c1 1 2 2 1 4 0 1-1 1-1 2h4 1 0c-1 1-4 1-5 1 1 1 6 1 8 0l2 1v1c-1 1-1 1-3 1-3 1-5 1-8 0l-3-1h0c1 0 2-2 3-2 0-1-1-1 0-2 0 0 1-1 1-2s0-1-1-2l-1 1c-1 2-2 4-4 5-2-2-1-5-1-8h0z" class="B"></path><path d="M423 173h-1-1c-1-2-2-5-3-7v-1c-1-1-1-3-1-4h0v-1-1h1v4 1l2 4v1 1l2 2v-1h1c0 1 0 1 1 2l1-1 1 1c1-1 1 0 1-2 2 0 2 1 3 1l2 1c2 1 7 7 8 7 1 1 1 2 1 3-1 0-1 0-1-1l-1 1c0 1 1 2 2 3v1l-9-9-4-5-1 1c1 1 1 2 1 3h-1l-1-3-1-1h-2z" class="H"></path><path d="M362 179l5-1v1h4 1c-1 1-1 1-2 1h0c2 0 4 0 5 2 1 0 1 1 1 1l-1 2c-2 2-2 3-3 6l1 1-2 2h0c-2-1-3-2-3-3v-1l2 2h1c-1-2-2-3-3-4l-1 1c-1-1-2-1-3-2l1-1-1-1c-4-1-7-3-11-4h2l4 1h1l-2-2c2 0 3 0 4-1z" class="G"></path><path d="M362 179l5-1v1h4 1c-1 1-1 1-2 1h0c2 0 4 0 5 2 1 0 1 1 1 1l-1 2-1-1-6 1c-1-1-2-1-4-1v-1c-1-1-2-2-2-4z" class="J"></path><defs><linearGradient id="AP" x1="440.34" y1="194.566" x2="429.124" y2="177.099" xlink:href="#B"><stop offset="0" stop-color="#202022"></stop><stop offset="1" stop-color="#35393b"></stop></linearGradient></defs><path fill="url(#AP)" d="M426 174l1 3h1c0-1 0-2-1-3l1-1 4 5 9 9v-1c-1-1-2-2-2-3l1-1c0 1 0 1 1 1l1 1c1 0 1 0 1 1 2 3 3 6 5 9l-1 1c-1 0-1 1-1 1 2 1 3 2 5 3l-1 1h0l-6-4c-4-3-18-16-19-20 0-1 1-1 1-2z"></path><path d="M432 178l9 9v-1c-1-1-2-2-2-3l1-1c0 1 0 1 1 1l1 1c1 0 1 0 1 1 2 3 3 6 5 9l-1 1c-1 0-1 1-1 1-5-4-8-8-11-13-1-2-3-3-3-5z" class="M"></path><path d="M441 187v-1c-1-1-2-2-2-3l1-1c0 1 0 1 1 1l1 1 1 3c1 1 1 1 1 2-1 0-3-1-3-2z" class="G"></path><path d="M454 161c2 0 3-1 5-2s4-1 6-1l-2 2-2 1v2s-2 2-2 3c-3 2-4 5-5 9v2 5c-1 2-1 5 0 7 0 1 0 2 1 3 0 1 1 3 2 5h0v3h-1l-1-2c-2-2-3-4-4-6l-1-1c0-1-1-2-1-4h0l1 1c1-1-2-6-2-8-1-4 1-8 2-13h0c2-2 3-4 4-6z" class="I"></path><path d="M453 182v9l-2-6c-1-3-1-4 0-7l1 4h1z" class="B"></path><path d="M461 161v2s-2 2-2 3c-3 2-4 5-5 9v2l-1 5h-1l-1-4c0-1 0-3 1-4l3-5v-1c2-3 4-4 6-7z" class="J"></path><path d="M367 178c3-1 7-3 10-5 1 1 1 3 2 4 1-1 3 0 5-1l2-2v1c0 2 1 2 3 3l2 1h3c-1 1-2 2-4 2 1 0 2 1 2 1l1 1c0 1 3 2 4 2h2l1-1 1 1c3 4 8 5 12 7h-3 0c-3 0-4 0-6-1v-1h-1l-2 3h-1v-1c-2-1-2-1-3-2 0-2-1-2-2-3-1 0-1 0-2 1h0l-2-1c-2-2 0-1 0-3h-2c-2 1-3 2-5 4h-1l-2 2h-1l-1-1c2-1 3-3 5-4 2 0 4-1 6-3-6 1-10 2-14 7-1 1 0 4 0 6v1c1 1 1 2 1 3-1 0-1-2-2-3v-5h0v-2l-2 2h-1 0c1-3 1-4 3-6l1-2s0-1-1-1c-1-2-3-2-5-2h0c1 0 1 0 2-1h-1-4v-1z" class="D"></path><path d="M367 178c3-1 7-3 10-5 1 1 1 3 2 4 1-1 3 0 5-1l2 4h-1l1 1c-2 0-4 1-5 1-2 1-3 2-4 2l-1-1s0-1-1-1c-1-2-3-2-5-2h0c1 0 1 0 2-1h-1-4v-1z" class="d"></path><path d="M379 177c1-1 3 0 5-1l2 4h-1c-3 0-4-1-6-3z" class="B"></path><path d="M487 159h1l1 1h1l1 1-1 1h-2c0 2 1 4 1 5l-2-1c-1 1-2 2-2 3h-1-1v2h-1l-2 4c1 0 0-1 1-1 1-1 4 1 5 1 2 1 4 4 5 6s3 3 3 6v2h0v-1c-1-1-1-2-2-3h0c0 1 0 2 1 4h-1v-1l-2 1c0 1-1 1-2 1-3 0-5-2-6-4h-1s-1 0-1 1c-2-1-3-2-5-4h-1c-1-1-2-1-2-3l3-8c1-3 2-9 5-11 2-1 4-1 6-1h1v-1z" class="D"></path><path d="M484 161h1c1 2 2 2 2 4l-1 1h-2c-1-1-1-2-2-3l2-2z" class="I"></path><path d="M474 183v-2c0-2 2-7 4-9l1 1v2l-2 2 1 1c-1 2-2 3-3 5h-1z" class="B"></path><path d="M478 178l4-4c5 3 9 7 12 13v2h0v-1c-1-1-1-2-2-3h0c0 1 0 2 1 4h-1v-1l-2 1c0 1-1 1-2 1-3 0-5-2-6-4h-1s-1 0-1 1c-2-1-3-2-5-4 1-2 2-3 3-5z" class="I"></path><path d="M485 181l1-1c3 3 5 4 6 8l-2 1v-1c-2-2-3-5-5-7z" class="B"></path><path d="M485 181l-2-3c-1 1-2 3-3 4 0 1 0 1-1 3h0l-1-1c0-2 0-2 1-3v-1c1-1 2-3 4-4 1 1 2 3 3 4l-1 1z" class="G"></path><defs><linearGradient id="AQ" x1="444.123" y1="171.221" x2="456.455" y2="136.468" xlink:href="#B"><stop offset="0" stop-color="#323437"></stop><stop offset="1" stop-color="#5a5d60"></stop></linearGradient></defs><path fill="url(#AQ)" d="M458 139h7c1 0 2 1 3 1-2 1-4 1-6 2-1 1-2 3-3 4l-1 4-3 8v1h0c0 1 0 1-1 2-1 2-2 4-4 6h0c-1 5-3 9-2 13 0 2 3 7 2 8l-1-1-1-2v-1-3c-2-2 0-5-1-8h-1v-2h-1-1c-1-1-1-2-2-3v-1-1h0c-2-4-2-11-1-15h1c1-3 2-5 4-7 4-3 7-4 12-5z"></path><path d="M452 152l1 1-5 4-1-1c0-1 0-2 1-3l4-1z" class="M"></path><path d="M455 151l3-1-3 8v1h0c0 1 0 1-1 2-1 2-2 4-4 6v-1c0-1-1-1 0-2 1-2 1-4 2-6s1-3 1-5l2-2z" class="R"></path><path d="M452 152c1-1 2-1 2-2s-1-2-1-3l-3 1h-1 0c2-2 5-4 8-5 2-1 3-2 5-1-1 1-2 3-3 4l-1 4-3 1-2 2h0l-1-1z" class="B"></path><path d="M455 151v-4l4-1-1 4-3 1z" class="Q"></path><defs><linearGradient id="AR" x1="486.951" y1="187.992" x2="505.084" y2="160.708" xlink:href="#B"><stop offset="0" stop-color="#252427"></stop><stop offset="1" stop-color="#3d4244"></stop></linearGradient></defs><path fill="url(#AR)" d="M494 149c2 0 3 1 5 1l12 6c0 3-1 7 0 10v14c-1 3-1 6-2 9-2 5-5 9-5 14l1 1h-7l-2-1h0 3 1c0-1 1-3 2-4l1-2h0c1-1 1-2 1-3s1-1 1-2v-3l1-1-1-1v1c-1 4-3 7-5 11h-1c-2 0-2 3-5 3v-1h1c2-1 3-5 4-7 1-1 1-2 1-3l1-1v-2c1-3 1-9 0-13h0v-1c0-1-1-2-1-3 0-2-1-4-2-5h0l1 5c0 2 1 4 1 6h-1v-3h0l-2-6v-1c-1-2-1-4-2-5l-1-1c-1-1-1-3-3-3l-1 2h-1l-1-1h-1c2-4 6-6 7-10z"></path><path d="M496 154h2c3 1 6 5 7 9 0 1 1 2 1 4h0c1 5 2 14 0 19v2h0l-1-1v1c-1 4-3 7-5 11h-1l1-2c2-4 3-7 5-11 2-7 0-17-2-24-2-4-3-6-7-8z" class="I"></path><defs><linearGradient id="AS" x1="507.85" y1="201.953" x2="502.066" y2="150.654" xlink:href="#B"><stop offset="0" stop-color="#171819"></stop><stop offset="1" stop-color="#34383b"></stop></linearGradient></defs><path fill="url(#AS)" d="M499 150l12 6c0 3-1 7 0 10v14c-1 3-1 6-2 9-2 5-5 9-5 14l1 1h-7l-2-1h0 3 1c0-1 1-3 2-4l1-2h0c1-1 1-2 1-3s1-1 1-2v-3l1-1h0v-2c2-5 1-14 0-19h0c0-2-1-3-1-4-1-4-4-8-7-9h-2c-1 0-1 0-2-1h2 0l-1-1c1 0 2-1 3-1l1-1z"></path><path d="M272 408h1c-2-1-1-1-2-1s-3-1-5-2c-1 0 0 0-1-1h-1l-2-1h0c-2 0-4-1-5-2l-1-1v-1l2 1c1-1 1-2 1-3h1 0c-1-1-2-1-3-2h1l3 1h1 1c3 2 7 4 10 6 5 2 10 5 15 6 1 1 2 1 3 2l4 1h1 1l3 1h3c2 1 3 1 5 1 1-1 1-1 1-2l-1-1-1-1c0-3-2-5-4-7 1-1 3-1 4-1 1-1 2-2 2-3h1 2l2 1c1 1 2 2 4 3l9 6c1 3 3 4 5 6 2 0 2 1 4 1h1-1v-2c1 0 1 0 2 1s2 3 3 4v1l1 1c2 2 3 3 4 5h0l12 12c1 2 4 6 6 6 1 1 2 2 3 4h0c1 1 1 2 1 3l1 1 2 2c1 2 3 5 4 8 1 1 1 4 2 5h1 0c1 1 2 1 3 2 2 1 2 2 3 4l-1 5-5 19v1c0 2 0 3-1 5v1h1v-1l1-1c0-1 0-1 1-2l1-1-1 3 1-1c0-1 1-2 2-2l1-1c1 0 2 0 3-1 1 1 2 1 2 2s1 1 1 2c-1 2-1 3-1 5l-2 3-4 5c-18 22-29 48-54 62-17 10-39 12-58 6-20-6-37-23-48-41l1-1c9 12 16 22 29 29 16 10 36 14 54 9 20-6 37-18 47-35 9-16 13-36 8-54s-17-31-33-40c-1-3-1-3-3-5-2-1-4-3-7-4l-1 1c-4-2-8-1-13-3-6-1-13 0-20 1v1l-18 5c-3 1-7 3-9 5-1 1-2 2-3 2l-2 2c-2 1-4 3-6 4l3-3c0-4 2-5 4-8l4-4h1c0-1 1-1 2-2h0 0l1-1h-2c-6 3-12 7-16 14-1-3-3-6-5-8h-3c-4-3-7-9-12-11h-4v2c-2-1-4-1-6-1v-1c0-1 2-2 4-3 0-1 0-3 1-4l1-1c2-4 5-3 8-5 0 0 1-1 2-1 2-1 6-2 8-5 1-1 3-2 5-2 1-1 3-1 4-2 3-2 5-1 7-1 3 0 5 1 7 1 6 0 10 3 15 5 3 2 6 3 9 4 2 0 4 1 7 1-3-3-11-5-15-7 2 0 4 0 7 1v-1c-1 0-1-1-1-1v-1c-1 0-2-1-3-1l-8-3z" class="C"></path><path d="M378 466c1 1 2 1 3 2 2 1 2 2 3 4l-1 5c-1-1-1-2-2-3 0-1 0-1-1-2-1-2-1-4-2-6z" class="B"></path><path d="M363 470l4 9h0c1 5 2 10 2 15h-1c0-6-3-12-5-17l1-1-1-1v-5z" class="b"></path><path d="M377 469c3 7 0 17-2 25-2-3-1-6-1-9h0c2-2 3-9 3-11v-5h0z" class="B"></path><path d="M372 479l2 6c0 3-1 6 1 9 0 2-2 4-3 7 0 1 0 2-1 4 0-3-1-8 0-11h1v-4-11z" class="Z"></path><path d="M300 434h-2c-1-1-5-2-5-3 2 1 6 2 9 1l17 7-1 1c-4-2-8-1-13-3v-1c-1-1-3-1-5-2z" class="V"></path><path d="M364 459c3 3 5 9 7 13l2 5-1 2v11 4h-1c0-5-1-10-2-15l-1-1c0-1 0-3-1-4l2-3-5-12z" class="W"></path><path d="M369 471c1 2 2 4 1 6 0 1 0 1-2 1 0-1 0-3-1-4l2-3z" class="b"></path><path d="M384 497c1 0 2 0 3-1 1 1 2 1 2 2s1 1 1 2c-1 2-1 3-1 5l-2 3-4 5c-1-1-1-5 0-6h0v-1c1-1 1-2 1-3-2 1-3 2-4 4l-8 11 6-22v1c0 2 0 3-1 5v1h1v-1l1-1c0-1 0-1 1-2l1-1-1 3 1-1c0-1 1-2 2-2l1-1z" class="B"></path><path d="M380 507c1-2 1-3 3-5l5-4c-1 2-2 4-1 7v3l-4 5c-1-1-1-5 0-6h0v-1c1-1 1-2 1-3-2 1-3 2-4 4z" class="K"></path><defs><linearGradient id="AT" x1="339.016" y1="459.398" x2="345.79" y2="445.985" xlink:href="#B"><stop offset="0" stop-color="#858785"></stop><stop offset="1" stop-color="#c9c4bf"></stop></linearGradient></defs><path fill="url(#AT)" d="M314 429c8 4 15 8 22 13 5 3 10 7 15 11 5 6 9 11 12 17v5l1 1-1 1c-3-7-8-14-13-20-10-11-23-18-35-24h1c2 0 2 0 3 1l4 2v-1c-1-2-5-3-6-4-1 0-2 0-3-1h0v-1z"></path><defs><linearGradient id="AU" x1="352.135" y1="466.751" x2="358.369" y2="447.236" xlink:href="#B"><stop offset="0" stop-color="#88867d"></stop><stop offset="1" stop-color="#cbc8c3"></stop></linearGradient></defs><path fill="url(#AU)" d="M339 441c3 0 6 2 9 5h2c1 1 2 2 3 2 1-1 1-1 1-2l-1-1c1-1 1-1 2-1l-3-2v-1c2 1 3 2 4 3 2 2 4 5 5 7l-1 1-1-2-1 1 2 4h1c1 2 2 3 3 4l5 12-2 3c1 1 1 3 1 4h0l-1 1h0l-4-9c-3-6-7-11-12-17l1-1-13-11z"></path><path d="M352 452c7 7 11 13 15 22 1 1 1 3 1 4h0l-1 1h0l-4-9c-3-6-7-11-12-17l1-1z" class="D"></path><path d="M346 425l12 12c1 2 4 6 6 6 1 1 2 2 3 4h0c1 1 1 2 1 3l1 1 2 2c1 2 3 5 4 8 1 1 1 4 2 5v3h0v5c0 2-1 9-3 11h0l-2-6 1-2-2-5c-2-4-4-10-7-13-1-1-2-2-3-4h-1l-2-4 1-1 1 2 1-1c-1-2-3-5-5-7l2-1c-4-4-9-7-13-11 6 2 11 7 16 11-2-5-7-9-11-13-2-1-3-3-4-5z" class="R"></path><path d="M373 463c2 5 3 8 1 13-1 0-1 1-1 1l-2-5c1-1 2-2 2-3 1-2 0-4 0-6z" class="a"></path><defs><linearGradient id="AV" x1="374.868" y1="462.503" x2="357.252" y2="454.725" xlink:href="#B"><stop offset="0" stop-color="#84827e"></stop><stop offset="1" stop-color="#b9b9b2"></stop></linearGradient></defs><path fill="url(#AV)" d="M358 443c1 1 2 2 4 2v-1c2 1 10 15 11 18v1c0 2 1 4 0 6 0 1-1 2-2 3-2-4-4-10-7-13-1-1-2-2-3-4h-1l-2-4 1-1 1 2 1-1c-1-2-3-5-5-7l2-1z"></path><path d="M247 411c3-2 5-1 7-1 3 0 5 1 7 1 6 0 10 3 15 5 3 2 6 3 9 4 2 0 4 1 7 1l22 8v1h0c1 1 2 1 3 1 1 1 5 2 6 4v1l-4-2c-1-1-1-1-3-1h-1c-2 0-5-2-7-2-4-2-9-4-14-5-19-5-36-4-55-1-7 2-15 3-21 6 0-1 0-3 1-4l1-1c2-4 5-3 8-5 0 0 1-1 2-1 2-1 6-2 8-5 1-1 3-2 5-2 1-1 3-1 4-2z" class="L"></path><path d="M247 411c3-2 5-1 7-1 3 0 5 1 7 1 6 0 10 3 15 5 3 2 6 3 9 4 2 0 4 1 7 1l22 8v1c-2-1-5-2-6-3-4-2-11-4-16-5h-4c-2-1-4-1-6-2-5-2-10-6-15-7h-5-2-4-1c-2 0-4 1-6 1v-1l3-1v-1h-5z" class="E"></path><path d="M247 411h5v1l-3 1v1c2 0 4-1 6-1h1l-36 13c2-4 5-3 8-5 0 0 1-1 2-1 2-1 6-2 8-5 1-1 3-2 5-2 1-1 3-1 4-2z" class="O"></path><defs><linearGradient id="AW" x1="295.132" y1="429.919" x2="234.298" y2="433.927" xlink:href="#B"><stop offset="0" stop-color="#b9b7b1"></stop><stop offset="1" stop-color="#fefefe"></stop></linearGradient></defs><path fill="url(#AW)" d="M260 439c-8-1-17 1-25 0-1-1-1-1-1-3 1-4 5-6 8-8 16-7 44-1 60 4-3 1-7 0-9-1 0 1 4 2 5 3h2c2 1 4 1 5 2v1c-6-1-13 0-20 1v1l-18 5c-3 1-7 3-9 5-1 1-2 2-3 2l-2 2c-2 1-4 3-6 4l3-3c0-4 2-5 4-8l4-4h1c0-1 1-1 2-2h0 0l1-1h-2z"></path><path d="M267 431c-9 1-16 0-25 2v-1h0c4-2 20-3 24-2l1 1z" class="E"></path><path d="M254 446c5-2 8-5 13-6 1-1 2-1 4-1 0 0 1 1 2 1-3 1-4 2-6 4-3 1-7 3-9 5-1 1-2 2-3 2l-2 2c-2 1-4 3-6 4l3-3c0-4 2-5 4-8z" class="S"></path><path d="M266 430l20 3c3 0 6 0 9 1 2 0 3 1 5 0 2 1 4 1 5 2v1c-6-1-13 0-20 1v1l-18 5c2-2 3-3 6-4 1 0 3-1 5-2 1 0 2 0 3-1h1 2c2-1 5-1 7-1 1-1 3 0 4 0l-8-2c-2 0-4 0-5-1h-1c-5 0-10-1-14-2l-1-1z" class="O"></path><path d="M272 408h1c-2-1-1-1-2-1s-3-1-5-2c-1 0 0 0-1-1h-1l-2-1h0c-2 0-4-1-5-2l-1-1v-1l2 1c1-1 1-2 1-3h1 0c-1-1-2-1-3-2h1l3 1h1 1c3 2 7 4 10 6 5 2 10 5 15 6 1 1 2 1 3 2l4 1h1 1l3 1h3c2 1 3 1 5 1 1-1 1-1 1-2l-1-1-1-1c0-3-2-5-4-7 1-1 3-1 4-1 1-1 2-2 2-3h1 2l2 1c1 1 2 2 4 3l9 6c1 3 3 4 5 6 2 0 2 1 4 1h1-1v-2c1 0 1 0 2 1s2 3 3 4v1l1 1c2 2 3 3 4 5h0c1 2 2 4 4 5 4 4 9 8 11 13-5-4-10-9-16-11 4 4 9 7 13 11l-2 1c-1-1-2-2-4-3v1l3 2c-1 0-1 0-2 1l1 1c0 1 0 1-1 2-1 0-2-1-3-2h-2c-3-3-6-5-9-5l13 11-1 1c-5-4-10-8-15-11-7-5-14-9-22-13l-22-8c-3-3-11-5-15-7 2 0 4 0 7 1v-1c-1 0-1-1-1-1v-1c-1 0-2-1-3-1l-8-3z" class="L"></path><path d="M330 423c1 1 2 2 4 2 2 2 3 5 6 6l6 5 2 1c0 1 1 1 2 2l2 2v1l-4-3v1c-3-2-5-4-8-6s-7-5-10-8c0-1-1-1-2-2h1c1 1 2 2 4 3 0-1-2-3-3-4z" class="E"></path><path d="M330 423v-1c-3-1-5-4-6-7 1 2 3 3 5 4l1 1v-1l4 2c1 1 3 2 4 3 1 2 3 4 5 6 2 1 3 2 5 2 2 1 4 3 6 4h0c-2-3-6-6-9-9 0-1-2-3-3-4v-3c2 2 3 3 4 5h0c1 2 2 4 4 5 4 4 9 8 11 13-5-4-10-9-16-11 4 4 9 7 13 11l-2 1c-1-1-2-2-4-3l-2-2c-1-1-2-1-2-2l-2-1-6-5c-3-1-4-4-6-6-2 0-3-1-4-2z" class="F"></path><defs><linearGradient id="AX" x1="306.374" y1="417.023" x2="296.126" y2="426.477" xlink:href="#B"><stop offset="0" stop-color="#96918c"></stop><stop offset="1" stop-color="#afb0a6"></stop></linearGradient></defs><path fill="url(#AX)" d="M277 414c2 0 4 0 7 1v-1c-1 0-1-1-1-1v-1c13 5 26 11 39 18 5 3 11 7 17 11l13 11-1 1c-5-4-10-8-15-11-7-5-14-9-22-13l-22-8c-3-3-11-5-15-7z"></path><defs><linearGradient id="AY" x1="789.225" y1="344.888" x2="844.442" y2="430.183" xlink:href="#B"><stop offset="0" stop-color="#c2c1bb"></stop><stop offset="1" stop-color="#f2f2ee"></stop></linearGradient></defs><path fill="url(#AY)" d="M772 321l1-1h0 3c1 1 2 1 2 1 3 2 7 4 10 6h1 1c1-1 1-1 3-1 3 1 6 4 10 3 11 6 18 15 25 25l4 2 1-1 1 1h0c1 1 4 4 4 5 1 2 2 3 3 5 2 6 13 23 18 25h1l1 1h1 1 1l2 2-3-1-1 1v1 1c-1-1-1-1-1-3h-1c-1 0-2-2-3-3-3-3-6-6-8-9v4h1v1l1 1h1c2 4 6 6 9 10l1 1v2l1 1h-1c-1-1-1-1-1-2-1-1-2-1-2-3-1-1-2-1-3-2-2-1-4-3-5-4v-1c-2-1-2-2-3-4h0c-1-1-1-3-1-4v-1h0v-2l-1-1h0l-3-6c-2-2-3-4-4-7 0-1-1-1-1-2v3l1 1v1 1h0l1 1v1 1h0l1 1c0 1 1 2 1 4 0 1 0-1 1 1 0 1 0 2 1 3l1 2 2 4 1 2c1 2 2 4 2 7v1l1 1v1h0l1 3 1 1v1l3 3c1 1 3 2 4 3h1c1 0 2 1 3 1h1c1 0 3 1 4 0h1 0c1 1 1 1 3 1h0 0c3 0 5-1 8-1-3 3-9 3-12 3-2 1-4 1-5 2-2 2-3 6-3 8 0 1 1 2 1 3-3-1-6-3-8-4-2 1-3 1-4 3h0c-1 3-1 5-3 7l-1 1h0c-2 0-3 2-4 3h-1c-1 0-2 0-3 1v-1h-2-1v-1l-1-1-1-1c-10-4-20-7-31-9 0 1-1 1-1 2l-13-2h-8l-5 1h-2l-5 1-8 1v-1-11c3-1 6-2 10-4 2-1 5-2 7-4h0-1c-2 1-1 0-2 0s-1 1-2 1c2-1 4-2 5-4l1-1h-3v1l-2 1h-1c-1-2-1-5-2-7 0-1 0-2 1-2l1-3c1-2 1-3 2-4l-2-1c2-1 2-3 3-4l2-2 1-2h0l1-2c1-3 1-8 0-11-2-4-12-3-16-5v-1c1-2 3-4 4-6s2-3 3-4l1-2v-2c1-1 1-1 1-2v-24z"></path><path d="M836 382c0 1 1 7 1 7 0 3-1 5-2 8 0-4-1-9 0-13l1-2z" class="N"></path><path d="M843 420v-3h0v8c3-1 5-4 9-5l-1 1c-1 2-5 5-7 6h0l-1 1c-1 0-1-1-1-2 1-2 1-4 1-6z" class="S"></path><path d="M837 365c1 0 2 3 2 4 1 3 2 7 4 10v2h0-1v1c1 2 1 3 1 5 0 1 1 2 0 3h0l-1-3c0-2-1-4-2-6v-1c-1-2-2-4-1-6 0-1 0-2-1-3 0-2-1-4-1-6z" class="F"></path><path d="M798 411c2 0 4 0 6 1 0 1 0 2 1 3l2 1c-1 1-1 1-3 0h0-6l-2-1-6-3h5l3-1z" class="V"></path><path d="M798 411c2 0 4 0 6 1 0 1 0 2 1 3l-10-3 3-1z" class="F"></path><path d="M832 430l3 3c1 1 0 1 2 2h1v-1-1c-1-1-1-11 0-13v-1c0-1 0-1 1-2 1 1 1 9 1 11l1-1c1 0 1-5 1-7h1c0 2 0 4-1 6 0 1 0 2 1 2l-4 6c0 1 0 2-1 2h0-2-1v-1l-1-1-1-1h0c-1-1-1-2-1-3z" class="E"></path><path d="M843 428l1-1h0c2-1 6-4 7-6l3 1c-2 1-3 1-4 3h0c-1 3-1 5-3 7l-1 1h0c-2 0-3 2-4 3h-1c-1 0-2 0-3 1v-1h0c1 0 1-1 1-2l4-6z" class="U"></path><path d="M828 354l4 2 1-1 1 1h0v3l1 2 1 2 1 2c0 2 1 4 1 6 1 1 1 2 1 3-1 2 0 4 1 6-2 2-1 9-1 12v5c-1-1-1-3-1-4v-6h-1v2s-1-6-1-7c0-2 0-5-1-7-1-8-4-14-7-21z" class="E"></path><path d="M807 416c7 3 12 2 19 2l1 3c0 2 2 4 3 6l-23-8-9-3h6 0c2 1 2 1 3 0z" class="g"></path><path d="M827 368c3 7 2 16 1 23-2 6-3 10-8 13-4 3-8 5-13 5 2-2 6-4 9-6 8-6 8-17 11-26h0v-2-5-1-1z" class="P"></path><path d="M790 327c1-1 1-1 3-1 3 1 6 4 10 3 11 6 18 15 25 25 3 7 6 13 7 21 1 2 1 5 1 7l-1 2c0-9-3-16-5-24-2-1-3-2-4-4 0-2-1-4-3-6-1-2-4-4-5-6-1-3-8-8-11-10h-4v1l-8-6-5-2z" class="O"></path><path d="M795 329h1c2 1 3 1 4 1 3 1 5 3 7 4h-4v1l-8-6z" class="X"></path><path d="M852 420c3-4 2-11 1-15h0l3 2c4 3 9 5 13 4h4 0c3 0 5-1 8-1-3 3-9 3-12 3-2 1-4 1-5 2-2 2-3 6-3 8 0 1 1 2 1 3-3-1-6-3-8-4l-3-1 1-1z" class="C"></path><path d="M827 377c-3 9-3 20-11 26-3 2-7 4-9 6-1 1-2 1-3 1h-6v1l-3 1h-5l-2-2h2l3-1c1 1 1 1 2 0-2-1-4-1-6-1v-1c-1 0-2 1-2 0h-1c0-1 2-2 2-2 5-1 11-1 16-2 0-2 0-3 1-5s2-4 3-5l1-1c6-2 12-7 15-12l3-3z" class="c"></path><path d="M789 407l5-1c2 0 7 0 9 1 0 1 0 1 1 1-1 1-2 1-3 1h0l3 1h-6v1l-3 1h-5l-2-2h2l3-1c1 1 1 1 2 0-2-1-4-1-6-1v-1z" class="F"></path><path d="M788 410h2 8v1l-3 1h-5l-2-2zm17-12c4-1 5-5 9-7 2-1 4-2 6-2 0 1-1 3-2 4l-1 1c-1 2-3 3-5 5l-1 1c-1 1-2 1-4 2-1 1-2 0-3 1 0-2 0-3 1-5z" class="E"></path><defs><linearGradient id="AZ" x1="773.823" y1="414.591" x2="805.717" y2="425.315" xlink:href="#B"><stop offset="0" stop-color="#b3b2ab"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#AZ)" d="M781 406c1 1 5-1 7-1 0 0-2 1-2 2h1c0 1 1 0 2 0v1c2 0 4 0 6 1-1 1-1 1-2 0l-3 1h-2l2 2 6 3 2 1 9 3 23 8 2 2v1c0 1 0 2 1 3h0c-10-4-20-7-31-9 0 1-1 1-1 2l-13-2h-8l-5 1h-2l-5 1-8 1v-1-11c3-1 6-2 10-4 2-1 5-2 7-4l3-1h1z"></path><path d="M774 423l14-1 1 2h-1 0-8l-5 1h-2l-5 1-8 1v-1c2-1 4-1 6-2l8-1z" class="C"></path><path d="M766 424c0-1 0-3 1-4v-1c2-3 5-6 8-7l1 1c-2 3-5 6-6 10h4l-8 1z" class="V"></path><defs><linearGradient id="Aa" x1="762.082" y1="413.909" x2="768.312" y2="418.62" xlink:href="#B"><stop offset="0" stop-color="#5f5d5c"></stop><stop offset="1" stop-color="#81807c"></stop></linearGradient></defs><path fill="url(#Aa)" d="M781 406c1 1 5-1 7-1 0 0-2 1-2 2h1c0 1 1 0 2 0v1c2 0 4 0 6 1-1 1-1 1-2 0l-3 1h-2-5c-2 1-5 1-6 2l-1 1-1-1c-3 1-6 4-8 7v1c-1 1-1 3-1 4-2 1-4 1-6 2v-11c3-1 6-2 10-4 2-1 5-2 7-4l3-1h1z"></path><path d="M781 406c1 1 5-1 7-1 0 0-2 1-2 2h1c0 1 1 0 2 0v1c2 0 4 0 6 1-1 1-1 1-2 0l-3 1h-2-5-1l-1-1c-1 1-5 1-6 1l1-1c1 0 2-1 3-1v-1l1-1h1z" class="g"></path><path d="M787 407c0 1 1 0 2 0v1c2 0 4 0 6 1-1 1-1 1-2 0-2-1-7 0-10 0v-1c1 0 2 0 4-1z" class="f"></path><path d="M796 415l2 1 9 3 23 8 2 2v1c0 1 0 2 1 3h0c-10-4-20-7-31-9 0 1-1 1-1 2l-13-2h0 1l-1-2h-3 0c1-1 4-1 5 0h1c1-1 3-2 4-2h1 3c3 1 6 2 9 2-3-1-6-2-8-3s-4-1-5-2h0 2c0-1-1-1-1-2z" class="L"></path><path d="M788 422c5 1 9 1 14 2 0 1-1 1-1 2l-13-2h0 1l-1-2z" class="P"></path><path d="M818 351c4 6 7 11 9 17v1 1 5 2h0l-3 3c-3 5-9 10-15 12l-1 1c-1 1-2 3-3 5s-1 3-1 5c-5 1-11 1-16 2-2 0-6 2-7 1h-1l-3 1h0-1c-2 1-1 0-2 0s-1 1-2 1c2-1 4-2 5-4l1-1c2-1 4-3 6-4s2-1 2-2h1l2-2c0-1 1-2 2-2 1-1 2-2 3-2l1-1 2-2 3-1c0-2 1-2 2-3s1-2 2-3c1-3 2-4 3-7 2-4 4-9 5-14h0 1c1-2 1-2 1-3h1v-5c1 0 2 0 3-1z" class="T"></path><path d="M827 370v5c-1 1-3 3-4 5l-3 1c2-4 6-7 7-11zm-23 21c3-5 6-6 11-8-1 2-2 3-3 5l-4 2-4 1z" class="L"></path><path d="M818 351c4 6 7 11 9 17v1c-2-2-3-5-5-7l-2 1h0c0-3 1-5-1-8h-1l-2 2h-1v-5c1 0 2 0 3-1z" class="C"></path><path d="M827 375v2h0l-3 3c-3 5-9 10-15 12l-1 1v-2-1l4-2c1-2 2-3 3-5l5-2 3-1c1-2 3-4 4-5z" class="G"></path><path d="M820 381l3-1c-3 4-7 6-11 8 1-2 2-3 3-5l5-2z" class="E"></path><path d="M804 391l4-1v1 2c-1 1-2 3-3 5s-1 3-1 5c-5 1-11 1-16 2-2 0-6 2-7 1 3-2 6-4 10-6 4-3 8-7 13-9z" class="C"></path><defs><linearGradient id="Ab" x1="765.14" y1="367.996" x2="801.3" y2="381.533" xlink:href="#B"><stop offset="0" stop-color="#d6d5ce"></stop><stop offset="1" stop-color="#f8f8f6"></stop></linearGradient></defs><path fill="url(#Ab)" d="M772 321l1-1h0 3c1 1 2 1 2 1 3 2 7 4 10 6h1 1l5 2 8 6c5 5 11 10 15 16-1 1-2 1-3 1v5h-1c0 1 0 1-1 3h-1 0c-1 5-3 10-5 14-1 3-2 4-3 7-1 1-1 2-2 3s-2 1-2 3l-3 1-2 2-1 1c-1 0-2 1-3 2-1 0-2 1-2 2l-2 2h-1c0 1 0 1-2 2s-4 3-6 4h-3v1l-2 1h-1c-1-2-1-5-2-7 0-1 0-2 1-2l1-3c1-2 1-3 2-4l-2-1c2-1 2-3 3-4l2-2 1-2h0l1-2c1-3 1-8 0-11-2-4-12-3-16-5v-1c1-2 3-4 4-6s2-3 3-4l1-2v-2c1-1 1-1 1-2v-24z"></path><path d="M785 366h1c0 1 1 2 1 3v1c1 4 0 8-2 12-3 4-6 6-9 9-2 2-3 3-4 5h-1v1l-1 1c0-1 0-2 1-2l1-3c1-2 1-3 2-4 1-2 3-3 5-5-1 2-3 4-4 6l1-1c4-2 9-8 10-11v-1-6-1c0-1 0-2-1-4z" class="E"></path><path d="M775 343l2-1c1 1 1 2 1 3 1 0 2 1 3 1 2 2 4 4 4 6l-1 1c-1-2-3-4-5-4h-1 1l-6-2c1 1 2 2 3 4h1c-2 0-3-1-4-2h-2v-2c1-1 1-1 1-2l2-1 1-1z" class="O"></path><path d="M783 369c0-3 0-4-1-6h-1v-1h0v-1c1 1 2 3 3 4v1h1c1 2 1 3 1 4v1 6 1c-1 3-6 9-10 11l-1 1c1-2 3-4 4-6s3-5 4-7v-8z" class="L"></path><path d="M794 349c2 0 2 0 3 1h1v1c1 0 1 1 1 2l2 1 1 2c1 1 2 2 2 4h1v-4l2 4c0 2 0 5-1 7v1c0 1 0 2-1 3v-1h0v-3h-2c-1 1-1 2-1 2 0 2-1 2-2 3l1-1v-2c0-1 0-3 1-4h0v-3c-1-1-1-3-1-4l-1-1c0-1-1-2-1-4-1-1-3-3-5-4z" class="F"></path><defs><linearGradient id="Ac" x1="771.734" y1="352.097" x2="779.986" y2="359.613" xlink:href="#B"><stop offset="0" stop-color="#9f9c95"></stop><stop offset="1" stop-color="#c3c1ba"></stop></linearGradient></defs><path fill="url(#Ac)" d="M773 349c1 1 2 2 4 2 3 3 8 10 9 15h-1-1v-1c-1-1-2-3-3-4v1h0v1h1c1 2 1 3 1 6-2-6-4-8-9-12-1-1-2-1-3-1l-1-2h0c1-1 2-1 2-2 1 0 1-2 1-3z"></path><path d="M775 343l-1-1h2l-1-2v-2h-1v-1h1 1l1 1h1 2c-1-1-2-1-2-1l-2-1h0 3l2 1c1 0 1 1 3 1h0 0 2 1 1l1 1c1 2 4 2 5 4 1 1 1 3 3 4l2 2v1c1 1 2 2 2 4l-2-1c0-1 0-2-1-2v-1h-1c-1-1-1-1-3-1h0c-2-1-2-3-3-5l-10-5h-4l-1 2h1l1-1c1 1 2 2 3 4 0 0 1 0 1 1 2 1 2 2 3 3v1c0 1 1 1 0 3 0 0 1 1 1 2h-1s0-1-1-1l1-1c0-2-2-4-4-6-1 0-2-1-3-1 0-1 0-2-1-3l-2 1z" class="N"></path><defs><linearGradient id="Ad" x1="762.486" y1="367.216" x2="782.304" y2="372.464" xlink:href="#B"><stop offset="0" stop-color="#878582"></stop><stop offset="1" stop-color="#c2c0b8"></stop></linearGradient></defs><path fill="url(#Ad)" d="M770 351l1-2h2c0 1 0 3-1 3 0 1-1 1-2 2h0l1 2c1 0 2 0 3 1 5 4 7 6 9 12v8c-1 2-3 5-4 7-2 2-4 3-5 5l-2-1c2-1 2-3 3-4l2-2 1-2h0l1-2c1-3 1-8 0-11-2-4-12-3-16-5v-1c1-2 3-4 4-6s2-3 3-4z"></path><path d="M763 361c1-2 3-4 4-6 1 1 1 1 1 2-1 2-3 3-5 4z" class="a"></path><path d="M770 351l1-2h2c0 1 0 3-1 3 0 1-1 1-2 2h0l1 2v1c-3-3-1-3-1-6z" class="V"></path><defs><linearGradient id="Ae" x1="772.99" y1="329.267" x2="785.985" y2="329.233" xlink:href="#B"><stop offset="0" stop-color="#a4a19a"></stop><stop offset="1" stop-color="#d9d8d3"></stop></linearGradient></defs><path fill="url(#Ae)" d="M772 321l1-1h0 3c1 1 2 1 2 1 3 2 7 4 10 6h-2c-1 3 0 7 1 10l1 1h-1-1-2 0 0c-2 0-2-1-3-1l-2-1h-3 0l2 1s1 0 2 1h-2-1l-1-1h-1-1v1h1v2l1 2h-2l1 1-1 1-2 1v-24z"></path><g class="L"><path d="M788 338l-1-1c-1-3-2-7-1-10h2 1c4 3 9 6 12 11 2 3 3 9 4 13 0 2 1 3 0 5v4h-1c0-2-1-3-2-4l-1-2c0-2-1-3-2-4v-1l-2-2c-2-1-2-3-3-4-1-2-4-2-5-4l-1-1z"></path><path d="M789 327h1l5 2 8 6c5 5 11 10 15 16-1 1-2 1-3 1v5h-1c0 1 0 1-1 3h-1 0c-1 5-3 10-5 14-1 3-2 4-3 7-1 1-1 2-2 3v-2c1-2 1-3 1-5h0v-2c1-1 0-2 0-3 1-1 1-3 1-4-2 1-2 5-3 7l-1 1 1 1-1 1c0 1-1 2-2 3h0c-1 1-2 1-3 2l-1-1c2-1 3-3 4-5v-1c1-1 1-1 1-2s1-1 1-2c1-1 2-1 2-3 0 0 0-1 1-2h2v3h0v1c1-1 1-2 1-3v-1c1-2 1-5 1-7l-2-4c1-2 0-3 0-5-1-4-2-10-4-13-3-5-8-8-12-11z"></path></g><path d="M789 327h1l5 2 8 6c5 5 11 10 15 16-1 1-2 1-3 1-4-5-9-10-13-13 3 7 4 13 5 21l-2-4c1-2 0-3 0-5-1-4-2-10-4-13-3-5-8-8-12-11z" class="Z"></path><defs><linearGradient id="Af" x1="579.043" y1="206.982" x2="587.264" y2="119.782" xlink:href="#B"><stop offset="0" stop-color="#141516"></stop><stop offset="1" stop-color="#444649"></stop></linearGradient></defs><path fill="url(#Af)" d="M481 129c2-5 5-5 8-7-2-2-12 3-15 4-4 1-8 1-12 1l-1-2c-1-3-1-4 1-7 3-5 10-7 15-8 9-2 20 1 27 6v1l1-1v1c-1 0-1 0-1 1h0c4 3 6 7 8 11l1-1h0c0-2 0-4 1-6h0v-1l1-2v-3h0c1-1 1-1 2 0h0c-1 1-2 2-2 3 2-1 3-3 4-4v-1c1-2 1-2 4-2-2 1-3 3-4 5 0 2-1 2-1 4l1 1c-1 2-3 5-4 7v1c1 2 0 4-1 7h0c2-1 2-3 3-4s1-1 1-2c1 0 1-1 1-1l1-1c1-1 3-2 5-4 1-1 2-1 4-2h3c1 0 1 1 2 1 2 0 4 1 5 2h2v1c1 1 3 2 4 2v1c-3 1-6 2-8 3h0c4 0 8-1 11 2l1 3 1 4c-1 1-2 2-3 4v-1h-7-1l-2 2c-2-1-4-1-6 0l1 3-1 1h0c1 0 2 1 2 1 2 1 5 4 5 6v1c3 1 7-1 9 2 3 3 4 10 6 13 1 3 2 5 2 7-2 2-4 1-6 3-1 0-2 2-2 2l-2 1-1-1-1 1h0c-2 2-2 2-4 2l-1-1c-2 2-2 3-2 6 2 2 3 2 5 3 2 0 4-1 6-1 3-3 7-3 11-4l6-6c2 0 3 1 4 1 2-8 0-12-5-18-1-2-3-4-4-7-1-1-1-4-1-5 1-1 2-1 3-1 3 0 7 0 9 3h0l-1-4c0-4-2-8-3-11l-2-2c-2-1-4-1-6-2-2 0-5 0-7-1 1-1 1-1 2-3 3-3 11-4 15-4 7 0 12 3 17 8s7 12 7 18c0 9-3 16-6 24h0 0c1 1 0 1 1 2l1 1c-1 2-2 3-2 5l1 2h1c5-5 11-11 14-17 0-1 0-1 1-2l2 2c1-1 1-1 2-1 0-1 1-2 0-3 1-1 1-1 1-2h0 1v3h0c1-1 1-3 2-4 1 1 1 4 1 6 0-2 1-3 2-4 0 2 0 3 1 5l1 1c-3 11-14 18-23 24l18-7 4-2v1c2 0 2 0 3-1 3-1 4-2 6-3s3-2 5-2c2-1 3-2 6-2h0c1 1 1 5 1 7 1-1 2-3 3-4 2-2 3-4 5-5h1v1c3 5 6 7 11 8-1-3-4-7-3-10 3-2 13 0 16 1l2-1v-2c0-1 0-2-1-4 4 2 7 4 11 5 2 0 4 0 5 1 4 0 8-1 11 0l1 1v-1h2v1l-1 1-1 1v1h0c2-2 2-2 5-3v-1l2-1c1 0 2 1 3 1v1h-2l-2 2h-1s-1 1-1 2h-1v1c-2 4-3 6-4 10v3 5l-180-1h-5-7l-1-1c0-5 3-9 5-14 1-3 1-6 2-9v-14c-1-3 0-7 0-10l-12-6c-2 0-3-1-5-1v-3h-3c-2-2-3-2-6-1-2 0-2 0-4-1l-1 1c-1-2-1-3-2-4l-2-1c0-3 0-4 2-6 2-3 8-2 11-2-2-1-5-1-7-2l-1-1z"></path><path d="M583 153l2 2v1 6h1c0 2-1 4-1 6-1-2 0-5-1-5l-1-10z" class="J"></path><path d="M547 196v5h-1c-2-1-4-2-5-4 2 0 4-1 6-1z" class="Q"></path><path d="M662 187l8 1c-1 1-3 2-5 3 0-1 0-1-1-2-1 0-2 0-3-1l1-1z" class="J"></path><path d="M520 129c1-1 3-2 5-4l1 2c1-1 3-1 5-1-4 2-7 3-10 4l-1-1z" class="Y"></path><path d="M677 194c1 2 2 3 3 4-2 1-5 1-7 1v-1l1-1 3-3z" class="M"></path><path d="M679 191l3 2 1-1 2 2-5 4c-1-1-2-2-3-4 1-1 2-1 2-3z" class="B"></path><path d="M519 162v-2l1-1c0 1 0 3 1 3l1-2 1 1h0l-1 2-1 1v2c0 1 1 2 0 3-1 2-1 9 0 11v1 3c-1-3-1-7-1-11 0-3-1-6-1-9v-2z" class="G"></path><path d="M529 123h3c1 0 1 1 2 1 2 0 4 1 5 2h-8c-2 0-4 0-5 1l-1-2c1-1 2-1 4-2z" class="D"></path><path d="M674 191l3-3c2-1 3 0 5 1-1 0-2 0-3 1v1c0 2-1 2-2 3l-3 3-1 1h-1l2-2v-1l1-1c-1-2-2-1-3-2h2v-1z" class="H"></path><path d="M664 186c2 0 5-1 7 0 3 0 4-2 6-3h1l1 1c-1 1-3 2-5 3v2h-2 0v-1h-2l-8-1-1-1h3z" class="K"></path><path d="M640 190c0-1 0-1 2-2 0 2 1 5 1 8v2c1 1 0 2 0 3h-1c-1 0-1-1-2-1v-1c0-1-1 0 0-1v-1c-1-2 0-5 0-7z" class="I"></path><path d="M519 115v-1c1-2 1-2 4-2-2 1-3 3-4 5 0 2-1 2-1 4l1 1c-1 2-3 5-4 7v1 1c-1-2-1-3-1-4 1-1 0-3 1-4 0-1 0 0 1-1v-1c1-2 3-4 3-6z" class="J"></path><path d="M558 140c-2 0-5 0-7-1 1-1 1-1 2-3 4 2 7 1 11 1h1c1 1 2 1 3 2-3 0-7-1-10 1z" class="D"></path><path d="M471 116c8 0 18 2 26 5h-1-2c-2-1-5-2-7-2h-2 0v1h-1c-1 0-2-1-3 0-2 0-4 1-5 0 1 0 0 0 1-1h-1c0-1 1-1 2-2h-4c-1-1-1 0-2 0h-3c0-1 1-1 2-1z" class="H"></path><path d="M639 187c2-2 3-4 5-5h1v1c0 3 0 4 3 6l1 1c1 0 1 1 2 1l1 1h1l-1 4h-1c0-1 0-1-1-2-2-1-3-2-3-4l-1-1c0-1-1-1-1-2s0-1-1-2l-1 1c-1 0-3 1-4 1z" class="J"></path><path d="M471 116l1-1c11-1 21 2 31 6h0c-1 0-1 0-2-1h-3v1h-1c-8-3-18-5-26-5z" class="K"></path><path d="M618 195l3-3h1c2 2 5 1 7 2l-1 3s-1 0-1 1l-1-1c-1 0-2 0-3-1h0v1h-2l2 1h0-2c-1 0-2 1-4 0-1 0-2 0-2-1v-1c1 0 2 0 3-1h0z" class="I"></path><path d="M575 141c3 1 5 2 7 4v1l1 1c2 2 4 4 5 7h1v-2l1 1v5 1l-1-2c0-1-1-2-2-3 0 2 0 4-1 7v1h-1v-6-1l-2-2v-3c-1-4-5-7-8-9z" class="J"></path><path d="M583 150c1 1 2 1 3 3v1 7 1h-1v-6-1l-2-2v-3z" class="D"></path><path d="M530 168v-1c1-1 1-4 3-4 1-1 1-2 2-3h-1v-1h2v2h1l1 1c-2 4-5 8-6 13-1 2-2 6-3 9 0-4 1-7 2-11 1-2 3-5 3-7l-3 6c-1 0-1 0-1 1h-1-1l2-5z" class="H"></path><path d="M531 172l3-6c0 2-2 5-3 7-1 4-2 7-2 11 0 5 0 10 3 15 0 0 0 1 1 2h-2l-3-6c-1-4 0-9 0-13-1-1-1-3-1-4l1-5h1 1c0-1 0-1 1-1z" class="C"></path><path d="M528 173h1 1c0-1 0-1 1-1-2 3-2 6-3 10-1-1-1-3-1-4l1-5z" class="D"></path><path d="M682 189c1-1 2-1 3-2 1 0 2-1 3-2l2 1c-1 1-2 1-2 3h0l1 1h0c1-1 2-1 3-1l1-1c-1 2-2 3-3 4 0 1 0 1-1 2 0 0-1 1-2 1s-1-1-2-1l-2-2-1 1-3-2v-1c1-1 2-1 3-1z" class="G"></path><path d="M689 194v-2c-2 0-3-1-4-2v-1h1c1 1 1 2 3 2 1 0 2-1 3-2l1-1c-1 2-2 3-3 4 0 1 0 1-1 2zm-128-38c3 0 7 0 9 3h0c1 3 3 5 4 7 2 3 3 7 4 10v9c-1 0-1 1-1 2l-1-1c1-5 0-11-3-15l-2-3c-1-3-5-5-7-7-1-1-2-3-3-3h0v-2z" class="I"></path><defs><linearGradient id="Ag" x1="693.724" y1="187.807" x2="676.616" y2="181.635" xlink:href="#B"><stop offset="0" stop-color="#191a1b"></stop><stop offset="1" stop-color="#3f4346"></stop></linearGradient></defs><path fill="url(#Ag)" d="M681 180c2 0 4 0 5 1 4 0 8-1 11 0l1 1-5 6-1 1c-1 0-2 0-3 1h0l-1-1h0c0-2 1-2 2-3l-2-1c-1 1-2 2-3 2-1 1-2 1-3 2-2-1-3-2-5-1l-3 3v-2-2c2-1 4-2 5-3l-1-1c1-1 2-1 3-3z"></path><path d="M681 180c2 0 4 0 5 1-2 1-4 2-7 3l-1-1c1-1 2-1 3-3zm-93 12c5-5 11-11 14-17 0-1 0-1 1-2l2 2c-4 7-10 14-17 19l-4 3c1 1 0 0 2 1h-2c-4 0-5 3-9 4 0 0-1 0-2-1l3-2c3-2 4-4 6-7l2-2 2-2-1-1-1 1-1-1 2-4c0-1 0-2 1-2v1h0c1 1 0 1 1 2l1 1c-1 2-2 3-2 5l1 2h1z" class="P"></path><path d="M588 192l-8 6c-1 1-2 2-4 2l10-10 1 2h1zm-50-4c0-2 0-3 1-5l1-1c0-2 3-4 4-5v-1h-1-1c2-1 3-1 5-1 1 0 1 1 2 1h1l1 1s1 1 2 1c0-1-1-2-1-3l1-1c1 3 2 5 2 7-2 2-4 1-6 3-1 0-2 2-2 2l-2 1-1-1-1 1h0c-2 2-2 2-4 2l-1-1z" class="K"></path><path d="M546 179h2l1 1c-2 0-3 1-3 2l-1 1v1l-1-2c0-2 0-2 2-3z" class="B"></path><defs><linearGradient id="Ah" x1="504.706" y1="143.142" x2="488.234" y2="153.456" xlink:href="#B"><stop offset="0" stop-color="#040405"></stop><stop offset="1" stop-color="#323337"></stop></linearGradient></defs><path fill="url(#Ah)" d="M481 144c0-1 0-1 1-2v1c6-1 10-1 17 0 4 2 8 4 11 7 1 2 1 4 1 6l-12-6c-2 0-3-1-5-1v-3h-3c-2-2-3-2-6-1-2 0-2 0-4-1z"></path><defs><linearGradient id="Ai" x1="522.134" y1="144.029" x2="539.37" y2="152.462" xlink:href="#B"><stop offset="0" stop-color="#101317"></stop><stop offset="1" stop-color="#2f2d2c"></stop></linearGradient></defs><path fill="url(#Ai)" d="M515 152l2-2c3-4 7-5 12-7s11-3 16-1c1 1 1 1 2 3h-7-1l-2 2c-2-1-4-1-6 0l1 3-1 1c-2-1-3-1-5 0h-1c-3 1-8 4-9 7l-1 1c0-1 0-2 1-3l1-1c1-1 0-1 0-2s5-5 6-5h1c1-1 1-1 2-1l1-1v-1c-2 0-3 1-5 2-3 2-6 4-7 7v2h-1c0-2 0-3 1-4z"></path><path d="M547 196c3-3 7-3 11-4l6-6c2 0 3 1 4 1 0 2 0 3-1 5v1c-1 2-3 4-5 6h0-1c-1 1-2 1-3 1-2-1-2-1-3-1v-1c-3 1-5 1-7 3h-1v-5z" class="K"></path><defs><linearGradient id="Aj" x1="599.913" y1="194.134" x2="599.087" y2="168.382" xlink:href="#B"><stop offset="0" stop-color="#131316"></stop><stop offset="1" stop-color="#484c4d"></stop></linearGradient></defs><path fill="url(#Aj)" d="M609 169v3h0c1-1 1-3 2-4 1 1 1 4 1 6-1 1-1 4-2 5-2 4-6 6-9 9v1c-1 1-3 2-4 2-4 3-7 5-11 7-2-1-1 0-2-1l4-3c7-5 13-12 17-19 1-1 1-1 2-1 0-1 1-2 0-3 1-1 1-1 1-2h0 1z"></path><path d="M670 175c4 2 7 4 11 5-1 2-2 2-3 3h-1c-2 1-3 3-6 3-2-1-5 0-7 0-1 0-4 0-5-1l-1-1h-1-1v2h-1c1 3 3 4 2 7l-1-1h-3-1l-1-1c-1 0-1-1-2-1l-1-1c-3-2-3-3-3-6 3 5 6 7 11 8-1-3-4-7-3-10 3-2 13 0 16 1l2-1v-2c0-1 0-2-1-4z" class="Q"></path><path d="M670 175c4 2 7 4 11 5-1 2-2 2-3 3h-1c-2 1-3 3-6 3-2-1-5 0-7 0-1 0-4 0-5-1l-1-1h-1 1c2-1 5-1 7 0l-1 1h4c0-1 1-1 2-1 2 0 3-1 5-2h-6l2-1v-2c0-1 0-2-1-4z" class="J"></path><path d="M671 179c2 0 4 1 5 1 0 1 0 2-1 2h-6l2-1v-2z" class="R"></path><path d="M635 184h0c1 1 1 5 1 7 1-1 2-3 3-4 1 0 3-1 4-1-1 2 0 7 0 10 0-3-1-6-1-8-2 1-2 1-2 2-2 1-3 1-3 3-1 2 0 5 0 7l-1 1-2-1c-1 0-2-1-2-2-2-2-2-4-1-6v-5c-1 2-2 5-2 7h0c-2-1-5 0-7-2h-1l-3 3 2-2c-2-1-5 1-7 1l-2-1 4-2v1c2 0 2 0 3-1 3-1 4-2 6-3s3-2 5-2c2-1 3-2 6-2z" class="B"></path><path d="M481 129l3-1h0c3 0 9-2 12 0l1 1h-6c-2-1-3-1-5 0v1c1 1 2 1 3 1h5c-1 1-2 1-3 1l1 1h-1l-1 1c1 1 2 1 3 1l2 2v1l-1-1-1 1-1-1v1h0l2 1c1 0 3 1 4 2l-1 1 2 1c-7-1-11-1-17 0v-1c-1 1-1 1-1 2l-1 1c-1-2-1-3-2-4l-2-1c0-3 0-4 2-6 2-3 8-2 11-2-2-1-5-1-7-2l-1-1z" class="Q"></path><path d="M497 142l-4-2h0c-3-1-6-1-9-1v-1c1-1 2-1 3-1l5 1h0l2 1c1 0 3 1 4 2l-1 1z" class="B"></path><defs><linearGradient id="Ak" x1="528.344" y1="153.431" x2="539.111" y2="130.941" xlink:href="#B"><stop offset="0" stop-color="#27292a"></stop><stop offset="1" stop-color="#45474b"></stop></linearGradient></defs><path fill="url(#Ak)" d="M515 152v-2l1-1c0-2 1-3 2-4 0-2 1-2 2-3l1-1c1-2 2-4 4-4 0 0 1 0 2-1 2-2 6-4 10-3h0c4 0 8-1 11 2l1 3 1 4c-1 1-2 2-3 4v-1c-1-2-1-2-2-3-5-2-11-1-16 1s-9 3-12 7l-2 2z"></path><path d="M550 142c-3-2-6-3-9-4h8l1 4z" class="K"></path><path d="M519 162v2c0 3 1 6 1 9 0 4 0 8 1 11-1 2 2 8 2 10 1 3 3 5 4 8-1-9-3-18-1-26 1-2 2-7 4-8l-2 5-1 5c0 1 0 3 1 4 0 4-1 9 0 13l3 6-1 1v1c-4 1-9 0-13 1h0-5-7l-1-1c0-5 3-9 5-14 1-3 1-6 2-9v-14c1 1 1 6 1 8 1 6 0 12 0 19 1 1 1 0 1 1 2-2 1-7 1-9h1v3c0 1 1 3 1 4h1v3l2 1v2c1 1 3 4 5 4h0c-2-4-4-9-5-13-1-5-1-10-1-15v-7l1-5z" class="C"></path><path d="M512 204l1-8c1 2 2 6 4 7v1h0-5z" class="O"></path><path d="M527 178c0 1 0 3 1 4 0 4-1 9 0 13l3 6-1 1c-3-6-4-16-3-24z" class="J"></path><defs><linearGradient id="Al" x1="572.569" y1="185.647" x2="571.309" y2="139.207" xlink:href="#B"><stop offset="0" stop-color="#292b2d"></stop><stop offset="1" stop-color="#525457"></stop></linearGradient></defs><path fill="url(#Al)" d="M558 140c3-2 7-1 10-1 2 1 4 1 6 2h1 0c3 2 7 5 8 9v3l1 10c-1 3-1 7-2 11l-2 6-2 5h0v-9c-1-3-2-7-4-10-1-2-3-4-4-7l-1-4c0-4-2-8-3-11l-2-2c-2-1-4-1-6-2z"></path><path d="M574 153h4c1 1 1 2 0 3-2 0-3-1-4-3z" class="M"></path><path d="M564 142c2-1 4 0 6 1 1 1 3 2 4 3h-4 0c-1-1-3-1-4-2l-2-2z" class="K"></path><path d="M566 144c1 1 3 1 4 2h0l1 1 1 1h0l-1 2c0 1 2 2 3 2 1 1 2 1 3 1h1-4l-5 2c0-4-2-8-3-11z" class="Q"></path><defs><linearGradient id="Am" x1="291.839" y1="302.027" x2="155.201" y2="304.585" xlink:href="#B"><stop offset="0" stop-color="#cfcec8"></stop><stop offset="1" stop-color="#fff"></stop></linearGradient></defs><path fill="url(#Am)" d="M273 227c5-2 9-2 14 0 2 0 4 2 5 3 0 1 1 2 1 3v3c-3 4-6 7-8 11-1 0-4 5-5 6-4 8-7 15-9 24 0 3-1 6 0 9v2 9l1 8 6 21 6 11h7-7c1 4 2 9 5 12l1 1c-2 0-2 2-4 2l2 1h-1 0l-6 1v1c3 0 7 0 9 2v1c1 0 2-1 4-1h0l-7 6-2 6c-1 2-2 7 0 10l2 2s1 0 1 1c3 4 5 9 9 13 2 2 4 5 6 7s4 4 4 7l1 1 1 1c0 1 0 1-1 2-2 0-3 0-5-1h-3l-3-1h-1-1l-4-1c-1-1-2-1-3-2-5-1-10-4-15-6-3-2-7-4-10-6h-1-1l-3-1h-1c1 1 2 1 3 2h0-1c0 1 0 2-1 3l-2-1v1l1 1c1 1 3 2 5 2h0l2 1h1c1 1 0 1 1 1 2 1 4 2 5 2s0 0 2 1h-1c-1 0-2-1-3-1-5-2-11-4-15-7-3-3-6-6-10-8-5-3-11-4-16-7h0l-4-3h0c-1-2-3-3-4-4l-1-1c-1-3-1-4-1-7-1 1-1 2-1 4-1-2-2-3-2-5-2 1-2 4-3 6s-1 4-1 6c-1 2-1 6-2 8-1-1 0-3 0-4 1-4 1-8 2-12 1 0 2-5 3-6l2-10c-2 2-4 5-5 8h-1c1-2 0-2 0-3l-1 3c-1 1-1 2-2 3l-1 4v1h-1c0 1-1 2-1 4-1 1-1 2-2 3v2c-1 0-1 1-2 2l-1 1c1-1 1-2 2-3v-2c1 0 1-1 1-1v-1c1-1 1-1 1-2 1-1 0-1 1-1l6-15v-1c0 1-1 2-2 3l-1-1 1-1v-1c-3 2-5 4-7 6-1 3-4 5-6 7-1 1-2 2-3 2v-1c-2 1-3 3-5 4-1 0-1 0-2 1s-3 2-5 2c-1 1-3 1-4 2h-1c-3 0-6 0-8-1-5 0-9-3-12-7l1-1c2-3 5-5 9-6 0-1 1-1 1-1h12l1-1c7-1 12-6 18-10 5-4 10-10 14-15 0-1 0-2 1-2l1-1h0l6-9c1-1 2-3 2-5l1-1v-1h0 2c1-2 1-3 2-5 0-2 3-5 4-8h0c0-1 0-2 1-3 2-4 4-7 6-11l8-13c1-2 2-6 4-7h1l2-1c1-5 4-10 6-15h0c0-1 0-1 1-1l1-2c0-1 1-2 1-3 0-3 1-6 2-8l3-9c1-3 4-6 7-8z"></path><path d="M266 235l2-1 3-3-1 3h0c-2 3-2 6-4 8v-1h-1 0c-1 1-1 2-2 3l3-9z" class="X"></path><path d="M218 352l-2 5c-2 2-4 5-5 8h-1c1-2 0-2 0-3l1-1c0-1 0-2 1-3v-1c1-1 0 1 1-1s3-4 5-4z" class="T"></path><path d="M192 373l1-2h0l-3 2h-1l-6 3h-1l-2 1c-3 1-9 2-12 1h5v-2c-1 0-1 0-1-1 11 3 20-4 29-10-1 3-4 5-6 7-1 1-2 2-3 2v-1z" class="F"></path><path d="M273 227c5-2 9-2 14 0 2 0 4 2 5 3-1 3-4 8-6 10 0-1 1-2 1-3l1-1c1-1 2-3 3-4 0-1 0-1-1-2 0-2-4-3-5-3h0c-2-1-4-1-6-1l-2 3c-1 1-1 2-2 3h-1l1-3v-1l-4 3-3 3-2 1c1-3 4-6 7-8z" class="f"></path><path d="M278 326l6 11h7-7c1 4 2 9 5 12l1 1c-2 0-2 2-4 2-1 0-2-1-3-1l-1-1h-3 0v-1h6c0-1-1-2-2-2v-2l1-1c0-2 0-2-1-3v-1-3l-1-1v-2l-1-1-2-4c-1-1-1-2-2-3h1zm-36 34c0-2 1-4 2-5v-1l1-2c7-10 17-15 29-18h1 1 1c-1 1-1 1-2 1-2 1-3 1-5 2h-2c-1 1-3 1-4 2h-1l-1 1h-1l-2 2c-4 3-6 5-9 9-2 3-6 6-6 10-1 1-1 3 0 4h-1l-1-4v-1z" class="U"></path><path d="M165 367h0c3 0 5 1 7 1 3 0 5-1 7 0h5 0c-4 3-8 3-12 3 1 1 3 1 5 1-1 1-2 1-3 1-3-1-7-1-10-2h-4l-3 3c1 2 3 3 4 4 2 1 3 2 5 2 0 1 1 1 2 1h1 2 0l4 1c-3 0-6 0-8-1-5 0-9-3-12-7l1-1c2-3 5-5 9-6z" class="E"></path><path d="M292 230c0 1 1 2 1 3v3c-3 4-6 7-8 11-1 0-4 5-5 6h0c-2 2-3 4-5 7l-3 3c-1 2-2 3-3 5 0-3 1-4 2-6l5-8c0-1 0-2 1-3h0l2-2v-1l2-3 1-1 1-1 1-1c0-1 0-2 2-2 2-2 5-7 6-10z" class="U"></path><path d="M166 366h12l1-1c7-1 12-6 18-10 5-4 10-10 14-15 0-1 0-2 1-2l1-1c-4 10-23 29-32 34-1 0-3 1-4 1h0c-2 0-4 0-5-1 4 0 8 0 12-3h0-5c-2-1-4 0-7 0-2 0-4-1-7-1h0c0-1 1-1 1-1z" class="O"></path><path d="M271 231l4-3v1l-1 3h1c1-1 1-2 2-3 1 0 1 0 2 1 0 1-1 1-1 2-1 1-2 2-2 3l-1 1v2c-1 5-4 9-5 13l-3 9h-1l1-3c-2 1-2 3-3 4v-2c1-1 1-2 1-3 0-3 1-7 2-10l2-4c1-3 2-5 3-7v-1h-2l1-3z" class="N"></path><path d="M237 294l8-13c1-2 2-6 4-7h1l2-1c1-5 4-10 6-15h0c0-1 0-1 1-1-1 4-3 8-4 11-8 17-18 33-29 48 0-2 3-5 4-8h0c0-1 0-2 1-3 2-4 4-7 6-11z" class="V"></path><defs><linearGradient id="An" x1="270.535" y1="305.883" x2="251.928" y2="306.111" xlink:href="#B"><stop offset="0" stop-color="#adaba5"></stop><stop offset="1" stop-color="#ceccc5"></stop></linearGradient></defs><path fill="url(#An)" d="M272 305l-14 7c-2 1-4 3-6 4l-1-1 1-1c-2-1-3 0-5 0h1c0-1 1-2 1-2l8-5c5-4 9-8 13-12h1v2h0l1 8z"></path><path d="M220 349c6-9 15-16 24-21 3-2 7-4 10-6l1-1c2 0 4-1 5 0h9l-11 4h-1c-4 2-9 5-13 9-1 1-6 6-7 6-4 4-8 8-11 14-2 3-4 6-6 10 0 1-1 3-1 4v2h-1c-1 1-1 2-1 4-1-2-2-3-2-5-2 1-2 4-3 6s-1 4-1 6c-1 2-1 6-2 8-1-1 0-3 0-4 1-4 1-8 2-12 1 0 2-5 3-6l2-10 2-5 2-3z" class="g"></path><path d="M220 349h1s1 0 1-1c0 1 0 2-1 2v1c0 1-1 2-1 3-1 2-2 5-2 7h0c0 2 1 3 0 4 0 1 0 1-1 2 0-1 1-2 0-3-1 1-2 3-3 3h0l2-10 2-5 2-3z" class="O"></path><path d="M218 361c2-4 4-8 7-11 2-3 5-4 7-7 1-1 2-3 3-3h2c-4 4-8 8-11 14-2 3-4 6-6 10 0 1-1 3-1 4v2h-1c-1 1-1 2-1 4-1-2-2-3-2-5-2 1-2 4-3 6s-1 4-1 6c-1 2-1 6-2 8-1-1 0-3 0-4 1-4 1-8 2-12 1 0 2-5 3-6h0c1 0 2-2 3-3 1 1 0 2 0 3 1-1 1-1 1-2 1-1 0-2 0-4z" class="f"></path><defs><linearGradient id="Ao" x1="243.384" y1="291.496" x2="253.616" y2="302.504" xlink:href="#B"><stop offset="0" stop-color="#747270"></stop><stop offset="1" stop-color="#aeaca2"></stop></linearGradient></defs><path fill="url(#Ao)" d="M269 268c1-2 2-3 3-5l3-3c2-3 3-5 5-7h0c-4 8-7 15-9 24 0 3-1 6 0 9v2l-2 2-6 7c-2 2-3 4-6 5-2 2-4 5-7 6-1 1-2 3-4 4h0c-4 4-9 7-13 11l-17 16c1-3 3-5 5-7l13-15c13-16 24-32 35-49z"></path><path d="M246 312c-3 0-5 3-7 4h-1l8-7c3-2 3-4 5-6 3-4 7-9 11-12 1-1 2-2 4-2v2l-2 2-2 3 1 1c-2 2-3 4-6 5-2 2-4 5-7 6-1 1-2 3-4 4h0z" class="f"></path><path d="M237 340c1 0 6-5 7-6 4-4 9-7 13-9h1c-7 4-11 9-16 15l1 1v4c-1 2-1 4-1 6 0 1 0 0-1 1v7l1 1v1l1 4h1c-1-1-1-3 0-4 0-4 4-7 6-10 3-4 5-6 9-9h0c1 0 2 0 3-1 1 0 1 0 2-1h4c1 1 1 1 0 3l-2 1c1 1 3 1 4 0 4 0 6 0 9 4l1 1c-1 0-3-1-4 0 0 0-1 0-1 1-1 0 0-1-1 0-1 0-1 0-2 1l-1 1c2-1 5-2 8-3v1h0 3l1 1c1 0 2 1 3 1l2 1h-1 0l-6 1v1c3 0 7 0 9 2v1c1 0 2-1 4-1h0l-7 6-2 6c-1 2-2 7 0 10l2 2s1 0 1 1c3 4 5 9 9 13 2 2 4 5 6 7s4 4 4 7l1 1 1 1c0 1 0 1-1 2-2 0-3 0-5-1h-3l-3-1h-1-1l-4-1c-1-1-2-1-3-2-5-1-10-4-15-6-3-2-7-4-10-6h-1-1l-3-1h-1c1 1 2 1 3 2h0-1c0 1 0 2-1 3l-2-1v1l1 1c1 1 3 2 5 2h0l2 1h1c1 1 0 1 1 1 2 1 4 2 5 2s0 0 2 1h-1c-1 0-2-1-3-1-5-2-11-4-15-7-3-3-6-6-10-8-5-3-11-4-16-7h0l-4-3h0c-1-2-3-3-4-4l-1-1c-1-3-1-4-1-7h1v-2c0-1 1-3 1-4 2-4 4-7 6-10 3-6 7-10 11-14z" class="T"></path><path d="M219 368l1-1 2 2h-2l-1 3c-1-1 0-1 0-2v-2z" class="E"></path><path d="M252 361h1l2 14c-3-5-4-8-3-14z" class="U"></path><path d="M288 395h1c0 1 0 1 1 2l4 4c0-1-1-1-1-2-1-1-2-3-2-4h1c1 2 1 3 3 5l3 4-1 1c-3-1-6-5-8-8h0l-1-1v-1z" class="F"></path><path d="M226 354h1c-1 2-2 3-2 5l1 3c-1 0-1-1-2 0-1 0-3 4-4 5l-1 1c0-1 1-3 1-4 2-4 4-7 6-10z" class="B"></path><path d="M219 370c0 1-1 1 0 2 3 2 5 5 7 6v1c-1 0-2-1-3-1h-1c0 1 1 2 2 3v1h0c-1-2-3-3-4-4l-1-1c-1-3-1-4-1-7h1z" class="N"></path><path d="M279 349v1h0c-6 2-11 4-15 10v1c-1 2-2 3-2 4v3 5c0 2 2 6 1 7-2-4-4-10-3-15s6-10 11-13c2-1 5-2 8-3z" class="O"></path><path d="M290 391c-2-3-2-6-2-9 3 4 5 9 9 13v3l2 3-3-2-1 1c-2-2-2-3-3-5h-1c0-2-1-3-1-4z" class="a"></path><path d="M290 391h1c2 1 2 2 3 4h-1-1 0-1c0-2-1-3-1-4z" class="b"></path><path d="M292 395h0 1 1l3 3 2 3-3-2-1 1c-2-2-2-3-3-5z" class="e"></path><path d="M244 365h0c1 4 2 10 2 14-1-3-3-5-4-8v-1l-1-2v-1c-1-1-1-3-1-4v-4-4c0-2 0-4 1-5v-3c1-2 1-4 2-6v4c-1 2-1 4-1 6 0 1 0 0-1 1v7l1 1v1l1 4h1z" class="N"></path><path d="M279 350h3l1 1c1 0 2 1 3 1l2 1h-1 0l-6 1v1c-2 0-4 0-6 2l-4 1c-3 1-6 6-8 9l-1 1v-3c0-1 1-2 2-4v-1c4-6 9-8 15-10z" class="F"></path><path d="M237 340c1 0 6-5 7-6 4-4 9-7 13-9h1c-7 4-11 9-16 15l-8 9c-1 1-3 2-3 4v3 1h0l-4-3h-1c3-6 7-10 11-14z" class="K"></path><path d="M303 412c-2-2-4-3-5-4-4-3-7-7-11-11-1-1-2-3-3-5l1 1 1-1 2 3v1l1 1h0c2 3 5 7 8 8l1-1-3-4 1-1 3 2-2-3v-3c2 2 4 5 6 7s4 4 4 7l1 1 1 1c0 1 0 1-1 2-2 0-3 0-5-1z" class="X"></path><path d="M297 395c2 2 4 5 6 7s4 4 4 7l1 1c-3 0-7-4-10-6l-3-4 1-1 3 2-2-3v-3z" class="R"></path><path d="M297 395c2 2 4 5 6 7s4 4 4 7c-3-2-5-5-8-8l-2-3v-3z" class="E"></path><defs><linearGradient id="Ap" x1="278.763" y1="372" x2="269.896" y2="359.426" xlink:href="#B"><stop offset="0" stop-color="#c4c3bd"></stop><stop offset="1" stop-color="#e7e6e3"></stop></linearGradient></defs><path fill="url(#Ap)" d="M281 355c3 0 7 0 9 2v1c1 0 2-1 4-1h0l-7 6c-5 3-14 5-16 11-2 3-1 9 1 12v1 1l-1-1h-1c-3-1-5-4-7-7 1-1-1-5-1-7v-5l1-1c2-3 5-8 8-9l4-1c2-2 4-2 6-2z"></path><path d="M281 355c3 0 7 0 9 2v1h-2-2c-2-1-3-1-5-1h-6c2-2 4-2 6-2z" class="N"></path><path d="M290 358c1 0 2-1 4-1h0l-7 6c-5 3-14 5-16 11-2 3-1 9 1 12h-1c-3-5-5-8-5-14 1-10 8-7 16-8 2-1 5-3 7-5l1-1z" class="P"></path><path d="M384 859l-1-1c-3-1-5-5-6-7l280-1-3 5c-2 3-2 3-5 3h-8v1l-1-1h-8-11c-2 0-4 0-6 1h0c-4-1-9-1-13-1l-1-2-1 1 1 2 2 5c2 7 2 16-2 22l-1 1c-2 0-5-1-7-2-3-1-7-6-9-7l-1 2c-2-1-4-1-7-1v3c-1 1-1 1 0 2l-2-2c-3-2-4-5-5-8-4-6-7-12-12-17h-1l-1 1 1 1c1 2 2 4 1 6 2 7 1 14-1 20-4 14-11 22-21 32a79.93 79.93 0 0 0-13 13c-3 4-6 10-8 15l-1 2h-1l-2-6c-8-15-22-23-33-36-5-5-8-13-11-20-2-5-2-12-1-18-1-2 0-3 0-5-2 2-3 5-4 7-3 6-6 13-11 18-1 1-3 2-4 2h-1c-1-1-2-1-2-3 1 0 2 0 3-1 0-2-2-3-2-6l-1-1-2-2c-1 1-1 1-2 1l-1-2c-1 0-1 0-2 1-3 3-11 7-12 11-1-1-1-3-2-5-1-5-1-11-1-16v-2l1-5v-2 1c-9 1-18 1-27 1-3-1-7-1-11 0z" class="C"></path><path d="M433 869l5-5c2-1 3-2 5-2h1l-1 2c-2 0-4 1-5 2l-1 1c-1 1-2 1-4 2zm138-2c0-1 0-2 1-3h4c1-1 2-1 3-1h1c2 0 3 2 5 4h0c-2-1-3-1-4-3l-1 1h-1-1c-2-1-5 1-7 2z" class="P"></path><path d="M422 859l2 2c1 1 1 4 1 6l1 1v1h-2c-1-2-1-3-3-5l1-5z" class="Z"></path><path d="M599 865c-1-1-2-3-2-5 1-1 3-1 4-1l2 5-1 1h0-3z" class="D"></path><path d="M556 873v1h1 0c0 1-1 3 0 4h0c1-3 1-6 0-8v-5c2 7 1 14-1 20v-1l-2-1 1-2c1-2 1-5 1-8z" class="I"></path><path d="M517 880h0c-2-3-3-7-2-10h0l2 1 2 3v-2h-1l1-1v1c1-1 1-1 2 0l-1 1v1c1 1 1 2 1 2l1 1v3c-1-1-2-2-3-2s-2 1-2 2z" class="G"></path><path d="M554 883c-1 1-1 1-2 0-6-1-12-8-14-13v-1c1 1 1 2 2 3h0c1 2 3 4 5 6 1 0 3 1 4 1 2-1 5 0 6-1v-4l1-1c0 3 0 6-1 8l-1 2z" class="H"></path><path d="M441 873l1 1 1-1v-1c1-2 1-5 3-7 1 1 0 4 1 6h1l-3 8h-1l-1-1-2-2c-1 1-1 1-2 1l-1-2c1-1 2-1 3-2z" class="I"></path><path d="M448 871c0-1 1-3 2-4 0 3-2 10-3 13l8-11c1 2 0 5-1 7-2 4-4 6-7 9h-1c0-2-2-3-2-6h1l3-8z" class="Y"></path><path d="M455 869c1-2 2-3 3-4h0v2l1 1h0c1-2 1-4 3-5 1-3 4-6 6-8-1 2-1 5-3 7s-3 5-4 7c-3 6-6 13-11 18-1 1-3 2-4 2h-1c-1-1-2-1-2-3 1 0 2 0 3-1h1c3-3 5-5 7-9 1-2 2-5 1-7z" class="I"></path><defs><linearGradient id="Aq" x1="473.202" y1="871.134" x2="475.921" y2="881.438" xlink:href="#B"><stop offset="0" stop-color="#34363a"></stop><stop offset="1" stop-color="#4d5255"></stop></linearGradient></defs><path fill="url(#Aq)" d="M465 867c1 4 0 8 1 12v1l1-1-1-1 1-2c1-1 2-2 3-4 0-1 1-2 2-2s1 0 2 1c0 0 1-2 2-2s1 1 2 0c0-1 0-1 1-1h1s0-1 1-1c0-1 1 0 2-1l1 1 1-1c1 0 1 1 2 0v1c-1 6-7 13-12 15-2 1-5 2-7 3h-2c-2-5-2-12-1-18z"></path><defs><linearGradient id="Ar" x1="434.576" y1="861.577" x2="426.748" y2="885.56" xlink:href="#B"><stop offset="0" stop-color="#111"></stop><stop offset="1" stop-color="#666a6d"></stop></linearGradient></defs><path fill="url(#Ar)" d="M426 868l3-4h0c-1 4-3 7-2 12 3-2 4-5 6-7 2-1 3-1 4-2l1-1c1-1 3-2 5-2 0 3-1 6-2 9-1 1-2 1-3 2-1 0-1 0-2 1-3 3-11 7-12 11-1-1-1-3-2-5-1-5-1-11-1-16v-2c2 2 2 3 3 5h2v-1z"></path><path d="M421 864c2 2 2 3 3 5h2c0 1 0 1-1 2h-1l-2-1-1-4v-2z" class="B"></path><path d="M478 902h-2v-1c-1-1 0-2 0-4 2-4 6-8 9-12 3-3 5-7 8-11h0c-1 3-2 4-3 6v1l7 2 2-3c0-2 1-3 2-4h0c0 1-1 3-1 4l2-2v-1c1 0 2 0 3-1h0 1l-2 3h-1c-2 2-4 3-5 6h4 1l1-1 1-1c0-1 0-1 1-2v-1l1-1v-1s0-1 1-1v-1h0l-1 4c-5 11-13 17-24 21l-5 1z" class="W"></path><defs><linearGradient id="As" x1="520.165" y1="875.44" x2="544.446" y2="900.552" xlink:href="#B"><stop offset="0" stop-color="#373c3e"></stop><stop offset="1" stop-color="#626668"></stop></linearGradient></defs><path fill="url(#As)" d="M528 882h1v-1l-1-1 1-1c0 1 0 0 1 1l2-2c0-1-1-2-2-3v-1c0-1-1-1-2-2l1-1c2 3 3 6 5 9l9 13c1 3 3 5 3 8l-1 1c-2-1-4-1-5-2-11-4-19-10-23-20 0-1 1-2 2-2s2 1 3 2v-3c0 1 1 3 2 4l1-1c0-1 1-1 1-2 1 1 2 3 2 4z"></path><path d="M524 881l1-1c0-1 1-1 1-2 1 1 2 3 2 4s1 2 1 3l1 1h-1c-2-1-3-3-5-5z" class="Y"></path><defs><linearGradient id="At" x1="571.542" y1="872.673" x2="602.943" y2="875.21" xlink:href="#B"><stop offset="0" stop-color="#0e1012"></stop><stop offset="1" stop-color="#585b5d"></stop></linearGradient></defs><path fill="url(#At)" d="M603 864c2 7 2 16-2 22l-1 1c-2 0-5-1-7-2-3-1-7-6-9-7l-1 2c-2-1-4-1-7-1v3c-1 1-1 1 0 2l-2-2h0v-2h-1v-1c-1-2-1-4-2-7v-5c2-1 5-3 7-2h1 1l1-1c1 2 2 2 4 3h0 2c1 1 1 1 1 2 1 1 3 1 4 1 2-3 0-1 0-3 3 2 6 6 9 9 0-4-1-7-2-11h3 0l1-1z"></path><path d="M593 885v-1l1 1 1-1v-3-1c2 1 3 3 5 4v3c-2 0-5-1-7-2z" class="B"></path><path d="M571 872h1v2c0-2 0-2 1-3l1 1v1 1c1 0 1-1 1-2v-1l1 1v6 1h0v3c-1 1-1 1 0 2l-2-2h0v-2h-1v-1c-1-2-1-4-2-7z" class="G"></path><path d="M576 872l1-2c1 0 5 4 6 5v1l-2 1c1 1 1 1 3 1l-1 2c-2-1-4-1-7-1h0v-1-6z" class="C"></path><path d="M576 879c1-1 1-2 3-3l2 1c1 1 1 1 3 1l-1 2c-2-1-4-1-7-1z" class="H"></path><defs><linearGradient id="Au" x1="509.091" y1="890.129" x2="514.63" y2="943.676" xlink:href="#B"><stop offset="0" stop-color="#373b3e"></stop><stop offset="1" stop-color="#686b6d"></stop></linearGradient></defs><path fill="url(#Au)" d="M509 880l3-11c2 9 5 16 9 24 1 0 1 0 1 1 2 2 7 7 7 10l6 7c-1 3-3 5-4 6-4 4-8 6-11 10-3 5-5 12-7 17l1 1-1 2h-1l-2-6c-8-15-22-23-33-36v-1-1l1-1 5-1c11-4 19-10 24-21h2z"></path><path d="M512 913c1-3 0-6 1-9 0 2 0 3 1 5v-5c0 2 0 5 1 7l-1 3c1 3 2 10 0 13 0 2 0 2-2 3-1-5 0-11 0-17z" class="D"></path><path d="M514 927l-1-1h-1v-4c1-1 1-3 1-4l1 1h0v-5c1 3 2 10 0 13z" class="H"></path><defs><linearGradient id="Av" x1="519.077" y1="875.013" x2="506.544" y2="906.529" xlink:href="#B"><stop offset="0" stop-color="#1a1d1e"></stop><stop offset="1" stop-color="#3d3f43"></stop></linearGradient></defs><path fill="url(#Av)" d="M509 880l3-11c2 9 5 16 9 24 1 0 1 0 1 1 2 2 7 7 7 10l-1 1c1 1 1 1 0 2-1 0 0 0-1-1-3-4-6-9-9-14l-3 3c0 4 1 11 0 16-1-2-1-5-1-7v5c-1-2-1-3-1-5-1 3 0 6-1 9l-1 1v1c0 1 1 4 0 5h-1v-8c-1-3-1-5-1-7 1-2 1-6 1-7 1-1 0-4 0-5l-2 1-1-1v-2h-1c-1 1-2 3-4 4v1c3-5 5-10 7-16z"></path><path d="M513 904v-1c1-3 0-7 0-10h1c1 2 0 8 0 11v5c-1-2-1-3-1-5zm-2 11l-1-1c0-5 0-10 1-15 0-2-1-4 1-5v3c-1 5-1 11-1 17v1z" class="B"></path><path d="M522 894c2 2 7 7 7 10l-1 1c1 1 1 1 0 2-1 0 0 0-1-1v-2c-2-2-3-4-4-6-1-1-1-2-1-4z" class="H"></path><defs><linearGradient id="Aw" x1="491.079" y1="891.406" x2="518.678" y2="926.11" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#272729"></stop></linearGradient></defs><path fill="url(#Aw)" d="M507 880h2c-2 6-4 11-7 16l-9 10c-2 2-4 3-5 5 3 3 6 7 9 9 2 2 5 4 7 5 3 3 5 11 6 15v1c-8-15-22-23-33-36v-1-1l1-1 5-1c11-4 19-10 24-21z"></path><path d="M756 180c-1-1-1-3-1-4-1-5 2-8 5-11 1 0 3 1 5 1-1-2-3-5-2-8 1-2 6-6 9-6 2 1 3 2 5 2 1 0 5-3 6-3 7-3 15 2 21 6l-3 4c3 1 7 1 10 1 5 1 9 2 13 3h0c1 1 2 1 3 1v1c3 1 5 2 8 4-1 4-1 9-2 12l2-2 4-1c2 0 4 0 6 1 1 2 1 5 1 7 2-2 7-7 10-8l1 1c2 2 2 5 2 7 4-3 7-6 12-7 3-1 6-1 9-2l1-1h1l12-3c0 2-1 4-1 6l9 3 1 1c2 1 4 2 6 4-3 2-5 3-8 5 7 3 12 8 15 14-4 1-8 1-12 1v1c2 4 2 9 1 13-1 1-1 1-2 1h-4c0 3-2 5-4 8v-2l-1-1v1 3h-1v-1l-1 1h0l-1-1c-1-2-2-3-4-4-4-3-10-2-15-1-1 1-2 2-2 4-1 0-2 1-2 2-2-2-3-4-5-5h0c-3-1-7-1-10-1-11 1-21 4-32 6-4 1-8 2-13 2-8 1-16-1-25-2-3-1-5-2-8-5h0l-4-7-1 1c-1-5-2-8-4-12l-11 3h-1l-3 2 4 4v1c-4-4-8-6-13-9l-7-2-23-4h-11-4v-5-3c1-4 2-6 4-10 3-2 4-4 8-4 5 0 10 0 15 1 3 0 6 0 8 1 1 1 1 2 1 3 1-1 1-3 1-4h7 1 5c3 0 7-2 9-4z" class="T"></path><path d="M810 174h1 4c2 1 4 1 6 1h-9-5c-1 0-5 1-7 1l4-1c2 0 4-1 6-1zm0-7c2-1 7-1 9 0-4 0-8 1-12 1-1 0-5 1-7 0h0 2c1 0 1-1 3-1h5z" class="L"></path><path d="M831 187h0c0 2-1 3-1 5v2c-1 2-3 4-5 5h-1c2-5 5-9 7-12z" class="D"></path><path d="M835 181c0 5-2 9-5 13v-2c0-2 1-3 1-5h0c0-1 1-3 2-4l2-2z" class="E"></path><path d="M893 219l3 2h0l2 2c0-1-1-4 0-5 1 2 0 4 1 6 0 3-2 5-4 8v-2l-1-1c2-4 1-6-1-10z" class="S"></path><path d="M780 211l2 1h0c1 1 2 1 3 1h1 0 1 1 5c3-1 6-1 8-1 1 0 2 0 3 1-1 0-2 1-4 1h-1c-7 2-12 0-18-1l-1-2z" class="L"></path><path d="M771 196l2-1v1c-1 1-2 3-2 4s0 2 1 3v2l1 1v-5h1v3c1 0 1 1 2 2s1 3 2 5h2l1 2h-1c-2 0-3-1-5-3s-4-4-5-7h0 0l-1-1c1-2 2-3 2-5v-1z" class="E"></path><path d="M762 210h1l1-1h1 2v-1-1c-1 0-1 0-1-1v-3-2c1 0 0-1 1-2h0l1-1h0l-1 5c1-2 2-5 4-7v1c0 2-1 3-2 5l1 1h0 0c1 3 3 5 5 7s3 3 5 3h1c6 1 11 3 18 1h1c2 0 3-1 4-1h1c-3 3-10 3-14 3-2 0-6-1-9 0l-3 1 2 2c1 1 3 2 5 2 3 1 5 2 7 2 11 3 23 0 34-3 4-1 8-3 12-4-3 2-6 3-9 4-6 2-12 4-19 5-8 1-17 0-25-3l-7-2h-1c0 1 0 2 1 3 1 2 1 3 0 5l1 1c2 1 3 2 3 4-3-1-5-2-8-5h0l-4-7-1 1c-1-5-2-8-4-12l-11 3h-1 0c1-1 3-2 4-2h1c1 0 2-1 3-1z" class="X"></path><path d="M818 182h1c-3 4-5 10-10 14-6 6-15 10-24 10-3 0-5 0-7-2 0-2 0-2 1-4 3-1 5-2 8-3 5-1 13-1 16-5v-1c-3-4-6-5-9-6l24-3z" class="C"></path><defs><linearGradient id="Ax" x1="731.136" y1="213.517" x2="730.181" y2="182.351" xlink:href="#B"><stop offset="0" stop-color="#a7a7a3"></stop><stop offset="1" stop-color="#cecdc7"></stop></linearGradient></defs><path fill="url(#Ax)" d="M756 180c1 1 2 1 3 2h1v2h-1l-2-2c-3 0-4 1-7 3v3l1 1c0 2 2 3 3 5l3-2 5-5c4-5 8-8 14-11h0l-5 4c-4 2-12 10-14 15 2-1 5-3 7-3l6-6 3-2v1h0v1l-1 1c-2 1-3 3-5 4-2 3-5 6-7 10-1 4-1 4 1 7l1 2c-1 0-2 1-3 1h-1c-1 0-3 1-4 2h0l-3 2 4 4v1c-4-4-8-6-13-9l-7-2-23-4h-11-4v-5-3c1-4 2-6 4-10 3-2 4-4 8-4 5 0 10 0 15 1 3 0 6 0 8 1 1 1 1 2 1 3 1-1 1-3 1-4h7 1 5c3 0 7-2 9-4z"></path><path d="M742 184h5l2 2-1 1c-2 1-4 1-6 1v-2-1-1z" class="B"></path><path d="M746 201h1c1 3 4 9 4 13h-1c-2-1-1-4-2-6l-2-7z" class="V"></path><path d="M732 196c5 5 8 9 11 14l-1 1-7-2v-1l-2-2h0v-1c1-2 1-4 0-6 0-1 0-1-1-2v-1z" class="Q"></path><defs><linearGradient id="Ay" x1="720.071" y1="207.941" x2="714.413" y2="187.501" xlink:href="#B"><stop offset="0" stop-color="#484746"></stop><stop offset="1" stop-color="#888682"></stop></linearGradient></defs><path fill="url(#Ay)" d="M697 197h1c4-2 6-7 11-9h1c5-1 11 0 16 3 2 2 5 3 6 5v1c1 1 1 1 1 2 1 2 1 4 0 6v1h0l2 2v1l-23-4h-11-4v-5-3z"></path><defs><linearGradient id="Az" x1="707.145" y1="204.61" x2="702.287" y2="197.795" xlink:href="#B"><stop offset="0" stop-color="#565553"></stop><stop offset="1" stop-color="#716d6b"></stop></linearGradient></defs><path fill="url(#Az)" d="M701 200l1-3 1 1h3l1-1c3 3 0 7 5 7v1h-11-4v-5c1 0 2-1 4 0z"></path><path d="M697 200c1 0 2-1 4 0v3 1h0v1h-4v-5z" class="b"></path><path d="M712 204c0-3 0-6 3-8 3-1 4-1 6 1 3 1 11 9 12 9l2 2v1l-23-4v-1z" class="C"></path><defs><linearGradient id="BA" x1="820.688" y1="196.985" x2="846.172" y2="245.87" xlink:href="#B"><stop offset="0" stop-color="#d8d6cd"></stop><stop offset="1" stop-color="#fefeff"></stop></linearGradient></defs><path fill="url(#BA)" d="M839 216h0c1 0 2-1 3-1 1-1 3-2 5-2l5-2c4-2 7-2 10-3 1-1 2-1 3-1h0 8c0 1 1 1 2 1 3 1 6 3 10 4h0c4 1 5 4 8 7 2 4 3 6 1 10v1 3h-1v-1l-1 1h0l-1-1c-1-2-2-3-4-4-4-3-10-2-15-1-1 1-2 2-2 4-1 0-2 1-2 2-2-2-3-4-5-5h0c-3-1-7-1-10-1-11 1-21 4-32 6-4 1-8 2-13 2-8 1-16-1-25-2 0-2-1-3-3-4l-1-1c1-2 1-3 0-5-1-1-1-2-1-3h1l7 2c8 3 17 4 25 3 7-1 13-3 19-5 3-1 6-2 9-4z"></path><path d="M885 212c4 1 5 4 8 7 2 4 3 6 1 10v1 3h-1v-1l-1 1h0l-1-1v-1c0-2 0-4-1-5-4-6-11-9-18-10 1-1 2-1 4-1h2l6 3c1 1 3 2 4 3s2 1 3 2v-2-3c-1-2-4-4-6-6z" class="E"></path><g class="C"><path d="M885 212c4 1 5 4 8 7 2 4 3 6 1 10v1-1c-1-3-1-7-3-11-1-2-4-4-6-6z"></path><path d="M872 227c-1-1-2-2-2-3-3-4-6-6-10-7 4-1 8-1 12-1 7 1 14 4 18 10 1 1 1 3 1 5v1c-1-2-2-3-4-4-4-3-10-2-15-1z"></path></g><path d="M236 118l1 1 2-1 3-1c6-2 16-1 22 0h3 2l1 1c1 0 1 0 3 1-1 0-2 0-3-1h-2-1c0-1-1 0-1 0-1 0-1-1-1-1-2 0-3 1-4 0h-8-5-3c-1 1-2 1-3 1h-1 0c-1 1-2 1-3 1s-2 1-3 1v1h-2l-2 1-10 7c0 1-1 1-1 2-1 0-1 1-2 1l-5 5-2 4v1h0l-1 2v1 4h-1c0 2 0 2 1 4 0 1-1 2 0 4v1h1v1s1 1 1 2c1 1 2 1 2 2 1 1 2 1 2 2h2c0-1-1-2-1-3h0l-1-3v-1-3h0v-4-1l1-1h0c0-1 1-2 2-2-3 6-3 12 0 18h0l8-2-1-1 21-12 1 2c4-2 7-2 12 0 2 0 3 1 5 2h0 1c1-1 1 0 2-1h1 0c3-2 6-5 9-8 0 1-1 2 0 3 0 0 1 2 1 3l6 6c6 6 10 10 14 18h1v2h1v2l1 1c1 0 1 2 2 2v2c1 0 1 0 2-1h1l1 1 2 2v2c3-3 8-5 12-7 2 1 3 1 5 0h2c5 1 10 3 13 8v1h0c0 2 1 2 1 3l1 1c-1 1-2 1-2 1-1-2-1-3-3-4 2 4 4 9 3 14l-1 2-11 4c-6 1-10 4-15 6l-10 8c-4 3-9 7-12 11v-3c0-1-1-2-1-3-1-1-3-3-5-3-5-2-9-2-14 0v-1l-7 4c-4 3-9 4-14 5-11 1-22 0-33-2-9-2-31-9-39-4-2 1-3 3-3 5h0c-2 0-2-1-3-2-2-2-2-3-3-5-3-2-7-2-10-1-2 0-5 2-6 5-2 0-3 1-4 0h-1-1c-2-2-3-5-3-7-2 0-4 1-6 0-2-4-1-10 0-14v-1c-4 1-8 0-12-1 4-7 9-11 16-14-4-2-6-3-9-6 5-3 10-5 16-7-1-2-1-3-1-6 3-1 7 2 10 3 1 0 2-1 3-1l5-3c0 1 0 1 1 1h2l1 1c1-1 2-2 2-4 0 0 0-1 1-1 0-1 1-2 2-3v-1c0-1 0-1-1-2l2 1v-1h2l2-1c2-1 4-2 6-4h1l1-1c2-2 5-4 7-6l3-4 1-1c2-2 3-5 6-6 0-1 1-1 2-2 1-2 3-3 4-4 0-2 3-5 4-5 2-1 2-3 4-4h1l1-1c0-1 1-1 2-2s3-2 5-3l3-1c1-1 2-1 3-2z" class="T"></path><path d="M260 152c2 0 3 1 5 2-2 0-1 0-2 1 0 1 1 2 2 3v1h0-1c-2-3-4-5-4-7z" class="L"></path><path d="M270 198l1 1c2 2 2 3 2 5v4c0 2 0 3-1 4-2 2-4 3-7 3h0c-1 1-3 0-4 0h-2v-1h4l5-1c2-1 3-3 4-4 1-4 0-8-2-11z" class="N"></path><path d="M168 174c0 1 0 1 1 1h2l1 1-1 1c1 2 4 1 6 1l-5 1-2 1c-3 0-5-1-7-3l5-3z" class="P"></path><path d="M285 166c3 3 5 6 5 10v2l-3 3h0-2l1-1c0-2 1-4 0-6 0-3-1-5-1-8z" class="E"></path><path d="M149 221h0c3-4 6-9 11-10-1 1-3 2-4 4-3 4-5 8-5 12 1 1 2 2 2 3-1 1-2 1-3 1h-1c-2-2-3-5-3-7l3-3z" class="M"></path><path d="M146 224l3-3v4h1v1s0 1 1 1h0c1 1 2 2 2 3-1 1-2 1-3 1h-1c-2-2-3-5-3-7z" class="F"></path><path d="M226 162l21-12 1 2-9 5 2 5-14 1-1-1z" class="P"></path><path d="M198 214c2 0 4 1 6 2 4 1 7 3 11 4l5 1 16 3c5 1 14 1 20-1h1c4-1 7-3 10-5l1 1h0c-2 1-3 2-6 3-2 1-4 2-7 3h-5c-3 1-4 1-7 1l-6-1c-12-1-23-4-34-8-1-1-4-2-5-3z" class="c"></path><path d="M267 218h0c3-2 5-3 7-5 0-2 1-3 2-4v-1-5h0l-3-8c0-2-1-2-2-4h-1l1-1c3 3 4 7 5 11 1 3 2 7 2 11-5 6-10 9-17 12h1v1c-1 1-3 1-5 1-5 1-9 1-14 0 3 0 4 0 7-1h5c3-1 5-2 7-3 3-1 4-2 6-3h0l-1-1z" class="F"></path><path d="M283 218v2c0-3-1-9 0-11l1-3v4h1v-4h0c1 3 1 4 0 7 0 1 1 1 1 2v1l1-2h1v1c0 1 0 1 1 1h0v-1c-1-1-1-2-1-3v-1c0-1 0-2-1-3v-4-2c-2-4-4-9-7-13h1c3 4 5 9 7 14l2 9v1l1 1v1 2c1 2 0 5 1 7l2 2 2-5v-1l1-1-3 11c-1 0-1 0-1-1-2-1-4-2-5-3h-1c-1-1-3-1-4-2h-1c-3 0-7 0-9 2l-7 4c1-1 2-3 4-4h1c1 0 1-1 2-2 0-2 0-3 1-5 2-1 4-2 6-1v-2h0c1-1 1-3 2-5l1 7z" class="X"></path><path d="M177 178c4 0 8 0 12-3 1-1 2-2 4-3h0v6c1 1 2 1 4 1s5-2 7-4c3-4 5-11 5-16l-1-3h0l-1 1c-1-1 0-5 1-7h0v1c0 7 4 11 8 15-2 1-5 2-7 3-2 4 2 10-2 11-2 0-6-1-8 0-2 3-1 4-2 7-3-2-5-5-8-7-1 0-2 1-3 2-1 2-1 6 0 8-5-4-9-8-14-11l5-1zm92-25h0c3-2 6-5 9-8 0 1-1 2 0 3 0 0 1 2 1 3l6 6c6 6 10 10 14 18h1v2h1v2l1 1c1 0 1 2 2 2v2c1 0 1 0 2-1h1l1 1c-2 0-3 1-5 1l-1 1h-1c-1 1-3 0-5 1l-1 1v-2c-2-3-5-4-8-5h0l3-3v-2c0-4-2-7-5-10-2-2-2 0-4 0 0-2 1-5 0-7-2-3-9-5-12-6z" class="C"></path><path d="M299 175h1v2h1v2l1 1c1 0 1 2 2 2v2c1 0 1 0 2-1h1l1 1c-2 0-3 1-5 1l-1 1h-1c0-4-1-7-2-11z" class="F"></path><path d="M224 182c5 1 11 1 16 2 4 0 8 0 11 1-3 1-7 2-9 4-1 1-1 1-1 3 3 4 10 4 14 5 3 1 5 2 7 3 1 2 2 2 3 5l-1 1c-4 1-10 0-14 0-7-2-14-6-19-12-3-4-5-8-7-12z" class="C"></path><defs><linearGradient id="BB" x1="319.427" y1="218.153" x2="307.026" y2="196.79" xlink:href="#B"><stop offset="0" stop-color="#61605d"></stop><stop offset="1" stop-color="#7e7d77"></stop></linearGradient></defs><path fill="url(#BB)" d="M297 219c3-7 6-13 10-19s11-12 18-12c6-1 10 0 14 3 2 4 4 9 3 14l-1 2-11 4c-6 1-10 4-15 6l-10 8c-4 3-9 7-12 11v-3c0-1-1-2-1-3-1-1-3-3-5-3-5-2-9-2-14 0v-1c2-2 6-2 9-2h1c1 1 3 1 4 2h1c1 1 3 2 5 3 0 1 0 1 1 1l3-11z"></path><path d="M305 225c0-2 1-5 2-7 4-7 8-13 14-18 2-2 3-3 6-3 1 1 2 1 3 3s1 6 0 8v1 2c-6 1-10 4-15 6l-10 8z" class="C"></path><path d="M330 200c1 2 1 6 0 8v1 2c-6 1-10 4-15 6 2-4 7-9 10-12 2-1 5-3 5-5z" class="B"></path><defs><linearGradient id="BC" x1="237.375" y1="176.016" x2="200.597" y2="250.882" xlink:href="#B"><stop offset="0" stop-color="#d4d2c9"></stop><stop offset="1" stop-color="#faf9fb"></stop></linearGradient></defs><path fill="url(#BC)" d="M276 201l1-1c-1-2-1-3-2-4v-1h1l2 3h0c0-1 0-1-1-1l-2-4h1c2 2 3 5 5 8 3 4 2 12 2 17l-1-7c-1 2-1 4-2 5h0v2c-2-1-4 0-6 1-1 2-1 3-1 5-1 1-1 2-2 2h-1c-2 1-3 3-4 4-4 3-9 4-14 5-11 1-22 0-33-2-9-2-31-9-39-4-2 1-3 3-3 5h0c-2 0-2-1-3-2-2-2-2-3-3-5-3-2-7-2-10-1-2 0-5 2-6 5-2 0-3 1-4 0h-1c1 0 2 0 3-1 0-1-1-2-2-3 0-4 2-8 5-12 1-2 3-3 4-4h1c1-1 3-2 5-2 1-1 2-1 3-1 1-1 2 0 2-1h1c1 0 4 0 5 1h3c1 0 3 1 4 1l4 1c1 0 1 1 2 1l1-1v1h0 1v1h0l1-1 1 1h1c1 1 2 1 3 2s4 2 5 3c11 4 22 7 34 8l6 1c5 1 9 1 14 0 2 0 4 0 5-1v-1h-1c7-3 12-6 17-12 0-4-1-8-2-11z"></path><path d="M186 223c4-1 9-1 14 0h0-1c-4 0-8-1-12 0-1 1-2 0-3 0h2zm-30-8v1c0 1-1 1-1 2-2 3-2 6-2 9v1c1-4 3-7 7-9l1-1h-2-1c1-1 2-1 3-1 2-1 3-1 5-2 4-1 8-1 12 0l2 1c-9 0-16 0-22 6-2 2-4 4-4 7 0 0 0 1 1 2-2 0-3 1-4 0h-1c1 0 2 0 3-1 0-1-1-2-2-3 0-4 2-8 5-12z" class="E"></path><path d="M155 231c-1-1-1-2-1-2 0-3 2-5 4-7 6-6 13-6 22-6h2l-1 1c-3 1-5 3-7 6-1 1-2 3-3 4-3-2-7-2-10-1-2 0-5 2-6 5zm530 573c-1 3-4 7-6 10h-6c-9 0-19 0-28-1-17-2-34-9-46-21-9-9-15-20-18-32-2-10-2-21-2-31l1-53V541l38-1c9 0 18 0 26 1 20 1 40 6 57 16 23 15 32 40 38 65h18l-14 23h-19c-2-15-6-31-14-45-9-15-22-27-38-33-5-2-11-4-17-6l3 5c1 1 2 2 2 4-2 0-3 1-5 1-1 0-3-2-4-2s-5 2-7 2c-2 1-4 1-6 1-5-1-8-4-13-5-2-1-4 0-6 0-3 0-8 1-10 4l-2 1c-1 2-4 3-6 3 0 2-3 6-4 6-5 10-8 20-7 31 1 3 2 8 1 11-1 1 0 3 0 5l-1-2-3-3c0 8 0 18 4 25 5 13 12 23 21 34l3 12v2l1 1c0 1 0 2 1 2h-1l-3-5-2-3c0 6 0 12 1 18h0c1 7 3 14 6 20h1c1 4 3 7 5 10 0 2 1 2 2 3l3 3c1 1 3 2 4 3v1c-2 0-2 0-4-1h-2c0-1-1-1-1-2l-2-2v1c1 2 3 4 5 6h0l2 1c2 2 6 4 9 4 1 1 3 1 4 2h-3c0-1-1-1-1-1h-1-1c-1-1-4 0-6-2 0-1-1-1-2-2h-1l-2-1c-1 0-2 0-3-1h-1-2v2c1 1 3 3 4 5l-1 1c2 1 3 3 3 5h0c-6-1-12-10-14-15l-3-6-1-1 2 9c-1 3-1 10-1 14 2 0 2 1 3 2l1 1c1 1 1 2 2 3h0l-1 4c1 3 1 8 4 10l3 2c3 3 7 5 11 7 2 0 5 1 7 1v-1c-2-2-3-5-3-8 0-5 2-10 5-14 4-3 6-4 11-4-1-1-1-2-1-3l-2-1c4-4 8-5 13-5 8-1 15 3 21 7 3 3 5 7 6 11 1 2 2 4 1 6 1 6-1 16-5 21h-1z" class="C"></path><path d="M678 804h0c0 2-1 4-3 5-1 1-2 2-4 2 0-1 2-3 3-4l1 1 1-1c1-2 1-2 2-3z" class="M"></path><path d="M597 581c0-1 1-2 2-3v-1c1-1 2-2 3-4-2 1-3 2-4 3v-1c1-1 2-2 4-3h0c1-1 2-1 3-2s2-3 3-4l3-1h0c-2 2-4 3-5 6 1 0 1 0 2-1s3-2 4-2c1-1 3-1 4-1l1-1h1l1 1c-3 0-8 1-10 4l-2 1c-1 2-4 3-6 3 0 2-3 6-4 6h0z" class="P"></path><defs><linearGradient id="BD" x1="604.924" y1="727.771" x2="595.076" y2="734.229" xlink:href="#B"><stop offset="0" stop-color="#1b2227"></stop><stop offset="1" stop-color="#403c3b"></stop></linearGradient></defs><path fill="url(#BD)" d="M600 741l-1-22 1-1 1 1c-1 4 1 6 1 10 1 2 0 4 1 6 0 2 0 3-1 5h0l-1 11c-1-1-1-3-1-5v-5z"></path><path d="M608 758c1 0 1 1 1 2v5h1c2 0 2 1 3 2l1 1c1 1 1 2 2 3h0l-1 4h0l-1-2-3 2-1-2c-1 2-1 5-1 7l-1 3v-11-2c-1-1 0-2 0-3 1-2 0-6 0-8v-1z" class="B"></path><path d="M611 770h1v2l1 1h1l-3 2-1-2c0-1 0-1-1-2l2-1z" class="J"></path><path d="M611 770l-1-1 2-1h1c1 1 1 3 2 5h-2l-1-1v-2h-1z" class="C"></path><path d="M601 719h0v-2l1 1v1h0c0-1 0-1 1-2 0-1 0-3 1-5l2-6c0 5 0 10 1 15v1 5l-1 2 1 4h-1c0-3-2-7-3-10v12c-1-2 0-4-1-6 0-4-2-6-1-10z" class="M"></path><defs><linearGradient id="BE" x1="603.067" y1="717.005" x2="616.016" y2="722.809" xlink:href="#B"><stop offset="0" stop-color="#141614"></stop><stop offset="1" stop-color="#2f3134"></stop></linearGradient></defs><path fill="url(#BE)" d="M608 717c-1-3-1-9 0-12 1 1 2 2 2 3v14c1 10 4 22 11 31 1 1 3 3 4 5l-1 1c-1-1-2-2-2-3-4-4-5-8-8-13l-3-9-2-1v1l-1-1c0-2 0-3-1-5v-1-5c1-1 0-4 1-5z"></path><path d="M608 717l1 16v1l-1-1c0-2 0-3-1-5v-1-5c1-1 0-4 1-5z" class="P"></path><path d="M607 733l-1-4 1-2v1c1 2 1 3 1 5l1 1v-1l2 1 3 9c3 5 4 9 8 13 0 1 1 2 2 3 2 1 3 3 3 5h0c-6-1-12-10-14-15l-3-6-1-1c0-1 0-2-1-4 0-1-1-3-1-5z" class="B"></path><path d="M586 704h1c1 0 2 0 3-1h3v1c-1 3-3 7-2 9 0 3-3 6-4 9l-6 15 2-24 3-9z" class="R"></path><path d="M587 623c-1-11-1-23 1-34 2-9 7-17 16-23h1l-5 4c-1 0-1 1-2 1-1 1 0 1-1 2h-1c-1 2-1 4-1 6h0c0 2-1 2-1 4v3c1-1 2-3 3-5h0c-5 10-8 20-7 31 1 3 2 8 1 11-1 1 0 3 0 5l-1-2-3-3z" class="B"></path><defs><linearGradient id="BF" x1="618.921" y1="765.87" x2="591.579" y2="753.63" xlink:href="#B"><stop offset="0" stop-color="#232221"></stop><stop offset="1" stop-color="#34393d"></stop></linearGradient></defs><path fill="url(#BF)" d="M603 735v-12c1 3 3 7 3 10h1c0 2 1 4 1 5 1 2 1 3 1 4l2 9c-1 3-1 10-1 14h-1v-5c0-1 0-2-1-2v1c-1 2-1 3-1 5 0 5-1 9-1 14v2l-1 1v1c1 5 2 9 5 13l-1 1c-1-1-2-2-2-3-1-2-1-3-2-4l-1-1v-1c-3-4-3-9-4-15 1-1 1-4 1-5v-16l1-11h0c1-2 1-3 1-5z"></path><path d="M607 747c2 2 1 7 1 11v1c-1 2-1 3-1 5 0 5-1 9-1 14v2l-1 1v1c-1-4-1-8-1-11 0-2 0-4 1-6 2-5 2-13 2-18z" class="M"></path><path d="M604 771c1-2 1-3 2-4 1 3-1 8 0 11v2l-1 1v1c-1-4-1-8-1-11z" class="P"></path><defs><linearGradient id="BG" x1="603.977" y1="750.27" x2="613.023" y2="753.23" xlink:href="#B"><stop offset="0" stop-color="#1b1f25"></stop><stop offset="1" stop-color="#3c3d3c"></stop></linearGradient></defs><path fill="url(#BG)" d="M606 733h1c0 2 1 4 1 5 1 2 1 3 1 4l2 9c-1 3-1 10-1 14h-1v-5c0-1 0-2-1-2 0-4 1-9-1-11 0-5 0-10-1-14z"></path><path d="M591 762h0s0-1-1-2h0c-1-4 0-9 0-12l-1-1v3l-1 1c0 1 0 2-1 3-1 3 0 6-1 8l-1-1c1-1 0-3 1-4v-1-1c1-2 1-3 2-4v-3c0-1 0-2 1-3v-2c0-1 1-3 1-4 1-1 2-3 2-4 1-2 2-4 1-6-2 5-5 11-6 17h-1v-3h1l1-4c0-1 0-2 1-2v-3l1-1v-1c1-3 2-5 4-7v-1l1-5c1-1 1-2 2-3 0 1 0 4-1 6v8c0 1 0 3 1 4v1 2l1 1c0 1 0 2 1 3h1v5c0 2 0 4 1 5v16c0 1 0 4-1 5 1 6 1 11 4 15v1l1 1c1 1 1 2 2 4-2-1-3-3-4-5-2-3-5-7-7-11-3-5-4-9-5-15z" class="I"></path><path d="M600 746c0 2 0 4 1 5v16c0 1 0 4-1 5-1-8-1-17 0-26z" class="D"></path><path d="M591 762h0v-6c0-3 1-7 2-10v-2-2h1c0-2 0-3 1-5 0 1 1 1 1 2s2 2 2 3v3 9l1 14v8c0 1 0 2 1 2l-1 1c0 1 1 1 1 2v1c-1-2-2-4-4-5-3-5-4-9-5-15z" class="J"></path><defs><linearGradient id="BH" x1="585.224" y1="686.825" x2="602.494" y2="700.825" xlink:href="#B"><stop offset="0" stop-color="#28292c"></stop><stop offset="1" stop-color="#4e5154"></stop></linearGradient></defs><path fill="url(#BH)" d="M583 713c-1-1-1-1-1-3v-1c-1 0-1-1-1-1l1-1v-2l-1-3c-1-4-1-6 1-9v-2c1-2 3-4 5-5v-5c1-4 0-8 1-11v14c-1 2-1 5 1 7l1-1c1-1 3-2 3-3v-4c-1-4-1-7-2-11-1-3-2-5-1-7 1 1 0 4 1 5 1 2 1 6 2 8 1-1 1-1 1-2h1v-2c-1-1-1-2-1-3h0l2 3c0 1 0 1 1 2l1 3c3 1 5 1 8 2h1v-1h0 1c2 4 2 9 1 13-1 3-2 5-4 7-1 1-2 3-4 4l-10 9c-1-2 1-6 2-9v-1h-3c-1 1-2 1-3 1h-1l-3 9z"></path><path d="M599 700c0-4 1-7 1-10v-1l3 6-1 4c-1 0-2 0-3 1z" class="P"></path><path d="M603 695l2 5c-1 1-2 3-4 4l-3-3 1-1c1-1 2-1 3-1l1-4z" class="M"></path><path d="M586 704h1c1-2 1-3 3-4 3-1 6 0 8 1l3 3-10 9c-1-2 1-6 2-9v-1h-3c-1 1-2 1-3 1h-1z" class="d"></path><defs><linearGradient id="BI" x1="669.034" y1="807.988" x2="652.169" y2="761.902" xlink:href="#B"><stop offset="0" stop-color="#191a1d"></stop><stop offset="1" stop-color="#5a5d60"></stop></linearGradient></defs><path fill="url(#BI)" d="M652 765l-2-1c4-4 8-5 13-5 8-1 15 3 21 7 3 3 5 7 6 11 1 2 2 4 1 6 1 6-1 16-5 21h-1v-1l1-1v-1-1-1c-1-1-1-1-1-2l1-1v-2-1 1c-1 3-3 6-5 9v-2l3-7 1-2c0-1 0 1 0-1 1-1 0-2 1-3l-3-1c-1 3-1 5-2 8s-2 6-4 9h-1c4-7 7-15 6-22-1 5-2 13-4 17-3 5-6 10-11 12h-2v-1h1c5-4 10-13 11-19 0-2 0-4-1-6v-3 8c-2 5-6 11-10 14-2 2-3 3-5 4h-1c-8 4-16 2-25 0 6 0 11-1 17-2h-6c1-1 3-1 4-1s1 0 2-1c5-1 11-4 15-9 2-3 4-6 3-10 0-6-5-9-9-13l-3-1c-1-2-3-2-5-3-1-1-1-2-1-3z"></path><path d="M652 765c0-1 0-1 1-1 2 1 3 1 5 1h5v2h-1l-2 2s0 1 1 2v1h0 0l-3-1c-1-2-3-2-5-3-1-1-1-2-1-3z" class="D"></path><path d="M688 777c-1-1 0-1 0-2 0 1 1 1 2 2 1 2 2 4 1 6 1 6-1 16-5 21h-1v-1l1-1v-1-1-1l1-2v-2l1-3v-3-1h0c1-2 1-7 1-8-1-1-1-2-1-3z" class="G"></path><path d="M688 777c-1-1 0-1 0-2 0 1 1 1 2 2 1 2 2 4 1 6h0v-1l-1 1-2-6z" class="D"></path><defs><linearGradient id="BJ" x1="647.154" y1="805.292" x2="631.777" y2="764.841" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#35383b"></stop></linearGradient></defs><path fill="url(#BJ)" d="M640 794c-2-2-3-5-3-8 0-5 2-10 5-14 4-3 6-4 11-4 2 1 4 1 5 3l3 1c4 4 9 7 9 13 1 4-1 7-3 10-4 5-10 8-15 9h-12-3l-2-1-3-1h-2-1c-1 0-2 0-3-1l-2-1c-2 0-4-1-6-2v-1l-2-1c-4-3-7-8-8-13l1-3c0-2 0-5 1-7l1 2 3-2 1 2h0c1 3 1 8 4 10l3 2c3 3 7 5 11 7 2 0 5 1 7 1v-1z"></path><path d="M614 773l1 2h0c1 3 1 8 4 10l3 2c3 3 7 5 11 7l-3 1c-2-1-5-2-7-4s-5-3-7-6c-1-2-1-4-3-6v-1c-1-1-2-2-2-3l3-2z" class="D"></path><path d="M614 773l1 2-2 3c-1-1-2-2-2-3l3-2z" class="C"></path><path d="M640 794c-2-2-3-5-3-8 0-5 2-10 5-14 4-3 6-4 11-4 2 1 4 1 5 3h0v1l-1 1h1c1 1 0 0 2 1 3 1 5 4 6 6 1 3 1 6 0 9-2 3-4 5-8 7-4 1-9 1-13-1l-4-2s-1 0-1 1z" class="Y"></path><path d="M654 771h4v1l-1 1h-6v-1c1-1 2-1 3-1z" class="I"></path><path d="M651 773c-2 2-4 4-5 7-1 1-1 3-1 4l1 1-1 1c0-1-1-2-1-2-1-2 0-5 0-6 1-2 3-5 5-6 0 0 1 0 1-1l1-1 3 1c-1 0-2 0-3 1v1z" class="B"></path><path d="M659 781h2l1 1v2 1h0c-1 2-3 4-4 5-2 0-4 0-5-1h0l-1-1c1 0 1 0 2-1h-1l1-1h1v-1h1l1 2h1v-1c0-2 1-2 2-4l-1-1z" class="H"></path><path d="M594 475h-14l-1-215h41c21 0 41 0 62 7 28 11 49 34 66 59 4 7 8 13 11 20l12 1v2l-1 2c-1 1-2 2-3 4s-3 4-4 6v1c4 2 14 1 16 5 1 3 1 8 0 11l-1 2h0-1s-1 0-1-1v-2c0-2 0-4-1-5l-2-2h-1c-1-2-4-4-7-5l-2-2-2 2c0 1-1 2-1 3-2 1-8 0-11 0h-1c0-6-24-37-29-44-1 6-3 12-4 18-6-4-8-7-10-12-2 3-5 9-8 11h-2c-5-9-2-19-5-28-2-9-8-15-16-20l-1-1-1 1-6-3-1 1v1c3 2 6 4 7 8 6 5 9 10 9 18v3c0 4-2 8-3 12-1 2-3 5-4 7h0c-1 2-2 3-3 4h0l2-1c0 1-1 2-2 2l1 1c-2 3-5 5-8 7-2 2-4 3-6 4l1 1c5-1 10-4 15-6 1-1 4-2 5-2 2-1 9-3 12-3s5 1 8 1h0c3-1 8 1 11 2 8 3 14 10 18 18l2 6c1 7 2 12 0 19-1 4-3 9-4 13-7 18-17 37-32 49-20 16-44 20-68 20 0-1 0-2-1-3h-1-5c-1 0-1-2-2-2h0l-1 1v1c-2-1-2-2-4-3 0 2-1 3-2 4v1h-1c-2-3-2-7-2-10v1c-1 2-1 4-1 6 1 1 2 2 2 3-5 1-9 0-14 1v-1 1z" class="C"></path><path d="M613 439l2-2c0 1 0 1 1 1l1 1c-1 0-2 1-3 2l-1-1v-1z" class="K"></path><path d="M643 315l1 4v1l1 1h-2c-1 1-2 1-4 0l4-6z" class="Q"></path><path d="M617 364c-4-2-7-4-9-6 4 0 8 3 12 5h-2l-1 1z" class="M"></path><path d="M646 276c6-1 13-1 19-1-1 1-3 1-5 1-3 0-6 1-9 1v-1h-5z" class="G"></path><path d="M595 449c2 1 3 4 4 6 1 3 2 5 4 8h0l-1-2h-1v2c-1-1-3-4-3-6l-3-8z" class="Z"></path><path d="M592 307c3-3 5-7 8-9 0 1-1 2-1 3l1-1c1-1 1-1 2-1l-9 10-1-2z" class="K"></path><path d="M614 385l2-1c3-1 5-3 7-4 1 1 2 0 4 0l1 1-8 3c-3 1-5 3-8 3l2-2zm3-21l1-1h2l12 3c-1 1-2 1-3 1-5 0-8-1-12-3z" class="G"></path><path d="M593 309c0 1-1 2-2 3l-5 9v-1c0-4 4-10 6-13l1 2z" class="P"></path><path d="M604 405l-1 3-1 1 1 1c-4 3-7 6-10 11 2-6 6-11 11-16z" class="J"></path><path d="M588 394c0-2 0-4 1-5 1-2 2-3 3-4l1-1h0v-1h1c3 0 5-5 9-5v2c0 1 0 1-2 2h-2c0 1-1 2-3 3h-2c-3 2-5 6-6 9z" class="M"></path><path d="M603 433h1v23c-3-8-4-14-1-23z" class="b"></path><path d="M644 332v4c1-1 1-1 3-1v2c1 1 3 2 4 3 2 2 6 5 6 8 0 2-1 3-2 4h-1c0-1 1-2 1-4 0-3-4-7-7-7-2-1-4-1-6-3 0-1 1-3 1-5l1-1z" class="Q"></path><path d="M631 355c-1-2 0-2 0-3h1c3 3 6 5 10 6 2 0 4-1 6-1-1 2-4 3-6 4-2 0-4 0-6 1-3-3-4-4-5-7z" class="H"></path><path d="M588 394c1-3 3-7 6-9h2c2-1 3-2 3-3h2c-5 6-9 12-13 19h0l1-1h0v-1h0c0-1 0-1 1-2 0-1-1-2-1-4l-1 3v-1l-1-1h1z" class="B"></path><defs><linearGradient id="BK" x1="619.493" y1="430.186" x2="641.044" y2="434.632" xlink:href="#B"><stop offset="0" stop-color="#2a2c30"></stop><stop offset="1" stop-color="#4f5152"></stop></linearGradient></defs><path fill="url(#BK)" d="M615 437c1-2 4-4 7-5 5-3 13-5 19-3l4 1c1 1 1 1 1 2-5-1-10-1-15 0s-11 3-14 7l-1-1c-1 0-1 0-1-1z"></path><path d="M607 411c2-2 3-3 6-3v1l-1 3-10 17c1-7 0-13 5-18z" class="d"></path><path d="M647 337c5-1 11 0 16-2 2 0 4-1 7-2 2-2 4-3 6-5 1 0 2-1 2-1 1-2 2-5 3-6 0 4-2 8-3 12-2 1-4 2-5 4l-1-1c-2 0-4 1-7 2-2 0-11-1-12 0l-2 2c-1-1-3-2-4-3zm-25 17l9 1c1 3 2 4 5 7l4 1c-9 2-20-2-27-6-1 0-2-1-2-2 5 0 11 1 17 2v-1c-1 0-2-1-3-1l-3-1z" class="I"></path><path d="M590 371c-1 1-1 1-2 1-1-1-2-2-2-3-1-2-1-6 0-8 2-3 5-3 8-4 0 1 1 2 2 3s1 1 1 3c1 2 2 3 1 5-2 1-5 3-8 3z" class="V"></path><path d="M590 371v-1-3s2 0 2-1h0 0l-1-1c1-1 2-2 4-3h0v-1c-1-1-1 0-1-1-1 0 0 0 0-1 1 1 1 1 2 1 1 1 1 1 1 3 1 2 2 3 1 5-2 1-5 3-8 3z" class="O"></path><path d="M625 416l2 1c-1 1 0 1-1 1-2 1-3 3-5 4-1 1-2 2-3 4-2 2-4 4-4 7v2l-2 3 1 1v1c-1 1-1 2-2 3h-1 1v-1l-2 1c0-1-1-2-1-3 1-1 1-1 1-2-2 2-1 3-2 5v-1c-1-7 2-13 6-18 2-2 6-6 8-7h3l1-1z" class="I"></path><path d="M625 416l2 1c-1 1 0 1-1 1-2 1-3 3-5 4-1 1-2 2-3 4-2 2-4 4-4 7v2l-2 3-2 3c0-1 0-3 1-4 0-8 7-15 13-20l1-1z" class="G"></path><defs><linearGradient id="BL" x1="597.441" y1="407.464" x2="593.013" y2="388.48" xlink:href="#B"><stop offset="0" stop-color="#303336"></stop><stop offset="1" stop-color="#4e5152"></stop></linearGradient></defs><path fill="url(#BL)" d="M603 384c-1 9-3 14-7 22-2 3-3 6-5 9 0-1 0-2 1-3l1-2-1-1c-2 0-3-1-4-1l-1 3h0c0-2 1-5 2-7 4-8 8-13 14-20z"></path><path d="M600 298l2-2c3-3 5-5 8-7v-1c11-7 23-12 36-12h5v1c-14 2-28 6-40 14-3 3-6 5-9 8-1 0-1 0-2 1l-1 1c0-1 1-2 1-3z" class="B"></path><defs><linearGradient id="BM" x1="614.209" y1="431.964" x2="641.914" y2="424.749" xlink:href="#B"><stop offset="0" stop-color="#060709"></stop><stop offset="1" stop-color="#333335"></stop></linearGradient></defs><path fill="url(#BM)" d="M625 425c6-2 13-1 19 2h0c0 1 1 2 2 3h-1l-4-1c-6-2-14 0-19 3-3 1-6 3-7 5l-2 2-1-1 2-3v-2c0-3 2-5 4-7v1 1l1-1c1 0 3 0 4-1 1 0 2 0 2-1z"></path><path d="M618 426v1 1l1-1c1 0 3 0 4-1-3 3-6 5-8 8l-1 1h0v-2c0-3 2-5 4-7z" class="B"></path><defs><linearGradient id="BN" x1="612.241" y1="331.235" x2="608.692" y2="307.82" xlink:href="#B"><stop offset="0" stop-color="#1f2226"></stop><stop offset="1" stop-color="#4b4f52"></stop></linearGradient></defs><path fill="url(#BN)" d="M623 305h2c2 0 3 0 4 1-1 2-7 3-9 4s-4 2-7 4c-7 5-16 15-20 24-1 3-1 6-2 9 0 2 0 3-1 4h0v-3l3-14c1-4 3-6 4-9 6-10 15-17 26-20z"></path><path d="M649 430c2 2 3 3 3 6l-1 2c-3 3-12 1-16 1-3 0-6 1-8 2-6 1-10 5-14 9v-2l4-3c1-1 0-1 1-2h1v-1h-1l3-2h0c1 0 2-1 4-2h0l1-1c-2 0-5 1-7 2-1 1 0 1-1 1v-1c1-1 3-2 4-2 7-2 13-3 20-3h6l1-1-1-1 1-2z" class="B"></path><path d="M649 430c2 2 3 3 3 6l-1 2v-1c-2-2-11-2-14-2h0c1 0 3 0 4-1h1 6l1-1-1-1 1-2z" class="W"></path><path d="M594 357v-1c2 0 3-1 3-2l1-4c5 9 11 16 14 25 1 4 2 7 2 10l-2 2c-1 1-2 2-3 1l-2-3c-1-2-2-3-1-4l1-1h0c0-5-5-9-9-12 1-2 0-3-1-5 0-2 0-2-1-3s-2-2-2-3z" class="T"></path><defs><linearGradient id="BO" x1="638.693" y1="304.731" x2="645.404" y2="283.785" xlink:href="#B"><stop offset="0" stop-color="#303436"></stop><stop offset="1" stop-color="#4a4d50"></stop></linearGradient></defs><path fill="url(#BO)" d="M672 300c-10-6-21-10-32-10-13 1-28 6-36 16-3 4-5 8-8 11h0c4-9 13-19 21-24 13-7 28-10 42-5 3 0 5 1 7 2l-1 1v1c3 2 6 4 7 8z"></path><path d="M659 288c3 0 5 1 7 2l-1 1v1c-2-1-6-2-6-4h0z" class="R"></path><path d="M613 357c-2 0-5-2-6-4l2-1h-2-1c-1-1 0-1 0-2h0c1 0 2 0 2-1-1 0-1 0-2-1l-1-1c1-1 1 0 2-1h0c-1 0-1 0-2-1 0-1 0 0 1-1l1-1h-2v-1l1-1-1-1v-1l-1-1c0-1 1-1 2-2v1h3l-2 2h0l2 1c-1 1-2 1-3 2h3l3-1c1-1 1-1 3-1v1h0v1c2 0 4 1 5 1l2 1c1-1 3-1 5 0l3 3 1 1h2c-1 1-3 1-4 2 2 1 2 0 3 2h-1c0 1-1 1 0 3l-9-1 3 1c1 0 2 1 3 1v1c-6-1-12-2-17-2 0 1 1 2 2 2z" class="B"></path><path d="M622 344c1-1 3-1 5 0l3 3 1 1c-1 0-2 1-3 1-3 0-5 0-7-2 0-1 0-2 1-3z" class="C"></path><path d="M613 357c-2 0-5-2-6-4l2-1h-2-1c-1-1 0-1 0-2h0c1 0 2 0 2-1-1 0-1 0-2-1l-1-1c1-1 1 0 2-1h0c-1 0-1 0-2-1 0-1 0 0 1-1l1-1h-2v-1l1-1-1-1v-1l-1-1c0-1 1-1 2-2v1h3l-2 2h0l2 1c-1 1-2 1-3 2h3l3-1v1c-2 1-5 2-6 3h10c-3 1-6 2-9 2l6 1c-1 1-3 1-3 1 2 1 4 1 6 2 1 0 3 1 3 1h-2c-2-1-5-1-8-1 1 1 3 1 4 1 3 1 6 2 9 2l3 1c1 0 2 1 3 1v1c-6-1-12-2-17-2 0 1 1 2 2 2z" class="Z"></path><defs><linearGradient id="BP" x1="652.366" y1="338.399" x2="661.796" y2="353.386" xlink:href="#B"><stop offset="0" stop-color="#1f2122"></stop><stop offset="1" stop-color="#494c50"></stop></linearGradient></defs><path fill="url(#BP)" d="M673 337c1-2 3-3 5-4-1 2-3 5-4 7h0c-1 2-2 3-3 4h0l2-1c0 1-1 2-2 2l1 1c-2 3-5 5-8 7-2 2-4 3-6 4l1 1-1 1-1-1h0c-4 1-8 4-12 4l-5 1-4-1c2-1 4-1 6-1 2-1 5-2 6-4 0 0 5-5 6-5h1c1-1 2-2 2-4 0-3-4-6-6-8l2-2c1-1 10 0 12 0 3-1 5-2 7-2l1 1z"></path><path d="M673 337c1-2 3-3 5-4-1 2-3 5-4 7-1 0-2 1-3 2v1l-1-1-2 2h0l1-2 1-1c0-2 1-3 3-4z" class="J"></path><path d="M611 333c-2 0-4 1-6 2h0c1-4 2-8 4-11 4-4 12-11 18-12 2 0 2 0 4 2l2 2v1l-1 2-4 5c4 7 8 16 14 22-3 1-6 2-9 2h-2l-1-1-3-3c-2-1-4-1-5 0l-2-1c-1 0-3-1-5-1v-1h0v-1c-2 0-2 0-3 1l-3 1h-3c1-1 2-1 3-2l-2-1h0l2-2h-3v-1l5-3z" class="D"></path><path d="M611 333h0l-3 3h1 2c2 1 4 1 5 1l1 1h-1c-2 0-5 1-7 3v1h-3c1-1 2-1 3-2l-2-1h0l2-2h-3v-1l5-3z" class="G"></path><path d="M611 333c-2 0-4 1-6 2h0c1-4 2-8 4-11 4-4 12-11 18-12 2 0 2 0 4 2l2 2v1l-1 2c-2 0-5 0-7 1-2 0-4 2-5 3l-11 8-2 2c2-1 2-1 3-2h1 0c1-1 1-1 2-1 3 0 10-5 13-3l2 2c-1 2-6 1-8 1l-9 3h0z" class="K"></path><path d="M679 350c2-1 9-3 12-3s5 1 8 1h0c-1 1-1 2-2 2l-1 1-24 12c2 1 5 0 7 0-2 1-3 1-5 2l-1 1c-7 1-13 2-19 5-2 1-5 1-6 2l-4 4c-4 1-10 1-14 3l-2 1-1-1c-2 0-3 1-4 0-2 1-4 3-7 4l-2 1c0-3-1-6-2-10 6 0 12-6 17-8 1 0 2 0 3-1 3-1 7-2 11-2v1h0 2v-3c4 0 8-3 12-4h0l1 1 1-1c5-1 10-4 15-6 1-1 4-2 5-2z" class="J"></path><path d="M645 365c4-2 10-3 14-3-4 3-10 2-14 6-3 1-6 1-8 2-6 2-11 5-15 8h-1c5-4 12-7 17-11 1-1 3-2 5-2h0 2z" class="I"></path><path d="M653 369c6-2 12-4 19-6 2 1 5 0 7 0-2 1-3 1-5 2l-1 1c-7 1-13 2-19 5-2 1-5 1-6 2l-4 4c-4 1-10 1-14 3l-2 1-1-1c-2 0-3 1-4 0l17-7c5-2 9-3 13-4z" class="Z"></path><path d="M627 380l21-7-4 4c-4 1-10 1-14 3l-2 1-1-1z" class="J"></path><defs><linearGradient id="BQ" x1="666.107" y1="351.35" x2="682.679" y2="365.312" xlink:href="#B"><stop offset="0" stop-color="#35383a"></stop><stop offset="1" stop-color="#6d7071"></stop></linearGradient></defs><path fill="url(#BQ)" d="M679 350c2-1 9-3 12-3s5 1 8 1h0c-1 1-1 2-2 2l-1 1-24 12c-7 2-13 4-19 6v-1c0-1 0-1 1-2h-2l-7 2c4-4 10-3 14-6-4 0-10 1-14 3v-3c4 0 8-3 12-4h0l1 1 1-1c5-1 10-4 15-6 1-1 4-2 5-2z"></path><path d="M674 352c1-1 4-2 5-2l1 2c-1 1-4 2-5 3l-1-1 1-1-1-1z" class="J"></path><defs><linearGradient id="BR" x1="651.235" y1="355.229" x2="671.955" y2="359.309" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#2b2c31"></stop></linearGradient></defs><path fill="url(#BR)" d="M659 358c5-1 10-4 15-6l1 1-1 1 1 1c-6 2-11 6-16 7-4 0-10 1-14 3v-3c4 0 8-3 12-4h0l1 1 1-1z"></path><defs><linearGradient id="BS" x1="656.54" y1="304.409" x2="658.763" y2="334.887" xlink:href="#B"><stop offset="0" stop-color="#323437"></stop><stop offset="1" stop-color="#4f5457"></stop></linearGradient></defs><path fill="url(#BS)" d="M629 299h-2c1-2 3-3 5-4-3 0-7 3-9 2 7-4 19-4 27-3 4 1 8 2 11 4 3 1 6 1 8 4h0c4 4 9 10 9 16-1 4-3 7-6 9-4 4-10 5-16 6-3 0-6 1-9 2-2 0-2 0-3 1v-4c-2-1-4-2-5-4s0-5 0-7c2 1 3 1 4 0h2l-1-1v-1l-1-4c-1-3-2-5-5-8-5-4-10-5-16-5h0c2-1 4-1 5 0h3v-1h1c-2 0-3 0-4-1h0 2v-1z"></path><path d="M650 320l1 1-1 1h1 4 3v2c-1 1-1 1-3 2-2-1-3-2-5-2-2-1-4-1-6-2l-1-1h2c1-1 3-1 5-1z" class="B"></path><path d="M650 320l1 1-1 1h1 4l-2 1c-3 0-6-1-9-1l-1-1h2c1-1 3-1 5-1z" class="H"></path><path d="M650 294c4 1 8 2 11 4 3 1 6 1 8 4h0-1v1c2 4 4 8 4 12l-1 1c0 2-2 4-4 5 0 0-1 0-1 1-2-1-2-1-2-3h1l-2-3h0c1 1 3 2 3 3v1h1l1-1 3-3c0-4-3-8-5-11-3-6-10-8-16-11z" class="M"></path><path d="M629 299h-2c1-2 3-3 5-4-3 0-7 3-9 2 7-4 19-4 27-3 6 3 13 5 16 11 2 3 5 7 5 11l-3 3-1 1h-1v-1c0-1-2-2-3-3h0c-1-1-1-3-1-3 1-1 0-1 1-2h-1l-1-3c0 4-1 6-3 8-3 2-6 3-8 4-2 0-4 0-5 1l-1-1c5-1 10-2 13-6 2-2 2-5 2-7-3-5-8-7-14-9-3-1-7-2-10-2h-1c-2 1-3 1-5 3z" class="K"></path><path d="M661 308c-2-5-5-7-10-9-2-1-4-1-5-2h1c5-1 11 2 15 6v1l1-1c1 1 2 2 3 2 2 3 5 7 5 11l-3 3-1 1h-1v-1c0-1-2-2-3-3h0c-1-1-1-3-1-3 1-1 0-1 1-2h-1l-1-3z" class="D"></path><defs><linearGradient id="BT" x1="638.647" y1="293.213" x2="650.749" y2="317.62" xlink:href="#B"><stop offset="0" stop-color="#2e3133"></stop><stop offset="1" stop-color="#4a4e51"></stop></linearGradient></defs><path fill="url(#BT)" d="M629 299c2-2 3-2 5-3h1c3 0 7 1 10 2 6 2 11 4 14 9 0 2 0 5-2 7-3 4-8 5-13 6v-1l-1-4c-1-3-2-5-5-8-5-4-10-5-16-5h0c2-1 4-1 5 0h3v-1h1c-2 0-3 0-4-1h0 2v-1z"></path><path d="M673 366h0l-1 2h11v1h0c3 1 7 2 10 4s6 5 5 9v3l-3 3c-1 2-3 3-3 5l-4 1s-1-1-1-2h0 0l-1 3v1c-2 4-6 6-10 7l-4 1h-1c-4 1-8 0-12 0 2 2 6 5 8 9 0 1-1 1-1 2-1 2 3 11 4 14-8-3-16-8-25-9h1v-2l-8-2c-5 1-9-2-14-1-2 0-6-2-8-4l-4 1 1-3v-1c-3 0-4 1-6 3l-3-1h-1l-1-1 1-1 1-3c2-2 5-3 8-5-4 0-10 4-13 7l-1 1-2 2 5-8c1-3 4-6 7-8 2-2 4-3 7-4 4-4 9-7 15-8v-2c4-2 10-2 14-3l4-4c1-1 4-1 6-2 6-3 12-4 19-5z" class="W"></path><path d="M660 394l10 1c-4 0-8 0-12-1h2zm-5 4c2 0 5 1 7 0 2 0 6 1 8 2h1-11l-2-1h-1l-2-1z" class="H"></path><path d="M612 400h1 2 2c1 0 3 0 4 1-3 0-5 0-7 1-3 1-6 3-9 3v1c0 1-1 2-2 2l1-3c2-2 5-3 8-5z" class="Y"></path><path d="M615 390c2 0 5-1 8-2h7c-3 0-4 0-6 1h-3c-1 0-2 1-3 1 1 0 4 1 5 0h4c-2 1-3 1-4 1-2 0-4 0-6 1-3 0-5 1-7 1l-2 1c2-2 4-3 7-4z" class="Q"></path><path d="M631 412c2 0 3 0 4 1 2 0 3 1 5 0 3 2 6 4 8 6-1 1-1 1-2 1v-2l-8-2c-2-1-5-3-7-4z" class="K"></path><path d="M644 391l1-1h0c2 1 3 1 5 1l10 3h-2c-2 0-4-1-6-1l10 5c-2 1-5 0-7 0-1-2-7-5-8-6l-3-1z" class="D"></path><path d="M657 379l17 1c2 0 3 0 5 1-1 2-4 3-6 3-7 2-13 0-20-3 1-1 3-2 4-2z" class="d"></path><path d="M604 410c2-2 5-3 8-3h1c9-2 19 1 27 6-2 1-3 0-5 0-1-1-2-1-4-1 2 1 5 3 7 4-5 1-9-2-14-1-2 0-6-2-8-4l-4 1 1-3v-1c-3 0-4 1-6 3l-3-1z" class="M"></path><path d="M616 411c0-1-1-1 0-2 3-2 11 2 15 3 2 1 5 3 7 4-5 1-9-2-14-1-2 0-6-2-8-4z" class="Q"></path><defs><linearGradient id="BU" x1="634.571" y1="390.248" x2="638.549" y2="374.783" xlink:href="#B"><stop offset="0" stop-color="#353a3d"></stop><stop offset="1" stop-color="#67686b"></stop></linearGradient></defs><path fill="url(#BU)" d="M648 378c3 0 6 0 9 1h0c-1 0-3 1-4 2h-1l-11 1c0 1-1 0 0 1h-1c-1 0-1 0-2 1h-1l-1 1-1-1h-5c-1 0-1 0-2 1 3 0 7 0 10 1h2c3 0 8 3 10 5-2 0-3 0-5-1h0l-1 1-3-1c-1 0-2-1-3-1h-3c-2-1-4-1-5-1h-7c-3 1-6 2-8 2 4-4 9-7 15-8l18-4z"></path><path d="M627 390l6 3c1 0 2 1 3 1s2 0 3 1l1 1s1 0 1 1c1 0 2 0 2 1h-1c-2-1-4-1-6-1 3 1 6 3 9 5 1 0 2 2 3 2 4 3 7 7 10 11-1 0-1-1-2-1 0 1 2 2 3 3-7-4-14-9-21-13h-1l-3-1c-4-1-9-3-13-2-1-1-3-1-4-1h-2-2-1c-4 0-10 4-13 7l-1 1-2 2 5-8c1-3 4-6 7-8l2-1c2 0 4-1 7-1 2-1 4-1 6-1 1 0 2 0 4-1z" class="Q"></path><path d="M620 397c2 0 3 0 6 1 3 0 7-1 10 0h1c2 1 4 2 5 3l1 1 1 1c1 0 1 1 1 2h0-2c-2-1-5-2-8-3h-1c-3-1-12-3-14-5z" class="W"></path><defs><linearGradient id="BV" x1="603.434" y1="399.28" x2="632.479" y2="390.589" xlink:href="#B"><stop offset="0" stop-color="#202123"></stop><stop offset="1" stop-color="#44494c"></stop></linearGradient></defs><path fill="url(#BV)" d="M627 390l6 3c1 0 2 1 3 1s2 0 3 1h-1-2c-2-1-4 0-5 0-1 1-3 1-5 1-3 0-5-1-8 0-4 1-8 1-12 3-1 1-3 3-5 3 1-3 4-6 7-8l2-1c2 0 4-1 7-1 2-1 4-1 6-1 1 0 2 0 4-1z"></path><path d="M673 366h0l-1 2h11v1h0c3 1 7 2 10 4s6 5 5 9v3l-3 3c-1 2-3 3-3 5l-4 1s-1-1-1-2h0 0l-1-1-1-3c-1-3-3-5-6-7-2-1-3-1-5-1l-17-1h0c-3-1-6-1-9-1l-18 4v-2c4-2 10-2 14-3l4-4c1-1 4-1 6-2 6-3 12-4 19-5z" class="C"></path><defs><linearGradient id="BW" x1="661.608" y1="366.315" x2="667.392" y2="372.685" xlink:href="#B"><stop offset="0" stop-color="#292d30"></stop><stop offset="1" stop-color="#434648"></stop></linearGradient></defs><path fill="url(#BW)" d="M673 366h0l-1 2h11v1h0c-10 0-19 1-29 4v-2c6-3 12-4 19-5z"></path><defs><linearGradient id="BX" x1="685.401" y1="379.695" x2="683.64" y2="391.341" xlink:href="#B"><stop offset="0" stop-color="#202224"></stop><stop offset="1" stop-color="#414245"></stop></linearGradient></defs><path fill="url(#BX)" d="M675 375c6 2 9 5 14 7l8 3h1l-3 3c-1 2-3 3-3 5l-4 1s-1-1-1-2h0 0l-1-1-1-3c-1-3-3-5-6-7-2-1-3-1-5-1l-1-3h0c2 1 4 1 6 1l-1-1c-1-1-2-1-3-2z"></path><path d="M689 382l8 3h1l-3 3c-2-2-7-2-9-4v-1-1h3z" class="G"></path><path d="M654 371v2h-3c8 1 17 0 24 2 1 1 2 1 3 2l1 1c-2 0-4 0-6-1h0l1 3-17-1h0c-3-1-6-1-9-1l-18 4v-2c4-2 10-2 14-3l4-4c1-1 4-1 6-2z" class="I"></path><path d="M654 371v2h-3c-1 0-2 1-2 1 2 0 6-1 7 1h-5l-7 2 4-4c1-1 4-1 6-2z" class="B"></path><path d="M648 378c6-2 11-2 17-1 3 0 5 1 8 0l1 3-17-1h0c-3-1-6-1-9-1z" class="W"></path><path d="M686 396c1 4 3 6 6 7 4 2 9 4 13 4 1-1 3-1 4-1 1 6 0 12-1 17 0 1 0 5-1 5 0 3-1 7-4 9-3 1-6 4-9 5l-1 1c-1 0-1 0-2 1l-7 6-2 2 2 2 1-1c1 1 1 0 1 1v1l-5 5c-1 1-2 2-4 2v1c-1-1-1 0-1-1v-1c-4-2-6 0-10 1-2 0-4 1-6 1l-2 1h-1c-3-3-4-7-6-10h-1c-3-1-4-2-7-3-6-3-14-3-19 0-2 1-4 2-5 4l-1 2c-2 2-4 3-5 6v1c0 1-1 4-1 5 0 2-1 3-2 4v1h-1c-2-3-2-7-2-10 0 0 1-4 1-5 1-3 3-6 5-9 4-4 8-8 14-9 2-1 5-2 8-2 4 0 13 2 16-1l1-2c0-3-1-4-3-6l-1 2 1 1-1 1-2-2c0-1 0-1-1-2h1c-1-1-2-2-2-3h0c-6-3-13-4-19-2 0 1-1 1-2 1-1 1-3 1-4 1l-1 1v-1-1c1-2 2-3 3-4 2-1 3-3 5-4 1 0 0 0 1-1l-2-1-1-1c5-1 9 2 14 1l8 2v2h-1c9 1 17 6 25 9-1-3-5-12-4-14 0-1 1-1 1-2-2-4-6-7-8-9 4 0 8 1 12 0h1l4-1c4-1 8-3 10-7z" class="d"></path><path d="M644 427c2 1 3 1 5 3l-1 2 1 1-1 1-2-2c0-1 0-1-1-2h1c-1-1-2-2-2-3z" class="Q"></path><defs><linearGradient id="BY" x1="608.513" y1="460.735" x2="639.716" y2="452.752" xlink:href="#B"><stop offset="0" stop-color="#4a4f53"></stop><stop offset="1" stop-color="#6c6b6d"></stop></linearGradient></defs><path fill="url(#BY)" d="M609 474c1-2 0-4 0-6 1-5 3-11 6-15 5-4 11-6 18-6s13 2 18 7h-1c-3-1-4-2-7-3-6-3-14-3-19 0-2 1-4 2-5 4l-1 2c-2 2-4 3-5 6v1c0 1-1 4-1 5 0 2-1 3-2 4v1h-1z"></path><path d="M692 427c0 2-2 3-3 4l-8 8c-3 3-5 6-8 9 0 1-1 2-2 2h0c-3 3-5 7-6 10-1-3-1-6 0-9 3-6 6-13 12-17l-1 8 16-15z" class="e"></path><defs><linearGradient id="BZ" x1="627.657" y1="431" x2="633.29" y2="414.654" xlink:href="#B"><stop offset="0" stop-color="#2d2e32"></stop><stop offset="1" stop-color="#4b4e52"></stop></linearGradient></defs><path fill="url(#BZ)" d="M624 415c5-1 9 2 14 1l8 2v2h-1c-5 0-10 1-15 3-2 0-3 1-4 1l-1 1c0 1-1 1-2 1-1 1-3 1-4 1l-1 1v-1-1c1-2 2-3 3-4 2-1 3-3 5-4 1 0 0 0 1-1l-2-1-1-1z"></path><defs><linearGradient id="Ba" x1="658.015" y1="462.505" x2="663.801" y2="444.565" xlink:href="#B"><stop offset="0" stop-color="#454546"></stop><stop offset="1" stop-color="#616465"></stop></linearGradient></defs><path fill="url(#Ba)" d="M705 407c1-1 3-1 4-1 1 6 0 12-1 17 0 1 0 5-1 5 0 3-1 7-4 9-3 1-6 4-9 5l-1 1c-1 0-1 0-2 1l-7 6-2 2 2 2 1-1c1 1 1 0 1 1v1l-5 5c-1 1-2 2-4 2v1c-1-1-1 0-1-1v-1c-4-2-6 0-10 1-2 0-4 1-6 1l-2 1h-1c-1-6-2-12-1-17 0-2 1-5 3-6l1 1c2 1 4 3 4 5l1 4c-1 3-1 6 0 9 1-3 3-7 6-10h0c1 0 2-1 2-2 3-3 5-6 8-9l8-8c1-1 3-2 3-4 0-1 5-6 6-8 3-3 6-8 7-12z"></path><path d="M660 463c1-1 1-2 2-2 4 0 6-1 10-3l1 1 2-1 2 2v2 1c-1-1-1 0-1-1v-1c-4-2-6 0-10 1-2 0-4 1-6 1z" class="W"></path><path d="M681 448c0 1 0 1-1 2v2l11-8-7 6-2 2 2 2 1-1c1 1 1 0 1 1v1l-5 5c-1 1-2 2-4 2v-2c1 0 1 0 1-1l-2-2h0-1-1c0-1 0-1 1-2l3-3c1-2 2-3 3-4z" class="b"></path><path d="M682 452l2 2 1-1c1 1 1 0 1 1v1l-5 5v-3-1l-1-1c0-1 1-2 2-3z" class="O"></path><path d="M703 430c1 0 2-1 3-2h1 0c0 3-1 7-4 9-3 1-6 4-9 5l-1 1c-1 0-1 0-2 1l-11 8v-2c1-1 1-1 1-2 3-5 9-8 13-11h0c2-1 8-5 9-7z" class="S"></path><path d="M705 407c1-1 3-1 4-1 1 6 0 12-1 17 0 1 0 5-1 5h0-1c-1 1-2 2-3 2-1 2-7 6-9 7h0c-2 0-3 1-5 2-4 2-8 7-11 10l-5 5c-1 1-2 2-3 2l-1 1-1-1c1-2 3-4 3-6 1 0 2-1 2-2 3-3 5-6 8-9l8-8c1-1 3-2 3-4 0-1 5-6 6-8 3-3 6-8 7-12z" class="F"></path><path d="M699 431v-1c3-2 5-6 9-7 0 1 0 5-1 5h0-1c-1 1-2 2-3 2h-1l-1 1c-1 1-2 1-4 2l2-2z" class="U"></path><path d="M681 439l1 1 13-10c4-3 8-7 12-10h0l-10 9 2 2-2 2c2-1 3-1 4-2l1-1h1c-1 2-7 6-9 7h0c-2 0-3 1-5 2-4 2-8 7-11 10l-5 5c-1 1-2 2-3 2l-1 1-1-1c1-2 3-4 3-6 1 0 2-1 2-2 3-3 5-6 8-9z" class="a"></path><path d="M689 436l8-7 2 2-2 2-6 3h-2z" class="c"></path><path d="M697 433c2-1 3-1 4-2l1-1h1c-1 2-7 6-9 7h0c-2 0-3 1-5 2-4 2-8 7-11 10 0-3 8-10 11-13h2l6-3z" class="O"></path><path d="M699 348c3-1 8 1 11 2 8 3 14 10 18 18l2 6c1 7 2 12 0 19-1 4-3 9-4 13-7 18-17 37-32 49-20 16-44 20-68 20 0-1 0-2-1-3h-1-5c-1 0-1-2-2-2h0l-1 1v1c-2-1-2-2-4-3 0-1 1-4 1-5v-1c1-3 3-4 5-6l1-2c1-2 3-3 5-4 5-3 13-3 19 0 3 1 4 2 7 3h1c2 3 3 7 6 10h1l2-1c2 0 4-1 6-1 4-1 6-3 10-1v1c0 1 0 0 1 1v-1c2 0 3-1 4-2l5-5v-1c0-1 0 0-1-1l-1 1-2-2 2-2 7-6c1-1 1-1 2-1l1-1c3-1 6-4 9-5 3-2 4-6 4-9 1 0 1-4 1-5 1-5 2-11 1-17-1 0-3 0-4 1-4 0-9-2-13-4-3-1-5-3-6-7v-1l1-3h0 0c0 1 1 2 1 2l4-1c0-2 2-3 3-5l3-3v-3c1-4-2-7-5-9s-7-3-10-4h0v-1h-11l1-2h0l1-1c2-1 3-1 5-2-2 0-5 1-7 0l24-12 1-1c1 0 1-1 2-2z" class="P"></path><path d="M722 380h1 0c3-1 5-4 7-6 1 7 2 12 0 19v-2-2-1c1-3 0-5 1-7l-1-1v-1l-1-1v1c0 1 0 2-1 3s0 4-1 6l-1 3c0-4-2-7-4-11z" class="G"></path><path d="M660 463c2 0 4-1 6-1 4-1 6-3 10-1v1c0 1 0 0 1 1-1 0-2 1-3 1h0c-1-1-2-1-3-1s-3 1-4 2c-3 0-7 1-9 2-1 0 0 0-1 1s-5 1-7 1c-3 1-5 1-7 1v-1h2c1-1 1-1 3-2 0-1 0-1 1-2l2-1v1c2 1 4 0 5 1h1l1-2 2-1z" class="D"></path><path d="M691 444c1-1 1-1 2-1l1-1c3-1 6-4 9-5l1 1c-1 5-7 9-11 12l-7 5v-1c0-1 0 0-1-1l-1 1-2-2 2-2 7-6z" class="F"></path><path d="M684 450c0 1 0 1 1 3 2-1 4-3 6-5l2 2-7 5v-1c0-1 0 0-1-1l-1 1-2-2 2-2z" class="U"></path><defs><linearGradient id="Bb" x1="634.325" y1="469.971" x2="632.569" y2="452.543" xlink:href="#B"><stop offset="0" stop-color="#44484c"></stop><stop offset="1" stop-color="#636566"></stop></linearGradient></defs><path fill="url(#Bb)" d="M618 457l1-2c1-2 3-3 5-4 5-3 13-3 19 0 3 1 4 2 7 3h1c2 3 3 7 6 10h1l-1 2h-1c-1-1-3 0-5-1v-1l-2 1c-1 1-1 1-1 2-2 1-2 1-3 2h-2v1c-3 1-7 1-9 0s-5-3-5-6c-1-2 0-3 2-5 1-1 3-2 4-3 1 0 1 0 1-1-4 1-7 2-10 6-2 3-2 7-1 11h-1-5c-1 0-1-2-2-2h0l-1 1v1c-2-1-2-2-4-3 0-1 1-4 1-5v-1c1-3 3-4 5-6z"></path><defs><linearGradient id="Bc" x1="617.076" y1="470.78" x2="625.079" y2="449.465" xlink:href="#B"><stop offset="0" stop-color="#34363a"></stop><stop offset="1" stop-color="#4d5154"></stop></linearGradient></defs><path fill="url(#Bc)" d="M618 457c1-1 2-3 4-4 2 0 6-2 8-2h1c1 0 2 1 3 1-6 1-10 3-14 8-2 2-2 5-3 8v2h0l-1 1v1c-2-1-2-2-4-3 0-1 1-4 1-5v-1c1-3 3-4 5-6z"></path><path d="M618 457l1-2c1-2 3-3 5-4 5-3 13-3 19 0 3 1 4 2 7 3h1c2 3 3 7 6 10h1l-1 2h-1c-1-1-3 0-5-1v-1c-1-1-1-1-2-1v-1c-1-1-1 0-2-1l1-1h1v-1s0-1-1-1c-3-5-9-5-14-6-1 0-2-1-3-1h-1c-2 0-6 2-8 2-2 1-3 3-4 4z" class="R"></path><defs><linearGradient id="Bd" x1="714.342" y1="378.307" x2="705.679" y2="394.899" xlink:href="#B"><stop offset="0" stop-color="#27282a"></stop><stop offset="1" stop-color="#494d51"></stop></linearGradient></defs><path fill="url(#Bd)" d="M695 372h1c4 1 8 3 13 4 2 0 2 0 4 1h1 0 2c2 1 3 1 3 2l1 1c3 3 5 7 5 11 0 1-1 2-1 2-1 4-2 7-4 9-5 1-10 0-14 0 1 1 1 1 2 1h-1l1 1 1 2c-1 0-3 0-4 1-4 0-9-2-13-4-3-1-5-3-6-7v-1l1-3h0 0c0 1 1 2 1 2l4-1c0-2 2-3 3-5l3-3v-3c1-4-2-7-5-9 1 0 2 1 3 1v1l-1-3z"></path><path d="M708 383c1-1 1-2 2-3v-1l1-1 1 1-1 1c0 2-1 4-1 6h-1c0-1 0-2-1-3z" class="G"></path><path d="M712 379l1-1h1c2 0 3 1 5 1h0l1 1h-4l-1 1c0 2 0 2-1 4l-1-1c0-2 0-3-1-5z" class="K"></path><path d="M708 380h0c1-1 1-2 2-3h3 1 0 2c2 1 3 1 3 2h0c-2 0-3-1-5-1h-1l-1 1-1-1-1 1v1c-1 1-1 2-2 3v-3z" class="I"></path><path d="M696 372c4 1 8 3 13 4 2 0 2 0 4 1h-3c-1 1-1 2-2 3h0l-2-3c-2 0-2-1-4-1h-3-1c0-2-1-3-2-4z" class="Z"></path><defs><linearGradient id="Be" x1="698.163" y1="377.016" x2="696.775" y2="391.318" xlink:href="#B"><stop offset="0" stop-color="#383c3f"></stop><stop offset="1" stop-color="#525558"></stop></linearGradient></defs><path fill="url(#Be)" d="M695 372h1c1 1 2 2 2 4 2 2 3 4 3 6v6h0l-1 2-1 1-7 2c0-2 2-3 3-5l3-3v-3c1-4-2-7-5-9 1 0 2 1 3 1v1l-1-3z"></path><defs><linearGradient id="Bf" x1="697.266" y1="404.8" x2="697.618" y2="396.448" xlink:href="#B"><stop offset="0" stop-color="#0d0d0e"></stop><stop offset="1" stop-color="#2c2c2c"></stop></linearGradient></defs><path fill="url(#Bf)" d="M699 391c0 1 1 1 1 2 2 1 4 2 6 2h0c2 0 7 1 9 2v-1h2 0l1 1h1 0l-1 1h1l1-1c1-2 2-2 4-4-1 4-2 7-4 9-5 1-10 0-14 0 1 1 1 1 2 1h-1l1 1 1 2c-1 0-3 0-4 1-4 0-9-2-13-4-3-1-5-3-6-7v-1l1-3h0 0c0 1 1 2 1 2l4-1 7-2z"></path><path d="M699 391c0 1 1 1 1 2 2 1 4 2 6 2h0c2 0 7 1 9 2l4 2c1 0 2-1 2-2l1 1c-1 1-2 2-3 2-2 1-4 1-6 1-5 0-9-1-14-2-2 0-4-1-6-1-2-1-5-2-7-3l1-3h0 0c0 1 1 2 1 2l4-1 7-2z" class="C"></path><path d="M694 395c4 1 8 2 11 3-4 0-7 0-11-1v-1-1z" class="Z"></path><path d="M687 392h0 0c0 1 1 2 1 2 3 1 4 1 6 1v1 1l-1 1c-2-1-5-2-7-3l1-3z" class="P"></path><defs><linearGradient id="Bg" x1="691.192" y1="374.005" x2="721.648" y2="358.054" xlink:href="#B"><stop offset="0" stop-color="#383b3f"></stop><stop offset="1" stop-color="#54585a"></stop></linearGradient></defs><path fill="url(#Bg)" d="M699 348c3-1 8 1 11 2 8 3 14 10 18 18l2 6c-2 2-4 5-7 6h0-1c-4-5-11-7-17-9-4-1-8-1-12-1h0l2 2 1 3v-1c-1 0-2-1-3-1-3-2-7-3-10-4h0v-1h-11l1-2h0l1-1c2-1 3-1 5-2-2 0-5 1-7 0l24-12 1-1c1 0 1-1 2-2z"></path><path d="M697 350h3c0 1-1 2-1 3l-1 1h1c-2 2-4 5-6 6l1 1h0c2 1 3 1 4 1-2 1-7 1-10 1l4-1c-1-1-2-1-3-1v-1c3-1 6-5 8-7l1-1c-1-1-1-1-2-1l1-1z" class="K"></path><path d="M698 362c4 0 8 0 12 2-2 0-5-1-7-1h-8v1h3l1 1c-5 1-11 1-16 1-4 0-7 1-10 0h0l1-1 14-2c3 0 8 0 10-1z" class="B"></path><path d="M673 366c3 1 6 0 10 0 3 1 6 1 9 2 2 0 4-1 6 0l-6 1h6 1l1 1h2 1 1l1 1c-4-1-8-1-12-1h0l2 2 1 3v-1c-1 0-2-1-3-1-3-2-7-3-10-4h0v-1h-11l1-2z" class="H"></path><path d="M696 351c1 0 1 0 2 1l-1 1c-2 2-5 6-8 7v1c1 0 2 0 3 1l-4 1-14 2c2-1 3-1 5-2-2 0-5 1-7 0l24-12z" class="C"></path><path d="M679 363c3-1 6-2 9-4s6-5 9-6c-2 2-5 6-8 7v1c1 0 2 0 3 1l-4 1-14 2c2-1 3-1 5-2z" class="I"></path><path d="M291 337l6 1c3-23 13-47 32-61 8-7 19-11 29-14 16-4 32-7 48-4 17 2 33 11 43 25 6 8 11 19 12 30 1 9 1 18 1 28v36l1 141v162 43c0 10 0 20-1 30-3 18-11 33-26 44-14 9-32 16-49 16-4-1-7-1-11-1-1-1-3-2-4-3-2-2-4-3-5-5h0c-2-3-4-5-5-8s-2-5-3-7v-3c1 1 1 3 2 4 4 8 9 15 18 18s20 0 29-3v-1h-1c-3 1-8 2-11 2l-1-1h3v-1l11-3c5-2 9-6 11-10l-1-1c0-1 1-2 1-3 2-2 0-5 2-8v-1h-1l1-1 2-1 1 1v-1l-2-1c0-1 0-2-1-3h-1l2-3h0v-1c3-2 4-4 5-7 0-4 1-7 0-11l2-5-1-1 1-3 1-5c0-1 0-3 1-4v-3c-1 0-1 0-2-1v1l-2 2c0 1 0 1-1 1h-1l2-8h-1 0v-1-3l1-5-1-2v-1-2-1 1l-1-2c-2 1-4 3-5 4 0-1 1-3 2-4-1-2-1-2-1-3 1-1 2-2 2-3v-1-2l2-4c0-1 1-3 2-4v-2h1c0-1 0-2-1-2h0l-1 1-3 3-2 2v1h-1v-1c1-1 1-1 1-2 1 0 1-1 1-1l1-1 1-1v-1c8-11 16-22 22-35v-1l1-2h-2v-1h0l-4 3h-1s2-4 3-5h-2c0-1 2-2 2-2 2-1 4-2 5-4 0-1 3-4 4-5h0c0-1 1-2 0-3l-1 1-1-1c2-6 2-13 3-19 0-8 0-16-1-24h0c-1-12-6-20-14-29-1 0-2-1-4-2-3-3-5-4-9-6l-3-2-5-2c-2-1-3-2-4-2-1-1-2-1-2-2h2 1c-1 0-2-1-3-1-2-1-3-2-5-2l-7-2c0-1 0 0 1-1h0v-3-6-1h0l-1-1h0c2 0 3 1 5 0h1l2 1-5-9c-1-1-2-2-2-4-2-2-2-6-2-9 0-4 2-8 5-11 1-1 3-2 5-1 3 1 4 4 5 7 1-1 1-2 1-3 1-4 2-7 5-10 3-2 9-6 13-6 2-5 3-9 3-14 0-7-2-11-5-17-8-9-16-13-29-12h-11c-2-1-2-1-3-3 0-3 0-4 2-7 2 1 3 1 5 1 2-1 4-1 6-1-1 1-1 0 0 1h5c3 1 10 1 12 3h3c1 0 3 1 4 2l1-1v-1l-1-1c-2-2-4-4-7-5-1-1-4-1-5-3 0-1 0-1-1-1-4-4-15-4-20-4h0l-21 8 4-13-1-1c1-5 5-8 8-11-6 1-11 2-17 1h-1c-8-3-9-7-14-12v-1l-1 5h0c-2 4-5 7-9 9s-8 2-13 3l-9-6c-2-1-3-2-4-3l-2-1h-2-1c0 1-1 2-2 3-1 0-3 0-4 1-2-2-4-5-6-7-4-4-6-9-9-13 0-1-1-1-1-1l-2-2c-2-3-1-8 0-10l2-6 7-6h0c-2 0-3 1-4 1v-1c-2-2-6-2-9-2v-1l6-1h0 1l-2-1c2 0 2-2 4-2l-1-1c-3-3-4-8-5-12h7z" class="C"></path><path d="M431 737h2 1l-4 9-1-1 1-3 1-5z" class="W"></path><path d="M439 382c2 4 4 6 6 9h-1c-2-1-3-1-4-2l-1-7zm-4 362c1 0 1 1 1 2l-1 12v3h-1v-4l1-13z" class="D"></path><path d="M438 405c1 0 2 1 4 3h0c2 1 3 3 3 5 1 2 3 4 3 6l-5-6 1-1c-4-1-5-4-6-7z" class="J"></path><path d="M449 641c0-1 3-4 4-5 0 3-2 11-3 12h-1-1v-4c1-1 1-1 1-2v-1zm-28-207c1 0 3 1 4 2 2 1 3 3 5 4h0v2h0c-3-1-4-3-6-4s-4-2-6-4h3z" class="G"></path><path d="M392 334c1 1 1 2 2 2v2c-2 2-5 1-7 2h-1c-1 1-3 2-4 3h-1l1-1v-1c1-1 2-1 3-2s4-2 5-3l2-2z" class="D"></path><path d="M433 716c2-2 0-5 2-7 1 2 0 6 1 9h-1l-1-1c0 4-2 8-2 13-1 0-1 0-2-1 1-2 2-6 2-8s0-4 1-5z" class="I"></path><path d="M428 762l1 12h-1c-3 0-4-2-5-4h0v-1c3-2 4-4 5-7z" class="P"></path><path d="M449 641v1c0 1 0 1-1 2v4h1 1c-1 2-2 4-3 5v-1l1-2h-2v-1h0l-4 3h-1s2-4 3-5h-2c0-1 2-2 2-2 2-1 4-2 5-4z" class="B"></path><path d="M425 535h0c1 1 2 1 2 2l2 2h1c1 1 0 1 1 1 2 1 3 3 4 4h1c2 1 7 4 9 4-2 2-3 2-6 2-3-5-8-8-12-12l-2-3z" class="Z"></path><path d="M427 538c4 4 9 7 12 12 2 2 4 4 5 6v1l-19-19h2z" class="J"></path><path d="M372 336l5 1c2 0 4-1 7-1h6c-1 1-4 2-5 3-2 0-4 0-6 1h0l-1-1h-5-1v-3z" class="G"></path><path d="M437 434h1c2 6 1 14-1 19h0 0c-1-6-2-13 0-19z" class="H"></path><path d="M422 525h4c0 1 1 1 2 1 0 2 0 3 1 4s2 3 3 5c1 0 2 1 3 2l-1 1c-4-2-8-6-10-9l-2-4z" class="B"></path><path d="M432 535c-1-1-2-1-3-2s-2-2-2-4c1 1 0 1 1 1h1c1 1 2 3 3 5z" class="K"></path><path d="M448 745l2 7c0 3 0 6-1 10-1 3-2 5-2 9 1 1 0 1 0 3h-1c0-1 0-2-1-3 0-3 1-6 2-8v-4c1-4 1-10 1-14zm-7-365c2 1 4 6 7 7 0 1 0 1 1 2h1 0l1-1v1 3c1 1 2 2 3 2l-1-3v-1c-1-1-1-1-1-2l1 2c1 1 0-1 1 1l1 1v1c1 0 1 0 1 1l1 1v3h0s-1 0-2-1v3 1h0l1 1v1l1 1h0c-3-2-5-7-7-10-3-4-7-9-9-14zm-21-36v-1-1c2-1 2 1 3 0 1 0 1 0 2 1h1-2l-1 1 1 1h0l1 1 1-1h1l2 1c3 0 6 1 8 2-1 1-2 1-4 1h0c-3 0-6 2-9 2v-1c2 0 2-1 4-1h2c-2 0-4 0-6-1-2 0-2 0-2-1l-1 1h-1c1-2 0-3 0-4z" class="B"></path><path d="M429 346c3 0 6 1 8 2-1 1-2 1-4 1h0c-2-1-7-1-9-1 1-1 0-1 1-1 2 0 3 0 4-1h0z" class="M"></path><path d="M423 770c1 2 2 4 5 4h1c1 5 2 9-1 14 0-1 0-3-1-4 0-2-3-4-4-5l1-2 1 1v-1l-2-1c0-1 0-2-1-3h-1l2-3zm13-301c1 2 1 2 1 5l-1 1v2 2l-1 3c-1 0-1 0 0 1h4v-1h0c1 0 1-1 2-1h1l2-2 1-1 3-2h0l-2 3h0l8-7c-2 3-4 8-6 10h-4l-1-1-3 2 1 1c1 0 1 0 2 1-3-1-8-1-10-2 2-5 3-9 3-14z" class="I"></path><path d="M444 741v-1h1 0l1-1c1 1 1 2 2 4v2c0 4 0 10-1 14v4l-1-1c-1-3-1-5-1-7-1-3 0-6-1-8v-6z" class="W"></path><path d="M444 741v-1h1 0l1-1c1 1 1 2 2 4v2c0 4 0 10-1 14v-9c0-2 0-3-1-4v-2l-1-2-1-1z" class="D"></path><path d="M434 757v4h1v-3c0 2 0 5 1 7v3-2h1l-1 10c-1 3-1 6-1 8l-1-1-3 10c0-1 0-1-1-1 1-3 2-6 2-8 2-9 0-18 2-27z" class="B"></path><path d="M436 765v3-2h1l-1 10c-1 3-1 6-1 8l-1-1c-1-1 0-4 0-5 1-5 1-9 2-13z" class="P"></path><path d="M311 330c1-3 3-7 5-9 0 2-1 3-2 5l-1 2c-1 1-1 2-1 3l1 1v1h0c0 1 0 2 1 3v1h0l3 1 1 1c1-2 2-3 3-5l1-1h0v-2-1l2-1h-1 0-1v-1-3h0c0-2 0-2 1-3v4h1v-3h0l1 1-1 1c0 1 0 4 1 5v1c0 1 1 2 2 4 0 1 1 1 1 2h1v1 1c1 1 2 1 3 1 1-1 1-2 1-3l1-1 1-2h0c0 2-1 4-2 6-1 1-1 1-2 1-3-2-6-7-8-10l-1 4c-2 3-3 5-7 7-2-1-3-9-4-11v-1z" class="B"></path><defs><linearGradient id="Bh" x1="440.658" y1="722.387" x2="429.025" y2="731.839" xlink:href="#B"><stop offset="0" stop-color="#1c1d1f"></stop><stop offset="1" stop-color="#3d3f41"></stop></linearGradient></defs><path fill="url(#Bh)" d="M432 730c0-5 2-9 2-13l1 1h1v3h0v-1l2-2 1 1c0 1 0 3-1 4-1 4-2 7-3 10 0 1-1 4-1 4h-1-2c0-1 0-3 1-4v-3z"></path><path d="M440 703h0c4-3 7-3 11-2l3 2h0l-1 1c-2 0-3-1-4 1 0 3 1 6 1 9-3-4-7-7-10-11z" class="d"></path><defs><linearGradient id="Bi" x1="423.554" y1="713.935" x2="434.976" y2="724.092" xlink:href="#B"><stop offset="0" stop-color="#1b1a1a"></stop><stop offset="1" stop-color="#313538"></stop></linearGradient></defs><path fill="url(#Bi)" d="M428 725v-4c1-4 1-13 4-16 2 4 1 7 1 11-1 1-1 3-1 5s-1 6-2 8v1l-2 2c0 1 0 1-1 1h-1l2-8z"></path><path d="M442 767c0-4 0-10 1-13l1 1h1c0 2 0 4 1 7l1 1c-1 2-2 5-2 8l-3 9v-1c-1 1-1 2-1 3l-1-1v-4c1-3 2-7 2-10h0z" class="D"></path><path d="M442 767c0-4 0-10 1-13l1 1-2 24c-1 1-1 2-1 3l-1-1v-4c1-3 2-7 2-10h0z" class="M"></path><path d="M450 543c1-4 2-7 2-10 1 4 2 8 2 12 2 8 4 16 3 24-1-2-1-4-1-6 0-1-2-4-2-5-2-5-4-10-4-15z" class="B"></path><path d="M409 359c1-2 2-5 4-7h2 2l1-1h2 4l-1 1c-1 0-2 0-3 1h-1-2c-1 1-3 1-4 1s-1 0 0 1h0c1-1 2-1 4-1h0 1 1 4c4 0 9-3 13-2-1 1-2 1-4 2-5 2-10 3-15 4l-4 1h-4z" class="K"></path><path d="M443 720v-6l1-1c2 3 0 9 2 12v2c1 4 4 6 3 11 2 2 1 5 1 8v6l-2-7v-2c-1-2-1-3-2-4l-1 1h0c0-2-1-3-1-5 1-1 0-8 0-9-1 0-1 3-1 4v-10z" class="M"></path><path d="M444 735c1 2 1 2 3 3s2 3 2 5l1 3v6l-2-7v-2c-1-2-1-3-2-4l-1 1h0c0-2-1-3-1-5z" class="K"></path><path d="M421 539c4 6 9 11 14 17 2 2 3 3 4 5-1 0-2-1-4-2-3-3-5-4-9-6l-3-2-5-2c-2-1-3-2-4-2-1-1-2-1-2-2h2c1 1 3 2 4 2l1 1 1-1 1 1 1-1 1-1c-2-1-1 0-2-1l1-1h0c0-1-1-2-1-3v-2z" class="I"></path><path d="M423 551v-1c2 1 2 1 3 1 2 1 3 2 5 3 1 0 1 0 2 1 1 0 1 1 2 1 2 2 3 3 4 5-1 0-2-1-4-2-3-3-5-4-9-6l-3-2z" class="M"></path><path d="M431 415c-1-2-3-5-4-7 2-1 3 0 5 0 4 3 7 7 7 13 1 2 1 6 0 8h0c-2-5-5-9-8-14h0z" class="d"></path><path d="M410 318l-2-2c0-1 1-3 3-3 1-1 5 0 6 1 8 3 15 9 18 17v3c-2 0-3 0-4-1 1-1 1-2 2-3-1-2-1-2-2-3h0c-5-6-13-10-20-10-1 0-1-1-2-1v1l1 1z" class="M"></path><path d="M436 776l1 1v-4c0 1 0 3 1 4l2-17v6h1 0l1 1h0c0 3-1 7-2 10v4l1 1c0-1 0-2 1-3v1l-9 16 2-12c0-2 0-5 1-8z" class="G"></path><path d="M436 746l1-1c0-1 0-2 1-3 1 1 0 3 0 4 1-1 1-2 2-2v7 9l-2 17c-1-1-1-3-1-4v4l-1-1 1-10h-1v2-3c-1-2-1-5-1-7l1-12z" class="D"></path><path d="M436 746l1-1c0-1 0-2 1-3 1 1 0 3 0 4 0 6 0 13-1 20h-1v2-3c-1-2-1-5-1-7l1-12z" class="K"></path><defs><linearGradient id="Bj" x1="431.533" y1="702.206" x2="420.46" y2="706.726" xlink:href="#B"><stop offset="0" stop-color="#141516"></stop><stop offset="1" stop-color="#323639"></stop></linearGradient></defs><path fill="url(#Bj)" d="M427 696l2-2v-2l1 1v3h1 0c0 3 0 7-1 10s-1 6-2 10l-1-2v-1-2-1 1l-1-2c-2 1-4 3-5 4 0-1 1-3 2-4-1-2-1-2-1-3 1-1 2-2 2-3v-1-2l2-4h1z"></path><path d="M426 696h1c0 2-1 4-3 6v-2l2-4zm-7 95c0-1 1-2 1-3 2-2 0-5 2-8v-1h-1l1-1 2-1-1 2c1 1 4 3 4 5 1 1 1 3 1 4-2 4-5 8-9 10-4 4-11 6-17 7l-4 1v-1l11-3c5-2 9-6 11-10l-1-1z" class="G"></path><defs><linearGradient id="Bk" x1="433.474" y1="348.981" x2="407.526" y2="338.019" xlink:href="#B"><stop offset="0" stop-color="#191819"></stop><stop offset="1" stop-color="#42464a"></stop></linearGradient></defs><path fill="url(#Bk)" d="M411 347c-1-1-1-1-2-1h-3c-1-1 0-1-1-1 3-3 7-3 9-6h-1c1 0 3-1 4-1 3 1 6 1 9 2h1 2l1 1c3 0 4-1 6 1l1 1-1 1 1 1c-1 1-3 1-5 0h-1-4-1l-1 1-1-1h0l-1-1 1-1h2-1c-1-1-1-1-2-1-1 1-1-1-3 0v1 1c-2-1-2-1-4-1l-5 4z"></path><path d="M454 703c1 2 1 3 1 5l1 3 4 26-10-23c0-3-1-6-1-9 1-2 2-1 4-1l1-1z" class="W"></path><defs><linearGradient id="Bl" x1="427.939" y1="322.842" x2="432.739" y2="312.147" xlink:href="#B"><stop offset="0" stop-color="#26292a"></stop><stop offset="1" stop-color="#4a4f53"></stop></linearGradient></defs><path fill="url(#Bl)" d="M414 305c3 0 5 1 8 1 9 4 19 13 23 23 3 4 4 9 5 15v1l-2-7c-1-3-3-6-5-9-8-12-17-17-30-23l1-1z"></path><path d="M426 358c2 0 4-1 6-2l5-2c-6 4-12 7-20 9-2 0-4 0-6 1h-2-2-1c1 1 2 1 3 1h1l1 1c3 0 7 0 10-1h1c-1 1-2 1-3 1v1c2 1 2 1 4 1 0 2 0 3-1 4-5-3-11-6-17-7-1-1-3 0-4 0l-1-1c1 0 3 0 4-1 2 0 4-2 5-4h4l4-1c1 1 1 1 2 1l1-1h6z" class="M"></path><path d="M413 359l4-1c1 1 1 1 2 1l1-1h6c-2 1-4 1-5 2h-1l-1 1h-2-1c-1 1-3 1-5 1l1-1c2 0 2 0 4-1h-2l-1-1z" class="P"></path><path d="M414 503h1l1 1c-1 0-1 0-1 1h0 1 0l1 1c1-2 0-2 2-4l1 1c-1 4-1 7-2 11v-1-2l-1 1h0c-1 3 1 7 1 10 1 4 4 9 7 13l2 3h-2c-7-6-12-17-13-27l2-8z" class="B"></path><defs><linearGradient id="Bm" x1="423.856" y1="319.68" x2="414.83" y2="329.207" xlink:href="#B"><stop offset="0" stop-color="#1e1f22"></stop><stop offset="1" stop-color="#3a3e41"></stop></linearGradient></defs><path fill="url(#Bm)" d="M410 318l-1-1v-1c1 0 1 1 2 1 7 0 15 4 20 10h0c1 1 1 1 2 3-1 1-1 2-2 3-2-1-4-1-6-2h0l-1-2h-1 0c-3-1-5-2-8-2h-2-1c1-2 1-2 1-4 0-1-2-3-3-5z"></path><defs><linearGradient id="Bn" x1="458.963" y1="522.108" x2="447.099" y2="520.543" xlink:href="#B"><stop offset="0" stop-color="#1e1f21"></stop><stop offset="1" stop-color="#3d4043"></stop></linearGradient></defs><path fill="url(#Bn)" d="M449 514l5-8h1v2l1-2c1 14 1 29 4 42l1 1c-2 2-2 3-2 5 0-3 0-6-1-8-1-8-4-17-8-23 0-2-1-3-2-4 0-1 0-3 1-5z"></path><path d="M450 543c0 5 2 10 4 15 0 1 2 4 2 5-2 2-1 7-1 10v4h0v-1l-2-4c-1-6-5-10-9-15v-1c-1-2-3-4-5-6 3 0 4 0 6-2l1-1c1 1 1 2 2 3 1-2 0-3 0-5 0 0 1-2 2-2z" class="B"></path><path d="M450 543c0 5 2 10 4 15 0 1 2 4 2 5-2 2-1 7-1 10l-7-23c1-2 0-3 0-5 0 0 1-2 2-2z" class="Z"></path><path d="M425 433l1-1c-1-4-4-7-7-9-2-3-6-3-8-5 2-1 4-1 6-2h0c1 1 2 1 2 1 5 0 8 3 11 6 2 2 4 5 4 8h0c-1-1-1-2-2-2-1-2-1-2-3-3-1-1-3-2-4-3h-1c-1 0-1-1-2 0h1c3 1 5 4 7 6 3 4 4 7 4 12 0 1-1 2-1 2h-1l-1-1c0-1 0-2-1-3v1c-2-1-3-3-5-4l1-1v-1l-1-1z" class="G"></path><path d="M426 434c2 2 3 4 4 5v1c-2-1-3-3-5-4l1-1v-1z" class="M"></path><defs><linearGradient id="Bo" x1="413.845" y1="431.362" x2="391.839" y2="442.572" xlink:href="#B"><stop offset="0" stop-color="#2d2e30"></stop><stop offset="1" stop-color="#4c4f52"></stop></linearGradient></defs><path fill="url(#Bo)" d="M390 430c2 1 3 1 5 1 2-1 4-1 6-1-1 1-1 0 0 1h5c-4 1-8 0-11 2h-1c2 1 4 1 6 1 3 0 7 0 10 1l5 1c1 0 1 0 2 1 1 0 2 1 4 1 2 1 5 3 7 5-2 0-6-3-8-3l-1-1-1 1c1 1 1 1 3 2s5 3 7 5c1 1 1 2 2 3h0c1 1 1 1 1 2-8-9-16-13-29-12h-11c-2-1-2-1-3-3 0-3 0-4 2-7z"></path><path d="M441 721c1-1 1-2 1-3h0l1 2v10c0-1 0-4 1-4 0 1 1 8 0 9 0 2 1 3 1 5h-1v1 6c1 2 0 5 1 8h-1l-1-1c-1 3-1 9-1 13l-1-1h0-1v-6-9-7c-1 0-1 1-2 2 0-1 1-3 0-4-1 1-1 2-1 3l-1 1c0-1 0-2-1-2l1-9-1-2c1-3 2-6 3-10l2 1c0-1 0-2 1-3z" class="B"></path><path d="M438 723l2 1c-1 2-1 4-2 6-1 1-1 3-2 5l-1-2c1-3 2-6 3-10z" class="I"></path><defs><linearGradient id="Bp" x1="447.315" y1="719.864" x2="435.185" y2="747.136" xlink:href="#B"><stop offset="0"></stop><stop offset="1" stop-color="#202526"></stop></linearGradient></defs><path fill="url(#Bp)" d="M441 721c1-1 1-2 1-3h0l1 2v10 9 2c-1 2-1 4-1 6v1c-1 1-1 0-1 1s-1 2-1 2v-7c-1-7 0-16 1-23z"></path><path d="M443 730c0-1 0-4 1-4 0 1 1 8 0 9 0 2 1 3 1 5h-1v1 6c1 2 0 5 1 8h-1l-1-1c-1 3-1 9-1 13l-1-1h0-1v-6-9s1-1 1-2 0 0 1-1v-1c0-2 0-4 1-6v-2-9z" class="B"></path><path d="M440 751s1-1 1-2 0 0 1-1l-1 18h0-1v-6-9z" class="M"></path><defs><linearGradient id="Bq" x1="444.453" y1="422.564" x2="452.915" y2="392.899" xlink:href="#B"><stop offset="0" stop-color="#0a0b0d"></stop><stop offset="1" stop-color="#53585b"></stop></linearGradient></defs><path fill="url(#Bq)" d="M440 389c1 1 2 1 4 2h1c8 9 13 22 13 33 0 1 0 2-1 3h0c-4-6-7-14-11-20 0 0-5-11-6-18z"></path><defs><linearGradient id="Br" x1="442.229" y1="655.904" x2="443.4" y2="684.215" xlink:href="#B"><stop offset="0" stop-color="#0a0909"></stop><stop offset="1" stop-color="#35393c"></stop></linearGradient></defs><path fill="url(#Br)" d="M432 683c2-5 7-11 10-16 4-4 7-9 11-13 0 4-1 7-2 11-2 7-4 13-3 21-1-2-1-2-1-4h-3c0 1 0 2-1 3v1l-2 2c0-2 0-3 1-5h-1l-2 2h0-1-2-1v1h-1c0-1 0-1 1-2v-1l-2-1-1 1z"></path><path d="M432 683l1-1 2 1v1c-1 1-1 1-1 2h1v-1h1 2 1 0l2-2h1c-1 2-1 3-1 5l2-2v-1c1-1 1-2 1-3h3c0 2 0 2 1 4 1 1 1 3 1 4 0 2 1 5 1 7l2 1c1 1 1 1 1 2v1h-2c-4-1-7-1-11 2h0 0c-3-4 0-10 1-14-3 3-5 6-6 10v1l-3-7c-1-3-1-6 0-10z" class="Q"></path><defs><linearGradient id="Bs" x1="462.578" y1="673.112" x2="443.381" y2="684.505" xlink:href="#B"><stop offset="0" stop-color="#020104"></stop><stop offset="1" stop-color="#383d3d"></stop></linearGradient></defs><path fill="url(#Bs)" d="M449 690c4-3 3-11 3-15s1-7 1-10c0-2 0-4 2-5v1c2 7 0 15 3 22 1 2 1 4 1 6s0 5 1 6v12h0c-1 1-1 1-1 2l-1-1h0c-2-1-1-3-3-4v1l1 1h0v2l1 1h-1v2l-1-3c0-2 0-3-1-5h0l-3-2h2v-1c0-1 0-1-1-2l-2-1c0-2-1-5-1-7z"></path><path d="M456 686v-1c1 0 1 0 1 1 1 1 1 2 2 3 0 2 0 5 1 6v12h0c-1 1-1 1-1 2l-1-1h0c-2-1-1-3-3-4v1l1 1h0v2l1 1h-1v2l-1-3c0-2 0-3-1-5h0v-6c-1-3 0-6 0-9 1 2 1 5 1 8v-3c1-2 1-5 1-7z" class="I"></path><path d="M455 696v-3c1-2 1-5 1-7 0 6 1 11 1 17h-1c-1-2-1-5-1-7z" class="B"></path><defs><linearGradient id="Bt" x1="417.443" y1="522.558" x2="400.249" y2="499.551" xlink:href="#B"><stop offset="0" stop-color="#121314"></stop><stop offset="1" stop-color="#2e2f31"></stop></linearGradient></defs><path fill="url(#Bt)" d="M401 516c-2-2-2-6-2-9 0-4 2-8 5-11 1-1 3-2 5-1 3 1 4 4 5 7v1l-2 8c0-1-1-4-2-5h-1c-2 7 0 13 3 19l3 6c-4-3-8-7-10-11v-1 1h-1c-1-2-2-3-3-4z"></path><defs><linearGradient id="Bu" x1="411.454" y1="538.493" x2="407.55" y2="543.408" xlink:href="#B"><stop offset="0" stop-color="#141416"></stop><stop offset="1" stop-color="#3a3b3e"></stop></linearGradient></defs><path fill="url(#Bu)" d="M401 516c1 1 2 2 3 4h1v-1 1c2 4 6 8 10 11 2 2 3 6 6 8v2c0 1 1 2 1 3h0l-1 1c1 1 0 0 2 1l-1 1-1 1-1-1-1 1-1-1c-1 0-3-1-4-2h1c-1 0-2-1-3-1-2-1-3-2-5-2l-7-2c0-1 0 0 1-1h0v-3-6-1h0l-1-1h0c2 0 3 1 5 0h1l2 1-5-9c-1-1-2-2-2-4z"></path><defs><linearGradient id="Bv" x1="410.449" y1="540.306" x2="405.128" y2="527.875" xlink:href="#B"><stop offset="0" stop-color="#27272c"></stop><stop offset="1" stop-color="#3b3e3f"></stop></linearGradient></defs><path fill="url(#Bv)" d="M401 529h0l-1-1h0c2 0 3 1 5 0h1l2 1c1 1 1 2 2 3h0c0 1 0 2 1 3l7 8h0 0l-2-1c-1-1-3-2-4-3-4-3-7-6-11-9v-1z"></path><path d="M401 516c1 1 2 2 3 4h1v-1 1c2 4 6 8 10 11 2 2 3 6 6 8v2c0 1 1 2 1 3h0l-1 1c1 1 0 0 2 1l-1 1c-1-1-2-1-2-3l-2-1h0l-7-8c-1-1-1-2-1-3h0c-1-1-1-2-2-3l-5-9c-1-1-2-2-2-4z" class="G"></path><path d="M403 520c5 4 7 11 12 15 2 2 4 6 5 8v1l-2-1h0l-7-8c-1-1-1-2-1-3h0c-1-1-1-2-2-3l-5-9z" class="J"></path><defs><linearGradient id="Bw" x1="414.58" y1="307.29" x2="425.488" y2="286.702" xlink:href="#B"><stop offset="0" stop-color="#0b0d10"></stop><stop offset="1" stop-color="#35393b"></stop></linearGradient></defs><path fill="url(#Bw)" d="M374 275h0c16-2 31 1 45 7 17 9 31 24 37 42 1 2 2 7 1 9l-1-4c-5-12-10-21-19-30-14-14-34-21-53-23h-16c2-1 4-1 6-1z"></path><path d="M428 378c2-5 4-9 6-13 3-5 7-10 10-15v3c1 0 1 1 1 2 1 2 3 2 5 3h1c2 0 3 1 4 3 2 2 2 7 1 9 0 1-1 1-1 2h-1-1c-3 0-5-1-7-3l-2-2c-3 3-8 7-9 10l-1 2c1 2 2 3 2 5-1 2-2 3-3 4-2 0-2 0-4-1-1-2-1-4-1-6v-3z" class="L"></path><path d="M451 358c2 0 3 1 4 3 2 2 2 7 1 9 0 1-1 1-1 2h-1c-1-1-1-1-2-1s-2-1-3-1c0 0-1-1-2-1-1-2-2-3-3-5 1-3 3-4 5-5l1-1h1z" class="g"></path><defs><linearGradient id="Bx" x1="401.507" y1="278.315" x2="353.888" y2="332.94" xlink:href="#B"><stop offset="0" stop-color="#393d41"></stop><stop offset="1" stop-color="#5e5e5f"></stop></linearGradient></defs><path fill="url(#Bx)" d="M358 293c2 0 4-2 6-3 3-1 6-2 10-3 17-5 36-1 50 9 3 2 6 4 7 6h1c-14-9-33-15-50-12-10 1-21 5-27 14-5 6-9 15-7 22v1c0 2 1 4 1 7-4-5-5-13-5-19 1-10 6-16 14-22z"></path><path d="M431 515c4-1 6-2 10-1 2 0 3 0 5 1h1 0l1-1h1c-1 2-1 4-1 5 1 1 2 2 2 4v-1c-2 0-2-1-3-2-1 0-1 0-2 1l1 1c-1 0-1 1-1 1 0 1 0 1 1 1l-2 1c1 0 1 0 2 1 2 0 3 1 3 3 1 1 1 2 1 3 0 2 0 4-2 6s-5 3-7 3c-3 0-5-1-7-3l1-1c-1-1-2-2-3-2-1-2-2-4-3-5s-1-2-1-4v-6c1-2 2-3 3-5z" class="Q"></path><path d="M443 530h2c1 1 2 1 2 3-1 2-1 2-3 3h-1c-2 0-3-1-4-2l1-2c1-1 1-2 3-2z" class="M"></path><path d="M442 531h3v1 2h-1l-1-1v-1l-1 2-1-1c0-1 0-1 1-2z" class="K"></path><path d="M431 515c4-1 6-2 10-1 2 0 3 0 5 1h1 0l1-1h1c-1 2-1 4-1 5 1 1 2 2 2 4v-1c-2 0-2-1-3-2-1 0-1 0-2 1l1 1c-1 0-1 1-1 1 0 1 0 1 1 1l-2 1c1 0 1 0 2 1 2 0 3 1 3 3 1 1 1 2 1 3-1-3-2-5-5-6l-4-1c1-1 1-1 2-1v-1h-1v-1h2c-1-2-2-3-4-4h0c2 0 3 0 5 1v-1c-3-2-4-3-7-3-2 0-2 0-4 1h0c-2 1-4 3-4 5-1 1 0 2 0 4 1 3 2 6 4 8 0 1 0 1 1 2 0 1 2 3 4 4h-1c-1 0-2-1-3-2s-2-2-3-2c-1-2-2-4-3-5s-1-2-1-4v-6c1-2 2-3 3-5z" class="M"></path><path d="M441 514c2 0 3 0 5 1h1 0l1-1h1c-1 2-1 4-1 5-2-2-4-3-7-5z" class="J"></path><defs><linearGradient id="By" x1="369.336" y1="336.52" x2="364.412" y2="353.59" xlink:href="#B"><stop offset="0" stop-color="#2a2d2f"></stop><stop offset="1" stop-color="#4a4d50"></stop></linearGradient></defs><path fill="url(#By)" d="M348 327c1 1 2 1 2 2 0 2 1 3 1 4h6l-1-3c-1 0-1 0-2 1-1 0-1 0-1-1v-3h0l1 1v1h0l1-1h1 1c2 0 2 1 3 2 1 0 1 0 2 1 2 0 3 2 5 2l2 2c1 1 1 1 2 1l1-1v1h0v3h1 5l1 1h0c2-1 4-1 6-1-1 1-2 1-3 2v1l-1 1h1c-1 2-2 4-1 6s3 4 5 6c-1 0-2 0-3-1-2 1-2 1-3 2l-1 1h-1c0 1-1 2-1 3-2-1-5-2-7-3l-3-2h0c-2-2-4-4-7-6-5-3-9-10-11-15 0-3-1-5-1-7z"></path><path d="M356 330c1-1 1-1 2-1 1 1 3 2 3 4l-2 2c0 1 1 0 0 1-2-2-2-4-3-6z" class="M"></path><defs><linearGradient id="Bz" x1="361.918" y1="330.871" x2="362.983" y2="338.432" xlink:href="#B"><stop offset="0" stop-color="#121315"></stop><stop offset="1" stop-color="#292d2f"></stop></linearGradient></defs><path fill="url(#Bz)" d="M356 328h1c2 0 2 1 3 2 1 0 1 0 2 1 2 0 3 2 5 2l2 2c1 1 1 1 2 1 0 1 0 1-1 2-2 0-2-1-4-2v1 4h-1c0-2 0-3-1-4h0c0 2 0 3-2 4 0-3-1-5-1-8 0-2-2-3-3-4-1 0-1 0-2 1v-2z"></path><path d="M386 355l3 2h1c3 1 7 1 9 1l1-1c5-1 8-3 11-7v-1c-3-2-8-2-12-2 2-5 5-8 8-12 2-3 3-6 5-8h0 1 2c3 0 5 1 8 2h0 1l1 2h0-2c2 1 4 2 7 3 2 0 3 1 5 2h0c0 1 1 2 1 2l1 1-1 1h-6 0-4c-3-1-6-1-9-2-1 0-3 1-4 1h1c-2 3-6 3-9 6 1 0 0 0 1 1h3c1 0 1 0 2 1l5-4c2 0 2 0 4 1 0 1 1 2 0 4h1l1-1c0 1 0 1 2 1 2 1 4 1 6 1h-2c-2 0-2 1-4 1v1h-4-2l-1 1h-2-2c-2 2-3 5-4 7s-3 4-5 4c-1 1-3 1-4 1-7-1-15-4-21-7l1-1c1-1 1-1 3-2 1 1 2 1 3 1z" class="D"></path><path d="M415 327c3 0 5 1 8 2h0 1-10l-1-1 2-1z" class="C"></path><path d="M430 334c2 0 3 1 5 2h0c0 1 1 2 1 2l1 1-1 1h-6 0v-1h2 2l-1-1-6-3c-1 0-2 0-2-1 2 0 3 1 5 0z" class="I"></path><path d="M411 347l5-4c2 0 2 0 4 1 0 1 1 2 0 4l-2 1c-2 0-5 0-7-1v-1z" class="C"></path><defs><linearGradient id="CA" x1="444.397" y1="515.877" x2="420.643" y2="489.265" xlink:href="#B"><stop offset="0" stop-color="#3a3e41"></stop><stop offset="1" stop-color="#545759"></stop></linearGradient></defs><path fill="url(#CA)" d="M433 483c2 1 7 1 10 2 4 1 6 3 9 6 3 5 4 9 4 15l-1 2v-2h-1l-5 8h-1l-1 1h0-1c-2-1-3-1-5-1-4-1-6 0-10 1-1 2-2 3-3 5v6c-1 0-2 0-2-1h-4c-1-4-3-7-4-11 1-4 1-7 2-11l-1-1c-2 2-1 2-2 4l-1-1h0-1 0c0-1 0-1 1-1l-1-1h-1v-1c1-1 1-2 1-3 1-4 2-7 5-10 3-2 9-6 13-6z"></path><path d="M425 515l-1-2h0 4l1 2c-2 1-2 0-4 0z" class="R"></path><path d="M429 515h1 1c-1 2-2 3-3 5h0c-1-1-2-1-2-1v-2l-1-1c-1-1-1 0 0-1 2 0 2 1 4 0z" class="H"></path><path d="M419 502l2-4c1-3 3-4 6-5 1 1 2 1 4 1-3 1-7 2-9 4-1 2-2 3-2 5l-1-1z" class="M"></path><path d="M452 491c3 5 4 9 4 15l-1 2v-2h-1l-5 8h-1c2-2 3-4 4-6 2-4 2-8 1-12l-1-1v-4z" class="B"></path><path d="M427 493c3-1 5-1 8-1h0c4 1 8 3 10 5 1 2 2 4 2 6s-1 4-3 6c-2 1-5 2-7 1-4 0-7-1-10-5 0-1 0-2 1-3s3-2 5-2c1 0 2 0 3 1 1-2 1-4 1-6-2-1-4-1-6-1s-3 0-4-1z" class="P"></path><path d="M432 501v1 5 1-1l-1-1-1-1v1h0c-1-1-1-1-1-2 1-2 1-2 3-3z" class="J"></path><path d="M437 495c2 1 5 3 5 5 1 2 1 3 0 5-1 1-3 3-5 3h-3 0l1-1c1 0 2-1 2-3 0-1 0-2-1-3 1-2 1-4 1-6z" class="H"></path><defs><linearGradient id="CB" x1="385.524" y1="290.394" x2="376.347" y2="331.688" xlink:href="#B"><stop offset="0" stop-color="#313336"></stop><stop offset="1" stop-color="#515659"></stop></linearGradient></defs><path fill="url(#CB)" d="M392 334c-1 0-1-1-2-1-1-1-5 0-6 0-7 1-14-1-19-5-5-3-9-7-10-13-1-3-1-5 1-7 5-8 14-12 23-15 10-2 21 0 31 2l-1 1c-1-1-2-1-3-1-2-1-2 0-4 1l2 1c1 0 1 0 2 1s2 2 4 3v1h4c1-1 2 0 3 0-4 0-8 1-12 2l-3 4c0 1-1 2-1 3-1 2-2 4-2 6 1 1 2 3 3 4 1 2 1 4 0 6 0 1-1 4-3 5h-4c0 2 0 3-1 4-1 0-1-1-2-2z"></path><path d="M381 314l-1 1c-1-1-1-1-1-2v-2l1-2c0 2 0 3 1 5z" class="W"></path><path d="M380 309v-1l1-2s0-1 1-1v-1l1-1c1 0 1-1 2-2h1l-1 1c-2 2-3 3-3 5v3l1 1-1 1v2h-1c-1-2-1-3-1-5z" class="H"></path><path d="M388 302l1-1h0c2 0 2 0 3-1 2-1 4-1 6 0 2 0 3 1 4 3h0-2v-1c-2-2-4-1-7 0h-3l-1 1-1-1z" class="Y"></path><path d="M375 321c1 0 2 0 4 1 3 0 7 1 10 2-2 0-4 0-6 1 0 2 0 2 1 4h0l-2-1c-2-1-3-3-4-4-2-1-2-2-3-3z" class="Q"></path><path d="M388 302l1 1 1-1h3c3-1 5-2 7 0v1l-1 1c-2-1-6-1-8 0-1 0-3 1-4 3l-1 1c0-2 0-3-1-4 1-1 2-2 3-2z" class="W"></path><path d="M372 306v1l-2 3c-1 2-2 3-2 4-2 1-3 3-5 4l-1-1c-1-1-1-2-1-3 1-1 2-2 4-3l2 1 2-2c0-1 1-3 3-4z" class="B"></path><path d="M375 321l-3-6h0c-1-4 1-8 3-11l1-2v1 1c-2 3-3 8-2 12 0 2 1 3 2 4l12 3h0c4 0 7 0 11-1h1c-1 1-3 2-5 2h-2-4c-3-1-7-2-10-2-2-1-3-1-4-1z" class="G"></path><path d="M382 314v-2l1-1c1 3 3 4 6 4h2c1 0 2 0 3-1l7-3c-1 2-2 4-2 6 1 1 2 3 3 4 0 0-1 0-2 1h-1-4c2 0 4-1 6-1v-1c-3-5-5-1-8-1l1-1v-1h-3c-1 0-1-1-2-1h-3c-2 0-3-1-4-2z" class="R"></path><path d="M394 317c1-1 2-2 4-1v1c-1 0-3 1-4 1v-1z" class="H"></path><path d="M387 307c1-2 3-3 4-3 2-1 6-1 8 0l1-1h2l3 1-3 4h-3l-3 1h0c-3 0-6 0-9-2z" class="C"></path><path d="M400 303h2l3 1-3 4h-3v-4l1-1z" class="a"></path><path d="M385 302v2h0c1 1 1 2 1 4l1-1c3 2 6 2 9 2h0l3-1h3c0 1-1 2-1 3l-7 3c-1 1-2 1-3 1h-2c-3 0-5-1-6-4l-1-1v-3c0-2 1-3 3-5z" class="H"></path><path d="M386 308l1-1c3 2 6 2 9 2l1 1-1 1h-8c-2-1-2-1-2-3z" class="D"></path><path d="M385 302v2l-1 1c0 5 1 6 5 9h2v1h-2c-3 0-5-1-6-4l-1-1v-3c0-2 1-3 3-5z" class="G"></path><path d="M335 334c1-3 1-6 1-10 0-5 0-10 2-14 4-8 12-15 20-18v1c-8 6-13 12-14 22 0 6 1 14 5 19 2 5 6 12 11 15 3 2 5 4 7 6h0c-11-3-22-7-33-8v1c0 1 0 2 1 3h-1l-2-1-2-2h-4c-7 1-16 4-20 10-2 2-3 5-4 8-2 4-4 7-3 13 1 9 6 14 13 19h-2-1c0 1-1 2-2 3-1 0-3 0-4 1-2-2-4-5-6-7-4-4-6-9-9-13 0-1-1-1-1-1l-2-2c-2-3-1-8 0-10l2-6 7-6h0c-2 0-3 1-4 1v-1c-2-2-6-2-9-2v-1l6-1h0 1l-2-1c2 0 2-2 4-2h9l6 1c1-2 1-7 2-9 0-4 2-8 4-12v1c1 2 2 10 4 11 4-2 5-4 7-7l1-4c2 3 5 8 8 10 1 0 1 0 2-1 1-2 2-4 2-6z" class="d"></path><path d="M326 348h0l8-1v1c0 1 0 2 1 3h-1l-2-1-2-2h-4z" class="a"></path><path d="M290 350h9l-7 1c0 1 0 1 1 1l-6 2v-1h0 1l-2-1c2 0 2-2 4-2z" class="c"></path><path d="M293 352c1 1 1 2 2 3 0 1 0 2-1 2h0c-2 0-3 1-4 1v-1c-2-2-6-2-9-2v-1l6-1v1l6-2z" class="O"></path><path d="M392 373h2c9 2 17 5 26 9 3 1 6 3 9 5 2 1 2 1 4 1 1-1 2-2 3-4 0-2-1-3-2-5l1-2c0 1 0 2 1 2l3 3 1 7c1 7 6 18 6 18h-1-1c-1 0-2-2-3-1l1 1v1h0c-2-2-3-3-4-3 1 3 2 6 6 7l-1 1-4-3c-2-1-5-2-7-3v1c-2 0-3-1-5 0 1 2 3 5 4 7-2 0-2-1-2-2l-2-3c-1-1-1-1-1-2v-1h-3 1v2h1v1c1 1 0 2 0 3v1h-1l-7 2c-2 1-4 1-6 2 2 2 6 2 8 5 3 2 6 5 7 9l-1 1c-2-2-4-4-7-5-1-1-4-1-5-3 0-1 0-1-1-1-4-4-15-4-20-4h0l-21 8 4-13-1-1c1-5 5-8 8-11-6 1-11 2-17 1h-1c-8-3-9-7-14-12v-1c-1-2 3-6 5-8l3-2c1-1 2-2 3-4h1c4 0 8-2 12-3 6-1 12-1 18 0v-1z" class="R"></path><path d="M373 394h2l2 1c-2 1-5 0-7 0h0l3-1z" class="D"></path><path d="M431 402c3 0 4 1 7 3 1 3 2 6 6 7l-1 1-4-3c-1-3-4-4-6-6 0-1-1-2-2-2z" class="Q"></path><path d="M405 402l10-2h15l1 2c-6-2-12-1-17 0-5 0-9 1-13 3h-1c2-2 3-2 5-3h0z" class="D"></path><path d="M419 386c3 1 7 3 10 5l2 1h0c-6 1-11 1-16 2h-2c2-2 2-1 4-2h2c1-1 2 0 3-1-1-2-1-2-3-2v-1c-4-1-9 0-13-1l2-1c3-1 7 1 11 0z" class="J"></path><path d="M373 394v-1l15-4c2-1 5-2 8-2 4 0 8-1 12-1l-2 1c-7 2-13 4-18 9v-1h0-1-3c-1 0-2 1-3 1h-1c0-1 0-1 1-1 5-2 9-5 15-6v-1h-3l-9 3-9 3h-2z" class="B"></path><path d="M361 381l1-1h1c7-1 15-1 23 0-1 1-5 2-7 3-4 1-10 3-14 0-2 0-3-1-4-2z" class="d"></path><path d="M394 417c4-3 8-6 13-8 7-3 18-5 25-2v1c-2 0-3-1-5 0 1 2 3 5 4 7-2 0-2-1-2-2l-2-3c-1-1-1-1-1-2v-1h-3 1v2h1v1c1 1 0 2 0 3-1-2-1-3-2-4l-1-1-9 2c-4 1-7 3-11 4v1c-3 0-5 3-8 2z" class="P"></path><path d="M374 374c6-1 12-1 18 0 2 1 9 1 10 3h-4c-1-1-2-1-3 0 3 1 7 0 9 3l-7-1h-7c-1-1-2 0-4 1-8-1-16-1-23 0h-1l-1 1h-3c1-1 2-2 3-4h1c4 0 8-2 12-3z" class="J"></path><path d="M392 373h2c9 2 17 5 26 9 3 1 6 3 9 5 2 1 2 1 4 1 1-1 2-2 3-4 0-2-1-3-2-5l1-2c0 1 0 2 1 2l3 3 1 7c1 7 6 18 6 18h-1c-1-3-8-9-11-11v-1h3c-2-2-5-3-8-4-3-2-7-4-10-5-5-3-10-5-15-6-2-3-6-2-9-3 1-1 2-1 3 0h4c-1-2-8-2-10-3v-1z" class="P"></path><path d="M402 414c4-1 7-3 11-4l9-2 1 1c1 1 1 2 2 4v1h-1l-7 2c-2 1-4 1-6 2 2 2 6 2 8 5 3 2 6 5 7 9l-1 1c-2-2-4-4-7-5-1-1-4-1-5-3 0-1 0-1-1-1-4-4-15-4-20-4l-1-1c1-1 2-1 3-2 3 1 5-2 8-2v-1z" class="H"></path><path d="M402 414c4-1 7-3 11-4l9-2 1 1c1 1 1 2 2 4v1h-1v-1l-2-2c-3 2-8 2-12 3h0c0-1 0-1 1-1v-1h0c-2 0-5 2-8 2h-1z" class="D"></path><path d="M429 391c3 1 6 2 8 4h-3v1c3 2 10 8 11 11h-1c-1 0-2-2-3-1l1 1v1h0c-2-2-3-3-4-3-3-2-4-3-7-3h0l-1-2h-15l-10 2h-3c-1 1-2 2-4 2-1 1-2 2-4 3 0 1-1 1-2 2s-2 1-3 2-1 0-2 1-2 1-3 2h0l-1 1h-1c0-1 1-2 1-2l1-2h1v-1c4-6 13-13 20-15l8-1h2c5-1 10-1 16-2h0l-2-1z" class="Y"></path><path d="M429 391c3 1 6 2 8 4h-3v1c3 2 10 8 11 11h-1c-1 0-2-2-3-1l1 1v1h0c-2-2-3-3-4-3-3-2-4-3-7-3h0l-1-2 8 2c-3-2-6-5-8-6-1 0-3-1-4-1-4 0-9-1-13 0-11 1-20 6-27 15v1h-1v-1c4-6 13-13 20-15l8-1h2c5-1 10-1 16-2h0l-2-1z" class="G"></path><path d="M334 347c11 1 22 5 33 8l3 2c2 1 5 2 7 3 0-1 1-2 1-3h1c6 3 14 6 21 7l1 1c1 0 3-1 4 0 6 1 12 4 17 7 2 2 5 3 6 6v3c0 2 0 4 1 6-3-2-6-4-9-5-9-4-17-7-26-9h-2v1c-6-1-12-1-18 0-4 1-8 3-12 3h-1c-1 2-2 3-3 4l-3 2c-2 2-6 6-5 8l-1 5h0c-2 4-5 7-9 9s-8 2-13 3l-9-6c-2-1-3-2-4-3l-2-1c-7-5-12-10-13-19-1-6 1-9 3-13 1-3 2-6 4-8 4-6 13-9 20-10h4l2 2 2 1h1c-1-1-1-2-1-3v-1z" class="Y"></path><path d="M348 366c-1 1-2 1-3 1l-1-1h-7c-1 1-2 1-4 1h-12l6-1c1-1 2-1 4-1v1h1 3c1-1 3-1 5-1s5 0 8 1z" class="Q"></path><path d="M311 383h0l-2 1-1-1c2-1 3-3 5-4s3-2 5-1c2-1 3-2 5-1 1 0 1 0 1 1l-1 1v-1c0 1-1 1-1 1-2 0-2-1-3 1h0c0 2 0 3-1 5v-1l-1 1c-1-2-1-2-2-2h1v-3l-2 1h0c-1 1-2 2-3 2z" class="K"></path><path d="M306 381h1c7-9 21-10 31-11l-3 3c-2 1-5 1-8 1-2 0-5 0-7 1h0c-1 0-2 0-2 1h-1-1l-1 1c-3 0-5 3-7 5l-2-1z" class="C"></path><path d="M332 350l2 1h0v1c1 5 6 7 10 9 4 3 10 2 14 5h-10c-3-1-6-1-8-1h0 2 5c-2-2-4-2-5-2-7-1-13 0-20 1h0-1c6-2 12-3 17-3-1-1-3-2-4-3-2-2-3-4-6-5h0 0c2-1 2-2 4-3z" class="I"></path><defs><linearGradient id="CC" x1="326.717" y1="374.82" x2="328.81" y2="381.638" xlink:href="#B"><stop offset="0" stop-color="#141517"></stop><stop offset="1" stop-color="#303335"></stop></linearGradient></defs><path fill="url(#CC)" d="M306 381l2 1c2-2 4-5 7-5l1-1h1 1c0-1 1-1 2-1h0c2-1 5-1 7-1 3 0 6 0 8-1-2 3-3 6-4 9l-1 1-1 1v-1h-1v1c-1 1-2 0-2 1l-1 1-1-1 1-1c-2-2-2-3-2-5l1-1c0-1 0-1-1-1-2-1-3 0-5 1-2-1-3 0-5 1s-3 3-5 4l1 1 2-1h0v1l1 1h-2c0 2 0 2-1 3-1 0-1 1-2 0-1 0-2-1-2-2 0-2 0-3 1-5z"></path><path d="M343 391c1 1 3 1 4 2-2 1-3 3-5 4s-4 1-6 2c-5 2-10 3-15 3h-2-1c-2-1-3-2-4-3 4-3 13-3 19-4 1-1 3-1 5-3-1 0-1 0-2-1h7z" class="C"></path><defs><linearGradient id="CD" x1="334.39" y1="395.812" x2="335.478" y2="405.437" xlink:href="#B"><stop offset="0" stop-color="#121214"></stop><stop offset="1" stop-color="#343535"></stop></linearGradient></defs><path fill="url(#CD)" d="M347 393l2 3c-2 4-5 7-9 9s-8 2-13 3l-9-6h1 2c5 0 10-1 15-3 2-1 4-1 6-2s3-3 5-4z"></path><path d="M339 386c-2-2-3-4-3-6s2-5 3-6c8-7 23-5 32-5l8 1c4 2 9 2 13 3v1c-6-1-12-1-18 0-4 1-8 3-12 3h-1c-1 2-2 3-3 4l-3 2c-2 2-6 6-5 8l-1 5h0l-2-3c-1-1-3-1-4-2h0c-1-3-2-4-4-5z" class="C"></path><path d="M351 379v-1h1v-1h1l1-1 1 1c1-1 1-1 2-1v-1c5-1 11-2 17-1-4 1-8 3-12 3-2 0-4 0-6 1l-5 1z" class="P"></path><defs><linearGradient id="CE" x1="345.952" y1="385.698" x2="347.534" y2="392.607" xlink:href="#B"><stop offset="0" stop-color="#313235"></stop><stop offset="1" stop-color="#494f52"></stop></linearGradient></defs><path fill="url(#CE)" d="M351 379l5-1c2-1 4-1 6-1h-1c-1 2-2 3-3 4l-3 2c-2 2-6 6-5 8l-1 5h0l-2-3c-1-1-3-1-4-2h0c-1-3-2-4-4-5v-2c1-2 3-2 5-3v1l-2 1h1l1 1 1-1v-1h2c0-1 1-1 2-1v-1h2v-1z"></path><path d="M361 377c-1 2-2 3-3 4l-3 2c-2 0-2 1-4 2h-1v-1c4-2 7-5 11-7z" class="B"></path><path d="M334 347c11 1 22 5 33 8l3 2c2 1 5 2 7 3 0-1 1-2 1-3h1c6 3 14 6 21 7l1 1c1 0 3-1 4 0 6 1 12 4 17 7 2 2 5 3 6 6v3c0 2 0 4 1 6-3-2-6-4-9-5-9-4-17-7-26-9h-2c-4-1-9-1-13-3l-8-1h4 3-1l-8-1c-1-1-3-1-4-1v-1h-7c-4-3-10-2-14-5-4-2-9-4-10-9v-1h0 1c-1-1-1-2-1-3v-1z" class="J"></path><path d="M379 370c5 0 10 0 15 3h-2c-4-1-9-1-13-3z" class="K"></path><path d="M386 367c2-1 4 0 7 0l14 3c5 1 8 4 13 6 2 2 4 3 6 5-2 0-3-1-5-2-2-2-4-3-6-3-9-4-19-8-29-9z" class="Z"></path><path d="M405 365c6 1 12 4 17 7 2 2 5 3 6 6v3h-2c-2-2-4-3-6-5-5-2-8-5-13-6l-3-3-2-1 3-1z" class="H"></path><path d="M404 367c4 0 6 3 10 4v1l3 2c1 0 2 1 3 2-5-2-8-5-13-6l-3-3z" class="D"></path><path d="M377 360c0-1 1-2 1-3h1c6 3 14 6 21 7l1 1c1 0 3-1 4 0l-3 1 2 1 3 3-14-3c-3 0-5-1-7 0-1 0-2-1-3-1-5 0-7-1-10-5 2 1 4 1 6 1l-3-1 1-1z" class="G"></path><path d="M397 364l4 1c1 0 3-1 4 0l-3 1h-1c-2 0-3-1-4-2z" class="J"></path><path d="M377 360c0-1 1-2 1-3h1c6 3 14 6 21 7l1 1-4-1c-1-1-16-2-18-2l-3-1 1-1z" class="P"></path><defs><linearGradient id="CF" x1="364.601" y1="351.129" x2="353.003" y2="364.939" xlink:href="#B"><stop offset="0" stop-color="#313133"></stop><stop offset="1" stop-color="#5e6367"></stop></linearGradient></defs><path fill="url(#CF)" d="M334 347c11 1 22 5 33 8l3 2c2 1 5 2 7 3l-1 1 3 1c-2 0-4 0-6-1 3 4 5 5 10 5 0 1 1 1 1 1l-1 1c-3 0-6-1-9-1-2-1-3-1-5-1h-1-3-7c-4-3-10-2-14-5-4-2-9-4-10-9v-1h0 1c-1-1-1-2-1-3v-1z"></path><path d="M370 357c2 1 5 2 7 3l-1 1 3 1c-2 0-4 0-6-1l-5-3h0c2 1 3 1 5 2-1-2-2-1-3-3z" class="M"></path><path d="M334 351c7 4 14 8 21 11 4 2 9 3 14 4h-1-3-7c-4-3-10-2-14-5-4-2-9-4-10-9v-1z" class="C"></path></svg>